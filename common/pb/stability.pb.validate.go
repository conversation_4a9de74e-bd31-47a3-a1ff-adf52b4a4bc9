// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: common/stability.proto

package pb

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on StabilityCustomDevices with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *StabilityCustomDevices) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on StabilityCustomDevices with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// StabilityCustomDevicesMultiError, or nil if none found.
func (m *StabilityCustomDevices) ValidateAll() error {
	return m.validate(true)
}

func (m *StabilityCustomDevices) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	switch v := m.Devices.(type) {
	case *StabilityCustomDevices_Udids:
		if v == nil {
			err := StabilityCustomDevicesValidationError{
				field:  "Devices",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetUdids()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, StabilityCustomDevicesValidationError{
						field:  "Udids",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, StabilityCustomDevicesValidationError{
						field:  "Udids",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetUdids()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return StabilityCustomDevicesValidationError{
					field:  "Udids",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *StabilityCustomDevices_Count:
		if v == nil {
			err := StabilityCustomDevicesValidationError{
				field:  "Devices",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if m.GetCount() != 0 {

			if m.GetCount() < 0 {
				err := StabilityCustomDevicesValidationError{
					field:  "Count",
					reason: "value must be greater than or equal to 0",
				}
				if !all {
					return err
				}
				errors = append(errors, err)
			}

		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return StabilityCustomDevicesMultiError(errors)
	}

	return nil
}

// StabilityCustomDevicesMultiError is an error wrapping multiple validation
// errors returned by StabilityCustomDevices.ValidateAll() if the designated
// constraints aren't met.
type StabilityCustomDevicesMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m StabilityCustomDevicesMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m StabilityCustomDevicesMultiError) AllErrors() []error { return m }

// StabilityCustomDevicesValidationError is the validation error returned by
// StabilityCustomDevices.Validate if the designated constraints aren't met.
type StabilityCustomDevicesValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e StabilityCustomDevicesValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e StabilityCustomDevicesValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e StabilityCustomDevicesValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e StabilityCustomDevicesValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e StabilityCustomDevicesValidationError) ErrorName() string {
	return "StabilityCustomDevicesValidationError"
}

// Error satisfies the builtin error interface
func (e StabilityCustomDevicesValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sStabilityCustomDevices.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = StabilityCustomDevicesValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = StabilityCustomDevicesValidationError{}

// Validate checks the field values on StabilityCustomScript with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *StabilityCustomScript) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on StabilityCustomScript with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// StabilityCustomScriptMultiError, or nil if none found.
func (m *StabilityCustomScript) ValidateAll() error {
	return m.validate(true)
}

func (m *StabilityCustomScript) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	switch v := m.Script.(type) {
	case *StabilityCustomScript_GitConfig:
		if v == nil {
			err := StabilityCustomScriptValidationError{
				field:  "Script",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetGitConfig()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, StabilityCustomScriptValidationError{
						field:  "GitConfig",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, StabilityCustomScriptValidationError{
						field:  "GitConfig",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetGitConfig()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return StabilityCustomScriptValidationError{
					field:  "GitConfig",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *StabilityCustomScript_Image:
		if v == nil {
			err := StabilityCustomScriptValidationError{
				field:  "Script",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if m.GetImage() != "" {

			if utf8.RuneCountInString(m.GetImage()) < 1 {
				err := StabilityCustomScriptValidationError{
					field:  "Image",
					reason: "value length must be at least 1 runes",
				}
				if !all {
					return err
				}
				errors = append(errors, err)
			}

		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return StabilityCustomScriptMultiError(errors)
	}

	return nil
}

// StabilityCustomScriptMultiError is an error wrapping multiple validation
// errors returned by StabilityCustomScript.ValidateAll() if the designated
// constraints aren't met.
type StabilityCustomScriptMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m StabilityCustomScriptMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m StabilityCustomScriptMultiError) AllErrors() []error { return m }

// StabilityCustomScriptValidationError is the validation error returned by
// StabilityCustomScript.Validate if the designated constraints aren't met.
type StabilityCustomScriptValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e StabilityCustomScriptValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e StabilityCustomScriptValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e StabilityCustomScriptValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e StabilityCustomScriptValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e StabilityCustomScriptValidationError) ErrorName() string {
	return "StabilityCustomScriptValidationError"
}

// Error satisfies the builtin error interface
func (e StabilityCustomScriptValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sStabilityCustomScript.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = StabilityCustomScriptValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = StabilityCustomScriptValidationError{}

// Validate checks the field values on ActivityStatistics with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ActivityStatistics) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ActivityStatistics with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ActivityStatisticsMultiError, or nil if none found.
func (m *ActivityStatistics) ValidateAll() error {
	return m.validate(true)
}

func (m *ActivityStatistics) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Coverage

	if len(errors) > 0 {
		return ActivityStatisticsMultiError(errors)
	}

	return nil
}

// ActivityStatisticsMultiError is an error wrapping multiple validation errors
// returned by ActivityStatistics.ValidateAll() if the designated constraints
// aren't met.
type ActivityStatisticsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ActivityStatisticsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ActivityStatisticsMultiError) AllErrors() []error { return m }

// ActivityStatisticsValidationError is the validation error returned by
// ActivityStatistics.Validate if the designated constraints aren't met.
type ActivityStatisticsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ActivityStatisticsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ActivityStatisticsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ActivityStatisticsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ActivityStatisticsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ActivityStatisticsValidationError) ErrorName() string {
	return "ActivityStatisticsValidationError"
}

// Error satisfies the builtin error interface
func (e ActivityStatisticsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sActivityStatistics.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ActivityStatisticsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ActivityStatisticsValidationError{}

// Validate checks the field values on Artifact with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Artifact) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Artifact with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ArtifactMultiError, or nil
// if none found.
func (m *Artifact) ValidateAll() error {
	return m.validate(true)
}

func (m *Artifact) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Type

	// no validation rules for FileName

	// no validation rules for FilePath

	// no validation rules for Description

	if len(errors) > 0 {
		return ArtifactMultiError(errors)
	}

	return nil
}

// ArtifactMultiError is an error wrapping multiple validation errors returned
// by Artifact.ValidateAll() if the designated constraints aren't met.
type ArtifactMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ArtifactMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ArtifactMultiError) AllErrors() []error { return m }

// ArtifactValidationError is the validation error returned by
// Artifact.Validate if the designated constraints aren't met.
type ArtifactValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ArtifactValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ArtifactValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ArtifactValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ArtifactValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ArtifactValidationError) ErrorName() string { return "ArtifactValidationError" }

// Error satisfies the builtin error interface
func (e ArtifactValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sArtifact.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ArtifactValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ArtifactValidationError{}

// Validate checks the field values on StabilityResult with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *StabilityResult) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on StabilityResult with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// StabilityResultMultiError, or nil if none found.
func (m *StabilityResult) ValidateAll() error {
	return m.validate(true)
}

func (m *StabilityResult) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetActivityStatistics()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, StabilityResultValidationError{
					field:  "ActivityStatistics",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, StabilityResultValidationError{
					field:  "ActivityStatistics",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetActivityStatistics()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return StabilityResultValidationError{
				field:  "ActivityStatistics",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetArtifacts() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, StabilityResultValidationError{
						field:  fmt.Sprintf("Artifacts[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, StabilityResultValidationError{
						field:  fmt.Sprintf("Artifacts[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return StabilityResultValidationError{
					field:  fmt.Sprintf("Artifacts[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for AnrCount

	// no validation rules for CrashCount

	if len(errors) > 0 {
		return StabilityResultMultiError(errors)
	}

	return nil
}

// StabilityResultMultiError is an error wrapping multiple validation errors
// returned by StabilityResult.ValidateAll() if the designated constraints
// aren't met.
type StabilityResultMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m StabilityResultMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m StabilityResultMultiError) AllErrors() []error { return m }

// StabilityResultValidationError is the validation error returned by
// StabilityResult.Validate if the designated constraints aren't met.
type StabilityResultValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e StabilityResultValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e StabilityResultValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e StabilityResultValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e StabilityResultValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e StabilityResultValidationError) ErrorName() string { return "StabilityResultValidationError" }

// Error satisfies the builtin error interface
func (e StabilityResultValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sStabilityResult.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = StabilityResultValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = StabilityResultValidationError{}
