// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: common/limit.proto

package pb

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on RateLimit with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *RateLimit) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RateLimit with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in RateLimitMultiError, or nil
// if none found.
func (m *RateLimit) ValidateAll() error {
	return m.validate(true)
}

func (m *RateLimit) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetTargetRps() <= 0 {
		err := RateLimitValidationError{
			field:  "TargetRps",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetInitialRps() != 0 {

		if m.GetInitialRps() <= 0 {
			err := RateLimitValidationError{
				field:  "InitialRps",
				reason: "value must be greater than 0",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if m.GetStepHeight() != 0 {

		if _, ok := _RateLimit_StepHeight_NotInLookup[m.GetStepHeight()]; ok {
			err := RateLimitValidationError{
				field:  "StepHeight",
				reason: "value must not be in list [0]",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if m.GetStepDuration() != "" {

		if utf8.RuneCountInString(m.GetStepDuration()) < 2 {
			err := RateLimitValidationError{
				field:  "StepDuration",
				reason: "value length must be at least 2 runes",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if len(errors) > 0 {
		return RateLimitMultiError(errors)
	}

	return nil
}

// RateLimitMultiError is an error wrapping multiple validation errors returned
// by RateLimit.ValidateAll() if the designated constraints aren't met.
type RateLimitMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RateLimitMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RateLimitMultiError) AllErrors() []error { return m }

// RateLimitValidationError is the validation error returned by
// RateLimit.Validate if the designated constraints aren't met.
type RateLimitValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RateLimitValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RateLimitValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RateLimitValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RateLimitValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RateLimitValidationError) ErrorName() string { return "RateLimitValidationError" }

// Error satisfies the builtin error interface
func (e RateLimitValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRateLimit.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RateLimitValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RateLimitValidationError{}

var _RateLimit_StepHeight_NotInLookup = map[int64]struct{}{
	0: {},
}

// Validate checks the field values on RateLimitV2 with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *RateLimitV2) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RateLimitV2 with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in RateLimitV2MultiError, or
// nil if none found.
func (m *RateLimitV2) ValidateAll() error {
	return m.validate(true)
}

func (m *RateLimitV2) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetTargetRps() <= 0 {
		err := RateLimitV2ValidationError{
			field:  "TargetRps",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetInitialRps() != 0 {

		if m.GetInitialRps() <= 0 {
			err := RateLimitV2ValidationError{
				field:  "InitialRps",
				reason: "value must be greater than 0",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if m.GetChangeDuration() != "" {

		if utf8.RuneCountInString(m.GetChangeDuration()) < 2 {
			err := RateLimitV2ValidationError{
				field:  "ChangeDuration",
				reason: "value length must be at least 2 runes",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if m.GetTargetDuration() != "" {

		if utf8.RuneCountInString(m.GetTargetDuration()) < 2 {
			err := RateLimitV2ValidationError{
				field:  "TargetDuration",
				reason: "value length must be at least 2 runes",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if len(errors) > 0 {
		return RateLimitV2MultiError(errors)
	}

	return nil
}

// RateLimitV2MultiError is an error wrapping multiple validation errors
// returned by RateLimitV2.ValidateAll() if the designated constraints aren't met.
type RateLimitV2MultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RateLimitV2MultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RateLimitV2MultiError) AllErrors() []error { return m }

// RateLimitV2ValidationError is the validation error returned by
// RateLimitV2.Validate if the designated constraints aren't met.
type RateLimitV2ValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RateLimitV2ValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RateLimitV2ValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RateLimitV2ValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RateLimitV2ValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RateLimitV2ValidationError) ErrorName() string { return "RateLimitV2ValidationError" }

// Error satisfies the builtin error interface
func (e RateLimitV2ValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRateLimitV2.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RateLimitV2ValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RateLimitV2ValidationError{}
