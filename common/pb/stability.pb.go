// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.1
// 	protoc        v5.29.2
// source: common/stability.proto

package pb

import (
	reflect "reflect"
	sync "sync"

	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	structpb "google.golang.org/protobuf/types/known/structpb"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// StabilityCustomDevices 稳定性测试自定义设备
type StabilityCustomDevices struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Types that are valid to be assigned to Devices:
	//
	//	*StabilityCustomDevices_Udids
	//	*StabilityCustomDevices_Count
	Devices       isStabilityCustomDevices_Devices `protobuf_oneof:"devices"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *StabilityCustomDevices) Reset() {
	*x = StabilityCustomDevices{}
	mi := &file_common_stability_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StabilityCustomDevices) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StabilityCustomDevices) ProtoMessage() {}

func (x *StabilityCustomDevices) ProtoReflect() protoreflect.Message {
	mi := &file_common_stability_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StabilityCustomDevices.ProtoReflect.Descriptor instead.
func (*StabilityCustomDevices) Descriptor() ([]byte, []int) {
	return file_common_stability_proto_rawDescGZIP(), []int{0}
}

func (x *StabilityCustomDevices) GetDevices() isStabilityCustomDevices_Devices {
	if x != nil {
		return x.Devices
	}
	return nil
}

func (x *StabilityCustomDevices) GetUdids() *structpb.ListValue {
	if x != nil {
		if x, ok := x.Devices.(*StabilityCustomDevices_Udids); ok {
			return x.Udids
		}
	}
	return nil
}

func (x *StabilityCustomDevices) GetCount() uint32 {
	if x != nil {
		if x, ok := x.Devices.(*StabilityCustomDevices_Count); ok {
			return x.Count
		}
	}
	return 0
}

type isStabilityCustomDevices_Devices interface {
	isStabilityCustomDevices_Devices()
}

type StabilityCustomDevices_Udids struct {
	Udids *structpb.ListValue `protobuf:"bytes,1,opt,name=udids,proto3,oneof"` // 设备编号列表（指定设备）
}

type StabilityCustomDevices_Count struct {
	Count uint32 `protobuf:"varint,2,opt,name=count,proto3,oneof"` // 设备数量（随机选择设备）
}

func (*StabilityCustomDevices_Udids) isStabilityCustomDevices_Devices() {}

func (*StabilityCustomDevices_Count) isStabilityCustomDevices_Devices() {}

// StabilityCustomScript 稳定性测试自定义脚本
type StabilityCustomScript struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Types that are valid to be assigned to Script:
	//
	//	*StabilityCustomScript_GitConfig
	//	*StabilityCustomScript_Image
	Script        isStabilityCustomScript_Script `protobuf_oneof:"script"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *StabilityCustomScript) Reset() {
	*x = StabilityCustomScript{}
	mi := &file_common_stability_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StabilityCustomScript) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StabilityCustomScript) ProtoMessage() {}

func (x *StabilityCustomScript) ProtoReflect() protoreflect.Message {
	mi := &file_common_stability_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StabilityCustomScript.ProtoReflect.Descriptor instead.
func (*StabilityCustomScript) Descriptor() ([]byte, []int) {
	return file_common_stability_proto_rawDescGZIP(), []int{1}
}

func (x *StabilityCustomScript) GetScript() isStabilityCustomScript_Script {
	if x != nil {
		return x.Script
	}
	return nil
}

func (x *StabilityCustomScript) GetGitConfig() *GitConfig {
	if x != nil {
		if x, ok := x.Script.(*StabilityCustomScript_GitConfig); ok {
			return x.GitConfig
		}
	}
	return nil
}

func (x *StabilityCustomScript) GetImage() string {
	if x != nil {
		if x, ok := x.Script.(*StabilityCustomScript_Image); ok {
			return x.Image
		}
	}
	return ""
}

type isStabilityCustomScript_Script interface {
	isStabilityCustomScript_Script()
}

type StabilityCustomScript_GitConfig struct {
	GitConfig *GitConfig `protobuf:"bytes,1,opt,name=git_config,json=gitConfig,proto3,oneof"` // 自定义脚本（Git仓库）
}

type StabilityCustomScript_Image struct {
	Image string `protobuf:"bytes,2,opt,name=image,proto3,oneof"` // 自定义脚本（镜像）
}

func (*StabilityCustomScript_GitConfig) isStabilityCustomScript_Script() {}

func (*StabilityCustomScript_Image) isStabilityCustomScript_Script() {}

// ActivityStatistics Activity统计
type ActivityStatistics struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	TestedActivity []string               `protobuf:"bytes,1,rep,name=tested_activity,json=testedActivity,proto3" json:"tested_activity,omitempty"` // 被测试的Activity
	TotalActivity  []string               `protobuf:"bytes,2,rep,name=total_activity,json=totalActivity,proto3" json:"total_activity,omitempty"`    // 总的Activity
	Coverage       float64                `protobuf:"fixed64,3,opt,name=coverage,proto3" json:"coverage,omitempty"`                                 // 覆盖率
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *ActivityStatistics) Reset() {
	*x = ActivityStatistics{}
	mi := &file_common_stability_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ActivityStatistics) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ActivityStatistics) ProtoMessage() {}

func (x *ActivityStatistics) ProtoReflect() protoreflect.Message {
	mi := &file_common_stability_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ActivityStatistics.ProtoReflect.Descriptor instead.
func (*ActivityStatistics) Descriptor() ([]byte, []int) {
	return file_common_stability_proto_rawDescGZIP(), []int{2}
}

func (x *ActivityStatistics) GetTestedActivity() []string {
	if x != nil {
		return x.TestedActivity
	}
	return nil
}

func (x *ActivityStatistics) GetTotalActivity() []string {
	if x != nil {
		return x.TotalActivity
	}
	return nil
}

func (x *ActivityStatistics) GetCoverage() float64 {
	if x != nil {
		return x.Coverage
	}
	return 0
}

// Artifact 产出物
type Artifact struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Type          ArtifactType           `protobuf:"varint,1,opt,name=type,proto3,enum=common.ArtifactType" json:"type,omitempty"` // 类型
	FileName      string                 `protobuf:"bytes,2,opt,name=file_name,json=fileName,proto3" json:"file_name,omitempty"`   // 文件名
	FilePath      string                 `protobuf:"bytes,3,opt,name=file_path,json=filePath,proto3" json:"file_path,omitempty"`   // 文件路径
	Description   string                 `protobuf:"bytes,4,opt,name=description,proto3" json:"description,omitempty"`             // 描述
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Artifact) Reset() {
	*x = Artifact{}
	mi := &file_common_stability_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Artifact) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Artifact) ProtoMessage() {}

func (x *Artifact) ProtoReflect() protoreflect.Message {
	mi := &file_common_stability_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Artifact.ProtoReflect.Descriptor instead.
func (*Artifact) Descriptor() ([]byte, []int) {
	return file_common_stability_proto_rawDescGZIP(), []int{3}
}

func (x *Artifact) GetType() ArtifactType {
	if x != nil {
		return x.Type
	}
	return ArtifactType_ArtifactType_NULL
}

func (x *Artifact) GetFileName() string {
	if x != nil {
		return x.FileName
	}
	return ""
}

func (x *Artifact) GetFilePath() string {
	if x != nil {
		return x.FilePath
	}
	return ""
}

func (x *Artifact) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

// StabilityResult 稳定性测试结果
type StabilityResult struct {
	state              protoimpl.MessageState `protogen:"open.v1"`
	ActivityStatistics *ActivityStatistics    `protobuf:"bytes,1,opt,name=activity_statistics,json=activityStatistics,proto3" json:"activity_statistics,omitempty"` // Activity统计
	Artifacts          []*Artifact            `protobuf:"bytes,2,rep,name=artifacts,proto3" json:"artifacts,omitempty"`                                             // 产出物
	AnrCount           uint32                 `protobuf:"varint,3,opt,name=anr_count,json=anrCount,proto3" json:"anr_count,omitempty"`                              // ANR数量
	CrashCount         uint32                 `protobuf:"varint,4,opt,name=crash_count,json=crashCount,proto3" json:"crash_count,omitempty"`                        // Crash数量
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *StabilityResult) Reset() {
	*x = StabilityResult{}
	mi := &file_common_stability_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StabilityResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StabilityResult) ProtoMessage() {}

func (x *StabilityResult) ProtoReflect() protoreflect.Message {
	mi := &file_common_stability_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StabilityResult.ProtoReflect.Descriptor instead.
func (*StabilityResult) Descriptor() ([]byte, []int) {
	return file_common_stability_proto_rawDescGZIP(), []int{4}
}

func (x *StabilityResult) GetActivityStatistics() *ActivityStatistics {
	if x != nil {
		return x.ActivityStatistics
	}
	return nil
}

func (x *StabilityResult) GetArtifacts() []*Artifact {
	if x != nil {
		return x.Artifacts
	}
	return nil
}

func (x *StabilityResult) GetAnrCount() uint32 {
	if x != nil {
		return x.AnrCount
	}
	return 0
}

func (x *StabilityResult) GetCrashCount() uint32 {
	if x != nil {
		return x.CrashCount
	}
	return 0
}

var File_common_stability_proto protoreflect.FileDescriptor

var file_common_stability_proto_rawDesc = []byte{
	0x0a, 0x16, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x73, 0x74, 0x61, 0x62, 0x69, 0x6c, 0x69,
	0x74, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x06, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2f, 0x73, 0x74, 0x72, 0x75, 0x63, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17,
	0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x13, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f,
	0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x11, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22,
	0x7a, 0x0a, 0x16, 0x53, 0x74, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x43, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x73, 0x12, 0x32, 0x0a, 0x05, 0x75, 0x64, 0x69,
	0x64, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x56,
	0x61, 0x6c, 0x75, 0x65, 0x48, 0x00, 0x52, 0x05, 0x75, 0x64, 0x69, 0x64, 0x73, 0x12, 0x21, 0x0a,
	0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x42, 0x09, 0xfa, 0x42,
	0x06, 0x2a, 0x04, 0x28, 0x00, 0x40, 0x01, 0x48, 0x00, 0x52, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x42, 0x09, 0x0a, 0x07, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x73, 0x22, 0x79, 0x0a, 0x15, 0x53,
	0x74, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x53, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x12, 0x32, 0x0a, 0x0a, 0x67, 0x69, 0x74, 0x5f, 0x63, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2e, 0x47, 0x69, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x48, 0x00, 0x52, 0x09, 0x67,
	0x69, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x22, 0x0a, 0x05, 0x69, 0x6d, 0x61, 0x67,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x72, 0x05, 0x10, 0x01,
	0xd0, 0x01, 0x01, 0x48, 0x00, 0x52, 0x05, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x42, 0x08, 0x0a, 0x06,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x22, 0x80, 0x01, 0x0a, 0x12, 0x41, 0x63, 0x74, 0x69, 0x76,
	0x69, 0x74, 0x79, 0x53, 0x74, 0x61, 0x74, 0x69, 0x73, 0x74, 0x69, 0x63, 0x73, 0x12, 0x27, 0x0a,
	0x0f, 0x74, 0x65, 0x73, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0e, 0x74, 0x65, 0x73, 0x74, 0x65, 0x64, 0x41, 0x63,
	0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x12, 0x25, 0x0a, 0x0e, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f,
	0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0d,
	0x74, 0x6f, 0x74, 0x61, 0x6c, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x12, 0x1a, 0x0a,
	0x08, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x61, 0x67, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x01, 0x52,
	0x08, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x61, 0x67, 0x65, 0x22, 0x90, 0x01, 0x0a, 0x08, 0x41, 0x72,
	0x74, 0x69, 0x66, 0x61, 0x63, 0x74, 0x12, 0x28, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x41, 0x72,
	0x74, 0x69, 0x66, 0x61, 0x63, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65,
	0x12, 0x1b, 0x0a, 0x09, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1b, 0x0a,
	0x09, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x50, 0x61, 0x74, 0x68, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0xcc, 0x01, 0x0a,
	0x0f, 0x53, 0x74, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x12, 0x4b, 0x0a, 0x13, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x5f, 0x73, 0x74, 0x61,
	0x74, 0x69, 0x73, 0x74, 0x69, 0x63, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x53,
	0x74, 0x61, 0x74, 0x69, 0x73, 0x74, 0x69, 0x63, 0x73, 0x52, 0x12, 0x61, 0x63, 0x74, 0x69, 0x76,
	0x69, 0x74, 0x79, 0x53, 0x74, 0x61, 0x74, 0x69, 0x73, 0x74, 0x69, 0x63, 0x73, 0x12, 0x2e, 0x0a,
	0x09, 0x61, 0x72, 0x74, 0x69, 0x66, 0x61, 0x63, 0x74, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x10, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x41, 0x72, 0x74, 0x69, 0x66, 0x61,
	0x63, 0x74, 0x52, 0x09, 0x61, 0x72, 0x74, 0x69, 0x66, 0x61, 0x63, 0x74, 0x73, 0x12, 0x1b, 0x0a,
	0x09, 0x61, 0x6e, 0x72, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x08, 0x61, 0x6e, 0x72, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x72,
	0x61, 0x73, 0x68, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x0a, 0x63, 0x72, 0x61, 0x73, 0x68, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x42, 0x3c, 0x5a, 0x3a, 0x67,
	0x69, 0x74, 0x6c, 0x61, 0x62, 0x2e, 0x74, 0x74, 0x79, 0x75, 0x79, 0x69, 0x6e, 0x2e, 0x63, 0x6f,
	0x6d, 0x2f, 0x54, 0x65, 0x73, 0x74, 0x44, 0x65, 0x76, 0x65, 0x6c, 0x6f, 0x70, 0x6d, 0x65, 0x6e,
	0x74, 0x2f, 0x70, 0x72, 0x6f, 0x62, 0x65, 0x2d, 0x62, 0x61, 0x63, 0x6b, 0x65, 0x6e, 0x64, 0x2f,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x33,
}

var (
	file_common_stability_proto_rawDescOnce sync.Once
	file_common_stability_proto_rawDescData = file_common_stability_proto_rawDesc
)

func file_common_stability_proto_rawDescGZIP() []byte {
	file_common_stability_proto_rawDescOnce.Do(func() {
		file_common_stability_proto_rawDescData = protoimpl.X.CompressGZIP(file_common_stability_proto_rawDescData)
	})
	return file_common_stability_proto_rawDescData
}

var file_common_stability_proto_msgTypes = make([]protoimpl.MessageInfo, 5)
var file_common_stability_proto_goTypes = []any{
	(*StabilityCustomDevices)(nil), // 0: common.StabilityCustomDevices
	(*StabilityCustomScript)(nil),  // 1: common.StabilityCustomScript
	(*ActivityStatistics)(nil),     // 2: common.ActivityStatistics
	(*Artifact)(nil),               // 3: common.Artifact
	(*StabilityResult)(nil),        // 4: common.StabilityResult
	(*structpb.ListValue)(nil),     // 5: google.protobuf.ListValue
	(*GitConfig)(nil),              // 6: common.GitConfig
	(ArtifactType)(0),              // 7: common.ArtifactType
}
var file_common_stability_proto_depIdxs = []int32{
	5, // 0: common.StabilityCustomDevices.udids:type_name -> google.protobuf.ListValue
	6, // 1: common.StabilityCustomScript.git_config:type_name -> common.GitConfig
	7, // 2: common.Artifact.type:type_name -> common.ArtifactType
	2, // 3: common.StabilityResult.activity_statistics:type_name -> common.ActivityStatistics
	3, // 4: common.StabilityResult.artifacts:type_name -> common.Artifact
	5, // [5:5] is the sub-list for method output_type
	5, // [5:5] is the sub-list for method input_type
	5, // [5:5] is the sub-list for extension type_name
	5, // [5:5] is the sub-list for extension extendee
	0, // [0:5] is the sub-list for field type_name
}

func init() { file_common_stability_proto_init() }
func file_common_stability_proto_init() {
	if File_common_stability_proto != nil {
		return
	}
	file_common_config_proto_init()
	file_common_enum_proto_init()
	file_common_stability_proto_msgTypes[0].OneofWrappers = []any{
		(*StabilityCustomDevices_Udids)(nil),
		(*StabilityCustomDevices_Count)(nil),
	}
	file_common_stability_proto_msgTypes[1].OneofWrappers = []any{
		(*StabilityCustomScript_GitConfig)(nil),
		(*StabilityCustomScript_Image)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_common_stability_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   5,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_common_stability_proto_goTypes,
		DependencyIndexes: file_common_stability_proto_depIdxs,
		MessageInfos:      file_common_stability_proto_msgTypes,
	}.Build()
	File_common_stability_proto = out.File
	file_common_stability_proto_rawDesc = nil
	file_common_stability_proto_goTypes = nil
	file_common_stability_proto_depIdxs = nil
}
