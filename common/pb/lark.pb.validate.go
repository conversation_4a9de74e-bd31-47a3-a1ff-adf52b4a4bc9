// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: common/lark.proto

package pb

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on LarkChat with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *LarkChat) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LarkChat with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in LarkChatMultiError, or nil
// if none found.
func (m *LarkChat) ValidateAll() error {
	return m.validate(true)
}

func (m *LarkChat) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetChatId()) != 35 {
		err := LarkChatValidationError{
			field:  "ChatId",
			reason: "value length must be 35 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)

	}

	if !_LarkChat_ChatId_Pattern.MatchString(m.GetChatId()) {
		err := LarkChatValidationError{
			field:  "ChatId",
			reason: "value does not match regex pattern \"oc_[0-9a-z]{32}\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if l := utf8.RuneCountInString(m.GetName()); l < 1 || l > 64 {
		err := LarkChatValidationError{
			field:  "Name",
			reason: "value length must be between 1 and 64 runes, inclusive",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return LarkChatMultiError(errors)
	}

	return nil
}

// LarkChatMultiError is an error wrapping multiple validation errors returned
// by LarkChat.ValidateAll() if the designated constraints aren't met.
type LarkChatMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LarkChatMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LarkChatMultiError) AllErrors() []error { return m }

// LarkChatValidationError is the validation error returned by
// LarkChat.Validate if the designated constraints aren't met.
type LarkChatValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LarkChatValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LarkChatValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LarkChatValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LarkChatValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LarkChatValidationError) ErrorName() string { return "LarkChatValidationError" }

// Error satisfies the builtin error interface
func (e LarkChatValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLarkChat.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LarkChatValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LarkChatValidationError{}

var _LarkChat_ChatId_Pattern = regexp.MustCompile("oc_[0-9a-z]{32}")
