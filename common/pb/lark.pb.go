// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.1
// 	protoc        v5.29.2
// source: common/lark.proto

package pb

import (
	reflect "reflect"
	sync "sync"

	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type LarkChat struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ChatId        string                 `protobuf:"bytes,1,opt,name=chat_id,json=chatId,proto3" json:"chat_id,omitempty"` // 飞书群组ID
	Name          string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`                   // 飞书群组名称
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *LarkChat) Reset() {
	*x = LarkChat{}
	mi := &file_common_lark_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LarkChat) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LarkChat) ProtoMessage() {}

func (x *LarkChat) ProtoReflect() protoreflect.Message {
	mi := &file_common_lark_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LarkChat.ProtoReflect.Descriptor instead.
func (*LarkChat) Descriptor() ([]byte, []int) {
	return file_common_lark_proto_rawDescGZIP(), []int{0}
}

func (x *LarkChat) GetChatId() string {
	if x != nil {
		return x.ChatId
	}
	return ""
}

func (x *LarkChat) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

var File_common_lark_proto protoreflect.FileDescriptor

var file_common_lark_proto_rawDesc = []byte{
	0x0a, 0x11, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x6c, 0x61, 0x72, 0x6b, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x12, 0x06, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x1a, 0x17, 0x76, 0x61, 0x6c,
	0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x22, 0x5d, 0x0a, 0x08, 0x4c, 0x61, 0x72, 0x6b, 0x43, 0x68, 0x61, 0x74,
	0x12, 0x32, 0x0a, 0x07, 0x63, 0x68, 0x61, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x19, 0xfa, 0x42, 0x16, 0x72, 0x14, 0x32, 0x0f, 0x6f, 0x63, 0x5f, 0x5b, 0x30, 0x2d,
	0x39, 0x61, 0x2d, 0x7a, 0x5d, 0x7b, 0x33, 0x32, 0x7d, 0x98, 0x01, 0x23, 0x52, 0x06, 0x63, 0x68,
	0x61, 0x74, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x40, 0x52, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x42, 0x3c, 0x5a, 0x3a, 0x67, 0x69, 0x74, 0x6c, 0x61, 0x62, 0x2e, 0x74, 0x74,
	0x79, 0x75, 0x79, 0x69, 0x6e, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x54, 0x65, 0x73, 0x74, 0x44, 0x65,
	0x76, 0x65, 0x6c, 0x6f, 0x70, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x70, 0x72, 0x6f, 0x62, 0x65, 0x2d,
	0x62, 0x61, 0x63, 0x6b, 0x65, 0x6e, 0x64, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x70,
	0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_common_lark_proto_rawDescOnce sync.Once
	file_common_lark_proto_rawDescData = file_common_lark_proto_rawDesc
)

func file_common_lark_proto_rawDescGZIP() []byte {
	file_common_lark_proto_rawDescOnce.Do(func() {
		file_common_lark_proto_rawDescData = protoimpl.X.CompressGZIP(file_common_lark_proto_rawDescData)
	})
	return file_common_lark_proto_rawDescData
}

var file_common_lark_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_common_lark_proto_goTypes = []any{
	(*LarkChat)(nil), // 0: common.LarkChat
}
var file_common_lark_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_common_lark_proto_init() }
func file_common_lark_proto_init() {
	if File_common_lark_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_common_lark_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_common_lark_proto_goTypes,
		DependencyIndexes: file_common_lark_proto_depIdxs,
		MessageInfos:      file_common_lark_proto_msgTypes,
	}.Build()
	File_common_lark_proto = out.File
	file_common_lark_proto_rawDesc = nil
	file_common_lark_proto_goTypes = nil
	file_common_lark_proto_depIdxs = nil
}
