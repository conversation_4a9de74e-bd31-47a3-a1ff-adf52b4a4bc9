// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.1
// 	protoc        v5.29.2
// source: common/perf.proto

package pb

import (
	reflect "reflect"
	sync "sync"

	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	structpb "google.golang.org/protobuf/types/known/structpb"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// PerfKeepalive 压测保活参数
type PerfKeepalive struct {
	state         protoimpl.MessageState         `protogen:"open.v1"`
	Auth          *PerfKeepalive_AuthConfig      `protobuf:"bytes,1,opt,name=auth,proto3" json:"auth,omitempty"`           // 认证接口的限流配置
	Heartbeat     *PerfKeepalive_HeartbeatConfig `protobuf:"bytes,2,opt,name=heartbeat,proto3" json:"heartbeat,omitempty"` // 心跳接口的限流配置
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PerfKeepalive) Reset() {
	*x = PerfKeepalive{}
	mi := &file_common_perf_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PerfKeepalive) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PerfKeepalive) ProtoMessage() {}

func (x *PerfKeepalive) ProtoReflect() protoreflect.Message {
	mi := &file_common_perf_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PerfKeepalive.ProtoReflect.Descriptor instead.
func (*PerfKeepalive) Descriptor() ([]byte, []int) {
	return file_common_perf_proto_rawDescGZIP(), []int{0}
}

func (x *PerfKeepalive) GetAuth() *PerfKeepalive_AuthConfig {
	if x != nil {
		return x.Auth
	}
	return nil
}

func (x *PerfKeepalive) GetHeartbeat() *PerfKeepalive_HeartbeatConfig {
	if x != nil {
		return x.Heartbeat
	}
	return nil
}

// PerfCaseStep 压测步骤
type PerfCaseStep struct {
	state protoimpl.MessageState `protogen:"open.v1"`

	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name" yaml:"Name"` // 步骤名称

	RateLimit *RateLimit `protobuf:"bytes,2,opt,name=rate_limit,json=rateLimit,proto3" json:"rate_limit" yaml:"RateLimit"` // 限流配置

	Url string `protobuf:"bytes,3,opt,name=url,proto3" json:"url" yaml:"Url"` // 请求URL

	Method string `protobuf:"bytes,4,opt,name=method,proto3" json:"method" yaml:"Method"` // 请求方法

	Headers map[string]string `protobuf:"bytes,5,rep,name=headers,proto3" json:"headers" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value" yaml:"Headers"` // 请求头

	Body string `protobuf:"bytes,6,opt,name=body,proto3" json:"body" yaml:"Body"` // 请求体

	Exports []*PerfCaseStep_Export `protobuf:"bytes,7,rep,name=exports,proto3" json:"exports" yaml:"Exports"` // 出参列表

	Sleep         string `protobuf:"bytes,8,opt,name=sleep,proto3" json:"sleep" yaml:"Sleep"` // 休眠时间
	Key           string `protobuf:"bytes,99,opt,name=key,proto3" json:"key,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PerfCaseStep) Reset() {
	*x = PerfCaseStep{}
	mi := &file_common_perf_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PerfCaseStep) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PerfCaseStep) ProtoMessage() {}

func (x *PerfCaseStep) ProtoReflect() protoreflect.Message {
	mi := &file_common_perf_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PerfCaseStep.ProtoReflect.Descriptor instead.
func (*PerfCaseStep) Descriptor() ([]byte, []int) {
	return file_common_perf_proto_rawDescGZIP(), []int{1}
}

func (x *PerfCaseStep) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *PerfCaseStep) GetRateLimit() *RateLimit {
	if x != nil {
		return x.RateLimit
	}
	return nil
}

func (x *PerfCaseStep) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *PerfCaseStep) GetMethod() string {
	if x != nil {
		return x.Method
	}
	return ""
}

func (x *PerfCaseStep) GetHeaders() map[string]string {
	if x != nil {
		return x.Headers
	}
	return nil
}

func (x *PerfCaseStep) GetBody() string {
	if x != nil {
		return x.Body
	}
	return ""
}

func (x *PerfCaseStep) GetExports() []*PerfCaseStep_Export {
	if x != nil {
		return x.Exports
	}
	return nil
}

func (x *PerfCaseStep) GetSleep() string {
	if x != nil {
		return x.Sleep
	}
	return ""
}

func (x *PerfCaseStep) GetKey() string {
	if x != nil {
		return x.Key
	}
	return ""
}

// PerfCaseContent 压测用例内容
type PerfCaseContent struct {
	state protoimpl.MessageState `protogen:"open.v1"`

	SetupSteps []*PerfCaseStep `protobuf:"bytes,1,rep,name=setup_steps,json=setupSteps,proto3" json:"setup_steps" yaml:"SetupSteps"` // 前置步骤列表

	SerialSteps []*PerfCaseStep `protobuf:"bytes,2,rep,name=serial_steps,json=serialSteps,proto3" json:"serial_steps" yaml:"SerialSteps"` // 串行步骤列表

	ParallelSteps []*PerfCaseStep `protobuf:"bytes,3,rep,name=parallel_steps,json=parallelSteps,proto3" json:"parallel_steps" yaml:"ParallelSteps"` // 并行步骤列表

	TeardownSteps []*PerfCaseStep `protobuf:"bytes,4,rep,name=teardown_steps,json=teardownSteps,proto3" json:"teardown_steps" yaml:"TeardownSteps"` // 后置步骤列表
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PerfCaseContent) Reset() {
	*x = PerfCaseContent{}
	mi := &file_common_perf_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PerfCaseContent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PerfCaseContent) ProtoMessage() {}

func (x *PerfCaseContent) ProtoReflect() protoreflect.Message {
	mi := &file_common_perf_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PerfCaseContent.ProtoReflect.Descriptor instead.
func (*PerfCaseContent) Descriptor() ([]byte, []int) {
	return file_common_perf_proto_rawDescGZIP(), []int{2}
}

func (x *PerfCaseContent) GetSetupSteps() []*PerfCaseStep {
	if x != nil {
		return x.SetupSteps
	}
	return nil
}

func (x *PerfCaseContent) GetSerialSteps() []*PerfCaseStep {
	if x != nil {
		return x.SerialSteps
	}
	return nil
}

func (x *PerfCaseContent) GetParallelSteps() []*PerfCaseStep {
	if x != nil {
		return x.ParallelSteps
	}
	return nil
}

func (x *PerfCaseContent) GetTeardownSteps() []*PerfCaseStep {
	if x != nil {
		return x.TeardownSteps
	}
	return nil
}

// PerfDataContent 压测数据内容
type PerfDataContent struct {
	state         protoimpl.MessageState  `protogen:"open.v1"`
	Lines         []*PerfDataContent_Line `protobuf:"bytes,1,rep,name=lines,proto3" json:"lines,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PerfDataContent) Reset() {
	*x = PerfDataContent{}
	mi := &file_common_perf_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PerfDataContent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PerfDataContent) ProtoMessage() {}

func (x *PerfDataContent) ProtoReflect() protoreflect.Message {
	mi := &file_common_perf_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PerfDataContent.ProtoReflect.Descriptor instead.
func (*PerfDataContent) Descriptor() ([]byte, []int) {
	return file_common_perf_proto_rawDescGZIP(), []int{3}
}

func (x *PerfDataContent) GetLines() []*PerfDataContent_Line {
	if x != nil {
		return x.Lines
	}
	return nil
}

// PerfRateLimits 压测相关的限流配置
type PerfRateLimits struct {
	state         protoimpl.MessageState          `protogen:"open.v1"`
	Auth          *PerfRateLimits_AuthConfig      `protobuf:"bytes,1,opt,name=auth,proto3" json:"auth,omitempty"`           // 认证接口的限流配置
	Heartbeat     *PerfRateLimits_HeartbeatConfig `protobuf:"bytes,2,opt,name=heartbeat,proto3" json:"heartbeat,omitempty"` // 心跳接口的限流配置
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PerfRateLimits) Reset() {
	*x = PerfRateLimits{}
	mi := &file_common_perf_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PerfRateLimits) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PerfRateLimits) ProtoMessage() {}

func (x *PerfRateLimits) ProtoReflect() protoreflect.Message {
	mi := &file_common_perf_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PerfRateLimits.ProtoReflect.Descriptor instead.
func (*PerfRateLimits) Descriptor() ([]byte, []int) {
	return file_common_perf_proto_rawDescGZIP(), []int{4}
}

func (x *PerfRateLimits) GetAuth() *PerfRateLimits_AuthConfig {
	if x != nil {
		return x.Auth
	}
	return nil
}

func (x *PerfRateLimits) GetHeartbeat() *PerfRateLimits_HeartbeatConfig {
	if x != nil {
		return x.Heartbeat
	}
	return nil
}

// PerfCaseStepV2 压测步骤
type PerfCaseStepV2 struct {
	state protoimpl.MessageState `protogen:"open.v1"`

	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name" yaml:"Name"` // 步骤名称

	RateLimits []*RateLimitV2 `protobuf:"bytes,2,rep,name=rate_limits,json=rateLimits,proto3" json:"rate_limits" yaml:"RateLimits"` // 限流配置

	Url string `protobuf:"bytes,3,opt,name=url,proto3" json:"url" yaml:"Url"` // 请求URL

	Method string `protobuf:"bytes,4,opt,name=method,proto3" json:"method" yaml:"Method"` // 请求方法

	Headers map[string]string `protobuf:"bytes,5,rep,name=headers,proto3" json:"headers" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value" yaml:"Headers"` // 请求头

	Body string `protobuf:"bytes,6,opt,name=body,proto3" json:"body" yaml:"Body"` // 请求体

	Exports []*PerfCaseStepV2_Export `protobuf:"bytes,7,rep,name=exports,proto3" json:"exports" yaml:"Exports"` // 出参列表

	Sleep         string `protobuf:"bytes,8,opt,name=sleep,proto3" json:"sleep" yaml:"Sleep"` // 休眠时间
	Key           string `protobuf:"bytes,99,opt,name=key,proto3" json:"key,omitempty"`
	Service       string `protobuf:"bytes,100,opt,name=service,proto3" json:"service,omitempty"`                                      // 服务名称
	Namespace     string `protobuf:"bytes,101,opt,name=namespace,proto3" json:"namespace,omitempty"`                                  // 命名空间
	Cmd           uint32 `protobuf:"varint,102,opt,name=cmd,proto3" json:"cmd,omitempty"`                                             // 命令号（`TT`专用）
	GrpcPath      string `protobuf:"bytes,103,opt,name=grpc_path,json=grpcPath,proto3" json:"grpc_path,omitempty"`                    // gRPC路径（`TT`专用）
	Deprecated    bool   `protobuf:"varint,104,opt,name=deprecated,proto3" json:"deprecated,omitempty"`                               // 是否已弃用（`TT`专用）
	ReferenceQps  *int64 `protobuf:"varint,105,opt,name=reference_qps,json=referenceQps,proto3,oneof" json:"reference_qps,omitempty"` // QPS参考值
	QueryPath     string `protobuf:"bytes,106,opt,name=query_path,json=queryPath,proto3" json:"query_path,omitempty"`                 // 请求路径（查询天相）
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PerfCaseStepV2) Reset() {
	*x = PerfCaseStepV2{}
	mi := &file_common_perf_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PerfCaseStepV2) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PerfCaseStepV2) ProtoMessage() {}

func (x *PerfCaseStepV2) ProtoReflect() protoreflect.Message {
	mi := &file_common_perf_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PerfCaseStepV2.ProtoReflect.Descriptor instead.
func (*PerfCaseStepV2) Descriptor() ([]byte, []int) {
	return file_common_perf_proto_rawDescGZIP(), []int{5}
}

func (x *PerfCaseStepV2) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *PerfCaseStepV2) GetRateLimits() []*RateLimitV2 {
	if x != nil {
		return x.RateLimits
	}
	return nil
}

func (x *PerfCaseStepV2) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *PerfCaseStepV2) GetMethod() string {
	if x != nil {
		return x.Method
	}
	return ""
}

func (x *PerfCaseStepV2) GetHeaders() map[string]string {
	if x != nil {
		return x.Headers
	}
	return nil
}

func (x *PerfCaseStepV2) GetBody() string {
	if x != nil {
		return x.Body
	}
	return ""
}

func (x *PerfCaseStepV2) GetExports() []*PerfCaseStepV2_Export {
	if x != nil {
		return x.Exports
	}
	return nil
}

func (x *PerfCaseStepV2) GetSleep() string {
	if x != nil {
		return x.Sleep
	}
	return ""
}

func (x *PerfCaseStepV2) GetKey() string {
	if x != nil {
		return x.Key
	}
	return ""
}

func (x *PerfCaseStepV2) GetService() string {
	if x != nil {
		return x.Service
	}
	return ""
}

func (x *PerfCaseStepV2) GetNamespace() string {
	if x != nil {
		return x.Namespace
	}
	return ""
}

func (x *PerfCaseStepV2) GetCmd() uint32 {
	if x != nil {
		return x.Cmd
	}
	return 0
}

func (x *PerfCaseStepV2) GetGrpcPath() string {
	if x != nil {
		return x.GrpcPath
	}
	return ""
}

func (x *PerfCaseStepV2) GetDeprecated() bool {
	if x != nil {
		return x.Deprecated
	}
	return false
}

func (x *PerfCaseStepV2) GetReferenceQps() int64 {
	if x != nil && x.ReferenceQps != nil {
		return *x.ReferenceQps
	}
	return 0
}

func (x *PerfCaseStepV2) GetQueryPath() string {
	if x != nil {
		return x.QueryPath
	}
	return ""
}

// PerfCaseContentV2 压测用例内容
type PerfCaseContentV2 struct {
	state protoimpl.MessageState `protogen:"open.v1"`

	SetupSteps []*PerfCaseStepV2 `protobuf:"bytes,1,rep,name=setup_steps,json=setupSteps,proto3" json:"setup_steps" yaml:"SetupSteps"` // 前置步骤列表

	SerialSteps []*PerfCaseStepV2 `protobuf:"bytes,2,rep,name=serial_steps,json=serialSteps,proto3" json:"serial_steps" yaml:"SerialSteps"` // 串行步骤列表

	ParallelSteps []*PerfCaseStepV2 `protobuf:"bytes,3,rep,name=parallel_steps,json=parallelSteps,proto3" json:"parallel_steps" yaml:"ParallelSteps"` // 并行步骤列表

	TeardownSteps []*PerfCaseStepV2 `protobuf:"bytes,4,rep,name=teardown_steps,json=teardownSteps,proto3" json:"teardown_steps" yaml:"TeardownSteps"` // 后置步骤列表
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PerfCaseContentV2) Reset() {
	*x = PerfCaseContentV2{}
	mi := &file_common_perf_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PerfCaseContentV2) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PerfCaseContentV2) ProtoMessage() {}

func (x *PerfCaseContentV2) ProtoReflect() protoreflect.Message {
	mi := &file_common_perf_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PerfCaseContentV2.ProtoReflect.Descriptor instead.
func (*PerfCaseContentV2) Descriptor() ([]byte, []int) {
	return file_common_perf_proto_rawDescGZIP(), []int{6}
}

func (x *PerfCaseContentV2) GetSetupSteps() []*PerfCaseStepV2 {
	if x != nil {
		return x.SetupSteps
	}
	return nil
}

func (x *PerfCaseContentV2) GetSerialSteps() []*PerfCaseStepV2 {
	if x != nil {
		return x.SerialSteps
	}
	return nil
}

func (x *PerfCaseContentV2) GetParallelSteps() []*PerfCaseStepV2 {
	if x != nil {
		return x.ParallelSteps
	}
	return nil
}

func (x *PerfCaseContentV2) GetTeardownSteps() []*PerfCaseStepV2 {
	if x != nil {
		return x.TeardownSteps
	}
	return nil
}

// PerfServiceMetaData 压测涉及的服务的元数据（注：TT相关协议专用）
type PerfServiceMetaData struct {
	state                  protoimpl.MessageState `protogen:"open.v1"`
	Name                   string                 `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`                                                                   // 服务名称
	Namespace              string                 `protobuf:"bytes,2,opt,name=namespace,proto3" json:"namespace,omitempty"`                                                         // 命名空间
	Developers             []*PerfUserInfo        `protobuf:"bytes,3,rep,name=developers,proto3" json:"developers,omitempty"`                                                       // 研发人员
	Maintainers            []*PerfUserInfo        `protobuf:"bytes,4,rep,name=maintainers,proto3" json:"maintainers,omitempty"`                                                     // 运维人员
	DatabaseAdministrators []*PerfUserInfo        `protobuf:"bytes,5,rep,name=database_administrators,json=databaseAdministrators,proto3" json:"database_administrators,omitempty"` // 数据库管理人员
	unknownFields          protoimpl.UnknownFields
	sizeCache              protoimpl.SizeCache
}

func (x *PerfServiceMetaData) Reset() {
	*x = PerfServiceMetaData{}
	mi := &file_common_perf_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PerfServiceMetaData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PerfServiceMetaData) ProtoMessage() {}

func (x *PerfServiceMetaData) ProtoReflect() protoreflect.Message {
	mi := &file_common_perf_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PerfServiceMetaData.ProtoReflect.Descriptor instead.
func (*PerfServiceMetaData) Descriptor() ([]byte, []int) {
	return file_common_perf_proto_rawDescGZIP(), []int{7}
}

func (x *PerfServiceMetaData) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *PerfServiceMetaData) GetNamespace() string {
	if x != nil {
		return x.Namespace
	}
	return ""
}

func (x *PerfServiceMetaData) GetDevelopers() []*PerfUserInfo {
	if x != nil {
		return x.Developers
	}
	return nil
}

func (x *PerfServiceMetaData) GetMaintainers() []*PerfUserInfo {
	if x != nil {
		return x.Maintainers
	}
	return nil
}

func (x *PerfServiceMetaData) GetDatabaseAdministrators() []*PerfUserInfo {
	if x != nil {
		return x.DatabaseAdministrators
	}
	return nil
}

// PerfUserInfo 压测涉及的用户的信息
type PerfUserInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Account       string                 `protobuf:"bytes,1,opt,name=account,proto3" json:"account,omitempty"`                           // 账号
	Fullname      string                 `protobuf:"bytes,2,opt,name=fullname,proto3" json:"fullname,omitempty"`                         // 全名
	Email         string                 `protobuf:"bytes,3,opt,name=email,proto3" json:"email,omitempty"`                               // 邮箱
	LarkUserId    string                 `protobuf:"bytes,4,opt,name=lark_user_id,json=larkUserId,proto3" json:"lark_user_id,omitempty"` // 飞书的用户ID
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PerfUserInfo) Reset() {
	*x = PerfUserInfo{}
	mi := &file_common_perf_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PerfUserInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PerfUserInfo) ProtoMessage() {}

func (x *PerfUserInfo) ProtoReflect() protoreflect.Message {
	mi := &file_common_perf_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PerfUserInfo.ProtoReflect.Descriptor instead.
func (*PerfUserInfo) Descriptor() ([]byte, []int) {
	return file_common_perf_proto_rawDescGZIP(), []int{8}
}

func (x *PerfUserInfo) GetAccount() string {
	if x != nil {
		return x.Account
	}
	return ""
}

func (x *PerfUserInfo) GetFullname() string {
	if x != nil {
		return x.Fullname
	}
	return ""
}

func (x *PerfUserInfo) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *PerfUserInfo) GetLarkUserId() string {
	if x != nil {
		return x.LarkUserId
	}
	return ""
}

// PerfStopRule 压测停止规则
type PerfStopRule struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	MetricType    MetricType             `protobuf:"varint,1,opt,name=metric_type,json=metricType,proto3,enum=common.MetricType" json:"metric_type,omitempty"` // 指标类型
	Threshold     float64                `protobuf:"fixed64,2,opt,name=threshold,proto3" json:"threshold,omitempty"`                                           // 阈值
	Duration      uint32                 `protobuf:"varint,3,opt,name=duration,proto3" json:"duration,omitempty"`                                              // 持续时间（秒）
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PerfStopRule) Reset() {
	*x = PerfStopRule{}
	mi := &file_common_perf_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PerfStopRule) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PerfStopRule) ProtoMessage() {}

func (x *PerfStopRule) ProtoReflect() protoreflect.Message {
	mi := &file_common_perf_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PerfStopRule.ProtoReflect.Descriptor instead.
func (*PerfStopRule) Descriptor() ([]byte, []int) {
	return file_common_perf_proto_rawDescGZIP(), []int{9}
}

func (x *PerfStopRule) GetMetricType() MetricType {
	if x != nil {
		return x.MetricType
	}
	return MetricType_MetricType_NULL
}

func (x *PerfStopRule) GetThreshold() float64 {
	if x != nil {
		return x.Threshold
	}
	return 0
}

func (x *PerfStopRule) GetDuration() uint32 {
	if x != nil {
		return x.Duration
	}
	return 0
}

type PerfKeepalive_AuthConfig struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	RateLimit     *RateLimit             `protobuf:"bytes,1,opt,name=rate_limit,json=rateLimit,proto3" json:"rate_limit,omitempty"`
	Key           string                 `protobuf:"bytes,99,opt,name=key,proto3" json:"key,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PerfKeepalive_AuthConfig) Reset() {
	*x = PerfKeepalive_AuthConfig{}
	mi := &file_common_perf_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PerfKeepalive_AuthConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PerfKeepalive_AuthConfig) ProtoMessage() {}

func (x *PerfKeepalive_AuthConfig) ProtoReflect() protoreflect.Message {
	mi := &file_common_perf_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PerfKeepalive_AuthConfig.ProtoReflect.Descriptor instead.
func (*PerfKeepalive_AuthConfig) Descriptor() ([]byte, []int) {
	return file_common_perf_proto_rawDescGZIP(), []int{0, 0}
}

func (x *PerfKeepalive_AuthConfig) GetRateLimit() *RateLimit {
	if x != nil {
		return x.RateLimit
	}
	return nil
}

func (x *PerfKeepalive_AuthConfig) GetKey() string {
	if x != nil {
		return x.Key
	}
	return ""
}

type PerfKeepalive_HeartbeatConfig struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	RateLimit     *RateLimit             `protobuf:"bytes,1,opt,name=rate_limit,json=rateLimit,proto3" json:"rate_limit,omitempty"`
	Interval      uint32                 `protobuf:"varint,2,opt,name=interval,proto3" json:"interval,omitempty"`
	Key           string                 `protobuf:"bytes,99,opt,name=key,proto3" json:"key,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PerfKeepalive_HeartbeatConfig) Reset() {
	*x = PerfKeepalive_HeartbeatConfig{}
	mi := &file_common_perf_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PerfKeepalive_HeartbeatConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PerfKeepalive_HeartbeatConfig) ProtoMessage() {}

func (x *PerfKeepalive_HeartbeatConfig) ProtoReflect() protoreflect.Message {
	mi := &file_common_perf_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PerfKeepalive_HeartbeatConfig.ProtoReflect.Descriptor instead.
func (*PerfKeepalive_HeartbeatConfig) Descriptor() ([]byte, []int) {
	return file_common_perf_proto_rawDescGZIP(), []int{0, 1}
}

func (x *PerfKeepalive_HeartbeatConfig) GetRateLimit() *RateLimit {
	if x != nil {
		return x.RateLimit
	}
	return nil
}

func (x *PerfKeepalive_HeartbeatConfig) GetInterval() uint32 {
	if x != nil {
		return x.Interval
	}
	return 0
}

func (x *PerfKeepalive_HeartbeatConfig) GetKey() string {
	if x != nil {
		return x.Key
	}
	return ""
}

type PerfCaseStep_Export struct {
	state protoimpl.MessageState `protogen:"open.v1"`

	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name" yaml:"Name"` // 变量名

	Expression    string `protobuf:"bytes,2,opt,name=expression,proto3" json:"expression" yaml:"Expression"` // 表达式
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PerfCaseStep_Export) Reset() {
	*x = PerfCaseStep_Export{}
	mi := &file_common_perf_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PerfCaseStep_Export) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PerfCaseStep_Export) ProtoMessage() {}

func (x *PerfCaseStep_Export) ProtoReflect() protoreflect.Message {
	mi := &file_common_perf_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PerfCaseStep_Export.ProtoReflect.Descriptor instead.
func (*PerfCaseStep_Export) Descriptor() ([]byte, []int) {
	return file_common_perf_proto_rawDescGZIP(), []int{1, 0}
}

func (x *PerfCaseStep_Export) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *PerfCaseStep_Export) GetExpression() string {
	if x != nil {
		return x.Expression
	}
	return ""
}

type PerfDataContent_Line struct {
	state protoimpl.MessageState `protogen:"open.v1"`

	AuthData *structpb.Struct `protobuf:"bytes,1,opt,name=auth_data,json=authData,proto3" json:"auth_data" yaml:"AuthData" csv:"auth_data"` // 认证数据

	RequestData   *structpb.ListValue `protobuf:"bytes,2,opt,name=request_data,json=requestData,proto3" json:"request_data" yaml:"RequestData" csv:"request_data"` // 请求数据
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PerfDataContent_Line) Reset() {
	*x = PerfDataContent_Line{}
	mi := &file_common_perf_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PerfDataContent_Line) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PerfDataContent_Line) ProtoMessage() {}

func (x *PerfDataContent_Line) ProtoReflect() protoreflect.Message {
	mi := &file_common_perf_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PerfDataContent_Line.ProtoReflect.Descriptor instead.
func (*PerfDataContent_Line) Descriptor() ([]byte, []int) {
	return file_common_perf_proto_rawDescGZIP(), []int{3, 0}
}

func (x *PerfDataContent_Line) GetAuthData() *structpb.Struct {
	if x != nil {
		return x.AuthData
	}
	return nil
}

func (x *PerfDataContent_Line) GetRequestData() *structpb.ListValue {
	if x != nil {
		return x.RequestData
	}
	return nil
}

type PerfRateLimits_AuthConfig struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	RateLimits    []*RateLimitV2         `protobuf:"bytes,1,rep,name=rate_limits,json=rateLimits,proto3" json:"rate_limits,omitempty"`
	Key           string                 `protobuf:"bytes,99,opt,name=key,proto3" json:"key,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PerfRateLimits_AuthConfig) Reset() {
	*x = PerfRateLimits_AuthConfig{}
	mi := &file_common_perf_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PerfRateLimits_AuthConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PerfRateLimits_AuthConfig) ProtoMessage() {}

func (x *PerfRateLimits_AuthConfig) ProtoReflect() protoreflect.Message {
	mi := &file_common_perf_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PerfRateLimits_AuthConfig.ProtoReflect.Descriptor instead.
func (*PerfRateLimits_AuthConfig) Descriptor() ([]byte, []int) {
	return file_common_perf_proto_rawDescGZIP(), []int{4, 0}
}

func (x *PerfRateLimits_AuthConfig) GetRateLimits() []*RateLimitV2 {
	if x != nil {
		return x.RateLimits
	}
	return nil
}

func (x *PerfRateLimits_AuthConfig) GetKey() string {
	if x != nil {
		return x.Key
	}
	return ""
}

type PerfRateLimits_HeartbeatConfig struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	RateLimits    []*RateLimitV2         `protobuf:"bytes,1,rep,name=rate_limits,json=rateLimits,proto3" json:"rate_limits,omitempty"`
	Interval      uint32                 `protobuf:"varint,2,opt,name=interval,proto3" json:"interval,omitempty"`
	Key           string                 `protobuf:"bytes,99,opt,name=key,proto3" json:"key,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PerfRateLimits_HeartbeatConfig) Reset() {
	*x = PerfRateLimits_HeartbeatConfig{}
	mi := &file_common_perf_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PerfRateLimits_HeartbeatConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PerfRateLimits_HeartbeatConfig) ProtoMessage() {}

func (x *PerfRateLimits_HeartbeatConfig) ProtoReflect() protoreflect.Message {
	mi := &file_common_perf_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PerfRateLimits_HeartbeatConfig.ProtoReflect.Descriptor instead.
func (*PerfRateLimits_HeartbeatConfig) Descriptor() ([]byte, []int) {
	return file_common_perf_proto_rawDescGZIP(), []int{4, 1}
}

func (x *PerfRateLimits_HeartbeatConfig) GetRateLimits() []*RateLimitV2 {
	if x != nil {
		return x.RateLimits
	}
	return nil
}

func (x *PerfRateLimits_HeartbeatConfig) GetInterval() uint32 {
	if x != nil {
		return x.Interval
	}
	return 0
}

func (x *PerfRateLimits_HeartbeatConfig) GetKey() string {
	if x != nil {
		return x.Key
	}
	return ""
}

type PerfCaseStepV2_Export struct {
	state protoimpl.MessageState `protogen:"open.v1"`

	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name" yaml:"Name"` // 变量名

	Expression    string `protobuf:"bytes,2,opt,name=expression,proto3" json:"expression" yaml:"Expression"` // 表达式
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PerfCaseStepV2_Export) Reset() {
	*x = PerfCaseStepV2_Export{}
	mi := &file_common_perf_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PerfCaseStepV2_Export) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PerfCaseStepV2_Export) ProtoMessage() {}

func (x *PerfCaseStepV2_Export) ProtoReflect() protoreflect.Message {
	mi := &file_common_perf_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PerfCaseStepV2_Export.ProtoReflect.Descriptor instead.
func (*PerfCaseStepV2_Export) Descriptor() ([]byte, []int) {
	return file_common_perf_proto_rawDescGZIP(), []int{5, 0}
}

func (x *PerfCaseStepV2_Export) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *PerfCaseStepV2_Export) GetExpression() string {
	if x != nil {
		return x.Expression
	}
	return ""
}

var File_common_perf_proto protoreflect.FileDescriptor

var file_common_perf_proto_rawDesc = []byte{
	0x0a, 0x11, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x70, 0x65, 0x72, 0x66, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x12, 0x06, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x1a, 0x1c, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x73, 0x74, 0x72,
	0x75, 0x63, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64,
	0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x11, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x12, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x6c, 0x69,
	0x6d, 0x69, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xed, 0x02, 0x0a, 0x0d, 0x50, 0x65,
	0x72, 0x66, 0x4b, 0x65, 0x65, 0x70, 0x61, 0x6c, 0x69, 0x76, 0x65, 0x12, 0x34, 0x0a, 0x04, 0x61,
	0x75, 0x74, 0x68, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2e, 0x50, 0x65, 0x72, 0x66, 0x4b, 0x65, 0x65, 0x70, 0x61, 0x6c, 0x69, 0x76, 0x65,
	0x2e, 0x41, 0x75, 0x74, 0x68, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x04, 0x61, 0x75, 0x74,
	0x68, 0x12, 0x43, 0x0a, 0x09, 0x68, 0x65, 0x61, 0x72, 0x74, 0x62, 0x65, 0x61, 0x74, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x65,
	0x72, 0x66, 0x4b, 0x65, 0x65, 0x70, 0x61, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x48, 0x65, 0x61, 0x72,
	0x74, 0x62, 0x65, 0x61, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x09, 0x68, 0x65, 0x61,
	0x72, 0x74, 0x62, 0x65, 0x61, 0x74, 0x1a, 0x5a, 0x0a, 0x0a, 0x41, 0x75, 0x74, 0x68, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x12, 0x3a, 0x0a, 0x0a, 0x72, 0x61, 0x74, 0x65, 0x5f, 0x6c, 0x69, 0x6d,
	0x69, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2e, 0x52, 0x61, 0x74, 0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x42, 0x08, 0xfa, 0x42, 0x05,
	0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x09, 0x72, 0x61, 0x74, 0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74,
	0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x63, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b,
	0x65, 0x79, 0x1a, 0x84, 0x01, 0x0a, 0x0f, 0x48, 0x65, 0x61, 0x72, 0x74, 0x62, 0x65, 0x61, 0x74,
	0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x3a, 0x0a, 0x0a, 0x72, 0x61, 0x74, 0x65, 0x5f, 0x6c,
	0x69, 0x6d, 0x69, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x52, 0x61, 0x74, 0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x42, 0x08, 0xfa,
	0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x09, 0x72, 0x61, 0x74, 0x65, 0x4c, 0x69, 0x6d,
	0x69, 0x74, 0x12, 0x23, 0x0a, 0x08, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0d, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x2a, 0x02, 0x20, 0x00, 0x52, 0x08, 0x69,
	0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x63,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x22, 0xa2, 0x04, 0x0a, 0x0c, 0x50, 0x65,
	0x72, 0x66, 0x43, 0x61, 0x73, 0x65, 0x53, 0x74, 0x65, 0x70, 0x12, 0x1d, 0x0a, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10,
	0x01, 0x18, 0x40, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x3a, 0x0a, 0x0a, 0x72, 0x61, 0x74,
	0x65, 0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x52, 0x61, 0x74, 0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74,
	0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x09, 0x72, 0x61, 0x74, 0x65,
	0x4c, 0x69, 0x6d, 0x69, 0x74, 0x12, 0x1d, 0x0a, 0x03, 0x75, 0x72, 0x6c, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x0b, 0xfa, 0x42, 0x08, 0x72, 0x06, 0x18, 0xff, 0x01, 0xd0, 0x01, 0x01, 0x52,
	0x03, 0x75, 0x72, 0x6c, 0x12, 0x22, 0x0a, 0x06, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x72, 0x05, 0x10, 0x03, 0x18, 0xff, 0x01,
	0x52, 0x06, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x12, 0x4b, 0x0a, 0x07, 0x68, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2e, 0x50, 0x65, 0x72, 0x66, 0x43, 0x61, 0x73, 0x65, 0x53, 0x74, 0x65, 0x70, 0x2e,
	0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x42, 0x0e, 0xfa, 0x42,
	0x0b, 0x9a, 0x01, 0x08, 0x22, 0x04, 0x72, 0x02, 0x10, 0x01, 0x30, 0x01, 0x52, 0x07, 0x68, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x73, 0x12, 0x1f, 0x0a, 0x04, 0x62, 0x6f, 0x64, 0x79, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x0b, 0xfa, 0x42, 0x08, 0x72, 0x06, 0x18, 0x80, 0x08, 0xd0, 0x01, 0x01,
	0x52, 0x04, 0x62, 0x6f, 0x64, 0x79, 0x12, 0x46, 0x0a, 0x07, 0x65, 0x78, 0x70, 0x6f, 0x72, 0x74,
	0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x50, 0x65, 0x72, 0x66, 0x43, 0x61, 0x73, 0x65, 0x53, 0x74, 0x65, 0x70, 0x2e, 0x45, 0x78,
	0x70, 0x6f, 0x72, 0x74, 0x42, 0x0f, 0xfa, 0x42, 0x0c, 0x92, 0x01, 0x09, 0x22, 0x05, 0x8a, 0x01,
	0x02, 0x10, 0x01, 0x28, 0x01, 0x52, 0x07, 0x65, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x12, 0x20,
	0x0a, 0x05, 0x73, 0x6c, 0x65, 0x65, 0x70, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0a, 0xfa,
	0x42, 0x07, 0x72, 0x05, 0x10, 0x02, 0xd0, 0x01, 0x01, 0x52, 0x05, 0x73, 0x6c, 0x65, 0x65, 0x70,
	0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x63, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b,
	0x65, 0x79, 0x1a, 0x4e, 0x0a, 0x06, 0x45, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x12, 0x1b, 0x0a, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72,
	0x02, 0x10, 0x01, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x27, 0x0a, 0x0a, 0x65, 0x78, 0x70,
	0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa,
	0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x0a, 0x65, 0x78, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69,
	0x6f, 0x6e, 0x1a, 0x3a, 0x0a, 0x0c, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x73, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0xbf,
	0x02, 0x0a, 0x0f, 0x50, 0x65, 0x72, 0x66, 0x43, 0x61, 0x73, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x74, 0x12, 0x46, 0x0a, 0x0b, 0x73, 0x65, 0x74, 0x75, 0x70, 0x5f, 0x73, 0x74, 0x65, 0x70,
	0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x50, 0x65, 0x72, 0x66, 0x43, 0x61, 0x73, 0x65, 0x53, 0x74, 0x65, 0x70, 0x42, 0x0f, 0xfa,
	0x42, 0x0c, 0x92, 0x01, 0x09, 0x22, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x28, 0x01, 0x52, 0x0a,
	0x73, 0x65, 0x74, 0x75, 0x70, 0x53, 0x74, 0x65, 0x70, 0x73, 0x12, 0x48, 0x0a, 0x0c, 0x73, 0x65,
	0x72, 0x69, 0x61, 0x6c, 0x5f, 0x73, 0x74, 0x65, 0x70, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x14, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x65, 0x72, 0x66, 0x43, 0x61,
	0x73, 0x65, 0x53, 0x74, 0x65, 0x70, 0x42, 0x0f, 0xfa, 0x42, 0x0c, 0x92, 0x01, 0x09, 0x22, 0x05,
	0x8a, 0x01, 0x02, 0x10, 0x01, 0x28, 0x01, 0x52, 0x0b, 0x73, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x53,
	0x74, 0x65, 0x70, 0x73, 0x12, 0x4c, 0x0a, 0x0e, 0x70, 0x61, 0x72, 0x61, 0x6c, 0x6c, 0x65, 0x6c,
	0x5f, 0x73, 0x74, 0x65, 0x70, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x65, 0x72, 0x66, 0x43, 0x61, 0x73, 0x65, 0x53, 0x74,
	0x65, 0x70, 0x42, 0x0f, 0xfa, 0x42, 0x0c, 0x92, 0x01, 0x09, 0x22, 0x05, 0x8a, 0x01, 0x02, 0x10,
	0x01, 0x28, 0x01, 0x52, 0x0d, 0x70, 0x61, 0x72, 0x61, 0x6c, 0x6c, 0x65, 0x6c, 0x53, 0x74, 0x65,
	0x70, 0x73, 0x12, 0x4c, 0x0a, 0x0e, 0x74, 0x65, 0x61, 0x72, 0x64, 0x6f, 0x77, 0x6e, 0x5f, 0x73,
	0x74, 0x65, 0x70, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x65, 0x72, 0x66, 0x43, 0x61, 0x73, 0x65, 0x53, 0x74, 0x65, 0x70,
	0x42, 0x0f, 0xfa, 0x42, 0x0c, 0x92, 0x01, 0x09, 0x22, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x28,
	0x01, 0x52, 0x0d, 0x74, 0x65, 0x61, 0x72, 0x64, 0x6f, 0x77, 0x6e, 0x53, 0x74, 0x65, 0x70, 0x73,
	0x22, 0xde, 0x01, 0x0a, 0x0f, 0x50, 0x65, 0x72, 0x66, 0x44, 0x61, 0x74, 0x61, 0x43, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x74, 0x12, 0x43, 0x0a, 0x05, 0x6c, 0x69, 0x6e, 0x65, 0x73, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x65, 0x72,
	0x66, 0x44, 0x61, 0x74, 0x61, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x2e, 0x4c, 0x69, 0x6e,
	0x65, 0x42, 0x0f, 0xfa, 0x42, 0x0c, 0x92, 0x01, 0x09, 0x08, 0x01, 0x22, 0x05, 0x8a, 0x01, 0x02,
	0x10, 0x01, 0x52, 0x05, 0x6c, 0x69, 0x6e, 0x65, 0x73, 0x1a, 0x85, 0x01, 0x0a, 0x04, 0x4c, 0x69,
	0x6e, 0x65, 0x12, 0x3e, 0x0a, 0x09, 0x61, 0x75, 0x74, 0x68, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x75, 0x63, 0x74, 0x42, 0x08,
	0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x08, 0x61, 0x75, 0x74, 0x68, 0x44, 0x61,
	0x74, 0x61, 0x12, 0x3d, 0x0a, 0x0c, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x64, 0x61,
	0x74, 0x61, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x56,
	0x61, 0x6c, 0x75, 0x65, 0x52, 0x0b, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x44, 0x61, 0x74,
	0x61, 0x22, 0x82, 0x03, 0x0a, 0x0e, 0x50, 0x65, 0x72, 0x66, 0x52, 0x61, 0x74, 0x65, 0x4c, 0x69,
	0x6d, 0x69, 0x74, 0x73, 0x12, 0x35, 0x0a, 0x04, 0x61, 0x75, 0x74, 0x68, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x21, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x65, 0x72, 0x66,
	0x52, 0x61, 0x74, 0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x73, 0x2e, 0x41, 0x75, 0x74, 0x68, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x04, 0x61, 0x75, 0x74, 0x68, 0x12, 0x44, 0x0a, 0x09, 0x68,
	0x65, 0x61, 0x72, 0x74, 0x62, 0x65, 0x61, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x65, 0x72, 0x66, 0x52, 0x61, 0x74, 0x65,
	0x4c, 0x69, 0x6d, 0x69, 0x74, 0x73, 0x2e, 0x48, 0x65, 0x61, 0x72, 0x74, 0x62, 0x65, 0x61, 0x74,
	0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x09, 0x68, 0x65, 0x61, 0x72, 0x74, 0x62, 0x65, 0x61,
	0x74, 0x1a, 0x63, 0x0a, 0x0a, 0x41, 0x75, 0x74, 0x68, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12,
	0x43, 0x0a, 0x0b, 0x72, 0x61, 0x74, 0x65, 0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x73, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x52, 0x61,
	0x74, 0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x56, 0x32, 0x42, 0x0d, 0xfa, 0x42, 0x0a, 0x92, 0x01,
	0x07, 0x22, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x0a, 0x72, 0x61, 0x74, 0x65, 0x4c, 0x69,
	0x6d, 0x69, 0x74, 0x73, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x63, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x1a, 0x8d, 0x01, 0x0a, 0x0f, 0x48, 0x65, 0x61, 0x72, 0x74,
	0x62, 0x65, 0x61, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x43, 0x0a, 0x0b, 0x72, 0x61,
	0x74, 0x65, 0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x13, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x52, 0x61, 0x74, 0x65, 0x4c, 0x69, 0x6d,
	0x69, 0x74, 0x56, 0x32, 0x42, 0x0d, 0xfa, 0x42, 0x0a, 0x92, 0x01, 0x07, 0x22, 0x05, 0x8a, 0x01,
	0x02, 0x10, 0x01, 0x52, 0x0a, 0x72, 0x61, 0x74, 0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x73, 0x12,
	0x23, 0x0a, 0x08, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0d, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x2a, 0x02, 0x20, 0x00, 0x52, 0x08, 0x69, 0x6e, 0x74, 0x65,
	0x72, 0x76, 0x61, 0x6c, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x63, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x22, 0x95, 0x06, 0x0a, 0x0e, 0x50, 0x65, 0x72, 0x66, 0x43,
	0x61, 0x73, 0x65, 0x53, 0x74, 0x65, 0x70, 0x56, 0x32, 0x12, 0x1d, 0x0a, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x01,
	0x18, 0x40, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x45, 0x0a, 0x0b, 0x72, 0x61, 0x74, 0x65,
	0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x13, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x52, 0x61, 0x74, 0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74,
	0x56, 0x32, 0x42, 0x0f, 0xfa, 0x42, 0x0c, 0x92, 0x01, 0x09, 0x22, 0x05, 0x8a, 0x01, 0x02, 0x10,
	0x01, 0x28, 0x01, 0x52, 0x0a, 0x72, 0x61, 0x74, 0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x73, 0x12,
	0x1d, 0x0a, 0x03, 0x75, 0x72, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0b, 0xfa, 0x42,
	0x08, 0x72, 0x06, 0x18, 0xff, 0x01, 0xd0, 0x01, 0x01, 0x52, 0x03, 0x75, 0x72, 0x6c, 0x12, 0x22,
	0x0a, 0x06, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0a,
	0xfa, 0x42, 0x07, 0x72, 0x05, 0x10, 0x03, 0x18, 0xff, 0x01, 0x52, 0x06, 0x6d, 0x65, 0x74, 0x68,
	0x6f, 0x64, 0x12, 0x4d, 0x0a, 0x07, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x73, 0x18, 0x05, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x65, 0x72,
	0x66, 0x43, 0x61, 0x73, 0x65, 0x53, 0x74, 0x65, 0x70, 0x56, 0x32, 0x2e, 0x48, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x42, 0x0e, 0xfa, 0x42, 0x0b, 0x9a, 0x01, 0x08,
	0x22, 0x04, 0x72, 0x02, 0x10, 0x01, 0x30, 0x01, 0x52, 0x07, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x73, 0x12, 0x1f, 0x0a, 0x04, 0x62, 0x6f, 0x64, 0x79, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x0b, 0xfa, 0x42, 0x08, 0x72, 0x06, 0x18, 0x80, 0x08, 0xd0, 0x01, 0x01, 0x52, 0x04, 0x62, 0x6f,
	0x64, 0x79, 0x12, 0x48, 0x0a, 0x07, 0x65, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x18, 0x07, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x65, 0x72,
	0x66, 0x43, 0x61, 0x73, 0x65, 0x53, 0x74, 0x65, 0x70, 0x56, 0x32, 0x2e, 0x45, 0x78, 0x70, 0x6f,
	0x72, 0x74, 0x42, 0x0f, 0xfa, 0x42, 0x0c, 0x92, 0x01, 0x09, 0x22, 0x05, 0x8a, 0x01, 0x02, 0x10,
	0x01, 0x28, 0x01, 0x52, 0x07, 0x65, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x12, 0x20, 0x0a, 0x05,
	0x73, 0x6c, 0x65, 0x65, 0x70, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0a, 0xfa, 0x42, 0x07,
	0x72, 0x05, 0x10, 0x02, 0xd0, 0x01, 0x01, 0x52, 0x05, 0x73, 0x6c, 0x65, 0x65, 0x70, 0x12, 0x10,
	0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x63, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79,
	0x12, 0x18, 0x0a, 0x07, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x18, 0x64, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x6e, 0x61,
	0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x18, 0x65, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6e,
	0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x63, 0x6d, 0x64, 0x18,
	0x66, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x03, 0x63, 0x6d, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x67, 0x72,
	0x70, 0x63, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x18, 0x67, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x67,
	0x72, 0x70, 0x63, 0x50, 0x61, 0x74, 0x68, 0x12, 0x1e, 0x0a, 0x0a, 0x64, 0x65, 0x70, 0x72, 0x65,
	0x63, 0x61, 0x74, 0x65, 0x64, 0x18, 0x68, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0a, 0x64, 0x65, 0x70,
	0x72, 0x65, 0x63, 0x61, 0x74, 0x65, 0x64, 0x12, 0x28, 0x0a, 0x0d, 0x72, 0x65, 0x66, 0x65, 0x72,
	0x65, 0x6e, 0x63, 0x65, 0x5f, 0x71, 0x70, 0x73, 0x18, 0x69, 0x20, 0x01, 0x28, 0x03, 0x48, 0x00,
	0x52, 0x0c, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x51, 0x70, 0x73, 0x88, 0x01,
	0x01, 0x12, 0x1d, 0x0a, 0x0a, 0x71, 0x75, 0x65, 0x72, 0x79, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x18,
	0x6a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x71, 0x75, 0x65, 0x72, 0x79, 0x50, 0x61, 0x74, 0x68,
	0x1a, 0x4e, 0x0a, 0x06, 0x45, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x12, 0x1b, 0x0a, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10,
	0x01, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x27, 0x0a, 0x0a, 0x65, 0x78, 0x70, 0x72, 0x65,
	0x73, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04,
	0x72, 0x02, 0x10, 0x01, 0x52, 0x0a, 0x65, 0x78, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e,
	0x1a, 0x3a, 0x0a, 0x0c, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79,
	0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b,
	0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x42, 0x10, 0x0a, 0x0e,
	0x5f, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x5f, 0x71, 0x70, 0x73, 0x22, 0xc9,
	0x02, 0x0a, 0x11, 0x50, 0x65, 0x72, 0x66, 0x43, 0x61, 0x73, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x74, 0x56, 0x32, 0x12, 0x48, 0x0a, 0x0b, 0x73, 0x65, 0x74, 0x75, 0x70, 0x5f, 0x73, 0x74,
	0x65, 0x70, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2e, 0x50, 0x65, 0x72, 0x66, 0x43, 0x61, 0x73, 0x65, 0x53, 0x74, 0x65, 0x70, 0x56,
	0x32, 0x42, 0x0f, 0xfa, 0x42, 0x0c, 0x92, 0x01, 0x09, 0x22, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01,
	0x28, 0x01, 0x52, 0x0a, 0x73, 0x65, 0x74, 0x75, 0x70, 0x53, 0x74, 0x65, 0x70, 0x73, 0x12, 0x4a,
	0x0a, 0x0c, 0x73, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x5f, 0x73, 0x74, 0x65, 0x70, 0x73, 0x18, 0x02,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x65,
	0x72, 0x66, 0x43, 0x61, 0x73, 0x65, 0x53, 0x74, 0x65, 0x70, 0x56, 0x32, 0x42, 0x0f, 0xfa, 0x42,
	0x0c, 0x92, 0x01, 0x09, 0x22, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x28, 0x01, 0x52, 0x0b, 0x73,
	0x65, 0x72, 0x69, 0x61, 0x6c, 0x53, 0x74, 0x65, 0x70, 0x73, 0x12, 0x4e, 0x0a, 0x0e, 0x70, 0x61,
	0x72, 0x61, 0x6c, 0x6c, 0x65, 0x6c, 0x5f, 0x73, 0x74, 0x65, 0x70, 0x73, 0x18, 0x03, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x16, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x65, 0x72, 0x66,
	0x43, 0x61, 0x73, 0x65, 0x53, 0x74, 0x65, 0x70, 0x56, 0x32, 0x42, 0x0f, 0xfa, 0x42, 0x0c, 0x92,
	0x01, 0x09, 0x22, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x28, 0x01, 0x52, 0x0d, 0x70, 0x61, 0x72,
	0x61, 0x6c, 0x6c, 0x65, 0x6c, 0x53, 0x74, 0x65, 0x70, 0x73, 0x12, 0x4e, 0x0a, 0x0e, 0x74, 0x65,
	0x61, 0x72, 0x64, 0x6f, 0x77, 0x6e, 0x5f, 0x73, 0x74, 0x65, 0x70, 0x73, 0x18, 0x04, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x16, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x65, 0x72, 0x66,
	0x43, 0x61, 0x73, 0x65, 0x53, 0x74, 0x65, 0x70, 0x56, 0x32, 0x42, 0x0f, 0xfa, 0x42, 0x0c, 0x92,
	0x01, 0x09, 0x22, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x28, 0x01, 0x52, 0x0d, 0x74, 0x65, 0x61,
	0x72, 0x64, 0x6f, 0x77, 0x6e, 0x53, 0x74, 0x65, 0x70, 0x73, 0x22, 0x84, 0x02, 0x0a, 0x13, 0x50,
	0x65, 0x72, 0x66, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x4d, 0x65, 0x74, 0x61, 0x44, 0x61,
	0x74, 0x61, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70,
	0x61, 0x63, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73,
	0x70, 0x61, 0x63, 0x65, 0x12, 0x34, 0x0a, 0x0a, 0x64, 0x65, 0x76, 0x65, 0x6c, 0x6f, 0x70, 0x65,
	0x72, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2e, 0x50, 0x65, 0x72, 0x66, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0a,
	0x64, 0x65, 0x76, 0x65, 0x6c, 0x6f, 0x70, 0x65, 0x72, 0x73, 0x12, 0x36, 0x0a, 0x0b, 0x6d, 0x61,
	0x69, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x14, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x65, 0x72, 0x66, 0x55, 0x73, 0x65,
	0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0b, 0x6d, 0x61, 0x69, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65,
	0x72, 0x73, 0x12, 0x4d, 0x0a, 0x17, 0x64, 0x61, 0x74, 0x61, 0x62, 0x61, 0x73, 0x65, 0x5f, 0x61,
	0x64, 0x6d, 0x69, 0x6e, 0x69, 0x73, 0x74, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x73, 0x18, 0x05, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x65, 0x72,
	0x66, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x16, 0x64, 0x61, 0x74, 0x61, 0x62,
	0x61, 0x73, 0x65, 0x41, 0x64, 0x6d, 0x69, 0x6e, 0x69, 0x73, 0x74, 0x72, 0x61, 0x74, 0x6f, 0x72,
	0x73, 0x22, 0x7c, 0x0a, 0x0c, 0x50, 0x65, 0x72, 0x66, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66,
	0x6f, 0x12, 0x18, 0x0a, 0x07, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x66,
	0x75, 0x6c, 0x6c, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x66,
	0x75, 0x6c, 0x6c, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x12, 0x20, 0x0a,
	0x0c, 0x6c, 0x61, 0x72, 0x6b, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0a, 0x6c, 0x61, 0x72, 0x6b, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64, 0x22,
	0x7d, 0x0a, 0x0c, 0x50, 0x65, 0x72, 0x66, 0x53, 0x74, 0x6f, 0x70, 0x52, 0x75, 0x6c, 0x65, 0x12,
	0x33, 0x0a, 0x0b, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x12, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x4d, 0x65,
	0x74, 0x72, 0x69, 0x63, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0a, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x74, 0x68, 0x72, 0x65, 0x73, 0x68, 0x6f, 0x6c,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x01, 0x52, 0x09, 0x74, 0x68, 0x72, 0x65, 0x73, 0x68, 0x6f,
	0x6c, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x3c,
	0x5a, 0x3a, 0x67, 0x69, 0x74, 0x6c, 0x61, 0x62, 0x2e, 0x74, 0x74, 0x79, 0x75, 0x79, 0x69, 0x6e,
	0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x54, 0x65, 0x73, 0x74, 0x44, 0x65, 0x76, 0x65, 0x6c, 0x6f, 0x70,
	0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x70, 0x72, 0x6f, 0x62, 0x65, 0x2d, 0x62, 0x61, 0x63, 0x6b, 0x65,
	0x6e, 0x64, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_common_perf_proto_rawDescOnce sync.Once
	file_common_perf_proto_rawDescData = file_common_perf_proto_rawDesc
)

func file_common_perf_proto_rawDescGZIP() []byte {
	file_common_perf_proto_rawDescOnce.Do(func() {
		file_common_perf_proto_rawDescData = protoimpl.X.CompressGZIP(file_common_perf_proto_rawDescData)
	})
	return file_common_perf_proto_rawDescData
}

var file_common_perf_proto_msgTypes = make([]protoimpl.MessageInfo, 19)
var file_common_perf_proto_goTypes = []any{
	(*PerfKeepalive)(nil),                  // 0: common.PerfKeepalive
	(*PerfCaseStep)(nil),                   // 1: common.PerfCaseStep
	(*PerfCaseContent)(nil),                // 2: common.PerfCaseContent
	(*PerfDataContent)(nil),                // 3: common.PerfDataContent
	(*PerfRateLimits)(nil),                 // 4: common.PerfRateLimits
	(*PerfCaseStepV2)(nil),                 // 5: common.PerfCaseStepV2
	(*PerfCaseContentV2)(nil),              // 6: common.PerfCaseContentV2
	(*PerfServiceMetaData)(nil),            // 7: common.PerfServiceMetaData
	(*PerfUserInfo)(nil),                   // 8: common.PerfUserInfo
	(*PerfStopRule)(nil),                   // 9: common.PerfStopRule
	(*PerfKeepalive_AuthConfig)(nil),       // 10: common.PerfKeepalive.AuthConfig
	(*PerfKeepalive_HeartbeatConfig)(nil),  // 11: common.PerfKeepalive.HeartbeatConfig
	(*PerfCaseStep_Export)(nil),            // 12: common.PerfCaseStep.Export
	nil,                                    // 13: common.PerfCaseStep.HeadersEntry
	(*PerfDataContent_Line)(nil),           // 14: common.PerfDataContent.Line
	(*PerfRateLimits_AuthConfig)(nil),      // 15: common.PerfRateLimits.AuthConfig
	(*PerfRateLimits_HeartbeatConfig)(nil), // 16: common.PerfRateLimits.HeartbeatConfig
	(*PerfCaseStepV2_Export)(nil),          // 17: common.PerfCaseStepV2.Export
	nil,                                    // 18: common.PerfCaseStepV2.HeadersEntry
	(*RateLimit)(nil),                      // 19: common.RateLimit
	(*RateLimitV2)(nil),                    // 20: common.RateLimitV2
	(MetricType)(0),                        // 21: common.MetricType
	(*structpb.Struct)(nil),                // 22: google.protobuf.Struct
	(*structpb.ListValue)(nil),             // 23: google.protobuf.ListValue
}
var file_common_perf_proto_depIdxs = []int32{
	10, // 0: common.PerfKeepalive.auth:type_name -> common.PerfKeepalive.AuthConfig
	11, // 1: common.PerfKeepalive.heartbeat:type_name -> common.PerfKeepalive.HeartbeatConfig
	19, // 2: common.PerfCaseStep.rate_limit:type_name -> common.RateLimit
	13, // 3: common.PerfCaseStep.headers:type_name -> common.PerfCaseStep.HeadersEntry
	12, // 4: common.PerfCaseStep.exports:type_name -> common.PerfCaseStep.Export
	1,  // 5: common.PerfCaseContent.setup_steps:type_name -> common.PerfCaseStep
	1,  // 6: common.PerfCaseContent.serial_steps:type_name -> common.PerfCaseStep
	1,  // 7: common.PerfCaseContent.parallel_steps:type_name -> common.PerfCaseStep
	1,  // 8: common.PerfCaseContent.teardown_steps:type_name -> common.PerfCaseStep
	14, // 9: common.PerfDataContent.lines:type_name -> common.PerfDataContent.Line
	15, // 10: common.PerfRateLimits.auth:type_name -> common.PerfRateLimits.AuthConfig
	16, // 11: common.PerfRateLimits.heartbeat:type_name -> common.PerfRateLimits.HeartbeatConfig
	20, // 12: common.PerfCaseStepV2.rate_limits:type_name -> common.RateLimitV2
	18, // 13: common.PerfCaseStepV2.headers:type_name -> common.PerfCaseStepV2.HeadersEntry
	17, // 14: common.PerfCaseStepV2.exports:type_name -> common.PerfCaseStepV2.Export
	5,  // 15: common.PerfCaseContentV2.setup_steps:type_name -> common.PerfCaseStepV2
	5,  // 16: common.PerfCaseContentV2.serial_steps:type_name -> common.PerfCaseStepV2
	5,  // 17: common.PerfCaseContentV2.parallel_steps:type_name -> common.PerfCaseStepV2
	5,  // 18: common.PerfCaseContentV2.teardown_steps:type_name -> common.PerfCaseStepV2
	8,  // 19: common.PerfServiceMetaData.developers:type_name -> common.PerfUserInfo
	8,  // 20: common.PerfServiceMetaData.maintainers:type_name -> common.PerfUserInfo
	8,  // 21: common.PerfServiceMetaData.database_administrators:type_name -> common.PerfUserInfo
	21, // 22: common.PerfStopRule.metric_type:type_name -> common.MetricType
	19, // 23: common.PerfKeepalive.AuthConfig.rate_limit:type_name -> common.RateLimit
	19, // 24: common.PerfKeepalive.HeartbeatConfig.rate_limit:type_name -> common.RateLimit
	22, // 25: common.PerfDataContent.Line.auth_data:type_name -> google.protobuf.Struct
	23, // 26: common.PerfDataContent.Line.request_data:type_name -> google.protobuf.ListValue
	20, // 27: common.PerfRateLimits.AuthConfig.rate_limits:type_name -> common.RateLimitV2
	20, // 28: common.PerfRateLimits.HeartbeatConfig.rate_limits:type_name -> common.RateLimitV2
	29, // [29:29] is the sub-list for method output_type
	29, // [29:29] is the sub-list for method input_type
	29, // [29:29] is the sub-list for extension type_name
	29, // [29:29] is the sub-list for extension extendee
	0,  // [0:29] is the sub-list for field type_name
}

func init() { file_common_perf_proto_init() }
func file_common_perf_proto_init() {
	if File_common_perf_proto != nil {
		return
	}
	file_common_enum_proto_init()
	file_common_limit_proto_init()
	file_common_perf_proto_msgTypes[5].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_common_perf_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   19,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_common_perf_proto_goTypes,
		DependencyIndexes: file_common_perf_proto_depIdxs,
		MessageInfos:      file_common_perf_proto_msgTypes,
	}.Build()
	File_common_perf_proto = out.File
	file_common_perf_proto_rawDesc = nil
	file_common_perf_proto_goTypes = nil
	file_common_perf_proto_depIdxs = nil
}
