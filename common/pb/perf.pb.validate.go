// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: common/perf.proto

package pb

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on PerfKeepalive with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *PerfKeepalive) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PerfKeepalive with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in PerfKeepaliveMultiError, or
// nil if none found.
func (m *PerfKeepalive) ValidateAll() error {
	return m.validate(true)
}

func (m *PerfKeepalive) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetAuth()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PerfKeepaliveValidationError{
					field:  "Auth",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PerfKeepaliveValidationError{
					field:  "Auth",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAuth()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PerfKeepaliveValidationError{
				field:  "Auth",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetHeartbeat()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PerfKeepaliveValidationError{
					field:  "Heartbeat",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PerfKeepaliveValidationError{
					field:  "Heartbeat",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeartbeat()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PerfKeepaliveValidationError{
				field:  "Heartbeat",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return PerfKeepaliveMultiError(errors)
	}

	return nil
}

// PerfKeepaliveMultiError is an error wrapping multiple validation errors
// returned by PerfKeepalive.ValidateAll() if the designated constraints
// aren't met.
type PerfKeepaliveMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PerfKeepaliveMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PerfKeepaliveMultiError) AllErrors() []error { return m }

// PerfKeepaliveValidationError is the validation error returned by
// PerfKeepalive.Validate if the designated constraints aren't met.
type PerfKeepaliveValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PerfKeepaliveValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PerfKeepaliveValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PerfKeepaliveValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PerfKeepaliveValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PerfKeepaliveValidationError) ErrorName() string { return "PerfKeepaliveValidationError" }

// Error satisfies the builtin error interface
func (e PerfKeepaliveValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPerfKeepalive.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PerfKeepaliveValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PerfKeepaliveValidationError{}

// Validate checks the field values on PerfCaseStep with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *PerfCaseStep) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PerfCaseStep with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in PerfCaseStepMultiError, or
// nil if none found.
func (m *PerfCaseStep) ValidateAll() error {
	return m.validate(true)
}

func (m *PerfCaseStep) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if l := utf8.RuneCountInString(m.GetName()); l < 1 || l > 64 {
		err := PerfCaseStepValidationError{
			field:  "Name",
			reason: "value length must be between 1 and 64 runes, inclusive",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetRateLimit() == nil {
		err := PerfCaseStepValidationError{
			field:  "RateLimit",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetRateLimit()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PerfCaseStepValidationError{
					field:  "RateLimit",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PerfCaseStepValidationError{
					field:  "RateLimit",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRateLimit()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PerfCaseStepValidationError{
				field:  "RateLimit",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if m.GetUrl() != "" {

		if utf8.RuneCountInString(m.GetUrl()) > 255 {
			err := PerfCaseStepValidationError{
				field:  "Url",
				reason: "value length must be at most 255 runes",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if l := utf8.RuneCountInString(m.GetMethod()); l < 3 || l > 255 {
		err := PerfCaseStepValidationError{
			field:  "Method",
			reason: "value length must be between 3 and 255 runes, inclusive",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(m.GetHeaders()) > 0 {

		{
			sorted_keys := make([]string, len(m.GetHeaders()))
			i := 0
			for key := range m.GetHeaders() {
				sorted_keys[i] = key
				i++
			}
			sort.Slice(sorted_keys, func(i, j int) bool { return sorted_keys[i] < sorted_keys[j] })
			for _, key := range sorted_keys {
				val := m.GetHeaders()[key]
				_ = val

				if utf8.RuneCountInString(key) < 1 {
					err := PerfCaseStepValidationError{
						field:  fmt.Sprintf("Headers[%v]", key),
						reason: "value length must be at least 1 runes",
					}
					if !all {
						return err
					}
					errors = append(errors, err)
				}

				// no validation rules for Headers[key]
			}
		}

	}

	if m.GetBody() != "" {

		if utf8.RuneCountInString(m.GetBody()) > 1024 {
			err := PerfCaseStepValidationError{
				field:  "Body",
				reason: "value length must be at most 1024 runes",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if len(m.GetExports()) > 0 {

		for idx, item := range m.GetExports() {
			_, _ = idx, item

			if item == nil {
				err := PerfCaseStepValidationError{
					field:  fmt.Sprintf("Exports[%v]", idx),
					reason: "value is required",
				}
				if !all {
					return err
				}
				errors = append(errors, err)
			}

			if all {
				switch v := interface{}(item).(type) {
				case interface{ ValidateAll() error }:
					if err := v.ValidateAll(); err != nil {
						errors = append(errors, PerfCaseStepValidationError{
							field:  fmt.Sprintf("Exports[%v]", idx),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				case interface{ Validate() error }:
					if err := v.Validate(); err != nil {
						errors = append(errors, PerfCaseStepValidationError{
							field:  fmt.Sprintf("Exports[%v]", idx),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				}
			} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
				if err := v.Validate(); err != nil {
					return PerfCaseStepValidationError{
						field:  fmt.Sprintf("Exports[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					}
				}
			}

		}

	}

	if m.GetSleep() != "" {

		if utf8.RuneCountInString(m.GetSleep()) < 2 {
			err := PerfCaseStepValidationError{
				field:  "Sleep",
				reason: "value length must be at least 2 runes",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	// no validation rules for Key

	if len(errors) > 0 {
		return PerfCaseStepMultiError(errors)
	}

	return nil
}

// PerfCaseStepMultiError is an error wrapping multiple validation errors
// returned by PerfCaseStep.ValidateAll() if the designated constraints aren't met.
type PerfCaseStepMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PerfCaseStepMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PerfCaseStepMultiError) AllErrors() []error { return m }

// PerfCaseStepValidationError is the validation error returned by
// PerfCaseStep.Validate if the designated constraints aren't met.
type PerfCaseStepValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PerfCaseStepValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PerfCaseStepValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PerfCaseStepValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PerfCaseStepValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PerfCaseStepValidationError) ErrorName() string { return "PerfCaseStepValidationError" }

// Error satisfies the builtin error interface
func (e PerfCaseStepValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPerfCaseStep.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PerfCaseStepValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PerfCaseStepValidationError{}

// Validate checks the field values on PerfCaseContent with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *PerfCaseContent) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PerfCaseContent with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PerfCaseContentMultiError, or nil if none found.
func (m *PerfCaseContent) ValidateAll() error {
	return m.validate(true)
}

func (m *PerfCaseContent) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(m.GetSetupSteps()) > 0 {

		for idx, item := range m.GetSetupSteps() {
			_, _ = idx, item

			if item == nil {
				err := PerfCaseContentValidationError{
					field:  fmt.Sprintf("SetupSteps[%v]", idx),
					reason: "value is required",
				}
				if !all {
					return err
				}
				errors = append(errors, err)
			}

			if all {
				switch v := interface{}(item).(type) {
				case interface{ ValidateAll() error }:
					if err := v.ValidateAll(); err != nil {
						errors = append(errors, PerfCaseContentValidationError{
							field:  fmt.Sprintf("SetupSteps[%v]", idx),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				case interface{ Validate() error }:
					if err := v.Validate(); err != nil {
						errors = append(errors, PerfCaseContentValidationError{
							field:  fmt.Sprintf("SetupSteps[%v]", idx),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				}
			} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
				if err := v.Validate(); err != nil {
					return PerfCaseContentValidationError{
						field:  fmt.Sprintf("SetupSteps[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					}
				}
			}

		}

	}

	if len(m.GetSerialSteps()) > 0 {

		for idx, item := range m.GetSerialSteps() {
			_, _ = idx, item

			if item == nil {
				err := PerfCaseContentValidationError{
					field:  fmt.Sprintf("SerialSteps[%v]", idx),
					reason: "value is required",
				}
				if !all {
					return err
				}
				errors = append(errors, err)
			}

			if all {
				switch v := interface{}(item).(type) {
				case interface{ ValidateAll() error }:
					if err := v.ValidateAll(); err != nil {
						errors = append(errors, PerfCaseContentValidationError{
							field:  fmt.Sprintf("SerialSteps[%v]", idx),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				case interface{ Validate() error }:
					if err := v.Validate(); err != nil {
						errors = append(errors, PerfCaseContentValidationError{
							field:  fmt.Sprintf("SerialSteps[%v]", idx),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				}
			} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
				if err := v.Validate(); err != nil {
					return PerfCaseContentValidationError{
						field:  fmt.Sprintf("SerialSteps[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					}
				}
			}

		}

	}

	if len(m.GetParallelSteps()) > 0 {

		for idx, item := range m.GetParallelSteps() {
			_, _ = idx, item

			if item == nil {
				err := PerfCaseContentValidationError{
					field:  fmt.Sprintf("ParallelSteps[%v]", idx),
					reason: "value is required",
				}
				if !all {
					return err
				}
				errors = append(errors, err)
			}

			if all {
				switch v := interface{}(item).(type) {
				case interface{ ValidateAll() error }:
					if err := v.ValidateAll(); err != nil {
						errors = append(errors, PerfCaseContentValidationError{
							field:  fmt.Sprintf("ParallelSteps[%v]", idx),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				case interface{ Validate() error }:
					if err := v.Validate(); err != nil {
						errors = append(errors, PerfCaseContentValidationError{
							field:  fmt.Sprintf("ParallelSteps[%v]", idx),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				}
			} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
				if err := v.Validate(); err != nil {
					return PerfCaseContentValidationError{
						field:  fmt.Sprintf("ParallelSteps[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					}
				}
			}

		}

	}

	if len(m.GetTeardownSteps()) > 0 {

		for idx, item := range m.GetTeardownSteps() {
			_, _ = idx, item

			if item == nil {
				err := PerfCaseContentValidationError{
					field:  fmt.Sprintf("TeardownSteps[%v]", idx),
					reason: "value is required",
				}
				if !all {
					return err
				}
				errors = append(errors, err)
			}

			if all {
				switch v := interface{}(item).(type) {
				case interface{ ValidateAll() error }:
					if err := v.ValidateAll(); err != nil {
						errors = append(errors, PerfCaseContentValidationError{
							field:  fmt.Sprintf("TeardownSteps[%v]", idx),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				case interface{ Validate() error }:
					if err := v.Validate(); err != nil {
						errors = append(errors, PerfCaseContentValidationError{
							field:  fmt.Sprintf("TeardownSteps[%v]", idx),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				}
			} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
				if err := v.Validate(); err != nil {
					return PerfCaseContentValidationError{
						field:  fmt.Sprintf("TeardownSteps[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					}
				}
			}

		}

	}

	if len(errors) > 0 {
		return PerfCaseContentMultiError(errors)
	}

	return nil
}

// PerfCaseContentMultiError is an error wrapping multiple validation errors
// returned by PerfCaseContent.ValidateAll() if the designated constraints
// aren't met.
type PerfCaseContentMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PerfCaseContentMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PerfCaseContentMultiError) AllErrors() []error { return m }

// PerfCaseContentValidationError is the validation error returned by
// PerfCaseContent.Validate if the designated constraints aren't met.
type PerfCaseContentValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PerfCaseContentValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PerfCaseContentValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PerfCaseContentValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PerfCaseContentValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PerfCaseContentValidationError) ErrorName() string { return "PerfCaseContentValidationError" }

// Error satisfies the builtin error interface
func (e PerfCaseContentValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPerfCaseContent.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PerfCaseContentValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PerfCaseContentValidationError{}

// Validate checks the field values on PerfDataContent with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *PerfDataContent) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PerfDataContent with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PerfDataContentMultiError, or nil if none found.
func (m *PerfDataContent) ValidateAll() error {
	return m.validate(true)
}

func (m *PerfDataContent) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(m.GetLines()) < 1 {
		err := PerfDataContentValidationError{
			field:  "Lines",
			reason: "value must contain at least 1 item(s)",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	for idx, item := range m.GetLines() {
		_, _ = idx, item

		if item == nil {
			err := PerfDataContentValidationError{
				field:  fmt.Sprintf("Lines[%v]", idx),
				reason: "value is required",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, PerfDataContentValidationError{
						field:  fmt.Sprintf("Lines[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, PerfDataContentValidationError{
						field:  fmt.Sprintf("Lines[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return PerfDataContentValidationError{
					field:  fmt.Sprintf("Lines[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return PerfDataContentMultiError(errors)
	}

	return nil
}

// PerfDataContentMultiError is an error wrapping multiple validation errors
// returned by PerfDataContent.ValidateAll() if the designated constraints
// aren't met.
type PerfDataContentMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PerfDataContentMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PerfDataContentMultiError) AllErrors() []error { return m }

// PerfDataContentValidationError is the validation error returned by
// PerfDataContent.Validate if the designated constraints aren't met.
type PerfDataContentValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PerfDataContentValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PerfDataContentValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PerfDataContentValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PerfDataContentValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PerfDataContentValidationError) ErrorName() string { return "PerfDataContentValidationError" }

// Error satisfies the builtin error interface
func (e PerfDataContentValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPerfDataContent.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PerfDataContentValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PerfDataContentValidationError{}

// Validate checks the field values on PerfRateLimits with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *PerfRateLimits) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PerfRateLimits with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in PerfRateLimitsMultiError,
// or nil if none found.
func (m *PerfRateLimits) ValidateAll() error {
	return m.validate(true)
}

func (m *PerfRateLimits) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetAuth()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PerfRateLimitsValidationError{
					field:  "Auth",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PerfRateLimitsValidationError{
					field:  "Auth",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAuth()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PerfRateLimitsValidationError{
				field:  "Auth",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetHeartbeat()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PerfRateLimitsValidationError{
					field:  "Heartbeat",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PerfRateLimitsValidationError{
					field:  "Heartbeat",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeartbeat()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PerfRateLimitsValidationError{
				field:  "Heartbeat",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return PerfRateLimitsMultiError(errors)
	}

	return nil
}

// PerfRateLimitsMultiError is an error wrapping multiple validation errors
// returned by PerfRateLimits.ValidateAll() if the designated constraints
// aren't met.
type PerfRateLimitsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PerfRateLimitsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PerfRateLimitsMultiError) AllErrors() []error { return m }

// PerfRateLimitsValidationError is the validation error returned by
// PerfRateLimits.Validate if the designated constraints aren't met.
type PerfRateLimitsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PerfRateLimitsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PerfRateLimitsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PerfRateLimitsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PerfRateLimitsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PerfRateLimitsValidationError) ErrorName() string { return "PerfRateLimitsValidationError" }

// Error satisfies the builtin error interface
func (e PerfRateLimitsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPerfRateLimits.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PerfRateLimitsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PerfRateLimitsValidationError{}

// Validate checks the field values on PerfCaseStepV2 with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *PerfCaseStepV2) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PerfCaseStepV2 with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in PerfCaseStepV2MultiError,
// or nil if none found.
func (m *PerfCaseStepV2) ValidateAll() error {
	return m.validate(true)
}

func (m *PerfCaseStepV2) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if l := utf8.RuneCountInString(m.GetName()); l < 1 || l > 64 {
		err := PerfCaseStepV2ValidationError{
			field:  "Name",
			reason: "value length must be between 1 and 64 runes, inclusive",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(m.GetRateLimits()) > 0 {

		for idx, item := range m.GetRateLimits() {
			_, _ = idx, item

			if item == nil {
				err := PerfCaseStepV2ValidationError{
					field:  fmt.Sprintf("RateLimits[%v]", idx),
					reason: "value is required",
				}
				if !all {
					return err
				}
				errors = append(errors, err)
			}

			if all {
				switch v := interface{}(item).(type) {
				case interface{ ValidateAll() error }:
					if err := v.ValidateAll(); err != nil {
						errors = append(errors, PerfCaseStepV2ValidationError{
							field:  fmt.Sprintf("RateLimits[%v]", idx),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				case interface{ Validate() error }:
					if err := v.Validate(); err != nil {
						errors = append(errors, PerfCaseStepV2ValidationError{
							field:  fmt.Sprintf("RateLimits[%v]", idx),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				}
			} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
				if err := v.Validate(); err != nil {
					return PerfCaseStepV2ValidationError{
						field:  fmt.Sprintf("RateLimits[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					}
				}
			}

		}

	}

	if m.GetUrl() != "" {

		if utf8.RuneCountInString(m.GetUrl()) > 255 {
			err := PerfCaseStepV2ValidationError{
				field:  "Url",
				reason: "value length must be at most 255 runes",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if l := utf8.RuneCountInString(m.GetMethod()); l < 3 || l > 255 {
		err := PerfCaseStepV2ValidationError{
			field:  "Method",
			reason: "value length must be between 3 and 255 runes, inclusive",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(m.GetHeaders()) > 0 {

		{
			sorted_keys := make([]string, len(m.GetHeaders()))
			i := 0
			for key := range m.GetHeaders() {
				sorted_keys[i] = key
				i++
			}
			sort.Slice(sorted_keys, func(i, j int) bool { return sorted_keys[i] < sorted_keys[j] })
			for _, key := range sorted_keys {
				val := m.GetHeaders()[key]
				_ = val

				if utf8.RuneCountInString(key) < 1 {
					err := PerfCaseStepV2ValidationError{
						field:  fmt.Sprintf("Headers[%v]", key),
						reason: "value length must be at least 1 runes",
					}
					if !all {
						return err
					}
					errors = append(errors, err)
				}

				// no validation rules for Headers[key]
			}
		}

	}

	if m.GetBody() != "" {

		if utf8.RuneCountInString(m.GetBody()) > 1024 {
			err := PerfCaseStepV2ValidationError{
				field:  "Body",
				reason: "value length must be at most 1024 runes",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if len(m.GetExports()) > 0 {

		for idx, item := range m.GetExports() {
			_, _ = idx, item

			if item == nil {
				err := PerfCaseStepV2ValidationError{
					field:  fmt.Sprintf("Exports[%v]", idx),
					reason: "value is required",
				}
				if !all {
					return err
				}
				errors = append(errors, err)
			}

			if all {
				switch v := interface{}(item).(type) {
				case interface{ ValidateAll() error }:
					if err := v.ValidateAll(); err != nil {
						errors = append(errors, PerfCaseStepV2ValidationError{
							field:  fmt.Sprintf("Exports[%v]", idx),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				case interface{ Validate() error }:
					if err := v.Validate(); err != nil {
						errors = append(errors, PerfCaseStepV2ValidationError{
							field:  fmt.Sprintf("Exports[%v]", idx),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				}
			} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
				if err := v.Validate(); err != nil {
					return PerfCaseStepV2ValidationError{
						field:  fmt.Sprintf("Exports[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					}
				}
			}

		}

	}

	if m.GetSleep() != "" {

		if utf8.RuneCountInString(m.GetSleep()) < 2 {
			err := PerfCaseStepV2ValidationError{
				field:  "Sleep",
				reason: "value length must be at least 2 runes",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	// no validation rules for Key

	// no validation rules for Service

	// no validation rules for Namespace

	// no validation rules for Cmd

	// no validation rules for GrpcPath

	// no validation rules for Deprecated

	// no validation rules for QueryPath

	if m.ReferenceQps != nil {
		// no validation rules for ReferenceQps
	}

	if len(errors) > 0 {
		return PerfCaseStepV2MultiError(errors)
	}

	return nil
}

// PerfCaseStepV2MultiError is an error wrapping multiple validation errors
// returned by PerfCaseStepV2.ValidateAll() if the designated constraints
// aren't met.
type PerfCaseStepV2MultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PerfCaseStepV2MultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PerfCaseStepV2MultiError) AllErrors() []error { return m }

// PerfCaseStepV2ValidationError is the validation error returned by
// PerfCaseStepV2.Validate if the designated constraints aren't met.
type PerfCaseStepV2ValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PerfCaseStepV2ValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PerfCaseStepV2ValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PerfCaseStepV2ValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PerfCaseStepV2ValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PerfCaseStepV2ValidationError) ErrorName() string { return "PerfCaseStepV2ValidationError" }

// Error satisfies the builtin error interface
func (e PerfCaseStepV2ValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPerfCaseStepV2.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PerfCaseStepV2ValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PerfCaseStepV2ValidationError{}

// Validate checks the field values on PerfCaseContentV2 with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *PerfCaseContentV2) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PerfCaseContentV2 with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PerfCaseContentV2MultiError, or nil if none found.
func (m *PerfCaseContentV2) ValidateAll() error {
	return m.validate(true)
}

func (m *PerfCaseContentV2) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(m.GetSetupSteps()) > 0 {

		for idx, item := range m.GetSetupSteps() {
			_, _ = idx, item

			if item == nil {
				err := PerfCaseContentV2ValidationError{
					field:  fmt.Sprintf("SetupSteps[%v]", idx),
					reason: "value is required",
				}
				if !all {
					return err
				}
				errors = append(errors, err)
			}

			if all {
				switch v := interface{}(item).(type) {
				case interface{ ValidateAll() error }:
					if err := v.ValidateAll(); err != nil {
						errors = append(errors, PerfCaseContentV2ValidationError{
							field:  fmt.Sprintf("SetupSteps[%v]", idx),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				case interface{ Validate() error }:
					if err := v.Validate(); err != nil {
						errors = append(errors, PerfCaseContentV2ValidationError{
							field:  fmt.Sprintf("SetupSteps[%v]", idx),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				}
			} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
				if err := v.Validate(); err != nil {
					return PerfCaseContentV2ValidationError{
						field:  fmt.Sprintf("SetupSteps[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					}
				}
			}

		}

	}

	if len(m.GetSerialSteps()) > 0 {

		for idx, item := range m.GetSerialSteps() {
			_, _ = idx, item

			if item == nil {
				err := PerfCaseContentV2ValidationError{
					field:  fmt.Sprintf("SerialSteps[%v]", idx),
					reason: "value is required",
				}
				if !all {
					return err
				}
				errors = append(errors, err)
			}

			if all {
				switch v := interface{}(item).(type) {
				case interface{ ValidateAll() error }:
					if err := v.ValidateAll(); err != nil {
						errors = append(errors, PerfCaseContentV2ValidationError{
							field:  fmt.Sprintf("SerialSteps[%v]", idx),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				case interface{ Validate() error }:
					if err := v.Validate(); err != nil {
						errors = append(errors, PerfCaseContentV2ValidationError{
							field:  fmt.Sprintf("SerialSteps[%v]", idx),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				}
			} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
				if err := v.Validate(); err != nil {
					return PerfCaseContentV2ValidationError{
						field:  fmt.Sprintf("SerialSteps[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					}
				}
			}

		}

	}

	if len(m.GetParallelSteps()) > 0 {

		for idx, item := range m.GetParallelSteps() {
			_, _ = idx, item

			if item == nil {
				err := PerfCaseContentV2ValidationError{
					field:  fmt.Sprintf("ParallelSteps[%v]", idx),
					reason: "value is required",
				}
				if !all {
					return err
				}
				errors = append(errors, err)
			}

			if all {
				switch v := interface{}(item).(type) {
				case interface{ ValidateAll() error }:
					if err := v.ValidateAll(); err != nil {
						errors = append(errors, PerfCaseContentV2ValidationError{
							field:  fmt.Sprintf("ParallelSteps[%v]", idx),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				case interface{ Validate() error }:
					if err := v.Validate(); err != nil {
						errors = append(errors, PerfCaseContentV2ValidationError{
							field:  fmt.Sprintf("ParallelSteps[%v]", idx),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				}
			} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
				if err := v.Validate(); err != nil {
					return PerfCaseContentV2ValidationError{
						field:  fmt.Sprintf("ParallelSteps[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					}
				}
			}

		}

	}

	if len(m.GetTeardownSteps()) > 0 {

		for idx, item := range m.GetTeardownSteps() {
			_, _ = idx, item

			if item == nil {
				err := PerfCaseContentV2ValidationError{
					field:  fmt.Sprintf("TeardownSteps[%v]", idx),
					reason: "value is required",
				}
				if !all {
					return err
				}
				errors = append(errors, err)
			}

			if all {
				switch v := interface{}(item).(type) {
				case interface{ ValidateAll() error }:
					if err := v.ValidateAll(); err != nil {
						errors = append(errors, PerfCaseContentV2ValidationError{
							field:  fmt.Sprintf("TeardownSteps[%v]", idx),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				case interface{ Validate() error }:
					if err := v.Validate(); err != nil {
						errors = append(errors, PerfCaseContentV2ValidationError{
							field:  fmt.Sprintf("TeardownSteps[%v]", idx),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				}
			} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
				if err := v.Validate(); err != nil {
					return PerfCaseContentV2ValidationError{
						field:  fmt.Sprintf("TeardownSteps[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					}
				}
			}

		}

	}

	if len(errors) > 0 {
		return PerfCaseContentV2MultiError(errors)
	}

	return nil
}

// PerfCaseContentV2MultiError is an error wrapping multiple validation errors
// returned by PerfCaseContentV2.ValidateAll() if the designated constraints
// aren't met.
type PerfCaseContentV2MultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PerfCaseContentV2MultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PerfCaseContentV2MultiError) AllErrors() []error { return m }

// PerfCaseContentV2ValidationError is the validation error returned by
// PerfCaseContentV2.Validate if the designated constraints aren't met.
type PerfCaseContentV2ValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PerfCaseContentV2ValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PerfCaseContentV2ValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PerfCaseContentV2ValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PerfCaseContentV2ValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PerfCaseContentV2ValidationError) ErrorName() string {
	return "PerfCaseContentV2ValidationError"
}

// Error satisfies the builtin error interface
func (e PerfCaseContentV2ValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPerfCaseContentV2.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PerfCaseContentV2ValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PerfCaseContentV2ValidationError{}

// Validate checks the field values on PerfServiceMetaData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *PerfServiceMetaData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PerfServiceMetaData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PerfServiceMetaDataMultiError, or nil if none found.
func (m *PerfServiceMetaData) ValidateAll() error {
	return m.validate(true)
}

func (m *PerfServiceMetaData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Name

	// no validation rules for Namespace

	for idx, item := range m.GetDevelopers() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, PerfServiceMetaDataValidationError{
						field:  fmt.Sprintf("Developers[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, PerfServiceMetaDataValidationError{
						field:  fmt.Sprintf("Developers[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return PerfServiceMetaDataValidationError{
					field:  fmt.Sprintf("Developers[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetMaintainers() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, PerfServiceMetaDataValidationError{
						field:  fmt.Sprintf("Maintainers[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, PerfServiceMetaDataValidationError{
						field:  fmt.Sprintf("Maintainers[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return PerfServiceMetaDataValidationError{
					field:  fmt.Sprintf("Maintainers[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetDatabaseAdministrators() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, PerfServiceMetaDataValidationError{
						field:  fmt.Sprintf("DatabaseAdministrators[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, PerfServiceMetaDataValidationError{
						field:  fmt.Sprintf("DatabaseAdministrators[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return PerfServiceMetaDataValidationError{
					field:  fmt.Sprintf("DatabaseAdministrators[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return PerfServiceMetaDataMultiError(errors)
	}

	return nil
}

// PerfServiceMetaDataMultiError is an error wrapping multiple validation
// errors returned by PerfServiceMetaData.ValidateAll() if the designated
// constraints aren't met.
type PerfServiceMetaDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PerfServiceMetaDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PerfServiceMetaDataMultiError) AllErrors() []error { return m }

// PerfServiceMetaDataValidationError is the validation error returned by
// PerfServiceMetaData.Validate if the designated constraints aren't met.
type PerfServiceMetaDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PerfServiceMetaDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PerfServiceMetaDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PerfServiceMetaDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PerfServiceMetaDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PerfServiceMetaDataValidationError) ErrorName() string {
	return "PerfServiceMetaDataValidationError"
}

// Error satisfies the builtin error interface
func (e PerfServiceMetaDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPerfServiceMetaData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PerfServiceMetaDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PerfServiceMetaDataValidationError{}

// Validate checks the field values on PerfUserInfo with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *PerfUserInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PerfUserInfo with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in PerfUserInfoMultiError, or
// nil if none found.
func (m *PerfUserInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *PerfUserInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Account

	// no validation rules for Fullname

	// no validation rules for Email

	// no validation rules for LarkUserId

	if len(errors) > 0 {
		return PerfUserInfoMultiError(errors)
	}

	return nil
}

// PerfUserInfoMultiError is an error wrapping multiple validation errors
// returned by PerfUserInfo.ValidateAll() if the designated constraints aren't met.
type PerfUserInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PerfUserInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PerfUserInfoMultiError) AllErrors() []error { return m }

// PerfUserInfoValidationError is the validation error returned by
// PerfUserInfo.Validate if the designated constraints aren't met.
type PerfUserInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PerfUserInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PerfUserInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PerfUserInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PerfUserInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PerfUserInfoValidationError) ErrorName() string { return "PerfUserInfoValidationError" }

// Error satisfies the builtin error interface
func (e PerfUserInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPerfUserInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PerfUserInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PerfUserInfoValidationError{}

// Validate checks the field values on PerfStopRule with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *PerfStopRule) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PerfStopRule with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in PerfStopRuleMultiError, or
// nil if none found.
func (m *PerfStopRule) ValidateAll() error {
	return m.validate(true)
}

func (m *PerfStopRule) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for MetricType

	// no validation rules for Threshold

	// no validation rules for Duration

	if len(errors) > 0 {
		return PerfStopRuleMultiError(errors)
	}

	return nil
}

// PerfStopRuleMultiError is an error wrapping multiple validation errors
// returned by PerfStopRule.ValidateAll() if the designated constraints aren't met.
type PerfStopRuleMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PerfStopRuleMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PerfStopRuleMultiError) AllErrors() []error { return m }

// PerfStopRuleValidationError is the validation error returned by
// PerfStopRule.Validate if the designated constraints aren't met.
type PerfStopRuleValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PerfStopRuleValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PerfStopRuleValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PerfStopRuleValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PerfStopRuleValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PerfStopRuleValidationError) ErrorName() string { return "PerfStopRuleValidationError" }

// Error satisfies the builtin error interface
func (e PerfStopRuleValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPerfStopRule.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PerfStopRuleValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PerfStopRuleValidationError{}

// Validate checks the field values on PerfKeepalive_AuthConfig with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *PerfKeepalive_AuthConfig) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PerfKeepalive_AuthConfig with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PerfKeepalive_AuthConfigMultiError, or nil if none found.
func (m *PerfKeepalive_AuthConfig) ValidateAll() error {
	return m.validate(true)
}

func (m *PerfKeepalive_AuthConfig) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetRateLimit() == nil {
		err := PerfKeepalive_AuthConfigValidationError{
			field:  "RateLimit",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetRateLimit()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PerfKeepalive_AuthConfigValidationError{
					field:  "RateLimit",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PerfKeepalive_AuthConfigValidationError{
					field:  "RateLimit",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRateLimit()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PerfKeepalive_AuthConfigValidationError{
				field:  "RateLimit",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Key

	if len(errors) > 0 {
		return PerfKeepalive_AuthConfigMultiError(errors)
	}

	return nil
}

// PerfKeepalive_AuthConfigMultiError is an error wrapping multiple validation
// errors returned by PerfKeepalive_AuthConfig.ValidateAll() if the designated
// constraints aren't met.
type PerfKeepalive_AuthConfigMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PerfKeepalive_AuthConfigMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PerfKeepalive_AuthConfigMultiError) AllErrors() []error { return m }

// PerfKeepalive_AuthConfigValidationError is the validation error returned by
// PerfKeepalive_AuthConfig.Validate if the designated constraints aren't met.
type PerfKeepalive_AuthConfigValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PerfKeepalive_AuthConfigValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PerfKeepalive_AuthConfigValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PerfKeepalive_AuthConfigValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PerfKeepalive_AuthConfigValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PerfKeepalive_AuthConfigValidationError) ErrorName() string {
	return "PerfKeepalive_AuthConfigValidationError"
}

// Error satisfies the builtin error interface
func (e PerfKeepalive_AuthConfigValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPerfKeepalive_AuthConfig.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PerfKeepalive_AuthConfigValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PerfKeepalive_AuthConfigValidationError{}

// Validate checks the field values on PerfKeepalive_HeartbeatConfig with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *PerfKeepalive_HeartbeatConfig) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PerfKeepalive_HeartbeatConfig with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// PerfKeepalive_HeartbeatConfigMultiError, or nil if none found.
func (m *PerfKeepalive_HeartbeatConfig) ValidateAll() error {
	return m.validate(true)
}

func (m *PerfKeepalive_HeartbeatConfig) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetRateLimit() == nil {
		err := PerfKeepalive_HeartbeatConfigValidationError{
			field:  "RateLimit",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetRateLimit()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PerfKeepalive_HeartbeatConfigValidationError{
					field:  "RateLimit",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PerfKeepalive_HeartbeatConfigValidationError{
					field:  "RateLimit",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRateLimit()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PerfKeepalive_HeartbeatConfigValidationError{
				field:  "RateLimit",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if m.GetInterval() <= 0 {
		err := PerfKeepalive_HeartbeatConfigValidationError{
			field:  "Interval",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for Key

	if len(errors) > 0 {
		return PerfKeepalive_HeartbeatConfigMultiError(errors)
	}

	return nil
}

// PerfKeepalive_HeartbeatConfigMultiError is an error wrapping multiple
// validation errors returned by PerfKeepalive_HeartbeatConfig.ValidateAll()
// if the designated constraints aren't met.
type PerfKeepalive_HeartbeatConfigMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PerfKeepalive_HeartbeatConfigMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PerfKeepalive_HeartbeatConfigMultiError) AllErrors() []error { return m }

// PerfKeepalive_HeartbeatConfigValidationError is the validation error
// returned by PerfKeepalive_HeartbeatConfig.Validate if the designated
// constraints aren't met.
type PerfKeepalive_HeartbeatConfigValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PerfKeepalive_HeartbeatConfigValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PerfKeepalive_HeartbeatConfigValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PerfKeepalive_HeartbeatConfigValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PerfKeepalive_HeartbeatConfigValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PerfKeepalive_HeartbeatConfigValidationError) ErrorName() string {
	return "PerfKeepalive_HeartbeatConfigValidationError"
}

// Error satisfies the builtin error interface
func (e PerfKeepalive_HeartbeatConfigValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPerfKeepalive_HeartbeatConfig.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PerfKeepalive_HeartbeatConfigValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PerfKeepalive_HeartbeatConfigValidationError{}

// Validate checks the field values on PerfCaseStep_Export with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *PerfCaseStep_Export) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PerfCaseStep_Export with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PerfCaseStep_ExportMultiError, or nil if none found.
func (m *PerfCaseStep_Export) ValidateAll() error {
	return m.validate(true)
}

func (m *PerfCaseStep_Export) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetName()) < 1 {
		err := PerfCaseStep_ExportValidationError{
			field:  "Name",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetExpression()) < 1 {
		err := PerfCaseStep_ExportValidationError{
			field:  "Expression",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return PerfCaseStep_ExportMultiError(errors)
	}

	return nil
}

// PerfCaseStep_ExportMultiError is an error wrapping multiple validation
// errors returned by PerfCaseStep_Export.ValidateAll() if the designated
// constraints aren't met.
type PerfCaseStep_ExportMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PerfCaseStep_ExportMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PerfCaseStep_ExportMultiError) AllErrors() []error { return m }

// PerfCaseStep_ExportValidationError is the validation error returned by
// PerfCaseStep_Export.Validate if the designated constraints aren't met.
type PerfCaseStep_ExportValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PerfCaseStep_ExportValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PerfCaseStep_ExportValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PerfCaseStep_ExportValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PerfCaseStep_ExportValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PerfCaseStep_ExportValidationError) ErrorName() string {
	return "PerfCaseStep_ExportValidationError"
}

// Error satisfies the builtin error interface
func (e PerfCaseStep_ExportValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPerfCaseStep_Export.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PerfCaseStep_ExportValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PerfCaseStep_ExportValidationError{}

// Validate checks the field values on PerfDataContent_Line with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *PerfDataContent_Line) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PerfDataContent_Line with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PerfDataContent_LineMultiError, or nil if none found.
func (m *PerfDataContent_Line) ValidateAll() error {
	return m.validate(true)
}

func (m *PerfDataContent_Line) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetAuthData() == nil {
		err := PerfDataContent_LineValidationError{
			field:  "AuthData",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetAuthData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PerfDataContent_LineValidationError{
					field:  "AuthData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PerfDataContent_LineValidationError{
					field:  "AuthData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAuthData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PerfDataContent_LineValidationError{
				field:  "AuthData",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetRequestData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PerfDataContent_LineValidationError{
					field:  "RequestData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PerfDataContent_LineValidationError{
					field:  "RequestData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PerfDataContent_LineValidationError{
				field:  "RequestData",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return PerfDataContent_LineMultiError(errors)
	}

	return nil
}

// PerfDataContent_LineMultiError is an error wrapping multiple validation
// errors returned by PerfDataContent_Line.ValidateAll() if the designated
// constraints aren't met.
type PerfDataContent_LineMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PerfDataContent_LineMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PerfDataContent_LineMultiError) AllErrors() []error { return m }

// PerfDataContent_LineValidationError is the validation error returned by
// PerfDataContent_Line.Validate if the designated constraints aren't met.
type PerfDataContent_LineValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PerfDataContent_LineValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PerfDataContent_LineValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PerfDataContent_LineValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PerfDataContent_LineValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PerfDataContent_LineValidationError) ErrorName() string {
	return "PerfDataContent_LineValidationError"
}

// Error satisfies the builtin error interface
func (e PerfDataContent_LineValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPerfDataContent_Line.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PerfDataContent_LineValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PerfDataContent_LineValidationError{}

// Validate checks the field values on PerfRateLimits_AuthConfig with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *PerfRateLimits_AuthConfig) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PerfRateLimits_AuthConfig with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PerfRateLimits_AuthConfigMultiError, or nil if none found.
func (m *PerfRateLimits_AuthConfig) ValidateAll() error {
	return m.validate(true)
}

func (m *PerfRateLimits_AuthConfig) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetRateLimits() {
		_, _ = idx, item

		if item == nil {
			err := PerfRateLimits_AuthConfigValidationError{
				field:  fmt.Sprintf("RateLimits[%v]", idx),
				reason: "value is required",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, PerfRateLimits_AuthConfigValidationError{
						field:  fmt.Sprintf("RateLimits[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, PerfRateLimits_AuthConfigValidationError{
						field:  fmt.Sprintf("RateLimits[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return PerfRateLimits_AuthConfigValidationError{
					field:  fmt.Sprintf("RateLimits[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for Key

	if len(errors) > 0 {
		return PerfRateLimits_AuthConfigMultiError(errors)
	}

	return nil
}

// PerfRateLimits_AuthConfigMultiError is an error wrapping multiple validation
// errors returned by PerfRateLimits_AuthConfig.ValidateAll() if the
// designated constraints aren't met.
type PerfRateLimits_AuthConfigMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PerfRateLimits_AuthConfigMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PerfRateLimits_AuthConfigMultiError) AllErrors() []error { return m }

// PerfRateLimits_AuthConfigValidationError is the validation error returned by
// PerfRateLimits_AuthConfig.Validate if the designated constraints aren't met.
type PerfRateLimits_AuthConfigValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PerfRateLimits_AuthConfigValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PerfRateLimits_AuthConfigValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PerfRateLimits_AuthConfigValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PerfRateLimits_AuthConfigValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PerfRateLimits_AuthConfigValidationError) ErrorName() string {
	return "PerfRateLimits_AuthConfigValidationError"
}

// Error satisfies the builtin error interface
func (e PerfRateLimits_AuthConfigValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPerfRateLimits_AuthConfig.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PerfRateLimits_AuthConfigValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PerfRateLimits_AuthConfigValidationError{}

// Validate checks the field values on PerfRateLimits_HeartbeatConfig with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *PerfRateLimits_HeartbeatConfig) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PerfRateLimits_HeartbeatConfig with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// PerfRateLimits_HeartbeatConfigMultiError, or nil if none found.
func (m *PerfRateLimits_HeartbeatConfig) ValidateAll() error {
	return m.validate(true)
}

func (m *PerfRateLimits_HeartbeatConfig) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetRateLimits() {
		_, _ = idx, item

		if item == nil {
			err := PerfRateLimits_HeartbeatConfigValidationError{
				field:  fmt.Sprintf("RateLimits[%v]", idx),
				reason: "value is required",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, PerfRateLimits_HeartbeatConfigValidationError{
						field:  fmt.Sprintf("RateLimits[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, PerfRateLimits_HeartbeatConfigValidationError{
						field:  fmt.Sprintf("RateLimits[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return PerfRateLimits_HeartbeatConfigValidationError{
					field:  fmt.Sprintf("RateLimits[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if m.GetInterval() <= 0 {
		err := PerfRateLimits_HeartbeatConfigValidationError{
			field:  "Interval",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for Key

	if len(errors) > 0 {
		return PerfRateLimits_HeartbeatConfigMultiError(errors)
	}

	return nil
}

// PerfRateLimits_HeartbeatConfigMultiError is an error wrapping multiple
// validation errors returned by PerfRateLimits_HeartbeatConfig.ValidateAll()
// if the designated constraints aren't met.
type PerfRateLimits_HeartbeatConfigMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PerfRateLimits_HeartbeatConfigMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PerfRateLimits_HeartbeatConfigMultiError) AllErrors() []error { return m }

// PerfRateLimits_HeartbeatConfigValidationError is the validation error
// returned by PerfRateLimits_HeartbeatConfig.Validate if the designated
// constraints aren't met.
type PerfRateLimits_HeartbeatConfigValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PerfRateLimits_HeartbeatConfigValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PerfRateLimits_HeartbeatConfigValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PerfRateLimits_HeartbeatConfigValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PerfRateLimits_HeartbeatConfigValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PerfRateLimits_HeartbeatConfigValidationError) ErrorName() string {
	return "PerfRateLimits_HeartbeatConfigValidationError"
}

// Error satisfies the builtin error interface
func (e PerfRateLimits_HeartbeatConfigValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPerfRateLimits_HeartbeatConfig.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PerfRateLimits_HeartbeatConfigValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PerfRateLimits_HeartbeatConfigValidationError{}

// Validate checks the field values on PerfCaseStepV2_Export with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *PerfCaseStepV2_Export) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PerfCaseStepV2_Export with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PerfCaseStepV2_ExportMultiError, or nil if none found.
func (m *PerfCaseStepV2_Export) ValidateAll() error {
	return m.validate(true)
}

func (m *PerfCaseStepV2_Export) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetName()) < 1 {
		err := PerfCaseStepV2_ExportValidationError{
			field:  "Name",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetExpression()) < 1 {
		err := PerfCaseStepV2_ExportValidationError{
			field:  "Expression",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return PerfCaseStepV2_ExportMultiError(errors)
	}

	return nil
}

// PerfCaseStepV2_ExportMultiError is an error wrapping multiple validation
// errors returned by PerfCaseStepV2_Export.ValidateAll() if the designated
// constraints aren't met.
type PerfCaseStepV2_ExportMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PerfCaseStepV2_ExportMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PerfCaseStepV2_ExportMultiError) AllErrors() []error { return m }

// PerfCaseStepV2_ExportValidationError is the validation error returned by
// PerfCaseStepV2_Export.Validate if the designated constraints aren't met.
type PerfCaseStepV2_ExportValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PerfCaseStepV2_ExportValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PerfCaseStepV2_ExportValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PerfCaseStepV2_ExportValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PerfCaseStepV2_ExportValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PerfCaseStepV2_ExportValidationError) ErrorName() string {
	return "PerfCaseStepV2_ExportValidationError"
}

// Error satisfies the builtin error interface
func (e PerfCaseStepV2_ExportValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPerfCaseStepV2_Export.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PerfCaseStepV2_ExportValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PerfCaseStepV2_ExportValidationError{}
