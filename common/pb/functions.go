package pb

import "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

func StringToTriggerMode() utils.TypeConverter {
	return utils.StringToPBEnum(TriggerMode_NULL)
}

func TriggerModeToString() utils.TypeConverter {
	return utils.PBEnumToString(TriggerMode_NULL)
}

func StringToPurposeType() utils.TypeConverter {
	return utils.StringToPBEnum(PurposeType_UNDEFINED)
}

func PurposeTypeToString() utils.TypeConverter {
	return utils.PBEnumToString(PurposeType_UNDEFINED)
}

func StringToDeviceType() utils.TypeConverter {
	return utils.StringToPBEnum(DeviceType_DT_NULL)
}

func DeviceTypeToString() utils.TypeConverter {
	return utils.PBEnumToString(DeviceType_DT_NULL)
}

func StringToPlatformType() utils.TypeConverter {
	return utils.StringToPBEnum(PlatformType_PT_NULL)
}

func PlatformTypeToString() utils.TypeConverter {
	return utils.PBEnumToString(PlatformType_PT_NULL)
}

func StringToFailRetry() utils.TypeConverter {
	return utils.StringToPBEnum(FailRetry_ZERO)
}

func FailRetryToString() utils.TypeConverter {
	return utils.PBEnumToString(FailRetry_ZERO)
}

func StringToTestLanguage() utils.TypeConverter {
	return utils.StringToPBEnum(TestLanguage_TestLanguage_NULL)
}

func TestLanguageToString() utils.TypeConverter {
	return utils.PBEnumToString(TestLanguage_TestLanguage_NULL)
}

func StringToTestFramework() utils.TypeConverter {
	return utils.StringToPBEnum(TestFramework_TestFramework_NULL)
}

func TestFrameworkToString() utils.TypeConverter {
	return utils.PBEnumToString(TestFramework_TestFramework_NULL)
}

func StringToProtocol() utils.TypeConverter {
	return utils.StringToPBEnum(Protocol_PROTOCOL_NULL)
}

func ProtocolToString() utils.TypeConverter {
	return utils.PBEnumToString(Protocol_PROTOCOL_NULL)
}

func StringToTargetEnvironment() utils.TypeConverter {
	return utils.StringToPBEnum(TargetEnvironment_TE_NULL)
}

func TargetEnvironmentToString() utils.TypeConverter {
	return utils.PBEnumToString(TargetEnvironment_TE_NULL)
}

func StringToPerfTaskType() utils.TypeConverter {
	return utils.StringToPBEnum(PerfTaskType_PTT_NULL)
}

func PerfTaskTypeToString() utils.TypeConverter {
	return utils.PBEnumToString(PerfTaskType_PTT_NULL)
}

func StringToPerfTaskExecutionMode() utils.TypeConverter {
	return utils.StringToPBEnum(PerfTaskExecutionMode_PTEM_NULL)
}

func PerfTaskExecutionModeToString() utils.TypeConverter {
	return utils.PBEnumToString(PerfTaskExecutionMode_PTEM_NULL)
}

func StringToPerfCaseStepType() utils.TypeConverter {
	return utils.StringToPBEnum(PerfCaseStepType_PerfCaseStepType_NULL)
}

func PerfCaseStepTypeToString() utils.TypeConverter {
	return utils.PBEnumToString(PerfCaseStepType_PerfCaseStepType_NULL)
}

func StringToMonitorUrlType() utils.TypeConverter {
	return utils.StringToPBEnum(MonitorUrlType_MUT_NULL)
}

func MonitorUrlTypeToString() utils.TypeConverter {
	return utils.PBEnumToString(MonitorUrlType_MUT_NULL)
}

func StringToMetricType() utils.TypeConverter {
	return utils.StringToPBEnum(MetricType_MetricType_NULL)
}

func MetricTypeToString() utils.TypeConverter {
	return utils.PBEnumToString(MetricType_MetricType_NULL)
}

func StringToPerfDataType() utils.TypeConverter {
	return utils.StringToPBEnum(PerfDataType_PerfDataType_NULL)
}

func PerfDataTypeToString() utils.TypeConverter {
	return utils.PBEnumToString(PerfDataType_PerfDataType_NULL)
}

func StringToExecuteType() utils.TypeConverter {
	return utils.StringToPBEnum(ExecuteType_ET_NULL)
}

func ExecuteTypeToString() utils.TypeConverter {
	return utils.PBEnumToString(ExecuteType_ET_NULL)
}
