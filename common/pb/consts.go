package pb

type ProtocolZH string

const (
	HTTPProtocolZH   ProtocolZH = "HTTP协议"
	GRPCProtocolZH   ProtocolZH = "GRPC协议"
	TTProtocolZH     ProtocolZH = "TT私有协议"
	TTAuthProtocolZH ProtocolZH = "TT登录压测场景专用协议"
)

type TargetEnvironmentZH string

const (
	DevelopmentZH TargetEnvironmentZH = "开发环境"
	TestingZH     TargetEnvironmentZH = "测试环境"
	StagingZH     TargetEnvironmentZH = "预发布环境"
	CanaryZH      TargetEnvironmentZH = "灰度环境"
	ProductionZH  TargetEnvironmentZH = "生产环境"
)

type TriggerModeZH string

const (
	ManualZH    TriggerModeZH = "手动触发"
	ScheduleZH  TriggerModeZH = "定时触发"
	InterfaceZH TriggerModeZH = "接口触发"
)

type PerfTaskTypeZH string

const (
	RunZH   PerfTaskTypeZH = "执行"
	DebugZH PerfTaskTypeZH = "调试"
)

type MonitorUrlTypeZH string

const (
	GrafanaZH    MonitorUrlTypeZH = "Grafana"
	AppInsightZH MonitorUrlTypeZH = "天相"
)
