// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.1
// 	protoc        v5.29.2
// source: common/load.proto

package pb

import (
	reflect "reflect"
	sync "sync"

	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type LoadGenerator struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	NumberOfLg       uint32                 `protobuf:"varint,1,opt,name=number_of_lg,json=numberOfLg,proto3" json:"number_of_lg,omitempty"`                  // 施压机数量
	RequestsOfCpu    string                 `protobuf:"bytes,2,opt,name=requests_of_cpu,json=requestsOfCpu,proto3" json:"requests_of_cpu,omitempty"`          // 最小分配的CPU资源
	RequestsOfMemory string                 `protobuf:"bytes,3,opt,name=requests_of_memory,json=requestsOfMemory,proto3" json:"requests_of_memory,omitempty"` // 最小分配的内存资源
	LimitsOfCpu      string                 `protobuf:"bytes,4,opt,name=limits_of_cpu,json=limitsOfCpu,proto3" json:"limits_of_cpu,omitempty"`                // 最大分配的CPU资源
	LimitsOfMemory   string                 `protobuf:"bytes,5,opt,name=limits_of_memory,json=limitsOfMemory,proto3" json:"limits_of_memory,omitempty"`       // 最大分配的内存资源
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *LoadGenerator) Reset() {
	*x = LoadGenerator{}
	mi := &file_common_load_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LoadGenerator) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LoadGenerator) ProtoMessage() {}

func (x *LoadGenerator) ProtoReflect() protoreflect.Message {
	mi := &file_common_load_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LoadGenerator.ProtoReflect.Descriptor instead.
func (*LoadGenerator) Descriptor() ([]byte, []int) {
	return file_common_load_proto_rawDescGZIP(), []int{0}
}

func (x *LoadGenerator) GetNumberOfLg() uint32 {
	if x != nil {
		return x.NumberOfLg
	}
	return 0
}

func (x *LoadGenerator) GetRequestsOfCpu() string {
	if x != nil {
		return x.RequestsOfCpu
	}
	return ""
}

func (x *LoadGenerator) GetRequestsOfMemory() string {
	if x != nil {
		return x.RequestsOfMemory
	}
	return ""
}

func (x *LoadGenerator) GetLimitsOfCpu() string {
	if x != nil {
		return x.LimitsOfCpu
	}
	return ""
}

func (x *LoadGenerator) GetLimitsOfMemory() string {
	if x != nil {
		return x.LimitsOfMemory
	}
	return ""
}

var File_common_load_proto protoreflect.FileDescriptor

var file_common_load_proto_rawDesc = []byte{
	0x0a, 0x11, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x6c, 0x6f, 0x61, 0x64, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x12, 0x06, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x1a, 0x17, 0x76, 0x61, 0x6c,
	0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x22, 0x90, 0x02, 0x0a, 0x0d, 0x4c, 0x6f, 0x61, 0x64, 0x47, 0x65, 0x6e,
	0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x12, 0x2b, 0x0a, 0x0c, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72,
	0x5f, 0x6f, 0x66, 0x5f, 0x6c, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x42, 0x09, 0xfa, 0x42,
	0x06, 0x2a, 0x04, 0x28, 0x00, 0x40, 0x01, 0x52, 0x0a, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x4f,
	0x66, 0x4c, 0x67, 0x12, 0x32, 0x0a, 0x0f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x73, 0x5f,
	0x6f, 0x66, 0x5f, 0x63, 0x70, 0x75, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0a, 0xfa, 0x42,
	0x07, 0x72, 0x05, 0x10, 0x01, 0xd0, 0x01, 0x01, 0x52, 0x0d, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x73, 0x4f, 0x66, 0x43, 0x70, 0x75, 0x12, 0x38, 0x0a, 0x12, 0x72, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x73, 0x5f, 0x6f, 0x66, 0x5f, 0x6d, 0x65, 0x6d, 0x6f, 0x72, 0x79, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x72, 0x05, 0x10, 0x01, 0xd0, 0x01, 0x01, 0x52,
	0x10, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x73, 0x4f, 0x66, 0x4d, 0x65, 0x6d, 0x6f, 0x72,
	0x79, 0x12, 0x2e, 0x0a, 0x0d, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x73, 0x5f, 0x6f, 0x66, 0x5f, 0x63,
	0x70, 0x75, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x72, 0x05, 0x10,
	0x01, 0xd0, 0x01, 0x01, 0x52, 0x0b, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x73, 0x4f, 0x66, 0x43, 0x70,
	0x75, 0x12, 0x34, 0x0a, 0x10, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x73, 0x5f, 0x6f, 0x66, 0x5f, 0x6d,
	0x65, 0x6d, 0x6f, 0x72, 0x79, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0a, 0xfa, 0x42, 0x07,
	0x72, 0x05, 0x10, 0x01, 0xd0, 0x01, 0x01, 0x52, 0x0e, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x73, 0x4f,
	0x66, 0x4d, 0x65, 0x6d, 0x6f, 0x72, 0x79, 0x42, 0x3c, 0x5a, 0x3a, 0x67, 0x69, 0x74, 0x6c, 0x61,
	0x62, 0x2e, 0x74, 0x74, 0x79, 0x75, 0x79, 0x69, 0x6e, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x54, 0x65,
	0x73, 0x74, 0x44, 0x65, 0x76, 0x65, 0x6c, 0x6f, 0x70, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x70, 0x72,
	0x6f, 0x62, 0x65, 0x2d, 0x62, 0x61, 0x63, 0x6b, 0x65, 0x6e, 0x64, 0x2f, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2f, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_common_load_proto_rawDescOnce sync.Once
	file_common_load_proto_rawDescData = file_common_load_proto_rawDesc
)

func file_common_load_proto_rawDescGZIP() []byte {
	file_common_load_proto_rawDescOnce.Do(func() {
		file_common_load_proto_rawDescData = protoimpl.X.CompressGZIP(file_common_load_proto_rawDescData)
	})
	return file_common_load_proto_rawDescData
}

var file_common_load_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_common_load_proto_goTypes = []any{
	(*LoadGenerator)(nil), // 0: common.LoadGenerator
}
var file_common_load_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_common_load_proto_init() }
func file_common_load_proto_init() {
	if File_common_load_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_common_load_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_common_load_proto_goTypes,
		DependencyIndexes: file_common_load_proto_depIdxs,
		MessageInfos:      file_common_load_proto_msgTypes,
	}.Build()
	File_common_load_proto = out.File
	file_common_load_proto_rawDesc = nil
	file_common_load_proto_goTypes = nil
	file_common_load_proto_depIdxs = nil
}
