package pb

import (
	"github.com/zyedidia/generic"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/thirdparty/appInsight"
)

func PerfServiceMetaDataUserInfoEquals(a, b *PerfUserInfo) bool {
	return a.GetAccount() == b.GetAccount()
}

func PerfServiceMetaDataUserInfoHash(a *PerfUserInfo) uint64 {
	return generic.HashString(a.GetAccount())
}

func (x Protocol) ConvertToZH() ProtocolZH {
	switch x {
	case Protocol_PROTOCOL_HTTP:
		return HTTPProtocolZH
	case Protocol_PROTOCOL_GRPC:
		return GRPCProtocolZH
	case Protocol_PROTOCOL_TT:
		return TTProtocolZH
	case Protocol_PROTOCOL_TT_AUTH:
		return TTAuthProtocolZH
	default:
		return ""
	}
}

func (x TargetEnvironment) ConvertToZH() TargetEnvironmentZH {
	switch x {
	case TargetEnvironment_TE_DEVELOPMENT:
		return DevelopmentZH
	case TargetEnvironment_TE_TESTING:
		return TestingZH
	case TargetEnvironment_TE_STAGING:
		return StagingZH
	case TargetEnvironment_TE_CANARY:
		return CanaryZH
	case TargetEnvironment_TE_PRODUCTION:
		return ProductionZH
	default:
		return ""
	}
}

func (x TriggerMode) ConvertToZH() TriggerModeZH {
	switch x {
	case TriggerMode_MANUAL:
		return ManualZH
	case TriggerMode_SCHEDULE:
		return ScheduleZH
	case TriggerMode_INTERFACE:
		return InterfaceZH
	default:
		return ""
	}
}

func (x PerfTaskType) ConvertToZH() PerfTaskTypeZH {
	switch x {
	case PerfTaskType_RUN:
		return RunZH
	case PerfTaskType_DEBUG:
		return DebugZH
	default:
		return ""
	}
}

func (x MonitorUrlType) ConvertToZH() MonitorUrlTypeZH {
	switch x {
	case MonitorUrlType_MUT_GRAFANA:
		return GrafanaZH
	case MonitorUrlType_MUT_APP_INSIGHT:
		return AppInsightZH
	default:
		return ""
	}
}

func (x MetricType) ConvertToZH() appInsight.MetricType {
	switch x {
	case MetricType_MetricType_QPS:
		return appInsight.MetricTypeOfQPS
	case MetricType_MetricType_FailRatio:
		return appInsight.MetricTypeOfFailRatio
	case MetricType_MetricType_P99:
		return appInsight.MetricTypeOfP99
	case MetricType_MetricType_P95:
		return appInsight.MetricTypeOfP95
	case MetricType_MetricType_P90:
		return appInsight.MetricTypeOfP90
	case MetricType_MetricType_P75:
		return appInsight.MetricTypeOfP75
	case MetricType_MetricType_P50:
		return appInsight.MetricTypeOfP50
	default:
		return ""
	}
}
