// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: common/load.proto

package pb

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on LoadGenerator with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *LoadGenerator) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LoadGenerator with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in LoadGeneratorMultiError, or
// nil if none found.
func (m *LoadGenerator) ValidateAll() error {
	return m.validate(true)
}

func (m *LoadGenerator) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetNumberOfLg() != 0 {

		if m.GetNumberOfLg() < 0 {
			err := LoadGeneratorValidationError{
				field:  "NumberOfLg",
				reason: "value must be greater than or equal to 0",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if m.GetRequestsOfCpu() != "" {

		if utf8.RuneCountInString(m.GetRequestsOfCpu()) < 1 {
			err := LoadGeneratorValidationError{
				field:  "RequestsOfCpu",
				reason: "value length must be at least 1 runes",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if m.GetRequestsOfMemory() != "" {

		if utf8.RuneCountInString(m.GetRequestsOfMemory()) < 1 {
			err := LoadGeneratorValidationError{
				field:  "RequestsOfMemory",
				reason: "value length must be at least 1 runes",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if m.GetLimitsOfCpu() != "" {

		if utf8.RuneCountInString(m.GetLimitsOfCpu()) < 1 {
			err := LoadGeneratorValidationError{
				field:  "LimitsOfCpu",
				reason: "value length must be at least 1 runes",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if m.GetLimitsOfMemory() != "" {

		if utf8.RuneCountInString(m.GetLimitsOfMemory()) < 1 {
			err := LoadGeneratorValidationError{
				field:  "LimitsOfMemory",
				reason: "value length must be at least 1 runes",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if len(errors) > 0 {
		return LoadGeneratorMultiError(errors)
	}

	return nil
}

// LoadGeneratorMultiError is an error wrapping multiple validation errors
// returned by LoadGenerator.ValidateAll() if the designated constraints
// aren't met.
type LoadGeneratorMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LoadGeneratorMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LoadGeneratorMultiError) AllErrors() []error { return m }

// LoadGeneratorValidationError is the validation error returned by
// LoadGenerator.Validate if the designated constraints aren't met.
type LoadGeneratorValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LoadGeneratorValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LoadGeneratorValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LoadGeneratorValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LoadGeneratorValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LoadGeneratorValidationError) ErrorName() string { return "LoadGeneratorValidationError" }

// Error satisfies the builtin error interface
func (e LoadGeneratorValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLoadGenerator.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LoadGeneratorValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LoadGeneratorValidationError{}
