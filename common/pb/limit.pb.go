// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.1
// 	protoc        v5.29.2
// source: common/limit.proto

package pb

import (
	reflect "reflect"
	sync "sync"

	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type RateLimit struct {
	state protoimpl.MessageState `protogen:"open.v1"`

	TargetRps int64 `protobuf:"varint,1,opt,name=target_rps,json=targetRps,proto3" json:"target_rps" yaml:"TargetRPS"` // 目标的RPS

	InitialRps int64 `protobuf:"varint,2,opt,name=initial_rps,json=initialRps,proto3" json:"initial_rps" yaml:"InitialRPS"` // 初始的RPS

	StepHeight int64 `protobuf:"varint,3,opt,name=step_height,json=stepHeight,proto3" json:"step_height" yaml:"StepHeight"` // 每次改变RPS的量

	StepDuration  string `protobuf:"bytes,4,opt,name=step_duration,json=stepDuration,proto3" json:"step_duration" yaml:"StepDuration"` // 改变后的RPS的持续时间
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RateLimit) Reset() {
	*x = RateLimit{}
	mi := &file_common_limit_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RateLimit) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RateLimit) ProtoMessage() {}

func (x *RateLimit) ProtoReflect() protoreflect.Message {
	mi := &file_common_limit_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RateLimit.ProtoReflect.Descriptor instead.
func (*RateLimit) Descriptor() ([]byte, []int) {
	return file_common_limit_proto_rawDescGZIP(), []int{0}
}

func (x *RateLimit) GetTargetRps() int64 {
	if x != nil {
		return x.TargetRps
	}
	return 0
}

func (x *RateLimit) GetInitialRps() int64 {
	if x != nil {
		return x.InitialRps
	}
	return 0
}

func (x *RateLimit) GetStepHeight() int64 {
	if x != nil {
		return x.StepHeight
	}
	return 0
}

func (x *RateLimit) GetStepDuration() string {
	if x != nil {
		return x.StepDuration
	}
	return ""
}

type RateLimitV2 struct {
	state protoimpl.MessageState `protogen:"open.v1"`

	TargetRps int64 `protobuf:"varint,1,opt,name=target_rps,json=targetRps,proto3" json:"target_rps" yaml:"TargetRPS"` // 目标的RPS

	InitialRps int64 `protobuf:"varint,2,opt,name=initial_rps,json=initialRps,proto3" json:"initial_rps" yaml:"InitialRPS"` // 初始的RPS

	ChangeDuration string `protobuf:"bytes,3,opt,name=change_duration,json=changeDuration,proto3" json:"change_duration" yaml:"ChangeDuration"` // 加压持续时间（从初始值到目标值）

	TargetDuration string `protobuf:"bytes,4,opt,name=target_duration,json=targetDuration,proto3" json:"target_duration" yaml:"TargetDuration"` // 施压持续时间（经过加压时间后维持的时间）
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *RateLimitV2) Reset() {
	*x = RateLimitV2{}
	mi := &file_common_limit_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RateLimitV2) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RateLimitV2) ProtoMessage() {}

func (x *RateLimitV2) ProtoReflect() protoreflect.Message {
	mi := &file_common_limit_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RateLimitV2.ProtoReflect.Descriptor instead.
func (*RateLimitV2) Descriptor() ([]byte, []int) {
	return file_common_limit_proto_rawDescGZIP(), []int{1}
}

func (x *RateLimitV2) GetTargetRps() int64 {
	if x != nil {
		return x.TargetRps
	}
	return 0
}

func (x *RateLimitV2) GetInitialRps() int64 {
	if x != nil {
		return x.InitialRps
	}
	return 0
}

func (x *RateLimitV2) GetChangeDuration() string {
	if x != nil {
		return x.ChangeDuration
	}
	return ""
}

func (x *RateLimitV2) GetTargetDuration() string {
	if x != nil {
		return x.TargetDuration
	}
	return ""
}

var File_common_limit_proto protoreflect.FileDescriptor

var file_common_limit_proto_rawDesc = []byte{
	0x0a, 0x12, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x12, 0x06, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x1a, 0x17, 0x76, 0x61,
	0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xbc, 0x01, 0x0a, 0x09, 0x52, 0x61, 0x74, 0x65, 0x4c, 0x69,
	0x6d, 0x69, 0x74, 0x12, 0x26, 0x0a, 0x0a, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x72, 0x70,
	0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00,
	0x52, 0x09, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x52, 0x70, 0x73, 0x12, 0x2a, 0x0a, 0x0b, 0x69,
	0x6e, 0x69, 0x74, 0x69, 0x61, 0x6c, 0x5f, 0x72, 0x70, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03,
	0x42, 0x09, 0xfa, 0x42, 0x06, 0x22, 0x04, 0x20, 0x00, 0x40, 0x01, 0x52, 0x0a, 0x69, 0x6e, 0x69,
	0x74, 0x69, 0x61, 0x6c, 0x52, 0x70, 0x73, 0x12, 0x2a, 0x0a, 0x0b, 0x73, 0x74, 0x65, 0x70, 0x5f,
	0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x42, 0x09, 0xfa, 0x42,
	0x06, 0x22, 0x04, 0x38, 0x00, 0x40, 0x01, 0x52, 0x0a, 0x73, 0x74, 0x65, 0x70, 0x48, 0x65, 0x69,
	0x67, 0x68, 0x74, 0x12, 0x2f, 0x0a, 0x0d, 0x73, 0x74, 0x65, 0x70, 0x5f, 0x64, 0x75, 0x72, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x72,
	0x05, 0x10, 0x02, 0xd0, 0x01, 0x01, 0x52, 0x0c, 0x73, 0x74, 0x65, 0x70, 0x44, 0x75, 0x72, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x22, 0xcb, 0x01, 0x0a, 0x0b, 0x52, 0x61, 0x74, 0x65, 0x4c, 0x69, 0x6d,
	0x69, 0x74, 0x56, 0x32, 0x12, 0x26, 0x0a, 0x0a, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x72,
	0x70, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20,
	0x00, 0x52, 0x09, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x52, 0x70, 0x73, 0x12, 0x2a, 0x0a, 0x0b,
	0x69, 0x6e, 0x69, 0x74, 0x69, 0x61, 0x6c, 0x5f, 0x72, 0x70, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x03, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x22, 0x04, 0x20, 0x00, 0x40, 0x01, 0x52, 0x0a, 0x69, 0x6e,
	0x69, 0x74, 0x69, 0x61, 0x6c, 0x52, 0x70, 0x73, 0x12, 0x33, 0x0a, 0x0f, 0x63, 0x68, 0x61, 0x6e,
	0x67, 0x65, 0x5f, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x72, 0x05, 0x10, 0x02, 0xd0, 0x01, 0x01, 0x52, 0x0e, 0x63,
	0x68, 0x61, 0x6e, 0x67, 0x65, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x33, 0x0a,
	0x0f, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x72, 0x05, 0x10, 0x02, 0xd0,
	0x01, 0x01, 0x52, 0x0e, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x42, 0x3c, 0x5a, 0x3a, 0x67, 0x69, 0x74, 0x6c, 0x61, 0x62, 0x2e, 0x74, 0x74, 0x79,
	0x75, 0x79, 0x69, 0x6e, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x54, 0x65, 0x73, 0x74, 0x44, 0x65, 0x76,
	0x65, 0x6c, 0x6f, 0x70, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x70, 0x72, 0x6f, 0x62, 0x65, 0x2d, 0x62,
	0x61, 0x63, 0x6b, 0x65, 0x6e, 0x64, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x70, 0x62,
	0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_common_limit_proto_rawDescOnce sync.Once
	file_common_limit_proto_rawDescData = file_common_limit_proto_rawDesc
)

func file_common_limit_proto_rawDescGZIP() []byte {
	file_common_limit_proto_rawDescOnce.Do(func() {
		file_common_limit_proto_rawDescData = protoimpl.X.CompressGZIP(file_common_limit_proto_rawDescData)
	})
	return file_common_limit_proto_rawDescData
}

var file_common_limit_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_common_limit_proto_goTypes = []any{
	(*RateLimit)(nil),   // 0: common.RateLimit
	(*RateLimitV2)(nil), // 1: common.RateLimitV2
}
var file_common_limit_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_common_limit_proto_init() }
func file_common_limit_proto_init() {
	if File_common_limit_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_common_limit_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_common_limit_proto_goTypes,
		DependencyIndexes: file_common_limit_proto_depIdxs,
		MessageInfos:      file_common_limit_proto_msgTypes,
	}.Build()
	File_common_limit_proto = out.File
	file_common_limit_proto_rawDesc = nil
	file_common_limit_proto_goTypes = nil
	file_common_limit_proto_depIdxs = nil
}
