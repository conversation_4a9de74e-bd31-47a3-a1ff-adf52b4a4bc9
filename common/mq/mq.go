package mq

import (
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworkerv2/base"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
)

// ConvertPbEnumerationToQueuePriority 转换pb枚举到队列优先级
func ConvertPbEnumerationToQueuePriority(priorityType pb.PriorityType) base.QueuePriorityType {
	switch priorityType {
	case pb.PriorityType_Low:
		return base.QueuePriorityLow
	case pb.PriorityType_Middle:
		return base.QueuePriorityDefault
	case pb.PriorityType_High:
		return base.QueuePriorityHigh
	case pb.PriorityType_Ultra:
		return base.QueuePriorityUltra
	}
	return base.QueuePriorityDefault
}
