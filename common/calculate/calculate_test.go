package calculate

import (
	"math"
	"reflect"
	"testing"
	"time"

	commonpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
)

func Test_calculateSinglePart(t *testing.T) {
	type args struct {
		rateLimit  *commonpb.RateLimitV2
		numberOfVU uint32
		isLastPart bool
	}
	tests := []struct {
		name             string
		args             args
		wantStepDuration time.Duration
		wantUnfinishedVU uint32
	}{
		{
			name: "test1",
			args: args{
				rateLimit: &commonpb.RateLimitV2{
					TargetRps:      101,
					InitialRps:     1,
					TargetDuration: "50s",
					ChangeDuration: "50s",
				},
				numberOfVU: 8000,
			},
			wantStepDuration: 105 * time.Second,
			wantUnfinishedVU: 450,
		},
		{
			name: "test2",
			args: args{
				rateLimit: &commonpb.RateLimitV2{
					TargetRps:      101,
					InitialRps:     1,
					TargetDuration: "50s",
					ChangeDuration: "50s",
				},
				numberOfVU: 10001,
				isLastPart: false,
			},
			wantStepDuration: 9 * time.Second,
			wantUnfinishedVU: 10,
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				gotStepDuration, gotUnfinishedVU := calculateSinglePartV2(
					tt.args.rateLimit, tt.args.numberOfVU, tt.args.isLastPart,
				)
				if gotStepDuration != tt.wantStepDuration {
					t.Errorf(
						"calculateSinglePart() gotStepDuration = %v, want %v", gotStepDuration, tt.wantStepDuration,
					)
				}
				if gotUnfinishedVU != tt.wantUnfinishedVU {
					t.Errorf(
						"calculateSinglePart() gotUnfinishedVU = %v, want %v", gotUnfinishedVU, tt.wantUnfinishedVU,
					)
				}
			},
		)
	}
}

func TestCalculateTotalCaseDuration(t *testing.T) {
	type args struct {
		perfCaseV2       *commonpb.PerfCaseContentV2
		numberOfPerfData uint32
		options          []Option
	}
	tests := []struct {
		name string
		args args
		want ExecutionDurations
	}{
		{
			name: "overload",
			args: args{
				perfCaseV2: &commonpb.PerfCaseContentV2{
					SetupSteps: []*commonpb.PerfCaseStepV2{
						{
							RateLimits: []*commonpb.RateLimitV2{
								{
									TargetRps:      101,
									InitialRps:     1,
									TargetDuration: "50s",
									ChangeDuration: "50s",
								},
							},
						},
					},
					SerialSteps: []*commonpb.PerfCaseStepV2{
						{
							RateLimits: []*commonpb.RateLimitV2{
								{
									TargetRps:      101,
									InitialRps:     1,
									TargetDuration: "50s",
									ChangeDuration: "50s",
								},
							},
						},
					},
					ParallelSteps: []*commonpb.PerfCaseStepV2{
						{
							RateLimits: []*commonpb.RateLimitV2{
								{
									TargetRps:      101,
									InitialRps:     1,
									TargetDuration: "50s",
									ChangeDuration: "50s",
								},
							},
						},
					},
					TeardownSteps: []*commonpb.PerfCaseStepV2{
						{
							RateLimits: []*commonpb.RateLimitV2{
								{
									TargetRps:      101,
									InitialRps:     1,
									TargetDuration: "50s",
									ChangeDuration: "50s",
								},
							},
						},
					},
				},
				numberOfPerfData: 10000,
				options: []Option{
					WithExecutionTimes(1),
					WithAuthRateLimits(
						[]*commonpb.RateLimitV2{
							{
								TargetRps:      101,
								InitialRps:     1,
								TargetDuration: "50s",
								ChangeDuration: "50s",
							},
						},
					),
				},
			},
			want: ExecutionDurations{
				AuthDuration:     0,
				SetupDuration:    0,
				PerfTestDuration: 0,
				TeardownDuration: 0,
				TotalDuration:    0,
			},
		},
		{
			name: "less",
			args: args{
				perfCaseV2: &commonpb.PerfCaseContentV2{
					SetupSteps: []*commonpb.PerfCaseStepV2{
						{
							RateLimits: []*commonpb.RateLimitV2{
								{
									TargetRps:      101,
									InitialRps:     1,
									TargetDuration: "5s",
									ChangeDuration: "10s",
								},
							},
						},
					},
					SerialSteps: []*commonpb.PerfCaseStepV2{
						{
							RateLimits: []*commonpb.RateLimitV2{
								{
									TargetRps:      101,
									InitialRps:     1,
									TargetDuration: "5s",
									ChangeDuration: "10s",
								},
							},
						},
					},
					ParallelSteps: []*commonpb.PerfCaseStepV2{
						{
							RateLimits: []*commonpb.RateLimitV2{
								{
									TargetRps:      101,
									InitialRps:     1,
									TargetDuration: "5s",
									ChangeDuration: "10s",
								},
							},
						},
					},
					TeardownSteps: []*commonpb.PerfCaseStepV2{
						{
							RateLimits: []*commonpb.RateLimitV2{
								{
									TargetRps:      101,
									InitialRps:     1,
									TargetDuration: "5s",
									ChangeDuration: "10s",
								},
							},
						},
					},
				},
				numberOfPerfData: 125,
				options: []Option{
					WithExecutionTimes(1),
					WithAuthRateLimits(
						[]*commonpb.RateLimitV2{
							{
								TargetRps:      101,
								InitialRps:     1,
								TargetDuration: "10s",
								ChangeDuration: "5s",
							},
						},
					),
				},
			},
			want: ExecutionDurations{
				AuthDuration:     0,
				SetupDuration:    0,
				PerfTestDuration: 0,
				TeardownDuration: 0,
				TotalDuration:    0,
			},
		},
		{
			name: "more case",
			args: args{
				perfCaseV2: &commonpb.PerfCaseContentV2{
					SetupSteps: []*commonpb.PerfCaseStepV2{
						{
							RateLimits: []*commonpb.RateLimitV2{
								{
									TargetRps:      100,
									InitialRps:     1,
									TargetDuration: "10s",
									ChangeDuration: "0s",
								},
							},
						},
					},
					SerialSteps: []*commonpb.PerfCaseStepV2{
						{

							//RateLimits: []*commonpb.RateLimitV2{
							//    {
							//        TargetRps:      100,
							//        InitialRps:     1,
							//        TargetDuration: "4m",
							//        ChangeDuration: "2m",
							//    },
							//    {
							//        TargetRps:      250,
							//        InitialRps:     100,
							//        TargetDuration: "4m",
							//        ChangeDuration: "2m",
							//    },
							//},
						},
					},

					//ParallelSteps: []*commonpb.PerfCaseStepV2{
					//    {
					//        RateLimits: []*commonpb.RateLimitV2{
					//            {
					//                TargetRps:      100,
					//                InitialRps:     1,
					//                TargetDuration: "4m",
					//                ChangeDuration: "2m",
					//            },
					//            {
					//                TargetRps:      250,
					//                InitialRps:     100,
					//                TargetDuration: "4m",
					//                ChangeDuration: "2m",
					//            },
					//        },
					//    },
					//},
					TeardownSteps: []*commonpb.PerfCaseStepV2{
						{
							RateLimits: []*commonpb.RateLimitV2{
								{
									TargetRps:      120,
									InitialRps:     1,
									TargetDuration: "10s",
									ChangeDuration: "0s",
								},
							},
						},
					},
				},
				numberOfPerfData: 125,
				options: []Option{
					WithExecutionTimes(0),
					WithAuthRateLimits(
						[]*commonpb.RateLimitV2{
							{
								TargetRps:      101,
								InitialRps:     1,
								TargetDuration: "10s",
								ChangeDuration: "5s",
							},
						},
					),
					WithGlobalRateLimits(
						[]*commonpb.RateLimitV2{
							{
								TargetRps:      100,
								InitialRps:     1,
								TargetDuration: "4m",
								ChangeDuration: "2m",
							},
							{
								TargetRps:      250,
								InitialRps:     100,
								TargetDuration: "4m",
								ChangeDuration: "2m",
							},
						},
					),
				},
			},
			want: ExecutionDurations{
				AuthDuration:     0,
				SetupDuration:    0,
				PerfTestDuration: 0,
				TeardownDuration: 0,
				TotalDuration:    0,
			},
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				if got := CalculateTotalCaseDuration(
					tt.args.perfCaseV2, tt.args.numberOfPerfData, tt.args.options...,
				); !reflect.DeepEqual(got, tt.want) {
					t.Errorf("CalculateTotalCaseDuration() = %v, want %v", got, tt.want)
				}
			},
		)
	}
}

func TestAny(t *testing.T) {
	t.Log("result: ", int(math.Ceil((6-10)/8)))
}

func TestCalculateNumberOfVirtualUsers(t *testing.T) {
	type args struct {
		serials           []*commonpb.PerfCaseStepV2
		parallels         []*commonpb.PerfCaseStepV2
		defaultRateLimits []*commonpb.RateLimitV2
	}
	tests := []struct {
		name string
		args args
		want int64
	}{
		{
			name: "test1",
			args: args{
				serials: []*commonpb.PerfCaseStepV2{
					{
						Sleep: "5s",
						RateLimits: []*commonpb.RateLimitV2{
							{
								TargetRps:      110,
								InitialRps:     110,
								TargetDuration: "5s",
								ChangeDuration: "10s",
							},
						},
					},
				},
				parallels: []*commonpb.PerfCaseStepV2{
					{
						Sleep: "30s",
						RateLimits: []*commonpb.RateLimitV2{
							{
								TargetRps:      100,
								InitialRps:     100,
								TargetDuration: "5s",
								ChangeDuration: "10s",
							},
						},
					},
				},
				defaultRateLimits: []*commonpb.RateLimitV2{
					{
						TargetRps:      100,
						InitialRps:     100,
						ChangeDuration: "5s",
						TargetDuration: "10s",
					},
				},
			},
			want: 3050,
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				if got := CalculateNumberOfVirtualUsers(
					tt.args.serials, tt.args.parallels, tt.args.defaultRateLimits,
				); got != tt.want {
					t.Errorf("CalculateNumberOfVirtualUsers() = %v, want %v", got, tt.want)
				}
			},
		)
	}
}

func TestCalculateLoadGeneratorResource(t *testing.T) {
	type args struct {
		vus  uint32
		load *commonpb.LoadGenerator
	}
	tests := []struct {
		name string
		args args
		want *commonpb.LoadGenerator
	}{
		{
			name: "test1",
			args: args{
				vus: 100,
				load: &commonpb.LoadGenerator{
					LimitsOfCpu:      "1",
					LimitsOfMemory:   "1Gi",
					NumberOfLg:       1,
					RequestsOfCpu:    "1",
					RequestsOfMemory: "1Gi",
				},
			},
			want: &commonpb.LoadGenerator{
				LimitsOfCpu:      "1",
				LimitsOfMemory:   "1Gi",
				NumberOfLg:       1,
				RequestsOfCpu:    "1",
				RequestsOfMemory: "1Gi",
			},
		},
		{
			name: "test2",
			args: args{
				vus:  2001,
				load: &commonpb.LoadGenerator{},
			},
			want: &commonpb.LoadGenerator{
				LimitsOfCpu:      "8",
				LimitsOfMemory:   "8Gi",
				NumberOfLg:       3,
				RequestsOfCpu:    "1",
				RequestsOfMemory: "1Gi",
			},
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				if got := CalculateLoadGeneratorResource(tt.args.vus, tt.args.load); !reflect.DeepEqual(got, tt.want) {
					t.Errorf("CalculateLoadGeneratorResource() = %v, want %v", got, tt.want)
				}
			},
		)
	}
}
