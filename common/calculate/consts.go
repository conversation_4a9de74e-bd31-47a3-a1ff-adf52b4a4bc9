package calculate

import "time"

const (
	ConstDefaultStepDuration = "10s"

	ConstDefaultVUPerLG          = 1000 // 每台施压机的最大虚拟用户数
	ConstMaxVUPerLG              = 10000
	ConstDefaultResponseTime     = 500 * time.Millisecond
	ConstDefaultRequestsOfCPU    = "1"
	ConstDefaultLimitsOfCPU      = "8"
	ConstDefaultRequestsOfMemory = "1Gi"
	ConstDefaultLimitsOfMemory   = "8Gi"
)

const (
	ConstDefaultAuthTargetRPS      = 500
	ConstDefaultAuthInitialRPS     = 50
	ConstDefaultAuthChangeDuration = 30 * time.Second

	// 灰度环境
	constDefaultCanaryAuthTargetRPS  = 50
	constDefaultCanaryAuthInitialRPS = 5

	// 预发布环境
	constDefaultStagingAuthTargetRPS  = 50
	constDefaultStagingAuthInitialRPS = 5

	// 测试环境
	constDefaultTestingAuthTargetRPS  = 50
	constDefaultTestingAuthInitialRPS = 5

	// 开发环境
	constDefaultDevelopmentAuthTargetRPS  = 50
	constDefaultDevelopmentAuthInitialRPS = 5

	ConstDefaultHeartbeatTargetRPS      = 1000
	ConstDefaultHeartbeatInitialRPS     = 100
	ConstDefaultHeartbeatChangeDuration = 30 * time.Second
	ConstDefaultHeartbeatInterval       = 60 // 60 seconds

	// 灰度环境
	constDefaultCanaryHeartbeatTargetRPS  = 100
	constDefaultCanaryHeartbeatInitialRPS = 10

	// 预发布环境
	constDefaultStagingHeartbeatTargetRPS  = 100
	constDefaultStagingHeartbeatInitialRPS = 10

	// 测试环境
	constDefaultTestingHeartbeatTargetRPS  = 100
	constDefaultTestingHeartbeatInitialRPS = 10

	// 开发环境
	constDefaultDevelopmentHeartbeatTargetRPS  = 100
	constDefaultDevelopmentHeartbeatInitialRPS = 10
)

const (
	minAuthDuration = 10 * time.Second

	ClientIdleTimeout  = 5 * time.Minute
	ClientReadTimeout  = 3 * time.Second
	ClientWriteTimeout = 2 * time.Second
	ClientDialTimeout  = 5 * time.Second
	WaiteRespTimeout   = 5 * time.Second
)
