package calculate

import (
	"fmt"
	"time"

	commonpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
)

type Option func(o *calculateDurationOption)

type calculateDurationOption struct {
	executionTimes   uint32
	authRateLimits   []*commonpb.RateLimitV2
	globalRateLimits []*commonpb.RateLimitV2
}

func WithExecutionTimes(executionTimes uint32) Option {
	return func(o *calculateDurationOption) {
		o.executionTimes = executionTimes
	}
}

func WithAuthRateLimits(authRateLimits []*commonpb.RateLimitV2) Option {
	return func(o *calculateDurationOption) {
		o.authRateLimits = authRateLimits
	}
}

func WithGlobalRateLimits(globalRateLimits []*commonpb.RateLimitV2) Option {
	return func(o *calculateDurationOption) {
		o.globalRateLimits = globalRateLimits
	}
}

func WithoutAuth() Option {
	return func(o *calculateDurationOption) {
		o.authRateLimits = []*commonpb.RateLimitV2{}
	}
}

func WithoutGlobal() Option {
	return func(o *calculateDurationOption) {
		o.globalRateLimits = []*commonpb.RateLimitV2{}
	}
}

func defaultOption() *calculateDurationOption {
	return &calculateDurationOption{
		executionTimes:   0, // by duration
		authRateLimits:   defaultAuthRateLimits,
		globalRateLimits: make([]*commonpb.RateLimitV2, 0),
	}
}

type ExecutionDurations struct {
	AuthDuration     time.Duration // 认证耗时
	SetupDuration    time.Duration // 前置步骤耗时
	PerfTestDuration time.Duration // 压测耗时（包括：串行步骤、并行步骤）
	TeardownDuration time.Duration // 后置步骤耗时
	TotalDuration    time.Duration // 总耗时
}

func (d ExecutionDurations) String() string {
	return fmt.Sprintf(
		`{"auth_duration": "%s", "setup_duration": "%s", "perf_test_duration": "%s", "teardown_duration": "%s", "total_duration": "%s"}`,
		d.AuthDuration.String(), d.SetupDuration.String(), d.PerfTestDuration.String(), d.TeardownDuration.String(),
		d.TotalDuration.String(),
	)
}
