package calculate

import (
	"math"
	"time"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/cache"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
)

func CalculateNumberOfVirtualUsers(
	serials, parallels []*pb.PerfCaseStepV2, defaultRateLimits []*pb.RateLimitV2,
) int64 {
	var (
		cost, svu, pvu int64
		rps            int64
		second         = int64(time.Second)
	)

	// 找到串行步骤的每段限流速率中的最大值
	for _, step := range serials {
		// 串行需要累加cost
		cost += int64(ConstDefaultResponseTime + cache.Parse(step.GetSleep()))

		rateLimits := step.GetRateLimits()
		if rateLimits == nil {
			rateLimits = defaultRateLimits
		}

		for _, rateLimit := range rateLimits {
			if rps < rateLimit.GetTargetRps() {
				rps = rateLimit.GetTargetRps()
			}
		}
	}
	svu = (rps*cost + second - 1) / second // ceil(rps / (second / cost)) => ceil(rps * cost / second)

	for _, step := range parallels {
		// 并行只算当前的即可
		cost = int64(ConstDefaultResponseTime + cache.Parse(step.GetSleep()))

		rateLimits := step.GetRateLimits()
		if rateLimits == nil {
			rateLimits = defaultRateLimits
		}

		for _, rateLimit := range rateLimits {
			vu := (rateLimit.GetTargetRps()*cost + second - 1) / second // ceil(rps / (second / cost)) => ceil(rps * cost / second)
			if pvu < vu {
				pvu = vu
			}
		}
	}

	if svu > pvu {
		return svu
	}

	return pvu
}

func CalculateLoadGeneratorResource(vus uint32, load *pb.LoadGenerator) *pb.LoadGenerator {
	lg := &pb.LoadGenerator{
		NumberOfLg:       load.GetNumberOfLg(),
		RequestsOfCpu:    load.GetRequestsOfCpu(),
		RequestsOfMemory: load.GetRequestsOfMemory(),
		LimitsOfCpu:      load.GetLimitsOfCpu(),
		LimitsOfMemory:   load.GetLimitsOfMemory(),
	}

	if lg.GetNumberOfLg() == 0 || lg.GetNumberOfLg() > vus {
		// calculate the number of load generators by the number of virtual users
		lg.NumberOfLg = (vus + ConstDefaultVUPerLG - 1) / ConstDefaultVUPerLG // round up
	} else if vus/lg.GetNumberOfLg() > ConstMaxVUPerLG {
		lg.NumberOfLg = (vus + ConstMaxVUPerLG - 1) / ConstMaxVUPerLG
	}
	if lg.GetRequestsOfCpu() == "" {
		lg.RequestsOfCpu = ConstDefaultRequestsOfCPU
	}
	if lg.GetRequestsOfMemory() == "" {
		lg.RequestsOfMemory = ConstDefaultRequestsOfMemory
	}
	if lg.GetLimitsOfCpu() == "" {
		lg.LimitsOfCpu = ConstDefaultLimitsOfCPU
	}
	if lg.GetLimitsOfMemory() == "" {
		lg.LimitsOfMemory = ConstDefaultLimitsOfMemory
	}

	return lg
}

func CalculateTotalCaseDuration(
	perfCaseV2 *pb.PerfCaseContentV2, numberOfPerfData uint32, options ...Option,
) ExecutionDurations {
	var (
		authDuration     = time.Duration(0)
		setupDuration    = time.Duration(0)
		serialDuration   = time.Duration(0)
		parallelDuration = time.Duration(0)
		teardownDuration = time.Duration(0)

		serialOrParallelDuration time.Duration
	)

	opt := defaultOption()
	for _, option := range options {
		option(opt)
	}

	// 1. calculate the max duration of auth process
	if len(opt.authRateLimits) > 0 {
		authDuration += CalculateStepDurationByTotalVu(
			&pb.PerfCaseStepV2{
				RateLimits: opt.authRateLimits,
				Sleep:      "0s",
			}, numberOfPerfData,
		)
		authDuration += WaiteRespTimeout
	}

	// 2. calculate the max duration of setup steps
	setupSteps := perfCaseV2.GetSetupSteps()
	numberOfSetupSteps := len(setupSteps)
	if numberOfSetupSteps > 0 {
		for _, step := range setupSteps {
			setupDuration += CalculateStepDurationByTotalVu(step, numberOfPerfData, opt.globalRateLimits)
		}
		setupDuration += time.Duration(numberOfSetupSteps) * WaiteRespTimeout
	}

	// 3. calculate the max duration of serial steps and parallel steps
	serialSteps := perfCaseV2.GetSerialSteps()
	parallelSteps := perfCaseV2.GetParallelSteps()
	numberOfSerialSteps := len(serialSteps)
	numberOfParallelSteps := len(parallelSteps)
	if opt.executionTimes > 0 { // by times
		times := opt.executionTimes
		if numberOfSerialSteps > 0 {
			// 累加所有的耗时
			for _, step := range serialSteps {
				serialDuration += CalculateStepDurationByTotalVu(
					step,
					numberOfPerfData,
					opt.globalRateLimits,
				) + WaiteRespTimeout
			}
			serialDuration *= time.Duration(times)
		}

		if numberOfParallelSteps > 0 {
			// 分别计算每个的耗时 , 取最久的
			for _, step := range parallelSteps {
				duration := CalculateStepDurationByTotalVu(step, numberOfPerfData, opt.globalRateLimits)
				duration += time.Duration(times) * WaiteRespTimeout
				parallelDuration = utils.Max(duration, parallelDuration)
			}
		}
	} else { // by duration
		if numberOfSerialSteps > 0 {
			serialDuration = CalculateStepDurationBySerial(serialSteps, opt.globalRateLimits)
		}

		if numberOfParallelSteps > 0 {
			parallelDuration = CalculateStepDurationByParallel(parallelSteps, opt.globalRateLimits)
		}
	}
	serialOrParallelDuration = utils.Max(serialDuration, parallelDuration)

	// 4. calculate the max duration of teardown steps
	teardownSteps := perfCaseV2.GetTeardownSteps()
	numberOfTeardownSteps := len(teardownSteps)
	if numberOfTeardownSteps > 0 {
		for _, step := range teardownSteps {
			teardownDuration += CalculateStepDurationByTotalVu(step, numberOfPerfData, opt.globalRateLimits)
		}
		teardownDuration += time.Duration(numberOfTeardownSteps) * WaiteRespTimeout
	}

	return ExecutionDurations{
		AuthDuration:     authDuration,
		SetupDuration:    setupDuration,
		PerfTestDuration: serialOrParallelDuration,
		TeardownDuration: teardownDuration,
		TotalDuration:    authDuration + setupDuration + serialOrParallelDuration + teardownDuration,
	}
}

func CalculateStepDurationBySerial(
	steps []*pb.PerfCaseStepV2, globalRateLimits ...[]*pb.RateLimitV2,
) time.Duration {
	var totalDuration time.Duration

	// 串行取最小的耗时, 防止把小接口压超了
	for _, step := range steps {
		rateLimits := make([]*pb.RateLimitV2, 0)
		rateLimits = append(rateLimits, step.RateLimits...)

		if step.GetRateLimits() == nil {
			for _, limit := range globalRateLimits {
				rateLimits = limit
			}
		}
		if totalDuration == 0 {
			totalDuration += calculateStepDurationByStep(step, rateLimits)
			continue
		}
		totalDuration = utils.Min[time.Duration](calculateStepDurationByStep(step, rateLimits), totalDuration)
	}

	return totalDuration
}

func CalculateStepDurationByParallel(
	steps []*pb.PerfCaseStepV2, globalRateLimits ...[]*pb.RateLimitV2,
) time.Duration {
	var totalDuration time.Duration

	// 并行取最长
	for _, step := range steps {
		rateLimits := make([]*pb.RateLimitV2, 0)
		rateLimits = append(rateLimits, step.RateLimits...)

		if step.GetRateLimits() == nil {
			for _, limit := range globalRateLimits {
				rateLimits = limit
			}
		}
		totalDuration = utils.Max[time.Duration](calculateStepDurationByStep(step, rateLimits), totalDuration)
	}

	return totalDuration
}

func calculateStepDurationByStep(step *pb.PerfCaseStepV2, rateLimits []*pb.RateLimitV2) time.Duration {
	var (
		sleepDuration = cache.Parse(step.GetSleep())
		totalDuration time.Duration
	)

	// 分段限流
	for _, limit := range rateLimits {
		// 加压时耗费的时间
		totalDuration += cache.Parse(limit.GetChangeDuration())

		// 在目标rps耗费的时间
		totalDuration += cache.Parse(limit.GetTargetDuration())
	}

	return totalDuration + sleepDuration
}

func CalculateStepDurationByTotalVu(
	step *pb.PerfCaseStepV2, numberOfVU uint32, globalRateLimits ...[]*pb.RateLimitV2,
) time.Duration {
	/**
	  根据用户数 计算耗时, 最后一段限流时需要把所有的用户都跑完, 即便已经超过了targetDuration
	*/
	var (
		sleepDuration = cache.Parse(step.GetSleep())
		totalDuration time.Duration
		unfinishedVU  uint32
		rateLimits    []*pb.RateLimitV2
	)

	unfinishedVU = numberOfVU

	if len(step.GetRateLimits()) > 0 {
		rateLimits = make([]*pb.RateLimitV2, 0, len(step.GetRateLimits()))
		rateLimits = append(rateLimits, step.GetRateLimits()...)
	} else if len(globalRateLimits) > 0 {
		rateLimits = globalRateLimits[0]
	} else {
		rateLimits = make([]*pb.RateLimitV2, 0)
	}

	// 分段限流
	for i, limit := range rateLimits {
		var isLastPart bool
		if i == len(rateLimits)-1 {
			isLastPart = true
		}

		// 针对一段 开始计算 , 如果是最后一段需要特殊处理 要一直把用户数跑完
		stepDuration, unfinishedVU := calculateSinglePartV2(limit, unfinishedVU, isLastPart)
		totalDuration += stepDuration
		if unfinishedVU == 0 {
			// 这些用户已经都跑完了, 不需要被其他限流器控制了
			break
		}
	}

	return sleepDuration + totalDuration
}

// 计算一段限流的耗时
func calculateSinglePart(rateLimit *pb.RateLimitV2, numberOfVU uint32) (
	stepDuration time.Duration, unfinishedVU uint32,
) {
	var (
		currentRPS = rateLimit.GetInitialRps()
		targetRPS  = rateLimit.GetTargetRps()

		steps                   time.Duration
		stepHeight              int64
		numberOfPreviewFinished int64
		unfinishedVURes         int64

		numberOfFinished     int64
		totalDurationSeconds int64
	)

	if currentRPS == rateLimit.GetTargetRps() {
		// 恒定速率
		currentRPS = rateLimit.GetTargetRps()
	}

	// 一步10s
	// - step_duration = 10s
	// - steps = change_duration / step_duration		// 在加压阶段 一共会有几个阶梯
	// - step_height = (target_rps - initial_rps) / steps		// 每一步阶梯需要上升的

	steps = cache.Parse(rateLimit.GetChangeDuration()) / cache.Parse(ConstDefaultStepDuration)
	stepHeight = (rateLimit.GetTargetRps() - rateLimit.GetInitialRps()) / int64(steps)

	// 加压阶段消耗的用户数
	changeTotalVu := (rateLimit.GetTargetRps() - rateLimit.GetInitialRps()) * (cache.Parse(rateLimit.GetChangeDuration()).Milliseconds() / 1000) / 2
	// 完整施压阶段消耗的用户数
	targetTotalVu := rateLimit.GetTargetRps() * cache.Parse(rateLimit.GetTargetDuration()).Milliseconds() / 1000

	// 预分配用户数
	numberOfPreviewFinished = changeTotalVu + targetTotalVu

	// 超于预算,则有多少使用多少
	if numberOfPreviewFinished > int64(numberOfVU) {
		numberOfPreviewFinished = int64(numberOfVU)
	}
	// 剩余的用户数
	unfinishedVURes = int64(numberOfVU) - changeTotalVu - targetTotalVu

	stepDurationSeconds := int64(cache.Parse(ConstDefaultStepDuration)) / int64(time.Second)
	if cache.Parse(ConstDefaultStepDuration)%time.Second != 0 {
		stepDurationSeconds += 1
	}
	if stepDurationSeconds == 0 {
		stepDurationSeconds = 1
	}

	// 将分配的用户数跑完
	for numberOfFinished < numberOfPreviewFinished {
		numberOfFinished += currentRPS
		totalDurationSeconds += 1

		if totalDurationSeconds%stepDurationSeconds == 0 && stepHeight != 0 {
			if stepHeight > 0 && currentRPS < targetRPS {
				currentRPS += stepHeight
			} else if stepHeight < 0 && currentRPS > targetRPS {
				currentRPS += -stepHeight
			}
		}
	}

	// 剩余用户数不足
	if unfinishedVURes <= 0 {
		return time.Duration(totalDurationSeconds) * time.Second, 0
	}

	return time.Duration(totalDurationSeconds) * time.Second, uint32(unfinishedVURes)
}

func calculateSinglePartV2(
	rateLimit *pb.RateLimitV2, numberOfVU uint32, isLastPart bool,
) (stepDuration time.Duration, unfinishedVU uint32) {
	reqStats := calculateStats(
		float64(rateLimit.GetInitialRps()), float64(rateLimit.GetTargetRps()),
		cache.Parse(rateLimit.GetChangeDuration()).Seconds(), cache.Parse(rateLimit.GetTargetDuration()).Seconds(),
		int(numberOfVU), isLastPart,
	)
	if reqStats.RemainingUsers < 0 {
		return time.Duration(reqStats.TotalTime) * time.Second, 0
	}

	return time.Duration(reqStats.TotalTime) * time.Second, uint32(reqStats.RemainingUsers)
}

// RequestStats 结构体用于存储请求的统计信息
type RequestStats struct {
	TotalExecutedRequests int     // 总执行请求数
	RemainingUsers        int     // 剩余未执行请求的用户数
	TotalTime             float64 // 总耗时
}

// calculateStats 函数用于计算处理一定数量用户请求所需总时间以及最终执行的请求数和剩余未执行请求用户数
func calculateStats(
	initialRate, targetRate, rampUpDuration, targetDuration float64, totalUsers int, isLastPart bool,
) RequestStats {
	var executedRequests int   // 已执行的请求数
	var timeElapsed float64    // 已流逝的时间
	currentRate := initialRate // 当前请求速率，初始化为初始速率

	// Ramp up phase：提升阶段
	for timeElapsed < rampUpDuration {
		// 计算当前时间间隔内的请求数
		requestsInInterval := currentRate*(timeElapsed+1) - currentRate*timeElapsed
		executedRequests += int(requestsInInterval)
		// 如果已执行请求数达到总用户请求数，则跳出循环
		if executedRequests >= totalUsers {
			break
		}
		timeElapsed++
		// 逐渐提升当前速率
		currentRate += (targetRate - initialRate) / rampUpDuration
	}

	// Target rate phase：目标速率阶段
	if executedRequests < totalUsers {
		for timeElapsed < rampUpDuration+targetDuration {
			requestsInInterval := targetRate
			executedRequests += int(requestsInInterval)
			timeElapsed++
			// 如果已执行请求数达到总用户请求数，则跳出循环
			if executedRequests >= totalUsers {
				break
			}
		}
	}

	// Continue with target rate if needed：如果需要，继续以目标速率处理请求
	if isLastPart && executedRequests < totalUsers {
		remainingUsers := totalUsers - executedRequests
		timeForRemaining := float64(remainingUsers) / targetRate
		timeElapsed += timeForRemaining
		executedRequests = totalUsers
	}

	timeElapsed = math.Ceil(timeElapsed) // 向上取整

	return RequestStats{
		TotalExecutedRequests: executedRequests,
		RemainingUsers:        totalUsers - executedRequests,
		TotalTime:             timeElapsed,
	}
}

func GetDefaultPerfRateLimitsByTargetEnv(targetEnv pb.TargetEnvironment) *pb.PerfRateLimits {
	switch targetEnv {
	case pb.TargetEnvironment_TE_DEVELOPMENT:
		return &pb.PerfRateLimits{
			Auth: &pb.PerfRateLimits_AuthConfig{
				RateLimits: []*pb.RateLimitV2{
					{
						TargetRps:      constDefaultDevelopmentAuthTargetRPS,
						InitialRps:     constDefaultDevelopmentAuthInitialRPS,
						ChangeDuration: ConstDefaultAuthChangeDuration.String(),
						TargetDuration: "",
					},
				},
				Key: "",
			},
			Heartbeat: &pb.PerfRateLimits_HeartbeatConfig{
				RateLimits: []*pb.RateLimitV2{
					{
						TargetRps:      constDefaultDevelopmentHeartbeatTargetRPS,
						InitialRps:     constDefaultDevelopmentHeartbeatInitialRPS,
						ChangeDuration: ConstDefaultHeartbeatChangeDuration.String(),
						TargetDuration: "",
					},
				},
				Interval: ConstDefaultHeartbeatInterval,
				Key:      "",
			},
		}
	case pb.TargetEnvironment_TE_TESTING:
		return &pb.PerfRateLimits{
			Auth: &pb.PerfRateLimits_AuthConfig{
				RateLimits: []*pb.RateLimitV2{
					{
						TargetRps:      constDefaultTestingAuthTargetRPS,
						InitialRps:     constDefaultTestingAuthInitialRPS,
						ChangeDuration: ConstDefaultAuthChangeDuration.String(),
						TargetDuration: "",
					},
				},
				Key: "",
			},
			Heartbeat: &pb.PerfRateLimits_HeartbeatConfig{
				RateLimits: []*pb.RateLimitV2{
					{
						TargetRps:      constDefaultTestingHeartbeatTargetRPS,
						InitialRps:     constDefaultTestingHeartbeatInitialRPS,
						ChangeDuration: ConstDefaultHeartbeatChangeDuration.String(),
						TargetDuration: "",
					},
				},
				Interval: ConstDefaultHeartbeatInterval,
				Key:      "",
			},
		}
	case pb.TargetEnvironment_TE_STAGING:
		return &pb.PerfRateLimits{
			Auth: &pb.PerfRateLimits_AuthConfig{
				RateLimits: []*pb.RateLimitV2{
					{
						TargetRps:      constDefaultStagingAuthTargetRPS,
						InitialRps:     constDefaultStagingAuthInitialRPS,
						ChangeDuration: ConstDefaultAuthChangeDuration.String(),
						TargetDuration: "",
					},
				},
				Key: "",
			},
			Heartbeat: &pb.PerfRateLimits_HeartbeatConfig{
				RateLimits: []*pb.RateLimitV2{
					{
						TargetRps:      constDefaultStagingHeartbeatTargetRPS,
						InitialRps:     constDefaultStagingHeartbeatInitialRPS,
						ChangeDuration: ConstDefaultHeartbeatChangeDuration.String(),
						TargetDuration: "",
					},
				},
				Interval: ConstDefaultHeartbeatInterval,
				Key:      "",
			},
		}
	case pb.TargetEnvironment_TE_CANARY:
		return &pb.PerfRateLimits{
			Auth: &pb.PerfRateLimits_AuthConfig{
				RateLimits: []*pb.RateLimitV2{
					{
						TargetRps:      constDefaultCanaryAuthTargetRPS,
						InitialRps:     constDefaultCanaryAuthInitialRPS,
						ChangeDuration: ConstDefaultAuthChangeDuration.String(),
						TargetDuration: "",
					},
				},
				Key: "",
			},
			Heartbeat: &pb.PerfRateLimits_HeartbeatConfig{
				RateLimits: []*pb.RateLimitV2{
					{
						TargetRps:      constDefaultCanaryHeartbeatTargetRPS,
						InitialRps:     constDefaultCanaryHeartbeatInitialRPS,
						ChangeDuration: ConstDefaultHeartbeatChangeDuration.String(),
						TargetDuration: "",
					},
				},
				Interval: ConstDefaultHeartbeatInterval,
				Key:      "",
			},
		}
	default:
		return &pb.PerfRateLimits{
			Auth: &pb.PerfRateLimits_AuthConfig{
				RateLimits: defaultAuthRateLimits,
				Key:        "",
			},
			Heartbeat: &pb.PerfRateLimits_HeartbeatConfig{
				RateLimits: defaultHeartbeatRateLimits,
				Interval:   ConstDefaultHeartbeatInterval,
				Key:        "",
			},
		}
	}
}
