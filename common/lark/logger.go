package lark

import (
	"context"

	"github.com/zeromicro/go-zero/core/logx"
)

type Logger struct {
	logx.Logger
}

func NewLogger(ctx context.Context) *Logger {
	return &Logger{
		Logger: logx.WithContext(ctx).WithCallerSkip(1),
	}
}

func (l *Logger) Debug(_ context.Context, v ...any) {
	l.Logger.Debug(v...)
}

func (l *Logger) Info(_ context.Context, v ...any) {
	l.Logger.Info(v...)
}

func (l *Logger) Warn(_ context.Context, v ...any) {
	l.Logger.Warn(v...)
}

func (l *Logger) Error(_ context.Context, v ...any) {
	l.Lo<PERSON>.<PERSON>(v...)
}
