package pkgpuller

import (
	"fmt"
	"io"
	"net/http"
	"regexp"
	"strings"

	"golang.org/x/net/html"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"
)

type TTAndroidPkgPuller struct {
	*BasePkgPuller
}

func (puller *TTAndroidPkgPuller) Pull() (string, error) {
	link, err := puller.fetchDownloadLink()
	if err != nil {
		return link, err
	}
	err = utils.DownloadFromUrl(puller.ctx, link, puller.targetPath)
	if err != nil {
		return link, fmt.Errorf("failed to download the test package, url: %s, error: %+v", link, err)
	}
	return link, err
}

func (puller *TTAndroidPkgPuller) fetchDownloadLink() (string, error) {
	var (
		link string
		url  string = string(TTAndroidPkgQueryURL)
	)
	req, err := http.NewRequestWithContext(puller.ctx, http.MethodGet, url, nil)
	if err != nil {
		return link, fmt.Errorf(
			"failed to new a http request, method: %s, url: %s, error: %+v",
			http.MethodGet, url, err,
		)
	}

	resp, err := http.DefaultClient.Do(req)
	defer func() {
		if resp != nil {
			_, _ = io.Copy(io.Discard, resp.Body)
			_ = resp.Body.Close()
		}
	}()
	if err != nil {
		return link, fmt.Errorf("failed to call a http request, url: %s, error: %+v", url, err)
	} else if resp.StatusCode != http.StatusOK {
		return link, fmt.Errorf(
			"failed to call a http request, url: %s, status code: %s[%d]", url, resp.Status, resp.StatusCode,
		)
	}

	doc, err := html.Parse(resp.Body)
	if err != nil {
		return link, fmt.Errorf("failed to parse the http response html, url: %s, error: %+v", url, err)
	}

	herf := traverseNodesForLink(doc, regexp.MustCompile(TTAndroidPkgLinkRegexp))
	if len(herf) == 0 {
		return link, fmt.Errorf("failed to fetch downlink from document, url: %s", url)
	}

	link = strings.Replace(url, "list", herf, 1)
	return link, nil
}

func traverseNodesForLink(n *html.Node, r *regexp.Regexp) string {
	var href string
	if n == nil || r == nil {
		return href
	}

	if n.Type == html.ElementNode && n.Data == "a" {
		for _, attr := range n.Attr {
			if attr.Key == "href" {
				if r.MatchString(attr.Val) {
					href = attr.Val
				}
			}
		}

		if len(href) > 0 {
			return href
		}
	}

	for c := n.FirstChild; c != nil; c = c.NextSibling {
		href = traverseNodesForLink(c, r)
		if len(href) > 0 {
			break
		}
	}
	return href
}
