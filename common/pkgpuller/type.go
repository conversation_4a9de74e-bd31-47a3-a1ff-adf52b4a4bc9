package pkgpuller

const (
	TTAndroidPkgLinkRegexp = `download\?code=[A-z0-9]+`
)

type AppPkgNameType string

const (
	TTAndroidPkgNameType AppPkgNameType = "com.yiyou.ga"
	TTIOSPkgNameType     AppPkgNameType = "com.yiyou.enterprise.tt"
)

type AppPkgQueryURL string

const (
	TTAndroidPkgQueryURL AppPkgQueryURL = "http://d-internal.ttyuyin.com/d/app/GAClient-master/list"
	TTIOSPkgQueryURL     AppPkgQueryURL = "http://ios.ttyuyin.com:8888/cd/output/package"
)

type AppPkgQueryBody string

const (
	TTIOSPkgQueryBody AppPkgQueryBody = `{"project_name":"tt","callback_url":"http://127.0.0.1:8000/iOS_pkg/"}`
)
