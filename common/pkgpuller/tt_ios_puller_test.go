package pkgpuller

import (
	"context"
	"os"
	"testing"
)

func TestTTIOSPkgPull(t *testing.T) {
	ctx := context.Background()
	targetPath := "/tmp/tt_ent.ipa"
	puller, err := NewAppPkgPuller(ctx, TTIOSPkgNameType, targetPath)
	if err != nil {
		t.<PERSON><PERSON>(err)
	}
	link, err := puller.Pull()
	if err != nil {
		t.<PERSON><PERSON>(err)
	}
	t.Logf("success to download TT ios package, path:%s, link: %s\n", targetPath, link)
	err = os.Remove(targetPath)
	if err != nil {
		t.<PERSON><PERSON>(err)
	}
	t.Logf("success to delete TT ios package, path: %s\n", targetPath)
}
