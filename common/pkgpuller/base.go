package pkgpuller

import (
	"context"
	"fmt"
)

type AppPkgPuller interface {
	Pull() (string, error)
}

type BasePkgPuller struct {
	ctx        context.Context
	targetPath string
}

func NewAppPkgPuller(ctx context.Context, pkgName AppPkgNameType, targetPath string) (AppPkgPuller, error) {
	basePkgPuller := &BasePkgPuller{
		ctx:        ctx,
		targetPath: targetPath,
	}
	switch pkgName {
	case TTAndroidPkgNameType:
		return &TTAndroidPkgPuller{
			basePkgPuller,
		}, nil
	case TTIOSPkgNameType:
		return &TTIOSPkgPuller{
			basePkgPuller,
		}, nil
	default:
		return nil, fmt.Errorf("unsupported package name type: %s", pkgName)
	}
}
