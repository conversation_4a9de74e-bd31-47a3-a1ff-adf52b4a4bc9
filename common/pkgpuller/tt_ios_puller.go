package pkgpuller

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"
)

type TTIOSPkgPuller struct {
	*BasePkgPuller
}

func (puller *TTIOSPkgPuller) Pull() (string, error) {
	link, err := puller.fetchDownloadLink()
	if err != nil {
		return link, err
	}
	err = utils.DownloadFromUrl(puller.ctx, link, puller.targetPath)
	if err != nil {
		return link, fmt.Errorf("failed to download the test package, url: %s, error: %+v", link, err)
	}
	return link, err
}

func (puller *TTIOSPkgPuller) fetchDownloadLink() (string, error) {
	var (
		link string
		url  string = string(TTIOSPkgQueryURL)
	)

	fn := func() error {
		req, err := http.NewRequestWithContext(puller.ctx, http.MethodPost, url, bytes.NewBufferString(string(TTIOSPkgQueryBody)))
		if err != nil {
			return fmt.Errorf(
				"failed to new a http request, method: %s, url: %s, error: %+v",
				http.MethodPost, url, err,
			)
		}

		resp, err := http.DefaultClient.Do(req)
		defer func() {
			if resp != nil {
				_, _ = io.Copy(io.Discard, resp.Body)
				_ = resp.Body.Close()
			}
		}()
		if err != nil {
			return fmt.Errorf("failed to call a http request, url: %s, error: %+v", url, err)
		} else if resp.StatusCode != http.StatusOK {
			return fmt.Errorf(
				"failed to call a http request, url: %s, status code: %s[%d]", url, resp.Status, resp.StatusCode,
			)
		}

		body, err := io.ReadAll(resp.Body)
		if err != nil {
			return fmt.Errorf("failed to read the http response body, url: %s, error: %+v", url, err)
		}

		data := make(map[string]any)
		if err := json.Unmarshal(body, &data); err != nil {
			return fmt.Errorf("failed to unmarshal the http response body, url: %s, body: %s, error: %+v", url, body, err)
		}

		if addrs, ok := data["resp"]; ok {
			addrs := addrs.(map[string]any)
			if addr, ok := addrs["ent_addr"]; ok {
				link = addr.(string)
			}
		}
		return nil
	}

	var err error
	// 企业包正在生成中,请等待1-2分钟
	for i := 0; i < 24; i++ {
		err = fn()
		if len(link) > 0 {
			break
		}
		time.Sleep(time.Second * 5)
	}

	if err != nil {
		return link, err
	} else if len(link) == 0 {
		return link, fmt.Errorf("unable to fetch the download link within 2 minutes")
	}
	return link, nil
}
