package pool

import "sync"

type (
	NewFunc[E any]     func() E
	ReleaseFunc[E any] func(E)

	Pool[E any] struct {
		p *sync.Pool

		n NewFunc[E]
		r ReleaseFunc[E]
	}
)

func NewPool[E any](newF NewFunc[E], releaseF ReleaseFunc[E]) *Pool[E] {
	return &Pool[E]{
		p: &sync.Pool{
			New: func() any {
				return newF()
			},
		},

		n: newF,
		r: releaseF,
	}
}

func (p *Pool[E]) Get() E {
	v := p.p.Get()
	return v.(E)
}

func (p *Pool[E]) Put(e E) {
	if p.r != nil {
		p.r(e)
	}

	p.p.Put(e)
}
