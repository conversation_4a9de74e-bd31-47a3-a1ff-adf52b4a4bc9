package utils

import (
	"fmt"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestGenCaseId(t *testing.T) {
	cid := GenCaseId()
	t.Log(cid)
	assert.Len(t, cid, 21+len(ConstCaseIdPrefix))
	assert.Regexp(t, fmt.Sprintf("^%s", ConstCaseIdPrefix), cid)
}

func TestGenVersion(t *testing.T) {
	v := GenVersion()
	t.Log(v)
	assert.Len(t, v, 31)
	assert.Regexp(t, fmt.Sprintf("^%s", ConstVersionPrefix), v)
}

func TestGenGitConfigID(t *testing.T) {
	cid := GenGitConfigID()
	t.Log(cid)
	assert.Len(t, cid, 21+len(ConstGitConfigIDPrefix))
	assert.Regexp(t, fmt.Sprintf("^%s", ConstGitConfigIDPrefix), cid)
}

func TestGenRuleID(t *testing.T) {
	rid := GenRuleID()
	t.Log(rid)
	assert.Len(t, rid, 21+len(ConstRuleIDPrefix))
	assert.Regexp(t, fmt.Sprintf("^%s", ConstRuleIDPrefix), rid)
}
