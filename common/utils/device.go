package utils

import (
	"fmt"
	"net/url"
	"strconv"
	"strings"
	"time"

	"github.com/electricbubble/gadb"
	gid "github.com/electricbubble/gidevice"
	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/caller"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
)

const (
	strOfFailedToAuthenticate = "failed to authenticate to"

	delayTimeOfADBConnect = 2 * time.Second
)

func ADBConnect(udid, remoteAddress string) (_ string, client *gadb.Client, device *gadb.Device, err error) {
	client, err = gadb.NewClient()
	if err != nil {
		return remoteAddress, nil, nil, errors.Wrapf(err, "failed to create adb client, udid: %s", udid)
	}

	if udid == "" && remoteAddress == "" {
		devices, err := client.ListDevices()
		if err != nil {
			return remoteAddress, nil, nil, errors.Wrap(err, "failed to list devices")
		} else if len(devices) == 0 {
			return remoteAddress, nil, nil, errors.New("not found any devices")
		}

		return remoteAddress, client, devices[0], nil
	}

	var serial string
	if udid != "" {
		serial = udid
	}
	if remoteAddress != "" {
		var (
			host string
			port int
		)
		if strings.Contains(remoteAddress, "://") {
			u, err := url.Parse(remoteAddress)
			if err != nil {
				return remoteAddress, nil, nil, errors.Wrapf(
					err,
					"failed to parse the device remote address, udid: %s, remote_address: %s",
					udid, remoteAddress,
				)
			}
			remoteAddress = u.Host
		}
		hostAndPort := strings.Split(remoteAddress, ":")
		switch len(hostAndPort) {
		case 1:
			host = hostAndPort[0]
			port = constants.ConstPort80
		case 2:
			host = hostAndPort[0]
			port, err = strconv.Atoi(hostAndPort[1])
			if err != nil {
				return remoteAddress, nil, nil, errors.Wrapf(
					err,
					"cannot convert the device remote address port to integer, udid: %s, remote_address: %s, port: %s",
					udid, remoteAddress, hostAndPort[1],
				)
			}
		default:
			return remoteAddress, nil, nil, errors.Errorf(
				"invalid device remote address, udid: %s, remote_address: %s", udid, remoteAddress,
			)
		}

		if err = client.Connect(host, gadb.WithPort(port)); err != nil {
			// ignore the `failed to authenticate to` error because it might have actually connected successfully.
			// if it indeed fails to connect, an error will be returned in the subsequent 'FindDeviceBySerial' call.
			if !strings.Contains(err.Error(), strOfFailedToAuthenticate) {
				return remoteAddress, nil, nil, errors.Wrapf(
					err,
					"failed to connect to the device, udid: %s, remote_address: %s",
					udid, remoteAddress,
				)
			}

			// even if it has connected successfully, there is a possibility of encountering a
			// 'device unauthorized' situation, so we delay here for a bit.
			time.Sleep(delayTimeOfADBConnect)
		}

		serial = remoteAddress
	}

	device, err = client.FindDeviceBySerial(serial)
	if err != nil {
		return remoteAddress, nil, nil, errors.Wrapf(
			err,
			"failed to find the device by remote address, udid: %s, remote_address: %s",
			udid, remoteAddress,
		)
	}

	return remoteAddress, client, device, nil
}

func GIDConnect(udid, remoteAddress string) (_ string, usb gid.Usbmux, device gid.Device, err error) {
	if remoteAddress != "" {
		if strings.Contains(remoteAddress, "://") {
			u, err := url.Parse(remoteAddress)
			if err != nil {
				return remoteAddress, nil, nil, errors.Wrapf(
					err,
					"failed to parse the device remote address, udid: %s, remote_address: %s",
					udid, remoteAddress,
				)
			}
			remoteAddress = u.Host
		}
		hostAndPort := strings.Split(remoteAddress, ":")
		if len(hostAndPort) < 1 {
			return remoteAddress, nil, nil, errors.Errorf(
				"invalid device remote address, udid: %s, remote_address: %s",
				udid, remoteAddress,
			)
		}
		remoteAddress = fmt.Sprintf("%s:%d", hostAndPort[0], constants.ConstUSBMuxPort)
	}

	if err = caller.RetryDo(
		caller.MaxRetryCount, func() error {
			usb, err = gid.NewUsbmuxWithAddress(remoteAddress)
			if err != nil {
				return errors.Wrapf(
					err, "failed to create an usbmux, udid: %s, remote_address: %s", udid, remoteAddress,
				)
			}

			if udid == "" {
				devices, err := usb.Devices()
				if err != nil {
					return errors.Wrap(err, "failed to list devices")
				} else if len(devices) == 0 {
					return errors.New("not found any devices")
				}
				device = devices[0]
			} else {
				device, err = usb.FindDeviceByUDID(udid)
				if err != nil {
					return errors.Wrapf(err, "failed to find the device by udid, udid: %s", udid)
				}
			}

			return nil
		},
	); err != nil {
		return remoteAddress, nil, nil, err
	}

	return remoteAddress, usb, device, nil
}
