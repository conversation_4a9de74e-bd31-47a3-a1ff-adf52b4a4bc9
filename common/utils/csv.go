package utils

import (
	"reflect"

	"github.com/jszwec/csvutil"
	"google.golang.org/protobuf/types/known/structpb"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
)

var (
	StructPBCSVUnmarshaler = csvutil.UnmarshalFunc[*structpb.Struct](
		func(b []byte, s *structpb.Struct) error {
			return protobuf.UnmarshalJSON(b, s)
		},
	)
	ListPBCSVUnmarshaler = csvutil.UnmarshalFunc[*structpb.ListValue](
		func(b []byte, l *structpb.ListValue) error {
			return protobuf.UnmarshalJSON(b, l)
		},
	)

	CSVUnmarshalers = csvutil.NewUnmarshalers(StructPBCSVUnmarshaler, ListPBCSVUnmarshaler)
)

func GetCSVTags(v any) []string {
	var tags []string
	val := reflect.ValueOf(v)
	typ := val.Type()

	for i := 0; i < val.NumField(); i++ {
		field := typ.Field(i)
		csvTag := field.Tag.Get("csv") // 获取 csv 标签
		tags = append(tags, csvTag)
	}
	return tags
}
