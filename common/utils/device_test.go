package utils

import (
	"strings"
	"testing"
)

func TestADBConnect(t *testing.T) {
	type args struct {
		udid          string
		remoteAddress string
	}
	tests := []struct {
		name    string
		args    args
		want    string
		wantErr bool
	}{
		{
			name: "empty udid and empty remote address",
			args: args{
				udid:          "",
				remoteAddress: "",
			},
			want:    "",
			wantErr: false,
		},
		{
			name: "address with scheme",
			args: args{
				udid:          "",
				remoteAddress: "http://**************:5555",
			},
			want:    "**************:5555",
			wantErr: false,
		},
		{
			name: "address without scheme",
			args: args{
				udid:          "",
				remoteAddress: "************:20004",
			},
			want:    "************:20004",
			wantErr: false,
		},
		{
			name: "udid and remote address",
			args: args{
				udid:          "PXUYD22628002359",
				remoteAddress: "************:20004",
			},
			want:    "************:20004",
			wantErr: false,
		},
		{
			name: "not found udid",
			args: args{
				udid:          "LZYTYLZT9HFI6DLN",
				remoteAddress: "",
			},
			want:    "",
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				s, c, d, err := ADBConnect(tt.args.udid, tt.args.remoteAddress)
				if err != nil {
					if !tt.wantErr {
						t.Errorf("ADBConnect() error = %v, wantErr %v", err, tt.wantErr)
					}
					return
				} else if !strings.EqualFold(s, tt.want) {
					t.Errorf("ADBConnect() = %v, want %v", s, tt.want)
					return
				}

				devices, _ := c.ListDevices()
				info := d.DeviceInfo()
				t.Logf("devices: %d", len(devices))
				t.Logf("info: %+v", info)
			},
		)
	}
}

func TestGIDConnect(t *testing.T) {
	type args struct {
		udid          string
		remoteAddress string
	}
	tests := []struct {
		name    string
		args    args
		want    string
		wantErr bool
	}{
		{
			name: "empty udid and empty remote address",
			args: args{
				udid:          "",
				remoteAddress: "",
			},
			want:    "",
			wantErr: false,
		},
		{
			name: "address with scheme",
			args: args{
				udid:          "",
				remoteAddress: "http://************:20007",
			},
			want:    "************:27015",
			wantErr: false,
		},
		{
			name: "address without scheme",
			args: args{
				udid:          "",
				remoteAddress: "************:20007",
			},
			want:    "************:27015",
			wantErr: false,
		},
		{
			name: "udid and remote address",
			args: args{
				udid:          "00008110-000C4CA23C03801E",
				remoteAddress: "http://************:20007",
			},
			want:    "************:27015",
			wantErr: false,
		},
		{
			name: "not found udid",
			args: args{
				udid:          "00008110-000C4CA23C03811E",
				remoteAddress: "http://************:20007",
			},
			want:    "",
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				s, c, d, err := GIDConnect(tt.args.udid, tt.args.remoteAddress)
				if err != nil {
					if !tt.wantErr {
						t.Errorf("GIDConnect() error = %v, wantErr %v", err, tt.wantErr)
					}
					return
				} else if !strings.EqualFold(s, tt.want) {
					t.Errorf("GIDConnect() = %v, want %v", s, tt.want)
					return
				}

				devices, _ := c.Devices()
				info := d.Properties()
				t.Logf("devices: %d", len(devices))
				t.Logf("info: %+v", info)
			},
		)
	}
}
