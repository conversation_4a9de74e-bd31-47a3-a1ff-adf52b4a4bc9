package cache

import (
	"sync"
	"time"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
)

var dc = NewDurationCache()

func Parse(s string) time.Duration {
	return dc.Parse(s)
}

type DurationCache struct {
	cache map[string]time.Duration
	lock  sync.RWMutex
}

func NewDurationCache() *DurationCache {
	return &DurationCache{
		cache: make(map[string]time.Duration, constants.ConstDefaultMakeMapSize),
	}
}

func (c *DurationCache) Parse(s string) time.Duration {
	c.lock.RLock()
	d, ok := c.cache[s]
	c.lock.RUnlock()
	if !ok {
		d, _ = time.ParseDuration(s)
		c.lock.Lock()
		c.cache[s] = d
		c.lock.Unlock()
	}

	return d
}
