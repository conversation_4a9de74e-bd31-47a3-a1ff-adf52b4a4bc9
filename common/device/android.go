package device

import (
	"context"
	"fmt"
	"path/filepath"
	"strconv"
	"strings"
	"time"

	"github.com/dlclark/regexp2"
	"github.com/electricbubble/gadb"
	"github.com/pkg/errors"
	"github.com/shogo82148/androidbinary/apk"
	"github.com/zeromicro/go-zero/core/lang"
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/threading"
	"github.com/zyedidia/generic"
	"github.com/zyedidia/generic/set"
	"go.uber.org/multierr"

	gu2 "gitlab.ttyuyin.com/TestDevelopment/go-uiautomator2"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	commonpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/utils"
)

const (
	basePathOnAndroid   = "/data/local/tmp"
	sdcardPathOnAndroid = "/sdcard"

	groupNameOfX            = "x"
	groupNameOfY            = "y"
	groupNameOfWidth        = "width"
	groupNameOfHeight       = "height"
	groupNameOfDensity      = "density"
	groupNameOfOrientation  = "orientation"
	groupNameOfPackageName  = "package_name"
	groupNameOfActivityName = "activity_name"
	groupNameOfPid          = "pid"
	groupNameOfMethodID     = "method_id"

	strOfMonkeyAborted         = "monkey aborted"
	strOfNoSuchFileOrDirectory = "No such file or directory"
	strOfTrue                  = "true"
	strOfScreenStateOn         = "SCREEN_STATE_ON"

	commandOfGetWindowSize         = "wm size"
	commandOfGetDisplay            = "dumpsys display"
	commandOfGetWindow             = "dumpsys window"
	commandOfGetSurfaceFlinger     = "dumpsys SurfaceFlinger"
	commandOfGetInput              = "dumpsys input"
	commandOfGetWindowDisplays     = "dumpsys window displays"
	commandOfInstallApp            = "pm install -r -t %s"
	commandOfUninstallApp          = "pm uninstall %s"
	commandOfGetVersion            = "dumpsys package %s | grep versionName"
	commandOfLaunchApp             = "monkey -p %s -c android.intent.category.LAUNCHER 1"
	commandOfLaunchAppWithActivity = "am start -n %s/%s" //nolint: gosec
	commandOfStopApp               = "am force-stop %s"
	commandOfGetTopActivity        = "dumpsys activity top"
	commandOfGetWindowPolicy       = "dumpsys window policy"
	commandOfGetWifiOn             = "settings get global wifi_on"
	commandOfEnableWifi            = "svc wifi enable"
	commandOfDisableWifi           = "svc wifi disable"
	commandOfGetInputMethod        = "dumpsys input_method"
	commandOfEnableIME             = "ime enable %s"
	commandOfDisableIME            = "ime disable %s"
	commandOfSetIME                = "ime set %s"
	commandOfResetIME              = "ime reset"
	commandOfListPackages          = "pm list packages"
	commandOfScreenshotByADB       = "screencap -p %s"

	commandOfBackKeyEvent   = "input keyevent 4"   // https://developer.android.com/reference/android/view/KeyEvent.html#KEYCODE_BACK
	commandOfPowerKeyEvent  = "input keyevent 26"  // https://developer.android.com/reference/android/view/KeyEvent.html#KEYCODE_POWER
	commandOfMenuKeyEvent   = "input keyevent 82"  // https://developer.android.com/reference/android/view/KeyEvent.html#KEYCODE_MENU
	commandOfWakeupKeyEvent = "input keyevent 224" // https://developer.android.com/reference/android/view/KeyEvent.html#KEYCODE_WAKEUP
)

var (
	windowSizeRE = regexp2.MustCompile(
		fmt.Sprintf(`(?<%s>\d+)x(?<%s>\d+)\s*$`, groupNameOfWidth, groupNameOfHeight), regexp2.None,
	)
	physicalDisplayRE = regexp2.MustCompile(
		fmt.Sprintf(
			`.*PhysicalDisplayInfo{(?<%s>\d+) x (?<%s>\d+), .*, density (?<%s>[\d.]+).*`,
			groupNameOfWidth, groupNameOfHeight, groupNameOfDensity,
		),
		regexp2.None,
	)
	unrestrictedScreenRE = regexp2.MustCompile(
		fmt.Sprintf(
			`\s*mUnrestrictedScreen=\((?<%s>\d+),(?<%s>\d+)\) (?<%s>\d+)x(?<%s>\d+)`,
			groupNameOfX, groupNameOfY, groupNameOfWidth, groupNameOfHeight,
		), regexp2.None,
	)
	displayWHRE = regexp2.MustCompile(
		fmt.Sprintf(`\s*DisplayWidth=(?<%s>\d+) *DisplayHeight=(?<%s>\d+)`, groupNameOfWidth, groupNameOfHeight),
		regexp2.None,
	)
	orientationRE = regexp2.MustCompile(
		fmt.Sprintf(`orientation=(?<%s>\d+)`, groupNameOfOrientation), regexp2.None,
	)
	surfaceOrientationRE = regexp2.MustCompile(
		fmt.Sprintf(`SurfaceOrientation:\s+(?<%s>\d+)`, groupNameOfOrientation), regexp2.None,
	)
	displayFramesRE = regexp2.MustCompile(
		fmt.Sprintf(`DisplayFrames.*r=(?<%s>\d+)`, groupNameOfOrientation), regexp2.None,
	)
	activityRE = regexp2.MustCompile(
		fmt.Sprintf(
			`\s*ACTIVITY (?<%s>[A-Za-z0-9_.$]+)/(?<%s>[A-Za-z0-9_.$]+) \w+ pid=(?<%s>\d+)`,
			groupNameOfPackageName, groupNameOfActivityName, groupNameOfPid,
		), regexp2.None,
	)
	screenOnRE    = regexp2.MustCompile("mScreenOnFully=(true|false)", regexp2.None)
	screenStateRE = regexp2.MustCompile("screenState=(SCREEN_STATE_ON|SCREEN_STATE_OFF)", regexp2.None)
	lockScreenRE  = regexp2.MustCompile(
		"(?:mShowingLockscreen|isStatusBarKeyguard|showing)=(true|false)", regexp2.None,
	)
	currentMethodIdRE = regexp2.MustCompile(
		fmt.Sprintf("mCurMethodId=(?<%s>[-_./\\w]+)", groupNameOfMethodID), regexp2.None,
	)
	methodShownRE           = regexp2.MustCompile("mInputShown=(true|false)", regexp2.None)
	manualConfirmationItems = []*manualConfirmationItem{
		{
			action: func(device *AndroidDevice, driver *gu2.Driver) {
				name := "已了解应用的风险检测结果"
				element := driver.FindElementBySelectorOptions(gu2.ByText(name))
				if element == nil {
					return
				} else if ok, err := element.Exist(); err != nil || !ok {
					return
				}

				info, err := element.Info()
				if err != nil {
					return
				}

				if info.Checkable && info.Checked {
					return
				}

				if err = element.Click(gu2.WithTimeout(time.Second)); err != nil {
					return
				}

				device.Debugf("click the element successfully, serial: %s, text: %s", device.serial, name)
			},
		},
		{
			action: func(device *AndroidDevice, driver *gu2.Driver) {
				device.defaultManualConfirmationAction(driver, "允许安装", gu2.ByText("允许安装"))
			},
		},
		{
			action: func(device *AndroidDevice, driver *gu2.Driver) {
				device.defaultManualConfirmationAction(driver, "继续安装", gu2.ByText("继续安装"))
			},
		},
		{
			action: func(device *AndroidDevice, driver *gu2.Driver) {
				device.defaultManualConfirmationAction(driver, "安装", gu2.ByText("安装"))
			},
		},
		{
			action: func(device *AndroidDevice, driver *gu2.Driver) {
				device.defaultManualConfirmationAction(driver, "确定", gu2.ByText("确定"))
			},
		},
		{
			action: func(device *AndroidDevice, driver *gu2.Driver) {
				device.defaultManualConfirmationAction(driver, "完成", gu2.ByText("完成"))
			},
		},
	}

	_ IDevice = (*AndroidDevice)(nil)
)

type (
	manualConfirmationItem struct {
		action func(*AndroidDevice, *gu2.Driver)
	}

	AndroidDevice struct {
		logx.Logger
		ctx context.Context

		deviceType            commonpb.DeviceType
		serial, remoteAddress string

		client *gadb.Client
		device *gadb.Device

		displayInfo *DisplayInfo
	}
)

func NewAndroidDevice(
	ctx context.Context, deviceType commonpb.DeviceType, serial, remoteAddress string,
) (*AndroidDevice, error) {
	connectHost, client, device, err := utils.ADBConnect(serial, remoteAddress)
	if err != nil {
		return nil, err
	}

	if serial == "" {
		serial = device.Serial()
	}

	d := &AndroidDevice{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,

		deviceType:    deviceType,
		serial:        serial,
		remoteAddress: connectHost,

		client: client,
		device: device,
	}

	d.displayInfo, err = d.DisplayInfo()
	if err != nil {
		return nil, err
	}

	return d, nil
}

func (d *AndroidDevice) Close() (err error) {
	return err
}

func (d *AndroidDevice) DeviceType() commonpb.DeviceType {
	return d.deviceType
}

func (d *AndroidDevice) PlatformType() commonpb.PlatformType {
	return commonpb.PlatformType_ANDROID
}

func (d *AndroidDevice) UDID() string {
	return d.serial
}

func (d *AndroidDevice) RemoteAddress() string {
	return d.remoteAddress
}

func (d *AndroidDevice) ADB() *gadb.Device {
	return d.device
}

func (d *AndroidDevice) DisplayInfo() (*DisplayInfo, error) {
	var err error
	for _, item := range []*findAndMatch{
		{
			cmd: commandOfGetWindowSize,
			re:  windowSizeRE,
		},
		{
			cmd: commandOfGetDisplay,
			re:  physicalDisplayRE,
		},
		{
			cmd: commandOfGetWindow,
			re:  unrestrictedScreenRE,
		},
		{
			cmd: commandOfGetWindow,
			re:  displayWHRE,
		},
	} {
		di, e := d.getDisplayInfo(item)
		if e != nil {
			err = multierr.Append(err, e)
			continue
		}

		return di, nil
	}

	return nil, err
}

func (d *AndroidDevice) getDisplayInfo(fm *findAndMatch) (*DisplayInfo, error) {
	output, err := d.device.RunShellCommand(fm.cmd)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to get the display info, serial: %s", d.serial)
	}

	match, err := fm.re.FindStringMatch(output)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to search for the Regexp, serial: %s, output: %s", d.serial, output)
	} else if match == nil {
		return nil, errors.Errorf("no content matching the Regexp was found, serial: %s, output: %s", d.serial, output)
	}

	group := match.GroupByName(groupNameOfWidth)
	if group == nil {
		return nil, errors.Errorf("failed to get the width, serial: %s, match: %s", d.serial, match.String())
	}
	width, err := strconv.Atoi(group.String())
	if err != nil {
		return nil, errors.Wrapf(
			err, "failed to convert the width to integer, serial: %s, match: %s, group: %s",
			d.serial, match.String(), group.String(),
		)
	}

	group = match.GroupByName(groupNameOfHeight)
	if group == nil {
		return nil, errors.Errorf("failed to get the height, serial: %s, match: %s", d.serial, match.String())
	}
	height, err := strconv.Atoi(group.String())
	if err != nil {
		return nil, errors.Wrapf(
			err, "failed to convert the height to integer, serial: %s, match: %s, group: %s",
			d.serial, match.String(), group.String(),
		)
	}

	return &DisplayInfo{
		Width:  width,
		Height: height,
	}, nil
}

func (d *AndroidDevice) Orientation() (int, error) {
	var err error
	for _, item := range []*findAndMatch{
		{
			cmd: commandOfGetSurfaceFlinger,
			re:  orientationRE,
		},
		{
			cmd: commandOfGetInput,
			re:  surfaceOrientationRE,
		},
		{
			cmd: commandOfGetWindowDisplays,
			re:  displayFramesRE,
		},
	} {
		ori, e := d.getOrientation(item)
		if e != nil {
			err = multierr.Append(err, e)
			continue
		}

		return ori, nil
	}

	return 0, err
}

func (d *AndroidDevice) getOrientation(fm *findAndMatch) (int, error) {
	output, err := d.device.RunShellCommand(fm.cmd)
	if err != nil {
		return 0, errors.Wrapf(err, "failed to get the orientation, serial: %s", d.serial)
	}

	match, err := fm.re.FindStringMatch(output)
	if err != nil {
		return 0, errors.Wrapf(err, "failed to search for the Regexp, serial: %s, output: %s", d.serial, output)
	} else if match == nil {
		return 0, errors.Errorf("no content matching the Regexp was found, serial: %s, output: %s", d.serial, output)
	}

	group := match.GroupByName(groupNameOfOrientation)
	if group == nil {
		return 0, errors.Errorf("failed to get the orientation, serial: %s, match: %s", d.serial, match.String())
	}
	orientation, err := strconv.Atoi(group.String())
	if err != nil {
		return 0, errors.Wrapf(
			err, "failed to convert the orientation to integer, serial: %s, match: %s, group: %s",
			d.serial, match.String(), group.String(),
		)
	}

	return orientation, nil
}

func (d *AndroidDevice) IsExists(filepath string) bool {
	output, err := d.device.RunShellCommand("ls", filepath)
	if err != nil {
		return false
	}

	return !strings.Contains(output, strOfNoSuchFileOrDirectory)
}

func (d *AndroidDevice) AppInstall(appPath string) error {
	var (
		needToReinstall, needToInstall bool

		driver *gu2.Driver
		exitCh chan lang.PlaceholderType
	)

	defer func() {
		if exitCh != nil {
			close(exitCh)
		}
		if driver != nil {
			_ = driver.Close()
		}
	}()

	info, err := apk.OpenFile(appPath)
	if err != nil {
		return errors.Wrapf(err, "failed to open the apk file, serial: %s, file: %s", d.serial, appPath)
	}

	packageName := info.PackageName()
	packages, err := d.ListPackages(WithFilterOfPackageName(packageName))
	if err != nil {
		return err
	}

	var cmd, output string
	if packages.Size() != 0 {
		cmd = fmt.Sprintf(commandOfGetVersion, packageName)
		output, err = d.device.RunShellCommand(cmd)
		if err != nil {
			return errors.Wrapf(
				err, "failed to get the version of the app, serial: %s, package_name: %s", d.serial, packageName,
			)
		}

		source := strings.TrimSpace(output)
		source = strings.TrimPrefix(source, "versionName=")
		target := info.Manifest().VersionName

		if !strings.EqualFold(source, target) {
			d.Infof(
				"need to reinstall the app, serial: %s, file: %s, package_name: %s, version: %s => %s",
				d.serial, appPath, info.PackageName(), source, target,
			)
			needToInstall = true
			needToReinstall = true
		}
	} else {
		needToInstall = true
	}

	if needToInstall {
		if needToReinstall {
			cmd = fmt.Sprintf(commandOfUninstallApp, packageName)
			if _, err = d.device.RunShellCommand(cmd); err != nil {
				return errors.Wrapf(
					err, "failed to uninstall the app, serial: %s, package_name: %s", d.serial, packageName,
				)
			}
		}

		pathOnDevice := filepath.Join(basePathOnAndroid, fmt.Sprintf("%s.apk", info.PackageName()))
		if err = d.device.PushFile(appPath, pathOnDevice); err != nil {
			return errors.Wrapf(
				err,
				"failed to push apk file to the device, serial: %s, file: %s, path: %s",
				d.serial, appPath, pathOnDevice,
			)
		}

		// if the device is an Android real phone, then try to check whether
		// it requires manual confirmation when installing an app.
		if d.deviceType == commonpb.DeviceType_REAL_PHONE {
			driver, err = gu2.NewDriver(d.device)
			if err != nil {
				d.Errorf("failed to new uiautomator2 driver, serial: %s, error: %+v", d.serial, err)
			} else {
				exitCh = make(chan lang.PlaceholderType)
				threading.GoSafeCtx(
					d.ctx, func() {
						d.appInstallWithManualConfirmation(driver, exitCh)
					},
				)
			}
		}

		cmd = fmt.Sprintf(commandOfInstallApp, pathOnDevice)
		output, err = d.device.RunShellCommand(cmd)
		if err != nil {
			return errors.Wrapf(err, "failed to run the shell command, serial: %s, command: %q", d.serial, cmd)
		}

		if d.deviceType == commonpb.DeviceType_REAL_PHONE {
			// sleep for 3 seconds to avoid missing the pop-up window after installing the app.
			time.Sleep(3 * time.Second)
		}
		d.Infof(
			"succeed to install the app, serial: %s, file: %s, package_name: %s, version: %s, command: %q, result: %s",
			d.serial, appPath, info.PackageName(), info.Manifest().VersionName, cmd, output,
		)
	} else {
		d.Debugf(
			"the app has been installed, serial: %s, file: %s, package_name: %s, version: %s",
			d.serial, appPath, info.PackageName(), info.Manifest().VersionName,
		)
	}

	return nil
}

func (d *AndroidDevice) defaultManualConfirmationAction(driver *gu2.Driver, name string, selector gu2.SelectorOption) {
	element := driver.FindElementBySelectorOptions(selector)
	if element == nil {
		return
	} else if ok, err := element.Exist(); err != nil || !ok {
		return
	} else if err = element.Click(gu2.WithTimeout(time.Second)); err != nil {
		return
	}

	d.Debugf("click the element successfully, serial: %s, name: %s", d.serial, name)
}

func (d *AndroidDevice) appInstallWithManualConfirmation(driver *gu2.Driver, exitCh <-chan lang.PlaceholderType) {
	for {
		select {
		case <-d.ctx.Done():
			d.Errorf("got a done signal while installing the app, serial: %s, error: %+v", d.serial, d.ctx.Err())
			return
		case <-exitCh:
			d.Debugf("got an exit signal while installing the app, serial: %s", d.serial)
			return
		default:
			for _, item := range manualConfirmationItems {
				if item == nil || item.action == nil {
					continue
				}

				item.action(d, driver)
			}
			//for _, text := range manualConfirmationTexts {
			//	element := driver.FindElementBySelectorOptions(gu2.ByText(text))
			//	if element == nil {
			//		continue
			//	} else if ok, err := element.Exist(); err != nil || !ok {
			//		continue
			//	} else if err = element.Click(gu2.WithTimeout(time.Second)); err != nil {
			//		continue
			//	}
			//
			//	d.Debugf("click th element successfully, serial: %s, text: %s", d.serial, text)
			//	break
			//}

			time.Sleep(time.Second)
		}
	}
}

func (d *AndroidDevice) AppUninstall(packageName string) error {
	packages, err := d.ListPackages(WithFilterOfPackageName(packageName))
	if err != nil {
		return err
	}

	if packages.Size() != 0 {
		cmd := fmt.Sprintf(commandOfUninstallApp, packageName)
		output, err := d.device.RunShellCommand(cmd)
		if err != nil {
			return errors.Wrapf(err, "failed to uninstall the app, serial: %s, package_name: %s", d.serial, packageName)
		}

		d.Infof("succeed to uninstall the app, serial: %s, package_name: %s, result: %s", d.serial, packageName, output)
	} else {
		d.Debugf("the app has not been installed, serial: %s, package_name: %s", d.serial, packageName)
	}

	return nil
}

func (d *AndroidDevice) AppLaunch(packageName string, args ...string) error {
	var launchableActivity string

	cmd := fmt.Sprintf(commandOfLaunchApp, packageName)
	if len(args) != 0 {
		launchableActivity = args[0]
		cmd = fmt.Sprintf(commandOfLaunchAppWithActivity, packageName, launchableActivity)
	}

	output, err := d.device.RunShellCommand(cmd)
	if err != nil {
		return errors.Wrapf(
			err, "failed to launch the app, serial: %s, package_name: %s, activity: %s",
			d.serial, packageName, launchableActivity,
		)
	}

	output = strings.TrimSpace(output)
	if strings.Contains(output, strOfMonkeyAborted) {
		return errors.Errorf(
			"failed to launch the app, serial: %s, package_name: %s, activity: %s, result: %s",
			d.serial, packageName, launchableActivity, output,
		)
	}

	d.Infof(
		"succeed to launch the app, serial: %s, package_name: %s, activity: %s, result: %s",
		d.serial, packageName, launchableActivity, output,
	)
	return nil
}

func (d *AndroidDevice) AppStop(packageName string) error {
	cmd := fmt.Sprintf(commandOfStopApp, packageName)
	output, err := d.device.RunShellCommand(cmd)
	if err != nil {
		return errors.Wrapf(err, "failed to stop the app, serial: %s, package_name: %s", d.serial, packageName)
	}

	d.Infof("succeed to stop the app, serial: %s, package_name: %s, result: %s", d.serial, packageName, output)
	return nil
}

func (d *AndroidDevice) TopActivity() (*Activity, error) {
	output, err := d.device.RunShellCommand(commandOfGetTopActivity)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to get the top activity, serial: %s", d.serial)
	}

	match, err := activityRE.FindStringMatch(output)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to match the top activity, serial: %s, output: %s", d.serial, output)
	}

	activities := make([]*Activity, 0, constants.ConstDefaultMakeSliceSize)
	fn := func() error {
		defer func() {
			match, _ = activityRE.FindNextMatch(match)
		}()

		group := match.GroupByName(groupNameOfPackageName)
		if group == nil {
			return errors.Errorf(
				"failed to get the package name from the top activity, serial: %s, match: %s", d.serial, match.String(),
			)
		}
		packageName := group.String()

		group = match.GroupByName(groupNameOfActivityName)
		if group == nil {
			return errors.Errorf(
				"failed to get the activity name from the top activity, serial: %s, match: %s",
				d.serial, match.String(),
			)
		}
		activityName := group.String()

		group = match.GroupByName(groupNameOfPid)
		if group == nil {
			return errors.Errorf(
				"failed to get the pid from the top activity, serial: %s, match: %s", d.serial, match.String(),
			)
		}
		pid, err := strconv.ParseInt(group.String(), 10, 64)
		if err != nil {
			return errors.Wrapf(
				err,
				"failed to parse the pid from the top activity, serial: %s, match: %s, group: %s",
				d.serial, match.String(), group.String(),
			)
		}

		activities = append(
			activities, &Activity{
				PackageName:  packageName,
				ActivityName: activityName,
				Pid:          pid,
			},
		)
		return nil
	}
	for match != nil {
		if err = fn(); err != nil {
			break
		}
	}
	if err != nil {
		return nil, errors.Wrapf(err, "failed to get the top activity, serial: %s, output: %s", d.serial, output)
	} else if len(activities) == 0 {
		return nil, errors.Errorf("not found any activities, serial: %s, output: %s", d.serial, output)
	}

	return activities[len(activities)-1], nil
}

func (d *AndroidDevice) PressPower() {
	_, _ = d.device.RunShellCommand(commandOfPowerKeyEvent)
}

func (d *AndroidDevice) PressBack() {
	_, _ = d.device.RunShellCommand(commandOfBackKeyEvent)
}

func (d *AndroidDevice) IsScreenOn() bool {
	output, err := d.device.RunShellCommand(commandOfGetWindowPolicy)
	if err != nil {
		return false
	}

	match, err := screenOnRE.FindStringMatch(output)
	if err == nil && match != nil {
		if group := match.GroupByNumber(1); group != nil {
			return group.String() == strOfTrue
		}
	}

	match, err = screenStateRE.FindStringMatch(output)
	if err == nil && match != nil {
		if group := match.GroupByNumber(1); group != nil {
			return group.String() == strOfScreenStateOn
		}
	}

	return false
}

func (d *AndroidDevice) Wakeup() {
	_, _ = d.device.RunShellCommand(commandOfWakeupKeyEvent)
}

func (d *AndroidDevice) IsLocked() bool {
	output, err := d.device.RunShellCommand(commandOfGetWindowPolicy)
	if err != nil {
		return false
	}

	match, err := lockScreenRE.FindStringMatch(output)
	if err == nil && match != nil {
		if group := match.GroupByNumber(1); group != nil {
			return group.String() == strOfTrue
		}
	}

	return false
}

// Unlock the screen
// NOTE: might not work on all devices
func (d *AndroidDevice) Unlock() {
	_, _ = d.device.RunShellCommand(commandOfMenuKeyEvent)
}

func (d *AndroidDevice) IsWifiOn() bool {
	output, err := d.device.RunShellCommand(commandOfGetWifiOn)
	if err != nil {
		return false
	}

	return strings.TrimSpace(output) == "1"
}

func (d *AndroidDevice) SetWifi(enable bool) error {
	var (
		action, output string
		err            error
	)

	if enable {
		action = "enable"
		output, err = d.device.RunShellCommand(commandOfEnableWifi)
	} else {
		action = "disable"
		output, err = d.device.RunShellCommand(commandOfDisableWifi)
	}
	if err != nil {
		return errors.Wrapf(err, "failed to set wifi to %s, serial: %s, output: %s", action, d.serial, output)
	}

	return nil
}

func (d *AndroidDevice) CurrentIME() (method string, shown bool, err error) {
	output, err := d.device.RunShellCommand(commandOfGetInputMethod)
	if err != nil {
		return "", false, errors.Wrapf(err, "failed to get the input method, serial: %s", d.serial)
	}

	match, err := currentMethodIdRE.FindStringMatch(output)
	if err != nil {
		return "", false, errors.Wrapf(
			err, "failed to match the current method id, serial: %s, output: %s", d.serial, output,
		)
	}

	group := match.GroupByName(groupNameOfMethodID)
	if group == nil {
		return "", false, errors.Errorf(
			"failed to get the current method id from the input method, serial: %s, match: %s", d.serial,
			match.String(),
		)
	}
	method = group.String()

	match, err = methodShownRE.FindStringMatch(output)
	if err == nil && match != nil {
		if group = match.GroupByNumber(1); group != nil {
			shown = group.String() == strOfTrue
		}
	}

	return method, shown, nil
}

func (d *AndroidDevice) SetIME(method string, enable bool) error {
	var (
		output string
		err    error
	)

	if enable {
		if output, err = d.device.RunShellCommand(fmt.Sprintf(commandOfEnableIME, method)); err != nil {
			return errors.Wrapf(
				err, "failed to enable the input method, serial: %s, method: %s, output: %s", d.serial, method, output,
			)
		}

		if output, err = d.device.RunShellCommand(fmt.Sprintf(commandOfSetIME, method)); err != nil {
			return errors.Wrapf(
				err, "failed to set the input method, serial: %s, method: %s, output: %s", d.serial, method, output,
			)
		}
	} else {
		if output, err = d.device.RunShellCommand(fmt.Sprintf(commandOfDisableIME, method)); err != nil {
			return errors.Wrapf(
				err, "failed to disable the input method, serial: %s, method: %s, output: %s", d.serial, method, output,
			)
		}

		if output, err = d.device.RunShellCommand(commandOfResetIME); err != nil {
			return errors.Wrapf(
				err, "failed to reset the input method, serial: %s, output: %s", d.serial, output,
			)
		}
	}

	return nil
}

type (
	ListPackagesOption func(*listPackagesOptions)

	listPackagesOptions struct {
		OnlySystemPackages     bool
		OnlyThirdPartyPackages bool
		FilterOfPackageName    string
	}
)

func WithOnlySystemPackages() ListPackagesOption {
	return func(o *listPackagesOptions) {
		o.OnlySystemPackages = true
	}
}

func WithOnlyThirdPartyPackages() ListPackagesOption {
	return func(o *listPackagesOptions) {
		o.OnlyThirdPartyPackages = true
	}
}

func WithFilterOfPackageName(filter string) ListPackagesOption {
	return func(o *listPackagesOptions) {
		o.FilterOfPackageName = filter
	}
}

func (d *AndroidDevice) ListPackages(opts ...ListPackagesOption) (*set.Set[string], error) {
	o := &listPackagesOptions{}
	for _, opt := range opts {
		opt(o)
	}

	cmd := commandOfListPackages
	if o.OnlySystemPackages {
		cmd += " -s"
	}
	if o.OnlyThirdPartyPackages {
		cmd += " -3"
	}
	if o.FilterOfPackageName != "" {
		cmd += " " + o.FilterOfPackageName
	}

	output, err := d.device.RunShellCommand(cmd)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to list the packages, serial: %s", d.serial)
	}

	output = strings.TrimSpace(output)
	lines := strings.Split(output, "\n")
	packages := set.NewHashset(uint64(len(lines)), generic.Equals, generic.HashString)
	for _, line := range lines {
		if line == "" {
			continue
		}

		fields := strings.Split(line, ":")
		if len(fields) < 2 {
			continue
		}

		packages.Put(fields[1])
	}

	return &packages, nil
}

func (d *AndroidDevice) ListThirdPartyPackages() (*set.Set[string], error) {
	return d.ListPackages(WithOnlyThirdPartyPackages())
}

func (d *AndroidDevice) RunShellCommand(cmd string, args ...string) (string, error) {
	return d.device.RunShellCommand(cmd, args...)
}
