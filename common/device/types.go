package device

import (
	"io"

	"github.com/dlclark/regexp2"

	commonpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
)

type IDevice interface {
	io.Closer

	IDeviceInfo
	IDeviceAction
}

type IDeviceInfo interface {
	DeviceType() commonpb.DeviceType
	PlatformType() commonpb.PlatformType
	UDID() string
	RemoteAddress() string
}

type IDeviceAction interface {
	AppInstall(appPath string) error
	AppUninstall(appName string) error
	AppLaunch(appName string, args ...string) error
}

type findAndMatch struct {
	cmd string
	re  *regexp2.Regexp
}

type DisplayInfo struct {
	Width  int `json:"width"`
	Height int `json:"height"`
}

type Activity struct {
	PackageName  string `json:"package_name"`
	ActivityName string `json:"activity_name"`
	Pid          int64  `json:"pid"`
}
