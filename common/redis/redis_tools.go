package redis

import (
	"context"
	"errors"
	"fmt"
	"math/rand"
	"reflect"
	"time"
	"unsafe"

	jsoniter "github.com/json-iterator/go"
	red "github.com/redis/go-redis/v9"
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/stores/redis"

	qetredis "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/redis"
)

var (
	// 例子
	randExampleMin            = -3
	randExampleMax            = 3
	randFloatExampleMin       = 0.05
	randFloatExampleMax       = -0.05
	defaultScanMax            = 100
	defaultScanCount    int64 = 500

	RepeatedTimes    = 5   // 次数
	RepeatedInterval = 200 // 毫秒
	DefaultTime      = -1

	IJson = jsoniter.ConfigCompatibleWithStandardLibrary
)

type (
	RCacheService struct {
		RedisUniversalClient red.UniversalClient
		RedisClient          *redis.Redis
		redisKeyMap          map[string]int
	}

	DelQueryObj struct {
		Name string
		Key  string
	}
)

func NewRCacheService(c redis.RedisConf, redisKeyMap map[string]int) *RCacheService {
	return &RCacheService{
		RedisUniversalClient: qetredis.NewClient(c),
		RedisClient:          redis.MustNewRedis(c),
		redisKeyMap:          redisKeyMap,
	}
}

/*set*/

func (s *RCacheService) GetCache(ctx context.Context, name, key string, obj any) error {
	logx.Infof("RCacheService GetCache , name : %s , key : %s", name, key)
	_, ok := s.redisKeyMap[name]
	if !ok {
		logx.WithContext(ctx).Errorf("RCacheService GetCache failed, error: %s", "没有找到")
		return nil
	}
	k := name + key
	resV, err := GetString(ctx, s.RedisClient, k)
	if err != nil {
		logx.WithContext(ctx).Warnf("RCacheService GetCache failed, error: %s", err)
		return err
	}
	if len(resV) > 0 {
		err = IJson.Unmarshal(StringToBytes(resV), obj)
	}
	return err
}

func (s *RCacheService) SetCache(ctx context.Context, name, key string, value any) error {
	logx.Infof("RCacheService SetCache , name : %s , key : %s", name, key)
	_, ok := s.redisKeyMap[name]
	if !ok {
		logx.WithContext(ctx).Errorf("RCacheService GetCache failed, error: %s", "没有找到")
		return nil
		// return errors.New("redis key name not exist")
	}
	Expire := s.GeyExTimeInt64ByRedisKeyMapForRandomNum(name)

	k := name + key
	if Expire > 0 {
		return SetString(ctx, s.RedisClient, k, value, Expire)
	}
	return SetString(ctx, s.RedisClient, k, value)
}

func (s *RCacheService) IsExists(ctx context.Context, name, key string) (bool, error) {
	_, ok := s.redisKeyMap[name]
	if !ok {
		logx.WithContext(ctx).Errorf("RCacheService IsExists failed, error: %s", "没有找到")
		return false, errors.New("redis key name not exist")
	}
	k := name + key
	b, err := s.RedisClient.ExistsCtx(ctx, k)
	if err != nil {
		logx.WithContext(ctx).Errorf("RCacheService IsExists failed, error: %s", err)
		return false, err
	}
	return b, err
}

func (s *RCacheService) DelKeys(ctx context.Context, name string, keys ...string) error {
	logx.WithContext(ctx).Debugf("RCacheService DelKeys , name:[%s] , key:[%s]", name, keys)
	_, ok := s.redisKeyMap[name]
	if !ok {
		logx.WithContext(ctx).Errorf("RCacheService GetHashCache failed, error: %s", "没有找到")
		return nil
	}
	strings := make([]string, 0, len(keys))
	for _, key := range keys {
		strings = append(strings, name+key)
	}
	err := DelKey(ctx, s.RedisClient, strings...)
	if err != nil {
		logx.WithContext(ctx).Warnf("RCacheService GetHashCache failed, error: %s", err)
		return err
	}
	/*fmt.Println("DelKeys:" + strings2.Join(strings, ","))*/
	return nil
}

// Scan 展示不限制扫描次数
func (s *RCacheService) Scan(ctx context.Context, match string) ([]string, error) {
	// cursor uint64, match string, count int64
	cursor := uint64(0)
	strings := make([]string, 0)
	for {
		result, cursorTmp, err := s.RedisUniversalClient.Scan(ctx, cursor, match, defaultScanCount).Result()
		if err != nil {
			logx.WithContext(ctx).Errorf("RCacheService GetHashCache failed, error: %s", err)
			return nil, err
		}
		strings = append(strings, result...)
		cursor = cursorTmp
		if cursorTmp == 0 {
			break
		}
		time.Sleep(time.Millisecond * 30)
	}
	return strings, nil
}

func GetString(ctx context.Context, con *redis.Redis, key string) (string, error) {
	count := 1
	for {
		result, err := con.GetCtx(ctx, key)
		if err == nil {
			return result, nil
		}

		if err == redis.Nil {
			return "", nil
		}

		count++
		if count > RepeatedTimes {
			logx.WithContext(ctx).Warnf("RCacheService GetCache failed, error: %s", err)
			return "", err
		} else {
			logx.WithContext(ctx).Warnf(
				"GetString请求key=%v数据失败，err = %s, "+
					"发起第%v请求", key, err, count,
			)
			time.Sleep(time.Duration(RepeatedInterval) * time.Millisecond) // 重试间隔
		}
	}
}

func SetString(ctx context.Context, con *redis.Redis, key string, value any, ex ...int) error {
	count := 1
	t := DefaultTime
	if len(ex) > 0 {
		t = ex[0]
	}
	valueMarshal, err := IJson.Marshal(value)
	if err != nil {
		fmt.Println(err)
		return err
	}
	for {
		err := con.SetexCtx(ctx, key, BytesToString(valueMarshal), t)
		if err == nil {
			return nil
		}
		count++
		if count > RepeatedTimes {
			logx.WithContext(ctx).Warnf("RCacheService GetCache failed, error: %s", err)
			return err
		} else {
			logx.WithContext(ctx).Warnf(
				"GetString请求key=%v数据失败，err = %s, "+
					"发起第%v请求", key, err, count,
			)
			time.Sleep(time.Duration(RepeatedInterval) * time.Millisecond) // 重试间隔
		}
	}
}

func Expire(ctx context.Context, con *redis.Redis, key string, t int64) error {
	err := con.ExpireCtx(ctx, key, int(t))
	return err
}

func DelKey(ctx context.Context, con *redis.Redis, key ...string) error {
	_, err := con.DelCtx(ctx, key...)
	return err
}

/*hash*/

func (s *RCacheService) GetHashCache(ctx context.Context, name, key string, obj any) error {
	logx.WithContext(ctx).Debugf("RCacheService GetHashCache , name:[%s] , key:[%s]", name, key)
	_, ok := s.redisKeyMap[name]
	if !ok {
		logx.WithContext(ctx).Errorf("RCacheService GetHashCache failed, error: %s", "没有找到")
		return nil
	}
	k := name + key
	err := GetHashAllForUniversal(ctx, s.RedisUniversalClient, k, obj)
	if err != nil {
		logx.WithContext(ctx).Warnf("RCacheService GetHashCache failed, error: %s", err)
		return err
	}
	return nil
}

func (s *RCacheService) SetHashCache(ctx context.Context, name, key string, value any) error {
	logx.Debugf("RCacheService SetHashCache , name : %s , key : %s", name, key)
	_, ok := s.redisKeyMap[name]
	if !ok {
		logx.WithContext(ctx).Errorf("RCacheService SetHashCache failed, error: %s", "没有找到")
		return nil
	}
	expire := s.GeyExTimeInt64ByRedisKeyMapForRandomNum(name)
	marshal, err := IJson.Marshal(value)
	if err != nil {
		logx.WithContext(ctx).Errorf("RCacheService SetHashCache failed, error: %s", err)
		return err
	}
	v := make(map[string]any)
	err = IJson.Unmarshal(marshal, &v)
	if err != nil {
		logx.WithContext(ctx).Errorf("RCacheService SetHashCache failed, error: %s", err)
		return err
	}
	k := name + key
	if expire > 0 {
		err := HSetHashForUniversal(ctx, s.RedisUniversalClient, k, v)
		if err != nil {
			logx.WithContext(ctx).Errorf("RCacheService SetHashCache failed, error: %s", err)
			return err
		}
		err = Expire(ctx, s.RedisClient, k, int64(expire))
		if err != nil {
			logx.WithContext(ctx).Errorf("RCacheService SetHashCache failed, error: %s", err)
			return err
		}
		return nil
	}
	return HSetHashForUniversal(ctx, s.RedisUniversalClient, k, v)
}

func GetHashAll(ctx context.Context, con *redis.Redis, key string) (map[string]string, error) {
	count := 1
	for {
		result, err := con.HgetallCtx(ctx, key)
		if err == nil {
			return result, nil
		}
		if errors.Is(err, redis.Nil) {
			return nil, nil
		}
		count++
		if count > RepeatedTimes {
			logx.WithContext(ctx).Warnf("GetHash请求key=%v数据失败 err=%s", key, err)
			return nil, err
		} else {
			logx.WithContext(ctx).Errorf(
				"GetHash请求key=%v数据失败，err = %s, "+
					"发起第%v请求", key, err, count,
			)
			time.Sleep(time.Duration(RepeatedInterval) * time.Millisecond) // 重试间隔
		}
	}
}

func GetHashAllForUniversal(ctx context.Context, con red.UniversalClient, key string, des any) error {
	count := 1
	for {
		mapStringStringCmd := con.HGetAll(ctx, key)
		err := mapStringStringCmd.Err()
		if errors.Is(err, redis.Nil) {
			return nil
		}
		err = mapStringStringCmd.Scan(des)
		if err == nil {
			return nil
		}
		count++
		if count > RepeatedTimes {
			logx.WithContext(ctx).Warnf("GetHash请求key=%v数据失败 err=%s", key, err)
			return err
		} else {
			logx.WithContext(ctx).Errorf(
				"GetHash请求key=%v数据失败，err = %s, "+
					"发起第%v请求", key, err, count,
			)
			time.Sleep(time.Duration(RepeatedInterval) * time.Millisecond) // 重试间隔
		}
	}
}

func GetHashLen(ctx context.Context, con *redis.Redis, key string) (int, error) {
	result, err := con.HlenCtx(ctx, key)
	return result, err
}

func GetHash(ctx context.Context, con *redis.Redis, key, field string) (string, error) {
	count := 1
	for {
		result, err := con.HgetCtx(ctx, key, field)
		if err == nil {
			return result, nil
		}

		if errors.Is(err, redis.Nil) {
			return "", nil
		}

		count++
		if count > RepeatedTimes {
			logx.WithContext(ctx).Errorf(fmt.Sprintf("GetHash请求key=%v数据失败 err=%s", key, err))
			return "", err
		} else {
			logx.WithContext(ctx).Errorf(
				fmt.Sprintf(
					"GetHash请求key=%v数据失败，err = %s, "+
						"发起第%v请求", key, err, count,
				),
			)
			time.Sleep(time.Duration(RepeatedInterval) * time.Millisecond) // 重试间隔
		}
	}
}

func GetHashForUniversal(ctx context.Context, con red.UniversalClient, key, field string) (string, error) {
	count := 1
	for {
		result, err := con.HGet(ctx, key, field).Result()
		if err == nil {
			return result, nil
		}

		if errors.Is(err, redis.Nil) {
			return "", nil
		}

		count++
		if count > RepeatedTimes {
			logx.WithContext(ctx).Errorf(fmt.Sprintf("GetHash请求key=%v数据失败 err=%s", key, err))
			return "", err
		} else {
			logx.WithContext(ctx).Errorf(
				fmt.Sprintf(
					"GetHash请求key=%v数据失败，err = %s, "+
						"发起第%v请求", key, err, count,
				),
			)
			time.Sleep(time.Duration(RepeatedInterval) * time.Millisecond) // 重试间隔
		}
	}
}

func SetHash(ctx context.Context, con *redis.Redis, key, field, value string) error {
	count := 1
	for {
		err := con.HsetCtx(ctx, key, field, value)
		if err == nil {
			return nil
		}
		count++
		if count > RepeatedTimes {
			logx.WithContext(ctx).Errorf(fmt.Sprintf("SetHash请求key=%v数据失败 err=%s", key, err))
			return err
		} else {
			logx.WithContext(ctx).Errorf(
				fmt.Sprintf(
					"SetHash请求key=%v数据失败，err = %s, "+
						"发起第%v请求", key, err, count,
				),
			)
			time.Sleep(time.Duration(RepeatedInterval) * time.Millisecond) // 重试间隔
		}
	}
}

func SetHashForUniversal(ctx context.Context, con red.UniversalClient, key, field string, value any) error {
	count := 1
	for {
		_, err := con.HSet(ctx, key, field, value).Result()
		if err == nil {
			return nil
		}
		count++
		if count > RepeatedTimes {
			logx.WithContext(ctx).Errorf(fmt.Sprintf("SetHash请求key=%v数据失败 err=%s", key, err))
			return err
		} else {
			logx.WithContext(ctx).Errorf(
				fmt.Sprintf(
					"SetHash请求key=%v数据失败，err = %s, "+
						"发起第%v请求", key, err, count,
				),
			)
			time.Sleep(time.Duration(RepeatedInterval) * time.Millisecond) // 重试间隔
		}
	}
}

func DelHash(ctx context.Context, con *redis.Redis, key string, field ...string) (b bool, err error) {
	return con.HdelCtx(ctx, key, field...)
}

func DelHashForUniversal(ctx context.Context, con red.UniversalClient, key string, field ...string) (
	num int64, err error,
) {
	result, err := con.HDel(ctx, key, field...).Result()
	if err != nil {
		logx.WithContext(ctx).Errorf(fmt.Sprintf("DelHashForUniversal key=%v数据失败 err=%s", key, err))
		return 0, err
	}

	return result, nil
}

func IncrByHash(ctx context.Context, con *redis.Redis, key, field string, incr int) (b int, err error) {
	b, err = con.HincrbyCtx(ctx, key, field, incr)
	if err != nil {
		logx.WithContext(ctx).Errorf(
			fmt.Sprintf(
				"IncrbyHash, key=%s, field=%s, incr=%v, err=%s", key, field, incr, err,
			),
		)
		return 0, err
	}
	return b, err
}

func HSetHashForUniversal(ctx context.Context, con red.UniversalClient, key string, fields map[string]any) error {
	_, err := con.HSet(ctx, key, fields).Result()
	if err != nil {
		logx.WithContext(ctx).Errorf(fmt.Sprintf("HMSetHash, key=%s, field=%v, err=%s", key, fields, err))
	}
	return err
}

func HMSetHash(ctx context.Context, con *redis.Redis, key string, fields map[string]string) error {
	err := con.HmsetCtx(ctx, key, fields)
	if err != nil {
		logx.WithContext(ctx).Errorf(fmt.Sprintf("HMSetHash, key=%s, field=%v, err=%s", key, fields, err))
	}
	return err
}

/*zset*/

// ZAdds key score1 member1 [score2 member2] 向有序集合添加一个或多个成员，或者更新已存在成员的分数
func (s *RCacheService) ZAdds(ctx context.Context, keyName, key string, fields ...redis.Pair) (bool, error) {
	exp, ok := s.redisKeyMap[keyName]
	if !ok {
		return false, errors.New("redis key name not exist")
	}

	keyResult := keyName + key
	if len(fields) > 0 {
		_, err := s.RedisClient.ZaddsCtx(ctx, keyResult, fields...)
		if err != nil {
			logx.WithContext(ctx).Errorf("RCacheService ZAdds failed, error: %s", err)
			return false, err
		}
		return true, nil
	}
	if exp > 0 {
		err := Expire(ctx, s.RedisClient, keyResult, int64(exp))
		if err != nil {
			logx.WithContext(ctx).Errorf("Redis ZAdds Expire failed", err)
		}
	}
	return false, nil
}

// ZRem key member [member ...] 移除有序集合中的一个或多个成员
func (s *RCacheService) ZRem(ctx context.Context, keyName, key string, field ...any) (bool, error) {
	_, ok := s.redisKeyMap[keyName]
	if !ok {
		return false, errors.New("redis key name not exist")
	}
	keyResult := keyName + key
	_, err := s.RedisClient.ZremCtx(ctx, keyResult, field...)
	if err != nil {
		return false, err
	}
	return true, nil
}

// ZRange key start stop [WITHSCORES]  通过索引区间返回有序集合指定区间内的成员
func (s *RCacheService) ZRange(ctx context.Context, keyName, key string, start, stop int64) ([]string, error) {
	_, ok := s.redisKeyMap[keyName]
	if !ok {
		return make([]string, 0), errors.New("redis key name not exist")
	}
	keyResult := keyName + key
	stringSlice, err := s.RedisClient.ZrangeCtx(ctx, keyResult, start, stop)
	if err != nil {
		logx.WithContext(ctx).Errorf("Redis ZRevRange failed", err)
		return nil, err
	}
	return stringSlice, nil
}

// ZRangeForPage key start stop [WITHSCORES]  通过索引区间返回有序集合指定区间内的成员
func (s *RCacheService) ZRangeForPage(ctx context.Context, keyName, key string, pageNum, pageSize int32) (
	[]string, error,
) {
	keyResult, start, end, err2, done := s.buildRangeValues(ctx, keyName, key, pageNum, pageSize)
	if done {
		return nil, err2
	}
	stringSlice, err := s.RedisClient.ZrangeCtx(ctx, keyResult, int64(start), int64(end))
	if err != nil {
		logx.WithContext(ctx).Errorf("Redis ZRangeForPage failed", err)
		return nil, err
	}
	return stringSlice, nil
}

// ZPopMin key count
func (s *RCacheService) ZPopMin(ctx context.Context, keyName string, count int64) (
	[]string, error,
) {
	_, ok := s.redisKeyMap[keyName]
	if !ok {
		return make([]string, 0), errors.New("redis key name not exist")
	}
	zSlice, err := s.RedisUniversalClient.ZPopMin(ctx, keyName, count).Result()
	if err != nil {
		logx.WithContext(ctx).Errorf("Redis ZPopMin failed", err)
		return nil, err
	}
	strings := make([]string, 0, len(zSlice))
	for _, v := range zSlice {
		s, ok := v.Member.(string)
		if ok {
			strings = append(strings, s)
		}
	}
	return strings, nil
}

// ZPopMin key count
func (s *RCacheService) ZPopMax(ctx context.Context, keyName string, count int64) (
	[]string, error,
) {
	_, ok := s.redisKeyMap[keyName]
	if !ok {
		return make([]string, 0), errors.New("redis key name not exist")
	}
	zSlice, err := s.RedisUniversalClient.ZPopMax(ctx, keyName, count).Result()
	if err != nil {
		logx.WithContext(ctx).Errorf("Redis ZPopMin failed", err)
		return nil, err
	}
	strings := make([]string, 0, len(zSlice))
	for _, v := range zSlice {
		s, ok := v.Member.(string)
		if ok {
			strings = append(strings, s)
		}
	}
	return strings, nil
}

// ZRevRange key start stop [WITHSCORES] 返回有序集中指定区间内的成员，通过索引，分数从高到低
func (s *RCacheService) ZRevRange(ctx context.Context, keyName, key string, start, stop int64) (
	[]string, error,
) {
	_, ok := s.redisKeyMap[keyName]
	if !ok {
		return make([]string, 0), errors.New("redis key name not exist")
	}
	keyResult := keyName + key
	stringSlice, err := s.RedisClient.ZrevrangeCtx(ctx, keyResult, start, stop)
	if err != nil {
		logx.WithContext(ctx).Errorf("Redis ZRevRange failed", err)
		return nil, err
	}
	return stringSlice, nil
}

// ZRevRangeForPage  key start stop [WITHSCORES] 返回有序集中指定区间内的成员，通过索引，分数从高到低
func (s *RCacheService) ZRevRangeForPage(ctx context.Context, keyName, key string, pageNum, pageSize int32) (
	[]string, error,
) {
	keyResult, start, end, err2, done := s.buildRangeValues(ctx, keyName, key, pageNum, pageSize)
	if done {
		return nil, err2
	}
	stringSlice, err := s.RedisClient.ZrevrangeCtx(ctx, keyResult, int64(start), int64(end))
	if err != nil {
		logx.WithContext(ctx).Errorf("Redis ZRevRangeForPage failed", err)
		return nil, err
	}
	return stringSlice, nil
}

// ZRangeWithScores 返回有序集中指定区间内的成员，通过索引，分数从低到高
func (s *RCacheService) ZRangeWithScores(
	ctx context.Context, keyName, key string, start, stop int64,
) []redis.Pair {
	_, ok := s.redisKeyMap[keyName]
	if !ok {
		return make([]redis.Pair, 0)
	}
	keyResult := keyName + key
	list, err := s.RedisClient.ZrangeWithScoresCtx(ctx, keyResult, start, stop)
	if err != nil {
		logx.WithContext(ctx).Errorf("Redis ZRangeWithScores failed", err)
		return make([]redis.Pair, 0)
	}
	return list
}

// ZRangeByScore key count
func (s *RCacheService) ZRangeByScore(
	ctx context.Context, keyName, key, min_, max_ string, offset, count int64,
) ([]string, error) {
	_, ok := s.redisKeyMap[keyName]
	if !ok {
		return make([]string, 0), errors.New("redis key name not exist")
	}
	r := &red.ZRangeBy{
		Min: min_,
		Max: max_,
	}
	if offset > 0 {
		r.Offset = offset
	}
	if count > 0 {
		r.Count = count
	}
	zSlice, err := s.RedisUniversalClient.ZRangeByScore(
		ctx, keyName+key, r,
	).Result()
	if err != nil {
		logx.WithContext(ctx).Errorf("Redis ZPopMin failed", err)
		return nil, err
	}
	return zSlice, nil
}

// ZRangeByScoreWithScores 根据分数范围获取有序集合中的元素及其分数
func (s *RCacheService) ZRangeByScoreWithScores(
	ctx context.Context, keyName, min_, max_ string, offset, count int64,
) ([]red.Z, error) {
	_, ok := s.redisKeyMap[keyName]
	if !ok {
		return make([]red.Z, 0), errors.New("redis key name not exist")
	}
	r := &red.ZRangeBy{
		Min: min_,
		Max: max_,
	}
	if offset > 0 {
		r.Offset = offset
	}
	if count > 0 {
		r.Count = count
	}
	zSlice, err := s.RedisUniversalClient.ZRangeByScoreWithScores(
		ctx, keyName, r,
	).Result()
	if err != nil {
		logx.WithContext(ctx).Errorf("Redis ZPopMin failed", err)
		return nil, err
	}
	return zSlice, nil
}

// ZRevRangeWithScores 返回有序集中指定区间内的成员，通过索引，分数从高到低
func (s *RCacheService) ZRevRangeWithScores(
	ctx context.Context, keyName, key string, start, stop int64,
) []redis.Pair {
	_, ok := s.redisKeyMap[keyName]
	if !ok {
		return make([]redis.Pair, 0)
	}
	keyResult := keyName + key
	list, err := s.RedisClient.ZrevrangeWithScoresCtx(ctx, keyResult, start, stop)
	if err != nil {
		logx.WithContext(ctx).Errorf("Redis ZRevRangeWithScores failed", err)
		return make([]redis.Pair, 0)
	}
	return list
}

// ZCard key 获取有序集合的成员数
func (s *RCacheService) ZCard(ctx context.Context, keyName, key string) (int, error) {
	_, ok := s.redisKeyMap[keyName]
	if !ok {
		return 0, errors.New("redis key name not exist")
	}
	keyResult := keyName + key
	intCmd, err := s.RedisClient.ZcardCtx(ctx, keyResult)
	if err != nil {
		logx.WithContext(ctx).Errorf("Redis ZCard failed", err)
		return 0, err
	}
	return intCmd, nil
}

// ZScore 获取返回有序集中，成员的分数值
func (s *RCacheService) ZScore(ctx context.Context, keyName, key, id string) int64 {
	_, ok := s.redisKeyMap[keyName]
	if !ok {
		return 0
	}
	keyResult := keyName + key
	value, err := s.RedisClient.ZscoreCtx(ctx, keyResult, id)
	if err != nil {
		logx.WithContext(ctx).Errorf("Redis ZScore failed", err)
		return 0
	}
	return value
}

func (s *RCacheService) ZIsExist(ctx context.Context, keyName, key, id string) (bool, error) {
	_, ok := s.redisKeyMap[keyName]
	if !ok {
		return false, nil
	}
	keyName = keyName + key
	_, err := s.RedisClient.ZscoreCtx(ctx, keyName, id)
	if errors.Is(err, redis.Nil) {
		return false, nil
	}
	if err != nil {
		logx.WithContext(ctx).Errorf("Redis ZIsExist failed", err)
		return false, err
	}
	return true, nil
}

// ZIncrby 有序集合中对指定成员的分数加上增量 increment
func (s *RCacheService) ZIncrby(ctx context.Context, keyName, key, sKey string, score int64) error {
	exp, ok := s.redisKeyMap[keyName]
	if !ok {
		return nil
	}
	keyName = keyName + key
	_, err := s.RedisClient.ZincrbyCtx(ctx, keyName, score, sKey)

	if errors.Is(err, redis.Nil) {
		return nil
	}

	if err != nil {
		logx.WithContext(ctx).Errorf("Redis ZIncrby failed", err)
		return err
	}
	if exp > 0 {
		err = Expire(ctx, s.RedisClient, keyName, int64(exp))
		if err != nil {
			logx.WithContext(ctx).Errorf("Redis ZIncrby failed", err)
		}
	}
	return nil
}

// ZScan 展示不限制扫描次数
func (s *RCacheService) ZScan(ctx context.Context, key, match string) ([]string, error) {
	// cursor uint64, match string, count int64
	cursor := uint64(0)
	strings := make([]string, 0)
	for {
		result, cursorTmp, err := s.RedisClient.ZscanCtx(ctx, key, cursor, match, defaultScanCount)
		if err != nil {
			logx.WithContext(ctx).Errorf("Redis ZScan failed", err)
			return nil, err
		}
		strings = append(strings, result...)
		cursor = cursorTmp
		if cursorTmp == 0 {
			break
		}
		time.Sleep(time.Millisecond * 30)
	}
	return strings, nil
}

func (s *RCacheService) buildRangeValues(
	ctx context.Context, keyName, key string, pageNum, pageSize int32,
) (
	string, int32, int32, error, bool,
) {
	_, ok := s.redisKeyMap[keyName]
	if !ok {
		return "", 0, 0, errors.New("redis key name not exist"), true
	}
	exists, err := s.IsExists(ctx, keyName, key)
	if err != nil {
		return "", 0, 0, err, true
	}
	if !exists {
		return "", 0, 0, nil, true
	}
	keyResult := keyName + key
	start := (pageNum - 1) * pageSize
	end := start + pageSize - 1
	return keyResult, start, end, nil, false
}

func (s *RCacheService) buildRangeValuesV2(
	ctx context.Context, keyName, key string, pageNum int32,
	pageSize int32,
) (
	string, int32, int32, error, bool,
) {
	exists, err := s.IsExists(ctx, keyName, key)
	if err != nil {
		return "", 0, 0, err, true
	}
	if !exists {
		return "", 0, 0, nil, true
	}
	keyResult := keyName + key
	start := (pageNum - 1) * pageSize
	end := start + pageSize - 1
	return keyResult, start, end, nil, false
}

// BytesToString converts byte slice to string.
func BytesToString(b []byte) string {
	return *(*string)(unsafe.Pointer(&b))
}

// StringToBytes converts string to byte slice.
func StringToBytes(s string) []byte {
	return *(*[]byte)(unsafe.Pointer(
		&struct {
			string
			Cap int
		}{s, len(s)},
	))
}

// GeyExTimeInt64ByRedisKeyMapForRandomNum 随机正负百分之5的过期时间防止雪崩
func (s *RCacheService) GeyExTimeInt64ByRedisKeyMapForRandomNum(name string) int {
	nameEx := s.redisKeyMap[name]
	// 主动设置过期时间为永久
	if nameEx == -1 {
		return 1
	}
	nameExFloat := float64(nameEx)
	randFloat64ByRegulations := s.RandFloat64ByRegulations()
	tmpExFloat := nameExFloat * randFloat64ByRegulations
	ex := nameExFloat + tmpExFloat
	// 当数据小于1时,初始化一秒过期时间,防止永久缓存
	if ex < 1 {
		return 1
	}
	return int(ex)
}

func (s *RCacheService) RandFloat64ByRegulations() float64 {
	// Seed uses the provided seed value to initialize the generator to a deterministic state.
	rand.Seed(time.Now().UnixNano())
	return s.RandFloatByMinAndMax(randFloatExampleMin, randFloatExampleMax)
}

func (s *RCacheService) RandFloatByMinAndMax(min_, max_ float64) float64 {
	return min_ + rand.Float64()*(max_-min_)
}

func (s *RCacheService) isSliceEmpty(slice any) bool {
	value := reflect.ValueOf(slice)
	if value.Kind() != reflect.Slice {
		return false
	}

	return value.Len() == 0
}
