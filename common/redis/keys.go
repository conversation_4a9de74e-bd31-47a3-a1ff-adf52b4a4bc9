package redis

const (
	ConstRedisNameSpace = "probe:"

	ConstRedisAccountServer              = "account:"
	ConstRedisAccountNotEnoughHandler    = "poolAccountNotEnoughHandler"
	ConstRedisAccountNotEnoughHandlerKey = ConstRedisNameSpace + ConstRedisAccountServer + ConstRedisAccountNotEnoughHandler

	ConstRedisDispatcherServer             = "dispatcher:"
	ConstRedisDispatcherTaskInfo           = "taskInfo"             // 工程ID:任务ID
	ConstRedisDispatcherTaskInfoStatusLock = "taskInfoStatusLock"   // 工程ID:任务ID
	ConstRedisDispatcherTaskInfo7dayLock   = "taskInfo7dayLock"     // 工程ID:任务ID
	ConstRedisDispatcherTaskInfoAll        = "taskInfoZset:all"     // {zset 全部队列} s:创建时间,f:工程ID
	ConstRedisDispatcherTaskInfoRunning    = "taskInfoZset:running" // {zset 进行中} s:创建时间,f:工程ID
	ConstRedisDispatcherTaskInfoArchive    = "taskInfoZset:archive" // {zset 结束} s:创建时间,f:工程ID

	ConstRedisDispatcherTaskInfoKey           = ConstRedisNameSpace + ConstRedisDispatcherServer + ConstRedisDispatcherTaskInfo           //probe:dispatcher:taskInfo:%project_id:%task_id" {hash}
	ConstRedisDispatcherTaskInfoStatusLockKey = ConstRedisNameSpace + ConstRedisDispatcherServer + ConstRedisDispatcherTaskInfoStatusLock //probe:dispatcher:taskInfoStatusLock:%project_id:%task_id" setenxex
	ConstRedisDispatcherTaskInfoAllKey        = ConstRedisNameSpace + ConstRedisDispatcherServer + ConstRedisDispatcherTaskInfoAll        //probe:dispatcher:taskInfoZset:all:%project_id" {zset} s:time field:task_id //当存在新数据刷新更新时间
	ConstRedisDispatcherTaskInfoRunningKey    = ConstRedisNameSpace + ConstRedisDispatcherServer + ConstRedisDispatcherTaskInfoRunning    //probe:dispatcher:taskInfoZset:running:%project_id" {zset} s:time field:task_id //当存在新数据刷新更新时间
	ConstRedisDispatcherTaskInfoArchiveKey    = ConstRedisNameSpace + ConstRedisDispatcherServer + ConstRedisDispatcherTaskInfoArchive    //probe:dispatcher:taskInfoZset:archive:%project_id" {zset} s:time field:task_id //当存在新数据刷新更新时间
	ConstRedisDispatcherTaskInfo7dayLockKey   = ConstRedisNameSpace + ConstRedisDispatcherServer + ConstRedisDispatcherTaskInfo7dayLock   //probe:dispatcher:taskInfoStatusLock:%project_id:%task_id" setenxex

	ConstRedisUIWorkerServer          = "uiworker:"
	ConstRedisUIWorkerTaskExecLock    = "taskExecLock"
	ConstRedisUIWorkerTaskExecLockKey = ConstRedisNameSpace + ConstRedisUIWorkerServer + ConstRedisUIWorkerTaskExecLock //probe:uiworker:taskExecLock:%task_id:%execute_id" setenxex

	ConstRedisPerfWorkerServer          = "perfworker:"
	ConstRedisPerfWorkerTaskExecLock    = "taskExecLock"
	ConstRedisPerfWorkerTaskExecLockKey = ConstRedisNameSpace + ConstRedisPerfWorkerServer + ConstRedisPerfWorkerTaskExecLock //probe:perfworker:taskExecLock:%task_id:%execute_id" setenxex
)

var DispatcherRedisKey = map[string]int{
	ConstRedisDispatcherTaskInfoKey:        2592000, // 默认保存20天
	ConstRedisDispatcherTaskInfoAllKey:     -1,
	ConstRedisDispatcherTaskInfoRunningKey: -1,
	ConstRedisDispatcherTaskInfoArchiveKey: -1,
}
