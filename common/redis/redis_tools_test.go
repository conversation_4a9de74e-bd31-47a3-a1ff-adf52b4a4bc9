package redis

import (
	"context"
	"fmt"
	"testing"

	"github.com/zeromicro/go-zero/core/stores/redis"
)

type man struct {
	Age  int    `json:"age"`
	Name string `json:"name"`
}

func TestTool(t *testing.T) {
	ctx := context.Background()
	rCacheService := NewRCacheService(
		Mock_RedisConf(), map[string]int{
			"test:abc:": 60,
			"test:zset": 0,
			"test:hash": 1800,
		},
	)
	m := man{}
	err := rCacheService.GetCache(ctx, "test:abc:", "1", &m)
	if err != nil {
		fmt.Println(err)
	}
	fmt.Println(m)
	m.Age = 12
	m.Name = "yangbo"
	err = rCacheService.SetCache(ctx, "test:abc:", "1", &m)
	if err != nil {
		fmt.Println(err)
	}

	m1 := man{}
	err = rCacheService.GetCache(ctx, "test:abc:", "1", &m1)
	if err != nil {
		fmt.Println(err)
	}
	fmt.Println(m1)
	/*pairs := []redis.Pair{
		redis.Pair{
			Key:   "0",
			Score: 0,
		},
		redis.Pair{
			Key:   "1",
			Score: 1,
		},
		redis.Pair{
			Key:   "2",
			Score: 2,
		},
		redis.Pair{
			Key:   "3",
			Score: 3,
		},
	}
	adds, err := rCacheService.ZAdds(ctx, "test:zset", "abc", pairs...)
	if err != nil {
		fmt.Println(err)
		return
	}
	fmt.Println(adds)*/
	page, err := rCacheService.ZRangeForPage(ctx, "test:zset", "abc", 2, 2)
	if err != nil {
		fmt.Println(err)
		return
	}
	fmt.Println(page)
	fmt.Println(rCacheService.ZCard(ctx, "test:zset", "abc"))
	fmt.Println(rCacheService.ZScore(ctx, "test:zset", "abc", "1"))
}

func Mock_Redis() *redis.Redis {
	return redis.MustNewRedis(Mock_RedisConf())
}

func Mock_RedisConf() redis.RedisConf {
	return redis.RedisConf{
		/*Host: "auto87.com:6379",
		Type: "node",
		Pass: "Quwan@2020",
		DB:   10,*/
		Host: "***************:6379",
		Type: "node",
		Pass: "",
		DB:   10,
	}
}
