package task

import (
	"context"
	"fmt"
	"strings"
	"testing"
	"time"

	redisgozero "github.com/zeromicro/go-zero/core/stores/redis"

	commonpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/redis"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/rpc/pb"
	managerPb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

var rCacheService *redis.RCacheService

func Mock_RedisConf() redisgozero.RedisConf {
	return redisgozero.RedisConf{
		/*Host: "auto87.com:6379",
		Type: "node",
		Pass: "Quwan@2020",
		DB:   10,*/
		Host: "************:6379",
		Type: "node",
		Pass: "",
		DB:   4,
	}
}

func init() {
	rCacheService = redis.NewRCacheService(
		Mock_RedisConf(), map[string]int{
			"test:abc:":                                  60,
			"test:zset":                                  0,
			"test:hash":                                  1800,
			redis.ConstRedisDispatcherTaskInfoKey:        2592000,
			redis.ConstRedisDispatcherTaskInfoAllKey:     -1,
			redis.ConstRedisDispatcherTaskInfoRunningKey: -1,
			redis.ConstRedisDispatcherTaskInfoArchiveKey: -1,
		},
	)
}

func TestTaskInfo(t *testing.T) {
	ctx := context.Background()

	s := new(InfoItem)
	s.ProjectId = "project|12"
	s.TaskId = "task|029287381"
	s.PlanName = "abc"
	s.PlanId = "2222"
	s.TriggerMode = "AVSB"
	s.TaskExecuteStatus = int8(commonpb.ExecuteStatus_TES_FINISH)
	s.TaskExecutedResult = int8(commonpb.ExecutedResult_TER_FAILURE)
	s.CreateTime = time.Now().Unix()
	s.TotalCase = 123
	err := rCacheService.SetHashCache(ctx, "test:hash", "1", s)
	if err != nil {
		fmt.Println(err)
		return
	}

	w := new(InfoItem)
	err = rCacheService.GetHashCache(ctx, "test:hash", "1", w)
	if err != nil {
		fmt.Println(err)
		return
	}
	fmt.Println(w)
}

func TestCreateTaskInfo(t *testing.T) {
	taskId, projectId := "task|029287381", "project|12"
	ctx := context.Background()
	taskInfoCtl := NewTaskInfoProcessor(rCacheService)

	taskquery := new(InfoItem)
	taskquery.ProjectId = projectId
	taskquery.TaskId = taskId
	err := taskInfoCtl.GetTaskInfo(ctx, taskquery)
	if err != nil {
		fmt.Println(err)
		return
	}
	fmt.Println(taskquery)
	if taskquery.PlanName == "" {
		fmt.Println("create task info")
		task := new(InfoItem)
		task.ProjectId = projectId
		task.TaskId = taskId
		task.PlanName = "abc"
		task.PlanId = "2222"
		task.ExecuteBy = "yb"
		task.TriggerMode = "AVSB"
		task.TaskExecuteStatus = int8(commonpb.ExecuteStatus_TES_FINISH)
		task.TaskExecutedResult = int8(commonpb.ExecutedResult_TER_FAILURE)
		task.CreateTime = time.Now().Unix()
		task.TotalCase = 123
		err = taskInfoCtl.CreateTaskInfo(ctx, task)
		if err != nil {
			fmt.Println(err)
			return
		}
		taskquery2 := new(InfoItem)
		taskquery2.ProjectId = projectId
		taskquery2.TaskId = taskId
		err = taskInfoCtl.GetTaskInfo(ctx, taskquery2)
		if err != nil {
			fmt.Println(err)
			return
		}
		fmt.Println(taskquery2)
	}

	// 更新SuccessCase数据
	fmt.Println("更新")
	num, err := taskInfoCtl.GetSuccessCase(ctx, projectId, taskId)
	if err != nil {
		fmt.Println(err)
		return
	}
	fmt.Println(num)
	err = taskInfoCtl.UpdateSuccessCaseForIncr(ctx, projectId, taskId)
	if err != nil {
		fmt.Println(err)
		return
	}
	err = taskInfoCtl.UpdateSuccessCaseForIncr(ctx, projectId, taskId)
	if err != nil {
		fmt.Println(err)
		return
	}
	num, err = taskInfoCtl.GetSuccessCase(ctx, projectId, taskId)
	if err != nil {
		fmt.Println(err)
		return
	}
	fmt.Println(num)

	// 更新TaskExecuteStatus数据
	res, err := taskInfoCtl.GetTaskExecuteStatus(ctx, projectId, taskId)
	if err != nil {
		fmt.Println(err)
		return
	}
	fmt.Println(res)
	err = taskInfoCtl.UpdateTaskExecuteStatus(ctx, projectId, taskId, commonpb.ExecuteStatus_TES_STOP, 0)
	if err != nil {
		fmt.Println(err)
		return
	}
	res, err = taskInfoCtl.GetTaskExecuteStatus(ctx, projectId, taskId)
	if err != nil {
		fmt.Println(err)
		return
	}
	fmt.Println(res)

	// 更新TaskExecutedResult
	err = taskInfoCtl.UpdateTaskExecutedResult(
		ctx, projectId, taskId, commonpb.ExecutedResult_TER_PANIC, time.Now().UnixMilli(),
	)
	if err != nil {
		fmt.Println(err)
		return
	}

	// 更新UpdateEndedAt
	err = taskInfoCtl.UpdateEndedAt(ctx, projectId, taskId, time.Now().Unix())
	if err != nil {
		fmt.Println(err)
		return
	}
	// 更新StartedAt
	err = taskInfoCtl.UpdateStartedAt(ctx, projectId, taskId, time.Now().Unix())
	if err != nil {
		fmt.Println(err)
		return
	}
	v, err := taskInfoCtl.GetStartedAt(ctx, projectId, taskId)
	if err != nil {
		fmt.Println(err)
		return
	}
	fmt.Println(v)

	// 更新CostTime
	err = taskInfoCtl.UpdateCostTime(ctx, projectId, taskId, int64(time.Minute.Seconds()))
	if err != nil {
		fmt.Println(err)
		return
	}

	taskquery3 := new(InfoItem)
	taskquery3.ProjectId = projectId
	taskquery3.TaskId = taskId
	err = taskInfoCtl.GetTaskInfo(ctx, taskquery3)
	if err != nil {
		fmt.Println(err)
		return
	}
	fmt.Println(taskquery3)
}

func TestTaskInfoFull(t *testing.T) {
	taskId, projectId := "task|98888", "project|1231231"
	ctx := context.Background()
	taskInfoCtl := NewTaskInfoProcessor(rCacheService)
	// 查询任务
	taskquery := new(InfoItem)
	taskquery.ProjectId = projectId
	taskquery.TaskId = taskId
	err := taskInfoCtl.GetTaskInfo(ctx, taskquery)
	if err != nil {
		fmt.Println(err)
		return
	}
	fmt.Println(taskquery)
	if taskquery.PlanName == "" {
		fmt.Println("create task info")
		task := new(InfoItem)
		task.ProjectId = projectId
		task.TaskId = taskId
		task.PlanName = "abc"
		task.PlanId = "2222"
		task.ExecuteBy = "yb"
		task.TriggerMode = "AVSB"
		// task.TaskExecuteStatus = int8(commonpb.ExecuteStatus_TES_FINISH)
		// task.TaskExecutedResult = int8(commonpb.ExecutedResult_TER_FAILURE)
		// task.CreateTime = time.Now().Unix()
		task.TotalCase = 123
		task.TotalSuite = 3
		err = taskInfoCtl.CreateTaskInfo(ctx, task)
		if err != nil {
			fmt.Println(err)
			return
		}
		taskquery2 := new(InfoItem)
		taskquery2.ProjectId = projectId
		taskquery2.TaskId = taskId
		err = taskInfoCtl.GetTaskInfo(ctx, taskquery2)
		if err != nil {
			fmt.Println(err)
			return
		}
		fmt.Println(taskquery2)
	}

	taskInfoCtl.UpdateFinishedCaseForIncr(ctx, projectId, taskId)
	// taskInfoCtl.UpdateTaskExecutedResult(ctx, projectId, taskId, commonpb.ExecutedResult_TER_PANIC)
}

func TestListTaskInfoForPage(t *testing.T) {
	_, projectId := "task|99999", "project|1231231"
	ctx := context.Background()
	taskInfoCtl := NewTaskInfoProcessor(rCacheService)
	page, err := taskInfoCtl.ListTaskInfoForPage(ctx, projectId, 1, 30, pb.TaskPageType_PT_ARCHIVE)
	if err != nil {
		fmt.Println(err)
		return
	}
	fmt.Println(page)
	fmt.Println("=================ARCHIVE=================")
	page, err = taskInfoCtl.ListTaskInfoForPage(ctx, projectId, 1, 30, pb.TaskPageType_PT_ALL)
	if err != nil {
		fmt.Println(err)
		return
	}
	fmt.Println(page)
	fmt.Println("=================ALL=================")
	page, err = taskInfoCtl.ListTaskInfoForPage(ctx, projectId, 1, 30, pb.TaskPageType_PT_ING)
	if err != nil {
		fmt.Println(err)
		return
	}
	fmt.Println(page)
	fmt.Println("=================ING=================")
}

func TestName(t *testing.T) {
	fmt.Println(time.Now().AddDate(0, 0, -7).UnixMilli())
	scan := []string{
		"probe:dispatcher:taskInfoZset:all:project_id:Kqllt5-",
		"probe:dispatcher:taskInfoZset:all:project_id:OdhjA5d",
		"probe:dispatcher:taskInfoZset:all:project_id:9fA",
	}

	pList := make([]string, 0, len(scan))
	for _, v := range scan {
		if after, found := strings.CutPrefix(v, redis.ConstRedisDispatcherTaskInfoAllKey+":"); found {
			pList = append(pList, after)
		}
	}
	fmt.Println(pList)
}

func TestScanTaskInfoAllKey(t *testing.T) {
	ctx := context.Background()
	taskInfoCtl := NewTaskInfoProcessor(rCacheService)
	key, err := taskInfoCtl.ScanTaskInfoAllKey(ctx)
	if err != nil {
		fmt.Println(err)
		return
	}
	fmt.Println(key)
	for _, k := range key {
		v, err := taskInfoCtl.FindMoreThan7DayByProjectId(ctx, k)
		if err != nil {
			fmt.Println(err)
			return
		}
		fmt.Println(v)
		err = taskInfoCtl.HandleMoreThan7Day(ctx, k, v)
		if err != nil {
			fmt.Println(err)
			return
		}
	}
}

func TestGetPlanMetaData(t *testing.T) {
	//probe:dispatcher:taskInfo:project_id:Kqllt5-9fA-I5UOdhjA5d:task_id:U0fmE2j8Hiqy25RMvdYYX
	// alter table task_info_record
	//    add plan_meta_data json null comment '计划元数据';
	ctx := context.Background()
	taskInfoCtl := NewTaskInfoProcessor(rCacheService)
	_UiPlanMeta := new(managerPb.UIPlanComponent)
	err := taskInfoCtl.GetPlanMetaData(
		ctx, "project_id:Kqllt5-9fA-I5UOdhjA5d", "task_id:U0fmE2j8Hiqy25RMvdYYX", _UiPlanMeta,
	)
	if err != nil {
		fmt.Println(err)
		return
	}
	fmt.Println(_UiPlanMeta)
}
