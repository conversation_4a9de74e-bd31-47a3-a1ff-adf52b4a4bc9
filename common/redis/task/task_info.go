package task

import (
	"context"
	"fmt"
	"strconv"
	"strings"
	"time"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/redislock"

	"github.com/zeromicro/go-zero/core/logx"
	redisgozero "github.com/zeromicro/go-zero/core/stores/redis"

	commonpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/redis"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/rpc/pb"
)

const (
	InfoItemFieldForProjectId          = "project_id"
	InfoItemFieldForTaskId             = "task_id"
	InfoItemFieldForPlanId             = "plan_id"
	InfoItemFieldForPlanName           = "plan_name"
	InfoItemFieldForTriggerMode        = "trigger_mode"
	InfoItemFieldForExecuteBy          = "execute_by"
	InfoItemFieldForCreateTime         = "create_time"
	InfoItemFieldForPriorityType       = "priority_type"
	InfoItemFieldForTotalCase          = "total_case"
	InfoItemFieldForTotalSuite         = "total_suite"
	InfoItemFieldForTaskExecuteStatus  = "task_execute_status"
	InfoItemFieldForTaskExecutedResult = "task_executed_result"
	InfoItemFieldForFinishedCase       = "finished_case"
	InfoItemFieldForSuccessCase        = "success_case"
	InfoItemFieldForFinishedSuite      = "finished_suite"
	InfoItemFieldForSuccessSuite       = "success_suite"
	InfoItemFieldForCostTime           = "cost_time"
	InfoItemFieldForWaitTime           = "wait_time"
	InfoItemFieldForStartedAt          = "started_at"
	InfoItemFieldForEndedAt            = "ended_at"
	InfoItemFieldForUpdateAt           = "update_at"
	InfoItemFieldForReportViewUrl      = "report_view_url"
	InfoItemFieldForReportDownloadUrl  = "report_download_url"
	InfoItemFieldForExecuteId          = "execute_id"
	InfoItemFieldForPlanMetaData       = "plan_meta_data"
)

type InfoItem struct {
	/*冷数据*/
	ProjectId         string `json:"project_id" redis:"project_id"`                    // 项目ID
	TaskId            string `json:"task_id"  redis:"task_id"`                         // 任务id
	PlanId            string `json:"plan_id"  redis:"plan_id"`                         // 计划ID
	PlanName          string `json:"plan_name"  redis:"plan_name"`                     // 计划名称
	TriggerMode       string `json:"trigger_mode"  redis:"trigger_mode"`               // 触发类型
	ExecuteBy         string `json:"execute_by"  redis:"execute_by"`                   // 创建者
	CreateTime        int64  `json:"create_time"  redis:"create_time"`                 // 创建时间
	PriorityType      int8   `json:"priority_type"  redis:"priority_type"`             // 优先级策略(0 1 2 3 4 =》Default、Middle、High、Ultra、Low)
	TotalCase         int64  `json:"total_case"  redis:"total_case"`                   // 总测试用例数
	TotalSuite        int64  `json:"total_suite"  redis:"total_suite"`                 // 测试集合总数
	ExecuteId         string `json:"execute_id"  redis:"execute_id"`                   // 计划ID
	ReportViewUrl     string `json:"report_view_url"  redis:"report_view_url"`         // 查看地址
	ReportDownloadUrl string `json:"report_download_url"  redis:"report_download_url"` // 下载地址
	PlanMetaData      string `json:"plan_meta_data"  redis:"plan_meta_data"`           // 计划原数据

	/*热数据*/
	/*
		UpdateTaskExecuteStatus
			内部调用更新 [UpdateFinishedCaseForIncr]
					TaskExecuteStatus_TES_EXECUTING
			内部调用更新 [UpdateTaskExecutedResult更新成功或者失败]
					TaskExecuteStatus_TES_FINISH
			外部调用更新[StopLogic.Stop()] 内部调用[更新结果为:TaskExecutedResult_TER_PANIC]
					TaskExecuteStatus_TES_STOP
	*/
	TaskExecuteStatus int8 `json:"task_execute_status"  redis:"task_execute_status"` // 执行状态(0排队中,1执行中,2已完成,3已停止)
	/*
		UpdateTaskExecutedResult
			外部调用更新
					TaskExecutedResult_TER_SUCCESS
			外部调用更新
					TaskExecutedResult_TER_FAILURE
			外部调用更新[Callback.Panic() and UIPlanPublisher.Panic()]
					TaskExecutedResult_TER_PANIC
	*/
	TaskExecutedResult int8 `json:"task_executed_result"  redis:"task_executed_result"` // 执行结果(0缺省,1成功,2失败,3异常)
	/*
		update [node.TaskInfoProcessorSync()]
	*/
	FinishedCase  int64 `json:"finished_case"  redis:"finished_case"`   // 已经执行的测试用例数
	SuccessCase   int64 `json:"success_case"  redis:"success_case"`     // 执行成功的测试用例数
	FinishedSuite int64 `json:"finished_suite"  redis:"finished_suite"` // 执行完的测试集合数
	SuccessSuite  int64 `json:"success_suite"  redis:"success_suite"`   // 执行成功的测试集合数

	CostTime  int64 `json:"cost_time"  redis:"cost_time"`   // 执行耗时
	WaitTime  int64 `json:"wait_time"  redis:"wait_time"`   // 排队耗时
	StartedAt int64 `json:"started_at"  redis:"started_at"` // 开始时间
	EndedAt   int64 `json:"ended_at"  redis:"ended_at"`     // 结束时间
	UpdateAt  int64 `json:"update_at"  redis:"update_at"`   // 更新时间

	PageType int8 `json:"page_type"  redis:"page_type"` // 执行结果(0全部,1进行中,2归档)
}

func (i *InfoItem) String() string {
	toString, err := redis.IJson.MarshalToString(i)
	if err != nil {
		return fmt.Sprintf("InfoItem: %#v", i)
	}
	return toString
}

type UserInfo struct {
	Account  string `json:"account" redis:"account"`
	Fullname string `json:"fullname" redis:"account"`
	DeptName string `json:"dept_name" redis:"account"`
	Email    string `json:"email" redis:"account"`
	Mobile   string `json:"mobile" redis:"account"`
}

// InfoProcessor 信息处理器
type InfoProcessor struct {
	redService *redis.RCacheService
}

func NewTaskInfoProcessor(redService *redis.RCacheService) *InfoProcessor {
	return &InfoProcessor{redService: redService}
}

// CreateTaskInfo  创建任务
func (m *InfoProcessor) CreateTaskInfo(ctx context.Context, item *InfoItem) error {
	logger := logx.WithContext(ctx)

	service := m.redService
	item.CreateTime = time.Now().UnixMilli()
	// item.StartedAt = time.Now().UnixMilli()
	err := service.SetHashCache(
		ctx, redis.ConstRedisDispatcherTaskInfoKey, m.genTaskIdAndProjectIdSuffix(item.ProjectId, item.TaskId), item,
	)
	if err != nil {
		logx.WithContext(ctx).Errorf("task CreateTaskInfo  failed, error: %s", err)
		return err
	}
	// 增加到zeset中(全局/活动)
	_, err = service.ZAdds(
		ctx, redis.ConstRedisDispatcherTaskInfoAllKey, m.genProjectIdSuffix(item.ProjectId), redisgozero.Pair{
			Key:   item.TaskId,
			Score: time.Now().UnixMilli(),
		},
	)
	if err != nil {
		logger.Errorf("task CreateTaskInfo  failed, error: %s", err)
		return err
	}
	_, err = service.ZAdds(
		ctx, redis.ConstRedisDispatcherTaskInfoRunningKey, m.genProjectIdSuffix(item.ProjectId), redisgozero.Pair{
			Key:   item.TaskId,
			Score: time.Now().UnixMilli(),
		},
	)
	if err != nil {
		logger.Errorf("task CreateTaskInfo  failed, error: %s", err)
		return err
	}
	return nil
}

// GetTaskInfo 创建任务
func (m *InfoProcessor) GetTaskInfo(ctx context.Context, item *InfoItem) error {
	logger := logx.WithContext(ctx)

	service := m.redService
	err := service.GetHashCache(
		ctx, redis.ConstRedisDispatcherTaskInfoKey, m.genTaskIdAndProjectIdSuffix(item.ProjectId, item.TaskId), item,
	)
	if err != nil {
		logger.Errorf("task CreateTaskInfo  failed, error: %s", err)
		return err
	}
	return nil
}

// GetPlanMetaData 获取计划元数据
// val any（请使用指针）
func (m *InfoProcessor) GetPlanMetaData(
	ctx context.Context, projectId, taskId string, val any,
) (err error) {
	logger := logx.WithContext(ctx)

	json, err := redis.GetHashForUniversal(
		ctx, m.redService.RedisUniversalClient, m.GenRedisTaskIdAndProjectId(projectId, taskId),
		InfoItemFieldForPlanMetaData,
	)
	if err != nil {
		logger.Errorf("task GetUpdateTime  failed, error: %s", err)
		return err
	}
	if len(json) == 0 {
		return nil
	}
	err = protobuf.UnmarshalJSONFromString(json, val)
	if err != nil {
		return err
	}
	return nil
}

// GetTaskExecutedResult 更新/获取任务信息
func (m *InfoProcessor) GetTaskExecutedResult(
	ctx context.Context, projectId, taskId string,
) (result commonpb.ExecutedResult, err error) {
	logger := logx.WithContext(ctx)

	res, err := redis.GetHashForUniversal(
		ctx, m.redService.RedisUniversalClient, m.GenRedisTaskIdAndProjectId(projectId, taskId),
		InfoItemFieldForTaskExecutedResult,
	)
	if err != nil {
		logger.Errorf("task GetTaskExecutedResult  failed, error: %s", err)
		return commonpb.ExecutedResult_TER_INIT, err
	}
	if len(res) == 0 {
		return commonpb.ExecutedResult_TER_INIT, nil
	}
	parseInt, err := strconv.ParseInt(res, 10, 32)
	if err != nil {
		logger.Errorf("task GetTaskExecutedResult  failed, error: %s", err)
		return commonpb.ExecutedResult_TER_INIT, err
	}
	return commonpb.ExecutedResult(parseInt), nil
}

func (m *InfoProcessor) UpdateTaskExecutedResult(
	ctx context.Context, projectId, taskId string, result commonpb.ExecutedResult, now int64,
) error {
	logger := logx.WithContext(ctx)

	b, item := m.CheckTaskInfoPoint(ctx, projectId, taskId)
	if !b {
		return nil
	}

	srcResult := commonpb.ExecutedResult(item.TaskExecutedResult)
	logger.Infof(
		"prepare to update the task execute result, project_id: %s, task_id: %s, result: %s => %s",
		projectId, taskId, srcResult, result,
	)

	err := redis.SetHashForUniversal(
		ctx, m.redService.RedisUniversalClient, m.GenRedisTaskIdAndProjectId(projectId, taskId),
		InfoItemFieldForTaskExecutedResult, int32(result),
	)
	if err != nil {
		logger.Errorf(
			"failed to update the task execute result, project_id: %s, task_id: %s, result: %s, error: %+v",
			projectId, taskId, result, err,
		)
		return err
	}
	switch result {
	case commonpb.ExecutedResult_TER_SUCCESS:
		// 成功失败都算TaskExecuteStatus_TES_FINISH
		fallthrough
	case commonpb.ExecutedResult_TER_FAILURE:
		err := m.UpdateTaskExecuteStatus(ctx, projectId, taskId, commonpb.ExecuteStatus_TES_FINISH, now)
		if ec, ok := errorx.FromError(err); ok && ec != nil && (ec.Code() == errorx.AlreadyExists || ec.Code() == errorx.AcquireRedisLockFailure) {
			logger.Warnf(
				"failed to update the task execute status, project_id: %s, task_id: %s, status: %s, result: %s, error: %+v",
				projectId, taskId, commonpb.ExecuteStatus_TES_FINISH, result, ec,
			)
		} else if err != nil {
			logger.Errorf(
				"failed to update execute status of task, project_id: %s, task_id: %s, status: %s, result: %s, error: %+v",
				projectId, taskId, commonpb.ExecuteStatus_TES_FINISH, result, err,
			)
			return err
		}
	case commonpb.ExecutedResult_TER_PANIC:
		err := m.UpdateTaskExecuteStatus(ctx, projectId, taskId, commonpb.ExecuteStatus_TES_STOP, now)
		if ec, ok := errorx.FromError(err); ok && ec != nil && (ec.Code() == errorx.AlreadyExists || ec.Code() == errorx.AcquireRedisLockFailure) {
			logger.Warnf(
				"failed to update the task execute status, project_id: %s, task_id: %s, status: %s, result: %s, error: %+v",
				projectId, taskId, commonpb.ExecuteStatus_TES_STOP, result, ec,
			)
		} else if err != nil {
			logger.Errorf(
				"failed to update execute status of task, project_id: %s, task_id: %s, status: %s, result: %s, error: %+v",
				projectId, taskId, commonpb.ExecuteStatus_TES_STOP, result, err,
			)
			return err
		}
	}
	// 进入归档
	err = m.ArchiveFunc(ctx, projectId, taskId, now, item)
	if err != nil {
		logger.Errorf("task UpdateTaskExecuteStatus  failed, error: %s", err)
		return err
	}

	return nil
}

func (m *InfoProcessor) GetTaskExecuteStatus(
	ctx context.Context, projectId, taskId string,
) (result commonpb.ExecuteStatus, err error) {
	logger := logx.WithContext(ctx)

	res, err := redis.GetHashForUniversal(
		ctx, m.redService.RedisUniversalClient, m.GenRedisTaskIdAndProjectId(projectId, taskId),
		InfoItemFieldForTaskExecuteStatus,
	)
	if err != nil {
		logger.Errorf("task GetTaskExecuteStatus  failed, error: %s", err)
		return commonpb.ExecuteStatus_TES_INIT, err
	}
	if len(res) == 0 {
		return commonpb.ExecuteStatus_TES_INIT, nil
	}
	parseInt, err := strconv.ParseInt(res, 10, 32)
	if err != nil {
		logger.Errorf("task GetTaskExecuteStatus  failed, error: %s", err)
		return commonpb.ExecuteStatus_TES_INIT, err
	}
	return commonpb.ExecuteStatus(parseInt), nil
}

func (m *InfoProcessor) CheckTaskInfoPoint(ctx context.Context, projectId, taskId string) (bool, *InfoItem) {
	logger := logx.WithContext(ctx)

	item := &InfoItem{
		ProjectId: projectId,
		TaskId:    taskId,
	}
	err := m.GetTaskInfo(ctx, item)
	if err != nil {
		logger.Errorf("failed to get task info, project_id: %s, task_id: %s, error: %+v", projectId, taskId, err)
		return false, nil
	}
	if item.PlanName == "" {
		logger.Warnf("task does not exist, project_id: %s, task_id: %s", projectId, taskId)
		return false, nil
	}
	/*if item.TaskExecutedResult != int8(commonpb.ExecutedResult_TER_INIT) {
		// 记录非法
		logger.Warnf("task UpdateTaskExecuteStatus failed,TaskExecutedResult 已经进入终态")
		return false, nil
	}*/
	return true, item
}

func (m *InfoProcessor) UpdateTaskExecuteStatus(
	ctx context.Context, projectId, taskId string, status commonpb.ExecuteStatus, now int64,
) error {
	logger := logx.WithContext(ctx)

	b, item := m.CheckTaskInfoPoint(ctx, projectId, taskId)
	if !b {
		return nil
	}

	srcStatus := commonpb.ExecuteStatus(item.TaskExecuteStatus)
	logger.Infof(
		"prepare to update the task execute status, project_id: %s, task_id: %s, status: %s => %s",
		projectId, taskId, srcStatus, status,
	)
	if status == srcStatus {
		return errorx.Err(errorx.AlreadyExists, "数据是被修改过的已是当前状态")
	} else if status < srcStatus {
		return errorx.Err(errorx.AlreadyExists, "数据已被修改为后续的状态")
	}

	lock, err := redislock.NewRedisLockAndAcquire(
		m.redService.RedisClient, m.GenRedisTaskIdAndProjectIdStatusLock(projectId, taskId),
		redislock.WithExpire(10*time.Second),
	)
	if err != nil {
		logger.Warnf(
			"failed to acquire the redis lock, project_id: %s, task_id: %s, error: %+v", projectId, taskId, err,
		)
		return err
	}
	defer func(lock *redislock.RedisLock) {
		err := lock.Release()
		if err != nil {
			logger.Warnf(
				"failed to release the redis lock, project_id: %s, task_id: %s, error: %+v", projectId, taskId, err,
			)
		}
	}(lock)
	// double check
	b, item = m.CheckTaskInfoPoint(ctx, projectId, taskId)
	if !b {
		return nil
	}

	srcStatus = commonpb.ExecuteStatus(item.TaskExecuteStatus)
	logger.Infof(
		"ready to update the task execute status, project_id: %s, task_id: %s, status: %s => %s",
		projectId, taskId, srcStatus, status,
	)
	if status == srcStatus {
		return nil
	} else if status < srcStatus {
		return nil
	}

	err = redis.SetHashForUniversal(
		ctx, m.redService.RedisUniversalClient, m.GenRedisTaskIdAndProjectId(projectId, taskId),
		InfoItemFieldForTaskExecuteStatus, int32(status),
	)
	if err != nil {
		logger.Errorf(
			"failed to update the task execute status, project_id: %s, task_id: %s, status: %s, error: %+v",
			projectId, taskId, status, err,
		)
		return err
	}
	// now := time.Now().UnixMilli()
	switch status {
	case commonpb.ExecuteStatus_TES_FINISH:
		// 进入归档 结束计算执行耗时(开始减去结束) 上层已经做了归档处理
		/*err2 := m.ArchiveFunc(ctx, projectId, taskId, now, item)
		if err2 != nil {
			logx.WithContext(ctx).Errorf("task UpdateTaskExecuteStatus  failed, error: %s", err)
			return err2
		}*/
	case commonpb.ExecuteStatus_TES_STOP:
		// 进入归档 结束计算执行耗时(开始减去结束) 停止的 执行时间和等待时间有上报url地址的时候触发更新
		/*err2 := m.ArchiveFunc(ctx, projectId, taskId, now, item)
		if err2 != nil {
			logx.WithContext(ctx).Errorf("task UpdateTaskExecuteStatus  failed, error: %s", err)
			return err2
		}
		err := redis.SetHashForUniversal(
			ctx, m.redService.RedisUniversalClient, m.GenRedisTaskIdAndProjectId(projectId, taskId),
			InfoItemFieldForTaskExecutedResult,
			int32(commonpb.ExecutedResult_TER_PANIC),
		)
		if err != nil {
			logx.WithContext(ctx).Errorf("task UpdateTaskExecuteStatus  failed, error: %s", err)
			return err
		}*/
	case commonpb.ExecuteStatus_TES_EXECUTING:
		logger.Debugf(
			"InfoProcessor UpdateTaskExecuteStatus[%s] : taskId:%s TaskExecuteStatus5", status, taskId,
		)
		// 计算时间(判断原状态是否是初始化状态)
		if srcStatus == commonpb.ExecuteStatus_TES_INIT {
			waitTime := now - item.CreateTime
			err := m.UpdateWaitTime(ctx, projectId, taskId, waitTime)
			if err != nil {
				logger.Errorf(
					"failed to update the `wait_time` field of task, project_id: %s, task_id: %s, wait_time: %d, error: %s",
					projectId, taskId, waitTime, err,
				)
				return err
			}
		}
		// 更新开始时间(会存在误差应该要减去第一个任务的执行时间)
		err := m.UpdateStartedAt(ctx, projectId, taskId, time.Now().UnixMilli())
		if err != nil {
			logger.Errorf(
				"failed to update the `started_at` field of task, project_id: %s, task_id: %s, error: %s",
				projectId, taskId, err,
			)
			return err
		}
		// 更新当前任务在zset中的时间
		err = m.updateRunningZSet(ctx, projectId, taskId)
		if err != nil {
			logger.Errorf("task UpdateTaskExecuteStatus  failed, error: %s", err)
			return err
		}
	}

	return nil
}

func (m *InfoProcessor) ArchiveFunc(
	ctx context.Context, projectId, taskId string, now int64, item *InfoItem,
) error {
	logger := logx.WithContext(ctx)

	err := m.UpdateEndedAt(ctx, projectId, taskId, now)
	if err != nil {
		logger.Errorf("task ArchiveFunc  failed, error: %s", err)
		return err
	}
	costTime := now - item.CreateTime
	if item.StartedAt > 0 {
		costTime = now - item.StartedAt
	}
	err = m.UpdateCostTime(ctx, projectId, taskId, costTime)
	if err != nil {
		logger.Errorf("task ArchiveFunc  failed, error: %s", err)
		return err
	}
	if item.WaitTime == 0 {
		waitTime := now - item.CreateTime
		err := m.UpdateWaitTime(ctx, projectId, taskId, waitTime)
		if err != nil {
			logger.Errorf("task UpdateTaskExecuteStatus  failed, error: %s", err)
			return err
		}
	}
	return nil
}

func (m *InfoProcessor) GetFinishedCase(
	ctx context.Context, projectId, taskId string,
) (num int64, err error) {
	logger := logx.WithContext(ctx)

	res, err := redis.GetHashForUniversal(
		ctx, m.redService.RedisUniversalClient, m.GenRedisTaskIdAndProjectId(projectId, taskId),
		InfoItemFieldForFinishedCase,
	)
	if err != nil {
		logger.Errorf("task GetFinishedCase  failed, error: %s", err)
		return 0, err
	}
	if len(res) == 0 {
		return 0, nil
	}
	parseInt, err := strconv.ParseInt(res, 10, 32)
	if err != nil {
		logger.Errorf("task GetFinishedCase  failed, error: %s", err)
		return 0, err
	}
	return parseInt, nil
}

// UpdateFinishedCaseForIncr 失败成功都算完成
func (m *InfoProcessor) UpdateFinishedCaseForIncr(
	ctx context.Context, projectId, taskId string,
) error {
	logger := logx.WithContext(ctx)

	b, _ := m.CheckTaskInfoPoint(ctx, projectId, taskId)
	if !b {
		return nil
	}
	_, err := redis.IncrByHash(
		ctx, m.redService.RedisClient, m.GenRedisTaskIdAndProjectId(projectId, taskId),
		InfoItemFieldForFinishedCase, 1,
	)
	if err != nil {
		logger.Errorf("task UpdateFinishedCaseForIncr  failed, error: %s", err)
		return err
	}
	// 第一个用例执行成功
	/*if v == 1 {
		err := m.UpdateTaskExecuteStatus(ctx, projectId, taskId, commonpb.ExecuteStatus_TES_EXECUTING)
		if err != nil {
			logger.Errorf("task UpdateTaskExecuteStatus  failed, error: %s", err)
			return err
		}
	}*/
	// 更新当前任务在zset中的时间
	err = m.updateRunningZSet(ctx, projectId, taskId)
	if err != nil {
		return err
	}
	return nil
}

func (m *InfoProcessor) GetSuccessCase(
	ctx context.Context, projectId, taskId string,
) (num int64, err error) {
	logger := logx.WithContext(ctx)

	res, err := redis.GetHashForUniversal(
		ctx, m.redService.RedisUniversalClient, m.GenRedisTaskIdAndProjectId(projectId, taskId),
		InfoItemFieldForSuccessCase,
	)
	if err != nil {
		logger.Errorf("task GetSuccessCase  failed, error: %s", err)
		return 0, err
	}
	if len(res) == 0 {
		return 0, nil
	}
	parseInt, err := strconv.ParseInt(res, 10, 32)
	if err != nil {
		logger.Errorf("task GetSuccessCase  failed, error: %s", err)
		return 0, err
	}
	return parseInt, nil
}

// UpdateSuccessCaseForIncr 只有成功才调用
func (m *InfoProcessor) UpdateSuccessCaseForIncr(
	ctx context.Context, projectId, taskId string,
) error {
	logger := logx.WithContext(ctx)

	b, _ := m.CheckTaskInfoPoint(ctx, projectId, taskId)
	if !b {
		return nil
	}
	_, err := redis.IncrByHash(
		ctx, m.redService.RedisClient, m.GenRedisTaskIdAndProjectId(projectId, taskId), InfoItemFieldForSuccessCase,
		1,
	)
	if err != nil {
		logger.Errorf("task UpdateSuccessCaseForIncr  failed, error: %s", err)
		return err
	}
	// 更新当前任务在zset中的时间
	err = m.updateRunningZSet(ctx, projectId, taskId)
	if err != nil {
		return err
	}
	return nil
}

// UpdateTotalCase 设置总用例数
func (m *InfoProcessor) UpdateTotalCase(
	ctx context.Context, projectId, taskId string, total int64,
) error {
	logger := logx.WithContext(ctx)

	b, _ := m.CheckTaskInfoPoint(ctx, projectId, taskId)
	if !b {
		return nil
	}
	err := redis.HSetHashForUniversal(
		ctx, m.redService.RedisUniversalClient, m.GenRedisTaskIdAndProjectId(projectId, taskId),
		map[string]any{
			InfoItemFieldForTotalCase: total,
		},
	)
	if err != nil {
		logger.Errorf("task UpdateTotalCase  failed, error: %s", err)
		return err
	}
	// 更新当前任务在zset中的时间
	err = m.updateRunningZSet(ctx, projectId, taskId)
	if err != nil {
		return err
	}
	return nil
}

// UpdateTotalCaseForIncrNum 递增总数
func (m *InfoProcessor) UpdateTotalCaseForIncrNum(
	ctx context.Context, projectId, taskId string, incr int,
) error {
	logger := logx.WithContext(ctx)

	b, _ := m.CheckTaskInfoPoint(ctx, projectId, taskId)
	if !b {
		return nil
	}
	_, err := redis.IncrByHash(
		ctx, m.redService.RedisClient, m.GenRedisTaskIdAndProjectId(projectId, taskId), InfoItemFieldForTotalCase,
		incr,
	)
	if err != nil {
		logger.Errorf("task UpdateTotalCaseForIncr  failed, error: %s", err)
		return err
	}
	// 更新当前任务在zset中的时间
	err = m.updateRunningZSet(ctx, projectId, taskId)
	if err != nil {
		return err
	}
	return nil
}

// UpdateTotalSuite 设置总用例数
func (m *InfoProcessor) UpdateTotalSuite(
	ctx context.Context, projectId, taskId string, total int64,
) error {
	logger := logx.WithContext(ctx)

	b, _ := m.CheckTaskInfoPoint(ctx, projectId, taskId)
	if !b {
		return nil
	}
	err := redis.SetHashForUniversal(
		ctx, m.redService.RedisUniversalClient, m.GenRedisTaskIdAndProjectId(projectId, taskId),
		InfoItemFieldForTotalSuite, total,
	)
	if err != nil {
		logger.Errorf("task UpdateTotalSuite  failed, error: %s", err)
		return err
	}
	// 更新当前任务在zset中的时间
	err = m.updateRunningZSet(ctx, projectId, taskId)
	if err != nil {
		return err
	}
	return nil
}

func (m *InfoProcessor) GetFinishedSuite(
	ctx context.Context, projectId, taskId string,
) (num int64, err error) {
	logger := logx.WithContext(ctx)

	res, err := redis.GetHash(
		ctx, m.redService.RedisClient, m.GenRedisTaskIdAndProjectId(projectId, taskId),
		InfoItemFieldForFinishedSuite,
	)
	if err != nil {
		logger.Errorf("task GetFinishedSuite  failed, error: %s", err)
		return 0, err
	}
	if len(res) == 0 {
		return 0, nil
	}
	parseInt, err := strconv.ParseInt(res, 10, 32)
	if err != nil {
		logger.Errorf("task SuccessCase  failed, error: %s", err)
		return 0, err
	}
	return parseInt, nil
}

func (m *InfoProcessor) UpdateFinishedSuiteForIncr(
	ctx context.Context, projectId, taskId string,
) (int, error) {
	logger := logx.WithContext(ctx)

	b, _ := m.CheckTaskInfoPoint(ctx, projectId, taskId)
	if !b {
		return 0, nil
	}
	val, err := redis.IncrByHash(
		ctx, m.redService.RedisClient, m.GenRedisTaskIdAndProjectId(projectId, taskId),
		InfoItemFieldForFinishedSuite, 1,
	)
	if err != nil {
		logger.Errorf("task UpdateFinishedSuiteForIncr  failed, error: %s", err)
		return 0, err
	}
	// 更新当前任务在zset中的时间
	err = m.updateRunningZSet(ctx, projectId, taskId)
	if err != nil {
		return 0, err
	}
	return val, nil
}

func (m *InfoProcessor) updateRunningZSet(ctx context.Context, projectId, taskId string) error {
	logger := logx.WithContext(ctx)

	key := m.genProjectIdSuffix(projectId)
	ok, err := m.redService.ZIsExist(ctx, redis.ConstRedisDispatcherTaskInfoRunningKey, key, taskId)
	if err != nil {
		logger.Errorf("task updateRunningZSet  failed, error: %s", err)
		return err
	}

	if ok {
		_, err = m.redService.ZAdds(
			ctx, redis.ConstRedisDispatcherTaskInfoRunningKey, key, redisgozero.Pair{
				Key:   taskId,
				Score: time.Now().UnixMilli(),
			},
		)
		if err != nil {
			logger.Errorf("task updateRunningZSet  failed, error: %s", err)
			return err
		}

		_ = m.UpdateUpdateTime(ctx, projectId, taskId, time.Now().UnixMilli())
	}

	return nil
}

func (m *InfoProcessor) GetSuccessSuite(
	ctx context.Context, projectId, taskId string,
) (num int64, err error) {
	logger := logx.WithContext(ctx)

	res, err := redis.GetHashForUniversal(
		ctx, m.redService.RedisUniversalClient, m.GenRedisTaskIdAndProjectId(projectId, taskId),
		InfoItemFieldForSuccessSuite,
	)
	if err != nil {
		logger.Errorf("task GetSuccessSuite  failed, error: %s", err)
		return 0, err
	}
	if len(res) == 0 {
		return 0, nil
	}
	parseInt, err := strconv.ParseInt(res, 10, 32)
	if err != nil {
		logger.Errorf("task GetSuccessSuite  failed, error: %s", err)
		return 0, err
	}
	return parseInt, nil
}

func (m *InfoProcessor) UpdateSuccessSuiteForIncr(
	ctx context.Context, projectId, taskId string,
) (int, error) {
	logger := logx.WithContext(ctx)

	b, _ := m.CheckTaskInfoPoint(ctx, projectId, taskId)
	if !b {
		return 0, nil
	}
	val, err := redis.IncrByHash(
		ctx, m.redService.RedisClient, m.GenRedisTaskIdAndProjectId(projectId, taskId),
		InfoItemFieldForSuccessSuite, 1,
	)
	if err != nil {
		logger.Errorf("task UpdateSuccessSuiteForIncr  failed, error: %s", err)
		return 0, err
	}
	// 更新当前任务在zset中的时间
	err = m.updateRunningZSet(ctx, projectId, taskId)
	if err != nil {
		return 0, err
	}
	return val, nil
}

func (m *InfoProcessor) GetEndedAt(
	ctx context.Context, projectId, taskId string,
) (time int64, err error) {
	logger := logx.WithContext(ctx)

	res, err := redis.GetHashForUniversal(
		ctx, m.redService.RedisUniversalClient, m.GenRedisTaskIdAndProjectId(projectId, taskId),
		InfoItemFieldForEndedAt,
	)
	if err != nil {
		logger.Errorf("task EndedAt  failed, error: %s", err)
		return 0, err
	}
	if len(res) == 0 {
		return 0, nil
	}
	parseInt, err := strconv.ParseInt(res, 10, 32)
	if err != nil {
		logger.Errorf("task EndedAt  failed, error: %s", err)
		return 0, err
	}
	return parseInt, nil
}

func (m *InfoProcessor) UpdateEndedAt(
	ctx context.Context, projectId, taskId string, t int64,
) error {
	logger := logx.WithContext(ctx)

	b, item := m.CheckTaskInfoPoint(ctx, projectId, taskId)
	if !b {
		return nil
	}
	if item.EndedAt > 0 {
		logger.Warn("task UpdateEndedAt, The data has been archived")
		return nil
	}
	err := redis.SetHashForUniversal(
		ctx, m.redService.RedisUniversalClient, m.GenRedisTaskIdAndProjectId(projectId, taskId),
		InfoItemFieldForEndedAt,
		t,
	)
	if err != nil {
		logger.Errorf("task UpdateEndedAt  failed, error: %s", err)
		return err
	}
	// 增加到zeset中(归档)从(活动)删除
	_, err = m.redService.ZAdds(
		ctx, redis.ConstRedisDispatcherTaskInfoArchiveKey, m.genProjectIdSuffix(projectId), redisgozero.Pair{
			Key:   taskId,
			Score: time.Now().UnixMilli(),
		},
	)
	if err != nil {
		logger.Errorf("task UpdateEndedAt  failed, error: %s", err)
		return err
	}
	_, err = m.redService.ZRem(
		ctx, redis.ConstRedisDispatcherTaskInfoRunningKey, m.genProjectIdSuffix(projectId), taskId,
	)
	if err != nil {
		logger.Errorf("task UpdateEndedAt  failed, error: %s", err)
		return err
	}
	_ = m.UpdateUpdateTime(ctx, projectId, taskId, time.Now().UnixMilli())

	return nil
}

func (m *InfoProcessor) GetStartedAt(
	ctx context.Context, projectId, taskId string,
) (time int64, err error) {
	logger := logx.WithContext(ctx)

	res, err := redis.GetHashForUniversal(
		ctx, m.redService.RedisUniversalClient, m.GenRedisTaskIdAndProjectId(projectId, taskId),
		InfoItemFieldForStartedAt,
	)
	if err != nil {
		logger.Errorf("task GetStartedAt  failed, error: %s", err)
		return 0, err
	}
	if len(res) == 0 {
		return 0, nil
	}
	parseInt, err := strconv.ParseInt(res, 10, 32)
	if err != nil {
		logger.Errorf("task GetStartedAt  failed, error: %s", err)
		return 0, err
	}
	return parseInt, nil
}

func (m *InfoProcessor) UpdateStartedAt(
	ctx context.Context, projectId, taskId string, t int64,
) error {
	logger := logx.WithContext(ctx)

	b, _ := m.CheckTaskInfoPoint(ctx, projectId, taskId)
	if !b {
		return nil
	}
	err := redis.SetHashForUniversal(
		ctx, m.redService.RedisUniversalClient, m.GenRedisTaskIdAndProjectId(projectId, taskId),
		InfoItemFieldForStartedAt,
		t,
	)
	if err != nil {
		logger.Errorf("task UpdateStartedAt  failed, error: %s", err)
		return err
	}
	_ = m.UpdateUpdateTime(ctx, projectId, taskId, time.Now().UnixMilli())
	return nil
}

func (m *InfoProcessor) GetWaitTime(
	ctx context.Context, projectId, taskId string,
) (time int64, err error) {
	logger := logx.WithContext(ctx)

	res, err := redis.GetHashForUniversal(
		ctx, m.redService.RedisUniversalClient, m.GenRedisTaskIdAndProjectId(projectId, taskId),
		InfoItemFieldForWaitTime,
	)
	if err != nil {
		logger.Errorf("task GetWaitTime  failed, error: %s", err)
		return 0, err
	}
	if len(res) == 0 {
		return 0, nil
	}
	parseInt, err := strconv.ParseInt(res, 10, 32)
	if err != nil {
		logger.Errorf("task GetWaitTime  failed, error: %s", err)
		return 0, err
	}
	return parseInt, nil
}

func (m *InfoProcessor) UpdateWaitTime(
	ctx context.Context, projectId, taskId string, t int64,
) error {
	logger := logx.WithContext(ctx)

	b, _ := m.CheckTaskInfoPoint(ctx, projectId, taskId)
	if !b {
		return nil
	}
	err := redis.SetHashForUniversal(
		ctx, m.redService.RedisUniversalClient, m.GenRedisTaskIdAndProjectId(projectId, taskId),
		InfoItemFieldForWaitTime,
		t,
	)
	if err != nil {
		logger.Errorf("task UpdateWaitTime  failed, error: %s", err)
		return err
	}
	_ = m.UpdateUpdateTime(ctx, projectId, taskId, time.Now().UnixMilli())
	return nil
}

func (m *InfoProcessor) GetCostTime(
	ctx context.Context, projectId, taskId string,
) (time int64, err error) {
	logger := logx.WithContext(ctx)

	res, err := redis.GetHashForUniversal(
		ctx, m.redService.RedisUniversalClient, m.GenRedisTaskIdAndProjectId(projectId, taskId),
		InfoItemFieldForCostTime,
	)
	if err != nil {
		logger.Errorf("task GetCostTime  failed, error: %s", err)
		return 0, err
	}
	if len(res) == 0 {
		return 0, nil
	}
	parseInt, err := strconv.ParseInt(res, 10, 32)
	if err != nil {
		logger.Errorf("task GetCostTime  failed, error: %s", err)
		return 0, err
	}
	return parseInt, nil
}

func (m *InfoProcessor) UpdateCostTime(
	ctx context.Context, projectId, taskId string, t int64,
) error {
	logger := logx.WithContext(ctx)

	b, _ := m.CheckTaskInfoPoint(ctx, projectId, taskId)
	if !b {
		return nil
	}
	err := redis.SetHashForUniversal(
		ctx, m.redService.RedisUniversalClient, m.GenRedisTaskIdAndProjectId(projectId, taskId),
		InfoItemFieldForCostTime,
		strconv.FormatInt(t, 10),
	)
	if err != nil {
		logger.Errorf("task UpdateCostTime  failed, error: %s", err)
		return err
	}
	_ = m.UpdateUpdateTime(ctx, projectId, taskId, time.Now().UnixMilli())
	return nil
}

// UpdateReportViewUrl
func (m *InfoProcessor) UpdateReportViewUrl(
	ctx context.Context, projectId, taskId, url string,
) error {
	logger := logx.WithContext(ctx)

	b, _ := m.CheckTaskInfoPoint(ctx, projectId, taskId)
	if !b {
		return nil
	}
	err := redis.SetHashForUniversal(
		ctx, m.redService.RedisUniversalClient, m.GenRedisTaskIdAndProjectId(projectId, taskId),
		InfoItemFieldForReportViewUrl,
		url,
	)
	if err != nil {
		logger.Errorf("task UpdateCostTime  failed, error: %s", err)
		return err
	}
	_ = m.UpdateUpdateTime(ctx, projectId, taskId, time.Now().UnixMilli())
	return nil
}

func (m *InfoProcessor) UpdateReportDownloadUrl(
	ctx context.Context, projectId, taskId, url string,
) error {
	logger := logx.WithContext(ctx)

	b, _ := m.CheckTaskInfoPoint(ctx, projectId, taskId)
	if !b {
		return nil
	}
	err := redis.SetHashForUniversal(
		ctx, m.redService.RedisUniversalClient, m.GenRedisTaskIdAndProjectId(projectId, taskId),
		InfoItemFieldForReportDownloadUrl,
		url,
	)
	if err != nil {
		logger.Errorf("task UpdateReportDownloadUrl  failed, error: %s", err)
		return err
	}
	_ = m.UpdateUpdateTime(ctx, projectId, taskId, time.Now().UnixMilli())
	return nil
}

func (m *InfoProcessor) GetUpdateTime(
	ctx context.Context, projectId, taskId string,
) (time int64, err error) {
	logger := logx.WithContext(ctx)

	res, err := redis.GetHashForUniversal(
		ctx, m.redService.RedisUniversalClient, m.GenRedisTaskIdAndProjectId(projectId, taskId),
		InfoItemFieldForUpdateAt,
	)
	if err != nil {
		logger.Errorf("task GetUpdateTime  failed, error: %s", err)
		return 0, err
	}
	if len(res) == 0 {
		return 0, nil
	}
	parseInt, err := strconv.ParseInt(res, 10, 32)
	if err != nil {
		logger.Errorf("task GetUpdateTime  failed, error: %s", err)
		return 0, err
	}
	return parseInt, nil
}

func (m *InfoProcessor) UpdateUpdateTime(
	ctx context.Context, projectId, taskId string, t int64,
) error {
	logger := logx.WithContext(ctx)

	err := redis.SetHashForUniversal(
		ctx, m.redService.RedisUniversalClient, m.GenRedisTaskIdAndProjectId(projectId, taskId),
		InfoItemFieldForUpdateAt,
		strconv.FormatInt(t, 10),
	)
	if err != nil {
		logger.Errorf("task UpdateCostTime  failed, error: %s", err)
		return err
	}
	return nil
}

// ListTaskInfoForPage 分页查询任务
func (m *InfoProcessor) ListTaskInfoForPage(
	ctx context.Context, projectId string, pageNum, pageSize int32, pageType pb.TaskPageType,
) ([]*InfoItem, error) {
	logger := logx.WithContext(ctx)

	var key string
	switch pageType {
	case pb.TaskPageType_PT_ALL:
		key = redis.ConstRedisDispatcherTaskInfoAllKey
	case pb.TaskPageType_PT_ING:
		key = redis.ConstRedisDispatcherTaskInfoRunningKey
	case pb.TaskPageType_PT_ARCHIVE:
		key = redis.ConstRedisDispatcherTaskInfoArchiveKey
	}

	page, err := m.redService.ZRevRangeForPage(ctx, key, m.genProjectIdSuffix(projectId), pageNum, pageSize)
	if err != nil {
		return nil, err
	}
	items := make([]*InfoItem, 0, len(page))
	for _, v := range page {
		item := new(InfoItem)
		item.ProjectId = projectId
		item.TaskId = v
		err := m.GetTaskInfo(ctx, item)
		if err != nil {
			logger.Errorf("task ListTaskInfoForPage  failed, error: %s", err)
			return nil, err
		}
		/*【UI计划 -> 执行记录，任务结束了，排队耗时一直在增长】https://www.tapd.cn/66310153/bugtrace/bugs/view?bug_id=1166310153001125644*/
		if item.WaitTime == 0 {
			if item.TaskExecutedResult == int8(commonpb.ExecutedResult_TER_INIT) {
				if item.StartedAt <= 0 {
					item.WaitTime = time.Now().UnixMilli() - item.CreateTime
				} else {
					item.WaitTime = time.Now().UnixMilli() - item.StartedAt
				}
				/*if item.StartedAt > 0 {
					item.WaitTime = time.Now().UnixMilli() - item.StartedAt
				} else {
					item.WaitTime = time.Now().UnixMilli() - item.CreateTime
				}*/
			}
		}
		if item.CostTime == 0 {
			item.CostTime = time.Now().UnixMilli() - item.CreateTime
		}
		items = append(items, item)
	}
	return items, err
}

// ScanTaskInfoAllKey  probe:dispatcher:taskInfoZset:all:project_id:Kqllt5-9fA-I5UOdhjA5d
func (m *InfoProcessor) ScanTaskInfoAllKey(
	ctx context.Context,
) ([]string, error) {
	logger := logx.WithContext(ctx)

	scan, err := m.redService.Scan(ctx, redis.ConstRedisDispatcherTaskInfoAllKey+"*")
	if err != nil {
		logger.Errorf("task ListTaskInfoForPage  failed, error: %s", err)
		return nil, err
	}
	pList := make([]string, 0, len(scan))
	for _, v := range scan {
		if after, found := strings.CutPrefix(v, redis.ConstRedisDispatcherTaskInfoAllKey+":"); found {
			pList = append(pList, after)
		}
	}
	return pList, err
}

func (m *InfoProcessor) FindMoreThan7DayByProjectId(
	ctx context.Context, projectId string,
) ([]string, error) {
	logger := logx.WithContext(ctx)

	unixMilli := time.Now().AddDate(0, 0, -7).UnixMilli()
	formatInt := strconv.FormatInt(unixMilli, 10)
	page, err := m.redService.ZRangeByScore(
		ctx, redis.ConstRedisDispatcherTaskInfoAllKey, m.genProjectIdSuffix(projectId), "-inf", formatInt, 0, 0,
	)
	if err != nil {
		logger.Errorf("task ListTaskInfoForPage  failed, error: %s", err)
		return nil, err
	}
	return page, err
}

func (m *InfoProcessor) HandleMoreThan7Day(
	ctx context.Context, projectId string, members []string,
) error {
	logger := logx.WithContext(ctx)

	values := make([]any, 0, len(members))
	for _, member := range members {
		values = append(values, member)
	}
	hashKeys := make([]string, 0, len(members))
	for _, member := range members {
		hashKeys = append(hashKeys, m.genProjectIdSuffix(projectId)+":"+member)
	}
	if len(values) == 0 {
		return nil
	}
	// probe:dispatcher:taskInfoZset:all:project_id:Kqllt5-9fA-I5UOdhjA5d
	_, err := m.redService.ZRem(
		ctx, redis.ConstRedisDispatcherTaskInfoAllKey, m.genProjectIdSuffix(projectId), values...,
	)
	if err != nil {
		logger.Errorf("task HandleMoreThan7Day  failed, error: %s", err)
		return err
	}
	// probe:dispatcher:taskInfoZset:archive:project_id:Kqllt5-9fA-I5UOdhjA5d
	_, err = m.redService.ZRem(
		ctx, redis.ConstRedisDispatcherTaskInfoArchiveKey, m.genProjectIdSuffix(projectId), values...,
	)
	if err != nil {
		logger.Errorf("task HandleMoreThan7Day  failed, error: %s", err)
		return err
	}
	// probe:dispatcher:taskInfo:project_id:Kqllt5-9fA-I5UOdhjA5d:task_id:-Y3UtdsNcJ4x3Q0GGvf1D
	err = m.redService.DelKeys(ctx, redis.ConstRedisDispatcherTaskInfoKey, hashKeys...)
	if err != nil {
		logger.Errorf("task HandleMoreThan7Day  failed, error: %s", err)
	}
	return nil
}

// GetTaskInfoCount 查询总数
func (m *InfoProcessor) GetTaskInfoCount(
	ctx context.Context, projectId string, pageType pb.TaskPageType,
) (int, error) {
	var key string
	switch pageType {
	case pb.TaskPageType_PT_ALL:
		key = redis.ConstRedisDispatcherTaskInfoAllKey
	case pb.TaskPageType_PT_ING:
		key = redis.ConstRedisDispatcherTaskInfoRunningKey
	case pb.TaskPageType_PT_ARCHIVE:
		key = redis.ConstRedisDispatcherTaskInfoArchiveKey
	}

	total, err := m.redService.ZCard(ctx, key, m.genProjectIdSuffix(projectId))
	if err != nil {
		return 0, err
	}
	return total, err
}

func (m *InfoProcessor) GenRedisTaskIdAndProjectId(projectId, taskId string) string {
	return redis.ConstRedisDispatcherTaskInfoKey + m.genTaskIdAndProjectIdSuffix(projectId, taskId)
}

func (m *InfoProcessor) GenRedisTaskIdAndProjectIdStatusLock(projectId, taskId string) string {
	return redis.ConstRedisDispatcherTaskInfoStatusLockKey + m.genTaskIdAndProjectIdSuffix(projectId, taskId)
}

// return :{projectId}:{taskId}
func (m *InfoProcessor) genTaskIdAndProjectIdSuffix(projectId, taskId string) string {
	builder := strings.Builder{}
	builder.WriteString(":")
	builder.WriteString(projectId)
	builder.WriteString(":")
	builder.WriteString(taskId)
	return builder.String()
}

// return :{projectId}
func (m *InfoProcessor) genProjectIdSuffix(projectId string) string {
	builder := strings.Builder{}
	builder.WriteString(":")
	builder.WriteString(projectId)
	return builder.String()
}
