package v1alpha

import (
	"context"
	"crypto/tls"

	"github.com/zeromicro/go-zero/core/logx"
	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials"
)

type gClient struct {
	AnyResourceServiceClient
	rc credentials.PerRPCCredentials
}

var (
	_   GRPCClient = (*gClient)(nil)
	log logx.Logger
)

func NewGRPCClient(url, token string) (GRPCClient, error) {
	ctx := context.Background()
	log = logx.WithContext(ctx)

	g := &gClient{
		rc: newPerRPCCredential(token),
	}
	conn, err := grpc.DialContext(
		context.Background(), url,
		grpc.WithTransportCredentials(credentials.NewTLS(&tls.Config{MinVersion: tls.VersionTLS12})),
		grpc.WithChainUnaryInterceptor(g.withToken),
	)
	if err != nil {
		log.Errorf("failed to dial grpc, err: %v, target: %s", err, url)
		return nil, err
	}
	log.Infof("dial grpc success, target: %s", url)
	g.AnyResourceServiceClient = NewAnyResourceServiceClient(conn)
	return g, nil
}

var _ grpc.UnaryClientInterceptor = (*gClient)(nil).withToken

func (g *gClient) withToken(
	ctx context.Context, method string, req, reply any, cc *grpc.ClientConn, invoker grpc.UnaryInvoker,
	opts ...grpc.CallOption,
) error {
	return invoker(ctx, method, req, reply, cc, append(opts, grpc.PerRPCCredentials(g.rc))...)
}

// perRPCCredential implements "grpccredentials.PerRPCCredentials" interface.
type perRPCCredential struct {
	authToken string
}

var _ credentials.PerRPCCredentials = (*perRPCCredential)(nil)

func newPerRPCCredential(token string) *perRPCCredential { return &perRPCCredential{authToken: token} }

func (rc *perRPCCredential) RequireTransportSecurity() bool { return false }

func (rc *perRPCCredential) GetRequestMetadata(_ context.Context, _ ...string) (map[string]string, error) {
	return map[string]string{"X-TOKEN": rc.authToken}, nil
}
