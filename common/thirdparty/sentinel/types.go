package sentinel

type Config struct {
	BaseURL string `json:",default=https://cicd.ttyuyin.com"`
	Token   string
}

type BaseResp struct {
	Code int    `json:"code"`
	Data any    `json:"data"`
	Msg  string `json:"msg"`
}

type FindAppsResp struct {
	BaseResp
	Data []*App `json:"data"`
}

type App struct {
	Code        string `json:"code"`
	CmdbId      string `json:"cmdbId"`
	Name        string `json:"name"`
	Description string `json:"description"`
	RepoAddr    string `json:"repoAddr"`
	BuildPath   string `json:"buildPath"`
	LangName    string `json:"langName"`
	LangVersion string `json:"langVersion"`
	Status      int    `json:"status"`

	ProjectId          int          `json:"projectID"`
	ProjectName        string       `json:"projectName"`
	ProjectDescription string       `json:"projectDescription"`
	ProjectUserGroup   string       `json:"projectUserGroup"`
	ProjectExtra       ProjectExtra `json:"projectExtra"`
}

type ProjectExtra struct {
	CmdbTeamId   string `json:"cmdbTeamId"`
	CmdbTeamName string `json:"cmdbTeamName"`
}
