package sentinel

import (
	"reflect"
	"testing"

	"github.com/zeromicro/go-zero/core/jsonx"
)

func TestClient_GetAppByName(t *testing.T) {
	c := NewClient(
		Config{
			BaseURL: "https://cicd.ttyuyin.com",
			Token:   "cXVhbGl0eQ==.51448796d22758fce4a7928d613fcef3fdcd604aa60259227d9ac0fc0dcb0f5c",
		},
	)

	type args struct {
		app string
	}
	tests := []struct {
		name    string
		args    args
		want    *App
		wantErr bool
	}{
		{
			name: "channel-dating-game-logic",
			args: args{
				app: "channel-dating-game-logic",
			},
			want: &App{
				Code:               "channel-dating-game-logic(channel-dating-game)",
				CmdbId:             "64ffd2419d28f950499f8c7d",
				Name:               "channel-dating-game-logic",
				Description:        "相亲房go logic",
				RepoAddr:           "https://gitlab.ttyuyin.com/griffin/griffin.git",
				BuildPath:          "services/tt-rev/channel-dating-game-logic",
				LangName:           "go",
				LangVersion:        "1.21",
				Status:             1,
				ProjectId:          37,
				ProjectName:        "TT-营收",
				ProjectDescription: "",
				ProjectUserGroup:   "TT",
				ProjectExtra: ProjectExtra{
					CmdbTeamId:   "team_id:qFIUkLkPYxNCq5t_2NQS_",
					CmdbTeamName: "营收组",
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				got, err := c.GetAppByName(tt.args.app)
				if (err != nil) != tt.wantErr {
					t.Errorf("GetAppByName() error = %v, wantErr %v", err, tt.wantErr)
					return
				}

				t.Logf("GetAppByName() got: %s", jsonx.MarshalIgnoreError(got))

				if !reflect.DeepEqual(got, tt.want) {
					t.Errorf("GetAppByName() got = %v, want %v", got, tt.want)
				}
			},
		)
	}
}
