package sentinel

import (
	"fmt"
	"net/http"
	"net/url"

	"github.com/zeromicro/go-zero/core/jsonx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/transport/http/fasthttp"
)

type Client struct {
	c    *fasthttp.Client
	conf Config
}

func NewClient(conf Config) *Client {
	return &Client{
		c: fasthttp.NewClient(
			fasthttp.ClientConf{
				BaseURL: conf.BaseURL,
			},
		),
		conf: conf,
	}
}

func (c *Client) send(apiName string, req *fasthttp.Request, resp *fasthttp.Response) (
	body []byte, err error,
) {
	if err = c.c.Send(req, resp, requestTimeout); err != nil {
		return body, errorx.Errorf(
			errorx.CallExternalAPIFailure,
			"failed to call the api of Sentinel, api: %s, error: %+v",
			apiName, err,
		)
	}

	body = resp.Body()
	status := resp.StatusCode()
	if status != http.StatusOK {
		return body, errorx.Errorf(
			errorx.CallExternalAPIFailure,
			"failed to call the api of Sentinel, api: %s, status code: %d, resp body: %s",
			apiName, status, body,
		)
	}

	return body, err
}

func (c *Client) GetAppsByName(name string) ([]*App, error) {
	apiName := findAppsAPIName

	values := url.Values{}
	values.Add(queryOfAppName, name)

	req := fasthttp.NewRequest(
		fasthttp.SetURL(c.c.BuildURL(fmt.Sprintf("%s?%s", apiName, values.Encode()))),
		fasthttp.SetMethod(http.MethodGet),
		fasthttp.SetHeader(
			http.Header{
				headerOfXToken: {c.conf.Token},
			},
		),
	)
	defer fasthttp.ReleaseRequest(req)

	resp := fasthttp.AcquireResponse()
	defer fasthttp.ReleaseResponse(resp)

	body, err := c.send(apiName, req, resp)
	if err != nil {
		return nil, err
	}

	var out FindAppsResp
	if err = jsonx.Unmarshal(body, &out); err != nil {
		return nil, errorx.Errorf(
			errorx.SerializationError,
			"failed to unmarshal the response of Sentinel, api: %s, app: %s, resp body: %s, error: %+v",
			apiName, name, body, err,
		)
	}

	return out.Data, nil
}

func (c *Client) GetAppByName(name string) (*App, error) {
	apiName := findAppsAPIName

	apps, err := c.GetAppsByName(name)
	if err != nil {
		return nil, err
	} else if len(apps) == 0 {
		return nil, errorx.Errorf(
			errorx.NotExists,
			"not found any apps by name, api: %s, app: %s",
			apiName, name,
		)
	}

	return apps[0], nil
}
