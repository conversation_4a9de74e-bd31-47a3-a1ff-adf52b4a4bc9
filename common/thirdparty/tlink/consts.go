package tlink

import "time"

const (
	getQueryTemplatesAPIName = "/topology-trace-api/templates"
	queryTraceDataAPIName    = "/topology-trace-api/query"

	queryOfName   = "name"
	queryOfParams = "params"

	requestTimeout = 5 * time.Second
)

const (
	templateOfRelationMethodBindWorkload = "relation_method_bind_workload"
	templateOfRelationRewriteBindMethod  = "relation_rewrite_bind_method"
	templateOfRelationMethodToWorkload   = "relation_method_to_workload"
)

type EntityType string

const (
	EntityTypeOfWorkload   EntityType = "Workload"
	EntityTypeOfMethod     EntityType = "Method"
	EntityTypeOfHTTPMethod EntityType = "HTTPMethod"
)
