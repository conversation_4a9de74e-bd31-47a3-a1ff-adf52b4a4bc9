package tlink

// Config 系统拓扑配置
type Config struct {
	BaseURL string `json:",default=https://testing-internal-api.ttyuyin.com"`
}

type GetQueryTemplatesResp struct {
	Templates []*Template `json:"templates"`
}

type QueryTraceDataResp struct {
	Entities []*Entity `json:"entities"`
}

type Template struct {
	Name string `json:"name"` // 模板标识，用于查询调用链数据
	Desc string `json:"desc"` // 模板描述
}

type Entity struct {
	Label      string     `json:"label"`      // 对象类型，如：Workload（服务）、Method（方法）、TTCMD（TT命令号）、TTBBT（TT接口测试）
	Name       string     `json:"name"`       // 对象名称，如：服务名、方法名、命令号、TT接口测试编号
	Properties Properties `json:"properties"` // 对象附加属性
}

type Properties struct {
	Level     string `json:"level"`     // 服务等级
	Namespace string `json:"namespace"` // 命名空间
}
