package appInsight

import (
	"testing"
	"time"

	"github.com/zeromicro/go-zero/core/jsonx"
)

func TestClient_QueryRangeMetrics(t *testing.T) {
	c := NewClient(
		Config{
			BaseURL: "http://tt-telemetry-web.ttyuyin.com",
		},
	)
	now := time.Now().Truncate(30 * time.Second)

	type args struct {
		in *QueryRangeMetricsReq
	}
	tests := []struct {
		name    string
		args    args
		want    *QueryRangeMetricsResp
		wantErr bool
	}{
		{
			name: "qps - presentlogic.appsvr",
			args: args{
				in: &QueryRangeMetricsReq{
					Metric:    MetricTypeOfQPS,
					Workload:  "presentlogic",
					Namespace: "appsvr",
					StartedAt: now.Add(-5 * time.Minute),
					EndedAt:   now,
				},
			},
			want:    nil,
			wantErr: false,
		},
		{
			name: "p95 presentlogic.appsvr",
			args: args{
				in: &QueryRangeMetricsReq{
					Metric:    MetricTypeOfP95,
					Workload:  "presentlogic",
					Namespace: "appsvr",
					StartedAt: now.Add(-5 * time.Minute),
					EndedAt:   now,
				},
			},
			want:    nil,
			wantErr: false,
		},
		{
			name: "p95 channel-core-logic.quicksilver /logic.ChannelCoreLogic/ChannelEnterV2",
			args: args{
				in: &QueryRangeMetricsReq{
					Metric:    MetricTypeOfP95,
					Workload:  "channel-core-logic",
					Namespace: "quicksilver",
					Method:    "/logic.ChannelCoreLogic/ChannelEnterV2",
					StartedAt: now.Add(-5 * time.Minute),
					EndedAt:   now,
				},
			},
			want:    nil,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				got, err := c.QueryRangeMetrics(tt.args.in)
				if (err != nil) != tt.wantErr {
					t.Errorf("QueryRangeMetrics() error = %v, wantErr %v", err, tt.wantErr)
					return
				}

				t.Logf("got: %s", jsonx.MarshalIgnoreError(got))

				//if !reflect.DeepEqual(got, tt.want) {
				//	t.Errorf("QueryRangeMetrics() got = %v, want %v", got, tt.want)
				//}
			},
		)
	}
}

func TestClient_QueryResults2Metrics(t *testing.T) {
	c := NewClient(
		Config{
			BaseURL: "http://tt-telemetry-web.ttyuyin.com",
		},
	)

	// Last Sunday 20:00
	now := time.Now().Truncate(7 * 24 * time.Hour).Add(-12 * time.Hour)
	t.Logf("last sunday date: %s", now.Format("2006-01-02 15:04:05"))

	type args struct {
		in *QueryResults2MetricsReq
	}
	tests := []struct {
		name    string
		args    args
		want    *QueryResults2MetricsResp
		wantErr bool
	}{
		{
			name: "sum - presentlogic.appsvr",
			args: args{
				in: &QueryResults2MetricsReq{
					Metric:    MetricTypeOfSUM,
					Workload:  "presentlogic",
					Namespace: "appsvr",
					Method:    "/ga.api.present.PresentLogic/PresentGetUserPresentInfo",
					TimeRanges: []TimeRange{
						{
							StartedAt: now,
							EndedAt:   now.Add(3 * time.Hour),
						},
						{
							StartedAt: now.Add(-24 * time.Hour),
							EndedAt:   now.Add(-24*time.Hour + 3*time.Hour),
						},
						{
							StartedAt: now.Add(-1 * 7 * 24 * time.Hour),
							EndedAt:   now.Add(-1*7*24*time.Hour + 3*time.Hour),
						},
						{
							StartedAt: now.Add(-1*7*24*time.Hour - 24*time.Hour),
							EndedAt:   now.Add(-1*7*24*time.Hour - 24*time.Hour + 3*time.Hour),
						},
						// queries count must less than 5
						// {
						// 	StartedAt: now.Add(-2 * 7 * 24 * time.Hour),
						// 	EndedAt:   now.Add(-2*7*24*time.Hour + 3*time.Hour),
						// },
						// {
						// 	StartedAt: now.Add(-2*7*24*time.Hour - 24*time.Hour),
						// 	EndedAt:   now.Add(-2*7*24*time.Hour - 24*time.Hour + 3*time.Hour),
						// },
						// {
						// 	StartedAt: now.Add(-3 * 7 * 24 * time.Hour),
						// 	EndedAt:   now.Add(-3*7*24*time.Hour + 3*time.Hour),
						// },
						// {
						// 	StartedAt: now.Add(-3*7*24*time.Hour - 24*time.Hour),
						// 	EndedAt:   now.Add(-3*7*24*time.Hour - 24*time.Hour + 3*time.Hour),
						// },
					},
				},
			},
			want:    nil,
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				got, err := c.QueryResults2Metrics(tt.args.in)
				if (err != nil) != tt.wantErr {
					t.Errorf("QueryResults2Metrics() error = %v, wantErr %v", err, tt.wantErr)
					return
				}

				t.Logf("got: %s", jsonx.MarshalIgnoreError(got))
			},
		)
	}
}
