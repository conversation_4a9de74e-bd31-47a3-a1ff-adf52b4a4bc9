package appInsight

import (
	"net/http"
	"strconv"

	"github.com/zeromicro/go-zero/core/jsonx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/transport/http/fasthttp"
)

type Client struct {
	c    *fasthttp.Client
	conf Config
}

func NewClient(conf Config) *Client {
	return &Client{
		c: fasthttp.NewClient(
			fasthttp.ClientConf{
				BaseURL: conf.BaseURL,
			},
		),
		conf: conf,
	}
}

func (c *Client) QueryRangeMetrics(in *QueryRangeMetricsReq) (*QueryRangeMetricsResp, error) {
	got, err := c.queryRangeMetrics(convertReq(in))
	if err != nil {
		return nil, err
	}

	return convertResp(got)
}

func (c *Client) queryRangeMetrics(in *queryRangeMetricsReq) (*queryRangeMetricsResp, error) {
	apiName := queryRangeMetricsAPIName
	req := fasthttp.NewRequest(
		fasthttp.SetURL(c.c.BuildURL(apiName)),
		fasthttp.SetMethod(http.MethodPost),
		fasthttp.SetHeader(http.Header{"Content-Type": {"application/json"}}),
		fasthttp.SetBody(jsonx.MarshalIgnoreError(in)),
	)
	defer fasthttp.ReleaseRequest(req)

	resp := fasthttp.AcquireResponse()
	defer fasthttp.ReleaseResponse(resp)

	if err := c.c.Send(req, resp, requestTimeout); err != nil {
		return nil, errorx.Errorf(
			errorx.CallExternalAPIFailure,
			"failed to call the api of AppInsight, api: %s, error: %+v",
			apiName, err,
		)
	}

	body := resp.Body()
	status := resp.StatusCode()
	if status != http.StatusOK {
		return nil, errorx.Errorf(
			errorx.CallExternalAPIFailure,
			"failed to call the api of AppInsight, api: %s, status code: %d, resp body: %s",
			apiName, status, body,
		)
	}

	var out queryRangeMetricsResp
	if err := jsonx.Unmarshal(body, &out); err != nil {
		return nil, errorx.Errorf(
			errorx.SerializationError,
			"failed to unmarshal the response of AppInsight, api: %s, resp body: %s, error: %+v",
			apiName, body, err,
		)
	}

	return &out, nil
}

func convertReq(in *QueryRangeMetricsReq) *queryRangeMetricsReq {
	labels := &Labels{
		Namespace: []string{equalSign, in.Namespace},
		Workload:  []string{equalSign, in.Workload},
	}
	if in.Method != "" {
		labels.RequestPath = []string{equalSign, in.Method}
	}

	return &queryRangeMetricsReq{
		MetricName: metricName,
		Metric:     in.Metric,
		Start:      in.StartedAt.Unix(),
		End:        in.EndedAt.Unix(),
		Labels:     labels,
		Version:    version,
	}
}

func convertResp(in *queryRangeMetricsResp) (*QueryRangeMetricsResp, error) {
	if in.Code != 0 {
		return nil, errorx.Errorf(
			errorx.CallExternalAPIFailure,
			"the response of AppInsight is not OK, code: %d, msg: %s",
			in.Code, in.Msg,
		)
	}

	if len(in.Data) == 0 || len(in.Data[0].Points) == 0 {
		return &QueryRangeMetricsResp{Points: make([]Point, 0)}, nil
	}

	out := &QueryRangeMetricsResp{Points: make([]Point, 0, len(in.Data[0].Points))}
	for _, point := range in.Data[0].Points {
		if len(point) < 2 {
			continue
		}

		out.Points = append(
			out.Points, Point{
				Timestamp: Time(point[0]),
				Value:     SampleValue(point[1]),
			},
		)
	}

	return out, nil
}

func (c *Client) QueryResults2Metrics(in *QueryResults2MetricsReq) (*QueryResults2MetricsResp, error) {
	got, err := c.queryResults2Metrics(convertQueryResults2MetricsReq(in))
	if err != nil {
		return nil, err
	}

	return convertQueryResults2MetricsResp(got)
}

func convertQueryResults2MetricsReq(in *QueryResults2MetricsReq) *queryResultsMetricsReq {
	labels := &Labels{
		Namespace: []string{equalSign, in.Namespace},
		Workload:  []string{equalSign, in.Workload},
	}
	if in.Method != "" {
		labels.RequestPath = []string{equalSign, in.Method}
	}

	out := make(queryResultsMetricsReq, len(in.TimeRanges))
	for i, timeRange := range in.TimeRanges {
		out[strconv.Itoa(i)] = &queryResultMetricsReq{
			MetricName: metricName,
			Metric:     in.Metric,
			Start:      timeRange.StartedAt.Unix(),
			End:        timeRange.EndedAt.Unix(),
			Labels:     labels,
			Version:    version,
		}
	}

	return &out
}

func convertQueryResults2MetricsResp(in *queryResultsMetricsResp) (*QueryResults2MetricsResp, error) {
	if in.Code != 0 {
		return nil, errorx.Errorf(
			errorx.CallExternalAPIFailure,
			"the response of AppInsight is not OK, code: %d, msg: %s",
			in.Code, in.Msg,
		)
	}

	if len(in.Data) == 0 {
		return &QueryResults2MetricsResp{Points: make([]*SampleValue, 0)}, nil
	}

	out := &QueryResults2MetricsResp{Points: make([]*SampleValue, 0, len(in.Data))}
	for _, data := range in.Data {
		var point *SampleValue
		if len(data) > 0 && data[0].Point != nil {
			point = data[0].Point
		} else {
			continue
		}
		out.Points = append(out.Points, point)
	}

	return out, nil
}

func (c *Client) queryResults2Metrics(in *queryResultsMetricsReq) (*queryResultsMetricsResp, error) {
	apiName := queryResultsMetricsAPIName
	req := fasthttp.NewRequest(
		fasthttp.SetURL(c.c.BuildURL(apiName)),
		fasthttp.SetMethod(http.MethodPost),
		fasthttp.SetHeader(http.Header{"Content-Type": {"application/json"}}),
		fasthttp.SetBody(jsonx.MarshalIgnoreError(in)),
	)
	defer fasthttp.ReleaseRequest(req)

	resp := fasthttp.AcquireResponse()
	defer fasthttp.ReleaseResponse(resp)

	if err := c.c.Send(req, resp, requestTimeout); err != nil {
		return nil, errorx.Errorf(
			errorx.CallExternalAPIFailure,
			"failed to call the api of AppInsight, api: %s, error: %+v",
			apiName, err,
		)
	}

	body := resp.Body()
	status := resp.StatusCode()
	if status != http.StatusOK {
		return nil, errorx.Errorf(
			errorx.CallExternalAPIFailure,
			"failed to call the api of AppInsight, api: %s, status code: %d, resp body: %s",
			apiName, status, body,
		)
	}

	var out queryResultsMetricsResp
	if err := jsonx.Unmarshal(body, &out); err != nil {
		return nil, errorx.Errorf(
			errorx.SerializationError,
			"failed to unmarshal the response of AppInsight, api: %s, resp body: %s, error: %+v",
			apiName, body, err,
		)
	}

	return &out, nil
}
