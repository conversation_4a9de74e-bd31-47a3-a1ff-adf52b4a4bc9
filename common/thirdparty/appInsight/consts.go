package appInsight

import "time"

const (
	queryRangeMetricsAPIName   = "/metric/query_range"
	queryResultsMetricsAPIName = "/metric/query_results"
	metricName                 = "服务端指标"
	equalSign                  = "="
	version                    = "v2"

	requestTimeout = 5 * time.Second

	MaxQueryResultsCount = 5
)

type MetricType string

const (
	MetricTypeOfQPS       MetricType = "qps"
	MetricTypeOfFailRatio MetricType = "fail_ratio"
	MetricTypeOfP99       MetricType = "p99"
	MetricTypeOfP95       MetricType = "p95"
	MetricTypeOfP90       MetricType = "p90"
	MetricTypeOfP75       MetricType = "p75"
	MetricTypeOfP50       MetricType = "p50"
	MetricTypeOfSUM       MetricType = "sum"
)
