package sla

import (
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/types"
)

// Config SLA数据平台配置
type Config struct {
	BaseURL string `json:",default=http://10.211.0.61:10089"`
}

type GetComparisonDataReq struct {
	Platform    string             `json:"platform"`
	TaskID      string             `json:"task_id"`
	BaseTaskID  string             `json:"base_task_id"`
	TestVersion string             `json:"test_version"`
	BaseVersion string             `json:"base_version"`
	BaseLine    *types.SLABaseLine `json:"base_line"`
}

type GetComparisonDataResp struct {
	DeviceComparisonData    []DeviceComparisonData  `json:"device_comparison_data"`
	DeviceIsPassedOrNotData DeviceIsPassedOrNotData `json:"device_is_passed_or_not_data"`
	ParticipatingVersion    ParticipatingVersion    `json:"participating_version"`
}

type DeviceComparisonMetaData struct {
	DeviceName            string  `json:"device_name"`
	Version               string  `json:"version"`
	Counts                int     `json:"counts"`
	AvgFinishLaunchedTime int     `json:"avg_finish_launched_time"`
	AvgAutoLogin          int     `json:"avg_auto_login"`
	AvgNewHomePage        int     `json:"avg_new_home_page"`
	FinishLaunchedPercent float64 `json:"finish_launched_percent,omitempty"`
	AutoLoginPercent      float64 `json:"auto_login_percent,omitempty"`
	NewHomePagePercent    float64 `json:"new_home_page_percent,omitempty"`
}

type DeviceComparisonData struct {
	TestData *DeviceComparisonMetaData `json:"test_data,omitempty"`
	BaseData *DeviceComparisonMetaData `json:"base_data,omitempty"`
}
type DeviceIsPassedOrNotData struct {
	FinishLaunchedTimePassList []string `json:"finish_launched_time_pass_list"`
	FinishLaunchedTimeFailList []string `json:"finish_launched_time_fail_list"`
	AutoLoginPassList          []string `json:"auto_login_pass_list"`
	AutoLoginFailList          []string `json:"auto_login_fail_list"`
	NewHomePagePassList        []string `json:"new_home_page_pass_list"`
	NewHomePageFailList        []string `json:"new_home_page_fail_list"`
}
type ParticipatingVersion struct {
	TestVersion string `json:"test_version"`
	BaseVersion string `json:"base_version"`
}

type GetDetailsDataReq struct {
	Platform   string `form:"platform" validate:"required,oneof=Android IOS" zh:"测试系统（Android、IOS）"`
	Version    string `form:"version" validate:"required" zh:"版本名称"`
	DeviceName string `form:"device_name" validate:"required" zh:"设备编号"`
	TaskId     string `form:"task_id,optional" zh:"任务ID"`
}

type GetDetailsDataRespItem struct {
	DeviceName         string `json:"device_name"`
	Version            string `json:"version"`
	FinishLaunchedTime int    `json:"finish_launched_time"`
	AutoLogin          int    `json:"auto_login"`

	// ISO
	AutoLoginCmd10         *int `json:"auto_login_cmd_10"`
	AutoLoginConnect       *int `json:"auto_login_connect"`
	AutoLoginSendAuth      *int `json:"auto_login_send_auth"`
	Launch                 *int `json:"launch"`
	LaunchPreMain          *int `json:"launch_pre_main"`
	LaunchFinishLaunching  *int `json:"launch_finish_launching"`
	NewHomePage            int  `json:"new_home_page"`
	NewHomePageCmd3080     *int `json:"new_home_page_cmd_3080"`
	NewHomePageCmd3086     *int `json:"new_home_page_cmd_3086"`
	NewHomePageHomeJingang *int `json:"new_home_page_home_jingang"`
	NewHomePageHomeList    *int `json:"new_home_page_home_list"`

	AppInitHomeProcessStart     *int   `json:"app_init_home_process_start"`
	AppInitHomePreMain          *int   `json:"app_init_home_pre_main"`
	AppInitHomeMainDefaultAgree *int   `json:"app_init_home_main_default_agree"`
	AppInitHomeMainSplashBg     *int   `json:"app_init_home_main_splash_bg"`
	AppInitHomeCmdCore3093      *int   `json:"app_init_home_cmd_core_3093"`
	AppInitHomeCmd3093          *int   `json:"app_init_home_cmd_3093"`
	AppInitHomeMainShowSplashBg *int   `json:"app_init_home_main_show_splash_bg"`
	AppInitHomeMainSplash       *int   `json:"app_init_home_main_splash"`
	AppInitHomeHomeFirstFrame   *int   `json:"app_init_home_home_first_frame"`
	AppInitHomeHomeJingang      *int   `json:"app_init_home_home_jingang"`
	AppInitHomeHomeJingangUI    *int   `json:"app_init_home_home_jingang_ui"`
	AppInitHomeHomeListRoom     *int   `json:"app_init_home_home_list_room"`
	AppInitHomeCmdCore31526     *int   `json:"app_init_home_cmd_core_31526"`
	AppInitHomeCmd31526         *int   `json:"app_init_home_cmd_31526"`
	AppInitHomeHomeListTab      *int   `json:"app_init_home_home_list_tab"`
	AppInitHomeHomeList         *int   `json:"app_init_home_home_list"`
	InitMainProcess             *int   `json:"init_main_process"`
	InitMainProcessMid          *int   `json:"init_main_process_mid"`
	AfterModuleInitBackTask     *int   `json:"after_module_init_back_task"`
	AfterModuleInitMaintask     *int   `json:"after_module_init_main_task"`
	ModuleInitBackTask          *int   `json:"module_init_back_task"`
	ModuleInitBackTask2         *int   `json:"module_init_back_task_2"`
	InitMaintask                *int   `json:"init_main_task"`
	ModuleInitBackTask1         *int   `json:"module_init_back_task_1"`
	InitBackTask                *int   `json:"init_back_task"`
	PreModuleInitMaintask       *int   `json:"pre_module_init_main_task"`
	CreateTime                  string `json:"create_time"`
}

type GetVersionInfoReq struct {
	Platform string `form:"platform" validate:"required,oneof=Android IOS" zh:"测试系统（Android、IOS）"`
	Branch   string `form:"branch,optional" validate:"omitempty,oneof=release testing" zh:"版本分支"`
}
type GetVersionInfoRespItem struct {
	Version string `json:"version"`
	Branch  string `json:"branch"`
}

type GetDefaultBaseVersionReq struct {
	Platform string `form:"platform" validate:"required,oneof=Android IOS" zh:"测试系统（Android、IOS）"`
	Branch   string `form:"branch,optional" validate:"omitempty,oneof=release testing" zh:"版本分支"`
}

type GetDefaultBaseVersionResp struct {
	Code int                           `json:"code"`
	Data GetDefaultBaseVersionRespData `json:"data"`
}

type GetDefaultBaseVersionRespData struct {
	Platform    string `json:"platform"`
	Branch      string `json:"branch"`
	TestVersion string `json:"test_version"`
	BaseVersion string `json:"base_version"`
	TestTaskID  string `json:"test_task_id"`
	BaseTaskID  string `json:"base_task_id"`
}
