package sla

import (
	"testing"

	"github.com/zeromicro/go-zero/core/jsonx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/types"
)

func TestClient_GetComparisonData(t *testing.T) {
	c := NewClient(
		Config{
			BaseURL: "http://10.211.0.61:10089",
		},
	)
	got, err := c.GetComparisonData(&GetComparisonDataReq{
		Platform:    "Android",
		TestVersion: "6.67.5 18809",
		BaseVersion: "6.67.5 18804",
		BaseLine: &types.SLABaseLine{
			FinishLaunchedLine: 10,
			AutoLoginLine:      10,
			NewHomePageLine:    10,
		},
	})
	if err != nil {
		t.Fatal(err)
	}

	t.Logf("%s", jsonx.MarshalIgnoreError(got))
}

func TestClient_GetDetailsAndroidData(t *testing.T) {
	c := NewClient(
		Config{
			BaseURL: "http://10.211.0.61:10089",
		},
	)
	got, err := c.GetDetailsData(&GetDetailsDataReq{
		Platform:   "Android",
		Version:    "6.67.5 18809",
		DeviceName: "b008ad06",
		TaskId:     "task_id:_AObNMK8TiDGslr6XrTFV",
	})
	if err != nil {
		t.Fatal(err)
	}

	t.Logf("%s", jsonx.MarshalIgnoreError(got))
}

func TestClient_GetDetailsIOSData(t *testing.T) {
	c := NewClient(
		Config{
			BaseURL: "http://10.211.0.61:10089",
		},
	)
	got, err := c.GetDetailsData(&GetDetailsDataReq{
		Platform:   "IOS",
		Version:    "Ver_6.68.1/build_3942623",
		DeviceName: "00008110-000C4CA23C03801E",
		TaskId:     "task_id:0nxsiTtI41L-QBVGuytPv",
	})
	if err != nil {
		t.Fatal(err)
	}

	t.Logf("%s", jsonx.MarshalIgnoreError(got))
}

func TestClient_GetVersionInfo(t *testing.T) {
	c := NewClient(
		Config{
			BaseURL: "http://10.211.0.61:10089",
		},
	)
	got, err := c.GetVersionInfo(&GetVersionInfoReq{
		Platform: "Android",
		Branch:   "release",
	})
	if err != nil {
		t.Fatal(err)
	}

	t.Logf("%s", jsonx.MarshalIgnoreError(got))
}

func TestClient_GetDefaultBaseVersion(t *testing.T) {
	c := NewClient(
		Config{
			BaseURL: "http://10.211.0.61:10089",
		},
	)
	got, err := c.GetDefaultBaseVersion(&GetDefaultBaseVersionReq{
		Platform: "Android",
		Branch:   "release",
	})
	if err != nil {
		t.Fatal(err)
	}

	t.Logf("%s", jsonx.MarshalIgnoreError(got))
}
