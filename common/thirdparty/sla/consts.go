package sla

import "time"

const (
	getComparisonDataAPIName     = "/sla_api/getComparisonDataVersionAndTaskId"
	getDetailsDataAPIName        = "/sla_api/getDetailsData"
	getVersionInfoAPIName        = "/sla_api/getVersionInfo"
	getDefaultBaseVersionAPIName = "/sla_api/getVersionAndTaskId"

	queryOfPlatform   = "platform"
	queryOfVersion    = "version"
	queryOfDeviceName = "device_name"
	queryOfTaskId     = "task_id"
	queryOfBranch     = "branch"

	requestTimeout = 5 * time.Second
)
