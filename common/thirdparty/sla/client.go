package sla

import (
	"fmt"
	"net/http"
	"net/url"

	"github.com/zeromicro/go-zero/core/jsonx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/transport/http/fasthttp"
)

type Client struct {
	c    *fasthttp.Client
	conf Config
}

func NewClient(conf Config) *Client {
	return &Client{
		c: fasthttp.NewClient(
			fasthttp.ClientConf{
				BaseURL: conf.BaseURL,
			},
		),
		conf: conf,
	}
}

func (c *Client) send(apiName string, req *fasthttp.Request, resp *fasthttp.Response) (
	body []byte, err error,
) {
	if err = c.c.Send(req, resp, requestTimeout); err != nil {
		return body, errorx.Errorf(
			errorx.CallExternalAPIFailure,
			"failed to call the api of SLA data platform, api: %s, error: %+v",
			apiName, err,
		)
	}

	body = resp.Body()
	status := resp.StatusCode()
	if status != http.StatusOK {
		return body, errorx.Errorf(
			errorx.CallExternalAPIFailure,
			"failed to call the api of SLA data platform, api: %s, status code: %d, resp body: %s",
			apiName, status, body,
		)
	}

	return body, err
}

func (c *Client) GetComparisonData(in *GetComparisonDataReq) (*GetComparisonDataResp, error) {
	apiName := getComparisonDataAPIName
	req := fasthttp.NewRequest(
		fasthttp.SetURL(c.c.BuildURL(apiName)),
		fasthttp.SetMethod(http.MethodPost),
		fasthttp.SetHeader(http.Header{"Content-Type": {"application/json"}}),
		fasthttp.SetBody(jsonx.MarshalIgnoreError(in)),
	)
	defer fasthttp.ReleaseRequest(req)

	resp := fasthttp.AcquireResponse()
	defer fasthttp.ReleaseResponse(resp)

	body, err := c.send(apiName, req, resp)
	if err != nil {
		return nil, err
	}

	var out GetComparisonDataResp
	if err = jsonx.Unmarshal(body, &out); err != nil {
		return nil, errorx.Errorf(
			errorx.SerializationError,
			"failed to unmarshal the response of SLA data platform, api: %s, resp body: %s, error: %+v",
			apiName, body, err,
		)
	}

	return &out, nil
}

func (c *Client) GetDetailsData(in *GetDetailsDataReq) ([]*GetDetailsDataRespItem, error) {
	apiName := getDetailsDataAPIName

	values := url.Values{}
	values.Add(queryOfPlatform, in.Platform)
	values.Add(queryOfVersion, in.Version)
	values.Add(queryOfDeviceName, in.DeviceName)
	values.Add(queryOfTaskId, in.TaskId)

	req := fasthttp.NewRequest(
		fasthttp.SetURL(c.c.BuildURL(fmt.Sprintf("%s?%s", apiName, values.Encode()))),
		fasthttp.SetMethod(http.MethodGet),
	)
	defer fasthttp.ReleaseRequest(req)

	resp := fasthttp.AcquireResponse()
	defer fasthttp.ReleaseResponse(resp)

	body, err := c.send(apiName, req, resp)
	if err != nil {
		return nil, err
	}

	var out []*GetDetailsDataRespItem
	if err = jsonx.Unmarshal(body, &out); err != nil {
		return nil, errorx.Errorf(
			errorx.SerializationError,
			"failed to unmarshal the response of SLA data platform, api: %s, resp body: %s, error: %+v",
			apiName, body, err,
		)
	}

	return out, nil
}

func (c *Client) GetVersionInfo(in *GetVersionInfoReq) ([]*GetVersionInfoRespItem, error) {
	apiName := getVersionInfoAPIName

	values := url.Values{}
	values.Add(queryOfPlatform, in.Platform)
	values.Add(queryOfBranch, in.Branch)

	req := fasthttp.NewRequest(
		fasthttp.SetURL(c.c.BuildURL(fmt.Sprintf("%s?%s", apiName, values.Encode()))),
		fasthttp.SetMethod(http.MethodGet),
	)
	defer fasthttp.ReleaseRequest(req)

	resp := fasthttp.AcquireResponse()
	defer fasthttp.ReleaseResponse(resp)

	body, err := c.send(apiName, req, resp)
	if err != nil {
		return nil, err
	}

	var out []*GetVersionInfoRespItem
	if err = jsonx.Unmarshal(body, &out); err != nil {
		return nil, errorx.Errorf(
			errorx.SerializationError,
			"failed to unmarshal the response of SLA data platform, api: %s, resp body: %s, error: %+v",
			apiName, body, err,
		)
	}

	return out, nil
}

func (c *Client) GetDefaultBaseVersion(in *GetDefaultBaseVersionReq) (*GetDefaultBaseVersionResp, error) {
	apiName := getDefaultBaseVersionAPIName

	values := url.Values{}
	values.Add(queryOfPlatform, in.Platform)
	values.Add(queryOfBranch, in.Branch)

	req := fasthttp.NewRequest(
		fasthttp.SetURL(c.c.BuildURL(fmt.Sprintf("%s?%s", apiName, values.Encode()))),
		fasthttp.SetMethod(http.MethodGet),
	)
	defer fasthttp.ReleaseRequest(req)

	resp := fasthttp.AcquireResponse()
	defer fasthttp.ReleaseResponse(resp)

	body, err := c.send(apiName, req, resp)
	if err != nil {
		return nil, err
	}

	var out GetDefaultBaseVersionResp
	if err = jsonx.Unmarshal(body, &out); err != nil {
		return nil, errorx.Errorf(
			errorx.SerializationError,
			"failed to unmarshal the response of SLA data platform, api: %s, resp body: %s, error: %+v",
			apiName, body, err,
		)
	}

	return &out, nil
}
