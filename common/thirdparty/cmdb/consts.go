package cmdb

import "time"

const (
	getResourceModuleInfoAPIName = "/alarm/v2/resource/module_info/"

	labelKeyOfName       = "name"
	labelKeyOfIP         = "ip_list.ip"
	labelKeyOfDomainList = "domain_list"
	labelKeyOfURI        = "uri"
	labelKeyOfPrivateID  = "private_id"

	requestTimeout = 5 * time.Second
)

type ModelCode string

const (
	ModelCodeOfHost        ModelCode = "host"         // 主机
	ModelCodeOfApplication ModelCode = "application"  // 应用
	ModelCodeOfMongoDB     ModelCode = "mongodb"      // mongodb
	ModelCodeOfRedis       ModelCode = "redis"        // redis
	ModelCodeOfMysql       ModelCode = "mysql"        // mysql
	ModelCodeOfKafka       ModelCode = "kafka"        // kafka
	ModelCodeOfCMD         ModelCode = "cmd"          // 命令号
	ModelCodeOfNginxRouter ModelCode = "nginx_router" // nginx_router
)
