package cmdb

type Config struct {
	BaseURL       string `json:",default=http://yw-inner-cmdb.ttyuyin.com:5000"`
	Authorization string
}

type GetResourceModuleInfoReq struct {
	ModelCode ModelCode      `json:"model_code"` // 资源`model_code`
	Labels    map[string]any `json:"labels"`     // 查询条件
}

type GetResourceModuleInfoResp struct {
	ModuleList        []string    `json:"module_list"`         // 业务模块
	BizMaintainerList []*UserInfo `json:"biz_maintainer_list"` // 业务模块负责人
	OpsUserList       []*UserInfo `json:"ops_user_list"`       // 运维负责人
	DevelopUserList   []*UserInfo `json:"develop_user_list"`   // 研发人员
	DBAUserList       []*UserInfo `json:"dba_user_list"`       // DBA
}

type UserInfo struct {
	Email    string `json:"email"`
	Name     string `json:"name"`
	RealName string `json:"real_name"`
	UserNO   string `json:"user_no"`
}
