package cmdb

import (
	"testing"

	"github.com/zeromicro/go-zero/core/jsonx"
)

func TestClient_GetResourceModuleInfo(t *testing.T) {
	c := NewClient(
		Config{
			BaseURL:       "http://yw-inner-cmdb.ttyuyin.com:5000",
			Authorization: "B7Hr2AWz9UtQWjUpLpQIFI0i3fxYtCagY9njB3aK1sdiM9YBmFRraNqAmxwXIu5m",
		},
	)

	type args struct {
		in *GetResourceModuleInfoReq
	}
	tests := []struct {
		name    string
		args    args
		want    *GetResourceModuleInfoResp
		wantErr bool
	}{
		{
			name: "host - ip",
			args: args{
				in: MakeReqOfGetHostInfo("************"),
			},
			want:    nil,
			wantErr: false,
		},
		{
			name: "application - manager",
			args: args{
				in: MakeReqOfGetApplicationInfo("manager"),
			},
			want:    nil,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				got, err := c.GetResourceModuleInfo(tt.args.in)
				if (err != nil) != tt.wantErr {
					t.Errorf("GetResourceModuleInfo() error = %v, wantErr %v", err, tt.wantErr)
					return
				}

				t.Logf("got: %s", jsonx.MarshalIgnoreError(got))

				//if !reflect.DeepEqual(got, tt.want) {
				//	t.Errorf("GetResourceModuleInfo() got = %v, want %v", got, tt.want)
				//}
			},
		)
	}
}
