package monitor

import (
	"bytes"
	"context"
	"net/http"
	"net/url"
	"path"
	"strings"

	"github.com/prometheus/client_golang/api"
	promv1 "github.com/prometheus/client_golang/api/prometheus/v1"
	"github.com/zeromicro/go-zero/core/lang"
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/threading"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
)

var (
	_ promv1.API = (*Client)(nil)
	_ api.Client = (*httpClient)(nil)

	WithTimeout = promv1.WithTimeout
)

type Client struct {
	promv1.API

	conf Config
}

func MustNewClient(conf Config) *Client {
	c, err := NewClient(conf)
	if err != nil {
		logx.Must(err)
	}

	return c
}

func NewClient(conf Config) (*Client, error) {
	u, err := url.Parse(conf.BaseURL)
	if err != nil {
		return nil, err
	}
	u.Path = strings.TrimRight(u.Path, "/")

	return &Client{
		API: promv1.NewAPI(
			&httpClient{
				endpoint: u,
				client:   http.DefaultClient,
			},
		),
		conf: conf,
	}, nil
}

type httpClient struct {
	endpoint *url.URL
	client   *http.Client
}

func (c *httpClient) URL(ep string, args map[string]string) *url.URL {
	p := path.Join(c.endpoint.Path, ep)

	for arg, val := range args {
		arg = ":" + arg
		p = strings.ReplaceAll(p, arg, val)
	}

	u := *c.endpoint
	u.Path = p

	return &u
}

func (c *httpClient) Do(ctx context.Context, req *http.Request) (*http.Response, []byte, error) {
	if ctx != nil {
		req = req.WithContext(ctx)
	}

	// set User-Agent
	req.Header.Set("User-Agent", constants.ConstQualityPlatformName)

	resp, err := c.client.Do(req)
	defer func() {
		if resp != nil {
			_ = resp.Body.Close()
		}
	}()
	if err != nil {
		return nil, nil, err
	}

	var body []byte
	done := make(chan lang.PlaceholderType)
	threading.GoSafe(
		func() {
			defer close(done)

			var buf bytes.Buffer
			_, err = buf.ReadFrom(resp.Body)
			body = buf.Bytes()
		},
	)

	select {
	case <-ctx.Done():
		<-done
		err = resp.Body.Close()
		if err == nil {
			err = ctx.Err()
		}
	case <-done:
	}

	return resp, body, err
}
