package monitor

import (
	"context"
	"testing"
	"time"

	promv1 "github.com/prometheus/client_golang/api/prometheus/v1"
	prommodel "github.com/prometheus/common/model"
	"github.com/zeromicro/go-zero/core/jsonx"
)

func TestClient_Query(t *testing.T) {
	c, err := NewClient(
		Config{
			BaseURL: "https://yw-hw-bj-sre-monitor-api.ttyuyin.com",
		},
	)
	if err != nil {
		t.Fatalf("failed to new a monitor client, error: %+v", err)
	}

	timeout := 5 * time.Second
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	type args struct {
		query string
		ts    time.Time
	}
	tests := []struct {
		name string
		args args
	}{
		{
			name: "perftool - counter",
			args: args{
				query: `qet_business_request_total{app="perftool", task_id="task_id:1ZuhGMbdgje9PGV_wsbtz"}`,
				ts: func() time.Time {
					t_, _ := time.ParseInLocation("2006-01-02 15:04:05", "2024-09-09 14:25:11", time.Local)
					return t_
				}(),
			},
		},
		{
			name: "perftool - histogram - count",
			args: args{
				query: `qet_business_request_duration_count{app="perftool", task_id="task_id:1ZuhGMbdgje9PGV_wsbtz"}`,
				ts: func() time.Time {
					t_, _ := time.ParseInLocation("2006-01-02 15:04:05", "2024-09-09 14:25:11", time.Local)
					return t_
				}(),
			},
		},
		{
			name: "perftool - histogram - sum",
			args: args{
				query: `qet_business_request_duration_sum{app="perftool", task_id="task_id:1ZuhGMbdgje9PGV_wsbtz"}`,
				ts: func() time.Time {
					t_, _ := time.ParseInLocation("2006-01-02 15:04:05", "2024-09-09 14:25:11", time.Local)
					return t_
				}(),
			},
		},
		{
			name: "perftool - histogram - bucket",
			args: args{
				query: `qet_business_request_duration_bucket{app="perftool", task_id="task_id:1ZuhGMbdgje9PGV_wsbtz"}`,
				ts: func() time.Time {
					t_, _ := time.ParseInLocation("2006-01-02 15:04:05", "2024-09-09 14:25:11", time.Local)
					return t_
				}(),
			},
		},
		{
			name: "perftool - histogram - quantile",
			args: args{
				query: `histogram_quantile(0.99, sum(irate(qet_business_request_duration_bucket{app="perftool", task_id="task_id:1ZuhGMbdgje9PGV_wsbtz", name="获取我管理的房间列表-447", result="response succeeded"}[1m])) by (le))`,
				ts: func() time.Time {
					t_, _ := time.ParseInLocation("2006-01-02 15:04:05", "2024-09-09 14:25:00", time.Local)
					return t_
				}(),
			},
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				value, warnings, err := c.Query(ctx, tt.args.query, tt.args.ts, WithTimeout(timeout))
				if err != nil {
					t.Fatalf("failed to call the query api by monitor client, error: %+v", err)
				} else if warnings != nil {
					t.Errorf(
						"got some warnings while calling the query api by monitor client, warnings: %s",
						jsonx.MarshalIgnoreError(warnings),
					)
				}

				t.Logf("value: %s\n%s", value.Type(), value.String())

				vector, ok := value.(prommodel.Vector)
				if ok && len(vector) > 0 {
					t.Logf("sample value: %s", vector[0].Value.String())
				}
			},
		)
	}
}

func TestClient_QueryRange(t *testing.T) {
	c, err := NewClient(
		Config{
			BaseURL: "https://yw-hw-bj-sre-monitor-api.ttyuyin.com",
		},
	)
	if err != nil {
		t.Fatalf("failed to new a monitor client, error: %+v", err)
	}

	timeout := 5 * time.Second
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	type args struct {
		query  string
		range_ promv1.Range
	}
	tests := []struct {
		name string
		args args
	}{
		{
			name: "perftool - histogram - quantile",
			args: args{
				query: `histogram_quantile(0.99, sum(irate(qet_business_request_duration_bucket{app="perftool", task_id="task_id:1ZuhGMbdgje9PGV_wsbtz", name="获取我管理的房间列表-447", result="response succeeded"}[1m])) by (le))`,
				range_: promv1.Range{
					Start: func() time.Time {
						t_, _ := time.ParseInLocation("2006-01-02 15:04:05", "2024-09-09 14:23:04", time.Local)
						return t_
					}(),
					End: func() time.Time {
						t_, _ := time.ParseInLocation("2006-01-02 15:04:05", "2024-09-09 14:25:11", time.Local)
						return t_
					}(),
					Step: 30 * time.Second,
				},
			},
		},
		{
			name: "perftool - histogram - quantile",
			args: args{
				query: `histogram_quantile(0.9, rate(qet_business_perftool_request_duration_ms_bucket{app="perftool", task_id="task_id:4vD34DRMLgUVlwluKx1ce"}[2m]))`,
				range_: promv1.Range{
					Start: func() time.Time {
						t_, _ := time.ParseInLocation("2006-01-02 15:04:05", "2024-09-11 17:14:30", time.Local)
						return t_
					}(),
					End: func() time.Time {
						t_, _ := time.ParseInLocation("2006-01-02 15:04:05", "2024-09-11 17:16:30", time.Local)
						return t_
					}(),
					Step: 30 * time.Second,
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				value, warnings, err := c.QueryRange(ctx, tt.args.query, tt.args.range_, WithTimeout(timeout))
				if err != nil {
					t.Fatalf("failed to call the query api by monitor client, error: %+v", err)
				} else if warnings != nil {
					t.Errorf(
						"got some warnings while calling the query api by monitor client, warnings: %s",
						jsonx.MarshalIgnoreError(warnings),
					)
				}

				t.Logf("value: %s\n%s", value.Type(), value.String())

				vector, ok := value.(prommodel.Vector)
				if ok && len(vector) > 0 {
					t.Logf("sample value: %s", vector[0].Value.String())
				}
			},
		)
	}
}
