package clickPilot

import (
	"errors"
	"fmt"
	"math/rand"
	"sync"
	"time"

	"github.com/zeromicro/go-zero/core/jsonx"
	"github.com/zeromicro/go-zero/core/lang"
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/threading"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/timewheel"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
)

var (
	_ IClient = (*mockClient)(nil)

	r = rand.New(rand.NewSource(time.Now().UnixNano()))
)

type (
	mockClient struct {
		conf Config

		mutex sync.RWMutex
		tasks map[string]*taskItem
	}

	taskItem struct {
		task   *UITask
		status TaskStatus
		steps  []*StepRecord
		logs   string

		duration  time.Duration
		startedAt time.Time
		endedAt   time.Time
		stopCh    chan lang.PlaceholderType
	}
)

func NewMockClient(conf Config) IClient {
	return &mockClient{
		conf: conf,

		tasks: make(map[string]*taskItem, constants.ConstDefaultMakeMapSize),
	}
}

func (c *mockClient) CreateUITask(task *UITask) error {
	logx.Infof("mockClient create task, task: %s", jsonx.MarshalIgnoreError(task))

	if task == nil {
		return errors.New("task is nil")
	}

	c.mutex.Lock()
	defer c.mutex.Unlock()

	item := &taskItem{task: task}
	item.start()
	c.tasks[task.TaskID] = item
	return nil
}

func (c *mockClient) GetTaskStatus(taskID string) (TaskStatus, error) {
	logx.Infof("mockClient get task status, task: %s", jsonx.MarshalIgnoreError(taskID))

	c.mutex.RLock()
	defer c.mutex.RUnlock()

	v, ok := c.tasks[taskID]
	if !ok {
		return "", errors.New("task not found")
	}

	return v.status, nil
}

func (c *mockClient) GetTaskRecord(taskID string) ([]*StepRecord, error) {
	logx.Infof("mockClient get task record, task: %s", jsonx.MarshalIgnoreError(taskID))

	c.mutex.RLock()
	defer c.mutex.RUnlock()

	v, ok := c.tasks[taskID]
	if !ok {
		return nil, errors.New("task not found")
	}

	return v.steps, nil
}

func (c *mockClient) GetTaskLog(taskID string) (string, error) {
	logx.Infof("mockClient get task log, task: %s", jsonx.MarshalIgnoreError(taskID))

	c.mutex.RLock()
	defer c.mutex.RUnlock()

	v, ok := c.tasks[taskID]
	if !ok {
		return "", errors.New("task not found")
	}

	return v.logs, nil
}

func (c *mockClient) StopTask(taskID string) error {
	logx.Infof("mockClient stop task, task_id: %s", taskID)

	c.mutex.RLock()
	v, ok := c.tasks[taskID]
	c.mutex.RUnlock()
	if !ok {
		return errors.New("task not found")
	}

	c.mutex.Lock()
	v.status = TaskStatusOfTerminated
	v.endedAt = time.Now()
	c.mutex.Unlock()
	return nil
}

func (c *mockClient) DeleteTask(taskID string) error {
	logx.Infof("mockClient delete task, task_id: %s", taskID)

	c.mutex.Lock()
	defer c.mutex.Unlock()

	delete(c.tasks, taskID)
	return nil
}

func (i *taskItem) start() {
	i.status = TaskStatusOfProcessing
	i.logs = "开始执行\n"
	i.duration = time.Duration(r.Intn(30)+5) * time.Second // [5, 30)
	i.startedAt = time.Now()
	i.stopCh = make(chan lang.PlaceholderType)

	threading.GoSafe(
		func() {
			ticker := timewheel.NewTicker(2 * time.Second)
			defer ticker.Stop()

			timer := timewheel.NewTimer(i.duration)
			defer timer.Stop()

			for {
				select {
				case <-i.stopCh:
					return
				case <-ticker.C:
					count := len(i.steps)
					if count != 0 {
						now := time.Now()
						st, _ := time.Parse(time.DateTime, i.steps[count-1].StartedAt)

						i.steps[count-1].Status = StepStatusOfCompleted
						i.steps[count-1].EndedAt = now.Format(time.DateTime)
						i.steps[count-1].CostTime = now.Sub(st).Seconds()
					}

					index := count + 1
					i.steps = append(
						i.steps, &StepRecord{
							Index:     int64(index),
							Name:      fmt.Sprintf("步骤%d", index),
							Thought:   fmt.Sprintf("思考步骤%d，如何执行，请稍后", index),
							Action:    "tap",
							Status:    StepStatusOfRunning,
							StartedAt: time.Now().Format(time.DateTime),
						},
					)
					i.logs = fmt.Sprintf("%s\n步骤%d的日志", i.logs, index)
				case <-timer.C:
					count := len(i.steps)
					if count != 0 {
						if i.steps[count-1].Status == StepStatusOfRunning {
							now := time.Now()
							st, _ := time.Parse(time.DateTime, i.steps[count-1].StartedAt)

							i.steps[count-1].Status = StepStatusOfCompleted
							i.steps[count-1].EndedAt = now.Format(time.DateTime)
							i.steps[count-1].CostTime = now.Sub(st).Seconds()
						}
					}

					i.status = TaskStatusOfSucceed
					i.endedAt = time.Now()
					return
				}
			}
		},
	)
}

func (i *taskItem) stop() {
	if !i.endedAt.IsZero() {
		return
	}

	i.status = TaskStatusOfTerminated
	i.endedAt = time.Now()
	close(i.stopCh)
}
