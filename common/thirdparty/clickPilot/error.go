package clickPilot

type Error struct {
	code    int
	message string
	err     error
}

func NewError(code int, message string, err error) *Error {
	return &Error{
		code:    code,
		message: message,
		err:     err,
	}
}

func (e *Error) Error() string {
	return e.err.Error()
}

func (e *Error) Code() int {
	return e.code
}

func (e *Error) Message() string {
	return e.message
}

func (e *Error) Unwrap() error {
	return e.err
}
