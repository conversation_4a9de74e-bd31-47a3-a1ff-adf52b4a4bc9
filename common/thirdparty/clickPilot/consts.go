package clickPilot

import "time"

const (
	createUITaskAPIName  = "/api/v1/ui-task/create"
	getTaskStatusAPIName = "/api/v1/ui-task/status/get"
	getTaskRecordAPIName = "/api/v1/ui-task/record/get"
	getTaskLogAPIName    = "/api/v1/ui-task/log/get"
	stopTaskAPIName      = "/api/v1/ui-task/stop"
	deleteTaskAPIName    = "/api/v1/ui-task/delete"

	queryOfTaskID        = "task_id"
	defaultAgentConfigID = "tt"

	requestTimeout = 5 * time.Second
)

// AgentType 代理类型
type AgentType string

const (
	AgentTypeOfAndroid AgentType = "android"
	AgentTypeOfIOS     AgentType = "ios"
)

// DeviceType 设备类型
type DeviceType string

const (
	DeviceTypeOfAndroid DeviceType = "android"
	DeviceTypeOfIOS     DeviceType = "ios"
)

// TaskStatus 任务状态
type TaskStatus string

const (
	TaskStatusOfNull TaskStatus = "" // 空

	TaskStatusOfProcessing TaskStatus = "processing" // 执行中
	TaskStatusOfSucceed    TaskStatus = "succeed"    // 成功
	TaskStatusOfFailed     TaskStatus = "failed"     // 失败
	TaskStatusOfTerminated TaskStatus = "terminate"  // 已终止
)

// StepStatus 步骤状态
type StepStatus string

const (
	StepStatusOfNull StepStatus = "" // 空

	StepStatusOfRunning    StepStatus = "running"   // 运行中
	StepStatusOfCompleted  StepStatus = "completed" // 已完成
	StepStatusOfFailed     StepStatus = "failed"    // 失败
	StepStatusOfTerminated StepStatus = "terminate" // 已终止
)
