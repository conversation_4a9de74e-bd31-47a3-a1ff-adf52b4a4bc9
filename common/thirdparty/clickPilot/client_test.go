package clickPilot

import (
	"testing"

	"github.com/zeromicro/go-zero/core/jsonx"
)

const testBaseURL = "http://10.64.168.45:8888"

func TestClient_GetTaskStatus(t *testing.T) {
	c := NewClient(
		Config{
			BaseURL: testBaseURL,
		},
	)

	type args struct {
		taskID string
	}
	tests := []struct {
		name    string
		args    args
		want    TaskStatus
		wantErr bool
	}{
		{
			name: "get task status",
			args: args{
				taskID: "execute_id:xQdMwZ_0MtSQCvREg88OF",
			},
			want:    TaskStatusOfSucceed,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				got, err := c.GetTaskStatus(tt.args.taskID)
				if (err != nil) != tt.wantErr {
					t.Errorf("GetTaskStatus() error = %v, wantErr %v", err, tt.wantErr)
					return
				}
				if got != tt.want {
					t.Errorf("GetTaskStatus() got = %v, want %v", got, tt.want)
				}
			},
		)
	}
}

func TestClient_GetTaskRecord(t *testing.T) {
	c := NewClient(
		Config{
			BaseURL: testBaseURL,
		},
	)

	type args struct {
		taskID string
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name: "",
			args: args{
				taskID: "execute_id:xQdMwZ_0MtSQCvREg88OF",
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				got, err := c.GetTaskRecord(tt.args.taskID)
				if (err != nil) != tt.wantErr {
					t.Errorf("GetTaskRecord() error = %v, wantErr %v", err, tt.wantErr)
					return
				}
				t.Logf("GetTaskRecord() got = %s", jsonx.MarshalIgnoreError(got))
			},
		)
	}
}
