package wiseEyes

import (
	"net/http"

	"github.com/zeromicro/go-zero/core/jsonx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/transport/http/fasthttp"
)

type Client struct {
	c    *fasthttp.Client
	conf Config
}

func NewClient(conf Config) *Client {
	return &Client{
		c: fasthttp.NewClient(
			fasthttp.ClientConf{
				BaseURL: conf.BaseURL,
			},
		),
		conf: conf,
	}
}

func (c *Client) SearchLog(in *SearchLogReq) (*SearchLogResp, error) {
	apiName := searchAPIName
	req := fasthttp.NewRequest(
		fasthttp.SetURL(c.c.BuildURL(apiName)),
		fasthttp.SetMethod(http.MethodPost),
		fasthttp.SetHeader(
			http.Header{
				"Authorization": {c.conf.Authorization},
				"Content-Type":  {"application/json"},
			},
		),
		fasthttp.SetBody(jsonx.MarshalIgnoreError(in)),
	)
	defer fasthttp.ReleaseRequest(req)

	resp := fasthttp.AcquireResponse()
	defer fasthttp.ReleaseResponse(resp)

	if err := c.c.Send(req, resp, requestTimeout); err != nil {
		return nil, errorx.Errorf(
			errorx.CallExternalAPIFailure,
			"failed to call the api of WiseEyes, api: %s, error: %+v",
			apiName, err,
		)
	}

	status := resp.StatusCode()
	body := resp.Body()
	if status != http.StatusOK {
		return nil, errorx.Errorf(
			errorx.CallExternalAPIFailure,
			"failed to call the api of WiseEyes, api: %s, status code: %d, resp body: %s",
			apiName, status, body,
		)
	}

	var out SearchLogResp
	if err := jsonx.Unmarshal(body, &out); err != nil {
		return nil, errorx.Errorf(
			errorx.SerializationError,
			"failed to unmarshal the response of WiseEyes, api: %s, resp body: %s, error: %+v",
			apiName, body, err,
		)
	}

	return &out, nil
}
