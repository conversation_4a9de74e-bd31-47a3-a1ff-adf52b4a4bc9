package wiseEyes

// Config 日志平台（慧眸）配置
type Config struct {
	BaseURL       string `json:",default=https://yw-rd-logs.ttyuyin.com"`
	Authorization string
	Topic         string
}

type SearchLogReq struct {
	TopicName string `json:"topic_name"`
	Query     string `json:"query"`
	From      int64  `json:"from"`
	To        int64  `json:"to"`
	Context   string `json:"context"`
	Sort      string `json:"sort,default=asc,options=asc|desc"`
}

type SearchLogResp struct {
	Response *SearchLogRespItem `json:"Response"`
}

type SearchLogRespItem struct {
	// 透传本次接口返回的Context值，可获取后续更多日志，过期时间1小时。
	// 注意：
	// * 仅适用于单日志主题检索，检索多个日志主题时，请使用Topics中的Context
	Context string `json:"Context"`

	// 符合检索条件的日志是否已全部返回，如未全部返回可使用Context参数获取后续更多日志
	// 注意：仅当检索分析语句(Query)不包含SQL时有效
	ListOver bool `json:"ListOver"`

	// 返回的是否为统计分析（即SQL）结果
	Analysis bool `json:"Analysis"`

	// 日志统计分析结果的列名
	// 当UseNewAnalysis为false时生效
	// 注意：此字段可能返回 null，表示取不到有效值。
	ColNames []string `json:"ColNames"`

	// 日志统计分析结果的列属性
	// 当UseNewAnalysis为true时生效
	// 注意：此字段可能返回 null，表示取不到有效值。
	Columns []*Column `json:"Columns"`

	// 匹配检索条件的原始日志
	// 注意：此字段可能返回 null，表示取不到有效值。
	Results []*LogInfo `json:"Results"`

	// 日志统计分析结果
	// 当UseNewAnalysis为false时生效
	// 注意：此字段可能返回 null，表示取不到有效值。
	AnalysisResults []*LogItems `json:"AnalysisResults"`

	// 日志统计分析结果
	// 当UseNewAnalysis为true时生效
	// 注意：此字段可能返回 null，表示取不到有效值。
	AnalysisRecords []string `json:"AnalysisRecords"`

	// 唯一请求 ID，由服务端生成，每次请求都会返回（若请求因其他原因未能抵达服务端，则该次请求不会获得 RequestId）。定位问题时需要提供该次请求的 RequestId。
	RequestId string `json:"RequestId"`

	// 本次统计分析使用的采样率
	// 注意：此字段可能返回 null，表示取不到有效值。
	SamplingRate float64 `json:"SamplingRate"`

	// 使用多日志主题检索时，各个日志主题的基本信息，例如报错信息。
	// 注意：此字段可能返回 null，表示取不到有效值。
	Topics *SearchLogTopics `json:"Topics"`
}

type Column struct {
	// 列的名字
	Name string `json:"Name"`

	// 列的属性
	Type string `json:"Type"`
}

type LogInfo struct {
	// 日志时间，单位ms
	Time int64 `json:"Time"`

	// 日志主题ID
	TopicId string `json:"TopicId"`

	// 日志主题名称
	TopicName string `json:"TopicName"`

	// 日志来源IP
	Source string `json:"Source"`

	// 日志文件名称
	FileName string `json:"FileName"`

	// 日志来源主机名称
	// 注意：此字段可能返回 null，表示取不到有效值。
	HostName string `json:"HostName"`

	// 日志上报请求包的ID
	PkgId string `json:"PkgId"`

	// 请求包内日志的ID
	PkgLogId string `json:"PkgLogId"`

	// 日志内容的高亮描述信息
	// 注意：此字段可能返回 null，表示取不到有效值。
	HighLights []*HighLightItem `json:"HighLights"`

	// 日志内容的Json序列化字符串
	// 注意：此字段可能返回 null，表示取不到有效值。
	LogJson string `json:"LogJson"`

	// 原始日志(仅在日志创建索引异常时有值)
	// 注意：此字段可能返回 null，表示取不到有效值。
	RawLog string `json:"RawLog"`

	// 日志创建索引异常原因(仅在日志创建索引异常时有值)
	// 注意：此字段可能返回 null，表示取不到有效值。
	IndexStatus string `json:"IndexStatus"`
}

type LogItems struct {
	// 分析结果返回的KV数据对
	Data []*LogItem `json:"Data"`
}

type LogItem struct {
	// 日志Key
	Key string `json:"Key"`

	// 日志Value
	Value string `json:"Value"`
}

type HighLightItem struct {
	// 高亮的日志Key
	Key string `json:"Key"`

	// 高亮的语法
	Values []string `json:"Values"`
}

type SearchLogTopics struct {
	// 多日志主题检索对应的错误信息
	// 注意：此字段可能返回 null，表示取不到有效值。
	Errors []*SearchLogErrors `json:"Errors"`

	// 多日志主题检索各日志主题信息
	// 注意：此字段可能返回 null，表示取不到有效值。
	Infos []*SearchLogInfos `json:"Infos"`
}

type SearchLogErrors struct {
	// 日志主题ID
	// 注意：此字段可能返回 null，表示取不到有效值。
	TopicId string `json:"TopicId"`

	// 错误信息
	// 注意：此字段可能返回 null，表示取不到有效值。
	ErrorMsg string `json:"ErrorMsg"`

	// 错误码
	// 注意：此字段可能返回 null，表示取不到有效值。
	ErrorCodeStr string `json:"ErrorCodeStr"`
}

type SearchLogInfos struct {
	// 日志主题ID
	TopicId string `json:"TopicId"`

	// 日志存储生命周期
	Period int64 `json:"Period"`

	// 透传本次接口返回的Context值，可获取后续更多日志，过期时间1小时
	// 注意：此字段可能返回 null，表示取不到有效值。
	Context string `json:"Context"`
}
