package cfgDispatcher

import (
	"context"
	"crypto/tls"
	"testing"
	"time"

	"github.com/zeromicro/go-zero/zrpc"
	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"

	cfg_dispatcher "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/thirdparty/cfgDispatcher/pb/services/cfg-dispatcher"
)

func TestGetTransportConfigV2(t *testing.T) {
	cli := zrpc.MustNewClient(
		zrpc.RpcClientConf{
			Target:   "api-internal.ttyuyin.com:80", // 内部域名，办公网访问不了
			NonBlock: true,
		},
		zrpc.WithDialOption(grpc.WithTransportCredentials(credentials.NewTLS(&tls.Config{MinVersion: tls.VersionTLS12}))),
		zrpc.WithDialOption(grpc.WithAuthority("cfg-dispatcher.52tt.local")),
	)
	c := cfg_dispatcher.NewCfgDispatcherClient(cli.Conn())

	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	out, err := c.GetTransportConfigV2(
		ctx, &cfg_dispatcher.GetTransportConfigV2Request{
			AppInfo: &cfg_dispatcher.AppInfo{
				ClientType:    cfg_dispatcher.ClientType_ENUM_CT_ANDROID,
				AppVersionInt: 104464387,
				MarketId:      0,
			},
		},
	)
	if err != nil {
		t.Fatal(err)
	}

	t.Logf("out: %s", protobuf.MarshalJSONIgnoreError(out.GetTransportConfig_V2()))
}
