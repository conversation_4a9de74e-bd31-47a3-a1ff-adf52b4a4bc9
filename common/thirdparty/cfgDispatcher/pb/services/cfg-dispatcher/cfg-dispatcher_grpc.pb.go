// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v4.25.2
// source: tt/quicksilver/cfg-dispatcher/cfg-dispatcher.proto

package cfg_dispatcher

import (
	context "context"

	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	CfgDispatcher_GetGrpcEndpoints_FullMethodName     = "/cfg_dispatcher.CfgDispatcher/GetGrpcEndpoints"
	CfgDispatcher_GetTransportConfigV2_FullMethodName = "/cfg_dispatcher.CfgDispatcher/GetTransportConfigV2"
)

// CfgDispatcherClient is the client API for CfgDispatcher service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type CfgDispatcherClient interface {
	//先放在这个服务，获取客户端grpc通道的配置接口
	GetGrpcEndpoints(ctx context.Context, in *GetGrpcEndpointsReq, opts ...grpc.CallOption) (*GetGrpcEndpointsResp, error)
	GetTransportConfigV2(ctx context.Context, in *GetTransportConfigV2Request, opts ...grpc.CallOption) (*GetTransportConfigV2Response, error)
}

type cfgDispatcherClient struct {
	cc grpc.ClientConnInterface
}

func NewCfgDispatcherClient(cc grpc.ClientConnInterface) CfgDispatcherClient {
	return &cfgDispatcherClient{cc}
}

func (c *cfgDispatcherClient) GetGrpcEndpoints(ctx context.Context, in *GetGrpcEndpointsReq, opts ...grpc.CallOption) (*GetGrpcEndpointsResp, error) {
	out := new(GetGrpcEndpointsResp)
	err := c.cc.Invoke(ctx, CfgDispatcher_GetGrpcEndpoints_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cfgDispatcherClient) GetTransportConfigV2(ctx context.Context, in *GetTransportConfigV2Request, opts ...grpc.CallOption) (*GetTransportConfigV2Response, error) {
	out := new(GetTransportConfigV2Response)
	err := c.cc.Invoke(ctx, CfgDispatcher_GetTransportConfigV2_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// CfgDispatcherServer is the server API for CfgDispatcher service.
// All implementations must embed UnimplementedCfgDispatcherServer
// for forward compatibility
type CfgDispatcherServer interface {
	//先放在这个服务，获取客户端grpc通道的配置接口
	GetGrpcEndpoints(context.Context, *GetGrpcEndpointsReq) (*GetGrpcEndpointsResp, error)
	GetTransportConfigV2(context.Context, *GetTransportConfigV2Request) (*GetTransportConfigV2Response, error)
	mustEmbedUnimplementedCfgDispatcherServer()
}

// UnimplementedCfgDispatcherServer must be embedded to have forward compatible implementations.
type UnimplementedCfgDispatcherServer struct {
}

func (UnimplementedCfgDispatcherServer) GetGrpcEndpoints(context.Context, *GetGrpcEndpointsReq) (*GetGrpcEndpointsResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetGrpcEndpoints not implemented")
}
func (UnimplementedCfgDispatcherServer) GetTransportConfigV2(context.Context, *GetTransportConfigV2Request) (*GetTransportConfigV2Response, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetTransportConfigV2 not implemented")
}
func (UnimplementedCfgDispatcherServer) mustEmbedUnimplementedCfgDispatcherServer() {}

// UnsafeCfgDispatcherServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to CfgDispatcherServer will
// result in compilation errors.
type UnsafeCfgDispatcherServer interface {
	mustEmbedUnimplementedCfgDispatcherServer()
}

func RegisterCfgDispatcherServer(s grpc.ServiceRegistrar, srv CfgDispatcherServer) {
	s.RegisterService(&CfgDispatcher_ServiceDesc, srv)
}

func _CfgDispatcher_GetGrpcEndpoints_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGrpcEndpointsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CfgDispatcherServer).GetGrpcEndpoints(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CfgDispatcher_GetGrpcEndpoints_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CfgDispatcherServer).GetGrpcEndpoints(ctx, req.(*GetGrpcEndpointsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _CfgDispatcher_GetTransportConfigV2_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTransportConfigV2Request)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CfgDispatcherServer).GetTransportConfigV2(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CfgDispatcher_GetTransportConfigV2_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CfgDispatcherServer).GetTransportConfigV2(ctx, req.(*GetTransportConfigV2Request))
	}
	return interceptor(ctx, in, info, handler)
}

// CfgDispatcher_ServiceDesc is the grpc.ServiceDesc for CfgDispatcher service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var CfgDispatcher_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "cfg_dispatcher.CfgDispatcher",
	HandlerType: (*CfgDispatcherServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetGrpcEndpoints",
			Handler:    _CfgDispatcher_GetGrpcEndpoints_Handler,
		},
		{
			MethodName: "GetTransportConfigV2",
			Handler:    _CfgDispatcher_GetTransportConfigV2_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "tt/quicksilver/cfg-dispatcher/cfg-dispatcher.proto",
}
