// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.32.0
// 	protoc        v4.25.2
// source: tt/quicksilver/cfg-dispatcher/cfg-dispatcher.proto

package cfg_dispatcher

import (
	reflect "reflect"
	sync "sync"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"

	grpc_transport_cfg "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/thirdparty/cfgDispatcher/pb/app/grpc_transport_cfg"
	transport "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/thirdparty/cfgDispatcher/pb/app/transport"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 跟EOSType对齐
type ClientType int32

const (
	ClientType_ENUM_CT_UNKNOWN  ClientType = 0
	ClientType_ENUM_CT_ANDROID  ClientType = 1
	ClientType_ENUM_CT_IOS      ClientType = 2
	ClientType_ENUM_CT_WINPHONE ClientType = 3
	ClientType_ENUM_CT_MACOSX   ClientType = 4
	ClientType_ENUM_CT_WINDOWS  ClientType = 5
	ClientType_ENUM_CT_LINUX    ClientType = 6
	ClientType_ENUM_CT_MAX      ClientType = 15
)

// Enum value maps for ClientType.
var (
	ClientType_name = map[int32]string{
		0:  "ENUM_CT_UNKNOWN",
		1:  "ENUM_CT_ANDROID",
		2:  "ENUM_CT_IOS",
		3:  "ENUM_CT_WINPHONE",
		4:  "ENUM_CT_MACOSX",
		5:  "ENUM_CT_WINDOWS",
		6:  "ENUM_CT_LINUX",
		15: "ENUM_CT_MAX",
	}
	ClientType_value = map[string]int32{
		"ENUM_CT_UNKNOWN":  0,
		"ENUM_CT_ANDROID":  1,
		"ENUM_CT_IOS":      2,
		"ENUM_CT_WINPHONE": 3,
		"ENUM_CT_MACOSX":   4,
		"ENUM_CT_WINDOWS":  5,
		"ENUM_CT_LINUX":    6,
		"ENUM_CT_MAX":      15,
	}
)

func (x ClientType) Enum() *ClientType {
	p := new(ClientType)
	*p = x
	return p
}

func (x ClientType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ClientType) Descriptor() protoreflect.EnumDescriptor {
	return file_tt_quicksilver_cfg_dispatcher_cfg_dispatcher_proto_enumTypes[0].Descriptor()
}

func (ClientType) Type() protoreflect.EnumType {
	return &file_tt_quicksilver_cfg_dispatcher_cfg_dispatcher_proto_enumTypes[0]
}

func (x ClientType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ClientType.Descriptor instead.
func (ClientType) EnumDescriptor() ([]byte, []int) {
	return file_tt_quicksilver_cfg_dispatcher_cfg_dispatcher_proto_rawDescGZIP(), []int{0}
}

type EndpointV2_TLSConfig_TLSMode int32

const (
	EndpointV2_TLSConfig_TLS_MODE_DISABLE EndpointV2_TLSConfig_TLSMode = 0
	EndpointV2_TLSConfig_TLS_MODE_SIMPLE  EndpointV2_TLSConfig_TLSMode = 1
	EndpointV2_TLSConfig_TLS_MODE_MUTUAL  EndpointV2_TLSConfig_TLSMode = 2
)

// Enum value maps for EndpointV2_TLSConfig_TLSMode.
var (
	EndpointV2_TLSConfig_TLSMode_name = map[int32]string{
		0: "TLS_MODE_DISABLE",
		1: "TLS_MODE_SIMPLE",
		2: "TLS_MODE_MUTUAL",
	}
	EndpointV2_TLSConfig_TLSMode_value = map[string]int32{
		"TLS_MODE_DISABLE": 0,
		"TLS_MODE_SIMPLE":  1,
		"TLS_MODE_MUTUAL":  2,
	}
)

func (x EndpointV2_TLSConfig_TLSMode) Enum() *EndpointV2_TLSConfig_TLSMode {
	p := new(EndpointV2_TLSConfig_TLSMode)
	*p = x
	return p
}

func (x EndpointV2_TLSConfig_TLSMode) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (EndpointV2_TLSConfig_TLSMode) Descriptor() protoreflect.EnumDescriptor {
	return file_tt_quicksilver_cfg_dispatcher_cfg_dispatcher_proto_enumTypes[1].Descriptor()
}

func (EndpointV2_TLSConfig_TLSMode) Type() protoreflect.EnumType {
	return &file_tt_quicksilver_cfg_dispatcher_cfg_dispatcher_proto_enumTypes[1]
}

func (x EndpointV2_TLSConfig_TLSMode) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use EndpointV2_TLSConfig_TLSMode.Descriptor instead.
func (EndpointV2_TLSConfig_TLSMode) EnumDescriptor() ([]byte, []int) {
	return file_tt_quicksilver_cfg_dispatcher_cfg_dispatcher_proto_rawDescGZIP(), []int{8, 0, 0}
}

type Rules_StringMatch_MatchType int32

const (
	Rules_StringMatch_MATCH_TYPE_UNSPECIFIED Rules_StringMatch_MatchType = 0
	Rules_StringMatch_MATCH_TYPE_EXACT       Rules_StringMatch_MatchType = 1
	Rules_StringMatch_MATCH_TYPE_PREFIX      Rules_StringMatch_MatchType = 2
	Rules_StringMatch_MATCH_TYPE_ALL         Rules_StringMatch_MatchType = 3
)

// Enum value maps for Rules_StringMatch_MatchType.
var (
	Rules_StringMatch_MatchType_name = map[int32]string{
		0: "MATCH_TYPE_UNSPECIFIED",
		1: "MATCH_TYPE_EXACT",
		2: "MATCH_TYPE_PREFIX",
		3: "MATCH_TYPE_ALL",
	}
	Rules_StringMatch_MatchType_value = map[string]int32{
		"MATCH_TYPE_UNSPECIFIED": 0,
		"MATCH_TYPE_EXACT":       1,
		"MATCH_TYPE_PREFIX":      2,
		"MATCH_TYPE_ALL":         3,
	}
)

func (x Rules_StringMatch_MatchType) Enum() *Rules_StringMatch_MatchType {
	p := new(Rules_StringMatch_MatchType)
	*p = x
	return p
}

func (x Rules_StringMatch_MatchType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Rules_StringMatch_MatchType) Descriptor() protoreflect.EnumDescriptor {
	return file_tt_quicksilver_cfg_dispatcher_cfg_dispatcher_proto_enumTypes[2].Descriptor()
}

func (Rules_StringMatch_MatchType) Type() protoreflect.EnumType {
	return &file_tt_quicksilver_cfg_dispatcher_cfg_dispatcher_proto_enumTypes[2]
}

func (x Rules_StringMatch_MatchType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Rules_StringMatch_MatchType.Descriptor instead.
func (Rules_StringMatch_MatchType) EnumDescriptor() ([]byte, []int) {
	return file_tt_quicksilver_cfg_dispatcher_cfg_dispatcher_proto_rawDescGZIP(), []int{10, 0, 0}
}

type Rules_Rule_Action int32

const (
	Rules_Rule_ACTION_UNSPECIFIED Rules_Rule_Action = 0
	Rules_Rule_ACTION_ALLOW       Rules_Rule_Action = 1
	Rules_Rule_ACTION_DENY        Rules_Rule_Action = 2
)

// Enum value maps for Rules_Rule_Action.
var (
	Rules_Rule_Action_name = map[int32]string{
		0: "ACTION_UNSPECIFIED",
		1: "ACTION_ALLOW",
		2: "ACTION_DENY",
	}
	Rules_Rule_Action_value = map[string]int32{
		"ACTION_UNSPECIFIED": 0,
		"ACTION_ALLOW":       1,
		"ACTION_DENY":        2,
	}
)

func (x Rules_Rule_Action) Enum() *Rules_Rule_Action {
	p := new(Rules_Rule_Action)
	*p = x
	return p
}

func (x Rules_Rule_Action) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Rules_Rule_Action) Descriptor() protoreflect.EnumDescriptor {
	return file_tt_quicksilver_cfg_dispatcher_cfg_dispatcher_proto_enumTypes[3].Descriptor()
}

func (Rules_Rule_Action) Type() protoreflect.EnumType {
	return &file_tt_quicksilver_cfg_dispatcher_cfg_dispatcher_proto_enumTypes[3]
}

func (x Rules_Rule_Action) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Rules_Rule_Action.Descriptor instead.
func (Rules_Rule_Action) EnumDescriptor() ([]byte, []int) {
	return file_tt_quicksilver_cfg_dispatcher_cfg_dispatcher_proto_rawDescGZIP(), []int{10, 1, 0}
}

type GetGrpcEndpointsReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Uid        uint32     `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ClientType ClientType `protobuf:"varint,2,opt,name=client_type,json=clientType,proto3,enum=cfg_dispatcher.ClientType" json:"client_type,omitempty"`
}

func (x *GetGrpcEndpointsReq) Reset() {
	*x = GetGrpcEndpointsReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tt_quicksilver_cfg_dispatcher_cfg_dispatcher_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetGrpcEndpointsReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetGrpcEndpointsReq) ProtoMessage() {}

func (x *GetGrpcEndpointsReq) ProtoReflect() protoreflect.Message {
	mi := &file_tt_quicksilver_cfg_dispatcher_cfg_dispatcher_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetGrpcEndpointsReq.ProtoReflect.Descriptor instead.
func (*GetGrpcEndpointsReq) Descriptor() ([]byte, []int) {
	return file_tt_quicksilver_cfg_dispatcher_cfg_dispatcher_proto_rawDescGZIP(), []int{0}
}

func (x *GetGrpcEndpointsReq) GetUid() uint32 {
	if x != nil {
		return x.Uid
	}
	return 0
}

func (x *GetGrpcEndpointsReq) GetClientType() ClientType {
	if x != nil {
		return x.ClientType
	}
	return ClientType_ENUM_CT_UNKNOWN
}

type GetGrpcEndpointsResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TransportConfig *transport.TransportConfig `protobuf:"bytes,1,opt,name=transport_config,json=transportConfig,proto3" json:"transport_config,omitempty"`
}

func (x *GetGrpcEndpointsResp) Reset() {
	*x = GetGrpcEndpointsResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tt_quicksilver_cfg_dispatcher_cfg_dispatcher_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetGrpcEndpointsResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetGrpcEndpointsResp) ProtoMessage() {}

func (x *GetGrpcEndpointsResp) ProtoReflect() protoreflect.Message {
	mi := &file_tt_quicksilver_cfg_dispatcher_cfg_dispatcher_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetGrpcEndpointsResp.ProtoReflect.Descriptor instead.
func (*GetGrpcEndpointsResp) Descriptor() ([]byte, []int) {
	return file_tt_quicksilver_cfg_dispatcher_cfg_dispatcher_proto_rawDescGZIP(), []int{1}
}

func (x *GetGrpcEndpointsResp) GetTransportConfig() *transport.TransportConfig {
	if x != nil {
		return x.TransportConfig
	}
	return nil
}

type AppInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Uid           uint32     `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ClientType    ClientType `protobuf:"varint,2,opt,name=client_type,json=clientType,proto3,enum=cfg_dispatcher.ClientType" json:"client_type,omitempty"`
	AppVersionInt uint32     `protobuf:"varint,3,opt,name=app_version_int,json=appVersionInt,proto3" json:"app_version_int,omitempty"`
	MarketId      uint32     `protobuf:"varint,4,opt,name=market_id,json=marketId,proto3" json:"market_id,omitempty"`
}

func (x *AppInfo) Reset() {
	*x = AppInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tt_quicksilver_cfg_dispatcher_cfg_dispatcher_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AppInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AppInfo) ProtoMessage() {}

func (x *AppInfo) ProtoReflect() protoreflect.Message {
	mi := &file_tt_quicksilver_cfg_dispatcher_cfg_dispatcher_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AppInfo.ProtoReflect.Descriptor instead.
func (*AppInfo) Descriptor() ([]byte, []int) {
	return file_tt_quicksilver_cfg_dispatcher_cfg_dispatcher_proto_rawDescGZIP(), []int{2}
}

func (x *AppInfo) GetUid() uint32 {
	if x != nil {
		return x.Uid
	}
	return 0
}

func (x *AppInfo) GetClientType() ClientType {
	if x != nil {
		return x.ClientType
	}
	return ClientType_ENUM_CT_UNKNOWN
}

func (x *AppInfo) GetAppVersionInt() uint32 {
	if x != nil {
		return x.AppVersionInt
	}
	return 0
}

func (x *AppInfo) GetMarketId() uint32 {
	if x != nil {
		return x.MarketId
	}
	return 0
}

type GetTransportConfigV2Request struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppInfo *AppInfo `protobuf:"bytes,1,opt,name=app_info,json=appInfo,proto3" json:"app_info,omitempty"`
}

func (x *GetTransportConfigV2Request) Reset() {
	*x = GetTransportConfigV2Request{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tt_quicksilver_cfg_dispatcher_cfg_dispatcher_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetTransportConfigV2Request) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTransportConfigV2Request) ProtoMessage() {}

func (x *GetTransportConfigV2Request) ProtoReflect() protoreflect.Message {
	mi := &file_tt_quicksilver_cfg_dispatcher_cfg_dispatcher_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTransportConfigV2Request.ProtoReflect.Descriptor instead.
func (*GetTransportConfigV2Request) Descriptor() ([]byte, []int) {
	return file_tt_quicksilver_cfg_dispatcher_cfg_dispatcher_proto_rawDescGZIP(), []int{3}
}

func (x *GetTransportConfigV2Request) GetAppInfo() *AppInfo {
	if x != nil {
		return x.AppInfo
	}
	return nil
}

type GetTransportConfigV2Response struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TransportConfig_V2 *grpc_transport_cfg.TransportConfigV2 `protobuf:"bytes,1,opt,name=transport_config_V2,json=transportConfigV2,proto3" json:"transport_config_V2,omitempty"`
}

func (x *GetTransportConfigV2Response) Reset() {
	*x = GetTransportConfigV2Response{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tt_quicksilver_cfg_dispatcher_cfg_dispatcher_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetTransportConfigV2Response) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTransportConfigV2Response) ProtoMessage() {}

func (x *GetTransportConfigV2Response) ProtoReflect() protoreflect.Message {
	mi := &file_tt_quicksilver_cfg_dispatcher_cfg_dispatcher_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTransportConfigV2Response.ProtoReflect.Descriptor instead.
func (*GetTransportConfigV2Response) Descriptor() ([]byte, []int) {
	return file_tt_quicksilver_cfg_dispatcher_cfg_dispatcher_proto_rawDescGZIP(), []int{4}
}

func (x *GetTransportConfigV2Response) GetTransportConfig_V2() *grpc_transport_cfg.TransportConfigV2 {
	if x != nil {
		return x.TransportConfig_V2
	}
	return nil
}

type Scope struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Gt uint32 `protobuf:"varint,1,opt,name=gt,proto3" json:"gt,omitempty"`
	Lt uint32 `protobuf:"varint,2,opt,name=lt,proto3" json:"lt,omitempty"`
}

func (x *Scope) Reset() {
	*x = Scope{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tt_quicksilver_cfg_dispatcher_cfg_dispatcher_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Scope) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Scope) ProtoMessage() {}

func (x *Scope) ProtoReflect() protoreflect.Message {
	mi := &file_tt_quicksilver_cfg_dispatcher_cfg_dispatcher_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Scope.ProtoReflect.Descriptor instead.
func (*Scope) Descriptor() ([]byte, []int) {
	return file_tt_quicksilver_cfg_dispatcher_cfg_dispatcher_proto_rawDescGZIP(), []int{5}
}

func (x *Scope) GetGt() uint32 {
	if x != nil {
		return x.Gt
	}
	return 0
}

func (x *Scope) GetLt() uint32 {
	if x != nil {
		return x.Lt
	}
	return 0
}

type UidIntMatch struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Action:
	//
	//	*UidIntMatch_Exact
	//	*UidIntMatch_Scope
	//	*UidIntMatch_Modulo_
	Action isUidIntMatch_Action `protobuf_oneof:"action"`
}

func (x *UidIntMatch) Reset() {
	*x = UidIntMatch{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tt_quicksilver_cfg_dispatcher_cfg_dispatcher_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UidIntMatch) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UidIntMatch) ProtoMessage() {}

func (x *UidIntMatch) ProtoReflect() protoreflect.Message {
	mi := &file_tt_quicksilver_cfg_dispatcher_cfg_dispatcher_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UidIntMatch.ProtoReflect.Descriptor instead.
func (*UidIntMatch) Descriptor() ([]byte, []int) {
	return file_tt_quicksilver_cfg_dispatcher_cfg_dispatcher_proto_rawDescGZIP(), []int{6}
}

func (m *UidIntMatch) GetAction() isUidIntMatch_Action {
	if m != nil {
		return m.Action
	}
	return nil
}

func (x *UidIntMatch) GetExact() uint32 {
	if x, ok := x.GetAction().(*UidIntMatch_Exact); ok {
		return x.Exact
	}
	return 0
}

func (x *UidIntMatch) GetScope() *Scope {
	if x, ok := x.GetAction().(*UidIntMatch_Scope); ok {
		return x.Scope
	}
	return nil
}

func (x *UidIntMatch) GetModulo() *UidIntMatch_Modulo {
	if x, ok := x.GetAction().(*UidIntMatch_Modulo_); ok {
		return x.Modulo
	}
	return nil
}

type isUidIntMatch_Action interface {
	isUidIntMatch_Action()
}

type UidIntMatch_Exact struct {
	Exact uint32 `protobuf:"varint,1,opt,name=exact,proto3,oneof"`
}

type UidIntMatch_Scope struct {
	//似乎scope弄成无限大就好了，就不需要gt、lt单独了
	Scope *Scope `protobuf:"bytes,2,opt,name=scope,proto3,oneof"`
}

type UidIntMatch_Modulo_ struct {
	Modulo *UidIntMatch_Modulo `protobuf:"bytes,3,opt,name=modulo,proto3,oneof"`
}

func (*UidIntMatch_Exact) isUidIntMatch_Action() {}

func (*UidIntMatch_Scope) isUidIntMatch_Action() {}

func (*UidIntMatch_Modulo_) isUidIntMatch_Action() {}

type AppVersionIntMatch struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Action:
	//
	//	*AppVersionIntMatch_Gt
	//	*AppVersionIntMatch_Lt
	Action isAppVersionIntMatch_Action `protobuf_oneof:"action"`
}

func (x *AppVersionIntMatch) Reset() {
	*x = AppVersionIntMatch{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tt_quicksilver_cfg_dispatcher_cfg_dispatcher_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AppVersionIntMatch) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AppVersionIntMatch) ProtoMessage() {}

func (x *AppVersionIntMatch) ProtoReflect() protoreflect.Message {
	mi := &file_tt_quicksilver_cfg_dispatcher_cfg_dispatcher_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AppVersionIntMatch.ProtoReflect.Descriptor instead.
func (*AppVersionIntMatch) Descriptor() ([]byte, []int) {
	return file_tt_quicksilver_cfg_dispatcher_cfg_dispatcher_proto_rawDescGZIP(), []int{7}
}

func (m *AppVersionIntMatch) GetAction() isAppVersionIntMatch_Action {
	if m != nil {
		return m.Action
	}
	return nil
}

func (x *AppVersionIntMatch) GetGt() uint32 {
	if x, ok := x.GetAction().(*AppVersionIntMatch_Gt); ok {
		return x.Gt
	}
	return 0
}

func (x *AppVersionIntMatch) GetLt() uint32 {
	if x, ok := x.GetAction().(*AppVersionIntMatch_Lt); ok {
		return x.Lt
	}
	return 0
}

type isAppVersionIntMatch_Action interface {
	isAppVersionIntMatch_Action()
}

type AppVersionIntMatch_Gt struct {
	Gt uint32 `protobuf:"varint,1,opt,name=gt,proto3,oneof"`
}

type AppVersionIntMatch_Lt struct {
	Lt uint32 `protobuf:"varint,2,opt,name=lt,proto3,oneof"`
}

func (*AppVersionIntMatch_Gt) isAppVersionIntMatch_Action() {}

func (*AppVersionIntMatch_Lt) isAppVersionIntMatch_Action() {}

// 跟transport_v2.proto定义一样，因为gogo插件的原因，这里只好单独拎出来
type EndpointV2 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// address like: dev-apv2.ttyuyin.com:443
	Address   string                `protobuf:"bytes,1,opt,name=address,proto3" json:"address,omitempty"`
	TlsConfig *EndpointV2_TLSConfig `protobuf:"bytes,2,opt,name=tls_config,json=tlsConfig,proto3" json:"tls_config,omitempty"`
}

func (x *EndpointV2) Reset() {
	*x = EndpointV2{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tt_quicksilver_cfg_dispatcher_cfg_dispatcher_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EndpointV2) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EndpointV2) ProtoMessage() {}

func (x *EndpointV2) ProtoReflect() protoreflect.Message {
	mi := &file_tt_quicksilver_cfg_dispatcher_cfg_dispatcher_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EndpointV2.ProtoReflect.Descriptor instead.
func (*EndpointV2) Descriptor() ([]byte, []int) {
	return file_tt_quicksilver_cfg_dispatcher_cfg_dispatcher_proto_rawDescGZIP(), []int{8}
}

func (x *EndpointV2) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

func (x *EndpointV2) GetTlsConfig() *EndpointV2_TLSConfig {
	if x != nil {
		return x.TlsConfig
	}
	return nil
}

type EndpointRule struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Rules []*EndpointRule_Rule `protobuf:"bytes,1,rep,name=rules,proto3" json:"rules,omitempty"`
}

func (x *EndpointRule) Reset() {
	*x = EndpointRule{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tt_quicksilver_cfg_dispatcher_cfg_dispatcher_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EndpointRule) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EndpointRule) ProtoMessage() {}

func (x *EndpointRule) ProtoReflect() protoreflect.Message {
	mi := &file_tt_quicksilver_cfg_dispatcher_cfg_dispatcher_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EndpointRule.ProtoReflect.Descriptor instead.
func (*EndpointRule) Descriptor() ([]byte, []int) {
	return file_tt_quicksilver_cfg_dispatcher_cfg_dispatcher_proto_rawDescGZIP(), []int{9}
}

func (x *EndpointRule) GetRules() []*EndpointRule_Rule {
	if x != nil {
		return x.Rules
	}
	return nil
}

type Rules struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Rules          []*Rules_Rule        `protobuf:"bytes,1,rep,name=rules,proto3" json:"rules,omitempty"`
	GzipBlackRules []*Rules_StringMatch `protobuf:"bytes,2,rep,name=gzip_black_rules,json=gzipBlackRules,proto3" json:"gzip_black_rules,omitempty"`
	GzipWhiteRules []*Rules_StringMatch `protobuf:"bytes,3,rep,name=gzip_white_rules,json=gzipWhiteRules,proto3" json:"gzip_white_rules,omitempty"`
}

func (x *Rules) Reset() {
	*x = Rules{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tt_quicksilver_cfg_dispatcher_cfg_dispatcher_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Rules) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Rules) ProtoMessage() {}

func (x *Rules) ProtoReflect() protoreflect.Message {
	mi := &file_tt_quicksilver_cfg_dispatcher_cfg_dispatcher_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Rules.ProtoReflect.Descriptor instead.
func (*Rules) Descriptor() ([]byte, []int) {
	return file_tt_quicksilver_cfg_dispatcher_cfg_dispatcher_proto_rawDescGZIP(), []int{10}
}

func (x *Rules) GetRules() []*Rules_Rule {
	if x != nil {
		return x.Rules
	}
	return nil
}

func (x *Rules) GetGzipBlackRules() []*Rules_StringMatch {
	if x != nil {
		return x.GzipBlackRules
	}
	return nil
}

func (x *Rules) GetGzipWhiteRules() []*Rules_StringMatch {
	if x != nil {
		return x.GzipWhiteRules
	}
	return nil
}

type UidIntMatch_Modulo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Divisor    uint32   `protobuf:"varint,1,opt,name=divisor,proto3" json:"divisor,omitempty"`
	Remainders []uint32 `protobuf:"varint,2,rep,packed,name=remainders,proto3" json:"remainders,omitempty"`
}

func (x *UidIntMatch_Modulo) Reset() {
	*x = UidIntMatch_Modulo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tt_quicksilver_cfg_dispatcher_cfg_dispatcher_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UidIntMatch_Modulo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UidIntMatch_Modulo) ProtoMessage() {}

func (x *UidIntMatch_Modulo) ProtoReflect() protoreflect.Message {
	mi := &file_tt_quicksilver_cfg_dispatcher_cfg_dispatcher_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UidIntMatch_Modulo.ProtoReflect.Descriptor instead.
func (*UidIntMatch_Modulo) Descriptor() ([]byte, []int) {
	return file_tt_quicksilver_cfg_dispatcher_cfg_dispatcher_proto_rawDescGZIP(), []int{6, 0}
}

func (x *UidIntMatch_Modulo) GetDivisor() uint32 {
	if x != nil {
		return x.Divisor
	}
	return 0
}

func (x *UidIntMatch_Modulo) GetRemainders() []uint32 {
	if x != nil {
		return x.Remainders
	}
	return nil
}

type EndpointV2_TLSConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Mode      EndpointV2_TLSConfig_TLSMode `protobuf:"varint,1,opt,name=mode,proto3,enum=cfg_dispatcher.EndpointV2_TLSConfig_TLSMode" json:"mode,omitempty"`
	Authority string                       `protobuf:"bytes,2,opt,name=authority,proto3" json:"authority,omitempty"`
}

func (x *EndpointV2_TLSConfig) Reset() {
	*x = EndpointV2_TLSConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tt_quicksilver_cfg_dispatcher_cfg_dispatcher_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EndpointV2_TLSConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EndpointV2_TLSConfig) ProtoMessage() {}

func (x *EndpointV2_TLSConfig) ProtoReflect() protoreflect.Message {
	mi := &file_tt_quicksilver_cfg_dispatcher_cfg_dispatcher_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EndpointV2_TLSConfig.ProtoReflect.Descriptor instead.
func (*EndpointV2_TLSConfig) Descriptor() ([]byte, []int) {
	return file_tt_quicksilver_cfg_dispatcher_cfg_dispatcher_proto_rawDescGZIP(), []int{8, 0}
}

func (x *EndpointV2_TLSConfig) GetMode() EndpointV2_TLSConfig_TLSMode {
	if x != nil {
		return x.Mode
	}
	return EndpointV2_TLSConfig_TLS_MODE_DISABLE
}

func (x *EndpointV2_TLSConfig) GetAuthority() string {
	if x != nil {
		return x.Authority
	}
	return ""
}

type EndpointRule_AppRule struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Uid           *UidIntMatch        `protobuf:"bytes,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ClientType    []uint32            `protobuf:"varint,2,rep,packed,name=client_type,json=clientType,proto3" json:"client_type,omitempty"`
	MarketId      []uint32            `protobuf:"varint,3,rep,packed,name=market_id,json=marketId,proto3" json:"market_id,omitempty"`
	AppVersionInt *AppVersionIntMatch `protobuf:"bytes,4,opt,name=app_version_int,json=appVersionInt,proto3" json:"app_version_int,omitempty"`
}

func (x *EndpointRule_AppRule) Reset() {
	*x = EndpointRule_AppRule{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tt_quicksilver_cfg_dispatcher_cfg_dispatcher_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EndpointRule_AppRule) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EndpointRule_AppRule) ProtoMessage() {}

func (x *EndpointRule_AppRule) ProtoReflect() protoreflect.Message {
	mi := &file_tt_quicksilver_cfg_dispatcher_cfg_dispatcher_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EndpointRule_AppRule.ProtoReflect.Descriptor instead.
func (*EndpointRule_AppRule) Descriptor() ([]byte, []int) {
	return file_tt_quicksilver_cfg_dispatcher_cfg_dispatcher_proto_rawDescGZIP(), []int{9, 0}
}

func (x *EndpointRule_AppRule) GetUid() *UidIntMatch {
	if x != nil {
		return x.Uid
	}
	return nil
}

func (x *EndpointRule_AppRule) GetClientType() []uint32 {
	if x != nil {
		return x.ClientType
	}
	return nil
}

func (x *EndpointRule_AppRule) GetMarketId() []uint32 {
	if x != nil {
		return x.MarketId
	}
	return nil
}

func (x *EndpointRule_AppRule) GetAppVersionInt() *AppVersionIntMatch {
	if x != nil {
		return x.AppVersionInt
	}
	return nil
}

type EndpointRule_Rule struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppRule      *EndpointRule_AppRule `protobuf:"bytes,1,opt,name=app_rule,json=appRule,proto3" json:"app_rule,omitempty"`
	EndpointList []*EndpointV2         `protobuf:"bytes,2,rep,name=endpoint_list,json=endpointList,proto3" json:"endpoint_list,omitempty"`
}

func (x *EndpointRule_Rule) Reset() {
	*x = EndpointRule_Rule{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tt_quicksilver_cfg_dispatcher_cfg_dispatcher_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EndpointRule_Rule) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EndpointRule_Rule) ProtoMessage() {}

func (x *EndpointRule_Rule) ProtoReflect() protoreflect.Message {
	mi := &file_tt_quicksilver_cfg_dispatcher_cfg_dispatcher_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EndpointRule_Rule.ProtoReflect.Descriptor instead.
func (*EndpointRule_Rule) Descriptor() ([]byte, []int) {
	return file_tt_quicksilver_cfg_dispatcher_cfg_dispatcher_proto_rawDescGZIP(), []int{9, 1}
}

func (x *EndpointRule_Rule) GetAppRule() *EndpointRule_AppRule {
	if x != nil {
		return x.AppRule
	}
	return nil
}

func (x *EndpointRule_Rule) GetEndpointList() []*EndpointV2 {
	if x != nil {
		return x.EndpointList
	}
	return nil
}

// 跟transport_v2.proto定义一样，因为gogo插件的原因，这里只好单独拎出来
type Rules_StringMatch struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MatchType  Rules_StringMatch_MatchType `protobuf:"varint,1,opt,name=match_type,json=matchType,proto3,enum=cfg_dispatcher.Rules_StringMatch_MatchType" json:"match_type,omitempty"`
	MatchValue string                      `protobuf:"bytes,2,opt,name=match_value,json=matchValue,proto3" json:"match_value,omitempty"`
}

func (x *Rules_StringMatch) Reset() {
	*x = Rules_StringMatch{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tt_quicksilver_cfg_dispatcher_cfg_dispatcher_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Rules_StringMatch) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Rules_StringMatch) ProtoMessage() {}

func (x *Rules_StringMatch) ProtoReflect() protoreflect.Message {
	mi := &file_tt_quicksilver_cfg_dispatcher_cfg_dispatcher_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Rules_StringMatch.ProtoReflect.Descriptor instead.
func (*Rules_StringMatch) Descriptor() ([]byte, []int) {
	return file_tt_quicksilver_cfg_dispatcher_cfg_dispatcher_proto_rawDescGZIP(), []int{10, 0}
}

func (x *Rules_StringMatch) GetMatchType() Rules_StringMatch_MatchType {
	if x != nil {
		return x.MatchType
	}
	return Rules_StringMatch_MATCH_TYPE_UNSPECIFIED
}

func (x *Rules_StringMatch) GetMatchValue() string {
	if x != nil {
		return x.MatchValue
	}
	return ""
}

type Rules_Rule struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Action  Rules_Rule_Action    `protobuf:"varint,1,opt,name=action,proto3,enum=cfg_dispatcher.Rules_Rule_Action" json:"action,omitempty"`
	AppRule *Rules_Rule_AppRule  `protobuf:"bytes,2,opt,name=app_rule,json=appRule,proto3" json:"app_rule,omitempty"`
	ApiRule []*Rules_StringMatch `protobuf:"bytes,3,rep,name=api_rule,json=apiRule,proto3" json:"api_rule,omitempty"`
}

func (x *Rules_Rule) Reset() {
	*x = Rules_Rule{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tt_quicksilver_cfg_dispatcher_cfg_dispatcher_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Rules_Rule) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Rules_Rule) ProtoMessage() {}

func (x *Rules_Rule) ProtoReflect() protoreflect.Message {
	mi := &file_tt_quicksilver_cfg_dispatcher_cfg_dispatcher_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Rules_Rule.ProtoReflect.Descriptor instead.
func (*Rules_Rule) Descriptor() ([]byte, []int) {
	return file_tt_quicksilver_cfg_dispatcher_cfg_dispatcher_proto_rawDescGZIP(), []int{10, 1}
}

func (x *Rules_Rule) GetAction() Rules_Rule_Action {
	if x != nil {
		return x.Action
	}
	return Rules_Rule_ACTION_UNSPECIFIED
}

func (x *Rules_Rule) GetAppRule() *Rules_Rule_AppRule {
	if x != nil {
		return x.AppRule
	}
	return nil
}

func (x *Rules_Rule) GetApiRule() []*Rules_StringMatch {
	if x != nil {
		return x.ApiRule
	}
	return nil
}

type Rules_Rule_AppRule struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Uid        *UidIntMatch `protobuf:"bytes,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ClientType []uint32     `protobuf:"varint,2,rep,packed,name=client_type,json=clientType,proto3" json:"client_type,omitempty"`
	MarketId   []uint32     `protobuf:"varint,3,rep,packed,name=market_id,json=marketId,proto3" json:"market_id,omitempty"`
	//app_version_int 只需要大于、或者小于
	AppVersionInt *AppVersionIntMatch `protobuf:"bytes,4,opt,name=app_version_int,json=appVersionInt,proto3" json:"app_version_int,omitempty"`
}

func (x *Rules_Rule_AppRule) Reset() {
	*x = Rules_Rule_AppRule{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tt_quicksilver_cfg_dispatcher_cfg_dispatcher_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Rules_Rule_AppRule) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Rules_Rule_AppRule) ProtoMessage() {}

func (x *Rules_Rule_AppRule) ProtoReflect() protoreflect.Message {
	mi := &file_tt_quicksilver_cfg_dispatcher_cfg_dispatcher_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Rules_Rule_AppRule.ProtoReflect.Descriptor instead.
func (*Rules_Rule_AppRule) Descriptor() ([]byte, []int) {
	return file_tt_quicksilver_cfg_dispatcher_cfg_dispatcher_proto_rawDescGZIP(), []int{10, 1, 0}
}

func (x *Rules_Rule_AppRule) GetUid() *UidIntMatch {
	if x != nil {
		return x.Uid
	}
	return nil
}

func (x *Rules_Rule_AppRule) GetClientType() []uint32 {
	if x != nil {
		return x.ClientType
	}
	return nil
}

func (x *Rules_Rule_AppRule) GetMarketId() []uint32 {
	if x != nil {
		return x.MarketId
	}
	return nil
}

func (x *Rules_Rule_AppRule) GetAppVersionInt() *AppVersionIntMatch {
	if x != nil {
		return x.AppVersionInt
	}
	return nil
}

var File_tt_quicksilver_cfg_dispatcher_cfg_dispatcher_proto protoreflect.FileDescriptor

var file_tt_quicksilver_cfg_dispatcher_cfg_dispatcher_proto_rawDesc = []byte{
	0x0a, 0x32, 0x74, 0x74, 0x2f, 0x71, 0x75, 0x69, 0x63, 0x6b, 0x73, 0x69, 0x6c, 0x76, 0x65, 0x72,
	0x2f, 0x63, 0x66, 0x67, 0x2d, 0x64, 0x69, 0x73, 0x70, 0x61, 0x74, 0x63, 0x68, 0x65, 0x72, 0x2f,
	0x63, 0x66, 0x67, 0x2d, 0x64, 0x69, 0x73, 0x70, 0x61, 0x74, 0x63, 0x68, 0x65, 0x72, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0e, 0x63, 0x66, 0x67, 0x5f, 0x64, 0x69, 0x73, 0x70, 0x61, 0x74,
	0x63, 0x68, 0x65, 0x72, 0x1a, 0x19, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x70, 0x6f, 0x72, 0x74, 0x2f,
	0x74, 0x72, 0x61, 0x6e, 0x73, 0x70, 0x6f, 0x72, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x25, 0x67, 0x72, 0x70, 0x63, 0x5f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x70, 0x6f, 0x72, 0x74, 0x5f,
	0x63, 0x66, 0x67, 0x2f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x76, 0x32,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x64, 0x0a, 0x13, 0x47, 0x65, 0x74, 0x47, 0x72, 0x70,
	0x63, 0x45, 0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x71, 0x12, 0x10, 0x0a,
	0x03, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x03, 0x75, 0x69, 0x64, 0x12,
	0x3b, 0x0a, 0x0b, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x1a, 0x2e, 0x63, 0x66, 0x67, 0x5f, 0x64, 0x69, 0x73, 0x70, 0x61,
	0x74, 0x63, 0x68, 0x65, 0x72, 0x2e, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65,
	0x52, 0x0a, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x22, 0x60, 0x0a, 0x14,
	0x47, 0x65, 0x74, 0x47, 0x72, 0x70, 0x63, 0x45, 0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x73,
	0x52, 0x65, 0x73, 0x70, 0x12, 0x48, 0x0a, 0x10, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x70, 0x6f, 0x72,
	0x74, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d,
	0x2e, 0x67, 0x61, 0x2e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x70, 0x6f, 0x72, 0x74, 0x2e, 0x54, 0x72,
	0x61, 0x6e, 0x73, 0x70, 0x6f, 0x72, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x0f, 0x74,
	0x72, 0x61, 0x6e, 0x73, 0x70, 0x6f, 0x72, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x22, 0x9d,
	0x01, 0x0a, 0x07, 0x41, 0x70, 0x70, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x03, 0x75, 0x69, 0x64, 0x12, 0x3b, 0x0a, 0x0b,
	0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x1a, 0x2e, 0x63, 0x66, 0x67, 0x5f, 0x64, 0x69, 0x73, 0x70, 0x61, 0x74, 0x63, 0x68,
	0x65, 0x72, 0x2e, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0a, 0x63,
	0x6c, 0x69, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x26, 0x0a, 0x0f, 0x61, 0x70, 0x70,
	0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x0d, 0x61, 0x70, 0x70, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x6e,
	0x74, 0x12, 0x1b, 0x0a, 0x09, 0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x49, 0x64, 0x22, 0x51,
	0x0a, 0x1b, 0x47, 0x65, 0x74, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x70, 0x6f, 0x72, 0x74, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x56, 0x32, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x32, 0x0a,
	0x08, 0x61, 0x70, 0x70, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x17, 0x2e, 0x63, 0x66, 0x67, 0x5f, 0x64, 0x69, 0x73, 0x70, 0x61, 0x74, 0x63, 0x68, 0x65, 0x72,
	0x2e, 0x41, 0x70, 0x70, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x07, 0x61, 0x70, 0x70, 0x49, 0x6e, 0x66,
	0x6f, 0x22, 0x78, 0x0a, 0x1c, 0x47, 0x65, 0x74, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x70, 0x6f, 0x72,
	0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x56, 0x32, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x58, 0x0a, 0x13, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x63,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x56, 0x32, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x28,
	0x2e, 0x67, 0x61, 0x2e, 0x67, 0x72, 0x70, 0x63, 0x5f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x70, 0x6f,
	0x72, 0x74, 0x5f, 0x63, 0x66, 0x67, 0x2e, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x70, 0x6f, 0x72, 0x74,
	0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x56, 0x32, 0x52, 0x11, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x70,
	0x6f, 0x72, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x56, 0x32, 0x22, 0x27, 0x0a, 0x05, 0x53,
	0x63, 0x6f, 0x70, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x67, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x02, 0x67, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x6c, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x02, 0x6c, 0x74, 0x22, 0xe0, 0x01, 0x0a, 0x0b, 0x55, 0x69, 0x64, 0x49, 0x6e, 0x74, 0x4d,
	0x61, 0x74, 0x63, 0x68, 0x12, 0x16, 0x0a, 0x05, 0x65, 0x78, 0x61, 0x63, 0x74, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0d, 0x48, 0x00, 0x52, 0x05, 0x65, 0x78, 0x61, 0x63, 0x74, 0x12, 0x2d, 0x0a, 0x05,
	0x73, 0x63, 0x6f, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x63, 0x66,
	0x67, 0x5f, 0x64, 0x69, 0x73, 0x70, 0x61, 0x74, 0x63, 0x68, 0x65, 0x72, 0x2e, 0x53, 0x63, 0x6f,
	0x70, 0x65, 0x48, 0x00, 0x52, 0x05, 0x73, 0x63, 0x6f, 0x70, 0x65, 0x12, 0x3c, 0x0a, 0x06, 0x6d,
	0x6f, 0x64, 0x75, 0x6c, 0x6f, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x63, 0x66,
	0x67, 0x5f, 0x64, 0x69, 0x73, 0x70, 0x61, 0x74, 0x63, 0x68, 0x65, 0x72, 0x2e, 0x55, 0x69, 0x64,
	0x49, 0x6e, 0x74, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x2e, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x6f, 0x48,
	0x00, 0x52, 0x06, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x6f, 0x1a, 0x42, 0x0a, 0x06, 0x4d, 0x6f, 0x64,
	0x75, 0x6c, 0x6f, 0x12, 0x18, 0x0a, 0x07, 0x64, 0x69, 0x76, 0x69, 0x73, 0x6f, 0x72, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x07, 0x64, 0x69, 0x76, 0x69, 0x73, 0x6f, 0x72, 0x12, 0x1e, 0x0a,
	0x0a, 0x72, 0x65, 0x6d, 0x61, 0x69, 0x6e, 0x64, 0x65, 0x72, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28,
	0x0d, 0x52, 0x0a, 0x72, 0x65, 0x6d, 0x61, 0x69, 0x6e, 0x64, 0x65, 0x72, 0x73, 0x42, 0x08, 0x0a,
	0x06, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x42, 0x0a, 0x12, 0x41, 0x70, 0x70, 0x56, 0x65,
	0x72, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x74, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x12, 0x10, 0x0a,
	0x02, 0x67, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x48, 0x00, 0x52, 0x02, 0x67, 0x74, 0x12,
	0x10, 0x0a, 0x02, 0x6c, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x48, 0x00, 0x52, 0x02, 0x6c,
	0x74, 0x42, 0x08, 0x0a, 0x06, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0xa4, 0x02, 0x0a, 0x0a,
	0x45, 0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x56, 0x32, 0x12, 0x18, 0x0a, 0x07, 0x61, 0x64,
	0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x64, 0x64,
	0x72, 0x65, 0x73, 0x73, 0x12, 0x43, 0x0a, 0x0a, 0x74, 0x6c, 0x73, 0x5f, 0x63, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x63, 0x66, 0x67, 0x5f, 0x64,
	0x69, 0x73, 0x70, 0x61, 0x74, 0x63, 0x68, 0x65, 0x72, 0x2e, 0x45, 0x6e, 0x64, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x56, 0x32, 0x2e, 0x54, 0x4c, 0x53, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x09,
	0x74, 0x6c, 0x73, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x1a, 0xb6, 0x01, 0x0a, 0x09, 0x54, 0x4c,
	0x53, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x40, 0x0a, 0x04, 0x6d, 0x6f, 0x64, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2c, 0x2e, 0x63, 0x66, 0x67, 0x5f, 0x64, 0x69, 0x73, 0x70,
	0x61, 0x74, 0x63, 0x68, 0x65, 0x72, 0x2e, 0x45, 0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x56,
	0x32, 0x2e, 0x54, 0x4c, 0x53, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x54, 0x4c, 0x53, 0x4d,
	0x6f, 0x64, 0x65, 0x52, 0x04, 0x6d, 0x6f, 0x64, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x61, 0x75, 0x74,
	0x68, 0x6f, 0x72, 0x69, 0x74, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x75,
	0x74, 0x68, 0x6f, 0x72, 0x69, 0x74, 0x79, 0x22, 0x49, 0x0a, 0x07, 0x54, 0x4c, 0x53, 0x4d, 0x6f,
	0x64, 0x65, 0x12, 0x14, 0x0a, 0x10, 0x54, 0x4c, 0x53, 0x5f, 0x4d, 0x4f, 0x44, 0x45, 0x5f, 0x44,
	0x49, 0x53, 0x41, 0x42, 0x4c, 0x45, 0x10, 0x00, 0x12, 0x13, 0x0a, 0x0f, 0x54, 0x4c, 0x53, 0x5f,
	0x4d, 0x4f, 0x44, 0x45, 0x5f, 0x53, 0x49, 0x4d, 0x50, 0x4c, 0x45, 0x10, 0x01, 0x12, 0x13, 0x0a,
	0x0f, 0x54, 0x4c, 0x53, 0x5f, 0x4d, 0x4f, 0x44, 0x45, 0x5f, 0x4d, 0x55, 0x54, 0x55, 0x41, 0x4c,
	0x10, 0x02, 0x22, 0x97, 0x03, 0x0a, 0x0c, 0x45, 0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x52,
	0x75, 0x6c, 0x65, 0x12, 0x37, 0x0a, 0x05, 0x72, 0x75, 0x6c, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x21, 0x2e, 0x63, 0x66, 0x67, 0x5f, 0x64, 0x69, 0x73, 0x70, 0x61, 0x74, 0x63,
	0x68, 0x65, 0x72, 0x2e, 0x45, 0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x52, 0x75, 0x6c, 0x65,
	0x2e, 0x52, 0x75, 0x6c, 0x65, 0x52, 0x05, 0x72, 0x75, 0x6c, 0x65, 0x73, 0x1a, 0xc2, 0x01, 0x0a,
	0x07, 0x41, 0x70, 0x70, 0x52, 0x75, 0x6c, 0x65, 0x12, 0x2d, 0x0a, 0x03, 0x75, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x63, 0x66, 0x67, 0x5f, 0x64, 0x69, 0x73, 0x70,
	0x61, 0x74, 0x63, 0x68, 0x65, 0x72, 0x2e, 0x55, 0x69, 0x64, 0x49, 0x6e, 0x74, 0x4d, 0x61, 0x74,
	0x63, 0x68, 0x52, 0x03, 0x75, 0x69, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x6c, 0x69, 0x65, 0x6e,
	0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0d, 0x52, 0x0a, 0x63, 0x6c,
	0x69, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x6d, 0x61, 0x72, 0x6b,
	0x65, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0d, 0x52, 0x08, 0x6d, 0x61, 0x72,
	0x6b, 0x65, 0x74, 0x49, 0x64, 0x12, 0x4a, 0x0a, 0x0f, 0x61, 0x70, 0x70, 0x5f, 0x76, 0x65, 0x72,
	0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22,
	0x2e, 0x63, 0x66, 0x67, 0x5f, 0x64, 0x69, 0x73, 0x70, 0x61, 0x74, 0x63, 0x68, 0x65, 0x72, 0x2e,
	0x41, 0x70, 0x70, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x74, 0x4d, 0x61, 0x74,
	0x63, 0x68, 0x52, 0x0d, 0x61, 0x70, 0x70, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x6e,
	0x74, 0x1a, 0x88, 0x01, 0x0a, 0x04, 0x52, 0x75, 0x6c, 0x65, 0x12, 0x3f, 0x0a, 0x08, 0x61, 0x70,
	0x70, 0x5f, 0x72, 0x75, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x63,
	0x66, 0x67, 0x5f, 0x64, 0x69, 0x73, 0x70, 0x61, 0x74, 0x63, 0x68, 0x65, 0x72, 0x2e, 0x45, 0x6e,
	0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x52, 0x75, 0x6c, 0x65, 0x2e, 0x41, 0x70, 0x70, 0x52, 0x75,
	0x6c, 0x65, 0x52, 0x07, 0x61, 0x70, 0x70, 0x52, 0x75, 0x6c, 0x65, 0x12, 0x3f, 0x0a, 0x0d, 0x65,
	0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x02, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x63, 0x66, 0x67, 0x5f, 0x64, 0x69, 0x73, 0x70, 0x61, 0x74, 0x63,
	0x68, 0x65, 0x72, 0x2e, 0x45, 0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x56, 0x32, 0x52, 0x0c,
	0x65, 0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x22, 0x85, 0x07, 0x0a,
	0x05, 0x52, 0x75, 0x6c, 0x65, 0x73, 0x12, 0x30, 0x0a, 0x05, 0x72, 0x75, 0x6c, 0x65, 0x73, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x63, 0x66, 0x67, 0x5f, 0x64, 0x69, 0x73, 0x70,
	0x61, 0x74, 0x63, 0x68, 0x65, 0x72, 0x2e, 0x52, 0x75, 0x6c, 0x65, 0x73, 0x2e, 0x52, 0x75, 0x6c,
	0x65, 0x52, 0x05, 0x72, 0x75, 0x6c, 0x65, 0x73, 0x12, 0x4b, 0x0a, 0x10, 0x67, 0x7a, 0x69, 0x70,
	0x5f, 0x62, 0x6c, 0x61, 0x63, 0x6b, 0x5f, 0x72, 0x75, 0x6c, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x21, 0x2e, 0x63, 0x66, 0x67, 0x5f, 0x64, 0x69, 0x73, 0x70, 0x61, 0x74, 0x63,
	0x68, 0x65, 0x72, 0x2e, 0x52, 0x75, 0x6c, 0x65, 0x73, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67,
	0x4d, 0x61, 0x74, 0x63, 0x68, 0x52, 0x0e, 0x67, 0x7a, 0x69, 0x70, 0x42, 0x6c, 0x61, 0x63, 0x6b,
	0x52, 0x75, 0x6c, 0x65, 0x73, 0x12, 0x4b, 0x0a, 0x10, 0x67, 0x7a, 0x69, 0x70, 0x5f, 0x77, 0x68,
	0x69, 0x74, 0x65, 0x5f, 0x72, 0x75, 0x6c, 0x65, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x21, 0x2e, 0x63, 0x66, 0x67, 0x5f, 0x64, 0x69, 0x73, 0x70, 0x61, 0x74, 0x63, 0x68, 0x65, 0x72,
	0x2e, 0x52, 0x75, 0x6c, 0x65, 0x73, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x4d, 0x61, 0x74,
	0x63, 0x68, 0x52, 0x0e, 0x67, 0x7a, 0x69, 0x70, 0x57, 0x68, 0x69, 0x74, 0x65, 0x52, 0x75, 0x6c,
	0x65, 0x73, 0x1a, 0xe4, 0x01, 0x0a, 0x0b, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x4d, 0x61, 0x74,
	0x63, 0x68, 0x12, 0x4a, 0x0a, 0x0a, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2b, 0x2e, 0x63, 0x66, 0x67, 0x5f, 0x64, 0x69, 0x73,
	0x70, 0x61, 0x74, 0x63, 0x68, 0x65, 0x72, 0x2e, 0x52, 0x75, 0x6c, 0x65, 0x73, 0x2e, 0x53, 0x74,
	0x72, 0x69, 0x6e, 0x67, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x2e, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x54,
	0x79, 0x70, 0x65, 0x52, 0x09, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1f,
	0x0a, 0x0b, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0a, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x22,
	0x68, 0x0a, 0x09, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1a, 0x0a, 0x16,
	0x4d, 0x41, 0x54, 0x43, 0x48, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45,
	0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x14, 0x0a, 0x10, 0x4d, 0x41, 0x54, 0x43,
	0x48, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x45, 0x58, 0x41, 0x43, 0x54, 0x10, 0x01, 0x12, 0x15,
	0x0a, 0x11, 0x4d, 0x41, 0x54, 0x43, 0x48, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x50, 0x52, 0x45,
	0x46, 0x49, 0x58, 0x10, 0x02, 0x12, 0x12, 0x0a, 0x0e, 0x4d, 0x41, 0x54, 0x43, 0x48, 0x5f, 0x54,
	0x59, 0x50, 0x45, 0x5f, 0x41, 0x4c, 0x4c, 0x10, 0x03, 0x1a, 0xc8, 0x03, 0x0a, 0x04, 0x52, 0x75,
	0x6c, 0x65, 0x12, 0x39, 0x0a, 0x06, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x21, 0x2e, 0x63, 0x66, 0x67, 0x5f, 0x64, 0x69, 0x73, 0x70, 0x61, 0x74, 0x63,
	0x68, 0x65, 0x72, 0x2e, 0x52, 0x75, 0x6c, 0x65, 0x73, 0x2e, 0x52, 0x75, 0x6c, 0x65, 0x2e, 0x41,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x06, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x3d, 0x0a,
	0x08, 0x61, 0x70, 0x70, 0x5f, 0x72, 0x75, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x22, 0x2e, 0x63, 0x66, 0x67, 0x5f, 0x64, 0x69, 0x73, 0x70, 0x61, 0x74, 0x63, 0x68, 0x65, 0x72,
	0x2e, 0x52, 0x75, 0x6c, 0x65, 0x73, 0x2e, 0x52, 0x75, 0x6c, 0x65, 0x2e, 0x41, 0x70, 0x70, 0x52,
	0x75, 0x6c, 0x65, 0x52, 0x07, 0x61, 0x70, 0x70, 0x52, 0x75, 0x6c, 0x65, 0x12, 0x3c, 0x0a, 0x08,
	0x61, 0x70, 0x69, 0x5f, 0x72, 0x75, 0x6c, 0x65, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x21,
	0x2e, 0x63, 0x66, 0x67, 0x5f, 0x64, 0x69, 0x73, 0x70, 0x61, 0x74, 0x63, 0x68, 0x65, 0x72, 0x2e,
	0x52, 0x75, 0x6c, 0x65, 0x73, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x4d, 0x61, 0x74, 0x63,
	0x68, 0x52, 0x07, 0x61, 0x70, 0x69, 0x52, 0x75, 0x6c, 0x65, 0x1a, 0xc2, 0x01, 0x0a, 0x07, 0x41,
	0x70, 0x70, 0x52, 0x75, 0x6c, 0x65, 0x12, 0x2d, 0x0a, 0x03, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x63, 0x66, 0x67, 0x5f, 0x64, 0x69, 0x73, 0x70, 0x61, 0x74,
	0x63, 0x68, 0x65, 0x72, 0x2e, 0x55, 0x69, 0x64, 0x49, 0x6e, 0x74, 0x4d, 0x61, 0x74, 0x63, 0x68,
	0x52, 0x03, 0x75, 0x69, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0d, 0x52, 0x0a, 0x63, 0x6c, 0x69, 0x65,
	0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74,
	0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0d, 0x52, 0x08, 0x6d, 0x61, 0x72, 0x6b, 0x65,
	0x74, 0x49, 0x64, 0x12, 0x4a, 0x0a, 0x0f, 0x61, 0x70, 0x70, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69,
	0x6f, 0x6e, 0x5f, 0x69, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x63,
	0x66, 0x67, 0x5f, 0x64, 0x69, 0x73, 0x70, 0x61, 0x74, 0x63, 0x68, 0x65, 0x72, 0x2e, 0x41, 0x70,
	0x70, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x74, 0x4d, 0x61, 0x74, 0x63, 0x68,
	0x52, 0x0d, 0x61, 0x70, 0x70, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x74, 0x22,
	0x43, 0x0a, 0x06, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x16, 0x0a, 0x12, 0x41, 0x43, 0x54,
	0x49, 0x4f, 0x4e, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10,
	0x00, 0x12, 0x10, 0x0a, 0x0c, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x41, 0x4c, 0x4c, 0x4f,
	0x57, 0x10, 0x01, 0x12, 0x0f, 0x0a, 0x0b, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x44, 0x45,
	0x4e, 0x59, 0x10, 0x02, 0x2a, 0xaa, 0x01, 0x0a, 0x0a, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x13, 0x0a, 0x0f, 0x45, 0x4e, 0x55, 0x4d, 0x5f, 0x43, 0x54, 0x5f, 0x55,
	0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x13, 0x0a, 0x0f, 0x45, 0x4e, 0x55, 0x4d,
	0x5f, 0x43, 0x54, 0x5f, 0x41, 0x4e, 0x44, 0x52, 0x4f, 0x49, 0x44, 0x10, 0x01, 0x12, 0x0f, 0x0a,
	0x0b, 0x45, 0x4e, 0x55, 0x4d, 0x5f, 0x43, 0x54, 0x5f, 0x49, 0x4f, 0x53, 0x10, 0x02, 0x12, 0x14,
	0x0a, 0x10, 0x45, 0x4e, 0x55, 0x4d, 0x5f, 0x43, 0x54, 0x5f, 0x57, 0x49, 0x4e, 0x50, 0x48, 0x4f,
	0x4e, 0x45, 0x10, 0x03, 0x12, 0x12, 0x0a, 0x0e, 0x45, 0x4e, 0x55, 0x4d, 0x5f, 0x43, 0x54, 0x5f,
	0x4d, 0x41, 0x43, 0x4f, 0x53, 0x58, 0x10, 0x04, 0x12, 0x13, 0x0a, 0x0f, 0x45, 0x4e, 0x55, 0x4d,
	0x5f, 0x43, 0x54, 0x5f, 0x57, 0x49, 0x4e, 0x44, 0x4f, 0x57, 0x53, 0x10, 0x05, 0x12, 0x11, 0x0a,
	0x0d, 0x45, 0x4e, 0x55, 0x4d, 0x5f, 0x43, 0x54, 0x5f, 0x4c, 0x49, 0x4e, 0x55, 0x58, 0x10, 0x06,
	0x12, 0x0f, 0x0a, 0x0b, 0x45, 0x4e, 0x55, 0x4d, 0x5f, 0x43, 0x54, 0x5f, 0x4d, 0x41, 0x58, 0x10,
	0x0f, 0x32, 0xe5, 0x01, 0x0a, 0x0d, 0x43, 0x66, 0x67, 0x44, 0x69, 0x73, 0x70, 0x61, 0x74, 0x63,
	0x68, 0x65, 0x72, 0x12, 0x5f, 0x0a, 0x10, 0x47, 0x65, 0x74, 0x47, 0x72, 0x70, 0x63, 0x45, 0x6e,
	0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x12, 0x23, 0x2e, 0x63, 0x66, 0x67, 0x5f, 0x64, 0x69,
	0x73, 0x70, 0x61, 0x74, 0x63, 0x68, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x47, 0x72, 0x70, 0x63,
	0x45, 0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x71, 0x1a, 0x24, 0x2e, 0x63,
	0x66, 0x67, 0x5f, 0x64, 0x69, 0x73, 0x70, 0x61, 0x74, 0x63, 0x68, 0x65, 0x72, 0x2e, 0x47, 0x65,
	0x74, 0x47, 0x72, 0x70, 0x63, 0x45, 0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x52, 0x65,
	0x73, 0x70, 0x22, 0x00, 0x12, 0x73, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x54, 0x72, 0x61, 0x6e, 0x73,
	0x70, 0x6f, 0x72, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x56, 0x32, 0x12, 0x2b, 0x2e, 0x63,
	0x66, 0x67, 0x5f, 0x64, 0x69, 0x73, 0x70, 0x61, 0x74, 0x63, 0x68, 0x65, 0x72, 0x2e, 0x47, 0x65,
	0x74, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x70, 0x6f, 0x72, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x56, 0x32, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2c, 0x2e, 0x63, 0x66, 0x67, 0x5f,
	0x64, 0x69, 0x73, 0x70, 0x61, 0x74, 0x63, 0x68, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x54, 0x72,
	0x61, 0x6e, 0x73, 0x70, 0x6f, 0x72, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x56, 0x32, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x42, 0x32, 0x5a, 0x30, 0x67, 0x6f, 0x6c,
	0x61, 0x6e, 0x67, 0x2e, 0x35, 0x32, 0x74, 0x74, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2f, 0x63,
	0x66, 0x67, 0x2d, 0x64, 0x69, 0x73, 0x70, 0x61, 0x74, 0x63, 0x68, 0x65, 0x72, 0x62, 0x06, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_tt_quicksilver_cfg_dispatcher_cfg_dispatcher_proto_rawDescOnce sync.Once
	file_tt_quicksilver_cfg_dispatcher_cfg_dispatcher_proto_rawDescData = file_tt_quicksilver_cfg_dispatcher_cfg_dispatcher_proto_rawDesc
)

func file_tt_quicksilver_cfg_dispatcher_cfg_dispatcher_proto_rawDescGZIP() []byte {
	file_tt_quicksilver_cfg_dispatcher_cfg_dispatcher_proto_rawDescOnce.Do(func() {
		file_tt_quicksilver_cfg_dispatcher_cfg_dispatcher_proto_rawDescData = protoimpl.X.CompressGZIP(file_tt_quicksilver_cfg_dispatcher_cfg_dispatcher_proto_rawDescData)
	})
	return file_tt_quicksilver_cfg_dispatcher_cfg_dispatcher_proto_rawDescData
}

var file_tt_quicksilver_cfg_dispatcher_cfg_dispatcher_proto_enumTypes = make([]protoimpl.EnumInfo, 4)
var file_tt_quicksilver_cfg_dispatcher_cfg_dispatcher_proto_msgTypes = make([]protoimpl.MessageInfo, 18)
var file_tt_quicksilver_cfg_dispatcher_cfg_dispatcher_proto_goTypes = []interface{}{
	(ClientType)(0),                              // 0: cfg_dispatcher.ClientType
	(EndpointV2_TLSConfig_TLSMode)(0),            // 1: cfg_dispatcher.EndpointV2.TLSConfig.TLSMode
	(Rules_StringMatch_MatchType)(0),             // 2: cfg_dispatcher.Rules.StringMatch.MatchType
	(Rules_Rule_Action)(0),                       // 3: cfg_dispatcher.Rules.Rule.Action
	(*GetGrpcEndpointsReq)(nil),                  // 4: cfg_dispatcher.GetGrpcEndpointsReq
	(*GetGrpcEndpointsResp)(nil),                 // 5: cfg_dispatcher.GetGrpcEndpointsResp
	(*AppInfo)(nil),                              // 6: cfg_dispatcher.AppInfo
	(*GetTransportConfigV2Request)(nil),          // 7: cfg_dispatcher.GetTransportConfigV2Request
	(*GetTransportConfigV2Response)(nil),         // 8: cfg_dispatcher.GetTransportConfigV2Response
	(*Scope)(nil),                                // 9: cfg_dispatcher.Scope
	(*UidIntMatch)(nil),                          // 10: cfg_dispatcher.UidIntMatch
	(*AppVersionIntMatch)(nil),                   // 11: cfg_dispatcher.AppVersionIntMatch
	(*EndpointV2)(nil),                           // 12: cfg_dispatcher.EndpointV2
	(*EndpointRule)(nil),                         // 13: cfg_dispatcher.EndpointRule
	(*Rules)(nil),                                // 14: cfg_dispatcher.Rules
	(*UidIntMatch_Modulo)(nil),                   // 15: cfg_dispatcher.UidIntMatch.Modulo
	(*EndpointV2_TLSConfig)(nil),                 // 16: cfg_dispatcher.EndpointV2.TLSConfig
	(*EndpointRule_AppRule)(nil),                 // 17: cfg_dispatcher.EndpointRule.AppRule
	(*EndpointRule_Rule)(nil),                    // 18: cfg_dispatcher.EndpointRule.Rule
	(*Rules_StringMatch)(nil),                    // 19: cfg_dispatcher.Rules.StringMatch
	(*Rules_Rule)(nil),                           // 20: cfg_dispatcher.Rules.Rule
	(*Rules_Rule_AppRule)(nil),                   // 21: cfg_dispatcher.Rules.Rule.AppRule
	(*transport.TransportConfig)(nil),            // 22: ga.transport.TransportConfig
	(*grpc_transport_cfg.TransportConfigV2)(nil), // 23: ga.grpc_transport_cfg.TransportConfigV2
}
var file_tt_quicksilver_cfg_dispatcher_cfg_dispatcher_proto_depIdxs = []int32{
	0,  // 0: cfg_dispatcher.GetGrpcEndpointsReq.client_type:type_name -> cfg_dispatcher.ClientType
	22, // 1: cfg_dispatcher.GetGrpcEndpointsResp.transport_config:type_name -> ga.transport.TransportConfig
	0,  // 2: cfg_dispatcher.AppInfo.client_type:type_name -> cfg_dispatcher.ClientType
	6,  // 3: cfg_dispatcher.GetTransportConfigV2Request.app_info:type_name -> cfg_dispatcher.AppInfo
	23, // 4: cfg_dispatcher.GetTransportConfigV2Response.transport_config_V2:type_name -> ga.grpc_transport_cfg.TransportConfigV2
	9,  // 5: cfg_dispatcher.UidIntMatch.scope:type_name -> cfg_dispatcher.Scope
	15, // 6: cfg_dispatcher.UidIntMatch.modulo:type_name -> cfg_dispatcher.UidIntMatch.Modulo
	16, // 7: cfg_dispatcher.EndpointV2.tls_config:type_name -> cfg_dispatcher.EndpointV2.TLSConfig
	18, // 8: cfg_dispatcher.EndpointRule.rules:type_name -> cfg_dispatcher.EndpointRule.Rule
	20, // 9: cfg_dispatcher.Rules.rules:type_name -> cfg_dispatcher.Rules.Rule
	19, // 10: cfg_dispatcher.Rules.gzip_black_rules:type_name -> cfg_dispatcher.Rules.StringMatch
	19, // 11: cfg_dispatcher.Rules.gzip_white_rules:type_name -> cfg_dispatcher.Rules.StringMatch
	1,  // 12: cfg_dispatcher.EndpointV2.TLSConfig.mode:type_name -> cfg_dispatcher.EndpointV2.TLSConfig.TLSMode
	10, // 13: cfg_dispatcher.EndpointRule.AppRule.uid:type_name -> cfg_dispatcher.UidIntMatch
	11, // 14: cfg_dispatcher.EndpointRule.AppRule.app_version_int:type_name -> cfg_dispatcher.AppVersionIntMatch
	17, // 15: cfg_dispatcher.EndpointRule.Rule.app_rule:type_name -> cfg_dispatcher.EndpointRule.AppRule
	12, // 16: cfg_dispatcher.EndpointRule.Rule.endpoint_list:type_name -> cfg_dispatcher.EndpointV2
	2,  // 17: cfg_dispatcher.Rules.StringMatch.match_type:type_name -> cfg_dispatcher.Rules.StringMatch.MatchType
	3,  // 18: cfg_dispatcher.Rules.Rule.action:type_name -> cfg_dispatcher.Rules.Rule.Action
	21, // 19: cfg_dispatcher.Rules.Rule.app_rule:type_name -> cfg_dispatcher.Rules.Rule.AppRule
	19, // 20: cfg_dispatcher.Rules.Rule.api_rule:type_name -> cfg_dispatcher.Rules.StringMatch
	10, // 21: cfg_dispatcher.Rules.Rule.AppRule.uid:type_name -> cfg_dispatcher.UidIntMatch
	11, // 22: cfg_dispatcher.Rules.Rule.AppRule.app_version_int:type_name -> cfg_dispatcher.AppVersionIntMatch
	4,  // 23: cfg_dispatcher.CfgDispatcher.GetGrpcEndpoints:input_type -> cfg_dispatcher.GetGrpcEndpointsReq
	7,  // 24: cfg_dispatcher.CfgDispatcher.GetTransportConfigV2:input_type -> cfg_dispatcher.GetTransportConfigV2Request
	5,  // 25: cfg_dispatcher.CfgDispatcher.GetGrpcEndpoints:output_type -> cfg_dispatcher.GetGrpcEndpointsResp
	8,  // 26: cfg_dispatcher.CfgDispatcher.GetTransportConfigV2:output_type -> cfg_dispatcher.GetTransportConfigV2Response
	25, // [25:27] is the sub-list for method output_type
	23, // [23:25] is the sub-list for method input_type
	23, // [23:23] is the sub-list for extension type_name
	23, // [23:23] is the sub-list for extension extendee
	0,  // [0:23] is the sub-list for field type_name
}

func init() { file_tt_quicksilver_cfg_dispatcher_cfg_dispatcher_proto_init() }
func file_tt_quicksilver_cfg_dispatcher_cfg_dispatcher_proto_init() {
	if File_tt_quicksilver_cfg_dispatcher_cfg_dispatcher_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_tt_quicksilver_cfg_dispatcher_cfg_dispatcher_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetGrpcEndpointsReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tt_quicksilver_cfg_dispatcher_cfg_dispatcher_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetGrpcEndpointsResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tt_quicksilver_cfg_dispatcher_cfg_dispatcher_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AppInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tt_quicksilver_cfg_dispatcher_cfg_dispatcher_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetTransportConfigV2Request); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tt_quicksilver_cfg_dispatcher_cfg_dispatcher_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetTransportConfigV2Response); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tt_quicksilver_cfg_dispatcher_cfg_dispatcher_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Scope); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tt_quicksilver_cfg_dispatcher_cfg_dispatcher_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UidIntMatch); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tt_quicksilver_cfg_dispatcher_cfg_dispatcher_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AppVersionIntMatch); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tt_quicksilver_cfg_dispatcher_cfg_dispatcher_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EndpointV2); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tt_quicksilver_cfg_dispatcher_cfg_dispatcher_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EndpointRule); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tt_quicksilver_cfg_dispatcher_cfg_dispatcher_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Rules); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tt_quicksilver_cfg_dispatcher_cfg_dispatcher_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UidIntMatch_Modulo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tt_quicksilver_cfg_dispatcher_cfg_dispatcher_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EndpointV2_TLSConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tt_quicksilver_cfg_dispatcher_cfg_dispatcher_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EndpointRule_AppRule); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tt_quicksilver_cfg_dispatcher_cfg_dispatcher_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EndpointRule_Rule); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tt_quicksilver_cfg_dispatcher_cfg_dispatcher_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Rules_StringMatch); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tt_quicksilver_cfg_dispatcher_cfg_dispatcher_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Rules_Rule); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tt_quicksilver_cfg_dispatcher_cfg_dispatcher_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Rules_Rule_AppRule); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_tt_quicksilver_cfg_dispatcher_cfg_dispatcher_proto_msgTypes[6].OneofWrappers = []interface{}{
		(*UidIntMatch_Exact)(nil),
		(*UidIntMatch_Scope)(nil),
		(*UidIntMatch_Modulo_)(nil),
	}
	file_tt_quicksilver_cfg_dispatcher_cfg_dispatcher_proto_msgTypes[7].OneofWrappers = []interface{}{
		(*AppVersionIntMatch_Gt)(nil),
		(*AppVersionIntMatch_Lt)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_tt_quicksilver_cfg_dispatcher_cfg_dispatcher_proto_rawDesc,
			NumEnums:      4,
			NumMessages:   18,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_tt_quicksilver_cfg_dispatcher_cfg_dispatcher_proto_goTypes,
		DependencyIndexes: file_tt_quicksilver_cfg_dispatcher_cfg_dispatcher_proto_depIdxs,
		EnumInfos:         file_tt_quicksilver_cfg_dispatcher_cfg_dispatcher_proto_enumTypes,
		MessageInfos:      file_tt_quicksilver_cfg_dispatcher_cfg_dispatcher_proto_msgTypes,
	}.Build()
	File_tt_quicksilver_cfg_dispatcher_cfg_dispatcher_proto = out.File
	file_tt_quicksilver_cfg_dispatcher_cfg_dispatcher_proto_rawDesc = nil
	file_tt_quicksilver_cfg_dispatcher_cfg_dispatcher_proto_goTypes = nil
	file_tt_quicksilver_cfg_dispatcher_cfg_dispatcher_proto_depIdxs = nil
}
