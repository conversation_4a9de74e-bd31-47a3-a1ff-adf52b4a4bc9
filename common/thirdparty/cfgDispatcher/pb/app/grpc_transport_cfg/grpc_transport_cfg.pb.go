// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.32.0
// 	protoc        v4.25.2
// source: grpc_transport_cfg/grpc_transport_cfg.proto

package grpc_transport_cfg

import (
	reflect "reflect"
	sync "sync"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"

	app "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/thirdparty/cfgDispatcher/pb/app"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type RefreshTransportConfigRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BaseReq          *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	PreviousCheckSum string       `protobuf:"bytes,2,opt,name=previous_check_sum,json=previousCheckSum,proto3" json:"previous_check_sum,omitempty"`
}

func (x *RefreshTransportConfigRequest) Reset() {
	*x = RefreshTransportConfigRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_grpc_transport_cfg_grpc_transport_cfg_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RefreshTransportConfigRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RefreshTransportConfigRequest) ProtoMessage() {}

func (x *RefreshTransportConfigRequest) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_transport_cfg_grpc_transport_cfg_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RefreshTransportConfigRequest.ProtoReflect.Descriptor instead.
func (*RefreshTransportConfigRequest) Descriptor() ([]byte, []int) {
	return file_grpc_transport_cfg_grpc_transport_cfg_proto_rawDescGZIP(), []int{0}
}

func (x *RefreshTransportConfigRequest) GetBaseReq() *app.BaseReq {
	if x != nil {
		return x.BaseReq
	}
	return nil
}

func (x *RefreshTransportConfigRequest) GetPreviousCheckSum() string {
	if x != nil {
		return x.PreviousCheckSum
	}
	return ""
}

type RefreshTransportConfigResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BaseResp        *app.BaseResp      `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	TransportConfig *TransportConfigV2 `protobuf:"bytes,2,opt,name=transport_config,json=transportConfig,proto3" json:"transport_config,omitempty"`
	NewCheckSum     string             `protobuf:"bytes,3,opt,name=new_check_sum,json=newCheckSum,proto3" json:"new_check_sum,omitempty"`
}

func (x *RefreshTransportConfigResponse) Reset() {
	*x = RefreshTransportConfigResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_grpc_transport_cfg_grpc_transport_cfg_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RefreshTransportConfigResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RefreshTransportConfigResponse) ProtoMessage() {}

func (x *RefreshTransportConfigResponse) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_transport_cfg_grpc_transport_cfg_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RefreshTransportConfigResponse.ProtoReflect.Descriptor instead.
func (*RefreshTransportConfigResponse) Descriptor() ([]byte, []int) {
	return file_grpc_transport_cfg_grpc_transport_cfg_proto_rawDescGZIP(), []int{1}
}

func (x *RefreshTransportConfigResponse) GetBaseResp() *app.BaseResp {
	if x != nil {
		return x.BaseResp
	}
	return nil
}

func (x *RefreshTransportConfigResponse) GetTransportConfig() *TransportConfigV2 {
	if x != nil {
		return x.TransportConfig
	}
	return nil
}

func (x *RefreshTransportConfigResponse) GetNewCheckSum() string {
	if x != nil {
		return x.NewCheckSum
	}
	return ""
}

var File_grpc_transport_cfg_grpc_transport_cfg_proto protoreflect.FileDescriptor

var file_grpc_transport_cfg_grpc_transport_cfg_proto_rawDesc = []byte{
	0x0a, 0x2b, 0x67, 0x72, 0x70, 0x63, 0x5f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x70, 0x6f, 0x72, 0x74,
	0x5f, 0x63, 0x66, 0x67, 0x2f, 0x67, 0x72, 0x70, 0x63, 0x5f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x70,
	0x6f, 0x72, 0x74, 0x5f, 0x63, 0x66, 0x67, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x15, 0x67,
	0x61, 0x2e, 0x67, 0x72, 0x70, 0x63, 0x5f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x70, 0x6f, 0x72, 0x74,
	0x5f, 0x63, 0x66, 0x67, 0x1a, 0x0d, 0x67, 0x61, 0x5f, 0x62, 0x61, 0x73, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x25, 0x67, 0x72, 0x70, 0x63, 0x5f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x70,
	0x6f, 0x72, 0x74, 0x5f, 0x63, 0x66, 0x67, 0x2f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x70, 0x6f, 0x72,
	0x74, 0x5f, 0x76, 0x32, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x75, 0x0a, 0x1d, 0x52, 0x65,
	0x66, 0x72, 0x65, 0x73, 0x68, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x70, 0x6f, 0x72, 0x74, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x26, 0x0a, 0x08, 0x62,
	0x61, 0x73, 0x65, 0x5f, 0x72, 0x65, 0x71, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e,
	0x67, 0x61, 0x2e, 0x42, 0x61, 0x73, 0x65, 0x52, 0x65, 0x71, 0x52, 0x07, 0x62, 0x61, 0x73, 0x65,
	0x52, 0x65, 0x71, 0x12, 0x2c, 0x0a, 0x12, 0x70, 0x72, 0x65, 0x76, 0x69, 0x6f, 0x75, 0x73, 0x5f,
	0x63, 0x68, 0x65, 0x63, 0x6b, 0x5f, 0x73, 0x75, 0x6d, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x10, 0x70, 0x72, 0x65, 0x76, 0x69, 0x6f, 0x75, 0x73, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x53, 0x75,
	0x6d, 0x22, 0xc4, 0x01, 0x0a, 0x1e, 0x52, 0x65, 0x66, 0x72, 0x65, 0x73, 0x68, 0x54, 0x72, 0x61,
	0x6e, 0x73, 0x70, 0x6f, 0x72, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x29, 0x0a, 0x09, 0x62, 0x61, 0x73, 0x65, 0x5f, 0x72, 0x65, 0x73,
	0x70, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0c, 0x2e, 0x67, 0x61, 0x2e, 0x42, 0x61, 0x73,
	0x65, 0x52, 0x65, 0x73, 0x70, 0x52, 0x08, 0x62, 0x61, 0x73, 0x65, 0x52, 0x65, 0x73, 0x70, 0x12,
	0x53, 0x0a, 0x10, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x63, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x67, 0x61, 0x2e, 0x67,
	0x72, 0x70, 0x63, 0x5f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x63, 0x66,
	0x67, 0x2e, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x70, 0x6f, 0x72, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x56, 0x32, 0x52, 0x0f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x70, 0x6f, 0x72, 0x74, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x12, 0x22, 0x0a, 0x0d, 0x6e, 0x65, 0x77, 0x5f, 0x63, 0x68, 0x65, 0x63,
	0x6b, 0x5f, 0x73, 0x75, 0x6d, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x6e, 0x65, 0x77,
	0x43, 0x68, 0x65, 0x63, 0x6b, 0x53, 0x75, 0x6d, 0x42, 0x4b, 0x0a, 0x18, 0x63, 0x6f, 0x6d, 0x2e,
	0x79, 0x69, 0x79, 0x6f, 0x75, 0x2e, 0x67, 0x61, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x5a, 0x2f, 0x67, 0x6f, 0x6c, 0x61, 0x6e, 0x67, 0x2e, 0x35, 0x32, 0x74,
	0x74, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x2f, 0x61,
	0x70, 0x70, 0x2f, 0x67, 0x72, 0x70, 0x63, 0x2d, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x70, 0x6f, 0x72,
	0x74, 0x2d, 0x63, 0x66, 0x67, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_grpc_transport_cfg_grpc_transport_cfg_proto_rawDescOnce sync.Once
	file_grpc_transport_cfg_grpc_transport_cfg_proto_rawDescData = file_grpc_transport_cfg_grpc_transport_cfg_proto_rawDesc
)

func file_grpc_transport_cfg_grpc_transport_cfg_proto_rawDescGZIP() []byte {
	file_grpc_transport_cfg_grpc_transport_cfg_proto_rawDescOnce.Do(func() {
		file_grpc_transport_cfg_grpc_transport_cfg_proto_rawDescData = protoimpl.X.CompressGZIP(file_grpc_transport_cfg_grpc_transport_cfg_proto_rawDescData)
	})
	return file_grpc_transport_cfg_grpc_transport_cfg_proto_rawDescData
}

var file_grpc_transport_cfg_grpc_transport_cfg_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_grpc_transport_cfg_grpc_transport_cfg_proto_goTypes = []interface{}{
	(*RefreshTransportConfigRequest)(nil),  // 0: ga.grpc_transport_cfg.RefreshTransportConfigRequest
	(*RefreshTransportConfigResponse)(nil), // 1: ga.grpc_transport_cfg.RefreshTransportConfigResponse
	(*app.BaseReq)(nil),                    // 2: ga.BaseReq
	(*app.BaseResp)(nil),                   // 3: ga.BaseResp
	(*TransportConfigV2)(nil),              // 4: ga.grpc_transport_cfg.TransportConfigV2
}
var file_grpc_transport_cfg_grpc_transport_cfg_proto_depIdxs = []int32{
	2, // 0: ga.grpc_transport_cfg.RefreshTransportConfigRequest.base_req:type_name -> ga.BaseReq
	3, // 1: ga.grpc_transport_cfg.RefreshTransportConfigResponse.base_resp:type_name -> ga.BaseResp
	4, // 2: ga.grpc_transport_cfg.RefreshTransportConfigResponse.transport_config:type_name -> ga.grpc_transport_cfg.TransportConfigV2
	3, // [3:3] is the sub-list for method output_type
	3, // [3:3] is the sub-list for method input_type
	3, // [3:3] is the sub-list for extension type_name
	3, // [3:3] is the sub-list for extension extendee
	0, // [0:3] is the sub-list for field type_name
}

func init() { file_grpc_transport_cfg_grpc_transport_cfg_proto_init() }
func file_grpc_transport_cfg_grpc_transport_cfg_proto_init() {
	if File_grpc_transport_cfg_grpc_transport_cfg_proto != nil {
		return
	}
	file_grpc_transport_cfg_transport_v2_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_grpc_transport_cfg_grpc_transport_cfg_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RefreshTransportConfigRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_grpc_transport_cfg_grpc_transport_cfg_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RefreshTransportConfigResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_grpc_transport_cfg_grpc_transport_cfg_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_grpc_transport_cfg_grpc_transport_cfg_proto_goTypes,
		DependencyIndexes: file_grpc_transport_cfg_grpc_transport_cfg_proto_depIdxs,
		MessageInfos:      file_grpc_transport_cfg_grpc_transport_cfg_proto_msgTypes,
	}.Build()
	File_grpc_transport_cfg_grpc_transport_cfg_proto = out.File
	file_grpc_transport_cfg_grpc_transport_cfg_proto_rawDesc = nil
	file_grpc_transport_cfg_grpc_transport_cfg_proto_goTypes = nil
	file_grpc_transport_cfg_grpc_transport_cfg_proto_depIdxs = nil
}
