//这里不用proto3会因为proto2的proto引用这个proto时，如果是proto3版本，会让服务端编译不了

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.32.0
// 	protoc        v4.25.2
// source: grpc_transport_cfg/transport_v2.proto

package grpc_transport_cfg

import (
	reflect "reflect"
	sync "sync"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type EndpointV2_TLSConfig_TLSMode int32

const (
	EndpointV2_TLSConfig_TLS_MODE_DISABLE EndpointV2_TLSConfig_TLSMode = 0
	EndpointV2_TLSConfig_TLS_MODE_SIMPLE  EndpointV2_TLSConfig_TLSMode = 1
	EndpointV2_TLSConfig_TLS_MODE_MUTUAL  EndpointV2_TLSConfig_TLSMode = 2
)

// Enum value maps for EndpointV2_TLSConfig_TLSMode.
var (
	EndpointV2_TLSConfig_TLSMode_name = map[int32]string{
		0: "TLS_MODE_DISABLE",
		1: "TLS_MODE_SIMPLE",
		2: "TLS_MODE_MUTUAL",
	}
	EndpointV2_TLSConfig_TLSMode_value = map[string]int32{
		"TLS_MODE_DISABLE": 0,
		"TLS_MODE_SIMPLE":  1,
		"TLS_MODE_MUTUAL":  2,
	}
)

func (x EndpointV2_TLSConfig_TLSMode) Enum() *EndpointV2_TLSConfig_TLSMode {
	p := new(EndpointV2_TLSConfig_TLSMode)
	*p = x
	return p
}

func (x EndpointV2_TLSConfig_TLSMode) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (EndpointV2_TLSConfig_TLSMode) Descriptor() protoreflect.EnumDescriptor {
	return file_grpc_transport_cfg_transport_v2_proto_enumTypes[0].Descriptor()
}

func (EndpointV2_TLSConfig_TLSMode) Type() protoreflect.EnumType {
	return &file_grpc_transport_cfg_transport_v2_proto_enumTypes[0]
}

func (x EndpointV2_TLSConfig_TLSMode) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *EndpointV2_TLSConfig_TLSMode) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = EndpointV2_TLSConfig_TLSMode(num)
	return nil
}

// Deprecated: Use EndpointV2_TLSConfig_TLSMode.Descriptor instead.
func (EndpointV2_TLSConfig_TLSMode) EnumDescriptor() ([]byte, []int) {
	return file_grpc_transport_cfg_transport_v2_proto_rawDescGZIP(), []int{0, 0, 0}
}

type StringMatch_MatchType int32

const (
	StringMatch_MATCH_TYPE_UNSPECIFIED StringMatch_MatchType = 0
	StringMatch_MATCH_TYPE_EXACT       StringMatch_MatchType = 1
	StringMatch_MATCH_TYPE_PREFIX      StringMatch_MatchType = 2
	StringMatch_MATCH_TYPE_ALL         StringMatch_MatchType = 3
)

// Enum value maps for StringMatch_MatchType.
var (
	StringMatch_MatchType_name = map[int32]string{
		0: "MATCH_TYPE_UNSPECIFIED",
		1: "MATCH_TYPE_EXACT",
		2: "MATCH_TYPE_PREFIX",
		3: "MATCH_TYPE_ALL",
	}
	StringMatch_MatchType_value = map[string]int32{
		"MATCH_TYPE_UNSPECIFIED": 0,
		"MATCH_TYPE_EXACT":       1,
		"MATCH_TYPE_PREFIX":      2,
		"MATCH_TYPE_ALL":         3,
	}
)

func (x StringMatch_MatchType) Enum() *StringMatch_MatchType {
	p := new(StringMatch_MatchType)
	*p = x
	return p
}

func (x StringMatch_MatchType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (StringMatch_MatchType) Descriptor() protoreflect.EnumDescriptor {
	return file_grpc_transport_cfg_transport_v2_proto_enumTypes[1].Descriptor()
}

func (StringMatch_MatchType) Type() protoreflect.EnumType {
	return &file_grpc_transport_cfg_transport_v2_proto_enumTypes[1]
}

func (x StringMatch_MatchType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *StringMatch_MatchType) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = StringMatch_MatchType(num)
	return nil
}

// Deprecated: Use StringMatch_MatchType.Descriptor instead.
func (StringMatch_MatchType) EnumDescriptor() ([]byte, []int) {
	return file_grpc_transport_cfg_transport_v2_proto_rawDescGZIP(), []int{2, 0}
}

type EndpointV2 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// address like: dev-apv2.ttyuyin.com:443
	Address   *string               `protobuf:"bytes,1,opt,name=address" json:"address,omitempty"`
	TlsConfig *EndpointV2_TLSConfig `protobuf:"bytes,2,opt,name=tls_config,json=tlsConfig" json:"tls_config,omitempty"`
}

func (x *EndpointV2) Reset() {
	*x = EndpointV2{}
	if protoimpl.UnsafeEnabled {
		mi := &file_grpc_transport_cfg_transport_v2_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EndpointV2) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EndpointV2) ProtoMessage() {}

func (x *EndpointV2) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_transport_cfg_transport_v2_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EndpointV2.ProtoReflect.Descriptor instead.
func (*EndpointV2) Descriptor() ([]byte, []int) {
	return file_grpc_transport_cfg_transport_v2_proto_rawDescGZIP(), []int{0}
}

func (x *EndpointV2) GetAddress() string {
	if x != nil && x.Address != nil {
		return *x.Address
	}
	return ""
}

func (x *EndpointV2) GetTlsConfig() *EndpointV2_TLSConfig {
	if x != nil {
		return x.TlsConfig
	}
	return nil
}

// 大背景 全面grpc通信
type TransportConfigV2 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// endpoints 有则采用endpoint，没有则让客户端hard code使用现有域名：apiv2.ttyuyin.com
	Endpoints []*EndpointV2 `protobuf:"bytes,1,rep,name=endpoints" json:"endpoints,omitempty"`
	// expire_at字段和min_app_version字段好像没有用了，已经是全面grpc通信
	BlackListApiInfo     *ApiInfo  `protobuf:"bytes,2,opt,name=black_list_api_info,json=blackListApiInfo" json:"black_list_api_info,omitempty"`
	GzipBlackListApiInfo *GzipInfo `protobuf:"bytes,3,opt,name=gzip_black_list_api_info,json=gzipBlackListApiInfo" json:"gzip_black_list_api_info,omitempty"` //gzip黑名单优先
	GzipWhiteListApiInfo *GzipInfo `protobuf:"bytes,4,opt,name=gzip_white_list_api_info,json=gzipWhiteListApiInfo" json:"gzip_white_list_api_info,omitempty"` //gzip白名单
}

func (x *TransportConfigV2) Reset() {
	*x = TransportConfigV2{}
	if protoimpl.UnsafeEnabled {
		mi := &file_grpc_transport_cfg_transport_v2_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TransportConfigV2) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TransportConfigV2) ProtoMessage() {}

func (x *TransportConfigV2) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_transport_cfg_transport_v2_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TransportConfigV2.ProtoReflect.Descriptor instead.
func (*TransportConfigV2) Descriptor() ([]byte, []int) {
	return file_grpc_transport_cfg_transport_v2_proto_rawDescGZIP(), []int{1}
}

func (x *TransportConfigV2) GetEndpoints() []*EndpointV2 {
	if x != nil {
		return x.Endpoints
	}
	return nil
}

func (x *TransportConfigV2) GetBlackListApiInfo() *ApiInfo {
	if x != nil {
		return x.BlackListApiInfo
	}
	return nil
}

func (x *TransportConfigV2) GetGzipBlackListApiInfo() *GzipInfo {
	if x != nil {
		return x.GzipBlackListApiInfo
	}
	return nil
}

func (x *TransportConfigV2) GetGzipWhiteListApiInfo() *GzipInfo {
	if x != nil {
		return x.GzipWhiteListApiInfo
	}
	return nil
}

// oneof类型再服务端这边因为proto插件的原因，导致编译不了，所以只能寻找其他的方式来实现
type StringMatch struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MatchType  *StringMatch_MatchType `protobuf:"varint,1,opt,name=match_type,json=matchType,enum=ga.grpc_transport_cfg.StringMatch_MatchType" json:"match_type,omitempty"`
	MatchValue *string                `protobuf:"bytes,2,opt,name=match_value,json=matchValue" json:"match_value,omitempty"`
}

func (x *StringMatch) Reset() {
	*x = StringMatch{}
	if protoimpl.UnsafeEnabled {
		mi := &file_grpc_transport_cfg_transport_v2_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StringMatch) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StringMatch) ProtoMessage() {}

func (x *StringMatch) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_transport_cfg_transport_v2_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StringMatch.ProtoReflect.Descriptor instead.
func (*StringMatch) Descriptor() ([]byte, []int) {
	return file_grpc_transport_cfg_transport_v2_proto_rawDescGZIP(), []int{2}
}

func (x *StringMatch) GetMatchType() StringMatch_MatchType {
	if x != nil && x.MatchType != nil {
		return *x.MatchType
	}
	return StringMatch_MATCH_TYPE_UNSPECIFIED
}

func (x *StringMatch) GetMatchValue() string {
	if x != nil && x.MatchValue != nil {
		return *x.MatchValue
	}
	return ""
}

type ApiInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ApiRule []*StringMatch `protobuf:"bytes,1,rep,name=api_rule,json=apiRule" json:"api_rule,omitempty"`
}

func (x *ApiInfo) Reset() {
	*x = ApiInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_grpc_transport_cfg_transport_v2_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ApiInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ApiInfo) ProtoMessage() {}

func (x *ApiInfo) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_transport_cfg_transport_v2_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ApiInfo.ProtoReflect.Descriptor instead.
func (*ApiInfo) Descriptor() ([]byte, []int) {
	return file_grpc_transport_cfg_transport_v2_proto_rawDescGZIP(), []int{3}
}

func (x *ApiInfo) GetApiRule() []*StringMatch {
	if x != nil {
		return x.ApiRule
	}
	return nil
}

type GzipInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ApiRule []*StringMatch `protobuf:"bytes,1,rep,name=api_rule,json=apiRule" json:"api_rule,omitempty"`
}

func (x *GzipInfo) Reset() {
	*x = GzipInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_grpc_transport_cfg_transport_v2_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GzipInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GzipInfo) ProtoMessage() {}

func (x *GzipInfo) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_transport_cfg_transport_v2_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GzipInfo.ProtoReflect.Descriptor instead.
func (*GzipInfo) Descriptor() ([]byte, []int) {
	return file_grpc_transport_cfg_transport_v2_proto_rawDescGZIP(), []int{4}
}

func (x *GzipInfo) GetApiRule() []*StringMatch {
	if x != nil {
		return x.ApiRule
	}
	return nil
}

type EndpointV2_TLSConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Mode      *EndpointV2_TLSConfig_TLSMode `protobuf:"varint,1,opt,name=mode,enum=ga.grpc_transport_cfg.EndpointV2_TLSConfig_TLSMode" json:"mode,omitempty"`
	Authority *string                       `protobuf:"bytes,2,opt,name=authority" json:"authority,omitempty"`
}

func (x *EndpointV2_TLSConfig) Reset() {
	*x = EndpointV2_TLSConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_grpc_transport_cfg_transport_v2_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EndpointV2_TLSConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EndpointV2_TLSConfig) ProtoMessage() {}

func (x *EndpointV2_TLSConfig) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_transport_cfg_transport_v2_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EndpointV2_TLSConfig.ProtoReflect.Descriptor instead.
func (*EndpointV2_TLSConfig) Descriptor() ([]byte, []int) {
	return file_grpc_transport_cfg_transport_v2_proto_rawDescGZIP(), []int{0, 0}
}

func (x *EndpointV2_TLSConfig) GetMode() EndpointV2_TLSConfig_TLSMode {
	if x != nil && x.Mode != nil {
		return *x.Mode
	}
	return EndpointV2_TLSConfig_TLS_MODE_DISABLE
}

func (x *EndpointV2_TLSConfig) GetAuthority() string {
	if x != nil && x.Authority != nil {
		return *x.Authority
	}
	return ""
}

var File_grpc_transport_cfg_transport_v2_proto protoreflect.FileDescriptor

var file_grpc_transport_cfg_transport_v2_proto_rawDesc = []byte{
	0x0a, 0x25, 0x67, 0x72, 0x70, 0x63, 0x5f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x70, 0x6f, 0x72, 0x74,
	0x5f, 0x63, 0x66, 0x67, 0x2f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x76,
	0x32, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x15, 0x67, 0x61, 0x2e, 0x67, 0x72, 0x70, 0x63,
	0x5f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x63, 0x66, 0x67, 0x22, 0xb2,
	0x02, 0x0a, 0x0a, 0x45, 0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x56, 0x32, 0x12, 0x18, 0x0a,
	0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x4a, 0x0a, 0x0a, 0x74, 0x6c, 0x73, 0x5f, 0x63,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x67, 0x61,
	0x2e, 0x67, 0x72, 0x70, 0x63, 0x5f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x70, 0x6f, 0x72, 0x74, 0x5f,
	0x63, 0x66, 0x67, 0x2e, 0x45, 0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x56, 0x32, 0x2e, 0x54,
	0x4c, 0x53, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x09, 0x74, 0x6c, 0x73, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x1a, 0xbd, 0x01, 0x0a, 0x09, 0x54, 0x4c, 0x53, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x12, 0x47, 0x0a, 0x04, 0x6d, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x33, 0x2e, 0x67, 0x61, 0x2e, 0x67, 0x72, 0x70, 0x63, 0x5f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x70,
	0x6f, 0x72, 0x74, 0x5f, 0x63, 0x66, 0x67, 0x2e, 0x45, 0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74,
	0x56, 0x32, 0x2e, 0x54, 0x4c, 0x53, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x54, 0x4c, 0x53,
	0x4d, 0x6f, 0x64, 0x65, 0x52, 0x04, 0x6d, 0x6f, 0x64, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x61, 0x75,
	0x74, 0x68, 0x6f, 0x72, 0x69, 0x74, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61,
	0x75, 0x74, 0x68, 0x6f, 0x72, 0x69, 0x74, 0x79, 0x22, 0x49, 0x0a, 0x07, 0x54, 0x4c, 0x53, 0x4d,
	0x6f, 0x64, 0x65, 0x12, 0x14, 0x0a, 0x10, 0x54, 0x4c, 0x53, 0x5f, 0x4d, 0x4f, 0x44, 0x45, 0x5f,
	0x44, 0x49, 0x53, 0x41, 0x42, 0x4c, 0x45, 0x10, 0x00, 0x12, 0x13, 0x0a, 0x0f, 0x54, 0x4c, 0x53,
	0x5f, 0x4d, 0x4f, 0x44, 0x45, 0x5f, 0x53, 0x49, 0x4d, 0x50, 0x4c, 0x45, 0x10, 0x01, 0x12, 0x13,
	0x0a, 0x0f, 0x54, 0x4c, 0x53, 0x5f, 0x4d, 0x4f, 0x44, 0x45, 0x5f, 0x4d, 0x55, 0x54, 0x55, 0x41,
	0x4c, 0x10, 0x02, 0x22, 0xd5, 0x02, 0x0a, 0x11, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x70, 0x6f, 0x72,
	0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x56, 0x32, 0x12, 0x3f, 0x0a, 0x09, 0x65, 0x6e, 0x64,
	0x70, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x67,
	0x61, 0x2e, 0x67, 0x72, 0x70, 0x63, 0x5f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x70, 0x6f, 0x72, 0x74,
	0x5f, 0x63, 0x66, 0x67, 0x2e, 0x45, 0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x56, 0x32, 0x52,
	0x09, 0x65, 0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x12, 0x4d, 0x0a, 0x13, 0x62, 0x6c,
	0x61, 0x63, 0x6b, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x5f, 0x61, 0x70, 0x69, 0x5f, 0x69, 0x6e, 0x66,
	0x6f, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x67, 0x61, 0x2e, 0x67, 0x72, 0x70,
	0x63, 0x5f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x63, 0x66, 0x67, 0x2e,
	0x41, 0x70, 0x69, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x10, 0x62, 0x6c, 0x61, 0x63, 0x6b, 0x4c, 0x69,
	0x73, 0x74, 0x41, 0x70, 0x69, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x57, 0x0a, 0x18, 0x67, 0x7a, 0x69,
	0x70, 0x5f, 0x62, 0x6c, 0x61, 0x63, 0x6b, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x5f, 0x61, 0x70, 0x69,
	0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x67, 0x61,
	0x2e, 0x67, 0x72, 0x70, 0x63, 0x5f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x70, 0x6f, 0x72, 0x74, 0x5f,
	0x63, 0x66, 0x67, 0x2e, 0x47, 0x7a, 0x69, 0x70, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x14, 0x67, 0x7a,
	0x69, 0x70, 0x42, 0x6c, 0x61, 0x63, 0x6b, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x70, 0x69, 0x49, 0x6e,
	0x66, 0x6f, 0x12, 0x57, 0x0a, 0x18, 0x67, 0x7a, 0x69, 0x70, 0x5f, 0x77, 0x68, 0x69, 0x74, 0x65,
	0x5f, 0x6c, 0x69, 0x73, 0x74, 0x5f, 0x61, 0x70, 0x69, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x67, 0x61, 0x2e, 0x67, 0x72, 0x70, 0x63, 0x5f, 0x74,
	0x72, 0x61, 0x6e, 0x73, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x63, 0x66, 0x67, 0x2e, 0x47, 0x7a, 0x69,
	0x70, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x14, 0x67, 0x7a, 0x69, 0x70, 0x57, 0x68, 0x69, 0x74, 0x65,
	0x4c, 0x69, 0x73, 0x74, 0x41, 0x70, 0x69, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0xe5, 0x01, 0x0a, 0x0b,
	0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x12, 0x4b, 0x0a, 0x0a, 0x6d,
	0x61, 0x74, 0x63, 0x68, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x2c, 0x2e, 0x67, 0x61, 0x2e, 0x67, 0x72, 0x70, 0x63, 0x5f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x70,
	0x6f, 0x72, 0x74, 0x5f, 0x63, 0x66, 0x67, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x4d, 0x61,
	0x74, 0x63, 0x68, 0x2e, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x54, 0x79, 0x70, 0x65, 0x52, 0x09, 0x6d,
	0x61, 0x74, 0x63, 0x68, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x6d, 0x61, 0x74, 0x63,
	0x68, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x6d,
	0x61, 0x74, 0x63, 0x68, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x22, 0x68, 0x0a, 0x09, 0x4d, 0x61, 0x74,
	0x63, 0x68, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1a, 0x0a, 0x16, 0x4d, 0x41, 0x54, 0x43, 0x48, 0x5f,
	0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44,
	0x10, 0x00, 0x12, 0x14, 0x0a, 0x10, 0x4d, 0x41, 0x54, 0x43, 0x48, 0x5f, 0x54, 0x59, 0x50, 0x45,
	0x5f, 0x45, 0x58, 0x41, 0x43, 0x54, 0x10, 0x01, 0x12, 0x15, 0x0a, 0x11, 0x4d, 0x41, 0x54, 0x43,
	0x48, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x50, 0x52, 0x45, 0x46, 0x49, 0x58, 0x10, 0x02, 0x12,
	0x12, 0x0a, 0x0e, 0x4d, 0x41, 0x54, 0x43, 0x48, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x41, 0x4c,
	0x4c, 0x10, 0x03, 0x22, 0x48, 0x0a, 0x07, 0x41, 0x70, 0x69, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x3d,
	0x0a, 0x08, 0x61, 0x70, 0x69, 0x5f, 0x72, 0x75, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x22, 0x2e, 0x67, 0x61, 0x2e, 0x67, 0x72, 0x70, 0x63, 0x5f, 0x74, 0x72, 0x61, 0x6e, 0x73,
	0x70, 0x6f, 0x72, 0x74, 0x5f, 0x63, 0x66, 0x67, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x4d,
	0x61, 0x74, 0x63, 0x68, 0x52, 0x07, 0x61, 0x70, 0x69, 0x52, 0x75, 0x6c, 0x65, 0x22, 0x49, 0x0a,
	0x08, 0x47, 0x7a, 0x69, 0x70, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x3d, 0x0a, 0x08, 0x61, 0x70, 0x69,
	0x5f, 0x72, 0x75, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x67, 0x61,
	0x2e, 0x67, 0x72, 0x70, 0x63, 0x5f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x70, 0x6f, 0x72, 0x74, 0x5f,
	0x63, 0x66, 0x67, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x52,
	0x07, 0x61, 0x70, 0x69, 0x52, 0x75, 0x6c, 0x65, 0x42, 0x4b, 0x0a, 0x18, 0x63, 0x6f, 0x6d, 0x2e,
	0x79, 0x69, 0x79, 0x6f, 0x75, 0x2e, 0x67, 0x61, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x5a, 0x2f, 0x67, 0x6f, 0x6c, 0x61, 0x6e, 0x67, 0x2e, 0x35, 0x32, 0x74,
	0x74, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x2f, 0x61,
	0x70, 0x70, 0x2f, 0x67, 0x72, 0x70, 0x63, 0x2d, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x70, 0x6f, 0x72,
	0x74, 0x2d, 0x63, 0x66, 0x67,
}

var (
	file_grpc_transport_cfg_transport_v2_proto_rawDescOnce sync.Once
	file_grpc_transport_cfg_transport_v2_proto_rawDescData = file_grpc_transport_cfg_transport_v2_proto_rawDesc
)

func file_grpc_transport_cfg_transport_v2_proto_rawDescGZIP() []byte {
	file_grpc_transport_cfg_transport_v2_proto_rawDescOnce.Do(func() {
		file_grpc_transport_cfg_transport_v2_proto_rawDescData = protoimpl.X.CompressGZIP(file_grpc_transport_cfg_transport_v2_proto_rawDescData)
	})
	return file_grpc_transport_cfg_transport_v2_proto_rawDescData
}

var file_grpc_transport_cfg_transport_v2_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_grpc_transport_cfg_transport_v2_proto_msgTypes = make([]protoimpl.MessageInfo, 6)
var file_grpc_transport_cfg_transport_v2_proto_goTypes = []interface{}{
	(EndpointV2_TLSConfig_TLSMode)(0), // 0: ga.grpc_transport_cfg.EndpointV2.TLSConfig.TLSMode
	(StringMatch_MatchType)(0),        // 1: ga.grpc_transport_cfg.StringMatch.MatchType
	(*EndpointV2)(nil),                // 2: ga.grpc_transport_cfg.EndpointV2
	(*TransportConfigV2)(nil),         // 3: ga.grpc_transport_cfg.TransportConfigV2
	(*StringMatch)(nil),               // 4: ga.grpc_transport_cfg.StringMatch
	(*ApiInfo)(nil),                   // 5: ga.grpc_transport_cfg.ApiInfo
	(*GzipInfo)(nil),                  // 6: ga.grpc_transport_cfg.GzipInfo
	(*EndpointV2_TLSConfig)(nil),      // 7: ga.grpc_transport_cfg.EndpointV2.TLSConfig
}
var file_grpc_transport_cfg_transport_v2_proto_depIdxs = []int32{
	7, // 0: ga.grpc_transport_cfg.EndpointV2.tls_config:type_name -> ga.grpc_transport_cfg.EndpointV2.TLSConfig
	2, // 1: ga.grpc_transport_cfg.TransportConfigV2.endpoints:type_name -> ga.grpc_transport_cfg.EndpointV2
	5, // 2: ga.grpc_transport_cfg.TransportConfigV2.black_list_api_info:type_name -> ga.grpc_transport_cfg.ApiInfo
	6, // 3: ga.grpc_transport_cfg.TransportConfigV2.gzip_black_list_api_info:type_name -> ga.grpc_transport_cfg.GzipInfo
	6, // 4: ga.grpc_transport_cfg.TransportConfigV2.gzip_white_list_api_info:type_name -> ga.grpc_transport_cfg.GzipInfo
	1, // 5: ga.grpc_transport_cfg.StringMatch.match_type:type_name -> ga.grpc_transport_cfg.StringMatch.MatchType
	4, // 6: ga.grpc_transport_cfg.ApiInfo.api_rule:type_name -> ga.grpc_transport_cfg.StringMatch
	4, // 7: ga.grpc_transport_cfg.GzipInfo.api_rule:type_name -> ga.grpc_transport_cfg.StringMatch
	0, // 8: ga.grpc_transport_cfg.EndpointV2.TLSConfig.mode:type_name -> ga.grpc_transport_cfg.EndpointV2.TLSConfig.TLSMode
	9, // [9:9] is the sub-list for method output_type
	9, // [9:9] is the sub-list for method input_type
	9, // [9:9] is the sub-list for extension type_name
	9, // [9:9] is the sub-list for extension extendee
	0, // [0:9] is the sub-list for field type_name
}

func init() { file_grpc_transport_cfg_transport_v2_proto_init() }
func file_grpc_transport_cfg_transport_v2_proto_init() {
	if File_grpc_transport_cfg_transport_v2_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_grpc_transport_cfg_transport_v2_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EndpointV2); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_grpc_transport_cfg_transport_v2_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TransportConfigV2); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_grpc_transport_cfg_transport_v2_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StringMatch); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_grpc_transport_cfg_transport_v2_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ApiInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_grpc_transport_cfg_transport_v2_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GzipInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_grpc_transport_cfg_transport_v2_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EndpointV2_TLSConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_grpc_transport_cfg_transport_v2_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   6,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_grpc_transport_cfg_transport_v2_proto_goTypes,
		DependencyIndexes: file_grpc_transport_cfg_transport_v2_proto_depIdxs,
		EnumInfos:         file_grpc_transport_cfg_transport_v2_proto_enumTypes,
		MessageInfos:      file_grpc_transport_cfg_transport_v2_proto_msgTypes,
	}.Build()
	File_grpc_transport_cfg_transport_v2_proto = out.File
	file_grpc_transport_cfg_transport_v2_proto_rawDesc = nil
	file_grpc_transport_cfg_transport_v2_proto_goTypes = nil
	file_grpc_transport_cfg_transport_v2_proto_depIdxs = nil
}
