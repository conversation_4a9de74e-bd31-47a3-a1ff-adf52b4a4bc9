// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.32.0
// 	protoc        v4.25.2
// source: transport/transport.proto

package transport

import (
	reflect "reflect"
	sync "sync"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type TLSConfig_TLSMode int32

const (
	TLSConfig_DISABLE TLSConfig_TLSMode = 0
	TLSConfig_SIMPLE  TLSConfig_TLSMode = 1
	TLSConfig_MUTUAL  TLSConfig_TLSMode = 2
)

// Enum value maps for TLSConfig_TLSMode.
var (
	TLSConfig_TLSMode_name = map[int32]string{
		0: "DISABLE",
		1: "SIMPLE",
		2: "MUTUAL",
	}
	TLSConfig_TLSMode_value = map[string]int32{
		"DISABLE": 0,
		"SIMPLE":  1,
		"MUTUAL":  2,
	}
)

func (x TLSConfig_TLSMode) Enum() *TLSConfig_TLSMode {
	p := new(TLSConfig_TLSMode)
	*p = x
	return p
}

func (x TLSConfig_TLSMode) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TLSConfig_TLSMode) Descriptor() protoreflect.EnumDescriptor {
	return file_transport_transport_proto_enumTypes[0].Descriptor()
}

func (TLSConfig_TLSMode) Type() protoreflect.EnumType {
	return &file_transport_transport_proto_enumTypes[0]
}

func (x TLSConfig_TLSMode) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *TLSConfig_TLSMode) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = TLSConfig_TLSMode(num)
	return nil
}

// Deprecated: Use TLSConfig_TLSMode.Descriptor instead.
func (TLSConfig_TLSMode) EnumDescriptor() ([]byte, []int) {
	return file_transport_transport_proto_rawDescGZIP(), []int{0, 0}
}

type TLSConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Mode      *TLSConfig_TLSMode `protobuf:"varint,1,opt,name=mode,enum=ga.transport.TLSConfig_TLSMode" json:"mode,omitempty"`
	Authority *string            `protobuf:"bytes,2,opt,name=authority" json:"authority,omitempty"` // reserved TransportType type;
}

func (x *TLSConfig) Reset() {
	*x = TLSConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_transport_transport_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TLSConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TLSConfig) ProtoMessage() {}

func (x *TLSConfig) ProtoReflect() protoreflect.Message {
	mi := &file_transport_transport_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TLSConfig.ProtoReflect.Descriptor instead.
func (*TLSConfig) Descriptor() ([]byte, []int) {
	return file_transport_transport_proto_rawDescGZIP(), []int{0}
}

func (x *TLSConfig) GetMode() TLSConfig_TLSMode {
	if x != nil && x.Mode != nil {
		return *x.Mode
	}
	return TLSConfig_DISABLE
}

func (x *TLSConfig) GetAuthority() string {
	if x != nil && x.Authority != nil {
		return *x.Authority
	}
	return ""
}

type Endpoint struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Address   *string    `protobuf:"bytes,1,opt,name=address" json:"address,omitempty"` //dev-apv2.ttyuyin.com:443
	TlsConfig *TLSConfig `protobuf:"bytes,2,opt,name=tls_config,json=tlsConfig" json:"tls_config,omitempty"`
}

func (x *Endpoint) Reset() {
	*x = Endpoint{}
	if protoimpl.UnsafeEnabled {
		mi := &file_transport_transport_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Endpoint) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Endpoint) ProtoMessage() {}

func (x *Endpoint) ProtoReflect() protoreflect.Message {
	mi := &file_transport_transport_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Endpoint.ProtoReflect.Descriptor instead.
func (*Endpoint) Descriptor() ([]byte, []int) {
	return file_transport_transport_proto_rawDescGZIP(), []int{1}
}

func (x *Endpoint) GetAddress() string {
	if x != nil && x.Address != nil {
		return *x.Address
	}
	return ""
}

func (x *Endpoint) GetTlsConfig() *TLSConfig {
	if x != nil {
		return x.TlsConfig
	}
	return nil
}

type TransportConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Endpoints     []*Endpoint `protobuf:"bytes,1,rep,name=endpoints" json:"endpoints,omitempty"`
	ExpireAt      *uint64     `protobuf:"varint,2,opt,name=expire_at,json=expireAt" json:"expire_at,omitempty"`
	MinAppVersion *string     `protobuf:"bytes,3,opt,name=min_app_version,json=minAppVersion" json:"min_app_version,omitempty"`
}

func (x *TransportConfig) Reset() {
	*x = TransportConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_transport_transport_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TransportConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TransportConfig) ProtoMessage() {}

func (x *TransportConfig) ProtoReflect() protoreflect.Message {
	mi := &file_transport_transport_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TransportConfig.ProtoReflect.Descriptor instead.
func (*TransportConfig) Descriptor() ([]byte, []int) {
	return file_transport_transport_proto_rawDescGZIP(), []int{2}
}

func (x *TransportConfig) GetEndpoints() []*Endpoint {
	if x != nil {
		return x.Endpoints
	}
	return nil
}

func (x *TransportConfig) GetExpireAt() uint64 {
	if x != nil && x.ExpireAt != nil {
		return *x.ExpireAt
	}
	return 0
}

func (x *TransportConfig) GetMinAppVersion() string {
	if x != nil && x.MinAppVersion != nil {
		return *x.MinAppVersion
	}
	return ""
}

var File_transport_transport_proto protoreflect.FileDescriptor

var file_transport_transport_proto_rawDesc = []byte{
	0x0a, 0x19, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x70, 0x6f, 0x72, 0x74, 0x2f, 0x74, 0x72, 0x61, 0x6e,
	0x73, 0x70, 0x6f, 0x72, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0c, 0x67, 0x61, 0x2e,
	0x74, 0x72, 0x61, 0x6e, 0x73, 0x70, 0x6f, 0x72, 0x74, 0x22, 0x8e, 0x01, 0x0a, 0x09, 0x54, 0x4c,
	0x53, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x33, 0x0a, 0x04, 0x6d, 0x6f, 0x64, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1f, 0x2e, 0x67, 0x61, 0x2e, 0x74, 0x72, 0x61, 0x6e, 0x73,
	0x70, 0x6f, 0x72, 0x74, 0x2e, 0x54, 0x4c, 0x53, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x54,
	0x4c, 0x53, 0x4d, 0x6f, 0x64, 0x65, 0x52, 0x04, 0x6d, 0x6f, 0x64, 0x65, 0x12, 0x1c, 0x0a, 0x09,
	0x61, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x69, 0x74, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x61, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x69, 0x74, 0x79, 0x22, 0x2e, 0x0a, 0x07, 0x54, 0x4c,
	0x53, 0x4d, 0x6f, 0x64, 0x65, 0x12, 0x0b, 0x0a, 0x07, 0x44, 0x49, 0x53, 0x41, 0x42, 0x4c, 0x45,
	0x10, 0x00, 0x12, 0x0a, 0x0a, 0x06, 0x53, 0x49, 0x4d, 0x50, 0x4c, 0x45, 0x10, 0x01, 0x12, 0x0a,
	0x0a, 0x06, 0x4d, 0x55, 0x54, 0x55, 0x41, 0x4c, 0x10, 0x02, 0x22, 0x5c, 0x0a, 0x08, 0x45, 0x6e,
	0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73,
	0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73,
	0x12, 0x36, 0x0a, 0x0a, 0x74, 0x6c, 0x73, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x67, 0x61, 0x2e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x70,
	0x6f, 0x72, 0x74, 0x2e, 0x54, 0x4c, 0x53, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x09, 0x74,
	0x6c, 0x73, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x22, 0x8c, 0x01, 0x0a, 0x0f, 0x54, 0x72, 0x61,
	0x6e, 0x73, 0x70, 0x6f, 0x72, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x34, 0x0a, 0x09,
	0x65, 0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x16, 0x2e, 0x67, 0x61, 0x2e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x70, 0x6f, 0x72, 0x74, 0x2e, 0x45,
	0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x52, 0x09, 0x65, 0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x73, 0x12, 0x1b, 0x0a, 0x09, 0x65, 0x78, 0x70, 0x69, 0x72, 0x65, 0x5f, 0x61, 0x74, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x08, 0x65, 0x78, 0x70, 0x69, 0x72, 0x65, 0x41, 0x74, 0x12,
	0x26, 0x0a, 0x0f, 0x6d, 0x69, 0x6e, 0x5f, 0x61, 0x70, 0x70, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69,
	0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x6d, 0x69, 0x6e, 0x41, 0x70, 0x70,
	0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x42, 0x42, 0x0a, 0x18, 0x63, 0x6f, 0x6d, 0x2e, 0x79,
	0x69, 0x79, 0x6f, 0x75, 0x2e, 0x67, 0x61, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x5a, 0x26, 0x67, 0x6f, 0x6c, 0x61, 0x6e, 0x67, 0x2e, 0x35, 0x32, 0x74, 0x74,
	0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x2f, 0x61, 0x70,
	0x70, 0x2f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x70, 0x6f, 0x72, 0x74,
}

var (
	file_transport_transport_proto_rawDescOnce sync.Once
	file_transport_transport_proto_rawDescData = file_transport_transport_proto_rawDesc
)

func file_transport_transport_proto_rawDescGZIP() []byte {
	file_transport_transport_proto_rawDescOnce.Do(func() {
		file_transport_transport_proto_rawDescData = protoimpl.X.CompressGZIP(file_transport_transport_proto_rawDescData)
	})
	return file_transport_transport_proto_rawDescData
}

var file_transport_transport_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_transport_transport_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_transport_transport_proto_goTypes = []interface{}{
	(TLSConfig_TLSMode)(0),  // 0: ga.transport.TLSConfig.TLSMode
	(*TLSConfig)(nil),       // 1: ga.transport.TLSConfig
	(*Endpoint)(nil),        // 2: ga.transport.Endpoint
	(*TransportConfig)(nil), // 3: ga.transport.TransportConfig
}
var file_transport_transport_proto_depIdxs = []int32{
	0, // 0: ga.transport.TLSConfig.mode:type_name -> ga.transport.TLSConfig.TLSMode
	1, // 1: ga.transport.Endpoint.tls_config:type_name -> ga.transport.TLSConfig
	2, // 2: ga.transport.TransportConfig.endpoints:type_name -> ga.transport.Endpoint
	3, // [3:3] is the sub-list for method output_type
	3, // [3:3] is the sub-list for method input_type
	3, // [3:3] is the sub-list for extension type_name
	3, // [3:3] is the sub-list for extension extendee
	0, // [0:3] is the sub-list for field type_name
}

func init() { file_transport_transport_proto_init() }
func file_transport_transport_proto_init() {
	if File_transport_transport_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_transport_transport_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TLSConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_transport_transport_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Endpoint); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_transport_transport_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TransportConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_transport_transport_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_transport_transport_proto_goTypes,
		DependencyIndexes: file_transport_transport_proto_depIdxs,
		EnumInfos:         file_transport_transport_proto_enumTypes,
		MessageInfos:      file_transport_transport_proto_msgTypes,
	}.Build()
	File_transport_transport_proto = out.File
	file_transport_transport_proto_rawDesc = nil
	file_transport_transport_proto_goTypes = nil
	file_transport_transport_proto_depIdxs = nil
}
