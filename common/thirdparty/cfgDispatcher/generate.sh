base_path=$(dirname $(realpath $0))
proto_path=${1:-/Users/<USER>/Workspace/TTProjects}
echo "Base Path: ${base_path}\nProto Path: ${proto_path}\n"

if [ ! -d ${base_path}/pb ]; then
  mkdir -p "${base_path}/pb"
fi

protoc \
--proto_path=${proto_path}/app \
--go_out=${base_path}/pb \
--go_opt=Mga_base.proto=gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/thirdparty/cfgDispatcher/pb/app \
--go_opt=Mgrpc_transport_cfg/grpc_transport_cfg.proto=gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/thirdparty/cfgDispatcher/pb/app/grpc_transport_cfg \
--go_opt=Mgrpc_transport_cfg/transport_v2.proto=gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/thirdparty/cfgDispatcher/pb/app/grpc_transport_cfg \
--go_opt=Mtransport/transport.proto=gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/thirdparty/cfgDispatcher/pb/app/transport \
--go_opt=module=gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/thirdparty/cfgDispatcher/pb \
${proto_path}/app/ga_base.proto \
${proto_path}/app/grpc_transport_cfg/*.proto \
${proto_path}/app/transport/transport.proto
echo "generate \`app\` code files"

protoc \
--proto_path=${proto_path}/app \
--proto_path=${proto_path}/quicksilver \
--go_out=${base_path}/pb \
--go-grpc_out=${base_path}/pb \
--go_opt=Mgrpc_transport_cfg/transport_v2.proto=gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/thirdparty/cfgDispatcher/pb/app/grpc_transport_cfg \
--go_opt=Mtransport/transport.proto=gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/thirdparty/cfgDispatcher/pb/app/transport \
--go_opt=Mtt/quicksilver/cfg-dispatcher/cfg-dispatcher.proto=gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/thirdparty/cfgDispatcher/pb/services/cfg-dispatcher \
--go-grpc_opt=Mgrpc_transport_cfg/transport_v2.proto=gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/thirdparty/cfgDispatcher/pb/app/grpc_transport_cfg \
--go-grpc_opt=Mtransport/transport.proto=gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/thirdparty/cfgDispatcher/pb/app/transport \
--go-grpc_opt=Mtt/quicksilver/cfg-dispatcher/cfg-dispatcher.proto=gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/thirdparty/cfgDispatcher/pb/services/cfg-dispatcher \
--go_opt=module=gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/thirdparty/cfgDispatcher/pb \
--go-grpc_opt=module=gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/thirdparty/cfgDispatcher/pb \
${proto_path}/quicksilver/tt/quicksilver/cfg-dispatcher/cfg-dispatcher.proto
echo "generate \`quicksilver\` code files"
