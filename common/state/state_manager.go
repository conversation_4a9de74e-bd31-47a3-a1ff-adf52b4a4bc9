package state

import (
	"errors"
	"fmt"
	"sync"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"

	dispatcherpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/rpc/pb"
)

// StateManager 节点的状态管理器，包括: State(状态)、Error(错误)、Message(提示信息)
// 颗粒度: State > ErrCode > Debug
type StateManager struct {
	lock    sync.RWMutex
	state   dispatcherpb.ComponentState
	errcode IErrCode
	debug   string
}

func NewStateManager(state dispatcherpb.ComponentState, errcode IErrCode) *StateManager {
	mgr := &StateManager{
		state:   state,
		errcode: errcode,
	}
	return mgr
}

func (mgr *StateManager) SetDebugf(msg string, args ...any) {
	mgr.lock.Lock()
	defer mgr.lock.Unlock()
	mgr.debug = fmt.Sprintf(msg, args...)
}

func (mgr *StateManager) UpdateStateManager(next dispatcherpb.ComponentState, errcode IErrCode) bool {
	mgr.lock.Lock()
	defer mgr.lock.Unlock()

	now := mgr.state
	if now > next {
		return false
	}

	mgr.state = next
	mgr.errcode = errcode
	return true
}

func (mgr *StateManager) State() dispatcherpb.ComponentState {
	mgr.lock.RLock()
	defer mgr.lock.RUnlock()
	return mgr.state
}

func (mgr *StateManager) Code() errorx.Code {
	mgr.lock.RLock()
	defer mgr.lock.RUnlock()
	return mgr.errcode.Code()
}

func (mgr *StateManager) Message() string {
	mgr.lock.RLock()
	defer mgr.lock.RUnlock()
	return mgr.errcode.String()
}

func (mgr *StateManager) Debug() string {
	mgr.lock.RLock()
	defer mgr.lock.RUnlock()
	return mgr.debug
}

func Error2Debug(err error) string {
	debug := ""
	if err != nil {
		debug = err.Error()
	}
	return debug
}

func Debug2Error(debug string) error {
	var err error
	if debug != "" {
		err = errors.New(debug)
	}
	return err
}
