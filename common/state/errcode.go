package state

import (
	"fmt"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
)

type IErrCode interface {
	String() string
	Code() errorx.Code
	Debug() string
}

type ErrCode struct {
	code  errorx.Code
	msg   string
	debug string
}

func GetErrCode(code errorx.Code, msg, format string, args ...any) ErrCode {
	return ErrCode{
		code:  code,
		msg:   msg,
		debug: fmt.Sprintf(format, args...),
	}
}

func (e ErrCode) String() string {
	return e.msg
}

func (e ErrCode) Code() errorx.Code {
	return e.code
}

func (e ErrCode) Debug() string {
	return e.debug
}
