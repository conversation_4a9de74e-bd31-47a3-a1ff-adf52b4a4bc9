package types

import "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"

type Function struct {
	Func       any
	Name       string
	Desc       string
	Parameters []Parameter
	Returns    []Return
	Example    string
	Category   constants.FunctionCategory
}

type Parameter struct {
	Name     string                  `json:"name"`
	Desc     string                  `json:"description"`
	Type     constants.ParameterType `json:"type"`
	Default  string                  `json:"default,omitempty,optional"`
	Variadic bool                    `json:"variadic"` // 是否为可变列表的参数
}

type Return struct {
	Name string                  `json:"name"`
	Desc string                  `json:"description"`
	Type constants.ParameterType `json:"type"`
}
