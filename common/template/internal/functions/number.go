package functions

import (
	"crypto/rand"
	"math"
	"math/big"
)

// Max returns the largest of a series of integers
func Max(a any, i ...any) int64 {
	aa := ToInt64(a)
	for _, b := range i {
		bb := ToInt64(b)
		if bb > aa {
			aa = bb
		}
	}
	return aa
}

// Min returns the smallest of a series of integers
func Min(a any, i ...any) int64 {
	aa := ToInt64(a)
	for _, b := range i {
		bb := ToInt64(b)
		if bb < aa {
			aa = bb
		}
	}
	return aa
}

func Until(count int) []int {
	step := 1
	if count < 0 {
		step = -1
	}
	return UntilStep(0, count, step)
}

func UntilStep(start, stop, step int) []int {
	var v []int

	if stop < start {
		if step >= 0 {
			return v
		}
		for i := start; i > stop; i += step {
			v = append(v, i)
		}
		return v
	}

	if step <= 0 {
		return v
	}
	for i := start; i < stop; i += step {
		v = append(v, i)
	}
	return v
}

// Floor returns the greatest integer value less than or equal to a
func Floor(a any) float64 {
	aa := ToFloat64(a)
	return math.Floor(aa)
}

// Ceil returns the least integer value greater than or equal to a
func Ceil(a any) float64 {
	aa := ToFloat64(a)
	return math.Ceil(aa)
}

// Round returns the nearest integer, rounding the specify option (default: half away from zero)
func Round(a any, p int, rOpt ...float64) float64 {
	roundOn := .5
	if len(rOpt) > 0 {
		roundOn = rOpt[0]
	}
	val := ToFloat64(a)
	places := ToFloat64(p)

	var r float64
	pow := math.Pow(10, places)
	digit := pow * val
	_, div := math.Modf(digit)
	if div >= roundOn {
		r = math.Ceil(digit)
	} else {
		r = math.Floor(digit)
	}
	return r / pow
}

// Choice returns a pseudo-random integer number in [start, end)
func Choice(start, end int) int {
	gap := big.NewInt(int64(end - start))
	if i, err := rand.Int(rand.Reader, gap); err != nil {
		return start
	} else {
		return int(i.Int64() + int64(start))
	}
}
