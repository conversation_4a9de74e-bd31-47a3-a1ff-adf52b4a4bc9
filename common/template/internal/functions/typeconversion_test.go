package functions

import "testing"

func TestToString(t *testing.T) {
	type args struct {
		i interface{}
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		{
			name: "int64 to string",
			args: args{
				i: int64(2490770),
			},
			want: "2490770",
		},
		{
			name: "float64 to string",
			args: args{
				i: float64(2490770),
			},
			want: "2490770",
		},
		{
			name: "float64 with precision to string",
			args: args{
				i: float64(2490770.123450),
			},
			want: "2490770.12345",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := ToString(tt.args.i)
			t.Logf("ToString(%v) => %s", tt.args.i, got)
			if got != tt.want {
				t.<PERSON>("ToString() = %v, want %v", got, tt.want)
			}
		})
	}
}
