package functions

import (
	"crypto/rand"
	_ "embed"
	"math/big"

	"github.com/zeromicro/go-zero/core/jsonx"
)

var (
	//go:embed corpus_zh.json
	corpusZHData string
	corpusZH     Corpus
)

type Corpus struct {
	Sentences []string `json:"sentences"`
	Phrases   []string `json:"phrases"`
	Nouns     []string `json:"nouns"`
}

func init() {
	_ = jsonx.UnmarshalFromString(corpusZHData, &corpusZH)
}

func SentenceZH() string {
	return randChoose(corpusZH.Sentences)
}

func PhraseZH() string {
	return randChoose(corpusZH.Phrases)
}

func NounZH() string {
	return randChoose(corpusZH.Nouns)
}

func randChoose(samples []string) string {
	count := len(samples)
	if count == 0 {
		return ""
	}

	if i, err := rand.Int(rand.Reader, big.NewInt(int64(count))); err != nil {
		return samples[0]
	} else {
		return samples[i.Int64()]
	}
}
