package functions

import (
	"fmt"
	"math"
	"reflect"
	"regexp"
	"strconv"

	"github.com/zeromicro/go-zero/core/jsonx"
)

var toTextRegexp = regexp.MustCompile(`{{toJson \s*\.([^}]*)\s*}}`)

// ToFloat64 converts 64-bit floats
func ToFloat64(i any) (iv float64) {
	var err error

	val := reflect.Indirect(reflect.ValueOf(i))
	switch val.Kind() {
	case reflect.String: // include: json.Number, string
		iv, err = strconv.ParseFloat(val.String(), 64)
	case reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64, reflect.Int:
		iv = float64(val.Int())
	case reflect.Uint8, reflect.Uint16, reflect.Uint32, reflect.Uint64, reflect.Uint:
		iv = float64(val.Uint())
	case reflect.Float32, reflect.Float64:
		iv = val.Float()
	case reflect.Bool:
		if val.Bool() {
			iv = 1
		}
	default:
		iv = 0
	}

	if err != nil {
		iv = 0
	}

	return iv
}

// ToInt converts to integer
func ToInt(i any) int {
	// It's not optimal. But I don't want duplicate toInt64 code.
	return int(ToInt64(i))
}

// ToInt64 converts integer types to 64-bit integers
func ToInt64(i any) (iv int64) {
	var err error

	val := reflect.Indirect(reflect.ValueOf(i))
	switch val.Kind() {
	case reflect.String: // include: json.Number, string
		iv, err = strconv.ParseInt(val.String(), 10, 64)
	case reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64, reflect.Int:
		iv = val.Int()
	case reflect.Uint8, reflect.Uint16, reflect.Uint32, reflect.Uint64, reflect.Uint:
		tv := val.Uint()
		if tv <= math.MaxInt64 {
			iv = int64(tv)
		} else {
			iv = math.MaxInt64
		}
	case reflect.Float32, reflect.Float64:
		iv = int64(val.Float())
	case reflect.Bool:
		if val.Bool() {
			iv = 1
		}
	default:
		iv = 0
	}

	if err != nil {
		iv = 0
	}

	return iv
}

// ToDecimal converts unix octal to decimal
func ToDecimal(i any) int64 {
	result, err := strconv.ParseInt(fmt.Sprint(i), 8, 64)
	if err != nil {
		return 0
	}
	return result
}

// ToString converts to a string
func ToString(i any) string {
	switch v := i.(type) {
	case string:
		return v
	case []byte:
		return string(v)
	case error:
		return v.Error()
	case fmt.Stringer:
		return v.String()
	case float32:
		return strconv.FormatFloat(float64(v), 'f', -1, 32)
	case float64:
		return strconv.FormatFloat(v, 'f', -1, 64)
	default:
		return fmt.Sprintf("%v", v)
	}
}

// ToJson encodes an item into a JSON string
func ToJson(i any) string {
	return jsonx.MarshalToStringIgnoreError(i)
}

// ToPrettyJson encodes an item into a pretty (indented) JSON string
func ToPrettyJson(i any) string {
	return jsonx.MarshalToIndentStringIgnoreError(i, "", "  ")
}

func ToText(i string) string {
	matches := toTextRegexp.FindStringSubmatch(i)
	if len(matches) >= 2 {
		v := matches[1]
		return fmt.Sprintf("{{.%s}}", v)
	}

	return i
}

// FromJson decodes a JSON string into a JSON object, ignoring errors
func FromJson(i string) any {
	var o any
	_ = jsonx.UnmarshalFromString(i, &o)
	return o
}
