package functions

import (
	"crypto/rand"
	"math/big"
	"unicode"
)

const (
	startOfZHUnicode = 0x4e00
	endOfZHUnicode   = 0x9fa5
	gapOfZHUnicode   = endOfZHUnicode - startOfZHUnicode + 1
)

func RandZH(count int) string {
	result := make([]rune, count)
	for i := 0; i < count; i++ {
		var r rune
		for !unicode.Is(unicode.Han, r) {
			n, err := rand.Int(rand.Reader, big.NewInt(gapOfZHUnicode))
			if err != nil {
				r = rune(startOfZHUnicode + i)
				break
			} else {
				r = rune(startOfZHUnicode + n.Int64())
			}
		}

		result[i] = r
	}

	return string(result)
}
