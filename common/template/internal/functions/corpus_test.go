package functions

import "testing"

func TestSentence(t *testing.T) {
	tests := []struct {
		name  string
		typ   string
		times int
	}{
		{
			name:  "句子",
			typ:   "sentence",
			times: 5,
		},
		{
			name:  "短语",
			typ:   "phrase",
			times: 8,
		},
		{
			name:  "名词",
			typ:   "noun",
			times: 12,
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				var fn func() string
				switch tt.typ {
				case "sentence":
					fn = SentenceZH
				case "phrase":
					fn = PhraseZH
				case "noun":
					fn = NounZH
				default:
					t.Fatalf("unknown type: %s", tt.typ)
				}

				for i := 0; i < tt.times; i++ {
					t.Logf("%s_%d: %s", tt.typ, i, fn())
				}
			},
		)
	}
}
