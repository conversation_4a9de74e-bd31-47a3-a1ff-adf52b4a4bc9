package docs

import (
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/template/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/utils"
)

var listDocs = []types.Function{
	{
		Name: "list",
		Desc: "Returns a list",
		Parameters: []types.Parameter{
			{
				Name:     "e",
				Desc:     "生成列表的元素",
				Type:     constants.ANY,
				Variadic: true,
			},
		},
		Returns: []types.Return{
			{
				Name: "output",
				Desc: "生成的列表",
				Type: constants.ARRAY,
			},
		},
		Example:  "list(1, 2, 3, 4, 5) => [1, 2, 3, 4, 5]",
		Category: constants.List,
	},
	{
		Name: "first",
		Desc: "To get the head item of a list",
		Parameters: []types.Parameter{
			{
				Name: "l",
				Desc: "列表",
				Type: constants.ARRAY,
			},
		},
		Returns: []types.Return{
			{
				Name: "output",
				Desc: "列表的第一个元素",
				Type: constants.ANY,
			},
		},
		Example:  "first([1, 2, 3, 4, 5]) => 1",
		Category: constants.List,
	},
	{
		Name: "last",
		Desc: "To get the last item of a list",
		Parameters: []types.Parameter{
			{
				Name: "l",
				Desc: "列表",
				Type: constants.ARRAY,
			},
		},
		Returns: []types.Return{
			{
				Name: "output",
				Desc: "列表的最后一个元素",
				Type: constants.ANY,
			},
		},
		Example:  "last([1, 2, 3, 4, 5]) => 5",
		Category: constants.List,
	},
	{
		Name: "append",
		Desc: "Append a new item to a list, creating a new list",
		Parameters: []types.Parameter{
			{
				Name: "l",
				Desc: "列表",
				Type: constants.ARRAY,
			},
			{
				Name: "e",
				Desc: "待插入到列表尾部的元素",
				Type: constants.ANY,
			},
		},
		Returns: []types.Return{
			{
				Name: "output",
				Desc: "尾部添加元素后的新列表",
				Type: constants.ARRAY,
			},
		},
		Example:  "append([1, 2, 3, 4, 5], 6) => [1, 2, 3, 4, 5, 6]",
		Category: constants.List,
	},
	{
		Name: "prepend",
		Desc: "Push a new item onto the front of a list, creating a new list",
		Parameters: []types.Parameter{
			{
				Name: "l",
				Desc: "列表",
				Type: constants.ARRAY,
			},
			{
				Name: "e",
				Desc: "待插入到列表头部的元素",
				Type: constants.ANY,
			},
		},
		Returns: []types.Return{
			{
				Name: "output",
				Desc: "头部添加元素后的新列表",
				Type: constants.ARRAY,
			},
		},
		Example:  "prepend([1, 2, 3, 4, 5], 0) => [0, 1, 2, 3, 4, 5]",
		Category: constants.List,
	},
	{
		Name: "concat",
		Desc: "Concatenate arbitrary number of lists into one",
		Parameters: []types.Parameter{
			{
				Name:     "l",
				Desc:     "列表",
				Type:     constants.ARRAY,
				Variadic: true,
			},
			{
				Name: "e",
				Desc: "待插入到列表头部的元素",
				Type: constants.ANY,
			},
		},
		Returns: []types.Return{
			{
				Name: "output",
				Desc: "连接后的列表",
				Type: constants.ARRAY,
			},
		},
		Example:  "concat([1, 2, 3, 4, 5], [6, 7]) => [1, 2, 3, 4, 5, 6, 7]",
		Category: constants.List,
	},
	{
		Name: "reverse",
		Desc: "Produce a new list with the reversed elements of the given list",
		Parameters: []types.Parameter{
			{
				Name: "l",
				Desc: "列表",
				Type: constants.ARRAY,
			},
		},
		Returns: []types.Return{
			{
				Name: "output",
				Desc: "反转后的新列表",
				Type: constants.ARRAY,
			},
		},
		Example:  "reverse([1, 2, 3, 4, 5]) => [5, 4, 3, 2, 1]",
		Category: constants.List,
	},
	{
		Name: "uniq",
		Desc: "Generate a list with all of the duplicates removed",
		Parameters: []types.Parameter{
			{
				Name: "l",
				Desc: "列表",
				Type: constants.ARRAY,
			},
		},
		Returns: []types.Return{
			{
				Name: "output",
				Desc: "去重后的新列表",
				Type: constants.ARRAY,
			},
		},
		Example:  "uniq([1, 2, 3, 3, 4, 5]) => [1, 2, 3, 4, 5]",
		Category: constants.List,
	},
	{
		Name: "without",
		Desc: "Generate a list with all of the specify items removed",
		Parameters: []types.Parameter{
			{
				Name: "l",
				Desc: "列表",
				Type: constants.ARRAY,
			},
			{
				Name:     "e",
				Desc:     "过滤的元素",
				Type:     constants.ANY,
				Variadic: true,
			},
		},
		Returns: []types.Return{
			{
				Name: "output",
				Desc: "过滤后的新列表",
				Type: constants.ARRAY,
			},
		},
		Example:  "without([1, 2, 3, 4, 5], 3) => [1, 2, 4, 5]",
		Category: constants.List,
	},
	{
		Name: "has",
		Desc: "Whether a specify item in a list",
		Parameters: []types.Parameter{
			{
				Name:     "e",
				Desc:     "待查询的元素",
				Type:     constants.ANY,
				Variadic: true,
			},
			{
				Name: "l",
				Desc: "列表",
				Type: constants.ARRAY,
			},
		},
		Returns: []types.Return{
			{
				Name: "output",
				Desc: "指定的元素是否存在于列表中",
				Type: constants.BOOLEAN,
			},
		},
		Example:  "has(3, [1, 2, 3, 4, 5]) => true",
		Category: constants.List,
	},
	{
		Name: "slice",
		Desc: "To get partial items of a list",
		Parameters: []types.Parameter{
			{
				Name: "l",
				Desc: "列表",
				Type: constants.ARRAY,
			},
			{
				Name:     "i",
				Desc:     "切片下标，不设置下标则表示取整个列表，设置1个下标表示[x:]，设置2个下标表示[x:y]",
				Type:     constants.NUMBER,
				Variadic: true,
			},
		},
		Returns: []types.Return{
			{
				Name: "output",
				Desc: "根据下标切割后的新列表",
				Type: constants.ARRAY,
			},
		},
		Example:  "slice([1, 2, 3, 4, 5], 1, 3) => [2, 3]",
		Category: constants.List,
	},
	{
		Name: "chunk",
		Desc: "To split a list into chunks of given size",
		Parameters: []types.Parameter{
			{
				Name: "size",
				Desc: "块的大小",
				Type: constants.NUMBER,
			},
			{
				Name: "l",
				Desc: "列表",
				Type: constants.ARRAY,
			},
		},
		Returns: []types.Return{
			{
				Name: "output",
				Desc: "拆分后的新列表",
				Type: constants.ARRAY,
			},
		},
		Example:  "chunk(3, [1, 2, 3, 4, 5]) => [[1, 2, 3], [4, 5]]",
		Category: constants.List,
	},
	{
		Func: utils.RandomChoose,
		Name: "choose",
		Desc: "To get a random item of a list",
		Parameters: []types.Parameter{
			{
				Name: "l",
				Desc: "列表",
				Type: constants.ARRAY,
			},
		},
		Returns: []types.Return{
			{
				Name: "output",
				Desc: "列表中的任意一个元素",
				Type: constants.ANY,
			},
		},
		Example:  `choose(["allen", "bob", "charles"]) => "charles" (maybe)`,
		Category: constants.List,
	},
}
