package docs

import (
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/template/internal/functions"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/template/internal/types"
)

var typeConversionDocs = []types.Function{
	{
		Name: "atoi",
		Desc: "Convert a string to an integer",
		Parameters: []types.Parameter{
			{
				Name: "s",
				Desc: "待类型转换的字符串",
				Type: constants.STRING,
			},
		},
		Returns: []types.Return{
			{
				Name: "output",
				Desc: "类型转换后的整数",
				Type: constants.NUMBER,
			},
		},
		Example:  `atoi("123") => 123`,
		Category: constants.TypeConversion,
	},
	{
		Func: functions.ToInt,
		Name: "int",
		Desc: "Convert to an int at the system's width",
		Parameters: []types.Parameter{
			{
				Name: "v",
				Desc: "待类型转换的值",
				Type: constants.ANY,
			},
		},
		Returns: []types.Return{
			{
				Name: "output",
				Desc: "类型转换后的数字（int类型）",
				Type: constants.NUMBER,
			},
		},
		Example:  `int("123") => 123`,
		Category: constants.TypeConversion,
	},
	{
		Func: functions.ToInt64,
		Name: "int64",
		Desc: "Convert to an int64",
		Parameters: []types.Parameter{
			{
				Name: "v",
				Desc: "待类型转换的值",
				Type: constants.ANY,
			},
		},
		Returns: []types.Return{
			{
				Name: "output",
				Desc: "类型转换后的数字（int64类型）",
				Type: constants.NUMBER,
			},
		},
		Example:  `int64("123") => 123`,
		Category: constants.TypeConversion,
	},
	{
		Func: functions.ToFloat64,
		Name: "float64",
		Desc: "Convert to a float64",
		Parameters: []types.Parameter{
			{
				Name: "v",
				Desc: "待类型转换的值",
				Type: constants.ANY,
			},
		},
		Returns: []types.Return{
			{
				Name: "output",
				Desc: "类型转换后的数字（float64类型）",
				Type: constants.NUMBER,
			},
		},
		Example:  `float64("123") => 123`,
		Category: constants.TypeConversion,
	},
	{
		Func: functions.ToDecimal,
		Name: "decimal",
		Desc: "Converts unix octal to decimal",
		Parameters: []types.Parameter{
			{
				Name: "v",
				Desc: "待类型转换的值（八进制数）",
				Type: constants.ANY,
			},
		},
		Returns: []types.Return{
			{
				Name: "output",
				Desc: "类型转换后的数字（int64类型）",
				Type: constants.NUMBER,
			},
		},
		Example:  "decimal(10) => 8",
		Category: constants.TypeConversion,
	},
	{
		Func: functions.ToString,
		Name: "str",
		Desc: "Convert to a string",
		Parameters: []types.Parameter{
			{
				Name: "v",
				Desc: "待类型转换的值",
				Type: constants.ANY,
			},
		},
		Returns: []types.Return{
			{
				Name: "output",
				Desc: "类型转换后的字符串",
				Type: constants.NUMBER,
			},
		},
		Example:  `str(123) => "123"`,
		Category: constants.TypeConversion,
	},
	{
		Func: functions.ToJson,
		Name: "toJson",
		Desc: "Encodes an item into a JSON string",
		Parameters: []types.Parameter{
			{
				Name: "item",
				Desc: "待JSON序列化的对象",
				Type: constants.ANY,
			},
		},
		Returns: []types.Return{
			{
				Name: "output",
				Desc: "序列化后的JSON字符串",
				Type: constants.STRING,
			},
		},
		Example:  `toJson({"name": "allen", "age": 18}) => "{\"name\": \"allen\", \"age\": 18}"`,
		Category: constants.TypeConversion,
	},
	{
		Func: functions.ToPrettyJson,
		Name: "toPrettyJson",
		Desc: "Encodes an item into a pretty (indented) JSON string",
		Parameters: []types.Parameter{
			{
				Name: "item",
				Desc: "待JSON序列化的对象",
				Type: constants.ANY,
			},
		},
		Returns: []types.Return{
			{
				Name: "output",
				Desc: "序列化并格式化后的JSON字符串",
				Type: constants.STRING,
			},
		},
		Example: `toPrettyJson({"name": "allen", "age": 18}) => "{
  \"name\": \"allen\", 
  \"age\": 18
}"`,
		Category: constants.TypeConversion,
	},
	{
		Func: functions.ToText,
		Name: "toText",
		Desc: "Encodes an item into a text string",
		Parameters: []types.Parameter{
			{
				Name: "item",
				Desc: "待原始化的JSON字符串",
				Type: constants.String,
			},
		},
		Returns: []types.Return{
			{
				Name: "output",
				Desc: "序列化并格式化后的JSON字符串",
				Type: constants.STRING,
			},
		},
		Example:  `toText("{{toJson .value}}") => "{{.value}}"`,
		Category: constants.TypeConversion,
	},
	{
		Func: functions.FromJson,
		Name: "fromJson",
		Desc: "Decodes a JSON string into a JSON object",
		Parameters: []types.Parameter{
			{
				Name: "item",
				Desc: "待JSON反序列化的字符串",
				Type: constants.STRING,
			},
		},
		Returns: []types.Return{
			{
				Name: "output",
				Desc: "反序列化后的JSON对象",
				Type: constants.ANY,
			},
		},
		Example:  `fromJson("{\"name\": \"allen\", \"age\": 18}") => {"name": "allen", "age": 18}`,
		Category: constants.TypeConversion,
	},
}
