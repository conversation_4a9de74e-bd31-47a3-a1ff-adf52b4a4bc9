package docs

import (
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/template/internal/functions"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/template/internal/types"
)

var stringDocs = []types.Function{
	{
		Name: "trim",
		Desc: "Removes space from either side of a string",
		Parameters: []types.Parameter{
			{
				Name: "s",
				Desc: "待处理的字符串",
				Type: constants.STRING,
			},
		},
		Returns: []types.Return{
			{
				Name: "output",
				Desc: "处理后的字符串",
				Type: constants.STRING,
			},
		},
		Example:  `trim("   hello    ") => hello`,
		Category: constants.String,
	},
	{
		Name: "trimAll",
		Desc: "Remove given characters from the front or back of a string",
		Parameters: []types.Parameter{
			{
				Name: "char",
				Desc: "指定去除的字符",
				Type: constants.STRING,
			},
			{
				Name: "s",
				Desc: "待处理的字符串",
				Type: constants.STRING,
			},
		},
		Returns: []types.Return{
			{
				Name: "output",
				Desc: "处理后的字符串",
				Type: constants.STRING,
			},
		},
		Example:  `trimAll("$", "$5.00") => 5.00 (as a string)`,
		Category: constants.String,
	},
	{
		Name: "trimSuffix",
		Desc: "Trim just the suffix from a string",
		Parameters: []types.Parameter{
			{
				Name: "char",
				Desc: "指定去除的字符",
				Type: constants.STRING,
			},
			{
				Name: "s",
				Desc: "待处理的字符串",
				Type: constants.STRING,
			},
		},
		Returns: []types.Return{
			{
				Name: "output",
				Desc: "处理后的字符串",
				Type: constants.STRING,
			},
		},
		Example:  "trimSuffix(\"-\", \"hello-\") => hello",
		Category: constants.String,
	},
	{
		Name: "trimPrefix",
		Desc: "Trim just the prefix from a string",
		Parameters: []types.Parameter{
			{
				Name: "char",
				Desc: "指定去除的字符",
				Type: constants.STRING,
			},
			{
				Name: "s",
				Desc: "待处理的字符串",
				Type: constants.STRING,
			},
		},
		Returns: []types.Return{
			{
				Name: "output",
				Desc: "处理后的字符串",
				Type: constants.STRING,
			},
		},
		Example:  `trimPrefix("-", "-hello") => hello`,
		Category: constants.String,
	},
	{
		Name: "upper",
		Desc: "Convert the entire string to uppercase",
		Parameters: []types.Parameter{
			{
				Name: "s",
				Desc: "待处理的字符串",
				Type: constants.STRING,
			},
		},
		Returns: []types.Return{
			{
				Name: "output",
				Desc: "处理后的字符串",
				Type: constants.STRING,
			},
		},
		Example:  `upper("hello") => HELLO`,
		Category: constants.String,
	},
	{
		Name: "lower",
		Desc: "Convert the entire string to lowercase",
		Parameters: []types.Parameter{
			{
				Name: "s",
				Desc: "待处理的字符串",
				Type: constants.STRING,
			},
		},
		Returns: []types.Return{
			{
				Name: "output",
				Desc: "处理后的字符串",
				Type: constants.STRING,
			},
		},
		Example:  "lower(\"HELLO\") => hello",
		Category: constants.String,
	},
	{
		Name: "title",
		Desc: "Convert to title case",
		Parameters: []types.Parameter{
			{
				Name: "s",
				Desc: "待处理的字符串",
				Type: constants.STRING,
			},
		},
		Returns: []types.Return{
			{
				Name: "output",
				Desc: "处理后的字符串",
				Type: constants.STRING,
			},
		},
		Example:  "title(\"hello world\") => Hello World",
		Category: constants.String,
	},
	{
		Name: "untitle",
		Desc: "Remove title casing",
		Parameters: []types.Parameter{
			{
				Name: "s",
				Desc: "待处理的字符串",
				Type: constants.STRING,
			},
		},
		Returns: []types.Return{
			{
				Name: "output",
				Desc: "处理后的字符串",
				Type: constants.STRING,
			},
		},
		Example:  `untitle("Hello World") => hello world`,
		Category: constants.String,
	},
	{
		Name: "repeat",
		Desc: "Repeat a string multiple times",
		Parameters: []types.Parameter{
			{
				Name: "times",
				Desc: "重复次数",
				Type: constants.NUMBER,
			},
			{
				Name: "s",
				Desc: "待处理的字符串",
				Type: constants.STRING,
			},
		},
		Returns: []types.Return{
			{
				Name: "output",
				Desc: "处理后的字符串",
				Type: constants.STRING,
			},
		},
		Example:  `repeat(3, "hello") => hellohellohello`,
		Category: constants.String,
	},
	{
		Name: "substr",
		Desc: "Get a substring from a string",
		Parameters: []types.Parameter{
			{
				Name: "start",
				Desc: "起始位置",
				Type: constants.NUMBER,
			},
			{
				Name: "end",
				Desc: "结束位置",
				Type: constants.NUMBER,
			},
			{
				Name: "s",
				Desc: "待处理的字符串",
				Type: constants.STRING,
			},
		},
		Returns: []types.Return{
			{
				Name: "output",
				Desc: "处理后的字符串",
				Type: constants.STRING,
			},
		},
		Example:  `substr(0, 5, "hello world") => hello`,
		Category: constants.String,
	},
	{
		Name: "nospace",
		Desc: "Remove all whitespace from a string",
		Parameters: []types.Parameter{
			{
				Name: "s",
				Desc: "待处理的字符串",
				Type: constants.STRING,
			},
		},
		Returns: []types.Return{
			{
				Name: "output",
				Desc: "处理后的字符串",
				Type: constants.STRING,
			},
		},
		Example:  `nospace("hello w o r l d") => helloworld`,
		Category: constants.String,
	},
	{
		Name: "trunc",
		Desc: "Truncate a string (and add no suffix)",
		Parameters: []types.Parameter{
			{
				Name: "end",
				Desc: "结束位置",
				Type: constants.NUMBER,
			},
			{
				Name: "s",
				Desc: "待处理的字符串",
				Type: constants.STRING,
			},
		},
		Returns: []types.Return{
			{
				Name: "output",
				Desc: "处理后的字符串",
				Type: constants.STRING,
			},
		},
		Example:  `trunc(5, "hello world") => hello`,
		Category: constants.String,
	},
	{
		Name: "abbrev",
		Desc: "Truncate a string with ellipses (...)",
		Parameters: []types.Parameter{
			{
				Name: "max",
				Desc: "最大的长度",
				Type: constants.NUMBER,
			},
			{
				Name: "s",
				Desc: "待处理的字符串",
				Type: constants.STRING,
			},
		},
		Returns: []types.Return{
			{
				Name: "output",
				Desc: "处理后的字符串",
				Type: constants.STRING,
			},
		},
		Example:  `abbrev(5, "hello world") => he...`,
		Category: constants.String,
	},
	{
		Name: "abbrevboth",
		Desc: "Abbreviate both sides",
		Parameters: []types.Parameter{
			{
				Name: "offset",
				Desc: "左侧偏移量",
				Type: constants.NUMBER,
			},
			{
				Name: "max",
				Desc: "最大的长度",
				Type: constants.NUMBER,
			},
			{
				Name: "s",
				Desc: "待处理的字符串",
				Type: constants.STRING,
			},
		},
		Returns: []types.Return{
			{
				Name: "output",
				Desc: "处理后的字符串",
				Type: constants.STRING,
			},
		},
		Example:  `abbrevboth(5, 10, "1234 5678 9123") => ...5678...`,
		Category: constants.String,
	},
	{
		Name: "initials",
		Desc: "Given multiple words, take the first letter of each word and combine",
		Parameters: []types.Parameter{
			{
				Name: "s",
				Desc: "待处理的字符串",
				Type: constants.STRING,
			},
		},
		Returns: []types.Return{
			{
				Name: "output",
				Desc: "处理后的字符串",
				Type: constants.STRING,
			},
		},
		Example:  `initials("Hello World") => HW`,
		Category: constants.String,
	},
	{
		Name: "randAlphaNum",
		Desc: "Generate cryptographically secure random strings with [0-9a-zA-Z]",
		Parameters: []types.Parameter{
			{
				Name: "length",
				Desc: "随机字符串的长度",
				Type: constants.NUMBER,
			},
		},
		Returns: []types.Return{
			{
				Name: "output",
				Desc: "生成的随机字符串",
				Type: constants.STRING,
			},
		},
		Example:  "randAlphaNum(3) => try",
		Category: constants.String,
	},
	{
		Name: "randAlpha",
		Desc: "Generate cryptographically secure random strings with [a-zA-Z]",
		Parameters: []types.Parameter{
			{
				Name: "length",
				Desc: "随机字符串的长度",
				Type: constants.NUMBER,
			},
		},
		Returns: []types.Return{
			{
				Name: "output",
				Desc: "生成的随机字符串",
				Type: constants.STRING,
			},
		},
		Example:  `randAlpha(3) => xoG`,
		Category: constants.String,
	},
	{
		Name: "randNumeric",
		Desc: "Generate cryptographically secure random strings with [0-9]",
		Parameters: []types.Parameter{
			{
				Name: "length",
				Desc: "随机字符串的长度",
				Type: constants.NUMBER,
			},
		},
		Returns: []types.Return{
			{
				Name: "output",
				Desc: "生成的随机字符串",
				Type: constants.STRING,
			},
		},
		Example:  `randNumeric(3) => 248`,
		Category: constants.String,
	},
	{
		Name: "randAscii",
		Desc: "Generate cryptographically secure random strings with all printable ASCII characters",
		Parameters: []types.Parameter{
			{
				Name: "length",
				Desc: "随机字符串的长度",
				Type: constants.NUMBER,
			},
		},
		Returns: []types.Return{
			{
				Name: "output",
				Desc: "生成的随机字符串",
				Type: constants.STRING,
			},
		},
		Example:  `randAscii(3) => &{a`,
		Category: constants.String,
	},
	{
		Name: "wrap",
		Desc: "Wrap text at a given column count",
		Parameters: []types.Parameter{
			{
				Name: "columns",
				Desc: "列数",
				Type: constants.NUMBER,
			},
			{
				Name: "s",
				Desc: "待处理的字符串",
				Type: constants.STRING,
			},
		},
		Returns: []types.Return{
			{
				Name: "output",
				Desc: "处理后的字符串",
				Type: constants.STRING,
			},
		},
		Example:  `wrap(80, "Hello World") => Hello World`,
		Category: constants.String,
	},
	{
		Name: "wrapWith",
		Desc: "Wrap text at a given column count with specify string (default uses \\n)",
		Parameters: []types.Parameter{
			{
				Name: "columns",
				Desc: "列数",
				Type: constants.NUMBER,
			},
			{
				Name: "char",
				Desc: "指定的换行符",
				Type: constants.STRING,
			},
			{
				Name: "s",
				Desc: "待处理的字符串",
				Type: constants.STRING,
			},
		},
		Returns: []types.Return{
			{
				Name: "output",
				Desc: "处理后的字符串",
				Type: constants.STRING,
			},
		},
		Example:  `wrapWith(3, "\t", "Hello World") => Hel        lo        Wor        ld (where the whitespace is an ASCII tab character)`,
		Category: constants.String,
	},
	{
		Name: "contains",
		Desc: "Whether one string is contained inside of another",
		Parameters: []types.Parameter{
			{
				Name: "substr",
				Desc: "子字符串",
				Type: constants.STRING,
			},
			{
				Name: "s",
				Desc: "待检查的字符串",
				Type: constants.STRING,
			},
		},
		Returns: []types.Return{
			{
				Name: "output",
				Desc: "是否包含子字符串",
				Type: constants.BOOLEAN,
			},
		},
		Example:  `contains("cat", "catch") => true`,
		Category: constants.String,
	},
	{
		Name: "hasPrefix",
		Desc: "Whether a string has a given prefix",
		Parameters: []types.Parameter{
			{
				Name: "prefix",
				Desc: "前缀字符串",
				Type: constants.STRING,
			},
			{
				Name: "s",
				Desc: "待检查的字符串",
				Type: constants.STRING,
			},
		},
		Returns: []types.Return{
			{
				Name: "output",
				Desc: "是否以前缀字符串开始",
				Type: constants.BOOLEAN,
			},
		},
		Example:  `hasPrefix("cat", "catch") => true`,
		Category: constants.String,
	},
	{
		Name: "hasSuffix",
		Desc: "Whether a string has a given suffix",
		Parameters: []types.Parameter{
			{
				Name: "suffix",
				Desc: "后缀字符串",
				Type: constants.STRING,
			},
			{
				Name: "s",
				Desc: "待检查的字符串",
				Type: constants.STRING,
			},
		},
		Returns: []types.Return{
			{
				Name: "output",
				Desc: "是否以后缀字符串结尾",
				Type: constants.BOOLEAN,
			},
		},
		Example:  `hasSuffix("cat", "catch") => false`,
		Category: constants.String,
	},
	{
		Name: "quote",
		Desc: "Wrap a string in double quotes",
		Parameters: []types.Parameter{
			{
				Name: "s",
				Desc: "待处理的字符串",
				Type: constants.STRING,
			},
		},
		Returns: []types.Return{
			{
				Name: "output",
				Desc: "处理后的字符串",
				Type: constants.STRING,
			},
		},
		Example:  `quote("Hello World") => "Hello World"`,
		Category: constants.String,
	},
	{
		Name: "squote",
		Desc: "Wrap a string in single quotes",
		Parameters: []types.Parameter{
			{
				Name: "s",
				Desc: "待处理的字符串",
				Type: constants.STRING,
			},
		},
		Returns: []types.Return{
			{
				Name: "output",
				Desc: "处理后的字符串",
				Type: constants.STRING,
			},
		},
		Example:  `squote("Hello World") => 'Hello World'`,
		Category: constants.String,
	},
	{
		Name: "cat",
		Desc: "Concatenates multiple strings together into one, separating them with spaces",
		Parameters: []types.Parameter{
			{
				Name:     "strs",
				Desc:     "待处理的若干字符串",
				Type:     constants.STRING,
				Variadic: true,
			},
		},
		Returns: []types.Return{
			{
				Name: "output",
				Desc: "处理后的字符串",
				Type: constants.STRING,
			},
		},
		Example:  `cat("Hello", "World") => Hello World`,
		Category: constants.String,
	},
	{
		Name: "indent",
		Desc: "Indents every line in a given string to the specified indent width",
		Parameters: []types.Parameter{
			{
				Name: "spaces",
				Desc: "缩进宽度",
				Type: constants.NUMBER,
			},
			{
				Name: "s",
				Desc: "待处理的字符串",
				Type: constants.STRING,
			},
		},
		Returns: []types.Return{
			{
				Name: "output",
				Desc: "处理后的字符串",
				Type: constants.STRING,
			},
		},
		Example:  `indent(4, "Hello World") =>     Hello World`,
		Category: constants.String,
	},
	{
		Name: "nindent",
		Desc: "Indents every line in a given string to the specified indent width, and prepends a new line to the beginning of the string",
		Parameters: []types.Parameter{
			{
				Name: "spaces",
				Desc: "缩进宽度",
				Type: constants.NUMBER,
			},
			{
				Name: "s",
				Desc: "待处理的字符串",
				Type: constants.STRING,
			},
		},
		Returns: []types.Return{
			{
				Name: "output",
				Desc: "处理后的字符串",
				Type: constants.STRING,
			},
		},
		Example:  `nindent(4, ""Hello World"") => \n    Hello World`,
		Category: constants.String,
	},
	{
		Name: "replace",
		Desc: "Perform simple string replacement\t",
		Parameters: []types.Parameter{
			{
				Name: "old",
				Desc: "需要替换的字符串",
				Type: constants.STRING,
			},
			{
				Name: "new",
				Desc: "替换为的字符串",
				Type: constants.STRING,
			},
			{
				Name: "s",
				Desc: "待处理的字符串",
				Type: constants.STRING,
			},
		},
		Returns: []types.Return{
			{
				Name: "output",
				Desc: "处理后的字符串",
				Type: constants.STRING,
			},
		},
		Example:  `replace(" ", "-", "Hello World") => Hello-World`,
		Category: constants.String,
	},
	{
		Name: "plural",
		Desc: "Pluralize a string",
		Parameters: []types.Parameter{
			{
				Name: "one",
				Desc: "表示单数的字符串",
				Type: constants.STRING,
			},
			{
				Name: "many",
				Desc: "表示复数的字符串",
				Type: constants.STRING,
			},
			{
				Name: "count",
				Desc: "数量",
				Type: constants.NUMBER,
			},
		},
		Returns: []types.Return{
			{
				Name: "output",
				Desc: "处理后的字符串",
				Type: constants.STRING,
			},
		},
		Example:  `plural("one fish", "many fishes", 5) => many fishes`,
		Category: constants.String,
	},
	{
		Name: "snakecase",
		Desc: "Convert string from camelCase to snake_case",
		Parameters: []types.Parameter{
			{
				Name: "s",
				Desc: "待处理的字符串",
				Type: constants.STRING,
			},
		},
		Returns: []types.Return{
			{
				Name: "output",
				Desc: "处理后的字符串",
				Type: constants.STRING,
			},
		},
		Example:  `snakecase("HelloWorld") => hello_world`,
		Category: constants.String,
	},
	{
		Name: "camelcase",
		Desc: "Convert string from snake_case to camelCase",
		Parameters: []types.Parameter{
			{
				Name: "s",
				Desc: "待处理的字符串",
				Type: constants.STRING,
			},
		},
		Returns: []types.Return{
			{
				Name: "output",
				Desc: "处理后的字符串",
				Type: constants.STRING,
			},
		},
		Example:  `camelcase("hello_world") => HelloWorld`,
		Category: constants.String,
	},
	{
		Name: "kebabcase",
		Desc: "Convert string from camelCase to kebab-case",
		Parameters: []types.Parameter{
			{
				Name: "s",
				Desc: "待处理的字符串",
				Type: constants.STRING,
			},
		},
		Returns: []types.Return{
			{
				Name: "output",
				Desc: "处理后的字符串",
				Type: constants.STRING,
			},
		},
		Example:  `kebabcase("HelloWorld") => hello-world`,
		Category: constants.String,
	},
	{
		Name: "swapcase",
		Desc: "Swap the case of a string",
		Parameters: []types.Parameter{
			{
				Name: "s",
				Desc: "待处理的字符串",
				Type: constants.STRING,
			},
		},
		Returns: []types.Return{
			{
				Name: "output",
				Desc: "处理后的字符串",
				Type: constants.STRING,
			},
		},
		Example:  `swapcase("UPPER Title lower") => upper tITLE LOWER`,
		Category: constants.String,
	},
	{
		Name: "shuffle",
		Desc: "Shuffle a string",
		Parameters: []types.Parameter{
			{
				Name: "s",
				Desc: "待处理的字符串",
				Type: constants.STRING,
			},
		},
		Returns: []types.Return{
			{
				Name: "output",
				Desc: "处理后的字符串",
				Type: constants.STRING,
			},
		},
		Example:  `shuffle("hello") => oelhl (maybe)`,
		Category: constants.String,
	},
	{
		Name: "regexMatch",
		Desc: "Whether the input string contains any match of the regular expression",
		Parameters: []types.Parameter{
			{
				Name: "regex",
				Desc: "正则表达式",
				Type: constants.STRING,
			},
			{
				Name: "s",
				Desc: "待检查的字符串",
				Type: constants.STRING,
			},
		},
		Returns: []types.Return{
			{
				Name: "output",
				Desc: "是否匹配",
				Type: constants.BOOLEAN,
			},
		},
		Example:  `regexMatch("^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,}$", "<EMAIL>") => true`,
		Category: constants.String,
	},
	{
		Name: "regexFindAll",
		Desc: "Returns a slice of all matches of the regular expression in the input string",
		Parameters: []types.Parameter{
			{
				Name: "regex",
				Desc: "正则表达式",
				Type: constants.STRING,
			},
			{
				Name: "s",
				Desc: "待检查的字符串",
				Type: constants.STRING,
			},
			{
				Name: "n",
				Desc: "返回匹配子串的数量，-1表示返回全部匹配的子串",
				Type: constants.NUMBER,
			},
		},
		Returns: []types.Return{
			{
				Name: "output",
				Desc: "匹配的子串列表",
				Type: constants.ARRAY,
			},
		},
		Example:  `regexFindAll("\\(.*?\\)", "Allen(allen), Bob(bob), Charles(charles)", -1) => [(allen), (bob), (charles)]`,
		Category: constants.String,
	},
	{
		Name: "regexFind",
		Desc: "Returns the first (left most) match of the regular expression in the input string",
		Parameters: []types.Parameter{
			{
				Name: "regex",
				Desc: "正则表达式",
				Type: constants.STRING,
			},
			{
				Name: "s",
				Desc: "待检查的字符串",
				Type: constants.STRING,
			},
		},
		Returns: []types.Return{
			{
				Name: "output",
				Desc: "匹配的字符串",
				Type: constants.STRING,
			},
		},
		Example:  `regexFind("[a-zA-Z][1-9]", "abcd1234") => d1`,
		Category: constants.String,
	},
	{
		Name: "regexReplaceAll",
		Desc: "Returns a copy of the input string, replacing matches of the regex expression with the replacement string replacement",
		Parameters: []types.Parameter{
			{
				Name: "regex",
				Desc: "正则表达式",
				Type: constants.STRING,
			},
			{
				Name: "s",
				Desc: "待处理的字符串",
				Type: constants.STRING,
			},
			{
				Name: "replacement",
				Desc: "替换为的字符串",
				Type: constants.STRING,
			},
		},
		Returns: []types.Return{
			{
				Name: "output",
				Desc: "处理后的字符串",
				Type: constants.STRING,
			},
		},
		Example:  `regexReplaceAll("a(x*)b", "-ab-axxb-", "${1}W") => -W-xxW-`,
		Category: constants.String,
	},
	{
		Name: "regexReplaceAllLiteral",
		Desc: "Returns a copy of the input string, replacing matches of the regex expression with the replacement string replacement. The replacement string is substituted directly, without using Expand",
		Parameters: []types.Parameter{
			{
				Name: "regex",
				Desc: "正则表达式",
				Type: constants.STRING,
			},
			{
				Name: "s",
				Desc: "待处理的字符串",
				Type: constants.STRING,
			},
			{
				Name: "replacement",
				Desc: "替换为的字符串",
				Type: constants.STRING,
			},
		},
		Returns: []types.Return{
			{
				Name: "output",
				Desc: "处理后的字符串",
				Type: constants.STRING,
			},
		},
		Example:  `regexReplaceAllLiteral("a(x*)b", "-ab-axxb-", "${1}") => -${1}-${1}-`,
		Category: constants.String,
	},
	{
		Name: "regexSplit",
		Desc: "Slices the input string into substrings separated by the expression and returns a slice of the substrings between those expression matches",
		Parameters: []types.Parameter{
			{
				Name: "regex",
				Desc: "正则表达式",
				Type: constants.STRING,
			},
			{
				Name: "s",
				Desc: "待切割的字符串",
				Type: constants.STRING,
			},
			{
				Name: "n",
				Desc: "返回切割后子串的数量，-1表示返回全部切割后的子串",
				Type: constants.NUMBER,
			},
		},
		Returns: []types.Return{
			{
				Name: "output",
				Desc: "切割后的子串列表",
				Type: constants.ARRAY,
			},
		},
		Example:  `regexSplit("z+", "pizza", -1) => [pi, a]`,
		Category: constants.String,
	},
	{
		Name: "regexQuoteMeta",
		Desc: "Returns a string that escapes all regular expression metacharacters inside the argument text, the returned string is a regular expression matching the literal text",
		Parameters: []types.Parameter{
			{
				Name: "s",
				Desc: "待处理的字符串",
				Type: constants.STRING,
			},
		},
		Returns: []types.Return{
			{
				Name: "output",
				Desc: "处理后的字符串",
				Type: constants.STRING,
			},
		},
		Example:  `regexQuoteMeta("1.2.3") => 1\.2\.3`,
		Category: constants.String,
	},
	{
		Name: "join",
		Desc: "Join a list of strings into a single string, with the given separator",
		Parameters: []types.Parameter{
			{
				Name: "sep",
				Desc: "分隔符",
				Type: constants.STRING,
			},
			{
				Name: "strs",
				Desc: "待接合的字符串列表",
				Type: constants.ARRAY,
			},
		},
		Returns: []types.Return{
			{
				Name: "output",
				Desc: "接合后的字符串",
				Type: constants.STRING,
			},
		},
		Example:  `join("_", ["hello", "world"]) => hello_world`,
		Category: constants.String,
	},
	{
		Name: "splitList",
		Desc: "Split a string into a list of strings",
		Parameters: []types.Parameter{
			{
				Name: "sep",
				Desc: "分隔符",
				Type: constants.STRING,
			},
			{
				Name: "s",
				Desc: "待拆分的字符串",
				Type: constants.STRING,
			},
		},
		Returns: []types.Return{
			{
				Name: "output",
				Desc: "拆分后的字符串列表",
				Type: constants.ARRAY,
			},
		},
		Example:  `splitList("$", "foo$bar$baz") => [foo, bar, baz]`,
		Category: constants.String,
	},
	{
		Name: "sortAlpha",
		Desc: "Sorts a list of strings into alphabetical (lexicographical) order",
		Parameters: []types.Parameter{
			{
				Name: "strs",
				Desc: "待排序的字符串列表",
				Type: constants.ARRAY,
			},
		},
		Returns: []types.Return{
			{
				Name: "output",
				Desc: "排序后的字符串列表",
				Type: constants.ARRAY,
			},
		},
		Example:  `sortAlpha(["give", "gift", "gate"]) => [gate, gift, give]`,
		Category: constants.String,
	},
	{
		Func:       functions.SentenceZH,
		Name:       "sentenceZH",
		Desc:       "Randomly select a Chinese sentence from the corpus",
		Parameters: []types.Parameter{},
		Returns: []types.Return{
			{
				Name: "output",
				Desc: "生成的随机中文句子",
				Type: constants.STRING,
			},
		},
		Example:  "sentenceZH() => 去爱吧，像没有受过伤一样。跳舞吧，像没有人会看一样。唱歌吧，像没有人会听一样",
		Category: constants.String,
	},
	{
		Func:       functions.PhraseZH,
		Name:       "phraseZH",
		Desc:       "Randomly select a Chinese phrase from the corpus",
		Parameters: []types.Parameter{},
		Returns: []types.Return{
			{
				Name: "output",
				Desc: "生成的随机中文短语",
				Type: constants.STRING,
			},
		},
		Example:  "phraseZH() => 很正常的",
		Category: constants.String,
	},
	{
		Func:       functions.NounZH,
		Name:       "nounZH",
		Desc:       "Randomly select a random Chinese noun from the corpus",
		Parameters: []types.Parameter{},
		Returns: []types.Return{
			{
				Name: "output",
				Desc: "生成的随机中文名词",
				Type: constants.STRING,
			},
		},
		Example:  "nounZH() => 撸串研究生",
		Category: constants.String,
	},
	{
		Func: functions.RandZH,
		Name: "randZH",
		Desc: "Generate a Chinese string of specified length",
		Parameters: []types.Parameter{
			{
				Name: "length",
				Desc: "随机字符串的长度",
				Type: constants.NUMBER,
			},
		},
		Returns: []types.Return{
			{
				Name: "output",
				Desc: "生成的随机字符串",
				Type: constants.STRING,
			},
		},
		Example:  `randZH(3) => 戭梭伜`,
		Category: constants.String,
	},
}
