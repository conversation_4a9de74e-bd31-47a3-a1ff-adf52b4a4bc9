package docs

import (
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/template/internal/functions"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/template/internal/types"
)

var encodingDocs = []types.Function{
	{
		Name: "b64enc",
		Desc: "Returns the base64 encoding of the input string",
		Parameters: []types.Parameter{
			{
				Name: "s",
				Desc: "待处理的字符串",
				Type: constants.STRING,
			},
		},
		Returns: []types.Return{
			{
				Name: "output",
				Desc: "处理后的字符串",
				Type: constants.STRING,
			},
		},
		Example:  `b64enc("Hello World") => SGVsbG8gV29ybGQ=`,
		Category: constants.Encoding,
	},
	{
		Name: "b64dec",
		Desc: "Returns the base64 decoding of the input string",
		Parameters: []types.Parameter{
			{
				Name: "s",
				Desc: "待处理的字符串",
				Type: constants.STRING,
			},
		},
		Returns: []types.Return{
			{
				Name: "output",
				Desc: "处理后的字符串",
				Type: constants.STRING,
			},
		},
		Example:  `b64dec("SGVsbG8gV29ybGQ=") => Hello World`,
		Category: constants.Encoding,
	},
	{
		Name: "b32enc",
		Desc: "Returns the base32 encoding of the input string",
		Parameters: []types.Parameter{
			{
				Name: "s",
				Desc: "待处理的字符串",
				Type: constants.STRING,
			},
		},
		Returns: []types.Return{
			{
				Name: "output",
				Desc: "处理后的字符串",
				Type: constants.STRING,
			},
		},
		Example:  `b32enc("Hello World") => JBSWY3DPEBLW64TMMQ======`,
		Category: constants.Encoding,
	},
	{
		Name: "b32dec",
		Desc: "Returns the base32 decoding of the input string",
		Parameters: []types.Parameter{
			{
				Name: "s",
				Desc: "待处理的字符串",
				Type: constants.STRING,
			},
		},
		Returns: []types.Return{
			{
				Name: "output",
				Desc: "处理后的字符串",
				Type: constants.STRING,
			},
		},
		Example:  `b32dec("JBSWY3DPEBLW64TMMQ======") => Hello World`,
		Category: constants.Encoding,
	},
	{
		Func: functions.Md5,
		Name: "md5",
		Desc: "Returns the MD5 checksum of the input string as a hexadecimal encoded string",
		Parameters: []types.Parameter{
			{
				Name: "s",
				Desc: "待处理的字符串",
				Type: constants.STRING,
			},
		},
		Returns: []types.Return{
			{
				Name: "output",
				Desc: "处理后的字符串",
				Type: constants.STRING,
			},
		},
		Example:  `md5("Hello World") => b10a8db164e0754105b7a99be72e3fe5`,
		Category: constants.Encoding,
	},
	{
		Func: functions.Sha1,
		Name: "sha1",
		Desc: "Returns the SHA1 checksum of the input string as a hexadecimal encoded string",
		Parameters: []types.Parameter{
			{
				Name: "s",
				Desc: "待处理的字符串",
				Type: constants.STRING,
			},
		},
		Returns: []types.Return{
			{
				Name: "output",
				Desc: "处理后的字符串",
				Type: constants.STRING,
			},
		},
		Example:  `sha1("Hello World") => 0a4d55a8d778e5022fab701977c5d840bbc486d0`,
		Category: constants.Encoding,
	},
	{
		Func: functions.Sha256,
		Name: "sha256",
		Desc: "Returns the SHA256 checksum of the input string as a hexadecimal encoded string",
		Parameters: []types.Parameter{
			{
				Name: "s",
				Desc: "待处理的字符串",
				Type: constants.STRING,
			},
		},
		Returns: []types.Return{
			{
				Name: "output",
				Desc: "处理后的字符串",
				Type: constants.STRING,
			},
		},
		Example:  `sha256("Hello World") => a591a6d40bf420404a011733cfb7b190d62c65bf0bcda32b57b277d9ad9f146e`,
		Category: constants.Encoding,
	},
	{
		Func: functions.HmacSHA1,
		Name: "hmacSHA1",
		Desc: "Returns the HMAC-SHA1 tag for the given message and key as a base64 encoded string",
		Parameters: []types.Parameter{
			{
				Name: "s",
				Desc: "待处理的字符串",
				Type: constants.STRING,
			},
		},
		Returns: []types.Return{
			{
				Name: "output",
				Desc: "处理后的字符串",
				Type: constants.STRING,
			},
		},
		Example:  `hmacSHA1("Hello World", "key") => zCTxrNsGz0Kbz5hhttcItuwgqPo=`,
		Category: constants.Encoding,
	},
	{
		Func: functions.HmacSHA256,
		Name: "hmacSHA256",
		Desc: "Returns the HMAC-SHA256 tag for the given message and key as a base64 encoded string",
		Parameters: []types.Parameter{
			{
				Name: "s",
				Desc: "待处理的字符串",
				Type: constants.STRING,
			},
		},
		Returns: []types.Return{
			{
				Name: "output",
				Desc: "处理后的字符串",
				Type: constants.STRING,
			},
		},
		Example:  `hmacSHA256("Hello World", "key") => QidPcoCDkuWLV9co1+PA5RY+rwfRZdwDEF9iq74afRE=`,
		Category: constants.Encoding,
	},
	{
		Func: functions.Hex,
		Name: "hex",
		Desc: "Returns the hexadecimal encoding of the input string",
		Parameters: []types.Parameter{
			{
				Name: "s",
				Desc: "待处理的字符串",
				Type: constants.STRING,
			},
		},
		Returns: []types.Return{
			{
				Name: "output",
				Desc: "处理后的字符串",
				Type: constants.STRING,
			},
		},
		Example:  `hex("Hello World") => 48656c6c6f20576f726c64`,
		Category: constants.Encoding,
	},
}
