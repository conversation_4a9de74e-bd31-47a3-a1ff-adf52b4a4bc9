package docs

import (
	"github.com/Masterminds/sprig/v3"
	"github.com/zeromicro/go-zero/core/logx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/template/internal/types"
)

var (
	docs [][]types.Function

	total int
)

func init() {
	m := sprig.TxtFuncMap()
	for _, fs := range [][]types.Function{
		stringDocs,
		numberDocs,
		dateDocs,
		encodingDocs,
		listDocs,
		dictionaryDocs,
		pathDocs,
		typeConversionDocs,
		otherDocs,
	} {
		i := 0
		for _, f := range fs {
			sf, ok := m[f.Name]
			if !ok && (f.Func == nil) {
				logx.Warnf("the data processing function[%s] is not implemented", f.Name)
				continue
			} else if ok && (f.Func == nil) {
				// 没有重写的内置函数使用`sprig`的模板函数
				f.Func = sf
			}
			fs[i] = f
			i++
		}
		total += i
		docs = append(docs, fs[:i])
	}
}

func FunctionDocs() []types.Function {
	out := make([]types.Function, total)

	var i int
	for _, fs := range docs {
		i += copy(out[i:], fs)
	}

	return out
}
