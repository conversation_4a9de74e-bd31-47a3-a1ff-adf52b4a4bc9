package docs

import (
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/template/internal/functions"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/template/internal/types"
)

var dateDocs = []types.Function{
	{
		Func:       functions.Now,
		Name:       "now",
		Desc:       "Returns a Unix time, the number of seconds elapsed since January 1, 1970 UTC",
		Parameters: []types.Parameter{},
		Returns: []types.Return{
			{
				Name: "output",
				Desc: "当前时间戳，单位为秒",
				Type: constants.NUMBER,
			},
		},
		Example:  "now() => 1657715417 (2022-07-13 20:30:17)",
		Category: constants.Date,
	},
	{
		Name: "ago",
		Desc: "Returns duration from the specify unix timestamp to now",
		Parameters: []types.Parameter{
			{
				Name: "t",
				Desc: "时间戳",
				Type: constants.NUMBER,
			},
		},
		Returns: []types.Return{
			{
				Name: "output",
				Desc: "时间差",
				Type: constants.NUMBER,
			},
		},
		Example:  "ago(1657715417) => 36s (now: 2022-07-13 20:30:53)",
		Category: constants.Date,
	},
	{
		Func: functions.Date,
		Name: "date",
		Desc: "Formats a date in local timezone",
		Parameters: []types.Parameter{
			{
				Name: "f",
				Desc: "Go的日期格式，具体请查看：https://go.dev/src/time/format.go",
				Type: constants.STRING,
			},
			{
				Name: "t",
				Desc: "时间戳",
				Type: constants.NUMBER,
			},
		},
		Returns: []types.Return{
			{
				Name: "output",
				Desc: "格式化后的字符串",
				Type: constants.STRING,
			},
		},
		Example:  `date("2006-01-02", 1657715417) => 2022-07-13`,
		Category: constants.Date,
	},
	{
		Func: functions.DateInZone,
		Name: "dateInZone",
		Desc: "Formats a date in specify timezone",
		Parameters: []types.Parameter{
			{
				Name: "f",
				Desc: "Go的日期格式，具体请查看：https://go.dev/src/time/format.go",
				Type: constants.STRING,
			},
			{
				Name: "t",
				Desc: "时间戳",
				Type: constants.NUMBER,
			},
			{
				Name:    "timezone",
				Desc:    "时区",
				Type:    constants.STRING,
				Default: "UTC",
			},
		},
		Returns: []types.Return{
			{
				Name: "output",
				Desc: "格式化后的字符串",
				Type: constants.STRING,
			},
		},
		Example:  `date("2006-01-02 15:04:05", 1657715417, "UTC") => 2022-07-13 12:30:17`,
		Category: constants.Date,
	},
	{
		Name: "duration",
		Desc: "Formats a given amount of seconds",
		Parameters: []types.Parameter{
			{
				Name: "t",
				Desc: "秒数",
				Type: constants.NUMBER,
			},
		},
		Returns: []types.Return{
			{
				Name: "output",
				Desc: "可阅读的时间字符串",
				Type: constants.STRING,
			},
		},
		Example:  "duration(95) => 1m35s",
		Category: constants.Date,
	},
}
