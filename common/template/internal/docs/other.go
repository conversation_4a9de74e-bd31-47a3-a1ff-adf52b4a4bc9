package docs

import (
	"github.com/jmespath/go-jmespath"
	"github.com/zeromicro/go-zero/core/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/template/internal/types"
)

var otherDocs = []types.Function{
	{
		Func:       utils.NewNanoId,
		Name:       "nanoid",
		Desc:       "Generate Nano ID",
		Parameters: []types.Parameter{},
		Returns: []types.Return{
			{
				Name: "output",
				Desc: "生成的NanoID",
				Type: constants.STRING,
			},
		},
		Example:  "nanoid() => YWb3GBvLl1AWSKCf5SL-c",
		Category: constants.Other,
	},
	{
		Func:       utils.NewUuid,
		Name:       "uuidv4",
		Desc:       "Generate v4's UUID",
		Parameters: []types.Parameter{},
		Returns: []types.Return{
			{
				Name: "output",
				Desc: "生成的v4版本的UUID",
				Type: constants.STRING,
			},
		},
		Example:  "uuidv4() => fef83089-5d12-4bad-bb26-e012fc3bb1de",
		Category: constants.Other,
	},
	{
		Func: jmespath.Search,
		Name: "jmespath",
		Desc: "Evaluates a JMESPath expression against input data and returns the result",
		Parameters: []types.Parameter{
			{
				Name: "expression",
				Desc: "JMESPath表达式",
				Type: constants.STRING,
			},
			{
				Name: "data",
				Desc: "JSON数据",
				Type: constants.ANY,
			},
		},
		Returns: []types.Return{
			{
				Name: "output",
				Desc: "根据JMESPath表达式提取的结果",
				Type: constants.ANY,
			},
		},
		Example:  `jmespath("students[0].name", {"students": [{"name": "Allen", "age": 20}, {"name": "Bob", "age": 21}]}) => "Allen"`,
		Category: constants.Other,
	},
}
