package docs

import (
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/template/internal/types"
)

var dictionaryDocs = []types.Function{
	{
		Name: "dict",
		Desc: "Returns a dictionary",
		Parameters: []types.Parameter{
			{
				Name:     "e",
				Desc:     "生成字典的元素（注：key1, value1, key2, value2, ...，因此元素个数为偶数个；另外key必须为字符串，value则可以为任意类型）",
				Type:     constants.ANY,
				Variadic: true,
			},
		},
		Returns: []types.Return{
			{
				Name: "output",
				Desc: "生成的字典",
				Type: constants.OBJECT,
			},
		},
		Example:  `dict("key1", "value1", "key2", "value2") => {"key1": "value1", "key2": "value2"}`,
		Category: constants.Dictionary,
	},
	{
		Name: "get",
		Desc: "Get the value of the specify key from the dictionary",
		Parameters: []types.Parameter{
			{
				Name: "d",
				Desc: "字典",
				Type: constants.OBJECT,
			},
			{
				Name: "key",
				Desc: "键",
				Type: constants.STRING,
			},
		},
		Returns: []types.Return{
			{
				Name: "output",
				Desc: "值",
				Type: constants.ANY,
			},
		},
		Example:  `get({"key1": "value1", "key2": "value2"}, "key1") => value1`,
		Category: constants.Dictionary,
	},
	{
		Name: "set",
		Desc: "Add a new key/value pair to a dictionary",
		Parameters: []types.Parameter{
			{
				Name: "d",
				Desc: "字典",
				Type: constants.OBJECT,
			},
			{
				Name: "key",
				Desc: "键",
				Type: constants.STRING,
			},
			{
				Name: "value",
				Desc: "值",
				Type: constants.ANY,
			},
		},
		Returns: []types.Return{
			{
				Name: "output",
				Desc: "增加键值对后的字典",
				Type: constants.OBJECT,
			},
		},
		Example:  `set({"key1": "value1", "key2": "value2"}, "key3", "value3") => {"key1": "value1", "key2": "value2", "key3": "value3"}`,
		Category: constants.Dictionary,
	},
	{
		Name: "unset",
		Desc: "Delete the key/value pair from a dictionary",
		Parameters: []types.Parameter{
			{
				Name: "d",
				Desc: "字典",
				Type: constants.OBJECT,
			},
			{
				Name: "key",
				Desc: "键",
				Type: constants.STRING,
			},
		},
		Returns: []types.Return{
			{
				Name: "output",
				Desc: "删除键值对后的字典",
				Type: constants.OBJECT,
			},
		},
		Example:  `unset({"key1": "value1", "key2": "value2"}, "key1") => {"key2": "value2"}`,
		Category: constants.Dictionary,
	},
	{
		Name: "hasKey",
		Desc: "Whether the specify key in the dictionary",
		Parameters: []types.Parameter{
			{
				Name: "d",
				Desc: "字典",
				Type: constants.OBJECT,
			},
			{
				Name: "key",
				Desc: "键",
				Type: constants.STRING,
			},
		},
		Returns: []types.Return{
			{
				Name: "output",
				Desc: "指定的键是否存在于字典中",
				Type: constants.BOOLEAN,
			},
		},
		Example:  `hasKey({"key1": "value1", "key2": "value2"}, "key1") => true`,
		Category: constants.Dictionary,
	},
	{
		Name: "keys",
		Desc: "Returns a list of all of the keys in a dictionary",
		Parameters: []types.Parameter{
			{
				Name: "d",
				Desc: "字典",
				Type: constants.OBJECT,
			},
		},
		Returns: []types.Return{
			{
				Name: "output",
				Desc: "键列表",
				Type: constants.ARRAY,
			},
		},
		Example:  `keys({"key1": "value1", "key2": "value2"}) => [key1, key2]`,
		Category: constants.Dictionary,
	},
	{
		Name: "values",
		Desc: "Returns a list of all the values of a dictionary",
		Parameters: []types.Parameter{
			{
				Name: "d",
				Desc: "字典",
				Type: constants.OBJECT,
			},
		},
		Returns: []types.Return{
			{
				Name: "output",
				Desc: "值列表",
				Type: constants.ARRAY,
			},
		},
		Example:  `keys({"key1": "value1", "key2": "value2"}) => [value1, value2]`,
		Category: constants.Dictionary,
	},
}
