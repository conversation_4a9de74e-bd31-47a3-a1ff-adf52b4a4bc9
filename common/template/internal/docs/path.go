package docs

import (
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/template/internal/types"
)

var pathDocs = []types.Function{
	{
		Name: "base",
		Desc: "Returns the last element of path",
		Parameters: []types.Parameter{
			{
				Name: "p",
				Desc: "待处理的路径（包括：Linux和MacOS的文件系统路径、URI的路径部分）",
				Type: constants.STRING,
			},
		},
		Returns: []types.Return{
			{
				Name: "output",
				Desc: "路径的最后一部分",
				Type: constants.STRING,
			},
		},
		Example:  `base("foo/bar/baz") => baz`,
		Category: constants.Path,
	},
	{
		Name: "dir",
		Desc: "Returns the directory, stripping the last part of the path",
		Parameters: []types.Parameter{
			{
				Name: "p",
				Desc: "待处理的路径（包括：Linux和MacOS的文件系统路径、URI的路径部分）",
				Type: constants.STRING,
			},
		},
		Returns: []types.Return{
			{
				Name: "output",
				Desc: "处理后的路径",
				Type: constants.STRING,
			},
		},
		Example:  `dir("foo/bar/baz") => foo/bar`,
		Category: constants.Path,
	},
	{
		Name: "clean",
		Desc: "Returns the shortest path name equivalent to path by purely lexical processing",
		Parameters: []types.Parameter{
			{
				Name: "p",
				Desc: "待处理的路径（包括：Linux和MacOS的文件系统路径、URI的路径部分）",
				Type: constants.STRING,
			},
		},
		Returns: []types.Return{
			{
				Name: "output",
				Desc: "处理后的路径",
				Type: constants.STRING,
			},
		},
		Example:  `clean("foo/bar/../baz") => foo/baz`,
		Category: constants.Path,
	},
	{
		Name: "ext",
		Desc: "Returns the file name extension used by path",
		Parameters: []types.Parameter{
			{
				Name: "p",
				Desc: "待处理的路径（包括：Linux和MacOS的文件系统路径、URI的路径部分）",
				Type: constants.STRING,
			},
		},
		Returns: []types.Return{
			{
				Name: "output",
				Desc: "路径的后缀",
				Type: constants.STRING,
			},
		},
		Example:  `ext("foo.bar") => bar`,
		Category: constants.Path,
	},
	{
		Name: "isAbs",
		Desc: "Whether the path is absolute",
		Parameters: []types.Parameter{
			{
				Name: "p",
				Desc: "待处理的路径（包括：Linux和MacOS的文件系统路径、URI的路径部分）",
				Type: constants.STRING,
			},
		},
		Returns: []types.Return{
			{
				Name: "output",
				Desc: "是否绝对路径",
				Type: constants.STRING,
			},
		},
		Example:  `isAbs("foo/bar/baz") => false`,
		Category: constants.Path,
	},
	{
		Name: "osBase",
		Desc: "Returns the last element of path",
		Parameters: []types.Parameter{
			{
				Name: "p",
				Desc: "待处理的路径（包括：Linux、MacOS和Windows的文件系统路径）",
				Type: constants.STRING,
			},
		},
		Returns: []types.Return{
			{
				Name: "output",
				Desc: "路径的最后一部分",
				Type: constants.STRING,
			},
		},
		Example:  `osBase("/foo/bar/baz") => baz`,
		Category: constants.Path,
	},
	{
		Name: "osDir",
		Desc: "Returns the directory, stripping the last part of the path",
		Parameters: []types.Parameter{
			{
				Name: "p",
				Desc: "待处理的路径（包括：Linux、MacOS和Windows的文件系统路径）",
				Type: constants.STRING,
			},
		},
		Returns: []types.Return{
			{
				Name: "output",
				Desc: "处理后的路径",
				Type: constants.STRING,
			},
		},
		Example:  `osDir("/foo/bar/baz") => /foo/bar`,
		Category: constants.Path,
	},
	{
		Name: "osClean",
		Desc: "Returns the shortest path name equivalent to path by purely lexical processing",
		Parameters: []types.Parameter{
			{
				Name: "p",
				Desc: "待处理的路径（包括：Linux、MacOS和Windows的文件系统路径）",
				Type: constants.STRING,
			},
		},
		Returns: []types.Return{
			{
				Name: "output",
				Desc: "处理后的路径",
				Type: constants.STRING,
			},
		},
		Example:  `osClean("/foo/bar/../baz") => /foo/baz`,
		Category: constants.Path,
	},
	{
		Name: "osExt",
		Desc: "Returns the file name extension used by path",
		Parameters: []types.Parameter{
			{
				Name: "p",
				Desc: "待处理的路径（包括：Linux、MacOS和Windows的文件系统路径）",
				Type: constants.STRING,
			},
		},
		Returns: []types.Return{
			{
				Name: "output",
				Desc: "路径的后缀",
				Type: constants.STRING,
			},
		},
		Example:  `osExt("/foo.bar") => bar`,
		Category: constants.Path,
	},
	{
		Name: "osIsAbs",
		Desc: "Whether the path is absolute",
		Parameters: []types.Parameter{
			{
				Name: "p",
				Desc: "待处理的路径（包括：Linux、MacOS和Windows的文件系统路径）",
				Type: constants.STRING,
			},
		},
		Returns: []types.Return{
			{
				Name: "output",
				Desc: "是否绝对路径",
				Type: constants.STRING,
			},
		},
		Example:  `osIsAbs("../foo/bar/baz") => false`,
		Category: constants.Path,
	},
}
