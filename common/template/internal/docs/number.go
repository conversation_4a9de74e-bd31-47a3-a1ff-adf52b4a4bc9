package docs

import (
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/template/internal/functions"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/template/internal/types"
)

var numberDocs = []types.Function{
	{
		Name: "add",
		Desc: "Sum numbers",
		Parameters: []types.Parameter{
			{
				Name:     "i",
				Desc:     "待计算的数字",
				Type:     constants.NUMBER,
				Variadic: true,
			},
		},
		Returns: []types.Return{
			{
				Name: "output",
				Desc: "计算结果",
				Type: constants.NUMBER,
			},
		},
		Example:  "add(1, 2, 3) => 6",
		Category: constants.Number,
	},
	{
		Name: "add1",
		Desc: "To increment by 1",
		Parameters: []types.Parameter{
			{
				Name: "i",
				Desc: "待计算的数字",
				Type: constants.NUMBER,
			},
		},
		Returns: []types.Return{
			{
				Name: "output",
				Desc: "计算结果",
				Type: constants.NUMBER,
			},
		},
		Example:  "add1(1) => 2",
		Category: constants.Number,
	},
	{
		Name: "sub",
		Desc: "To subtract",
		Parameters: []types.Parameter{
			{
				Name: "a",
				Desc: "被减数",
				Type: constants.NUMBER,
			},
			{
				Name: "b",
				Desc: "减数",
				Type: constants.NUMBER,
			},
		},
		Returns: []types.Return{
			{
				Name: "output",
				Desc: "计算结果",
				Type: constants.NUMBER,
			},
		},
		Example:  "sub(5, 2) => 3",
		Category: constants.Number,
	},
	{
		Name: "div",
		Desc: "Perform integer division",
		Parameters: []types.Parameter{
			{
				Name: "a",
				Desc: "被除数",
				Type: constants.NUMBER,
			},
			{
				Name: "b",
				Desc: "除数",
				Type: constants.NUMBER,
			},
		},
		Returns: []types.Return{
			{
				Name: "output",
				Desc: "计算结果",
				Type: constants.NUMBER,
			},
		},
		Example:  "div(12, 4) => 3",
		Category: constants.Number,
	},
	{
		Name: "mod",
		Desc: "Modulo",
		Parameters: []types.Parameter{
			{
				Name: "a",
				Desc: "被除数",
				Type: constants.NUMBER,
			},
			{
				Name: "b",
				Desc: "除数",
				Type: constants.NUMBER,
			},
		},
		Returns: []types.Return{
			{
				Name: "output",
				Desc: "计算结果",
				Type: constants.NUMBER,
			},
		},
		Example:  "mod(12, 5) => 2",
		Category: constants.Number,
	},
	{
		Name: "mul",
		Desc: "Multiply",
		Parameters: []types.Parameter{
			{
				Name:     "i",
				Desc:     "待计算的数字",
				Type:     constants.NUMBER,
				Variadic: true,
			},
		},
		Returns: []types.Return{
			{
				Name: "output",
				Desc: "计算结果",
				Type: constants.NUMBER,
			},
		},
		Example:  "mul(1, 2, 3) => 6",
		Category: constants.Number,
	},
	{
		Func: functions.Max,
		Name: "max",
		Desc: "Returns the largest of a series of integers",
		Parameters: []types.Parameter{
			{
				Name:     "i",
				Desc:     "待计算的数字",
				Type:     constants.NUMBER,
				Variadic: true,
			},
		},
		Returns: []types.Return{
			{
				Name: "output",
				Desc: "计算结果",
				Type: constants.NUMBER,
			},
		},
		Example:  "max(1, 2, 3) => 3",
		Category: constants.Number,
	},
	{
		Func: functions.Min,
		Name: "min",
		Desc: "Returns the smallest of a series of integers",
		Parameters: []types.Parameter{
			{
				Name:     "i",
				Desc:     "待计算的数字",
				Type:     constants.NUMBER,
				Variadic: true,
			},
		},
		Returns: []types.Return{
			{
				Name: "output",
				Desc: "计算结果",
				Type: constants.NUMBER,
			},
		},
		Example:  "min(1, 2, 3) => 1",
		Category: constants.Number,
	},
	{
		Func: functions.Floor,
		Name: "floor",
		Desc: "Returns the greatest float value less than or equal to input value",
		Parameters: []types.Parameter{
			{
				Name: "i",
				Desc: "待计算的数字",
				Type: constants.NUMBER,
			},
		},
		Returns: []types.Return{
			{
				Name: "output",
				Desc: "计算结果",
				Type: constants.NUMBER,
			},
		},
		Example:  "floor(123.9999) => 123.0",
		Category: constants.Number,
	},
	{
		Func: functions.Ceil,
		Name: "ceil",
		Desc: "Returns the greatest float value greater than or equal to input value",
		Parameters: []types.Parameter{
			{
				Name: "i",
				Desc: "待计算的数字",
				Type: constants.NUMBER,
			},
		},
		Returns: []types.Return{
			{
				Name: "output",
				Desc: "计算结果",
				Type: constants.NUMBER,
			},
		},
		Example:  "ceil(123.001) => 124.0",
		Category: constants.Number,
	},
	{
		Func: functions.Round,
		Name: "round",
		Desc: "Returns a float value with the remainder rounded to the given number to digits after the decimal point",
		Parameters: []types.Parameter{
			{
				Name: "i",
				Desc: "待计算的数字",
				Type: constants.NUMBER,
			},
			{
				Name: "p",
				Desc: "计算后保留的位数",
				Type: constants.NUMBER,
			},
		},
		Returns: []types.Return{
			{
				Name: "output",
				Desc: "计算结果",
				Type: constants.NUMBER,
			},
		},
		Example:  "round(123.555555, 3) => 123.556",
		Category: constants.Number,
	},
	{
		Func: functions.Choice,
		Name: "randInt",
		Desc: "Returns a random integer value from min (inclusive) to max (exclusive)",
		Parameters: []types.Parameter{
			{
				Name: "min",
				Desc: "随机数的下限（包含）",
				Type: constants.NUMBER,
			},
			{
				Name: "max",
				Desc: "随机数的上限（不包含）",
				Type: constants.NUMBER,
			},
		},
		Returns: []types.Return{
			{
				Name: "output",
				Desc: "随机数",
				Type: constants.NUMBER,
			},
		},
		Example:  "randInt(12, 30) => 15 (maybe)",
		Category: constants.Number,
	},
	{
		Func: functions.Until,
		Name: "until",
		Desc: "Generates a list of counting integers",
		Parameters: []types.Parameter{
			{
				Name: "count",
				Desc: "数量",
				Type: constants.NUMBER,
			},
		},
		Returns: []types.Return{
			{
				Name: "output",
				Desc: "生成的数字列表",
				Type: constants.ARRAY,
			},
		},
		Example:  "until(5) => [0, 1, 2, 3, 4]",
		Category: constants.Number,
	},
	{
		Func: functions.UntilStep,
		Name: "untilStep",
		Desc: "Generates a list of counting integers with specify the start, stop and step",
		Parameters: []types.Parameter{
			{
				Name: "start",
				Desc: "起始数",
				Type: constants.NUMBER,
			},
			{
				Name: "stop",
				Desc: "结束数",
				Type: constants.NUMBER,
			},
			{
				Name: "step",
				Desc: "步长",
				Type: constants.NUMBER,
			},
		},
		Returns: []types.Return{
			{
				Name: "output",
				Desc: "生成的数字列表",
				Type: constants.ARRAY,
			},
		},
		Example:  "untilStep(3, 6, 2) => [3, 5]",
		Category: constants.Number,
	},
	{
		Name: "seq",
		Desc: "Works like the bash `seq` command",
		Parameters: []types.Parameter{
			{
				Name:     "params",
				Desc:     "参数",
				Type:     constants.NUMBER,
				Variadic: true,
			},
		},
		Returns: []types.Return{
			{
				Name: "output",
				Desc: "生成的数字列表",
				Type: constants.ARRAY,
			},
		},
		Example:  "seq(5) => [1, 2, 3, 4, 5]",
		Category: constants.Number,
	},
	{
		Name: "addf",
		Desc: "Sum numbers",
		Parameters: []types.Parameter{
			{
				Name:     "i",
				Desc:     "待计算的数字",
				Type:     constants.NUMBER,
				Variadic: true,
			},
		},
		Returns: []types.Return{
			{
				Name: "output",
				Desc: "计算结果",
				Type: constants.NUMBER,
			},
		},
		Example:  "addf(1.5, 2, 2) => 5.5",
		Category: constants.Number,
	},
	{
		Name: "add1f",
		Desc: "To increment by 1",
		Parameters: []types.Parameter{
			{
				Name: "i",
				Desc: "待计算的数字",
				Type: constants.NUMBER,
			},
		},
		Returns: []types.Return{
			{
				Name: "output",
				Desc: "计算结果",
				Type: constants.NUMBER,
			},
		},
		Example:  "add1f(1.5) => 2.5",
		Category: constants.Number,
	},
	{
		Name: "subf",
		Desc: "To subtract",
		Parameters: []types.Parameter{
			{
				Name: "a",
				Desc: "被减数",
				Type: constants.NUMBER,
			},
			{
				Name: "b",
				Desc: "减数",
				Type: constants.NUMBER,
			},
		},
		Returns: []types.Return{
			{
				Name: "output",
				Desc: "计算结果",
				Type: constants.NUMBER,
			},
		},
		Example:  "subf(7.5, 2) => 5.5",
		Category: constants.Number,
	},
	{
		Name: "divf",
		Desc: "Perform integer division",
		Parameters: []types.Parameter{
			{
				Name: "a",
				Desc: "被除数",
				Type: constants.NUMBER,
			},
			{
				Name: "b",
				Desc: "除数",
				Type: constants.NUMBER,
			},
		},
		Returns: []types.Return{
			{
				Name: "output",
				Desc: "计算结果",
				Type: constants.NUMBER,
			},
		},
		Example:  "divf(10, 8) => 1.25",
		Category: constants.Number,
	},
	{
		Name: "mulf",
		Desc: "Multiply",
		Parameters: []types.Parameter{
			{
				Name:     "i",
				Desc:     "待计算的数字",
				Type:     constants.NUMBER,
				Variadic: true,
			},
		},
		Returns: []types.Return{
			{
				Name: "output",
				Desc: "计算结果",
				Type: constants.NUMBER,
			},
		},
		Example:  "mulf(1.5, 2, 2) => 6",
		Category: constants.Number,
	},
	{
		Name: "maxf",
		Desc: "Returns the largest of a series of floats",
		Parameters: []types.Parameter{
			{
				Name:     "i",
				Desc:     "待计算的数字",
				Type:     constants.NUMBER,
				Variadic: true,
			},
		},
		Returns: []types.Return{
			{
				Name: "output",
				Desc: "计算结果",
				Type: constants.NUMBER,
			},
		},
		Example:  "maxf(1, 2.5, 3) => 3",
		Category: constants.Number,
	},
	{
		Name: "minf",
		Desc: "Returns the smallest of a series of floats",
		Parameters: []types.Parameter{
			{
				Name:     "i",
				Desc:     "待计算的数字",
				Type:     constants.NUMBER,
				Variadic: true,
			},
		},
		Returns: []types.Return{
			{
				Name: "output",
				Desc: "计算结果",
				Type: constants.NUMBER,
			},
		},
		Example:  "min(1.5, 2, 3) => 1.5",
		Category: constants.Number,
	},
}
