package template

import (
	"testing"

	"github.com/zeromicro/go-zero/core/jsonx"
)

func TestExecute(t *testing.T) {
	type args struct {
		tpl  string
		data map[string]any
	}

	tests := []struct {
		name string
		args args
	}{
		{
			name: "call `int` function",
			args: args{
				tpl: `{ "base_req": {}, "account": "{{ .username }}" "channel_id": {{int .channel_id}} }`,
				data: map[string]any{
					"username":   "***********",
					"password":   "absavmoizigf",
					"channel_id": 2042456,
				},
			},
		},
		{
			name: "call `list` and `choose` function",
			args: args{
				tpl: `{"name": {{ list .name1 .name2 .name3 | choose }}}`,
				data: map[string]any{
					"name1": "allen",
					"name2": "bob",
					"name3": "charles",
				},
			},
		},
		{
			name: "call `from<PERSON><PERSON>` and `choose` function",
			args: args{
				tpl: `{"name": {{ fromJson .names | choose }}}`,
				data: map[string]any{
					"names": `["allen", "bob", "charles"]`,
				},
			},
		},
	}

	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				got, err := Execute(tt.name, tt.args.tpl, tt.args.data)
				if err != nil {
					t.Fatalf("Execute() error = %+v", err)
				}

				t.Logf("Execute()\nbefore = %s\nafter = %s", tt.args.tpl, got)
			},
		)
	}
}

func TestCall(t *testing.T) {
	type args struct {
		name string
		args []interface{}
	}

	var students map[string]interface{}
	_ = jsonx.UnmarshalFromString(`{"students": [{"name": "Allen", "age": 20}, {"name": "Bob", "age": 21}]}`, &students)

	tests := []struct {
		name string
		args args
	}{
		{
			name: "call `randInt` function",
			args: args{
				name: "randInt",
				args: []interface{}{int64(10), int32(25)},
			},
		},
		{
			name: "call `now` function",
			args: args{
				name: "now",
				args: nil,
			},
		},
		{
			name: "call `now` function with too many input arguments",
			args: args{
				name: "now",
				args: []interface{}{1, 2, "allen"},
			},
		},
		{
			name: "call `without` function",
			args: args{
				name: "without",
				args: []interface{}{[]int{1, 2, 3, 4, 5}, 3, 2},
			},
		},
		{
			name: "call `jmespath` function",
			args: args{
				name: "jmespath",
				args: []interface{}{"students[0].name", students},
			},
		},
		{
			name: "call `date` function",
			args: args{
				name: "date",
				args: []interface{}{"2006-01-02 15:04:05", 1657715417},
			},
		},
	}

	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				got, err := Call(tt.args.name, tt.args.args...)
				if err != nil {
					t.Fatalf("Call() error = %+v", err)
				}

				t.Logf("Call() args = %+v, result = %+v", tt.args.args, got)
			},
		)
	}
}
