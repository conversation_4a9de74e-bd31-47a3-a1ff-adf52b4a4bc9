package template

import (
	"bytes"
	"fmt"
	"io"
	"reflect"
	"regexp"
	"strings"
	"sync"
	ttemplate "text/template"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/logx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/codes"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/template/internal/docs"
)

var (
	mutex               sync.RWMutex
	blankCharRegCompile = regexp.MustCompile("\r|\n|\\s+")
	templateFuncMap     = make(ttemplate.FuncMap)
	templateFuncDocs    []Function
	templateFuncDocMap  = make(map[string]Function)
	templates           = make(map[string]*ttemplate.Template)

	reflectValueType = reflect.TypeOf((*reflect.Value)(nil)).Elem()

	FuncDocNotFound = errors.New("document of builtin function is not found")
)

func init() {
	templateFuncDocs = docs.FunctionDocs()
	for _, fd := range templateFuncDocs {
		templateFuncMap[fd.Name] = fd.Func
		templateFuncDocMap[fd.Name] = fd
	}
}

// Store add a new template function
func Store(key string, value any) {
	mutex.Lock()
	defer mutex.Unlock()

	templateFuncMap[key] = value
}

func FuncMap() ttemplate.FuncMap {
	mutex.RLock()
	defer mutex.RUnlock()

	tfm := make(map[string]any, len(templateFuncMap))
	for k, v := range templateFuncMap {
		tfm[k] = v
	}
	return tfm
}

func FuncDocs() []Function {
	tfd := make([]Function, len(templateFuncDocs))
	copy(tfd, templateFuncDocs)
	return tfd
}

func FuncDoc(name string) (Function, error) {
	fd, ok := templateFuncDocMap[name]
	if !ok {
		return fd, FuncDocNotFound
	}

	return fd, nil
}

func Parse(name, tpl string) (*ttemplate.Template, error) {
	return ParseWithFuncs(name, tpl, nil)
}

func ParseWithFuncs(name, tpl string, funcMap ttemplate.FuncMap) (t *ttemplate.Template, err error) {
	key := generateKey(name, tpl, funcMap)
	if t = findTemplate(key); t != nil {
		return t, nil
	}

	t, err = newTemplate(key, name, tpl, funcMap)
	if err != nil {
		return nil, errors.Wrapf(
			errorx.Err(codes.ParseOrRenderTemplateFailure, err.Error()),
			"failed to parse template, name: %s, template: %s, error: %+v",
			name, blankCharRegCompile.ReplaceAllString(tpl, ""), err,
		)
	}

	return t, nil
}

func generateKey(name, tpl string, funcMap ttemplate.FuncMap) string {
	keys := make([]string, 0, len(funcMap)+2)
	keys = append(keys, name, tpl)
	for k := range funcMap {
		keys = append(keys, k)
	}

	return strings.Join(keys, "-")
}

func findTemplate(key string) *ttemplate.Template {
	mutex.RLock()
	defer mutex.RUnlock()

	t, ok := templates[key]
	if ok {
		return t
	}

	return nil
}

func newTemplate(key, name, tpl string, funcMap ttemplate.FuncMap) (t *ttemplate.Template, err error) {
	defer func() {
		if err == nil {
			mutex.Lock()
			defer mutex.Unlock()

			templates[key] = t
		}
	}()

	if funcMap == nil {
		t, err = ttemplate.New(name).Funcs(templateFuncMap).Parse(tpl)
	} else {
		m := FuncMap()
		for k, v := range funcMap {
			m[k] = v
		}

		t, err = ttemplate.New(name).Funcs(m).Parse(tpl)
	}
	if err != nil {
		return nil, err
	}

	return t, nil
}

func Execute(name, tpl string, data any) ([]byte, error) {
	t, err := Parse(name, tpl)
	if err != nil {
		return nil, err
	}

	bf := new(bytes.Buffer)
	if err = t.Execute(bf, data); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(codes.ParseOrRenderTemplateFailure, err.Error()),
			"failed to render template[%s]: error: %+v",
			blankCharRegCompile.ReplaceAllString(tpl, ""), err,
		)
	}

	return bf.Bytes(), nil
}

func PrintTemplate(w io.Writer, name, tpl string, data any) error {
	t, err := Parse(name, tpl)
	if err != nil {
		return err
	}

	err = t.Execute(w, data)

	if o, ok := w.(interface{ Flush() error }); ok {
		_ = o.Flush()
	}

	return err
}

func Call(name string, args ...any) (any, error) {
	fn, err := findFunction(name)
	if err != nil {
		// return nil, errors.WithStack(errorx.Err(errorx.CallTemplateFunctionFailure, err.Error()))
		return nil, err
	}

	var variadicType reflect.Type
	var argv []reflect.Value

	typ := fn.Type()
	numIn := typ.NumIn()
	if typ.IsVariadic() {
		if len(args) < numIn-1 {
			return nil, errors.Errorf(
				"failed to call the builtin function[%s], error: wrong number of args: got %d want at least %d",
				name, len(args), numIn-1,
			)
		}
		variadicType = typ.In(numIn - 1).Elem()
		argv = make([]reflect.Value, len(args))
	} else {
		if len(args) < numIn {
			return nil, errors.Errorf(
				"failed to call the builtin function[%s], error: wrong number of args: got %d want %d", name, len(args),
				numIn,
			)
		}
		argv = make([]reflect.Value, numIn)
	}

	for i, arg := range args {
		if !typ.IsVariadic() && i >= numIn {
			logx.Warnf("call the builtin function[%s] with too many input arguments", name)
			break
		}

		a := reflect.ValueOf(arg)

		argType := variadicType
		if !typ.IsVariadic() || i < numIn-1 {
			argType = typ.In(i)
		}

		argv[i], err = prepareArg(a, argType)
		if err != nil {
			return nil, errors.Errorf("failed to prepare argv[%d], error: %+v", i, err)
		}
	}

	ret, err := safeCall(fn, argv)
	if err != nil {
		return nil, errors.Errorf("failed to call the builtin function[%s], error: %+v", name, err)
	}
	return ret.Interface(), nil
}

func findFunction(name string) (reflect.Value, error) {
	mutex.RLock()
	defer mutex.RUnlock()

	f, ok := templateFuncMap[name]
	if !ok {
		return reflect.Value{}, errors.Errorf("not found the builtin function[%s]", name)
	}

	fn := reflect.ValueOf(f)
	if fn.Kind() != reflect.Func || !fn.IsValid() {
		return fn, errors.Errorf("the builtin function[%s] is not a valid golang function", name)
	}

	return fn, nil
}

func prepareArg(val reflect.Value, typ reflect.Type) (reflect.Value, error) {
	if !val.IsValid() {
		if !canBeNil(typ) {
			return reflect.Value{}, errors.Errorf("value is nil, should be of type %s", typ)
		}
		val = reflect.Zero(typ)
	}
	if val.Type().AssignableTo(typ) {
		return val, nil
	}
	if val.Type().ConvertibleTo(typ) {
		val = val.Convert(typ)
		return val, nil
	}
	return reflect.Value{}, errors.Errorf("value has type %s, should be %s", val.Type(), typ)
}

// canBeNil reports whether an untyped nil can be assigned to the type. See reflect.Zero.
func canBeNil(typ reflect.Type) bool {
	switch typ.Kind() {
	case reflect.Chan, reflect.Func, reflect.Interface, reflect.Map, reflect.Pointer, reflect.Slice:
		return true
	case reflect.Struct:
		return typ == reflectValueType
	default:
		return false
	}
}

func safeCall(fun reflect.Value, argv []reflect.Value) (val reflect.Value, err error) {
	defer func() {
		if r := recover(); r != nil {
			if e, ok := r.(error); ok {
				err = e
			} else {
				err = fmt.Errorf("%v", r)
			}
		}
	}()

	ret := fun.Call(argv)
	if len(ret) == 2 && !ret[1].IsNil() {
		return ret[0], ret[1].Interface().(error)
	}
	return ret[0], nil
}
