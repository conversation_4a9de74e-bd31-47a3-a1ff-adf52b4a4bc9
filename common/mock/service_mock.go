package mock

import (
	"context"

	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/stores/redis"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"go.uber.org/zap"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/log"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/common/zrpc"
)

func Mock_Mysql() sqlx.SqlConn {
	// source := "root:Quwan@2020@tcp(127.0.0.1:3306)/manager?charset=utf8mb4&parseTime=true&loc=Asia%2FShanghai"
	source := "probe:Quwan@2020_TTinternation@tcp(************:3306)/manager?charset=utf8mb4&parseTime=true&loc=Asia%2FShanghai"
	return sqlx.NewMysql(source)
}

func Mock_RedisConf() redis.RedisConf {
	return redis.RedisConf{
		Host: "127.0.0.1:6379",
		Type: "node",
		Pass: "Quwan@2020",
		DB:   4,
	}
}

func Mock_ReporterRpc() *zrpc.ReporterRPC {
	return &zrpc.ReporterRPC{
		Name: "rpc.reporter.prod",
	}
}

func Mock_DispatcherRpc() *zrpc.DispatcherRpc {
	return &zrpc.DispatcherRpc{
		Name: "rpc.dispatcher.prod",
	}
}

func Mock_ManagerRpc() *zrpc.ManagerRPC {
	return &zrpc.ManagerRPC{
		Name: "rpc.manager.prod",
	}
}

func Mock_UserRpc() *zrpc.UserRPC {
	return &zrpc.UserRPC{
		Name: "rpc.user.prod",
	}
}

func Mock_NotifyRpc() *zrpc.NotifierRpc {
	return &zrpc.NotifierRpc{
		Name: "rpc.notifier.prod",
	}
}

func Mock_Log() {
	logconf := logx.LogConf{
		ServiceName: "mqc.dispatcher",
		Encoding:    "plain",
		Mode:        "console",
	}
	w := log.NewZapWriter(logconf, zap.AddCaller(), zap.Development())
	log.SetWriter(w)
}

func Mock_Context() context.Context {
	return context.Background()
}
