package db

import (
	"context"
	"fmt"

	"github.com/Masterminds/squirrel"
	"github.com/zeromicro/go-zero/core/stores/sqlc"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sqlbuilder"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sqlbuilder/rpc"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/types"
)

const (
	defaultZero     = 0
	defaultPageNum  = 1
	defaultPageSize = 10
)

type PageQuery struct {
	Pagination *rpc.Pagination
	Condition  *rpc.Condition
	OrderBy    []string
}

type DiyQuery struct {
	Field string
	Value string
}

type PageInfo[T any] struct {
	DataList    []T    `json:"data_list"`
	CurrentPage uint64 `json:"current_page"`
	PageSize    uint64 `json:"page_size"`
	Pages       uint64 `json:"pages"`
	Total       uint64 `json:"total"`
}

func NewPageInfo[T any]() *PageInfo[T] {
	return &PageInfo[T]{}
}

func (m *PageInfo[T]) FindByPageArgAndQuery(
	ctx context.Context, pageNum, pageSize uint64, countBuilder, queryBuilder squirrel.SelectBuilder,
	conn sqlc.CachedConn,
) error {
	m.checkPagNumAndSize(&pageSize, &pageSize)
	var (
		resp  []T
		count uint64
	)
	countQuery, values, err := countBuilder.ToSql()
	if err != nil {
		return err
	}
	err = conn.QueryRowNoCacheCtx(ctx, &count, countQuery, values...)
	if err != nil {
		return err
	}
	// 校验 offset 和pagenum合法性质
	offset := pageSize * (pageNum - 1)
	queryBuilder = queryBuilder.Limit(pageSize).Offset(offset)
	query, values, err := queryBuilder.ToSql()
	if err != nil {
		return err
	}
	err = conn.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
	if err != nil {
		return err
	}

	m.Pages = m.pagesNum(count, pageSize)
	m.Total = count
	m.CurrentPage = pageNum
	m.PageSize = pageSize
	m.DataList = resp
	return nil
}

func (m *PageInfo[T]) FindByPageQuery(
	ctx context.Context, pageQuery PageQuery, model types.DBModel,
	conn sqlc.CachedConn,
) error {
	m.checkPagination(pageQuery.Pagination)
	var (
		resp  []T
		count uint64
	)
	countSquirrel := squirrel.Select("count(1)").From(model.Table())
	countSquirrel = sqlbuilder.SearchOptions(
		countSquirrel,
		sqlbuilder.WithCondition(model, pageQuery.Condition),
	)
	countQuery, values, err := countSquirrel.ToSql()
	if err != nil {
		return err
	}
	err = conn.QueryRowNoCacheCtx(ctx, &count, countQuery, values...)
	if err != nil {
		return err
	}
	selectBuilder := sqlbuilder.SearchOptions(
		squirrel.Select(model.Fields()...).OrderBy(pageQuery.OrderBy...).From(model.Table()),
		sqlbuilder.WithCondition(model, pageQuery.Condition),
		sqlbuilder.WithPagination(model, pageQuery.Pagination),
	)
	query, values, err := selectBuilder.ToSql()
	if err != nil {
		return err
	}
	err = conn.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
	if err != nil {
		return err
	}
	m.Pages = m.pagesNum(count, pageQuery.Pagination.GetPageSize())
	m.Total = count
	m.CurrentPage = pageQuery.Pagination.CurrentPage
	m.PageSize = pageQuery.Pagination.PageSize
	m.DataList = resp
	return nil
}

func (m *PageInfo[T]) FindByPageQueryV2(
	ctx context.Context, pageQuery PageQuery, diyQueryList []DiyQuery, model types.DBModel,
	conn sqlc.CachedConn,
) error {
	m.checkPagination(pageQuery.Pagination)
	var (
		resp  []T
		count uint64
	)
	countSquirrel := squirrel.Select("count(1)").From(model.Table())
	countSquirrel = sqlbuilder.SearchOptions(
		countSquirrel,
		sqlbuilder.WithCondition(model, pageQuery.Condition),
	)
	selectBuilder := sqlbuilder.SearchOptions(
		squirrel.Select(model.Fields()...).OrderBy(pageQuery.OrderBy...).From(model.Table()),
		sqlbuilder.WithCondition(model, pageQuery.Condition),
		sqlbuilder.WithPagination(model, pageQuery.Pagination),
	)
	for _, query := range diyQueryList {
		selectBuilder = selectBuilder.Where(fmt.Sprintf("`%s` = ?", query.Field), query.Value)
		countSquirrel = countSquirrel.Where(fmt.Sprintf("`%s` = ?", query.Field), query.Value)
	}
	query, values, err := selectBuilder.ToSql()
	if err != nil {
		return err
	}
	err = conn.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
	if err != nil {
		return err
	}

	countQuery, values, err := countSquirrel.ToSql()
	if err != nil {
		return err
	}
	err = conn.QueryRowNoCacheCtx(ctx, &count, countQuery, values...)
	if err != nil {
		return err
	}

	m.Pages = m.pagesNum(count, pageQuery.Pagination.GetPageSize())
	m.Total = count
	m.CurrentPage = pageQuery.Pagination.CurrentPage
	m.PageSize = pageQuery.Pagination.PageSize
	m.DataList = resp
	return nil
}

func (m *PageInfo[T]) pagesNum(count, pageSize uint64) uint64 {
	// 计算最大页
	var pages uint64
	if count%pageSize == 0 {
		pages = count / pageSize
	} else {
		pages = count/pageSize + 1
	}
	return pages
}

func (m *PageInfo[T]) checkPagNumAndSize(pageNumArg, pageSizeArg *uint64) {
	if *pageNumArg == defaultZero {
		*pageNumArg = defaultPageNum
	}
	if *pageSizeArg == defaultZero {
		*pageSizeArg = defaultPageSize
	}
}

func (m *PageInfo[T]) checkPagination(pagination *rpc.Pagination) {
	if pagination.CurrentPage == defaultZero {
		pagination.CurrentPage = defaultPageNum
	}
	if pagination.PageSize == defaultZero {
		pagination.PageSize = defaultPageSize
	}
}
