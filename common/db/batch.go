package db

const (
	DefaultBatchSplitSize = uint(2000)
)

func BatchSplit[T any](dataList []*T, size uint, f func(dataList []*T) error) error {
	if len(dataList) == 0 {
		return nil
	}
	count, line, total := uint(0), uint(0), size
	if uint(len(dataList)) <= total || total == 0 {
		return f(dataList)
	}
	if uint(len(dataList))%total > 0 {
		count = uint(len(dataList))/total + 1
	} else {
		count = uint(len(dataList)) / total
	}
	for i := uint(1); i <= count; i++ {
		num1 := line * total
		num2 := total * i
		if total*i > uint(len(dataList)) {
			num2 = uint(len(dataList))
		}
		records := dataList[num1:num2]
		err := f(records)
		if err != nil {
			return err
		}
		line++
	}
	// _ = records
	return nil
}
