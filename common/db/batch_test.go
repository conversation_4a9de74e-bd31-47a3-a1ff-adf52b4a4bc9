package db

import (
	"fmt"
	"testing"
)

type Man struct {
	name string
	age  int64
}

func TestBatchSplit(t *testing.T) {
	mans := make([]*Man, 0)
	mans = append(
		mans, &<PERSON>{
			name: "1",
			age:  1,
		},
	)
	mans = append(
		mans, &<PERSON>{
			name: "2",
			age:  2,
		},
	)
	mans = append(
		mans, &<PERSON>{
			name: "3",
			age:  3,
		},
	)
	mans = append(
		mans, &Man{
			name: "4",
			age:  4,
		},
	)
	mans = append(
		mans, &Man{
			name: "5",
			age:  5,
		},
	)
	mans = append(
		mans, &<PERSON>{
			name: "6",
			age:  6,
		},
	)
	for i := uint(1); i < 8; i++ {
		err := BatchSplit(
			mans, i, func(dataList []*Man) error {
				for _, man := range dataList {
					fmt.Println(man.name)
					fmt.Println(man.age)
				}
				fmt.Println("======")
				return nil
			},
		)
		if err != nil {
			fmt.Println(err)
		}
	}
}
