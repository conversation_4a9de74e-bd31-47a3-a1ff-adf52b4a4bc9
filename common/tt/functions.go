package tt

import (
	"fmt"

	"github.com/zeromicro/go-zero/core/jsonx"
	"google.golang.org/protobuf/reflect/protoreflect"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
)

func GetLogicOptions(md protoreflect.MethodDescriptor) LogicOptions {
	sd, _ := md.Parent().(protoreflect.ServiceDescriptor)
	opts := LogicOptions{
		ServiceOptions: getServiceOptions(sd),
		MethodOptions:  getMethodOptions(md),
	}
	opts.OriginMethod = grpcPath(md)
	opts.RequestMethod = requestMethod(md, opts)
	opts.RewriteMethod = rewriteMethod(md, opts)
	return opts
}

func getServiceOptions(sd protoreflect.ServiceDescriptor) (opts ServiceOptions) {
	if sd != nil {
		sd.Options().ProtoReflect().Range(
			func(fd protoreflect.FieldDescriptor, v protoreflect.Value) bool {
				if fd.IsExtension() && fd.Kind() == protoreflect.StringKind {
					switch fd.FullName() {
					case logicServiceNameExtensionFullName:
						opts.Name = v.String()
					case logicServiceLanguageExtensionFullName:
						opts.Language = v.String()
					case logicServiceURIRewriteExtensionFullName:
						opts.URIRewrite = v.String()
					}

					return opts.Name == "" || opts.Language == "" || opts.URIRewrite == ""
				}

				return true // 继续遍历
			},
		)
	}

	return opts
}

func getMethodOptions(md protoreflect.MethodDescriptor) (opts MethodOptions) {
	if md != nil {
		md.Options().ProtoReflect().Range(
			func(fd protoreflect.FieldDescriptor, v protoreflect.Value) bool {
				if fd.IsExtension() && fd.Kind() == protoreflect.MessageKind && fd.FullName() == logicCommandExtensionFullName {
					bs, _ := protobuf.MarshalJSON(v.Message().Interface())
					_ = jsonx.Unmarshal(bs, &opts)

					return false // 跳出遍历
				}

				return true // 继续遍历
			},
		)
	}

	return opts
}

func requestMethod(md protoreflect.MethodDescriptor, opts LogicOptions) string {
	if opts.RequestMethod != "" {
		return opts.RequestMethod
	} else if opts.MethodOptions.Deprecated && opts.MethodOptions.RewriteFullPath != "" {
		return opts.MethodOptions.RewriteFullPath
	}

	return grpcPath(md)
}

func rewriteMethod(md protoreflect.MethodDescriptor, opts LogicOptions) string {
	if opts.RewriteMethod != "" {
		return opts.RewriteMethod
	} else if opts.MethodOptions.RewriteFullPath != "" {
		return opts.MethodOptions.RewriteFullPath
	} else if opts.ServiceOptions.URIRewrite != "" {
		serviceURI := opts.ServiceOptions.URIRewrite
		if serviceURI[0] != '/' {
			serviceURI = "/" + serviceURI
		}
		if serviceURI[len(serviceURI)-1] != '/' {
			serviceURI += "/"
		}

		return fmt.Sprintf("%s%s", serviceURI, md.Name())
	}

	return grpcPath(md)
}

func grpcPath(md protoreflect.MethodDescriptor) string {
	service, _ := md.Parent().(protoreflect.ServiceDescriptor)
	return fmt.Sprintf("/%s/%s", service.FullName(), md.Name())
}
