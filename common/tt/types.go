package tt

type ServiceOptions struct {
	Name       string `json:"logic_service_name"`
	Language   string `json:"logic_service_language"`
	URIRewrite string `json:"logic_service_uri_rewrite"`
}

type MethodOptions struct {
	ID              uint32 `json:"id"`
	Deprecated      bool   `json:"deprecated"`
	RewriteFullPath string `json:"rewrite_full_path"`
}

type LogicOptions struct {
	ServiceOptions ServiceOptions
	MethodOptions  MethodOptions

	OriginMethod  string // 原始请求的接口路径
	RequestMethod string // 最终请求的接口路径
	RewriteMethod string // 重写后的接口路径（监控面板的参数需要用此字段）
}
