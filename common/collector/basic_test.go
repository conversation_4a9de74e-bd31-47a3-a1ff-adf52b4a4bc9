package collector

import (
	"context"
	"fmt"
	"testing"
	"time"

	"github.com/zeromicro/go-zero/core/jsonx"
	"github.com/zeromicro/go-zero/core/lang"
	"github.com/zeromicro/go-zero/core/logx"
)

func TestSafeSendData(t *testing.T) {
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	bc := &basicCollector{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,

		dataTypes:        defaultDataTypes.Clone(),
		intervalOfCPU:    defaultIntervalOfCPU,
		intervalOfMemory: defaultIntervalOfMemory,
		intervalOfFPS:    defaultIntervalOfFPS,
		workers:          defaultWorkers,

		stopCh: make(chan lang.PlaceholderType),
		dataCh: make(chan *MetricData, 1),
	}

	// Test normal write
	testData := &MetricData{
		Type:     CPU,
		Interval: time.Second,
		Unit:     UnitOfPercentage,
		Data: &CPUData{
			Timestamp: time.Now(),
			Usage:     50.0,
		},
	}

	// This should work normally
	bc.safeSendData(testData)

	// Read the data to verify it was written
	select {
	case data := <-bc.dataCh:
		if data.Type != CPU {
			t.Errorf("Expected CPU data type, got %v", data.Type)
		}
	case <-time.After(time.Second):
		t.Error("Timeout waiting for data")
	}

	// Test write after channel is closed
	close(bc.dataCh)

	// This should not panic
	bc.safeSendData(testData)

	// Test write after context is cancelled
	cancel()
	bc.safeSendData(testData)

	// Test write after stop signal
	bc2 := &basicCollector{
		Logger: logx.WithContext(context.Background()),
		ctx:    context.Background(),

		dataTypes:        defaultDataTypes.Clone(),
		intervalOfCPU:    defaultIntervalOfCPU,
		intervalOfMemory: defaultIntervalOfMemory,
		intervalOfFPS:    defaultIntervalOfFPS,
		workers:          defaultWorkers,

		stopCh: make(chan lang.PlaceholderType),
		dataCh: make(chan *MetricData, 1),
	}

	close(bc2.stopCh)
	bc2.safeSendData(testData)

	t.Log("All safe write tests passed without panic")
}

// Test alternative implementation without explicit returns
func (c *basicCollector) safeSendDataAlternative(data *MetricData) {
	defer func() {
		if r := recover(); r != nil {
			c.Debugf("dataCh is closed, failed to write data: %s, error: %+v", jsonx.MarshalIgnoreError(data), r)
		}
	}()

	select {
	case <-c.ctx.Done():
		// No explicit return
	case <-c.stopCh:
		// No explicit return
	case c.dataCh <- data:
		// Successfully sent data
	}
	// Function ends here - implicit return
}

func TestSafeSendDataAlternative(t *testing.T) {
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	bc := &basicCollector{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		stopCh: make(chan lang.PlaceholderType),
		dataCh: make(chan *MetricData, 1),
	}

	testData := &MetricData{
		Type:     CPU,
		Interval: time.Second,
		Unit:     UnitOfPercentage,
		Data: &CPUData{
			Timestamp: time.Now(),
			Usage:     50.0,
		},
	}

	// Test with cancelled context
	cancel()
	bc.safeSendDataAlternative(testData)

	// Test with closed stop channel
	bc2 := &basicCollector{
		Logger: logx.WithContext(context.Background()),
		ctx:    context.Background(),
		stopCh: make(chan lang.PlaceholderType),
		dataCh: make(chan *MetricData, 1),
	}
	close(bc2.stopCh)
	bc2.safeSendDataAlternative(testData)

	t.Log("Alternative implementation also works without explicit returns")
}

func TestSendOnClosedChannel(t *testing.T) {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	ch := make(chan string, 1)
	go func() {
		index := 0
		timer := time.NewTicker(time.Second)
		defer timer.Stop()

		for range timer.C {
			func() {
				defer func() {
					if r := recover(); r != nil {
						t.Logf("panic: %v", r)
					}
				}()

				select {
				case <-ctx.Done():
					t.Log("context done")
					return
				case ch <- fmt.Sprintf("hello, %d", index):
					index++
				}
			}()
		}
	}()

	exit := false
	for !exit {
		select {
		case <-ctx.Done():
			t.Log("timer expired")
			exit = true
		case s := <-ch:
			t.Log(s)
		}
	}

	close(ch)
	time.Sleep(3 * time.Second)
}
