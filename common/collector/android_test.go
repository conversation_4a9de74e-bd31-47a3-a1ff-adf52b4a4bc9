package collector

import (
	"context"
	"testing"
	"time"

	"github.com/electricbubble/gadb"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/timewheel"
)

func TestAndroidCollector(t *testing.T) {
	client, err := gadb.NewClient()
	if err != nil {
		t.Fatalf("failed to new client: %v", err)
	}

	devices, err := client.ListDevices()
	if err != nil {
		t.Fatalf("failed to list devices: %v", err)
	} else if len(devices) == 0 {
		t.Fatal("no device found")
	}

	device := devices[0]
	packageName := "com.yiyou.ga"

	type args struct {
		ctx         context.Context
		device      *gadb.Device
		packageName string
		options     []Option
		timeout     time.Duration
		duration    time.Duration
	}
	tests := []struct {
		name string
		args args
	}{
		{
			name: "default options with 3min",
			args: args{
				ctx:         context.Background(),
				device:      device,
				packageName: packageName,
				duration:    3 * time.Minute,
			},
		},
		{
			name: "cpu with 3min",
			args: args{
				ctx:         context.Background(),
				device:      device,
				packageName: packageName,
				options: []Option{
					WithDataTypes(CPU),
					WithIntervalOfCPU(10 * time.Second),
				},
				duration: 3 * time.Minute,
			},
		},
		{
			name: "with custom callback function",
			args: args{
				ctx:         context.Background(),
				device:      device,
				packageName: packageName,
				options: []Option{
					WithCallback(
						func(dataType DataType, interval time.Duration, points []PointData) {
							t.Logf("DataType: %v, Interval: %v, Points: %v", dataType, interval, points)
						},
					),
				},
				duration: 3 * time.Minute,
			},
		},
		{
			name: "with context timeout",
			args: args{
				ctx:         context.Background(),
				device:      device,
				packageName: packageName,
				options: []Option{
					WithIntervalOfCPU(10 * time.Second),
					WithIntervalOfMemory(20 * time.Second),
				},
				timeout:  time.Minute,
				duration: 2 * time.Minute,
			},
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				if tt.args.timeout > 0 {
					ctx, cancel := context.WithTimeout(tt.args.ctx, tt.args.timeout)
					defer cancel()
					tt.args.ctx = ctx
				}

				c := NewAndroidCollector(
					tt.args.ctx, tt.args.device, tt.args.packageName, tt.args.options...,
				)
				if err := c.Start(); err != nil {
					t.Error(err)
				}
				defer func() {
					if err := c.Stop(); err != nil {
						t.Error(err)
					}
				}()

				timer := timewheel.NewTimer(tt.args.duration)
				select {
				case <-c.Finished():
					t.Log("finished")
					return
				case <-timer.C:
					t.Log("time is up")
					return
				}
			},
		)
	}
}

func TestTimeTruncate(t *testing.T) {
	type args struct {
		now      time.Time
		interval time.Duration
	}
	tests := []struct {
		name string
		args args
	}{
		{
			name: "2025-05-08 17:36:00",
			args: args{
				now:      time.Date(2025, 5, 8, 17, 36, 0, 0, time.Local),
				interval: 30 * time.Second,
			},
		},
		{
			name: "2025-05-08 17:36:05",
			args: args{
				now:      time.Date(2025, 5, 8, 17, 36, 5, 0, time.Local),
				interval: 30 * time.Second,
			},
		},
		{
			name: "2025-05-08 17:36:15",
			args: args{
				now:      time.Date(2025, 5, 8, 17, 36, 15, 0, time.Local),
				interval: 30 * time.Second,
			},
		},
		{
			name: "2025-05-08 17:36:28",
			args: args{
				now:      time.Date(2025, 5, 8, 17, 36, 28, 0, time.Local),
				interval: 30 * time.Second,
			},
		},
		{
			name: "2025-05-08 17:36:30",
			args: args{
				now:      time.Date(2025, 5, 8, 17, 36, 30, 0, time.Local),
				interval: 30 * time.Second,
			},
		},
		{
			name: "2025-05-08 17:36:32",
			args: args{
				now:      time.Date(2025, 5, 8, 17, 36, 32, 0, time.Local),
				interval: 30 * time.Second,
			},
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				t_ := tt.args.now.Truncate(tt.args.interval)
				r_ := tt.args.now.Round(tt.args.interval)
				next := tt.args.now.Add(tt.args.interval).Truncate(tt.args.interval)
				t.Logf(`
now:      %s
truncate: %s
round:	  %s
next:	  %s`, tt.args.now, t_, r_, next)
			},
		)
	}
}
