package collector

import (
	"time"

	"github.com/segmentio/fasthash/fnv1a"
	"github.com/zyedidia/generic"
	"github.com/zyedidia/generic/set"

	commonpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
)

const (
	defaultIntervalOfCPU = 30 * time.Second
	minIntervalOfCPU     = time.Second

	defaultIntervalOfMemory = 30 * time.Second
	minIntervalOfMemory     = time.Second

	defaultIntervalOfFPS = 30 * time.Second
	minIntervalOfFPS     = time.Second

	defaultWorkers = 16
	minWorkers     = 1
)

var defaultDataTypes = set.NewHashset[DataType](
	16, generic.Equals[DataType], Hashes, CPU, MEMORY, FPS,
)

type DataType string

const (
	CPU     DataType = "CPU"
	MEMORY  DataType = "MEMORY"
	FPS     DataType = "FPS"
	DISK    DataType = "DISK"
	NETWORK DataType = "NETWORK"
)

func Hashes[T DataType](v T) uint64 {
	return fnv1a.HashString64(string(v))
}

func (x DataType) ConvertToPerfDataType() commonpb.PerfDataType {
	switch x {
	case CPU:
		return commonpb.PerfDataType_PerfDataType_CPU
	case MEMORY:
		return commonpb.PerfDataType_PerfDataType_MEMORY
	case FPS:
		return commonpb.PerfDataType_PerfDataType_FPS
	case DISK:
		return commonpb.PerfDataType_PerfDataType_DISK
	case NETWORK:
		return commonpb.PerfDataType_PerfDataType_NETWORK
	default:
		return commonpb.PerfDataType_PerfDataType_NULL
	}
}

type Unit string

const (
	UnitOfPercentage Unit = "%"
	UnitOfByte       Unit = "B"
	UnitOfKiloByte   Unit = "KB"
	UnitOfMegaByte   Unit = "MB"
	UnitOfEmpty      Unit = ""
)

type Series string

const (
	SeriesOfUsage Series = "Usage"
	SeriesOfPSS   Series = "PSS"
	SeriesOfRSS   Series = "RSS"
	SeriesOfSwap  Series = "Swap"
	SeriesOfVSS   Series = "VSS"
	SeriesOfFPS   Series = "FPS"
)
