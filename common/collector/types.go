package collector

import (
	"strconv"
	"time"
)

type (
	CallBackFunc func(dataType DataType, interval time.Duration, points []PointData)

	IMetricData interface {
		ToPoints() []PointData
	}

	PointData struct {
		Series Series
		Unit   Unit
		X      string
		Y      string
	}

	MetricData struct {
		Type     DataType      `json:"type"`     // 数据类型
		Interval time.Duration `json:"interval"` // 采集间隔
		Unit     Unit          `json:"unit"`     // 单位
		Data     IMetricData   `json:"data"`     // 指标数据
	}

	CPUData struct {
		Timestamp time.Time `json:"timestamp"` // 时间戳
		Usage     float64   `json:"usage"`     // CPU使用率
	}

	MemoryDataOfAndroid struct {
		Timestamp time.Time `json:"timestamp"` // 时间戳
		PSS       int64     `json:"pss"`       // 进程占用的物理内存大小，会按比例分担共享内存（单位：KB）
		RSS       int64     `json:"rss"`       // 进程占用的物理内存大小，分担全部共享内存（单位：KB）
		Swap      int64     `json:"swap"`      // 进程占用的Swap内存大小（单位：KB）
	}

	MemoryDataOfIOS struct {
		Timestamp time.Time `json:"timestamp"` // 时间戳
		PSS       int64     `json:"pss"`       // 应用占用的物理内存大小（单位：B）
		RSS       int64     `json:"rss"`       // 应用分配的物理内存大小（单位：B）
		VSS       int64     `json:"vss"`       // 应用占用的虚拟内存大小（单位：B）
	}

	FPSData struct {
		Timestamp time.Time `json:"timestamp"` // 时间戳
		FPS       float64   `json:"fps"`       // 帧率
	}
)

func (x *CPUData) ToPoints() []PointData {
	return []PointData{
		{
			Series: SeriesOfUsage,
			Unit:   UnitOfPercentage,
			X:      x.Timestamp.Format(time.DateTime),
			Y:      strconv.FormatFloat(x.Usage, 'f', 2, 64),
		},
	}
}

func (x *MemoryDataOfAndroid) ToPoints() []PointData {
	timestamp := x.Timestamp.Format(time.DateTime)
	return []PointData{
		{
			Series: SeriesOfPSS,
			Unit:   UnitOfMegaByte,
			X:      timestamp,
			Y:      strconv.FormatInt(x.PSS/1024, 10),
		},
		{
			Series: SeriesOfRSS,
			Unit:   UnitOfMegaByte,
			X:      timestamp,
			Y:      strconv.FormatInt(x.RSS/1024, 10),
		},
		{
			Series: SeriesOfSwap,
			Unit:   UnitOfMegaByte,
			X:      timestamp,
			Y:      strconv.FormatInt(x.Swap/1024, 10),
		},
	}
}

func (x *MemoryDataOfIOS) ToPoints() []PointData {
	timestamp := x.Timestamp.Format(time.DateTime)
	return []PointData{
		{
			Series: SeriesOfPSS,
			Unit:   UnitOfMegaByte,
			X:      timestamp,
			Y:      strconv.FormatInt(x.PSS/1024/1024, 10),
		},
		{
			Series: SeriesOfRSS,
			Unit:   UnitOfMegaByte,
			X:      timestamp,
			Y:      strconv.FormatInt(x.RSS/1024/1024, 10),
		},
		{
			Series: SeriesOfVSS,
			Unit:   UnitOfMegaByte,
			X:      timestamp,
			Y:      strconv.FormatInt(x.VSS/1024/1024, 10),
		},
	}
}

func (x *FPSData) ToPoints() []PointData {
	return []PointData{
		{
			Series: SeriesOfFPS,
			Unit:   UnitOfEmpty,
			X:      x.Timestamp.Format(time.DateTime),
			Y:      strconv.FormatFloat(x.FPS, 'f', 2, 64),
		},
	}
}
