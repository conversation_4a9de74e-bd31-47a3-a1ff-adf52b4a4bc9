package metrics

import prommodel "github.com/prometheus/common/model"

const (
	ConstMetricsLabelNameOfTaskID         prommodel.LabelName = "task_id"
	ConstMetricsLabelNameOfExecuteID      prommodel.LabelName = "execute_id"
	ConstMetricsLabelNameOfProtocol       prommodel.LabelName = "protocol"
	ConstMetricsLabelNameOfName           prommodel.LabelName = "name"
	ConstMetricsLabelNameOfPath           prommodel.LabelName = "path"
	ConstMetricsLabelNameOfResult         prommodel.LabelName = "result"
	ConstMetricsLabelNameOfGrpcStatus     prommodel.LabelName = "grpc_status"
	ConstMetricsLabelNameOfHttpStatus     prommodel.LabelName = "http_status"
	ConstMetricsLabelNameOfBusinessStatus prommodel.LabelName = "business_status"
	ConstMetricsLabelNameOfLe             prommodel.LabelName = "le"

	ConstMetricsLabelNameOfProjectID      prommodel.LabelName = "project_id"
	ConstMetricsLabelNameOfProjectName    prommodel.LabelName = "project_name"
	ConstMetricsLabelNameOfPlanExecuteID  prommodel.LabelName = "plan_execute_id"
	ConstMetricsLabelNameOfPlanID         prommodel.LabelName = "plan_id"
	ConstMetricsLabelNameOfPlanName       prommodel.LabelName = "plan_name"
	ConstMetricsLabelNameOfSuiteExecuteID prommodel.LabelName = "suite_execute_id"
	ConstMetricsLabelNameOfSuiteID        prommodel.LabelName = "suite_id"
	ConstMetricsLabelNameOfSuiteName      prommodel.LabelName = "suite_name"
	ConstMetricsLabelNameOfCaseExecuteID  prommodel.LabelName = "case_execute_id"
	ConstMetricsLabelNameOfCaseID         prommodel.LabelName = "case_id"
	ConstMetricsLabelNameOfCaseName       prommodel.LabelName = "case_name"
	ConstMetricsLabelNameOfMaintainedBy   prommodel.LabelName = "maintained_by"
	ConstMetricsLabelNameOfStartedAt      prommodel.LabelName = "started_at"
	ConstMetricsLabelNameOfEndedAt        prommodel.LabelName = "ended_at"
)

const (
	ConstMetricsLabelValueOfRequestFailed     = "request failed"
	ConstMetricsLabelValueOfRequestSucceeded  = "request succeeded"
	ConstMetricsLabelValueOfResponseFailed    = "response failed"
	ConstMetricsLabelValueOfResponseSucceeded = "response succeeded"
)

const (
	ConstMetricsPerfTaskPrefix = "perf_"

	ConstMetricsLabelNameOfPerfTaskTaskID         = ConstMetricsPerfTaskPrefix + ConstMetricsLabelNameOfTaskID
	ConstMetricsLabelNameOfPerfTaskExecuteID      = ConstMetricsPerfTaskPrefix + ConstMetricsLabelNameOfExecuteID
	ConstMetricsLabelNameOfPerfTaskProtocol       = ConstMetricsPerfTaskPrefix + ConstMetricsLabelNameOfProtocol
	ConstMetricsLabelNameOfPerfTaskName           = ConstMetricsPerfTaskPrefix + ConstMetricsLabelNameOfName
	ConstMetricsLabelNameOfPerfTaskPath           = ConstMetricsPerfTaskPrefix + ConstMetricsLabelNameOfPath
	ConstMetricsLabelNameOfPerfTaskResult         = ConstMetricsPerfTaskPrefix + ConstMetricsLabelNameOfResult
	ConstMetricsLabelNameOfPerfTaskGrpcStatus     = ConstMetricsPerfTaskPrefix + ConstMetricsLabelNameOfGrpcStatus
	ConstMetricsLabelNameOfPerfTaskHttpStatus     = ConstMetricsPerfTaskPrefix + ConstMetricsLabelNameOfHttpStatus
	ConstMetricsLabelNameOfPerfTaskBusinessStatus = ConstMetricsPerfTaskPrefix + ConstMetricsLabelNameOfBusinessStatus
)
