package metrics

import "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/metrics"

const (
	ConstMetricsAccountPoolUsername        = "pool_username"
	ConstMetricsAccountPoolUsernameResidue = "pool_username_residue"
)

const (
	ConstMetricsPerfToolRequestTotal    = "perftool_request" + string(metrics.ConstSystemMetricUnitTypeTotal)
	ConstMetricsPerfToolRequestDuration = "perftool_request_duration" + string(metrics.ConstSystemMetricUnitTypeMilliseconds)
)

const (
	ConstMetricsApiWorkerCaseRequestTotal    = "apiworker_case_request" + string(metrics.ConstSystemMetricUnitTypeTotal)
	ConstMetricsApiWorkerCaseRequestDuration = "apiworker_case_request_duration" + string(metrics.ConstSystemMetricUnitTypeMilliseconds)
)
