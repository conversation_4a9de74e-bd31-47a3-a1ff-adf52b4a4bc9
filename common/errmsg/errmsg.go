package errmsg

import "sync"

var (
	mutex     sync.RWMutex
	msgToCode = map[ErrZHMsg]ErrCode{
		SuccessZHMsg: Success,
		UnknownZHMsg: Unknown,
	}
)

func RegisterErrZHMsg(m map[ErrZHMsg]ErrCode) {
	mutex.Lock()
	defer mutex.Unlock()

	for k, v := range m {
		msgToCode[k] = v
	}
}

type ErrMsg interface {
	Error() string
	Code() ErrCode
	Message() ErrZHMsg
}

type errMsg struct {
	err  error
	code ErrCode
	msg  ErrZHMsg
}

type Option func(e *errMsg)

func WithErrCode(code ErrCode) Option {
	return func(e *errMsg) {
		e.code = code
	}
}

func WithErrMsg(msg ErrZHMsg) Option {
	return func(e *errMsg) {
		e.msg = msg
	}
}

func NewErrMsg(err error, opts ...Option) ErrMsg {
	if err == nil {
		return nil
	}

	e := &errMsg{
		err:  err,
		code: Unknown,
		msg:  UnknownZHMsg,
	}
	for _, opt := range opts {
		opt(e)
	}

	if e.code == Unknown && e.msg != UnknownZHMsg {
		mutex.RLock()
		e.code = msgToCode[e.msg]
		mutex.RUnlock()
	}

	return e
}

func (e *errMsg) Error() string {
	return e.err.Error()
}

func (e *errMsg) Code() ErrCode {
	return e.code
}

func (e *errMsg) Message() ErrZHMsg {
	return e.msg
}
