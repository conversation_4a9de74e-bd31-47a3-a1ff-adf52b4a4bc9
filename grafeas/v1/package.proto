// Copyright 2019 The Grafeas Authors. All rights reserved.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//    http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package grafeas.v1;

import "google/api/field_behavior.proto";
import "grafeas/v1/common.proto";

option go_package = "google.golang.org/genproto/googleapis/grafeas/v1;grafeas";
option java_multiple_files = true;
option java_package = "io.grafeas.v1";
option objc_class_prefix = "GRA";

// Instruction set architectures supported by various package managers.
enum Architecture {
  // Unknown architecture.
  ARCHITECTURE_UNSPECIFIED = 0;
  // X86 architecture.
  X86 = 1;
  // X64 architecture.
  X64 = 2;
}

// This represents a particular channel of distribution for a given package.
// E.g., Debian's jessie-backports dpkg mirror.
message Distribution {
  // The cpe_uri in [CPE format](https://cpe.mitre.org/specification/)
  // denoting the package manager version distributing a package.
  string cpe_uri = 1 [(google.api.field_behavior) = REQUIRED];

  // The CPU architecture for which packages in this distribution channel were
  // built.
  Architecture architecture = 2;

  // The latest available version of this package in this distribution channel.
  Version latest_version = 3;

  // A freeform string denoting the maintainer of this package.
  string maintainer = 4;

  // The distribution channel-specific homepage for this package.
  string url = 5;

  // The distribution channel-specific description of this package.
  string description = 6;
}

// An occurrence of a particular package installation found within a system's
// filesystem. E.g., glibc was found in `/var/lib/dpkg/status`.
message Location {
  // Deprecated.
  // The CPE URI in [CPE format](https://cpe.mitre.org/specification/)
  string cpe_uri = 1;

  // Deprecated.
  // The version installed at this location.
  Version version = 2;

  // The path from which we gathered that this package/version is installed.
  string path = 3;
}

// PackageNote represents a particular package version.
message PackageNote {
  // The name of the package.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.field_behavior) = IMMUTABLE
  ];

  // Deprecated.
  // The various channels by which a package is distributed.
  repeated Distribution distribution = 10;

  // The type of package; whether native or non native (e.g., ruby gems,
  // node.js packages, etc.).
  string package_type = 11;

  // The cpe_uri in [CPE format](https://cpe.mitre.org/specification/)
  // denoting the package manager version distributing a package.
  // The cpe_uri will be blank for language packages.
  string cpe_uri = 12;

  // The CPU architecture for which packages in this distribution channel were
  // built. Architecture will be blank for language packages.
  Architecture architecture = 13;

  // The version of the package.
  Version version = 14;

  // A freeform text denoting the maintainer of this package.
  string maintainer = 15;

  // The homepage for this package.
  string url = 16;

  // The description of this package.
  string description = 17;

  // Licenses that have been declared by the authors of the package.
  License license = 18;

  // Hash value, typically a file digest, that allows unique
  // identification a specific package.
  repeated Digest digest = 19;
}

// Details on how a particular software package was installed on a system.
message PackageOccurrence {
  // The name of the installed package.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.field_behavior) = OUTPUT_ONLY
  ];

  // All of the places within the filesystem versions of this package
  // have been found.
  repeated Location location = 2;

  // The type of package; whether native or non native (e.g., ruby gems,
  // node.js packages, etc.).
  string package_type = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // The cpe_uri in [CPE format](https://cpe.mitre.org/specification/)
  // denoting the package manager version distributing a package.
  // The cpe_uri will be blank for language packages.
  string cpe_uri = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // The CPU architecture for which packages in this distribution channel were
  // built. Architecture will be blank for language packages.
  Architecture architecture = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Licenses that have been declared by the authors of the package.
  License license = 6;

  // The version of the package.
  Version version = 7 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Version contains structured information about the version of a package.
message Version {
  // Used to correct mistakes in the version numbering scheme.
  int32 epoch = 1;

  // Required only when version kind is NORMAL. The main part of the version
  // name.
  string name = 2;

  // The iteration of the package build from the above version.
  string revision = 3;

  // Whether this version is specifying part of an inclusive range. Grafeas
  // does not have the capability to specify version ranges; instead we have
  // fields that specify start version and end versions. At times this is
  // insufficient - we also need to specify whether the version is included in
  // the range or is excluded from the range. This boolean is expected to be set
  // to true when the version is included in a range.
  bool inclusive = 6;

  // Whether this is an ordinary package version or a sentinel MIN/MAX version.
  enum VersionKind {
    // Unknown.
    VERSION_KIND_UNSPECIFIED = 0;
    // A standard package version.
    NORMAL = 1;
    // A special version representing negative infinity.
    MINIMUM = 2;
    // A special version representing positive infinity.
    MAXIMUM = 3;
  }

  // Required. Distinguishes between sentinel MIN/MAX versions and normal
  // versions.
  VersionKind kind = 4;

  // Human readable version string. This string is of the form
  // <epoch>:<name>-<revision> and is only set when kind is NORMAL.
  string full_name = 5;
}
