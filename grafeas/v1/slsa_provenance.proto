// Copyright 2021 The Grafeas Authors. All rights reserved.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//    http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package grafeas.v1;

import "google/protobuf/any.proto";
import "google/protobuf/timestamp.proto";

option go_package = "google.golang.org/genproto/googleapis/grafeas/v1;grafeas";
option java_multiple_files = true;
option java_package = "io.grafeas.v1";
option objc_class_prefix = "GRA";
option java_outer_classname = "SlsaProvenanceProto";

message SlsaProvenance {
  // Steps taken to build the artifact.
  // For a TaskRun, typically each container corresponds to one step in the
  // recipe.
  message SlsaRecipe {
    // URI indicating what type of recipe was performed. It determines the
    // meaning of recipe.entryPoint, recipe.arguments, recipe.environment, and
    // materials.
    string type = 1;
    // Index in materials containing the recipe steps that are not implied by
    // recipe.type. For example, if the recipe type were "make", then this would
    // point to the source containing the Makefile, not the make program itself.
    // Set to -1 if the recipe doesn't come from a material, as zero is default
    // unset value for int64.
    int64 defined_in_material = 2;
    // String identifying the entry point into the build.
    // This is often a path to a configuration file and/or a target label within
    // that file. The syntax and meaning are defined by recipe.type. For
    // example, if the recipe type were "make", then this would reference the
    // directory in which to run make as well as which target to use.
    string entry_point = 3;
    // Collection of all external inputs that influenced the build on top of
    // recipe.definedInMaterial and recipe.entryPoint. For example, if the
    // recipe type were "make", then this might be the flags passed to make
    // aside from the target, which is captured in recipe.entryPoint. Depending
    // on the recipe Type, the structure may be different.
    google.protobuf.Any arguments = 4;
    // Any other builder-controlled inputs necessary for correctly evaluating
    // the recipe. Usually only needed for reproducing the build but not
    // evaluated as part of policy. Depending on the recipe Type, the structure
    // may be different.
    google.protobuf.Any environment = 5;
  }

  // Indicates that the builder claims certain fields in this message to be
  // complete.
  message SlsaCompleteness {
    // If true, the builder claims that recipe.arguments is complete, meaning
    // that all external inputs are properly captured in the recipe.
    bool arguments = 1;
    // If true, the builder claims that recipe.environment is claimed to be
    // complete.
    bool environment = 2;
    // If true, the builder claims that materials are complete, usually through
    // some controls to prevent network access. Sometimes called "hermetic".
    bool materials = 3;
  }

  // Other properties of the build.
  message SlsaMetadata {
    // Identifies the particular build invocation, which can be useful for
    // finding associated logs or other ad-hoc analysis. The value SHOULD be
    // globally unique, per in-toto Provenance spec.
    string build_invocation_id = 1;
    // The timestamp of when the build started.
    google.protobuf.Timestamp build_started_on = 2;
    // The timestamp of when the build completed.
    google.protobuf.Timestamp build_finished_on = 3;
    // Indicates that the builder claims certain fields in this message to be
    // complete.
    SlsaCompleteness completeness = 4;
    // If true, the builder claims that running the recipe on materials will
    // produce bit-for-bit identical output.
    bool reproducible = 5;
  }

  message SlsaBuilder {
    string id = 1;
  }

  message Material {
    string uri = 1;
    map<string, string> digest = 2;
  }

  SlsaBuilder builder = 1;  // required
  // Identifies the configuration used for the build.
  // When combined with materials, this SHOULD fully describe the build,
  // such that re-running this recipe results in bit-for-bit identical output
  // (if the build is reproducible).
  SlsaRecipe recipe = 2;  // required
  SlsaMetadata metadata = 3;
  // The collection of artifacts that influenced the build including sources,
  // dependencies, build tools, base images, and so on. This is considered to be
  // incomplete unless metadata.completeness.materials is true. Unset or null is
  // equivalent to empty.
  repeated Material materials = 4;
}
