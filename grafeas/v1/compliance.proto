// Copyright 2021 The Grafeas Authors. All rights reserved.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//    http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package grafeas.v1;

import "grafeas/v1/severity.proto";

option go_package = "google.golang.org/genproto/googleapis/grafeas/v1;grafeas";
option java_multiple_files = true;
option java_package = "io.grafeas.v1";
option objc_class_prefix = "GRA";

message ComplianceNote {
  // The title that identifies this compliance check.
  string title = 1;
  // A description about this compliance check.
  string description = 2;
  // The OS and config versions the benchmark applies to.
  repeated grafeas.v1.ComplianceVersion version = 3;
  // A rationale for the existence of this compliance check.
  string rationale = 4;
  // A description of remediation steps if the compliance check fails.
  string remediation = 5;
  // A compliance check that is a CIS benchmark.
  message CisBenchmark {
    int32 profile_level = 1;
    grafeas.v1.Severity severity = 2;
  }
  oneof compliance_type {
    CisBenchmark cis_benchmark = 6;
  }
  // Serialized scan instructions with a predefined format.
  bytes scan_instructions = 7;
  // Potential impact of the suggested remediation
  oneof potential_impact {
    string impact = 8;
  }
}

// Describes the CIS benchmark version that is applicable to a given OS and
// os version.
message ComplianceVersion {
  // The CPE URI (https://cpe.mitre.org/specification/) this benchmark is
  // applicable to.
  string cpe_uri = 1;
  // The name of the document that defines this benchmark, e.g. "CIS
  // Container-Optimized OS".
  string benchmark_document = 3;
  // The version of the benchmark. This is set to the version of the OS-specific
  // CIS document the benchmark is defined in.
  string version = 2;
}

// An indication that the compliance checks in the associated ComplianceNote
// were not satisfied for particular resources or a specified reason.
message ComplianceOccurrence {
  repeated NonCompliantFile non_compliant_files = 2;
  string non_compliance_reason = 3;
  // The OS and config version the benchmark was run on.
  grafeas.v1.ComplianceVersion version = 4;
}

// Details about files that caused a compliance check to fail.
message NonCompliantFile {
  // display_command is a single command that can be used to display a list of
  // non compliant files. When there is no such command, we can also iterate a
  // list of non compliant file using 'path'.

  // Empty if `display_command` is set.
  string path = 1;
  // Command to display the non-compliant files.
  string display_command = 2;
  // Explains why a file is non compliant for a CIS check.
  string reason = 3;
}
