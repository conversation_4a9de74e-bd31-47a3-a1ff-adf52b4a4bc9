{"swagger": "2.0", "info": {"title": "grafeas.proto", "version": "version not set"}, "tags": [{"name": "Grafeas"}, {"name": "Projects"}], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/v1/projects": {"get": {"summary": "Lists projects.", "operationId": "Projects_ListProjects", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/projectListProjectsResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "filter", "description": "The filter expression.", "in": "query", "required": false, "type": "string"}, {"name": "pageSize", "description": "Number of projects to return in the list.", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageToken", "description": "Token to provide to skip to a particular spot in the list.", "in": "query", "required": false, "type": "string"}], "tags": ["Projects"]}, "post": {"summary": "Creates a new project.", "operationId": "Projects_CreateProject", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/projectProject"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "description": "The project to create.", "in": "body", "required": true, "schema": {"$ref": "#/definitions/projectProject"}}], "tags": ["Projects"]}}, "/v1/{name_1}": {"get": {"summary": "Gets the specified occurrence.", "operationId": "Grafeas_GetOccurrence2", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/v1Occurrence"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "name_1", "description": "The name of the occurrence in the form of\n`projects/[PROJECT_ID]/occurrences/[OCCURRENCE_ID]`.", "in": "path", "required": true, "type": "string", "pattern": "projects/[^/]+/locations/[^/]+/occurrences/[^/]+"}], "tags": ["Grafeas"]}, "delete": {"summary": "Deletes the specified occurrence. For example, use this method to delete an\noccurrence when the occurrence is no longer applicable for the given\nresource.", "operationId": "Grafeas_DeleteOccurrence2", "responses": {"200": {"description": "A successful response.", "schema": {"properties": {}}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "name_1", "description": "The name of the occurrence in the form of\n`projects/[PROJECT_ID]/occurrences/[OCCURRENCE_ID]`.", "in": "path", "required": true, "type": "string", "pattern": "projects/[^/]+/locations/[^/]+/occurrences/[^/]+"}], "tags": ["Grafeas"]}, "patch": {"summary": "Updates the specified occurrence.", "operationId": "Grafeas_UpdateOccurrence2", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/v1Occurrence"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "name_1", "description": "The name of the occurrence in the form of\n`projects/[PROJECT_ID]/occurrences/[OCCURRENCE_ID]`.", "in": "path", "required": true, "type": "string", "pattern": "projects/[^/]+/locations/[^/]+/occurrences/[^/]+"}, {"name": "body", "description": "The updated occurrence.", "in": "body", "required": true, "schema": {"$ref": "#/definitions/v1Occurrence"}}, {"name": "updateMask", "description": "The fields to update.", "in": "query", "required": false, "type": "string"}], "tags": ["Grafeas"]}}, "/v1/{name_1}/notes": {"get": {"summary": "Gets the note attached to the specified occurrence. Consumer projects can\nuse this method to get a note that belongs to a provider project.", "operationId": "Grafeas_GetOccurrenceNote2", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/v1Note"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "name_1", "description": "The name of the occurrence in the form of\n`projects/[PROJECT_ID]/occurrences/[OCCURRENCE_ID]`.", "in": "path", "required": true, "type": "string", "pattern": "projects/[^/]+/locations/[^/]+/occurrences/[^/]+"}], "tags": ["Grafeas"]}}, "/v1/{name_1}/occurrences": {"get": {"summary": "Lists occurrences referencing the specified note. Provider projects can use\nthis method to get all occurrences across consumer projects referencing the\nspecified note.", "operationId": "Grafeas_ListNoteOccurrences2", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/v1ListNoteOccurrencesResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "name_1", "description": "The name of the note to list occurrences for in the form of\n`projects/[PROVIDER_ID]/notes/[NOTE_ID]`.", "in": "path", "required": true, "type": "string", "pattern": "projects/[^/]+/locations/[^/]+/notes/[^/]+"}, {"name": "filter", "description": "The filter expression.", "in": "query", "required": false, "type": "string"}, {"name": "pageSize", "description": "Number of occurrences to return in the list.", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageToken", "description": "Token to provide to skip to a particular spot in the list.", "in": "query", "required": false, "type": "string"}], "tags": ["Grafeas"]}}, "/v1/{name_2}": {"get": {"summary": "Gets the specified note.", "operationId": "Grafeas_GetNote", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/v1Note"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "name_2", "description": "The name of the note in the form of\n`projects/[PROVIDER_ID]/notes/[NOTE_ID]`.", "in": "path", "required": true, "type": "string", "pattern": "projects/[^/]+/notes/[^/]+"}], "tags": ["Grafeas"]}, "delete": {"summary": "Deletes the specified note.", "operationId": "Grafeas_DeleteNote", "responses": {"200": {"description": "A successful response.", "schema": {"properties": {}}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "name_2", "description": "The name of the note in the form of\n`projects/[PROVIDER_ID]/notes/[NOTE_ID]`.", "in": "path", "required": true, "type": "string", "pattern": "projects/[^/]+/notes/[^/]+"}], "tags": ["Grafeas"]}, "patch": {"summary": "Updates the specified note.", "operationId": "Grafeas_UpdateNote", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/v1Note"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "name_2", "description": "The name of the note in the form of\n`projects/[PROVIDER_ID]/notes/[NOTE_ID]`.", "in": "path", "required": true, "type": "string", "pattern": "projects/[^/]+/notes/[^/]+"}, {"name": "body", "description": "The updated note.", "in": "body", "required": true, "schema": {"$ref": "#/definitions/v1Note"}}, {"name": "updateMask", "description": "The fields to update.", "in": "query", "required": false, "type": "string"}], "tags": ["Grafeas"]}}, "/v1/{name_3}": {"get": {"summary": "Gets the specified note.", "operationId": "Grafeas_GetNote2", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/v1Note"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "name_3", "description": "The name of the note in the form of\n`projects/[PROVIDER_ID]/notes/[NOTE_ID]`.", "in": "path", "required": true, "type": "string", "pattern": "projects/[^/]+/locations/[^/]+/notes/[^/]+"}], "tags": ["Grafeas"]}, "delete": {"summary": "Deletes the specified note.", "operationId": "Grafeas_DeleteNote2", "responses": {"200": {"description": "A successful response.", "schema": {"properties": {}}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "name_3", "description": "The name of the note in the form of\n`projects/[PROVIDER_ID]/notes/[NOTE_ID]`.", "in": "path", "required": true, "type": "string", "pattern": "projects/[^/]+/locations/[^/]+/notes/[^/]+"}], "tags": ["Grafeas"]}, "patch": {"summary": "Updates the specified note.", "operationId": "Grafeas_UpdateNote2", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/v1Note"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "name_3", "description": "The name of the note in the form of\n`projects/[PROVIDER_ID]/notes/[NOTE_ID]`.", "in": "path", "required": true, "type": "string", "pattern": "projects/[^/]+/locations/[^/]+/notes/[^/]+"}, {"name": "body", "description": "The updated note.", "in": "body", "required": true, "schema": {"$ref": "#/definitions/v1Note"}}, {"name": "updateMask", "description": "The fields to update.", "in": "query", "required": false, "type": "string"}], "tags": ["Grafeas"]}}, "/v1/{name_4}": {"get": {"summary": "Gets the specified project.", "operationId": "Projects_GetProject", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/projectProject"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "name_4", "description": "The name of the project in the form of `projects/{PROJECT_ID}`.", "in": "path", "required": true, "type": "string", "pattern": "projects/[^/]+"}], "tags": ["Projects"]}, "delete": {"summary": "Deletes the specified project.", "operationId": "Projects_DeleteProject", "responses": {"200": {"description": "A successful response.", "schema": {"properties": {}}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "name_4", "description": "The name of the project in the form of `projects/{PROJECT_ID}`.", "in": "path", "required": true, "type": "string", "pattern": "projects/[^/]+"}], "tags": ["Projects"]}}, "/v1/{name}": {"get": {"summary": "Gets the specified occurrence.", "operationId": "Grafeas_GetOccurrence", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/v1Occurrence"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "name", "description": "The name of the occurrence in the form of\n`projects/[PROJECT_ID]/occurrences/[OCCURRENCE_ID]`.", "in": "path", "required": true, "type": "string", "pattern": "projects/[^/]+/occurrences/[^/]+"}], "tags": ["Grafeas"]}, "delete": {"summary": "Deletes the specified occurrence. For example, use this method to delete an\noccurrence when the occurrence is no longer applicable for the given\nresource.", "operationId": "Grafeas_DeleteOccurrence", "responses": {"200": {"description": "A successful response.", "schema": {"properties": {}}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "name", "description": "The name of the occurrence in the form of\n`projects/[PROJECT_ID]/occurrences/[OCCURRENCE_ID]`.", "in": "path", "required": true, "type": "string", "pattern": "projects/[^/]+/occurrences/[^/]+"}], "tags": ["Grafeas"]}, "patch": {"summary": "Updates the specified occurrence.", "operationId": "Grafeas_UpdateOccurrence", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/v1Occurrence"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "name", "description": "The name of the occurrence in the form of\n`projects/[PROJECT_ID]/occurrences/[OCCURRENCE_ID]`.", "in": "path", "required": true, "type": "string", "pattern": "projects/[^/]+/occurrences/[^/]+"}, {"name": "body", "description": "The updated occurrence.", "in": "body", "required": true, "schema": {"$ref": "#/definitions/v1Occurrence"}}, {"name": "updateMask", "description": "The fields to update.", "in": "query", "required": false, "type": "string"}], "tags": ["Grafeas"]}}, "/v1/{name}/notes": {"get": {"summary": "Gets the note attached to the specified occurrence. Consumer projects can\nuse this method to get a note that belongs to a provider project.", "operationId": "Grafeas_GetOccurrenceNote", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/v1Note"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "name", "description": "The name of the occurrence in the form of\n`projects/[PROJECT_ID]/occurrences/[OCCURRENCE_ID]`.", "in": "path", "required": true, "type": "string", "pattern": "projects/[^/]+/occurrences/[^/]+"}], "tags": ["Grafeas"]}}, "/v1/{name}/occurrences": {"get": {"summary": "Lists occurrences referencing the specified note. Provider projects can use\nthis method to get all occurrences across consumer projects referencing the\nspecified note.", "operationId": "Grafeas_ListNoteOccurrences", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/v1ListNoteOccurrencesResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "name", "description": "The name of the note to list occurrences for in the form of\n`projects/[PROVIDER_ID]/notes/[NOTE_ID]`.", "in": "path", "required": true, "type": "string", "pattern": "projects/[^/]+/notes/[^/]+"}, {"name": "filter", "description": "The filter expression.", "in": "query", "required": false, "type": "string"}, {"name": "pageSize", "description": "Number of occurrences to return in the list.", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageToken", "description": "Token to provide to skip to a particular spot in the list.", "in": "query", "required": false, "type": "string"}], "tags": ["Grafeas"]}}, "/v1/{parent_1}/notes": {"get": {"summary": "Lists notes for the specified project.", "operationId": "Grafeas_ListNotes2", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/v1ListNotesResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "parent_1", "description": "The name of the project to list notes for in the form of\n`projects/[PROJECT_ID]`.", "in": "path", "required": true, "type": "string", "pattern": "projects/[^/]+/locations/[^/]+"}, {"name": "filter", "description": "The filter expression.", "in": "query", "required": false, "type": "string"}, {"name": "pageSize", "description": "Number of notes to return in the list. Must be positive. Max allowed page\nsize is 1000. If not specified, page size defaults to 20.", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageToken", "description": "Token to provide to skip to a particular spot in the list.", "in": "query", "required": false, "type": "string"}], "tags": ["Grafeas"]}, "post": {"summary": "Creates a new note.", "operationId": "Grafeas_CreateNote2", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/v1Note"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "parent_1", "description": "The name of the project in the form of `projects/[PROJECT_ID]`, under which\nthe note is to be created.", "in": "path", "required": true, "type": "string", "pattern": "projects/[^/]+/locations/[^/]+"}, {"name": "body", "description": "The note to create.", "in": "body", "required": true, "schema": {"$ref": "#/definitions/v1Note"}}, {"name": "noteId", "description": "The ID to use for this note.", "in": "query", "required": true, "type": "string"}], "tags": ["Grafeas"]}}, "/v1/{parent_1}/notes:batchCreate": {"post": {"summary": "Creates new notes in batch.", "operationId": "Grafeas_BatchCreateNotes2", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/v1BatchCreateNotesResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "parent_1", "description": "The name of the project in the form of `projects/[PROJECT_ID]`, under which\nthe notes are to be created.", "in": "path", "required": true, "type": "string", "pattern": "projects/[^/]+/locations/[^/]+"}, {"name": "body", "in": "body", "required": true, "schema": {"type": "object", "properties": {"notes": {"type": "object", "additionalProperties": {"$ref": "#/definitions/v1Note"}, "description": "The notes to create. Max allowed length is 1000.", "required": ["notes"]}}, "description": "Request to create notes in batch.", "required": ["notes"]}}], "tags": ["Grafeas"]}}, "/v1/{parent_1}/occurrences": {"get": {"summary": "Lists occurrences for the specified project.", "operationId": "Grafeas_ListOccurrences2", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/v1ListOccurrencesResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "parent_1", "description": "The name of the project to list occurrences for in the form of\n`projects/[PROJECT_ID]`.", "in": "path", "required": true, "type": "string", "pattern": "projects/[^/]+/locations/[^/]+"}, {"name": "filter", "description": "The filter expression.", "in": "query", "required": false, "type": "string"}, {"name": "pageSize", "description": "Number of occurrences to return in the list. Must be positive. Max allowed\npage size is 1000. If not specified, page size defaults to 20.", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageToken", "description": "Token to provide to skip to a particular spot in the list.", "in": "query", "required": false, "type": "string"}], "tags": ["Grafeas"]}, "post": {"summary": "Creates a new occurrence.", "operationId": "Grafeas_CreateOccurrence2", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/v1Occurrence"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "parent_1", "description": "The name of the project in the form of `projects/[PROJECT_ID]`, under which\nthe occurrence is to be created.", "in": "path", "required": true, "type": "string", "pattern": "projects/[^/]+/locations/[^/]+"}, {"name": "body", "description": "The occurrence to create.", "in": "body", "required": true, "schema": {"$ref": "#/definitions/v1Occurrence"}}], "tags": ["Grafeas"]}}, "/v1/{parent_1}/occurrences:batchCreate": {"post": {"summary": "Creates new occurrences in batch.", "operationId": "Grafeas_BatchCreateOccurrences2", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/v1BatchCreateOccurrencesResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "parent_1", "description": "The name of the project in the form of `projects/[PROJECT_ID]`, under which\nthe occurrences are to be created.", "in": "path", "required": true, "type": "string", "pattern": "projects/[^/]+/locations/[^/]+"}, {"name": "body", "in": "body", "required": true, "schema": {"type": "object", "properties": {"occurrences": {"type": "array", "items": {"$ref": "#/definitions/v1Occurrence"}, "description": "The occurrences to create. Max allowed length is 1000.", "required": ["occurrences"]}}, "description": "Request to create occurrences in batch.", "required": ["occurrences"]}}], "tags": ["Grafeas"]}}, "/v1/{parent}/notes": {"get": {"summary": "Lists notes for the specified project.", "operationId": "Grafeas_ListNotes", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/v1ListNotesResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "parent", "description": "The name of the project to list notes for in the form of\n`projects/[PROJECT_ID]`.", "in": "path", "required": true, "type": "string", "pattern": "projects/[^/]+"}, {"name": "filter", "description": "The filter expression.", "in": "query", "required": false, "type": "string"}, {"name": "pageSize", "description": "Number of notes to return in the list. Must be positive. Max allowed page\nsize is 1000. If not specified, page size defaults to 20.", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageToken", "description": "Token to provide to skip to a particular spot in the list.", "in": "query", "required": false, "type": "string"}], "tags": ["Grafeas"]}, "post": {"summary": "Creates a new note.", "operationId": "Grafeas_CreateNote", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/v1Note"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "parent", "description": "The name of the project in the form of `projects/[PROJECT_ID]`, under which\nthe note is to be created.", "in": "path", "required": true, "type": "string", "pattern": "projects/[^/]+"}, {"name": "body", "description": "The note to create.", "in": "body", "required": true, "schema": {"$ref": "#/definitions/v1Note"}}, {"name": "noteId", "description": "The ID to use for this note.", "in": "query", "required": true, "type": "string"}], "tags": ["Grafeas"]}}, "/v1/{parent}/notes:batchCreate": {"post": {"summary": "Creates new notes in batch.", "operationId": "Grafeas_BatchCreateNotes", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/v1BatchCreateNotesResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "parent", "description": "The name of the project in the form of `projects/[PROJECT_ID]`, under which\nthe notes are to be created.", "in": "path", "required": true, "type": "string", "pattern": "projects/[^/]+"}, {"name": "body", "in": "body", "required": true, "schema": {"type": "object", "properties": {"notes": {"type": "object", "additionalProperties": {"$ref": "#/definitions/v1Note"}, "description": "The notes to create. Max allowed length is 1000.", "required": ["notes"]}}, "description": "Request to create notes in batch.", "required": ["notes"]}}], "tags": ["Grafeas"]}}, "/v1/{parent}/occurrences": {"get": {"summary": "Lists occurrences for the specified project.", "operationId": "Grafeas_ListOccurrences", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/v1ListOccurrencesResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "parent", "description": "The name of the project to list occurrences for in the form of\n`projects/[PROJECT_ID]`.", "in": "path", "required": true, "type": "string", "pattern": "projects/[^/]+"}, {"name": "filter", "description": "The filter expression.", "in": "query", "required": false, "type": "string"}, {"name": "pageSize", "description": "Number of occurrences to return in the list. Must be positive. Max allowed\npage size is 1000. If not specified, page size defaults to 20.", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageToken", "description": "Token to provide to skip to a particular spot in the list.", "in": "query", "required": false, "type": "string"}], "tags": ["Grafeas"]}, "post": {"summary": "Creates a new occurrence.", "operationId": "Grafeas_CreateOccurrence", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/v1Occurrence"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "parent", "description": "The name of the project in the form of `projects/[PROJECT_ID]`, under which\nthe occurrence is to be created.", "in": "path", "required": true, "type": "string", "pattern": "projects/[^/]+"}, {"name": "body", "description": "The occurrence to create.", "in": "body", "required": true, "schema": {"$ref": "#/definitions/v1Occurrence"}}], "tags": ["Grafeas"]}}, "/v1/{parent}/occurrences:batchCreate": {"post": {"summary": "Creates new occurrences in batch.", "operationId": "Grafeas_BatchCreateOccurrences", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/v1BatchCreateOccurrencesResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "parent", "description": "The name of the project in the form of `projects/[PROJECT_ID]`, under which\nthe occurrences are to be created.", "in": "path", "required": true, "type": "string", "pattern": "projects/[^/]+"}, {"name": "body", "in": "body", "required": true, "schema": {"type": "object", "properties": {"occurrences": {"type": "array", "items": {"$ref": "#/definitions/v1Occurrence"}, "description": "The occurrences to create. Max allowed length is 1000.", "required": ["occurrences"]}}, "description": "Request to create occurrences in batch.", "required": ["occurrences"]}}], "tags": ["Grafeas"]}}}, "definitions": {"AliasContextKind": {"type": "string", "enum": ["KIND_UNSPECIFIED", "FIXED", "MOVABLE", "OTHER"], "default": "KIND_UNSPECIFIED", "description": "The type of an alias.\n\n - KIND_UNSPECIFIED: Unknown.\n - FIXED: Git tag.\n - MOVABLE: Git branch.\n - OTHER: Used to specify non-standard aliases. For example, if a Git repo has a\nref named \"refs/foo/bar\"."}, "AssessmentJustification": {"type": "object", "properties": {"justificationType": {"$ref": "#/definitions/JustificationJustificationType", "description": "The justification type for this vulnerability."}, "details": {"type": "string", "description": "Additional details on why this justification was chosen."}}, "description": "Justification provides the justification when the state of the\nassessment if NOT_AFFECTED."}, "AssessmentRemediation": {"type": "object", "properties": {"remediationType": {"$ref": "#/definitions/RemediationRemediationType", "description": "The type of remediation that can be applied."}, "details": {"type": "string", "description": "Contains a comprehensive human-readable discussion of the remediation."}, "remediationUri": {"$ref": "#/definitions/v1RelatedUrl", "description": "Contains the URL where to obtain the remediation."}}, "description": "Specifies details on how to handle (and presumably, fix) a vulnerability."}, "AssessmentState": {"type": "string", "enum": ["STATE_UNSPECIFIED", "AFFECTED", "NOT_AFFECTED", "FIXED", "UNDER_INVESTIGATION"], "default": "STATE_UNSPECIFIED", "description": "Provides the state of this Vulnerability assessment.\n\n - STATE_UNSPECIFIED: No state is specified.\n - AFFECTED: This product is known to be affected by this vulnerability.\n - NOT_AFFECTED: This product is known to be not affected by this vulnerability.\n - FIXED: This product contains a fix for this vulnerability.\n - UNDER_INVESTIGATION: It is not known yet whether these versions are or are not affected\nby the vulnerability. However, it is still under investigation."}, "AttestationNoteHint": {"type": "object", "properties": {"humanReadableName": {"type": "string", "description": "Required. The human readable name of this attestation authority, for\nexample \"qa\"."}}, "description": "This submessage provides human-readable hints about the purpose of the\nauthority. Because the name of a note acts as its resource reference, it is\nimportant to disambiguate the canonical name of the Note (which might be a\nUUID for security purposes) from \"readable\" names more suitable for debug\noutput. Note that these hints should not be used to look up authorities in\nsecurity sensitive contexts, such as when looking up attestations to\nverify."}, "CVSSAuthentication": {"type": "string", "enum": ["AUTHENTICATION_UNSPECIFIED", "AUTHENTICATION_MULTIPLE", "AUTHENTICATION_SINGLE", "AUTHENTICATION_NONE"], "default": "AUTHENTICATION_UNSPECIFIED"}, "ComplianceNoteCisBenchmark": {"type": "object", "properties": {"profileLevel": {"type": "integer", "format": "int32"}, "severity": {"$ref": "#/definitions/v1Severity"}}, "description": "A compliance check that is a CIS benchmark."}, "DSSEAttestationNoteDSSEHint": {"type": "object", "properties": {"humanReadableName": {"type": "string", "description": "Required. The human readable name of this attestation authority, for example \"cloudbuild-prod\"."}}, "description": "This submessage provides human-readable hints about the purpose of the\nauthority. Because the name of a note acts as its resource reference, it is\nimportant to disambiguate the canonical name of the Note (which might be a\nUUID for security purposes) from \"readable\" names more suitable for debug\noutput. Note that these hints should not be used to look up authorities in\nsecurity sensitive contexts, such as when looking up attestations to\nverify."}, "DeploymentOccurrencePlatform": {"type": "string", "enum": ["PLATFORM_UNSPECIFIED", "GKE", "FLEX", "CUSTOM"], "default": "PLATFORM_UNSPECIFIED", "description": "Types of platforms.\n\n - PLATFORM_UNSPECIFIED: Unknown.\n - GKE: Google Container Engine.\n - FLEX: Google App Engine: Flexible Environment.\n - CUSTOM: Custom user-defined platform."}, "DiscoveryOccurrenceAnalysisCompleted": {"type": "object", "properties": {"analysisType": {"type": "array", "items": {"type": "string"}}}, "description": "Indicates which analysis completed successfully. Multiple types of\nanalysis can be performed on a single resource."}, "DiscoveryOccurrenceAnalysisStatus": {"type": "string", "enum": ["ANALYSIS_STATUS_UNSPECIFIED", "PENDING", "SCANNING", "FINISHED_SUCCESS", "COMPLETE", "FINISHED_FAILED", "FINISHED_UNSUPPORTED"], "default": "ANALYSIS_STATUS_UNSPECIFIED", "description": "Analysis status for a resource. Currently for initial analysis only (not\nupdated in continuous analysis).\n\n - ANALYSIS_STATUS_UNSPECIFIED: Unknown.\n - PENDING: Resource is known but no action has been taken yet.\n - SCANNING: Resource is being analyzed.\n - FINISHED_SUCCESS: Analysis has finished successfully.\n - COMPLETE: Analysis has completed.\n - FINISHED_FAILED: Analysis has finished unsuccessfully, the analysis itself is in a bad\nstate.\n - FINISHED_UNSUPPORTED: The resource is known not to be supported."}, "DiscoveryOccurrenceContinuousAnalysis": {"type": "string", "enum": ["CONTINUOUS_ANALYSIS_UNSPECIFIED", "ACTIVE", "INACTIVE"], "default": "CONTINUOUS_ANALYSIS_UNSPECIFIED", "description": "Whether the resource is continuously analyzed.\n\n - CONTINUOUS_ANALYSIS_UNSPECIFIED: Unknown.\n - ACTIVE: The resource is continuously analyzed.\n - INACTIVE: The resource is ignored for continuous analysis."}, "DiscoveryOccurrenceSBOMStatus": {"type": "object", "properties": {"sbomState": {"$ref": "#/definitions/SBOMStatusSBOMState", "description": "The progress of the SBOM generation."}, "error": {"type": "string", "description": "If there was an error generating an SBOM, this will indicate what that\nerror was."}}, "description": "The status of an SBOM generation."}, "DiscoveryOccurrenceVulnerabilityAttestation": {"type": "object", "properties": {"lastAttemptTime": {"type": "string", "format": "date-time", "description": "The last time we attempted to generate an attestation."}, "state": {"$ref": "#/definitions/VulnerabilityAttestationVulnerabilityAttestationState", "description": "The success/failure state of the latest attestation attempt."}, "error": {"type": "string", "description": "If failure, the error reason for why the attestation generation failed."}}, "description": "The status of an vulnerability attestation generation."}, "InTotoSlsaProvenanceV1BuildDefinition": {"type": "object", "properties": {"buildType": {"type": "string"}, "externalParameters": {"type": "object"}, "internalParameters": {"type": "object"}, "resolvedDependencies": {"type": "array", "items": {"$ref": "#/definitions/v1InTotoSlsaProvenanceV1ResourceDescriptor"}}}}, "InTotoSlsaProvenanceV1BuildMetadata": {"type": "object", "properties": {"invocationId": {"type": "string"}, "startedOn": {"type": "string", "format": "date-time"}, "finishedOn": {"type": "string", "format": "date-time"}}}, "InTotoSlsaProvenanceV1ProvenanceBuilder": {"type": "object", "properties": {"id": {"type": "string"}, "version": {"type": "object", "additionalProperties": {"type": "string"}}, "builderDependencies": {"type": "array", "items": {"$ref": "#/definitions/v1InTotoSlsaProvenanceV1ResourceDescriptor"}}}}, "InTotoSlsaProvenanceV1RunDetails": {"type": "object", "properties": {"builder": {"$ref": "#/definitions/InTotoSlsaProvenanceV1ProvenanceBuilder"}, "metadata": {"$ref": "#/definitions/InTotoSlsaProvenanceV1BuildMetadata"}, "byproducts": {"type": "array", "items": {"$ref": "#/definitions/v1InTotoSlsaProvenanceV1ResourceDescriptor"}}}}, "InTotoSlsaProvenanceV1SlsaProvenanceV1": {"type": "object", "properties": {"buildDefinition": {"$ref": "#/definitions/InTotoSlsaProvenanceV1BuildDefinition"}, "runDetails": {"$ref": "#/definitions/InTotoSlsaProvenanceV1RunDetails"}}, "description": "Keep in sync with schema at\nhttps://github.com/slsa-framework/slsa/blob/main/docs/provenance/schema/v1/provenance.proto\nBuilder renamed to ProvenanceBuilder because of Java conflicts."}, "JustificationJustificationType": {"type": "string", "enum": ["JUSTIFICATION_TYPE_UNSPECIFIED", "COMPONENT_NOT_PRESENT", "VULNERABLE_CODE_NOT_PRESENT", "VULNERABLE_CODE_NOT_IN_EXECUTE_PATH", "VULNERABLE_CODE_CANNOT_BE_CONTROLLED_BY_ADVERSARY", "INLINE_MITIGATIONS_ALREADY_EXIST"], "default": "JUSTIFICATION_TYPE_UNSPECIFIED", "description": "Provides the type of justification.\n\n - JUSTIFICATION_TYPE_UNSPECIFIED: JUSTIFICATION_TYPE_UNSPECIFIED.\n - COMPONENT_NOT_PRESENT: The vulnerable component is not present in the product.\n - VULNERABLE_CODE_NOT_PRESENT: The vulnerable code is not present. Typically this case\noccurs when source code is configured or built in a way that excludes\nthe vulnerable code.\n - VULNERABLE_CODE_NOT_IN_EXECUTE_PATH: The vulnerable code can not be executed.\nTypically this case occurs when the product includes the vulnerable\ncode but does not call or use the vulnerable code.\n - VULNERABLE_CODE_CANNOT_BE_CONTROLLED_BY_ADVERSARY: The vulnerable code cannot be controlled by an attacker to exploit\nthe vulnerability.\n - INLINE_MITIGATIONS_ALREADY_EXIST: The product includes built-in protections or features that prevent\nexploitation of the vulnerability. These built-in protections cannot\nbe subverted by the attacker and cannot be configured or disabled by\nthe user. These mitigations completely prevent exploitation based on\nknown attack vectors."}, "RemediationRemediationType": {"type": "string", "enum": ["REMEDIATION_TYPE_UNSPECIFIED", "MITIGATION", "NO_FIX_PLANNED", "NONE_AVAILABLE", "VENDOR_FIX", "WORKAROUND"], "default": "REMEDIATION_TYPE_UNSPECIFIED", "description": "The type of remediation that can be applied.\n\n - REMEDIATION_TYPE_UNSPECIFIED: No remediation type specified.\n - MITIGATION: A MITIGATION is available.\n - NO_FIX_PLANNED: No fix is planned.\n - NONE_AVAILABLE: Not available.\n - VENDOR_FIX: A vendor fix is available.\n - WORKAROUND: A workaround is available."}, "SBOMStatusSBOMState": {"type": "string", "enum": ["SBOM_STATE_UNSPECIFIED", "PENDING", "COMPLETE"], "default": "SBOM_STATE_UNSPECIFIED", "description": "An enum indicating the progress of the SBOM generation.\n\n - SBOM_STATE_UNSPECIFIED: Default unknown state.\n - PENDING: SBOM scanning is pending.\n - COMPLETE: SBOM scanning has completed."}, "SlsaProvenanceMaterial": {"type": "object", "properties": {"uri": {"type": "string"}, "digest": {"type": "object", "additionalProperties": {"type": "string"}}}}, "SlsaProvenanceSlsaRecipe": {"type": "object", "properties": {"type": {"type": "string", "description": "URI indicating what type of recipe was performed. It determines the meaning of recipe.entryPoint, recipe.arguments, recipe.environment, and materials."}, "definedInMaterial": {"type": "string", "format": "int64", "description": "Index in materials containing the recipe steps that are not implied by recipe.type.\nFor example, if the recipe type were \"make\", then this would point to the source containing the Makefile, not the make program itself.\nSet to -1 if the recipe doesn't come from a material, as zero is default unset value for int64."}, "entryPoint": {"type": "string", "description": "String identifying the entry point into the build.\nThis is often a path to a configuration file and/or a target label within that file.\nThe syntax and meaning are defined by recipe.type.\nFor example, if the recipe type were \"make\", then this would reference the directory in which to run make as well as which target to use."}, "arguments": {"$ref": "#/definitions/protobufAny", "description": "Collection of all external inputs that influenced the build on top of recipe.definedInMaterial and recipe.entryPoint.\nFor example, if the recipe type were \"make\", then this might be the flags passed to make aside from the target, which is captured in recipe.entryPoint.\nDepending on the recipe Type, the structure may be different."}, "environment": {"$ref": "#/definitions/protobufAny", "description": "Any other builder-controlled inputs necessary for correctly evaluating the recipe. Usually only needed for reproducing the build but not evaluated as part of policy.\nDepending on the recipe Type, the structure may be different."}}, "description": "Steps taken to build the artifact.\nFor a TaskRun, typically each container corresponds to one step in the recipe."}, "SlsaProvenanceZeroTwoSlsaConfigSource": {"type": "object", "properties": {"uri": {"type": "string"}, "digest": {"type": "object", "additionalProperties": {"type": "string"}}, "entryPoint": {"type": "string"}}, "description": "Describes where the config file that kicked off the build came from.\nThis is effectively a pointer to the source where buildConfig came from."}, "SlsaProvenanceZeroTwoSlsaInvocation": {"type": "object", "properties": {"configSource": {"$ref": "#/definitions/SlsaProvenanceZeroTwoSlsaConfigSource"}, "parameters": {"type": "object"}, "environment": {"type": "object"}}, "description": "Identifies the event that kicked off the build."}, "SlsaProvenanceZeroTwoSlsaMaterial": {"type": "object", "properties": {"uri": {"type": "string"}, "digest": {"type": "object", "additionalProperties": {"type": "string"}}}, "description": "The collection of artifacts that influenced the build including sources,\ndependencies, build tools, base images, and so on."}, "VersionVersionKind": {"type": "string", "enum": ["VERSION_KIND_UNSPECIFIED", "NORMAL", "MINIMUM", "MAXIMUM"], "default": "VERSION_KIND_UNSPECIFIED", "description": "Whether this is an ordinary package version or a sentinel MIN/MAX version.\n\n - VERSION_KIND_UNSPECIFIED: Unknown.\n - NORMAL: A standard package version.\n - MINIMUM: A special version representing negative infinity.\n - MAXIMUM: A special version representing positive infinity."}, "VulnerabilityAssessmentNoteAssessment": {"type": "object", "properties": {"cve": {"type": "string", "description": "Holds the MITRE standard Common Vulnerabilities and Exposures (CVE)\ntracking number for the vulnerability.\nDeprecated: Use vulnerability_id instead to denote CVEs."}, "vulnerabilityId": {"type": "string", "description": "The vulnerability identifier for this Assessment. Will hold one of\ncommon identifiers e.g. CVE, GHSA etc."}, "shortDescription": {"type": "string", "description": "A one sentence description of this Vex."}, "longDescription": {"type": "string", "description": "A detailed description of this Vex."}, "relatedUris": {"type": "array", "items": {"$ref": "#/definitions/v1RelatedUrl"}, "description": "Holds a list of references associated with this vulnerability item and\nassessment. These uris have additional information about the\nvulnerability and the assessment itself. E.g. Link to a document which\ndetails how this assessment concluded the state of this vulnerability."}, "state": {"$ref": "#/definitions/AssessmentState", "description": "Provides the state of this Vulnerability assessment."}, "impacts": {"type": "array", "items": {"type": "string"}, "description": "Contains information about the impact of this vulnerability,\nthis will change with time."}, "justification": {"$ref": "#/definitions/AssessmentJustification", "description": "Justification provides the justification when the state of the\nassessment if NOT_AFFECTED."}, "remediations": {"type": "array", "items": {"$ref": "#/definitions/AssessmentRemediation"}, "description": "Specifies details on how to handle (and presumably, fix) a vulnerability."}}, "description": "Assessment provides all information that is related to a single\nvulnerability for this product."}, "VulnerabilityAssessmentNoteProduct": {"type": "object", "properties": {"name": {"type": "string", "description": "Name of the product."}, "id": {"type": "string", "description": "Token that identifies a product so that it can be referred to from other\nparts in the document. There is no predefined format as long as it\nuniquely identifies a group in the context of the current document."}, "genericUri": {"type": "string", "description": "Contains a URI which is vendor-specific.\nExample: The artifact repository URL of an image."}}, "title": "Product contains information about a product and how to uniquely identify\nit.\n(-- api-linter: core::0123::resource-annotation=disabled\n    aip.dev/not-precedent: Product is not a separate resource. --)"}, "VulnerabilityAssessmentNotePublisher": {"type": "object", "properties": {"name": {"type": "string", "description": "Name of the publisher.\nExamples: 'Google', 'Google Cloud Platform'."}, "issuingAuthority": {"type": "string", "description": "Provides information about the authority of the issuing party to\nrelease the document, in particular, the party's constituency and\nresponsibilities or other obligations."}, "publisherNamespace": {"type": "string", "title": "The context or namespace.\nContains a URL which is under control of the issuing party and can\nbe used as a globally unique identifier for that issuing party.\nExample: https://csaf.io"}}, "title": "Publisher contains information about the publisher of\nthis Note.\n(-- api-linter: core::0123::resource-annotation=disabled\n    aip.dev/not-precedent: Publisher is not a separate resource. --)"}, "VulnerabilityAttestationVulnerabilityAttestationState": {"type": "string", "enum": ["VULNERABILITY_ATTESTATION_STATE_UNSPECIFIED", "SUCCESS", "FAILURE"], "default": "VULNERABILITY_ATTESTATION_STATE_UNSPECIFIED", "description": "An enum indicating the state of the attestation generation.\n\n - VULNERABILITY_ATTESTATION_STATE_UNSPECIFIED: Default unknown state.\n - SUCCESS: Attestation was successfully generated and stored.\n - FAILURE: Attestation was unsuccessfully generated and stored."}, "VulnerabilityNoteDetail": {"type": "object", "properties": {"severityName": {"type": "string", "description": "The distro assigned severity of this vulnerability."}, "description": {"type": "string", "description": "A vendor-specific description of this vulnerability."}, "packageType": {"type": "string", "description": "The type of package; whether native or non native (e.g., ruby gems,\nnode.js packages, etc.)."}, "affectedCpeUri": {"type": "string", "description": "Required. The [CPE URI](https://cpe.mitre.org/specification/) this\nvulnerability affects."}, "affectedPackage": {"type": "string", "description": "Required. The package this vulnerability affects."}, "affectedVersionStart": {"$ref": "#/definitions/v1Version", "description": "The version number at the start of an interval in which this vulnerability\nexists. A vulnerability can affect a package between version numbers\nthat are disjoint sets of intervals (example: [1.0.0-1.1.0], [2.4.6-2.4.8]\nand [4.5.6-4.6.8]) each of which will be represented in its own Detail.\nIf a specific affected version is provided by a vulnerability database,\naffected_version_start and affected_version_end will be the same in that\nDetail."}, "affectedVersionEnd": {"$ref": "#/definitions/v1Version", "description": "The version number at the end of an interval in which this vulnerability\nexists. A vulnerability can affect a package between version numbers\nthat are disjoint sets of intervals (example: [1.0.0-1.1.0], [2.4.6-2.4.8]\nand [4.5.6-4.6.8]) each of which will be represented in its own Detail.\nIf a specific affected version is provided by a vulnerability database,\naffected_version_start and affected_version_end will be the same in that\nDetail."}, "fixedCpeUri": {"type": "string", "description": "The distro recommended [CPE URI](https://cpe.mitre.org/specification/)\nto update to that contains a fix for this vulnerability. It is possible\nfor this to be different from the affected_cpe_uri."}, "fixedPackage": {"type": "string", "description": "The distro recommended package to update to that contains a fix for this\nvulnerability. It is possible for this to be different from the affected_package."}, "fixedVersion": {"$ref": "#/definitions/v1Version", "description": "The distro recommended version to update to that contains a\nfix for this vulnerability. Setting this to VersionKind.MAXIMUM means no\nsuch version is yet available."}, "isObsolete": {"type": "boolean", "description": "Whether this detail is obsolete. Occurrences are expected not to point to\nobsolete details."}, "sourceUpdateTime": {"type": "string", "format": "date-time", "description": "The time this information was last changed at the source. This is an\nupstream timestamp from the underlying information source - e.g. Ubuntu\nsecurity tracker."}, "source": {"type": "string", "description": "The source from which the information in this Detail was obtained."}, "vendor": {"type": "string", "description": "The name of the vendor of the product."}}, "description": "A detail for a distro and package affected by this vulnerability and its\nassociated fix (if one is available)."}, "VulnerabilityNoteWindowsDetail": {"type": "object", "properties": {"cpeUri": {"type": "string", "description": "Required. The [CPE URI](https://cpe.mitre.org/specification/) this\nvulnerability affects."}, "name": {"type": "string", "description": "Required. The name of this vulnerability."}, "description": {"type": "string", "description": "The description of this vulnerability."}, "fixingKbs": {"type": "array", "items": {"$ref": "#/definitions/WindowsDetailKnowledgeBase"}, "description": "Required. The names of the KBs which have hotfixes to mitigate this\nvulnerability. Note that there may be multiple hotfixes (and thus\nmultiple KBs) that mitigate a given vulnerability. Currently any listed\nKBs presence is considered a fix."}}}, "VulnerabilityOccurrencePackageIssue": {"type": "object", "properties": {"affectedCpeUri": {"type": "string", "description": "Required. The [CPE URI](https://cpe.mitre.org/specification/) this\nvulnerability was found in."}, "affectedPackage": {"type": "string", "description": "Required. The package this vulnerability was found in."}, "affectedVersion": {"$ref": "#/definitions/v1Version", "description": "Required. The version of the package that is installed on the resource\naffected by this vulnerability."}, "fixedCpeUri": {"type": "string", "description": "The [CPE URI](https://cpe.mitre.org/specification/) this vulnerability was\nfixed in. It is possible for this to be different from\nthe affected_cpe_uri."}, "fixedPackage": {"type": "string", "description": "The package this vulnerability was fixed in. It is possible for this to be\ndifferent from the affected_package."}, "fixedVersion": {"$ref": "#/definitions/v1Version", "description": "Required. The version of the package this vulnerability was fixed in.\nSetting this to VersionKind.MAXIMUM means no fix is yet available."}, "fixAvailable": {"type": "boolean", "description": "Output only. Whether a fix is available for this package.", "readOnly": true}, "packageType": {"type": "string", "description": "The type of package (e.g. OS, MAVEN, GO)."}, "effectiveSeverity": {"$ref": "#/definitions/v1Severity", "description": "The distro or language system assigned severity for this vulnerability\nwhen that is available and note provider assigned severity when it is not\navailable."}, "fileLocation": {"type": "array", "items": {"$ref": "#/definitions/v1FileLocation"}, "description": "The location at which this package was found."}}, "description": "A detail for a distro and package this vulnerability occurrence was found in\nand its associated fix (if one is available)."}, "VulnerabilityOccurrenceVexAssessment": {"type": "object", "properties": {"cve": {"type": "string", "description": "Holds the MITRE standard Common Vulnerabilities and Exposures (CVE)\ntracking number for the vulnerability.\nDeprecated: Use vulnerability_id instead to denote CVEs."}, "vulnerabilityId": {"type": "string", "description": "The vulnerability identifier for this Assessment. Will hold one of\ncommon identifiers e.g. CVE, GHSA etc."}, "relatedUris": {"type": "array", "items": {"$ref": "#/definitions/v1RelatedUrl"}, "description": "Holds a list of references associated with this vulnerability item and\nassessment."}, "noteName": {"type": "string", "title": "The VulnerabilityAssessment note from which this VexAssessment was\ngenerated.\nThis will be of the form: `projects/[PROJECT_ID]/notes/[NOTE_ID]`.\n(-- api-linter: core::0122::name-suffix=disabled\n    aip.dev/not-precedent: The suffix is kept for consistency. --)"}, "state": {"$ref": "#/definitions/AssessmentState", "description": "Provides the state of this Vulnerability assessment."}, "impacts": {"type": "array", "items": {"type": "string"}, "description": "Contains information about the impact of this vulnerability,\nthis will change with time."}, "remediations": {"type": "array", "items": {"$ref": "#/definitions/AssessmentRemediation"}, "description": "Specifies details on how to handle (and presumably, fix) a vulnerability."}, "justification": {"$ref": "#/definitions/AssessmentJustification", "description": "Justification provides the justification when the state of the\nassessment if NOT_AFFECTED."}}, "description": "VexAssessment provides all publisher provided Vex information that is\nrelated to this vulnerability."}, "WindowsDetailKnowledgeBase": {"type": "object", "properties": {"name": {"type": "string", "description": "The KB name (generally of the form KB[0-9]+ (e.g., KB123456))."}, "url": {"type": "string", "description": "A link to the KB in the [Windows update catalog]\n(https://www.catalog.update.microsoft.com/)."}}}, "WindowsUpdateCategory": {"type": "object", "properties": {"categoryId": {"type": "string", "description": "The identifier of the category."}, "name": {"type": "string", "description": "The localized name of the category."}}, "description": "The category to which the update belongs."}, "WindowsUpdateIdentity": {"type": "object", "properties": {"updateId": {"type": "string", "description": "The revision independent identifier of the update."}, "revision": {"type": "integer", "format": "int32", "description": "The revision number of the update."}}, "description": "The unique identifier of the update."}, "grafeasv1Location": {"type": "object", "properties": {"cpeUri": {"type": "string", "title": "Deprecated.\nThe CPE URI in [CPE format](https://cpe.mitre.org/specification/)"}, "version": {"$ref": "#/definitions/v1Version", "description": "Deprecated.\nThe version installed at this location."}, "path": {"type": "string", "description": "The path from which we gathered that this package/version is installed."}}, "description": "An occurrence of a particular package installation found within a system's\nfilesystem. E.g., glibc was found in `/var/lib/dpkg/status`."}, "projectListProjectsResponse": {"type": "object", "properties": {"projects": {"type": "array", "items": {"$ref": "#/definitions/projectProject"}, "description": "The projects requested."}, "nextPageToken": {"type": "string", "description": "The next pagination token in the list response. It should be used as\n`page_token` for the following request. An empty value means no more\nresults."}}, "description": "Response for listing projects."}, "projectProject": {"type": "object", "properties": {"name": {"type": "string", "description": "The name of the project in the form of `projects/{PROJECT_ID}`."}}, "description": "Describes a Grafeas project."}, "protobufAny": {"type": "object", "properties": {"@type": {"type": "string", "description": "A URL/resource name that uniquely identifies the type of the serialized\nprotocol buffer message. This string must contain at least\none \"/\" character. The last segment of the URL's path must represent\nthe fully qualified name of the type (as in\n`path/google.protobuf.Duration`). The name should be in a canonical form\n(e.g., leading \".\" is not accepted).\n\nIn practice, teams usually precompile into the binary all types that they\nexpect it to use in the context of Any. However, for URLs which use the\nscheme `http`, `https`, or no scheme, one can optionally set up a type\nserver that maps type URLs to message definitions as follows:\n\n* If no scheme is provided, `https` is assumed.\n* An HTTP GET on the URL must yield a [google.protobuf.Type][]\n  value in binary format, or produce an error.\n* Applications are allowed to cache lookup results based on the\n  URL, or have them precompiled into a binary to avoid any\n  lookup. Therefore, binary compatibility needs to be preserved\n  on changes to types. (Use versioned type names to manage\n  breaking changes.)\n\nNote: this functionality is not currently available in the official\nprotobuf release, and it is not used for type URLs beginning with\ntype.googleapis.com.\n\nSchemes other than `http`, `https` (or the empty scheme) might be\nused with implementation specific semantics."}}, "additionalProperties": {}, "description": "`Any` contains an arbitrary serialized protocol buffer message along with a\nURL that describes the type of the serialized message.\n\nProtobuf library provides support to pack/unpack Any values in the form\nof utility functions or additional generated methods of the Any type.\n\nExample 1: Pack and unpack a message in C++.\n\n    Foo foo = ...;\n    Any any;\n    any.PackFrom(foo);\n    ...\n    if (any.UnpackTo(&foo)) {\n      ...\n    }\n\nExample 2: Pack and unpack a message in Java.\n\n    Foo foo = ...;\n    Any any = Any.pack(foo);\n    ...\n    if (any.is(Foo.class)) {\n      foo = any.unpack(Foo.class);\n    }\n\n Example 3: Pack and unpack a message in Python.\n\n    foo = Foo(...)\n    any = Any()\n    any.Pack(foo)\n    ...\n    if any.Is(Foo.DESCRIPTOR):\n      any.Unpack(foo)\n      ...\n\n Example 4: Pack and unpack a message in Go\n\n     foo := &pb.Foo{...}\n     any, err := ptypes.MarshalAny(foo)\n     ...\n     foo := &pb.Foo{}\n     if err := ptypes.UnmarshalAny(any, foo); err != nil {\n       ...\n     }\n\nThe pack methods provided by protobuf library will by default use\n'type.googleapis.com/full.type.name' as the type URL and the unpack\nmethods only use the fully qualified type name after the last '/'\nin the type URL, for example \"foo.bar.com/x/y.z\" will yield type\nname \"y.z\".\n\n\nJSON\n====\nThe JSON representation of an `Any` value uses the regular\nrepresentation of the deserialized, embedded message, with an\nadditional field `@type` which contains the type URL. Example:\n\n    package google.profile;\n    message Person {\n      string first_name = 1;\n      string last_name = 2;\n    }\n\n    {\n      \"@type\": \"type.googleapis.com/google.profile.Person\",\n      \"firstName\": <string>,\n      \"lastName\": <string>\n    }\n\nIf the embedded message type is well-known and has a custom JSON\nrepresentation, that representation will be embedded adding a field\n`value` which holds the custom JSON in addition to the `@type`\nfield. Example (for message [google.protobuf.Duration][]):\n\n    {\n      \"@type\": \"type.googleapis.com/google.protobuf.Duration\",\n      \"value\": \"1.212s\"\n    }"}, "protobufNullValue": {"type": "string", "enum": ["NULL_VALUE"], "default": "NULL_VALUE", "description": "`NullValue` is a singleton enumeration to represent the null value for the\n`Value` type union.\n\n The JSON representation for `NullValue` is JSON `null`.\n\n - NULL_VALUE: Null value."}, "rpcStatus": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32", "description": "The status code, which should be an enum value of [google.rpc.Code][google.rpc.Code]."}, "message": {"type": "string", "description": "A developer-facing error message, which should be in English. Any\nuser-facing error message should be localized and sent in the\n[google.rpc.Status.details][google.rpc.Status.details] field, or localized by the client."}, "details": {"type": "array", "items": {"$ref": "#/definitions/protobufAny"}, "description": "A list of messages that carry the error details.  There is a common set of\nmessage types for APIs to use."}}, "description": "- Simple to use and understand for most users\n- Flexible enough to meet unexpected needs\n\n# Overview\n\nThe `Status` message contains three pieces of data: error code, error message,\nand error details. The error code should be an enum value of\n[google.rpc.Code][google.rpc.Code], but it may accept additional error codes if needed.  The\nerror message should be a developer-facing English message that helps\ndevelopers *understand* and *resolve* the error. If a localized user-facing\nerror message is needed, put the localized message in the error details or\nlocalize it in the client. The optional error details may contain arbitrary\ninformation about the error. There is a predefined set of error detail types\nin the package `google.rpc` that can be used for common error conditions.\n\n# Language mapping\n\nThe `Status` message is the logical representation of the error model, but it\nis not necessarily the actual wire format. When the `Status` message is\nexposed in different client libraries and different wire protocols, it can be\nmapped differently. For example, it will likely be mapped to some exceptions\nin Java, but more likely mapped to some error codes in C.\n\n# Other uses\n\nThe error model and the `Status` message can be used in a variety of\nenvironments, either with or without APIs, to provide a\nconsistent developer experience across different environments.\n\nExample uses of this error model include:\n\n- Partial errors. If a service needs to return partial errors to the client,\n    it may embed the `Status` in the normal response to indicate the partial\n    errors.\n\n- Workflow errors. A typical workflow has multiple steps. Each step may\n    have a `Status` message for error reporting.\n\n- Batch operations. If a client uses batch request and batch response, the\n    `Status` message should be used directly inside batch response, one for\n    each error sub-response.\n\n- Asynchronous operations. If an API call embeds asynchronous operation\n    results in its response, the status of those operations should be\n    represented directly using the `Status` message.\n\n- Logging. If some API errors are stored in logs, the message `Status` could\n    be used directly after any stripping needed for security/privacy reasons.", "title": "The `Status` type defines a logical error model that is suitable for different\nprogramming environments, including REST APIs and RPC APIs. It is used by\n[gRPC](https://github.com/grpc). The error model is designed to be:"}, "v1AliasContext": {"type": "object", "properties": {"kind": {"$ref": "#/definitions/AliasContextKind", "description": "The alias kind."}, "name": {"type": "string", "description": "The alias name."}}, "description": "An alias to a repo revision."}, "v1Architecture": {"type": "string", "enum": ["ARCHITECTURE_UNSPECIFIED", "X86", "X64"], "default": "ARCHITECTURE_UNSPECIFIED", "description": "Instruction set architectures supported by various package managers.\n\n - ARCHITECTURE_UNSPECIFIED: Unknown architecture.\n - X86: X86 architecture.\n - X64: X64 architecture."}, "v1Artifact": {"type": "object", "properties": {"checksum": {"type": "string", "description": "Hash or checksum value of a binary, or Docker Registry 2.0 digest of a\ncontainer."}, "id": {"type": "string", "description": "Artifact ID, if any; for container images, this will be a URL by digest\nlike `gcr.io/projectID/imagename@sha256:123456`."}, "names": {"type": "array", "items": {"type": "string"}, "description": "Related artifact names. This may be the path to a binary or jar file, or in\nthe case of a container build, the name used to push the container image to\nGoogle Container Registry, as presented to `docker push`. Note that a\nsingle Artifact ID can have multiple names, for example if two tags are\napplied to one image."}}, "description": "Artifact describes a build product."}, "v1AttestationNote": {"type": "object", "properties": {"hint": {"$ref": "#/definitions/AttestationNoteHint", "description": "Hint hints at the purpose of the attestation authority."}}, "description": "Note kind that represents a logical attestation \"role\" or \"authority\". For\nexample, an organization might have one `Authority` for \"QA\" and one for\n\"build\". This note is intended to act strictly as a grouping mechanism for\nthe attached occurrences (Attestations). This grouping mechanism also\nprovides a security boundary, since IAM ACLs gate the ability for a principle\nto attach an occurrence to a given note. It also provides a single point of\nlookup to find all attached attestation occurrences, even if they don't all\nlive in the same project."}, "v1AttestationOccurrence": {"type": "object", "properties": {"serializedPayload": {"type": "string", "format": "byte", "description": "Required. The serialized payload that is verified by one or more `signatures`."}, "signatures": {"type": "array", "items": {"$ref": "#/definitions/v1Signature"}, "description": "One or more signatures over `serialized_payload`.  Verifier implementations\nshould consider this attestation message verified if at least one\n`signature` verifies `serialized_payload`.  See `Signature` in common.proto\nfor more details on signature structure and verification."}, "jwts": {"type": "array", "items": {"$ref": "#/definitions/v1Jwt"}, "description": "One or more JWTs encoding a self-contained attestation.\nEach JWT encodes the payload that it verifies within the JWT itself. \nVerifier implementation SHOULD ignore the `serialized_payload` field \nwhen verifying these JWTs.\nIf only JWTs are present on this AttestationOccurrence, then the\n`serialized_payload` SHOULD be left empty.\nEach JWT SHOULD encode a claim specific to the `resource_uri` of this\nOccurrence, but this is not validated by Grafeas metadata API\nimplementations.  The JWT itself is opaque to Grafeas."}}, "description": "Occurrence that represents a single \"attestation\". The authenticity of an\nattestation can be verified using the attached signature. If the verifier\ntrusts the public key of the signer, then verifying the signature is\nsufficient to establish trust. In this circumstance, the authority to which\nthis attestation is attached is primarily useful for lookup (how to find\nthis attestation if you already know the authority and artifact to be\nverified) and intent (for which authority this attestation was intended to\nsign."}, "v1BatchCreateNotesResponse": {"type": "object", "properties": {"notes": {"type": "array", "items": {"$ref": "#/definitions/v1Note"}, "description": "The notes that were created."}}, "description": "Response for creating notes in batch."}, "v1BatchCreateOccurrencesResponse": {"type": "object", "properties": {"occurrences": {"type": "array", "items": {"$ref": "#/definitions/v1Occurrence"}, "description": "The occurrences that were created."}}, "description": "Response for creating occurrences in batch."}, "v1BuildNote": {"type": "object", "properties": {"builderVersion": {"type": "string", "description": "Required. Immutable. Version of the builder which produced this build."}}, "description": "Note holding the version of the provider's builder and the signature of the\nprovenance message in the build details occurrence."}, "v1BuildOccurrence": {"type": "object", "properties": {"provenance": {"$ref": "#/definitions/v1BuildProvenance", "description": "The actual provenance for the build."}, "provenanceBytes": {"type": "string", "description": "Serialized JSON representation of the provenance, used in generating the\nbuild signature in the corresponding build note. After verifying the\nsignature, `provenance_bytes` can be unmarshalled and compared to the\nprovenance to confirm that it is unchanged. A base64-encoded string\nrepresentation of the provenance bytes is used for the signature in order\nto interoperate with openssl which expects this format for signature\nverification.\n\nThe serialized form is captured both to avoid ambiguity in how the\nprovenance is marshalled to json as well to prevent incompatibilities with\nfuture changes."}, "intotoProvenance": {"$ref": "#/definitions/v1InTotoProvenance", "description": "Deprecated. See InTotoStatement for the replacement.\nIn-toto Provenance representation as defined in spec."}, "intotoStatement": {"$ref": "#/definitions/v1InTotoStatement", "description": "In-toto Statement representation as defined in spec.\nThe intoto_statement can contain any type of provenance. The serialized\npayload of the statement can be stored and signed in the Occurrence's\nenvelope."}, "inTotoSlsaProvenanceV1": {"$ref": "#/definitions/v1InTotoSlsaProvenanceV1", "description": "In-Toto Slsa Provenance V1 represents a slsa provenance meeting the slsa\nspec, wrapped in an in-toto statement. This allows for direct\njsonification of a to-spec in-toto slsa statement with a to-spec\nslsa provenance."}}, "description": "Details of a build occurrence."}, "v1BuildProvenance": {"type": "object", "properties": {"id": {"type": "string", "description": "Required. Unique identifier of the build."}, "projectId": {"type": "string", "description": "ID of the project."}, "commands": {"type": "array", "items": {"$ref": "#/definitions/v1Command"}, "description": "Commands requested by the build."}, "builtArtifacts": {"type": "array", "items": {"$ref": "#/definitions/v1Artifact"}, "description": "Output of the build."}, "createTime": {"type": "string", "format": "date-time", "description": "Time at which the build was created."}, "startTime": {"type": "string", "format": "date-time", "description": "Time at which execution of the build was started."}, "endTime": {"type": "string", "format": "date-time", "description": "Time at which execution of the build was finished."}, "creator": {"type": "string", "description": "E-mail address of the user who initiated this build. Note that this was the\nuser's e-mail address at the time the build was initiated; this address may\nnot represent the same end-user for all time."}, "logsUri": {"type": "string", "description": "URI where any logs for this provenance were written."}, "sourceProvenance": {"$ref": "#/definitions/v1Source", "description": "Details of the Source input to the build."}, "triggerId": {"type": "string", "description": "Trigger identifier if the build was triggered automatically; empty if not."}, "buildOptions": {"type": "object", "additionalProperties": {"type": "string"}, "description": "Special options applied to this build. This is a catch-all field where\nbuild providers can enter any desired additional details."}, "builderVersion": {"type": "string", "description": "Version string of the builder at the time this build was executed."}}, "description": "Provenance of a build. Contains all information needed to verify the full\ndetails about the build from source to completion."}, "v1BuilderConfig": {"type": "object", "properties": {"id": {"type": "string"}}}, "v1CVSS": {"type": "object", "properties": {"baseScore": {"type": "number", "format": "float", "description": "The base score is a function of the base metric scores."}, "exploitabilityScore": {"type": "number", "format": "float"}, "impactScore": {"type": "number", "format": "float"}, "attackVector": {"$ref": "#/definitions/v1CVSSAttackVector", "description": "Base Metrics\nRepresents the intrinsic characteristics of a vulnerability that are\nconstant over time and across user environments."}, "attackComplexity": {"$ref": "#/definitions/v1CVSSAttackComplexity"}, "authentication": {"$ref": "#/definitions/CVSSAuthentication"}, "privilegesRequired": {"$ref": "#/definitions/v1CVSSPrivilegesRequired"}, "userInteraction": {"$ref": "#/definitions/v1CVSSUserInteraction"}, "scope": {"$ref": "#/definitions/v1CVSSScope"}, "confidentialityImpact": {"$ref": "#/definitions/v1CVSSImpact"}, "integrityImpact": {"$ref": "#/definitions/v1CVSSImpact"}, "availabilityImpact": {"$ref": "#/definitions/v1CVSSImpact"}}, "description": "Common Vulnerability Scoring System.\nFor details, see https://www.first.org/cvss/specification-document\nThis is a message we will try to use for storing various versions of CVSS\nrather than making a separate proto for storing a specific version."}, "v1CVSSAttackComplexity": {"type": "string", "enum": ["ATTACK_COMPLEXITY_UNSPECIFIED", "ATTACK_COMPLEXITY_LOW", "ATTACK_COMPLEXITY_HIGH", "ATTACK_COMPLEXITY_MEDIUM"], "default": "ATTACK_COMPLEXITY_UNSPECIFIED"}, "v1CVSSAttackVector": {"type": "string", "enum": ["ATTACK_VECTOR_UNSPECIFIED", "ATTACK_VECTOR_NETWORK", "ATTACK_VECTOR_ADJACENT", "ATTACK_VECTOR_LOCAL", "ATTACK_VECTOR_PHYSICAL"], "default": "ATTACK_VECTOR_UNSPECIFIED"}, "v1CVSSImpact": {"type": "string", "enum": ["IMPACT_UNSPECIFIED", "IMPACT_HIGH", "IMPACT_LOW", "IMPACT_NONE", "IMPACT_PARTIAL", "IMPACT_COMPLETE"], "default": "IMPACT_UNSPECIFIED"}, "v1CVSSPrivilegesRequired": {"type": "string", "enum": ["PRIVILEGES_REQUIRED_UNSPECIFIED", "PRIVILEGES_REQUIRED_NONE", "PRIVILEGES_REQUIRED_LOW", "PRIVILEGES_REQUIRED_HIGH"], "default": "PRIVILEGES_REQUIRED_UNSPECIFIED"}, "v1CVSSScope": {"type": "string", "enum": ["SCOPE_UNSPECIFIED", "SCOPE_UNCHANGED", "SCOPE_CHANGED"], "default": "SCOPE_UNSPECIFIED"}, "v1CVSSUserInteraction": {"type": "string", "enum": ["USER_INTERACTION_UNSPECIFIED", "USER_INTERACTION_NONE", "USER_INTERACTION_REQUIRED"], "default": "USER_INTERACTION_UNSPECIFIED"}, "v1CVSSVersion": {"type": "string", "enum": ["CVSS_VERSION_UNSPECIFIED", "CVSS_VERSION_2", "CVSS_VERSION_3"], "default": "CVSS_VERSION_UNSPECIFIED", "description": "CVSS Version."}, "v1CVSSv3": {"type": "object", "properties": {"baseScore": {"type": "number", "format": "float", "description": "The base score is a function of the base metric scores."}, "exploitabilityScore": {"type": "number", "format": "float"}, "impactScore": {"type": "number", "format": "float"}, "attackVector": {"$ref": "#/definitions/v1CVSSv3AttackVector", "description": "Base Metrics\nRepresents the intrinsic characteristics of a vulnerability that are\nconstant over time and across user environments."}, "attackComplexity": {"$ref": "#/definitions/v1CVSSv3AttackComplexity"}, "privilegesRequired": {"$ref": "#/definitions/v1CVSSv3PrivilegesRequired"}, "userInteraction": {"$ref": "#/definitions/v1CVSSv3UserInteraction"}, "scope": {"$ref": "#/definitions/v1CVSSv3Scope"}, "confidentialityImpact": {"$ref": "#/definitions/v1CVSSv3Impact"}, "integrityImpact": {"$ref": "#/definitions/v1CVSSv3Impact"}, "availabilityImpact": {"$ref": "#/definitions/v1CVSSv3Impact"}}, "title": "Common Vulnerability Scoring System version 3.\nFor details, see https://www.first.org/cvss/specification-document"}, "v1CVSSv3AttackComplexity": {"type": "string", "enum": ["ATTACK_COMPLEXITY_UNSPECIFIED", "ATTACK_COMPLEXITY_LOW", "ATTACK_COMPLEXITY_HIGH"], "default": "ATTACK_COMPLEXITY_UNSPECIFIED"}, "v1CVSSv3AttackVector": {"type": "string", "enum": ["ATTACK_VECTOR_UNSPECIFIED", "ATTACK_VECTOR_NETWORK", "ATTACK_VECTOR_ADJACENT", "ATTACK_VECTOR_LOCAL", "ATTACK_VECTOR_PHYSICAL"], "default": "ATTACK_VECTOR_UNSPECIFIED"}, "v1CVSSv3Impact": {"type": "string", "enum": ["IMPACT_UNSPECIFIED", "IMPACT_HIGH", "IMPACT_LOW", "IMPACT_NONE"], "default": "IMPACT_UNSPECIFIED"}, "v1CVSSv3PrivilegesRequired": {"type": "string", "enum": ["PRIVILEGES_REQUIRED_UNSPECIFIED", "PRIVILEGES_REQUIRED_NONE", "PRIVILEGES_REQUIRED_LOW", "PRIVILEGES_REQUIRED_HIGH"], "default": "PRIVILEGES_REQUIRED_UNSPECIFIED"}, "v1CVSSv3Scope": {"type": "string", "enum": ["SCOPE_UNSPECIFIED", "SCOPE_UNCHANGED", "SCOPE_CHANGED"], "default": "SCOPE_UNSPECIFIED"}, "v1CVSSv3UserInteraction": {"type": "string", "enum": ["USER_INTERACTION_UNSPECIFIED", "USER_INTERACTION_NONE", "USER_INTERACTION_REQUIRED"], "default": "USER_INTERACTION_UNSPECIFIED"}, "v1CloudRepoSourceContext": {"type": "object", "properties": {"repoId": {"$ref": "#/definitions/v1RepoId", "description": "The ID of the repo."}, "revisionId": {"type": "string", "description": "A revision ID."}, "aliasContext": {"$ref": "#/definitions/v1AliasContext", "description": "An alias, which may be a branch or tag."}}, "description": "A CloudRepoSourceContext denotes a particular revision in a Google Cloud\nSource Repo."}, "v1Command": {"type": "object", "properties": {"name": {"type": "string", "description": "Required. Name of the command, as presented on the command line, or if the\ncommand is packaged as a Docker container, as presented to `docker pull`."}, "env": {"type": "array", "items": {"type": "string"}, "description": "Environment variables set before running this command."}, "args": {"type": "array", "items": {"type": "string"}, "description": "Command-line arguments used when executing this command."}, "dir": {"type": "string", "description": "Working directory (relative to project source root) used when running this\ncommand."}, "id": {"type": "string", "description": "Optional unique identifier for this command, used in wait_for to reference\nthis command as a dependency."}, "waitFor": {"type": "array", "items": {"type": "string"}, "description": "The ID(s) of the command(s) that this command depends on."}}, "description": "Command describes a step performed as part of the build pipeline."}, "v1Completeness": {"type": "object", "properties": {"arguments": {"type": "boolean", "description": "If true, the builder claims that recipe.arguments is complete, meaning that all external inputs are properly captured in the recipe."}, "environment": {"type": "boolean", "description": "If true, the builder claims that recipe.environment is claimed to be complete."}, "materials": {"type": "boolean", "description": "If true, the builder claims that materials are complete, usually through some controls to prevent network access. Sometimes called \"hermetic\"."}}, "description": "Indicates that the builder claims certain fields in this message to be complete."}, "v1ComplianceNote": {"type": "object", "properties": {"title": {"type": "string", "description": "The title that identifies this compliance check."}, "description": {"type": "string", "description": "A description about this compliance check."}, "version": {"type": "array", "items": {"$ref": "#/definitions/v1ComplianceVersion"}, "description": "The OS and config versions the benchmark applies to."}, "rationale": {"type": "string", "description": "A rationale for the existence of this compliance check."}, "remediation": {"type": "string", "description": "A description of remediation steps if the compliance check fails."}, "cisBenchmark": {"$ref": "#/definitions/ComplianceNoteCisBenchmark"}, "scanInstructions": {"type": "string", "format": "byte", "description": "Serialized scan instructions with a predefined format."}, "impact": {"type": "string"}}}, "v1ComplianceOccurrence": {"type": "object", "properties": {"nonCompliantFiles": {"type": "array", "items": {"$ref": "#/definitions/v1NonCompliantFile"}}, "nonComplianceReason": {"type": "string"}, "version": {"$ref": "#/definitions/v1ComplianceVersion", "description": "The OS and config version the benchmark was run on."}}, "description": "An indication that the compliance checks in the associated ComplianceNote\nwere not satisfied for particular resources or a specified reason."}, "v1ComplianceVersion": {"type": "object", "properties": {"cpeUri": {"type": "string", "description": "The CPE URI (https://cpe.mitre.org/specification/) this benchmark is\napplicable to."}, "benchmarkDocument": {"type": "string", "description": "The name of the document that defines this benchmark, e.g. \"CIS Container-Optimized OS\"."}, "version": {"type": "string", "description": "The version of the benchmark. This is set to the version of the OS-specific\nCIS document the benchmark is defined in."}}, "description": "Describes the CIS benchmark version that is applicable to a given OS and \nos version."}, "v1DSSEAttestationNote": {"type": "object", "properties": {"hint": {"$ref": "#/definitions/DSSEAttestationNoteDSSEHint", "description": "DSSEHint hints at the purpose of the attestation authority."}}}, "v1DSSEAttestationOccurrence": {"type": "object", "properties": {"envelope": {"$ref": "#/definitions/v1Envelope", "description": "If doing something security critical, make sure to verify the signatures in this metadata."}, "statement": {"$ref": "#/definitions/v1InTotoStatement"}}, "description": "Deprecated. Prefer to use a regular Occurrence, and populate the\nEnvelope at the top level of the Occurrence."}, "v1DeploymentNote": {"type": "object", "properties": {"resourceUri": {"type": "array", "items": {"type": "string"}, "description": "Required. Resource URI for the artifact being deployed."}}, "description": "An artifact that can be deployed in some runtime."}, "v1DeploymentOccurrence": {"type": "object", "properties": {"userEmail": {"type": "string", "description": "Identity of the user that triggered this deployment."}, "deployTime": {"type": "string", "format": "date-time", "description": "Required. Beginning of the lifetime of this deployment."}, "undeployTime": {"type": "string", "format": "date-time", "description": "End of the lifetime of this deployment."}, "config": {"type": "string", "description": "Configuration used to create this deployment."}, "address": {"type": "string", "description": "Address of the runtime element hosting this deployment."}, "resourceUri": {"type": "array", "items": {"type": "string"}, "description": "Output only. Resource URI for the artifact being deployed taken from\nthe deployable field with the same name.", "readOnly": true}, "platform": {"$ref": "#/definitions/DeploymentOccurrencePlatform", "description": "Platform hosting this deployment."}}, "description": "The period during which some deployable was active in a runtime."}, "v1Digest": {"type": "object", "properties": {"algo": {"type": "string", "description": "`SHA1`, `SHA512` etc."}, "digestBytes": {"type": "string", "format": "byte", "description": "Value of the digest."}}, "description": "Digest information."}, "v1DiscoveryNote": {"type": "object", "properties": {"analysisKind": {"$ref": "#/definitions/v1NoteKind", "description": "Required. Immutable. The kind of analysis that is handled by this\ndiscovery."}}, "description": "A note that indicates a type of analysis a provider would perform. This note\nexists in a provider's project. A `Discovery` occurrence is created in a\nconsumer's project at the start of analysis."}, "v1DiscoveryOccurrence": {"type": "object", "properties": {"continuousAnalysis": {"$ref": "#/definitions/DiscoveryOccurrenceContinuousAnalysis", "description": "Whether the resource is continuously analyzed."}, "analysisStatus": {"$ref": "#/definitions/DiscoveryOccurrenceAnalysisStatus", "description": "The status of discovery for the resource."}, "analysisCompleted": {"$ref": "#/definitions/DiscoveryOccurrenceAnalysisCompleted"}, "analysisError": {"type": "array", "items": {"$ref": "#/definitions/rpcStatus"}, "description": "Indicates any errors encountered during analysis of a resource. There\ncould be 0 or more of these errors."}, "analysisStatusError": {"$ref": "#/definitions/rpcStatus", "description": "When an error is encountered this will contain a LocalizedMessage under\ndetails to show to the user. The LocalizedMessage is output only and\npopulated by the API."}, "cpe": {"type": "string", "description": "The CPE of the resource being scanned."}, "lastScanTime": {"type": "string", "format": "date-time", "description": "The last time this resource was scanned."}, "archiveTime": {"type": "string", "format": "date-time", "description": "The time occurrences related to this discovery occurrence were archived.", "readOnly": true}, "sbomStatus": {"$ref": "#/definitions/DiscoveryOccurrenceSBOMStatus", "description": "The status of an SBOM generation."}, "vulnerabilityAttestation": {"$ref": "#/definitions/DiscoveryOccurrenceVulnerabilityAttestation", "description": "The status of an vulnerability attestation generation."}}, "description": "Provides information about the analysis status of a discovered resource."}, "v1Distribution": {"type": "object", "properties": {"cpeUri": {"type": "string", "description": "The cpe_uri in [CPE format](https://cpe.mitre.org/specification/)\ndenoting the package manager version distributing a package.", "required": ["cpe_uri"]}, "architecture": {"$ref": "#/definitions/v1Architecture", "description": "The CPU architecture for which packages in this distribution channel were\nbuilt."}, "latestVersion": {"$ref": "#/definitions/v1Version", "description": "The latest available version of this package in this distribution channel."}, "maintainer": {"type": "string", "description": "A freeform string denoting the maintainer of this package."}, "url": {"type": "string", "description": "The distribution channel-specific homepage for this package."}, "description": {"type": "string", "description": "The distribution channel-specific description of this package."}}, "description": "This represents a particular channel of distribution for a given package.\nE.g., Debian's jessie-backports dpkg mirror.", "required": ["cpeUri"]}, "v1Envelope": {"type": "object", "properties": {"payload": {"type": "string", "format": "byte"}, "payloadType": {"type": "string"}, "signatures": {"type": "array", "items": {"$ref": "#/definitions/v1EnvelopeSignature"}}}, "description": "MUST match https://github.com/secure-systems-lab/dsse/blob/master/envelope.proto.\nAn authenticated message of arbitrary type."}, "v1EnvelopeSignature": {"type": "object", "properties": {"sig": {"type": "string", "format": "byte"}, "keyid": {"type": "string"}}}, "v1FileHashes": {"type": "object", "properties": {"fileHash": {"type": "array", "items": {"$ref": "#/definitions/v1Hash"}, "description": "Required. Collection of file hashes."}}, "description": "Container message for hashes of byte content of files, used in source\nmessages to verify integrity of source input to the build."}, "v1FileLocation": {"type": "object", "properties": {"filePath": {"type": "string", "description": "For jars that are contained inside .war files, this filepath\ncan indicate the path to war file combined with the path to jar file."}}, "description": "Indicates the location at which a package was found."}, "v1Fingerprint": {"type": "object", "properties": {"v1Name": {"type": "string", "description": "Required. The layer ID of the final layer in the Docker image's v1\nrepresentation."}, "v2Blob": {"type": "array", "items": {"type": "string"}, "description": "Required. The ordered list of v2 blobs that represent a given image."}, "v2Name": {"type": "string", "description": "Output only. The name of the image's v2 blobs computed via:\n  [bottom] := v2_blob[bottom]\n  [N] := sha256(v2_blob[N] + \" \" + v2_name[N+1])\nOnly the name of the final blob is kept.", "readOnly": true}}, "description": "A set of properties that uniquely identify a given <PERSON><PERSON> image."}, "v1GerritSourceContext": {"type": "object", "properties": {"hostUri": {"type": "string", "description": "The URI of a running Gerrit instance."}, "gerritProject": {"type": "string", "description": "The full project name within the host. Projects may be nested, so\n\"project/subproject\" is a valid project name. The \"repo name\" is the\nhostURI/project."}, "revisionId": {"type": "string", "description": "A revision (commit) ID."}, "aliasContext": {"$ref": "#/definitions/v1AliasContext", "description": "An alias, which may be a branch or tag."}}, "description": "A SourceContext referring to a Gerrit project."}, "v1GitSourceContext": {"type": "object", "properties": {"url": {"type": "string", "description": "Git repository URL."}, "revisionId": {"type": "string", "description": "Git commit hash."}}, "description": "A GitSourceContext denotes a particular revision in a third party Git\nrepository (e.g., GitHub)."}, "v1Hash": {"type": "object", "properties": {"type": {"type": "string", "description": "Required. The type of hash that was performed, e.g. \"SHA-256\"."}, "value": {"type": "string", "format": "byte", "description": "Required. The hash value."}}, "description": "Container message for hash values."}, "v1ImageNote": {"type": "object", "properties": {"resourceUrl": {"type": "string", "description": "Required. Immutable. The resource_url for the resource representing the\nbasis of associated occurrence images."}, "fingerprint": {"$ref": "#/definitions/v1Fingerprint", "description": "Required. Immutable. The fingerprint of the base image."}}, "description": "Basis describes the base image portion (Note) of the DockerImage\nrelationship. Linked occurrences are derived from this or an equivalent image\nvia:\n  FROM <Basis.resource_url>\nOr an equivalent reference, e.g., a tag of the resource_url."}, "v1ImageOccurrence": {"type": "object", "properties": {"fingerprint": {"$ref": "#/definitions/v1Fingerprint", "description": "Required. The fingerprint of the derived image."}, "distance": {"type": "integer", "format": "int32", "description": "Output only. The number of layers by which this image differs from the\nassociated image basis.", "readOnly": true}, "layerInfo": {"type": "array", "items": {"$ref": "#/definitions/v1Layer"}, "description": "This contains layer-specific metadata, if populated it has length\n\"distance\" and is ordered with [distance] being the layer immediately\nfollowing the base image and [1] being the final layer."}, "baseResourceUrl": {"type": "string", "description": "Output only. This contains the base image URL for the derived image\noccurrence.", "readOnly": true}}, "description": "Details of the derived image portion of the DockerImage relationship. This\nimage would be produced from a Dockerfile with FROM <DockerImage.Basis in\nattached Note>."}, "v1InTotoProvenance": {"type": "object", "properties": {"builderConfig": {"$ref": "#/definitions/v1BuilderConfig"}, "recipe": {"$ref": "#/definitions/v1Recipe", "description": "Identifies the configuration used for the build.\nWhen combined with materials, this SHOULD fully describe the build,\nsuch that re-running this recipe results in bit-for-bit identical output\n(if the build is reproducible)."}, "metadata": {"$ref": "#/definitions/v1Metadata"}, "materials": {"type": "array", "items": {"type": "string"}, "description": "The collection of artifacts that influenced the build including sources, dependencies, build tools, base images, and so on.\nThis is considered to be incomplete unless metadata.completeness.materials is true. Unset or null is equivalent to empty."}}}, "v1InTotoSlsaProvenanceV1": {"type": "object", "properties": {"_type": {"type": "string", "title": "InToto spec defined at https://github.com/in-toto/attestation/tree/main/spec#statement"}, "subject": {"type": "array", "items": {"$ref": "#/definitions/v1Subject"}}, "predicateType": {"type": "string"}, "predicate": {"$ref": "#/definitions/InTotoSlsaProvenanceV1SlsaProvenanceV1"}}}, "v1InTotoSlsaProvenanceV1ResourceDescriptor": {"type": "object", "properties": {"name": {"type": "string"}, "uri": {"type": "string"}, "digest": {"type": "object", "additionalProperties": {"type": "string"}}, "content": {"type": "string", "format": "byte"}, "downloadLocation": {"type": "string"}, "mediaType": {"type": "string"}, "annotations": {"type": "object", "additionalProperties": {"type": "object"}}}}, "v1InTotoStatement": {"type": "object", "properties": {"_type": {"type": "string", "description": "Always `https://in-toto.io/Statement/v0.1`."}, "subject": {"type": "array", "items": {"$ref": "#/definitions/v1Subject"}}, "predicateType": {"type": "string", "description": "`https://slsa.dev/provenance/v0.1` for SlsaProvenance."}, "provenance": {"$ref": "#/definitions/v1InTotoProvenance"}, "slsaProvenance": {"$ref": "#/definitions/v1SlsaProvenance"}, "slsaProvenanceZeroTwo": {"$ref": "#/definitions/v1SlsaProvenanceZeroTwo"}}, "description": "Spec defined at https://github.com/in-toto/attestation/tree/main/spec#statement\nThe serialized InTotoStatement will be stored as Envelope.payload.\nEnvelope.payloadType is always \"application/vnd.in-toto+json\"."}, "v1Jwt": {"type": "object", "properties": {"compactJwt": {"type": "string", "title": "The compact encoding of a JWS, which is always three base64 encoded strings\njoined by periods. For details, see:\nhttps://tools.ietf.org/html/rfc7515.html#section-3.1"}}}, "v1Layer": {"type": "object", "properties": {"directive": {"type": "string", "description": "Required. The recovered Dockerfile directive used to construct this layer.\nSee https://docs.docker.com/engine/reference/builder/ for more information."}, "arguments": {"type": "string", "description": "The recovered arguments to the Dockerfile directive."}}, "description": "Layer holds metadata specific to a layer of a Docker image."}, "v1License": {"type": "object", "properties": {"expression": {"type": "string", "description": "Often a single license can be used to represent the licensing terms.\nSometimes it is necessary to include a choice of one or more licenses\nor some combination of license identifiers.\nExamples: \"LGPL-2.1-only OR MIT\", \"LGPL-2.1-only AND MIT\",\n\"GPL-2.0-or-later WITH Bison-exception-2.2\"."}, "comments": {"type": "string", "title": "Comments"}}, "description": "License information."}, "v1ListNoteOccurrencesResponse": {"type": "object", "properties": {"occurrences": {"type": "array", "items": {"$ref": "#/definitions/v1Occurrence"}, "description": "The occurrences attached to the specified note."}, "nextPageToken": {"type": "string", "description": "Token to provide to skip to a particular spot in the list."}}, "description": "Response for listing occurrences for a note."}, "v1ListNotesResponse": {"type": "object", "properties": {"notes": {"type": "array", "items": {"$ref": "#/definitions/v1Note"}, "description": "The notes requested."}, "nextPageToken": {"type": "string", "description": "The next pagination token in the list response. It should be used as\n`page_token` for the following request. An empty value means no more\nresults."}}, "description": "Response for listing notes."}, "v1ListOccurrencesResponse": {"type": "object", "properties": {"occurrences": {"type": "array", "items": {"$ref": "#/definitions/v1Occurrence"}, "description": "The occurrences requested."}, "nextPageToken": {"type": "string", "description": "The next pagination token in the list response. It should be used as\n`page_token` for the following request. An empty value means no more\nresults."}}, "description": "Response for listing occurrences."}, "v1Metadata": {"type": "object", "properties": {"buildInvocationId": {"type": "string", "description": "Identifies the particular build invocation, which can be useful for finding associated logs or other ad-hoc analysis.\nThe value SHOULD be globally unique, per in-toto Provenance spec."}, "buildStartedOn": {"type": "string", "format": "date-time", "description": "The timestamp of when the build started."}, "buildFinishedOn": {"type": "string", "format": "date-time", "description": "The timestamp of when the build completed."}, "completeness": {"$ref": "#/definitions/v1Completeness", "description": "Indicates that the builder claims certain fields in this message to be complete."}, "reproducible": {"type": "boolean", "description": "If true, the builder claims that running the recipe on materials will produce bit-for-bit identical output."}}, "description": "Other properties of the build."}, "v1NonCompliantFile": {"type": "object", "properties": {"path": {"type": "string", "description": "Empty if `display_command` is set."}, "displayCommand": {"type": "string", "description": "Command to display the non-compliant files."}, "reason": {"type": "string", "description": "Explains why a file is non compliant for a CIS check."}}, "description": "Details about files that caused a compliance check to fail."}, "v1Note": {"type": "object", "properties": {"name": {"type": "string", "description": "Output only. The name of the note in the form of\n`projects/[PROVIDER_ID]/notes/[NOTE_ID]`.", "readOnly": true}, "shortDescription": {"type": "string", "description": "A one sentence description of this note."}, "longDescription": {"type": "string", "description": "A detailed description of this note."}, "kind": {"$ref": "#/definitions/v1NoteKind", "description": "Output only. The type of analysis. This field can be used as a filter in\nlist requests.", "readOnly": true}, "relatedUrl": {"type": "array", "items": {"$ref": "#/definitions/v1RelatedUrl"}, "description": "URLs associated with this note."}, "expirationTime": {"type": "string", "format": "date-time", "description": "Time of expiration for this note. Empty if note does not expire."}, "createTime": {"type": "string", "format": "date-time", "description": "Output only. The time this note was created. This field can be used as a\nfilter in list requests.", "readOnly": true}, "updateTime": {"type": "string", "format": "date-time", "description": "Output only. The time this note was last updated. This field can be used as\na filter in list requests.", "readOnly": true}, "relatedNoteNames": {"type": "array", "items": {"type": "string"}, "description": "Other notes related to this note."}, "vulnerability": {"$ref": "#/definitions/v1VulnerabilityNote", "description": "A note describing a package vulnerability."}, "build": {"$ref": "#/definitions/v1BuildNote", "description": "A note describing build provenance for a verifiable build."}, "image": {"$ref": "#/definitions/v1ImageNote", "description": "A note describing a base image."}, "package": {"$ref": "#/definitions/v1PackageNote", "description": "A note describing a package hosted by various package managers."}, "deployment": {"$ref": "#/definitions/v1DeploymentNote", "description": "A note describing something that can be deployed."}, "discovery": {"$ref": "#/definitions/v1DiscoveryNote", "description": "A note describing the initial analysis of a resource."}, "attestation": {"$ref": "#/definitions/v1AttestationNote", "description": "A note describing an attestation role."}, "upgrade": {"$ref": "#/definitions/v1UpgradeNote", "description": "A note describing available package upgrades."}, "compliance": {"$ref": "#/definitions/v1ComplianceNote", "description": "A note describing a compliance check."}, "dsseAttestation": {"$ref": "#/definitions/v1DSSEAttestationNote", "description": "A note describing a dsse attestation note."}, "vulnerabilityAssessment": {"$ref": "#/definitions/v1VulnerabilityAssessmentNote", "description": "A note describing a vulnerability assessment."}, "sbomReference": {"$ref": "#/definitions/v1SBOMReferenceNote", "description": "A note describing an SBOM reference."}}, "description": "A type of analysis that can be done for a resource."}, "v1NoteKind": {"type": "string", "enum": ["NOTE_KIND_UNSPECIFIED", "VULNERABILITY", "BUILD", "IMAGE", "PACKAGE", "DEPLOYMENT", "DISCOVERY", "ATTESTATION", "UPGRADE", "COMPLIANCE", "DSSE_ATTESTATION", "VULNERABILITY_ASSESSMENT", "SBOM_REFERENCE"], "default": "NOTE_KIND_UNSPECIFIED", "description": "Kind represents the kinds of notes supported.\n\n - NOTE_KIND_UNSPECIFIED: Default value. This value is unused.\n - VULNERABILITY: The note and occurrence represent a package vulnerability.\n - BUILD: The note and occurrence assert build provenance.\n - IMAGE: This represents an image basis relationship.\n - PACKAGE: This represents a package installed via a package manager.\n - DEPLOYMENT: The note and occurrence track deployment events.\n - DISCOVERY: The note and occurrence track the initial discovery status of a resource.\n - ATTESTATION: This represents a logical \"role\" that can attest to artifacts.\n - UPGRADE: This represents an available package upgrade.\n - COMPLIANCE: This represents a Compliance Note\n - DSSE_ATTESTATION: This represents a DSSE attestation Note\n - VULNERABILITY_ASSESSMENT: This represents a Vulnerability Assessment.\n - SBOM_REFERENCE: This represents an SBOM Reference."}, "v1Occurrence": {"type": "object", "properties": {"name": {"type": "string", "description": "Output only. The name of the occurrence in the form of\n`projects/[PROJECT_ID]/occurrences/[OCCURRENCE_ID]`.", "readOnly": true}, "resourceUri": {"type": "string", "description": "Required. Immutable. A URI that represents the resource for which the\noccurrence applies. For example,\n`https://gcr.io/project/image@sha256:123abc` for a Docker image."}, "noteName": {"type": "string", "description": "Required. Immutable. The analysis note associated with this occurrence, in\nthe form of `projects/[PROVIDER_ID]/notes/[NOTE_ID]`. This field can be\nused as a filter in list requests."}, "kind": {"$ref": "#/definitions/v1NoteKind", "description": "Output only. This explicitly denotes which of the occurrence details are\nspecified. This field can be used as a filter in list requests.", "readOnly": true}, "remediation": {"type": "string", "description": "A description of actions that can be taken to remedy the note."}, "createTime": {"type": "string", "format": "date-time", "description": "Output only. The time this occurrence was created.", "readOnly": true}, "updateTime": {"type": "string", "format": "date-time", "description": "Output only. The time this occurrence was last updated.", "readOnly": true}, "vulnerability": {"$ref": "#/definitions/v1VulnerabilityOccurrence", "description": "Describes a security vulnerability."}, "build": {"$ref": "#/definitions/v1BuildOccurrence", "description": "Describes a verifiable build."}, "image": {"$ref": "#/definitions/v1ImageOccurrence", "description": "Describes how this resource derives from the basis in the associated\nnote."}, "package": {"$ref": "#/definitions/v1PackageOccurrence", "description": "Describes the installation of a package on the linked resource."}, "deployment": {"$ref": "#/definitions/v1DeploymentOccurrence", "description": "Describes the deployment of an artifact on a runtime."}, "discovery": {"$ref": "#/definitions/v1DiscoveryOccurrence", "description": "Describes when a resource was discovered."}, "attestation": {"$ref": "#/definitions/v1AttestationOccurrence", "description": "Describes an attestation of an artifact."}, "upgrade": {"$ref": "#/definitions/v1UpgradeOccurrence", "description": "Describes an available package upgrade on the linked resource."}, "compliance": {"$ref": "#/definitions/v1ComplianceOccurrence", "description": "Describes a compliance violation on a linked resource."}, "dsseAttestation": {"$ref": "#/definitions/v1DSSEAttestationOccurrence", "description": "Describes an attestation of an artifact using dsse."}, "sbomReference": {"$ref": "#/definitions/v1SBOMReferenceOccurrence", "description": "Describes a specific SBOM reference occurrences."}, "envelope": {"$ref": "#/definitions/v1Envelope", "title": "https://github.com/secure-systems-lab/dsse"}}, "description": "An instance of an analysis type that has been found on a resource."}, "v1PackageNote": {"type": "object", "properties": {"name": {"type": "string", "description": "The name of the package.", "required": ["name"]}, "distribution": {"type": "array", "items": {"$ref": "#/definitions/v1Distribution"}, "description": "Deprecated.\nThe various channels by which a package is distributed."}, "packageType": {"type": "string", "description": "The type of package; whether native or non native (e.g., ruby gems,\nnode.js packages, etc.)."}, "cpeUri": {"type": "string", "description": "The cpe_uri in [CPE format](https://cpe.mitre.org/specification/)\ndenoting the package manager version distributing a package.\nThe cpe_uri will be blank for language packages."}, "architecture": {"$ref": "#/definitions/v1Architecture", "description": "The CPU architecture for which packages in this distribution channel were\nbuilt. Architecture will be blank for language packages."}, "version": {"$ref": "#/definitions/v1Version", "description": "The version of the package."}, "maintainer": {"type": "string", "description": "A freeform text denoting the maintainer of this package."}, "url": {"type": "string", "description": "The homepage for this package."}, "description": {"type": "string", "description": "The description of this package."}, "license": {"$ref": "#/definitions/v1License", "description": "Licenses that have been declared by the authors of the package."}, "digest": {"type": "array", "items": {"$ref": "#/definitions/v1Digest"}, "description": "Hash value, typically a file digest, that allows unique\nidentification a specific package."}}, "description": "PackageNote represents a particular package version.", "required": ["name"]}, "v1PackageOccurrence": {"type": "object", "properties": {"name": {"type": "string", "description": "The name of the installed package.", "readOnly": true, "required": ["name"]}, "location": {"type": "array", "items": {"$ref": "#/definitions/grafeasv1Location"}, "description": "All of the places within the filesystem versions of this package\nhave been found."}, "packageType": {"type": "string", "description": "The type of package; whether native or non native (e.g., ruby gems,\nnode.js packages, etc.).", "readOnly": true}, "cpeUri": {"type": "string", "description": "The cpe_uri in [CPE format](https://cpe.mitre.org/specification/)\ndenoting the package manager version distributing a package.\nThe cpe_uri will be blank for language packages.", "readOnly": true}, "architecture": {"$ref": "#/definitions/v1Architecture", "description": "The CPU architecture for which packages in this distribution channel were\nbuilt. Architecture will be blank for language packages."}, "license": {"$ref": "#/definitions/v1License", "description": "Licenses that have been declared by the authors of the package."}, "version": {"$ref": "#/definitions/v1Version", "description": "The version of the package."}}, "description": "Details on how a particular software package was installed on a system.", "required": ["name"]}, "v1ProjectRepoId": {"type": "object", "properties": {"projectId": {"type": "string", "description": "The ID of the project."}, "repoName": {"type": "string", "description": "The name of the repo. Leave empty for the default repo."}}, "description": "Selects a repo using a Google Cloud Platform project ID (e.g.,\nwinged-cargo-31) and a repo name within that project."}, "v1Recipe": {"type": "object", "properties": {"type": {"type": "string", "description": "URI indicating what type of recipe was performed. It determines the meaning of recipe.entryPoint, recipe.arguments, recipe.environment, and materials."}, "definedInMaterial": {"type": "string", "format": "int64", "description": "Index in materials containing the recipe steps that are not implied by recipe.type.\nFor example, if the recipe type were \"make\", then this would point to the source containing the Makefile, not the make program itself.\nSet to -1 if the recipe doesn't come from a material, as zero is default unset value for int64."}, "entryPoint": {"type": "string", "description": "String identifying the entry point into the build.\nThis is often a path to a configuration file and/or a target label within that file.\nThe syntax and meaning are defined by recipe.type.\nFor example, if the recipe type were \"make\", then this would reference the directory in which to run make as well as which target to use."}, "arguments": {"type": "array", "items": {"$ref": "#/definitions/protobufAny"}, "description": "Collection of all external inputs that influenced the build on top of recipe.definedInMaterial and recipe.entryPoint.\nFor example, if the recipe type were \"make\", then this might be the flags passed to make aside from the target, which is captured in recipe.entryPoint.\nSince the arguments field can greatly vary in structure, depending on the builder and recipe type, this is of form \"Any\"."}, "environment": {"type": "array", "items": {"$ref": "#/definitions/protobufAny"}, "description": "Any other builder-controlled inputs necessary for correctly evaluating the recipe. Usually only needed for reproducing the build but not evaluated as part of policy.\nSince the environment field can greatly vary in structure, depending on the builder and recipe type, this is of form \"Any\"."}}, "description": "Steps taken to build the artifact.\nFor a TaskRun, typically each container corresponds to one step in the recipe."}, "v1RelatedUrl": {"type": "object", "properties": {"url": {"type": "string", "description": "Specific URL associated with the resource."}, "label": {"type": "string", "description": "Label to describe usage of the URL."}}, "description": "Metadata for any related URL information."}, "v1RepoId": {"type": "object", "properties": {"projectRepoId": {"$ref": "#/definitions/v1ProjectRepoId", "description": "A combination of a project ID and a repo name."}, "uid": {"type": "string", "description": "A server-assigned, globally unique identifier."}}, "description": "A unique identifier for a Cloud Repo."}, "v1SBOMReferenceNote": {"type": "object", "properties": {"format": {"type": "string", "description": "The format that SBOM takes. E.g. may be spdx, cyclonedx, etc..."}, "version": {"type": "string", "description": "The version of the format that the SBOM takes. E.g. if the format\nis spdx, the version may be 2.3."}}, "description": "The note representing an SBOM reference."}, "v1SBOMReferenceOccurrence": {"type": "object", "properties": {"payload": {"$ref": "#/definitions/v1SbomReferenceIntotoPayload", "description": "The actual payload that contains the SBOM reference data."}, "payloadType": {"type": "string", "description": "The kind of payload that SbomReferenceIntotoPayload takes. Since it's in\nthe intoto format, this value is expected to be\n'application/vnd.in-toto+json'."}, "signatures": {"type": "array", "items": {"$ref": "#/definitions/v1EnvelopeSignature"}, "description": "The signatures over the payload."}}, "description": "The occurrence representing an SBOM reference as applied to a specific\nresource. The occurrence follows the DSSE specification. See\nhttps://github.com/secure-systems-lab/dsse/blob/master/envelope.md for more\ndetails."}, "v1SbomReferenceIntotoPayload": {"type": "object", "properties": {"_type": {"type": "string", "description": "Identifier for the schema of the Statement."}, "predicateType": {"type": "string", "description": "URI identifying the type of the Predicate."}, "subject": {"type": "array", "items": {"$ref": "#/definitions/v1Subject"}, "description": "Set of software artifacts that the attestation applies to. Each element\nrepresents a single software artifact."}, "predicate": {"$ref": "#/definitions/v1SbomReferenceIntotoPredicate", "description": "Additional parameters of the Predicate. Includes the actual data about the\nSBOM."}}, "description": "The actual payload that contains the SBOM Reference data.\nThe payload follows the intoto statement specification. See\nhttps://github.com/in-toto/attestation/blob/main/spec/v1.0/statement.md\nfor more details."}, "v1SbomReferenceIntotoPredicate": {"type": "object", "properties": {"referrerId": {"type": "string", "description": "The person or system referring this predicate to the consumer."}, "location": {"type": "string", "description": "The location of the SBOM."}, "mimeType": {"type": "string", "description": "The mime type of the SBOM."}, "digest": {"type": "object", "additionalProperties": {"type": "string"}, "description": "A map of algorithm to digest of the contents of the SBOM."}}, "description": "A predicate which describes the SBOM being referenced."}, "v1Severity": {"type": "string", "enum": ["SEVERITY_UNSPECIFIED", "MINIMAL", "LOW", "MEDIUM", "HIGH", "CRITICAL"], "default": "SEVERITY_UNSPECIFIED", "description": "Note provider assigned severity/impact ranking.\n\n - SEVERITY_UNSPECIFIED: Unknown.\n - MINIMAL: Minimal severity.\n - LOW: Low severity.\n - MEDIUM: Medium severity.\n - HIGH: High severity.\n - CRITICAL: Critical severity."}, "v1Signature": {"type": "object", "properties": {"signature": {"type": "string", "format": "byte", "description": "The content of the signature, an opaque bytestring.\nThe payload that this signature verifies MUST be unambiguously provided\nwith the Signature during verification. A wrapper message might provide\nthe payload explicitly. Alternatively, a message might have a canonical\nserialization that can always be unambiguously computed to derive the\npayload."}, "publicKeyId": {"type": "string", "description": "The identifier for the public key that verifies this signature.\n  * The `public_key_id` is required.\n  * The `public_key_id` SHOULD be an RFC3986 conformant URI.\n  * When possible, the `public_key_id` SHOULD be an immutable reference,\n    such as a cryptographic digest.\n\nExamples of valid `public_key_id`s:\n\nOpenPGP V4 public key fingerprint:\n  * \"openpgp4fpr:74FAF3B861BDA0870C7B6DEF607E48D2A663AEEA\"\nSee https://www.iana.org/assignments/uri-schemes/prov/openpgp4fpr for more\ndetails on this scheme.\n\nRFC6920 digest-named SubjectPublicKeyInfo (digest of the DER\nserialization):\n  * \"ni:///sha-256;cD9o9Cq6LG3jD0iKXqEi_vdjJGecm_iXkbqVoScViaU\"\n  * \"nih:///sha-256;703f68f42aba2c6de30f488a5ea122fef76324679c9bf89791ba95a1271589a5\""}}, "description": "Verifiers (e.g. Kritis implementations) MUST verify signatures\nwith respect to the trust anchors defined in policy (e.g. a Kritis policy).\nTypically this means that the verifier has been configured with a map from\n`public_key_id` to public key material (and any required parameters, e.g.\nsigning algorithm).\n\nIn particular, verification implementations MUST NOT treat the signature\n`public_key_id` as anything more than a key lookup hint. The `public_key_id`\nDOES NOT validate or authenticate a public key; it only provides a mechanism\nfor quickly selecting a public key ALREADY CONFIGURED on the verifier through\na trusted channel. Verification implementations MUST reject signatures in any\nof the following circumstances:\n  * The `public_key_id` is not recognized by the verifier.\n  * The public key that `public_key_id` refers to does not verify the\n    signature with respect to the payload.\n\nThe `signature` contents SHOULD NOT be \"attached\" (where the payload is\nincluded with the serialized `signature` bytes). Verifiers MUST ignore any\n\"attached\" payload and only verify signatures with respect to explicitly\nprovided payload (e.g. a `payload` field on the proto message that holds\nthis Signature, or the canonical serialization of the proto message that\nholds this signature)."}, "v1SlsaProvenance": {"type": "object", "properties": {"builder": {"$ref": "#/definitions/v1SlsaProvenanceSlsaBuilder"}, "recipe": {"$ref": "#/definitions/SlsaProvenanceSlsaRecipe", "description": "Identifies the configuration used for the build.\nWhen combined with materials, this SHOULD fully describe the build,\nsuch that re-running this recipe results in bit-for-bit identical output\n(if the build is reproducible)."}, "metadata": {"$ref": "#/definitions/v1SlsaProvenanceSlsaMetadata"}, "materials": {"type": "array", "items": {"$ref": "#/definitions/SlsaProvenanceMaterial"}, "description": "The collection of artifacts that influenced the build including sources, dependencies, build tools, base images, and so on.\nThis is considered to be incomplete unless metadata.completeness.materials is true. Unset or null is equivalent to empty."}}}, "v1SlsaProvenanceSlsaBuilder": {"type": "object", "properties": {"id": {"type": "string"}}}, "v1SlsaProvenanceSlsaCompleteness": {"type": "object", "properties": {"arguments": {"type": "boolean", "description": "If true, the builder claims that recipe.arguments is complete, meaning that all external inputs are properly captured in the recipe."}, "environment": {"type": "boolean", "description": "If true, the builder claims that recipe.environment is claimed to be complete."}, "materials": {"type": "boolean", "description": "If true, the builder claims that materials are complete, usually through some controls to prevent network access. Sometimes called \"hermetic\"."}}, "description": "Indicates that the builder claims certain fields in this message to be complete."}, "v1SlsaProvenanceSlsaMetadata": {"type": "object", "properties": {"buildInvocationId": {"type": "string", "description": "Identifies the particular build invocation, which can be useful for finding associated logs or other ad-hoc analysis.\nThe value SHOULD be globally unique, per in-toto Provenance spec."}, "buildStartedOn": {"type": "string", "format": "date-time", "description": "The timestamp of when the build started."}, "buildFinishedOn": {"type": "string", "format": "date-time", "description": "The timestamp of when the build completed."}, "completeness": {"$ref": "#/definitions/v1SlsaProvenanceSlsaCompleteness", "description": "Indicates that the builder claims certain fields in this message to be complete."}, "reproducible": {"type": "boolean", "description": "If true, the builder claims that running the recipe on materials will produce bit-for-bit identical output."}}, "description": "Other properties of the build."}, "v1SlsaProvenanceZeroTwo": {"type": "object", "properties": {"builder": {"$ref": "#/definitions/v1SlsaProvenanceZeroTwoSlsaBuilder"}, "buildType": {"type": "string"}, "invocation": {"$ref": "#/definitions/SlsaProvenanceZeroTwoSlsaInvocation"}, "buildConfig": {"type": "object"}, "metadata": {"$ref": "#/definitions/v1SlsaProvenanceZeroTwoSlsaMetadata"}, "materials": {"type": "array", "items": {"$ref": "#/definitions/SlsaProvenanceZeroTwoSlsaMaterial"}}}}, "v1SlsaProvenanceZeroTwoSlsaBuilder": {"type": "object", "properties": {"id": {"type": "string"}}, "description": "Identifies the entity that executed the recipe, which is trusted to have\ncorrectly performed the operation and populated this provenance."}, "v1SlsaProvenanceZeroTwoSlsaCompleteness": {"type": "object", "properties": {"parameters": {"type": "boolean"}, "environment": {"type": "boolean"}, "materials": {"type": "boolean"}}, "description": "Indicates that the builder claims certain fields in this message to be\ncomplete."}, "v1SlsaProvenanceZeroTwoSlsaMetadata": {"type": "object", "properties": {"buildInvocationId": {"type": "string"}, "buildStartedOn": {"type": "string", "format": "date-time"}, "buildFinishedOn": {"type": "string", "format": "date-time"}, "completeness": {"$ref": "#/definitions/v1SlsaProvenanceZeroTwoSlsaCompleteness"}, "reproducible": {"type": "boolean"}}, "description": "Other properties of the build."}, "v1Source": {"type": "object", "properties": {"artifactStorageSourceUri": {"type": "string", "description": "If provided, the input binary artifacts for the build came from this\nlocation."}, "fileHashes": {"type": "object", "additionalProperties": {"$ref": "#/definitions/v1FileHashes"}, "description": "Hash(es) of the build source, which can be used to verify that the original\nsource integrity was maintained in the build.\n\nThe keys to this map are file paths used as build source and the values\ncontain the hash values for those files.\n\nIf the build source came in a single package such as a gzipped tarfile\n(.tar.gz), the FileHash will be for the single path to that file."}, "context": {"$ref": "#/definitions/v1SourceContext", "description": "If provided, the source code used for the build came from this location."}, "additionalContexts": {"type": "array", "items": {"$ref": "#/definitions/v1SourceContext"}, "description": "If provided, some of the source code used for the build may be found in\nthese locations, in the case where the source repository had multiple\nremotes or submodules. This list will not include the context specified in\nthe context field."}}, "description": "Source describes the location of the source used for the build."}, "v1SourceContext": {"type": "object", "properties": {"cloudRepo": {"$ref": "#/definitions/v1CloudRepoSourceContext", "description": "A SourceContext referring to a revision in a Google Cloud Source Repo."}, "gerrit": {"$ref": "#/definitions/v1GerritSourceContext", "description": "A SourceContext referring to a Gerrit project."}, "git": {"$ref": "#/definitions/v1GitSourceContext", "description": "A SourceContext referring to any third party Git repo (e.g., GitHub)."}, "labels": {"type": "object", "additionalProperties": {"type": "string"}, "description": "Labels with user defined metadata."}}, "description": "A SourceContext is a reference to a tree of files. A SourceContext together\nwith a path point to a unique revision of a single file or directory."}, "v1Subject": {"type": "object", "properties": {"name": {"type": "string"}, "digest": {"type": "object", "additionalProperties": {"type": "string"}, "title": "`\"<ALGORITHM>\": \"<HEX_VALUE>\"`\nAlgorithms can be e.g. sha256, sha512\nSee https://github.com/in-toto/attestation/blob/main/spec/field_types.md#DigestSet"}}}, "v1UpgradeDistribution": {"type": "object", "properties": {"cpeUri": {"type": "string", "description": "Required - The specific operating system this metadata applies to. See\nhttps://cpe.mitre.org/specification/."}, "classification": {"type": "string", "title": "The operating system classification of this Upgrade, as specified by the\nupstream operating system upgrade feed. For Windows the classification is\none of the category_ids listed at https://docs.microsoft.com/en-us/previous-versions/windows/desktop/ff357803(v=vs.85)"}, "severity": {"type": "string", "description": "The severity as specified by the upstream operating system."}, "cve": {"type": "array", "items": {"type": "string"}, "description": "The cve tied to this Upgrade."}}, "description": "The Upgrade Distribution represents metadata about the Upgrade for each\noperating system (CPE). Some distributions have additional metadata around\nupdates, classifying them into various categories and severities."}, "v1UpgradeNote": {"type": "object", "properties": {"package": {"type": "string", "description": "Required for non-Windows OS. The package this Upgrade is for."}, "version": {"$ref": "#/definitions/v1Version", "description": "Required for non-Windows OS. The version of the package in machine + human\nreadable form."}, "distributions": {"type": "array", "items": {"$ref": "#/definitions/v1UpgradeDistribution"}, "description": "Metadata about the upgrade for each specific operating system."}, "windowsUpdate": {"$ref": "#/definitions/v1WindowsUpdate", "description": "Required for Windows OS. Represents the metadata about the Windows update."}}, "description": "An Upgrade Note represents a potential upgrade of a package to a given\nversion. For each package version combination (i.e. bash 4.0, bash 4.1,\nbash 4.1.2), there will be an Upgrade Note. For Windows, windows_update field\nrepresents the information related to the update."}, "v1UpgradeOccurrence": {"type": "object", "properties": {"package": {"type": "string", "description": "Required for non-Windows OS. The package this Upgrade is for."}, "parsedVersion": {"$ref": "#/definitions/v1Version", "description": "Required for non-Windows OS. The version of the package in a machine +\nhuman readable form."}, "distribution": {"$ref": "#/definitions/v1UpgradeDistribution", "description": "Metadata about the upgrade for available for the specific operating system\nfor the resource_url. This allows efficient filtering, as well as\nmaking it easier to use the occurrence."}, "windowsUpdate": {"$ref": "#/definitions/v1WindowsUpdate", "description": "Required for Windows OS. Represents the metadata about the Windows update."}}, "description": "An Upgrade Occurrence represents that a specific resource_url could install a\nspecific upgrade. This presence is supplied via local sources (i.e. it is\npresent in the mirror and the running system has noticed its availability).\nFor Windows, both distribution and windows_update contain information for the\nWindows update."}, "v1Version": {"type": "object", "properties": {"epoch": {"type": "integer", "format": "int32", "description": "Used to correct mistakes in the version numbering scheme."}, "name": {"type": "string", "description": "Required only when version kind is NORMAL. The main part of the version\nname."}, "revision": {"type": "string", "description": "The iteration of the package build from the above version."}, "inclusive": {"type": "boolean", "description": "Whether this version is specifying part of an inclusive range. Grafeas\ndoes not have the capability to specify version ranges; instead we have\nfields that specify start version and end versions. At times this is\ninsufficient - we also need to specify whether the version is included in\nthe range or is excluded from the range. This boolean is expected to be set\nto true when the version is included in a range."}, "kind": {"$ref": "#/definitions/VersionVersionKind", "description": "Required. Distinguishes between sentinel MIN/MAX versions and normal\nversions."}, "fullName": {"type": "string", "description": "Human readable version string. This string is of the form\n<epoch>:<name>-<revision> and is only set when kind is NORMAL."}}, "description": "Version contains structured information about the version of a package."}, "v1VulnerabilityAssessmentNote": {"type": "object", "properties": {"title": {"type": "string", "title": "The title of the note. E.g. `Vex-Debian-11.4`"}, "shortDescription": {"type": "string", "description": "A one sentence description of this Vex."}, "longDescription": {"type": "string", "description": "A detailed description of this Vex."}, "languageCode": {"type": "string", "description": "Identifies the language used by this document,\ncorresponding to IETF BCP 47 / RFC 5646."}, "publisher": {"$ref": "#/definitions/VulnerabilityAssessmentNotePublisher", "description": "Publisher details of this Note."}, "product": {"$ref": "#/definitions/VulnerabilityAssessmentNoteProduct", "description": "The product affected by this vex."}, "assessment": {"$ref": "#/definitions/VulnerabilityAssessmentNoteAssessment", "description": "Represents a vulnerability assessment for the product."}}, "description": "A single VulnerabilityAssessmentNote represents\none particular product's vulnerability assessment for one CVE."}, "v1VulnerabilityNote": {"type": "object", "properties": {"cvssScore": {"type": "number", "format": "float", "description": "The CVSS score of this vulnerability. CVSS score is on a scale of 0 - 10\nwhere 0 indicates low severity and 10 indicates high severity."}, "severity": {"$ref": "#/definitions/v1Severity", "description": "The note provider assigned severity of this vulnerability."}, "details": {"type": "array", "items": {"$ref": "#/definitions/VulnerabilityNoteDetail"}, "description": "Details of all known distros and packages affected by this vulnerability."}, "cvssV3": {"$ref": "#/definitions/v1CVSSv3", "description": "The full description of the CVSSv3 for this vulnerability."}, "windowsDetails": {"type": "array", "items": {"$ref": "#/definitions/VulnerabilityNoteWindowsDetail"}, "description": "Windows details get their own format because the information format and\nmodel don't match a normal detail. Specifically Windows updates are done as\npatches, thus Windows vulnerabilities really are a missing package, rather\nthan a package being at an incorrect version."}, "sourceUpdateTime": {"type": "string", "format": "date-time", "description": "The time this information was last changed at the source. This is an\nupstream timestamp from the underlying information source - e.g. Ubuntu\nsecurity tracker."}, "cvssVersion": {"$ref": "#/definitions/v1CVSSVersion", "description": "CVSS version used to populate cvss_score and severity."}, "cvssV2": {"$ref": "#/definitions/v1CVSS", "description": "The full description of the v2 CVSS for this vulnerability."}}, "description": "A security vulnerability that can be found in resources."}, "v1VulnerabilityOccurrence": {"type": "object", "properties": {"type": {"type": "string", "description": "The type of package; whether native or non native (e.g., ruby gems, node.js\npackages, etc.)."}, "severity": {"$ref": "#/definitions/v1Severity", "description": "Output only. The note provider assigned severity of this vulnerability.", "readOnly": true}, "cvssScore": {"type": "number", "format": "float", "description": "Output only. The CVSS score of this vulnerability. CVSS score is on a\nscale of 0 - 10 where 0 indicates low severity and 10 indicates high\nseverity.", "readOnly": true}, "cvssv3": {"$ref": "#/definitions/v1CVSS", "description": "The cvss v3 score for the vulnerability."}, "packageIssue": {"type": "array", "items": {"$ref": "#/definitions/VulnerabilityOccurrencePackageIssue"}, "description": "Required. The set of affected locations and their fixes (if available)\nwithin the associated resource."}, "shortDescription": {"type": "string", "description": "Output only. A one sentence description of this vulnerability.", "readOnly": true}, "longDescription": {"type": "string", "description": "Output only. A detailed description of this vulnerability.", "readOnly": true}, "relatedUrls": {"type": "array", "items": {"$ref": "#/definitions/v1RelatedUrl"}, "description": "Output only. URLs related to this vulnerability.", "readOnly": true}, "effectiveSeverity": {"$ref": "#/definitions/v1Severity", "description": "The distro assigned severity for this vulnerability when it is available,\notherwise this is the note provider assigned severity.\n\nWhen there are multiple PackageIssues for this vulnerability, they can have\ndifferent effective severities because some might be provided by the distro\nwhile others are provided by the language ecosystem for a language pack.\nFor this reason, it is advised to use the effective severity on the\nPackageIssue level. In the case where multiple PackageIssues have differing\neffective severities, this field should be the highest severity for any of\nthe PackageIssues."}, "fixAvailable": {"type": "boolean", "description": "Output only. Whether at least one of the affected packages has a fix\navailable.", "readOnly": true}, "cvssVersion": {"$ref": "#/definitions/v1CVSSVersion", "description": "Output only. CVSS version used to populate cvss_score and severity.", "readOnly": true}, "cvssV2": {"$ref": "#/definitions/v1CVSS", "description": "The cvss v2 score for the vulnerability."}, "vexAssessment": {"$ref": "#/definitions/VulnerabilityOccurrenceVexAssessment"}, "extraDetails": {"type": "string", "description": "Occurrence-specific extra details about the vulnerability."}}, "description": "An occurrence of a severity vulnerability on a resource."}, "v1WindowsUpdate": {"type": "object", "properties": {"identity": {"$ref": "#/definitions/WindowsUpdateIdentity", "description": "Required - The unique identifier for the update."}, "title": {"type": "string", "description": "The localized title of the update."}, "description": {"type": "string", "description": "The localized description of the update."}, "categories": {"type": "array", "items": {"$ref": "#/definitions/WindowsUpdateCategory"}, "description": "The list of categories to which the update belongs."}, "kbArticleIds": {"type": "array", "items": {"type": "string"}, "description": "The Microsoft Knowledge Base article IDs that are associated with the\nupdate."}, "supportUrl": {"type": "string", "description": "The hyperlink to the support information for the update."}, "lastPublishedTimestamp": {"type": "string", "format": "date-time", "description": "The last published timestamp of the update."}}, "description": "Windows Update represents the metadata about the update for the Windows\noperating system. The fields in this message come from the Windows Update API\ndocumented at\nhttps://docs.microsoft.com/en-us/windows/win32/api/wuapi/nn-wuapi-iupdate."}}}