type: google.api.Service
config_version: 3
name: containeranalysis.googleapis.com
title: Container Analysis API

apis:
- name: grafeas.v1.Grafeas

documentation:
  summary: |-
    An implementation of the Grafeas API, which stores, and enables querying and
    retrieval of critical metadata about all of your software artifacts.
  overview: |-
    The Container Analysis API allows you to store and retrieve metadata for a
    container resource.

backend:
  rules:
  - selector: grafeas.v1.Grafeas.GetOccurrence
    deadline: 30.0
  - selector: grafeas.v1.Grafeas.ListOccurrences
    deadline: 30.0
  - selector: grafeas.v1.Grafeas.DeleteOccurrence
    deadline: 30.0
  - selector: grafeas.v1.Grafeas.CreateOccurrence
    deadline: 30.0
  - selector: grafeas.v1.Grafeas.BatchCreateOccurrences
    deadline: 30.0
  - selector: grafeas.v1.Grafeas.UpdateOccurrence
    deadline: 30.0
  - selector: grafeas.v1.Grafeas.GetOccurrenceNote
    deadline: 30.0
  - selector: grafeas.v1.Grafeas.GetNote
    deadline: 30.0
  - selector: grafeas.v1.Grafeas.ListNotes
    deadline: 30.0
  - selector: grafeas.v1.Grafeas.DeleteNote
    deadline: 30.0
  - selector: grafeas.v1.Grafeas.CreateNote
    deadline: 30.0
  - selector: grafeas.v1.Grafeas.BatchCreateNotes
    deadline: 30.0
  - selector: grafeas.v1.Grafeas.UpdateNote
    deadline: 30.0
  - selector: grafeas.v1.Grafeas.ListNoteOccurrences
    deadline: 30.0

authentication:
  rules:
  - selector: '*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
