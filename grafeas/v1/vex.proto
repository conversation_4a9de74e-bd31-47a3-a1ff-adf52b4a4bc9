// Copyright 2023 The Grafeas Authors. All rights reserved.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//    http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package grafeas.v1;

import "grafeas/v1/common.proto";

option go_package = "google.golang.org/genproto/googleapis/grafeas/v1;grafeas";
option java_multiple_files = true;
option java_package = "io.grafeas.v1";
option objc_class_prefix = "GRA";

// A single VulnerabilityAssessmentNote represents
// one particular product's vulnerability assessment for one CVE.
message VulnerabilityAssessmentNote {
  // The title of the note. E.g. `Vex-Debian-11.4`
  string title = 1;

  // A one sentence description of this Vex.
  string short_description = 2;

  // A detailed description of this Vex.
  string long_description = 3;

  // Identifies the language used by this document,
  // corresponding to IETF BCP 47 / RFC 5646.
  string language_code = 4;

  // Publisher contains information about the publisher of
  // this Note.
  // (-- api-linter: core::0123::resource-annotation=disabled
  //     aip.dev/not-precedent: Publisher is not a separate resource. --)
  message Publisher {
    // Name of the publisher.
    // Examples: 'Google', 'Google Cloud Platform'.
    string name = 1;

    // Provides information about the authority of the issuing party to
    // release the document, in particular, the party's constituency and
    // responsibilities or other obligations.
    string issuing_authority = 2;

    // The context or namespace.
    // Contains a URL which is under control of the issuing party and can
    // be used as a globally unique identifier for that issuing party.
    // Example: https://csaf.io
    string publisher_namespace = 3;
  }

  // Publisher details of this Note.
  Publisher publisher = 5;

  // Product contains information about a product and how to uniquely identify
  // it.
  // (-- api-linter: core::0123::resource-annotation=disabled
  //     aip.dev/not-precedent: Product is not a separate resource. --)
  message Product {
    // Name of the product.
    string name = 1;

    // Token that identifies a product so that it can be referred to from other
    // parts in the document. There is no predefined format as long as it
    // uniquely identifies a group in the context of the current document.
    string id = 2;

    oneof identifier {
      // Contains a URI which is vendor-specific.
      // Example: The artifact repository URL of an image.
      string generic_uri = 3;
    }
  }

  // The product affected by this vex.
  Product product = 6;

  // Assessment provides all information that is related to a single
  // vulnerability for this product.
  message Assessment {
    // Holds the MITRE standard Common Vulnerabilities and Exposures (CVE)
    // tracking number for the vulnerability.
    // Deprecated: Use vulnerability_id instead to denote CVEs.
    string cve = 1 [deprecated = true];

    // The vulnerability identifier for this Assessment. Will hold one of
    // common identifiers e.g. CVE, GHSA etc.
    string vulnerability_id = 9;

    // A one sentence description of this Vex.
    string short_description = 2;

    // A detailed description of this Vex.
    string long_description = 3;

    // Holds a list of references associated with this vulnerability item and
    // assessment. These uris have additional information about the
    // vulnerability and the assessment itself. E.g. Link to a document which
    // details how this assessment concluded the state of this vulnerability.
    repeated grafeas.v1.RelatedUrl related_uris = 4;

    // Provides the state of this Vulnerability assessment.
    enum State {
      // No state is specified.
      STATE_UNSPECIFIED = 0;
      // This product is known to be affected by this vulnerability.
      AFFECTED = 1;
      // This product is known to be not affected by this vulnerability.
      NOT_AFFECTED = 2;
      // This product contains a fix for this vulnerability.
      FIXED = 3;
      // It is not known yet whether these versions are or are not affected
      // by the vulnerability. However, it is still under investigation.
      UNDER_INVESTIGATION = 4;
    }

    // Provides the state of this Vulnerability assessment.
    State state = 5;

    // Contains information about the impact of this vulnerability,
    // this will change with time.
    repeated string impacts = 6;

    // Justification provides the justification when the state of the
    // assessment if NOT_AFFECTED.
    message Justification {
      // Provides the type of justification.
      enum JustificationType {
        // JUSTIFICATION_TYPE_UNSPECIFIED.
        JUSTIFICATION_TYPE_UNSPECIFIED = 0;
        // The vulnerable component is not present in the product.
        COMPONENT_NOT_PRESENT = 1;
        // The vulnerable code is not present. Typically this case
        // occurs when source code is configured or built in a way that excludes
        // the vulnerable code.
        VULNERABLE_CODE_NOT_PRESENT = 2;
        // The vulnerable code can not be executed.
        // Typically this case occurs when the product includes the vulnerable
        // code but does not call or use the vulnerable code.
        VULNERABLE_CODE_NOT_IN_EXECUTE_PATH = 3;
        // The vulnerable code cannot be controlled by an attacker to exploit
        // the vulnerability.
        VULNERABLE_CODE_CANNOT_BE_CONTROLLED_BY_ADVERSARY = 4;
        // The product includes built-in protections or features that prevent
        // exploitation of the vulnerability. These built-in protections cannot
        // be subverted by the attacker and cannot be configured or disabled by
        // the user. These mitigations completely prevent exploitation based on
        // known attack vectors.
        INLINE_MITIGATIONS_ALREADY_EXIST = 5;
      }

      // The justification type for this vulnerability.
      JustificationType justification_type = 1;

      // Additional details on why this justification was chosen.
      string details = 2;
    }

    // Justification provides the justification when the state of the
    // assessment if NOT_AFFECTED.
    Justification justification = 7;

    // Specifies details on how to handle (and presumably, fix) a vulnerability.
    message Remediation {
      // The type of remediation that can be applied.
      enum RemediationType {
        // No remediation type specified.
        REMEDIATION_TYPE_UNSPECIFIED = 0;
        // A MITIGATION is available.
        MITIGATION = 1;
        // No fix is planned.
        NO_FIX_PLANNED = 2;
        // Not available.
        NONE_AVAILABLE = 3;
        // A vendor fix is available.
        VENDOR_FIX = 4;
        // A workaround is available.
        WORKAROUND = 5;
      }

      // The type of remediation that can be applied.
      RemediationType remediation_type = 1;

      // Contains a comprehensive human-readable discussion of the remediation.
      string details = 2;

      // Contains the URL where to obtain the remediation.
      grafeas.v1.RelatedUrl remediation_uri = 3;
    }

    // Specifies details on how to handle (and presumably, fix) a vulnerability.
    repeated Remediation remediations = 8;
  }

  // Represents a vulnerability assessment for the product.
  Assessment assessment = 7;
}
