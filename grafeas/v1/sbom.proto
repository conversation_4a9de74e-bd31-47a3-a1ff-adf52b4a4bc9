// Copyright 2023 The Grafeas Authors. All rights reserved.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//    http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package grafeas.v1;

import "grafeas/v1/common.proto";
import "grafeas/v1/intoto_statement.proto";

option go_package = "google.golang.org/genproto/googleapis/grafeas/v1;grafeas";
option java_multiple_files = true;
option java_package = "io.grafeas.v1";
option objc_class_prefix = "GRA";

// The note representing an SBOM reference.
message SBOMReferenceNote {
  // The format that SBOM takes. E.g. may be spdx, cyclonedx, etc...
  string format = 1;
  // The version of the format that the SBOM takes. E.g. if the format
  // is spdx, the version may be 2.3.
  string version = 2;
}

// The occurrence representing an SBOM reference as applied to a specific
// resource. The occurrence follows the DSSE specification. See
// https://github.com/secure-systems-lab/dsse/blob/master/envelope.md for more
// details.
message SBOMReferenceOccurrence {
  // The actual payload that contains the SBOM reference data.
  SbomReferenceIntotoPayload payload = 1;
  // The kind of payload that SbomReferenceIntotoPayload takes. Since it's in
  // the intoto format, this value is expected to be
  // 'application/vnd.in-toto+json'.
  string payload_type = 2;
  // The signatures over the payload.
  repeated EnvelopeSignature signatures = 3;
}

// The actual payload that contains the SBOM Reference data.
// The payload follows the intoto statement specification. See
// https://github.com/in-toto/attestation/blob/main/spec/v1.0/statement.md
// for more details.
message SbomReferenceIntotoPayload {
  // Identifier for the schema of the Statement.
  string type = 1 [json_name = "_type"];
  // URI identifying the type of the Predicate.
  string predicate_type = 2;
  // Set of software artifacts that the attestation applies to. Each element
  // represents a single software artifact.
  repeated Subject subject = 3;
  // Additional parameters of the Predicate. Includes the actual data about the
  // SBOM.
  SbomReferenceIntotoPredicate predicate = 4;
}

// A predicate which describes the SBOM being referenced.
message SbomReferenceIntotoPredicate {
  // The person or system referring this predicate to the consumer.
  string referrer_id = 1;
  // The location of the SBOM.
  string location = 2;
  // The mime type of the SBOM.
  string mime_type = 3;
  // A map of algorithm to digest of the contents of the SBOM.
  map<string, string> digest = 4;
}
