{"methodConfig": [{"name": [{"service": "grafeas.v1.<PERSON><PERSON>", "method": "GetOccurrence"}, {"service": "grafeas.v1.<PERSON><PERSON>", "method": "ListOccurrences"}, {"service": "grafeas.v1.<PERSON><PERSON>", "method": "DeleteOccurrence"}, {"service": "grafeas.v1.<PERSON><PERSON>", "method": "GetOccurrenceNote"}, {"service": "grafeas.v1.<PERSON><PERSON>", "method": "GetNote"}, {"service": "grafeas.v1.<PERSON><PERSON>", "method": "ListNotes"}, {"service": "grafeas.v1.<PERSON><PERSON>", "method": "DeleteNote"}, {"service": "grafeas.v1.<PERSON><PERSON>", "method": "ListNoteOccurrences"}], "timeout": "30s", "retryPolicy": {"maxAttempts": 5, "initialBackoff": "0.100s", "maxBackoff": "60s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["UNAVAILABLE", "DEADLINE_EXCEEDED"]}}, {"name": [{"service": "grafeas.v1.<PERSON><PERSON>", "method": "CreateOccurrence"}, {"service": "grafeas.v1.<PERSON><PERSON>", "method": "BatchCreateOccurrences"}, {"service": "grafeas.v1.<PERSON><PERSON>", "method": "UpdateOccurrence"}, {"service": "grafeas.v1.<PERSON><PERSON>", "method": "CreateNote"}, {"service": "grafeas.v1.<PERSON><PERSON>", "method": "BatchCreateNotes"}, {"service": "grafeas.v1.<PERSON><PERSON>", "method": "UpdateNote"}], "timeout": "30s"}]}