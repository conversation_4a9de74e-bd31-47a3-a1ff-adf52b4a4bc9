package cmd

import (
	"github.com/spf13/cobra"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/command"
)

const (
	rootCmdUse   = "apiworker"
	rootCmdShort = "Apiworker负责履行质量平台的任务执行职责"
	rootCmdLong  = `Apiworker负责履行质量平台的任务执行职责，通过订阅队列获取任务并执行。任务由dispatcher生产，任务执行过程的状态将被记录到apitest，执行结果将被记录到reporter`
)

func NewRootCommand() *cobra.Command {
	return command.NewRootCommand(rootCmdUse, rootCmdShort, rootCmdLong)
}
