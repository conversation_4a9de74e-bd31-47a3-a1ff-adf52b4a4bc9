package main

import (
	"github.com/spf13/cobra"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/version"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/apiworker/cmd"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/apiworker/mqc/server"
)

var ConfigFile = new(string)

func main() {
	root := cmd.NewRootCommand()
	root.Args = cobra.NoArgs
	root.RunE = func(cmd *cobra.Command, args []string) error {
		s, err := server.NewConsumeServer(*ConfigFile)
		if err != nil {
			return err
		}
		defer s.Stop()

		s.Start()

		return nil
	}

	vi := version.NewVersionInfo()
	root.Version = vi.String()

	flags := root.Flags()
	flags.SortFlags = false
	flags.StringVarP(ConfigFile, "mqc-config", "f", "etc/apiworker.yaml", "the config file of service")

	cobra.CheckErr(root.Execute())
}
