Name: apiworker

Log:
  ServiceName: mqc.apiworker
  Encoding: plain
  Level: info
  Path: /app/logs/apiworker

Prometheus:
  Host: 0.0.0.0
  Port: 20423
  Path: /metrics

Telemetry:
  Name: mqc.apiworker
  Endpoint: http://127.0.0.1:14268/api/traces
  Sampler: 1.0
  Batcher: jaeger

DevServer:
  Enabled: true
  Port: 20433

Redis:
  Host: 127.0.0.1:6379
  Type: node
  Pass: Quwan@2020
  DB: 4

Account:
  Endpoints:
    - 127.0.0.1:20111
  NonBlock: true
  Timeout: 0

Manager:
  Endpoints:
    - 127.0.0.1:20211
  NonBlock: true
  Timeout: 0

Reporter:
  Endpoints:
    - 127.0.0.1:20511
  NonBlock: true
  Timeout: 0

Relation:
  Endpoints:
    - 127.0.0.1:20711
  NonBlock: true
  Timeout: 0

Apiworker:
  Broker: 127.0.0.1:6379
  Backend: 127.0.0.1:6379
  Queue: mqc:apiworker
  ConsumerTag: mqc_worker
  IsEnableMetricsExporter: false # 新增加 暂时不需要开启
  IsMonitorHttp: false # 新增加 选择性开启
  Db: 5
  MaxWorker: 0

RelationProducer:
  Broker: 127.0.0.1:6379
  Backend: 127.0.0.1:6379
  Queue: mqc:relation
  Db: 16

ScriptPath: ${SCRIPT_PATH}

Tds:
  Endpoints:
    - ${TDS01}

HTTPRetryRules:
  - ProjectID: project_id:m1_ohldHVqPHs8bDL5VaG
    Retry: 5
    Interval: 2s

Security:
  Enabled: true
  Headers:
    x-tt-security-timestamp: *************
