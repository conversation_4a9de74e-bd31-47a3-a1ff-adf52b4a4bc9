package svc

import (
	"github.com/zeromicro/go-zero/core/service"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/apiworker/common/mock"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/apiworker/mqc/internal/config"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/apiworker/mqc/internal/http"
)

func Mock_ServiceContext() *ServiceContext {
	mock.Mock_Log()

	return &ServiceContext{
		Config: config.Config{
			ServiceConf: service.ServiceConf{
				Name: "mqc.apiworker",
			},
		},
		ReporterRpc:    mock.Mock_ReporterRpc(),
		AccountRpc:     mock.Mock_AccountRpc(),
		ManagerRpc:     mock.Mock_ManagerRpc(),
		HttpClientPool: http.NewHttpClient(),
	}
}
