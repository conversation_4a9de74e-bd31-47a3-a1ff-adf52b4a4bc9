package svc

import (
	red "github.com/redis/go-redis/v9"

	producerv2 "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworkerv2/producer"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/redis"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/apiworker/common/zrpc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/apiworker/mqc/internal/config"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/apiworker/mqc/internal/http"
)

type ServiceContext struct {
	Config config.Config

	Redis red.UniversalClient

	ReporterRpc *zrpc.ReporterRpc
	RelationRpc *zrpc.RelationRpc
	AccountRpc  *zrpc.AccountRpc
	ManagerRpc  *zrpc.ManagerRpc

	RelationProducer *producerv2.Producer

	HttpClientPool *http.HttpClientPool

	HTTPRetryRules map[string]config.HTTPRetryRule
}

func NewServiceContext(conf config.Config) *ServiceContext {
	httpRetryRules := make(map[string]config.HTTPRetryRule, len(conf.HTTPRetryRules))
	for _, rule := range conf.HTTPRetryRules {
		if rule.ProjectID == "" {
			continue
		}

		if v, ok := httpRetryRules[rule.ProjectID]; !ok || rule.Retry < v.Retry {
			httpRetryRules[rule.ProjectID] = rule
		}
	}

	return &ServiceContext{
		Config: conf,

		Redis: redis.NewClient(conf.Redis),

		ReporterRpc: zrpc.NewReporterRpc(conf.Reporter),
		RelationRpc: zrpc.NewRelationRpc(conf.Relation),
		AccountRpc:  zrpc.NewAccountRpc(conf.Account),
		ManagerRpc:  zrpc.NewManagerRpc(conf.Manager),

		RelationProducer: producerv2.NewProducer(conf.RelationProducer),

		HttpClientPool: http.NewHttpClient(),

		HTTPRetryRules: httpRetryRules,
	}
}
