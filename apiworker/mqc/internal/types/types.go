package types

type ProjectInfo struct {
	ProjectID   string `json:"project_id"`
	ProjectName string `json:"project_name"`
}

type PlanInfo struct {
	PlanExecuteID string `json:"plan_execute_id"`
	PlanID        string `json:"plan_id"`
	PlanName      string `json:"plan_name"`
}

type SuiteInfo struct {
	SuiteExecuteID string `json:"suite_execute_id"`
	SuiteID        string `json:"suite_id"`
	SuiteName      string `json:"suite_name"`
}

type CaseInfo struct {
	CaseExecuteID string `json:"case_execute_id"`
	CaseID        string `json:"case_id"`
	CaseName      string `json:"case_name"`
	MaintainedBy  string `json:"maintained_by"`
}
