package config

import (
	"time"

	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/service"
	"github.com/zeromicro/go-zero/core/stores/redis"
	"github.com/zeromicro/go-zero/zrpc"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
	consumerv2 "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworkerv2/consumer"
	producerv2 "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworkerv2/producer"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/security"
)

type Tds struct {
	Endpoints []string
}

type HTTPRetryRule struct {
	ProjectID string
	Retry     int           `json:",default=0,range=[0:10]"`
	Interval  time.Duration `json:",default=100ms"`
}

type Config struct {
	service.ServiceConf

	Redis redis.RedisConf

	Apitest  zrpc.RpcClientConf
	Reporter zrpc.RpcClientConf
	Relation zrpc.RpcClientConf
	Account  zrpc.RpcClientConf
	Manager  zrpc.RpcClientConf

	Apiworker        consumerv2.Config
	RelationProducer producerv2.Config

	ScriptPath     string
	Tds            Tds
	HTTPRetryRules []HTTPRetryRule
	Security       security.Config `json:",optional"`
}

func (c Config) LogConfig() logx.LogConf {
	return c.ServiceConf.Log
}

func (c Config) ListenOn() string {
	return constants.NoNeedToListen
}
