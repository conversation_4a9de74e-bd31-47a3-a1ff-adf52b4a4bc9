// Code generated by goctl. DO NOT EDIT.
package handler

import (
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworkerv2/consumer"

	casehandler "gitlab.ttyuyin.com/TestDevelopment/probe-backend/apiworker/mqc/internal/handler/case"
	componentgrouphandler "gitlab.ttyuyin.com/TestDevelopment/probe-backend/apiworker/mqc/internal/handler/componentgroup"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/apiworker/mqc/internal/handler/interfacecase"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/apiworker/mqc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
)

func RegisterHandlers(server *consumer.Consumer, serverCtx *svc.ServiceContext) error {
	return server.RegisterHandlers(
		consumer.NewTaskHandlerOjb(constants.MQTaskTypeAPIWorkerCase, casehandler.NewProcessor(serverCtx)),
		consumer.NewTaskHandlerOjb(
			constants.MQTaskTypeAPIWorkerComponentGroup, componentgrouphandler.NewProcessor(serverCtx),
		),
		consumer.NewTaskHandlerOjb(constants.MQTaskTypeAPIWorkerInterfaceCase, interfacecase.NewProcessor(serverCtx)),
	)
}
