package casehandler

import (
	"context"
	"encoding/base64"

	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/trace"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworkerv2/base"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/apiworker/mqc/internal/logic/apitesttask"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/apiworker/mqc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/rpc/pb"
)

// HandlerFunc 结构定义和队列数据需要匹配
// data: 原始数据
// buf: callback的pb
type HandlerFunc func(data []byte) (buf []byte, err error)

// Deprecated: use new api.
func CreateHandler(svcCtx *svc.ServiceContext) HandlerFunc {
	return func(data []byte) ([]byte, error) {
		defer func() {
			if r := recover(); r != nil {
				logx.Errorf("recover result: %+v", r)
			}
		}()

		taskData := &pb.WorkerReq{}
		task := apitesttask.NewApitestTask(context.Background(), svcCtx) // ctx done
		err := protobuf.UnmarshalJSON(data, taskData)
		if err != nil {
			logx.Errorf("failed to unmarshal the payload of api case task, payload: %s, error: %+v", data, err)
			return nil, err
		}

		task.SetBase64RequestPb(base64.StdEncoding.EncodeToString(data))
		_ = task.Run(taskData)
		return task.GetCallbackPB(), nil
	}
}

type Processor struct {
	svcCtx *svc.ServiceContext
}

func NewProcessor(svcCtx *svc.ServiceContext) (call base.Handler) {
	return &Processor{
		svcCtx: svcCtx,
	}
}

func (processor *Processor) ProcessTask(ctx context.Context, data *base.Task) (result []byte, err error) {
	logger := logx.WithContext(ctx)
	logger.Debugf(
		"processor trace_id: %s, span_id: %s, task_name: %s",
		trace.TraceIDFromContext(ctx), trace.SpanIDFromContext(ctx), data.Typename,
	)
	defer func() {
		if r := recover(); r != nil {
			logger.Errorf("processor recover result: %+v", r)
		}
	}()

	taskData := &pb.WorkerReq{}
	task := apitesttask.NewApitestTask(ctx, processor.svcCtx)
	err = protobuf.UnmarshalJSON(data.Payload, taskData)
	if err != nil {
		logger.Errorf("failed to unmarshal the payload of api case task, payload: %s, error: %+v", data.Payload, err)
		return nil, err
	}

	task.SetBase64RequestPb(base64.StdEncoding.EncodeToString(data.Payload))
	_ = task.Run(taskData)
	return task.GetCallbackPB(), nil
}
