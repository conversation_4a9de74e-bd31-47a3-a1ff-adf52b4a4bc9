package http

import (
	"fmt"
	"sync"
	"testing"

	"github.com/valyala/fasthttp"
)

// 9318364959 ns/op
func BenchmarkPool(b *testing.B) {
	pool := NewHttpClient()

	wg := &sync.WaitGroup{}
	// init
	pool.GetWithTimeout(6000, 6000, 6000)
	for n := 0; n < 500; n++ {
		wg.Add(1)
		cli := pool.GetWithTimeout(6000, 6000, 6000)
		post_test(cli, wg)
	}
	wg.Wait()
}

// *********** ns/op
func BenchmarkNoPool(b *testing.B) {
	wg := &sync.WaitGroup{}
	for n := 0; n < 500; n++ {
		wg.Add(1)
		cli := &fasthttp.Client{}
		post_test(cli, wg)
	}
	wg.Wait()
}

func post_test(cli *fasthttp.Client, wg *sync.WaitGroup) {
	defer wg.Done()
	raw_url := "http://probe.ttyuyin.com/tds/common/client"
	bodyval := "{\n    \"account\": \"*********\",\n    \"client_version\": \"6.0.0\",\n    \"drv_type\": 3,\n    \"is_tes_account\": true,\n    \"password\": \"Tt123456\",\n    \"platform_type\": \"1\",\n    \"prod_type\": 1,\n    \"uri\": \"tcp://testing-login.ttyuyin.com\",\n    \"use_epoll\": true\n}"

	req := fasthttp.AcquireRequest()
	defer fasthttp.ReleaseRequest(req)

	req.SetRequestURI(raw_url)
	req.Header.SetMethod("POST")
	req.Header.SetContentType("application/json")
	req.SetBodyString(bodyval)

	resp := fasthttp.AcquireResponse()
	defer fasthttp.ReleaseResponse(resp)

	err := cli.Do(req, resp)
	if err != nil {
		fmt.Println(err)
	}
}
