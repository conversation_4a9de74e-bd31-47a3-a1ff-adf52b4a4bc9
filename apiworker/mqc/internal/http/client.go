package http

import (
	"crypto/tls"
	"fmt"
	"sync"
	"time"

	"github.com/valyala/fasthttp"
)

type HttpClientPool struct {
	lock sync.Mutex

	cache map[string]*fasthttp.Client
}

func NewHttpClient() *HttpClientPool {
	cli := &HttpClientPool{
		cache: make(map[string]*fasthttp.Client),
	}

	return cli
}

func (cli *HttpClientPool) GetWithTimeout(connectTimeout, writeTimeout, readTimeout int64) *fasthttp.Client {
	// 暂时hardcode 解决tds超时问题
	var TemporaryTimeout int64 = 20000
	if connectTimeout < TemporaryTimeout {
		connectTimeout = TemporaryTimeout
	}
	if writeTimeout < TemporaryTimeout {
		writeTimeout = TemporaryTimeout
	}
	if readTimeout < TemporaryTimeout {
		readTimeout = TemporaryTimeout
	}

	maxConnWaitTimeout_ := time.Duration(connectTimeout) * time.Millisecond
	writeTimeout_ := time.Duration(writeTimeout) * time.Millisecond
	readTimeout_ := time.Duration(readTimeout) * time.Millisecond

	key := fmt.Sprintf("%s:%s:%s", maxConnWaitTimeout_, writeTimeout_, readTimeout_)

	cli.lock.Lock()
	defer cli.lock.Unlock()

	c, ok := cli.cache[key]
	if ok {
		return c
	}

	c = cli.newClient(maxConnWaitTimeout_, writeTimeout_, readTimeout_)
	cli.cache[key] = c
	return c
}

func (cli *HttpClientPool) newClient(maxConnWaitTimeout, writeTimeout, readTimeout time.Duration) *fasthttp.Client {
	c := &fasthttp.Client{
		ReadBufferSize:         16 * 1024, // 设置读缓冲区大小
		WriteBufferSize:        16 * 1024, // 设置写缓冲区大小
		DisablePathNormalizing: true,      // 禁用路径格式化
	}

	if maxConnWaitTimeout > 0 {
		c.MaxConnWaitTimeout = maxConnWaitTimeout
	}
	if writeTimeout > 0 {
		c.WriteTimeout = writeTimeout
	}
	if readTimeout > 0 {
		c.ReadTimeout = readTimeout
	}

	return c
}

// UseHttpsNotVerify 跳过https的tls证书验证
func (cli *HttpClientPool) UseHttpsNotVerify(client *fasthttp.Client) *fasthttp.Client {
	if client == nil {
		client = &fasthttp.Client{
			DisablePathNormalizing: true, // 禁用路径格式化
		}
	}
	keyVerify := fmt.Sprintf("%s:%s:%s:notverify", client.MaxConnWaitTimeout, client.WriteTimeout, client.ReadTimeout)

	cli.lock.Lock()
	defer cli.lock.Unlock()

	c, ok := cli.cache[keyVerify]
	if ok {
		return c
	}

	c = cli.newClient(client.MaxConnWaitTimeout, client.WriteTimeout, client.ReadTimeout)
	c.TLSConfig = &tls.Config{
		InsecureSkipVerify: true,
	} // #nosec G402

	cli.cache[keyVerify] = c
	return c
}
