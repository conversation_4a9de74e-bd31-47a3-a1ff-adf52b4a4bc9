package apitesttask

import (
	"encoding/json"
	"fmt"
	"reflect"
	"strconv"
	"sync"
	"unicode/utf8"

	"github.com/pkg/errors"
	"github.com/stretchr/testify/assert"
	"github.com/zeromicro/go-zero/core/jsonx"
	"github.com/zeromicro/go-zero/core/logx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/codes"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/template"
	managerpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type VarsPool struct {
	// 全局变量
	global map[string]any
	// 节点变量池
	nodes map[string]*NodeVarsPool
	lock  sync.RWMutex
}

type NodeVarsPool struct {
	Imports map[string]any // 入参
	Exports map[string]any // 出参
}

func NewVarsPool() *VarsPool {
	return &VarsPool{
		global: make(map[string]any),
		nodes:  make(map[string]*NodeVarsPool),
		lock:   sync.RWMutex{},
	}
}

func (vp *VarsPool) SetGlobalVar(name string, value any) {
	vp.lock.Lock()
	defer vp.lock.Unlock()
	vp.global[name] = value
}

func (vp *VarsPool) GetGlobalVar(name string) (any, bool) {
	vp.lock.RLock()
	defer vp.lock.RUnlock()
	v, ok := vp.global[name]
	return v, ok
}

func (vp *VarsPool) GetGlobal() map[string]any {
	vp.lock.RLock()
	defer vp.lock.RUnlock()
	return copyMap(vp.global)
}

// SetNodeImportVar 配置节点入参
func (vp *VarsPool) SetNodeImportVar(nodeId, name string, value any) {
	vp.lock.Lock()
	defer vp.lock.Unlock()

	node, ok := vp.nodes[nodeId]
	if !ok {
		node = &NodeVarsPool{
			Imports: make(map[string]any),
			Exports: make(map[string]any),
		}
	}
	node.Imports[name] = value
	vp.nodes[nodeId] = node
}

// SetNodeExportVar 配置节点出参
func (vp *VarsPool) SetNodeExportVar(nodeId, name string, value any) {
	vp.lock.Lock()
	defer vp.lock.Unlock()

	node, ok := vp.nodes[nodeId]
	if !ok {
		node = &NodeVarsPool{
			Imports: make(map[string]any),
			Exports: make(map[string]any),
		}
	}
	node.Exports[name] = value
	vp.nodes[nodeId] = node
}

func (vp *VarsPool) GetNodeImports(nodeId string) map[string]any {
	vp.lock.RLock()
	defer vp.lock.RUnlock()

	node, ok := vp.nodes[nodeId]
	if !ok {
		return make(map[string]any)
	}

	return copyMap(node.Imports)
}

func (vp *VarsPool) GetNodeExports(nodeId string) map[string]any {
	vp.lock.RLock()
	defer vp.lock.RUnlock()

	node, ok := vp.nodes[nodeId]
	if !ok {
		return make(map[string]any)
	}

	return copyMap(node.Exports)
}

func (vp *VarsPool) GetNodeImportVar(nodeId, name string) (any, bool) {
	vp.lock.RLock()
	defer vp.lock.RUnlock()

	node, ok := vp.nodes[nodeId]
	if !ok {
		return nil, false
	}

	v, ok := node.Imports[name]
	return v, ok
}

func (vp *VarsPool) GetNodeExportVar(nodeId, name string) (any, bool) {
	vp.lock.RLock()
	defer vp.lock.RUnlock()

	node, ok := vp.nodes[nodeId]
	if !ok {
		return nil, false
	}

	v, ok := node.Exports[name]
	return v, ok
}

func copyMap(src map[string]any) (dst map[string]any) {
	// 浅拷贝
	dst = make(map[string]any)
	if src == nil {
		return
	}

	_ = utils.Copy(&dst, src)
	return
}

func ReplaceVars_Json(str string, vars map[string]any) string {
	jsonVal := make(map[string]any, len(vars))
	for key := range vars {
		jsonVal[key] = fmt.Sprintf("{{toJson .%s}}", key)
	}

	buf, err := template.Execute("variableReplace", str, jsonVal)
	if err != nil {
		logx.Infof("ReplaceVars_Json ReplaceVars Execute fail, err: %s", err)
		return str
	}

	return ReplaceVars(string(buf), vars)
}

func ReplaceVars(str string, vars map[string]any) string {
	// go.number to json.Number, 解决替换出现浮点数的问题(模版替换不支持函数传参)
	repalceVals := make(map[string]any, len(vars))
	for key := range vars {
		value := vars[key]
		if numVal, ok := canConvertToJsonNumber(value); ok {
			value = numVal
		}
		repalceVals[key] = value
	}

	buf, err := template.Execute("variableReplace", str, repalceVals)
	if err != nil {
		logx.Infof("ReplaceVars Execute fail, err: %s", err)
		return str
	}
	return string(buf)
}

type VariableGetter interface {
	GetSource() managerpb.VariableSource
	GetManual() *managerpb.VariableManual
	GetExport() *managerpb.VariableExport
	GetEnvironment() *managerpb.VariableEnvironment
	GetFunction() *managerpb.VariableFunction
}

func (node *BaseNode) GetVariable(v VariableGetter) (any, error) {
	switch v.GetSource() {
	case managerpb.VariableSource_MANUAL:
		return node.GetVariable_Manual(v.GetManual())
	case managerpb.VariableSource_EXPORT:
		return node.GetVariable_Export(v.GetExport())
	case managerpb.VariableSource_ENVIRONMENT:
		return node.GetVariable_Environment(v.GetEnvironment())
	case managerpb.VariableSource_FUNCTION:
		return node.GetVariable_Function(v.GetFunction())
	}
	return nil, fmt.Errorf("无效的变量来源类型:%s", v.GetSource().String())
}

func (node *BaseNode) GetVariable_Manual(v *managerpb.VariableManual) (result any, err error) {
	// 先做模板替换, 再利用json反序列化获取真实类型的值
	source := ReplaceVars(v.GetValue(), node.task.VarsPool.GetNodeImports(node.Id()))

	var value any
	err = jsonx.UnmarshalFromString(source, &value)
	if err != nil {
		return nil, err
	}

	return value, nil
}

func (node *BaseNode) GetVariable_Export(v *managerpb.VariableExport) (result any, err error) {
	if v.GetNodeId() == node.ParentComboId() {
		// 如果是父框组件，则从父框组件的import中取变量.
		result, ok := node.task.VarsPool.GetNodeImportVar(v.GetNodeId(), v.GetValue())
		if !ok {
			return nil, fmt.Errorf("找不到节点:%s的入参:%s", v.GetNodeId(), v.GetValue())
		}
		return result, nil
	}

	result, ok := node.task.VarsPool.GetNodeExportVar(v.GetNodeId(), v.GetValue())
	if !ok {
		return nil, fmt.Errorf("找不到节点:%s的入参:%s", v.GetNodeId(), v.GetValue())
	}
	return result, nil
}

func (node *BaseNode) GetVariable_Environment(v *managerpb.VariableEnvironment) (result any, err error) {
	result, ok := node.task.VarsPool.GetGlobalVar(v.GetValue())
	if !ok {
		return nil, fmt.Errorf("找不到全局变量:%s", v.GetValue())
	}
	return result, nil
}

func (node *BaseNode) GetVariable_Function(v *managerpb.VariableFunction) (result any, err error) {
	return node.RunFunction(v)
}

type nilErrorf struct{}

func (n nilErrorf) Errorf(format string, args ...any) {
}

var defaultNilError nilErrorf

type CompareType string

const (
	CompareType_EQ           CompareType = "EQ"           // 相等
	CompareType_NE           CompareType = "NE"           // 不相等
	CompareType_LT           CompareType = "LT"           // 小于
	CompareType_LE           CompareType = "LE"           // 小于等于
	CompareType_GT           CompareType = "GT"           // 大于
	CompareType_GE           CompareType = "GE"           // 大于等于
	CompareType_CONTAINS     CompareType = "CONTAINS"     // 包含
	CompareType_NOT_CONTAINS CompareType = "NOT_CONTAINS" // 不包含
	CompareType_RE           CompareType = "RE"           // regex
	CompareType_LEN_EQ       CompareType = "LEN_EQ"       // 长度等于
	CompareType_LEN_NE       CompareType = "LEN_NE"       // 长度不等于
	CompareType_LEN_LT       CompareType = "LEN_LT"       // 长度小于
	CompareType_LEN_LE       CompareType = "LEN_LE"       // 长度小于等于
	CompareType_LEN_GT       CompareType = "LEN_GT"       // 长度大于
	CompareType_LEN_GE       CompareType = "LEN_GE"       // 长度大于等于
)

// Compare 关系运算
func Compare(typ CompareType, actual, expected any) bool {
	actual = convertToCompareValue(actual)
	expected = convertToCompareValue(expected)

	switch typ {
	case CompareType_EQ:
		return assert.Equal(defaultNilError, actual, expected)
	case CompareType_NE:
		return assert.NotEqual(defaultNilError, actual, expected)
	case CompareType_LT:
		return assert.Less(defaultNilError, actual, expected)
	case CompareType_LE:
		return assert.LessOrEqual(defaultNilError, actual, expected)
	case CompareType_GT:
		return assert.Greater(defaultNilError, actual, expected)
	case CompareType_GE:
		return assert.GreaterOrEqual(defaultNilError, actual, expected)
	case CompareType_CONTAINS:
		return assert.Contains(defaultNilError, actual, expected)
	case CompareType_NOT_CONTAINS:
		return assert.NotContains(defaultNilError, actual, expected)
	case CompareType_RE:
		return assert.Regexp(defaultNilError, expected, actual)
	}

	// 长度比较
	len_actual, ok := canConvertToStringLenOrNumber(actual)
	if !ok {
		return false
	}

	len_expected, ok := canConvertToStringLenOrNumber(expected)
	if !ok {
		return false
	}

	switch typ {
	case CompareType_LEN_EQ:
		return assert.Equal(defaultNilError, len_actual, len_expected)
	case CompareType_LEN_NE:
		return assert.NotEqual(defaultNilError, len_actual, len_expected)
	case CompareType_LEN_LT:
		return assert.Less(defaultNilError, len_actual, len_expected)
	case CompareType_LEN_LE:
		return assert.LessOrEqual(defaultNilError, len_actual, len_expected)
	case CompareType_LEN_GT:
		return assert.Greater(defaultNilError, len_actual, len_expected)
	case CompareType_LEN_GE:
		return assert.GreaterOrEqual(defaultNilError, len_actual, len_expected)
	}

	return false
}

func convertToCompareValue(v any) (out any) {
	// is array
	val := reflect.Indirect(reflect.ValueOf(v))
	switch val.Kind() {
	case reflect.Array, reflect.Slice:
		list := make([]any, val.Len())
		for i := 0; i < val.Len(); i++ {
			list[i] = convertToCompareValue(val.Index(i).Interface())
		}
		return list
	}

	// convert value
	out, ok := canConvertToFloat64(v)
	if ok {
		return out
	}

	return v
}

func canConvertToFloat64(v any) (float64, bool) {
	// json.Number to float64
	if jv, ok := v.(json.Number); ok {
		f, err := jv.Float64()
		if err != nil {
			f = 0
		}
		return f, true
	}

	val := reflect.Indirect(reflect.ValueOf(v))
	switch val.Kind() {
	case reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64, reflect.Int:
		return float64(val.Int()), true
	case reflect.Uint8, reflect.Uint16, reflect.Uint32, reflect.Uint64, reflect.Uint:
		return float64(val.Uint()), true
	case reflect.Float32, reflect.Float64:
		return val.Float(), true
	}

	return 0, false
}

func canConvertToJsonNumber(v any) (json.Number, bool) {
	if jv, ok := v.(json.Number); ok {
		return jv, ok
	}

	val := reflect.Indirect(reflect.ValueOf(v))
	switch val.Kind() {
	case reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64, reflect.Int,
		reflect.Uint8, reflect.Uint16, reflect.Uint32, reflect.Uint64, reflect.Uint:
		return json.Number(fmt.Sprintf("%v", v)), true
	case reflect.Float32, reflect.Float64:
		return json.Number(strconv.FormatFloat(val.Float(), 'f', -1, 64)), true
	}

	return "", false
}

func canConvertToStringLenOrNumber(v any) (int, bool) {
	if f, ok := canConvertToFloat64(v); ok {
		return int(f), true
	}

	val := reflect.Indirect(reflect.ValueOf(v))
	switch val.Kind() {
	case reflect.Array, reflect.Slice:
		return val.Len(), true
	case reflect.String:
		return utf8.RuneCountInString(val.String()), true
	}

	return 0, false
}

type HttpError struct {
	error

	code codes.Code
}

func NewHTTPError(code codes.Code, err error) *HttpError {
	return &HttpError{
		error: err,
		code:  code,
	}
}

func NewHTTPErrorWithMessage(code codes.Code, message string) *HttpError {
	return NewHTTPError(code, errors.New(message))
}

func NewHTTPErrorWithMessagef(code codes.Code, message string, args ...any) *HttpError {
	return NewHTTPError(code, errors.Errorf(message, args...))
}

func (e *HttpError) Code() codes.Code {
	return e.code
}
