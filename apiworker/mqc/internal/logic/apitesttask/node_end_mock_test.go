package apitesttask

import (
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	managerpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

func mock_default_end_execution_data() *managerpb.ApiExecutionData {
	return &managerpb.ApiExecutionData{
		Id:   utils.GenNanoId("END_"),
		Type: managerpb.ApiExecutionDataType_END,
		Data: &managerpb.ApiExecutionData_End{
			End: &managerpb.EndComponent{},
		},
	}
}
