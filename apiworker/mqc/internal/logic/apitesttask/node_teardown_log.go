package apitesttask

type TeardownNode_Log struct {
	*BaseComboNode_Log
}

type TeardownNodeLogWriter struct {
	*BaseComboNodeLogWriter

	log *TeardownNode_Log
}

func NewTeardownNodeLogWriter(bw *BaseComboNodeLogWriter) *TeardownNodeLogWriter {
	writer := &TeardownNodeLogWriter{
		BaseComboNodeLogWriter: bw,
		log: &TeardownNode_Log{
			BaseComboNode_Log: bw.log,
		},
	}
	return writer
}

func (writer *TeardownNodeLogWriter) toJson() string {
	return log2Json(writer.log)
}
