package apitesttask

import (
	"context"
	"testing"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/apiworker/common/zrpc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/apiworker/mqc/internal/config"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/apiworker/mqc/internal/http"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/apiworker/mqc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/utils"
	dispatcherpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/rpc/pb"
	managerpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

func test_new_task() *ApitestTask {
	s := &svc.ServiceContext{
		Config: config.Config{
			ScriptPath: "/Users/<USER>/workspace/go_sample",
		},
		ReporterRpc:    &zrpc.ReporterRpc{},
		AccountRpc:     &zrpc.AccountRpc{},
		ManagerRpc:     &zrpc.ManagerRpc{},
		HttpClientPool: http.NewHttpClient(),
	}

	task := NewApitestTask(context.Background(), s)
	task.SetSource(
		&dispatcherpb.WorkerReq{
			ProjectId:   utils.GenProjectId(),
			ExecuteType: managerpb.ApiExecutionDataType_API_CASE,
		},
	)
	task.SetRunAble(true)

	return task
}

func test_setup_new_ApiExecutionData(id string) *managerpb.ApiExecutionData {
	execData := &managerpb.ApiExecutionData{
		Id:   "SETUP_" + id,
		Type: managerpb.ApiExecutionDataType_ASSERT,
		Data: &managerpb.ApiExecutionData_Setup{
			Setup: &managerpb.SetupComponent{
				Imports: make([]*managerpb.Import, 0, 10),
				Exports: make([]*managerpb.Export, 0, 10),
			},
		},
		Children: []*managerpb.ApiExecutionData_ChildData{
			{
				Child: make([]*managerpb.ApiExecutionData, 0, 10),
			},
		},
	}
	return execData
}

func test_append_child(parent, child *managerpb.ApiExecutionData, idx int) {
	parent.Children[idx].Child = append(parent.Children[idx].Child, child)
}

func TestSetupNode(t *testing.T) {
	task := test_new_task()

	task.VarsPool.SetNodeExportVar("HTTP_1", "cid", "124")

	exec := test_setup_new_ApiExecutionData("1")
	test_append_child(exec, test_start_new_execution_data("1"), 0)
	assert := test_assert_new_execution_data("1")
	assert.GetAssert().Assertions = append(
		assert.GetAssert().Assertions, &managerpb.AssertComponent_Assertion{
			Compare: "GT",
			Actual: &managerpb.AssertComponent_Actual{
				NodeId: "HTTP_1",
				Value:  "cid",
			},
			Expected: &managerpb.AssertComponent_Expected{
				Source: managerpb.VariableSource_MANUAL,
				Manual: &managerpb.VariableManual{
					Value: "123",
				},
			},
		},
	)
	test_append_child(exec, assert, 0)
	test_append_child(exec, test_end_new_execution_data("1"), 0)

	exec.GetSetup().Imports = append(
		exec.GetSetup().Imports, &managerpb.Import{
			Name:   "cid",
			Source: managerpb.VariableSource_MANUAL,
			Manual: &managerpb.VariableManual{
				Value: "123",
			},
		},
	)
	exec.GetSetup().Exports = append(
		exec.GetSetup().Exports, &managerpb.Export{
			Name: "cid",
			Export: &managerpb.VariableExport{
				NodeId: "HTTP_1",
				Value:  "cid",
			},
		},
	)

	node, err := NewSetupNode(task, exec, nil, true)
	if err != nil {
		t.Errorf("NewSetupNode, err: %s", err)
		t.FailNow()
	}

	err = node.run()

	t.Log("content =", node.Content())

	if err != nil {
		t.Errorf("run, err: %s", err)
		t.FailNow()
	}
}

func test_start_new_execution_data(id string) *managerpb.ApiExecutionData {
	exec := &managerpb.ApiExecutionData{
		Id:   "START_" + id,
		Type: managerpb.ApiExecutionDataType_START,
		Data: &managerpb.ApiExecutionData_Start{
			Start: &managerpb.StartComponent{},
		},
	}
	return exec
}

func test_end_new_execution_data(id string) *managerpb.ApiExecutionData {
	exec := &managerpb.ApiExecutionData{
		Id:   "END_" + id,
		Type: managerpb.ApiExecutionDataType_END,
		Data: &managerpb.ApiExecutionData_End{
			End: &managerpb.EndComponent{},
		},
	}
	return exec
}

func test_assert_new_execution_data(id string) *managerpb.ApiExecutionData {
	exec := &managerpb.ApiExecutionData{
		Id:   "ASSERT_" + id,
		Type: managerpb.ApiExecutionDataType_ASSERT,
		Data: &managerpb.ApiExecutionData_Assert{
			Assert: &managerpb.AssertComponent{
				Assertions: []*managerpb.AssertComponent_Assertion{},
			},
		},
	}
	return exec
}
