package apitesttask

import (
	zeroutils "github.com/zeromicro/go-zero/core/utils"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	managerpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

func mock_http_execution_data_from_json(componentData string) *managerpb.ApiExecutionData {
	component := &managerpb.HttpRequestComponent{}
	if err := managerpb.DefaultUnmarshalOptions.Unmarshal(zeroutils.StringToByteSlice(componentData), component); err != nil {
		panic(any("无效的http组件信息, err:" + err.Error()))
	}

	execData := &managerpb.ApiExecutionData{
		Id:   utils.GenNanoId("HTTP_"),
		Type: managerpb.ApiExecutionDataType_HTTP,
		Data: &managerpb.ApiExecutionData_HttpRequest{
			HttpRequest: component,
		},
	}

	return execData
}
