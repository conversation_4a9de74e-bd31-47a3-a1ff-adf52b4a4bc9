package apitesttask

import (
	cryptorand "crypto/rand"
	"math/big"
	mathrand "math/rand"
	"net/url"
	"time"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	commonpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
)

const (
	Variable_PlanPurpose     = "plan_purpose"
	Variable_GeneralConfigId = "general_config_id"
	Variable_BaseUrl         = "base_url"
	Variable_BaseUrl_Chinese = "环境域名"
)

var rand = mathrand.New(mathrand.NewSource(time.Now().UnixNano()))

//func init() {
//	mathrand.Seed(time.Now().UnixNano())
//}

func (task *ApitestTask) InitGlobalEnv(conf *commonpb.GeneralConfig) {
	task.VarsPool.SetGlobalVar(Variable_GeneralConfigId, conf.GetConfigId())

	if conf.GetBaseUrl() != "" {
		count := len(task.svcCtx.Config.Tds.Endpoints)
		// tds的特殊处理
		for _, target := range task.svcCtx.Config.Tds.Endpoints {
			if target == conf.GetBaseUrl() {
				n := random(int64(count))
				conf.BaseUrl = task.svcCtx.Config.Tds.Endpoints[n]
				task.Infof("selected baseurl is [%s], random number is %d", conf.GetBaseUrl(), n)
				break
			}
		}

		_, err := url.Parse(conf.GetBaseUrl())
		if err != nil {
			task.Errorf("无效的base_url: %s, err: %s", conf.GetBaseUrl(), err)
		}

		task.VarsPool.SetGlobalVar(Variable_BaseUrl, conf.GetBaseUrl())
		task.VarsPool.SetGlobalVar(Variable_BaseUrl_Chinese, conf.GetBaseUrl())
		task.SetVerify(conf.GetVerify())
	}

	for _, item := range conf.GetVariables() {
		task.VarsPool.SetGlobalVar(item.Key, item.Value)
	}
}

func (task *ApitestTask) InitAccountEnv(confs []*commonpb.AccountConfig) {
	if len(confs) == 0 {
		return
	}

	task.accountEnv = make(map[int64]*commonpb.AccountConfig, len(confs))
	for _, cfg := range confs {
		target := &commonpb.AccountConfig{}
		_ = utils.Copy(target, cfg)
		task.accountEnv[cfg.GetProductType()] = target
	}
}

func random(max_ int64) int64 {
	n, err := cryptorand.Int(cryptorand.Reader, big.NewInt(max_))
	if err != nil {
		return rand.Int63n(max_)
	}

	return n.Int64()
}
