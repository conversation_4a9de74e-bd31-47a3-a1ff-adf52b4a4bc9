package apitesttask

import (
	"bytes"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"mime/multipart"
	"net/http"
	"net/url"
	"regexp"
	"slices"
	"strings"
	"time"

	"github.com/jmespath/go-jmespath"
	"github.com/valyala/fasthttp"
	"github.com/zeromicro/go-zero/core/jsonx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/apiworker/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/codes"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/state"
	managerpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

var apiProxyPaths = []string{common.ConstApiProxyCreateClientPath, common.ConstApiProxyCommonCallPath}

type HttpNode struct {
	*BaseNode

	source *managerpb.HttpRequestComponent
	logW   *HttpNodeLogWriter

	HttpClient *fasthttp.Client
	Request    *fasthttp.Request
	Response   *fasthttp.Response

	respHeader http.Header
}

func NewHttpNode(task *ApitestTask, data *managerpb.ApiExecutionData, parent Node, isShell bool) (Node, error) {
	node := &HttpNode{}
	var err error
	node.BaseNode, err = NewBaseNode(task, data, node, parent, isShell)
	if err != nil {
		return nil, node.UpdateInvalidStatef(codes.NodeInvalid, "%s", err)
	}

	// source数据
	source := data.GetHttpRequest()
	node.source = source
	node.logW = NewHttpNodeLogWriter(node.BaseNode.logW)

	return node, nil
}

func (node *HttpNode) run() (err error) {
	var (
		taskID    = node.task.Source().GetTaskId()
		executeID = node.task.Source().GetExecuteId()
		nodeID    = node.NodeId
	)

	node.processingImports()

	node.getClientWithTimeout()

	node.Request = fasthttp.AcquireRequest()
	defer fasthttp.ReleaseRequest(node.Request)

	err = node.setUrl()
	if err != nil {
		return err
	}

	node.setMethod()
	node.setAuthorization()
	node.setHeaders()
	node.setQueryParams()
	err = node.setBody()
	if err != nil {
		return err
	}

	node.Response = fasthttp.AcquireResponse()
	defer fasthttp.ReleaseResponse(node.Response)

	rule := node.task.RetryRule()
	ruleBytes := jsonx.MarshalIgnoreError(rule)
	for i := 0; i <= rule.Retry; i++ {
		if i != 0 && err == nil {
			node.task.Infof(
				"after retrying %d times, the HTTP node executed successfully, task_id: %s, execute_id: %s, node_id: %s, rule: %s",
				i, taskID, executeID, nodeID, ruleBytes,
			)
			break
		} else if i != 0 {
			time.Sleep(rule.Interval)

			node.task.Infof(
				"retry to execute the HTTP node, task_id: %s, execute_id: %s, node_id: %s, rule: %s, index: %d, error: %+v",
				taskID, executeID, nodeID, ruleBytes, i, err,
			)
			node.logW.ResetBeforeRetry()
			node.Response.Reset()
		}
		node.task.Debugf(
			"ready to execute the HTTP node, task_id: %s, execute_id: %s, node_id: %s, index: %d",
			taskID, executeID, nodeID, i,
		)

		err = node.doRequest()
		if err != nil {
			continue
		}

		node.logW.SetResponseStatusCode(node.Response.StatusCode())
		node.logW.SetResponseBody(string(node.Response.Body()))

		node.processingRespHeader()
		err = node.processingAssertions()
		if err != nil {
			continue
		}

		node.processingExports()
	}

	if err != nil {
		// 注：这里不能使用`errors.As(err, &he)`进行判断
		if he, ok := err.(*HttpError); ok {
			if rule.Retry > 0 {
				err = node.UpdateFailureStatef(
					he.Code(), "重试%d次后仍然失败，最后一次的失败原因：%s", rule.Retry, he.Error(),
				)
			} else {
				err = node.UpdateFailureStatef(he.Code(), "%s", he.Error())
			}
		} else {
			node.task.Errorf(
				"invalid error type, task_id: %s, execute_id: %s, node_id: %s, expected: %T, but got %T",
				taskID, executeID, nodeID, (*HttpError)(nil), err,
			)
			err = node.UpdateInvalidStatef(codes.NodeInvalid, "无效的错误类型，本次执行的失败原因：%s", err.Error())
		}
	}

	return err
}

func (node *HttpNode) Content() string {
	return node.logW.toJson()
}

func (node *HttpNode) Logger() NodeLogger {
	return node.logW
}

func (node *HttpNode) processingImports() {
	// 配置入参
	for _, im := range node.source.GetImports() {
		node.processingImport(im)
	}
}

func (node *HttpNode) processingImport(im *managerpb.Import) {
	key := im.GetName()
	var value any
	var err error
	errcode := errorx.OK
	logIm := &managerpb.Import{}
	_ = utils.Copy(logIm, im)

	if key == "" {
		// 忽略key为空的入参
		return
	}

	defer func() {
		node.logW.SetImport(key, value, logIm, errcode, GetErrCode(errcode).String(), state.Error2Debug(err))
	}()

	// 配置入参
	value, err = node.GetVariable(im)
	if err != nil {
		errcode = codes.NodeImportNotFound
		node.UpdateWarningStatef(codes.NodeImportNotFound, "key: %s, err: %s", key, err)
	}
	node.task.VarsPool.SetNodeImportVar(node.Id(), key, value)
}

func (node *HttpNode) setUrl() (err error) {
	// url支持:
	// 1. 如果只传递了路径(如：/aa/bb)，则 url = repalceVars({{.base_url}}/aa/bb)
	// 2. 如果传递了{{.base_url}}/aa/b，则 url = repalceVars({{.base_url}}/aa/bb)
	// 3. 如果传递了完整url(比如: http://baidu.com/aa/bb)，则 url = url

	errcode := errorx.OK
	rawUrl := node.source.GetUrl()

	defer func() {
		node.logW.SetUrl(rawUrl, errcode, GetErrCode(errcode).String(), state.Error2Debug(err))
	}()

	rawUrl = ReplaceVars(rawUrl, node.task.VarsPool.GetNodeImports(node.Id()))

	urlParser, err := url.Parse(rawUrl)
	if err != nil {
		errcode = codes.NodeHTTPInvalidUrl
		return node.UpdateFailureStatef(codes.NodeHTTPInvalidUrl, "url: %s, err: %s", rawUrl, err)
	}

	if urlParser.Host == "" {
		rawUrl = fmt.Sprintf("{{.%s}}%s", Variable_BaseUrl, rawUrl)
	}

	rawUrl = ReplaceVars(rawUrl, node.task.VarsPool.GetGlobal())
	node.Request.SetRequestURI(rawUrl)

	node.setTlsVerify(rawUrl)

	return nil
}

// setTlsVerify 检测是否校验tls证书
func (node *HttpNode) setTlsVerify(rawUrl string) {
	// 如果使用了 https 的 {{.base_url}}，那么通用配置的verify就会针对该 url 生效
	generalBaseUrl, exist := node.task.VarsPool.GetGlobalVar(Variable_BaseUrl)
	if exist {
		general, ok := generalBaseUrl.(string)
		if !ok {
			return
		}

		// 默认会验证证书，因此只有不需要验证证书时候才需要配置
		if strings.HasPrefix(general, "https://") && strings.HasPrefix(rawUrl, general) && !node.task.Verify() {
			node.HttpClient = node.task.svcCtx.HttpClientPool.UseHttpsNotVerify(node.HttpClient)
		}
	}
}

func (node *HttpNode) setMethod() {
	node.Request.Header.SetMethod(node.source.GetMethod())
	node.logW.SetMethod(node.source.GetMethod())
}

func (node *HttpNode) doRequest() (err error) {
	begin := time.Now().UnixNano() / 1e6
	errcode := errorx.OK

	defer func() {
		end := time.Now().UnixNano() / 1e6
		node.logW.SetCostMs(end-begin, errcode, GetErrCode(errcode).String(), state.Error2Debug(err))
		//node.logW.SetRequestBody(string(node.Request.Body()))
		//node.Request.Header.VisitAll(
		//	func(key, value []byte) {
		//		node.logW.SetHeader(string(key), string(value))
		//	},
		//)
	}()

	err = node.HttpClient.Do(node.Request, node.Response)
	if err != nil {
		errcode = codes.NodeHTTPRequestFailure
		// return node.UpdateFailureStatef(codes.NodeHTTPRequestFailure, err.Error())
		return NewHTTPError(codes.NodeHTTPRequestFailure, err)
	}

	return nil
}

func (node *HttpNode) setAuthorization() {
	switch node.source.GetAuthorization().GetType() {
	case managerpb.HttpRequestComponent_Authorization_NO_AUTH:
		return
	case managerpb.HttpRequestComponent_Authorization_API_KEY:
		node.setAuthorization_ApiKey(node.source.GetAuthorization().GetApiKey())
		return
	case managerpb.HttpRequestComponent_Authorization_BEARER_TOKEN:
		node.setAuthorization_BearerToken(node.source.GetAuthorization().GetBearerToken())
	case managerpb.HttpRequestComponent_Authorization_BASIC_AUTH:
		node.setAuthorization_BasicAuth(node.source.GetAuthorization().GetBasicAuth())
	}
}

func (node *HttpNode) setAuthorization_ApiKey(apikey *managerpb.HttpRequestComponent_Authorization_ApiKey) {
	switch apikey.GetAddTo() {
	case managerpb.HttpRequestComponent_Authorization_ApiKey_HEADERS:
		node.setHeader(apikey.GetKey(), apikey.GetValue())
	case managerpb.HttpRequestComponent_Authorization_ApiKey_QUERY_PARAMS:
		node.setQueryParam(apikey.GetKey(), apikey.GetValue())
	}
}

func (node *HttpNode) setAuthorization_BearerToken(btoken *managerpb.HttpRequestComponent_Authorization_BearerToken) {
	bearer := "Bearer " + ReplaceVars(btoken.GetToken(), node.task.VarsPool.GetNodeImports(node.Id()))
	node.setHeader("Authorization", bearer)
}

func (node *HttpNode) setAuthorization_BasicAuth(bauth *managerpb.HttpRequestComponent_Authorization_BasicAuth) {
	username := ReplaceVars(bauth.GetUsername(), node.task.VarsPool.GetNodeImports(node.Id()))
	password := ReplaceVars(bauth.GetPassword(), node.task.VarsPool.GetNodeImports(node.Id()))
	basic := username + ":" + password
	auth := "Basic " + base64.StdEncoding.EncodeToString([]byte(basic))
	node.setHeader("Authorization", auth)
}

func (node *HttpNode) setHeaders() {
	for _, item := range node.source.GetHeaders() {
		node.setHeader(item.GetKey(), item.GetValue())
	}

	// 如果不是通过`api-proxy`进行请求，则需要设置`TT`安全请求头
	uri := string(node.Request.RequestURI())
	if !slices.Contains(apiProxyPaths, uri) {
		for key, val := range node.task.securityHeaders {
			node.setHeader(key, val)
		}
	}
}

func (node *HttpNode) setHeader(key, value string) {
	key = ReplaceVars(key, node.task.VarsPool.GetNodeImports(node.Id()))
	value = ReplaceVars(value, node.task.VarsPool.GetNodeImports(node.Id()))
	if strings.EqualFold(key, fasthttp.HeaderHost) {
		node.Request.UseHostHeader = true
	}
	node.Request.Header.Set(key, value)
	node.logW.SetHeader(key, value)
}

func (node *HttpNode) setQueryParams() {
	for _, param := range node.source.GetQueryParams() {
		node.setQueryParam(param.GetKey(), param.GetValue())
	}
}

func (node *HttpNode) setQueryParam(key, value string) {
	key = ReplaceVars(key, node.task.VarsPool.GetNodeImports(node.Id()))
	value = ReplaceVars(value, node.task.VarsPool.GetNodeImports(node.Id()))

	switch node.source.GetMethod() {
	case "GET":
		node.Request.URI().QueryArgs().Add(key, value)
	case "POST":
		node.Request.PostArgs().Add(key, value)
	case "PUT":
		node.Request.PostArgs().Add(key, value)
	case "DELETE":
		node.Request.URI().QueryArgs().Add(key, value)
	}
	node.logW.SetQueryParam(key, value)
}

func (node *HttpNode) setBody() error {
	switch node.source.GetBody().GetType() {
	case managerpb.HttpRequestComponent_Body_NONE:
		return nil
	case managerpb.HttpRequestComponent_Body_APPLICATION_JSON:
		return node.setBody_ApplicationJson(node.source.GetBody())
	case managerpb.HttpRequestComponent_Body_MULTIPART_FORM_DATA:
		return node.setBody_MultipartFormData(node.source.GetBody())
	case managerpb.HttpRequestComponent_Body_TEXT_PLAIN:
		return node.setBody_TextPlain(node.source.GetBody())
	case managerpb.HttpRequestComponent_Body_APPLICATION_FORM_URLENCODED:
		return node.setBody_ApplicationFormUrlencoded(node.source.GetBody())
	}
	return nil
}

func (node *HttpNode) setBody_ApplicationJson(body *managerpb.HttpRequestComponent_Body) error {
	node.Request.Header.SetContentType("application/json")
	node.logW.SetHeader("content-type", "application/json")

	raw := body.GetRaw()
	bodyVal := ReplaceVars_Json(raw, node.task.VarsPool.GetNodeImports(node.Id()))
	node.Request.SetBodyString(bodyVal)
	node.logW.SetRequestBody(bodyVal)

	return nil
}

func (node *HttpNode) setBody_MultipartFormData(body *managerpb.HttpRequestComponent_Body) error {
	form := &multipart.Form{
		Value: make(map[string][]string),
	}

	for _, item := range body.GetFormData() {
		key := ReplaceVars(item.GetKey(), node.task.VarsPool.GetNodeImports(node.Id()))
		value := ReplaceVars(item.GetValue(), node.task.VarsPool.GetNodeImports(node.Id()))
		form.Value[key] = []string{value}
	}

	buff := bytes.NewBuffer(make([]byte, 0, 1024))
	boundary := "-------------589556666"
	err := fasthttp.WriteMultipartForm(buff, form, boundary)
	if err != nil {
		return node.UpdateFailureStatef(codes.NodeHTTPBodyFailure, "multipart: %#v, err: %s", form, err)
	}

	node.Request.Header.SetMultipartFormBoundary(boundary)
	node.logW.SetHeader("content-type", "multipart/form-data; boundary=-------------589556666")

	node.Request.SetBodyStream(buff, buff.Len())
	node.logW.SetRequestBody(buff.String())

	return nil
}

func (node *HttpNode) setBody_TextPlain(body *managerpb.HttpRequestComponent_Body) error {
	node.Request.Header.SetContentType("text/plain")
	node.logW.SetHeader("content-type", "text/plain")

	raw := ReplaceVars(body.GetRaw(), node.task.VarsPool.GetNodeImports(node.Id()))
	node.Request.SetBodyString(raw)
	node.logW.SetRequestBody(raw)

	return nil
}

func (node *HttpNode) setBody_ApplicationFormUrlencoded(body *managerpb.HttpRequestComponent_Body) error {
	node.Request.Header.SetContentType("application/x-www-form-urlencoded")
	node.logW.SetHeader("content-type", "application/x-www-form-urlencoded")

	formValues := url.Values{}
	for _, item := range body.GetFormData() {
		key := ReplaceVars(item.GetKey(), node.task.VarsPool.GetNodeImports(node.Id()))
		value := ReplaceVars(item.GetValue(), node.task.VarsPool.GetNodeImports(node.Id()))
		formValues.Add(key, value)
	}

	raw := formValues.Encode()
	node.Request.SetBodyString(raw)
	node.logW.SetRequestBody(raw)
	return nil
}

func (node *HttpNode) getClientWithTimeout() {
	timeout := node.source.GetTimeout()
	node.HttpClient = node.task.svcCtx.HttpClientPool.GetWithTimeout(
		timeout.GetConnectTimeout(), timeout.GetRequestTimeout(), timeout.GetResponseTimeout(),
	)
	node.logW.SetTimeout(timeout.GetConnectTimeout(), timeout.GetRequestTimeout(), timeout.GetResponseTimeout())
}

func (node *HttpNode) processingRespHeader() {
	node.respHeader = make(http.Header)
	node.Response.Header.VisitAll(
		func(key, value []byte) {
			node.respHeader.Add(string(key), string(value))
			node.logW.SetResponseHeader(string(key), string(value))
		},
	)
}

func (node *HttpNode) processingExports() {
	for _, exp := range node.source.GetExports() {
		if exp.GetName() == "" {
			// 忽略出参名为空的出参
			continue
		}

		switch exp.GetSource() {
		case managerpb.ResponseSource_BODY:
			node.processingExport_Body(exp)
		case managerpb.ResponseSource_HEADERS:
			node.processingExport_Headers(exp)
		case managerpb.ResponseSource_STATUS_CODE:
			node.processingExport_StatusCode(exp)
		}
	}
}

func (node *HttpNode) processingExport_Body(exp *managerpb.HttpRequestComponent_Export) {
	switch exp.GetBody().GetType() {
	case managerpb.HttpRequestComponent_Export_Body_JMESPATH:
		node.processingExport_Body_Jmespath(exp)
	case managerpb.HttpRequestComponent_Export_Body_REGEX:
		node.processingExport_Body_Regex(exp)
	}
}

func (node *HttpNode) processingExport_Body_Jmespath(exp *managerpb.HttpRequestComponent_Export) {
	name := exp.GetName()
	var value any
	expression := exp.GetBody().GetExpression()
	errcode := errorx.OK
	var err error
	defer func() {
		node.logW.SetExport_Body(
			name, int(managerpb.HttpRequestComponent_Export_Body_JMESPATH), expression, value, errcode,
			GetErrCode(errcode).String(), state.Error2Debug(err),
		)
	}()

	var data any
	// TODO: BUGFIX: jsonx默认使用了json.Number,但是jmespath不支持json.Number,因此这里使用官方的json
	err = json.Unmarshal(node.Response.Body(), &data)
	if err != nil {
		errcode = codes.NodeExportNotFound
		node.UpdateWarningStatef(codes.NodeExportNotFound, "body反序列化json错误, err: %s", err)
		return
	}

	expression = ReplaceVars(expression, node.task.VarsPool.GetNodeImports(node.Id()))
	value, err = jmespath.Search(expression, data)
	if err != nil {
		errcode = codes.NodeExportNotFound
		node.UpdateWarningStatef(
			codes.NodeExportNotFound, "jmespath.Search失败, expression: %s, err: %s", expression, err,
		)
		return
	}

	// 如果找不到会被赋予空值，但如果值本身就是null，则会同样出现报警
	if value == nil {
		errcode = codes.NodeExportNotFound
		node.UpdateWarningStatef(codes.NodeExportNotFound, "jmespath.Search 的key可能不存在. key: %s", expression)
	}

	// 统一：保证value输出的float64都是json.Number
	var jvalue any
	err = jsonx.UnmarshalFromString(jsonx.MarshalToStringIgnoreError(value), &jvalue)
	if err != nil {
		errcode = codes.NodeExportNotFound
		node.UpdateWarningStatef(codes.NodeExportNotFound, "jmespath.Search 的值可能不合法. value: %s", jvalue)
	}

	node.task.VarsPool.SetNodeExportVar(node.Id(), name, jvalue)
}

func (node *HttpNode) processingExport_Body_Regex(exp *managerpb.HttpRequestComponent_Export) {
	name := exp.GetName()
	var value any
	expression := exp.GetBody().GetExpression()
	var err error
	errcode := errorx.OK
	defer func() {
		node.logW.SetExport_Body(
			name, int(managerpb.HttpRequestComponent_Export_Body_REGEX), expression, value, errcode,
			GetErrCode(errcode).String(), state.Error2Debug(err),
		)
	}()

	body := node.Response.Body()

	expression = ReplaceVars(expression, node.task.VarsPool.GetNodeImports(node.Id()))
	regex, err := regexp.Compile(expression)
	if err != nil {
		errcode = codes.NodeExportNotFound
		node.UpdateWarningStatef(
			codes.NodeExportNotFound, "无效的regex表达式, expression: %s, err: %s", expression, err,
		)
		return
	}

	value = regex.FindString(string(body))
	node.task.VarsPool.SetNodeExportVar(node.Id(), exp.GetName(), value)
}

func (node *HttpNode) processingExport_Headers(exp *managerpb.HttpRequestComponent_Export) {
	name := exp.GetName()
	headerKey := exp.GetHeaders().GetKey()
	var value any
	defer func() {
		node.logW.SetExport_Header(name, headerKey, value, errorx.OK, GetErrCode(errorx.OK).String(), "")
	}()

	headerKey = ReplaceVars(headerKey, node.task.VarsPool.GetNodeImports(node.Id()))
	value = node.respHeader.Get(headerKey)
	node.task.VarsPool.SetNodeExportVar(node.Id(), name, value)
}

func (node *HttpNode) processingExport_StatusCode(exp *managerpb.HttpRequestComponent_Export) {
	name := exp.GetName()
	var value any = node.Response.StatusCode()
	defer func() {
		node.logW.SetExport_StatusCode(name, value, errorx.OK, GetErrCode(errorx.OK).String(), "")
	}()

	node.task.VarsPool.SetNodeExportVar(node.Id(), name, value)
}

func (node *HttpNode) processingAssertions() error {
	var err error
	for _, assert := range node.source.GetAssertions() {
		var nerr error
		// 运行完所有断言后再返回
		switch assert.GetSource() {
		case managerpb.ResponseSource_BODY:
			nerr = node.processingAssertion_Body(assert.GetBody())
		case managerpb.ResponseSource_HEADERS:
			nerr = node.processingAssertion_Headers(assert.GetHeaders())
		case managerpb.ResponseSource_STATUS_CODE:
			nerr = node.processingAssertion_StatusCode(assert.GetStatusCode())
		}

		if nerr != nil {
			err = nerr
		}
	}
	return err
}

func (node *HttpNode) processingAssertion_Body(body *managerpb.HttpRequestComponent_Assertion_Body) error {
	switch body.GetType() {
	case managerpb.HttpRequestComponent_Assertion_Body_JMESPATH:
		return node.processingAssertion_Body_Jmespath(body.GetJmespath())
	case managerpb.HttpRequestComponent_Assertion_Body_REGEX:
		return node.processingAssertion_Body_Regex(body.GetRegex())
	}
	return nil
}

func (node *HttpNode) processingAssertion_Body_Jmespath(data *managerpb.HttpRequestComponent_Assertion_Body_JMESPath) (err error) {
	expression := data.GetExpression()
	compare := data.GetCompare()
	var actual any
	var expect any
	errcode := errorx.OK

	defer func() {
		node.logW.SetAssertion_Body_Jmespath(
			expression, compare, actual, expect, errcode, GetErrCode(errcode).String(), state.Error2Debug(err),
		)
	}()

	var bodyData any
	// TODO: BUGFIX: jsonx默认使用了json.Number,但是jmespath不支持json.Number,因此这里使用官方的json
	err = json.Unmarshal(node.Response.Body(), &bodyData)
	if err != nil {
		errcode = codes.NodeHTTPAssertFailure
		// return node.UpdateFailureStatef(codes.NodeHTTPAssertFailure, "body反序列化json错误, err: %s", err)
		return NewHTTPErrorWithMessagef(codes.NodeHTTPAssertFailure, "body反序列化json错误, err: %s", err)
	}

	expression = ReplaceVars(expression, node.task.VarsPool.GetNodeImports(node.Id()))

	actual, err = jmespath.Search(expression, bodyData)
	if err != nil {
		errcode = codes.NodeHTTPAssertFailure
		//return node.UpdateFailureStatef(
		//	codes.NodeHTTPAssertFailure, "jmespath.Search失败, expression: %s, err: %s", expression, err,
		//)
		return NewHTTPErrorWithMessagef(
			codes.NodeHTTPAssertFailure, "jmespath.Search失败, expression: %s, err: %s", expression, err,
		)
	}

	// expect是手工输入值
	expect, err = node.GetVariable_Manual(&managerpb.VariableManual{Value: data.GetExpectation()})
	if err != nil {
		errcode = codes.NodeHTTPAssertFailure
		//return node.UpdateFailureStatef(
		//	codes.NodeHTTPAssertFailure, "获取断言期望值失败,请检查格式是否符合json。 expectation: %s, err: %s",
		//	data.GetExpectation(), err,
		//)
		return NewHTTPErrorWithMessagef(
			codes.NodeHTTPAssertFailure,
			"获取断言期望值失败,请检查格式是否符合json。 expectation: %s, err: %s",
			data.GetExpectation(), err,
		)
	}

	isOk := Compare(CompareType(compare), actual, expect)
	if !isOk {
		errcode = codes.NodeHTTPAssertFailure
		//return node.UpdateFailureStatef(
		//	codes.NodeHTTPAssertFailure, "值不符合预期。 compare: %s, actual: %#v, expect: %#v", data.GetCompare(),
		//	actual, expect,
		//)
		return NewHTTPErrorWithMessagef(
			codes.NodeHTTPAssertFailure,
			"值不符合预期。 compare: %s, actual: %#v, expect: %#v",
			data.GetCompare(), actual, expect,
		)
	}
	return nil
}

func (node *HttpNode) processingAssertion_Body_Regex(data *managerpb.HttpRequestComponent_Assertion_Body_Regex) (err error) {
	expression := data.GetExpression()
	actual := string(node.Response.Body())
	errcode := errorx.OK
	defer func() {
		node.logW.SetAssertion_Body_Regex(
			expression, string(CompareType_RE), actual, errcode, GetErrCode(errcode).String(), state.Error2Debug(err),
		)
	}()

	expression = ReplaceVars(expression, node.task.VarsPool.GetNodeImports(node.Id()))
	isOk := Compare(CompareType_RE, actual, expression)
	if !isOk {
		errcode = codes.NodeHTTPAssertFailure
		//return node.UpdateFailureStatef(
		//	codes.NodeHTTPAssertFailure, "值不符合预期。 compare: %s, actual: %#v, expect: %#v", string(CompareType_RE),
		//	string(node.Response.Body()), expression,
		//)
		return NewHTTPErrorWithMessagef(
			codes.NodeHTTPAssertFailure,
			"值不符合预期。 compare: %s, actual: %#v, expect: %#v",
			string(CompareType_RE), string(node.Response.Body()), expression,
		)
	}

	return nil
}

func (node *HttpNode) processingAssertion_Headers(headers *managerpb.HttpRequestComponent_Assertion_Headers) (err error) {
	headerKey := ReplaceVars(headers.GetKey(), node.task.VarsPool.GetNodeImports(node.Id()))
	compare := headers.GetCompare()
	actual := node.respHeader.Get(headerKey)
	var expect any
	errcode := errorx.OK

	defer func() {
		node.logW.SetAssertion_Header(
			headerKey, compare, actual, expect, errcode, GetErrCode(errcode).String(), state.Error2Debug(err),
		)
	}()

	expect, err = node.GetVariable_Manual(&managerpb.VariableManual{Value: headers.GetExpression()})
	if err != nil {
		errcode = codes.NodeHTTPAssertFailure
		//return node.UpdateFailureStatef(
		//	codes.NodeHTTPAssertFailure, "获取断言期望值失败,请检查格式是否符合json。 expression: %s, err: %s",
		//	headers.GetExpression(), err,
		//)
		return NewHTTPErrorWithMessagef(
			codes.NodeHTTPAssertFailure,
			"获取断言期望值失败,请检查格式是否符合json。 expression: %s, err: %s",
			headers.GetExpression(), err,
		)
	}

	isOk := Compare(CompareType(compare), actual, expect)
	if !isOk {
		errcode = codes.NodeHTTPAssertFailure
		//return node.UpdateFailureStatef(
		//	codes.NodeHTTPAssertFailure, "值不符合预期。 compare: %s, actual: %#v, expect: %#v", headers.GetCompare(),
		//	actual, expect,
		//)
		return NewHTTPErrorWithMessagef(
			codes.NodeHTTPAssertFailure,
			"值不符合预期。 compare: %s, actual: %#v, expect: %#v",
			headers.GetCompare(), actual, expect,
		)
	}
	return nil
}

func (node *HttpNode) processingAssertion_StatusCode(scode *managerpb.HttpRequestComponent_Assertion_StatusCode) (err error) {
	errcode := errorx.OK
	compare := scode.GetCompare()
	actual := node.Response.StatusCode()
	var expect any
	defer func() {
		node.logW.SetAssertion_StatusCode(
			compare, actual, expect, errcode, GetErrCode(errcode).String(), state.Error2Debug(err),
		)
	}()

	actual = node.Response.StatusCode()
	expect, err = node.GetVariable_Manual(&managerpb.VariableManual{Value: scode.GetExpectation()})
	if err != nil {
		errcode = codes.NodeHTTPAssertFailure
		//return node.UpdateFailureStatef(
		//	codes.NodeHTTPAssertFailure, "获取断言期望值失败,请检查格式是否符合json。 expression: %s, err: %s",
		//	scode.GetExpectation(), err,
		//)
		return NewHTTPErrorWithMessagef(
			codes.NodeHTTPAssertFailure,
			"获取断言期望值失败,请检查格式是否符合json。 expression: %s, err: %s",
			scode.GetExpectation(), err,
		)
	}

	isOk := Compare(CompareType(compare), actual, expect)
	if !isOk {
		errcode = codes.NodeHTTPAssertFailure
		//return node.UpdateFailureStatef(
		//	codes.NodeHTTPAssertFailure, "值不符合预期。 compare: %s, actual: %#v, expect: %#v", scode.GetCompare(),
		//	actual, expect,
		//)
		return NewHTTPErrorWithMessagef(
			codes.NodeHTTPAssertFailure, "值不符合预期。 compare: %s, actual: %#v, expect: %#v",
			scode.GetCompare(), actual, expect,
		)
	}
	return nil
}
