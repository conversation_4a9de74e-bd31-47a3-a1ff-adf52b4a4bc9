package apitesttask

type ReferComponentGroupNode_Log struct {
	*BaseComboNode_Log
	ReferId string
}

type ReferComponentGroupNodeLogWriter struct {
	*BaseComboNodeLogWriter

	log *ReferComponentGroupNode_Log
}

func NewReferComponentGroupNodeLogWriter(bw *BaseComboNodeLogWriter) *ReferComponentGroupNodeLogWriter {
	writer := &ReferComponentGroupNodeLogWriter{
		BaseComboNodeLogWriter: bw,
		log: &ReferComponentGroupNode_Log{
			BaseComboNode_Log: bw.log,
		},
	}
	return writer
}

func (writer *ReferComponentGroupNodeLogWriter) toJson() string {
	return log2Json(writer.log)
}

func (writer *ReferComponentGroupNodeLogWriter) SetReferId(id string) {
	writer.log.ReferId = id
}
