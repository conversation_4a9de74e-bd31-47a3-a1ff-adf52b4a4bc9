package apitesttask

import (
	"encoding/json"
	"testing"
)

func TestReplaceVarsJson(t *testing.T) {
	tpldata := `
{
	"string_key": {{.string_value}},
	"null_key": {{.null_value}},
	"bool_key": {{.bool_value}},
	"object_key": {{.object_value}},
	"array_key": {{.array_value}},
	"int_number_key": {{.int_number_value}},
	"json_number_key": {{.json_number_value}},
	"float_number_key": {{.float_number_value}},
	"original_string_key": "{{toText .string_value}}"
}
`
	vars := map[string]any{
		"string_value": "str",
		"null_value":   nil,
		"bool_value":   true,
		"object_value": map[string]any{
			"k1": 1,
			"k2": "v2",
		},
		"array_value":        []any{1, 2, 3, "v1", "v2", "v3"},
		"int_number_value":   int64(10),
		"json_number_value":  json.Number("1.234"),
		"float_number_value": float64(123456789.12345),
	}

	result := ReplaceVars_Json(tpldata, vars)
	t.Log(result)
}

func TestCompare(t *testing.T) {
	valuelist := []struct {
		Compare CompareType
		V1      any
		V2      any
	}{
		{CompareType_GT, int64(10), int32(5)},
		{CompareType_GT, json.Number("10"), int64(5)},
		{CompareType_GT, float64(10), int32(5)},
		{CompareType_CONTAINS, []any{int64(10), json.Number("5"), []any{10}}, int32(5)},
		{CompareType_RE, "localhost", "loca+l"},
		{CompareType_LEN_EQ, "请求成功", 4},
		{CompareType_RE, false, "^(?i)(true|false)$"},
	}

	for _, v := range valuelist {
		t.Logf(
			"[%v] %s, V1:%T:%v, V2:%T:%v",
			Compare(v.Compare, v.V1, v.V2),
			v.Compare,
			v.V1, v.V1,
			v.V2, v.V2,
		)
	}
}
