package apitesttask

import (
	cryptorand "crypto/rand"
	"math/big"
	mathrand "math/rand"
	"testing"
	"time"
)

func init() {
	mathrand.Seed(time.Now().UnixNano())
}

func TestMathRand(t *testing.T) {
	var (
		times       = 30
		max   int64 = 4

		m = make(map[int64]int64, times)
	)
	for i := 0; i < times; i++ {
		n := mathrand.Int63n(max)
		if _, ok := m[n]; ok {
			m[n] += 1
		} else {
			m[n] = 1
		}
	}

	for k, v := range m {
		t.Logf("%d: %d", k, v)
	}
}

func TestCryptoRand(t *testing.T) {
	var (
		times       = 30
		max   int64 = 4
		min   int64 = 0

		m = make(map[int64]int64, times)
	)
	for i := 0; i < times; i++ {
		n, err := cryptorand.Int(cryptorand.Reader, big.NewInt(max-min))
		if err != nil {
			t.<PERSON>al(err)
		}

		i64 := n.Int64()
		if _, ok := m[i64]; ok {
			m[i64] += 1
		} else {
			m[i64] = 1
		}
		// t.Logf("%d: %d", i, i64)
	}

	for k, v := range m {
		t.Logf("%d: %d", k, v)
	}
}

func Test_random(t *testing.T) {
	type args struct {
		max int64
	}
	tests := []struct {
		name  string
		args  args
		times int
	}{
		{
			name: "random 10",
			args: args{
				max: 4,
			},
			times: 10,
		},
		{
			name: "random 20",
			args: args{
				max: 4,
			},
			times: 20,
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				m := make(map[int64]int64, tt.times)
				for i := 0; i <= tt.times; i++ {
					if got := random(tt.args.max); got >= tt.args.max {
						t.Errorf("random() = %v, max %v", got, tt.args.max)
					} else if _, ok := m[got]; ok {
						m[got] += 1
					} else {
						m[got] = 1
					}
				}

				for k, v := range m {
					t.Logf("%d: %d", k, v)
				}
			},
		)
	}
}
