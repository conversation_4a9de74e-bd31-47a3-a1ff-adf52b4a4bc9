package apitesttask

import (
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/codes"
	managerpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

// TeardownNode 后置组件
type TeardownNode struct {
	*BaseComboNode

	source *managerpb.TeardownComponent
	logW   *TeardownNodeLogWriter
}

func NewTeardownNode(task *ApitestTask, data *managerpb.ApiExecutionData, parent Node, isShell bool) (Node, error) {
	node := &TeardownNode{}
	var err error
	node.BaseComboNode, err = NewBaseComboNode(task, data, node, parent, isShell)
	if err != nil {
		return nil, node.UpdateInvalidStatef(codes.NodeInvalid, "%s", err)
	}

	source := data.GetTeardown()
	node.source = source
	node.SetImports(node.source.GetImports())

	node.logW = NewTeardownNodeLogWriter(node.BaseComboNode.logW)

	err = node.NewChildren(task, data)
	if err != nil {
		return node, node.UpdateInvalidStatef(codes.NodeInvalid, "%s", err)
	}

	return node, nil
}

func (node *TeardownNode) Skip() bool {
	// 后置组件必须执行
	// 其子组件按正常逻辑运行
	node.task.SetRunAble(true)
	return !node.task.RunAble()
}

func (node *TeardownNode) Content() string {
	return node.logW.toJson()
}

func (node *TeardownNode) Logger() NodeLogger {
	return node.logW
}
