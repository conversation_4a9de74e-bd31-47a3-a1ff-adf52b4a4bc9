package apitesttask

import (
	"fmt"

	"github.com/zeromicro/go-zero/core/jsonx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	managerpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

func log2Json(l any) string {
	j, _ := jsonx.MarshalToString(l)
	return j
}

type NodeLogger interface {
	SetCommon(node Node)
}

type BaseNode_Log_Common_Error struct {
	Code    errorx.Code `json:"code"`    // 错误码
	Message string      `json:"message"` // 提示信息,提供code的具体信息
	Debug   string      `json:"debug"`   // 用于展示具体的错误报告
}

func getBaseNodeLogCommonError(code errorx.Code, msg, debug string) BaseNode_Log_Common_Error {
	l := BaseNode_Log_Common_Error{
		Code:    code,
		Message: msg,
		Debug:   debug,
	}
	return l
}

type BaseNode_Log_Common struct {
	BaseNode_Log_Common_Error
	NodeData struct {
		TaskId        string `json:"task_id"`         // 任务id
		ExecuteId     string `json:"execute_id"`      // 任务id
		NodeId        string `json:"node_id"`         // 节点id
		NodeType      string `json:"node_type"`       // 节点类型
		Level         int    `json:"level"`           // 节点层级
		RankScore     int    `json:"rank_score"`      // 节点执行序号,表示这是第几个执行的组（第几个被初始化）
		ParentId      string `json:"parent_id"`       // 父节点id
		ParentComboId string `json:"parent_combo_id"` // 父框组件id
	} `json:"node_data"`
	TaskData *BaseNode_Log_Common_TaskData `json:"task_data,omitempty"`
	RpcData  []string                      `json:"rpc_data,omitempty"`
}

type BaseNode_Log_Common_TaskData struct {
	Base64Pb string `json:"base64_pb,omitempty"`
}

func getBaseNodeLogCommon(node Node) BaseNode_Log_Common {
	errcode, msg, debug := node.Error()
	common := BaseNode_Log_Common{
		BaseNode_Log_Common_Error: getBaseNodeLogCommonError(errcode, msg, debug),
		RpcData:                   make([]string, 0, 10),
	}
	common.NodeData.TaskId = node.Task().Source().GetTaskId()
	common.NodeData.ExecuteId = node.Task().Source().GetExecuteId()
	common.NodeData.NodeId = node.Id()
	common.NodeData.NodeType = node.Type()
	common.NodeData.Level = node.Level()
	common.NodeData.RankScore = node.RankScore()
	common.NodeData.ParentId = node.ParentId()
	common.NodeData.ParentComboId = node.ParentComboId()
	if node.Shell() && node.Task().Debug() {
		common.TaskData = &BaseNode_Log_Common_TaskData{
			Base64Pb: node.Task().Base64RequestPb(),
		}
	}

	return common
}

type BaseNode_Log_Children []BaseNode_Log_Common

type BaseNode_Log_Variable struct {
	Name        string                         `json:"name"`
	Source      managerpb.VariableSource       `json:"source"`
	Manual      *managerpb.VariableManual      `json:"manual,omitempty"`
	Export      *managerpb.VariableExport      `json:"export,omitempty"`
	Environment *managerpb.VariableEnvironment `json:"environment,omitempty"`
	Function    *managerpb.VariableFunction    `json:"function,omitempty"`
}

func getBaseNodeLogVariable(name string, variable VariableGetter) BaseNode_Log_Variable {
	v := BaseNode_Log_Variable{
		Name:        name,
		Source:      variable.GetSource(),
		Manual:      variable.GetManual(),
		Export:      variable.GetExport(),
		Environment: variable.GetEnvironment(),
		Function:    variable.GetFunction(),
	}
	return v
}

type BaseNode_Log_Import struct {
	BaseNode_Log_Common_Error
	BaseNode_Log_Variable
	Actual string `json:"actual"` // json.encode(入参值)
}

func getBaseNodeLogImport(
	name string, val any, variable VariableGetter, errcode errorx.Code, errmsg, debug string,
) BaseNode_Log_Import {
	_import := BaseNode_Log_Import{
		BaseNode_Log_Common_Error: getBaseNodeLogCommonError(errcode, errmsg, debug),
		BaseNode_Log_Variable:     getBaseNodeLogVariable(name, variable),
		Actual:                    jsonx.MarshalToStringIgnoreError(val),
	}
	return _import
}

type BaseNode_Log_Imports []BaseNode_Log_Import

type BaseNode_Log_Export struct {
	BaseNode_Log_Common_Error

	Name   string `json:"name"`   // 出参变量名
	Actual string `json:"actual"` // json.encode(出参值)
	Export struct {
		NodeId string `json:"node_id"` // 来源节点id
		Value  string `json:"value"`   // 变量名
	} `json:"export,omitempty"`
}

func getBaseNodeLogExport(
	name string, val any, variable *managerpb.VariableExport, errcode errorx.Code, errmsg, debug string,
) BaseNode_Log_Export {
	export := BaseNode_Log_Export{
		BaseNode_Log_Common_Error: getBaseNodeLogCommonError(errcode, errmsg, debug),
	}
	export.Name = name
	export.Actual = jsonx.MarshalToStringIgnoreError(val)
	if variable != nil {
		export.Export.NodeId = variable.GetNodeId()
		export.Export.Value = variable.GetValue()
	}

	return export
}

type BaseNode_Log_Exports []BaseNode_Log_Export

type BaseNodeLogWriter struct {
	log *BaseNode_Log_Common
}

func NewBaseNodeLogWriter() *BaseNodeLogWriter {
	writer := &BaseNodeLogWriter{
		log: &BaseNode_Log_Common{},
	}
	return writer
}

func (writer *BaseNodeLogWriter) SetCommon(node Node) {
	// 直接修改指针变量内的值
	c := getBaseNodeLogCommon(node)
	_ = utils.Copy(writer.log, &c)
}

func (writer *BaseNodeLogWriter) SetRpc(rpcScore int, rpcName, base64pb string) {
	writer.log.RpcData = append(writer.log.RpcData, fmt.Sprintf("%d:%s:%s", rpcScore, rpcName, base64pb))
}

func (writer *BaseNodeLogWriter) toJson() string {
	return log2Json(writer.log)
}
