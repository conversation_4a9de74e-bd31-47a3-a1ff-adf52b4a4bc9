package apitesttask

import (
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/codes"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/state"
)

var codeToStrMap = map[errorx.Code]string{
	errorx.OK:            "成功",
	errorx.Failure:       "失败",
	errorx.InternalError: "内部错误",

	codes.TaskPanic:            "任务处理异常",
	codes.TaskUnmarshalFailure: "任务数据异常",

	codes.NodePanic:            "节点处理异常",
	codes.NodeNotRegister:      "节点未注册",
	codes.NodeNotFunc:          "节点未实现方法",
	codes.NodeRecordFailure:    "更新运行记录失败",
	codes.NodeWriteFileFailure: "写文件失败",
	codes.NodeInvalid:          "节点无效",

	codes.NodePoolAccountReleaseFailure:  "池账号释放失败",
	codes.NodePoolAccountQueryEnvFailure: "获取账号配置失败",
	codes.NodePoolAccountInvalidType:     "类型转换异常",

	codes.NodeHTTPBodyFailure: "创建请求体异常",

	codes.TaskInvalidBaseUrl: "无效的全局url配置",

	codes.NodeSkip:           "节点被跳过",
	codes.NodeChildFailure:   "子节点处理异常",
	codes.NodeImportNotFound: "入参未找到",
	codes.NodeExportNotFound: "出参未找到",

	codes.NodeAssertFailure:                "断言失败",
	codes.NodeAssertFailureInvalidActual:   "断言失败，实际值无效",
	codes.NodeAssertFailureInvalidExpected: "断言失败，期望值无效",
	codes.NodeAssertFailureCompareFailure:  "断言失败，实际值与期望值不符",

	codes.NodeFunctionArgsNotFound: "函数执行存在异常，参数未找到",
	codes.NodeFunctionExecFailure:  "函数执行失败，执行失败",

	codes.NodeHTTPInvalidUrl:     "无效的url",
	codes.NodeHTTPRequestFailure: "http请求失败",
	codes.NodeHTTPAssertFailure:  "断言失败",

	codes.NodeAccountNotEnv:       "没有提供账号环境配置",
	codes.NodePoolAccountNotFound: "找不到满足条件的账号",

	codes.NodeConditionLeftValueNotFound:  "左值没找到",
	codes.NodeConditionRightValueNotFound: "右值没找到",
	codes.NodeConditionAssertFailure:      "左值与右值不对等",

	codes.NodeSQLParamInvalid: "参数无效",
	codes.NodeSQLExecFailure:  "执行失败",
}

func GetErrCode(code errorx.Code) state.IErrCode {
	return state.GetErrCode(code, codeToStrMap[code], "")
}
