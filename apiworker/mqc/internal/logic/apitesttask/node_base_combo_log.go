package apitesttask

import (
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"

	managerpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type BaseComboNode_Log struct {
	*BaseNode_Log_Common
	Imports  BaseNode_Log_Imports  `json:"imports"`
	Exports  BaseNode_Log_Exports  `json:"exports"`
	Children BaseNode_Log_Children `json:"children"`
}

type BaseComboNodeLogWriter struct {
	*BaseNodeLogWriter

	log *BaseComboNode_Log
}

func NewBaseComboNodeLogWriter(bw *BaseNodeLogWriter) *BaseComboNodeLogWriter {
	writer := &BaseComboNodeLogWriter{
		BaseNodeLogWriter: bw,
		log: &BaseComboNode_Log{
			BaseNode_Log_Common: bw.log,
			Imports:             make(BaseNode_Log_Imports, 0, 10),
			Exports:             make(BaseNode_Log_Exports, 0, 10),
			Children:            make(BaseNode_Log_Children, 0, 10),
		},
	}
	return writer
}

func (writer *BaseComboNodeLogWriter) SetImport(
	key string, val any, variable VariableGetter, errcode errorx.Code, errmsg, debug string,
) {
	writer.log.Imports = append(writer.log.Imports, getBaseNodeLogImport(key, val, variable, errcode, errmsg, debug))
}

func (writer *BaseComboNodeLogWriter) SetExport(
	key string, val any, variable *managerpb.VariableExport, errcode errorx.Code, errmsg, debug string,
) {
	writer.log.Exports = append(writer.log.Exports, getBaseNodeLogExport(key, val, variable, errcode, errmsg, debug))
}

func (writer *BaseComboNodeLogWriter) SetChild(node Node) {
	writer.log.Children = append(writer.log.Children, getBaseNodeLogCommon(node))
}

func (writer *BaseComboNodeLogWriter) toJson() string {
	return log2Json(writer.log)
}
