package apitesttask

import (
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/codes"
	managerpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type ReferComponentGroupNode struct {
	*BaseComboNode
	source *managerpb.ReferenceComponent
	logW   *ReferComponentGroupNodeLogWriter

	ReferId string // 引用的组件id
}

func NewReferComponentGroupNode(task *ApitestTask, data *managerpb.ApiExecutionData, parent Node, isShell bool) (
	Node, error,
) {
	node := &ReferComponentGroupNode{}
	var err error
	node.BaseComboNode, err = NewBaseComboNode(task, data, node, parent, isShell)
	if err != nil {
		return nil, node.UpdateInvalidStatef(codes.NodeInvalid, "%s", err)
	}

	// source数据
	source := data.GetReference()
	node.source = source
	node.ReferId = source.GetReferenceComponentId()
	node.SetVersion(source.GetVersion())
	node.SetImports(node.source.GetImports())
	node.SetExports(node.source.GetExports())

	node.logW = NewReferComponentGroupNodeLogWriter(node.BaseComboNode.logW)
	node.logW.SetReferId(node.ReferId)

	// 初始化子节点, 引用组件的子节点就是被引用的组件(单组件/组件组)
	err = node.NewChildren(task, data)
	if err != nil {
		return node, node.UpdateInvalidStatef(codes.NodeInvalid, "%s", err)
	}

	return node, nil
}

func (node *ReferComponentGroupNode) run() (err error) {
	// 配置入参
	node.processingImports()

	err = node.runChild()

	// 配置出参
	node.processingExports()
	return err
}

func (node *ReferComponentGroupNode) processingExports() {
	for _, exp := range node.Exports {
		node.processingExport(exp)
	}
}

func (node *ReferComponentGroupNode) processingExport(exp *managerpb.Export) {
	key := exp.GetName()
	var value any
	logExp := &managerpb.VariableExport{}
	_ = utils.Copy(logExp, exp.GetExport())
	errcode := errorx.OK

	if key == "" {
		// 忽略key为空的出参
		return
	}

	defer func() {
		logExp.NodeId = node.ReferId
		logExp.Value = key
		node.logW.SetExport(key, value, logExp, errcode, GetErrCode(errcode).String(), "")
	}()

	// 出参来源于被引用组件的出参
	value, ok := node.task.VarsPool.GetNodeExportVar(node.ReferId, key)
	if !ok {
		errcode = codes.NodeExportNotFound
		node.UpdateWarningStatef(codes.NodeExportNotFound, "referid: %s, key: %s", node.ReferId, key)
	}
	node.task.VarsPool.SetNodeExportVar(node.Id(), key, value)
}

func (node *ReferComponentGroupNode) Content() string {
	return node.logW.toJson()
}

func (node *ReferComponentGroupNode) Logger() NodeLogger {
	return node.logW
}
