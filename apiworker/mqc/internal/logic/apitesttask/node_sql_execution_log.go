package apitesttask

import (
	"github.com/zeromicro/go-zero/core/jsonx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
)

type SqlExecutionNode_Log struct {
	*BaseNode_Log_Common
	Sqls []SqlExecutionNode_Log_SQL `json:"sqls"`
}

type SqlExecutionNode_Log_SQL struct {
	BaseNode_Log_Common_Error
	Host     BaseNode_Log_Import             `json:"host"`
	Port     BaseNode_Log_Import             `json:"port"`
	User     BaseNode_Log_Import             `json:"user"`
	Password BaseNode_Log_Import             `json:"password"`
	Database BaseNode_Log_Import             `json:"database"`
	Url      string                          `json:"url"`
	CostMs   int64                           `json:"cost_ms"`
	Export   SqlExecutionNode_Log_SQL_Export `json:"export"`
}

type SqlExecutionNode_Log_SQL_Export struct {
	BaseNode_Log_Common_Error
	Name   string `json:"name"`
	Actual string `json:"actual"`
}

type SqlExecutionNodeLogWriter struct {
	*BaseNodeLogWriter

	log *SqlExecutionNode_Log
}

func NewSqlExecutionNodeLogWriter(bw *BaseNodeLogWriter) *SqlExecutionNodeLogWriter {
	writer := &SqlExecutionNodeLogWriter{
		BaseNodeLogWriter: bw,
		log: &SqlExecutionNode_Log{
			BaseNode_Log_Common: bw.log,
			Sqls:                make([]SqlExecutionNode_Log_SQL, 0, 10),
		},
	}
	return writer
}

func (writer *SqlExecutionNodeLogWriter) SetSQL(
	sql SqlExecutionNode_Log_SQL, errcode errorx.Code, errmsg, debug string,
) {
	writer.log.Sqls = append(writer.log.Sqls, sql)
}

func (writer *SqlExecutionNodeLogWriter) SetSQL_Host(
	sql *SqlExecutionNode_Log_SQL, key string, val any, variable VariableGetter, errcode errorx.Code, errmsg string,
	debug string,
) {
	sql.Host = getBaseNodeLogImport(key, val, variable, errcode, errmsg, debug)
}

func (writer *SqlExecutionNodeLogWriter) SetSQL_Port(
	sql *SqlExecutionNode_Log_SQL, key string, val any, variable VariableGetter, errcode errorx.Code, errmsg string,
	debug string,
) {
	sql.Port = getBaseNodeLogImport(key, val, variable, errcode, errmsg, debug)
}

func (writer *SqlExecutionNodeLogWriter) SetSQL_User(
	sql *SqlExecutionNode_Log_SQL, key string, val any, variable VariableGetter, errcode errorx.Code, errmsg string,
	debug string,
) {
	sql.User = getBaseNodeLogImport(key, val, variable, errcode, errmsg, debug)
}

func (writer *SqlExecutionNodeLogWriter) SetSQL_Password(
	sql *SqlExecutionNode_Log_SQL, key string, val any, variable VariableGetter, errcode errorx.Code, errmsg string,
	debug string,
) {
	sql.Password = getBaseNodeLogImport(key, val, variable, errcode, errmsg, debug)
}

func (writer *SqlExecutionNodeLogWriter) SetSQL_Database(
	sql *SqlExecutionNode_Log_SQL, key string, val any, variable VariableGetter, errcode errorx.Code, errmsg string,
	debug string,
) {
	sql.Database = getBaseNodeLogImport(key, val, variable, errcode, errmsg, debug)
}

func (writer *SqlExecutionNodeLogWriter) SetSQL_Url(sql *SqlExecutionNode_Log_SQL, url string) {
	sql.Url = url
}

func (writer *SqlExecutionNodeLogWriter) SetSQL_CostMs(sql *SqlExecutionNode_Log_SQL, ms int64) {
	sql.CostMs = ms
}

func (writer *SqlExecutionNodeLogWriter) setSQL_Export(sql *SqlExecutionNode_Log_SQL, key string, val any) {
	sql.Export = SqlExecutionNode_Log_SQL_Export{
		BaseNode_Log_Common_Error: getBaseNodeLogCommonError(errorx.OK, GetErrCode(errorx.OK).String(), ""),
		Name:                      key,
		Actual:                    jsonx.MarshalToStringIgnoreError(val),
	}
}

func (writer *SqlExecutionNodeLogWriter) toJson() string {
	return log2Json(writer.log)
}
