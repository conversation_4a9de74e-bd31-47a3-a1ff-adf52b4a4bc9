package apitesttask

import (
	"fmt"
	"testing"

	"github.com/zeromicro/go-zero/core/jsonx"
)

// 获取线上环境mock数据：
// https://bi.skyengine.com.cn/product/view.htm?module=dashboard&productId=f76dae8c-54b0-47c8-bab9-0051095ee004&menuId=ekkbihi0uto

func TestTask(t *testing.T) {
	base64pb := "CpAECh1jYXNlX2lkOkpURlJTQWNwYVViTDhicWlPQng2ZBACGoIDCikKIlNUQVJUXzF1NmpmMy1jYTQ3S0U1WHdsb1FxSTZCYlc1TmMQC6oBAAqrAgoqU1FMX0VYRUNVVElPTl8xdjh6ZmctaXFmVnpwMlFsamF3eWozd08wRlRwEDHaA/kBCvYBEikSEQoPIjE5Mi4xNjguMjIuODciGgYKASASASAiACoKGggQARoAIgAqABoeEgYKBDMzMDYaBgoBIBIBICIAKgoaCBABGgAiACoAIiASCAoGInJvb3QiGgYKASASASAiACoKGggQARoAIgAqAComEg4KDCJRdXdhbkAyMDIwIhoGCgEgEgEgIgAqChoIEAEaACIAKgAyIBILCgkibWFuYWdlciIaAwoBICIAKgoaCBABGgAiACoAOi9zZWxlY3QgaWQsIHByb2plY3RfaWQsIGNyZWF0ZWRfYXQgZnJvbSBwcm9qZWN0O0IJU1FM57uE5Lu2SIgnCicKIEVORF9lMzEycjktRlNSZXllTnZydG9NM0x0R1EzQzBpEAyyAQBiaAogcHJvamVjdF9pZDpTQ2FXaXRqUDlid0FXQVZ5cTg4V1ESHWNhc2VfaWQ6SlRGUlNBY3BhVWJMOGJxaU9CeDZkGgEtIgEtKh92ZXJzaW9uOjIwMjIxMTE2MTEyMzQ4MjI2OmdGMVE4EoEDGiBwcm9qZWN0X2lkOlNDYVdpdGpQOWJ3QVdBVnlxODhXUSIddGFza19pZDpJUkdWal91NFEydjAyWnpsTzM0OVIqIGV4ZWN1dGVfaWQ6Z3ZZekJwUDBQc25EczhzRXZUc243MAJCZgpCCh1jYXNlX2lkOkpURlJTQWNwYVViTDhicWlPQng2ZBACIh92ZXJzaW9uOjIwMjIxMTE2MTEyMzQ4MjI2OmdGMVE4GiBleGVjdXRlX2lkOmd2WXpCcFAwUHNuRHM4c0V2VHNuN0qhAQogcHJvamVjdF9pZDpTQ2FXaXRqUDlid0FXQVZ5cTg4V1ESJ2dlbmVyYWxfY29uZmlnX2lkOjFraU9JNW9oLXg2MTZfRFpyUklwQRoOODfmtYvor5Xnjq/looMqFGh0dHA6Ly8xOTIuMTY4LjIyLjg3Oi4KCnByb2plY3RfaWQSIHByb2plY3RfaWQ6U0NhV2l0alA5YndBV0FWeXE4OFdRWgVlbXB0eWIFZW1wdHloAQ=="
	task := Mock_ApitestTask()
	task_params := Mock_WorkerReq_from_data(base64pb)

	fmt.Println("task_params =", jsonx.MarshalToStringIgnoreError(task_params))

	// mock_register_rpc_data(task, 7, []string{"1:rpc.account.prod:************************************************************************"})

	err := task.Run(task_params)
	if err != nil {
		t.FailNow()
	}
}
