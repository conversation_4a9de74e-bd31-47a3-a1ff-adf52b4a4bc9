package apitesttask

import (
	"github.com/zeromicro/go-zero/core/jsonx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
)

type HttpNode_Log struct {
	*BaseNode_Log_Common

	Imports    BaseNode_Log_Imports    `json:"imports"`
	Exports    HttpNode_Log_Exports    `json:"exports"`
	Timeout    HttpNode_Log_Timeout    `json:"timeout"`
	Request    HttpNode_Log_Request    `json:"request"`
	Response   HttpNode_Log_Response   `json:"response"`
	Assertions HttpNode_Log_Assertions `json:"assertions"`
}

type HttpNode_Log_Export struct {
	BaseNode_Log_Common_Error
	Name    string `json:"name"`
	Source  int    `json:"source"`
	Headers struct {
		Key string `json:"key"`
	} `json:"headers"`
	Body struct {
		Type       int    `json:"type"`
		Expression string `json:"expression"`
	} `json:"body"`
	Actual string `json:"actual"`
}

type HttpNode_Log_Exports []HttpNode_Log_Export

type HttpNode_Log_Timeout struct {
	ConnectTimeout  int64 `json:"connect_timeout"`
	RequestTimeout  int64 `json:"request_timeout"`
	ResponseTimeout int64 `json:"response_timeout"`
}

type HttpNode_Log_Request struct {
	BaseNode_Log_Common_Error
	Url         string                             `json:"url"`
	Method      string                             `json:"method"`
	Headers     []HttpNode_Log_Header              `json:"headers"`
	QueryParams []HttpNode_Log_Request_QueryParams `json:"query_params"`
	Body        string                             `json:"body"`
	CostMs      int64                              `json:"cost_ms"`
}

type HttpNode_Log_Header struct {
	Key   string `json:"key"`
	Value string `json:"value"`
}

type HttpNode_Log_Request_QueryParams struct {
	Key   string `json:"key"`
	Value string `json:"value"`
}

type HttpNode_Log_Response struct {
	StatusCode int                   `json:"status_code"`
	Headers    []HttpNode_Log_Header `json:"haeders"`
	Body       string                `json:"body"`
}

type HttpNode_Log_Assertion struct {
	BaseNode_Log_Common_Error
	Source  int `json:"source"`
	Headers struct {
		Key        string `json:"key"`
		Compare    string `json:"compare"`
		Expression string `json:"expression"`
	} `json:"headers"`
	Body struct {
		Type     int `json:"type"`
		Jmespath struct {
			Expression  string `json:"expression"`
			Compare     string `json:"compare"`
			Expectation string `json:"expectation"`
		} `json:"jmespath"`
		Regex struct {
			Compare    string `json:"compare"`
			Expression string `json:"expression"`
		} `json:"regex"`
	} `json:"body"`
	StatusCode struct {
		Compare     string `json:"compare"`
		Expectation string `json:"expectation"`
	} `json:"status_code"`
	Actual string `json:"actual"`
}

type HttpNode_Log_Assertions []HttpNode_Log_Assertion

type HttpNodeLogWriter struct {
	*BaseNodeLogWriter

	log *HttpNode_Log
}

func NewHttpNodeLogWriter(bw *BaseNodeLogWriter) *HttpNodeLogWriter {
	writer := &HttpNodeLogWriter{
		BaseNodeLogWriter: bw,
		log: &HttpNode_Log{
			BaseNode_Log_Common: bw.log,
			Imports:             make(BaseNode_Log_Imports, 0, 10),
			Exports:             make(HttpNode_Log_Exports, 0, 10),
			Request: HttpNode_Log_Request{
				Headers:     make([]HttpNode_Log_Header, 0, 10),
				QueryParams: make([]HttpNode_Log_Request_QueryParams, 0, 10),
			},
			Response: HttpNode_Log_Response{
				Headers: make([]HttpNode_Log_Header, 0, 10),
			},
			Assertions: make(HttpNode_Log_Assertions, 0, 10),
		},
	}
	return writer
}

func (writer *HttpNodeLogWriter) toJson() string {
	return log2Json(writer.log)
}

func (writer *HttpNodeLogWriter) SetImport(
	key string, val any, variable VariableGetter, errcode errorx.Code, errmsg, debug string,
) {
	writer.log.Imports = append(writer.log.Imports, getBaseNodeLogImport(key, val, variable, errcode, errmsg, debug))
}

func (writer *HttpNodeLogWriter) SetExport_Header(
	name, header string, val any, errcode errorx.Code, errmsg, debug string,
) {
	export := HttpNode_Log_Export{
		BaseNode_Log_Common_Error: getBaseNodeLogCommonError(errcode, errmsg, debug),
	}
	export.Name = name
	export.Source = 0
	export.Headers.Key = header
	export.Actual = jsonx.MarshalToStringIgnoreError(val)
	writer.log.Exports = append(writer.log.Exports, export)
}

func (writer *HttpNodeLogWriter) SetExport_Body(
	name string, btype int, expression string, val any, errcode errorx.Code, errmsg, debug string,
) {
	export := HttpNode_Log_Export{
		BaseNode_Log_Common_Error: getBaseNodeLogCommonError(errcode, errmsg, debug),
	}
	export.Name = name
	export.Source = 1
	export.Body.Type = btype
	export.Body.Expression = expression
	export.Actual = jsonx.MarshalToStringIgnoreError(val)
	writer.log.Exports = append(writer.log.Exports, export)
}

func (writer *HttpNodeLogWriter) SetExport_StatusCode(
	name string, val any, errcode errorx.Code, errmsg, debug string,
) {
	export := HttpNode_Log_Export{
		BaseNode_Log_Common_Error: getBaseNodeLogCommonError(errcode, errmsg, debug),
	}
	export.Name = name
	export.Source = 2
	export.Actual = jsonx.MarshalToStringIgnoreError(val)

	writer.log.Exports = append(writer.log.Exports, export)
}

func (writer *HttpNodeLogWriter) SetTimeout(connect_timeout, request_timeout, response_timeout int64) {
	writer.log.Timeout = HttpNode_Log_Timeout{
		ConnectTimeout:  connect_timeout,
		RequestTimeout:  request_timeout,
		ResponseTimeout: response_timeout,
	}
}

func (writer *HttpNodeLogWriter) SetUrl(url string, errcode errorx.Code, errmsg, debug string) {
	writer.log.Request.Url = url
	writer.log.Request.Code = errcode
	writer.log.Request.Message = errmsg
	writer.log.Request.Debug = debug
}

func (writer *HttpNodeLogWriter) SetMethod(method string) {
	writer.log.Request.Method = method
}

func (writer *HttpNodeLogWriter) SetHeader(key, value string) {
	writer.log.Request.Headers = append(
		writer.log.Request.Headers, HttpNode_Log_Header{
			Key:   key,
			Value: value,
		},
	)
}

func (writer *HttpNodeLogWriter) SetQueryParam(key, value string) {
	writer.log.Request.QueryParams = append(
		writer.log.Request.QueryParams, HttpNode_Log_Request_QueryParams{
			Key:   key,
			Value: value,
		},
	)
}

func (writer *HttpNodeLogWriter) SetRequestBody(body string) {
	writer.log.Request.Body = body
}

func (writer *HttpNodeLogWriter) SetResponseStatusCode(statusCode int) {
	writer.log.Response.StatusCode = statusCode
}

func (writer *HttpNodeLogWriter) SetResponseHeader(key, value string) {
	writer.log.Response.Headers = append(
		writer.log.Response.Headers, HttpNode_Log_Header{
			Key:   key,
			Value: value,
		},
	)
}

func (writer *HttpNodeLogWriter) SetResponseBody(body string) {
	writer.log.Response.Body = body
}

func (writer *HttpNodeLogWriter) SetAssertion_Header(
	key, compare string, actual, expected any, errcode errorx.Code, errmsg, debug string,
) {
	assert := HttpNode_Log_Assertion{
		BaseNode_Log_Common_Error: getBaseNodeLogCommonError(errcode, errmsg, debug),
	}
	assert.Source = 0
	assert.Headers.Key = key
	assert.Headers.Compare = compare
	assert.Headers.Expression = jsonx.MarshalToStringIgnoreError(expected)
	assert.Actual = jsonx.MarshalToStringIgnoreError(actual)
	writer.log.Assertions = append(writer.log.Assertions, assert)
}

func (writer *HttpNodeLogWriter) SetAssertion_Body_Jmespath(
	expression, compare string, actual, expected any, errcode errorx.Code, errmsg, debug string,
) {
	assert := HttpNode_Log_Assertion{
		BaseNode_Log_Common_Error: getBaseNodeLogCommonError(errcode, errmsg, debug),
	}
	assert.Source = 1
	assert.Body.Type = 0
	assert.Body.Jmespath.Expression = expression
	assert.Body.Jmespath.Compare = compare
	assert.Body.Jmespath.Expectation = jsonx.MarshalToStringIgnoreError(expected)
	assert.Actual = jsonx.MarshalToStringIgnoreError(actual)
	writer.log.Assertions = append(writer.log.Assertions, assert)
}

func (writer *HttpNodeLogWriter) SetAssertion_Body_Regex(
	regex, compare string, actual any, errcode errorx.Code, errmsg, debug string,
) {
	assert := HttpNode_Log_Assertion{
		BaseNode_Log_Common_Error: getBaseNodeLogCommonError(errcode, errmsg, debug),
	}
	assert.Source = 1
	assert.Body.Type = 1
	assert.Body.Regex.Compare = compare
	assert.Body.Regex.Expression = jsonx.MarshalToStringIgnoreError(regex)
	assert.Actual = jsonx.MarshalToStringIgnoreError(actual)
	writer.log.Assertions = append(writer.log.Assertions, assert)
}

func (writer *HttpNodeLogWriter) SetAssertion_StatusCode(
	compare string, actual, expected any, errcode errorx.Code, errmsg, debug string,
) {
	assert := HttpNode_Log_Assertion{
		BaseNode_Log_Common_Error: getBaseNodeLogCommonError(errcode, errmsg, debug),
	}
	assert.Source = 2
	assert.StatusCode.Compare = compare
	assert.StatusCode.Expectation = jsonx.MarshalToStringIgnoreError(expected)
	assert.Actual = jsonx.MarshalToStringIgnoreError(actual)
	writer.log.Assertions = append(writer.log.Assertions, assert)
}

func (writer *HttpNodeLogWriter) SetCostMs(ms int64, errcode errorx.Code, errmsg, debug string) {
	writer.log.Request.CostMs = ms
	writer.log.Request.Code = errcode
	writer.log.Request.Message = errmsg
	writer.log.Request.Debug = debug
}

func (writer *HttpNodeLogWriter) ResetBeforeRetry() {
	writer.log.Response = HttpNode_Log_Response{
		Headers: make([]HttpNode_Log_Header, 0, 10),
	}
	writer.log.Assertions = make(HttpNode_Log_Assertions, 0, 10)
}
