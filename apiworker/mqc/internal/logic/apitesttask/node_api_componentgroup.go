package apitesttask

import (
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/codes"
	managerpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type ApiComponentGroupNode struct {
	*BaseComboNode
	ProjectId string

	source *managerpb.ComponentGroupComponent
	logW   *ApiComponentGroupNodeLogWriter
}

func NewApiComponentGroupNode(task *ApitestTask, data *managerpb.ApiExecutionData, parent Node, isShell bool) (
	Node, error,
) {
	node := &ApiComponentGroupNode{}

	var err error
	node.BaseComboNode, err = NewBaseComboNode(task, data, node, parent, isShell)
	if err != nil {
		return nil, node.UpdateInvalidStatef(codes.NodeInvalid, "%s", err)
	}

	// source数据
	source := data.GetGroup()
	node.source = source
	node.ProjectId = source.GetProjectId()
	node.name = source.GetName()
	node.description = source.GetDescription()
	node.SetVersion(source.GetVersion())
	node.SetImports(node.source.GetImports())
	node.SetExports(node.source.GetExports())

	node.logW = NewApiComponentGroupNodeLogWriter(node.BaseComboNode.logW)

	err = node.NewChildren(task, data)
	if err != nil {
		return node, node.UpdateInvalidStatef(codes.NodeInvalid, "%s", err)
	}

	return node, nil
}

func (node *ApiComponentGroupNode) Content() string {
	return node.logW.toJson()
}

func (node *ApiComponentGroupNode) Logger() NodeLogger {
	return node.logW
}
