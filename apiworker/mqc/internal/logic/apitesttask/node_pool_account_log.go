package apitesttask

import "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"

type PoolAccountNode_Log struct {
	*BaseNode_Log_Common
	PoolEnvTable string               `json:"pool_env_table"`
	Accounts     []string             `json:"accounts"`
	Exports      BaseNode_Log_Exports `json:"exports"`
}

type PoolAccountNodeLogWriter struct {
	*BaseNodeLogWriter

	log *PoolAccountNode_Log
}

func NewPoolAccountNodeLogWriter(bw *BaseNodeLogWriter) *PoolAccountNodeLogWriter {
	writer := &PoolAccountNodeLogWriter{
		BaseNodeLogWriter: bw,
		log: &PoolAccountNode_Log{
			BaseNode_Log_Common: bw.log,
			Accounts:            make([]string, 0, 10),
			Exports:             make(BaseNode_Log_Exports, 0, 10),
		},
	}
	return writer
}

func (writer *PoolAccountNodeLogWriter) toJson() string {
	return log2Json(writer.log)
}

func (writer *PoolAccountNodeLogWriter) SetPoolEnvTable(v string) {
	writer.log.PoolEnvTable = v
}

func (writer *PoolAccountNodeLogWriter) SetAccount(acc string) {
	writer.log.Accounts = append(writer.log.Accounts, acc)
}

func (writer *PoolAccountNodeLogWriter) SetExport(
	key string, val any, errcode errorx.Code, errmsg, debug string,
) {
	writer.log.Exports = append(writer.log.Exports, getBaseNodeLogExport(key, val, nil, errcode, errmsg, debug))
}
