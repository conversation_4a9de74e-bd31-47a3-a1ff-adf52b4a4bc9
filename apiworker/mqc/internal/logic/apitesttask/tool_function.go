package apitesttask

import (
	"fmt"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/codes"
	managerpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type Argv struct {
	Value any
	Exist bool
}

func (node *BaseNode) RunFunction(vf *managerpb.VariableFunction) (result any, err error) {
	typ := vf.GetType()
	switch typ {
	case managerpb.FunctionType_BUILTIN:
		return node.RunFunction_Builtin(vf)
	case managerpb.FunctionType_CUSTOM:
		return node.RunFunction_Custom(vf)
	}
	return nil, fmt.Errorf("未定义的函数类型:%s", typ.String())
}

func (node *BaseNode) RunFunction_GetArgs(params []*managerpb.VariableFunction_Parameter) (args []Argv) {
	args = make([]Argv, len(params))
	for idx, getter := range params {
		argv := Argv{Exist: true}
		var err error
		argv.Value, err = node.GetVariable(getter)
		if err != nil {
			argv.Exist = false
			node.UpdateWarningStatef(codes.NodeFunctionArgsNotFound, "key: %s, err: %s", getter.GetName(), err)
		}
		args[idx] = argv
	}
	return args
}
