package apitesttask

import (
	"github.com/zeromicro/go-zero/core/jsonx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"

	managerpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type ConditionNode_Log struct {
	*BaseNode_Log_Common
	Branch string `json:"branch"` // left; right
	ConditionNode_Log_Condition
}

type ConditionNode_Log_Condition struct {
	Type   int                                `json:"type"` // 0:单条件, 1:条件组
	Single ConditionNode_Log_Condition_Single `json:"single"`
	Group  ConditionNode_Log_Condition_Group  `json:"group"`
}

type ConditionNode_Log_Condition_Single struct {
	BaseNode_Log_Common_Error
	Left      BaseNode_Log_Variable `json:"left"`
	Compare   string                `json:"compare"`
	Right     BaseNode_Log_Variable `json:"right"`
	LeftData  string                `json:"left_data"`
	RightData string                `json:"right_data"`
}

type ConditionNode_Log_Condition_Group struct {
	RelationShip int                           `json:"relationship"`
	Conditions   []ConditionNode_Log_Condition `json:"conditions"`
}

type ConditionNodeLogWriter struct {
	*BaseNodeLogWriter

	log *ConditionNode_Log
}

func NewConditionNodeLogWriter(bw *BaseNodeLogWriter) *ConditionNodeLogWriter {
	writer := &ConditionNodeLogWriter{
		BaseNodeLogWriter: bw,
		log: &ConditionNode_Log{
			BaseNode_Log_Common: bw.log,
		},
	}
	return writer
}

func (writer *ConditionNodeLogWriter) SetBranch(isOk bool) {
	if isOk {
		writer.log.Branch = "LEFT"
		return
	}
	writer.log.Branch = "RIGHT"
}

func (writer *ConditionNodeLogWriter) GetRootCondition() *ConditionNode_Log_Condition {
	return &writer.log.ConditionNode_Log_Condition
}

func (writer *ConditionNodeLogWriter) SetConditionSingle(cond *ConditionNode_Log_Condition, data *managerpb.ConditionComponent_SingleCondition, left, right any, errcode errorx.Code, errmsg, debug string) {
	cond.Type = 0
	cond.Single = ConditionNode_Log_Condition_Single{
		BaseNode_Log_Common_Error: getBaseNodeLogCommonError(errcode, errmsg, debug),
		Left:                      getBaseNodeLogVariable("", data.GetLeft()),
		Compare:                   data.GetCompare(),
		Right:                     getBaseNodeLogVariable("", data.GetRight()),
		LeftData:                  jsonx.MarshalToStringIgnoreError(left),
		RightData:                 jsonx.MarshalToStringIgnoreError(right),
	}
}

func (writer *ConditionNodeLogWriter) SetConditionGroup(cond *ConditionNode_Log_Condition, data *managerpb.ConditionComponent_GroupCondition) {
	cond.Type = 0
	cond.Group = ConditionNode_Log_Condition_Group{
		RelationShip: int(data.GetRelationship()),
		Conditions:   make([]ConditionNode_Log_Condition, len(data.GetConditions())),
	}
}

func (writer *ConditionNodeLogWriter) toJson() string {
	return log2Json(writer.log)
}
