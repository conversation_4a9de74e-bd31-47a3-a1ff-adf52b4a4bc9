package apitesttask

type ApiComponentGroupNode_Log struct {
	*BaseComboNode_Log
}

type ApiComponentGroupNodeLogWriter struct {
	*BaseComboNodeLogWriter

	log *ApiComponentGroupNode_Log
}

func NewApiComponentGroupNodeLogWriter(bw *BaseComboNodeLogWriter) *ApiComponentGroupNodeLogWriter {
	writer := &ApiComponentGroupNodeLogWriter{
		BaseComboNodeLogWriter: bw,
		log: &ApiComponentGroupNode_Log{
			BaseComboNode_Log: bw.log,
		},
	}
	return writer
}

func (writer *ApiComponentGroupNodeLogWriter) toJson() string {
	return log2Json(writer.log)
}
