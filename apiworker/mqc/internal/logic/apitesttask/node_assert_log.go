package apitesttask

import (
	"github.com/zeromicro/go-zero/core/jsonx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"

	managerpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type AssertNode_Log struct {
	*BaseNode_Log_Common
	Assertions []AssertNode_Log_Assertion
}

type AssertNode_Log_Assertion struct {
	BaseNode_Log_Common_Error
	Actual struct {
		NodeId string `json:"node_id"` // 出参节点id
		Value  string `json:"value"`   // 节点的出参变量名称
	} `json:"actual"` // 真实值
	Compare  string `json:"compare"` // 比较类型
	Expected struct {
		BaseNode_Log_Variable
	} `json:"expected"`
	ActualData string `json:"actual_data"`
	ExpectData string `json:"expect_data"`
}

type AssertNodeLogWriter struct {
	*BaseNodeLogWriter

	log *AssertNode_Log
}

func NewAssertNodeLogWriter(bw *BaseNodeLogWriter) *AssertNodeLogWriter {
	writer := &AssertNodeLogWriter{
		BaseNodeLogWriter: bw,
		log: &AssertNode_Log{
			BaseNode_Log_Common: bw.log,
			Assertions:          make([]AssertNode_Log_Assertion, 0, 10),
		},
	}
	return writer
}

func (writer *AssertNodeLogWriter) SetAssertion(data *managerpb.AssertComponent_Assertion, actual, expected any, code errorx.Code, msg, debug string) {
	assert := AssertNode_Log_Assertion{
		BaseNode_Log_Common_Error: getBaseNodeLogCommonError(code, msg, debug),
	}
	assert.Actual.NodeId = data.GetActual().GetNodeId()
	assert.Actual.Value = data.GetActual().GetValue()
	assert.Compare = data.GetCompare()
	assert.Expected.BaseNode_Log_Variable = getBaseNodeLogVariable("", data.GetExpected())
	assert.ActualData = jsonx.MarshalToStringIgnoreError(actual)
	assert.ExpectData = jsonx.MarshalToStringIgnoreError(expected)
	writer.log.Assertions = append(writer.log.Assertions, assert)
}

func (writer *AssertNodeLogWriter) toJson() string {
	return log2Json(writer.log)
}
