package apitesttask

import (
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/codes"
	managerpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type EndNode struct {
	*BaseNode
	logW *EndNodeLogWriter
}

func NewEndNode(task *ApitestTask, data *managerpb.ApiExecutionData, parent Node, isShell bool) (Node, error) {
	node := &EndNode{}
	var err error
	node.BaseNode, err = NewBaseNode(task, data, node, parent, isShell)
	if err != nil {
		return nil, node.UpdateInvalidStatef(codes.NodeInvalid, "%s", err)
	}

	node.logW = NewEndNodeLogWriter(node.BaseNode.logW)

	return node, nil
}

func (node *EndNode) Skip() bool {
	// 结束组件必须执行
	return false
}

func (node *EndNode) run() (err error) {
	node.Task().SetEnded(true)
	return nil
}

func (node *EndNode) Content() string {
	return node.logW.toJson()
}

func (node *EndNode) Logger() NodeLogger {
	return node.logW
}
