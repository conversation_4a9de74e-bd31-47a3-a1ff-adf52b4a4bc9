package apitesttask

import (
	"encoding/base64"
	"fmt"
	"strings"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"

	commonutils "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/apiworker/common/mock"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/apiworker/mqc/internal/svc"
	commonpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/utils"
	dispatcherpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/rpc/pb"
	managerpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

func Mock_WorkerReq_ApiCase(node *managerpb.ApiExecutionData) *dispatcherpb.WorkerReq {
	project_id := utils.GenProjectId()
	execute_id := utils.GenExecuteId()
	req := &dispatcherpb.WorkerReq{
		NodeData:      nil,
		TriggerMode:   commonpb.TriggerMode_MANUAL,
		ProjectId:     project_id,
		TaskId:        utils.GenTaskId(),
		ExecuteId:     execute_id,
		ExecuteType:   managerpb.ApiExecutionDataType_API_CASE,
		WorkerType:    dispatcherpb.WorkerType_WorkerType_API_CASE,
		GeneralConfig: mock.Mock_GeneralConfig(project_id),
		AccountConfig: mock.Mock_AccountConfig(project_id),
		User:          "unit-test",
		UserId:        commonutils.GenNanoId("user_id:"),
		Data: &dispatcherpb.WorkerReq_Case{
			Case: &dispatcherpb.CaseWorkerInfo{
				CaseId:        utils.GenCaseId(),
				CaseExecuteId: execute_id,
				Version:       utils.GenVersion(),
			},
		},
	}

	// node data
	api_case := mock_default_case_execution_data()
	mock_case_execution_data_append_child(api_case, mock_default_start_execution_data()) // start
	mock_case_execution_data_append_child(api_case, node)
	mock_case_execution_data_append_child(api_case, mock_default_end_execution_data()) // end
	req.NodeData = api_case

	return req
}

func Mock_ApitestTask() *ApitestTask {
	return NewApitestTask(mock.Mock_Context(), svc.Mock_ServiceContext())
}

func Mock_WorkerReq_from_data(base64pb string) *dispatcherpb.WorkerReq {
	taskpb, err := base64.StdEncoding.DecodeString(base64pb)
	if err != nil {
		panic(any("无效的task信息,不是base64, err:" + err.Error()))
	}

	taskData := &dispatcherpb.WorkerReq{}
	err = protobuf.UnmarshalJSON(taskpb, taskData)
	if err != nil {
		panic(any("无效的taskpb, err:" + err.Error()))
	}

	return taskData
}

func mock_register_rpc_data(task *ApitestTask, node_score int, rpc_data []string) {
	task.mockRpc = make(map[string][]byte)

	for _, rdata := range rpc_data {
		splitv := strings.SplitN(rdata, ":", 3)
		if len(splitv) != 3 {
			continue
		}
		rpc_score := splitv[0]
		rpc_name := splitv[1]
		base64pb := splitv[2]

		rpc_data, err := base64.StdEncoding.DecodeString(base64pb)
		if err != nil {
			continue
		}

		key := fmt.Sprintf("%d:%s:%s", node_score, rpc_name, rpc_score)
		task.mockRpc[key] = rpc_data
	}
}
