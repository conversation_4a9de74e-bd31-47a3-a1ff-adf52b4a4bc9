package apitesttask

import (
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/caller"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/utils"
)

type BaseReporter struct{}

func NewBaseReporter() *BaseReporter {
	return &BaseReporter{}
}

func (r *BaseReporter) RetryModify(node *BaseNode) (err error) {
	err = caller.RetryDo(3, func() error {
		_, err := node.Task().svcCtx.ReporterRpc.Modify(node.Task().ctx, node.getRecordRequest())
		return err
	})
	// TODO: 如果3次都失败，则告警

	return err
}

func (r *BaseReporter) RetryCreate(node *BaseNode) (executeId string, err error) {
	err = caller.RetryDo(3, func() error {
		resp, err1 := node.Task().svcCtx.ReporterRpc.Create(node.Task().ctx, node.getRecordRequest())
		if err1 == nil {
			executeId = resp.GetComponentExecuteId()
		}
		return err1
	})
	// TODO: 如果3次都失败，则告警
	if err != nil {
		executeId = utils.GenExecuteId()
	}

	return executeId, err
}

func (r *BaseReporter) Init(node *BaseNode) (err error) {
	if node.Shell() {
		// 如果是外壳，说明组件已经被dispatcher创建了记录，需要执行更新
		return r.RetryModify(node)
	}

	// 创建新的执行记录
	executeId, err := r.RetryCreate(node)
	node.SetNodeExecuteId(executeId)
	return err
}

func (r *BaseReporter) Setup(node *BaseNode) (err error) {
	return r.RetryModify(node)
}

func (r *BaseReporter) Teardown(node *BaseNode) (err error) {
	return r.RetryModify(node)
}
