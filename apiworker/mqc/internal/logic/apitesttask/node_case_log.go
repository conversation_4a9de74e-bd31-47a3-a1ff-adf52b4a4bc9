package apitesttask

type CaseNode_Log struct {
	*BaseComboNode_Log
}

type CaseNodeLogWriter struct {
	*BaseComboNodeLogWriter

	log *CaseNode_Log
}

func NewCaseNodeLogWriter(bw *BaseComboNodeLogWriter) *CaseNodeLogWriter {
	writer := &CaseNodeLogWriter{
		BaseComboNodeLogWriter: bw,
		log: &CaseNode_Log{
			BaseComboNode_Log: bw.log,
		},
	}
	return writer
}

func (writer *CaseNodeLogWriter) toJson() string {
	return log2Json(writer.log)
}
