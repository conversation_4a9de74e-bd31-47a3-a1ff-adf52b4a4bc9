package apitesttask

type StartNode_Log struct {
	*BaseNode_Log_Common
}

type StartNodeLogWriter struct {
	*BaseNodeLogWriter

	log *StartNode_Log
}

func NewStartNodeLogWriter(bw *BaseNodeLogWriter) *StartNodeLogWriter {
	writer := &StartNodeLogWriter{
		BaseNodeLogWriter: bw,
		log: &StartNode_Log{
			BaseNode_Log_Common: bw.log,
		},
	}
	return writer
}

func (writer *StartNodeLogWriter) toJson() string {
	return log2Json(writer.log)
}
