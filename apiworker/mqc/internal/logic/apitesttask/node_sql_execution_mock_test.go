package apitesttask

import (
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	managerpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

func mock_sql_execution_data_pg() *managerpb.ApiExecutionData {
	component := &managerpb.SqlExecutionComponent{
		Sqls: []*managerpb.SqlExecutionComponent_Sql{
			{
				Type: managerpb.DataSourceType_POSTGRESQL,
				Host: &managerpb.SqlExecutionComponent_Value{
					Source: managerpb.VariableSource_MANUAL,
					Manual: &managerpb.VariableManual{
						Value: "\"*************\"",
					},
				},
				Port: &managerpb.SqlExecutionComponent_Value{
					Source: managerpb.VariableSource_MANUAL,
					Manual: &managerpb.VariableManual{
						Value: "5432",
					},
				},
				User: &managerpb.SqlExecutionComponent_Value{
					Source: managerpb.VariableSource_MANUAL,
					Manual: &managerpb.VariableManual{
						Value: "\"postgres\"",
					},
				},
				Password: &managerpb.SqlExecutionComponent_Value{
					Source: managerpb.VariableSource_MANUAL,
					Manual: &managerpb.VariableManual{
						Value: "\"mysecretpassword\"",
					},
				},
				Database: &managerpb.SqlExecutionComponent_Value{
					Source: managerpb.VariableSource_MANUAL,
					Manual: &managerpb.VariableManual{
						Value: "\"pyj_test\"",
					},
				},
				Sql:     "insert into haha.xixi(name, url) values ('pyj', 'test')",
				Name:    "result",
				Timeout: 5000,
			},
		},
	}

	exec_data := &managerpb.ApiExecutionData{
		Id:   utils.GenNanoId("SQL_EXECUTION_"),
		Type: managerpb.ApiExecutionDataType_SQL_EXECUTION,
		Data: &managerpb.ApiExecutionData_Sql{
			Sql: component,
		},
	}

	return exec_data
}
