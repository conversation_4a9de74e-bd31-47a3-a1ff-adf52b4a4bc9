package apitesttask

import (
	"context"
	"errors"
	"fmt"
	"io"
	"os"
	"os/exec"
	"reflect"
	"time"

	"github.com/zeromicro/go-zero/core/jsonx"

	managerpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

const (
	PYTHON = `python3`

	// 自定义函数: PYTHON模板
	CustomFunctionTemplate_Python = `
import json
import sys

{{.function_content}}

def {{.function_name}}_json(input):
    body = {
        'data': {{.function_name}}(*input.get('args'))
    }
    return json.dumps(body)

if __name__ == "__main__":
    instr = sys.argv[1]
    input = json.loads(instr)
    output = {{.function_name}}_json(input)
    output_file = input.get('output_file')

    with open(output_file, 'w') as f:
        f.write(output)
`
)

type FunctionPythonRequest struct {
	Args       []any  `json:"args"`
	OutputFile string `json:"output_file"`
}

type FunctionPythonResponse struct {
	Data any
}

// RunFunctionCustom_Python 运行自定义函数:python类型
func (node *BaseNode) RunFunctionCustom_Python(
	function *managerpb.DataProcessingFunction, args []Argv,
) (result any, err error) {
	// 安全性校验
	args, err = node.RunFunctionCustom_Python_ValidRequest(function.GetParameters(), args)
	if err != nil {
		return nil, err
	}

	scriptPath := fmt.Sprintf("%s/%s/python", node.task.svcCtx.Config.ScriptPath, node.task.ProjectId())
	if _, err = os.Stat(scriptPath); errors.Is(err, os.ErrNotExist) {
		err = os.MkdirAll(scriptPath, os.ModePerm)
		if err != nil {
			return nil, fmt.Errorf("创建python目录失败，script_path: %s, err: %+v", scriptPath, err)
		}
	}

	scriptFile := fmt.Sprintf("%s/%s_%s.py", scriptPath, function.GetName(), function.GetVersion())

	// python执行文件是否存在, write and move 解决并发冲突问题
	if _, err = os.Stat(scriptFile); errors.Is(err, os.ErrNotExist) {
		// 如果不存在，则创建
		// 解析模板
		pythonScript := ReplaceVars(
			CustomFunctionTemplate_Python, map[string]any{
				"function_name":    function.GetName(),
				"function_content": function.GetContent(),
			},
		)

		writeFile := fmt.Sprintf(
			"%s/%s_%s_%d.py", scriptPath, function.GetName(), function.GetVersion(), time.Now().UnixMicro(),
		)
		err = os.WriteFile(writeFile, []byte(pythonScript), 0o777) // #nosec G306
		if err != nil {
			return nil, fmt.Errorf("创建python脚本失败，filename: %s, err: %+v", writeFile, err)
		}

		err = os.Rename(writeFile, scriptFile)
		if err != nil {
			return nil, fmt.Errorf(
				"创建python脚本失败, write_file: %s, script_file: %s, err: %+v", writeFile, scriptFile, err,
			)
		}
	}

	// 执行py脚本
	result, err = node.RunFunctionCustom_Python_Exec(scriptFile, function, args)
	if err != nil {
		return nil, fmt.Errorf("执行python脚本失败，filename: %s, err: %+v", scriptFile, err)
	}

	return result, nil
}

func (node *BaseNode) RunFunctionCustom_Python_ValidRequest(
	parameters []*managerpb.Parameter, args []Argv,
) (rargs []Argv, err error) {
	arglen := len(args)

	rargs = make([]Argv, len(parameters))
	for i := 0; i < len(parameters); i++ {
		if i < arglen {
			// 如果不存在，则赋予默认值
			if !args[i].Exist {
				if parameters[i].GetDefault() == "" {
					return nil, fmt.Errorf("请求参数校验失败, err:未提供变量且未配置默认值")
				}

				err := jsonx.UnmarshalFromString(parameters[i].GetDefault(), &args[i].Value)
				if err != nil {
					return nil, fmt.Errorf("无效的默认参数, default: %s, err: %+v", parameters[i].GetDefault(), err)
				}
				args[i].Exist = true
			}

			err := node.RunFunctionCustom_Python_ValidRequest_Type(parameters[i].GetType(), args[i].Value)
			if err != nil {
				return nil, fmt.Errorf("参数无效，err: %+v", err)
			}
			rargs[i] = args[i]
			continue
		}

		// 未提供值使用默认值
		if parameters[i].GetDefault() == "" {
			return nil, fmt.Errorf("请求参数校验失败, err: 未提供变量且未配置默认值")
		}

		err := jsonx.UnmarshalFromString(parameters[i].GetDefault(), &rargs[i].Value)
		if err != nil {
			return nil, fmt.Errorf("无效的默认参数, default: %s, err: %+v", parameters[i].GetDefault(), err)
		}
		rargs[i].Exist = true
	}

	return rargs, nil
}

func (node *BaseNode) RunFunctionCustom_Python_ValidRequest_Type(
	target managerpb.ParameterOrReturnType, source any,
) error {
	if target == managerpb.ParameterOrReturnType_ANY {
		return nil
	}

	ParamType2ReflectKind := map[managerpb.ParameterOrReturnType][]reflect.Kind{
		managerpb.ParameterOrReturnType_STRING: {reflect.String},
		managerpb.ParameterOrReturnType_NUMBER: {
			reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64, reflect.Int, reflect.Uint8,
			reflect.Uint16, reflect.Uint32, reflect.Uint64, reflect.Uint, reflect.Float32, reflect.Float64,
			reflect.String, // include: json.Number, string
			reflect.Bool,
		},
		managerpb.ParameterOrReturnType_ARRAY:   {reflect.Array, reflect.Slice},
		managerpb.ParameterOrReturnType_OBJECT:  {reflect.Map},
		managerpb.ParameterOrReturnType_BOOLEAN: {reflect.Bool},
		managerpb.ParameterOrReturnType_NULL:    {reflect.Invalid},
	}

	allowKinds, ok := ParamType2ReflectKind[target]
	if !ok {
		return fmt.Errorf("未注册类型: %s", target.String())
	}

	sourceKind := reflect.Indirect(reflect.ValueOf(source)).Kind()
	for _, allow := range allowKinds {
		if sourceKind == allow {
			return nil
		}
	}

	return fmt.Errorf("类型错误. 目标类型: %s, 提供类型: %s", target.String(), sourceKind.String()) // now found
}

func (node *BaseNode) RunFunctionCustom_Python_Exec(
	filename string, function *managerpb.DataProcessingFunction, args []Argv,
) (result any, err error) {
	funcArgs := make([]any, len(args))
	for idx, arg := range args {
		funcArgs[idx] = arg.Value
	}

	outputPath := fmt.Sprintf("%s/%s/output", node.task.svcCtx.Config.ScriptPath, node.task.ProjectId())
	request := FunctionPythonRequest{
		Args: funcArgs,
		OutputFile: fmt.Sprintf(
			"%s/%s_%s_%d_%s.json", outputPath, function.GetName(), function.GetVersion(), time.Now().UnixMicro(),
			node.nodeExecuteId,
		),
	}

	if _, err = os.Stat(outputPath); errors.Is(err, os.ErrNotExist) {
		err = os.MkdirAll(outputPath, os.ModePerm)
		if err != nil {
			return nil, fmt.Errorf("创建python.output目录失败，output: %s, err: %+v", outputPath, err)
		}
	}

	requestBody, err := jsonx.MarshalToString(request)
	if err != nil {
		return nil, fmt.Errorf("json marshal request err: %+v", err)
	}

	ctx, cancel := context.WithTimeout(context.Background(), time.Second*5) // ctx done?
	defer cancel()
	outPrint, err := exec.CommandContext(ctx, PYTHON, filename, requestBody).CombinedOutput()
	if err != nil {
		return nil, fmt.Errorf("outprint: %s, err: %+v", string(outPrint), err)
	}

	outfile, err := os.Open(request.OutputFile)
	if err != nil {
		return nil, fmt.Errorf("获取output失败 err: %+v", err)
	}
	defer func() {
		outfile.Close()           // #nosec G307
		os.Remove(outfile.Name()) // #nosec G307
	}()
	outBuffer, err := io.ReadAll(outfile)
	if err != nil {
		return nil, fmt.Errorf("读取output失败 err: %+v", err)
	}

	response := FunctionPythonResponse{}
	err = jsonx.Unmarshal(outBuffer, &response)
	if err != nil {
		return nil, fmt.Errorf("output不是有效json, data: %s, err: %+v", string(outBuffer), err)
	}

	return response.Data, nil
}
