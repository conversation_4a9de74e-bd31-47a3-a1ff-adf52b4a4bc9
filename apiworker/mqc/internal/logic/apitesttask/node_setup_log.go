package apitesttask

type SetupNode_Log struct {
	*BaseComboNode_Log
}

type SetupNodeLogWriter struct {
	*BaseComboNodeLogWriter

	log *SetupNode_Log
}

func NewSetupNodeLogWriter(bw *BaseComboNodeLogWriter) *SetupNodeLogWriter {
	// 次生writer类，由于使用的不是interface,因此需要引用基础类
	writer := &SetupNodeLogWriter{
		BaseComboNodeLogWriter: bw,
		log: &SetupNode_Log{
			BaseComboNode_Log: bw.log,
		},
	}
	return writer
}

func (writer *SetupNodeLogWriter) toJson() string {
	return log2Json(writer.log)
}
