package apitesttask

type WaitNode_Log struct {
	*BaseNode_Log_Common
	CostMs int64 `json:"cost_ms"`
}

type WaitNodeLogWriter struct {
	*BaseNodeLogWriter

	log *WaitNode_Log
}

func NewWaitNodeLogWriter(bw *BaseNodeLogWriter) *WaitNodeLogWriter {
	writer := &WaitNodeLogWriter{
		BaseNodeLogWriter: bw,
		log: &WaitNode_Log{
			BaseNode_Log_Common: bw.log,
		},
	}
	return writer
}

func (writer *WaitNodeLogWriter) SetCostMs(ms int64) {
	writer.log.CostMs = ms
}

func (writer *WaitNodeLogWriter) toJson() string {
	return log2Json(writer.log)
}
