package apitesttask

import (
	"fmt"

	dataprocessingfunctionservice "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/client/dataprocessingfunctionservice"
	managerpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

// RunFunction_Custom 执行函数-自定义函数
func (node *BaseNode) RunFunction_Custom(vf *managerpb.VariableFunction) (result any, err error) {
	args := node.RunFunction_GetArgs(vf.GetParameters())

	// manager.rpc.view获取原始数据
	dpf, err := node.task.svcCtx.ManagerRpc.ViewDataProcessingFunction(
		node.task.ctx, &dataprocessingfunctionservice.ViewDataProcessingFunctionReq{
			ProjectId: node.task.ProjectId(),
			Name:      vf.GetName(),
			Type:      vf.GetType(),
		},
	)
	if err != nil {
		return nil, fmt.Errorf(
			"获取函数失败, project_id:%s, func_type:%s, func_name:%s, err:%s", node.task.ProjectId(), vf.GetType(),
			vf.GetName(), err,
		)
	}

	function := dpf.GetFunction()

	switch function.GetLanguage() {
	case managerpb.CodeLanguage_PYTHON:
		result, err = node.RunFunctionCustom_Python(function, args)
		if err != nil {
			return nil, fmt.Errorf(
				"执行python函数失败, project_id:%s, func_type:%s, func_name:%s, err:%s", node.task.ProjectId(),
				vf.GetType(), vf.GetName(), err,
			)
		}
	case managerpb.CodeLanguage_GOLANG:
		return nil, fmt.Errorf(
			"未找到函数, project_id:%s, func_type:%s, func_name:%s, err:%s", node.task.ProjectId(), vf.GetType(),
			vf.GetName(), err,
		)
	}

	return result, nil
}
