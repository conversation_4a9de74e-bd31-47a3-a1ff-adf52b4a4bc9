package apitesttask

import (
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/codes"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/state"
	managerpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type DataProcessingNode struct {
	*BaseNode

	source *managerpb.DataProcessingComponent
	logW   *DataProcessingNodeLogWriter
}

func NewDataProcessingNode(task *ApitestTask, data *managerpb.ApiExecutionData, parent Node, isShell bool) (
	Node, error,
) {
	node := &DataProcessingNode{}
	var err error
	node.BaseNode, err = NewBaseNode(task, data, node, parent, isShell)
	if err != nil {
		return nil, node.UpdateInvalidStatef(codes.NodeInvalid, "%s", err)
	}

	// source数据
	source := data.GetProcessing()
	node.source = source
	node.logW = NewDataProcessingNodeLogWriter(node.BaseNode.logW)

	return node, nil
}

func (node *DataProcessingNode) run() (err error) {
	return node.runProcesses()
}

func (node *DataProcessingNode) runProcesses() error {
	for _, process := range node.source.GetProcesses() {
		err := node.runProcess(process)
		if err != nil {
			return err
		}
	}

	return nil
}

func (node *DataProcessingNode) runProcess(process *managerpb.DataProcessingComponent_Process) (err error) {
	key := process.GetName()
	var result any
	errcode := errorx.OK
	logPs := &managerpb.DataProcessingComponent_Process{}
	_ = utils.Copy(logPs, process)

	if key == "" {
		return
	}

	defer func() {
		node.logW.SetProcess(result, logPs, errcode, GetErrCode(errcode).String(), state.Error2Debug(err))
	}()

	result, err = node.RunFunction(process.GetFunction())
	if err != nil {
		errcode = codes.NodeFunctionExecFailure
		return node.UpdateFailureStatef(codes.NodeFunctionExecFailure, "err: %s", err)
	}

	node.task.VarsPool.SetNodeExportVar(node.Id(), key, result)
	return nil
}

func (node *DataProcessingNode) Content() string {
	return node.logW.toJson()
}

func (node *DataProcessingNode) Logger() NodeLogger {
	return node.logW
}
