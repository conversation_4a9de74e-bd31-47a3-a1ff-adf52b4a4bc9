package apitesttask

import (
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/codes"
	managerpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

// BusinessGroupNode 业务行为组组件
type BusinessGroupNode struct {
	*BaseComboNode

	source *managerpb.BusinessGroupComponent
	logW   *BusinessGroupNodeLogWriter
}

func NewBusinessGroupNode(task *ApitestTask, data *managerpb.ApiExecutionData, parent Node, isShell bool) (Node, error) {
	node := &BusinessGroupNode{}
	var err error
	node.BaseComboNode, err = NewBaseComboNode(task, data, node, parent, isShell)
	if err != nil {
		return nil, node.UpdateInvalidStatef(codes.NodeInvalid, "%s", err)
	}

	source := data.GetBusinessGroup()
	node.source = source
	node.SetImports(node.source.GetImports())
	node.SetExports(node.source.GetExports())

	node.logW = NewBusinessGroupNodeLogWriter(node.BaseComboNode.logW)

	err = node.NewChildren(task, data)
	if err != nil {
		return node, err
	}

	return node, nil
}

func (node *BusinessGroupNode) Content() string {
	return node.logW.toJson()
}

func (node *BusinessGroupNode) Logger() NodeLogger {
	return node.logW
}
