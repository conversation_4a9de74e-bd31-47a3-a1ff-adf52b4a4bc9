package apitesttask

import (
	"testing"

	"github.com/pkg/errors"
	"github.com/valyala/fasthttp"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/codes"
)

// 获取线上环境mock数据：
// https://bi.skyengine.com.cn/product/view.htm?module=dashboard&productId=f76dae8c-54b0-47c8-bab9-0051095ee004&menuId=ekkbihi0uto

func TestHttpNode_Simple(t *testing.T) {
	mockHttpData := `{"url":"/router/v1/common/api/call","body":{"raw":"{\n  \"cid\": {{.cid}},\n  \"method\": \"ga.api.channel_scheme.ChannelSchemeLogic.GetChannelSchemeInfo\"\n}","type":4,"form_data":[]},"name":"精准测试HTTP","size":"200*80","clazz":"PRECISION_TESTING_HTTP","label":"","method":"POST","exports":[{"body":{"type":0,"expression":"data.call_resp.body.scheme_info.scheme_id"},"name":"scheme_id","source":1,"headers":{"key":""},"description":"玩法id"}],"headers":[],"imports":[{"name":"cid","export":{"value":"cid","node_id":"BUSINESS_GROUP_1j7nog-VbwRAKQ0EdHntP188iiEO"},"manual":{"value":""},"source":1,"account":{},"function":{"name":"","type":0,"parameters":[{"name":"","export":{"value":"","node_id":""},"manual":{"value":""},"source":1,"environment":{"value":""}}]},"description":"","environment":{"value":""}}],"timeout":{"connect_timeout":6000,"request_timeout":6001,"response_timeout":6002},"assertions":[{"body":{"type":0,"regex":{"expression":""},"jmespath":{"compare":"EQ","expression":"data.call_resp.body.base_resp.ret","expectation":"0"}},"source":1,"headers":{"key":"","compare":"EQ","expression":""},"description":"","status_code":{"compare":"EQ","expectation":""}},{"body":{"type":0,"regex":{"expression":""},"jmespath":{"compare":"NE","expression":"data.call_resp.body.scheme_info","expectation":"null"}},"source":1,"headers":{"key":"","compare":"EQ","expression":""},"description":"兼容旧命令的一些字段(客户端需要哪些字段后面再补上去)\n\n","status_code":{"compare":"EQ","expectation":""}},{"body":{"type":0,"regex":{"expression":""},"jmespath":{"compare":"EQ","expression":"code","expectation":"0"}},"source":1,"headers":{"key":"","compare":"EQ","expression":""},"description":"","status_code":{"compare":"EQ","expectation":""}}],"query_params":[],"authorization":{"type":0,"api_key":{"key":"","value":"","add_to":1},"basic_auth":{"password":"","username":""},"bearer_token":{"token":""}}}`

	task := Mock_ApitestTask()
	taskParams := Mock_WorkerReq_ApiCase(mock_http_execution_data_from_json(mockHttpData))

	task.VarsPool.SetNodeExportVar(
		"BUSINESS_GROUP_1j7nog-VbwRAKQ0EdHntP188iiEO", "cid", "client_id:8CELiCFT4gc5JxsdHQ51Z",
	)
	// task.VarsPool.SetNodeExportVar("POOL_ACCOUNT_a283hb-soObx8E8QMNmFogYJxK85", "password", "ksnczaqddwhj")
	task.VarsPool.SetGlobalVar("base_url", "http://api-proxy-router.api-proxy.svc.cluster.local")

	err := task.Run(taskParams)
	if err != nil {
		t.FailNow()
	}
}

func TestHTTPError(t *testing.T) {
	err := func() error {
		return NewHTTPErrorWithMessage(codes.NodeHTTPRequestFailure, "HTTP请求失败")
	}()

	var he HttpError
	if errors.As(err, &he) {
		t.Logf("OK, got: %T", err)
	} else {
		t.Logf("NOT OK, expected: %T, but got: %T", (*HttpError)(nil), err)
	}

	if errors.Is(err, &he) {
		t.Logf("OK, got: %T", err)
	} else {
		t.Logf("NOT OK, expected: %T, but got: %T", (*HttpError)(nil), err)
	}

	v, ok := err.(*HttpError)
	if ok {
		t.Logf("OK, got: %T", v)
	} else {
		t.Logf("NOT OK, expected: %T, but got: %T", (*HttpError)(nil), err)
	}
}

func TestHTTPReadBufferSize(t *testing.T) {
	c := &fasthttp.Client{
		ReadBufferSize:         8192,
		WriteBufferSize:        8192,
		DisablePathNormalizing: true, // 禁用路径格式化
	}

	req := fasthttp.AcquireRequest()
	defer fasthttp.ReleaseRequest(req)

	resp := fasthttp.AcquireResponse()
	defer fasthttp.ReleaseResponse(resp)

	req.SetRequestURI("http://dap-yuntest.ttyuyin.com:8159/dsp-lpm/entityGroup/calculateCount")
	req.Header.SetMethod(fasthttp.MethodPost)
	req.Header.Set(
		"Authorization",
		"eyJhbGciOiJIUzI1NiJ9.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.MnuQnhAOsKoYOZryh-4Wr27oxc9BTSKjkLetJ5Cy234",
	)
	req.Header.SetContentType("application/json")
	req.SetHost("dap-yuntest.ttyuyin.com:8159")
	req.SetBodyString(`{"groupId":"","jobId":"","groupRuleList":[{"rules":[{"rules":[{"rules":[{"showName":"年龄","function":"equal","type":"profile_rule","labelName":"age","realTimeType":"2","labelTable":"lpm_ttvoice_user_label_string_all","labelValueType":"string","params":["3"],"labelValueTree":"0","labelValueVer":"2025-05-19 10:32:27","productId":"ttvoice"}],"type":"rules_relation","productId":"ttvoice"}],"type":"rules_relation","productId":"ttvoice"},{"rules":[],"type":"rules_relation","productId":"ttvoice"}],"relation":"and","type":"rules_relation","productId":"ttvoice"}],"entityType":"user","productId":"ttvoice"}`)

	t.Logf("req: %s", req.String())
	err := c.Do(req, resp)
	if err != nil {
		t.Fatal(err)
	}

	t.Logf("resp: %s", resp.String())
}

func TestHTTPSetHeader(t *testing.T) {
	c := &fasthttp.Client{
		ReadBufferSize:         8192,
		WriteBufferSize:        8192,
		DisablePathNormalizing: true, // 禁用路径格式化
	}

	req := fasthttp.AcquireRequest()
	defer fasthttp.ReleaseRequest(req)

	resp := fasthttp.AcquireResponse()
	defer fasthttp.ReleaseResponse(resp)

	req.SetRequestURI("http://*************:8000/api/features/v1/data/write/json")
	req.Header.SetMethod(fasthttp.MethodPost)
	req.Header.Set("host", "rcmd-tools.rcmd-tt.production.mpengine")
	req.Header.Set("content-type", "application/json")
	req.Header.Set("x-tt-security-timestamp", "1419379681024")
	req.UseHostHeader = true
	req.SetBodyString(`{"app_id":"10007","version":2,"task":"test_task","operate":"set","ttl":60000,"header":["u_ugc_game_play_pref_14d"],"values":{"240763262":[{"王者荣耀":"王者荣耀_游戏名称_王者荣耀"}]}}`)

	t.Logf("req0: %s", req.String())
	err := c.Do(req, resp)
	if err != nil {
		t.Fatal(err)
	}

	t.Logf("req1: %s", req.String())
	t.Logf("resp: %s", resp.String())
}
