package apitesttask

import (
	"time"

	"github.com/valyala/fasthttp"
	"github.com/zeromicro/go-zero/core/jsonx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworkerv2/base"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/codes"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	commonpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/rpc/pb"
	managerpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
	relationpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/relation/rpc/pb"
)

type PrecisionInfo struct {
	ProbeProjectId       string `json:"probe_project_id" validate:"required"`
	ProbeGeneralConfigId string `json:"probe_general_config_id" validate:"required"`
	ProbeTestCaseId      string `json:"probe_test_case_id" validate:"required"`
}

type PrecisionTestingHttpNode struct {
	*HttpNode
	PrecisionInfo *PrecisionInfo
}

func NewPrecisionTestingHttpNode(task *ApitestTask, data *managerpb.ApiExecutionData, parent Node, isShell bool) (
	Node, error,
) {
	node := &PrecisionTestingHttpNode{
		HttpNode:      &HttpNode{},
		PrecisionInfo: &PrecisionInfo{},
	}
	var err error
	node.BaseNode, err = NewBaseNode(task, data, node, parent, isShell)
	if err != nil {
		return nil, node.UpdateInvalidStatef(codes.NodeInvalid, "%s", err)
	}

	// source数据
	source := data.GetPrecisionTestingHttpRequest()
	node.source = source
	node.logW = NewHttpNodeLogWriter(node.BaseNode.logW)

	return node, nil
}

func (node *PrecisionTestingHttpNode) run() (err error) {
	var (
		taskID    = node.task.Source().GetTaskId()
		executeID = node.task.Source().GetExecuteId()
		nodeID    = node.NodeId
	)

	node.processingImports()

	node.getClientWithTimeout()

	node.Request = fasthttp.AcquireRequest()
	defer fasthttp.ReleaseRequest(node.Request)

	err = node.setUrl()
	if err != nil {
		return err
	}

	node.setMethod()
	node.setAuthorization()
	node.setHeaders()
	node.setPrecisionInfo()
	node.setQueryParams()
	err = node.setBody()
	if err != nil {
		return err
	}

	node.Response = fasthttp.AcquireResponse()
	defer fasthttp.ReleaseResponse(node.Response)

	defer func() {
		//requestUrl := node.source.GetUrl()
		//precisionInfo, _ := json.Marshal(node.PrecisionInfo)
		//requestBody := node.Request.Body()
		//responseBody := node.Response.Body()
		//
		//_, bindTestCaseToServiceErr := node.Task().svcCtx.RelationRpc.BindTestCaseToService(
		//	node.task.ctx,
		//	&relationpb.BindTestCaseToServiceV2Request{
		//		RequestUrl:    requestUrl,
		//		PrecisionInfo: precisionInfo,
		//		RequestBody:   requestBody,
		//		ResponseBody:  responseBody,
		//	},
		//)
		//
		//if bindTestCaseToServiceErr != nil {
		//	node.task.Errorf("向relation服务发起服务与测试用例绑定操作发生异常：「%s」", bindTestCaseToServiceErr)
		//}

		if node.task.ExecuteType() != managerpb.ApiExecutionDataType_API_PLAN {
			return
		}

		info := node.getPrecisionInfo()
		if info == nil {
			return
		}

		payload := protobuf.MarshalJSONIgnoreError(
			&relationpb.BindCaseToServiceReq{
				RequestUrl:    node.source.GetUrl(),
				PrecisionInfo: info,
				RequestBody:   string(node.Request.Body()),
				ResponseBody:  string(node.Response.Body()),
			},
		)
		if _, e := node.task.svcCtx.RelationProducer.Send(
			node.task.ctx, base.NewTask(
				constants.MQTaskTypeRelationBindCaseToService,
				payload,
				base.WithRetentionOptions(5*time.Minute),
			), base.QueuePriorityDefault,
		); e != nil {
			node.task.Errorf(
				"failed to send task to mq, task_id: %s, type: %s, payload: %s, error: %+v",
				taskID, constants.MQTaskTypeRelationBindCaseToService, payload, e,
			)
		} else {
			node.task.Infof(
				"send task to mq successfully, task_id: %s, type: %s, payload: %s",
				taskID, constants.MQTaskTypeRelationBindCaseToService, payload,
			)
		}
	}()

	rule := node.task.RetryRule()
	ruleBytes := jsonx.MarshalIgnoreError(rule)
	for i := 0; i <= rule.Retry; i++ {
		if i != 0 && err == nil {
			node.task.Infof(
				"after retrying %d times, the HTTP node executed successfully, task_id: %s, execute_id: %s, node_id: %s, rule: %s",
				i, taskID, executeID, nodeID, ruleBytes,
			)
			break
		} else if i != 0 {
			time.Sleep(rule.Interval)

			node.task.Infof(
				"retry to execute the HTTP node, task_id: %s, execute_id: %s, node_id: %s, rule: %s, index: %d, error: %+v",
				taskID, executeID, nodeID, ruleBytes, i, err,
			)
			node.logW.ResetBeforeRetry()
			node.Response.Reset()
		}
		node.task.Debugf(
			"ready to execute the HTTP node, task_id: %s, execute_id: %s, node_id: %s, index: %d",
			taskID, executeID, nodeID, i,
		)

		err = node.doRequest()
		if err != nil {
			continue
		}

		node.logW.SetResponseStatusCode(node.Response.StatusCode())
		node.logW.SetResponseBody(string(node.Response.Body()))

		node.processingRespHeader()
		err = node.processingAssertions()
		if err != nil {
			continue
		}

		node.processingExports()
	}

	if err != nil {
		// 注：这里不能使用`errors.As(err, &he)`进行判断
		if he, ok := err.(*HttpError); ok {
			if rule.Retry > 0 {
				err = node.UpdateFailureStatef(
					he.Code(), "重试%d次后仍然失败，最后一次的失败原因：%s", rule.Retry, he.Error(),
				)
			} else {
				err = node.UpdateFailureStatef(he.Code(), "%s", he.Error())
			}
		} else {
			node.task.Errorf(
				"invalid error type, task_id: %s, execute_id: %s, node_id: %s, expected: %T, but got %T",
				taskID, executeID, nodeID, (*HttpError)(nil), err,
			)
			err = node.UpdateInvalidStatef(codes.NodeInvalid, "无效的错误类型，本次执行的失败原因：%s", err.Error())
		}
	}

	return err
}

func (node *PrecisionTestingHttpNode) setPrecisionInfo() {
	// 执行（非精准）测试计划的时候需要在http请求头携带项目id、通配环境id和测试用例id
	if node.task.ExecuteType() == managerpb.ApiExecutionDataType_API_PLAN {
		planPurpose := node.task.VarsPool.GetGlobal()[Variable_PlanPurpose]
		var planPurposeStr string
		if planPurpose != nil {
			planPurposeStr = planPurpose.(string)
		}
		if planPurposeStr != commonpb.PurposeType_name[int32(commonpb.PurposeType_PRECISION_TESTING)] {
			generalConfigId := node.task.VarsPool.GetGlobal()[Variable_GeneralConfigId]
			var probeGeneralConfigId string
			if generalConfigId != nil {
				probeGeneralConfigId = generalConfigId.(string)
			}
			var interfaceDocumentId string
			interfaceCase, flag := node.task.Source().Data.(*pb.WorkerReq_InterfaceCase)
			if flag {
				interfaceDocumentId = interfaceCase.InterfaceCase.InterfaceId
			}
			testCaseId := node.task.rootNode.Id()
			projectId := node.task.ProjectId()

			testCaseId = testCaseId + "::" + interfaceDocumentId

			node.PrecisionInfo = &PrecisionInfo{
				ProbeProjectId:       projectId,
				ProbeGeneralConfigId: probeGeneralConfigId,
				ProbeTestCaseId:      testCaseId,
			}
		}
	}
}

func (node *PrecisionTestingHttpNode) getPrecisionInfo() *relationpb.PrecisionInfo {
	var (
		req             = node.task.Source()
		projectID       = req.GetProjectId()
		generalConfigID = req.GetGeneralConfig().GetConfigId()

		documentID, caseID string
	)

	switch v := req.GetData().(type) {
	case *pb.WorkerReq_Case:
		caseID = v.Case.GetCaseId()
	case *pb.WorkerReq_InterfaceCase:
		documentID = v.InterfaceCase.GetDocumentId() // 注：`v.InterfaceCase.GetInterfaceId()`设置的是`service_id`，而不是`document_id`
		caseID = v.InterfaceCase.GetInterfaceCaseId()
	}

	return &relationpb.PrecisionInfo{
		ProjectId:       projectID,
		GeneralConfigId: generalConfigID,
		DocumentId:      documentID,
		CaseId:          caseID,
	}
}
