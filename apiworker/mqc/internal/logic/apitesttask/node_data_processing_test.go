package apitesttask

import (
	"testing"

	managerpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

func test_dataProcessing_new_ApiExecutionData(id string) *managerpb.ApiExecutionData {
	execData := &managerpb.ApiExecutionData{
		Id:   "Processing" + id,
		Type: managerpb.ApiExecutionDataType_DATA_PROCESSING,
		Data: &managerpb.ApiExecutionData_Processing{
			Processing: &managerpb.DataProcessingComponent{
				Processes: []*managerpb.DataProcessingComponent_Process{
					{
						Name: "cid",
						Function: &managerpb.VariableFunction{
							Name: "add",
							Type: managerpb.FunctionType_CUSTOM,
							Parameters: []*managerpb.VariableFunction_Parameter{
								{
									Name:   "v1",
									Source: managerpb.VariableSource_MANUAL,
									Manual: &managerpb.VariableManual{
										Value: "1",
									},
								},
								{
									Name:   "v2",
									Source: managerpb.VariableSource_MANUAL,
									Manual: &managerpb.VariableManual{
										Value: "2",
									},
								},
							},
						},
					},
				},
			},
		},
	}
	return execData
}

func TestRunFunction(t *testing.T) {
	task := test_new_task()
	task.VarsPool.SetNodeExportVar("HTTP_1", "cid", "124")

	exec := test_dataProcessing_new_ApiExecutionData("1")
	node, err := NewDataProcessingNode(task, exec, nil, true)
	if err != nil {
		t.Errorf("err: %s", err)
		t.FailNow()
	}

	err = RunNode(node)
	if err != nil {
		t.Errorf("err: %s", err)
		t.FailNow()
	}

	result, ok := task.VarsPool.GetNodeExportVar(node.Id(), "cid")
	if !ok {
		t.Errorf("未找到出参")
		t.FailNow()
	}

	t.Logf("出参: type:%T, val:%v", result, result)
}

func TestRunFunction_Without(t *testing.T) {
	task := test_new_task()

	data := &managerpb.ApiExecutionData{
		Id:   "Processing" + "_without",
		Type: managerpb.ApiExecutionDataType_DATA_PROCESSING,
		Data: &managerpb.ApiExecutionData_Processing{
			Processing: &managerpb.DataProcessingComponent{
				Processes: []*managerpb.DataProcessingComponent_Process{
					{
						Name: "without",
						Function: &managerpb.VariableFunction{
							Name: "without",
							Type: managerpb.FunctionType_BUILTIN,
							Parameters: []*managerpb.VariableFunction_Parameter{
								{
									Name:   "l",
									Source: managerpb.VariableSource_MANUAL,
									Manual: &managerpb.VariableManual{
										Value: "[1, 2, 3, 4, 5]",
									},
								},
								{
									Name:   "e",
									Source: managerpb.VariableSource_MANUAL,
									Manual: &managerpb.VariableManual{
										Value: "3",
									},
								},
								{
									Name:   "e",
									Source: managerpb.VariableSource_MANUAL,
									Manual: &managerpb.VariableManual{Value: "2"},
								},
							},
						},
					},
				},
			},
		},
	}
	node, err := NewDataProcessingNode(task, data, nil, true)
	if err != nil {
		t.Fatalf("NewDataProcessingNode error: %+v", err)
	}

	err = RunNode(node)
	if err != nil {
		t.Fatalf("RunNode error: %+v", err)
	}

	result, ok := task.VarsPool.GetNodeExportVar(node.Id(), "without")
	if !ok {
		t.Fatalf("未找到出参")
	}

	t.Logf("出参: type:%T, val:%v", result, result)
}

func TestRunFunction_JMESPath(t *testing.T) {
	task := test_new_task()

	data := &managerpb.ApiExecutionData{
		Id:   "Processing" + "_jmespath",
		Type: managerpb.ApiExecutionDataType_DATA_PROCESSING,
		Data: &managerpb.ApiExecutionData_Processing{
			Processing: &managerpb.DataProcessingComponent{
				Processes: []*managerpb.DataProcessingComponent_Process{
					{
						Name: "uid",
						Function: &managerpb.VariableFunction{
							Name: "jmespath",
							Type: managerpb.FunctionType_BUILTIN,
							Parameters: []*managerpb.VariableFunction_Parameter{
								{
									Name:   "expression",
									Source: managerpb.VariableSource_MANUAL,
									Manual: &managerpb.VariableManual{
										Value: "\"[?member.uid == `2492619`]|[0].member.uid\"",
									},
								},
								{
									Name:   "data",
									Source: managerpb.VariableSource_MANUAL,
									Manual: &managerpb.VariableManual{
										Value: `[{"admin_role":1,"member":{"account":"tt*********","nick_name":"*********","uid":2501418}},{"admin_role":2,"member":{"account":"tt*********","nick_name":"*********","uid":2492619}}]`,
									},
								},
							},
						},
					},
				},
			},
		},
	}
	node, err := NewDataProcessingNode(task, data, nil, true)
	if err != nil {
		t.Fatalf("NewDataProcessingNode error: %+v", err)
	}

	err = RunNode(node)
	if err != nil {
		t.Fatalf("RunNode error: %+v", err)
	}

	result, ok := task.VarsPool.GetNodeExportVar(node.Id(), "uid")
	if !ok {
		t.Fatalf("未找到出参")
	}

	t.Logf("出参: type:%T, val:%v", result, result)
}

func TestRunFunction_RandInt(t *testing.T) {
	task := test_new_task()

	data := &managerpb.ApiExecutionData{
		Id:   "Processing" + "_randInt",
		Type: managerpb.ApiExecutionDataType_DATA_PROCESSING,
		Data: &managerpb.ApiExecutionData_Processing{
			Processing: &managerpb.DataProcessingComponent{
				Processes: []*managerpb.DataProcessingComponent_Process{
					{
						Name: "randInt",
						Function: &managerpb.VariableFunction{
							Name: "randInt",
							Type: managerpb.FunctionType_BUILTIN,
							Parameters: []*managerpb.VariableFunction_Parameter{
								{
									Name:   "min",
									Source: managerpb.VariableSource_MANUAL,
									Manual: &managerpb.VariableManual{
										Value: "1",
									},
								},
								{
									Name:   "max",
									Source: managerpb.VariableSource_MANUAL,
									Manual: &managerpb.VariableManual{
										Value: "5",
									},
								},
							},
						},
					},
				},
			},
		},
	}
	node, err := NewDataProcessingNode(task, data, nil, true)
	if err != nil {
		t.Fatalf("NewDataProcessingNode error: %+v", err)
	}

	err = RunNode(node)
	if err != nil {
		t.Fatalf("RunNode error: %+v", err)
	}

	result, ok := task.VarsPool.GetNodeExportVar(node.Id(), "randInt")
	if !ok {
		t.Fatalf("未找到出参")
	}

	t.Logf("出参: type:%T, val:%v", result, result)
}

func TestRunFunction_ToString(t *testing.T) {
	task := test_new_task()

	data := &managerpb.ApiExecutionData{
		Id:   "Processing" + "_str",
		Type: managerpb.ApiExecutionDataType_DATA_PROCESSING,
		Data: &managerpb.ApiExecutionData_Processing{
			Processing: &managerpb.DataProcessingComponent{
				Processes: []*managerpb.DataProcessingComponent_Process{
					{
						Name: "str",
						Function: &managerpb.VariableFunction{
							Name: "str",
							Type: managerpb.FunctionType_BUILTIN,
							Parameters: []*managerpb.VariableFunction_Parameter{
								{
									Name:   "v",
									Source: managerpb.VariableSource_MANUAL,
									Manual: &managerpb.VariableManual{
										Value: "2490770",
									},
								},
							},
						},
					},
				},
			},
		},
	}
	node, err := NewDataProcessingNode(task, data, nil, true)
	if err != nil {
		t.Fatalf("NewDataProcessingNode error: %+v", err)
	}

	err = RunNode(node)
	if err != nil {
		t.Fatalf("RunNode error: %+v", err)
	}

	result, ok := task.VarsPool.GetNodeExportVar(node.Id(), "str")
	if !ok {
		t.Fatalf("未找到出参")
	}

	t.Logf("出参: type:%T, val:%v", result, result)
}
