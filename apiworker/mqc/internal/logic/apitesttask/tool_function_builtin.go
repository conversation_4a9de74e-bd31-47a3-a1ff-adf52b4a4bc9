package apitesttask

import (
	"encoding/json"
	"reflect"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/template"
	managerpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

// RunFunction_Builtin 执行函数-内置函数
func (node *BaseNode) RunFunction_Builtin(vf *managerpb.VariableFunction) (result any, err error) {
	args := node.RunFunction_GetArgs(vf.GetParameters())

	goargs := make([]any, len(args))
	for i, arg := range args {
		goargs[i] = CompatibleValue(arg.Value)
	}

	return template.Call(vf.GetName(), goargs...)
}

// CompatibleValue 类型兼容
func CompatibleValue(v any) any {
	rv := reflect.ValueOf(v)

	switch rv.Kind() {
	case reflect.Array, reflect.Slice:
		ret := make([]any, rv.Len())
		for i := 0; i < rv.Len(); i++ {
			ret[i] = CompatibleValue(rv.Index(i).Interface())
		}

		return ret
	case reflect.Map:
		ret := make(map[string]any, rv.Len())
		for _, k := range rv.MapKeys() {
			ret[k.String()] = CompatibleValue(rv.MapIndex(k).Interface())
		}

		return ret
	}

	// json.Number类型兼容
	if _, ok := v.(json.Number); ok {
		return JsonNumberToNumber(v)
	}
	return v
}

// JsonNumberToNumber 兼容函数，用于处理json.Number带来的类型不匹配问题，所有number都以float64处理
// 注意，一个badcase：jmespath函数传递的object，对number值的要求必须是float64，因此不使用int64
func JsonNumberToNumber(v any) any {
	if jnum, ok := v.(json.Number); ok {
		if fv, err := jnum.Float64(); err == nil {
			return fv
		}
	}
	return v
}
