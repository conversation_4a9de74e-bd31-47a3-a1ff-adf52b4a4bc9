package apitesttask

import (
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/codes"
	managerpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type StartNode struct {
	*BaseNode
	logW *StartNodeLogWriter
}

func NewStartNode(task *ApitestTask, data *managerpb.ApiExecutionData, parent Node, isShell bool) (Node, error) {
	node := &StartNode{}
	var err error
	node.BaseNode, err = NewBaseNode(task, data, node, parent, isShell)
	if err != nil {
		return nil, node.UpdateInvalidStatef(codes.NodeInvalid, "%s", err)
	}

	node.logW = NewStartNodeLogWriter(node.BaseNode.logW)

	return node, nil
}

func (node *StartNode) run() (err error) {
	return nil
}

func (node *StartNode) Content() string {
	return node.logW.toJson()
}

func (node *StartNode) Logger() NodeLogger {
	return node.logW
}
