package apitesttask

import (
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/codes"
	managerpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

// SetupNode 前置组件
type SetupNode struct {
	*BaseComboNode

	source *managerpb.SetupComponent
	logW   *SetupNodeLogWriter
}

func NewSetupNode(task *ApitestTask, data *managerpb.ApiExecutionData, parent Node, isShell bool) (Node, error) {
	node := &SetupNode{}
	var err error
	node.BaseComboNode, err = NewBaseComboNode(task, data, node, parent, isShell)
	if err != nil {
		return nil, node.UpdateInvalidStatef(codes.NodeInvalid, "%s", err)
	}

	source := data.GetSetup()
	node.source = source
	node.SetImports(node.source.GetImports())
	node.SetExports(node.source.GetExports())

	node.logW = NewSetupNodeLogWriter(node.BaseComboNode.logW)

	err = node.NewChildren(task, data)
	if err != nil {
		return node, node.UpdateInvalidStatef(codes.NodeInvalid, "%s", err)
	}

	return node, nil
}

func (node *SetupNode) Content() string {
	return node.logW.toJson()
}

func (node *SetupNode) Logger() NodeLogger {
	return node.logW
}
