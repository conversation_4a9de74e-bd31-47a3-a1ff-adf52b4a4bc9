package apitesttask

import (
	"testing"

	managerpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

func TestRunFunction_Builtin(t *testing.T) {
	vf := managerpb.VariableFunction{
		Name: "toJson",
		Type: managerpb.FunctionType_BUILTIN,
		Parameters: []*managerpb.VariableFunction_Parameter{
			{
				Manual: &managerpb.VariableManual{
					Value: "{\"admin_role\": 1}",
				},
				Source: managerpb.VariableSource_MANUAL,
			},
		},
	}

	task := test_new_task()
	exec := test_sqlExecution_new_ApiExecutionData()
	node, err := NewBaseNode(task, exec, nil, nil, true)
	if err != nil {
		t.Errorf("new base node err:%s", err)
		t.FailNow()
	}

	result, err := node.RunFunction_Builtin(&vf)
	if err != nil {
		t.<PERSON>("RunFunction_Builtin err:%s", err)
		t.FailNow()
	}

	t.Logf("result = %v", result)
}

func TestRunFunction_Builtin_Date(t *testing.T) {
	vf := managerpb.VariableFunction{
		Name: "date",
		Type: managerpb.FunctionType_BUILTIN,
		Parameters: []*managerpb.VariableFunction_Parameter{
			{
				Manual: &managerpb.VariableManual{
					Value: "\"2006-01-02 15:04:05\"",
				},
				Source: managerpb.VariableSource_MANUAL,
			},
			{
				Manual: &managerpb.VariableManual{
					Value: "1657715417",
				},
				Source: managerpb.VariableSource_MANUAL,
			},
		},
	}

	task := test_new_task()
	exec := test_sqlExecution_new_ApiExecutionData()
	node, err := NewBaseNode(task, exec, nil, nil, true)
	if err != nil {
		t.Errorf("new base node err:%s", err)
		t.FailNow()
	}

	result, err := node.RunFunction_Builtin(&vf)
	if err != nil {
		t.Errorf("RunFunction_Builtin err:%s", err)
		t.FailNow()
	}

	t.Logf("result = %v", result)
}
