package apitesttask

import (
	"context"
	"fmt"
	"strings"
	"sync"
	"time"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"
	"github.com/zeromicro/go-zero/core/logx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/security"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/apiworker/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/apiworker/metrics"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/apiworker/mqc/internal/config"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/apiworker/mqc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/apiworker/mqc/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	commonpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/utils"
	dutils "gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/common/utils"
	dispatcherpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/rpc/pb"
	managerpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
	reporterpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
)

var stateToResult = map[dispatcherpb.ComponentState]common.Result{
	dispatcherpb.ComponentState_Success: common.Success,
	dispatcherpb.ComponentState_Warning: common.Warning,
	dispatcherpb.ComponentState_Failure: common.Failure,
	dispatcherpb.ComponentState_Panic:   common.Panic,
	dispatcherpb.ComponentState_Invalid: common.Invalid,
}

type ApitestTask struct {
	logx.Logger
	ctx      context.Context
	svcCtx   *svc.ServiceContext
	base64pb string // base64后的请求体protobuf

	// 状态管理器
	VarsPool      *VarsPool // 记录变量
	TearDownFuncs []func()  // 注册释放函数

	// 任务颗粒度的全局变量
	lock               sync.RWMutex                      // 任务颗粒度的读写锁
	_source            *dispatcherpb.WorkerReq           // 任务执行请求
	runAble            bool                              // 能否继续执行节点, 当runAble=false,剩余节点将被跳过(后置组件除外)
	ended              bool                              // 节点执行逻辑是否已经结束, 当遇到第一个end节点，剩余节点将被无视
	accountEnv         map[int64]*commonpb.AccountConfig // 池账号环境配置
	verify             bool                              // http是否验证服务器的TLS证书
	state              dispatcherpb.ComponentState       // 任务运行状态
	rootNode           Node                              // 根节点
	componentExecuteId string                            // 根节点的组件运行id
	nodeCount          int                               // 节点数量
	retryRule          config.HTTPRetryRule              // 当前任务的HTTP重试规则
	securityHeaders    map[string]string                 // `TT`安全请求头

	// mock管理
	debug   bool              // 是否为debug模式
	mockRpc map[string][]byte // rpc的mock数据，key: ${node_score}:${rpc_name}:${rpc_score}
}

func NewApitestTask(ctx context.Context, svcCtx *svc.ServiceContext) *ApitestTask {
	headers := make(map[string]string, constants.ConstDefaultMakeMapSize)
	security.HandleHeaders(headers)

	return &ApitestTask{
		Logger:          logx.WithContext(ctx),
		ctx:             ctx,
		svcCtx:          svcCtx,
		VarsPool:        NewVarsPool(),
		TearDownFuncs:   make([]func(), 0, 10),
		state:           dispatcherpb.ComponentState_Pending,
		securityHeaders: headers,
	}
}

func (task *ApitestTask) Run(req *dispatcherpb.WorkerReq) (err error) {
	task.setup(req)

	stop, err := dutils.GetStopStatus(task.ctx, task.svcCtx.Redis, task.TaskId())
	if err != nil {
		return err
	}

	if stop {
		task.SetState(dispatcherpb.ComponentState_Stop)
		return nil
	}

	err = task.run()

	task.teardown()

	return err
}

func (task *ApitestTask) setup(req *dispatcherpb.WorkerReq) {
	task.SetSource(req)
	task.SetRunAble(true)
	task.SetEnded(false)
	task.SetDebug(req.GetDebug())
	task.SetComponentExecuteId(task.req2ComponentExecuteId(req))
	task.SetRetryRule()
	if task.retryRule.Retry != 0 {
		task.Infof(
			"the project has been set a http retry rule, project_id: %s, retry: %d, interval: %s",
			task.retryRule.ProjectID, task.retryRule.Retry, task.retryRule.Interval,
		)
	}

	task.InitGlobalEnv(req.GetGeneralConfig())
	task.InitAccountEnv(req.GetAccountConfig())
	task.SetState(dispatcherpb.ComponentState_Started)
}

func (task *ApitestTask) run() (err error) {
	task.rootNode, err = GetNode(task, task.Source().GetNodeData(), nil, true)
	if err != nil {
		task.SetState(dispatcherpb.ComponentState_Invalid)
		task.Errorf("GetNode, %s, %s", err, task.Source().GetNodeData().GetType().String())
		return err
	}

	err = RunNode(task.rootNode)
	if err != nil {
		task.SetState(task.rootNode.State())
		task.Errorf("RunNode, %s, %s", task.Source().GetExecuteId(), err)
		return err
	}

	task.SetState(task.rootNode.State())
	return nil
}

func (task *ApitestTask) teardown() {
	defer task.saveMetrics()

	for _, f := range task.TearDownFuncs {
		f()
	}

	request := &reporterpb.PutRecordRequest{
		TaskId:                   task.TaskId(),
		ProjectId:                task.ProjectId(),
		ExecuteId:                task.ExecuteId(),
		ExecuteType:              task.ExecuteType().String(),
		ComponentId:              task.rootNode.Id(),
		ComponentType:            task.rootNode.Type(),
		ComponentExecuteId:       task.ComponentExecuteId(),
		ParentComponentId:        task.req2ParentComponentId(task.Source()),
		ParentComponentExecuteId: task.req2ParentComponentExecuteId(task.Source()),
		Times:                    task.rootNode.Times(),
		Status:                   task.State().String(),
		EndedAt:                  time.Now().UnixNano() / 1e6,
		Version:                  task.rootNode.Version(),
		ExecutedBy:               task.ExecuteUserId(),
		Content:                  task.rootNode.Content(),
	}

	// 更新任务执行状态(即更新root节点的状态)
	_, err := task.svcCtx.ReporterRpc.Modify(task.ctx, request)
	if err != nil {
		task.Errorf("Rpc.Reporter.Modify, %s", err)
	}
}

func (task *ApitestTask) GetCallback() *dispatcherpb.CallbackReq {
	callback := &dispatcherpb.CallbackReq{
		TriggerMode:  task.Source().GetTriggerMode(),
		TriggerRule:  task.Source().GetTriggerRule(),
		ProjectId:    task.Source().GetProjectId(),
		TaskId:       task.Source().GetTaskId(),
		ExecuteType:  task.Source().GetExecuteType(),
		CallbackType: task.req2CallbackType(task.Source()),
		PurposeType:  task.Source().GetPurposeType(),
	}

	task.req2CallbackData(task.Source(), callback, task.State())
	return callback
}

func (task *ApitestTask) GetCallbackPB() []byte {
	// buf, _ := proto.Marshal(task.GetCallback())
	// return buf
	return protobuf.MarshalJSONIgnoreError(task.GetCallback())
}

func (task *ApitestTask) RegisterTeardownFunc(f func()) {
	task.lock.Lock()
	defer task.lock.Unlock()
	task.TearDownFuncs = append(task.TearDownFuncs, f)
}

func (task *ApitestTask) SetBase64RequestPb(data string) {
	task.lock.Lock()
	defer task.lock.Unlock()
	task.base64pb = data
}

func (task *ApitestTask) Base64RequestPb() string {
	task.lock.RLock()
	defer task.lock.RUnlock()
	return task.base64pb
}

func (task *ApitestTask) SetSource(source *dispatcherpb.WorkerReq) {
	task.lock.Lock()
	defer task.lock.Unlock()
	task._source = source
}

func (task *ApitestTask) Source() *dispatcherpb.WorkerReq {
	task.lock.RLock()
	defer task.lock.RUnlock()
	return task._source
}

func (task *ApitestTask) SetRunAble(v bool) {
	task.lock.Lock()
	defer task.lock.Unlock()
	task.runAble = v
}

func (task *ApitestTask) RunAble() bool {
	task.lock.RLock()
	defer task.lock.RUnlock()
	return task.runAble
}

func (task *ApitestTask) SetEnded(v bool) {
	task.lock.Lock()
	defer task.lock.Unlock()
	task.ended = v
}

func (task *ApitestTask) Ended() bool {
	task.lock.RLock()
	defer task.lock.RUnlock()
	return task.ended
}

func (task *ApitestTask) TaskId() string {
	task.lock.RLock()
	defer task.lock.RUnlock()
	return task.Source().GetTaskId()
}

func (task *ApitestTask) ExecuteId() string {
	task.lock.RLock()
	defer task.lock.RUnlock()
	return task.Source().GetExecuteId()
}

func (task *ApitestTask) ExecuteType() managerpb.ApiExecutionDataType {
	task.lock.RLock()
	defer task.lock.RUnlock()
	return task.Source().GetExecuteType()
}

func (task *ApitestTask) ProjectId() string {
	task.lock.RLock()
	defer task.lock.RUnlock()
	return task.Source().GetProjectId()
}

func (task *ApitestTask) AccountEnv(productType int64) (*commonpb.AccountConfig, bool) {
	task.lock.RLock()
	defer task.lock.RUnlock()
	c, ok := task.accountEnv[productType]
	return c, ok
}

func (task *ApitestTask) SetVerify(v bool) {
	task.lock.Lock()
	defer task.lock.Unlock()
	task.verify = v
}

func (task *ApitestTask) Verify() bool {
	task.lock.RLock()
	defer task.lock.RUnlock()
	return task.verify
}

func (task *ApitestTask) ExecuteUserId() string {
	task.lock.RLock()
	defer task.lock.RUnlock()
	return task.Source().GetUserId()
}

func (task *ApitestTask) ExecuteUserName() string {
	task.lock.RLock()
	defer task.lock.RUnlock()
	return task.Source().GetUser()
}

func (task *ApitestTask) SetState(s dispatcherpb.ComponentState) {
	task.lock.Lock()
	defer task.lock.Unlock()
	task.state = s
}

func (task *ApitestTask) State() dispatcherpb.ComponentState {
	task.lock.RLock()
	defer task.lock.RUnlock()
	return task.state
}

func (task *ApitestTask) SetComponentExecuteId(id string) {
	task.lock.Lock()
	defer task.lock.Unlock()
	task.componentExecuteId = id
}

func (task *ApitestTask) ComponentExecuteId() string {
	task.lock.RLock()
	defer task.lock.RUnlock()
	return task.componentExecuteId
}

func (task *ApitestTask) SetDebug(v bool) {
	task.lock.Lock()
	defer task.lock.Unlock()
	task.debug = v
}

func (task *ApitestTask) Debug() bool {
	task.lock.RLock()
	defer task.lock.RUnlock()
	return task.debug
}

func (task *ApitestTask) AddNodeCount(cnt int) {
	task.lock.Lock()
	defer task.lock.Unlock()
	task.nodeCount += cnt
}

func (task *ApitestTask) NodeCount() int {
	task.lock.RLock()
	defer task.lock.RUnlock()
	return task.nodeCount
}

func (task *ApitestTask) SetRetryRule() {
	task.lock.Lock()
	defer task.lock.Unlock()
	task.retryRule = task.svcCtx.HTTPRetryRules[task._source.GetProjectId()]
}

func (task *ApitestTask) RetryRule() config.HTTPRetryRule {
	task.lock.RLock()
	defer task.lock.RUnlock()
	return task.retryRule
}

func (task *ApitestTask) req2ComponentExecuteId(req *dispatcherpb.WorkerReq) string {
	switch req.GetWorkerType() {
	case dispatcherpb.WorkerType_WorkerType_API_CASE:
		return req.GetCase().GetCaseExecuteId()
	case dispatcherpb.WorkerType_WorkerType_API_COMPONENT_GROUP:
		return req.GetComponentGroup().GetComponentGroupExecuteId()
	case dispatcherpb.WorkerType_WorkerType_INTERFACE_CASE:
		return req.GetInterfaceCase().GetInterfaceCaseExecuteId()
	}
	return ""
}

func (task *ApitestTask) req2ParentComponentId(req *dispatcherpb.WorkerReq) string {
	switch req.GetWorkerType() {
	case dispatcherpb.WorkerType_WorkerType_API_CASE:
		return req.GetCase().GetSuiteId()
	case dispatcherpb.WorkerType_WorkerType_API_COMPONENT_GROUP:
		return ""
	case dispatcherpb.WorkerType_WorkerType_INTERFACE_CASE:
		return req.GetInterfaceCase().GetInterfaceId()
	}
	return ""
}

func (task *ApitestTask) req2ParentComponentExecuteId(req *dispatcherpb.WorkerReq) string {
	switch req.GetWorkerType() {
	case dispatcherpb.WorkerType_WorkerType_API_CASE:
		return req.GetCase().GetSuiteExecuteId()
	case dispatcherpb.WorkerType_WorkerType_API_COMPONENT_GROUP:
		return ""
	case dispatcherpb.WorkerType_WorkerType_INTERFACE_CASE:
		return req.GetInterfaceCase().GetInterfaceExecuteId()
	}
	return ""
}

func (task *ApitestTask) req2CallbackType(req *dispatcherpb.WorkerReq) dispatcherpb.CallbackType {
	callbackType := dispatcherpb.CallbackType_CallbackType_UNKNOWN
	switch req.GetWorkerType() {
	case dispatcherpb.WorkerType_WorkerType_API_CASE:
		callbackType = dispatcherpb.CallbackType_CallbackType_API_CASE
	case dispatcherpb.WorkerType_WorkerType_INTERFACE_CASE:
		callbackType = dispatcherpb.CallbackType_CallbackType_INTERFACE_CASE
	}
	return callbackType
}

func (task *ApitestTask) req2CallbackData(
	req *dispatcherpb.WorkerReq, callback *dispatcherpb.CallbackReq, state dispatcherpb.ComponentState,
) {
	switch req.GetWorkerType() {
	case dispatcherpb.WorkerType_WorkerType_API_CASE:
		callback.Data = &dispatcherpb.CallbackReq_Case{
			Case: &dispatcherpb.CaseCallbackData{
				SuiteId:        req.GetCase().GetSuiteId(),
				SuiteExecuteId: req.GetCase().GetSuiteExecuteId(),
				CaseId:         req.GetCase().GetCaseId(),
				CaseExecuteId:  req.GetCase().GetCaseExecuteId(),
				CaseState:      state,
				Version:        req.GetCase().GetVersion(),
			},
		}
	case dispatcherpb.WorkerType_WorkerType_INTERFACE_CASE:
		callback.Data = &dispatcherpb.CallbackReq_InterfaceCase{
			InterfaceCase: &dispatcherpb.InterfaceCaseCallbackData{
				InterfaceId:            req.GetInterfaceCase().GetInterfaceId(),
				InterfaceExecuteId:     req.GetInterfaceCase().GetInterfaceExecuteId(),
				InterfaceCaseId:        req.GetInterfaceCase().GetInterfaceCaseId(),
				InterfaceCaseExecuteId: req.GetInterfaceCase().GetInterfaceCaseExecuteId(),
				InterfaceCaseState:     state,
				Version:                req.GetInterfaceCase().GetVersion(),
			},
		}
	}
}

func (task *ApitestTask) saveMetrics() {
	var (
		taskID      = task._source.GetTaskId()
		executeID   = task._source.GetExecuteId()
		executeType = task._source.GetExecuteType()
		nodeType    = task._source.GetNodeData().GetType()
		state       = task.state
	)

	if executeType != managerpb.ApiExecutionDataType_API_PLAN {
		return
	} else if nodeType != managerpb.ApiExecutionDataType_API_CASE && nodeType != managerpb.ApiExecutionDataType_INTERFACE_CASE {
		return
	}

	result, ok := stateToResult[state]
	if !ok {
		task.Warnf("no need to save metrics, task_id: %s, execute_id: %s, state: %s", taskID, executeID, state.String())
		return
	}

	projectInfo, err := task.getProjectInfo()
	if err != nil {
		task.Errorf("failed to get project info, task_id: %s, execute_id: %s, error: %+v", taskID, executeID, err)
		return
	}

	planInfo, suiteInfo, caseInfo, err := task.getPlanSuiteCaseInfo()
	if err != nil {
		task.Errorf(
			"failed to get plan suite case info, task_id: %s, execute_id: %s, error: %+v", taskID, executeID, err,
		)
		return
	}

	suiteID := suiteInfo.SuiteID
	if strings.HasPrefix(suiteID, utils.ConstServiceIdPrefix) {
		suiteID = "" // 集合ID为服务ID时该集合为虚拟集合，把集合ID设置为空，避免维度爆炸
	}

	metrics.SaveResult(
		metrics.CaseResult{
			TaskID:         taskID,
			ProjectID:      projectInfo.ProjectID,
			ProjectName:    projectInfo.ProjectName,
			PlanExecuteID:  planInfo.PlanExecuteID,
			PlanID:         planInfo.PlanID,
			PlanName:       planInfo.PlanName,
			SuiteExecuteID: suiteInfo.SuiteExecuteID,
			SuiteID:        suiteID,
			SuiteName:      suiteInfo.SuiteName,
			CaseExecuteID:  caseInfo.CaseExecuteID,
			CaseID:         caseInfo.CaseID,
			CaseName:       caseInfo.CaseName,
			MaintainedBy:   caseInfo.MaintainedBy,
			Result:         result,
			Elapsed:        task.rootNode.Elapsed(),
			StartedAt:      task.rootNode.StartedAt(),
			EndedAt:        task.rootNode.EndedAt(),
		},
	)
	task.Debugf("finish to save metrics, task_id: %s, execute_id: %s, state: %s", taskID, executeID, state.String())
}

func (task *ApitestTask) getProjectInfo() (*types.ProjectInfo, error) {
	var (
		projectID = task._source.GetProjectId()
		key       = fmt.Sprintf("%s:%s", common.ConstCacheApiWorkerProjectInfoProjectIDPrefix, projectID)

		info *types.ProjectInfo
	)

	result, err := task.svcCtx.Redis.Get(task.ctx, key).Result()
	if err == nil && result != "" {
		info = &types.ProjectInfo{}
		if err = jsonx.UnmarshalFromString(result, info); err != nil {
			task.Errorf("failed to unmarshal project info, key: %s, value: %s, error: %+v", key, result, err)
		}

		if info.ProjectID != "" && info.ProjectName != "" {
			return info, nil
		}
	}

	defer func() {
		if info != nil && info.ProjectID != "" && info.ProjectName != "" {
			value := jsonx.MarshalToStringIgnoreError(info)
			if result, err = task.svcCtx.Redis.Set(
				task.ctx, key, value, common.ConstCacheProjectInfoExpireTime,
			).Result(); err != nil {
				task.Errorf(
					"failed to set project info, key: %s, value: %s, result: %s, error: %+v", key, value, result, err,
				)
			}
		}
	}()

	out, err := task.svcCtx.ManagerRpc.ViewProject(
		task.ctx, &managerpb.ViewProjectReq{
			ProjectId: projectID,
		},
	)
	if err != nil {
		return nil, err
	}

	info = &types.ProjectInfo{
		ProjectID:   out.GetProject().GetProjectId(),
		ProjectName: out.GetProject().GetName(),
	}

	return info, nil
}

func (task *ApitestTask) getPlanSuiteCaseInfo() (*types.PlanInfo, *types.SuiteInfo, *types.CaseInfo, error) {
	var (
		nodeType = task._source.GetNodeData().GetType()

		planInfo  *types.PlanInfo
		suiteInfo *types.SuiteInfo
		caseInfo  *types.CaseInfo
	)

	switch nodeType {
	case managerpb.ApiExecutionDataType_API_CASE:
		data := task._source.GetNodeData().GetCase()
		info := task._source.GetCase()

		planInfo = &types.PlanInfo{
			PlanExecuteID: info.GetPlanExecuteId(),
			PlanID:        info.GetPlanId(),
			PlanName:      info.GetPlanName(),
		}
		suiteInfo = &types.SuiteInfo{
			SuiteExecuteID: info.GetSuiteExecuteId(),
			SuiteID:        info.GetSuiteId(),
			SuiteName:      info.GetSuiteName(),
		}
		caseInfo = &types.CaseInfo{
			CaseExecuteID: info.GetCaseExecuteId(),
			CaseID:        info.GetCaseId(),
			CaseName:      data.GetName(),
			MaintainedBy:  data.GetMaintainedBy(),
		}
	case managerpb.ApiExecutionDataType_INTERFACE_CASE:
		data := task._source.GetNodeData().GetInterfaceCase()
		info := task._source.GetInterfaceCase()

		planInfo = &types.PlanInfo{
			PlanExecuteID: info.GetPlanExecuteId(),
			PlanID:        info.GetPlanId(),
			PlanName:      info.GetPlanName(),
		}
		suiteInfo = &types.SuiteInfo{
			SuiteExecuteID: info.GetInterfaceExecuteId(),
			SuiteID:        info.GetInterfaceId(),
			SuiteName:      info.GetDocumentName(),
		}
		caseInfo = &types.CaseInfo{
			CaseExecuteID: info.GetInterfaceCaseExecuteId(),
			CaseID:        info.GetInterfaceCaseId(),
			CaseName:      data.GetName(),
			MaintainedBy:  data.GetMaintainedBy(),
		}
	default:
		return nil, nil, nil, errors.Errorf("invalid node type: %s", nodeType.String())
	}

	return planInfo, suiteInfo, caseInfo, nil
}
