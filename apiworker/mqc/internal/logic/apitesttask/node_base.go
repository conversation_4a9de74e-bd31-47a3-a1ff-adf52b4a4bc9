package apitesttask

import (
	"encoding/base64"
	"flag"
	"fmt"
	"time"

	"google.golang.org/protobuf/proto"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/codes"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/state"
	dispatcherpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/rpc/pb"
	managerpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
	reporter "gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/client/reporter"
)

type BaseNode struct {
	task        *ApitestTask
	entry       Node
	parent      Node
	parentCombo Node
	logW        *BaseNodeLogWriter
	reporter    Reporter

	shell  bool                // 是否是外壳
	stateM *state.StateManager // 运行状态管理

	NodeId              string                         // 节点id
	NodeType            managerpb.ApiExecutionDataType // 节点类型
	times               int64                          // 运行次数
	StartTime           int64                          // 开始时间
	EndTime             int64                          // 结束时间
	parentId            string                         // 父节点id
	parentComboId       string                         // 父框组件id(注: 父框组件不一定是父组件,比如条件节点)
	isCombo             bool                           // 当前节点是否为框
	nodeExecuteId       string                         // 节点执行id, 由create接口返回
	parentNodeExecuteId string                         // 父节点执行id
	level               int                            // 自身层级
	referencedId        string                         // 引用当前节点 的 节点id
	rankScore           int                            // 节点排名，第N个被初始化
	rpcCount            int                            // 节点内的rpc调用次数

	name         string
	description  string
	version      string
	maintainedBy string
}

func NewBaseNode(task *ApitestTask, data *managerpb.ApiExecutionData, entry, parent Node, isShell bool) (
	*BaseNode, error,
) {
	node := &BaseNode{
		task:   task,
		entry:  entry,
		parent: parent,
	}
	node.logW = NewBaseNodeLogWriter()
	node.NodeId = data.GetId()
	node.NodeType = data.GetType()

	// 外壳层 & 默认设置
	node.SetShell(isShell)
	node.SetParentId(node.Id())
	node.SetParentComboId(node.Id())
	node.SetNodeExecuteId(task.ComponentExecuteId())
	node.stateM = state.NewStateManager(dispatcherpb.ComponentState_Init, GetErrCode(errorx.OK))

	// 上报方式，如果是计划，则压缩上报
	node.reporter = NewBaseReporter()
	if task.ExecuteType() == managerpb.ApiExecutionDataType_API_PLAN {
		node.reporter = NewPlanReporter()
	}

	// 节点执行顺序
	node.task.AddNodeCount(1)
	node.SetRankScore(node.task.NodeCount())

	// 第 1 ~ N 层
	if !node.Shell() {
		node.SetShell(false)
		node.SetParentId(parent.Id())
		node.SetVersion(parent.Version())
		node.SetLevel(parent.Level() + 1)
		node.SetReferencedId(parent.Id())
		node.SetNodeExecuteId("")

		node.SetParentComboId(parent.ParentComboId())
		node.SetParentCombo(parent.ParentCombo())
		if parent.Combo() {
			// 如果父节点是框组件，则当前节点的 父框组件 为 父组件
			node.SetParentComboId(parent.Id())
			node.SetParentCombo(parent)
		}

		if parent.Shell() {
			// 如果父节点是根节点
			node.SetParentNodeExecuteId(node.parent.NodeExecuteId())
		}
	}

	return node, nil
}

func (node *BaseNode) InitReport() (err error) {
	node.StartTime = time.Now().UnixNano() / 1e6
	node.EndTime = node.StartTime // 默认值

	err = node.reporter.Init(node)
	if err != nil {
		return node.UpdateFailureStatef(codes.NodeRecordFailure, "InitReport失败, err: %s", err)
	}
	return nil
}

func (node *BaseNode) setup() (err error) {
	if node.parent != nil {
		node.SetParentNodeExecuteId(node.parent.NodeExecuteId())
	}

	node.StartTime = time.Now().UnixNano() / 1e6 // 刷新开始时间
	node.EndTime = node.StartTime                // 默认值
	node.times += 1
	err = node.reporter.Setup(node)
	if err != nil {
		return node.UpdateFailureStatef(codes.NodeRecordFailure, "Setup 失败, err: %s", err)
	}
	return nil
}

func (node *BaseNode) getRecordRequest() *reporter.PutRecordRequest {
	req := &reporter.PutRecordRequest{
		TaskId:                   node.task.TaskId(),
		ProjectId:                node.task.ProjectId(),
		ExecuteId:                node.task.ExecuteId(),
		ExecuteType:              node.task.ExecuteType().String(),
		ComponentId:              node.Id(),
		ComponentName:            node.Name(),
		ComponentType:            node.Type(),
		ComponentExecuteId:       node.NodeExecuteId(),
		ParentComponentId:        node.ParentId(),
		ParentComponentExecuteId: node.ParentNodeExecuteId(),
		Version:                  node.Version(),
		Times:                    node.Times(),
		Status:                   node.State().String(),
		Content:                  node.Content(),
		ExecutedBy:               node.task.ExecuteUserId(),
		StartedAt:                node.StartTime,
		EndedAt:                  node.EndTime,
	}

	return req
}

func (node *BaseNode) run() error {
	return node.UpdateFailureStatef(codes.NodeNotFunc, "该节点没有实现run方法")
}

func (node *BaseNode) Skip() bool {
	return !node.task.RunAble()
}

func (node *BaseNode) teardown() (err error) {
	node.EndTime = time.Now().UnixNano() / 1e6
	err = node.reporter.Teardown(node)
	if err != nil {
		node.UpdateWarningStatef(codes.NodeRecordFailure, "Teardown 失败, err: %s", err)
		return err
	}
	return nil
}

func (node *BaseNode) SetParentId(parentId string) {
	node.parentId = parentId
}

func (node *BaseNode) ParentId() string {
	return node.parentId
}

func (node *BaseNode) SetParentComboId(id string) {
	node.parentComboId = id
}

func (node *BaseNode) ParentComboId() string {
	return node.parentComboId
}

func (node *BaseNode) SetCombo(v bool) {
	node.isCombo = v
}

func (node *BaseNode) Combo() bool {
	return node.isCombo
}

// SetParentCombo 设置父组件组
func (node *BaseNode) SetParentCombo(n Node) {
	node.parentCombo = n
}

func (node *BaseNode) ParentCombo() Node {
	return node.parentCombo
}

func (node *BaseNode) Id() string {
	return node.NodeId
}

func (node *BaseNode) Type() string {
	return node.NodeType.String()
}

func (node *BaseNode) Name() string {
	return node.name
}

func (node *BaseNode) MaintainedBy() string {
	return node.maintainedBy
}

func (node *BaseNode) Content() string {
	if node.entry == nil {
		return node.logW.toJson()
	}

	return node.entry.Content()
}

func (node *BaseNode) SetError() {
	node.Logger().SetCommon(node)
}

func (node *BaseNode) Error() (code errorx.Code, message, debug string) {
	code = node.stateM.Code()
	message = node.stateM.Message()
	debug = node.stateM.Debug()
	return
}

func (node *BaseNode) SetShell(is bool) {
	node.shell = is
}

func (node *BaseNode) Shell() bool {
	return node.shell
}

func (node *BaseNode) SetVersion(v string) {
	node.version = v
}

func (node *BaseNode) Version() string {
	return node.version
}

func (node *BaseNode) SetNodeExecuteId(id string) {
	node.nodeExecuteId = id
}

func (node *BaseNode) NodeExecuteId() string {
	return node.nodeExecuteId
}

func (node *BaseNode) SetParentNodeExecuteId(id string) {
	node.parentNodeExecuteId = id
}

func (node *BaseNode) ParentNodeExecuteId() string {
	return node.parentNodeExecuteId
}

func (node *BaseNode) SetRankScore(v int) {
	node.rankScore = v
}

func (node *BaseNode) RankScore() int {
	return node.rankScore
}

func (node *BaseNode) IncrRpcCount() {
	node.rpcCount++
}

func (node *BaseNode) RpcCount() int {
	return node.rpcCount
}

func (node *BaseNode) SetLevel(v int) {
	node.level = v
}

func (node *BaseNode) Level() int {
	return node.level
}

func (node *BaseNode) StateM() *state.StateManager {
	return node.stateM
}

func (node *BaseNode) State() dispatcherpb.ComponentState {
	return node.stateM.State()
}

func (node *BaseNode) RpcExec(rpcFunc func() (string, proto.Message, error)) (err error) {
	rpcName, resp, err := rpcFunc()
	node.IncrRpcCount()

	if flag.Lookup("test.v") != nil {
		// 测试流程，获取mock数据
		return mock_get_rpc_data(node.Task(), resp, node.RankScore(), rpcName, node.RpcCount())
	}

	if node.Task().Debug() {
		respBuf, bufErr := proto.Marshal(resp)
		if bufErr == nil {
			node.logW.SetRpc(node.RpcCount(), rpcName, base64.StdEncoding.EncodeToString(respBuf))
		}
	}

	return err
}

func (node *BaseNode) UpdateState(state dispatcherpb.ComponentState, code errorx.Code) bool {
	return node.stateM.UpdateStateManager(state, GetErrCode(code))
}

func (node *BaseNode) UpdateStatef(state dispatcherpb.ComponentState, code errorx.Code, msg string, args ...any) {
	ok := node.UpdateState(state, code)
	if ok {
		node.stateM.SetDebugf(msg, args...)
	}
}

func (node *BaseNode) UpdateInvalidStatef(code errorx.Code, msg string, args ...any) error {
	node.task.SetRunAble(false)
	node.UpdateStatef(dispatcherpb.ComponentState_Invalid, code, msg, args...)
	node.task.Errorf(msg, args...)
	return fmt.Errorf(msg, args...)
}

func (node *BaseNode) UpdateWarningStatef(code errorx.Code, msg string, args ...any) {
	node.UpdateStatef(dispatcherpb.ComponentState_Warning, code, msg, args...)
	node.task.Warnf(msg, args...)
}

func (node *BaseNode) UpdateFailureStatef(code errorx.Code, msg string, args ...any) error {
	node.task.SetRunAble(false)
	node.UpdateStatef(dispatcherpb.ComponentState_Failure, code, msg, args...)
	node.task.Errorf(msg, args...)
	return fmt.Errorf(msg, args...)
}

func (node *BaseNode) UpdatePanicStatef(code errorx.Code, msg string, args ...any) error {
	node.task.SetRunAble(false)
	node.UpdateStatef(dispatcherpb.ComponentState_Panic, code, msg, args...)
	node.task.Errorf(msg, args...)
	return fmt.Errorf(msg, args...)
}

func (node *BaseNode) SetReferencedId(id string) {
	if _, ok := node.parent.(*ReferComponentGroupNode); ok {
		node.referencedId = id
	}
}

func (node *BaseNode) ReferencedId() string {
	return node.referencedId
}

func (node *BaseNode) Logger() NodeLogger {
	if node.entry == nil {
		return node.logW
	}

	return node.entry.Logger()
}

func (node *BaseNode) Task() *ApitestTask {
	return node.task
}

func (node *BaseNode) Times() int64 {
	return node.times
}

func (node *BaseNode) Elapsed() int64 {
	return node.EndTime - node.StartTime
}

func (node *BaseNode) StartedAt() int64 {
	return node.StartTime
}

func (node *BaseNode) EndedAt() int64 {
	return node.EndTime
}

func mock_get_rpc_data(task *ApitestTask, resp proto.Message, nodeScore int, rpcName string, rpcScore int) error {
	if task.mockRpc == nil {
		return fmt.Errorf("未注册必填的mock")
	}

	key := fmt.Sprintf("%d:%s:%d", nodeScore, rpcName, rpcScore)
	rpcData, exist := task.mockRpc[key]
	if !exist {
		return fmt.Errorf("未注册必填的mock, key:%s", key)
	}

	return proto.Unmarshal(rpcData, resp)
}
