package apitesttask

type EndNode_Log struct {
	*BaseNode_Log_Common
}

type EndNodeLogWriter struct {
	*BaseNodeLogWriter

	log *EndNode_Log
}

func NewEndNodeLogWriter(bw *BaseNodeLogWriter) *EndNodeLogWriter {
	writer := &EndNodeLogWriter{
		BaseNodeLogWriter: bw,
		log: &EndNode_Log{
			BaseNode_Log_Common: bw.log,
		},
	}
	return writer
}

func (writer *EndNodeLogWriter) toJson() string {
	return log2Json(writer.log)
}
