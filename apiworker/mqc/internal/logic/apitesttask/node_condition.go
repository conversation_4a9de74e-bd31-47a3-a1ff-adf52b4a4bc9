package apitesttask

import (
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/codes"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/state"
	dispatcherpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/rpc/pb"
	managerpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type ConditionNode struct {
	*BaseNode
	Children [2]ChildData // 子节点

	source *managerpb.ConditionComponent
	logW   *ConditionNodeLogWriter
}

func NewConditionNode(task *ApitestTask, data *managerpb.ApiExecutionData, parent Node, isShell bool) (Node, error) {
	node := &ConditionNode{}
	var err error
	node.BaseNode, err = NewBaseNode(task, data, node, parent, isShell)
	if err != nil {
		return nil, node.UpdateInvalidStatef(codes.NodeInvalid, "%s", err)
	}

	// source数据
	source := data.GetCondition()
	node.source = source
	node.logW = NewConditionNodeLogWriter(node.BaseNode.logW)

	// 左分支
	err = node.NewChildren(task, data, 0)
	if err != nil {
		return node, node.UpdateInvalidStatef(codes.NodeInvalid, "%s", err)
	}

	// 右分支
	err = node.NewChildren(task, data, 1)
	if err != nil {
		return node, node.UpdateInvalidStatef(codes.NodeInvalid, "%s", err)
	}

	return node, nil
}

func (node *ConditionNode) NewChildren(task *ApitestTask, data *managerpb.ApiExecutionData, branch int) (err error) {
	children := data.Children[branch].Child
	for _, child := range children {
		childNode, err := GetNode(task, child, node.entry, false)
		if err != nil {
			return err
		}
		node.Children[branch].Child = append(node.Children[branch].Child, childNode)
	}

	return nil
}

func (node *ConditionNode) Skip() bool {
	// 分支节点本质上也是一个End节点（对于case/componentgroup来说），需要执行它的子节点的后置/End
	// 如果已经失败, 则执行右分支（默认分支，不符合条件的分支）的组件
	if !node.task.RunAble() {
		node.logW.SetBranch(false)
		node.runChild(1)
	}

	return !node.task.RunAble()
}

func (node *ConditionNode) run() (err error) {
	leftOk := node.runCondition(node.source, node.logW.GetRootCondition())
	node.logW.SetBranch(leftOk)
	if leftOk {
		node.runChild(0)
	} else {
		node.runChild(1)
	}

	return err
}

func (node *ConditionNode) runChild(branch int) {
	child := node.Children[branch].Child
	for _, childNode := range child {
		nerr := RunNode(childNode)
		if nerr != nil {
			// 条件节点如果子节点异常，自身不会记录异常信息，只会完成异常上报
			_ = node.ParentCombo().UpdateFailureStatef(codes.NodeChildFailure, nerr.Error())
			// 如果子节点异常，则上抛异常
			if childNode.State() == dispatcherpb.ComponentState_Panic {
				_ = node.ParentCombo().UpdatePanicStatef(codes.NodeChildFailure, nerr.Error())
			}
		}
		// 条件节点如果子节点报警，自身并不记录异常信息，只会完成报警上报
		if childNode.State() == dispatcherpb.ComponentState_Warning {
			node.ParentCombo().UpdateWarningStatef(
				childNode.StateM().Code(), "子节点报警:%s", childNode.StateM().Debug(),
			)
		}
	}
}

func (node *ConditionNode) runCondition(
	condition *managerpb.ConditionComponent, logCond *ConditionNode_Log_Condition,
) bool {
	switch condition.Type {
	case managerpb.ConditionType_SINGLE:
		return node.runSingle(condition.GetSingle(), logCond)
	case managerpb.ConditionType_GROUP:
		return node.runGroup(condition.GetGroup(), logCond)
	}
	return false
}

func (node *ConditionNode) runSingle(
	single *managerpb.ConditionComponent_SingleCondition, logCond *ConditionNode_Log_Condition,
) bool {
	var left, right any
	var err error
	errcode := errorx.OK

	defer func() {
		node.logW.SetConditionSingle(
			logCond, single, left, right, errcode, GetErrCode(errcode).String(), state.Error2Debug(err),
		)
	}()

	left, err = node.GetVariable(single.GetLeft())
	if err != nil {
		errcode = codes.NodeConditionLeftValueNotFound
		node.UpdateWarningStatef(codes.NodeConditionLeftValueNotFound, "获取左值失败, err: %s", err)
	}

	right, err = node.GetVariable(single.GetRight())
	if err != nil {
		errcode = codes.NodeConditionRightValueNotFound
		node.UpdateWarningStatef(codes.NodeConditionRightValueNotFound, "获取右值失败, err: %s", err)
	}

	ok := Compare(CompareType(single.GetCompare()), left, right)
	if !ok {
		// 条件错误走右分支，不需要认为组件错误
		errcode = codes.NodeConditionAssertFailure
		node.UpdateState(dispatcherpb.ComponentState_Success, codes.NodeConditionAssertFailure)
	}

	return ok
}

func (node *ConditionNode) runGroup(
	group *managerpb.ConditionComponent_GroupCondition, logCond *ConditionNode_Log_Condition,
) bool {
	node.logW.SetConditionGroup(logCond, group)

	switch group.GetRelationship() {
	case managerpb.Relationship_AND:
		return node.runRelationship_And(group.GetConditions(), &logCond.Group)
	case managerpb.Relationship_OR:
		return node.runRelationship_Or(group.GetConditions(), &logCond.Group)
	}
	return false
}

func (node *ConditionNode) runRelationship_And(
	conditions []*managerpb.ConditionComponent, logCond *ConditionNode_Log_Condition_Group,
) bool {
	isOk := true
	for idx, cond := range conditions {
		// 把所有条件都执行完再返回
		if !node.runCondition(cond, &logCond.Conditions[idx]) {
			isOk = false
		}
	}
	return isOk
}

func (node *ConditionNode) runRelationship_Or(
	conditions []*managerpb.ConditionComponent, logCond *ConditionNode_Log_Condition_Group,
) bool {
	isOk := false
	for idx, cond := range conditions {
		if node.runCondition(cond, &logCond.Conditions[idx]) {
			isOk = true
		}
	}
	return isOk
}

func (node *ConditionNode) Logger() NodeLogger {
	return node.logW
}

func (node *ConditionNode) Content() string {
	return node.logW.toJson()
}
