package apitesttask

import (
	"testing"
)

// 获取线上环境mock数据：
// https://bi.skyengine.com.cn/product/view.htm?module=dashboard&productId=f76dae8c-54b0-47c8-bab9-0051095ee004&menuId=ekkbihi0uto

func TestPoolAccountNode_Simple(t *testing.T) {
	mock_exec_data := `{"exports": [{"key": "account", "value": "account"}, {"key": "password", "value": "password"}], "condition": {"type": 1, "group": {"conditions": [], "relationship": "AND"}, "single": null}, "product_type": 17}`

	task := Mock_ApitestTask()
	task_params := Mock_WorkerReq_ApiCase(
		mock_pool_account_execution_data_from_json(mock_exec_data),
	)

	mock_register_rpc_data(task, 3, []string{"1:rpc.account.prod:********************************************************************************"})

	err := task.Run(task_params)
	if err != nil {
		t.FailNow()
	}
}
