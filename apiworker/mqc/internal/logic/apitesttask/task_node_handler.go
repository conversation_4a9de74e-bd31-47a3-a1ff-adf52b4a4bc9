package apitesttask

import (
	"fmt"

	"github.com/zeromicro/go-zero/core/jsonx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/codes"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/state"
	dispatcherpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/rpc/pb"
	managerpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

var nodeCreateMap map[managerpb.ApiExecutionDataType]CreateHandler

func init() {
	registerNode()
}

type CreateHandler func(*ApitestTask, *managerpb.ApiExecutionData, Node, bool) (Node, error)

func registerNode() {
	nodeCreateMap = map[managerpb.ApiExecutionDataType]CreateHandler{
		// 外层的执行数据类型
		managerpb.ApiExecutionDataType_API_COMPONENT_GROUP: NewApiComponentGroupNode, // API组件组
		managerpb.ApiExecutionDataType_API_CASE:            NewCaseNode,              // API测试用例
		managerpb.ApiExecutionDataType_API_SUITE:           nil,                      // API测试集合
		managerpb.ApiExecutionDataType_API_PLAN:            nil,                      // API测试计划
		managerpb.ApiExecutionDataType_INTERFACE_CASE:      NewInterfaceCaseNode,     // 接口用例

		// 内层的执行数据类型，即组件类型
		// 21 ~ 40: 框的组件（预留）
		managerpb.ApiExecutionDataType_START:           NewStartNode,          // 开始
		managerpb.ApiExecutionDataType_END:             NewEndNode,            // 结束
		managerpb.ApiExecutionDataType_SETUP:           NewSetupNode,          // 前置 - 框
		managerpb.ApiExecutionDataType_TEARDOWN:        NewTeardownNode,       // 后置 - 框
		managerpb.ApiExecutionDataType_BUSINESS_SINGLE: NewBusinessSingleNode, // 业务单请求 - 框
		managerpb.ApiExecutionDataType_BUSINESS_GROUP:  NewBusinessGroupNode,  // 业务行为组 - 框
		managerpb.ApiExecutionDataType_LOOP:            nil,                   // 循环 - 框
		managerpb.ApiExecutionDataType_PARALLEL:        nil,                   // 并行 - 框

		// 41 ~ 60: 点的组件（预留）
		managerpb.ApiExecutionDataType_HTTP:                   NewHttpNode,                 // HTTP请求
		managerpb.ApiExecutionDataType_PRECISION_TESTING_HTTP: NewPrecisionTestingHttpNode, // 精准测试HTTP请求
		managerpb.ApiExecutionDataType_COMPONENT_GROUP:        NewReferComponentGroupNode,  // 引用组件组
		managerpb.ApiExecutionDataType_CONDITION:              NewConditionNode,            // 条件
		managerpb.ApiExecutionDataType_WAIT:                   NewWaitNode,                 // 等待
		managerpb.ApiExecutionDataType_ASSERT:                 NewAssertNode,               // 断言
		managerpb.ApiExecutionDataType_POOL_ACCOUNT:           NewAccountNode,              // 池账号
		managerpb.ApiExecutionDataType_DATA_PROCESSING:        NewDataProcessingNode,       // 数据处理
		managerpb.ApiExecutionDataType_DATA_DRIVEN:            nil,                         // 数据驱动
		managerpb.ApiExecutionDataType_SQL_EXECUTION:          NewSqlExecutionNode,         // SQL执行
	}
}

func GetNode(task *ApitestTask, data *managerpb.ApiExecutionData, parent Node, isShell bool) (node Node, err error) {
	defer func() {
		if node == nil {
			// set default value
			node, _ = NewBaseNode(task, data, nil, nil, isShell)
		}

		if nodePanic := recover(); nodePanic != any(nil) {
			err = node.UpdatePanicStatef(codes.NodePanic, "%v", nodePanic)
		}

		// 记录节点日志
		node.SetError()

		// 上报: 先上报父节点，再上报子节点
		_ = node.InitReport()

		if err != nil {
			task.Errorf(
				"GetNode[%s] : Id:%s, %s, data:%s",
				node.Type(), node.Id(), node.Content(), jsonx.MarshalIgnoreError(data),
			)
			return
		}
		task.Infof("GetNode[%s] : Id:%s, %s", node.Type(), node.Id(), node.Content())
	}()

	creator, exist := nodeCreateMap[data.GetType()]
	if !exist {
		return nil, fmt.Errorf("节点未实现 id: %s, type: %s", data.GetId(), data.GetType())
	}

	node, err = creator(task, data, parent, isShell)
	return node, err
}

func RunNode(node Node) (err error) {
	if node.Task().Ended() {
		return nil
	}

	defer func() {
		if nodePanic := recover(); nodePanic != any(nil) {
			err = node.UpdatePanicStatef(codes.NodePanic, "%v", nodePanic)
		}

		node.SetError()
		// teardown错误只打印日志，不影响节点执行
		_ = node.teardown()

		if err != nil {
			node.Task().Errorf("RunNode[%s] : %s", node.Type(), node.Content())
			return
		}
		node.Task().Infof("RunNode[%s] : %s", node.Type(), node.Content())
	}()

	node.UpdateState(dispatcherpb.ComponentState_Started, errorx.OK)
	err = node.setup()
	if err != nil {
		return err
	}

	if node.Skip() {
		node.UpdateState(dispatcherpb.ComponentState_Skip, codes.NodeSkip)
		return fmt.Errorf("节点被跳过")
	}

	err = node.run()
	if err != nil {
		return err
	}

	node.UpdateState(dispatcherpb.ComponentState_Success, errorx.OK)

	if n, ok := node.(*ConditionNode); ok {
		// 如果是条件节点，异常状态被设置到父节点，因此需要根据父节点的运行状态进行判断
		debug := n.ParentCombo().StateM().Debug()
		switch n.ParentCombo().State() {
		case dispatcherpb.ComponentState_Invalid:
			return fmt.Errorf("条件节点下存在状态为无效的子节点: %s", debug)
		case dispatcherpb.ComponentState_Panic:
			return fmt.Errorf("条件节点下存在状态为异常的子节点: %s", debug)
		case dispatcherpb.ComponentState_Failure:
			return fmt.Errorf("条件节点下存在状态为失败的子节点: %s", debug)
		}
	}

	return nil
}

type Node interface {
	run() error // run方法，每个节点都要实现
	setup() error
	teardown() error
	Skip() bool
	Content() string
	SetError()
	Error() (code errorx.Code, message, debug string)
	InitReport() error

	Id() string
	Type() string
	Name() string
	MaintainedBy() string

	// Task 获取任务对象
	Task() *ApitestTask

	// StateM 获取状态管理器
	StateM() *state.StateManager
	State() dispatcherpb.ComponentState
	UpdateState(state dispatcherpb.ComponentState, code errorx.Code) bool
	UpdateStatef(state dispatcherpb.ComponentState, code errorx.Code, msg string, args ...any)
	UpdateInvalidStatef(code errorx.Code, msg string, args ...any) error
	UpdateWarningStatef(code errorx.Code, msg string, args ...any)
	UpdateFailureStatef(code errorx.Code, msg string, args ...any) error
	UpdatePanicStatef(code errorx.Code, msg string, args ...any) error

	// SetShell 是否是外壳，外壳: 任务执行的最外层组件
	SetShell(is bool)
	Shell() bool

	// SetVersion 设置版本
	SetVersion(string)
	Version() string

	// SetParentId 设置父ID
	SetParentId(string)
	ParentId() string

	// SetParentComboId 设置父组件组ID
	SetParentComboId(string)
	ParentComboId() string

	// SetCombo 设置为框组件
	SetCombo(bool)
	Combo() bool

	// SetParentCombo 设置父组件组
	SetParentCombo(Node)
	ParentCombo() Node

	// SetNodeExecuteId 设置节点执行ID
	SetNodeExecuteId(string)
	NodeExecuteId() string

	// SetParentNodeExecuteId 设置父节点执行ID
	SetParentNodeExecuteId(string)
	ParentNodeExecuteId() string

	// SetReferencedId 当前组件被谁引用? 如果有被引用，则记录引用当前组件的组件id
	SetReferencedId(string)
	ReferencedId() string

	// SetLevel 设置节点运行层级
	SetLevel(int)
	Level() int

	// SetRankScore 设置节点运行次序
	SetRankScore(int)
	RankScore() int

	Times() int64
	Elapsed() int64
	StartedAt() int64
	EndedAt() int64

	// Logger 返回实例节点自身的logWriter
	Logger() NodeLogger
}
