package apitesttask

type BusinessSingleNode_Log struct {
	*BaseComboNode_Log
}

type BusinessSingleNodeLogWriter struct {
	*BaseComboNodeLogWriter

	log *BusinessSingleNode_Log
}

func NewBusinessSingleNodeLogWriter(bw *BaseComboNodeLogWriter) *BusinessSingleNodeLogWriter {
	// 次生writer类，由于使用的不是interface,因此需要引用基础类
	writer := &BusinessSingleNodeLogWriter{
		BaseComboNodeLogWriter: bw,
		log: &BusinessSingleNode_Log{
			BaseComboNode_Log: bw.log,
		},
	}
	return writer
}

func (writer *BusinessSingleNodeLogWriter) toJson() string {
	return log2Json(writer.log)
}
