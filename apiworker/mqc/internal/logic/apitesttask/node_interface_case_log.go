package apitesttask

type InterfaceCaseNode_Log struct {
	*BaseComboNode_Log
}

type InterfaceCaseNodeLogWriter struct {
	*BaseComboNodeLogWriter

	log *InterfaceCaseNode_Log
}

func NewInterfaceCaseNodeLogWriter(bw *BaseComboNodeLogWriter) *InterfaceCaseNodeLogWriter {
	writer := &InterfaceCaseNodeLogWriter{
		BaseComboNodeLogWriter: bw,
		log: &InterfaceCaseNode_Log{
			BaseComboNode_Log: bw.log,
		},
	}
	return writer
}

func (writer *InterfaceCaseNodeLogWriter) toJson() string {
	return log2Json(writer.log)
}
