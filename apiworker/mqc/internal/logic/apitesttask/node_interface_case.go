package apitesttask

import (
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/codes"
	managerpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type InterfaceCaseNode struct {
	*BaseComboNode
	ProjectId string

	source *managerpb.InterfaceCaseComponent
	logW   *InterfaceCaseNodeLogWriter
}

func NewInterfaceCaseNode(task *ApitestTask, data *managerpb.ApiExecutionData, parent Node, isShell bool) (
	Node, error,
) {
	node := &InterfaceCaseNode{}
	var err error
	node.BaseComboNode, err = NewBaseComboNode(task, data, node, parent, isShell)
	if err != nil {
		return nil, node.UpdateInvalidStatef(codes.NodeInvalid, "%s", err)
	}

	// source数据
	source := data.GetInterfaceCase()
	node.source = source
	node.ProjectId = source.GetProjectId()
	node.name = source.GetName()
	node.description = source.GetDescription()
	node.SetVersion(source.GetVersion())
	node.maintainedBy = source.GetMaintainedBy()

	node.logW = NewInterfaceCaseNodeLogWriter(node.BaseComboNode.logW)

	// 初始化子节点
	err = node.NewChildren(task, data)
	if err != nil {
		return node, node.UpdateInvalidStatef(codes.NodeInvalid, "%s", err)
	}

	return node, nil
}

func (node *InterfaceCaseNode) Content() string {
	return node.logW.toJson()
}

func (node *InterfaceCaseNode) Logger() NodeLogger {
	return node.logW
}
