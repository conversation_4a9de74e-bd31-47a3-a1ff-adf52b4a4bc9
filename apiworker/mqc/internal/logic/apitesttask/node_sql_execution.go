package apitesttask

import (
	"context"
	"encoding/json"
	"fmt"
	"reflect"
	"strconv"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/jsonx"
	"github.com/zeromicro/go-zero/core/stores/postgres"
	"github.com/zeromicro/go-zero/core/stores/sqlx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/db"
	adaptermysql "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/db/adapter/mysql"
	adapterpostgres "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/db/adapter/postgres"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/codes"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/state"
	managerpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type SqlExecutionNode struct {
	*BaseNode

	source *managerpb.SqlExecutionComponent
	logW   *SqlExecutionNodeLogWriter
}

func NewSqlExecutionNode(task *ApitestTask, data *managerpb.ApiExecutionData, parent Node, isShell bool) (Node, error) {
	node := &SqlExecutionNode{}
	var err error
	node.BaseNode, err = NewBaseNode(task, data, node, parent, isShell)
	if err != nil {
		return nil, node.UpdateInvalidStatef(codes.NodeInvalid, "%s", err)
	}

	node.source = data.GetSql()
	node.logW = NewSqlExecutionNodeLogWriter(node.BaseNode.logW)

	return node, nil
}

func (node *SqlExecutionNode) run() (err error) {
	for _, sqlc := range node.source.GetSqls() {
		// 所有sql都执行完
		err = node.runSql(sqlc)
	}

	return err
}

func (node *SqlExecutionNode) runSql(sqlc *managerpb.SqlExecutionComponent_Sql) (err error) {
	errcode := errorx.OK
	start := time.Now().UnixMilli()
	var sqlLog SqlExecutionNode_Log_SQL

	defer func() {
		end := time.Now().UnixMilli()
		node.logW.SetSQL_CostMs(&sqlLog, end-start)
		node.logW.SetSQL(sqlLog, errcode, GetErrCode(errcode).String(), state.Error2Debug(err))
	}()

	host, err := node.getHost(sqlc.GetHost(), &sqlLog)
	if err != nil {
		errcode = codes.NodeSQLParamInvalid
		return err
	}

	port, err := node.getPort(sqlc.GetPort(), &sqlLog)
	if err != nil {
		errcode = codes.NodeSQLParamInvalid
		return err
	}

	user, err := node.getUser(sqlc.GetUser(), &sqlLog)
	if err != nil {
		errcode = codes.NodeSQLParamInvalid
		return err
	}

	password, err := node.getPassword(sqlc.GetPassword(), &sqlLog)
	if err != nil {
		errcode = codes.NodeSQLParamInvalid
		return err
	}

	database, err := node.getDatabase(sqlc.GetDatabase(), &sqlLog)
	if err != nil {
		errcode = codes.NodeSQLParamInvalid
		return err
	}

	var sqlConn sqlx.SqlConn
	switch sqlc.GetType() {
	case managerpb.DataSourceType_MYSQL:
		connUrl := adaptermysql.ConnectionURL{
			ConnectionURL: db.ConnectionURL{
				Host:     node.paramToString(host),
				Port:     node.paramToString(port),
				User:     node.paramToString(user),
				Password: node.paramToString(password),
				Database: node.paramToString(database),
			},
		}
		url_str := connUrl.FormatDSN()
		node.logW.SetSQL_Url(&sqlLog, url_str)
		sqlConn = sqlx.NewMysql(url_str)

	case managerpb.DataSourceType_POSTGRESQL:
		connUrl := adapterpostgres.ConnectionURL{
			ConnectionURL: db.ConnectionURL{
				Host:     node.paramToString(host),
				Port:     node.paramToString(port),
				User:     node.paramToString(user),
				Password: node.paramToString(password),
				Database: node.paramToString(database),
			},
		}
		url_str := connUrl.FormatDSN()
		node.logW.SetSQL_Url(&sqlLog, url_str)
		sqlConn = postgres.New(url_str)
	default:
		errcode = codes.NodeSQLParamInvalid
		return node.UpdateFailureStatef(codes.NodeSQLParamInvalid, "无效的数据库类型:%s", sqlc.GetType().String())
	}

	timeout := time.Duration(sqlc.GetTimeout()) * time.Millisecond
	if timeout > 10*time.Second {
		timeout = 10 * time.Second
	}

	ctx, cancel := context.WithTimeout(context.Background(), timeout) // ctx done?
	defer cancel()
	result, err := node.runSqlUseConnCtx(ctx, sqlConn, sqlc.GetSql())
	if err != nil {
		errcode = codes.NodeSQLExecFailure
		return node.UpdateFailureStatef(codes.NodeSQLExecFailure, "%s", err)
	}

	node.task.VarsPool.SetNodeExportVar(node.Id(), sqlc.GetName(), result)
	node.logW.setSQL_Export(&sqlLog, sqlc.GetName(), result)

	return nil
}

func (node *SqlExecutionNode) getHost(
	data *managerpb.SqlExecutionComponent_Value, sqlLog *SqlExecutionNode_Log_SQL,
) (host any, err error) {
	errcode := errorx.OK
	defer func() {
		node.logW.SetSQL_Host(sqlLog, "host", host, data, errcode, GetErrCode(errcode).String(), state.Error2Debug(err))
	}()

	host, err = node.GetVariable(data)
	if err != nil {
		errcode = codes.NodeSQLParamInvalid
		return "", node.UpdateFailureStatef(codes.NodeSQLParamInvalid, "获取host失败, err:%s", err)
	}
	return host, nil
}

func (node *SqlExecutionNode) getPort(
	data *managerpb.SqlExecutionComponent_Value, sqlLog *SqlExecutionNode_Log_SQL,
) (port any, err error) {
	errcode := errorx.OK
	defer func() {
		node.logW.SetSQL_Port(sqlLog, "port", port, data, errcode, GetErrCode(errcode).String(), state.Error2Debug(err))
	}()

	port, err = node.GetVariable(data)
	if err != nil {
		errcode = codes.NodeSQLParamInvalid
		return "", node.UpdateFailureStatef(codes.NodeSQLParamInvalid, "获取port失败, err:%s", err)
	}
	return port, nil
}

func (node *SqlExecutionNode) getUser(
	data *managerpb.SqlExecutionComponent_Value, sqlLog *SqlExecutionNode_Log_SQL,
) (user any, err error) {
	errcode := errorx.OK
	defer func() {
		node.logW.SetSQL_User(sqlLog, "user", user, data, errcode, GetErrCode(errcode).String(), state.Error2Debug(err))
	}()

	user, err = node.GetVariable(data)
	if err != nil {
		errcode = codes.NodeSQLParamInvalid
		return "", node.UpdateFailureStatef(codes.NodeSQLParamInvalid, "获取user失败, err:%s", err)
	}
	return user, nil
}

func (node *SqlExecutionNode) getPassword(
	data *managerpb.SqlExecutionComponent_Value, sqlLog *SqlExecutionNode_Log_SQL,
) (pass any, err error) {
	errcode := errorx.OK
	defer func() {
		node.logW.SetSQL_Password(
			sqlLog, "password", pass, data, errcode, GetErrCode(errcode).String(), state.Error2Debug(err),
		)
	}()

	pass, err = node.GetVariable(data)
	if err != nil {
		errcode = codes.NodeSQLParamInvalid
		return "", node.UpdateFailureStatef(codes.NodeSQLParamInvalid, "获取password失败, err:%s", err)
	}
	return pass, nil
}

func (node *SqlExecutionNode) getDatabase(
	data *managerpb.SqlExecutionComponent_Value, sqlLog *SqlExecutionNode_Log_SQL,
) (db any, err error) {
	errcode := errorx.OK
	defer func() {
		node.logW.SetSQL_Database(
			sqlLog, "database", db, data, errcode, GetErrCode(errcode).String(), state.Error2Debug(err),
		)
	}()

	db, err = node.GetVariable(data)
	if err != nil {
		errcode = codes.NodeSQLParamInvalid
		return "", node.UpdateFailureStatef(codes.NodeSQLParamInvalid, "获取database失败, err:%s", err)
	}
	return db, nil
}

func (node *SqlExecutionNode) runSqlUseConnCtx(
	ctx context.Context, conn sqlx.SqlConn, sql string, args ...any,
) (result any, err error) {
	sql = strings.Trim(sql, " ")
	sqls := strings.SplitN(sql, " ", 2)
	if len(sqls) < 2 {
		return nil, fmt.Errorf("无效的sql:%s", sql)
	}

	switch sqls[0] {
	case "insert", "update", "delete":
		execResult, err := conn.ExecCtx(ctx, sql)
		if err != nil {
			return nil, fmt.Errorf("执行SQL失败, err:%s", err)
		}

		result, err = execResult.RowsAffected()
		if err != nil {
			return nil, fmt.Errorf("获取改变行数失败, err:%s", err)
		}
		return result, nil

	case "select":
		return node.runSqlUseConn_Query(ctx, conn, sql, args...)
	}

	return
}

func (node *SqlExecutionNode) runSqlUseConn_Query(
	ctx context.Context, conn sqlx.SqlConn, sql string, args ...any,
) (result any, err error) {
	rawdb, err := conn.RawDB()
	if err != nil {
		return nil, fmt.Errorf("无法获取DB, err:%s", err)
	}

	rows, err := rawdb.QueryContext(ctx, sql, args...)
	if err != nil {
		return nil, fmt.Errorf("执行SQL失败, err:%s", err)
	}
	defer rows.Close()

	columnTypes, err := rows.ColumnTypes()
	if err != nil {
		return nil, fmt.Errorf("获取行信息失败, err:%s", err)
	}

	columnCount := len(columnTypes)
	rowDatas := make([]map[string]any, 0, 50)

	rowValues := make([]any, columnCount)
	rowValuePtrs := make([]any, columnCount)

	for rows.Next() {
		for i := 0; i < columnCount; i++ {
			rowValuePtrs[i] = &rowValues[i]
		}

		err = rows.Scan(rowValuePtrs...)
		if err != nil {
			return nil, fmt.Errorf("获取行数据失败， err:%s", err)
		}

		rowData := make(map[string]any, columnCount)
		for i := 0; i < columnCount; i++ {
			key := columnTypes[i].Name()
			val, err := node.convertDBValue2GoValue(columnTypes[i].DatabaseTypeName(), rowValues[i])
			if err != nil {
				return nil, fmt.Errorf("数据库类型转换本地类型失败, err:%s", err)
			}

			rowData[key] = val
		}
		rowDatas = append(rowDatas, rowData)
	}

	return rowDatas, nil
}

func (node *SqlExecutionNode) convertDBValue2GoValue(dbtype string, dbvalue any) (
	goval any, err error,
) {
	var dbvalueStr string
	dbvalueByte, ok := dbvalue.([]byte)
	if !ok {
		dbvalueStr = fmt.Sprintf("%v", dbvalue)
		// return nil, fmt.Errorf("DB数据类型不是bytes, 无效的类型：%T", dbvalue)
	} else {
		dbvalueStr = string(dbvalueByte)
	}

	switch dbtype {
	case "VARCHAR", "TEXT", "NVARCHAR":
		return dbvalueStr, nil
	case "DECIMAL", "INT", "BIGINT", "TINYINT":
		iv, err := strconv.ParseInt(dbvalueStr, 10, 64)
		if err != nil {
			return nil, err
		}
		return iv, nil
	case "FLOAT":
		fv, err := strconv.ParseFloat(dbvalueStr, 64)
		if err != nil {
			return nil, err
		}
		return fv, nil
	case "BOOL":
		bv, err := strconv.ParseBool(dbvalueStr)
		if err != nil {
			return nil, err
		}
		return bv, nil
	case "TIMESTAMP":
		return dbvalueStr, nil
	default:
		return nil, fmt.Errorf("无效的数据库类型,不支持类型转换, dbtype:%s", dbtype)
	}
}

func (node *SqlExecutionNode) paramToString(v any) string {
	switch reflect.ValueOf(v).Kind() {
	case reflect.Int, reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64,
		reflect.Uint, reflect.Uint8, reflect.Uint16, reflect.Uint32, reflect.Uint64,
		reflect.Float32, reflect.Float64:
		return fmt.Sprintf("%v", v)
	case reflect.Invalid:
		return ""
	case reflect.Array, reflect.Map, reflect.Slice, reflect.Struct:
		return jsonx.MarshalToStringIgnoreError(v)
	case reflect.String:
		if jv, ok := v.(json.Number); ok {
			return jv.String()
		}

		return v.(string)
	default:
		return ""
	}
}

func (node *SqlExecutionNode) Content() string {
	return node.logW.toJson()
}

func (node *SqlExecutionNode) Logger() NodeLogger {
	return node.logW
}
