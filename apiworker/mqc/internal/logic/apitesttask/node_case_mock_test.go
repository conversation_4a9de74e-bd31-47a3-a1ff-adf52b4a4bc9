package apitesttask

import (
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/utils"
	managerpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

func mock_default_case_execution_data() *managerpb.ApiExecutionData {
	case_id := utils.GenCaseId()
	return &managerpb.ApiExecutionData{
		Id:   case_id,
		Type: managerpb.ApiExecutionDataType_API_CASE,
		Data: &managerpb.ApiExecutionData_Case{
			Case: &managerpb.CaseComponent{
				ProjectId: utils.GenProjectId(),
				CaseId:    case_id,
				Name:      "unit-test",
				Version:   utils.GenVersion(),
			},
		},
	}
}

func mock_case_execution_data_append_child(apiCase, child *managerpb.ApiExecutionData) {
	if apiCase.Children == nil {
		apiCase.Children = make([]*managerpb.ApiExecutionData_ChildData, 1)
		apiCase.Children[0] = &managerpb.ApiExecutionData_ChildData{
			Child: make([]*managerpb.ApiExecutionData, 0, 10),
		}
	}

	apiCase.Children[0].Child = append(apiCase.Children[0].Child, child)
}
