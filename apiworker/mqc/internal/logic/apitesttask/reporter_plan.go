package apitesttask

import (
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/utils"
)

type PlanReporter struct {
	base *BaseReporter
}

func NewPlanReporter() *PlanReporter {
	return &PlanReporter{
		base: NewBaseReporter(),
	}
}

func (r *PlanReporter) Init(node *BaseNode) (err error) {
	// 组件需要初始化一个执行ID
	if !node.Shell() {
		node.SetNodeExecuteId(utils.GenExecuteId())
	}
	return nil
}

func (r *PlanReporter) Setup(node *BaseNode) (err error) {
	return nil
}

func (r *PlanReporter) Teardown(node *BaseNode) (err error) {
	if node.Shell() {
		// 如果是外壳，说明组件已经被dispatcher创建了记录，需要执行更新
		return r.base.RetryModify(node)
	}

	// 创建新的执行记录
	_, err = r.base.RetryCreate(node)
	return err
}
