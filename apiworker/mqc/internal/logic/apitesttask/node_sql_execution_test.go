package apitesttask

import (
	"testing"

	zeroutils "github.com/zeromicro/go-zero/core/utils"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	managerpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

func test_sqlExecution_new_ApiExecutionData() *managerpb.ApiExecutionData {
	execData := &managerpb.ApiExecutionData{
		Id:   utils.GenNanoId("SQL_EXECUTION"),
		Type: managerpb.ApiExecutionDataType_SQL_EXECUTION,
		Data: &managerpb.ApiExecutionData_Sql{
			Sql: &managerpb.SqlExecutionComponent{
				Sqls: []*managerpb.SqlExecutionComponent_Sql{
					{
						Type: managerpb.DataSourceType_MYSQL,
						Host: &managerpb.SqlExecutionComponent_Value{
							Source: managerpb.VariableSource_MANUAL,
							Manual: &managerpb.VariableManual{Value: `"*************"`},
						},
						Port: &managerpb.SqlExecutionComponent_Value{
							Source: managerpb.VariableSource_MANUAL,
							Manual: &managerpb.VariableManual{Value: `3306`},
						},
						User: &managerpb.SqlExecutionComponent_Value{
							Source: managerpb.VariableSource_MANUAL,
							Manual: &managerpb.VariableManual{Value: `"root"`},
						},
						Password: &managerpb.SqlExecutionComponent_Value{
							Source: managerpb.VariableSource_MANUAL,
							Manual: &managerpb.VariableManual{Value: `"Quwan@2020"`},
						},
						Database: &managerpb.SqlExecutionComponent_Value{
							Source: managerpb.VariableSource_MANUAL,
							Manual: &managerpb.VariableManual{Value: `"manager"`},
						},
						Sql:     "select * from project;",
						Name:    "result",
						Timeout: 5000,
					},
				},
			},
		},
	}
	return execData
}

func TestSqlNodeSimple(t *testing.T) {
	task := Mock_ApitestTask()
	task_params := Mock_WorkerReq_ApiCase(test_sqlExecution_new_ApiExecutionData())
	err := task.Run(task_params)
	if err != nil {
		t.FailNow()
	}
}

func TestSqlNodeByMock(t *testing.T) {
	mock_data := `{"sqls": [{"sql": "select id, project_id, created_at from project;", "host": {"export": {"value": " ", "node_id": " "}, "manual": {"value": "\"*************\""}, "source": 0, "function": {"name": "", "type": 0, "parameters": [{"name": "", "export": {"value": "", "node_id": ""}, "manual": {"value": ""}, "source": 1, "environment": {"value": ""}}]}, "environment": {"value": ""}}, "name": "SQL组件", "port": {"export": {"value": " ", "node_id": " "}, "manual": {"value": "3306"}, "source": 0, "function": {"name": "", "type": 0, "parameters": [{"name": "", "export": {"value": "", "node_id": ""}, "manual": {"value": ""}, "source": 1, "environment": {"value": ""}}]}, "environment": {"value": ""}}, "type": 0, "user": {"export": {"value": " ", "node_id": " "}, "manual": {"value": "\"root\""}, "source": 0, "function": {"name": "", "type": 0, "parameters": [{"name": "", "export": {"value": "", "node_id": ""}, "manual": {"value": ""}, "source": 1, "environment": {"value": ""}}]}, "environment": {"value": ""}}, "alias": "result", "timeout": 5000, "database": {"export": {"value": "", "node_id": " "}, "manual": {"value": "\"manager\""}, "source": 0, "function": {"name": "", "type": 0, "parameters": [{"name": "", "export": {"value": "", "node_id": ""}, "manual": {"value": ""}, "source": 1, "environment": {"value": ""}}]}, "environment": {"value": ""}}, "password": {"export": {"value": " ", "node_id": " "}, "manual": {"value": "\"Quwan@2020\""}, "source": 0, "function": {"name": "", "type": 0, "parameters": [{"name": "", "export": {"value": "", "node_id": ""}, "manual": {"value": ""}, "source": 1, "environment": {"value": ""}}]}, "environment": {"value": ""}}}], "exports": [{"key": "result", "value": "SQL组件"}]}`

	task := Mock_ApitestTask()
	task_params := Mock_WorkerReq_ApiCase(mock_sql_execution_data_from_json(mock_data))
	err := task.Run(task_params)
	if err != nil {
		t.FailNow()
	}
}

func mock_sql_execution_data_from_json(component_data string) *managerpb.ApiExecutionData {
	component := &managerpb.SqlExecutionComponent{}
	if err := managerpb.DefaultUnmarshalOptions.Unmarshal(zeroutils.StringToByteSlice(component_data), component); err != nil {
		panic(any("无效的sql组件信息, err:" + err.Error()))
	}

	exec_data := &managerpb.ApiExecutionData{
		Id:   utils.GenNanoId("SQL_EXECUTION_"),
		Type: managerpb.ApiExecutionDataType_SQL_EXECUTION,
		Data: &managerpb.ApiExecutionData_Sql{
			Sql: component,
		},
	}

	return exec_data
}

func TestSqlNode_Pg(t *testing.T) {
	task := Mock_ApitestTask()
	task_params := Mock_WorkerReq_ApiCase(mock_sql_execution_data_pg())
	err := task.Run(task_params)
	if err != nil {
		t.FailNow()
	}
}
