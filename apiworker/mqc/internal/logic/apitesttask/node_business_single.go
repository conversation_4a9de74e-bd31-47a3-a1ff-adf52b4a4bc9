package apitesttask

import (
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/codes"
	managerpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

// BusinessSingleNode 业务单请求组件
type BusinessSingleNode struct {
	*BaseComboNode

	source *managerpb.BusinessSingleComponent
	logW   *BusinessSingleNodeLogWriter
}

func NewBusinessSingleNode(task *ApitestTask, data *managerpb.ApiExecutionData, parent Node, isShell bool) (Node, error) {
	node := &BusinessSingleNode{}
	var err error
	node.BaseComboNode, err = NewBaseComboNode(task, data, node, parent, isShell)
	if err != nil {
		return nil, node.UpdateInvalidStatef(codes.NodeInvalid, "%s", err)
	}

	source := data.GetBusinessSingle()
	node.source = source
	node.SetImports(node.source.GetImports())
	node.SetExports(node.source.GetExports())

	node.logW = NewBusinessSingleNodeLogWriter(node.BaseComboNode.logW)

	err = node.NewChildren(task, data)
	if err != nil {
		return node, node.UpdateInvalidStatef(codes.NodeInvalid, "%s", err)
	}

	return node, nil
}

func (node *BusinessSingleNode) Content() string {
	return node.logW.toJson()
}

func (node *BusinessSingleNode) Logger() NodeLogger {
	return node.logW
}
