package apitesttask

import (
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/codes"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/state"
	managerpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type AssertNode struct {
	*BaseNode

	source *managerpb.AssertComponent
	logW   *AssertNodeLogWriter
}

func NewAssertNode(task *ApitestTask, data *managerpb.ApiExecutionData, parent Node, isShell bool) (Node, error) {
	node := &AssertNode{}
	var err error
	node.BaseNode, err = NewBaseNode(task, data, node, parent, isShell)
	if err != nil {
		return nil, node.UpdateInvalidStatef(codes.NodeInvalid, "%s", err)
	}

	node.source = data.GetAssert()
	node.logW = NewAssertNodeLogWriter(node.BaseNode.logW)
	return node, nil
}

func (node *AssertNode) run() (err error) {
	err = node.runAssertions()
	return err
}

func (node *AssertNode) runAssertions() error {
	var err error
	for _, assert := range node.source.GetAssertions() {
		// 运行完所有断言后再返回
		nerr := node.runAssertion(assert)
		if nerr != nil {
			err = nerr
		}
	}
	return err
}

func (node *AssertNode) runAssertion(assert *managerpb.AssertComponent_Assertion) (err error) {
	var actual, expected any
	var ok bool
	code := errorx.OK

	defer func() {
		node.logW.SetAssertion(assert, actual, expected, code, GetErrCode(code).String(), state.Error2Debug(err))
	}()

	actual, err = node.GetVariable_Export(
		&managerpb.VariableExport{
			NodeId: assert.GetActual().GetNodeId(),
			Value:  assert.GetActual().GetValue(),
		},
	)
	if err != nil {
		code = codes.NodeAssertFailureInvalidActual
		return node.UpdateFailureStatef(
			codes.NodeAssertFailureInvalidActual, "找不到真实值, nodeid:%s, key:%s", assert.GetActual().GetNodeId(),
			assert.GetActual().GetValue(),
		)
	}

	expected, err = node.GetVariable(assert.GetExpected())
	if err != nil {
		code = codes.NodeAssertFailureInvalidExpected
		node.UpdateWarningStatef(codes.NodeAssertFailureInvalidExpected, "err: %s", err)
	}

	ok = Compare(CompareType(assert.GetCompare()), actual, expected)
	if !ok {
		code = codes.NodeAssertFailureCompareFailure
		return node.UpdateFailureStatef(
			codes.NodeAssertFailureCompareFailure, "compare: %s, actual: %#v, expected: %#v", assert.GetCompare(),
			actual, expected,
		)
	}

	return nil
}

func (node *AssertNode) Content() string {
	return node.logW.toJson()
}

func (node *AssertNode) Logger() NodeLogger {
	return node.logW
}
