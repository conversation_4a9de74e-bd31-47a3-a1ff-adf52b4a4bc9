package apitesttask

import (
	"github.com/zeromicro/go-zero/core/jsonx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"

	managerpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type DataProcessingNode_Log struct {
	*BaseNode_Log_Common
	Processes []DataProcessingNode_Log_Process `json:"process"`
}

type DataProcessingNode_Log_Process struct {
	BaseNode_Log_Common_Error
	FunctionName string
	FunctionType string
	Parameters   []BaseNode_Log_Variable
	Name         string `json:"name"`   // 出参名
	Result       string `json:"result"` // json.encode(函数执行结果值)
}

func getDataProcessingNodeLogProcess(
	result any, process *managerpb.DataProcessingComponent_Process, errcode errorx.Code, errmsg, debug string,
) DataProcessingNode_Log_Process {
	p := DataProcessingNode_Log_Process{
		BaseNode_Log_Common_Error: getBaseNodeLogCommonError(errcode, errmsg, debug),
		FunctionName:              process.GetFunction().GetName(),
		FunctionType:              process.GetFunction().GetType().String(),
		Name:                      process.GetName(),
		Result:                    jsonx.MarshalToStringIgnoreError(result),
		Parameters:                make([]BaseNode_Log_Variable, 0, 10),
	}

	for _, param := range process.GetFunction().GetParameters() {
		p.Parameters = append(p.Parameters, getBaseNodeLogVariable(param.GetName(), param))
	}

	return p
}

type DataProcessingNodeLogWriter struct {
	*BaseNodeLogWriter

	log *DataProcessingNode_Log
}

func NewDataProcessingNodeLogWriter(bw *BaseNodeLogWriter) *DataProcessingNodeLogWriter {
	writer := &DataProcessingNodeLogWriter{
		BaseNodeLogWriter: bw,
		log: &DataProcessingNode_Log{
			BaseNode_Log_Common: bw.log,
			Processes:           make([]DataProcessingNode_Log_Process, 0, 10),
		},
	}
	return writer
}

func (writer *DataProcessingNodeLogWriter) toJson() string {
	return log2Json(writer.log)
}

func (writer *DataProcessingNodeLogWriter) SetProcess(
	val any, process *managerpb.DataProcessingComponent_Process, errcode errorx.Code, errmsg, debug string,
) {
	writer.log.Processes = append(
		writer.log.Processes, getDataProcessingNodeLogProcess(val, process, errcode, errmsg, debug),
	)
}
