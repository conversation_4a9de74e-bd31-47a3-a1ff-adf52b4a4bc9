package apitesttask

import (
	"fmt"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/codes"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/state"
	dispatcherpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/rpc/pb"
	managerpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

// BaseComboNode 基础组件 - 框
type BaseComboNode struct {
	*BaseNode
	Children [1]ChildData

	logW *BaseComboNodeLogWriter

	Imports []*managerpb.Import
	Exports []*managerpb.Export
}

type ChildData struct {
	Child []Node
}

func NewBaseComboNode(
	task *ApitestTask, data *managerpb.ApiExecutionData, entry, parent Node, isShell bool,
) (*BaseComboNode, error) {
	node := &BaseComboNode{}
	var err error
	node.BaseNode, err = NewBaseNode(task, data, entry, parent, isShell)
	if err != nil {
		return nil, node.UpdateInvalidStatef(codes.NodeInvalid, "%s", err)
	}

	node.SetCombo(true)
	node.logW = NewBaseComboNodeLogWriter(node.BaseNode.logW)

	return node, nil
}

// NewChildren 初始化完成父节点后，才创建子节点
func (node *BaseComboNode) NewChildren(task *ApitestTask, data *managerpb.ApiExecutionData) (err error) {
	if len(data.Children) == 0 {
		return fmt.Errorf("子节点不存在")
	}

	// 处理子节点
	children := data.Children[0].Child
	for _, child := range children {
		childNode, err := GetNode(task, child, node.entry, false)
		if err != nil {
			return err
		}

		node.Children[0].Child = append(node.Children[0].Child, childNode)
	}
	return nil
}

func (node *BaseComboNode) SetImports(imports []*managerpb.Import) {
	if node.Shell() {
		return
	}

	node.Imports = imports
}

func (node *BaseComboNode) SetExports(exports []*managerpb.Export) {
	if node.Shell() {
		return
	}

	node.Exports = exports
}

func (node *BaseComboNode) run() (err error) {
	// 配置入参
	node.processingImports()

	err = node.runChild()

	// 配置出参
	node.processingExports()
	return err
}

func (node *BaseComboNode) runChild() error {
	var err error

	child := node.Children[0].Child
	for _, childNode := range child {
		nerr := RunNode(childNode)
		if nerr != nil {
			err = node.UpdateFailureStatef(codes.NodeChildFailure, "%s", nerr.Error())
			// 如果子节点异常，则上抛异常
			if childNode.State() == dispatcherpb.ComponentState_Panic {
				err = node.UpdatePanicStatef(codes.NodeChildFailure, "%s", nerr.Error())
			}
		}
		node.logW.SetChild(childNode)

		// 子节点报警上报
		if childNode.State() == dispatcherpb.ComponentState_Warning {
			node.UpdateWarningStatef(childNode.StateM().Code(), "子节点报警:%s", childNode.StateM().Debug())
		}
	}

	return err
}

func (node *BaseComboNode) processingImports() {
	// 配置入参
	for _, im := range node.Imports {
		node.processingImport(im)
	}
}

func (node *BaseComboNode) processingImport(im *managerpb.Import) {
	key := im.GetName()
	var value any
	errcode := errorx.OK
	var err error
	logIm := &managerpb.Import{}
	_ = utils.Copy(logIm, im)

	if key == "" {
		// 忽略key为空的入参
		return
	}

	defer func() {
		node.logW.SetImport(key, value, logIm, errcode, GetErrCode(errcode).String(), state.Error2Debug(err))
	}()

	// 配置入参
	value, err = node.GetVariable(im)
	// 如果这是个被引用组件，则入参来源是引用组件id的入参。（仅对 单组件/组件组 有效, v0.2.0）
	if node.ReferencedId() != "" {
		var refValue any
		refValue, ok := node.task.VarsPool.GetNodeImportVar(node.ReferencedId(), key)
		if ok {
			value = refValue
			err = nil
		}
		logIm.Export.NodeId = node.ReferencedId() // 更新执行日志结构
	}

	if err != nil {
		errcode = codes.NodeImportNotFound
		node.UpdateWarningStatef(codes.NodeImportNotFound, "key:%s, err:%s", key, err)
	}

	node.task.VarsPool.SetNodeImportVar(node.Id(), key, value)
}

func (node *BaseComboNode) processingExports() {
	for _, exp := range node.Exports {
		node.processingExport(exp)
	}
}

func (node *BaseComboNode) processingExport(exp *managerpb.Export) {
	key := exp.GetName()
	var value any
	errcode := errorx.OK
	var err error
	logExp := &managerpb.VariableExport{}
	_ = utils.Copy(logExp, exp.GetExport())

	if key == "" {
		// 忽略key为空的出参
		return
	}

	defer func() {
		node.logW.SetExport(key, value, logExp, errcode, GetErrCode(errcode).String(), state.Error2Debug(err))
	}()

	value, ok := node.task.VarsPool.GetNodeExportVar(exp.GetExport().GetNodeId(), exp.GetExport().GetValue())
	if !ok {
		errcode = codes.NodeExportNotFound
		node.UpdateWarningStatef(
			codes.NodeExportNotFound, "nodeid: %s, key: %s", exp.GetExport().GetNodeId(), exp.GetExport().GetValue(),
		)
	}

	node.task.VarsPool.SetNodeExportVar(node.Id(), key, value)
}

func (node *BaseComboNode) Content() string {
	return node.logW.toJson()
}

func (node *BaseComboNode) Logger() NodeLogger {
	return node.logW
}
