package mock

import (
	"context"

	"github.com/zeromicro/go-zero/core/logx"
	"go.uber.org/zap"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/log"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/apiworker/common/zrpc"
)

func Mock_ReporterRpc() *zrpc.ReporterRpc {
	return &zrpc.ReporterRpc{
		Name: "rpc.reporter.prod",
	}
}

func Mock_ManagerRpc() *zrpc.ManagerRpc {
	return &zrpc.ManagerRpc{
		Name: "rpc.manager.prod",
	}
}

func Mock_AccountRpc() *zrpc.AccountRpc {
	return &zrpc.AccountRpc{
		Name: "rpc.account.prod",
	}
}

func Mock_Log() {
	logconf := logx.LogConf{
		ServiceName: "mqc.apiworker",
		Encoding:    "plain",
		Mode:        "console",
	}
	w := log.NewZapWriter(logconf, zap.AddCaller(), zap.Development())
	log.SetWriter(w)
}

func Mock_Context() context.Context {
	return context.Background()
}
