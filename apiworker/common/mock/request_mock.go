package mock

import (
	commonpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/utils"
)

func Mock_GeneralConfig(project_id string) *commonpb.GeneralConfig {
	return &commonpb.GeneralConfig{
		ProjectId: project_id,
		ConfigId:  utils.GenGeneralConfigId(),
		Name:      "unit-test-general-config",
	}
}

func Mock_AccountConfig(project_id string) []*commonpb.AccountConfig {
	account := make([]*commonpb.AccountConfig, 1)
	account[0] = &commonpb.AccountConfig{
		ProjectId:    project_id,
		ConfigId:     utils.GenAccountConfigId(),
		Name:         "unit-test-account-config",
		ProductType:  17,
		ProductName:  "unit-test-product",
		PoolEnvTable: "pool-env-table",
		PoolEnvName:  "pool-env-name",
	}
	return account
}
