package zrpc

import (
	"context"
	"flag"

	"github.com/zeromicro/go-zero/zrpc"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/interceptors/clientinterceptors"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/relation/rpc/client/relationservice"
	relationpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/relation/rpc/pb"
)

type RelationRpc struct {
	conf zrpc.RpcClientConf

	Name string
	Cli  relationservice.RelationService
}

func NewRelationRpc(conf zrpc.RpcClientConf) *RelationRpc {
	cli := &RelationRpc{
		conf: conf,
		Name: conf.Etcd.Key,
		Cli: relationservice.NewRelationService(
			zrpc.MustNewClient(
				conf, clientinterceptors.UnaryUserInfoClientOption(),
			),
		),
	}

	return cli
}

func (cli *RelationRpc) BindTestCaseToService(
	ctx context.Context, req *relationpb.BindTestCaseToServiceV2Request,
) (*relationpb.BindTestCaseToServiceV2Response, error) {
	if flag.Lookup("test.v") != nil {
		return mockDefaultRelationRpcBindTestCaseToService()
	}

	return cli.Cli.BindTestCaseToServiceV2(ctx, req)
}

func mockDefaultRelationRpcBindTestCaseToService() (*relationpb.BindTestCaseToServiceV2Response, error) {
	resp := &relationservice.BindTestCaseToServiceV2Response{}

	return resp, nil
}
