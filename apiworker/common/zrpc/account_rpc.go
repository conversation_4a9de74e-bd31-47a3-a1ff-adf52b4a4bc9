package zrpc

import (
	"context"
	"flag"

	"github.com/zeromicro/go-zero/zrpc"

	account "gitlab.ttyuyin.com/TestDevelopment/probe-backend/account/rpc/client/account"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/interceptors/clientinterceptors"
)

type AccountRpc struct {
	conf zrpc.RpcClientConf

	Name string
	Cli  account.Account
}

func NewAccountRpc(conf zrpc.RpcClientConf) *AccountRpc {
	cli := &AccountRpc{
		conf: conf,
		Name: conf.Etcd.Key,
		Cli:  account.NewAccount(zrpc.MustNewClient(conf, clientinterceptors.UnaryUserInfoClientOption())),
	}

	return cli
}

func (cli *AccountRpc) QueryEnv(ctx context.Context, req *account.QueryAccountPoolEnvDataRequest) (*account.QueryAccountPoolEnvDataResponse, error) {
	if flag.Lookup("test.v") != nil {
		return &account.QueryAccountPoolEnvDataResponse{}, nil
	}

	return cli.Cli.QueryAccountPoolEnvData(ctx, req)
}

func (cli *AccountRpc) ReleaseTestAccount(ctx context.Context, req *account.ReleaseTestAccountRequest) (*account.ReleaseTestAccountResponse, error) {
	if flag.Lookup("test.v") != nil {
		return &account.ReleaseTestAccountResponse{}, nil
	}

	return cli.Cli.ReleaseTestAccount(ctx, req)
}
