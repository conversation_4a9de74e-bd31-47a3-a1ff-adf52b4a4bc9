package zrpc

import (
	"context"
	"flag"

	"github.com/zeromicro/go-zero/zrpc"
	"google.golang.org/grpc"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/interceptors/clientinterceptors"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/client/dataprocessingfunctionservice"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/client/projectservice"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type ManagerRpc struct {
	conf zrpc.RpcClientConf

	Name     string
	project  projectservice.ProjectService
	function dataprocessingfunctionservice.DataProcessingFunctionService
}

func NewManagerRpc(conf zrpc.RpcClientConf) *ManagerRpc {
	return &ManagerRpc{
		conf: conf,
		Name: conf.Etcd.Key,
		project: projectservice.NewProjectService(
			zrpc.MustNewClient(
				conf, clientinterceptors.UnaryUserInfoClientOption(),
			),
		),
		function: dataprocessingfunctionservice.NewDataProcessingFunctionService(
			zrpc.MustNewClient(
				conf, clientinterceptors.UnaryUserInfoClientOption(),
			),
		),
	}
}

func (cli *ManagerRpc) ViewProject(ctx context.Context, in *pb.ViewProjectReq, opts ...grpc.CallOption) (
	*pb.ViewProjectResp, error,
) {
	return cli.project.ViewProject(ctx, in, opts...)
}

func (cli *ManagerRpc) ViewDataProcessingFunction(
	ctx context.Context, in *pb.ViewDataProcessingFunctionReq, opts ...grpc.CallOption,
) (*pb.ViewDataProcessingFunctionResp, error) {
	if flag.Lookup("test.v") != nil {
		return &pb.ViewDataProcessingFunctionResp{
			Function: &pb.DataProcessingFunction{
				ProjectId:   "test",
				Name:        "add",
				Type:        pb.FunctionType_CUSTOM,
				Category:    "Other",
				Description: "",
				Language:    pb.CodeLanguage_PYTHON,
				Content:     "def add(a, b):\n\treturn a + b\n",
				Parameters: []*pb.Parameter{
					{
						Name: "v1",
						Type: pb.ParameterOrReturnType_NUMBER,
					},
					{
						Name: "v2",
						Type: pb.ParameterOrReturnType_NUMBER,
					},
				},
				Returns: []*pb.Return{
					{
						Name: "v3",
						Type: pb.ParameterOrReturnType_NUMBER,
					},
				},
				Example: "",
				Version: "v1.0.0",
			},
		}, nil
	}

	return cli.function.ViewDataProcessingFunction(ctx, in, opts...)
}
