package zrpc

import (
	"context"
	"flag"

	"github.com/zeromicro/go-zero/zrpc"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/interceptors/clientinterceptors"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/utils"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/client/reporter"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
)

type ReporterRpc struct {
	conf zrpc.RpcClientConf

	Name string
	Cli  reporter.Reporter
}

func NewReporterRpc(conf zrpc.RpcClientConf) *ReporterRpc {
	cli := &ReporterRpc{
		conf: conf,
		Name: conf.Etcd.Key,
		Cli:  reporter.NewReporter(zrpc.MustNewClient(conf, clientinterceptors.UnaryUserInfoClientOption())),
	}

	return cli
}

func (cli *ReporterRpc) Create(ctx context.Context, req *pb.PutRecordRequest) (*pb.CreateRecordResponse, error) {
	if flag.Lookup("test.v") != nil {
		return mock_default_reporterRpc_Create(req)
	}

	return cli.Cli.CreateRecord(ctx, req)
}

func (cli *ReporterRpc) Modify(ctx context.Context, req *pb.PutRecordRequest) (*pb.ModifyRecordResponse, error) {
	if flag.Lookup("test.v") != nil {
		return mock_default_reporterRpc_Modify(req)
	}

	return cli.Cli.ModifyRecord(ctx, req)
}

func mock_default_reporterRpc_Create(req *pb.PutRecordRequest) (*pb.CreateRecordResponse, error) {
	resp := &pb.CreateRecordResponse{}
	resp.ComponentExecuteId = req.GetComponentExecuteId()
	if resp.GetComponentExecuteId() == "" {
		resp.ComponentExecuteId = utils.GenExecuteId()
	}

	return resp, nil
}

func mock_default_reporterRpc_Modify(req *pb.PutRecordRequest) (*pb.ModifyRecordResponse, error) {
	return &pb.ModifyRecordResponse{}, nil
}
