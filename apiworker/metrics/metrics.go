package metrics

import (
	"strconv"

	qetmetrics "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/metrics"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/apiworker/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/metrics"
)

var (
	caseRequestCounter   qetmetrics.MetricCounterHandler
	caseRequestHistogram qetmetrics.MetricHistogramHandler
)

type CaseResult struct {
	TaskID string

	ProjectID   string
	ProjectName string

	PlanExecuteID string
	PlanID        string
	PlanName      string

	SuiteExecuteID string
	SuiteID        string
	SuiteName      string

	CaseExecuteID string
	CaseID        string
	CaseName      string
	MaintainedBy  string
	Result        common.Result
	Elapsed       int64

	StartedAt, EndedAt int64
}

func init() {
	counterVecOpts := qetmetrics.MetricCounterVecOpts
	counterVecOpts.Name = metrics.ConstMetricsApiWorkerCaseRequestTotal
	counterVecOpts.Help = "the request result count of api cases in the api plan"
	counterVecOpts.Labels = []string{
		string(metrics.ConstMetricsLabelNameOfProjectID),    // project_id
		string(metrics.ConstMetricsLabelNameOfProjectName),  // project_name
		string(metrics.ConstMetricsLabelNameOfPlanID),       // plan_id
		string(metrics.ConstMetricsLabelNameOfPlanName),     // plan_name
		string(metrics.ConstMetricsLabelNameOfSuiteID),      // suite_id
		string(metrics.ConstMetricsLabelNameOfSuiteName),    // suite_name
		string(metrics.ConstMetricsLabelNameOfCaseID),       // case_id
		string(metrics.ConstMetricsLabelNameOfCaseName),     // case_name
		string(metrics.ConstMetricsLabelNameOfMaintainedBy), // maintained_by
		string(metrics.ConstMetricsLabelNameOfResult),       // result
	}
	caseRequestCounter = qetmetrics.NewMetricCounter(&counterVecOpts)

	histogramOpts := qetmetrics.MetricHistogramVecOpts
	histogramOpts.Name = metrics.ConstMetricsApiWorkerCaseRequestDuration
	histogramOpts.Help = "the request duration(ms) of api cases in the api plan"
	histogramOpts.Labels = []string{
		string(metrics.ConstMetricsLabelNameOfProjectID),    // project_id
		string(metrics.ConstMetricsLabelNameOfProjectName),  // project_name
		string(metrics.ConstMetricsLabelNameOfPlanID),       // plan_id
		string(metrics.ConstMetricsLabelNameOfPlanName),     // plan_name
		string(metrics.ConstMetricsLabelNameOfSuiteID),      // suite_id
		string(metrics.ConstMetricsLabelNameOfSuiteName),    // suite_name
		string(metrics.ConstMetricsLabelNameOfCaseID),       // case_id
		string(metrics.ConstMetricsLabelNameOfCaseName),     // case_name
		string(metrics.ConstMetricsLabelNameOfMaintainedBy), // maintained_by
		string(metrics.ConstMetricsLabelNameOfResult),       // result
	}
	histogramOpts.Buckets = []float64{500, 1000, 2500, 5000, 10000, 25000, 50000, 100000, 250000}
	caseRequestHistogram = qetmetrics.NewMetricHistogram(&histogramOpts)
}

func SaveResult(v CaseResult) {
	result := strconv.FormatInt(int64(v.Result), 10)
	//startedAt := strconv.FormatInt(v.StartedAt, 10)
	//endedAt := strconv.FormatInt(v.EndedAt, 10)

	caseRequestCounter.Inc(
		v.ProjectID, v.ProjectName,
		v.PlanID, v.PlanName,
		v.SuiteID, v.SuiteName,
		v.CaseID, v.CaseName, v.MaintainedBy,
		result,
	)
	caseRequestHistogram.Observe(
		v.Elapsed,
		v.ProjectID, v.ProjectName,
		v.PlanID, v.PlanName,
		v.SuiteID, v.SuiteName,
		v.CaseID, v.CaseName, v.MaintainedBy,
		result,
	)
}
