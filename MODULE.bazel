# Copyright 2023 Buf Technologies, Inc.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

module(
    name = "protovalidate",
    version = "0.0.0",
    repo_name = "com_github_bufbuild_protovalidate",
)

bazel_dep(name = "rules_go", version = "0.51.0", repo_name = "io_bazel_rules_go")
bazel_dep(name = "gazelle", version = "0.40.0", repo_name = "bazel_gazelle")
bazel_dep(name = "rules_buf", version = "0.3.0")
bazel_dep(name = "protobuf", version = "29.2", repo_name = "com_google_protobuf")
bazel_dep(name = "rules_proto", version = "7.1.0")
bazel_dep(name = "googleapis", version = "0.0.0-20240819-fe8ba054a")

go_sdk = use_extension("@io_bazel_rules_go//go:extensions.bzl", "go_sdk")
go_sdk.download(version = "1.20.4")

go_deps = use_extension("@bazel_gazelle//:extensions.bzl", "go_deps")
go_deps.from_file(go_mod = "//tools:go.mod")
use_repo(go_deps, "com_github_bufbuild_protocompile", "com_github_spf13_pflag", "com_github_stretchr_testify", "in_gopkg_yaml_v3", "org_golang_google_protobuf", "org_golang_x_sync")

buf = use_extension("@rules_buf//buf:extensions.bzl", "buf")
buf.toolchains(
    version = "v1.50.0",
)
