linters-settings:
  errcheck:
    check-type-assertions: true
  forbidigo:
    forbid:
      - '^fmt\.Print'
      - '^log\.'
      - '^print$'
      - '^println$'
      - '^panic$'
  godox:
    # TODO, OPT, etc. comments are fine to commit. Use FIXME comments for
    # temporary hacks, and use godox to prevent committing them.
    keywords: [FIXME]
  varnamelen:
    ignore-decls:
      - T any
      - i int
      - wg sync.WaitGroup
      - ok bool
      - w io.Writer
linters:
  enable-all: true
  disable:
    - cyclop            # covered by gocyclo
    - depguard          # we can manage dependencies strictly if the need arises in the future
    - err113            # internal error causes may be dynamic
    - execinquery       # deprecated
    - exhaustruct       # don't _always_ need to exhaustively create struct
    - exportloopref     # deprecated
    - funlen            # rely on code review to limit function length
    - gocognit          # dubious "cognitive overhead" quantification
    - gofumpt           # prefer standard gofmt
    - goimports         # rely on gci instead
    - gomnd             # some unnamed constants are okay
    - gomoddirectives   # we use go modules replacements intentionally
    - gomodguard        # not compatible with go workspaces
    - ireturn           # "accept interfaces, return structs" isn't ironclad
    - lll               # don't want hard limits for line length
    - maintidx          # covered by gocyclo
    - musttag           # incompatible with go workspaces
    - nlreturn          # generous whitespace violates house style
    - nonamedreturns    # usage of named returns should be selective
    - testpackage       # internal tests are fine
    - wrapcheck         # don't _always_ need to wrap errors
    - wsl               # over-generous whitespace violates house style
issues:
  exclude-use-default: true
  exclude-rules:
    # Loosen requirements on test harness
    - path: go/v2/internal/cmd/
      linters:
        - forbidigo
        - errorlint
    # Loosen requirements on tools
    - path: tools/
      linters:
        - gosec
        - forbidigo
    # gci is confused by "slices" and suggests to move it to the end of the import list
    - path: tools/protovalidate-conformance/internal/results/result.go
      linters:
        - gci
    # Building the conformance test suites
    - path: tools/protovalidate-conformance/internal/cases/
      linters:
        - funlen
        - maintidx
        - mnd
        - dupl
        - gosmopolitan
    # Loosen requirements on tests
    - path: _test.go
      linters:
        - funlen
        - varnamelen
        - unparam
        - gosec
    - path: go/v2/internal/celext/lib.go
      linters:
        # setting up custom functions/overloads appears duplicative (false positive)
        - dupl
        # Types are checked internally within CEL. There are bigger issues if its
        # type analysis is wrong
        - forcetypeassert
    # static unexported lookup tables
    - path: go/v2/internal/constraints/lookups.go
      linters:
        - gochecknoglobals
