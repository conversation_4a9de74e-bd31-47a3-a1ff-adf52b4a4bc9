project=$(basename $(dirname $(realpath $0)))
base_path=$(dirname $(dirname $(realpath $0)))
echo "Project: ${project}\nBase Path: ${base_path}\n"

buildtime_image="cr.ttyuyin.com/probe/golang:1.23.3"
runtime_image="cr.ttyuyin.com/probe/debian:bookworm-20240904-slim"

image_tag=""

function handle_tag() {
  if [[ ! "$1" =~ ^[0-9] ]]; then
    image_tag="$1"
  else
    image_tag="v$1"
  fi
}

function build() {
  local tag="${project}:${image_tag}"
  local binary="${project}.linux"
  local dockerfile="${project}/Dockerfile"
  local source_tag="${project}:${image_tag}"
  local target_tag="cr.ttyuyin.com/probe/${project}:${image_tag}"

  cd "${base_path}"
  docker build -t ${tag} --build-arg BUILDTIME_IMAGE=${buildtime_image} --build-arg RUNTIME_IMAGE=${runtime_image} --build-arg SERVICE=${project} --build-arg BINARY=${binary} -f ${dockerfile} .
  docker tag ${source_tag} ${target_tag}
  docker images --filter "before=${source_tag}" --filter "reference=perftool:v*" --filter "reference=*/*/perftool:v*" --format "{{ .Repository }}:{{ .Tag }}" | xargs -I {} docker rmi {}
  cd -
}

function push() {
  local image="cr.ttyuyin.com/probe/${project}:${image_tag}"
  docker push ${image}
}

function help() {
  echo "Usage: $(realpath $0) {build | push} {tag}"
  exit -1
}

function run() {
  if [ -z "$2" ]; then
    echo "image tag cannot be empty"
    help
  else
    handle_tag "$2"
    echo "image tag: ${image_tag}"
  fi

  case "$1" in
    build)
      build
    ;;
    push)
      push
    ;;
    release)
      build
      push
    ;;
    *)
      echo "Unknown Command: $1"
      help
    ;;
  esac

  exit 0
}

run "$1" "$2"
