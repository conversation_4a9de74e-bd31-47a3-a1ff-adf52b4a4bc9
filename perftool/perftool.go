package main

import (
	"github.com/spf13/cobra"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/version"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/perftool/cmd"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/perftool/mqc/server"
)

var configFile = new(string)

func main() {
	root := cmd.NewRootCommand()
	root.Args = cobra.NoArgs
	root.RunE = func(cmd *cobra.Command, args []string) error {
		s, err := server.NewServer(*configFile)
		if err != nil {
			return err
		}
		defer s.Shutdown()

		s.Serve()

		return nil
	}

	vi := version.NewVersionInfo()
	root.Version = vi.String()

	flags := root.Flags()
	flags.SortFlags = false
	flags.StringVarP(configFile, "mqc-config", "f", "mqc/etc/perftool.yaml", "the config file of mqc service")

	cobra.CheckErr(root.Execute())
}
