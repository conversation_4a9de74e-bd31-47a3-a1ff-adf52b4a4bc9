package reporter

import (
	"context"

	"github.com/zeromicro/go-zero/zrpc"
	"google.golang.org/grpc"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/interceptors/clientinterceptors"

	client "gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/client/perfreporter"
)

type PerfReporterRPC struct {
	conf zrpc.RpcClientConf

	client client.PerfReporter
}

func NewPerfReporterRPC(c zrpc.RpcClientConf) *PerfReporterRPC {
	return &PerfReporterRPC{
		conf:   c,
		client: client.NewPerfReporter(zrpc.MustNewClient(c, clientinterceptors.UnaryUserInfoClientOption())),
	}
}

func (c *PerfReporterRPC) UpdateMonitorURLOfPerfPlanRecord(
	ctx context.Context, in *client.UpdateMonitorURLOfPerfPlanRecordReq, opts ...grpc.CallOption,
) (*client.UpdateMonitorURLOfPerfPlanRecordResp, error) {
	return c.client.UpdateMonitorURLOfPerfPlanRecord(ctx, in, opts...)
}
