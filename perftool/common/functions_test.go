package common

import (
	"testing"
	"time"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
)

func TestGetPerfDataFromFile(t *testing.T) {
	type args struct {
		filePath string
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name: "tt_perf_data_test.csv",
			args: args{
				filePath: "../testdata/tt_perf_data_test.csv",
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				got, err := GetPerfDataFromFile(tt.args.filePath)
				if (err != nil) != tt.wantErr {
					t.Errorf("GetPerfDataFromFile() error = %v, wantErr %v", err, tt.wantErr)
					return
				}

				t.Logf("GetPerfDataFromFile() data = %s", protobuf.MarshalJSONIgnoreError(got))
			},
		)
	}
}

func TestGetPerfCaseFromFile(t *testing.T) {
	type args struct {
		filePath string
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name: "88888_perf_case.yaml",
			args: args{
				filePath: "../testdata/88888_perf_case.yaml",
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				got, err := GetPerfCaseFromFile(tt.args.filePath)
				if (err != nil) != tt.wantErr {
					t.Errorf("GetPerfCaseFromFile() error = %v, wantErr %v", err, tt.wantErr)
					return
				}

				t.Logf("GetPerfCaseFromFile() data = %s", protobuf.MarshalJSONIgnoreError(got))
			},
		)
	}
}

func TestCalculateStepTimeByTimes(t *testing.T) {
	type args struct {
		step       *pb.PerfCaseStep
		numberOfVU uint32
		times      uint32
	}
	tests := []struct {
		name string
		args args
		want time.Duration
	}{
		{
			name: "calculate step time with constants rate limit by times",
			args: args{
				step: &pb.PerfCaseStep{
					Name: "Step1",
					RateLimit: &pb.RateLimit{
						TargetRps: 50,
					},
					Sleep: "200ms",
				},
				numberOfVU: 200,
				times:      4,
			},
			want: 4 * (4*time.Second + 200*time.Millisecond),
		},
		{
			name: "calculate step time with step rate limit by times",
			args: args{
				step: &pb.PerfCaseStep{
					Name: "Step1",
					RateLimit: &pb.RateLimit{
						TargetRps:    100,
						InitialRps:   10,
						StepHeight:   10,
						StepDuration: "2s",
					},
					Sleep: "100ms",
				},
				numberOfVU: 100,
				times:      5,
			},
			want: 6*time.Second + 3*time.Second + 3*2*time.Second + 5*100*time.Millisecond,
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				if got := CalculateStepTimeByTimes(tt.args.step, tt.args.numberOfVU, tt.args.times); got != tt.want {
					t.Errorf("CalculateStepTimeByTimes() = %v, want %v", got, tt.want)
				}
			},
		)
	}
}

func TestTemplateExecuteByString(t *testing.T) {
	type args struct {
		name string
		tpl  string
		data any
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		{
			name: "call `choose` function",
			args: args{
				name: "template",
				tpl:  `{"channel_id": {{ list 123 456 789 | choose }}}`,
				data: nil,
			},
			want: "",
		},
		{
			name: "call `choose` function with variables",
			args: args{
				name: "template",
				tpl:  `{"channel_id": {{ choose .channels }}}`,
				data: map[string]any{
					"channels": []any{123, 456, 789},
				},
			},
			want: "",
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				got := TemplateExecuteByString(tt.args.name, tt.args.tpl, tt.args.data)
				t.Logf("TemplateExecuteByString() = %s", got)
			},
		)
	}
}
