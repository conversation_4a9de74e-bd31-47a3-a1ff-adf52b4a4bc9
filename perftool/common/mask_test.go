package common

import "testing"

func TestMaskIPPort(t *testing.T) {
	type args struct {
		input string
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		{
			name: "mask ip:port",
			args: args{
				input: "transport: authentication handshake failed: read tcp ***********:43534 -> ************:443",
			},
			want: "transport: authentication handshake failed: read tcp " + ipPortMask + " -> " + ipPortMask,
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				if got := MaskIPPort(tt.args.input); got != tt.want {
					t.<PERSON>rrorf("MaskIPPort() = %v, want %v", got, tt.want)
				}
			},
		)
	}
}
