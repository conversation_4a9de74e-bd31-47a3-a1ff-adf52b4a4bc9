package common

import (
	"context"
	"sync"
	"sync/atomic"

	"github.com/zeromicro/go-zero/core/logx"
)

var (
	globalLogger *PerfTestLogger
	mutex        sync.RWMutex
)

func init() {
	mutex.Lock()
	defer mutex.Unlock()

	globalLogger = NewPerfTestLogger(context.Background())
}

func LoadGlobalLogger() *PerfTestLogger {
	mutex.RLock()
	defer mutex.RUnlock()

	return globalLogger
}

func StoreGlobalLogger(logger *PerfTestLogger) {
	mutex.Lock()
	defer mutex.Unlock()

	globalLogger = logger
}

func Error(v ...any) {
	globalLogger.Error(v...)
}

func Errorf(format string, v ...any) {
	globalLogger.Errorf(format, v...)
}

func Warn(v ...any) {
	globalLogger.Warn(v...)
}

func Warnf(format string, v ...any) {
	globalLogger.Warnf(format, v...)
}

func Info(v ...any) {
	globalLogger.Info(v...)
}

func Infof(format string, v ...any) {
	globalLogger.Infof(format, v...)
}

func Debug(v ...any) {
	globalLogger.Debug(v...)
}

func Debugf(format string, v ...any) {
	globalLogger.Debugf(format, v...)
}

type PerfTestLogger struct {
	logx.Logger

	flag *atomic.Bool
}

func NewPerfTestLogger(ctx context.Context) *PerfTestLogger {
	logger := logx.WithContext(ctx)
	logger.WithCallerSkip(1)

	flag := new(atomic.Bool)
	flag.Store(false)

	return &PerfTestLogger{
		Logger: logger,

		flag: flag,
	}
}

func (l *PerfTestLogger) DebugToInfo() {
	l.flag.Store(true)
}

func (l *PerfTestLogger) Error(v ...any) {
	l.Logger.Error(v...)
}

func (l *PerfTestLogger) Errorf(format string, v ...any) {
	l.Logger.Errorf(format, v...)
}

func (l *PerfTestLogger) Warn(v ...any) {
	l.Logger.Warn(v...)
}

func (l *PerfTestLogger) Warnf(format string, v ...any) {
	l.Logger.Warnf(format, v...)
}

func (l *PerfTestLogger) Info(v ...any) {
	l.Logger.Info(v...)
}

func (l *PerfTestLogger) Infof(format string, v ...any) {
	l.Logger.Infof(format, v...)
}

func (l *PerfTestLogger) Debug(v ...any) {
	if l.flag.Load() {
		l.Logger.Info(v...)
	} else {
		l.Logger.Debug(v...)
	}
}

func (l *PerfTestLogger) Debugf(format string, v ...any) {
	if l.flag.Load() {
		l.Logger.Infof(format, v...)
	} else {
		l.Logger.Debugf(format, v...)
	}
}
