package common

import "time"

const (
	ConstMaxWaitTaskTimeout       = 10*time.Minute + 30*time.Second // MaxTimeoutOfWaitJobStarted of perfworker + 30s
	ConstMetricCollectionInterval = 45 * time.Second                // 1.5 metric collection interval

	ConstLogFieldKeyOfTaskID      = "task_id"
	ConstLogFieldKeyOfExecuteID   = "execute_id"
	ConstLogFieldKeyOfExecuteMode = "execute_mode"

	ConstAuthAPIName      = "Auth"
	ConstHeartbeatAPIName = "Heartbeat"
)
