package common

import (
	"os"
	"path/filepath"
	"strings"
	"time"

	"github.com/jmespath/go-jmespath"
	"github.com/oliveagle/jsonpath"
	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"
	zeroutils "github.com/zeromicro/go-zero/core/utils"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/cache"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/template"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/utils"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/perftool/mqc/types"
)

func NewProtobufManager(targets []*pb.ProtobufTarget) (
	*protobuf.ProtoManager, error,
) {
	projects := make([]protobuf.Project, 0, len(targets))
	for _, target := range targets {
		projects = append(
			projects, protobuf.Project{
				Name:         filepath.Base(target.GetPath()),
				Branch:       constants.ConstDefaultBranchName,
				Path:         target.GetPath(),
				ImportPaths:  target.GetImportPaths(),
				ExcludePaths: target.GetExcludePaths(),
				ExcludeFiles: target.GetExcludeFiles(),
			},
		)
	}
	pm, err := protobuf.NewProtoManager(protobuf.WithProducts(protobuf.Product{Projects: projects}))
	if err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.NewObjectFailure, err.Error()),
			"failed to new a protobuf manager, protobuf targets: %s, error: %+v",
			protobuf.MarshalJSONWithMessagesToStringIgnoreError(targets), err,
		)
	}

	return pm, nil
}

func GetPerfCaseFromFile(filePath string) (*pb.PerfCaseContent, error) {
	file, err := os.Open(filePath)
	if err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.FileOperationFailure, err.Error()),
			"failed to open the perf case file, file: %s, error: %+v",
			filePath, err,
		)
	}
	defer func() {
		_ = file.Close()
	}()

	return utils.GetPerfCaseFromReader(file)
}

func GetPerfDataFromFile(filePath string) (*pb.PerfDataContent, error) {
	file, err := os.Open(filePath)
	if err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.FileOperationFailure, err.Error()),
			"failed to open the perf data file, file: %s, error: %+v",
			filePath, err,
		)
	}
	defer func() {
		_ = file.Close()
	}()

	return utils.GetPerfDataFromReader(file)
}

func ConvertToGeneralConfig(config *pb.GeneralConfig) *types.GeneralConfig {
	variables := make(map[string]any, len(config.GetVariables()))
	for _, variable := range config.GetVariables() {
		if variable.GetKey() != "" {
			variables[variable.GetKey()] = variable.GetValue()
		}
	}

	return &types.GeneralConfig{
		BaseURL:   config.GetBaseUrl(),
		Verify:    config.GetVerify(),
		Variables: variables,
	}
}

func CalculateStepTimeByTimes(step *pb.PerfCaseStep, numberOfVU, times uint32) time.Duration {
	var (
		rl            = step.GetRateLimit()
		initialRPS    = rl.GetInitialRps()
		targetRPS     = rl.GetTargetRps()
		stepHeight    = rl.GetStepHeight()
		stepDuration  = cache.Parse(rl.GetStepDuration())
		sleepDuration = cache.Parse(step.GetSleep())
		numberOfTotal = int64(numberOfVU)

		totalDurationSeconds int64
		currentRPS           int64
		numberOfFinished     int64
	)

	// round up
	stepDurationSeconds := int64(stepDuration) / int64(time.Second)
	if stepDuration%time.Second != 0 {
		stepDurationSeconds += 1
	}
	if stepDurationSeconds == 0 {
		stepDurationSeconds = 1
	}

	if stepHeight == 0 {
		currentRPS = targetRPS
	} else {
		currentRPS = initialRPS
	}

	for i := 0; i < int(times); i++ {
		for numberOfFinished < numberOfTotal {
			numberOfFinished += currentRPS
			totalDurationSeconds += 1

			if totalDurationSeconds%stepDurationSeconds == 0 && stepHeight != 0 {
				if stepHeight > 0 && currentRPS < targetRPS {
					currentRPS += stepHeight
				} else if stepHeight < 0 && currentRPS > targetRPS {
					currentRPS += -stepHeight
				}
			}
		}
		numberOfFinished = 0
	}

	return time.Duration(totalDurationSeconds)*time.Second + sleepDuration*time.Duration(times)
}

func TemplateExecuteByString(name, tpl string, data any) string {
	b, err := template.Execute(name, tpl, data)
	if err != nil {
		return tpl
	}

	return zeroutils.ByteSliceToString(b)
}

func TemplateExecuteByMap[M ~map[K]V, K ~string, V any](m M, data any) M {
	out := make(M, len(m))

	for key, val := range m {
		out[key] = val

		if v, ok := any(val).(string); ok {
			if b, e := template.Execute(string(key), v, data); e == nil {
				out[key] = any(zeroutils.ByteSliceToString(b)).(V)
			}
		}
	}

	return out
}

func ExtractByExportExpression(data any, exports []*pb.PerfCaseStepV2_Export, output map[string]any) error {
	var (
		m   any
		err error
	)

	switch v := data.(type) {
	case []byte:
		err = jsonx.Unmarshal(v, &m)
	case string:
		err = jsonx.UnmarshalFromString(v, &m)
	default:
		m = v
	}
	if err != nil {
		return errors.Wrapf(
			errorx.Err(errorx.SerializationError, err.Error()),
			"failed to unmarshal the data, data: %v, error: %+v",
			data, err,
		)
	}

	for _, export := range exports {
		var (
			expr = export.GetExpression()

			result any
		)
		if strings.HasPrefix(expr, "$") {
			result, err = jsonpath.JsonPathLookup(m, expr)
		} else {
			result, err = jmespath.Search(expr, m)
		}

		if err == nil {
			output[export.GetName()] = result
		}
	}

	return nil
}
