project=$(basename $(dirname $(realpath $0)))
base_path=$(dirname $(dirname $(realpath $0)))
echo "Project: ${project}\nBase Path: ${base_path}\n"

buildtime_image="cr.ttyuyin.com/probe/golang:1.23.3"
runtime_image="cr.ttyuyin.com/probe/debian:bookworm-20240904-slim"
image_tag=""
if [[ ! "$1" =~ ^[0-9] ]]; then
  image_tag="$1"
else
  image_tag="v$1"
fi

export DOCKER_CONFIG=/kaniko/.docker
export GOPROXY=http://yw-nexus.ttyuyin.com:8081/repository/group-go/

cp kaniko-harbor-secret.json /kaniko/.docker/config.json

/kaniko/executor \
  --dockerfile=/workspace/${project}/Dockerfile \
  --destination=cr.ttyuyin.com/probe/${project}:${image_tag} \
  --context=dir:///workspace/ \
  --cache=true \
  --build-arg BUILDTIME_IMAGE=${buildtime_image} \
  --build-arg RUNTIME_IMAGE=${runtime_image} \
  --build-arg SERVICE=${project} \
  --build-arg BINARY=${project}.linux \
  --log-timestamp=true \
  --use-new-run \
  --registry-mirror=mirror.ccs.tencentyun.com \
  --registry-mirror=docker.mirrors.ustc.edu.cn
