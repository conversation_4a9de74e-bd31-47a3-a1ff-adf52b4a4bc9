package metrics

import (
	commonmetrics "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/metrics"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/metrics"
)

var (
	requestCounter   commonmetrics.MetricCounterHandler
	requestHistogram commonmetrics.MetricHistogramHandler
)

func init() {
	counterOpts := commonmetrics.MetricCounterVecOpts
	counterOpts.Name = metrics.ConstMetricsPerfToolRequestTotal
	counterOpts.Help = "perftool requests result count."
	counterOpts.Labels = []string{
		string(metrics.ConstMetricsLabelNameOfPerfTaskTaskID),
		string(metrics.ConstMetricsLabelNameOfPerfTaskExecuteID),
		string(metrics.ConstMetricsLabelNameOfPerfTaskProtocol),
		string(metrics.ConstMetricsLabelNameOfPerfTaskName),
		string(metrics.ConstMetricsLabelNameOfPerfTaskPath),
		string(metrics.ConstMetricsLabelNameOfPerfTaskResult),
		string(metrics.ConstMetricsLabelNameOfPerfTaskGrpcStatus),
		string(metrics.ConstMetricsLabelNameOfPerfTaskHttpStatus),
		string(metrics.ConstMetricsLabelNameOfPerfTaskBusinessStatus),
	}
	requestCounter = commonmetrics.NewMetricCounter(&counterOpts)

	histogramOpts := commonmetrics.MetricHistogramVecOpts
	histogramOpts.Name = metrics.ConstMetricsPerfToolRequestDuration
	histogramOpts.Help = "perftool requests duration(ms)."
	histogramOpts.Labels = []string{
		string(metrics.ConstMetricsLabelNameOfPerfTaskTaskID),
		string(metrics.ConstMetricsLabelNameOfPerfTaskExecuteID),
		string(metrics.ConstMetricsLabelNameOfPerfTaskProtocol),
		string(metrics.ConstMetricsLabelNameOfPerfTaskName),
		string(metrics.ConstMetricsLabelNameOfPerfTaskPath),
		string(metrics.ConstMetricsLabelNameOfPerfTaskResult),
		string(metrics.ConstMetricsLabelNameOfPerfTaskGrpcStatus),
		string(metrics.ConstMetricsLabelNameOfPerfTaskHttpStatus),
		string(metrics.ConstMetricsLabelNameOfPerfTaskBusinessStatus),
	}
	histogramOpts.Buckets = metrics.DefaultMetricsBuckets
	requestHistogram = commonmetrics.NewMetricHistogram(&histogramOpts)
}

func RequestFailed(taskID, executeID, protocol, name, path string) {
	requestCounter.Inc(
		taskID, executeID, protocol, name, path, metrics.ConstMetricsLabelValueOfRequestFailed, "", "", "",
	)
}

func RequestSucceeded(taskID, executeID, protocol, name, path string) {
	requestCounter.Inc(
		taskID, executeID, protocol, name, path, metrics.ConstMetricsLabelValueOfRequestSucceeded, "", "", "",
	)
}

func ResponseFailed(taskID, executeID, protocol, name, path string) {
	requestCounter.Inc(
		taskID, executeID, protocol, name, path, metrics.ConstMetricsLabelValueOfResponseFailed, "", "", "",
	)
}

func ResponseSucceeded(
	taskID, executeID, protocol, name, path, result, grpcStatus, httpStatus, businessStatus string, elapsed int64,
) {
	requestCounter.Inc(
		taskID, executeID, protocol, name, path, result, grpcStatus, httpStatus, businessStatus,
	)
	requestHistogram.Observe(
		elapsed, taskID, executeID, protocol, name, path, result, grpcStatus, httpStatus, businessStatus,
	)
}
