package server

import (
	"context"
	"os"
	"sync"
	"syscall"
	"time"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/conf"
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/proc"
	"github.com/zeromicro/go-zero/core/threading"
	"go.uber.org/zap"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/log"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/timewheel"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/perftool/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/perftool/mqc/internal"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/perftool/mqc/internal/config"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/perftool/mqc/internal/svc"
)

type Server struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext

	process      *os.Process
	shutdownOnce sync.Once
}

func NewServer(configFile string) (*Server, error) {
	p, err := os.FindProcess(os.Getpid())
	if err != nil {
		return nil, errors.Errorf("failed to find self process, error: %+v", err)
	}

	var c config.Config
	conf.MustLoad(configFile, &c, conf.UseEnv())
	if err = c.ServiceConf.SetUp(); err != nil {
		return nil, errors.Errorf("failed to setup service config, error: %+v", err)
	}

	w := log.NewZapWriter(c.Log, zap.AddCaller(), zap.AddCallerSkip(1), zap.Development())
	log.SetWriter(w)

	ctx := context.Background()
	s := &Server{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svc.NewServiceContext(c),

		process: p,
	}

	if err = internal.HandleSetupOperations(s.svcCtx); err != nil {
		return nil, err
	}

	proc.AddShutdownListener(s.Shutdown)

	return s, nil
}

func (s *Server) Serve() {
	s.Infof("Starting %q at %s", s.svcCtx.Config.Name, time.Now().Format(time.DateTime))

	threading.GoSafe(s.monitor)
	s.svcCtx.Consumer.Start()
}

func (s *Server) Shutdown() {
	s.shutdownOnce.Do(
		func() {
			s.Infof("Stopping %q at %s", s.svcCtx.Config.Name, time.Now().Format(time.DateTime))

			// shutdown the consumer
			if err := s.process.Signal(syscall.SIGTERM); err != nil {
				s.Errorf("failed to send signal TERM to the consumer, pid: %d, error: %+v", s.process.Pid, err)
			}
			s.svcCtx.Consumer.Stop()

			// send an exit signal to other goroutines
			close(s.svcCtx.ExitChannel)
		},
	)
}

func (s *Server) monitor() {
	defer s.Shutdown()

	timer := timewheel.NewTimer(common.ConstMaxWaitTaskTimeout)
	var stopOnce sync.Once
	stop := func() {
		stopOnce.Do(
			func() {
				timer.Stop()

				if err := s.process.Signal(syscall.SIGTSTP); err != nil {
					s.Errorf("failed to send signal TSTP to the consumer, pid: %d, error: %+v", s.process.Pid, err)
				}
			},
		)
	}
	defer stop()

	for {
		select {
		case <-timer.C:
			s.Warnf(
				"wait for a perf task timeout, task_id: %s, execute_id: %s, timeout: %s",
				s.svcCtx.Config.Task.TaskID, s.svcCtx.Config.Task.ExecuteID, common.ConstMaxWaitTaskTimeout.String(),
			)
			return
		case <-s.svcCtx.TasksChannel:
			s.Infof(
				"got a perf task at %s, task_id: %s, execute_id: %s",
				time.Now().Format(time.DateTime), s.svcCtx.Config.Task.TaskID, s.svcCtx.Config.Task.ExecuteID,
			)
			stop()
		case <-s.svcCtx.FinishChannel:
			s.Infof(
				"the perf task completed at %s, task_id: %s, execute_id: %s",
				time.Now().Format(time.DateTime), s.svcCtx.Config.Task.TaskID, s.svcCtx.Config.Task.ExecuteID,
			)
			return
		case <-s.svcCtx.ExitChannel:
			s.Infof(
				"got an exit signal while waiting for a perf task at %s, task_id: %s, execute_id: %s",
				time.Now().Format(time.DateTime), s.svcCtx.Config.Task.TaskID, s.svcCtx.Config.Task.ExecuteID,
			)
			return
		}
	}
}
