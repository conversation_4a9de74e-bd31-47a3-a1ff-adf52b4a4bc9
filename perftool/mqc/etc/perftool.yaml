Name: perftool
Mode: dev

Log:
  ServiceName: mqc.perftool
  Level: ${LOG_LEVEL}
  Stat: false

DevServer:
  Port: 21221
  EnablePprof: false

Redis:
  Host: 127.0.0.1:6379
  Type: node
  Pass:
  DB: 21

Reporter:
  Endpoints:
    - 127.0.0.1:20511
  NonBlock: true
  Timeout: 0

Consumer:
  Broker: 127.0.0.1:6379
  Backend: 127.0.0.1:6379
  Queue: mqc:perftool
  ConsumerTag: mqc:perftool
  IsEnableMetricsExporter: false # 新增加 暂时不需要开启
  IsMonitorHttp: false # 新增加 选择性开启
  Db: 21
  MaxWorker: 1

PerfWorkerProducer:
  Broker: 127.0.0.1:6379
  Backend: 127.0.0.1:6379
  Queue: mqc:perfworker
  Db: 20

Task:
  TypeName: # 任务类型名称
  TaskID: ${TASK_ID} # 任务ID
  ExecuteID: ${EXECUTE_ID} # 压测用例执行ID
  UseGlobalLimit: false # 是否使用全局限流
  UseLocalLimiter: true # 是否使用本地限流器

Security:
  Enabled: true
  Headers:
    x-tt-security-timestamp: 1419379681024
