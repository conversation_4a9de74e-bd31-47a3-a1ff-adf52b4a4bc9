Name: perftool

Log:
  ServiceName: mqc.perftool
  Level: ${LOG_LEVEL}
  Stat: false

Redis:
  Host: ${REDIS_HOST}
  Type: ${REDIS_TYPE}
  Pass: ${REDIS_PASS}
  DB: ${REDIS_DB}

DevServer:
  Port: 9101
  EnablePprof: false

Reporter:
  Target: ${REPORTER_TARGET} # reporter.probe-test.svc.cluster.local:8080
  NonBlock: true

Consumer:
  Broker: ${CONSUMER_BROKER}
  Backend: ${CONSUMER_BACKEND}
  Queue: ${CONSUMER_QUEUE}
  ConsumerTag: mqc:perftool
  IsEnableMetricsExporter: false # 新增加 暂时不需要开启
  IsMonitorHttp: false # 新增加 选择性开启
  Db: ${CONSUMER_DB}
  MaxWorker: 1

PerfWorkerProducer:
  Broker: ${PERFWORKER_PRODUCER_BROKER}
  Backend: ${PERFWORKER_PRODUCER_BACKEND}
  Queue: ${PERFWORKER_PRODUCER_QUEUE}
  Db: ${PERFWORKER_PRODUCER_DB}

Task:
  TypeName: ${TASK_TYPE_NAME} # 任务类型名称
  TaskID: ${TASK_ID} # 任务ID
  ExecuteID: ${EXECUTE_ID} # 压测用例执行ID
  UseGlobalLimit: false # 是否使用全局限流
  UseLocalLimiter: true # 是否使用本地限流器

Security:
  Enabled: true
  Headers:
    x-tt-security-timestamp: 1419379681024
