package types

import (
	"github.com/zyedidia/generic"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/perftool/mqc/internal/config"
	reporterpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
)

// Config 配置
type Config = config.Config

// ProductName 产品名称
type ProductName string

// ClientType 客户端类型
type ClientType string

func HashProductName(n ProductName) uint64 {
	return generic.HashString(string(n))
}

type GeneralConfig struct {
	BaseURL   string
	Verify    bool
	Variables map[string]any
}

type ResultFeedbackTaskInfo struct {
	TaskId    string
	ExecuteId string
	PodName   string
	ErrMsg    *reporterpb.ErrorMessage
}
