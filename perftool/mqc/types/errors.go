package types

import (
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/perfworker/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/perfworker/common/errorzh"
)

func ErrZhOthersFailed(err error) errorzh.ErrZh {
	return errorzh.NewErrorZh(err, common.ErrMsgZh_Tool_OthersFailed)
}

func ErrZhAllClientCreateFailed(err error) errorzh.ErrZh {
	return errorzh.NewErrorZh(err, common.ErrMsgZh_Tool_AllClientCreateFailed)
}

func ErrZhAllVULoginFailed(err error) errorzh.ErrZh {
	return errorzh.NewErrorZh(err, common.ErrMsgZh_Tool_AllVULoginFailed)
}
