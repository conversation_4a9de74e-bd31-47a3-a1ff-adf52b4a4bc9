package tt

import (
	"github.com/zyedidia/generic"
	"github.com/zyedidia/generic/set"
	"google.golang.org/protobuf/types/dynamicpb"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
	ttcp "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/transport/tcp"
)

func ConvertRequestIDWithMessageAndData(
	pm *protobuf.ProtoManager, name string, data []byte, options ...protobuf.FindOption,
) (
	any, error,
) {
	m, err := pm.CreateMessage(name, options...)
	if err != nil {
		return nil, err
	} else if m == nil {
		return nil, errPBMsgNotFound
	}
	defer pm.PutMessage(m, options...)

	if err := protobuf.UnmarshalMessage(data, m); err != nil {
		return nil, err
	}

	return ConvertRequestIDWithDynamicMessage(m)
}

func ConvertRequestIDWithDynamicMessage(m *dynamicpb.Message) (any, error) {
	r := protobuf.ParseOptions{
		UseProtoNames:  true,
		UseEnumNumbers: false,
	}.ParseMessage(m)
	v, ok := r[keyOfBaseResp]
	if !ok {
		return r, nil
	}

	sm, ok := v.(map[string]any)
	if !ok {
		return r, nil
	}

	v, ok = sm[keyOfRequestID]
	if !ok {
		return r, nil
	}

	rid, ok := v.([]byte)
	if !ok {
		return r, nil
	}

	sm[keyOfRequestID] = string(rid)

	return r, nil
}

func equal[T comparable](old_, new_ []T, equals generic.EqualsFn[T], hash generic.HashFn[T]) bool {
	_old := set.NewHashset(uint64(len(old_)), equals, hash, old_...)
	_new := set.NewHashset(uint64(len(new_)), equals, hash, new_...)

	return _old.Equal(_new)
}

func parsePackage(buf []byte) (int, int) {
	if size := NewPacketParser(buf).IsComplete(); size != 0 {
		return int(size), ttcp.PackageFull
	}

	return 0, ttcp.PackageLess
}
