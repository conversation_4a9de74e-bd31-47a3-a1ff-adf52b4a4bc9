package tt

import (
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/perftool/mqc/internal/clienttypes/registry"
)

func Init() {
	registry.Register(clientTypeTT, NewClient, (*Client)(nil))             // 注册`TT`客户端
	registry.Register(clientTypeTCP, NewClient, (*Client)(nil))            // 注册`TT TCP`客户端
	registry.Register(clientTypeWebSocket, NewClient, (*Client)(nil))      // 注册`TT Websocket`客户端
	registry.Register(clientTypeGRPC, NewClient, (*Client)(nil))           // 注册`TT gRPC`客户端
	registry.Register(clientTypeTTAuth, NewAuthClient, (*AuthClient)(nil)) // 注册`TT Auth`客户端
}
