package tt

import (
	"strconv"
	"strings"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/perftool/common"
)

func GetClientVersionByString(s string, defaults ...uint32) uint32 {
	if t, e := strconv.Atoi(s); e == nil {
		return uint32(t)
	}

	u, err := convert(strings.Split(s, "."))
	if err != nil {
		if s != "" {
			common.Errorf("failed to parse client version[%s], error: %+v", s, err)
		}

		if len(defaults) > 0 {
			return defaults[0]
		}
		return defaultClientVersion
	}

	return u
}

func GetClientVersionByInteger(u uint32) string {
	return strings.Join(
		[]string{
			strconv.Itoa(int((u & 0xff000000) >> 24)),
			strconv.Itoa(int((u & 0xff0000) >> 16)),
			strconv.Itoa(int(u & 0xffff)),
		}, ".",
	)
}

func convert(slice []string) (ret uint32, err error) {
	val := 0

	if len(slice) > 0 {
		if val, err = strconv.Atoi(slice[0]); err != nil {
			return
		}
		ret |= uint32(val) << 24
	}
	if len(slice) > 1 {
		if val, err = strconv.Atoi(slice[1]); err != nil {
			return
		}
		ret |= uint32(val) << 16
	}
	if len(slice) > 2 {
		if val, err = strconv.Atoi(slice[2]); err != nil {
			return
		}
		ret |= uint32(val)
	}
	if ret > 0 {
		return ret, nil
	} else {
		return defaultClientVersion, nil
	}
}
