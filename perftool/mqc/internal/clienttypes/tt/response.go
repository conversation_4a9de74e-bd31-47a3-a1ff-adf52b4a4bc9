package tt

import (
	"bytes"
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"net/http"
	"strings"
	"sync"
	"time"

	"github.com/hashicorp/go-multierror"
	"github.com/pkg/errors"
	"github.com/spf13/cast"
	"github.com/zeromicro/go-zero/core/jsonx"
	"github.com/zeromicro/go-zero/core/trace"
	"github.com/zeromicro/go-zero/core/utils"
	"google.golang.org/grpc/metadata"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/proto"
	"google.golang.org/protobuf/reflect/protoreflect"
	"google.golang.org/protobuf/types/dynamicpb"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
	tgrpc "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/transport/grpc"
	tfasthttp "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/transport/http/fasthttp"

	commonpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/perftool/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/perftool/mqc/internal/clienttypes"
)

var (
	_ clienttypes.IResponse = (*Response)(nil)
	_ clienttypes.IResponse = (*TCPOrWSResponse)(nil)
	_ clienttypes.IResponse = (*GRPCResponse)(nil)
)

type Response struct {
	ctx context.Context // 请求上下文

	method  string        // 请求方法名称
	cmd     uint32        // 响应消息的CMD值
	seq     uint32        // 响应消息的序列号
	elapsed time.Duration // 请求发起到响应接收的耗时
	_data   []byte        // 响应消息的数据（已解密、解压）
	err     error

	bodyOnce sync.Once
	body     any // 响应消息的数据（PB转JSON）
}

func (r *Response) tryToJSONUnmarshal() {
	if len(r._data) == 0 {
		r.body = nil
		return
	}

	var m map[string]any
	if err := json.Unmarshal(r._data, &m); err != nil { // without using json.Number
		common.Errorf("failed to unmarshal the response data, data: %s, error: %v", r._data, err)

		r.body = utils.ByteSliceToString(r._data)
		return
	}

	r.body = m
}

func (r *Response) Key() string {
	return fmt.Sprintf("cmd: %d, seq: %d", r.cmd, r.seq)
}

func (r *Response) Headers() *clienttypes.Headers {
	return nil
}

func (r *Response) Body() any {
	if r.body == nil {
		r.bodyOnce.Do(r.tryToJSONUnmarshal)
	}

	return r.body
}

func (r *Response) Status() int32 {
	return 0
}

func (r *Response) Result() string {
	return ""
}

func (r *Response) Error() error {
	return r.err
}

func (r *Response) Elapsed() time.Duration {
	return r.elapsed
}

func (r *Response) Extract(exports []*commonpb.PerfCaseStepV2_Export, output map[string]any) {
	if r.body == nil || len(exports) == 0 {
		return
	}

	if err := common.ExtractByExportExpression(r.body, exports, output); err != nil {
		common.Errorf(
			"failed to extract variable from the response data, req_id: %s, data: %s, error: %+v",
			trace.TraceIDFromContext(r.ctx), r._data, err,
		)
	}
}

func (r *Response) BusinessStatus() int32 {
	return 0
}

type TCPOrWSResponse struct {
	*Response

	methodDescriptor protoreflect.MethodDescriptor  // 方法描述
	outputDescriptor protoreflect.MessageDescriptor // 响应描述

	ret1   int32 // 服务端返回的结果码（SPH.Ret）
	ret2   int32 // 服务端返回的结果码（SPH.ServiceRetCode）
	isPush bool  // 是否服务端主动推送的消息
}

func (r *TCPOrWSResponse) Status() int32 {
	return r.ret1
}

func (r *TCPOrWSResponse) Result() string {
	if r.ret1 == 0 && r.ret2 == 0 && r.err != nil {
		return r.err.Error()
	}

	return fmt.Sprintf(
		"ret: %d(%s), svrRet: %d(%s)",
		r.ret1, MessageFromCode(int(r.ret1)),
		r.ret2, MessageFromCode(int(r.ret2)),
	)
}

func (r *TCPOrWSResponse) BusinessStatus() int32 {
	if r.ret1 != 0 {
		return r.ret1
	} else if r.ret2 != 0 {
		return r.ret2
	}

	return 0
}

type GRPCResponse struct {
	*Response

	methodDescriptor protoreflect.MethodDescriptor  // 方法描述
	outputDescriptor protoreflect.MessageDescriptor // 响应描述

	headers metadata.MD    // 响应消息的Header数据
	trailer metadata.MD    // 响应消息的Trailer数据
	status  *status.Status // gRPC状态码

	ttCode    int32  // TT错误码
	ttMessage string // TT错误信息

	supportCommonStatus bool // 是否支持`TT`的`CommonStatus`
	commonStatusMessage *dynamicpb.Message

	r *tgrpc.Response
}

func (r *GRPCResponse) Headers() *clienttypes.Headers {
	return &clienttypes.Headers{
		Header:  r.headers.Copy(),
		Trailer: r.trailer.Copy(),
	}
}

func (r *GRPCResponse) Status() int32 {
	return int32(r.status.Code())
}

func (r *GRPCResponse) Result() string {
	if r.ttCode == 0 && r.ttMessage == "" && r.err != nil {
		return r.err.Error()
	}

	return fmt.Sprintf(
		"code: %d, message: %s, tt-code: %d, tt-message: %s",
		r.status.Code(), r.status.Message(), r.ttCode, r.ttMessage,
	)
}

func (r *GRPCResponse) BusinessStatus() int32 {
	return r.ttCode
}

func (r *GRPCResponse) OnReceiveHeaders(md metadata.MD) {
	r.saveMetadata(header, md)
}

func (r *GRPCResponse) OnReceiveResponse(m proto.Message) {
	if dm, ok := m.(*dynamicpb.Message); ok && len(r._data) == 0 {
		r.body, r.err = ConvertRequestIDWithDynamicMessage(dm)
	}
}

func (r *GRPCResponse) OnReceiveTrailers(s *status.Status, md metadata.MD) {
	r.saveMetadata(trailer, md)
	r.saveStatus(s)
}

func (r *GRPCResponse) handle(respErr error) error {
	if !errors.Is(respErr, r.err) {
		return respErr
	}

	r.handleBusinessError()
	r.handleBaseResponse()
	return nil
}

type mdType int

const (
	header mdType = iota + 1
	trailer
)

const binHdrSuffix = "-bin"

func (r *GRPCResponse) saveMetadata(typ mdType, md metadata.MD) {
	var to metadata.MD
	switch typ {
	case header:
		if r.headers == nil {
			r.headers = make(metadata.MD, md.Len())
		}
		to = r.headers
	case trailer:
		if r.trailer == nil {
			r.trailer = make(metadata.MD, md.Len())
		}
		to = r.trailer
	default:
		return
	}

	for k, vs := range md {
		if k == "" || len(vs) == 0 {
			continue
		}

		for _, v := range vs {
			if strings.HasSuffix(k, binHdrSuffix) {
				v = base64.RawStdEncoding.EncodeToString([]byte(v))
			}

			to.Append(k, v)
		}
	}
}

func (r *GRPCResponse) saveStatus(s *status.Status) {
	r.status = s

	err := s.Err()
	if err != nil && r.err != nil {
		r.err = multierror.Append(err, r.err)
	} else if err != nil {
		r.err = err
	}
}

func (r *GRPCResponse) handleBusinessError() {
	if r.supportCommonStatus {
		r.handleBusinessErrorByStatus()
	} else {
		r.handleBusinessErrorByMetadata()
	}
}

func (r *GRPCResponse) handleBusinessErrorByStatus() {
	traceID := trace.TraceIDFromContext(r.ctx)

	defer func() {
		if r.ttCode == 0 {
			// try to get the `x-qw-common-code`
			if vs := r.trailer.Get(respHeaderKeyOfXQWCommonCode); len(vs) > 0 {
				r.ttCode = cast.ToInt32(vs[0])
			}
		}

		if r.ttCode != 0 && r.err != nil {
			r.handleBaseResponse()
			common.Debugf(
				"convert grpc err to `base_resp`, req_id: %s, grpc_err: %+v, base_resp: %s",
				traceID, r.err, jsonx.MarshalIgnoreError(r.body),
			)

			r.err = nil
		}
	}()

	details := r.status.Proto().GetDetails()
	if len(details) == 0 {
		return
	}

	if err := details[0].UnmarshalTo(r.commonStatusMessage); err != nil {
		common.Errorf(
			"failed to unmarshal the grpc status detail to %q, req_id: %s, error: %+v",
			messageFullNameOfCommonStatus, traceID, err,
		)
		return
	}
	common.Debugf(
		"unmarshal the grpc status detail to %q successfully, req_id: %s, data: %s",
		messageFullNameOfCommonStatus, traceID, protobuf.MarshalJSONIgnoreError(r.commonStatusMessage),
	)

	r.commonStatusMessage.Range(
		func(fd protoreflect.FieldDescriptor, val protoreflect.Value) bool {
			switch fd.FullName() {
			case messageFieldFullNameOfCommonStatusCode:
				r.ttCode = cast.ToInt32(val.Interface())
			case messageFieldFullNameOfCommonStatusMessage:
				r.ttMessage = cast.ToString(val.Interface())
			}

			return true
		},
	)
}

func (r *GRPCResponse) handleBusinessErrorByMetadata() {
	if vs := r.trailer.Get(respHeaderKeyOfXTTCode); len(vs) > 0 {
		r.ttCode = cast.ToInt32(vs[0])
	}
	if vs := r.trailer.Get(respHeaderKeyOfXTTMsg); len(vs) > 0 {
		r.ttMessage = cast.ToString(vs[0])
	}
}

func (r *GRPCResponse) handleBaseResponse() {
	if r.body != nil {
		return
	}

	// in order for the test case to be able to make assertions in a uniform way,
	// a fake `base_resp` response needs to be constructed.
	var requestID string
	if vs := r.trailer.Get(reqHeaderKeyOfRequestID); len(vs) > 0 {
		requestID = vs[0]
	}

	r.body = map[string]any{
		keyOfBaseResp: map[string]any{
			keyOfRet:       r.ttCode,
			keyOfErrMsg:    r.ttMessage,
			keyOfRequestID: requestID,
		},
	}
}

type HTTPResponse struct {
	*Response

	url        string      // 请求消息的Url
	headers    http.Header // 响应消息的Header数据
	status     string      // 响应消息的Status数据
	statusCode int         // 响应消息的StatusCode数据

	ttCode    int32  // TT错误码
	ttMessage string // TT错误信息

	r         *tfasthttp.Response
	closeOnce sync.Once
}

func (r *HTTPResponse) Key() string {
	return fmt.Sprintf("url: %s, seq: %d", r.url, r.seq)
}

func (r *HTTPResponse) Headers() *clienttypes.Headers {
	return &clienttypes.Headers{
		Header: r.headers.Clone(),
	}
}

func (r *HTTPResponse) Status() int32 {
	return int32(r.statusCode)
}

func (r *HTTPResponse) Result() string {
	if r.ttCode == 0 && r.ttMessage == "" && r.err != nil {
		return r.err.Error()
	}

	return fmt.Sprintf(
		"status: %s[%d], tt-code: %d, tt-message: %s",
		r.status, r.statusCode, r.ttCode, r.ttMessage,
	)
}

func (r *HTTPResponse) BusinessStatus() int32 {
	return r.ttCode
}

func (r *HTTPResponse) handle() {
	if r.r == nil {
		return
	}

	r.handleHeader()
	r.handleBody()
	r.handleStatus()
	r.handleBusinessError()
}

func (r *HTTPResponse) handleHeader() {
	r.headers = make(http.Header)
	r.r.Header.VisitAll(
		func(k, v []byte) {
			r.headers.Set(utils.ByteSliceToString(k), utils.ByteSliceToString(v))
		},
	)
}

func (r *HTTPResponse) handleBody() {
	var buf bytes.Buffer
	_ = r.r.BodyWriteTo(&buf)
	r._data = buf.Bytes()
	r.tryToConvertDataToBody()
}

func (r *HTTPResponse) handleStatus() {
	r.status = utils.ByteSliceToString(r.r.Header.StatusMessage())
	r.statusCode = r.r.StatusCode()
}

func (r *HTTPResponse) tryToConvertDataToBody() {
	// `TT HTTP`的响应都按`application/json`处理
	r.Response.tryToJSONUnmarshal()
}

func (r *HTTPResponse) handleBusinessError() {
	r.tryToGetXQWCommonCode()
	r.tryToGetCommonStatus()
	r.tryToGetActivityResultCode()
}

// tryToGetXQWCommonCode is used to get the `x-qw-common-code` from the response header
func (r *HTTPResponse) tryToGetXQWCommonCode() {
	if r.ttCode != 0 {
		return
	}

	if v := r.headers.Get(respHeaderKeyOfXQWCommonCode); len(v) > 0 {
		r.ttCode = cast.ToInt32(v)
	}
}

// tryToGetCommonStatus is used to get the `common_status` from the response body
func (r *HTTPResponse) tryToGetCommonStatus() {
	m1, ok := r.body.(map[string]any)
	if !ok {
		return
	}

	cs, ok := m1[respBodyKeyOfStatus]
	if !ok {
		return
	}

	m2, ok := cs.(map[string]any)
	if !ok {
		return
	}

	code, ok := m2[respBodyKeyOfCode]
	if ok {
		r.ttCode = cast.ToInt32(code)
	}

	message, ok := m2[respBodyKeyOfMessage]
	if ok {
		r.ttMessage = cast.ToString(message)
	}
}

func (r *HTTPResponse) tryToGetActivityResultCode() {
	if r.ttCode != 0 {
		return
	}

	m1, ok := r.body.(map[string]any)
	if !ok {
		return
	}

	code, ok := m1[respBodyKeyOfCode]
	if ok {
		r.ttCode = cast.ToInt32(code)
	}

	msg, ok := m1[respBodyKeyOfMsg]
	if ok {
		r.ttMessage = cast.ToString(msg)
	}
}

//func (r *HTTPResponse) closeBody() {
//	r.closeOnce.Do(
//		func() {
//			if r.r != nil && r.r.Body != nil {
//				_, _ = io.Copy(io.Discard, r.r.Body)
//				_ = r.r.Body.Close()
//			}
//		},
//	)
//}
