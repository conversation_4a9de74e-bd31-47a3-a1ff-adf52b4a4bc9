package tt

import "testing"

func TestGetClientVersionByString(t *testing.T) {
	type args struct {
		s string
	}
	tests := []struct {
		name string
		args args
		want uint32
	}{
		{
			name: "123",
			args: args{
				s: "123",
			},
			want: 123,
		},
		{
			name: "6.34.0",
			args: args{
				s: "6.34.0",
			},
			want: 102891520,
		},
		{
			name: "6.38.0",
			args: args{
				s: "6.38.0",
			},
			want: 103153664,
		},
		{
			name: "6.52.5",
			args: args{
				s: "6.52.5",
			},
			want: 104071173,
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				if got := GetClientVersionByString(tt.args.s); got != tt.want {
					t.<PERSON>("GetClientVersionByString() = %v, want %v", got, tt.want)
				} else {
					t.Logf("GetClientVersionByString() = %d", got)
				}
			},
		)
	}
}

func TestGetClientVersionByInteger(t *testing.T) {
	type args struct {
		u uint32
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		{
			name: "default client version",
			args: args{
				u: defaultClientVersion,
			},
			want: "5.245.57599",
		},
		{
			name: "6.34.0",
			args: args{
				u: 102891520,
			},
			want: "6.34.0",
		},
		{
			name: "6.38.0",
			args: args{
				u: 103153664,
			},
			want: "6.38.0",
		},
		{
			name: "6.60.5",
			args: args{
				u: 104595461,
			},
			want: "6.60.5",
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				if got := GetClientVersionByInteger(tt.args.u); got != tt.want {
					t.Errorf("GetClientVersionByInteger() = %v, want %v", got, tt.want)
				} else {
					t.Logf("GetClientVersionByInteger() = %s", got)
				}
			},
		)
	}
}
