syntax="proto2";

package servicepacket;


//
//为了 proxy 和 logic 兼容，加到 body 后, 顾名 tail
//

message TailReq {
    optional bytes jaegerContext = 1; // opentelemetry jaeger_context binary marshal
    optional string test_context = 2; // 自动化测试平台测试用例
    map<string, string> header = 3;   // 透传http2协议头部
    optional string request_id = 4;   // 客户端携带的request-id
}

message TailResp {
}

message ExtraTailReq {
    optional bytes trace_id = 1;
    optional uint32 channel_id = 2;
    optional bool is_extra_channel = 3;

    optional uint32 proxy_ip = 4;
    optional uint32 proxy_port = 5;
    optional uint32 client_id = 6;
}

message ExtraTailResp  {
    optional uint32 channel_id = 1;
}
