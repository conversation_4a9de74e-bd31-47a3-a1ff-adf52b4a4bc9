package tt

import (
	"net/url"
	"strings"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.uber.org/atomic"

	thttp "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/transport/http"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
)

func TestPointer(t *testing.T) {
	l1 := &GRPCBlackList{
		ApiRules: []StringMatch{
			{
				MatchType:  ConstStringMatchTypePrefix,
				MatchValue: "/a/b/c",
			},
			{
				MatchType:  ConstStringMatchTypeExact,
				MatchValue: "/x/y/z",
			},
		},
	}
	l2 := &GRPCBlackList{
		ApiRules: []StringMatch{
			{
				MatchType:  ConstStringMatchTypePrefix,
				MatchValue: "/a/b/d",
			},
			{
				MatchType:  ConstStringMatchTypeExact,
				MatchValue: "/x/y/z",
			},
		},
	}
	tests := []struct {
		desc      string
		newAtomic func() *atomic.Pointer[GRPCBlackList]
		initial   *GRPCBlackList
	}{
		{
			desc: "New",
			newAtomic: func() *atomic.Pointer[GRPCBlackList] {
				return atomic.NewPointer(l1)
			},
			initial: l1,
		},
		{
			desc: "New with nil",
			newAtomic: func() *atomic.Pointer[GRPCBlackList] {
				return atomic.NewPointer[GRPCBlackList](nil)
			},
			initial: nil,
		},
		{
			desc: "New with (*T)(nil)",
			newAtomic: func() *atomic.Pointer[GRPCBlackList] {
				return atomic.NewPointer[GRPCBlackList]((*GRPCBlackList)(nil))
			},
			initial: nil,
		},
		{
			desc: "zero value",
			newAtomic: func() *atomic.Pointer[GRPCBlackList] {
				var p atomic.Pointer[GRPCBlackList]
				return &p
			},
			initial: nil,
		},
	}

	for _, tt := range tests {
		t.Run(
			tt.desc, func(t *testing.T) {
				t.Run(
					"Load", func(t *testing.T) {
						atom := tt.newAtomic()
						require.Equal(t, tt.initial, atom.Load(), "Load should report nil.")
					},
				)

				t.Run(
					"Swap", func(t *testing.T) {
						atom := tt.newAtomic()
						require.Equal(t, tt.initial, atom.Swap(l2), "Swap didn't return the old value.")
						require.Equal(t, l2, atom.Load(), "Swap didn't set the correct value.")
					},
				)

				t.Run(
					"CAS", func(t *testing.T) {
						atom := tt.newAtomic()
						require.True(t, atom.CompareAndSwap(tt.initial, l2), "CAS didn't report a swap.")
						require.Equal(t, l2, atom.Load(), "CAS didn't set the correct value.")
					},
				)

				t.Run(
					"Store", func(t *testing.T) {
						atom := tt.newAtomic()
						atom.Store(l1)
						require.Equal(t, l1, atom.Load(), "Store didn't set the correct value.")
					},
				)
			},
		)
	}
}

func TestParseURL(t *testing.T) {
	tests := []struct {
		name string
		url_ string
		want string
	}{
		{
			name: "http url",
			url_: "https://dev-quality.ttyuyin.com",
			want: "dev-quality.ttyuyin.com",
		},
		{
			name: "http url with 80 port",
			url_: "https://dev-quality.ttyuyin.com:80",
			want: "dev-quality.ttyuyin.com",
		},
		{
			name: "tcp url with authority",
			url_: "tcp://110258177:<EMAIL>",
			want: "testing-login.ttyuyin.com",
		},
		{
			name: "multiple urls",
			url_: "tcp://110258177:<EMAIL>,grpc://testing-apiv2.ttyuyin.com:443",
			want: "testing-login.ttyuyin.com",
		},
		{
			name: "grpc url with 443 port",
			url_: "grpc://testing-apiv2.ttyuyin.com:443",
			want: "testing-apiv2.ttyuyin.com:443",
		},
	}

	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				urls := strings.Split(tt.url_, constants.ConstAddressSeparator)
				u, err := url.Parse(urls[0])
				if err != nil {
					t.Fatal(err)
				}

				domain := u.Host
				if u.Port() == "80" {
					domain = u.Hostname()
				}

				t.Logf("Domain: %s", domain)
				assert.Equal(t, tt.want, domain)
			},
		)
	}
}

func TestGetHTTPService(t *testing.T) {
	tests := []struct {
		name    string
		baseURL string
		reqURL  string
		want    string
	}{
		{
			name:    "with base url 1",
			baseURL: "https://node-unify.52tt.com",
			reqURL:  "/activity-production/wwxs-2024/activity.Activity/getGuildRank",
			want:    "wwxs-2024.activity-production",
		},
		{
			name:    "with base url 2",
			baseURL: "https://node-unify.52tt.com/",
			reqURL:  "/activity-production/wwxs-2024/activity.Activity/getGuildRank",
			want:    "wwxs-2024.activity-production",
		},
		{
			name:    "with base url 3",
			baseURL: "https://node-unify.52tt.com/",
			reqURL:  "activity-production/wwxs-2024/activity.Activity/getGuildRank",
			want:    "wwxs-2024.activity-production",
		},
		{
			name:    "with base url 4",
			baseURL: "https://node-unify.52tt.com",
			reqURL:  "activity-production/wwxs-2024/activity.Activity/getGuildRank",
			want:    "wwxs-2024.activity-production",
		},
		{
			name:    "abs url with base url",
			baseURL: "https://node-unify.52tt.com",
			reqURL:  "https://node-unify.52tt.com/activity-production/wwxs-2024/activity.Activity/getGuildRank",
			want:    "wwxs-2024.activity-production",
		},
		{
			name:    "without base url",
			baseURL: "",
			reqURL:  "https://node-unify.52tt.com/activity-production/wwxs-2024/activity.Activity/getGuildRank",
			want:    "wwxs-2024.activity-production",
		},
	}

	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				url_ := thttp.BuildURL(tt.baseURL, tt.reqURL)
				u, err := url.Parse(url_)
				if err != nil {
					t.Fatal(err)
				}

				path := u.Path
				if !strings.HasPrefix(path, "/") {
					path = "/" + path
				}

				paths := strings.Split(path, "/")
				if len(paths) < 3 {
					t.Errorf("invalid path: %s", path)
					return
				}

				service := paths[2] + "." + paths[1]
				t.Logf("service: %s", service)
				assert.Equal(t, tt.want, service)
			},
		)
	}
}
