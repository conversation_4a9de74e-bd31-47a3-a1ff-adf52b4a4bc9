package tt

const (
	AuthMethodNameZH          = "登录"
	HeartbeatMethodNameZH     = "心跳"
	RefreshTokenMethodNameZH  = "刷新令牌" //nolint: gosec
	RefreshConfigMethodNameZH = "刷新配置"

	apiNameOfRefreshToken  = "RefreshToken"
	apiNameOfRefreshConfig = "RefreshConfig"

	methodFullNameOfAuth            = "ga.api.auth.AuthLogic.Auth"
	methodFullNameOfHeartbeat       = "ga.api.sync.SyncGoLogic.CheckSyncKey"
	methodFullNameOfRefreshToken    = "ga.api.auth.AuthCppLogic.RefreshUnionTokenService" //nolint:gosec
	methodFullNameOfRefreshConfig   = "ga.api.grpc_transport_cfg.GrpcTransportCfgLogic.RefreshTransportConfig"
	methodFullNameOfCppCheckSyncKey = "ga.api.sync.SyncLogic.CheckSyncKey"
	methodFullNameOfGoCheckSyncKey  = "ga.api.sync.SyncGoLogic.CheckSyncKey"
	methodFullNameOfSignIn          = "quwan.stellaris.authentication.v1.AuthenticationService.SignIn"
	methodFullNameOfExchangeToken   = "quwan.stellaris.authentication.v1.AuthenticationService.ExchangeToken" //nolint:gosec

	methodNameOfAuth          = "/ga.api.auth.AuthLogic/Auth"
	methodNameOfHeartbeat     = "/ga.api.sync.SyncGoLogic/CheckSyncKey"
	methodNameOfRefreshToken  = "/ga.api.auth.AuthCppLogic/RefreshUnionTokenService" //nolint: gosec
	methodNameOfRefreshConfig = "/ga.api.grpc_transport_cfg.GrpcTransportCfgLogic/RefreshTransportConfig"
	methodNameOfSignIn        = "/quwan.stellaris.authentication.v1.AuthenticationService/SignIn"
	methodNameOfExchangeToken = "/quwan.stellaris.authentication.v1.AuthenticationService/ExchangeToken" //nolint:gosec

	AuthSleepTime = "5s"

	messageFullNameOfCommonStatus             = "quwan.api.CommonStatus"
	messageFieldFullNameOfCommonStatusCode    = "quwan.api.CommonStatus.code"
	messageFieldFullNameOfCommonStatusMessage = "quwan.api.CommonStatus.message"
	messageFullNameOfOnlyBaseResp             = "ga.RespWithBaseRespOnly"

	cmdOfAuth             uint32 = 10
	cmdOfHeartbeat        uint32 = 74
	cmdOfNotify           uint32 = 4
	cmdOfKickOut          uint32 = 5
	cmdOfPush             uint32 = 6
	cmdOfTransmissionPush uint32 = 9
	cmdOfRefreshToken     uint32 = 33000
	cmdOfRefreshConfig    uint32 = 33010

	keyOfBaseReq            = "base_req"
	keyOfAppID              = "app_id"
	keyOfMarketID           = "market_id"
	keyOfUserPhone          = "user_phone"
	keyOfPwd                = "pwd"
	keyOfLoginType          = "login_type"
	keyOfLoginAccountType   = "login_account_type"
	keyOfSignature          = "signature"
	keyOfCurrVer            = "curr_ver"
	keyOfCurrVerCode        = "curr_ver_code"
	keyOfPreviousCheckSum   = "previous_check_sum"
	keyOfPasswordCredential = "password_credential"
	keyOfIdentifier         = "identifier"
	keyOfPassword           = "password"
	keyOfRefreshToken       = "refresh_token"

	keyOfBaseResp          = "base_resp"
	keyOfAuthInfo          = "auth_info"
	keyOfTransportConfigV2 = "transport_config_v2"
	keyOfTTUnionToken      = "tt_union_token" //nolint:gosec
	keyOfExpiresIn         = "expires_in"
	keyOfWebSecureToken    = "web_secure_token"
	keyOfTransportConfig   = "transport_config"
	keyOfNewCheckSum       = "new_check_sum"
	keyOfRequestID         = "request_id"
	keyOfRet               = "ret"
	keyOfErrMsg            = "err_msg"
	keyOfSecureTokens      = "secure_tokens"
	keyOfUserInfo          = "user_info"

	reqHeaderKeyOfAuthorization          = "authorization"              // 用户身份凭证（JWT）
	reqHeaderKeyOfXTTClientBundleID      = "x-tt-client-bundle-id"      // 客户端bundle_id
	reqHeaderKeyOfXTTMarket              = "x-tt-market"                // 客户端market_id，同BaseReq.market_id
	reqHeaderKeyOfXTTClientType          = "x-tt-client-type"           // 客户端类型
	reqHeaderKeyOfXTTClientVersion       = "x-tt-client-version"        // 客户端版本（TT私有协议请求头ret）
	reqHeaderKeyOfXTTDeviceID            = "x-tt-device-id"             // 设备ID（base64编码后的值）
	reqHeaderKeyOfXTTTerminalType        = "x-tt-terminal-type"         // 终端类型
	reqHeaderKeyOfXTTRequestID           = "x-tt-request-id"            // 请求ID
	reqHeaderKeyOfXQWTrafficMark         = "x-qw-traffic-mark"          // 子环境标识
	reqHeaderKeyOfXTTSupportCommonStatus = "x-tt-support-common-status" // (GRPC协议) 是否支持`common status`

	reqHeaderKeyOfUID           = "req_uid"       // 用户ID
	reqHeaderKeyOfMarketID      = "req_market_id" // 客户端market_id，同BaseReq.market_id
	reqHeaderKeyOfClientType    = "req_cli_type"  // 客户端类型
	reqHeaderKeyOfClientVersion = "req_cli_ver"   // 客户端版本（TT私有协议请求头ret）
	reqHeaderKeyOfDeviceID      = "req_dev_id"    // 设备ID（base64编码后的值）
	reqHeaderKeyOfTerm          = "req_term"      // 终端类型
	reqHeaderKeyOfRequestID     = "req_seqid"     // 请求ID

	respHeaderKeyOfXQWCommonCode        = "x-qw-common-code"        // 兜底错误码
	respHeaderKeyOfStatus               = ":status"                 // http错误码，200为成功
	respHeaderKeyOfGrpcStatus           = "grpc-status"             // grpc状态码，0为成功
	respHeaderKeyOfGrpcMessage          = "grpc-message"            // grpc基本错误信息
	respHeaderKeyOfXTTCode              = "x-tt-code"               // tt错误码
	respHeaderKeyOfXTTMsg               = "x-tt-msg"                // tt错误信息
	respHeaderKeyOfGrpcStatusDetailsBin = "grpc-status-details-bin" // 错误详情

	respBodyKeyOfStatus  = "status" // 通用状态码
	respBodyKeyOfCode    = "code"
	respBodyKeyOfMessage = "message"
	respBodyKeyOfMsg     = "msg" // 活动相关接口错误信息
)

const (
	constRsaPublicKeyN = "0XC2EDCAFAD6D8641C7CEDC1FF962F262767F421D4F15F0F2DA4C215C0F37AD1D9773BC5CF31BB438F7868D5F404C90FE710E7C3172B4AC9038493C1869530BC4B048B54764FE88019402B9F1885515D52827544CF1BF684B11460FF9DE87AC1DC0AA889394EDADC19E6B239D238305D3A6EF171E86D6357C1448877A1115CE121"
	constRsaPublicKeyE = "010001"

	constSizeOfPacketLen uint32 = 4
	constCompressVersion uint16 = 1001

	defaultClientBundleID string = "com.quwan.lfg" // 默认的`bundle_id`

	defaultClientVersion   uint32 = ********* // 默认版本: 6.58.3
	defaultPCClientVersion uint32 = ********  // 默认的PC版本: 2.0.8
)

// ClientType 客户端类型
type ClientType uint16

const (
	ConstClientTypeAndroid ClientType = iota
	ConstClientTypeIOS
	ConstClientTypeWeb
	ConstClientTypePCAssistant
	ConstClientTypeCar
	ConstClientTypePCTT
	ConstClientTypeTXMini                // qq,微信的小程序
	ConstClientTypePCLFG  ClientType = 9 // PC极速版

	ConstClientTypeRobot       ClientType = 253
	ConstClientTypeTAccount    ClientType = 254 // 有些请求的来自于taccountsvr的, 暂时这么叫
	ConstClientTypeUseTerminal ClientType = 255
)

// PlatformType 终端设备类型
type PlatformType byte

const (
	ConstPlatformTypeUnknown PlatformType = iota
	ConstPlatformTypeMobile
	ConstPlatformTypeWeb
	ConstPlatformTypePC
	ConstPlatformTypePad
	ConstPlatformTypeQQMini                  // QQ小程序
	ConstPlatformTypeWeChatMini              // 微信小程序
	ConstPlatformTypeMax        PlatformType = 15
)

// OSType 操作系统类型
type OSType byte

const (
	ConstOSTypeUnknown OSType = iota
	ConstOSTypeAndroid
	ConstOSTypeIOS
	ConstOSTypeWinPhone
	ConstOSTypeMacOSX
	ConstOSTypeWindows
	ConstOSTypeLinux
	ConstOSTypeMax OSType = 15
)

// AppID 应用ID
type AppID uint16

const (
	ConstAppIDTTNormal  AppID = iota /* 默认正常情况下TT的APPID为0 */
	ConstAppIDTTAndroid              /* 废弃 新版就不要填这个值了，考虑到兼容性 APPID 为1 表示 */
	ConstAppIDTTiPhone               /* 废弃 新版就不要填这个值了，考虑到兼容性 APPID 为2 表示 */

	/* TT插件应用的APPID从11开始 */
	ConstAppIDHappyCity      AppID = 11 /* 欢城 */
	ConstAppIDTTGameSDK      AppID = 12 /* TT GAME SDK */
	ConstAppIDTTWinAssistant AppID = 13 /* TT Windows 助手 目前功能限于频道主播助手*/
	ConstAppIDH5WebView      AppID = 14 /* 内嵌H5浏览器 */

	// 2020.06.11
	ConstAppIDTTPC         AppID = 15 // PC版TT
	ConstAppIDTTQQMini     AppID = 16 // QQ小程序版TT
	ConstAppIDTTWeChatMini AppID = 17 // 微信小程序版TT
	ConstAppIDTTPCLFG      AppID = 19 // PC极速版

	ConstAppIDHuanYou  AppID = 22 // 欢游，TT的紧急替代版本
	ConstAppIDZaiYa    AppID = 23 // 在呀，TT的紧急替代版本
	ConstAppIDTopSpeed AppID = 24 // 急速版，TT的紧急替代版本
	ConstAppIDMaiKe    AppID = 25 // 麦可，TT的紧急替代版本
	ConstAppIDMiJing   AppID = 26 // 谜境，沉浸式剧本场景的马甲包

	ConstAppIDAll AppID = 65534
	ConstAppIDMax AppID = 65535
)

// MarketID 应用版本ID
type MarketID uint16

const (
	ConstMarketIDNone     MarketID = iota // TT原版
	ConstMarketIDLite                     // 轻量版（不知道干嘛的），删除某些功能
	ConstMarketIDHuanYou                  // 欢游，TT的紧急替代版本
	ConstMarketIDZaiYa                    // 在呀，TT的紧急替代版本
	ConstMarketIDTopSpeed                 // 急速版，TT的紧急替代版本。t次元
	ConstMarketIDMaiKe                    // 麦可，TT的紧急替代版本
	ConstMarketIDMiJing                   // 谜境，沉浸式剧本场景的马甲包

	ConstMarketIDAll MarketID = 65534
	ConstMarketIDMax MarketID = 65535
)

// LoginType 登录类型
type LoginType uint32

const (
	ConstLoginTypeManual    LoginType = iota + 1 // 手工登录
	ConstLoginTypeReLogin                        // 重新登录
	ConstLoginTypeAutoLogin                      // 自动登录
)

// LoginAccountType 登录账号类型
type LoginAccountType uint32

const (
	ConstLoginAccountTypeInvalid    LoginAccountType = iota // 无效
	ConstLoginAccountTypePhone                              // 手机号
	ConstLoginAccountTypeAccount                            // TT账号
	ConstLoginAccountTypeThirdParty                         // 第三方账号
	ConstLoginAccountTypeTTFuzzy                            // 模糊 TT帐号 或者 手机号（不支持第三方帐号）
)

// 压缩算法
const (
	ConstCompressAlgorithmVersionV1 uint16 = iota + 1
	ConstCompressAlgorithmVersionV2        // 不压缩
)

// 加密算法
const (
	ConstCryptAlgorithmNoEncrypt uint16 = iota
	ConstCryptAlgorithmRsaEncryptWithPublicKey
	ConstCryptAlgorithmRsaDecryptWithPrivateKey
	ConstCryptAlgorithmDesEncryptWithPublicKey
	ConstCryptAlgorithmDesDecryptWithPrivateKey
	ConstCryptAlgorithmAesDecryptWithPrivateKey
	ConstCryptAlgorithmAesDecryptWithoutEncrypt
)

// ServicePacketHeader
const (
	ConstSphMagic     byte   = 189
	ConstSphHeadLen   byte   = 104
	ConstSphHeadVerV1 uint16 = 1
	ConstSphHeadVerV2 uint16 = 2
)

// PacketHeader
const (
	ConstFixedHeadLenV1 uint16 = 16
	ConstFixedHeadLenV2 uint16 = 20
	ConstFixedVersionV1 uint16 = 1
	ConstFixedVersionV2 uint16 = 2
)

// Deprecated: use StringMatchType instead.
type MatchType int32

const (
	ConstMatchTypeUnspecified MatchType = 0 // 未指定
	ConstMatchTypeExact       MatchType = 1 // 精确匹配
	ConstMatchTypePrefix      MatchType = 2 // 前缀匹配
	ConstMatchTypeAll         MatchType = 3 // 完整匹配
)

type StringMatchType string

const (
	ConstStringMatchTypeUnspecified StringMatchType = "MATCH_TYPE_UNSPECIFIED" // 未指定
	ConstStringMatchTypeExact       StringMatchType = "MATCH_TYPE_EXACT"       // 精确匹配
	ConstStringMatchTypePrefix      StringMatchType = "MATCH_TYPE_PREFIX"      // 前缀匹配
	ConstStringMatchTypeAll         StringMatchType = "MATCH_TYPE_ALL"         // 完整匹配
)

type TLSMode string

const (
	ConstTLSModeDisable TLSMode = "TLS_MODE_DISABLE"
	ConstTLSModeSimple  TLSMode = "TLS_MODE_SIMPLE"
	ConstTLSModeMutual  TLSMode = "TLS_MODE_MUTUAL"
)

type SyncType uint32

const (
	SyncTypeIMMsg          SyncType = iota + 1 // IM消息
	SyncTypeContact                            // 联系人
	SyncTypeGame                               // 游戏
	SyncTypeGuild                              // 工会
	SyncTypeGameCircle                         // 游戏圈
	SyncTypeGrow                               // 成长体系
	SyncTypeConfig                             // 配置信息(5次checkSync才可能sync 1次)
	SyncTypeGeneral                            // 公共数据
	SyncTypeSession                            // 群组开黑 （已经下线 不再进行sync）
	SyncTypeGuildV2                            // 工会v2版本同步
	SyncTypeIMMsgV2                            // IM消息v2版本,个人消息与群消息分离.
	SyncTypeGroup                              // 群组
	SyncTypeGuildCircle                        // 公会圈子
	SyncTypeAdvancedConfig                     // 配置信息(1次checkSync就可能sync 1次)
	SyncTypeUGC                                // 异步内容
	SyncTypeNobility                           // 贵族系统数据更新，对应拿到NOBILITY_MSG类型的消息结构，里面有贵族等级
)
