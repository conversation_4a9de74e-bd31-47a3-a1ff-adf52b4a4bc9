package tt

import (
	"context"
	"fmt"
	"sync"
	"time"

	"github.com/zeromicro/go-zero/core/lang"
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zyedidia/generic"
	"github.com/zyedidia/generic/hashmap"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/timewheel"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
)

type cleanType int

const (
	cleanTypeOfTimeout cleanType = iota // 超时
	cleanTypeOfExit                     // 退出
)

type IClient interface {
	logx.Logger

	Key() string
}

type TcpOrWsPendingRequests[C IClient] struct {
	client C

	ctx    context.Context
	exitCh <-chan lang.PlaceholderType

	cache   *hashmap.Map[string, *TCPOrWSRequest] // TCP或者Websocket协议的请求等待响应队列
	timeout time.Duration                         // TCP或者Websocket协议的等待响应超时时间
	mutex   sync.Mutex
}

func (p *TcpOrWsPendingRequests[C]) handle() {
	defer p.clean(cleanTypeOfExit)

	ticker := timewheel.NewTicker(time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			p.clean(cleanTypeOfTimeout)
		case <-p.ctx.Done():
			return
		case <-p.exitCh:
			return
		}
	}
}

func (p *TcpOrWsPendingRequests[C]) clean(t cleanType) {
	key := p.client.Key()

	p.mutex.Lock()
	defer p.mutex.Unlock()

	// 处理剩余的待处理请求
	p.cache.Each(
		func(k string, req *TCPOrWSRequest) {
			switch t {
			case cleanTypeOfTimeout:
				if time.Since(req.startedAt) > p.timeout {
					// 生成超时响应，避免请求侧一直等待响应
					if req.handle(req.newErrorResponse(errClientWaitTimeout)) {
						p.client.Warnf(
							"waiting for the response timeout[%s], key: %s, method: %s, cmd: %d, seq: %d, startedAt: %s",
							p.timeout.String(), key, req.methodDescriptor.FullName(), req.cmd,
							req.seq, req.startedAt.Format("2006-01-02 15:04:05.000"),
						)
					}

					p.cache.Remove(k)
				}
			case cleanTypeOfExit:
				// 生成失效响应，避免请求侧一直等待响应
				if req.handle(req.newErrorResponse(errClientDisabled)) {
					p.client.Warnf(
						"the client has been disabled, key: %s, method: %s, cmd: %d, seq: %d, startedAt: %s",
						key, req.methodDescriptor.FullName(), req.cmd, req.seq,
						req.startedAt.Format("2006-01-02 15:04:05.000"),
					)
				}

				p.cache.Remove(k)
			default:
				return
			}
		},
	)
}

func (p *TcpOrWsPendingRequests[C]) put(req *TCPOrWSRequest) {
	p.mutex.Lock()
	defer p.mutex.Unlock()

	if p.cache == nil {
		p.cache = hashmap.New[string, *TCPOrWSRequest](
			constants.ConstDefaultMakeMapSize, generic.Equals[string], generic.HashString,
		)
	}
	p.cache.Put(genKeyByCmdAndSeq(req.cmd, req.seq), req)
}

func (p *TcpOrWsPendingRequests[C]) getAndRemove(resp *TCPOrWSResponse) *TCPOrWSRequest {
	p.mutex.Lock()
	defer p.mutex.Unlock()

	key := genKeyByCmdAndSeq(resp.cmd, resp.seq)
	req, ok := p.cache.Get(key)
	if !ok {
		return nil
	}

	p.cache.Remove(key)
	return req
}

func genKeyByCmdAndSeq(cmd, seq uint32) string {
	return fmt.Sprintf("%d:%d", cmd, seq)
}
