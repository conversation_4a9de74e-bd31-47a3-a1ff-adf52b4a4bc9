package tt

import (
	"encoding/hex"
	"testing"
)

//nolint:lll
func TestNewPacket(t *testing.T) {
	rawHexStr := "0a0012093236383337383538301a20656666316131393766366437663937626537346162643637386637343566393920012804"
	dataHexStr := "789ce36210e23432b33036b730b5309052484d4b334c34b4344f334b314fb3344f4a3537494c4a3133b7483337314db3b45460d460010025aa0bd9"

	raw, err := hex.DecodeString(rawHexStr)
	if err != nil {
		t.Fatalf("Failed to hex.DecodeString raw: %v", err)
	}

	data, err := hex.DecodeString(dataHexStr)
	if err != nil {
		t.Fatalf("Failed to hex.DecodeString data: %v", err)
	}

	sph := NewServicePacketHeader(
		WithBodyLen(uint32(len(data))),
		WithCompressLen(uint32(len(raw))),
		WithClientVersion(int32(GetClientVersionByString("5.5.14"))),
		WithCmd(10),
		WithUid(0),
		WithClientType(uint16(ConstClientTypeAndroid)),
		WithCryptAlgorithm(ConstCryptAlgorithmNoEncrypt),
		WithTerminalType(NewFromClientType(ConstClientTypeAndroid).Get()),
	)

	ph := NewPacketHeader(10, 1, constSizeOfPacketLen+uint32(sph.HeadLen)+sph.BodyLen)

	packet := NewPacket(ph, sph, data, []byte{})

	t.Logf(
		"PackageLength: %d, HeaderLength: %d\nBody: %s\nData: %s", packet.PacketHeader().PackageLength,
		packet.PacketHeader().HeaderLength, hex.EncodeToString(packet.Body()), hex.EncodeToString(packet.Data()),
	)
	t.Logf("PacketHeader: %s", ph.String())
	t.Logf("ServicePacketHeader: %s", sph.String())
	t.Logf("Packet: %s", packet.String())
}

//nolint:lll
func TestPacketParser(t *testing.T) {
	dataHexStr := "00000315001400020000000a0000000100000000000002fdbd680001000002950000000000000000000000000000000000000000000000000000000000000000000002fe000000000000000a0c660096cce0def7fe4840d0bfeb48c58992a8e5000003e900010000000000000000000000000000000000000000000000000000789c4d90bb8b13511487e70eaea80195adc42a5889b0f1de3b73e7ce5db018f39c6c6692ece635d384792633796c5e6e922974ad84ad04511651d852ac640b11abfd0304612bb5b4b214b1b0d3240b8be581ef9cf3fd7e572e5ce2d6b9f5cf6bd75f3c3e746ec69a4d2cc90295890c6fc5184202250449a2781b40b01927081222facc923dcb2110bacc21be433dec7a3ef67c98e34ac0e4ecd864727e25e446dc8cdb07e0005cc5a2c418440c23c444f929e05e82cbe7e011e0de828781dc13ba91cc74a2645bed743e49fa4d27a39aac6cf5ea6215cbc559cb0de96833186ecf2665b55a1b518f64a753439ed7c66a5a9b4ee57b9bde3c1f19984dcd1d55527b6ebb50cf8c8d7e6da206d3c0ccd6da667636b07bd5a018a6b1165561b16244c54a9ebd034fae7d00e00480537051926a504c7e01dc771067d0638ba0820f655760b24b6d990a54a244f230b664fb0778fdeaf9d74f777f02f00770fb3c77c0ff5fe5217ffcebf7dfb5239e7bc33ffbf6e8987fcf2f14db76d6098a8112ec6434a455d4a090540237d79daae16e4bed0c1ad5799e251660d7cb294b70a6871ad2531dac879df912b6ea19b884b585bf5e2913bde2102d98b6d45e3772e68bec491569fd6d58e8e97bf68e3a5693f9c86ce8d1ea417036bb757525e1e6ca733d6c457ad442c594722693adcded6587290dea9106b5505bee40af71e6b362eaddceca2154442d959ee9a934f4cb09b76e2a72bbbe656df78dc82f745471776bbc31b08ca60867f769a6da1d961eb4c81036f446b220198ad584833da1e59b7490e96f45a1614ea2b047535d6718a54a9d9221a632ca473e86c4041612224b50f184e74ef9f53d6fb481214610218c048209fec27337409cbf13f77c1f5988515f72a9cfa8ed51d1b25d89ca3e1589cfd83f88d5ea78"
	data, err := hex.DecodeString(dataHexStr)
	if err != nil {
		t.Fatalf("Failed to hex.DecodeString: %v", err)
	}

	parser := NewPacketParser(data)
	if 0 == parser.IsComplete() {
		t.Fatalf("Packet data is not complete: %d", parser.IsComplete())
	}
	t.Logf("PacketParser.IsComplete: %d", parser.IsComplete())

	packet := parser.ParsePacket()
	if packet == nil {
		t.Fatal("Failed to parse packet")
	}
	t.Logf(
		"PackageLength: %d, HeaderLength: %d", packet.PacketHeader().PackageLength, packet.PacketHeader().HeaderLength,
	)
}
