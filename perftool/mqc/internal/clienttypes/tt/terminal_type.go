package tt

type TerminalType struct {
	platformType PlatformType
	osType       OSType
	appID        AppID
	terminalType uint32
}

func NewTerminalType(platformType PlatformType, osType OSType, appID AppID) *TerminalType {
	t := &TerminalType{
		platformType: platformType,
		osType:       osType,
		appID:        appID,
	}

	t.convert()
	return t
}

func NewFromClientType(clientType ClientType) *TerminalType {
	switch clientType {
	case ConstClientTypeIOS:
		return NewTerminalType(ConstPlatformTypeMobile, ConstOSTypeIOS, ConstAppIDTTNormal)
	case ConstClientTypePCTT:
		return NewTerminalType(ConstPlatformTypePC, ConstOSTypeWindows, ConstAppIDTTPC)
	case ConstClientTypePCLFG:
		return NewTerminalType(ConstPlatformTypePC, ConstOSTypeWindows, ConstAppIDTTPCLFG)
	default:
		// 默认为安卓移动端
		return NewTerminalType(ConstPlatformTypeMobile, ConstOSType<PERSON>ndroid, ConstAppIDTTNormal)
	}
}

func (t *TerminalType) convert() uint32 {
	t.terminalType = 0
	t.terminalType |= ((uint32)(t.platformType) << 20) & 0x0f00000 // 4 bits（20 ~ 23）
	t.terminalType |= ((uint32)(t.osType) << 16) & 0x0f0000        // 4 bits（16 ~ 19）
	t.terminalType |= uint32(t.appID) & 0xffff                     // 16 bits（0 ~ 15）

	return t.terminalType
}

func (t *TerminalType) GetPlatformType() PlatformType {
	return t.platformType
}

func (t *TerminalType) GetOSType() OSType {
	return t.osType
}

func (t *TerminalType) GetAppID() AppID {
	return t.appID
}

func (t *TerminalType) Get() uint32 {
	return t.terminalType
}
