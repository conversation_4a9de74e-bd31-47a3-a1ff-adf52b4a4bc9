package tt

import (
	"context"
	"encoding/hex"
	"io"
	"strconv"
	"time"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/hash"
	"github.com/zeromicro/go-zero/core/jsonx"
	"github.com/zeromicro/go-zero/core/utils"
	"golang.org/x/exp/slices"
	"google.golang.org/protobuf/reflect/protoreflect"
	"google.golang.org/protobuf/types/dynamicpb"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
	tgrpc "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/transport/grpc"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/perftool/mqc/internal/clienttypes"
)

func (c *client[C]) Key() string {
	if c.authInfo.Uid != 0 {
		return strconv.FormatInt(int64(c.authInfo.Uid), 10)
	}

	return c.authInfo.Username
}

func (c *client[C]) getUidByCmd(cmd uint32) uint32 {
	if cmd == cmdOfAuth {
		return 0
	}

	return c.authInfo.Uid
}

func (c *client[C]) clientVersion() int32 {
	if c.clientInfo.ClientType == ConstClientTypePCTT {
		return int32(GetClientVersionByString(c.clientInfo.ClientVersion, defaultPCClientVersion))
	} else {
		return int32(GetClientVersionByString(c.clientInfo.ClientVersion))
	}
}

func (c *client[C]) terminalType() uint32 {
	return NewFromClientType(c.clientInfo.ClientType).Get()
}

func (c *client[C]) deviceID() []byte {
	if c.clientInfo.ClientType == ConstClientTypePCTT {
		return utils.StringToByteSlice(c.authInfo.Account)
	}

	return hash.Md5(utils.StringToByteSlice(c.authInfo.Username))
}

func (c *client[C]) sendTCPOrWSRequest(ctx context.Context, req *TCPOrWSRequest) (resp *TCPOrWSResponse, err error) {
	key := c.Key()

	if req.cmd == 0 || req.seq == 0 {
		return nil, errors.Errorf(
			"invalid request data, the cmd or seq cannot be zero, key: %s, method: %s, cmd: %d, seq: %d",
			key, req.methodDescriptor.FullName(), req.cmd, req.seq,
		)
	}

	// 根据私有协议对请求进行打包
	data, err := c.tcpOrWsProtocol.PackRequest(ctx, req)
	if err != nil {
		return nil, errors.Wrapf(
			err, "failed to pack the request data, key: %s, method: %s, cmd: %d, seq: %d",
			key, req.methodDescriptor.FullName(), req.cmd, req.seq,
		)
	}

	// 请求没有响应，则构造空响应来返回
	if req.noResponse {
		return req.newEmptyResponse(), nil
	}

	respCh := respChPool.Get().(chan clienttypes.IResponse)
	defer func() {
		req.responseCh = nil
		respChPool.Put(respCh)
	}()

	// 请求有响应，则缓存请求信息等待响应
	c.tcpOrWsPendingRequests.put(req)

	req.responseCh = respCh
	if req.startedAt.IsZero() {
		req.startedAt = time.Now()
	}

	var transport Transport
	if c.tcpTransport != nil {
		transport = c.tcpTransport
	} else if c.wsTransport != nil {
		transport = c.wsTransport
	} else {
		return nil, errEmptyTcpOrWsTransport
	}
	if err = transport.Send(data); err != nil {
		return nil, errors.Wrapf(
			err, "failed to send the tcp or websocket request, key: %s, method: %s, cmd: %d, seq: %d",
			key, req.methodDescriptor.FullName(), req.cmd, req.seq,
		)
	}

	select {
	case <-ctx.Done():
		req.handled = true // 避免协程继续把响应消息传入响应管道中
		return nil, ctx.Err()
	case r, ok := <-respCh:
		if !ok {
			return nil, errors.Errorf(
				"the response channel has been closed, key: %s, method: %s, cmd: %d, seq: %d",
				key, req.methodDescriptor.FullName(), req.cmd, req.seq,
			)
		}

		resp, ok = r.(*TCPOrWSResponse)
		if !ok {
			return nil, errors.Errorf(
				"invalid response type, expected: %T, but got: %T, key: %s, method: %s, cmd: %d, seq: %d",
				(*TCPOrWSResponse)(nil), r, key, req.methodDescriptor.FullName(), req.cmd, req.seq,
			)
		}

		return resp, nil
	}
}

func (c *client[C]) handleRecvData(pkg []byte) (req *TCPOrWSRequest, resp *TCPOrWSResponse, err error) {
	resp, err = c.tcpOrWsProtocol.UnpackResponse(pkg)
	if err != nil || resp.isPush {
		return req, resp, err
	}

	req = c.tcpOrWsPendingRequests.getAndRemove(resp)
	if req != nil {
		resp.outputDescriptor = req.outputDescriptor
		resp.elapsed = time.Since(req.startedAt)

		if (resp.ret1 != 0 || resp.ret2 != 0) && len(resp._data) == 0 {
			resp.err = errors.Errorf(
				"ret: %d(%s), svrRet: %d(%s)",
				resp.ret1, MessageFromCode(int(resp.ret1)),
				resp.ret2, MessageFromCode(int(resp.ret2)),
			)
		} else {
			output := string(req.outputDescriptor.FullName())
			if resp.ret1 != 0 || resp.ret2 != 0 {
				output = messageFullNameOfOnlyBaseResp
			}
			resp.body, resp.err = c.convertRequestIDWithMessageAndData(output, resp._data)
		}

		req.handle(resp)
	}

	return req, resp, err
}

func (c *client[C]) grpcRequestSupplier(pm *protobuf.ProtoManager, req *GRPCRequest) tgrpc.RequestSupplier {
	key := c.Key()

	return func(msg *dynamicpb.Message) error {
		// JSON -> PB Message
		req.data = c.tryToSetBaseReq(req.data)
		req.data = c.tryToSetClientVersion(req.methodDescriptor, req.data)
		if err := pm.UnmarshalMessage(msg, req.data); err != nil {
			c.Errorf(
				"failed to unmarshal request data to message, key: %s, method: %s, cmd: %d, seq: %d, error: %+v",
				key, req.methodDescriptor.FullName(), req.cmd, req.seq, err,
			)
			return err
		}

		return io.EOF
	}
}

func (c *client[C]) tryToSetBaseReq(raw []byte) []byte {
	var m map[string]any
	if err := jsonx.Unmarshal(raw, &m); err != nil {
		return raw
	}

	v, ok := m[keyOfBaseReq]
	if !ok {
		// 没有设置`base_req`
		m[keyOfBaseReq] = map[string]any{
			keyOfAppID:    c.clientInfo.AppID,
			keyOfMarketID: c.clientInfo.MarketID,
		}
	} else {
		// 有设置`base_req`
		br, ok := v.(map[string]any)
		if !ok {
			return raw
		}

		if _, ok = br[keyOfAppID]; !ok {
			// 没有设置`base_req.app_id`
			br[keyOfAppID] = c.clientInfo.AppID
		}
		if _, ok = br[keyOfMarketID]; !ok {
			// 没有设置`base_req.market_id`
			br[keyOfMarketID] = c.clientInfo.MarketID
		}
	}

	bs, err := jsonx.Marshal(m)
	if err != nil {
		return raw
	}

	return bs
}

func (c *client[C]) tryToSetClientVersion(md protoreflect.MethodDescriptor, raw []byte) []byte {
	method := string(md.FullName())
	if !slices.Contains[[]string, string](setClientVersionMethods, method) {
		return raw
	}

	var m map[string]any
	if err := jsonx.Unmarshal(raw, &m); err != nil {
		return raw
	}

	switch method {
	case methodFullNameOfCppCheckSyncKey, methodFullNameOfGoCheckSyncKey:
		_, ok := m[keyOfCurrVer]
		if !ok {
			m[keyOfCurrVer] = c.clientInfo.ClientVersion
		}
		_, ok = m[keyOfCurrVerCode]
		if !ok {
			m[keyOfCurrVerCode] = c.metadata.clientVersion
		}
	}

	bs, err := jsonx.Marshal(m)
	if err != nil {
		return raw
	}

	return bs
}

func (c *client[C]) convertRequestIDWithMessageAndData(name string, data []byte) (any, error) {
	body, err := ConvertRequestIDWithMessageAndData(c.CreateClientInfo.ProtoManager, name, data)
	if err != nil {
		c.Errorf(
			"failed to convert request id with message and data, name: %s, data: %s, error: %+v",
			name, hex.EncodeToString(data), err,
		)
	}

	return body, err
}
