package tt

import (
	"context"
	"encoding/base64"
	"fmt"
	"net"
	"net/http"
	"net/url"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/devfeel/mapper"
	"github.com/hashicorp/go-multierror"
	"github.com/mitchellh/mapstructure"
	"github.com/pkg/errors"
	"github.com/spf13/cast"
	"github.com/zeromicro/go-zero/core/hash"
	"github.com/zeromicro/go-zero/core/jsonx"
	"github.com/zeromicro/go-zero/core/lang"
	"github.com/zeromicro/go-zero/core/threading"
	"github.com/zeromicro/go-zero/core/trace"
	"github.com/zeromicro/go-zero/core/utils"
	"github.com/zyedidia/generic"
	"github.com/zyedidia/generic/hashmap"
	"github.com/zyedidia/generic/set"
	"go.uber.org/atomic"
	"golang.org/x/exp/maps"
	"google.golang.org/grpc"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/encoding/gzip"
	"google.golang.org/grpc/metadata"
	"google.golang.org/protobuf/reflect/protoreflect"

	qetconstants "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/security"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/timewheel"
	tgrpc "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/transport/grpc"
	thttp "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/transport/http"
	tfasthttp "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/transport/http/fasthttp"
	ttcp "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/transport/tcp"
	twebsocket "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/transport/websocket"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/calculate"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	commonpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/tt"
	commonutils "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/utils"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/perftool/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/perftool/mqc/internal/clienttypes"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/perftool/mqc/types"
)

const (
	productName  types.ProductName = "tt"
	clientTypeTT                   = types.ClientType(productName)
	// Deprecated: Use clientTypeTT instead.
	clientTypeTCP = types.ClientType(productName + "_tcp")
	// Deprecated: Use clientTypeTT instead.
	clientTypeWebSocket = types.ClientType(productName + "_websocket")
	// Deprecated: Use clientTypeTT instead.
	clientTypeGRPC = types.ClientType(productName + "_grpc")
	currentPrefix  = "current_"

	defaultBranch = "master"

	ClientQueueLen = 100

	heartbeatInterval     = 3 * time.Minute
	refreshTokenInterval  = 9 * time.Minute
	refreshConfigInterval = 6 * time.Minute
)

var (
	_ clienttypes.IClient  = (*Client)(nil)
	_ clienttypes.IAuth    = (*Client)(nil)
	_ clienttypes.IMonitor = (*Client)(nil)
	_ clienttypes.ILimiter = (*Client)(nil)

	pushCmdSlice       = []uint32{cmdOfNotify, cmdOfKickOut, cmdOfPush, cmdOfTransmissionPush}
	builtInCallMethods = []string{
		methodFullNameOfAuth,
		methodFullNameOfHeartbeat,
		methodFullNameOfRefreshToken,
		methodFullNameOfRefreshConfig,
		methodFullNameOfSignIn,
		methodFullNameOfExchangeToken,
	}
	setClientVersionMethods = []string{methodFullNameOfCppCheckSyncKey, methodFullNameOfGoCheckSyncKey}

	httpTransport = &http.Transport{
		Proxy: http.ProxyFromEnvironment,
		DialContext: (&net.Dialer{
			Timeout:   30 * time.Second, // 连接超时时间
			KeepAlive: 30 * time.Second, // TCP KeepAlive 时间
		}).DialContext,
		// ForceAttemptHTTP2:     true,
		DisableKeepAlives:     true,
		MaxIdleConns:          5000,             // 最大空闲连接数
		MaxIdleConnsPerHost:   1000,             // 每个主机的最大空闲连接数
		MaxConnsPerHost:       1000,             // 单个主机的最大连接数
		IdleConnTimeout:       30 * time.Second, // 空闲连接的超时时间
		TLSHandshakeTimeout:   10 * time.Second, // TLS 握手的超时时间
		ExpectContinueTimeout: 1 * time.Second,
	}

	respChPool = &sync.Pool{New: func() any { return make(chan clienttypes.IResponse, 1) }}
)

func NewClient(
	ctx context.Context, exitCh <-chan lang.PlaceholderType, req *clienttypes.CreateClientReq,
) (v clienttypes.IClient, err error) {
	ci, ai, err := parseCreateClientReq(req)
	if err != nil {
		return nil, errors.Errorf(
			"failed to create %s client, req: %s, error: %v", productName, jsonx.MarshalIgnoreError(req), err,
		)
	}

	c := &Client{
		client: &client[*Client]{
			BasicClient: clienttypes.NewBasicClient(ctx, exitCh, req),

			grpcWaitResponseTimeout: clienttypes.WaiteRespTimeout,

			clientInfo: ci,
			authInfo:   ai,
		},

		loginResp:               make(map[string]any),
		httpWaitResponseTimeout: clienttypes.WaiteRespTimeout,

		logicOptions: hashmap.New[string, tt.LogicOptions](
			constants.ConstDefaultMakeMapSize, generic.Equals[string], generic.HashString,
		),
		callByGRPC_: hashmap.New[string, bool](
			constants.ConstDefaultMakeMapSize, generic.Equals[string], generic.HashString,
		),
		callWithGZIP_: hashmap.New[string, bool](
			constants.ConstDefaultMakeMapSize, generic.Equals[string], generic.HashString,
		),
	}
	if c.CreateClientInfo.HeartbeatConfig != nil {
		c.refreshTokenKey = strings.Replace(
			c.CreateClientInfo.HeartbeatConfig.GetKey(), common.ConstHeartbeatAPIName, apiNameOfRefreshToken, 1,
		)
		c.refreshConfigKey = strings.Replace(
			c.CreateClientInfo.HeartbeatConfig.GetKey(), common.ConstHeartbeatAPIName, apiNameOfRefreshConfig, 1,
		)
	}
	c.tcpOrWsProtocol = NewProtocol(c.client)
	c.tcpOrWsPendingRequests = &TcpOrWsPendingRequests[*Client]{
		client: c,
		ctx:    ctx,
		exitCh: exitCh,
		cache: hashmap.New[string, *TCPOrWSRequest](
			constants.ConstDefaultMakeMapSize, generic.Equals[string], generic.HashString,
		),
		timeout: clienttypes.WaiteRespTimeout,
	}
	c.metadata = clientMetadata{
		clientVersion: c.clientVersion(),
		terminalType:  c.terminalType(),
		deviceID:      c.deviceID(),
	}
	c.metadata.deviceIDString = fmt.Sprintf("%x", c.metadata.deviceID)

	// 创建`transports`
	if err = c.newTransports(); err != nil {
		return nil, err
	}

	// 定时清理超时的请求数据
	threading.GoSafe(c.tcpOrWsPendingRequests.handle)

	return c, nil
}

// parseCreateClientReq 解析创建客户端请求消息
func parseCreateClientReq(req *clienttypes.CreateClientReq) (ci ClientInfo, ai AuthInfo, err error) {
	if req.URL == "" {
		return ci, ai, errEmptyURL
	}
	if req.ProtoManager == nil {
		return ci, ai, errEmptyProtoManager
	}

	ci, err = getClientInfoFromCreateClientReq(req)
	if err != nil {
		return ci, ai, err
	}

	if ci.TcpURL == "" && ci.WebsocketURL == "" && ci.ClientType != ConstClientTypePCLFG {
		return ci, ai, errEmptyTcpOrWsURL
	} else if ci.GrpcURL == "" && ci.ClientType == ConstClientTypePCLFG {
		return ci, ai, errEmptyGrpcURL
	} else if ci.Username == "" {
		return ci, ai, errEmptyUsername
	} else if ci.Password == "" {
		return ci, ai, errEmptyPassword
	}

	ai = AuthInfo{
		Username:    ci.Username,
		Password:    ci.Password,
		MD5Password: hash.Md5Hex(utils.StringToByteSlice(ci.Password)),
	}

	return ci, ai, nil
}

func getClientInfoFromCreateClientReq(req *clienttypes.CreateClientReq) (ClientInfo, error) {
	ci := defaultClientInfo()

	urls := strings.Split(req.URL, constants.ConstAddressSeparator)
	for _, url_ := range urls {
		u, err := url.Parse(url_)
		if err != nil {
			return ci, err
		}

		// 服务端地址取第一个
		url__ := fmt.Sprintf("%s://%s", u.Scheme, u.Host)
		if ci.Url == "" {
			ci.Url = url__
		}

		// 用户名和密码取第一个
		if u.User != nil && ci.Username == "" && ci.Password == "" {
			ci.Username = u.User.Username()
			ci.Password, _ = u.User.Password()
		}

		switch u.Scheme {
		case string(qetconstants.TCP), string(qetconstants.GRPC):
			host := u.Host
			if host == "" {
				host = u.Path
			}
			if u.Port() == "" {
				host = fmt.Sprintf("%s:%d", host, constants.ConstPort80)
			}

			if u.Scheme == string(qetconstants.TCP) {
				ci.TcpURL = host
			} else {
				ci.GrpcURL = host
			}
		case string(qetconstants.WS), string(qetconstants.WSS):
			ci.WebsocketURL = url__
		case string(qetconstants.HTTP), string(qetconstants.HTTPS):
			ci.HttpURL = url__
		}
	}

	if req.Variables != nil {
		if err := mapper.MapperMap(req.Variables, &ci); err != nil {
			return ci, err
		}
	}
	if ci.ClientType == ConstClientTypePCLFG {
		ci.AppID = ConstAppIDTTPCLFG
	}

	return ci, nil
}

func (c *Client) RequiredAfterAuth() bool {
	return true
}

func (c *Client) GetMonitorParams(
	generalConfig *types.GeneralConfig, pm *protobuf.ProtoManager, perfCase *commonpb.PerfCaseContentV2,
) clienttypes.MonitorParams {
	var (
		baseURL = ""

		commandSet = set.NewHashset(
			constants.ConstDefaultMakeSliceSize, generic.Equals[uint32], generic.HashUint32,
		)
		grpcPathSet = set.NewHashset(
			constants.ConstDefaultMakeSliceSize, generic.Equals[string], generic.HashString,
		)
		serviceSet = set.NewHashset(
			constants.ConstDefaultMakeSliceSize, generic.Equals[string], generic.HashString,
		)
	)

	if c != nil && c.loggedIn {
		baseURL = c.httpBaseURL
	} else {
		urls := strings.Split(generalConfig.BaseURL, constants.ConstAddressSeparator)
		for _, url_ := range urls {
			u, err := url.Parse(url_)
			if err != nil {
				continue
			} else if u.Scheme == string(qetconstants.HTTP) || u.Scheme == string(qetconstants.HTTPS) {
				baseURL = fmt.Sprintf("%s://%s", u.Scheme, u.Host)
				break
			}
		}
	}

	saveCmdAndGRPCPathFn := func(step *commonpb.PerfCaseStepV2) {
		if step.GetCmd() == 0 {
			return
		}

		grpcPath := step.GetGrpcPath()
		if len(step.GetQueryPath()) > 0 {
			grpcPath = step.GetQueryPath()
		}

		if c != nil && c.loggedIn {
			if c.callByGRPC(step.GetCmd(), commonutils.GetGRPCFullMethodName(step.GetMethod())) {
				grpcPathSet.Put(grpcPath)
			} else {
				commandSet.Put(step.GetCmd())
			}
		} else {
			commandSet.Put(step.GetCmd())
			grpcPathSet.Put(grpcPath)
		}
	}
	saveHTTPServiceFn := func(step *commonpb.PerfCaseStepV2) {
		// https://node-unify.52tt.com/activity-production/wwxs-2024/activity.Activity/getGuildRank => wwxs-2024.activity-production
		url_ := thttp.BuildURL(baseURL, step.GetUrl())
		u, err := url.Parse(url_)
		if err != nil {
			return
		}

		path := u.Path
		if !strings.HasPrefix(path, "/") {
			path = "/" + path
		}

		paths := strings.Split(path, "/")
		if len(paths) < 3 {
			return
		}

		serviceSet.Put(paths[2] + "." + paths[1])
	}

	for _, steps := range [][]*commonpb.PerfCaseStepV2{
		perfCase.GetSetupSteps(),
		perfCase.GetSerialSteps(),
		perfCase.GetParallelSteps(),
		perfCase.GetTeardownSteps(),
	} {
		for _, step := range steps {
			if thttp.IsHTTPMethod(step.GetMethod()) {
				saveHTTPServiceFn(step)
			} else {
				saveCmdAndGRPCPathFn(step)
			}
		}
	}

	return clienttypes.MonitorParams{
		Commands:  commandSet.Keys(),
		GRPCPaths: grpcPathSet.Keys(),
		Services:  serviceSet.Keys(),
	}
}

func (c *Client) GetRateLimits(targetEnv commonpb.TargetEnvironment) []*clienttypes.RateLimit {
	var (
		targetRPS  int64 = calculate.ConstDefaultHeartbeatInitialRPS // 100
		initialRPS       = targetRPS
	)
	switch targetEnv {
	case commonpb.TargetEnvironment_TE_STAGING,
		commonpb.TargetEnvironment_TE_CANARY,
		commonpb.TargetEnvironment_TE_PRODUCTION:
		targetRPS = calculate.ConstDefaultHeartbeatTargetRPS // 1000
		initialRPS = targetRPS
	}

	return []*clienttypes.RateLimit{
		{
			ApiName:  apiNameOfRefreshToken,
			Interval: uint32(refreshTokenInterval.Seconds()),
			Limits: []*commonpb.RateLimitV2{
				{
					TargetRps:  targetRPS,
					InitialRps: initialRPS,
				},
			},
		},
		{
			ApiName:  apiNameOfRefreshConfig,
			Interval: uint32(refreshConfigInterval.Seconds()),
			Limits: []*commonpb.RateLimitV2{
				{
					TargetRps:  targetRPS,
					InitialRps: initialRPS,
				},
			},
		},
	}
}

func (c *Client) newTransports() error {
	if c.clientInfo.TcpURL == "" && c.clientInfo.WebsocketURL == "" && c.clientInfo.ClientType != ConstClientTypePCLFG {
		// 非PC极速版通过`tcp`或者`websocket`进行登录
		return errEmptyTcpOrWsURL
	}
	if c.clientInfo.GrpcURL == "" && c.clientInfo.ClientType == ConstClientTypePCLFG {
		// PC极速版通过`grpc`进行登录
		return errEmptyGrpcURL
	}

	if c.clientInfo.TcpURL != "" {
		c.newTCPTransport()
	}
	if c.clientInfo.WebsocketURL != "" {
		c.newWebsocketTransport()
	}
	if c.clientInfo.GrpcURL != "" {
		c.newGRPCTransport()
	}
	if c.clientInfo.HttpURL != "" {
		c.newHTTPTransport()
	}

	return nil
}

// newTCPTransport 创建`TCP Transport`
func (c *Client) newTCPTransport() {
	key := c.Key()

	if c.clientInfo.TcpURL != "" {
		c.tcpURL = c.clientInfo.TcpURL
	}

	if c.tcpTransport != nil {
		c.Debugf("tcp transport has been created, key: %s", key)
		return
	}

	c.tcpTransport = ttcp.NewClient(
		c.tcpURL, c, ttcp.ClientConf{
			QueueLen:     ClientQueueLen,
			IdleTimeout:  clienttypes.ClientIdleTimeout,
			ReadTimeout:  clienttypes.ClientReadTimeout,
			WriteTimeout: clienttypes.ClientWriteTimeout,
			DialTimeout:  clienttypes.ClientDialTimeout,
		},
	)
}

// newWebsocketTransport 创建`Websocket Transport`
func (c *Client) newWebsocketTransport() {
	key := c.Key()

	if c.clientInfo.WebsocketURL != "" {
		c.wsURL = c.clientInfo.WebsocketURL
	}

	if c.wsTransport != nil {
		c.Debugf("websocket transport has been created, key: %s", key)
		return
	}

	c.wsTransport = twebsocket.NewClient(
		c.wsURL, c, twebsocket.ClientConf{
			QueueLen:     ClientQueueLen,
			IdleTimeout:  clienttypes.ClientIdleTimeout,
			ReadTimeout:  clienttypes.ClientReadTimeout,
			WriteTimeout: clienttypes.ClientWriteTimeout,
			DialTimeout:  clienttypes.ClientDialTimeout,
		},
	)
}

// newGRPCTransport 创建`gRPC Transport`
func (c *Client) newGRPCTransport() {
	key := c.Key()

	if c.clientInfo.GrpcURL != "" {
		c.grpcURL = c.clientInfo.GrpcURL
	}
	if c.clientInfo.GrpcAuthority != "" {
		c.grpcAuthority = c.clientInfo.GrpcAuthority
	}

	afterLogin := false
	if c.refreshInfo != nil && c.refreshInfo.Endpoints != nil {
		afterLogin = true
		if es := c.refreshInfo.Endpoints.Load(); es != nil && len(*es) > 0 {
			endpoint := (*es)[0]
			// `c.clientInfo.GrpcURL` has a higher priority than `endpoint.Address`
			if c.clientInfo.GrpcURL == "" {
				c.grpcURL = endpoint.Address
			}
			// `c.clientInfo.GrpcAuthority` has a higher priority than `endpoint.TlsConfig.Authority`
			if c.clientInfo.GrpcAuthority == "" {
				c.grpcAuthority = endpoint.TlsConfig.Authority
			}
		}
	}

	if c.grpcURL == "" {
		c.Warnf("the grpc url and endpoints are both empty, key: %s", key)
		return
	} else if !afterLogin && c.grpcAuthority == "" && c.clientInfo.ClientType != ConstClientTypePCLFG {
		c.Debugf("the grpc authority is not set, try to create grpc transport after logging in, key: %s", key)
		return
	}

	if c.grpcTransport != nil && c.grpcTransport.Load() != nil {
		c.Debugf("grpc transport has been created, key: %s", key)
		return
	}

	c.Debugf("create grpc transport by url and authority, url: %s, authority: %s", c.grpcURL, c.grpcAuthority)
	c.grpcTransport = atomic.NewPointer(tgrpc.NewClient(c.grpcURL, tgrpc.ClientConf{Authority: c.grpcAuthority}))
}

// newHTTPTransport 创建`HTTP Transport`
func (c *Client) newHTTPTransport() {
	key := c.Key()

	if c.clientInfo.HttpURL != "" {
		c.httpBaseURL = c.clientInfo.HttpURL
	}

	if c.httpTransport != nil {
		c.Debugf("http transport has been created, key: %s", key)
		return
	}

	c.httpTransport = tfasthttp.NewClient(
		tfasthttp.ClientConf{
			BaseURL:      c.httpBaseURL,
			IdleTimeout:  clienttypes.ClientIdleTimeout,
			ReadTimeout:  clienttypes.ClientReadTimeout,
			WriteTimeout: clienttypes.ClientWriteTimeout,
		},
	)
}

// ProductName 获取客户端的产品名称
func (c *Client) ProductName() types.ProductName {
	return productName
}

// ClientType 获取客户端类型
func (c *Client) ClientType() types.ClientType {
	return c.CreateClientInfo.Type
}

// Environment 获取客户端对应的环境（返回空字符串表示不区分环境）
func (c *Client) Environment() string {
	var _url string
	urls := strings.Split(c.CreateClientInfo.URL, constants.ConstAddressSeparator)
	if len(urls) > 0 && urls[0] != "" {
		u, err := url.Parse(urls[0])
		if err == nil {
			_url = u.Host
		}
	} else if c.clientInfo.TcpURL != "" {
		_url = c.clientInfo.TcpURL
	} else if c.clientInfo.WebsocketURL != "" {
		_url = c.clientInfo.WebsocketURL
	}

	return strings.TrimSuffix(_url, ":"+strconv.Itoa(constants.ConstPort80))
}

// GetLoginResp 获取模拟登录的响应信息
func (c *Client) GetLoginResp() map[string]any {
	return maps.Clone(c.loginResp)
}

// GetUserInfo 获取用户基础信息
func (c *Client) GetUserInfo() map[string]any {
	m := make(map[string]any)
	_ = mapper.Mapper(&c.authInfo, &m)
	return m
}

func (c *Client) GetSyncKeys() []SyncKey {
	if c.refreshInfo != nil && c.refreshInfo.SyncKeys != nil {
		return *c.refreshInfo.SyncKeys.Load()
	}

	return []SyncKey{}
}

func (c *Client) Close() (err error) {
	c.closeOnce.Do(
		func() {
			if c.Enabled() {
				c.SetDisable()
			}

			if c.loggedIn {
				_ = c.Logout(context.TODO())
			}

			if c.tcpTransport != nil {
				err = multierror.Append(err, c.tcpTransport.Close())
			}

			if c.wsTransport != nil {
				err = multierror.Append(err, c.wsTransport.Close())
			}

			if c.grpcTransport != nil {
				if v := c.grpcTransport.Load(); v != nil {
					err = multierror.Append(err, v.Close())
				}
			}

			if c.httpTransport != nil {
				err = multierror.Append(err, c.httpTransport.Close())
			}
		},
	)

	return err
}

func (c *Client) NewRequest(req *clienttypes.CreateRequestReq) (clienttypes.IRequest, error) {
	if req.Variables != nil {
		// update information that needs to be refreshed regularly
		if c.refreshInfo != nil && c.refreshInfo.UnionToken != nil {
			req.Variables[keyOfTTUnionToken] = c.refreshInfo.UnionToken.Load()
		}
		if c.refreshInfo != nil && c.refreshInfo.WebSecureToken != nil {
			req.Variables[keyOfWebSecureToken] = c.refreshInfo.WebSecureToken.Load()
		}

		req.Step.Url = common.TemplateExecuteByString(req.Step.GetName(), req.Step.GetUrl(), req.Variables)
		req.Step.Headers = common.TemplateExecuteByMap[map[string]string](req.Step.GetHeaders(), req.Variables)
		req.Step.Body = common.TemplateExecuteByString(req.Step.GetName(), req.Step.GetBody(), req.Variables)
	}

	if thttp.IsHTTPMethod(req.Step.GetMethod()) {
		return c.newHTTPRequest(req.Step)
	} else {
		return c.newTCPOrWSOrGRPCRequest(req.Step)
	}
}

func (c *Client) getLogicOptions(md protoreflect.MethodDescriptor) tt.LogicOptions {
	c.cacheMutex.Lock()
	defer c.cacheMutex.Unlock()

	key := string(md.FullName())
	opts, ok := c.logicOptions.Get(key)
	if !ok {
		opts = tt.GetLogicOptions(md)
		c.logicOptions.Put(key, opts)
	}

	return opts
}

func (c *Client) newTCPOrWSOrGRPCRequest(step *commonpb.PerfCaseStepV2) (clienttypes.IRequest, error) {
	var (
		key = c.Key()
		pm  = c.CreateClientInfo.ProtoManager

		name    = step.GetName()
		method  = step.GetMethod()
		headers = step.GetHeaders()
		body    = utils.StringToByteSlice(step.GetBody())
		cmd     = step.GetCmd()
		// grpcPath = step.GetGrpcPath()

		md protoreflect.MethodDescriptor
		id protoreflect.MessageDescriptor
		od protoreflect.MessageDescriptor

		err error
	)

	if method == "" {
		return nil, errors.Errorf(
			"the method cannot be empty while creating tcp or websocket or grpc request, key: %s", key,
		)
	}

	// TCP or Websocket or gRPC
	md, err = pm.FindMethodDescriptorByName(method)
	if err != nil {
		return nil, err
	}
	id = md.Input()
	od = md.Output()

	opts := c.getLogicOptions(md)
	if cmd == 0 {
		cmd = opts.MethodOptions.ID
	}
	//if grpcPath == "" {
	//	grpcPath = opts.RequestMethod
	//}

	if opts.MethodOptions.Deprecated {
		c.Warnf("the method has been deprecated, key: %s, method: %s", key, method)
	}
	//if grpcPath != opts.OriginMethod {
	//	c.Debugf(
	//		"the method has different path, key: %s, method: %s, proto: %s, topology: %s",
	//		key, method, opts.OriginMethod, grpcPath,
	//	)
	//}
	//if grpcPath != opts.RequestMethod {
	//	opts.RequestMethod = grpcPath
	//}
	c.Debugf("the method options, key: %s, options: %s", key, jsonx.MarshalIgnoreError(opts))

	if body == nil {
		body = emptyReqBody
	}

	r := &Request{
		name:    name,
		cmd:     cmd,
		seq:     c.GetSeq(),
		raw:     body,
		handled: false,
	}

	if c.callByGRPC(cmd, opts.OriginMethod) {
		return c.newGRPCRequest(pm, r, md, id, od, metadata.New(headers), opts)
	}

	return c.newTCPOrWSRequest(pm, r, md, id, od, headers)
}

func (c *Client) newTCPOrWSRequest(
	pm *protobuf.ProtoManager, r *Request,
	md protoreflect.MethodDescriptor, id, od protoreflect.MessageDescriptor,
	headers map[string]string,
) (req *TCPOrWSRequest, err error) {
	if c.wsTransport != nil {
		r.addr = c.wsURL
	} else {
		r.addr = fmt.Sprintf("%s://%s", qetconstants.TCP, c.tcpURL)
	}

	// JSON -> PB（bytes）
	data := r.raw
	data = c.tryToSetBaseReq(data)
	data = c.tryToSetClientVersion(md, data)
	r.raw, err = pm.UnmarshalPB(string(id.FullName()), data)
	if err != nil {
		return nil, errors.Wrapf(
			err,
			"failed to unmarshal the request data, method: %s, cmd: %d, seq: %d",
			md.FullName(), r.cmd, r.seq,
		)
	}

	return &TCPOrWSRequest{
		Request:          r,
		methodDescriptor: md,
		inputDescriptor:  id,
		outputDescriptor: od,
		headers:          headers,
		data:             data,
	}, nil
}

func (c *Client) newGRPCRequest(
	pm *protobuf.ProtoManager, r *Request,
	md protoreflect.MethodDescriptor, id, od protoreflect.MessageDescriptor,
	headers metadata.MD, opts tt.LogicOptions,
) (req *GRPCRequest, err error) { //nolint:unparam
	r.addr = fmt.Sprintf("%s://%s", qetconstants.GRPC, c.grpcURL)

	req = &GRPCRequest{
		Request:             r,
		methodDescriptor:    md,
		inputDescriptor:     id,
		outputDescriptor:    od,
		headers:             headers,
		data:                r.raw,
		supportCommonStatus: true,
	}

	callOptions := make([]grpc.CallOption, 0, 1)
	if c.callWithGZIP(opts.OriginMethod) {
		callOptions = append(callOptions, grpc.UseCompressor(gzip.Name))
	}
	req.r = tgrpc.NewRequest(
		req.methodDescriptor, c.grpcRequestSupplier(pm, req),
		tgrpc.WithMethod(opts.RequestMethod), tgrpc.WithCallOptions(callOptions...),
	)

	return req, nil
}

func (c *Client) newHTTPRequest(step *commonpb.PerfCaseStepV2) (req *HTTPRequest, err error) { //nolint: unparam
	var (
		name    = step.GetName()
		url_    = thttp.BuildURL(c.httpBaseURL, step.GetUrl())
		method  = thttp.RequestMethod(step.GetMethod())
		headers = step.GetHeaders()
		body    = utils.StringToByteSlice(step.GetBody())
	)

	r := &Request{
		name:    name,
		seq:     c.GetSeq(),
		raw:     body,
		addr:    url_,
		handled: false,
	}

	headers_ := thttp.NewHeader(headers)

	req = &HTTPRequest{
		Request: r,
		url:     url_,
		method:  method,
		headers: headers_,
	}
	req.r = tfasthttp.NewRequest(
		tfasthttp.SetURL(url_),
		tfasthttp.SetMethod(method),
		tfasthttp.SetHeader(headers_),
		tfasthttp.SetBody(body),
	)

	return req, err
}

func (c *Client) keepalive() {
	key := c.Key()

	interval1 := heartbeatInterval
	if c.CreateClientInfo.HeartbeatConfig != nil && c.CreateClientInfo.HeartbeatConfig.GetInterval() > 0 {
		interval1 = time.Duration(c.CreateClientInfo.HeartbeatConfig.GetInterval()) * time.Second
	}
	heartbeatTicker := timewheel.NewTicker(interval1)
	defer heartbeatTicker.Stop()

	// [300, 480)
	interval2 := time.Duration(commonutils.RandomInt(5*60, 8*60)) * time.Second
	refreshTokenTicker := timewheel.NewTicker(interval2)
	defer refreshTokenTicker.Stop()

	// [180, 300)
	interval3 := time.Duration(commonutils.RandomInt(3*60, 5*60)) * time.Second
	refreshConfigTicker := timewheel.NewTicker(interval3)
	defer refreshConfigTicker.Stop()

	c.Debugf(
		"keepalive intervals, key: %s, heartbeat: %s, refresh_token: %s, refresh_config: %s",
		key, interval1.String(), interval2.String(), interval3.String(),
	)

	allowFunc := func(limiterKey string, timeout time.Duration) error {
		ctx, cancel := context.WithTimeout(context.Background(), timeout)
		defer cancel()

		if c.CreateClientInfo.RateLimiter != nil {
			ret, err := c.CreateClientInfo.RateLimiter.WaitForAllow(ctx, limiterKey)
			if err != nil {
				return err
			} else if ret != nil && ret.Allowed < 1 {
				return errorx.Errorf(
					errorx.ProhibitedBehavior,
					"cannot to send a keepalive request due to exceeding traffic limit, key: %s, limiter_key: %s",
					key, limiterKey,
				)
			}
		}

		return nil
	}

	for {
		select {
		case <-heartbeatTicker.C:
			if c.CreateClientInfo.HeartbeatConfig != nil {
				if err := allowFunc(c.CreateClientInfo.HeartbeatConfig.GetKey(), interval1); err != nil {
					c.Error(err)
					continue
				}
			}

			// 定时发送心跳同步请求
			threading.GoSafe(c.Heartbeat)
		case <-refreshTokenTicker.C:
			if err := allowFunc(c.refreshTokenKey, interval2); err != nil {
				c.Error(err)
				continue
			}

			// 定时发送刷新Token请求
			threading.GoSafe(c.RefreshToken)
		case <-refreshConfigTicker.C:
			if err := allowFunc(c.refreshConfigKey, interval3); err != nil {
				c.Error(err)
				continue
			}

			// 定时发送刷新Transport配置请求
			threading.GoSafe(c.RefreshConfig)
		case <-c.Context.Done():
			return
		case <-c.ExitCh:
			return
		}
	}
}

// Login 模拟登录
func (c *Client) Login(ctx context.Context) (err error) {
	defer func() {
		if err != nil {
			_ = c.Close()
		} else {
			c.loggedIn = true
		}
	}()

	key := c.Key()

	var (
		req_  clienttypes.IRequest
		resp_ clienttypes.IResponse
	)

	req_, err = c.NewRequest(
		&clienttypes.CreateRequestReq{
			Step: c.makeLoginReqStep(),
		},
	)
	if err != nil {
		return errors.Wrapf(err, "failed to create login request, key: %s", key)
	}

	select {
	case <-c.Context.Done():
		err = c.Context.Err()
	default:
		resp_, err = c.Send(ctx, req_)
		if err != nil {
			return errors.Wrapf(err, "failed to send login request, key: %s", key)
		}

		var respBody any
		switch resp := resp_.(type) {
		case *TCPOrWSResponse:
			if resp.err != nil {
				return resp.err
			} else if resp.ret1 != 0 || resp.ret2 != 0 {
				return errors.Errorf(
					"failed to auth login, key: %s, ret: %d(%s), svrRet: %d(%s)",
					key,
					resp.ret1, MessageFromCode(int(resp.ret1)),
					resp.ret2, MessageFromCode(int(resp.ret2)),
				)
			}

			respBody = resp.body
		case *GRPCResponse:
			if resp.err != nil {
				return resp.err
			} else if resp.status.Code() != codes.OK {
				return errors.Errorf(
					"failed to auth login, key: %s, code: %d, msg: %s",
					key, resp.status.Code(), resp.status.Message(),
				)
			} else if resp.ttCode != 0 {
				return errors.Errorf(
					"failed to auth login, key: %s, tt_code: %d, tt_msg %s", key, resp.ttCode, resp.ttMessage,
				)
			}

			respBody = resp.body
		default:
			return errors.Errorf(
				"unknown the type of auth login response, expected %T or %T, but got %T, key: %s",
				(*TCPOrWSResponse)(nil), (*GRPCResponse)(nil), resp_, key,
			)
		}

		c.Infof("the response data of auth login, key: %s, data: %s", key, jsonx.MarshalIgnoreError(respBody))
		err = c.parseLoginRespData(respBody)
		if err == nil {
			// 创建GRPC客户端
			c.newGRPCTransport()
			// 创建HTTP客户端
			c.newHTTPTransport()

			// 持续发送心跳
			threading.GoSafe(c.keepalive)
		}
	}

	return err
}

// makeLoginReqStep 构建登录请求数据
func (c *Client) makeLoginReqStep() *commonpb.PerfCaseStepV2 {
	headers := make(map[string]string, constants.ConstDefaultMakeMapSize)
	security.HandleHeaders(headers)

	var (
		method, grpcPath string
		data             map[string]any
	)
	if c.clientInfo.ClientType == ConstClientTypePCLFG {
		method = methodFullNameOfSignIn
		grpcPath = methodNameOfSignIn
		data = map[string]any{
			keyOfPasswordCredential: map[string]any{
				keyOfIdentifier: c.authInfo.Username,
				keyOfPassword:   c.authInfo.MD5Password,
			},
		}
	} else {
		method = methodFullNameOfAuth
		grpcPath = methodNameOfAuth
		data = map[string]any{
			keyOfBaseReq: map[string]any{
				keyOfAppID:    uint32(c.clientInfo.AppID),
				keyOfMarketID: uint32(c.clientInfo.MarketID),
			},
			keyOfUserPhone:        c.authInfo.Username,
			keyOfPwd:              c.authInfo.MD5Password,
			keyOfLoginType:        uint32(ConstLoginTypeManual),
			keyOfLoginAccountType: uint32(ConstLoginAccountTypeTTFuzzy),
		}
		if c.clientInfo.ClientType == ConstClientTypePCTT {
			// 对于PC端，`signature`字段为数字版本号AES加密
			data[keyOfSignature], _ = c.tcpOrWsProtocol.Encrypt(
				utils.StringToByteSlice(strconv.FormatInt(int64(c.metadata.clientVersion), 10)),
				ConstCryptAlgorithmAesDecryptWithPrivateKey,
			)
		}
	}

	step := &commonpb.PerfCaseStepV2{
		Name:    AuthMethodNameZH,
		Method:  method,
		Headers: headers,
		Body:    jsonx.MarshalToStringIgnoreError(data),

		Cmd:      cmdOfAuth,
		GrpcPath: grpcPath,
	}

	return step
}

// parseLoginRespData 解析登录响应数据
func (c *Client) parseLoginRespData(data any) error {
	key := c.Key()

	var ok bool
	c.loginResp, ok = data.(map[string]any)
	if !ok {
		return errors.Errorf(
			"invalid type of auth response data, expected: map[string]any, but got: %T, key: %s",
			data, key,
		)
	}

	if c.clientInfo.ClientType == ConstClientTypePCLFG {
		return c.parseSignInRespData()
	} else {
		return c.parseAuthRespData()
	}
}

func (c *Client) parseAuthRespData() error {
	key := c.Key()

	authInfo, ok := c.loginResp[keyOfAuthInfo]
	if !ok {
		return errors.Errorf(
			"the required field[%s] is missing from the login response, key: %s, data: %s",
			keyOfAuthInfo, key, jsonx.MarshalIgnoreError(c.loginResp),
		)
	}

	var ai AuthInfo
	m, ok := authInfo.(map[string]any)
	if !ok {
		if err := mapstructure.Decode(authInfo, &ai); err != nil {
			return errors.Wrapf(
				err, "failed to copy data to struct[%T], key: %s, data: %v", ai, key, authInfo,
			)
		}
	} else {
		if err := mapper.MapperMap(m, &ai); err != nil {
			return errors.Wrapf(
				err, "failed to copy data to struct[%T], key: %s, data: %v", ai, key, m,
			)
		}
	}

	ai.Username = c.authInfo.Username
	ai.Password = c.authInfo.Password
	ai.MD5Password = c.authInfo.MD5Password
	c.authInfo = ai

	// 设置需要定时刷新的对象
	if c.refreshInfo == nil {
		c.refreshInfo = newRefreshInfo()
	}
	c.refreshInfo.UnionToken.Store(c.authInfo.UnionToken)
	c.refreshInfo.UnionTokenExpiresIn.Store(c.authInfo.UnionTokenExpiresIn)
	c.refreshInfo.WebSecureToken.Store(c.authInfo.WebSecureToken)
	c.refreshInfo.SyncKeys.Store(&[]SyncKey{})
	c.refreshInfo.RefreshToken.Store("")
	c.refreshInfo.RefreshTokenExpiresIn.Store(0)
	c.refreshInfo.Checksum.Store("")

	if ok {
		transportConfig, ok1 := m[keyOfTransportConfigV2]
		if !ok1 {
			c.Warnf("the field[%s] is missing from auth info, key: %s, data: %v", keyOfTransportConfigV2, key, m)
			return nil
		}

		c.grpcInfo = GRPCInfo{}
		// 不能使用`mapper.MapperMap`这个方法，因为`GRPCInfo`中存在切片类型字段
		if err := mapstructure.Decode(transportConfig, &c.grpcInfo); err != nil {
			c.Errorf("failed to copy data to struct[%T], key: %s, error: %v", c.grpcInfo, key, err)
		}

		// 设置需要定时刷新的对象
		c.refreshInfo.GRPCBlackList.Store(&c.grpcInfo.GRPCBlackList)
		c.refreshInfo.GZIPWhiteList.Store(&c.grpcInfo.GZIPWhiteList)
		c.refreshInfo.Endpoints.Store(&c.grpcInfo.Endpoints)
	}

	return nil
}

func (c *Client) parseSignInRespData() error {
	key := c.Key()

	userInfo, ok := c.loginResp[keyOfUserInfo]
	if !ok {
		return errors.Errorf(
			"the required field[%s] is missing from the login response, key: %s, data: %s",
			keyOfUserInfo, key, jsonx.MarshalIgnoreError(c.loginResp),
		)
	}

	secureTokens, ok := c.loginResp[keyOfSecureTokens]
	if !ok {
		return errors.Errorf(
			"the required field[%s] is missing from the login response, key: %s, data: %s",
			keyOfSecureTokens, key, jsonx.MarshalIgnoreError(c.loginResp),
		)
	}

	var ai AuthInfo
	m, ok := userInfo.(map[string]any)
	if !ok {
		if err := mapstructure.Decode(userInfo, &ai); err != nil {
			return errors.Wrapf(
				err, "failed to copy data to struct[%T], key: %s, data: %v", ai, key, userInfo,
			)
		}
	} else {
		if err := mapper.MapperMap(m, &ai); err != nil {
			return errors.Wrapf(
				err, "failed to copy data to struct[%T], key: %s, data: %v", ai, key, m,
			)
		}
	}

	ai.Username = c.authInfo.Username
	ai.Password = c.authInfo.Password
	ai.MD5Password = c.authInfo.MD5Password
	c.authInfo = ai

	var sts SecureTokens
	m, ok = secureTokens.(map[string]any)
	if !ok {
		if err := mapstructure.Decode(secureTokens, &sts); err != nil {
			return errors.Wrapf(
				err, "failed to copy data to struct[%T], key: %s, data: %v", sts, key, secureTokens,
			)
		}
	} else {
		if err := mapper.MapperMap(m, &sts); err != nil {
			return errors.Wrapf(
				err, "failed to copy data to struct[%T], key: %s, data: %v", sts, key, m,
			)
		}
	}

	// 设置需要定时刷新的对象
	if c.refreshInfo == nil {
		c.refreshInfo = newRefreshInfo()
	}
	c.refreshInfo.UnionToken.Store(sts.AccessToken)
	c.refreshInfo.UnionTokenExpiresIn.Store(int64(sts.ExpiresIn))
	c.refreshInfo.WebSecureToken.Store("")
	c.refreshInfo.SyncKeys.Store(&[]SyncKey{})
	c.refreshInfo.RefreshToken.Store(sts.RefreshToken)
	c.refreshInfo.RefreshTokenExpiresIn.Store(int64(sts.RefreshTokenExpiresIn))
	c.refreshInfo.Checksum.Store("")
	c.refreshInfo.GRPCBlackList.Store(&GRPCBlackList{})
	c.refreshInfo.GZIPWhiteList.Store(&GZIPWhiteList{})
	c.refreshInfo.Endpoints.Store(&[]Endpoint{})

	return nil
}

// Logout 模拟登出
func (c *Client) Logout(ctx context.Context) error {
	c.Infof("auth logout, key: %s", c.Key())
	return nil
}

// Heartbeat 心跳
func (c *Client) Heartbeat() {
	var (
		key     = c.Key()
		failure = true
	)
	defer func() {
		if failure {
			// 失败则增加连续失败次数
			c.AddFailures()
		} else {
			// 成功则重置连续失败次数
			c.ResetFailures()
		}
	}()

	var (
		req_  clienttypes.IRequest
		resp_ clienttypes.IResponse
		err   error
	)

	req_, err = c.NewRequest(
		&clienttypes.CreateRequestReq{
			Step: c.makeHeartbeatReqStep(),
		},
	)
	if err != nil {
		c.Errorf("failed to new heartbeat request, key: %s, error: %v", key, err)
		return
	}

	select {
	case <-c.Context.Done():
	case <-c.ExitCh:
	default:
		ctx, cancel := context.WithTimeout(context.Background(), c.tcpOrWsPendingRequests.timeout)
		defer cancel()

		resp_, err = c.Send(ctx, req_)
		if err != nil {
			c.Errorf("failed to send heartbeat request, key: %s, error: %v", key, err)
			return
		}

		switch resp := resp_.(type) {
		case *TCPOrWSResponse:
			if resp.err != nil {
				c.Errorf("the error of sync heartbeat response, key: %s, error: %v", key, resp.err)
			} else if resp.ret1 != 0 || resp.ret2 != 0 {
				c.Errorf(
					"failed to sync heartbeat, key: %s, ret: %d(%s), svrRet: %d(%s)",
					key,
					resp.ret1, MessageFromCode(int(resp.ret1)),
					resp.ret2, MessageFromCode(int(resp.ret2)),
				)
			} else {
				failure = false
				// c.Debugf("the response data of heartbeat, key: %s, data: %s", key, jsonx.MarshalIgnoreError(resp.body))
			}
		case *GRPCResponse:
			if resp.err != nil {
				c.Errorf("the error of sync heartbeat response, key: %s, error: %v", key, resp.err)
			} else if resp.status.Code() != codes.OK {
				c.Errorf(
					"failed to sync heartbeat, key: %s, code: %d, msg: %s",
					key, resp.status.Code(), resp.status.Message(),
				)
			} else if resp.ttCode != 0 {
				c.Errorf(
					"failed to sync heartbeat, key: %s, tt_code: %d, tt_msg: %s", key, resp.ttCode, resp.ttMessage,
				)
			} else {
				failure = false
				// c.Debugf("the response data of heartbeat, key: %s, data: %s", key, jsonx.MarshalIgnoreError(resp.body))
			}
		default:
			c.Errorf(
				"unknown the type of sync heartbeat response, expected %T or %T, but got %T, key: %s",
				(*TCPOrWSResponse)(nil), (*GRPCResponse)(nil), resp_, key,
			)
		}
	}
}

// makeHeartbeatReqStep 构建心跳请求数据
func (c *Client) makeHeartbeatReqStep() *commonpb.PerfCaseStepV2 {
	headers := make(map[string]string, constants.ConstDefaultMakeMapSize)
	security.HandleHeaders(headers)

	data := map[string]any{
		keyOfBaseReq: map[string]any{
			keyOfAppID:    c.clientInfo.AppID,
			keyOfMarketID: c.clientInfo.MarketID,
		},
		keyOfCurrVer:     c.clientInfo.ClientVersion,
		keyOfCurrVerCode: c.metadata.clientVersion,
	}
	if c.clientInfo.ClientType == ConstClientTypePCTT {
		// 对于PC端，`curr_ver`字段为数字版本号AES加密
		data[keyOfCurrVer], _ = c.tcpOrWsProtocol.Encrypt(
			utils.StringToByteSlice(strconv.FormatInt(int64(c.metadata.clientVersion), 10)),
			ConstCryptAlgorithmAesDecryptWithPrivateKey,
		)
	}

	return &commonpb.PerfCaseStepV2{
		Name:    HeartbeatMethodNameZH,
		Method:  methodFullNameOfHeartbeat,
		Headers: headers,
		Body:    jsonx.MarshalToStringIgnoreError(data),

		Cmd:      cmdOfHeartbeat,
		GrpcPath: methodNameOfHeartbeat,
	}
}

// parseHeartbeatRespData 解析同步心跳响应数据
func (c *Client) parseHeartbeatRespData(data any) error {
	key := c.Key()

	r, ok := data.(map[string]any)
	if !ok {
		return errors.Errorf(
			"invalid heartbeat response data type, expected: map[string]any, but got: %T, key: %s",
			data, key,
		)
	}

	var resp CheckSyncKeyResp
	// 不能使用`mapper.MapperMap`这个方法，因为`CheckSyncKeyResp`中存在切片类型字段
	if err := mapstructure.Decode(r, &resp); err != nil {
		return errors.Errorf(
			"failed to decode the heartbeat response data, key: %s, data: %s, error: %+v",
			key, jsonx.MarshalIgnoreError(r), err,
		)
	}

	if c.refreshInfo == nil {
		c.refreshInfo = newRefreshInfo()
	}

	var oldToken_ string
	if c.refreshInfo.WebSecureToken == nil {
		c.refreshInfo.WebSecureToken = atomic.NewString(resp.WebSecureToken)
	} else {
		oldToken_ = c.refreshInfo.WebSecureToken.Swap(resp.WebSecureToken)
	}
	c.authInfo.WebSecureToken = resp.WebSecureToken
	c.Debugf("web secure token, key: %s, old: %s, new: %s", key, oldToken_, resp.WebSecureToken)

	var oldKeys_ []SyncKey
	if c.refreshInfo.SyncKeys == nil {
		c.refreshInfo.SyncKeys = atomic.NewPointer(&resp.SyncKeys)
	} else if old := c.refreshInfo.SyncKeys.Swap(&resp.SyncKeys); old != nil {
		oldKeys_ = *old
	}
	c.Debugf(
		"sync key list, key: %s, old: %s, new: %s",
		key, jsonx.MarshalIgnoreError(oldKeys_), jsonx.MarshalIgnoreError(resp.SyncKeys),
	)

	return nil
}

// RefreshToken 刷新Token
func (c *Client) RefreshToken() {
	var (
		key     = c.Key()
		failure = true
	)
	defer func() {
		if failure {
			// 失败则增加连续失败次数
			c.AddFailures()
		} else {
			// 成功则重置连续失败次数
			c.ResetFailures()
		}
	}()

	var (
		req_  clienttypes.IRequest
		resp_ clienttypes.IResponse
		err   error
	)

	req_, err = c.NewRequest(
		&clienttypes.CreateRequestReq{
			Step: c.makeRefreshTokenReqStep(),
		},
	)
	if err != nil {
		c.Errorf("failed to new refresh token request, key: %s, error: %v", key, err)
		return
	}

	select {
	case <-c.Context.Done():
	case <-c.ExitCh:
	default:
		ctx, cancel := context.WithTimeout(context.Background(), c.tcpOrWsPendingRequests.timeout)
		defer cancel()

		resp_, err = c.Send(ctx, req_)
		if err != nil {
			c.Errorf("failed to send refresh token request, key: %s, error: %v", key, err)
			return
		}

		switch resp := resp_.(type) {
		case *TCPOrWSResponse:
			if resp.err != nil {
				c.Errorf("the error of refresh token response, key: %s, error: %v", key, resp.err)
			} else if resp.ret1 != 0 || resp.ret2 != 0 {
				c.Errorf(
					"failed to refresh token, key: %s, ret: %d(%s), svrRet: %d(%s)",
					key,
					resp.ret1, MessageFromCode(int(resp.ret1)),
					resp.ret2, MessageFromCode(int(resp.ret2)),
				)
			} else {
				failure = false
				//c.Debugf(
				//	"the response data of refresh token, key: %s, data: %s", key, jsonx.MarshalIgnoreError(resp.body),
				//)
			}
		case *GRPCResponse:
			if resp.err != nil {
				c.Errorf("the error of refresh token response, key: %s, error: %v", key, resp.err)
			} else if resp.status.Code() != codes.OK {
				c.Errorf(
					"failed to refresh token, key: %s, code: %d, msg: %s",
					key, resp.status.Code(), resp.status.Message(),
				)
			} else if resp.ttCode != 0 {
				c.Errorf("failed to refresh token, key: %s, tt_code: %d, tt_msg: %s", key, resp.ttCode, resp.ttMessage)
			} else {
				failure = false
				//c.Debugf(
				//	"the response data of refresh token, key: %s, data: %s", key, jsonx.MarshalIgnoreError(resp.body),
				//)
			}
		default:
			c.Errorf(
				"unknown the type of refresh token response, expected: %T or %T, but got %T, key: %s",
				(*TCPOrWSResponse)(nil), (*GRPCResponse)(nil), resp_, key,
			)
		}
	}
}

// makeRefreshTokenReqStep 构建刷新Token请求数据
func (c *Client) makeRefreshTokenReqStep() *commonpb.PerfCaseStepV2 {
	headers := make(map[string]string, constants.ConstDefaultMakeMapSize)
	security.HandleHeaders(headers)

	if c.clientInfo.ClientType == ConstClientTypePCLFG {
		return &commonpb.PerfCaseStepV2{
			Name:    RefreshTokenMethodNameZH,
			Method:  methodFullNameOfExchangeToken,
			Headers: headers,
			Body: jsonx.MarshalToStringIgnoreError(
				map[string]any{
					keyOfRefreshToken: c.refreshInfo.RefreshToken.Load(),
				},
			),

			Cmd:      cmdOfRefreshToken,
			GrpcPath: methodNameOfExchangeToken,
		}
	} else {
		return &commonpb.PerfCaseStepV2{
			Name:    RefreshTokenMethodNameZH,
			Method:  methodFullNameOfRefreshToken,
			Headers: headers,
			Body: jsonx.MarshalToStringIgnoreError(
				map[string]any{
					keyOfBaseReq: map[string]any{
						keyOfAppID:    c.clientInfo.AppID,
						keyOfMarketID: c.clientInfo.MarketID,
					},
				},
			),

			Cmd:      cmdOfRefreshToken,
			GrpcPath: methodNameOfRefreshToken,
		}
	}
}

// parseRefreshTokenRespData 解析刷新Token响应数据
func (c *Client) parseRefreshTokenRespData(data any) error {
	key := c.Key()

	m, ok := data.(map[string]any)
	if !ok {
		return errors.Errorf(
			"invalid refresh token response data type, expected: map[string]any, but got: %T, key: %s",
			data, key,
		)
	}

	if c.clientInfo.ClientType == ConstClientTypePCLFG {
		c.parseExchangeTokenRespData(m)
	} else {
		c.parseRefreshUnionTokenRespData(m)
	}

	return nil
}

func (c *Client) parseRefreshUnionTokenRespData(data map[string]any) {
	key := c.Key()

	if v, ok := data[keyOfTTUnionToken]; ok {
		new_ := cast.ToString(v)
		old_ := c.refreshInfo.UnionToken.Swap(new_)
		c.authInfo.UnionToken = new_
		c.Infof("refresh union token, key: %s, old: %s, new: %s", key, old_, new_)
	}
	if v, ok := data[keyOfExpiresIn]; ok {
		// TODO: 待确定，一个是`expires at`，一个是`expires in`，可能需要做时间戳处理
		new_ := cast.ToInt64(v)
		old_ := c.refreshInfo.UnionTokenExpiresIn.Swap(new_)
		c.authInfo.UnionTokenExpiresIn = new_
		c.Debugf("refresh union token expires in, key: %s, old: %d, new: %d", key, old_, new_)
	}
}

func (c *Client) parseExchangeTokenRespData(data map[string]any) {
	key := c.Key()

	v, ok := data[keyOfSecureTokens]
	if !ok {
		return
	}

	var sts SecureTokens
	m, ok := v.(map[string]any)
	if !ok {
		if err := mapstructure.Decode(v, &sts); err != nil {
			c.Errorf("failed to copy data to struct[%T], key: %s, data: %v, error: %+v", sts, key, v, err)
			return
		}
	} else {
		if err := mapper.MapperMap(m, &sts); err != nil {
			c.Errorf("failed to copy data to struct[%T], key: %s, data: %v, error: %+v", sts, key, m, err)
			return
		}
	}

	if sts.AccessToken != "" {
		new_ := sts.AccessToken
		old_ := c.refreshInfo.UnionToken.Swap(new_)
		c.authInfo.UnionToken = new_
		c.Infof("refresh union token, key: %s, old: %s, new: %s", key, old_, new_)
	}
	if sts.ExpiresIn != 0 {
		new_ := sts.ExpiresIn
		old_ := c.refreshInfo.UnionTokenExpiresIn.Swap(int64(new_))
		c.authInfo.UnionTokenExpiresIn = int64(new_)
		c.Debugf("refresh union token expires in, key: %s, old: %d, new: %d", key, old_, new_)
	}
	if sts.RefreshToken != "" {
		new_ := sts.RefreshToken
		old_ := c.refreshInfo.RefreshToken.Swap(new_)
		c.Infof("refresh refresh token, key: %s, old: %s, new: %s", key, old_, new_)
	}
	if sts.RefreshTokenExpiresIn != 0 {
		new_ := sts.RefreshTokenExpiresIn
		old_ := c.refreshInfo.RefreshTokenExpiresIn.Swap(int64(new_))
		c.Debugf("refresh refresh token expires in, key: %s, old: %d, new: %d", key, old_, new_)
	}
}

// RefreshConfig 刷新配置
func (c *Client) RefreshConfig() {
	if c.clientInfo.ClientType == ConstClientTypePCLFG {
		return
	}

	failure := true
	defer func() {
		if failure {
			// 失败则增加连续失败次数
			c.AddFailures()
		} else {
			// 成功则重置连续失败次数
			c.ResetFailures()
		}
	}()

	key := c.Key()

	var (
		req_  clienttypes.IRequest
		resp_ clienttypes.IResponse
		err   error
	)

	req_, err = c.NewRequest(
		&clienttypes.CreateRequestReq{
			Step: c.makeRefreshConfigReqStep(),
		},
	)
	if err != nil {
		c.Errorf("failed to new refresh transport config request, key: %s, error: %v", key, err)
		return
	}

	select {
	case <-c.Context.Done():
	case <-c.ExitCh:
	default:
		ctx, cancel := context.WithTimeout(context.Background(), c.tcpOrWsPendingRequests.timeout)
		defer cancel()

		resp_, err = c.Send(ctx, req_)
		if err != nil {
			c.Errorf("failed to send refresh transport config request, key: %s, error: %v", key, err)
			return
		}

		switch resp := resp_.(type) {
		case *TCPOrWSResponse:
			if resp.err != nil {
				c.Errorf("the error of refresh transport config response, key: %s, error: %v", key, resp.err)
			} else if resp.ret1 != 0 || resp.ret2 != 0 {
				c.Errorf(
					"failed to refresh transport config, key: %s, ret: %d(%s), svrRet: %d(%s)",
					key,
					resp.ret1, MessageFromCode(int(resp.ret1)),
					resp.ret2, MessageFromCode(int(resp.ret2)),
				)
			} else {
				failure = false
				//c.Debugf(
				//	"the response data of refresh transport config, key: %s, data: %s",
				//	key, jsonx.MarshalIgnoreError(resp.body),
				//)
			}
		case *GRPCResponse:
			if resp.err != nil {
				c.Errorf("the error of refresh transport config response, key: %s, error: %v", key, resp.err)
			} else if resp.status.Code() != codes.OK {
				c.Errorf(
					"failed to refresh transport config, key: %s, code: %d, msg: %s",
					key, resp.status.Code(), resp.status.Message(),
				)
			} else if resp.ttCode != 0 {
				c.Errorf(
					"failed to refresh transport config, key: %s, tt_code: %d, tt_msg: %s",
					key, resp.ttCode, resp.ttMessage,
				)
			} else {
				failure = false
				//c.Debugf(
				//	"the response data of refresh transport config, key: %s, data: %s",
				//	key, jsonx.MarshalIgnoreError(resp.body),
				//)
			}
		default:
			c.Errorf(
				"unknown the type of refresh transport config response, expected %T or %T, but got %T, key: %s",
				(*TCPOrWSResponse)(nil), (*GRPCResponse)(nil), resp_, key,
			)
		}
	}
}

// makeRefreshConfigReqStep 构建刷新配置请求数据
func (c *Client) makeRefreshConfigReqStep() *commonpb.PerfCaseStepV2 {
	headers := make(map[string]string, constants.ConstDefaultMakeMapSize)
	security.HandleHeaders(headers)

	body := map[string]any{
		keyOfBaseReq: map[string]any{
			keyOfAppID:    c.clientInfo.AppID,
			keyOfMarketID: c.clientInfo.MarketID,
		},
		keyOfPreviousCheckSum: "",
	}
	if c.refreshInfo != nil && c.refreshInfo.Checksum != nil {
		body[keyOfPreviousCheckSum] = c.refreshInfo.Checksum.Load()
	}

	return &commonpb.PerfCaseStepV2{
		Name:    RefreshConfigMethodNameZH,
		Method:  methodFullNameOfRefreshConfig,
		Headers: headers,
		Body:    jsonx.MarshalToStringIgnoreError(body),

		Cmd:      cmdOfRefreshConfig,
		GrpcPath: methodNameOfRefreshConfig,
	}
}

// parseRefreshConfigRespData 解析刷新配置响应数据
func (c *Client) parseRefreshConfigRespData(data any) error {
	key := c.Key()

	r, ok := data.(map[string]any)
	if !ok {
		return errors.Errorf(
			"invalid refresh transport config response data type, expected: map[string]any, but got: %T, key: %s",
			data, key,
		)
	}

	v, ok := r[keyOfNewCheckSum]
	if !ok {
		return errors.Errorf(
			"the required field[%s] is missing from the refresh transport config response, key: %s, data: %s",
			keyOfNewCheckSum, key, data,
		)
	}

	new_ := cast.ToString(v)
	old_ := c.refreshInfo.Checksum.Swap(new_)
	c.Debugf("refresh checksum of transport config, key: %s, old: %s, new: %s", key, old_, new_)
	if strings.EqualFold(old_, new_) {
		c.Debugf("checksum values are the same, no need to update transport config, key: %s", key)
		return nil
	}

	if v, ok := r[keyOfTransportConfig]; ok {
		var grpcInfo GRPCInfo
		// 不能使用`mapper.MapperMap`这个方法，因为`GRPCInfo`中存在切片类型字段
		if err := mapstructure.Decode(v, &grpcInfo); err != nil {
			return errors.Errorf("failed to copy data to struct[%T], key: %s, error: %v", grpcInfo, key, err)
		}

		if !equal(
			c.refreshInfo.GRPCBlackList.Load().GetApiRules(),
			grpcInfo.GRPCBlackList.ApiRules,
			StringMatchEqualsFn,
			StringMatchHashFn,
		) {
			var oldVal GRPCBlackList
			if old_ := c.refreshInfo.GRPCBlackList.Swap(&grpcInfo.GRPCBlackList); old_ != nil {
				oldVal = *old_
			}
			c.grpcInfo.GRPCBlackList = grpcInfo.GRPCBlackList

			c.cacheMutex.Lock()
			c.callByGRPC_.Clear()
			c.cacheMutex.Unlock()

			c.Infof(
				"refresh gRPC black list of transport config, key: %s, old: %s, new: %s",
				key, jsonx.MarshalIgnoreError(oldVal), jsonx.MarshalIgnoreError(grpcInfo.GRPCBlackList),
			)
		}

		if !equal(
			c.refreshInfo.GZIPWhiteList.Load().GetApiRules(),
			grpcInfo.GZIPWhiteList.ApiRules,
			StringMatchEqualsFn,
			StringMatchHashFn,
		) {
			var oldVal GZIPWhiteList
			if old_ := c.refreshInfo.GZIPWhiteList.Swap(&grpcInfo.GZIPWhiteList); old_ != nil {
				oldVal = *old_
			}
			c.grpcInfo.GZIPWhiteList = grpcInfo.GZIPWhiteList

			c.cacheMutex.Lock()
			c.callWithGZIP_.Clear()
			c.cacheMutex.Unlock()

			c.Infof(
				"refresh gzip white list of transport config, key: %s, old: %s, new: %s",
				key, jsonx.MarshalIgnoreError(oldVal), jsonx.MarshalIgnoreError(grpcInfo.GZIPWhiteList),
			)
		}

		if !equal(*c.refreshInfo.Endpoints.Load(), grpcInfo.Endpoints, EndpointEqualsFn, EndpointHashFn) {
			if len(grpcInfo.Endpoints) > 0 {
				var oldVal []Endpoint
				if old_ := c.refreshInfo.Endpoints.Swap(&grpcInfo.Endpoints); old_ != nil {
					oldVal = *old_
				}
				c.grpcInfo.Endpoints = grpcInfo.Endpoints
				endpoint := grpcInfo.Endpoints[0]

				rebuild := true
				if c.clientInfo.GrpcURL != "" {
					// 创建客户端时指定了`gRPC URL`，则不重新创建`gRPC`客户端
					rebuild = false
				} else if c.grpcURL == endpoint.Address && c.grpcAuthority == endpoint.TlsConfig.Authority {
					// `gRPC URL`没有变化，则不重新创建`gRPC`客户端
					rebuild = false
				}
				if rebuild {
					c.grpcURL = endpoint.Address
					vv := c.grpcTransport.Swap(
						tgrpc.NewClient(
							c.grpcURL, tgrpc.ClientConf{
								Authority: endpoint.TlsConfig.Authority,
							},
						),
					)

					// 可能影响当前执行的请求
					_ = vv.Close()
				}

				c.Infof(
					"refresh endpoints of transport config, key: %s, old: %s, new: %s",
					key, jsonx.MarshalIgnoreError(oldVal), jsonx.MarshalIgnoreError(grpcInfo.Endpoints),
				)
			} else {
				c.Warnf(
					"endpoints of transport config is empty, key: %s, config: %s",
					key, jsonx.MarshalIgnoreError(grpcInfo),
				)
			}
		}
	}

	return nil
}

// Send 发送请求消息
func (c *Client) Send(ctx context.Context, req clienttypes.IRequest) (resp clienttypes.IResponse, err error) {
	return c.Call(ctx, req, c.Key(), c.ClientType(), c.send)
}

func (c *Client) send(ctx context.Context, req clienttypes.IRequest) (resp clienttypes.IResponse, err error) {
	key := c.Key()

	defer func() {
		if err == nil {
			c.afterSend(req, resp)
		}
	}()

	switch v := req.(type) {
	case *TCPOrWSRequest:
		resp, err = c.sendTCPOrWSRequest(ctx, v)
	case *GRPCRequest:
		resp, err = c.sendGRPCRequest(ctx, v)
	case *HTTPRequest:
		resp, err = c.sendHTTPRequest(ctx, v)
	default:
		err = errors.Errorf(
			"invalid request type, expected %T or %T or %T, but got %T, key: %s",
			(*TCPOrWSRequest)(nil), (*GRPCRequest)(nil), (*HTTPRequest)(nil), req, key,
		)
	}

	return resp, err
}

func (c *Client) sendGRPCRequest(ctx context.Context, req *GRPCRequest) (resp *GRPCResponse, err error) {
	key := c.Key()

	if c.grpcTransport == nil || c.grpcTransport.Load() == nil {
		return nil, errors.Errorf(
			"grpc transport is null, key: %s, method: %s, cmd: %d, seq: %d",
			key, req.methodDescriptor.FullName(), req.cmd, req.seq,
		)
	} else if req.methodDescriptor.IsStreamingClient() || req.methodDescriptor.IsStreamingServer() {
		return nil, errors.Errorf(
			"non-unary gRPC methods are not currently supported, key: %s, method: %s, cmd: %d, seq: %d",
			key, req.methodDescriptor.FullName(), req.cmd, req.seq,
		)
	}

	// 设置固定的请求头信息
	if c.refreshInfo != nil && c.refreshInfo.UnionToken != nil {
		req.headers.Set(reqHeaderKeyOfAuthorization, fmt.Sprintf("Bearer %s", c.refreshInfo.UnionToken.Load()))
	}
	req.headers.Set(reqHeaderKeyOfXTTClientBundleID, c.clientInfo.BundleID)
	req.headers.Set(reqHeaderKeyOfXTTMarket, strconv.FormatUint(uint64(c.clientInfo.MarketID), 10))
	req.headers.Set(reqHeaderKeyOfXTTClientVersion, strconv.FormatInt(int64(c.metadata.clientVersion), 10))
	req.headers.Set(reqHeaderKeyOfXTTDeviceID, c.metadata.deviceIDString)
	req.headers.Set(reqHeaderKeyOfXTTTerminalType, strconv.FormatUint(uint64(c.metadata.terminalType), 10))
	req.headers.Set(reqHeaderKeyOfXTTClientType, strconv.FormatUint(uint64(c.clientInfo.ClientType), 10))
	if req.supportCommonStatus {
		req.headers.Set(reqHeaderKeyOfXTTSupportCommonStatus, "true")
	}
	if traceID := trace.TraceIDFromContext(ctx); traceID != "" {
		req.headers.Set(reqHeaderKeyOfRequestID, traceID)
	}
	if c.clientInfo.SubEnvFlag != "" {
		req.headers.Set(reqHeaderKeyOfXQWTrafficMark, c.clientInfo.SubEnvFlag)
	}

	pm := c.CreateClientInfo.ProtoManager
	commonStatusMessage, err := pm.CreateMessage(messageFullNameOfCommonStatus)
	if err != nil {
		return nil, err
	}
	defer pm.PutMessage(commonStatusMessage)

	resp = &GRPCResponse{
		Response: &Response{
			ctx: ctx,
			cmd: req.cmd,
			seq: req.seq,
		},
		methodDescriptor:    req.methodDescriptor,
		outputDescriptor:    req.outputDescriptor,
		supportCommonStatus: req.supportCommonStatus,
		commonStatusMessage: commonStatusMessage,
	}
	resp.r = tgrpc.NewResponse(req.methodDescriptor, resp)

	if req.startedAt.IsZero() {
		req.startedAt = time.Now()
	}

	invokeCtx, cancel := context.WithTimeout(ctx, c.grpcWaitResponseTimeout)
	defer cancel()

	found := false
	if c.grpcTransport != nil {
		if v := c.grpcTransport.Load(); v != nil {
			found = true

			err = v.InvokeRPC(invokeCtx, req.r, req.headers, resp.r)
			err = resp.handle(err)
			if err != nil && !errors.Is(err, resp.err) {
				resp.err = err
			}

			if resp.err == nil && resp.ttCode != 0 {
				resp.err = errors.Errorf(
					"code: %d, message: %s, tt-code: %d, tt-message: %s",
					resp.status.Code(), resp.status.Message(), resp.ttCode, resp.ttMessage,
				)
			}

			resp.elapsed = time.Since(req.startedAt)
		}
	}
	if !found {
		return nil, errors.Errorf(
			"grpc transport is null, key: %s, method: %s, cmd: %d, seq: %d",
			key, req.methodDescriptor.FullName(), req.cmd, req.seq,
		)
	}

	return resp, nil
}

func (c *Client) sendHTTPRequest(ctx context.Context, req *HTTPRequest) (resp *HTTPResponse, err error) {
	key := c.Key()

	defer func() {
		if req != nil && req.r != nil {
			// the `AcquireRequest` function was called during `NewRequest`
			tfasthttp.ReleaseRequest(req.r)
		}
	}()

	if c.httpTransport == nil {
		return nil, errors.Errorf(
			"http transport is null, key: %s, url: %s, method: %s, seq: %d",
			key, req.url, req.method, req.seq,
		)
	} else if req.url == "" || req.seq == 0 {
		return nil, errors.Errorf("invalid request data, the url cannot be empty or seq cannot be zero, key: %s", key)
	}

	resp = &HTTPResponse{
		Response: &Response{
			method: req.method,
			cmd:    req.cmd,
			seq:    req.seq,
		},
		url: req.url,

		r: tfasthttp.AcquireResponse(),
	}
	defer func() {
		if resp.r != nil {
			tfasthttp.ReleaseResponse(resp.r)
		}
	}()

	// 设置固定的请求头信息
	if c.refreshInfo != nil && c.refreshInfo.UnionToken != nil {
		req.r.Header.Set(reqHeaderKeyOfAuthorization, fmt.Sprintf("Bearer %s", c.refreshInfo.UnionToken.Load()))
	}
	req.r.Header.Set(reqHeaderKeyOfXTTClientBundleID, c.clientInfo.BundleID)
	req.r.Header.Set(reqHeaderKeyOfXTTMarket, strconv.FormatUint(uint64(c.clientInfo.MarketID), 10))
	req.r.Header.Set(reqHeaderKeyOfXTTClientVersion, strconv.FormatInt(int64(c.metadata.clientVersion), 10))
	req.r.Header.Set(reqHeaderKeyOfXTTDeviceID, base64.StdEncoding.EncodeToString(c.metadata.deviceID))
	req.r.Header.Set(reqHeaderKeyOfXTTTerminalType, strconv.FormatUint(uint64(c.metadata.terminalType), 10))
	req.r.Header.Set(reqHeaderKeyOfXTTClientType, strconv.FormatUint(uint64(c.clientInfo.ClientType), 10))
	if traceID := trace.TraceIDFromContext(ctx); traceID != "" {
		req.headers.Set(reqHeaderKeyOfRequestID, traceID)
	}
	if c.clientInfo.SubEnvFlag != "" {
		req.headers.Set(reqHeaderKeyOfXQWTrafficMark, c.clientInfo.SubEnvFlag)
	}

	if req.startedAt.IsZero() {
		req.startedAt = time.Now()
	}

	err = c.httpTransport.Send(req.r, resp.r, c.httpWaitResponseTimeout)
	if err != nil {
		resp.err = err
	} else {
		resp.handle()
	}

	if resp.err == nil && resp.ttCode != 0 {
		resp.err = errors.Errorf(
			"status: %s[%d], tt-code: %d, tt-message: %s",
			resp.status, resp.statusCode, resp.ttCode, resp.ttMessage,
		)
	}

	resp.elapsed = time.Since(req.startedAt)

	return resp, nil
}

func (c *Client) afterSend(req clienttypes.IRequest, resp clienttypes.IResponse) {
	key := c.Key()

	switch v1 := req.(type) {
	case *TCPOrWSRequest:
		if v2, ok := resp.(*TCPOrWSResponse); ok && v2.ret1 == 0 && v2.ret2 == 0 {
			c.afterSendTCPOrWSRequest(v1, v2)
		}
	case *GRPCRequest:
		if v2, ok := resp.(*GRPCResponse); ok && v2.err == nil && v2.status.Code() == codes.OK && v2.ttCode == 0 {
			c.afterSendGRPCRequest(v1, v2)
		}
	case *HTTPRequest:
		if v2, ok := resp.(*HTTPResponse); ok {
			c.afterSendHTTPRequest(v1, v2)
		}
	default:
		c.Warnf(
			"unknown request type, expected %T or %T or %T, but got %T, key: %s",
			(*TCPOrWSRequest)(nil), (*GRPCRequest)(nil), (*HTTPRequest)(nil), req, key,
		)
	}
}

func (c *Client) afterSendTCPOrWSRequest(req *TCPOrWSRequest, resp *TCPOrWSResponse) {
	var err error

	switch req.methodDescriptor.FullName() {
	case methodFullNameOfHeartbeat:
		err = c.parseHeartbeatRespData(resp.body)
	case methodFullNameOfRefreshToken:
		err = c.parseRefreshTokenRespData(resp.body)
	case methodFullNameOfRefreshConfig:
		err = c.parseRefreshConfigRespData(resp.body)
	}

	if err != nil {
		c.Errorf(
			"failed to handle the request and response after send a request, method: %s, data: %v",
			req.methodDescriptor.FullName(), resp.body,
		)
	}
}

func (c *Client) afterSendGRPCRequest(req *GRPCRequest, resp *GRPCResponse) {
	var err error

	switch req.methodDescriptor.FullName() {
	case methodFullNameOfHeartbeat:
		err = c.parseHeartbeatRespData(resp.body)
	case methodFullNameOfRefreshToken, methodFullNameOfExchangeToken:
		err = c.parseRefreshTokenRespData(resp.body)
	case methodFullNameOfRefreshConfig:
		err = c.parseRefreshConfigRespData(resp.body)
	}

	if err != nil {
		c.Errorf(
			"failed to handle the request and response after send a request, method: %s, data: %v",
			req.methodDescriptor.FullName(), resp.body,
		)
	}
}

func (c *Client) afterSendHTTPRequest(_ *HTTPRequest, _ *HTTPResponse) {
}

// Recv 接收数据
func (c *Client) Recv(pkg []byte) {
	key := c.Key()

	req, resp, err := c.handleRecvData(pkg)
	if err != nil {
		c.Errorf("failed to unpack the response, key: %s, error: %+v", key, err)
	} else if resp != nil && resp.isPush {
		if resp.cmd == cmdOfKickOut {
			c.Warnf("receive a kick out message, key: %s, cmd: %d, seq: %d", key, resp.cmd, resp.seq)
			_ = c.Close()
		} else {
			c.Debugf("receive a push message, key: %s, cmd: %d, seq: %d", key, resp.cmd, resp.seq)
		}
	} else if resp != nil && req == nil {
		c.Warnf(
			"the request corresponding to the response could not be found, key: %s, cmd: %d, seq: %d, ret: %d(%s), svrRet: %d(%s)",
			key, resp.cmd, resp.seq,
			resp.ret1, MessageFromCode(int(resp.ret1)),
			resp.ret2, MessageFromCode(int(resp.ret2)),
		)
	}
}

// ParsePackage 解析数据包
func (c *Client) ParsePackage(buf []byte) (int, int) {
	return parsePackage(buf)
}

func (c *Client) callByGRPC(cmd uint32, method string) (flag bool) {
	if c.clientInfo.ClientType == ConstClientTypePCLFG {
		// PC极速版走`gRPC`的接口
		return true
	}
	if c.CreateClientInfo.Type == clientTypeTCP || // 客户端类型为`tt_tcp`
		(c.clientInfo.ClientType != ConstClientTypePCLFG && (cmd == cmdOfAuth || cmd == cmdOfHeartbeat || cmd == cmdOfRefreshToken)) || // 非PC极速版指定走`TCP`的接口
		c.grpcTransport == nil || c.grpcTransport.Load() == nil {
		return false
	}

	c.cacheMutex.RLock()
	v, ok := c.callByGRPC_.Get(method)
	c.cacheMutex.RUnlock()
	if ok {
		return v
	}
	defer func() {
		c.cacheMutex.Lock()
		defer c.cacheMutex.Unlock()

		c.callByGRPC_.Put(method, flag)
	}()

	if c.refreshInfo != nil && c.refreshInfo.GRPCBlackList != nil {
		for _, rule := range c.refreshInfo.GRPCBlackList.Load().GetApiRules() {
			switch rule.MatchType {
			case ConstStringMatchTypeExact, ConstStringMatchTypeAll:
				if method == rule.MatchValue {
					return false
				}
			case ConstStringMatchTypePrefix:
				if strings.HasPrefix(method, rule.MatchValue) {
					return false
				}
			default:
				continue
			}
		}
	}

	return true
}

func (c *Client) callWithGZIP(method string) (flag bool) {
	c.cacheMutex.RLock()
	v, ok := c.callWithGZIP_.Get(method)
	c.cacheMutex.RUnlock()
	if ok {
		return v
	}
	defer func() {
		c.cacheMutex.Lock()
		defer c.cacheMutex.Unlock()

		c.callWithGZIP_.Put(method, flag)
	}()

	if c.refreshInfo != nil && c.refreshInfo.GZIPWhiteList != nil {
		for _, rule := range c.refreshInfo.GZIPWhiteList.Load().GetApiRules() {
			switch rule.MatchType {
			case ConstStringMatchTypeExact, ConstStringMatchTypeAll:
				if method == rule.MatchValue {
					return true
				}
			case ConstStringMatchTypePrefix:
				if strings.HasPrefix(method, rule.MatchValue) {
					return true
				}
			default:
				continue
			}
		}
	}

	return false
}
