package clienttypes

import (
	"fmt"

	"github.com/spf13/cast"
	"google.golang.org/protobuf/reflect/protoreflect"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/utils"
)

func GetClientKeyByVariables(variables map[string]any) string {
	var (
		clientKey string
		err       error
	)

	for _, key := range []string{clientKeyOfKey, clientKeyOfAccount, clientKeyOfUsername, clientKeyOfUid} {
		val, ok := variables[key]
		if !ok {
			continue
		}

		if clientKey, err = cast.ToStringE(val); err == nil {
			return clientKey
		}
	}

	return utils.GenClientID()
}

func GetGRPCPathByMethodDescriptor(md protoreflect.MethodDescriptor) string {
	service, _ := md.Parent().(protoreflect.ServiceDescriptor)
	return fmt.Sprintf("/%s/%s", service.FullName(), md.Name())
}
