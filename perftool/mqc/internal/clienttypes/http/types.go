package http

import (
	"sync"

	http "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/transport/http/fasthttp"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/perftool/mqc/internal/clienttypes"
)

type (
	Client struct {
		*clienttypes.BasicClient

		transport *http.Client

		key        string
		clientInfo ClientInfo // 客户端信息

		closeOnce sync.Once
	}

	ClientInfo struct {
		URL                string `json:"url"`
		UserAgent          string `json:"user_agent"`
		InsecureSkipVerify bool   `json:"insecure_skip_verify"`
	}
)
