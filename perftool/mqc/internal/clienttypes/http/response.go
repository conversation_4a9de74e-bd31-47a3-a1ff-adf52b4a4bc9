package http

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"time"

	"github.com/zeromicro/go-zero/core/jsonx"
	"github.com/zeromicro/go-zero/core/trace"
	"github.com/zeromicro/go-zero/core/utils"

	tfasthttp "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/transport/http/fasthttp"

	commonpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/perftool/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/perftool/mqc/internal/clienttypes"
)

const headerValueApplicationJson = "application/json"

var (
	_ clienttypes.IResponse = (*Response)(nil)

	headerValueApplicationJsonBytes = []byte(headerValueApplicationJson)
)

type Response struct {
	r *tfasthttp.Response

	ctx     context.Context // 请求上下文
	url     string          // 请求消息的Url
	method  string          // 请求消息的Method
	seq     uint32          // 响应消息的序列号
	elapsed time.Duration   // 请求发起到响应接收的耗时
	err     error

	headers    http.Header // 响应消息的Header数据
	body       any         // 响应消息的Body数据
	statusCode int         // 响应消息的HTTP状态码
}

func (r *Response) Key() string {
	return fmt.Sprintf("url: %s, seq: %d", r.url, r.seq)
}

func (r *Response) Headers() *clienttypes.Headers {
	return &clienttypes.Headers{
		Header: r.headers.Clone(),
	}
}

func (r *Response) Body() any {
	return r.body
}

func (r *Response) Status() int32 {
	return int32(r.statusCode)
}

func (r *Response) Result() string {
	return fmt.Sprintf("status code: %d", r.statusCode)
}

func (r *Response) Error() error {
	return r.err
}

func (r *Response) Elapsed() time.Duration {
	return r.elapsed
}

func (r *Response) Extract(exports []*commonpb.PerfCaseStepV2_Export, output map[string]any) {
	body, ok := r.body.(map[string]any)
	if !ok {
		return
	}

	if body == nil || len(exports) == 0 {
		return
	}

	if err := common.ExtractByExportExpression(body, exports, output); err != nil {
		common.Errorf(
			"failed to extract variable from the response data, trace_id: %s, data: %s, error: %+v",
			trace.TraceIDFromContext(r.ctx), jsonx.MarshalIgnoreError(body), err,
		)
	}
}

func (r *Response) handle() {
	if r.r == nil {
		return
	}

	r.handleHeader()
	r.handleBody()
	r.handleStatus()
}

func (r *Response) handleHeader() {
	r.headers = make(http.Header)
	r.r.Header.VisitAll(
		func(k, v []byte) {
			r.headers.Set(utils.ByteSliceToString(k), utils.ByteSliceToString(v))
		},
	)
}

func (r *Response) handleBody() {
	var buf bytes.Buffer
	_ = r.r.BodyWriteTo(&buf)
	body := buf.Bytes()

	if len(body) == 0 {
		r.body = nil
		return
	}

	if bytes.Contains(bytes.ToLower(r.r.Header.ContentType()), headerValueApplicationJsonBytes) {
		var m map[string]any
		err := json.Unmarshal(body, &m) // without using json.Number
		if err == nil {
			r.body = m
			return
		}

		common.Errorf(
			"failed to unmarshal the response data, data: %s, req_id: %s, error: %v",
			body, trace.TraceIDFromContext(r.ctx), err,
		)
	}

	r.body = utils.ByteSliceToString(body)
}

func (r *Response) handleStatus() {
	r.statusCode = r.r.StatusCode()
}
