package http

import (
	"context"
	"crypto/tls"
	"net/url"
	"strconv"
	"strings"
	"time"

	"github.com/devfeel/mapper"
	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"
	"github.com/zeromicro/go-zero/core/lang"
	"github.com/zeromicro/go-zero/core/utils"

	qetconsts "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
	thttp "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/transport/http"
	tfasthttp "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/transport/http/fasthttp"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/perftool/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/perftool/mqc/internal/clienttypes"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/perftool/mqc/types"
)

const (
	productName    types.ProductName = "http"
	clientTypeHTTP                   = types.ClientType(productName)
)

var _ clienttypes.IClient = (*Client)(nil)

func ProductName() types.ProductName {
	return productName
}

func NewClient(
	ctx context.Context, exitCh <-chan lang.PlaceholderType, req *clienttypes.CreateClientReq,
) (clienttypes.IClient, error) {
	ci, err := parseCreateClientReq(req)
	if err != nil {
		return nil, errors.Errorf(
			"failed to create %s client, req: %s, error: %v", productName, jsonx.MarshalIgnoreError(req), err,
		)
	}

	c := &Client{
		BasicClient: clienttypes.NewBasicClient(ctx, exitCh, req),

		key:        clienttypes.GetClientKeyByVariables(req.Variables),
		clientInfo: ci,
	}

	c.transport = tfasthttp.NewClient(
		tfasthttp.ClientConf{
			BaseURL:      c.clientInfo.URL,
			IdleTimeout:  clienttypes.ClientIdleTimeout,
			ReadTimeout:  clienttypes.ClientReadTimeout,
			WriteTimeout: clienttypes.ClientWriteTimeout,
			TlsConfig: &tls.Config{
				InsecureSkipVerify: c.clientInfo.InsecureSkipVerify, //nolint: gosec
			},
		},
	)

	return c, nil
}

func parseCreateClientReq(req *clienttypes.CreateClientReq) (ci ClientInfo, err error) {
	if req.URL != "" {
		u, err := url.Parse(req.URL)
		if err != nil {
			return ci, err
		}

		if u.Scheme != string(qetconsts.HTTP) && u.Scheme != string(qetconsts.HTTPS) {
			u.Scheme = string(qetconsts.HTTP)
		}

		ci.URL = u.String()
	}

	ci.UserAgent = common.UserAgentOfPerfTool
	ci.InsecureSkipVerify = true
	if req.Variables != nil {
		if err = mapper.MapperMap(req.Variables, &ci); err != nil {
			return ci, err
		}
	}

	return ci, nil
}

// ProductName 获取客户端的产品名称
func (c *Client) ProductName() types.ProductName {
	return productName
}

// ClientType 获取客户端类型
func (c *Client) ClientType() types.ClientType {
	return clientTypeHTTP
}

func (c *Client) Key() string {
	return c.key
}

// Environment 获取客户端对应的环境（返回空字符串表示不区分环境）
func (c *Client) Environment() string {
	if c.clientInfo.URL == "" {
		return c.BasicClient.Environment()
	}

	u, err := url.Parse(c.clientInfo.URL)
	if err != nil {
		return c.BasicClient.Environment()
	}

	return strings.TrimSuffix(u.Host, ":"+strconv.Itoa(constants.ConstPort80))
}

func (c *Client) Close() (err error) {
	c.closeOnce.Do(
		func() {
			if c.Enabled() {
				c.SetDisable()
			}

			if c.transport != nil {
				err = c.transport.Close()
			}
		},
	)

	return err
}

func (c *Client) NewRequest(req *clienttypes.CreateRequestReq) (clienttypes.IRequest, error) {
	var (
		name    = req.Step.GetName()
		url_    = req.Step.GetUrl()
		method  = req.Step.GetMethod()
		headers = req.Step.GetHeaders()
		body    = req.Step.GetBody()
	)
	if req.Variables != nil {
		url_ = common.TemplateExecuteByString(name, req.Step.GetUrl(), req.Variables)
		headers = common.TemplateExecuteByMap[map[string]string](req.Step.GetHeaders(), req.Variables)
		body = common.TemplateExecuteByString(name, req.Step.GetBody(), req.Variables)
	}

	url_ = c.transport.BuildURL(url_)
	method = thttp.RequestMethod(method)
	headers_ := thttp.NewHeader(headers)
	body_ := utils.StringToByteSlice(body)
	r := tfasthttp.NewRequest(
		tfasthttp.SetURL(url_),
		tfasthttp.SetMethod(method),
		tfasthttp.SetHeader(headers_),
		tfasthttp.SetBody(body_),
	)
	r.Header.SetUserAgent(c.clientInfo.UserAgent)

	return &Request{
		r: r,

		name:    name,
		seq:     c.GetSeq(),
		url:     url_,
		method:  method,
		headers: headers_,
		raw:     body_,
	}, nil
}

func (c *Client) Send(ctx context.Context, req clienttypes.IRequest) (clienttypes.IResponse, error) {
	return c.Call(ctx, req, c.Key(), c.ClientType(), c.send)
}

func (c *Client) send(ctx context.Context, req clienttypes.IRequest) (clienttypes.IResponse, error) {
	key := c.Key()

	// logger := logx.WithContext(ctx)

	v, ok := req.(*Request)
	if !ok {
		return nil, errors.Errorf("invalid request type, expected %T, but got %T, key: %s", (*Request)(nil), req, key)
	}
	defer func() {
		if v.r != nil {
			// the `AcquireRequest` function was called during `NewRequest`
			tfasthttp.ReleaseRequest(v.r)
		}
	}()

	if c.transport == nil {
		return nil, errors.Errorf("http transport is null, key: %s, url: %s, seq: %d", key, v.url, v.seq)
	}

	resp := &Response{
		r:      tfasthttp.AcquireResponse(),
		ctx:    ctx,
		url:    v.url,
		method: v.method,
		seq:    v.seq,
	}
	defer func() {
		if resp.r != nil {
			tfasthttp.ReleaseResponse(resp.r)
		}
	}()

	if v.startedAt.IsZero() {
		v.startedAt = time.Now()
	}

	err := c.transport.Send(v.r, resp.r, clienttypes.WaiteRespTimeout)
	if err != nil {
		resp.err = err
	} else {
		resp.handle()
	}
	resp.elapsed = time.Since(v.startedAt)

	//logger.Debugf(
	//	"the response data of %q which is called by http, key: %s, data: %s",
	//	v.url, key, jsonx.MarshalIgnoreError(resp.body),
	//)

	return resp, nil
}
