package registry

import (
	"context"
	"strings"
	"sync"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/lang"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/perftool/mqc/internal/clienttypes"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/perftool/mqc/types"
)

var r = &Registry{
	cr: &clientRegistry{
		cache: make(map[types.ClientType]*clientCache),
	},
}

type (
	clientCache struct {
		newFunc clienttypes.NewClientFunc
		nullObj clienttypes.IClient
	}
	clientRegistry struct {
		cache       map[types.ClientType]*clientCache
		clientTypes []types.ClientType

		lock sync.RWMutex
	}

	Registry struct {
		cr *clientRegistry
	}
)

func Register(clientType types.ClientType, fn clienttypes.NewClientFunc, obj clienttypes.IClient) {
	r.cr.lock.Lock()
	defer r.cr.lock.Unlock()

	if _, ok := r.cr.cache[clientType]; !ok {
		r.cr.clientTypes = append(r.cr.clientTypes, clientType)
	}
	r.cr.cache[clientType] = &clientCache{
		newFunc: fn,
		nullObj: obj,
	}
}

func findByClientType(clientType types.ClientType) (*clientCache, error) {
	r.cr.lock.RLock()
	defer r.cr.lock.RUnlock()

	cache, ok := r.cr.cache[clientType]
	if !ok {
		return nil, errors.Errorf("the client type is not currently supported, client type: %s", clientType)
	}

	return cache, nil
}

func NewClient(
	ctx context.Context, exitCh <-chan lang.PlaceholderType, req *clienttypes.CreateClientReq,
) (clienttypes.IClient, error) {
	cache, err := findByClientType(req.Type)
	if err != nil {
		return nil, err
	}

	return cache.newFunc(ctx, exitCh, req)
}

func GetAllClientTypes() []types.ClientType {
	clientTypes := make([]types.ClientType, len(r.cr.clientTypes))
	copy(clientTypes, r.cr.clientTypes)
	return clientTypes
}

func GetClientTypeByProtocol(protocol pb.Protocol) (types.ClientType, error) {
	p := protobuf.GetEnumStringOf(protocol)
	for _, t := range r.cr.clientTypes {
		if strings.EqualFold(string(t), p) {
			return t, nil
		}
	}

	return "", errors.Errorf("not found client type by the protocol, protocol: %s", p)
}

// GetNullClient 获取空客户端
// 注意：这里返回的接口类型是一个空对象，请谨慎使用
func GetNullClient(clientType types.ClientType) clienttypes.IClient {
	cache, err := findByClientType(clientType)
	if err != nil {
		return nil
	}

	return cache.nullObj
}

// IsAuthClient 判断指定的客户端类型是否实现了`clienttypes.IAuth`接口
// 注意：这里返回的接口类型是一个空对象，请谨慎使用
func IsAuthClient(clientType types.ClientType) (clienttypes.IAuth, bool) {
	v, ok := GetNullClient(clientType).(clienttypes.IAuth)
	return v, ok
}

// IsMonitorClient 判断指定的客户端类型是否实现了`clienttypes.IMonitor`接口
// 注意：这里返回的接口类型是一个空对象，请谨慎使用
func IsMonitorClient(clientType types.ClientType) (clienttypes.IMonitor, bool) {
	v, ok := GetNullClient(clientType).(clienttypes.IMonitor)
	return v, ok
}
