package grpc

import (
	"context"
	"os"
	"path/filepath"
	"testing"

	"github.com/zeromicro/go-zero/core/jsonx"
	"github.com/zeromicro/go-zero/core/lang"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"

	commonpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/perftool/mqc/internal/clienttypes"
)

func TestClient_Send(t *testing.T) {
	goPath := os.Getenv("GOPATH")

	ctx := context.Background()
	exitCh := make(chan lang.PlaceholderType, 1)

	pm, err := protobuf.NewProtoManager(
		protobuf.WithProducts(
			protobuf.Product{
				Name:   "",
				Branch: "",
				Projects: []protobuf.Project{
					{
						Name:   "probe-backend",
						Branch: "",
						Path:   "../../../../../protos",
						ImportPaths: []string{
							filepath.Join(
								goPath, "/pkg/mod/github.com/envoyproxy/protoc-gen-validate@v1.0.4",
							),
							"../../../../../dep_protos/protovalidate/proto/protovalidate",
							"../../../../../../qet-backend-common/protos",
							"../../../../../../qet-backend-middleware/protos",
						},
					},
				},
			},
		),
	)
	if err != nil {
		t.Fatal(err)
	}

	c, err := NewClient(
		ctx, exitCh, &clienttypes.CreateClientReq{
			Type: clientTypeGRPC,
			URL:  "grpc://127.0.0.1:20211",
			Variables: map[string]any{
				"no_tls": true,
			},

			TaskID:       "task_id:1",
			ExecuteID:    "execute_id:1",
			ProtoManager: pm,
			RateLimiter:  nil,
		},
	)
	if err != nil {
		t.Fatal(err)
	}

	req, err := c.NewRequest(
		&clienttypes.CreateRequestReq{
			Step: &commonpb.PerfCaseStepV2{
				Name:   "搜索项目",
				Method: "manager.ProjectService.SearchProject",
				Headers: map[string]string{
					"user_info": "eyJhY2NvdW50IjoiYWRtaW4iLCJmdWxsX25hbWUiOiLotKjph4/lubPlj7Dns7vnu5/nrqHnkIblkZgiLCJkZXB0X25hbWUiOiLotKjph4/lubPlj7Dnu4QiLCJlbWFpbCI6ImF1dG90ZXN0QDUydHQuY29tIiwibW9iaWxlIjoiIiwiZGVwdF9pZCI6IiIsImZ1bGxfZGVwdF9uYW1lIjoi56CU5Y+R5pWI6IO96YOoIC0g6LSo6YeP5bmz5Y+w57uEIiwicGhvdG8iOiIiLCJlbmFibGVkIjp0cnVlfQ==",
				},
				Body: `{
    "condition": {
        "single": {
            "field": "name",
            "compare": "EQ",
            "other": {
                "value": "TT（正式）"
            }
        }
    },
    "pagination": {
        "current_page": 1,
        "page_size": 2
    }
}`,
				Exports: nil,
				Key:     "",
			},
		},
	)
	if err != nil {
		t.Fatal(err)
	}

	resp, err := c.Send(ctx, req)
	if err != nil {
		t.Fatal(err)
	}

	t.Logf("body: %s", jsonx.MarshalIgnoreError(resp.Body()))
}
