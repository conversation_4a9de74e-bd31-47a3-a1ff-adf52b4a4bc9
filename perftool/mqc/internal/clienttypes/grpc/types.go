package grpc

import (
	"sync"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/transport/grpc"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/perftool/mqc/internal/clienttypes"
)

type (
	Client struct {
		*clienttypes.BasicClient

		transport *grpc.Client

		key        string
		clientInfo ClientInfo // 客户端信息

		closeOnce sync.Once
	}

	ClientInfo struct {
		URL                string `json:"url"`
		Authority          string `json:"authority"`
		UserAgent          string `json:"user_agent"`
		NoTLS              bool   `json:"no_tls"`
		InsecureSkipVerify bool   `json:"insecure_skip_verify"`
	}
)
