package grpc

import (
	"context"
	"encoding/base64"
	"fmt"
	"strings"
	"time"

	"github.com/hashicorp/go-multierror"
	"github.com/zeromicro/go-zero/core/jsonx"
	"github.com/zeromicro/go-zero/core/trace"
	"google.golang.org/grpc/metadata"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/proto"
	"google.golang.org/protobuf/reflect/protoreflect"
	"google.golang.org/protobuf/types/dynamicpb"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
	tgrpc "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/transport/grpc"

	commonpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/perftool/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/perftool/mqc/internal/clienttypes"
)

var (
	_ clienttypes.IResponse = (*Response)(nil)
	_ tgrpc.ResponseHandler = (*Response)(nil)
)

type Response struct {
	r *tgrpc.Response

	ctx     context.Context // 请求上下文
	seq     uint32          // 响应消息的序列号
	elapsed time.Duration   // 请求发起到响应接收的耗时
	err     error

	methodDescriptor protoreflect.MethodDescriptor  // 方法描述
	outputDescriptor protoreflect.MessageDescriptor // 响应描述

	headers  metadata.MD    // 响应消息的Header数据
	trailers metadata.MD    // 响应消息的Trailer数据
	body     any            // 响应消息的Body数据
	status   *status.Status // 响应消息的gRPC状态码
}

func (r *Response) Key() string {
	return fmt.Sprintf("method: %s, seq: %d", r.methodDescriptor.FullName(), r.seq)
}

func (r *Response) Headers() *clienttypes.Headers {
	return &clienttypes.Headers{
		Header:  r.headers.Copy(),
		Trailer: r.trailers.Copy(),
	}
}

func (r *Response) Body() any {
	return r.body
}

func (r *Response) Status() int32 {
	return int32(r.status.Code())
}

func (r *Response) Result() string {
	return fmt.Sprintf("code: %d, message: %s", r.status.Code(), r.status.Message())
}

func (r *Response) Error() error {
	return r.err
}

func (r *Response) Elapsed() time.Duration {
	return r.elapsed
}

func (r *Response) Extract(exports []*commonpb.PerfCaseStepV2_Export, output map[string]any) {
	if r.body == nil || len(exports) == 0 {
		return
	}

	if err := common.ExtractByExportExpression(r.body, exports, output); err != nil {
		common.Errorf(
			"failed to extract variable from the response data, trace_id: %s, data: %s, error: %+v",
			trace.TraceIDFromContext(r.ctx), jsonx.MarshalIgnoreError(r.body), err,
		)
	}
}

func (r *Response) OnReceiveHeaders(md metadata.MD) {
	r.saveMetadata(header, md)
}

func (r *Response) OnReceiveResponse(m proto.Message) {
	if dm, ok := m.(*dynamicpb.Message); ok {
		r.body = protobuf.ParseOptions{
			UseProtoNames:  true,
			UseEnumNumbers: false,
		}.ParseMessage(dm)
	} else {
		mm := make(map[string]any)
		m.ProtoReflect().Range(
			func(fieldDescriptor protoreflect.FieldDescriptor, value protoreflect.Value) bool {
				key := string(fieldDescriptor.Name())
				if fieldDescriptor.HasJSONName() {
					key = fieldDescriptor.JSONName()
				}

				mm[key] = value.Interface()
				return true
			},
		)
		r.body = mm
	}
}

func (r *Response) OnReceiveTrailers(s *status.Status, md metadata.MD) {
	r.saveMetadata(trailer, md)
	r.saveStatus(s)
}

type mdType int

const (
	header mdType = iota + 1
	trailer
)

const binHdrSuffix = "-bin"

func (r *Response) saveMetadata(typ mdType, md metadata.MD) {
	var to metadata.MD
	switch typ {
	case header:
		if r.headers == nil {
			r.headers = make(metadata.MD, md.Len())
		}
		to = r.headers
	case trailer:
		if r.trailers == nil {
			r.trailers = make(metadata.MD, md.Len())
		}
		to = r.trailers
	default:
		return
	}

	for k, vs := range md {
		if k == "" || len(vs) == 0 {
			continue
		}

		for _, v := range vs {
			if strings.HasSuffix(k, binHdrSuffix) {
				v = base64.RawStdEncoding.EncodeToString([]byte(v))
			}

			to.Append(k, v)
		}
	}
}

func (r *Response) saveStatus(s *status.Status) {
	r.status = s

	err := s.Err()
	if err != nil && r.err != nil {
		r.err = multierror.Append(err, r.err)
	} else if err != nil {
		r.err = err
	}
}
