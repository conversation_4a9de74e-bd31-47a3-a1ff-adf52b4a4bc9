package clienttypes

import (
	"context"
	"testing"

	"github.com/zeromicro/go-zero/core/trace"
	"go.opentelemetry.io/otel"
	"go.opentelemetry.io/otel/sdk/resource"
	sdktrace "go.opentelemetry.io/otel/sdk/trace"
	semconv "go.opentelemetry.io/otel/semconv/v1.4.0"
	oteltrace "go.opentelemetry.io/otel/trace"
)

func TestNewTraceID(t *testing.T) {
	parentCtx := context.Background()

	opts := []sdktrace.TracerProviderOption{
		// Set the sampling rate based on the parent span to 100%
		sdktrace.WithSampler(sdktrace.ParentBased(sdktrace.TraceIDRatioBased(1))),
		// Record information about this application in a Resource.
		sdktrace.WithResource(resource.NewSchemaless(semconv.ServiceNameKey.String("test"))),
	}
	tp := sdktrace.NewTracerProvider(opts...)
	otel.SetTracerProvider(tp)
	defer func() {
		_ = tp.Shutdown(parentCtx)
	}()

	tracer1 := trace.TracerFromContext(nil)
	ctx1, span1 := tracer1.Start(context.TODO(), "test1", oteltrace.WithSpanKind(oteltrace.SpanKindClient))
	defer span1.End()
	t.Logf("trace_id1: %s", trace.TraceIDFromContext(ctx1))

	tracer2 := trace.TracerFromContext(ctx1)
	ctx2, span2 := tracer2.Start(context.TODO(), "test2", oteltrace.WithSpanKind(oteltrace.SpanKindClient))
	defer span2.End()
	t.Logf("trace_id2: %s", trace.TraceIDFromContext(ctx2))

	tracer3 := trace.TracerFromContext(ctx1)
	ctx3, span3 := tracer3.Start(context.TODO(), "test3", oteltrace.WithSpanKind(oteltrace.SpanKindClient))
	defer span3.End()
	t.Logf("trace_id3: %s", trace.TraceIDFromContext(ctx3))
}
