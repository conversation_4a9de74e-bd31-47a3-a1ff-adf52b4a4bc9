package clienttypes

import "time"

const (
	ClientIdleTimeout  = 5 * time.Minute
	ClientReadTimeout  = 3 * time.Second
	ClientWriteTimeout = 2 * time.Second
	ClientDialTimeout  = 5 * time.Second
	WaiteRespTimeout   = 5 * time.Second

	maxLenOfResult = 1024
	ellipsis       = "..."
)

const (
	clientKeyOfKey      = "key"
	clientKeyOfAccount  = "account"
	clientKeyOfUsername = "username"
	clientKeyOfUid      = "uid"
)
