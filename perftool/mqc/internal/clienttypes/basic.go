package clienttypes

import (
	"context"
	"reflect"
	"strconv"
	"time"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"
	"github.com/zeromicro/go-zero/core/lang"
	"github.com/zeromicro/go-zero/core/trace"
	oteltrace "go.opentelemetry.io/otel/trace"
	"go.uber.org/atomic"
	"google.golang.org/protobuf/types/known/structpb"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/perftool/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/perftool/metrics"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/perftool/mqc/types"
)

var nilResponseError = errors.New("got a nil response")

// BasicClient 自定义的`Client`的父类，由于各`Client`在不同的包中，所以公共的字段设置为导出字段
type BasicClient struct {
	*common.PerfTestLogger

	Context          context.Context
	ExitCh           <-chan lang.PlaceholderType
	CreateClientInfo *CreateClientReq // 创建客户端时的请求信息

	seq             *atomic.Uint32 // 序列号
	failures        *atomic.Uint32 // 连续失败次数
	disable         *atomic.Bool   // 生失效标记
	createdAt       *atomic.Time   // 创建时间
	lastRequestedAt *atomic.Time   // 最近一次业务请求时间（排除客户端自动发起的请求）
	numOfRequest    *atomic.Uint64 // 请求次数
}

func NewBasicClient(ctx context.Context, exitCh <-chan lang.PlaceholderType, req *CreateClientReq) *BasicClient {
	now := time.Now()
	return &BasicClient{
		PerfTestLogger: common.LoadGlobalLogger(),

		Context:          ctx,
		ExitCh:           exitCh,
		CreateClientInfo: req,

		seq:             atomic.NewUint32(0),
		failures:        atomic.NewUint32(0),
		disable:         atomic.NewBool(false),
		createdAt:       atomic.NewTime(now),
		lastRequestedAt: atomic.NewTime(now),
		numOfRequest:    atomic.NewUint64(0),
	}
}

// Environment 获取客户端对应的环境（返回空字符串表示不区分环境）
func (c *BasicClient) Environment() string {
	return ""
}

// GetSeq 获取序列号（每次调用都会自增）
func (c *BasicClient) GetSeq() uint32 {
	return c.seq.Add(1)
}

// Failures 获取客户端的连续失败次数
func (c *BasicClient) Failures() uint32 {
	return c.failures.Load()
}

// ResetFailures 重置客户端的连续失败次数
func (c *BasicClient) ResetFailures() {
	c.failures.Store(0)
}

// AddFailures 客户端的连续失败次数加一
func (c *BasicClient) AddFailures() uint32 {
	return c.failures.Add(1)
}

// Enabled 客户端是否生效
func (c *BasicClient) Enabled() bool {
	return !c.disable.Load()
}

// SetDisable 把客户端设置为无效
func (c *BasicClient) SetDisable() {
	c.disable.Store(true)
}

// CreatedAt 获取客户端创建时间
func (c *BasicClient) CreatedAt() time.Time {
	return c.createdAt.Load()
}

// LastRequestedAt 获取客户端最近一次业务请求时间
func (c *BasicClient) LastRequestedAt() time.Time {
	return c.lastRequestedAt.Load()
}

// NumOfRequest 获取客户端请求次数
func (c *BasicClient) NumOfRequest() uint64 {
	return c.numOfRequest.Load()
}

// Requested 客户端完成一次请求（更新最近一次业务请求时间、增加一次请求次数）
func (c *BasicClient) Requested() {
	c.lastRequestedAt.Store(time.Now())
	c.numOfRequest.Add(1)
}

// GetCustomInfo 获取客户端自定义信息
func (c *BasicClient) GetCustomInfo() *structpb.Struct {
	return nil
}

func (c *BasicClient) Call(
	ctx context.Context, req IRequest, key string, clientType types.ClientType, fn SendFunc,
) (resp IResponse, err error) {
	if !c.Enabled() {
		return nil, errors.Errorf("the client is disable and cannot send any more requests, key: %s", key)
	}

	// a new trace_id is created for each request
	tracer := trace.TracerFromContext(ctx)
	ctx, span := tracer.Start(
		context.Background(), string(clientType), oteltrace.WithSpanKind(oteltrace.SpanKindClient),
	)
	defer span.End()

	var (
		traceID = trace.TraceIDFromContext(ctx)

		taskID         = c.CreateClientInfo.TaskID
		executeID      = c.CreateClientInfo.ExecuteID
		name           = req.Name()
		protocol       = req.Protocol()
		path           = req.Path()
		grpcStatus     = ""
		httpStatus     = ""
		businessStatus = ""
	)

	defer func() {
		c.Debugf(
			"the request data of %s which is called by %s, key: %s, req_id: %s, %s, req_headers: %s, req_data: %s",
			name, protocol, key, traceID, req.Key(), jsonx.MarshalIgnoreError(req.Headers()), req.Data(),
		)

		if err != nil {
			c.Errorf(
				"failed to call %s by %s, key: %s, req_id: %s, %s, req_headers: %s, req_data: %s, error: %+v",
				name, protocol, key, traceID, req.Key(), jsonx.MarshalIgnoreError(req.Headers()), req.Data(), err,
			)
		} else if resp != nil && resp.Error() != nil {
			c.Errorf(
				"got an error from response of %s which is called by %s, key: %s, req_id: %s, %s, req_headers: %s, req_data: %s, %s, resp_headers: %s, resp_data: %s, error: %+v",
				name, protocol, key, traceID, req.Key(), jsonx.MarshalIgnoreError(req.Headers()), req.Data(),
				resp.Result(), jsonx.MarshalIgnoreError(resp.Headers()), jsonx.MarshalIgnoreError(resp.Body()),
				resp.Error(),
			)
		} else if resp != nil && err == nil {
			c.Debugf(
				"the response data of %s which is called by %s, key: %s, req_id: %s, %s, %s, resp_headers: %s, resp_data: %s",
				name, protocol, key, traceID, resp.Key(), resp.Result(), jsonx.MarshalIgnoreError(resp.Headers()),
				jsonx.MarshalIgnoreError(resp.Body()),
			)
		}
	}()

	resp, err = fn(ctx, req)
	if err != nil {
		metrics.RequestFailed(taskID, executeID, protocol, name, path)
		return resp, err
	} else {
		metrics.RequestSucceeded(taskID, executeID, protocol, name, path)
	}

	if rv := reflect.ValueOf(resp); rv.IsValid() && !rv.IsNil() {
		switch protocol {
		case string(constants.GRPC):
			grpcStatus = strconv.FormatInt(int64(resp.Status()), 10)
		case string(constants.HTTP), string(constants.HTTPS):
			httpStatus = strconv.FormatInt(int64(resp.Status()), 10)
		}

		if bm, ok := resp.(BusinessMetric); ok {
			businessStatus = strconv.FormatInt(int64(bm.BusinessStatus()), 10)
		}

		metrics.ResponseSucceeded(
			taskID, executeID, protocol, name, path, handleResult(resp.Result()), grpcStatus, httpStatus,
			businessStatus, resp.Elapsed().Milliseconds(),
		)
	} else {
		metrics.ResponseFailed(taskID, executeID, protocol, name, path)
		return nil, nilResponseError
	}

	return resp, nil
}

func handleResult(input string) (output string) {
	defer func() {
		if len(output) > maxLenOfResult {
			output = output[:maxLenOfResult] + ellipsis
		}
	}()

	output = common.MaskIPPort(input)
	return output
}
