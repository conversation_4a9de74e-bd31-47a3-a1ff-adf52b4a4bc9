package clienttypes

import (
	"context"
	"time"

	"github.com/zeromicro/go-zero/core/lang"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"

	commonpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/perftool/mqc/internal/limiter"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/perftool/mqc/types"
)

type SendFunc func(context.Context, IRequest) (IResponse, error)

// Transport is the interface that wraps the basic Send method.
// Must be implemented by custom Client.
type Transport interface {
	Send(context.Context, IRequest) (IResponse, error)
}

// Closer is the interface that wraps the basic Close method.
// Must be implemented by custom Client.
type Closer interface {
	Close() error
}

// IAuth is the interface about authN.
// Can optionally be implemented by custom Client.
type IAuth interface {
	// Login 登录
	Login(context.Context) error
	// Logout 登出
	Logout(context.Context) error
	// GetLoginResp 获取登录响应信息
	GetLoginResp() map[string]any
	// GetUserInfo 获取用户基础信息
	GetUserInfo() map[string]any
}

// IMonitor is the interface about monitor.
// Can optionally be implemented by custom Client.
type IMonitor interface {
	// RequiredAfterAuth 是否在登录后才能获取监控参数
	RequiredAfterAuth() bool
	// GetMonitorParams 获取监控参数
	GetMonitorParams(*types.GeneralConfig, *protobuf.ProtoManager, *commonpb.PerfCaseContentV2) MonitorParams
}

// ILimiter is the interface about limiter.
// Can optionally be implemented by custom Client.
type ILimiter interface {
	// GetRateLimits 获取限流配置
	GetRateLimits(commonpb.TargetEnvironment) []*RateLimit
}

// IClient is the interface about custom Client
type IClient interface {
	Transport
	Closer

	// ProductName 获取客户端的产品名称
	ProductName() types.ProductName
	// ClientType 获取客户端类型
	ClientType() types.ClientType
	// Key 获取唯一性键值（用于区分不同的客户端）
	Key() string
	// Environment 获取客户端对应的环境（返回空字符串表示不区分环境）
	Environment() string
	// CreatedAt 获取客户端创建时间
	CreatedAt() time.Time

	// Failures 获取客户端的连续失败次数
	Failures() uint32
	// ResetFailures 重置客户端的连续失败次数
	ResetFailures()
	// AddFailures 客户端的连续失败次数加一
	AddFailures() uint32
	// Enabled 客户端是否生效
	Enabled() bool
	// SetDisable 把客户端设置为无效
	SetDisable()
	// LastRequestedAt 获取客户端最近一次业务请求时间
	LastRequestedAt() time.Time
	// NumOfRequest 获取客户端请求次数
	NumOfRequest() uint64
	// Requested 客户端完成一次请求
	Requested()

	// NewRequest 创建请求，各客户端返回其对应的请求对象
	NewRequest(*CreateRequestReq) (IRequest, error)
}

// IRequest is the interface about custom Request.
// Must be implemented by custom Request.
type IRequest interface {
	// Key 获取唯一性键值（用于区分不同的请求）
	Key() string
	// Headers 获取请求头信息
	Headers() map[string][]string
	// Body 获取请求体信息
	Body() []byte
	// Target 获取目标对象（用于限流判断，如：`HTTP`的请求路径、`gRPC`的方法全名、...）
	Target() string
	// Name 获取请求名称
	Name() string
	// Protocol 获取请求协议
	Protocol() string
	// Host  获取请求域名
	Host() string
	// Path 获取请求路径
	Path() string
	// Data 获取请求数据
	Data() []byte
}

// IResponse is the interface about custom Response.
// Must be implemented by custom Response.
type IResponse interface {
	// Key 获取唯一性键值（用于区分不同的响应）
	Key() string
	// Headers 获取响应头信息
	Headers() *Headers
	// Body 获取响应体信息
	Body() any
	// Status 获取响应状态码
	Status() int32
	// Result 获取响应结果
	Result() string
	// Error 获取响应错误信息
	Error() error
	// Elapsed 获取响应耗时
	Elapsed() time.Duration
	// Extract 从响应数据中提取对应的值
	Extract([]*commonpb.PerfCaseStepV2_Export, map[string]any)
}

// BusinessMetric is the interface about business metric.
// Can optionally be implemented by custom Response.
type BusinessMetric interface {
	// BusinessStatus 获取业务状态码
	BusinessStatus() int32
}

type MonitorParams struct {
	Commands  []uint32 `json:"commands"`
	GRPCPaths []string `json:"grpc_paths"`
	Services  []string `json:"services"`
}

type RateLimit struct {
	ApiName  string                  `json:"api_name"`
	Interval uint32                  `json:"interval"`
	Limits   []*commonpb.RateLimitV2 `json:"limits"`
}

type Headers struct {
	Header  map[string][]string
	Trailer map[string][]string
}

type (
	NewClientFunc         func(context.Context, <-chan lang.PlaceholderType, *CreateClientReq) (IClient, error)
	RegisterNewClientFunc func(types.ClientType, NewClientFunc)
)

type CreateClientReq struct {
	Type      types.ClientType `json:"type"`
	URL       string           `json:"url"`
	Variables map[string]any   `json:"variables"`

	TaskID          string                                   `json:"task_id"`
	ExecuteID       string                                   `json:"execute_id"`
	HeartbeatConfig *commonpb.PerfRateLimits_HeartbeatConfig `json:"heartbeat_config"`
	ProtoManager    *protobuf.ProtoManager                   `json:"-"`
	RateLimiter     *limiter.RateLimiterV2                   `json:"-"`
}

type CreateRequestReq struct {
	Step      *commonpb.PerfCaseStepV2
	Variables map[string]any
}
