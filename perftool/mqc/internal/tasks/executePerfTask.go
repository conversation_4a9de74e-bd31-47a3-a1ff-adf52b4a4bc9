package tasks

import (
	"context"
	"os"
	"time"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"
	"github.com/zeromicro/go-zero/core/lang"
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/service"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworkerv2/base"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	commonpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/perftool/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/perftool/mqc/internal/controller"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/perftool/mqc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/perftool/mqc/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/perfworker/common/errorzh"
	reporterpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
)

var _ base.Handler = (*PerfTestTaskProcessor)(nil)

type PerfTestTaskProcessor struct {
	*common.PerfTestLogger

	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewPerfTestTaskProcessor(svcCtx *svc.ServiceContext) base.Handler {
	ctx := context.Background()
	return &PerfTestTaskProcessor{
		PerfTestLogger: common.NewPerfTestLogger(ctx),

		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (p *PerfTestTaskProcessor) ProcessTask(ctx context.Context, task *base.Task) ([]byte, error) {
	// notify the server that the consumer has received a perf task
	p.svcCtx.TasksChannel <- lang.Placeholder
	defer func() {
		// notify the server that the consumer has finished the perf task
		p.svcCtx.FinishChannel <- lang.Placeholder
	}()

	var (
		info = &commonpb.PerfTestTaskInfo{}

		c   *controller.Controller
		err error
	)
	defer func() {
		if err != nil {
			p.Errorf("failed to process the perf test task, error: %+v", err)
		}

		if err := p.sendResultToPerfWorker(info, err); err != nil {
			p.Errorf("failed to send perf_tool_result_feedback task, error: %+v", err)
		}
	}()

	if err = protobuf.UnmarshalJSON(task.Payload, info); err != nil {
		return []byte(constants.FAILURE), errors.Wrapf(
			errorx.Err(errorx.SerializationError, err.Error()),
			"failed to unmarshal the payload of perf test task, payload: %s, error: %+v",
			task.Payload, err,
		)
	}

	if info.GetExecuteMode() == commonpb.PerfTaskExecutionMode_BY_TIMES && info.GetTimes() == 1 {
		p.PerfTestLogger.DebugToInfo()
	}

	p.updateContext(ctx, info)
	p.Infof("the content of config: %s", jsonx.MarshalIgnoreError(p.svcCtx.Config))
	p.Infof("perf task info: %s", protobuf.MarshalJSONIgnoreError(info))

	if err = p.validateTaskInfo(info); err != nil {
		return []byte(constants.FAILURE), err
	}

	c, err = controller.NewController(p.ctx, p.svcCtx, info)
	if err != nil {
		return []byte(constants.FAILURE), err
	}

	if err = c.Run(); err != nil {
		return []byte(constants.FAILURE), err
	}

	if p.svcCtx.Config.Mode == service.ProMode {
		// waiting for the monitoring platform to collect the relevant metrics data of this perf test
		time.Sleep(common.ConstMetricCollectionInterval)
	}
	return []byte(constants.SUCCESS), nil
}

func (p *PerfTestTaskProcessor) updateContext(ctx context.Context, info *commonpb.PerfTestTaskInfo) {
	// generate a new context with task id
	p.ctx = logx.ContextWithFields(
		ctx,
		logx.Field(common.ConstLogFieldKeyOfTaskID, info.GetTaskId()),
		logx.Field(common.ConstLogFieldKeyOfExecuteID, info.GetExecuteId()),
		logx.Field(common.ConstLogFieldKeyOfExecuteMode, info.GetExecuteMode()),
	)
	p.PerfTestLogger.Logger = p.PerfTestLogger.Logger.WithContext(p.ctx)
	common.StoreGlobalLogger(p.PerfTestLogger)
}

func (p *PerfTestTaskProcessor) validateTaskInfo(info *commonpb.PerfTestTaskInfo) error {
	if info == nil {
		return errorx.Err(errorx.ValidateParamError, "the info of perf test task is null")
	}

	if err := info.ValidateAll(); err != nil {
		return errors.Wrapf(
			errorx.Err(errorx.ValidateParamError, err.Error()),
			"the info of pref test task does not pass parameter validation, info: %s, error: %+v",
			protobuf.MarshalJSONIgnoreError(info), err,
		)
	}

	if info.GetProtocol() == commonpb.Protocol_PROTOCOL_GRPC || info.GetProtocol() == commonpb.Protocol_PROTOCOL_TT {
		generalConfig := info.GetGeneralConfig()
		if generalConfig == nil {
			return errorx.Errorf(errorx.ValidateParamError, "the general configuration is null")
		}

		if generalConfig.GetBaseUrl() == "" {
			return errorx.Err(
				errorx.ValidateParamError,
				"when the protocol is `gRPC` or `TT`, the base url in the general configuration cannot be empty",
			)
		}
	}

	switch info.GetExecuteMode() {
	case commonpb.PerfTaskExecutionMode_BY_DURATION:
		if info.GetDuration() == 0 {
			return errorx.Err(
				errorx.ValidateParamError, "when executing a perf test task by duration, the duration cannot be zero",
			)
		} else if info.GetTimeout() < info.GetDuration() {
			return errorx.Err(
				errorx.ValidateParamError,
				"when executing a perf test task by duration, the timeout cannot be less than the duration",
			)
		}
	case commonpb.PerfTaskExecutionMode_BY_TIMES:
		if info.GetTimes() == 0 {
			return errorx.Err(
				errorx.ValidateParamError, "when executing a perf test task by times, the times cannot be zero",
			)
		}
	default:
		return errorx.Err(errorx.ValidateParamError, "the execute mode of perf test task is invalid")
	}

	return nil
}

func (p *PerfTestTaskProcessor) sendResultToPerfWorker(info *commonpb.PerfTestTaskInfo, err error) error {
	var errMsg *reporterpb.ErrorMessage
	if err != nil {
		errZh, ok := err.(errorzh.ErrZh)
		if !ok {
			errZh = types.ErrZhOthersFailed(err)
		}
		errMsg = &reporterpb.ErrorMessage{
			MessageEn: errZh.Error(),
			MessageZh: string(errZh.ErrorZh()),
		}
	}

	hostname, err := os.Hostname()
	if err != nil {
		return err
	}

	payload, err := jsonx.Marshal(&types.ResultFeedbackTaskInfo{
		TaskId:    info.GetTaskId(),
		ExecuteId: info.GetExecuteId(),
		PodName:   hostname,
		ErrMsg:    errMsg,
	})
	if err != nil {
		return err
	}

	task := base.NewTask(
		constants.MQTaskTypePerfWorkerToolResultFeedback, payload,
		base.WithMaxRetryOptions(0),
		base.WithRetentionOptions(time.Minute),
	)

	p.Infof("send the perf_tool_result_feedback task, playload: %s", payload)

	_, err = p.svcCtx.PerfWorkerProducer.Send(
		p.ctx, task, base.QueuePriorityDefault,
	)

	return err
}
