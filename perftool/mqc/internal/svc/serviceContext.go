package svc

import (
	"time"

	"github.com/zeromicro/go-zero/core/lang"
	"github.com/zeromicro/go-zero/core/stores/redis"

	consumerv2 "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworkerv2/consumer"
	producerv2 "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworkerv2/producer"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/perftool/common/zrpc/reporter"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/perftool/mqc/internal/config"
)

type ServiceContext struct {
	Config config.Config

	Redis *redis.Redis

	PerfReporter       *reporter.PerfReporterRPC
	Consumer           *consumerv2.Consumer
	PerfWorkerProducer *producerv2.Producer

	TasksChannel  chan lang.PlaceholderType // 接收消费者接收到任务的信号
	FinishChannel chan lang.PlaceholderType // 接收消费者完成任务的信号
	ExitChannel   chan lang.PlaceholderType // 接收退出信号
}

func NewServiceContext(c config.Config) *ServiceContext {
	redis.SetSlowThreshold(time.Second)

	return &ServiceContext{
		Config: c,

		Redis: redis.MustNewRedis(c.Redis, redis.WithDB(c.Redis.DB)),

		PerfReporter:       reporter.NewPerfReporterRPC(c.Reporter),
		Consumer:           consumerv2.NewConsumer(c.Consumer),
		PerfWorkerProducer: producerv2.NewProducer(c.PerfWorkerProducer),

		TasksChannel:  make(chan lang.PlaceholderType, 1),
		FinishChannel: make(chan lang.PlaceholderType, 1),
		ExitChannel:   make(chan lang.PlaceholderType),
	}
}
