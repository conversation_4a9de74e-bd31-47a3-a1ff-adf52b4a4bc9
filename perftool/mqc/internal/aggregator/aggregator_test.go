package aggregator

import (
	"context"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/zeromicro/go-zero/core/lang"
	"github.com/zeromicro/go-zero/core/stores/redis"
	"github.com/zeromicro/go-zero/core/threading"
)

func generator(aggr *Aggregator) {
	time.Sleep(3 * time.Second)
	for i := 0; i < 10; i++ {
		switch i % 3 {
		case 0:
			_ = aggr.IncrPassed()
		case 1:
			_ = aggr.IncrFailed()
		default:
			_ = aggr.IncrSkipped()
		}

		time.Sleep(100 * time.Millisecond)
	}
}

func TestAggregator(t *testing.T) {
	ctx := context.Background()
	exitCh := make(chan lang.PlaceholderType, 1)
	aggr, err := NewAggregator(
		ctx, exitCh, redis.MustNewRedis(
			redis.RedisConf{
				Host: "127.0.0.1:6379",
				Type: redis.NodeType,
				Pass: "",
				DB:   0,
			},
		), "people", 10, WithExpireTime(60),
	)
	assert.Nil(t, err)

	threading.GoSafe(
		func() {
			generator(aggr)
		},
	)

	select {
	// case <-ctx.Done():
	//	t.Logf("got a done signal from context")
	case <-aggr.Finished():
		if err = aggr.Err(); err != nil {
			t.Errorf("got an error from aggregator, error: %s", err)
		} else {
			total, _ := aggr.Total()
			t.Logf(
				"got a finish signal from aggregator, total: %d, elapsed: %dms", total, aggr.Elapsed().Milliseconds(),
			)
		}
	}
}

func TestAggregator_Timeout(t *testing.T) {
	ctx, cancel := context.WithTimeout(context.Background(), 3*time.Second)
	defer cancel()

	exitCh := make(chan lang.PlaceholderType, 1)
	aggr, err := NewAggregator(
		ctx, exitCh, redis.MustNewRedis(
			redis.RedisConf{
				Host: "127.0.0.1:6379",
				Type: redis.NodeType,
				Pass: "",
				DB:   0,
			},
		), "people", 10, WithExpireTime(60),
	)
	assert.Nil(t, err)

	threading.GoSafe(
		func() {
			generator(aggr)
		},
	)

	select {
	// case <-ctx.Done():
	//	t.Logf("got a done signal from context")
	case <-aggr.Finished():
		if err = aggr.Err(); err != nil {
			t.Errorf("got an error from aggregator, error: %s", err)
		} else {
			total, _ := aggr.Total()
			t.Logf(
				"got a finish signal from aggregator, total: %d, elapsed: %dms", total, aggr.Elapsed().Milliseconds(),
			)
		}
	}
}

func TestAggregator_Exit(t *testing.T) {
	ctx := context.Background()
	exitCh := make(chan lang.PlaceholderType, 1)
	aggr, err := NewAggregator(
		ctx, exitCh, redis.MustNewRedis(
			redis.RedisConf{
				Host: "127.0.0.1:6379",
				Type: redis.NodeType,
				Pass: "",
				DB:   0,
			},
		), "people", 10, WithExpireTime(60),
	)
	assert.Nil(t, err)

	threading.GoSafe(
		func() {
			generator(aggr)
		},
	)
	threading.GoSafe(
		func() {
			time.Sleep(3500 * time.Millisecond)
			close(exitCh)
		},
	)

	select {
	// case <-ctx.Done():
	//	t.Logf("got a done signal from context")
	case <-aggr.Finished():
		if err = aggr.Err(); err != nil {
			t.Errorf("got an error from aggregator, error: %s", err)
		} else {
			total, _ := aggr.Total()
			t.Logf(
				"got a finish signal from aggregator, total: %d, elapsed: %dms", total, aggr.Elapsed().Milliseconds(),
			)
		}
	}
}
