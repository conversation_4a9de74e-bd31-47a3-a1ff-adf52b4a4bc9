-- KEYS[1]: key of aggregator
-- ARGV[1]: target number of aggregator
-- ARGV[2]: expire time (seconds) of aggregator
-- ARGV[3]: field of aggregator, e.g. `passed`, `failed`, `skipped`
-- ARGV[4]: increment of field

local target = tonumber(ARGV[1])
local expire = tonumber(ARGV[2])
local field = tostring(ARGV[3])
local increment = tonumber(ARGV[4])

if redis.call("EXISTS", KEYS[1]) == 0 then
	redis.call("HSET", KEYS[1],
               "target", target,
               "passed", 0,
               "failed", 0,
               "skipped", 0)
else
    local src = redis.call("HGET", KEYS[1], "target")
    if tonumber(src) ~= target then
        return redis.error_reply("ERR the target number of aggregator is not match, src: " .. src .. ", dst: " .. tostring(target))
    end
end

redis.call("HINCRBY", KEYS[1], field, increment)
redis.call("EXPIRE", KEYS[1], expire)
return redis.status_reply("OK")
