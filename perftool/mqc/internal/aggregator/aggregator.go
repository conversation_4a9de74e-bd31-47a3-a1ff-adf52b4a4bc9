package aggregator

import (
	"context"
	_ "embed"
	"fmt"
	"strconv"
	"sync"
	"sync/atomic"
	"time"

	"github.com/pkg/errors"
	"github.com/spf13/cast"
	"github.com/zeromicro/go-zero/core/lang"
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/stores/redis"
	"github.com/zeromicro/go-zero/core/syncx"
	"github.com/zeromicro/go-zero/core/threading"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/redisscript"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/timewheel"
)

const (
	keyPrefix = "aggregator:"

	expireTime    = 60 * 60 // 3600 seconds
	checkInterval = time.Second
	errTimes      = 3
	maxErrTimes   = 100

	fieldOfTarget  = "target"
	fieldOfPassed  = "passed"
	fieldOfFailed  = "failed"
	fieldOfSkipped = "skipped"
)

var (
	//go:embed increaseCmd.lua
	increaseLuaScript string
	increaseCmd       = redisscript.NewScript(increaseLuaScript)
)

type (
	Aggregator struct {
		logx.Logger
		ctx    context.Context
		exitCh <-chan lang.PlaceholderType
		rdb    *redis.Redis

		key    string
		target uint64

		expire   uint64
		interval time.Duration
		errTimes uint64

		startOnce sync.Once
		startedAt *atomic.Pointer[time.Time]
		closeOnce sync.Once
		closed    *atomic.Bool
		elapsed   time.Duration
		err       error

		finishCh     chan lang.PlaceholderType
		singleFlight syncx.SingleFlight
	}

	Info struct {
		Target  uint64 `json:"target"`
		Passed  uint64 `json:"passed"`
		Failed  uint64 `json:"failed"`
		Skipped uint64 `json:"skipped"`
	}

	Option func(*Aggregator)
)

func WithExpireTime(expire uint64) Option {
	return func(a *Aggregator) {
		if expire != 0 {
			a.expire = expire
		}
	}
}

func WithCheckInterval(interval time.Duration) Option {
	return func(a *Aggregator) {
		if interval >= 100*time.Millisecond && interval <= time.Minute {
			a.interval = interval
		}
	}
}

func WithConsecutiveErrTimes(times uint64) Option {
	return func(a *Aggregator) {
		if times > 0 && times <= maxErrTimes {
			a.errTimes = times
		}
	}
}

func NewAggregator(
	ctx context.Context, exitCh <-chan lang.PlaceholderType, rdb *redis.Redis, key string, target uint64,
	opts ...Option,
) (*Aggregator, error) {
	a := &Aggregator{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		exitCh: exitCh,
		rdb:    rdb,

		key:    fmt.Sprintf("%s{%s}", keyPrefix, key),
		target: target,

		expire:   expireTime,
		interval: checkInterval,
		errTimes: errTimes,

		startedAt: new(atomic.Pointer[time.Time]),
		closed:    new(atomic.Bool),

		finishCh:     make(chan lang.PlaceholderType, 1),
		singleFlight: syncx.NewSingleFlight(),
	}
	for _, opt := range opts {
		opt(a)
	}

	threading.GoSafeCtx(ctx, a.watch)
	return a, nil
}

func (a *Aggregator) start() {
	a.startOnce.Do(
		func() {
			if s := a.startedAt.Load(); s == nil || (*s).IsZero() {
				now := time.Now()
				a.startedAt.Store(&now)
			}
		},
	)
}

func (a *Aggregator) close() {
	a.closeOnce.Do(
		func() {
			close(a.finishCh)
			a.closed.Store(true)

			if s := a.startedAt.Load(); s != nil && !(*s).IsZero() {
				a.elapsed = time.Since(*s)
				a.Infof(
					"closing the aggregator, key: %s, started_at: %s, elapsed: %s",
					a.key, (*s).Format(time.DateTime), a.elapsed,
				)
			}
		},
	)
}

func (a *Aggregator) watch() {
	var (
		ticker = timewheel.NewTicker(a.interval)

		times uint64 = 0
	)
	defer ticker.Stop()
	defer a.close()

	for {
		select {
		case <-a.exitCh:
			a.Warnf("got an exit signal while watching the aggregator, key: %s", a.key)
			return
		case <-a.ctx.Done():
			a.Debugf("got a done signal while watching the aggregator, key: %s", a.key)
			return
		case <-ticker.C:
			if s := a.startedAt.Load(); s == nil || (*s).IsZero() {
				continue
			}

			info, err := a.get()
			if err != nil {
				a.Error(err.Error())
				times += 1
				if times >= a.errTimes {
					a.Errorf("the aggregator reached the max consecutive error times, key: %s, times: %d", a.key, times)
					a.err = err
					return
				}

				continue
			} else if times != 0 {
				times = 0
			}

			if info.Passed+info.Failed+info.Skipped >= a.target {
				a.Infof(
					"the aggregator reached the target value, key: %s, target: %d, passed: %d, failed: %d, skipped: %d",
					a.key, a.target, info.Passed, info.Failed, info.Skipped,
				)
				return
			}
		}
	}
}

func (a *Aggregator) IncrPassed() error {
	return a.incr(fieldOfPassed, 1)
}

func (a *Aggregator) IncrFailed() error {
	return a.incr(fieldOfFailed, 1)
}

func (a *Aggregator) IncrSkipped() error {
	return a.incr(fieldOfSkipped, 1)
}

func (a *Aggregator) incr(field string, increment int64) error {
	if a.closed.Load() {
		a.Debugf(
			"the aggregator has been closed, cannot to increase the number of %q, key: %s", a.key, field,
		)
		return nil
	}

	defer a.start()

	val, err := increaseCmd.Execute(a.ctx, a.rdb, []string{a.key}, a.target, a.expire, field, increment)
	if err != nil {
		return errors.Wrapf(
			errorx.Err(errorx.EvalRedisScriptFailure, err.Error()),
			"failed to execute redis script for increasing the number of %q, key: %s, error: %+v",
			field, a.key, err,
		)
	}

	result := cast.ToInt64(val)
	if result == 0 {
		return errorx.Errorf(errorx.NotExists, "the aggregator does not exist, key: %s", a.key)
	}

	return nil
}

func (a *Aggregator) Target() uint64 {
	return a.target
}

func (a *Aggregator) Passed() (uint64, error) {
	info, err := a.get()
	if err != nil {
		return 0, err
	}

	return info.Passed, nil
}

func (a *Aggregator) Failed() (uint64, error) {
	info, err := a.get()
	if err != nil {
		return 0, err
	}

	return info.Failed, nil
}

func (a *Aggregator) Skipped() (uint64, error) {
	info, err := a.get()
	if err != nil {
		return 0, err
	}

	return info.Skipped, nil
}

func (a *Aggregator) Total() (uint64, error) {
	info, err := a.get()
	if err != nil {
		return 0, err
	}

	return info.Passed + info.Failed + info.Skipped, nil
}

func (a *Aggregator) get() (*Info, error) {
	// requests within the same second are returned via SingleFlight
	key := strconv.FormatInt(time.Now().Unix(), 10)
	val, err := a.singleFlight.Do(
		key, func() (any, error) {
			m, err := a.rdb.HgetallCtx(a.ctx, a.key)
			if err != nil {
				return nil, errors.Wrapf(
					errorx.Err(errorx.DBError, err.Error()),
					"failed to execute the redis hgetall command, key: %s, error: %+v", a.key, err,
				)
			}

			return &Info{
				Target:  cast.ToUint64(m[fieldOfTarget]),
				Passed:  cast.ToUint64(m[fieldOfPassed]),
				Failed:  cast.ToUint64(m[fieldOfFailed]),
				Skipped: cast.ToUint64(m[fieldOfSkipped]),
			}, nil
		},
	)
	if err != nil {
		return nil, err
	}

	v, ok := val.(*Info)
	if !ok {
		return nil, errorx.Errorf(
			errorx.InternalError,
			"the type of the value returned by SingleFlight does not match the expected type, expected: %T, but got: %T",
			(*Info)(nil), val,
		)
	}

	return v, nil
}

func (a *Aggregator) Finished() <-chan lang.PlaceholderType {
	return a.finishCh
}

func (a *Aggregator) Elapsed() time.Duration {
	return a.elapsed
}

func (a *Aggregator) Err() error {
	return a.err
}
