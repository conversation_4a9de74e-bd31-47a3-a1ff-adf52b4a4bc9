package config

import (
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/service"
	"github.com/zeromicro/go-zero/core/stores/redis"
	"github.com/zeromicro/go-zero/zrpc"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/security"

	consumerv2 "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworkerv2/consumer"
	producerv2 "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworkerv2/producer"
)

type Config struct {
	service.ServiceConf

	Redis redis.RedisConf

	Reporter           zrpc.RpcClientConf
	Consumer           consumerv2.Config
	PerfWorkerProducer producerv2.Config
	Task               TaskInfo        `json:",optional"`
	Security           security.Config `json:",optional"`
}

func (c Config) LogConfig() logx.LogConf {
	return c.ServiceConf.Log
}

func (c Config) ListenOn() string {
	return constants.NoNeedToListen
}

type TaskInfo struct {
	TypeName        string // 任务类型名称
	TaskID          string `json:",optional"` // 任务ID
	ExecuteID       string `json:",optional"` // 压测用例执行ID
	UseGlobalLimit  bool   `json:",optional"` // 是否使用全局限流
	UseLocalLimiter bool   `json:",optional"` // 是否使用本地限流器
}
