package limiter

import (
	"context"
	"math"
	"sync"
	"time"

	"github.com/zeromicro/go-zero/core/lang"
	"github.com/zeromicro/go-zero/core/logx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/cache"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/types"
)

var _ Limiter = (*LocalRateLimiterV2)(nil)

const (
	DefaultStepNumber = 8
)

type (
	LocalRateLimiterV2 struct {
		logx.Logger
		ctx    context.Context
		exitCh <-chan lang.PlaceholderType

		cache map[string]*localItemV2
	}

	localItemV2 struct {
		key          string
		targetRPS    int64
		initialRPS   int64
		currentRPS   int64
		stepHeight   int64
		stepDuration time.Duration

		lastRate   int64
		lastUpdate int64
		tat        int64
		mu         sync.RWMutex

		RateLimits []types.RateLimitV2

		startTimestamp   int64 // 当前阶段开始的微秒时间戳
		currentRateLimit types.RateLimitV2
	}
)

func NewLocalRateLimiterV2(
	ctx context.Context, exitCh <-chan lang.PlaceholderType, configs []ConfigV2,
) *LocalRateLimiterV2 {
	items := make(map[string]*localItemV2, len(configs))
	for _, config := range configs {
		if config.Key != "" {
			item := &localItemV2{
				key:        config.Key,
				RateLimits: config.RateLimits,
			}

			// 对第一个阶梯的参数初始化到item中
			if len(config.RateLimits) >= 1 {
				initStepRateLimitConfig(item, config.RateLimits[0])
			}

			items[config.Key] = item
		}
	}

	return &LocalRateLimiterV2{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		exitCh: exitCh,

		cache: items,
	}
}

func (l *LocalRateLimiterV2) SetExpireTime(_ int64) {
}

func (l *LocalRateLimiterV2) BlockTake(ctx context.Context, key string) (*Result, error) {
	return l.Take(ctx, key, true)
}

func (l *LocalRateLimiterV2) Take(ctx context.Context, key string, blocked bool) (*Result, error) {
	item, ok := l.cache[key]
	if !ok {
		// no need for rate limiting
		return nil, nil
	}

	for {
		select {
		case <-l.exitCh:
			l.Warnf("got an exit signal while waiting to pass through the rate limiter, key: %s", key)
			return nil, ExitSignalError
		case <-l.ctx.Done():
			l.Debugf("got a done signal while waiting to pass through the rate limiter, key: %s", key)
			return nil, DoneSignalError
		case <-ctx.Done():
			return nil, ctx.Err()
		default:
			result := l.allow(ctx, key, item, 1)
			if (result != nil && result.Allowed >= 1) || !blocked {
				return result, nil
			}

			if result != nil && result.RetryAfter > 0 {
				time.Sleep(result.RetryAfter)
			}
		}
	}
}

func (l *LocalRateLimiterV2) allow(_ context.Context, key string, item *localItemV2, cost int64) *Result {
	item.mu.Lock()
	defer item.mu.Unlock()

	var (
		now         = time.Now().UnixMicro()
		currentRate = l.cache[key].lastRate
	)

	l.increaseRateLimit(now, key, l.cache[key].currentRateLimit)

	// theoretical arrival time
	tat := item.tat
	if tat == 0 || tat < now {
		tat = now
	}

	burst := currentRate
	period := (time.Second / time.Duration(currentRate)).Microseconds()
	if period == 0 {
		period = 1
	}
	emissionInterval := period
	increment := emissionInterval * cost
	burstOffset := emissionInterval * burst

	newTat := tat + increment
	allowAt := newTat - burstOffset

	diff := now - allowAt
	remaining := diff / emissionInterval // round down

	if diff < 0 {
		return &Result{
			CurrentRate: currentRate,
			Allowed:     0,
			Remaining:   0,
			RetryAfter:  time.Duration(diff*-1) * time.Microsecond,
			ResetAfter:  time.Duration(tat-now) * time.Microsecond,
		}
	}

	resetAfter := newTat - now
	if resetAfter > 0 {
		item.tat = newTat
	}

	return &Result{
		CurrentRate: currentRate,
		Allowed:     cost,
		Remaining:   remaining,
		RetryAfter:  -1,
		ResetAfter:  time.Duration(resetAfter) * time.Microsecond,
	}
}

/**
1. 拆分阶梯, 适配老的结构
2. 递增每段限流器
*/

func (l *LocalRateLimiterV2) increaseRateLimit(now int64, key string, currentRateLimit types.RateLimitV2) {
	item := l.cache[key]

	// 检查当前请求是否需要递增到下段限流配置
	for idx, limit := range item.RateLimits {
		// 找到当前限流器配置, 并且当前时间超过限流器配置的target_duration+change_duration
		if limit.InitialRps == currentRateLimit.InitialRps && limit.TargetRps ==
			currentRateLimit.TargetRps &&
			((now - item.startTimestamp) >
				cache.Parse(currentRateLimit.TargetDuration).Microseconds()+cache.Parse(currentRateLimit.ChangeDuration).Microseconds()) {
			// 更新下一段限流器配置
			if idx+1 < len(item.RateLimits) {
				initStepRateLimitConfig(item, item.RateLimits[idx+1])
			}
		}
	}

	// 重新拆分限流配置
	l.convertRateLimit(now, key, item.currentRateLimit)
}

func (l *LocalRateLimiterV2) convertRateLimit(now int64, key string, currentRateLimit types.RateLimitV2) {
	item := l.cache[key]

	// 加压阶段, 递增rps
	if (now - item.startTimestamp) < cache.Parse(currentRateLimit.ChangeDuration).Microseconds() {
		item.stepDuration = getFixedStepDuration(currentRateLimit.ChangeDuration)

		var (
			elapsed    int64
			lastRate   = item.lastRate
			stepHeight = item.stepHeight
		)

		if item.lastUpdate == 0 {
			item.lastUpdate = now
		}

		elapsed = now - item.lastUpdate

		// 调整施压的rps
		steps := elapsed / item.stepDuration.Microseconds() // round down
		if stepHeight > 0 {
			item.lastRate = utils.Min(item.targetRPS, lastRate+steps*stepHeight)
		} else if stepHeight < 0 {
			item.lastRate = utils.Max(item.targetRPS, lastRate+steps*stepHeight)
		}

		if steps > 0 {
			l.Logger.Debugf(
				"递增加压阶段的阶梯 key:%s 当前时间:[%s]  当前rps:[%d]", key,
				time.Now().Format("2006-01-02 15:04:05.000"), item.lastRate,
			)
		}

		if steps != 0 {
			item.lastUpdate = now
		}
	}

	// 全力压阶段 , 固定rps
	if (now - item.startTimestamp) >= cache.Parse(currentRateLimit.ChangeDuration).Microseconds() {
		item.lastRate = item.targetRPS
		item.lastUpdate = now
		l.Logger.Debugf(
			"进入全力压阶段 key:%s 当前时间:[%s]  当前rps:[%d]", key, time.Now().Format("2006-01-02 15:04:05.000"),
			item.lastRate,
		)
	}
}

func getFixedStepHeight(initialRps, targetRps int64) (stepHeight int64) {
	// 正数向上取整 , 负数向下取整 , 都是为了防止 stepHeight变成0
	res := float64(targetRps-initialRps) / DefaultStepNumber
	if res > 0 {
		// 正数向上取整
		return int64(math.Ceil(res))
	} else if res < 0 {
		// 负数向下取整
		return int64(math.Floor(res))
	} else {
		// x为0，直接返回0
		return 0
	}
}

// 返回是纳秒格式
func getFixedStepDuration(duration string) time.Duration {
	return cache.Parse(duration) / DefaultStepNumber
}

func initStepRateLimitConfig(item *localItemV2, rateLimit types.RateLimitV2) {
	item.initialRPS = rateLimit.InitialRps
	item.targetRPS = rateLimit.TargetRps

	if rateLimit.InitialRps == 0 {
		item.lastRate = 1
	} else {
		item.lastRate = rateLimit.InitialRps
	}
	item.stepHeight = getFixedStepHeight(rateLimit.InitialRps, rateLimit.TargetRps)
	item.stepDuration = getFixedStepDuration(rateLimit.ChangeDuration)

	item.startTimestamp = time.Now().UnixMicro()
	item.currentRateLimit = rateLimit
}
