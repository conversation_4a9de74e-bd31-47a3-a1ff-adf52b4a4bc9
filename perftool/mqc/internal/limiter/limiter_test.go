package limiter

import (
	"context"
	"sync"
	"sync/atomic"
	"testing"
	"time"

	"github.com/zeromicro/go-zero/core/lang"
	"github.com/zeromicro/go-zero/core/proc"
	"github.com/zeromicro/go-zero/core/stores/redis"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/types"
)

func TestRateLimiter_Local_Constant(t *testing.T) {
	const (
		key = "API11"

		total = 30
		rate  = 2
	)

	ctx := context.Background()
	exitCh := make(chan lang.PlaceholderType, 1)
	proc.AddShutdownListener(
		func() {
			close(exitCh)
		},
	)
	cs := []Config{
		{
			RateLimit: types.RateLimit{
				TargetRps: rate,
			},
			Key:  key,
			Type: Local,
		},
	}
	rl := NewRateLimiter(
		ctx, exitCh, redis.MustNewRedis(
			redis.RedisConf{
				Host: "127.0.0.1:6379",
				Type: redis.NodeType,
				Pass: "",
				DB:   0,
			},
		), cs, 60,
	)

	t.Logf("startedAt: %s", time.Now())
	for i := 0; i < total; i++ {
		ret, err := rl.WaitForAllow(ctx, key)
		if err != nil {
			t.Fatalf("[%s] %d, error: %+v", time.Now(), i, err)
		} else if ret != nil && ret.Allowed < 1 {
			t.Errorf("[%s] %d, result: %+v", time.Now(), i, ret)
			continue
		}

		t.Logf("[%s] %d", time.Now(), i)
	}
	t.Logf("endedAt: %s", time.Now())
}

func TestRateLimiter_Distributed_Constant(t *testing.T) {
	const (
		key = "API12"

		total = 30
		rate  = 2
	)

	ctx := context.Background()
	exitCh := make(chan lang.PlaceholderType, 1)
	proc.AddShutdownListener(
		func() {
			close(exitCh)
		},
	)
	cs := []Config{
		{
			RateLimit: types.RateLimit{
				TargetRps: rate,
			},
			Key:  key,
			Type: Distributed,
		},
	}
	rl := NewRateLimiter(
		ctx, exitCh, redis.MustNewRedis(
			redis.RedisConf{
				Host: "127.0.0.1:6379",
				Type: redis.NodeType,
				Pass: "",
				DB:   0,
			},
		), cs, 60,
	)

	t.Logf("startedAt: %s", time.Now())
	for i := 0; i < total; i++ {
		ret, err := rl.WaitForAllow(ctx, key)
		if err != nil {
			t.Fatalf("[%s] %d, error: %+v", time.Now(), i, err)
		} else if ret != nil && ret.Allowed < 1 {
			t.Errorf("[%s] %d, result: %+v", time.Now(), i, ret)
			continue
		}

		t.Logf("[%s] %d", time.Now(), i)
	}
	t.Logf("endedAt: %s", time.Now())
}

func TestRateLimiter_Local_Step(t *testing.T) {
	const (
		key = "API21"

		total = 30
		rate  = 10
	)

	ctx := context.Background()
	exitCh := make(chan lang.PlaceholderType, 1)
	proc.AddShutdownListener(
		func() {
			close(exitCh)
		},
	)
	cs := []Config{
		{
			RateLimit: types.RateLimit{
				TargetRps:    rate,
				InitialRps:   1,
				StepHeight:   2,
				StepDuration: "5s",
			},
			Key:  key,
			Type: Local,
		},
	}
	rl := NewRateLimiter(
		ctx, exitCh, redis.MustNewRedis(
			redis.RedisConf{
				Host: "127.0.0.1:6379",
				Type: redis.NodeType,
				Pass: "",
				DB:   0,
			},
		), cs, 60,
	)

	t.Logf("startedAt: %s", time.Now().Format("2006-01-02 15:04:05.000"))
	for i := 0; i < total; i++ {
		ret, err := rl.WaitForAllow(ctx, key)
		if err != nil {
			t.Fatalf("[%s] %d, error: %+v", time.Now().Format("2006-01-02 15:04:05.000"), i, err)
		} else if ret != nil && ret.Allowed < 1 {
			t.Errorf("[%s] %d, result: %+v", time.Now().Format("2006-01-02 15:04:05.000"), i, ret)
			continue
		}

		t.Logf("[%s] %d", time.Now().Format("2006-01-02 15:04:05.000"), i)
	}
	t.Logf("endedAt: %s", time.Now().Format("2006-01-02 15:04:05.000"))
}

func TestRateLimiter_Distributed_Step(t *testing.T) {
	const (
		key = "API22"

		total = 30
		rate  = 10
	)

	ctx := context.Background()
	exitCh := make(chan lang.PlaceholderType, 1)
	proc.AddShutdownListener(
		func() {
			close(exitCh)
		},
	)
	cs := []Config{
		{
			RateLimit: types.RateLimit{
				TargetRps:    rate,
				InitialRps:   1,
				StepHeight:   2,
				StepDuration: "5s",
			},
			Key:  key,
			Type: Distributed,
		},
	}
	rl := NewRateLimiter(
		ctx, exitCh, redis.MustNewRedis(
			redis.RedisConf{
				Host: "127.0.0.1:6379",
				Type: redis.NodeType,
				Pass: "",
				DB:   0,
			},
		), cs, 60,
	)

	t.Logf("startedAt: %s", time.Now().Format("2006-01-02 15:04:05.000"))
	for i := 0; i < total; i++ {
		ret, err := rl.WaitForAllow(ctx, key)
		if err != nil {
			t.Fatalf("[%s] %d, error: %+v", time.Now().Format("2006-01-02 15:04:05.000"), i, err)
		} else if ret != nil && ret.Allowed < 1 {
			t.Errorf("[%s] %d, result: %+v", time.Now().Format("2006-01-02 15:04:05.000"), i, ret)
			continue
		}

		t.Logf("[%s] %d", time.Now().Format("2006-01-02 15:04:05.000"), i)
	}
	t.Logf("endedAt: %s", time.Now().Format("2006-01-02 15:04:05.000"))
}

func TestRateLimiter_Concurrent(t *testing.T) {
	const (
		key = "API3"

		concurrent = 20
		total      = 100
		rate       = 10
	)

	ctx := context.Background()
	exitCh := make(chan lang.PlaceholderType, 1)
	proc.AddShutdownListener(
		func() {
			close(exitCh)
		},
	)
	cs := []Config{
		{
			RateLimit: types.RateLimit{
				TargetRps:    rate,
				InitialRps:   1,
				StepHeight:   2,
				StepDuration: "5s",
			},
			Key:  key,
			Type: Distributed,
		},
	}
	rl := NewRateLimiter(
		ctx, exitCh, redis.MustNewRedis(
			redis.RedisConf{
				Host: "127.0.0.1:6379",
				Type: redis.NodeType,
				Pass: "",
				DB:   0,
			},
		), cs, 60,
	)

	t.Logf("startedAt: %s", time.Now().Format("2006-01-02 15:04:05.000"))
	var (
		wg sync.WaitGroup

		count = new(atomic.Int64)
	)
	count.Store(0)
	for i := 0; i < concurrent; i++ {
		i := i
		wg.Add(1)
		go func() {
			defer func() {
				wg.Done()
			}()

			for {
				if count.Load() > total {
					break
				}

				ret, err := rl.WaitForAllow(ctx, key)
				if err != nil {
					t.Errorf(
						"[%s] %d:%d, error: %+v", time.Now().Format("2006-01-02 15:04:05.000"), i, count.Add(1), err,
					)
					return
				} else if ret != nil && ret.Allowed < 1 {
					t.Errorf(
						"[%s] %d:%d, result: %+v", time.Now().Format("2006-01-02 15:04:05.000"), i, count.Add(1), ret,
					)
					continue
				}

				t.Logf("[%s] %d:%d", time.Now().Format("2006-01-02 15:04:05.000"), i, count.Add(1))
			}
		}()
	}
	wg.Wait()
	t.Logf("endedAt: %s", time.Now().Format("2006-01-02 15:04:05.000"))
}
