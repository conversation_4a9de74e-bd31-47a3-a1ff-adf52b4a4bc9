package limiter

import (
	"context"
	"sync"
	"time"

	"github.com/zeromicro/go-zero/core/lang"
	"github.com/zeromicro/go-zero/core/logx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/cache"
)

var _ Limiter = (*LocalRateLimiter)(nil)

type (
	LocalRateLimiter struct {
		logx.Logger
		ctx    context.Context
		exitCh <-chan lang.PlaceholderType

		cache map[string]*localItem
	}

	localItem struct {
		key          string
		targetRPS    int64
		initialRPS   int64
		stepHeight   int64
		stepDuration time.Duration

		lastRate   int64
		lastUpdate int64
		tat        int64
		mu         sync.Mutex
	}
)

func NewLocalRateLimiter(ctx context.Context, exitCh <-chan lang.PlaceholderType, configs []Config) *LocalRateLimiter {
	items := make(map[string]*localItem, len(configs))
	for _, config := range configs {
		if config.Key != "" {
			item := &localItem{
				key:          config.Key,
				targetRPS:    config.TargetRps,
				initialRPS:   config.InitialRps,
				stepHeight:   config.StepHeight,
				stepDuration: cache.Parse(config.StepDuration),
			}
			if config.StepHeight == 0 {
				item.initialRPS = config.TargetRps
				item.stepDuration = time.Second

				item.lastRate = config.TargetRps
			}
			items[config.Key] = item
		}
	}

	return &LocalRateLimiter{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		exitCh: exitCh,

		cache: items,
	}
}

func (l *LocalRateLimiter) SetExpireTime(_ int64) {
}

func (l *LocalRateLimiter) BlockTake(ctx context.Context, key string) (*Result, error) {
	return l.Take(ctx, key, true)
}

func (l *LocalRateLimiter) Take(ctx context.Context, key string, blocked bool) (*Result, error) {
	item, ok := l.cache[key]
	if !ok {
		// no need for rate limiting
		return nil, nil
	}

	for {
		select {
		case <-l.exitCh:
			l.Warnf("got an exit signal while waiting to pass through the rate limiter, key: %s", key)
			return nil, ExitSignalError
		case <-l.ctx.Done():
			l.Debugf("got a done signal while waiting to pass through the rate limiter, key: %s", key)
			return nil, DoneSignalError
		case <-ctx.Done():
			return nil, ctx.Err()
		default:
			result := l.allow(ctx, item, 1)
			if (result != nil && result.Allowed >= 1) || !blocked {
				return result, nil
			}

			if result != nil && result.RetryAfter > 0 {
				time.Sleep(result.RetryAfter)
			}
		}
	}
}

func (l *LocalRateLimiter) allow(_ context.Context, item *localItem, cost int64) *Result {
	item.mu.Lock()
	defer item.mu.Unlock()

	var (
		now          = time.Now().UnixMicro()
		stepDuration = item.stepDuration.Microseconds()

		currentRate int64
	)

	if stepDuration == 0 {
		stepDuration = time.Second.Microseconds()
	}

	if item.stepHeight == 0 {
		// constant rate limit
		currentRate = item.targetRPS
		item.lastUpdate = now
	} else {
		// step rate limit
		var (
			lastRate   = item.lastRate
			lastUpdate = item.lastUpdate
		)
		if lastRate == 0 {
			lastRate = item.initialRPS
		}
		if lastUpdate == 0 {
			lastUpdate = now
		}

		elapsed := now - lastUpdate
		steps := elapsed / stepDuration // round down
		currentRate = utils.Min(item.targetRPS, lastRate+steps*item.stepHeight)

		// save the last update time
		if steps != 0 {
			item.lastUpdate = now
		} else {
			item.lastUpdate = lastUpdate
		}
	}

	// save the updated rate
	if currentRate == 0 {
		currentRate = 1
	}
	item.lastRate = currentRate

	// theoretical arrival time
	tat := item.tat
	if tat == 0 || tat < now {
		tat = now
	}

	burst := currentRate
	period := (time.Second / time.Duration(currentRate)).Microseconds()
	if period == 0 {
		period = 1
	}
	emissionInterval := period
	increment := emissionInterval * cost
	burstOffset := emissionInterval * burst

	newTat := tat + increment
	allowAt := newTat - burstOffset

	diff := now - allowAt
	remaining := diff / emissionInterval // round down

	if diff < 0 {
		return &Result{
			CurrentRate: currentRate,
			Allowed:     0,
			Remaining:   0,
			RetryAfter:  time.Duration(diff*-1) * time.Microsecond,
			ResetAfter:  time.Duration(tat-now) * time.Microsecond,
		}
	}

	resetAfter := newTat - now
	if resetAfter > 0 {
		item.tat = newTat
	}

	return &Result{
		CurrentRate: currentRate,
		Allowed:     cost,
		Remaining:   remaining,
		RetryAfter:  -1,
		ResetAfter:  time.Duration(resetAfter) * time.Microsecond,
	}
}
