-- Generic Cell Rate Algorithm: https://en.wikipedia.org/wiki/Generic_cell_rate_algorithm

-- KEYS[1]: rate_key, e.g., {prefix}:rate
-- KEYS[2]: last_update_key, e.g., {prefix}:last_update
-- KEYS[3]: tat_key, e.g., {prefix}:tat
-- ARGV[1]: target rps of rate limiter
-- ARGV[2]: initial rps of rate limiter
-- ARGV[3]: step height (increment) of rate limiter
-- ARGV[4]: step duration (seconds) of rate limiter
-- ARGV[5]: expire time (seconds) of rate limiter
-- ARGV[6]: cost of request

local target_rps = tonumber(ARGV[1])
local initial_rps = tonumber(ARGV[2])
local step_height = tonumber(ARGV[3])
local step_duration = tonumber(ARGV[4]) * 1000000 -- convert to microseconds
local expire = tonumber(ARGV[5]) * 1000 -- convert to milliseconds
local cost = tonumber(ARGV[6])

local now = redis.call("TIME")
now = (now[1] * 1000000) + now[2]

local current_rate

if step_height == 0 then
    -- constant rate limit
    initial_rps = target_rps
    step_duration = 1000000
    current_rate = target_rps
    redis.call("SET", KEYS[2], now, "PX", expire)
else
    -- step rate limit
    expire = math.max(expire, (target_rps - initial_rps) / step_height * (step_duration / 1000))

    local last_rate = tonumber(redis.call("GET", KEYS[1]) or initial_rps)
    local last_update = tonumber(redis.call("GET", KEYS[2]) or now)

    local elapsed = now - last_update
    local steps = math.floor(elapsed / step_duration)
    current_rate = math.min(target_rps, last_rate + steps * step_height)
    if current_rate == 0 then
        current_rate = 1
    end

    -- save the last update time
    if steps ~= 0 then
        redis.call("SET", KEYS[2], now, "PX", expire)
    else
        redis.call("SET", KEYS[2], last_update, "PX", expire)
    end
end

-- save the updated rate
redis.call("SET", KEYS[1], current_rate, "PX", expire)


-- tat: theoretical arrival time
local tat = math.max(tonumber(redis.call("GET", KEYS[3]) or now), now)

local burst = current_rate
local period = math.ceil(1000000 / current_rate)
local emission_interval = period
local increment = emission_interval * cost
local burst_offset = emission_interval * burst

local new_tat = tat + increment
local allow_at = new_tat - burst_offset

local diff = now - allow_at
local remaining = math.floor(diff / emission_interval)

local retry_after = -1
local reset_after

if diff < 0 then
    retry_after = diff * -1
    reset_after = tat - now

    return {
        current_rate, -- current rate
        0, -- allowed
        0, -- remaining
        tostring(retry_after),
        tostring(reset_after),
    }
end

reset_after = new_tat - now
if reset_after > 0 then
    redis.call("SET", KEYS[3], new_tat, "PX", math.ceil(reset_after / 1000))
end

return {current_rate, cost, remaining, tostring(retry_after), tostring(reset_after)}
