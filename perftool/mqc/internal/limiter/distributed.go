package limiter

import (
	"context"
	_ "embed"
	"fmt"
	"strconv"
	"time"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/lang"
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/stores/redis"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/redisscript"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/cache"
)

const (
	keyPrefix           = "rate_limiter:"
	rateKeySuffix       = ":rate"
	lastUpdateKeySuffix = ":last_update"
	tatKeySuffix        = ":tat"

	rateKeyFormat       = keyPrefix + "{%s}" + rateKeySuffix
	lastUpdateKeyFormat = keyPrefix + "{%s}" + lastUpdateKeySuffix
	tatKeyFormat        = keyPrefix + "{%s}" + tatKeySuffix

	expireTime   = 60 * 60 // 3600 seconds
	minSleepTime = 100 * time.Millisecond
)

var (
	_ Limiter = (*DistributedRateLimiter)(nil)

	//go:embed gcra.lua
	allowLuaScript string
	allowCmd       = redisscript.NewScript(allowLuaScript)
)

type (
	DistributedRateLimiter struct {
		logx.Logger
		ctx    context.Context
		exitCh <-chan lang.PlaceholderType
		rdb    *redis.Redis

		cache  map[string]*distributedItem
		expire int64
	}

	distributedItem struct {
		key                            string
		rateKey, lastUpdateKey, tatKey string

		targetRPS    int64
		initialRPS   int64
		stepHeight   int64
		stepDuration time.Duration
	}
)

func NewDistributedRateLimiter(
	ctx context.Context, exitCh <-chan lang.PlaceholderType, rdb *redis.Redis, configs []Config,
) *DistributedRateLimiter {
	items := make(map[string]*distributedItem, len(configs))
	for _, config := range configs {
		if config.Key != "" {
			items[config.Key] = &distributedItem{
				key:           config.Key,
				rateKey:       RateKey(config.Key),
				lastUpdateKey: LastUpdateKey(config.Key),
				tatKey:        TatKey(config.Key),
				targetRPS:     config.TargetRps,
				initialRPS:    config.InitialRps,
				stepHeight:    config.StepHeight,
				stepDuration:  cache.Parse(config.StepDuration),
			}
		}
	}

	return &DistributedRateLimiter{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		exitCh: exitCh,
		rdb:    rdb,

		cache:  items,
		expire: expireTime,
	}
}

func (l *DistributedRateLimiter) SetExpireTime(expire int64) {
	if expire != 0 {
		l.expire = expire
	}
}

func (l *DistributedRateLimiter) BlockTake(ctx context.Context, key string) (*Result, error) {
	return l.Take(ctx, key, true)
}

func (l *DistributedRateLimiter) Take(ctx context.Context, key string, blocked bool) (*Result, error) {
	item, ok := l.cache[key]
	if !ok {
		// no need for rate limiting
		return nil, nil
	}

	for {
		select {
		case <-l.exitCh:
			l.Warnf("got an exit signal while waiting to pass through the rate limiter, key: %s", key)
			return nil, ExitSignalError
		case <-l.ctx.Done():
			l.Debugf("got a done signal while waiting to pass through the rate limiter, key: %s", key)
			return nil, DoneSignalError
		case <-ctx.Done():
			return nil, ctx.Err()
		default:
			result, err := l.allow(ctx, item, 1)
			if err != nil || (result != nil && result.Allowed >= 1) || !blocked {
				return result, err
			}

			if result != nil && result.RetryAfter > 0 {
				if result.RetryAfter < minSleepTime {
					time.Sleep(minSleepTime)
				} else {
					time.Sleep(result.RetryAfter)
				}
			}
		}
	}
}

func (l *DistributedRateLimiter) allow(ctx context.Context, item *distributedItem, cost int64) (*Result, error) {
	val, err := allowCmd.Execute(
		ctx, l.rdb,
		[]string{item.rateKey, item.lastUpdateKey, item.tatKey}, // KEYS
		item.targetRPS, item.initialRPS, item.stepHeight, int64(item.stepDuration.Seconds()), l.expire, cost, // ARGV
	)
	if err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.EvalRedisScriptFailure, err.Error()),
			"failed to execute redis script for rate limiting, key: %s, error: %+v",
			item.key, err,
		)
	}

	vs, ok := val.([]any)
	if !ok {
		return nil, errorx.Errorf(
			errorx.EvalRedisScriptFailure,
			"invalid type of result, key: %s, error: expected []any, but got %T",
			item.key, val,
		)
	} else if len(vs) < 5 {
		return nil, errorx.Errorf(
			errorx.EvalRedisScriptFailure,
			"incorrect number of result values, key: %s, error: expected >=5, but got %d",
			item.key, len(vs),
		)
	}

	rate, ok := vs[0].(int64)
	if !ok {
		return nil, errorx.Errorf(
			errorx.EvalRedisScriptFailure,
			"invalid type of `rate`, key: %s, error: expected int64, but got %T",
			item.key, vs[0],
		)
	}
	allowed, ok := vs[1].(int64)
	if !ok {
		return nil, errorx.Errorf(
			errorx.EvalRedisScriptFailure,
			"invalid type of `allowed`, key: %s, error: expected int64, but got %T",
			item.key, vs[1],
		)
	}
	remaining, ok := vs[2].(int64)
	if !ok {
		return nil, errorx.Errorf(
			errorx.EvalRedisScriptFailure,
			"invalid type of `remaining`, key: %s, error: expected int64, but got %T",
			item.key, vs[2],
		)
	}
	v3, ok := vs[3].(string)
	if !ok {
		return nil, errorx.Errorf(
			errorx.EvalRedisScriptFailure,
			"invalid type of `retry_after`, key: %s, error: expected string, but got: %T",
			item.key, vs[3],
		)
	}
	v4, ok := vs[4].(string)
	if !ok {
		return nil, errorx.Errorf(
			errorx.EvalRedisScriptFailure,
			"invalid type of `reset_after`, key: %s, error: expected string, but got: %T",
			item.key, vs[4],
		)
	}

	retryAfter, _ := strconv.ParseFloat(v3, 64)
	resetAfter, _ := strconv.ParseFloat(v4, 64)

	return &Result{
		CurrentRate: rate,
		Allowed:     allowed,
		Remaining:   remaining,
		RetryAfter:  time.Duration(retryAfter) * time.Microsecond,
		ResetAfter:  time.Duration(resetAfter) * time.Microsecond,
	}, nil
}

func RateKey(key string) string {
	return fmt.Sprintf(rateKeyFormat, key)
}

func LastUpdateKey(key string) string {
	return fmt.Sprintf(lastUpdateKeyFormat, key)
}

func TatKey(key string) string {
	return fmt.Sprintf(tatKeyFormat, key)
}
