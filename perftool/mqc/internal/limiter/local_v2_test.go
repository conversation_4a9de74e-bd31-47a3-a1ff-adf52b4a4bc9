package limiter

import (
	"context"
	"sync"
	"testing"
	"time"

	"github.com/zeromicro/go-zero/core/lang"
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/proc"
	"go.uber.org/zap"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/log"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/types"
)

func TestMain(m *testing.M) {
	log.NewZapWriter(
		logx.LogConf{
			Mode:       "file",
			Encoding:   "plain",
			Path:       "./logs",
			Level:      "info",
			TimeFormat: time.DateTime,
			Stat:       true,
		}, zap.AddCaller(), zap.Development(),
	).SetLogxWriter()

	m.Run()
}

func TestRateLimiterV2_Local_Concurrent(t *testing.T) {
	const (
		key = "task_id:zrdwSECHXxS7iuUPcar-P:TT:Auth"

		concurrent = 9394
		every      = 10
	)

	ctx := context.Background()
	exitCh := make(chan lang.PlaceholderType, 1)
	proc.AddShutdownListener(
		func() {
			close(exitCh)
		},
	)
	cs := []ConfigV2{
		{
			RateLimits: []types.RateLimitV2{
				{
					TargetRps:      25,
					InitialRps:     3,
					ChangeDuration: "30s",
					TargetDuration: "",
				},
			},
			Key:  key,
			Type: Local,
		},
	}
	rl := NewLocalRateLimiterV2(ctx, exitCh, cs)

	logx.Infof("startedAt: %s", time.Now().Format(time.DateTime))

	var wg sync.WaitGroup
	for i := 0; i < concurrent; i++ {
		wg.Add(1)
		go func(index int) {
			defer func() {
				wg.Done()
			}()

			var count int
			for {
				if count >= every {
					break
				}
				count++
				ret, err := rl.BlockTake(ctx, key)
				if err != nil {
					logx.Errorf("%d:%d, error: %+v", index, count, err)
					return
				} else if ret != nil && ret.Allowed < 1 {
					logx.Errorf("%d:%d, result: %+v", index, count, ret)
					continue
				}

				logx.Infof("%d:%d, result: %+v", index, count, ret)
			}
		}(i)
	}
	wg.Wait()
	logx.Infof("endedAt: %s", time.Now().Format(time.DateTime))
}
