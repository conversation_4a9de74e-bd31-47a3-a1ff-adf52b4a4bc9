package limiter

import (
	"context"
	"time"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/types"
)

type (
	Limiter interface {
		SetExpireTime(expire int64)
		BlockTake(ctx context.Context, key string) (*Result, error)
	}

	Config struct {
		types.RateLimit

		Key  string `json:"key"`
		Type Type   `json:"type"`
	}

	ConfigV2 struct {
		RateLimits []types.RateLimitV2

		Key  string `json:"key"`
		Type Type   `json:"type"`
	}

	Result struct {
		// CurrentRate is the current rate of the RateLimiter.
		CurrentRate int64

		// Allowed is the number of events that may happen at time now.
		Allowed int64

		// Remaining is the maximum number of requests that could be
		// permitted instantaneously for this key given the current
		// state. For example, if a rate limiter allows 10 requests per
		// second and has already received 6 requests for this key this
		// second, Remaining would be 4.
		Remaining int64

		// RetryAfter is the time until the next request will be permitted.
		// It should be -1 unless the rate limit has been exceeded.
		RetryAfter time.Duration

		// ResetAfter is the time until the RateLimiter returns to its
		// initial state for a given key. For example, if a rate limiter
		// manages requests per second and received one request 200ms ago,
		// Reset would return 800ms. You can also think of this as the time
		// until Limit and Remaining will be equal.
		ResetAfter time.Duration
	}
)
