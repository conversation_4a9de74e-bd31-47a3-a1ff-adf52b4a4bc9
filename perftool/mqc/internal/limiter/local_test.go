package limiter

import (
	"math"
	"testing"
	"time"
)

func TestRoundDown(t *testing.T) {
	type args struct {
		a int64
		b int64
	}
	tests := []struct {
		name string
		args args
		want int64
	}{
		{
			name: "",
			args: args{
				a: 123000,                     // 123ms
				b: time.Second.Microseconds(), // 1s
			},
			want: 0,
		},
		{
			name: "",
			args: args{
				a: 1234000,                    // 1.234s
				b: time.Second.Microseconds(), // 1s
			},
			want: 1,
		},
		{
			name: "",
			args: args{
				a: 1987000,                    // 1.987s
				b: time.Second.Microseconds(), // 1s
			},
			want: 1,
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				got1 := tt.args.a / tt.args.b
				if got1 != tt.want {
					t.Errorf("floor(%d / %d) = %d, want %d", tt.args.a, tt.args.b, got1, tt.want)
				}
				t.Logf("floor(%d / %d) = %d", tt.args.a, tt.args.b, got1)

				got2 := int64(math.Floor(float64(tt.args.a) / float64(tt.args.b)))
				if got2 != tt.want {
					t.Errorf("math.Floor(%d / %d) = %d, want %d", tt.args.a, tt.args.b, got2, tt.want)
				}
				t.Logf("math.Floor(%d / %d) = %d", tt.args.a, tt.args.b, got2)
			},
		)
	}
}

func TestRoundUp(t *testing.T) {
	type args struct {
		a int64
		b int64
	}
	tests := []struct {
		name string
		args args
		want int64
	}{
		{
			name: "",
			args: args{
				a: 123000,                     // 123ms
				b: time.Second.Microseconds(), // 1s
			},
			want: 1,
		},
		{
			name: "",
			args: args{
				a: 1234000,                    // 1.234s
				b: time.Second.Microseconds(), // 1s
			},
			want: 2,
		},
		{
			name: "",
			args: args{
				a: 1987000,                    // 1.987s
				b: time.Second.Microseconds(), // 1s
			},
			want: 2,
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				got1 := (tt.args.a + tt.args.b - 1) / tt.args.b
				if got1 != tt.want {
					t.Errorf("ceil(%d / %d) = %d, want %d", tt.args.a, tt.args.b, got1, tt.want)
				}
				t.Logf("ceil(%d / %d) = %d", tt.args.a, tt.args.b, got1)

				got2 := int64(math.Ceil(float64(tt.args.a) / float64(tt.args.b)))
				if got2 != tt.want {
					t.Errorf("math.Ceil(%d / %d) = %d, want %d", tt.args.a, tt.args.b, got2, tt.want)
				}
				t.Logf("math.Ceil(%d / %d) = %d", tt.args.a, tt.args.b, got2)
			},
		)
	}
}
