-- KEYS[1]: rate_key, {prefix}:rate
-- KEYS[2]: last_update_key, {prefix}:last_update
-- KEYS[3]: tokens_key, {prefix}:tokens

-- ARGV[1]: target rps of rate limiter
-- ARGV[2]: initial rps of rate limiter
-- ARGV[3]: step height (increment) of rate limiter
-- ARGV[4]: step duration (seconds) of rate limiter
-- ARGV[5]: expire time (seconds) of rate limiter
-- ARGV[6]: cost of request

-- ARGV[2]: Max tokens in the bucket (bucket capacity)
-- ARGV[3]: Initial token generation rate (tokens per millisecond)
-- ARGV[4]: Step height (increment in tokens per second)
-- ARGV[5]: Step duration in milliseconds
-- ARGV[6]: Target token generation rate (tokens per millisecond)
-- ARGV[7]: Requested tokens for the current request

local target_rps = tonumber(ARGV[1])
local initial_rps = tonumber(ARGV[2])
local step_height = tonumber(ARGV[3])
local step_duration = tonumber(ARGV[4])
local expire = tonumber(ARGV[5]) * 1000 -- convert to milliseconds
local tokens_requested = tonumber(ARGV[6])

local target_rate = target_rps / 1000
local initial_rate = initial_rps / 1000
local now = redis.call("TIME")
now = (now[1] * 1000) + (now[2] / 1000)

local last_rate = tonumber(redis.call("GET", KEYS[1]), or initial_rate)
local last_update = tonumber(redis.call("GET", KEYS[2]) or now)
local last_tokens = tonumber(redis.call("GET", KEYS[3]) or target_rps)

local delta_time = math.max(0, now - last_update)
local new_tokens = math.min(target_rps, last_tokens + delta_time * last_rate)

-- update the rate if enough time has passed
if delta_time >= step_duration and current_rate < target_rate then
    local steps = math.floor(delta_time / step_duration)
    last_rate = math.min(target_rate, last_rate + steps * step_height)
    last_update = now
else
    last_update = last_update + delta_time
end

-- determine if the request can be fulfilled
local allowed = new_tokens >= tokens_requested
if allowed then
    -- enough tokens, allow the request
    new_tokens = new_tokens - tokens_requested
end

redis.call("SET", KEYS[1], last_rate, "PX", expire)
redis.call("SET", KEYS[2], last_update, "PX", expire)
redis.call("SET", KEYS[3], new_tokens, "PX", expire)

return allowed
