package limiter

import (
	"context"

	"github.com/zeromicro/go-zero/core/jsonx"
	"github.com/zeromicro/go-zero/core/lang"
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/stores/redis"
)

type RateLimiter struct {
	logx.Logger

	limiters map[string]Limiter
}

func NewRateLimiter(
	ctx context.Context, exitCh <-chan lang.PlaceholderType, rdb *redis.Redis, configs []Config, expire int64,
) *RateLimiter {
	l := &RateLimiter{
		Logger: logx.WithContext(ctx),

		limiters: make(map[string]Limiter, len(configs)),
	}

	localConfigs := make([]Config, 0, len(configs))
	distributedConfigs := make([]Config, 0, len(configs))
	for _, config := range configs {
		if config.Key == "" {
			continue
		}

		switch config.Type {
		case Local:
			localConfigs = append(localConfigs, config)
		case Distributed:
			distributedConfigs = append(distributedConfigs, config)
		default:
			continue
		}
	}

	if len(localConfigs) > 0 {
		lrl := NewLocalRateLimiter(ctx, exitCh, localConfigs)
		for _, config := range localConfigs {
			l.limiters[config.Key] = lrl
		}
		l.Infof("new a local rate limiter, configs: %s", jsonx.MarshalIgnoreError(localConfigs))
	}

	if len(distributedConfigs) > 0 {
		drl := NewDistributedRateLimiter(ctx, exitCh, rdb, distributedConfigs)
		drl.SetExpireTime(expire)
		for _, config := range distributedConfigs {
			l.limiters[config.Key] = drl
		}
		l.Infof(
			"new a distributed rate limiter, configs: %s, expire: %d",
			jsonx.MarshalIgnoreError(distributedConfigs), expire,
		)
	}

	return l
}

func (l *RateLimiter) WaitForAllow(ctx context.Context, key string) (*Result, error) {
	rl, ok := l.limiters[key]
	if !ok {
		return nil, nil
	}

	return rl.BlockTake(ctx, key)
}
