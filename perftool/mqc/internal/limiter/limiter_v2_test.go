package limiter

import (
	"context"
	"sync"
	"sync/atomic"
	"testing"
	"time"

	"github.com/zeromicro/go-zero/core/lang"
	"github.com/zeromicro/go-zero/core/proc"
	"github.com/zeromicro/go-zero/core/stores/redis"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/types"
)

var defaultRedisConf = redis.RedisConf{
	Host: "************:6379",
	Type: redis.NodeType,
	Pass: "",
	DB:   0,
}

func TestRateLimiterV2_Local_Constant(t *testing.T) {
	const (
		key = "API11"

		total = 300
		rate  = 10
	)

	ctx := context.Background()
	exitCh := make(chan lang.PlaceholderType, 1)
	proc.AddShutdownListener(
		func() {
			close(exitCh)
		},
	)

	cs := []ConfigV2{
		{
			RateLimits: []types.RateLimitV2{
				{
					TargetRps:      rate,
					InitialRps:     1,
					ChangeDuration: "0s",
					TargetDuration: "10s",
				},
				{
					TargetRps:      rate * 2,
					InitialRps:     1,
					ChangeDuration: "0s",
					TargetDuration: "10s",
				},
			},
			Key:  key,
			Type: Local,
		},
	}

	rl := NewRateLimiterV2(
		ctx, exitCh, redis.MustNewRedis(
			defaultRedisConf,
		), nil, cs, 60,
	)

	t.Logf("startedAt: %s", time.Now())
	for i := 0; i < total; i++ {
		ret, err := rl.WaitForAllow(ctx, key)
		if err != nil {
			t.Fatalf("[%s] %d, error: %+v", time.Now(), i, err)
		} else if ret != nil && ret.Allowed < 1 {
			t.Errorf("[%s] %d, result: %+v", time.Now(), i, ret)
			continue
		}

		t.Logf("[%s] %d", time.Now(), i)
	}
	t.Logf("endedAt: %s", time.Now())
}

func TestRateLimiterV2_Local_Step_Up(t *testing.T) {
	const (
		key = "API21"

		total = 3000
	)

	ctx := context.Background()
	exitCh := make(chan lang.PlaceholderType, 1)
	proc.AddShutdownListener(
		func() {
			close(exitCh)
		},
	)

	cs := []ConfigV2{
		{
			RateLimits: []types.RateLimitV2{
				{
					TargetRps:      10,
					InitialRps:     1,
					ChangeDuration: "20s",
					TargetDuration: "3s",
				},
				{
					TargetRps:      50,
					InitialRps:     10,
					ChangeDuration: "30s",
					TargetDuration: "10s",
				},
			},
			Key:  key,
			Type: Local,
		},
	}
	rl := NewRateLimiterV2(
		ctx, exitCh, redis.MustNewRedis(
			defaultRedisConf,
		), nil, cs, 60,
	)

	t.Logf("startedAt: %s", time.Now().Format("2006-01-02 15:04:05.000"))
	for i := 0; i < total; i++ {
		ret, err := rl.WaitForAllow(ctx, key)
		if err != nil {
			t.Fatalf("[%s] %d, error: %+v", time.Now().Format("2006-01-02 15:04:05.000"), i, err)
		} else if ret != nil && ret.Allowed < 1 {
			t.Errorf("[%s] %d, result: %+v", time.Now().Format("2006-01-02 15:04:05.000"), i, ret)
			continue
		}

		t.Logf("[%s] %d", time.Now().Format("2006-01-02 15:04:05.000"), i)
	}
	t.Logf("endedAt: %s", time.Now().Format("2006-01-02 15:04:05.000"))
}

func TestRateLimiterV2_Local_Step_Down(t *testing.T) {
	const (
		key = "API21"

		total = 3000
	)

	ctx := context.Background()
	exitCh := make(chan lang.PlaceholderType, 1)
	proc.AddShutdownListener(
		func() {
			close(exitCh)
		},
	)

	cs := []ConfigV2{
		{
			RateLimits: []types.RateLimitV2{
				{
					TargetRps:      30,
					InitialRps:     10,
					ChangeDuration: "20s",
					TargetDuration: "5s",
				},
				{
					TargetRps:      1,
					InitialRps:     30,
					ChangeDuration: "20s",
					TargetDuration: "5s",
				},
			},
			Key:  key,
			Type: Local,
		},
	}
	rl := NewRateLimiterV2(
		ctx, exitCh, redis.MustNewRedis(
			defaultRedisConf,
		), nil, cs, 60,
	)

	t.Logf("startedAt: %s", time.Now().Format("2006-01-02 15:04:05.000"))
	for i := 0; i < total; i++ {
		ret, err := rl.WaitForAllow(ctx, key)
		if err != nil {
			t.Fatalf("[%s] %d, error: %+v", time.Now().Format("2006-01-02 15:04:05.000"), i, err)
		} else if ret != nil && ret.Allowed < 1 {
			t.Errorf("[%s] %d, result: %+v", time.Now().Format("2006-01-02 15:04:05.000"), i, ret)
			continue
		}

		t.Logf("[%s] %d", time.Now().Format("2006-01-02 15:04:05.000"), i)
	}
	t.Logf("endedAt: %s", time.Now().Format("2006-01-02 15:04:05.000"))
}

func TestRateLimiterV2_Concurrent(t *testing.T) {
	const (
		key = "API3"

		concurrent = 20
		total      = 100
		rate       = 10
	)

	ctx := context.Background()
	exitCh := make(chan lang.PlaceholderType, 1)
	proc.AddShutdownListener(
		func() {
			close(exitCh)
		},
	)
	cs := []Config{
		{
			RateLimit: types.RateLimit{
				TargetRps:    rate,
				InitialRps:   1,
				StepHeight:   2,
				StepDuration: "5s",
			},
			Key:  key,
			Type: Distributed,
		},
	}
	rl := NewRateLimiter(
		ctx, exitCh, redis.MustNewRedis(
			defaultRedisConf,
		), cs, 60,
	)

	t.Logf("startedAt: %s", time.Now().Format("2006-01-02 15:04:05.000"))
	var (
		wg sync.WaitGroup

		count = new(atomic.Int64)
	)
	count.Store(0)
	for i := 0; i < concurrent; i++ {
		i := i
		wg.Add(1)
		go func() {
			defer func() {
				wg.Done()
			}()

			for {
				if count.Load() > total {
					break
				}

				ret, err := rl.WaitForAllow(ctx, key)
				if err != nil {
					t.Errorf(
						"[%s] %d:%d, error: %+v", time.Now().Format("2006-01-02 15:04:05.000"), i, count.Add(1), err,
					)
					return
				} else if ret != nil && ret.Allowed < 1 {
					t.Errorf(
						"[%s] %d:%d, result: %+v", time.Now().Format("2006-01-02 15:04:05.000"), i, count.Add(1), ret,
					)
					continue
				}

				t.Logf("[%s] %d:%d", time.Now().Format("2006-01-02 15:04:05.000"), i, count.Add(1))
			}
		}()
	}
	wg.Wait()
	t.Logf("endedAt: %s", time.Now().Format("2006-01-02 15:04:05.000"))
}
