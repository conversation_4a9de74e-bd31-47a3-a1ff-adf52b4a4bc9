package broadcasttimer

import (
	"sync"
	"time"

	"github.com/zeromicro/go-zero/core/lang"
	"github.com/zeromicro/go-zero/core/threading"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/timewheel"
)

type Timer struct {
	timer     *timewheel.Timer
	delay     time.Duration
	startOnce sync.Once
	notifyCh  chan lang.PlaceholderType
	stopCh    chan lang.PlaceholderType
	stopped   bool
	lock      sync.Mutex
}

func NewTimer(delay time.Duration) *Timer {
	return &Timer{
		delay:    delay,
		notifyCh: make(chan lang.PlaceholderType),
		stopCh:   make(chan lang.PlaceholderType),
	}
}

func (t *Timer) Start() <-chan lang.PlaceholderType {
	t.startOnce.Do(
		func() {
			t.lock.Lock()
			defer t.lock.Unlock()

			if t.stopped {
				return
			}

			t.timer = timewheel.NewTimer(t.delay)
			threading.GoSafe(t.wait)
		},
	)

	return t.notifyCh
}

func (t *Timer) Reset(delay time.Duration) {
	t.lock.Lock()
	defer t.lock.Unlock()

	if t.timer != nil {
		t.timer.Stop()
	}

	t.delay = delay
	t.startOnce = sync.Once{}
	t.notifyCh = make(chan lang.PlaceholderType)
	t.stopped = false
}

func (t *Timer) Stop() {
	t.lock.Lock()
	defer t.lock.Unlock()

	if !t.stopped && t.timer != nil {
		t.timer.Stop()
		close(t.stopCh)
	}
	t.stopped = true
}

func (t *Timer) wait() {
	select {
	case <-t.timer.C:
		t.broadcast()
		return
	case <-t.stopCh:
		return
	}
}

func (t *Timer) broadcast() {
	t.lock.Lock()
	defer t.lock.Unlock()

	if !t.stopped {
		close(t.notifyCh)
	}
}
