package controller

import (
	"reflect"
	"testing"
	"time"

	"github.com/zeromicro/go-zero/core/jsonx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"

	commonpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
	commontypes "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/perftool/common"
	ttclient "gitlab.ttyuyin.com/TestDevelopment/probe-backend/perftool/mqc/internal/clienttypes/tt"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/perftool/mqc/internal/limiter"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/perftool/mqc/types"
)

func TestMain(m *testing.M) {
	ttclient.Init()

	m.Run()
}

func Test_calculateMaxDuration(t *testing.T) {
	type args struct {
		clientType       types.ClientType
		perfInfo         *commonpb.PerfTestTaskInfo
		perfCase         *commonpb.PerfCaseContent
		numberOfPerfData uint32
	}
	tests := []struct {
		name string
		args args
		want ExecutionDurations
	}{
		{
			name: "",
			args: args{
				clientType: "tt",
				perfInfo: &commonpb.PerfTestTaskInfo{
					TriggerMode: commonpb.TriggerMode_MANUAL,
					ExecuteMode: commonpb.PerfTaskExecutionMode_BY_DURATION,
					Protocol:    commonpb.Protocol_PROTOCOL_TT,
					Keepalive: &commonpb.PerfKeepalive{
						Auth: &commonpb.PerfKeepalive_AuthConfig{
							RateLimit: &commonpb.RateLimit{
								TargetRps:    150,
								InitialRps:   0,
								StepHeight:   0,
								StepDuration: "0s",
							},
							Key: "",
						},
					},
					Duration:  1800,
					Times:     0,
					Timeout:   2160,
					TotalOfVu: 4000,
					TotalOfLg: 4,
				},
				perfCase: &commonpb.PerfCaseContent{
					SetupSteps: nil,
					SerialSteps: []*commonpb.PerfCaseStep{
						{
							Name: "88888 - 获取广告信息",
							RateLimit: &commonpb.RateLimit{
								TargetRps:    8000,
								InitialRps:   0,
								StepHeight:   2000,
								StepDuration: "300s",
							},
							Method: "ga.api.ad_center.AdCenterLogic.BatchGetAd",
							Sleep:  "",
							Key:    "",
						},
					},
					ParallelSteps: nil,
					TeardownSteps: nil,
				},
				numberOfPerfData: 1000,
			},
			want: ExecutionDurations{
				authDuration:     32 * time.Second,
				setupDuration:    0,
				perfTestDuration: 30 * time.Minute,
				teardownDuration: 0,
				totalDuration:    30*time.Minute + 32*time.Second,
			},
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				got := calculateMaxDuration(
					tt.args.clientType, tt.args.perfInfo, tt.args.perfCase, tt.args.numberOfPerfData,
				)
				if !reflect.DeepEqual(got, tt.want) {
					t.Errorf("calculateMaxDuration() = %v, want %v", got, tt.want)
				}

				t.Logf("durations: %s", got.String())
			},
		)
	}
}

func TestGetLimiterConfigsFromPerfKeepaliveV2(t *testing.T) {
	type args struct {
		keyPrefix  string
		protocol   commonpb.Protocol
		keepalive  *commonpb.PerfRateLimits
		numberOfLG uint32
	}
	tests := []struct {
		name string
		args args
		want []limiter.ConfigV2
	}{
		{
			name: "",
			args: args{
				keyPrefix: "task_id:1",
				protocol:  commonpb.Protocol_PROTOCOL_TT,
				keepalive: &commonpb.PerfRateLimits{
					Auth: &commonpb.PerfRateLimits_AuthConfig{
						RateLimits: []*commonpb.RateLimitV2{
							{
								TargetRps:      500,
								InitialRps:     50,
								ChangeDuration: "30s",
								TargetDuration: "",
							},
						},
					},
					Heartbeat: &commonpb.PerfRateLimits_HeartbeatConfig{
						RateLimits: []*commonpb.RateLimitV2{
							{
								TargetRps:      1000,
								InitialRps:     100,
								ChangeDuration: "30s",
								TargetDuration: "",
							},
						},
						Interval: 60,
					},
				},
				numberOfLG: 20,
			},
			want: []limiter.ConfigV2{
				{
					RateLimits: []commontypes.RateLimitV2{
						{
							TargetRps:      (500 + 20 - 1) / 20,
							InitialRps:     (50 + 20 - 1) / 20,
							ChangeDuration: "30s",
							TargetDuration: "",
						},
					},
					Key: generateLimiterConfigKey(
						"task_id:1", commonpb.Protocol_PROTOCOL_TT, common.ConstAuthAPIName,
					),
					Type: limiter.Local,
				},
				{
					RateLimits: []commontypes.RateLimitV2{
						{
							TargetRps:      (1000 + 20 - 1) / 20,
							InitialRps:     (100 + 20 - 1) / 20,
							ChangeDuration: "30s",
							TargetDuration: "",
						},
					},
					Key: generateLimiterConfigKey(
						"task_id:1", commonpb.Protocol_PROTOCOL_TT, common.ConstHeartbeatAPIName,
					),
					Type: limiter.Local,
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				t.Logf("keepalive1: %s", protobuf.MarshalJSONIgnoreError(tt.args.keepalive))
				got := GetLimiterConfigsFromPerfKeepaliveV2(
					tt.args.keyPrefix, tt.args.protocol, tt.args.keepalive, tt.args.numberOfLG,
				)
				t.Logf("keepalive2: %s", protobuf.MarshalJSONIgnoreError(tt.args.keepalive))
				t.Logf("GetLimiterConfigsFromPerfKeepaliveV2() = %s", jsonx.MarshalIgnoreError(got))
				if !reflect.DeepEqual(got, tt.want) {
					t.Errorf("GetLimiterConfigsFromPerfKeepaliveV2() = %v, want %v", got, tt.want)
				}
			},
		)
	}
}

func TestGetLimiterConfigsFromPerfCaseV2(t *testing.T) {
	type args struct {
		keyPrefix  string
		protocol   commonpb.Protocol
		perfCase   *commonpb.PerfCaseContentV2
		numberOfLG uint32
	}
	tests := []struct {
		name string
		args args
		want []limiter.ConfigV2
	}{
		{
			name: "",
			args: args{
				keyPrefix: "task_id:1",
				protocol:  commonpb.Protocol_PROTOCOL_TT,
				perfCase: &commonpb.PerfCaseContentV2{
					SetupSteps: []*commonpb.PerfCaseStepV2{
						{
							Name:   "获取财神入口信息",
							Method: "ga.api.wealth_god_logic.WealthGodLogic.GetWealthGodEntry",
							RateLimits: []*commonpb.RateLimitV2{
								{
									TargetRps:      1500,
									InitialRps:     1,
									ChangeDuration: "30s",
									TargetDuration: "3m",
								},
							},
						},
						{
							Name:   "上报财神降临房间任务完成",
							Method: "ga.api.wealth_god_logic.WealthGodLogic.ReportStayRoomMissionFinish",
							RateLimits: []*commonpb.RateLimitV2{
								{
									TargetRps:      1500,
									InitialRps:     1,
									ChangeDuration: "30s",
									TargetDuration: "3m",
								},
							},
						},
					},
					SerialSteps: []*commonpb.PerfCaseStepV2{
						{
							Name:   "开启财神宝箱奖励",
							Method: "ga.api.wealth_god_logic.WealthGodLogic.OpenWealthGodBoxReward",
							RateLimits: []*commonpb.RateLimitV2{
								{
									TargetRps:      2000,
									InitialRps:     1,
									ChangeDuration: "1s",
									TargetDuration: "60s",
								},
							},
						},
					},
					ParallelSteps: nil,
					TeardownSteps: nil,
				},
				numberOfLG: 20,
			},
			want: []limiter.ConfigV2{
				{
					RateLimits: []commontypes.RateLimitV2{
						{
							TargetRps:      (1500 + 20 - 1) / 20,
							InitialRps:     (1 + 20 - 1) / 20,
							ChangeDuration: "30s",
							TargetDuration: "3m",
						},
					},
					Key:  "task_id:1:TT:ga.api.wealth_god_logic.WealthGodLogic.GetWealthGodEntry",
					Type: limiter.Local,
				},
				{
					RateLimits: []commontypes.RateLimitV2{
						{
							TargetRps:      (1500 + 20 - 1) / 20,
							InitialRps:     (1 + 20 - 1) / 20,
							ChangeDuration: "30s",
							TargetDuration: "3m",
						},
					},
					Key:  "task_id:1:TT:ga.api.wealth_god_logic.WealthGodLogic.ReportStayRoomMissionFinish",
					Type: limiter.Local,
				},
				{
					RateLimits: []commontypes.RateLimitV2{
						{
							TargetRps:      (2000 + 20 - 1) / 20,
							InitialRps:     (1 + 20 - 1) / 20,
							ChangeDuration: "1s",
							TargetDuration: "60s",
						},
					},
					Key:  "task_id:1:TT:ga.api.wealth_god_logic.WealthGodLogic.OpenWealthGodBoxReward",
					Type: limiter.Local,
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				t.Logf("perfCase1: %s", protobuf.MarshalJSONIgnoreError(tt.args.perfCase))
				got := GetLimiterConfigsFromPerfCaseV2(
					tt.args.keyPrefix, tt.args.protocol, tt.args.perfCase, tt.args.numberOfLG,
				)
				t.Logf("perfCase2: %s", protobuf.MarshalJSONIgnoreError(tt.args.perfCase))
				t.Logf("GetLimiterConfigsFromPerfCaseV2() = %s", jsonx.MarshalIgnoreError(got))
				if !reflect.DeepEqual(got, tt.want) {
					t.Errorf("GetLimiterConfigsFromPerfCaseV2() = %v, want %v", got, tt.want)
				}
			},
		)
	}
}
