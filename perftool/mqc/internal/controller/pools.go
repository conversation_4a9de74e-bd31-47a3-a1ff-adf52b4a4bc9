package controller

import (
	"github.com/zeromicro/go-zero/core/lang"
	"golang.org/x/exp/maps"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pool"
)

var (
	errChPool = pool.NewPool[chan error](
		func() chan error {
			return make(chan error, constants.ConstDefaultMakeChannelSize)
		}, func(ch chan error) {
			drain(ch)
		},
	)

	placeholderChPool = pool.NewPool[chan lang.PlaceholderType](
		func() chan lang.PlaceholderType {
			return make(chan lang.PlaceholderType, constants.ConstDefaultMakeChannelSize)
		}, func(ch chan lang.PlaceholderType) {
			drain(ch)
		},
	)

	mapStrAnyPool = pool.NewPool[map[string]any](
		func() map[string]any {
			return make(map[string]any, constants.ConstDefaultMakeMapSize)
		}, func(m map[string]any) {
			maps.Clear(m)
		},
	)
)

// drain drains the channel
func drain[T any](channel <-chan T) {
	// drain the channel
	for {
		select {
		case <-channel:
			// read and discard the value
		default:
			// exit when the channel is empty
			return
		}
	}
}
