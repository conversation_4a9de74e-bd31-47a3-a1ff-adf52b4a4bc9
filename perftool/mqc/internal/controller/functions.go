package controller

import (
	"fmt"
	"net/url"
	"strings"
	"time"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/transport/http"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	commonpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
	commontypes "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/types"
	commonutils "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/utils"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/perftool/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/perftool/mqc/internal/clienttypes"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/perftool/mqc/internal/clienttypes/registry"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/perftool/mqc/internal/limiter"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/perftool/mqc/types"
)

const (
	globalKeyPrefix = "global"

	defaultTargetRPS            int64 = 1
	defaultTargetRPSOfAuth      int64 = 50
	defaultTargetRPSOfHeartbeat int64 = 500
	defaultInitialRPS           int64 = 1

	minAuthDuration = 10 * time.Second
)

// Deprecated: use `GetLimiterConfigsFromPerfKeepaliveV2` instead
func GetLimiterConfigsFromPerfKeepalive(
	keyPrefix string, protocol commonpb.Protocol, keepalive *commonpb.PerfKeepalive, numberOfLG uint32,
	useGlobal, useLocal bool,
) []limiter.Config {
	var (
		limiterType       = limiter.Distributed
		number      int64 = 1

		configs = make([]limiter.Config, 0, 2)
	)

	if useGlobal {
		keyPrefix = ""
	} else if useLocal {
		limiterType = limiter.Local
		number = utils.Max(1, int64(numberOfLG))
	}

	if auth := keepalive.GetAuth(); auth != nil {
		auth.Key = generateLimiterConfigKey(keyPrefix, protocol, common.ConstAuthAPIName)
		if rl := auth.GetRateLimit(); rl != nil {
			targetRPS := rl.GetTargetRps()
			if targetRPS == 0 {
				targetRPS = defaultTargetRPSOfAuth
			}

			config := limiter.Config{
				RateLimit: commontypes.RateLimit{
					TargetRps: (targetRPS + number - 1) / number, // round up
				},
				Key:  auth.GetKey(),
				Type: limiterType,
			}
			if rl.GetStepHeight() != 0 {
				initialRPS := rl.GetInitialRps()
				if initialRPS == 0 {
					initialRPS = defaultInitialRPS
				}

				config.InitialRps = (initialRPS + number - 1) / number         // round up
				config.StepHeight = (rl.GetStepHeight() + number - 1) / number // round up
				config.StepDuration = rl.GetStepDuration()
			}
			configs = append(configs, config)
		}
	}
	if heartbeat := keepalive.GetHeartbeat(); heartbeat != nil {
		heartbeat.Key = generateLimiterConfigKey(keyPrefix, protocol, common.ConstHeartbeatAPIName)
		if rl := heartbeat.GetRateLimit(); rl != nil {
			targetRPS := rl.GetTargetRps()
			if targetRPS == 0 {
				targetRPS = defaultTargetRPSOfHeartbeat
			}

			config := limiter.Config{
				RateLimit: commontypes.RateLimit{
					TargetRps: (targetRPS + number - 1) / number, // round up
				},
				Key:  heartbeat.GetKey(),
				Type: limiterType,
			}
			if rl.GetStepHeight() != 0 {
				initialRPS := rl.GetInitialRps()
				if initialRPS == 0 {
					initialRPS = defaultInitialRPS
				}

				config.InitialRps = (initialRPS + number - 1) / number         // round up
				config.StepHeight = (rl.GetStepHeight() + number - 1) / number // round up
				config.StepDuration = rl.GetStepDuration()
			}
			configs = append(configs, config)
		}
	}

	return configs
}

func GetLimiterConfigsFromPerfKeepaliveV2(
	keyPrefix string, protocol commonpb.Protocol, keepalive *commonpb.PerfRateLimits, numberOfLG uint32,
) []limiter.ConfigV2 {
	var (
		limiterType = limiter.Local
		number      = utils.Max(1, int64(numberOfLG))

		configs = make([]limiter.ConfigV2, 0, 2)
	)

	if auth := keepalive.GetAuth(); auth != nil {
		auth.Key = generateLimiterConfigKey(keyPrefix, protocol, common.ConstAuthAPIName)
		if rls := auth.GetRateLimits(); rls != nil {
			rateLimits := make([]commontypes.RateLimitV2, 0, len(rls))
			for _, rl := range rls {
				targetRPS := rl.GetTargetRps()
				initialRps := rl.GetInitialRps()
				if targetRPS == 0 {
					targetRPS = defaultTargetRPSOfAuth
				}

				// 根据虚拟用户数拆分`targetRps`和`initialRps`，并直接把传入的限流配置改为按虚拟用户数拆分后的配置
				rl.TargetRps = (targetRPS + number - 1) / number // round up
				rl.InitialRps = (initialRps + number - 1) / number
				rateLimits = append(
					rateLimits, commontypes.RateLimitV2{
						TargetRps:      rl.GetTargetRps(),
						InitialRps:     rl.GetInitialRps(),
						TargetDuration: rl.GetTargetDuration(),
						ChangeDuration: rl.GetChangeDuration(),
					},
				)
			}

			config := limiter.ConfigV2{
				Key:        auth.GetKey(),
				Type:       limiterType,
				RateLimits: rateLimits,
			}

			configs = append(configs, config)
		}
	}
	if heartbeat := keepalive.GetHeartbeat(); heartbeat != nil {
		heartbeat.Key = generateLimiterConfigKey(keyPrefix, protocol, common.ConstHeartbeatAPIName)
		if rls := heartbeat.GetRateLimits(); rls != nil {
			rateLimits := make([]commontypes.RateLimitV2, 0, len(rls))
			for _, rl := range rls {
				targetRPS := rl.GetTargetRps()
				initialRps := rl.GetInitialRps()
				if targetRPS == 0 {
					targetRPS = defaultTargetRPSOfHeartbeat
				}

				// 根据虚拟用户数拆分`targetRps`和`initialRps`，并直接把传入的限流配置改为按虚拟用户数拆分后的配置
				rl.TargetRps = (targetRPS + number - 1) / number // round up
				rl.InitialRps = (initialRps + number - 1) / number
				rateLimits = append(
					rateLimits, commontypes.RateLimitV2{
						TargetRps:      rl.GetTargetRps(),
						InitialRps:     rl.GetInitialRps(),
						TargetDuration: rl.GetTargetDuration(),
						ChangeDuration: rl.GetChangeDuration(),
					},
				)
			}

			config := limiter.ConfigV2{
				Key:        heartbeat.GetKey(),
				Type:       limiterType,
				RateLimits: rateLimits,
			}

			configs = append(configs, config)
		}
	}

	return configs
}

// Deprecated: use `GetLimiterConfigsFromPerfCaseV2` instead
func GetLimiterConfigsFromPerfCase(
	keyPrefix string, protocol commonpb.Protocol, perfCase *commonpb.PerfCaseContent, numberOfLG uint32, useLocal bool,
) []limiter.Config {
	var (
		setupSteps    = perfCase.GetSetupSteps()
		serialSteps   = perfCase.GetSerialSteps()
		parallelSteps = perfCase.GetParallelSteps()
		teardownSteps = perfCase.GetTeardownSteps()

		limiterType       = limiter.Distributed
		number      int64 = 1

		configs = make([]limiter.Config, 0, len(setupSteps)+len(serialSteps)+len(parallelSteps)+len(teardownSteps))
	)

	if useLocal {
		limiterType = limiter.Local
		number = utils.Max(1, int64(numberOfLG))
	}

	for _, steps := range [][]*commonpb.PerfCaseStep{setupSteps, serialSteps, parallelSteps, teardownSteps} {
		for _, step := range steps {
			if step == nil {
				continue
			}

			step.Key = generateLimiterConfigKey(keyPrefix, protocol, getAPINameFromPerfCaseStep(protocol, step))
			if rl := step.GetRateLimit(); rl != nil {
				targetRPS := rl.GetTargetRps()
				if targetRPS == 0 {
					targetRPS = defaultTargetRPS
				}

				rl.TargetRps = (targetRPS + number - 1) / number // round up
				config := limiter.Config{
					RateLimit: commontypes.RateLimit{
						TargetRps: rl.GetTargetRps(),
					},
					Key:  step.GetKey(),
					Type: limiterType,
				}

				stepHeight := rl.GetStepHeight()
				if stepHeight != 0 {
					initialRPS := rl.GetInitialRps()
					if initialRPS == 0 {
						initialRPS = defaultInitialRPS
					}

					rl.InitialRps = (initialRPS + number - 1) / number
					rl.StepHeight = (stepHeight + number - 1) / number
					config.InitialRps = rl.GetInitialRps()
					config.StepHeight = rl.GetStepHeight()
					config.StepDuration = rl.GetStepDuration()
				}

				configs = append(configs, config)
			}
		}
	}

	return configs
}

func GetLimiterConfigsFromPerfCaseV2(
	keyPrefix string, protocol commonpb.Protocol, perfCase *commonpb.PerfCaseContentV2, numberOfLG uint32,
) []limiter.ConfigV2 {
	var (
		setupSteps    = perfCase.GetSetupSteps()
		serialSteps   = perfCase.GetSerialSteps()
		parallelSteps = perfCase.GetParallelSteps()
		teardownSteps = perfCase.GetTeardownSteps()

		limiterType = limiter.Local
		number      = utils.Max(1, int64(numberOfLG))

		configs = make([]limiter.ConfigV2, 0, len(setupSteps)+len(serialSteps)+len(parallelSteps)+len(teardownSteps))
	)

	for _, steps := range [][]*commonpb.PerfCaseStepV2{setupSteps, serialSteps, parallelSteps, teardownSteps} {
		for _, step := range steps {
			if step == nil {
				continue
			}

			step.Key = generateLimiterConfigKey(keyPrefix, protocol, getAPINameFromPerfCaseStepV2(protocol, step))
			if rls := step.GetRateLimits(); rls != nil {
				rateLimits := make([]commontypes.RateLimitV2, 0, len(rls))
				for _, rl := range rls {
					targetRPS := rl.GetTargetRps()
					initialRps := rl.GetInitialRps()
					if targetRPS == 0 {
						targetRPS = defaultTargetRPSOfAuth
					}

					// 根据虚拟用户数拆分`targetRps`和`initialRps`，并直接把传入的限流配置改为按虚拟用户数拆分后的配置
					rl.TargetRps = (targetRPS + number - 1) / number // round up
					rl.InitialRps = (initialRps + number - 1) / number
					rateLimits = append(
						rateLimits, commontypes.RateLimitV2{
							TargetRps:      rl.GetTargetRps(),
							InitialRps:     rl.GetInitialRps(),
							TargetDuration: rl.GetTargetDuration(),
							ChangeDuration: rl.GetChangeDuration(),
						},
					)
				}

				config := limiter.ConfigV2{
					Key:        step.GetKey(),
					Type:       limiterType,
					RateLimits: rateLimits,
				}

				configs = append(configs, config)
			}
		}
	}

	return configs
}

func GetLimiterConfigsFromRateLimitV2(
	keyPrefix string, protocol commonpb.Protocol, rateLimits []*clienttypes.RateLimit, numberOfLG uint32,
) []limiter.ConfigV2 {
	var (
		limiterType = limiter.Local
		number      = utils.Max(1, int64(numberOfLG))

		configs = make([]limiter.ConfigV2, 0, len(rateLimits))
	)

	for _, rateLimit := range rateLimits {
		if rateLimit == nil {
			continue
		}

		key := generateLimiterConfigKey(keyPrefix, protocol, rateLimit.ApiName)
		limits := make([]commontypes.RateLimitV2, 0, len(rateLimit.Limits))
		for _, limit := range rateLimit.Limits {
			targetRPS := limit.GetTargetRps()
			initialRps := limit.GetInitialRps()
			if targetRPS == 0 {
				targetRPS = defaultTargetRPS
			}

			// 根据虚拟用户数拆分`targetRps`和`initialRps`，并直接把传入的限流配置改为按虚拟用户数拆分后的配置
			limits = append(
				limits, commontypes.RateLimitV2{
					TargetRps:      (targetRPS + number - 1) / number, // round up
					InitialRps:     (initialRps + number - 1) / number,
					ChangeDuration: limit.GetChangeDuration(),
					TargetDuration: limit.GetTargetDuration(),
				},
			)
		}

		configs = append(
			configs, limiter.ConfigV2{
				Key:        key,
				Type:       limiterType,
				RateLimits: limits,
			},
		)
	}

	return configs
}

func getAPINameFromPerfCaseStep(protocol commonpb.Protocol, step *commonpb.PerfCaseStep) string {
	switch protocol {
	case commonpb.Protocol_PROTOCOL_HTTP:
		url_ := step.GetUrl()
		if u, err := url.Parse(step.GetUrl()); u != nil && err == nil {
			u.ForceQuery = false
			u.RawQuery = ""
			u.Fragment = ""
			url_ = u.String()
		}

		return fmt.Sprintf("%s_%s", strings.ToUpper(step.GetMethod()), url_)
	case commonpb.Protocol_PROTOCOL_GRPC, commonpb.Protocol_PROTOCOL_TT:
		return step.GetMethod()
	default:
		return step.GetName()
	}
}

func getAPINameFromPerfCaseStepV2(protocol commonpb.Protocol, step *commonpb.PerfCaseStepV2) string {
	handleHTTPFunc := func(step *commonpb.PerfCaseStepV2) string {
		url_ := step.GetUrl()
		if u, err := url.Parse(step.GetUrl()); u != nil && err == nil {
			u.ForceQuery = false
			u.RawQuery = ""
			u.Fragment = ""
			url_ = u.String()
		}

		return fmt.Sprintf("%s_%s", strings.ToUpper(step.GetMethod()), url_)
	}

	switch protocol {
	case commonpb.Protocol_PROTOCOL_HTTP:
		return handleHTTPFunc(step)
	case commonpb.Protocol_PROTOCOL_GRPC:
		return step.GetMethod()
	case commonpb.Protocol_PROTOCOL_TT:
		if http.IsHTTPMethod(step.GetMethod()) {
			return handleHTTPFunc(step)
		}

		return step.GetMethod()
	default:
		return step.GetName()
	}
}

func generateLimiterConfigKey(keyPrefix string, protocol commonpb.Protocol, apiName string) string {
	if keyPrefix == "" {
		keyPrefix = globalKeyPrefix
	}

	return fmt.Sprintf("%s:%s:%s", keyPrefix, protobuf.GetEnumStringOf(protocol), apiName)
}

// Deprecated: use `calculate.ExecutionDurations` instead
type ExecutionDurations struct {
	authDuration     time.Duration
	setupDuration    time.Duration
	perfTestDuration time.Duration
	teardownDuration time.Duration
	totalDuration    time.Duration
}

func (d ExecutionDurations) String() string {
	return fmt.Sprintf(
		`{"auth_duration": "%s", "setup_duration": "%s", "perf_test_duration": "%s", "teardown_duration": "%s", "total_duration": "%s"}`,
		d.authDuration.String(), d.setupDuration.String(), d.perfTestDuration.String(), d.teardownDuration.String(),
		d.totalDuration.String(),
	)
}

// Deprecated: use `calculate.CalculateTotalCaseDuration` instead
func calculateMaxDuration(
	clientType types.ClientType, perfInfo *commonpb.PerfTestTaskInfo, perfCase *commonpb.PerfCaseContent,
	numberOfPerfData uint32,
) ExecutionDurations {
	// 1. calculate the max duration of auth process
	authDuration := time.Duration(0)
	if _, ok := registry.IsAuthClient(clientType); ok {
		authDuration += common.CalculateStepTimeByTimes(
			&commonpb.PerfCaseStep{
				Name:      "Auth",
				RateLimit: perfInfo.GetKeepalive().GetAuth().GetRateLimit(),
				Sleep:     "0s",
			}, perfInfo.GetTotalOfVu(), 1,
		)
		authDuration += clienttypes.WaiteRespTimeout
	} else {
		authDuration += minAuthDuration
	}

	// 2. calculate the max duration of setup steps
	setupDuration := time.Duration(0)
	setupSteps := perfCase.GetSetupSteps()
	numberOfSetupSteps := len(setupSteps)
	if numberOfSetupSteps > 0 {
		setupDuration += common.CalculateStepTimeByTimes(
			&commonpb.PerfCaseStep{
				Name:      "Setup",
				RateLimit: commonutils.GetMinRPSFromSteps(setupSteps),
				Sleep:     commonutils.GetMaxSleepFromSteps(setupSteps).String(),
			}, numberOfPerfData, 1,
		)
		setupDuration += time.Duration(numberOfSetupSteps) * clienttypes.WaiteRespTimeout
	}

	// 3. calculate the max duration of serial steps and parallel steps
	serialOrParallelDuration := time.Duration(0)
	serialSteps := perfCase.GetSerialSteps()
	parallelSteps := perfCase.GetParallelSteps()
	numberOfSerialSteps := len(serialSteps)
	numberOfParallelSteps := len(parallelSteps)
	switch perfInfo.GetExecuteMode() {
	case commonpb.PerfTaskExecutionMode_BY_TIMES: // by times
		times := perfInfo.GetTimes()

		serialDuration := time.Duration(0)
		if numberOfSerialSteps > 0 {
			serialDuration += common.CalculateStepTimeByTimes(
				&commonpb.PerfCaseStep{
					Name:      "Serial",
					RateLimit: commonutils.GetMinRPSFromSteps(serialSteps),
					Sleep:     commonutils.GetMaxSleepFromSteps(serialSteps).String(),
				}, numberOfPerfData, times,
			)
			serialDuration += time.Duration(numberOfSerialSteps) * clienttypes.WaiteRespTimeout
		}

		parallelDuration := time.Duration(0)
		for _, step := range parallelSteps {
			duration := common.CalculateStepTimeByTimes(step, numberOfPerfData, times)
			duration += time.Duration(times) * clienttypes.WaiteRespTimeout
			parallelDuration = utils.Max(parallelDuration, duration)
		}

		serialOrParallelDuration = utils.Max(serialDuration, parallelDuration)
	default: // by duration
		if numberOfSerialSteps > 0 || numberOfParallelSteps > 0 {
			serialOrParallelDuration = time.Duration(perfInfo.GetDuration()) * time.Second
		}
	}

	// 4. calculate the max duration of teardown steps
	teardownDuration := time.Duration(0)
	teardownSteps := perfCase.GetTeardownSteps()
	numberOfTeardownSteps := len(teardownSteps)
	if numberOfTeardownSteps > 0 {
		teardownDuration += common.CalculateStepTimeByTimes(
			&commonpb.PerfCaseStep{
				Name:      "Teardown",
				RateLimit: commonutils.GetMinRPSFromSteps(teardownSteps),
				Sleep:     commonutils.GetMaxSleepFromSteps(teardownSteps).String(),
			}, numberOfPerfData, 1,
		)
		teardownDuration += time.Duration(numberOfTeardownSteps) * clienttypes.WaiteRespTimeout
	}

	return ExecutionDurations{
		authDuration:     authDuration,
		setupDuration:    setupDuration,
		perfTestDuration: serialOrParallelDuration,
		teardownDuration: teardownDuration,
		totalDuration:    authDuration + setupDuration + serialOrParallelDuration + teardownDuration,
	}
}
