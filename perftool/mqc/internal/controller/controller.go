package controller

import (
	"context"
	"fmt"
	"os"
	"reflect"
	"strings"
	"sync"
	"sync/atomic"
	"time"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"
	"github.com/zeromicro/go-zero/core/lang"
	"github.com/zeromicro/go-zero/core/mr"
	"github.com/zeromicro/go-zero/core/service"
	"github.com/zeromicro/go-zero/core/threading"
	"github.com/zeromicro/go-zero/zrpc"
	"golang.org/x/exp/maps"
	"golang.org/x/exp/slices"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/security"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/timewheel"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/cache"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/calculate"
	commonpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/perftool/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/perftool/mqc/internal/aggregator"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/perftool/mqc/internal/broadcasttimer"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/perftool/mqc/internal/clienttypes"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/perftool/mqc/internal/clienttypes/registry"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/perftool/mqc/internal/limiter"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/perftool/mqc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/perftool/mqc/types"
	reporterpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
)

const logInterval = 30 * time.Second

var (
	exitSignalError  = errors.New("got an exit signal")
	waitTimeoutError = errors.New("wait timeout")
)

type Controller struct {
	*common.PerfTestLogger

	ctx    context.Context
	cancel context.CancelFunc

	svcCtx *svc.ServiceContext

	clientType  types.ClientType
	pm          *protobuf.ProtoManager
	rateLimiter *limiter.RateLimiterV2
	authAggr    *aggregator.Aggregator
	setupAggr   *aggregator.Aggregator
	authTimer   *broadcasttimer.Timer
	setupTimer  *broadcasttimer.Timer
	durTimer    *broadcasttimer.Timer

	perfInfo         *commonpb.PerfTestTaskInfo   // 压测任务信息
	perfCase         *commonpb.PerfCaseContentV2  // 压测用例
	perfData         *commonpb.PerfDataContent    // 压测数据
	generalConfig    *types.GeneralConfig         // 通用配置
	limiterConfigs   []limiter.Config             // 限流配置
	limiterConfigV2s []limiter.ConfigV2           // 限流配置
	durations        calculate.ExecutionDurations // 预估执行时长

	newSetupAggrOnce sync.Once                 // 保证只创建一次setup aggregator
	sentMP           *atomic.Bool              // 是否已发送监控参数
	finishCh         chan lang.PlaceholderType // 用于接收子控制器的完成信号
}

func NewController(
	ctx context.Context, svcCtx *svc.ServiceContext, info *commonpb.PerfTestTaskInfo,
) (
	*Controller, error,
) {
	var (
		pm       *protobuf.ProtoManager
		perfCase *commonpb.PerfCaseContentV2
		perfData *commonpb.PerfDataContent
		err      error
	)

	// get client type by protocol
	clientType, err := registry.GetClientTypeByProtocol(info.GetProtocol())
	if err != nil {
		return nil, errorx.Err(errorx.ValidateParamError, err.Error())
	}
	nullClient := registry.GetNullClient(clientType)

	// new a protobuf manager
	if len(info.GetProtobufTargets()) > 0 {
		pm, err = common.NewProtobufManager(info.GetProtobufTargets())
		if err != nil {
			return nil, err
		}
	}

	// get content of perf case
	perfCase = info.GetPerfCase()
	//perfCase, err = common.GetPerfCaseFromFile(info.GetPerfCasePath())
	//if err != nil {
	//	return nil, err
	//}

	// get content of perf data
	perfData, err = common.GetPerfDataFromFile(info.GetPerfDataPath())
	if err != nil {
		return nil, err
	}

	// convert `commonpb.GeneralConfig` to `types.GeneralConfig`
	generalConfig := common.ConvertToGeneralConfig(info.GetGeneralConfig())

	// auth and heartbeat
	cs1 := GetLimiterConfigsFromPerfKeepaliveV2(
		info.GetTaskId(), info.GetProtocol(), info.GetRateLimits(), info.GetTotalOfLg(),
	)
	// perf case steps
	cs2 := GetLimiterConfigsFromPerfCaseV2(
		info.GetTaskId(), info.GetProtocol(), perfCase, info.GetTotalOfLg(),
	)
	var cs3 []limiter.ConfigV2
	if v, ok := nullClient.(clienttypes.ILimiter); ok {
		cs3 = GetLimiterConfigsFromRateLimitV2(
			info.GetTaskId(), info.GetProtocol(), v.GetRateLimits(info.GetTargetEnv()), info.GetTotalOfLg(),
		)
	}

	// calculate the max duration of perf test
	options := make([]calculate.Option, 0, 2)
	switch info.GetProtocol() {
	case commonpb.Protocol_PROTOCOL_TT:
		options = append(options, calculate.WithAuthRateLimits(info.GetRateLimits().GetAuth().GetRateLimits()))
	case commonpb.Protocol_PROTOCOL_TT_AUTH:
		options = append(options, calculate.WithGlobalRateLimits(info.GetRateLimits().GetAuth().GetRateLimits()))
	default:
		options = append(options, calculate.WithoutAuth())
	}
	if info.GetTimes() > 0 {
		options = append(options, calculate.WithExecutionTimes(info.GetTimes()))
	}
	durations := calculate.CalculateTotalCaseDuration(perfCase, uint32(len(perfData.GetLines())), options...)
	if durations.PerfTestDuration == 0 {
		return nil, errorx.Errorf(
			errorx.ValidateParamError,
			"invalid perf case cause by the duration of perf test is 0, durations: %s",
			durations.String(),
		)
	}

	c := &Controller{
		PerfTestLogger: common.LoadGlobalLogger(),

		ctx:    ctx,
		svcCtx: svcCtx,

		clientType: clientType,
		pm:         pm,

		perfInfo:         info,
		perfCase:         perfCase,
		perfData:         perfData,
		generalConfig:    generalConfig,
		limiterConfigV2s: append(append(cs1, cs2...), cs3...),
		durations:        durations,

		sentMP:   new(atomic.Bool),
		finishCh: make(chan lang.PlaceholderType, 1),
	}
	err = c.sendMonitorParamsToReporter(nullClient, false)
	return c, err
}

func (c *Controller) sendMonitorParamsToReporter(client clienttypes.IClient, afterAuth bool) error {
	mon, ok := client.(clienttypes.IMonitor)
	if !ok {
		return nil
	}

	if c.sentMP.Load() {
		return nil
	}

	// the protobuf manager is not nil
	// the `afterAuth` flag is match with the monitor client
	// the monitor parameters have not been sent
	if c.pm != nil && afterAuth == mon.RequiredAfterAuth() && c.sentMP.CompareAndSwap(false, true) {
		// ATTENTION:
		// the `mon` is an uninitialized object while `afterAuth` is false,
		// therefore this is similar to calling the static method of a class in Python
		params := mon.GetMonitorParams(c.generalConfig, c.pm, c.perfCase)
		c.Infof("monitor parameters: %s", jsonx.MarshalIgnoreError(params))

		if c.svcCtx.Config.Mode == service.DevMode {
			return nil
		}
		if len(params.Commands) == 0 && len(params.GRPCPaths) == 0 && len(params.Services) == 0 {
			return nil
		}

		out, err := c.svcCtx.PerfReporter.UpdateMonitorURLOfPerfPlanRecord(
			c.ctx, &reporterpb.UpdateMonitorURLOfPerfPlanRecordReq{
				TaskId:       c.perfInfo.GetTaskId(),
				ExecuteId:    c.perfInfo.GetPlanExecuteId(),
				ProjectId:    c.perfInfo.GetProjectId(),
				Commands:     params.Commands,
				GrpcPaths:    params.GRPCPaths,
				HttpServices: params.Services,
			}, zrpc.WithCallTimeout(clienttypes.WaiteRespTimeout),
		)
		if err != nil {
			return err
		}

		c.Infof("the latest monitor url: %s", jsonx.MarshalIgnoreError(out.GetMonitorUrls()))
	}

	return nil
}

func (c *Controller) Run() error {
	var err error

	c.Infof(
		"total number of virtual users: %d, total number of load generators: %d, number of virtual users: %d",
		c.perfInfo.GetTotalOfVu(), c.perfInfo.GetTotalOfLg(), len(c.perfData.GetLines()),
	)

	if c.durations.AuthDuration > 0 {
		// auth aggregation time increased by 1 minute
		c.durations.AuthDuration += time.Minute
		// due to the increase in aggregation time, the total processing time also requires a corresponding extension
		c.durations.TotalDuration += time.Minute
	}
	if c.durations.SetupDuration > 0 {
		// setup aggregation time increased by 1 minute
		c.durations.SetupDuration += time.Minute
		// due to the increase in aggregation time, the total processing time also requires a corresponding extension
		c.durations.TotalDuration += time.Minute
	}
	timeout := c.durations.TotalDuration
	c.Infof(
		"timeout of perf task: %ds, timeout of context: %.2fs, durations of calculation by perftool: %s",
		c.perfInfo.GetTimeout(), timeout.Seconds(), c.durations.String(),
	)

	c.ctx, c.cancel = context.WithTimeout(c.ctx, timeout)
	defer c.cancel()

	// new a rate limiter based on api key
	c.rateLimiter = limiter.NewRateLimiterV2(
		c.ctx, c.svcCtx.ExitChannel, c.svcCtx.Redis, c.limiterConfigs, c.limiterConfigV2s, int64(timeout.Seconds()),
	)

	// new an auth aggregator
	authAggrKey := fmt.Sprintf("Auth::%s::%s", c.perfInfo.GetTaskId(), c.perfInfo.GetExecuteId())
	c.authAggr, err = aggregator.NewAggregator(
		c.ctx, c.svcCtx.ExitChannel, c.svcCtx.Redis, authAggrKey, uint64(c.perfInfo.GetTotalOfVu()),
		aggregator.WithExpireTime(uint64(timeout.Seconds())),
	)
	if err != nil {
		return err
	}

	if c.durations.AuthDuration > 0 {
		c.authTimer = broadcasttimer.NewTimer(c.durations.AuthDuration)
		defer c.authTimer.Stop()
	}
	if c.durations.SetupDuration > 0 {
		c.setupTimer = broadcasttimer.NewTimer(c.durations.SetupDuration)
		defer c.setupTimer.Stop()
	}

	c.durTimer = broadcasttimer.NewTimer(c.durations.PerfTestDuration)
	if c.perfInfo.GetExecuteMode() == commonpb.PerfTaskExecutionMode_BY_DURATION {
		defer c.durTimer.Stop()
	} else {
		c.durTimer.Stop()
	}

	count := new(atomic.Int64)
	for i, line := range c.perfData.GetLines() {
		if sc, err := NewSubController(c, line); err != nil {
			c.Errorf(
				"failed to new sub controller, index: %d, data: %s, error: %+v",
				i, protobuf.MarshalJSONIgnoreError(line), err,
			)
		} else {
			count.Add(1)
			threading.GoSafe(
				func() {
					defer func() {
						c.finishCh <- lang.Placeholder
					}()

					sc.Run()
				},
			)
		}
	}
	if count.Load() == 0 {
		return types.ErrZhAllClientCreateFailed(
			errorx.Err(
				errorx.NewObjectFailure, "cannot to run the perf task cause by no sub controller has been created",
			),
		)
	}

	logTicker := timewheel.NewTicker(logInterval)
	defer logTicker.Stop()

	for {
		select {
		case <-logTicker.C:
			c.Info("the perf task is in progress...")
		case <-c.finishCh:
			if v := count.Add(-1); v <= 0 {
				c.Info("all sub controllers have been executed completely")
				if err := c.validateAllVULogin(); err != nil {
					c.Errorf("failed to validate all virtual users login, error: %+v", err)
					return err
				}
				return nil
			}
		case <-c.svcCtx.ExitChannel:
			c.Warn("got an exit signal while running the perf task")
			return nil
		case <-c.ctx.Done():
			c.Infof("got a done signal while running the perf task, error: %+v", c.ctx.Err())
			return nil
		}
	}
}

func (c *Controller) validateAllVULogin() error {
	total, err := c.authAggr.Total()
	if err != nil {
		return err
	}

	passed, err := c.authAggr.Passed()
	if err != nil {
		return err
	}
	failed, err := c.authAggr.Failed()
	if err != nil {
		return err
	}
	skipped, err := c.authAggr.Skipped()
	if err != nil {
		return err
	}

	hostname, _ := os.Hostname()
	if passed == 0 && failed > 0 && (failed+skipped) == total {
		return types.ErrZhAllVULoginFailed(
			errors.Errorf(
				"all virtual users login failed, host: %s, passed: %d, failed: %d, skipped: %d",
				hostname, passed, failed, skipped,
			),
		)
	}

	return nil
}

func (c *Controller) newSetupAggregator() (err error) {
	c.newSetupAggrOnce.Do(
		func() {
			passed, _ := c.authAggr.Passed()
			skipped, _ := c.authAggr.Skipped()
			if passed+skipped == 0 {
				return
			}

			setupAggrKey := fmt.Sprintf("Setup::%s::%s", c.perfInfo.GetTaskId(), c.perfInfo.GetExecuteId())
			c.setupAggr, err = aggregator.NewAggregator(
				c.ctx, c.svcCtx.ExitChannel, c.svcCtx.Redis, setupAggrKey, passed+skipped,
				aggregator.WithExpireTime(uint64(c.durations.TotalDuration.Seconds())),
			)
		},
	)

	if err != nil {
		c.Errorf("failed to new setup aggregator, error: %+v", err)
	}
	return err
}

type SubController struct {
	ctl *Controller
	cli clienttypes.IClient

	// basicVarPool includes the following variables:
	// 1. variables in general configuration
	// 2. auth data in perf data
	// 3. user info from client
	// 4. variables extracted from the setup steps
	basicVarPool map[string]any
	perfData     *commonpb.PerfDataContent_Line
}

func NewSubController(ctl *Controller, perfData *commonpb.PerfDataContent_Line) (*SubController, error) {
	authData := perfData.GetAuthData().AsMap()
	basicVars := make(map[string]any, len(ctl.generalConfig.Variables)+len(authData))
	maps.Copy(basicVars, ctl.generalConfig.Variables)
	maps.Copy(basicVars, authData)

	c, err := registry.NewClient(
		ctl.ctx, ctl.svcCtx.ExitChannel, &clienttypes.CreateClientReq{
			Type:      ctl.clientType,
			URL:       ctl.generalConfig.BaseURL,
			Variables: basicVars,

			TaskID:          ctl.perfInfo.GetTaskId(),
			ExecuteID:       ctl.perfInfo.GetExecuteId(),
			HeartbeatConfig: ctl.perfInfo.GetRateLimits().GetHeartbeat(),
			ProtoManager:    ctl.pm,
			RateLimiter:     ctl.rateLimiter,
		},
	)
	if err != nil {
		return nil, errorx.Err(errorx.NewObjectFailure, err.Error())
	}

	return &SubController{
		ctl: ctl,
		cli: c,

		basicVarPool: basicVars,
		perfData:     perfData,
	}, nil
}

func (c *SubController) Run() {
	key := c.cli.Key()

	// execute the auth process
	if err := c.runAuthProcess(); err != nil {
		c.ctl.Errorf("failed to run auth process, key: %s, error: %+v", key, err)
		return
	}

	// execute the teardown steps
	defer c.runTeardownSteps()

	// execute the setup steps
	if err := c.runSetupSteps(); err != nil {
		return
	}

	waitCh := placeholderChPool.Get()
	defer placeholderChPool.Put(waitCh)

	// execute the serial steps and parallel steps
	count := new(atomic.Int32)
	count.Add(2)
	threading.GoSafe(
		func() {
			defer func() {
				waitCh <- lang.Placeholder
			}()

			c.runSerialSteps()
		},
	)
	threading.GoSafe(
		func() {
			defer func() {
				waitCh <- lang.Placeholder
			}()

			c.runParallelSteps()
		},
	)

	for {
		select {
		case <-c.ctl.svcCtx.ExitChannel:
			c.ctl.Debugf("got an exit signal while running the perf case, key: %s", key)
			return
		case <-c.ctl.ctx.Done():
			c.ctl.Debugf("got a done signal while running the perf case, key: %s, error: %+v", key, c.ctl.ctx.Err())
			return
		case <-waitCh:
			if v := count.Add(-1); v <= 0 {
				c.ctl.Debugf("the serial steps and parallel steps have been completed, key: %s", key)
				return
			}
		}
	}
}

func (c *SubController) runAuthProcess() error {
	key := c.cli.Key()

	auth, ok := c.cli.(clienttypes.IAuth)
	if ok {
		ret, err := c.ctl.rateLimiter.WaitForAllow(c.ctl.ctx, c.ctl.perfInfo.GetRateLimits().GetAuth().GetKey())
		if err != nil {
			_ = c.ctl.authAggr.IncrFailed()
			return err
		} else if ret != nil && ret.Allowed < 1 {
			_ = c.ctl.authAggr.IncrFailed()
			return errorx.Errorf(
				errorx.ProhibitedBehavior,
				"cannot to run the auth process due to exceeding traffic limit, key: %s",
				key,
			)
		}

		c.ctl.Infof("allow to run the auth process, key: %s", key)
		if err = auth.Login(c.ctl.ctx); err != nil {
			_ = c.ctl.authAggr.IncrFailed()
			return err
		}

		// save the user info to variable pool
		maps.Copy(c.basicVarPool, auth.GetUserInfo())
		_ = c.ctl.authAggr.IncrPassed()

		if err = c.ctl.sendMonitorParamsToReporter(c.cli, true); err != nil {
			c.ctl.Errorf("failed to send monitor params to reporter, key: %s, error: %+v", key, err)
		}
	} else {
		_ = c.ctl.authAggr.IncrSkipped()
	}

	if ok && c.ctl.authTimer != nil {
		select {
		case <-c.ctl.svcCtx.ExitChannel:
			c.ctl.Warnf("got an exit signal while running the auth process, key: %s", key)
			return exitSignalError
		case <-c.ctl.ctx.Done():
			err := c.ctl.ctx.Err()
			c.ctl.Debugf("got a done signal while running the auth process, key: %s, error: %+v", key, err)
			return err
		case <-c.ctl.authAggr.Finished():
			if err := c.ctl.authAggr.Err(); err != nil {
				return err
			}

			c.ctl.Debugf(
				"the aggregator of auth process reached the target value, key: %s, target: %d",
				key, c.ctl.authAggr.Target(),
			)
			return nil
		case <-c.ctl.authTimer.Start():
			c.ctl.Debugf(
				"wait for a finished signal of the auth process aggregator timeout, key: %s, duration: %s",
				key, c.ctl.durations.AuthDuration.String(),
			)
			return nil
		}
	} else {
		select {
		case <-c.ctl.svcCtx.ExitChannel:
			c.ctl.Warnf("got an exit signal while running the auth process, key: %s", key)
			return exitSignalError
		case <-c.ctl.ctx.Done():
			err := c.ctl.ctx.Err()
			c.ctl.Debugf("got a done signal while running the auth process, key: %s, error: %+v", key, err)
			return err
		case <-c.ctl.authAggr.Finished():
			if err := c.ctl.authAggr.Err(); err != nil {
				return err
			}

			c.ctl.Debugf(
				"the aggregator of auth process reached the target value, key: %s, target: %d",
				key, c.ctl.authAggr.Target(),
			)
			return nil
		}
	}
}

func (c *SubController) runSetupSteps() (err error) {
	var (
		key          = c.cli.Key()
		steps        = c.ctl.perfCase.GetSetupSteps()
		requestsData = c.perfData.GetRequestData()

		index = 0
	)

	if len(steps) == 0 {
		c.ctl.Debugf("there are no setup steps to execute, key: %s", key)
		return nil
	}

	passed, _ := c.ctl.authAggr.Passed()
	skipped, _ := c.ctl.authAggr.Skipped()
	if passed+skipped > 0 && c.ctl.setupTimer != nil {
		defer func() {
			if e := c.ctl.newSetupAggregator(); e != nil || c.ctl.setupAggr == nil {
				return
			}

			if err != nil {
				_ = c.ctl.setupAggr.IncrFailed()
				return
			} else {
				_ = c.ctl.setupAggr.IncrPassed()
			}

			select {
			case <-c.ctl.svcCtx.ExitChannel:
				c.ctl.Warnf("got an exit signal while waiting for the setup steps to be completed, key: %s", key)
			case <-c.ctl.ctx.Done():
				c.ctl.Debugf(
					"got a done signal while waiting for the setup steps to be completed, key: %s, error: %+v",
					key, c.ctl.ctx.Err(),
				)
			case <-c.ctl.setupAggr.Finished():
				if e := c.ctl.setupAggr.Err(); e != nil {
					return
				}

				c.ctl.Debugf(
					"the aggregator of setup steps reached the target value, key: %s, target: %d",
					key, c.ctl.setupAggr.Target(),
				)
			case <-c.ctl.setupTimer.Start():
				c.ctl.Debugf(
					"wait for a finished signal of the setup steps aggregator timeout, key: %s, duration: %s",
					key, c.ctl.durations.SetupDuration.String(),
				)
			}
		}()
	}

	if len(requestsData.GetValues()) > 0 {
		// merge request data to variable pool
		maps.Copy(
			c.basicVarPool,
			common.TemplateExecuteByMap(requestsData.GetValues()[0].GetStructValue().AsMap(), c.basicVarPool),
		)
	}

	for {
		select {
		case <-c.ctl.svcCtx.ExitChannel:
			c.ctl.Warnf("got an exit signal while running the setup steps, key: %s", key)
			return nil
		case <-c.ctl.ctx.Done():
			c.ctl.Debugf("got a done signal while running the setup steps, key: %s, error: %+v", key, c.ctl.ctx.Err())
			return nil
		default:
			if !c.cli.Enabled() {
				c.ctl.Warnf("found the client to be disabled while running the setup steps, key: %s", key)
				return errors.Errorf("found the client to be disabled while running the setup steps, key: %s", key)
			}

			if index >= len(steps) {
				return nil
			}

			step := steps[index]
			if err = c.runSingleStep(
				c.ctl.ctx, commonpb.PerfCaseStepType_PerfCaseStepType_SETUP, step, c.basicVarPool,
			); err != nil {
				if !errors.Is(err, limiter.ExitSignalError) && !errors.Is(err, limiter.DoneSignalError) {
					c.ctl.Errorf("failed to run setup step, key: %s, step: %s, error: %+v", key, step.GetName(), err)
				}

				return err
			}

			index += 1
		}
	}
}

func (c *SubController) runSerialSteps() {
	var (
		key     = c.cli.Key()
		steps   = c.ctl.perfCase.GetSerialSteps()
		stepLen = len(steps)
	)

	if stepLen == 0 {
		c.ctl.Debugf("there are no serial steps to execute, key: %s", key)
		return
	}

	var (
		index          = 0
		times          = 0
		dataLen        = len(c.perfData.GetRequestData().GetValues())
		executeMode    = c.ctl.perfInfo.GetExecuteMode()
		targetDuration = c.ctl.perfInfo.GetDuration()
		targetTimes    = c.ctl.perfInfo.GetTimes()

		variables map[string]any
	)
	defer func() {
		if variables != nil {
			mapStrAnyPool.Put(variables)
		}
	}()

	for {
		select {
		case <-c.ctl.svcCtx.ExitChannel:
			c.ctl.Warnf("got an exit signal while running the serial steps, key: %s", key)
			return
		case <-c.ctl.ctx.Done():
			c.ctl.Debugf("got a done signal while running the serial steps, key: %s, error: %+v", key, c.ctl.ctx.Err())
			return
		case <-c.ctl.durTimer.Start():
			c.ctl.Debugf(
				"execution time of the serial steps reaches the target value, key: %s, duration: %ds",
				key, targetDuration,
			)
			return
		default:
			if !c.cli.Enabled() {
				c.ctl.Warnf("found the client to be disabled while running the serial steps, key: %s", key)
				return
			}

			if index >= stepLen {
				index = 0
			}
			if index == 0 {
				if executeMode == commonpb.PerfTaskExecutionMode_BY_TIMES && targetTimes <= uint32(times) {
					c.ctl.Debugf(
						"execution count of the serial steps reaches the target value, key: %s, times: %d", key, times,
					)
					return
				}

				if variables != nil {
					mapStrAnyPool.Put(variables)
				}
				variables = mapStrAnyPool.Get()
				maps.Copy(variables, c.basicVarPool)

				if dataLen > 0 {
					maps.Copy(
						variables, common.TemplateExecuteByMap(
							c.perfData.GetRequestData().GetValues()[times%dataLen].GetStructValue().AsMap(), variables,
						),
					)
				}

				times += 1
			}

			step := steps[index]
			if err := c.runSingleStep(
				c.ctl.ctx, commonpb.PerfCaseStepType_PerfCaseStepType_SERIAL, step, variables,
			); err != nil {
				if !errors.Is(err, limiter.ExitSignalError) && !errors.Is(err, limiter.DoneSignalError) {
					c.ctl.Errorf(
						"failed to run serial step, key: %s, step: %s, error: %+v", key, step.GetName(), err,
					)
				}

				// failed to execute the serial step, skipping the remaining steps, and proceeding to the next iteration
				index = stepLen
			} else {
				index += 1
			}
		}
	}
}

func (c *SubController) runParallelSteps() {
	var (
		key     = c.cli.Key()
		steps   = c.ctl.perfCase.GetParallelSteps()
		stepLen = len(steps)
	)

	if stepLen == 0 {
		c.ctl.Debugf("there are no parallel steps to execute, key: %s", key)
		return
	}

	var (
		executeMode    = c.ctl.perfInfo.GetExecuteMode()
		targetDuration = c.ctl.perfInfo.GetDuration()
		targetTimes    = c.ctl.perfInfo.GetTimes()
	)
	_ = mr.MapReduceVoid[*commonpb.PerfCaseStepV2, any](
		func(source chan<- *commonpb.PerfCaseStepV2) {
			for _, step := range steps {
				if step != nil {
					source <- step
				}
			}
		}, func(item *commonpb.PerfCaseStepV2, writer mr.Writer[any], cancel func(error)) {
			var (
				times   = 0
				dataLen = len(c.perfData.GetRequestData().GetValues())
				name    = item.GetName()

				runSingleStep = func() error {
					variables := mapStrAnyPool.Get()
					defer mapStrAnyPool.Put(variables)
					maps.Copy(variables, c.basicVarPool)

					if dataLen > 0 {
						maps.Copy(
							variables, common.TemplateExecuteByMap(
								c.perfData.GetRequestData().GetValues()[times%dataLen].GetStructValue().AsMap(),
								variables,
							),
						)
					}

					return c.runSingleStep(
						c.ctl.ctx, commonpb.PerfCaseStepType_PerfCaseStepType_PARALLEL, item, variables,
					)
				}
			)

			for {
				select {
				case <-c.ctl.svcCtx.ExitChannel:
					c.ctl.Warnf(
						"got an exit signal while running the parallel step, key: %s, step: %s", key, name,
					)
					return
				case <-c.ctl.ctx.Done():
					c.ctl.Debugf(
						"got a done signal while running the parallel step, key: %s, step: %s, error: %+v",
						key, name, c.ctl.ctx.Err(),
					)
					return
				case <-c.ctl.durTimer.Start():
					c.ctl.Debugf(
						"execution time of the parallel step reaches the target value, key: %s, step: %s, duration: %ds",
						key, name, targetDuration,
					)
					return
				default:
					if !c.cli.Enabled() {
						c.ctl.Warnf("found the client to be disabled while running the parallel steps, key: %s", key)
						return
					}

					times += 1
					if err := runSingleStep(); err != nil {
						if !errors.Is(err, limiter.ExitSignalError) && !errors.Is(err, limiter.DoneSignalError) {
							c.ctl.Errorf("failed to run parallel step, key: %s, step: %s, error: %+v", key, name, err)
						}
					}

					if executeMode == commonpb.PerfTaskExecutionMode_BY_TIMES && targetTimes <= uint32(times) {
						c.ctl.Debugf(
							"execution count of the parallel step reaches the target value, key: %s, step: %s, times: %d",
							key, name, times,
						)
						return
					}
				}
			}
		}, func(pipe <-chan any, cancel func(error)) {
		}, mr.WithContext(c.ctl.ctx), mr.WithWorkers(stepLen),
	)
}

func (c *SubController) runTeardownSteps() {
	var (
		key          = c.cli.Key()
		steps        = c.ctl.perfCase.GetTeardownSteps()
		requestsData = c.perfData.GetRequestData()
	)

	if len(steps) == 0 {
		c.ctl.Debugf("there are no teardown steps to execute, key: %s", key)
		return
	}

	variables := mapStrAnyPool.Get()
	defer mapStrAnyPool.Put(variables)
	maps.Copy(variables, c.basicVarPool)

	if len(requestsData.GetValues()) > 0 {
		// merge request data to variable pool
		maps.Copy(
			variables, common.TemplateExecuteByMap(requestsData.GetValues()[0].GetStructValue().AsMap(), variables),
		)
	}

	ctx, cancel := context.WithTimeout(context.Background(), c.ctl.durations.TeardownDuration)
	defer cancel()

	for _, step := range steps {
		if !c.cli.Enabled() {
			c.ctl.Warnf("found the client to be disabled while running the teardown steps, key: %s", key)
			return
		}

		if err := c.runSingleStep(
			ctx, commonpb.PerfCaseStepType_PerfCaseStepType_TEARDOWN, step, variables,
		); err != nil && !errors.Is(err, limiter.ExitSignalError) && !errors.Is(err, limiter.DoneSignalError) {
			c.ctl.Errorf("failed to run teardown step, key: %s, step: %s, error: %+v", key, step.GetName(), err)
		}
	}
}

func (c *SubController) runSingleStep(
	ctx context.Context, stepType commonpb.PerfCaseStepType, step *commonpb.PerfCaseStepV2, variables map[string]any,
) error {
	var (
		key      = c.cli.Key()
		st       = strings.ToLower(protobuf.GetEnumStringOf(stepType))
		stepName = step.GetName()

		req  clienttypes.IRequest
		resp clienttypes.IResponse
		err  error
	)

	if ctx == nil {
		ctx = c.ctl.ctx
	}

	headers := maps.Clone(step.GetHeaders())
	if headers == nil {
		headers = make(map[string]string)
	}
	security.HandleHeaders(headers)

	req, err = c.cli.NewRequest(
		&clienttypes.CreateRequestReq{
			Step: &commonpb.PerfCaseStepV2{
				Name:       stepName,
				RateLimits: slices.Clone(step.GetRateLimits()),
				Url:        step.GetUrl(),
				Method:     step.GetMethod(),
				Headers:    headers,
				Body:       step.GetBody(),
				Exports:    slices.Clone(step.GetExports()),
				Sleep:      step.GetSleep(),
				Key:        step.GetKey(),
				Service:    step.GetService(),
				Namespace:  step.GetNamespace(),
				Cmd:        step.GetCmd(),
				GrpcPath:   step.GetGrpcPath(),
				Deprecated: step.GetDeprecated(),
			},
			Variables: variables,
		},
	)
	if err != nil {
		return errors.Wrapf(
			errorx.Err(errorx.NewObjectFailure, err.Error()),
			"failed to new a request of %s step, key: %s, step: %s, error: %+v",
			st, key, stepName, err,
		)
	}

	ret, err := c.ctl.rateLimiter.WaitForAllow(ctx, step.GetKey())
	if err != nil {
		return err
	} else if ret != nil && ret.Allowed < 1 {
		return errorx.Errorf(
			errorx.ProhibitedBehavior,
			"cannot to send a request of %s step due to exceeding traffic limit, key: %s, step: %s",
			st, key, stepName,
		)
	}

	defer func() {
		if step.GetSleep() != "" {
			time.Sleep(cache.Parse(step.GetSleep()))
		}
	}()

	resp, err = c.cli.Send(ctx, req)
	if err != nil {
		return errors.Wrapf(
			errorx.Err(errorx.CallExternalAPIFailure, err.Error()),
			"failed to send a request of %s step, key: %s, step: %s, error: %+v",
			st, key, stepName, err,
		)
	}

	if rv := reflect.ValueOf(resp); !rv.IsValid() || rv.IsNil() {
		return errorx.Errorf(
			errorx.CallExternalAPIFailure,
			"got a nil response of %s step, key: %s, step: %s",
			st, key, stepName,
		)
	}

	if err = resp.Error(); err != nil {
		return errors.Wrapf(
			errorx.Err(errorx.CallExternalAPIFailure, err.Error()),
			"got an error from the response of %s step, key: %s, step: %s, %s, %s, error: %+v",
			st, key, stepName, resp.Key(), resp.Result(), err,
		)
	}

	if stepType != commonpb.PerfCaseStepType_PerfCaseStepType_PARALLEL && len(step.GetExports()) > 0 {
		tmp := mapStrAnyPool.Get()
		defer mapStrAnyPool.Put(tmp)

		resp.Extract(step.GetExports(), tmp)
		maps.Copy(variables, tmp)
		c.ctl.Debugf(
			"the export data of %s step, key: %s, step: %s, data: %s",
			st, key, stepName, jsonx.MarshalIgnoreError(tmp),
		)
	}

	return nil
}
