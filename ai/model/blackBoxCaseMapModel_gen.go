// Code generated by goctl. DO NOT EDIT.
// versions:
//  goctl version: 1.7.4

package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlc"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
)

var (
	blackBoxCaseMapTableName           = "`black_box_case_map`"
	blackBoxCaseMapFieldNames          = builder.RawFieldNames(&BlackBoxCaseMap{})
	blackBoxCaseMapRows                = strings.Join(blackBoxCaseMapFieldNames, ",")
	blackBoxCaseMapRowsExpectAutoSet   = strings.Join(stringx.Remove(blackBoxCaseMapFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), ",")
	blackBoxCaseMapRowsWithPlaceHolder = strings.Join(stringx.Remove(blackBoxCaseMapFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), "=?,") + "=?"

	cacheArtificialIntelligenceBlackBoxCaseMapIdPrefix     = "cache:artificialIntelligence:blackBoxCaseMap:id:"
	cacheArtificialIntelligenceBlackBoxCaseMapCaseIdPrefix = "cache:artificialIntelligence:blackBoxCaseMap:caseId:"
)

type (
	blackBoxCaseMapModel interface {
		Insert(ctx context.Context, session sqlx.Session, data *BlackBoxCaseMap) (sql.Result, error)
		FindOne(ctx context.Context, id int64) (*BlackBoxCaseMap, error)
		FindOneByCaseId(ctx context.Context, caseId string) (*BlackBoxCaseMap, error)
		Update(ctx context.Context, session sqlx.Session, data *BlackBoxCaseMap) (sql.Result, error)
		Delete(ctx context.Context, session sqlx.Session, id int64) error
		LogicDelete(ctx context.Context, session sqlx.Session, id int64) error
	}

	defaultBlackBoxCaseMapModel struct {
		sqlc.CachedConn
		table string
	}

	BlackBoxCaseMap struct {
		Id             int64          `db:"id"`              // 自增ID
		ProjectId      string         `db:"project_id"`      // 项目ID
		DirId          string         `db:"dir_id"`          // 目录ID
		CaseId         string         `db:"case_id"`         // 用例ID
		MapId          string         `db:"map_id"`          // 评审导图ID
		Data           string         `db:"data"`            // 评审导图数据
		Tags           string         `db:"tags"`            // 术语标签
		Experiences    string         `db:"experiences"`     // 测试经验
		Deleted        int64          `db:"deleted"`         // 逻辑删除标识（未删除、已删除）
		CreatedBy      string         `db:"created_by"`      // 创建者的用户ID
		UpdatedBy      string         `db:"updated_by"`      // 最近一次更新者的用户ID
		DeletedBy      sql.NullString `db:"deleted_by"`      // 删除者的用户ID
		CreatedAt      time.Time      `db:"created_at"`      // 创建时间
		UpdatedAt      time.Time      `db:"updated_at"`      // 更新时间
		DeletedAt      sql.NullTime   `db:"deleted_at"`      // 删除时间
		UsedKnowledge  sql.NullString `db:"used_knowledge"`  // 已使用的知识
		RecallDocument sql.NullString `db:"recall_document"` // 召回文档
	}
)

func newBlackBoxCaseMapModel(conn sqlx.SqlConn, c cache.CacheConf, opts ...cache.Option) *defaultBlackBoxCaseMapModel {
	return &defaultBlackBoxCaseMapModel{
		CachedConn: sqlc.NewConn(conn, c, opts...),
		table:      "`black_box_case_map`",
	}
}

func (m *defaultBlackBoxCaseMapModel) Delete(ctx context.Context, session sqlx.Session, id int64) error {
	data, err := m.FindOne(ctx, id)
	if err != nil {
		return err
	}

	artificialIntelligenceBlackBoxCaseMapCaseIdKey := fmt.Sprintf("%s%v", cacheArtificialIntelligenceBlackBoxCaseMapCaseIdPrefix, data.CaseId)
	artificialIntelligenceBlackBoxCaseMapIdKey := fmt.Sprintf("%s%v", cacheArtificialIntelligenceBlackBoxCaseMapIdPrefix, id)
	_, err = m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("delete from %s where `id` = ?", m.table)
		if session != nil {
			return session.ExecCtx(ctx, query, id)
		}
		return conn.ExecCtx(ctx, query, id)
	}, artificialIntelligenceBlackBoxCaseMapCaseIdKey, artificialIntelligenceBlackBoxCaseMapIdKey)
	return err
}

func (m *defaultBlackBoxCaseMapModel) LogicDelete(ctx context.Context, session sqlx.Session, id int64) error {
	data, err := m.FindOne(ctx, id)
	if err != nil {
		return err
	}

	artificialIntelligenceBlackBoxCaseMapCaseIdKey := fmt.Sprintf("%s%v", cacheArtificialIntelligenceBlackBoxCaseMapCaseIdPrefix, data.CaseId)
	artificialIntelligenceBlackBoxCaseMapIdKey := fmt.Sprintf("%s%v", cacheArtificialIntelligenceBlackBoxCaseMapIdPrefix, id)
	_, err = m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set `deleted` = ?, `deleted_at` = ? where `id` = ?", m.table)
		if session != nil {
			return session.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
		}
		return conn.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
	}, artificialIntelligenceBlackBoxCaseMapCaseIdKey, artificialIntelligenceBlackBoxCaseMapIdKey)
	return err
}

func (m *defaultBlackBoxCaseMapModel) FindOne(ctx context.Context, id int64) (*BlackBoxCaseMap, error) {
	artificialIntelligenceBlackBoxCaseMapIdKey := fmt.Sprintf("%s%v", cacheArtificialIntelligenceBlackBoxCaseMapIdPrefix, id)
	var resp BlackBoxCaseMap
	err := m.QueryRowCtx(ctx, &resp, artificialIntelligenceBlackBoxCaseMapIdKey, func(ctx context.Context, conn sqlx.SqlConn, v any) error {
		query := fmt.Sprintf("select %s from %s where `id` = ? and `deleted` = ? limit 1", blackBoxCaseMapRows, m.table)
		return conn.QueryRowCtx(ctx, v, query, id, constants.NotDeleted)
	})
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultBlackBoxCaseMapModel) FindOneByCaseId(ctx context.Context, caseId string) (*BlackBoxCaseMap, error) {
	artificialIntelligenceBlackBoxCaseMapCaseIdKey := fmt.Sprintf("%s%v", cacheArtificialIntelligenceBlackBoxCaseMapCaseIdPrefix, caseId)
	var resp BlackBoxCaseMap
	err := m.QueryRowIndexCtx(ctx, &resp, artificialIntelligenceBlackBoxCaseMapCaseIdKey, m.formatPrimary, func(ctx context.Context, conn sqlx.SqlConn, v any) (i any, e error) {
		query := fmt.Sprintf("select %s from %s where `case_id` = ? and `deleted` = ? limit 1", blackBoxCaseMapRows, m.table)
		if err := conn.QueryRowCtx(ctx, &resp, query, caseId, constants.NotDeleted); err != nil {
			return nil, err
		}
		return resp.Id, nil
	}, m.queryPrimary)
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultBlackBoxCaseMapModel) Insert(ctx context.Context, session sqlx.Session, data *BlackBoxCaseMap) (sql.Result, error) {
	artificialIntelligenceBlackBoxCaseMapCaseIdKey := fmt.Sprintf("%s%v", cacheArtificialIntelligenceBlackBoxCaseMapCaseIdPrefix, data.CaseId)
	artificialIntelligenceBlackBoxCaseMapIdKey := fmt.Sprintf("%s%v", cacheArtificialIntelligenceBlackBoxCaseMapIdPrefix, data.Id)

	return m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", m.table, blackBoxCaseMapRowsExpectAutoSet)
		if session != nil {
			return session.ExecCtx(ctx, query, data.ProjectId, data.DirId, data.CaseId, data.MapId, data.Data, data.Tags, data.Experiences, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy, data.UsedKnowledge, data.RecallDocument)
		}
		return conn.ExecCtx(ctx, query, data.ProjectId, data.DirId, data.CaseId, data.MapId, data.Data, data.Tags, data.Experiences, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy, data.UsedKnowledge, data.RecallDocument)
	}, artificialIntelligenceBlackBoxCaseMapCaseIdKey, artificialIntelligenceBlackBoxCaseMapIdKey)
}

func (m *defaultBlackBoxCaseMapModel) Update(ctx context.Context, session sqlx.Session, newData *BlackBoxCaseMap) (sql.Result, error) {
	data, err := m.FindOne(ctx, newData.Id)
	if err != nil {
		return nil, err
	}

	artificialIntelligenceBlackBoxCaseMapCaseIdKey := fmt.Sprintf("%s%v", cacheArtificialIntelligenceBlackBoxCaseMapCaseIdPrefix, data.CaseId)
	artificialIntelligenceBlackBoxCaseMapIdKey := fmt.Sprintf("%s%v", cacheArtificialIntelligenceBlackBoxCaseMapIdPrefix, data.Id)

	return m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, blackBoxCaseMapRowsWithPlaceHolder)
		if session != nil {
			return session.ExecCtx(ctx, query, newData.ProjectId, newData.DirId, newData.CaseId, newData.MapId, newData.Data, newData.Tags, newData.Experiences, newData.Deleted, newData.CreatedBy, newData.UpdatedBy, newData.DeletedBy, newData.UsedKnowledge, newData.RecallDocument, newData.Id)
		}
		return conn.ExecCtx(ctx, query, newData.ProjectId, newData.DirId, newData.CaseId, newData.MapId, newData.Data, newData.Tags, newData.Experiences, newData.Deleted, newData.CreatedBy, newData.UpdatedBy, newData.DeletedBy, newData.UsedKnowledge, newData.RecallDocument, newData.Id)
	}, artificialIntelligenceBlackBoxCaseMapCaseIdKey, artificialIntelligenceBlackBoxCaseMapIdKey)
}

func (m *defaultBlackBoxCaseMapModel) formatPrimary(primary any) string {
	return fmt.Sprintf("%s%v", cacheArtificialIntelligenceBlackBoxCaseMapIdPrefix, primary)
}

func (m *defaultBlackBoxCaseMapModel) queryPrimary(ctx context.Context, conn sqlx.SqlConn, v, primary any) error {
	query := fmt.Sprintf("select %s from %s where `id` = ? limit 1", blackBoxCaseMapRows, m.table)
	return conn.QueryRowCtx(ctx, v, query, primary)
}

func (m *defaultBlackBoxCaseMapModel) tableName() string {
	return m.table
}
