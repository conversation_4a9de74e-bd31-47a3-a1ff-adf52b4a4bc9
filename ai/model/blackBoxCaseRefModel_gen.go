// Code generated by goctl. DO NOT EDIT.
// versions:
//  goctl version: 1.7.4

package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlc"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
)

var (
	blackBoxCaseRefTableName           = "`black_box_case_ref`"
	blackBoxCaseRefFieldNames          = builder.RawFieldNames(&BlackBoxCaseRef{})
	blackBoxCaseRefRows                = strings.Join(blackBoxCaseRefFieldNames, ",")
	blackBoxCaseRefRowsExpectAutoSet   = strings.Join(stringx.Remove(blackBoxCaseRefFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), ",")
	blackBoxCaseRefRowsWithPlaceHolder = strings.Join(stringx.Remove(blackBoxCaseRefFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), "=?,") + "=?"

	cacheArtificialIntelligenceBlackBoxCaseRefIdPrefix        = "cache:artificialIntelligence:blackBoxCaseRef:id:"
	cacheArtificialIntelligenceBlackBoxCaseRefCaseRefIdPrefix = "cache:artificialIntelligence:blackBoxCaseRef:caseRefId:"
)

type (
	blackBoxCaseRefModel interface {
		Insert(ctx context.Context, session sqlx.Session, data *BlackBoxCaseRef) (sql.Result, error)
		FindOne(ctx context.Context, id uint64) (*BlackBoxCaseRef, error)
		FindOneByCaseRefId(ctx context.Context, caseRefId string) (*BlackBoxCaseRef, error)
		Update(ctx context.Context, session sqlx.Session, data *BlackBoxCaseRef) (sql.Result, error)
		Delete(ctx context.Context, session sqlx.Session, id uint64) error
		LogicDelete(ctx context.Context, session sqlx.Session, id uint64) error
	}

	defaultBlackBoxCaseRefModel struct {
		sqlc.CachedConn
		table string
	}

	BlackBoxCaseRef struct {
		Id            uint64         `db:"id"`              // 自增ID
		CaseRefId     string         `db:"case_ref_id"`     // 用例ID
		Deleted       int64          `db:"deleted"`         // 逻辑删除标识（未删除、已删除）
		CreatedBy     string         `db:"created_by"`      // 创建者的用户ID
		UpdatedBy     string         `db:"updated_by"`      // 最近一次更新者的用户ID
		DeletedBy     sql.NullString `db:"deleted_by"`      // 删除者的用户ID
		CreatedAt     time.Time      `db:"created_at"`      // 创建时间
		UpdatedAt     time.Time      `db:"updated_at"`      // 更新时间
		DeletedAt     sql.NullTime   `db:"deleted_at"`      // 删除时间
		CaseRefType   string         `db:"case_ref_type"`   // 类型(修改/生成/局部修改)
		CaseDataCount int64          `db:"case_data_count"` // 生成用例数据统计
	}
)

func newBlackBoxCaseRefModel(conn sqlx.SqlConn, c cache.CacheConf, opts ...cache.Option) *defaultBlackBoxCaseRefModel {
	return &defaultBlackBoxCaseRefModel{
		CachedConn: sqlc.NewConn(conn, c, opts...),
		table:      "`black_box_case_ref`",
	}
}

func (m *defaultBlackBoxCaseRefModel) Delete(ctx context.Context, session sqlx.Session, id uint64) error {
	data, err := m.FindOne(ctx, id)
	if err != nil {
		return err
	}

	artificialIntelligenceBlackBoxCaseRefCaseRefIdKey := fmt.Sprintf("%s%v", cacheArtificialIntelligenceBlackBoxCaseRefCaseRefIdPrefix, data.CaseRefId)
	artificialIntelligenceBlackBoxCaseRefIdKey := fmt.Sprintf("%s%v", cacheArtificialIntelligenceBlackBoxCaseRefIdPrefix, id)
	_, err = m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("delete from %s where `id` = ?", m.table)
		if session != nil {
			return session.ExecCtx(ctx, query, id)
		}
		return conn.ExecCtx(ctx, query, id)
	}, artificialIntelligenceBlackBoxCaseRefCaseRefIdKey, artificialIntelligenceBlackBoxCaseRefIdKey)
	return err
}

func (m *defaultBlackBoxCaseRefModel) LogicDelete(ctx context.Context, session sqlx.Session, id uint64) error {
	data, err := m.FindOne(ctx, id)
	if err != nil {
		return err
	}

	artificialIntelligenceBlackBoxCaseRefCaseRefIdKey := fmt.Sprintf("%s%v", cacheArtificialIntelligenceBlackBoxCaseRefCaseRefIdPrefix, data.CaseRefId)
	artificialIntelligenceBlackBoxCaseRefIdKey := fmt.Sprintf("%s%v", cacheArtificialIntelligenceBlackBoxCaseRefIdPrefix, id)
	_, err = m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set `deleted` = ?, `deleted_at` = ? where `id` = ?", m.table)
		if session != nil {
			return session.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
		}
		return conn.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
	}, artificialIntelligenceBlackBoxCaseRefCaseRefIdKey, artificialIntelligenceBlackBoxCaseRefIdKey)
	return err
}

func (m *defaultBlackBoxCaseRefModel) FindOne(ctx context.Context, id uint64) (*BlackBoxCaseRef, error) {
	artificialIntelligenceBlackBoxCaseRefIdKey := fmt.Sprintf("%s%v", cacheArtificialIntelligenceBlackBoxCaseRefIdPrefix, id)
	var resp BlackBoxCaseRef
	err := m.QueryRowCtx(ctx, &resp, artificialIntelligenceBlackBoxCaseRefIdKey, func(ctx context.Context, conn sqlx.SqlConn, v any) error {
		query := fmt.Sprintf("select %s from %s where `id` = ? and `deleted` = ? limit 1", blackBoxCaseRefRows, m.table)
		return conn.QueryRowCtx(ctx, v, query, id, constants.NotDeleted)
	})
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultBlackBoxCaseRefModel) FindOneByCaseRefId(ctx context.Context, caseRefId string) (*BlackBoxCaseRef, error) {
	artificialIntelligenceBlackBoxCaseRefCaseRefIdKey := fmt.Sprintf("%s%v", cacheArtificialIntelligenceBlackBoxCaseRefCaseRefIdPrefix, caseRefId)
	var resp BlackBoxCaseRef
	err := m.QueryRowIndexCtx(ctx, &resp, artificialIntelligenceBlackBoxCaseRefCaseRefIdKey, m.formatPrimary, func(ctx context.Context, conn sqlx.SqlConn, v any) (i any, e error) {
		query := fmt.Sprintf("select %s from %s where `case_ref_id` = ? and `deleted` = ? limit 1", blackBoxCaseRefRows, m.table)
		if err := conn.QueryRowCtx(ctx, &resp, query, caseRefId, constants.NotDeleted); err != nil {
			return nil, err
		}
		return resp.Id, nil
	}, m.queryPrimary)
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultBlackBoxCaseRefModel) Insert(ctx context.Context, session sqlx.Session, data *BlackBoxCaseRef) (sql.Result, error) {
	artificialIntelligenceBlackBoxCaseRefCaseRefIdKey := fmt.Sprintf("%s%v", cacheArtificialIntelligenceBlackBoxCaseRefCaseRefIdPrefix, data.CaseRefId)
	artificialIntelligenceBlackBoxCaseRefIdKey := fmt.Sprintf("%s%v", cacheArtificialIntelligenceBlackBoxCaseRefIdPrefix, data.Id)

	return m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?, ?, ?, ?)", m.table, blackBoxCaseRefRowsExpectAutoSet)
		if session != nil {
			return session.ExecCtx(ctx, query, data.CaseRefId, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy, data.CaseRefType, data.CaseDataCount)
		}
		return conn.ExecCtx(ctx, query, data.CaseRefId, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy, data.CaseRefType, data.CaseDataCount)
	}, artificialIntelligenceBlackBoxCaseRefCaseRefIdKey, artificialIntelligenceBlackBoxCaseRefIdKey)
}

func (m *defaultBlackBoxCaseRefModel) Update(ctx context.Context, session sqlx.Session, newData *BlackBoxCaseRef) (sql.Result, error) {
	data, err := m.FindOne(ctx, newData.Id)
	if err != nil {
		return nil, err
	}

	artificialIntelligenceBlackBoxCaseRefCaseRefIdKey := fmt.Sprintf("%s%v", cacheArtificialIntelligenceBlackBoxCaseRefCaseRefIdPrefix, data.CaseRefId)
	artificialIntelligenceBlackBoxCaseRefIdKey := fmt.Sprintf("%s%v", cacheArtificialIntelligenceBlackBoxCaseRefIdPrefix, data.Id)

	return m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, blackBoxCaseRefRowsWithPlaceHolder)
		if session != nil {
			return session.ExecCtx(ctx, query, newData.CaseRefId, newData.Deleted, newData.CreatedBy, newData.UpdatedBy, newData.DeletedBy, newData.CaseRefType, newData.CaseDataCount, newData.Id)
		}
		return conn.ExecCtx(ctx, query, newData.CaseRefId, newData.Deleted, newData.CreatedBy, newData.UpdatedBy, newData.DeletedBy, newData.CaseRefType, newData.CaseDataCount, newData.Id)
	}, artificialIntelligenceBlackBoxCaseRefCaseRefIdKey, artificialIntelligenceBlackBoxCaseRefIdKey)
}

func (m *defaultBlackBoxCaseRefModel) formatPrimary(primary any) string {
	return fmt.Sprintf("%s%v", cacheArtificialIntelligenceBlackBoxCaseRefIdPrefix, primary)
}

func (m *defaultBlackBoxCaseRefModel) queryPrimary(ctx context.Context, conn sqlx.SqlConn, v, primary any) error {
	query := fmt.Sprintf("select %s from %s where `id` = ? limit 1", blackBoxCaseRefRows, m.table)
	return conn.QueryRowCtx(ctx, v, query, primary)
}

func (m *defaultBlackBoxCaseRefModel) tableName() string {
	return m.table
}
