// Code generated by goctl. DO NOT EDIT.
// versions:
//  goctl version: 1.7.4

package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlc"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
)

var (
	blackBoxCaseDirV2TableName           = "`black_box_case_dir_v2`"
	blackBoxCaseDirV2FieldNames          = builder.RawFieldNames(&BlackBoxCaseDirV2{})
	blackBoxCaseDirV2Rows                = strings.Join(blackBoxCaseDirV2FieldNames, ",")
	blackBoxCaseDirV2RowsExpectAutoSet   = strings.Join(stringx.Remove(blackBoxCaseDirV2FieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), ",")
	blackBoxCaseDirV2RowsWithPlaceHolder = strings.Join(stringx.Remove(blackBoxCaseDirV2FieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), "=?,") + "=?"

	cacheArtificialIntelligenceBlackBoxCaseDirV2IdPrefix             = "cache:artificialIntelligence:blackBoxCaseDirV2:id:"
	cacheArtificialIntelligenceBlackBoxCaseDirV2ProjectIdDirIdPrefix = "cache:artificialIntelligence:blackBoxCaseDirV2:projectId:dirId:"
)

type (
	blackBoxCaseDirV2Model interface {
		Insert(ctx context.Context, session sqlx.Session, data *BlackBoxCaseDirV2) (sql.Result, error)
		FindOne(ctx context.Context, id int64) (*BlackBoxCaseDirV2, error)
		FindOneByProjectIdDirId(ctx context.Context, projectId string, dirId string) (*BlackBoxCaseDirV2, error)
		Update(ctx context.Context, session sqlx.Session, data *BlackBoxCaseDirV2) (sql.Result, error)
		Delete(ctx context.Context, session sqlx.Session, id int64) error
		LogicDelete(ctx context.Context, session sqlx.Session, id int64) error
	}

	defaultBlackBoxCaseDirV2Model struct {
		sqlc.CachedConn
		table string
	}

	BlackBoxCaseDirV2 struct {
		Id             int64          `db:"id"`              // 自增ID
		ProjectId      string         `db:"project_id"`      // 项目ID
		DirId          string         `db:"dir_id"`          // 目录ID
		DirName        string         `db:"dir_name"`        // 目录名称
		DirDescription sql.NullString `db:"dir_description"` // 目录描述
		Deleted        int64          `db:"deleted"`         // 逻辑删除标识（未删除、已删除）
		CreatedBy      string         `db:"created_by"`      // 创建者的用户ID
		UpdatedBy      string         `db:"updated_by"`      // 最近一次更新者的用户ID
		DeletedBy      sql.NullString `db:"deleted_by"`      // 删除者的用户ID
		CreatedAt      time.Time      `db:"created_at"`      // 创建时间
		UpdatedAt      time.Time      `db:"updated_at"`      // 更新时间
		DeletedAt      sql.NullTime   `db:"deleted_at"`      // 删除时间
	}
)

func newBlackBoxCaseDirV2Model(conn sqlx.SqlConn, c cache.CacheConf, opts ...cache.Option) *defaultBlackBoxCaseDirV2Model {
	return &defaultBlackBoxCaseDirV2Model{
		CachedConn: sqlc.NewConn(conn, c, opts...),
		table:      "`black_box_case_dir_v2`",
	}
}

func (m *defaultBlackBoxCaseDirV2Model) Delete(ctx context.Context, session sqlx.Session, id int64) error {
	data, err := m.FindOne(ctx, id)
	if err != nil {
		return err
	}

	artificialIntelligenceBlackBoxCaseDirV2IdKey := fmt.Sprintf("%s%v", cacheArtificialIntelligenceBlackBoxCaseDirV2IdPrefix, id)
	artificialIntelligenceBlackBoxCaseDirV2ProjectIdDirIdKey := fmt.Sprintf("%s%v:%v", cacheArtificialIntelligenceBlackBoxCaseDirV2ProjectIdDirIdPrefix, data.ProjectId, data.DirId)
	_, err = m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("delete from %s where `id` = ?", m.table)
		if session != nil {
			return session.ExecCtx(ctx, query, id)
		}
		return conn.ExecCtx(ctx, query, id)
	}, artificialIntelligenceBlackBoxCaseDirV2IdKey, artificialIntelligenceBlackBoxCaseDirV2ProjectIdDirIdKey)
	return err
}

func (m *defaultBlackBoxCaseDirV2Model) LogicDelete(ctx context.Context, session sqlx.Session, id int64) error {
	data, err := m.FindOne(ctx, id)
	if err != nil {
		return err
	}

	artificialIntelligenceBlackBoxCaseDirV2IdKey := fmt.Sprintf("%s%v", cacheArtificialIntelligenceBlackBoxCaseDirV2IdPrefix, id)
	artificialIntelligenceBlackBoxCaseDirV2ProjectIdDirIdKey := fmt.Sprintf("%s%v:%v", cacheArtificialIntelligenceBlackBoxCaseDirV2ProjectIdDirIdPrefix, data.ProjectId, data.DirId)
	_, err = m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set `deleted` = ?, `deleted_at` = ? where `id` = ?", m.table)
		if session != nil {
			return session.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
		}
		return conn.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
	}, artificialIntelligenceBlackBoxCaseDirV2IdKey, artificialIntelligenceBlackBoxCaseDirV2ProjectIdDirIdKey)
	return err
}

func (m *defaultBlackBoxCaseDirV2Model) FindOne(ctx context.Context, id int64) (*BlackBoxCaseDirV2, error) {
	artificialIntelligenceBlackBoxCaseDirV2IdKey := fmt.Sprintf("%s%v", cacheArtificialIntelligenceBlackBoxCaseDirV2IdPrefix, id)
	var resp BlackBoxCaseDirV2
	err := m.QueryRowCtx(ctx, &resp, artificialIntelligenceBlackBoxCaseDirV2IdKey, func(ctx context.Context, conn sqlx.SqlConn, v any) error {
		query := fmt.Sprintf("select %s from %s where `id` = ? and `deleted` = ? limit 1", blackBoxCaseDirV2Rows, m.table)
		return conn.QueryRowCtx(ctx, v, query, id, constants.NotDeleted)
	})
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultBlackBoxCaseDirV2Model) FindOneByProjectIdDirId(ctx context.Context, projectId string, dirId string) (*BlackBoxCaseDirV2, error) {
	artificialIntelligenceBlackBoxCaseDirV2ProjectIdDirIdKey := fmt.Sprintf("%s%v:%v", cacheArtificialIntelligenceBlackBoxCaseDirV2ProjectIdDirIdPrefix, projectId, dirId)
	var resp BlackBoxCaseDirV2
	err := m.QueryRowIndexCtx(ctx, &resp, artificialIntelligenceBlackBoxCaseDirV2ProjectIdDirIdKey, m.formatPrimary, func(ctx context.Context, conn sqlx.SqlConn, v any) (i any, e error) {
		query := fmt.Sprintf("select %s from %s where `project_id` = ? and `dir_id` = ? and `deleted` = ? limit 1", blackBoxCaseDirV2Rows, m.table)
		if err := conn.QueryRowCtx(ctx, &resp, query, projectId, dirId, constants.NotDeleted); err != nil {
			return nil, err
		}
		return resp.Id, nil
	}, m.queryPrimary)
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultBlackBoxCaseDirV2Model) Insert(ctx context.Context, session sqlx.Session, data *BlackBoxCaseDirV2) (sql.Result, error) {
	artificialIntelligenceBlackBoxCaseDirV2IdKey := fmt.Sprintf("%s%v", cacheArtificialIntelligenceBlackBoxCaseDirV2IdPrefix, data.Id)
	artificialIntelligenceBlackBoxCaseDirV2ProjectIdDirIdKey := fmt.Sprintf("%s%v:%v", cacheArtificialIntelligenceBlackBoxCaseDirV2ProjectIdDirIdPrefix, data.ProjectId, data.DirId)

	return m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?, ?, ?, ?, ?)", m.table, blackBoxCaseDirV2RowsExpectAutoSet)
		if session != nil {
			return session.ExecCtx(ctx, query, data.ProjectId, data.DirId, data.DirName, data.DirDescription, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy)
		}
		return conn.ExecCtx(ctx, query, data.ProjectId, data.DirId, data.DirName, data.DirDescription, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy)
	}, artificialIntelligenceBlackBoxCaseDirV2IdKey, artificialIntelligenceBlackBoxCaseDirV2ProjectIdDirIdKey)
}

func (m *defaultBlackBoxCaseDirV2Model) Update(ctx context.Context, session sqlx.Session, newData *BlackBoxCaseDirV2) (sql.Result, error) {
	data, err := m.FindOne(ctx, newData.Id)
	if err != nil {
		return nil, err
	}

	artificialIntelligenceBlackBoxCaseDirV2IdKey := fmt.Sprintf("%s%v", cacheArtificialIntelligenceBlackBoxCaseDirV2IdPrefix, data.Id)
	artificialIntelligenceBlackBoxCaseDirV2ProjectIdDirIdKey := fmt.Sprintf("%s%v:%v", cacheArtificialIntelligenceBlackBoxCaseDirV2ProjectIdDirIdPrefix, data.ProjectId, data.DirId)

	return m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, blackBoxCaseDirV2RowsWithPlaceHolder)
		if session != nil {
			return session.ExecCtx(ctx, query, newData.ProjectId, newData.DirId, newData.DirName, newData.DirDescription, newData.Deleted, newData.CreatedBy, newData.UpdatedBy, newData.DeletedBy, newData.Id)
		}
		return conn.ExecCtx(ctx, query, newData.ProjectId, newData.DirId, newData.DirName, newData.DirDescription, newData.Deleted, newData.CreatedBy, newData.UpdatedBy, newData.DeletedBy, newData.Id)
	}, artificialIntelligenceBlackBoxCaseDirV2IdKey, artificialIntelligenceBlackBoxCaseDirV2ProjectIdDirIdKey)
}

func (m *defaultBlackBoxCaseDirV2Model) formatPrimary(primary any) string {
	return fmt.Sprintf("%s%v", cacheArtificialIntelligenceBlackBoxCaseDirV2IdPrefix, primary)
}

func (m *defaultBlackBoxCaseDirV2Model) queryPrimary(ctx context.Context, conn sqlx.SqlConn, v, primary any) error {
	query := fmt.Sprintf("select %s from %s where `id` = ? limit 1", blackBoxCaseDirV2Rows, m.table)
	return conn.QueryRowCtx(ctx, v, query, primary)
}

func (m *defaultBlackBoxCaseDirV2Model) tableName() string {
	return m.table
}
