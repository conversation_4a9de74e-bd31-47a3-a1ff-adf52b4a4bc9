// Code generated by goctl. DO NOT EDIT.
// versions:
//  goctl version: 1.7.4

package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlc"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
)

var (
	blackBoxCaseAssistantDocumentRelationshipTableName           = "`black_box_case_assistant_document_relationship`"
	blackBoxCaseAssistantDocumentRelationshipFieldNames          = builder.RawFieldNames(&BlackBoxCaseAssistantDocumentRelationship{})
	blackBoxCaseAssistantDocumentRelationshipRows                = strings.Join(blackBoxCaseAssistantDocumentRelationshipFieldNames, ",")
	blackBoxCaseAssistantDocumentRelationshipRowsExpectAutoSet   = strings.Join(stringx.Remove(blackBoxCaseAssistantDocumentRelationshipFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), ",")
	blackBoxCaseAssistantDocumentRelationshipRowsWithPlaceHolder = strings.Join(stringx.Remove(blackBoxCaseAssistantDocumentRelationshipFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), "=?,") + "=?"

	cacheArtificialIntelligenceBlackBoxCaseAssistantDocumentRelationshipIdPrefix = "cache:artificialIntelligence:blackBoxCaseAssistantDocumentRelationship:id:"
)

type (
	blackBoxCaseAssistantDocumentRelationshipModel interface {
		Insert(ctx context.Context, session sqlx.Session, data *BlackBoxCaseAssistantDocumentRelationship) (sql.Result, error)
		FindOne(ctx context.Context, id int64) (*BlackBoxCaseAssistantDocumentRelationship, error)
		Update(ctx context.Context, session sqlx.Session, data *BlackBoxCaseAssistantDocumentRelationship) (sql.Result, error)
		Delete(ctx context.Context, session sqlx.Session, id int64) error
		LogicDelete(ctx context.Context, session sqlx.Session, id int64) error
	}

	defaultBlackBoxCaseAssistantDocumentRelationshipModel struct {
		sqlc.CachedConn
		table string
	}

	BlackBoxCaseAssistantDocumentRelationship struct {
		Id          int64          `db:"id"`           // 自增ID
		ProjectId   string         `db:"project_id"`   // 项目ID
		AssistantId string         `db:"assistant_id"` // 助手ID
		DocumentId  string         `db:"document_id"`  // 文档ID
		Deleted     int64          `db:"deleted"`      // 逻辑删除标识（未删除、已删除）
		CreatedBy   string         `db:"created_by"`   // 创建者的用户ID
		UpdatedBy   string         `db:"updated_by"`   // 最近一次更新者的用户ID
		DeletedBy   sql.NullString `db:"deleted_by"`   // 删除者的用户ID
		CreatedAt   time.Time      `db:"created_at"`   // 创建时间
		UpdatedAt   time.Time      `db:"updated_at"`   // 更新时间
		DeletedAt   sql.NullTime   `db:"deleted_at"`   // 删除时间
	}
)

func newBlackBoxCaseAssistantDocumentRelationshipModel(conn sqlx.SqlConn, c cache.CacheConf, opts ...cache.Option) *defaultBlackBoxCaseAssistantDocumentRelationshipModel {
	return &defaultBlackBoxCaseAssistantDocumentRelationshipModel{
		CachedConn: sqlc.NewConn(conn, c, opts...),
		table:      "`black_box_case_assistant_document_relationship`",
	}
}

func (m *defaultBlackBoxCaseAssistantDocumentRelationshipModel) Delete(ctx context.Context, session sqlx.Session, id int64) error {
	artificialIntelligenceBlackBoxCaseAssistantDocumentRelationshipIdKey := fmt.Sprintf("%s%v", cacheArtificialIntelligenceBlackBoxCaseAssistantDocumentRelationshipIdPrefix, id)
	_, err := m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("delete from %s where `id` = ?", m.table)
		if session != nil {
			return session.ExecCtx(ctx, query, id)
		}
		return conn.ExecCtx(ctx, query, id)
	}, artificialIntelligenceBlackBoxCaseAssistantDocumentRelationshipIdKey)
	return err
}

func (m *defaultBlackBoxCaseAssistantDocumentRelationshipModel) LogicDelete(ctx context.Context, session sqlx.Session, id int64) error {
	artificialIntelligenceBlackBoxCaseAssistantDocumentRelationshipIdKey := fmt.Sprintf("%s%v", cacheArtificialIntelligenceBlackBoxCaseAssistantDocumentRelationshipIdPrefix, id)
	_, err := m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set `deleted` = ?, `deleted_at` = ? where `id` = ?", m.table)
		if session != nil {
			return session.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
		}
		return conn.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
	}, artificialIntelligenceBlackBoxCaseAssistantDocumentRelationshipIdKey)
	return err
}

func (m *defaultBlackBoxCaseAssistantDocumentRelationshipModel) FindOne(ctx context.Context, id int64) (*BlackBoxCaseAssistantDocumentRelationship, error) {
	artificialIntelligenceBlackBoxCaseAssistantDocumentRelationshipIdKey := fmt.Sprintf("%s%v", cacheArtificialIntelligenceBlackBoxCaseAssistantDocumentRelationshipIdPrefix, id)
	var resp BlackBoxCaseAssistantDocumentRelationship
	err := m.QueryRowCtx(ctx, &resp, artificialIntelligenceBlackBoxCaseAssistantDocumentRelationshipIdKey, func(ctx context.Context, conn sqlx.SqlConn, v any) error {
		query := fmt.Sprintf("select %s from %s where `id` = ? and `deleted` = ? limit 1", blackBoxCaseAssistantDocumentRelationshipRows, m.table)
		return conn.QueryRowCtx(ctx, v, query, id, constants.NotDeleted)
	})
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultBlackBoxCaseAssistantDocumentRelationshipModel) Insert(ctx context.Context, session sqlx.Session, data *BlackBoxCaseAssistantDocumentRelationship) (sql.Result, error) {
	artificialIntelligenceBlackBoxCaseAssistantDocumentRelationshipIdKey := fmt.Sprintf("%s%v", cacheArtificialIntelligenceBlackBoxCaseAssistantDocumentRelationshipIdPrefix, data.Id)

	return m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?, ?, ?, ?)", m.table, blackBoxCaseAssistantDocumentRelationshipRowsExpectAutoSet)
		if session != nil {
			return session.ExecCtx(ctx, query, data.ProjectId, data.AssistantId, data.DocumentId, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy)
		}
		return conn.ExecCtx(ctx, query, data.ProjectId, data.AssistantId, data.DocumentId, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy)
	}, artificialIntelligenceBlackBoxCaseAssistantDocumentRelationshipIdKey)
}

func (m *defaultBlackBoxCaseAssistantDocumentRelationshipModel) Update(ctx context.Context, session sqlx.Session, data *BlackBoxCaseAssistantDocumentRelationship) (sql.Result, error) {

	artificialIntelligenceBlackBoxCaseAssistantDocumentRelationshipIdKey := fmt.Sprintf("%s%v", cacheArtificialIntelligenceBlackBoxCaseAssistantDocumentRelationshipIdPrefix, data.Id)

	return m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, blackBoxCaseAssistantDocumentRelationshipRowsWithPlaceHolder)
		if session != nil {
			return session.ExecCtx(ctx, query, data.ProjectId, data.AssistantId, data.DocumentId, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy, data.Id)
		}
		return conn.ExecCtx(ctx, query, data.ProjectId, data.AssistantId, data.DocumentId, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy, data.Id)
	}, artificialIntelligenceBlackBoxCaseAssistantDocumentRelationshipIdKey)
}

func (m *defaultBlackBoxCaseAssistantDocumentRelationshipModel) formatPrimary(primary any) string {
	return fmt.Sprintf("%s%v", cacheArtificialIntelligenceBlackBoxCaseAssistantDocumentRelationshipIdPrefix, primary)
}

func (m *defaultBlackBoxCaseAssistantDocumentRelationshipModel) queryPrimary(ctx context.Context, conn sqlx.SqlConn, v, primary any) error {
	query := fmt.Sprintf("select %s from %s where `id` = ? limit 1", blackBoxCaseAssistantDocumentRelationshipRows, m.table)
	return conn.QueryRowCtx(ctx, v, query, primary)
}

func (m *defaultBlackBoxCaseAssistantDocumentRelationshipModel) tableName() string {
	return m.table
}
