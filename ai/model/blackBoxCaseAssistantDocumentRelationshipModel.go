package model

import (
	"context"

	"github.com/Masterminds/squirrel"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/types"
)

var (
	_ BlackBoxCaseAssistantDocumentRelationshipModel = (*customBlackBoxCaseAssistantDocumentRelationshipModel)(nil)

	blackBoxCaseAssistantDocumentRelationshipInsertFields = stringx.Remove(blackBoxCaseAssistantDocumentRelationshipFieldNames, "`id`", "`create_time`", "`update_time`", "`deleted_by`", "`created_at`", "`updated_at`", "`deleted_at`")
)

type (
	// BlackBoxCaseAssistantDocumentRelationshipModel is an interface to be customized, add more methods here,
	// and implement the added methods in customBlackBoxCaseAssistantDocumentRelationshipModel.
	BlackBoxCaseAssistantDocumentRelationshipModel interface {
		blackBoxCaseAssistantDocumentRelationshipModel
		types.DBModel

		Trans(ctx context.Context, fn func(context context.Context, session sqlx.Session) error) error
		InsertBuilder(data *BlackBoxCaseAssistantDocumentRelationship) squirrel.InsertBuilder
		UpdateBuilder(data *BlackBoxCaseAssistantDocumentRelationship) squirrel.UpdateBuilder
		SelectBuilder() squirrel.SelectBuilder
		SelectCountBuilder() squirrel.SelectBuilder
		FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error)
		FindNoCacheByQuery(ctx context.Context, selectBuilder squirrel.SelectBuilder) ([]*BlackBoxCaseAssistantDocumentRelationship, error)
	}

	customBlackBoxCaseAssistantDocumentRelationshipModel struct {
		*defaultBlackBoxCaseAssistantDocumentRelationshipModel

		conn sqlx.SqlConn
	}
)

// NewBlackBoxCaseAssistantDocumentRelationshipModel returns a model for the database table.
func NewBlackBoxCaseAssistantDocumentRelationshipModel(conn sqlx.SqlConn, c cache.CacheConf, opts ...cache.Option) BlackBoxCaseAssistantDocumentRelationshipModel {
	return &customBlackBoxCaseAssistantDocumentRelationshipModel{
		defaultBlackBoxCaseAssistantDocumentRelationshipModel: newBlackBoxCaseAssistantDocumentRelationshipModel(conn, c, opts...),
		conn: conn,
	}
}

func (m *customBlackBoxCaseAssistantDocumentRelationshipModel) Table() string {
	return m.table
}

func (m *customBlackBoxCaseAssistantDocumentRelationshipModel) Fields() []string {
	return blackBoxCaseAssistantDocumentRelationshipFieldNames
}

func (m *customBlackBoxCaseAssistantDocumentRelationshipModel) Trans(ctx context.Context, fn func(context context.Context, session sqlx.Session) error) error {
	return m.TransactCtx(ctx, fn)
}

func (m *customBlackBoxCaseAssistantDocumentRelationshipModel) InsertBuilder(data *BlackBoxCaseAssistantDocumentRelationship) squirrel.InsertBuilder {
	// TODO: fill the insert values
	return squirrel.Insert(m.table).Columns(blackBoxCaseAssistantDocumentRelationshipInsertFields...).Values()
}

func (m *customBlackBoxCaseAssistantDocumentRelationshipModel) UpdateBuilder(data *BlackBoxCaseAssistantDocumentRelationship) squirrel.UpdateBuilder {
	eq := squirrel.Eq{
		// TODO: fill the update values
	}
	return squirrel.Update(m.table).SetMap(eq).Where("`id` = ?", data.Id)
}

func (m *customBlackBoxCaseAssistantDocumentRelationshipModel) SelectBuilder() squirrel.SelectBuilder {
	return squirrel.Select(blackBoxCaseAssistantDocumentRelationshipFieldNames...).Where("`deleted` = ?", constants.NotDeleted).From(m.table)
}

func (m *customBlackBoxCaseAssistantDocumentRelationshipModel) SelectCountBuilder() squirrel.SelectBuilder {
	return squirrel.Select("count(*)").Where("`deleted` = ?", constants.NotDeleted).From(m.table)
}

func (m *customBlackBoxCaseAssistantDocumentRelationshipModel) FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error) {
	query, values, err := countBuilder.ToSql()
	if err != nil {
		return 0, err
	}

	var resp int64
	err = m.QueryRowNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return 0, err
	}
}

func (m *customBlackBoxCaseAssistantDocumentRelationshipModel) FindNoCacheByQuery(ctx context.Context, selectBuilder squirrel.SelectBuilder) ([]*BlackBoxCaseAssistantDocumentRelationship, error) {
	query, values, err := selectBuilder.ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*BlackBoxCaseAssistantDocumentRelationship
	err = m.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}
