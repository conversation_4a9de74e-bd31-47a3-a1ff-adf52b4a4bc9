package model

import (
	"strings"
)

// AddTableNameToFields add table name to each field in slice
// Example: `name` => table.`name`
func AddTableNameToFields(table string, fields []string) []string {
	if table == "" {
		return fields
	}

	out := make([]string, len(fields))

	for i, field := range fields {
		if !strings.HasPrefix(field, table+".") {
			field = table + "." + field
		}
		out[i] = field
	}

	return out
}
