package model

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	"time"

	"github.com/Masterminds/squirrel"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlc"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sqlbuilder"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sqlbuilder/rpc"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/types"
)

var (
	_ BlackBoxCaseTwBetaModel = (*customBlackBoxCaseTwBetaModel)(nil)

	blackBoxCaseTwBetaInsertFields = stringx.Remove(
		blackBoxCaseTwBetaFieldNames, "`id`", "`create_time`", "`update_time`", "`deleted_by`", "`created_at`",
		"`updated_at`", "`deleted_at`",
	)
)

type (
	// BlackBoxCaseTwBetaModel is an interface to be customized, add more methods here,
	// and implement the added methods in customBlackBoxCaseTwBetaModel.
	BlackBoxCaseTwBetaModel interface {
		blackBoxCaseTwBetaModel
		types.DBModel

		Trans(ctx context.Context, fn func(context context.Context, session sqlx.Session) error) error
		InsertBuilder(data *BlackBoxCaseSession) squirrel.InsertBuilder
		UpdateBuilder(data *BlackBoxCaseSession) squirrel.UpdateBuilder
		SelectBuilder() squirrel.SelectBuilder
		SelectCountBuilder() squirrel.SelectBuilder
		FindOneByRevisionId(ctx context.Context, revisionId string) (*BlackBoxCaseTwBeta, error)
		FindOneByBetaCaseId(ctx context.Context, betaCaseId string) (*BlackBoxCaseTwBeta, error)
		FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error)
		FindByQueryObj(ctx context.Context, req ListBlackBoxCaseTwBetaQueryObj) ([]*BlackBoxCaseTwBeta, error)
		CountByQueryObj(ctx context.Context, req ListBlackBoxCaseTwBetaQueryObj) (int64, error)
		LogicDeleteById(ctx context.Context, betaCaseId string) error
	}

	customBlackBoxCaseTwBetaModel struct {
		*defaultBlackBoxCaseTwBetaModel
	}

	ListBlackBoxCaseTwBetaQueryObj struct {
		BetaCaseName string
		Pagination   *rpc.Pagination
	}
)

// NewBlackBoxCaseTwBetaModel returns a model for the database table.
func NewBlackBoxCaseTwBetaModel(conn sqlx.SqlConn, c cache.CacheConf, opts ...cache.Option) BlackBoxCaseTwBetaModel {
	return &customBlackBoxCaseTwBetaModel{
		defaultBlackBoxCaseTwBetaModel: newBlackBoxCaseTwBetaModel(conn, c, opts...),
	}
}

func (m *customBlackBoxCaseTwBetaModel) Table() string {
	return m.table
}

func (m *customBlackBoxCaseTwBetaModel) Fields() []string {
	return blackBoxCaseTwBetaFieldNames
}

func (m *customBlackBoxCaseTwBetaModel) Trans(
	ctx context.Context, fn func(context context.Context, session sqlx.Session) error,
) error {
	return m.TransactCtx(ctx, fn)
}

func (m *customBlackBoxCaseTwBetaModel) InsertBuilder(data *BlackBoxCaseSession) squirrel.InsertBuilder {
	return squirrel.Insert(m.table).Columns(blackBoxCaseTwBetaInsertFields...).Values()
}

func (m *customBlackBoxCaseTwBetaModel) UpdateBuilder(data *BlackBoxCaseSession) squirrel.UpdateBuilder {
	eq := squirrel.Eq{}
	return squirrel.Update(m.table).SetMap(eq).Where("`id` = ?", data.Id)
}

func (m *customBlackBoxCaseTwBetaModel) SelectBuilder() squirrel.SelectBuilder {
	return squirrel.Select(blackBoxCaseTwBetaFieldNames...).From(m.table).Where("`deleted` = ?", constants.NotDeleted)
}

func (m *customBlackBoxCaseTwBetaModel) SelectCountBuilder() squirrel.SelectBuilder {
	return squirrel.Select("count(*)").Where("`deleted` = ?", constants.NotDeleted).From(m.table)
}

func (m *customBlackBoxCaseTwBetaModel) FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (
	int64, error,
) {
	query, values, err := countBuilder.ToSql()
	if err != nil {
		return 0, err
	}

	var resp int64
	err = m.QueryRowNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return 0, err
	}
}

func (m *customBlackBoxCaseTwBetaModel) FindByQueryObj(
	ctx context.Context, req ListBlackBoxCaseTwBetaQueryObj,
) ([]*BlackBoxCaseTwBeta, error) {
	sql := m.SelectBuilder()
	if len(req.BetaCaseName) > 0 {
		sql = sql.Where("`beta_case_name` LIKE ? ", "%"+req.BetaCaseName+"%")
	}
	sql = sqlbuilder.SearchOptions(
		sql, sqlbuilder.WithPagination(m, req.Pagination),
	).OrderBy("`created_at` DESC")

	query, values, err := sql.ToSql()
	if err != nil {
		return nil, err
	}
	var resp []*BlackBoxCaseTwBeta
	err = m.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
	switch {
	case err == nil:
		return resp, nil
	case errors.Is(err, sqlc.ErrNotFound):
		return resp, nil
	default:
		return nil, err
	}
}

func (m *customBlackBoxCaseTwBetaModel) CountByQueryObj(ctx context.Context, req ListBlackBoxCaseTwBetaQueryObj) (
	int64, error,
) {
	sql := m.SelectCountBuilder()
	if len(req.BetaCaseName) > 0 {
		sql = sql.Where("`beta_case_name` like ? and `deleted` = ? ", "%"+req.BetaCaseName+"%", constants.NotDeleted)
	}
	resp, err := m.FindCount(ctx, sql)
	switch {
	case err == nil:
		return resp, nil
	case errors.Is(err, sqlc.ErrNotFound):
		return resp, nil
	default:
		return 0, err
	}
}

func (m *customBlackBoxCaseTwBetaModel) LogicDeleteById(ctx context.Context, betaCaseId string) error {
	_, err := m.ExecCtx(
		ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
			query := fmt.Sprintf("update %s set `deleted` = ?, `deleted_at` = ? where `beta_case_id` = ?", m.table)
			return conn.ExecCtx(
				ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, betaCaseId,
			)
		},
	)
	return err
}

func (m *customBlackBoxCaseTwBetaModel) FindOneByBetaCaseId(
	ctx context.Context, betaCaseId string,
) (*BlackBoxCaseTwBeta, error) {
	var resp BlackBoxCaseTwBeta

	// SQL 查询语句，查询 revision_id 对应的一条记录
	query := fmt.Sprintf("SELECT %s FROM %s WHERE beta_case_id = ? LIMIT 1", blackBoxCaseTwBetaRows, m.table)

	// 使用 QueryRowNoCache 进行数据库查询，不走缓存
	err := m.QueryRowNoCacheCtx(ctx, &resp, query, betaCaseId)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			// 如果没有数据，返回 nil 表示没有找到
			return nil, nil
		}
		// 处理其他错误
		return nil, err
	}

	return &resp, nil
}

func (m *customBlackBoxCaseTwBetaModel) FindOneByRevisionId(
	ctx context.Context, revisionId string,
) (*BlackBoxCaseTwBeta, error) {
	var resp BlackBoxCaseTwBeta

	// SQL 查询语句，查询 revision_id 对应的一条记录
	query := fmt.Sprintf(
		"SELECT %s FROM %s WHERE revision_id = ? and deleted = 0 LIMIT 1", blackBoxCaseTwBetaRows, m.table,
	)

	// 使用 QueryRowNoCache 进行数据库查询，不走缓存
	err := m.QueryRowNoCacheCtx(ctx, &resp, query, revisionId)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			// 如果没有数据，返回 nil 表示没有找到
			return nil, nil
		}
		// 处理其他错误
		return nil, err
	}

	return &resp, nil
}
