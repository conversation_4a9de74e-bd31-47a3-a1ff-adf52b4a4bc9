package model

import (
	"context"

	"github.com/Masterminds/squirrel"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlc"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sqlbuilder"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sqlbuilder/rpc"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/types"
)

var (
	_ BlackBoxCaseDocumentModel = (*customBlackBoxCaseDocumentModel)(nil)

	blackBoxCaseDocumentInsertFields = stringx.Remove(
		blackBoxCaseDocumentFieldNames, "`id`", "`create_time`", "`update_time`", "`deleted_by`", "`created_at`",
		"`updated_at`", "`deleted_at`",
	)
)

type (
	// BlackBoxCaseDocumentModel is an interface to be customized, add more methods here,
	// and implement the added methods in customBlackBoxCaseDocumentModel.
	BlackBoxCaseDocumentModel interface {
		blackBoxCaseDocumentModel
		types.DBModel

		Trans(ctx context.Context, fn func(context context.Context, session sqlx.Session) error) error
		InsertBuilder(data *BlackBoxCaseDocument) squirrel.InsertBuilder
		UpdateBuilder(data *BlackBoxCaseDocument) squirrel.UpdateBuilder
		SelectBuilder() squirrel.SelectBuilder
		SelectCountBuilder() squirrel.SelectBuilder
		FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error)
		FindNoCacheByQuery(ctx context.Context, selectBuilder squirrel.SelectBuilder) ([]*BlackBoxCaseDocument, error)
		FindByQueryObj(
			ctx context.Context, req SearchBlackBoxCaseDocumentForAssistant,
		) ([]*BlackBoxCaseDocument, error)
		FindByDocumentIdList(
			ctx context.Context, documentIds []string,
		) ([]*BlackBoxCaseDocument, error)
		FindOneByDocumentId(
			ctx context.Context, documentId string,
		) (
			*BlackBoxCaseDocument, error,
		)
		DeleteByProjectIdAssistantId(
			ctx context.Context, session sqlx.Session, projectId, assistantId string,
		) error
	}

	customBlackBoxCaseDocumentModel struct {
		*defaultBlackBoxCaseDocumentModel

		conn sqlx.SqlConn
	}

	SearchBlackBoxCaseDocumentForAssistant struct {
		ProjectId      string
		AssistantId    string
		Pagination     *rpc.Pagination
		DocumentIdList []string
	}
)

// NewBlackBoxCaseDocumentModel returns a model for the database table.
func NewBlackBoxCaseDocumentModel(
	conn sqlx.SqlConn, c cache.CacheConf, opts ...cache.Option,
) BlackBoxCaseDocumentModel {
	return &customBlackBoxCaseDocumentModel{
		defaultBlackBoxCaseDocumentModel: newBlackBoxCaseDocumentModel(conn, c, opts...),
		conn:                             conn,
	}
}

func (m *customBlackBoxCaseDocumentModel) Table() string {
	return m.table
}

func (m *customBlackBoxCaseDocumentModel) Fields() []string {
	return blackBoxCaseDocumentFieldNames
}

func (m *customBlackBoxCaseDocumentModel) Trans(
	ctx context.Context, fn func(context context.Context, session sqlx.Session) error,
) error {
	return m.TransactCtx(ctx, fn)
}

func (m *customBlackBoxCaseDocumentModel) InsertBuilder(data *BlackBoxCaseDocument) squirrel.InsertBuilder {
	// TODO: fill the insert values
	return squirrel.Insert(m.table).Columns(blackBoxCaseDocumentInsertFields...).Values()
}

func (m *customBlackBoxCaseDocumentModel) UpdateBuilder(data *BlackBoxCaseDocument) squirrel.UpdateBuilder {
	eq := squirrel.Eq{
		// TODO: fill the update values
	}
	return squirrel.Update(m.table).SetMap(eq).Where("`id` = ?", data.Id)
}

func (m *customBlackBoxCaseDocumentModel) SelectBuilder() squirrel.SelectBuilder {
	return squirrel.Select(blackBoxCaseDocumentFieldNames...).Where("`deleted` = ?", constants.NotDeleted).From(m.table)
}

func (m *customBlackBoxCaseDocumentModel) SelectCountBuilder() squirrel.SelectBuilder {
	return squirrel.Select("count(*)").Where("`deleted` = ?", constants.NotDeleted).From(m.table)
}

func (m *customBlackBoxCaseDocumentModel) FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (
	int64, error,
) {
	query, values, err := countBuilder.ToSql()
	if err != nil {
		return 0, err
	}

	var resp int64
	err = m.QueryRowNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return 0, err
	}
}

func (m *customBlackBoxCaseDocumentModel) FindNoCacheByQuery(
	ctx context.Context, selectBuilder squirrel.SelectBuilder,
) ([]*BlackBoxCaseDocument, error) {
	query, values, err := selectBuilder.ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*BlackBoxCaseDocument
	err = m.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

func (m *customBlackBoxCaseDocumentModel) FindByQueryObj(
	ctx context.Context, req SearchBlackBoxCaseDocumentForAssistant,
) ([]*BlackBoxCaseDocument, error) {
	sql := m.SelectBuilder()
	if len(req.DocumentIdList) > 0 {
		sql = sql.Where(squirrel.Eq{"`document_id`": req.DocumentIdList})
	} else {
		sql = sql.Where("`project_id` = ? and `assistant_id` = ?", req.ProjectId, req.AssistantId)
	}
	sql = sqlbuilder.SearchOptions(
		sql, sqlbuilder.WithPagination(m, req.Pagination),
	).OrderBy("`updated_at` desc")

	query, values, err := sql.ToSql()
	if err != nil {
		return nil, err
	}
	var resp []*BlackBoxCaseDocument
	err = m.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	case sqlc.ErrNotFound:
		return resp, nil
	default:
		return nil, err
	}
}

func (m *customBlackBoxCaseDocumentModel) FindByDocumentIdList(
	ctx context.Context, documentIds []string,
) ([]*BlackBoxCaseDocument, error) {
	var resp []*BlackBoxCaseDocument
	var err error
	sql := m.SelectBuilder()
	if len(documentIds) > 0 {
		sql = sql.Where(squirrel.Eq{"`document_id`": documentIds}).OrderBy("`updated_at` desc")
		query, values, err1 := sql.ToSql()
		if err1 != nil {
			return nil, err1
		}
		err = m.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
	}
	switch err {
	case nil:
		return resp, nil
	case sqlc.ErrNotFound:
		return resp, nil
	default:
		return nil, err
	}
}

/*
func (m *customBlackBoxCaseDocumentModel) FindOneByProjectIdDocumentId(
	ctx context.Context, projectId, documentId string,
) (
	*BlackBoxCaseDocument, error,
) {
	sql := m.SelectBuilder().Where("`project_id` = ? and `document_id` = ?", projectId, documentId).Limit(1)
	query, values, err := sql.ToSql()
	if err != nil {
		return nil, err
	}

	resp := new(BlackBoxCaseDocument)
	err = m.QueryRowNoCacheCtx(ctx, resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}


}*/

func (m *customBlackBoxCaseDocumentModel) FindOneByDocumentId(
	ctx context.Context, documentId string,
) (
	*BlackBoxCaseDocument, error,
) {
	sql := m.SelectBuilder().Where(
		"`document_id` = ? ", documentId,
	).Limit(1)
	query, values, err := sql.ToSql()
	if err != nil {
		return nil, err
	}

	resp := new(BlackBoxCaseDocument)
	err = m.QueryRowNoCacheCtx(ctx, resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

func (l *customBlackBoxCaseDocumentModel) DeleteByProjectIdAssistantId(
	ctx context.Context, session sqlx.Session, projectId, assistantId string,
) error {
	sql := squirrel.Delete(l.Table()).Where(
		"`project_id` = ? and `assistant_id` = ?", projectId, assistantId,
	)
	query, values, err := sql.ToSql()
	if err != nil {
		return err
	}
	if session != nil {
		_, err := session.ExecCtx(ctx, query, values...)
		return err
	}
	_, err = l.conn.ExecCtx(ctx, query, values...)
	return err
}
