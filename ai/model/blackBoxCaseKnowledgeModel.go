package model

import (
	"context"

	"github.com/Masterminds/squirrel"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/types"
)

var (
	_ BlackBoxCaseKnowledgeModel = (*customBlackBoxCaseKnowledgeModel)(nil)

	blackBoxCaseKnowledgeInsertFields = stringx.Remove(blackBoxCaseKnowledgeFieldNames, "`id`", "`create_time`", "`update_time`", "`deleted_by`", "`created_at`", "`updated_at`", "`deleted_at`")
)

type (
	// BlackBoxCaseKnowledgeModel is an interface to be customized, add more methods here,
	// and implement the added methods in customBlackBoxCaseKnowledgeModel.
	BlackBoxCaseKnowledgeModel interface {
		blackBoxCaseKnowledgeModel
		types.DBModel

		Trans(ctx context.Context, fn func(context context.Context, session sqlx.Session) error) error
		InsertBuilder(data *BlackBoxCaseKnowledge) squirrel.InsertBuilder
		UpdateBuilder(data *BlackBoxCaseKnowledge) squirrel.UpdateBuilder
		SelectBuilder() squirrel.SelectBuilder
		SelectCountBuilder() squirrel.SelectBuilder
		FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error)
		FindNoCacheByQuery(ctx context.Context, selectBuilder squirrel.SelectBuilder) ([]*BlackBoxCaseKnowledge, error)
		FindByDirId(ctx context.Context, dirId string) ([]*BlackBoxCaseKnowledge, error)
	}

	customBlackBoxCaseKnowledgeModel struct {
		*defaultBlackBoxCaseKnowledgeModel

		conn sqlx.SqlConn
	}
)

// NewBlackBoxCaseKnowledgeModel returns a model for the database table.
func NewBlackBoxCaseKnowledgeModel(conn sqlx.SqlConn, c cache.CacheConf, opts ...cache.Option) BlackBoxCaseKnowledgeModel {
	return &customBlackBoxCaseKnowledgeModel{
		defaultBlackBoxCaseKnowledgeModel: newBlackBoxCaseKnowledgeModel(conn, c, opts...),
		conn:                              conn,
	}
}

func (m *customBlackBoxCaseKnowledgeModel) Table() string {
	return m.table
}

func (m *customBlackBoxCaseKnowledgeModel) Fields() []string {
	return blackBoxCaseKnowledgeFieldNames
}

func (m *customBlackBoxCaseKnowledgeModel) Trans(ctx context.Context, fn func(context context.Context, session sqlx.Session) error) error {
	return m.TransactCtx(ctx, fn)
}

func (m *customBlackBoxCaseKnowledgeModel) InsertBuilder(data *BlackBoxCaseKnowledge) squirrel.InsertBuilder {
	// TODO: fill the insert values
	return squirrel.Insert(m.table).Columns(blackBoxCaseKnowledgeInsertFields...).Values()
}

func (m *customBlackBoxCaseKnowledgeModel) UpdateBuilder(data *BlackBoxCaseKnowledge) squirrel.UpdateBuilder {
	eq := squirrel.Eq{
		// TODO: fill the update values
	}
	return squirrel.Update(m.table).SetMap(eq).Where("`id` = ?", data.Id)
}

func (m *customBlackBoxCaseKnowledgeModel) SelectBuilder() squirrel.SelectBuilder {
	return squirrel.Select(blackBoxCaseKnowledgeFieldNames...).Where("`deleted` = ?", constants.NotDeleted).From(m.table)
}

func (m *customBlackBoxCaseKnowledgeModel) SelectCountBuilder() squirrel.SelectBuilder {
	return squirrel.Select("count(*)").Where("`deleted` = ?", constants.NotDeleted).From(m.table)
}

func (m *customBlackBoxCaseKnowledgeModel) FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error) {
	query, values, err := countBuilder.ToSql()
	if err != nil {
		return 0, err
	}

	var resp int64
	err = m.QueryRowNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return 0, err
	}
}

func (m *customBlackBoxCaseKnowledgeModel) FindNoCacheByQuery(ctx context.Context, selectBuilder squirrel.SelectBuilder) ([]*BlackBoxCaseKnowledge, error) {
	query, values, err := selectBuilder.ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*BlackBoxCaseKnowledge
	err = m.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

func (m *customBlackBoxCaseKnowledgeModel) FindByDirId(ctx context.Context, dirId string) (
	[]*BlackBoxCaseKnowledge, error,
) {
	sql := squirrel.Select(blackBoxCaseKnowledgeFieldNames...).From(m.table).Where("dir_id = ?", dirId)
	query, values, err := sql.ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*BlackBoxCaseKnowledge
	err = m.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}
