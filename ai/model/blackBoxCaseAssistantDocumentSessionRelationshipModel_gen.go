// Code generated by goctl. DO NOT EDIT.
// versions:
//  goctl version: 1.7.4

package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlc"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
)

var (
	blackBoxCaseAssistantDocumentSessionRelationshipTableName           = "`black_box_case_assistant_document_session_relationship`"
	blackBoxCaseAssistantDocumentSessionRelationshipFieldNames          = builder.RawFieldNames(&BlackBoxCaseAssistantDocumentSessionRelationship{})
	blackBoxCaseAssistantDocumentSessionRelationshipRows                = strings.Join(blackBoxCaseAssistantDocumentSessionRelationshipFieldNames, ",")
	blackBoxCaseAssistantDocumentSessionRelationshipRowsExpectAutoSet   = strings.Join(stringx.Remove(blackBoxCaseAssistantDocumentSessionRelationshipFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), ",")
	blackBoxCaseAssistantDocumentSessionRelationshipRowsWithPlaceHolder = strings.Join(stringx.Remove(blackBoxCaseAssistantDocumentSessionRelationshipFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), "=?,") + "=?"

	cacheArtificialIntelligenceBlackBoxCaseAssistantDocumentSessionRelationshipIdPrefix = "cache:artificialIntelligence:blackBoxCaseAssistantDocumentSessionRelationship:id:"
)

type (
	blackBoxCaseAssistantDocumentSessionRelationshipModel interface {
		Insert(ctx context.Context, session sqlx.Session, data *BlackBoxCaseAssistantDocumentSessionRelationship) (sql.Result, error)
		FindOne(ctx context.Context, id int64) (*BlackBoxCaseAssistantDocumentSessionRelationship, error)
		Update(ctx context.Context, session sqlx.Session, data *BlackBoxCaseAssistantDocumentSessionRelationship) (sql.Result, error)
		Delete(ctx context.Context, session sqlx.Session, id int64) error
		LogicDelete(ctx context.Context, session sqlx.Session, id int64) error
	}

	defaultBlackBoxCaseAssistantDocumentSessionRelationshipModel struct {
		sqlc.CachedConn
		table string
	}

	BlackBoxCaseAssistantDocumentSessionRelationship struct {
		Id          int64          `db:"id"`           // 自增ID
		ProjectId   string         `db:"project_id"`   // 项目ID
		AssistantId string         `db:"assistant_id"` // 助手ID
		SessionId   string         `db:"session_id"`   // 会话ID
		DocumentId  string         `db:"document_id"`  // 文档ID
		Deleted     int64          `db:"deleted"`      // 逻辑删除标识（未删除、已删除）
		CreatedBy   string         `db:"created_by"`   // 创建者的用户ID
		UpdatedBy   string         `db:"updated_by"`   // 最近一次更新者的用户ID
		DeletedBy   sql.NullString `db:"deleted_by"`   // 删除者的用户ID
		CreatedAt   time.Time      `db:"created_at"`   // 创建时间
		UpdatedAt   time.Time      `db:"updated_at"`   // 更新时间
		DeletedAt   sql.NullTime   `db:"deleted_at"`   // 删除时间
	}
)

func newBlackBoxCaseAssistantDocumentSessionRelationshipModel(conn sqlx.SqlConn, c cache.CacheConf, opts ...cache.Option) *defaultBlackBoxCaseAssistantDocumentSessionRelationshipModel {
	return &defaultBlackBoxCaseAssistantDocumentSessionRelationshipModel{
		CachedConn: sqlc.NewConn(conn, c, opts...),
		table:      "`black_box_case_assistant_document_session_relationship`",
	}
}

func (m *defaultBlackBoxCaseAssistantDocumentSessionRelationshipModel) Delete(ctx context.Context, session sqlx.Session, id int64) error {
	artificialIntelligenceBlackBoxCaseAssistantDocumentSessionRelationshipIdKey := fmt.Sprintf("%s%v", cacheArtificialIntelligenceBlackBoxCaseAssistantDocumentSessionRelationshipIdPrefix, id)
	_, err := m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("delete from %s where `id` = ?", m.table)
		if session != nil {
			return session.ExecCtx(ctx, query, id)
		}
		return conn.ExecCtx(ctx, query, id)
	}, artificialIntelligenceBlackBoxCaseAssistantDocumentSessionRelationshipIdKey)
	return err
}

func (m *defaultBlackBoxCaseAssistantDocumentSessionRelationshipModel) LogicDelete(ctx context.Context, session sqlx.Session, id int64) error {
	artificialIntelligenceBlackBoxCaseAssistantDocumentSessionRelationshipIdKey := fmt.Sprintf("%s%v", cacheArtificialIntelligenceBlackBoxCaseAssistantDocumentSessionRelationshipIdPrefix, id)
	_, err := m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set `deleted` = ?, `deleted_at` = ? where `id` = ?", m.table)
		if session != nil {
			return session.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
		}
		return conn.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
	}, artificialIntelligenceBlackBoxCaseAssistantDocumentSessionRelationshipIdKey)
	return err
}

func (m *defaultBlackBoxCaseAssistantDocumentSessionRelationshipModel) FindOne(ctx context.Context, id int64) (*BlackBoxCaseAssistantDocumentSessionRelationship, error) {
	artificialIntelligenceBlackBoxCaseAssistantDocumentSessionRelationshipIdKey := fmt.Sprintf("%s%v", cacheArtificialIntelligenceBlackBoxCaseAssistantDocumentSessionRelationshipIdPrefix, id)
	var resp BlackBoxCaseAssistantDocumentSessionRelationship
	err := m.QueryRowCtx(ctx, &resp, artificialIntelligenceBlackBoxCaseAssistantDocumentSessionRelationshipIdKey, func(ctx context.Context, conn sqlx.SqlConn, v any) error {
		query := fmt.Sprintf("select %s from %s where `id` = ? and `deleted` = ? limit 1", blackBoxCaseAssistantDocumentSessionRelationshipRows, m.table)
		return conn.QueryRowCtx(ctx, v, query, id, constants.NotDeleted)
	})
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultBlackBoxCaseAssistantDocumentSessionRelationshipModel) Insert(ctx context.Context, session sqlx.Session, data *BlackBoxCaseAssistantDocumentSessionRelationship) (sql.Result, error) {
	artificialIntelligenceBlackBoxCaseAssistantDocumentSessionRelationshipIdKey := fmt.Sprintf("%s%v", cacheArtificialIntelligenceBlackBoxCaseAssistantDocumentSessionRelationshipIdPrefix, data.Id)

	return m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?, ?, ?, ?, ?)", m.table, blackBoxCaseAssistantDocumentSessionRelationshipRowsExpectAutoSet)
		if session != nil {
			return session.ExecCtx(ctx, query, data.ProjectId, data.AssistantId, data.SessionId, data.DocumentId, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy)
		}
		return conn.ExecCtx(ctx, query, data.ProjectId, data.AssistantId, data.SessionId, data.DocumentId, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy)
	}, artificialIntelligenceBlackBoxCaseAssistantDocumentSessionRelationshipIdKey)
}

func (m *defaultBlackBoxCaseAssistantDocumentSessionRelationshipModel) Update(ctx context.Context, session sqlx.Session, data *BlackBoxCaseAssistantDocumentSessionRelationship) (sql.Result, error) {

	artificialIntelligenceBlackBoxCaseAssistantDocumentSessionRelationshipIdKey := fmt.Sprintf("%s%v", cacheArtificialIntelligenceBlackBoxCaseAssistantDocumentSessionRelationshipIdPrefix, data.Id)

	return m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, blackBoxCaseAssistantDocumentSessionRelationshipRowsWithPlaceHolder)
		if session != nil {
			return session.ExecCtx(ctx, query, data.ProjectId, data.AssistantId, data.SessionId, data.DocumentId, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy, data.Id)
		}
		return conn.ExecCtx(ctx, query, data.ProjectId, data.AssistantId, data.SessionId, data.DocumentId, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy, data.Id)
	}, artificialIntelligenceBlackBoxCaseAssistantDocumentSessionRelationshipIdKey)
}

func (m *defaultBlackBoxCaseAssistantDocumentSessionRelationshipModel) formatPrimary(primary any) string {
	return fmt.Sprintf("%s%v", cacheArtificialIntelligenceBlackBoxCaseAssistantDocumentSessionRelationshipIdPrefix, primary)
}

func (m *defaultBlackBoxCaseAssistantDocumentSessionRelationshipModel) queryPrimary(ctx context.Context, conn sqlx.SqlConn, v, primary any) error {
	query := fmt.Sprintf("select %s from %s where `id` = ? limit 1", blackBoxCaseAssistantDocumentSessionRelationshipRows, m.table)
	return conn.QueryRowCtx(ctx, v, query, primary)
}

func (m *defaultBlackBoxCaseAssistantDocumentSessionRelationshipModel) tableName() string {
	return m.table
}
