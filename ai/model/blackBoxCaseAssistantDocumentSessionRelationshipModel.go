package model

import (
	"context"
	"database/sql"
	"fmt"
	"time"

	"github.com/Masterminds/squirrel"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/types"
)

var (
	_ BlackBoxCaseAssistantDocumentSessionRelationshipModel = (*customBlackBoxCaseAssistantDocumentSessionRelationshipModel)(nil)

	blackBoxCaseAssistantDocumentSessionRelationshipInsertFields = stringx.Remove(
		blackBoxCaseAssistantDocumentSessionRelationshipFieldNames, "`id`", "`create_time`", "`update_time`",
		"`deleted_by`", "`created_at`", "`updated_at`", "`deleted_at`",
	)
)

type (
	// BlackBoxCaseAssistantDocumentSessionRelationshipModel is an interface to be customized, add more methods here,
	// and implement the added methods in customBlackBoxCaseAssistantDocumentSessionRelationshipModel.
	BlackBoxCaseAssistantDocumentSessionRelationshipModel interface {
		blackBoxCaseAssistantDocumentSessionRelationshipModel
		types.DBModel

		Trans(ctx context.Context, fn func(context context.Context, session sqlx.Session) error) error
		InsertBuilder(data *BlackBoxCaseAssistantDocumentSessionRelationship) squirrel.InsertBuilder
		UpdateBuilder(data *BlackBoxCaseAssistantDocumentSessionRelationship) squirrel.UpdateBuilder
		SelectBuilder() squirrel.SelectBuilder
		SelectCountBuilder() squirrel.SelectBuilder
		FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error)
		FindNoCacheByQuery(
			ctx context.Context, selectBuilder squirrel.SelectBuilder,
		) ([]*BlackBoxCaseAssistantDocumentSessionRelationship, error)
		FindByProjectIdAssistantIdSessionId(
			ctx context.Context, projectId, sessionId string,
		) ([]*BlackBoxCaseAssistantDocumentSessionRelationship, error)
		FindOneByProjectIdAssistantIdSessionId(
			ctx context.Context, projectId, assistantId, sessionId string,
		) (*BlackBoxCaseAssistantDocumentSessionRelationship, error)
		FindOneByProjectIdAssistantIdSessionIdDocumentId(
			ctx context.Context, projectId, assistantId, sessionId, documentId string,
		) (*BlackBoxCaseAssistantDocumentSessionRelationship, error)
		FindJoinDocByProjectIdAssistantIdSessionId(
			ctx context.Context, projectId, assistantId, sessionId string,
		) ([]*MergeRelationshipAndDocument, error)
		DeleteByProjectIdDocumentIds(
			ctx context.Context, session sqlx.Session, projectId string, documentIds ...string,
		) error
		DeleteByProjectIdAssistantId(
			ctx context.Context, session sqlx.Session, projectId, assistantId string,
		) error
		DeleteByProjectIdAssistantIdSessionIdDocumentIds(
			ctx context.Context, session sqlx.Session, projectId, assistantId, sessionId string, documentIds ...string,
		) error
		DeleteByProjectIdAssistantIdSessionId(
			ctx context.Context, session sqlx.Session, projectId, assistantId, sessionId string,
		) error
	}

	customBlackBoxCaseAssistantDocumentSessionRelationshipModel struct {
		*defaultBlackBoxCaseAssistantDocumentSessionRelationshipModel

		conn sqlx.SqlConn
	}
	MergeRelationshipAndDocument struct {
		/*ben*/
		ProjectId   string         `db:"project_id"`   // 项目ID
		AssistantId string         `db:"assistant_id"` // 助手ID
		DocumentId  string         `db:"document_id"`  // 文档ID
		CreatedBy   string         `db:"created_by"`   // 创建者的用户ID
		UpdatedBy   string         `db:"updated_by"`   // 最近一次更新者的用户ID
		DeletedBy   sql.NullString `db:"deleted_by"`   // 删除者的用户ID
		CreatedAt   time.Time      `db:"created_at"`   // 创建时间
		UpdatedAt   time.Time      `db:"updated_at"`   // 更新时间
		DeletedAt   sql.NullTime   `db:"deleted_at"`   // 删除时间
		/*join*/
		Id                  int64          `db:"id"`                   // id
		DocumentName        string         `db:"document_name"`        // 文档名称
		DocumentDescription sql.NullString `db:"document_description"` // 文档描述
		DocumentUrl         sql.NullString `db:"document_url"`         // 文档地址
		DocumentText        sql.NullString `db:"document_text"`        // 文档文本
		DocumentType        int64          `db:"document_type"`        // 文档类型：纯文本(text)：0，飞书(feishu)：1，doc(doc)：2
		Status              int64          `db:"status"`               // 文档状态：处理中：0，完成：1，失败：2
	}
)

// NewBlackBoxCaseAssistantDocumentSessionRelationshipModel returns a model for the database table.
func NewBlackBoxCaseAssistantDocumentSessionRelationshipModel(
	conn sqlx.SqlConn, c cache.CacheConf, opts ...cache.Option,
) BlackBoxCaseAssistantDocumentSessionRelationshipModel {
	return &customBlackBoxCaseAssistantDocumentSessionRelationshipModel{
		defaultBlackBoxCaseAssistantDocumentSessionRelationshipModel: newBlackBoxCaseAssistantDocumentSessionRelationshipModel(
			conn, c, opts...,
		),
		conn: conn,
	}
}

func (m *customBlackBoxCaseAssistantDocumentSessionRelationshipModel) Table() string {
	return m.table
}

func (m *customBlackBoxCaseAssistantDocumentSessionRelationshipModel) Fields() []string {
	return blackBoxCaseAssistantDocumentSessionRelationshipFieldNames
}

func (m *customBlackBoxCaseAssistantDocumentSessionRelationshipModel) Trans(
	ctx context.Context, fn func(context context.Context, session sqlx.Session) error,
) error {
	return m.TransactCtx(ctx, fn)
}

func (m *customBlackBoxCaseAssistantDocumentSessionRelationshipModel) InsertBuilder(data *BlackBoxCaseAssistantDocumentSessionRelationship) squirrel.InsertBuilder {
	// TODO: fill the insert values
	return squirrel.Insert(m.table).Columns(blackBoxCaseAssistantDocumentSessionRelationshipInsertFields...).Values()
}

func (m *customBlackBoxCaseAssistantDocumentSessionRelationshipModel) UpdateBuilder(data *BlackBoxCaseAssistantDocumentSessionRelationship) squirrel.UpdateBuilder {
	eq := squirrel.Eq{
		// TODO: fill the update values
	}
	return squirrel.Update(m.table).SetMap(eq).Where("`id` = ?", data.Id)
}

func (m *customBlackBoxCaseAssistantDocumentSessionRelationshipModel) SelectBuilder() squirrel.SelectBuilder {
	return squirrel.Select(blackBoxCaseAssistantDocumentSessionRelationshipFieldNames...).Where(
		"`deleted` = ?", constants.NotDeleted,
	).From(m.table)
}

func (m *customBlackBoxCaseAssistantDocumentSessionRelationshipModel) SelectCountBuilder() squirrel.SelectBuilder {
	return squirrel.Select("count(*)").Where("`deleted` = ?", constants.NotDeleted).From(m.table)
}

func (m *customBlackBoxCaseAssistantDocumentSessionRelationshipModel) FindCount(
	ctx context.Context, countBuilder squirrel.SelectBuilder,
) (int64, error) {
	query, values, err := countBuilder.ToSql()
	if err != nil {
		return 0, err
	}

	var resp int64
	err = m.QueryRowNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return 0, err
	}
}

func (m *customBlackBoxCaseAssistantDocumentSessionRelationshipModel) FindNoCacheByQuery(
	ctx context.Context, selectBuilder squirrel.SelectBuilder,
) ([]*BlackBoxCaseAssistantDocumentSessionRelationship, error) {
	query, values, err := selectBuilder.ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*BlackBoxCaseAssistantDocumentSessionRelationship
	err = m.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

func (m *customBlackBoxCaseAssistantDocumentSessionRelationshipModel) FindOneByProjectIdAssistantIdSessionId(
	ctx context.Context, projectId, assistantId, sessionId string,
) (*BlackBoxCaseAssistantDocumentSessionRelationship, error) {
	sqlB := m.SelectBuilder().Where(
		"`project_id` = ? and `session_id` = ? and `assistant_id` = ?", projectId, sessionId, assistantId,
	).Limit(1)
	query, values, err := sqlB.ToSql()
	if err != nil {
		return nil, err
	}
	resp := new(BlackBoxCaseAssistantDocumentSessionRelationship)
	err = m.QueryRowNoCacheCtx(ctx, resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

func (m *customBlackBoxCaseAssistantDocumentSessionRelationshipModel) FindByProjectIdAssistantIdSessionId(
	ctx context.Context, projectId, sessionId string,
) ([]*BlackBoxCaseAssistantDocumentSessionRelationship, error) {
	sqlB := m.SelectBuilder().Where(
		"`project_id` = ? and `session_id` = ?", projectId, sessionId,
	)
	query, values, err := sqlB.ToSql()
	if err != nil {
		return nil, err
	}
	var resp []*BlackBoxCaseAssistantDocumentSessionRelationship
	err = m.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

func (m *customBlackBoxCaseAssistantDocumentSessionRelationshipModel) FindJoinDocByProjectIdAssistantIdSessionId(
	ctx context.Context, projectId, assistantId, sessionId string,
) ([]*MergeRelationshipAndDocument, error) {
	documentSessionRelationshipTableAs := "bbcadsr"
	documentSessionRelationshipToFields := AddTableNameToFields(
		documentSessionRelationshipTableAs,
		stringx.Remove(blackBoxCaseAssistantDocumentSessionRelationshipFieldNames, "`id`"),
	)
	documentSessionRelationshipToFields = append(
		documentSessionRelationshipToFields,
		"bbcd.`document_name` as document_name",
		"bbcd.`document_description` as document_description",
		"bbcd.`document_url` as document_url",
		"bbcd.`document_text` as document_text",
		"bbcd.`document_type` as document_type",
		"bbcd.`status` as status",
		"bbcd.`id` as id",
	)
	documentSessionRelationshipSql := squirrel.Select(
		documentSessionRelationshipToFields...,
	).From(m.Table()+" AS "+documentSessionRelationshipTableAs).LeftJoin(
		fmt.Sprintf(
			"%s as bbcd on bbcd.`project_id` = bbcadsr.`project_id` and bbcd.`assistant_id` = bbcadsr.`assistant_id` and bbcd.`deleted` = bbcadsr.`deleted` and bbcd.`document_id` = bbcadsr.`document_id`",
			"black_box_case_document",
		),
	).Where(
		"bbcadsr.`project_id` = ? AND bbcadsr.`assistant_id` = ?  AND bbcadsr.`session_id` = ? AND bbcadsr.`deleted` = ?",
		projectId, assistantId,
		sessionId, constants.NotDeleted,
	)
	query, values, err := documentSessionRelationshipSql.ToSql()
	if err != nil {
		return nil, err
	}
	var resp []*MergeRelationshipAndDocument
	err = m.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

func (m *customBlackBoxCaseAssistantDocumentSessionRelationshipModel) FindOneByProjectIdAssistantIdSessionIdDocumentId(
	ctx context.Context, projectId, assistantId, sessionId, documentId string,
) (*BlackBoxCaseAssistantDocumentSessionRelationship, error) {
	sqlB := m.SelectBuilder().Where(
		"`project_id` = ? and `assistant_id` = ? and `sessionId` = ? and `document_id` = ? ", projectId, assistantId,
		sessionId, documentId,
	).Limit(1)
	query, values, err := sqlB.ToSql()
	if err != nil {
		return nil, err
	}
	resp := new(BlackBoxCaseAssistantDocumentSessionRelationship)
	err = m.QueryRowNoCacheCtx(ctx, resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

func (l *customBlackBoxCaseAssistantDocumentSessionRelationshipModel) DeleteByProjectIdDocumentIds(
	ctx context.Context, session sqlx.Session, projectId string, documentIds ...string,
) error {
	sqlB := squirrel.Delete(l.Table()).Where(
		"`project_id` = ?", projectId,
	).Where(squirrel.Eq{"`document_id`": documentIds})
	query, values, err := sqlB.ToSql()
	if err != nil {
		return err
	}
	if session != nil {
		_, err := session.ExecCtx(ctx, query, values...)
		return err
	}
	_, err = l.conn.ExecCtx(ctx, query, values...)
	return err
}

func (l *customBlackBoxCaseAssistantDocumentSessionRelationshipModel) DeleteByProjectIdAssistantIdSessionIdDocumentIds(
	ctx context.Context, session sqlx.Session, projectId, assistantId, sessionId string, documentIds ...string,
) error {
	sqlB := squirrel.Delete(l.Table()).Where(
		"`project_id` = ? and `assistant_id` = ? and `session_id` = ?", projectId, assistantId, sessionId,
	).Where(squirrel.Eq{"`document_id`": documentIds})
	query, values, err := sqlB.ToSql()
	if err != nil {
		return err
	}
	if session != nil {
		_, err := session.ExecCtx(ctx, query, values...)
		return err
	}
	_, err = l.conn.ExecCtx(ctx, query, values...)
	return err
}

func (l *customBlackBoxCaseAssistantDocumentSessionRelationshipModel) DeleteByProjectIdAssistantIdSessionId(
	ctx context.Context, session sqlx.Session, projectId, assistantId, sessionId string,
) error {
	sqlB := squirrel.Delete(l.Table()).Where(
		"`project_id` = ? and `assistant_id` = ? and `session_id` = ?", projectId, assistantId, sessionId,
	)
	query, values, err := sqlB.ToSql()
	if err != nil {
		return err
	}
	if session != nil {
		_, err := session.ExecCtx(ctx, query, values...)
		return err
	}
	_, err = l.conn.ExecCtx(ctx, query, values...)
	return err
}

func (l *customBlackBoxCaseAssistantDocumentSessionRelationshipModel) DeleteByProjectIdAssistantId(
	ctx context.Context, session sqlx.Session, projectId, assistantId string,
) error {
	sqlB := squirrel.Delete(l.Table()).Where(
		"`project_id` = ? and `assistant_id` = ?", projectId, assistantId,
	)
	query, values, err := sqlB.ToSql()
	if err != nil {
		return err
	}
	if session != nil {
		_, err := session.ExecCtx(ctx, query, values...)
		return err
	}
	_, err = l.conn.ExecCtx(ctx, query, values...)
	return err
}
