// Code generated by goctl. DO NOT EDIT.
// versions:
//  goctl version: 1.7.4

package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlc"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
)

var (
	blackBoxCaseDocumentTableName           = "`black_box_case_document`"
	blackBoxCaseDocumentFieldNames          = builder.RawFieldNames(&BlackBoxCaseDocument{})
	blackBoxCaseDocumentRows                = strings.Join(blackBoxCaseDocumentFieldNames, ",")
	blackBoxCaseDocumentRowsExpectAutoSet   = strings.Join(stringx.Remove(blackBoxCaseDocumentFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), ",")
	blackBoxCaseDocumentRowsWithPlaceHolder = strings.Join(stringx.Remove(blackBoxCaseDocumentFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), "=?,") + "=?"

	cacheArtificialIntelligenceBlackBoxCaseDocumentIdPrefix                             = "cache:artificialIntelligence:blackBoxCaseDocument:id:"
	cacheArtificialIntelligenceBlackBoxCaseDocumentProjectIdAssistantIdDocumentIdPrefix = "cache:artificialIntelligence:blackBoxCaseDocument:projectId:assistantId:documentId:"
)

type (
	blackBoxCaseDocumentModel interface {
		Insert(ctx context.Context, session sqlx.Session, data *BlackBoxCaseDocument) (sql.Result, error)
		FindOne(ctx context.Context, id int64) (*BlackBoxCaseDocument, error)
		FindOneByProjectIdAssistantIdDocumentId(ctx context.Context, projectId string, assistantId string, documentId string) (*BlackBoxCaseDocument, error)
		Update(ctx context.Context, session sqlx.Session, data *BlackBoxCaseDocument) (sql.Result, error)
		Delete(ctx context.Context, session sqlx.Session, id int64) error
		LogicDelete(ctx context.Context, session sqlx.Session, id int64) error
	}

	defaultBlackBoxCaseDocumentModel struct {
		sqlc.CachedConn
		table string
	}

	BlackBoxCaseDocument struct {
		Id                  int64          `db:"id"`                   // 自增ID
		ProjectId           string         `db:"project_id"`           // 项目ID
		AssistantId         string         `db:"assistant_id"`         // 助手ID
		DocumentId          string         `db:"document_id"`          // 文档ID
		DocumentName        string         `db:"document_name"`        // 文档名称
		DocumentDescription sql.NullString `db:"document_description"` // 文档描述
		DocumentUrl         sql.NullString `db:"document_url"`         // 文档地址
		DocumentText        sql.NullString `db:"document_text"`        // 文档文本
		DocumentType        int64          `db:"document_type"`        // 文档类型：纯文本(text)：0，飞书(feishu)：1，doc(doc)：2
		ExternalDocumentId  sql.NullString `db:"external_document_id"` // 外部文档ID
		Status              int64          `db:"status"`               // 文档状态：处理中：0，完成：1，失败：2
		Deleted             int64          `db:"deleted"`              // 逻辑删除标识（未删除、已删除）
		CreatedBy           string         `db:"created_by"`           // 创建者的用户ID
		UpdatedBy           string         `db:"updated_by"`           // 最近一次更新者的用户ID
		DeletedBy           sql.NullString `db:"deleted_by"`           // 删除者的用户ID
		CreatedAt           time.Time      `db:"created_at"`           // 创建时间
		UpdatedAt           time.Time      `db:"updated_at"`           // 更新时间
		DeletedAt           sql.NullTime   `db:"deleted_at"`           // 删除时间
	}
)

func newBlackBoxCaseDocumentModel(conn sqlx.SqlConn, c cache.CacheConf, opts ...cache.Option) *defaultBlackBoxCaseDocumentModel {
	return &defaultBlackBoxCaseDocumentModel{
		CachedConn: sqlc.NewConn(conn, c, opts...),
		table:      "`black_box_case_document`",
	}
}

func (m *defaultBlackBoxCaseDocumentModel) Delete(ctx context.Context, session sqlx.Session, id int64) error {
	data, err := m.FindOne(ctx, id)
	if err != nil {
		return err
	}

	artificialIntelligenceBlackBoxCaseDocumentIdKey := fmt.Sprintf("%s%v", cacheArtificialIntelligenceBlackBoxCaseDocumentIdPrefix, id)
	artificialIntelligenceBlackBoxCaseDocumentProjectIdAssistantIdDocumentIdKey := fmt.Sprintf("%s%v:%v:%v", cacheArtificialIntelligenceBlackBoxCaseDocumentProjectIdAssistantIdDocumentIdPrefix, data.ProjectId, data.AssistantId, data.DocumentId)
	_, err = m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("delete from %s where `id` = ?", m.table)
		if session != nil {
			return session.ExecCtx(ctx, query, id)
		}
		return conn.ExecCtx(ctx, query, id)
	}, artificialIntelligenceBlackBoxCaseDocumentIdKey, artificialIntelligenceBlackBoxCaseDocumentProjectIdAssistantIdDocumentIdKey)
	return err
}

func (m *defaultBlackBoxCaseDocumentModel) LogicDelete(ctx context.Context, session sqlx.Session, id int64) error {
	data, err := m.FindOne(ctx, id)
	if err != nil {
		return err
	}

	artificialIntelligenceBlackBoxCaseDocumentIdKey := fmt.Sprintf("%s%v", cacheArtificialIntelligenceBlackBoxCaseDocumentIdPrefix, id)
	artificialIntelligenceBlackBoxCaseDocumentProjectIdAssistantIdDocumentIdKey := fmt.Sprintf("%s%v:%v:%v", cacheArtificialIntelligenceBlackBoxCaseDocumentProjectIdAssistantIdDocumentIdPrefix, data.ProjectId, data.AssistantId, data.DocumentId)
	_, err = m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set `deleted` = ?, `deleted_at` = ? where `id` = ?", m.table)
		if session != nil {
			return session.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
		}
		return conn.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
	}, artificialIntelligenceBlackBoxCaseDocumentIdKey, artificialIntelligenceBlackBoxCaseDocumentProjectIdAssistantIdDocumentIdKey)
	return err
}

func (m *defaultBlackBoxCaseDocumentModel) FindOne(ctx context.Context, id int64) (*BlackBoxCaseDocument, error) {
	artificialIntelligenceBlackBoxCaseDocumentIdKey := fmt.Sprintf("%s%v", cacheArtificialIntelligenceBlackBoxCaseDocumentIdPrefix, id)
	var resp BlackBoxCaseDocument
	err := m.QueryRowCtx(ctx, &resp, artificialIntelligenceBlackBoxCaseDocumentIdKey, func(ctx context.Context, conn sqlx.SqlConn, v any) error {
		query := fmt.Sprintf("select %s from %s where `id` = ? and `deleted` = ? limit 1", blackBoxCaseDocumentRows, m.table)
		return conn.QueryRowCtx(ctx, v, query, id, constants.NotDeleted)
	})
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultBlackBoxCaseDocumentModel) FindOneByProjectIdAssistantIdDocumentId(ctx context.Context, projectId string, assistantId string, documentId string) (*BlackBoxCaseDocument, error) {
	artificialIntelligenceBlackBoxCaseDocumentProjectIdAssistantIdDocumentIdKey := fmt.Sprintf("%s%v:%v:%v", cacheArtificialIntelligenceBlackBoxCaseDocumentProjectIdAssistantIdDocumentIdPrefix, projectId, assistantId, documentId)
	var resp BlackBoxCaseDocument
	err := m.QueryRowIndexCtx(ctx, &resp, artificialIntelligenceBlackBoxCaseDocumentProjectIdAssistantIdDocumentIdKey, m.formatPrimary, func(ctx context.Context, conn sqlx.SqlConn, v any) (i any, e error) {
		query := fmt.Sprintf("select %s from %s where `project_id` = ? and `assistant_id` = ? and `document_id` = ? and `deleted` = ? limit 1", blackBoxCaseDocumentRows, m.table)
		if err := conn.QueryRowCtx(ctx, &resp, query, projectId, assistantId, documentId, constants.NotDeleted); err != nil {
			return nil, err
		}
		return resp.Id, nil
	}, m.queryPrimary)
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultBlackBoxCaseDocumentModel) Insert(ctx context.Context, session sqlx.Session, data *BlackBoxCaseDocument) (sql.Result, error) {
	artificialIntelligenceBlackBoxCaseDocumentIdKey := fmt.Sprintf("%s%v", cacheArtificialIntelligenceBlackBoxCaseDocumentIdPrefix, data.Id)
	artificialIntelligenceBlackBoxCaseDocumentProjectIdAssistantIdDocumentIdKey := fmt.Sprintf("%s%v:%v:%v", cacheArtificialIntelligenceBlackBoxCaseDocumentProjectIdAssistantIdDocumentIdPrefix, data.ProjectId, data.AssistantId, data.DocumentId)

	return m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", m.table, blackBoxCaseDocumentRowsExpectAutoSet)
		if session != nil {
			return session.ExecCtx(ctx, query, data.ProjectId, data.AssistantId, data.DocumentId, data.DocumentName, data.DocumentDescription, data.DocumentUrl, data.DocumentText, data.DocumentType, data.ExternalDocumentId, data.Status, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy)
		}
		return conn.ExecCtx(ctx, query, data.ProjectId, data.AssistantId, data.DocumentId, data.DocumentName, data.DocumentDescription, data.DocumentUrl, data.DocumentText, data.DocumentType, data.ExternalDocumentId, data.Status, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy)
	}, artificialIntelligenceBlackBoxCaseDocumentIdKey, artificialIntelligenceBlackBoxCaseDocumentProjectIdAssistantIdDocumentIdKey)
}

func (m *defaultBlackBoxCaseDocumentModel) Update(ctx context.Context, session sqlx.Session, newData *BlackBoxCaseDocument) (sql.Result, error) {
	data, err := m.FindOne(ctx, newData.Id)
	if err != nil {
		return nil, err
	}

	artificialIntelligenceBlackBoxCaseDocumentIdKey := fmt.Sprintf("%s%v", cacheArtificialIntelligenceBlackBoxCaseDocumentIdPrefix, data.Id)
	artificialIntelligenceBlackBoxCaseDocumentProjectIdAssistantIdDocumentIdKey := fmt.Sprintf("%s%v:%v:%v", cacheArtificialIntelligenceBlackBoxCaseDocumentProjectIdAssistantIdDocumentIdPrefix, data.ProjectId, data.AssistantId, data.DocumentId)

	return m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, blackBoxCaseDocumentRowsWithPlaceHolder)
		if session != nil {
			return session.ExecCtx(ctx, query, newData.ProjectId, newData.AssistantId, newData.DocumentId, newData.DocumentName, newData.DocumentDescription, newData.DocumentUrl, newData.DocumentText, newData.DocumentType, newData.ExternalDocumentId, newData.Status, newData.Deleted, newData.CreatedBy, newData.UpdatedBy, newData.DeletedBy, newData.Id)
		}
		return conn.ExecCtx(ctx, query, newData.ProjectId, newData.AssistantId, newData.DocumentId, newData.DocumentName, newData.DocumentDescription, newData.DocumentUrl, newData.DocumentText, newData.DocumentType, newData.ExternalDocumentId, newData.Status, newData.Deleted, newData.CreatedBy, newData.UpdatedBy, newData.DeletedBy, newData.Id)
	}, artificialIntelligenceBlackBoxCaseDocumentIdKey, artificialIntelligenceBlackBoxCaseDocumentProjectIdAssistantIdDocumentIdKey)
}

func (m *defaultBlackBoxCaseDocumentModel) formatPrimary(primary any) string {
	return fmt.Sprintf("%s%v", cacheArtificialIntelligenceBlackBoxCaseDocumentIdPrefix, primary)
}

func (m *defaultBlackBoxCaseDocumentModel) queryPrimary(ctx context.Context, conn sqlx.SqlConn, v, primary any) error {
	query := fmt.Sprintf("select %s from %s where `id` = ? limit 1", blackBoxCaseDocumentRows, m.table)
	return conn.QueryRowCtx(ctx, v, query, primary)
}

func (m *defaultBlackBoxCaseDocumentModel) tableName() string {
	return m.table
}
