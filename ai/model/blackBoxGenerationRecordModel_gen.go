// Code generated by goctl. DO NOT EDIT.
// versions:
//  goctl version: 1.7.4

package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlc"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
)

var (
	blackBoxGenerationRecordTableName           = "`black_box_generation_record`"
	blackBoxGenerationRecordFieldNames          = builder.RawFieldNames(&BlackBoxGenerationRecord{})
	blackBoxGenerationRecordRows                = strings.Join(blackBoxGenerationRecordFieldNames, ",")
	blackBoxGenerationRecordRowsExpectAutoSet   = strings.Join(stringx.Remove(blackBoxGenerationRecordFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), ",")
	blackBoxGenerationRecordRowsWithPlaceHolder = strings.Join(stringx.Remove(blackBoxGenerationRecordFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), "=?,") + "=?"

	cacheArtificialIntelligenceBlackBoxGenerationRecordIdPrefix          = "cache:artificialIntelligence:blackBoxGenerationRecord:id:"
	cacheArtificialIntelligenceBlackBoxGenerationRecordCreatedDatePrefix = "cache:artificialIntelligence:blackBoxGenerationRecord:createdDate:"
)

type (
	blackBoxGenerationRecordModel interface {
		Insert(ctx context.Context, session sqlx.Session, data *BlackBoxGenerationRecord) (sql.Result, error)
		FindOne(ctx context.Context, id int64) (*BlackBoxGenerationRecord, error)
		FindOneByCreatedDate(ctx context.Context, createdDate time.Time) (*BlackBoxGenerationRecord, error)
		Update(ctx context.Context, session sqlx.Session, data *BlackBoxGenerationRecord) (sql.Result, error)
		Delete(ctx context.Context, session sqlx.Session, id int64) error
		LogicDelete(ctx context.Context, session sqlx.Session, id int64) error
	}

	defaultBlackBoxGenerationRecordModel struct {
		sqlc.CachedConn
		table string
	}

	BlackBoxGenerationRecord struct {
		Id                      int64     `db:"id"`                         // 主键
		FunctionPointCount      int64     `db:"function_point_count"`       // 生成功能点次数
		TestSceneCount          int64     `db:"test_scene_count"`           // 生成测试场景次数
		SupplementDocumentCount int64     `db:"supplement_document_count"`  // 补充文档次数
		CreatedDate             time.Time `db:"created_date"`               // 创建日期
		BaseMapCaseCount        int64     `db:"base_map_case_count"`        // 生成评审导图次数
		UpdatedAddCount         int64     `db:"updated_add_count"`          // 修改后生成的用例条数
		CaseNameUpdateCount     int64     `db:"case_name_update_count"`     // 编辑用例时更新用例名称的次数
		PreconditionUpdateCount int64     `db:"precondition_update_count"`  // 编辑用例时更新前置条件的次数
		CaseStepUpdateCount     int64     `db:"case_step_update_count"`     // 编辑用例时更新用例步骤的次数
		ExpectResultUpdateCount int64     `db:"expect_result_update_count"` // 编辑用例时更新预期结果的次数
		CaseLevelUpdateCount    int64     `db:"case_level_update_count"`    // 编辑用例时更新用例等级的次数
	}
)

func newBlackBoxGenerationRecordModel(conn sqlx.SqlConn, c cache.CacheConf, opts ...cache.Option) *defaultBlackBoxGenerationRecordModel {
	return &defaultBlackBoxGenerationRecordModel{
		CachedConn: sqlc.NewConn(conn, c, opts...),
		table:      "`black_box_generation_record`",
	}
}

func (m *defaultBlackBoxGenerationRecordModel) Delete(ctx context.Context, session sqlx.Session, id int64) error {
	data, err := m.FindOne(ctx, id)
	if err != nil {
		return err
	}

	artificialIntelligenceBlackBoxGenerationRecordCreatedDateKey := fmt.Sprintf("%s%v", cacheArtificialIntelligenceBlackBoxGenerationRecordCreatedDatePrefix, data.CreatedDate)
	artificialIntelligenceBlackBoxGenerationRecordIdKey := fmt.Sprintf("%s%v", cacheArtificialIntelligenceBlackBoxGenerationRecordIdPrefix, id)
	_, err = m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("delete from %s where `id` = ?", m.table)
		if session != nil {
			return session.ExecCtx(ctx, query, id)
		}
		return conn.ExecCtx(ctx, query, id)
	}, artificialIntelligenceBlackBoxGenerationRecordCreatedDateKey, artificialIntelligenceBlackBoxGenerationRecordIdKey)
	return err
}

func (m *defaultBlackBoxGenerationRecordModel) LogicDelete(ctx context.Context, session sqlx.Session, id int64) error {
	data, err := m.FindOne(ctx, id)
	if err != nil {
		return err
	}

	artificialIntelligenceBlackBoxGenerationRecordCreatedDateKey := fmt.Sprintf("%s%v", cacheArtificialIntelligenceBlackBoxGenerationRecordCreatedDatePrefix, data.CreatedDate)
	artificialIntelligenceBlackBoxGenerationRecordIdKey := fmt.Sprintf("%s%v", cacheArtificialIntelligenceBlackBoxGenerationRecordIdPrefix, id)
	_, err = m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set `deleted` = ?, `deleted_at` = ? where `id` = ?", m.table)
		if session != nil {
			return session.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
		}
		return conn.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
	}, artificialIntelligenceBlackBoxGenerationRecordCreatedDateKey, artificialIntelligenceBlackBoxGenerationRecordIdKey)
	return err
}

func (m *defaultBlackBoxGenerationRecordModel) FindOne(ctx context.Context, id int64) (*BlackBoxGenerationRecord, error) {
	artificialIntelligenceBlackBoxGenerationRecordIdKey := fmt.Sprintf("%s%v", cacheArtificialIntelligenceBlackBoxGenerationRecordIdPrefix, id)
	var resp BlackBoxGenerationRecord
	err := m.QueryRowCtx(ctx, &resp, artificialIntelligenceBlackBoxGenerationRecordIdKey, func(ctx context.Context, conn sqlx.SqlConn, v any) error {
		query := fmt.Sprintf("select %s from %s where `id` = ? and `deleted` = ? limit 1", blackBoxGenerationRecordRows, m.table)
		return conn.QueryRowCtx(ctx, v, query, id, constants.NotDeleted)
	})
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultBlackBoxGenerationRecordModel) FindOneByCreatedDate(ctx context.Context, createdDate time.Time) (*BlackBoxGenerationRecord, error) {
	artificialIntelligenceBlackBoxGenerationRecordCreatedDateKey := fmt.Sprintf("%s%v", cacheArtificialIntelligenceBlackBoxGenerationRecordCreatedDatePrefix, createdDate)
	var resp BlackBoxGenerationRecord
	err := m.QueryRowIndexCtx(ctx, &resp, artificialIntelligenceBlackBoxGenerationRecordCreatedDateKey, m.formatPrimary, func(ctx context.Context, conn sqlx.SqlConn, v any) (i any, e error) {
		query := fmt.Sprintf("select %s from %s where `created_date` = ? and `deleted` = ? limit 1", blackBoxGenerationRecordRows, m.table)
		if err := conn.QueryRowCtx(ctx, &resp, query, createdDate, constants.NotDeleted); err != nil {
			return nil, err
		}
		return resp.Id, nil
	}, m.queryPrimary)
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultBlackBoxGenerationRecordModel) Insert(ctx context.Context, session sqlx.Session, data *BlackBoxGenerationRecord) (sql.Result, error) {
	artificialIntelligenceBlackBoxGenerationRecordCreatedDateKey := fmt.Sprintf("%s%v", cacheArtificialIntelligenceBlackBoxGenerationRecordCreatedDatePrefix, data.CreatedDate)
	artificialIntelligenceBlackBoxGenerationRecordIdKey := fmt.Sprintf("%s%v", cacheArtificialIntelligenceBlackBoxGenerationRecordIdPrefix, data.Id)

	return m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", m.table, blackBoxGenerationRecordRowsExpectAutoSet)
		if session != nil {
			return session.ExecCtx(ctx, query, data.FunctionPointCount, data.TestSceneCount, data.SupplementDocumentCount, data.CreatedDate, data.BaseMapCaseCount, data.UpdatedAddCount, data.CaseNameUpdateCount, data.PreconditionUpdateCount, data.CaseStepUpdateCount, data.ExpectResultUpdateCount, data.CaseLevelUpdateCount)
		}
		return conn.ExecCtx(ctx, query, data.FunctionPointCount, data.TestSceneCount, data.SupplementDocumentCount, data.CreatedDate, data.BaseMapCaseCount, data.UpdatedAddCount, data.CaseNameUpdateCount, data.PreconditionUpdateCount, data.CaseStepUpdateCount, data.ExpectResultUpdateCount, data.CaseLevelUpdateCount)
	}, artificialIntelligenceBlackBoxGenerationRecordCreatedDateKey, artificialIntelligenceBlackBoxGenerationRecordIdKey)
}

func (m *defaultBlackBoxGenerationRecordModel) Update(ctx context.Context, session sqlx.Session, newData *BlackBoxGenerationRecord) (sql.Result, error) {
	data, err := m.FindOne(ctx, newData.Id)
	if err != nil {
		return nil, err
	}

	artificialIntelligenceBlackBoxGenerationRecordCreatedDateKey := fmt.Sprintf("%s%v", cacheArtificialIntelligenceBlackBoxGenerationRecordCreatedDatePrefix, data.CreatedDate)
	artificialIntelligenceBlackBoxGenerationRecordIdKey := fmt.Sprintf("%s%v", cacheArtificialIntelligenceBlackBoxGenerationRecordIdPrefix, data.Id)

	return m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, blackBoxGenerationRecordRowsWithPlaceHolder)
		if session != nil {
			return session.ExecCtx(ctx, query, newData.FunctionPointCount, newData.TestSceneCount, newData.SupplementDocumentCount, newData.CreatedDate, newData.BaseMapCaseCount, newData.UpdatedAddCount, newData.CaseNameUpdateCount, newData.PreconditionUpdateCount, newData.CaseStepUpdateCount, newData.ExpectResultUpdateCount, newData.CaseLevelUpdateCount, newData.Id)
		}
		return conn.ExecCtx(ctx, query, newData.FunctionPointCount, newData.TestSceneCount, newData.SupplementDocumentCount, newData.CreatedDate, newData.BaseMapCaseCount, newData.UpdatedAddCount, newData.CaseNameUpdateCount, newData.PreconditionUpdateCount, newData.CaseStepUpdateCount, newData.ExpectResultUpdateCount, newData.CaseLevelUpdateCount, newData.Id)
	}, artificialIntelligenceBlackBoxGenerationRecordCreatedDateKey, artificialIntelligenceBlackBoxGenerationRecordIdKey)
}

func (m *defaultBlackBoxGenerationRecordModel) formatPrimary(primary any) string {
	return fmt.Sprintf("%s%v", cacheArtificialIntelligenceBlackBoxGenerationRecordIdPrefix, primary)
}

func (m *defaultBlackBoxGenerationRecordModel) queryPrimary(ctx context.Context, conn sqlx.SqlConn, v, primary any) error {
	query := fmt.Sprintf("select %s from %s where `id` = ? limit 1", blackBoxGenerationRecordRows, m.table)
	return conn.QueryRowCtx(ctx, v, query, primary)
}

func (m *defaultBlackBoxGenerationRecordModel) tableName() string {
	return m.table
}
