// Code generated by goctl. DO NOT EDIT.
// versions:
//  goctl version: 1.7.4

package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlc"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
)

var (
	blackBoxCaseKnowledgeTableName           = "`black_box_case_knowledge`"
	blackBoxCaseKnowledgeFieldNames          = builder.RawFieldNames(&BlackBoxCaseKnowledge{})
	blackBoxCaseKnowledgeRows                = strings.Join(blackBoxCaseKnowledgeFieldNames, ",")
	blackBoxCaseKnowledgeRowsExpectAutoSet   = strings.Join(stringx.Remove(blackBoxCaseKnowledgeFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), ",")
	blackBoxCaseKnowledgeRowsWithPlaceHolder = strings.Join(stringx.Remove(blackBoxCaseKnowledgeFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), "=?,") + "=?"

	cacheArtificialIntelligenceBlackBoxCaseKnowledgeIdPrefix          = "cache:artificialIntelligence:blackBoxCaseKnowledge:id:"
	cacheArtificialIntelligenceBlackBoxCaseKnowledgeKnowledgeIdPrefix = "cache:artificialIntelligence:blackBoxCaseKnowledge:knowledgeId:"
)

type (
	blackBoxCaseKnowledgeModel interface {
		Insert(ctx context.Context, session sqlx.Session, data *BlackBoxCaseKnowledge) (sql.Result, error)
		FindOne(ctx context.Context, id uint64) (*BlackBoxCaseKnowledge, error)
		FindOneByKnowledgeId(ctx context.Context, knowledgeId string) (*BlackBoxCaseKnowledge, error)
		Update(ctx context.Context, session sqlx.Session, data *BlackBoxCaseKnowledge) (sql.Result, error)
		Delete(ctx context.Context, session sqlx.Session, id uint64) error
		LogicDelete(ctx context.Context, session sqlx.Session, id uint64) error
	}

	defaultBlackBoxCaseKnowledgeModel struct {
		sqlc.CachedConn
		table string
	}

	BlackBoxCaseKnowledge struct {
		Id          uint64         `db:"id"`           // 自增ID
		DirId       string         `db:"dir_id"`       // 目录ID
		KnowledgeId string         `db:"knowledge_id"` // 知识文档ID
		Deleted     int64          `db:"deleted"`      // 逻辑删除标识（未删除、已删除）
		CreatedBy   string         `db:"created_by"`   // 创建者的用户ID
		UpdatedBy   string         `db:"updated_by"`   // 最近一次更新者的用户ID
		DeletedBy   sql.NullString `db:"deleted_by"`   // 删除者的用户ID
		CreatedAt   time.Time      `db:"created_at"`   // 创建时间
		UpdatedAt   time.Time      `db:"updated_at"`   // 更新时间
		DeletedAt   sql.NullTime   `db:"deleted_at"`   // 删除时间
	}
)

func newBlackBoxCaseKnowledgeModel(conn sqlx.SqlConn, c cache.CacheConf, opts ...cache.Option) *defaultBlackBoxCaseKnowledgeModel {
	return &defaultBlackBoxCaseKnowledgeModel{
		CachedConn: sqlc.NewConn(conn, c, opts...),
		table:      "`black_box_case_knowledge`",
	}
}

func (m *defaultBlackBoxCaseKnowledgeModel) Delete(ctx context.Context, session sqlx.Session, id uint64) error {
	data, err := m.FindOne(ctx, id)
	if err != nil {
		return err
	}

	artificialIntelligenceBlackBoxCaseKnowledgeIdKey := fmt.Sprintf("%s%v", cacheArtificialIntelligenceBlackBoxCaseKnowledgeIdPrefix, id)
	artificialIntelligenceBlackBoxCaseKnowledgeKnowledgeIdKey := fmt.Sprintf("%s%v", cacheArtificialIntelligenceBlackBoxCaseKnowledgeKnowledgeIdPrefix, data.KnowledgeId)
	_, err = m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("delete from %s where `id` = ?", m.table)
		if session != nil {
			return session.ExecCtx(ctx, query, id)
		}
		return conn.ExecCtx(ctx, query, id)
	}, artificialIntelligenceBlackBoxCaseKnowledgeIdKey, artificialIntelligenceBlackBoxCaseKnowledgeKnowledgeIdKey)
	return err
}

func (m *defaultBlackBoxCaseKnowledgeModel) LogicDelete(ctx context.Context, session sqlx.Session, id uint64) error {
	data, err := m.FindOne(ctx, id)
	if err != nil {
		return err
	}

	artificialIntelligenceBlackBoxCaseKnowledgeIdKey := fmt.Sprintf("%s%v", cacheArtificialIntelligenceBlackBoxCaseKnowledgeIdPrefix, id)
	artificialIntelligenceBlackBoxCaseKnowledgeKnowledgeIdKey := fmt.Sprintf("%s%v", cacheArtificialIntelligenceBlackBoxCaseKnowledgeKnowledgeIdPrefix, data.KnowledgeId)
	_, err = m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set `deleted` = ?, `deleted_at` = ? where `id` = ?", m.table)
		if session != nil {
			return session.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
		}
		return conn.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
	}, artificialIntelligenceBlackBoxCaseKnowledgeIdKey, artificialIntelligenceBlackBoxCaseKnowledgeKnowledgeIdKey)
	return err
}

func (m *defaultBlackBoxCaseKnowledgeModel) FindOne(ctx context.Context, id uint64) (*BlackBoxCaseKnowledge, error) {
	artificialIntelligenceBlackBoxCaseKnowledgeIdKey := fmt.Sprintf("%s%v", cacheArtificialIntelligenceBlackBoxCaseKnowledgeIdPrefix, id)
	var resp BlackBoxCaseKnowledge
	err := m.QueryRowCtx(ctx, &resp, artificialIntelligenceBlackBoxCaseKnowledgeIdKey, func(ctx context.Context, conn sqlx.SqlConn, v any) error {
		query := fmt.Sprintf("select %s from %s where `id` = ? and `deleted` = ? limit 1", blackBoxCaseKnowledgeRows, m.table)
		return conn.QueryRowCtx(ctx, v, query, id, constants.NotDeleted)
	})
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultBlackBoxCaseKnowledgeModel) FindOneByKnowledgeId(ctx context.Context, knowledgeId string) (*BlackBoxCaseKnowledge, error) {
	artificialIntelligenceBlackBoxCaseKnowledgeKnowledgeIdKey := fmt.Sprintf("%s%v", cacheArtificialIntelligenceBlackBoxCaseKnowledgeKnowledgeIdPrefix, knowledgeId)
	var resp BlackBoxCaseKnowledge
	err := m.QueryRowIndexCtx(ctx, &resp, artificialIntelligenceBlackBoxCaseKnowledgeKnowledgeIdKey, m.formatPrimary, func(ctx context.Context, conn sqlx.SqlConn, v any) (i any, e error) {
		query := fmt.Sprintf("select %s from %s where `knowledge_id` = ? and `deleted` = ? limit 1", blackBoxCaseKnowledgeRows, m.table)
		if err := conn.QueryRowCtx(ctx, &resp, query, knowledgeId, constants.NotDeleted); err != nil {
			return nil, err
		}
		return resp.Id, nil
	}, m.queryPrimary)
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultBlackBoxCaseKnowledgeModel) Insert(ctx context.Context, session sqlx.Session, data *BlackBoxCaseKnowledge) (sql.Result, error) {
	artificialIntelligenceBlackBoxCaseKnowledgeIdKey := fmt.Sprintf("%s%v", cacheArtificialIntelligenceBlackBoxCaseKnowledgeIdPrefix, data.Id)
	artificialIntelligenceBlackBoxCaseKnowledgeKnowledgeIdKey := fmt.Sprintf("%s%v", cacheArtificialIntelligenceBlackBoxCaseKnowledgeKnowledgeIdPrefix, data.KnowledgeId)

	return m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?, ?, ?)", m.table, blackBoxCaseKnowledgeRowsExpectAutoSet)
		if session != nil {
			return session.ExecCtx(ctx, query, data.DirId, data.KnowledgeId, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy)
		}
		return conn.ExecCtx(ctx, query, data.DirId, data.KnowledgeId, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy)
	}, artificialIntelligenceBlackBoxCaseKnowledgeIdKey, artificialIntelligenceBlackBoxCaseKnowledgeKnowledgeIdKey)
}

func (m *defaultBlackBoxCaseKnowledgeModel) Update(ctx context.Context, session sqlx.Session, newData *BlackBoxCaseKnowledge) (sql.Result, error) {
	data, err := m.FindOne(ctx, newData.Id)
	if err != nil {
		return nil, err
	}

	artificialIntelligenceBlackBoxCaseKnowledgeIdKey := fmt.Sprintf("%s%v", cacheArtificialIntelligenceBlackBoxCaseKnowledgeIdPrefix, data.Id)
	artificialIntelligenceBlackBoxCaseKnowledgeKnowledgeIdKey := fmt.Sprintf("%s%v", cacheArtificialIntelligenceBlackBoxCaseKnowledgeKnowledgeIdPrefix, data.KnowledgeId)

	return m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, blackBoxCaseKnowledgeRowsWithPlaceHolder)
		if session != nil {
			return session.ExecCtx(ctx, query, newData.DirId, newData.KnowledgeId, newData.Deleted, newData.CreatedBy, newData.UpdatedBy, newData.DeletedBy, newData.Id)
		}
		return conn.ExecCtx(ctx, query, newData.DirId, newData.KnowledgeId, newData.Deleted, newData.CreatedBy, newData.UpdatedBy, newData.DeletedBy, newData.Id)
	}, artificialIntelligenceBlackBoxCaseKnowledgeIdKey, artificialIntelligenceBlackBoxCaseKnowledgeKnowledgeIdKey)
}

func (m *defaultBlackBoxCaseKnowledgeModel) formatPrimary(primary any) string {
	return fmt.Sprintf("%s%v", cacheArtificialIntelligenceBlackBoxCaseKnowledgeIdPrefix, primary)
}

func (m *defaultBlackBoxCaseKnowledgeModel) queryPrimary(ctx context.Context, conn sqlx.SqlConn, v, primary any) error {
	query := fmt.Sprintf("select %s from %s where `id` = ? limit 1", blackBoxCaseKnowledgeRows, m.table)
	return conn.QueryRowCtx(ctx, v, query, primary)
}

func (m *defaultBlackBoxCaseKnowledgeModel) tableName() string {
	return m.table
}
