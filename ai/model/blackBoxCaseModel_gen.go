// Code generated by goctl. DO NOT EDIT.
// versions:
//  goctl version: 1.7.4

package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlc"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
)

var (
	blackBoxCaseTableName           = "`black_box_case`"
	blackBoxCaseFieldNames          = builder.RawFieldNames(&BlackBoxCase{})
	blackBoxCaseRows                = strings.Join(blackBoxCaseFieldNames, ",")
	blackBoxCaseRowsExpectAutoSet   = strings.Join(stringx.Remove(blackBoxCaseFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), ",")
	blackBoxCaseRowsWithPlaceHolder = strings.Join(stringx.Remove(blackBoxCaseFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), "=?,") + "=?"

	cacheArtificialIntelligenceBlackBoxCaseIdPrefix          = "cache:artificialIntelligence:blackBoxCase:id:"
	cacheArtificialIntelligenceBlackBoxCaseCaseIdPrefix      = "cache:artificialIntelligence:blackBoxCase:caseId:"
	cacheArtificialIntelligenceBlackBoxCaseDirIdCaseIdPrefix = "cache:artificialIntelligence:blackBoxCase:dirId:caseId:"
)

type (
	blackBoxCaseModel interface {
		Insert(ctx context.Context, session sqlx.Session, data *BlackBoxCase) (sql.Result, error)
		FindOne(ctx context.Context, id int64) (*BlackBoxCase, error)
		FindOneByCaseId(ctx context.Context, caseId string) (*BlackBoxCase, error)
		FindOneByDirIdCaseId(ctx context.Context, dirId string, caseId string) (*BlackBoxCase, error)
		Update(ctx context.Context, session sqlx.Session, data *BlackBoxCase) (sql.Result, error)
		Delete(ctx context.Context, session sqlx.Session, id int64) error
		LogicDelete(ctx context.Context, session sqlx.Session, id int64) error
	}

	defaultBlackBoxCaseModel struct {
		sqlc.CachedConn
		table string
	}

	BlackBoxCase struct {
		Id                          int64          `db:"id"`                             // 自增ID
		DirId                       string         `db:"dir_id"`                         // 目录ID
		CaseId                      string         `db:"case_id"`                        // 用例ID
		CaseName                    string         `db:"case_name"`                      // 用例名称
		CaseContinueToWrite         int64          `db:"case_continue_to_write"`         // 用例续写次数
		CaseModelCharacter          string         `db:"case_model_character"`           // 用例模型性格
		KnowledgeId                 string         `db:"knowledge_id"`                   // 知识文档关联ID
		CreatedBy                   string         `db:"created_by"`                     // 创建者的用户ID
		UpdatedBy                   string         `db:"updated_by"`                     // 最近一次更新者的用户ID
		DeletedBy                   sql.NullString `db:"deleted_by"`                     // 删除者的用户ID
		CreatedAt                   time.Time      `db:"created_at"`                     // 创建时间
		UpdatedAt                   time.Time      `db:"updated_at"`                     // 更新时间
		DeletedAt                   sql.NullTime   `db:"deleted_at"`                     // 删除时间
		Deleted                     int64          `db:"deleted"`                        // 逻辑删除标识（未删除、已删除）
		CaseRemarks                 string         `db:"case_remarks"`                   // 用例备注
		KnowledgeParagraphTitleId   int64          `db:"knowledge_paragraph_title_id"`   // 创建所选标题id
		ReferenceDoc                string         `db:"reference_doc"`                  // 关联文档内容
		EnableReference             int64          `db:"enable_reference"`               // 是否关联文档
		KnowledgeFixSugg            string         `db:"knowledge_fix_sugg"`             // 文档修改意见
		QueryTimes                  int64          `db:"query_times"`                    // 查询用例次数
		KnowledgeParagraphTitleText string         `db:"knowledge_paragraph_title_text"` // 知识文档段落标题
		KnowledgeSize               string         `db:"knowledge_size"`                 // 用例关联的产品文档大小
		KnowledgeParagraphTitles    string         `db:"knowledge_paragraph_titles"`     // 知识文档段落标题
	}
)

func newBlackBoxCaseModel(conn sqlx.SqlConn, c cache.CacheConf, opts ...cache.Option) *defaultBlackBoxCaseModel {
	return &defaultBlackBoxCaseModel{
		CachedConn: sqlc.NewConn(conn, c, opts...),
		table:      "`black_box_case`",
	}
}

func (m *defaultBlackBoxCaseModel) Delete(ctx context.Context, session sqlx.Session, id int64) error {
	data, err := m.FindOne(ctx, id)
	if err != nil {
		return err
	}

	artificialIntelligenceBlackBoxCaseCaseIdKey := fmt.Sprintf("%s%v", cacheArtificialIntelligenceBlackBoxCaseCaseIdPrefix, data.CaseId)
	artificialIntelligenceBlackBoxCaseDirIdCaseIdKey := fmt.Sprintf("%s%v:%v", cacheArtificialIntelligenceBlackBoxCaseDirIdCaseIdPrefix, data.DirId, data.CaseId)
	artificialIntelligenceBlackBoxCaseIdKey := fmt.Sprintf("%s%v", cacheArtificialIntelligenceBlackBoxCaseIdPrefix, id)
	_, err = m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("delete from %s where `id` = ?", m.table)
		if session != nil {
			return session.ExecCtx(ctx, query, id)
		}
		return conn.ExecCtx(ctx, query, id)
	}, artificialIntelligenceBlackBoxCaseCaseIdKey, artificialIntelligenceBlackBoxCaseDirIdCaseIdKey, artificialIntelligenceBlackBoxCaseIdKey)
	return err
}

func (m *defaultBlackBoxCaseModel) LogicDelete(ctx context.Context, session sqlx.Session, id int64) error {
	data, err := m.FindOne(ctx, id)
	if err != nil {
		return err
	}

	artificialIntelligenceBlackBoxCaseCaseIdKey := fmt.Sprintf("%s%v", cacheArtificialIntelligenceBlackBoxCaseCaseIdPrefix, data.CaseId)
	artificialIntelligenceBlackBoxCaseDirIdCaseIdKey := fmt.Sprintf("%s%v:%v", cacheArtificialIntelligenceBlackBoxCaseDirIdCaseIdPrefix, data.DirId, data.CaseId)
	artificialIntelligenceBlackBoxCaseIdKey := fmt.Sprintf("%s%v", cacheArtificialIntelligenceBlackBoxCaseIdPrefix, id)
	_, err = m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set `deleted` = ?, `deleted_at` = ? where `id` = ?", m.table)
		if session != nil {
			return session.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
		}
		return conn.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
	}, artificialIntelligenceBlackBoxCaseCaseIdKey, artificialIntelligenceBlackBoxCaseDirIdCaseIdKey, artificialIntelligenceBlackBoxCaseIdKey)
	return err
}

func (m *defaultBlackBoxCaseModel) FindOne(ctx context.Context, id int64) (*BlackBoxCase, error) {
	artificialIntelligenceBlackBoxCaseIdKey := fmt.Sprintf("%s%v", cacheArtificialIntelligenceBlackBoxCaseIdPrefix, id)
	var resp BlackBoxCase
	err := m.QueryRowCtx(ctx, &resp, artificialIntelligenceBlackBoxCaseIdKey, func(ctx context.Context, conn sqlx.SqlConn, v any) error {
		query := fmt.Sprintf("select %s from %s where `id` = ? and `deleted` = ? limit 1", blackBoxCaseRows, m.table)
		return conn.QueryRowCtx(ctx, v, query, id, constants.NotDeleted)
	})
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultBlackBoxCaseModel) FindOneByCaseId(ctx context.Context, caseId string) (*BlackBoxCase, error) {
	artificialIntelligenceBlackBoxCaseCaseIdKey := fmt.Sprintf("%s%v", cacheArtificialIntelligenceBlackBoxCaseCaseIdPrefix, caseId)
	var resp BlackBoxCase
	err := m.QueryRowIndexCtx(ctx, &resp, artificialIntelligenceBlackBoxCaseCaseIdKey, m.formatPrimary, func(ctx context.Context, conn sqlx.SqlConn, v any) (i any, e error) {
		query := fmt.Sprintf("select %s from %s where `case_id` = ? and `deleted` = ? limit 1", blackBoxCaseRows, m.table)
		if err := conn.QueryRowCtx(ctx, &resp, query, caseId, constants.NotDeleted); err != nil {
			return nil, err
		}
		return resp.Id, nil
	}, m.queryPrimary)
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultBlackBoxCaseModel) FindOneByDirIdCaseId(ctx context.Context, dirId string, caseId string) (*BlackBoxCase, error) {
	artificialIntelligenceBlackBoxCaseDirIdCaseIdKey := fmt.Sprintf("%s%v:%v", cacheArtificialIntelligenceBlackBoxCaseDirIdCaseIdPrefix, dirId, caseId)
	var resp BlackBoxCase
	err := m.QueryRowIndexCtx(ctx, &resp, artificialIntelligenceBlackBoxCaseDirIdCaseIdKey, m.formatPrimary, func(ctx context.Context, conn sqlx.SqlConn, v any) (i any, e error) {
		query := fmt.Sprintf("select %s from %s where `dir_id` = ? and `case_id` = ? and `deleted` = ? limit 1", blackBoxCaseRows, m.table)
		if err := conn.QueryRowCtx(ctx, &resp, query, dirId, caseId, constants.NotDeleted); err != nil {
			return nil, err
		}
		return resp.Id, nil
	}, m.queryPrimary)
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultBlackBoxCaseModel) Insert(ctx context.Context, session sqlx.Session, data *BlackBoxCase) (sql.Result, error) {
	artificialIntelligenceBlackBoxCaseCaseIdKey := fmt.Sprintf("%s%v", cacheArtificialIntelligenceBlackBoxCaseCaseIdPrefix, data.CaseId)
	artificialIntelligenceBlackBoxCaseDirIdCaseIdKey := fmt.Sprintf("%s%v:%v", cacheArtificialIntelligenceBlackBoxCaseDirIdCaseIdPrefix, data.DirId, data.CaseId)
	artificialIntelligenceBlackBoxCaseIdKey := fmt.Sprintf("%s%v", cacheArtificialIntelligenceBlackBoxCaseIdPrefix, data.Id)

	return m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", m.table, blackBoxCaseRowsExpectAutoSet)
		if session != nil {
			return session.ExecCtx(ctx, query, data.DirId, data.CaseId, data.CaseName, data.CaseContinueToWrite, data.CaseModelCharacter, data.KnowledgeId, data.CreatedBy, data.UpdatedBy, data.DeletedBy, data.Deleted, data.CaseRemarks, data.KnowledgeParagraphTitleId, data.ReferenceDoc, data.EnableReference, data.KnowledgeFixSugg, data.QueryTimes, data.KnowledgeParagraphTitleText, data.KnowledgeSize, data.KnowledgeParagraphTitles)
		}
		return conn.ExecCtx(ctx, query, data.DirId, data.CaseId, data.CaseName, data.CaseContinueToWrite, data.CaseModelCharacter, data.KnowledgeId, data.CreatedBy, data.UpdatedBy, data.DeletedBy, data.Deleted, data.CaseRemarks, data.KnowledgeParagraphTitleId, data.ReferenceDoc, data.EnableReference, data.KnowledgeFixSugg, data.QueryTimes, data.KnowledgeParagraphTitleText, data.KnowledgeSize, data.KnowledgeParagraphTitles)
	}, artificialIntelligenceBlackBoxCaseCaseIdKey, artificialIntelligenceBlackBoxCaseDirIdCaseIdKey, artificialIntelligenceBlackBoxCaseIdKey)
}

func (m *defaultBlackBoxCaseModel) Update(ctx context.Context, session sqlx.Session, newData *BlackBoxCase) (sql.Result, error) {
	data, err := m.FindOne(ctx, newData.Id)
	if err != nil {
		return nil, err
	}

	artificialIntelligenceBlackBoxCaseCaseIdKey := fmt.Sprintf("%s%v", cacheArtificialIntelligenceBlackBoxCaseCaseIdPrefix, data.CaseId)
	artificialIntelligenceBlackBoxCaseDirIdCaseIdKey := fmt.Sprintf("%s%v:%v", cacheArtificialIntelligenceBlackBoxCaseDirIdCaseIdPrefix, data.DirId, data.CaseId)
	artificialIntelligenceBlackBoxCaseIdKey := fmt.Sprintf("%s%v", cacheArtificialIntelligenceBlackBoxCaseIdPrefix, data.Id)

	return m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, blackBoxCaseRowsWithPlaceHolder)
		if session != nil {
			return session.ExecCtx(ctx, query, newData.DirId, newData.CaseId, newData.CaseName, newData.CaseContinueToWrite, newData.CaseModelCharacter, newData.KnowledgeId, newData.CreatedBy, newData.UpdatedBy, newData.DeletedBy, newData.Deleted, newData.CaseRemarks, newData.KnowledgeParagraphTitleId, newData.ReferenceDoc, newData.EnableReference, newData.KnowledgeFixSugg, newData.QueryTimes, newData.KnowledgeParagraphTitleText, newData.KnowledgeSize, newData.KnowledgeParagraphTitles, newData.Id)
		}
		return conn.ExecCtx(ctx, query, newData.DirId, newData.CaseId, newData.CaseName, newData.CaseContinueToWrite, newData.CaseModelCharacter, newData.KnowledgeId, newData.CreatedBy, newData.UpdatedBy, newData.DeletedBy, newData.Deleted, newData.CaseRemarks, newData.KnowledgeParagraphTitleId, newData.ReferenceDoc, newData.EnableReference, newData.KnowledgeFixSugg, newData.QueryTimes, newData.KnowledgeParagraphTitleText, newData.KnowledgeSize, newData.KnowledgeParagraphTitles, newData.Id)
	}, artificialIntelligenceBlackBoxCaseCaseIdKey, artificialIntelligenceBlackBoxCaseDirIdCaseIdKey, artificialIntelligenceBlackBoxCaseIdKey)
}

func (m *defaultBlackBoxCaseModel) formatPrimary(primary any) string {
	return fmt.Sprintf("%s%v", cacheArtificialIntelligenceBlackBoxCaseIdPrefix, primary)
}

func (m *defaultBlackBoxCaseModel) queryPrimary(ctx context.Context, conn sqlx.SqlConn, v, primary any) error {
	query := fmt.Sprintf("select %s from %s where `id` = ? limit 1", blackBoxCaseRows, m.table)
	return conn.QueryRowCtx(ctx, v, query, primary)
}

func (m *defaultBlackBoxCaseModel) tableName() string {
	return m.table
}
