package dto

type BlackBoxCaseData struct {
	Id           int32  `json:"id"`            // 自增ID
	CaseId       string `json:"case_id"`       // 用例ID
	RevisionId   string `json:"revision_id"`   // 版本ID
	CaseDataId   string `json:"case_data_id"`  // 数据ID
	OrderId      string `json:"order_id"`      // 序号
	Requirement  string `json:"requirement"`   // 需求名称
	PreCondition string `json:"pre_condition"` // 前置条件
	CaseStep     string `json:"case_step"`     // 用例步骤
	ExpectResult string `json:"expect_result"` // 预期结果
	Terminal     string `json:"terminal"`      // 终端
	CaseLevel    string `json:"case_level"`    // 用例等级
	Tag          string `json:"tag"`           // 标识
	IsKeep       bool   `json:"is_keep"`       // 是否保留
	CaseName     string `json:"case_name"`     // 用例名称
	Deleted      string `json:"deleted"`       // 是否删除
	CreatedBy    string `json:"created_by"`    // 创建者的用户ID
	UpdatedBy    string `json:"updated_by"`    // 最近一次更新者的用户ID
	DeletedBy    string `json:"deleted_by"`    // 删除者的用户ID
	CreatedAt    int64  `json:"created_at"`    // 创建时间
	UpdatedAt    int64  `json:"updated_at"`    // 更新时间
	DeletedAt    int64  `json:"deleted_at"`    // 删除时间
}
