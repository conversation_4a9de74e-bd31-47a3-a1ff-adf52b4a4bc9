// Code generated by goctl. DO NOT EDIT.
// versions:
//  goctl version: 1.7.4

package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlc"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
)

var (
	blackBoxCaseDirTableName           = "`black_box_case_dir`"
	blackBoxCaseDirFieldNames          = builder.RawFieldNames(&BlackBoxCaseDir{})
	blackBoxCaseDirRows                = strings.Join(blackBoxCaseDirFieldNames, ",")
	blackBoxCaseDirRowsExpectAutoSet   = strings.Join(stringx.Remove(blackBoxCaseDirFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), ",")
	blackBoxCaseDirRowsWithPlaceHolder = strings.Join(stringx.Remove(blackBoxCaseDirFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), "=?,") + "=?"

	cacheArtificialIntelligenceBlackBoxCaseDirIdPrefix             = "cache:artificialIntelligence:blackBoxCaseDir:id:"
	cacheArtificialIntelligenceBlackBoxCaseDirProjectIdDirIdPrefix = "cache:artificialIntelligence:blackBoxCaseDir:projectId:dirId:"
)

type (
	blackBoxCaseDirModel interface {
		Insert(ctx context.Context, session sqlx.Session, data *BlackBoxCaseDir) (sql.Result, error)
		FindOne(ctx context.Context, id int64) (*BlackBoxCaseDir, error)
		FindOneByProjectIdDirId(ctx context.Context, projectId string, dirId string) (*BlackBoxCaseDir, error)
		Update(ctx context.Context, session sqlx.Session, data *BlackBoxCaseDir) (sql.Result, error)
		Delete(ctx context.Context, session sqlx.Session, id int64) error
		LogicDelete(ctx context.Context, session sqlx.Session, id int64) error
	}

	defaultBlackBoxCaseDirModel struct {
		sqlc.CachedConn
		table string
	}

	BlackBoxCaseDir struct {
		Id             int64          `db:"id"`              // 自增ID
		ProjectId      string         `db:"project_id"`      // 项目ID
		DirId          string         `db:"dir_id"`          // 目录ID
		DirName        string         `db:"dir_name"`        // 目录名称
		DirDescription sql.NullString `db:"dir_description"` // 目录描述
		Deleted        int64          `db:"deleted"`         // 逻辑删除标识（未删除、已删除）
		CreatedBy      string         `db:"created_by"`      // 创建者的用户ID
		UpdatedBy      string         `db:"updated_by"`      // 最近一次更新者的用户ID
		DeletedBy      sql.NullString `db:"deleted_by"`      // 删除者的用户ID
		CreatedAt      time.Time      `db:"created_at"`      // 创建时间
		UpdatedAt      time.Time      `db:"updated_at"`      // 更新时间
		DeletedAt      sql.NullTime   `db:"deleted_at"`      // 删除时间
	}
)

func newBlackBoxCaseDirModel(conn sqlx.SqlConn, c cache.CacheConf, opts ...cache.Option) *defaultBlackBoxCaseDirModel {
	return &defaultBlackBoxCaseDirModel{
		CachedConn: sqlc.NewConn(conn, c, opts...),
		table:      "`black_box_case_dir`",
	}
}

func (m *defaultBlackBoxCaseDirModel) Delete(ctx context.Context, session sqlx.Session, id int64) error {
	data, err := m.FindOne(ctx, id)
	if err != nil {
		return err
	}

	artificialIntelligenceBlackBoxCaseDirIdKey := fmt.Sprintf("%s%v", cacheArtificialIntelligenceBlackBoxCaseDirIdPrefix, id)
	artificialIntelligenceBlackBoxCaseDirProjectIdDirIdKey := fmt.Sprintf("%s%v:%v", cacheArtificialIntelligenceBlackBoxCaseDirProjectIdDirIdPrefix, data.ProjectId, data.DirId)
	_, err = m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("delete from %s where `id` = ?", m.table)
		if session != nil {
			return session.ExecCtx(ctx, query, id)
		}
		return conn.ExecCtx(ctx, query, id)
	}, artificialIntelligenceBlackBoxCaseDirIdKey, artificialIntelligenceBlackBoxCaseDirProjectIdDirIdKey)
	return err
}

func (m *defaultBlackBoxCaseDirModel) LogicDelete(ctx context.Context, session sqlx.Session, id int64) error {
	data, err := m.FindOne(ctx, id)
	if err != nil {
		return err
	}

	artificialIntelligenceBlackBoxCaseDirIdKey := fmt.Sprintf("%s%v", cacheArtificialIntelligenceBlackBoxCaseDirIdPrefix, id)
	artificialIntelligenceBlackBoxCaseDirProjectIdDirIdKey := fmt.Sprintf("%s%v:%v", cacheArtificialIntelligenceBlackBoxCaseDirProjectIdDirIdPrefix, data.ProjectId, data.DirId)
	_, err = m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set `deleted` = ?, `deleted_at` = ? where `id` = ?", m.table)
		if session != nil {
			return session.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
		}
		return conn.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
	}, artificialIntelligenceBlackBoxCaseDirIdKey, artificialIntelligenceBlackBoxCaseDirProjectIdDirIdKey)
	return err
}

func (m *defaultBlackBoxCaseDirModel) FindOne(ctx context.Context, id int64) (*BlackBoxCaseDir, error) {
	artificialIntelligenceBlackBoxCaseDirIdKey := fmt.Sprintf("%s%v", cacheArtificialIntelligenceBlackBoxCaseDirIdPrefix, id)
	var resp BlackBoxCaseDir
	err := m.QueryRowCtx(ctx, &resp, artificialIntelligenceBlackBoxCaseDirIdKey, func(ctx context.Context, conn sqlx.SqlConn, v any) error {
		query := fmt.Sprintf("select %s from %s where `id` = ? and `deleted` = ? limit 1", blackBoxCaseDirRows, m.table)
		return conn.QueryRowCtx(ctx, v, query, id, constants.NotDeleted)
	})
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultBlackBoxCaseDirModel) FindOneByProjectIdDirId(ctx context.Context, projectId string, dirId string) (*BlackBoxCaseDir, error) {
	artificialIntelligenceBlackBoxCaseDirProjectIdDirIdKey := fmt.Sprintf("%s%v:%v", cacheArtificialIntelligenceBlackBoxCaseDirProjectIdDirIdPrefix, projectId, dirId)
	var resp BlackBoxCaseDir
	err := m.QueryRowIndexCtx(ctx, &resp, artificialIntelligenceBlackBoxCaseDirProjectIdDirIdKey, m.formatPrimary, func(ctx context.Context, conn sqlx.SqlConn, v any) (i any, e error) {
		query := fmt.Sprintf("select %s from %s where `project_id` = ? and `dir_id` = ? and `deleted` = ? limit 1", blackBoxCaseDirRows, m.table)
		if err := conn.QueryRowCtx(ctx, &resp, query, projectId, dirId, constants.NotDeleted); err != nil {
			return nil, err
		}
		return resp.Id, nil
	}, m.queryPrimary)
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultBlackBoxCaseDirModel) Insert(ctx context.Context, session sqlx.Session, data *BlackBoxCaseDir) (sql.Result, error) {
	artificialIntelligenceBlackBoxCaseDirIdKey := fmt.Sprintf("%s%v", cacheArtificialIntelligenceBlackBoxCaseDirIdPrefix, data.Id)
	artificialIntelligenceBlackBoxCaseDirProjectIdDirIdKey := fmt.Sprintf("%s%v:%v", cacheArtificialIntelligenceBlackBoxCaseDirProjectIdDirIdPrefix, data.ProjectId, data.DirId)

	return m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?, ?, ?, ?, ?)", m.table, blackBoxCaseDirRowsExpectAutoSet)
		if session != nil {
			return session.ExecCtx(ctx, query, data.ProjectId, data.DirId, data.DirName, data.DirDescription, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy)
		}
		return conn.ExecCtx(ctx, query, data.ProjectId, data.DirId, data.DirName, data.DirDescription, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy)
	}, artificialIntelligenceBlackBoxCaseDirIdKey, artificialIntelligenceBlackBoxCaseDirProjectIdDirIdKey)
}

func (m *defaultBlackBoxCaseDirModel) Update(ctx context.Context, session sqlx.Session, newData *BlackBoxCaseDir) (sql.Result, error) {
	data, err := m.FindOne(ctx, newData.Id)
	if err != nil {
		return nil, err
	}

	artificialIntelligenceBlackBoxCaseDirIdKey := fmt.Sprintf("%s%v", cacheArtificialIntelligenceBlackBoxCaseDirIdPrefix, data.Id)
	artificialIntelligenceBlackBoxCaseDirProjectIdDirIdKey := fmt.Sprintf("%s%v:%v", cacheArtificialIntelligenceBlackBoxCaseDirProjectIdDirIdPrefix, data.ProjectId, data.DirId)

	return m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, blackBoxCaseDirRowsWithPlaceHolder)
		if session != nil {
			return session.ExecCtx(ctx, query, newData.ProjectId, newData.DirId, newData.DirName, newData.DirDescription, newData.Deleted, newData.CreatedBy, newData.UpdatedBy, newData.DeletedBy, newData.Id)
		}
		return conn.ExecCtx(ctx, query, newData.ProjectId, newData.DirId, newData.DirName, newData.DirDescription, newData.Deleted, newData.CreatedBy, newData.UpdatedBy, newData.DeletedBy, newData.Id)
	}, artificialIntelligenceBlackBoxCaseDirIdKey, artificialIntelligenceBlackBoxCaseDirProjectIdDirIdKey)
}

func (m *defaultBlackBoxCaseDirModel) formatPrimary(primary any) string {
	return fmt.Sprintf("%s%v", cacheArtificialIntelligenceBlackBoxCaseDirIdPrefix, primary)
}

func (m *defaultBlackBoxCaseDirModel) queryPrimary(ctx context.Context, conn sqlx.SqlConn, v, primary any) error {
	query := fmt.Sprintf("select %s from %s where `id` = ? limit 1", blackBoxCaseDirRows, m.table)
	return conn.QueryRowCtx(ctx, v, query, primary)
}

func (m *defaultBlackBoxCaseDirModel) tableName() string {
	return m.table
}
