package model

import (
	"context"
	"time"

	"github.com/Masterminds/squirrel"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/types"
)

var (
	_ BlackBoxGenerationRecordModel = (*customBlackBoxGenerationRecordModel)(nil)

	blackBoxGenerationRecordInsertFields = stringx.Remove(blackBoxGenerationRecordFieldNames, "`id`", "`create_time`", "`update_time`", "`deleted_by`", "`created_at`", "`updated_at`", "`deleted_at`")
)

type (
	// BlackBoxGenerationRecordModel is an interface to be customized, add more methods here,
	// and implement the added methods in customBlackBoxGenerationRecordModel.
	BlackBoxGenerationRecordModel interface {
		blackBoxGenerationRecordModel
		types.DBModel

		Trans(ctx context.Context, fn func(context context.Context, session sqlx.Session) error) error
		InsertBuilder(data *BlackBoxGenerationRecord) squirrel.InsertBuilder
		UpdateBuilder(data *BlackBoxGenerationRecord) squirrel.UpdateBuilder
		SelectBuilder() squirrel.SelectBuilder
		SelectCountBuilder() squirrel.SelectBuilder
		FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error)
		FindNoCacheByQuery(ctx context.Context, selectBuilder squirrel.SelectBuilder) ([]*BlackBoxGenerationRecord, error)
		Save(ctx context.Context, data *BlackBoxGenerationRecord) (err error)
	}

	customBlackBoxGenerationRecordModel struct {
		*defaultBlackBoxGenerationRecordModel

		conn sqlx.SqlConn
	}
)

// NewBlackBoxGenerationRecordModel returns a model for the database table.
func NewBlackBoxGenerationRecordModel(conn sqlx.SqlConn, c cache.CacheConf, opts ...cache.Option) BlackBoxGenerationRecordModel {
	return &customBlackBoxGenerationRecordModel{
		defaultBlackBoxGenerationRecordModel: newBlackBoxGenerationRecordModel(conn, c, opts...),
		conn:                                 conn,
	}
}

func (m *customBlackBoxGenerationRecordModel) Table() string {
	return m.table
}

func (m *customBlackBoxGenerationRecordModel) Fields() []string {
	return blackBoxGenerationRecordFieldNames
}

func (m *customBlackBoxGenerationRecordModel) Trans(ctx context.Context, fn func(context context.Context, session sqlx.Session) error) error {
	return m.TransactCtx(ctx, fn)
}

func (m *customBlackBoxGenerationRecordModel) InsertBuilder(data *BlackBoxGenerationRecord) squirrel.InsertBuilder {
	// TODO: fill the insert values
	return squirrel.Insert(m.table).Columns(blackBoxGenerationRecordInsertFields...).Values()
}

func (m *customBlackBoxGenerationRecordModel) UpdateBuilder(data *BlackBoxGenerationRecord) squirrel.UpdateBuilder {
	eq := squirrel.Eq{
		// TODO: fill the update values
	}
	return squirrel.Update(m.table).SetMap(eq).Where("`id` = ?", data.Id)
}

func (m *customBlackBoxGenerationRecordModel) SelectBuilder() squirrel.SelectBuilder {
	return squirrel.Select(blackBoxGenerationRecordFieldNames...).Where("`deleted` = ?", constants.NotDeleted).From(m.table)
}

func (m *customBlackBoxGenerationRecordModel) SelectCountBuilder() squirrel.SelectBuilder {
	return squirrel.Select("count(*)").Where("`deleted` = ?", constants.NotDeleted).From(m.table)
}

func (m *customBlackBoxGenerationRecordModel) FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error) {
	query, values, err := countBuilder.ToSql()
	if err != nil {
		return 0, err
	}

	var resp int64
	err = m.QueryRowNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return 0, err
	}
}

func (m *customBlackBoxGenerationRecordModel) FindNoCacheByQuery(ctx context.Context, selectBuilder squirrel.SelectBuilder) ([]*BlackBoxGenerationRecord, error) {
	query, values, err := selectBuilder.ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*BlackBoxGenerationRecord
	err = m.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

func (m *customBlackBoxGenerationRecordModel) Save(ctx context.Context, data *BlackBoxGenerationRecord) (err error) {
	stmt := squirrel.Insert(m.table).Columns("created_date", "function_point_count",
		"test_scene_count", "supplement_document_count",
		"base_map_case_count", "updated_add_count",
		"case_name_update_count", "precondition_update_count",
		"case_step_update_count", "expect_result_update_count", "case_level_update_count").
		Values(time.Now().Format("2006-01-02"), data.FunctionPointCount, data.TestSceneCount,
			data.SupplementDocumentCount, data.BaseMapCaseCount, data.UpdatedAddCount, data.CaseNameUpdateCount,
			data.PreconditionUpdateCount, data.CaseStepUpdateCount, data.ExpectResultUpdateCount, data.CaseLevelUpdateCount)

	sql, i, err := stmt.ToSql()
	if err != nil {
		return err
	}

	sql += " ON DUPLICATE KEY UPDATE function_point_count = function_point_count + VALUES(function_point_count), " +
		"test_scene_count = test_scene_count + VALUES(test_scene_count), " +
		"supplement_document_count = supplement_document_count + VALUES(supplement_document_count)," +
		"base_map_case_count = base_map_case_count + VALUES(base_map_case_count)," +
		"updated_add_count = updated_add_count + VALUES(updated_add_count), " +
		"case_name_update_count = case_name_update_count + VALUES(case_name_update_count)," +
		"precondition_update_count = precondition_update_count + VALUES(precondition_update_count)," +
		"case_step_update_count = case_step_update_count + VALUES(case_step_update_count)," +
		"expect_result_update_count = expect_result_update_count + VALUES(expect_result_update_count)," +
		"case_level_update_count = case_level_update_count + VALUES(case_level_update_count)"

	_, err = m.conn.ExecCtx(ctx, sql, i...)

	return nil
}
