// Code generated by goctl. DO NOT EDIT.
// versions:
//  goctl version: 1.7.4

package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlc"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
)

var (
	blackBoxCaseAssistantTableName           = "`black_box_case_assistant`"
	blackBoxCaseAssistantFieldNames          = builder.RawFieldNames(&BlackBoxCaseAssistant{})
	blackBoxCaseAssistantRows                = strings.Join(blackBoxCaseAssistantFieldNames, ",")
	blackBoxCaseAssistantRowsExpectAutoSet   = strings.Join(stringx.Remove(blackBoxCaseAssistantFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), ",")
	blackBoxCaseAssistantRowsWithPlaceHolder = strings.Join(stringx.Remove(blackBoxCaseAssistantFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), "=?,") + "=?"

	cacheArtificialIntelligenceBlackBoxCaseAssistantIdPrefix                   = "cache:artificialIntelligence:blackBoxCaseAssistant:id:"
	cacheArtificialIntelligenceBlackBoxCaseAssistantProjectIdAssistantIdPrefix = "cache:artificialIntelligence:blackBoxCaseAssistant:projectId:assistantId:"
)

type (
	blackBoxCaseAssistantModel interface {
		Insert(ctx context.Context, session sqlx.Session, data *BlackBoxCaseAssistant) (sql.Result, error)
		FindOne(ctx context.Context, id int64) (*BlackBoxCaseAssistant, error)
		FindOneByProjectIdAssistantId(ctx context.Context, projectId string, assistantId string) (*BlackBoxCaseAssistant, error)
		Update(ctx context.Context, session sqlx.Session, data *BlackBoxCaseAssistant) (sql.Result, error)
		Delete(ctx context.Context, session sqlx.Session, id int64) error
		LogicDelete(ctx context.Context, session sqlx.Session, id int64) error
	}

	defaultBlackBoxCaseAssistantModel struct {
		sqlc.CachedConn
		table string
	}

	BlackBoxCaseAssistant struct {
		Id                   int64          `db:"id"`                    // 自增ID
		ProjectId            string         `db:"project_id"`            // 项目ID
		AssistantId          string         `db:"assistant_id"`          // 助手ID
		AssistantName        string         `db:"assistant_name"`        // 助手名称
		AssistantDescription sql.NullString `db:"assistant_description"` // 助手描述
		Account              sql.NullString `db:"account"`               // 用户id【仅在个人空间存在此数据】
		WorkSpaceType        int64          `db:"work_space_type"`       // 工作空间类型：个人空间(private)：0，公共空间()：1
		WorkPriorityType     int64          `db:"work_priority_type"`    // 工作优先级类型：准确优先：0，速度优先：1
		SessionRound         int64          `db:"session_round"`         // 对话轮数
		DocumentIds          sql.NullString `db:"document_ids"`          // 文档集合
		Deleted              int64          `db:"deleted"`               // 逻辑删除标识（未删除、已删除）
		CreatedBy            string         `db:"created_by"`            // 创建者的用户ID
		UpdatedBy            string         `db:"updated_by"`            // 最近一次更新者的用户ID
		DeletedBy            sql.NullString `db:"deleted_by"`            // 删除者的用户ID
		CreatedAt            time.Time      `db:"created_at"`            // 创建时间
		UpdatedAt            time.Time      `db:"updated_at"`            // 更新时间
		DeletedAt            sql.NullTime   `db:"deleted_at"`            // 删除时间
	}
)

func newBlackBoxCaseAssistantModel(conn sqlx.SqlConn, c cache.CacheConf, opts ...cache.Option) *defaultBlackBoxCaseAssistantModel {
	return &defaultBlackBoxCaseAssistantModel{
		CachedConn: sqlc.NewConn(conn, c, opts...),
		table:      "`black_box_case_assistant`",
	}
}

func (m *defaultBlackBoxCaseAssistantModel) Delete(ctx context.Context, session sqlx.Session, id int64) error {
	data, err := m.FindOne(ctx, id)
	if err != nil {
		return err
	}

	artificialIntelligenceBlackBoxCaseAssistantIdKey := fmt.Sprintf("%s%v", cacheArtificialIntelligenceBlackBoxCaseAssistantIdPrefix, id)
	artificialIntelligenceBlackBoxCaseAssistantProjectIdAssistantIdKey := fmt.Sprintf("%s%v:%v", cacheArtificialIntelligenceBlackBoxCaseAssistantProjectIdAssistantIdPrefix, data.ProjectId, data.AssistantId)
	_, err = m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("delete from %s where `id` = ?", m.table)
		if session != nil {
			return session.ExecCtx(ctx, query, id)
		}
		return conn.ExecCtx(ctx, query, id)
	}, artificialIntelligenceBlackBoxCaseAssistantIdKey, artificialIntelligenceBlackBoxCaseAssistantProjectIdAssistantIdKey)
	return err
}

func (m *defaultBlackBoxCaseAssistantModel) LogicDelete(ctx context.Context, session sqlx.Session, id int64) error {
	data, err := m.FindOne(ctx, id)
	if err != nil {
		return err
	}

	artificialIntelligenceBlackBoxCaseAssistantIdKey := fmt.Sprintf("%s%v", cacheArtificialIntelligenceBlackBoxCaseAssistantIdPrefix, id)
	artificialIntelligenceBlackBoxCaseAssistantProjectIdAssistantIdKey := fmt.Sprintf("%s%v:%v", cacheArtificialIntelligenceBlackBoxCaseAssistantProjectIdAssistantIdPrefix, data.ProjectId, data.AssistantId)
	_, err = m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set `deleted` = ?, `deleted_at` = ? where `id` = ?", m.table)
		if session != nil {
			return session.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
		}
		return conn.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
	}, artificialIntelligenceBlackBoxCaseAssistantIdKey, artificialIntelligenceBlackBoxCaseAssistantProjectIdAssistantIdKey)
	return err
}

func (m *defaultBlackBoxCaseAssistantModel) FindOne(ctx context.Context, id int64) (*BlackBoxCaseAssistant, error) {
	artificialIntelligenceBlackBoxCaseAssistantIdKey := fmt.Sprintf("%s%v", cacheArtificialIntelligenceBlackBoxCaseAssistantIdPrefix, id)
	var resp BlackBoxCaseAssistant
	err := m.QueryRowCtx(ctx, &resp, artificialIntelligenceBlackBoxCaseAssistantIdKey, func(ctx context.Context, conn sqlx.SqlConn, v any) error {
		query := fmt.Sprintf("select %s from %s where `id` = ? and `deleted` = ? limit 1", blackBoxCaseAssistantRows, m.table)
		return conn.QueryRowCtx(ctx, v, query, id, constants.NotDeleted)
	})
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultBlackBoxCaseAssistantModel) FindOneByProjectIdAssistantId(ctx context.Context, projectId string, assistantId string) (*BlackBoxCaseAssistant, error) {
	artificialIntelligenceBlackBoxCaseAssistantProjectIdAssistantIdKey := fmt.Sprintf("%s%v:%v", cacheArtificialIntelligenceBlackBoxCaseAssistantProjectIdAssistantIdPrefix, projectId, assistantId)
	var resp BlackBoxCaseAssistant
	err := m.QueryRowIndexCtx(ctx, &resp, artificialIntelligenceBlackBoxCaseAssistantProjectIdAssistantIdKey, m.formatPrimary, func(ctx context.Context, conn sqlx.SqlConn, v any) (i any, e error) {
		query := fmt.Sprintf("select %s from %s where `project_id` = ? and `assistant_id` = ? and `deleted` = ? limit 1", blackBoxCaseAssistantRows, m.table)
		if err := conn.QueryRowCtx(ctx, &resp, query, projectId, assistantId, constants.NotDeleted); err != nil {
			return nil, err
		}
		return resp.Id, nil
	}, m.queryPrimary)
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultBlackBoxCaseAssistantModel) Insert(ctx context.Context, session sqlx.Session, data *BlackBoxCaseAssistant) (sql.Result, error) {
	artificialIntelligenceBlackBoxCaseAssistantIdKey := fmt.Sprintf("%s%v", cacheArtificialIntelligenceBlackBoxCaseAssistantIdPrefix, data.Id)
	artificialIntelligenceBlackBoxCaseAssistantProjectIdAssistantIdKey := fmt.Sprintf("%s%v:%v", cacheArtificialIntelligenceBlackBoxCaseAssistantProjectIdAssistantIdPrefix, data.ProjectId, data.AssistantId)

	return m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", m.table, blackBoxCaseAssistantRowsExpectAutoSet)
		if session != nil {
			return session.ExecCtx(ctx, query, data.ProjectId, data.AssistantId, data.AssistantName, data.AssistantDescription, data.Account, data.WorkSpaceType, data.WorkPriorityType, data.SessionRound, data.DocumentIds, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy)
		}
		return conn.ExecCtx(ctx, query, data.ProjectId, data.AssistantId, data.AssistantName, data.AssistantDescription, data.Account, data.WorkSpaceType, data.WorkPriorityType, data.SessionRound, data.DocumentIds, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy)
	}, artificialIntelligenceBlackBoxCaseAssistantIdKey, artificialIntelligenceBlackBoxCaseAssistantProjectIdAssistantIdKey)
}

func (m *defaultBlackBoxCaseAssistantModel) Update(ctx context.Context, session sqlx.Session, newData *BlackBoxCaseAssistant) (sql.Result, error) {
	data, err := m.FindOne(ctx, newData.Id)
	if err != nil {
		return nil, err
	}

	artificialIntelligenceBlackBoxCaseAssistantIdKey := fmt.Sprintf("%s%v", cacheArtificialIntelligenceBlackBoxCaseAssistantIdPrefix, data.Id)
	artificialIntelligenceBlackBoxCaseAssistantProjectIdAssistantIdKey := fmt.Sprintf("%s%v:%v", cacheArtificialIntelligenceBlackBoxCaseAssistantProjectIdAssistantIdPrefix, data.ProjectId, data.AssistantId)

	return m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, blackBoxCaseAssistantRowsWithPlaceHolder)
		if session != nil {
			return session.ExecCtx(ctx, query, newData.ProjectId, newData.AssistantId, newData.AssistantName, newData.AssistantDescription, newData.Account, newData.WorkSpaceType, newData.WorkPriorityType, newData.SessionRound, newData.DocumentIds, newData.Deleted, newData.CreatedBy, newData.UpdatedBy, newData.DeletedBy, newData.Id)
		}
		return conn.ExecCtx(ctx, query, newData.ProjectId, newData.AssistantId, newData.AssistantName, newData.AssistantDescription, newData.Account, newData.WorkSpaceType, newData.WorkPriorityType, newData.SessionRound, newData.DocumentIds, newData.Deleted, newData.CreatedBy, newData.UpdatedBy, newData.DeletedBy, newData.Id)
	}, artificialIntelligenceBlackBoxCaseAssistantIdKey, artificialIntelligenceBlackBoxCaseAssistantProjectIdAssistantIdKey)
}

func (m *defaultBlackBoxCaseAssistantModel) formatPrimary(primary any) string {
	return fmt.Sprintf("%s%v", cacheArtificialIntelligenceBlackBoxCaseAssistantIdPrefix, primary)
}

func (m *defaultBlackBoxCaseAssistantModel) queryPrimary(ctx context.Context, conn sqlx.SqlConn, v, primary any) error {
	query := fmt.Sprintf("select %s from %s where `id` = ? limit 1", blackBoxCaseAssistantRows, m.table)
	return conn.QueryRowCtx(ctx, v, query, primary)
}

func (m *defaultBlackBoxCaseAssistantModel) tableName() string {
	return m.table
}
