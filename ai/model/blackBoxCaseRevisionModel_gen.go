// Code generated by goctl. DO NOT EDIT.
// versions:
//  goctl version: 1.7.4

package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlc"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
)

var (
	blackBoxCaseRevisionTableName           = "`black_box_case_revision`"
	blackBoxCaseRevisionFieldNames          = builder.RawFieldNames(&BlackBoxCaseRevision{})
	blackBoxCaseRevisionRows                = strings.Join(blackBoxCaseRevisionFieldNames, ",")
	blackBoxCaseRevisionRowsExpectAutoSet   = strings.Join(stringx.Remove(blackBoxCaseRevisionFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), ",")
	blackBoxCaseRevisionRowsWithPlaceHolder = strings.Join(stringx.Remove(blackBoxCaseRevisionFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), "=?,") + "=?"

	cacheArtificialIntelligenceBlackBoxCaseRevisionIdPrefix               = "cache:artificialIntelligence:blackBoxCaseRevision:id:"
	cacheArtificialIntelligenceBlackBoxCaseRevisionCaseIdRevisionIdPrefix = "cache:artificialIntelligence:blackBoxCaseRevision:caseId:revisionId:"
	cacheArtificialIntelligenceBlackBoxCaseRevisionRevisionIdPrefix       = "cache:artificialIntelligence:blackBoxCaseRevision:revisionId:"
)

type (
	blackBoxCaseRevisionModel interface {
		Insert(ctx context.Context, session sqlx.Session, data *BlackBoxCaseRevision) (sql.Result, error)
		FindOne(ctx context.Context, id uint64) (*BlackBoxCaseRevision, error)
		FindOneByCaseIdRevisionId(ctx context.Context, caseId string, revisionId string) (*BlackBoxCaseRevision, error)
		FindOneByRevisionId(ctx context.Context, revisionId string) (*BlackBoxCaseRevision, error)
		Update(ctx context.Context, session sqlx.Session, data *BlackBoxCaseRevision) (sql.Result, error)
		Delete(ctx context.Context, session sqlx.Session, id uint64) error
		LogicDelete(ctx context.Context, session sqlx.Session, id uint64) error
	}

	defaultBlackBoxCaseRevisionModel struct {
		sqlc.CachedConn
		table string
	}

	BlackBoxCaseRevision struct {
		Id                          uint64         `db:"id"`                             // 自增ID
		CaseId                      string         `db:"case_id"`                        // 用例ID
		RevisionId                  string         `db:"revision_id"`                    // 版本ID
		RevisionName                string         `db:"revision_name"`                  // 版本名称
		Deleted                     int64          `db:"deleted"`                        // 逻辑删除标识（未删除、已删除）
		CreatedBy                   string         `db:"created_by"`                     // 创建者的用户ID
		UpdatedBy                   string         `db:"updated_by"`                     // 最近一次更新者的用户ID
		DeletedBy                   sql.NullString `db:"deleted_by"`                     // 删除者的用户ID
		CreatedAt                   time.Time      `db:"created_at"`                     // 创建时间
		UpdatedAt                   time.Time      `db:"updated_at"`                     // 更新时间
		DeletedAt                   sql.NullTime   `db:"deleted_at"`                     // 删除时间
		KnowledgeParagraphTitle     string         `db:"knowledge_paragraph_title"`      // 知识文档段落标题列表
		KnowledgeParagraphTitleText string         `db:"knowledge_paragraph_title_text"` // 知识文档段落标题
		KnowledgeId                 string         `db:"knowledge_id"`                   // 知识文档关联ID
		CaseRefId                   string         `db:"case_ref_id"`                    // 用例关联ID
		Coverage                    int64          `db:"coverage"`                       // 用例覆盖率[0~100]
		AddCount                    int64          `db:"add_count"`                      // AI生成新增的条数
		UpdateCount                 int64          `db:"update_count"`                   // AI生成修改的条数
	}
)

func newBlackBoxCaseRevisionModel(conn sqlx.SqlConn, c cache.CacheConf, opts ...cache.Option) *defaultBlackBoxCaseRevisionModel {
	return &defaultBlackBoxCaseRevisionModel{
		CachedConn: sqlc.NewConn(conn, c, opts...),
		table:      "`black_box_case_revision`",
	}
}

func (m *defaultBlackBoxCaseRevisionModel) Delete(ctx context.Context, session sqlx.Session, id uint64) error {
	data, err := m.FindOne(ctx, id)
	if err != nil {
		return err
	}

	artificialIntelligenceBlackBoxCaseRevisionCaseIdRevisionIdKey := fmt.Sprintf("%s%v:%v", cacheArtificialIntelligenceBlackBoxCaseRevisionCaseIdRevisionIdPrefix, data.CaseId, data.RevisionId)
	artificialIntelligenceBlackBoxCaseRevisionIdKey := fmt.Sprintf("%s%v", cacheArtificialIntelligenceBlackBoxCaseRevisionIdPrefix, id)
	artificialIntelligenceBlackBoxCaseRevisionRevisionIdKey := fmt.Sprintf("%s%v", cacheArtificialIntelligenceBlackBoxCaseRevisionRevisionIdPrefix, data.RevisionId)
	_, err = m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("delete from %s where `id` = ?", m.table)
		if session != nil {
			return session.ExecCtx(ctx, query, id)
		}
		return conn.ExecCtx(ctx, query, id)
	}, artificialIntelligenceBlackBoxCaseRevisionCaseIdRevisionIdKey, artificialIntelligenceBlackBoxCaseRevisionIdKey, artificialIntelligenceBlackBoxCaseRevisionRevisionIdKey)
	return err
}

func (m *defaultBlackBoxCaseRevisionModel) LogicDelete(ctx context.Context, session sqlx.Session, id uint64) error {
	data, err := m.FindOne(ctx, id)
	if err != nil {
		return err
	}

	artificialIntelligenceBlackBoxCaseRevisionCaseIdRevisionIdKey := fmt.Sprintf("%s%v:%v", cacheArtificialIntelligenceBlackBoxCaseRevisionCaseIdRevisionIdPrefix, data.CaseId, data.RevisionId)
	artificialIntelligenceBlackBoxCaseRevisionIdKey := fmt.Sprintf("%s%v", cacheArtificialIntelligenceBlackBoxCaseRevisionIdPrefix, id)
	artificialIntelligenceBlackBoxCaseRevisionRevisionIdKey := fmt.Sprintf("%s%v", cacheArtificialIntelligenceBlackBoxCaseRevisionRevisionIdPrefix, data.RevisionId)
	_, err = m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set `deleted` = ?, `deleted_at` = ? where `id` = ?", m.table)
		if session != nil {
			return session.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
		}
		return conn.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
	}, artificialIntelligenceBlackBoxCaseRevisionCaseIdRevisionIdKey, artificialIntelligenceBlackBoxCaseRevisionIdKey, artificialIntelligenceBlackBoxCaseRevisionRevisionIdKey)
	return err
}

func (m *defaultBlackBoxCaseRevisionModel) FindOne(ctx context.Context, id uint64) (*BlackBoxCaseRevision, error) {
	artificialIntelligenceBlackBoxCaseRevisionIdKey := fmt.Sprintf("%s%v", cacheArtificialIntelligenceBlackBoxCaseRevisionIdPrefix, id)
	var resp BlackBoxCaseRevision
	err := m.QueryRowCtx(ctx, &resp, artificialIntelligenceBlackBoxCaseRevisionIdKey, func(ctx context.Context, conn sqlx.SqlConn, v any) error {
		query := fmt.Sprintf("select %s from %s where `id` = ? and `deleted` = ? limit 1", blackBoxCaseRevisionRows, m.table)
		return conn.QueryRowCtx(ctx, v, query, id, constants.NotDeleted)
	})
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultBlackBoxCaseRevisionModel) FindOneByCaseIdRevisionId(ctx context.Context, caseId string, revisionId string) (*BlackBoxCaseRevision, error) {
	artificialIntelligenceBlackBoxCaseRevisionCaseIdRevisionIdKey := fmt.Sprintf("%s%v:%v", cacheArtificialIntelligenceBlackBoxCaseRevisionCaseIdRevisionIdPrefix, caseId, revisionId)
	var resp BlackBoxCaseRevision
	err := m.QueryRowIndexCtx(ctx, &resp, artificialIntelligenceBlackBoxCaseRevisionCaseIdRevisionIdKey, m.formatPrimary, func(ctx context.Context, conn sqlx.SqlConn, v any) (i any, e error) {
		query := fmt.Sprintf("select %s from %s where `case_id` = ? and `revision_id` = ? and `deleted` = ? limit 1", blackBoxCaseRevisionRows, m.table)
		if err := conn.QueryRowCtx(ctx, &resp, query, caseId, revisionId, constants.NotDeleted); err != nil {
			return nil, err
		}
		return resp.Id, nil
	}, m.queryPrimary)
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultBlackBoxCaseRevisionModel) FindOneByRevisionId(ctx context.Context, revisionId string) (*BlackBoxCaseRevision, error) {
	artificialIntelligenceBlackBoxCaseRevisionRevisionIdKey := fmt.Sprintf("%s%v", cacheArtificialIntelligenceBlackBoxCaseRevisionRevisionIdPrefix, revisionId)
	var resp BlackBoxCaseRevision
	err := m.QueryRowIndexCtx(ctx, &resp, artificialIntelligenceBlackBoxCaseRevisionRevisionIdKey, m.formatPrimary, func(ctx context.Context, conn sqlx.SqlConn, v any) (i any, e error) {
		query := fmt.Sprintf("select %s from %s where `revision_id` = ? and `deleted` = ? limit 1", blackBoxCaseRevisionRows, m.table)
		if err := conn.QueryRowCtx(ctx, &resp, query, revisionId, constants.NotDeleted); err != nil {
			return nil, err
		}
		return resp.Id, nil
	}, m.queryPrimary)
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultBlackBoxCaseRevisionModel) Insert(ctx context.Context, session sqlx.Session, data *BlackBoxCaseRevision) (sql.Result, error) {
	artificialIntelligenceBlackBoxCaseRevisionCaseIdRevisionIdKey := fmt.Sprintf("%s%v:%v", cacheArtificialIntelligenceBlackBoxCaseRevisionCaseIdRevisionIdPrefix, data.CaseId, data.RevisionId)
	artificialIntelligenceBlackBoxCaseRevisionIdKey := fmt.Sprintf("%s%v", cacheArtificialIntelligenceBlackBoxCaseRevisionIdPrefix, data.Id)
	artificialIntelligenceBlackBoxCaseRevisionRevisionIdKey := fmt.Sprintf("%s%v", cacheArtificialIntelligenceBlackBoxCaseRevisionRevisionIdPrefix, data.RevisionId)

	return m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", m.table, blackBoxCaseRevisionRowsExpectAutoSet)
		if session != nil {
			return session.ExecCtx(ctx, query, data.CaseId, data.RevisionId, data.RevisionName, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy, data.KnowledgeParagraphTitle, data.KnowledgeParagraphTitleText, data.KnowledgeId, data.CaseRefId, data.Coverage, data.AddCount, data.UpdateCount)
		}
		return conn.ExecCtx(ctx, query, data.CaseId, data.RevisionId, data.RevisionName, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy, data.KnowledgeParagraphTitle, data.KnowledgeParagraphTitleText, data.KnowledgeId, data.CaseRefId, data.Coverage, data.AddCount, data.UpdateCount)
	}, artificialIntelligenceBlackBoxCaseRevisionCaseIdRevisionIdKey, artificialIntelligenceBlackBoxCaseRevisionIdKey, artificialIntelligenceBlackBoxCaseRevisionRevisionIdKey)
}

func (m *defaultBlackBoxCaseRevisionModel) Update(ctx context.Context, session sqlx.Session, newData *BlackBoxCaseRevision) (sql.Result, error) {
	data, err := m.FindOne(ctx, newData.Id)
	if err != nil {
		return nil, err
	}

	artificialIntelligenceBlackBoxCaseRevisionCaseIdRevisionIdKey := fmt.Sprintf("%s%v:%v", cacheArtificialIntelligenceBlackBoxCaseRevisionCaseIdRevisionIdPrefix, data.CaseId, data.RevisionId)
	artificialIntelligenceBlackBoxCaseRevisionIdKey := fmt.Sprintf("%s%v", cacheArtificialIntelligenceBlackBoxCaseRevisionIdPrefix, data.Id)
	artificialIntelligenceBlackBoxCaseRevisionRevisionIdKey := fmt.Sprintf("%s%v", cacheArtificialIntelligenceBlackBoxCaseRevisionRevisionIdPrefix, data.RevisionId)

	return m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, blackBoxCaseRevisionRowsWithPlaceHolder)
		if session != nil {
			return session.ExecCtx(ctx, query, newData.CaseId, newData.RevisionId, newData.RevisionName, newData.Deleted, newData.CreatedBy, newData.UpdatedBy, newData.DeletedBy, newData.KnowledgeParagraphTitle, newData.KnowledgeParagraphTitleText, newData.KnowledgeId, newData.CaseRefId, newData.Coverage, newData.AddCount, newData.UpdateCount, newData.Id)
		}
		return conn.ExecCtx(ctx, query, newData.CaseId, newData.RevisionId, newData.RevisionName, newData.Deleted, newData.CreatedBy, newData.UpdatedBy, newData.DeletedBy, newData.KnowledgeParagraphTitle, newData.KnowledgeParagraphTitleText, newData.KnowledgeId, newData.CaseRefId, newData.Coverage, newData.AddCount, newData.UpdateCount, newData.Id)
	}, artificialIntelligenceBlackBoxCaseRevisionCaseIdRevisionIdKey, artificialIntelligenceBlackBoxCaseRevisionIdKey, artificialIntelligenceBlackBoxCaseRevisionRevisionIdKey)
}

func (m *defaultBlackBoxCaseRevisionModel) formatPrimary(primary any) string {
	return fmt.Sprintf("%s%v", cacheArtificialIntelligenceBlackBoxCaseRevisionIdPrefix, primary)
}

func (m *defaultBlackBoxCaseRevisionModel) queryPrimary(ctx context.Context, conn sqlx.SqlConn, v, primary any) error {
	query := fmt.Sprintf("select %s from %s where `id` = ? limit 1", blackBoxCaseRevisionRows, m.table)
	return conn.QueryRowCtx(ctx, v, query, primary)
}

func (m *defaultBlackBoxCaseRevisionModel) tableName() string {
	return m.table
}
