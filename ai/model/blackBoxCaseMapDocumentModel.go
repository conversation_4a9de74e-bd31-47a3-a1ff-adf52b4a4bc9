package model

import (
	"context"

	"github.com/Masterminds/squirrel"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/types"
)

var (
	_ BlackBoxCaseMapDocumentModel = (*customBlackBoxCaseMapDocumentModel)(nil)

	blackBoxCaseMapDocumentInsertFields = stringx.Remove(blackBoxCaseMapDocumentFieldNames, "`id`", "`create_time`", "`update_time`", "`deleted_by`", "`created_at`", "`updated_at`", "`deleted_at`")
)

type (
	// BlackBoxCaseMapDocumentModel is an interface to be customized, add more methods here,
	// and implement the added methods in customBlackBoxCaseMapDocumentModel.
	BlackBoxCaseMapDocumentModel interface {
		blackBoxCaseMapDocumentModel
		types.DBModel

		Trans(ctx context.Context, fn func(context context.Context, session sqlx.Session) error) error
		InsertBuilder(data *BlackBoxCaseMapDocument) squirrel.InsertBuilder
		UpdateBuilder(data *BlackBoxCaseMapDocument) squirrel.UpdateBuilder
		SelectBuilder() squirrel.SelectBuilder
		SelectCountBuilder() squirrel.SelectBuilder
		FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error)
		FindNoCacheByQuery(ctx context.Context, selectBuilder squirrel.SelectBuilder) ([]*BlackBoxCaseMapDocument, error)

		FindByCaseId(ctx context.Context, caseId string) ([]*BlackBoxCaseMapDocument, error)
	}

	customBlackBoxCaseMapDocumentModel struct {
		*defaultBlackBoxCaseMapDocumentModel

		conn sqlx.SqlConn
	}
)

// NewBlackBoxCaseMapDocumentModel returns a model for the database table.
func NewBlackBoxCaseMapDocumentModel(conn sqlx.SqlConn, c cache.CacheConf, opts ...cache.Option) BlackBoxCaseMapDocumentModel {
	return &customBlackBoxCaseMapDocumentModel{
		defaultBlackBoxCaseMapDocumentModel: newBlackBoxCaseMapDocumentModel(conn, c, opts...),
		conn:                                conn,
	}
}

func (m *customBlackBoxCaseMapDocumentModel) Table() string {
	return m.table
}

func (m *customBlackBoxCaseMapDocumentModel) Fields() []string {
	return blackBoxCaseMapDocumentFieldNames
}

func (m *customBlackBoxCaseMapDocumentModel) Trans(ctx context.Context, fn func(context context.Context, session sqlx.Session) error) error {
	return m.TransactCtx(ctx, fn)
}

func (m *customBlackBoxCaseMapDocumentModel) InsertBuilder(data *BlackBoxCaseMapDocument) squirrel.InsertBuilder {
	// TODO: fill the insert values
	return squirrel.Insert(m.table).Columns(blackBoxCaseMapDocumentInsertFields...).Values()
}

func (m *customBlackBoxCaseMapDocumentModel) UpdateBuilder(data *BlackBoxCaseMapDocument) squirrel.UpdateBuilder {
	eq := squirrel.Eq{
		// TODO: fill the update values
	}
	return squirrel.Update(m.table).SetMap(eq).Where("`id` = ?", data.Id)
}

func (m *customBlackBoxCaseMapDocumentModel) SelectBuilder() squirrel.SelectBuilder {
	return squirrel.Select(blackBoxCaseMapDocumentFieldNames...).Where("`deleted` = ?", constants.NotDeleted).From(m.table)
}

func (m *customBlackBoxCaseMapDocumentModel) SelectCountBuilder() squirrel.SelectBuilder {
	return squirrel.Select("count(*)").Where("`deleted` = ?", constants.NotDeleted).From(m.table)
}

func (m *customBlackBoxCaseMapDocumentModel) FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error) {
	query, values, err := countBuilder.ToSql()
	if err != nil {
		return 0, err
	}

	var resp int64
	err = m.QueryRowNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return 0, err
	}
}

func (m *customBlackBoxCaseMapDocumentModel) FindNoCacheByQuery(ctx context.Context, selectBuilder squirrel.SelectBuilder) ([]*BlackBoxCaseMapDocument, error) {
	query, values, err := selectBuilder.ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*BlackBoxCaseMapDocument
	err = m.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

func (m *customBlackBoxCaseMapDocumentModel) FindByCaseId(ctx context.Context, caseId string) (
	[]*BlackBoxCaseMapDocument, error,
) {
	sql := squirrel.Select(blackBoxCaseMapDocumentFieldNames...).From(m.table).Where("`case_id` = ?", caseId).OrderBy("id desc")
	query, values, err := sql.ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*BlackBoxCaseMapDocument
	err = m.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}
