package model

import (
	"context"

	"github.com/Masterminds/squirrel"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/types"
)

var (
	_ BlackBoxCaseDirV2Model = (*customBlackBoxCaseDirV2Model)(nil)

	blackBoxCaseDirV2InsertFields = stringx.Remove(blackBoxCaseDirV2FieldNames, "`id`", "`create_time`", "`update_time`", "`deleted_by`", "`created_at`", "`updated_at`", "`deleted_at`")
)

type (
	// BlackBoxCaseDirV2Model is an interface to be customized, add more methods here,
	// and implement the added methods in customBlackBoxCaseDirV2Model.
	BlackBoxCaseDirV2Model interface {
		blackBoxCaseDirV2Model
		types.DBModel

		Trans(ctx context.Context, fn func(context context.Context, session sqlx.Session) error) error
		InsertBuilder(data *BlackBoxCaseDirV2) squirrel.InsertBuilder
		UpdateBuilder(data *BlackBoxCaseDirV2) squirrel.UpdateBuilder
		SelectBuilder() squirrel.SelectBuilder
		SelectCountBuilder() squirrel.SelectBuilder
		FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error)
		FindNoCacheByQuery(ctx context.Context, selectBuilder squirrel.SelectBuilder) ([]*BlackBoxCaseDirV2, error)

		FindByProjectId(ctx context.Context, projectId string, user string) ([]*BlackBoxCaseDirV2, error)
	}

	customBlackBoxCaseDirV2Model struct {
		*defaultBlackBoxCaseDirV2Model

		conn sqlx.SqlConn
	}
)

// NewBlackBoxCaseDirV2Model returns a model for the database table.
func NewBlackBoxCaseDirV2Model(conn sqlx.SqlConn, c cache.CacheConf, opts ...cache.Option) BlackBoxCaseDirV2Model {
	return &customBlackBoxCaseDirV2Model{
		defaultBlackBoxCaseDirV2Model: newBlackBoxCaseDirV2Model(conn, c, opts...),
		conn:                          conn,
	}
}

func (m *customBlackBoxCaseDirV2Model) Table() string {
	return m.table
}

func (m *customBlackBoxCaseDirV2Model) Fields() []string {
	return blackBoxCaseDirV2FieldNames
}

func (m *customBlackBoxCaseDirV2Model) Trans(ctx context.Context, fn func(context context.Context, session sqlx.Session) error) error {
	return m.TransactCtx(ctx, fn)
}

func (m *customBlackBoxCaseDirV2Model) InsertBuilder(data *BlackBoxCaseDirV2) squirrel.InsertBuilder {
	// TODO: fill the insert values
	return squirrel.Insert(m.table).Columns(blackBoxCaseDirV2InsertFields...).Values()
}

func (m *customBlackBoxCaseDirV2Model) UpdateBuilder(data *BlackBoxCaseDirV2) squirrel.UpdateBuilder {
	eq := squirrel.Eq{
		// TODO: fill the update values
	}
	return squirrel.Update(m.table).SetMap(eq).Where("`id` = ?", data.Id)
}

func (m *customBlackBoxCaseDirV2Model) SelectBuilder() squirrel.SelectBuilder {
	return squirrel.Select(blackBoxCaseDirV2FieldNames...).Where("`deleted` = ?", constants.NotDeleted).From(m.table)
}

func (m *customBlackBoxCaseDirV2Model) SelectCountBuilder() squirrel.SelectBuilder {
	return squirrel.Select("count(*)").Where("`deleted` = ?", constants.NotDeleted).From(m.table)
}

func (m *customBlackBoxCaseDirV2Model) FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error) {
	query, values, err := countBuilder.ToSql()
	if err != nil {
		return 0, err
	}

	var resp int64
	err = m.QueryRowNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return 0, err
	}
}

func (m *customBlackBoxCaseDirV2Model) FindNoCacheByQuery(ctx context.Context, selectBuilder squirrel.SelectBuilder) ([]*BlackBoxCaseDirV2, error) {
	query, values, err := selectBuilder.ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*BlackBoxCaseDirV2
	err = m.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

func (m *customBlackBoxCaseDirV2Model) FindByProjectId(ctx context.Context, projectId string, user string) (
	[]*BlackBoxCaseDirV2, error,
) {
	sql := squirrel.Select(blackBoxCaseDirFieldNames...).From(m.table).Where("`project_id` = ? and `created_by` = ?", projectId, user).OrderBy("id desc")
	query, values, err := sql.ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*BlackBoxCaseDirV2
	err = m.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}
