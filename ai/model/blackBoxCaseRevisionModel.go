package model

import (
	"context"
	"database/sql"
	"fmt"

	"github.com/Masterminds/squirrel"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/public/util"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/types"
)

var (
	_ BlackBoxCaseRevisionModel = (*customBlackBoxCaseRevisionModel)(nil)

	blackBoxCaseRevisionInsertFields = stringx.Remove(blackBoxCaseRevisionFieldNames, "`id`", "`create_time`", "`update_time`", "`deleted_by`", "`created_at`", "`updated_at`", "`deleted_at`")
)

type (
	// BlackBoxCaseRevisionModel is an interface to be customized, add more methods here,
	// and implement the added methods in customBlackBoxCaseRevisionModel.
	BlackBoxCaseRevisionModel interface {
		blackBoxCaseRevisionModel
		types.DBModel

		Trans(ctx context.Context, fn func(context context.Context, session sqlx.Session) error) error
		InsertBuilder(data *BlackBoxCaseRevision) squirrel.InsertBuilder
		UpdateBuilder(data *BlackBoxCaseRevision) squirrel.UpdateBuilder
		SelectBuilder() squirrel.SelectBuilder
		SelectCountBuilder() squirrel.SelectBuilder
		FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error)
		FindNoCacheByQuery(ctx context.Context, selectBuilder squirrel.SelectBuilder) ([]*BlackBoxCaseRevision, error)
		FindByCaseId(ctx context.Context, caseId string) ([]*BlackBoxCaseRevision, error)
		FindOneByCaseDataId(ctx context.Context, caseDataId string) (*BlackBoxCaseRevision, error)
		FindLastByCaseId(ctx context.Context, caseId string) (*BlackBoxCaseRevision, error)
		FindCaseIdByAdopted(ctx context.Context) ([]*BlackBoxCaseRevision, error)
		FindOneByCaseRefId(ctx context.Context, caseRefId string) (*BlackBoxCaseRevision, error)
		UpdateTX(ctx context.Context, session sqlx.Session, data *BlackBoxCaseRevision) (sql.Result, error)
	}

	customBlackBoxCaseRevisionModel struct {
		*defaultBlackBoxCaseRevisionModel

		conn sqlx.SqlConn
	}
)

// NewBlackBoxCaseRevisionModel returns a model for the database table.
func NewBlackBoxCaseRevisionModel(conn sqlx.SqlConn, c cache.CacheConf, opts ...cache.Option) BlackBoxCaseRevisionModel {
	return &customBlackBoxCaseRevisionModel{
		defaultBlackBoxCaseRevisionModel: newBlackBoxCaseRevisionModel(conn, c, opts...),
		conn:                             conn,
	}
}

func (m *customBlackBoxCaseRevisionModel) Table() string {
	return m.table
}

func (m *customBlackBoxCaseRevisionModel) Fields() []string {
	return blackBoxCaseRevisionFieldNames
}

func (m *customBlackBoxCaseRevisionModel) Trans(ctx context.Context, fn func(context context.Context, session sqlx.Session) error) error {
	return m.TransactCtx(ctx, fn)
}

func (m *customBlackBoxCaseRevisionModel) InsertBuilder(data *BlackBoxCaseRevision) squirrel.InsertBuilder {
	// TODO: fill the insert values
	return squirrel.Insert(m.table).Columns(blackBoxCaseRevisionInsertFields...).Values()
}

func (m *customBlackBoxCaseRevisionModel) UpdateBuilder(data *BlackBoxCaseRevision) squirrel.UpdateBuilder {
	eq := squirrel.Eq{
		"case_id":                        data.CaseId,
		"revision_id":                    data.RevisionId,
		"revision_name":                  data.RevisionName,
		"knowledge_id":                   data.KnowledgeId,
		"knowledge_paragraph_title":      data.KnowledgeParagraphTitle,
		"knowledge_paragraph_title_text": data.KnowledgeParagraphTitleText,
		"add_count":                      data.AddCount,
		"update_count":                   data.UpdateCount,
		"coverage":                       data.Coverage,
		"case_ref_id":                    data.CaseRefId,
	}
	return squirrel.Update(m.table).SetMap(eq).Where("`id` = ?", data.Id)
}

func (m *customBlackBoxCaseRevisionModel) SelectBuilder() squirrel.SelectBuilder {
	return squirrel.Select(blackBoxCaseRevisionFieldNames...).Where("`deleted` = ?", constants.NotDeleted).From(m.table)
}

func (m *customBlackBoxCaseRevisionModel) SelectCountBuilder() squirrel.SelectBuilder {
	return squirrel.Select("count(*)").Where("`deleted` = ?", constants.NotDeleted).From(m.table)
}

func (m *customBlackBoxCaseRevisionModel) FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error) {
	query, values, err := countBuilder.ToSql()
	if err != nil {
		return 0, err
	}

	var resp int64
	err = m.QueryRowNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return 0, err
	}
}

func (m *customBlackBoxCaseRevisionModel) FindNoCacheByQuery(ctx context.Context, selectBuilder squirrel.SelectBuilder) ([]*BlackBoxCaseRevision, error) {
	query, values, err := selectBuilder.ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*BlackBoxCaseRevision
	err = m.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

func (m *customBlackBoxCaseRevisionModel) FindByCaseId(ctx context.Context, caseId string) (
	[]*BlackBoxCaseRevision, error,
) {
	sql := squirrel.Select(blackBoxCaseRevisionFieldNames...).From(m.table).Where("`case_id` = ?", caseId).OrderBy("id desc")
	query, values, err := sql.ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*BlackBoxCaseRevision
	err = m.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}
func (m *customBlackBoxCaseRevisionModel) FindByRevisionId(ctx context.Context, revisionId string) (
	[]*BlackBoxCaseRevision, error,
) {
	sql := squirrel.Select(blackBoxCaseRevisionFieldNames...).From(m.table).Where("`revision_id` = ?", revisionId).OrderBy("id desc")
	query, values, err := sql.ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*BlackBoxCaseRevision
	err = m.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

func (m *customBlackBoxCaseRevisionModel) FindOneByCaseDataId(ctx context.Context, caseId string) (*BlackBoxCaseRevision, error) {
	sql := squirrel.Select(blackBoxCaseRevisionFieldNames...).From(m.table).Where("revision_id = (select revision_id from black_box_case_data where case_data_id = ?) limit 1", caseId)
	query, values, err := sql.ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*BlackBoxCaseRevision
	err = m.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp[0], nil
	default:
		return nil, err
	}
}

func (m *customBlackBoxCaseRevisionModel) FindLastByCaseId(ctx context.Context, caseId string) (*BlackBoxCaseRevision, error) {
	sql := squirrel.Select(blackBoxCaseRevisionFieldNames...).From(m.table).Where("`case_id` = ?", caseId).OrderBy("id desc").Limit(1)
	query, values, err := sql.ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*BlackBoxCaseRevision
	err = m.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp[0], nil
	default:
		return nil, err
	}
}

func (m *customBlackBoxCaseRevisionModel) FindCaseIdByAdopted(ctx context.Context) ([]*BlackBoxCaseRevision, error) {
	sql := squirrel.Select(blackBoxCaseRevisionFieldNames...).From(m.table).Where("revision_id in (SELECT revision_id from black_box_case_data where adopted = ? and deleted = ?)", util.BoolToInt64(true), constants.NotDeleted)
	query, values, err := sql.ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*BlackBoxCaseRevision
	err = m.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

func (m *customBlackBoxCaseRevisionModel) FindOneByCaseRefId(ctx context.Context, caseRefId string) (*BlackBoxCaseRevision, error) {
	sql := squirrel.Select(blackBoxCaseRevisionFieldNames...).From(m.table).Where("case_ref_id = ? ", caseRefId).OrderBy("id desc").Limit(1)
	query, values, err := sql.ToSql()
	if err != nil {
		return nil, err
	}
	var resp []*BlackBoxCaseRevision
	err = m.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp[0], nil
	default:
		return nil, err
	}
}

func (m *customBlackBoxCaseRevisionModel) UpdateTX(
	ctx context.Context, session sqlx.Session, data *BlackBoxCaseRevision,
) (sql.Result, error) {
	cacheArtificialIntelligenceBlackBoxCaseIdKey := fmt.Sprintf("%s%v", cacheArtificialIntelligenceBlackBoxCaseIdPrefix, data.Id)

	return m.ExecCtx(
		ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
			query, values, err := m.UpdateBuilder(data).ToSql()
			if err != nil {
				return nil, err
			}
			if session != nil {
				return session.ExecCtx(ctx, query, values...)
			}
			return conn.ExecCtx(ctx, query, values...)
		}, cacheArtificialIntelligenceBlackBoxCaseIdKey,
	)
}
