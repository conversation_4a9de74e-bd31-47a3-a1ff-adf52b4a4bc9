// Code generated by goctl. DO NOT EDIT.
// versions:
//  goctl version: 1.7.4

package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlc"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
)

var (
	blackBoxCaseTwBetaTableName           = "`black_box_case_tw_beta`"
	blackBoxCaseTwBetaFieldNames          = builder.RawFieldNames(&BlackBoxCaseTwBeta{})
	blackBoxCaseTwBetaRows                = strings.Join(blackBoxCaseTwBetaFieldNames, ",")
	blackBoxCaseTwBetaRowsExpectAutoSet   = strings.Join(stringx.Remove(blackBoxCaseTwBetaFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), ",")
	blackBoxCaseTwBetaRowsWithPlaceHolder = strings.Join(stringx.Remove(blackBoxCaseTwBetaFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), "=?,") + "=?"

	cacheArtificialIntelligenceBlackBoxCaseTwBetaIdPrefix         = "cache:artificialIntelligence:blackBoxCaseTwBeta:id:"
	cacheArtificialIntelligenceBlackBoxCaseTwBetaBetaCaseIdPrefix = "cache:artificialIntelligence:blackBoxCaseTwBeta:betaCaseId:"
)

type (
	blackBoxCaseTwBetaModel interface {
		Insert(ctx context.Context, session sqlx.Session, data *BlackBoxCaseTwBeta) (sql.Result, error)
		FindOne(ctx context.Context, id int64) (*BlackBoxCaseTwBeta, error)
		FindOneByBetaCaseId(ctx context.Context, betaCaseId string) (*BlackBoxCaseTwBeta, error)
		Update(ctx context.Context, session sqlx.Session, data *BlackBoxCaseTwBeta) (sql.Result, error)
		Delete(ctx context.Context, session sqlx.Session, id int64) error
		LogicDelete(ctx context.Context, session sqlx.Session, id int64) error
	}

	defaultBlackBoxCaseTwBetaModel struct {
		sqlc.CachedConn
		table string
	}

	BlackBoxCaseTwBeta struct {
		Id                   int64          `db:"id"`                     // 自增ID
		BetaCaseId           string         `db:"beta_case_id"`           // 用例版本ID
		BetaCaseName         string         `db:"beta_case_name"`         // 用例名称
		RevisionId           string         `db:"revision_id"`            // 版本ID
		CaseContent          string         `db:"case_content"`           // 原始用例结果
		DocumentName         string         `db:"document_name"`          // 文档名称
		DocumentUrl          string         `db:"document_url"`           // 文档URL
		DocumentChapterTitle string         `db:"document_chapter_title"` // 选中章节标题
		DocumentContent      string         `db:"document_content"`       // 选中文档内容
		ReferenceDoc         sql.NullString `db:"reference_doc"`          // 补充文档内容
		KnowledgeFixSugg     string         `db:"knowledge_fix_sugg"`     // 文档修改意见
		CaseRefId            string         `db:"case_ref_id"`            // 用例关联ID
		Status               int64          `db:"status"`                 // 会话状态：使用中：0，归档：1
		Deleted              int64          `db:"deleted"`                // 逻辑删除标识（未删除、已删除）
		CreatedBy            string         `db:"created_by"`             // 创建者的用户ID
		UpdatedBy            string         `db:"updated_by"`             // 最近一次更新者的用户ID
		DeletedBy            sql.NullString `db:"deleted_by"`             // 删除者的用户ID
		CreatedAt            time.Time      `db:"created_at"`             // 创建时间
		UpdatedAt            time.Time      `db:"updated_at"`             // 更新时间
		DeletedAt            sql.NullTime   `db:"deleted_at"`             // 删除时间
	}
)

func newBlackBoxCaseTwBetaModel(conn sqlx.SqlConn, c cache.CacheConf, opts ...cache.Option) *defaultBlackBoxCaseTwBetaModel {
	return &defaultBlackBoxCaseTwBetaModel{
		CachedConn: sqlc.NewConn(conn, c, opts...),
		table:      "`black_box_case_tw_beta`",
	}
}

func (m *defaultBlackBoxCaseTwBetaModel) Delete(ctx context.Context, session sqlx.Session, id int64) error {
	data, err := m.FindOne(ctx, id)
	if err != nil {
		return err
	}

	artificialIntelligenceBlackBoxCaseTwBetaBetaCaseIdKey := fmt.Sprintf("%s%v", cacheArtificialIntelligenceBlackBoxCaseTwBetaBetaCaseIdPrefix, data.BetaCaseId)
	artificialIntelligenceBlackBoxCaseTwBetaIdKey := fmt.Sprintf("%s%v", cacheArtificialIntelligenceBlackBoxCaseTwBetaIdPrefix, id)
	_, err = m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("delete from %s where `id` = ?", m.table)
		if session != nil {
			return session.ExecCtx(ctx, query, id)
		}
		return conn.ExecCtx(ctx, query, id)
	}, artificialIntelligenceBlackBoxCaseTwBetaBetaCaseIdKey, artificialIntelligenceBlackBoxCaseTwBetaIdKey)
	return err
}

func (m *defaultBlackBoxCaseTwBetaModel) LogicDelete(ctx context.Context, session sqlx.Session, id int64) error {
	data, err := m.FindOne(ctx, id)
	if err != nil {
		return err
	}

	artificialIntelligenceBlackBoxCaseTwBetaBetaCaseIdKey := fmt.Sprintf("%s%v", cacheArtificialIntelligenceBlackBoxCaseTwBetaBetaCaseIdPrefix, data.BetaCaseId)
	artificialIntelligenceBlackBoxCaseTwBetaIdKey := fmt.Sprintf("%s%v", cacheArtificialIntelligenceBlackBoxCaseTwBetaIdPrefix, id)
	_, err = m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set `deleted` = ?, `deleted_at` = ? where `id` = ?", m.table)
		if session != nil {
			return session.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
		}
		return conn.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
	}, artificialIntelligenceBlackBoxCaseTwBetaBetaCaseIdKey, artificialIntelligenceBlackBoxCaseTwBetaIdKey)
	return err
}

func (m *defaultBlackBoxCaseTwBetaModel) FindOne(ctx context.Context, id int64) (*BlackBoxCaseTwBeta, error) {
	artificialIntelligenceBlackBoxCaseTwBetaIdKey := fmt.Sprintf("%s%v", cacheArtificialIntelligenceBlackBoxCaseTwBetaIdPrefix, id)
	var resp BlackBoxCaseTwBeta
	err := m.QueryRowCtx(ctx, &resp, artificialIntelligenceBlackBoxCaseTwBetaIdKey, func(ctx context.Context, conn sqlx.SqlConn, v any) error {
		query := fmt.Sprintf("select %s from %s where `id` = ? and `deleted` = ? limit 1", blackBoxCaseTwBetaRows, m.table)
		return conn.QueryRowCtx(ctx, v, query, id, constants.NotDeleted)
	})
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultBlackBoxCaseTwBetaModel) FindOneByBetaCaseId(ctx context.Context, betaCaseId string) (*BlackBoxCaseTwBeta, error) {
	artificialIntelligenceBlackBoxCaseTwBetaBetaCaseIdKey := fmt.Sprintf("%s%v", cacheArtificialIntelligenceBlackBoxCaseTwBetaBetaCaseIdPrefix, betaCaseId)
	var resp BlackBoxCaseTwBeta
	err := m.QueryRowIndexCtx(ctx, &resp, artificialIntelligenceBlackBoxCaseTwBetaBetaCaseIdKey, m.formatPrimary, func(ctx context.Context, conn sqlx.SqlConn, v any) (i any, e error) {
		query := fmt.Sprintf("select %s from %s where `beta_case_id` = ? and `deleted` = ? limit 1", blackBoxCaseTwBetaRows, m.table)
		if err := conn.QueryRowCtx(ctx, &resp, query, betaCaseId, constants.NotDeleted); err != nil {
			return nil, err
		}
		return resp.Id, nil
	}, m.queryPrimary)
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultBlackBoxCaseTwBetaModel) Insert(ctx context.Context, session sqlx.Session, data *BlackBoxCaseTwBeta) (sql.Result, error) {
	artificialIntelligenceBlackBoxCaseTwBetaBetaCaseIdKey := fmt.Sprintf("%s%v", cacheArtificialIntelligenceBlackBoxCaseTwBetaBetaCaseIdPrefix, data.BetaCaseId)
	artificialIntelligenceBlackBoxCaseTwBetaIdKey := fmt.Sprintf("%s%v", cacheArtificialIntelligenceBlackBoxCaseTwBetaIdPrefix, data.Id)

	return m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", m.table, blackBoxCaseTwBetaRowsExpectAutoSet)
		if session != nil {
			return session.ExecCtx(ctx, query, data.BetaCaseId, data.BetaCaseName, data.RevisionId, data.CaseContent, data.DocumentName, data.DocumentUrl, data.DocumentChapterTitle, data.DocumentContent, data.ReferenceDoc, data.KnowledgeFixSugg, data.CaseRefId, data.Status, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy)
		}
		return conn.ExecCtx(ctx, query, data.BetaCaseId, data.BetaCaseName, data.RevisionId, data.CaseContent, data.DocumentName, data.DocumentUrl, data.DocumentChapterTitle, data.DocumentContent, data.ReferenceDoc, data.KnowledgeFixSugg, data.CaseRefId, data.Status, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy)
	}, artificialIntelligenceBlackBoxCaseTwBetaBetaCaseIdKey, artificialIntelligenceBlackBoxCaseTwBetaIdKey)
}

func (m *defaultBlackBoxCaseTwBetaModel) Update(ctx context.Context, session sqlx.Session, newData *BlackBoxCaseTwBeta) (sql.Result, error) {
	data, err := m.FindOne(ctx, newData.Id)
	if err != nil {
		return nil, err
	}

	artificialIntelligenceBlackBoxCaseTwBetaBetaCaseIdKey := fmt.Sprintf("%s%v", cacheArtificialIntelligenceBlackBoxCaseTwBetaBetaCaseIdPrefix, data.BetaCaseId)
	artificialIntelligenceBlackBoxCaseTwBetaIdKey := fmt.Sprintf("%s%v", cacheArtificialIntelligenceBlackBoxCaseTwBetaIdPrefix, data.Id)

	return m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, blackBoxCaseTwBetaRowsWithPlaceHolder)
		if session != nil {
			return session.ExecCtx(ctx, query, newData.BetaCaseId, newData.BetaCaseName, newData.RevisionId, newData.CaseContent, newData.DocumentName, newData.DocumentUrl, newData.DocumentChapterTitle, newData.DocumentContent, newData.ReferenceDoc, newData.KnowledgeFixSugg, newData.CaseRefId, newData.Status, newData.Deleted, newData.CreatedBy, newData.UpdatedBy, newData.DeletedBy, newData.Id)
		}
		return conn.ExecCtx(ctx, query, newData.BetaCaseId, newData.BetaCaseName, newData.RevisionId, newData.CaseContent, newData.DocumentName, newData.DocumentUrl, newData.DocumentChapterTitle, newData.DocumentContent, newData.ReferenceDoc, newData.KnowledgeFixSugg, newData.CaseRefId, newData.Status, newData.Deleted, newData.CreatedBy, newData.UpdatedBy, newData.DeletedBy, newData.Id)
	}, artificialIntelligenceBlackBoxCaseTwBetaBetaCaseIdKey, artificialIntelligenceBlackBoxCaseTwBetaIdKey)
}

func (m *defaultBlackBoxCaseTwBetaModel) formatPrimary(primary any) string {
	return fmt.Sprintf("%s%v", cacheArtificialIntelligenceBlackBoxCaseTwBetaIdPrefix, primary)
}

func (m *defaultBlackBoxCaseTwBetaModel) queryPrimary(ctx context.Context, conn sqlx.SqlConn, v, primary any) error {
	query := fmt.Sprintf("select %s from %s where `id` = ? limit 1", blackBoxCaseTwBetaRows, m.table)
	return conn.QueryRowCtx(ctx, v, query, primary)
}

func (m *defaultBlackBoxCaseTwBetaModel) tableName() string {
	return m.table
}
