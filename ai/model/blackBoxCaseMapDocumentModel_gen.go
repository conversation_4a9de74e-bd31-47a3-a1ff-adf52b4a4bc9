// Code generated by goctl. DO NOT EDIT.
// versions:
//  goctl version: 1.7.4

package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlc"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
)

var (
	blackBoxCaseMapDocumentTableName           = "`black_box_case_map_document`"
	blackBoxCaseMapDocumentFieldNames          = builder.RawFieldNames(&BlackBoxCaseMapDocument{})
	blackBoxCaseMapDocumentRows                = strings.Join(blackBoxCaseMapDocumentFieldNames, ",")
	blackBoxCaseMapDocumentRowsExpectAutoSet   = strings.Join(stringx.Remove(blackBoxCaseMapDocumentFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), ",")
	blackBoxCaseMapDocumentRowsWithPlaceHolder = strings.Join(stringx.Remove(blackBoxCaseMapDocumentFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), "=?,") + "=?"

	cacheArtificialIntelligenceBlackBoxCaseMapDocumentIdPrefix           = "cache:artificialIntelligence:blackBoxCaseMapDocument:id:"
	cacheArtificialIntelligenceBlackBoxCaseMapDocumentCaseIdFuncIdPrefix = "cache:artificialIntelligence:blackBoxCaseMapDocument:caseId:funcId:"
)

type (
	blackBoxCaseMapDocumentModel interface {
		Insert(ctx context.Context, session sqlx.Session, data *BlackBoxCaseMapDocument) (sql.Result, error)
		FindOne(ctx context.Context, id int64) (*BlackBoxCaseMapDocument, error)
		FindOneByCaseIdFuncId(ctx context.Context, caseId string, funcId string) (*BlackBoxCaseMapDocument, error)
		Update(ctx context.Context, session sqlx.Session, data *BlackBoxCaseMapDocument) (sql.Result, error)
		Delete(ctx context.Context, session sqlx.Session, id int64) error
		LogicDelete(ctx context.Context, session sqlx.Session, id int64) error
	}

	defaultBlackBoxCaseMapDocumentModel struct {
		sqlc.CachedConn
		table string
	}

	BlackBoxCaseMapDocument struct {
		Id        int64          `db:"id"`         // 自增ID
		ProjectId string         `db:"project_id"` // 项目ID
		DirId     string         `db:"dir_id"`     // 目录ID
		CaseId    string         `db:"case_id"`    // 用例ID
		MapId     string         `db:"map_id"`     // 评审导图ID
		FuncId    string         `db:"func_id"`    // 功能点ID
		FuncName  string         `db:"func_name"`  // 功能点名称
		FuncDoc   sql.NullString `db:"func_doc"`   // AI生成功能点的补充文档
		ExpeDoc   sql.NullString `db:"expe_doc"`   // AI生成测试经验的补充文档
		Deleted   int64          `db:"deleted"`    // 逻辑删除标识（未删除、已删除）
		CreatedBy string         `db:"created_by"` // 创建者的用户ID
		UpdatedBy string         `db:"updated_by"` // 最近一次更新者的用户ID
		DeletedBy sql.NullString `db:"deleted_by"` // 删除者的用户ID
		CreatedAt time.Time      `db:"created_at"` // 创建时间
		UpdatedAt time.Time      `db:"updated_at"` // 更新时间
		DeletedAt sql.NullTime   `db:"deleted_at"` // 删除时间
	}
)

func newBlackBoxCaseMapDocumentModel(conn sqlx.SqlConn, c cache.CacheConf, opts ...cache.Option) *defaultBlackBoxCaseMapDocumentModel {
	return &defaultBlackBoxCaseMapDocumentModel{
		CachedConn: sqlc.NewConn(conn, c, opts...),
		table:      "`black_box_case_map_document`",
	}
}

func (m *defaultBlackBoxCaseMapDocumentModel) Delete(ctx context.Context, session sqlx.Session, id int64) error {
	data, err := m.FindOne(ctx, id)
	if err != nil {
		return err
	}

	artificialIntelligenceBlackBoxCaseMapDocumentCaseIdFuncIdKey := fmt.Sprintf("%s%v:%v", cacheArtificialIntelligenceBlackBoxCaseMapDocumentCaseIdFuncIdPrefix, data.CaseId, data.FuncId)
	artificialIntelligenceBlackBoxCaseMapDocumentIdKey := fmt.Sprintf("%s%v", cacheArtificialIntelligenceBlackBoxCaseMapDocumentIdPrefix, id)
	_, err = m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("delete from %s where `id` = ?", m.table)
		if session != nil {
			return session.ExecCtx(ctx, query, id)
		}
		return conn.ExecCtx(ctx, query, id)
	}, artificialIntelligenceBlackBoxCaseMapDocumentCaseIdFuncIdKey, artificialIntelligenceBlackBoxCaseMapDocumentIdKey)
	return err
}

func (m *defaultBlackBoxCaseMapDocumentModel) LogicDelete(ctx context.Context, session sqlx.Session, id int64) error {
	data, err := m.FindOne(ctx, id)
	if err != nil {
		return err
	}

	artificialIntelligenceBlackBoxCaseMapDocumentCaseIdFuncIdKey := fmt.Sprintf("%s%v:%v", cacheArtificialIntelligenceBlackBoxCaseMapDocumentCaseIdFuncIdPrefix, data.CaseId, data.FuncId)
	artificialIntelligenceBlackBoxCaseMapDocumentIdKey := fmt.Sprintf("%s%v", cacheArtificialIntelligenceBlackBoxCaseMapDocumentIdPrefix, id)
	_, err = m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set `deleted` = ?, `deleted_at` = ? where `id` = ?", m.table)
		if session != nil {
			return session.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
		}
		return conn.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
	}, artificialIntelligenceBlackBoxCaseMapDocumentCaseIdFuncIdKey, artificialIntelligenceBlackBoxCaseMapDocumentIdKey)
	return err
}

func (m *defaultBlackBoxCaseMapDocumentModel) FindOne(ctx context.Context, id int64) (*BlackBoxCaseMapDocument, error) {
	artificialIntelligenceBlackBoxCaseMapDocumentIdKey := fmt.Sprintf("%s%v", cacheArtificialIntelligenceBlackBoxCaseMapDocumentIdPrefix, id)
	var resp BlackBoxCaseMapDocument
	err := m.QueryRowCtx(ctx, &resp, artificialIntelligenceBlackBoxCaseMapDocumentIdKey, func(ctx context.Context, conn sqlx.SqlConn, v any) error {
		query := fmt.Sprintf("select %s from %s where `id` = ? and `deleted` = ? limit 1", blackBoxCaseMapDocumentRows, m.table)
		return conn.QueryRowCtx(ctx, v, query, id, constants.NotDeleted)
	})
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultBlackBoxCaseMapDocumentModel) FindOneByCaseIdFuncId(ctx context.Context, caseId string, funcId string) (*BlackBoxCaseMapDocument, error) {
	artificialIntelligenceBlackBoxCaseMapDocumentCaseIdFuncIdKey := fmt.Sprintf("%s%v:%v", cacheArtificialIntelligenceBlackBoxCaseMapDocumentCaseIdFuncIdPrefix, caseId, funcId)
	var resp BlackBoxCaseMapDocument
	err := m.QueryRowIndexCtx(ctx, &resp, artificialIntelligenceBlackBoxCaseMapDocumentCaseIdFuncIdKey, m.formatPrimary, func(ctx context.Context, conn sqlx.SqlConn, v any) (i any, e error) {
		query := fmt.Sprintf("select %s from %s where `case_id` = ? and `func_id` = ? and `deleted` = ? limit 1", blackBoxCaseMapDocumentRows, m.table)
		if err := conn.QueryRowCtx(ctx, &resp, query, caseId, funcId, constants.NotDeleted); err != nil {
			return nil, err
		}
		return resp.Id, nil
	}, m.queryPrimary)
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultBlackBoxCaseMapDocumentModel) Insert(ctx context.Context, session sqlx.Session, data *BlackBoxCaseMapDocument) (sql.Result, error) {
	artificialIntelligenceBlackBoxCaseMapDocumentCaseIdFuncIdKey := fmt.Sprintf("%s%v:%v", cacheArtificialIntelligenceBlackBoxCaseMapDocumentCaseIdFuncIdPrefix, data.CaseId, data.FuncId)
	artificialIntelligenceBlackBoxCaseMapDocumentIdKey := fmt.Sprintf("%s%v", cacheArtificialIntelligenceBlackBoxCaseMapDocumentIdPrefix, data.Id)

	return m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", m.table, blackBoxCaseMapDocumentRowsExpectAutoSet)
		if session != nil {
			return session.ExecCtx(ctx, query, data.ProjectId, data.DirId, data.CaseId, data.MapId, data.FuncId, data.FuncName, data.FuncDoc, data.ExpeDoc, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy)
		}
		return conn.ExecCtx(ctx, query, data.ProjectId, data.DirId, data.CaseId, data.MapId, data.FuncId, data.FuncName, data.FuncDoc, data.ExpeDoc, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy)
	}, artificialIntelligenceBlackBoxCaseMapDocumentCaseIdFuncIdKey, artificialIntelligenceBlackBoxCaseMapDocumentIdKey)
}

func (m *defaultBlackBoxCaseMapDocumentModel) Update(ctx context.Context, session sqlx.Session, newData *BlackBoxCaseMapDocument) (sql.Result, error) {
	data, err := m.FindOne(ctx, newData.Id)
	if err != nil {
		return nil, err
	}

	artificialIntelligenceBlackBoxCaseMapDocumentCaseIdFuncIdKey := fmt.Sprintf("%s%v:%v", cacheArtificialIntelligenceBlackBoxCaseMapDocumentCaseIdFuncIdPrefix, data.CaseId, data.FuncId)
	artificialIntelligenceBlackBoxCaseMapDocumentIdKey := fmt.Sprintf("%s%v", cacheArtificialIntelligenceBlackBoxCaseMapDocumentIdPrefix, data.Id)

	return m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, blackBoxCaseMapDocumentRowsWithPlaceHolder)
		if session != nil {
			return session.ExecCtx(ctx, query, newData.ProjectId, newData.DirId, newData.CaseId, newData.MapId, newData.FuncId, newData.FuncName, newData.FuncDoc, newData.ExpeDoc, newData.Deleted, newData.CreatedBy, newData.UpdatedBy, newData.DeletedBy, newData.Id)
		}
		return conn.ExecCtx(ctx, query, newData.ProjectId, newData.DirId, newData.CaseId, newData.MapId, newData.FuncId, newData.FuncName, newData.FuncDoc, newData.ExpeDoc, newData.Deleted, newData.CreatedBy, newData.UpdatedBy, newData.DeletedBy, newData.Id)
	}, artificialIntelligenceBlackBoxCaseMapDocumentCaseIdFuncIdKey, artificialIntelligenceBlackBoxCaseMapDocumentIdKey)
}

func (m *defaultBlackBoxCaseMapDocumentModel) formatPrimary(primary any) string {
	return fmt.Sprintf("%s%v", cacheArtificialIntelligenceBlackBoxCaseMapDocumentIdPrefix, primary)
}

func (m *defaultBlackBoxCaseMapDocumentModel) queryPrimary(ctx context.Context, conn sqlx.SqlConn, v, primary any) error {
	query := fmt.Sprintf("select %s from %s where `id` = ? limit 1", blackBoxCaseMapDocumentRows, m.table)
	return conn.QueryRowCtx(ctx, v, query, primary)
}

func (m *defaultBlackBoxCaseMapDocumentModel) tableName() string {
	return m.table
}
