package model

import (
	"context"

	"github.com/Masterminds/squirrel"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/types"
)

var (
	_ BlackBoxCaseMapModel = (*customBlackBoxCaseMapModel)(nil)

	blackBoxCaseMapInsertFields = stringx.Remove(blackBoxCaseMapFieldNames, "`id`", "`create_time`", "`update_time`", "`deleted_by`", "`created_at`", "`updated_at`", "`deleted_at`")
)

type (
	// BlackBoxCaseMapModel is an interface to be customized, add more methods here,
	// and implement the added methods in customBlackBoxCaseMapModel.
	BlackBoxCaseMapModel interface {
		blackBoxCaseMapModel
		types.DBModel

		Trans(ctx context.Context, fn func(context context.Context, session sqlx.Session) error) error
		InsertBuilder(data *BlackBoxCaseMap) squirrel.InsertBuilder
		UpdateBuilder(data *BlackBoxCaseMap) squirrel.UpdateBuilder
		SelectBuilder() squirrel.SelectBuilder
		SelectCountBuilder() squirrel.SelectBuilder
		FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error)
		FindNoCacheByQuery(ctx context.Context, selectBuilder squirrel.SelectBuilder) ([]*BlackBoxCaseMap, error)

		FindByMapId(ctx context.Context, mapId string) ([]*BlackBoxCaseMap, error)
	}

	customBlackBoxCaseMapModel struct {
		*defaultBlackBoxCaseMapModel

		conn sqlx.SqlConn
	}
)

// NewBlackBoxCaseMapModel returns a model for the database table.
func NewBlackBoxCaseMapModel(conn sqlx.SqlConn, c cache.CacheConf, opts ...cache.Option) BlackBoxCaseMapModel {
	return &customBlackBoxCaseMapModel{
		defaultBlackBoxCaseMapModel: newBlackBoxCaseMapModel(conn, c, opts...),
		conn:                        conn,
	}
}

func (m *customBlackBoxCaseMapModel) Table() string {
	return m.table
}

func (m *customBlackBoxCaseMapModel) Fields() []string {
	return blackBoxCaseMapFieldNames
}

func (m *customBlackBoxCaseMapModel) Trans(ctx context.Context, fn func(context context.Context, session sqlx.Session) error) error {
	return m.TransactCtx(ctx, fn)
}

func (m *customBlackBoxCaseMapModel) InsertBuilder(data *BlackBoxCaseMap) squirrel.InsertBuilder {
	// TODO: fill the insert values
	return squirrel.Insert(m.table).Columns(blackBoxCaseMapInsertFields...).Values()
}

func (m *customBlackBoxCaseMapModel) UpdateBuilder(data *BlackBoxCaseMap) squirrel.UpdateBuilder {
	eq := squirrel.Eq{
		// TODO: fill the update values
	}
	return squirrel.Update(m.table).SetMap(eq).Where("`id` = ?", data.Id)
}

func (m *customBlackBoxCaseMapModel) SelectBuilder() squirrel.SelectBuilder {
	return squirrel.Select(blackBoxCaseMapFieldNames...).Where("`deleted` = ?", constants.NotDeleted).From(m.table)
}

func (m *customBlackBoxCaseMapModel) SelectCountBuilder() squirrel.SelectBuilder {
	return squirrel.Select("count(*)").Where("`deleted` = ?", constants.NotDeleted).From(m.table)
}

func (m *customBlackBoxCaseMapModel) FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error) {
	query, values, err := countBuilder.ToSql()
	if err != nil {
		return 0, err
	}

	var resp int64
	err = m.QueryRowNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return 0, err
	}
}

func (m *customBlackBoxCaseMapModel) FindNoCacheByQuery(ctx context.Context, selectBuilder squirrel.SelectBuilder) ([]*BlackBoxCaseMap, error) {
	query, values, err := selectBuilder.ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*BlackBoxCaseMap
	err = m.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

func (m *customBlackBoxCaseMapModel) FindByMapId(ctx context.Context, mapId string) ([]*BlackBoxCaseMap, error) {
	sql := squirrel.Select(blackBoxCaseMapFieldNames...).From(m.table).Where("`map_id` = ?", mapId).OrderBy("id desc")
	query, values, err := sql.ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*BlackBoxCaseMap
	err = m.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}
