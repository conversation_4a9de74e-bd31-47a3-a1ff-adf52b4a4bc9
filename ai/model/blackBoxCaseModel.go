package model

import (
	"context"
	"database/sql"
	"fmt"

	"github.com/Masterminds/squirrel"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlc"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sqlbuilder"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sqlbuilder/rpc"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/types"
)

var (
	_ BlackBoxCaseModel = (*customBlackBoxCaseModel)(nil)

	blackBoxCaseInsertFields = stringx.Remove(blackBoxCaseFieldNames, "`id`", "`create_time`", "`update_time`", "`deleted_by`", "`created_at`", "`updated_at`", "`deleted_at`")
)

type (
	// BlackBoxCaseModel is an interface to be customized, add more methods here,
	// and implement the added methods in customBlackBoxCaseModel.
	BlackBoxCaseModel interface {
		blackBoxCaseModel
		types.DBModel

		Trans(ctx context.Context, fn func(context context.Context, session sqlx.Session) error) error
		InsertBuilder(data *BlackBoxCase) squirrel.InsertBuilder
		UpdateBuilder(data *BlackBoxCase) squirrel.UpdateBuilder
		SelectBuilder() squirrel.SelectBuilder
		SelectCountBuilder() squirrel.SelectBuilder
		SelectCountAllBuilder() squirrel.SelectBuilder
		FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error)
		FindNoCacheByQuery(ctx context.Context, selectBuilder squirrel.SelectBuilder) ([]*BlackBoxCase, error)
		FindByQueryObj(ctx context.Context, req ListBlackBoxCaseQueryObj) ([]*BlackBoxCase, error)
		CountByQueryObj(ctx context.Context, req ListBlackBoxCaseQueryObj) (int64, error)
		UpdateByObj(ctx context.Context, session sqlx.Session, data *BlackBoxCase) error
		QueryInc(ctx context.Context, session sqlx.Session, caseId string) error
		CountQueryTimes(ctx context.Context) (int64, error)
		UpdateTX(ctx context.Context, session sqlx.Session, data *BlackBoxCase) (sql.Result, error)
	}

	customBlackBoxCaseModel struct {
		*defaultBlackBoxCaseModel

		conn sqlx.SqlConn
	}

	ListBlackBoxCaseQueryObj struct {
		DirId      string
		Pagination *rpc.Pagination
		CaseName   string
	}
)

// NewBlackBoxCaseModel returns a model for the database table.
func NewBlackBoxCaseModel(conn sqlx.SqlConn, c cache.CacheConf, opts ...cache.Option) BlackBoxCaseModel {
	return &customBlackBoxCaseModel{
		defaultBlackBoxCaseModel: newBlackBoxCaseModel(conn, c, opts...),
		conn:                     conn,
	}
}

func (m *customBlackBoxCaseModel) Table() string {
	return m.table
}

func (m *customBlackBoxCaseModel) Fields() []string {
	return blackBoxCaseFieldNames
}

func (m *customBlackBoxCaseModel) Trans(ctx context.Context, fn func(context context.Context, session sqlx.Session) error) error {
	return m.TransactCtx(ctx, fn)
}

func (m *customBlackBoxCaseModel) InsertBuilder(data *BlackBoxCase) squirrel.InsertBuilder {
	// TODO: fill the insert values
	return squirrel.Insert(m.table).Columns(blackBoxCaseInsertFields...).Values()
}

func (m *customBlackBoxCaseModel) UpdateBuilder(data *BlackBoxCase) squirrel.UpdateBuilder {
	eq := squirrel.Eq{
		"dir_id":                         data.DirId,
		"case_id":                        data.CaseId,
		"case_name":                      data.CaseName,
		"case_continue_to_write":         data.CaseContinueToWrite,
		"case_model_character":           data.CaseModelCharacter,
		"knowledge_id":                   data.KnowledgeId,
		"case_remarks":                   data.CaseRemarks,
		"knowledge_paragraph_title_id":   data.KnowledgeParagraphTitleId,
		"reference_doc":                  data.ReferenceDoc,
		"enable_reference":               data.EnableReference,
		"knowledge_fix_sugg":             data.KnowledgeFixSugg,
		"query_times":                    data.QueryTimes,
		"knowledge_paragraph_title_text": data.KnowledgeParagraphTitleText,
		"knowledge_paragraph_titles":     data.KnowledgeParagraphTitles,
		"knowledge_size":                 data.KnowledgeSize,
	}
	return squirrel.Update(m.table).SetMap(eq).Where("`id` = ?", data.Id)
}

func (m *customBlackBoxCaseModel) SelectBuilder() squirrel.SelectBuilder {
	return squirrel.Select(blackBoxCaseFieldNames...).Where("`deleted` = ?", constants.NotDeleted).From(m.table)
}

func (m *customBlackBoxCaseModel) SelectCountBuilder() squirrel.SelectBuilder {
	return squirrel.Select("count(*)").Where("`deleted` = ?", constants.NotDeleted).From(m.table)
}

func (m *customBlackBoxCaseModel) SelectCountAllBuilder() squirrel.SelectBuilder {
	return squirrel.Select("count(*)").From(m.table)
}

func (m *customBlackBoxCaseModel) FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error) {
	query, values, err := countBuilder.ToSql()
	if err != nil {
		return 0, err
	}

	var resp int64
	err = m.QueryRowNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return 0, err
	}
}

func (m *customBlackBoxCaseModel) FindNoCacheByQuery(ctx context.Context, selectBuilder squirrel.SelectBuilder) ([]*BlackBoxCase, error) {
	query, values, err := selectBuilder.ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*BlackBoxCase
	err = m.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

func (m *customBlackBoxCaseModel) FindByQueryObj(ctx context.Context, req ListBlackBoxCaseQueryObj) ([]*BlackBoxCase, error) {
	sql := m.SelectBuilder()
	sql = sql.Where("`dir_id` = ?", req.DirId)
	if len(req.CaseName) > 0 {
		sql = sql.Where("`case_name` like ? and `deleted` = ? ", "%"+req.CaseName+"%", constants.NotDeleted)
	}

	sql = sqlbuilder.SearchOptions(
		sql, sqlbuilder.WithPagination(m, req.Pagination),
	).OrderBy("`updated_at` desc")

	query, values, err := sql.ToSql()
	if err != nil {
		return nil, err
	}
	var resp []*BlackBoxCase
	err = m.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	case sqlc.ErrNotFound:
		return resp, nil
	default:
		return nil, err
	}
}

func (m *customBlackBoxCaseModel) CountByQueryObj(ctx context.Context, req ListBlackBoxCaseQueryObj) (int64, error) {
	sql := m.SelectCountBuilder()
	sql = sql.Where("`dir_id` = ?", req.DirId)
	if len(req.CaseName) > 0 {
		sql = sql.Where("`case_name` like ? and `deleted` = ? ", "%"+req.CaseName+"%", constants.NotDeleted)
	}

	resp, err := m.FindCount(ctx, sql)
	switch err {
	case nil:
		return resp, nil
	case sqlc.ErrNotFound:
		return resp, nil
	default:
		return 0, err
	}
}

func (m *customBlackBoxCaseModel) UpdateByObj(ctx context.Context, session sqlx.Session, data *BlackBoxCase) error {
	artificialIntelligenceBlackBoxCaseCaseIdKey := fmt.Sprintf("%s%v", cacheArtificialIntelligenceBlackBoxCaseCaseIdPrefix, data.CaseId)
	query := fmt.Sprintf(
		"update %s set `case_name` = ? , `case_continue_to_write` = ? , `case_model_character` = ? , `updated_at` = current_timestamp() where `case_id` = ?",
		m.table,
	)
	_, err := m.ExecCtx(
		ctx, func(ctx context.Context, conn sqlx.SqlConn) (sql.Result, error) {
			if session != nil {
				row, err := session.ExecCtx(ctx, query, data.CaseName, data.CaseContinueToWrite, data.CaseModelCharacter, data.CaseId)
				return row, err
			}
			row, err := m.conn.ExecCtx(ctx, query, data.CaseName, data.CaseContinueToWrite, data.CaseModelCharacter, data.CaseId)
			return row, err
		}, artificialIntelligenceBlackBoxCaseCaseIdKey,
	)
	return err
}

func (m *customBlackBoxCaseModel) QueryInc(ctx context.Context, session sqlx.Session, caseId string) error {
	query := fmt.Sprintf(
		"update %s set `query_times` = query_times + 1 where `case_id` = ?",
		m.table,
	)
	_, err := m.ExecCtx(
		ctx, func(ctx context.Context, conn sqlx.SqlConn) (sql.Result, error) {
			if session != nil {
				row, err := session.ExecCtx(ctx, query, caseId)
				return row, err
			}
			row, err := m.conn.ExecCtx(ctx, query, caseId)
			return row, err
		},
	)
	return err
}

func (m *customBlackBoxCaseModel) CountQueryTimes(ctx context.Context) (int64, error) {
	type CaseResp struct {
		QueryTimes int64 `db:"query_times"`
	}
	var o CaseResp
	// err = m.QueryRowsNoCacheCtx(ctx, &o, query, values...)
	err := m.conn.QueryRowCtx(ctx, &o, "SELECT sum(query_times) as query_times FROM `black_box_case` WHERE `query_times` > 0")
	switch err {
	case nil:
		return o.QueryTimes, nil
	default:
		return 0, err
	}
}

func (m *customBlackBoxCaseModel) UpdateTX(
	ctx context.Context, session sqlx.Session, data *BlackBoxCase,
) (sql.Result, error) {
	cacheArtificialIntelligenceBlackBoxCaseIdKey := fmt.Sprintf("%s%v", cacheArtificialIntelligenceBlackBoxCaseIdPrefix, data.Id)

	return m.ExecCtx(
		ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
			query, values, err := m.UpdateBuilder(data).ToSql()
			if err != nil {
				return nil, err
			}
			if session != nil {
				return session.ExecCtx(ctx, query, values...)
			}
			return conn.ExecCtx(ctx, query, values...)
		}, cacheArtificialIntelligenceBlackBoxCaseIdKey,
	)
}
