package model

import (
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
)

var _ BlackBoxCaseDemoModel = (*customBlackBoxCaseDemoModel)(nil)

type (
	// BlackBoxCaseDemoModel is an interface to be customized, add more methods here,
	// and implement the added methods in customBlackBoxCaseDemoModel.
	BlackBoxCaseDemoModel interface {
		blackBoxCaseDemoModel
	}

	customBlackBoxCaseDemoModel struct {
		*defaultBlackBoxCaseDemoModel
	}
)

// NewBlackBoxCaseDemoModel returns a model for the database table.
func NewBlackBoxCaseDemoModel(conn sqlx.SqlConn, c cache.CacheConf, opts ...cache.Option) BlackBoxCaseDemoModel {
	return &customBlackBoxCaseDemoModel{
		defaultBlackBoxCaseDemoModel: newBlackBoxCaseDemoModel(conn, c, opts...),
	}
}
