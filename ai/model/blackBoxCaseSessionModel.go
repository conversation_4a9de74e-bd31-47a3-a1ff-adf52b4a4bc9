package model

import (
	"context"

	"github.com/Masterminds/squirrel"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/types"
)

var (
	_ BlackBoxCaseSessionModel = (*customBlackBoxCaseSessionModel)(nil)

	blackBoxCaseSessionInsertFields = stringx.Remove(
		blackBoxCaseSessionFieldNames, "`id`", "`create_time`", "`update_time`", "`deleted_by`", "`created_at`",
		"`updated_at`", "`deleted_at`",
	)
)

type (
	// BlackBoxCaseSessionModel is an interface to be customized, add more methods here,
	// and implement the added methods in customBlackBoxCaseSessionModel.
	BlackBoxCaseSessionModel interface {
		blackBoxCaseSessionModel
		types.DBModel

		Trans(ctx context.Context, fn func(context context.Context, session sqlx.Session) error) error
		InsertBuilder(data *BlackBoxCaseSession) squirrel.InsertBuilder
		UpdateBuilder(data *BlackBoxCaseSession) squirrel.UpdateBuilder
		SelectBuilder() squirrel.SelectBuilder
		SelectCountBuilder() squirrel.SelectBuilder
		FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error)
		FindNoCacheByQuery(ctx context.Context, selectBuilder squirrel.SelectBuilder) ([]*BlackBoxCaseSession, error)
		FindByProjectIdAssistantIdAccount(
			ctx context.Context, projectId, assistantId, account string,
		) ([]*BlackBoxCaseSession, error)
		DeleteByProjectIdAssistantId(
			ctx context.Context, session sqlx.Session, projectId, assistantId string,
		) error
		DeleteByProjectIdAssistantIdSessionId(
			ctx context.Context, session sqlx.Session, projectId, assistantId, sessionId string,
		) error
		UpdateSessionRoundByProjectIdAssistantId(
			ctx context.Context, session sqlx.Session, projectId, assistantId string, sessionRound int64,
		) error
	}

	customBlackBoxCaseSessionModel struct {
		*defaultBlackBoxCaseSessionModel

		conn sqlx.SqlConn
	}
)

// NewBlackBoxCaseSessionModel returns a model for the database table.
func NewBlackBoxCaseSessionModel(conn sqlx.SqlConn, c cache.CacheConf, opts ...cache.Option) BlackBoxCaseSessionModel {
	return &customBlackBoxCaseSessionModel{
		defaultBlackBoxCaseSessionModel: newBlackBoxCaseSessionModel(conn, c, opts...),
		conn:                            conn,
	}
}

func (m *customBlackBoxCaseSessionModel) Table() string {
	return m.table
}

func (m *customBlackBoxCaseSessionModel) Fields() []string {
	return blackBoxCaseSessionFieldNames
}

func (m *customBlackBoxCaseSessionModel) Trans(
	ctx context.Context, fn func(context context.Context, session sqlx.Session) error,
) error {
	return m.TransactCtx(ctx, fn)
}

func (m *customBlackBoxCaseSessionModel) InsertBuilder(data *BlackBoxCaseSession) squirrel.InsertBuilder {
	// TODO: fill the insert values
	return squirrel.Insert(m.table).Columns(blackBoxCaseSessionInsertFields...).Values()
}

func (m *customBlackBoxCaseSessionModel) UpdateBuilder(data *BlackBoxCaseSession) squirrel.UpdateBuilder {
	eq := squirrel.Eq{
		// TODO: fill the update values
	}
	return squirrel.Update(m.table).SetMap(eq).Where("`id` = ?", data.Id)
}

func (m *customBlackBoxCaseSessionModel) SelectBuilder() squirrel.SelectBuilder {
	return squirrel.Select(blackBoxCaseSessionFieldNames...).Where("`deleted` = ?", constants.NotDeleted).From(m.table)
}

func (m *customBlackBoxCaseSessionModel) SelectCountBuilder() squirrel.SelectBuilder {
	return squirrel.Select("count(*)").Where("`deleted` = ?", constants.NotDeleted).From(m.table)
}

func (m *customBlackBoxCaseSessionModel) FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (
	int64, error,
) {
	query, values, err := countBuilder.ToSql()
	if err != nil {
		return 0, err
	}

	var resp int64
	err = m.QueryRowNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return 0, err
	}
}

func (m *customBlackBoxCaseSessionModel) FindNoCacheByQuery(
	ctx context.Context, selectBuilder squirrel.SelectBuilder,
) ([]*BlackBoxCaseSession, error) {
	query, values, err := selectBuilder.ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*BlackBoxCaseSession
	err = m.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

func (m *customBlackBoxCaseSessionModel) FindByProjectIdAssistantIdAccount(
	ctx context.Context, projectId, assistantId, account string,
) ([]*BlackBoxCaseSession, error) {
	sqlB := m.SelectBuilder().Where(
		"`project_id` = ? and `assistant_id` = ? and `account` = ?", projectId, assistantId, account,
	).OrderBy("`created_at` desc")
	query, values, err := sqlB.ToSql()
	if err != nil {
		return nil, err
	}
	var resp []*BlackBoxCaseSession
	err = m.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case ErrNotFound:
		return resp, nil
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

func (l *customBlackBoxCaseSessionModel) DeleteByProjectIdAssistantId(
	ctx context.Context, session sqlx.Session, projectId, assistantId string,
) error {
	sqlB := squirrel.Delete(l.Table()).Where(
		"`project_id` = ? and `assistant_id` = ?", projectId, assistantId,
	)
	query, values, err := sqlB.ToSql()
	if err != nil {
		return err
	}
	if session != nil {
		_, err := session.ExecCtx(ctx, query, values...)
		return err
	}
	_, err = l.conn.ExecCtx(ctx, query, values...)
	return err
}

func (l *customBlackBoxCaseSessionModel) DeleteByProjectIdAssistantIdSessionId(
	ctx context.Context, session sqlx.Session, projectId, assistantId, sessionId string,
) error {
	sqlB := squirrel.Delete(l.Table()).Where(
		"`project_id` = ? and `assistant_id` = ? and `session_id` = ?", projectId, assistantId, sessionId,
	)
	query, values, err := sqlB.ToSql()
	if err != nil {
		return err
	}
	if session != nil {
		_, err := session.ExecCtx(ctx, query, values...)
		return err
	}
	_, err = l.conn.ExecCtx(ctx, query, values...)
	return err
}

func (l *customBlackBoxCaseSessionModel) UpdateSessionRoundByProjectIdAssistantId(
	ctx context.Context, session sqlx.Session, projectId, assistantId string, sessionRound int64,
) error {
	eq := squirrel.Eq{
		"`session_round`": sessionRound,
	}
	sqlB := squirrel.Update(l.Table()).SetMap(eq).Where(
		"`project_id` = ? and `assistant_id` = ? ", projectId, assistantId,
	)
	query, values, err := sqlB.ToSql()
	if err != nil {
		return err
	}
	if session != nil {
		_, err := session.ExecCtx(ctx, query, values...)
		return err
	}
	_, err = l.conn.ExecCtx(ctx, query, values...)
	return err
}
