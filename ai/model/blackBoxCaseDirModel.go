package model

import (
	"context"

	"github.com/Masterminds/squirrel"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
)

var _ BlackBoxCaseDirModel = (*customBlackBoxCaseDirModel)(nil)

type (
	// BlackBoxCaseDirModel is an interface to be customized, add more methods here,
	// and implement the added methods in customBlackBoxCaseDirModel.
	BlackBoxCaseDirModel interface {
		blackBoxCaseDirModel
		Trans(ctx context.Context, fn func(context context.Context, session sqlx.Session) error) error
		FindByProjectId(ctx context.Context, projectId string, user string) ([]*BlackBoxCaseDir, error)
	}

	customBlackBoxCaseDirModel struct {
		*defaultBlackBoxCaseDirModel
	}
)

// NewBlackBoxCaseDirModel returns a model for the database table.
func NewBlackBoxCaseDirModel(conn sqlx.SqlConn, c cache.CacheConf, opts ...cache.Option) BlackBoxCaseDirModel {
	return &customBlackBoxCaseDirModel{
		defaultBlackBoxCaseDirModel: newBlackBoxCaseDirModel(conn, c, opts...),
	}
}

func (m *customBlackBoxCaseDirModel) Trans(
	ctx context.Context, fn func(context context.Context, session sqlx.Session) error,
) error {
	return m.TransactCtx(ctx, fn)
}

func (m *customBlackBoxCaseDirModel) FindByProjectId(ctx context.Context, projectId string, user string) (
	[]*BlackBoxCaseDir, error,
) {
	sql := squirrel.Select(blackBoxCaseDirFieldNames...).From(m.table).Where("`project_id` = ? and `created_by` = ?", projectId, user).OrderBy("id desc")
	query, values, err := sql.ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*BlackBoxCaseDir
	err = m.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}
