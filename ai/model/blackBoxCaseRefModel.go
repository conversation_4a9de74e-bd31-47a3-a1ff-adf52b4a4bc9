package model

import (
	"context"
	"database/sql"
	"fmt"
	"strconv"

	"github.com/Masterminds/squirrel"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlc"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/types"
)

var (
	_ BlackBoxCaseRefModel = (*customBlackBoxCaseRefModel)(nil)

	blackBoxCaseRefInsertFields = stringx.Remove(blackBoxCaseRefFieldNames, "`id`", "`create_time`", "`update_time`", "`deleted_by`", "`created_at`", "`updated_at`", "`deleted_at`")
)

type (
	// BlackBoxCaseRefModel is an interface to be customized, add more methods here,
	// and implement the added methods in customBlackBoxCaseRefModel.
	BlackBoxCaseRefModel interface {
		blackBoxCaseRefModel
		types.DBModel

		Trans(ctx context.Context, fn func(context context.Context, session sqlx.Session) error) error
		InsertBuilder(data *BlackBoxCaseRef) squirrel.InsertBuilder
		UpdateBuilder(data *BlackBoxCaseRef) squirrel.UpdateBuilder
		SelectBuilder() squirrel.SelectBuilder
		SelectCountBuilder() squirrel.SelectBuilder
		FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error)
		FindNoCacheByQuery(ctx context.Context, selectBuilder squirrel.SelectBuilder) ([]*BlackBoxCaseRef, error)
		FindCountByType(ctx context.Context, caseRefType string) (int64, error)
		UpdateCountById(ctx context.Context, session sqlx.Session, caseRefId string, count int64) error
		GetSumRefDataCount(ctx context.Context) (int64, error)
	}

	customBlackBoxCaseRefModel struct {
		*defaultBlackBoxCaseRefModel

		conn sqlx.SqlConn
	}
)

// NewBlackBoxCaseRefModel returns a model for the database table.
func NewBlackBoxCaseRefModel(conn sqlx.SqlConn, c cache.CacheConf, opts ...cache.Option) BlackBoxCaseRefModel {
	return &customBlackBoxCaseRefModel{
		defaultBlackBoxCaseRefModel: newBlackBoxCaseRefModel(conn, c, opts...),
		conn:                        conn,
	}
}

func (m *customBlackBoxCaseRefModel) Table() string {
	return m.table
}

func (m *customBlackBoxCaseRefModel) Fields() []string {
	return blackBoxCaseRefFieldNames
}

func (m *customBlackBoxCaseRefModel) Trans(ctx context.Context, fn func(context context.Context, session sqlx.Session) error) error {
	return m.TransactCtx(ctx, fn)
}

func (m *customBlackBoxCaseRefModel) InsertBuilder(data *BlackBoxCaseRef) squirrel.InsertBuilder {
	// TODO: fill the insert values
	return squirrel.Insert(m.table).Columns(blackBoxCaseRefInsertFields...).Values()
}

func (m *customBlackBoxCaseRefModel) UpdateBuilder(data *BlackBoxCaseRef) squirrel.UpdateBuilder {
	eq := squirrel.Eq{
		// TODO: fill the update values
	}
	return squirrel.Update(m.table).SetMap(eq).Where("`id` = ?", data.Id)
}

func (m *customBlackBoxCaseRefModel) SelectBuilder() squirrel.SelectBuilder {
	return squirrel.Select(blackBoxCaseRefFieldNames...).Where("`deleted` = ?", constants.NotDeleted).From(m.table)
}

func (m *customBlackBoxCaseRefModel) SelectCountBuilder() squirrel.SelectBuilder {
	return squirrel.Select("count(*)").Where("`deleted` = ?", constants.NotDeleted).From(m.table)
}

func (m *customBlackBoxCaseRefModel) FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error) {
	query, values, err := countBuilder.ToSql()
	if err != nil {
		return 0, err
	}

	var resp int64
	err = m.QueryRowNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return 0, err
	}
}

func (m *customBlackBoxCaseRefModel) FindNoCacheByQuery(ctx context.Context, selectBuilder squirrel.SelectBuilder) ([]*BlackBoxCaseRef, error) {
	query, values, err := selectBuilder.ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*BlackBoxCaseRef
	err = m.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

func (m *customBlackBoxCaseRefModel) FindCountByType(ctx context.Context, caseRefType string) (int64, error) {
	sql := m.SelectCountBuilder()
	sql = sql.Where("`case_ref_type` = ?", caseRefType)
	resp, err := m.FindCount(ctx, sql)
	switch err {
	case nil:
		return resp, nil
	case sqlc.ErrNotFound:
		return resp, nil
	default:
		return 0, err
	}
}

func (m *customBlackBoxCaseRefModel) UpdateCountById(ctx context.Context, session sqlx.Session, caseRefId string, count int64) error {
	query := fmt.Sprintf(
		"update %s set `case_data_count` = ? ,`updated_at` = current_timestamp() where `case_ref_id` = ?",
		m.table,
	)
	_, err := m.ExecCtx(
		ctx, func(ctx context.Context, conn sqlx.SqlConn) (sql.Result, error) {
			if session != nil {
				row, err := session.ExecCtx(ctx, query, count, caseRefId)
				return row, err
			}
			row, err := m.conn.ExecCtx(ctx, query, count, caseRefId)
			return row, err
		}, "",
	)
	return err
}

func (m *customBlackBoxCaseRefModel) GetSumRefDataCount(ctx context.Context) (int64, error) {
	type CaseDataRef struct {
		CaseDataCount string `db:"case_data_count"`
	}
	var c CaseDataRef
	// err = m.QueryRowsNoCacheCtx(ctx, &o, query, values...)
	err := m.conn.QueryRowCtx(ctx, &c, "SELECT SUM(case_data_count) as case_data_count FROM `black_box_case_ref`")
	switch err {
	case nil:
		return strconv.ParseInt(c.CaseDataCount, 10, 64)
	default:
		return 0, err
	}
}
