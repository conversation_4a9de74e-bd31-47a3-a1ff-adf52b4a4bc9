// Code generated by goctl. DO NOT EDIT.
// versions:
//  goctl version: 1.7.4

package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlc"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
)

var (
	blackBoxCaseDataTableName           = "`black_box_case_data`"
	blackBoxCaseDataFieldNames          = builder.RawFieldNames(&BlackBoxCaseData{})
	blackBoxCaseDataRows                = strings.Join(blackBoxCaseDataFieldNames, ",")
	blackBoxCaseDataRowsExpectAutoSet   = strings.Join(stringx.Remove(blackBoxCaseDataFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), ",")
	blackBoxCaseDataRowsWithPlaceHolder = strings.Join(stringx.Remove(blackBoxCaseDataFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), "=?,") + "=?"

	cacheArtificialIntelligenceBlackBoxCaseDataIdPrefix         = "cache:artificialIntelligence:blackBoxCaseData:id:"
	cacheArtificialIntelligenceBlackBoxCaseDataCaseDataIdPrefix = "cache:artificialIntelligence:blackBoxCaseData:caseDataId:"
)

type (
	blackBoxCaseDataModel interface {
		Insert(ctx context.Context, session sqlx.Session, data *BlackBoxCaseData) (sql.Result, error)
		FindOne(ctx context.Context, id uint64) (*BlackBoxCaseData, error)
		FindOneByCaseDataId(ctx context.Context, caseDataId string) (*BlackBoxCaseData, error)
		Update(ctx context.Context, session sqlx.Session, data *BlackBoxCaseData) (sql.Result, error)
		Delete(ctx context.Context, session sqlx.Session, id uint64) error
		LogicDelete(ctx context.Context, session sqlx.Session, id uint64) error
	}

	defaultBlackBoxCaseDataModel struct {
		sqlc.CachedConn
		table string
	}

	BlackBoxCaseData struct {
		Id              uint64         `db:"id"`                // 自增ID
		CaseId          sql.NullString `db:"case_id"`           // 用例ID
		RevisionId      string         `db:"revision_id"`       // 版本ID
		CaseDataId      string         `db:"case_data_id"`      // 数据ID
		OrderId         string         `db:"order_id"`          // 序号
		Requirement     string         `db:"requirement"`       // 需求名称
		PreCondition    string         `db:"pre_condition"`     // 前置条件
		CaseStep        string         `db:"case_step"`         // 用例步骤
		ExpectResult    string         `db:"expect_result"`     // 预期结果
		Terminal        string         `db:"terminal"`          // 终端
		CaseLevel       string         `db:"case_level"`        // 用例等级
		Tag             string         `db:"tag"`               // 标识
		Deleted         int64          `db:"deleted"`           // 逻辑删除标识（未删除、已删除）
		CreatedBy       string         `db:"created_by"`        // 创建者的用户ID
		UpdatedBy       string         `db:"updated_by"`        // 最近一次更新者的用户ID
		DeletedBy       sql.NullString `db:"deleted_by"`        // 删除者的用户ID
		CreatedAt       time.Time      `db:"created_at"`        // 创建时间
		UpdatedAt       time.Time      `db:"updated_at"`        // 更新时间
		DeletedAt       sql.NullTime   `db:"deleted_at"`        // 删除时间
		CaseName        string         `db:"case_name"`         // 用例名称
		Adopted         int64          `db:"adopted"`           // 是否被采纳
		UpdatedHumanTmp int64          `db:"updated_human_tmp"` // 修改次数(未采纳)
		UpdatedHuman    int64          `db:"updated_human"`     // 修改次数
		Version         string         `db:"version"`           // 版本(v2、v2-4)
	}
)

func newBlackBoxCaseDataModel(conn sqlx.SqlConn, c cache.CacheConf, opts ...cache.Option) *defaultBlackBoxCaseDataModel {
	return &defaultBlackBoxCaseDataModel{
		CachedConn: sqlc.NewConn(conn, c, opts...),
		table:      "`black_box_case_data`",
	}
}

func (m *defaultBlackBoxCaseDataModel) Delete(ctx context.Context, session sqlx.Session, id uint64) error {
	data, err := m.FindOne(ctx, id)
	if err != nil {
		return err
	}

	artificialIntelligenceBlackBoxCaseDataCaseDataIdKey := fmt.Sprintf("%s%v", cacheArtificialIntelligenceBlackBoxCaseDataCaseDataIdPrefix, data.CaseDataId)
	artificialIntelligenceBlackBoxCaseDataIdKey := fmt.Sprintf("%s%v", cacheArtificialIntelligenceBlackBoxCaseDataIdPrefix, id)
	_, err = m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("delete from %s where `id` = ?", m.table)
		if session != nil {
			return session.ExecCtx(ctx, query, id)
		}
		return conn.ExecCtx(ctx, query, id)
	}, artificialIntelligenceBlackBoxCaseDataCaseDataIdKey, artificialIntelligenceBlackBoxCaseDataIdKey)
	return err
}

func (m *defaultBlackBoxCaseDataModel) LogicDelete(ctx context.Context, session sqlx.Session, id uint64) error {
	data, err := m.FindOne(ctx, id)
	if err != nil {
		return err
	}

	artificialIntelligenceBlackBoxCaseDataCaseDataIdKey := fmt.Sprintf("%s%v", cacheArtificialIntelligenceBlackBoxCaseDataCaseDataIdPrefix, data.CaseDataId)
	artificialIntelligenceBlackBoxCaseDataIdKey := fmt.Sprintf("%s%v", cacheArtificialIntelligenceBlackBoxCaseDataIdPrefix, id)
	_, err = m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set `deleted` = ?, `deleted_at` = ? where `id` = ?", m.table)
		if session != nil {
			return session.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
		}
		return conn.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
	}, artificialIntelligenceBlackBoxCaseDataCaseDataIdKey, artificialIntelligenceBlackBoxCaseDataIdKey)
	return err
}

func (m *defaultBlackBoxCaseDataModel) FindOne(ctx context.Context, id uint64) (*BlackBoxCaseData, error) {
	artificialIntelligenceBlackBoxCaseDataIdKey := fmt.Sprintf("%s%v", cacheArtificialIntelligenceBlackBoxCaseDataIdPrefix, id)
	var resp BlackBoxCaseData
	err := m.QueryRowCtx(ctx, &resp, artificialIntelligenceBlackBoxCaseDataIdKey, func(ctx context.Context, conn sqlx.SqlConn, v any) error {
		query := fmt.Sprintf("select %s from %s where `id` = ? and `deleted` = ? limit 1", blackBoxCaseDataRows, m.table)
		return conn.QueryRowCtx(ctx, v, query, id, constants.NotDeleted)
	})
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultBlackBoxCaseDataModel) FindOneByCaseDataId(ctx context.Context, caseDataId string) (*BlackBoxCaseData, error) {
	artificialIntelligenceBlackBoxCaseDataCaseDataIdKey := fmt.Sprintf("%s%v", cacheArtificialIntelligenceBlackBoxCaseDataCaseDataIdPrefix, caseDataId)
	var resp BlackBoxCaseData
	err := m.QueryRowIndexCtx(ctx, &resp, artificialIntelligenceBlackBoxCaseDataCaseDataIdKey, m.formatPrimary, func(ctx context.Context, conn sqlx.SqlConn, v any) (i any, e error) {
		query := fmt.Sprintf("select %s from %s where `case_data_id` = ? and `deleted` = ? limit 1", blackBoxCaseDataRows, m.table)
		if err := conn.QueryRowCtx(ctx, &resp, query, caseDataId, constants.NotDeleted); err != nil {
			return nil, err
		}
		return resp.Id, nil
	}, m.queryPrimary)
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultBlackBoxCaseDataModel) Insert(ctx context.Context, session sqlx.Session, data *BlackBoxCaseData) (sql.Result, error) {
	artificialIntelligenceBlackBoxCaseDataCaseDataIdKey := fmt.Sprintf("%s%v", cacheArtificialIntelligenceBlackBoxCaseDataCaseDataIdPrefix, data.CaseDataId)
	artificialIntelligenceBlackBoxCaseDataIdKey := fmt.Sprintf("%s%v", cacheArtificialIntelligenceBlackBoxCaseDataIdPrefix, data.Id)

	return m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", m.table, blackBoxCaseDataRowsExpectAutoSet)
		if session != nil {
			return session.ExecCtx(ctx, query, data.CaseId, data.RevisionId, data.CaseDataId, data.OrderId, data.Requirement, data.PreCondition, data.CaseStep, data.ExpectResult, data.Terminal, data.CaseLevel, data.Tag, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy, data.CaseName, data.Adopted, data.UpdatedHumanTmp, data.UpdatedHuman, data.Version)
		}
		return conn.ExecCtx(ctx, query, data.CaseId, data.RevisionId, data.CaseDataId, data.OrderId, data.Requirement, data.PreCondition, data.CaseStep, data.ExpectResult, data.Terminal, data.CaseLevel, data.Tag, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy, data.CaseName, data.Adopted, data.UpdatedHumanTmp, data.UpdatedHuman, data.Version)
	}, artificialIntelligenceBlackBoxCaseDataCaseDataIdKey, artificialIntelligenceBlackBoxCaseDataIdKey)
}

func (m *defaultBlackBoxCaseDataModel) Update(ctx context.Context, session sqlx.Session, newData *BlackBoxCaseData) (sql.Result, error) {
	data, err := m.FindOne(ctx, newData.Id)
	if err != nil {
		return nil, err
	}

	artificialIntelligenceBlackBoxCaseDataCaseDataIdKey := fmt.Sprintf("%s%v", cacheArtificialIntelligenceBlackBoxCaseDataCaseDataIdPrefix, data.CaseDataId)
	artificialIntelligenceBlackBoxCaseDataIdKey := fmt.Sprintf("%s%v", cacheArtificialIntelligenceBlackBoxCaseDataIdPrefix, data.Id)

	return m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, blackBoxCaseDataRowsWithPlaceHolder)
		if session != nil {
			return session.ExecCtx(ctx, query, newData.CaseId, newData.RevisionId, newData.CaseDataId, newData.OrderId, newData.Requirement, newData.PreCondition, newData.CaseStep, newData.ExpectResult, newData.Terminal, newData.CaseLevel, newData.Tag, newData.Deleted, newData.CreatedBy, newData.UpdatedBy, newData.DeletedBy, newData.CaseName, newData.Adopted, newData.UpdatedHumanTmp, newData.UpdatedHuman, newData.Version, newData.Id)
		}
		return conn.ExecCtx(ctx, query, newData.CaseId, newData.RevisionId, newData.CaseDataId, newData.OrderId, newData.Requirement, newData.PreCondition, newData.CaseStep, newData.ExpectResult, newData.Terminal, newData.CaseLevel, newData.Tag, newData.Deleted, newData.CreatedBy, newData.UpdatedBy, newData.DeletedBy, newData.CaseName, newData.Adopted, newData.UpdatedHumanTmp, newData.UpdatedHuman, newData.Version, newData.Id)
	}, artificialIntelligenceBlackBoxCaseDataCaseDataIdKey, artificialIntelligenceBlackBoxCaseDataIdKey)
}

func (m *defaultBlackBoxCaseDataModel) formatPrimary(primary any) string {
	return fmt.Sprintf("%s%v", cacheArtificialIntelligenceBlackBoxCaseDataIdPrefix, primary)
}

func (m *defaultBlackBoxCaseDataModel) queryPrimary(ctx context.Context, conn sqlx.SqlConn, v, primary any) error {
	query := fmt.Sprintf("select %s from %s where `id` = ? limit 1", blackBoxCaseDataRows, m.table)
	return conn.QueryRowCtx(ctx, v, query, primary)
}

func (m *defaultBlackBoxCaseDataModel) tableName() string {
	return m.table
}
