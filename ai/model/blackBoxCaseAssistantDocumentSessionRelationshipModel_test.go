package model

import (
	"context"
	"fmt"
	"strings"
	"sync"
	"testing"
	"time"

	"github.com/RichardKnop/machinery/v2/brokers/errs"
	"github.com/zeromicro/go-zero/core/threading"
)

func TestName(t *testing.T) {
	s := "22222"

	abc(s, "ssss")
}
func abc(str ...string) {
	join := strings.Join(str, ",")
	fmt.Println(join)
}

func Test1(t *testing.T) {
	go funcName()
	time.Sleep(3 * time.Second)
	fmt.Println("Test1" + "结束")
}

func funcName() {
	errors := make(chan error, 4)
	var wg sync.WaitGroup
	ctx := context.Background()
	wg.Add(4)

	threading.GoSafeCtx(
		ctx, func() {
			defer wg.Done()
			time.Sleep(1 * time.Second)
			fmt.Println("GoSafeCtx" + "睡眠结束")
			errors <- errs.ErrConsumerStopped
		},
	)
	threading.GoSafeCtx(
		ctx, func() {
			defer wg.Done()
			// errors <- nil
		},
	)
	threading.GoSafeCtx(
		ctx, func() {
			defer wg.Done()
			errors <- nil
		},
	)
	threading.GoSafeCtx(
		ctx, func() {
			defer wg.Done()
			// errors <- nil
		},
	)
	wg.Wait()
	close(errors)
	defer fmt.Println("funcName" + "结束")
	for {
		select {
		case err, ok := <-errors:
			if !ok {
				fmt.Println("读取结束")
				return
			}
			if err != nil {
				fmt.Println(err)
				// return
			} else {
				fmt.Println(err)
			}
		}
	}
}
