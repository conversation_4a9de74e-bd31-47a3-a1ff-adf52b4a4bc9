package model

import (
	"context"
	"database/sql"
	"fmt"

	"github.com/Masterminds/squirrel"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/types"
)

var (
	_ BlackBoxCaseAssistantModel = (*customBlackBoxCaseAssistantModel)(nil)

	blackBoxCaseAssistantInsertFields = stringx.Remove(
		blackBoxCaseAssistantFieldNames, "`id`", "`create_time`", "`update_time`", "`deleted_by`", "`created_at`",
		"`updated_at`", "`deleted_at`",
	)
)

type (
	// BlackBoxCaseAssistantModel is an interface to be customized, add more methods here,
	// and implement the added methods in customBlackBoxCaseAssistantModel.
	BlackBoxCaseAssistantModel interface {
		blackBoxCaseAssistantModel
		types.DBModel

		Trans(ctx context.Context, fn func(context context.Context, session sqlx.Session) error) error
		InsertBuilder(data *BlackBoxCaseAssistant) squirrel.InsertBuilder
		UpdateBuilder(data *BlackBoxCaseAssistant) squirrel.UpdateBuilder
		SelectBuilder() squirrel.SelectBuilder
		SelectCountBuilder() squirrel.SelectBuilder
		FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error)
		FindNoCacheByQuery(ctx context.Context, selectBuilder squirrel.SelectBuilder) ([]*BlackBoxCaseAssistant, error)
		FindByProjectId(ctx context.Context, projectId string) (
			[]*BlackBoxCaseAssistant, error,
		)
		UpdateDocumentIdsById(
			ctx context.Context, session sqlx.Session, id int64, documentIds string,
		) error
	}

	customBlackBoxCaseAssistantModel struct {
		*defaultBlackBoxCaseAssistantModel

		conn sqlx.SqlConn
	}
)

// NewBlackBoxCaseAssistantModel returns a model for the database table.
func NewBlackBoxCaseAssistantModel(
	conn sqlx.SqlConn, c cache.CacheConf, opts ...cache.Option,
) BlackBoxCaseAssistantModel {
	return &customBlackBoxCaseAssistantModel{
		defaultBlackBoxCaseAssistantModel: newBlackBoxCaseAssistantModel(conn, c, opts...),
		conn:                              conn,
	}
}

func (m *customBlackBoxCaseAssistantModel) Table() string {
	return m.table
}

func (m *customBlackBoxCaseAssistantModel) Fields() []string {
	return blackBoxCaseAssistantFieldNames
}

func (m *customBlackBoxCaseAssistantModel) Trans(
	ctx context.Context, fn func(context context.Context, session sqlx.Session) error,
) error {
	return m.TransactCtx(ctx, fn)
}

func (m *customBlackBoxCaseAssistantModel) InsertBuilder(data *BlackBoxCaseAssistant) squirrel.InsertBuilder {
	// TODO: fill the insert values
	return squirrel.Insert(m.table).Columns(blackBoxCaseAssistantInsertFields...).Values()
}

func (m *customBlackBoxCaseAssistantModel) UpdateBuilder(data *BlackBoxCaseAssistant) squirrel.UpdateBuilder {
	eq := squirrel.Eq{
		// TODO: fill the update values
	}
	return squirrel.Update(m.table).SetMap(eq).Where("`id` = ?", data.Id)
}

func (m *customBlackBoxCaseAssistantModel) SelectBuilder() squirrel.SelectBuilder {
	return squirrel.Select(blackBoxCaseAssistantFieldNames...).Where(
		"`deleted` = ?", constants.NotDeleted,
	).From(m.table)
}

func (m *customBlackBoxCaseAssistantModel) SelectCountBuilder() squirrel.SelectBuilder {
	return squirrel.Select("count(*)").Where("`deleted` = ?", constants.NotDeleted).From(m.table)
}

func (m *customBlackBoxCaseAssistantModel) FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (
	int64, error,
) {
	query, values, err := countBuilder.ToSql()
	if err != nil {
		return 0, err
	}

	var resp int64
	err = m.QueryRowNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return 0, err
	}
}

func (m *customBlackBoxCaseAssistantModel) FindNoCacheByQuery(
	ctx context.Context, selectBuilder squirrel.SelectBuilder,
) ([]*BlackBoxCaseAssistant, error) {
	query, values, err := selectBuilder.ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*BlackBoxCaseAssistant
	err = m.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

func (m *customBlackBoxCaseAssistantModel) FindByProjectId(ctx context.Context, projectId string) (
	[]*BlackBoxCaseAssistant, error,
) {
	sql := m.SelectBuilder().Where("`project_id` = ?", projectId)
	query, values, err := sql.ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*BlackBoxCaseAssistant
	err = m.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

func (l *customBlackBoxCaseAssistantModel) UpdateDocumentIdsById(
	ctx context.Context, session sqlx.Session, id int64, documentIds string,
) error {
	query := fmt.Sprintf(
		"update %s set `document_ids` = ? ,`updated_at` = current_timestamp() where `id` = ? and `deleted` = ?",
		l.table,
	)
	_, err := l.ExecCtx(
		ctx, func(ctx context.Context, conn sqlx.SqlConn) (sql.Result, error) {
			if session != nil {
				row, err := session.ExecCtx(ctx, query, documentIds, id, constants.NotDeleted)
				return row, err
			}
			row, err := l.conn.ExecCtx(ctx, query, documentIds, id, constants.NotDeleted)
			return row, err
		}, "", // todo
	)
	return err
}
