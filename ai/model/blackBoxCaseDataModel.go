package model

import (
	"context"
	"database/sql"
	"fmt"
	"strconv"
	"strings"
	"time"

	"github.com/Masterminds/squirrel"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlc"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/public/util"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/types"
)

var (
	_ BlackBoxCaseDataModel = (*customBlackBoxCaseDataModel)(nil)

	blackBoxCaseDataInsertFields = stringx.Remove(blackBoxCaseDataFieldNames, "`id`", "`create_time`", "`update_time`", "`deleted_by`", "`created_at`", "`updated_at`", "`deleted_at`")
)

type (
	// BlackBoxCaseDataModel is an interface to be customized, add more methods here,
	// and implement the added methods in customBlackBoxCaseDataModel.
	BlackBoxCaseDataModel interface {
		blackBoxCaseDataModel
		types.DBModel

		Trans(ctx context.Context, fn func(context context.Context, session sqlx.Session) error) error
		InsertBuilder(data *BlackBoxCaseData) squirrel.InsertBuilder
		UpdateBuilder(data *BlackBoxCaseData) squirrel.UpdateBuilder
		SelectBuilder() squirrel.SelectBuilder
		SelectCountBuilder() squirrel.SelectBuilder
		FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error)
		FindNoCacheByQuery(ctx context.Context, selectBuilder squirrel.SelectBuilder) ([]*BlackBoxCaseData, error)
		FindByCaseId(ctx context.Context, caseId string) ([]*BlackBoxCaseData, error)
		FindByCaseIds(ctx context.Context, caseIds []string) ([]*BlackBoxCaseData, error)
		FindByRevisionId(ctx context.Context, revisionId string) ([]*BlackBoxCaseData, error)
		CancelKeepDataByIds(ctx context.Context, session sqlx.Session, ids []string) error
		FindMaxOrderIdByRevisionId(ctx context.Context, id string) (int64, error)
		DeleteByDataIds(ctx context.Context, session sqlx.Session, caseDataIds []string) error
		UpdateByObj(ctx context.Context, session sqlx.Session, data *BlackBoxCaseData) error
		FindCountByAdopted(ctx context.Context) (int64, error)
		FindCountByUpdatedAdopted(ctx context.Context) (int64, error)
		AdoptedByRevisionId(ctx context.Context, session sqlx.Session, revisionId string) error
		LogicDeleteAndStatic(ctx context.Context, session sqlx.Session, id uint64) error
		LogicDeleteByRevisionId(ctx context.Context, session sqlx.Session, revisionId string) error
	}

	customBlackBoxCaseDataModel struct {
		*defaultBlackBoxCaseDataModel

		conn sqlx.SqlConn
	}
)

// NewBlackBoxCaseDataModel returns a model for the database table.
func NewBlackBoxCaseDataModel(conn sqlx.SqlConn, c cache.CacheConf, opts ...cache.Option) BlackBoxCaseDataModel {
	return &customBlackBoxCaseDataModel{
		defaultBlackBoxCaseDataModel: newBlackBoxCaseDataModel(conn, c, opts...),
		conn:                         conn,
	}
}

func (m *customBlackBoxCaseDataModel) Table() string {
	return m.table
}

func (m *customBlackBoxCaseDataModel) Fields() []string {
	return blackBoxCaseDataFieldNames
}

func (m *customBlackBoxCaseDataModel) Trans(ctx context.Context, fn func(context context.Context, session sqlx.Session) error) error {
	return m.TransactCtx(ctx, fn)
}

func (m *customBlackBoxCaseDataModel) InsertBuilder(data *BlackBoxCaseData) squirrel.InsertBuilder {
	// TODO: fill the insert values
	return squirrel.Insert(m.table).Columns(blackBoxCaseDataInsertFields...).Values()
}

func (m *customBlackBoxCaseDataModel) UpdateBuilder(data *BlackBoxCaseData) squirrel.UpdateBuilder {
	eq := squirrel.Eq{
		// TODO: fill the update values
	}
	return squirrel.Update(m.table).SetMap(eq).Where("`id` = ?", data.Id)
}

func (m *customBlackBoxCaseDataModel) SelectBuilder() squirrel.SelectBuilder {
	return squirrel.Select(blackBoxCaseDataFieldNames...).Where("`deleted` = ?", constants.NotDeleted).From(m.table)
}

func (m *customBlackBoxCaseDataModel) SelectCountBuilder() squirrel.SelectBuilder {
	return squirrel.Select("count(*)").Where("`deleted` = ?", constants.NotDeleted).From(m.table)
}

func (m *customBlackBoxCaseDataModel) FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error) {
	query, values, err := countBuilder.ToSql()
	if err != nil {
		return 0, err
	}

	var resp int64
	err = m.QueryRowNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return 0, err
	}
}

func (m *customBlackBoxCaseDataModel) FindNoCacheByQuery(ctx context.Context, selectBuilder squirrel.SelectBuilder) ([]*BlackBoxCaseData, error) {
	query, values, err := selectBuilder.ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*BlackBoxCaseData
	err = m.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

func (m *customBlackBoxCaseDataModel) FindByCaseId(ctx context.Context, caseId string) ([]*BlackBoxCaseData, error) {
	sql := squirrel.Select(blackBoxCaseDataFieldNames...).From(m.table).Where("`case_id` = ? and `deleted` = ?", caseId, util.BoolToInt64(false)).OrderBy("CAST(`order_id` AS UNSIGNED) ASC")
	query, values, err := sql.ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*BlackBoxCaseData
	err = m.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

func (m *customBlackBoxCaseDataModel) FindByCaseIds(ctx context.Context, caseIds []string) ([]*BlackBoxCaseData, error) {
	sql := squirrel.Select(blackBoxCaseDataFieldNames...).From(m.table).Where(squirrel.Eq{"case_id": caseIds}).Where("`deleted` = ?", caseIds, util.BoolToInt64(false)).OrderBy("CAST(`order_id` AS UNSIGNED) ASC")
	query, values, err := sql.ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*BlackBoxCaseData
	err = m.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

func (m *customBlackBoxCaseDataModel) FindByRevisionId(ctx context.Context, revisionId string) ([]*BlackBoxCaseData, error) {
	sql := squirrel.Select(blackBoxCaseDataFieldNames...).From(m.table).Where("`revision_id` = ? and `deleted` = ?", revisionId, util.BoolToInt64(false)).OrderBy("CAST(`order_id` AS UNSIGNED) ASC")
	query, values, err := sql.ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*BlackBoxCaseData
	err = m.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

func (m *customBlackBoxCaseDataModel) CancelKeepDataByIds(ctx context.Context, session sqlx.Session, ids []string) error {
	query := fmt.Sprintf(
		"update %s set `case_id` = '' ,`updated_at` = current_timestamp() where `id` in (%s)",
		m.table,
		strings.Repeat("?,", len(ids)-1)+"?",
	)
	var args []interface{}
	for _, id := range ids {
		args = append(args, id)
	}
	_, err := m.ExecCtx(
		ctx, func(ctx context.Context, conn sqlx.SqlConn) (sql.Result, error) {
			if session != nil {
				row, err := session.ExecCtx(ctx, query, args...)
				return row, err
			}
			row, err := m.conn.ExecCtx(ctx, query, args...)
			return row, err
		}, "",
	)
	return err
}

func (m *customBlackBoxCaseDataModel) FindMaxOrderIdByRevisionId(ctx context.Context, id string) (int64, error) {
	type OrderResp struct {
		OrderId string `db:"order_id"`
	}
	var o OrderResp
	// err = m.QueryRowsNoCacheCtx(ctx, &o, query, values...)
	err := m.conn.QueryRowCtx(ctx, &o, "SELECT MAX(CAST(`order_id` AS UNSIGNED)) as order_id FROM `black_box_case_data` WHERE `revision_id` = ?", id)
	switch err {
	case nil:
		maxId, _ := strconv.ParseInt(o.OrderId, 10, 64)
		return maxId, nil
	default:
		return 0, err
	}
}

func (m *customBlackBoxCaseDataModel) DeleteByDataIds(ctx context.Context, session sqlx.Session, caseDataIds []string) error {
	query := fmt.Sprintf(
		"delete from %s where `case_data_id` in (%s)",
		m.table,
		strings.Repeat("?,", len(caseDataIds)-1)+"?",
	)
	var args []interface{}
	for _, id := range caseDataIds {
		args = append(args, id)
	}
	_, err := m.ExecCtx(
		ctx, func(ctx context.Context, conn sqlx.SqlConn) (sql.Result, error) {
			if session != nil {
				row, err := session.ExecCtx(ctx, query, args...)
				return row, err
			}
			row, err := m.conn.ExecCtx(ctx, query, args...)
			return row, err
		}, "",
	)
	return err
}

func (m *customBlackBoxCaseDataModel) UpdateByObj(ctx context.Context, session sqlx.Session, data *BlackBoxCaseData) error {
	query := fmt.Sprintf(
		"update %s set `order_id` = ? ,`updated_at` = current_timestamp() where `case_data_id` = ?",
		m.table,
	)
	_, err := m.ExecCtx(
		ctx, func(ctx context.Context, conn sqlx.SqlConn) (sql.Result, error) {
			if session != nil {
				row, err := session.ExecCtx(ctx, query, data.OrderId, data.CaseDataId)
				return row, err
			}
			row, err := m.conn.ExecCtx(ctx, query, data.OrderId, data.CaseDataId)
			return row, err
		}, "",
	)
	return err
}

func (m *customBlackBoxCaseDataModel) FindCountByAdopted(ctx context.Context) (int64, error) {
	sql := m.SelectCountBuilder()
	sql = sql.Where("`adopted` = ?", 1)
	resp, err := m.FindCount(ctx, sql)
	switch err {
	case nil:
		return resp, nil
	case sqlc.ErrNotFound:
		return resp, nil
	default:
		return 0, err
	}
}

func (m *customBlackBoxCaseDataModel) FindCountByUpdatedAdopted(ctx context.Context) (int64, error) {
	sql := m.SelectCountBuilder()
	sql = sql.Where("`adopted` = ? and `updated_human` = ?", 1, 1)
	resp, err := m.FindCount(ctx, sql)
	switch err {
	case nil:
		return resp, nil
	case sqlc.ErrNotFound:
		return resp, nil
	default:
		return 0, err
	}
}

func (m *customBlackBoxCaseDataModel) AdoptedByRevisionId(ctx context.Context, session sqlx.Session, revisionId string) error {
	query := fmt.Sprintf(
		"update %s set `adopted` = ?, `updated_human` = `updated_human_tmp` where `revision_id` = ?",
		m.table,
	)
	_, err := m.ExecCtx(
		ctx, func(ctx context.Context, conn sqlx.SqlConn) (sql.Result, error) {
			if session != nil {
				row, err := session.ExecCtx(ctx, query, 1, revisionId)
				return row, err
			}
			row, err := m.conn.ExecCtx(ctx, query, 1, revisionId)
			return row, err
		}, "",
	)
	return err
}

func (m *defaultBlackBoxCaseDataModel) LogicDeleteAndStatic(ctx context.Context, session sqlx.Session, id uint64) error {
	data, err := m.FindOne(ctx, id)
	if err != nil {
		return err
	}

	artificialIntelligenceBlackBoxCaseDataCaseDataIdKey := fmt.Sprintf("%s%v", cacheArtificialIntelligenceBlackBoxCaseDataCaseDataIdPrefix, data.CaseDataId)
	artificialIntelligenceBlackBoxCaseDataIdKey := fmt.Sprintf("%s%v", cacheArtificialIntelligenceBlackBoxCaseDataIdPrefix, id)
	_, err = m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set `updated_human_tmp` = ?, `deleted` = ?, `deleted_at` = ? where `id` = ?", m.table)
		if session != nil {
			return session.ExecCtx(ctx, query, util.BoolToInt64(true), constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
		}
		return conn.ExecCtx(ctx, query, util.BoolToInt64(true), constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
	}, artificialIntelligenceBlackBoxCaseDataCaseDataIdKey, artificialIntelligenceBlackBoxCaseDataIdKey)
	return err
}

func (m *defaultBlackBoxCaseDataModel) LogicDeleteByRevisionId(ctx context.Context, session sqlx.Session, revisionId string) error {
	_, err := m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set `deleted` = ?, `deleted_at` = ? where `revision_id` = ?", m.table)
		if session != nil {
			return session.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, revisionId)
		}
		return conn.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, revisionId)
	})
	return err
}
