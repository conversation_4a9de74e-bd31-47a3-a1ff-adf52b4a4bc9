// Code generated by goctl. DO NOT EDIT.
// versions:
//  goctl version: 1.7.4

package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlc"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
)

var (
	blackBoxCaseSessionTableName           = "`black_box_case_session`"
	blackBoxCaseSessionFieldNames          = builder.RawFieldNames(&BlackBoxCaseSession{})
	blackBoxCaseSessionRows                = strings.Join(blackBoxCaseSessionFieldNames, ",")
	blackBoxCaseSessionRowsExpectAutoSet   = strings.Join(stringx.Remove(blackBoxCaseSessionFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), ",")
	blackBoxCaseSessionRowsWithPlaceHolder = strings.Join(stringx.Remove(blackBoxCaseSessionFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), "=?,") + "=?"

	cacheArtificialIntelligenceBlackBoxCaseSessionIdPrefix                            = "cache:artificialIntelligence:blackBoxCaseSession:id:"
	cacheArtificialIntelligenceBlackBoxCaseSessionProjectIdAssistantIdSessionIdPrefix = "cache:artificialIntelligence:blackBoxCaseSession:projectId:assistantId:sessionId:"
)

type (
	blackBoxCaseSessionModel interface {
		Insert(ctx context.Context, session sqlx.Session, data *BlackBoxCaseSession) (sql.Result, error)
		FindOne(ctx context.Context, id int64) (*BlackBoxCaseSession, error)
		FindOneByProjectIdAssistantIdSessionId(ctx context.Context, projectId string, assistantId string, sessionId string) (*BlackBoxCaseSession, error)
		Update(ctx context.Context, session sqlx.Session, data *BlackBoxCaseSession) (sql.Result, error)
		Delete(ctx context.Context, session sqlx.Session, id int64) error
		LogicDelete(ctx context.Context, session sqlx.Session, id int64) error
	}

	defaultBlackBoxCaseSessionModel struct {
		sqlc.CachedConn
		table string
	}

	BlackBoxCaseSession struct {
		Id                 int64          `db:"id"`                  // 自增ID
		ProjectId          string         `db:"project_id"`          // 项目ID
		AssistantId        string         `db:"assistant_id"`        // 助手ID
		SessionId          string         `db:"session_id"`          // 会话ID
		ExternalSessionId  sql.NullString `db:"external_session_id"` // 外部会话ID
		SessionName        string         `db:"session_name"`        // 会话名称
		SessionDescription sql.NullString `db:"session_description"` // 会话描述
		SessionRound       int64          `db:"session_round"`       // 对话轮数[继承助手配置轮数]
		Account            sql.NullString `db:"account"`             // 用户id【仅在个人空间存在此数据】
		DocumentIds        sql.NullString `db:"document_ids"`        // 文档集合[继承助手文档的子集]
		Status             int64          `db:"status"`              // 会话状态：使用中：0，归档：1
		Deleted            int64          `db:"deleted"`             // 逻辑删除标识（未删除、已删除）
		CreatedBy          string         `db:"created_by"`          // 创建者的用户ID
		UpdatedBy          string         `db:"updated_by"`          // 最近一次更新者的用户ID
		DeletedBy          sql.NullString `db:"deleted_by"`          // 删除者的用户ID
		CreatedAt          time.Time      `db:"created_at"`          // 创建时间
		UpdatedAt          time.Time      `db:"updated_at"`          // 更新时间
		DeletedAt          sql.NullTime   `db:"deleted_at"`          // 删除时间
	}
)

func newBlackBoxCaseSessionModel(conn sqlx.SqlConn, c cache.CacheConf, opts ...cache.Option) *defaultBlackBoxCaseSessionModel {
	return &defaultBlackBoxCaseSessionModel{
		CachedConn: sqlc.NewConn(conn, c, opts...),
		table:      "`black_box_case_session`",
	}
}

func (m *defaultBlackBoxCaseSessionModel) Delete(ctx context.Context, session sqlx.Session, id int64) error {
	data, err := m.FindOne(ctx, id)
	if err != nil {
		return err
	}

	artificialIntelligenceBlackBoxCaseSessionIdKey := fmt.Sprintf("%s%v", cacheArtificialIntelligenceBlackBoxCaseSessionIdPrefix, id)
	artificialIntelligenceBlackBoxCaseSessionProjectIdAssistantIdSessionIdKey := fmt.Sprintf("%s%v:%v:%v", cacheArtificialIntelligenceBlackBoxCaseSessionProjectIdAssistantIdSessionIdPrefix, data.ProjectId, data.AssistantId, data.SessionId)
	_, err = m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("delete from %s where `id` = ?", m.table)
		if session != nil {
			return session.ExecCtx(ctx, query, id)
		}
		return conn.ExecCtx(ctx, query, id)
	}, artificialIntelligenceBlackBoxCaseSessionIdKey, artificialIntelligenceBlackBoxCaseSessionProjectIdAssistantIdSessionIdKey)
	return err
}

func (m *defaultBlackBoxCaseSessionModel) LogicDelete(ctx context.Context, session sqlx.Session, id int64) error {
	data, err := m.FindOne(ctx, id)
	if err != nil {
		return err
	}

	artificialIntelligenceBlackBoxCaseSessionIdKey := fmt.Sprintf("%s%v", cacheArtificialIntelligenceBlackBoxCaseSessionIdPrefix, id)
	artificialIntelligenceBlackBoxCaseSessionProjectIdAssistantIdSessionIdKey := fmt.Sprintf("%s%v:%v:%v", cacheArtificialIntelligenceBlackBoxCaseSessionProjectIdAssistantIdSessionIdPrefix, data.ProjectId, data.AssistantId, data.SessionId)
	_, err = m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set `deleted` = ?, `deleted_at` = ? where `id` = ?", m.table)
		if session != nil {
			return session.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
		}
		return conn.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
	}, artificialIntelligenceBlackBoxCaseSessionIdKey, artificialIntelligenceBlackBoxCaseSessionProjectIdAssistantIdSessionIdKey)
	return err
}

func (m *defaultBlackBoxCaseSessionModel) FindOne(ctx context.Context, id int64) (*BlackBoxCaseSession, error) {
	artificialIntelligenceBlackBoxCaseSessionIdKey := fmt.Sprintf("%s%v", cacheArtificialIntelligenceBlackBoxCaseSessionIdPrefix, id)
	var resp BlackBoxCaseSession
	err := m.QueryRowCtx(ctx, &resp, artificialIntelligenceBlackBoxCaseSessionIdKey, func(ctx context.Context, conn sqlx.SqlConn, v any) error {
		query := fmt.Sprintf("select %s from %s where `id` = ? and `deleted` = ? limit 1", blackBoxCaseSessionRows, m.table)
		return conn.QueryRowCtx(ctx, v, query, id, constants.NotDeleted)
	})
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultBlackBoxCaseSessionModel) FindOneByProjectIdAssistantIdSessionId(ctx context.Context, projectId string, assistantId string, sessionId string) (*BlackBoxCaseSession, error) {
	artificialIntelligenceBlackBoxCaseSessionProjectIdAssistantIdSessionIdKey := fmt.Sprintf("%s%v:%v:%v", cacheArtificialIntelligenceBlackBoxCaseSessionProjectIdAssistantIdSessionIdPrefix, projectId, assistantId, sessionId)
	var resp BlackBoxCaseSession
	err := m.QueryRowIndexCtx(ctx, &resp, artificialIntelligenceBlackBoxCaseSessionProjectIdAssistantIdSessionIdKey, m.formatPrimary, func(ctx context.Context, conn sqlx.SqlConn, v any) (i any, e error) {
		query := fmt.Sprintf("select %s from %s where `project_id` = ? and `assistant_id` = ? and `session_id` = ? and `deleted` = ? limit 1", blackBoxCaseSessionRows, m.table)
		if err := conn.QueryRowCtx(ctx, &resp, query, projectId, assistantId, sessionId, constants.NotDeleted); err != nil {
			return nil, err
		}
		return resp.Id, nil
	}, m.queryPrimary)
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultBlackBoxCaseSessionModel) Insert(ctx context.Context, session sqlx.Session, data *BlackBoxCaseSession) (sql.Result, error) {
	artificialIntelligenceBlackBoxCaseSessionIdKey := fmt.Sprintf("%s%v", cacheArtificialIntelligenceBlackBoxCaseSessionIdPrefix, data.Id)
	artificialIntelligenceBlackBoxCaseSessionProjectIdAssistantIdSessionIdKey := fmt.Sprintf("%s%v:%v:%v", cacheArtificialIntelligenceBlackBoxCaseSessionProjectIdAssistantIdSessionIdPrefix, data.ProjectId, data.AssistantId, data.SessionId)

	return m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", m.table, blackBoxCaseSessionRowsExpectAutoSet)
		if session != nil {
			return session.ExecCtx(ctx, query, data.ProjectId, data.AssistantId, data.SessionId, data.ExternalSessionId, data.SessionName, data.SessionDescription, data.SessionRound, data.Account, data.DocumentIds, data.Status, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy)
		}
		return conn.ExecCtx(ctx, query, data.ProjectId, data.AssistantId, data.SessionId, data.ExternalSessionId, data.SessionName, data.SessionDescription, data.SessionRound, data.Account, data.DocumentIds, data.Status, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy)
	}, artificialIntelligenceBlackBoxCaseSessionIdKey, artificialIntelligenceBlackBoxCaseSessionProjectIdAssistantIdSessionIdKey)
}

func (m *defaultBlackBoxCaseSessionModel) Update(ctx context.Context, session sqlx.Session, newData *BlackBoxCaseSession) (sql.Result, error) {
	data, err := m.FindOne(ctx, newData.Id)
	if err != nil {
		return nil, err
	}

	artificialIntelligenceBlackBoxCaseSessionIdKey := fmt.Sprintf("%s%v", cacheArtificialIntelligenceBlackBoxCaseSessionIdPrefix, data.Id)
	artificialIntelligenceBlackBoxCaseSessionProjectIdAssistantIdSessionIdKey := fmt.Sprintf("%s%v:%v:%v", cacheArtificialIntelligenceBlackBoxCaseSessionProjectIdAssistantIdSessionIdPrefix, data.ProjectId, data.AssistantId, data.SessionId)

	return m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, blackBoxCaseSessionRowsWithPlaceHolder)
		if session != nil {
			return session.ExecCtx(ctx, query, newData.ProjectId, newData.AssistantId, newData.SessionId, newData.ExternalSessionId, newData.SessionName, newData.SessionDescription, newData.SessionRound, newData.Account, newData.DocumentIds, newData.Status, newData.Deleted, newData.CreatedBy, newData.UpdatedBy, newData.DeletedBy, newData.Id)
		}
		return conn.ExecCtx(ctx, query, newData.ProjectId, newData.AssistantId, newData.SessionId, newData.ExternalSessionId, newData.SessionName, newData.SessionDescription, newData.SessionRound, newData.Account, newData.DocumentIds, newData.Status, newData.Deleted, newData.CreatedBy, newData.UpdatedBy, newData.DeletedBy, newData.Id)
	}, artificialIntelligenceBlackBoxCaseSessionIdKey, artificialIntelligenceBlackBoxCaseSessionProjectIdAssistantIdSessionIdKey)
}

func (m *defaultBlackBoxCaseSessionModel) formatPrimary(primary any) string {
	return fmt.Sprintf("%s%v", cacheArtificialIntelligenceBlackBoxCaseSessionIdPrefix, primary)
}

func (m *defaultBlackBoxCaseSessionModel) queryPrimary(ctx context.Context, conn sqlx.SqlConn, v, primary any) error {
	query := fmt.Sprintf("select %s from %s where `id` = ? limit 1", blackBoxCaseSessionRows, m.table)
	return conn.QueryRowCtx(ctx, v, query, primary)
}

func (m *defaultBlackBoxCaseSessionModel) tableName() string {
	return m.table
}
