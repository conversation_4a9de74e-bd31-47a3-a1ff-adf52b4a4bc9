package tools

import (
	"context"
	"time"

	"github.com/zeromicro/go-zero/core/jsonx"
	"github.com/zeromicro/go-zero/core/logx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sse"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/model"
)

func HandleSseStream(ctx context.Context, stream *sse.Stream, fn func() (result any, err error)) {
	stopChan := make(chan struct{})
	stopTimeCh := make(chan struct{})

	go func() {
		defer func() {
			if err := recover(); err != nil {
				logx.Error(err)
			}
		}()
		respData := model.SseMessage{
			Code:    0,
			Message: "",
			Data:    nil,
		}
		errChan := make(chan error)

		go func() {
			defer func() {
				if err := recover(); err != nil {
					logx.Error(err)
				}
				close(err<PERSON>han)
			}()

			// init
			stream.Event <- &sse.Event{
				ID:   []byte(stream.ID),
				Data: jsonx.MarshalIgnoreError(respData),
			}

			result, err := fn()
			if err != nil {
				errChan <- err
				return
			}
			respData.Data = result
			stream.Event <- &sse.Event{
				ID:   []byte(stream.ID),
				Data: jsonx.MarshalIgnoreError(respData),
			}
			stopChan <- struct{}{}
		}()

		// 在select外层添加
		ticker := time.NewTicker(10 * time.Second)
		defer ticker.Stop()

		for {
			select {
			case <-ctx.Done():
				logx.Error("客户端断开...")
				stopTimeCh <- struct{}{}
			case err := <-errChan:
				if err != nil {
					logx.Error("流异常:", err)
					stream.Event <- &sse.Event{
						ID:   []byte(stream.ID),
						Data: jsonx.MarshalIgnoreError(model.HandlerSseError(err)),
					}
					stream.Quit()
					return
				}
			case <-stopChan:
				stream.Quit()
				return
			case <-ticker.C:
				emptyData := model.SseMessage{
					Code:    0,
					Message: "",
					Data:    nil,
				}
				stream.Event <- &sse.Event{
					ID:   []byte(stream.ID),
					Data: jsonx.MarshalIgnoreError(emptyData),
				}
			}
		}
	}()
}
