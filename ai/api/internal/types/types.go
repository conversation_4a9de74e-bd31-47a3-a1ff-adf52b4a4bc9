// Code generated by goctl. DO NOT EDIT.
// goctl 1.7.5

package types

type AdoptBlackBoxCaseRevisionDataReq struct {
	RevisionId string `json:"revision_id" zh:"版本Id"`
}

type AdoptBlackBoxCaseRevisionDataResp struct {
}

type AppendBlackBoxCaseDataReq struct {
	RevisionId string                  `json:"revision_id,optional" zh:"版本Id"`
	Items      []*BlackBoxCaseDataBase `json:"items"`
}

type AppendBlackBoxCaseDataResp struct {
	Items    []*BlackBoxCaseData `json:"items"`
	NewItems []*BlackBoxCaseData `json:"new_items"`
}

type BatchDeleteBlackBoxCaseDocumentForAssistantReq struct {
	ProjectId   string   `json:"project_id" validate:"required" zh:"项目Id"`
	AssistantId string   `json:"assistant_id" validate:"required" zh:"助手Id"`
	DocumentIds []string `json:"document_ids" zh:"文档Id集合"`
}

type BatchDeleteBlackBoxCaseDocumentForAssistantResp struct {
}

type BatchDeleteBlackBoxCaseDocumentForSessionReq struct {
	ProjectId   string   `json:"project_id" validate:"required" zh:"项目Id"`
	AssistantId string   `json:"assistant_id" validate:"required" zh:"助手Id"`
	SessionId   string   `json:"session_id" validate:"required" zh:"会话Id"`
	DocumentIds []string `json:"document_ids" zh:"文档Id集合"`
}

type BatchDeleteBlackBoxCaseDocumentForSessionResp struct {
}

type BatchUpdateBlackBoxCaseDataReq struct {
	Items []*UpdateBlackBoxCaseDataReq `json:"items"`
}

type BatchUpdateBlackBoxCaseDataResp struct {
}

type Between struct {
	Start string `json:"start"`
	End   string `json:"end"`
}

type BlackBoxCase struct {
	Id                          int                    `json:"id" zh:"自增Id"`
	DirId                       string                 `json:"dir_id" zh:"目录Id"`
	CaseId                      string                 `json:"case_id" zh:"用例Id"`
	CaseName                    string                 `json:"case_name" zh:"用例名称"`
	CaseContinueToWrite         int64                  `json:"case_continue_to_write" zh:"用例续写次数"`
	CaseModelCharacter          string                 `json:"case_model_character" zh:"用例模型性格"`
	CaseRemarks                 string                 `json:"case_remarks" zh:"用例备注"`
	KnowledgeId                 string                 `json:"knowledge_id" zh:"知识文档ID"`
	KnowledgeName               string                 `json:"knowledge_name" zh:"知识文档ID"`
	KnowledgeContent            string                 `json:"knowledge_content" zh:"知识文档ID"`
	KnowledgeParagraphTitleId   int64                  `json:"knowledge_paragraph_title_id" zh:"知识文档ID"`
	EnableReference             bool                   `json:"enable_reference" zh:"是否关联文档"`
	KnowledgeFixSugg            string                 `json:"knowledge_fix_sugg" zh:"是否关联文档"`
	ReferenceDoc                *ReferenceDoc          `json:"reference_doc" zh:"关联文档"`
	CreatedBy                   *FullUserInfo          `json:"created_by" zh:"创建者的用户Id"`
	UpdatedBy                   *FullUserInfo          `json:"updated_by" zh:"最近一次更新者的用户Id"`
	DeletedBy                   *FullUserInfo          `json:"deleted_by" zh:"删除者的用户Id"`
	CreatedAt                   int64                  `json:"created_at" zh:"创建时间"`
	UpdatedAt                   int64                  `json:"updated_at" zh:"更新时间"`
	DeletedAt                   int64                  `json:"deleted_at" zh:"删除时间"`
	KnowledgeParagraphTitleText string                 `json:"knowledge_paragraph_title_text,optional,omitempty" zh:"知识文档段落标题"`
	KnowledgeDocPgTitle         []*KnowledgeDocPgTitle `json:"knowledge_paragraph_title,optional,omitempty" zh:"新版知识文档段落标题"`
}

type BlackBoxCaseAssistant struct {
	Id                       int                     `json:"id" zh:"自增Id"`
	ProjectId                string                  `json:"project_id" zh:"项目Id"`
	AssistantId              string                  `json:"assistant_id" zh:"助手Id"`
	AssistantName            string                  `json:"assistant_name" zh:"助手名称"`
	AssistantDescription     string                  `json:"assistant_description" zh:"助手描述"`
	Account                  string                  `json:"account" zh:"用户id【仅在个人空间存在此数据】"`
	WorkSpaceType            int8                    `json:"work_space_type" zh:"工作空间类型：个人空间(private)：0，公共空间()：1"`
	WorkPriorityType         int8                    `json:"work_priority_type" zh:"工作优先级类型：准确优先：0，速度优先：1"`
	SessionRound             int                     `json:"session_round" zh:"对话轮数"`
	BlackBoxCaseDocumentList []*BlackBoxCaseDocument `json:"black_box_case_document_list" zh:"文档集合"`
	CreatedBy                *FullUserInfo           `json:"created_by" zh:"创建者的用户Id"`
	UpdatedBy                *FullUserInfo           `json:"updated_by" zh:"最近一次更新者的用户Id"`
	DeletedBy                *FullUserInfo           `json:"deleted_by" zh:"删除者的用户Id"`
	CreatedAt                int64                   `json:"created_at" zh:"创建时间"`
	UpdatedAt                int64                   `json:"updated_at" zh:"更新时间"`
	DeletedAt                int64                   `json:"deleted_at" zh:"删除时间"`
}

type BlackBoxCaseBaseMap struct {
	Id       string                `json:"id" zh:"Id"`
	Data     Data                  `json:"data" zh:"项目Id"`
	Children []BlackBoxCaseBaseMap `json:"children" zh:"子节点"`
}

type BlackBoxCaseData struct {
	Id           int           `json:"id" zh:"自增Id"`
	CaseId       string        `json:"case_id,optional" zh:"用例Id"`
	RevisionId   string        `json:"revision_id" zh:"版本Id"`
	CaseDataId   string        `json:"case_data_id" zh:"知识文档Id"`
	OrderId      string        `json:"order_id,optional" zh:"序号"`
	CaseName     string        `json:"case_name,optional" zh:"用例名称"`
	Requirement  string        `json:"requirement,optional" zh:"需求名称"`
	PreCondition string        `json:"pre_condition,optional" zh:"前置条件"`
	CaseStep     string        `json:"case_step,optional" zh:"用例步骤"`
	ExpectResult string        `json:"expect_result,optional" zh:"预期结果"`
	Terminal     string        `json:"terminal,optional" zh:"终端"`
	CaseLevel    string        `json:"case_level,optional" zh:"用例等级"`
	Tag          string        `json:"tag,optional" zh:"标识"`
	IsKeep       bool          `json:"is_keep,optional" zh:"是否保留"`
	CreatedBy    *FullUserInfo `json:"created_by,optional" zh:"创建者的用户Id"`
	UpdatedBy    *FullUserInfo `json:"updated_by,optional" zh:"最近一次更新者的用户Id"`
	DeletedBy    *FullUserInfo `json:"deleted_by,optional" zh:"删除者的用户Id"`
	CreatedAt    int64         `json:"created_at,optional" zh:"创建时间"`
	UpdatedAt    int64         `json:"updated_at,optional" zh:"更新时间"`
	DeletedAt    int64         `json:"deleted_at,optional" zh:"删除时间"`
}

type BlackBoxCaseDataBase struct {
	CaseId       string `json:"case_id,optional" zh:"用例Id"`
	RevisionId   string `json:"revision_id,optional" zh:"版本Id"`
	CaseDataId   string `json:"case_data_id" zh:"知识文档Id"`
	OrderId      string `json:"order_id,optional" zh:"序号"`
	CaseName     string `json:"case_name,optional" zh:"用例名称"`
	Requirement  string `json:"requirement,optional" zh:"需求名称"`
	PreCondition string `json:"pre_condition,optional" zh:"前置条件"`
	CaseStep     string `json:"case_step,optional" zh:"用例步骤"`
	ExpectResult string `json:"expect_result,optional" zh:"预期结果"`
	Terminal     string `json:"terminal,optional" zh:"终端"`
	CaseLevel    string `json:"case_level,optional" zh:"用例等级"`
	Tag          string `json:"tag,optional" zh:"标识"`
}

type BlackBoxCaseDir struct {
	Id             int           `json:"id" zh:"自增Id"`
	ProjectId      string        `json:"project_id" zh:"项目Id"`
	DirId          string        `json:"dir_id" zh:"目录Id"`
	DirName        string        `json:"dir_name" zh:"目录名称"`
	DirDescription string        `json:"dir_description" zh:"目录名称"`
	CreatedBy      *FullUserInfo `json:"created_by" zh:"创建者的用户Id"`
	UpdatedBy      *FullUserInfo `json:"updated_by" zh:"最近一次更新者的用户Id"`
	DeletedBy      *FullUserInfo `json:"deleted_by" zh:"删除者的用户Id"`
	CreatedAt      int64         `json:"created_at" zh:"创建时间"`
	UpdatedAt      int64         `json:"updated_at" zh:"更新时间"`
	DeletedAt      int64         `json:"deleted_at" zh:"删除时间"`
}

type BlackBoxCaseDocument struct {
	Id                  int           `json:"id" zh:"自增Id"`
	ProjectId           string        `json:"project_id" zh:"项目Id"`
	AssistantId         string        `json:"assistant_id" zh:"助手Id"`
	DocumentId          string        `json:"document_id" zh:"文档Id"`
	DocumentName        string        `json:"document_name" zh:"文档名称"`
	DocumentDescription string        `json:"document_description,omitempty" zh:"文档描述"`
	DocumentUrl         string        `json:"document_url,omitempty" zh:"文档地址"`
	DocumentText        string        `json:"document_text,omitempty" zh:"文档文本"`
	DocumentType        int8          `json:"document_type" zh:"文档类型：纯文本(text)：0，飞书(feishu)：1，doc(doc)：2"`
	ExternalDocumentId  string        `json:"external_document_id,omitempty" zh:"外部文档Id"`
	Status              int8          `json:"status" zh:"文档状态：处理中：0，完成：1，失败：2"`
	CreatedBy           *FullUserInfo `json:"created_by" zh:"创建者的用户Id"`
	UpdatedBy           *FullUserInfo `json:"updated_by" zh:"最近一次更新者的用户Id"`
	DeletedBy           *FullUserInfo `json:"deleted_by,omitempty" zh:"删除者的用户Id"`
	CreatedAt           int64         `json:"created_at" zh:"创建时间"`
	UpdatedAt           int64         `json:"updated_at" zh:"更新时间"`
	DeletedAt           int64         `json:"deleted_at" zh:"删除时间"`
}

type BlackBoxCaseKnowledge struct {
	Id                      int           `json:"id" zh:"自增Id"`
	DirId                   string        `json:"dir_id" zh:"目录Id"`
	KnowledgeId             string        `json:"knowledge_id" zh:"知识文档Id"`
	KnowledgeName           string        `json:"knowledge_name" zh:"文档名称"`
	KnowledgeType           string        `json:"knowledge_type" zh:"文档类型"`
	KnowledgeContent        string        `json:"knowledge_content" zh:"文档内容"`
	KnowledgeStatus         string        `json:"knowledge_status" zh:"文档状态"`
	KnowledgeParagraphTitle string        `json:"knowledge_paragraph_title" zh:"文档标题"`
	KnowledgeErrorMsg       string        `json:"knowledge_error_msg" zh:"文档标题"`
	CreatedBy               *FullUserInfo `json:"created_by" zh:"创建者的用户Id"`
	UpdatedBy               *FullUserInfo `json:"updated_by" zh:"最近一次更新者的用户Id"`
	DeletedBy               *FullUserInfo `json:"deleted_by" zh:"删除者的用户Id"`
	CreatedAt               int64         `json:"created_at" zh:"创建时间"`
	UpdatedAt               int64         `json:"updated_at" zh:"更新时间"`
	DeletedAt               int64         `json:"deleted_at" zh:"删除时间"`
}

type BlackBoxCaseKnowledgeExperience struct {
	Id                 string    `json:"id" validate:"required" zh:"测试经验Id"`
	ProjectId          string    `json:"project_id" zh:"项目Id"`
	Project            string    `json:"project" zh:"项目名称"`
	Category           string    `json:"category" zh:"经验分类"`
	Tags               []*string `json:"tags" zh:"经验标签"`
	TestExperience     string    `json:"test_experience" zh:"经验名称"`
	NormalFocusPoint   string    `json:"normal_focus_point" zh:"正常场景"`
	AbnormalFocusPoint string    `json:"abnormal_focus_point" zh:"异常场景"`
}

type BlackBoxCaseKnowledgeSupplementDoc struct {
	Type     string   `json:"type,optional" zh:"类型"`
	Feishu   FeishuV2 `json:"feishu,optional" zh:"飞书"`
	Markdown Markdown `json:"markdown,optional" zh:"Markdown"`
}

type BlackBoxCaseMapNode struct {
	ID       string                  `json:"id" validate:"required" zh:"节点Id"`
	Data     BlackBoxCaseMapNodeData `json:"data" validate:"required" zh:"节点数据"`
	Children []*BlackBoxCaseMapNode  `json:"children" zh:"子节点内容"`
}

type BlackBoxCaseMapNodeData struct {
	Text string `json:"text" validate:"required" zh:"节点内容"`
}

type BlackBoxCaseMapSupplementRequest struct {
	ProjectId                 string                             `json:"project_id" zh:"项目Id"`
	DirId                     string                             `json:"dir_id" zh:"目录Id"`
	CaseId                    string                             `json:"case_id" validate:"required" zh:"用例Id"`
	KnowledgeSupplementEnable bool                               `json:"knowledge_supplement_enable" zh:"是否开启补充文档"`
	KnowledgeSupplementDoc    BlackBoxCaseKnowledgeSupplementDoc `json:"knowledge_supplement_doc" zh:"知识补充文档"`
	FunctionPoint             string                             `json:"function_point" validate:"required" zh:"功能点"`
	GenerateOpinion           string                             `json:"generate_opinion" zh:"生成意见"`
}

type BlackBoxCaseReferenceContent struct {
	ContentType string   `json:"content_type" zh:"内容类型：文本：text，文档：docß"`
	DocHeaders  []string `json:"doc_headers" zh:"文档标题列表"`
	DocId       string   `json:"doc_id" zh:"文档id"`
	Text        string   `json:"text" zh:"文本内容"`
	TextHeader  string   `json:"text_header" zh:"文本标题"`
}

type BlackBoxCaseRevision struct {
	Id                          int           `json:"id" zh:"自增Id"`
	CaseId                      string        `json:"case_id" zh:"用例Id"`
	RevisionId                  string        `json:"revision_id" zh:"版本Id"`
	RevisionName                string        `json:"revision_name" zh:"版本名称"`
	KnowledgeId                 string        `json:"knowledge_id" zh:"知识文档关联ID"`
	KnowledgeParagraphTitleText string        `json:"knowledge_paragraph_title_text" zh:"知识文档段落标题"`
	KnowledgeParagraphTitle     []string      `json:"knowledge_paragraph_title" zh:"知识文档段落标题列表"`
	CaseRefId                   string        `json:"case_ref_id" zh:"用例关联ID"`
	CreatedBy                   *FullUserInfo `json:"created_by" zh:"创建者的用户ID"`
	UpdatedBy                   *FullUserInfo `json:"updated_by" zh:"最近一次更新者的用户ID"`
	DeletedBy                   *FullUserInfo `json:"deleted_by" zh:"删除者的用户ID"`
	CreatedAt                   int64         `json:"created_at" zh:"创建时间"`
	UpdatedAt                   int64         `json:"updated_at" zh:"更新时间"`
	DeletedAt                   int64         `json:"deleted_at" zh:"删除时间"`
}

type BlackBoxCaseSession struct {
	Id                 int           `json:"id" zh:"自增Id"`
	ProjectId          string        `json:"project_id" zh:"项目Id"`
	AssistantId        string        `json:"assistant_id" zh:"助手Id"`
	SessionId          string        `json:"session_id" zh:"会话Id"`
	SessionName        string        `json:"session_name" zh:"会话名称"`
	SessionDescription string        `json:"session_description,omitempty" zh:"会话描述"`
	SessionRound       int           `json:"session_round" zh:"对话轮数[继承会话配置轮数]"`
	DocumentIds        string        `json:"document_ids" zh:"文档集合[继承会话文档的子集]"`
	Status             int8          `json:"status" zh:"会话状态：使用中：0，归档：1"`
	CreatedBy          *FullUserInfo `json:"created_by" zh:"创建者的用户Id"`
	UpdatedBy          *FullUserInfo `json:"updated_by" zh:"最近一次更新者的用户Id"`
	DeletedBy          *FullUserInfo `json:"deleted_by,omitempty" zh:"删除者的用户Id"`
	CreatedAt          int64         `json:"created_at" zh:"创建时间"`
	UpdatedAt          int64         `json:"updated_at" zh:"更新时间"`
	DeletedAt          int64         `json:"deleted_at,omitempty" zh:"删除时间"`
}

type BlackBoxCaseSessionConversationState struct {
	State     string `json:"state" zh:"state"`
	StateDesc string `json:"state_desc" zh:"state_desc"`
}

type BlackBoxCaseSessionMessage struct {
	Content  BlackBoxCaseSessionMsgContent  `json:"content" zh:"消息内容"`
	Metadata BlackBoxCaseSessionMsgMetadata `json:"metadata" zh:"消息内容"`
}

type BlackBoxCaseSessionMsgContent struct {
	Human string `json:"human" zh:"human"`
	Ai    string `json:"ai" zh:"ai"`
}

type BlackBoxCaseSessionMsgMetadata struct {
	ContentType    string                               `json:"content_type" zh:"内容类型 table, text"`
	SessionId      string                               `json:"session_id" zh:"会话Id"`
	CreatedAt      string                               `json:"created_at" zh:"创建时间"`
	ConversationId string                               `json:"conversation_id" zh:"对话id"`
	State          BlackBoxCaseSessionConversationState `json:"state" zh:"状态"`
}

type BlackBoxCaseTwBeta struct {
	Id                   int           `json:"id" zh:"自增Id"`
	BetaCaseId           string        `json:"beta_case_id" zh:"BETA用例ID"`
	BetaCaseName         string        `json:"beta_case_name" zh:"BETA用例名称"`
	RevisionId           string        `json:"revision_id" zh:"版本Id"`
	CaseContent          string        `json:"case_content" zh:"原始用例结果"`
	DocumentName         string        `json:"document_name" zh:"文档名称"`
	DocumentUrl          string        `json:"document_url" zh:"文档URL"`
	DocumentChapterTitle string        `json:"document_chapter_title" zh:"章节标题"`
	DocumentContent      string        `json:"document_content" zh:"选中文档内容"`
	ReferenceDoc         string        `json:"reference_doc" zh:"补充文档内容"`
	KnowledgeFixSugg     string        `json:"knowledge_fix_sugg" zh:"修改意见"`
	CaseRefId            string        `json:"case_ref_id" zh:"用例关联ID"`
	Deleted              int64         `json:"deleted" zh:"逻辑删除标识（未删除、已删除）"`
	CreatedBy            *FullUserInfo `json:"created_by" zh:"创建者的用户ID"`
	UpdatedBy            *FullUserInfo `json:"updated_by" zh:"最近一次更新者的用户ID"`
	DeletedBy            *FullUserInfo `json:"deleted_by" zh:"删除者的用户ID"`
	CreatedAt            int64         `json:"created_at" zh:"创建时间"`
	UpdatedAt            int64         `json:"updated_at" zh:"更新时间"`
	DeletedAt            int64         `json:"deleted_at" zh:"删除时间"`
}

type BlackBoxCaseTwBetaMind struct {
	Id            int64                          `json:"id" zh:"id"`
	ProductModule string                         `json:"product_module" zh:"产品模块"`
	AcCheckList   []*BlackBoxCaseTwBetaMindCheck `json:"ac_check_list" zh:"ac_check_list"`
	TcCheckList   []*BlackBoxCaseTwBetaMindCheck `json:"tc_check_list" zh:"tc_check_list"`
	Status        string                         `json:"status" zh:"状态 succeed, failed, processing"`
	CreatedAt     int64                          `json:"created_at" zh:"创建时间"`
}

type BlackBoxCaseTwBetaMindCheck struct {
	UseCase    string `json:"use_case" zh:"use_case"`
	CheckPoint string `json:"check_point" zh:"check_point"`
}

type BlackBoxKnowledgeExperience struct {
	Id                 string                              `json:"id" validate:"required" zh:"经验Id"`
	ProjectId          string                              `json:"project_id" validate:"required" zh:"项目Id"`
	Category           BlackBoxKnowledgeExperienceCategory `json:"category" zh:"经验类型"`
	TestExperience     string                              `json:"test_experience" zh:"经验名称"`
	NormalFocusPoint   string                              `json:"normal_focus_point" zh:"正常经验"`
	AbnormalFocusPoint string                              `json:"abnormal_focus_point" zh:"异常经验"`
	Tags               []*BlackBoxKnowledgeTag             `json:"tags"`
}

type BlackBoxKnowledgeExperienceCategory struct {
	Id       int    `json:"id" validate:"required" zh:"场景类型Id"`
	TypeName string `json:"type_name" validate:"required" zh:"场景类型名称"`
}

type BlackBoxKnowledgeTag struct {
	Id  int    `json:"id" validate:"required" zh:"标签Id"`
	Tag string `json:"tag" validate:"required" zh:"标签名称"`
}

type BlackBoxKnowledgeTerm struct {
	Id            string                  `json:"id" validate:"required" zh:"术语Id"`
	ProjectId     string                  `json:"project_id" validate:"required" zh:"项目Id"`
	Tags          []*BlackBoxKnowledgeTag `json:"tags"`
	TermInChinese string                  `json:"term_in_chinese" validate:"required" zh:"术语名称"`
	TermMeaning   string                  `json:"term_meaning" validate:"required" zh:"术语含义"`
}

type ClearKeepBlackBoxCaseDataReq struct {
	CaseId string `json:"case_id" zh:"用例Id"`
}

type ClearKeepBlackBoxCaseDataResp struct {
}

type Condition struct {
	Single *SingleCondition `json:"single,omitempty,optional"`
	Group  *GroupCondition  `json:"group,omitempty,optional"`
}

type CreateBlackBoxCaseAssistantReq struct {
	ProjectId            string `json:"project_id" validate:"required" zh:"项目Id"`
	AssistantName        string `json:"assistant_name" validate:"required" zh:"助手名称"`
	AssistantDescription string `json:"assistant_description" zh:"助手描述"`
	WorkSpaceType        int8   `json:"work_space_type,optional" validate:"omitempty,oneof=0 1" zh:"工作空间类型：个人空间(private)：0，公共空间()：1"`
	WorkPriorityType     int8   `json:"work_priority_type,optional" validate:"omitempty,oneof=0 1" zh:"工作优先级类型：准确优先：0，速度优先：1"`
	SessionRound         int    `json:"session_round" validate:"required" zh:"对话轮数"`
}

type CreateBlackBoxCaseAssistantResp struct {
	ProjectId   string `json:"project_id" zh:"项目Id"`
	AssistantId string `json:"assistant_id" zh:"助手Id"`
}

type CreateBlackBoxCaseData struct {
	RevisionId   string `json:"revision_id" zh:"版本Id"`
	OrderId      string `json:"order_id,optional" zh:"序号"`
	CaseName     string `json:"case_name,optional" zh:"用例名称"`
	Requirement  string `json:"requirement,optional" zh:"需求名称"`
	PreCondition string `json:"pre_condition,optional" zh:"前置条件"`
	CaseStep     string `json:"case_step,optional" zh:"用例步骤"`
	ExpectResult string `json:"expect_result,optional" zh:"预期结果"`
	Terminal     string `json:"terminal,optional" zh:"终端"`
	CaseLevel    string `json:"case_level,optional" zh:"用例等级"`
	Tag          string `json:"tag,optional" zh:"标识"`
}

type CreateBlackBoxCaseDataReq struct {
	Items []*CreateBlackBoxCaseData `json:"items"`
}

type CreateBlackBoxCaseDataResp struct {
}

type CreateBlackBoxCaseDataV2Req struct {
	ProjectId  string  `json:"project_id" zh:"项目Id"`
	DirId      string  `json:"dir_id" zh:"目录Id"`
	CaseId     string  `json:"case_id" validate:"required" zh:"用例Id"`
	RevisionId *string `json:"revision_id,optional" zh:"版本Id"`
}

type CreateBlackBoxCaseDataV2WithSseReq struct {
	ProjectId  string  `json:"project_id" zh:"项目Id"`
	DirId      string  `json:"dir_id" zh:"目录Id"`
	CaseId     string  `json:"case_id" validate:"required" zh:"用例Id"`
	RevisionId *string `json:"revision_id,optional" zh:"版本Id"`
}

type CreateBlackBoxCaseDataV2WithSseResp struct {
}

type CreateBlackBoxCaseDirReq struct {
	ProjectId string `json:"project_id" validate:"required" zh:"项目Id"`
	DirName   string `json:"dir_name" validate:"required" zh:"目录名称"`
}

type CreateBlackBoxCaseDirResp struct {
	DirId string `json:"dir_id" zh:"目录Id"`
}

type CreateBlackBoxCaseDocumentForAssistant struct {
	DocumentName        string `json:"document_name" validate:"required" zh:"文档名称"`
	DocumentDescription string `json:"document_description,omitempty" zh:"文档描述"`
	DocumentUrl         string `json:"document_url,omitempty" zh:"文档地址"`
	DocumentText        string `json:"document_text,omitempty" zh:"文档文本"`
	DocumentType        int8   `json:"document_type" validate:"required" zh:"文档类型：纯文本(text)：0，飞书(feishu)：1，doc(doc)：2"`
}

type CreateBlackBoxCaseDocumentForAssistantReq struct {
	ProjectId   string                                    `json:"project_id" validate:"required" zh:"项目Id"`
	AssistantId string                                    `json:"assistant_id" validate:"required" zh:"助手Id"`
	Items       []*CreateBlackBoxCaseDocumentForAssistant `json:"items"`
}

type CreateBlackBoxCaseDocumentForAssistantResp struct {
	ProjectId      string   `json:"project_id" zh:"项目Id"`
	AssistantId    string   `json:"assistant_id" zh:"助手Id"`
	DocumentIdList []string `json:"document_id_list" zh:"文档Id"`
}

type CreateBlackBoxCaseDocumentForSessionRecvReq struct {
	ProjectId       string `json:"project_id" validate:"required" zh:"项目Id"`
	AssistantId     string `json:"assistant_id" validate:"required" zh:"助手Id"`
	SessionId       string `json:"session_id" validate:"required" zh:"会话Id"`
	MsgDocumentText string `json:"msg_document_text" validate:"required" zh:"消息文档文本"`
}

type CreateBlackBoxCaseDocumentForSessionRecvResp struct {
	ProjectId   string `json:"project_id" zh:"项目Id"`
	AssistantId string `json:"assistant_id" zh:"助手Id"`
	DocumentId  string `json:"document_id" zh:"文档Id"`
}

type CreateBlackBoxCaseDocumentForSessionReq struct {
	ProjectId   string `json:"project_id" validate:"required" zh:"项目Id"`
	AssistantId string `json:"assistant_id" validate:"required" zh:"助手Id"`
	SessionId   string `json:"session_id" validate:"required" zh:"会话Id"`
	DocumentId  string `json:"document_id" validate:"required" zh:"文档Id"`
}

type CreateBlackBoxCaseDocumentForSessionResp struct {
}

type CreateBlackBoxCaseKnowledgeReq struct {
	DirId            string `json:"dir_id" validate:"required" zh:"目录Id"`
	KnowledgeName    string `json:"knowledge_name" zh:"文档名称"`
	KnowledgeType    string `json:"knowledge_type" zh:"文档类型"`
	KnowledgeContent string `json:"knowledge_content" zh:"文档内容"`
}

type CreateBlackBoxCaseKnowledgeResp struct {
	KnowledgeId string `json:"knowledge_id" zh:"目录Id"`
}

type CreateBlackBoxCaseMapExperienceWithSseResp struct {
}

type CreateBlackBoxCaseMapFunctionWithSseResp struct {
}

type CreateBlackBoxCaseMapReq struct {
	ProjectId string `json:"project_id" zh:"项目Id"`
	DirId     string `json:"dir_id" zh:"目录Id"`
	CaseId    string `json:"case_id" validate:"required" zh:"用例Id"`
}

type CreateBlackBoxCaseMapWithSseReq struct {
	ProjectId string `json:"project_id" zh:"项目Id"`
	DirId     string `json:"dir_id" zh:"目录Id"`
	CaseId    string `json:"case_id" validate:"required" zh:"用例Id"`
}

type CreateBlackBoxCaseMapWithSseResp struct {
}

type CreateBlackBoxCaseModifyTestCaseReferenceContentReq struct {
	ProjectId        string                          `json:"project_id" validate:"required" zh:"项目Id"`
	AssistantId      string                          `json:"assistant_id" validate:"required" zh:"助手Id"`
	SessionId        string                          `json:"session_id" validate:"required" zh:"会话Id"`
	ConversationId   string                          `json:"conversation_id" validate:"required" zh:"对话 id"`
	ReferenceContent []*BlackBoxCaseReferenceContent `json:"reference_content" zh:"关联文档"`
}

type CreateBlackBoxCaseModifyTestCaseReferenceContentResp struct {
}

type CreateBlackBoxCaseReq struct {
	DirId                     string `json:"dir_id" validate:"required" zh:"目录Id"`
	CaseName                  string `json:"case_name" zh:"用例名称"`
	CaseContinueToWrite       int64  `json:"case_continue_to_write" zh:"用例续写次数"`
	CaseModelCharacter        string `json:"case_model_character" zh:"用例模型性格"`
	CaseRemarks               string `json:"case_remarks,optional" zh:"用例备注"`
	KnowledgeId               string `json:"knowledge_id" zh:"知识文档ID"`
	KnowledgeParagraphTitleId int64  `json:"knowledge_paragraph_title_id,optional"`
}

type CreateBlackBoxCaseResp struct {
	CaseId string `json:"case_id" zh:"用例Id"`
}

type CreateBlackBoxCaseRevisionDataReq struct {
	CaseId string                  `json:"case_id" zh:"用例Id"`
	Items  []*BlackBoxCaseDataBase `json:"items"`
}

type CreateBlackBoxCaseRevisionDataResp struct {
	RevisionId string `json:"revision_id" zh:"版本Id"`
}

type CreateBlackBoxCaseSessionReq struct {
	ProjectId          string `json:"project_id" zh:"项目Id"`
	AssistantId        string `json:"assistant_id" zh:"助手Id"`
	SessionName        string `json:"session_name" zh:"会话名称"`
	SessionDescription string `json:"session_description,omitempty" zh:"会话描述"`
}

type CreateBlackBoxCaseSessionResp struct {
	ProjectId   string `json:"project_id" zh:"项目Id"`
	AssistantId string `json:"assistant_id" zh:"助手Id"`
	SessionId   string `json:"session_id" zh:"会话Id"`
}

type CreateBlackBoxCaseTwBetaMindReq struct {
	BetaCaseId    string                         `json:"beta_case_id" zh:"BETA用例ID"`
	ProductModule string                         `json:"product_module" zh:"产品模块"`
	AcCheckList   []*BlackBoxCaseTwBetaMindCheck `json:"ac_check_list" zh:"ac_check_list"`
	TcCheckList   []*BlackBoxCaseTwBetaMindCheck `json:"tc_check_list" zh:"tc_check_list"`
}

type CreateBlackBoxCaseTwBetaMindResp struct {
}

type CreateBlackBoxCaseTwBetaReq struct {
	MindId    int64  `json:"mind_id" zh:"脑图ID"`
	CaseRefId string `json:"case_ref_id" zh:"BETA用例ID"`
}

type CreateBlackBoxCaseTwBetaResp struct {
}

type CreateBlackBoxKnowledgeExperienceCategoryReq struct {
	ProjectId string `json:"project_id" validate:"required" zh:"项目Id"`
	TypeName  string `json:"type_name" validate:"required" zh:"经验类型名称"`
}

type CreateBlackBoxKnowledgeExperienceCategoryResp struct {
}

type CreateBlackBoxKnowledgeExperienceReq struct {
	ProjectId          string                              `json:"project_id" validate:"required" zh:"项目Id"`
	Category           BlackBoxKnowledgeExperienceCategory `json:"category" validate:"required" zh:"经验类型"`
	TestExperience     string                              `json:"test_experience" validate:"required" zh:"经验名称"`
	NormalFocusPoint   string                              `json:"normal_focus_point" validate:"required" zh:"正常经验"`
	AbnormalFocusPoint string                              `json:"abnormal_focus_point" validate:"required" zh:"异常经验"`
	Tags               []*BlackBoxKnowledgeTag             `json:"tags"`
}

type CreateBlackBoxKnowledgeExperienceResp struct {
}

type CreateBlackBoxKnowledgeProjectReq struct {
	ProjectId string `json:"project_id" validate:"required" zh:"项目Id"`
	Name      string `json:"name" validate:"required" zh:"项目名称"`
}

type CreateBlackBoxKnowledgeProjectResp struct {
}

type CreateBlackBoxKnowledgeTagReq struct {
	ProjectId string `json:"project_id" validate:"required" zh:"项目Id"`
	Tag       string `json:"tag" validate:"required" zh:"标签名称"`
}

type CreateBlackBoxKnowledgeTagResp struct {
}

type CreateBlackBoxKnowledgeTermReq struct {
	ProjectId     string                  `json:"project_id" validate:"required" zh:"项目Id"`
	Tags          []*BlackBoxKnowledgeTag `json:"tags"`
	TermInChinese string                  `json:"term_in_chinese" validate:"required" zh:"术语名称"`
	TermMeaning   string                  `json:"term_meaning" validate:"required" zh:"术语含义"`
}

type CreateBlackBoxKnowledgeTermResp struct {
}

type Data struct {
	Text string `json:"text" zh:"标题内容"`
}

type DeleteBlackBoxCaseAssistantReq struct {
	ProjectId   string `form:"project_id" validate:"required" zh:"项目Id"`
	AssistantId string `form:"assistant_id" validate:"required" zh:"助手Id"`
}

type DeleteBlackBoxCaseAssistantResp struct {
}

type DeleteBlackBoxCaseDataReq struct {
	CaseDataId string `form:"case_data_id" zh:"知识文档Id"`
}

type DeleteBlackBoxCaseDataResp struct {
}

type DeleteBlackBoxCaseDirReq struct {
	ProjectId string `json:"project_id" validate:"required" zh:"项目Id"`
	DirId     string `json:"dir_id" validate:"required" zh:"目录Id"`
}

type DeleteBlackBoxCaseDirResp struct {
}

type DeleteBlackBoxCaseDocumentForAssistantReq struct {
	ProjectId   string `json:"project_id" validate:"required" zh:"项目Id"`
	AssistantId string `json:"assistant_id" validate:"required" zh:"助手Id"`
	DocumentId  string `json:"document_id" zh:"文档Id"`
}

type DeleteBlackBoxCaseDocumentForAssistantResp struct {
}

type DeleteBlackBoxCaseDocumentForSessionReq struct {
	ProjectId   string `form:"project_id" validate:"required" zh:"项目Id"`
	AssistantId string `form:"assistant_id" validate:"required" zh:"助手Id"`
	SessionId   string `form:"session_id" validate:"required" zh:"会话Id"`
	DocumentId  string `form:"document_id" zh:"文档Id集合"`
}

type DeleteBlackBoxCaseDocumentForSessionResp struct {
}

type DeleteBlackBoxCaseKnowledgeReq struct {
	KnowledgeId string `json:"knowledge_id" validate:"required" zh:"文档ID"`
}

type DeleteBlackBoxCaseKnowledgeResp struct {
}

type DeleteBlackBoxCaseMapDocumentReq struct {
	ProjectId string `json:"project_id" zh:"项目Id"`
	DirId     string `json:"dir_id" zh:"目录Id"`
	CaseId    string `json:"case_id" validate:"required" zh:"用例Id"`
	FuncId    string `json:"func_id" validate:"required" zh:"功能点Id"`
}

type DeleteBlackBoxCaseMapDocumentResp struct {
}

type DeleteBlackBoxCaseReq struct {
	DirId  string `json:"dir_id" validate:"required" zh:"目录Id"`
	CaseId string `json:"case_id" validate:"required" zh:"用例Id"`
}

type DeleteBlackBoxCaseResp struct {
}

type DeleteBlackBoxCaseSessionReq struct {
	ProjectId   string `form:"project_id" validate:"required" zh:"项目Id"`
	AssistantId string `form:"assistant_id" validate:"required" zh:"助手Id"`
	SessionId   string `form:"session_id" validate:"required" zh:"会话Id"`
}

type DeleteBlackBoxCaseSessionResp struct {
}

type DeleteBlackBoxCaseTwBetaReq struct {
	BetaCaseId string `json:"beta_case_id" zh:"BETA用例ID"`
}

type DeleteBlackBoxCaseTwBetaResp struct {
}

type DeleteBlackBoxKnowledgeExperienceCategoryReq struct {
	Id int `json:"id" validate:"required" zh:"经验类型Id"`
}

type DeleteBlackBoxKnowledgeExperienceCategoryResp struct {
}

type DeleteBlackBoxKnowledgeExperienceReq struct {
	Id string `json:"id" validate:"required" zh:"经验Id"`
}

type DeleteBlackBoxKnowledgeExperienceResp struct {
}

type DeleteBlackBoxKnowledgeTagReq struct {
	Id int `json:"id" validate:"required" zh:"标签Id"`
}

type DeleteBlackBoxKnowledgeTagResp struct {
}

type DeleteBlackBoxKnowledgeTermReq struct {
	Id string `json:"id" validate:"required" zh:"术语Id"`
}

type DeleteBlackBoxKnowledgeTermResp struct {
}

type EditBlackBoxCaseDataReq struct {
	CaseId                      string       `json:"case_id" zh:"用例Id"`
	RevisionId                  string       `json:"revision_id" zh:"版本Id"`
	KnowledgeId                 string       `json:"knowledge_id" zh:"知识文档Id"`
	KnowledgeParagraphTitleText string       `json:"knowledge_paragraph_title_text" zh:"文档使用标题"`
	KnowledgeParagraphTitleId   int64        `json:"knowledge_paragraph_title_id" zh:"文档使用标题id"`
	KnowledgeFixSugg            string       `json:"knowledge_fix_sugg" zh:"修改意见"`
	EnableReference             bool         `json:"enable_reference" zh:"是否启用参考文档"`
	ReferenceDoc                ReferenceDoc `json:"reference_doc"`
}

type EditBlackBoxCaseDataResp struct {
}

type Feishu struct {
	KnowledgeId                string                        `json:"knowledge_id,optional" zh:"知识文档Id"`
	KnowledgeParagraphTitle    []*string                     `json:"knowledge_paragraph_title,optional" zh:"文档使用标题"`
	KnowledgeParagraphTitleMap []*KnowledgeParagraphTitleMap `json:"knowledge_paragraph_title_map,optional" zh:"文档使用标题"`
}

type FeishuV2 struct {
	KnowledgeDocId      string                    `json:"knowledge_doc_id,optional" zh:"知识文档Id"`
	KnowledgeDocPgTitle []*KnowledgeDocPgTitleMap `json:"knowledge_doc_pg_title" zh:"文档使用标题"`
}

type FullUserInfo struct {
	Account      string `json:"account"`
	Fullname     string `json:"fullname"`
	DeptId       string `json:"dept_id"`
	DeptName     string `json:"dept_name"`
	FullDeptName string `json:"full_dept_name"`
	Email        string `json:"email"`
	Mobile       string `json:"mobile"`
	Photo        string `json:"photo"`
	Enabled      bool   `json:"enabled"`
}

type GenerateBlackBoxCaseDataReq struct {
	CaseId                      string `json:"case_id" zh:"用例Id"`
	KnowledgeId                 string `json:"knowledge_id" zh:"知识文档Id"`
	KnowledgeParagraphTitleText string `json:"knowledge_paragraph_title_text" zh:"文档使用标题"`
	KnowledgeParagraphTitleID   int64  `json:"knowledge_paragraph_title_id" zh:"文档使用标题id"`
}

type GenerateBlackBoxCaseDataResp struct {
}

type GetBlackBoxCaseAIStateReq struct {
	CaseRefId string `form:"case_ref_id" zh:"版本ID"`
}

type GetBlackBoxCaseAIStateResp struct {
	State          string `json:"state" zh:"状态"`
	StateDesc      string `json:"state_desc" zh:"状态描述"`
	ExtractContent string `json:"extract_content" zh:"内容"`
}

type GetBlackBoxCaseAssistantReq struct {
	ProjectId   string `form:"project_id" validate:"required" zh:"项目Id"`
	AssistantId string `form:"assistant_id" validate:"required" zh:"助手Id"`
}

type GetBlackBoxCaseAssistantResp struct {
	Item *BlackBoxCaseAssistant `json:"item"`
}

type GetBlackBoxCaseConversationAIMessageReq struct {
	ProjectId      string `form:"project_id" validate:"required" zh:"项目Id"`
	AssistantId    string `form:"assistant_id" validate:"required" zh:"助手Id"`
	SessionId      string `form:"session_id" validate:"required" zh:"会话Id"`
	ConversationId string `form:"conversation_id" validate:"required" zh:"对话 id"`
}

type GetBlackBoxCaseConversationAIMessageResp struct {
	State     string `json:"state" zh:"状态"`
	StateDesc string `json:"state_desc" zh:"状态描述"`
	Message   string `json:"message" zh:"文档内容"`
}

type GetBlackBoxCaseConversationAIStateReq struct {
	ProjectId      string `form:"project_id" validate:"required" zh:"项目Id"`
	AssistantId    string `form:"assistant_id" validate:"required" zh:"助手Id"`
	SessionId      string `form:"session_id" validate:"required" zh:"会话Id"`
	ConversationId string `form:"conversation_id" validate:"required" zh:"对话 id"`
}

type GetBlackBoxCaseConversationAIStateResp struct {
	State     string `json:"state" zh:"状态"`
	StateDesc string `json:"state_desc" zh:"状态描述"`
}

type GetBlackBoxCaseDataCoverageReq struct {
	ProjectId  string  `form:"project_id" zh:"项目Id"`
	DirId      string  `form:"dir_id" zh:"目录Id"`
	CaseId     string  `form:"case_id" validate:"required" zh:"用例Id"`
	RevisionId *string `form:"revision_id,optional" zh:"版本Id"`
}

type GetBlackBoxCaseDataCoverageResp struct {
	Coverage int64 `json:"coverage,optional" zh:"覆盖率"`
}

type GetBlackBoxCaseDataMapReq struct {
	ProjectId      string `form:"project_id" zh:"项目Id"`
	DirId          string `form:"dir_id" zh:"目录Id"`
	CaseId         string `form:"case_id" zh:"用例Id"`
	RevisionId     string `form:"revision_id" validate:"required" zh:"版本Id"`
	MindType       string `form:"mind_type" validate:"required" zh:"导图类型"`
	ParagraphTitle string `form:"paragraph_title" zh:"导图标题"`
}

type GetBlackBoxCaseDataMapResp struct {
}

type GetBlackBoxCaseDataProgressReq struct {
	ProjectId string `form:"project_id" zh:"项目Id"`
	DirId     string `form:"dir_id" zh:"目录Id"`
	CaseId    string `form:"case_id" validate:"required" zh:"用例Id"`
}

type GetBlackBoxCaseDataProgressResp struct {
}

type GetBlackBoxCaseDataRevisionReq struct {
	ProjectId  string `form:"project_id" zh:"项目Id"`
	DirId      string `form:"dir_id" zh:"目录Id"`
	CaseId     string `form:"case_id" zh:"用例Id"`
	RevisionId string `form:"revision_id" validate:"required" zh:"版本Id"`
}

type GetBlackBoxCaseDirReq struct {
	ProjectId string `form:"project_id" validate:"required" zh:"项目Id"`
	DirId     string `form:"dir_id" validate:"required" zh:"目录Id"`
}

type GetBlackBoxCaseDirResp struct {
	Item *BlackBoxCaseDir `json:"item"`
}

type GetBlackBoxCaseDocumentHeadersListReq struct {
	ProjectId   string `form:"project_id" validate:"required" zh:"项目Id"`
	AssistantId string `form:"assistant_id" validate:"required" zh:"助手Id"`
	SessionId   string `form:"session_id" validate:"required" zh:"会话Id"`
	DocumentId  string `form:"document_id" validate:"required" zh:"文档Id"`
}

type GetBlackBoxCaseDocumentHeadersListResp struct {
	Items []string `json:"items" zh:"文档标题集合"`
}

type GetBlackBoxCaseDocumentReq struct {
	ProjectId   string `form:"project_id" validate:"required" zh:"项目Id"`
	AssistantId string `form:"assistant_id" validate:"required" zh:"助手Id"`
	DocumentId  string `form:"document_id" validate:"required"  zh:"文档Id"`
}

type GetBlackBoxCaseDocumentResp struct {
	Item *BlackBoxCaseDocument `json:"item"`
}

type GetBlackBoxCaseKnowledgeReq struct {
	KnowledgeId string `form:"knowledge_id" validate:"required" zh:"文档ID"`
}

type GetBlackBoxCaseKnowledgeResp struct {
	Item *BlackBoxCaseKnowledge `json:"item"`
}

type GetBlackBoxCaseKnowledgeTitleReq struct {
	KnowledgeId string `form:"knowledge_id" validate:"required" zh:"文档ID"`
}

type GetBlackBoxCaseKnowledgeTitleResp struct {
	Data interface{} `json:"data" zh:"返回结果"`
}

type GetBlackBoxCaseKnowledgeV2Req struct {
	ProjectId string `form:"project_id" zh:"项目Id"`
	DirId     string `form:"dir_id" zh:"目录Id"`
	CaseId    string `form:"case_id" validate:"required" zh:"用例Id"`
}

type GetBlackBoxCaseKnowledgeV2Resp struct {
	Tags        []*string                          `json:"tags" zh:"术语标签"`
	Experiences []*BlackBoxCaseKnowledgeExperience `json:"experiences" zh:"测试经验"`
}

type GetBlackBoxCaseMapDocumentReq struct {
	ProjectId string `form:"project_id" zh:"项目Id"`
	DirId     string `form:"dir_id" zh:"目录Id"`
	CaseId    string `form:"case_id" validate:"required" zh:"用例Id"`
	FuncId    string `form:"func_id" validate:"required" zh:"功能点Id"`
}

type GetBlackBoxCaseMapDocumentResp struct {
	FuncDoc *BlackBoxCaseKnowledgeSupplementDoc `json:"func_doc,optional" zh:"生成功能点文档"`
	ExpeDoc *BlackBoxCaseKnowledgeSupplementDoc `json:"expe_doc,optional" zh:"生成测试经验文档"`
}

type GetBlackBoxCaseMapKnowledgeReq struct {
	ProjectId string `form:"project_id" zh:"项目Id"`
	DirId     string `form:"dir_id" zh:"目录Id"`
	CaseId    string `form:"case_id" validate:"required" zh:"用例Id"`
}

type GetBlackBoxCaseMapKnowledgeResp struct {
}

type GetBlackBoxCaseMapProgressReq struct {
	ProjectId string `form:"project_id" zh:"项目Id"`
	DirId     string `form:"dir_id" zh:"目录Id"`
	CaseId    string `form:"case_id" validate:"required" zh:"用例Id"`
}

type GetBlackBoxCaseMapProgressResp struct {
}

type GetBlackBoxCaseMapReq struct {
	ProjectId string `form:"project_id" zh:"项目Id"`
	DirId     string `form:"dir_id" zh:"目录Id"`
	CaseId    string `form:"case_id" validate:"required" zh:"用例Id"`
}

type GetBlackBoxCaseModifyTestCaseReferenceContentListReq struct {
	ProjectId      string `form:"project_id" validate:"required" zh:"项目Id"`
	AssistantId    string `form:"assistant_id" validate:"required" zh:"助手Id"`
	SessionId      string `form:"session_id" validate:"required" zh:"会话Id"`
	ConversationId string `form:"conversation_id" validate:"required" zh:"对话 id"`
}

type GetBlackBoxCaseModifyTestCaseReferenceContentListResp struct {
	ModifySuggestion string                          `json:"modify_suggestion" zh:"修改意见"`
	ReferenceContent []*BlackBoxCaseReferenceContent `json:"reference_content" zh:"关联文档"`
}

type GetBlackBoxCaseRefJsonReq struct {
	CaseRefId string `form:"case_ref_id" zh:"版本ID"`
}

type GetBlackBoxCaseRefResp struct {
	Items []*BlackBoxCaseData `json:"items"`
}

type GetBlackBoxCaseReq struct {
	DirId  string `form:"dir_id" validate:"required" zh:"目录Id"`
	CaseId string `form:"case_id" validate:"required" zh:"用例Id"`
}

type GetBlackBoxCaseResp struct {
	Item *BlackBoxCase `json:"item"`
}

type GetBlackBoxCaseSessionHistoryMessageListReq struct {
	ProjectId    string `form:"project_id" validate:"required" zh:"项目Id"`
	AssistantId  string `form:"assistant_id" validate:"required" zh:"助手Id"`
	SessionId    string `form:"session_id" validate:"required" zh:"会话Id"`
	SessionRound int8   `form:"session_round" validate:"required" zh:"对话轮数"`
}

type GetBlackBoxCaseSessionHistoryMessageListResp struct {
	Messages []*BlackBoxCaseSessionMessage `json:"messages"`
}

type GetBlackBoxCaseSessionReq struct {
	ProjectId   string `form:"project_id" validate:"required" zh:"项目Id"`
	AssistantId string `form:"assistant_id" validate:"required" zh:"助手Id"`
	SessionId   string `form:"session_id" validate:"required" zh:"会话Id"`
}

type GetBlackBoxCaseSessionResp struct {
	Item *BlackBoxCaseSession `json:"item"`
}

type GetBlackBoxCaseTwBetaContentReq struct {
	MindId int64 `json:"mind_id" zh:"脑图ID"`
}

type GetBlackBoxCaseTwBetaContentResp struct {
	Data string `json:"data" zh:"用例内容"`
}

type GetBlackBoxCaseTwBetaMindContentReq struct {
	Id       int64  `json:"id" zh:"脑图ID"`
	MindType string `json:"mind_type" zh:"脑图类型"`
}

type GetBlackBoxCaseTwBetaMindContentResp struct {
	Data string `json:"data" zh:"脑图内容"`
}

type GetCreateBlackBoxCaseAIMessageReq struct {
	ProjectId      string `form:"project_id" validate:"required" zh:"项目Id"`
	AssistantId    string `form:"assistant_id" validate:"required" zh:"助手Id"`
	SessionId      string `form:"session_id" validate:"required" zh:"会话Id"`
	ConversationId string `form:"conversation_id" validate:"required" zh:"对话 id"`
}

type GetCreateBlackBoxCaseAIMessageResp struct {
	Message string `json:"message" zh:"AI回答内容"`
}

type GetEditBlackBoxCaseDataProgressReq struct {
	CaseRefId string `form:"case_ref_id" zh:"用例数据生成ID"`
}

type GetEditBlackBoxCaseDataProgressResp struct {
}

type GroupCondition struct {
	Relationship string       `json:"relationship" validate:"oneof=AND OR"`
	Conditions   []*Condition `json:"conditions"`
}

type KeepBlackBoxCaseDataReq struct {
	CaseDataId string `json:"case_data_id" zh:"用例数据ID"`
	Action     string `json:"action" zh:"是否保留(keep/cancel)"`
}

type KeepBlackBoxCaseDataResp struct {
}

type KnowledgeDocPgTitle struct {
	Id           int64    `json:"knowledge_paragraph_title_id"`
	Title        string   `json:"knowledge_paragraph_title_text"`
	DemandPoints []string `json:"demandPoints,optional,omitempty"`
}

type KnowledgeDocPgTitleMap struct {
	Id    int64  `json:"id,optional,omitempty" zh:"飞书文档标题id"`
	Title string `json:"title,optional,omitempty" zh:"飞书文档标题内容"`
}

type KnowledgeParagraphTitleMap struct {
	Id   int64  `json:"id,optional,omitempty" zh:"飞书标题id"`
	Text string `json:"text,optional,omitempty" zh:"飞书标题内容"`
}

type ListBlackBoxCaseDataReq struct {
	CaseId     string `json:"case_id,optional" zh:"用例Id"`
	RevisionId string `json:"revision_id,optional" zh:"版本Id"`
	Type       string `json:"type,optional" zh:"返回格式:markdown/json"`
}

type ListBlackBoxCaseDataResp struct {
	Items   []*BlackBoxCaseData `json:"items"`
	Content string              `json:"Content"`
}

type ListBlackBoxCaseDataV2Req struct {
	CaseId     string `form:"case_id,optional" zh:"用例Id"`
	RevisionId string `form:"revision_id,optional" zh:"版本Id"`
	Type       string `form:"type,optional" zh:"返回格式:markdown/json"`
}

type ListBlackBoxCaseDirsReq struct {
	ProjectId string `json:"project_id" validate:"required" zh:"项目Id"`
}

type ListBlackBoxCaseDirsResp struct {
	Items []*BlackBoxCaseDir `json:"items"`
}

type ListBlackBoxCaseKnowledgeReq struct {
	DirId string `json:"dir_id" validate:"required" zh:"目录Id"`
}

type ListBlackBoxCaseKnowledgeResp struct {
	Items []*BlackBoxCaseKnowledge `json:"items"`
}

type ListBlackBoxCaseMapDocumentsReq struct {
	ProjectId string `form:"project_id" zh:"项目Id"`
	DirId     string `form:"dir_id" zh:"目录Id"`
	CaseId    string `form:"case_id" validate:"required" zh:"用例Id"`
}

type ListBlackBoxCaseMapDocumentsResp struct {
}

type ListBlackBoxCaseReq struct {
	DirId      string      `json:"dir_id" validate:"required" zh:"目录Id"`
	CaseName   string      `json:"case_name,optional" zh:"用例名称"`
	Pagination *Pagination `json:"pagination,omitempty,optional" zh:"查询分页"`
}

type ListBlackBoxCaseResp struct {
	CurrentPage uint64          `json:"current_page"`
	PageSize    uint64          `json:"page_size"`
	TotalCount  uint64          `json:"total_count"`
	TotalPage   uint64          `json:"total_page"`
	Items       []*BlackBoxCase `json:"items"`
}

type ListBlackBoxCaseRevisionReq struct {
	CaseId string `json:"case_id" zh:"用例Id"`
}

type ListBlackBoxCaseRevisionResp struct {
	Items []*BlackBoxCaseRevision `json:"items"`
}

type ListBlackBoxCaseTwBetaMindCheckReq struct {
	ProductModule string `json:"product_module" zh:"产品模块"`
}

type ListBlackBoxCaseTwBetaMindCheckResp struct {
	AcCheckList []*BlackBoxCaseTwBetaMindCheck `json:"ac_check_list" zh:"ac_check_list"`
	TcCheckList []*BlackBoxCaseTwBetaMindCheck `json:"tc_check_list" zh:"tc_check_list"`
}

type ListBlackBoxCaseTwBetaMindReq struct {
	BetaCaseId string `json:"beta_case_id" zh:"BETA用例ID"`
}

type ListBlackBoxCaseTwBetaMindResp struct {
	Items []*BlackBoxCaseTwBetaMind `json:"items"`
}

type ListBlackBoxCaseTwBetaReq struct {
	BetaCaseName string      `json:"beta_case_name" zh:"BETA用例名称"`
	Pagination   *Pagination `json:"pagination,omitempty,optional" zh:"查询分页"`
}

type ListBlackBoxCaseTwBetaResp struct {
	Items       []*BlackBoxCaseTwBeta `json:"items"`
	CurrentPage uint64                `json:"current_page"`
	PageSize    uint64                `json:"page_size"`
	TotalCount  uint64                `json:"total_count"`
	TotalPage   uint64                `json:"total_page"`
}

type ListBlackBoxKnowledgeExperienceCategoriesReq struct {
	ProjectId string `form:"project_id" url:"project_id" validate:"required" zh:"项目Id"`
}

type ListBlackBoxKnowledgeExperienceCategoriesResp struct {
	Items []*BlackBoxKnowledgeExperienceCategory `json:"items"`
}

type ListBlackBoxKnowledgeExperiencesFilter struct {
	Tag      string `json:"tag" zh:"标签名称"`
	Category string `json:"category" zh:"经验类型名称"`
}

type ListBlackBoxKnowledgeExperiencesReq struct {
	ProjectId string                                 `json:"project_id" validate:"required" zh:"项目Id"`
	Filter    ListBlackBoxKnowledgeExperiencesFilter `json:"filter,optional" zh:"标签筛选"`
	Search    string                                 `json:"search,optional" zh:"术语搜索"`
	Page      int                                    `json:"page,optional" zh:"页码"`
	Size      int                                    `json:"size,optional" zh:"页容量"`
}

type ListBlackBoxKnowledgeExperiencesResp struct {
	Total int                            `json:"total,optional" zh:"总容量"`
	Data  []*BlackBoxKnowledgeExperience `json:"data"`
}

type ListBlackBoxKnowledgeTagsReq struct {
	ProjectId string `form:"project_id" url:"project_id" validate:"required" zh:"项目Id"`
}

type ListBlackBoxKnowledgeTagsResp struct {
	Items []*BlackBoxKnowledgeTag `json:"items"`
}

type ListBlackBoxKnowledgeTermsFilter struct {
	Tag string `json:"tag" zh:"标签名称"`
}

type ListBlackBoxKnowledgeTermsReq struct {
	ProjectId string                           `json:"project_id" validate:"required" zh:"项目Id"`
	Filter    ListBlackBoxKnowledgeTermsFilter `json:"filter,optional" zh:"标签筛选"`
	Search    string                           `json:"search,optional" zh:"术语搜索"`
	Page      int                              `json:"page,optional" zh:"页码"`
	Size      int                              `json:"size,optional" zh:"页容量"`
}

type ListBlackBoxKnowledgeTermsResp struct {
	Total int                      `json:"total,optional" zh:"总容量"`
	Data  []*BlackBoxKnowledgeTerm `json:"data"`
}

type Markdown struct {
	Text string `json:"text,optional"`
}

type MergeBlackBoxCaseDataReq struct {
	CaseIds  []string `json:"case_ids" zh:"用例Id列表"`
	CaseName string   `json:"case_name" zh:"用例名"`
}

type MergeBlackBoxCaseDataResp struct {
	CaseId string `json:"case_id" zh:"用例Id"`
}

type MergeBlackBoxCaseReq struct {
	ProjectId       string   `json:"project_id" validate:"required" zh:"项目Id"`
	AssistantId     string   `json:"assistant_id" validate:"required" zh:"助手Id"`
	SessionId       string   `json:"session_id" validate:"required" zh:"会话Id"`
	ConversationIds []string `json:"conversation_ids" validate:"required,min=2" zh:"对话 ids"`
}

type MergeBlackBoxCaseResp struct {
	Messages []*BlackBoxCaseSessionMessage `json:"messages"`
}

type MetricsMigreation struct {
}

type ModifyBlackBoxCaseDataWithSseReq struct {
	ProjectId  string  `json:"project_id" zh:"项目Id"`
	DirId      string  `json:"dir_id" zh:"目录Id"`
	CaseId     string  `json:"case_id" validate:"required" zh:"用例Id"`
	RevisionId *string `json:"revision_id,optional" zh:"版本Id"`
}

type ModifyBlackBoxCaseDataWithSseResp struct {
}

type NoArgs struct {
}

type Other struct {
	Value string `json:"value"`
}

type Pagination struct {
	CurrentPage uint64 `json:"current_page,default=1" validate:"gte=1"`
	PageSize    uint64 `json:"page_size,default=10" validate:"gte=1"`
}

type PartialEditBlackBoxCaseDataReq struct {
	CaseId                      string              `json:"case_id" zh:"用例Id"`
	RevisionId                  string              `json:"revision_id" zh:"版本Id"`
	KnowledgeId                 string              `json:"knowledge_id" zh:"知识文档Id"`
	KnowledgeParagraphTitleText string              `json:"knowledge_paragraph_title_text" zh:"文档使用标题"`
	KnowledgeParagraphTitleId   int64               `json:"knowledge_paragraph_title_id" zh:"文档使用标题id"`
	KnowledgeFixSugg            string              `json:"knowledge_fix_sugg" zh:"修改意见"`
	EnableReference             bool                `json:"enable_reference" zh:"是否启用参考文档"`
	ReferenceDoc                ReferenceDoc        `json:"reference_doc"`
	Items                       []*BlackBoxCaseData `json:"items"`
}

type PartialEditBlackBoxCaseDataResp struct {
}

type QueryBlackBoxCaseKnowledgeReq struct {
	CaseId                      string `json:"case_id" zh:"用例ID"`
	KnowledgeId                 string `json:"knowledge_id" zh:"知识文档ID"`
	KnowledgeParagraphTitleText string `json:"knowledge_paragraph_title_text,optional,omitempty" zh:"文档使用标题"`
	KnowledgeParagraphTitleId   int64  `json:"knowledge_paragraph_title_id,optional,omitempty" zh:"文档使用标题id"`
}

type QueryBlackBoxCaseKnowledgeResp struct {
}

type QueryBlackBoxCaseKnowledgeV2Req struct {
	CaseId                  string                 `json:"case_id" zh:"用例ID"`
	KnowledgeId             string                 `json:"knowledge_id" zh:"知识文档ID"`
	KnowledgeParagraphTitle []*KnowledgeDocPgTitle `json:"knowledge_paragraph_title" zh:"文档使用标题"`
}

type QueryBlackBoxCaseKnowledgeV2Resp struct {
}

type ReCreateBlackBoxCaseReq struct {
	ProjectId      string `json:"project_id" validate:"required" zh:"项目Id"`
	AssistantId    string `json:"assistant_id" validate:"required" zh:"助手Id"`
	SessionId      string `json:"session_id" validate:"required" zh:"会话Id"`
	ConversationId string `json:"conversation_id" validate:"required" zh:"对话 id"`
}

type ReCreateBlackBoxCaseResp struct {
}

type ReferenceDoc struct {
	Type   string `json:"type,optional" zh:"类型"`
	Feishu Feishu `json:"feishu,optional" zh:"飞书"`
	Text   Text   `json:"text,optional" zh:"文本"`
}

type ReloadBlackBoxCaseDocumentReq struct {
	ProjectId   string `json:"project_id" validate:"required" zh:"项目Id"`
	AssistantId string `json:"assistant_id" validate:"required" zh:"助手Id"`
	DocumentId  string `json:"document_id" validate:"required" zh:"文档Id"`
}

type ReloadBlackBoxCaseDocumentResp struct {
}

type ReloadBlackBoxCaseKnowledgeReq struct {
	KnowledgeId string `json:"knowledge_id" validate:"required" zh:"文档ID"`
}

type ReloadBlackBoxCaseKnowledgeResp struct {
}

type RemoveBlackBoxCaseSessionHistoryMessageListReq struct {
	ProjectId   string `form:"project_id" validate:"required" zh:"项目Id"`
	AssistantId string `form:"assistant_id" validate:"required" zh:"助手Id"`
	SessionId   string `form:"session_id" validate:"required" zh:"会话Id"`
}

type RemoveBlackBoxCaseSessionHistoryMessageListResp struct {
}

type ReorderBlackBoxCaseDataReq struct {
	RevisionId string `json:"revision_id,optional" zh:"版本Id"`
}

type ReorderBlackBoxCaseDataResp struct {
}

type ReplaceBlackBoxCaseDataReq struct {
	RevisionId string                  `json:"revision_id" zh:"版本Id"`
	Items      []*BlackBoxCaseDataBase `json:"items"`
	NewItems   []*BlackBoxCaseData     `json:"new_items"`
}

type ReplaceBlackBoxCaseDataResp struct {
	Items    []*BlackBoxCaseData `json:"items"`
	NewItems []*BlackBoxCaseData `json:"new_items"`
}

type ReplaceBlackBoxCaseReq struct {
	ProjectId      string `json:"project_id" validate:"required" zh:"项目Id"`
	AssistantId    string `json:"assistant_id" validate:"required" zh:"助手Id"`
	SessionId      string `json:"session_id" validate:"required" zh:"会话Id"`
	ConversationId string `json:"conversation_id" validate:"required" zh:"对话 id"`
	Content        string `json:"content" validate:"required" zh:"替换内容"`
}

type ReplaceBlackBoxCaseResp struct {
}

type SearchBlackBoxCaseAssistantReq struct {
	ProjectId string `json:"project_id" validate:"required" zh:"项目Id"`
}

type SearchBlackBoxCaseAssistantResp struct {
	Items []*BlackBoxCaseAssistant `json:"items"`
}

type SearchBlackBoxCaseDocumentForAssistantReq struct {
	ProjectId   string      `json:"project_id" validate:"required" zh:"项目Id"`
	AssistantId string      `json:"assistant_id" zh:"助手Id"`
	Pagination  *Pagination `json:"pagination,omitempty,optional" zh:"查询分页"`
}

type SearchBlackBoxCaseDocumentForAssistantResp struct {
	CurrentPage uint64                  `json:"current_page"`
	PageSize    uint64                  `json:"page_size"`
	TotalCount  uint64                  `json:"total_count"`
	TotalPage   uint64                  `json:"total_page"`
	Items       []*BlackBoxCaseDocument `json:"items"`
}

type SearchBlackBoxCaseDocumentForSessionReq struct {
	ProjectId   string      `json:"project_id" validate:"required" zh:"项目Id"`
	AssistantId string      `json:"assistant_id" zh:"助手Id"`
	SessionId   string      `json:"session_id" zh:"会话Id"`
	Pagination  *Pagination `json:"pagination,omitempty,optional" zh:"查询分页"`
}

type SearchBlackBoxCaseDocumentForSessionResp struct {
	CurrentPage uint64                  `json:"current_page"`
	PageSize    uint64                  `json:"page_size"`
	TotalCount  uint64                  `json:"total_count"`
	TotalPage   uint64                  `json:"total_page"`
	Items       []*BlackBoxCaseDocument `json:"items"`
}

type SearchBlackBoxCaseSessionReq struct {
	ProjectId   string `json:"project_id" validate:"required" zh:"项目Id"`
	AssistantId string `json:"assistant_id" zh:"助手Id"`
}

type SearchBlackBoxCaseSessionResp struct {
	Items []*BlackBoxCaseSession `json:"items"`
}

type SendBlackBoxCaseForSessionReq struct {
	ProjectId      string `json:"project_id" validate:"required" zh:"项目Id"`
	AssistantId    string `json:"assistant_id" validate:"required" zh:"助手Id"`
	SessionId      string `json:"session_id" validate:"required" zh:"会话Id"`
	Message        string `json:"message" validate:"required" zh:"消息"`
	ConversationId string `json:"conversation_id" validate:"required" zh:"对话 id"`
}

type SendBlackBoxCaseForSessionResp struct {
	Message *BlackBoxCaseSessionMessage `json:"message"`
	Event   string                      `json:"event" zh:"事件类型 [start | running | done]"`
}

type SetBlackBoxCaseDataCoverageReq struct {
	ProjectId  string  `json:"project_id" zh:"项目Id"`
	DirId      string  `json:"dir_id" zh:"目录Id"`
	CaseId     string  `json:"case_id" validate:"required" zh:"用例Id"`
	RevisionId *string `json:"revision_id,optional" zh:"版本Id"`
	Coverage   int64   `json:"coverage,optional" zh:"覆盖率"`
}

type SetBlackBoxCaseDataCoverageResp struct {
}

type SingleCondition struct {
	Field   string   `json:"field" validate:"required"`
	Compare string   `json:"compare,default=EQ" validate:"oneof=EQ NE LT LE GT GE LIKE IN BETWEEN"`
	In      []string `json:"in,omitempty,optional"`
	Between *Between `json:"between,omitempty,optional"`
	Other   *Other   `json:"other,omitempty,optional"`
}

type SortField struct {
	Field string `json:"field" validate:"required"`
	Order string `json:"order,default=ASC"`
}

type Text struct {
	Content string `json:"content,optional"`
}

type TransBlackBoxCaseDataReq struct {
	RevisionId string `json:"revision_id" zh:"版本ID"`
}

type TransBlackBoxCaseDataResp struct {
	Content string `json:"content" zh:"内容"`
}

type TransferBlackBoxCase2XMindReq struct {
	ProjectId      string `json:"project_id" validate:"required" zh:"项目Id"`
	AssistantId    string `json:"assistant_id" validate:"required" zh:"助手Id"`
	SessionId      string `json:"session_id" validate:"required" zh:"会话Id"`
	ConversationId string `json:"conversation_id" validate:"required" zh:"对话 id"`
	MindType       int8   `json:"mind_type,optional" validate:"omitempty,oneof=0 1" zh:"转换类型：用例导图：0，评审导图：1"`
}

type TransferBlackBoxCase2XMindResp struct {
	Data string `json:"data" zh:"转换结果"`
}

type UpdateBlackBoxCaseAIReq struct {
	CaseId              string `json:"case_id" validate:"required" zh:"用例Id"`
	CaseName            string `json:"case_name,optional,omitempty" zh:"用例名称"`
	CaseContinueToWrite int64  `json:"case_continue_to_write,optional,omitempty" zh:"用例续写次数"`
	CaseModelCharacter  string `json:"case_model_character,optional,omitempty" zh:"用例模型性格"`
}

type UpdateBlackBoxCaseAIResp struct {
}

type UpdateBlackBoxCaseAssistantReq struct {
	ProjectId            string `json:"project_id" validate:"required" zh:"项目Id"`
	AssistantId          string `json:"assistant_id" validate:"required" zh:"助手Id"`
	AssistantName        string `json:"assistant_name" validate:"required" zh:"助手名称"`
	AssistantDescription string `json:"assistant_description" zh:"助手描述"`
	WorkSpaceType        int8   `json:"work_space_type,optional" validate:"omitempty,oneof=0 1" zh:"工作空间类型：个人空间(private)：0，公共空间()：1"`
	WorkPriorityType     int8   `json:"work_priority_type,optional" validate:"omitempty,oneof=0 1" zh:"工作优先级类型：准确优先：0，速度优先：1"`
	SessionRound         int    `json:"session_round" validate:"required" zh:"对话轮数"`
}

type UpdateBlackBoxCaseAssistantResp struct {
}

type UpdateBlackBoxCaseDataOrderReq struct {
	RevisionId string `json:"revision_id"`
}

type UpdateBlackBoxCaseDataOrderResp struct {
}

type UpdateBlackBoxCaseDataReq struct {
	CaseDataId   string `json:"case_data_id" zh:"知识文档Id"`
	OrderId      string `json:"order_id,optional" zh:"序号"`
	CaseName     string `json:"case_name,optional" zh:"用例名称"`
	Requirement  string `json:"requirement,optional" zh:"需求名称"`
	PreCondition string `json:"pre_condition,optional" zh:"前置条件"`
	CaseStep     string `json:"case_step,optional" zh:"用例步骤"`
	ExpectResult string `json:"expect_result,optional" zh:"预期结果"`
	Terminal     string `json:"terminal,optional" zh:"终端"`
	CaseLevel    string `json:"case_level,optional" zh:"用例等级"`
	Tag          string `json:"tag,optional" zh:"标识"`
	IsKeep       bool   `json:"is_keep,optional" zh:"是否保留"`
	EditedField  string `json:"edited_field,optional" zh:"修改的字段"`
}

type UpdateBlackBoxCaseDataResp struct {
}

type UpdateBlackBoxCaseDirReq struct {
	ProjectId string `json:"project_id" validate:"required" zh:"项目Id"`
	DirId     string `json:"dir_id" validate:"required" zh:"目录Id"`
	DirName   string `json:"dir_name" validate:"required" zh:"目录名称"`
}

type UpdateBlackBoxCaseDirResp struct {
}

type UpdateBlackBoxCaseDocumentReq struct {
	ProjectId           string `json:"project_id" validate:"required" zh:"项目Id"`
	AssistantId         string `json:"assistant_id" validate:"required" zh:"助手Id"`
	DocumentId          string `json:"document_id" validate:"required" zh:"文档Id"`
	DocumentName        string `json:"document_name" validate:"required" zh:"文档名称"`
	DocumentDescription string `json:"document_description,omitempty" zh:"文档描述"`
}

type UpdateBlackBoxCaseDocumentResp struct {
}

type UpdateBlackBoxCaseDocumentStatusForAssistantReq struct {
	ProjectId  string `json:"project_id,optional" validate:"omitempty" zh:"项目Id"`
	DocumentId string `json:"document_id" validate:"required" zh:"文档名称"`
	Status     int8   `json:"status" validate:"required" zh:"文档状态：处理中：0，完成：1，失败：2"`
}

type UpdateBlackBoxCaseDocumentStatusForAssistantResp struct {
}

type UpdateBlackBoxCaseItemReq struct {
	DirId               string                `json:"dir_id" validate:"required" zh:"目录Id"`
	CaseId              string                `json:"case_id" validate:"required" zh:"用例Id"`
	CaseName            string                `json:"case_name,optional,omitempty" zh:"用例名称"`
	CaseContinueToWrite int64                 `json:"case_continue_to_write,optional,omitempty" zh:"用例续写次数"`
	CaseModelCharacter  string                `json:"case_model_character,optional,omitempty" zh:"用例模型性格"`
	CaseRemarks         string                `json:"case_remarks,optional,omitempty" zh:"用例备注"`
	KnowledgeId         string                `json:"knowledge_id,optional,omitempty" zh:"知识文档ID"`
	KnowledgeDocPgTitle []KnowledgeDocPgTitle `json:"knowledge_paragraph_title,optional,omitempty" zh:"知识文档段落标题"`
}

type UpdateBlackBoxCaseItemResp struct {
}

type UpdateBlackBoxCaseItemV2Req struct {
	DirId                       string                `json:"dir_id" validate:"required" zh:"目录Id"`
	CaseId                      string                `json:"case_id" validate:"required" zh:"用例Id"`
	CaseName                    string                `json:"case_name,optional,omitempty" zh:"用例名称"`
	CaseContinueToWrite         int64                 `json:"case_continue_to_write,optional,omitempty" zh:"用例续写次数"`
	CaseModelCharacter          string                `json:"case_model_character,optional,omitempty" zh:"用例模型性格"`
	CaseRemarks                 string                `json:"case_remarks,optional,omitempty" zh:"用例备注"`
	KnowledgeId                 string                `json:"knowledge_id,optional,omitempty" zh:"知识文档ID"`
	KnowledgeParagraphTitleId   int64                 `json:"knowledge_paragraph_title_id,optional,omitempty" zh:"知识文档标题ID"`
	KnowledgeParagraphTitleText string                `json:"knowledge_paragraph_title_text,optional,omitempty" zh:"知识文档标题名词"`
	KnowledgeFixSugg            string                `json:"knowledge_fix_sugg,optional,omitempty" zh:"修改意见"`
	KnowledgeDocPgTitle         []KnowledgeDocPgTitle `json:"knowledge_paragraph_title,optional,omitempty" zh:"新版文档标题""`
}

type UpdateBlackBoxCaseKnowledgeReq struct {
	DirId            string `json:"dir_id" validate:"required" zh:"目录Id"`
	KnowledgeId      string `json:"knowledge_id" validate:"required" zh:"文档ID"`
	KnowledgeName    string `json:"knowledge_name" zh:"文档名称"`
	KnowledgeType    string `json:"knowledge_type" zh:"文档类型"`
	KnowledgeContent string `json:"knowledge_content" zh:"文档内容"`
}

type UpdateBlackBoxCaseKnowledgeResp struct {
}

type UpdateBlackBoxCaseKnowledgeV2Req struct {
	ProjectId   string                             `json:"project_id" validate:"required" zh:"项目Id"`
	DirId       string                             `json:"dir_id" validate:"required" zh:"目录Id"`
	CaseId      string                             `json:"case_id" validate:"required" zh:"用例Id"`
	Tags        []*string                          `json:"tags" zh:"术语标签"`
	Experiences []*BlackBoxCaseKnowledgeExperience `json:"experiences" zh:"测试经验"`
}

type UpdateBlackBoxCaseKnowledgeV2Resp struct {
}

type UpdateBlackBoxCaseMapDocumentReq struct {
	ProjectId string                              `json:"project_id" zh:"项目Id"`
	DirId     string                              `json:"dir_id" zh:"目录Id"`
	CaseId    string                              `json:"case_id" validate:"required" zh:"用例Id"`
	FuncId    string                              `json:"func_id" validate:"required" zh:"功能点Id"`
	FuncName  string                              `json:"func_name" validate:"required" zh:"功能点名称"`
	FuncDoc   *BlackBoxCaseKnowledgeSupplementDoc `json:"func_doc,optional" zh:"生成功能点文档"`
	ExpeDoc   *BlackBoxCaseKnowledgeSupplementDoc `json:"expe_doc,optional" zh:"生成测试经验文档"`
}

type UpdateBlackBoxCaseMapDocumentResp struct {
}

type UpdateBlackBoxCaseMapExperienceWithSseRequset struct {
	ProjectId                 string                             `json:"project_id" zh:"项目Id"`
	DirId                     string                             `json:"dir_id" zh:"目录Id"`
	CaseId                    string                             `json:"case_id" validate:"required" zh:"用例Id"`
	KnowledgeSupplementEnable bool                               `json:"knowledge_supplement_enable" zh:"是否开启补充文档"`
	KnowledgeSupplementDoc    BlackBoxCaseKnowledgeSupplementDoc `json:"knowledge_supplement_doc" zh:"知识补充文档"`
	FunctionPoint             string                             `json:"function_point" validate:"required" zh:"功能点"`
	GenerateOpinion           string                             `json:"generate_opinion" zh:"生成意见"`
	BlackBoxCaseBaseMap       BlackBoxCaseBaseMap                `json:"old_function_point_mind_data"  zh:"评审导图"`
}

type UpdateBlackBoxCaseMapExperienceWithSseResp struct {
}

type UpdateBlackBoxCaseMapReq struct {
	ProjectId string              `json:"project_id" zh:"项目Id"`
	DirId     string              `json:"dir_id" zh:"目录Id"`
	CaseId    string              `json:"case_id" validate:"required" zh:"用例Id"`
	Data      BlackBoxCaseMapNode `json:"data" validate:"required" zh:"导图数据"`
}

type UpdateBlackBoxCaseMapResp struct {
}

type UpdateBlackBoxCaseMindTwBetaReq struct {
	MindId      int64  `json:"mind_id" zh:"脑图ID"`
	MindContent string `json:"mind_content" zh:"脑图内容"`
}

type UpdateBlackBoxCaseMindTwBetaResp struct {
}

type UpdateBlackBoxCaseReq struct {
	ProjectId        string `json:"project_id" validate:"required" zh:"项目Id"`
	AssistantId      string `json:"assistant_id" validate:"required" zh:"助手Id"`
	SessionId        string `json:"session_id" validate:"required" zh:"会话Id"`
	ConversationId   string `json:"conversation_id" validate:"required" zh:"对话 id"`
	ModifySuggestion string `json:"modify_suggestion" validate:"required" zh:"修改意见"`
	Content          string `json:"content" zh:"用例列表"`
}

type UpdateBlackBoxCaseSessionReq struct {
	ProjectId          string `json:"project_id" zh:"项目Id"`
	AssistantId        string `json:"assistant_id" zh:"助手Id"`
	WorkSpaceType      int8   `json:"work_space_type" validate:"required" zh:"工作空间类型：个人空间(private)：0，公共空间()：1"`
	SessionId          string `json:"session_id" zh:"会话Id"`
	SessionName        string `json:"session_name" zh:"会话名称"`
	SessionDescription string `json:"session_description,omitempty" zh:"会话描述"`
}

type UpdateBlackBoxCaseSessionResp struct {
}

type UpdateBlackBoxKnowledgeExperienceReq struct {
	Id                 string                              `json:"id" validate:"required" zh:"经验Id"`
	ProjectId          string                              `json:"project_id" validate:"required" zh:"项目Id"`
	Category           BlackBoxKnowledgeExperienceCategory `json:"category" validate:"required" zh:"经验类型"`
	TestExperience     string                              `json:"test_experience" validate:"required" zh:"经验名称"`
	NormalFocusPoint   string                              `json:"normal_focus_point" validate:"required" zh:"正常经验"`
	AbnormalFocusPoint string                              `json:"abnormal_focus_point" validate:"required" zh:"异常经验"`
	Tags               []*BlackBoxKnowledgeTag             `json:"tags"`
}

type UpdateBlackBoxKnowledgeExperienceResp struct {
}

type UpdateBlackBoxKnowledgeTagReq struct {
	Id  int    `json:"id" validate:"required" zh:"标签Id"`
	Tag string `json:"tag" validate:"required" zh:"标签名称"`
}

type UpdateBlackBoxKnowledgeTagResp struct {
}

type UpdateBlackBoxKnowledgeTermReq struct {
	Id            string                  `json:"id" validate:"required" zh:"术语Id"`
	ProjectId     string                  `json:"project_id" validate:"required" zh:"项目Id"`
	Tags          []*BlackBoxKnowledgeTag `json:"tags"`
	TermInChinese string                  `json:"term_in_chinese" validate:"required" zh:"术语名称"`
	TermMeaning   string                  `json:"term_meaning" validate:"required" zh:"术语含义"`
}

type UpdateBlackBoxKnowledgeTermResp struct {
}
