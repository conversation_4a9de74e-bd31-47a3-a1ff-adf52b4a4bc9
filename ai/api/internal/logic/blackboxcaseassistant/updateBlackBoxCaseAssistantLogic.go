package blackboxcaseassistant

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
)

type UpdateBlackBoxCaseAssistantLogic struct {
	*logic.BaseLogic
}

// update black box case assistant
func NewUpdateBlackBoxCaseAssistantLogic(
	ctx context.Context, svcCtx *svc.ServiceContext,
) *UpdateBlackBoxCaseAssistantLogic {
	return &UpdateBlackBoxCaseAssistantLogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),
	}
}

func (l *UpdateBlackBoxCaseAssistantLogic) UpdateBlackBoxCaseAssistant(req *types.UpdateBlackBoxCaseAssistantReq) (
	resp *types.UpdateBlackBoxCaseAssistantResp, err error,
) {
	in := &pb.UpdateBlackBoxCaseAssistantReq{}
	if err = utils.Copy(in, req, l.Converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy data[%+v] to request, error: %+v", req,
			err,
		)
	}

	_, err = l.SvcCtx.BlackBoxCaseAssistantServiceRpc.UpdateBlackBoxCaseAssistant(l.Ctx, in)
	if err != nil {
		return nil, err
	}

	return &types.UpdateBlackBoxCaseAssistantResp{}, nil
}
