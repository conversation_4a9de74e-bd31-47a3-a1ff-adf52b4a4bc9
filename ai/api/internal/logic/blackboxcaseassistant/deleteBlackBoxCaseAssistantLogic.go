package blackboxcaseassistant

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
)

type DeleteBlackBoxCaseAssistantLogic struct {
	*logic.BaseLogic
}

// delete black box case assistant
func NewDeleteBlackBoxCaseAssistantLogic(
	ctx context.Context, svcCtx *svc.ServiceContext,
) *DeleteBlackBoxCaseAssistantLogic {
	return &DeleteBlackBoxCaseAssistantLogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),
	}
}

func (l *DeleteBlackBoxCaseAssistantLogic) DeleteBlackBoxCaseAssistant(req *types.DeleteBlackBoxCaseAssistantReq) (
	resp *types.DeleteBlackBoxCaseAssistantResp, err error,
) {
	in := &pb.DeleteBlackBoxCaseAssistantReq{}
	if err = utils.Copy(in, req, l.Converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy data[%+v] to request, error: %+v", req,
			err,
		)
	}

	_, err = l.SvcCtx.BlackBoxCaseAssistantServiceRpc.DeleteBlackBoxCaseAssistant(l.Ctx, in)
	if err != nil {
		return nil, err
	}

	return &types.DeleteBlackBoxCaseAssistantResp{}, nil
}
