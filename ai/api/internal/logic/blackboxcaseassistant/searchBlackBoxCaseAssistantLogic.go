package blackboxcaseassistant

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
)

type SearchBlackBoxCaseAssistantLogic struct {
	*logic.BaseLogic
}

// search black box case assistant
func NewSearchBlackBoxCaseAssistantLogic(
	ctx context.Context, svcCtx *svc.ServiceContext,
) *SearchBlackBoxCaseAssistantLogic {
	return &SearchBlackBoxCaseAssistantLogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),
	}
}

func (l *SearchBlackBoxCaseAssistantLogic) SearchBlackBoxCaseAssistant(req *types.SearchBlackBoxCaseAssistantReq) (
	resp *types.SearchBlackBoxCaseAssistantResp, err error,
) {
	in := &pb.SearchBlackBoxCaseAssistantReq{}
	if err = utils.Copy(in, req, l.Converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy data[%+v] to request, error: %+v", req,
			err,
		)
	}
	out, err := l.SvcCtx.BlackBoxCaseAssistantServiceRpc.SearchBlackBoxCaseAssistant(l.Ctx, in)
	if err != nil {
		return nil, err
	}
	resp = &types.SearchBlackBoxCaseAssistantResp{Items: []*types.BlackBoxCaseAssistant{}}
	if err = utils.Copy(resp, out, l.Converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy data[%+v] to response, error: %+v",
			out, err,
		)
	}
	return resp, nil
}
