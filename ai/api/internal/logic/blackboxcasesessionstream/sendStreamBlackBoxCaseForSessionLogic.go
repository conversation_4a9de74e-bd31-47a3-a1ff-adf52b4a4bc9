package blackboxcasesessionstream

import (
	"context"
	"strconv"
	"time"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/public/httpc"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sse"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/types"
)

type SendStreamBlackBoxCaseForSessionLogic struct {
	*logic.BaseLogic
}

// NewSendStreamBlackBoxCaseForSessionLogic send stream black box case for session
func NewSendStreamBlackBoxCaseForSessionLogic(
	ctx context.Context, svcCtx *svc.ServiceContext,
) *SendStreamBlackBoxCaseForSessionLogic {
	return &SendStreamBlackBoxCaseForSessionLogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),
	}
}

func (l *SendStreamBlackBoxCaseForSessionLogic) SendStreamBlackBoxCaseForSession(req *types.SendBlackBoxCaseForSessionReq) (
	resp *sse.Stream, err error,
) {
	stream := sse.NewStream(strconv.Itoa(int(time.Now().UnixMilli())), 0)
	stopChan := make(chan struct{})

	go func() {
		defer func() {
			stream.Quit()
			close(stopChan)
		}()

		errChan := make(chan error)

		go func() {
			errChan <- httpc.SendMessage(
				l.SvcCtx.ExternalAiDomain,
				&httpc.SendMsgForSessionReq{
					SessionId: req.SessionId, ConversationId: req.ConversationId, Message: req.Message,
				},
				stream,
			)
		}()

		select {
		case err = <-errChan:
			if err != nil {
				l.Error(err)
			}
		case <-stopChan:
		}
	}()

	return stream, err
}
