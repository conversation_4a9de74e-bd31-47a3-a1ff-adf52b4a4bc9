package blackboxcasesessionstream

import (
	"context"
	"strconv"
	"time"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/public/httpc"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sse"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/types"
)

type ReCreateBlackBoxCaseLogic struct {
	*logic.BaseLogic
}

// NewReCreateBlackBoxCaseLogic recreate black box case
func NewReCreateBlackBoxCaseLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ReCreateBlackBoxCaseLogic {
	return &ReCreateBlackBoxCaseLogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),
	}
}

func (l *ReCreateBlackBoxCaseLogic) ReCreateBlackBoxCase(req *types.ReCreateBlackBoxCaseReq) (
	resp *sse.Stream, err error,
) {
	stream := sse.NewStream(strconv.Itoa(int(time.Now().UnixMilli())), 0)
	stopChan := make(chan struct{})

	go func() {
		defer func() {
			stream.Quit()
			close(stopChan)
		}()

		errChan := make(chan error)

		go func() {
			errChan <- httpc.ReCreateTestCase(
				l.SvcCtx.ExternalAiDomain,
				&httpc.ReCreateTestCaseReq{ConversationId: req.ConversationId},
				stream,
			)
		}()

		select {
		case err = <-errChan:
			if err != nil {
				l.Error(err)
			}
		case <-stopChan:
		}
	}()

	return stream, err
}
