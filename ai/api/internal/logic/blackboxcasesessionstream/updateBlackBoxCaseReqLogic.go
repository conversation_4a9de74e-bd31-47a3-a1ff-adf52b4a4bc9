package blackboxcasesessionstream

import (
	"context"
	"strconv"
	"time"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sse"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/public/httpc"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/types"
)

type UpdateBlackBoxCaseReqLogic struct {
	*logic.BaseLogic
}

// NewUpdateBlackBoxCaseReqLogic update stream black box case for session
func NewUpdateBlackBoxCaseReqLogic(ctx context.Context, svcCtx *svc.ServiceContext) *UpdateBlackBoxCaseReqLogic {
	return &UpdateBlackBoxCaseReqLogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),
	}
}

func (l *UpdateBlackBoxCaseReqLogic) UpdateBlackBoxCaseReq(req *types.UpdateBlackBoxCaseReq) (
	resp *sse.Stream, err error,
) {
	stream := sse.NewStream(strconv.Itoa(int(time.Now().UnixMilli())), 0)
	stopChan := make(chan struct{})

	go func() {
		defer func() {
			stream.Quit()
			close(stopChan)
		}()

		errChan := make(chan error)

		go func() {
			errChan <- httpc.UpdateTestCase(
				l.SvcCtx.ExternalAiDomain,
				&httpc.UpdateTestCaseReq{ConversationId: req.ConversationId, ModifySuggestion: req.ModifySuggestion},
				stream,
			)
		}()

		select {
		case err = <-errChan:
			if err != nil {
				l.Error(err)
			}
		case <-stopChan:
		}
	}()

	return stream, err
}
