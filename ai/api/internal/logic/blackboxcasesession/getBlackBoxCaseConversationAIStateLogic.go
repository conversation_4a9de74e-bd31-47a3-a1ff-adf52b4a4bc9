package blackboxcasesession

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/types"
)

type GetBlackBoxCaseConversationAIStateLogic struct {
	*logic.BaseLogic
}

// NewGetBlackBoxCaseConversationAIStateLogic get black box case conversation ai state
func NewGetBlackBoxCaseConversationAIStateLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetBlackBoxCaseConversationAIStateLogic {
	return &GetBlackBoxCaseConversationAIStateLogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),
	}
}

func (l *GetBlackBoxCaseConversationAIStateLogic) GetBlackBoxCaseConversationAIState(req *types.GetBlackBoxCaseConversationAIStateReq) (resp *types.GetBlackBoxCaseConversationAIStateResp, err error) {
	in := &pb.GetBlackBoxCaseConversationAIStateReq{}
	if err = utils.Copy(in, req, l.Converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy data[%+v] to request, error: %+v", req,
			err,
		)
	}
	out, err := l.SvcCtx.BlackBoxCaseSessionServiceRpc.GetBlackBoxCaseConversationAIState(l.Ctx, in)
	if err != nil {
		return nil, err
	}
	resp = &types.GetBlackBoxCaseConversationAIStateResp{}
	if err = utils.Copy(resp, out, l.Converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy data[%+v] to response, error: %+v",
			out, err,
		)
	}
	return resp, nil
}
