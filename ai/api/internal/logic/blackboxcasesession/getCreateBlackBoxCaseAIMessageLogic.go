package blackboxcasesession

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/types"
)

type GetCreateBlackBoxCaseAIMessageLogic struct {
	*logic.BaseLogic
}

// NewGetCreateBlackBoxCaseAIMessageLogic get black box case ai message
func NewGetCreateBlackBoxCaseAIMessageLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetCreateBlackBoxCaseAIMessageLogic {
	return &GetCreateBlackBoxCaseAIMessageLogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),
	}
}

func (l *GetCreateBlackBoxCaseAIMessageLogic) GetCreateBlackBoxCaseAIMessage(req *types.GetCreateBlackBoxCaseAIMessageReq) (
	resp *types.GetCreateBlackBoxCaseAIMessageResp, err error) {
	in := &pb.GetCreateBlackBoxCaseAIMessageReq{}
	if err = utils.Copy(in, req, l.Converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy data[%+v] to request, error: %+v", req,
			err,
		)
	}
	out, err := l.SvcCtx.BlackBoxCaseSessionServiceRpc.GetBlackBoxCaseAIMessage(l.Ctx, in)
	if err != nil {
		return nil, err
	}
	resp = &types.GetCreateBlackBoxCaseAIMessageResp{}
	if err = utils.Copy(resp, out, l.Converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy data[%+v] to response, error: %+v",
			out, err,
		)
	}
	return resp, nil
}
