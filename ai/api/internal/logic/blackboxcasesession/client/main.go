package main

import (
	"context"
	"fmt"
	"log"
	"net/http"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/jsonx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sse"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/types"
)

type Man struct {
	Name string `json:"name"`
	Age  string `json:"age"`
}

func main() {
	var err error
	client := sse.NewClient("")
	err = client.SubscribeWithContext(
		context.Background(), req(), func(event *sse.Event) {
			fmt.Println(string(event.ID))
			fmt.Println(string(event.Data))
		},
	)
	if err != nil {
		panic(err)
	}
	fmt.Println("结束1")

	events := make(chan *sse.Event)
	client2 := sse.NewClient("")
	err = client2.SubscribeChanWithContext(
		context.Background(), req(), events,
	)
	if err != nil {
		log.Fatal(err)
	}
	after := time.After(time.Minute)
	for {
		select {
		case <-after:
			fmt.Println("结束2")
			return
		case event := <-events:
			fmt.Println(string(event.ID))
			fmt.Println(string(event.Data))
		}
	}
}

func req() *http.Request {
	// url := "http://probe-test.ttyuyin.com/"
	url := "http://localhost:21001/"
	m := new(types.SendBlackBoxCaseForSessionReq)
	m.ProjectId = "project_id:129287374222"
	m.AssistantId = "AssistantID：19238376737"
	m.SessionId = "SessionID：91817237723"
	m.Message = "你好"
	m.ConversationId = "ConversationId：12038737373773"
	marshal, _ := jsonx.Marshal(m)
	req, _ := http.NewRequestWithContext(
		context.Background(),
		http.MethodPost, url+"ai/v1/black/box/case/for_session/send",
		strings.NewReader(string(marshal)),
	)

	req.Header.Set("Content-Type", "application/json")
	// req.Header.Set("Accept", "text/event-stream")
	req.Header.Set(
		"X-Auth",
		"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE3MTU1ODY0NjksIm5iZiI6MTcxNDk4MTY2OSwiaWF0IjoxNzE0OTgxNjY5LCJqdGkiOiJ3ZEtfUXF1OHhseWpBRjJxaFpjVWMiLCJhY2NvdW50IjoiVDQ0NDAiLCJmdWxsbmFtZSI6IuadqOWNmiIsImRlcHRfbmFtZSI6IuW5s-WPsOeglOWPkee7hCIsImVtYWlsIjoieWFuZ2JvQDUydHQuY29tIiwibW9iaWxlIjoiIiwicmVmcmVzaF9hZnRlciI6MTcxNTU4NjQ2OH0.GLTwLK9XnYwGjP7L0Bd_v1BdaHMBpJsmU_l1372j5kc",
	)
	return req
}
