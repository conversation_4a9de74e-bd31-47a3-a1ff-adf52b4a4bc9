package blackboxcasesession

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
)

type DeleteBlackBoxCaseSessionLogic struct {
	*logic.BaseLogic
}

// delete black box case session
func NewDeleteBlackBoxCaseSessionLogic(
	ctx context.Context, svcCtx *svc.ServiceContext,
) *DeleteBlackBoxCaseSessionLogic {
	return &DeleteBlackBoxCaseSessionLogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),
	}
}

func (l *DeleteBlackBoxCaseSessionLogic) DeleteBlackBoxCaseSession(req *types.DeleteBlackBoxCaseSessionReq) (
	resp *types.DeleteBlackBoxCaseSessionResp, err error,
) {
	in := &pb.DeleteBlackBoxCaseSessionReq{}
	if err = utils.Copy(in, req, l.Converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy data[%+v] to request, error: %+v", req,
			err,
		)
	}
	_, err = l.SvcCtx.BlackBoxCaseSessionServiceRpc.DeleteBlackBoxCaseSession(l.Ctx, in)
	if err != nil {
		return nil, err
	}
	return &types.DeleteBlackBoxCaseSessionResp{}, nil
}
