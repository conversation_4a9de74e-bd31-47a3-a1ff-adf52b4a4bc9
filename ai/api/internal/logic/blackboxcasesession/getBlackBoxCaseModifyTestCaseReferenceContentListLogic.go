package blackboxcasesession

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/types"
)

type GetBlackBoxCaseModifyTestCaseReferenceContentListLogic struct {
	*logic.BaseLogic
}

// NewGetBlackBoxCaseModifyTestCaseReferenceContentListLogic get black box case modify reference content list
func NewGetBlackBoxCaseModifyTestCaseReferenceContentListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetBlackBoxCaseModifyTestCaseReferenceContentListLogic {
	return &GetBlackBoxCaseModifyTestCaseReferenceContentListLogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),
	}
}

func (l *GetBlackBoxCaseModifyTestCaseReferenceContentListLogic) GetBlackBoxCaseModifyTestCaseReferenceContentList(req *types.GetBlackBoxCaseModifyTestCaseReferenceContentListReq) (resp *types.GetBlackBoxCaseModifyTestCaseReferenceContentListResp, err error) {
	in := &pb.GetBlackBoxCaseModifyTestCaseReferenceContentListReq{}
	if err = utils.Copy(in, req, l.Converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy data[%+v] to request, error: %+v", req,
			err,
		)
	}
	out, err := l.SvcCtx.BlackBoxCaseSessionServiceRpc.GetBlackBoxCaseModifyTestCaseReferenceContentList(l.Ctx, in)
	if err != nil {
		return nil, err
	}
	resp = &types.GetBlackBoxCaseModifyTestCaseReferenceContentListResp{}
	if err = utils.Copy(resp, out, l.Converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy data[%+v] to response, error: %+v",
			out, err,
		)
	}
	return resp, nil
}
