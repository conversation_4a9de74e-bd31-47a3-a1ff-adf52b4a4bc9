package blackboxcasesession

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
)

type UpdateBlackBoxCaseSessionLogic struct {
	*logic.BaseLogic
}

// update black box case session
func NewUpdateBlackBoxCaseSessionLogic(
	ctx context.Context, svcCtx *svc.ServiceContext,
) *UpdateBlackBoxCaseSessionLogic {
	return &UpdateBlackBoxCaseSessionLogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),
	}
}

func (l *UpdateBlackBoxCaseSessionLogic) UpdateBlackBoxCaseSession(req *types.UpdateBlackBoxCaseSessionReq) (
	resp *types.UpdateBlackBoxCaseSessionResp, err error,
) {
	in := &pb.UpdateBlackBoxCaseSessionReq{}
	if err = utils.Copy(in, req, l.Converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy data[%+v] to request, error: %+v", req,
			err,
		)
	}
	_, err = l.SvcCtx.BlackBoxCaseSessionServiceRpc.UpdateBlackBoxCaseSession(l.Ctx, in)
	if err != nil {
		return nil, err
	}
	return &types.UpdateBlackBoxCaseSessionResp{}, nil
}
