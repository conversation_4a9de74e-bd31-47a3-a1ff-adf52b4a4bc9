package blackboxcasesession

import (
	"context"
	"fmt"
	"strconv"
	"time"

	"github.com/zeromicro/go-zero/core/jsonx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sse"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
)

type SendBlackBoxCaseForSessionLogic struct {
	*logic.BaseLogic
}

// NewSendBlackBoxCaseForSessionLogic send black box case for session
func NewSendBlackBoxCaseForSessionLogic(
	ctx context.Context, svcCtx *svc.ServiceContext,
) *SendBlackBoxCaseForSessionLogic {
	return &SendBlackBoxCaseForSessionLogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),
	}
}

func (l *SendBlackBoxCaseForSessionLogic) SendBlackBoxCaseForSession(req *types.SendBlackBoxCaseForSessionReq) (
	resp *sse.Stream, err error,
) {
	stream := sse.NewStream(strconv.Itoa(int(time.Now().UnixMilli())), 0)
	_, err = l.SvcCtx.BlackBoxCaseSessionServiceRpc.SendBlackBoxCaseForSession(
		l.Ctx, &pb.SendBlackBoxCaseForSessionReq{
			ProjectId:      req.ProjectId,
			AssistantId:    req.AssistantId,
			SessionId:      req.SessionId,
			Message:        "123",
			ConversationId: req.ConversationId,
		},
	)
	if err != nil {
		return nil, err
	}
	// 通道用于停止事件发送
	stopChan := make(chan struct{})
	go func() {
		defer func() {
			stream.Quit()
		}()
		tmp := &types.SendBlackBoxCaseForSessionResp{
			Event: "start",
		}
		stream.Event <- &sse.Event{
			ID:   []byte(stream.ID),
			Data: jsonx.MarshalIgnoreError(tmp),
		}
		// 每秒发送一次数据
		ticker := time.NewTicker(1 * time.Second)
		defer ticker.Stop()
		count := 1
		for {
			select {
			case <-l.Ctx.Done():
				fmt.Println("SendBlackBoxCaseForSession：用户中断")
				close(stopChan) // todo
				// close(respCh)
				return
			case <-ticker.C:
				if count > 10 {
					close(stopChan)
				}
				// 向客户端发送新的数据
				tmp := &types.SendBlackBoxCaseForSessionResp{
					Message: &types.BlackBoxCaseSessionMessage{
						Content: types.BlackBoxCaseSessionMsgContent{
							Human: "",
							Ai:    "是的我很好",
						},
						Metadata: types.BlackBoxCaseSessionMsgMetadata{
							ContentType:    "table",
							SessionId:      "123123asdadasds",
							CreatedAt:      "19:20",
							ConversationId: "9aaa91918",
						},
					},
					Event: "running",
				}
				stream.Event <- &sse.Event{
					ID:   []byte(stream.ID),
					Data: jsonx.MarshalIgnoreError(tmp),
				}
				count++
			case <-stopChan:
				// 停止事件发送
				tmp := &types.SendBlackBoxCaseForSessionResp{
					Event: "done",
				}
				stream.Event <- &sse.Event{
					ID:   []byte(stream.ID),
					Data: jsonx.MarshalIgnoreError(tmp),
				}
				return
			}
		}
	}()
	// 以上逻辑用于模拟外部接口返回
	// todo
	return stream, err
}
