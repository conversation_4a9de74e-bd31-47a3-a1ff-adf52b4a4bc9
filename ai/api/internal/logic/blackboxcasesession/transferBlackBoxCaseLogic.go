package blackboxcasesession

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/types"
)

type TransferBlackBoxCaseLogic struct {
	*logic.BaseLogic
}

// NewTransferBlackBoxCaseLogic transfer black box case
func NewTransferBlackBoxCaseLogic(ctx context.Context, svcCtx *svc.ServiceContext) *TransferBlackBoxCaseLogic {
	return &TransferBlackBoxCaseLogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),
	}
}

func (l *TransferBlackBoxCaseLogic) TransferBlackBoxCase(req *types.TransferBlackBoxCase2XMindReq) (
	resp *types.TransferBlackBoxCase2XMindResp, err error) {
	in := &pb.TransferBlackBoxCase2XMindReq{}
	if err = utils.Copy(in, req, l.Converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy data[%+v] to request, error: %+v", req,
			err,
		)
	}
	out, err := l.SvcCtx.BlackBoxCaseSessionServiceRpc.TransferBlackBoxCase2XMind(l.Ctx, in)
	if err != nil {
		return nil, err
	}
	resp = &types.TransferBlackBoxCase2XMindResp{}
	if err = utils.Copy(resp, out, l.Converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy data[%+v] to response, error: %+v",
			out, err,
		)
	}
	return resp, nil
}
