package blackboxcasesession

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/types"
)

type MergeBlackBoxCaseLogic struct {
	*logic.BaseLogic
}

// merge black box case
func NewMergeBlackBoxCaseLogic(ctx context.Context, svcCtx *svc.ServiceContext) *MergeBlackBoxCaseLogic {
	return &MergeBlackBoxCaseLogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),
	}
}

func (l *MergeBlackBoxCaseLogic) MergeBlackBoxCase(req *types.MergeBlackBoxCaseReq) (
	resp *types.MergeBlackBoxCaseResp, err error) {
	in := &pb.MergeBlackBoxCaseReq{}
	if err = utils.Copy(in, req, l.Converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy data[%+v] to request, error: %+v", req,
			err,
		)
	}

	out, err := l.SvcCtx.BlackBoxCaseSessionServiceRpc.MergeBlackBoxCase(l.Ctx, in)
	if err != nil {
		return nil, err
	}
	resp = &types.MergeBlackBoxCaseResp{}
	if err = utils.Copy(resp, out, l.Converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy data[%+v] to response, error: %+v",
			out, err,
		)
	}
	return resp, nil
}
