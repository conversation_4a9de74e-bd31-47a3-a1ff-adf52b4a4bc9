package blackboxcasesession

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
)

type RemoveBlackBoxCaseSessionHistoryMessageListLogic struct {
	*logic.BaseLogic
}

// remove black box case session history message list
func NewRemoveBlackBoxCaseSessionHistoryMessageListLogic(
	ctx context.Context, svcCtx *svc.ServiceContext,
) *RemoveBlackBoxCaseSessionHistoryMessageListLogic {
	return &RemoveBlackBoxCaseSessionHistoryMessageListLogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),
	}
}

func (l *RemoveBlackBoxCaseSessionHistoryMessageListLogic) RemoveBlackBoxCaseSessionHistoryMessageList(req *types.RemoveBlackBoxCaseSessionHistoryMessageListReq) (
	resp *types.RemoveBlackBoxCaseSessionHistoryMessageListResp, err error,
) {
	in := &pb.RemoveBlackBoxCaseSessionHistoryMessageListReq{}
	if err = utils.Copy(in, req, l.Converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy data[%+v] to request, error: %+v", req,
			err,
		)
	}
	_, err = l.SvcCtx.BlackBoxCaseSessionServiceRpc.RemoveBlackBoxCaseSessionHistoryMessageList(l.Ctx, in)
	if err != nil {
		return nil, err
	}
	return &types.RemoveBlackBoxCaseSessionHistoryMessageListResp{}, nil
}
