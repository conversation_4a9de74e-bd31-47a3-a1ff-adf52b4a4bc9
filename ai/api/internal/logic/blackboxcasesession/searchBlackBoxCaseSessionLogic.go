package blackboxcasesession

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
)

type SearchBlackBoxCaseSessionLogic struct {
	*logic.BaseLogic
}

// search black box case session
func NewSearchBlackBoxCaseSessionLogic(
	ctx context.Context, svcCtx *svc.ServiceContext,
) *SearchBlackBoxCaseSessionLogic {
	return &SearchBlackBoxCaseSessionLogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),
	}
}

func (l *SearchBlackBoxCaseSessionLogic) SearchBlackBoxCaseSession(req *types.SearchBlackBoxCaseSessionReq) (
	resp *types.SearchBlackBoxCaseSessionResp, err error,
) {
	in := &pb.SearchBlackBoxCaseSessionReq{}
	if err = utils.Copy(in, req, l.Converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy data[%+v] to request, error: %+v", req,
			err,
		)
	}
	out, err := l.SvcCtx.BlackBoxCaseSessionServiceRpc.SearchBlackBoxCaseSession(l.Ctx, in)
	if err != nil {
		return nil, err
	}
	resp = &types.SearchBlackBoxCaseSessionResp{Items: []*types.BlackBoxCaseSession{}}
	if err = utils.Copy(resp, out, l.Converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy data[%+v] to response, error: %+v",
			out, err,
		)
	}
	return resp, nil
}
