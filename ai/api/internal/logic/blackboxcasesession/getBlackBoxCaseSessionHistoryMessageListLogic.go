package blackboxcasesession

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
)

type GetBlackBoxCaseSessionHistoryMessageListLogic struct {
	*logic.BaseLogic
}

// get black box case session history message list
func NewGetBlackBoxCaseSessionHistoryMessageListLogic(
	ctx context.Context, svcCtx *svc.ServiceContext,
) *GetBlackBoxCaseSessionHistoryMessageListLogic {
	return &GetBlackBoxCaseSessionHistoryMessageListLogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),
	}
}

func (l *GetBlackBoxCaseSessionHistoryMessageListLogic) GetBlackBoxCaseSessionHistoryMessageList(req *types.GetBlackBoxCaseSessionHistoryMessageListReq) (
	resp *types.GetBlackBoxCaseSessionHistoryMessageListResp, err error,
) {
	in := &pb.GetBlackBoxCaseSessionHistoryMessageListReq{}
	if err = utils.Copy(in, req, l.Converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy data[%+v] to request, error: %+v", req,
			err,
		)
	}
	out, err := l.SvcCtx.BlackBoxCaseSessionServiceRpc.GetBlackBoxCaseSessionHistoryMessageList(l.Ctx, in)
	if err != nil {
		return nil, err
	}
	resp = &types.GetBlackBoxCaseSessionHistoryMessageListResp{Messages: []*types.BlackBoxCaseSessionMessage{}}
	if err = utils.Copy(resp, out, l.Converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy data[%+v] to response, error: %+v",
			out, err,
		)
	}
	return resp, nil
}
