package blackboxcasesession

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
)

type GetBlackBoxCaseSessionLogic struct {
	*logic.BaseLogic
}

// get black box case session
func NewGetBlackBoxCaseSessionLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetBlackBoxCaseSessionLogic {
	return &GetBlackBoxCaseSessionLogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),
	}
}

func (l *GetBlackBoxCaseSessionLogic) GetBlackBoxCaseSession(req *types.GetBlackBoxCaseSessionReq) (
	resp *types.GetBlackBoxCaseSessionResp, err error,
) {
	in := &pb.GetBlackBoxCaseSessionReq{}
	if err = utils.Copy(in, req, l.Converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy data[%+v] to request, error: %+v", req,
			err,
		)
	}
	out, err := l.SvcCtx.BlackBoxCaseSessionServiceRpc.GetBlackBoxCaseSession(l.Ctx, in)
	if err != nil {
		return nil, err
	}
	resp = &types.GetBlackBoxCaseSessionResp{Item: &types.BlackBoxCaseSession{}}
	if out.GetItem() != nil {
		if err = utils.Copy(resp.Item, out.GetItem(), l.Converters...); err != nil {
			return nil, errors.Wrapf(
				errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy data[%+v] to response, error: %+v",
				out, err,
			)
		}
	}
	return resp, nil
}
