package blackboxcasesession

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
)

type CreateBlackBoxCaseSessionLogic struct {
	*logic.BaseLogic
}

// create black box case session
func NewCreateBlackBoxCaseSessionLogic(
	ctx context.Context, svcCtx *svc.ServiceContext,
) *CreateBlackBoxCaseSessionLogic {
	return &CreateBlackBoxCaseSessionLogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),
	}
}

func (l *CreateBlackBoxCaseSessionLogic) CreateBlackBoxCaseSession(req *types.CreateBlackBoxCaseSessionReq) (
	resp *types.CreateBlackBoxCaseSessionResp, err error,
) {
	in := &pb.CreateBlackBoxCaseSessionReq{}
	if err = utils.Copy(in, req, l.Converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy data[%+v] to request, error: %+v", req,
			err,
		)
	}
	out, err := l.SvcCtx.BlackBoxCaseSessionServiceRpc.CreateBlackBoxCaseSession(l.Ctx, in)
	if err != nil {
		return nil, err
	}
	return &types.CreateBlackBoxCaseSessionResp{
		ProjectId:   out.GetProjectId(),
		AssistantId: out.GetAssistantId(),
		SessionId:   out.GetSessionId(),
	}, nil
}
