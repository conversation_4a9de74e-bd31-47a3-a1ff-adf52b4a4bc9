package blackboxcasesession

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/types"
)

type CreateBlackBoxCaseModifyTestCaseReferenceContentLogic struct {
	*logic.BaseLogic
}

// create black box case modify reference content
func NewCreateBlackBoxCaseModifyTestCaseReferenceContentLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CreateBlackBoxCaseModifyTestCaseReferenceContentLogic {
	return &CreateBlackBoxCaseModifyTestCaseReferenceContentLogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),
	}
}

func (l *CreateBlackBoxCaseModifyTestCaseReferenceContentLogic) CreateBlackBoxCaseModifyTestCaseReferenceContent(
	req *types.CreateBlackBoxCaseModifyTestCaseReferenceContentReq) (resp *types.CreateBlackBoxCaseModifyTestCaseReferenceContentResp, err error) {
	in := &pb.CreateBlackBoxCaseModifyTestCaseReferenceContentReq{}
	if err = utils.Copy(in, req, l.Converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy data[%+v] to request, error: %+v", req,
			err,
		)
	}
	_, err = l.SvcCtx.BlackBoxCaseSessionServiceRpc.CreateBlackBoxCaseModifyTestCaseReferenceContent(l.Ctx, in)
	if err != nil {
		return nil, err
	}
	return &types.CreateBlackBoxCaseModifyTestCaseReferenceContentResp{}, nil
}
