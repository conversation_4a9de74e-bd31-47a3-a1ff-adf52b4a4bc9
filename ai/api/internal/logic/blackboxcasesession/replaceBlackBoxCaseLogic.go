package blackboxcasesession

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/types"
)

type ReplaceBlackBoxCaseLogic struct {
	*logic.BaseLogic
}

// NewReplaceBlackBoxCaseLogic replace black box case
func NewReplaceBlackBoxCaseLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ReplaceBlackBoxCaseLogic {
	return &ReplaceBlackBoxCaseLogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),
	}
}

func (l *ReplaceBlackBoxCaseLogic) ReplaceBlackBoxCase(req *types.ReplaceBlackBoxCaseReq) (
	resp *types.ReplaceBlackBoxCaseResp, err error) {
	in := &pb.ReplaceBlackBoxCaseReq{}
	if err = utils.Copy(in, req, l.Converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy data[%+v] to request, error: %+v", req,
			err,
		)
	}
	_, err = l.SvcCtx.BlackBoxCaseSessionServiceRpc.ReplaceBlackBoxCase(l.Ctx, in)
	if err != nil {
		return nil, err
	}
	return &types.ReplaceBlackBoxCaseResp{}, nil
}
