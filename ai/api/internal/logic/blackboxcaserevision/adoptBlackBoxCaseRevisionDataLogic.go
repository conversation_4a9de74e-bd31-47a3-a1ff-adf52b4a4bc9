package blackboxcaserevision

import (
	"context"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
)

type AdoptBlackBoxCaseRevisionDataLogic struct {
	*logic.BaseLogic
}

func NewAdoptBlackBoxCaseRevisionDataLogic(ctx context.Context, svcCtx *svc.ServiceContext) *AdoptBlackBoxCaseRevisionDataLogic {
	return &AdoptBlackBoxCaseRevisionDataLogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),
	}
}

func (l *AdoptBlackBoxCaseRevisionDataLogic) AdoptBlackBoxCaseRevisionData(req *types.AdoptBlackBoxCaseRevisionDataReq) (resp *types.AdoptBlackBoxCaseRevisionDataResp, err error) {
	in := &pb.AdoptBlackBoxCaseRevisionReq{
		RevisionId: req.RevisionId,
	}
	_, err = l.SvcCtx.BlackBoxCaseRevisionServiceRpc.AdoptBlackBoxCaseRevision(l.Ctx, in)
	if err != nil {
		return nil, err
	}

	return &types.AdoptBlackBoxCaseRevisionDataResp{}, nil
}
