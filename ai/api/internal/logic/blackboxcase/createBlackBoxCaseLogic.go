package blackboxcase

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"
)

type CreateBlackBoxCaseLogic struct {
	*logic.BaseLogic
}

func NewCreateBlackBoxCaseLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CreateBlackBoxCaseLogic {
	return &CreateBlackBoxCaseLogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),
	}
}

func (l *CreateBlackBoxCaseLogic) CreateBlackBoxCase(req *types.CreateBlackBoxCaseReq) (resp *types.CreateBlackBoxCaseResp, err error) {
	in := &pb.CreateBlackBoxCaseReq{}
	if err = utils.Copy(in, req, l.Converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy data[%+v] to request, error: %+v", req,
			err,
		)
	}
	out, err := l.SvcCtx.BlackBoxCaseServiceRpc.CreateBlackBoxCase(l.Ctx, in)
	if err != nil {
		return nil, err
	}
	resp = &types.CreateBlackBoxCaseResp{CaseId: out.CaseId}

	return resp, nil
}
