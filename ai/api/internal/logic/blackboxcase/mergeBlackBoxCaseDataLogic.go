package blackboxcase

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"
)

type MergeBlackBoxCaseDataLogic struct {
	*logic.BaseLogic
}

func NewMergeBlackBoxCaseDataLogic(ctx context.Context, svcCtx *svc.ServiceContext) *MergeBlackBoxCaseDataLogic {
	return &MergeBlackBoxCaseDataLogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),
	}
}

func (l *MergeBlackBoxCaseDataLogic) MergeBlackBoxCaseData(req *types.MergeBlackBoxCaseDataReq) (resp *types.MergeBlackBoxCaseDataResp, err error) {
	in := &pb.MergeBlackBoxCaseDataReq{}
	if err = utils.Copy(in, req, l.Converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy data[%+v] to request, error: %+v", req,
			err,
		)
	}

	out, err := l.SvcCtx.BlackBoxCaseServiceRpc.MergeBlackBoxCaseData(l.Ctx, in)
	if err != nil {
		return nil, err
	}
	resp = &types.MergeBlackBoxCaseDataResp{CaseId: out.CaseId}
	if err = utils.Copy(resp, out, l.Converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy data[%+v] to response, error: %+v",
			out, err,
		)
	}

	return resp, nil
}
