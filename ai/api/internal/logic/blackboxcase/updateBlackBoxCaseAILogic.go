package blackboxcase

import (
	"context"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"github.com/pkg/errors"
)

type UpdateBlackBoxCaseAILogic struct {
	*logic.BaseLogic
}

func NewUpdateBlackBoxCaseAILogic(ctx context.Context, svcCtx *svc.ServiceContext) *UpdateBlackBoxCaseAILogic {
	return &UpdateBlackBoxCaseAILogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),
	}
}

func (l *UpdateBlackBoxCaseAILogic) UpdateBlackBoxCaseAI(req *types.UpdateBlackBoxCaseAIReq) (resp *types.UpdateBlackBoxCaseAIResp, err error) {
	in := &pb.UpdateBlackBoxCaseAIReq{}
	if err = utils.Copy(in, req, l.Converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy data[%+v] to request, error: %+v", req,
			err,
		)
	}

	_, err = l.SvcCtx.BlackBoxCaseServiceRpc.UpdateBlackBoxCaseAI(l.Ctx, in)
	if err != nil {
		return nil, err
	}

	return &types.UpdateBlackBoxCaseAIResp{}, nil
}
