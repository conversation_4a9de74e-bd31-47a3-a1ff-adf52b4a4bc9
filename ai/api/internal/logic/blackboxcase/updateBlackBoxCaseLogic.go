package blackboxcase

import (
	"context"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
)

type UpdateBlackBoxCaseLogic struct {
	*logic.BaseLogic
}

func NewUpdateBlackBoxCaseLogic(ctx context.Context, svcCtx *svc.ServiceContext) *UpdateBlackBoxCaseLogic {
	return &UpdateBlackBoxCaseLogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),
	}
}

func (l *UpdateBlackBoxCaseLogic) UpdateBlackBoxCase(req *types.UpdateBlackBoxCaseItemReq) (resp *types.UpdateBlackBoxCaseItemResp, err error) {
	knowledgeParagraphTitles := make([]*pb.KnowledgeDocPgTitle, 0, len(req.KnowledgeDocPgTitle))
	for _, title := range req.KnowledgeDocPgTitle {
		knowledgeParagraphTitles = append(knowledgeParagraphTitles, &pb.KnowledgeDocPgTitle{
			DemandPoints: title.DemandPoints,
			Id:           title.Id,
			Title:        title.Title,
		})
	}

	in := &pb.UpdateBlackBoxCaseReq{
		Item: &pb.BlackBoxCase{
			DirId:                   req.DirId,
			CaseId:                  req.CaseId,
			CaseName:                req.CaseName,
			CaseContinueToWrite:     req.CaseContinueToWrite,
			CaseModelCharacter:      req.CaseModelCharacter,
			CaseRemarks:             req.CaseRemarks,
			KnowledgeId:             req.KnowledgeId,
			KnowledgeParagraphTitle: knowledgeParagraphTitles,
		},
	}

	_, err = l.SvcCtx.BlackBoxCaseServiceRpc.UpdateBlackBoxCase(l.Ctx, in)
	if err != nil {
		return nil, err
	}

	return &types.UpdateBlackBoxCaseItemResp{}, nil
}
