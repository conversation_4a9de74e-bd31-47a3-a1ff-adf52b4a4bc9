package blackboxcasedata

import (
	"context"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"github.com/pkg/errors"
)

type BatchUpdateBlackBoxCaseDataLogic struct {
	*logic.BaseLogic
}

func NewBatchUpdateBlackBoxCaseDataLogic(ctx context.Context, svcCtx *svc.ServiceContext) *BatchUpdateBlackBoxCaseDataLogic {
	return &BatchUpdateBlackBoxCaseDataLogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),
	}
}

func (l *BatchUpdateBlackBoxCaseDataLogic) BatchUpdateBlackBoxCaseData(req *types.BatchUpdateBlackBoxCaseDataReq) (resp *types.BatchUpdateBlackBoxCaseDataResp, err error) {
	in := &pb.BatchUpdateBlackBoxCaseDataReq{}
	if err = utils.Copy(in, req, l.Converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy data[%+v] to request, error: %+v", req,
			err,
		)
	}

	_, err = l.SvcCtx.BlackBoxCaseDataServiceRpc.BatchUpdateBlackBoxCaseData(l.Ctx, in)
	if err != nil {
		return nil, err
	}

	return &types.BatchUpdateBlackBoxCaseDataResp{}, nil
}
