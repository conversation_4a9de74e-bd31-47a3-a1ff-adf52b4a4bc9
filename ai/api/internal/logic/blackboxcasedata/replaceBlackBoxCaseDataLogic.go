package blackboxcasedata

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"
)

type ReplaceBlackBoxCaseDataLogic struct {
	*logic.BaseLogic
}

func NewReplaceBlackBoxCaseDataLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ReplaceBlackBoxCaseDataLogic {
	return &ReplaceBlackBoxCaseDataLogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),
	}
}

func (l *ReplaceBlackBoxCaseDataLogic) ReplaceBlackBoxCaseData(req *types.ReplaceBlackBoxCaseDataReq) (resp *types.ReplaceBlackBoxCaseDataResp, err error) {
	in := &pb.ReplaceBlackBoxCaseDataReq{}
	if err = utils.Copy(in, req, l.Converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy data[%+v] to request, error: %+v", req,
			err,
		)
	}

	out, err := l.SvcCtx.BlackBoxCaseDataServiceRpc.ReplaceBlackBoxCaseData(l.Ctx, in)
	if err != nil {
		return nil, err
	}

	resp = &types.ReplaceBlackBoxCaseDataResp{Items: []*types.BlackBoxCaseData{}, NewItems: []*types.BlackBoxCaseData{}}
	if err = utils.Copy(resp, out, l.Converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy data[%+v] to response, error: %+v",
			out, err,
		)
	}
	return resp, nil
}
