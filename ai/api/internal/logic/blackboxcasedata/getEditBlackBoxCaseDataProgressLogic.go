package blackboxcasedata

import (
	"context"
	"strconv"
	"time"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/public/httpc"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sse"
)

type GetEditBlackBoxCaseDataProgressLogic struct {
	*logic.BaseLogic
}

func NewGetEditBlackBoxCaseDataProgressLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetEditBlackBoxCaseDataProgressLogic {
	return &GetEditBlackBoxCaseDataProgressLogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),
	}
}

func (l *GetEditBlackBoxCaseDataProgressLogic) GetEditBlackBoxCaseDataProgress(req *types.GetEditBlackBoxCaseDataProgressReq) (resp *sse.Stream, err error) {
	stream := sse.NewStream(strconv.Itoa(int(time.Now().UnixMilli())), 0)
	stopChan := make(chan error)

	go func() {
		defer func() {
			if err := recover(); err != nil {
				l.Error(err)
			}

			stream.Quit()
			close(stopChan)
		}()

		errChan := make(chan httpc.ResponseWithErrors)

		go func() {
			defer func() {
				if err := recover(); err != nil {
					l.Error(err)
					errChan <- httpc.ResponseWithErrors{Err: err.(error)}
				}
			}()
			errChan <- httpc.GetEditTestCaseDataProgress(l.SvcCtx.ExternalAiDomain, req.CaseRefId, stream)
		}()

		var resErr httpc.ResponseWithErrors
		select {
		case <-l.Ctx.Done():
			l.Error("客户端断开...")
		case resErr = <-errChan:
			if resErr.Err != nil {
				l.Error("流异常:", err)
				stream.Quit()
				close(stopChan)
			}
			if resErr.Stop != nil {
				println("下游流正常结束")
			}
		case <-stopChan:
		}
	}()
	return stream, err
}
