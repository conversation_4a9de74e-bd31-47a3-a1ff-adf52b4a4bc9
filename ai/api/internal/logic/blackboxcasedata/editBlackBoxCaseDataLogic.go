package blackboxcasedata

import (
	"context"
	"encoding/json"
	"strconv"
	"time"

	"github.com/zeromicro/go-zero/core/jsonx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sse"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/metrics"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/public"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/public/httpc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
)

type EditBlackBoxCaseDataLogic struct {
	*logic.BaseLogic
}

func NewEditBlackBoxCaseDataLogic(ctx context.Context, svcCtx *svc.ServiceContext) *EditBlackBoxCaseDataLogic {
	return &EditBlackBoxCaseDataLogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),
	}
}

func (l *EditBlackBoxCaseDataLogic) EditBlackBoxCaseData(req *types.EditBlackBoxCaseDataReq) (
	resp *sse.Stream, err error,
) {
	stream := sse.NewStream(strconv.Itoa(int(time.Now().UnixMilli())), 0)
	stopChan := make(chan struct{})

	// 获取用例详情,生成用例需要传入ai属性
	in := &pb.GetBlackBoxCaseReq{CaseId: req.CaseId}
	caseDetail, err := l.SvcCtx.BlackBoxCaseServiceRpc.GetBlackBoxCase(l.Ctx, in)
	if err != nil {
		return stream, err
	}

	// 生成case_ref_id用于临时id记录
	caseRefType := "edit"
	refIn := &pb.GenerateBlackBoxCaseRefReq{CaseRefType: caseRefType}
	caseRef, err := l.SvcCtx.BlackBoxCaseDataServiceRpc.GenerateBlackBoxCaseRef(l.Ctx, refIn)
	if err != nil {
		return stream, err
	}

	// 刷新metrics
	l.SvcCtx.BlackBoxCaseDataServiceRpc.RefreshCaseRefMetrics(
		l.Ctx, &pb.RefreshCaseRefMetricsReq{CaseRefType: caseRefType},
	)

	// 提前生成revision给到前端查询当前生成用例对应的版本
	generateRevision, err := l.SvcCtx.BlackBoxCaseRevisionServiceRpc.GenerateBlackBoxCaseRevisionId(
		l.Ctx, &pb.GenerateBlackBoxCaseRevisionIdReq{},
	)
	if err != nil {
		return stream, err
	}

	caseDataList, err := l.SvcCtx.BlackBoxCaseDataServiceRpc.ListBlackBoxCaseData(
		l.Ctx, &pb.ListBlackBoxCaseDataReq{
			RevisionId: req.RevisionId,
		},
	)
	if err != nil {
		return stream, err
	}

	stopTimeCh := make(chan struct{})
	go func() {
		defer func() {
			if err := recover(); err != nil {
				l.Error(err)
			}
			stopTimeCh <- struct{}{}
			if err == nil {
				// 结束以后创建新版本
				revisionReq := &pb.CreateBlackBoxCaseRevisionReq{
					CaseId:                      req.CaseId,
					RevisionName:                public.GenerateRevisionName(),
					KnowledgeId:                 req.KnowledgeId,
					KnowledgeParagraphTitleText: req.KnowledgeParagraphTitleText,
					CaseRefId:                   caseRef.CaseRefId,
				}
				revision, err := l.SvcCtx.BlackBoxCaseRevisionServiceRpc.CreateBlackBoxCaseRevision(l.Ctx, revisionReq)
				if err != nil {
					l.Error("生成用例创建版本异常: ", err)
					stream.Quit()
					close(stopChan)
					return
				}

				var caseData httpc.ResponseData[[]*pb.BlackBoxCaseData]
				for i := 0; i < 5; i++ {
					caseData, err = httpc.GetCaseJson(l.SvcCtx.ExternalAiDomain, caseRef.CaseRefId)
					if err != nil {
						l.Info("暂未获取到用例数据: ", err)
					}
					if caseData.Data != nil {
						break
					}
					time.Sleep(2 * time.Second)
				}

				// 用例数据落库
				if caseData.Data != nil {
					dataIn := &pb.CreateBlackBoxCaseDataReq{
						Items: *caseData.Data,
					}
					for i := 0; i < len(dataIn.Items); i++ {
						dataIn.Items[i].RevisionId = revision.RevisionId
					}
					_, err := l.SvcCtx.BlackBoxCaseDataServiceRpc.CreateBlackBoxCaseData(l.Ctx, dataIn)
					if err != nil {
						l.Error("创建用例数据异常: ", err)
						stream.Quit()
						close(stopChan)
						return
					}

					// 更新case_ref的数据统计
					for i := 0; i < 5; i++ {
						caseCount, err := httpc.GetCaseRowCount(l.SvcCtx.ExternalAiDomain, caseRef.CaseRefId)
						if err != nil {
							l.Error("获取case_ref数据统计异常: ", err)
						}
						if *caseCount.Data > 0 {
							err = l.SvcCtx.BlackBoxCaseRefModel.UpdateCountById(
								l.Ctx, nil, caseRef.CaseRefId, *caseCount.Data,
							)
							if err != nil {
								l.Error("更新case_ref数据统计异常: ", err)
							}
							metrics.CaseDataTotalGauge.Add(float64(*caseCount.Data), []string{""}...)
							break
						}
						time.Sleep(1 * time.Second)
					}

					// 修改case状态
					caseItem, err := l.SvcCtx.BlackBoxCaseServiceRpc.GetBlackBoxCase(
						l.Ctx, &pb.GetBlackBoxCaseReq{
							CaseId: req.CaseId,
						},
					)
					if err != nil {
						l.Error("更新case状态异常: ", err)
						stream.Quit()
						close(stopChan)
						return
					}
					refDocs, err := json.Marshal(req.ReferenceDoc)
					if err != nil {
						l.Error("更新case状态序列化json异常: ", err)
						stream.Quit()
						close(stopChan)
						return
					}
					caseItem.Item.KnowledgeFixSugg = req.KnowledgeFixSugg
					caseItem.Item.EnableReference = req.EnableReference
					caseItem.Item.ReferenceDoc = string(refDocs)
					_, err = l.SvcCtx.BlackBoxCaseServiceRpc.UpdateBlackBoxCase(
						l.Ctx, &pb.UpdateBlackBoxCaseReq{
							Item: caseItem.Item,
						},
					)
					if err != nil {
						l.Error("更新case状态数据库异常: ", err)
						stream.Quit()
						close(stopChan)
						return
					}
				} else {
					var res httpc.ResponseData[httpc.SendMsgForSessionResp]
					res.Code = 1
					res.Message = "获取不到本次用例的json数据,请检查是否入库: " + caseRef.CaseRefId
					stream.Event <- &sse.Event{
						ID:   []byte(stream.ID),
						Data: jsonx.MarshalIgnoreError(&res),
					}
					l.Error("获取不到本次用例的json数据,请检查是否入库: " + caseRef.CaseRefId)
				}
				stream.Quit()
				close(stopChan)
			} else {
				l.Error("协程异常: ", err)
			}
		}()

		errChan := make(chan error)

		go func() {
			ticker := time.NewTicker(30 * time.Second)
			defer ticker.Stop()
			for {
				select {
				case <-ticker.C:
					stream.Event <- &sse.Event{
						ID: []byte(stream.ID),
						Data: jsonx.MarshalIgnoreError(
							&httpc.CreateCaseDataResp{
								RevisionId: generateRevision.RevisionId,
								CaseRefId:  caseRef.CaseRefId,
							},
						),
					}
				case <-stopTimeCh:
					// 收到停止信号，退出循环
					return
				}
			}
		}()

		go func() {
			aiReq := &httpc.EditTestCaseDataReq{
				CaseId:                    caseRef.CaseRefId,
				KnowledgeId:               req.KnowledgeId,
				KnowledgeParagraphTitle:   req.KnowledgeParagraphTitleText,
				KnowledgeParagraphTitleId: req.KnowledgeParagraphTitleId,
				Revision:                  generateRevision.RevisionId,
				CaseTable:                 caseDataList.Items,
				Suggestions:               req.KnowledgeFixSugg,
				ReferenceDoc:              map[string]any{},
				ModelCharacter:            caseDetail.Item.CaseModelCharacter,
				ContinueToWrite:           caseDetail.Item.CaseContinueToWrite,
			}
			if len(caseDataList.Items) == 0 {
				aiReq.CaseTable = []string{}
			}
			if req.EnableReference {
				aiReq.ReferenceDoc = req.ReferenceDoc
			}
			errChan <- httpc.EditTestCaseData(l.SvcCtx.ExternalAiDomain, aiReq, stream)
		}()

		select {
		case <-l.Ctx.Done():
			l.Error("客户端断开...")
			stopTimeCh <- struct{}{}
		case err = <-errChan:
			if err != nil {
				l.Error("流异常:", err)
				stream.Quit()
				close(stopChan)
				stopTimeCh <- struct{}{}
			}
		case <-stopChan:
		}
	}()

	return stream, err
}
