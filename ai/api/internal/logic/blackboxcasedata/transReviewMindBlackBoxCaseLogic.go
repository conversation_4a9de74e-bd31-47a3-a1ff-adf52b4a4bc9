package blackboxcasedata

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/public/httpc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"
)

type TransReviewMindBlackBoxCaseLogic struct {
	*logic.BaseLogic
}

func NewTransReviewMindBlackBoxCaseLogic(ctx context.Context, svcCtx *svc.ServiceContext) *TransReviewMindBlackBoxCaseLogic {
	return &TransReviewMindBlackBoxCaseLogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),
	}
}

func (l *TransReviewMindBlackBoxCaseLogic) TransReviewMindBlackBoxCase(req *types.TransBlackBoxCaseDataReq) (resp *types.TransBlackBoxCaseDataResp, err error) {
	in := &pb.GetBlackBoxCaseRevisionReq{}
	if err = utils.Copy(in, req, l.Converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy data[%+v] to request, error: %+v", req,
			err,
		)
	}
	// 获取版本信息
	revision, err := l.SvcCtx.BlackBoxCaseRevisionServiceRpc.GetBlackBoxCaseRevision(l.Ctx, in)
	if err != nil {
		return nil, err
	}
	// 若标题为空传递给AI接口为None
	if len(revision.Item.KnowledgeParagraphTitleText) == 0 {
		revision.Item.KnowledgeParagraphTitleText = "None"
	}

	// 获取版本下case_data数据
	dataIn := &pb.ListBlackBoxCaseDataReq{}
	if err = utils.Copy(dataIn, req, l.Converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy data[%+v] to request, error: %+v", req,
			err,
		)
	}
	caseData, err := l.SvcCtx.BlackBoxCaseDataServiceRpc.ListBlackBoxCaseData(l.Ctx, dataIn)
	if err != nil {
		return nil, err
	}
	if len(caseData.Items) == 0 {
		return nil, errors.New("该版本没有用例数据,请先生成用例数据")
	}

	res, err := httpc.TransReviewMindGraph(l.SvcCtx.ExternalAiDomain, httpc.CaseTableReq{
		KnowledgeParagraphTitle: revision.Item.KnowledgeParagraphTitleText,
		CaseTable:               caseData.Items,
		KnowledgeId:             revision.Item.KnowledgeId,
	})
	if err != nil {
		return nil, err
	}

	// 生成验证数据
	l.SvcCtx.BlackBoxCaseTwBetaServiceRpc.CreateBlackBoxCaseTwBeta(l.Ctx, &pb.CreateBlackBoxCaseTwBetaReq{
		Revision: revision.Item,
		Items:    caseData.Items,
	})

	// 标记数据采纳
	l.SvcCtx.BlackBoxCaseRevisionServiceRpc.AdoptBlackBoxCaseRevision(l.Ctx, &pb.AdoptBlackBoxCaseRevisionReq{RevisionId: in.RevisionId})

	return &types.TransBlackBoxCaseDataResp{Content: *res.Data}, nil
}
