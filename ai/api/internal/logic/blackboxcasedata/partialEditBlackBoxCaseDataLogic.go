package blackboxcasedata

import (
	"context"
	"errors"
	"strconv"
	"time"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sse"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/metrics"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/public/httpc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
)

type PartialEditBlackBoxCaseDataLogic struct {
	*logic.BaseLogic
}

func NewPartialEditBlackBoxCaseDataLogic(
	ctx context.Context, svcCtx *svc.ServiceContext,
) *PartialEditBlackBoxCaseDataLogic {
	return &PartialEditBlackBoxCaseDataLogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),
	}
}

func (l *PartialEditBlackBoxCaseDataLogic) PartialEditBlackBoxCaseData(req *types.PartialEditBlackBoxCaseDataReq) (
	resp *sse.Stream, err error,
) {
	stream := sse.NewStream(strconv.Itoa(int(time.Now().UnixMilli())), 0)
	stopChan := make(chan struct{})

	caseTables := make([]*httpc.BlackBoxCaseData, 0)
	for _, item := range req.Items {
		caseTable := httpc.BlackBoxCaseData{
			OrderId:      item.OrderId,
			CaseName:     item.CaseName,
			Requirement:  item.Requirement,
			PreCondition: item.PreCondition,
			CaseStep:     item.CaseStep,
			ExpectResult: item.ExpectResult,
			Terminal:     item.Terminal,
			CaseLevel:    item.CaseLevel,
			Tag:          item.Tag,
		}
		caseTables = append(caseTables, &caseTable)
	}

	// 生成case_ref_id用于临时id记录
	caseRefType := "partial_edit"
	caseRef, err := l.SvcCtx.BlackBoxCaseDataServiceRpc.GenerateBlackBoxCaseRef(
		l.Ctx, &pb.GenerateBlackBoxCaseRefReq{
			CaseRefType: caseRefType,
		},
	)
	if err != nil {
		return stream, err
	}

	// 刷新metrics
	l.SvcCtx.BlackBoxCaseDataServiceRpc.RefreshCaseRefMetrics(
		l.Ctx, &pb.RefreshCaseRefMetricsReq{CaseRefType: caseRefType},
	)

	// 获取用例详情
	caseDetail, err := l.SvcCtx.BlackBoxCaseServiceRpc.GetBlackBoxCase(
		l.Ctx, &pb.GetBlackBoxCaseReq{
			CaseId: req.CaseId,
		},
	)
	if err != nil {
		return stream, err
	}
	if caseDetail.Item.CaseModelCharacter == "" {
		return stream, errors.New("模型性格不允许为空,请先编辑AI属性")
	}

	// // 获取版本详情
	// revision, err := l.SvcCtx.BlackBoxCaseRevisionServiceRpc.GetBlackBoxCaseRevision(l.Ctx, &pb.GetBlackBoxCaseRevisionReq{
	// 	RevisionId: req.RevisionId,
	// })

	go func() {
		defer func() {
			if err := recover(); err != nil {
				l.Error(err)
			}

			// 更新case_ref的数据统计
			for i := 0; i < 5; i++ {
				caseCount, err := httpc.GetCaseRowCount(l.SvcCtx.ExternalAiDomain, caseRef.CaseRefId)
				if err != nil {
					l.Error("获取case_ref数据统计异常: ", err)
				}
				if *caseCount.Data > 0 {
					err = l.SvcCtx.BlackBoxCaseRefModel.UpdateCountById(l.Ctx, nil, caseRef.CaseRefId, *caseCount.Data)
					if err != nil {
						l.Error("更新case_ref数据统计异常: ", err)
					}
					metrics.CaseDataTotalGauge.Add(float64(*caseCount.Data), []string{""}...)
					break
				}
				time.Sleep(1 * time.Second)
			}

			stream.Quit()
			close(stopChan)
		}()

		errChan := make(chan error)

		go func() {
			aiReq := &httpc.PartialEditTestCaseDataReq{
				CaseId:                    caseRef.CaseRefId,
				KnowledgeId:               caseDetail.Item.KnowledgeId,
				KnowledgeParagraphTitle:   req.KnowledgeParagraphTitleText,
				KnowledgeParagraphTitleId: req.KnowledgeParagraphTitleId,
				Suggestions:               req.KnowledgeFixSugg,
				ContinueToWrite:           caseDetail.Item.CaseContinueToWrite,
				ModelCharacter:            caseDetail.Item.CaseModelCharacter,
				CaseTable:                 caseTables,
				ReferenceDoc:              map[string]any{},
			}
			// if caseDetail.Item.ReferenceDoc != "{}" {
			// 	ref := &types.ReferenceDoc{}
			// 	err = json.Unmarshal([]byte(caseDetail.Item.ReferenceDoc), ref)
			// 	if err != nil {
			// 		l.Error(err)
			// 		return
			// 	}
			// 	req.ReferenceDoc = ref
			// }
			if req.EnableReference {
				aiReq.ReferenceDoc = req.ReferenceDoc
			}
			errChan <- httpc.PartialEditTestCaseData(
				l.SvcCtx.ExternalAiDomain, aiReq, stream,
			)
		}()

		select {
		case err = <-errChan:
			if err != nil {
				l.Error(err)
				stream.Quit()
				close(stopChan)
			}
		case <-stopChan:
		}
	}()

	return stream, err
}
