package blackboxcasedata

import (
	"context"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/public/httpc"
)

type GetBlackBoxCaseAIStateLogic struct {
	*logic.BaseLogic
}

func NewGetBlackBoxCaseAIStateLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetBlackBoxCaseAIStateLogic {
	return &GetBlackBoxCaseAIStateLogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),
	}
}

func (l *GetBlackBoxCaseAIStateLogic) GetBlackBoxCaseAIState(req *types.GetBlackBoxCaseAIStateReq) (resp *types.GetBlackBoxCaseAIStateResp, err error) {
	res, err := httpc.GetAIState(l.SvcCtx.ExternalAiDomain, req.CaseRefId)
	if err != nil {
		return nil, err
	}
	resp = &types.GetBlackBoxCaseAIStateResp{
		ExtractContent: res.Data.ExtractContent,
		State:          res.Data.State,
		StateDesc:      res.Data.StateDesc,
	}
	return resp, nil
}
