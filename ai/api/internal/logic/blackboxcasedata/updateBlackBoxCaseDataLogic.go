package blackboxcasedata

import (
	"context"
	// "github.com/pkg/errors"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"
	// "gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
	// "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	// "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"
)

type UpdateBlackBoxCaseDataLogic struct {
	*logic.BaseLogic
}

func NewUpdateBlackBoxCaseDataLogic(ctx context.Context, svcCtx *svc.ServiceContext) *UpdateBlackBoxCaseDataLogic {
	return &UpdateBlackBoxCaseDataLogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),
	}
}

func (l *UpdateBlackBoxCaseDataLogic) UpdateBlackBoxCaseData(req *types.UpdateBlackBoxCaseDataReq) (resp *types.UpdateBlackBoxCaseDataResp, err error) {
	in := &pb.UpdateBlackBoxCaseDataReq{}
	if err = utils.Copy(in, req, l.Converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy data[%+v] to request, error: %+v", req,
			err,
		)
	}

	_, err = l.SvcCtx.BlackBoxCaseDataServiceRpc.UpdateBlackBoxCaseData(l.Ctx, in)
	if err != nil {
		return nil, err
	}

	return &types.UpdateBlackBoxCaseDataResp{}, nil
}
