package blackboxcasedata

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"
)

type AppendBlackBoxCaseDataLogic struct {
	*logic.BaseLogic
}

func NewAppendBlackBoxCaseDataLogic(ctx context.Context, svcCtx *svc.ServiceContext) *AppendBlackBoxCaseDataLogic {
	return &AppendBlackBoxCaseDataLogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),
	}
}

func (l *AppendBlackBoxCaseDataLogic) AppendBlackBoxCaseData(req *types.AppendBlackBoxCaseDataReq) (resp *types.AppendBlackBoxCaseDataResp, err error) {
	in := &pb.AppendBlackBoxCaseDataReq{}
	if err = utils.Copy(in, req, l.Converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy data[%+v] to request, error: %+v", req,
			err,
		)
	}

	out, err := l.SvcCtx.BlackBoxCaseDataServiceRpc.AppendBlackBoxCaseData(l.Ctx, in)
	if err != nil {
		return nil, err
	}
	resp = &types.AppendBlackBoxCaseDataResp{Items: []*types.BlackBoxCaseData{}, NewItems: []*types.BlackBoxCaseData{}}
	if err = utils.Copy(resp, out, l.Converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy data[%+v] to response, error: %+v",
			out, err,
		)
	}
	return resp, nil
}
