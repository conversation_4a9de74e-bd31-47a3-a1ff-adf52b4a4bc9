package blackboxcasedata

import (
	"context"
	"fmt"
	"strings"

	"github.com/zeromicro/go-zero/core/logx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/model"
)

type MetricsMigreationLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewMetricsMigreationLogic(ctx context.Context, svcCtx *svc.ServiceContext) *MetricsMigreationLogic {
	return &MetricsMigreationLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *MetricsMigreationLogic) MetricsMigreation(req *types.MetricsMigreation) (resp string, err error) {
	caseDataList, err := l.svcCtx.BlackBoxCaseDataModel.FindNoCacheByQuery(l.ctx, l.svcCtx.BlackBoxCaseDataModel.SelectBuilder())
	if err != nil {
		return
	}

	uniqueMap := make(map[string]*model.BlackBoxCaseData)
	for _, caseData := range caseDataList {
		// 将所有字段拼接成一个字符串作为唯一键
		key := strings.Join([]string{
			caseData.OrderId,
			caseData.Requirement,
			caseData.PreCondition,
			caseData.CaseStep,
			caseData.ExpectResult,
			caseData.Terminal,
			caseData.CaseLevel,
			caseData.Tag,
			caseData.CaseName,
		}, "|")
		uniqueMap[key] = caseData
	}

	existDataSum, err := l.svcCtx.BlackBoxCaseRefModel.GetSumRefDataCount(l.ctx)
	if err != nil {
		return "", err
	}

	count := int64(0)
	if int64(len(uniqueMap)) > existDataSum {
		count = int64(len(uniqueMap)) - existDataSum
	} else {
		count = 0
	}
	caseRef := &model.BlackBoxCaseRef{
		CaseRefId:     "metricsMigration",
		CaseDataCount: count,
	}
	l.svcCtx.BlackBoxCaseRefModel.Insert(l.ctx, nil, caseRef)

	return fmt.Sprintf("原始总数:%v, 去重后总数: %v, 结果: %v", len(caseDataList), len(uniqueMap), count), err
}
