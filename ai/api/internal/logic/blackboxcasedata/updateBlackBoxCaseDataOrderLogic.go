package blackboxcasedata

import (
	"context"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type UpdateBlackBoxCaseDataOrderLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewUpdateBlackBoxCaseDataOrderLogic(ctx context.Context, svcCtx *svc.ServiceContext) *UpdateBlackBoxCaseDataOrderLogic {
	return &UpdateBlackBoxCaseDataOrderLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *UpdateBlackBoxCaseDataOrderLogic) UpdateBlackBoxCaseDataOrder(req *types.UpdateBlackBoxCaseDataOrderReq) (resp *types.UpdateBlackBoxCaseDataOrderResp, err error) {
	// todo: add your logic here and delete this line

	return
}
