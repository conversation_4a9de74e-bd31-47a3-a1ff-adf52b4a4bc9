package blackboxcasedata

import (
	"context"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/public/httpc"
)

type GetBlackBoxCaseRefJsonLogic struct {
	*logic.BaseLogic
}

func NewGetBlackBoxCaseRefJsonLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetBlackBoxCaseRefJsonLogic {
	return &GetBlackBoxCaseRefJsonLogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),
	}
}

func (l *GetBlackBoxCaseRefJsonLogic) GetBlackBoxCaseRefJson(req *types.GetBlackBoxCaseRefJsonReq) (resp *types.GetBlackBoxCaseRefResp, err error) {
	caseData, err := httpc.GetCaseJson(l.SvcCtx.ExternalAiDomain, req.CaseRefId)
	if err != nil {
		l.Error("调用AI获取用例数据异常: ", err)
		return nil, err
	}
	resp = &types.GetBlackBoxCaseRefResp{Items: []*types.BlackBoxCaseData{}}
	for _, item := range *caseData.Data {
		data := &types.BlackBoxCaseData{
			OrderId:      item.OrderId,
			Requirement:  item.Requirement,
			PreCondition: item.PreCondition,
			CaseStep:     item.CaseStep,
			ExpectResult: item.ExpectResult,
			Terminal:     item.Terminal,
			CaseLevel:    item.CaseLevel,
			Tag:          item.Tag,
			CaseName:     item.CaseName,
		}
		resp.Items = append(resp.Items, data)
	}
	return resp, nil
}
