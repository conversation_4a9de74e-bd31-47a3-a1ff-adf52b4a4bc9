package blackboxcasedata

import (
	"context"
	"strconv"
	"time"

	"github.com/zeromicro/go-zero/core/jsonx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sse"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/metrics"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/public"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/public/httpc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
)

type GenerateBlackBoxCaseDataLogic struct {
	*logic.BaseLogic
}

func NewGenerateBlackBoxCaseDataLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GenerateBlackBoxCaseDataLogic {
	return &GenerateBlackBoxCaseDataLogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),
	}
}

func (l *GenerateBlackBoxCaseDataLogic) GenerateBlackBoxCaseData(req *types.GenerateBlackBoxCaseDataReq) (
	resp *sse.Stream, err error,
) {
	stream := sse.NewStream(strconv.Itoa(int(time.Now().UnixMilli())), 0)
	stopChan := make(chan struct{})

	// 获取用例详情,生成用例需要传入ai属性
	in := &pb.GetBlackBoxCaseReq{CaseId: req.CaseId}
	caseDetail, err := l.SvcCtx.BlackBoxCaseServiceRpc.GetBlackBoxCase(l.Ctx, in)
	if err != nil {
		return stream, err
	}

	// 生成case_ref_id用于临时id记录
	caseRefType := "generate"
	refIn := &pb.GenerateBlackBoxCaseRefReq{CaseRefType: caseRefType}
	caseRef, err := l.SvcCtx.BlackBoxCaseDataServiceRpc.GenerateBlackBoxCaseRef(l.Ctx, refIn)
	if err != nil {
		return stream, err
	}

	// 刷新metrics
	l.SvcCtx.BlackBoxCaseDataServiceRpc.RefreshCaseRefMetrics(
		l.Ctx, &pb.RefreshCaseRefMetricsReq{CaseRefType: caseRefType},
	)

	// 提前生成revision给到前端查询当前生成用例对应的版本
	generateRevision, err := l.SvcCtx.BlackBoxCaseRevisionServiceRpc.GenerateBlackBoxCaseRevisionId(
		l.Ctx, &pb.GenerateBlackBoxCaseRevisionIdReq{},
	)
	if err != nil {
		return stream, err
	}

	go func() {
		defer func() {
			if err := recover(); err != nil {
				l.Error(err)
			}
			if err == nil {
				// 结束以后创建新版本
				revisionReq := &pb.CreateBlackBoxCaseRevisionReq{
					CaseId:                      req.CaseId,
					RevisionName:                public.GenerateRevisionName(),
					KnowledgeId:                 req.KnowledgeId,
					KnowledgeParagraphTitleText: req.KnowledgeParagraphTitleText,
					CaseRefId:                   caseRef.CaseRefId,
				}
				revision, err := l.SvcCtx.BlackBoxCaseRevisionServiceRpc.CreateBlackBoxCaseRevision(l.Ctx, revisionReq)
				if err != nil {
					l.Error("生成用例创建版本异常: ", err)
					stream.Quit()
					close(stopChan)
					return
				}

				// 用例数据落库
				var caseData httpc.ResponseData[[]*pb.BlackBoxCaseData]
				for i := 0; i < 5; i++ {
					caseData, err = httpc.GetCaseJson(l.SvcCtx.ExternalAiDomain, caseRef.CaseRefId)
					if err != nil {
						l.Error("生成用例创建用例数据异常: ", err)
					}
					if caseData.Data != nil {
						break
					}
					time.Sleep(2 * time.Second)
				}

				if caseData.Data != nil {
					dataIn := &pb.CreateBlackBoxCaseDataReq{
						Items: *caseData.Data,
					}
					for i := 0; i < len(dataIn.Items); i++ {
						dataIn.Items[i].RevisionId = revision.RevisionId
					}
					_, err = l.SvcCtx.BlackBoxCaseDataServiceRpc.CreateBlackBoxCaseData(l.Ctx, dataIn)
					if err != nil {
						l.Error("用例数据入库异常: ", err)
						stream.Quit()
						close(stopChan)
						return
					}

					// 更新case_ref的数据统计
					for i := 0; i < 5; i++ {
						caseCount, err := httpc.GetCaseRowCount(l.SvcCtx.ExternalAiDomain, caseRef.CaseRefId)
						if err != nil {
							l.Error("获取case_ref数据统计异常: ", err)
						}
						if *caseCount.Data > 0 {
							err = l.SvcCtx.BlackBoxCaseRefModel.UpdateCountById(
								l.Ctx, nil, caseRef.CaseRefId, *caseCount.Data,
							)
							if err != nil {
								l.Error("更新case_ref数据统计异常: ", err)
							}
							metrics.CaseDataTotalGauge.Add(float64(*caseCount.Data), []string{""}...)
							break
						}
						time.Sleep(1 * time.Second)
					}

					// 更新case信息
					caseDetail.Item.KnowledgeParagraphTitleId = req.KnowledgeParagraphTitleID
					caseDetail.Item.KnowledgeId = req.KnowledgeId
					updateCaseIn := &pb.UpdateBlackBoxCaseReq{
						Item: caseDetail.Item,
					}
					_, err = l.SvcCtx.BlackBoxCaseServiceRpc.UpdateBlackBoxCase(l.Ctx, updateCaseIn)
					if err != nil {
						l.Error("更新用例信息异常: ", err, caseDetail.Item.CaseId)
					}
				} else {
					var res httpc.ResponseData[httpc.SendMsgForSessionResp]
					res.Code = 1
					res.Message = "获取不到本次用例的json数据,请检查是否入库: " + caseRef.CaseRefId
					stream.Event <- &sse.Event{
						ID:   []byte(stream.ID),
						Data: jsonx.MarshalIgnoreError(&res),
					}
					l.Error("获取不到本次用例的json数据,请检查是否入库: " + caseRef.CaseRefId)
				}

				stream.Quit()
				close(stopChan)
			}
		}()

		errChan := make(chan error)

		go func() {
			errChan <- httpc.CreateTestCaseData(
				l.SvcCtx.ExternalAiDomain,
				&httpc.CreateTestCaseDataReq{
					CaseId:                    caseRef.CaseRefId,
					ModelCharacter:            caseDetail.Item.CaseModelCharacter,
					KnowledgeId:               req.KnowledgeId,
					KnowledgeParagraphTitle:   req.KnowledgeParagraphTitleText,
					KnowledgeParagraphTitleId: req.KnowledgeParagraphTitleID,
					ContinueToWrite:           caseDetail.Item.CaseContinueToWrite,
					Revision:                  generateRevision.RevisionId,
				},
				stream,
			)
		}()

		select {
		case <-l.Ctx.Done():
			l.Error("客户端断开...")
		case err = <-errChan:
			if err != nil {
				l.Error(err)
				stream.Quit()
				close(stopChan)
			}
		case <-stopChan:
		}
	}()

	return stream, err
}
