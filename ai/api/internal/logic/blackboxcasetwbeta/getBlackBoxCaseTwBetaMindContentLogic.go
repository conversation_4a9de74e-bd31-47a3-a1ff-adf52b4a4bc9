package blackboxcasetwbeta

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/types"
)

type GetBlackBoxCaseTwBetaMindContentLogic struct {
	*logic.BaseLogic
}

func NewGetBlackBoxCaseTwBetaMindContentLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetBlackBoxCaseTwBetaMindContentLogic {
	return &GetBlackBoxCaseTwBetaMindContentLogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),
	}
}

func (l *GetBlackBoxCaseTwBetaMindContentLogic) GetBlackBoxCaseTwBetaMindContent(req *types.GetBlackBoxCaseTwBetaMindContentReq) (resp *types.GetBlackBoxCaseTwBetaMindContentResp, err error) {
	in := &pb.GetBlackBoxCaseTwBetaMindContentReq{}
	if err = utils.Copy(in, req, l.Converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy data[%+v] to request, error: %+v", req,
			err,
		)
	}
	out, err := l.SvcCtx.BlackBoxCaseTwBetaServiceRpc.GetBlackBoxCaseTwBetaMindContent(l.Ctx, in)
	if err != nil {
		return nil, err
	}
	resp = &types.GetBlackBoxCaseTwBetaMindContentResp{Data: out.Data}
	return resp, nil
}
