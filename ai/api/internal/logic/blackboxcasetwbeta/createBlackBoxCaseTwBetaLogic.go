package blackboxcasetwbeta

import (
	"context"
	"strconv"
	"time"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/public/httpc"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sse"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/types"
)

type CreateBlackBoxCaseTwBetaLogic struct {
	*logic.BaseLogic
}

// NewCreateBlackBoxCaseTwBetaLogic create black box tw test case
func NewCreateBlackBoxCaseTwBetaLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CreateBlackBoxCaseTwBetaLogic {
	return &CreateBlackBoxCaseTwBetaLogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),
	}
}

func (l *CreateBlackBoxCaseTwBetaLogic) CreateBlackBoxCaseTwBeta(req *types.CreateBlackBoxCaseTwBetaReq) (resp *sse.Stream, err error) {
	stream := sse.NewStream(strconv.Itoa(int(time.Now().UnixMilli())), 0)
	stopChan := make(chan struct{})

	go func() {
		defer func() {
			stream.Quit()
			close(stopChan)
		}()

		errChan := make(chan error)

		go func() {
			errChan <- httpc.CreateTwTestCase(
				l.SvcCtx.ExternalAiDomain,
				&httpc.CreateTwCaseReq{CaseId: req.CaseRefId, MindId: req.MindId},
				stream,
			)
		}()

		select {
		case err = <-errChan:
			if err != nil {
				l.Error(err)
			}
		case <-stopChan:
		}
	}()

	return stream, err
}
