package blackboxcasetwbeta

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/types"
)

type CreateBlackBoxCaseTwBetaMindLogic struct {
	*logic.BaseLogic
}

func NewCreateBlackBoxCaseTwBetaMindLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CreateBlackBoxCaseTwBetaMindLogic {
	return &CreateBlackBoxCaseTwBetaMindLogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),
	}
}

func (l *CreateBlackBoxCaseTwBetaMindLogic) CreateBlackBoxCaseTwBetaMind(req *types.CreateBlackBoxCaseTwBetaMindReq) (resp *types.CreateBlackBoxCaseTwBetaMindResp, err error) {
	in := &pb.CreateBlackBoxCaseTwBetaMindReq{}
	if err = utils.Copy(in, req, l.Converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy data[%+v] to request, error: %+v", req,
			err,
		)
	}
	_, err = l.SvcCtx.BlackBoxCaseTwBetaServiceRpc.CreateBlackBoxCaseTwBetaMind(l.Ctx, in)
	if err != nil {
		return nil, err
	}
	return &types.CreateBlackBoxCaseTwBetaMindResp{}, nil
}
