package blackboxcasetwbeta

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/types"
)

type ListBlackBoxCaseTwBetaLogic struct {
	*logic.BaseLogic
}

func NewListBlackBoxCaseTwBetaLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ListBlackBoxCaseTwBetaLogic {
	return &ListBlackBoxCaseTwBetaLogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),
	}
}

func (l *ListBlackBoxCaseTwBetaLogic) ListBlackBoxCaseTwBeta(req *types.ListBlackBoxCaseTwBetaReq) (resp *types.ListBlackBoxCaseTwBetaResp, err error) {
	in := &pb.ListBlackBoxCaseTwBetaReq{}
	if err = utils.Copy(in, req, l.Converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy data[%+v] to request, error: %+v", req,
			err,
		)
	}

	out, err := l.SvcCtx.BlackBoxCaseTwBetaServiceRpc.ListBlackBoxCaseTwBeta(l.Ctx, in)
	if err != nil {
		return nil, err
	}
	resp = &types.ListBlackBoxCaseTwBetaResp{Items: []*types.BlackBoxCaseTwBeta{}}
	if err = utils.Copy(resp, out, l.Converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy data[%+v] to response, error: %+v",
			out, err,
		)
	}
	return resp, nil
}
