package blackboxcasetwbeta

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/types"
)

type DeleteBlackBoxCaseTwBetaLogic struct {
	*logic.BaseLogic
}

func NewDeleteBlackBoxCaseTwBetaLogic(ctx context.Context, svcCtx *svc.ServiceContext) *DeleteBlackBoxCaseTwBetaLogic {
	return &DeleteBlackBoxCaseTwBetaLogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),
	}
}

func (l *DeleteBlackBoxCaseTwBetaLogic) DeleteBlackBoxCaseTwBeta(req *types.DeleteBlackBoxCaseTwBetaReq) (resp *types.DeleteBlackBoxCaseTwBetaResp, err error) {
	in := &pb.DeleteBlackBoxCaseTwBetaReq{}
	if err = utils.Copy(in, req, l.Converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy data[%+v] to request, error: %+v", req,
			err,
		)
	}
	_, err = l.SvcCtx.BlackBoxCaseTwBetaServiceRpc.DeleteBlackBoxCaseTwBeta(l.Ctx, in)
	if err != nil {
		return nil, err
	}
	return &types.DeleteBlackBoxCaseTwBetaResp{}, nil
}
