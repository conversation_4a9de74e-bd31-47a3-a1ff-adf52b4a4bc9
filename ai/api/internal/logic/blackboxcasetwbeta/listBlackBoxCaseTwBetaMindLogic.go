package blackboxcasetwbeta

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/types"
)

type ListBlackBoxCaseTwBetaMindLogic struct {
	*logic.BaseLogic
}

func NewListBlackBoxCaseTwBetaMindLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ListBlackBoxCaseTwBetaMindLogic {
	return &ListBlackBoxCaseTwBetaMindLogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),
	}
}

func (l *ListBlackBoxCaseTwBetaMindLogic) ListBlackBoxCaseTwBetaMind(req *types.ListBlackBoxCaseTwBetaMindReq) (resp *types.ListBlackBoxCaseTwBetaMindResp, err error) {
	in := &pb.ListBlackBoxCaseTwBetaMindReq{}
	if err = utils.Copy(in, req, l.Converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy data[%+v] to request, error: %+v", req,
			err,
		)
	}

	out, err := l.SvcCtx.BlackBoxCaseTwBetaServiceRpc.ListBlackBoxCaseTwBetaMind(l.Ctx, in)
	if err != nil {
		return nil, err
	}
	resp = &types.ListBlackBoxCaseTwBetaMindResp{Items: []*types.BlackBoxCaseTwBetaMind{}}
	if err = utils.Copy(resp, out, l.Converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy data[%+v] to response, error: %+v",
			out, err,
		)
	}
	return resp, nil
}
