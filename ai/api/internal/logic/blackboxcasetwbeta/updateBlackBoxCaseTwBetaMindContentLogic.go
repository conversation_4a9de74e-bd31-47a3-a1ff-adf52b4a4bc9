package blackboxcasetwbeta

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/types"
)

type UpdateBlackBoxCaseTwBetaMindContentLogic struct {
	*logic.BaseLogic
}

// NewUpdateBlackBoxCaseTwBetaMindContentLogic update black box tw mind content
func NewUpdateBlackBoxCaseTwBetaMindContentLogic(ctx context.Context, svcCtx *svc.ServiceContext) *UpdateBlackBoxCaseTwBetaMindContentLogic {
	return &UpdateBlackBoxCaseTwBetaMindContentLogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),
	}
}

func (l *UpdateBlackBoxCaseTwBetaMindContentLogic) UpdateBlackBoxCaseTwBetaMindContent(req *types.UpdateBlackBoxCaseMindTwBetaReq) (resp *types.UpdateBlackBoxCaseMindTwBetaResp, err error) {
	in := &pb.UpdateBlackBoxCaseMindTwBetaReq{}
	if err = utils.Copy(in, req, l.Converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy data[%+v] to request, error: %+v", req,
			err,
		)
	}
	_, err = l.SvcCtx.BlackBoxCaseTwBetaServiceRpc.UpdateBlackBoxCaseTwBetaMindContent(l.Ctx, in)
	if err != nil {
		return nil, err
	}
	resp = &types.UpdateBlackBoxCaseMindTwBetaResp{}
	return resp, nil
}
