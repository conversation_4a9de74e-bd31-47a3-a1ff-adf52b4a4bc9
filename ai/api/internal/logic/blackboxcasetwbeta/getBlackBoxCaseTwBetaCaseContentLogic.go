package blackboxcasetwbeta

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/types"
)

type GetBlackBoxCaseTwBetaCaseContentLogic struct {
	*logic.BaseLogic
}

// NewGetBlackBoxCaseTwBetaCaseContentLogic get black box tw case content
func NewGetBlackBoxCaseTwBetaCaseContentLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetBlackBoxCaseTwBetaCaseContentLogic {
	return &GetBlackBoxCaseTwBetaCaseContentLogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),
	}
}

func (l *GetBlackBoxCaseTwBetaCaseContentLogic) GetBlackBoxCaseTwBetaCaseContent(req *types.GetBlackBoxCaseTwBetaContentReq) (resp *types.GetBlackBoxCaseTwBetaContentResp, err error) {
	in := &pb.GetBlackBoxCaseTwBetaContentReq{}
	if err = utils.Copy(in, req, l.Converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy data[%+v] to request, error: %+v", req,
			err,
		)
	}
	out, err := l.SvcCtx.BlackBoxCaseTwBetaServiceRpc.GetBlackBoxCaseTwBetaCaseContent(l.Ctx, in)
	if err != nil {
		return nil, err
	}
	resp = &types.GetBlackBoxCaseTwBetaContentResp{Data: out.Data}
	return resp, nil
}
