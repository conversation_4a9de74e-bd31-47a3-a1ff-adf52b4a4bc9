package blackboxcasedir

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"
)

type CreateBlackBoxCaseDirLogic struct {
	*logic.BaseLogic
}

func NewCreateBlackBoxCaseDirLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CreateBlackBoxCaseDirLogic {
	return &CreateBlackBoxCaseDirLogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),
	}
}

func (l *CreateBlackBoxCaseDirLogic) CreateBlackBoxCaseDir(req *types.CreateBlackBoxCaseDirReq) (resp *types.CreateBlackBoxCaseDirResp, err error) {
	in := &pb.CreateBlackBoxCaseDirReq{}
	if err = utils.Copy(in, req, l.Converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy data[%+v] to request, error: %+v", req,
			err,
		)
	}
	out, err := l.SvcCtx.BlackBoxCaseDirServiceRpc.CreateBlackBoxCaseDir(l.Ctx, in)
	if err != nil {
		return nil, err
	}
	resp = &types.CreateBlackBoxCaseDirResp{DirId: out.DirId}

	return resp, nil
}
