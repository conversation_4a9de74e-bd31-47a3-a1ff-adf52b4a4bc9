package blackboxcasedir

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"
)

type UpdateBlackBoxCaseDirLogic struct {
	*logic.BaseLogic
}

func NewUpdateBlackBoxCaseDirLogic(ctx context.Context, svcCtx *svc.ServiceContext) *UpdateBlackBoxCaseDirLogic {
	return &UpdateBlackBoxCaseDirLogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),
	}
}

func (l *UpdateBlackBoxCaseDirLogic) UpdateBlackBoxCaseDir(req *types.UpdateBlackBoxCaseDirReq) (resp *types.UpdateBlackBoxCaseDirResp, err error) {
	in := &pb.UpdateBlackBoxCaseDirReq{}
	if err = utils.Copy(in, req, l.Converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy data[%+v] to request, error: %+v", req,
			err,
		)
	}

	_, err = l.SvcCtx.BlackBoxCaseDirServiceRpc.UpdateBlackBoxCaseDir(l.Ctx, in)
	if err != nil {
		return nil, err
	}

	return &types.UpdateBlackBoxCaseDirResp{}, nil
}
