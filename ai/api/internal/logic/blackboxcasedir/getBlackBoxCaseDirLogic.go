package blackboxcasedir

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"
)

type GetBlackBoxCaseDirLogic struct {
	*logic.BaseLogic
}

func NewGetBlackBoxCaseDirLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetBlackBoxCaseDirLogic {
	return &GetBlackBoxCaseDirLogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),
	}
}

func (l *GetBlackBoxCaseDirLogic) GetBlackBoxCaseDir(req *types.GetBlackBoxCaseDirReq) (resp *types.GetBlackBoxCaseDirResp, err error) {
	in := &pb.GetBlackBoxCaseDirReq{}
	if err = utils.Copy(in, req, l.Converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy data[%+v] to request, error: %+v", req,
			err,
		)
	}

	out, err := l.SvcCtx.BlackBoxCaseDirServiceRpc.GetBlackBoxCaseDir(l.Ctx, in)
	if err != nil {
		return nil, err
	}
	resp = &types.GetBlackBoxCaseDirResp{Item: &types.BlackBoxCaseDir{}}
	if err = utils.Copy(resp, out, l.Converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy data[%+v] to response, error: %+v",
			out, err,
		)
	}

	return resp, nil
}
