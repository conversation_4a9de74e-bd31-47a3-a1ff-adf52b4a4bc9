package blackboxcasedir

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"
)

type DeleteBlackBoxCaseDirLogic struct {
	*logic.BaseLogic
}

func NewDeleteBlackBoxCaseDirLogic(ctx context.Context, svcCtx *svc.ServiceContext) *DeleteBlackBoxCaseDirLogic {
	return &DeleteBlackBoxCaseDirLogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),
	}
}

func (l *DeleteBlackBoxCaseDirLogic) DeleteBlackBoxCaseDir(req *types.DeleteBlackBoxCaseDirReq) (resp *types.DeleteBlackBoxCaseDirResp, err error) {
	in := &pb.DeleteBlackBoxCaseDirReq{}
	if err = utils.Copy(in, req, l.Converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy data[%+v] to request, error: %+v", req,
			err,
		)
	}

	_, err = l.SvcCtx.BlackBoxCaseDirServiceRpc.DeleteBlackBoxCaseDir(l.Ctx, in)
	if err != nil {
		return nil, err
	}

	return &types.DeleteBlackBoxCaseDirResp{}, nil
}
