package blackboxcasedirectory

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"
)

type ListBlackBoxCaseDirectoriesLogic struct {
	*logic.BaseLogic
}

// list blackbox case directories
func NewListBlackBoxCaseDirectoriesLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ListBlackBoxCaseDirectoriesLogic {
	return &ListBlackBoxCaseDirectoriesLogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),
	}
}

func (l *ListBlackBoxCaseDirectoriesLogic) ListBlackBoxCaseDirectories(req *types.ListBlackBoxCaseDirsReq) (resp *types.ListBlackBoxCaseDirsResp, err error) {
	in := &pb.ListBlackBoxCaseDirsReq{}
	if err = utils.Copy(in, req, l.Converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy data[%+v] to request, error: %+v", req,
			err,
		)
	}

	out, err := l.SvcCtx.BlackBoxCaseDirectoryServiceRpc.ListBlackBoxCaseDirectories(l.Ctx, in)
	if err != nil {
		return nil, err
	}
	resp = &types.ListBlackBoxCaseDirsResp{Items: []*types.BlackBoxCaseDir{}}
	if err = utils.Copy(resp, out, l.Converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy data[%+v] to response, error: %+v",
			out, err,
		)
	}

	return resp, nil
}
