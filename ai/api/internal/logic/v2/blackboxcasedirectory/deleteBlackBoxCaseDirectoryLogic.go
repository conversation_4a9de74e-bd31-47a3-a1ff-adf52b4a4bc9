package blackboxcasedirectory

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"
)

type DeleteBlackBoxCaseDirectoryLogic struct {
	*logic.BaseLogic
}

// delete blackbox case directory
func NewDeleteBlackBoxCaseDirectoryLogic(ctx context.Context, svcCtx *svc.ServiceContext) *DeleteBlackBoxCaseDirectoryLogic {
	return &DeleteBlackBoxCaseDirectoryLogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),
	}
}

func (l *DeleteBlackBoxCaseDirectoryLogic) DeleteBlackBoxCaseDirectory(req *types.DeleteBlackBoxCaseDirReq) (resp *types.DeleteBlackBoxCaseDirResp, err error) {
	in := &pb.DeleteBlackBoxCaseDirReq{}
	if err = utils.Copy(in, req, l.Converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy data[%+v] to request, error: %+v", req,
			err,
		)
	}

	_, err = l.SvcCtx.BlackBoxCaseDirectoryServiceRpc.DeleteBlackBoxCaseDirectory(l.Ctx, in)
	if err != nil {
		return nil, err
	}

	return &types.DeleteBlackBoxCaseDirResp{}, nil
}
