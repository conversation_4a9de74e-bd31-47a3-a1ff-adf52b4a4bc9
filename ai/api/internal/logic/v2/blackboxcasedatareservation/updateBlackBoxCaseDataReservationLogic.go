package blackboxcasedatareservation

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"
)

type UpdateBlackBoxCaseDataReservationLogic struct {
	*logic.BaseLogic
}

// update blackbox case data reservation
func NewUpdateBlackBoxCaseDataReservationLogic(ctx context.Context, svcCtx *svc.ServiceContext) *UpdateBlackBoxCaseDataReservationLogic {
	return &UpdateBlackBoxCaseDataReservationLogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),
	}
}

func (l *UpdateBlackBoxCaseDataReservationLogic) UpdateBlackBoxCaseDataReservation(req *types.KeepBlackBoxCaseDataReq) (resp *types.KeepBlackBoxCaseDataResp, err error) {
	in := &pb.KeepBlackBoxCaseDataReq{}
	if err = utils.Copy(in, req, l.Converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy data[%+v] to request, error: %+v", req,
			err,
		)
	}

	_, err = l.SvcCtx.BlackBoxCaseDataServiceRpc.KeepBlackBoxCaseData(l.Ctx, in)
	if err != nil {
		return nil, err
	}

	return &types.KeepBlackBoxCaseDataResp{}, nil
}
