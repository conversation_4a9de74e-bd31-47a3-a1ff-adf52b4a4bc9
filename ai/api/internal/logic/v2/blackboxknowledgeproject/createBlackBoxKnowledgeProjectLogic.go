package blackboxknowledgeproject

import (
	"context"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/logx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/public/httpc"
)

type CreateBlackBoxKnowledgeProjectLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// create blackbox knowledge project
func NewCreateBlackBoxKnowledgeProjectLogic(ctx context.Context,
	svcCtx *svc.ServiceContext) *CreateBlackBoxKnowledgeProjectLogic {
	return &CreateBlackBoxKnowledgeProjectLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *CreateBlackBoxKnowledgeProjectLogic) CreateBlackBoxKnowledgeProject(
	req *types.CreateBlackBoxKnowledgeProjectReq) (resp any, err error) {
	projectRes, err := httpc.CreateKnowledgeMetaProject(l.svcCtx.ExternalAiDomain, httpc.CreateKnowledgeMetaProjectReq{
		ProjectId: req.ProjectId,
		Name:      req.Name,
	})
	if err != nil {
		l.Logger.Errorf("failed to create blackbox knowledge project, error: %+v", err)
		return nil, err
	}

	if projectRes.Code != 0 {
		return nil, errors.New("调用AI接口异常: " + projectRes.Message)
	}

	return projectRes.Data, nil
}
