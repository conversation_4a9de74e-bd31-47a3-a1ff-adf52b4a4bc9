package blackboxcasedatasort

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"
)

type CreateBlackBoxCaseDataSortLogic struct {
	*logic.BaseLogic
}

// create blackbox case data sort
func NewCreateBlackBoxCaseDataSortLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CreateBlackBoxCaseDataSortLogic {
	return &CreateBlackBoxCaseDataSortLogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),
	}
}

func (l *CreateBlackBoxCaseDataSortLogic) CreateBlackBoxCaseDataSort(req *types.ReorderBlackBoxCaseDataReq) (resp *types.ReorderBlackBoxCaseDataResp, err error) {
	in := &pb.ReorderBlackBoxCaseDataReq{}
	if err = utils.Copy(in, req, l.Converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy data[%+v] to request, error: %+v", req,
			err,
		)
	}

	_, err = l.SvcCtx.BlackBoxCaseDataServiceRpc.ReorderBlackBoxCaseData(l.Ctx, in)
	if err != nil {
		return nil, err
	}

	return resp, nil
}
