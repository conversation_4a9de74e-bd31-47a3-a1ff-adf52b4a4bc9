package blackboxknowledgeexperience

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/public/httpc"

	"github.com/zeromicro/go-zero/core/logx"
)

type DeleteBlackBoxKnowledgeExperienceLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// delete blackbox knowledge experience
func NewDeleteBlackBoxKnowledgeExperienceLogic(ctx context.Context,
	svcCtx *svc.ServiceContext) *DeleteBlackBoxKnowledgeExperienceLogic {
	return &DeleteBlackBoxKnowledgeExperienceLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *DeleteBlackBoxKnowledgeExperienceLogic) DeleteBlackBoxKnowledgeExperience(
	req *types.DeleteBlackBoxKnowledgeExperienceReq) (resp any, err error) {
	experienceRes, err := httpc.DeleteKnowledgeTestExperience(l.svcCtx.ExternalAiDomain,
		httpc.DeleteKnowledgeTestExperienceReq{
			Id: req.Id,
		})
	if err != nil {
		l.Logger.Errorf("failed to delete blackbox knowledge experience, error: %+v", err)
		return nil, err
	}

	if experienceRes.Code != 0 {
		return nil, errors.New("调用AI接口异常: " + experienceRes.Message)
	}

	return experienceRes.Data, nil
}
