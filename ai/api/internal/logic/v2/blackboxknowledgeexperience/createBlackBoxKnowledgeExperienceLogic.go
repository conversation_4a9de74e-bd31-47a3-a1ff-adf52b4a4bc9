package blackboxknowledgeexperience

import (
	"context"
	"fmt"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/convert"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/public/httpc"
	managerpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
	sqlbuilderRpc "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sqlbuilder/rpc"

	"github.com/zeromicro/go-zero/core/logx"
)

type CreateBlackBoxKnowledgeExperienceLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// NewCreateBlackBoxKnowledgeExperienceLogic create blackbox knowledge experience
func NewCreateBlackBoxKnowledgeExperienceLogic(ctx context.Context,
	svcCtx *svc.ServiceContext) *CreateBlackBoxKnowledgeExperienceLogic {
	return &CreateBlackBoxKnowledgeExperienceLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *CreateBlackBoxKnowledgeExperienceLogic) CreateBlackBoxKnowledgeExperience(
	req *types.CreateBlackBoxKnowledgeExperienceReq) (resp any, err error) {
	searchProjectRes, err := l.svcCtx.ManagerProjectRpc.SearchProject(l.ctx, &managerpb.SearchProjectReq{
		Condition: &sqlbuilderRpc.Condition{
			Single: &sqlbuilderRpc.SingleCondition{
				Field:   "project_id",
				Compare: "EQ",
				Other:   &sqlbuilderRpc.Other{Value: req.ProjectId},
			},
		},
	})
	if err != nil {
		return nil, err
	}

	if len(searchProjectRes.Items) != 1 {
		return nil, fmt.Errorf("project_id:%s result is not 1", req.ProjectId)
	}

	project := searchProjectRes.Items[0]

	experienceRes, err := httpc.CreateKnowledgeTestExperience(l.svcCtx.ExternalAiDomain, httpc.CreateKnowledgeTestExperienceReq{
		ProjectId: req.ProjectId,
		Project:   project.GetName(),
		Category: &httpc.KnowledgeExperienceCategory{
			Id:       int64(req.Category.Id),
			TypeName: req.Category.TypeName,
		},
		Tags:               convert.Convert2KnowledgeTags(req.Tags),
		TestExperience:     req.TestExperience,
		NormalFocusPoint:   req.NormalFocusPoint,
		AbnormalFocusPoint: req.AbnormalFocusPoint,
	})
	if err != nil {
		l.Logger.Errorf("failed to create blackbox knowledge experience, error: %+v", err)
		return nil, err
	}

	if experienceRes.Code != 0 {
		return nil, errors.New("调用AI接口异常: " + experienceRes.Message)
	}

	return experienceRes.Data, nil
}
