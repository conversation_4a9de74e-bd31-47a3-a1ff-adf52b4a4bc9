package blackboxknowledgeexperience

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/public/httpc"

	"github.com/zeromicro/go-zero/core/logx"
)

type ListBlackBoxKnowledgeExperiencesLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// list blackbox knowledge experiences
func NewListBlackBoxKnowledgeExperiencesLogic(ctx context.Context,
	svcCtx *svc.ServiceContext) *ListBlackBoxKnowledgeExperiencesLogic {
	return &ListBlackBoxKnowledgeExperiencesLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *ListBlackBoxKnowledgeExperiencesLogic) ListBlackBoxKnowledgeExperiences(
	req *types.ListBlackBoxKnowledgeExperiencesReq) (resp any, err error) {
	var testGeniusReq httpc.ListKnowledgeTestExperienceReq

	//err = utils.Copy(&testGeniusReq, req)
	//if err != nil {
	//    l.Logger.Errorf("failed to copy data[%+v] to request, error: %+v", req, err)
	//    return nil, err
	//}

	testGeniusReq.ProjectId = req.ProjectId
	testGeniusReq.Page = int64(req.Page)
	testGeniusReq.Size = int64(req.Size)
	if req.Search != "" {
		testGeniusReq.Search = req.Search
	}

	if req.Filter.Tag != "" {
		testGeniusReq.Filter.Tag = req.Filter.Tag
	}

	if req.Filter.Category != "" {
		testGeniusReq.Filter.Category = req.Filter.Category
	}

	experienceRes, err := httpc.ListKnowledgeTestExperience(l.svcCtx.ExternalAiDomain, testGeniusReq)
	if err != nil {
		l.Logger.Errorf("failed to list blackbox knowledge experience, error: %+v", err)
		return nil, err
	}

	if experienceRes.Code != 0 {
		return nil, errors.New("调用AI接口异常: " + experienceRes.Message)
	}

	return experienceRes.Data, nil
}
