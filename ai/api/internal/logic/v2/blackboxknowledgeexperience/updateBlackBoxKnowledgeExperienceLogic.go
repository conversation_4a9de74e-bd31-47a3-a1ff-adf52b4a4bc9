package blackboxknowledgeexperience

import (
	"context"
	"fmt"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/convert"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/public/httpc"
	managerpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
	sqlbuilderRpc "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sqlbuilder/rpc"

	"github.com/zeromicro/go-zero/core/logx"
)

type UpdateBlackBoxKnowledgeExperienceLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// update blackbox knowledge experience
func NewUpdateBlackBoxKnowledgeExperienceLogic(ctx context.Context, svcCtx *svc.ServiceContext) *UpdateBlackBoxKnowledgeExperienceLogic {
	return &UpdateBlackBoxKnowledgeExperienceLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *UpdateBlackBoxKnowledgeExperienceLogic) UpdateBlackBoxKnowledgeExperience(
	req *types.UpdateBlackBoxKnowledgeExperienceReq) (resp any, err error) {
	searchProjectRes, err := l.svcCtx.ManagerProjectRpc.SearchProject(l.ctx, &managerpb.SearchProjectReq{
		Condition: &sqlbuilderRpc.Condition{
			Single: &sqlbuilderRpc.SingleCondition{
				Field:   "project_id",
				Compare: "EQ",
				Other:   &sqlbuilderRpc.Other{Value: req.ProjectId},
			},
		},
	})
	if err != nil {
		return nil, err
	}
	if len(searchProjectRes.Items) != 1 {
		return nil, fmt.Errorf("project_id:%s result is not 1", req.ProjectId)
	}

	project := searchProjectRes.Items[0]

	testGeniusReq := httpc.UpdateKnowledgeTestExperienceReq{
		Id:                 req.Id,
		Project:            project.GetName(),
		TestExperience:     req.TestExperience,
		NormalFocusPoint:   req.NormalFocusPoint,
		AbnormalFocusPoint: req.AbnormalFocusPoint,
		Category:           convert.Convert2ExperienceCategory(req.Category),
		Tags:               convert.Convert2KnowledgeTags(req.Tags),
	}

	experienceRes, err := httpc.UpdateKnowledgeTestExperience(l.svcCtx.ExternalAiDomain, testGeniusReq)
	if err != nil {
		l.Logger.Errorf("failed to update blackbox knowledge experience, error: %+v", err)
		return nil, err
	}

	if experienceRes.Code != 0 {
		return nil, errors.New("调用AI接口异常: " + experienceRes.Message)
	}

	return experienceRes.Data, nil
}
