package blackboxcase

import (
	"context"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"github.com/pkg/errors"
)

type ListBlackBoxCaseLogic struct {
	*logic.BaseLogic
}

// list blackbox cases
func NewListBlackBoxCaseLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ListBlackBoxCaseLogic {
	return &ListBlackBoxCaseLogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),
	}
}

func (l *ListBlackBoxCaseLogic) ListBlackBoxCase(req *types.ListBlackBoxCaseReq) (resp *types.ListBlackBoxCaseResp, err error) {
	in := &pb.ListBlackBoxCaseReq{}
	if err = utils.Copy(in, req, l.Converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy data[%+v] to request, error: %+v", req,
			err,
		)
	}

	out, err := l.SvcCtx.BlackBoxCaseServiceRpc.ListBlackBoxCase(l.Ctx, in)
	if err != nil {
		return nil, err
	}
	resp = &types.ListBlackBoxCaseResp{Items: []*types.BlackBoxCase{}}
	if err = utils.Copy(resp, out, l.Converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy data[%+v] to response, error: %+v",
			out, err,
		)
	}

	return resp, nil
}
