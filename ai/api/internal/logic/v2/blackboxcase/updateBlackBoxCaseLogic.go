package blackboxcase

import (
	"context"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/common"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
)

type UpdateBlackBoxCaseLogic struct {
	*logic.BaseLogic
}

func NewUpdateBlackBoxCaseLogic(ctx context.Context, svcCtx *svc.ServiceContext) *UpdateBlackBoxCaseLogic {
	return &UpdateBlackBoxCaseLogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),
	}
}

func (l *UpdateBlackBoxCaseLogic) UpdateBlackBoxCase(req *types.UpdateBlackBoxCaseItemV2Req) (resp *types.UpdateBlackBoxCaseItemResp, err error) {
	in := &pb.UpdateBlackBoxCaseReq{
		Item: &pb.BlackBoxCase{
			DirId:                       req.DirId,
			CaseId:                      req.CaseId,
			CaseName:                    req.CaseName,
			CaseContinueToWrite:         req.CaseContinueToWrite,
			CaseModelCharacter:          req.CaseModelCharacter,
			CaseRemarks:                 req.CaseRemarks,
			KnowledgeId:                 req.KnowledgeId,
			KnowledgeParagraphTitleId:   req.KnowledgeParagraphTitleId,
			KnowledgeParagraphTitleText: req.KnowledgeParagraphTitleText,
			KnowledgeFixSugg:            req.KnowledgeFixSugg,
			KnowledgeParagraphTitle:     common.KnowledgeDocPgTitleReq2PbStruct(req.KnowledgeDocPgTitle),
		},
	}

	_, err = l.SvcCtx.BlackBoxCaseServiceRpc.UpdateBlackBoxCase(l.Ctx, in)
	if err != nil {
		return nil, err
	}

	return &types.UpdateBlackBoxCaseItemResp{}, nil
}
