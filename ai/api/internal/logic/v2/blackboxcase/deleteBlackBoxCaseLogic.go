package blackboxcase

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"
)

type DeleteBlackBoxCaseLogic struct {
	*logic.BaseLogic
}

func NewDeleteBlackBoxCaseLogic(ctx context.Context, svcCtx *svc.ServiceContext) *DeleteBlackBoxCaseLogic {
	return &DeleteBlackBoxCaseLogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),
	}
}

func (l *DeleteBlackBoxCaseLogic) DeleteBlackBoxCase(req *types.DeleteBlackBoxCaseReq) (resp *types.DeleteBlackBoxCaseResp, err error) {
	in := &pb.DeleteBlackBoxCaseReq{}
	if err = utils.Copy(in, req, l.Converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy data[%+v] to request, error: %+v", req,
			err,
		)
	}

	_, err = l.SvcCtx.BlackBoxCaseServiceRpc.DeleteBlackBoxCase(l.Ctx, in)
	if err != nil {
		return nil, err
	}

	return &types.DeleteBlackBoxCaseResp{}, nil
}
