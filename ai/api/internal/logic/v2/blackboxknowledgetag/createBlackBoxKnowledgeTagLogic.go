package blackboxknowledgetag

import (
	"context"
	"errors"

	"github.com/zeromicro/go-zero/core/logx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/public/httpc"
)

type CreateBlackBoxKnowledgeTagLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// create blackbox knowledge tag
func NewCreateBlackBoxKnowledgeTagLogic(ctx context.Context,
	svcCtx *svc.ServiceContext) *CreateBlackBoxKnowledgeTagLogic {
	return &CreateBlackBoxKnowledgeTagLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *CreateBlackBoxKnowledgeTagLogic) CreateBlackBoxKnowledgeTag(
	req *types.CreateBlackBoxKnowledgeTagReq) (resp any, err error) {
	tagRes, err := httpc.CreateKnowledgeMetaTag(l.svcCtx.ExternalAiDomain, httpc.CreateKnowledgeMetaTagReq{
		ProjectId: req.ProjectId,
		Tag:       req.Tag,
	})
	if err != nil {
		l.Logger.Errorf("failed to create blackbox knowledge tag, error: %+v", err)
		return nil, err
	}

	if tagRes.Code != 0 {
		return nil, errors.New("调用AI接口异常: " + tagRes.Message)
	}

	return tagRes.Data, nil
}
