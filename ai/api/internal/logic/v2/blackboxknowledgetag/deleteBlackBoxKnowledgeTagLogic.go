package blackboxknowledgetag

import (
	"context"
	"errors"

	"github.com/zeromicro/go-zero/core/logx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/public/httpc"
)

type DeleteBlackBoxKnowledgeTagLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// delete blackbox knowledge tag
func NewDeleteBlackBoxKnowledgeTagLogic(ctx context.Context,
	svcCtx *svc.ServiceContext) *DeleteBlackBoxKnowledgeTagLogic {
	return &DeleteBlackBoxKnowledgeTagLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *DeleteBlackBoxKnowledgeTagLogic) DeleteBlackBoxKnowledgeTag(
	req *types.DeleteBlackBoxKnowledgeTagReq) (resp any, err error) {
	tagRes, err := httpc.DeleteKnowledgeMetaTag(l.svcCtx.ExternalAiDomain, httpc.DeleteKnowledgeMetaTagReq{
		Id: int64(req.Id),
	})
	if err != nil {
		l.Logger.Errorf("failed to delete blackbox knowledge tag, error: %+v", err)
		return nil, err
	}

	if tagRes.Code != 0 {
		return nil, errors.New("调用AI接口异常: " + tagRes.Message)
	}

	return tagRes.Data, nil
}
