package blackboxknowledgetag

import (
	"context"
	"errors"

	"github.com/zeromicro/go-zero/core/logx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/public/httpc"
)

type ListBlackBoxKnowledgeTagsLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// list blackbox knowledge tags
func NewListBlackBoxKnowledgeTagsLogic(ctx context.Context,
	svcCtx *svc.ServiceContext) *ListBlackBoxKnowledgeTagsLogic {
	return &ListBlackBoxKnowledgeTagsLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *ListBlackBoxKnowledgeTagsLogic) ListBlackBoxKnowledgeTags(
	req *types.ListBlackBoxKnowledgeTagsReq) (resp any, err error) {
	testGeniusReq := httpc.ListKnowledgeMetaTagReq{
		ProjectId: req.ProjectId,
	}

	tagRes, err := httpc.ListKnowledgeMetaTag(l.svcCtx.ExternalAiDomain, testGeniusReq)
	if err != nil {
		l.Logger.Errorf("failed to list blackbox knowledge tag, error: %+v", err)
		return nil, err
	}

	if tagRes.Code != 0 {
		return nil, errors.New("调用AI接口异常: " + tagRes.Message)
	}

	return tagRes.Data, nil
}
