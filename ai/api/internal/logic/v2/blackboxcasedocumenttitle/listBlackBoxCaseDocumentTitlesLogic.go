package blackboxcasedocumenttitle

import (
	"context"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/public/httpc"
)

type ListBlackBoxCaseDocumentTitlesLogic struct {
	*logic.BaseLogic
}

// list blackbox case document titles
func NewListBlackBoxCaseDocumentTitlesLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ListBlackBoxCaseDocumentTitlesLogic {
	return &ListBlackBoxCaseDocumentTitlesLogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),
	}
}

func (l *ListBlackBoxCaseDocumentTitlesLogic) ListBlackBoxCaseDocumentTitles(req *types.GetBlackBoxCaseKnowledgeTitleReq) (resp *types.GetBlackBoxCaseKnowledgeTitleResp, err error) {
	res, err := httpc.GetKnowledgeTitle(l.SvcCtx.Config.ExternalAiDomain, req.KnowledgeId)
	if err != nil {
		return nil, err
	}

	resp = &types.GetBlackBoxCaseKnowledgeTitleResp{Data: res.Data}
	return resp, nil
}
