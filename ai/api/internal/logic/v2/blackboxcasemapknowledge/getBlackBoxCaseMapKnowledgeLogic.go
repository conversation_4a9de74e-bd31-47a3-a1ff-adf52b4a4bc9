package blackboxcasemapknowledge

import (
	"context"
	"encoding/json"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/public/httpc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/types"
)

type GetBlackBoxCaseMapKnowledgeLogic struct {
	*logic.BaseLogic
}

// get blackbox case map knowledge
func NewGetBlackBoxCaseMapKnowledgeLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetBlackBoxCaseMapKnowledgeLogic {
	return &GetBlackBoxCaseMapKnowledgeLogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),
	}
}

func (l *GetBlackBoxCaseMapKnowledgeLogic) GetBlackBoxCaseMapKnowledge(req *types.GetBlackBoxCaseMapKnowledgeReq) (resp any, err error) {
	caseMap, err := l.SvcCtx.BlackBoxCaseMapServiceRpc.GetCompleteBlackBoxCaseMap(l.Ctx, &pb.GetCompleteBlackBoxCaseMapReq{
		ProjectId: req.ProjectId,
		DirId:     req.DirId,
		CaseId:    req.CaseId,
	})
	if err != nil {
		return nil, err
	}

	var uk httpc.GetBlackBoxCaseUsedKnowledgeResp
	if caseMap.UsedKnowledge != "" {
		if err = json.Unmarshal([]byte(caseMap.UsedKnowledge), &uk); err != nil {
			return nil, errors.Wrapf(err, "failed to unmarshal usedKnowledge from JSON")
		}
	} else {
		return nil, nil
	}

	return uk, nil
}
