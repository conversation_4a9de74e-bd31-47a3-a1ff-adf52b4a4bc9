package blackboxcasemapprogress

import (
	"context"
	"errors"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/public/httpc"
)

type GetBlackBoxCaseMapProgressLogic struct {
	*logic.BaseLogic
}

// get blackbox case map progress
func NewGetBlackBoxCaseMapProgressLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetBlackBoxCaseMapProgressLogic {
	return &GetBlackBoxCaseMapProgressLogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),
	}
}

func (l *GetBlackBoxCaseMapProgressLogic) GetBlackBoxCaseMapProgress(req *types.GetBlackBoxCaseMapProgressReq) (resp any, err error) {
	var testGeniusReq httpc.GetBlackBoxCaseEstimatedProgressReq

	testGeniusReq.ControlNode = httpc.ControlNodeType_ReviewMap

	mapProgressRes, err := httpc.GetBlackBoxCaseEstimatedProgress(l.SvcCtx.ExternalAiDomain, testGeniusReq)
	if err != nil {
		l.Logger.Errorf("failed to get blackbox-case map progress, error: %+v", err)
		return nil, err
	}

	if mapProgressRes.Code != 0 {
		return nil, errors.New("调用AI接口异常: " + mapProgressRes.Message)
	}

	return mapProgressRes.Data, nil
}
