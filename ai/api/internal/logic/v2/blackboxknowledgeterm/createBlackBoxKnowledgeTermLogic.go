package blackboxknowledgeterm

import (
	"context"
	"errors"
	"fmt"

	"github.com/zeromicro/go-zero/core/logx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/convert"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/public/httpc"
	managerpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
	sqlbuilderRpc "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sqlbuilder/rpc"
)

type CreateBlackBoxKnowledgeTermLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// create blackbox knowledge term
func NewCreateBlackBoxKnowledgeTermLogic(ctx context.Context,
	svcCtx *svc.ServiceContext) *CreateBlackBoxKnowledgeTermLogic {
	return &CreateBlackBoxKnowledgeTermLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *CreateBlackBoxKnowledgeTermLogic) CreateBlackBoxKnowledgeTerm(
	req *types.CreateBlackBoxKnowledgeTermReq) (resp any, err error) {
	searchProjectRes, err := l.svcCtx.ManagerProjectRpc.SearchProject(l.ctx, &managerpb.SearchProjectReq{
		Condition: &sqlbuilderRpc.Condition{
			Single: &sqlbuilderRpc.SingleCondition{
				Field:   "project_id",
				Compare: "EQ",
				Other:   &sqlbuilderRpc.Other{Value: req.ProjectId},
			},
		},
	})
	if err != nil {
		return nil, err
	}

	if len(searchProjectRes.Items) != 1 {
		return nil, fmt.Errorf("project_id:%s result is not 1", req.ProjectId)
	}

	project := searchProjectRes.Items[0]

	tagRes, err := httpc.CreateKnowledgeTerm(l.svcCtx.ExternalAiDomain, httpc.CreateKnowledgeTermReq{
		ProjectId:     req.ProjectId,
		Project:       project.GetName(),
		TermInChinese: req.TermInChinese,
		TermMeaning:   req.TermMeaning,
		Tags:          convert.Convert2KnowledgeTags(req.Tags),
	})
	if err != nil {
		l.Logger.Errorf("failed to create blackbox knowledge term, error: %+v", err)
		return nil, err
	}

	if tagRes.Code != 0 {
		return nil, errors.New("调用AI接口异常: " + tagRes.Message)
	}

	return tagRes.Data, nil
}
