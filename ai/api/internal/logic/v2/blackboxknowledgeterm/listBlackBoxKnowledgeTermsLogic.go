package blackboxknowledgeterm

import (
	"context"
	"errors"

	"github.com/zeromicro/go-zero/core/logx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/public/httpc"
)

type ListBlackBoxKnowledgeTermsLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// list blackbox knowledge terms
func NewListBlackBoxKnowledgeTermsLogic(ctx context.Context,
	svcCtx *svc.ServiceContext) *ListBlackBoxKnowledgeTermsLogic {
	return &ListBlackBoxKnowledgeTermsLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *ListBlackBoxKnowledgeTermsLogic) ListBlackBoxKnowledgeTerms(
	req *types.ListBlackBoxKnowledgeTermsReq) (resp any, err error) {
	var testGeniusReq httpc.ListKnowledgeTermReq

	// 查询接口特殊, 不copy了

	testGeniusReq.ProjectId = req.ProjectId
	testGeniusReq.Page = int64(req.Page)
	testGeniusReq.Size = int64(req.Size)
	if req.Search != "" {
		testGeniusReq.Search = req.Search
	}

	if req.Filter.Tag != "" {
		testGeniusReq.Filter.Tag = req.Filter.Tag
	}

	experienceRes, err := httpc.ListKnowledgeTerm(l.svcCtx.ExternalAiDomain, testGeniusReq)
	if err != nil {
		l.Logger.Errorf("failed to list blackbox knowledge term, error: %+v", err)
		return nil, err
	}

	if experienceRes.Code != 0 {
		return nil, errors.New("调用AI接口异常: " + experienceRes.Message)
	}

	return experienceRes.Data, nil
}
