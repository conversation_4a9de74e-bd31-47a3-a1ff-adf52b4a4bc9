package blackboxknowledgeterm

import (
	"context"
	"errors"

	"github.com/zeromicro/go-zero/core/logx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/public/httpc"
)

type DeleteBlackBoxKnowledgeTermLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// delete blackbox knowledge term
func NewDeleteBlackBoxKnowledgeTermLogic(ctx context.Context,
	svcCtx *svc.ServiceContext) *DeleteBlackBoxKnowledgeTermLogic {
	return &DeleteBlackBoxKnowledgeTermLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *DeleteBlackBoxKnowledgeTermLogic) DeleteBlackBoxKnowledgeTerm(
	req *types.DeleteBlackBoxKnowledgeTermReq) (resp any, err error) {
	tagRes, err := httpc.DeleteKnowledgeTerm(l.svcCtx.ExternalAiDomain, httpc.DeleteKnowledgeTermReq{
		Id: req.Id,
	})
	if err != nil {
		l.Logger.Errorf("failed to delete blackbox knowledge term, error: %+v", err)
		return nil, err
	}

	if tagRes.Code != 0 {
		return nil, errors.New("调用AI接口异常: " + tagRes.Message)
	}

	return tagRes.Data, nil
}
