package blackboxcasedocument

import (
	"context"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/public/httpc"
)

type UpdateBlackBoxCaseDocumentLogic struct {
	*logic.BaseLogic
}

// update blackbox case document
func NewUpdateBlackBoxCaseDocumentLogic(ctx context.Context, svcCtx *svc.ServiceContext) *UpdateBlackBoxCaseDocumentLogic {
	return &UpdateBlackBoxCaseDocumentLogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),
	}
}

func (l *UpdateBlackBoxCaseDocumentLogic) UpdateBlackBoxCaseDocument(req *types.UpdateBlackBoxCaseKnowledgeReq) (resp *types.UpdateBlackBoxCaseKnowledgeResp, err error) {
	err = httpc.ReloadKnowledge(l.SvcCtx.ExternalAiDomain, req.KnowledgeId)
	if err != nil {
		return nil, err
	}
	return &types.UpdateBlackBoxCaseKnowledgeResp{}, nil
}
