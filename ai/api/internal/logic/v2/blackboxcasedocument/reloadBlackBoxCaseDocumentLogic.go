package blackboxcasedocument

import (
	"context"

	"github.com/zeromicro/go-zero/core/logx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/public/httpc"
)

type ReloadBlackBoxCaseDocumentLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// reload blackbox case document
func NewReloadBlackBoxCaseDocumentLogic(
	ctx context.Context, svcCtx *svc.ServiceContext,
) *ReloadBlackBoxCaseDocumentLogic {
	return &ReloadBlackBoxCaseDocumentLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *ReloadBlackBoxCaseDocumentLogic) ReloadBlackBoxCaseDocument(req *types.ReloadBlackBoxCaseKnowledgeReq) (
	resp *types.ReloadBlackBoxCaseKnowledgeResp, err error,
) {
	err = httpc.ReloadKnowledge(l.svcCtx.ExternalAiDomain, req.KnowledgeId)
	if err != nil {
		return nil, err
	}
	return &types.ReloadBlackBoxCaseKnowledgeResp{}, nil
}
