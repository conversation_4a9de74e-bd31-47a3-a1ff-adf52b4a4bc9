package blackboxcasedocument

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"
)

type CreateBlackBoxCaseDocumentLogic struct {
	*logic.BaseLogic
}

// create blackbox case document
func NewCreateBlackBoxCaseDocumentLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CreateBlackBoxCaseDocumentLogic {
	return &CreateBlackBoxCaseDocumentLogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),
	}
}

func (l *CreateBlackBoxCaseDocumentLogic) CreateBlackBoxCaseDocument(req *types.CreateBlackBoxCaseKnowledgeReq) (resp *types.CreateBlackBoxCaseKnowledgeResp, err error) {
	in := &pb.CreateBlackBoxCaseKnowledgeReq{}
	if err = utils.Copy(in, req, l.Converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy data[%+v] to request, error: %+v", req,
			err,
		)
	}
	out, err := l.SvcCtx.BlackBoxCaseKnowledgeServiceRpc.CreateBlackBoxCaseKnowledge(l.Ctx, in)
	if err != nil {
		return nil, err
	}
	resp = &types.CreateBlackBoxCaseKnowledgeResp{KnowledgeId: out.KnowledgeId}

	return resp, nil
}
