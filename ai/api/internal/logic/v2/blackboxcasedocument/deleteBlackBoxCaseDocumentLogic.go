package blackboxcasedocument

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"
)

type DeleteBlackBoxCaseDocumentLogic struct {
	*logic.BaseLogic
}

// delete blackbox case document
func NewDeleteBlackBoxCaseDocumentLogic(ctx context.Context, svcCtx *svc.ServiceContext) *DeleteBlackBoxCaseDocumentLogic {
	return &DeleteBlackBoxCaseDocumentLogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),
	}
}

func (l *DeleteBlackBoxCaseDocumentLogic) DeleteBlackBoxCaseDocument(req *types.DeleteBlackBoxCaseKnowledgeReq) (resp *types.DeleteBlackBoxCaseKnowledgeResp, err error) {
	in := &pb.DeleteBlackBoxCaseKnowledgeReq{}
	if err = utils.Copy(in, req, l.Converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy data[%+v] to request, error: %+v", req,
			err,
		)
	}

	_, err = l.SvcCtx.BlackBoxCaseKnowledgeServiceRpc.DeleteBlackBoxCaseKnowledge(l.Ctx, in)
	if err != nil {
		return nil, err
	}

	return &types.DeleteBlackBoxCaseKnowledgeResp{}, nil
}
