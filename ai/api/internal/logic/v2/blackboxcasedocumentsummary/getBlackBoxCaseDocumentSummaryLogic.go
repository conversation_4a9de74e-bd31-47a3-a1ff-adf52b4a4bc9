package blackboxcasedocumentsummary

import (
	"context"
	"strconv"
	"time"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sse"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/public/httpc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
)

type GetBlackBoxCaseDocumentSummaryLogic struct {
	*logic.BaseLogic
}

// get blackbox case document summary
func NewGetBlackBoxCaseDocumentSummaryLogic(
	ctx context.Context, svcCtx *svc.ServiceContext,
) *GetBlackBoxCaseDocumentSummaryLogic {
	return &GetBlackBoxCaseDocumentSummaryLogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),
	}
}

func (l *GetBlackBoxCaseDocumentSummaryLogic) GetBlackBoxCaseDocumentSummary(req *types.QueryBlackBoxCaseKnowledgeV2Req) (
	resp *sse.Stream, err error,
) {
	stream := sse.NewStream(strconv.Itoa(int(time.Now().UnixMilli())), 0)
	stopChan := make(chan struct{})

	// 获取用例详情,生成用例需要传入ai属性
	in := &pb.GetBlackBoxCaseReq{CaseId: req.CaseId}
	caseDetail, err := l.SvcCtx.BlackBoxCaseServiceRpc.GetBlackBoxCase(l.Ctx, in)
	if err != nil {
		return stream, err
	}

	titles := make([]httpc.KnowledgeDocPgTitleV2, 0, len(req.KnowledgeParagraphTitle))
	for _, title := range req.KnowledgeParagraphTitle {
		titles = append(titles, httpc.KnowledgeDocPgTitleV2{
			DemandPoints: title.DemandPoints,
			Id:           title.Id,
			Title:        title.Title,
		})
	}

	go func() {
		defer func() {
			l.SvcCtx.BlackBoxCaseServiceRpc.QueryIncBlackBoxCase(l.Ctx, &pb.QueryIncBlackBoxCaseReq{CaseId: req.CaseId})
			stream.Quit()
			close(stopChan)
		}()

		errChan := make(chan error)

		go func() {
			errChan <- httpc.SummarizeCaseKnowledgeDoc(
				l.SvcCtx.ExternalAiDomain,
				&httpc.SummarizeCaseDocReq{
					KnowledgeDocId:       caseDetail.GetItem().GetKnowledgeId(),
					KnowledgeDocPgTitles: titles,
				},
				stream,
			)
		}()

		select {
		case err = <-errChan:
			if err != nil {
				l.Error(err)
			}
		case <-stopChan:
		}
	}()

	return stream, err
}
