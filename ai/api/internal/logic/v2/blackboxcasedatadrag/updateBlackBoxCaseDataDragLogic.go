package blackboxcasedatadrag

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"
)

type UpdateBlackBoxCaseDataDragLogic struct {
	*logic.BaseLogic
}

// update blackbox case data drag
func NewUpdateBlackBoxCaseDataDragLogic(ctx context.Context, svcCtx *svc.ServiceContext) *UpdateBlackBoxCaseDataDragLogic {
	return &UpdateBlackBoxCaseDataDragLogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),
	}
}

func (l *UpdateBlackBoxCaseDataDragLogic) UpdateBlackBoxCaseDataDrag(req *types.BatchUpdateBlackBoxCaseDataReq) (resp *types.BatchUpdateBlackBoxCaseDataResp, err error) {
	in := &pb.BatchUpdateBlackBoxCaseDataReq{}
	if err = utils.Copy(in, req, l.Converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy data[%+v] to request, error: %+v", req,
			err,
		)
	}

	_, err = l.SvcCtx.BlackBoxCaseDataServiceRpc.BatchUpdateBlackBoxCaseData(l.Ctx, in)
	if err != nil {
		return nil, err
	}

	return &types.BatchUpdateBlackBoxCaseDataResp{}, nil
}
