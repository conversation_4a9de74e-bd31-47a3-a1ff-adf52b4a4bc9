package blackboxcasedatarevision

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"
)

type GetBlackBoxCaseDataRevisionLogic struct {
	*logic.BaseLogic
}

// get blackbox case data revision
func NewGetBlackBoxCaseDataRevisionLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetBlackBoxCaseDataRevisionLogic {
	return &GetBlackBoxCaseDataRevisionLogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),
	}
}

func (l *GetBlackBoxCaseDataRevisionLogic) GetBlackBoxCaseDataRevision(req *types.GetBlackBoxCaseDataRevisionReq) (resp *types.BlackBoxCaseRevision, err error) {
	in := &pb.GetBlackBoxCaseRevisionReq{}
	if err = utils.Copy(in, req, l.Converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy data[%+v] to request, error: %+v", req,
			err,
		)
	}

	out, err := l.SvcCtx.BlackBoxCaseRevisionServiceRpc.GetBlackBoxCaseRevision(l.Ctx, in)
	if err != nil {
		return nil, err
	}
	resp = &types.BlackBoxCaseRevision{}
	if err = utils.Copy(resp, out.Item, l.Converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy data[%+v] to response, error: %+v",
			out, err,
		)
	}
	return resp, nil
}
