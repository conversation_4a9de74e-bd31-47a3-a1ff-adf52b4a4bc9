package blackboxcasedatarevision

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/public"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"
)

type CreateBlackBoxCaseDataRevisionLogic struct {
	*logic.BaseLogic
}

// create blackbox case data revision
func NewCreateBlackBoxCaseDataRevisionLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CreateBlackBoxCaseDataRevisionLogic {
	return &CreateBlackBoxCaseDataRevisionLogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),
	}
}

func (l *CreateBlackBoxCaseDataRevisionLogic) CreateBlackBoxCaseDataRevision(req *types.CreateBlackBoxCaseRevisionDataReq) (resp *types.CreateBlackBoxCaseRevisionDataResp, err error) {
	in := &pb.CreateBlackBoxCaseRevisionDataReq{}
	if err = utils.Copy(in, req, l.Converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy data[%+v] to request, error: %+v", req,
			err,
		)
	}

	in.RevisionName = public.GenerateRevisionName()
	out, err := l.SvcCtx.BlackBoxCaseRevisionServiceRpc.CreateBlackBoxCaseRevisionData(l.Ctx, in)
	if err != nil {
		return nil, err
	}

	return &types.CreateBlackBoxCaseRevisionDataResp{RevisionId: out.RevisionId}, nil
}
