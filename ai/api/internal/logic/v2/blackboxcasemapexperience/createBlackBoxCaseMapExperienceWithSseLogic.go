package blackboxcasemapexperience

import (
	"context"
	"strconv"
	"time"

	"github.com/zeromicro/go-zero/core/logx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sse"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/tools"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/types"
)

type CreateBlackBoxCaseMapExperienceWithSseLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
	*CreateBlackBoxCaseMapExperienceLogic
}

// create blackbox case map experience with sse
func NewCreateBlackBoxCaseMapExperienceWithSseLogic(
	ctx context.Context, svcCtx *svc.ServiceContext,
) *CreateBlackBoxCaseMapExperienceWithSseLogic {
	return &CreateBlackBoxCaseMapExperienceWithSseLogic{
		Logger:                               logx.WithContext(ctx),
		ctx:                                  ctx,
		svcCtx:                               svcCtx,
		CreateBlackBoxCaseMapExperienceLogic: NewCreateBlackBoxCaseMapExperienceLogic(ctx, svcCtx),
	}
}

func (l *CreateBlackBoxCaseMapExperienceWithSseLogic) CreateBlackBoxCaseMapExperienceWithSse(req *types.BlackBoxCaseMapSupplementRequest) (
	resp *sse.Stream, err error,
) {
	stream := sse.NewStream(strconv.Itoa(int(time.Now().UnixMilli())), 0)
	fn := func() (result any, err error) {
		result, err = l.CreateBlackBoxCaseMapExperienceLogic.create(
			&types.BlackBoxCaseMapSupplementRequest{
				ProjectId:                 req.ProjectId,
				DirId:                     req.DirId,
				CaseId:                    req.CaseId,
				KnowledgeSupplementEnable: req.KnowledgeSupplementEnable,
				KnowledgeSupplementDoc:    req.KnowledgeSupplementDoc,
				FunctionPoint:             req.FunctionPoint,
				GenerateOpinion:           req.GenerateOpinion,
			},
		)
		return result, err
	}

	tools.HandleSseStream(l.BaseLogic.Ctx, stream, fn)

	return stream, nil
}
