package blackboxcasemapexperience

import (
	"context"
	"encoding/json"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/common"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/public/httpc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"
)

type CreateBlackBoxCaseMapExperienceLogic struct {
	*logic.BaseLogic
}

// create blackbox case map experience
func NewCreateBlackBoxCaseMapExperienceLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CreateBlackBoxCaseMapExperienceLogic {
	return &CreateBlackBoxCaseMapExperienceLogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),
	}
}

func (l *CreateBlackBoxCaseMapExperienceLogic) CreateBlackBoxCaseMapExperience(req *types.BlackBoxCaseMapSupplementRequest) (resp any, err error) {
	return l.create(req)
}

func (l *CreateBlackBoxCaseMapExperienceLogic) create(req *types.BlackBoxCaseMapSupplementRequest) (resp any, err error) {
	// 1. fetch doc_id/title/model_characteristic from black_box_case_dir_v2 table;
	caseIn := &pb.GetBlackBoxCaseReq{
		DirId:  req.DirId,
		CaseId: req.CaseId,
	}

	caseOut, err := l.SvcCtx.BlackBoxCaseServiceRpc.GetBlackBoxCase(l.Ctx, caseIn)
	if err != nil {
		return nil, err
	}
	// caseOut.Item.KnowledgeId
	// caseOut.Item.KnowledgeParagraphTitleId
	// CaseOut.Item.KnowledgeParagraphTitleText 从缓存中获取，或者，新加一个字段
	// caseOut.Item.CaseModelCharacter

	// 2. fetch tags/experiences from black_box_case_map table;
	knowledgeIn := &pb.GetBlackBoxCaseKnowledgeV2Req{
		ProjectId: req.ProjectId,
		DirId:     req.DirId,
		CaseId:    req.CaseId,
	}

	knowledgeOut, err := l.SvcCtx.BlackBoxCaseKnowledgeV2ServiceRpc.GetBlackBoxCaseKnowledge(l.Ctx, knowledgeIn)
	if err != nil {
		return nil, err
	}
	// knowledgeOut.Tags
	// knowledgeOut.Experiences
	var tags []string
	if knowledgeOut.Tags != "" {
		if err = json.Unmarshal([]byte(knowledgeOut.Tags), &tags); err != nil {
			return nil, errors.Wrapf(err, "failed to unmarshal tags from JSON")
		}
	}
	var experiences []httpc.KnowledgeExperience
	if knowledgeOut.Experiences != "" {
		if err = json.Unmarshal([]byte(knowledgeOut.Experiences), &experiences); err != nil {
			return nil, errors.Wrapf(err, "failed to unmarshal experiences from JSON")
		}
	}

	// 3. generate map_id that has been saved to cache;
	mapId, err := l.SvcCtx.BlackBoxCaseMapServiceRpc.CreateBlackBoxCaseMapId(l.Ctx, &pb.CreateBlackBoxCaseMapIdReq{CaseId: req.CaseId})
	if err != nil {
		return nil, err
	}

	// 4. copy func supplement doc to http request;
	knowledgeSupplementDoc := &httpc.SupplementDoc{}
	if req.KnowledgeSupplementEnable {
		if err = utils.Copy(knowledgeSupplementDoc, req.KnowledgeSupplementDoc, l.Converters...); err != nil {
			return nil, errors.Wrapf(
				errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy data[%+v] to request, error: %+v", req,
				err,
			)
		}
	}
	if knowledgeSupplementDoc.Feishu.KnowledgeDocPgTitle == nil {
		knowledgeSupplementDoc.Feishu.KnowledgeDocPgTitle = []*httpc.KnowledgeDocPgTitle{}
	}

	// 5. http bypass to external AI;
	// 		success: bypass return data;
	// 		fail: return error;
	expeMapResp, err := httpc.CreateTestSceneReviewMap(l.SvcCtx.ExternalAiDomain, httpc.CreateBlackBoxTestSceneReviewMapReq{
		MapId:          mapId.MapId,
		ProjectID:      req.ProjectId,
		KnowledgeDocID: caseOut.Item.KnowledgeId,
		//KnowledgeDocPgTitle: httpc.KnowledgeDocPgTitle{
		//	Id:    caseOut.Item.KnowledgeParagraphTitleId,
		//	Title: caseOut.Item.KnowledgeParagraphTitleText,
		//},
		KnowledgeDocPgTitles:    common.KnowledgeDocPgTitlePbStruct2Msg(caseOut.GetItem().GetKnowledgeParagraphTitle()),
		KnowledgeTermList:       tags,
		KnowledgeExperienceList: experiences,
		ModelCharacteristic:     caseOut.Item.CaseModelCharacter,
		KnowledgeSupplementDoc:  *knowledgeSupplementDoc,
		FunctionPoint:           req.FunctionPoint,
		GenerateOpinion:         req.GenerateOpinion,
	})
	if err != nil {
		return nil, err
	}
	// update func supplement doc to black_box_case_map_document table from front todo with func_id

	// resp = &types.BlackBoxCaseMapNode{}
	// if err = utils.Copy(resp, *expeMapResp.Data, l.Converters...); err != nil {
	// 	return nil, errors.Wrapf(
	// 		errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy data[%+v] to request, error: %+v", *expeMapResp.Data,
	// 		err,
	// 	)
	// }

	_, err = l.SvcCtx.BlackBoxGenerationRecordServiceRpc.AddBlackBoxTestSceneCount(l.Ctx, &pb.AddBlackBoxTestSceneCountReq{})
	if err != nil {
		l.Logger.Error(l.Ctx, "failed to add black box test scene count error:%s", err.Error())
	}

	if req.KnowledgeSupplementEnable {
		_, err = l.SvcCtx.BlackBoxGenerationRecordServiceRpc.AddBlackBoxSupplementDocumentCount(l.Ctx, &pb.AddBlackBoxSupplementDocumentCountReq{})
		if err != nil {
			l.Logger.Error(l.Ctx, "failed to add black box supplement document count error:%s", err.Error())
		}
	}

	return expeMapResp.Data, nil
}
