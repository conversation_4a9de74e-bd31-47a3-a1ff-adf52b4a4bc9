package blackboxcasemapexperience

import (
	"context"
	"encoding/json"
	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/tools"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/public/httpc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sse"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"
	"strconv"
	"time"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type UpdateBlackBoxCaseMapExperienceWithSseLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// update blackbox case map experience with sse
func NewUpdateBlackBoxCaseMapExperienceWithSseLogic(ctx context.Context, svcCtx *svc.ServiceContext) *UpdateBlackBoxCaseMapExperienceWithSseLogic {
	return &UpdateBlackBoxCaseMapExperienceWithSseLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *UpdateBlackBoxCaseMapExperienceWithSseLogic) UpdateBlackBoxCaseMapExperienceWithSse(req *types.UpdateBlackBoxCaseMapExperienceWithSseRequset) (resp *sse.Stream, err error) {
	stream := sse.NewStream(strconv.Itoa(int(time.Now().UnixMilli())), 0)
	fn := func() (result any, err error) {
		result, err = l.update(req)
		if err != nil {
			return nil, err
		}
		return result, err
	}

	tools.HandleSseStream(l.ctx, stream, fn)
	return stream, nil
}

func (l *UpdateBlackBoxCaseMapExperienceWithSseLogic) update(req *types.UpdateBlackBoxCaseMapExperienceWithSseRequset) (resp any, err error) {
	blackboxCase, err := l.svcCtx.BlackBoxCaseServiceRpc.GetBlackBoxCase(l.ctx, &pb.GetBlackBoxCaseReq{
		CaseId: req.CaseId,
	})
	if err != nil {
		return nil, err
	}

	caseMap, err := l.svcCtx.BlackBoxCaseMapServiceRpc.GetCompleteBlackBoxCaseMap(l.ctx, &pb.GetCompleteBlackBoxCaseMapReq{
		ProjectId: req.ProjectId,
		DirId:     req.DirId,
		CaseId:    req.CaseId,
	})
	if err != nil {
		return nil, err
	}

	mapData := httpc.MapData{}
	marshal := jsonx.MarshalIgnoreError(req.BlackBoxCaseBaseMap)
	err = jsonx.Unmarshal(marshal, &mapData)
	if err != nil {
		return nil, err
	}

	knowledgeIn := &pb.GetBlackBoxCaseKnowledgeV2Req{
		ProjectId: req.ProjectId,
		DirId:     req.DirId,
		CaseId:    req.CaseId,
	}

	knowledgeOut, err := l.svcCtx.BlackBoxCaseKnowledgeV2ServiceRpc.GetBlackBoxCaseKnowledge(l.ctx, knowledgeIn)
	if err != nil {
		return nil, err
	}
	var tags []string
	if knowledgeOut.Tags != "" {
		if err = json.Unmarshal([]byte(knowledgeOut.Tags), &tags); err != nil {
			return nil, errors.Wrapf(err, "failed to unmarshal tags from JSON")
		}
	}
	var experiences []httpc.KnowledgeExperience
	if knowledgeOut.Experiences != "" {
		if err = json.Unmarshal([]byte(knowledgeOut.Experiences), &experiences); err != nil {
			return nil, errors.Wrapf(err, "failed to unmarshal experiences from JSON")
		}
	}

	knowledgeSupplementDoc := &httpc.SupplementDoc{}
	if req.KnowledgeSupplementEnable {
		if err = utils.Copy(knowledgeSupplementDoc, req.KnowledgeSupplementDoc); err != nil {
			return nil, errors.Wrapf(
				errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy data[%+v] to request, error: %+v", req,
				err,
			)
		}
	}
	if knowledgeSupplementDoc.Feishu.KnowledgeDocPgTitle == nil {
		knowledgeSupplementDoc.Feishu.KnowledgeDocPgTitle = []*httpc.KnowledgeDocPgTitle{}
	}

	reviewMap, err := httpc.UpdateTestSceneReviewMap(l.svcCtx.ExternalAiDomain, httpc.UpdateBlackBoxTestSceneReviewMapReq{
		MapId:                   caseMap.GetMapId(),
		ProjectID:               req.ProjectId,
		KnowledgeDocID:          blackboxCase.GetItem().GetKnowledgeId(),
		KnowledgeDocPgTitles:    common.KnowledgeDocPgTitlePbStruct2Msg(blackboxCase.GetItem().GetKnowledgeParagraphTitle()),
		KnowledgeTermList:       tags,
		KnowledgeExperienceList: experiences,
		ModelCharacteristic:     blackboxCase.GetItem().GetCaseModelCharacter(),
		KnowledgeSupplementDoc:  *knowledgeSupplementDoc,
		FunctionPoint:           req.FunctionPoint,
		GenerateOpinion:         req.GenerateOpinion,
		MapData:                 mapData,
	})
	if err != nil {
		return nil, err
	}

	return reviewMap.Data, nil
}
