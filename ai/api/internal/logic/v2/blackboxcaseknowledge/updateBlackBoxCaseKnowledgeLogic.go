package blackboxcaseknowledge

import (
	"context"
	"encoding/json"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"github.com/pkg/errors"
)

type UpdateBlackBoxCaseKnowledgeLogic struct {
	*logic.BaseLogic
}

// update blackbox case knowledge
func NewUpdateBlackBoxCaseKnowledgeLogic(ctx context.Context, svcCtx *svc.ServiceContext) *UpdateBlackBoxCaseKnowledgeLogic {
	return &UpdateBlackBoxCaseKnowledgeLogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),
	}
}

func (l *UpdateBlackBoxCaseKnowledgeLogic) UpdateBlackBoxCaseKnowledge(req *types.UpdateBlackBoxCaseKnowledgeV2Req) (resp *types.UpdateBlackBoxCaseKnowledgeV2Resp, err error) {
	in := &pb.UpdateBlackBoxCaseKnowledgeV2Req{}
	if err = utils.Copy(in, req, l.Converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy data[%+v] to request, error: %+v", req,
			err,
		)
	}

	// Serialize tags to JSON string
	tagsBytes, err := json.Marshal(req.Tags)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to marshal tags to JSON")
	}
	in.Tags = string(tagsBytes)

	// Serialize experiences to JSON string
	experiencesBytes, err := json.Marshal(req.Experiences)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to marshal experiences to JSON")
	}
	in.Experiences = string(experiencesBytes)

	_, err = l.SvcCtx.BlackBoxCaseKnowledgeV2ServiceRpc.UpdateBlackBoxCaseKnowledge(l.Ctx, in)
	if err != nil {
		return nil, err
	}

	return &types.UpdateBlackBoxCaseKnowledgeV2Resp{}, nil
}
