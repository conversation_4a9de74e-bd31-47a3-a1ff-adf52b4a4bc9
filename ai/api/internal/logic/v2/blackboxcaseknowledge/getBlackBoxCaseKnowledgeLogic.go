package blackboxcaseknowledge

import (
	"context"
	"encoding/json"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"
)

type GetBlackBoxCaseKnowledgeLogic struct {
	*logic.BaseLogic
}

// get blackbox case knowledge
func NewGetBlackBoxCaseKnowledgeLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetBlackBoxCaseKnowledgeLogic {
	return &GetBlackBoxCaseKnowledgeLogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),
	}
}

func (l *GetBlackBoxCaseKnowledgeLogic) GetBlackBoxCaseKnowledge(req *types.GetBlackBoxCaseKnowledgeV2Req) (resp *types.GetBlackBoxCaseKnowledgeV2Resp, err error) {
	in := &pb.GetBlackBoxCaseKnowledgeV2Req{}
	if err = utils.Copy(in, req, l.Converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy data[%+v] to request, error: %+v", req,
			err,
		)
	}

	out, err := l.SvcCtx.BlackBoxCaseKnowledgeV2ServiceRpc.GetBlackBoxCaseKnowledge(l.Ctx, in)
	if err != nil {
		return nil, err
	}
	resp = &types.GetBlackBoxCaseKnowledgeV2Resp{}
	if err = utils.Copy(resp, out, l.Converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy data[%+v] to response, error: %+v",
			out, err,
		)
	}

	// Deserialize tags from JSON string
	if out.Tags != "" {
		var tags []*string
		if err = json.Unmarshal([]byte(out.Tags), &tags); err != nil {
			return nil, errors.Wrapf(err, "failed to unmarshal tags from JSON")
		}
		resp.Tags = tags
	} else {
		resp.Tags = []*string{}
	}

	// Deserialize experiences from JSON string
	if out.Experiences != "" {
		var experiences []*types.BlackBoxCaseKnowledgeExperience
		if err = json.Unmarshal([]byte(out.Experiences), &experiences); err != nil {
			return nil, errors.Wrapf(err, "failed to unmarshal experiences from JSON")
		}
		resp.Experiences = experiences
	} else {
		resp.Experiences = []*types.BlackBoxCaseKnowledgeExperience{}
	}

	return resp, nil
}
