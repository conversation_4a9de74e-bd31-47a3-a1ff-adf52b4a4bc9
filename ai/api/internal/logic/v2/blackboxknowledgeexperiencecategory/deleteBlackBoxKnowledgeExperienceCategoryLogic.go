package blackboxknowledgeexperiencecategory

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/public/httpc"

	"github.com/zeromicro/go-zero/core/logx"
)

type DeleteBlackBoxKnowledgeExperienceCategoryLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// delete blackbox knowledge experience category
func NewDeleteBlackBoxKnowledgeExperienceCategoryLogic(ctx context.Context, svcCtx *svc.ServiceContext) *DeleteBlackBoxKnowledgeExperienceCategoryLogic {
	return &DeleteBlackBoxKnowledgeExperienceCategoryLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *DeleteBlackBoxKnowledgeExperienceCategoryLogic) DeleteBlackBoxKnowledgeExperienceCategory(req *types.DeleteBlackBoxKnowledgeExperienceCategoryReq) (resp any, err error) {
	experienceRes, err := httpc.DeleteKnowledgeTestExperienceCategory(l.svcCtx.ExternalAiDomain, httpc.DeleteKnowledgeTestExperienceCategoryReq{
		Id: int64(req.Id),
	})
	if err != nil {
		l.Logger.Errorf("failed to delete blackbox knowledge experience category, error: %+v", err)
		return nil, err
	}

	if experienceRes.Code != 0 {
		return nil, errors.New("调用AI接口异常: " + experienceRes.Message)
	}

	return experienceRes.Data, nil
}
