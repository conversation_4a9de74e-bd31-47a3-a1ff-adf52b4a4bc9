package blackboxknowledgeexperiencecategory

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/public/httpc"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type ListBlackBoxKnowledgeExperienceCategoriesLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// list blackbox knowledge experience categories
func NewListBlackBoxKnowledgeExperienceCategoriesLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ListBlackBoxKnowledgeExperienceCategoriesLogic {
	return &ListBlackBoxKnowledgeExperienceCategoriesLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *ListBlackBoxKnowledgeExperienceCategoriesLogic) ListBlackBoxKnowledgeExperienceCategories(req *types.ListBlackBoxKnowledgeExperienceCategoriesReq) (resp any, err error) {
	var testGeniusReq httpc.ListKnowledgeTestExperienceCategoryReq

	err = utils.Copy(&testGeniusReq, req)
	if err != nil {
		l.Logger.Errorf("failed to copy data[%+v] to request, error: %+v", req, err)
		return nil, err
	}

	experienceRes, err := httpc.ListKnowledgeTestExperienceCategory(l.svcCtx.ExternalAiDomain, testGeniusReq)
	if err != nil {
		l.Logger.Errorf("failed to list blackbox knowledge experience category, error: %+v", err)
		return nil, err
	}

	if experienceRes.Code != 0 {
		return nil, errors.New("调用AI接口异常: " + experienceRes.Message)
	}

	return experienceRes.Data, nil
}
