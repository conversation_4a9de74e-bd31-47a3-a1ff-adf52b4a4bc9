package blackboxcasemapfunction

import (
	"context"
	"strconv"
	"time"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sse"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/tools"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/types"
)

type CreateBlackBoxCaseMapFunctionWithSseLogic struct {
	*logic.BaseLogic
	*CreateBlackBoxCaseMapFunctionLogic
}

// create blackbox case map function
func NewCreateBlackBoxCaseMapFunctionWithSseLogic(
	ctx context.Context, svcCtx *svc.ServiceContext,
) *CreateBlackBoxCaseMapFunctionWithSseLogic {
	return &CreateBlackBoxCaseMapFunctionWithSseLogic{
		BaseLogic:                          logic.NewBaseLogic(ctx, svcCtx),
		CreateBlackBoxCaseMapFunctionLogic: NewCreateBlackBoxCaseMapFunctionLogic(ctx, svcCtx),
	}
}

func (l *CreateBlackBoxCaseMapFunctionWithSseLogic) CreateBlackBoxCaseMapFunctionWithSse(req *types.BlackBoxCaseMapSupplementRequest) (
	resp *sse.Stream, err error,
) {
	stream := sse.NewStream(strconv.Itoa(int(time.Now().UnixMilli())), 0)
	fn := func() (result any, err error) {
		result, err = l.CreateBlackBoxCaseMapFunctionLogic.create(
			&types.BlackBoxCaseMapSupplementRequest{
				ProjectId:                 req.ProjectId,
				DirId:                     req.DirId,
				CaseId:                    req.CaseId,
				KnowledgeSupplementEnable: req.KnowledgeSupplementEnable,
				KnowledgeSupplementDoc:    req.KnowledgeSupplementDoc,
				FunctionPoint:             req.FunctionPoint,
				GenerateOpinion:           req.GenerateOpinion,
			},
		)
		return result, err
	}

	tools.HandleSseStream(l.BaseLogic.Ctx, stream, fn)

	return stream, nil
}
