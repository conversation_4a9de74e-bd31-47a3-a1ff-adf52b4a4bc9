package blackboxcasedata

import (
	"context"
	"encoding/json"
	"strconv"
	"time"

	common2 "gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/common"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/common/constants"

	"github.com/zeromicro/go-zero/core/threading"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/tools"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/public"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/public/httpc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sse"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type ModifyBlackBoxCaseDataWithSseLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// modify blackbox case data with sse
func NewModifyBlackBoxCaseDataWithSseLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ModifyBlackBoxCaseDataWithSseLogic {
	return &ModifyBlackBoxCaseDataWithSseLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *ModifyBlackBoxCaseDataWithSseLogic) ModifyBlackBoxCaseDataWithSse(req *types.ModifyBlackBoxCaseDataWithSseReq) (resp *sse.Stream, err error) {
	stream := sse.NewStream(strconv.Itoa(int(time.Now().UnixMilli())), 0)
	fn := func() (result any, err error) {
		result, err = l.modify(
			&types.ModifyBlackBoxCaseDataWithSseReq{
				ProjectId:  req.ProjectId,
				DirId:      req.DirId,
				CaseId:     req.CaseId,
				RevisionId: req.RevisionId,
			},
		)
		return result, err
	}

	tools.HandleSseStream(l.ctx, stream, fn)

	return stream, nil
}

func (l *ModifyBlackBoxCaseDataWithSseLogic) modify(req *types.ModifyBlackBoxCaseDataWithSseReq) (resp *types.ListBlackBoxCaseDataResp, err error) {
	// 1. fetch doc_id/title/model_characteristic from black_box_case_dir_v2 table;
	caseIn := &pb.GetBlackBoxCaseReq{
		DirId:  req.DirId,
		CaseId: req.CaseId,
	}

	caseOut, err := l.svcCtx.BlackBoxCaseServiceRpc.GetBlackBoxCase(l.ctx, caseIn)
	if err != nil {
		return nil, err
	}

	// 2. fetch func supplement documents from black_box_case_map_document table;
	docs, err := l.svcCtx.BlackBoxCaseMapDocumentServiceRpc.ListBlackBoxCaseMapDocuments(
		l.ctx, &pb.ListBlackBoxCaseMapDocumentsReq{
			ProjectId: req.ProjectId,
			DirId:     req.DirId,
			CaseId:    req.CaseId,
		},
	)
	if err != nil {
		return nil, err
	}

	// 3. fetch map from black_box_case table;
	caseMap, err := l.svcCtx.BlackBoxCaseMapServiceRpc.GetBlackBoxCaseMap(
		l.ctx, &pb.GetBlackBoxCaseMapReq{
			ProjectId: req.ProjectId,
			DirId:     req.DirId,
			CaseId:    req.CaseId,
		},
	)
	if err != nil {
		return nil, err
	}

	var mapData httpc.MapData
	if caseMap.Data != "" {
		if err = json.Unmarshal([]byte(caseMap.Data), &mapData); err != nil {
			return nil, errors.Wrapf(err, "failed to unmarshal mapData from JSON")
		}
	}

	var caseResults []httpc.CaseResult
	// 4. if revision_id != nil, fetch case data from black_box_case_data table;

	caseRefType := "edit"
	revisionCaseData, err := l.svcCtx.BlackBoxCaseDataServiceRpc.ListBlackBoxCaseData(
		l.ctx, &pb.ListBlackBoxCaseDataReq{RevisionId: *req.RevisionId},
	)
	if err != nil {
		return nil, err
	}

	if err = utils.Copy(&caseResults, revisionCaseData.Items); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy data[%+v] to request, error: %+v",
			revisionCaseData.Items,
			err,
		)
	}

	// 5. generate case_ref_id;
	refIn := &pb.GenerateBlackBoxCaseRefReq{CaseRefType: caseRefType}
	caseRef, err := l.svcCtx.BlackBoxCaseDataServiceRpc.GenerateBlackBoxCaseRef(l.ctx, refIn)
	if err != nil {
		return nil, err
	}
	// 刷新metrics
	l.svcCtx.BlackBoxCaseDataServiceRpc.RefreshCaseRefMetrics(
		l.ctx, &pb.RefreshCaseRefMetricsReq{CaseRefType: caseRefType},
	)

	// 6. http bypass to external AI;
	//	    success: return case data markdown;
	// 		fail: return error;
	var (
		caseData httpc.ResponseData[[]*pb.BlackBoxCaseData]
	)
	var knowledgeSupplementDoc []httpc.SupplementDoc

	knowledgeSupplementDoc = common.MergeKnowledgeSupplementDoc(docs.GetItems())
	for i := range knowledgeSupplementDoc {
		if knowledgeSupplementDoc[i].Feishu.KnowledgeDocPgTitle == nil {
			knowledgeSupplementDoc[i].Feishu.KnowledgeDocPgTitle = []*httpc.KnowledgeDocPgTitle{}
		}
	}

	caseData, err = httpc.UpdateTestCaseWithV24(
		l.svcCtx.ExternalAiDomain, httpc.UpdateBlackBoxTestCaseReq{
			CaseID:         caseRef.CaseRefId,
			KnowledgeDocID: caseOut.Item.KnowledgeId,
			//KnowledgeDocPgTitle: httpc.KnowledgeDocPgTitle{
			//	Id:    caseOut.Item.KnowledgeParagraphTitleId,
			//	Title: caseOut.Item.KnowledgeParagraphTitleText,
			//},
			KnowledgeDocPgTitles:   common2.KnowledgeDocPgTitlePbStruct2Msg(caseOut.GetItem().GetKnowledgeParagraphTitle()),
			ModelCharacteristic:    caseOut.Item.CaseModelCharacter,
			KnowledgeSupplementDoc: knowledgeSupplementDoc,
			GenerateOpinion:        caseOut.Item.KnowledgeFixSugg,
			ReviewMap:              mapData,
			CaseResult:             caseResults,
		},
	)

	if err != nil {
		return nil, err
	}

	if caseData.Data != nil {
		// 7. create new revision;
		revisionReq := &pb.CreateBlackBoxCaseRevisionReq{
			CaseId:                      req.CaseId,
			RevisionName:                public.GenerateRevisionName(),
			KnowledgeId:                 caseOut.Item.KnowledgeId,
			KnowledgeParagraphTitleText: caseOut.Item.KnowledgeParagraphTitleText,
			CaseRefId:                   caseRef.CaseRefId,
		}
		revision, err := l.svcCtx.BlackBoxCaseRevisionServiceRpc.CreateBlackBoxCaseRevision(l.ctx, revisionReq)
		if err != nil {
			return nil, err
		}

		// 8. save case data to black_box_case_data table;
		dataIn := &pb.CreateBlackBoxCaseDataReq{
			Items: *caseData.Data,
		}
		for i := 0; i < len(dataIn.Items); i++ {
			dataIn.Items[i].RevisionId = revision.RevisionId
			// add version
			dataIn.Items[i].Version = constants.BlackBoxCaseDataVersionV24
		}
		_, err = l.svcCtx.BlackBoxCaseDataServiceRpc.CreateBlackBoxCaseData(l.ctx, dataIn)
		if err != nil {
			return nil, err
		}

		// 9. convert case data to markdown;
		resp = &types.ListBlackBoxCaseDataResp{}
		dataContent, err := httpc.Json2MD(
			l.svcCtx.ExternalAiDomain, httpc.CaseTableReq{
				CaseTable: *caseData.Data,
			},
		)
		if err != nil {
			return nil, err
		}
		resp.Content = *dataContent.Data
	} else {
		return nil, errors.Wrapf(err, "case data is nil from external AI")
	}

	threading.GoSafe(func() {
		ctx := context.Background()
		// delay 30s
		time.Sleep(time.Second * 30)

		count, err := httpc.GetUpdatedCaseAddUpdateCount(l.svcCtx.ExternalAiDomain, httpc.GetUpdatedCaseAddUpdateCountReq{
			CaseRefId: caseRef.CaseRefId,
		})
		if err != nil {
			l.Logger.Errorf("failed to get updated case add count, error: %+v", err)
			return
		}

		//_, err = l.svcCtx.BlackBoxGenerationRecordServiceRpc.AddBlackBoxUpdatedAddCaseCount(ctx, &pb.AddBlackBoxUpdatedAddCaseCountReq{
		//    Count: count.Data.Count,
		//})
		//if err != nil {
		//    l.Logger.Errorf("failed to add black box updated add case count, error: %+v", err)
		//    return
		//}

		_, err = l.svcCtx.BlackBoxCaseRevisionServiceRpc.UpdateBlackBoxCaseRevisionWithCaseRefId(ctx,
			&pb.UpdateBlackBoxCaseRevisionWithCaseRefIdReq{BlackBoxCaseRevision: &pb.BlackBoxCaseRevision{
				CaseRefId:   caseRef.GetCaseRefId(),
				AddCount:    count.Data.Add,
				UpdateCount: count.Data.Update,
			}})
		if err != nil {
			l.Logger.Errorf("failed to update black box case revision, error: %+v", err)
			return
		}
	})

	return resp, nil
}
