package blackboxcasedata

import (
	"context"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/common/constants"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"
)

type UpdateBlackBoxCaseDataLogic struct {
	*logic.BaseLogic
}

// update blackbox case data
func NewUpdateBlackBoxCaseDataLogic(ctx context.Context, svcCtx *svc.ServiceContext) *UpdateBlackBoxCaseDataLogic {
	return &UpdateBlackBoxCaseDataLogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),
	}
}

func (l *UpdateBlackBoxCaseDataLogic) UpdateBlackBoxCaseData(req *types.UpdateBlackBoxCaseDataReq) (resp *types.UpdateBlackBoxCaseDataResp, err error) {
	in := &pb.UpdateBlackBoxCaseDataReq{}
	if err = utils.Copy(in, req, l.Converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy data[%+v] to request, error: %+v", req,
			err,
		)
	}

	in.Version = constants.BlackBoxCaseDataVersionV24

	_, err = l.SvcCtx.BlackBoxCaseDataServiceRpc.UpdateBlackBoxCaseData(l.Ctx, in)
	if err != nil {
		return nil, err
	}

	if in.GetEditedField() != "" {
		switch in.GetEditedField() {
		case "case_name":
			_, err = l.SvcCtx.BlackBoxGenerationRecordServiceRpc.AddBlackBoxCaseNameUpdatedCount(l.Ctx, &pb.AddBlackBoxCaseNameUpdatedCountReq{})
			if err != nil {
				l.Logger.Error(l.Ctx, "failed to add black box case name updated count, error: %+v", err)
				return nil, err
			}
		case "pre_condition":
			_, err = l.SvcCtx.BlackBoxGenerationRecordServiceRpc.AddBlackBoxPreConditionUpdatedCount(l.Ctx, &pb.AddBlackBoxPreConditionUpdatedCountReq{})
			if err != nil {
				l.Logger.Error(l.Ctx, "failed to add black box precondition updated count, error: %+v", err)
				return nil, err
			}
		case "case_step":
			_, err = l.SvcCtx.BlackBoxGenerationRecordServiceRpc.AddBlackBoxCaseStepUpdatedCount(l.Ctx, &pb.AddBlackBoxCaseStepUpdatedCountReq{})
			if err != nil {
				l.Logger.Error(l.Ctx, "failed to add black box case step updated count, error: %+v", err)
				return nil, err
			}
		case "expect_result":
			_, err = l.SvcCtx.BlackBoxGenerationRecordServiceRpc.AddBlackBoxExpectResultUpdatedCount(l.Ctx, &pb.AddBlackBoxExpectResultUpdatedCountReq{})
			if err != nil {
				l.Logger.Error(l.Ctx, "failed to add black box expect result updated count, error: %+v", err)
				return nil, err
			}
		case "case_level":
			_, err = l.SvcCtx.BlackBoxGenerationRecordServiceRpc.AddBlackBoxCaseLevelUpdatedCount(l.Ctx, &pb.AddBlackBoxCaseLevelUpdatedCountReq{})
			if err != nil {
				l.Logger.Error(l.Ctx, "failed to add black box case level updated count, error: %+v", err)
				return nil, err
			}
		}
	}

	return &types.UpdateBlackBoxCaseDataResp{}, nil
}
