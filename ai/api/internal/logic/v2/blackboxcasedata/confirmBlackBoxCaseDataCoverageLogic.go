package blackboxcasedata

import (
	"context"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/client/blackboxcaserevisionservice"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type ConfirmBlackBoxCaseDataCoverageLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// set blackbox case data coverage
func NewConfirmBlackBoxCaseDataCoverageLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ConfirmBlackBoxCaseDataCoverageLogic {
	return &ConfirmBlackBoxCaseDataCoverageLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *ConfirmBlackBoxCaseDataCoverageLogic) ConfirmBlackBoxCaseDataCoverage(req *types.SetBlackBoxCaseDataCoverageReq) (resp *types.SetBlackBoxCaseDataCoverageResp, err error) {
	resp = &types.SetBlackBoxCaseDataCoverageResp{}
	_, err = l.svcCtx.BlackBoxCaseRevisionServiceRpc.UpdateBlackBoxCaseRevision(l.ctx,
		&blackboxcaserevisionservice.UpdateBlackBoxCaseRevisionReq{BlackBoxCaseRevision: &pb.BlackBoxCaseRevision{
			RevisionId: *req.RevisionId,
			Coverage:   req.Coverage,
		}})
	if err != nil {
		return nil, err
	}

	return resp, nil
}
