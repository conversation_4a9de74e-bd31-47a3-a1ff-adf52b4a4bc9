package blackboxcasedata

import (
	"context"
	"strconv"
	"time"

	"github.com/zeromicro/go-zero/core/logx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sse"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/tools"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/types"
)

type CreateBlackBoxCaseDataWithSseLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
	*CreateBlackBoxCaseDataLogic
}

// create blackbox case data with sse
func NewCreateBlackBoxCaseDataWithSseLogic(
	ctx context.Context, svcCtx *svc.ServiceContext,
) *CreateBlackBoxCaseDataWithSseLogic {
	return &CreateBlackBoxCaseDataWithSseLogic{
		Logger:                      logx.WithContext(ctx),
		ctx:                         ctx,
		svcCtx:                      svcCtx,
		CreateBlackBoxCaseDataLogic: NewCreateBlackBoxCaseDataLogic(ctx, svcCtx),
	}
}

func (l *CreateBlackBoxCaseDataWithSseLogic) CreateBlackBoxCaseDataWithSse(req *types.CreateBlackBoxCaseDataV2WithSseReq) (
	resp *sse.Stream, err error,
) {
	stream := sse.NewStream(strconv.Itoa(int(time.Now().UnixMilli())), 0)
	fn := func() (result any, err error) {
		result, err = l.CreateBlackBoxCaseDataLogic.create(
			&types.CreateBlackBoxCaseDataV2Req{
				ProjectId:  req.ProjectId,
				DirId:      req.DirId,
				CaseId:     req.CaseId,
				RevisionId: req.RevisionId,
			},
		)
		return result, err
	}

	tools.HandleSseStream(l.ctx, stream, fn)

	return stream, nil
}
