package blackboxcasedata

import (
	"context"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/client/blackboxcaserevisionservice"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetBlackBoxCaseDataCoverageLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// get blackbox case data coverage
func NewGetBlackBoxCaseDataCoverageLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetBlackBoxCaseDataCoverageLogic {
	return &GetBlackBoxCaseDataCoverageLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetBlackBoxCaseDataCoverageLogic) GetBlackBoxCaseDataCoverage(req *types.GetBlackBoxCaseDataCoverageReq) (resp *types.GetBlackBoxCaseDataCoverageResp, err error) {
	resp = &types.GetBlackBoxCaseDataCoverageResp{}
	revisionData, err := l.svcCtx.BlackBoxCaseRevisionServiceRpc.GetBlackBoxCaseRevisionByRevisionId(l.ctx,
		&blackboxcaserevisionservice.GetBlackBoxCaseRevisionByRevisionIdReq{RevisionId: *req.RevisionId})
	if err != nil {
		return nil, err
	}

	resp.Coverage = revisionData.GetRevisionData().GetCoverage()

	revisions, err := l.svcCtx.BlackBoxCaseRevisionServiceRpc.ListBlackBoxCaseRevision(l.ctx, &pb.ListBlackBoxCaseRevisionReq{CaseId: revisionData.GetRevisionData().GetCaseId()})
	if err != nil {
		return nil, err
	}

	var lastCreated int64
	for _, revision := range revisions.GetItems() {
		if revision.GetCoverage() != 0 && revision.GetCreatedAt() > lastCreated {
			lastCreated = revision.GetCreatedAt()
			resp.Coverage = revision.GetCoverage()
		}
	}

	return resp, nil
}
