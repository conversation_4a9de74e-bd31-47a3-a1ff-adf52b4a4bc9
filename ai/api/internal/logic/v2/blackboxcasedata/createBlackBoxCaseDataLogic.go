package blackboxcasedata

import (
	"context"
	"encoding/json"

	common2 "gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/common"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/common/constants"

	"github.com/zeromicro/go-zero/core/threading"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/public"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/public/httpc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
)

type CreateBlackBoxCaseDataLogic struct {
	*logic.BaseLogic
}

// create blackbox case data
func NewCreateBlackBoxCaseDataLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CreateBlackBoxCaseDataLogic {
	return &CreateBlackBoxCaseDataLogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),
	}
}

func (l *CreateBlackBoxCaseDataLogic) CreateBlackBoxCaseData(req *types.CreateBlackBoxCaseDataV2Req) (
	resp *types.ListBlackBoxCaseDataResp, err error,
) {
	return l.create(req)
}

func (l *CreateBlackBoxCaseDataLogic) create(req *types.CreateBlackBoxCaseDataV2Req) (
	resp *types.ListBlackBoxCaseDataResp, err error,
) {
	// 1. fetch doc_id/title/model_characteristic from black_box_case_dir_v2 table;
	caseIn := &pb.GetBlackBoxCaseReq{
		DirId:  req.DirId,
		CaseId: req.CaseId,
	}

	caseOut, err := l.SvcCtx.BlackBoxCaseServiceRpc.GetBlackBoxCase(l.Ctx, caseIn)
	if err != nil {
		return nil, err
	}

	// 2. fetch func supplement documents from black_box_case_map_document table;
	docs, err := l.SvcCtx.BlackBoxCaseMapDocumentServiceRpc.ListBlackBoxCaseMapDocuments(
		l.Ctx, &pb.ListBlackBoxCaseMapDocumentsReq{
			ProjectId: req.ProjectId,
			DirId:     req.DirId,
			CaseId:    req.CaseId,
		},
	)
	if err != nil {
		return nil, err
	}

	// 3. fetch map from black_box_case table;
	caseMap, err := l.SvcCtx.BlackBoxCaseMapServiceRpc.GetBlackBoxCaseMap(
		l.Ctx, &pb.GetBlackBoxCaseMapReq{
			ProjectId: req.ProjectId,
			DirId:     req.DirId,
			CaseId:    req.CaseId,
		},
	)
	if err != nil {
		return nil, err
	}

	var mapData httpc.MapData
	if caseMap.Data != "" {
		if err = json.Unmarshal([]byte(caseMap.Data), &mapData); err != nil {
			return nil, errors.Wrapf(err, "failed to unmarshal mapData from JSON")
		}
	}

	caseRefType := "generate"

	var caseResults []httpc.CaseResult
	// 4. if revision_id != nil, fetch case data from black_box_case_data table;
	if req.RevisionId != nil {
		caseRefType = "edit"
		revisionCaseData, err := l.SvcCtx.BlackBoxCaseDataServiceRpc.ListBlackBoxCaseData(
			l.Ctx, &pb.ListBlackBoxCaseDataReq{RevisionId: *req.RevisionId},
		)
		if err != nil {
			return nil, err
		}

		if err = utils.Copy(&caseResults, revisionCaseData.Items, l.Converters...); err != nil {
			return nil, errors.Wrapf(
				errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy data[%+v] to request, error: %+v",
				revisionCaseData.Items,
				err,
			)
		}
	}

	// 5. generate case_ref_id;
	refIn := &pb.GenerateBlackBoxCaseRefReq{CaseRefType: caseRefType}
	caseRef, err := l.SvcCtx.BlackBoxCaseDataServiceRpc.GenerateBlackBoxCaseRef(l.Ctx, refIn)
	if err != nil {
		return nil, err
	}
	// 刷新metrics
	l.SvcCtx.BlackBoxCaseDataServiceRpc.RefreshCaseRefMetrics(
		l.Ctx, &pb.RefreshCaseRefMetricsReq{CaseRefType: caseRefType},
	)

	// 6. http bypass to external AI;
	//	    success: return case data markdown;
	// 		fail: return error;
	var (
		caseData httpc.ResponseData[[]*pb.BlackBoxCaseData]
	)
	var knowledgeSupplementDoc []httpc.SupplementDoc

	knowledgeSupplementDoc = common.MergeKnowledgeSupplementDoc(docs.GetItems())
	for i := range knowledgeSupplementDoc {
		if knowledgeSupplementDoc[i].Feishu.KnowledgeDocPgTitle == nil {
			knowledgeSupplementDoc[i].Feishu.KnowledgeDocPgTitle = []*httpc.KnowledgeDocPgTitle{}
		}
	}

	caseData, err = httpc.CreateTestCase(
		l.SvcCtx.ExternalAiDomain, httpc.CreateBlackBoxTestCaseReq{
			CaseID:         caseRef.CaseRefId,
			KnowledgeDocID: caseOut.Item.KnowledgeId,
			//KnowledgeDocPgTitle: httpc.KnowledgeDocPgTitle{
			//	Id:    caseOut.Item.KnowledgeParagraphTitleId,
			//	Title: caseOut.Item.KnowledgeParagraphTitleText,
			//},
			KnowledgeDocPgTitles:   common2.KnowledgeDocPgTitlePbStruct2Msg(caseOut.GetItem().GetKnowledgeParagraphTitle()),
			ModelCharacteristic:    caseOut.Item.CaseModelCharacter,
			KnowledgeSupplementDoc: knowledgeSupplementDoc,
			ReviewMap:              mapData,
		},
	)

	if err != nil {
		return nil, err
	}

	var revisionId string

	if caseData.Data != nil {
		knowledgeParagraphTitles := make([]string, 0, len(caseOut.Item.KnowledgeParagraphTitle))
		for _, title := range caseOut.Item.KnowledgeParagraphTitle {
			knowledgeParagraphTitles = append(knowledgeParagraphTitles, title.Title)
		}

		// 7. create new revision;
		revisionReq := &pb.CreateBlackBoxCaseRevisionReq{
			CaseId:                      req.CaseId,
			RevisionName:                public.GenerateRevisionName(),
			KnowledgeId:                 caseOut.Item.KnowledgeId,
			KnowledgeParagraphTitleText: caseOut.Item.KnowledgeParagraphTitleText,
			CaseRefId:                   caseRef.CaseRefId,
			KnowledgeParagraphTitle:     knowledgeParagraphTitles,
		}
		revision, err := l.SvcCtx.BlackBoxCaseRevisionServiceRpc.CreateBlackBoxCaseRevision(l.Ctx, revisionReq)
		if err != nil {
			return nil, err
		}

		// 8. save case data to black_box_case_data table;
		dataIn := &pb.CreateBlackBoxCaseDataReq{
			Items: *caseData.Data,
		}
		for i := 0; i < len(dataIn.Items); i++ {
			dataIn.Items[i].RevisionId = revision.RevisionId
			// add version
			dataIn.Items[i].Version = constants.BlackBoxCaseDataVersionV24
		}
		_, err = l.SvcCtx.BlackBoxCaseDataServiceRpc.CreateBlackBoxCaseData(l.Ctx, dataIn)
		if err != nil {
			return nil, err
		}

		revisionId = revision.RevisionId

		// 9. convert case data to markdown;
		resp = &types.ListBlackBoxCaseDataResp{}
		dataContent, err := httpc.Json2MD(
			l.SvcCtx.ExternalAiDomain, httpc.CaseTableReq{
				CaseTable: *caseData.Data,
			},
		)
		if err != nil {
			return nil, err
		}
		resp.Content = *dataContent.Data
	} else {
		return nil, errors.Wrapf(err, "case data is nil from external AI")
	}

	threading.GoSafe(func() {
		// rest context
		ctx := context.Background()

		// 10. get document size;
		size, err := httpc.GetDocumentSize(l.SvcCtx.ExternalAiDomain, httpc.GetDocumentSizeReq{
			CaseRefId: caseRef.CaseRefId,
		})
		if err != nil {
			l.Logger.Error(l.Ctx, "failed to get document size from external AI error:%s", err.Error())
			return
		}

		// 11. update case data size;
		_, err = l.SvcCtx.BlackBoxCaseServiceRpc.UpdateBlackBoxCase(ctx, &pb.UpdateBlackBoxCaseReq{Item: &pb.BlackBoxCase{
			CaseId:        req.CaseId,
			KnowledgeSize: size.Data.Size,
		}})
		if err != nil {
			l.Logger.Error(l.Ctx, "failed to update black box case size error:%s", err.Error())
			return
		}

		// 12. get func coverage
		coverage, err := httpc.GetFuncCoverage(l.SvcCtx.ExternalAiDomain, httpc.GetFuncCoverageReq{
			CaseRefId: caseRef.CaseRefId,
		})
		if err != nil {
			l.Logger.Error(l.Ctx, "failed to get func coverage from external AI error:%s", err.Error())
			return
		}

		// 满95-10, 不要问我为什么
		var coverageRes int64
		coverageRes = int64(coverage.Data.Coverage * 100)
		if coverageRes >= 95 {
			coverageRes -= 10
		}

		// 13. update case data coverage;
		_, err = l.SvcCtx.BlackBoxCaseRevisionServiceRpc.UpdateBlackBoxCaseRevision(ctx,
			&pb.UpdateBlackBoxCaseRevisionReq{
				BlackBoxCaseRevision: &pb.BlackBoxCaseRevision{
					Coverage:   coverageRes,
					RevisionId: revisionId,
				}})
		if err != nil {
			l.Logger.Error(l.Ctx, "failed to update black box case revision coverage error:%s", err.Error())
			return
		}
	})

	return resp, nil
}
