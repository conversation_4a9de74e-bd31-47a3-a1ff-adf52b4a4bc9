package blackboxcasedataprogress

import (
	"context"
	"errors"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/public/httpc"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/types"
)

type GetBlackBoxCaseDataProgressLogic struct {
	*logic.BaseLogic
}

// get blackbox case data progress
func NewGetBlackBoxCaseDataProgressLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetBlackBoxCaseDataProgressLogic {
	return &GetBlackBoxCaseDataProgressLogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),
	}
}

func (l *GetBlackBoxCaseDataProgressLogic) GetBlackBoxCaseDataProgress(req *types.GetBlackBoxCaseDataProgressReq) (resp any, err error) {
	var testGeniusReq httpc.GetBlackBoxCaseEstimatedProgressReq

	testGeniusReq.ControlNode = httpc.ControlNodeType_FuncCase

	caseProgressRes, err := httpc.GetBlackBoxCaseEstimatedProgress(l.SvcCtx.ExternalAiDomain, testGeniusReq)
	if err != nil {
		l.Logger.Errorf("failed to get blackbox-case case progress, error: %+v", err)
		return nil, err
	}

	if caseProgressRes.Code != 0 {
		return nil, errors.New("调用AI接口异常: " + caseProgressRes.Message)
	}

	return caseProgressRes.Data, nil
}
