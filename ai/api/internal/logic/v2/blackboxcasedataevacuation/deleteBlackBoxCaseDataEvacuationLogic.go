package blackboxcasedataevacuation

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"
)

type DeleteBlackBoxCaseDataEvacuationLogic struct {
	*logic.BaseLogic
}

// delete blackbox case data evacuation
func NewDeleteBlackBoxCaseDataEvacuationLogic(ctx context.Context, svcCtx *svc.ServiceContext) *DeleteBlackBoxCaseDataEvacuationLogic {
	return &DeleteBlackBoxCaseDataEvacuationLogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),
	}
}

func (l *DeleteBlackBoxCaseDataEvacuationLogic) DeleteBlackBoxCaseDataEvacuation(req *types.ClearKeepBlackBoxCaseDataReq) (resp *types.ClearKeepBlackBoxCaseDataResp, err error) {
	in := &pb.ClearKeepBlackBoxCaseDataReq{}
	if err = utils.Copy(in, req, l.Converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy data[%+v] to request, error: %+v", req,
			err,
		)
	}

	_, err = l.SvcCtx.BlackBoxCaseDataServiceRpc.ClearKeepBlackBoxCaseData(l.Ctx, in)
	if err != nil {
		return nil, err
	}

	return &types.ClearKeepBlackBoxCaseDataResp{}, nil
}
