package blackboxcasedatamap

import (
	"context"
	"fmt"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/public/httpc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
)

type GetBlackBoxCaseDataMapLogic struct {
	*logic.BaseLogic
}

// get blackbox case data map
func NewGetBlackBoxCaseDataMapLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetBlackBoxCaseDataMapLogic {
	return &GetBlackBoxCaseDataMapLogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),
	}
}

func (l *GetBlackBoxCaseDataMapLogic) GetBlackBoxCaseDataMap(req *types.GetBlackBoxCaseDataMapReq) (resp any, err error) {
	var testGeniusReq httpc.GetBlackBoxCaseMindReq

	revisionData, err := l.SvcCtx.BlackBoxCaseRevisionServiceRpc.GetBlackBoxCaseRevisionByRevisionId(l.Ctx, &pb.GetBlackBoxCaseRevisionByRevisionIdReq{
		RevisionId: req.RevisionId,
	})
	if err != nil {
		return nil, err
	}

	rpcResult, err := l.SvcCtx.BlackBoxCaseDataServiceRpc.ListBlackBoxCaseData(l.Ctx,
		&pb.ListBlackBoxCaseDataReq{RevisionId: revisionData.GetRevisionData().GetRevisionId()})
	if err != nil {
		return nil, err
	}

	if len(rpcResult.GetItems()) == 0 {
		return nil, fmt.Errorf("当前版本没有生成用例导图")
	}

	tableData := make([]*httpc.BlackBoxCaseMindTableData, 0, len(rpcResult.GetItems()))

	for _, data := range rpcResult.GetItems() {
		tableData = append(tableData, &httpc.BlackBoxCaseMindTableData{
			OrderId:      data.GetOrderId(),
			Requirement:  data.GetRequirement(),
			CaseName:     data.GetCaseName(),
			PreCondition: data.GetPreCondition(),
			CaseStep:     data.GetCaseStep(),
			ExpectResult: data.GetExpectResult(),
			Terminal:     data.GetTerminal(),
			CaseLevel:    data.GetCaseLevel(),
			Tag:          data.GetTag(),
			CaseDataId:   data.GetCaseDataId(),
		})
	}

	// adopt case
	_, err = l.SvcCtx.BlackBoxCaseRevisionServiceRpc.AdoptBlackBoxCaseRevision(l.Ctx, &pb.AdoptBlackBoxCaseRevisionReq{RevisionId: req.RevisionId})
	if err != nil {
		l.Logger.Errorf("adopt black box case revision failed, err: %v", err)
	}

	testGeniusReq.MindType = httpc.MindType(req.MindType)

	testGeniusReq.KnowledgeDocPgTitle = req.ParagraphTitle
	//testGeniusReq.KnowledgeDocPgTitle = revisionData.GetRevisionData().GetKnowledgeParagraphTitleText()
	testGeniusReq.TableData = tableData

	_, err = l.SvcCtx.BlackBoxCaseRevisionServiceRpc.AdoptBlackBoxCaseRevision(l.Ctx, &pb.AdoptBlackBoxCaseRevisionReq{
		RevisionId: req.RevisionId,
	})
	if err != nil {
		l.Logger.Error(l.Ctx, "failed to adopt revision, error: %+v", err)
	}

	mindResp, err := httpc.GetBlackBoxCaseMind(l.SvcCtx.ExternalAiDomain, testGeniusReq)
	if err != nil {
		return nil, err
	}

	return mindResp.Data, nil
}
