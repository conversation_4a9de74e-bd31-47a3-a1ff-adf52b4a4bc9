package blackboxcasemap

import (
	"context"
	"encoding/json"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"github.com/pkg/errors"
)

type UpdateBlackBoxCaseMapLogic struct {
	*logic.BaseLogic
}

// update blackbox case map
func NewUpdateBlackBoxCaseMapLogic(ctx context.Context, svcCtx *svc.ServiceContext) *UpdateBlackBoxCaseMapLogic {
	return &UpdateBlackBoxCaseMapLogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),
	}
}

func (l *UpdateBlackBoxCaseMapLogic) UpdateBlackBoxCaseMap(req *types.UpdateBlackBoxCaseMapReq) (resp *types.UpdateBlackBoxCaseMapResp, err error) {
	in := &pb.UpdateBlackBoxCaseMapReq{}
	if err = utils.Copy(in, req, l.Converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy data[%+v] to request, error: %+v", req,
			err,
		)
	}

	data, err := json.Marshal(req.Data)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to marshal data to JSON")
	}
	in.Data = string(data)

	_, err = l.SvcCtx.BlackBoxCaseMapServiceRpc.UpdateBlackBoxCaseMap(l.Ctx, in)
	if err != nil {
		return nil, err
	}

	return &types.UpdateBlackBoxCaseMapResp{}, nil
}
