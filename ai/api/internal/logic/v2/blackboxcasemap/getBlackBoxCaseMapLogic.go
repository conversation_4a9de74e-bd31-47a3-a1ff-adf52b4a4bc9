package blackboxcasemap

import (
	"context"
	"encoding/json"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"
)

type GetBlackBoxCaseMapLogic struct {
	*logic.BaseLogic
}

// get blackbox case map
func NewGetBlackBoxCaseMapLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetBlackBoxCaseMapLogic {
	return &GetBlackBoxCaseMapLogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),
	}
}

func (l *GetBlackBoxCaseMapLogic) GetBlackBoxCaseMap(req *types.GetBlackBoxCaseMapReq) (resp *types.BlackBoxCaseMapNode, err error) {
	resp = &types.BlackBoxCaseMapNode{}

	in := &pb.GetBlackBoxCaseMapReq{}
	if err = utils.Copy(in, req, l.Converters...); err != nil {
		return resp, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy data[%+v] to request, error: %+v", req,
			err,
		)
	}

	out, err := l.SvcCtx.BlackBoxCaseMapServiceRpc.GetBlackBoxCaseMap(l.Ctx, in)
	if err != nil {
		return resp, err
	}
	if out.Data == "" {
		return nil, nil
	}

	err = json.Unmarshal([]byte(out.Data), &resp)
	if err != nil {
		return resp, errors.Wrapf(err, "failed to unmarshal data from JSON")
	}

	return resp, nil
}
