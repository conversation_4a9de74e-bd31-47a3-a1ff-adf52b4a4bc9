package blackboxcasemap

import (
	"context"
	"strconv"
	"time"

	"github.com/zeromicro/go-zero/core/logx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sse"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/tools"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/types"
)

type CreateBlackBoxCaseMapWithSseLogic struct {
	logx.Logger
	ctx                        context.Context
	svcCtx                     *svc.ServiceContext
	createBlackBoxCaseMapLogic *CreateBlackBoxCaseMapLogic
}

// create blackbox case map with sse
func NewCreateBlackBoxCaseMapWithSseLogic(
	ctx context.Context, svcCtx *svc.ServiceContext,
) *CreateBlackBoxCaseMapWithSseLogic {
	return &CreateBlackBoxCaseMapWithSseLogic{
		Logger:                     logx.WithContext(ctx),
		ctx:                        ctx,
		svcCtx:                     svcCtx,
		createBlackBoxCaseMapLogic: NewCreateBlackBoxCaseMapLogic(ctx, svcCtx),
	}
}

//func (l *CreateBlackBoxCaseMapWithSseLogic) CreateBlackBoxCaseMapWithSse(req *types.CreateBlackBoxCaseMapWithSseReq) (resp *sse.Stream, err error) {
//    stream := sse.NewStream(strconv.Itoa(int(time.Now().UnixMilli())), 0)
//    stopChan := make(chan struct{})
//    //defer close(stopChan)
//
//    stopTimeCh := make(chan struct{})
//    //defer close(stopTimeCh)
//
//    go func() {
//        defer func() {
//            if err := recover(); err != nil {
//                l.Error(err)
//            }
//        }()
//        respData := model.SseMessage{
//            Code:    0,
//            Message: "",
//            Data:    nil,
//        }
//
//        errChan := make(chan error)
//
//        go func() {
//            defer func() {
//                if err := recover(); err != nil {
//                    l.Error(err)
//                }
//                //close(errChan)
//            }()
//            l.Logger.Infof("准备创建用例,caseId:%s", req.CaseId)
//            // init
//            stream.Event <- &sse.Event{
//                ID:   []byte(stream.ID),
//                Data: jsonx.MarshalIgnoreError(respData),
//            }
//
//            result, err := l.createBlackBoxCaseMapLogic.create(&types.CreateBlackBoxCaseMapReq{
//                ProjectId: req.ProjectId,
//                DirId:     req.DirId,
//                CaseId:    req.CaseId,
//            })
//
//            //err = fmt.Errorf("创建用例失败: 直接挂了")
//
//            l.Logger.Infof("创建用例完成,caseId:%s", req.CaseId)
//            if err != nil {
//                errChan <- err
//                return
//            }
//
//            respData.Data = result
//            stream.Event <- &sse.Event{
//                ID:   []byte(stream.ID),
//                Data: jsonx.MarshalIgnoreError(respData),
//            }
//            stopChan <- struct{}{}
//        }()
//
//        select {
//        case <-l.ctx.Done():
//            l.Error("客户端断开...")
//            stopTimeCh <- struct{}{}
//        case err = <-errChan:
//            if err != nil {
//                l.Error("流异常:", err)
//                stream.Event <- &sse.Event{
//                    ID:   []byte(stream.ID),
//                    Data: jsonx.MarshalIgnoreError(model.HandlerSseError(err)),
//                }
//                stream.Quit()
//                return
//            }
//        case <-stopChan:
//            stream.Quit()
//            return
//        }
//    }()
//
//    return stream, nil
//}

func (l *CreateBlackBoxCaseMapWithSseLogic) CreateBlackBoxCaseMapWithSse(req *types.CreateBlackBoxCaseMapWithSseReq) (
	resp *sse.Stream, err error,
) {
	stream := sse.NewStream(strconv.Itoa(int(time.Now().UnixMilli())), 0)
	fn := func() (result any, err error) {
		result, err = l.createBlackBoxCaseMapLogic.create(
			&types.CreateBlackBoxCaseMapReq{
				ProjectId: req.ProjectId,
				DirId:     req.DirId,
				CaseId:    req.CaseId,
			},
		)
		return result, err
	}

	tools.HandleSseStream(l.ctx, stream, fn)

	return stream, nil
}
