package blackboxcasemap

import (
	"context"
	"encoding/json"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/common"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/public/httpc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
)

type CreateBlackBoxCaseMapLogic struct {
	*logic.BaseLogic
}

// create blackbox case map
func NewCreateBlackBoxCaseMapLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CreateBlackBoxCaseMapLogic {
	return &CreateBlackBoxCaseMapLogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),
	}
}

func (l *CreateBlackBoxCaseMapLogic) CreateBlackBoxCaseMap(req *types.CreateBlackBoxCaseMapReq) (resp any, err error) {
	return l.create(req)
}

func (l *CreateBlackBoxCaseMapLogic) create(req *types.CreateBlackBoxCaseMapReq) (resp any, err error) {
	// 1. fetch doc_id/title/model_characteristic from black_box_case_dir_v2 table;
	caseIn := &pb.GetBlackBoxCaseReq{
		DirId:  req.DirId,
		CaseId: req.CaseId,
	}

	caseOut, err := l.SvcCtx.BlackBoxCaseServiceRpc.GetBlackBoxCase(l.Ctx, caseIn)
	if err != nil {
		return nil, err
	}
	// caseOut.Item.KnowledgeId
	// caseOut.Item.KnowledgeParagraphTitleId
	// CaseOut.Item.KnowledgeParagraphTitleText 从缓存中获取，或者，新加一个字段
	// caseOut.Item.CaseModelCharacter

	// 2. fetch tags/experiences from black_box_case_map table;
	knowledgeIn := &pb.GetBlackBoxCaseKnowledgeV2Req{
		ProjectId: req.ProjectId,
		DirId:     req.DirId,
		CaseId:    req.CaseId,
	}

	knowledgeOut, err := l.SvcCtx.BlackBoxCaseKnowledgeV2ServiceRpc.GetBlackBoxCaseKnowledge(l.Ctx, knowledgeIn)
	if err != nil {
		return nil, err
	}
	// knowledgeOut.Tags
	// knowledgeOut.Experiences
	var tags []string
	if knowledgeOut.Tags != "" {
		if err = json.Unmarshal([]byte(knowledgeOut.Tags), &tags); err != nil {
			return nil, errors.Wrapf(err, "failed to unmarshal tags from JSON")
		}
	}
	var experiences []httpc.KnowledgeExperience
	if knowledgeOut.Experiences != "" {
		if err = json.Unmarshal([]byte(knowledgeOut.Experiences), &experiences); err != nil {
			return nil, errors.Wrapf(err, "failed to unmarshal experiences from JSON")
		}
	}

	// 3. generate map_id that has been saved to cache;
	mapId, err := l.SvcCtx.BlackBoxCaseMapServiceRpc.CreateBlackBoxCaseMapId(l.Ctx, &pb.CreateBlackBoxCaseMapIdReq{CaseId: req.CaseId})
	if err != nil {
		return nil, err
	}

	// 4. http bypass to external AI;
	mapResp, err := httpc.CreateBaseReviewMap(l.SvcCtx.ExternalAiDomain, httpc.CreateBlackBoxBaseReviewMapReq{
		MapId:          mapId.MapId,
		ProjectId:      req.ProjectId,
		KnowledgeDocId: caseOut.Item.KnowledgeId,
		//KnowledgeDocPgTitle: httpc.KnowledgeDocPgTitle{
		//    Id:    caseOut.Item.KnowledgeParagraphTitleId,
		//    Title: caseOut.Item.KnowledgeParagraphTitleText,
		//},
		KnowledgeDocPgTitles:    common.KnowledgeDocPgTitlePbStruct2Msg(caseOut.GetItem().GetKnowledgeParagraphTitle()),
		KnowledgeTermList:       tags,
		KnowledgeExperienceList: experiences,
		ModelCharacteristic:     caseOut.Item.CaseModelCharacter,
	})
	if err != nil {
		l.Logger.Errorf("failed to create blackbox case map, error: %+v", err)
	}
	if mapResp.Code != 0 {
		return nil, errors.New("调用AI接口异常: " + mapResp.Message)
	}

	_, err = l.SvcCtx.BlackBoxGenerationRecordServiceRpc.AddBlackBoxBaseMapCount(l.Ctx, &pb.AddBlackBoxBaseMapCountReq{})
	if err != nil {
		l.Logger.Error(l.Ctx, "failed to add black box base map count error:%s", err.Error())
	}

	// 5. save data to black_box_case_map table;
	data, err := json.Marshal(*mapResp.Data)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to marshal mapResp.Data to JSON")
	}
	_, err = l.SvcCtx.BlackBoxCaseMapServiceRpc.UpdateBlackBoxCaseMap(l.Ctx, &pb.UpdateBlackBoxCaseMapReq{
		ProjectId: req.ProjectId,
		DirId:     req.DirId,
		CaseId:    req.CaseId,
		Data:      string(data),
	})
	if err != nil {
		return nil, err
	}

	// 6. save used_knowledge to black_box_case_map table;
	knowledgeRes, err := httpc.GetBlackBoxCaseUsedKnowledge(l.SvcCtx.ExternalAiDomain, httpc.GetBlackBoxCaseUsedKnowledgeReq{MapId: mapId.MapId})
	if err != nil {
		l.Logger.Errorf("failed to get used knowledge from external AI, error: %+v", err)
		return nil, err
	}
	if knowledgeRes.Code != 0 {
		return nil, errors.New("调用AI接口异常: " + knowledgeRes.Message)
	}

	usedKnowledge, err := json.Marshal(knowledgeRes.Data)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to marshal knowledgeRes.Data to JSON")
	}
	_, err = l.SvcCtx.BlackBoxCaseKnowledgeV2ServiceRpc.UpdateBlackBoxCaseMapUsedKnowledge(l.Ctx, &pb.UpdateBlackBoxCaseMapUsedKnowledgeReq{
		ProjectId:     req.ProjectId,
		DirId:         req.DirId,
		CaseId:        req.CaseId,
		UsedKnowledge: string(usedKnowledge),
	})
	if err != nil {
		return nil, err
	}

	// 7. save recall_document to black_box_case_map table;
	docRes, err := httpc.ListBlackBoxCaseMapDocuments(l.SvcCtx.ExternalAiDomain, httpc.ListBlackBoxCaseMapDocumentsReq{
		MapId: mapId.MapId,
	})
	if err != nil {
		l.Logger.Errorf("failed to get recall document from external AI, error: %+v", err)
		return nil, err
	}
	if docRes.Code != 0 {
		return nil, errors.New("调用AI接口异常: " + docRes.Message)
	}

	recallDoc, err := json.Marshal(docRes.Data)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to marshal docRes.Data to JSON")
	}
	_, err = l.SvcCtx.BlackBoxCaseMapDocumentServiceRpc.UpdateBlackBoxCaseMapRecallDocument(l.Ctx, &pb.UpdateBlackBoxCaseMapRecallDocumentReq{
		ProjectId:      req.ProjectId,
		DirId:          req.DirId,
		CaseId:         req.CaseId,
		RecallDocument: string(recallDoc),
	})
	if err != nil {
		return nil, err
	}

	// 8. return data with node format;
	// resp = &types.BlackBoxCaseMapNode{}
	// if err = utils.Copy(resp, *mapResp.Data, l.Converters...); err != nil {
	// 	return nil, errors.Wrapf(
	// 		errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy data[%+v] to response, error: %+v",
	// 		*mapResp.Data, err,
	// 	)
	// }

	// 9. delete before documents from black_box_case_map_document table;
	_, err = l.SvcCtx.BlackBoxCaseMapDocumentServiceRpc.DeleteBlackBoxCaseMapDocument(l.Ctx, &pb.DeleteBlackBoxCaseMapDocumentReq{
		ProjectId: req.ProjectId,
		DirId:     req.DirId,
		CaseId:    req.CaseId,
	})
	if err != nil {
		return nil, err
	}

	return mapResp.Data, nil
}
