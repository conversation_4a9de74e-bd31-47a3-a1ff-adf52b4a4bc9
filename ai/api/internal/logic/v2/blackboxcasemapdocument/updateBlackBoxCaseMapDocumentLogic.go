package blackboxcasemapdocument

import (
	"context"
	"encoding/json"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"
)

type UpdateBlackBoxCaseMapDocumentLogic struct {
	*logic.BaseLogic
}

// update blackbox case map document
func NewUpdateBlackBoxCaseMapDocumentLogic(ctx context.Context, svcCtx *svc.ServiceContext) *UpdateBlackBoxCaseMapDocumentLogic {
	return &UpdateBlackBoxCaseMapDocumentLogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),
	}
}

func (l *UpdateBlackBoxCaseMapDocumentLogic) UpdateBlackBoxCaseMapDocument(req *types.UpdateBlackBoxCaseMapDocumentReq) (resp *types.UpdateBlackBoxCaseMapDocumentResp, err error) {
	in := &pb.BlackBoxCaseMapDocument{}
	if err = utils.Copy(in, req, l.Converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy data[%+v] to request, error: %+v", req,
			err,
		)
	}

	// Serialize func_doc to JSON string
	if req.FuncDoc != nil {
		funcDocBytes, err := json.Marshal(req.FuncDoc)
		if err != nil {
			return nil, errors.Wrapf(err, "failed to marshal func_doc to JSON")
		}
		in.FuncDoc = string(funcDocBytes)
	}

	// Serialize expe_doc to JSON string
	if req.ExpeDoc != nil {
		expeDocBytes, err := json.Marshal(req.ExpeDoc)
		if err != nil {
			return nil, errors.Wrapf(err, "failed to marshal expe_doc to JSON")
		}
		in.ExpeDoc = string(expeDocBytes)
	}

	_, err = l.SvcCtx.BlackBoxCaseMapDocumentServiceRpc.UpdateBlackBoxCaseMapDocument(l.Ctx, in)
	if err != nil {
		return nil, err
	}

	return &types.UpdateBlackBoxCaseMapDocumentResp{}, nil
}
