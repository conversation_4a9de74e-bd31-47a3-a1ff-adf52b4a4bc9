package blackboxcasemapdocument

import (
	"context"
	"encoding/json"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
)

type GetBlackBoxCaseMapDocumentLogic struct {
	*logic.BaseLogic
}

// get blackbox case map document
func NewGetBlackBoxCaseMapDocumentLogic(
	ctx context.Context, svcCtx *svc.ServiceContext,
) *GetBlackBoxCaseMapDocumentLogic {
	return &GetBlackBoxCaseMapDocumentLogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),
	}
}

func (l *GetBlackBoxCaseMapDocumentLogic) GetBlackBoxCaseMapDocument(req *types.GetBlackBoxCaseMapDocumentReq) (
	resp *types.GetBlackBoxCaseMapDocumentResp, err error,
) {
	in := &pb.GetBlackBoxCaseMapDocumentReq{}
	if err = utils.Copy(in, req, l.Converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy data[%+v] to request, error: %+v", req,
			err,
		)
	}

	out, err := l.SvcCtx.BlackBoxCaseMapDocumentServiceRpc.GetBlackBoxCaseMapDocument(l.Ctx, in)
	if err != nil || out.FuncId == "" {
		return nil, err
	}

	resp = &types.GetBlackBoxCaseMapDocumentResp{}
	if out.FuncDoc != "" {
		funcDoc := &types.BlackBoxCaseKnowledgeSupplementDoc{}
		if err = json.Unmarshal([]byte(out.FuncDoc), funcDoc); err != nil {
			return nil, errors.Wrapf(err, "failed to unmarshal out.FuncDoc from JSON")
		}
		resp.FuncDoc = funcDoc
	}
	if out.ExpeDoc != "" {
		expeDoc := &types.BlackBoxCaseKnowledgeSupplementDoc{}
		if err = json.Unmarshal([]byte(out.ExpeDoc), expeDoc); err != nil {
			return nil, errors.Wrapf(err, "failed to unmarshal out.ExpeDoc from JSON")
		}
		resp.ExpeDoc = expeDoc
	}

	return resp, nil
}
