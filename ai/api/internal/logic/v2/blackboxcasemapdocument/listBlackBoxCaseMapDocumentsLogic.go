package blackboxcasemapdocument

import (
	"context"
	"encoding/json"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/public/httpc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/types"
)

type ListBlackBoxCaseMapDocumentsLogic struct {
	*logic.BaseLogic
}

// list blackbox case map documents
func NewListBlackBoxCaseMapDocumentsLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ListBlackBoxCaseMapDocumentsLogic {
	return &ListBlackBoxCaseMapDocumentsLogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),
	}
}

func (l *ListBlackBoxCaseMapDocumentsLogic) ListBlackBoxCaseMapDocuments(req *types.ListBlackBoxCaseMapDocumentsReq) (resp any, err error) {
	caseMap, err := l.SvcCtx.BlackBoxCaseMapServiceRpc.GetCompleteBlackBoxCaseMap(l.Ctx, &pb.GetCompleteBlackBoxCaseMapReq{
		ProjectId: req.ProjectId,
		DirId:     req.DirId,
		CaseId:    req.CaseId,
	})
	if err != nil {
		return nil, err
	}

	var rdoc []httpc.ListBlackBoxCaseMapDocumentsRespItem
	if caseMap.RecallDocument != "" {
		if err = json.Unmarshal([]byte(caseMap.RecallDocument), &rdoc); err != nil {
			return nil, errors.Wrapf(err, "failed to unmarshal recallDocument from JSON")
		}
	} else {
		return nil, nil
	}

	return rdoc, nil
}
