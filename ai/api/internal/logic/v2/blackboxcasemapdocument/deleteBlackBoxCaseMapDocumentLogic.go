package blackboxcasemapdocument

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"
)

type DeleteBlackBoxCaseMapDocumentLogic struct {
	*logic.BaseLogic
}

// delete blackbox case map document
func NewDeleteBlackBoxCaseMapDocumentLogic(ctx context.Context, svcCtx *svc.ServiceContext) *DeleteBlackBoxCaseMapDocumentLogic {
	return &DeleteBlackBoxCaseMapDocumentLogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),
	}
}

func (l *DeleteBlackBoxCaseMapDocumentLogic) DeleteBlackBoxCaseMapDocument(req *types.DeleteBlackBoxCaseMapDocumentReq) (resp *types.DeleteBlackBoxCaseMapDocumentResp, err error) {
	in := &pb.DeleteBlackBoxCaseMapDocumentReq{}
	if err = utils.Copy(in, req, l.Converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy data[%+v] to request, error: %+v", req,
			err,
		)
	}

	_, err = l.SvcCtx.BlackBoxCaseMapDocumentServiceRpc.DeleteBlackBoxCaseMapDocument(l.Ctx, in)
	if err != nil {
		return nil, err
	}

	return &types.DeleteBlackBoxCaseMapDocumentResp{}, nil
}
