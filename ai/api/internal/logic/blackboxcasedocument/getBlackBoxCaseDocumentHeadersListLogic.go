package blackboxcasedocument

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/types"
)

type GetBlackBoxCaseDocumentHeadersListLogic struct {
	*logic.BaseLogic
}

// NewGetBlackBoxCaseDocumentHeadersListLogic get black box case document headers list
func NewGetBlackBoxCaseDocumentHeadersListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetBlackBoxCaseDocumentHeadersListLogic {
	return &GetBlackBoxCaseDocumentHeadersListLogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),
	}
}

func (l *GetBlackBoxCaseDocumentHeadersListLogic) GetBlackBoxCaseDocumentHeadersList(
	req *types.GetBlackBoxCaseDocumentHeadersListReq) (resp *types.GetBlackBoxCaseDocumentHeadersListResp, err error) {
	in := &pb.GetBlackBoxCaseDocumentHeadersListReq{}
	if err = utils.Copy(in, req, l.Converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy data[%+v] to request, error: %+v", req,
			err,
		)
	}
	out, err := l.SvcCtx.BlackBoxCaseDocumentServiceRpc.GetBlackBoxCaseDocumentHeadersList(l.Ctx, in)
	if err != nil {
		return nil, err
	}
	resp = &types.GetBlackBoxCaseDocumentHeadersListResp{}
	if err = utils.Copy(resp, out, l.Converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy data[%+v] to response, error: %+v",
			out, err,
		)
	}
	return resp, nil
}
