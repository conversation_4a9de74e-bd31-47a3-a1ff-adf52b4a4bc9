package blackboxcasedocument

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
)

type DeleteBlackBoxCaseDocumentForSessionLogic struct {
	*logic.BaseLogic
}

// delete black box case document for session
func NewDeleteBlackBoxCaseDocumentForSessionLogic(
	ctx context.Context, svcCtx *svc.ServiceContext,
) *DeleteBlackBoxCaseDocumentForSessionLogic {
	return &DeleteBlackBoxCaseDocumentForSessionLogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),
	}
}

func (l *DeleteBlackBoxCaseDocumentForSessionLogic) DeleteBlackBoxCaseDocumentForSession(req *types.DeleteBlackBoxCaseDocumentForSessionReq) (
	resp *types.DeleteBlackBoxCaseDocumentForSessionResp, err error,
) {
	in := &pb.DeleteBlackBoxCaseDocumentForSessionReq{}
	if err = utils.Copy(in, req, l.Converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy data[%+v] to request, error: %+v", req,
			err,
		)
	}
	_, err = l.SvcCtx.BlackBoxCaseDocumentServiceRpc.DeleteBlackBoxCaseDocumentForSession(l.Ctx, in)
	if err != nil {
		return nil, err
	}
	return &types.DeleteBlackBoxCaseDocumentForSessionResp{}, nil
}
