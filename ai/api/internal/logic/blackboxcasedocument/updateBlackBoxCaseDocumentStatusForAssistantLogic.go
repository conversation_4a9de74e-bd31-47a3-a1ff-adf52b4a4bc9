package blackboxcasedocument

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
)

type UpdateBlackBoxCaseDocumentStatusForAssistantLogic struct {
	*logic.BaseLogic
}

// update black box case document status for assistant
func NewUpdateBlackBoxCaseDocumentStatusForAssistantLogic(
	ctx context.Context, svcCtx *svc.ServiceContext,
) *UpdateBlackBoxCaseDocumentStatusForAssistantLogic {
	return &UpdateBlackBoxCaseDocumentStatusForAssistantLogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),
	}
}

func (l *UpdateBlackBoxCaseDocumentStatusForAssistantLogic) UpdateBlackBoxCaseDocumentStatusForAssistant(req *types.UpdateBlackBoxCaseDocumentStatusForAssistantReq) (
	resp *types.UpdateBlackBoxCaseDocumentStatusForAssistantResp, err error,
) {
	in := &pb.UpdateBlackBoxCaseDocumentStatusForAssistantReq{}
	if err = utils.Copy(in, req, l.Converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy data[%+v] to request, error: %+v", req,
			err,
		)
	}
	_, err = l.SvcCtx.BlackBoxCaseDocumentServiceRpc.UpdateBlackBoxCaseDocumentStatusForAssistant(l.Ctx, in)
	if err != nil {
		return nil, err
	}
	return &types.UpdateBlackBoxCaseDocumentStatusForAssistantResp{}, nil
}
