package blackboxcasedocument

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
)

type SearchBlackBoxCaseDocumentForSessionLogic struct {
	*logic.BaseLogic
}

// search black box case document for session
func NewSearchBlackBoxCaseDocumentForSessionLogic(
	ctx context.Context, svcCtx *svc.ServiceContext,
) *SearchBlackBoxCaseDocumentForSessionLogic {
	return &SearchBlackBoxCaseDocumentForSessionLogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),
	}
}

func (l *SearchBlackBoxCaseDocumentForSessionLogic) SearchBlackBoxCaseDocumentForSession(req *types.SearchBlackBoxCaseDocumentForSessionReq) (
	resp *types.SearchBlackBoxCaseDocumentForSessionResp, err error,
) {
	in := &pb.SearchBlackBoxCaseDocumentForSessionReq{}
	if err = utils.Copy(in, req, l.Converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy data[%+v] to request, error: %+v", req,
			err,
		)
	}
	out, err := l.SvcCtx.BlackBoxCaseDocumentServiceRpc.SearchBlackBoxCaseDocumentForSession(l.Ctx, in)
	if err != nil {
		return nil, err
	}
	resp = &types.SearchBlackBoxCaseDocumentForSessionResp{Items: []*types.BlackBoxCaseDocument{}}
	if err = utils.Copy(resp, out, l.Converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy data[%+v] to response, error: %+v",
			out, err,
		)
	}
	return resp, nil
}
