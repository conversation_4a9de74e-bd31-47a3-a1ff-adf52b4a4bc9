package blackboxcasedocument

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/types"
)

type ReloadBlackBoxCaseDocumentLogic struct {
	*logic.BaseLogic
}

// NewReloadBlackBoxCaseDocumentLogic reload black box case document
func NewReloadBlackBoxCaseDocumentLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ReloadBlackBoxCaseDocumentLogic {
	return &ReloadBlackBoxCaseDocumentLogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),
	}
}

func (l *ReloadBlackBoxCaseDocumentLogic) ReloadBlackBoxCaseDocument(
	req *types.ReloadBlackBoxCaseDocumentReq) (resp *types.ReloadBlackBoxCaseDocumentResp, err error) {
	in := &pb.ReloadBlackBoxCaseDocumentReq{}
	if err = utils.Copy(in, req, l.Converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy data[%+v] to request, error: %+v", req,
			err,
		)
	}
	_, err = l.SvcCtx.BlackBoxCaseDocumentServiceRpc.ReloadBlackBoxCaseDocument(l.Ctx, in)
	if err != nil {
		return nil, err
	}
	return &types.ReloadBlackBoxCaseDocumentResp{}, nil
}
