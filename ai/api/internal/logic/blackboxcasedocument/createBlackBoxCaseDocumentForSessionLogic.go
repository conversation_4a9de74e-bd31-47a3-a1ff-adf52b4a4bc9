package blackboxcasedocument

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
)

type CreateBlackBoxCaseDocumentForSessionLogic struct {
	*logic.BaseLogic
}

// create black box case document for session
func NewCreateBlackBoxCaseDocumentForSessionLogic(
	ctx context.Context, svcCtx *svc.ServiceContext,
) *CreateBlackBoxCaseDocumentForSessionLogic {
	return &CreateBlackBoxCaseDocumentForSessionLogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),
	}
}

func (l *CreateBlackBoxCaseDocumentForSessionLogic) CreateBlackBoxCaseDocumentForSession(req *types.CreateBlackBoxCaseDocumentForSessionReq) (
	resp *types.CreateBlackBoxCaseDocumentForSessionResp, err error,
) {
	in := &pb.CreateBlackBoxCaseDocumentForSessionReq{}
	if err = utils.Copy(in, req, l.Converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy data[%+v] to request, error: %+v", req,
			err,
		)
	}
	_, err = l.SvcCtx.BlackBoxCaseDocumentServiceRpc.CreateBlackBoxCaseDocumentForSession(l.Ctx, in)
	if err != nil {
		return nil, err
	}
	return &types.CreateBlackBoxCaseDocumentForSessionResp{}, nil
}
