package blackboxcasedocument

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
)

type GetBlackBoxCaseDocumentLogic struct {
	*logic.BaseLogic
}

// get black box case document
func NewGetBlackBoxCaseDocumentLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetBlackBoxCaseDocumentLogic {
	return &GetBlackBoxCaseDocumentLogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),
	}
}

func (l *GetBlackBoxCaseDocumentLogic) GetBlackBoxCaseDocument(req *types.GetBlackBoxCaseDocumentReq) (
	resp *types.GetBlackBoxCaseDocumentResp, err error,
) {
	in := &pb.GetBlackBoxCaseDocumentReq{}
	if err = utils.Copy(in, req, l.Converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy data[%+v] to request, error: %+v", req,
			err,
		)
	}
	out, err := l.SvcCtx.BlackBoxCaseDocumentServiceRpc.GetBlackBoxCaseDocument(l.Ctx, in)
	if err != nil {
		return nil, err
	}
	resp = &types.GetBlackBoxCaseDocumentResp{Item: &types.BlackBoxCaseDocument{}}
	if out.GetItem() != nil {
		if err = utils.Copy(resp.Item, out.GetItem(), l.Converters...); err != nil {
			return nil, errors.Wrapf(
				errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy data[%+v] to response, error: %+v",
				out, err,
			)
		}
	}
	return resp, nil
}
