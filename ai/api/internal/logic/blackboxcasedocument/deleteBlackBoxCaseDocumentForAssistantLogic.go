package blackboxcasedocument

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
)

type DeleteBlackBoxCaseDocumentForAssistantLogic struct {
	*logic.BaseLogic
}

// delete black box case document for assistant
func NewDeleteBlackBoxCaseDocumentForAssistantLogic(
	ctx context.Context, svcCtx *svc.ServiceContext,
) *DeleteBlackBoxCaseDocumentForAssistantLogic {
	return &DeleteBlackBoxCaseDocumentForAssistantLogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),
	}
}

func (l *DeleteBlackBoxCaseDocumentForAssistantLogic) DeleteBlackBoxCaseDocumentForAssistant(req *types.DeleteBlackBoxCaseDocumentForAssistantReq) (
	resp *types.DeleteBlackBoxCaseDocumentForAssistantResp, err error,
) {
	in := &pb.DeleteBlackBoxCaseDocumentForAssistantReq{}
	if err = utils.Copy(in, req, l.Converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy data[%+v] to request, error: %+v", req,
			err,
		)
	}
	_, err = l.SvcCtx.BlackBoxCaseDocumentServiceRpc.DeleteBlackBoxCaseDocumentForAssistant(l.Ctx, in)
	if err != nil {
		return nil, err
	}
	return &types.DeleteBlackBoxCaseDocumentForAssistantResp{}, nil
}
