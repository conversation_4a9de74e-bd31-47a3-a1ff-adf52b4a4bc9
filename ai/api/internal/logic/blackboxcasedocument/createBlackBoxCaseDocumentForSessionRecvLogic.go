package blackboxcasedocument

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
)

type CreateBlackBoxCaseDocumentForSessionRecvLogic struct {
	*logic.BaseLogic
}

// create black box case document for session recv 在文档表和会话文档表创建
func NewCreateBlackBoxCaseDocumentForSessionRecvLogic(
	ctx context.Context, svcCtx *svc.ServiceContext,
) *CreateBlackBoxCaseDocumentForSessionRecvLogic {
	return &CreateBlackBoxCaseDocumentForSessionRecvLogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),
	}
}

func (l *CreateBlackBoxCaseDocumentForSessionRecvLogic) CreateBlackBoxCaseDocumentForSessionRecv(req *types.CreateBlackBoxCaseDocumentForSessionRecvReq) (
	resp *types.CreateBlackBoxCaseDocumentForSessionRecvResp, err error,
) {
	in := &pb.CreateBlackBoxCaseDocumentForSessionRecvReq{}
	if err = utils.Copy(in, req, l.Converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy data[%+v] to request, error: %+v", req,
			err,
		)
	}
	_, err = l.SvcCtx.BlackBoxCaseDocumentServiceRpc.CreateBlackBoxCaseDocumentForSessionRecv(l.Ctx, in)
	if err != nil {
		return nil, err
	}
	return &types.CreateBlackBoxCaseDocumentForSessionRecvResp{}, nil
}
