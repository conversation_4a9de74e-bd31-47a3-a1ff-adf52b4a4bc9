package blackboxcasedocument

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
)

type SearchBlackBoxCaseDocumentForAssistantLogic struct {
	*logic.BaseLogic
}

// search black box case document for assistant
func NewSearchBlackBoxCaseDocumentForAssistantLogic(
	ctx context.Context, svcCtx *svc.ServiceContext,
) *SearchBlackBoxCaseDocumentForAssistantLogic {
	return &SearchBlackBoxCaseDocumentForAssistantLogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),
	}
}

func (l *SearchBlackBoxCaseDocumentForAssistantLogic) SearchBlackBoxCaseDocumentForAssistant(req *types.SearchBlackBoxCaseDocumentForAssistantReq) (
	resp *types.SearchBlackBoxCaseDocumentForAssistantResp, err error,
) {
	in := &pb.SearchBlackBoxCaseDocumentForAssistantReq{}
	if err = utils.Copy(in, req, l.Converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy data[%+v] to request, error: %+v", req,
			err,
		)
	}
	out, err := l.SvcCtx.BlackBoxCaseDocumentServiceRpc.SearchBlackBoxCaseDocumentForAssistant(l.Ctx, in)
	if err != nil {
		return nil, err
	}
	resp = &types.SearchBlackBoxCaseDocumentForAssistantResp{Items: []*types.BlackBoxCaseDocument{}}
	if err = utils.Copy(resp, out, l.Converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy data[%+v] to response, error: %+v",
			out, err,
		)
	}
	return resp, nil
}
