package blackboxcasedocument

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
)

type BatchDeleteBlackBoxCaseDocumentForAssistantLogic struct {
	*logic.BaseLogic
}

// batch delete black box case document for assistant
func NewBatchDeleteBlackBoxCaseDocumentForAssistantLogic(
	ctx context.Context, svcCtx *svc.ServiceContext,
) *BatchDeleteBlackBoxCaseDocumentForAssistantLogic {
	return &BatchDeleteBlackBoxCaseDocumentForAssistantLogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),
	}
}

func (l *BatchDeleteBlackBoxCaseDocumentForAssistantLogic) BatchDeleteBlackBoxCaseDocumentForAssistant(req *types.BatchDeleteBlackBoxCaseDocumentForAssistantReq) (
	resp *types.BatchDeleteBlackBoxCaseDocumentForAssistantResp, err error,
) {
	in := &pb.BatchDeleteBlackBoxCaseDocumentForAssistantReq{}
	if err = utils.Copy(in, req, l.Converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy data[%+v] to request, error: %+v", req,
			err,
		)
	}
	_, err = l.SvcCtx.BlackBoxCaseDocumentServiceRpc.BatchDeleteBlackBoxCaseDocumentForAssistant(l.Ctx, in)
	if err != nil {
		return nil, err
	}
	return &types.BatchDeleteBlackBoxCaseDocumentForAssistantResp{}, nil
}
