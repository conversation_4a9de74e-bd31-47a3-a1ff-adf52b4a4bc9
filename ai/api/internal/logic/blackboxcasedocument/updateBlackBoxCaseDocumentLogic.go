package blackboxcasedocument

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
)

type UpdateBlackBoxCaseDocumentLogic struct {
	*logic.BaseLogic
}

// update black box case document
func NewUpdateBlackBoxCaseDocumentLogic(
	ctx context.Context, svcCtx *svc.ServiceContext,
) *UpdateBlackBoxCaseDocumentLogic {
	return &UpdateBlackBoxCaseDocumentLogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),
	}
}

func (l *UpdateBlackBoxCaseDocumentLogic) UpdateBlackBoxCaseDocument(req *types.UpdateBlackBoxCaseDocumentReq) (
	resp *types.UpdateBlackBoxCaseDocumentResp, err error,
) {
	in := &pb.UpdateBlackBoxCaseDocumentReq{}
	if err = utils.Copy(in, req, l.Converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy data[%+v] to request, error: %+v", req,
			err,
		)
	}
	_, err = l.SvcCtx.BlackBoxCaseDocumentServiceRpc.UpdateBlackBoxCaseDocument(l.Ctx, in)
	if err != nil {
		return nil, err
	}
	return &types.UpdateBlackBoxCaseDocumentResp{}, nil
}
