package blackboxcasedocument

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
)

type CreateBlackBoxCaseDocumentForAssistantLogic struct {
	*logic.BaseLogic
}

// create black box case document for assistant
func NewCreateBlackBoxCaseDocumentForAssistantLogic(
	ctx context.Context, svcCtx *svc.ServiceContext,
) *CreateBlackBoxCaseDocumentForAssistantLogic {
	return &CreateBlackBoxCaseDocumentForAssistantLogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),
	}
}

func (l *CreateBlackBoxCaseDocumentForAssistantLogic) CreateBlackBoxCaseDocumentForAssistant(req *types.CreateBlackBoxCaseDocumentForAssistantReq) (
	resp *types.CreateBlackBoxCaseDocumentForAssistantResp, err error,
) {
	in := &pb.CreateBlackBoxCaseDocumentForAssistantReq{}
	if err = utils.Copy(in, req, l.Converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy data[%+v] to request, error: %+v", req,
			err,
		)
	}

	out, err := l.SvcCtx.BlackBoxCaseDocumentServiceRpc.CreateBlackBoxCaseDocumentForAssistant(l.Ctx, in)
	if err != nil {
		return nil, err
	}

	return &types.CreateBlackBoxCaseDocumentForAssistantResp{
		ProjectId:      req.ProjectId,
		AssistantId:    out.GetAssistantId(),
		DocumentIdList: out.GetDocumentIdList(),
	}, nil
}
