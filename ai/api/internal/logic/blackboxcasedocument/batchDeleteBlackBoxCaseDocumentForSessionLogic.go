package blackboxcasedocument

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
)

type BatchDeleteBlackBoxCaseDocumentForSessionLogic struct {
	*logic.BaseLogic
}

// batch delete black box case document for session
func NewBatchDeleteBlackBoxCaseDocumentForSessionLogic(
	ctx context.Context, svcCtx *svc.ServiceContext,
) *BatchDeleteBlackBoxCaseDocumentForSessionLogic {
	return &BatchDeleteBlackBoxCaseDocumentForSessionLogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),
	}
}

func (l *BatchDeleteBlackBoxCaseDocumentForSessionLogic) BatchDeleteBlackBoxCaseDocumentForSession(req *types.BatchDeleteBlackBoxCaseDocumentForSessionReq) (
	resp *types.BatchDeleteBlackBoxCaseDocumentForSessionResp, err error,
) {
	in := &pb.BatchDeleteBlackBoxCaseDocumentForSessionReq{}
	if err = utils.Copy(in, req, l.Converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy data[%+v] to request, error: %+v", req,
			err,
		)
	}
	_, err = l.SvcCtx.BlackBoxCaseDocumentServiceRpc.BatchDeleteBlackBoxCaseDocumentForSession(l.Ctx, in)
	if err != nil {
		return nil, err
	}
	return &types.BatchDeleteBlackBoxCaseDocumentForSessionResp{}, nil
}
