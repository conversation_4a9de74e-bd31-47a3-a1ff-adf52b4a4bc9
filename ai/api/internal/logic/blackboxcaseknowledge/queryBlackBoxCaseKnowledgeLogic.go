package blackboxcaseknowledge

import (
	"context"
	"strconv"
	"time"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sse"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/public/httpc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
)

type QueryBlackBoxCaseKnowledgeLogic struct {
	*logic.BaseLogic
}

func NewQueryBlackBoxCaseKnowledgeLogic(
	ctx context.Context, svcCtx *svc.ServiceContext,
) *QueryBlackBoxCaseKnowledgeLogic {
	return &QueryBlackBoxCaseKnowledgeLogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),
	}
}

func (l *QueryBlackBoxCaseKnowledgeLogic) QueryBlackBoxCaseKnowledge(req *types.QueryBlackBoxCaseKnowledgeReq) (
	resp *sse.Stream, err error,
) {
	stream := sse.NewStream(strconv.Itoa(int(time.Now().UnixMilli())), 0)
	stopChan := make(chan struct{})

	// 获取用例详情,生成用例需要传入ai属性
	in := &pb.GetBlackBoxCaseReq{CaseId: req.CaseId}
	caseDetail, err := l.SvcCtx.BlackBoxCaseServiceRpc.GetBlackBoxCase(l.Ctx, in)
	if err != nil {
		return stream, err
	}

	go func() {
		defer func() {
			l.SvcCtx.BlackBoxCaseServiceRpc.QueryIncBlackBoxCase(l.Ctx, &pb.QueryIncBlackBoxCaseReq{CaseId: req.CaseId})
			stream.Quit()
			close(stopChan)
		}()

		errChan := make(chan error)

		go func() {
			errChan <- httpc.QueryKnowledge(
				l.SvcCtx.ExternalAiDomain,
				&httpc.CreateTestCaseDataReq{
					KnowledgeId:               req.KnowledgeId,
					KnowledgeParagraphTitle:   req.KnowledgeParagraphTitleText,
					KnowledgeParagraphTitleId: req.KnowledgeParagraphTitleId,
					ModelCharacter:            caseDetail.Item.CaseModelCharacter,
					ContinueToWrite:           caseDetail.Item.CaseContinueToWrite,
				},
				stream,
			)
		}()

		select {
		case err = <-errChan:
			if err != nil {
				l.Error(err)
			}
		case <-stopChan:
		}
	}()

	return stream, err
}
