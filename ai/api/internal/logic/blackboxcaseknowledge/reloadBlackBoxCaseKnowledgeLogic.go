package blackboxcaseknowledge

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"
)

type ReloadBlackBoxCaseKnowledgeLogic struct {
	*logic.BaseLogic
}

func NewReloadBlackBoxCaseKnowledgeLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ReloadBlackBoxCaseKnowledgeLogic {
	return &ReloadBlackBoxCaseKnowledgeLogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),
	}
}

func (l *ReloadBlackBoxCaseKnowledgeLogic) ReloadBlackBoxCaseKnowledge(req *types.ReloadBlackBoxCaseKnowledgeReq) (resp *types.ReloadBlackBoxCaseKnowledgeResp, err error) {
	in := &pb.ReloadBlackBoxCaseKnowledgeReq{}
	if err = utils.Copy(in, req, l.Converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy data[%+v] to request, error: %+v", req,
			err,
		)
	}

	_, err = l.SvcCtx.BlackBoxCaseKnowledgeServiceRpc.ReloadBlackBoxCaseKnowledge(l.Ctx, in)
	if err != nil {
		return nil, err
	}

	return &types.ReloadBlackBoxCaseKnowledgeResp{}, nil
}
