package blackboxcaseknowledge

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"
)

type ListBlackBoxCaseKnowledgeLogic struct {
	*logic.BaseLogic
}

// list black box case
func NewListBlackBoxCaseKnowledgeLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ListBlackBoxCaseKnowledgeLogic {
	return &ListBlackBoxCaseKnowledgeLogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),
	}
}

func (l *ListBlackBoxCaseKnowledgeLogic) ListBlackBoxCaseKnowledge(req *types.ListBlackBoxCaseKnowledgeReq) (resp *types.ListBlackBoxCaseKnowledgeResp, err error) {
	in := &pb.ListBlackBoxCaseKnowledgeReq{}
	if err = utils.Copy(in, req, l.Converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy data[%+v] to request, error: %+v", req,
			err,
		)
	}

	out, err := l.SvcCtx.BlackBoxCaseKnowledgeServiceRpc.ListBlackBoxCaseKnowledge(l.Ctx, in)
	if err != nil {
		return nil, err
	}
	resp = &types.ListBlackBoxCaseKnowledgeResp{Items: []*types.BlackBoxCaseKnowledge{}}
	if err = utils.Copy(resp, out, l.Converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy data[%+v] to response, error: %+v",
			out, err,
		)
	}

	return resp, nil
}
