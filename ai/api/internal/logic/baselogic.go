package logic

import (
	"context"

	"github.com/zeromicro/go-zero/core/logx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"
	usercommon "gitlab.ttyuyin.com/TestDevelopment/qet-backend-middleware/user/common"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/svc"
)

type BaseLogic struct {
	logx.Logger
	Ctx    context.Context
	SvcCtx *svc.ServiceContext

	Converters []utils.TypeConverter
}

func NewBaseLogic(ctx context.Context, svcCtx *svc.ServiceContext) *BaseLogic {
	return &BaseLogic{
		Logger: logx.WithContext(ctx),
		Ctx:    ctx,
		SvcCtx: svcCtx,

		Converters: []utils.TypeConverter{
			usercommon.StringToUserInfo(ctx, svcCtx.UserRpc, nil),
		},
	}
}
