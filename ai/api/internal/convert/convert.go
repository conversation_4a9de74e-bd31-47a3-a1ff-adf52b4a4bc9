package convert

import (
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/public/httpc"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"
)

/**
v2-4版本, 出现了由ai服务直接透传请求到test-genius服务的过程, 其中会有结构转换的过程, 故在当前包中添加了自动转换的函数
*/

func Convert2ExperienceCategory(input types.BlackBoxKnowledgeExperienceCategory) (category *httpc.KnowledgeExperienceCategory) {
	return &httpc.KnowledgeExperienceCategory{
		Id:       int64(input.Id),
		TypeName: input.TypeName,
	}
}

func Convert2KnowledgeTag(input types.BlackBoxKnowledgeTag) (category *httpc.KnowledgeTag) {
	err := utils.Copy(category, input)
	if err != nil {
		return nil
	}
	return category
}

func Convert2KnowledgeTags(input []*types.BlackBoxKnowledgeTag) (category []*httpc.KnowledgeTag) {
	category = make([]*httpc.KnowledgeTag, 0, len(input))
	err := utils.Copy(&category, input)
	if err != nil {
		return nil
	}
	return category
}
