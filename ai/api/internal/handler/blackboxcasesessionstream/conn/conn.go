package conn

import (
	"bufio"
	"fmt"
	"net"
	"net/http"
)

type ResponseWriter struct {
	conn   net.Conn
	header http.Header
	buf    *bufio.ReadWriter
}

func NewConnResponseWriter(conn net.Conn, buf *bufio.ReadWriter) http.ResponseWriter {
	return &ResponseWriter{
		conn:   conn,
		header: make(http.Header),
		buf:    buf,
	}
}

func (w *ResponseWriter) Header() http.Header {
	return w.header
}

func (w *ResponseWriter) Write(data []byte) (int, error) {
	return w.buf.Write(data)
}

func (w *ResponseWriter) WriteHeader(statusCode int) {
	// Write status line
	statusText := http.StatusText(statusCode)
	statusLine := fmt.Sprintf("HTTP/1.1 %d %s\r\n", statusCode, statusText)
	_, _ = w.buf.Write([]byte(statusLine))

	// Write headers
	headers := w.<PERSON>er()
	// headers.Set("Content-Type", "text/plain") // Example content type
	for key, values := range headers {
		for _, value := range values {
			headerLine := fmt.Sprintf("%s: %s\r\n", key, value)
			_, _ = w.buf.Write([]byte(headerLine))
		}
	}

	// Write empty line to signal end of headers
	_, _ = w.buf.Write([]byte("\r\n"))
}

func (w *ResponseWriter) CloseNotify() <-chan bool {
	closed := make(chan bool, 1)
	closed <- false
	return closed
}

func (w *ResponseWriter) Flush() {
	err := w.buf.Flush()
	if err != nil {
		fmt.Printf("flush error: %s\n", err)
	}
}
