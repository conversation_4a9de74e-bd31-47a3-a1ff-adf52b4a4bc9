package blackboxcasesessionstream

import (
	"net/http"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/rest/httpx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/response"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/logic/blackboxcasesessionstream"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/types"
)

// SendStreamBlackBoxCaseForSessionHandler send stream black box case for session
func SendStreamBlackBoxCaseForSessionHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		/*h, ok := w.(http.Hijacker)
		if !ok {
			return
		}
		netConn, buf, err := h.Hijack()
		if err != nil {
			response.MakeHttpResponse(
				r, w, nil, errors.Wrapf(
					errorx.Err(errorx.MultipleError, err.Error()), "Hijack, error: %+v", err,
				),
			)
			return
		}
		defer netConn.Close()

		w = conn.NewConnResponseWriter(netConn, buf)
		err = netConn.SetDeadline(time.Time{})
		// err = errorx.Err(123, "asdasd")
		if err != nil {
			response.MakeHttpResponse(
				r, w, nil, errors.Wrapf(
					errorx.Err(errorx.MultipleError, err.Error()), "Hijack SetDeadline, error: %+v", err,
				),
			)
			return
		}*/
		var req types.SendBlackBoxCaseForSessionReq
		if err := httpx.Parse(r, &req); err != nil {
			response.MakeHttpResponse(
				r, w, nil, errors.Wrapf(
					errorx.Err(errorx.ParseParamError, err.Error()), "failed to parse parameters, error: %+v", err,
				),
			)
			return
		}

		if err := svcCtx.Validator.Validate.StructCtx(r.Context(), req); err != nil {
			response.MakeHttpResponse(
				r, w, nil, errors.Wrapf(
					errorx.Err(errorx.ValidateParamError, svcCtx.Validator.Translate(err)),
					"failed to validate parameters, error: %+v", err,
				),
			)
			return
		}

		l := blackboxcasesessionstream.NewSendStreamBlackBoxCaseForSessionLogic(r.Context(), svcCtx)
		stream, err := l.SendStreamBlackBoxCaseForSession(&req)
		response.MakeHttpResponse2EventStream(r, w, stream, err)
	}
}
