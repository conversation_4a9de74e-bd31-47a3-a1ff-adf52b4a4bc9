// Code generated by goctl. DO NOT EDIT.
// goctl 1.7.5

package handler

import (
	"net/http"

	blackboxcase "gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/handler/blackboxcase"
	blackboxcaseassistant "gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/handler/blackboxcaseassistant"
	blackboxcasedata "gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/handler/blackboxcasedata"
	blackboxcasedir "gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/handler/blackboxcasedir"
	blackboxcasedocument "gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/handler/blackboxcasedocument"
	blackboxcaseknowledge "gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/handler/blackboxcaseknowledge"
	blackboxcaserevision "gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/handler/blackboxcaserevision"
	blackboxcasesession "gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/handler/blackboxcasesession"
	blackboxcasesessionstream "gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/handler/blackboxcasesessionstream"
	blackboxcasetwbeta "gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/handler/blackboxcasetwbeta"
	v2blackboxcase "gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/handler/v2/blackboxcase"
	v2blackboxcasedata "gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/handler/v2/blackboxcasedata"
	v2blackboxcasedatadrag "gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/handler/v2/blackboxcasedatadrag"
	v2blackboxcasedataevacuation "gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/handler/v2/blackboxcasedataevacuation"
	v2blackboxcasedatamap "gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/handler/v2/blackboxcasedatamap"
	v2blackboxcasedataprogress "gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/handler/v2/blackboxcasedataprogress"
	v2blackboxcasedatareservation "gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/handler/v2/blackboxcasedatareservation"
	v2blackboxcasedatarevision "gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/handler/v2/blackboxcasedatarevision"
	v2blackboxcasedatasort "gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/handler/v2/blackboxcasedatasort"
	v2blackboxcasedirectory "gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/handler/v2/blackboxcasedirectory"
	v2blackboxcasedocument "gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/handler/v2/blackboxcasedocument"
	v2blackboxcasedocumentsummary "gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/handler/v2/blackboxcasedocumentsummary"
	v2blackboxcasedocumenttitle "gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/handler/v2/blackboxcasedocumenttitle"
	v2blackboxcaseknowledge "gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/handler/v2/blackboxcaseknowledge"
	v2blackboxcasemap "gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/handler/v2/blackboxcasemap"
	v2blackboxcasemapdocument "gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/handler/v2/blackboxcasemapdocument"
	v2blackboxcasemapexperience "gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/handler/v2/blackboxcasemapexperience"
	v2blackboxcasemapfunction "gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/handler/v2/blackboxcasemapfunction"
	v2blackboxcasemapknowledge "gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/handler/v2/blackboxcasemapknowledge"
	v2blackboxcasemapprogress "gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/handler/v2/blackboxcasemapprogress"
	v2blackboxknowledgeexperience "gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/handler/v2/blackboxknowledgeexperience"
	v2blackboxknowledgeexperiencecategory "gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/handler/v2/blackboxknowledgeexperiencecategory"
	v2blackboxknowledgeproject "gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/handler/v2/blackboxknowledgeproject"
	v2blackboxknowledgetag "gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/handler/v2/blackboxknowledgetag"
	v2blackboxknowledgeterm "gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/handler/v2/blackboxknowledgeterm"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/svc"

	"github.com/zeromicro/go-zero/rest"
)

func RegisterHandlers(server *rest.Server, serverCtx *svc.ServiceContext) {
	server.AddRoutes(
		[]rest.Route{
			{
				Method:  http.MethodGet,
				Path:    "/black/box/case",
				Handler: blackboxcase.GetBlackBoxCaseHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/black/box/case",
				Handler: blackboxcase.CreateBlackBoxCaseHandler(serverCtx),
			},
			{
				Method:  http.MethodDelete,
				Path:    "/black/box/case",
				Handler: blackboxcase.DeleteBlackBoxCaseHandler(serverCtx),
			},
			{
				Method:  http.MethodPut,
				Path:    "/black/box/case",
				Handler: blackboxcase.UpdateBlackBoxCaseHandler(serverCtx),
			},
			{
				Method:  http.MethodPut,
				Path:    "/black/box/case/ai",
				Handler: blackboxcase.UpdateBlackBoxCaseAIHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/black/box/case/data/merge",
				Handler: blackboxcase.MergeBlackBoxCaseDataHandler(serverCtx),
			},
			{
				// list black box case
				Method:  http.MethodPost,
				Path:    "/black/box/case/list",
				Handler: blackboxcase.ListBlackBoxCaseHandler(serverCtx),
			},
		},
		rest.WithPrefix("/ai/v1"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				// create black box case assistant
				Method:  http.MethodPost,
				Path:    "/black/box/case/assistant/create",
				Handler: blackboxcaseassistant.CreateBlackBoxCaseAssistantHandler(serverCtx),
			},
			{
				// delete black box case assistant
				Method:  http.MethodDelete,
				Path:    "/black/box/case/assistant/delete",
				Handler: blackboxcaseassistant.DeleteBlackBoxCaseAssistantHandler(serverCtx),
			},
			{
				// get black box case assistant
				Method:  http.MethodGet,
				Path:    "/black/box/case/assistant/get",
				Handler: blackboxcaseassistant.GetBlackBoxCaseAssistantHandler(serverCtx),
			},
			{
				// search black box case assistant
				Method:  http.MethodPost,
				Path:    "/black/box/case/assistant/search",
				Handler: blackboxcaseassistant.SearchBlackBoxCaseAssistantHandler(serverCtx),
			},
			{
				// update black box case assistant
				Method:  http.MethodPut,
				Path:    "/black/box/case/assistant/update",
				Handler: blackboxcaseassistant.UpdateBlackBoxCaseAssistantHandler(serverCtx),
			},
		},
		rest.WithPrefix("/ai/v1"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				Method:  http.MethodPost,
				Path:    "/black/box/case/data",
				Handler: blackboxcasedata.CreateBlackBoxCaseDataHandler(serverCtx),
			},
			{
				Method:  http.MethodDelete,
				Path:    "/black/box/case/data",
				Handler: blackboxcasedata.DeleteBlackBoxCaseDataHandler(serverCtx),
			},
			{
				Method:  http.MethodPut,
				Path:    "/black/box/case/data",
				Handler: blackboxcasedata.UpdateBlackBoxCaseDataHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/black/box/case/data/activities-mind/transfer",
				Handler: blackboxcasedata.TransActivitiesMindBlackBoxCaseHandler(serverCtx),
			},
			{
				Method:  http.MethodGet,
				Path:    "/black/box/case/data/ai-state",
				Handler: blackboxcasedata.GetBlackBoxCaseAIStateHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/black/box/case/data/append",
				Handler: blackboxcasedata.AppendBlackBoxCaseDataHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/black/box/case/data/batch",
				Handler: blackboxcasedata.BatchUpdateBlackBoxCaseDataHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/black/box/case/data/edit",
				Handler: blackboxcasedata.EditBlackBoxCaseDataHandler(serverCtx),
			},
			{
				Method:  http.MethodGet,
				Path:    "/black/box/case/data/edit/progress",
				Handler: blackboxcasedata.GetEditBlackBoxCaseDataProgressHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/black/box/case/data/generate",
				Handler: blackboxcasedata.GenerateBlackBoxCaseDataHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/black/box/case/data/keep",
				Handler: blackboxcasedata.KeepBlackBoxCaseDataHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/black/box/case/data/keep/clear",
				Handler: blackboxcasedata.ClearKeepBlackBoxCaseDataHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/black/box/case/data/list",
				Handler: blackboxcasedata.ListBlackBoxCaseDataHandler(serverCtx),
			},
			{
				Method:  http.MethodGet,
				Path:    "/black/box/case/data/metrics/migreation",
				Handler: blackboxcasedata.MetricsMigreationHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/black/box/case/data/mind/transfer",
				Handler: blackboxcasedata.TransMindBlackBoxCaseHandler(serverCtx),
			},
			{
				Method:  http.MethodPut,
				Path:    "/black/box/case/data/order",
				Handler: blackboxcasedata.UpdateBlackBoxCaseDataOrderHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/black/box/case/data/partial-edit",
				Handler: blackboxcasedata.PartialEditBlackBoxCaseDataHandler(serverCtx),
			},
			{
				Method:  http.MethodGet,
				Path:    "/black/box/case/data/ref",
				Handler: blackboxcasedata.GetBlackBoxCaseRefJsonHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/black/box/case/data/reorder",
				Handler: blackboxcasedata.ReorderBlackBoxCaseDataHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/black/box/case/data/replace",
				Handler: blackboxcasedata.ReplaceBlackBoxCaseDataHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/black/box/case/data/review-mind/transfer",
				Handler: blackboxcasedata.TransReviewMindBlackBoxCaseHandler(serverCtx),
			},
		},
		rest.WithPrefix("/ai/v1"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				Method:  http.MethodGet,
				Path:    "/black/box/case/dir",
				Handler: blackboxcasedir.GetBlackBoxCaseDirHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/black/box/case/dir",
				Handler: blackboxcasedir.CreateBlackBoxCaseDirHandler(serverCtx),
			},
			{
				Method:  http.MethodDelete,
				Path:    "/black/box/case/dir",
				Handler: blackboxcasedir.DeleteBlackBoxCaseDirHandler(serverCtx),
			},
			{
				Method:  http.MethodPut,
				Path:    "/black/box/case/dir",
				Handler: blackboxcasedir.UpdateBlackBoxCaseDirHandler(serverCtx),
			},
			{
				// list black box dirs
				Method:  http.MethodPost,
				Path:    "/black/box/case/dir/list",
				Handler: blackboxcasedir.ListBlackBoxCaseDirsHandler(serverCtx),
			},
		},
		rest.WithPrefix("/ai/v1"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				// batch delete black box case document for assistant
				Method:  http.MethodDelete,
				Path:    "/black/box/case/document/for_assistant/batch/delete",
				Handler: blackboxcasedocument.BatchDeleteBlackBoxCaseDocumentForAssistantHandler(serverCtx),
			},
			{
				// create black box case document for assistant
				Method:  http.MethodPost,
				Path:    "/black/box/case/document/for_assistant/create",
				Handler: blackboxcasedocument.CreateBlackBoxCaseDocumentForAssistantHandler(serverCtx),
			},
			{
				// delete black box case document for assistant
				Method:  http.MethodDelete,
				Path:    "/black/box/case/document/for_assistant/delete",
				Handler: blackboxcasedocument.DeleteBlackBoxCaseDocumentForAssistantHandler(serverCtx),
			},
			{
				// search black box case document for assistant
				Method:  http.MethodPost,
				Path:    "/black/box/case/document/for_assistant/search",
				Handler: blackboxcasedocument.SearchBlackBoxCaseDocumentForAssistantHandler(serverCtx),
			},
			{
				// batch delete black box case document for session
				Method:  http.MethodDelete,
				Path:    "/black/box/case/document/for_session/batch/delete",
				Handler: blackboxcasedocument.BatchDeleteBlackBoxCaseDocumentForSessionHandler(serverCtx),
			},
			{
				// create black box case document for session
				Method:  http.MethodPost,
				Path:    "/black/box/case/document/for_session/create",
				Handler: blackboxcasedocument.CreateBlackBoxCaseDocumentForSessionHandler(serverCtx),
			},
			{
				// delete black box case document for session
				Method:  http.MethodDelete,
				Path:    "/black/box/case/document/for_session/delete",
				Handler: blackboxcasedocument.DeleteBlackBoxCaseDocumentForSessionHandler(serverCtx),
			},
			{
				// search black box case document for session
				Method:  http.MethodPost,
				Path:    "/black/box/case/document/for_session/search",
				Handler: blackboxcasedocument.SearchBlackBoxCaseDocumentForSessionHandler(serverCtx),
			},
			{
				// create black box case document for session recv 在文档表和会话文档表创建
				Method:  http.MethodPost,
				Path:    "/black/box/case/document/for_session_recv/create",
				Handler: blackboxcasedocument.CreateBlackBoxCaseDocumentForSessionRecvHandler(serverCtx),
			},
			{
				// get black box case document
				Method:  http.MethodGet,
				Path:    "/black/box/case/document/get",
				Handler: blackboxcasedocument.GetBlackBoxCaseDocumentHandler(serverCtx),
			},
			{
				// get black box case document headers list
				Method:  http.MethodGet,
				Path:    "/black/box/case/document/headers/list",
				Handler: blackboxcasedocument.GetBlackBoxCaseDocumentHeadersListHandler(serverCtx),
			},
			{
				// reload black box case document
				Method:  http.MethodPut,
				Path:    "/black/box/case/document/reload",
				Handler: blackboxcasedocument.ReloadBlackBoxCaseDocumentHandler(serverCtx),
			},
			{
				// update black box case document
				Method:  http.MethodPut,
				Path:    "/black/box/case/document/update",
				Handler: blackboxcasedocument.UpdateBlackBoxCaseDocumentHandler(serverCtx),
			},
			{
				// update black box case document status for assistant
				Method:  http.MethodPut,
				Path:    "/black/box/case/document_status/for_assistant/update",
				Handler: blackboxcasedocument.UpdateBlackBoxCaseDocumentStatusForAssistantHandler(serverCtx),
			},
		},
		rest.WithPrefix("/ai/v1"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				Method:  http.MethodGet,
				Path:    "/black/box/case/knowledge",
				Handler: blackboxcaseknowledge.GetBlackBoxCaseKnowledgeHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/black/box/case/knowledge",
				Handler: blackboxcaseknowledge.CreateBlackBoxCaseKnowledgeHandler(serverCtx),
			},
			{
				Method:  http.MethodDelete,
				Path:    "/black/box/case/knowledge",
				Handler: blackboxcaseknowledge.DeleteBlackBoxCaseKnowledgeHandler(serverCtx),
			},
			{
				Method:  http.MethodPut,
				Path:    "/black/box/case/knowledge",
				Handler: blackboxcaseknowledge.UpdateBlackBoxCaseKnowledgeHandler(serverCtx),
			},
			{
				// list black box case
				Method:  http.MethodPost,
				Path:    "/black/box/case/knowledge/list",
				Handler: blackboxcaseknowledge.ListBlackBoxCaseKnowledgeHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/black/box/case/knowledge/query",
				Handler: blackboxcaseknowledge.QueryBlackBoxCaseKnowledgeHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/black/box/case/knowledge/reload",
				Handler: blackboxcaseknowledge.ReloadBlackBoxCaseKnowledgeHandler(serverCtx),
			},
			{
				Method:  http.MethodGet,
				Path:    "/black/box/case/knowledge/title",
				Handler: blackboxcaseknowledge.GetBlackBoxCaseKnowledgeTitleHandler(serverCtx),
			},
		},
		rest.WithPrefix("/ai/v1"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				Method:  http.MethodPost,
				Path:    "/black/box/case/revision/data",
				Handler: blackboxcaserevision.CreateBlackBoxCaseRevisionDataHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/black/box/case/revision/data/adopt",
				Handler: blackboxcaserevision.AdoptBlackBoxCaseRevisionDataHandler(serverCtx),
			},
			{
				// list black box case revision
				Method:  http.MethodPost,
				Path:    "/black/box/case/revision/list",
				Handler: blackboxcaserevision.ListBlackBoxCaseRevisionHandler(serverCtx),
			},
		},
		rest.WithPrefix("/ai/v1"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				// get black box case ai message
				Method:  http.MethodGet,
				Path:    "/black/box/case/ai/message/get",
				Handler: blackboxcasesession.GetCreateBlackBoxCaseAIMessageHandler(serverCtx),
			},
			{
				// get black box case conversation ai message
				Method:  http.MethodGet,
				Path:    "/black/box/case/conversation/ai/message/get",
				Handler: blackboxcasesession.GetBlackBoxCaseConversationAIMessageHandler(serverCtx),
			},
			{
				// get black box case conversation ai state
				Method:  http.MethodGet,
				Path:    "/black/box/case/conversation/ai/state/get",
				Handler: blackboxcasesession.GetBlackBoxCaseConversationAIStateHandler(serverCtx),
			},
			{
				// send black box case for session
				Method:  http.MethodPost,
				Path:    "/black/box/case/for_session/send",
				Handler: blackboxcasesession.SendBlackBoxCaseForSessionHandler(serverCtx),
			},
			{
				// merge black box case
				Method:  http.MethodPost,
				Path:    "/black/box/case/merge",
				Handler: blackboxcasesession.MergeBlackBoxCaseHandler(serverCtx),
			},
			{
				// create black box case modify reference content
				Method:  http.MethodPost,
				Path:    "/black/box/case/modify/reference/content/create",
				Handler: blackboxcasesession.CreateBlackBoxCaseModifyTestCaseReferenceContentHandler(serverCtx),
			},
			{
				// get black box case modify reference content list
				Method:  http.MethodGet,
				Path:    "/black/box/case/modify/reference/content/list/get",
				Handler: blackboxcasesession.GetBlackBoxCaseModifyTestCaseReferenceContentListHandler(serverCtx),
			},
			{
				// replace black box case
				Method:  http.MethodPost,
				Path:    "/black/box/case/replace",
				Handler: blackboxcasesession.ReplaceBlackBoxCaseHandler(serverCtx),
			},
			{
				// create black box case session
				Method:  http.MethodPost,
				Path:    "/black/box/case/session/create",
				Handler: blackboxcasesession.CreateBlackBoxCaseSessionHandler(serverCtx),
			},
			{
				// delete black box case session
				Method:  http.MethodDelete,
				Path:    "/black/box/case/session/delete",
				Handler: blackboxcasesession.DeleteBlackBoxCaseSessionHandler(serverCtx),
			},
			{
				// get black box case session
				Method:  http.MethodGet,
				Path:    "/black/box/case/session/get",
				Handler: blackboxcasesession.GetBlackBoxCaseSessionHandler(serverCtx),
			},
			{
				// get black box case session history message list
				Method:  http.MethodGet,
				Path:    "/black/box/case/session/history_message_list/get",
				Handler: blackboxcasesession.GetBlackBoxCaseSessionHistoryMessageListHandler(serverCtx),
			},
			{
				// remove black box case session history message list
				Method:  http.MethodDelete,
				Path:    "/black/box/case/session/history_message_list/remove",
				Handler: blackboxcasesession.RemoveBlackBoxCaseSessionHistoryMessageListHandler(serverCtx),
			},
			{
				// search black box case session
				Method:  http.MethodPost,
				Path:    "/black/box/case/session/search",
				Handler: blackboxcasesession.SearchBlackBoxCaseSessionHandler(serverCtx),
			},
			{
				// update black box case session
				Method:  http.MethodPut,
				Path:    "/black/box/case/session/update",
				Handler: blackboxcasesession.UpdateBlackBoxCaseSessionHandler(serverCtx),
			},
			{
				// transfer black box case
				Method:  http.MethodPost,
				Path:    "/black/box/case/transfer",
				Handler: blackboxcasesession.TransferBlackBoxCaseHandler(serverCtx),
			},
		},
		rest.WithPrefix("/ai/v1"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				// recreate black box case
				Method:  http.MethodPost,
				Path:    "/stream/black/box/case/for_session/recreate",
				Handler: blackboxcasesessionstream.ReCreateBlackBoxCaseHandler(serverCtx),
			},
			{
				// send stream black box case for session
				Method:  http.MethodPost,
				Path:    "/stream/black/box/case/for_session/send",
				Handler: blackboxcasesessionstream.SendStreamBlackBoxCaseForSessionHandler(serverCtx),
			},
			{
				// update stream black box case for session
				Method:  http.MethodPost,
				Path:    "/stream/black/box/case/for_session/update",
				Handler: blackboxcasesessionstream.UpdateBlackBoxCaseReqHandler(serverCtx),
			},
		},
		rest.WithPrefix("/ai/v1"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				// delete black box case of tw beta
				Method:  http.MethodPost,
				Path:    "/black/box/case/beta-v1",
				Handler: blackboxcasetwbeta.DeleteBlackBoxCaseTwBetaHandler(serverCtx),
			},
			{
				// get black box tw case content
				Method:  http.MethodPost,
				Path:    "/black/box/case/beta-v1/case/content",
				Handler: blackboxcasetwbeta.GetBlackBoxCaseTwBetaCaseContentHandler(serverCtx),
			},
			{
				// list black box case of tw beta mind check
				Method:  http.MethodPost,
				Path:    "/black/box/case/beta-v1/check/list",
				Handler: blackboxcasetwbeta.ListBlackBoxCaseTwBetaMindCheckHandler(serverCtx),
			},
			{
				// create black box tw test case
				Method:  http.MethodPost,
				Path:    "/black/box/case/beta-v1/create",
				Handler: blackboxcasetwbeta.CreateBlackBoxCaseTwBetaHandler(serverCtx),
			},
			{
				// list black box case of tw beta
				Method:  http.MethodPost,
				Path:    "/black/box/case/beta-v1/list",
				Handler: blackboxcasetwbeta.ListBlackBoxCaseTwBetaHandler(serverCtx),
			},
			{
				// create black box case of tw beta mind
				Method:  http.MethodPost,
				Path:    "/black/box/case/beta-v1/mind",
				Handler: blackboxcasetwbeta.CreateBlackBoxCaseTwBetaMindHandler(serverCtx),
			},
			{
				// get black box case of tw beta mind content
				Method:  http.MethodPost,
				Path:    "/black/box/case/beta-v1/mind/content",
				Handler: blackboxcasetwbeta.GetBlackBoxCaseTwBetaMindContentHandler(serverCtx),
			},
			{
				// update black box tw mind content
				Method:  http.MethodPost,
				Path:    "/black/box/case/beta-v1/mind/content/update",
				Handler: blackboxcasetwbeta.UpdateBlackBoxCaseTwBetaMindContentHandler(serverCtx),
			},
			{
				// list black box case of tw beta mind
				Method:  http.MethodPost,
				Path:    "/black/box/case/beta-v1/mind/list",
				Handler: blackboxcasetwbeta.ListBlackBoxCaseTwBetaMindHandler(serverCtx),
			},
		},
		rest.WithPrefix("/ai/v1"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				// create blackbox case
				Method:  http.MethodPost,
				Path:    "/black/box/case",
				Handler: v2blackboxcase.CreateBlackBoxCaseHandler(serverCtx),
			},
			{
				// get blackbox case
				Method:  http.MethodGet,
				Path:    "/black/box/case",
				Handler: v2blackboxcase.GetBlackBoxCaseHandler(serverCtx),
			},
			{
				// update blackbox case
				Method:  http.MethodPut,
				Path:    "/black/box/case",
				Handler: v2blackboxcase.UpdateBlackBoxCaseHandler(serverCtx),
			},
			{
				// delete blackbox case
				Method:  http.MethodDelete,
				Path:    "/black/box/case",
				Handler: v2blackboxcase.DeleteBlackBoxCaseHandler(serverCtx),
			},
			{
				// list blackbox cases
				Method:  http.MethodPost,
				Path:    "/black/box/cases",
				Handler: v2blackboxcase.ListBlackBoxCaseHandler(serverCtx),
			},
		},
		rest.WithPrefix("/ai/v2"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				// list blackbox case data
				Method:  http.MethodGet,
				Path:    "/black/box/case/data",
				Handler: v2blackboxcasedata.ListBlackBoxCaseDataHandler(serverCtx),
			},
			{
				// create blackbox case data
				Method:  http.MethodPost,
				Path:    "/black/box/case/data",
				Handler: v2blackboxcasedata.CreateBlackBoxCaseDataHandler(serverCtx),
			},
			{
				// update blackbox case data
				Method:  http.MethodPut,
				Path:    "/black/box/case/data",
				Handler: v2blackboxcasedata.UpdateBlackBoxCaseDataHandler(serverCtx),
			},
			{
				// delete blackbox case data
				Method:  http.MethodDelete,
				Path:    "/black/box/case/data",
				Handler: v2blackboxcasedata.DeleteBlackBoxCaseDataHandler(serverCtx),
			},
			{
				// get blackbox case data coverage
				Method:  http.MethodGet,
				Path:    "/black/box/case/data/coverage/get",
				Handler: v2blackboxcasedata.GetBlackBoxCaseDataCoverageHandler(serverCtx),
			},
			{
				// set blackbox case data coverage
				Method:  http.MethodPost,
				Path:    "/black/box/case/data/coverage/set",
				Handler: v2blackboxcasedata.ConfirmBlackBoxCaseDataCoverageHandler(serverCtx),
			},
			{
				// create blackbox case data with sse
				Method:  http.MethodPost,
				Path:    "/black/box/case/data/create",
				Handler: v2blackboxcasedata.CreateBlackBoxCaseDataWithSseHandler(serverCtx),
			},
			{
				// modify blackbox case data with sse
				Method:  http.MethodPost,
				Path:    "/black/box/case/data/modify",
				Handler: v2blackboxcasedata.ModifyBlackBoxCaseDataWithSseHandler(serverCtx),
			},
		},
		rest.WithPrefix("/ai/v2"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				// update blackbox case data drag
				Method:  http.MethodPut,
				Path:    "/black/box/case/data/drag",
				Handler: v2blackboxcasedatadrag.UpdateBlackBoxCaseDataDragHandler(serverCtx),
			},
		},
		rest.WithPrefix("/ai/v2"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				// delete blackbox case data evacuation
				Method:  http.MethodDelete,
				Path:    "/black/box/case/data/evacuation",
				Handler: v2blackboxcasedataevacuation.DeleteBlackBoxCaseDataEvacuationHandler(serverCtx),
			},
		},
		rest.WithPrefix("/ai/v2"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				// get blackbox case data map
				Method:  http.MethodGet,
				Path:    "/black/box/case/data/map",
				Handler: v2blackboxcasedatamap.GetBlackBoxCaseDataMapHandler(serverCtx),
			},
		},
		rest.WithPrefix("/ai/v2"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				// get blackbox case data progress
				Method:  http.MethodGet,
				Path:    "/black/box/case/data/progress",
				Handler: v2blackboxcasedataprogress.GetBlackBoxCaseDataProgressHandler(serverCtx),
			},
		},
		rest.WithPrefix("/ai/v2"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				// update blackbox case data reservation
				Method:  http.MethodPut,
				Path:    "/black/box/case/data/reservation",
				Handler: v2blackboxcasedatareservation.UpdateBlackBoxCaseDataReservationHandler(serverCtx),
			},
		},
		rest.WithPrefix("/ai/v2"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				// create blackbox case data revision
				Method:  http.MethodPost,
				Path:    "/black/box/case/data/revision",
				Handler: v2blackboxcasedatarevision.CreateBlackBoxCaseDataRevisionHandler(serverCtx),
			},
			{
				// get blackbox case data revision
				Method:  http.MethodGet,
				Path:    "/black/box/case/data/revision",
				Handler: v2blackboxcasedatarevision.GetBlackBoxCaseDataRevisionHandler(serverCtx),
			},
			{
				// update blackbox case data revision
				Method:  http.MethodPut,
				Path:    "/black/box/case/data/revision",
				Handler: v2blackboxcasedatarevision.UpdateBlackBoxCaseDataRevisionHandler(serverCtx),
			},
			{
				// list blackbox case data revisions
				Method:  http.MethodPost,
				Path:    "/black/box/case/data/revisions",
				Handler: v2blackboxcasedatarevision.ListBlackBoxCaseDataRevisionsHandler(serverCtx),
			},
		},
		rest.WithPrefix("/ai/v2"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				// create blackbox case data sort
				Method:  http.MethodPost,
				Path:    "/black/box/case/data/sort",
				Handler: v2blackboxcasedatasort.CreateBlackBoxCaseDataSortHandler(serverCtx),
			},
		},
		rest.WithPrefix("/ai/v2"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				// list blackbox case directories
				Method:  http.MethodPost,
				Path:    "/black/box/case/directories",
				Handler: v2blackboxcasedirectory.ListBlackBoxCaseDirectoriesHandler(serverCtx),
			},
			{
				// create blackbox case directory
				Method:  http.MethodPost,
				Path:    "/black/box/case/directory",
				Handler: v2blackboxcasedirectory.CreateBlackBoxCaseDirectoryHandler(serverCtx),
			},
			{
				// delete blackbox case directory
				Method:  http.MethodDelete,
				Path:    "/black/box/case/directory",
				Handler: v2blackboxcasedirectory.DeleteBlackBoxCaseDirectoryHandler(serverCtx),
			},
		},
		rest.WithPrefix("/ai/v2"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				// create blackbox case document
				Method:  http.MethodPost,
				Path:    "/black/box/case/document",
				Handler: v2blackboxcasedocument.CreateBlackBoxCaseDocumentHandler(serverCtx),
			},
			{
				// get blackbox case document
				Method:  http.MethodGet,
				Path:    "/black/box/case/document",
				Handler: v2blackboxcasedocument.GetBlackBoxCaseDocumentHandler(serverCtx),
			},
			{
				// update blackbox case document
				Method:  http.MethodPut,
				Path:    "/black/box/case/document",
				Handler: v2blackboxcasedocument.UpdateBlackBoxCaseDocumentHandler(serverCtx),
			},
			{
				// delete blackbox case document
				Method:  http.MethodDelete,
				Path:    "/black/box/case/document",
				Handler: v2blackboxcasedocument.DeleteBlackBoxCaseDocumentHandler(serverCtx),
			},
			{
				// reload blackbox case document
				Method:  http.MethodPost,
				Path:    "/black/box/case/document/reload",
				Handler: v2blackboxcasedocument.ReloadBlackBoxCaseDocumentHandler(serverCtx),
			},
			{
				// list blackbox case documents
				Method:  http.MethodPost,
				Path:    "/black/box/case/documents",
				Handler: v2blackboxcasedocument.ListBlackBoxCaseDocumentsHandler(serverCtx),
			},
		},
		rest.WithPrefix("/ai/v2"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				// get blackbox case document summary
				Method:  http.MethodPost,
				Path:    "/black/box/case/document/summary",
				Handler: v2blackboxcasedocumentsummary.GetBlackBoxCaseDocumentSummaryHandler(serverCtx),
			},
		},
		rest.WithPrefix("/ai/v2"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				// list blackbox case document titles
				Method:  http.MethodGet,
				Path:    "/black/box/case/document/titles",
				Handler: v2blackboxcasedocumenttitle.ListBlackBoxCaseDocumentTitlesHandler(serverCtx),
			},
		},
		rest.WithPrefix("/ai/v2"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				// get blackbox case knowledge
				Method:  http.MethodGet,
				Path:    "/black/box/case/knowledge",
				Handler: v2blackboxcaseknowledge.GetBlackBoxCaseKnowledgeHandler(serverCtx),
			},
			{
				// update blackbox case knowledge
				Method:  http.MethodPut,
				Path:    "/black/box/case/knowledge",
				Handler: v2blackboxcaseknowledge.UpdateBlackBoxCaseKnowledgeHandler(serverCtx),
			},
		},
		rest.WithPrefix("/ai/v2"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				// create blackbox case map
				Method:  http.MethodPost,
				Path:    "/black/box/case/map",
				Handler: v2blackboxcasemap.CreateBlackBoxCaseMapHandler(serverCtx),
			},
			{
				// get blackbox case map
				Method:  http.MethodGet,
				Path:    "/black/box/case/map",
				Handler: v2blackboxcasemap.GetBlackBoxCaseMapHandler(serverCtx),
			},
			{
				// update blackbox case map
				Method:  http.MethodPut,
				Path:    "/black/box/case/map",
				Handler: v2blackboxcasemap.UpdateBlackBoxCaseMapHandler(serverCtx),
			},
			{
				// create blackbox case map with sse
				Method:  http.MethodPost,
				Path:    "/black/box/case/map/create",
				Handler: v2blackboxcasemap.CreateBlackBoxCaseMapWithSseHandler(serverCtx),
			},
		},
		rest.WithPrefix("/ai/v2"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				// get blackbox case map document
				Method:  http.MethodGet,
				Path:    "/black/box/case/map/document",
				Handler: v2blackboxcasemapdocument.GetBlackBoxCaseMapDocumentHandler(serverCtx),
			},
			{
				// update blackbox case map document
				Method:  http.MethodPut,
				Path:    "/black/box/case/map/document",
				Handler: v2blackboxcasemapdocument.UpdateBlackBoxCaseMapDocumentHandler(serverCtx),
			},
			{
				// delete blackbox case map document
				Method:  http.MethodDelete,
				Path:    "/black/box/case/map/document",
				Handler: v2blackboxcasemapdocument.DeleteBlackBoxCaseMapDocumentHandler(serverCtx),
			},
			{
				// list blackbox case map documents
				Method:  http.MethodGet,
				Path:    "/black/box/case/map/documents",
				Handler: v2blackboxcasemapdocument.ListBlackBoxCaseMapDocumentsHandler(serverCtx),
			},
		},
		rest.WithPrefix("/ai/v2"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				// create blackbox case map experience
				Method:  http.MethodPost,
				Path:    "/black/box/case/map/experience",
				Handler: v2blackboxcasemapexperience.CreateBlackBoxCaseMapExperienceHandler(serverCtx),
			},
			{
				// create blackbox case map experience with sse
				Method:  http.MethodPost,
				Path:    "/black/box/case/map/experience/create",
				Handler: v2blackboxcasemapexperience.CreateBlackBoxCaseMapExperienceWithSseHandler(serverCtx),
			},
			{
				// update blackbox case map experience with sse
				Method:  http.MethodPost,
				Path:    "/black/box/case/map/experience/update",
				Handler: v2blackboxcasemapexperience.UpdateBlackBoxCaseMapExperienceWithSseHandler(serverCtx),
			},
		},
		rest.WithPrefix("/ai/v2"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				// create blackbox case map function
				Method:  http.MethodPost,
				Path:    "/black/box/case/map/function",
				Handler: v2blackboxcasemapfunction.CreateBlackBoxCaseMapFunctionHandler(serverCtx),
			},
			{
				// create blackbox case map function with sse
				Method:  http.MethodPost,
				Path:    "/black/box/case/map/function/create",
				Handler: v2blackboxcasemapfunction.CreateBlackBoxCaseMapFunctionWithSseHandler(serverCtx),
			},
		},
		rest.WithPrefix("/ai/v2"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				// get blackbox case map knowledge
				Method:  http.MethodGet,
				Path:    "/black/box/case/map/knowledge",
				Handler: v2blackboxcasemapknowledge.GetBlackBoxCaseMapKnowledgeHandler(serverCtx),
			},
		},
		rest.WithPrefix("/ai/v2"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				// get blackbox case map progress
				Method:  http.MethodGet,
				Path:    "/black/box/case/map/progress",
				Handler: v2blackboxcasemapprogress.GetBlackBoxCaseMapProgressHandler(serverCtx),
			},
		},
		rest.WithPrefix("/ai/v2"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				// create blackbox knowledge experience
				Method:  http.MethodPost,
				Path:    "/black/box/knowledge/experience",
				Handler: v2blackboxknowledgeexperience.CreateBlackBoxKnowledgeExperienceHandler(serverCtx),
			},
			{
				// update blackbox knowledge experience
				Method:  http.MethodPut,
				Path:    "/black/box/knowledge/experience",
				Handler: v2blackboxknowledgeexperience.UpdateBlackBoxKnowledgeExperienceHandler(serverCtx),
			},
			{
				// delete blackbox knowledge experience
				Method:  http.MethodDelete,
				Path:    "/black/box/knowledge/experience",
				Handler: v2blackboxknowledgeexperience.DeleteBlackBoxKnowledgeExperienceHandler(serverCtx),
			},
			{
				// list blackbox knowledge experiences
				Method:  http.MethodPost,
				Path:    "/black/box/knowledge/experiences",
				Handler: v2blackboxknowledgeexperience.ListBlackBoxKnowledgeExperiencesHandler(serverCtx),
			},
		},
		rest.WithPrefix("/ai/v2"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				// list blackbox knowledge experience categories
				Method:  http.MethodGet,
				Path:    "/black/box/knowledge/experience/categories",
				Handler: v2blackboxknowledgeexperiencecategory.ListBlackBoxKnowledgeExperienceCategoriesHandler(serverCtx),
			},
			{
				// create blackbox knowledge experience category
				Method:  http.MethodPost,
				Path:    "/black/box/knowledge/experience/category",
				Handler: v2blackboxknowledgeexperiencecategory.CreateBlackBoxKnowledgeExperienceCategoryHandler(serverCtx),
			},
			{
				// delete blackbox knowledge experience category
				Method:  http.MethodDelete,
				Path:    "/black/box/knowledge/experience/category",
				Handler: v2blackboxknowledgeexperiencecategory.DeleteBlackBoxKnowledgeExperienceCategoryHandler(serverCtx),
			},
		},
		rest.WithPrefix("/ai/v2"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				// create blackbox knowledge project
				Method:  http.MethodPost,
				Path:    "/black/box/knowledge/project",
				Handler: v2blackboxknowledgeproject.CreateBlackBoxKnowledgeProjectHandler(serverCtx),
			},
		},
		rest.WithPrefix("/ai/v2"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				// create blackbox knowledge tag
				Method:  http.MethodPost,
				Path:    "/black/box/knowledge/tag",
				Handler: v2blackboxknowledgetag.CreateBlackBoxKnowledgeTagHandler(serverCtx),
			},
			{
				// update blackbox knowledge tag
				Method:  http.MethodPut,
				Path:    "/black/box/knowledge/tag",
				Handler: v2blackboxknowledgetag.UpdateBlackBoxKnowledgeTagHandler(serverCtx),
			},
			{
				// delete blackbox knowledge tag
				Method:  http.MethodDelete,
				Path:    "/black/box/knowledge/tag",
				Handler: v2blackboxknowledgetag.DeleteBlackBoxKnowledgeTagHandler(serverCtx),
			},
			{
				// list blackbox knowledge tags
				Method:  http.MethodGet,
				Path:    "/black/box/knowledge/tags",
				Handler: v2blackboxknowledgetag.ListBlackBoxKnowledgeTagsHandler(serverCtx),
			},
		},
		rest.WithPrefix("/ai/v2"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				// create blackbox knowledge term
				Method:  http.MethodPost,
				Path:    "/black/box/knowledge/term",
				Handler: v2blackboxknowledgeterm.CreateBlackBoxKnowledgeTermHandler(serverCtx),
			},
			{
				// update blackbox knowledge term
				Method:  http.MethodPut,
				Path:    "/black/box/knowledge/term",
				Handler: v2blackboxknowledgeterm.UpdateBlackBoxKnowledgeTermHandler(serverCtx),
			},
			{
				// delete blackbox knowledge term
				Method:  http.MethodDelete,
				Path:    "/black/box/knowledge/term",
				Handler: v2blackboxknowledgeterm.DeleteBlackBoxKnowledgeTermHandler(serverCtx),
			},
			{
				// list blackbox knowledge terms
				Method:  http.MethodPost,
				Path:    "/black/box/knowledge/terms",
				Handler: v2blackboxknowledgeterm.ListBlackBoxKnowledgeTermsHandler(serverCtx),
			},
		},
		rest.WithPrefix("/ai/v2"),
	)
}
