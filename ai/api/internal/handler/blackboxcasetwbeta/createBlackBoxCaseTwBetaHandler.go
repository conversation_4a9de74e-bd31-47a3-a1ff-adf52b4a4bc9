package blackboxcasetwbeta

import (
	"net/http"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/response"

	"github.com/zeromicro/go-zero/rest/httpx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/logic/blackboxcasetwbeta"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/types"
)

// CreateBlackBoxCaseTwBetaHandler create black box tw test case
func CreateBlackBoxCaseTwBetaHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.CreateBlackBoxCaseTwBetaReq
		if err := httpx.Parse(r, &req); err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
			return
		}

		l := blackboxcasetwbeta.NewCreateBlackBoxCaseTwBetaLogic(r.Context(), svcCtx)
		stream, err := l.CreateBlackBoxCaseTwBeta(&req)
		response.MakeHttpResponse2EventStream(r, w, stream, err)
	}
}
