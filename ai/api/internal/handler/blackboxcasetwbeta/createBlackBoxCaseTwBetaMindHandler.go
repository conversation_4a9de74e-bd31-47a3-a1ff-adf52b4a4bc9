package blackboxcasetwbeta

import (
	"net/http"

	"github.com/zeromicro/go-zero/rest/httpx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/logic/blackboxcasetwbeta"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/types"
)

func CreateBlackBoxCaseTwBetaMindHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.CreateBlackBoxCaseTwBetaMindReq
		if err := httpx.Parse(r, &req); err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
			return
		}

		l := blackboxcasetwbeta.NewCreateBlackBoxCaseTwBetaMindLogic(r.Context(), svcCtx)
		resp, err := l.CreateBlackBoxCaseTwBetaMind(&req)
		if err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
		} else {
			httpx.OkJsonCtx(r.Context(), w, resp)
		}
	}
}
