package blackboxcasetwbeta

import (
	"net/http"

	"github.com/zeromicro/go-zero/rest/httpx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/logic/blackboxcasetwbeta"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/types"
)

// get black box tw case content
func GetBlackBoxCaseTwBetaCaseContentHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.GetBlackBoxCaseTwBetaContentReq
		if err := httpx.Parse(r, &req); err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
			return
		}

		l := blackboxcasetwbeta.NewGetBlackBoxCaseTwBetaCaseContentLogic(r.Context(), svcCtx)
		resp, err := l.GetBlackBoxCaseTwBetaCaseContent(&req)
		if err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
		} else {
			httpx.OkJsonCtx(r.Context(), w, resp)
		}
	}
}
