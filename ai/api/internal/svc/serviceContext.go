package svc

import (
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/zrpc"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/client/blackboxcasedirectoryservice"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/client/blackboxcasemapdocumentservice"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/client/blackboxcasemapservice"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/client/blackboxcasetwbetaservice"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/client/blackboxgenerationrecordservice"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/client/projectservice"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/interceptors/clientinterceptors"
	userService "gitlab.ttyuyin.com/TestDevelopment/qet-backend-middleware/user/rpc/client/userservice"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/config"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/client/blackboxcaseassistantservice"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/client/blackboxcasedataservice"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/client/blackboxcasedirservice"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/client/blackboxcasedocumentservice"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/client/blackboxcaseknowledgeservice"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/client/blackboxcaseknowledgev2service"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/client/blackboxcaserevisionservice"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/client/blackboxcaseservice"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/client/blackboxcasesessionservice"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"
)

type ServiceContext struct {
	Config                          config.Config
	ExternalAiDomain                string
	Validator                       *utils.Validator
	UserRpc                         userService.UserService
	BlackBoxCaseRefModel            model.BlackBoxCaseRefModel
	BlackBoxCaseDataModel           model.BlackBoxCaseDataModel
	BlackBoxCaseAssistantServiceRpc blackboxcaseassistantservice.BlackBoxCaseAssistantService
	BlackBoxCaseDocumentServiceRpc  blackboxcasedocumentservice.BlackBoxCaseDocumentService
	BlackBoxCaseSessionServiceRpc   blackboxcasesessionservice.BlackBoxCaseSessionService
	BlackBoxCaseDirServiceRpc       blackboxcasedirservice.BlackBoxCaseDirService
	BlackBoxCaseDataServiceRpc      blackboxcasedataservice.BlackBoxCaseDataService
	BlackBoxCaseServiceRpc          blackboxcaseservice.BlackBoxCaseService
	BlackBoxCaseKnowledgeServiceRpc blackboxcaseknowledgeservice.BlackBoxCaseKnowledgeService
	BlackBoxCaseRevisionServiceRpc  blackboxcaserevisionservice.BlackBoxCaseRevisionService
	BlackBoxCaseTwBetaServiceRpc    blackboxcasetwbetaservice.BlackBoxCaseTwBetaService
	// v2
	ManagerProjectRpc                  projectservice.ProjectService
	BlackBoxCaseDirectoryServiceRpc    blackboxcasedirectoryservice.BlackBoxCaseDirectoryService
	BlackBoxCaseKnowledgeV2ServiceRpc  blackboxcaseknowledgev2service.BlackBoxCaseKnowledgeV2Service
	BlackBoxCaseMapServiceRpc          blackboxcasemapservice.BlackBoxCaseMapService
	BlackBoxCaseMapDocumentServiceRpc  blackboxcasemapdocumentservice.BlackBoxCaseMapDocumentService
	BlackBoxGenerationRecordServiceRpc blackboxgenerationrecordservice.BlackBoxGenerationRecordService
}

func NewServiceContext(c config.Config) *ServiceContext {
	sqlConn := sqlx.NewMysql(c.DB.DataSource)
	return &ServiceContext{
		Config:           c,
		ExternalAiDomain: c.ExternalAiDomain,
		Validator:        utils.NewValidator(c.Validator.Locale),

		UserRpc: userService.NewUserService(
			zrpc.MustNewClient(
				c.UserRpc, zrpc.WithUnaryClientInterceptor(clientinterceptors.UserInfoUnaryClientInterceptor),
			),
		),
		BlackBoxCaseRefModel:  model.NewBlackBoxCaseRefModel(sqlConn, c.Cache),
		BlackBoxCaseDataModel: model.NewBlackBoxCaseDataModel(sqlConn, c.Cache),
		BlackBoxCaseAssistantServiceRpc: blackboxcaseassistantservice.NewBlackBoxCaseAssistantService(
			zrpc.MustNewClient(
				c.AIRpc, clientinterceptors.UnaryUserInfoClientOption(),
			),
		),
		BlackBoxCaseDocumentServiceRpc: blackboxcasedocumentservice.NewBlackBoxCaseDocumentService(
			zrpc.MustNewClient(
				c.AIRpc, clientinterceptors.UnaryUserInfoClientOption(),
			),
		),
		BlackBoxCaseSessionServiceRpc: blackboxcasesessionservice.NewBlackBoxCaseSessionService(
			zrpc.MustNewClient(
				c.AIRpc, clientinterceptors.UnaryUserInfoClientOption(),
			),
		),
		BlackBoxCaseDirServiceRpc: blackboxcasedirservice.NewBlackBoxCaseDirService(
			zrpc.MustNewClient(
				c.AIRpc, clientinterceptors.UnaryUserInfoClientOption(),
			),
		),
		BlackBoxCaseServiceRpc: blackboxcaseservice.NewBlackBoxCaseService(
			zrpc.MustNewClient(
				c.AIRpc, clientinterceptors.UnaryUserInfoClientOption(),
			),
		),
		BlackBoxCaseKnowledgeServiceRpc: blackboxcaseknowledgeservice.NewBlackBoxCaseKnowledgeService(
			zrpc.MustNewClient(
				c.AIRpc, clientinterceptors.UnaryUserInfoClientOption(),
			),
		),
		BlackBoxCaseRevisionServiceRpc: blackboxcaserevisionservice.NewBlackBoxCaseRevisionService(
			zrpc.MustNewClient(
				c.AIRpc, clientinterceptors.UnaryUserInfoClientOption(),
			),
		),
		BlackBoxCaseDataServiceRpc: blackboxcasedataservice.NewBlackBoxCaseDataService(
			zrpc.MustNewClient(
				c.AIRpc, clientinterceptors.UnaryUserInfoClientOption(),
			),
		),
		BlackBoxCaseTwBetaServiceRpc: blackboxcasetwbetaservice.NewBlackBoxCaseTwBetaService(
			zrpc.MustNewClient(
				c.AIRpc, clientinterceptors.UnaryUserInfoClientOption(),
			),
		),
		ManagerProjectRpc: projectservice.NewProjectService(
			zrpc.MustNewClient(
				c.ManagerRpc, clientinterceptors.UnaryUserInfoClientOption(),
			),
		),
		BlackBoxCaseDirectoryServiceRpc: blackboxcasedirectoryservice.NewBlackBoxCaseDirectoryService(
			zrpc.MustNewClient(
				c.AIRpc, clientinterceptors.UnaryUserInfoClientOption(),
			),
		),
		BlackBoxCaseKnowledgeV2ServiceRpc: blackboxcaseknowledgev2service.NewBlackBoxCaseKnowledgeV2Service(
			zrpc.MustNewClient(
				c.AIRpc, clientinterceptors.UnaryUserInfoClientOption(),
			),
		),
		BlackBoxCaseMapServiceRpc: blackboxcasemapservice.NewBlackBoxCaseMapService(
			zrpc.MustNewClient(
				c.AIRpc, clientinterceptors.UnaryUserInfoClientOption(),
			),
		),
		BlackBoxCaseMapDocumentServiceRpc: blackboxcasemapdocumentservice.NewBlackBoxCaseMapDocumentService(
			zrpc.MustNewClient(
				c.AIRpc, clientinterceptors.UnaryUserInfoClientOption(),
			),
		),
		BlackBoxGenerationRecordServiceRpc: blackboxgenerationrecordservice.NewBlackBoxGenerationRecordService(
			zrpc.MustNewClient(
				c.AIRpc, clientinterceptors.UnaryUserInfoClientOption(),
			),
		),
	}
}
