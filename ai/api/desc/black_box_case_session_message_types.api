syntax = "v1"

import "types.api"

type BlackBoxCaseSessionMessage {
    Content BlackBoxCaseSessionMsgContent `json:"content" zh:"消息内容"`
    Metadata BlackBoxCaseSessionMsgMetadata `json:"metadata" zh:"消息内容"`
}

type BlackBoxCaseSessionMsgContent{
    Human string `json:"human" zh:"human"`
    Ai string `json:"ai" zh:"ai"`
}

type BlackBoxCaseSessionMsgMetadata{
    ContentType string `json:"content_type" zh:"内容类型 table, text"`
    SessionId string `json:"session_id" zh:"会话Id"`
    CreatedAt string `json:"created_at" zh:"创建时间"`
    ConversationId string `json:"conversation_id" zh:"对话id"`
    State BlackBoxCaseSessionConversationState `json:"state" zh:"状态"`
}


type BlackBoxCaseSessionConversationState{
    State string `json:"state" zh:"state"`
    StateDesc string `json:"state_desc" zh:"state_desc"`
}