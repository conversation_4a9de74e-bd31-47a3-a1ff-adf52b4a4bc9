syntax = "v1"

import "black_box_case_types.api"

@server (
	prefix: ai/v1
	group: blackboxcase
    //timeout : 3000ms
)
service ai {
    @doc "list black box case"
    @handler listBlackBoxCase
    post /black/box/case/list (ListBlackBoxCaseReq) returns (ListBlackBoxCaseResp)

    @handler getBlackBoxCase
    get /black/box/case (GetBlackBoxCaseReq) returns (GetBlackBoxCaseResp)

    @handler createBlackBoxCase
    post /black/box/case (CreateBlackBoxCaseReq) returns (CreateBlackBoxCaseResp)

    @handler deleteBlackBoxCase
    delete /black/box/case (DeleteBlackBoxCaseReq) returns (DeleteBlackBoxCaseResp)

    @handler updateBlackBoxCase
    put /black/box/case (UpdateBlackBoxCaseItemReq) returns (UpdateBlackBoxCaseItemResp)

    @handler updateBlackBoxCaseAI
    put /black/box/case/ai (UpdateBlackBoxCaseAIReq) returns (UpdateBlackBoxCaseAIResp)

    @handler mergeBlackBoxCaseData
    post /black/box/case/data/merge (MergeBlackBoxCaseDataReq) returns (MergeBlackBoxCaseDataResp)
}
