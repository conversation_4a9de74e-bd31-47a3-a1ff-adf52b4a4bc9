syntax = "v1"

import "types.api"

type BlackBoxCaseDocument {
    Id int `json:"id" zh:"自增Id"`
    ProjectId string `json:"project_id" zh:"项目Id"`
    AssistantId string `json:"assistant_id" zh:"助手Id"`
    DocumentId string `json:"document_id" zh:"文档Id"`
    DocumentName string `json:"document_name" zh:"文档名称"`
    DocumentDescription string `json:"document_description,omitempty" zh:"文档描述"`
    DocumentUrl string `json:"document_url,omitempty" zh:"文档地址"`
    DocumentText string `json:"document_text,omitempty" zh:"文档文本"`
    DocumentType int8 `json:"document_type" zh:"文档类型：纯文本(text)：0，飞书(feishu)：1，doc(doc)：2"`
    ExternalDocumentId string `json:"external_document_id,omitempty" zh:"外部文档Id"`
    Status int8 `json:"status" zh:"文档状态：处理中：0，完成：1，失败：2"`
    CreatedBy *FullUserInfo `json:"created_by" zh:"创建者的用户Id"`
    UpdatedBy *FullUserInfo `json:"updated_by" zh:"最近一次更新者的用户Id"`
    DeletedBy *FullUserInfo `json:"deleted_by,omitempty" zh:"删除者的用户Id"`
    CreatedAt int64 `json:"created_at" zh:"创建时间"`
    UpdatedAt int64 `json:"updated_at" zh:"更新时间"`
    DeletedAt int64 `json:"deleted_at" zh:"删除时间"`
}

// 查询黑盒用例文档
type (
    SearchBlackBoxCaseDocumentForAssistantReq {
        ProjectId string `json:"project_id" validate:"required" zh:"项目Id"`
        AssistantId string `json:"assistant_id" zh:"助手Id"`
        //Condition *Condition `json:"condition,omitempty,optional" zh:"查询条件"`
        Pagination *Pagination `json:"pagination,omitempty,optional" zh:"查询分页"`
        //Sort []*SortField `json:"sort,omitempty,optional" zh:"查询排序"`
    }
    SearchBlackBoxCaseDocumentForAssistantResp {
        CurrentPage uint64 `json:"current_page"`
        PageSize uint64 `json:"page_size"`
        TotalCount uint64 `json:"total_count"`
        TotalPage uint64 `json:"total_page"`
        Items []*BlackBoxCaseDocument `json:"items"`
    }
)

// 查询黑盒用例文档
type (
    SearchBlackBoxCaseDocumentForSessionReq {
        ProjectId string `json:"project_id" validate:"required" zh:"项目Id"`
        AssistantId string `json:"assistant_id" zh:"助手Id"`
        SessionId string `json:"session_id" zh:"会话Id"`
        //Condition *Condition `json:"condition,omitempty,optional" zh:"查询条件"`
        Pagination *Pagination `json:"pagination,omitempty,optional" zh:"查询分页"`
        //Sort []*SortField `json:"sort,omitempty,optional" zh:"查询排序"`
    }
    SearchBlackBoxCaseDocumentForSessionResp {
        CurrentPage uint64 `json:"current_page"`
        PageSize uint64 `json:"page_size"`
        TotalCount uint64 `json:"total_count"`
        TotalPage uint64 `json:"total_page"`
        Items []*BlackBoxCaseDocument `json:"items"`
    }
)
type (
    UpdateBlackBoxCaseDocumentReq {
        ProjectId string `json:"project_id" validate:"required" zh:"项目Id"`
        AssistantId string `json:"assistant_id" validate:"required" zh:"助手Id"`
        DocumentId string `json:"document_id" validate:"required" zh:"文档Id"`
        DocumentName string `json:"document_name" validate:"required" zh:"文档名称"`
        DocumentDescription string `json:"document_description,omitempty" zh:"文档描述"`
    }
    UpdateBlackBoxCaseDocumentResp {
    }
)

type (
    ReloadBlackBoxCaseDocumentReq {
        ProjectId string `json:"project_id" validate:"required" zh:"项目Id"`
        AssistantId string `json:"assistant_id" validate:"required" zh:"助手Id"`
        DocumentId string `json:"document_id" validate:"required" zh:"文档Id"`
    }
    ReloadBlackBoxCaseDocumentResp {
    }
)

type (
    DeleteBlackBoxCaseDocumentForAssistantReq {
        ProjectId string `json:"project_id" validate:"required" zh:"项目Id"`
        AssistantId string `json:"assistant_id" validate:"required" zh:"助手Id"`
        DocumentId string `json:"document_id" zh:"文档Id"`
    }
    DeleteBlackBoxCaseDocumentForAssistantResp {
    }
)
type (
    BatchDeleteBlackBoxCaseDocumentForAssistantReq {
        ProjectId string `json:"project_id" validate:"required" zh:"项目Id"`
        AssistantId string `json:"assistant_id" validate:"required" zh:"助手Id"`
        DocumentIds []string `json:"document_ids" zh:"文档Id集合"`
    }
    BatchDeleteBlackBoxCaseDocumentForAssistantResp {
    }
)
type (
    DeleteBlackBoxCaseDocumentForSessionReq {
        ProjectId string `form:"project_id" validate:"required" zh:"项目Id"`
        AssistantId string `form:"assistant_id" validate:"required" zh:"助手Id"`
        SessionId string `form:"session_id" validate:"required" zh:"会话Id"`
        DocumentId string `form:"document_id" zh:"文档Id集合"`
    }
    DeleteBlackBoxCaseDocumentForSessionResp {
    }
)
type (
    BatchDeleteBlackBoxCaseDocumentForSessionReq {
        ProjectId string `json:"project_id" validate:"required" zh:"项目Id"`
        AssistantId string `json:"assistant_id" validate:"required" zh:"助手Id"`
        SessionId string `json:"session_id" validate:"required" zh:"会话Id"`
        DocumentIds []string `json:"document_ids" zh:"文档Id集合"`
    }
    BatchDeleteBlackBoxCaseDocumentForSessionResp {
    }
)

type (
    GetBlackBoxCaseDocumentReq {
        ProjectId string `form:"project_id" validate:"required" zh:"项目Id"`
        AssistantId string `form:"assistant_id" validate:"required" zh:"助手Id"`
        DocumentId string `form:"document_id" validate:"required"  zh:"文档Id"`
    }
    GetBlackBoxCaseDocumentResp {
        item *BlackBoxCaseDocument `json:"item"`
    }
)

type (
    CreateBlackBoxCaseDocumentForAssistantReq {
        ProjectId string `json:"project_id" validate:"required" zh:"项目Id"`
        AssistantId string `json:"assistant_id" validate:"required" zh:"助手Id"`
        Items []*CreateBlackBoxCaseDocumentForAssistant `json:"items"`
    }
    CreateBlackBoxCaseDocumentForAssistant  {
        DocumentName string `json:"document_name" validate:"required" zh:"文档名称"`
        DocumentDescription string `json:"document_description,omitempty" zh:"文档描述"`
        DocumentUrl string `json:"document_url,omitempty" zh:"文档地址"`
        DocumentText string `json:"document_text,omitempty" zh:"文档文本"`
        DocumentType int8 `json:"document_type" validate:"required" zh:"文档类型：纯文本(text)：0，飞书(feishu)：1，doc(doc)：2"`
    }
    CreateBlackBoxCaseDocumentForAssistantResp {
        ProjectId string `json:"project_id" zh:"项目Id"`
        AssistantId string `json:"assistant_id" zh:"助手Id"`
        DocumentIdList []string `json:"document_id_list" zh:"文档Id"`
    }
)

type (
    UpdateBlackBoxCaseDocumentStatusForAssistantReq {
        ProjectId string `json:"project_id,optional" validate:"omitempty" zh:"项目Id"`
        DocumentId string `json:"document_id" validate:"required" zh:"文档名称"`
        Status int8 `json:"status" validate:"required" zh:"文档状态：处理中：0，完成：1，失败：2"`
    }
    UpdateBlackBoxCaseDocumentStatusForAssistantResp {
    }
)

type (
    CreateBlackBoxCaseDocumentForSessionReq {
        ProjectId string `json:"project_id" validate:"required" zh:"项目Id"`
        AssistantId string `json:"assistant_id" validate:"required" zh:"助手Id"`
        SessionId string `json:"session_id" validate:"required" zh:"会话Id"`
        DocumentId string `json:"document_id" validate:"required" zh:"文档Id"`
    }
    CreateBlackBoxCaseDocumentForSessionResp {
    }
)

type (
    CreateBlackBoxCaseDocumentForSessionRecvReq {
        ProjectId string `json:"project_id" validate:"required" zh:"项目Id"`
        AssistantId string `json:"assistant_id" validate:"required" zh:"助手Id"`
        SessionId string `json:"session_id" validate:"required" zh:"会话Id"`
        MsgDocumentText string `json:"msg_document_text" validate:"required" zh:"消息文档文本"`
    }

    CreateBlackBoxCaseDocumentForSessionRecvResp {
        ProjectId string `json:"project_id" zh:"项目Id"`
        AssistantId string `json:"assistant_id" zh:"助手Id"`
        DocumentId string `json:"document_id" zh:"文档Id"`
    }
)

type (
    GetBlackBoxCaseDocumentHeadersListReq {
        ProjectId string `form:"project_id" validate:"required" zh:"项目Id"`
        AssistantId string `form:"assistant_id" validate:"required" zh:"助手Id"`
        SessionId string `form:"session_id" validate:"required" zh:"会话Id"`
        DocumentId string `form:"document_id" validate:"required" zh:"文档Id"`
    }

    GetBlackBoxCaseDocumentHeadersListResp {
        Items []string `json:"items" zh:"文档标题集合"`
    }
)