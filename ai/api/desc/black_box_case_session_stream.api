syntax = "v1"

import "black_box_case_session_types.api"

@server(
    prefix: ai/v1
    group: blackboxcasesessionstream
)
service ai {
    @doc "send stream black box case for session"
    @handler sendStreamBlackBoxCaseForSession
    post /stream/black/box/case/for_session/send (SendBlackBoxCaseForSessionReq) returns (SendBlackBoxCaseForSessionResp)

    @doc "update stream black box case for session"
    @handler UpdateBlackBoxCaseReq
    post /stream/black/box/case/for_session/update (UpdateBlackBoxCaseReq) returns (SendBlackBoxCaseForSessionResp)

    @doc "recreate black box case"
    @handler reCreateBlackBoxCase
    post /stream/black/box/case/for_session/recreate (ReCreateBlackBoxCaseReq) returns (ReCreateBlackBoxCaseResp)

}
