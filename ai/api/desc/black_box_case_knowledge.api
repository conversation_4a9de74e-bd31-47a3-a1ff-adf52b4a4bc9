syntax = "v1"

import "black_box_case_knowledge_types.api"

@server (
	prefix: ai/v1
	group: blackboxcaseknowledge
    //timeout : 3000ms
)
service ai {
    @doc "list black box case"
    @handler listBlackBoxCaseKnowledge
    post /black/box/case/knowledge/list (ListBlackBoxCaseKnowledgeReq) returns (ListBlackBoxCaseKnowledgeResp)

    @handler getBlackBoxCaseKnowledge
    get /black/box/case/knowledge (GetBlackBoxCaseKnowledgeReq) returns (GetBlackBoxCaseKnowledgeResp)

    @handler createBlackBoxCaseKnowledge
    post /black/box/case/knowledge (CreateBlackBoxCaseKnowledgeReq) returns (CreateBlackBoxCaseKnowledgeResp)

    @handler deleteBlackBoxCaseKnowledge
    delete /black/box/case/knowledge (DeleteBlackBoxCaseKnowledgeReq) returns (DeleteBlackBoxCaseKnowledgeResp)

    @handler updateBlackBoxCaseKnowledge
    put /black/box/case/knowledge (UpdateBlackBoxCaseKnowledgeReq) returns (UpdateBlackBoxCaseKnowledgeResp)

    @handler reloadBlackBoxCaseKnowledge
    post /black/box/case/knowledge/reload (ReloadBlackBoxCaseKnowledgeReq) returns (ReloadBlackBoxCaseKnowledgeResp)

    @handler queryBlackBoxCaseKnowledge
    post /black/box/case/knowledge/query (QueryBlackBoxCaseKnowledgeReq) returns (QueryBlackBoxCaseKnowledgeResp)

    @handler getBlackBoxCaseKnowledgeTitle
    get /black/box/case/knowledge/title (GetBlackBoxCaseKnowledgeTitleReq) returns (GetBlackBoxCaseKnowledgeTitleResp)
}
