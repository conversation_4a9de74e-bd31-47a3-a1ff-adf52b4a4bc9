syntax = "v1"

import "types.api"
import "black_box_case_session_message_types.api"

type BlackBoxCaseSession {
    Id int `json:"id" zh:"自增Id"`
    ProjectId string `json:"project_id" zh:"项目Id"`
    AssistantId string `json:"assistant_id" zh:"助手Id"`
    SessionId string `json:"session_id" zh:"会话Id"`
    SessionName string `json:"session_name" zh:"会话名称"`
    SessionDescription string `json:"session_description,omitempty" zh:"会话描述"`
    SessionRound int `json:"session_round" zh:"对话轮数[继承会话配置轮数]"`
    DocumentIds string `json:"document_ids" zh:"文档集合[继承会话文档的子集]"`
    Status int8 `json:"status" zh:"会话状态：使用中：0，归档：1"`
    CreatedBy *FullUserInfo `json:"created_by" zh:"创建者的用户Id"`
    UpdatedBy *FullUserInfo `json:"updated_by" zh:"最近一次更新者的用户Id"`
    DeletedBy *FullUserInfo `json:"deleted_by,omitempty" zh:"删除者的用户Id"`
    CreatedAt int64 `json:"created_at" zh:"创建时间"`
    UpdatedAt int64 `json:"updated_at" zh:"更新时间"`
    DeletedAt int64 `json:"deleted_at,omitempty" zh:"删除时间"`
}

type BlackBoxCaseReferenceContent {
    ContentType string `json:"content_type" zh:"内容类型：文本：text，文档：docß"`
    DocHeaders []string `json:"doc_headers" zh:"文档标题列表"`
    DocId string `json:"doc_id" zh:"文档id"`
    Text string `json:"text" zh:"文本内容"`
    TextHeader string `json:"text_header" zh:"文本标题"`
}

// 查询黑盒用例会话
type (
    SearchBlackBoxCaseSessionReq {
        ProjectId string `json:"project_id" validate:"required" zh:"项目Id"`
        AssistantId string `json:"assistant_id" zh:"助手Id"`
    }
    SearchBlackBoxCaseSessionResp {
        Items []*BlackBoxCaseSession `json:"items"`
    }
)
type (
    UpdateBlackBoxCaseSessionReq {
        ProjectId string `json:"project_id" zh:"项目Id"`
        AssistantId string `json:"assistant_id" zh:"助手Id"`
        WorkSpaceType int8 `json:"work_space_type" validate:"required" zh:"工作空间类型：个人空间(private)：0，公共空间()：1"`
        SessionId string `json:"session_id" zh:"会话Id"`
        SessionName string `json:"session_name" zh:"会话名称"`
        SessionDescription string `json:"session_description,omitempty" zh:"会话描述"`
    }
    UpdateBlackBoxCaseSessionResp {
    }
)
type (
    DeleteBlackBoxCaseSessionReq {
        ProjectId string `form:"project_id" validate:"required" zh:"项目Id"`
        AssistantId string `form:"assistant_id" validate:"required" zh:"助手Id"`
        SessionId string `form:"session_id" validate:"required" zh:"会话Id"`
    }
    DeleteBlackBoxCaseSessionResp {
    }
)

type (
    GetBlackBoxCaseSessionReq {
        ProjectId string `form:"project_id" validate:"required" zh:"项目Id"`
        AssistantId string `form:"assistant_id" validate:"required" zh:"助手Id"`
        SessionId string `form:"session_id" validate:"required" zh:"会话Id"`
    }
    GetBlackBoxCaseSessionResp {
        item *BlackBoxCaseSession `json:"item"`
    }
)

type (
    SendBlackBoxCaseForSessionReq {
        ProjectId string `json:"project_id" validate:"required" zh:"项目Id"`
        AssistantId string `json:"assistant_id" validate:"required" zh:"助手Id"`
        SessionId string `json:"session_id" validate:"required" zh:"会话Id"`
        Message string `json:"message" validate:"required" zh:"消息"`
        ConversationId string `json:"conversation_id" validate:"required" zh:"对话 id"`

    }
    SendBlackBoxCaseForSessionResp {
        Message *BlackBoxCaseSessionMessage `json:"message"`
        Event string `json:"event" zh:"事件类型 [start | running | done]"`
    }
)

type (
    GetBlackBoxCaseSessionHistoryMessageListReq {
        ProjectId string `form:"project_id" validate:"required" zh:"项目Id"`
        AssistantId string `form:"assistant_id" validate:"required" zh:"助手Id"`
        SessionId string `form:"session_id" validate:"required" zh:"会话Id"`
        SessionRound int8 `form:"session_round" validate:"required" zh:"对话轮数"`
    }
    GetBlackBoxCaseSessionHistoryMessageListResp {
        Messages []*BlackBoxCaseSessionMessage `json:"messages"`
    }
)

type (
    RemoveBlackBoxCaseSessionHistoryMessageListReq {
        ProjectId string `form:"project_id" validate:"required" zh:"项目Id"`
        AssistantId string `form:"assistant_id" validate:"required" zh:"助手Id"`
        SessionId string `form:"session_id" validate:"required" zh:"会话Id"`
    }
    RemoveBlackBoxCaseSessionHistoryMessageListResp {
    }
)


type (
    CreateBlackBoxCaseSessionReq {
        ProjectId string `json:"project_id" zh:"项目Id"`
        AssistantId string `json:"assistant_id" zh:"助手Id"`
        SessionName string `json:"session_name" zh:"会话名称"`
        SessionDescription string `json:"session_description,omitempty" zh:"会话描述"`
    }
    CreateBlackBoxCaseSessionResp {
        ProjectId string `json:"project_id" zh:"项目Id"`
        AssistantId string `json:"assistant_id" zh:"助手Id"`
        SessionId string `json:"session_id" zh:"会话Id"`
    }
)

type (
    GetBlackBoxCaseConversationAIStateReq {
        ProjectId string `form:"project_id" validate:"required" zh:"项目Id"`
        AssistantId string `form:"assistant_id" validate:"required" zh:"助手Id"`
        SessionId string `form:"session_id" validate:"required" zh:"会话Id"`
        ConversationId string `form:"conversation_id" validate:"required" zh:"对话 id"`
    }

    GetBlackBoxCaseConversationAIStateResp {
        State string `json:"state" zh:"状态"`
        StateDesc string `json:"state_desc" zh:"状态描述"`
    }
)

type (
    GetBlackBoxCaseConversationAIMessageReq {
        ProjectId string `form:"project_id" validate:"required" zh:"项目Id"`
        AssistantId string `form:"assistant_id" validate:"required" zh:"助手Id"`
        SessionId string `form:"session_id" validate:"required" zh:"会话Id"`
        ConversationId string `form:"conversation_id" validate:"required" zh:"对话 id"`
    }

    GetBlackBoxCaseConversationAIMessageResp {
        State string `json:"state" zh:"状态"`
        StateDesc string `json:"state_desc" zh:"状态描述"`
        Message string `json:"message" zh:"文档内容"`
    }
)


type (
   GetBlackBoxCaseModifyTestCaseReferenceContentListReq {
       ProjectId string `form:"project_id" validate:"required" zh:"项目Id"`
       AssistantId string `form:"assistant_id" validate:"required" zh:"助手Id"`
       SessionId string `form:"session_id" validate:"required" zh:"会话Id"`
       ConversationId string `form:"conversation_id" validate:"required" zh:"对话 id"`
   }

   GetBlackBoxCaseModifyTestCaseReferenceContentListResp {
       ModifySuggestion string `json:"modify_suggestion" zh:"修改意见"`
       ReferenceContent []*BlackBoxCaseReferenceContent `json:"reference_content" zh:"关联文档"`
   }

)

type (
    CreateBlackBoxCaseModifyTestCaseReferenceContentReq {
        ProjectId string `json:"project_id" validate:"required" zh:"项目Id"`
        AssistantId string `json:"assistant_id" validate:"required" zh:"助手Id"`
        SessionId string `json:"session_id" validate:"required" zh:"会话Id"`
        ConversationId string `json:"conversation_id" validate:"required" zh:"对话 id"`
        ReferenceContent []*BlackBoxCaseReferenceContent `json:"reference_content" zh:"关联文档"`
    }

    CreateBlackBoxCaseModifyTestCaseReferenceContentResp {
    }

)


type (
    UpdateBlackBoxCaseReq {
        ProjectId string `json:"project_id" validate:"required" zh:"项目Id"`
        AssistantId string `json:"assistant_id" validate:"required" zh:"助手Id"`
        SessionId string `json:"session_id" validate:"required" zh:"会话Id"`
        ConversationId string `json:"conversation_id" validate:"required" zh:"对话 id"`
        ModifySuggestion string `json:"modify_suggestion" validate:"required" zh:"修改意见"`
        Content string `json:"content" zh:"用例列表"`
    }
)


type (
    ReplaceBlackBoxCaseReq {
        ProjectId string `json:"project_id" validate:"required" zh:"项目Id"`
        AssistantId string `json:"assistant_id" validate:"required" zh:"助手Id"`
        SessionId string `json:"session_id" validate:"required" zh:"会话Id"`
        ConversationId string `json:"conversation_id" validate:"required" zh:"对话 id"`
        Content string `json:"content" validate:"required" zh:"替换内容"`
    }

    ReplaceBlackBoxCaseResp {
    }
)


type (
    MergeBlackBoxCaseReq {
        ProjectId string `json:"project_id" validate:"required" zh:"项目Id"`
        AssistantId string `json:"assistant_id" validate:"required" zh:"助手Id"`
        SessionId string `json:"session_id" validate:"required" zh:"会话Id"`
        ConversationIds []string `json:"conversation_ids" validate:"required,min=2" zh:"对话 ids"`    }

    MergeBlackBoxCaseResp {
        Messages []*BlackBoxCaseSessionMessage `json:"messages"`
    }
)


type (
    TransferBlackBoxCase2XMindReq {
        ProjectId string `json:"project_id" validate:"required" zh:"项目Id"`
        AssistantId string `json:"assistant_id" validate:"required" zh:"助手Id"`
        SessionId string `json:"session_id" validate:"required" zh:"会话Id"`
        ConversationId string `json:"conversation_id" validate:"required" zh:"对话 id"`
        MindType int8 `json:"mind_type,optional" validate:"omitempty,oneof=0 1" zh:"转换类型：用例导图：0，评审导图：1"`
    }

    TransferBlackBoxCase2XMindResp {
        Data string `json:"data" zh:"转换结果"`
    }
)


type (
    ReCreateBlackBoxCaseReq {
        ProjectId string `json:"project_id" validate:"required" zh:"项目Id"`
        AssistantId string `json:"assistant_id" validate:"required" zh:"助手Id"`
        SessionId string `json:"session_id" validate:"required" zh:"会话Id"`
        ConversationId string `json:"conversation_id" validate:"required" zh:"对话 id"`
    }

    ReCreateBlackBoxCaseResp {
    }
)

type (
    GetCreateBlackBoxCaseAIMessageReq {
        ProjectId string `form:"project_id" validate:"required" zh:"项目Id"`
        AssistantId string `form:"assistant_id" validate:"required" zh:"助手Id"`
        SessionId string `form:"session_id" validate:"required" zh:"会话Id"`
        ConversationId string `form:"conversation_id" validate:"required" zh:"对话 id"`
    }

    GetCreateBlackBoxCaseAIMessageResp {
        Message string `json:"message" zh:"AI回答内容"`
    }
)