syntax = "v1"

import "black_box_case_data_types.api"

@server (
	prefix: ai/v1
	group: blackboxcasedata
    //timeout : 3000ms
)
service ai {
    @handler listBlackBoxCaseData
    post /black/box/case/data/list (ListBlackBoxCaseDataReq) returns (ListBlackBoxCaseDataResp)

    @handler createBlackBoxCaseData
    post /black/box/case/data (CreateBlackBoxCaseDataReq) returns (CreateBlackBoxCaseDataResp)

    @handler generateBlackBoxCaseData
    post /black/box/case/data/generate (GenerateBlackBoxCaseDataReq) returns (GenerateBlackBoxCaseDataResp)

    @handler deleteBlackBoxCaseData
    delete /black/box/case/data (DeleteBlackBoxCaseDataReq) returns (DeleteBlackBoxCaseDataResp)

    @handler updateBlackBoxCaseData
    put /black/box/case/data (UpdateBlackBoxCaseDataReq) returns (UpdateBlackBoxCaseDataResp)

    @handler updateBlackBoxCaseDataOrder
    put /black/box/case/data/order (UpdateBlackBoxCaseDataOrderReq) returns (UpdateBlackBoxCaseDataOrderResp)

    @handler keepBlackBoxCaseData
    post /black/box/case/data/keep (KeepBlackBoxCaseDataReq) returns (KeepBlackBoxCaseDataResp)

    @handler clearKeepBlackBoxCaseData
    post /black/box/case/data/keep/clear (ClearKeepBlackBoxCaseDataReq) returns (ClearKeepBlackBoxCaseDataResp)

    @handler appendBlackBoxCaseData
    post /black/box/case/data/append (AppendBlackBoxCaseDataReq) returns (AppendBlackBoxCaseDataResp)

    @handler replaceBlackBoxCaseData
    post /black/box/case/data/replace (ReplaceBlackBoxCaseDataReq) returns (ReplaceBlackBoxCaseDataResp)

    @handler getEditBlackBoxCaseDataProgress
    get /black/box/case/data/edit/progress (GetEditBlackBoxCaseDataProgressReq) returns (GetEditBlackBoxCaseDataProgressResp)

    @handler editBlackBoxCaseData
    post /black/box/case/data/edit (EditBlackBoxCaseDataReq) returns (EditBlackBoxCaseDataResp)

    @handler partialEditBlackBoxCaseData
    post /black/box/case/data/partial-edit (PartialEditBlackBoxCaseDataReq) returns (PartialEditBlackBoxCaseDataResp)

    @handler transMindBlackBoxCase
    post /black/box/case/data/mind/transfer (TransBlackBoxCaseDataReq) returns (TransBlackBoxCaseDataResp)

    @handler transReviewMindBlackBoxCase
    post /black/box/case/data/review-mind/transfer (TransBlackBoxCaseDataReq) returns (TransBlackBoxCaseDataResp)

    @handler transActivitiesMindBlackBoxCase
    post /black/box/case/data/activities-mind/transfer (TransBlackBoxCaseDataReq) returns (TransBlackBoxCaseDataResp)

    @handler getBlackBoxCaseAIState
    get /black/box/case/data/ai-state (GetBlackBoxCaseAIStateReq) returns (GetBlackBoxCaseAIStateResp)

    @handler getBlackBoxCaseRefJson
    get /black/box/case/data/ref (GetBlackBoxCaseRefJsonReq) returns (GetBlackBoxCaseRefResp)

    @handler reorderBlackBoxCaseData
    post /black/box/case/data/reorder (ReorderBlackBoxCaseDataReq) returns (ReorderBlackBoxCaseDataResp)

    @handler batchUpdateBlackBoxCaseData
    post /black/box/case/data/batch (BatchUpdateBlackBoxCaseDataReq) returns (BatchUpdateBlackBoxCaseDataResp)

    @handler metricsMigreation
    get /black/box/case/data/metrics/migreation (MetricsMigreation) returns (string)
}
