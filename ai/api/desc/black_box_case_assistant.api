syntax = "v1"

import "black_box_case_assistant_types.api"

@server (
	prefix: ai/v1
	group: blackboxcaseassistant
    //timeout : 3000ms
)
service ai {
    @doc "create black box case assistant"
    @handler createBlackBoxCaseAssistant
    post /black/box/case/assistant/create (CreateBlackBoxCaseAssistantReq) returns (CreateBlackBoxCaseAssistantResp)

    @doc "update black box case assistant"
    @handler updateBlackBoxCaseAssistant
    put /black/box/case/assistant/update (UpdateBlackBoxCaseAssistantReq) returns (UpdateBlackBoxCaseAssistantResp)

    @doc "delete black box case assistant"
    @handler deleteBlackBoxCaseAssistant
    delete /black/box/case/assistant/delete (DeleteBlackBoxCaseAssistantReq) returns (DeleteBlackBoxCaseAssistantResp)

    @doc "get black box case assistant"
    @handler getBlackBoxCaseAssistant
    get /black/box/case/assistant/get (GetBlackBoxCaseAssistantReq) returns (GetBlackBoxCaseAssistantResp)

	@doc "search black box case assistant"
	@handler searchBlackBoxCaseAssistant
	post /black/box/case/assistant/search (SearchBlackBoxCaseAssistantReq) returns (SearchBlackBoxCaseAssistantResp)
}
