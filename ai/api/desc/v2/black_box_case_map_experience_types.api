syntax = "v1"

import (
	"../types.api"
	"black_box_case_map_types.api"
)

type (
	CreateBlackBoxCaseMapExperienceWithSseResp  {}
	UpdateBlackBoxCaseMapExperienceWithSseRequset {
		ProjectId                 string                             `json:"project_id" zh:"项目Id"`
		DirId                     string                             `json:"dir_id" zh:"目录Id"`
		CaseId                    string                             `json:"case_id" validate:"required" zh:"用例Id"`
		KnowledgeSupplementEnable bool                               `json:"knowledge_supplement_enable" zh:"是否开启补充文档"`
		KnowledgeSupplementDoc    BlackBoxCaseKnowledgeSupplementDoc `json:"knowledge_supplement_doc" zh:"知识补充文档"`
		FunctionPoint             string                             `json:"function_point" validate:"required" zh:"功能点"`
		GenerateOpinion           string                             `json:"generate_opinion" zh:"生成意见"`
		BlackBoxCaseBaseMap       BlackBoxCaseBaseMap                `json:"old_function_point_mind_data"  zh:"评审导图"`
	}
	UpdateBlackBoxCaseMapExperienceWithSseResp  {}
)

