syntax = "v1"

import "black_box_knowledge_term_types.api"

@server (
	prefix: ai/v2
	group: v2/blackboxknowledgeterm
    //timeout : 3000ms
)
service ai {
    @doc "list blackbox knowledge terms"
    @handler listBlackBoxKnowledgeTerms
    post /black/box/knowledge/terms (ListBlackBoxKnowledgeTermsReq) returns (ListBlackBoxKnowledgeTermsResp)

    @doc "create blackbox knowledge term"
    @handler createBlackBoxKnowledgeTerm
    post /black/box/knowledge/term (CreateBlackBoxKnowledgeTermReq) returns (CreateBlackBoxKnowledgeTermResp)

    @doc "update blackbox knowledge term"
    @handler updateBlackBoxKnowledgeTerm
    put /black/box/knowledge/term (UpdateBlackBoxKnowledgeTermReq) returns (UpdateBlackBoxKnowledgeTermResp)

    @doc "delete blackbox knowledge term"
    @handler deleteBlackBoxKnowledgeTerm
    delete /black/box/knowledge/term (DeleteBlackBoxKnowledgeTermReq) returns (DeleteBlackBoxKnowledgeTermResp)
}
