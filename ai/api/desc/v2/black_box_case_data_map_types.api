syntax = "v1"

import "../types.api"

type (
	GetBlackBoxCaseDataMapReq {
		ProjectId      string `form:"project_id" zh:"项目Id"`
		DirId          string `form:"dir_id" zh:"目录Id"`
		CaseId         string `form:"case_id" zh:"用例Id"`
		RevisionId     string `form:"revision_id" validate:"required" zh:"版本Id"`
		MindType       string `form:"mind_type" validate:"required" zh:"导图类型"`
		ParagraphTitle string `form:"paragraph_title" zh:"导图标题"`
	}
	GetBlackBoxCaseDataMapResp  {}
)

