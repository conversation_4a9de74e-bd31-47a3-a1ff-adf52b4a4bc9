syntax = "v1"

import "black_box_knowledge_tag_types.api"

@server (
	prefix: ai/v2
	group: v2/blackboxknowledgetag
    //timeout : 3000ms
)
service ai {
    @doc "list blackbox knowledge tags"
    @handler listBlackBoxKnowledgeTags
    get /black/box/knowledge/tags (ListBlackBoxKnowledgeTagsReq) returns (ListBlackBoxKnowledgeTagsResp)

    @doc "create blackbox knowledge tag"
    @handler createBlackBoxKnowledgeTag
    post /black/box/knowledge/tag (CreateBlackBoxKnowledgeTagReq) returns (CreateBlackBoxKnowledgeTagResp)

    @doc "update blackbox knowledge tag"
    @handler updateBlackBoxKnowledgeTag
    put /black/box/knowledge/tag (UpdateBlackBoxKnowledgeTagReq) returns (UpdateBlackBoxKnowledgeTagResp)

    @doc "delete blackbox knowledge tag"
    @handler deleteBlackBoxKnowledgeTag
    delete /black/box/knowledge/tag (DeleteBlackBoxKnowledgeTagReq) returns (DeleteBlackBoxKnowledgeTagResp)
}
