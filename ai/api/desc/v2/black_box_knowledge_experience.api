syntax = "v1"

import "black_box_knowledge_experience_types.api"

@server (
	prefix: ai/v2
	group: v2/blackboxknowledgeexperience
    //timeout : 3000ms
)
service ai {
    @doc "list blackbox knowledge experiences"
    @handler listBlackBoxKnowledgeExperiences
    post /black/box/knowledge/experiences (ListBlackBoxKnowledgeExperiencesReq) returns (ListBlackBoxKnowledgeExperiencesResp)

    @doc "create blackbox knowledge experience"
    @handler createBlackBoxKnowledgeExperience
    post /black/box/knowledge/experience (CreateBlackBoxKnowledgeExperienceReq) returns (CreateBlackBoxKnowledgeExperienceResp)

    @doc "update blackbox knowledge experience"
    @handler updateBlackBoxKnowledgeExperience
    put /black/box/knowledge/experience (UpdateBlackBoxKnowledgeExperienceReq) returns (UpdateBlackBoxKnowledgeExperienceResp)

    @doc "delete blackbox knowledge experience"
    @handler deleteBlackBoxKnowledgeExperience
    delete /black/box/knowledge/experience (DeleteBlackBoxKnowledgeExperienceReq) returns (DeleteBlackBoxKnowledgeExperienceResp)
}
