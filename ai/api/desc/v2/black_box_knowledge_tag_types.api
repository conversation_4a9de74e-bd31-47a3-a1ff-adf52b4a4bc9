syntax = "v1"

import "../types.api"

type (
	ListBlackBoxKnowledgeTagsReq {
		ProjectId string `form:"project_id" url:"project_id" validate:"required" zh:"项目Id"`
	}
	ListBlackBoxKnowledgeTagsResp {
		Items []*BlackBoxKnowledgeTag `json:"items"`
	}
	CreateBlackBoxKnowledgeTagReq {
		ProjectId string `json:"project_id" validate:"required" zh:"项目Id"`
		Tag       string `json:"tag" validate:"required" zh:"标签名称"`
	}
	CreateBlackBoxKnowledgeTagResp  {}
	UpdateBlackBoxKnowledgeTagReq {
		Id  int    `json:"id" validate:"required" zh:"标签Id"`
		Tag string `json:"tag" validate:"required" zh:"标签名称"`
	}
	UpdateBlackBoxKnowledgeTagResp  {}
	DeleteBlackBoxKnowledgeTagReq {
		Id int `json:"id" validate:"required" zh:"标签Id"`
	}
	DeleteBlackBoxKnowledgeTagResp  {}
)

