syntax = "v1"

import "../types.api"

type (
	ListBlackBoxKnowledgeExperiencesFilter {
		Tag      string `json:"tag" zh:"标签名称"`
		Category string `json:"category" zh:"经验类型名称"`
	}
	BlackBoxKnowledgeExperience {
		Id                 string                              `json:"id" validate:"required" zh:"经验Id"`
		ProjectId          string                              `json:"project_id" validate:"required" zh:"项目Id"`
		Category           BlackBoxKnowledgeExperienceCategory `json:"category" zh:"经验类型"`
		TestExperience     string                              `json:"test_experience" zh:"经验名称"`
		NormalFocusPoint   string                              `json:"normal_focus_point" zh:"正常经验"`
		AbnormalFocusPoint string                              `json:"abnormal_focus_point" zh:"异常经验"`
		Tags               []*BlackBoxKnowledgeTag             `json:"tags"`
	}
)

type (
	ListBlackBoxKnowledgeExperiencesReq {
		ProjectId string                                 `json:"project_id" validate:"required" zh:"项目Id"`
		Filter    ListBlackBoxKnowledgeExperiencesFilter `json:"filter,optional" zh:"标签筛选"`
		Search    string                                 `json:"search,optional" zh:"术语搜索"`
		Page      int                                    `json:"page,optional" zh:"页码"`
		Size      int                                    `json:"size,optional" zh:"页容量"`
	}
	ListBlackBoxKnowledgeExperiencesResp {
		Total int                            `json:"total,optional" zh:"总容量"`
		Data  []*BlackBoxKnowledgeExperience `json:"data"`
	}
	CreateBlackBoxKnowledgeExperienceReq {
		ProjectId          string                              `json:"project_id" validate:"required" zh:"项目Id"`
		Category           BlackBoxKnowledgeExperienceCategory `json:"category" validate:"required" zh:"经验类型"`
		TestExperience     string                              `json:"test_experience" validate:"required" zh:"经验名称"`
		NormalFocusPoint   string                              `json:"normal_focus_point" validate:"required" zh:"正常经验"`
		AbnormalFocusPoint string                              `json:"abnormal_focus_point" validate:"required" zh:"异常经验"`
		Tags               []*BlackBoxKnowledgeTag             `json:"tags"`
	}
	CreateBlackBoxKnowledgeExperienceResp  {}
	UpdateBlackBoxKnowledgeExperienceReq {
		Id                 string                              `json:"id" validate:"required" zh:"经验Id"`
		ProjectId          string                              `json:"project_id" validate:"required" zh:"项目Id"`
		Category           BlackBoxKnowledgeExperienceCategory `json:"category" validate:"required" zh:"经验类型"`
		TestExperience     string                              `json:"test_experience" validate:"required" zh:"经验名称"`
		NormalFocusPoint   string                              `json:"normal_focus_point" validate:"required" zh:"正常经验"`
		AbnormalFocusPoint string                              `json:"abnormal_focus_point" validate:"required" zh:"异常经验"`
		Tags               []*BlackBoxKnowledgeTag             `json:"tags"`
	}
	UpdateBlackBoxKnowledgeExperienceResp  {}
	DeleteBlackBoxKnowledgeExperienceReq {
		Id string `json:"id" validate:"required" zh:"经验Id"`
	}
	DeleteBlackBoxKnowledgeExperienceResp  {}
)

