syntax = "v1"

import "../types.api"

type (
    ListBlackBoxKnowledgeExperienceCategoriesReq {
        ProjectId string `form:"project_id" url:"project_id" validate:"required" zh:"项目Id"`
    }
    ListBlackBoxKnowledgeExperienceCategoriesResp {
        Items []*BlackBoxKnowledgeExperienceCategory `json:"items"`
    }

    CreateBlackBoxKnowledgeExperienceCategoryReq {
        ProjectId string `json:"project_id" validate:"required" zh:"项目Id"`
        TypeName  string `json:"type_name" validate:"required" zh:"经验类型名称"`
    }
    CreateBlackBoxKnowledgeExperienceCategoryResp {}

    DeleteBlackBoxKnowledgeExperienceCategoryReq {
        Id int `json:"id" validate:"required" zh:"经验类型Id"`
    }
    DeleteBlackBoxKnowledgeExperienceCategoryResp {}
)