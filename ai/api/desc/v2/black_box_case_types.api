syntax = "v1"

import "../types.api"
import "../black_box_case_types.api"

type (
	UpdateBlackBoxCaseItemV2Req {
		DirId                       string  `json:"dir_id" validate:"required" zh:"目录Id"`
		CaseId                      string  `json:"case_id" validate:"required" zh:"用例Id"`
		CaseName                    string  `json:"case_name,optional,omitempty" zh:"用例名称"`
		CaseContinueToWrite         int64   `json:"case_continue_to_write,optional,omitempty" zh:"用例续写次数"`
		CaseModelCharacter          string  `json:"case_model_character,optional,omitempty" zh:"用例模型性格"`
		CaseRemarks                 string  `json:"case_remarks,optional,omitempty" zh:"用例备注"`
		KnowledgeId                 string  `json:"knowledge_id,optional,omitempty" zh:"知识文档ID"`
		KnowledgeParagraphTitleId   int64   `json:"knowledge_paragraph_title_id,optional,omitempty" zh:"知识文档标题ID"`
		KnowledgeParagraphTitleText string  `json:"knowledge_paragraph_title_text,optional,omitempty" zh:"知识文档标题名词"`
		KnowledgeFixSugg			string  `json:"knowledge_fix_sugg,optional,omitempty" zh:"修改意见"`
		KnowledgeDocPgTitle []KnowledgeDocPgTitle `json:"knowledge_paragraph_title,optional,omitempty" zh:"新版文档标题""`
	}
)