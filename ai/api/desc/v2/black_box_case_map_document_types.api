syntax = "v1"

import "../types.api"

type (
	ListBlackBoxCaseMapDocumentsReq {
		ProjectId string `form:"project_id" zh:"项目Id"`
		DirId     string `form:"dir_id" zh:"目录Id"`
		CaseId    string `form:"case_id" validate:"required" zh:"用例Id"`
	}
	ListBlackBoxCaseMapDocumentsResp {}

	GetBlackBoxCaseMapDocumentReq {
		ProjectId string `form:"project_id" zh:"项目Id"`
		DirId     string `form:"dir_id" zh:"目录Id"`
		CaseId    string `form:"case_id" validate:"required" zh:"用例Id"`
		FuncId    string `form:"func_id" validate:"required" zh:"功能点Id"`
	}
	GetBlackBoxCaseMapDocumentResp {
		FuncDoc   *BlackBoxCaseKnowledgeSupplementDoc `json:"func_doc,optional" zh:"生成功能点文档"`
		ExpeDoc   *BlackBoxCaseKnowledgeSupplementDoc `json:"expe_doc,optional" zh:"生成测试经验文档"`
	}

	UpdateBlackBoxCaseMapDocumentReq {
		ProjectId string                              `json:"project_id" zh:"项目Id"`
		DirId     string                              `json:"dir_id" zh:"目录Id"`
		CaseId    string                              `json:"case_id" validate:"required" zh:"用例Id"`
		FuncId    string                              `json:"func_id" validate:"required" zh:"功能点Id"`
		FuncName  string                              `json:"func_name" validate:"required" zh:"功能点名称"`
		FuncDoc   *BlackBoxCaseKnowledgeSupplementDoc `json:"func_doc,optional" zh:"生成功能点文档"`
		ExpeDoc   *BlackBoxCaseKnowledgeSupplementDoc `json:"expe_doc,optional" zh:"生成测试经验文档"`
	}
	UpdateBlackBoxCaseMapDocumentResp {}

	DeleteBlackBoxCaseMapDocumentReq {
		ProjectId string `json:"project_id" zh:"项目Id"`
		DirId     string `json:"dir_id" zh:"目录Id"`
		CaseId    string `json:"case_id" validate:"required" zh:"用例Id"`
		FuncId    string `json:"func_id" validate:"required" zh:"功能点Id"`
	}
	DeleteBlackBoxCaseMapDocumentResp {}
)
