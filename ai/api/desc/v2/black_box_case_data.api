syntax = "v1"

import "black_box_case_data_types.api"

@server (
	prefix: ai/v2
	group: v2/blackboxcasedata
    //timeout : 3000ms
)
service ai {
    @doc "list blackbox case data"
    @handler listBlackBoxCaseData
    get /black/box/case/data (ListBlackBoxCaseDataV2Req) returns (ListBlackBoxCaseDataResp)

    @doc "create blackbox case data"
    @handler createBlackBoxCaseData
    post /black/box/case/data (CreateBlackBoxCaseDataV2Req) returns (ListBlackBoxCaseDataResp)

    @doc "create blackbox case data with sse"
    @handler createBlackBoxCaseDataWithSse
    post /black/box/case/data/create (CreateBlackBoxCaseDataV2WithSseReq) returns (CreateBlackBoxCaseDataV2WithSseResp)


    @doc "update blackbox case data"
    @handler updateBlackBoxCaseData
    put /black/box/case/data (UpdateBlackBoxCaseDataReq) returns (UpdateBlackBoxCaseDataResp)

    @doc "modify blackbox case data with sse"
    @handler modifyBlackBoxCaseDataWithSse
    post /black/box/case/data/modify (ModifyBlackBoxCaseDataWithSseReq) returns (ModifyBlackBoxCaseDataWithSseResp)

    @doc "delete blackbox case data"
    @handler deleteBlackBoxCaseData
    delete /black/box/case/data (DeleteBlackBoxCaseDataReq) returns (DeleteBlackBoxCaseDataResp)

    @doc "get blackbox case data coverage"
    @handler getBlackBoxCaseDataCoverage
    get /black/box/case/data/coverage/get (GetBlackBoxCaseDataCoverageReq) returns (GetBlackBoxCaseDataCoverageResp)

    @doc "set blackbox case data coverage"
    @handler confirmBlackBoxCaseDataCoverage
    post /black/box/case/data/coverage/set (SetBlackBoxCaseDataCoverageReq) returns (SetBlackBoxCaseDataCoverageResp)
}
