syntax = "v1"

import "../types.api"

type (
	GetBlackBoxCaseKnowledgeV2Req {
		ProjectId string `form:"project_id" zh:"项目Id"`
		DirId     string `form:"dir_id" zh:"目录Id"`
		CaseId    string `form:"case_id" validate:"required" zh:"用例Id"`
	}
	GetBlackBoxCaseKnowledgeV2Resp {
		Tags        []*string                          `json:"tags" zh:"术语标签"`
		Experiences []*BlackBoxCaseKnowledgeExperience `json:"experiences" zh:"测试经验"`
	}

	UpdateBlackBoxCaseKnowledgeV2Req{
		ProjectId   string                             `json:"project_id" validate:"required" zh:"项目Id"`
		DirId       string                             `json:"dir_id" validate:"required" zh:"目录Id"`
		CaseId      string                             `json:"case_id" validate:"required" zh:"用例Id"`
		Tags        []*string                          `json:"tags" zh:"术语标签"`
		Experiences []*BlackBoxCaseKnowledgeExperience `json:"experiences" zh:"测试经验"`
	}
	UpdateBlackBoxCaseKnowledgeV2Resp{}
)
