syntax = "v1"

import "black_box_case_knowledge_types.api"

@server (
	prefix: ai/v2
	group: v2/blackboxcaseknowledge
    //timeout : 3000ms
)
service ai {
    @doc "get blackbox case knowledge "
    @handler getBlackBoxCaseKnowledge
    get /black/box/case/knowledge (GetBlackBoxCaseKnowledgeV2Req) returns (GetBlackBoxCaseKnowledgeV2Resp)

    @doc "update blackbox case knowledge"
    @handler updateBlackBoxCaseKnowledge
    put /black/box/case/knowledge (UpdateBlackBoxCaseKnowledgeV2Req) returns (UpdateBlackBoxCaseKnowledgeV2Resp)
}
