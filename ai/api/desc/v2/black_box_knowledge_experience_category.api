syntax = "v1"

import "black_box_knowledge_experience_category_types.api"

@server (
	prefix: ai/v2
	group: v2/blackboxknowledgeexperiencecategory
    //timeout : 3000ms
)
service ai {
    @doc "list blackbox knowledge experience categories"
    @handler listBlackBoxKnowledgeExperienceCategories
    get /black/box/knowledge/experience/categories (ListBlackBoxKnowledgeExperienceCategoriesReq) returns (ListBlackBoxKnowledgeExperienceCategoriesResp)

    @doc "create blackbox knowledge experience category"
    @handler createBlackBoxKnowledgeExperienceCategory
    post /black/box/knowledge/experience/category (CreateBlackBoxKnowledgeExperienceCategoryReq) returns (CreateBlackBoxKnowledgeExperienceCategoryResp)

    @doc "delete blackbox knowledge experience category"
    @handler deleteBlackBoxKnowledgeExperienceCategory
    delete /black/box/knowledge/experience/category (DeleteBlackBoxKnowledgeExperienceCategoryReq) returns (DeleteBlackBoxKnowledgeExperienceCategoryResp)
}
