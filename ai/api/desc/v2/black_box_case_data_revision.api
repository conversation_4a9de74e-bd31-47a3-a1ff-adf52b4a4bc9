syntax = "v1"

import "black_box_case_data_revision_types.api"

@server (
	prefix: ai/v2
	group: v2/blackboxcasedatarevision
    //timeout : 3000ms
)
service ai {
    @doc "list blackbox case data revisions"
    @handler listBlackBoxCaseDataRevisions
    post /black/box/case/data/revisions (ListBlackBoxCaseRevisionReq) returns (ListBlackBoxCaseRevisionResp)

    @doc "create blackbox case data revision"
    @handler createBlackBoxCaseDataRevision
    post /black/box/case/data/revision (CreateBlackBoxCaseRevisionDataReq) returns (CreateBlackBoxCaseRevisionDataResp)

    @doc "get blackbox case data revision"
    @handler getBlackBoxCaseDataRevision
    get /black/box/case/data/revision (GetBlackBoxCaseDataRevisionReq) returns (BlackBoxCaseRevision)

    @doc "update blackbox case data revision"
    @handler updateBlackBoxCaseDataRevision
    put /black/box/case/data/revision (AppendBlackBoxCaseDataReq) returns (AppendBlackBoxCaseDataResp)
}
