syntax = "v1"

import "black_box_case_map_document_types.api"

@server (
	prefix: ai/v2
	group: v2/blackboxcasemapdocument
    //timeout : 3000ms
)
service ai {
    @doc "list blackbox case map documents"
    @handler listBlackBoxCaseMapDocuments
    get /black/box/case/map/documents (ListBlackBoxCaseMapDocumentsReq) returns (ListBlackBoxCaseMapDocumentsResp)

    @doc "get blackbox case map document"
    @handler getBlackBoxCaseMapDocument
    get /black/box/case/map/document (GetBlackBoxCaseMapDocumentReq) returns (GetBlackBoxCaseMapDocumentResp)

    @doc "update blackbox case map document"
    @handler updateBlackBoxCaseMapDocument
    put /black/box/case/map/document (UpdateBlackBoxCaseMapDocumentReq) returns (UpdateBlackBoxCaseMapDocumentResp)

    @doc "delete blackbox case map document"
    @handler deleteBlackBoxCaseMapDocument
    delete /black/box/case/map/document (DeleteBlackBoxCaseMapDocumentReq) returns (DeleteBlackBoxCaseMapDocumentResp)
}
