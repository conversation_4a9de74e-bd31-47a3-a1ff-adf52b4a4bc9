syntax = "v1"

import (
	"../types.api"
	"black_box_case_map_experience_types.api"
)

@server (
	prefix: ai/v2
	group:  v2/blackboxcasemapexperience
//timeout : 3000ms
)
service ai {
	@doc "create blackbox case map experience"
	@handler createBlackBoxCaseMapExperience
	post /black/box/case/map/experience (BlackBoxCaseMapSupplementRequest) returns (BlackBoxCaseMapNode)

	@doc "create blackbox case map experience with sse"
	@handler createBlackBoxCaseMapExperienceWithSse
	post /black/box/case/map/experience/create (BlackBoxCaseMapSupplementRequest) returns (CreateBlackBoxCaseMapExperienceWithSseResp)

	@doc "update blackbox case map experience with sse"
	@handler updateBlackBoxCaseMapExperienceWithSse
	post /black/box/case/map/experience/update (UpdateBlackBoxCaseMapExperienceWithSseRequset) returns (UpdateBlackBoxCaseMapExperienceWithSseResp)
}

