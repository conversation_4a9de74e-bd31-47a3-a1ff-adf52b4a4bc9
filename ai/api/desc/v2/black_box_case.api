syntax = "v1"

import "black_box_case_types.api"

@server (
	prefix: ai/v2
	group: v2/blackboxcase
    //timeout : 3000ms
)
service ai {
    @doc "list blackbox cases"
    @handler listBlackBoxCase
    post /black/box/cases (ListBlackBoxCaseReq) returns (ListBlackBoxCaseResp)

    @doc "create blackbox case"
    @handler createBlackBoxCase
    post /black/box/case (CreateBlackBoxCaseReq) returns (CreateBlackBoxCaseResp)

    @doc "get blackbox case"
    @handler getBlackBoxCase
    get /black/box/case (GetBlackBoxCaseReq) returns (GetBlackBoxCaseResp)

    @doc "update blackbox case"
    @handler updateBlackBoxCase
    put /black/box/case (UpdateBlackBoxCaseItemV2Req) returns (UpdateBlackBoxCaseItemResp)

    @doc "delete blackbox case"
    @handler deleteBlackBoxCase
    delete /black/box/case (DeleteBlackBoxCaseReq) returns (DeleteBlackBoxCaseResp)
}
