syntax = "v1"

import "../types.api"

type (
	CreateBlackBoxCaseMapReq {
		ProjectId string `json:"project_id" zh:"项目Id"`
		DirId     string `json:"dir_id" zh:"目录Id"`
		CaseId    string `json:"case_id" validate:"required" zh:"用例Id"`
	}
	//CreateBlackBoxCaseMapResp {}
	GetBlackBoxCaseMapReq {
		ProjectId string `form:"project_id" zh:"项目Id"`
		DirId     string `form:"dir_id" zh:"目录Id"`
		CaseId    string `form:"case_id" validate:"required" zh:"用例Id"`
	}
	//GetBlackBoxCaseMapResp {}
	UpdateBlackBoxCaseMapReq {
		ProjectId string              `json:"project_id" zh:"项目Id"`
		DirId     string              `json:"dir_id" zh:"目录Id"`
		CaseId    string              `json:"case_id" validate:"required" zh:"用例Id"`
		Data      BlackBoxCaseMapNode `json:"data" validate:"required" zh:"导图数据"`
	}
	UpdateBlackBoxCaseMapResp  {}
	CreateBlackBoxCaseMapWithSseReq {
		ProjectId string `json:"project_id" zh:"项目Id"`
		DirId     string `json:"dir_id" zh:"目录Id"`
		CaseId    string `json:"case_id" validate:"required" zh:"用例Id"`
	}
	CreateBlackBoxCaseMapWithSseResp  {}
	Data {
		Text string `json:"text" zh:"标题内容"`
	}
	BlackBoxCaseBaseMap {
		Id       string                `json:"id" zh:"Id"`
		Data     Data                  `json:"data" zh:"项目Id"`
		Children []BlackBoxCaseBaseMap `json:"children" zh:"子节点"`
	}
)

