syntax = "v1"

import "../types.api"

type (
	ListBlackBoxKnowledgeTermsFilter {
		Tag string `json:"tag" zh:"标签名称"`
	}
	BlackBoxKnowledgeTerm {
		Id            string                  `json:"id" validate:"required" zh:"术语Id"`
		ProjectId     string                  `json:"project_id" validate:"required" zh:"项目Id"`
		Tags          []*BlackBoxKnowledgeTag `json:"tags"`
		TermInChinese string                  `json:"term_in_chinese" validate:"required" zh:"术语名称"`
		TermMeaning   string                  `json:"term_meaning" validate:"required" zh:"术语含义"`
	}
)

type (
	ListBlackBoxKnowledgeTermsReq {
		ProjectId string                           `json:"project_id" validate:"required" zh:"项目Id"`
		Filter    ListBlackBoxKnowledgeTermsFilter `json:"filter,optional" zh:"标签筛选"`
		Search    string                           `json:"search,optional" zh:"术语搜索"`
		Page      int                              `json:"page,optional" zh:"页码"`
		Size      int                              `json:"size,optional" zh:"页容量"`
	}
	ListBlackBoxKnowledgeTermsResp {
		Total int                      `json:"total,optional" zh:"总容量"`
		Data  []*BlackBoxKnowledgeTerm `json:"data"`
	}
	CreateBlackBoxKnowledgeTermReq {
		ProjectId     string                  `json:"project_id" validate:"required" zh:"项目Id"`
		Tags          []*BlackBoxKnowledgeTag `json:"tags"`
		TermInChinese string                  `json:"term_in_chinese" validate:"required" zh:"术语名称"`
		TermMeaning   string                  `json:"term_meaning" validate:"required" zh:"术语含义"`
	}
	CreateBlackBoxKnowledgeTermResp  {}
	UpdateBlackBoxKnowledgeTermReq {
		Id            string                  `json:"id" validate:"required" zh:"术语Id"`
		ProjectId     string                  `json:"project_id" validate:"required" zh:"项目Id"`
		Tags          []*BlackBoxKnowledgeTag `json:"tags"`
		TermInChinese string                  `json:"term_in_chinese" validate:"required" zh:"术语名称"`
		TermMeaning   string                  `json:"term_meaning" validate:"required" zh:"术语含义"`
	}
	UpdateBlackBoxKnowledgeTermResp  {}
	DeleteBlackBoxKnowledgeTermReq {
		Id string `json:"id" validate:"required" zh:"术语Id"`
	}
	DeleteBlackBoxKnowledgeTermResp  {}
)

