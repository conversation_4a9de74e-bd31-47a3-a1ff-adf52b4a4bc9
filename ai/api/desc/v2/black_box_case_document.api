syntax = "v1"

import "../black_box_case_knowledge_types.api"

@server (
	prefix: ai/v2
	group: v2/blackboxcasedocument
    //timeout : 3000ms
)
service ai {
    @doc "list blackbox case documents"
    @handler listBlackBoxCaseDocuments
    post /black/box/case/documents (ListBlackBoxCaseKnowledgeReq) returns (ListBlackBoxCaseKnowledgeResp)

    @doc "create blackbox case document"
    @handler createBlackBoxCaseDocument
    post /black/box/case/document (CreateBlackBoxCaseKnowledgeReq) returns (CreateBlackBoxCaseKnowledgeResp)

    @doc "get blackbox case document"
    @handler getBlackBoxCaseDocument
    get /black/box/case/document (GetBlackBoxCaseKnowledgeReq) returns (GetBlackBoxCaseKnowledgeResp)

    @doc "update blackbox case document"
    @handler updateBlackBoxCaseDocument
    put /black/box/case/document (UpdateBlackBoxCaseKnowledgeReq) returns (UpdateBlackBoxCaseKnowledgeResp)

    @doc "reload blackbox case document"
    @handler reloadBlackBoxCaseDocument
    post /black/box/case/document/reload (ReloadBlackBoxCaseKnowledgeReq) returns (ReloadBlackBoxCaseKnowledgeResp)


    @doc "delete blackbox case document"
    @handler deleteBlackBoxCaseDocument
    delete /black/box/case/document (DeleteBlackBoxCaseKnowledgeReq) returns (DeleteBlackBoxCaseKnowledgeResp)
}
