syntax = "v1"

import "black_box_case_map_types.api"

@server (
	prefix: ai/v2
	group:  v2/blackboxcasemap
//timeout : 3000ms
)
service ai {
	@doc "create blackbox case map "
	@handler createBlackBoxCaseMap
	post /black/box/case/map (CreateBlackBoxCaseMapReq) returns (BlackBoxCaseMapNode)

	@doc "get blackbox case map "
	@handler getBlackBoxCaseMap
	get /black/box/case/map (GetBlackBoxCaseMapReq) returns (BlackBoxCaseMapNode)

	@doc "update blackbox case map"
	@handler updateBlackBoxCaseMap
	put /black/box/case/map (UpdateBlackBoxCaseMapReq) returns (UpdateBlackBoxCaseMapResp)

	@doc "create blackbox case map with sse"
	@handler createBlackBoxCaseMapWithSse
	post /black/box/case/map/create (CreateBlackBoxCaseMapWithSseReq) returns (CreateBlackBoxCaseMapWithSseResp)
}

