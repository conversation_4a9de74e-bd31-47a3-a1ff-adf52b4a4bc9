syntax = "v1"

import "../black_box_case_dir_types.api"

@server (
	prefix: ai/v2
	group: v2/blackboxcasedirectory
    //timeout : 3000ms
)
service ai {
    @doc "list blackbox case directories"
    @handler listBlackBoxCaseDirectories
    post /black/box/case/directories (ListBlackBoxCaseDirsReq) returns (ListBlackBoxCaseDirsResp)

    @doc "create blackbox case directory"
    @handler createBlackBoxCaseDirectory
    post /black/box/case/directory (CreateBlackBoxCaseDirReq) returns (CreateBlackBoxCaseDirResp)

    @doc "delete blackbox case directory"
    @handler deleteBlackBoxCaseDirectory
    delete /black/box/case/directory (DeleteBlackBoxCaseDirReq) returns (DeleteBlackBoxCaseDirResp)
}
