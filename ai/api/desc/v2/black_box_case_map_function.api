syntax = "v1"

import (
	"../types.api"
	"black_box_case_map_function_types.api"
)

@server (
	prefix: ai/v2
	group:  v2/blackboxcasemapfunction
//timeout : 3000ms
)
service ai {
	@doc "create blackbox case map function"
	@handler createBlackBoxCaseMapFunction
	post /black/box/case/map/function (BlackBoxCaseMapSupplementRequest) returns (BlackBoxCaseMapNode)

	@doc "create blackbox case map function with sse"
	@handler createBlackBoxCaseMapFunctionWithSse
	post /black/box/case/map/function/create (BlackBoxCaseMapSupplementRequest) returns (CreateBlackBoxCaseMapFunctionWithSseResp)
}

