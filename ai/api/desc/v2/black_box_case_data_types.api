syntax = "v1"

import "../black_box_case_data_types.api"

type (
	ListBlackBoxCaseDataV2Req {
		CaseId     string `form:"case_id,optional" zh:"用例Id"`
		RevisionId string `form:"revision_id,optional" zh:"版本Id"`
		Type       string `form:"type,optional" zh:"返回格式:markdown/json"`
	}
	CreateBlackBoxCaseDataV2Req {
		ProjectId  string  `json:"project_id" zh:"项目Id"`
		DirId      string  `json:"dir_id" zh:"目录Id"`
		CaseId     string  `json:"case_id" validate:"required" zh:"用例Id"`
		RevisionId *string `json:"revision_id,optional" zh:"版本Id"`
	//CaseModelCharacter string  `json:"case_model_character" zh:"用例模型性格"`
	//KnowledgeFixSugg   string  `json:"knowledge_fix_sugg" zh:"是否关联文档"`
	}
	//CreateBlackBoxCaseDataV2Resp {}
	CreateBlackBoxCaseDataV2WithSseReq {
		ProjectId  string  `json:"project_id" zh:"项目Id"`
		DirId      string  `json:"dir_id" zh:"目录Id"`
		CaseId     string  `json:"case_id" validate:"required" zh:"用例Id"`
		RevisionId *string `json:"revision_id,optional" zh:"版本Id"`
	}
	CreateBlackBoxCaseDataV2WithSseResp  {}
	ModifyBlackBoxCaseDataWithSseReq {
		ProjectId  string  `json:"project_id" zh:"项目Id"`
		DirId      string  `json:"dir_id" zh:"目录Id"`
		CaseId     string  `json:"case_id" validate:"required" zh:"用例Id"`
		RevisionId *string `json:"revision_id,optional" zh:"版本Id"`
	}
	ModifyBlackBoxCaseDataWithSseResp  {}
	GetBlackBoxCaseDataCoverageReq {
		ProjectId  string  `form:"project_id" zh:"项目Id"`
		DirId      string  `form:"dir_id" zh:"目录Id"`
		CaseId     string  `form:"case_id" validate:"required" zh:"用例Id"`
		RevisionId *string `form:"revision_id,optional" zh:"版本Id"`
	}
	GetBlackBoxCaseDataCoverageResp {
		Coverage int64 `json:"coverage,optional" zh:"覆盖率"`
	}
	SetBlackBoxCaseDataCoverageReq {
		ProjectId  string  `json:"project_id" zh:"项目Id"`
		DirId      string  `json:"dir_id" zh:"目录Id"`
		CaseId     string  `json:"case_id" validate:"required" zh:"用例Id"`
		RevisionId *string `json:"revision_id,optional" zh:"版本Id"`
		Coverage   int64   `json:"coverage,optional" zh:"覆盖率"`
	}
	SetBlackBoxCaseDataCoverageResp  {}
)

