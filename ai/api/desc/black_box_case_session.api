syntax = "v1"

import "black_box_case_session_types.api"

@server(
    prefix: ai/v1
    group: blackboxcasesession
    // timeout : 3000ms
)
service ai {
    @doc "create black box case session"
    @handler createBlackBoxCaseSession
    post /black/box/case/session/create (CreateBlackBoxCaseSessionReq) returns (CreateBlackBoxCaseSessionResp)

    @doc "update black box case session"
    @handler updateBlackBoxCaseSession
    put /black/box/case/session/update (UpdateBlackBoxCaseSessionReq) returns (UpdateBlackBoxCaseSessionResp)

    @doc "delete black box case session"
    @handler deleteBlackBoxCaseSession
    delete /black/box/case/session/delete (DeleteBlackBoxCaseSessionReq) returns (DeleteBlackBoxCaseSessionResp)

    @doc "search black box case session"
    @handler searchBlackBoxCaseSession
    post /black/box/case/session/search (SearchBlackBoxCaseSessionReq) returns (SearchBlackBoxCaseSessionResp)

    @doc "get black box case session"
    @handler getBlackBoxCaseSession
    get /black/box/case/session/get (GetBlackBoxCaseSessionReq) returns (GetBlackBoxCaseSessionResp)

    @doc "get black box case session history message list"
    @handler getBlackBoxCaseSessionHistoryMessageList
    get /black/box/case/session/history_message_list/get (GetBlackBoxCaseSessionHistoryMessageListReq) returns (GetBlackBoxCaseSessionHistoryMessageListResp)

    @doc "remove black box case session history message list"
    @handler removeBlackBoxCaseSessionHistoryMessageList
    delete /black/box/case/session/history_message_list/remove (RemoveBlackBoxCaseSessionHistoryMessageListReq) returns (RemoveBlackBoxCaseSessionHistoryMessageListResp)

    @doc "send black box case for session"
    @handler sendBlackBoxCaseForSession
    post /black/box/case/for_session/send (SendBlackBoxCaseForSessionReq) returns (SendBlackBoxCaseForSessionResp)

    @doc "get black box case conversation ai state"
    @handler getBlackBoxCaseConversationAIState
    get /black/box/case/conversation/ai/state/get (GetBlackBoxCaseConversationAIStateReq) returns (GetBlackBoxCaseConversationAIStateResp)

    @doc "get black box case conversation ai message"
    @handler getBlackBoxCaseConversationAIMessage
    get /black/box/case/conversation/ai/message/get (GetBlackBoxCaseConversationAIMessageReq) returns (GetBlackBoxCaseConversationAIMessageResp)

    @doc "get black box case modify reference content list"
    @handler getBlackBoxCaseModifyTestCaseReferenceContentList
    get /black/box/case/modify/reference/content/list/get (GetBlackBoxCaseModifyTestCaseReferenceContentListReq) returns (GetBlackBoxCaseModifyTestCaseReferenceContentListResp)

    @doc "create black box case modify reference content"
    @handler createBlackBoxCaseModifyTestCaseReferenceContent
    post /black/box/case/modify/reference/content/create (CreateBlackBoxCaseModifyTestCaseReferenceContentReq) returns (CreateBlackBoxCaseModifyTestCaseReferenceContentResp)

    @doc "replace black box case"
    @handler replaceBlackBoxCase
    post /black/box/case/replace (ReplaceBlackBoxCaseReq) returns (ReplaceBlackBoxCaseResp)

    @doc "merge black box case"
    @handler mergeBlackBoxCase
    post /black/box/case/merge (MergeBlackBoxCaseReq) returns (MergeBlackBoxCaseResp)

    @doc "transfer black box case"
    @handler transferBlackBoxCase
    post /black/box/case/transfer (TransferBlackBoxCase2XMindReq) returns (TransferBlackBoxCase2XMindResp)

    @doc "get black box case ai message"
    @handler getCreateBlackBoxCaseAIMessage
    get /black/box/case/ai/message/get (GetCreateBlackBoxCaseAIMessageReq) returns (GetCreateBlackBoxCaseAIMessageResp)

}
