syntax = "v1"

import "types.api"

type BlackBoxCaseDir {
    Id int `json:"id" zh:"自增Id"`
    ProjectId string `json:"project_id" zh:"项目Id"`
    DirId string `json:"dir_id" zh:"目录Id"`
    DirName string `json:"dir_name" zh:"目录名称"`
    DirDescription string `json:"dir_description" zh:"目录名称"`
    CreatedBy *FullUserInfo `json:"created_by" zh:"创建者的用户Id"`
    UpdatedBy *FullUserInfo `json:"updated_by" zh:"最近一次更新者的用户Id"`
    DeletedBy *FullUserInfo `json:"deleted_by" zh:"删除者的用户Id"`
    CreatedAt int64 `json:"created_at" zh:"创建时间"`
    UpdatedAt int64 `json:"updated_at" zh:"更新时间"`
    DeletedAt int64 `json:"deleted_at" zh:"删除时间"`
}

type (
    ListBlackBoxCaseDirsReq {
        ProjectId string `json:"project_id" validate:"required" zh:"项目Id"`
    }
    ListBlackBoxCaseDirsResp {
        items []*BlackBoxCaseDir `json:"items"`
    }

    DeleteBlackBoxCaseDirReq {
        ProjectId string `json:"project_id" validate:"required" zh:"项目Id"`
        DirId     string `json:"dir_id" validate:"required" zh:"目录Id"`
    }
    DeleteBlackBoxCaseDirResp {}

    GetBlackBoxCaseDirReq {
        ProjectId string `form:"project_id" validate:"required" zh:"项目Id"`
        DirId     string `form:"dir_id" validate:"required" zh:"目录Id"`
    }
    GetBlackBoxCaseDirResp {
        item *BlackBoxCaseDir `json:"item"`
    }

    CreateBlackBoxCaseDirReq {
        ProjectId string `json:"project_id" validate:"required" zh:"项目Id"`
        DirName string `json:"dir_name" validate:"required" zh:"目录名称"`
        // DirDescription string `json:"dir_description" zh:"目录描述"`
    }
    CreateBlackBoxCaseDirResp {
        DirId string `json:"dir_id" zh:"目录Id"`
    }

    UpdateBlackBoxCaseDirReq {
        ProjectId string `json:"project_id" validate:"required" zh:"项目Id"`
        DirId     string `json:"dir_id" validate:"required" zh:"目录Id"`
        DirName   string `json:"dir_name" validate:"required" zh:"目录名称"`
        // DirDescription string `json:"dir_description" zh:"目录描述"`
    }
    UpdateBlackBoxCaseDirResp {}
)
