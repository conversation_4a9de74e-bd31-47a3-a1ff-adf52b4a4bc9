syntax = "v1"

import "black_box_case_dir_types.api"

@server (
	prefix: ai/v1
	group: blackboxcasedir
    //timeout : 3000ms
)
service ai {
    @doc "list black box dirs"
    @handler listBlackBoxCaseDirs
    post /black/box/case/dir/list (ListBlackBoxCaseDirsReq) returns (ListBlackBoxCaseDirsResp)

    @handler getBlackBoxCaseDir
    get /black/box/case/dir (GetBlackBoxCaseDirReq) returns (GetBlackBoxCaseDirResp)

    @handler createBlackBoxCaseDir
    post /black/box/case/dir (CreateBlackBoxCaseDirReq) returns (CreateBlackBoxCaseDirResp)

    @handler deleteBlackBoxCaseDir
    delete /black/box/case/dir (DeleteBlackBoxCaseDirReq) returns (DeleteBlackBoxCaseDirResp)

    @handler updateBlackBoxCaseDir
    put /black/box/case/dir (UpdateBlackBoxCaseDirReq) returns (UpdateBlackBoxCaseDirResp)
}
