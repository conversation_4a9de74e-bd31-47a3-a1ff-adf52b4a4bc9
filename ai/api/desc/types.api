syntax = "v1"

type Condition {
	Single *SingleCondition `json:"single,omitempty,optional"`
	Group  *GroupCondition  `json:"group,omitempty,optional"`
}

type GroupCondition {
	Relationship string       `json:"relationship" validate:"oneof=AND OR"`
	Conditions   []*Condition `json:"conditions"`
}

type Between {
	Start string `json:"start"`
	End   string `json:"end"`
}

type Other {
	Value string `json:"value"`
}

type SingleCondition {
	Field   string   `json:"field" validate:"required"`
	Compare string   `json:"compare,default=EQ" validate:"oneof=EQ NE LT LE GT GE LIKE IN BETWEEN"`
	In      []string `json:"in,omitempty,optional"`
	Between *Between `json:"between,omitempty,optional"`
	Other   *Other   `json:"other,omitempty,optional"`
}

type Pagination {
	CurrentPage uint64 `json:"current_page,default=1" validate:"gte=1"`
	PageSize    uint64 `json:"page_size,default=10" validate:"gte=1"`
}

type SortField {
	Field string `json:"field" validate:"required"`
	Order string `json:"order,default=ASC"`
}

type FullUserInfo {
	Account      string `json:"account"`
	Fullname     string `json:"fullname"`
	DeptId       string `json:"dept_id"`
	DeptName     string `json:"dept_name"`
	FullDeptName string `json:"full_dept_name"`
	Email        string `json:"email"`
	Mobile       string `json:"mobile"`
	Photo        string `json:"photo"`
	Enabled      bool   `json:"enabled"`
}

type BlackBoxCaseDataBase {
	CaseId       string `json:"case_id,optional" zh:"用例Id"`
	RevisionId   string `json:"revision_id,optional" zh:"版本Id"`
	CaseDataId   string `json:"case_data_id" zh:"知识文档Id"`
	OrderId      string `json:"order_id,optional" zh:"序号"`
	CaseName     string `json:"case_name,optional" zh:"用例名称"`
	Requirement  string `json:"requirement,optional" zh:"需求名称"`
	PreCondition string `json:"pre_condition,optional" zh:"前置条件"`
	CaseStep     string `json:"case_step,optional" zh:"用例步骤"`
	ExpectResult string `json:"expect_result,optional" zh:"预期结果"`
	Terminal     string `json:"terminal,optional" zh:"终端"`
	CaseLevel    string `json:"case_level,optional" zh:"用例等级"`
	Tag          string `json:"tag,optional" zh:"标识"`
}

type ReferenceDoc {
	Type   string `json:"type,optional" zh:"类型"`
	Feishu Feishu `json:"feishu,optional" zh:"飞书"`
	Text   Text   `json:"text,optional" zh:"文本"`
}

type Feishu {
	KnowledgeId                string                        `json:"knowledge_id,optional" zh:"知识文档Id"`
	KnowledgeParagraphTitle    []*string                        `json:"knowledge_paragraph_title,optional" zh:"文档使用标题"`
	KnowledgeParagraphTitleMap []*KnowledgeParagraphTitleMap `json:"knowledge_paragraph_title_map,optional" zh:"文档使用标题"`
}

type KnowledgeParagraphTitleMap {
	Id   int64  `json:"id,optional,omitempty" zh:"飞书标题id"`
	Text string `json:"text,optional,omitempty" zh:"飞书标题内容"`
}

type Text {
	Content string `json:"content,optional"`
}

type (
	NoArgs {
	}
)

// v2
type (
	BlackBoxKnowledgeTag {
		Id int  `json:"id" validate:"required" zh:"标签Id"`
		Tag string `json:"tag" validate:"required" zh:"标签名称"`
	}

	BlackBoxKnowledgeExperienceCategory {
        Id       int    `json:"id" validate:"required" zh:"场景类型Id"`
        TypeName string `json:"type_name" validate:"required" zh:"场景类型名称"`
    }

	BlackBoxCaseKnowledgeExperience {
		Id                 string    `json:"id" validate:"required" zh:"测试经验Id"`
		ProjectId          string    `json:"project_id" zh:"项目Id"`
		Project            string    `json:"project" zh:"项目名称"`
		Category           string    `json:"category" zh:"经验分类"`
		Tags               []*string `json:"tags" zh:"经验标签"`
		TestExperience     string    `json:"test_experience" zh:"经验名称"`
		NormalFocusPoint   string    `json:"normal_focus_point" zh:"正常场景"`
		AbnormalFocusPoint string    `json:"abnormal_focus_point" zh:"异常场景"`
	}

	BlackBoxCaseMapNode {
		ID       string                  `json:"id" validate:"required" zh:"节点Id"`
		Data     BlackBoxCaseMapNodeData `json:"data" validate:"required" zh:"节点数据"`
		Children []*BlackBoxCaseMapNode  `json:"children" zh:"子节点内容"`
	}
	BlackBoxCaseMapNodeData {
		Text string `json:"text" validate:"required" zh:"节点内容"`
	}

	BlackBoxCaseKnowledgeSupplementDoc {
		Type     string   `json:"type,optional" zh:"类型"`
		Feishu   FeishuV2 `json:"feishu,optional" zh:"飞书"`
		Markdown Markdown `json:"markdown,optional" zh:"Markdown"`
	}
	FeishuV2 {
		KnowledgeDocId      string                    `json:"knowledge_doc_id,optional" zh:"知识文档Id"`
		KnowledgeDocPgTitle []*KnowledgeDocPgTitleMap `json:"knowledge_doc_pg_title" zh:"文档使用标题"`
	}
	KnowledgeDocPgTitleMap {
		Id    int64  `json:"id,optional,omitempty" zh:"飞书文档标题id"`
		Title string `json:"title,optional,omitempty" zh:"飞书文档标题内容"`
	}
	Markdown {
		Text string `json:"text,optional"`
	}

	BlackBoxCaseMapSupplementRequest {
		ProjectId 				  string        					 `json:"project_id" zh:"项目Id"`
		DirId     				  string        					 `json:"dir_id" zh:"目录Id"`
		CaseId    				  string        		 			 `json:"case_id" validate:"required" zh:"用例Id"`
		KnowledgeSupplementEnable bool                               `json:"knowledge_supplement_enable" zh:"是否开启补充文档"`
		KnowledgeSupplementDoc    BlackBoxCaseKnowledgeSupplementDoc `json:"knowledge_supplement_doc" zh:"知识补充文档"`
		FunctionPoint             string                             `json:"function_point" validate:"required" zh:"功能点"`
		GenerateOpinion           string                             `json:"generate_opinion" zh:"生成意见"`
	}
)
    