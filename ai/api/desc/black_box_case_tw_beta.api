syntax = "v1"

import "black_box_case_tw_beta_types.api"

@server (
    prefix: ai/v1
    group: blackboxcasetwbeta
)

service ai {
    @doc "list black box case of tw beta"
    @handler listBlackBoxCaseTwBeta
    post /black/box/case/beta-v1/list (ListBlackBoxCaseTwBetaReq) returns (ListBlackBoxCaseTwBetaResp)

    @doc "create black box case of tw beta mind"
    @handler createBlackBoxCaseTwBetaMind
    post /black/box/case/beta-v1/mind (CreateBlackBoxCaseTwBetaMindReq) returns (CreateBlackBoxCaseTwBetaMindResp)

    @doc "list black box case of tw beta mind"
    @handler listBlackBoxCaseTwBetaMind
    post /black/box/case/beta-v1/mind/list (ListBlackBoxCaseTwBetaMindReq) returns (ListBlackBoxCaseTwBetaMindResp)

    @doc "get black box case of tw beta mind content"
    @handler getBlackBoxCaseTwBetaMindContent
    post /black/box/case/beta-v1/mind/content (GetBlackBoxCaseTwBetaMindContentReq) returns (GetBlackBoxCaseTwBetaMindContentResp)

    @doc "list black box case of tw beta mind check"
    @handler listBlackBoxCaseTwBetaMindCheck
    post /black/box/case/beta-v1/check/list (ListBlackBoxCaseTwBetaMindCheckReq) returns (ListBlackBoxCaseTwBetaMindCheckResp)

    @doc "delete black box case of tw beta"
    @handler deleteBlackBoxCaseTwBeta
    post /black/box/case/beta-v1 (DeleteBlackBoxCaseTwBetaReq) returns (DeleteBlackBoxCaseTwBetaResp)

    @doc "create black box tw test case"
    @handler createBlackBoxCaseTwBeta
    post /black/box/case/beta-v1/create (CreateBlackBoxCaseTwBetaReq) returns (CreateBlackBoxCaseTwBetaResp)

    @doc "update black box tw mind content"
    @handler updateBlackBoxCaseTwBetaMindContent
    post /black/box/case/beta-v1/mind/content/update (UpdateBlackBoxCaseMindTwBetaReq) returns (UpdateBlackBoxCaseMindTwBetaResp)


    @doc "get black box tw case content"
    @handler getBlackBoxCaseTwBetaCaseContent
    post /black/box/case/beta-v1/case/content (GetBlackBoxCaseTwBetaContentReq) returns (GetBlackBoxCaseTwBetaContentResp)
}