syntax = "v1"

import "types.api"
import "black_box_case_document_types.api"

type BlackBoxCaseAssistant {
    Id int `json:"id" zh:"自增Id"`
    ProjectId string `json:"project_id" zh:"项目Id"`
    AssistantId string `json:"assistant_id" zh:"助手Id"`
    AssistantName string `json:"assistant_name" zh:"助手名称"`
    AssistantDescription string `json:"assistant_description" zh:"助手描述"`
    Account string `json:"account" zh:"用户id【仅在个人空间存在此数据】"`
    WorkSpaceType int8 `json:"work_space_type" zh:"工作空间类型：个人空间(private)：0，公共空间()：1"`
    WorkPriorityType int8 `json:"work_priority_type" zh:"工作优先级类型：准确优先：0，速度优先：1"`
    SessionRound int `json:"session_round" zh:"对话轮数"`
    //DocumentIds string `json:"document_ids" zh:"文档集合(待保留)"`
    BlackBoxCaseDocumentList []*BlackBoxCaseDocument `json:"black_box_case_document_list" zh:"文档集合"`
    CreatedBy *FullUserInfo `json:"created_by" zh:"创建者的用户Id"`
    UpdatedBy *FullUserInfo `json:"updated_by" zh:"最近一次更新者的用户Id"`
    DeletedBy *FullUserInfo `json:"deleted_by" zh:"删除者的用户Id"`
    CreatedAt int64 `json:"created_at" zh:"创建时间"`
    UpdatedAt int64 `json:"updated_at" zh:"更新时间"`
    DeletedAt int64 `json:"deleted_at" zh:"删除时间"`
}

// 查询黑盒用例助手
type (
    SearchBlackBoxCaseAssistantReq {
        ProjectId string `json:"project_id" validate:"required" zh:"项目Id"`
        //Condition *Condition `json:"condition,omitempty,optional" zh:"查询条件"`
        //Pagination *Pagination `json:"pagination,omitempty,optional" zh:"查询分页"`
        //Sort []*SortField `json:"sort,omitempty,optional" zh:"查询排序"`
    }
    SearchBlackBoxCaseAssistantResp {
        Items []*BlackBoxCaseAssistant `json:"items"`
    }
)
type (
    UpdateBlackBoxCaseAssistantReq {
        ProjectId string `json:"project_id" validate:"required" zh:"项目Id"`
        AssistantId string `json:"assistant_id" validate:"required" zh:"助手Id"`
        AssistantName string `json:"assistant_name" validate:"required" zh:"助手名称"`
        AssistantDescription string `json:"assistant_description" zh:"助手描述"`
        WorkSpaceType int8 `json:"work_space_type,optional" validate:"omitempty,oneof=0 1" zh:"工作空间类型：个人空间(private)：0，公共空间()：1"`
        WorkPriorityType int8 `json:"work_priority_type,optional" validate:"omitempty,oneof=0 1" zh:"工作优先级类型：准确优先：0，速度优先：1"`
        SessionRound int `json:"session_round" validate:"required" zh:"对话轮数"`
    }
    UpdateBlackBoxCaseAssistantResp {
    }
)
type (
    DeleteBlackBoxCaseAssistantReq {
        ProjectId string `form:"project_id" validate:"required" zh:"项目Id"`
        AssistantId string `form:"assistant_id" validate:"required" zh:"助手Id"`
    }
    DeleteBlackBoxCaseAssistantResp {
    }
)

type (
    GetBlackBoxCaseAssistantReq {
        ProjectId string `form:"project_id" validate:"required" zh:"项目Id"`
        AssistantId string `form:"assistant_id" validate:"required" zh:"助手Id"`
    }
    GetBlackBoxCaseAssistantResp {
        item *BlackBoxCaseAssistant `json:"item"`
    }
)

type (
    CreateBlackBoxCaseAssistantReq {
        ProjectId string `json:"project_id" validate:"required" zh:"项目Id"`
        // AssistantId string `json:"assistant_id" validate:"required" zh:"助手Id"`
        AssistantName string `json:"assistant_name" validate:"required" zh:"助手名称"`
        AssistantDescription string `json:"assistant_description" zh:"助手描述"`
        WorkSpaceType int8 `json:"work_space_type,optional" validate:"omitempty,oneof=0 1" zh:"工作空间类型：个人空间(private)：0，公共空间()：1"`
        WorkPriorityType int8 `json:"work_priority_type,optional" validate:"omitempty,oneof=0 1" zh:"工作优先级类型：准确优先：0，速度优先：1"`
        SessionRound int `json:"session_round" validate:"required" zh:"对话轮数"`
    }
    CreateBlackBoxCaseAssistantResp {
        ProjectId string `json:"project_id" zh:"项目Id"`
        AssistantId string `json:"assistant_id" zh:"助手Id"`
    }
)
