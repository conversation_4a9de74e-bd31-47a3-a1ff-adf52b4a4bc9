syntax = "v1"

import "types.api"

type BlackBoxCaseData {
	Id           int           `json:"id" zh:"自增Id"`
	CaseId       string        `json:"case_id,optional" zh:"用例Id"`
	RevisionId   string        `json:"revision_id" zh:"版本Id"`
	CaseDataId   string        `json:"case_data_id" zh:"知识文档Id"`
	OrderId      string        `json:"order_id,optional" zh:"序号"`
	CaseName     string        `json:"case_name,optional" zh:"用例名称"`
	Requirement  string        `json:"requirement,optional" zh:"需求名称"`
	PreCondition string        `json:"pre_condition,optional" zh:"前置条件"`
	CaseStep     string        `json:"case_step,optional" zh:"用例步骤"`
	ExpectResult string        `json:"expect_result,optional" zh:"预期结果"`
	Terminal     string        `json:"terminal,optional" zh:"终端"`
	CaseLevel    string        `json:"case_level,optional" zh:"用例等级"`
	Tag          string        `json:"tag,optional" zh:"标识"`
	IsKeep       bool          `json:"is_keep,optional" zh:"是否保留"`
	CreatedBy    *FullUserInfo `json:"created_by,optional" zh:"创建者的用户Id"`
	UpdatedBy    *FullUserInfo `json:"updated_by,optional" zh:"最近一次更新者的用户Id"`
	DeletedBy    *FullUserInfo `json:"deleted_by,optional" zh:"删除者的用户Id"`
	CreatedAt    int64         `json:"created_at,optional" zh:"创建时间"`
	UpdatedAt    int64         `json:"updated_at,optional" zh:"更新时间"`
	DeletedAt    int64         `json:"deleted_at,optional" zh:"删除时间"`
}

type (
	EditBlackBoxCaseDataReq {
		CaseId                      string       `json:"case_id" zh:"用例Id"`
		RevisionId                  string       `json:"revision_id" zh:"版本Id"`
		KnowledgeId                 string       `json:"knowledge_id" zh:"知识文档Id"`
		KnowledgeParagraphTitleText string       `json:"knowledge_paragraph_title_text" zh:"文档使用标题"`
		KnowledgeParagraphTitleId   int64        `json:"knowledge_paragraph_title_id" zh:"文档使用标题id"`
		KnowledgeFixSugg            string       `json:"knowledge_fix_sugg" zh:"修改意见"`
		EnableReference             bool         `json:"enable_reference" zh:"是否启用参考文档"`
		ReferenceDoc                ReferenceDoc `json:"reference_doc"`
	}
	EditBlackBoxCaseDataResp {
	}

	GetEditBlackBoxCaseDataProgressReq {
		CaseRefId string `form:"case_ref_id" zh:"用例数据生成ID"`
	}
	GetEditBlackBoxCaseDataProgressResp {
	}

	PartialEditBlackBoxCaseDataReq {
		CaseId                      string              `json:"case_id" zh:"用例Id"`
		RevisionId                  string              `json:"revision_id" zh:"版本Id"`
		KnowledgeId                 string              `json:"knowledge_id" zh:"知识文档Id"`
		KnowledgeParagraphTitleText string              `json:"knowledge_paragraph_title_text" zh:"文档使用标题"`
		KnowledgeParagraphTitleId   int64               `json:"knowledge_paragraph_title_id" zh:"文档使用标题id"`
		KnowledgeFixSugg            string              `json:"knowledge_fix_sugg" zh:"修改意见"`
		EnableReference             bool                `json:"enable_reference" zh:"是否启用参考文档"`
		ReferenceDoc                ReferenceDoc        `json:"reference_doc"`
		Items                       []*BlackBoxCaseData `json:"items"`
	}
	PartialEditBlackBoxCaseDataResp {
	}

	ListBlackBoxCaseDataReq {
		CaseId     string `json:"case_id,optional" zh:"用例Id"`
		RevisionId string `json:"revision_id,optional" zh:"版本Id"`
		Type       string `json:"type,optional" zh:"返回格式:markdown/json"`
	}
	ListBlackBoxCaseDataResp {
		Items   []*BlackBoxCaseData `json:"items"`
		Content string              `json:"Content"`
	}

	DeleteBlackBoxCaseDataReq {
		CaseDataId string `form:"case_data_id" zh:"知识文档Id"`
	}
	DeleteBlackBoxCaseDataResp {}

	CreateBlackBoxCaseData {
		RevisionId   string `json:"revision_id" zh:"版本Id"`
		OrderId      string `json:"order_id,optional" zh:"序号"`
		CaseName     string `json:"case_name,optional" zh:"用例名称"`
		Requirement  string `json:"requirement,optional" zh:"需求名称"`
		PreCondition string `json:"pre_condition,optional" zh:"前置条件"`
		CaseStep     string `json:"case_step,optional" zh:"用例步骤"`
		ExpectResult string `json:"expect_result,optional" zh:"预期结果"`
		Terminal     string `json:"terminal,optional" zh:"终端"`
		CaseLevel    string `json:"case_level,optional" zh:"用例等级"`
		Tag          string `json:"tag,optional" zh:"标识"`
	}
	CreateBlackBoxCaseDataReq {
		Items []*CreateBlackBoxCaseData `json:"items"`
	}
	CreateBlackBoxCaseDataResp {}

	GenerateBlackBoxCaseDataReq {
		CaseId                      string `json:"case_id" zh:"用例Id"`
		KnowledgeId                 string `json:"knowledge_id" zh:"知识文档Id"`
		KnowledgeParagraphTitleText string `json:"knowledge_paragraph_title_text" zh:"文档使用标题"`
		KnowledgeParagraphTitleID   int64  `json:"knowledge_paragraph_title_id" zh:"文档使用标题id"`
	}
	GenerateBlackBoxCaseDataResp {}

	UpdateBlackBoxCaseDataReq {
		CaseDataId   string `json:"case_data_id" zh:"知识文档Id"`
		OrderId      string `json:"order_id,optional" zh:"序号"`
		CaseName     string `json:"case_name,optional" zh:"用例名称"`
		Requirement  string `json:"requirement,optional" zh:"需求名称"`
		PreCondition string `json:"pre_condition,optional" zh:"前置条件"`
		CaseStep     string `json:"case_step,optional" zh:"用例步骤"`
		ExpectResult string `json:"expect_result,optional" zh:"预期结果"`
		Terminal     string `json:"terminal,optional" zh:"终端"`
		CaseLevel    string `json:"case_level,optional" zh:"用例等级"`
		Tag          string `json:"tag,optional" zh:"标识"`
		IsKeep       bool   `json:"is_keep,optional" zh:"是否保留"`
	    EditedField  string `json:"edited_field,optional" zh:"修改的字段"`
	}
	UpdateBlackBoxCaseDataResp {}

	UpdateBlackBoxCaseDataOrderReq {
		RevisionId string `json:"revision_id"`
	}
	UpdateBlackBoxCaseDataOrderResp {}

	BatchUpdateBlackBoxCaseDataReq {
		Items []*UpdateBlackBoxCaseDataReq `json:"items"`
	}
	BatchUpdateBlackBoxCaseDataResp {}

	KeepBlackBoxCaseDataReq {
		CaseDataId string `json:"case_data_id" zh:"用例数据ID"`
		Action     string `json:"action" zh:"是否保留(keep/cancel)"`
	}
	KeepBlackBoxCaseDataResp {}

	ClearKeepBlackBoxCaseDataReq {
		CaseId string `json:"case_id" zh:"用例Id"`
	}
	ClearKeepBlackBoxCaseDataResp {}

	AppendBlackBoxCaseDataReq {
		RevisionId string                  `json:"revision_id,optional" zh:"版本Id"`
		Items      []*BlackBoxCaseDataBase `json:"items"`
	}
	AppendBlackBoxCaseDataResp {
		Items    []*BlackBoxCaseData `json:"items"`
		NewItems []*BlackBoxCaseData `json:"new_items"`
	}

	ReplaceBlackBoxCaseDataReq {
		RevisionId string                  `json:"revision_id" zh:"版本Id"`
		Items      []*BlackBoxCaseDataBase `json:"items"`
		NewItems   []*BlackBoxCaseData     `json:"new_items"`
	}
	ReplaceBlackBoxCaseDataResp {
		Items    []*BlackBoxCaseData `json:"items"`
		NewItems []*BlackBoxCaseData `json:"new_items"`
	}

	TransBlackBoxCaseDataReq {
		RevisionId string `json:"revision_id" zh:"版本ID"`
	}
	TransBlackBoxCaseDataResp {
		Content string `json:"content" zh:"内容"`
	}

	GetBlackBoxCaseAIStateReq {
		CaseRefId string `form:"case_ref_id" zh:"版本ID"`
	}
	GetBlackBoxCaseAIStateResp {
		State          string `json:"state" zh:"状态"`
		StateDesc      string `json:"state_desc" zh:"状态描述"`
		ExtractContent string `json:"extract_content" zh:"内容"`
	}

	GetBlackBoxCaseRefJsonReq {
		CaseRefId string `form:"case_ref_id" zh:"版本ID"`
	}
	GetBlackBoxCaseRefResp {
		Items []*BlackBoxCaseData `json:"items"`
	}

	ReorderBlackBoxCaseDataReq {
		RevisionId string `json:"revision_id,optional" zh:"版本Id"`
	}
	ReorderBlackBoxCaseDataResp {}

	MetricsMigreation {}
)