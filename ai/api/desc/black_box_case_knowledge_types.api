syntax = "v1"

import (
	"types.api"
 	"black_box_case_types.api"
	)

type BlackBoxCaseKnowledge {
	Id                      int           `json:"id" zh:"自增Id"`
	DirId                   string        `json:"dir_id" zh:"目录Id"`
	KnowledgeId             string        `json:"knowledge_id" zh:"知识文档Id"`
	KnowledgeName           string        `json:"knowledge_name" zh:"文档名称"`
	KnowledgeType           string        `json:"knowledge_type" zh:"文档类型"`
	KnowledgeContent        string        `json:"knowledge_content" zh:"文档内容"`
	KnowledgeStatus         string        `json:"knowledge_status" zh:"文档状态"`
	KnowledgeParagraphTitle string        `json:"knowledge_paragraph_title" zh:"文档标题"`
	KnowledgeErrorMsg       string        `json:"knowledge_error_msg" zh:"文档标题"`
	CreatedBy               *FullUserInfo `json:"created_by" zh:"创建者的用户Id"`
	UpdatedBy               *FullUserInfo `json:"updated_by" zh:"最近一次更新者的用户Id"`
	DeletedBy               *FullUserInfo `json:"deleted_by" zh:"删除者的用户Id"`
	CreatedAt               int64         `json:"created_at" zh:"创建时间"`
	UpdatedAt               int64         `json:"updated_at" zh:"更新时间"`
	DeletedAt               int64         `json:"deleted_at" zh:"删除时间"`
}

type (
	ListBlackBoxCaseKnowledgeReq {
		DirId string `json:"dir_id" validate:"required" zh:"目录Id"`
	}
	ListBlackBoxCaseKnowledgeResp {
		Items []*BlackBoxCaseKnowledge `json:"items"`
	}

	DeleteBlackBoxCaseKnowledgeReq {
		KnowledgeId string `json:"knowledge_id" validate:"required" zh:"文档ID"`
	}
	DeleteBlackBoxCaseKnowledgeResp {}

	ReloadBlackBoxCaseKnowledgeReq {
		KnowledgeId string `json:"knowledge_id" validate:"required" zh:"文档ID"`
	}
	ReloadBlackBoxCaseKnowledgeResp {}

	GetBlackBoxCaseKnowledgeReq {
		KnowledgeId string `form:"knowledge_id" validate:"required" zh:"文档ID"`
	}
	GetBlackBoxCaseKnowledgeResp {
		Item *BlackBoxCaseKnowledge `json:"item"`
	}
	
	GetBlackBoxCaseKnowledgeTitleReq {
		KnowledgeId string `form:"knowledge_id" validate:"required" zh:"文档ID"`
	}
	GetBlackBoxCaseKnowledgeTitleResp {
		Data interface{} `json:"data" zh:"返回结果"`
	}

	CreateBlackBoxCaseKnowledgeReq {
		DirId            string `json:"dir_id" validate:"required" zh:"目录Id"`
		KnowledgeName    string `json:"knowledge_name" zh:"文档名称"`
		KnowledgeType    string `json:"knowledge_type" zh:"文档类型"`
		KnowledgeContent string `json:"knowledge_content" zh:"文档内容"`
	}
	CreateBlackBoxCaseKnowledgeResp {
		KnowledgeId string `json:"knowledge_id" zh:"目录Id"`
	}

	UpdateBlackBoxCaseKnowledgeReq {
		DirId            string `json:"dir_id" validate:"required" zh:"目录Id"`
		KnowledgeId      string `json:"knowledge_id" validate:"required" zh:"文档ID"`
		KnowledgeName    string `json:"knowledge_name" zh:"文档名称"`
		KnowledgeType    string `json:"knowledge_type" zh:"文档类型"`
		KnowledgeContent string `json:"knowledge_content" zh:"文档内容"`
	}
	UpdateBlackBoxCaseKnowledgeResp {}

	QueryBlackBoxCaseKnowledgeReq {
		CaseId                      string `json:"case_id" zh:"用例ID"`
		KnowledgeId                 string `json:"knowledge_id" zh:"知识文档ID"`
		KnowledgeParagraphTitleText string `json:"knowledge_paragraph_title_text,optional,omitempty" zh:"文档使用标题"`
		KnowledgeParagraphTitleId   int64  `json:"knowledge_paragraph_title_id,optional,omitempty" zh:"文档使用标题id"`
	}
	QueryBlackBoxCaseKnowledgeResp {}

	QueryBlackBoxCaseKnowledgeV2Req {
		CaseId                      string `json:"case_id" zh:"用例ID"`
		KnowledgeId                 string `json:"knowledge_id" zh:"知识文档ID"`
		KnowledgeParagraphTitle     []*KnowledgeDocPgTitle `json:"knowledge_paragraph_title" zh:"文档使用标题"`
	}
	QueryBlackBoxCaseKnowledgeV2Resp {}
)