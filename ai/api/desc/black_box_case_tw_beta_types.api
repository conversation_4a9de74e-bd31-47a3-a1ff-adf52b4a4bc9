syntax = "v1"

import "types.api"

type BlackBoxCaseTwBeta {
	Id                    int           `json:"id" zh:"自增Id"`
	BetaCaseId            string        `json:"beta_case_id" zh:"BETA用例ID"`
	BetaCaseName          string        `json:"beta_case_name" zh:"BETA用例名称"`
	RevisionId            string        `json:"revision_id" zh:"版本Id"`
	CaseContent           string        `json:"case_content" zh:"原始用例结果"`
	DocumentName          string        `json:"document_name" zh:"文档名称"`
	DocumentUrl           string        `json:"document_url" zh:"文档URL"`
	DocumentChapterTitle  string        `json:"document_chapter_title" zh:"章节标题"`
	DocumentContent		  string        `json:"document_content" zh:"选中文档内容"`
	ReferenceDoc		  string        `json:"reference_doc" zh:"补充文档内容"`
	KnowledgeFixSugg	  string        `json:"knowledge_fix_sugg" zh:"修改意见"`
	CaseRefId			  string        `json:"case_ref_id" zh:"用例关联ID"`
	Deleted				  int64         `json:"deleted" zh:"逻辑删除标识（未删除、已删除）"`
	CreatedBy             *FullUserInfo `json:"created_by" zh:"创建者的用户ID"`
	UpdatedBy             *FullUserInfo `json:"updated_by" zh:"最近一次更新者的用户ID"`
	DeletedBy             *FullUserInfo `json:"deleted_by" zh:"删除者的用户ID"`
	CreatedAt             int64         `json:"created_at" zh:"创建时间"`
	UpdatedAt             int64         `json:"updated_at" zh:"更新时间"`
	DeletedAt             int64         `json:"deleted_at" zh:"删除时间"`
}

type BlackBoxCaseTwBetaMind {
	Id                    int64                                 `json:"id" zh:"id"`
	ProductModule         string                                `json:"product_module" zh:"产品模块"`
	AcCheckList           []*BlackBoxCaseTwBetaMindCheck        `json:"ac_check_list" zh:"ac_check_list"`
	TcCheckList           []*BlackBoxCaseTwBetaMindCheck        `json:"tc_check_list" zh:"tc_check_list"`
	Status                string                                `json:"status" zh:"状态 succeed, failed, processing"`
	CreatedAt             int64                                 `json:"created_at" zh:"创建时间"`
}

type BlackBoxCaseTwBetaMindCheck {
	UseCase               string        `json:"use_case" zh:"use_case"`
	CheckPoint            string        `json:"check_point" zh:"check_point"`
}

type (
	ListBlackBoxCaseTwBetaReq {
		BetaCaseName string      `json:"beta_case_name" zh:"BETA用例名称"`
		Pagination   *Pagination `json:"pagination,omitempty,optional" zh:"查询分页"`
	}
	ListBlackBoxCaseTwBetaResp {
		Items []*BlackBoxCaseTwBeta `json:"items"`
		CurrentPage uint64            `json:"current_page"`
		PageSize    uint64            `json:"page_size"`
		TotalCount  uint64            `json:"total_count"`
		TotalPage   uint64            `json:"total_page"`
	}
)

type (
	CreateBlackBoxCaseTwBetaMindReq {
		BetaCaseId            string      `json:"beta_case_id" zh:"BETA用例ID"`
		ProductModule         string      `json:"product_module" zh:"产品模块"`
		AcCheckList           []*BlackBoxCaseTwBetaMindCheck        `json:"ac_check_list" zh:"ac_check_list"`
		TcCheckList           []*BlackBoxCaseTwBetaMindCheck        `json:"tc_check_list" zh:"tc_check_list"`
	}
	CreateBlackBoxCaseTwBetaMindResp {
	}
)

type (
	ListBlackBoxCaseTwBetaMindReq {
		BetaCaseId string      `json:"beta_case_id" zh:"BETA用例ID"`
	}
	ListBlackBoxCaseTwBetaMindResp {
		Items       []*BlackBoxCaseTwBetaMind `json:"items"`
	}
)

type (
	GetBlackBoxCaseTwBetaMindContentReq {
		id       int64      `json:"id" zh:"脑图ID"`
		MindType string      `json:"mind_type" zh:"脑图类型"`
	}
	GetBlackBoxCaseTwBetaMindContentResp {
		Data string      `json:"data" zh:"脑图内容"`
	}
)

type (
	ListBlackBoxCaseTwBetaMindCheckReq {
		ProductModule         string                                `json:"product_module" zh:"产品模块"`
	}
	ListBlackBoxCaseTwBetaMindCheckResp {
		AcCheckList           []*BlackBoxCaseTwBetaMindCheck        `json:"ac_check_list" zh:"ac_check_list"`
		TcCheckList           []*BlackBoxCaseTwBetaMindCheck        `json:"tc_check_list" zh:"tc_check_list"`
	}
)

type (
	DeleteBlackBoxCaseTwBetaReq {
		BetaCaseId string      `json:"beta_case_id" zh:"BETA用例ID"`
	}
	DeleteBlackBoxCaseTwBetaResp {
	}
)


type (
	CreateBlackBoxCaseTwBetaReq {
		mindId          int64       `json:"mind_id" zh:"脑图ID"`
		CaseRefId      string      `json:"case_ref_id" zh:"BETA用例ID"`
	}

	CreateBlackBoxCaseTwBetaResp {
	}
)

type (
	UpdateBlackBoxCaseMindTwBetaReq {
		mindId          int64       `json:"mind_id" zh:"脑图ID"`
		mindContent     string      `json:"mind_content" zh:"脑图内容"`
	}

	UpdateBlackBoxCaseMindTwBetaResp {
	}
)

type (
	GetBlackBoxCaseTwBetaContentReq {
		mindId          int64       `json:"mind_id" zh:"脑图ID"`
	}
	GetBlackBoxCaseTwBetaContentResp {
		Data string      `json:"data" zh:"用例内容"`
	}
)