syntax = "v1"

import "black_box_case_revision_types.api"

@server (
	prefix: ai/v1
	group: blackboxcaserevision
    //timeout : 3000ms
)
service ai {
    @doc "list black box case revision"
    @handler listBlackBoxCaseRevision
    post /black/box/case/revision/list (ListBlackBoxCaseRevisionReq) returns (ListBlackBoxCaseRevisionResp)

    @handler createBlackBoxCaseRevisionData
    post /black/box/case/revision/data (CreateBlackBoxCaseRevisionDataReq) returns (CreateBlackBoxCaseRevisionDataResp)

    @handler adoptBlackBoxCaseRevisionData
    post /black/box/case/revision/data/adopt (AdoptBlackBoxCaseRevisionDataReq) returns (AdoptBlackBoxCaseRevisionDataResp)
}
