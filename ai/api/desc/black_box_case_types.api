syntax = "v1"

import "types.api"

type BlackBoxCase {
	Id                          int           `json:"id" zh:"自增Id"`
	DirId                       string        `json:"dir_id" zh:"目录Id"`
	CaseId                      string        `json:"case_id" zh:"用例Id"`
	CaseName                    string        `json:"case_name" zh:"用例名称"`
	CaseContinueToWrite         int64         `json:"case_continue_to_write" zh:"用例续写次数"`
	CaseModelCharacter          string        `json:"case_model_character" zh:"用例模型性格"`
	CaseRemarks                 string        `json:"case_remarks" zh:"用例备注"`
	KnowledgeId                 string        `json:"knowledge_id" zh:"知识文档ID"`
	KnowledgeName               string        `json:"knowledge_name" zh:"知识文档ID"`
	KnowledgeContent            string        `json:"knowledge_content" zh:"知识文档ID"`
	KnowledgeParagraphTitleId   int64         `json:"knowledge_paragraph_title_id" zh:"知识文档ID"`
	EnableReference             bool          `json:"enable_reference" zh:"是否关联文档"`
	KnowledgeFixSugg            string        `json:"knowledge_fix_sugg" zh:"是否关联文档"`
	ReferenceDoc                *ReferenceDoc `json:"reference_doc" zh:"关联文档"`
	CreatedBy                   *FullUserInfo `json:"created_by" zh:"创建者的用户Id"`
	UpdatedBy                   *FullUserInfo `json:"updated_by" zh:"最近一次更新者的用户Id"`
	DeletedBy                   *FullUserInfo `json:"deleted_by" zh:"删除者的用户Id"`
	CreatedAt                   int64         `json:"created_at" zh:"创建时间"`
	UpdatedAt                   int64         `json:"updated_at" zh:"更新时间"`
	DeletedAt                   int64         `json:"deleted_at" zh:"删除时间"`
	KnowledgeParagraphTitleText string        `json:"knowledge_paragraph_title_text,optional,omitempty" zh:"知识文档段落标题"`
	KnowledgeDocPgTitle []*KnowledgeDocPgTitle        `json:"knowledge_paragraph_title,optional,omitempty" zh:"新版知识文档段落标题"`
}

type (
	ListBlackBoxCaseReq {
		DirId      string      `json:"dir_id" validate:"required" zh:"目录Id"`
		CaseName   string      `json:"case_name,optional" zh:"用例名称"`
		Pagination *Pagination `json:"pagination,omitempty,optional" zh:"查询分页"`
	}
	ListBlackBoxCaseResp {
		CurrentPage uint64          `json:"current_page"`
		PageSize    uint64          `json:"page_size"`
		TotalCount  uint64          `json:"total_count"`
		TotalPage   uint64          `json:"total_page"`
		Items       []*BlackBoxCase `json:"items"`
	}
	DeleteBlackBoxCaseReq {
		DirId  string `json:"dir_id" validate:"required" zh:"目录Id"`
		CaseId string `json:"case_id" validate:"required" zh:"用例Id"`
	}
	DeleteBlackBoxCaseResp  {}
	GetBlackBoxCaseReq {
		DirId  string `form:"dir_id" validate:"required" zh:"目录Id"`
		CaseId string `form:"case_id" validate:"required" zh:"用例Id"`
	}
	GetBlackBoxCaseResp {
		Item *BlackBoxCase `json:"item"`
	}
	CreateBlackBoxCaseReq {
		DirId                     string `json:"dir_id" validate:"required" zh:"目录Id"`
		CaseName                  string `json:"case_name" zh:"用例名称"`
		CaseContinueToWrite       int64  `json:"case_continue_to_write" zh:"用例续写次数"`
		CaseModelCharacter        string `json:"case_model_character" zh:"用例模型性格"`
		CaseRemarks               string `json:"case_remarks,optional" zh:"用例备注"`
		KnowledgeId               string `json:"knowledge_id" zh:"知识文档ID"`
		KnowledgeParagraphTitleId int64  `json:"knowledge_paragraph_title_id,optional"`
	}
	CreateBlackBoxCaseResp {
		CaseId string `json:"case_id" zh:"用例Id"`
	}
	KnowledgeDocPgTitle {
		Id           int64    `json:"knowledge_paragraph_title_id"`
		Title        string   `json:"knowledge_paragraph_title_text"`
		DemandPoints []string `json:"demandPoints,optional,omitempty"`
	}
	UpdateBlackBoxCaseItemReq {
		DirId               string                `json:"dir_id" validate:"required" zh:"目录Id"`
		CaseId              string                `json:"case_id" validate:"required" zh:"用例Id"`
		CaseName            string                `json:"case_name,optional,omitempty" zh:"用例名称"`
		CaseContinueToWrite int64                 `json:"case_continue_to_write,optional,omitempty" zh:"用例续写次数"`
		CaseModelCharacter  string                `json:"case_model_character,optional,omitempty" zh:"用例模型性格"`
		CaseRemarks         string                `json:"case_remarks,optional,omitempty" zh:"用例备注"`
		KnowledgeId         string                `json:"knowledge_id,optional,omitempty" zh:"知识文档ID"`
		KnowledgeDocPgTitle []KnowledgeDocPgTitle `json:"knowledge_paragraph_title,optional,omitempty" zh:"知识文档段落标题"`
	}
	UpdateBlackBoxCaseItemResp  {}
	UpdateBlackBoxCaseAIReq {
		CaseId              string `json:"case_id" validate:"required" zh:"用例Id"`
		CaseName            string `json:"case_name,optional,omitempty" zh:"用例名称"`
		CaseContinueToWrite int64  `json:"case_continue_to_write,optional,omitempty" zh:"用例续写次数"`
		CaseModelCharacter  string `json:"case_model_character,optional,omitempty" zh:"用例模型性格"`
	}
	UpdateBlackBoxCaseAIResp  {}
	MergeBlackBoxCaseDataReq {
		CaseIds  []string `json:"case_ids" zh:"用例Id列表"`
		CaseName string   `json:"case_name" zh:"用例名"`
	}
	MergeBlackBoxCaseDataResp {
		CaseId string `json:"case_id" zh:"用例Id"`
	}
)

