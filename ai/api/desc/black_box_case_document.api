syntax = "v1"

import "black_box_case_document_types.api"

@server (
	prefix: ai/v1
	group: blackboxcasedocument
    //  timeout : 3000ms
)
service ai {
    @doc "create black box case document for assistant"
    @handler createBlackBoxCaseDocumentForAssistant
    post /black/box/case/document/for_assistant/create (CreateBlackBoxCaseDocumentForAssistantReq) returns (CreateBlackBoxCaseDocumentForAssistantResp)

    @doc "create black box case document for session"
    @handler createBlackBoxCaseDocumentForSession
    post /black/box/case/document/for_session/create (CreateBlackBoxCaseDocumentForSessionReq) returns (CreateBlackBoxCaseDocumentForSessionResp)

    @doc "create black box case document for session recv 在文档表和会话文档表创建"
    @handler createBlackBoxCaseDocumentForSessionRecv
    post /black/box/case/document/for_session_recv/create (CreateBlackBoxCaseDocumentForSessionRecvReq) returns (CreateBlackBoxCaseDocumentForSessionRecvResp)

    @doc "update black box case document status for assistant"
    @handler updateBlackBoxCaseDocumentStatusForAssistant
    put /black/box/case/document_status/for_assistant/update (UpdateBlackBoxCaseDocumentStatusForAssistantReq) returns (UpdateBlackBoxCaseDocumentStatusForAssistantResp)

    @doc "get black box case document"
    @handler getBlackBoxCaseDocument
    get /black/box/case/document/get (GetBlackBoxCaseDocumentReq) returns (GetBlackBoxCaseDocumentResp)

    @doc "update black box case document"
    @handler updateBlackBoxCaseDocument
    put /black/box/case/document/update (UpdateBlackBoxCaseDocumentReq) returns (UpdateBlackBoxCaseDocumentResp)

    @doc "delete black box case document for assistant"
    @handler deleteBlackBoxCaseDocumentForAssistant
    delete /black/box/case/document/for_assistant/delete (DeleteBlackBoxCaseDocumentForAssistantReq) returns (DeleteBlackBoxCaseDocumentForAssistantResp)

    @doc "batch delete black box case document for assistant"
    @handler batchDeleteBlackBoxCaseDocumentForAssistant
    delete /black/box/case/document/for_assistant/batch/delete (BatchDeleteBlackBoxCaseDocumentForAssistantReq) returns (BatchDeleteBlackBoxCaseDocumentForAssistantResp)

    @doc "delete black box case document for session"
    @handler deleteBlackBoxCaseDocumentForSession
    delete /black/box/case/document/for_session/delete (DeleteBlackBoxCaseDocumentForSessionReq) returns (DeleteBlackBoxCaseDocumentForSessionResp)

    @doc "batch delete black box case document for session"
    @handler batchDeleteBlackBoxCaseDocumentForSession
    delete /black/box/case/document/for_session/batch/delete (BatchDeleteBlackBoxCaseDocumentForSessionReq) returns (BatchDeleteBlackBoxCaseDocumentForSessionResp)

	@doc "search black box case document for assistant"
	@handler searchBlackBoxCaseDocumentForAssistant
	post /black/box/case/document/for_assistant/search (SearchBlackBoxCaseDocumentForAssistantReq) returns (SearchBlackBoxCaseDocumentForAssistantResp)

	@doc "search black box case document for session"
	@handler searchBlackBoxCaseDocumentForSession
	post /black/box/case/document/for_session/search (SearchBlackBoxCaseDocumentForSessionReq) returns (SearchBlackBoxCaseDocumentForSessionResp)

    @doc "get black box case document headers list"
    @handler getBlackBoxCaseDocumentHeadersList
    get /black/box/case/document/headers/list (GetBlackBoxCaseDocumentHeadersListReq) returns (GetBlackBoxCaseDocumentHeadersListResp)

    @doc "reload black box case document"
    @handler reloadBlackBoxCaseDocument
    put /black/box/case/document/reload (ReloadBlackBoxCaseDocumentReq) returns (ReloadBlackBoxCaseDocumentResp)

}
