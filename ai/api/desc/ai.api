syntax = "v1"

info (
	title:   "质量平台-测试管理服务"
	desc:    "质量平台中的AI服务"
	author:  "yb"
	email:   "<EMAIL>"
	version: "0.0.0"
)

import (
	"black_box_case_assistant.api"
	"black_box_case_document.api"
	"black_box_case_session.api"
	"black_box_case_session_stream.api"
	"black_box_case_dir.api"
	"black_box_case.api"
	"black_box_case_knowledge.api"
	"black_box_case_revision.api"
	"black_box_case_data.api"
	"black_box_case_tw_beta.api"
	"v2/black_box_knowledge_project.api"
	"v2/black_box_knowledge_tag.api"
	"v2/black_box_knowledge_term.api"
	"v2/black_box_knowledge_experience.api"
	"v2/black_box_knowledge_experience_category.api"
	"v2/black_box_case_directory.api"
	"v2/black_box_case_document.api"
	"v2/black_box_case_document_title.api"
	"v2/black_box_case_document_summary.api"
	"v2/black_box_case.api"
	"v2/black_box_case_knowledge.api"
	"v2/black_box_case_map.api"
	"v2/black_box_case_map_function.api"
	"v2/black_box_case_map_experience.api"
	"v2/black_box_case_map_knowledge.api"
	"v2/black_box_case_map_progress.api"
	"v2/black_box_case_map_document.api"
	"v2/black_box_case_data.api"
	"v2/black_box_case_data_revision.api"
	"v2/black_box_case_data_reservation.api"
	"v2/black_box_case_data_evacuation.api"
	"v2/black_box_case_data_progress.api"
	"v2/black_box_case_data_sort.api"
	"v2/black_box_case_data_map.api"
	"v2/black_box_case_data_drag.api"
)

