
SERVICE_NAME = $(shell basename $(dir $(abspath $(lastword $(MAKEFILE_LIST)))))
MAKEFILE_DIR := $(patsubst %/,%,$(dir $(abspath $(lastword $(MAKEFILE_LIST)))))
BIN_DIR := ${MAKEFILE_DIR}/bin

ifeq (, $(GITLAB_GROUP))
GITLAB_GROUP := "gitlab.ttyuyin.com/TestDevelopment"
endif

ifeq (, $(VERSION))
VERSION := $(shell [ -f "../VERSION" ] && cat ../VERSION || echo "0.0.1")
endif

ifeq (, $(VERSION_PACKAGE))
VERSION_PACKAGE := $(GITLAB_GROUP)/qet-backend-common/version
endif

ifeq (, $(LDFLAGS))
$(shell cd $(MAKEFILE_DIR)/..)

GIT_BRANCH := $(shell git rev-parse --abbrev-ref HEAD)
GIT_TAG := $(shell [ "`git describe --tags --abbrev=0 2>/dev/null`" != "" ] && git describe --tags --abbrev=0 || git log --pretty=format:'%h' -n 1)
GIT_COMMIT := $(shell git rev-parse --short HEAD)
BUILD_DATETIME := $(shell TZ=Asia/Shanghai date +'%F %T %Z')
BUILD_GO_VERSION := $(shell go env GOVERSION)
PLATFORM := $(shell uname)

GCFLAGS := "-N -l"
LDFLAGS := "-s -w -X \"${VERSION_PACKAGE}.Version=${VERSION}\" -X \"${VERSION_PACKAGE}.GitBranch=${GIT_BRANCH}\" -X \"${VERSION_PACKAGE}.GitTag=${GIT_TAG}\" -X \"${VERSION_PACKAGE}.GitCommit=${GIT_COMMIT}\" -X \"${VERSION_PACKAGE}.BuildDatetime=${BUILD_DATETIME}\" -X \"${VERSION_PACKAGE}.BuildGoVersion=${BUILD_GO_VERSION}\""

$(shell cd $(MAKEFILE_DIR))
endif

define cmd-build
	@$(eval PRE_ENV := $(if $(filter darwin,$(2)),GOOS=$(2),GOOS=$(2) GOARCH=amd64))
	@$(eval TARGET := $(if $(filter "",$(1)),$(SERVICE_NAME).$(2),$(SERVICE_NAME).$(1).$(2)))
	@$(eval SOURCE := $(if $(filter "",$(1)),$(SERVICE_NAME).go,$(1)/$(SERVICE_NAME).go))
	@echo "build $(TARGET) by $(PRE_ENV)"
	@$(PRE_ENV) go build -o $(BIN_DIR)/$(TARGET) -gcflags $(GCFLAGS) -ldflags $(LDFLAGS) $(MAKEFILE_DIR)/$(SOURCE)
endef

.PHONY: echo
echo:
	@echo "SERVICE_NAME: $(SERVICE_NAME)"
	@echo "MAKEFILE_DIR: $(MAKEFILE_DIR)"
	@echo "GITLAB_GROUP: $(GITLAB_GROUP)"
	@echo "VERSION: $(VERSION)"
	@echo "VERSION_PACKAGE: $(VERSION_PACKAGE)"
	@echo "GIT_BRANCH: $(GIT_BRANCH)"
	@echo "GIT_TAG: $(GIT_TAG)"
	@echo "GIT_COMMIT: $(GIT_COMMIT)"
	@echo "BUILD_DATETIME: $(BUILD_DATETIME)"
	@echo "BUILD_GO_VERSION: $(BUILD_GO_VERSION)"
	@echo "PLATFORM: $(PLATFORM)"
	@echo "LDFLAGS: $(LDFLAGS)"
	@echo "BIN_DIR: $(BIN_DIR)"
	@echo ""

.PHONY: fmt
fmt:
	@go list -f {{.Dir}} $(MAKEFILE_DIR)/... | xargs gofmt -s -w
	@goimports -l -w -local $(GITLAB_GROUP) $(MAKEFILE_DIR)

.PHONY: lint
lint:
	@golangci-lint run -c $(MAKEFILE_DIR)/../.golangci.yaml

.PHONY: build
build: api rpc

.PHONY: api
api: api-mac api-linux

.PHONY: api-mac
api-mac:
	$(call cmd-build,api,darwin)

.PHONY: api-linux
api-linux:
	$(call cmd-build,api,linux)

.PHONY: rpc
rpc: rpc-mac rpc-linux

.PHONY: rpc-mac
rpc-mac:
	$(call cmd-build,rpc,darwin)

.PHONY: rpc-linux
rpc-linux:
	$(call cmd-build,rpc,linux)

.PHONY: all
all: all-mac all-linux all-win

.PHONY: all-mac
all-mac:
	$(call cmd-build,"",darwin)

.PHONY: all-linux
all-linux:
	$(call cmd-build,"",linux)

.PHONY: all-win
all-win:
	$(call cmd-build,"",windows)

## TODO: 如何把 分支名作为版本号写入VERSION文件
