package common

import (
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/public/httpc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
)

// RemoveDuplicate 数组去重
func RemoveDuplicate[T comparable](duplicateSlice []T) []T {
	set := map[T]any{}
	res := []T{}
	for _, item := range duplicateSlice {
		_, ok := set[item]
		if !ok {
			res = append(res, item)
			set[item] = nil
		}
	}
	return res
}

func KnowledgeDocPgTitleMsg2PbStruct(knowledgeDocPgTitles []httpc.KnowledgeDocPgTitleV2) (res []*pb.KnowledgeDocPgTitle) {
	res = make([]*pb.KnowledgeDocPgTitle, 0, len(knowledgeDocPgTitles))
	for _, title := range knowledgeDocPgTitles {
		res = append(res, &pb.KnowledgeDocPgTitle{
			Id:           title.Id,
			Title:        title.Title,
			DemandPoints: title.DemandPoints,
		})
	}
	return res
}

func KnowledgeDocPgTitlePbStruct2Msg(knowledgeDocPgTitles []*pb.KnowledgeDocPgTitle) (res []httpc.KnowledgeDocPgTitleV2) {
	res = make([]httpc.KnowledgeDocPgTitleV2, 0, len(knowledgeDocPgTitles))
	for _, title := range knowledgeDocPgTitles {
		res = append(res, httpc.KnowledgeDocPgTitleV2{
			Id:           title.Id,
			Title:        title.Title,
			DemandPoints: title.DemandPoints,
		})
	}
	return res
}
