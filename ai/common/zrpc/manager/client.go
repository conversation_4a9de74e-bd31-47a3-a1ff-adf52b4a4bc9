package manager

import (
	"context"

	"github.com/zeromicro/go-zero/zrpc"
	"google.golang.org/grpc"

	client "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/client/projectservice"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/interceptors/clientinterceptors"
)

type ProjectServiceRPC struct {
	conf zrpc.RpcClientConf

	client client.ProjectService
}

func NewProjectServiceRPC(c zrpc.RpcClientConf) ProjectServiceRPC {
	return ProjectServiceRPC{
		conf:   c,
		client: client.NewProjectService(zrpc.MustNewClient(c, clientinterceptors.UnaryUserInfoClientOption())),
	}
}

func (c *ProjectServiceRPC) SearchProject(
	ctx context.Context, in *client.SearchProjectReq, opts ...grpc.CallOption,
) (*client.SearchProjectResp, error) {
	return c.client.SearchProject(ctx, in, opts...)
}
