package constants

import (
	"fmt"
)

const (
	CacheBlackBoxCaseMapProgressRedisKeyPrefix = "cache:ai:blackbox:case:map:progress:%s" // + case_id

	DefaultRedisCacheTTL = 60 * 30 // 30 minutes
)

var (
	BlackBoxCaseDataVersionV24 = "v2-4"
)

func GenerateBlackBoxCaseMapProgressRedisKey(caseId string) (redisKey string) {
	if caseId == "" {
		return ""
	}
	return fmt.Sprintf(CacheBlackBoxCaseMapProgressRedisKeyPrefix, caseId)
}
