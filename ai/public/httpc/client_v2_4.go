package httpc

import (
	"bytes"
	"encoding/json"
	"fmt"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sse"
	"net/http"
	"time"

	"github.com/google/go-querystring/query"
	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/logx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
)

// from https://q9jvw0u5f5.feishu.cn/wiki/J1xnwWlFRipLoJkzyFQc5OWnnEF#share-MS4zdjFJdo1bUHxMCoFcFI3yn3r

var (
	CreateKnowledgeMetaProjectUri            = "/api/v2-4/knowledge-meta/project/create_or_update"
	ListKnowledgeMetaTagUri                  = "/api/v2-4/knowledge-meta/tag/list"
	CreateKnowledgeMetaTagUri                = "/api/v2-4/knowledge-meta/tag/create"
	UpdateKnowledgeMetaTagUri                = "/api/v2-4/knowledge-meta/tag/update"
	DeleteKnowledgeMetaTagUri                = "/api/v2-4/knowledge-meta/tag/delete"
	ListKnowledgeTermUri                     = "/api/v2-4/knowledge-term/list"
	CreateKnowledgeTermUri                   = "/api/v2-4/knowledge-term/create"
	UpdateKnowledgeTermUri                   = "/api/v2-4/knowledge-term/update"
	DeleteKnowledgeTermUri                   = "/api/v2-4/knowledge-term/delete"
	ListKnowledgeTestExperienceUri           = "/api/v2-4/knowledge-test-experience/list"
	CreateKnowledgeTestExperienceUri         = "/api/v2-4/knowledge-test-experience/create"
	UpdateKnowledgeTestExperienceUri         = "/api/v2-4/knowledge-test-experience/update"
	DeleteKnowledgeTestExperienceUri         = "/api/v2-4/knowledge-test-experience/delete"
	ListKnowledgeMetaProjectUri              = "/api/v2-4/knowledge-meta/project/list"
	ListKnowledgeTestExperienceCategoryUri   = "/api/v2-4/knowledge-test-experience/category/list"
	CreateKnowledgeTestExperienceCategoryUri = "/api/v2-4/knowledge-test-experience/category/create"
	DeleteKnowledgeTestExperienceCategoryUri = "/api/v2-4/knowledge-test-experience/category/delete"

	GetAiStateCaseMapProgressUri       = "/api/v2-4/ai-state/generation/estimated-progress/get"
	ListAiStateCaseMapDocumentsUri     = "/api/v2-4/ai-state/document/list"
	GetAiStateCaseUsedCaseKnowledgeUri = "/api/v2-4/ai-state/used-knowledge/list"
	GetTestCaseMindUri                 = "/api/v2-4/ai-state/test-case/mind/get"

	CreateBaseReviewMapUri      = "/api/v2-4/review-map/base/create"
	CreateFuncReviewMapUri      = "/api/v2-4/review-map/func/create"
	CreateTestSceneReviewMapUri = "/api/v2-4/review-map/test-scene/create"
	UpdateTestSceneReviewMapUri = "/api/v2-4/review-map/test-scene/fix"
	CreateTestCaseUri           = "/api/v2-4/test-case/create"
	UpdateTestCaseUri           = "/api/v2-4/test-case/update"

	Json2MDV24Uri = "/api/v2/test-case/table/convert"

	GetFuncCoverageUri        = "/api/v2-4/statistics/get_test_case_coverage_by_case_id"
	GetDocumentSizeUri        = "/api/v2-4/statistics/get_doc_size_by_case_id"
	GetUpdatedCaseAddCountUri = "/api/v2-4/statistics/get_add_number_by_case_id"

	CreateExtractDemandPointsUri = "/api/v2-4/knowledge-doc/extrac-demaned-points/create"
	SummarizeCaseDocUri          = "/api/v2-4/test-case/query" // 测试用例文档总结
)

func CreateKnowledgeMetaProject(baseUrl string, in CreateKnowledgeMetaProjectReq) (ResponseData[any], error) {
	var respData ResponseData[any]
	err := ExecuteRequest(http.MethodPost, baseUrl, CreateKnowledgeMetaProjectUri, in, &respData)
	if err != nil {
		return respData, err
	}
	if respData.Code != 0 {
		return respData, errors.New(respData.Message)
	}
	return respData, nil
}

func ListKnowledgeMetaTag(baseUrl string, in ListKnowledgeMetaTagReq) (ResponseData[any], error) {
	var respData ResponseData[any]

	values, err := query.Values(in)
	if err != nil {
		logx.Errorf("ListKnowledgeMetaTag convert data error:%v", err)
		return ResponseData[any]{}, err
	}

	err = ExecuteRequest(http.MethodGet, baseUrl, ListKnowledgeMetaTagUri+"?"+values.Encode(), nil, &respData)
	if err != nil {
		return respData, err
	}
	if respData.Code != 0 {
		return respData, errors.New(respData.Message)
	}
	return respData, nil
}

func CreateKnowledgeMetaTag(baseUrl string, in CreateKnowledgeMetaTagReq) (ResponseData[any], error) {
	var respData ResponseData[any]
	err := ExecuteRequest(http.MethodPost, baseUrl, CreateKnowledgeMetaTagUri, in, &respData)
	if err != nil {
		return respData, err
	}
	if respData.Code != 0 {
		return respData, errors.New(respData.Message)
	}
	return respData, nil
}

func UpdateKnowledgeMetaTag(baseUrl string, in UpdateKnowledgeMetaTagReq) (ResponseData[any], error) {
	var respData ResponseData[any]
	err := ExecuteRequest(http.MethodPost, baseUrl, UpdateKnowledgeMetaTagUri, in, &respData)
	if err != nil {
		return respData, err
	}
	if respData.Code != 0 {
		return respData, errors.New(respData.Message)
	}
	return respData, nil
}

func DeleteKnowledgeMetaTag(baseUrl string, in DeleteKnowledgeMetaTagReq) (ResponseData[any], error) {
	var respData ResponseData[any]
	err := ExecuteRequest(http.MethodDelete, baseUrl, DeleteKnowledgeMetaTagUri, in, &respData)
	if err != nil {
		return respData, err
	}
	if respData.Code != 0 {
		return respData, errors.New(respData.Message)
	}
	return respData, nil
}

func ListKnowledgeTerm(baseUrl string, in ListKnowledgeTermReq) (ResponseData[any], error) {
	var respData ResponseData[any]

	err := ExecuteRequest(http.MethodPost, baseUrl, ListKnowledgeTermUri, in, &respData)
	if err != nil {
		return respData, err
	}
	if respData.Code != 0 {
		return respData, errors.New(respData.Message)
	}
	return respData, nil
}

func CreateKnowledgeTerm(baseUrl string, in CreateKnowledgeTermReq) (ResponseData[any], error) {
	var respData ResponseData[any]
	err := ExecuteRequest(http.MethodPost, baseUrl, CreateKnowledgeTermUri, in, &respData)
	if err != nil {
		return respData, err
	}
	if respData.Code != 0 {
		return respData, errors.New(respData.Message)
	}
	return respData, nil
}

func UpdateKnowledgeTerm(baseUrl string, in UpdateKnowledgeTermReq) (ResponseData[any], error) {
	var respData ResponseData[any]
	err := ExecuteRequest(http.MethodPost, baseUrl, UpdateKnowledgeTermUri, in, &respData)
	if err != nil {
		return respData, err
	}
	if respData.Code != 0 {
		return respData, errors.New(respData.Message)
	}
	return respData, nil
}

func DeleteKnowledgeTerm(baseUrl string, in DeleteKnowledgeTermReq) (ResponseData[any], error) {
	var respData ResponseData[any]
	err := ExecuteRequest(http.MethodDelete, baseUrl, DeleteKnowledgeTermUri, in, &respData)
	if err != nil {
		return respData, err
	}
	if respData.Code != 0 {
		return respData, errors.New(respData.Message)
	}
	return respData, nil
}

func ListKnowledgeTestExperience(baseUrl string, in ListKnowledgeTestExperienceReq) (ResponseData[any], error) {
	var respData ResponseData[any]
	err := ExecuteRequest(http.MethodPost, baseUrl, ListKnowledgeTestExperienceUri, in, &respData)
	if err != nil {
		return respData, err
	}
	if respData.Code != 0 {
		return respData, errors.New(respData.Message)
	}
	return respData, nil
}

func CreateKnowledgeTestExperience(baseUrl string, in CreateKnowledgeTestExperienceReq) (ResponseData[any], error) {
	var respData ResponseData[any]
	err := ExecuteRequest(http.MethodPost, baseUrl, CreateKnowledgeTestExperienceUri, in, &respData)
	if err != nil {
		return respData, err
	}
	if respData.Code != 0 {
		return respData, errors.New(respData.Message)
	}
	return respData, nil
}

func UpdateKnowledgeTestExperience(baseUrl string, in UpdateKnowledgeTestExperienceReq) (ResponseData[any], error) {
	var respData ResponseData[any]
	err := ExecuteRequest(http.MethodPost, baseUrl, UpdateKnowledgeTestExperienceUri, in, &respData)
	if err != nil {
		return respData, err
	}
	if respData.Code != 0 {
		return respData, errors.New(respData.Message)
	}
	return respData, nil
}

func DeleteKnowledgeTestExperience(baseUrl string, in DeleteKnowledgeTestExperienceReq) (ResponseData[any], error) {
	var respData ResponseData[any]
	err := ExecuteRequest(http.MethodDelete, baseUrl, DeleteKnowledgeTestExperienceUri, in, &respData)
	if err != nil {
		return respData, err
	}
	if respData.Code != 0 {
		return respData, errors.New(respData.Message)
	}
	return respData, nil
}

func ListKnowledgeMetaProject(baseUrl string, in ListKnowledgeMetaProjectReq) (ResponseData[any], error) {
	var respData ResponseData[any]
	err := ExecuteRequest(http.MethodGet, baseUrl, ListKnowledgeMetaProjectUri, in, &respData)
	if err != nil {
		return respData, err
	}
	if respData.Code != 0 {
		return respData, errors.New(respData.Message)
	}
	return respData, nil
}

func ListKnowledgeTestExperienceCategory(baseUrl string, in ListKnowledgeTestExperienceCategoryReq) (ResponseData[any], error) {
	var respData ResponseData[any]

	values, err := query.Values(in)
	if err != nil {
		logx.Errorf("ListKnowledgeTestExperienceCategory convert data error:%v", err)
		return ResponseData[any]{}, err
	}

	err = ExecuteRequest(http.MethodGet, baseUrl, ListKnowledgeTestExperienceCategoryUri+"?"+values.Encode(), nil, &respData)
	if err != nil {
		return respData, err
	}
	if respData.Code != 0 {
		return respData, errors.New(respData.Message)
	}
	return respData, nil
}

func CreateKnowledgeTestExperienceCategory(baseUrl string, in CreateKnowledgeTestExperienceCategoryReq) (ResponseData[any], error) {
	var respData ResponseData[any]
	err := ExecuteRequest(http.MethodPost, baseUrl, CreateKnowledgeTestExperienceCategoryUri, in, &respData)
	if err != nil {
		return respData, err
	}
	if respData.Code != 0 {
		return respData, errors.New(respData.Message)
	}
	return respData, nil
}

func DeleteKnowledgeTestExperienceCategory(baseUrl string, in DeleteKnowledgeTestExperienceCategoryReq) (ResponseData[any], error) {
	var respData ResponseData[any]
	err := ExecuteRequest(http.MethodDelete, baseUrl, DeleteKnowledgeTestExperienceCategoryUri, in, &respData)
	if err != nil {
		return respData, err
	}
	if respData.Code != 0 {
		return respData, errors.New(respData.Message)
	}
	return respData, nil
}

func GetBlackBoxCaseEstimatedProgress(baseUrl string, in GetBlackBoxCaseEstimatedProgressReq) (ResponseData[*GetBlackBoxCaseEstimatedProgressResp], error) {
	var respData ResponseData[*GetBlackBoxCaseEstimatedProgressResp]
	values, err := query.Values(in)
	if err != nil {
		logx.Errorf("GetBlackBoxCaseEstimatedProgress convert data error:%v", err)
		return ResponseData[*GetBlackBoxCaseEstimatedProgressResp]{}, err
	}

	err = ExecuteRequest(http.MethodGet, baseUrl, GetAiStateCaseMapProgressUri+"?"+values.Encode(), nil, &respData)
	if err != nil {
		return respData, err
	}
	if respData.Code != 0 {
		return respData, errors.New(respData.Message)
	}
	return respData, nil
}

func ListBlackBoxCaseMapDocuments(baseUrl string, in ListBlackBoxCaseMapDocumentsReq) (ResponseData[any], error) {
	var respData ResponseData[any]
	values, err := query.Values(in)
	if err != nil {
		logx.Errorf("ListBlackBoxCaseMapDocuments convert data error:%v", err)
		return ResponseData[any]{}, err
	}

	err = ExecuteRequest(http.MethodGet, baseUrl, ListAiStateCaseMapDocumentsUri+"?"+values.Encode(), nil, &respData)
	if err != nil {
		return respData, err
	}
	if respData.Code != 0 {
		return respData, errors.New(respData.Message)
	}
	return respData, nil
}

func GetBlackBoxCaseUsedKnowledge(baseUrl string, in GetBlackBoxCaseUsedKnowledgeReq) (ResponseData[any], error) {
	var respData ResponseData[any]
	values, err := query.Values(in)
	if err != nil {
		logx.Errorf("GetBlackBoxCaseUsedKnowledge convert data error:%v", err)
		return ResponseData[any]{}, err
	}

	err = ExecuteRequest(http.MethodGet, baseUrl, GetAiStateCaseUsedCaseKnowledgeUri+"?"+values.Encode(), nil, &respData)
	if err != nil {
		return respData, err
	}
	if respData.Code != 0 {
		return respData, errors.New(respData.Message)
	}
	return respData, nil
}

func GetBlackBoxCaseMind(baseUrl string, in GetBlackBoxCaseMindReq) (ResponseData[string], error) {
	var respData ResponseData[string]

	err := ExecuteRequest(http.MethodPost, baseUrl, GetTestCaseMindUri, in, &respData)
	if err != nil {
		return respData, err
	}
	if respData.Code != 0 {
		return respData, errors.New(respData.Message)
	}
	return respData, nil
}
func CreateBaseReviewMap(baseUrl string, in CreateBlackBoxBaseReviewMapReq) (ResponseData[any], error) {
	var respData ResponseData[any]

	err := ExecuteRequest(http.MethodPost, baseUrl, CreateBaseReviewMapUri, in, &respData)
	if err != nil {
		logx.Errorf("CreateBaseReviewMap error:%v", err)
		return respData, err
	}
	if respData.Code != 0 {
		return respData, errors.New(respData.Message)
	}
	return respData, nil
}

func CreateFuncReviewMap(baseUrl string, in CreateBlackBoxFuncReviewMapReq) (ResponseData[any], error) {
	var respData ResponseData[any]

	err := ExecuteRequest(http.MethodPost, baseUrl, CreateFuncReviewMapUri, in, &respData)
	if err != nil {
		logx.Errorf("CreateFuncReviewMap error:%v", err)
		return respData, err
	}
	if respData.Code != 0 {
		return respData, errors.New(respData.Message)
	}
	return respData, nil
}

func CreateTestSceneReviewMap(baseUrl string, in CreateBlackBoxTestSceneReviewMapReq) (ResponseData[any], error) {
	var respData ResponseData[any]
	err := ExecuteRequest(http.MethodPost, baseUrl, CreateTestSceneReviewMapUri, in, &respData)
	if err != nil {
		logx.Errorf("CreateTestSceneReviewMap error:%v", err)
		return respData, err
	}
	if respData.Code != 0 {
		return respData, errors.New(respData.Message)
	}
	return respData, nil
}

func UpdateTestSceneReviewMap(baseUrl string, in UpdateBlackBoxTestSceneReviewMapReq) (ResponseData[any], error) {
	var respData ResponseData[any]
	err := ExecuteRequest(http.MethodPost, baseUrl, UpdateTestSceneReviewMapUri, in, &respData)
	if err != nil {
		logx.Errorf("CreateTestSceneReviewMap error:%v", err)
		return respData, err
	}
	if respData.Code != 0 {
		return respData, errors.New(respData.Message)
	}
	return respData, nil
}

func CreateTestCase(baseUrl string, in CreateBlackBoxTestCaseReq) (ResponseData[[]*pb.BlackBoxCaseData], error) {
	var respData ResponseData[[]*pb.BlackBoxCaseData]
	err := ExecuteRequest(http.MethodPost, baseUrl, CreateTestCaseUri, in, &respData)
	if err != nil {
		logx.Errorf("CreateTestCase error:%v", err)
		return respData, err
	}
	if respData.Code != 0 {
		return respData, errors.New(respData.Message)
	}
	return respData, nil
}

func UpdateTestCaseWithV24(baseUrl string, in UpdateBlackBoxTestCaseReq) (ResponseData[[]*pb.BlackBoxCaseData], error) {
	var respData ResponseData[[]*pb.BlackBoxCaseData]
	err := ExecuteRequest(http.MethodPost, baseUrl, UpdateTestCaseUri, in, &respData)
	if err != nil {
		return respData, err
	}
	if respData.Code != 0 {
		return respData, errors.New(respData.Message)
	}
	return respData, nil
}

func Json2MDV24(baseUrl string, in Json2MDV24Req) (ResponseData[string], error) {
	var respData ResponseData[string]
	// 新版本用2.4
	in.Version = "2.4"
	err := ExecuteRequest(http.MethodPost, baseUrl, Json2MDV24Uri, in, &respData)
	if err != nil {
		return respData, err
	}
	if respData.Code != 0 {
		return respData, errors.New(respData.Message)
	}

	return respData, nil
}

// GetFuncCoverage 获取功能点覆盖率
func GetFuncCoverage(baseUrl string, in GetFuncCoverageReq) (ResponseData[GetFuncCoverageResp], error) {
	var respData ResponseData[GetFuncCoverageResp]
	err := ExecuteRequest(http.MethodPost, baseUrl, GetFuncCoverageUri, in, &respData)
	if err != nil {
		return respData, err
	}

	return respData, nil
}

// GetDocumentSize 获取文档size
func GetDocumentSize(baseUrl string, in GetDocumentSizeReq) (ResponseData[GetDocumentSizeResp], error) {
	var (
		respData       ResponseData[GetDocumentSizeResp]
		nativeRespData ResponseData[string]
	)

	respData.Data = &GetDocumentSizeResp{}

	err := ExecuteRequest(http.MethodPost, baseUrl, GetDocumentSizeUri, in, &nativeRespData)
	if err != nil {
		return respData, err
	}

	respData.Data.Size = *nativeRespData.Data

	return respData, nil
}

// GetUpdatedCaseAddUpdateCount 获取修改用例的条数
func GetUpdatedCaseAddUpdateCount(baseUrl string, in GetUpdatedCaseAddUpdateCountReq) (ResponseData[GetUpdatedCaseAddUpdateCountResp], error) {
	var (
		respData ResponseData[GetUpdatedCaseAddUpdateCountResp]
	)

	err := ExecuteRequest(http.MethodPost, baseUrl, GetUpdatedCaseAddCountUri, in, &respData)
	if err != nil {
		return respData, err
	}

	return respData, nil
}

func CreateExtractDemandPoints(baseUrl string, in CreateExtractDemandPointsReq) {

}

func SummarizeCaseKnowledgeDoc(baseUrl string, messageReq *SummarizeCaseDocReq, stream *sse.Stream) error {
	bodyBytes, err := json.Marshal(messageReq)
	if err != nil {
		setStreamError(stream, "参数解析失败")
		return err
	}

	logx.Info(
		fmt.Sprintf(
			"request info:%s - method:%s url:%s",
			time.Now().Format(time.RFC3339), // 使用RFC3339格式化时间
			"POST",
			baseUrl+SummarizeCaseDocUri,
		),
	)
	println(string(bodyBytes))

	req, err := http.NewRequest("POST", baseUrl+SummarizeCaseDocUri, bytes.NewReader(bodyBytes))
	if err != nil {
		setStreamError(stream, "接口异常初始化，请稍后重试")
		return err
	}
	req.Header.Set("Content-Type", "application/json")
	client := &http.Client{}
	resp, err := client.Do(req)
	defer func(r *http.Response) {
		if r != nil {
			_ = r.Body.Close()
		}
	}(resp)
	if err != nil {
		setStreamError(stream, "接口异常，请稍后重试")
		return fmt.Errorf("error sending: %s", err)
	}

	err = resp2Stream(resp, stream, "")
	if err != nil {
		return err
	}
	return nil
}
