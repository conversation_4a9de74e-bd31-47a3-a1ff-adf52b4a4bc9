package httpc

import (
	"bufio"
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net"
	"net/http"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/logx"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sse"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/model"
)

func ExecuteRequest[T any](
	method, baseUrl, endpoint string, requestBody any, responseData *ResponseData[T],
) error {
	// 记录开始时间
	start := time.Now()
	jsonValue, err := json.Marshal(requestBody)
	if err != nil {
		return err
	}

	url := baseUrl + endpoint
	req, err := http.NewRequest(method, url, bytes.NewBuffer(jsonValue))
	if err != nil {
		return err
	}
	req.Header.Set("Content-Type", "application/json")
	logx.Info(
		fmt.Sprintf(
			"request info:%s - method:%s url:%s remoteAddr:%s",
			time.Now().Format(time.RFC3339), // 使用RFC3339格式化时间
			req.Method,
			req.URL.String(),
			req.RemoteAddr,
		),
	)
	println(string(jsonValue))

	client := &http.Client{
		Transport: &http.Transport{
			Proxy: http.ProxyFromEnvironment,
			DialContext: (&net.Dialer{
				Timeout:   5 * time.Minute, // 连接超时时间
				KeepAlive: 5 * time.Minute, // TCP KeepAlive 时间
			}).DialContext,
			ForceAttemptHTTP2:     true,
			DisableKeepAlives:     true,
			MaxIdleConns:          100,
			IdleConnTimeout:       90 * time.Second,
			TLSHandshakeTimeout:   10 * time.Second,
			ExpectContinueTimeout: 1 * time.Second,
		},
	}
	resp, err := client.Do(req)
	defer func(r *http.Response) {
		if r != nil {
			_ = r.Body.Close()
		}
	}(resp)
	if err != nil {
		return err
	}
	if resp.StatusCode >= 400 {
		bodyBytes, _ := io.ReadAll(resp.Body)
		_ = json.Unmarshal(bodyBytes, responseData)
		logx.Error(fmt.Sprintf("response parse body: %v", responseData))
		return fmt.Errorf("%s request failed", responseData.Message+": "+resp.Status)
	}

	bodyBytes, err := io.ReadAll(resp.Body)
	if err != nil {
		return err
	}

	err = json.Unmarshal(bodyBytes, responseData)
	if err != nil {
		return err
	}

	if responseData.Code != 0 {
		return fmt.Errorf("server responded with error: %s", responseData.Message)
	}
	// 记录结束时间
	end := time.Now()
	// 计算耗时
	elapsed := end.Sub(start)
	// 打印耗时
	fmt.Printf("调用外部接口操作耗时: %s: %s: %v\n", baseUrl, endpoint, elapsed)
	return nil
}

// CreateDocument 新增文档
func CreateDocument(baseUrl string, document *model.BlackBoxCaseDocument) error {
	documentType := "text"
	var documentContent string
	if document.DocumentType > 0 {
		if document.DocumentUrl.Valid {
			documentContent = document.DocumentUrl.String
		}
		if document.DocumentType == 1 {
			documentType = "feishu"
		}
		if document.DocumentType == 2 {
			documentType = "doc"
		}
	} else {
		if document.DocumentText.Valid {
			documentContent = document.DocumentText.String
		}
	}

	req := &CreateDocReq{
		DocId:   document.DocumentId,
		Title:   document.DocumentName,
		Type:    documentType,
		Content: documentContent,
	}

	var responseData ResponseData[string]
	err := ExecuteRequest(http.MethodPost, baseUrl, "/api/v1/test-case/document/create", req, &responseData)
	if err != nil {
		return err
	}
	return nil
}

// UpdateDocument 更新文档
func UpdateDocument(baseUrl string, document *model.BlackBoxCaseDocument) error {
	req := &UpdateDocReq{
		DocId: document.DocumentId,
		Title: document.DocumentName,
	}

	var responseData ResponseData[string]
	err := ExecuteRequest(http.MethodPost, baseUrl, "/api/v1/test-case/document/update", req, &responseData)
	if err != nil {
		return err
	}
	return nil
}

func ReloadDocument(baseUrl string, documentId string) error {
	req := &ReloadDocReq{
		DocumentId: documentId,
	}
	var responseData ResponseData[string]
	err := ExecuteRequest(http.MethodPut, baseUrl, "/api/v1/test-case/document/reload", req, &responseData)
	if err != nil {
		return err
	}
	return nil
}

// DeleteDocument 删除文档
func DeleteDocument(baseUrl string, documentId string) error {
	req := &DeleteDocReq{DocId: documentId}

	var responseData ResponseData[string]
	err := ExecuteRequest(http.MethodDelete, baseUrl, "/api/v1/test-case/document/delete", req, &responseData)
	if err != nil {
		return err
	}
	return nil
}

func BatchDeleteDocument(baseUrl string, documentIds []string) error {
	req := &BatchDeleteDocReq{DocIds: documentIds}
	var responseData ResponseData[string]
	err := ExecuteRequest(http.MethodDelete, baseUrl, "/api/v1/test-case/document/delete/batch", req, &responseData)
	if err != nil {
		return err
	}
	return nil
}

// CreateSession 创建会话
func CreateSession(baseUrl string, session *model.BlackBoxCaseSession) error {
	req := &CreateSessionReq{
		SessionId:        session.SessionId,
		SessionName:      session.SessionName,
		SessionMode:      "accuracy",
		SessionRound:     session.SessionRound,
		KnowledgeContext: []string{},
	}

	var responseData ResponseData[string]
	err := ExecuteRequest(http.MethodPost, baseUrl, "/api/v1/test-case/session/create", req, &responseData)
	if err != nil {
		return err
	}
	return nil
}

// UpdateSession 更新会话
func UpdateSession(baseUrl string, session *model.BlackBoxCaseSession) error {
	req := &UpdateSessionReq{
		SessionId:        session.SessionId,
		SessionName:      session.SessionName,
		SessionMode:      "accuracy",
		SessionRound:     session.SessionRound,
		KnowledgeContext: []string{},
	}

	var responseData ResponseData[string]
	err := ExecuteRequest(http.MethodPost, baseUrl, "/api/v1/test-case/session/update", req, &responseData)
	if err != nil {
		return err
	}
	return nil
}

func DeleteSession(baseUrl string, sessionId string) error {
	req := &DeleteSessionReq{SessionId: sessionId}
	var responseData ResponseData[string]
	err := ExecuteRequest(http.MethodDelete, baseUrl, "/api/v1/test-case/session/delete", req, &responseData)
	if err != nil {
		return err
	}
	return nil
}

func BatchDeleteSession(baseUrl string, sessionIds []string) error {
	req := &BatchDeleteSessionReq{SessionIds: sessionIds}
	var responseData ResponseData[string]
	err := ExecuteRequest(http.MethodDelete, baseUrl, "/api/v1/test-case/session/delete/batch", req, &responseData)
	if err != nil {
		return err
	}
	return nil
}

func CreateDocumentSessionRelation(baseUrl string, sessionId string, documentId string) error {
	req := &SessionDocumentReq{SessionId: sessionId, DocumentId: documentId}
	var responseData ResponseData[string]
	err := ExecuteRequest(http.MethodPost, baseUrl, "/api/v1/test-case/document/session/relation", req, &responseData)
	if err != nil {
		return err
	}
	return nil
}

func DeleteDocumentSessionRelation(baseUrl string, sessionId string, documentId string) error {
	req := &SessionDocumentReq{SessionId: sessionId, DocumentId: documentId}
	var responseData ResponseData[string]
	err := ExecuteRequest(http.MethodDelete, baseUrl, "/api/v1/test-case/document/session/relation", req, &responseData)
	if err != nil {
		return err
	}
	return nil
}

func GetSessionHistoryList(baseUrl string, sessionId string) (error, ResponseData[[]*Message]) {
	req := &MsgHistoryForSessionReq{SessionId: sessionId}
	var responseData ResponseData[[]*Message]
	err := ExecuteRequest(http.MethodGet, baseUrl, "/api/v1/test-case/session/history/list", req, &responseData)
	if err != nil {
		return err, responseData
	}
	return nil, responseData
}

func GetBlackBoxCaseConversationAIState(baseUrl string, in *pb.GetBlackBoxCaseConversationAIStateReq) (
	error, ResponseData[*pb.GetBlackBoxCaseConversationAIStateResp],
) {
	var responseData ResponseData[*pb.GetBlackBoxCaseConversationAIStateResp]
	err := ExecuteRequest(http.MethodGet, baseUrl, "/api/v1/test-case/conversation/ai/state/get", in, &responseData)
	if err != nil {
		return err, responseData
	}
	return nil, responseData
}

func GetBlackBoxCaseConversationAIMessage(baseUrl string, in *pb.GetBlackBoxCaseConversationAIMessageReq) (
	error, ResponseData[*pb.GetBlackBoxCaseConversationAIMessageResp],
) {
	var responseData ResponseData[*pb.GetBlackBoxCaseConversationAIMessageResp]
	err := ExecuteRequest(http.MethodGet, baseUrl, "/api/v1/test-case/conversation/ai/message/get", in, &responseData)
	if err != nil {
		return err, responseData
	}
	return nil, responseData
}

func GetBlackBoxCaseAIMessage(baseUrl string, in *pb.GetCreateBlackBoxCaseAIMessageReq) (error, ResponseData[string]) {
	var responseData ResponseData[string]
	err := ExecuteRequest(http.MethodGet, baseUrl, "/api/v1/test-case/ai/message/get", in, &responseData)
	if err != nil {
		return err, responseData
	}
	return nil, responseData
}

func GetBlackBoxCaseDocumentHeadersList(baseUrl string, in *pb.GetBlackBoxCaseDocumentHeadersListReq) (
	error, ResponseData[[]string],
) {
	var responseData ResponseData[[]string]
	err := ExecuteRequest(http.MethodGet, baseUrl, "/api/v1/test-case/document/headers/list/get", in, &responseData)
	if err != nil {
		return err, responseData
	}
	return nil, responseData
}

func GetBlackBoxCaseModifyTestCaseReferenceContentList(
	baseUrl string, in *pb.GetBlackBoxCaseModifyTestCaseReferenceContentListReq,
) (error, ResponseData[*pb.GetBlackBoxCaseModifyTestCaseReferenceContentListResp]) {
	var responseData ResponseData[*pb.GetBlackBoxCaseModifyTestCaseReferenceContentListResp]
	err := ExecuteRequest(http.MethodGet, baseUrl, "/api/v1/test-case/modify-case/get", in, &responseData)
	if err != nil {
		return err, responseData
	}
	return nil, responseData
}

func MergeBlackBoxCase(baseUrl string, in *pb.MergeBlackBoxCaseReq) (
	ResponseData[[]*pb.BlackBoxCaseSessionMessage], error,
) {
	var responseData ResponseData[[]*pb.BlackBoxCaseSessionMessage]
	err := ExecuteRequest(http.MethodPost, baseUrl, "/api/v1/test-case/merge-case/create", in, &responseData)
	if err != nil {
		return responseData, err
	}
	return responseData, nil
}

func CreateBlackBoxCaseModifyTestCaseReferenceContent(
	baseUrl string, in *pb.CreateBlackBoxCaseModifyTestCaseReferenceContentReq,
) error {
	var responseData ResponseData[string]
	err := ExecuteRequest(
		http.MethodPost, baseUrl, "/api/v1/test-case/modify-case/relate-doc/create", in, &responseData,
	)
	if err != nil {
		return err
	}
	return nil
}

func ReplaceBlackBoxCase(baseUrl string, in *pb.ReplaceBlackBoxCaseReq) error {
	var responseData ResponseData[string]
	err := ExecuteRequest(http.MethodPost, baseUrl, "/api/v1/test-case/modify-case/replace", in, &responseData)
	if err != nil {
		return err
	}
	return nil
}

func TransferBlackBoxCase2XMind(baseUrl string, in *pb.TransferBlackBoxCase2XMindReq) (error, ResponseData[string]) {
	var responseData ResponseData[string]
	mindType := "case"
	if in.MindType == 1 {
		mindType = "review"
	}
	req := &TransferBlackBoxCase2Req{
		ConversationId: in.ConversationId,
		MindType:       mindType,
	}
	err := ExecuteRequest(http.MethodPost, baseUrl, "/api/v1/test-case/x-mind/transfer", req, &responseData)
	if err != nil {
		return err, responseData
	}
	return nil, responseData
}

func DeleteSessionHistory(baseUrl string, sessionId string) error {
	req := &MsgHistoryForSessionReq{SessionId: sessionId}
	var responseData ResponseData[string]
	err := ExecuteRequest(http.MethodDelete, baseUrl, "/api/v1/test-case/session/history/clear", req, &responseData)
	if err != nil {
		return err
	}
	return nil
}

func SendMessage(baseUrl string, messageReq *SendMsgForSessionReq, stream *sse.Stream) error {
	bodyBytes, err := json.Marshal(messageReq)
	if err != nil {
		setStreamError(stream, "接口异常，请稍后重试。")
		return err
	}

	req, err := http.NewRequest("POST", baseUrl+"/api/v1/test-case/session/message/stream", bytes.NewReader(bodyBytes))
	if err != nil {
		setStreamError(stream, "接口异常，请稍后重试。")
		return err
	}
	req.Header.Set("Content-Type", "application/json")
	client := &http.Client{}
	resp, err := client.Do(req)
	defer func(r *http.Response) {
		if r != nil {
			_ = r.Body.Close()
		}
	}(resp)
	if err != nil {
		setStreamError(stream, "接口异常，请稍后重试。")
		return fmt.Errorf("error sending: %s", err)
	}

	err = dealStreamResult(resp, stream)
	if err != nil {
		return err
	}
	return nil
}

func UpdateTestCase(baseUrl string, messageReq *UpdateTestCaseReq, stream *sse.Stream) error {
	bodyBytes, err := json.Marshal(messageReq)
	if err != nil {
		return err
	}

	req, err := http.NewRequest("POST", baseUrl+"/api/v1/test-case/modify-case/create", bytes.NewReader(bodyBytes))
	if err != nil {
		return err
	}
	req.Header.Set("Content-Type", "application/json")
	client := &http.Client{}
	resp, err := client.Do(req)
	defer func(r *http.Response) {
		if r != nil {
			_ = r.Body.Close()
		}
	}(resp)
	if err != nil {
		return errors.Errorf("Error sending: %s", err)
	}

	err = dealStreamResult(resp, stream)
	if err != nil {
		return err
	}
	return nil
}

func ReCreateTestCase(baseUrl string, messageReq *ReCreateTestCaseReq, stream *sse.Stream) error {
	bodyBytes, err := json.Marshal(messageReq)
	if err != nil {
		setStreamError(stream, "参数解析失败")
		return err
	}

	req, err := http.NewRequest(
		"POST", baseUrl+"/api/v1/test-case/session/message/recreate", bytes.NewReader(bodyBytes),
	)
	if err != nil {
		setStreamError(stream, "接口异常，请稍后重试")
		return err
	}
	req.Header.Set("Content-Type", "application/json")
	client := &http.Client{}
	resp, err := client.Do(req)
	defer func(r *http.Response) {
		if r != nil {
			_ = r.Body.Close()
		}
	}(resp)
	if err != nil {
		setStreamError(stream, "接口异常，请稍后重试")
		return fmt.Errorf("error sending: %s", err)
	}

	err = dealStreamResult(resp, stream)
	if err != nil {
		return err
	}
	return nil
}

func dealStreamResult(resp *http.Response, stream *sse.Stream) error {
	if resp.StatusCode != http.StatusOK {
		setStreamError(stream, "接口异常，请稍后重试。"+resp.Status)
		return fmt.Errorf("unexpected status code: %d", resp.StatusCode)
	}

	reader := bufio.NewReader(resp.Body)
	for {
		line, err := reader.ReadString('\n')
		if err != nil {
			break
		}
		if strings.HasPrefix(line, "data:") && strings.HasSuffix(line, "\n") {
			data := strings.TrimPrefix(line[len("data: "):], "\n")
			var resp ResponseData[SendMsgForSessionResp]
			_ = json.Unmarshal([]byte(data), &resp)
			if resp.Code == 0 {
				stream.Event <- &sse.Event{
					ID:   []byte(stream.ID),
					Data: jsonx.MarshalIgnoreError(&resp.Data),
				}
			} else {
				stream.Event <- &sse.Event{
					ID:   []byte(stream.ID),
					Data: jsonx.MarshalIgnoreError(&resp),
				}
			}
		}
	}
	return nil
}

func setStreamError(stream *sse.Stream, message string) {
	var resp ResponseData[SendMsgForSessionResp]
	resp.Code = 1
	resp.Message = message
	stream.Event <- &sse.Event{
		ID:   []byte(stream.ID),
		Data: jsonx.MarshalIgnoreError(&resp),
	}
}

// MapToQueryString 将map[string]string转换为URL查询字符串
func MapToQueryString(params map[string]string, uri string) string {
	if len(params) == 0 {
		return uri
	}

	var pairs []string
	for k, v := range params {
		// 使用url.QueryEscape对键和值进行URL编码
		// encodedKey := url.QueryEscape(k)
		// encodedValue := url.QueryEscape(v)
		pairs = append(pairs, fmt.Sprintf("%s=%s", k, v))
	}

	// 使用&连接所有的键值对
	return uri + "?" + strings.Join(pairs, "&")
}
