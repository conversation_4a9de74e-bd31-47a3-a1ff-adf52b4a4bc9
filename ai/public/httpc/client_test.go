package httpc

import (
	"context"
	"fmt"
	"net/http"
	"strings"
	"testing"

	"github.com/zeromicro/go-zero/core/jsonx"
	"github.com/zeromicro/go-zero/rest/httpc"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/userinfo"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/codes"
)

const (
	externalCreateDocURlTest = "/ai/v1/black/box/case/assistant/search"
)

type BlackBoxCaseAssistant struct {
	Id                   int                    `json:"id" zh:"自增Id"`
	ProjectId            string                 `json:"project_id" zh:"项目Id"`
	AssistantId          string                 `json:"assistant_id" zh:"助手Id"`
	AssistantName        string                 `json:"assistant_name" zh:"助手名称"`
	AssistantDescription string                 `json:"assistant_description" zh:"助手描述"`
	Account              string                 `json:"account" zh:"用户id【仅在个人空间存在此数据】"`
	WorkSpaceType        int8                   `json:"work_space_type" zh:"工作空间类型：个人空间(private)：0，公共空间()：1"`
	WorkPriorityType     int8                   `json:"work_priority_type" zh:"工作优先级类型：准确优先：0，速度优先：1"`
	SessionRound         int                    `json:"session_round" zh:"对话轮数"`
	CreatedBy            *userinfo.FullUserInfo `json:"created_by" zh:"创建者的用户Id"`
	UpdatedBy            *userinfo.FullUserInfo `json:"updated_by" zh:"最近一次更新者的用户Id"`
	DeletedBy            *userinfo.FullUserInfo `json:"deleted_by" zh:"删除者的用户Id"`
	CreatedAt            int64                  `json:"created_at" zh:"创建时间"`
	UpdatedAt            int64                  `json:"updated_at" zh:"更新时间"`
	DeletedAt            int64                  `json:"deleted_at" zh:"删除时间"`
}

type SearchBlackBoxCaseAssistantReq struct {
	ProjectId string `json:"project_id" validate:"required" zh:"项目Id"`
}

type SearchBlackBoxCaseAssistantResp struct {
	Items []*BlackBoxCaseAssistant `json:"items"`
}

func TestName(t *testing.T) {
	err := CreateDocTest(context.Background())
	if err != nil {
		t.Fatal(err)
	}
}

func CreateDocTest(ctx context.Context) error {
	// url := "http://probe-test.ttyuyin.com/"
	url := "http://localhost:21001/"
	assistantReq := SearchBlackBoxCaseAssistantReq{ProjectId: "roject_id:Kqllt5-9fA-I5UOdhjA5d"}
	marshal, _ := jsonx.Marshal(&assistantReq)
	req, _ := http.NewRequest(
		http.MethodPost, url+externalCreateDocURlTest,
		strings.NewReader(string(marshal)),
	)
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set(
		"X-Auth",
		"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE3MTU1ODY0NjksIm5iZiI6MTcxNDk4MTY2OSwiaWF0IjoxNzE0OTgxNjY5LCJqdGkiOiJ3ZEtfUXF1OHhseWpBRjJxaFpjVWMiLCJhY2NvdW50IjoiVDQ0NDAiLCJmdWxsbmFtZSI6IuadqOWNmiIsImRlcHRfbmFtZSI6IuW5s-WPsOeglOWPkee7hCIsImVtYWlsIjoieWFuZ2JvQDUydHQuY29tIiwibW9iaWxlIjoiIiwicmVmcmVzaF9hZnRlciI6MTcxNTU4NjQ2OH0.GLTwLK9XnYwGjP7L0Bd_v1BdaHMBpJsmU_l1372j5kc",
	)
	resp, err := httpc.DoRequest(req.WithContext(ctx))
	if err != nil {
		fmt.Println("Error:", err)
		return err
	}
	respObj := NewResponseData(SearchBlackBoxCaseAssistantResp{})
	err = httpc.Parse(resp, respObj)
	if err != nil {
		fmt.Println("Error:", err)
		return err
	}
	if respObj.Code != ResponseDataCodeSuccess {
		return errorx.Err(codes.ExternalFailure, respObj.Message)
	}
	fmt.Println(respObj)
	return nil
}
