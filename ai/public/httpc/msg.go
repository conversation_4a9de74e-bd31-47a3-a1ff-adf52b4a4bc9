package httpc

import (
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
)

const (
	ResponseDataCodeSuccess = 0 // 成功

)

type ResponseData[T any] struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
	Data    *T     `json:"data,omitempty,optional"`
}

func NewResponseData[T any](t T) *ResponseData[T] {
	return &ResponseData[T]{
		Data: &t,
	}
}

// CreateDocReq 文档请求
type CreateDocReq struct {
	DocId   string `json:"doc_id"`
	Title   string `json:"title"`
	Type    string `json:"type"`
	Content string `json:"content"`
}

type UpdateDocReq struct {
	DocId string `json:"doc_id"`
	Title string `json:"title"`
}

type ReloadDocReq struct {
	DocumentId string `json:"document_id"`
}

type DeleteDocReq struct {
	DocId string `json:"doc_id"`
}

type BatchDeleteDocReq struct {
	DocIds []string `json:"doc_ids"`
}

// CreateSessionReq 会话请求
type CreateSessionReq struct {
	SessionId        string   `json:"session_id"`
	SessionName      string   `json:"session_name"`
	SessionMode      string   `json:"session_mode"`
	SessionRound     int64    `json:"session_round"`
	KnowledgeContext []string `json:"knowledge_context"`
}

type SessionDocumentReq struct {
	SessionId  string `json:"session_id"`
	DocumentId string `json:"doc_id"`
}

type UpdateSessionReq struct {
	SessionId        string   `json:"session_id"`
	SessionName      string   `json:"session_name"`
	SessionMode      string   `json:"session_mode"`
	SessionRound     int64    `json:"session_round"`
	KnowledgeContext []string `json:"knowledge_context"`
}

type DeleteSessionReq struct {
	SessionId string `json:"session_id"`
}

type BatchDeleteSessionReq struct {
	SessionIds []string `json:"session_ids"`
}

type RemoveMsgHistoryForSessionReq struct {
	SessionId string `json:"session_id"`
}

type SendMsgForSessionReq struct {
	SessionId      string `json:"session_id"`
	ConversationId string `json:"conversation_id"`
	Message        string `json:"message"`
}

type CreateTwCaseReq struct {
	MindId int64  `json:"mind_id" zh:"脑图ID"`
	CaseId string `json:"case_id" zh:"用例ID"`
}

type CreateCaseData struct {
	Content string `json:"content"`
	CaseId  string `json:"case_id"`
}

type CreateCaseDataResp struct {
	Content    string `json:"content"`
	CaseRefId  string `json:"case_ref_id"`
	RevisionId string `json:"revision_id"`
}

type SendMsgForSessionResp struct {
	Message *Message `json:"message"`
	Event   string   `json:"event" zh:"事件类型 [start | running | done]"`
}

type UpdateTestCaseReq struct {
	ConversationId   string `json:"conversation_id"`
	ModifySuggestion string `json:"modify_suggestion"`
}

type ReCreateTestCaseReq struct {
	ConversationId string `json:"conversation_id"`
}

type CreateTestCaseDataReq struct {
	CaseId                    string `json:"case_id"`
	Revision                  string `json:"revision_id"`
	KnowledgeId               string `json:"knowledge_id"`
	KnowledgeParagraphTitle   string `json:"knowledge_paragraph_title"`
	KnowledgeParagraphTitleId int64  `json:"knowledge_paragraph_title_id"`
	ContinueToWrite           int64  `json:"continue_to_write"`
	ModelCharacter            string `json:"model_character"`
}

type EditTestCaseDataReq struct {
	CaseId                    string `json:"case_id"`
	Revision                  string `json:"revision_id"`
	KnowledgeId               string `json:"knowledge_id"`
	KnowledgeParagraphTitle   string `json:"knowledge_paragraph_title"`
	KnowledgeParagraphTitleId int64  `json:"knowledge_paragraph_title_id"`
	CaseTable                 any    `json:"case_table"`
	Suggestions               string `json:"suggestions"`
	ReferenceDoc              any    `json:"reference_doc"`
	EnableReference           bool   `json:"enable_reference"`
	ContinueToWrite           int64  `json:"continue_to_write"`
	ModelCharacter            string `json:"model_character"`
}

type PartialEditTestCaseDataReq struct {
	CaseId                    string              `json:"case_id"`
	Revision                  string              `json:"revision_id"`
	KnowledgeId               string              `json:"knowledge_id"`
	KnowledgeParagraphTitle   string              `json:"knowledge_paragraph_title"`
	KnowledgeParagraphTitleId int64               `json:"knowledge_paragraph_title_id"`
	CaseTable                 []*BlackBoxCaseData `json:"case_table"`
	Suggestions               string              `json:"suggestions"`
	ReferenceDoc              interface{}         `json:"reference_doc"`
	EnableReference           bool                `json:"enable_reference"`
	ContinueToWrite           int64               `json:"continue_to_write"`
	ModelCharacter            string              `json:"model_character"`
}

type CreateKnowledgeReq struct {
	KnowledgeId string `json:"knowledge_id"`
	Title       string `json:"title"`
	Url         string `json:"url"`
}
type CaseTableReq struct {
	KnowledgeId               string                 `json:"knowledge_id"`
	KnowledgeParagraphTitle   string                 `json:"knowledge_paragraph_title"`
	KnowledgeParagraphTitleId int64                  `json:"knowledge_paragraph_title_id"`
	CaseTable                 []*pb.BlackBoxCaseData `json:"case_table"`
}

type MergeCasesReq struct {
	// KnowledgeId             string                 `json:"knowledge_id"`
	CaseTable [][]*BlackBoxCaseData `json:"case_table"`
}

type GetCaseJsonResp struct {
	CaseTable []*pb.BlackBoxCaseData `json:"case_table"`
}

type BlackBoxCaseData struct {
	OrderId      string `json:"order_id" zh:"序号"`
	CaseName     string `json:"case_name" zh:"用例名称"`
	Requirement  string `json:"requirement" zh:"需求名称"`
	PreCondition string `json:"pre_condition" zh:"前置条件"`
	CaseStep     string `json:"case_step" zh:"用例步骤"`
	ExpectResult string `json:"expect_result" zh:"预期结果"`
	Terminal     string `json:"terminal" zh:"终端"`
	CaseLevel    string `json:"case_level" zh:"用例等级"`
	Tag          string `json:"tag" zh:"标识"`
}

type GetAIStateResp struct {
	State          string `json:"state" zh:"状态"`
	StateDesc      string `json:"state_desc" zh:"状态描述"`
	ExtractContent string `json:"extract_content" zh:"内容"`
}

type ReloadKnowledgeReq struct {
	KnowledgeId string `json:"knowledge_id"`
}

type ReorderCaseDataReq struct {
	KnowledgeId string                 `json:"knowledge_id"`
	CaseTable   []*pb.BlackBoxCaseData `json:"case_table"`
}

type ListKnowledgeResp struct {
	KnowledgeId            string `json:"knowledge_id"`
	FeishuUrl              string `json:"feishu_url"`
	JsonParagraphTitle     string `json:"json_paragraph_title"`
	Status                 string `json:"status"`
	CreatedAt              string `json:"created_at"`
	UpdatedAt              string `json:"updated_at"`
	MarkdownParagraphTitle string `json:"markdown_paragraph_title"`
	Title                  string `json:"title"`
	FeishuTitle            string `json:"feishu_title"`
	Markdown               string `json:"markdown"`
	ErrorMsg               string `json:"error_msg"`
	Id                     int64  `json:"id"`
}

type Message struct {
	Content  MsgContent  `json:"content" zh:"消息内容"`
	Metadata MsgMetadata `json:"metadata" zh:"消息内容"`
}

type MsgContent struct {
	Human string `json:"human" zh:"human"`
	Ai    string `json:"ai" zh:"ai"`
}

type MsgState struct {
	State     string `json:"state" zh:"state"`
	StateDesc string `json:"state_desc" zh:"state_desc"`
}

type MsgMetadata struct {
	ContentType    string   `json:"content_type" zh:"内容类型 table, text"`
	SessionId      string   `json:"session_id" zh:"会话Id"`
	CreatedAt      string   `json:"created_at" zh:"创建时间"`
	ConversationId string   `json:"conversation_id" zh:"对话id"`
	State          MsgState `json:"state" zh:"状态"`
}

type MsgHistoryForSessionReq struct {
	SessionId string `json:"session_id" zh:"会话Id"`
}

// MsgHistoryForSessionResp 历史
type MsgHistoryForSessionResp struct {
	Message []*Message `json:"data"`
}

type TransferBlackBoxCase2Req struct {
	ConversationId string `json:"conversation_id" zh:"conversation_id"`
	MindType       string `json:"mind_type" zh:"mind_type"`
}

type CreateTwBetaMindReq struct {
	CaseId        string                            `json:"case_id"`
	CaseVersionId string                            `json:"case_version_id"`
	AcCheckList   []*pb.BlackBoxCaseTwBetaMindCheck `json:"ac_check_list"`
	TcCheckList   []*pb.BlackBoxCaseTwBetaMindCheck `json:"tc_check_list"`
	ProductModule string                            `json:"product_module"`
}

type QueryTwBetaMindKnowledgeResp struct {
	Knowledge           string `json:"knowledge"`
	SupplementKnowledge string `json:"supplement_knowledge"`
}
