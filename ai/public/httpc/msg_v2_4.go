package httpc

import (
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/model/dto"
)

type PageSize struct {
	Page int64 `json:"page"`
	Size int64 `json:"size"`
}

type CreateKnowledgeMetaProjectReq struct {
	Name      string `json:"name"`
	ProjectId string `json:"project_id"`
}

type ListKnowledgeMetaTagReq struct {
	ProjectId string `json:"project_id" url:"project_id"`
}

type CreateKnowledgeMetaTagReq struct {
	ProjectId string `json:"project_id"`
	Tag       string `json:"tag"`
}

type UpdateKnowledgeMetaTagReq struct {
	Id  int64  `json:"id"`
	Tag string `json:"tag"`
}

type DeleteKnowledgeMetaTagReq struct {
	Id int64 `json:"id"`
}

type ListKnowledgeTermReq struct {
	ProjectId string      `json:"project_id,omitempty"`
	Filter    TermsFilter `json:"filter,omitempty"`
	Search    string      `json:"search,omitempty"`
	PageSize
}

type TermsFilter struct {
	Tag string `json:"tag,omitempty" zh:"标签名称"`
}

type KnowledgeTag struct {
	Id  int64  `json:"id"`
	Tag string `json:"tag"`
}

type CreateKnowledgeTermReq struct {
	ProjectId     string          `json:"project_id"`
	Project       string          `json:"project"`
	TermInChinese string          `json:"term_in_chinese"`
	TermMeaning   string          `json:"term_meaning"`
	Tags          []*KnowledgeTag `json:"tags"`
}

type UpdateKnowledgeTermReq struct {
	Id            string          `json:"id"`
	TermInChinese string          `json:"term_in_chinese"`
	TermMeaning   string          `json:"term_meaning"`
	Tags          []*KnowledgeTag `json:"tags"`
}

type DeleteKnowledgeTermReq struct {
	Id string `json:"id"`
}

type ListKnowledgeTestExperienceReq struct {
	PageSize
	ProjectId string                        `json:"project_id"`
	Search    string                        `json:"search,omitempty"`
	Filter    KnowledgeTestExperienceFilter `json:"filter,omitempty"`
}

type KnowledgeTestExperienceFilter struct {
	Category string `json:"category,omitempty"`
	Tag      string `json:"tag,omitempty"`
}

type KnowledgeExperienceCategory struct {
	Id       int64  `json:"id"`
	TypeName string `json:"type_name"`
}

type CreateKnowledgeTestExperienceReq struct {
	ProjectId          string                       `json:"project_id"`
	Project            string                       `json:"project"`
	Category           *KnowledgeExperienceCategory `json:"category"`
	Tags               []*KnowledgeTag              `json:"tags"`
	TestExperience     string                       `json:"test_experience"`
	NormalFocusPoint   string                       `json:"normal_focus_point"`
	AbnormalFocusPoint string                       `json:"abnormal_focus_point"`
}

type UpdateKnowledgeTestExperienceReq struct {
	Id                 string                       `json:"id"`
	Project            string                       `json:"project"`
	Category           *KnowledgeExperienceCategory `json:"category"`
	Tags               []*KnowledgeTag              `json:"tags"`
	TestExperience     string                       `json:"test_experience"`
	NormalFocusPoint   string                       `json:"normal_focus_point"`
	AbnormalFocusPoint string                       `json:"abnormal_focus_point"`
}

type DeleteKnowledgeTestExperienceReq struct {
	Id string `json:"id"`
}

type ListKnowledgeMetaProjectReq struct {
}

type ListKnowledgeTestExperienceCategoryReq struct {
	ProjectId string `form:"project_id" url:"project_id"`
}

type CreateKnowledgeTestExperienceCategoryReq struct {
	ProjectId string `json:"project_id"`
	TypeName  string `json:"type_name"`
}

type DeleteKnowledgeTestExperienceCategoryReq struct {
	Id int64 `json:"id"`
}

type GetBlackBoxCaseEstimatedProgressReq struct {
	ControlNode ControlNodeType `form:"control_node" url:"control_node"`
}

type GetBlackBoxCaseEstimatedProgressResp struct {
	WaitingSec int64 `json:"waiting_sec"`
}

type ListBlackBoxCaseMapDocumentsReq struct {
	MapId string `form:"map_id" url:"map_id"`
}

type ListBlackBoxCaseMapDocumentsRespItem struct {
	Title   string `json:"title"`
	Content string `json:"content"`
}

type GetBlackBoxCaseUsedKnowledgeReq struct {
	MapId string `form:"map_id" url:"map_id"`
}
type GetBlackBoxCaseUsedKnowledgeResp struct {
	Terms       string `json:"terms"`
	Experiences string `json:"experiences"`
}

type GetBlackBoxCaseMindReq struct {
	MindType            MindType                     `json:"mind_type"`
	KnowledgeDocPgTitle string                       `json:"paragraph_title"`
	TableData           []*BlackBoxCaseMindTableData `json:"table_data"`
}

type BlackBoxCaseMindTableData struct {
	OrderId      string `json:"order_id"`
	Requirement  string `json:"requirement"`
	CaseName     string `json:"case_name"`
	PreCondition string `json:"pre_condition"`
	CaseStep     string `json:"case_step"`
	ExpectResult string `json:"expect_result"`
	Terminal     string `json:"terminal"`
	CaseLevel    string `json:"case_level"`
	Tag          string `json:"tag"`
	CaseDataId   string `json:"case_data_id"`
}

type GetBlackBoxCaseMindResp struct {
}

type KnowledgeDocPgTitle struct {
	Id    int64  `json:"id"`
	Title string `json:"title"`
}

type KnowledgeDocPgTitleV2 struct {
	Id           int64    `json:"id"`
	Title        string   `json:"title"`
	DemandPoints []string `json:"demandPoints"`
}

type CreateBlackBoxBaseReviewMapReq struct {
	MapId          string `json:"map_id"`
	ProjectId      string `json:"project_id"`
	KnowledgeDocId string `json:"knowledge_doc_id"`
	//KnowledgeDocPgTitle     KnowledgeDocPgTitle     `json:"knowledge_doc_pg_title"`
	KnowledgeDocPgTitles    []KnowledgeDocPgTitleV2 `json:"knowledge_doc_pg_title"`
	KnowledgeTermList       []string                `json:"knowledge_term_list"`
	KnowledgeExperienceList []KnowledgeExperience   `json:"knowledge_experience_list"`
	ModelCharacteristic     string                  `json:"model_characteristic"`
}

type KnowledgeExperience struct {
	ID                 string   `json:"id"`
	ProjectID          string   `json:"project_id"`
	Project            string   `json:"project"`
	Category           string   `json:"category"`
	Tags               []string `json:"tags"`
	TestExperience     string   `json:"test_experience"`
	NormalFocusPoint   string   `json:"normal_focus_point"`
	AbnormalFocusPoint string   `json:"abnormal_focus_point"`
}

type CreateBlackBoxBaseReviewMapResp struct {
	MapData
}

type MapData struct {
	ID       string    `json:"id"`
	Data     Text      `json:"data"` // 注意这里的Data和外层的Data字段冲突，因此需要重命名
	Children []MapData `json:"children"`
}

type Text struct {
	Text string `json:"text"`
}

type CreateBlackBoxFuncReviewMapReq struct {
	MapId          string `json:"map_id"`
	ProjectID      string `json:"project_id"`
	KnowledgeDocID string `json:"knowledge_doc_id"`
	//KnowledgeDocPgTitle     KnowledgeDocPgTitle     `json:"knowledge_doc_pg_title"`
	KnowledgeDocPgTitles    []KnowledgeDocPgTitleV2 `json:"knowledge_doc_pg_title"`
	KnowledgeTermList       []string                `json:"knowledge_term_list"`
	KnowledgeExperienceList []KnowledgeExperience   `json:"knowledge_experience_list"`
	ModelCharacteristic     string                  `json:"model_characteristic"`
	KnowledgeSupplementDoc  SupplementDoc           `json:"knowledge_supplement_doc"`
	FunctionPoint           string                  `json:"function_point"`
	GenerateOpinion         string                  `json:"generate_opinion"`
}

type SupplementDoc struct {
	Type     string    `json:"type"`
	Feishu   FeishuDoc `json:"feishu"`
	Markdown Markdown  `json:"markdown"`
}

type FeishuDoc struct {
	KnowledgeDocId      string                 `json:"knowledge_doc_id"`
	KnowledgeDocPgTitle []*KnowledgeDocPgTitle `json:"knowledge_doc_pg_title"`
}

type Markdown struct {
	Text string `json:"text"`
}

type CreateBlackBoxFuncReviewMapResp struct {
	MapData
}

type CreateBlackBoxTestSceneReviewMapReq struct {
	MapId          string `json:"map_id"`
	ProjectID      string `json:"project_id"`
	KnowledgeDocID string `json:"knowledge_doc_id"`
	//KnowledgeDocPgTitle     KnowledgeDocPgTitle     `json:"knowledge_doc_pg_title"`
	KnowledgeDocPgTitles    []KnowledgeDocPgTitleV2 `json:"knowledge_doc_pg_title"`
	KnowledgeTermList       []string                `json:"knowledge_term_list"`
	KnowledgeExperienceList []KnowledgeExperience   `json:"knowledge_experience_list"`
	ModelCharacteristic     string                  `json:"model_characteristic"`
	KnowledgeSupplementDoc  SupplementDoc           `json:"knowledge_supplement_doc"`
	FunctionPoint           string                  `json:"function_point"`
	GenerateOpinion         string                  `json:"generate_opinion"`
}

type CreateBlackBoxTestSceneReviewMapResp struct {
	MapData
}

type UpdateBlackBoxTestSceneReviewMapReq struct {
	MapId                   string                  `json:"map_id"`
	ProjectID               string                  `json:"project_id"`
	KnowledgeDocID          string                  `json:"knowledge_doc_id"`
	KnowledgeDocPgTitles    []KnowledgeDocPgTitleV2 `json:"knowledge_doc_pg_title"`
	KnowledgeTermList       []string                `json:"knowledge_term_list"`
	KnowledgeExperienceList []KnowledgeExperience   `json:"knowledge_experience_list"`
	ModelCharacteristic     string                  `json:"model_characteristic"`
	KnowledgeSupplementDoc  SupplementDoc           `json:"knowledge_supplement_doc"`
	FunctionPoint           string                  `json:"function_point"`
	GenerateOpinion         string                  `json:"generate_opinion"`
	MapData                 MapData                 `json:"review_map_old"`
}

type UpdateBlackBoxTestSceneReviewMapResp struct {
	MapData
}

type CreateBlackBoxTestCaseReq struct {
	CaseID         string `json:"case_id"`
	KnowledgeDocID string `json:"knowledge_doc_id"`
	//KnowledgeDocPgTitle    KnowledgeDocPgTitle     `json:"knowledge_doc_pg_title"`
	KnowledgeDocPgTitles   []KnowledgeDocPgTitleV2 `json:"knowledge_doc_pg_title"`
	ModelCharacteristic    string                  `json:"model_characteristic"`
	KnowledgeSupplementDoc []SupplementDoc         `json:"knowledge_supplement_doc"`
	GenerateOpinion        string                  `json:"generate_opinion"`
	ReviewMap              MapData                 `json:"review_map"`
}

type CreateBlackBoxTestCaseResp struct {
	Data []BlackBoxTestCase
}

type BlackBoxTestCase struct {
	OrderID      string `json:"order_id"`
	Requirement  string `json:"requirement"`
	CaseName     string `json:"case_name"`
	PreCondition string `json:"pre_condition"`
	CaseStep     string `json:"case_step"`
	ExpectResult string `json:"expect_result"`
	Terminal     string `json:"terminal"`
	CaseLevel    string `json:"case_level"`
	Tag          string `json:"tag"`
}

type UpdateBlackBoxTestCaseReq struct {
	CaseID         string `json:"case_id"`
	KnowledgeDocID string `json:"knowledge_doc_id"`
	//KnowledgeDocPgTitle    KnowledgeDocPgTitle     `json:"knowledge_doc_pg_title"`
	KnowledgeDocPgTitles   []KnowledgeDocPgTitleV2 `json:"knowledge_doc_pg_title"`
	ModelCharacteristic    string                  `json:"model_characteristic"`
	KnowledgeSupplementDoc []SupplementDoc         `json:"knowledge_supplement_doc"`
	GenerateOpinion        string                  `json:"generate_opinion"`
	ReviewMap              MapData                 `json:"review_map"`
	CaseResult             []CaseResult            `json:"case_result"`
}

type UpdateBlackBoxTestCaseResp struct {
	Data []BlackBoxTestCase
}

type CaseResult struct {
	OrderId      string `json:"order_id"`
	Requirement  string `json:"requirement"`
	CaseName     string `json:"case_name"`
	PreCondition string `json:"pre_condition"`
	CaseStep     string `json:"case_step"`
	ExpectResult string `json:"expect_result"`
	Terminal     string `json:"terminal"`
	CaseLevel    string `json:"case_level"`
	Tag          string `json:"tag"`
}

type Json2MDV24Req struct {
	KnowledgeId               string                  `json:"knowledge_id"`
	KnowledgeParagraphTitle   string                  `json:"knowledge_paragraph_title"`
	KnowledgeParagraphTitleId int64                   `json:"knowledge_paragraph_title_id"`
	CaseTable                 []*dto.BlackBoxCaseData `json:"case_table"`
	Version                   string                  `json:"version"`
}

type GetFuncCoverageReq struct {
	CaseRefId string `json:"case_id"`
}

type GetFuncCoverageResp struct {
	Coverage float64 `json:"coverage"`
}

type GetDocumentSizeReq struct {
	CaseRefId string `json:"case_id"`
}

type GetDocumentSizeResp struct {
	Size string `json:"size"`
}

type GetUpdatedCaseAddUpdateCountReq struct {
	CaseRefId string `json:"case_id"`
}

type GetUpdatedCaseAddUpdateCountResp struct {
	Count  int64 `json:"count"`
	Add    int64 `json:"add"`
	Update int64 `json:"update"`
}

type CreateExtractDemandPointsReq struct {
}

type SummarizeCaseDocReq struct {
	KnowledgeDocId       string                  `json:"knowledge_doc_id"`
	KnowledgeDocPgTitles []KnowledgeDocPgTitleV2 `json:"knowledge_doc_pg_title"`
}
type SummarizeCaseDocResp struct {
}
