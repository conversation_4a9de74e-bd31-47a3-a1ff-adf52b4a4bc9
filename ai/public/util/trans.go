package util

func Int64ToBool(in int64) bool {
	if in == 0 {
		return false
	} else {
		return true
	}
}

func BoolToInt64(in bool) int64 {
	if in {
		return 1
	} else {
		return 0
	}
}

func RemoveDuplicates(slice []string) []string {
	keys := make(map[string]bool)
	list := []string{}
	for _, entry := range slice {
		if _, value := keys[entry]; !value {
			keys[entry] = true
			list = append(list, entry)
		}
	}
	return list
}
