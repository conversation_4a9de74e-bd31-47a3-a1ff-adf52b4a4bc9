package metrics

import (
	commonmetrics "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/metrics"
)

var (
	Subsystem               = "ai"
	CaseRefTotalGauge       commonmetrics.MetricGaugeHandler // 用例生成次数
	CaseTotalGauge          commonmetrics.MetricGaugeHandler // 用例总数
	CaseDataAdoptTotalGauge commonmetrics.MetricGaugeHandler // 采纳总行数
	CaseAdoptTotalGauge     commonmetrics.MetricGaugeHandler // 采纳总用例数
	CaseQueryTotalGauge     commonmetrics.MetricGaugeHandler // 查询总数
	CaseDataTotalGauge      commonmetrics.MetricGaugeHandler // 用例总行数
	CaseListTotalGauge      commonmetrics.MetricGaugeHandler // 用例列表访问次数(用于统计每天用户使用量)
	CaseListUsersTotalGauge commonmetrics.MetricGaugeHandler // 用例列表访问用户数(用于统计每天用户使用量)

	GenerateCaseLineTotalGauge        commonmetrics.MetricGaugeHandler   // AI生成总行数
	GenerateAdoptedCaseLineTotalGauge commonmetrics.MetricCounterHandler // AI生成已采纳总行数
	GenerateUpdatedCaseLineTotalGauge commonmetrics.MetricCounterHandler // AI生成后修改的用例行数

	// 用例功能覆盖率
	// 搁置的用例数: 未采纳、未删除

	PVKey     = "qet_ai_case_list_viewer"
	PVUserKey = "qet_ai_case_user_list_viewer"
)

func init() {
	gaugeVecOpts := commonmetrics.MetricGaugeVecOpts
	gaugeVecOpts.Subsystem = Subsystem
	gaugeVecOpts.Name = "case_generate" + string(commonmetrics.ConstSystemMetricUnitTypeTotal)
	gaugeVecOpts.Help = "The total of the case generation"
	gaugeVecOpts.Labels = []string{"generate_type"}
	CaseRefTotalGauge = commonmetrics.NewMetricGauge(&gaugeVecOpts)

	gaugeVecOpts1 := commonmetrics.MetricGaugeVecOpts
	gaugeVecOpts1.Subsystem = Subsystem
	gaugeVecOpts1.Name = "case" + string(commonmetrics.ConstSystemMetricUnitTypeTotal)
	gaugeVecOpts1.Help = "The total of the case"
	gaugeVecOpts1.Labels = []string{"dir_id"}
	CaseTotalGauge = commonmetrics.NewMetricGauge(&gaugeVecOpts1)

	gaugeVecOpts2 := commonmetrics.MetricGaugeVecOpts
	gaugeVecOpts2.Subsystem = Subsystem
	gaugeVecOpts2.Name = "data_adopted" + string(commonmetrics.ConstSystemMetricUnitTypeTotal)
	gaugeVecOpts2.Help = "The total number of data adopted"
	gaugeVecOpts2.Labels = []string{"updated_human"}
	CaseDataAdoptTotalGauge = commonmetrics.NewMetricGauge(&gaugeVecOpts2)

	gaugeVecOpts3 := commonmetrics.MetricGaugeVecOpts
	gaugeVecOpts3.Subsystem = Subsystem
	gaugeVecOpts3.Name = "case_query" + string(commonmetrics.ConstSystemMetricUnitTypeTotal)
	gaugeVecOpts3.Help = "The total number of case query"
	gaugeVecOpts3.Labels = []string{"knowledge_id"}
	CaseQueryTotalGauge = commonmetrics.NewMetricGauge(&gaugeVecOpts3)

	gaugeVecOpts4 := commonmetrics.MetricGaugeVecOpts
	gaugeVecOpts4.Subsystem = Subsystem
	gaugeVecOpts4.Name = "data" + string(commonmetrics.ConstSystemMetricUnitTypeTotal)
	gaugeVecOpts4.Help = "The total number of data"
	gaugeVecOpts4.Labels = []string{"generate_type"}
	CaseDataTotalGauge = commonmetrics.NewMetricGauge(&gaugeVecOpts4)

	gaugeVecOpts5 := commonmetrics.MetricGaugeVecOpts
	gaugeVecOpts5.Subsystem = Subsystem
	gaugeVecOpts5.Name = "case_adopted" + string(commonmetrics.ConstSystemMetricUnitTypeTotal)
	gaugeVecOpts5.Help = "The total number of case adopted"
	gaugeVecOpts5.Labels = []string{"dir_id"}
	CaseAdoptTotalGauge = commonmetrics.NewMetricGauge(&gaugeVecOpts5)

	gaugeVecOpts6 := commonmetrics.MetricGaugeVecOpts
	gaugeVecOpts6.Subsystem = Subsystem
	gaugeVecOpts6.Name = "case_list" + string(commonmetrics.ConstSystemMetricUnitTypeTotal)
	gaugeVecOpts6.Help = "The case list viewed count by account"
	gaugeVecOpts6.Labels = []string{"user_account", "user_name"}
	CaseListTotalGauge = commonmetrics.NewMetricGauge(&gaugeVecOpts6)

	gaugeVecOpts7 := commonmetrics.MetricGaugeVecOpts
	gaugeVecOpts7.Subsystem = Subsystem
	gaugeVecOpts7.Name = "case_list_users" + string(commonmetrics.ConstSystemMetricUnitTypeTotal)
	gaugeVecOpts7.Help = "The case list viewed users"
	gaugeVecOpts7.Labels = []string{"project_id"}
	CaseListUsersTotalGauge = commonmetrics.NewMetricGauge(&gaugeVecOpts7)
}
