USE artificial_intelligence;

-- ----------------------------
-- Table structure for black_box_case
-- ----------------------------
CREATE TABLE `black_box_case` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `dir_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '目录ID',
  `case_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '用例ID',
  `case_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '用例名称',
  `case_continue_to_write` int NOT NULL COMMENT '用例续写次数',
  `case_model_character` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '用例模型性格',
  `knowledge_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '知识文档关联ID',
  `created_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '创建者的用户ID',
  `updated_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '最近一次更新者的用户ID',
  `deleted_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '删除者的用户ID',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted_at` timestamp NULL DEFAULT NULL COMMENT '删除时间',
  `deleted` tinyint NOT NULL COMMENT '逻辑删除标识（未删除、已删除）',
  `case_remarks` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '用例备注',
  `knowledge_paragraph_title_id` int NOT NULL COMMENT '创建所选标题id',
  `knowledge_paragraph_title_text` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '知识文档段落标题',
  `reference_doc` json NOT NULL COMMENT '关联文档内容',
  `enable_reference` tinyint NOT NULL COMMENT '是否关联文档',
  `knowledge_fix_sugg` varchar(255) COLLATE utf8mb4_general_ci NOT NULL COMMENT '文档修改意见',
  `query_times` int NOT NULL DEFAULT '0' COMMENT '查询用例次数',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uniq_bbca_piai` (`case_id`) USING BTREE,
  UNIQUE KEY `uniq_dir_case` (`dir_id`,`case_id`)
) ENGINE=InnoDB AUTO_INCREMENT=121 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='AI-黑盒测试用例-用例表';

-- ----------------------------
-- Table structure for black_box_case_data
-- ----------------------------
CREATE TABLE `black_box_case_data` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `case_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '用例ID',
  `revision_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '版本ID',
  `case_data_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '数据ID',
  `order_id` varchar(64) COLLATE utf8mb4_general_ci NOT NULL COMMENT '序号',
  `requirement` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '需求名称',
  `pre_condition` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '前置条件',
  `case_step` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '用例步骤',
  `expect_result` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '预期结果',
  `terminal` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '终端',
  `case_level` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '用例等级',
  `tag` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '标识',
  `deleted` tinyint NOT NULL DEFAULT '0' COMMENT '逻辑删除标识（未删除、已删除）',
  `created_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '创建者的用户ID',
  `updated_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '最近一次更新者的用户ID',
  `deleted_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '删除者的用户ID',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted_at` timestamp NULL DEFAULT NULL COMMENT '删除时间',
  `case_name` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '用例名称',
  `adopted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否被采纳',
  `updated_human_tmp` tinyint(1) NOT NULL DEFAULT '0' COMMENT '修改次数(未采纳)',
  `updated_human` tinyint(1) NOT NULL DEFAULT '0' COMMENT '修改次数',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uniq_bbca_piai` (`case_data_id`)
) ENGINE=InnoDB AUTO_INCREMENT=5749 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='AI-黑盒测试用例-用例数据表';

-- ----------------------------
-- Table structure for black_box_case_dir
-- ----------------------------
CREATE TABLE `black_box_case_dir` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `project_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '项目ID',
  `dir_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '目录ID',
  `dir_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '目录名称',
  `dir_description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '目录描述',
  `deleted` tinyint NOT NULL COMMENT '逻辑删除标识（未删除、已删除）',
  `created_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '创建者的用户ID',
  `updated_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '最近一次更新者的用户ID',
  `deleted_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '删除者的用户ID',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted_at` timestamp NULL DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uniq_bbca_piai` (`project_id`,`dir_id`)
) ENGINE=InnoDB AUTO_INCREMENT=54 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='AI-黑盒测试用例-目录表';

-- ----------------------------
-- Table structure for black_box_case_knowledge
-- ----------------------------
CREATE TABLE `black_box_case_knowledge` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `dir_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '目录ID',
  `knowledge_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '知识文档ID',
  `deleted` tinyint NOT NULL DEFAULT '0' COMMENT '逻辑删除标识（未删除、已删除）',
  `created_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '创建者的用户ID',
  `updated_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '最近一次更新者的用户ID',
  `deleted_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '删除者的用户ID',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted_at` timestamp NULL DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uniq_bbca_piai` (`knowledge_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=52 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='AI-黑盒测试用例-知识文档表';

-- ----------------------------
-- Table structure for black_box_case_ref
-- ----------------------------
CREATE TABLE `black_box_case_ref` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `case_ref_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '用例ID',
  `deleted` tinyint NOT NULL DEFAULT '0' COMMENT '逻辑删除标识（未删除、已删除）',
  `created_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '创建者的用户ID',
  `updated_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '最近一次更新者的用户ID',
  `deleted_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '删除者的用户ID',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted_at` timestamp NULL DEFAULT NULL COMMENT '删除时间',
  `case_ref_type` varchar(64) COLLATE utf8mb4_general_ci NOT NULL COMMENT '类型(修改/生成/局部修改)',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uniq_bbca_piai` (`case_ref_id`)
) ENGINE=InnoDB AUTO_INCREMENT=553 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='AI-黑盒测试用例-用例数据关联表';

-- ----------------------------
-- Table structure for black_box_case_revision
-- ----------------------------
CREATE TABLE `black_box_case_revision` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `case_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '用例ID',
  `revision_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '版本ID',
  `revision_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '版本名称',
  `deleted` tinyint NOT NULL DEFAULT '0' COMMENT '逻辑删除标识（未删除、已删除）',
  `created_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '创建者的用户ID',
  `updated_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '最近一次更新者的用户ID',
  `deleted_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '删除者的用户ID',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted_at` timestamp NULL DEFAULT NULL COMMENT '删除时间',
  `knowledge_paragraph_title_text` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '知识文档段落标题',
  `knowledge_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '知识文档关联ID',
  `case_ref_id` varchar(64) COLLATE utf8mb4_general_ci NOT NULL COMMENT '用例关联ID',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uniq_bbca_piai` (`case_id`,`revision_id`),
  UNIQUE KEY `uniq_revision` (`revision_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=426 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='AI-黑盒测试用例-用例版本表';


CREATE TABLE `black_box_case_tw_beta`
(
    `id`                       int NOT NULL AUTO_INCREMENT COMMENT '自增ID',
    `beta_case_id`             varchar(64) COLLATE utf8mb4_general_ci NOT NULL COMMENT '用例版本ID',
    `beta_case_name`           varchar(255) COLLATE utf8mb4_general_ci NOT NULL COMMENT '用例名称',
    `revision_id`              varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '版本ID',
    `case_content`             longtext COLLATE utf8mb4_general_ci NOT NULL COMMENT '原始用例结果',
    `document_name`            varchar(255) COLLATE utf8mb4_general_ci NOT NULL COMMENT '文档名称',
    `document_url`             varchar(255) COLLATE utf8mb4_general_ci NOT NULL COMMENT '文档URL',
    `document_chapter_title`   varchar(255) COLLATE utf8mb4_general_ci NOT NULL COMMENT '选中章节标题',
    `document_content`         longtext COLLATE utf8mb4_general_ci NOT NULL COMMENT '选中文档内容',
    `reference_doc`            longtext COLLATE utf8mb4_general_ci NULL COMMENT '补充文档内容',
    `knowledge_fix_sugg`       varchar(255) COLLATE utf8mb4_general_ci NOT NULL COMMENT '文档修改意见',
    `case_ref_id`              varchar(64) COLLATE utf8mb4_general_ci NOT NULL COMMENT '用例关联ID',
    `status`                   tinyint                                NOT NULL DEFAULT '0' COMMENT '会话状态：使用中：0，归档：1',
    `deleted`                  tinyint                                NOT NULL DEFAULT '0' COMMENT '逻辑删除标识（未删除、已删除）',
    `created_by`               varchar(64) COLLATE utf8mb4_general_ci NOT NULL COMMENT '创建者的用户ID',
    `updated_by`               varchar(64) COLLATE utf8mb4_general_ci NOT NULL COMMENT '最近一次更新者的用户ID',
    `deleted_by`               varchar(64) COLLATE utf8mb4_general_ci          DEFAULT NULL COMMENT '删除者的用户ID',
    `created_at`               timestamp                              NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at`               timestamp                              NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at`               timestamp                              NULL     DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uniq_bbcs_piaisi` (`beta_case_id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 1
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT ='AI-黑盒测试用例-BETA表';


-- 2025.01.02 v2-4版本: 新增三张表: black_box_case_dir_v2、black_box_case_map、black_box_case_map_document


ALTER TABLE
    black_box_case
ADD
    COLUMN knowledge_paragraph_title_text VARCHAR(255) NOT NULL DEFAULT '' COMMENT '知识文档段落标题'

-- ----------------------------
-- Table structure for black_box_case_dir_v2
-- ----------------------------
CREATE TABLE `black_box_case_dir_v2`  (
`id` int NOT NULL AUTO_INCREMENT COMMENT '自增ID',
`project_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '项目ID',
`dir_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '目录ID',
`dir_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '目录名称',
`dir_description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '目录描述',
`deleted` tinyint NOT NULL COMMENT '逻辑删除标识（未删除、已删除）',
`created_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '创建者的用户ID',
`updated_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '最近一次更新者的用户ID',
`deleted_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '删除者的用户ID',
`created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
`updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
`deleted_at` timestamp NULL DEFAULT NULL COMMENT '删除时间',
PRIMARY KEY (`id`) USING BTREE,
UNIQUE INDEX `uniq_bbca_piai`(`project_id` ASC, `dir_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = 'AI-黑盒测试用例-目录表' ROW_FORMAT = Dynamic;


-- ----------------------------
-- Table structure for black_box_case_map
-- ----------------------------
CREATE TABLE `black_box_case_map`  (
`id` int NOT NULL AUTO_INCREMENT COMMENT '自增ID',
`project_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '项目ID',
`dir_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '目录ID',
`case_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '用例ID',
`map_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '评审导图ID',
`data` json NOT NULL COMMENT '评审导图数据',
`tags` json NOT NULL COMMENT '术语标签',
`experiences` json NOT NULL COMMENT '测试经验',
`deleted` tinyint NOT NULL COMMENT '逻辑删除标识（未删除、已删除）',
`created_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '创建者的用户ID',
`updated_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '最近一次更新者的用户ID',
`deleted_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '删除者的用户ID',
`created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
`updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
`deleted_at` timestamp NULL DEFAULT NULL COMMENT '删除时间',
`used_knowledge` json NULL COMMENT '已使用的知识',
`recall_document` json NULL COMMENT '召回文档',
PRIMARY KEY (`id`) USING BTREE,
UNIQUE INDEX `uniq_case_id`(`case_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = 'AI-黑盒测试用例-评审导图' ROW_FORMAT = Dynamic;


-- ----------------------------
-- Table structure for black_box_case_map_document
-- ----------------------------
CREATE TABLE `black_box_case_map_document`  (
`id` int NOT NULL AUTO_INCREMENT COMMENT '自增ID',
`project_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '项目ID',
`dir_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '目录ID',
`case_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '用例ID',
`map_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '评审导图ID',
`func_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '功能点ID',
`func_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '功能点名称',
`func_doc` json NULL COMMENT 'AI生成功能点的补充文档',
`expe_doc` json NULL COMMENT 'AI生成测试经验的补充文档',
`deleted` tinyint NOT NULL COMMENT '逻辑删除标识（未删除、已删除）',
`created_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '创建者的用户ID',
`updated_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '最近一次更新者的用户ID',
`deleted_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '删除者的用户ID',
`created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
`updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
`deleted_at` timestamp NULL DEFAULT NULL COMMENT '删除时间',
PRIMARY KEY (`id`) USING BTREE,
UNIQUE INDEX `uniq_case_func`(`case_id` ASC, `func_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = 'AI-黑盒测试用例-评审导图文档' ROW_FORMAT = Dynamic;

-- ----------------------------
-- 2025.01.14
-- ----------------------------
-- ----------------------------
-- Table structure for black_box_generation_record
-- ----------------------------
DROP TABLE IF EXISTS `black_box_generation_record`;
CREATE TABLE `black_box_generation_record`  (
                                                `id` int NOT NULL AUTO_INCREMENT COMMENT '主键',
                                                `function_point_count` int NOT NULL COMMENT '生成功能点次数',
                                                `test_scene_count` int NOT NULL COMMENT '生成测试场景次数',
                                                `supplement_document_count` int NOT NULL COMMENT '补充文档次数',
                                                `created_date` date NOT NULL COMMENT '创建日期',
                                                `base_map_case_count` int NOT NULL COMMENT '生成评审导图次数',
                                                `updated_add_count` int NOT NULL COMMENT '修改后生成的用例条数',
                                                PRIMARY KEY (`id`) USING BTREE,
                                                UNIQUE INDEX `uniq_created_date`(`created_date` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 18 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '生成记录统计表' ROW_FORMAT = Dynamic;


use `artificial_intelligence`
ALTER TABLE
    `artificial_intelligence`.`black_box_case_revision`
ADD
    COLUMN `coverage` int NOT NULL COMMENT '用例覆盖率[0~100]'

ALTER TABLE
    `artificial_intelligence`.`black_box_case`
ADD
    COLUMN `knowledge_size` varchar(64) NOT NULL COMMENT '用例关联的产品文档大小'

-- ----------------------------
-- 2025.01.20
-- ----------------------------
ALTER TABLE
    `artificial_intelligence`.`black_box_case_revision`
    ADD
        COLUMN `add_count` int NOT NULL COMMENT 'AI生成新增的条数',
ADD
  COLUMN `update_count` int NOT NULL COMMENT 'AI生成修改的条数'

-- ----------------------------
-- 2025.02.19
-- ----------------------------
ALTER TABLE `artificial_intelligence`.`black_box_generation_record`
ADD COLUMN `case_name_update_count` int NOT NULL COMMENT '编辑用例时更新用例名称的次数' AFTER `updated_add_count`,
ADD COLUMN `precondition_update_count` int NOT NULL COMMENT '编辑用例时更新前置条件的次数' AFTER `case_name_update_count`,
ADD COLUMN `case_step_update_count` int NOT NULL COMMENT '编辑用例时更新用例步骤的次数' AFTER `precondition_update_count`,
ADD COLUMN `expect_result_update_count` int NOT NULL COMMENT '编辑用例时更新预期结果的次数' AFTER `case_step_update_count`,
ADD COLUMN `case_level_update_count` int NOT NULL COMMENT '编辑用例时更新用例等级的次数' AFTER `expect_result_update_count`

-- ----------------------------
-- 2025.02.27
-- ----------------------------
use `artificial_intelligence`
ALTER TABLE `artificial_intelligence`.`black_box_case`
    ADD COLUMN `knowledge_paragraph_titles` json NOT NULL COMMENT '知识文档段落标题' AFTER `knowledge_size`
ALTER TABLE `artificial_intelligence`.`black_box_case_revision`
    ADD COLUMN `knowledge_paragraph_title` json NOT NULL COMMENT '知识文档段落标题列表' AFTER `deleted_at`
