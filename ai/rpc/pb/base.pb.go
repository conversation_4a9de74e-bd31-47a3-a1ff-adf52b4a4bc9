// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.1
// 	protoc        v3.7.1
// source: ai/base.proto

package pb

import (
	reflect "reflect"
	sync "sync"

	_struct "github.com/golang/protobuf/ptypes/struct"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type WorkPriorityType int32

const (
	WorkPriorityType_WORK_PRIORITY_TYPE_ACCURACY_PRIORITY WorkPriorityType = 0 //准确优先
	WorkPriorityType_WORK_PRIORITY_TYPE_SPEED_PRIORITY    WorkPriorityType = 1 //速度优先
)

// Enum value maps for WorkPriorityType.
var (
	WorkPriorityType_name = map[int32]string{
		0: "WORK_PRIORITY_TYPE_ACCURACY_PRIORITY",
		1: "WORK_PRIORITY_TYPE_SPEED_PRIORITY",
	}
	WorkPriorityType_value = map[string]int32{
		"WORK_PRIORITY_TYPE_ACCURACY_PRIORITY": 0,
		"WORK_PRIORITY_TYPE_SPEED_PRIORITY":    1,
	}
)

func (x WorkPriorityType) Enum() *WorkPriorityType {
	p := new(WorkPriorityType)
	*p = x
	return p
}

func (x WorkPriorityType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (WorkPriorityType) Descriptor() protoreflect.EnumDescriptor {
	return file_ai_base_proto_enumTypes[0].Descriptor()
}

func (WorkPriorityType) Type() protoreflect.EnumType {
	return &file_ai_base_proto_enumTypes[0]
}

func (x WorkPriorityType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use WorkPriorityType.Descriptor instead.
func (WorkPriorityType) EnumDescriptor() ([]byte, []int) {
	return file_ai_base_proto_rawDescGZIP(), []int{0}
}

type WorkSpaceType int32

const (
	WorkSpaceType_WORK_SPACE_TYPE_PERSONAL WorkSpaceType = 0 // 个人空间
	WorkSpaceType_WORK_SPACE_TYPE_PUBLIC   WorkSpaceType = 1 // 公共空间
)

// Enum value maps for WorkSpaceType.
var (
	WorkSpaceType_name = map[int32]string{
		0: "WORK_SPACE_TYPE_PERSONAL",
		1: "WORK_SPACE_TYPE_PUBLIC",
	}
	WorkSpaceType_value = map[string]int32{
		"WORK_SPACE_TYPE_PERSONAL": 0,
		"WORK_SPACE_TYPE_PUBLIC":   1,
	}
)

func (x WorkSpaceType) Enum() *WorkSpaceType {
	p := new(WorkSpaceType)
	*p = x
	return p
}

func (x WorkSpaceType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (WorkSpaceType) Descriptor() protoreflect.EnumDescriptor {
	return file_ai_base_proto_enumTypes[1].Descriptor()
}

func (WorkSpaceType) Type() protoreflect.EnumType {
	return &file_ai_base_proto_enumTypes[1]
}

func (x WorkSpaceType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use WorkSpaceType.Descriptor instead.
func (WorkSpaceType) EnumDescriptor() ([]byte, []int) {
	return file_ai_base_proto_rawDescGZIP(), []int{1}
}

type MindType int32

const (
	MindType_MIND_TYPE_CASE   MindType = 0 // case
	MindType_MIND_TYPE_REVIEW MindType = 1 // review
)

// Enum value maps for MindType.
var (
	MindType_name = map[int32]string{
		0: "MIND_TYPE_CASE",
		1: "MIND_TYPE_REVIEW",
	}
	MindType_value = map[string]int32{
		"MIND_TYPE_CASE":   0,
		"MIND_TYPE_REVIEW": 1,
	}
)

func (x MindType) Enum() *MindType {
	p := new(MindType)
	*p = x
	return p
}

func (x MindType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (MindType) Descriptor() protoreflect.EnumDescriptor {
	return file_ai_base_proto_enumTypes[2].Descriptor()
}

func (MindType) Type() protoreflect.EnumType {
	return &file_ai_base_proto_enumTypes[2]
}

func (x MindType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use MindType.Descriptor instead.
func (MindType) EnumDescriptor() ([]byte, []int) {
	return file_ai_base_proto_rawDescGZIP(), []int{2}
}

type SessionStatus int32

const (
	SessionStatus_SESSION_STATUS_ACTIVE   SessionStatus = 0 // 使用中
	SessionStatus_SESSION_STATUS_ARCHIVED SessionStatus = 1 // 归档
)

// Enum value maps for SessionStatus.
var (
	SessionStatus_name = map[int32]string{
		0: "SESSION_STATUS_ACTIVE",
		1: "SESSION_STATUS_ARCHIVED",
	}
	SessionStatus_value = map[string]int32{
		"SESSION_STATUS_ACTIVE":   0,
		"SESSION_STATUS_ARCHIVED": 1,
	}
)

func (x SessionStatus) Enum() *SessionStatus {
	p := new(SessionStatus)
	*p = x
	return p
}

func (x SessionStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (SessionStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_ai_base_proto_enumTypes[3].Descriptor()
}

func (SessionStatus) Type() protoreflect.EnumType {
	return &file_ai_base_proto_enumTypes[3]
}

func (x SessionStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use SessionStatus.Descriptor instead.
func (SessionStatus) EnumDescriptor() ([]byte, []int) {
	return file_ai_base_proto_rawDescGZIP(), []int{3}
}

type DocumentType int32

const (
	DocumentType_DOCUMENT_TYPE_TEXT   DocumentType = 0 // 纯文本
	DocumentType_DOCUMENT_TYPE_FEISHU DocumentType = 1 // 飞书
	DocumentType_DOCUMENT_TYPE_DOC    DocumentType = 2 // doc
)

// Enum value maps for DocumentType.
var (
	DocumentType_name = map[int32]string{
		0: "DOCUMENT_TYPE_TEXT",
		1: "DOCUMENT_TYPE_FEISHU",
		2: "DOCUMENT_TYPE_DOC",
	}
	DocumentType_value = map[string]int32{
		"DOCUMENT_TYPE_TEXT":   0,
		"DOCUMENT_TYPE_FEISHU": 1,
		"DOCUMENT_TYPE_DOC":    2,
	}
)

func (x DocumentType) Enum() *DocumentType {
	p := new(DocumentType)
	*p = x
	return p
}

func (x DocumentType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DocumentType) Descriptor() protoreflect.EnumDescriptor {
	return file_ai_base_proto_enumTypes[4].Descriptor()
}

func (DocumentType) Type() protoreflect.EnumType {
	return &file_ai_base_proto_enumTypes[4]
}

func (x DocumentType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use DocumentType.Descriptor instead.
func (DocumentType) EnumDescriptor() ([]byte, []int) {
	return file_ai_base_proto_rawDescGZIP(), []int{4}
}

type DocumentStatus int32

const (
	DocumentStatus_DOCUMENT_STATUS_PROCESSING DocumentStatus = 0 // 处理中
	DocumentStatus_DOCUMENT_STATUS_COMPLETED  DocumentStatus = 1 // 完成
	DocumentStatus_DOCUMENT_STATUS_FAILED     DocumentStatus = 2 // 失败
)

// Enum value maps for DocumentStatus.
var (
	DocumentStatus_name = map[int32]string{
		0: "DOCUMENT_STATUS_PROCESSING",
		1: "DOCUMENT_STATUS_COMPLETED",
		2: "DOCUMENT_STATUS_FAILED",
	}
	DocumentStatus_value = map[string]int32{
		"DOCUMENT_STATUS_PROCESSING": 0,
		"DOCUMENT_STATUS_COMPLETED":  1,
		"DOCUMENT_STATUS_FAILED":     2,
	}
)

func (x DocumentStatus) Enum() *DocumentStatus {
	p := new(DocumentStatus)
	*p = x
	return p
}

func (x DocumentStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DocumentStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_ai_base_proto_enumTypes[5].Descriptor()
}

func (DocumentStatus) Type() protoreflect.EnumType {
	return &file_ai_base_proto_enumTypes[5]
}

func (x DocumentStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use DocumentStatus.Descriptor instead.
func (DocumentStatus) EnumDescriptor() ([]byte, []int) {
	return file_ai_base_proto_rawDescGZIP(), []int{5}
}

type Relation struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Type          string                 `protobuf:"bytes,2,opt,name=type,proto3" json:"type,omitempty"`
	Children      []*_struct.ListValue   `protobuf:"bytes,3,rep,name=children,proto3" json:"children,omitempty"`
	ReferenceId   string                 `protobuf:"bytes,4,opt,name=reference_id,json=referenceId,proto3" json:"reference_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Relation) Reset() {
	*x = Relation{}
	mi := &file_ai_base_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Relation) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Relation) ProtoMessage() {}

func (x *Relation) ProtoReflect() protoreflect.Message {
	mi := &file_ai_base_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Relation.ProtoReflect.Descriptor instead.
func (*Relation) Descriptor() ([]byte, []int) {
	return file_ai_base_proto_rawDescGZIP(), []int{0}
}

func (x *Relation) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Relation) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *Relation) GetChildren() []*_struct.ListValue {
	if x != nil {
		return x.Children
	}
	return nil
}

func (x *Relation) GetReferenceId() string {
	if x != nil {
		return x.ReferenceId
	}
	return ""
}

var File_ai_base_proto protoreflect.FileDescriptor

var file_ai_base_proto_rawDesc = []byte{
	0x0a, 0x0d, 0x61, 0x69, 0x2f, 0x62, 0x61, 0x73, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12,
	0x02, 0x61, 0x69, 0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2f, 0x73, 0x74, 0x72, 0x75, 0x63, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x22, 0x89, 0x01, 0x0a, 0x08, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x0e,
	0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12,
	0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x79,
	0x70, 0x65, 0x12, 0x36, 0x0a, 0x08, 0x63, 0x68, 0x69, 0x6c, 0x64, 0x72, 0x65, 0x6e, 0x18, 0x03,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x56, 0x61, 0x6c, 0x75, 0x65,
	0x52, 0x08, 0x63, 0x68, 0x69, 0x6c, 0x64, 0x72, 0x65, 0x6e, 0x12, 0x21, 0x0a, 0x0c, 0x72, 0x65,
	0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0b, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x49, 0x64, 0x2a, 0x63, 0x0a,
	0x10, 0x57, 0x6f, 0x72, 0x6b, 0x50, 0x72, 0x69, 0x6f, 0x72, 0x69, 0x74, 0x79, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x28, 0x0a, 0x24, 0x57, 0x4f, 0x52, 0x4b, 0x5f, 0x50, 0x52, 0x49, 0x4f, 0x52, 0x49,
	0x54, 0x59, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x41, 0x43, 0x43, 0x55, 0x52, 0x41, 0x43, 0x59,
	0x5f, 0x50, 0x52, 0x49, 0x4f, 0x52, 0x49, 0x54, 0x59, 0x10, 0x00, 0x12, 0x25, 0x0a, 0x21, 0x57,
	0x4f, 0x52, 0x4b, 0x5f, 0x50, 0x52, 0x49, 0x4f, 0x52, 0x49, 0x54, 0x59, 0x5f, 0x54, 0x59, 0x50,
	0x45, 0x5f, 0x53, 0x50, 0x45, 0x45, 0x44, 0x5f, 0x50, 0x52, 0x49, 0x4f, 0x52, 0x49, 0x54, 0x59,
	0x10, 0x01, 0x2a, 0x49, 0x0a, 0x0d, 0x57, 0x6f, 0x72, 0x6b, 0x53, 0x70, 0x61, 0x63, 0x65, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x1c, 0x0a, 0x18, 0x57, 0x4f, 0x52, 0x4b, 0x5f, 0x53, 0x50, 0x41, 0x43,
	0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x50, 0x45, 0x52, 0x53, 0x4f, 0x4e, 0x41, 0x4c, 0x10,
	0x00, 0x12, 0x1a, 0x0a, 0x16, 0x57, 0x4f, 0x52, 0x4b, 0x5f, 0x53, 0x50, 0x41, 0x43, 0x45, 0x5f,
	0x54, 0x59, 0x50, 0x45, 0x5f, 0x50, 0x55, 0x42, 0x4c, 0x49, 0x43, 0x10, 0x01, 0x2a, 0x34, 0x0a,
	0x08, 0x4d, 0x69, 0x6e, 0x64, 0x54, 0x79, 0x70, 0x65, 0x12, 0x12, 0x0a, 0x0e, 0x4d, 0x49, 0x4e,
	0x44, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x43, 0x41, 0x53, 0x45, 0x10, 0x00, 0x12, 0x14, 0x0a,
	0x10, 0x4d, 0x49, 0x4e, 0x44, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x52, 0x45, 0x56, 0x49, 0x45,
	0x57, 0x10, 0x01, 0x2a, 0x47, 0x0a, 0x0d, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x19, 0x0a, 0x15, 0x53, 0x45, 0x53, 0x53, 0x49, 0x4f, 0x4e, 0x5f,
	0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x56, 0x45, 0x10, 0x00, 0x12,
	0x1b, 0x0a, 0x17, 0x53, 0x45, 0x53, 0x53, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55,
	0x53, 0x5f, 0x41, 0x52, 0x43, 0x48, 0x49, 0x56, 0x45, 0x44, 0x10, 0x01, 0x2a, 0x57, 0x0a, 0x0c,
	0x44, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x16, 0x0a, 0x12,
	0x44, 0x4f, 0x43, 0x55, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x54, 0x45,
	0x58, 0x54, 0x10, 0x00, 0x12, 0x18, 0x0a, 0x14, 0x44, 0x4f, 0x43, 0x55, 0x4d, 0x45, 0x4e, 0x54,
	0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x46, 0x45, 0x49, 0x53, 0x48, 0x55, 0x10, 0x01, 0x12, 0x15,
	0x0a, 0x11, 0x44, 0x4f, 0x43, 0x55, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f,
	0x44, 0x4f, 0x43, 0x10, 0x02, 0x2a, 0x6b, 0x0a, 0x0e, 0x44, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e,
	0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1e, 0x0a, 0x1a, 0x44, 0x4f, 0x43, 0x55, 0x4d,
	0x45, 0x4e, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x50, 0x52, 0x4f, 0x43, 0x45,
	0x53, 0x53, 0x49, 0x4e, 0x47, 0x10, 0x00, 0x12, 0x1d, 0x0a, 0x19, 0x44, 0x4f, 0x43, 0x55, 0x4d,
	0x45, 0x4e, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x43, 0x4f, 0x4d, 0x50, 0x4c,
	0x45, 0x54, 0x45, 0x44, 0x10, 0x01, 0x12, 0x1a, 0x0a, 0x16, 0x44, 0x4f, 0x43, 0x55, 0x4d, 0x45,
	0x4e, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44,
	0x10, 0x02, 0x42, 0x3c, 0x5a, 0x3a, 0x67, 0x69, 0x74, 0x6c, 0x61, 0x62, 0x2e, 0x74, 0x74, 0x79,
	0x75, 0x79, 0x69, 0x6e, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x54, 0x65, 0x73, 0x74, 0x44, 0x65, 0x76,
	0x65, 0x6c, 0x6f, 0x70, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x70, 0x72, 0x6f, 0x62, 0x65, 0x2d, 0x62,
	0x61, 0x63, 0x6b, 0x65, 0x6e, 0x64, 0x2f, 0x61, 0x69, 0x2f, 0x72, 0x70, 0x63, 0x2f, 0x70, 0x62,
	0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_ai_base_proto_rawDescOnce sync.Once
	file_ai_base_proto_rawDescData = file_ai_base_proto_rawDesc
)

func file_ai_base_proto_rawDescGZIP() []byte {
	file_ai_base_proto_rawDescOnce.Do(func() {
		file_ai_base_proto_rawDescData = protoimpl.X.CompressGZIP(file_ai_base_proto_rawDescData)
	})
	return file_ai_base_proto_rawDescData
}

var file_ai_base_proto_enumTypes = make([]protoimpl.EnumInfo, 6)
var file_ai_base_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_ai_base_proto_goTypes = []any{
	(WorkPriorityType)(0),     // 0: ai.WorkPriorityType
	(WorkSpaceType)(0),        // 1: ai.WorkSpaceType
	(MindType)(0),             // 2: ai.MindType
	(SessionStatus)(0),        // 3: ai.SessionStatus
	(DocumentType)(0),         // 4: ai.DocumentType
	(DocumentStatus)(0),       // 5: ai.DocumentStatus
	(*Relation)(nil),          // 6: ai.Relation
	(*_struct.ListValue)(nil), // 7: google.protobuf.ListValue
}
var file_ai_base_proto_depIdxs = []int32{
	7, // 0: ai.Relation.children:type_name -> google.protobuf.ListValue
	1, // [1:1] is the sub-list for method output_type
	1, // [1:1] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_ai_base_proto_init() }
func file_ai_base_proto_init() {
	if File_ai_base_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_ai_base_proto_rawDesc,
			NumEnums:      6,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_ai_base_proto_goTypes,
		DependencyIndexes: file_ai_base_proto_depIdxs,
		EnumInfos:         file_ai_base_proto_enumTypes,
		MessageInfos:      file_ai_base_proto_msgTypes,
	}.Build()
	File_ai_base_proto = out.File
	file_ai_base_proto_rawDesc = nil
	file_ai_base_proto_goTypes = nil
	file_ai_base_proto_depIdxs = nil
}
