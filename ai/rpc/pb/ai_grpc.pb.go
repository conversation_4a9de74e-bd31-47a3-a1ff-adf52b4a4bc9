// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v3.7.1
// source: ai/ai.proto

package pb

import (
	context "context"

	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	BlackBoxCaseTwBetaService_GetBlackBoxCaseTwBetaCaseContent_FullMethodName    = "/ai.BlackBoxCaseTwBetaService/GetBlackBoxCaseTwBetaCaseContent"
	BlackBoxCaseTwBetaService_UpdateBlackBoxCaseTwBetaMindContent_FullMethodName = "/ai.BlackBoxCaseTwBetaService/UpdateBlackBoxCaseTwBetaMindContent"
	BlackBoxCaseTwBetaService_CreateBlackBoxCaseTwBeta_FullMethodName            = "/ai.BlackBoxCaseTwBetaService/CreateBlackBoxCaseTwBeta"
	BlackBoxCaseTwBetaService_ListBlackBoxCaseTwBeta_FullMethodName              = "/ai.BlackBoxCaseTwBetaService/ListBlackBoxCaseTwBeta"
	BlackBoxCaseTwBetaService_CreateBlackBoxCaseTwBetaMind_FullMethodName        = "/ai.BlackBoxCaseTwBetaService/createBlackBoxCaseTwBetaMind"
	BlackBoxCaseTwBetaService_ListBlackBoxCaseTwBetaMind_FullMethodName          = "/ai.BlackBoxCaseTwBetaService/ListBlackBoxCaseTwBetaMind"
	BlackBoxCaseTwBetaService_GetBlackBoxCaseTwBetaMindContent_FullMethodName    = "/ai.BlackBoxCaseTwBetaService/GetBlackBoxCaseTwBetaMindContent"
	BlackBoxCaseTwBetaService_ListBlackBoxCaseTwBetaMindCheck_FullMethodName     = "/ai.BlackBoxCaseTwBetaService/ListBlackBoxCaseTwBetaMindCheck"
	BlackBoxCaseTwBetaService_DeleteBlackBoxCaseTwBeta_FullMethodName            = "/ai.BlackBoxCaseTwBetaService/DeleteBlackBoxCaseTwBeta"
)

// BlackBoxCaseTwBetaServiceClient is the client API for BlackBoxCaseTwBetaService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type BlackBoxCaseTwBetaServiceClient interface {
	// GetBlackBoxCaseTwBetaCaseContent
	GetBlackBoxCaseTwBetaCaseContent(ctx context.Context, in *GetBlackBoxCaseTwBetaContentReq, opts ...grpc.CallOption) (*GetBlackBoxCaseTwBetaContentResp, error)
	// UpdateBlackBoxCaseTwBetaMindContent
	UpdateBlackBoxCaseTwBetaMindContent(ctx context.Context, in *UpdateBlackBoxCaseMindTwBetaReq, opts ...grpc.CallOption) (*UpdateBlackBoxCaseMindTwBetaResp, error)
	// CreateBlackBoxCaseTwBeta list black box case of tw beta
	CreateBlackBoxCaseTwBeta(ctx context.Context, in *CreateBlackBoxCaseTwBetaReq, opts ...grpc.CallOption) (*CreateBlackBoxCaseTwBetaResp, error)
	// ListBlackBoxCaseTwBeta list black box case of tw beta
	ListBlackBoxCaseTwBeta(ctx context.Context, in *ListBlackBoxCaseTwBetaReq, opts ...grpc.CallOption) (*ListBlackBoxCaseTwBetaResp, error)
	// CreateBlackBoxCaseTwBetaMind create black box case of tw beta mind
	CreateBlackBoxCaseTwBetaMind(ctx context.Context, in *CreateBlackBoxCaseTwBetaMindReq, opts ...grpc.CallOption) (*CreateBlackBoxCaseTwBetaMindResp, error)
	// ListBlackBoxCaseTwBetaMind list black box case of tw beta mind
	ListBlackBoxCaseTwBetaMind(ctx context.Context, in *ListBlackBoxCaseTwBetaMindReq, opts ...grpc.CallOption) (*ListBlackBoxCaseTwBetaMindResp, error)
	// GetBlackBoxCaseTwBetaMindContent get black box case of tw beta mind content
	GetBlackBoxCaseTwBetaMindContent(ctx context.Context, in *GetBlackBoxCaseTwBetaMindContentReq, opts ...grpc.CallOption) (*GetBlackBoxCaseTwBetaMindContentResp, error)
	// ListBlackBoxCaseTwBetaMindCheck list black box case of tw beta mind check
	ListBlackBoxCaseTwBetaMindCheck(ctx context.Context, in *ListBlackBoxCaseTwBetaMindCheckReq, opts ...grpc.CallOption) (*ListBlackBoxCaseTwBetaMindCheckResp, error)
	// DeleteBlackBoxCaseTwBeta delete black box case of tw beta
	DeleteBlackBoxCaseTwBeta(ctx context.Context, in *DeleteBlackBoxCaseTwBetaReq, opts ...grpc.CallOption) (*DeleteBlackBoxCaseTwBetaResp, error)
}

type blackBoxCaseTwBetaServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewBlackBoxCaseTwBetaServiceClient(cc grpc.ClientConnInterface) BlackBoxCaseTwBetaServiceClient {
	return &blackBoxCaseTwBetaServiceClient{cc}
}

func (c *blackBoxCaseTwBetaServiceClient) GetBlackBoxCaseTwBetaCaseContent(ctx context.Context, in *GetBlackBoxCaseTwBetaContentReq, opts ...grpc.CallOption) (*GetBlackBoxCaseTwBetaContentResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetBlackBoxCaseTwBetaContentResp)
	err := c.cc.Invoke(ctx, BlackBoxCaseTwBetaService_GetBlackBoxCaseTwBetaCaseContent_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *blackBoxCaseTwBetaServiceClient) UpdateBlackBoxCaseTwBetaMindContent(ctx context.Context, in *UpdateBlackBoxCaseMindTwBetaReq, opts ...grpc.CallOption) (*UpdateBlackBoxCaseMindTwBetaResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UpdateBlackBoxCaseMindTwBetaResp)
	err := c.cc.Invoke(ctx, BlackBoxCaseTwBetaService_UpdateBlackBoxCaseTwBetaMindContent_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *blackBoxCaseTwBetaServiceClient) CreateBlackBoxCaseTwBeta(ctx context.Context, in *CreateBlackBoxCaseTwBetaReq, opts ...grpc.CallOption) (*CreateBlackBoxCaseTwBetaResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateBlackBoxCaseTwBetaResp)
	err := c.cc.Invoke(ctx, BlackBoxCaseTwBetaService_CreateBlackBoxCaseTwBeta_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *blackBoxCaseTwBetaServiceClient) ListBlackBoxCaseTwBeta(ctx context.Context, in *ListBlackBoxCaseTwBetaReq, opts ...grpc.CallOption) (*ListBlackBoxCaseTwBetaResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListBlackBoxCaseTwBetaResp)
	err := c.cc.Invoke(ctx, BlackBoxCaseTwBetaService_ListBlackBoxCaseTwBeta_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *blackBoxCaseTwBetaServiceClient) CreateBlackBoxCaseTwBetaMind(ctx context.Context, in *CreateBlackBoxCaseTwBetaMindReq, opts ...grpc.CallOption) (*CreateBlackBoxCaseTwBetaMindResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateBlackBoxCaseTwBetaMindResp)
	err := c.cc.Invoke(ctx, BlackBoxCaseTwBetaService_CreateBlackBoxCaseTwBetaMind_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *blackBoxCaseTwBetaServiceClient) ListBlackBoxCaseTwBetaMind(ctx context.Context, in *ListBlackBoxCaseTwBetaMindReq, opts ...grpc.CallOption) (*ListBlackBoxCaseTwBetaMindResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListBlackBoxCaseTwBetaMindResp)
	err := c.cc.Invoke(ctx, BlackBoxCaseTwBetaService_ListBlackBoxCaseTwBetaMind_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *blackBoxCaseTwBetaServiceClient) GetBlackBoxCaseTwBetaMindContent(ctx context.Context, in *GetBlackBoxCaseTwBetaMindContentReq, opts ...grpc.CallOption) (*GetBlackBoxCaseTwBetaMindContentResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetBlackBoxCaseTwBetaMindContentResp)
	err := c.cc.Invoke(ctx, BlackBoxCaseTwBetaService_GetBlackBoxCaseTwBetaMindContent_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *blackBoxCaseTwBetaServiceClient) ListBlackBoxCaseTwBetaMindCheck(ctx context.Context, in *ListBlackBoxCaseTwBetaMindCheckReq, opts ...grpc.CallOption) (*ListBlackBoxCaseTwBetaMindCheckResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListBlackBoxCaseTwBetaMindCheckResp)
	err := c.cc.Invoke(ctx, BlackBoxCaseTwBetaService_ListBlackBoxCaseTwBetaMindCheck_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *blackBoxCaseTwBetaServiceClient) DeleteBlackBoxCaseTwBeta(ctx context.Context, in *DeleteBlackBoxCaseTwBetaReq, opts ...grpc.CallOption) (*DeleteBlackBoxCaseTwBetaResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DeleteBlackBoxCaseTwBetaResp)
	err := c.cc.Invoke(ctx, BlackBoxCaseTwBetaService_DeleteBlackBoxCaseTwBeta_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// BlackBoxCaseTwBetaServiceServer is the server API for BlackBoxCaseTwBetaService service.
// All implementations must embed UnimplementedBlackBoxCaseTwBetaServiceServer
// for forward compatibility.
type BlackBoxCaseTwBetaServiceServer interface {
	// GetBlackBoxCaseTwBetaCaseContent
	GetBlackBoxCaseTwBetaCaseContent(context.Context, *GetBlackBoxCaseTwBetaContentReq) (*GetBlackBoxCaseTwBetaContentResp, error)
	// UpdateBlackBoxCaseTwBetaMindContent
	UpdateBlackBoxCaseTwBetaMindContent(context.Context, *UpdateBlackBoxCaseMindTwBetaReq) (*UpdateBlackBoxCaseMindTwBetaResp, error)
	// CreateBlackBoxCaseTwBeta list black box case of tw beta
	CreateBlackBoxCaseTwBeta(context.Context, *CreateBlackBoxCaseTwBetaReq) (*CreateBlackBoxCaseTwBetaResp, error)
	// ListBlackBoxCaseTwBeta list black box case of tw beta
	ListBlackBoxCaseTwBeta(context.Context, *ListBlackBoxCaseTwBetaReq) (*ListBlackBoxCaseTwBetaResp, error)
	// CreateBlackBoxCaseTwBetaMind create black box case of tw beta mind
	CreateBlackBoxCaseTwBetaMind(context.Context, *CreateBlackBoxCaseTwBetaMindReq) (*CreateBlackBoxCaseTwBetaMindResp, error)
	// ListBlackBoxCaseTwBetaMind list black box case of tw beta mind
	ListBlackBoxCaseTwBetaMind(context.Context, *ListBlackBoxCaseTwBetaMindReq) (*ListBlackBoxCaseTwBetaMindResp, error)
	// GetBlackBoxCaseTwBetaMindContent get black box case of tw beta mind content
	GetBlackBoxCaseTwBetaMindContent(context.Context, *GetBlackBoxCaseTwBetaMindContentReq) (*GetBlackBoxCaseTwBetaMindContentResp, error)
	// ListBlackBoxCaseTwBetaMindCheck list black box case of tw beta mind check
	ListBlackBoxCaseTwBetaMindCheck(context.Context, *ListBlackBoxCaseTwBetaMindCheckReq) (*ListBlackBoxCaseTwBetaMindCheckResp, error)
	// DeleteBlackBoxCaseTwBeta delete black box case of tw beta
	DeleteBlackBoxCaseTwBeta(context.Context, *DeleteBlackBoxCaseTwBetaReq) (*DeleteBlackBoxCaseTwBetaResp, error)
	mustEmbedUnimplementedBlackBoxCaseTwBetaServiceServer()
}

// UnimplementedBlackBoxCaseTwBetaServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedBlackBoxCaseTwBetaServiceServer struct{}

func (UnimplementedBlackBoxCaseTwBetaServiceServer) GetBlackBoxCaseTwBetaCaseContent(context.Context, *GetBlackBoxCaseTwBetaContentReq) (*GetBlackBoxCaseTwBetaContentResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetBlackBoxCaseTwBetaCaseContent not implemented")
}
func (UnimplementedBlackBoxCaseTwBetaServiceServer) UpdateBlackBoxCaseTwBetaMindContent(context.Context, *UpdateBlackBoxCaseMindTwBetaReq) (*UpdateBlackBoxCaseMindTwBetaResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateBlackBoxCaseTwBetaMindContent not implemented")
}
func (UnimplementedBlackBoxCaseTwBetaServiceServer) CreateBlackBoxCaseTwBeta(context.Context, *CreateBlackBoxCaseTwBetaReq) (*CreateBlackBoxCaseTwBetaResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateBlackBoxCaseTwBeta not implemented")
}
func (UnimplementedBlackBoxCaseTwBetaServiceServer) ListBlackBoxCaseTwBeta(context.Context, *ListBlackBoxCaseTwBetaReq) (*ListBlackBoxCaseTwBetaResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListBlackBoxCaseTwBeta not implemented")
}
func (UnimplementedBlackBoxCaseTwBetaServiceServer) CreateBlackBoxCaseTwBetaMind(context.Context, *CreateBlackBoxCaseTwBetaMindReq) (*CreateBlackBoxCaseTwBetaMindResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateBlackBoxCaseTwBetaMind not implemented")
}
func (UnimplementedBlackBoxCaseTwBetaServiceServer) ListBlackBoxCaseTwBetaMind(context.Context, *ListBlackBoxCaseTwBetaMindReq) (*ListBlackBoxCaseTwBetaMindResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListBlackBoxCaseTwBetaMind not implemented")
}
func (UnimplementedBlackBoxCaseTwBetaServiceServer) GetBlackBoxCaseTwBetaMindContent(context.Context, *GetBlackBoxCaseTwBetaMindContentReq) (*GetBlackBoxCaseTwBetaMindContentResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetBlackBoxCaseTwBetaMindContent not implemented")
}
func (UnimplementedBlackBoxCaseTwBetaServiceServer) ListBlackBoxCaseTwBetaMindCheck(context.Context, *ListBlackBoxCaseTwBetaMindCheckReq) (*ListBlackBoxCaseTwBetaMindCheckResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListBlackBoxCaseTwBetaMindCheck not implemented")
}
func (UnimplementedBlackBoxCaseTwBetaServiceServer) DeleteBlackBoxCaseTwBeta(context.Context, *DeleteBlackBoxCaseTwBetaReq) (*DeleteBlackBoxCaseTwBetaResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteBlackBoxCaseTwBeta not implemented")
}
func (UnimplementedBlackBoxCaseTwBetaServiceServer) mustEmbedUnimplementedBlackBoxCaseTwBetaServiceServer() {
}
func (UnimplementedBlackBoxCaseTwBetaServiceServer) testEmbeddedByValue() {}

// UnsafeBlackBoxCaseTwBetaServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to BlackBoxCaseTwBetaServiceServer will
// result in compilation errors.
type UnsafeBlackBoxCaseTwBetaServiceServer interface {
	mustEmbedUnimplementedBlackBoxCaseTwBetaServiceServer()
}

func RegisterBlackBoxCaseTwBetaServiceServer(s grpc.ServiceRegistrar, srv BlackBoxCaseTwBetaServiceServer) {
	// If the following call pancis, it indicates UnimplementedBlackBoxCaseTwBetaServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&BlackBoxCaseTwBetaService_ServiceDesc, srv)
}

func _BlackBoxCaseTwBetaService_GetBlackBoxCaseTwBetaCaseContent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetBlackBoxCaseTwBetaContentReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BlackBoxCaseTwBetaServiceServer).GetBlackBoxCaseTwBetaCaseContent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BlackBoxCaseTwBetaService_GetBlackBoxCaseTwBetaCaseContent_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BlackBoxCaseTwBetaServiceServer).GetBlackBoxCaseTwBetaCaseContent(ctx, req.(*GetBlackBoxCaseTwBetaContentReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BlackBoxCaseTwBetaService_UpdateBlackBoxCaseTwBetaMindContent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateBlackBoxCaseMindTwBetaReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BlackBoxCaseTwBetaServiceServer).UpdateBlackBoxCaseTwBetaMindContent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BlackBoxCaseTwBetaService_UpdateBlackBoxCaseTwBetaMindContent_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BlackBoxCaseTwBetaServiceServer).UpdateBlackBoxCaseTwBetaMindContent(ctx, req.(*UpdateBlackBoxCaseMindTwBetaReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BlackBoxCaseTwBetaService_CreateBlackBoxCaseTwBeta_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateBlackBoxCaseTwBetaReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BlackBoxCaseTwBetaServiceServer).CreateBlackBoxCaseTwBeta(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BlackBoxCaseTwBetaService_CreateBlackBoxCaseTwBeta_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BlackBoxCaseTwBetaServiceServer).CreateBlackBoxCaseTwBeta(ctx, req.(*CreateBlackBoxCaseTwBetaReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BlackBoxCaseTwBetaService_ListBlackBoxCaseTwBeta_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListBlackBoxCaseTwBetaReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BlackBoxCaseTwBetaServiceServer).ListBlackBoxCaseTwBeta(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BlackBoxCaseTwBetaService_ListBlackBoxCaseTwBeta_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BlackBoxCaseTwBetaServiceServer).ListBlackBoxCaseTwBeta(ctx, req.(*ListBlackBoxCaseTwBetaReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BlackBoxCaseTwBetaService_CreateBlackBoxCaseTwBetaMind_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateBlackBoxCaseTwBetaMindReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BlackBoxCaseTwBetaServiceServer).CreateBlackBoxCaseTwBetaMind(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BlackBoxCaseTwBetaService_CreateBlackBoxCaseTwBetaMind_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BlackBoxCaseTwBetaServiceServer).CreateBlackBoxCaseTwBetaMind(ctx, req.(*CreateBlackBoxCaseTwBetaMindReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BlackBoxCaseTwBetaService_ListBlackBoxCaseTwBetaMind_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListBlackBoxCaseTwBetaMindReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BlackBoxCaseTwBetaServiceServer).ListBlackBoxCaseTwBetaMind(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BlackBoxCaseTwBetaService_ListBlackBoxCaseTwBetaMind_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BlackBoxCaseTwBetaServiceServer).ListBlackBoxCaseTwBetaMind(ctx, req.(*ListBlackBoxCaseTwBetaMindReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BlackBoxCaseTwBetaService_GetBlackBoxCaseTwBetaMindContent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetBlackBoxCaseTwBetaMindContentReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BlackBoxCaseTwBetaServiceServer).GetBlackBoxCaseTwBetaMindContent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BlackBoxCaseTwBetaService_GetBlackBoxCaseTwBetaMindContent_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BlackBoxCaseTwBetaServiceServer).GetBlackBoxCaseTwBetaMindContent(ctx, req.(*GetBlackBoxCaseTwBetaMindContentReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BlackBoxCaseTwBetaService_ListBlackBoxCaseTwBetaMindCheck_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListBlackBoxCaseTwBetaMindCheckReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BlackBoxCaseTwBetaServiceServer).ListBlackBoxCaseTwBetaMindCheck(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BlackBoxCaseTwBetaService_ListBlackBoxCaseTwBetaMindCheck_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BlackBoxCaseTwBetaServiceServer).ListBlackBoxCaseTwBetaMindCheck(ctx, req.(*ListBlackBoxCaseTwBetaMindCheckReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BlackBoxCaseTwBetaService_DeleteBlackBoxCaseTwBeta_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteBlackBoxCaseTwBetaReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BlackBoxCaseTwBetaServiceServer).DeleteBlackBoxCaseTwBeta(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BlackBoxCaseTwBetaService_DeleteBlackBoxCaseTwBeta_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BlackBoxCaseTwBetaServiceServer).DeleteBlackBoxCaseTwBeta(ctx, req.(*DeleteBlackBoxCaseTwBetaReq))
	}
	return interceptor(ctx, in, info, handler)
}

// BlackBoxCaseTwBetaService_ServiceDesc is the grpc.ServiceDesc for BlackBoxCaseTwBetaService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var BlackBoxCaseTwBetaService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "ai.BlackBoxCaseTwBetaService",
	HandlerType: (*BlackBoxCaseTwBetaServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetBlackBoxCaseTwBetaCaseContent",
			Handler:    _BlackBoxCaseTwBetaService_GetBlackBoxCaseTwBetaCaseContent_Handler,
		},
		{
			MethodName: "UpdateBlackBoxCaseTwBetaMindContent",
			Handler:    _BlackBoxCaseTwBetaService_UpdateBlackBoxCaseTwBetaMindContent_Handler,
		},
		{
			MethodName: "CreateBlackBoxCaseTwBeta",
			Handler:    _BlackBoxCaseTwBetaService_CreateBlackBoxCaseTwBeta_Handler,
		},
		{
			MethodName: "ListBlackBoxCaseTwBeta",
			Handler:    _BlackBoxCaseTwBetaService_ListBlackBoxCaseTwBeta_Handler,
		},
		{
			MethodName: "createBlackBoxCaseTwBetaMind",
			Handler:    _BlackBoxCaseTwBetaService_CreateBlackBoxCaseTwBetaMind_Handler,
		},
		{
			MethodName: "ListBlackBoxCaseTwBetaMind",
			Handler:    _BlackBoxCaseTwBetaService_ListBlackBoxCaseTwBetaMind_Handler,
		},
		{
			MethodName: "GetBlackBoxCaseTwBetaMindContent",
			Handler:    _BlackBoxCaseTwBetaService_GetBlackBoxCaseTwBetaMindContent_Handler,
		},
		{
			MethodName: "ListBlackBoxCaseTwBetaMindCheck",
			Handler:    _BlackBoxCaseTwBetaService_ListBlackBoxCaseTwBetaMindCheck_Handler,
		},
		{
			MethodName: "DeleteBlackBoxCaseTwBeta",
			Handler:    _BlackBoxCaseTwBetaService_DeleteBlackBoxCaseTwBeta_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "ai/ai.proto",
}

const (
	BlackBoxCaseAssistantService_CreateBlackBoxCaseAssistant_FullMethodName = "/ai.BlackBoxCaseAssistantService/CreateBlackBoxCaseAssistant"
	BlackBoxCaseAssistantService_UpdateBlackBoxCaseAssistant_FullMethodName = "/ai.BlackBoxCaseAssistantService/UpdateBlackBoxCaseAssistant"
	BlackBoxCaseAssistantService_DeleteBlackBoxCaseAssistant_FullMethodName = "/ai.BlackBoxCaseAssistantService/DeleteBlackBoxCaseAssistant"
	BlackBoxCaseAssistantService_GetBlackBoxCaseAssistant_FullMethodName    = "/ai.BlackBoxCaseAssistantService/GetBlackBoxCaseAssistant"
	BlackBoxCaseAssistantService_SearchBlackBoxCaseAssistant_FullMethodName = "/ai.BlackBoxCaseAssistantService/SearchBlackBoxCaseAssistant"
)

// BlackBoxCaseAssistantServiceClient is the client API for BlackBoxCaseAssistantService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// BlackBoxCaseAssistantService
type BlackBoxCaseAssistantServiceClient interface {
	// CreateBlackBoxCaseAssistant creates a black box case assistant
	CreateBlackBoxCaseAssistant(ctx context.Context, in *CreateBlackBoxCaseAssistantReq, opts ...grpc.CallOption) (*CreateBlackBoxCaseAssistantResp, error)
	// UpdateBlackBoxCaseAssistant updates a black box case assistant
	UpdateBlackBoxCaseAssistant(ctx context.Context, in *UpdateBlackBoxCaseAssistantReq, opts ...grpc.CallOption) (*UpdateBlackBoxCaseAssistantResp, error)
	// DeleteBlackBoxCaseAssistant deletes a black box case assistant
	DeleteBlackBoxCaseAssistant(ctx context.Context, in *DeleteBlackBoxCaseAssistantReq, opts ...grpc.CallOption) (*DeleteBlackBoxCaseAssistantResp, error)
	// GetBlackBoxCaseAssistant gets a black box case assistant
	GetBlackBoxCaseAssistant(ctx context.Context, in *GetBlackBoxCaseAssistantReq, opts ...grpc.CallOption) (*GetBlackBoxCaseAssistantResp, error)
	// SearchBlackBoxCaseAssistant searches for black box case assistants
	SearchBlackBoxCaseAssistant(ctx context.Context, in *SearchBlackBoxCaseAssistantReq, opts ...grpc.CallOption) (*SearchBlackBoxCaseAssistantResp, error)
}

type blackBoxCaseAssistantServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewBlackBoxCaseAssistantServiceClient(cc grpc.ClientConnInterface) BlackBoxCaseAssistantServiceClient {
	return &blackBoxCaseAssistantServiceClient{cc}
}

func (c *blackBoxCaseAssistantServiceClient) CreateBlackBoxCaseAssistant(ctx context.Context, in *CreateBlackBoxCaseAssistantReq, opts ...grpc.CallOption) (*CreateBlackBoxCaseAssistantResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateBlackBoxCaseAssistantResp)
	err := c.cc.Invoke(ctx, BlackBoxCaseAssistantService_CreateBlackBoxCaseAssistant_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *blackBoxCaseAssistantServiceClient) UpdateBlackBoxCaseAssistant(ctx context.Context, in *UpdateBlackBoxCaseAssistantReq, opts ...grpc.CallOption) (*UpdateBlackBoxCaseAssistantResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UpdateBlackBoxCaseAssistantResp)
	err := c.cc.Invoke(ctx, BlackBoxCaseAssistantService_UpdateBlackBoxCaseAssistant_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *blackBoxCaseAssistantServiceClient) DeleteBlackBoxCaseAssistant(ctx context.Context, in *DeleteBlackBoxCaseAssistantReq, opts ...grpc.CallOption) (*DeleteBlackBoxCaseAssistantResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DeleteBlackBoxCaseAssistantResp)
	err := c.cc.Invoke(ctx, BlackBoxCaseAssistantService_DeleteBlackBoxCaseAssistant_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *blackBoxCaseAssistantServiceClient) GetBlackBoxCaseAssistant(ctx context.Context, in *GetBlackBoxCaseAssistantReq, opts ...grpc.CallOption) (*GetBlackBoxCaseAssistantResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetBlackBoxCaseAssistantResp)
	err := c.cc.Invoke(ctx, BlackBoxCaseAssistantService_GetBlackBoxCaseAssistant_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *blackBoxCaseAssistantServiceClient) SearchBlackBoxCaseAssistant(ctx context.Context, in *SearchBlackBoxCaseAssistantReq, opts ...grpc.CallOption) (*SearchBlackBoxCaseAssistantResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SearchBlackBoxCaseAssistantResp)
	err := c.cc.Invoke(ctx, BlackBoxCaseAssistantService_SearchBlackBoxCaseAssistant_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// BlackBoxCaseAssistantServiceServer is the server API for BlackBoxCaseAssistantService service.
// All implementations must embed UnimplementedBlackBoxCaseAssistantServiceServer
// for forward compatibility.
//
// BlackBoxCaseAssistantService
type BlackBoxCaseAssistantServiceServer interface {
	// CreateBlackBoxCaseAssistant creates a black box case assistant
	CreateBlackBoxCaseAssistant(context.Context, *CreateBlackBoxCaseAssistantReq) (*CreateBlackBoxCaseAssistantResp, error)
	// UpdateBlackBoxCaseAssistant updates a black box case assistant
	UpdateBlackBoxCaseAssistant(context.Context, *UpdateBlackBoxCaseAssistantReq) (*UpdateBlackBoxCaseAssistantResp, error)
	// DeleteBlackBoxCaseAssistant deletes a black box case assistant
	DeleteBlackBoxCaseAssistant(context.Context, *DeleteBlackBoxCaseAssistantReq) (*DeleteBlackBoxCaseAssistantResp, error)
	// GetBlackBoxCaseAssistant gets a black box case assistant
	GetBlackBoxCaseAssistant(context.Context, *GetBlackBoxCaseAssistantReq) (*GetBlackBoxCaseAssistantResp, error)
	// SearchBlackBoxCaseAssistant searches for black box case assistants
	SearchBlackBoxCaseAssistant(context.Context, *SearchBlackBoxCaseAssistantReq) (*SearchBlackBoxCaseAssistantResp, error)
	mustEmbedUnimplementedBlackBoxCaseAssistantServiceServer()
}

// UnimplementedBlackBoxCaseAssistantServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedBlackBoxCaseAssistantServiceServer struct{}

func (UnimplementedBlackBoxCaseAssistantServiceServer) CreateBlackBoxCaseAssistant(context.Context, *CreateBlackBoxCaseAssistantReq) (*CreateBlackBoxCaseAssistantResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateBlackBoxCaseAssistant not implemented")
}
func (UnimplementedBlackBoxCaseAssistantServiceServer) UpdateBlackBoxCaseAssistant(context.Context, *UpdateBlackBoxCaseAssistantReq) (*UpdateBlackBoxCaseAssistantResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateBlackBoxCaseAssistant not implemented")
}
func (UnimplementedBlackBoxCaseAssistantServiceServer) DeleteBlackBoxCaseAssistant(context.Context, *DeleteBlackBoxCaseAssistantReq) (*DeleteBlackBoxCaseAssistantResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteBlackBoxCaseAssistant not implemented")
}
func (UnimplementedBlackBoxCaseAssistantServiceServer) GetBlackBoxCaseAssistant(context.Context, *GetBlackBoxCaseAssistantReq) (*GetBlackBoxCaseAssistantResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetBlackBoxCaseAssistant not implemented")
}
func (UnimplementedBlackBoxCaseAssistantServiceServer) SearchBlackBoxCaseAssistant(context.Context, *SearchBlackBoxCaseAssistantReq) (*SearchBlackBoxCaseAssistantResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SearchBlackBoxCaseAssistant not implemented")
}
func (UnimplementedBlackBoxCaseAssistantServiceServer) mustEmbedUnimplementedBlackBoxCaseAssistantServiceServer() {
}
func (UnimplementedBlackBoxCaseAssistantServiceServer) testEmbeddedByValue() {}

// UnsafeBlackBoxCaseAssistantServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to BlackBoxCaseAssistantServiceServer will
// result in compilation errors.
type UnsafeBlackBoxCaseAssistantServiceServer interface {
	mustEmbedUnimplementedBlackBoxCaseAssistantServiceServer()
}

func RegisterBlackBoxCaseAssistantServiceServer(s grpc.ServiceRegistrar, srv BlackBoxCaseAssistantServiceServer) {
	// If the following call pancis, it indicates UnimplementedBlackBoxCaseAssistantServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&BlackBoxCaseAssistantService_ServiceDesc, srv)
}

func _BlackBoxCaseAssistantService_CreateBlackBoxCaseAssistant_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateBlackBoxCaseAssistantReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BlackBoxCaseAssistantServiceServer).CreateBlackBoxCaseAssistant(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BlackBoxCaseAssistantService_CreateBlackBoxCaseAssistant_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BlackBoxCaseAssistantServiceServer).CreateBlackBoxCaseAssistant(ctx, req.(*CreateBlackBoxCaseAssistantReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BlackBoxCaseAssistantService_UpdateBlackBoxCaseAssistant_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateBlackBoxCaseAssistantReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BlackBoxCaseAssistantServiceServer).UpdateBlackBoxCaseAssistant(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BlackBoxCaseAssistantService_UpdateBlackBoxCaseAssistant_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BlackBoxCaseAssistantServiceServer).UpdateBlackBoxCaseAssistant(ctx, req.(*UpdateBlackBoxCaseAssistantReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BlackBoxCaseAssistantService_DeleteBlackBoxCaseAssistant_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteBlackBoxCaseAssistantReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BlackBoxCaseAssistantServiceServer).DeleteBlackBoxCaseAssistant(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BlackBoxCaseAssistantService_DeleteBlackBoxCaseAssistant_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BlackBoxCaseAssistantServiceServer).DeleteBlackBoxCaseAssistant(ctx, req.(*DeleteBlackBoxCaseAssistantReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BlackBoxCaseAssistantService_GetBlackBoxCaseAssistant_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetBlackBoxCaseAssistantReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BlackBoxCaseAssistantServiceServer).GetBlackBoxCaseAssistant(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BlackBoxCaseAssistantService_GetBlackBoxCaseAssistant_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BlackBoxCaseAssistantServiceServer).GetBlackBoxCaseAssistant(ctx, req.(*GetBlackBoxCaseAssistantReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BlackBoxCaseAssistantService_SearchBlackBoxCaseAssistant_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SearchBlackBoxCaseAssistantReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BlackBoxCaseAssistantServiceServer).SearchBlackBoxCaseAssistant(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BlackBoxCaseAssistantService_SearchBlackBoxCaseAssistant_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BlackBoxCaseAssistantServiceServer).SearchBlackBoxCaseAssistant(ctx, req.(*SearchBlackBoxCaseAssistantReq))
	}
	return interceptor(ctx, in, info, handler)
}

// BlackBoxCaseAssistantService_ServiceDesc is the grpc.ServiceDesc for BlackBoxCaseAssistantService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var BlackBoxCaseAssistantService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "ai.BlackBoxCaseAssistantService",
	HandlerType: (*BlackBoxCaseAssistantServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateBlackBoxCaseAssistant",
			Handler:    _BlackBoxCaseAssistantService_CreateBlackBoxCaseAssistant_Handler,
		},
		{
			MethodName: "UpdateBlackBoxCaseAssistant",
			Handler:    _BlackBoxCaseAssistantService_UpdateBlackBoxCaseAssistant_Handler,
		},
		{
			MethodName: "DeleteBlackBoxCaseAssistant",
			Handler:    _BlackBoxCaseAssistantService_DeleteBlackBoxCaseAssistant_Handler,
		},
		{
			MethodName: "GetBlackBoxCaseAssistant",
			Handler:    _BlackBoxCaseAssistantService_GetBlackBoxCaseAssistant_Handler,
		},
		{
			MethodName: "SearchBlackBoxCaseAssistant",
			Handler:    _BlackBoxCaseAssistantService_SearchBlackBoxCaseAssistant_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "ai/ai.proto",
}

const (
	BlackBoxCaseDocumentService_CreateBlackBoxCaseDocumentForAssistant_FullMethodName       = "/ai.BlackBoxCaseDocumentService/CreateBlackBoxCaseDocumentForAssistant"
	BlackBoxCaseDocumentService_CreateBlackBoxCaseDocumentForSession_FullMethodName         = "/ai.BlackBoxCaseDocumentService/CreateBlackBoxCaseDocumentForSession"
	BlackBoxCaseDocumentService_CreateBlackBoxCaseDocumentForSessionRecv_FullMethodName     = "/ai.BlackBoxCaseDocumentService/CreateBlackBoxCaseDocumentForSessionRecv"
	BlackBoxCaseDocumentService_UpdateBlackBoxCaseDocumentStatusForAssistant_FullMethodName = "/ai.BlackBoxCaseDocumentService/UpdateBlackBoxCaseDocumentStatusForAssistant"
	BlackBoxCaseDocumentService_GetBlackBoxCaseDocument_FullMethodName                      = "/ai.BlackBoxCaseDocumentService/GetBlackBoxCaseDocument"
	BlackBoxCaseDocumentService_UpdateBlackBoxCaseDocument_FullMethodName                   = "/ai.BlackBoxCaseDocumentService/UpdateBlackBoxCaseDocument"
	BlackBoxCaseDocumentService_ReloadBlackBoxCaseDocument_FullMethodName                   = "/ai.BlackBoxCaseDocumentService/ReloadBlackBoxCaseDocument"
	BlackBoxCaseDocumentService_DeleteBlackBoxCaseDocumentForAssistant_FullMethodName       = "/ai.BlackBoxCaseDocumentService/DeleteBlackBoxCaseDocumentForAssistant"
	BlackBoxCaseDocumentService_BatchDeleteBlackBoxCaseDocumentForAssistant_FullMethodName  = "/ai.BlackBoxCaseDocumentService/BatchDeleteBlackBoxCaseDocumentForAssistant"
	BlackBoxCaseDocumentService_DeleteBlackBoxCaseDocumentForSession_FullMethodName         = "/ai.BlackBoxCaseDocumentService/DeleteBlackBoxCaseDocumentForSession"
	BlackBoxCaseDocumentService_BatchDeleteBlackBoxCaseDocumentForSession_FullMethodName    = "/ai.BlackBoxCaseDocumentService/BatchDeleteBlackBoxCaseDocumentForSession"
	BlackBoxCaseDocumentService_SearchBlackBoxCaseDocumentForAssistant_FullMethodName       = "/ai.BlackBoxCaseDocumentService/SearchBlackBoxCaseDocumentForAssistant"
	BlackBoxCaseDocumentService_SearchBlackBoxCaseDocumentForSession_FullMethodName         = "/ai.BlackBoxCaseDocumentService/SearchBlackBoxCaseDocumentForSession"
	BlackBoxCaseDocumentService_GetBlackBoxCaseDocumentHeadersList_FullMethodName           = "/ai.BlackBoxCaseDocumentService/GetBlackBoxCaseDocumentHeadersList"
)

// BlackBoxCaseDocumentServiceClient is the client API for BlackBoxCaseDocumentService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// BlackBoxCaseDocumentService
type BlackBoxCaseDocumentServiceClient interface {
	// CreateBlackBoxCaseDocumentForAssistant creates a black box case document for assistant
	CreateBlackBoxCaseDocumentForAssistant(ctx context.Context, in *CreateBlackBoxCaseDocumentForAssistantReq, opts ...grpc.CallOption) (*CreateBlackBoxCaseDocumentForAssistantResp, error)
	// CreateBlackBoxCaseDocumentForSession creates a black box case document for session
	CreateBlackBoxCaseDocumentForSession(ctx context.Context, in *CreateBlackBoxCaseDocumentForSessionReq, opts ...grpc.CallOption) (*CreateBlackBoxCaseDocumentForSessionResp, error)
	// CreateBlackBoxCaseDocumentForSessionRecv creates a black box case document for session recv
	CreateBlackBoxCaseDocumentForSessionRecv(ctx context.Context, in *CreateBlackBoxCaseDocumentForSessionRecvReq, opts ...grpc.CallOption) (*CreateBlackBoxCaseDocumentForSessionRecvResp, error)
	// UpdateBlackBoxCaseDocumentStatusForAssistant update black box case document status for assistant
	UpdateBlackBoxCaseDocumentStatusForAssistant(ctx context.Context, in *UpdateBlackBoxCaseDocumentStatusForAssistantReq, opts ...grpc.CallOption) (*UpdateBlackBoxCaseDocumentStatusForAssistantResp, error)
	// GetBlackBoxCaseDocument gets a black box case document
	GetBlackBoxCaseDocument(ctx context.Context, in *GetBlackBoxCaseDocumentReq, opts ...grpc.CallOption) (*GetBlackBoxCaseDocumentResp, error)
	// UpdateBlackBoxCaseDocument updates a black box case document
	UpdateBlackBoxCaseDocument(ctx context.Context, in *UpdateBlackBoxCaseDocumentReq, opts ...grpc.CallOption) (*UpdateBlackBoxCaseDocumentResp, error)
	// ReloadBlackBoxCaseDocument reload black box case document
	ReloadBlackBoxCaseDocument(ctx context.Context, in *ReloadBlackBoxCaseDocumentReq, opts ...grpc.CallOption) (*ReloadBlackBoxCaseDocumentResp, error)
	// DeleteBlackBoxCaseDocumentForAssistant deletes a black box case document for assistant
	DeleteBlackBoxCaseDocumentForAssistant(ctx context.Context, in *DeleteBlackBoxCaseDocumentForAssistantReq, opts ...grpc.CallOption) (*DeleteBlackBoxCaseDocumentForAssistantResp, error)
	// BatchDeleteBlackBoxCaseDocumentForAssistant batch deletes black box case documents for assistant
	BatchDeleteBlackBoxCaseDocumentForAssistant(ctx context.Context, in *BatchDeleteBlackBoxCaseDocumentForAssistantReq, opts ...grpc.CallOption) (*BatchDeleteBlackBoxCaseDocumentForAssistantResp, error)
	// DeleteBlackBoxCaseDocumentForSession deletes a black box case document for session
	DeleteBlackBoxCaseDocumentForSession(ctx context.Context, in *DeleteBlackBoxCaseDocumentForSessionReq, opts ...grpc.CallOption) (*DeleteBlackBoxCaseDocumentForSessionResp, error)
	// BatchDeleteBlackBoxCaseDocumentForSession batch deletes black box case documents for session
	BatchDeleteBlackBoxCaseDocumentForSession(ctx context.Context, in *BatchDeleteBlackBoxCaseDocumentForSessionReq, opts ...grpc.CallOption) (*BatchDeleteBlackBoxCaseDocumentForSessionResp, error)
	// SearchBlackBoxCaseDocumentForAssistant searches for black box case documents for assistant
	SearchBlackBoxCaseDocumentForAssistant(ctx context.Context, in *SearchBlackBoxCaseDocumentForAssistantReq, opts ...grpc.CallOption) (*SearchBlackBoxCaseDocumentForAssistantResp, error)
	// SearchBlackBoxCaseDocumentForSession searches for black box case documents for session
	SearchBlackBoxCaseDocumentForSession(ctx context.Context, in *SearchBlackBoxCaseDocumentForSessionReq, opts ...grpc.CallOption) (*SearchBlackBoxCaseDocumentForSessionResp, error)
	// GetBlackBoxCaseDocumentHeadersList get black box case document Headers list
	GetBlackBoxCaseDocumentHeadersList(ctx context.Context, in *GetBlackBoxCaseDocumentHeadersListReq, opts ...grpc.CallOption) (*GetBlackBoxCaseDocumentHeadersListResp, error)
}

type blackBoxCaseDocumentServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewBlackBoxCaseDocumentServiceClient(cc grpc.ClientConnInterface) BlackBoxCaseDocumentServiceClient {
	return &blackBoxCaseDocumentServiceClient{cc}
}

func (c *blackBoxCaseDocumentServiceClient) CreateBlackBoxCaseDocumentForAssistant(ctx context.Context, in *CreateBlackBoxCaseDocumentForAssistantReq, opts ...grpc.CallOption) (*CreateBlackBoxCaseDocumentForAssistantResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateBlackBoxCaseDocumentForAssistantResp)
	err := c.cc.Invoke(ctx, BlackBoxCaseDocumentService_CreateBlackBoxCaseDocumentForAssistant_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *blackBoxCaseDocumentServiceClient) CreateBlackBoxCaseDocumentForSession(ctx context.Context, in *CreateBlackBoxCaseDocumentForSessionReq, opts ...grpc.CallOption) (*CreateBlackBoxCaseDocumentForSessionResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateBlackBoxCaseDocumentForSessionResp)
	err := c.cc.Invoke(ctx, BlackBoxCaseDocumentService_CreateBlackBoxCaseDocumentForSession_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *blackBoxCaseDocumentServiceClient) CreateBlackBoxCaseDocumentForSessionRecv(ctx context.Context, in *CreateBlackBoxCaseDocumentForSessionRecvReq, opts ...grpc.CallOption) (*CreateBlackBoxCaseDocumentForSessionRecvResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateBlackBoxCaseDocumentForSessionRecvResp)
	err := c.cc.Invoke(ctx, BlackBoxCaseDocumentService_CreateBlackBoxCaseDocumentForSessionRecv_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *blackBoxCaseDocumentServiceClient) UpdateBlackBoxCaseDocumentStatusForAssistant(ctx context.Context, in *UpdateBlackBoxCaseDocumentStatusForAssistantReq, opts ...grpc.CallOption) (*UpdateBlackBoxCaseDocumentStatusForAssistantResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UpdateBlackBoxCaseDocumentStatusForAssistantResp)
	err := c.cc.Invoke(ctx, BlackBoxCaseDocumentService_UpdateBlackBoxCaseDocumentStatusForAssistant_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *blackBoxCaseDocumentServiceClient) GetBlackBoxCaseDocument(ctx context.Context, in *GetBlackBoxCaseDocumentReq, opts ...grpc.CallOption) (*GetBlackBoxCaseDocumentResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetBlackBoxCaseDocumentResp)
	err := c.cc.Invoke(ctx, BlackBoxCaseDocumentService_GetBlackBoxCaseDocument_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *blackBoxCaseDocumentServiceClient) UpdateBlackBoxCaseDocument(ctx context.Context, in *UpdateBlackBoxCaseDocumentReq, opts ...grpc.CallOption) (*UpdateBlackBoxCaseDocumentResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UpdateBlackBoxCaseDocumentResp)
	err := c.cc.Invoke(ctx, BlackBoxCaseDocumentService_UpdateBlackBoxCaseDocument_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *blackBoxCaseDocumentServiceClient) ReloadBlackBoxCaseDocument(ctx context.Context, in *ReloadBlackBoxCaseDocumentReq, opts ...grpc.CallOption) (*ReloadBlackBoxCaseDocumentResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ReloadBlackBoxCaseDocumentResp)
	err := c.cc.Invoke(ctx, BlackBoxCaseDocumentService_ReloadBlackBoxCaseDocument_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *blackBoxCaseDocumentServiceClient) DeleteBlackBoxCaseDocumentForAssistant(ctx context.Context, in *DeleteBlackBoxCaseDocumentForAssistantReq, opts ...grpc.CallOption) (*DeleteBlackBoxCaseDocumentForAssistantResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DeleteBlackBoxCaseDocumentForAssistantResp)
	err := c.cc.Invoke(ctx, BlackBoxCaseDocumentService_DeleteBlackBoxCaseDocumentForAssistant_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *blackBoxCaseDocumentServiceClient) BatchDeleteBlackBoxCaseDocumentForAssistant(ctx context.Context, in *BatchDeleteBlackBoxCaseDocumentForAssistantReq, opts ...grpc.CallOption) (*BatchDeleteBlackBoxCaseDocumentForAssistantResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(BatchDeleteBlackBoxCaseDocumentForAssistantResp)
	err := c.cc.Invoke(ctx, BlackBoxCaseDocumentService_BatchDeleteBlackBoxCaseDocumentForAssistant_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *blackBoxCaseDocumentServiceClient) DeleteBlackBoxCaseDocumentForSession(ctx context.Context, in *DeleteBlackBoxCaseDocumentForSessionReq, opts ...grpc.CallOption) (*DeleteBlackBoxCaseDocumentForSessionResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DeleteBlackBoxCaseDocumentForSessionResp)
	err := c.cc.Invoke(ctx, BlackBoxCaseDocumentService_DeleteBlackBoxCaseDocumentForSession_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *blackBoxCaseDocumentServiceClient) BatchDeleteBlackBoxCaseDocumentForSession(ctx context.Context, in *BatchDeleteBlackBoxCaseDocumentForSessionReq, opts ...grpc.CallOption) (*BatchDeleteBlackBoxCaseDocumentForSessionResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(BatchDeleteBlackBoxCaseDocumentForSessionResp)
	err := c.cc.Invoke(ctx, BlackBoxCaseDocumentService_BatchDeleteBlackBoxCaseDocumentForSession_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *blackBoxCaseDocumentServiceClient) SearchBlackBoxCaseDocumentForAssistant(ctx context.Context, in *SearchBlackBoxCaseDocumentForAssistantReq, opts ...grpc.CallOption) (*SearchBlackBoxCaseDocumentForAssistantResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SearchBlackBoxCaseDocumentForAssistantResp)
	err := c.cc.Invoke(ctx, BlackBoxCaseDocumentService_SearchBlackBoxCaseDocumentForAssistant_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *blackBoxCaseDocumentServiceClient) SearchBlackBoxCaseDocumentForSession(ctx context.Context, in *SearchBlackBoxCaseDocumentForSessionReq, opts ...grpc.CallOption) (*SearchBlackBoxCaseDocumentForSessionResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SearchBlackBoxCaseDocumentForSessionResp)
	err := c.cc.Invoke(ctx, BlackBoxCaseDocumentService_SearchBlackBoxCaseDocumentForSession_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *blackBoxCaseDocumentServiceClient) GetBlackBoxCaseDocumentHeadersList(ctx context.Context, in *GetBlackBoxCaseDocumentHeadersListReq, opts ...grpc.CallOption) (*GetBlackBoxCaseDocumentHeadersListResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetBlackBoxCaseDocumentHeadersListResp)
	err := c.cc.Invoke(ctx, BlackBoxCaseDocumentService_GetBlackBoxCaseDocumentHeadersList_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// BlackBoxCaseDocumentServiceServer is the server API for BlackBoxCaseDocumentService service.
// All implementations must embed UnimplementedBlackBoxCaseDocumentServiceServer
// for forward compatibility.
//
// BlackBoxCaseDocumentService
type BlackBoxCaseDocumentServiceServer interface {
	// CreateBlackBoxCaseDocumentForAssistant creates a black box case document for assistant
	CreateBlackBoxCaseDocumentForAssistant(context.Context, *CreateBlackBoxCaseDocumentForAssistantReq) (*CreateBlackBoxCaseDocumentForAssistantResp, error)
	// CreateBlackBoxCaseDocumentForSession creates a black box case document for session
	CreateBlackBoxCaseDocumentForSession(context.Context, *CreateBlackBoxCaseDocumentForSessionReq) (*CreateBlackBoxCaseDocumentForSessionResp, error)
	// CreateBlackBoxCaseDocumentForSessionRecv creates a black box case document for session recv
	CreateBlackBoxCaseDocumentForSessionRecv(context.Context, *CreateBlackBoxCaseDocumentForSessionRecvReq) (*CreateBlackBoxCaseDocumentForSessionRecvResp, error)
	// UpdateBlackBoxCaseDocumentStatusForAssistant update black box case document status for assistant
	UpdateBlackBoxCaseDocumentStatusForAssistant(context.Context, *UpdateBlackBoxCaseDocumentStatusForAssistantReq) (*UpdateBlackBoxCaseDocumentStatusForAssistantResp, error)
	// GetBlackBoxCaseDocument gets a black box case document
	GetBlackBoxCaseDocument(context.Context, *GetBlackBoxCaseDocumentReq) (*GetBlackBoxCaseDocumentResp, error)
	// UpdateBlackBoxCaseDocument updates a black box case document
	UpdateBlackBoxCaseDocument(context.Context, *UpdateBlackBoxCaseDocumentReq) (*UpdateBlackBoxCaseDocumentResp, error)
	// ReloadBlackBoxCaseDocument reload black box case document
	ReloadBlackBoxCaseDocument(context.Context, *ReloadBlackBoxCaseDocumentReq) (*ReloadBlackBoxCaseDocumentResp, error)
	// DeleteBlackBoxCaseDocumentForAssistant deletes a black box case document for assistant
	DeleteBlackBoxCaseDocumentForAssistant(context.Context, *DeleteBlackBoxCaseDocumentForAssistantReq) (*DeleteBlackBoxCaseDocumentForAssistantResp, error)
	// BatchDeleteBlackBoxCaseDocumentForAssistant batch deletes black box case documents for assistant
	BatchDeleteBlackBoxCaseDocumentForAssistant(context.Context, *BatchDeleteBlackBoxCaseDocumentForAssistantReq) (*BatchDeleteBlackBoxCaseDocumentForAssistantResp, error)
	// DeleteBlackBoxCaseDocumentForSession deletes a black box case document for session
	DeleteBlackBoxCaseDocumentForSession(context.Context, *DeleteBlackBoxCaseDocumentForSessionReq) (*DeleteBlackBoxCaseDocumentForSessionResp, error)
	// BatchDeleteBlackBoxCaseDocumentForSession batch deletes black box case documents for session
	BatchDeleteBlackBoxCaseDocumentForSession(context.Context, *BatchDeleteBlackBoxCaseDocumentForSessionReq) (*BatchDeleteBlackBoxCaseDocumentForSessionResp, error)
	// SearchBlackBoxCaseDocumentForAssistant searches for black box case documents for assistant
	SearchBlackBoxCaseDocumentForAssistant(context.Context, *SearchBlackBoxCaseDocumentForAssistantReq) (*SearchBlackBoxCaseDocumentForAssistantResp, error)
	// SearchBlackBoxCaseDocumentForSession searches for black box case documents for session
	SearchBlackBoxCaseDocumentForSession(context.Context, *SearchBlackBoxCaseDocumentForSessionReq) (*SearchBlackBoxCaseDocumentForSessionResp, error)
	// GetBlackBoxCaseDocumentHeadersList get black box case document Headers list
	GetBlackBoxCaseDocumentHeadersList(context.Context, *GetBlackBoxCaseDocumentHeadersListReq) (*GetBlackBoxCaseDocumentHeadersListResp, error)
	mustEmbedUnimplementedBlackBoxCaseDocumentServiceServer()
}

// UnimplementedBlackBoxCaseDocumentServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedBlackBoxCaseDocumentServiceServer struct{}

func (UnimplementedBlackBoxCaseDocumentServiceServer) CreateBlackBoxCaseDocumentForAssistant(context.Context, *CreateBlackBoxCaseDocumentForAssistantReq) (*CreateBlackBoxCaseDocumentForAssistantResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateBlackBoxCaseDocumentForAssistant not implemented")
}
func (UnimplementedBlackBoxCaseDocumentServiceServer) CreateBlackBoxCaseDocumentForSession(context.Context, *CreateBlackBoxCaseDocumentForSessionReq) (*CreateBlackBoxCaseDocumentForSessionResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateBlackBoxCaseDocumentForSession not implemented")
}
func (UnimplementedBlackBoxCaseDocumentServiceServer) CreateBlackBoxCaseDocumentForSessionRecv(context.Context, *CreateBlackBoxCaseDocumentForSessionRecvReq) (*CreateBlackBoxCaseDocumentForSessionRecvResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateBlackBoxCaseDocumentForSessionRecv not implemented")
}
func (UnimplementedBlackBoxCaseDocumentServiceServer) UpdateBlackBoxCaseDocumentStatusForAssistant(context.Context, *UpdateBlackBoxCaseDocumentStatusForAssistantReq) (*UpdateBlackBoxCaseDocumentStatusForAssistantResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateBlackBoxCaseDocumentStatusForAssistant not implemented")
}
func (UnimplementedBlackBoxCaseDocumentServiceServer) GetBlackBoxCaseDocument(context.Context, *GetBlackBoxCaseDocumentReq) (*GetBlackBoxCaseDocumentResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetBlackBoxCaseDocument not implemented")
}
func (UnimplementedBlackBoxCaseDocumentServiceServer) UpdateBlackBoxCaseDocument(context.Context, *UpdateBlackBoxCaseDocumentReq) (*UpdateBlackBoxCaseDocumentResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateBlackBoxCaseDocument not implemented")
}
func (UnimplementedBlackBoxCaseDocumentServiceServer) ReloadBlackBoxCaseDocument(context.Context, *ReloadBlackBoxCaseDocumentReq) (*ReloadBlackBoxCaseDocumentResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ReloadBlackBoxCaseDocument not implemented")
}
func (UnimplementedBlackBoxCaseDocumentServiceServer) DeleteBlackBoxCaseDocumentForAssistant(context.Context, *DeleteBlackBoxCaseDocumentForAssistantReq) (*DeleteBlackBoxCaseDocumentForAssistantResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteBlackBoxCaseDocumentForAssistant not implemented")
}
func (UnimplementedBlackBoxCaseDocumentServiceServer) BatchDeleteBlackBoxCaseDocumentForAssistant(context.Context, *BatchDeleteBlackBoxCaseDocumentForAssistantReq) (*BatchDeleteBlackBoxCaseDocumentForAssistantResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BatchDeleteBlackBoxCaseDocumentForAssistant not implemented")
}
func (UnimplementedBlackBoxCaseDocumentServiceServer) DeleteBlackBoxCaseDocumentForSession(context.Context, *DeleteBlackBoxCaseDocumentForSessionReq) (*DeleteBlackBoxCaseDocumentForSessionResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteBlackBoxCaseDocumentForSession not implemented")
}
func (UnimplementedBlackBoxCaseDocumentServiceServer) BatchDeleteBlackBoxCaseDocumentForSession(context.Context, *BatchDeleteBlackBoxCaseDocumentForSessionReq) (*BatchDeleteBlackBoxCaseDocumentForSessionResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BatchDeleteBlackBoxCaseDocumentForSession not implemented")
}
func (UnimplementedBlackBoxCaseDocumentServiceServer) SearchBlackBoxCaseDocumentForAssistant(context.Context, *SearchBlackBoxCaseDocumentForAssistantReq) (*SearchBlackBoxCaseDocumentForAssistantResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SearchBlackBoxCaseDocumentForAssistant not implemented")
}
func (UnimplementedBlackBoxCaseDocumentServiceServer) SearchBlackBoxCaseDocumentForSession(context.Context, *SearchBlackBoxCaseDocumentForSessionReq) (*SearchBlackBoxCaseDocumentForSessionResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SearchBlackBoxCaseDocumentForSession not implemented")
}
func (UnimplementedBlackBoxCaseDocumentServiceServer) GetBlackBoxCaseDocumentHeadersList(context.Context, *GetBlackBoxCaseDocumentHeadersListReq) (*GetBlackBoxCaseDocumentHeadersListResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetBlackBoxCaseDocumentHeadersList not implemented")
}
func (UnimplementedBlackBoxCaseDocumentServiceServer) mustEmbedUnimplementedBlackBoxCaseDocumentServiceServer() {
}
func (UnimplementedBlackBoxCaseDocumentServiceServer) testEmbeddedByValue() {}

// UnsafeBlackBoxCaseDocumentServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to BlackBoxCaseDocumentServiceServer will
// result in compilation errors.
type UnsafeBlackBoxCaseDocumentServiceServer interface {
	mustEmbedUnimplementedBlackBoxCaseDocumentServiceServer()
}

func RegisterBlackBoxCaseDocumentServiceServer(s grpc.ServiceRegistrar, srv BlackBoxCaseDocumentServiceServer) {
	// If the following call pancis, it indicates UnimplementedBlackBoxCaseDocumentServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&BlackBoxCaseDocumentService_ServiceDesc, srv)
}

func _BlackBoxCaseDocumentService_CreateBlackBoxCaseDocumentForAssistant_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateBlackBoxCaseDocumentForAssistantReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BlackBoxCaseDocumentServiceServer).CreateBlackBoxCaseDocumentForAssistant(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BlackBoxCaseDocumentService_CreateBlackBoxCaseDocumentForAssistant_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BlackBoxCaseDocumentServiceServer).CreateBlackBoxCaseDocumentForAssistant(ctx, req.(*CreateBlackBoxCaseDocumentForAssistantReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BlackBoxCaseDocumentService_CreateBlackBoxCaseDocumentForSession_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateBlackBoxCaseDocumentForSessionReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BlackBoxCaseDocumentServiceServer).CreateBlackBoxCaseDocumentForSession(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BlackBoxCaseDocumentService_CreateBlackBoxCaseDocumentForSession_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BlackBoxCaseDocumentServiceServer).CreateBlackBoxCaseDocumentForSession(ctx, req.(*CreateBlackBoxCaseDocumentForSessionReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BlackBoxCaseDocumentService_CreateBlackBoxCaseDocumentForSessionRecv_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateBlackBoxCaseDocumentForSessionRecvReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BlackBoxCaseDocumentServiceServer).CreateBlackBoxCaseDocumentForSessionRecv(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BlackBoxCaseDocumentService_CreateBlackBoxCaseDocumentForSessionRecv_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BlackBoxCaseDocumentServiceServer).CreateBlackBoxCaseDocumentForSessionRecv(ctx, req.(*CreateBlackBoxCaseDocumentForSessionRecvReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BlackBoxCaseDocumentService_UpdateBlackBoxCaseDocumentStatusForAssistant_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateBlackBoxCaseDocumentStatusForAssistantReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BlackBoxCaseDocumentServiceServer).UpdateBlackBoxCaseDocumentStatusForAssistant(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BlackBoxCaseDocumentService_UpdateBlackBoxCaseDocumentStatusForAssistant_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BlackBoxCaseDocumentServiceServer).UpdateBlackBoxCaseDocumentStatusForAssistant(ctx, req.(*UpdateBlackBoxCaseDocumentStatusForAssistantReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BlackBoxCaseDocumentService_GetBlackBoxCaseDocument_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetBlackBoxCaseDocumentReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BlackBoxCaseDocumentServiceServer).GetBlackBoxCaseDocument(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BlackBoxCaseDocumentService_GetBlackBoxCaseDocument_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BlackBoxCaseDocumentServiceServer).GetBlackBoxCaseDocument(ctx, req.(*GetBlackBoxCaseDocumentReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BlackBoxCaseDocumentService_UpdateBlackBoxCaseDocument_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateBlackBoxCaseDocumentReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BlackBoxCaseDocumentServiceServer).UpdateBlackBoxCaseDocument(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BlackBoxCaseDocumentService_UpdateBlackBoxCaseDocument_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BlackBoxCaseDocumentServiceServer).UpdateBlackBoxCaseDocument(ctx, req.(*UpdateBlackBoxCaseDocumentReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BlackBoxCaseDocumentService_ReloadBlackBoxCaseDocument_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReloadBlackBoxCaseDocumentReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BlackBoxCaseDocumentServiceServer).ReloadBlackBoxCaseDocument(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BlackBoxCaseDocumentService_ReloadBlackBoxCaseDocument_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BlackBoxCaseDocumentServiceServer).ReloadBlackBoxCaseDocument(ctx, req.(*ReloadBlackBoxCaseDocumentReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BlackBoxCaseDocumentService_DeleteBlackBoxCaseDocumentForAssistant_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteBlackBoxCaseDocumentForAssistantReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BlackBoxCaseDocumentServiceServer).DeleteBlackBoxCaseDocumentForAssistant(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BlackBoxCaseDocumentService_DeleteBlackBoxCaseDocumentForAssistant_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BlackBoxCaseDocumentServiceServer).DeleteBlackBoxCaseDocumentForAssistant(ctx, req.(*DeleteBlackBoxCaseDocumentForAssistantReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BlackBoxCaseDocumentService_BatchDeleteBlackBoxCaseDocumentForAssistant_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchDeleteBlackBoxCaseDocumentForAssistantReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BlackBoxCaseDocumentServiceServer).BatchDeleteBlackBoxCaseDocumentForAssistant(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BlackBoxCaseDocumentService_BatchDeleteBlackBoxCaseDocumentForAssistant_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BlackBoxCaseDocumentServiceServer).BatchDeleteBlackBoxCaseDocumentForAssistant(ctx, req.(*BatchDeleteBlackBoxCaseDocumentForAssistantReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BlackBoxCaseDocumentService_DeleteBlackBoxCaseDocumentForSession_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteBlackBoxCaseDocumentForSessionReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BlackBoxCaseDocumentServiceServer).DeleteBlackBoxCaseDocumentForSession(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BlackBoxCaseDocumentService_DeleteBlackBoxCaseDocumentForSession_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BlackBoxCaseDocumentServiceServer).DeleteBlackBoxCaseDocumentForSession(ctx, req.(*DeleteBlackBoxCaseDocumentForSessionReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BlackBoxCaseDocumentService_BatchDeleteBlackBoxCaseDocumentForSession_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchDeleteBlackBoxCaseDocumentForSessionReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BlackBoxCaseDocumentServiceServer).BatchDeleteBlackBoxCaseDocumentForSession(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BlackBoxCaseDocumentService_BatchDeleteBlackBoxCaseDocumentForSession_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BlackBoxCaseDocumentServiceServer).BatchDeleteBlackBoxCaseDocumentForSession(ctx, req.(*BatchDeleteBlackBoxCaseDocumentForSessionReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BlackBoxCaseDocumentService_SearchBlackBoxCaseDocumentForAssistant_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SearchBlackBoxCaseDocumentForAssistantReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BlackBoxCaseDocumentServiceServer).SearchBlackBoxCaseDocumentForAssistant(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BlackBoxCaseDocumentService_SearchBlackBoxCaseDocumentForAssistant_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BlackBoxCaseDocumentServiceServer).SearchBlackBoxCaseDocumentForAssistant(ctx, req.(*SearchBlackBoxCaseDocumentForAssistantReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BlackBoxCaseDocumentService_SearchBlackBoxCaseDocumentForSession_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SearchBlackBoxCaseDocumentForSessionReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BlackBoxCaseDocumentServiceServer).SearchBlackBoxCaseDocumentForSession(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BlackBoxCaseDocumentService_SearchBlackBoxCaseDocumentForSession_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BlackBoxCaseDocumentServiceServer).SearchBlackBoxCaseDocumentForSession(ctx, req.(*SearchBlackBoxCaseDocumentForSessionReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BlackBoxCaseDocumentService_GetBlackBoxCaseDocumentHeadersList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetBlackBoxCaseDocumentHeadersListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BlackBoxCaseDocumentServiceServer).GetBlackBoxCaseDocumentHeadersList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BlackBoxCaseDocumentService_GetBlackBoxCaseDocumentHeadersList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BlackBoxCaseDocumentServiceServer).GetBlackBoxCaseDocumentHeadersList(ctx, req.(*GetBlackBoxCaseDocumentHeadersListReq))
	}
	return interceptor(ctx, in, info, handler)
}

// BlackBoxCaseDocumentService_ServiceDesc is the grpc.ServiceDesc for BlackBoxCaseDocumentService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var BlackBoxCaseDocumentService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "ai.BlackBoxCaseDocumentService",
	HandlerType: (*BlackBoxCaseDocumentServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateBlackBoxCaseDocumentForAssistant",
			Handler:    _BlackBoxCaseDocumentService_CreateBlackBoxCaseDocumentForAssistant_Handler,
		},
		{
			MethodName: "CreateBlackBoxCaseDocumentForSession",
			Handler:    _BlackBoxCaseDocumentService_CreateBlackBoxCaseDocumentForSession_Handler,
		},
		{
			MethodName: "CreateBlackBoxCaseDocumentForSessionRecv",
			Handler:    _BlackBoxCaseDocumentService_CreateBlackBoxCaseDocumentForSessionRecv_Handler,
		},
		{
			MethodName: "UpdateBlackBoxCaseDocumentStatusForAssistant",
			Handler:    _BlackBoxCaseDocumentService_UpdateBlackBoxCaseDocumentStatusForAssistant_Handler,
		},
		{
			MethodName: "GetBlackBoxCaseDocument",
			Handler:    _BlackBoxCaseDocumentService_GetBlackBoxCaseDocument_Handler,
		},
		{
			MethodName: "UpdateBlackBoxCaseDocument",
			Handler:    _BlackBoxCaseDocumentService_UpdateBlackBoxCaseDocument_Handler,
		},
		{
			MethodName: "ReloadBlackBoxCaseDocument",
			Handler:    _BlackBoxCaseDocumentService_ReloadBlackBoxCaseDocument_Handler,
		},
		{
			MethodName: "DeleteBlackBoxCaseDocumentForAssistant",
			Handler:    _BlackBoxCaseDocumentService_DeleteBlackBoxCaseDocumentForAssistant_Handler,
		},
		{
			MethodName: "BatchDeleteBlackBoxCaseDocumentForAssistant",
			Handler:    _BlackBoxCaseDocumentService_BatchDeleteBlackBoxCaseDocumentForAssistant_Handler,
		},
		{
			MethodName: "DeleteBlackBoxCaseDocumentForSession",
			Handler:    _BlackBoxCaseDocumentService_DeleteBlackBoxCaseDocumentForSession_Handler,
		},
		{
			MethodName: "BatchDeleteBlackBoxCaseDocumentForSession",
			Handler:    _BlackBoxCaseDocumentService_BatchDeleteBlackBoxCaseDocumentForSession_Handler,
		},
		{
			MethodName: "SearchBlackBoxCaseDocumentForAssistant",
			Handler:    _BlackBoxCaseDocumentService_SearchBlackBoxCaseDocumentForAssistant_Handler,
		},
		{
			MethodName: "SearchBlackBoxCaseDocumentForSession",
			Handler:    _BlackBoxCaseDocumentService_SearchBlackBoxCaseDocumentForSession_Handler,
		},
		{
			MethodName: "GetBlackBoxCaseDocumentHeadersList",
			Handler:    _BlackBoxCaseDocumentService_GetBlackBoxCaseDocumentHeadersList_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "ai/ai.proto",
}

const (
	BlackBoxCaseSessionService_CreateBlackBoxCaseSession_FullMethodName                         = "/ai.BlackBoxCaseSessionService/CreateBlackBoxCaseSession"
	BlackBoxCaseSessionService_UpdateBlackBoxCaseSession_FullMethodName                         = "/ai.BlackBoxCaseSessionService/UpdateBlackBoxCaseSession"
	BlackBoxCaseSessionService_DeleteBlackBoxCaseSession_FullMethodName                         = "/ai.BlackBoxCaseSessionService/DeleteBlackBoxCaseSession"
	BlackBoxCaseSessionService_GetBlackBoxCaseSession_FullMethodName                            = "/ai.BlackBoxCaseSessionService/GetBlackBoxCaseSession"
	BlackBoxCaseSessionService_SearchBlackBoxCaseSession_FullMethodName                         = "/ai.BlackBoxCaseSessionService/SearchBlackBoxCaseSession"
	BlackBoxCaseSessionService_GetBlackBoxCaseSessionHistoryMessageList_FullMethodName          = "/ai.BlackBoxCaseSessionService/GetBlackBoxCaseSessionHistoryMessageList"
	BlackBoxCaseSessionService_RemoveBlackBoxCaseSessionHistoryMessageList_FullMethodName       = "/ai.BlackBoxCaseSessionService/RemoveBlackBoxCaseSessionHistoryMessageList"
	BlackBoxCaseSessionService_SendBlackBoxCaseForSession_FullMethodName                        = "/ai.BlackBoxCaseSessionService/SendBlackBoxCaseForSession"
	BlackBoxCaseSessionService_GetBlackBoxCaseConversationAIState_FullMethodName                = "/ai.BlackBoxCaseSessionService/GetBlackBoxCaseConversationAIState"
	BlackBoxCaseSessionService_GetBlackBoxCaseConversationAIMessage_FullMethodName              = "/ai.BlackBoxCaseSessionService/GetBlackBoxCaseConversationAIMessage"
	BlackBoxCaseSessionService_GetBlackBoxCaseModifyTestCaseReferenceContentList_FullMethodName = "/ai.BlackBoxCaseSessionService/GetBlackBoxCaseModifyTestCaseReferenceContentList"
	BlackBoxCaseSessionService_CreateBlackBoxCaseModifyTestCaseReferenceContent_FullMethodName  = "/ai.BlackBoxCaseSessionService/CreateBlackBoxCaseModifyTestCaseReferenceContent"
	BlackBoxCaseSessionService_ReplaceBlackBoxCase_FullMethodName                               = "/ai.BlackBoxCaseSessionService/ReplaceBlackBoxCase"
	BlackBoxCaseSessionService_MergeBlackBoxCase_FullMethodName                                 = "/ai.BlackBoxCaseSessionService/MergeBlackBoxCase"
	BlackBoxCaseSessionService_TransferBlackBoxCase2XMind_FullMethodName                        = "/ai.BlackBoxCaseSessionService/TransferBlackBoxCase2XMind"
	BlackBoxCaseSessionService_GetBlackBoxCaseAIMessage_FullMethodName                          = "/ai.BlackBoxCaseSessionService/GetBlackBoxCaseAIMessage"
)

// BlackBoxCaseSessionServiceClient is the client API for BlackBoxCaseSessionService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// BlackBoxCaseSessionService
type BlackBoxCaseSessionServiceClient interface {
	// CreateBlackBoxCaseSession creates a black box case session
	CreateBlackBoxCaseSession(ctx context.Context, in *CreateBlackBoxCaseSessionReq, opts ...grpc.CallOption) (*CreateBlackBoxCaseSessionResp, error)
	// UpdateBlackBoxCaseSession updates a black box case session
	UpdateBlackBoxCaseSession(ctx context.Context, in *UpdateBlackBoxCaseSessionReq, opts ...grpc.CallOption) (*UpdateBlackBoxCaseSessionResp, error)
	// DeleteBlackBoxCaseSession deletes a black box case session
	DeleteBlackBoxCaseSession(ctx context.Context, in *DeleteBlackBoxCaseSessionReq, opts ...grpc.CallOption) (*DeleteBlackBoxCaseSessionResp, error)
	// GetBlackBoxCaseSession gets a black box case session
	GetBlackBoxCaseSession(ctx context.Context, in *GetBlackBoxCaseSessionReq, opts ...grpc.CallOption) (*GetBlackBoxCaseSessionResp, error)
	// SearchBlackBoxCaseSession searches for black box case sessions
	SearchBlackBoxCaseSession(ctx context.Context, in *SearchBlackBoxCaseSessionReq, opts ...grpc.CallOption) (*SearchBlackBoxCaseSessionResp, error)
	// GetBlackBoxCaseSessionHistoryMessageList gets black box case session history message list
	GetBlackBoxCaseSessionHistoryMessageList(ctx context.Context, in *GetBlackBoxCaseSessionHistoryMessageListReq, opts ...grpc.CallOption) (*GetBlackBoxCaseSessionHistoryMessageListResp, error)
	// RemoveBlackBoxCaseSessionHistoryMessageList removes black box case session history message list
	RemoveBlackBoxCaseSessionHistoryMessageList(ctx context.Context, in *RemoveBlackBoxCaseSessionHistoryMessageListReq, opts ...grpc.CallOption) (*RemoveBlackBoxCaseSessionHistoryMessageListResp, error)
	// SendBlackBoxCaseForSession sends black box case for session
	SendBlackBoxCaseForSession(ctx context.Context, in *SendBlackBoxCaseForSessionReq, opts ...grpc.CallOption) (*SendBlackBoxCaseForSessionResp, error)
	// GetBlackBoxCaseConversationAIState get black box case conversation ai state
	GetBlackBoxCaseConversationAIState(ctx context.Context, in *GetBlackBoxCaseConversationAIStateReq, opts ...grpc.CallOption) (*GetBlackBoxCaseConversationAIStateResp, error)
	// GetBlackBoxCaseConversationAIMessage get black box case conversation ai message
	GetBlackBoxCaseConversationAIMessage(ctx context.Context, in *GetBlackBoxCaseConversationAIMessageReq, opts ...grpc.CallOption) (*GetBlackBoxCaseConversationAIMessageResp, error)
	// GetBlackBoxCaseConversationAIMessageList get black box case modify reference content list
	GetBlackBoxCaseModifyTestCaseReferenceContentList(ctx context.Context, in *GetBlackBoxCaseModifyTestCaseReferenceContentListReq, opts ...grpc.CallOption) (*GetBlackBoxCaseModifyTestCaseReferenceContentListResp, error)
	// CreateBlackBoxCaseModifyTestCaseReferenceContent create black box case modify reference content
	CreateBlackBoxCaseModifyTestCaseReferenceContent(ctx context.Context, in *CreateBlackBoxCaseModifyTestCaseReferenceContentReq, opts ...grpc.CallOption) (*CreateBlackBoxCaseModifyTestCaseReferenceContentResp, error)
	// ReplaceBlackBoxCase replace black box case
	ReplaceBlackBoxCase(ctx context.Context, in *ReplaceBlackBoxCaseReq, opts ...grpc.CallOption) (*ReplaceBlackBoxCaseResp, error)
	// MergeBlackBoxCase merge black box case
	MergeBlackBoxCase(ctx context.Context, in *MergeBlackBoxCaseReq, opts ...grpc.CallOption) (*MergeBlackBoxCaseResp, error)
	// TransferBlackBoxCase2XMind transfer black box case
	TransferBlackBoxCase2XMind(ctx context.Context, in *TransferBlackBoxCase2XMindReq, opts ...grpc.CallOption) (*TransferBlackBoxCase2XMindResp, error)
	// GetBlackBoxCaseAIMessageList get black box case conversation ai message
	GetBlackBoxCaseAIMessage(ctx context.Context, in *GetCreateBlackBoxCaseAIMessageReq, opts ...grpc.CallOption) (*GetCreateBlackBoxCaseAIMessageResp, error)
}

type blackBoxCaseSessionServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewBlackBoxCaseSessionServiceClient(cc grpc.ClientConnInterface) BlackBoxCaseSessionServiceClient {
	return &blackBoxCaseSessionServiceClient{cc}
}

func (c *blackBoxCaseSessionServiceClient) CreateBlackBoxCaseSession(ctx context.Context, in *CreateBlackBoxCaseSessionReq, opts ...grpc.CallOption) (*CreateBlackBoxCaseSessionResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateBlackBoxCaseSessionResp)
	err := c.cc.Invoke(ctx, BlackBoxCaseSessionService_CreateBlackBoxCaseSession_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *blackBoxCaseSessionServiceClient) UpdateBlackBoxCaseSession(ctx context.Context, in *UpdateBlackBoxCaseSessionReq, opts ...grpc.CallOption) (*UpdateBlackBoxCaseSessionResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UpdateBlackBoxCaseSessionResp)
	err := c.cc.Invoke(ctx, BlackBoxCaseSessionService_UpdateBlackBoxCaseSession_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *blackBoxCaseSessionServiceClient) DeleteBlackBoxCaseSession(ctx context.Context, in *DeleteBlackBoxCaseSessionReq, opts ...grpc.CallOption) (*DeleteBlackBoxCaseSessionResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DeleteBlackBoxCaseSessionResp)
	err := c.cc.Invoke(ctx, BlackBoxCaseSessionService_DeleteBlackBoxCaseSession_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *blackBoxCaseSessionServiceClient) GetBlackBoxCaseSession(ctx context.Context, in *GetBlackBoxCaseSessionReq, opts ...grpc.CallOption) (*GetBlackBoxCaseSessionResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetBlackBoxCaseSessionResp)
	err := c.cc.Invoke(ctx, BlackBoxCaseSessionService_GetBlackBoxCaseSession_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *blackBoxCaseSessionServiceClient) SearchBlackBoxCaseSession(ctx context.Context, in *SearchBlackBoxCaseSessionReq, opts ...grpc.CallOption) (*SearchBlackBoxCaseSessionResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SearchBlackBoxCaseSessionResp)
	err := c.cc.Invoke(ctx, BlackBoxCaseSessionService_SearchBlackBoxCaseSession_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *blackBoxCaseSessionServiceClient) GetBlackBoxCaseSessionHistoryMessageList(ctx context.Context, in *GetBlackBoxCaseSessionHistoryMessageListReq, opts ...grpc.CallOption) (*GetBlackBoxCaseSessionHistoryMessageListResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetBlackBoxCaseSessionHistoryMessageListResp)
	err := c.cc.Invoke(ctx, BlackBoxCaseSessionService_GetBlackBoxCaseSessionHistoryMessageList_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *blackBoxCaseSessionServiceClient) RemoveBlackBoxCaseSessionHistoryMessageList(ctx context.Context, in *RemoveBlackBoxCaseSessionHistoryMessageListReq, opts ...grpc.CallOption) (*RemoveBlackBoxCaseSessionHistoryMessageListResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(RemoveBlackBoxCaseSessionHistoryMessageListResp)
	err := c.cc.Invoke(ctx, BlackBoxCaseSessionService_RemoveBlackBoxCaseSessionHistoryMessageList_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *blackBoxCaseSessionServiceClient) SendBlackBoxCaseForSession(ctx context.Context, in *SendBlackBoxCaseForSessionReq, opts ...grpc.CallOption) (*SendBlackBoxCaseForSessionResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SendBlackBoxCaseForSessionResp)
	err := c.cc.Invoke(ctx, BlackBoxCaseSessionService_SendBlackBoxCaseForSession_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *blackBoxCaseSessionServiceClient) GetBlackBoxCaseConversationAIState(ctx context.Context, in *GetBlackBoxCaseConversationAIStateReq, opts ...grpc.CallOption) (*GetBlackBoxCaseConversationAIStateResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetBlackBoxCaseConversationAIStateResp)
	err := c.cc.Invoke(ctx, BlackBoxCaseSessionService_GetBlackBoxCaseConversationAIState_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *blackBoxCaseSessionServiceClient) GetBlackBoxCaseConversationAIMessage(ctx context.Context, in *GetBlackBoxCaseConversationAIMessageReq, opts ...grpc.CallOption) (*GetBlackBoxCaseConversationAIMessageResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetBlackBoxCaseConversationAIMessageResp)
	err := c.cc.Invoke(ctx, BlackBoxCaseSessionService_GetBlackBoxCaseConversationAIMessage_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *blackBoxCaseSessionServiceClient) GetBlackBoxCaseModifyTestCaseReferenceContentList(ctx context.Context, in *GetBlackBoxCaseModifyTestCaseReferenceContentListReq, opts ...grpc.CallOption) (*GetBlackBoxCaseModifyTestCaseReferenceContentListResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetBlackBoxCaseModifyTestCaseReferenceContentListResp)
	err := c.cc.Invoke(ctx, BlackBoxCaseSessionService_GetBlackBoxCaseModifyTestCaseReferenceContentList_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *blackBoxCaseSessionServiceClient) CreateBlackBoxCaseModifyTestCaseReferenceContent(ctx context.Context, in *CreateBlackBoxCaseModifyTestCaseReferenceContentReq, opts ...grpc.CallOption) (*CreateBlackBoxCaseModifyTestCaseReferenceContentResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateBlackBoxCaseModifyTestCaseReferenceContentResp)
	err := c.cc.Invoke(ctx, BlackBoxCaseSessionService_CreateBlackBoxCaseModifyTestCaseReferenceContent_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *blackBoxCaseSessionServiceClient) ReplaceBlackBoxCase(ctx context.Context, in *ReplaceBlackBoxCaseReq, opts ...grpc.CallOption) (*ReplaceBlackBoxCaseResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ReplaceBlackBoxCaseResp)
	err := c.cc.Invoke(ctx, BlackBoxCaseSessionService_ReplaceBlackBoxCase_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *blackBoxCaseSessionServiceClient) MergeBlackBoxCase(ctx context.Context, in *MergeBlackBoxCaseReq, opts ...grpc.CallOption) (*MergeBlackBoxCaseResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(MergeBlackBoxCaseResp)
	err := c.cc.Invoke(ctx, BlackBoxCaseSessionService_MergeBlackBoxCase_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *blackBoxCaseSessionServiceClient) TransferBlackBoxCase2XMind(ctx context.Context, in *TransferBlackBoxCase2XMindReq, opts ...grpc.CallOption) (*TransferBlackBoxCase2XMindResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(TransferBlackBoxCase2XMindResp)
	err := c.cc.Invoke(ctx, BlackBoxCaseSessionService_TransferBlackBoxCase2XMind_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *blackBoxCaseSessionServiceClient) GetBlackBoxCaseAIMessage(ctx context.Context, in *GetCreateBlackBoxCaseAIMessageReq, opts ...grpc.CallOption) (*GetCreateBlackBoxCaseAIMessageResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetCreateBlackBoxCaseAIMessageResp)
	err := c.cc.Invoke(ctx, BlackBoxCaseSessionService_GetBlackBoxCaseAIMessage_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// BlackBoxCaseSessionServiceServer is the server API for BlackBoxCaseSessionService service.
// All implementations must embed UnimplementedBlackBoxCaseSessionServiceServer
// for forward compatibility.
//
// BlackBoxCaseSessionService
type BlackBoxCaseSessionServiceServer interface {
	// CreateBlackBoxCaseSession creates a black box case session
	CreateBlackBoxCaseSession(context.Context, *CreateBlackBoxCaseSessionReq) (*CreateBlackBoxCaseSessionResp, error)
	// UpdateBlackBoxCaseSession updates a black box case session
	UpdateBlackBoxCaseSession(context.Context, *UpdateBlackBoxCaseSessionReq) (*UpdateBlackBoxCaseSessionResp, error)
	// DeleteBlackBoxCaseSession deletes a black box case session
	DeleteBlackBoxCaseSession(context.Context, *DeleteBlackBoxCaseSessionReq) (*DeleteBlackBoxCaseSessionResp, error)
	// GetBlackBoxCaseSession gets a black box case session
	GetBlackBoxCaseSession(context.Context, *GetBlackBoxCaseSessionReq) (*GetBlackBoxCaseSessionResp, error)
	// SearchBlackBoxCaseSession searches for black box case sessions
	SearchBlackBoxCaseSession(context.Context, *SearchBlackBoxCaseSessionReq) (*SearchBlackBoxCaseSessionResp, error)
	// GetBlackBoxCaseSessionHistoryMessageList gets black box case session history message list
	GetBlackBoxCaseSessionHistoryMessageList(context.Context, *GetBlackBoxCaseSessionHistoryMessageListReq) (*GetBlackBoxCaseSessionHistoryMessageListResp, error)
	// RemoveBlackBoxCaseSessionHistoryMessageList removes black box case session history message list
	RemoveBlackBoxCaseSessionHistoryMessageList(context.Context, *RemoveBlackBoxCaseSessionHistoryMessageListReq) (*RemoveBlackBoxCaseSessionHistoryMessageListResp, error)
	// SendBlackBoxCaseForSession sends black box case for session
	SendBlackBoxCaseForSession(context.Context, *SendBlackBoxCaseForSessionReq) (*SendBlackBoxCaseForSessionResp, error)
	// GetBlackBoxCaseConversationAIState get black box case conversation ai state
	GetBlackBoxCaseConversationAIState(context.Context, *GetBlackBoxCaseConversationAIStateReq) (*GetBlackBoxCaseConversationAIStateResp, error)
	// GetBlackBoxCaseConversationAIMessage get black box case conversation ai message
	GetBlackBoxCaseConversationAIMessage(context.Context, *GetBlackBoxCaseConversationAIMessageReq) (*GetBlackBoxCaseConversationAIMessageResp, error)
	// GetBlackBoxCaseConversationAIMessageList get black box case modify reference content list
	GetBlackBoxCaseModifyTestCaseReferenceContentList(context.Context, *GetBlackBoxCaseModifyTestCaseReferenceContentListReq) (*GetBlackBoxCaseModifyTestCaseReferenceContentListResp, error)
	// CreateBlackBoxCaseModifyTestCaseReferenceContent create black box case modify reference content
	CreateBlackBoxCaseModifyTestCaseReferenceContent(context.Context, *CreateBlackBoxCaseModifyTestCaseReferenceContentReq) (*CreateBlackBoxCaseModifyTestCaseReferenceContentResp, error)
	// ReplaceBlackBoxCase replace black box case
	ReplaceBlackBoxCase(context.Context, *ReplaceBlackBoxCaseReq) (*ReplaceBlackBoxCaseResp, error)
	// MergeBlackBoxCase merge black box case
	MergeBlackBoxCase(context.Context, *MergeBlackBoxCaseReq) (*MergeBlackBoxCaseResp, error)
	// TransferBlackBoxCase2XMind transfer black box case
	TransferBlackBoxCase2XMind(context.Context, *TransferBlackBoxCase2XMindReq) (*TransferBlackBoxCase2XMindResp, error)
	// GetBlackBoxCaseAIMessageList get black box case conversation ai message
	GetBlackBoxCaseAIMessage(context.Context, *GetCreateBlackBoxCaseAIMessageReq) (*GetCreateBlackBoxCaseAIMessageResp, error)
	mustEmbedUnimplementedBlackBoxCaseSessionServiceServer()
}

// UnimplementedBlackBoxCaseSessionServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedBlackBoxCaseSessionServiceServer struct{}

func (UnimplementedBlackBoxCaseSessionServiceServer) CreateBlackBoxCaseSession(context.Context, *CreateBlackBoxCaseSessionReq) (*CreateBlackBoxCaseSessionResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateBlackBoxCaseSession not implemented")
}
func (UnimplementedBlackBoxCaseSessionServiceServer) UpdateBlackBoxCaseSession(context.Context, *UpdateBlackBoxCaseSessionReq) (*UpdateBlackBoxCaseSessionResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateBlackBoxCaseSession not implemented")
}
func (UnimplementedBlackBoxCaseSessionServiceServer) DeleteBlackBoxCaseSession(context.Context, *DeleteBlackBoxCaseSessionReq) (*DeleteBlackBoxCaseSessionResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteBlackBoxCaseSession not implemented")
}
func (UnimplementedBlackBoxCaseSessionServiceServer) GetBlackBoxCaseSession(context.Context, *GetBlackBoxCaseSessionReq) (*GetBlackBoxCaseSessionResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetBlackBoxCaseSession not implemented")
}
func (UnimplementedBlackBoxCaseSessionServiceServer) SearchBlackBoxCaseSession(context.Context, *SearchBlackBoxCaseSessionReq) (*SearchBlackBoxCaseSessionResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SearchBlackBoxCaseSession not implemented")
}
func (UnimplementedBlackBoxCaseSessionServiceServer) GetBlackBoxCaseSessionHistoryMessageList(context.Context, *GetBlackBoxCaseSessionHistoryMessageListReq) (*GetBlackBoxCaseSessionHistoryMessageListResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetBlackBoxCaseSessionHistoryMessageList not implemented")
}
func (UnimplementedBlackBoxCaseSessionServiceServer) RemoveBlackBoxCaseSessionHistoryMessageList(context.Context, *RemoveBlackBoxCaseSessionHistoryMessageListReq) (*RemoveBlackBoxCaseSessionHistoryMessageListResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RemoveBlackBoxCaseSessionHistoryMessageList not implemented")
}
func (UnimplementedBlackBoxCaseSessionServiceServer) SendBlackBoxCaseForSession(context.Context, *SendBlackBoxCaseForSessionReq) (*SendBlackBoxCaseForSessionResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SendBlackBoxCaseForSession not implemented")
}
func (UnimplementedBlackBoxCaseSessionServiceServer) GetBlackBoxCaseConversationAIState(context.Context, *GetBlackBoxCaseConversationAIStateReq) (*GetBlackBoxCaseConversationAIStateResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetBlackBoxCaseConversationAIState not implemented")
}
func (UnimplementedBlackBoxCaseSessionServiceServer) GetBlackBoxCaseConversationAIMessage(context.Context, *GetBlackBoxCaseConversationAIMessageReq) (*GetBlackBoxCaseConversationAIMessageResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetBlackBoxCaseConversationAIMessage not implemented")
}
func (UnimplementedBlackBoxCaseSessionServiceServer) GetBlackBoxCaseModifyTestCaseReferenceContentList(context.Context, *GetBlackBoxCaseModifyTestCaseReferenceContentListReq) (*GetBlackBoxCaseModifyTestCaseReferenceContentListResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetBlackBoxCaseModifyTestCaseReferenceContentList not implemented")
}
func (UnimplementedBlackBoxCaseSessionServiceServer) CreateBlackBoxCaseModifyTestCaseReferenceContent(context.Context, *CreateBlackBoxCaseModifyTestCaseReferenceContentReq) (*CreateBlackBoxCaseModifyTestCaseReferenceContentResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateBlackBoxCaseModifyTestCaseReferenceContent not implemented")
}
func (UnimplementedBlackBoxCaseSessionServiceServer) ReplaceBlackBoxCase(context.Context, *ReplaceBlackBoxCaseReq) (*ReplaceBlackBoxCaseResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ReplaceBlackBoxCase not implemented")
}
func (UnimplementedBlackBoxCaseSessionServiceServer) MergeBlackBoxCase(context.Context, *MergeBlackBoxCaseReq) (*MergeBlackBoxCaseResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method MergeBlackBoxCase not implemented")
}
func (UnimplementedBlackBoxCaseSessionServiceServer) TransferBlackBoxCase2XMind(context.Context, *TransferBlackBoxCase2XMindReq) (*TransferBlackBoxCase2XMindResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TransferBlackBoxCase2XMind not implemented")
}
func (UnimplementedBlackBoxCaseSessionServiceServer) GetBlackBoxCaseAIMessage(context.Context, *GetCreateBlackBoxCaseAIMessageReq) (*GetCreateBlackBoxCaseAIMessageResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetBlackBoxCaseAIMessage not implemented")
}
func (UnimplementedBlackBoxCaseSessionServiceServer) mustEmbedUnimplementedBlackBoxCaseSessionServiceServer() {
}
func (UnimplementedBlackBoxCaseSessionServiceServer) testEmbeddedByValue() {}

// UnsafeBlackBoxCaseSessionServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to BlackBoxCaseSessionServiceServer will
// result in compilation errors.
type UnsafeBlackBoxCaseSessionServiceServer interface {
	mustEmbedUnimplementedBlackBoxCaseSessionServiceServer()
}

func RegisterBlackBoxCaseSessionServiceServer(s grpc.ServiceRegistrar, srv BlackBoxCaseSessionServiceServer) {
	// If the following call pancis, it indicates UnimplementedBlackBoxCaseSessionServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&BlackBoxCaseSessionService_ServiceDesc, srv)
}

func _BlackBoxCaseSessionService_CreateBlackBoxCaseSession_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateBlackBoxCaseSessionReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BlackBoxCaseSessionServiceServer).CreateBlackBoxCaseSession(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BlackBoxCaseSessionService_CreateBlackBoxCaseSession_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BlackBoxCaseSessionServiceServer).CreateBlackBoxCaseSession(ctx, req.(*CreateBlackBoxCaseSessionReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BlackBoxCaseSessionService_UpdateBlackBoxCaseSession_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateBlackBoxCaseSessionReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BlackBoxCaseSessionServiceServer).UpdateBlackBoxCaseSession(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BlackBoxCaseSessionService_UpdateBlackBoxCaseSession_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BlackBoxCaseSessionServiceServer).UpdateBlackBoxCaseSession(ctx, req.(*UpdateBlackBoxCaseSessionReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BlackBoxCaseSessionService_DeleteBlackBoxCaseSession_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteBlackBoxCaseSessionReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BlackBoxCaseSessionServiceServer).DeleteBlackBoxCaseSession(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BlackBoxCaseSessionService_DeleteBlackBoxCaseSession_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BlackBoxCaseSessionServiceServer).DeleteBlackBoxCaseSession(ctx, req.(*DeleteBlackBoxCaseSessionReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BlackBoxCaseSessionService_GetBlackBoxCaseSession_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetBlackBoxCaseSessionReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BlackBoxCaseSessionServiceServer).GetBlackBoxCaseSession(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BlackBoxCaseSessionService_GetBlackBoxCaseSession_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BlackBoxCaseSessionServiceServer).GetBlackBoxCaseSession(ctx, req.(*GetBlackBoxCaseSessionReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BlackBoxCaseSessionService_SearchBlackBoxCaseSession_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SearchBlackBoxCaseSessionReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BlackBoxCaseSessionServiceServer).SearchBlackBoxCaseSession(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BlackBoxCaseSessionService_SearchBlackBoxCaseSession_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BlackBoxCaseSessionServiceServer).SearchBlackBoxCaseSession(ctx, req.(*SearchBlackBoxCaseSessionReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BlackBoxCaseSessionService_GetBlackBoxCaseSessionHistoryMessageList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetBlackBoxCaseSessionHistoryMessageListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BlackBoxCaseSessionServiceServer).GetBlackBoxCaseSessionHistoryMessageList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BlackBoxCaseSessionService_GetBlackBoxCaseSessionHistoryMessageList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BlackBoxCaseSessionServiceServer).GetBlackBoxCaseSessionHistoryMessageList(ctx, req.(*GetBlackBoxCaseSessionHistoryMessageListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BlackBoxCaseSessionService_RemoveBlackBoxCaseSessionHistoryMessageList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RemoveBlackBoxCaseSessionHistoryMessageListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BlackBoxCaseSessionServiceServer).RemoveBlackBoxCaseSessionHistoryMessageList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BlackBoxCaseSessionService_RemoveBlackBoxCaseSessionHistoryMessageList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BlackBoxCaseSessionServiceServer).RemoveBlackBoxCaseSessionHistoryMessageList(ctx, req.(*RemoveBlackBoxCaseSessionHistoryMessageListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BlackBoxCaseSessionService_SendBlackBoxCaseForSession_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SendBlackBoxCaseForSessionReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BlackBoxCaseSessionServiceServer).SendBlackBoxCaseForSession(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BlackBoxCaseSessionService_SendBlackBoxCaseForSession_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BlackBoxCaseSessionServiceServer).SendBlackBoxCaseForSession(ctx, req.(*SendBlackBoxCaseForSessionReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BlackBoxCaseSessionService_GetBlackBoxCaseConversationAIState_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetBlackBoxCaseConversationAIStateReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BlackBoxCaseSessionServiceServer).GetBlackBoxCaseConversationAIState(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BlackBoxCaseSessionService_GetBlackBoxCaseConversationAIState_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BlackBoxCaseSessionServiceServer).GetBlackBoxCaseConversationAIState(ctx, req.(*GetBlackBoxCaseConversationAIStateReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BlackBoxCaseSessionService_GetBlackBoxCaseConversationAIMessage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetBlackBoxCaseConversationAIMessageReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BlackBoxCaseSessionServiceServer).GetBlackBoxCaseConversationAIMessage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BlackBoxCaseSessionService_GetBlackBoxCaseConversationAIMessage_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BlackBoxCaseSessionServiceServer).GetBlackBoxCaseConversationAIMessage(ctx, req.(*GetBlackBoxCaseConversationAIMessageReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BlackBoxCaseSessionService_GetBlackBoxCaseModifyTestCaseReferenceContentList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetBlackBoxCaseModifyTestCaseReferenceContentListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BlackBoxCaseSessionServiceServer).GetBlackBoxCaseModifyTestCaseReferenceContentList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BlackBoxCaseSessionService_GetBlackBoxCaseModifyTestCaseReferenceContentList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BlackBoxCaseSessionServiceServer).GetBlackBoxCaseModifyTestCaseReferenceContentList(ctx, req.(*GetBlackBoxCaseModifyTestCaseReferenceContentListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BlackBoxCaseSessionService_CreateBlackBoxCaseModifyTestCaseReferenceContent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateBlackBoxCaseModifyTestCaseReferenceContentReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BlackBoxCaseSessionServiceServer).CreateBlackBoxCaseModifyTestCaseReferenceContent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BlackBoxCaseSessionService_CreateBlackBoxCaseModifyTestCaseReferenceContent_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BlackBoxCaseSessionServiceServer).CreateBlackBoxCaseModifyTestCaseReferenceContent(ctx, req.(*CreateBlackBoxCaseModifyTestCaseReferenceContentReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BlackBoxCaseSessionService_ReplaceBlackBoxCase_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReplaceBlackBoxCaseReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BlackBoxCaseSessionServiceServer).ReplaceBlackBoxCase(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BlackBoxCaseSessionService_ReplaceBlackBoxCase_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BlackBoxCaseSessionServiceServer).ReplaceBlackBoxCase(ctx, req.(*ReplaceBlackBoxCaseReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BlackBoxCaseSessionService_MergeBlackBoxCase_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MergeBlackBoxCaseReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BlackBoxCaseSessionServiceServer).MergeBlackBoxCase(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BlackBoxCaseSessionService_MergeBlackBoxCase_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BlackBoxCaseSessionServiceServer).MergeBlackBoxCase(ctx, req.(*MergeBlackBoxCaseReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BlackBoxCaseSessionService_TransferBlackBoxCase2XMind_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TransferBlackBoxCase2XMindReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BlackBoxCaseSessionServiceServer).TransferBlackBoxCase2XMind(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BlackBoxCaseSessionService_TransferBlackBoxCase2XMind_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BlackBoxCaseSessionServiceServer).TransferBlackBoxCase2XMind(ctx, req.(*TransferBlackBoxCase2XMindReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BlackBoxCaseSessionService_GetBlackBoxCaseAIMessage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCreateBlackBoxCaseAIMessageReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BlackBoxCaseSessionServiceServer).GetBlackBoxCaseAIMessage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BlackBoxCaseSessionService_GetBlackBoxCaseAIMessage_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BlackBoxCaseSessionServiceServer).GetBlackBoxCaseAIMessage(ctx, req.(*GetCreateBlackBoxCaseAIMessageReq))
	}
	return interceptor(ctx, in, info, handler)
}

// BlackBoxCaseSessionService_ServiceDesc is the grpc.ServiceDesc for BlackBoxCaseSessionService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var BlackBoxCaseSessionService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "ai.BlackBoxCaseSessionService",
	HandlerType: (*BlackBoxCaseSessionServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateBlackBoxCaseSession",
			Handler:    _BlackBoxCaseSessionService_CreateBlackBoxCaseSession_Handler,
		},
		{
			MethodName: "UpdateBlackBoxCaseSession",
			Handler:    _BlackBoxCaseSessionService_UpdateBlackBoxCaseSession_Handler,
		},
		{
			MethodName: "DeleteBlackBoxCaseSession",
			Handler:    _BlackBoxCaseSessionService_DeleteBlackBoxCaseSession_Handler,
		},
		{
			MethodName: "GetBlackBoxCaseSession",
			Handler:    _BlackBoxCaseSessionService_GetBlackBoxCaseSession_Handler,
		},
		{
			MethodName: "SearchBlackBoxCaseSession",
			Handler:    _BlackBoxCaseSessionService_SearchBlackBoxCaseSession_Handler,
		},
		{
			MethodName: "GetBlackBoxCaseSessionHistoryMessageList",
			Handler:    _BlackBoxCaseSessionService_GetBlackBoxCaseSessionHistoryMessageList_Handler,
		},
		{
			MethodName: "RemoveBlackBoxCaseSessionHistoryMessageList",
			Handler:    _BlackBoxCaseSessionService_RemoveBlackBoxCaseSessionHistoryMessageList_Handler,
		},
		{
			MethodName: "SendBlackBoxCaseForSession",
			Handler:    _BlackBoxCaseSessionService_SendBlackBoxCaseForSession_Handler,
		},
		{
			MethodName: "GetBlackBoxCaseConversationAIState",
			Handler:    _BlackBoxCaseSessionService_GetBlackBoxCaseConversationAIState_Handler,
		},
		{
			MethodName: "GetBlackBoxCaseConversationAIMessage",
			Handler:    _BlackBoxCaseSessionService_GetBlackBoxCaseConversationAIMessage_Handler,
		},
		{
			MethodName: "GetBlackBoxCaseModifyTestCaseReferenceContentList",
			Handler:    _BlackBoxCaseSessionService_GetBlackBoxCaseModifyTestCaseReferenceContentList_Handler,
		},
		{
			MethodName: "CreateBlackBoxCaseModifyTestCaseReferenceContent",
			Handler:    _BlackBoxCaseSessionService_CreateBlackBoxCaseModifyTestCaseReferenceContent_Handler,
		},
		{
			MethodName: "ReplaceBlackBoxCase",
			Handler:    _BlackBoxCaseSessionService_ReplaceBlackBoxCase_Handler,
		},
		{
			MethodName: "MergeBlackBoxCase",
			Handler:    _BlackBoxCaseSessionService_MergeBlackBoxCase_Handler,
		},
		{
			MethodName: "TransferBlackBoxCase2XMind",
			Handler:    _BlackBoxCaseSessionService_TransferBlackBoxCase2XMind_Handler,
		},
		{
			MethodName: "GetBlackBoxCaseAIMessage",
			Handler:    _BlackBoxCaseSessionService_GetBlackBoxCaseAIMessage_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "ai/ai.proto",
}

const (
	BlackBoxCaseDirService_CreateBlackBoxCaseDir_FullMethodName = "/ai.BlackBoxCaseDirService/CreateBlackBoxCaseDir"
	BlackBoxCaseDirService_DeleteBlackBoxCaseDir_FullMethodName = "/ai.BlackBoxCaseDirService/DeleteBlackBoxCaseDir"
	BlackBoxCaseDirService_UpdateBlackBoxCaseDir_FullMethodName = "/ai.BlackBoxCaseDirService/UpdateBlackBoxCaseDir"
	BlackBoxCaseDirService_GetBlackBoxCaseDir_FullMethodName    = "/ai.BlackBoxCaseDirService/GetBlackBoxCaseDir"
	BlackBoxCaseDirService_ListBlackBoxCaseDirs_FullMethodName  = "/ai.BlackBoxCaseDirService/ListBlackBoxCaseDirs"
)

// BlackBoxCaseDirServiceClient is the client API for BlackBoxCaseDirService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// BlackBoxCaseDirService
type BlackBoxCaseDirServiceClient interface {
	// CreateBlackBoxCaseDir create a black box case dir
	CreateBlackBoxCaseDir(ctx context.Context, in *CreateBlackBoxCaseDirReq, opts ...grpc.CallOption) (*CreateBlackBoxCaseDirResp, error)
	// DeleteBlackBoxCaseDir create a black box case dir
	DeleteBlackBoxCaseDir(ctx context.Context, in *DeleteBlackBoxCaseDirReq, opts ...grpc.CallOption) (*DeleteBlackBoxCaseDirResp, error)
	// UpdateBlackBoxCaseDir create a black box case dir
	UpdateBlackBoxCaseDir(ctx context.Context, in *UpdateBlackBoxCaseDirReq, opts ...grpc.CallOption) (*UpdateBlackBoxCaseDirResp, error)
	// GetBlackBoxCaseDir gets a black box case dir
	GetBlackBoxCaseDir(ctx context.Context, in *GetBlackBoxCaseDirReq, opts ...grpc.CallOption) (*GetBlackBoxCaseDirResp, error)
	// ListBlackBoxCaseDirs list black box case dirs
	ListBlackBoxCaseDirs(ctx context.Context, in *ListBlackBoxCaseDirsReq, opts ...grpc.CallOption) (*ListBlackBoxCaseDirsResp, error)
}

type blackBoxCaseDirServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewBlackBoxCaseDirServiceClient(cc grpc.ClientConnInterface) BlackBoxCaseDirServiceClient {
	return &blackBoxCaseDirServiceClient{cc}
}

func (c *blackBoxCaseDirServiceClient) CreateBlackBoxCaseDir(ctx context.Context, in *CreateBlackBoxCaseDirReq, opts ...grpc.CallOption) (*CreateBlackBoxCaseDirResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateBlackBoxCaseDirResp)
	err := c.cc.Invoke(ctx, BlackBoxCaseDirService_CreateBlackBoxCaseDir_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *blackBoxCaseDirServiceClient) DeleteBlackBoxCaseDir(ctx context.Context, in *DeleteBlackBoxCaseDirReq, opts ...grpc.CallOption) (*DeleteBlackBoxCaseDirResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DeleteBlackBoxCaseDirResp)
	err := c.cc.Invoke(ctx, BlackBoxCaseDirService_DeleteBlackBoxCaseDir_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *blackBoxCaseDirServiceClient) UpdateBlackBoxCaseDir(ctx context.Context, in *UpdateBlackBoxCaseDirReq, opts ...grpc.CallOption) (*UpdateBlackBoxCaseDirResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UpdateBlackBoxCaseDirResp)
	err := c.cc.Invoke(ctx, BlackBoxCaseDirService_UpdateBlackBoxCaseDir_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *blackBoxCaseDirServiceClient) GetBlackBoxCaseDir(ctx context.Context, in *GetBlackBoxCaseDirReq, opts ...grpc.CallOption) (*GetBlackBoxCaseDirResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetBlackBoxCaseDirResp)
	err := c.cc.Invoke(ctx, BlackBoxCaseDirService_GetBlackBoxCaseDir_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *blackBoxCaseDirServiceClient) ListBlackBoxCaseDirs(ctx context.Context, in *ListBlackBoxCaseDirsReq, opts ...grpc.CallOption) (*ListBlackBoxCaseDirsResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListBlackBoxCaseDirsResp)
	err := c.cc.Invoke(ctx, BlackBoxCaseDirService_ListBlackBoxCaseDirs_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// BlackBoxCaseDirServiceServer is the server API for BlackBoxCaseDirService service.
// All implementations must embed UnimplementedBlackBoxCaseDirServiceServer
// for forward compatibility.
//
// BlackBoxCaseDirService
type BlackBoxCaseDirServiceServer interface {
	// CreateBlackBoxCaseDir create a black box case dir
	CreateBlackBoxCaseDir(context.Context, *CreateBlackBoxCaseDirReq) (*CreateBlackBoxCaseDirResp, error)
	// DeleteBlackBoxCaseDir create a black box case dir
	DeleteBlackBoxCaseDir(context.Context, *DeleteBlackBoxCaseDirReq) (*DeleteBlackBoxCaseDirResp, error)
	// UpdateBlackBoxCaseDir create a black box case dir
	UpdateBlackBoxCaseDir(context.Context, *UpdateBlackBoxCaseDirReq) (*UpdateBlackBoxCaseDirResp, error)
	// GetBlackBoxCaseDir gets a black box case dir
	GetBlackBoxCaseDir(context.Context, *GetBlackBoxCaseDirReq) (*GetBlackBoxCaseDirResp, error)
	// ListBlackBoxCaseDirs list black box case dirs
	ListBlackBoxCaseDirs(context.Context, *ListBlackBoxCaseDirsReq) (*ListBlackBoxCaseDirsResp, error)
	mustEmbedUnimplementedBlackBoxCaseDirServiceServer()
}

// UnimplementedBlackBoxCaseDirServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedBlackBoxCaseDirServiceServer struct{}

func (UnimplementedBlackBoxCaseDirServiceServer) CreateBlackBoxCaseDir(context.Context, *CreateBlackBoxCaseDirReq) (*CreateBlackBoxCaseDirResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateBlackBoxCaseDir not implemented")
}
func (UnimplementedBlackBoxCaseDirServiceServer) DeleteBlackBoxCaseDir(context.Context, *DeleteBlackBoxCaseDirReq) (*DeleteBlackBoxCaseDirResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteBlackBoxCaseDir not implemented")
}
func (UnimplementedBlackBoxCaseDirServiceServer) UpdateBlackBoxCaseDir(context.Context, *UpdateBlackBoxCaseDirReq) (*UpdateBlackBoxCaseDirResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateBlackBoxCaseDir not implemented")
}
func (UnimplementedBlackBoxCaseDirServiceServer) GetBlackBoxCaseDir(context.Context, *GetBlackBoxCaseDirReq) (*GetBlackBoxCaseDirResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetBlackBoxCaseDir not implemented")
}
func (UnimplementedBlackBoxCaseDirServiceServer) ListBlackBoxCaseDirs(context.Context, *ListBlackBoxCaseDirsReq) (*ListBlackBoxCaseDirsResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListBlackBoxCaseDirs not implemented")
}
func (UnimplementedBlackBoxCaseDirServiceServer) mustEmbedUnimplementedBlackBoxCaseDirServiceServer() {
}
func (UnimplementedBlackBoxCaseDirServiceServer) testEmbeddedByValue() {}

// UnsafeBlackBoxCaseDirServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to BlackBoxCaseDirServiceServer will
// result in compilation errors.
type UnsafeBlackBoxCaseDirServiceServer interface {
	mustEmbedUnimplementedBlackBoxCaseDirServiceServer()
}

func RegisterBlackBoxCaseDirServiceServer(s grpc.ServiceRegistrar, srv BlackBoxCaseDirServiceServer) {
	// If the following call pancis, it indicates UnimplementedBlackBoxCaseDirServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&BlackBoxCaseDirService_ServiceDesc, srv)
}

func _BlackBoxCaseDirService_CreateBlackBoxCaseDir_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateBlackBoxCaseDirReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BlackBoxCaseDirServiceServer).CreateBlackBoxCaseDir(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BlackBoxCaseDirService_CreateBlackBoxCaseDir_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BlackBoxCaseDirServiceServer).CreateBlackBoxCaseDir(ctx, req.(*CreateBlackBoxCaseDirReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BlackBoxCaseDirService_DeleteBlackBoxCaseDir_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteBlackBoxCaseDirReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BlackBoxCaseDirServiceServer).DeleteBlackBoxCaseDir(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BlackBoxCaseDirService_DeleteBlackBoxCaseDir_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BlackBoxCaseDirServiceServer).DeleteBlackBoxCaseDir(ctx, req.(*DeleteBlackBoxCaseDirReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BlackBoxCaseDirService_UpdateBlackBoxCaseDir_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateBlackBoxCaseDirReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BlackBoxCaseDirServiceServer).UpdateBlackBoxCaseDir(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BlackBoxCaseDirService_UpdateBlackBoxCaseDir_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BlackBoxCaseDirServiceServer).UpdateBlackBoxCaseDir(ctx, req.(*UpdateBlackBoxCaseDirReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BlackBoxCaseDirService_GetBlackBoxCaseDir_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetBlackBoxCaseDirReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BlackBoxCaseDirServiceServer).GetBlackBoxCaseDir(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BlackBoxCaseDirService_GetBlackBoxCaseDir_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BlackBoxCaseDirServiceServer).GetBlackBoxCaseDir(ctx, req.(*GetBlackBoxCaseDirReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BlackBoxCaseDirService_ListBlackBoxCaseDirs_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListBlackBoxCaseDirsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BlackBoxCaseDirServiceServer).ListBlackBoxCaseDirs(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BlackBoxCaseDirService_ListBlackBoxCaseDirs_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BlackBoxCaseDirServiceServer).ListBlackBoxCaseDirs(ctx, req.(*ListBlackBoxCaseDirsReq))
	}
	return interceptor(ctx, in, info, handler)
}

// BlackBoxCaseDirService_ServiceDesc is the grpc.ServiceDesc for BlackBoxCaseDirService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var BlackBoxCaseDirService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "ai.BlackBoxCaseDirService",
	HandlerType: (*BlackBoxCaseDirServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateBlackBoxCaseDir",
			Handler:    _BlackBoxCaseDirService_CreateBlackBoxCaseDir_Handler,
		},
		{
			MethodName: "DeleteBlackBoxCaseDir",
			Handler:    _BlackBoxCaseDirService_DeleteBlackBoxCaseDir_Handler,
		},
		{
			MethodName: "UpdateBlackBoxCaseDir",
			Handler:    _BlackBoxCaseDirService_UpdateBlackBoxCaseDir_Handler,
		},
		{
			MethodName: "GetBlackBoxCaseDir",
			Handler:    _BlackBoxCaseDirService_GetBlackBoxCaseDir_Handler,
		},
		{
			MethodName: "ListBlackBoxCaseDirs",
			Handler:    _BlackBoxCaseDirService_ListBlackBoxCaseDirs_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "ai/ai.proto",
}

const (
	BlackBoxCaseService_CreateBlackBoxCase_FullMethodName    = "/ai.BlackBoxCaseService/CreateBlackBoxCase"
	BlackBoxCaseService_DeleteBlackBoxCase_FullMethodName    = "/ai.BlackBoxCaseService/DeleteBlackBoxCase"
	BlackBoxCaseService_UpdateBlackBoxCase_FullMethodName    = "/ai.BlackBoxCaseService/UpdateBlackBoxCase"
	BlackBoxCaseService_UpdateBlackBoxCaseAI_FullMethodName  = "/ai.BlackBoxCaseService/UpdateBlackBoxCaseAI"
	BlackBoxCaseService_GetBlackBoxCase_FullMethodName       = "/ai.BlackBoxCaseService/GetBlackBoxCase"
	BlackBoxCaseService_ListBlackBoxCase_FullMethodName      = "/ai.BlackBoxCaseService/ListBlackBoxCase"
	BlackBoxCaseService_MergeBlackBoxCaseData_FullMethodName = "/ai.BlackBoxCaseService/MergeBlackBoxCaseData"
	BlackBoxCaseService_QueryIncBlackBoxCase_FullMethodName  = "/ai.BlackBoxCaseService/QueryIncBlackBoxCase"
)

// BlackBoxCaseServiceClient is the client API for BlackBoxCaseService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// BlackBoxCaseService
type BlackBoxCaseServiceClient interface {
	// CreateBlackBoxCase create a black box case
	CreateBlackBoxCase(ctx context.Context, in *CreateBlackBoxCaseReq, opts ...grpc.CallOption) (*CreateBlackBoxCaseResp, error)
	// DeleteBlackBoxCase create a black box case
	DeleteBlackBoxCase(ctx context.Context, in *DeleteBlackBoxCaseReq, opts ...grpc.CallOption) (*DeleteBlackBoxCaseResp, error)
	// UpdateBlackBoxCase create a black box case
	UpdateBlackBoxCase(ctx context.Context, in *UpdateBlackBoxCaseReq, opts ...grpc.CallOption) (*UpdateBlackBoxCaseResp, error)
	// UpdateBlackBoxCase create a black box case
	UpdateBlackBoxCaseAI(ctx context.Context, in *UpdateBlackBoxCaseAIReq, opts ...grpc.CallOption) (*UpdateBlackBoxCaseAIResp, error)
	// GetBlackBoxCase gets a black box case
	GetBlackBoxCase(ctx context.Context, in *GetBlackBoxCaseReq, opts ...grpc.CallOption) (*GetBlackBoxCaseResp, error)
	// ListBlackBoxCase list black box case
	ListBlackBoxCase(ctx context.Context, in *ListBlackBoxCaseReq, opts ...grpc.CallOption) (*ListBlackBoxCaseResp, error)
	// MergeBlackBoxCaseData list black box case
	MergeBlackBoxCaseData(ctx context.Context, in *MergeBlackBoxCaseDataReq, opts ...grpc.CallOption) (*MergeBlackBoxCaseDataResp, error)
	// QueryIncBlackBoxCase list black box case
	QueryIncBlackBoxCase(ctx context.Context, in *QueryIncBlackBoxCaseReq, opts ...grpc.CallOption) (*QueryIncBlackBoxCaseResp, error)
}

type blackBoxCaseServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewBlackBoxCaseServiceClient(cc grpc.ClientConnInterface) BlackBoxCaseServiceClient {
	return &blackBoxCaseServiceClient{cc}
}

func (c *blackBoxCaseServiceClient) CreateBlackBoxCase(ctx context.Context, in *CreateBlackBoxCaseReq, opts ...grpc.CallOption) (*CreateBlackBoxCaseResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateBlackBoxCaseResp)
	err := c.cc.Invoke(ctx, BlackBoxCaseService_CreateBlackBoxCase_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *blackBoxCaseServiceClient) DeleteBlackBoxCase(ctx context.Context, in *DeleteBlackBoxCaseReq, opts ...grpc.CallOption) (*DeleteBlackBoxCaseResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DeleteBlackBoxCaseResp)
	err := c.cc.Invoke(ctx, BlackBoxCaseService_DeleteBlackBoxCase_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *blackBoxCaseServiceClient) UpdateBlackBoxCase(ctx context.Context, in *UpdateBlackBoxCaseReq, opts ...grpc.CallOption) (*UpdateBlackBoxCaseResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UpdateBlackBoxCaseResp)
	err := c.cc.Invoke(ctx, BlackBoxCaseService_UpdateBlackBoxCase_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *blackBoxCaseServiceClient) UpdateBlackBoxCaseAI(ctx context.Context, in *UpdateBlackBoxCaseAIReq, opts ...grpc.CallOption) (*UpdateBlackBoxCaseAIResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UpdateBlackBoxCaseAIResp)
	err := c.cc.Invoke(ctx, BlackBoxCaseService_UpdateBlackBoxCaseAI_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *blackBoxCaseServiceClient) GetBlackBoxCase(ctx context.Context, in *GetBlackBoxCaseReq, opts ...grpc.CallOption) (*GetBlackBoxCaseResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetBlackBoxCaseResp)
	err := c.cc.Invoke(ctx, BlackBoxCaseService_GetBlackBoxCase_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *blackBoxCaseServiceClient) ListBlackBoxCase(ctx context.Context, in *ListBlackBoxCaseReq, opts ...grpc.CallOption) (*ListBlackBoxCaseResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListBlackBoxCaseResp)
	err := c.cc.Invoke(ctx, BlackBoxCaseService_ListBlackBoxCase_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *blackBoxCaseServiceClient) MergeBlackBoxCaseData(ctx context.Context, in *MergeBlackBoxCaseDataReq, opts ...grpc.CallOption) (*MergeBlackBoxCaseDataResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(MergeBlackBoxCaseDataResp)
	err := c.cc.Invoke(ctx, BlackBoxCaseService_MergeBlackBoxCaseData_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *blackBoxCaseServiceClient) QueryIncBlackBoxCase(ctx context.Context, in *QueryIncBlackBoxCaseReq, opts ...grpc.CallOption) (*QueryIncBlackBoxCaseResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(QueryIncBlackBoxCaseResp)
	err := c.cc.Invoke(ctx, BlackBoxCaseService_QueryIncBlackBoxCase_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// BlackBoxCaseServiceServer is the server API for BlackBoxCaseService service.
// All implementations must embed UnimplementedBlackBoxCaseServiceServer
// for forward compatibility.
//
// BlackBoxCaseService
type BlackBoxCaseServiceServer interface {
	// CreateBlackBoxCase create a black box case
	CreateBlackBoxCase(context.Context, *CreateBlackBoxCaseReq) (*CreateBlackBoxCaseResp, error)
	// DeleteBlackBoxCase create a black box case
	DeleteBlackBoxCase(context.Context, *DeleteBlackBoxCaseReq) (*DeleteBlackBoxCaseResp, error)
	// UpdateBlackBoxCase create a black box case
	UpdateBlackBoxCase(context.Context, *UpdateBlackBoxCaseReq) (*UpdateBlackBoxCaseResp, error)
	// UpdateBlackBoxCase create a black box case
	UpdateBlackBoxCaseAI(context.Context, *UpdateBlackBoxCaseAIReq) (*UpdateBlackBoxCaseAIResp, error)
	// GetBlackBoxCase gets a black box case
	GetBlackBoxCase(context.Context, *GetBlackBoxCaseReq) (*GetBlackBoxCaseResp, error)
	// ListBlackBoxCase list black box case
	ListBlackBoxCase(context.Context, *ListBlackBoxCaseReq) (*ListBlackBoxCaseResp, error)
	// MergeBlackBoxCaseData list black box case
	MergeBlackBoxCaseData(context.Context, *MergeBlackBoxCaseDataReq) (*MergeBlackBoxCaseDataResp, error)
	// QueryIncBlackBoxCase list black box case
	QueryIncBlackBoxCase(context.Context, *QueryIncBlackBoxCaseReq) (*QueryIncBlackBoxCaseResp, error)
	mustEmbedUnimplementedBlackBoxCaseServiceServer()
}

// UnimplementedBlackBoxCaseServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedBlackBoxCaseServiceServer struct{}

func (UnimplementedBlackBoxCaseServiceServer) CreateBlackBoxCase(context.Context, *CreateBlackBoxCaseReq) (*CreateBlackBoxCaseResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateBlackBoxCase not implemented")
}
func (UnimplementedBlackBoxCaseServiceServer) DeleteBlackBoxCase(context.Context, *DeleteBlackBoxCaseReq) (*DeleteBlackBoxCaseResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteBlackBoxCase not implemented")
}
func (UnimplementedBlackBoxCaseServiceServer) UpdateBlackBoxCase(context.Context, *UpdateBlackBoxCaseReq) (*UpdateBlackBoxCaseResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateBlackBoxCase not implemented")
}
func (UnimplementedBlackBoxCaseServiceServer) UpdateBlackBoxCaseAI(context.Context, *UpdateBlackBoxCaseAIReq) (*UpdateBlackBoxCaseAIResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateBlackBoxCaseAI not implemented")
}
func (UnimplementedBlackBoxCaseServiceServer) GetBlackBoxCase(context.Context, *GetBlackBoxCaseReq) (*GetBlackBoxCaseResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetBlackBoxCase not implemented")
}
func (UnimplementedBlackBoxCaseServiceServer) ListBlackBoxCase(context.Context, *ListBlackBoxCaseReq) (*ListBlackBoxCaseResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListBlackBoxCase not implemented")
}
func (UnimplementedBlackBoxCaseServiceServer) MergeBlackBoxCaseData(context.Context, *MergeBlackBoxCaseDataReq) (*MergeBlackBoxCaseDataResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method MergeBlackBoxCaseData not implemented")
}
func (UnimplementedBlackBoxCaseServiceServer) QueryIncBlackBoxCase(context.Context, *QueryIncBlackBoxCaseReq) (*QueryIncBlackBoxCaseResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QueryIncBlackBoxCase not implemented")
}
func (UnimplementedBlackBoxCaseServiceServer) mustEmbedUnimplementedBlackBoxCaseServiceServer() {}
func (UnimplementedBlackBoxCaseServiceServer) testEmbeddedByValue()                             {}

// UnsafeBlackBoxCaseServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to BlackBoxCaseServiceServer will
// result in compilation errors.
type UnsafeBlackBoxCaseServiceServer interface {
	mustEmbedUnimplementedBlackBoxCaseServiceServer()
}

func RegisterBlackBoxCaseServiceServer(s grpc.ServiceRegistrar, srv BlackBoxCaseServiceServer) {
	// If the following call pancis, it indicates UnimplementedBlackBoxCaseServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&BlackBoxCaseService_ServiceDesc, srv)
}

func _BlackBoxCaseService_CreateBlackBoxCase_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateBlackBoxCaseReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BlackBoxCaseServiceServer).CreateBlackBoxCase(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BlackBoxCaseService_CreateBlackBoxCase_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BlackBoxCaseServiceServer).CreateBlackBoxCase(ctx, req.(*CreateBlackBoxCaseReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BlackBoxCaseService_DeleteBlackBoxCase_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteBlackBoxCaseReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BlackBoxCaseServiceServer).DeleteBlackBoxCase(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BlackBoxCaseService_DeleteBlackBoxCase_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BlackBoxCaseServiceServer).DeleteBlackBoxCase(ctx, req.(*DeleteBlackBoxCaseReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BlackBoxCaseService_UpdateBlackBoxCase_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateBlackBoxCaseReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BlackBoxCaseServiceServer).UpdateBlackBoxCase(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BlackBoxCaseService_UpdateBlackBoxCase_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BlackBoxCaseServiceServer).UpdateBlackBoxCase(ctx, req.(*UpdateBlackBoxCaseReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BlackBoxCaseService_UpdateBlackBoxCaseAI_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateBlackBoxCaseAIReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BlackBoxCaseServiceServer).UpdateBlackBoxCaseAI(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BlackBoxCaseService_UpdateBlackBoxCaseAI_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BlackBoxCaseServiceServer).UpdateBlackBoxCaseAI(ctx, req.(*UpdateBlackBoxCaseAIReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BlackBoxCaseService_GetBlackBoxCase_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetBlackBoxCaseReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BlackBoxCaseServiceServer).GetBlackBoxCase(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BlackBoxCaseService_GetBlackBoxCase_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BlackBoxCaseServiceServer).GetBlackBoxCase(ctx, req.(*GetBlackBoxCaseReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BlackBoxCaseService_ListBlackBoxCase_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListBlackBoxCaseReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BlackBoxCaseServiceServer).ListBlackBoxCase(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BlackBoxCaseService_ListBlackBoxCase_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BlackBoxCaseServiceServer).ListBlackBoxCase(ctx, req.(*ListBlackBoxCaseReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BlackBoxCaseService_MergeBlackBoxCaseData_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MergeBlackBoxCaseDataReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BlackBoxCaseServiceServer).MergeBlackBoxCaseData(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BlackBoxCaseService_MergeBlackBoxCaseData_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BlackBoxCaseServiceServer).MergeBlackBoxCaseData(ctx, req.(*MergeBlackBoxCaseDataReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BlackBoxCaseService_QueryIncBlackBoxCase_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QueryIncBlackBoxCaseReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BlackBoxCaseServiceServer).QueryIncBlackBoxCase(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BlackBoxCaseService_QueryIncBlackBoxCase_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BlackBoxCaseServiceServer).QueryIncBlackBoxCase(ctx, req.(*QueryIncBlackBoxCaseReq))
	}
	return interceptor(ctx, in, info, handler)
}

// BlackBoxCaseService_ServiceDesc is the grpc.ServiceDesc for BlackBoxCaseService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var BlackBoxCaseService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "ai.BlackBoxCaseService",
	HandlerType: (*BlackBoxCaseServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateBlackBoxCase",
			Handler:    _BlackBoxCaseService_CreateBlackBoxCase_Handler,
		},
		{
			MethodName: "DeleteBlackBoxCase",
			Handler:    _BlackBoxCaseService_DeleteBlackBoxCase_Handler,
		},
		{
			MethodName: "UpdateBlackBoxCase",
			Handler:    _BlackBoxCaseService_UpdateBlackBoxCase_Handler,
		},
		{
			MethodName: "UpdateBlackBoxCaseAI",
			Handler:    _BlackBoxCaseService_UpdateBlackBoxCaseAI_Handler,
		},
		{
			MethodName: "GetBlackBoxCase",
			Handler:    _BlackBoxCaseService_GetBlackBoxCase_Handler,
		},
		{
			MethodName: "ListBlackBoxCase",
			Handler:    _BlackBoxCaseService_ListBlackBoxCase_Handler,
		},
		{
			MethodName: "MergeBlackBoxCaseData",
			Handler:    _BlackBoxCaseService_MergeBlackBoxCaseData_Handler,
		},
		{
			MethodName: "QueryIncBlackBoxCase",
			Handler:    _BlackBoxCaseService_QueryIncBlackBoxCase_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "ai/ai.proto",
}

const (
	BlackBoxCaseKnowledgeService_CreateBlackBoxCaseKnowledge_FullMethodName = "/ai.BlackBoxCaseKnowledgeService/CreateBlackBoxCaseKnowledge"
	BlackBoxCaseKnowledgeService_DeleteBlackBoxCaseKnowledge_FullMethodName = "/ai.BlackBoxCaseKnowledgeService/DeleteBlackBoxCaseKnowledge"
	BlackBoxCaseKnowledgeService_UpdateBlackBoxCaseKnowledge_FullMethodName = "/ai.BlackBoxCaseKnowledgeService/UpdateBlackBoxCaseKnowledge"
	BlackBoxCaseKnowledgeService_GetBlackBoxCaseKnowledge_FullMethodName    = "/ai.BlackBoxCaseKnowledgeService/GetBlackBoxCaseKnowledge"
	BlackBoxCaseKnowledgeService_ListBlackBoxCaseKnowledge_FullMethodName   = "/ai.BlackBoxCaseKnowledgeService/ListBlackBoxCaseKnowledge"
	BlackBoxCaseKnowledgeService_ReloadBlackBoxCaseKnowledge_FullMethodName = "/ai.BlackBoxCaseKnowledgeService/ReloadBlackBoxCaseKnowledge"
)

// BlackBoxCaseKnowledgeServiceClient is the client API for BlackBoxCaseKnowledgeService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// BlackBoxCaseKnowledgeService
type BlackBoxCaseKnowledgeServiceClient interface {
	// CreateBlackBoxCaseKnowledge create a black box case
	CreateBlackBoxCaseKnowledge(ctx context.Context, in *CreateBlackBoxCaseKnowledgeReq, opts ...grpc.CallOption) (*CreateBlackBoxCaseKnowledgeResp, error)
	// DeleteBlackBoxCaseKnowledge create a black box case
	DeleteBlackBoxCaseKnowledge(ctx context.Context, in *DeleteBlackBoxCaseKnowledgeReq, opts ...grpc.CallOption) (*DeleteBlackBoxCaseKnowledgeResp, error)
	// UpdateBlackBoxCaseKnowledge create a black box case
	UpdateBlackBoxCaseKnowledge(ctx context.Context, in *UpdateBlackBoxCaseKnowledgeReq, opts ...grpc.CallOption) (*UpdateBlackBoxCaseKnowledgeResp, error)
	// GetBlackBoxCaseKnowledge gets a black box case
	GetBlackBoxCaseKnowledge(ctx context.Context, in *GetBlackBoxCaseKnowledgeReq, opts ...grpc.CallOption) (*GetBlackBoxCaseKnowledgeResp, error)
	// ListBlackBoxCaseKnowledge list black box case
	ListBlackBoxCaseKnowledge(ctx context.Context, in *ListBlackBoxCaseKnowledgeReq, opts ...grpc.CallOption) (*ListBlackBoxCaseKnowledgeResp, error)
	// ReloadBlackBoxCaseKnowledge list black box case
	ReloadBlackBoxCaseKnowledge(ctx context.Context, in *ReloadBlackBoxCaseKnowledgeReq, opts ...grpc.CallOption) (*ReloadBlackBoxCaseKnowledgeResp, error)
}

type blackBoxCaseKnowledgeServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewBlackBoxCaseKnowledgeServiceClient(cc grpc.ClientConnInterface) BlackBoxCaseKnowledgeServiceClient {
	return &blackBoxCaseKnowledgeServiceClient{cc}
}

func (c *blackBoxCaseKnowledgeServiceClient) CreateBlackBoxCaseKnowledge(ctx context.Context, in *CreateBlackBoxCaseKnowledgeReq, opts ...grpc.CallOption) (*CreateBlackBoxCaseKnowledgeResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateBlackBoxCaseKnowledgeResp)
	err := c.cc.Invoke(ctx, BlackBoxCaseKnowledgeService_CreateBlackBoxCaseKnowledge_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *blackBoxCaseKnowledgeServiceClient) DeleteBlackBoxCaseKnowledge(ctx context.Context, in *DeleteBlackBoxCaseKnowledgeReq, opts ...grpc.CallOption) (*DeleteBlackBoxCaseKnowledgeResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DeleteBlackBoxCaseKnowledgeResp)
	err := c.cc.Invoke(ctx, BlackBoxCaseKnowledgeService_DeleteBlackBoxCaseKnowledge_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *blackBoxCaseKnowledgeServiceClient) UpdateBlackBoxCaseKnowledge(ctx context.Context, in *UpdateBlackBoxCaseKnowledgeReq, opts ...grpc.CallOption) (*UpdateBlackBoxCaseKnowledgeResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UpdateBlackBoxCaseKnowledgeResp)
	err := c.cc.Invoke(ctx, BlackBoxCaseKnowledgeService_UpdateBlackBoxCaseKnowledge_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *blackBoxCaseKnowledgeServiceClient) GetBlackBoxCaseKnowledge(ctx context.Context, in *GetBlackBoxCaseKnowledgeReq, opts ...grpc.CallOption) (*GetBlackBoxCaseKnowledgeResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetBlackBoxCaseKnowledgeResp)
	err := c.cc.Invoke(ctx, BlackBoxCaseKnowledgeService_GetBlackBoxCaseKnowledge_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *blackBoxCaseKnowledgeServiceClient) ListBlackBoxCaseKnowledge(ctx context.Context, in *ListBlackBoxCaseKnowledgeReq, opts ...grpc.CallOption) (*ListBlackBoxCaseKnowledgeResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListBlackBoxCaseKnowledgeResp)
	err := c.cc.Invoke(ctx, BlackBoxCaseKnowledgeService_ListBlackBoxCaseKnowledge_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *blackBoxCaseKnowledgeServiceClient) ReloadBlackBoxCaseKnowledge(ctx context.Context, in *ReloadBlackBoxCaseKnowledgeReq, opts ...grpc.CallOption) (*ReloadBlackBoxCaseKnowledgeResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ReloadBlackBoxCaseKnowledgeResp)
	err := c.cc.Invoke(ctx, BlackBoxCaseKnowledgeService_ReloadBlackBoxCaseKnowledge_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// BlackBoxCaseKnowledgeServiceServer is the server API for BlackBoxCaseKnowledgeService service.
// All implementations must embed UnimplementedBlackBoxCaseKnowledgeServiceServer
// for forward compatibility.
//
// BlackBoxCaseKnowledgeService
type BlackBoxCaseKnowledgeServiceServer interface {
	// CreateBlackBoxCaseKnowledge create a black box case
	CreateBlackBoxCaseKnowledge(context.Context, *CreateBlackBoxCaseKnowledgeReq) (*CreateBlackBoxCaseKnowledgeResp, error)
	// DeleteBlackBoxCaseKnowledge create a black box case
	DeleteBlackBoxCaseKnowledge(context.Context, *DeleteBlackBoxCaseKnowledgeReq) (*DeleteBlackBoxCaseKnowledgeResp, error)
	// UpdateBlackBoxCaseKnowledge create a black box case
	UpdateBlackBoxCaseKnowledge(context.Context, *UpdateBlackBoxCaseKnowledgeReq) (*UpdateBlackBoxCaseKnowledgeResp, error)
	// GetBlackBoxCaseKnowledge gets a black box case
	GetBlackBoxCaseKnowledge(context.Context, *GetBlackBoxCaseKnowledgeReq) (*GetBlackBoxCaseKnowledgeResp, error)
	// ListBlackBoxCaseKnowledge list black box case
	ListBlackBoxCaseKnowledge(context.Context, *ListBlackBoxCaseKnowledgeReq) (*ListBlackBoxCaseKnowledgeResp, error)
	// ReloadBlackBoxCaseKnowledge list black box case
	ReloadBlackBoxCaseKnowledge(context.Context, *ReloadBlackBoxCaseKnowledgeReq) (*ReloadBlackBoxCaseKnowledgeResp, error)
	mustEmbedUnimplementedBlackBoxCaseKnowledgeServiceServer()
}

// UnimplementedBlackBoxCaseKnowledgeServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedBlackBoxCaseKnowledgeServiceServer struct{}

func (UnimplementedBlackBoxCaseKnowledgeServiceServer) CreateBlackBoxCaseKnowledge(context.Context, *CreateBlackBoxCaseKnowledgeReq) (*CreateBlackBoxCaseKnowledgeResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateBlackBoxCaseKnowledge not implemented")
}
func (UnimplementedBlackBoxCaseKnowledgeServiceServer) DeleteBlackBoxCaseKnowledge(context.Context, *DeleteBlackBoxCaseKnowledgeReq) (*DeleteBlackBoxCaseKnowledgeResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteBlackBoxCaseKnowledge not implemented")
}
func (UnimplementedBlackBoxCaseKnowledgeServiceServer) UpdateBlackBoxCaseKnowledge(context.Context, *UpdateBlackBoxCaseKnowledgeReq) (*UpdateBlackBoxCaseKnowledgeResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateBlackBoxCaseKnowledge not implemented")
}
func (UnimplementedBlackBoxCaseKnowledgeServiceServer) GetBlackBoxCaseKnowledge(context.Context, *GetBlackBoxCaseKnowledgeReq) (*GetBlackBoxCaseKnowledgeResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetBlackBoxCaseKnowledge not implemented")
}
func (UnimplementedBlackBoxCaseKnowledgeServiceServer) ListBlackBoxCaseKnowledge(context.Context, *ListBlackBoxCaseKnowledgeReq) (*ListBlackBoxCaseKnowledgeResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListBlackBoxCaseKnowledge not implemented")
}
func (UnimplementedBlackBoxCaseKnowledgeServiceServer) ReloadBlackBoxCaseKnowledge(context.Context, *ReloadBlackBoxCaseKnowledgeReq) (*ReloadBlackBoxCaseKnowledgeResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ReloadBlackBoxCaseKnowledge not implemented")
}
func (UnimplementedBlackBoxCaseKnowledgeServiceServer) mustEmbedUnimplementedBlackBoxCaseKnowledgeServiceServer() {
}
func (UnimplementedBlackBoxCaseKnowledgeServiceServer) testEmbeddedByValue() {}

// UnsafeBlackBoxCaseKnowledgeServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to BlackBoxCaseKnowledgeServiceServer will
// result in compilation errors.
type UnsafeBlackBoxCaseKnowledgeServiceServer interface {
	mustEmbedUnimplementedBlackBoxCaseKnowledgeServiceServer()
}

func RegisterBlackBoxCaseKnowledgeServiceServer(s grpc.ServiceRegistrar, srv BlackBoxCaseKnowledgeServiceServer) {
	// If the following call pancis, it indicates UnimplementedBlackBoxCaseKnowledgeServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&BlackBoxCaseKnowledgeService_ServiceDesc, srv)
}

func _BlackBoxCaseKnowledgeService_CreateBlackBoxCaseKnowledge_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateBlackBoxCaseKnowledgeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BlackBoxCaseKnowledgeServiceServer).CreateBlackBoxCaseKnowledge(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BlackBoxCaseKnowledgeService_CreateBlackBoxCaseKnowledge_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BlackBoxCaseKnowledgeServiceServer).CreateBlackBoxCaseKnowledge(ctx, req.(*CreateBlackBoxCaseKnowledgeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BlackBoxCaseKnowledgeService_DeleteBlackBoxCaseKnowledge_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteBlackBoxCaseKnowledgeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BlackBoxCaseKnowledgeServiceServer).DeleteBlackBoxCaseKnowledge(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BlackBoxCaseKnowledgeService_DeleteBlackBoxCaseKnowledge_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BlackBoxCaseKnowledgeServiceServer).DeleteBlackBoxCaseKnowledge(ctx, req.(*DeleteBlackBoxCaseKnowledgeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BlackBoxCaseKnowledgeService_UpdateBlackBoxCaseKnowledge_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateBlackBoxCaseKnowledgeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BlackBoxCaseKnowledgeServiceServer).UpdateBlackBoxCaseKnowledge(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BlackBoxCaseKnowledgeService_UpdateBlackBoxCaseKnowledge_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BlackBoxCaseKnowledgeServiceServer).UpdateBlackBoxCaseKnowledge(ctx, req.(*UpdateBlackBoxCaseKnowledgeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BlackBoxCaseKnowledgeService_GetBlackBoxCaseKnowledge_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetBlackBoxCaseKnowledgeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BlackBoxCaseKnowledgeServiceServer).GetBlackBoxCaseKnowledge(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BlackBoxCaseKnowledgeService_GetBlackBoxCaseKnowledge_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BlackBoxCaseKnowledgeServiceServer).GetBlackBoxCaseKnowledge(ctx, req.(*GetBlackBoxCaseKnowledgeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BlackBoxCaseKnowledgeService_ListBlackBoxCaseKnowledge_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListBlackBoxCaseKnowledgeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BlackBoxCaseKnowledgeServiceServer).ListBlackBoxCaseKnowledge(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BlackBoxCaseKnowledgeService_ListBlackBoxCaseKnowledge_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BlackBoxCaseKnowledgeServiceServer).ListBlackBoxCaseKnowledge(ctx, req.(*ListBlackBoxCaseKnowledgeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BlackBoxCaseKnowledgeService_ReloadBlackBoxCaseKnowledge_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReloadBlackBoxCaseKnowledgeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BlackBoxCaseKnowledgeServiceServer).ReloadBlackBoxCaseKnowledge(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BlackBoxCaseKnowledgeService_ReloadBlackBoxCaseKnowledge_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BlackBoxCaseKnowledgeServiceServer).ReloadBlackBoxCaseKnowledge(ctx, req.(*ReloadBlackBoxCaseKnowledgeReq))
	}
	return interceptor(ctx, in, info, handler)
}

// BlackBoxCaseKnowledgeService_ServiceDesc is the grpc.ServiceDesc for BlackBoxCaseKnowledgeService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var BlackBoxCaseKnowledgeService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "ai.BlackBoxCaseKnowledgeService",
	HandlerType: (*BlackBoxCaseKnowledgeServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateBlackBoxCaseKnowledge",
			Handler:    _BlackBoxCaseKnowledgeService_CreateBlackBoxCaseKnowledge_Handler,
		},
		{
			MethodName: "DeleteBlackBoxCaseKnowledge",
			Handler:    _BlackBoxCaseKnowledgeService_DeleteBlackBoxCaseKnowledge_Handler,
		},
		{
			MethodName: "UpdateBlackBoxCaseKnowledge",
			Handler:    _BlackBoxCaseKnowledgeService_UpdateBlackBoxCaseKnowledge_Handler,
		},
		{
			MethodName: "GetBlackBoxCaseKnowledge",
			Handler:    _BlackBoxCaseKnowledgeService_GetBlackBoxCaseKnowledge_Handler,
		},
		{
			MethodName: "ListBlackBoxCaseKnowledge",
			Handler:    _BlackBoxCaseKnowledgeService_ListBlackBoxCaseKnowledge_Handler,
		},
		{
			MethodName: "ReloadBlackBoxCaseKnowledge",
			Handler:    _BlackBoxCaseKnowledgeService_ReloadBlackBoxCaseKnowledge_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "ai/ai.proto",
}

const (
	BlackBoxCaseRevisionService_CreateBlackBoxCaseRevision_FullMethodName              = "/ai.BlackBoxCaseRevisionService/CreateBlackBoxCaseRevision"
	BlackBoxCaseRevisionService_CreateBlackBoxCaseRevisionData_FullMethodName          = "/ai.BlackBoxCaseRevisionService/CreateBlackBoxCaseRevisionData"
	BlackBoxCaseRevisionService_DeleteBlackBoxCaseRevision_FullMethodName              = "/ai.BlackBoxCaseRevisionService/DeleteBlackBoxCaseRevision"
	BlackBoxCaseRevisionService_UpdateBlackBoxCaseRevision_FullMethodName              = "/ai.BlackBoxCaseRevisionService/UpdateBlackBoxCaseRevision"
	BlackBoxCaseRevisionService_UpdateBlackBoxCaseRevisionWithCaseRefId_FullMethodName = "/ai.BlackBoxCaseRevisionService/UpdateBlackBoxCaseRevisionWithCaseRefId"
	BlackBoxCaseRevisionService_AdoptBlackBoxCaseRevision_FullMethodName               = "/ai.BlackBoxCaseRevisionService/AdoptBlackBoxCaseRevision"
	BlackBoxCaseRevisionService_GetBlackBoxCaseRevision_FullMethodName                 = "/ai.BlackBoxCaseRevisionService/GetBlackBoxCaseRevision"
	BlackBoxCaseRevisionService_ListBlackBoxCaseRevision_FullMethodName                = "/ai.BlackBoxCaseRevisionService/ListBlackBoxCaseRevision"
	BlackBoxCaseRevisionService_GenerateBlackBoxCaseRevisionId_FullMethodName          = "/ai.BlackBoxCaseRevisionService/GenerateBlackBoxCaseRevisionId"
	BlackBoxCaseRevisionService_GetBlackBoxCaseRevisionByRevisionId_FullMethodName     = "/ai.BlackBoxCaseRevisionService/GetBlackBoxCaseRevisionByRevisionId"
)

// BlackBoxCaseRevisionServiceClient is the client API for BlackBoxCaseRevisionService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// BlackBoxCaseRevisionService
type BlackBoxCaseRevisionServiceClient interface {
	// CreateBlackBoxCaseRevision create a black box case
	CreateBlackBoxCaseRevision(ctx context.Context, in *CreateBlackBoxCaseRevisionReq, opts ...grpc.CallOption) (*CreateBlackBoxCaseRevisionResp, error)
	// CreateBlackBoxCaseRevisionData create a black box case data
	CreateBlackBoxCaseRevisionData(ctx context.Context, in *CreateBlackBoxCaseRevisionDataReq, opts ...grpc.CallOption) (*CreateBlackBoxCaseRevisionDataResp, error)
	// DeleteBlackBoxCaseRevision create a black box case
	DeleteBlackBoxCaseRevision(ctx context.Context, in *DeleteBlackBoxCaseRevisionReq, opts ...grpc.CallOption) (*DeleteBlackBoxCaseRevisionResp, error)
	// UpdateBlackBoxCaseRevision create a black box case
	UpdateBlackBoxCaseRevision(ctx context.Context, in *UpdateBlackBoxCaseRevisionReq, opts ...grpc.CallOption) (*UpdateBlackBoxCaseRevisionResp, error)
	// UpdateBlackBoxCaseRevisionWithCaseRefId update a black box case with case ref id
	UpdateBlackBoxCaseRevisionWithCaseRefId(ctx context.Context, in *UpdateBlackBoxCaseRevisionWithCaseRefIdReq, opts ...grpc.CallOption) (*UpdateBlackBoxCaseRevisionWithCaseRefIdResp, error)
	// AdoptBlackBoxCaseRevision adopt a black box case
	AdoptBlackBoxCaseRevision(ctx context.Context, in *AdoptBlackBoxCaseRevisionReq, opts ...grpc.CallOption) (*AdoptBlackBoxCaseRevisionResp, error)
	// GetBlackBoxCaseRevision gets a black box case
	GetBlackBoxCaseRevision(ctx context.Context, in *GetBlackBoxCaseRevisionReq, opts ...grpc.CallOption) (*GetBlackBoxCaseRevisionResp, error)
	// ListBlackBoxCaseRevision list black box case
	ListBlackBoxCaseRevision(ctx context.Context, in *ListBlackBoxCaseRevisionReq, opts ...grpc.CallOption) (*ListBlackBoxCaseRevisionResp, error)
	// GenerateBlackBoxCaseRevisionId list black box case
	GenerateBlackBoxCaseRevisionId(ctx context.Context, in *GenerateBlackBoxCaseRevisionIdReq, opts ...grpc.CallOption) (*GenerateBlackBoxCaseRevisionIdResp, error)
	// GetBlackBoxCaseRevisionByRevisionId get black box case revision by revisionId
	GetBlackBoxCaseRevisionByRevisionId(ctx context.Context, in *GetBlackBoxCaseRevisionByRevisionIdReq, opts ...grpc.CallOption) (*GetBlackBoxCaseRevisionByRevisionIdResp, error)
}

type blackBoxCaseRevisionServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewBlackBoxCaseRevisionServiceClient(cc grpc.ClientConnInterface) BlackBoxCaseRevisionServiceClient {
	return &blackBoxCaseRevisionServiceClient{cc}
}

func (c *blackBoxCaseRevisionServiceClient) CreateBlackBoxCaseRevision(ctx context.Context, in *CreateBlackBoxCaseRevisionReq, opts ...grpc.CallOption) (*CreateBlackBoxCaseRevisionResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateBlackBoxCaseRevisionResp)
	err := c.cc.Invoke(ctx, BlackBoxCaseRevisionService_CreateBlackBoxCaseRevision_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *blackBoxCaseRevisionServiceClient) CreateBlackBoxCaseRevisionData(ctx context.Context, in *CreateBlackBoxCaseRevisionDataReq, opts ...grpc.CallOption) (*CreateBlackBoxCaseRevisionDataResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateBlackBoxCaseRevisionDataResp)
	err := c.cc.Invoke(ctx, BlackBoxCaseRevisionService_CreateBlackBoxCaseRevisionData_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *blackBoxCaseRevisionServiceClient) DeleteBlackBoxCaseRevision(ctx context.Context, in *DeleteBlackBoxCaseRevisionReq, opts ...grpc.CallOption) (*DeleteBlackBoxCaseRevisionResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DeleteBlackBoxCaseRevisionResp)
	err := c.cc.Invoke(ctx, BlackBoxCaseRevisionService_DeleteBlackBoxCaseRevision_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *blackBoxCaseRevisionServiceClient) UpdateBlackBoxCaseRevision(ctx context.Context, in *UpdateBlackBoxCaseRevisionReq, opts ...grpc.CallOption) (*UpdateBlackBoxCaseRevisionResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UpdateBlackBoxCaseRevisionResp)
	err := c.cc.Invoke(ctx, BlackBoxCaseRevisionService_UpdateBlackBoxCaseRevision_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *blackBoxCaseRevisionServiceClient) UpdateBlackBoxCaseRevisionWithCaseRefId(ctx context.Context, in *UpdateBlackBoxCaseRevisionWithCaseRefIdReq, opts ...grpc.CallOption) (*UpdateBlackBoxCaseRevisionWithCaseRefIdResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UpdateBlackBoxCaseRevisionWithCaseRefIdResp)
	err := c.cc.Invoke(ctx, BlackBoxCaseRevisionService_UpdateBlackBoxCaseRevisionWithCaseRefId_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *blackBoxCaseRevisionServiceClient) AdoptBlackBoxCaseRevision(ctx context.Context, in *AdoptBlackBoxCaseRevisionReq, opts ...grpc.CallOption) (*AdoptBlackBoxCaseRevisionResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(AdoptBlackBoxCaseRevisionResp)
	err := c.cc.Invoke(ctx, BlackBoxCaseRevisionService_AdoptBlackBoxCaseRevision_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *blackBoxCaseRevisionServiceClient) GetBlackBoxCaseRevision(ctx context.Context, in *GetBlackBoxCaseRevisionReq, opts ...grpc.CallOption) (*GetBlackBoxCaseRevisionResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetBlackBoxCaseRevisionResp)
	err := c.cc.Invoke(ctx, BlackBoxCaseRevisionService_GetBlackBoxCaseRevision_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *blackBoxCaseRevisionServiceClient) ListBlackBoxCaseRevision(ctx context.Context, in *ListBlackBoxCaseRevisionReq, opts ...grpc.CallOption) (*ListBlackBoxCaseRevisionResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListBlackBoxCaseRevisionResp)
	err := c.cc.Invoke(ctx, BlackBoxCaseRevisionService_ListBlackBoxCaseRevision_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *blackBoxCaseRevisionServiceClient) GenerateBlackBoxCaseRevisionId(ctx context.Context, in *GenerateBlackBoxCaseRevisionIdReq, opts ...grpc.CallOption) (*GenerateBlackBoxCaseRevisionIdResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GenerateBlackBoxCaseRevisionIdResp)
	err := c.cc.Invoke(ctx, BlackBoxCaseRevisionService_GenerateBlackBoxCaseRevisionId_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *blackBoxCaseRevisionServiceClient) GetBlackBoxCaseRevisionByRevisionId(ctx context.Context, in *GetBlackBoxCaseRevisionByRevisionIdReq, opts ...grpc.CallOption) (*GetBlackBoxCaseRevisionByRevisionIdResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetBlackBoxCaseRevisionByRevisionIdResp)
	err := c.cc.Invoke(ctx, BlackBoxCaseRevisionService_GetBlackBoxCaseRevisionByRevisionId_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// BlackBoxCaseRevisionServiceServer is the server API for BlackBoxCaseRevisionService service.
// All implementations must embed UnimplementedBlackBoxCaseRevisionServiceServer
// for forward compatibility.
//
// BlackBoxCaseRevisionService
type BlackBoxCaseRevisionServiceServer interface {
	// CreateBlackBoxCaseRevision create a black box case
	CreateBlackBoxCaseRevision(context.Context, *CreateBlackBoxCaseRevisionReq) (*CreateBlackBoxCaseRevisionResp, error)
	// CreateBlackBoxCaseRevisionData create a black box case data
	CreateBlackBoxCaseRevisionData(context.Context, *CreateBlackBoxCaseRevisionDataReq) (*CreateBlackBoxCaseRevisionDataResp, error)
	// DeleteBlackBoxCaseRevision create a black box case
	DeleteBlackBoxCaseRevision(context.Context, *DeleteBlackBoxCaseRevisionReq) (*DeleteBlackBoxCaseRevisionResp, error)
	// UpdateBlackBoxCaseRevision create a black box case
	UpdateBlackBoxCaseRevision(context.Context, *UpdateBlackBoxCaseRevisionReq) (*UpdateBlackBoxCaseRevisionResp, error)
	// UpdateBlackBoxCaseRevisionWithCaseRefId update a black box case with case ref id
	UpdateBlackBoxCaseRevisionWithCaseRefId(context.Context, *UpdateBlackBoxCaseRevisionWithCaseRefIdReq) (*UpdateBlackBoxCaseRevisionWithCaseRefIdResp, error)
	// AdoptBlackBoxCaseRevision adopt a black box case
	AdoptBlackBoxCaseRevision(context.Context, *AdoptBlackBoxCaseRevisionReq) (*AdoptBlackBoxCaseRevisionResp, error)
	// GetBlackBoxCaseRevision gets a black box case
	GetBlackBoxCaseRevision(context.Context, *GetBlackBoxCaseRevisionReq) (*GetBlackBoxCaseRevisionResp, error)
	// ListBlackBoxCaseRevision list black box case
	ListBlackBoxCaseRevision(context.Context, *ListBlackBoxCaseRevisionReq) (*ListBlackBoxCaseRevisionResp, error)
	// GenerateBlackBoxCaseRevisionId list black box case
	GenerateBlackBoxCaseRevisionId(context.Context, *GenerateBlackBoxCaseRevisionIdReq) (*GenerateBlackBoxCaseRevisionIdResp, error)
	// GetBlackBoxCaseRevisionByRevisionId get black box case revision by revisionId
	GetBlackBoxCaseRevisionByRevisionId(context.Context, *GetBlackBoxCaseRevisionByRevisionIdReq) (*GetBlackBoxCaseRevisionByRevisionIdResp, error)
	mustEmbedUnimplementedBlackBoxCaseRevisionServiceServer()
}

// UnimplementedBlackBoxCaseRevisionServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedBlackBoxCaseRevisionServiceServer struct{}

func (UnimplementedBlackBoxCaseRevisionServiceServer) CreateBlackBoxCaseRevision(context.Context, *CreateBlackBoxCaseRevisionReq) (*CreateBlackBoxCaseRevisionResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateBlackBoxCaseRevision not implemented")
}
func (UnimplementedBlackBoxCaseRevisionServiceServer) CreateBlackBoxCaseRevisionData(context.Context, *CreateBlackBoxCaseRevisionDataReq) (*CreateBlackBoxCaseRevisionDataResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateBlackBoxCaseRevisionData not implemented")
}
func (UnimplementedBlackBoxCaseRevisionServiceServer) DeleteBlackBoxCaseRevision(context.Context, *DeleteBlackBoxCaseRevisionReq) (*DeleteBlackBoxCaseRevisionResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteBlackBoxCaseRevision not implemented")
}
func (UnimplementedBlackBoxCaseRevisionServiceServer) UpdateBlackBoxCaseRevision(context.Context, *UpdateBlackBoxCaseRevisionReq) (*UpdateBlackBoxCaseRevisionResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateBlackBoxCaseRevision not implemented")
}
func (UnimplementedBlackBoxCaseRevisionServiceServer) UpdateBlackBoxCaseRevisionWithCaseRefId(context.Context, *UpdateBlackBoxCaseRevisionWithCaseRefIdReq) (*UpdateBlackBoxCaseRevisionWithCaseRefIdResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateBlackBoxCaseRevisionWithCaseRefId not implemented")
}
func (UnimplementedBlackBoxCaseRevisionServiceServer) AdoptBlackBoxCaseRevision(context.Context, *AdoptBlackBoxCaseRevisionReq) (*AdoptBlackBoxCaseRevisionResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AdoptBlackBoxCaseRevision not implemented")
}
func (UnimplementedBlackBoxCaseRevisionServiceServer) GetBlackBoxCaseRevision(context.Context, *GetBlackBoxCaseRevisionReq) (*GetBlackBoxCaseRevisionResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetBlackBoxCaseRevision not implemented")
}
func (UnimplementedBlackBoxCaseRevisionServiceServer) ListBlackBoxCaseRevision(context.Context, *ListBlackBoxCaseRevisionReq) (*ListBlackBoxCaseRevisionResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListBlackBoxCaseRevision not implemented")
}
func (UnimplementedBlackBoxCaseRevisionServiceServer) GenerateBlackBoxCaseRevisionId(context.Context, *GenerateBlackBoxCaseRevisionIdReq) (*GenerateBlackBoxCaseRevisionIdResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GenerateBlackBoxCaseRevisionId not implemented")
}
func (UnimplementedBlackBoxCaseRevisionServiceServer) GetBlackBoxCaseRevisionByRevisionId(context.Context, *GetBlackBoxCaseRevisionByRevisionIdReq) (*GetBlackBoxCaseRevisionByRevisionIdResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetBlackBoxCaseRevisionByRevisionId not implemented")
}
func (UnimplementedBlackBoxCaseRevisionServiceServer) mustEmbedUnimplementedBlackBoxCaseRevisionServiceServer() {
}
func (UnimplementedBlackBoxCaseRevisionServiceServer) testEmbeddedByValue() {}

// UnsafeBlackBoxCaseRevisionServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to BlackBoxCaseRevisionServiceServer will
// result in compilation errors.
type UnsafeBlackBoxCaseRevisionServiceServer interface {
	mustEmbedUnimplementedBlackBoxCaseRevisionServiceServer()
}

func RegisterBlackBoxCaseRevisionServiceServer(s grpc.ServiceRegistrar, srv BlackBoxCaseRevisionServiceServer) {
	// If the following call pancis, it indicates UnimplementedBlackBoxCaseRevisionServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&BlackBoxCaseRevisionService_ServiceDesc, srv)
}

func _BlackBoxCaseRevisionService_CreateBlackBoxCaseRevision_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateBlackBoxCaseRevisionReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BlackBoxCaseRevisionServiceServer).CreateBlackBoxCaseRevision(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BlackBoxCaseRevisionService_CreateBlackBoxCaseRevision_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BlackBoxCaseRevisionServiceServer).CreateBlackBoxCaseRevision(ctx, req.(*CreateBlackBoxCaseRevisionReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BlackBoxCaseRevisionService_CreateBlackBoxCaseRevisionData_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateBlackBoxCaseRevisionDataReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BlackBoxCaseRevisionServiceServer).CreateBlackBoxCaseRevisionData(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BlackBoxCaseRevisionService_CreateBlackBoxCaseRevisionData_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BlackBoxCaseRevisionServiceServer).CreateBlackBoxCaseRevisionData(ctx, req.(*CreateBlackBoxCaseRevisionDataReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BlackBoxCaseRevisionService_DeleteBlackBoxCaseRevision_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteBlackBoxCaseRevisionReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BlackBoxCaseRevisionServiceServer).DeleteBlackBoxCaseRevision(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BlackBoxCaseRevisionService_DeleteBlackBoxCaseRevision_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BlackBoxCaseRevisionServiceServer).DeleteBlackBoxCaseRevision(ctx, req.(*DeleteBlackBoxCaseRevisionReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BlackBoxCaseRevisionService_UpdateBlackBoxCaseRevision_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateBlackBoxCaseRevisionReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BlackBoxCaseRevisionServiceServer).UpdateBlackBoxCaseRevision(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BlackBoxCaseRevisionService_UpdateBlackBoxCaseRevision_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BlackBoxCaseRevisionServiceServer).UpdateBlackBoxCaseRevision(ctx, req.(*UpdateBlackBoxCaseRevisionReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BlackBoxCaseRevisionService_UpdateBlackBoxCaseRevisionWithCaseRefId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateBlackBoxCaseRevisionWithCaseRefIdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BlackBoxCaseRevisionServiceServer).UpdateBlackBoxCaseRevisionWithCaseRefId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BlackBoxCaseRevisionService_UpdateBlackBoxCaseRevisionWithCaseRefId_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BlackBoxCaseRevisionServiceServer).UpdateBlackBoxCaseRevisionWithCaseRefId(ctx, req.(*UpdateBlackBoxCaseRevisionWithCaseRefIdReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BlackBoxCaseRevisionService_AdoptBlackBoxCaseRevision_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AdoptBlackBoxCaseRevisionReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BlackBoxCaseRevisionServiceServer).AdoptBlackBoxCaseRevision(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BlackBoxCaseRevisionService_AdoptBlackBoxCaseRevision_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BlackBoxCaseRevisionServiceServer).AdoptBlackBoxCaseRevision(ctx, req.(*AdoptBlackBoxCaseRevisionReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BlackBoxCaseRevisionService_GetBlackBoxCaseRevision_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetBlackBoxCaseRevisionReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BlackBoxCaseRevisionServiceServer).GetBlackBoxCaseRevision(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BlackBoxCaseRevisionService_GetBlackBoxCaseRevision_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BlackBoxCaseRevisionServiceServer).GetBlackBoxCaseRevision(ctx, req.(*GetBlackBoxCaseRevisionReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BlackBoxCaseRevisionService_ListBlackBoxCaseRevision_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListBlackBoxCaseRevisionReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BlackBoxCaseRevisionServiceServer).ListBlackBoxCaseRevision(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BlackBoxCaseRevisionService_ListBlackBoxCaseRevision_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BlackBoxCaseRevisionServiceServer).ListBlackBoxCaseRevision(ctx, req.(*ListBlackBoxCaseRevisionReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BlackBoxCaseRevisionService_GenerateBlackBoxCaseRevisionId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GenerateBlackBoxCaseRevisionIdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BlackBoxCaseRevisionServiceServer).GenerateBlackBoxCaseRevisionId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BlackBoxCaseRevisionService_GenerateBlackBoxCaseRevisionId_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BlackBoxCaseRevisionServiceServer).GenerateBlackBoxCaseRevisionId(ctx, req.(*GenerateBlackBoxCaseRevisionIdReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BlackBoxCaseRevisionService_GetBlackBoxCaseRevisionByRevisionId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetBlackBoxCaseRevisionByRevisionIdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BlackBoxCaseRevisionServiceServer).GetBlackBoxCaseRevisionByRevisionId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BlackBoxCaseRevisionService_GetBlackBoxCaseRevisionByRevisionId_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BlackBoxCaseRevisionServiceServer).GetBlackBoxCaseRevisionByRevisionId(ctx, req.(*GetBlackBoxCaseRevisionByRevisionIdReq))
	}
	return interceptor(ctx, in, info, handler)
}

// BlackBoxCaseRevisionService_ServiceDesc is the grpc.ServiceDesc for BlackBoxCaseRevisionService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var BlackBoxCaseRevisionService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "ai.BlackBoxCaseRevisionService",
	HandlerType: (*BlackBoxCaseRevisionServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateBlackBoxCaseRevision",
			Handler:    _BlackBoxCaseRevisionService_CreateBlackBoxCaseRevision_Handler,
		},
		{
			MethodName: "CreateBlackBoxCaseRevisionData",
			Handler:    _BlackBoxCaseRevisionService_CreateBlackBoxCaseRevisionData_Handler,
		},
		{
			MethodName: "DeleteBlackBoxCaseRevision",
			Handler:    _BlackBoxCaseRevisionService_DeleteBlackBoxCaseRevision_Handler,
		},
		{
			MethodName: "UpdateBlackBoxCaseRevision",
			Handler:    _BlackBoxCaseRevisionService_UpdateBlackBoxCaseRevision_Handler,
		},
		{
			MethodName: "UpdateBlackBoxCaseRevisionWithCaseRefId",
			Handler:    _BlackBoxCaseRevisionService_UpdateBlackBoxCaseRevisionWithCaseRefId_Handler,
		},
		{
			MethodName: "AdoptBlackBoxCaseRevision",
			Handler:    _BlackBoxCaseRevisionService_AdoptBlackBoxCaseRevision_Handler,
		},
		{
			MethodName: "GetBlackBoxCaseRevision",
			Handler:    _BlackBoxCaseRevisionService_GetBlackBoxCaseRevision_Handler,
		},
		{
			MethodName: "ListBlackBoxCaseRevision",
			Handler:    _BlackBoxCaseRevisionService_ListBlackBoxCaseRevision_Handler,
		},
		{
			MethodName: "GenerateBlackBoxCaseRevisionId",
			Handler:    _BlackBoxCaseRevisionService_GenerateBlackBoxCaseRevisionId_Handler,
		},
		{
			MethodName: "GetBlackBoxCaseRevisionByRevisionId",
			Handler:    _BlackBoxCaseRevisionService_GetBlackBoxCaseRevisionByRevisionId_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "ai/ai.proto",
}

const (
	BlackBoxCaseDataService_CreateBlackBoxCaseData_FullMethodName        = "/ai.BlackBoxCaseDataService/CreateBlackBoxCaseData"
	BlackBoxCaseDataService_DeleteBlackBoxCaseData_FullMethodName        = "/ai.BlackBoxCaseDataService/DeleteBlackBoxCaseData"
	BlackBoxCaseDataService_UpdateBlackBoxCaseData_FullMethodName        = "/ai.BlackBoxCaseDataService/UpdateBlackBoxCaseData"
	BlackBoxCaseDataService_UpdateBlackBoxCaseDataOrder_FullMethodName   = "/ai.BlackBoxCaseDataService/UpdateBlackBoxCaseDataOrder"
	BlackBoxCaseDataService_BatchUpdateBlackBoxCaseData_FullMethodName   = "/ai.BlackBoxCaseDataService/BatchUpdateBlackBoxCaseData"
	BlackBoxCaseDataService_GetBlackBoxCaseData_FullMethodName           = "/ai.BlackBoxCaseDataService/GetBlackBoxCaseData"
	BlackBoxCaseDataService_ListBlackBoxCaseData_FullMethodName          = "/ai.BlackBoxCaseDataService/ListBlackBoxCaseData"
	BlackBoxCaseDataService_ListBlackBoxCaseDataV24_FullMethodName       = "/ai.BlackBoxCaseDataService/ListBlackBoxCaseDataV24"
	BlackBoxCaseDataService_KeepBlackBoxCaseData_FullMethodName          = "/ai.BlackBoxCaseDataService/KeepBlackBoxCaseData"
	BlackBoxCaseDataService_ClearKeepBlackBoxCaseData_FullMethodName     = "/ai.BlackBoxCaseDataService/ClearKeepBlackBoxCaseData"
	BlackBoxCaseDataService_AppendBlackBoxCaseData_FullMethodName        = "/ai.BlackBoxCaseDataService/AppendBlackBoxCaseData"
	BlackBoxCaseDataService_ReplaceBlackBoxCaseData_FullMethodName       = "/ai.BlackBoxCaseDataService/ReplaceBlackBoxCaseData"
	BlackBoxCaseDataService_GenerateBlackBoxCaseRef_FullMethodName       = "/ai.BlackBoxCaseDataService/GenerateBlackBoxCaseRef"
	BlackBoxCaseDataService_RefreshCaseRefMetrics_FullMethodName         = "/ai.BlackBoxCaseDataService/RefreshCaseRefMetrics"
	BlackBoxCaseDataService_ReorderBlackBoxCaseData_FullMethodName       = "/ai.BlackBoxCaseDataService/ReorderBlackBoxCaseData"
	BlackBoxCaseDataService_ListBlackBoxCaseDataByCaseIds_FullMethodName = "/ai.BlackBoxCaseDataService/ListBlackBoxCaseDataByCaseIds"
)

// BlackBoxCaseDataServiceClient is the client API for BlackBoxCaseDataService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// BlackBoxCaseDataService
type BlackBoxCaseDataServiceClient interface {
	// CreateBlackBoxCaseData create a black box case
	CreateBlackBoxCaseData(ctx context.Context, in *CreateBlackBoxCaseDataReq, opts ...grpc.CallOption) (*CreateBlackBoxCaseDataResp, error)
	// DeleteBlackBoxCaseData create a black box case
	DeleteBlackBoxCaseData(ctx context.Context, in *DeleteBlackBoxCaseDataReq, opts ...grpc.CallOption) (*DeleteBlackBoxCaseDataResp, error)
	// UpdateBlackBoxCaseData create a black box case
	UpdateBlackBoxCaseData(ctx context.Context, in *UpdateBlackBoxCaseDataReq, opts ...grpc.CallOption) (*UpdateBlackBoxCaseDataResp, error)
	// UpdateBlackBoxCaseData create a black box case
	UpdateBlackBoxCaseDataOrder(ctx context.Context, in *UpdateBlackBoxCaseDataOrderReq, opts ...grpc.CallOption) (*UpdateBlackBoxCaseDataOrderResp, error)
	// BatchUpdateBlackBoxCaseData create a black box case
	BatchUpdateBlackBoxCaseData(ctx context.Context, in *BatchUpdateBlackBoxCaseDataReq, opts ...grpc.CallOption) (*BatchUpdateBlackBoxCaseDataResp, error)
	// GetBlackBoxCaseData gets a black box case
	GetBlackBoxCaseData(ctx context.Context, in *GetBlackBoxCaseDataReq, opts ...grpc.CallOption) (*GetBlackBoxCaseDataResp, error)
	// ListBlackBoxCaseData list black box case
	ListBlackBoxCaseData(ctx context.Context, in *ListBlackBoxCaseDataReq, opts ...grpc.CallOption) (*ListBlackBoxCaseDataResp, error)
	// ListBlackBoxCaseDataV24 list black box case
	ListBlackBoxCaseDataV24(ctx context.Context, in *ListBlackBoxCaseDataReq, opts ...grpc.CallOption) (*ListBlackBoxCaseDataResp, error)
	// KeepBlackBoxCaseData keep black box case
	KeepBlackBoxCaseData(ctx context.Context, in *KeepBlackBoxCaseDataReq, opts ...grpc.CallOption) (*KeepBlackBoxCaseDataResp, error)
	// KeepBlackBoxCaseData keep black box case
	ClearKeepBlackBoxCaseData(ctx context.Context, in *ClearKeepBlackBoxCaseDataReq, opts ...grpc.CallOption) (*ClearKeepBlackBoxCaseDataResp, error)
	// AppendBlackBoxCaseData append black box case
	AppendBlackBoxCaseData(ctx context.Context, in *AppendBlackBoxCaseDataReq, opts ...grpc.CallOption) (*AppendBlackBoxCaseDataResp, error)
	// ReplaceBlackBoxCaseData replace black box case
	ReplaceBlackBoxCaseData(ctx context.Context, in *ReplaceBlackBoxCaseDataReq, opts ...grpc.CallOption) (*ReplaceBlackBoxCaseDataResp, error)
	// ReplaceBlackBoxCaseData replace black box case
	GenerateBlackBoxCaseRef(ctx context.Context, in *GenerateBlackBoxCaseRefReq, opts ...grpc.CallOption) (*GenerateBlackBoxCaseRefResp, error)
	// RefreshCaseRefMetrics replace black box case
	RefreshCaseRefMetrics(ctx context.Context, in *RefreshCaseRefMetricsReq, opts ...grpc.CallOption) (*RefreshCaseRefMetricsResp, error)
	// ReorderBlackBoxCaseData list black box case
	ReorderBlackBoxCaseData(ctx context.Context, in *ReorderBlackBoxCaseDataReq, opts ...grpc.CallOption) (*ReorderBlackBoxCaseDataResp, error)
	// ListBlackBoxCaseDataByCaseIds list black box case by caseIds
	ListBlackBoxCaseDataByCaseIds(ctx context.Context, in *ListBlackBoxCaseDataByCaseIdsReq, opts ...grpc.CallOption) (*ListBlackBoxCaseDataByCaseIdsResp, error)
}

type blackBoxCaseDataServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewBlackBoxCaseDataServiceClient(cc grpc.ClientConnInterface) BlackBoxCaseDataServiceClient {
	return &blackBoxCaseDataServiceClient{cc}
}

func (c *blackBoxCaseDataServiceClient) CreateBlackBoxCaseData(ctx context.Context, in *CreateBlackBoxCaseDataReq, opts ...grpc.CallOption) (*CreateBlackBoxCaseDataResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateBlackBoxCaseDataResp)
	err := c.cc.Invoke(ctx, BlackBoxCaseDataService_CreateBlackBoxCaseData_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *blackBoxCaseDataServiceClient) DeleteBlackBoxCaseData(ctx context.Context, in *DeleteBlackBoxCaseDataReq, opts ...grpc.CallOption) (*DeleteBlackBoxCaseDataResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DeleteBlackBoxCaseDataResp)
	err := c.cc.Invoke(ctx, BlackBoxCaseDataService_DeleteBlackBoxCaseData_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *blackBoxCaseDataServiceClient) UpdateBlackBoxCaseData(ctx context.Context, in *UpdateBlackBoxCaseDataReq, opts ...grpc.CallOption) (*UpdateBlackBoxCaseDataResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UpdateBlackBoxCaseDataResp)
	err := c.cc.Invoke(ctx, BlackBoxCaseDataService_UpdateBlackBoxCaseData_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *blackBoxCaseDataServiceClient) UpdateBlackBoxCaseDataOrder(ctx context.Context, in *UpdateBlackBoxCaseDataOrderReq, opts ...grpc.CallOption) (*UpdateBlackBoxCaseDataOrderResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UpdateBlackBoxCaseDataOrderResp)
	err := c.cc.Invoke(ctx, BlackBoxCaseDataService_UpdateBlackBoxCaseDataOrder_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *blackBoxCaseDataServiceClient) BatchUpdateBlackBoxCaseData(ctx context.Context, in *BatchUpdateBlackBoxCaseDataReq, opts ...grpc.CallOption) (*BatchUpdateBlackBoxCaseDataResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(BatchUpdateBlackBoxCaseDataResp)
	err := c.cc.Invoke(ctx, BlackBoxCaseDataService_BatchUpdateBlackBoxCaseData_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *blackBoxCaseDataServiceClient) GetBlackBoxCaseData(ctx context.Context, in *GetBlackBoxCaseDataReq, opts ...grpc.CallOption) (*GetBlackBoxCaseDataResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetBlackBoxCaseDataResp)
	err := c.cc.Invoke(ctx, BlackBoxCaseDataService_GetBlackBoxCaseData_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *blackBoxCaseDataServiceClient) ListBlackBoxCaseData(ctx context.Context, in *ListBlackBoxCaseDataReq, opts ...grpc.CallOption) (*ListBlackBoxCaseDataResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListBlackBoxCaseDataResp)
	err := c.cc.Invoke(ctx, BlackBoxCaseDataService_ListBlackBoxCaseData_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *blackBoxCaseDataServiceClient) ListBlackBoxCaseDataV24(ctx context.Context, in *ListBlackBoxCaseDataReq, opts ...grpc.CallOption) (*ListBlackBoxCaseDataResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListBlackBoxCaseDataResp)
	err := c.cc.Invoke(ctx, BlackBoxCaseDataService_ListBlackBoxCaseDataV24_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *blackBoxCaseDataServiceClient) KeepBlackBoxCaseData(ctx context.Context, in *KeepBlackBoxCaseDataReq, opts ...grpc.CallOption) (*KeepBlackBoxCaseDataResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(KeepBlackBoxCaseDataResp)
	err := c.cc.Invoke(ctx, BlackBoxCaseDataService_KeepBlackBoxCaseData_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *blackBoxCaseDataServiceClient) ClearKeepBlackBoxCaseData(ctx context.Context, in *ClearKeepBlackBoxCaseDataReq, opts ...grpc.CallOption) (*ClearKeepBlackBoxCaseDataResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ClearKeepBlackBoxCaseDataResp)
	err := c.cc.Invoke(ctx, BlackBoxCaseDataService_ClearKeepBlackBoxCaseData_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *blackBoxCaseDataServiceClient) AppendBlackBoxCaseData(ctx context.Context, in *AppendBlackBoxCaseDataReq, opts ...grpc.CallOption) (*AppendBlackBoxCaseDataResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(AppendBlackBoxCaseDataResp)
	err := c.cc.Invoke(ctx, BlackBoxCaseDataService_AppendBlackBoxCaseData_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *blackBoxCaseDataServiceClient) ReplaceBlackBoxCaseData(ctx context.Context, in *ReplaceBlackBoxCaseDataReq, opts ...grpc.CallOption) (*ReplaceBlackBoxCaseDataResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ReplaceBlackBoxCaseDataResp)
	err := c.cc.Invoke(ctx, BlackBoxCaseDataService_ReplaceBlackBoxCaseData_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *blackBoxCaseDataServiceClient) GenerateBlackBoxCaseRef(ctx context.Context, in *GenerateBlackBoxCaseRefReq, opts ...grpc.CallOption) (*GenerateBlackBoxCaseRefResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GenerateBlackBoxCaseRefResp)
	err := c.cc.Invoke(ctx, BlackBoxCaseDataService_GenerateBlackBoxCaseRef_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *blackBoxCaseDataServiceClient) RefreshCaseRefMetrics(ctx context.Context, in *RefreshCaseRefMetricsReq, opts ...grpc.CallOption) (*RefreshCaseRefMetricsResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(RefreshCaseRefMetricsResp)
	err := c.cc.Invoke(ctx, BlackBoxCaseDataService_RefreshCaseRefMetrics_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *blackBoxCaseDataServiceClient) ReorderBlackBoxCaseData(ctx context.Context, in *ReorderBlackBoxCaseDataReq, opts ...grpc.CallOption) (*ReorderBlackBoxCaseDataResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ReorderBlackBoxCaseDataResp)
	err := c.cc.Invoke(ctx, BlackBoxCaseDataService_ReorderBlackBoxCaseData_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *blackBoxCaseDataServiceClient) ListBlackBoxCaseDataByCaseIds(ctx context.Context, in *ListBlackBoxCaseDataByCaseIdsReq, opts ...grpc.CallOption) (*ListBlackBoxCaseDataByCaseIdsResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListBlackBoxCaseDataByCaseIdsResp)
	err := c.cc.Invoke(ctx, BlackBoxCaseDataService_ListBlackBoxCaseDataByCaseIds_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// BlackBoxCaseDataServiceServer is the server API for BlackBoxCaseDataService service.
// All implementations must embed UnimplementedBlackBoxCaseDataServiceServer
// for forward compatibility.
//
// BlackBoxCaseDataService
type BlackBoxCaseDataServiceServer interface {
	// CreateBlackBoxCaseData create a black box case
	CreateBlackBoxCaseData(context.Context, *CreateBlackBoxCaseDataReq) (*CreateBlackBoxCaseDataResp, error)
	// DeleteBlackBoxCaseData create a black box case
	DeleteBlackBoxCaseData(context.Context, *DeleteBlackBoxCaseDataReq) (*DeleteBlackBoxCaseDataResp, error)
	// UpdateBlackBoxCaseData create a black box case
	UpdateBlackBoxCaseData(context.Context, *UpdateBlackBoxCaseDataReq) (*UpdateBlackBoxCaseDataResp, error)
	// UpdateBlackBoxCaseData create a black box case
	UpdateBlackBoxCaseDataOrder(context.Context, *UpdateBlackBoxCaseDataOrderReq) (*UpdateBlackBoxCaseDataOrderResp, error)
	// BatchUpdateBlackBoxCaseData create a black box case
	BatchUpdateBlackBoxCaseData(context.Context, *BatchUpdateBlackBoxCaseDataReq) (*BatchUpdateBlackBoxCaseDataResp, error)
	// GetBlackBoxCaseData gets a black box case
	GetBlackBoxCaseData(context.Context, *GetBlackBoxCaseDataReq) (*GetBlackBoxCaseDataResp, error)
	// ListBlackBoxCaseData list black box case
	ListBlackBoxCaseData(context.Context, *ListBlackBoxCaseDataReq) (*ListBlackBoxCaseDataResp, error)
	// ListBlackBoxCaseDataV24 list black box case
	ListBlackBoxCaseDataV24(context.Context, *ListBlackBoxCaseDataReq) (*ListBlackBoxCaseDataResp, error)
	// KeepBlackBoxCaseData keep black box case
	KeepBlackBoxCaseData(context.Context, *KeepBlackBoxCaseDataReq) (*KeepBlackBoxCaseDataResp, error)
	// KeepBlackBoxCaseData keep black box case
	ClearKeepBlackBoxCaseData(context.Context, *ClearKeepBlackBoxCaseDataReq) (*ClearKeepBlackBoxCaseDataResp, error)
	// AppendBlackBoxCaseData append black box case
	AppendBlackBoxCaseData(context.Context, *AppendBlackBoxCaseDataReq) (*AppendBlackBoxCaseDataResp, error)
	// ReplaceBlackBoxCaseData replace black box case
	ReplaceBlackBoxCaseData(context.Context, *ReplaceBlackBoxCaseDataReq) (*ReplaceBlackBoxCaseDataResp, error)
	// ReplaceBlackBoxCaseData replace black box case
	GenerateBlackBoxCaseRef(context.Context, *GenerateBlackBoxCaseRefReq) (*GenerateBlackBoxCaseRefResp, error)
	// RefreshCaseRefMetrics replace black box case
	RefreshCaseRefMetrics(context.Context, *RefreshCaseRefMetricsReq) (*RefreshCaseRefMetricsResp, error)
	// ReorderBlackBoxCaseData list black box case
	ReorderBlackBoxCaseData(context.Context, *ReorderBlackBoxCaseDataReq) (*ReorderBlackBoxCaseDataResp, error)
	// ListBlackBoxCaseDataByCaseIds list black box case by caseIds
	ListBlackBoxCaseDataByCaseIds(context.Context, *ListBlackBoxCaseDataByCaseIdsReq) (*ListBlackBoxCaseDataByCaseIdsResp, error)
	mustEmbedUnimplementedBlackBoxCaseDataServiceServer()
}

// UnimplementedBlackBoxCaseDataServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedBlackBoxCaseDataServiceServer struct{}

func (UnimplementedBlackBoxCaseDataServiceServer) CreateBlackBoxCaseData(context.Context, *CreateBlackBoxCaseDataReq) (*CreateBlackBoxCaseDataResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateBlackBoxCaseData not implemented")
}
func (UnimplementedBlackBoxCaseDataServiceServer) DeleteBlackBoxCaseData(context.Context, *DeleteBlackBoxCaseDataReq) (*DeleteBlackBoxCaseDataResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteBlackBoxCaseData not implemented")
}
func (UnimplementedBlackBoxCaseDataServiceServer) UpdateBlackBoxCaseData(context.Context, *UpdateBlackBoxCaseDataReq) (*UpdateBlackBoxCaseDataResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateBlackBoxCaseData not implemented")
}
func (UnimplementedBlackBoxCaseDataServiceServer) UpdateBlackBoxCaseDataOrder(context.Context, *UpdateBlackBoxCaseDataOrderReq) (*UpdateBlackBoxCaseDataOrderResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateBlackBoxCaseDataOrder not implemented")
}
func (UnimplementedBlackBoxCaseDataServiceServer) BatchUpdateBlackBoxCaseData(context.Context, *BatchUpdateBlackBoxCaseDataReq) (*BatchUpdateBlackBoxCaseDataResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BatchUpdateBlackBoxCaseData not implemented")
}
func (UnimplementedBlackBoxCaseDataServiceServer) GetBlackBoxCaseData(context.Context, *GetBlackBoxCaseDataReq) (*GetBlackBoxCaseDataResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetBlackBoxCaseData not implemented")
}
func (UnimplementedBlackBoxCaseDataServiceServer) ListBlackBoxCaseData(context.Context, *ListBlackBoxCaseDataReq) (*ListBlackBoxCaseDataResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListBlackBoxCaseData not implemented")
}
func (UnimplementedBlackBoxCaseDataServiceServer) ListBlackBoxCaseDataV24(context.Context, *ListBlackBoxCaseDataReq) (*ListBlackBoxCaseDataResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListBlackBoxCaseDataV24 not implemented")
}
func (UnimplementedBlackBoxCaseDataServiceServer) KeepBlackBoxCaseData(context.Context, *KeepBlackBoxCaseDataReq) (*KeepBlackBoxCaseDataResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method KeepBlackBoxCaseData not implemented")
}
func (UnimplementedBlackBoxCaseDataServiceServer) ClearKeepBlackBoxCaseData(context.Context, *ClearKeepBlackBoxCaseDataReq) (*ClearKeepBlackBoxCaseDataResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ClearKeepBlackBoxCaseData not implemented")
}
func (UnimplementedBlackBoxCaseDataServiceServer) AppendBlackBoxCaseData(context.Context, *AppendBlackBoxCaseDataReq) (*AppendBlackBoxCaseDataResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AppendBlackBoxCaseData not implemented")
}
func (UnimplementedBlackBoxCaseDataServiceServer) ReplaceBlackBoxCaseData(context.Context, *ReplaceBlackBoxCaseDataReq) (*ReplaceBlackBoxCaseDataResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ReplaceBlackBoxCaseData not implemented")
}
func (UnimplementedBlackBoxCaseDataServiceServer) GenerateBlackBoxCaseRef(context.Context, *GenerateBlackBoxCaseRefReq) (*GenerateBlackBoxCaseRefResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GenerateBlackBoxCaseRef not implemented")
}
func (UnimplementedBlackBoxCaseDataServiceServer) RefreshCaseRefMetrics(context.Context, *RefreshCaseRefMetricsReq) (*RefreshCaseRefMetricsResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RefreshCaseRefMetrics not implemented")
}
func (UnimplementedBlackBoxCaseDataServiceServer) ReorderBlackBoxCaseData(context.Context, *ReorderBlackBoxCaseDataReq) (*ReorderBlackBoxCaseDataResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ReorderBlackBoxCaseData not implemented")
}
func (UnimplementedBlackBoxCaseDataServiceServer) ListBlackBoxCaseDataByCaseIds(context.Context, *ListBlackBoxCaseDataByCaseIdsReq) (*ListBlackBoxCaseDataByCaseIdsResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListBlackBoxCaseDataByCaseIds not implemented")
}
func (UnimplementedBlackBoxCaseDataServiceServer) mustEmbedUnimplementedBlackBoxCaseDataServiceServer() {
}
func (UnimplementedBlackBoxCaseDataServiceServer) testEmbeddedByValue() {}

// UnsafeBlackBoxCaseDataServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to BlackBoxCaseDataServiceServer will
// result in compilation errors.
type UnsafeBlackBoxCaseDataServiceServer interface {
	mustEmbedUnimplementedBlackBoxCaseDataServiceServer()
}

func RegisterBlackBoxCaseDataServiceServer(s grpc.ServiceRegistrar, srv BlackBoxCaseDataServiceServer) {
	// If the following call pancis, it indicates UnimplementedBlackBoxCaseDataServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&BlackBoxCaseDataService_ServiceDesc, srv)
}

func _BlackBoxCaseDataService_CreateBlackBoxCaseData_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateBlackBoxCaseDataReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BlackBoxCaseDataServiceServer).CreateBlackBoxCaseData(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BlackBoxCaseDataService_CreateBlackBoxCaseData_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BlackBoxCaseDataServiceServer).CreateBlackBoxCaseData(ctx, req.(*CreateBlackBoxCaseDataReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BlackBoxCaseDataService_DeleteBlackBoxCaseData_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteBlackBoxCaseDataReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BlackBoxCaseDataServiceServer).DeleteBlackBoxCaseData(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BlackBoxCaseDataService_DeleteBlackBoxCaseData_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BlackBoxCaseDataServiceServer).DeleteBlackBoxCaseData(ctx, req.(*DeleteBlackBoxCaseDataReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BlackBoxCaseDataService_UpdateBlackBoxCaseData_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateBlackBoxCaseDataReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BlackBoxCaseDataServiceServer).UpdateBlackBoxCaseData(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BlackBoxCaseDataService_UpdateBlackBoxCaseData_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BlackBoxCaseDataServiceServer).UpdateBlackBoxCaseData(ctx, req.(*UpdateBlackBoxCaseDataReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BlackBoxCaseDataService_UpdateBlackBoxCaseDataOrder_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateBlackBoxCaseDataOrderReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BlackBoxCaseDataServiceServer).UpdateBlackBoxCaseDataOrder(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BlackBoxCaseDataService_UpdateBlackBoxCaseDataOrder_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BlackBoxCaseDataServiceServer).UpdateBlackBoxCaseDataOrder(ctx, req.(*UpdateBlackBoxCaseDataOrderReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BlackBoxCaseDataService_BatchUpdateBlackBoxCaseData_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchUpdateBlackBoxCaseDataReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BlackBoxCaseDataServiceServer).BatchUpdateBlackBoxCaseData(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BlackBoxCaseDataService_BatchUpdateBlackBoxCaseData_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BlackBoxCaseDataServiceServer).BatchUpdateBlackBoxCaseData(ctx, req.(*BatchUpdateBlackBoxCaseDataReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BlackBoxCaseDataService_GetBlackBoxCaseData_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetBlackBoxCaseDataReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BlackBoxCaseDataServiceServer).GetBlackBoxCaseData(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BlackBoxCaseDataService_GetBlackBoxCaseData_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BlackBoxCaseDataServiceServer).GetBlackBoxCaseData(ctx, req.(*GetBlackBoxCaseDataReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BlackBoxCaseDataService_ListBlackBoxCaseData_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListBlackBoxCaseDataReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BlackBoxCaseDataServiceServer).ListBlackBoxCaseData(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BlackBoxCaseDataService_ListBlackBoxCaseData_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BlackBoxCaseDataServiceServer).ListBlackBoxCaseData(ctx, req.(*ListBlackBoxCaseDataReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BlackBoxCaseDataService_ListBlackBoxCaseDataV24_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListBlackBoxCaseDataReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BlackBoxCaseDataServiceServer).ListBlackBoxCaseDataV24(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BlackBoxCaseDataService_ListBlackBoxCaseDataV24_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BlackBoxCaseDataServiceServer).ListBlackBoxCaseDataV24(ctx, req.(*ListBlackBoxCaseDataReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BlackBoxCaseDataService_KeepBlackBoxCaseData_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(KeepBlackBoxCaseDataReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BlackBoxCaseDataServiceServer).KeepBlackBoxCaseData(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BlackBoxCaseDataService_KeepBlackBoxCaseData_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BlackBoxCaseDataServiceServer).KeepBlackBoxCaseData(ctx, req.(*KeepBlackBoxCaseDataReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BlackBoxCaseDataService_ClearKeepBlackBoxCaseData_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ClearKeepBlackBoxCaseDataReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BlackBoxCaseDataServiceServer).ClearKeepBlackBoxCaseData(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BlackBoxCaseDataService_ClearKeepBlackBoxCaseData_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BlackBoxCaseDataServiceServer).ClearKeepBlackBoxCaseData(ctx, req.(*ClearKeepBlackBoxCaseDataReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BlackBoxCaseDataService_AppendBlackBoxCaseData_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AppendBlackBoxCaseDataReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BlackBoxCaseDataServiceServer).AppendBlackBoxCaseData(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BlackBoxCaseDataService_AppendBlackBoxCaseData_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BlackBoxCaseDataServiceServer).AppendBlackBoxCaseData(ctx, req.(*AppendBlackBoxCaseDataReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BlackBoxCaseDataService_ReplaceBlackBoxCaseData_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReplaceBlackBoxCaseDataReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BlackBoxCaseDataServiceServer).ReplaceBlackBoxCaseData(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BlackBoxCaseDataService_ReplaceBlackBoxCaseData_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BlackBoxCaseDataServiceServer).ReplaceBlackBoxCaseData(ctx, req.(*ReplaceBlackBoxCaseDataReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BlackBoxCaseDataService_GenerateBlackBoxCaseRef_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GenerateBlackBoxCaseRefReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BlackBoxCaseDataServiceServer).GenerateBlackBoxCaseRef(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BlackBoxCaseDataService_GenerateBlackBoxCaseRef_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BlackBoxCaseDataServiceServer).GenerateBlackBoxCaseRef(ctx, req.(*GenerateBlackBoxCaseRefReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BlackBoxCaseDataService_RefreshCaseRefMetrics_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RefreshCaseRefMetricsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BlackBoxCaseDataServiceServer).RefreshCaseRefMetrics(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BlackBoxCaseDataService_RefreshCaseRefMetrics_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BlackBoxCaseDataServiceServer).RefreshCaseRefMetrics(ctx, req.(*RefreshCaseRefMetricsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BlackBoxCaseDataService_ReorderBlackBoxCaseData_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReorderBlackBoxCaseDataReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BlackBoxCaseDataServiceServer).ReorderBlackBoxCaseData(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BlackBoxCaseDataService_ReorderBlackBoxCaseData_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BlackBoxCaseDataServiceServer).ReorderBlackBoxCaseData(ctx, req.(*ReorderBlackBoxCaseDataReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BlackBoxCaseDataService_ListBlackBoxCaseDataByCaseIds_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListBlackBoxCaseDataByCaseIdsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BlackBoxCaseDataServiceServer).ListBlackBoxCaseDataByCaseIds(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BlackBoxCaseDataService_ListBlackBoxCaseDataByCaseIds_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BlackBoxCaseDataServiceServer).ListBlackBoxCaseDataByCaseIds(ctx, req.(*ListBlackBoxCaseDataByCaseIdsReq))
	}
	return interceptor(ctx, in, info, handler)
}

// BlackBoxCaseDataService_ServiceDesc is the grpc.ServiceDesc for BlackBoxCaseDataService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var BlackBoxCaseDataService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "ai.BlackBoxCaseDataService",
	HandlerType: (*BlackBoxCaseDataServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateBlackBoxCaseData",
			Handler:    _BlackBoxCaseDataService_CreateBlackBoxCaseData_Handler,
		},
		{
			MethodName: "DeleteBlackBoxCaseData",
			Handler:    _BlackBoxCaseDataService_DeleteBlackBoxCaseData_Handler,
		},
		{
			MethodName: "UpdateBlackBoxCaseData",
			Handler:    _BlackBoxCaseDataService_UpdateBlackBoxCaseData_Handler,
		},
		{
			MethodName: "UpdateBlackBoxCaseDataOrder",
			Handler:    _BlackBoxCaseDataService_UpdateBlackBoxCaseDataOrder_Handler,
		},
		{
			MethodName: "BatchUpdateBlackBoxCaseData",
			Handler:    _BlackBoxCaseDataService_BatchUpdateBlackBoxCaseData_Handler,
		},
		{
			MethodName: "GetBlackBoxCaseData",
			Handler:    _BlackBoxCaseDataService_GetBlackBoxCaseData_Handler,
		},
		{
			MethodName: "ListBlackBoxCaseData",
			Handler:    _BlackBoxCaseDataService_ListBlackBoxCaseData_Handler,
		},
		{
			MethodName: "ListBlackBoxCaseDataV24",
			Handler:    _BlackBoxCaseDataService_ListBlackBoxCaseDataV24_Handler,
		},
		{
			MethodName: "KeepBlackBoxCaseData",
			Handler:    _BlackBoxCaseDataService_KeepBlackBoxCaseData_Handler,
		},
		{
			MethodName: "ClearKeepBlackBoxCaseData",
			Handler:    _BlackBoxCaseDataService_ClearKeepBlackBoxCaseData_Handler,
		},
		{
			MethodName: "AppendBlackBoxCaseData",
			Handler:    _BlackBoxCaseDataService_AppendBlackBoxCaseData_Handler,
		},
		{
			MethodName: "ReplaceBlackBoxCaseData",
			Handler:    _BlackBoxCaseDataService_ReplaceBlackBoxCaseData_Handler,
		},
		{
			MethodName: "GenerateBlackBoxCaseRef",
			Handler:    _BlackBoxCaseDataService_GenerateBlackBoxCaseRef_Handler,
		},
		{
			MethodName: "RefreshCaseRefMetrics",
			Handler:    _BlackBoxCaseDataService_RefreshCaseRefMetrics_Handler,
		},
		{
			MethodName: "ReorderBlackBoxCaseData",
			Handler:    _BlackBoxCaseDataService_ReorderBlackBoxCaseData_Handler,
		},
		{
			MethodName: "ListBlackBoxCaseDataByCaseIds",
			Handler:    _BlackBoxCaseDataService_ListBlackBoxCaseDataByCaseIds_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "ai/ai.proto",
}

const (
	BlackBoxCaseDirectoryService_ListBlackBoxCaseDirectories_FullMethodName = "/ai.BlackBoxCaseDirectoryService/ListBlackBoxCaseDirectories"
	BlackBoxCaseDirectoryService_CreateBlackBoxCaseDirectory_FullMethodName = "/ai.BlackBoxCaseDirectoryService/CreateBlackBoxCaseDirectory"
	BlackBoxCaseDirectoryService_DeleteBlackBoxCaseDirectory_FullMethodName = "/ai.BlackBoxCaseDirectoryService/DeleteBlackBoxCaseDirectory"
)

// BlackBoxCaseDirectoryServiceClient is the client API for BlackBoxCaseDirectoryService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// BlackBoxCaseDirectoryService
type BlackBoxCaseDirectoryServiceClient interface {
	// ListBlackBoxCaseDirectories list blackbox case directories
	ListBlackBoxCaseDirectories(ctx context.Context, in *ListBlackBoxCaseDirsReq, opts ...grpc.CallOption) (*ListBlackBoxCaseDirsResp, error)
	// CreateBlackBoxCaseDirectory create blackbox case directory
	CreateBlackBoxCaseDirectory(ctx context.Context, in *CreateBlackBoxCaseDirReq, opts ...grpc.CallOption) (*CreateBlackBoxCaseDirResp, error)
	// DeleteBlackBoxCaseDirectory delete blackbox case directory
	DeleteBlackBoxCaseDirectory(ctx context.Context, in *DeleteBlackBoxCaseDirReq, opts ...grpc.CallOption) (*DeleteBlackBoxCaseDirResp, error)
}

type blackBoxCaseDirectoryServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewBlackBoxCaseDirectoryServiceClient(cc grpc.ClientConnInterface) BlackBoxCaseDirectoryServiceClient {
	return &blackBoxCaseDirectoryServiceClient{cc}
}

func (c *blackBoxCaseDirectoryServiceClient) ListBlackBoxCaseDirectories(ctx context.Context, in *ListBlackBoxCaseDirsReq, opts ...grpc.CallOption) (*ListBlackBoxCaseDirsResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListBlackBoxCaseDirsResp)
	err := c.cc.Invoke(ctx, BlackBoxCaseDirectoryService_ListBlackBoxCaseDirectories_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *blackBoxCaseDirectoryServiceClient) CreateBlackBoxCaseDirectory(ctx context.Context, in *CreateBlackBoxCaseDirReq, opts ...grpc.CallOption) (*CreateBlackBoxCaseDirResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateBlackBoxCaseDirResp)
	err := c.cc.Invoke(ctx, BlackBoxCaseDirectoryService_CreateBlackBoxCaseDirectory_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *blackBoxCaseDirectoryServiceClient) DeleteBlackBoxCaseDirectory(ctx context.Context, in *DeleteBlackBoxCaseDirReq, opts ...grpc.CallOption) (*DeleteBlackBoxCaseDirResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DeleteBlackBoxCaseDirResp)
	err := c.cc.Invoke(ctx, BlackBoxCaseDirectoryService_DeleteBlackBoxCaseDirectory_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// BlackBoxCaseDirectoryServiceServer is the server API for BlackBoxCaseDirectoryService service.
// All implementations must embed UnimplementedBlackBoxCaseDirectoryServiceServer
// for forward compatibility.
//
// BlackBoxCaseDirectoryService
type BlackBoxCaseDirectoryServiceServer interface {
	// ListBlackBoxCaseDirectories list blackbox case directories
	ListBlackBoxCaseDirectories(context.Context, *ListBlackBoxCaseDirsReq) (*ListBlackBoxCaseDirsResp, error)
	// CreateBlackBoxCaseDirectory create blackbox case directory
	CreateBlackBoxCaseDirectory(context.Context, *CreateBlackBoxCaseDirReq) (*CreateBlackBoxCaseDirResp, error)
	// DeleteBlackBoxCaseDirectory delete blackbox case directory
	DeleteBlackBoxCaseDirectory(context.Context, *DeleteBlackBoxCaseDirReq) (*DeleteBlackBoxCaseDirResp, error)
	mustEmbedUnimplementedBlackBoxCaseDirectoryServiceServer()
}

// UnimplementedBlackBoxCaseDirectoryServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedBlackBoxCaseDirectoryServiceServer struct{}

func (UnimplementedBlackBoxCaseDirectoryServiceServer) ListBlackBoxCaseDirectories(context.Context, *ListBlackBoxCaseDirsReq) (*ListBlackBoxCaseDirsResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListBlackBoxCaseDirectories not implemented")
}
func (UnimplementedBlackBoxCaseDirectoryServiceServer) CreateBlackBoxCaseDirectory(context.Context, *CreateBlackBoxCaseDirReq) (*CreateBlackBoxCaseDirResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateBlackBoxCaseDirectory not implemented")
}
func (UnimplementedBlackBoxCaseDirectoryServiceServer) DeleteBlackBoxCaseDirectory(context.Context, *DeleteBlackBoxCaseDirReq) (*DeleteBlackBoxCaseDirResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteBlackBoxCaseDirectory not implemented")
}
func (UnimplementedBlackBoxCaseDirectoryServiceServer) mustEmbedUnimplementedBlackBoxCaseDirectoryServiceServer() {
}
func (UnimplementedBlackBoxCaseDirectoryServiceServer) testEmbeddedByValue() {}

// UnsafeBlackBoxCaseDirectoryServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to BlackBoxCaseDirectoryServiceServer will
// result in compilation errors.
type UnsafeBlackBoxCaseDirectoryServiceServer interface {
	mustEmbedUnimplementedBlackBoxCaseDirectoryServiceServer()
}

func RegisterBlackBoxCaseDirectoryServiceServer(s grpc.ServiceRegistrar, srv BlackBoxCaseDirectoryServiceServer) {
	// If the following call pancis, it indicates UnimplementedBlackBoxCaseDirectoryServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&BlackBoxCaseDirectoryService_ServiceDesc, srv)
}

func _BlackBoxCaseDirectoryService_ListBlackBoxCaseDirectories_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListBlackBoxCaseDirsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BlackBoxCaseDirectoryServiceServer).ListBlackBoxCaseDirectories(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BlackBoxCaseDirectoryService_ListBlackBoxCaseDirectories_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BlackBoxCaseDirectoryServiceServer).ListBlackBoxCaseDirectories(ctx, req.(*ListBlackBoxCaseDirsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BlackBoxCaseDirectoryService_CreateBlackBoxCaseDirectory_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateBlackBoxCaseDirReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BlackBoxCaseDirectoryServiceServer).CreateBlackBoxCaseDirectory(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BlackBoxCaseDirectoryService_CreateBlackBoxCaseDirectory_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BlackBoxCaseDirectoryServiceServer).CreateBlackBoxCaseDirectory(ctx, req.(*CreateBlackBoxCaseDirReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BlackBoxCaseDirectoryService_DeleteBlackBoxCaseDirectory_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteBlackBoxCaseDirReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BlackBoxCaseDirectoryServiceServer).DeleteBlackBoxCaseDirectory(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BlackBoxCaseDirectoryService_DeleteBlackBoxCaseDirectory_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BlackBoxCaseDirectoryServiceServer).DeleteBlackBoxCaseDirectory(ctx, req.(*DeleteBlackBoxCaseDirReq))
	}
	return interceptor(ctx, in, info, handler)
}

// BlackBoxCaseDirectoryService_ServiceDesc is the grpc.ServiceDesc for BlackBoxCaseDirectoryService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var BlackBoxCaseDirectoryService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "ai.BlackBoxCaseDirectoryService",
	HandlerType: (*BlackBoxCaseDirectoryServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ListBlackBoxCaseDirectories",
			Handler:    _BlackBoxCaseDirectoryService_ListBlackBoxCaseDirectories_Handler,
		},
		{
			MethodName: "CreateBlackBoxCaseDirectory",
			Handler:    _BlackBoxCaseDirectoryService_CreateBlackBoxCaseDirectory_Handler,
		},
		{
			MethodName: "DeleteBlackBoxCaseDirectory",
			Handler:    _BlackBoxCaseDirectoryService_DeleteBlackBoxCaseDirectory_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "ai/ai.proto",
}

const (
	BlackBoxCaseKnowledgeV2Service_GetBlackBoxCaseKnowledge_FullMethodName           = "/ai.BlackBoxCaseKnowledgeV2Service/GetBlackBoxCaseKnowledge"
	BlackBoxCaseKnowledgeV2Service_UpdateBlackBoxCaseKnowledge_FullMethodName        = "/ai.BlackBoxCaseKnowledgeV2Service/UpdateBlackBoxCaseKnowledge"
	BlackBoxCaseKnowledgeV2Service_UpdateBlackBoxCaseMapUsedKnowledge_FullMethodName = "/ai.BlackBoxCaseKnowledgeV2Service/UpdateBlackBoxCaseMapUsedKnowledge"
)

// BlackBoxCaseKnowledgeV2ServiceClient is the client API for BlackBoxCaseKnowledgeV2Service service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// BlackBoxCaseKnowledgeV2Service
type BlackBoxCaseKnowledgeV2ServiceClient interface {
	// GetBlackBoxCaseKnowledge get blackbox case knowledge
	GetBlackBoxCaseKnowledge(ctx context.Context, in *GetBlackBoxCaseKnowledgeV2Req, opts ...grpc.CallOption) (*GetBlackBoxCaseKnowledgeV2Resp, error)
	// UpdateBlackBoxCaseKnowledge update blackbox case knowledge
	UpdateBlackBoxCaseKnowledge(ctx context.Context, in *UpdateBlackBoxCaseKnowledgeV2Req, opts ...grpc.CallOption) (*UpdateBlackBoxCaseKnowledgeV2Resp, error)
	// UpdateBlackBoxCaseMapUsedKnowledge update blackbox case knowledge
	UpdateBlackBoxCaseMapUsedKnowledge(ctx context.Context, in *UpdateBlackBoxCaseMapUsedKnowledgeReq, opts ...grpc.CallOption) (*UpdateBlackBoxCaseMapUsedKnowledgeResp, error)
}

type blackBoxCaseKnowledgeV2ServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewBlackBoxCaseKnowledgeV2ServiceClient(cc grpc.ClientConnInterface) BlackBoxCaseKnowledgeV2ServiceClient {
	return &blackBoxCaseKnowledgeV2ServiceClient{cc}
}

func (c *blackBoxCaseKnowledgeV2ServiceClient) GetBlackBoxCaseKnowledge(ctx context.Context, in *GetBlackBoxCaseKnowledgeV2Req, opts ...grpc.CallOption) (*GetBlackBoxCaseKnowledgeV2Resp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetBlackBoxCaseKnowledgeV2Resp)
	err := c.cc.Invoke(ctx, BlackBoxCaseKnowledgeV2Service_GetBlackBoxCaseKnowledge_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *blackBoxCaseKnowledgeV2ServiceClient) UpdateBlackBoxCaseKnowledge(ctx context.Context, in *UpdateBlackBoxCaseKnowledgeV2Req, opts ...grpc.CallOption) (*UpdateBlackBoxCaseKnowledgeV2Resp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UpdateBlackBoxCaseKnowledgeV2Resp)
	err := c.cc.Invoke(ctx, BlackBoxCaseKnowledgeV2Service_UpdateBlackBoxCaseKnowledge_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *blackBoxCaseKnowledgeV2ServiceClient) UpdateBlackBoxCaseMapUsedKnowledge(ctx context.Context, in *UpdateBlackBoxCaseMapUsedKnowledgeReq, opts ...grpc.CallOption) (*UpdateBlackBoxCaseMapUsedKnowledgeResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UpdateBlackBoxCaseMapUsedKnowledgeResp)
	err := c.cc.Invoke(ctx, BlackBoxCaseKnowledgeV2Service_UpdateBlackBoxCaseMapUsedKnowledge_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// BlackBoxCaseKnowledgeV2ServiceServer is the server API for BlackBoxCaseKnowledgeV2Service service.
// All implementations must embed UnimplementedBlackBoxCaseKnowledgeV2ServiceServer
// for forward compatibility.
//
// BlackBoxCaseKnowledgeV2Service
type BlackBoxCaseKnowledgeV2ServiceServer interface {
	// GetBlackBoxCaseKnowledge get blackbox case knowledge
	GetBlackBoxCaseKnowledge(context.Context, *GetBlackBoxCaseKnowledgeV2Req) (*GetBlackBoxCaseKnowledgeV2Resp, error)
	// UpdateBlackBoxCaseKnowledge update blackbox case knowledge
	UpdateBlackBoxCaseKnowledge(context.Context, *UpdateBlackBoxCaseKnowledgeV2Req) (*UpdateBlackBoxCaseKnowledgeV2Resp, error)
	// UpdateBlackBoxCaseMapUsedKnowledge update blackbox case knowledge
	UpdateBlackBoxCaseMapUsedKnowledge(context.Context, *UpdateBlackBoxCaseMapUsedKnowledgeReq) (*UpdateBlackBoxCaseMapUsedKnowledgeResp, error)
	mustEmbedUnimplementedBlackBoxCaseKnowledgeV2ServiceServer()
}

// UnimplementedBlackBoxCaseKnowledgeV2ServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedBlackBoxCaseKnowledgeV2ServiceServer struct{}

func (UnimplementedBlackBoxCaseKnowledgeV2ServiceServer) GetBlackBoxCaseKnowledge(context.Context, *GetBlackBoxCaseKnowledgeV2Req) (*GetBlackBoxCaseKnowledgeV2Resp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetBlackBoxCaseKnowledge not implemented")
}
func (UnimplementedBlackBoxCaseKnowledgeV2ServiceServer) UpdateBlackBoxCaseKnowledge(context.Context, *UpdateBlackBoxCaseKnowledgeV2Req) (*UpdateBlackBoxCaseKnowledgeV2Resp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateBlackBoxCaseKnowledge not implemented")
}
func (UnimplementedBlackBoxCaseKnowledgeV2ServiceServer) UpdateBlackBoxCaseMapUsedKnowledge(context.Context, *UpdateBlackBoxCaseMapUsedKnowledgeReq) (*UpdateBlackBoxCaseMapUsedKnowledgeResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateBlackBoxCaseMapUsedKnowledge not implemented")
}
func (UnimplementedBlackBoxCaseKnowledgeV2ServiceServer) mustEmbedUnimplementedBlackBoxCaseKnowledgeV2ServiceServer() {
}
func (UnimplementedBlackBoxCaseKnowledgeV2ServiceServer) testEmbeddedByValue() {}

// UnsafeBlackBoxCaseKnowledgeV2ServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to BlackBoxCaseKnowledgeV2ServiceServer will
// result in compilation errors.
type UnsafeBlackBoxCaseKnowledgeV2ServiceServer interface {
	mustEmbedUnimplementedBlackBoxCaseKnowledgeV2ServiceServer()
}

func RegisterBlackBoxCaseKnowledgeV2ServiceServer(s grpc.ServiceRegistrar, srv BlackBoxCaseKnowledgeV2ServiceServer) {
	// If the following call pancis, it indicates UnimplementedBlackBoxCaseKnowledgeV2ServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&BlackBoxCaseKnowledgeV2Service_ServiceDesc, srv)
}

func _BlackBoxCaseKnowledgeV2Service_GetBlackBoxCaseKnowledge_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetBlackBoxCaseKnowledgeV2Req)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BlackBoxCaseKnowledgeV2ServiceServer).GetBlackBoxCaseKnowledge(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BlackBoxCaseKnowledgeV2Service_GetBlackBoxCaseKnowledge_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BlackBoxCaseKnowledgeV2ServiceServer).GetBlackBoxCaseKnowledge(ctx, req.(*GetBlackBoxCaseKnowledgeV2Req))
	}
	return interceptor(ctx, in, info, handler)
}

func _BlackBoxCaseKnowledgeV2Service_UpdateBlackBoxCaseKnowledge_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateBlackBoxCaseKnowledgeV2Req)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BlackBoxCaseKnowledgeV2ServiceServer).UpdateBlackBoxCaseKnowledge(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BlackBoxCaseKnowledgeV2Service_UpdateBlackBoxCaseKnowledge_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BlackBoxCaseKnowledgeV2ServiceServer).UpdateBlackBoxCaseKnowledge(ctx, req.(*UpdateBlackBoxCaseKnowledgeV2Req))
	}
	return interceptor(ctx, in, info, handler)
}

func _BlackBoxCaseKnowledgeV2Service_UpdateBlackBoxCaseMapUsedKnowledge_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateBlackBoxCaseMapUsedKnowledgeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BlackBoxCaseKnowledgeV2ServiceServer).UpdateBlackBoxCaseMapUsedKnowledge(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BlackBoxCaseKnowledgeV2Service_UpdateBlackBoxCaseMapUsedKnowledge_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BlackBoxCaseKnowledgeV2ServiceServer).UpdateBlackBoxCaseMapUsedKnowledge(ctx, req.(*UpdateBlackBoxCaseMapUsedKnowledgeReq))
	}
	return interceptor(ctx, in, info, handler)
}

// BlackBoxCaseKnowledgeV2Service_ServiceDesc is the grpc.ServiceDesc for BlackBoxCaseKnowledgeV2Service service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var BlackBoxCaseKnowledgeV2Service_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "ai.BlackBoxCaseKnowledgeV2Service",
	HandlerType: (*BlackBoxCaseKnowledgeV2ServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetBlackBoxCaseKnowledge",
			Handler:    _BlackBoxCaseKnowledgeV2Service_GetBlackBoxCaseKnowledge_Handler,
		},
		{
			MethodName: "UpdateBlackBoxCaseKnowledge",
			Handler:    _BlackBoxCaseKnowledgeV2Service_UpdateBlackBoxCaseKnowledge_Handler,
		},
		{
			MethodName: "UpdateBlackBoxCaseMapUsedKnowledge",
			Handler:    _BlackBoxCaseKnowledgeV2Service_UpdateBlackBoxCaseMapUsedKnowledge_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "ai/ai.proto",
}

const (
	BlackBoxCaseMapService_GetBlackBoxCaseMap_FullMethodName         = "/ai.BlackBoxCaseMapService/GetBlackBoxCaseMap"
	BlackBoxCaseMapService_UpdateBlackBoxCaseMap_FullMethodName      = "/ai.BlackBoxCaseMapService/UpdateBlackBoxCaseMap"
	BlackBoxCaseMapService_CreateBlackBoxCaseMap_FullMethodName      = "/ai.BlackBoxCaseMapService/CreateBlackBoxCaseMap"
	BlackBoxCaseMapService_GetCompleteBlackBoxCaseMap_FullMethodName = "/ai.BlackBoxCaseMapService/GetCompleteBlackBoxCaseMap"
	BlackBoxCaseMapService_CreateBlackBoxCaseMapId_FullMethodName    = "/ai.BlackBoxCaseMapService/CreateBlackBoxCaseMapId"
)

// BlackBoxCaseMapServiceClient is the client API for BlackBoxCaseMapService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// BlackBoxCaseMapService
type BlackBoxCaseMapServiceClient interface {
	// GetBlackBoxCaseMap get blackbox case map
	GetBlackBoxCaseMap(ctx context.Context, in *GetBlackBoxCaseMapReq, opts ...grpc.CallOption) (*GetBlackBoxCaseMapResp, error)
	// UpdateBlackBoxCaseMap update blackbox case map
	UpdateBlackBoxCaseMap(ctx context.Context, in *UpdateBlackBoxCaseMapReq, opts ...grpc.CallOption) (*UpdateBlackBoxCaseMapResp, error)
	// CreateBlackBoxCaseMap create blackbox case map
	CreateBlackBoxCaseMap(ctx context.Context, in *CreateBlackBoxCaseMapReq, opts ...grpc.CallOption) (*CreateBlackBoxCaseMapResp, error)
	// GetCompleteBlackBoxCaseMap get complete blackbox case map
	GetCompleteBlackBoxCaseMap(ctx context.Context, in *GetCompleteBlackBoxCaseMapReq, opts ...grpc.CallOption) (*GetCompleteBlackBoxCaseMapResp, error)
	// CreateBlackBoxCaseMapId create blackbox case map_id
	CreateBlackBoxCaseMapId(ctx context.Context, in *CreateBlackBoxCaseMapIdReq, opts ...grpc.CallOption) (*CreateBlackBoxCaseMapIdResp, error)
}

type blackBoxCaseMapServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewBlackBoxCaseMapServiceClient(cc grpc.ClientConnInterface) BlackBoxCaseMapServiceClient {
	return &blackBoxCaseMapServiceClient{cc}
}

func (c *blackBoxCaseMapServiceClient) GetBlackBoxCaseMap(ctx context.Context, in *GetBlackBoxCaseMapReq, opts ...grpc.CallOption) (*GetBlackBoxCaseMapResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetBlackBoxCaseMapResp)
	err := c.cc.Invoke(ctx, BlackBoxCaseMapService_GetBlackBoxCaseMap_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *blackBoxCaseMapServiceClient) UpdateBlackBoxCaseMap(ctx context.Context, in *UpdateBlackBoxCaseMapReq, opts ...grpc.CallOption) (*UpdateBlackBoxCaseMapResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UpdateBlackBoxCaseMapResp)
	err := c.cc.Invoke(ctx, BlackBoxCaseMapService_UpdateBlackBoxCaseMap_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *blackBoxCaseMapServiceClient) CreateBlackBoxCaseMap(ctx context.Context, in *CreateBlackBoxCaseMapReq, opts ...grpc.CallOption) (*CreateBlackBoxCaseMapResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateBlackBoxCaseMapResp)
	err := c.cc.Invoke(ctx, BlackBoxCaseMapService_CreateBlackBoxCaseMap_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *blackBoxCaseMapServiceClient) GetCompleteBlackBoxCaseMap(ctx context.Context, in *GetCompleteBlackBoxCaseMapReq, opts ...grpc.CallOption) (*GetCompleteBlackBoxCaseMapResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetCompleteBlackBoxCaseMapResp)
	err := c.cc.Invoke(ctx, BlackBoxCaseMapService_GetCompleteBlackBoxCaseMap_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *blackBoxCaseMapServiceClient) CreateBlackBoxCaseMapId(ctx context.Context, in *CreateBlackBoxCaseMapIdReq, opts ...grpc.CallOption) (*CreateBlackBoxCaseMapIdResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateBlackBoxCaseMapIdResp)
	err := c.cc.Invoke(ctx, BlackBoxCaseMapService_CreateBlackBoxCaseMapId_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// BlackBoxCaseMapServiceServer is the server API for BlackBoxCaseMapService service.
// All implementations must embed UnimplementedBlackBoxCaseMapServiceServer
// for forward compatibility.
//
// BlackBoxCaseMapService
type BlackBoxCaseMapServiceServer interface {
	// GetBlackBoxCaseMap get blackbox case map
	GetBlackBoxCaseMap(context.Context, *GetBlackBoxCaseMapReq) (*GetBlackBoxCaseMapResp, error)
	// UpdateBlackBoxCaseMap update blackbox case map
	UpdateBlackBoxCaseMap(context.Context, *UpdateBlackBoxCaseMapReq) (*UpdateBlackBoxCaseMapResp, error)
	// CreateBlackBoxCaseMap create blackbox case map
	CreateBlackBoxCaseMap(context.Context, *CreateBlackBoxCaseMapReq) (*CreateBlackBoxCaseMapResp, error)
	// GetCompleteBlackBoxCaseMap get complete blackbox case map
	GetCompleteBlackBoxCaseMap(context.Context, *GetCompleteBlackBoxCaseMapReq) (*GetCompleteBlackBoxCaseMapResp, error)
	// CreateBlackBoxCaseMapId create blackbox case map_id
	CreateBlackBoxCaseMapId(context.Context, *CreateBlackBoxCaseMapIdReq) (*CreateBlackBoxCaseMapIdResp, error)
	mustEmbedUnimplementedBlackBoxCaseMapServiceServer()
}

// UnimplementedBlackBoxCaseMapServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedBlackBoxCaseMapServiceServer struct{}

func (UnimplementedBlackBoxCaseMapServiceServer) GetBlackBoxCaseMap(context.Context, *GetBlackBoxCaseMapReq) (*GetBlackBoxCaseMapResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetBlackBoxCaseMap not implemented")
}
func (UnimplementedBlackBoxCaseMapServiceServer) UpdateBlackBoxCaseMap(context.Context, *UpdateBlackBoxCaseMapReq) (*UpdateBlackBoxCaseMapResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateBlackBoxCaseMap not implemented")
}
func (UnimplementedBlackBoxCaseMapServiceServer) CreateBlackBoxCaseMap(context.Context, *CreateBlackBoxCaseMapReq) (*CreateBlackBoxCaseMapResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateBlackBoxCaseMap not implemented")
}
func (UnimplementedBlackBoxCaseMapServiceServer) GetCompleteBlackBoxCaseMap(context.Context, *GetCompleteBlackBoxCaseMapReq) (*GetCompleteBlackBoxCaseMapResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCompleteBlackBoxCaseMap not implemented")
}
func (UnimplementedBlackBoxCaseMapServiceServer) CreateBlackBoxCaseMapId(context.Context, *CreateBlackBoxCaseMapIdReq) (*CreateBlackBoxCaseMapIdResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateBlackBoxCaseMapId not implemented")
}
func (UnimplementedBlackBoxCaseMapServiceServer) mustEmbedUnimplementedBlackBoxCaseMapServiceServer() {
}
func (UnimplementedBlackBoxCaseMapServiceServer) testEmbeddedByValue() {}

// UnsafeBlackBoxCaseMapServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to BlackBoxCaseMapServiceServer will
// result in compilation errors.
type UnsafeBlackBoxCaseMapServiceServer interface {
	mustEmbedUnimplementedBlackBoxCaseMapServiceServer()
}

func RegisterBlackBoxCaseMapServiceServer(s grpc.ServiceRegistrar, srv BlackBoxCaseMapServiceServer) {
	// If the following call pancis, it indicates UnimplementedBlackBoxCaseMapServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&BlackBoxCaseMapService_ServiceDesc, srv)
}

func _BlackBoxCaseMapService_GetBlackBoxCaseMap_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetBlackBoxCaseMapReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BlackBoxCaseMapServiceServer).GetBlackBoxCaseMap(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BlackBoxCaseMapService_GetBlackBoxCaseMap_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BlackBoxCaseMapServiceServer).GetBlackBoxCaseMap(ctx, req.(*GetBlackBoxCaseMapReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BlackBoxCaseMapService_UpdateBlackBoxCaseMap_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateBlackBoxCaseMapReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BlackBoxCaseMapServiceServer).UpdateBlackBoxCaseMap(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BlackBoxCaseMapService_UpdateBlackBoxCaseMap_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BlackBoxCaseMapServiceServer).UpdateBlackBoxCaseMap(ctx, req.(*UpdateBlackBoxCaseMapReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BlackBoxCaseMapService_CreateBlackBoxCaseMap_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateBlackBoxCaseMapReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BlackBoxCaseMapServiceServer).CreateBlackBoxCaseMap(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BlackBoxCaseMapService_CreateBlackBoxCaseMap_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BlackBoxCaseMapServiceServer).CreateBlackBoxCaseMap(ctx, req.(*CreateBlackBoxCaseMapReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BlackBoxCaseMapService_GetCompleteBlackBoxCaseMap_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCompleteBlackBoxCaseMapReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BlackBoxCaseMapServiceServer).GetCompleteBlackBoxCaseMap(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BlackBoxCaseMapService_GetCompleteBlackBoxCaseMap_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BlackBoxCaseMapServiceServer).GetCompleteBlackBoxCaseMap(ctx, req.(*GetCompleteBlackBoxCaseMapReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BlackBoxCaseMapService_CreateBlackBoxCaseMapId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateBlackBoxCaseMapIdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BlackBoxCaseMapServiceServer).CreateBlackBoxCaseMapId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BlackBoxCaseMapService_CreateBlackBoxCaseMapId_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BlackBoxCaseMapServiceServer).CreateBlackBoxCaseMapId(ctx, req.(*CreateBlackBoxCaseMapIdReq))
	}
	return interceptor(ctx, in, info, handler)
}

// BlackBoxCaseMapService_ServiceDesc is the grpc.ServiceDesc for BlackBoxCaseMapService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var BlackBoxCaseMapService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "ai.BlackBoxCaseMapService",
	HandlerType: (*BlackBoxCaseMapServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetBlackBoxCaseMap",
			Handler:    _BlackBoxCaseMapService_GetBlackBoxCaseMap_Handler,
		},
		{
			MethodName: "UpdateBlackBoxCaseMap",
			Handler:    _BlackBoxCaseMapService_UpdateBlackBoxCaseMap_Handler,
		},
		{
			MethodName: "CreateBlackBoxCaseMap",
			Handler:    _BlackBoxCaseMapService_CreateBlackBoxCaseMap_Handler,
		},
		{
			MethodName: "GetCompleteBlackBoxCaseMap",
			Handler:    _BlackBoxCaseMapService_GetCompleteBlackBoxCaseMap_Handler,
		},
		{
			MethodName: "CreateBlackBoxCaseMapId",
			Handler:    _BlackBoxCaseMapService_CreateBlackBoxCaseMapId_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "ai/ai.proto",
}

const (
	BlackBoxCaseMapDocumentService_ListBlackBoxCaseMapDocuments_FullMethodName        = "/ai.BlackBoxCaseMapDocumentService/ListBlackBoxCaseMapDocuments"
	BlackBoxCaseMapDocumentService_GetBlackBoxCaseMapDocument_FullMethodName          = "/ai.BlackBoxCaseMapDocumentService/GetBlackBoxCaseMapDocument"
	BlackBoxCaseMapDocumentService_UpdateBlackBoxCaseMapDocument_FullMethodName       = "/ai.BlackBoxCaseMapDocumentService/UpdateBlackBoxCaseMapDocument"
	BlackBoxCaseMapDocumentService_DeleteBlackBoxCaseMapDocument_FullMethodName       = "/ai.BlackBoxCaseMapDocumentService/DeleteBlackBoxCaseMapDocument"
	BlackBoxCaseMapDocumentService_UpdateBlackBoxCaseMapRecallDocument_FullMethodName = "/ai.BlackBoxCaseMapDocumentService/UpdateBlackBoxCaseMapRecallDocument"
)

// BlackBoxCaseMapDocumentServiceClient is the client API for BlackBoxCaseMapDocumentService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// BlackBoxCaseMapDocumentService
type BlackBoxCaseMapDocumentServiceClient interface {
	// ListBlackBoxCaseMapDocuments list blackbox case map documents
	ListBlackBoxCaseMapDocuments(ctx context.Context, in *ListBlackBoxCaseMapDocumentsReq, opts ...grpc.CallOption) (*ListBlackBoxCaseMapDocumentsResp, error)
	// GetBlackBoxCaseMapDocument get blackbox case map document
	GetBlackBoxCaseMapDocument(ctx context.Context, in *GetBlackBoxCaseMapDocumentReq, opts ...grpc.CallOption) (*BlackBoxCaseMapDocument, error)
	// UpdateBlackBoxCaseMapDocument update blackbox case map document
	UpdateBlackBoxCaseMapDocument(ctx context.Context, in *BlackBoxCaseMapDocument, opts ...grpc.CallOption) (*UpdateBlackBoxCaseMapDocumentResp, error)
	// DeleteBlackBoxCaseMapDocument delete blackbox case map document
	DeleteBlackBoxCaseMapDocument(ctx context.Context, in *DeleteBlackBoxCaseMapDocumentReq, opts ...grpc.CallOption) (*DeleteBlackBoxCaseMapDocumentResp, error)
	// UpdateBlackBoxCaseMapRecallDocument update blackbox case map document
	UpdateBlackBoxCaseMapRecallDocument(ctx context.Context, in *UpdateBlackBoxCaseMapRecallDocumentReq, opts ...grpc.CallOption) (*UpdateBlackBoxCaseMapRecallDocumentResp, error)
}

type blackBoxCaseMapDocumentServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewBlackBoxCaseMapDocumentServiceClient(cc grpc.ClientConnInterface) BlackBoxCaseMapDocumentServiceClient {
	return &blackBoxCaseMapDocumentServiceClient{cc}
}

func (c *blackBoxCaseMapDocumentServiceClient) ListBlackBoxCaseMapDocuments(ctx context.Context, in *ListBlackBoxCaseMapDocumentsReq, opts ...grpc.CallOption) (*ListBlackBoxCaseMapDocumentsResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListBlackBoxCaseMapDocumentsResp)
	err := c.cc.Invoke(ctx, BlackBoxCaseMapDocumentService_ListBlackBoxCaseMapDocuments_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *blackBoxCaseMapDocumentServiceClient) GetBlackBoxCaseMapDocument(ctx context.Context, in *GetBlackBoxCaseMapDocumentReq, opts ...grpc.CallOption) (*BlackBoxCaseMapDocument, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(BlackBoxCaseMapDocument)
	err := c.cc.Invoke(ctx, BlackBoxCaseMapDocumentService_GetBlackBoxCaseMapDocument_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *blackBoxCaseMapDocumentServiceClient) UpdateBlackBoxCaseMapDocument(ctx context.Context, in *BlackBoxCaseMapDocument, opts ...grpc.CallOption) (*UpdateBlackBoxCaseMapDocumentResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UpdateBlackBoxCaseMapDocumentResp)
	err := c.cc.Invoke(ctx, BlackBoxCaseMapDocumentService_UpdateBlackBoxCaseMapDocument_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *blackBoxCaseMapDocumentServiceClient) DeleteBlackBoxCaseMapDocument(ctx context.Context, in *DeleteBlackBoxCaseMapDocumentReq, opts ...grpc.CallOption) (*DeleteBlackBoxCaseMapDocumentResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DeleteBlackBoxCaseMapDocumentResp)
	err := c.cc.Invoke(ctx, BlackBoxCaseMapDocumentService_DeleteBlackBoxCaseMapDocument_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *blackBoxCaseMapDocumentServiceClient) UpdateBlackBoxCaseMapRecallDocument(ctx context.Context, in *UpdateBlackBoxCaseMapRecallDocumentReq, opts ...grpc.CallOption) (*UpdateBlackBoxCaseMapRecallDocumentResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UpdateBlackBoxCaseMapRecallDocumentResp)
	err := c.cc.Invoke(ctx, BlackBoxCaseMapDocumentService_UpdateBlackBoxCaseMapRecallDocument_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// BlackBoxCaseMapDocumentServiceServer is the server API for BlackBoxCaseMapDocumentService service.
// All implementations must embed UnimplementedBlackBoxCaseMapDocumentServiceServer
// for forward compatibility.
//
// BlackBoxCaseMapDocumentService
type BlackBoxCaseMapDocumentServiceServer interface {
	// ListBlackBoxCaseMapDocuments list blackbox case map documents
	ListBlackBoxCaseMapDocuments(context.Context, *ListBlackBoxCaseMapDocumentsReq) (*ListBlackBoxCaseMapDocumentsResp, error)
	// GetBlackBoxCaseMapDocument get blackbox case map document
	GetBlackBoxCaseMapDocument(context.Context, *GetBlackBoxCaseMapDocumentReq) (*BlackBoxCaseMapDocument, error)
	// UpdateBlackBoxCaseMapDocument update blackbox case map document
	UpdateBlackBoxCaseMapDocument(context.Context, *BlackBoxCaseMapDocument) (*UpdateBlackBoxCaseMapDocumentResp, error)
	// DeleteBlackBoxCaseMapDocument delete blackbox case map document
	DeleteBlackBoxCaseMapDocument(context.Context, *DeleteBlackBoxCaseMapDocumentReq) (*DeleteBlackBoxCaseMapDocumentResp, error)
	// UpdateBlackBoxCaseMapRecallDocument update blackbox case map document
	UpdateBlackBoxCaseMapRecallDocument(context.Context, *UpdateBlackBoxCaseMapRecallDocumentReq) (*UpdateBlackBoxCaseMapRecallDocumentResp, error)
	mustEmbedUnimplementedBlackBoxCaseMapDocumentServiceServer()
}

// UnimplementedBlackBoxCaseMapDocumentServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedBlackBoxCaseMapDocumentServiceServer struct{}

func (UnimplementedBlackBoxCaseMapDocumentServiceServer) ListBlackBoxCaseMapDocuments(context.Context, *ListBlackBoxCaseMapDocumentsReq) (*ListBlackBoxCaseMapDocumentsResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListBlackBoxCaseMapDocuments not implemented")
}
func (UnimplementedBlackBoxCaseMapDocumentServiceServer) GetBlackBoxCaseMapDocument(context.Context, *GetBlackBoxCaseMapDocumentReq) (*BlackBoxCaseMapDocument, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetBlackBoxCaseMapDocument not implemented")
}
func (UnimplementedBlackBoxCaseMapDocumentServiceServer) UpdateBlackBoxCaseMapDocument(context.Context, *BlackBoxCaseMapDocument) (*UpdateBlackBoxCaseMapDocumentResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateBlackBoxCaseMapDocument not implemented")
}
func (UnimplementedBlackBoxCaseMapDocumentServiceServer) DeleteBlackBoxCaseMapDocument(context.Context, *DeleteBlackBoxCaseMapDocumentReq) (*DeleteBlackBoxCaseMapDocumentResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteBlackBoxCaseMapDocument not implemented")
}
func (UnimplementedBlackBoxCaseMapDocumentServiceServer) UpdateBlackBoxCaseMapRecallDocument(context.Context, *UpdateBlackBoxCaseMapRecallDocumentReq) (*UpdateBlackBoxCaseMapRecallDocumentResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateBlackBoxCaseMapRecallDocument not implemented")
}
func (UnimplementedBlackBoxCaseMapDocumentServiceServer) mustEmbedUnimplementedBlackBoxCaseMapDocumentServiceServer() {
}
func (UnimplementedBlackBoxCaseMapDocumentServiceServer) testEmbeddedByValue() {}

// UnsafeBlackBoxCaseMapDocumentServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to BlackBoxCaseMapDocumentServiceServer will
// result in compilation errors.
type UnsafeBlackBoxCaseMapDocumentServiceServer interface {
	mustEmbedUnimplementedBlackBoxCaseMapDocumentServiceServer()
}

func RegisterBlackBoxCaseMapDocumentServiceServer(s grpc.ServiceRegistrar, srv BlackBoxCaseMapDocumentServiceServer) {
	// If the following call pancis, it indicates UnimplementedBlackBoxCaseMapDocumentServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&BlackBoxCaseMapDocumentService_ServiceDesc, srv)
}

func _BlackBoxCaseMapDocumentService_ListBlackBoxCaseMapDocuments_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListBlackBoxCaseMapDocumentsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BlackBoxCaseMapDocumentServiceServer).ListBlackBoxCaseMapDocuments(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BlackBoxCaseMapDocumentService_ListBlackBoxCaseMapDocuments_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BlackBoxCaseMapDocumentServiceServer).ListBlackBoxCaseMapDocuments(ctx, req.(*ListBlackBoxCaseMapDocumentsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BlackBoxCaseMapDocumentService_GetBlackBoxCaseMapDocument_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetBlackBoxCaseMapDocumentReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BlackBoxCaseMapDocumentServiceServer).GetBlackBoxCaseMapDocument(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BlackBoxCaseMapDocumentService_GetBlackBoxCaseMapDocument_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BlackBoxCaseMapDocumentServiceServer).GetBlackBoxCaseMapDocument(ctx, req.(*GetBlackBoxCaseMapDocumentReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BlackBoxCaseMapDocumentService_UpdateBlackBoxCaseMapDocument_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BlackBoxCaseMapDocument)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BlackBoxCaseMapDocumentServiceServer).UpdateBlackBoxCaseMapDocument(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BlackBoxCaseMapDocumentService_UpdateBlackBoxCaseMapDocument_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BlackBoxCaseMapDocumentServiceServer).UpdateBlackBoxCaseMapDocument(ctx, req.(*BlackBoxCaseMapDocument))
	}
	return interceptor(ctx, in, info, handler)
}

func _BlackBoxCaseMapDocumentService_DeleteBlackBoxCaseMapDocument_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteBlackBoxCaseMapDocumentReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BlackBoxCaseMapDocumentServiceServer).DeleteBlackBoxCaseMapDocument(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BlackBoxCaseMapDocumentService_DeleteBlackBoxCaseMapDocument_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BlackBoxCaseMapDocumentServiceServer).DeleteBlackBoxCaseMapDocument(ctx, req.(*DeleteBlackBoxCaseMapDocumentReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BlackBoxCaseMapDocumentService_UpdateBlackBoxCaseMapRecallDocument_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateBlackBoxCaseMapRecallDocumentReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BlackBoxCaseMapDocumentServiceServer).UpdateBlackBoxCaseMapRecallDocument(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BlackBoxCaseMapDocumentService_UpdateBlackBoxCaseMapRecallDocument_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BlackBoxCaseMapDocumentServiceServer).UpdateBlackBoxCaseMapRecallDocument(ctx, req.(*UpdateBlackBoxCaseMapRecallDocumentReq))
	}
	return interceptor(ctx, in, info, handler)
}

// BlackBoxCaseMapDocumentService_ServiceDesc is the grpc.ServiceDesc for BlackBoxCaseMapDocumentService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var BlackBoxCaseMapDocumentService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "ai.BlackBoxCaseMapDocumentService",
	HandlerType: (*BlackBoxCaseMapDocumentServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ListBlackBoxCaseMapDocuments",
			Handler:    _BlackBoxCaseMapDocumentService_ListBlackBoxCaseMapDocuments_Handler,
		},
		{
			MethodName: "GetBlackBoxCaseMapDocument",
			Handler:    _BlackBoxCaseMapDocumentService_GetBlackBoxCaseMapDocument_Handler,
		},
		{
			MethodName: "UpdateBlackBoxCaseMapDocument",
			Handler:    _BlackBoxCaseMapDocumentService_UpdateBlackBoxCaseMapDocument_Handler,
		},
		{
			MethodName: "DeleteBlackBoxCaseMapDocument",
			Handler:    _BlackBoxCaseMapDocumentService_DeleteBlackBoxCaseMapDocument_Handler,
		},
		{
			MethodName: "UpdateBlackBoxCaseMapRecallDocument",
			Handler:    _BlackBoxCaseMapDocumentService_UpdateBlackBoxCaseMapRecallDocument_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "ai/ai.proto",
}

const (
	BlackBoxGenerationRecordService_AddBlackBoxFuncCount_FullMethodName                = "/ai.BlackBoxGenerationRecordService/AddBlackBoxFuncCount"
	BlackBoxGenerationRecordService_AddBlackBoxTestSceneCount_FullMethodName           = "/ai.BlackBoxGenerationRecordService/AddBlackBoxTestSceneCount"
	BlackBoxGenerationRecordService_AddBlackBoxSupplementDocumentCount_FullMethodName  = "/ai.BlackBoxGenerationRecordService/AddBlackBoxSupplementDocumentCount"
	BlackBoxGenerationRecordService_AddBlackBoxBaseMapCount_FullMethodName             = "/ai.BlackBoxGenerationRecordService/AddBlackBoxBaseMapCount"
	BlackBoxGenerationRecordService_AddBlackBoxUpdatedAddCaseCount_FullMethodName      = "/ai.BlackBoxGenerationRecordService/AddBlackBoxUpdatedAddCaseCount"
	BlackBoxGenerationRecordService_AddBlackBoxCaseNameUpdatedCount_FullMethodName     = "/ai.BlackBoxGenerationRecordService/AddBlackBoxCaseNameUpdatedCount"
	BlackBoxGenerationRecordService_AddBlackBoxPreConditionUpdatedCount_FullMethodName = "/ai.BlackBoxGenerationRecordService/AddBlackBoxPreConditionUpdatedCount"
	BlackBoxGenerationRecordService_AddBlackBoxCaseStepUpdatedCount_FullMethodName     = "/ai.BlackBoxGenerationRecordService/AddBlackBoxCaseStepUpdatedCount"
	BlackBoxGenerationRecordService_AddBlackBoxExpectResultUpdatedCount_FullMethodName = "/ai.BlackBoxGenerationRecordService/AddBlackBoxExpectResultUpdatedCount"
	BlackBoxGenerationRecordService_AddBlackBoxCaseLevelUpdatedCount_FullMethodName    = "/ai.BlackBoxGenerationRecordService/AddBlackBoxCaseLevelUpdatedCount"
)

// BlackBoxGenerationRecordServiceClient is the client API for BlackBoxGenerationRecordService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type BlackBoxGenerationRecordServiceClient interface {
	// AddBlackBoxFuncCount add blackbox func count
	AddBlackBoxFuncCount(ctx context.Context, in *AddBlackBoxFuncCountReq, opts ...grpc.CallOption) (*AddBlackBoxFuncCountResp, error)
	// AddBlackBoxTestSceneCount add blackbox test scene count
	AddBlackBoxTestSceneCount(ctx context.Context, in *AddBlackBoxTestSceneCountReq, opts ...grpc.CallOption) (*AddBlackBoxTestSceneCountResp, error)
	// AddBlackBoxSupplementDocumentCount add blackbox supplement document count
	AddBlackBoxSupplementDocumentCount(ctx context.Context, in *AddBlackBoxSupplementDocumentCountReq, opts ...grpc.CallOption) (*AddBlackBoxSupplementDocumentCountResp, error)
	// AddBlackBoxBaseMapCount add blackbox base map count
	AddBlackBoxBaseMapCount(ctx context.Context, in *AddBlackBoxBaseMapCountReq, opts ...grpc.CallOption) (*AddBlackBoxBaseMapCountResp, error)
	// AddBlackBoxUpdatedAddCaseCount add blackbox updated add case count
	AddBlackBoxUpdatedAddCaseCount(ctx context.Context, in *AddBlackBoxUpdatedAddCaseCountReq, opts ...grpc.CallOption) (*AddBlackBoxUpdatedAddCaseCountResp, error)
	// AddBlackBoxCaseNameUpdatedCount add blackbox case name updated count
	AddBlackBoxCaseNameUpdatedCount(ctx context.Context, in *AddBlackBoxCaseNameUpdatedCountReq, opts ...grpc.CallOption) (*AddBlackBoxCaseNameUpdatedCountResp, error)
	// AddBlackBoxPreConditionUpdatedCount add blackbox precondition updated count
	AddBlackBoxPreConditionUpdatedCount(ctx context.Context, in *AddBlackBoxPreConditionUpdatedCountReq, opts ...grpc.CallOption) (*AddBlackBoxPreConditionUpdatedCountResp, error)
	// AddBlackBoxCaseStepUpdatedCount add blackbox case step updated count
	AddBlackBoxCaseStepUpdatedCount(ctx context.Context, in *AddBlackBoxCaseStepUpdatedCountReq, opts ...grpc.CallOption) (*AddBlackBoxCaseStepUpdatedCountResp, error)
	// AddBlackBoxExpectResultUpdatedCount add blackbox expect result updated count
	AddBlackBoxExpectResultUpdatedCount(ctx context.Context, in *AddBlackBoxExpectResultUpdatedCountReq, opts ...grpc.CallOption) (*AddBlackBoxExpectResultUpdatedCountReq, error)
	// AddBlackBoxCaseLevelUpdatedCount add blackbox case level updated count
	AddBlackBoxCaseLevelUpdatedCount(ctx context.Context, in *AddBlackBoxCaseLevelUpdatedCountReq, opts ...grpc.CallOption) (*AddBlackBoxCaseLevelUpdatedCountReq, error)
}

type blackBoxGenerationRecordServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewBlackBoxGenerationRecordServiceClient(cc grpc.ClientConnInterface) BlackBoxGenerationRecordServiceClient {
	return &blackBoxGenerationRecordServiceClient{cc}
}

func (c *blackBoxGenerationRecordServiceClient) AddBlackBoxFuncCount(ctx context.Context, in *AddBlackBoxFuncCountReq, opts ...grpc.CallOption) (*AddBlackBoxFuncCountResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(AddBlackBoxFuncCountResp)
	err := c.cc.Invoke(ctx, BlackBoxGenerationRecordService_AddBlackBoxFuncCount_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *blackBoxGenerationRecordServiceClient) AddBlackBoxTestSceneCount(ctx context.Context, in *AddBlackBoxTestSceneCountReq, opts ...grpc.CallOption) (*AddBlackBoxTestSceneCountResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(AddBlackBoxTestSceneCountResp)
	err := c.cc.Invoke(ctx, BlackBoxGenerationRecordService_AddBlackBoxTestSceneCount_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *blackBoxGenerationRecordServiceClient) AddBlackBoxSupplementDocumentCount(ctx context.Context, in *AddBlackBoxSupplementDocumentCountReq, opts ...grpc.CallOption) (*AddBlackBoxSupplementDocumentCountResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(AddBlackBoxSupplementDocumentCountResp)
	err := c.cc.Invoke(ctx, BlackBoxGenerationRecordService_AddBlackBoxSupplementDocumentCount_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *blackBoxGenerationRecordServiceClient) AddBlackBoxBaseMapCount(ctx context.Context, in *AddBlackBoxBaseMapCountReq, opts ...grpc.CallOption) (*AddBlackBoxBaseMapCountResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(AddBlackBoxBaseMapCountResp)
	err := c.cc.Invoke(ctx, BlackBoxGenerationRecordService_AddBlackBoxBaseMapCount_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *blackBoxGenerationRecordServiceClient) AddBlackBoxUpdatedAddCaseCount(ctx context.Context, in *AddBlackBoxUpdatedAddCaseCountReq, opts ...grpc.CallOption) (*AddBlackBoxUpdatedAddCaseCountResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(AddBlackBoxUpdatedAddCaseCountResp)
	err := c.cc.Invoke(ctx, BlackBoxGenerationRecordService_AddBlackBoxUpdatedAddCaseCount_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *blackBoxGenerationRecordServiceClient) AddBlackBoxCaseNameUpdatedCount(ctx context.Context, in *AddBlackBoxCaseNameUpdatedCountReq, opts ...grpc.CallOption) (*AddBlackBoxCaseNameUpdatedCountResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(AddBlackBoxCaseNameUpdatedCountResp)
	err := c.cc.Invoke(ctx, BlackBoxGenerationRecordService_AddBlackBoxCaseNameUpdatedCount_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *blackBoxGenerationRecordServiceClient) AddBlackBoxPreConditionUpdatedCount(ctx context.Context, in *AddBlackBoxPreConditionUpdatedCountReq, opts ...grpc.CallOption) (*AddBlackBoxPreConditionUpdatedCountResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(AddBlackBoxPreConditionUpdatedCountResp)
	err := c.cc.Invoke(ctx, BlackBoxGenerationRecordService_AddBlackBoxPreConditionUpdatedCount_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *blackBoxGenerationRecordServiceClient) AddBlackBoxCaseStepUpdatedCount(ctx context.Context, in *AddBlackBoxCaseStepUpdatedCountReq, opts ...grpc.CallOption) (*AddBlackBoxCaseStepUpdatedCountResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(AddBlackBoxCaseStepUpdatedCountResp)
	err := c.cc.Invoke(ctx, BlackBoxGenerationRecordService_AddBlackBoxCaseStepUpdatedCount_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *blackBoxGenerationRecordServiceClient) AddBlackBoxExpectResultUpdatedCount(ctx context.Context, in *AddBlackBoxExpectResultUpdatedCountReq, opts ...grpc.CallOption) (*AddBlackBoxExpectResultUpdatedCountReq, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(AddBlackBoxExpectResultUpdatedCountReq)
	err := c.cc.Invoke(ctx, BlackBoxGenerationRecordService_AddBlackBoxExpectResultUpdatedCount_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *blackBoxGenerationRecordServiceClient) AddBlackBoxCaseLevelUpdatedCount(ctx context.Context, in *AddBlackBoxCaseLevelUpdatedCountReq, opts ...grpc.CallOption) (*AddBlackBoxCaseLevelUpdatedCountReq, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(AddBlackBoxCaseLevelUpdatedCountReq)
	err := c.cc.Invoke(ctx, BlackBoxGenerationRecordService_AddBlackBoxCaseLevelUpdatedCount_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// BlackBoxGenerationRecordServiceServer is the server API for BlackBoxGenerationRecordService service.
// All implementations must embed UnimplementedBlackBoxGenerationRecordServiceServer
// for forward compatibility.
type BlackBoxGenerationRecordServiceServer interface {
	// AddBlackBoxFuncCount add blackbox func count
	AddBlackBoxFuncCount(context.Context, *AddBlackBoxFuncCountReq) (*AddBlackBoxFuncCountResp, error)
	// AddBlackBoxTestSceneCount add blackbox test scene count
	AddBlackBoxTestSceneCount(context.Context, *AddBlackBoxTestSceneCountReq) (*AddBlackBoxTestSceneCountResp, error)
	// AddBlackBoxSupplementDocumentCount add blackbox supplement document count
	AddBlackBoxSupplementDocumentCount(context.Context, *AddBlackBoxSupplementDocumentCountReq) (*AddBlackBoxSupplementDocumentCountResp, error)
	// AddBlackBoxBaseMapCount add blackbox base map count
	AddBlackBoxBaseMapCount(context.Context, *AddBlackBoxBaseMapCountReq) (*AddBlackBoxBaseMapCountResp, error)
	// AddBlackBoxUpdatedAddCaseCount add blackbox updated add case count
	AddBlackBoxUpdatedAddCaseCount(context.Context, *AddBlackBoxUpdatedAddCaseCountReq) (*AddBlackBoxUpdatedAddCaseCountResp, error)
	// AddBlackBoxCaseNameUpdatedCount add blackbox case name updated count
	AddBlackBoxCaseNameUpdatedCount(context.Context, *AddBlackBoxCaseNameUpdatedCountReq) (*AddBlackBoxCaseNameUpdatedCountResp, error)
	// AddBlackBoxPreConditionUpdatedCount add blackbox precondition updated count
	AddBlackBoxPreConditionUpdatedCount(context.Context, *AddBlackBoxPreConditionUpdatedCountReq) (*AddBlackBoxPreConditionUpdatedCountResp, error)
	// AddBlackBoxCaseStepUpdatedCount add blackbox case step updated count
	AddBlackBoxCaseStepUpdatedCount(context.Context, *AddBlackBoxCaseStepUpdatedCountReq) (*AddBlackBoxCaseStepUpdatedCountResp, error)
	// AddBlackBoxExpectResultUpdatedCount add blackbox expect result updated count
	AddBlackBoxExpectResultUpdatedCount(context.Context, *AddBlackBoxExpectResultUpdatedCountReq) (*AddBlackBoxExpectResultUpdatedCountReq, error)
	// AddBlackBoxCaseLevelUpdatedCount add blackbox case level updated count
	AddBlackBoxCaseLevelUpdatedCount(context.Context, *AddBlackBoxCaseLevelUpdatedCountReq) (*AddBlackBoxCaseLevelUpdatedCountReq, error)
	mustEmbedUnimplementedBlackBoxGenerationRecordServiceServer()
}

// UnimplementedBlackBoxGenerationRecordServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedBlackBoxGenerationRecordServiceServer struct{}

func (UnimplementedBlackBoxGenerationRecordServiceServer) AddBlackBoxFuncCount(context.Context, *AddBlackBoxFuncCountReq) (*AddBlackBoxFuncCountResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddBlackBoxFuncCount not implemented")
}
func (UnimplementedBlackBoxGenerationRecordServiceServer) AddBlackBoxTestSceneCount(context.Context, *AddBlackBoxTestSceneCountReq) (*AddBlackBoxTestSceneCountResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddBlackBoxTestSceneCount not implemented")
}
func (UnimplementedBlackBoxGenerationRecordServiceServer) AddBlackBoxSupplementDocumentCount(context.Context, *AddBlackBoxSupplementDocumentCountReq) (*AddBlackBoxSupplementDocumentCountResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddBlackBoxSupplementDocumentCount not implemented")
}
func (UnimplementedBlackBoxGenerationRecordServiceServer) AddBlackBoxBaseMapCount(context.Context, *AddBlackBoxBaseMapCountReq) (*AddBlackBoxBaseMapCountResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddBlackBoxBaseMapCount not implemented")
}
func (UnimplementedBlackBoxGenerationRecordServiceServer) AddBlackBoxUpdatedAddCaseCount(context.Context, *AddBlackBoxUpdatedAddCaseCountReq) (*AddBlackBoxUpdatedAddCaseCountResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddBlackBoxUpdatedAddCaseCount not implemented")
}
func (UnimplementedBlackBoxGenerationRecordServiceServer) AddBlackBoxCaseNameUpdatedCount(context.Context, *AddBlackBoxCaseNameUpdatedCountReq) (*AddBlackBoxCaseNameUpdatedCountResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddBlackBoxCaseNameUpdatedCount not implemented")
}
func (UnimplementedBlackBoxGenerationRecordServiceServer) AddBlackBoxPreConditionUpdatedCount(context.Context, *AddBlackBoxPreConditionUpdatedCountReq) (*AddBlackBoxPreConditionUpdatedCountResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddBlackBoxPreConditionUpdatedCount not implemented")
}
func (UnimplementedBlackBoxGenerationRecordServiceServer) AddBlackBoxCaseStepUpdatedCount(context.Context, *AddBlackBoxCaseStepUpdatedCountReq) (*AddBlackBoxCaseStepUpdatedCountResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddBlackBoxCaseStepUpdatedCount not implemented")
}
func (UnimplementedBlackBoxGenerationRecordServiceServer) AddBlackBoxExpectResultUpdatedCount(context.Context, *AddBlackBoxExpectResultUpdatedCountReq) (*AddBlackBoxExpectResultUpdatedCountReq, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddBlackBoxExpectResultUpdatedCount not implemented")
}
func (UnimplementedBlackBoxGenerationRecordServiceServer) AddBlackBoxCaseLevelUpdatedCount(context.Context, *AddBlackBoxCaseLevelUpdatedCountReq) (*AddBlackBoxCaseLevelUpdatedCountReq, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddBlackBoxCaseLevelUpdatedCount not implemented")
}
func (UnimplementedBlackBoxGenerationRecordServiceServer) mustEmbedUnimplementedBlackBoxGenerationRecordServiceServer() {
}
func (UnimplementedBlackBoxGenerationRecordServiceServer) testEmbeddedByValue() {}

// UnsafeBlackBoxGenerationRecordServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to BlackBoxGenerationRecordServiceServer will
// result in compilation errors.
type UnsafeBlackBoxGenerationRecordServiceServer interface {
	mustEmbedUnimplementedBlackBoxGenerationRecordServiceServer()
}

func RegisterBlackBoxGenerationRecordServiceServer(s grpc.ServiceRegistrar, srv BlackBoxGenerationRecordServiceServer) {
	// If the following call pancis, it indicates UnimplementedBlackBoxGenerationRecordServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&BlackBoxGenerationRecordService_ServiceDesc, srv)
}

func _BlackBoxGenerationRecordService_AddBlackBoxFuncCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddBlackBoxFuncCountReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BlackBoxGenerationRecordServiceServer).AddBlackBoxFuncCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BlackBoxGenerationRecordService_AddBlackBoxFuncCount_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BlackBoxGenerationRecordServiceServer).AddBlackBoxFuncCount(ctx, req.(*AddBlackBoxFuncCountReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BlackBoxGenerationRecordService_AddBlackBoxTestSceneCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddBlackBoxTestSceneCountReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BlackBoxGenerationRecordServiceServer).AddBlackBoxTestSceneCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BlackBoxGenerationRecordService_AddBlackBoxTestSceneCount_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BlackBoxGenerationRecordServiceServer).AddBlackBoxTestSceneCount(ctx, req.(*AddBlackBoxTestSceneCountReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BlackBoxGenerationRecordService_AddBlackBoxSupplementDocumentCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddBlackBoxSupplementDocumentCountReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BlackBoxGenerationRecordServiceServer).AddBlackBoxSupplementDocumentCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BlackBoxGenerationRecordService_AddBlackBoxSupplementDocumentCount_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BlackBoxGenerationRecordServiceServer).AddBlackBoxSupplementDocumentCount(ctx, req.(*AddBlackBoxSupplementDocumentCountReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BlackBoxGenerationRecordService_AddBlackBoxBaseMapCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddBlackBoxBaseMapCountReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BlackBoxGenerationRecordServiceServer).AddBlackBoxBaseMapCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BlackBoxGenerationRecordService_AddBlackBoxBaseMapCount_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BlackBoxGenerationRecordServiceServer).AddBlackBoxBaseMapCount(ctx, req.(*AddBlackBoxBaseMapCountReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BlackBoxGenerationRecordService_AddBlackBoxUpdatedAddCaseCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddBlackBoxUpdatedAddCaseCountReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BlackBoxGenerationRecordServiceServer).AddBlackBoxUpdatedAddCaseCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BlackBoxGenerationRecordService_AddBlackBoxUpdatedAddCaseCount_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BlackBoxGenerationRecordServiceServer).AddBlackBoxUpdatedAddCaseCount(ctx, req.(*AddBlackBoxUpdatedAddCaseCountReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BlackBoxGenerationRecordService_AddBlackBoxCaseNameUpdatedCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddBlackBoxCaseNameUpdatedCountReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BlackBoxGenerationRecordServiceServer).AddBlackBoxCaseNameUpdatedCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BlackBoxGenerationRecordService_AddBlackBoxCaseNameUpdatedCount_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BlackBoxGenerationRecordServiceServer).AddBlackBoxCaseNameUpdatedCount(ctx, req.(*AddBlackBoxCaseNameUpdatedCountReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BlackBoxGenerationRecordService_AddBlackBoxPreConditionUpdatedCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddBlackBoxPreConditionUpdatedCountReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BlackBoxGenerationRecordServiceServer).AddBlackBoxPreConditionUpdatedCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BlackBoxGenerationRecordService_AddBlackBoxPreConditionUpdatedCount_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BlackBoxGenerationRecordServiceServer).AddBlackBoxPreConditionUpdatedCount(ctx, req.(*AddBlackBoxPreConditionUpdatedCountReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BlackBoxGenerationRecordService_AddBlackBoxCaseStepUpdatedCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddBlackBoxCaseStepUpdatedCountReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BlackBoxGenerationRecordServiceServer).AddBlackBoxCaseStepUpdatedCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BlackBoxGenerationRecordService_AddBlackBoxCaseStepUpdatedCount_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BlackBoxGenerationRecordServiceServer).AddBlackBoxCaseStepUpdatedCount(ctx, req.(*AddBlackBoxCaseStepUpdatedCountReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BlackBoxGenerationRecordService_AddBlackBoxExpectResultUpdatedCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddBlackBoxExpectResultUpdatedCountReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BlackBoxGenerationRecordServiceServer).AddBlackBoxExpectResultUpdatedCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BlackBoxGenerationRecordService_AddBlackBoxExpectResultUpdatedCount_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BlackBoxGenerationRecordServiceServer).AddBlackBoxExpectResultUpdatedCount(ctx, req.(*AddBlackBoxExpectResultUpdatedCountReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BlackBoxGenerationRecordService_AddBlackBoxCaseLevelUpdatedCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddBlackBoxCaseLevelUpdatedCountReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BlackBoxGenerationRecordServiceServer).AddBlackBoxCaseLevelUpdatedCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BlackBoxGenerationRecordService_AddBlackBoxCaseLevelUpdatedCount_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BlackBoxGenerationRecordServiceServer).AddBlackBoxCaseLevelUpdatedCount(ctx, req.(*AddBlackBoxCaseLevelUpdatedCountReq))
	}
	return interceptor(ctx, in, info, handler)
}

// BlackBoxGenerationRecordService_ServiceDesc is the grpc.ServiceDesc for BlackBoxGenerationRecordService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var BlackBoxGenerationRecordService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "ai.BlackBoxGenerationRecordService",
	HandlerType: (*BlackBoxGenerationRecordServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "AddBlackBoxFuncCount",
			Handler:    _BlackBoxGenerationRecordService_AddBlackBoxFuncCount_Handler,
		},
		{
			MethodName: "AddBlackBoxTestSceneCount",
			Handler:    _BlackBoxGenerationRecordService_AddBlackBoxTestSceneCount_Handler,
		},
		{
			MethodName: "AddBlackBoxSupplementDocumentCount",
			Handler:    _BlackBoxGenerationRecordService_AddBlackBoxSupplementDocumentCount_Handler,
		},
		{
			MethodName: "AddBlackBoxBaseMapCount",
			Handler:    _BlackBoxGenerationRecordService_AddBlackBoxBaseMapCount_Handler,
		},
		{
			MethodName: "AddBlackBoxUpdatedAddCaseCount",
			Handler:    _BlackBoxGenerationRecordService_AddBlackBoxUpdatedAddCaseCount_Handler,
		},
		{
			MethodName: "AddBlackBoxCaseNameUpdatedCount",
			Handler:    _BlackBoxGenerationRecordService_AddBlackBoxCaseNameUpdatedCount_Handler,
		},
		{
			MethodName: "AddBlackBoxPreConditionUpdatedCount",
			Handler:    _BlackBoxGenerationRecordService_AddBlackBoxPreConditionUpdatedCount_Handler,
		},
		{
			MethodName: "AddBlackBoxCaseStepUpdatedCount",
			Handler:    _BlackBoxGenerationRecordService_AddBlackBoxCaseStepUpdatedCount_Handler,
		},
		{
			MethodName: "AddBlackBoxExpectResultUpdatedCount",
			Handler:    _BlackBoxGenerationRecordService_AddBlackBoxExpectResultUpdatedCount_Handler,
		},
		{
			MethodName: "AddBlackBoxCaseLevelUpdatedCount",
			Handler:    _BlackBoxGenerationRecordService_AddBlackBoxCaseLevelUpdatedCount_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "ai/ai.proto",
}
