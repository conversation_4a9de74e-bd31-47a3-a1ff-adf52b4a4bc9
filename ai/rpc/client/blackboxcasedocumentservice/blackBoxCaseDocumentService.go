// Code generated by goctl. DO NOT EDIT.
// goctl 1.7.5
// Source: ai.proto

package blackboxcasedocumentservice

import (
	"context"

	"github.com/zeromicro/go-zero/zrpc"
	"google.golang.org/grpc"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
)

type (
	AddBlackBoxBaseMapCountReq                            = pb.AddBlackBoxBaseMapCountReq
	AddBlackBoxBaseMapCountResp                           = pb.AddBlackBoxBaseMapCountResp
	AddBlackBoxCaseLevelUpdatedCountReq                   = pb.AddBlackBoxCaseLevelUpdatedCountReq
	AddBlackBoxCaseLevelUpdatedCountResp                  = pb.AddBlackBoxCaseLevelUpdatedCountResp
	AddBlackBoxCaseNameUpdatedCountReq                    = pb.AddBlackBoxCaseNameUpdatedCountReq
	AddBlackBoxCaseNameUpdatedCountResp                   = pb.AddBlackBoxCaseNameUpdatedCountResp
	AddBlackBoxCaseStepUpdatedCountReq                    = pb.AddBlackBoxCaseStepUpdatedCountReq
	AddBlackBoxCaseStepUpdatedCountResp                   = pb.AddBlackBoxCaseStepUpdatedCountResp
	AddBlackBoxExpectResultUpdatedCountReq                = pb.AddBlackBoxExpectResultUpdatedCountReq
	AddBlackBoxExpectResultUpdatedCountResp               = pb.AddBlackBoxExpectResultUpdatedCountResp
	AddBlackBoxFuncCountReq                               = pb.AddBlackBoxFuncCountReq
	AddBlackBoxFuncCountResp                              = pb.AddBlackBoxFuncCountResp
	AddBlackBoxPreConditionUpdatedCountReq                = pb.AddBlackBoxPreConditionUpdatedCountReq
	AddBlackBoxPreConditionUpdatedCountResp               = pb.AddBlackBoxPreConditionUpdatedCountResp
	AddBlackBoxSupplementDocumentCountReq                 = pb.AddBlackBoxSupplementDocumentCountReq
	AddBlackBoxSupplementDocumentCountResp                = pb.AddBlackBoxSupplementDocumentCountResp
	AddBlackBoxTestSceneCountReq                          = pb.AddBlackBoxTestSceneCountReq
	AddBlackBoxTestSceneCountResp                         = pb.AddBlackBoxTestSceneCountResp
	AddBlackBoxUpdatedAddCaseCountReq                     = pb.AddBlackBoxUpdatedAddCaseCountReq
	AddBlackBoxUpdatedAddCaseCountResp                    = pb.AddBlackBoxUpdatedAddCaseCountResp
	AdoptBlackBoxCaseRevisionReq                          = pb.AdoptBlackBoxCaseRevisionReq
	AdoptBlackBoxCaseRevisionResp                         = pb.AdoptBlackBoxCaseRevisionResp
	AppendBlackBoxCaseDataReq                             = pb.AppendBlackBoxCaseDataReq
	AppendBlackBoxCaseDataResp                            = pb.AppendBlackBoxCaseDataResp
	BatchDeleteBlackBoxCaseDocumentForAssistantReq        = pb.BatchDeleteBlackBoxCaseDocumentForAssistantReq
	BatchDeleteBlackBoxCaseDocumentForAssistantResp       = pb.BatchDeleteBlackBoxCaseDocumentForAssistantResp
	BatchDeleteBlackBoxCaseDocumentForSessionReq          = pb.BatchDeleteBlackBoxCaseDocumentForSessionReq
	BatchDeleteBlackBoxCaseDocumentForSessionResp         = pb.BatchDeleteBlackBoxCaseDocumentForSessionResp
	BatchDeleteCaseFailLogReq                             = pb.BatchDeleteCaseFailLogReq
	BatchUpdateBlackBoxCaseDataReq                        = pb.BatchUpdateBlackBoxCaseDataReq
	BatchUpdateBlackBoxCaseDataResp                       = pb.BatchUpdateBlackBoxCaseDataResp
	BlackBoxCase                                          = pb.BlackBoxCase
	BlackBoxCaseAssistant                                 = pb.BlackBoxCaseAssistant
	BlackBoxCaseData                                      = pb.BlackBoxCaseData
	BlackBoxCaseDir                                       = pb.BlackBoxCaseDir
	BlackBoxCaseDocument                                  = pb.BlackBoxCaseDocument
	BlackBoxCaseKnowledge                                 = pb.BlackBoxCaseKnowledge
	BlackBoxCaseMapDocument                               = pb.BlackBoxCaseMapDocument
	BlackBoxCaseReferenceContent                          = pb.BlackBoxCaseReferenceContent
	BlackBoxCaseRevision                                  = pb.BlackBoxCaseRevision
	BlackBoxCaseSession                                   = pb.BlackBoxCaseSession
	BlackBoxCaseSessionConversationState                  = pb.BlackBoxCaseSessionConversationState
	BlackBoxCaseSessionMessage                            = pb.BlackBoxCaseSessionMessage
	BlackBoxCaseSessionMsgContent                         = pb.BlackBoxCaseSessionMsgContent
	BlackBoxCaseSessionMsgMetadata                        = pb.BlackBoxCaseSessionMsgMetadata
	BlackBoxCaseTwBeta                                    = pb.BlackBoxCaseTwBeta
	BlackBoxCaseTwBetaMind                                = pb.BlackBoxCaseTwBetaMind
	BlackBoxCaseTwBetaMindCheck                           = pb.BlackBoxCaseTwBetaMindCheck
	BlackBoxKnowledgeExperience                           = pb.BlackBoxKnowledgeExperience
	BlackBoxKnowledgeRecallDocument                       = pb.BlackBoxKnowledgeRecallDocument
	BlackBoxKnowledgeTag                                  = pb.BlackBoxKnowledgeTag
	BlackBoxKnowledgeUsedKnowledge                        = pb.BlackBoxKnowledgeUsedKnowledge
	ClearKeepBlackBoxCaseDataReq                          = pb.ClearKeepBlackBoxCaseDataReq
	ClearKeepBlackBoxCaseDataResp                         = pb.ClearKeepBlackBoxCaseDataResp
	CreateBlackBoxCaseAssistantReq                        = pb.CreateBlackBoxCaseAssistantReq
	CreateBlackBoxCaseAssistantResp                       = pb.CreateBlackBoxCaseAssistantResp
	CreateBlackBoxCaseDataReq                             = pb.CreateBlackBoxCaseDataReq
	CreateBlackBoxCaseDataResp                            = pb.CreateBlackBoxCaseDataResp
	CreateBlackBoxCaseDirReq                              = pb.CreateBlackBoxCaseDirReq
	CreateBlackBoxCaseDirResp                             = pb.CreateBlackBoxCaseDirResp
	CreateBlackBoxCaseDocumentForAssistant                = pb.CreateBlackBoxCaseDocumentForAssistant
	CreateBlackBoxCaseDocumentForAssistantReq             = pb.CreateBlackBoxCaseDocumentForAssistantReq
	CreateBlackBoxCaseDocumentForAssistantResp            = pb.CreateBlackBoxCaseDocumentForAssistantResp
	CreateBlackBoxCaseDocumentForSessionRecvReq           = pb.CreateBlackBoxCaseDocumentForSessionRecvReq
	CreateBlackBoxCaseDocumentForSessionRecvResp          = pb.CreateBlackBoxCaseDocumentForSessionRecvResp
	CreateBlackBoxCaseDocumentForSessionReq               = pb.CreateBlackBoxCaseDocumentForSessionReq
	CreateBlackBoxCaseDocumentForSessionResp              = pb.CreateBlackBoxCaseDocumentForSessionResp
	CreateBlackBoxCaseKnowledgeReq                        = pb.CreateBlackBoxCaseKnowledgeReq
	CreateBlackBoxCaseKnowledgeResp                       = pb.CreateBlackBoxCaseKnowledgeResp
	CreateBlackBoxCaseMapIdReq                            = pb.CreateBlackBoxCaseMapIdReq
	CreateBlackBoxCaseMapIdResp                           = pb.CreateBlackBoxCaseMapIdResp
	CreateBlackBoxCaseMapReq                              = pb.CreateBlackBoxCaseMapReq
	CreateBlackBoxCaseMapResp                             = pb.CreateBlackBoxCaseMapResp
	CreateBlackBoxCaseModifyTestCaseReferenceContentReq   = pb.CreateBlackBoxCaseModifyTestCaseReferenceContentReq
	CreateBlackBoxCaseModifyTestCaseReferenceContentResp  = pb.CreateBlackBoxCaseModifyTestCaseReferenceContentResp
	CreateBlackBoxCaseReq                                 = pb.CreateBlackBoxCaseReq
	CreateBlackBoxCaseResp                                = pb.CreateBlackBoxCaseResp
	CreateBlackBoxCaseRevisionDataReq                     = pb.CreateBlackBoxCaseRevisionDataReq
	CreateBlackBoxCaseRevisionDataResp                    = pb.CreateBlackBoxCaseRevisionDataResp
	CreateBlackBoxCaseRevisionReq                         = pb.CreateBlackBoxCaseRevisionReq
	CreateBlackBoxCaseRevisionResp                        = pb.CreateBlackBoxCaseRevisionResp
	CreateBlackBoxCaseSessionReq                          = pb.CreateBlackBoxCaseSessionReq
	CreateBlackBoxCaseSessionResp                         = pb.CreateBlackBoxCaseSessionResp
	CreateBlackBoxCaseTwBetaMindReq                       = pb.CreateBlackBoxCaseTwBetaMindReq
	CreateBlackBoxCaseTwBetaMindResp                      = pb.CreateBlackBoxCaseTwBetaMindResp
	CreateBlackBoxCaseTwBetaReq                           = pb.CreateBlackBoxCaseTwBetaReq
	CreateBlackBoxCaseTwBetaResp                          = pb.CreateBlackBoxCaseTwBetaResp
	DeleteBlackBoxCaseAssistantReq                        = pb.DeleteBlackBoxCaseAssistantReq
	DeleteBlackBoxCaseAssistantResp                       = pb.DeleteBlackBoxCaseAssistantResp
	DeleteBlackBoxCaseDataReq                             = pb.DeleteBlackBoxCaseDataReq
	DeleteBlackBoxCaseDataResp                            = pb.DeleteBlackBoxCaseDataResp
	DeleteBlackBoxCaseDirReq                              = pb.DeleteBlackBoxCaseDirReq
	DeleteBlackBoxCaseDirResp                             = pb.DeleteBlackBoxCaseDirResp
	DeleteBlackBoxCaseDocumentForAssistantReq             = pb.DeleteBlackBoxCaseDocumentForAssistantReq
	DeleteBlackBoxCaseDocumentForAssistantResp            = pb.DeleteBlackBoxCaseDocumentForAssistantResp
	DeleteBlackBoxCaseDocumentForSessionReq               = pb.DeleteBlackBoxCaseDocumentForSessionReq
	DeleteBlackBoxCaseDocumentForSessionResp              = pb.DeleteBlackBoxCaseDocumentForSessionResp
	DeleteBlackBoxCaseKnowledgeReq                        = pb.DeleteBlackBoxCaseKnowledgeReq
	DeleteBlackBoxCaseKnowledgeResp                       = pb.DeleteBlackBoxCaseKnowledgeResp
	DeleteBlackBoxCaseMapDocumentReq                      = pb.DeleteBlackBoxCaseMapDocumentReq
	DeleteBlackBoxCaseMapDocumentResp                     = pb.DeleteBlackBoxCaseMapDocumentResp
	DeleteBlackBoxCaseReq                                 = pb.DeleteBlackBoxCaseReq
	DeleteBlackBoxCaseResp                                = pb.DeleteBlackBoxCaseResp
	DeleteBlackBoxCaseRevisionReq                         = pb.DeleteBlackBoxCaseRevisionReq
	DeleteBlackBoxCaseRevisionResp                        = pb.DeleteBlackBoxCaseRevisionResp
	DeleteBlackBoxCaseSessionReq                          = pb.DeleteBlackBoxCaseSessionReq
	DeleteBlackBoxCaseSessionResp                         = pb.DeleteBlackBoxCaseSessionResp
	DeleteBlackBoxCaseTwBetaReq                           = pb.DeleteBlackBoxCaseTwBetaReq
	DeleteBlackBoxCaseTwBetaResp                          = pb.DeleteBlackBoxCaseTwBetaResp
	GenerateBlackBoxCaseRefReq                            = pb.GenerateBlackBoxCaseRefReq
	GenerateBlackBoxCaseRefResp                           = pb.GenerateBlackBoxCaseRefResp
	GenerateBlackBoxCaseRevisionIdReq                     = pb.GenerateBlackBoxCaseRevisionIdReq
	GenerateBlackBoxCaseRevisionIdResp                    = pb.GenerateBlackBoxCaseRevisionIdResp
	GetBlackBoxCaseAssistantReq                           = pb.GetBlackBoxCaseAssistantReq
	GetBlackBoxCaseAssistantResp                          = pb.GetBlackBoxCaseAssistantResp
	GetBlackBoxCaseConversationAIMessageReq               = pb.GetBlackBoxCaseConversationAIMessageReq
	GetBlackBoxCaseConversationAIMessageResp              = pb.GetBlackBoxCaseConversationAIMessageResp
	GetBlackBoxCaseConversationAIStateReq                 = pb.GetBlackBoxCaseConversationAIStateReq
	GetBlackBoxCaseConversationAIStateResp                = pb.GetBlackBoxCaseConversationAIStateResp
	GetBlackBoxCaseDataReq                                = pb.GetBlackBoxCaseDataReq
	GetBlackBoxCaseDataResp                               = pb.GetBlackBoxCaseDataResp
	GetBlackBoxCaseDirReq                                 = pb.GetBlackBoxCaseDirReq
	GetBlackBoxCaseDirResp                                = pb.GetBlackBoxCaseDirResp
	GetBlackBoxCaseDocumentHeadersListReq                 = pb.GetBlackBoxCaseDocumentHeadersListReq
	GetBlackBoxCaseDocumentHeadersListResp                = pb.GetBlackBoxCaseDocumentHeadersListResp
	GetBlackBoxCaseDocumentReq                            = pb.GetBlackBoxCaseDocumentReq
	GetBlackBoxCaseDocumentResp                           = pb.GetBlackBoxCaseDocumentResp
	GetBlackBoxCaseKnowledgeReq                           = pb.GetBlackBoxCaseKnowledgeReq
	GetBlackBoxCaseKnowledgeResp                          = pb.GetBlackBoxCaseKnowledgeResp
	GetBlackBoxCaseKnowledgeV2Req                         = pb.GetBlackBoxCaseKnowledgeV2Req
	GetBlackBoxCaseKnowledgeV2Resp                        = pb.GetBlackBoxCaseKnowledgeV2Resp
	GetBlackBoxCaseMapDocumentReq                         = pb.GetBlackBoxCaseMapDocumentReq
	GetBlackBoxCaseMapReq                                 = pb.GetBlackBoxCaseMapReq
	GetBlackBoxCaseMapResp                                = pb.GetBlackBoxCaseMapResp
	GetBlackBoxCaseModifyTestCaseReferenceContentListReq  = pb.GetBlackBoxCaseModifyTestCaseReferenceContentListReq
	GetBlackBoxCaseModifyTestCaseReferenceContentListResp = pb.GetBlackBoxCaseModifyTestCaseReferenceContentListResp
	GetBlackBoxCaseReq                                    = pb.GetBlackBoxCaseReq
	GetBlackBoxCaseResp                                   = pb.GetBlackBoxCaseResp
	GetBlackBoxCaseRevisionByRevisionIdReq                = pb.GetBlackBoxCaseRevisionByRevisionIdReq
	GetBlackBoxCaseRevisionByRevisionIdResp               = pb.GetBlackBoxCaseRevisionByRevisionIdResp
	GetBlackBoxCaseRevisionReq                            = pb.GetBlackBoxCaseRevisionReq
	GetBlackBoxCaseRevisionResp                           = pb.GetBlackBoxCaseRevisionResp
	GetBlackBoxCaseSessionHistoryMessageListReq           = pb.GetBlackBoxCaseSessionHistoryMessageListReq
	GetBlackBoxCaseSessionHistoryMessageListResp          = pb.GetBlackBoxCaseSessionHistoryMessageListResp
	GetBlackBoxCaseSessionReq                             = pb.GetBlackBoxCaseSessionReq
	GetBlackBoxCaseSessionResp                            = pb.GetBlackBoxCaseSessionResp
	GetBlackBoxCaseTwBetaContentReq                       = pb.GetBlackBoxCaseTwBetaContentReq
	GetBlackBoxCaseTwBetaContentResp                      = pb.GetBlackBoxCaseTwBetaContentResp
	GetBlackBoxCaseTwBetaMindContentReq                   = pb.GetBlackBoxCaseTwBetaMindContentReq
	GetBlackBoxCaseTwBetaMindContentResp                  = pb.GetBlackBoxCaseTwBetaMindContentResp
	GetCompleteBlackBoxCaseMapReq                         = pb.GetCompleteBlackBoxCaseMapReq
	GetCompleteBlackBoxCaseMapResp                        = pb.GetCompleteBlackBoxCaseMapResp
	GetCreateBlackBoxCaseAIMessageReq                     = pb.GetCreateBlackBoxCaseAIMessageReq
	GetCreateBlackBoxCaseAIMessageResp                    = pb.GetCreateBlackBoxCaseAIMessageResp
	KeepBlackBoxCaseDataReq                               = pb.KeepBlackBoxCaseDataReq
	KeepBlackBoxCaseDataResp                              = pb.KeepBlackBoxCaseDataResp
	KnowledgeDocPgTitle                                   = pb.KnowledgeDocPgTitle
	ListBlackBoxCaseDataByCaseIdsReq                      = pb.ListBlackBoxCaseDataByCaseIdsReq
	ListBlackBoxCaseDataByCaseIdsResp                     = pb.ListBlackBoxCaseDataByCaseIdsResp
	ListBlackBoxCaseDataReq                               = pb.ListBlackBoxCaseDataReq
	ListBlackBoxCaseDataResp                              = pb.ListBlackBoxCaseDataResp
	ListBlackBoxCaseDirsReq                               = pb.ListBlackBoxCaseDirsReq
	ListBlackBoxCaseDirsResp                              = pb.ListBlackBoxCaseDirsResp
	ListBlackBoxCaseKnowledgeReq                          = pb.ListBlackBoxCaseKnowledgeReq
	ListBlackBoxCaseKnowledgeResp                         = pb.ListBlackBoxCaseKnowledgeResp
	ListBlackBoxCaseMapDocumentsReq                       = pb.ListBlackBoxCaseMapDocumentsReq
	ListBlackBoxCaseMapDocumentsResp                      = pb.ListBlackBoxCaseMapDocumentsResp
	ListBlackBoxCaseReq                                   = pb.ListBlackBoxCaseReq
	ListBlackBoxCaseResp                                  = pb.ListBlackBoxCaseResp
	ListBlackBoxCaseRevisionReq                           = pb.ListBlackBoxCaseRevisionReq
	ListBlackBoxCaseRevisionResp                          = pb.ListBlackBoxCaseRevisionResp
	ListBlackBoxCaseTwBetaMindCheckReq                    = pb.ListBlackBoxCaseTwBetaMindCheckReq
	ListBlackBoxCaseTwBetaMindCheckResp                   = pb.ListBlackBoxCaseTwBetaMindCheckResp
	ListBlackBoxCaseTwBetaMindReq                         = pb.ListBlackBoxCaseTwBetaMindReq
	ListBlackBoxCaseTwBetaMindResp                        = pb.ListBlackBoxCaseTwBetaMindResp
	ListBlackBoxCaseTwBetaReq                             = pb.ListBlackBoxCaseTwBetaReq
	ListBlackBoxCaseTwBetaResp                            = pb.ListBlackBoxCaseTwBetaResp
	MergeBlackBoxCaseDataReq                              = pb.MergeBlackBoxCaseDataReq
	MergeBlackBoxCaseDataResp                             = pb.MergeBlackBoxCaseDataResp
	MergeBlackBoxCaseReq                                  = pb.MergeBlackBoxCaseReq
	MergeBlackBoxCaseResp                                 = pb.MergeBlackBoxCaseResp
	QueryIncBlackBoxCaseReq                               = pb.QueryIncBlackBoxCaseReq
	QueryIncBlackBoxCaseResp                              = pb.QueryIncBlackBoxCaseResp
	RefreshCaseRefMetricsReq                              = pb.RefreshCaseRefMetricsReq
	RefreshCaseRefMetricsResp                             = pb.RefreshCaseRefMetricsResp
	ReloadBlackBoxCaseDocumentReq                         = pb.ReloadBlackBoxCaseDocumentReq
	ReloadBlackBoxCaseDocumentResp                        = pb.ReloadBlackBoxCaseDocumentResp
	ReloadBlackBoxCaseKnowledgeReq                        = pb.ReloadBlackBoxCaseKnowledgeReq
	ReloadBlackBoxCaseKnowledgeResp                       = pb.ReloadBlackBoxCaseKnowledgeResp
	RemoveBlackBoxCaseSessionHistoryMessageListReq        = pb.RemoveBlackBoxCaseSessionHistoryMessageListReq
	RemoveBlackBoxCaseSessionHistoryMessageListResp       = pb.RemoveBlackBoxCaseSessionHistoryMessageListResp
	ReorderBlackBoxCaseDataReq                            = pb.ReorderBlackBoxCaseDataReq
	ReorderBlackBoxCaseDataResp                           = pb.ReorderBlackBoxCaseDataResp
	ReplaceBlackBoxCaseDataReq                            = pb.ReplaceBlackBoxCaseDataReq
	ReplaceBlackBoxCaseDataResp                           = pb.ReplaceBlackBoxCaseDataResp
	ReplaceBlackBoxCaseReq                                = pb.ReplaceBlackBoxCaseReq
	ReplaceBlackBoxCaseResp                               = pb.ReplaceBlackBoxCaseResp
	SearchBlackBoxCaseAssistantReq                        = pb.SearchBlackBoxCaseAssistantReq
	SearchBlackBoxCaseAssistantResp                       = pb.SearchBlackBoxCaseAssistantResp
	SearchBlackBoxCaseDocumentForAssistantReq             = pb.SearchBlackBoxCaseDocumentForAssistantReq
	SearchBlackBoxCaseDocumentForAssistantResp            = pb.SearchBlackBoxCaseDocumentForAssistantResp
	SearchBlackBoxCaseDocumentForSessionReq               = pb.SearchBlackBoxCaseDocumentForSessionReq
	SearchBlackBoxCaseDocumentForSessionResp              = pb.SearchBlackBoxCaseDocumentForSessionResp
	SearchBlackBoxCaseSessionReq                          = pb.SearchBlackBoxCaseSessionReq
	SearchBlackBoxCaseSessionResp                         = pb.SearchBlackBoxCaseSessionResp
	SearchCaseReq                                         = pb.SearchCaseReq
	SendBlackBoxCaseForSessionReq                         = pb.SendBlackBoxCaseForSessionReq
	SendBlackBoxCaseForSessionResp                        = pb.SendBlackBoxCaseForSessionResp
	TransferBlackBoxCase2XMindReq                         = pb.TransferBlackBoxCase2XMindReq
	TransferBlackBoxCase2XMindResp                        = pb.TransferBlackBoxCase2XMindResp
	UpdateBlackBoxCaseAIReq                               = pb.UpdateBlackBoxCaseAIReq
	UpdateBlackBoxCaseAIResp                              = pb.UpdateBlackBoxCaseAIResp
	UpdateBlackBoxCaseAssistantReq                        = pb.UpdateBlackBoxCaseAssistantReq
	UpdateBlackBoxCaseAssistantResp                       = pb.UpdateBlackBoxCaseAssistantResp
	UpdateBlackBoxCaseDataOrderReq                        = pb.UpdateBlackBoxCaseDataOrderReq
	UpdateBlackBoxCaseDataOrderResp                       = pb.UpdateBlackBoxCaseDataOrderResp
	UpdateBlackBoxCaseDataReq                             = pb.UpdateBlackBoxCaseDataReq
	UpdateBlackBoxCaseDataResp                            = pb.UpdateBlackBoxCaseDataResp
	UpdateBlackBoxCaseDirReq                              = pb.UpdateBlackBoxCaseDirReq
	UpdateBlackBoxCaseDirResp                             = pb.UpdateBlackBoxCaseDirResp
	UpdateBlackBoxCaseDocumentReq                         = pb.UpdateBlackBoxCaseDocumentReq
	UpdateBlackBoxCaseDocumentResp                        = pb.UpdateBlackBoxCaseDocumentResp
	UpdateBlackBoxCaseDocumentStatusForAssistantReq       = pb.UpdateBlackBoxCaseDocumentStatusForAssistantReq
	UpdateBlackBoxCaseDocumentStatusForAssistantResp      = pb.UpdateBlackBoxCaseDocumentStatusForAssistantResp
	UpdateBlackBoxCaseKnowledgeReq                        = pb.UpdateBlackBoxCaseKnowledgeReq
	UpdateBlackBoxCaseKnowledgeResp                       = pb.UpdateBlackBoxCaseKnowledgeResp
	UpdateBlackBoxCaseKnowledgeV2Req                      = pb.UpdateBlackBoxCaseKnowledgeV2Req
	UpdateBlackBoxCaseKnowledgeV2Resp                     = pb.UpdateBlackBoxCaseKnowledgeV2Resp
	UpdateBlackBoxCaseMapDocumentResp                     = pb.UpdateBlackBoxCaseMapDocumentResp
	UpdateBlackBoxCaseMapRecallDocumentReq                = pb.UpdateBlackBoxCaseMapRecallDocumentReq
	UpdateBlackBoxCaseMapRecallDocumentResp               = pb.UpdateBlackBoxCaseMapRecallDocumentResp
	UpdateBlackBoxCaseMapReq                              = pb.UpdateBlackBoxCaseMapReq
	UpdateBlackBoxCaseMapResp                             = pb.UpdateBlackBoxCaseMapResp
	UpdateBlackBoxCaseMapUsedKnowledgeReq                 = pb.UpdateBlackBoxCaseMapUsedKnowledgeReq
	UpdateBlackBoxCaseMapUsedKnowledgeResp                = pb.UpdateBlackBoxCaseMapUsedKnowledgeResp
	UpdateBlackBoxCaseMindTwBetaReq                       = pb.UpdateBlackBoxCaseMindTwBetaReq
	UpdateBlackBoxCaseMindTwBetaResp                      = pb.UpdateBlackBoxCaseMindTwBetaResp
	UpdateBlackBoxCaseReq                                 = pb.UpdateBlackBoxCaseReq
	UpdateBlackBoxCaseResp                                = pb.UpdateBlackBoxCaseResp
	UpdateBlackBoxCaseRevisionReq                         = pb.UpdateBlackBoxCaseRevisionReq
	UpdateBlackBoxCaseRevisionResp                        = pb.UpdateBlackBoxCaseRevisionResp
	UpdateBlackBoxCaseRevisionWithCaseRefIdReq            = pb.UpdateBlackBoxCaseRevisionWithCaseRefIdReq
	UpdateBlackBoxCaseRevisionWithCaseRefIdResp           = pb.UpdateBlackBoxCaseRevisionWithCaseRefIdResp
	UpdateBlackBoxCaseSessionReq                          = pb.UpdateBlackBoxCaseSessionReq
	UpdateBlackBoxCaseSessionResp                         = pb.UpdateBlackBoxCaseSessionResp

	BlackBoxCaseDocumentService interface {
		// CreateBlackBoxCaseDocumentForAssistant creates a black box case document for assistant
		CreateBlackBoxCaseDocumentForAssistant(ctx context.Context, in *CreateBlackBoxCaseDocumentForAssistantReq, opts ...grpc.CallOption) (*CreateBlackBoxCaseDocumentForAssistantResp, error)
		// CreateBlackBoxCaseDocumentForSession creates a black box case document for session
		CreateBlackBoxCaseDocumentForSession(ctx context.Context, in *CreateBlackBoxCaseDocumentForSessionReq, opts ...grpc.CallOption) (*CreateBlackBoxCaseDocumentForSessionResp, error)
		// CreateBlackBoxCaseDocumentForSessionRecv creates a black box case document for session recv
		CreateBlackBoxCaseDocumentForSessionRecv(ctx context.Context, in *CreateBlackBoxCaseDocumentForSessionRecvReq, opts ...grpc.CallOption) (*CreateBlackBoxCaseDocumentForSessionRecvResp, error)
		// UpdateBlackBoxCaseDocumentStatusForAssistant update black box case document status for assistant
		UpdateBlackBoxCaseDocumentStatusForAssistant(ctx context.Context, in *UpdateBlackBoxCaseDocumentStatusForAssistantReq, opts ...grpc.CallOption) (*UpdateBlackBoxCaseDocumentStatusForAssistantResp, error)
		// GetBlackBoxCaseDocument gets a black box case document
		GetBlackBoxCaseDocument(ctx context.Context, in *GetBlackBoxCaseDocumentReq, opts ...grpc.CallOption) (*GetBlackBoxCaseDocumentResp, error)
		// UpdateBlackBoxCaseDocument updates a black box case document
		UpdateBlackBoxCaseDocument(ctx context.Context, in *UpdateBlackBoxCaseDocumentReq, opts ...grpc.CallOption) (*UpdateBlackBoxCaseDocumentResp, error)
		// ReloadBlackBoxCaseDocument reload black box case document
		ReloadBlackBoxCaseDocument(ctx context.Context, in *ReloadBlackBoxCaseDocumentReq, opts ...grpc.CallOption) (*ReloadBlackBoxCaseDocumentResp, error)
		// DeleteBlackBoxCaseDocumentForAssistant deletes a black box case document for assistant
		DeleteBlackBoxCaseDocumentForAssistant(ctx context.Context, in *DeleteBlackBoxCaseDocumentForAssistantReq, opts ...grpc.CallOption) (*DeleteBlackBoxCaseDocumentForAssistantResp, error)
		// BatchDeleteBlackBoxCaseDocumentForAssistant batch deletes black box case documents for assistant
		BatchDeleteBlackBoxCaseDocumentForAssistant(ctx context.Context, in *BatchDeleteBlackBoxCaseDocumentForAssistantReq, opts ...grpc.CallOption) (*BatchDeleteBlackBoxCaseDocumentForAssistantResp, error)
		// DeleteBlackBoxCaseDocumentForSession deletes a black box case document for session
		DeleteBlackBoxCaseDocumentForSession(ctx context.Context, in *DeleteBlackBoxCaseDocumentForSessionReq, opts ...grpc.CallOption) (*DeleteBlackBoxCaseDocumentForSessionResp, error)
		// BatchDeleteBlackBoxCaseDocumentForSession batch deletes black box case documents for session
		BatchDeleteBlackBoxCaseDocumentForSession(ctx context.Context, in *BatchDeleteBlackBoxCaseDocumentForSessionReq, opts ...grpc.CallOption) (*BatchDeleteBlackBoxCaseDocumentForSessionResp, error)
		// SearchBlackBoxCaseDocumentForAssistant searches for black box case documents for assistant
		SearchBlackBoxCaseDocumentForAssistant(ctx context.Context, in *SearchBlackBoxCaseDocumentForAssistantReq, opts ...grpc.CallOption) (*SearchBlackBoxCaseDocumentForAssistantResp, error)
		// SearchBlackBoxCaseDocumentForSession searches for black box case documents for session
		SearchBlackBoxCaseDocumentForSession(ctx context.Context, in *SearchBlackBoxCaseDocumentForSessionReq, opts ...grpc.CallOption) (*SearchBlackBoxCaseDocumentForSessionResp, error)
		// GetBlackBoxCaseDocumentHeadersList get black box case document Headers list
		GetBlackBoxCaseDocumentHeadersList(ctx context.Context, in *GetBlackBoxCaseDocumentHeadersListReq, opts ...grpc.CallOption) (*GetBlackBoxCaseDocumentHeadersListResp, error)
	}

	defaultBlackBoxCaseDocumentService struct {
		cli zrpc.Client
	}
)

func NewBlackBoxCaseDocumentService(cli zrpc.Client) BlackBoxCaseDocumentService {
	return &defaultBlackBoxCaseDocumentService{
		cli: cli,
	}
}

// CreateBlackBoxCaseDocumentForAssistant creates a black box case document for assistant
func (m *defaultBlackBoxCaseDocumentService) CreateBlackBoxCaseDocumentForAssistant(ctx context.Context, in *CreateBlackBoxCaseDocumentForAssistantReq, opts ...grpc.CallOption) (*CreateBlackBoxCaseDocumentForAssistantResp, error) {
	client := pb.NewBlackBoxCaseDocumentServiceClient(m.cli.Conn())
	return client.CreateBlackBoxCaseDocumentForAssistant(ctx, in, opts...)
}

// CreateBlackBoxCaseDocumentForSession creates a black box case document for session
func (m *defaultBlackBoxCaseDocumentService) CreateBlackBoxCaseDocumentForSession(ctx context.Context, in *CreateBlackBoxCaseDocumentForSessionReq, opts ...grpc.CallOption) (*CreateBlackBoxCaseDocumentForSessionResp, error) {
	client := pb.NewBlackBoxCaseDocumentServiceClient(m.cli.Conn())
	return client.CreateBlackBoxCaseDocumentForSession(ctx, in, opts...)
}

// CreateBlackBoxCaseDocumentForSessionRecv creates a black box case document for session recv
func (m *defaultBlackBoxCaseDocumentService) CreateBlackBoxCaseDocumentForSessionRecv(ctx context.Context, in *CreateBlackBoxCaseDocumentForSessionRecvReq, opts ...grpc.CallOption) (*CreateBlackBoxCaseDocumentForSessionRecvResp, error) {
	client := pb.NewBlackBoxCaseDocumentServiceClient(m.cli.Conn())
	return client.CreateBlackBoxCaseDocumentForSessionRecv(ctx, in, opts...)
}

// UpdateBlackBoxCaseDocumentStatusForAssistant update black box case document status for assistant
func (m *defaultBlackBoxCaseDocumentService) UpdateBlackBoxCaseDocumentStatusForAssistant(ctx context.Context, in *UpdateBlackBoxCaseDocumentStatusForAssistantReq, opts ...grpc.CallOption) (*UpdateBlackBoxCaseDocumentStatusForAssistantResp, error) {
	client := pb.NewBlackBoxCaseDocumentServiceClient(m.cli.Conn())
	return client.UpdateBlackBoxCaseDocumentStatusForAssistant(ctx, in, opts...)
}

// GetBlackBoxCaseDocument gets a black box case document
func (m *defaultBlackBoxCaseDocumentService) GetBlackBoxCaseDocument(ctx context.Context, in *GetBlackBoxCaseDocumentReq, opts ...grpc.CallOption) (*GetBlackBoxCaseDocumentResp, error) {
	client := pb.NewBlackBoxCaseDocumentServiceClient(m.cli.Conn())
	return client.GetBlackBoxCaseDocument(ctx, in, opts...)
}

// UpdateBlackBoxCaseDocument updates a black box case document
func (m *defaultBlackBoxCaseDocumentService) UpdateBlackBoxCaseDocument(ctx context.Context, in *UpdateBlackBoxCaseDocumentReq, opts ...grpc.CallOption) (*UpdateBlackBoxCaseDocumentResp, error) {
	client := pb.NewBlackBoxCaseDocumentServiceClient(m.cli.Conn())
	return client.UpdateBlackBoxCaseDocument(ctx, in, opts...)
}

// ReloadBlackBoxCaseDocument reload black box case document
func (m *defaultBlackBoxCaseDocumentService) ReloadBlackBoxCaseDocument(ctx context.Context, in *ReloadBlackBoxCaseDocumentReq, opts ...grpc.CallOption) (*ReloadBlackBoxCaseDocumentResp, error) {
	client := pb.NewBlackBoxCaseDocumentServiceClient(m.cli.Conn())
	return client.ReloadBlackBoxCaseDocument(ctx, in, opts...)
}

// DeleteBlackBoxCaseDocumentForAssistant deletes a black box case document for assistant
func (m *defaultBlackBoxCaseDocumentService) DeleteBlackBoxCaseDocumentForAssistant(ctx context.Context, in *DeleteBlackBoxCaseDocumentForAssistantReq, opts ...grpc.CallOption) (*DeleteBlackBoxCaseDocumentForAssistantResp, error) {
	client := pb.NewBlackBoxCaseDocumentServiceClient(m.cli.Conn())
	return client.DeleteBlackBoxCaseDocumentForAssistant(ctx, in, opts...)
}

// BatchDeleteBlackBoxCaseDocumentForAssistant batch deletes black box case documents for assistant
func (m *defaultBlackBoxCaseDocumentService) BatchDeleteBlackBoxCaseDocumentForAssistant(ctx context.Context, in *BatchDeleteBlackBoxCaseDocumentForAssistantReq, opts ...grpc.CallOption) (*BatchDeleteBlackBoxCaseDocumentForAssistantResp, error) {
	client := pb.NewBlackBoxCaseDocumentServiceClient(m.cli.Conn())
	return client.BatchDeleteBlackBoxCaseDocumentForAssistant(ctx, in, opts...)
}

// DeleteBlackBoxCaseDocumentForSession deletes a black box case document for session
func (m *defaultBlackBoxCaseDocumentService) DeleteBlackBoxCaseDocumentForSession(ctx context.Context, in *DeleteBlackBoxCaseDocumentForSessionReq, opts ...grpc.CallOption) (*DeleteBlackBoxCaseDocumentForSessionResp, error) {
	client := pb.NewBlackBoxCaseDocumentServiceClient(m.cli.Conn())
	return client.DeleteBlackBoxCaseDocumentForSession(ctx, in, opts...)
}

// BatchDeleteBlackBoxCaseDocumentForSession batch deletes black box case documents for session
func (m *defaultBlackBoxCaseDocumentService) BatchDeleteBlackBoxCaseDocumentForSession(ctx context.Context, in *BatchDeleteBlackBoxCaseDocumentForSessionReq, opts ...grpc.CallOption) (*BatchDeleteBlackBoxCaseDocumentForSessionResp, error) {
	client := pb.NewBlackBoxCaseDocumentServiceClient(m.cli.Conn())
	return client.BatchDeleteBlackBoxCaseDocumentForSession(ctx, in, opts...)
}

// SearchBlackBoxCaseDocumentForAssistant searches for black box case documents for assistant
func (m *defaultBlackBoxCaseDocumentService) SearchBlackBoxCaseDocumentForAssistant(ctx context.Context, in *SearchBlackBoxCaseDocumentForAssistantReq, opts ...grpc.CallOption) (*SearchBlackBoxCaseDocumentForAssistantResp, error) {
	client := pb.NewBlackBoxCaseDocumentServiceClient(m.cli.Conn())
	return client.SearchBlackBoxCaseDocumentForAssistant(ctx, in, opts...)
}

// SearchBlackBoxCaseDocumentForSession searches for black box case documents for session
func (m *defaultBlackBoxCaseDocumentService) SearchBlackBoxCaseDocumentForSession(ctx context.Context, in *SearchBlackBoxCaseDocumentForSessionReq, opts ...grpc.CallOption) (*SearchBlackBoxCaseDocumentForSessionResp, error) {
	client := pb.NewBlackBoxCaseDocumentServiceClient(m.cli.Conn())
	return client.SearchBlackBoxCaseDocumentForSession(ctx, in, opts...)
}

// GetBlackBoxCaseDocumentHeadersList get black box case document Headers list
func (m *defaultBlackBoxCaseDocumentService) GetBlackBoxCaseDocumentHeadersList(ctx context.Context, in *GetBlackBoxCaseDocumentHeadersListReq, opts ...grpc.CallOption) (*GetBlackBoxCaseDocumentHeadersListResp, error) {
	client := pb.NewBlackBoxCaseDocumentServiceClient(m.cli.Conn())
	return client.GetBlackBoxCaseDocumentHeadersList(ctx, in, opts...)
}
