package server

import (
	"time"

	server2 "gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/server/blackboxgenerationrecordservice"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/conf"
	"github.com/zeromicro/go-zero/core/service"
	"github.com/zeromicro/go-zero/zrpc"
	"google.golang.org/grpc"
	"google.golang.org/grpc/reflection"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/log"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/server"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/config"
	blackboxcaseassistantserviceServer "gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/server/blackboxcaseassistantservice"
	blackBoxCaseDataService "gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/server/blackboxcasedataservice"
	blackBoxCaseDirectoryService "gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/server/blackboxcasedirectoryservice"
	blackBoxCaseDirService "gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/server/blackboxcasedirservice"
	blackboxcasedocumentserviceServer "gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/server/blackboxcasedocumentservice"
	blackBoxCaseKnowledgeService "gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/server/blackboxcaseknowledgeservice"
	blackBoxCaseKnowledgeV2Service "gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/server/blackboxcaseknowledgev2service"
	blackBoxCaseMapDocumentService "gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/server/blackboxcasemapdocumentservice"
	blackBoxCaseMapService "gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/server/blackboxcasemapservice"
	blackBoxCaseRevisionService "gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/server/blackboxcaserevisionservice"
	blackBoxCaseService "gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/server/blackboxcaseservice"
	blackboxcasesessionserviceServer "gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/server/blackboxcasesessionservice"
	blackboxcasetwbetaservice "gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/server/blackboxcasetwbetaservice"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
)

// NewRpcServer for single server startup
func NewRpcServer(configFile string) (*server.Server, error) {
	return server.NewServer(Options(configFile)...)
}

// NewServer for combine server startup
func NewServer(c server.Config, w *log.ZapWriter) (service.Service, error) {
	cc, ok := c.(config.Config)
	if !ok {
		return nil, errors.Errorf("failed to new rpc server, cause by the config[%T] isn't a rpc config", c)
	}

	ctx := svc.NewServiceContext(cc)
	s := zrpc.MustNewServer(
		cc.RpcServerConf, func(grpcServer *grpc.Server) {
			pb.RegisterBlackBoxCaseAssistantServiceServer(
				grpcServer, blackboxcaseassistantserviceServer.NewBlackBoxCaseAssistantServiceServer(ctx),
			)
			pb.RegisterBlackBoxCaseDocumentServiceServer(
				grpcServer, blackboxcasedocumentserviceServer.NewBlackBoxCaseDocumentServiceServer(ctx),
			)
			pb.RegisterBlackBoxCaseSessionServiceServer(
				grpcServer, blackboxcasesessionserviceServer.NewBlackBoxCaseSessionServiceServer(ctx),
			)
			pb.RegisterBlackBoxCaseDirServiceServer(
				grpcServer, blackBoxCaseDirService.NewBlackBoxCaseDirServiceServer(ctx),
			)
			pb.RegisterBlackBoxCaseKnowledgeServiceServer(
				grpcServer, blackBoxCaseKnowledgeService.NewBlackBoxCaseKnowledgeServiceServer(ctx),
			)
			pb.RegisterBlackBoxCaseServiceServer(
				grpcServer, blackBoxCaseService.NewBlackBoxCaseServiceServer(ctx),
			)
			pb.RegisterBlackBoxCaseRevisionServiceServer(
				grpcServer, blackBoxCaseRevisionService.NewBlackBoxCaseRevisionServiceServer(ctx),
			)
			pb.RegisterBlackBoxCaseDataServiceServer(
				grpcServer, blackBoxCaseDataService.NewBlackBoxCaseDataServiceServer(ctx),
			)
			pb.RegisterBlackBoxCaseTwBetaServiceServer(
				grpcServer, blackboxcasetwbetaservice.NewBlackBoxCaseTwBetaServiceServer(ctx),
			)
			pb.RegisterBlackBoxCaseDirectoryServiceServer(
				grpcServer, blackBoxCaseDirectoryService.NewBlackBoxCaseDirectoryServiceServer(ctx),
			)
			pb.RegisterBlackBoxCaseKnowledgeV2ServiceServer(
				grpcServer, blackBoxCaseKnowledgeV2Service.NewBlackBoxCaseKnowledgeV2ServiceServer(ctx),
			)
			pb.RegisterBlackBoxCaseMapServiceServer(
				grpcServer, blackBoxCaseMapService.NewBlackBoxCaseMapServiceServer(ctx),
			)
			pb.RegisterBlackBoxCaseMapDocumentServiceServer(
				grpcServer, blackBoxCaseMapDocumentService.NewBlackBoxCaseMapDocumentServiceServer(ctx),
			)
			pb.RegisterBlackBoxGenerationRecordServiceServer(
				grpcServer, server2.NewBlackBoxGenerationRecordServiceServer(ctx),
			)
			if cc.Mode == service.DevMode || cc.Mode == service.TestMode {
				reflection.Register(grpcServer)
			}
		},
	)

	if err := internal.SetupOperation(ctx, true); err != nil {
		return nil, errors.Errorf("failed to setup operation, error: %+v", err)
	}

	// 需要在 `ServiceConf.SetUp` 后再设置 `Writer`
	log.SetWriter(w)

	err := internal.MetricsHandler(ctx)
	if err != nil {
		return nil, errors.Errorf("failed to init metrics, error: %+v", err)
	}

	// 每天零点清理redis缓存中的统计数据
	go func() {
		for {
			// 计算到下一个午夜的时间
			now := time.Now()
			nextMidnight := time.Date(now.Year(), now.Month(), now.Day()+1, 0, 0, 0, 0, now.Location())
			// nextMidnight := time.Date(now.Year(), now.Month(), now.Day(), now.Hour(), now.Minute() + 1, now.Second(), 0, now.Location())
			if now.After(nextMidnight) { // 如果现在已经是第二天了，就计算到后天的午夜
				nextMidnight = nextMidnight.Add(24 * time.Hour)
			}

			// 设置定时器
			timer := time.NewTimer(nextMidnight.Sub(now))

			// 等待定时器触发
			<-timer.C
			err = internal.MetricsReset(ctx)
			if err != nil {
				println("初始化指标异常:", err)
			}
		}
	}()

	return s, nil
}

// NewConfig new a config of server
func NewConfig(configFile string) server.Config {
	var c config.Config
	conf.MustLoad(configFile, &c)

	return c
}

// Options as a param of `server.NewServer`
func Options(configFile string) []server.Option {
	return []server.Option{
		server.WithNewConfigFunc(
			func() server.Config {
				return NewConfig(configFile)
			},
		),
		server.WithNewServiceFunc(NewServer),
	}
}
