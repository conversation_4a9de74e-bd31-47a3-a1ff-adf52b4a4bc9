Name: rpc.ai
Mode: test
ListenOn: 127.0.0.1:21011
Timeout: 0
  # MethodTimeouts:
  #   - FullMethod: /ai.ApiCaseService/ModifyApiCase
  #     Timeout: 8s
  #   - FullMethod: /ai.UiCaseService/ModifyUiCase
  #     Timeout: 8s
  #   - FullMethod: /ai.UiCaseService/RemoveUiCase
  #     Timeout: 8s
  #   - FullMethod: /ai.UiCaseService/ViewUiCase
  #     Timeout: 8s
  #   - FullMethod: /ai.InterfaceDefinitionService/LocalImportInterfaceDefinition
#     Timeout: 4.5m

Log:
  ServiceName: rpc.ai
  Encoding: plain
  Level: info
  Path: /app/logs/ai

Prometheus:
  Host: 0.0.0.0
  Port: 21022
  Path: /metrics

Telemetry:
  Name: rpc.ai
  Endpoint: ap-beijing.apm.tencentcs.com:4317
  Sampler: 1.0
  Batcher: otlpgrpc

DevServer:
  Enabled: true
  Port: 21032

#Etcd:
#  Hosts:
#    - ***************:2379
#  Key: rpc.ai

Redis:
  Key: rpc.ai
  Host: ************:6379
  Type: node
  DB: 19

Cache:
  - Host: ************:6379
    DB: 19

DB:
  DataSource: probe:Quwan@2020_TTinternation@tcp(************:3306)/artificial_intelligence?charset=utf8mb4&parseTime=true&loc=Asia%2FShanghai

UserRpc:
  #Etcd:
  #  Hosts:
  #    - ***************:2379
  #  Key: rpc.user
  Endpoints:
    - 127.0.0.1:10111
  NonBlock: true
  Timeout: 0

#ExternalAiDomain: http://*************:8000
ExternalAiDomain: http://probe-test.ttyuyin.com:8000


