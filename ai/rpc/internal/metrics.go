package internal

import (
	"context"
	"encoding/json"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/stores/redis"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/common/cache"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/metrics"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/public/util"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/userinfo"
)

// 初始化指标
func MetricsHandler(serviceContext *svc.ServiceContext) error {
	ctx := context.TODO()
	// 初始化采纳率指标
	adoptedTotal, err := serviceContext.BlackBoxCaseDataModel.FindCountByAdopted(ctx)
	if err != nil {
		logx.Error(err)
		return err
	}
	adoptedUpdatedTotal, err := serviceContext.BlackBoxCaseDataModel.FindCountByUpdatedAdopted(ctx)
	if err != nil {
		logx.Error(err)
		return err
	}
	// 被修改过的用例数据
	metrics.CaseDataAdoptTotalGauge.Set(float64(adoptedUpdatedTotal), []string{"true"}...)
	// 未被修改过的,采纳的数据
	metrics.CaseDataAdoptTotalGauge.Set(float64(adoptedTotal-adoptedUpdatedTotal), []string{"false"}...)

	// 初始化用例生成数指标
	for _, CaseRefType := range []string{"edit", "generate", "partial_edit"} {
		total, err := serviceContext.BlackBoxCaseRefModel.FindCountByType(ctx, CaseRefType)
		if err != nil {
			logx.Error(err)
			return err
		}
		metrics.CaseRefTotalGauge.Set(float64(total), []string{CaseRefType}...)
	}

	// 初始化用例数指标
	total, err := serviceContext.BlackBoxCaseModel.FindCount(ctx, serviceContext.BlackBoxCaseModel.SelectCountAllBuilder())
	if err != nil {
		logx.Error(err)
		return err
	}
	metrics.CaseTotalGauge.Set(float64(total), []string{""}...)

	// 初始化查询次数指标
	total, err = serviceContext.BlackBoxCaseModel.CountQueryTimes(ctx)
	if err != nil {
		logx.Error(err)
	}
	metrics.CaseQueryTotalGauge.Set(float64(total), []string{""}...)

	// 初始化生成用例行数总数指标
	total, err = serviceContext.BlackBoxCaseRefModel.GetSumRefDataCount(ctx)
	if err != nil {
		logx.Error(err)
	}
	metrics.CaseDataTotalGauge.Set(float64(total), []string{""}...)

	// 初始化查询采纳用例总数指标
	revisonList, err := serviceContext.BlackBoxCaseRevisionModel.FindCaseIdByAdopted(ctx)
	if err != nil {
		logx.Error(err)
	}
	var caseIds []string
	var revisionIds []string
	for _, revision := range revisonList {
		caseIds = append(caseIds, revision.CaseId)
		revisionIds = append(revisionIds, revision.RevisionId)
	}
	caseIds = stringx.Distinct(caseIds)
	revisionIds = stringx.Distinct(revisionIds)
	serviceContext.Redis.Set("ai_adopted_revision_ids", strings.Join(revisionIds, ","))
	metrics.CaseAdoptTotalGauge.Set(float64(len(caseIds)), []string{""}...)

	// 初始化用户访问指标
	viewerStr, err := serviceContext.Redis.Get(metrics.PVKey)
	if err != nil {
		logx.Error(err)
	}
	var viewerList []*userinfo.UserInfo
	if len(viewerStr) > 0 {
		err = json.Unmarshal([]byte(viewerStr), &viewerList)
		if err != nil {
			logx.Error(err)
		}
		for _, viewer := range viewerList {
			metrics.CaseListTotalGauge.Set(float64(1), viewer.Account, viewer.Fullname)
		}
	}

	// 初始化访问用户数指标
	viewerListStr, err := serviceContext.Redis.Get(metrics.PVUserKey)
	if err != nil {
		logx.Error(err)
	}

	var viewers []string
	if len(viewerListStr) > 0 {
		viewers = strings.Split(viewerListStr, ",")
	}
	metrics.CaseListUsersTotalGauge.Set(float64(len(viewers)), "")

	return nil
}

func MetricsReset(serviceContext *svc.ServiceContext) error {
	logx.Info("开始重置PV数据")
	viewerStr, err := serviceContext.Redis.Get(metrics.PVKey)
	if err != nil {
		return nil
	}
	var viewerList []*userinfo.UserInfo
	if len(viewerStr) > 0 {
		err = json.Unmarshal([]byte(viewerStr), &viewerList)
		if err != nil {
			return nil
		}
		for _, viewer := range viewerList {
			metrics.CaseListTotalGauge.Set(float64(0), viewer.Account, viewer.Fullname)
		}
	}

	err = serviceContext.Redis.Set(metrics.PVKey, "")
	if err != nil {
		return err
	}

	// 重置用户统计
	err = serviceContext.Redis.Set(metrics.PVUserKey, "")
	if err != nil {
		return err
	}
	metrics.CaseListUsersTotalGauge.Set(float64(0), "")

	logx.Info("重置PV数据完成")
	return nil
}

func UpdateMetricsRedis(serviceContext *svc.ServiceContext, userInfo *userinfo.UserInfo) error {
	viewerStr, err := serviceContext.Redis.Get(metrics.PVKey)
	if err != nil {
		return nil
	}
	var viewerList []*userinfo.UserInfo
	if len(viewerStr) > 0 {
		err = json.Unmarshal([]byte(viewerStr), &viewerList)
		if err != nil {
			return nil
		}
		for _, viewer := range viewerList {
			if viewer.Account == userInfo.Account {
				return nil
			}
		}
	}
	// 循环结束说明当前数据中没有该用户指标
	viewerList = append(viewerList, userInfo)
	newViewerList, err := json.Marshal(viewerList)
	if err != nil {
		return err
	}
	return serviceContext.Redis.Set(metrics.PVKey, string(newViewerList))
}

func UpdateUsersMetrics(serviceContext *svc.ServiceContext, userInfo *userinfo.UserInfo) error {
	viewerStr, err := serviceContext.Redis.Get(metrics.PVUserKey)
	if err != nil {
		return nil
	}

	var viewers []string
	if len(viewerStr) > 0 {
		viewers = strings.Split(viewerStr, ",")
	}
	viewers = append(viewers, userInfo.Account)
	viewers = util.RemoveDuplicates(viewers)

	err = serviceContext.Redis.Set(metrics.PVUserKey, strings.Join(viewers, ","))
	if err != nil {
		return err
	}
	metrics.CaseListUsersTotalGauge.Set(float64(len(viewers)), "")
	return serviceContext.Redis.Set(metrics.PVUserKey, strings.Join(viewers, ","))
}

/**
用户相关指标
set: 用于对单个用户做去重
zset: score为登陆的时间戳, member为用户信息
*/

// AddUserPvMetricsRedis .
func AddUserPvMetricsRedis(ctx context.Context, serviceContext *svc.ServiceContext, userInfo *userinfo.UserInfo) error {
	dateKey := time.Now().Format("2006-01-02")
	dailyUsersKey := cache.UserLoginDuplicateKeyPrefix + dateKey

	return serviceContext.Redis.PipelinedCtx(ctx, func(pipe redis.Pipeliner) error {
		// 仅保留第一次登录的情况
		isMember, err := serviceContext.Redis.Sismember(dailyUsersKey, userInfo.Fullname)
		if err != nil {
			return err
		}

		if !isMember {
			score := time.Now().Unix()
			pipe.ZAdd(ctx, cache.UserLoginCacheKey, redis.Z{Score: float64(score), Member: userInfo.Fullname})
			pipe.SAdd(ctx, dailyUsersKey, userInfo.Fullname)
		}
		return nil
	})
}

func FilterUserLogins(ctx context.Context, serviceContext *svc.ServiceContext, startDate, endDate time.Time) ([]string, error) {
	startScore := startDate.Unix()
	endScore := endDate.Unix()
	results, err := serviceContext.Redis.ZrangebyscoreWithScores(cache.UserLoginCacheKey, startScore, endScore)
	if err != nil {
		return nil, err
	}

	users := make([]string, 0)
	for _, result := range results {
		users = append(users, result.Key)
	}

	return users, nil
}
