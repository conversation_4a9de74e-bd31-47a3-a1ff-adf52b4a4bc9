// Code generated by goctl. DO NOT EDIT.
// goctl 1.7.5
// Source: ai.proto

package server

import (
	"context"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"

	blackboxcasemapdocumentservicelogic "gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/logic/blackboxcasemapdocumentservice"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
)

type BlackBoxCaseMapDocumentServiceServer struct {
	svcCtx *svc.ServiceContext
	pb.UnimplementedBlackBoxCaseMapDocumentServiceServer
}

func NewBlackBoxCaseMapDocumentServiceServer(svcCtx *svc.ServiceContext) *BlackBoxCaseMapDocumentServiceServer {
	return &BlackBoxCaseMapDocumentServiceServer{
		svcCtx: svcCtx,
	}
}

// ListBlackBoxCaseMapDocuments list blackbox case map documents
func (s *BlackBoxCaseMapDocumentServiceServer) ListBlackBoxCaseMapDocuments(ctx context.Context, in *pb.ListBlackBoxCaseMapDocumentsReq) (*pb.ListBlackBoxCaseMapDocumentsResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := blackboxcasemapdocumentservicelogic.NewListBlackBoxCaseMapDocumentsLogic(ctx, s.svcCtx)

	return l.ListBlackBoxCaseMapDocuments(in)
}

// GetBlackBoxCaseMapDocument get blackbox case map document
func (s *BlackBoxCaseMapDocumentServiceServer) GetBlackBoxCaseMapDocument(ctx context.Context, in *pb.GetBlackBoxCaseMapDocumentReq) (*pb.BlackBoxCaseMapDocument, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := blackboxcasemapdocumentservicelogic.NewGetBlackBoxCaseMapDocumentLogic(ctx, s.svcCtx)

	return l.GetBlackBoxCaseMapDocument(in)
}

// UpdateBlackBoxCaseMapDocument update blackbox case map document
func (s *BlackBoxCaseMapDocumentServiceServer) UpdateBlackBoxCaseMapDocument(ctx context.Context, in *pb.BlackBoxCaseMapDocument) (*pb.UpdateBlackBoxCaseMapDocumentResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := blackboxcasemapdocumentservicelogic.NewUpdateBlackBoxCaseMapDocumentLogic(ctx, s.svcCtx)

	return l.UpdateBlackBoxCaseMapDocument(in)
}

// DeleteBlackBoxCaseMapDocument delete blackbox case map document
func (s *BlackBoxCaseMapDocumentServiceServer) DeleteBlackBoxCaseMapDocument(ctx context.Context, in *pb.DeleteBlackBoxCaseMapDocumentReq) (*pb.DeleteBlackBoxCaseMapDocumentResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := blackboxcasemapdocumentservicelogic.NewDeleteBlackBoxCaseMapDocumentLogic(ctx, s.svcCtx)

	return l.DeleteBlackBoxCaseMapDocument(in)
}

// UpdateBlackBoxCaseMapRecallDocument update blackbox case map document
func (s *BlackBoxCaseMapDocumentServiceServer) UpdateBlackBoxCaseMapRecallDocument(ctx context.Context, in *pb.UpdateBlackBoxCaseMapRecallDocumentReq) (*pb.UpdateBlackBoxCaseMapRecallDocumentResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := blackboxcasemapdocumentservicelogic.NewUpdateBlackBoxCaseMapRecallDocumentLogic(ctx, s.svcCtx)

	return l.UpdateBlackBoxCaseMapRecallDocument(in)
}
