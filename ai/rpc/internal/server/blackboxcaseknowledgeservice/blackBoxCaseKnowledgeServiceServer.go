// Code generated by goctl. DO NOT EDIT.
// goctl 1.7.5
// Source: ai.proto

package server

import (
	"context"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"

	blackboxcaseknowledgeservicelogic "gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/logic/blackboxcaseknowledgeservice"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
)

type BlackBoxCaseKnowledgeServiceServer struct {
	svcCtx *svc.ServiceContext
	pb.UnimplementedBlackBoxCaseKnowledgeServiceServer
}

func NewBlackBoxCaseKnowledgeServiceServer(svcCtx *svc.ServiceContext) *BlackBoxCaseKnowledgeServiceServer {
	return &BlackBoxCaseKnowledgeServiceServer{
		svcCtx: svcCtx,
	}
}

// CreateBlackBoxCaseKnowledge create a black box case
func (s *BlackBoxCaseKnowledgeServiceServer) CreateBlackBoxCaseKnowledge(ctx context.Context, in *pb.CreateBlackBoxCaseKnowledgeReq) (*pb.CreateBlackBoxCaseKnowledgeResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := blackboxcaseknowledgeservicelogic.NewCreateBlackBoxCaseKnowledgeLogic(ctx, s.svcCtx)

	return l.CreateBlackBoxCaseKnowledge(in)
}

// DeleteBlackBoxCaseKnowledge create a black box case
func (s *BlackBoxCaseKnowledgeServiceServer) DeleteBlackBoxCaseKnowledge(ctx context.Context, in *pb.DeleteBlackBoxCaseKnowledgeReq) (*pb.DeleteBlackBoxCaseKnowledgeResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := blackboxcaseknowledgeservicelogic.NewDeleteBlackBoxCaseKnowledgeLogic(ctx, s.svcCtx)

	return l.DeleteBlackBoxCaseKnowledge(in)
}

// UpdateBlackBoxCaseKnowledge create a black box case
func (s *BlackBoxCaseKnowledgeServiceServer) UpdateBlackBoxCaseKnowledge(ctx context.Context, in *pb.UpdateBlackBoxCaseKnowledgeReq) (*pb.UpdateBlackBoxCaseKnowledgeResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := blackboxcaseknowledgeservicelogic.NewUpdateBlackBoxCaseKnowledgeLogic(ctx, s.svcCtx)

	return l.UpdateBlackBoxCaseKnowledge(in)
}

// GetBlackBoxCaseKnowledge gets a black box case
func (s *BlackBoxCaseKnowledgeServiceServer) GetBlackBoxCaseKnowledge(ctx context.Context, in *pb.GetBlackBoxCaseKnowledgeReq) (*pb.GetBlackBoxCaseKnowledgeResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := blackboxcaseknowledgeservicelogic.NewGetBlackBoxCaseKnowledgeLogic(ctx, s.svcCtx)

	return l.GetBlackBoxCaseKnowledge(in)
}

// ListBlackBoxCaseKnowledge list black box case
func (s *BlackBoxCaseKnowledgeServiceServer) ListBlackBoxCaseKnowledge(ctx context.Context, in *pb.ListBlackBoxCaseKnowledgeReq) (*pb.ListBlackBoxCaseKnowledgeResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := blackboxcaseknowledgeservicelogic.NewListBlackBoxCaseKnowledgeLogic(ctx, s.svcCtx)

	return l.ListBlackBoxCaseKnowledge(in)
}

// ReloadBlackBoxCaseKnowledge list black box case
func (s *BlackBoxCaseKnowledgeServiceServer) ReloadBlackBoxCaseKnowledge(ctx context.Context, in *pb.ReloadBlackBoxCaseKnowledgeReq) (*pb.ReloadBlackBoxCaseKnowledgeResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := blackboxcaseknowledgeservicelogic.NewReloadBlackBoxCaseKnowledgeLogic(ctx, s.svcCtx)

	return l.ReloadBlackBoxCaseKnowledge(in)
}
