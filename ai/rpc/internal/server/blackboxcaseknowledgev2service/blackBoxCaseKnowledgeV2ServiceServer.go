// Code generated by goctl. DO NOT EDIT.
// goctl 1.7.5
// Source: ai.proto

package server

import (
	"context"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"

	blackboxcaseknowledgev2servicelogic "gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/logic/blackboxcaseknowledgev2service"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
)

type BlackBoxCaseKnowledgeV2ServiceServer struct {
	svcCtx *svc.ServiceContext
	pb.UnimplementedBlackBoxCaseKnowledgeV2ServiceServer
}

func NewBlackBoxCaseKnowledgeV2ServiceServer(svcCtx *svc.ServiceContext) *BlackBoxCaseKnowledgeV2ServiceServer {
	return &BlackBoxCaseKnowledgeV2ServiceServer{
		svcCtx: svcCtx,
	}
}

// GetBlackBoxCaseKnowledge get blackbox case knowledge
func (s *BlackBoxCaseKnowledgeV2ServiceServer) GetBlackBoxCaseKnowledge(ctx context.Context, in *pb.GetBlackBoxCaseKnowledgeV2Req) (*pb.GetBlackBoxCaseKnowledgeV2Resp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := blackboxcaseknowledgev2servicelogic.NewGetBlackBoxCaseKnowledgeLogic(ctx, s.svcCtx)

	return l.GetBlackBoxCaseKnowledge(in)
}

// UpdateBlackBoxCaseKnowledge update blackbox case knowledge
func (s *BlackBoxCaseKnowledgeV2ServiceServer) UpdateBlackBoxCaseKnowledge(ctx context.Context, in *pb.UpdateBlackBoxCaseKnowledgeV2Req) (*pb.UpdateBlackBoxCaseKnowledgeV2Resp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := blackboxcaseknowledgev2servicelogic.NewUpdateBlackBoxCaseKnowledgeLogic(ctx, s.svcCtx)

	return l.UpdateBlackBoxCaseKnowledge(in)
}

// UpdateBlackBoxCaseMapUsedKnowledge update blackbox case knowledge
func (s *BlackBoxCaseKnowledgeV2ServiceServer) UpdateBlackBoxCaseMapUsedKnowledge(ctx context.Context, in *pb.UpdateBlackBoxCaseMapUsedKnowledgeReq) (*pb.UpdateBlackBoxCaseMapUsedKnowledgeResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := blackboxcaseknowledgev2servicelogic.NewUpdateBlackBoxCaseMapUsedKnowledgeLogic(ctx, s.svcCtx)

	return l.UpdateBlackBoxCaseMapUsedKnowledge(in)
}
