// Code generated by goctl. DO NOT EDIT.
// goctl 1.7.5
// Source: ai.proto

package server

import (
	"context"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"

	blackboxcasetwbetaservicelogic "gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/logic/blackboxcasetwbetaservice"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
)

type BlackBoxCaseTwBetaServiceServer struct {
	svcCtx *svc.ServiceContext
	pb.UnimplementedBlackBoxCaseTwBetaServiceServer
}

func NewBlackBoxCaseTwBetaServiceServer(svcCtx *svc.ServiceContext) *BlackBoxCaseTwBetaServiceServer {
	return &BlackBoxCaseTwBetaServiceServer{
		svcCtx: svcCtx,
	}
}

// GetBlackBoxCaseTwBetaCaseContent
func (s *BlackBoxCaseTwBetaServiceServer) GetBlackBoxCaseTwBetaCaseContent(ctx context.Context, in *pb.GetBlackBoxCaseTwBetaContentReq) (*pb.GetBlackBoxCaseTwBetaContentResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := blackboxcasetwbetaservicelogic.NewGetBlackBoxCaseTwBetaCaseContentLogic(ctx, s.svcCtx)

	return l.GetBlackBoxCaseTwBetaCaseContent(in)
}

// UpdateBlackBoxCaseTwBetaMindContent
func (s *BlackBoxCaseTwBetaServiceServer) UpdateBlackBoxCaseTwBetaMindContent(ctx context.Context, in *pb.UpdateBlackBoxCaseMindTwBetaReq) (*pb.UpdateBlackBoxCaseMindTwBetaResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := blackboxcasetwbetaservicelogic.NewUpdateBlackBoxCaseTwBetaMindContentLogic(ctx, s.svcCtx)

	return l.UpdateBlackBoxCaseTwBetaMindContent(in)
}

// CreateBlackBoxCaseTwBeta list black box case of tw beta
func (s *BlackBoxCaseTwBetaServiceServer) CreateBlackBoxCaseTwBeta(ctx context.Context, in *pb.CreateBlackBoxCaseTwBetaReq) (*pb.CreateBlackBoxCaseTwBetaResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := blackboxcasetwbetaservicelogic.NewCreateBlackBoxCaseTwBetaLogic(ctx, s.svcCtx)

	return l.CreateBlackBoxCaseTwBeta(in)
}

// ListBlackBoxCaseTwBeta list black box case of tw beta
func (s *BlackBoxCaseTwBetaServiceServer) ListBlackBoxCaseTwBeta(ctx context.Context, in *pb.ListBlackBoxCaseTwBetaReq) (*pb.ListBlackBoxCaseTwBetaResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := blackboxcasetwbetaservicelogic.NewListBlackBoxCaseTwBetaLogic(ctx, s.svcCtx)

	return l.ListBlackBoxCaseTwBeta(in)
}

// CreateBlackBoxCaseTwBetaMind create black box case of tw beta mind
func (s *BlackBoxCaseTwBetaServiceServer) CreateBlackBoxCaseTwBetaMind(ctx context.Context, in *pb.CreateBlackBoxCaseTwBetaMindReq) (*pb.CreateBlackBoxCaseTwBetaMindResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := blackboxcasetwbetaservicelogic.NewCreateBlackBoxCaseTwBetaMindLogic(ctx, s.svcCtx)

	return l.CreateBlackBoxCaseTwBetaMind(in)
}

// ListBlackBoxCaseTwBetaMind list black box case of tw beta mind
func (s *BlackBoxCaseTwBetaServiceServer) ListBlackBoxCaseTwBetaMind(ctx context.Context, in *pb.ListBlackBoxCaseTwBetaMindReq) (*pb.ListBlackBoxCaseTwBetaMindResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := blackboxcasetwbetaservicelogic.NewListBlackBoxCaseTwBetaMindLogic(ctx, s.svcCtx)

	return l.ListBlackBoxCaseTwBetaMind(in)
}

// GetBlackBoxCaseTwBetaMindContent get black box case of tw beta mind content
func (s *BlackBoxCaseTwBetaServiceServer) GetBlackBoxCaseTwBetaMindContent(ctx context.Context, in *pb.GetBlackBoxCaseTwBetaMindContentReq) (*pb.GetBlackBoxCaseTwBetaMindContentResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := blackboxcasetwbetaservicelogic.NewGetBlackBoxCaseTwBetaMindContentLogic(ctx, s.svcCtx)

	return l.GetBlackBoxCaseTwBetaMindContent(in)
}

// ListBlackBoxCaseTwBetaMindCheck list black box case of tw beta mind check
func (s *BlackBoxCaseTwBetaServiceServer) ListBlackBoxCaseTwBetaMindCheck(ctx context.Context, in *pb.ListBlackBoxCaseTwBetaMindCheckReq) (*pb.ListBlackBoxCaseTwBetaMindCheckResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := blackboxcasetwbetaservicelogic.NewListBlackBoxCaseTwBetaMindCheckLogic(ctx, s.svcCtx)

	return l.ListBlackBoxCaseTwBetaMindCheck(in)
}

// DeleteBlackBoxCaseTwBeta delete black box case of tw beta
func (s *BlackBoxCaseTwBetaServiceServer) DeleteBlackBoxCaseTwBeta(ctx context.Context, in *pb.DeleteBlackBoxCaseTwBetaReq) (*pb.DeleteBlackBoxCaseTwBetaResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := blackboxcasetwbetaservicelogic.NewDeleteBlackBoxCaseTwBetaLogic(ctx, s.svcCtx)

	return l.DeleteBlackBoxCaseTwBeta(in)
}
