// Code generated by goctl. DO NOT EDIT.
// goctl 1.7.5
// Source: ai.proto

package server

import (
	"context"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"

	blackboxcasedocumentservicelogic "gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/logic/blackboxcasedocumentservice"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
)

type BlackBoxCaseDocumentServiceServer struct {
	svcCtx *svc.ServiceContext
	pb.UnimplementedBlackBoxCaseDocumentServiceServer
}

func NewBlackBoxCaseDocumentServiceServer(svcCtx *svc.ServiceContext) *BlackBoxCaseDocumentServiceServer {
	return &BlackBoxCaseDocumentServiceServer{
		svcCtx: svcCtx,
	}
}

// CreateBlackBoxCaseDocumentForAssistant creates a black box case document for assistant
func (s *BlackBoxCaseDocumentServiceServer) CreateBlackBoxCaseDocumentForAssistant(ctx context.Context, in *pb.CreateBlackBoxCaseDocumentForAssistantReq) (*pb.CreateBlackBoxCaseDocumentForAssistantResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := blackboxcasedocumentservicelogic.NewCreateBlackBoxCaseDocumentForAssistantLogic(ctx, s.svcCtx)

	return l.CreateBlackBoxCaseDocumentForAssistant(in)
}

// CreateBlackBoxCaseDocumentForSession creates a black box case document for session
func (s *BlackBoxCaseDocumentServiceServer) CreateBlackBoxCaseDocumentForSession(ctx context.Context, in *pb.CreateBlackBoxCaseDocumentForSessionReq) (*pb.CreateBlackBoxCaseDocumentForSessionResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := blackboxcasedocumentservicelogic.NewCreateBlackBoxCaseDocumentForSessionLogic(ctx, s.svcCtx)

	return l.CreateBlackBoxCaseDocumentForSession(in)
}

// CreateBlackBoxCaseDocumentForSessionRecv creates a black box case document for session recv
func (s *BlackBoxCaseDocumentServiceServer) CreateBlackBoxCaseDocumentForSessionRecv(ctx context.Context, in *pb.CreateBlackBoxCaseDocumentForSessionRecvReq) (*pb.CreateBlackBoxCaseDocumentForSessionRecvResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := blackboxcasedocumentservicelogic.NewCreateBlackBoxCaseDocumentForSessionRecvLogic(ctx, s.svcCtx)

	return l.CreateBlackBoxCaseDocumentForSessionRecv(in)
}

// UpdateBlackBoxCaseDocumentStatusForAssistant update black box case document status for assistant
func (s *BlackBoxCaseDocumentServiceServer) UpdateBlackBoxCaseDocumentStatusForAssistant(ctx context.Context, in *pb.UpdateBlackBoxCaseDocumentStatusForAssistantReq) (*pb.UpdateBlackBoxCaseDocumentStatusForAssistantResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := blackboxcasedocumentservicelogic.NewUpdateBlackBoxCaseDocumentStatusForAssistantLogic(ctx, s.svcCtx)

	return l.UpdateBlackBoxCaseDocumentStatusForAssistant(in)
}

// GetBlackBoxCaseDocument gets a black box case document
func (s *BlackBoxCaseDocumentServiceServer) GetBlackBoxCaseDocument(ctx context.Context, in *pb.GetBlackBoxCaseDocumentReq) (*pb.GetBlackBoxCaseDocumentResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := blackboxcasedocumentservicelogic.NewGetBlackBoxCaseDocumentLogic(ctx, s.svcCtx)

	return l.GetBlackBoxCaseDocument(in)
}

// UpdateBlackBoxCaseDocument updates a black box case document
func (s *BlackBoxCaseDocumentServiceServer) UpdateBlackBoxCaseDocument(ctx context.Context, in *pb.UpdateBlackBoxCaseDocumentReq) (*pb.UpdateBlackBoxCaseDocumentResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := blackboxcasedocumentservicelogic.NewUpdateBlackBoxCaseDocumentLogic(ctx, s.svcCtx)

	return l.UpdateBlackBoxCaseDocument(in)
}

// ReloadBlackBoxCaseDocument reload black box case document
func (s *BlackBoxCaseDocumentServiceServer) ReloadBlackBoxCaseDocument(ctx context.Context, in *pb.ReloadBlackBoxCaseDocumentReq) (*pb.ReloadBlackBoxCaseDocumentResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := blackboxcasedocumentservicelogic.NewReloadBlackBoxCaseDocumentLogic(ctx, s.svcCtx)

	return l.ReloadBlackBoxCaseDocument(in)
}

// DeleteBlackBoxCaseDocumentForAssistant deletes a black box case document for assistant
func (s *BlackBoxCaseDocumentServiceServer) DeleteBlackBoxCaseDocumentForAssistant(ctx context.Context, in *pb.DeleteBlackBoxCaseDocumentForAssistantReq) (*pb.DeleteBlackBoxCaseDocumentForAssistantResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := blackboxcasedocumentservicelogic.NewDeleteBlackBoxCaseDocumentForAssistantLogic(ctx, s.svcCtx)

	return l.DeleteBlackBoxCaseDocumentForAssistant(in)
}

// BatchDeleteBlackBoxCaseDocumentForAssistant batch deletes black box case documents for assistant
func (s *BlackBoxCaseDocumentServiceServer) BatchDeleteBlackBoxCaseDocumentForAssistant(ctx context.Context, in *pb.BatchDeleteBlackBoxCaseDocumentForAssistantReq) (*pb.BatchDeleteBlackBoxCaseDocumentForAssistantResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := blackboxcasedocumentservicelogic.NewBatchDeleteBlackBoxCaseDocumentForAssistantLogic(ctx, s.svcCtx)

	return l.BatchDeleteBlackBoxCaseDocumentForAssistant(in)
}

// DeleteBlackBoxCaseDocumentForSession deletes a black box case document for session
func (s *BlackBoxCaseDocumentServiceServer) DeleteBlackBoxCaseDocumentForSession(ctx context.Context, in *pb.DeleteBlackBoxCaseDocumentForSessionReq) (*pb.DeleteBlackBoxCaseDocumentForSessionResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := blackboxcasedocumentservicelogic.NewDeleteBlackBoxCaseDocumentForSessionLogic(ctx, s.svcCtx)

	return l.DeleteBlackBoxCaseDocumentForSession(in)
}

// BatchDeleteBlackBoxCaseDocumentForSession batch deletes black box case documents for session
func (s *BlackBoxCaseDocumentServiceServer) BatchDeleteBlackBoxCaseDocumentForSession(ctx context.Context, in *pb.BatchDeleteBlackBoxCaseDocumentForSessionReq) (*pb.BatchDeleteBlackBoxCaseDocumentForSessionResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := blackboxcasedocumentservicelogic.NewBatchDeleteBlackBoxCaseDocumentForSessionLogic(ctx, s.svcCtx)

	return l.BatchDeleteBlackBoxCaseDocumentForSession(in)
}

// SearchBlackBoxCaseDocumentForAssistant searches for black box case documents for assistant
func (s *BlackBoxCaseDocumentServiceServer) SearchBlackBoxCaseDocumentForAssistant(ctx context.Context, in *pb.SearchBlackBoxCaseDocumentForAssistantReq) (*pb.SearchBlackBoxCaseDocumentForAssistantResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := blackboxcasedocumentservicelogic.NewSearchBlackBoxCaseDocumentForAssistantLogic(ctx, s.svcCtx)

	return l.SearchBlackBoxCaseDocumentForAssistant(in)
}

// SearchBlackBoxCaseDocumentForSession searches for black box case documents for session
func (s *BlackBoxCaseDocumentServiceServer) SearchBlackBoxCaseDocumentForSession(ctx context.Context, in *pb.SearchBlackBoxCaseDocumentForSessionReq) (*pb.SearchBlackBoxCaseDocumentForSessionResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := blackboxcasedocumentservicelogic.NewSearchBlackBoxCaseDocumentForSessionLogic(ctx, s.svcCtx)

	return l.SearchBlackBoxCaseDocumentForSession(in)
}

// GetBlackBoxCaseDocumentHeadersList get black box case document Headers list
func (s *BlackBoxCaseDocumentServiceServer) GetBlackBoxCaseDocumentHeadersList(ctx context.Context, in *pb.GetBlackBoxCaseDocumentHeadersListReq) (*pb.GetBlackBoxCaseDocumentHeadersListResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := blackboxcasedocumentservicelogic.NewGetBlackBoxCaseDocumentHeadersListLogic(ctx, s.svcCtx)

	return l.GetBlackBoxCaseDocumentHeadersList(in)
}
