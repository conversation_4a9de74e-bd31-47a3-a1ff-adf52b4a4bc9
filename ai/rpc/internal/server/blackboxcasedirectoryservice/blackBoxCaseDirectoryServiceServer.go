// Code generated by goctl. DO NOT EDIT.
// goctl 1.7.5
// Source: ai.proto

package server

import (
	"context"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"

	blackboxcasedirectoryservicelogic "gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/logic/blackboxcasedirectoryservice"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
)

type BlackBoxCaseDirectoryServiceServer struct {
	svcCtx *svc.ServiceContext
	pb.UnimplementedBlackBoxCaseDirectoryServiceServer
}

func NewBlackBoxCaseDirectoryServiceServer(svcCtx *svc.ServiceContext) *BlackBoxCaseDirectoryServiceServer {
	return &BlackBoxCaseDirectoryServiceServer{
		svcCtx: svcCtx,
	}
}

// ListBlackBoxCaseDirectories list blackbox case directories
func (s *BlackBoxCaseDirectoryServiceServer) ListBlackBoxCaseDirectories(ctx context.Context, in *pb.ListBlackBoxCaseDirsReq) (*pb.ListBlackBoxCaseDirsResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := blackboxcasedirectoryservicelogic.NewListBlackBoxCaseDirectoriesLogic(ctx, s.svcCtx)

	return l.ListBlackBoxCaseDirectories(in)
}

// CreateBlackBoxCaseDirectory create blackbox case directory
func (s *BlackBoxCaseDirectoryServiceServer) CreateBlackBoxCaseDirectory(ctx context.Context, in *pb.CreateBlackBoxCaseDirReq) (*pb.CreateBlackBoxCaseDirResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := blackboxcasedirectoryservicelogic.NewCreateBlackBoxCaseDirectoryLogic(ctx, s.svcCtx)

	return l.CreateBlackBoxCaseDirectory(in)
}

// DeleteBlackBoxCaseDirectory delete blackbox case directory
func (s *BlackBoxCaseDirectoryServiceServer) DeleteBlackBoxCaseDirectory(ctx context.Context, in *pb.DeleteBlackBoxCaseDirReq) (*pb.DeleteBlackBoxCaseDirResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := blackboxcasedirectoryservicelogic.NewDeleteBlackBoxCaseDirectoryLogic(ctx, s.svcCtx)

	return l.DeleteBlackBoxCaseDirectory(in)
}
