// Code generated by goctl. DO NOT EDIT.
// goctl 1.7.5
// Source: ai.proto

package server

import (
	"context"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"

	blackboxcasesessionservicelogic "gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/logic/blackboxcasesessionservice"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
)

type BlackBoxCaseSessionServiceServer struct {
	svcCtx *svc.ServiceContext
	pb.UnimplementedBlackBoxCaseSessionServiceServer
}

func NewBlackBoxCaseSessionServiceServer(svcCtx *svc.ServiceContext) *BlackBoxCaseSessionServiceServer {
	return &BlackBoxCaseSessionServiceServer{
		svcCtx: svcCtx,
	}
}

// CreateBlackBoxCaseSession creates a black box case session
func (s *BlackBoxCaseSessionServiceServer) CreateBlackBoxCaseSession(ctx context.Context, in *pb.CreateBlackBoxCaseSessionReq) (*pb.CreateBlackBoxCaseSessionResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := blackboxcasesessionservicelogic.NewCreateBlackBoxCaseSessionLogic(ctx, s.svcCtx)

	return l.CreateBlackBoxCaseSession(in)
}

// UpdateBlackBoxCaseSession updates a black box case session
func (s *BlackBoxCaseSessionServiceServer) UpdateBlackBoxCaseSession(ctx context.Context, in *pb.UpdateBlackBoxCaseSessionReq) (*pb.UpdateBlackBoxCaseSessionResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := blackboxcasesessionservicelogic.NewUpdateBlackBoxCaseSessionLogic(ctx, s.svcCtx)

	return l.UpdateBlackBoxCaseSession(in)
}

// DeleteBlackBoxCaseSession deletes a black box case session
func (s *BlackBoxCaseSessionServiceServer) DeleteBlackBoxCaseSession(ctx context.Context, in *pb.DeleteBlackBoxCaseSessionReq) (*pb.DeleteBlackBoxCaseSessionResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := blackboxcasesessionservicelogic.NewDeleteBlackBoxCaseSessionLogic(ctx, s.svcCtx)

	return l.DeleteBlackBoxCaseSession(in)
}

// GetBlackBoxCaseSession gets a black box case session
func (s *BlackBoxCaseSessionServiceServer) GetBlackBoxCaseSession(ctx context.Context, in *pb.GetBlackBoxCaseSessionReq) (*pb.GetBlackBoxCaseSessionResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := blackboxcasesessionservicelogic.NewGetBlackBoxCaseSessionLogic(ctx, s.svcCtx)

	return l.GetBlackBoxCaseSession(in)
}

// SearchBlackBoxCaseSession searches for black box case sessions
func (s *BlackBoxCaseSessionServiceServer) SearchBlackBoxCaseSession(ctx context.Context, in *pb.SearchBlackBoxCaseSessionReq) (*pb.SearchBlackBoxCaseSessionResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := blackboxcasesessionservicelogic.NewSearchBlackBoxCaseSessionLogic(ctx, s.svcCtx)

	return l.SearchBlackBoxCaseSession(in)
}

// GetBlackBoxCaseSessionHistoryMessageList gets black box case session history message list
func (s *BlackBoxCaseSessionServiceServer) GetBlackBoxCaseSessionHistoryMessageList(ctx context.Context, in *pb.GetBlackBoxCaseSessionHistoryMessageListReq) (*pb.GetBlackBoxCaseSessionHistoryMessageListResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := blackboxcasesessionservicelogic.NewGetBlackBoxCaseSessionHistoryMessageListLogic(ctx, s.svcCtx)

	return l.GetBlackBoxCaseSessionHistoryMessageList(in)
}

// RemoveBlackBoxCaseSessionHistoryMessageList removes black box case session history message list
func (s *BlackBoxCaseSessionServiceServer) RemoveBlackBoxCaseSessionHistoryMessageList(ctx context.Context, in *pb.RemoveBlackBoxCaseSessionHistoryMessageListReq) (*pb.RemoveBlackBoxCaseSessionHistoryMessageListResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := blackboxcasesessionservicelogic.NewRemoveBlackBoxCaseSessionHistoryMessageListLogic(ctx, s.svcCtx)

	return l.RemoveBlackBoxCaseSessionHistoryMessageList(in)
}

// SendBlackBoxCaseForSession sends black box case for session
func (s *BlackBoxCaseSessionServiceServer) SendBlackBoxCaseForSession(ctx context.Context, in *pb.SendBlackBoxCaseForSessionReq) (*pb.SendBlackBoxCaseForSessionResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := blackboxcasesessionservicelogic.NewSendBlackBoxCaseForSessionLogic(ctx, s.svcCtx)

	return l.SendBlackBoxCaseForSession(in)
}

// GetBlackBoxCaseConversationAIState get black box case conversation ai state
func (s *BlackBoxCaseSessionServiceServer) GetBlackBoxCaseConversationAIState(ctx context.Context, in *pb.GetBlackBoxCaseConversationAIStateReq) (*pb.GetBlackBoxCaseConversationAIStateResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := blackboxcasesessionservicelogic.NewGetBlackBoxCaseConversationAIStateLogic(ctx, s.svcCtx)

	return l.GetBlackBoxCaseConversationAIState(in)
}

// GetBlackBoxCaseConversationAIMessage get black box case conversation ai message
func (s *BlackBoxCaseSessionServiceServer) GetBlackBoxCaseConversationAIMessage(ctx context.Context, in *pb.GetBlackBoxCaseConversationAIMessageReq) (*pb.GetBlackBoxCaseConversationAIMessageResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := blackboxcasesessionservicelogic.NewGetBlackBoxCaseConversationAIMessageLogic(ctx, s.svcCtx)

	return l.GetBlackBoxCaseConversationAIMessage(in)
}

// GetBlackBoxCaseConversationAIMessageList get black box case modify reference content list
func (s *BlackBoxCaseSessionServiceServer) GetBlackBoxCaseModifyTestCaseReferenceContentList(ctx context.Context, in *pb.GetBlackBoxCaseModifyTestCaseReferenceContentListReq) (*pb.GetBlackBoxCaseModifyTestCaseReferenceContentListResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := blackboxcasesessionservicelogic.NewGetBlackBoxCaseModifyTestCaseReferenceContentListLogic(ctx, s.svcCtx)

	return l.GetBlackBoxCaseModifyTestCaseReferenceContentList(in)
}

// CreateBlackBoxCaseModifyTestCaseReferenceContent create black box case modify reference content
func (s *BlackBoxCaseSessionServiceServer) CreateBlackBoxCaseModifyTestCaseReferenceContent(ctx context.Context, in *pb.CreateBlackBoxCaseModifyTestCaseReferenceContentReq) (*pb.CreateBlackBoxCaseModifyTestCaseReferenceContentResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := blackboxcasesessionservicelogic.NewCreateBlackBoxCaseModifyTestCaseReferenceContentLogic(ctx, s.svcCtx)

	return l.CreateBlackBoxCaseModifyTestCaseReferenceContent(in)
}

// ReplaceBlackBoxCase replace black box case
func (s *BlackBoxCaseSessionServiceServer) ReplaceBlackBoxCase(ctx context.Context, in *pb.ReplaceBlackBoxCaseReq) (*pb.ReplaceBlackBoxCaseResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := blackboxcasesessionservicelogic.NewReplaceBlackBoxCaseLogic(ctx, s.svcCtx)

	return l.ReplaceBlackBoxCase(in)
}

// MergeBlackBoxCase merge black box case
func (s *BlackBoxCaseSessionServiceServer) MergeBlackBoxCase(ctx context.Context, in *pb.MergeBlackBoxCaseReq) (*pb.MergeBlackBoxCaseResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := blackboxcasesessionservicelogic.NewMergeBlackBoxCaseLogic(ctx, s.svcCtx)

	return l.MergeBlackBoxCase(in)
}

// TransferBlackBoxCase2XMind transfer black box case
func (s *BlackBoxCaseSessionServiceServer) TransferBlackBoxCase2XMind(ctx context.Context, in *pb.TransferBlackBoxCase2XMindReq) (*pb.TransferBlackBoxCase2XMindResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := blackboxcasesessionservicelogic.NewTransferBlackBoxCase2XMindLogic(ctx, s.svcCtx)

	return l.TransferBlackBoxCase2XMind(in)
}

// GetBlackBoxCaseAIMessageList get black box case conversation ai message
func (s *BlackBoxCaseSessionServiceServer) GetBlackBoxCaseAIMessage(ctx context.Context, in *pb.GetCreateBlackBoxCaseAIMessageReq) (*pb.GetCreateBlackBoxCaseAIMessageResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := blackboxcasesessionservicelogic.NewGetBlackBoxCaseAIMessageLogic(ctx, s.svcCtx)

	return l.GetBlackBoxCaseAIMessage(in)
}
