// Code generated by goctl. DO NOT EDIT.
// goctl 1.7.5
// Source: ai.proto

package server

import (
	"context"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"

	blackboxcasedataservicelogic "gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/logic/blackboxcasedataservice"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
)

type BlackBoxCaseDataServiceServer struct {
	svcCtx *svc.ServiceContext
	pb.UnimplementedBlackBoxCaseDataServiceServer
}

func NewBlackBoxCaseDataServiceServer(svcCtx *svc.ServiceContext) *BlackBoxCaseDataServiceServer {
	return &BlackBoxCaseDataServiceServer{
		svcCtx: svcCtx,
	}
}

// CreateBlackBoxCaseData create a black box case
func (s *BlackBoxCaseDataServiceServer) CreateBlackBoxCaseData(ctx context.Context, in *pb.CreateBlackBoxCaseDataReq) (*pb.CreateBlackBoxCaseDataResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := blackboxcasedataservicelogic.NewCreateBlackBoxCaseDataLogic(ctx, s.svcCtx)

	return l.CreateBlackBoxCaseData(in)
}

// DeleteBlackBoxCaseData create a black box case
func (s *BlackBoxCaseDataServiceServer) DeleteBlackBoxCaseData(ctx context.Context, in *pb.DeleteBlackBoxCaseDataReq) (*pb.DeleteBlackBoxCaseDataResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := blackboxcasedataservicelogic.NewDeleteBlackBoxCaseDataLogic(ctx, s.svcCtx)

	return l.DeleteBlackBoxCaseData(in)
}

// UpdateBlackBoxCaseData create a black box case
func (s *BlackBoxCaseDataServiceServer) UpdateBlackBoxCaseData(ctx context.Context, in *pb.UpdateBlackBoxCaseDataReq) (*pb.UpdateBlackBoxCaseDataResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := blackboxcasedataservicelogic.NewUpdateBlackBoxCaseDataLogic(ctx, s.svcCtx)

	return l.UpdateBlackBoxCaseData(in)
}

// UpdateBlackBoxCaseData create a black box case
func (s *BlackBoxCaseDataServiceServer) UpdateBlackBoxCaseDataOrder(ctx context.Context, in *pb.UpdateBlackBoxCaseDataOrderReq) (*pb.UpdateBlackBoxCaseDataOrderResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := blackboxcasedataservicelogic.NewUpdateBlackBoxCaseDataOrderLogic(ctx, s.svcCtx)

	return l.UpdateBlackBoxCaseDataOrder(in)
}

// BatchUpdateBlackBoxCaseData create a black box case
func (s *BlackBoxCaseDataServiceServer) BatchUpdateBlackBoxCaseData(ctx context.Context, in *pb.BatchUpdateBlackBoxCaseDataReq) (*pb.BatchUpdateBlackBoxCaseDataResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := blackboxcasedataservicelogic.NewBatchUpdateBlackBoxCaseDataLogic(ctx, s.svcCtx)

	return l.BatchUpdateBlackBoxCaseData(in)
}

// GetBlackBoxCaseData gets a black box case
func (s *BlackBoxCaseDataServiceServer) GetBlackBoxCaseData(ctx context.Context, in *pb.GetBlackBoxCaseDataReq) (*pb.GetBlackBoxCaseDataResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := blackboxcasedataservicelogic.NewGetBlackBoxCaseDataLogic(ctx, s.svcCtx)

	return l.GetBlackBoxCaseData(in)
}

// ListBlackBoxCaseData list black box case
func (s *BlackBoxCaseDataServiceServer) ListBlackBoxCaseData(ctx context.Context, in *pb.ListBlackBoxCaseDataReq) (*pb.ListBlackBoxCaseDataResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := blackboxcasedataservicelogic.NewListBlackBoxCaseDataLogic(ctx, s.svcCtx)

	return l.ListBlackBoxCaseData(in)
}

// ListBlackBoxCaseDataV24 list black box case
func (s *BlackBoxCaseDataServiceServer) ListBlackBoxCaseDataV24(ctx context.Context, in *pb.ListBlackBoxCaseDataReq) (*pb.ListBlackBoxCaseDataResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := blackboxcasedataservicelogic.NewListBlackBoxCaseDataV24Logic(ctx, s.svcCtx)

	return l.ListBlackBoxCaseDataV24(in)
}

// KeepBlackBoxCaseData keep black box case
func (s *BlackBoxCaseDataServiceServer) KeepBlackBoxCaseData(ctx context.Context, in *pb.KeepBlackBoxCaseDataReq) (*pb.KeepBlackBoxCaseDataResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := blackboxcasedataservicelogic.NewKeepBlackBoxCaseDataLogic(ctx, s.svcCtx)

	return l.KeepBlackBoxCaseData(in)
}

// KeepBlackBoxCaseData keep black box case
func (s *BlackBoxCaseDataServiceServer) ClearKeepBlackBoxCaseData(ctx context.Context, in *pb.ClearKeepBlackBoxCaseDataReq) (*pb.ClearKeepBlackBoxCaseDataResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := blackboxcasedataservicelogic.NewClearKeepBlackBoxCaseDataLogic(ctx, s.svcCtx)

	return l.ClearKeepBlackBoxCaseData(in)
}

// AppendBlackBoxCaseData append black box case
func (s *BlackBoxCaseDataServiceServer) AppendBlackBoxCaseData(ctx context.Context, in *pb.AppendBlackBoxCaseDataReq) (*pb.AppendBlackBoxCaseDataResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := blackboxcasedataservicelogic.NewAppendBlackBoxCaseDataLogic(ctx, s.svcCtx)

	return l.AppendBlackBoxCaseData(in)
}

// ReplaceBlackBoxCaseData replace black box case
func (s *BlackBoxCaseDataServiceServer) ReplaceBlackBoxCaseData(ctx context.Context, in *pb.ReplaceBlackBoxCaseDataReq) (*pb.ReplaceBlackBoxCaseDataResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := blackboxcasedataservicelogic.NewReplaceBlackBoxCaseDataLogic(ctx, s.svcCtx)

	return l.ReplaceBlackBoxCaseData(in)
}

// ReplaceBlackBoxCaseData replace black box case
func (s *BlackBoxCaseDataServiceServer) GenerateBlackBoxCaseRef(ctx context.Context, in *pb.GenerateBlackBoxCaseRefReq) (*pb.GenerateBlackBoxCaseRefResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := blackboxcasedataservicelogic.NewGenerateBlackBoxCaseRefLogic(ctx, s.svcCtx)

	return l.GenerateBlackBoxCaseRef(in)
}

// RefreshCaseRefMetrics replace black box case
func (s *BlackBoxCaseDataServiceServer) RefreshCaseRefMetrics(ctx context.Context, in *pb.RefreshCaseRefMetricsReq) (*pb.RefreshCaseRefMetricsResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := blackboxcasedataservicelogic.NewRefreshCaseRefMetricsLogic(ctx, s.svcCtx)

	return l.RefreshCaseRefMetrics(in)
}

// ReorderBlackBoxCaseData list black box case
func (s *BlackBoxCaseDataServiceServer) ReorderBlackBoxCaseData(ctx context.Context, in *pb.ReorderBlackBoxCaseDataReq) (*pb.ReorderBlackBoxCaseDataResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := blackboxcasedataservicelogic.NewReorderBlackBoxCaseDataLogic(ctx, s.svcCtx)

	return l.ReorderBlackBoxCaseData(in)
}

// ListBlackBoxCaseDataByCaseIds list black box case by caseIds
func (s *BlackBoxCaseDataServiceServer) ListBlackBoxCaseDataByCaseIds(ctx context.Context, in *pb.ListBlackBoxCaseDataByCaseIdsReq) (*pb.ListBlackBoxCaseDataByCaseIdsResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := blackboxcasedataservicelogic.NewListBlackBoxCaseDataByCaseIdsLogic(ctx, s.svcCtx)

	return l.ListBlackBoxCaseDataByCaseIds(in)
}
