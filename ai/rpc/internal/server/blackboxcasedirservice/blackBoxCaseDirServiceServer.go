// Code generated by goctl. DO NOT EDIT.
// goctl 1.7.5
// Source: ai.proto

package server

import (
	"context"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"

	blackboxcasedirservicelogic "gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/logic/blackboxcasedirservice"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
)

type BlackBoxCaseDirServiceServer struct {
	svcCtx *svc.ServiceContext
	pb.UnimplementedBlackBoxCaseDirServiceServer
}

func NewBlackBoxCaseDirServiceServer(svcCtx *svc.ServiceContext) *BlackBoxCaseDirServiceServer {
	return &BlackBoxCaseDirServiceServer{
		svcCtx: svcCtx,
	}
}

// CreateBlackBoxCaseDir create a black box case dir
func (s *BlackBoxCaseDirServiceServer) CreateBlackBoxCaseDir(ctx context.Context, in *pb.CreateBlackBoxCaseDirReq) (*pb.CreateBlackBoxCaseDirResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := blackboxcasedirservicelogic.NewCreateBlackBoxCaseDirLogic(ctx, s.svcCtx)

	return l.CreateBlackBoxCaseDir(in)
}

// DeleteBlackBoxCaseDir create a black box case dir
func (s *BlackBoxCaseDirServiceServer) DeleteBlackBoxCaseDir(ctx context.Context, in *pb.DeleteBlackBoxCaseDirReq) (*pb.DeleteBlackBoxCaseDirResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := blackboxcasedirservicelogic.NewDeleteBlackBoxCaseDirLogic(ctx, s.svcCtx)

	return l.DeleteBlackBoxCaseDir(in)
}

// UpdateBlackBoxCaseDir create a black box case dir
func (s *BlackBoxCaseDirServiceServer) UpdateBlackBoxCaseDir(ctx context.Context, in *pb.UpdateBlackBoxCaseDirReq) (*pb.UpdateBlackBoxCaseDirResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := blackboxcasedirservicelogic.NewUpdateBlackBoxCaseDirLogic(ctx, s.svcCtx)

	return l.UpdateBlackBoxCaseDir(in)
}

// GetBlackBoxCaseDir gets a black box case dir
func (s *BlackBoxCaseDirServiceServer) GetBlackBoxCaseDir(ctx context.Context, in *pb.GetBlackBoxCaseDirReq) (*pb.GetBlackBoxCaseDirResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := blackboxcasedirservicelogic.NewGetBlackBoxCaseDirLogic(ctx, s.svcCtx)

	return l.GetBlackBoxCaseDir(in)
}

// ListBlackBoxCaseDirs list black box case dirs
func (s *BlackBoxCaseDirServiceServer) ListBlackBoxCaseDirs(ctx context.Context, in *pb.ListBlackBoxCaseDirsReq) (*pb.ListBlackBoxCaseDirsResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := blackboxcasedirservicelogic.NewListBlackBoxCaseDirsLogic(ctx, s.svcCtx)

	return l.ListBlackBoxCaseDirs(in)
}
