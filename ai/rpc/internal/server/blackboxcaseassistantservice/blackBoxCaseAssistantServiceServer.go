// Code generated by goctl. DO NOT EDIT.
// goctl 1.7.5
// Source: ai.proto

package server

import (
	"context"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"

	blackboxcaseassistantservicelogic "gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/logic/blackboxcaseassistantservice"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
)

type BlackBoxCaseAssistantServiceServer struct {
	svcCtx *svc.ServiceContext
	pb.UnimplementedBlackBoxCaseAssistantServiceServer
}

func NewBlackBoxCaseAssistantServiceServer(svcCtx *svc.ServiceContext) *BlackBoxCaseAssistantServiceServer {
	return &BlackBoxCaseAssistantServiceServer{
		svcCtx: svcCtx,
	}
}

// CreateBlackBoxCaseAssistant creates a black box case assistant
func (s *BlackBoxCaseAssistantServiceServer) CreateBlackBoxCaseAssistant(ctx context.Context, in *pb.CreateBlackBoxCaseAssistantReq) (*pb.CreateBlackBoxCaseAssistantResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := blackboxcaseassistantservicelogic.NewCreateBlackBoxCaseAssistantLogic(ctx, s.svcCtx)

	return l.CreateBlackBoxCaseAssistant(in)
}

// UpdateBlackBoxCaseAssistant updates a black box case assistant
func (s *BlackBoxCaseAssistantServiceServer) UpdateBlackBoxCaseAssistant(ctx context.Context, in *pb.UpdateBlackBoxCaseAssistantReq) (*pb.UpdateBlackBoxCaseAssistantResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := blackboxcaseassistantservicelogic.NewUpdateBlackBoxCaseAssistantLogic(ctx, s.svcCtx)

	return l.UpdateBlackBoxCaseAssistant(in)
}

// DeleteBlackBoxCaseAssistant deletes a black box case assistant
func (s *BlackBoxCaseAssistantServiceServer) DeleteBlackBoxCaseAssistant(ctx context.Context, in *pb.DeleteBlackBoxCaseAssistantReq) (*pb.DeleteBlackBoxCaseAssistantResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := blackboxcaseassistantservicelogic.NewDeleteBlackBoxCaseAssistantLogic(ctx, s.svcCtx)

	return l.DeleteBlackBoxCaseAssistant(in)
}

// GetBlackBoxCaseAssistant gets a black box case assistant
func (s *BlackBoxCaseAssistantServiceServer) GetBlackBoxCaseAssistant(ctx context.Context, in *pb.GetBlackBoxCaseAssistantReq) (*pb.GetBlackBoxCaseAssistantResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := blackboxcaseassistantservicelogic.NewGetBlackBoxCaseAssistantLogic(ctx, s.svcCtx)

	return l.GetBlackBoxCaseAssistant(in)
}

// SearchBlackBoxCaseAssistant searches for black box case assistants
func (s *BlackBoxCaseAssistantServiceServer) SearchBlackBoxCaseAssistant(ctx context.Context, in *pb.SearchBlackBoxCaseAssistantReq) (*pb.SearchBlackBoxCaseAssistantResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := blackboxcaseassistantservicelogic.NewSearchBlackBoxCaseAssistantLogic(ctx, s.svcCtx)

	return l.SearchBlackBoxCaseAssistant(in)
}
