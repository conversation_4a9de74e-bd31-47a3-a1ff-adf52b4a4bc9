// Code generated by goctl. DO NOT EDIT.
// goctl 1.7.5
// Source: ai.proto

package server

import (
	"context"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"

	blackboxcaserevisionservicelogic "gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/logic/blackboxcaserevisionservice"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
)

type BlackBoxCaseRevisionServiceServer struct {
	svcCtx *svc.ServiceContext
	pb.UnimplementedBlackBoxCaseRevisionServiceServer
}

func NewBlackBoxCaseRevisionServiceServer(svcCtx *svc.ServiceContext) *BlackBoxCaseRevisionServiceServer {
	return &BlackBoxCaseRevisionServiceServer{
		svcCtx: svcCtx,
	}
}

// CreateBlackBoxCaseRevision create a black box case
func (s *BlackBoxCaseRevisionServiceServer) CreateBlackBoxCaseRevision(ctx context.Context, in *pb.CreateBlackBoxCaseRevisionReq) (*pb.CreateBlackBoxCaseRevisionResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := blackboxcaserevisionservicelogic.NewCreateBlackBoxCaseRevisionLogic(ctx, s.svcCtx)

	return l.CreateBlackBoxCaseRevision(in)
}

// CreateBlackBoxCaseRevisionData create a black box case data
func (s *BlackBoxCaseRevisionServiceServer) CreateBlackBoxCaseRevisionData(ctx context.Context, in *pb.CreateBlackBoxCaseRevisionDataReq) (*pb.CreateBlackBoxCaseRevisionDataResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := blackboxcaserevisionservicelogic.NewCreateBlackBoxCaseRevisionDataLogic(ctx, s.svcCtx)

	return l.CreateBlackBoxCaseRevisionData(in)
}

// DeleteBlackBoxCaseRevision create a black box case
func (s *BlackBoxCaseRevisionServiceServer) DeleteBlackBoxCaseRevision(ctx context.Context, in *pb.DeleteBlackBoxCaseRevisionReq) (*pb.DeleteBlackBoxCaseRevisionResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := blackboxcaserevisionservicelogic.NewDeleteBlackBoxCaseRevisionLogic(ctx, s.svcCtx)

	return l.DeleteBlackBoxCaseRevision(in)
}

// UpdateBlackBoxCaseRevision create a black box case
func (s *BlackBoxCaseRevisionServiceServer) UpdateBlackBoxCaseRevision(ctx context.Context, in *pb.UpdateBlackBoxCaseRevisionReq) (*pb.UpdateBlackBoxCaseRevisionResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := blackboxcaserevisionservicelogic.NewUpdateBlackBoxCaseRevisionLogic(ctx, s.svcCtx)

	return l.UpdateBlackBoxCaseRevision(in)
}

// UpdateBlackBoxCaseRevisionWithCaseRefId update a black box case with case ref id
func (s *BlackBoxCaseRevisionServiceServer) UpdateBlackBoxCaseRevisionWithCaseRefId(ctx context.Context, in *pb.UpdateBlackBoxCaseRevisionWithCaseRefIdReq) (*pb.UpdateBlackBoxCaseRevisionWithCaseRefIdResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := blackboxcaserevisionservicelogic.NewUpdateBlackBoxCaseRevisionWithCaseRefIdLogic(ctx, s.svcCtx)

	return l.UpdateBlackBoxCaseRevisionWithCaseRefId(in)
}

// AdoptBlackBoxCaseRevision adopt a black box case
func (s *BlackBoxCaseRevisionServiceServer) AdoptBlackBoxCaseRevision(ctx context.Context, in *pb.AdoptBlackBoxCaseRevisionReq) (*pb.AdoptBlackBoxCaseRevisionResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := blackboxcaserevisionservicelogic.NewAdoptBlackBoxCaseRevisionLogic(ctx, s.svcCtx)

	return l.AdoptBlackBoxCaseRevision(in)
}

// GetBlackBoxCaseRevision gets a black box case
func (s *BlackBoxCaseRevisionServiceServer) GetBlackBoxCaseRevision(ctx context.Context, in *pb.GetBlackBoxCaseRevisionReq) (*pb.GetBlackBoxCaseRevisionResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := blackboxcaserevisionservicelogic.NewGetBlackBoxCaseRevisionLogic(ctx, s.svcCtx)

	return l.GetBlackBoxCaseRevision(in)
}

// ListBlackBoxCaseRevision list black box case
func (s *BlackBoxCaseRevisionServiceServer) ListBlackBoxCaseRevision(ctx context.Context, in *pb.ListBlackBoxCaseRevisionReq) (*pb.ListBlackBoxCaseRevisionResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := blackboxcaserevisionservicelogic.NewListBlackBoxCaseRevisionLogic(ctx, s.svcCtx)

	return l.ListBlackBoxCaseRevision(in)
}

// GenerateBlackBoxCaseRevisionId list black box case
func (s *BlackBoxCaseRevisionServiceServer) GenerateBlackBoxCaseRevisionId(ctx context.Context, in *pb.GenerateBlackBoxCaseRevisionIdReq) (*pb.GenerateBlackBoxCaseRevisionIdResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := blackboxcaserevisionservicelogic.NewGenerateBlackBoxCaseRevisionIdLogic(ctx, s.svcCtx)

	return l.GenerateBlackBoxCaseRevisionId(in)
}

// GetBlackBoxCaseRevisionByRevisionId get black box case revision by revisionId
func (s *BlackBoxCaseRevisionServiceServer) GetBlackBoxCaseRevisionByRevisionId(ctx context.Context, in *pb.GetBlackBoxCaseRevisionByRevisionIdReq) (*pb.GetBlackBoxCaseRevisionByRevisionIdResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := blackboxcaserevisionservicelogic.NewGetBlackBoxCaseRevisionByRevisionIdLogic(ctx, s.svcCtx)

	return l.GetBlackBoxCaseRevisionByRevisionId(in)
}
