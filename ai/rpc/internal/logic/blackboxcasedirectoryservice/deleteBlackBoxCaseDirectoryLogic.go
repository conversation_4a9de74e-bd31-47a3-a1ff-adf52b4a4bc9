package blackboxcasedirectoryservicelogic

import (
	"context"

	"github.com/zeromicro/go-zero/core/stores/sqlx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
)

type DeleteBlackBoxCaseDirectoryLogic struct {
	*logic.BaseLogic
}

func NewDeleteBlackBoxCaseDirectoryLogic(ctx context.Context, svcCtx *svc.ServiceContext) *DeleteBlackBoxCaseDirectoryLogic {
	return &DeleteBlackBoxCaseDirectoryLogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),
	}
}

// DeleteBlackBoxCaseDirectory delete blackbox case directory
func (l *DeleteBlackBoxCaseDirectoryLogic) DeleteBlackBoxCaseDirectory(in *pb.DeleteBlackBoxCaseDirReq) (out *pb.DeleteBlackBoxCaseDirResp, err error) {
	dbData, err := l.SvcCtx.BlackBoxCaseDirV2Model.FindOneByProjectIdDirId(
		l.Ctx, in.GetProjectId(), in.GetDirId(),
	)

	if !l.CheckBlackBoxCaseDirV2DbData(dbData, err) {
		return nil, errorx.Err(errorx.NotExists, "数据不存在")
	}

	err = l.SvcCtx.BlackBoxCaseDirModel.Trans(
		l.Ctx, func(context context.Context, session sqlx.Session) error {
			// TODO 删除关联表数据
			if err = l.SvcCtx.BlackBoxCaseDirV2Model.Delete(l.Ctx, session, dbData.Id); err != nil {
				return err
			}
			return nil
		},
	)

	return &pb.DeleteBlackBoxCaseDirResp{}, nil
}
