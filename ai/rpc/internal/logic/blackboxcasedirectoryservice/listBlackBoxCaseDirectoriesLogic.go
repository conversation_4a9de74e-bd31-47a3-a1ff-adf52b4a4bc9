package blackboxcasedirectoryservicelogic

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"
)

type ListBlackBoxCaseDirectoriesLogic struct {
	*logic.BaseLogic
}

func NewListBlackBoxCaseDirectoriesLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ListBlackBoxCaseDirectoriesLogic {
	return &ListBlackBoxCaseDirectoriesLogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),
	}
}

// ListBlackBoxCaseDirectories list blackbox case directories
func (l *ListBlackBoxCaseDirectoriesLogic) ListBlackBoxCaseDirectories(in *pb.ListBlackBoxCaseDirsReq) (out *pb.ListBlackBoxCaseDirsResp, err error) {
	dbDataList, err := l.SvcCtx.BlackBoxCaseDirV2Model.FindByProjectId(
		l.Ctx, in.GetProjectId(), l.CurrentUser.Account,
	)
	if err != nil {
		return nil, err
	}

	out = &pb.ListBlackBoxCaseDirsResp{Items: make([]*pb.BlackBoxCaseDir, 0, len(dbDataList))}
	for _, db := range dbDataList {
		item := &pb.BlackBoxCaseDir{}
		if err = utils.Copy(item, db, l.Converters...); err != nil {
			return nil, errors.Wrapf(
				errorx.Err(errorx.CopyToStructFailure, err.Error()),
				"failed to copy db[%+v] to response, error: %+v", db, err,
			)
		}
		out.Items = append(out.Items, item)
	}
	return out, nil
}
