package blackboxcasedirectoryservicelogic

import (
	"context"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
)

type CreateBlackBoxCaseDirectoryLogic struct {
	*logic.BaseLogic
}

func NewCreateBlackBoxCaseDirectoryLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CreateBlackBoxCaseDirectoryLogic {
	return &CreateBlackBoxCaseDirectoryLogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),
	}
}

// CreateBlackBoxCaseDirectory create blackbox case directory
func (l *CreateBlackBoxCaseDirectoryLogic) CreateBlackBoxCaseDirectory(in *pb.CreateBlackBoxCaseDirReq) (out *pb.CreateBlackBoxCaseDirResp, err error) {
	if l.CurrentUser == nil {
		return nil, nil
	}

	data, err := l.create(in)
	if err != nil {
		return nil, err
	}

	return &pb.CreateBlackBoxCaseDirResp{DirId: data.DirId}, nil
}

func (l *CreateBlackBoxCaseDirectoryLogic) create(req *pb.CreateBlackBoxCaseDirReq) (
	*model.BlackBoxCaseDirV2, error,
) {
	if l.CurrentUser == nil {
		return nil, nil
	}
	dirId, err := l.GenerateAiDirId(req.GetProjectId())
	if err != nil {
		return nil, err
	}
	dir := &model.BlackBoxCaseDirV2{
		ProjectId: req.GetProjectId(),
		DirId:     dirId,
		DirName:   req.GetDirName(),
		CreatedBy: l.CurrentUser.Account,
	}

	_, err = l.SvcCtx.BlackBoxCaseDirV2Model.Insert(l.Ctx, nil, dir)
	if err != nil {
		return nil, err
	}
	return dir, nil
}
