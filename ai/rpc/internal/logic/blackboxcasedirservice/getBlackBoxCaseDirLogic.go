package blackboxcasedirservicelogic

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"
)

type GetBlackBoxCaseDirLogic struct {
	*logic.BaseLogic
}

func NewGetBlackBoxCaseDirLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetBlackBoxCaseDirLogic {
	return &GetBlackBoxCaseDirLogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),
	}
}

// GetBlackBoxCaseDir gets a black box case dir
func (l *GetBlackBoxCaseDirLogic) GetBlackBoxCaseDir(in *pb.GetBlackBoxCaseDirReq) (*pb.GetBlackBoxCaseDirResp, error) {
	resp := &pb.GetBlackBoxCaseDirResp{Item: &pb.BlackBoxCaseDir{}}
	dbData, err := l.SvcCtx.BlackBoxCaseDirModel.FindOneByProjectIdDirId(
		l.Ctx, in.GetProjectId(), in.GetDirId(),
	)
	if !l.CheckBlackBoxCaseDirDbData(dbData, err) {
		return nil, errorx.Err(errorx.NotExists, "数据不存在")
	}

	if err = utils.Copy(resp.Item, dbData, l.Converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy black box case dir[%+v] to response, error: %+v",
			dbData, err,
		)
	}

	return resp, nil
}
