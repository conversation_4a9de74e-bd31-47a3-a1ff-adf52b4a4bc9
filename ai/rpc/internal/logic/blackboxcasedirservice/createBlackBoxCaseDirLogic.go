package blackboxcasedirservicelogic

import (
	"context"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
)

type CreateBlackBoxCaseDirLogic struct {
	*logic.BaseLogic
}

func NewCreateBlackBoxCaseDirLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CreateBlackBoxCaseDirLogic {
	return &CreateBlackBoxCaseDirLogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),
	}
}

// CreateBlackBoxCaseDir create a black box case dir
func (l *CreateBlackBoxCaseDirLogic) CreateBlackBoxCaseDir(in *pb.CreateBlackBoxCaseDirReq) (*pb.CreateBlackBoxCaseDirResp, error) {
	if l.CurrentUser == nil {
		return nil, nil
	}

	data, err := l.create(in)
	if err != nil {
		return nil, err
	}

	return &pb.CreateBlackBoxCaseDirResp{DirId: data.DirId}, nil
}

func (l *CreateBlackBoxCaseDirLogic) create(req *pb.CreateBlackBoxCaseDirReq) (
	*model.BlackBoxCaseDir, error,
) {
	if l.CurrentUser == nil {
		return nil, nil
	}
	dirId, err := l.GenerateAiDirId(req.GetProjectId())
	if err != nil {
		return nil, err
	}
	dir := &model.BlackBoxCaseDir{
		ProjectId: req.GetProjectId(),
		DirId:     dirId,
		DirName:   req.GetDirName(),
		CreatedBy: l.CurrentUser.Account,
	}

	_, err = l.SvcCtx.BlackBoxCaseDirModel.Insert(l.Ctx, nil, dir)
	if err != nil {
		return nil, err
	}
	return dir, nil
}
