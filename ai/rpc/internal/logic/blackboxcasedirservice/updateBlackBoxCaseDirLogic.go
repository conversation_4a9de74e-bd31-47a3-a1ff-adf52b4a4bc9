package blackboxcasedirservicelogic

import (
	"context"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/stores/sqlx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
)

type UpdateBlackBoxCaseDirLogic struct {
	*logic.BaseLogic
}

func NewUpdateBlackBoxCaseDirLogic(ctx context.Context, svcCtx *svc.ServiceContext) *UpdateBlackBoxCaseDirLogic {
	return &UpdateBlackBoxCaseDirLogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),
	}
}

// UpdateBlackBoxCaseDir create a black box case dir
func (l *UpdateBlackBoxCaseDirLogic) UpdateBlackBoxCaseDir(in *pb.UpdateBlackBoxCaseDirReq) (
	*pb.UpdateBlackBoxCaseDirResp, error,
) {
	if l.CurrentUser == nil {
		return nil, nil
	}
	dbData, err := l.SvcCtx.BlackBoxCaseDirModel.FindOneByProjectIdDirId(
		l.Ctx, in.GetProjectId(), in.GetDirId(),
	)
	if !l.CheckBlackBoxCaseDirDbData(dbData, err) {
		return nil, errorx.Err(errorx.NotExists, "数据不存在")
	}

	err = l.SvcCtx.BlackBoxCaseDirModel.Trans(
		l.Ctx, func(context context.Context, session sqlx.Session) error {
			item := &model.BlackBoxCaseDir{Id: dbData.Id}
			if err = utils.Copy(item, in, l.Converters...); err != nil {
				return errors.Wrapf(
					errorx.Err(errorx.CopyToStructFailure, err.Error()),
					"failed to copy db[%+v] to response, error: %+v", in, err,
				)
			}
			item.UpdatedBy = l.CurrentUser.Account
			_, err = l.SvcCtx.BlackBoxCaseDirModel.Update(l.Ctx, session, item)
			if err != nil {
				return err
			}
			return nil
		},
	)

	return &pb.UpdateBlackBoxCaseDirResp{}, nil
}
