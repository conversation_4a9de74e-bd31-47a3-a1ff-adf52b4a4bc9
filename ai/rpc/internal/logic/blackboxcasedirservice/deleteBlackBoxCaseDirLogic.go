package blackboxcasedirservicelogic

import (
	"context"

	"github.com/zeromicro/go-zero/core/stores/sqlx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
)

type DeleteBlackBoxCaseDirLogic struct {
	*logic.BaseLogic
}

func NewDeleteBlackBoxCaseDirLogic(ctx context.Context, svcCtx *svc.ServiceContext) *DeleteBlackBoxCaseDirLogic {
	return &DeleteBlackBoxCaseDirLogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),
	}
}

// DeleteBlackBoxCaseDir create a black box case dir
func (l *DeleteBlackBoxCaseDirLogic) DeleteBlackBoxCaseDir(in *pb.DeleteBlackBoxCaseDirReq) (*pb.DeleteBlackBoxCaseDirResp, error) {
	dbData, err := l.SvcCtx.BlackBoxCaseDirModel.FindOneByProjectIdDirId(
		l.Ctx, in.GetProjectId(), in.GetDirId(),
	)

	if !l.CheckBlackBoxCaseDirDbData(dbData, err) {
		return nil, errorx.Err(errorx.NotExists, "数据不存在")
	}

	err = l.SvcCtx.BlackBoxCaseDirModel.Trans(
		l.Ctx, func(context context.Context, session sqlx.Session) error {
			// TODO 删除关联表数据
			if err = l.SvcCtx.BlackBoxCaseDirModel.Delete(l.Ctx, session, dbData.Id); err != nil {
				return err
			}
			return nil
		},
	)

	return &pb.DeleteBlackBoxCaseDirResp{}, err
}
