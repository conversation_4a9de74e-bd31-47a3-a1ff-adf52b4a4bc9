package blackboxcasesessionservicelogic

import (
	"context"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/public/httpc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/logic"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
)

type CreateBlackBoxCaseModifyTestCaseReferenceContentLogic struct {
	*logic.BaseLogic
}

func NewCreateBlackBoxCaseModifyTestCaseReferenceContentLogic(
	ctx context.Context, svcCtx *svc.ServiceContext,
) *CreateBlackBoxCaseModifyTestCaseReferenceContentLogic {
	return &CreateBlackBoxCaseModifyTestCaseReferenceContentLogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),
	}
}

// CreateBlackBoxCaseModifyTestCaseReferenceContent create black box case modify reference content
func (l *CreateBlackBoxCaseModifyTestCaseReferenceContentLogic) CreateBlackBoxCaseModifyTestCaseReferenceContent(
	in *pb.CreateBlackBoxCaseModifyTestCaseReferenceContentReq,
) (out *pb.CreateBlackBoxCaseModifyTestCaseReferenceContentResp, err error) {
	err = httpc.CreateBlackBoxCaseModifyTestCaseReferenceContent(l.SvcCtx.ExternalAiDomain, in)
	if err != nil {
		return nil, err
	}
	return &pb.CreateBlackBoxCaseModifyTestCaseReferenceContentResp{}, nil
}
