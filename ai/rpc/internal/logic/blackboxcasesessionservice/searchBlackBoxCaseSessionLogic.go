package blackboxcasesessionservicelogic

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
)

type SearchBlackBoxCaseSessionLogic struct {
	*logic.BaseLogic
}

func NewSearchBlackBoxCaseSessionLogic(
	ctx context.Context, svcCtx *svc.ServiceContext,
) *SearchBlackBoxCaseSessionLogic {
	return &SearchBlackBoxCaseSessionLogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),
	}
}

// SearchBlackBoxCaseSession searches for black box case sessions
func (l *SearchBlackBoxCaseSessionLogic) SearchBlackBoxCaseSession(req *pb.SearchBlackBoxCaseSessionReq) (
	resp *pb.SearchBlackBoxCaseSessionResp, err error,
) {
	resp = &pb.SearchBlackBoxCaseSessionResp{}
	if l.CurrentUser == nil {
		return nil, nil
	}
	b, err := l.checkSearchBlackBoxCaseSession(
		req.GetProjectId(), req.GetAssistantId(),
	)
	if !b {
		return resp, err
	}
	queryList, err := l.SvcCtx.BlackBoxCaseSessionModel.FindByProjectIdAssistantIdAccount(
		l.Ctx, req.GetProjectId(), req.GetAssistantId(), l.CurrentUser.Account,
	)
	if err != nil {
		return nil, err
	}
	resp.Items = make([]*pb.BlackBoxCaseSession, 0, len(queryList))
	for _, db := range queryList {
		item := &pb.BlackBoxCaseSession{}
		if err = utils.Copy(item, db, l.Converters...); err != nil {
			return nil, errors.Wrapf(
				errorx.Err(errorx.CopyToStructFailure, err.Error()),
				"failed to copy db[%+v] to response, error: %+v", db, err,
			)
		}
		resp.Items = append(resp.Items, item)
	}
	return resp, nil
}

func (l *SearchBlackBoxCaseSessionLogic) checkSearchBlackBoxCaseSession(projectId, assistantId string) (
	b bool, err error,
) {
	testData := &logic.Data{
		ProjectId:   projectId,
		AssistantId: assistantId,
		SessionId:   "sessionId",
		DocumentId:  "documentId",
		IsValid:     false,
	}
	assistantCheckChain := logic.NewAssistantCheckChain(l.BaseLogic)
	return testData.IsValid, assistantCheckChain.Handle(testData)
}
