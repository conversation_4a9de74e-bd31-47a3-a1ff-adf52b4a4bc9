package blackboxcasesessionservicelogic

import (
	"context"
	"fmt"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
)

type SendBlackBoxCaseForSessionLogic struct {
	*logic.BaseLogic
}

func NewSendBlackBoxCaseForSessionLogic(
	ctx context.Context, svcCtx *svc.ServiceContext,
) *SendBlackBoxCaseForSessionLogic {
	return &SendBlackBoxCaseForSessionLogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),
	}
}

// SendBlackBoxCaseForSession sends black box case for session
func (l *SendBlackBoxCaseForSessionLogic) SendBlackBoxCaseForSession(in *pb.SendBlackBoxCaseForSessionReq) (
	out *pb.SendBlackBoxCaseForSessionResp, err error,
) {
	// todo: add your logic here and delete this line
	fmt.Println("rpc SendBlackBoxCaseForSession sends black box case for session")
	fmt.Println(l.CurrentUser.Account)
	fmt.Println(l.CurrentUser.Email)
	fmt.Println(l.CurrentUser.Fullname)
	return &pb.SendBlackBoxCaseForSessionResp{}, nil
}
