package blackboxcasesessionservicelogic

import (
	"context"
	"database/sql"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/public/httpc"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
)

type UpdateBlackBoxCaseSessionLogic struct {
	*logic.BaseLogic
}

func NewUpdateBlackBoxCaseSessionLogic(
	ctx context.Context, svcCtx *svc.ServiceContext,
) *UpdateBlackBoxCaseSessionLogic {
	return &UpdateBlackBoxCaseSessionLogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),
	}
}

// UpdateBlackBoxCaseSession updates a black box case session
func (l *UpdateBlackBoxCaseSessionLogic) UpdateBlackBoxCaseSession(in *pb.UpdateBlackBoxCaseSessionReq) (
	out *pb.UpdateBlackBoxCaseSessionResp, err error,
) {
	if l.CurrentUser == nil {
		return nil, nil
	}
	dbData, err := l.SvcCtx.BlackBoxCaseSessionModel.FindOneByProjectIdAssistantIdSessionId(
		l.Ctx, in.GetProjectId(), in.GetAssistantId(), in.GetSessionId(),
	)
	if !l.CheckBlackBoxCaseSessionDbData(dbData, err) {
		return nil, errorx.Err(errorx.NotExists, "数据不存在")
	}
	dbData.SessionName = in.GetSessionName()
	dbData.SessionDescription = sql.NullString{
		String: in.GetSessionDescription(),
		Valid:  in.GetSessionDescription() != "",
	}
	dbData.UpdatedBy = l.CurrentUser.Account
	if _, err = l.SvcCtx.BlackBoxCaseSessionModel.Update(l.Ctx, nil, dbData); err != nil {
		return nil, err
	}

	err = httpc.UpdateSession(l.SvcCtx.ExternalAiDomain, dbData)
	if err != nil {
		return nil, err
	}
	return &pb.UpdateBlackBoxCaseSessionResp{}, nil
}
