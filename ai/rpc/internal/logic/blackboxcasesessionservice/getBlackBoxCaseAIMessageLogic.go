package blackboxcasesessionservicelogic

import (
	"context"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/public/httpc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/logic"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
)

type GetBlackBoxCaseAIMessageLogic struct {
	*logic.BaseLogic
}

func NewGetBlackBoxCaseAIMessageLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetBlackBoxCaseAIMessageLogic {
	return &GetBlackBoxCaseAIMessageLogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),
	}
}

// GetBlackBoxCaseAIMessage GetBlackBoxCaseAIMessageList get black box case conversation ai message
func (l *GetBlackBoxCaseAIMessageLogic) GetBlackBoxCaseAIMessage(in *pb.GetCreateBlackBoxCaseAIMessageReq) (
	out *pb.GetCreateBlackBoxCaseAIMessageResp, err error) {
	err, resp := httpc.GetBlackBoxCaseAIMessage(l.SvcCtx.ExternalAiDomain, in)
	if err != nil {
		return nil, err
	}
	return &pb.GetCreateBlackBoxCaseAIMessageResp{
		Message: *resp.Data,
	}, nil
}
