package blackboxcasesessionservicelogic

import (
	"context"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/public/httpc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/logic"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
)

type TransferBlackBoxCase2XMindLogic struct {
	*logic.BaseLogic
}

func NewTransferBlackBoxCase2XMindLogic(ctx context.Context, svcCtx *svc.ServiceContext) *TransferBlackBoxCase2XMindLogic {
	return &TransferBlackBoxCase2XMindLogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),
	}
}

// TransferBlackBoxCase2XMind transfer black box case
func (l *TransferBlackBoxCase2XMindLogic) TransferBlackBoxCase2XMind(in *pb.TransferBlackBoxCase2XMindReq) (out *pb.TransferBlackBoxCase2XMindResp, err error) {
	err, resp := httpc.TransferBlackBoxCase2XMind(l.SvcCtx.ExternalAiDomain, in)
	if err != nil {
		return nil, err
	}
	return &pb.TransferBlackBoxCase2XMindResp{Data: *resp.Data}, nil
}
