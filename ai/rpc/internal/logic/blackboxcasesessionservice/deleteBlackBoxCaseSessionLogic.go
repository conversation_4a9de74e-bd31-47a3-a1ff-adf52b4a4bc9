package blackboxcasesessionservicelogic

import (
	"context"

	"github.com/zeromicro/go-zero/core/stores/sqlx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/public/httpc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/codes"
)

type DeleteBlackBoxCaseSessionLogic struct {
	*logic.BaseLogic
}

func NewDeleteBlackBoxCaseSessionLogic(
	ctx context.Context, svcCtx *svc.ServiceContext,
) *DeleteBlackBoxCaseSessionLogic {
	return &DeleteBlackBoxCaseSessionLogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),
	}
}

// DeleteBlackBoxCaseSession deletes a black box case session
func (l *DeleteBlackBoxCaseSessionLogic) DeleteBlackBoxCaseSession(req *pb.DeleteBlackBoxCaseSessionReq) (
	resp *pb.DeleteBlackBoxCaseSessionResp, err error,
) {
	resp = new(pb.DeleteBlackBoxCaseSessionResp)
	if l.CurrentUser == nil {
		return nil, nil
	}
	b, err := l.checkDeleteBlackBoxCaseDocumentForSession(
		req.GetProjectId(), req.GetAssistantId(), req.GetSessionId(),
	)
	if !b {
		return resp, err
	}
	dbAssistantData, _ := l.SvcCtx.BlackBoxCaseAssistantModel.FindOneByProjectIdAssistantId(
		l.Ctx, req.ProjectId, req.AssistantId,
	)
	if pb.WorkSpaceType_WORK_SPACE_TYPE_PERSONAL == pb.WorkSpaceType(dbAssistantData.WorkSpaceType) {
		if dbAssistantData.Account.String != l.CurrentUser.Account {
			return nil, errorx.Err(
				codes.TheDoesNotBelongToTheOwnerFailure, "助手为个人模式，删除会话者不属于拥有者",
			)
		}
	}
	err = l.SvcCtx.BlackBoxCaseSessionModel.Trans(
		l.Ctx, func(context context.Context, session sqlx.Session) error {
			err = l.SvcCtx.BlackBoxCaseSessionModel.DeleteByProjectIdAssistantIdSessionId(
				l.Ctx, session, req.GetProjectId(), req.GetAssistantId(), req.GetSessionId(),
			)
			if err != nil {
				return err
			}
			err = l.SvcCtx.BlackBoxCaseAssistantDocumentSessionRelationshipModel.DeleteByProjectIdAssistantIdSessionId(
				l.Ctx, session, req.GetProjectId(), req.GetAssistantId(), req.GetSessionId(),
			)
			if err != nil {
				return err
			}
			return nil
		},
	)
	if err != nil {
		return nil, err
	}
	err = httpc.DeleteSession(l.SvcCtx.ExternalAiDomain, req.SessionId)
	if err != nil {
		return nil, err
	}
	return resp, nil
}

func (l *DeleteBlackBoxCaseSessionLogic) checkDeleteBlackBoxCaseDocumentForSession(projectId, assistantId, sessionId string) (
	b bool, err error,
) {
	testData := &logic.Data{
		ProjectId:   projectId,
		AssistantId: assistantId,
		SessionId:   sessionId,
		DocumentId:  "",
		IsValid:     false,
	}
	assistantCheckChain := logic.NewAssistantCheckChain(l.BaseLogic)
	sessionCheckChain := logic.NewSessionCheckChain(l.BaseLogic)
	assistantCheckChain.SetNext(sessionCheckChain)
	return testData.IsValid, assistantCheckChain.Handle(testData)
}
