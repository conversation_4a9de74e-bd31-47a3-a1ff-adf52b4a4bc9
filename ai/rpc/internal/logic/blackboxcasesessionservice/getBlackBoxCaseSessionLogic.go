package blackboxcasesessionservicelogic

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
)

type GetBlackBoxCaseSessionLogic struct {
	*logic.BaseLogic
}

func NewGetBlackBoxCaseSessionLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetBlackBoxCaseSessionLogic {
	return &GetBlackBoxCaseSessionLogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),
	}
}

// GetBlackBoxCaseSession gets a black box case session
func (l *GetBlackBoxCaseSessionLogic) GetBlackBoxCaseSession(in *pb.GetBlackBoxCaseSessionReq) (
	out *pb.GetBlackBoxCaseSessionResp, err error,
) {
	resp := &pb.GetBlackBoxCaseSessionResp{Item: &pb.BlackBoxCaseSession{}}
	dbData, err := l.SvcCtx.BlackBoxCaseSessionModel.FindOneByProjectIdAssistantIdSessionId(
		l.Ctx,
		in.GetProjectId(),
		in.GetAssistantId(),
		in.GetSessionId(),
	)
	if !l.CheckBlackBoxCaseSessionDbData(dbData, err) {
		return nil, nil
	}
	if err = utils.Copy(resp.Item, dbData, l.Converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy black box case assistant[%+v] to response, error: %+v",
			dbData, err,
		)
	}
	return resp, nil
}
