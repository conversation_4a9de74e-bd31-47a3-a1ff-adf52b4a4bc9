package blackboxcasesessionservicelogic

import (
	"context"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/public/httpc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/logic"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
)

type GetBlackBoxCaseConversationAIStateLogic struct {
	*logic.BaseLogic
}

func NewGetBlackBoxCaseConversationAIStateLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetBlackBoxCaseConversationAIStateLogic {
	return &GetBlackBoxCaseConversationAIStateLogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),
	}
}

// GetBlackBoxCaseConversationAIState get black box case conversation ai state
func (l *GetBlackBoxCaseConversationAIStateLogic) GetBlackBoxCaseConversationAIState(in *pb.GetBlackBoxCaseConversationAIStateReq) (out *pb.GetBlackBoxCaseConversationAIStateResp, err error) {
	err, resp := httpc.GetBlackBoxCaseConversationAIState(l.SvcCtx.ExternalAiDomain, in)
	if err != nil {
		return nil, err
	}
	return *resp.Data, nil
}
