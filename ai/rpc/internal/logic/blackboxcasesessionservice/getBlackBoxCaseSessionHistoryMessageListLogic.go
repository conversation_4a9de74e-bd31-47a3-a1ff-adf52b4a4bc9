package blackboxcasesessionservicelogic

import (
	"context"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/public/httpc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
)

type GetBlackBoxCaseSessionHistoryMessageListLogic struct {
	*logic.BaseLogic
}

func NewGetBlackBoxCaseSessionHistoryMessageListLogic(
	ctx context.Context, svcCtx *svc.ServiceContext,
) *GetBlackBoxCaseSessionHistoryMessageListLogic {
	return &GetBlackBoxCaseSessionHistoryMessageListLogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),
	}
}

// GetBlackBoxCaseSessionHistoryMessageList gets black box case session history message list
func (l *GetBlackBoxCaseSessionHistoryMessageListLogic) GetBlackBoxCaseSessionHistoryMessageList(in *pb.GetBlackBoxCaseSessionHistoryMessageListReq) (
	out *pb.GetBlackBoxCaseSessionHistoryMessageListResp, err error,

) {
	err, resp := httpc.GetSessionHistoryList(l.SvcCtx.ExternalAiDomain, in.SessionId)
	if err != nil {
		return nil, err
	}
	var blackBoxMessages []*pb.BlackBoxCaseSessionMessage
	for _, message := range *resp.Data {
		blackBoxMessages = append(blackBoxMessages, &pb.BlackBoxCaseSessionMessage{
			Content: &pb.BlackBoxCaseSessionMsgContent{
				Human: message.Content.Human,
				Ai:    message.Content.Ai,
			},
			Metadata: &pb.BlackBoxCaseSessionMsgMetadata{
				SessionId:      message.Metadata.SessionId,
				CreatedAt:      message.Metadata.CreatedAt,
				ContentType:    message.Metadata.ContentType,
				ConversationId: message.Metadata.ConversationId,
				State: &pb.BlackBoxCaseSessionConversationState{
					State:     message.Metadata.State.State,
					StateDesc: message.Metadata.State.StateDesc,
				},
			},
		})
	}
	return &pb.GetBlackBoxCaseSessionHistoryMessageListResp{
		Messages: blackBoxMessages,
	}, nil
}
