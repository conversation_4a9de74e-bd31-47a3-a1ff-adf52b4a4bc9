package blackboxcasesessionservicelogic

import (
	"context"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/public/httpc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/logic"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
)

type GetBlackBoxCaseConversationAIMessageLogic struct {
	*logic.BaseLogic
}

func NewGetBlackBoxCaseConversationAIMessageLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetBlackBoxCaseConversationAIMessageLogic {
	return &GetBlackBoxCaseConversationAIMessageLogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),
	}
}

// GetBlackBoxCaseConversationAIMessage get black box case conversation ai message
func (l *GetBlackBoxCaseConversationAIMessageLogic) GetBlackBoxCaseConversationAIMessage(in *pb.GetBlackBoxCaseConversationAIMessageReq) (out *pb.GetBlackBoxCaseConversationAIMessageResp, err error) {
	err, resp := httpc.GetBlackBoxCaseConversationAIMessage(l.SvcCtx.ExternalAiDomain, in)
	if err != nil {
		return nil, err
	}
	return *resp.Data, nil
}
