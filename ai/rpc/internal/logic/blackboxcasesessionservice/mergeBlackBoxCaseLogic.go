package blackboxcasesessionservicelogic

import (
	"context"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/public/httpc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/logic"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
)

type MergeBlackBoxCaseLogic struct {
	*logic.BaseLogic
}

func NewMergeBlackBoxCaseLogic(ctx context.Context, svcCtx *svc.ServiceContext) *MergeBlackBoxCaseLogic {
	return &MergeBlackBoxCaseLogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),
	}
}

// MergeBlackBoxCase merge black box case
func (l *MergeBlackBoxCaseLogic) MergeBlackBoxCase(in *pb.MergeBlackBoxCaseReq) (out *pb.MergeBlackBoxCaseResp, err error) {
	resp, err := httpc.MergeBlackBoxCase(l.SvcCtx.ExternalAiDomain, in)
	if err != nil {
		return nil, err
	}
	return &pb.MergeBlackBoxCaseResp{
		Messages: *resp.Data,
	}, nil
}
