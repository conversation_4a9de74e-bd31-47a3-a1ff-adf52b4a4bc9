package blackboxcasesessionservicelogic

import (
	"context"
	"database/sql"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/public/httpc"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/codes"
)

type CreateBlackBoxCaseSessionLogic struct {
	*logic.BaseLogic
}

func NewCreateBlackBoxCaseSessionLogic(
	ctx context.Context, svcCtx *svc.ServiceContext,
) *CreateBlackBoxCaseSessionLogic {
	return &CreateBlackBoxCaseSessionLogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),
	}
}

// CreateBlackBoxCaseSession creates a black box case session
func (l *CreateBlackBoxCaseSessionLogic) CreateBlackBoxCaseSession(req *pb.CreateBlackBoxCaseSessionReq) (
	resp *pb.CreateBlackBoxCaseSessionResp, err error,
) {
	resp = new(pb.CreateBlackBoxCaseSessionResp)
	if l.CurrentUser == nil {
		return nil, nil
	}
	b, err := l.checkCreateBlackBoxCaseDocumentForSession(
		req.GetProjectId(), req.GetAssistantId(),
	)
	if !b {
		return resp, err
	}
	assistantDb, _ := l.SvcCtx.BlackBoxCaseAssistantModel.FindOneByProjectIdAssistantId(
		l.Ctx, req.GetProjectId(), req.GetAssistantId(),
	)
	id, err := l.GenerateAiSessionId(req.GetProjectId(), req.GetAssistantId())
	if err != nil {
		return nil, err
	}
	resp.SessionId = id
	resp.AssistantId = req.GetAssistantId()
	resp.ProjectId = req.GetProjectId()
	if pb.WorkSpaceType_WORK_SPACE_TYPE_PERSONAL == pb.WorkSpaceType(assistantDb.WorkSpaceType) {
		if assistantDb.Account.String != l.CurrentUser.Account {
			return nil, errorx.Err(
				codes.TheDoesNotBelongToTheOwnerFailure, "助手为个人模式，创建会话者不属于拥有者",
			)
		}
	}
	dbData := &model.BlackBoxCaseSession{
		ProjectId:   req.GetProjectId(),
		AssistantId: req.GetAssistantId(),
		SessionId:   id,
		SessionName: req.GetSessionName(),
		SessionDescription: sql.NullString{
			String: req.GetSessionDescription(),
			Valid:  req.GetSessionDescription() != "",
		},
		Account: sql.NullString{
			String: l.CurrentUser.Account,
			Valid:  true,
		},
		SessionRound: assistantDb.SessionRound,
		CreatedBy:    l.CurrentUser.Account,
		UpdatedBy:    l.CurrentUser.Account,
	}
	_, err = l.SvcCtx.BlackBoxCaseSessionModel.Insert(
		l.Ctx, nil, dbData,
	)
	if err != nil {
		return nil, err
	}

	err = httpc.CreateSession(l.SvcCtx.ExternalAiDomain, dbData)
	if err != nil {
		return nil, err
	}

	return resp, nil
}

func (l *CreateBlackBoxCaseSessionLogic) checkCreateBlackBoxCaseDocumentForSession(projectId, assistantId string) (
	b bool, err error,
) {
	testData := &logic.Data{
		ProjectId:   projectId,
		AssistantId: assistantId,
		SessionId:   "",
		DocumentId:  "",
		IsValid:     false,
	}
	assistantCheckChain := logic.NewAssistantCheckChain(l.BaseLogic)
	return testData.IsValid, assistantCheckChain.Handle(testData)
}
