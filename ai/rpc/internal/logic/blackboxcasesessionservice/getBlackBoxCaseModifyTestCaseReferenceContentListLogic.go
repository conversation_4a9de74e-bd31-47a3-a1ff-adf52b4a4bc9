package blackboxcasesessionservicelogic

import (
	"context"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/public/httpc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/logic"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
)

type GetBlackBoxCaseModifyTestCaseReferenceContentListLogic struct {
	*logic.BaseLogic
}

func NewGetBlackBoxCaseModifyTestCaseReferenceContentListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetBlackBoxCaseModifyTestCaseReferenceContentListLogic {
	return &GetBlackBoxCaseModifyTestCaseReferenceContentListLogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),
	}
}

// GetBlackBoxCaseModifyTestCaseReferenceContentList  get black box case modify reference content list
func (l *GetBlackBoxCaseModifyTestCaseReferenceContentListLogic) GetBlackBoxCaseModifyTestCaseReferenceContentList(
	in *pb.GetBlackBoxCaseModifyTestCaseReferenceContentListReq) (
	out *pb.GetBlackBoxCaseModifyTestCaseReferenceContentListResp, err error) {
	err, resp := httpc.GetBlackBoxCaseModifyTestCaseReferenceContentList(l.SvcCtx.ExternalAiDomain, in)
	if err != nil {
		return nil, err
	}
	if resp.Data != nil {
		return *resp.Data, nil
	}
	return &pb.GetBlackBoxCaseModifyTestCaseReferenceContentListResp{}, nil
}
