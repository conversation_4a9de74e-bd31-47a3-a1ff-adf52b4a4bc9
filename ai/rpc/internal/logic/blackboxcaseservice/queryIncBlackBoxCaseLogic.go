package blackboxcaseservicelogic

import (
	"context"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/metrics"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
)

type QueryIncBlackBoxCaseLogic struct {
	*logic.BaseLogic
}

func NewQueryIncBlackBoxCaseLogic(ctx context.Context, svcCtx *svc.ServiceContext) *QueryIncBlackBoxCaseLogic {
	return &QueryIncBlackBoxCaseLogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),
	}
}

// QueryIncBlackBoxCase list black box case
func (l *QueryIncBlackBoxCaseLogic) QueryIncBlackBoxCase(in *pb.QueryIncBlackBoxCaseReq) (out *pb.QueryIncBlackBoxCaseResp, err error) {
	err = l.SvcCtx.BlackBoxCaseModel.QueryInc(l.Ctx, nil, in.CaseId)
	if err != nil {
		return nil, err
	}
	total, err := l.SvcCtx.BlackBoxCaseModel.CountQueryTimes(l.Ctx)
	if err != nil {
		return nil, err
	}
	metrics.CaseQueryTotalGauge.Set(float64(total), []string{""}...)

	return &pb.QueryIncBlackBoxCaseResp{}, nil
}
