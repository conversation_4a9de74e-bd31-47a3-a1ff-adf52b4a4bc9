package blackboxcaseservicelogic

import (
	"context"
	"encoding/json"
	"github.com/zeromicro/go-zero/core/jsonx"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/model"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/stores/sqlx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
)

type UpdateBlackBoxCaseLogic struct {
	*logic.BaseLogic
}

func NewUpdateBlackBoxCaseLogic(ctx context.Context, svcCtx *svc.ServiceContext) *UpdateBlackBoxCaseLogic {
	return &UpdateBlackBoxCaseLogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),
	}
}

// UpdateBlackBoxCase create a black box case
func (l *UpdateBlackBoxCaseLogic) UpdateBlackBoxCase(in *pb.UpdateBlackBoxCaseReq) (
	out *pb.UpdateBlackBoxCaseResp, err error,
) {
	if l.CurrentUser == nil {
		return nil, nil
	}
	dbData, err := l.SvcCtx.BlackBoxCaseModel.FindOneByCaseId(
		l.Ctx, in.Item.GetCaseId(),
	)
	if !l.CheckBlackBoxCaseDbData(dbData, err) {
		return nil, errorx.Err(errorx.NotExists, "数据不存在")
	}

	// 若有数据,不允许修改knowledgeId
	if in.Item.KnowledgeId != "" && in.Item.KnowledgeId != dbData.KnowledgeId {
		revision, err := l.SvcCtx.BlackBoxCaseRevisionModel.FindByCaseId(
			l.Ctx, in.Item.CaseId,
		)
		if err != nil {
			return nil, err
		}
		if len(revision) > 0 {
			return nil, errors.New("该用例下存在数据,不允许变更知识文档")
		}
	}

	err = l.SvcCtx.BlackBoxCaseModel.Trans(
		l.Ctx, func(context context.Context, session sqlx.Session) error {
			in.Item.Id = int32(dbData.Id)
			if in.GetItem().GetReferenceDoc() == "" {
				dbData.ReferenceDoc = "{}"
			}
			if in.GetItem().GetEnableReference() {
				dbData.EnableReference = 1
			} else {
				dbData.EnableReference = 0
			}
			if in.GetItem().GetDirId() != "" {
				dbData.DirId = in.GetItem().GetDirId()
			}

			if in.GetItem().GetCaseId() != "" {
				dbData.CaseId = in.GetItem().GetCaseId()
			}

			if in.GetItem().GetCaseName() != "" {
				dbData.CaseName = in.GetItem().GetCaseName()
			}

			if in.GetItem().GetCaseContinueToWrite() != 0 {
				dbData.CaseContinueToWrite = in.GetItem().GetCaseContinueToWrite()
			}

			if in.GetItem().GetCaseModelCharacter() != "" {
				dbData.CaseModelCharacter = in.GetItem().GetCaseModelCharacter()
			}

			if in.GetItem().GetKnowledgeId() != "" {
				dbData.KnowledgeId = in.GetItem().GetKnowledgeId()
			}

			if in.GetItem().GetCaseRemarks() != "" {
				dbData.CaseRemarks = in.GetItem().GetCaseRemarks()
			}

			if in.GetItem().GetKnowledgeParagraphTitleId() != 0 {
				dbData.KnowledgeParagraphTitleId = in.GetItem().GetKnowledgeParagraphTitleId()
			}

			if in.GetItem().GetKnowledgeParagraphTitleText() != "" {
				dbData.KnowledgeParagraphTitleText = in.GetItem().GetKnowledgeParagraphTitleText()
			}

			if in.GetItem().GetKnowledgeSize() != "" {
				dbData.KnowledgeSize = in.GetItem().GetKnowledgeSize()
			}

			if len(in.GetItem().GetKnowledgeParagraphTitle()) != 0 {
				marshal, _ := json.Marshal(in.GetItem().GetKnowledgeParagraphTitle())

				dbData.KnowledgeParagraphTitles = string(marshal)
			}

			dbData.KnowledgeFixSugg = in.GetItem().GetKnowledgeFixSugg()
			_, err = l.SvcCtx.BlackBoxCaseModel.Update(l.Ctx, session, dbData)
			if err != nil {
				return err
			}
			return nil
		},
	)
	if err != nil {
		return nil, err
	}

	return &pb.UpdateBlackBoxCaseResp{}, nil
}

func (l *UpdateBlackBoxCaseLogic) ConvertKnowledgePgTitle() (err error) {
	if l.CurrentUser == nil {
		return nil
	}

	cases, err := l.SvcCtx.BlackBoxCaseModel.FindNoCacheByQuery(l.Ctx, l.SvcCtx.BlackBoxCaseModel.SelectBuilder())
	if err != nil {
		return err
	}

	updated := make([]*model.BlackBoxCase, 0)

	for _, blackboxCase := range cases {
		if blackboxCase.KnowledgeParagraphTitleId != 0 || blackboxCase.KnowledgeParagraphTitleText != "" {
			knowledgeDocPgTitle := make([]*pb.KnowledgeDocPgTitle, 0)
			knowledgeDocPgTitle = append(knowledgeDocPgTitle, &pb.KnowledgeDocPgTitle{
				Id:    blackboxCase.KnowledgeParagraphTitleId,
				Title: blackboxCase.KnowledgeParagraphTitleText,
			})

			blackboxCase.KnowledgeParagraphTitles = string(jsonx.MarshalIgnoreError(knowledgeDocPgTitle))
			updated = append(updated, blackboxCase)
		}
	}

	for _, boxCase := range updated {
		if _, err = l.SvcCtx.BlackBoxCaseModel.UpdateTX(l.Ctx, nil, boxCase); err != nil {
			l.Logger.Warnf(
				"failed to update the black box case,id: %d, error: %+v", boxCase.Id, err,
			)
			continue
		}
	}

	return nil
}
