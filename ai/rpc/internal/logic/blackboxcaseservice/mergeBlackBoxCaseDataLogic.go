package blackboxcaseservicelogic

import (
	"context"
	"errors"

	"github.com/zeromicro/go-zero/core/stores/sqlx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/public"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/public/httpc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/logic"
	blackboxcasedataservicelogic "gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/logic/blackboxcasedataservice"
	blackboxcaserevisionservicelogic "gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/logic/blackboxcaserevisionservice"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
)

type MergeBlackBoxCaseDataLogic struct {
	*logic.BaseLogic
}

func NewMergeBlackBoxCaseDataLogic(ctx context.Context, svcCtx *svc.ServiceContext) *MergeBlackBoxCaseDataLogic {
	return &MergeBlackBoxCaseDataLogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),
	}
}

// MergeBlackBoxCaseData list black box case
func (l *MergeBlackBoxCaseDataLogic) MergeBlackBoxCaseData(in *pb.MergeBlackBoxCaseDataReq) (out *pb.MergeBlackBoxCaseDataResp, err error) {
	if l.CurrentUser == nil {
		return nil, nil
	}

	// 根据caseId查询详情,用于创建新的case实例
	// 判断合并用例列表中是否存在不同的文档,有则不允许合并
	caseList := []*model.BlackBoxCase{}
	for i, caseId := range in.CaseIds {
		caseDetail, err := l.SvcCtx.BlackBoxCaseModel.FindOneByCaseId(l.Ctx, caseId)
		if !l.CheckBlackBoxCaseDbData(caseDetail, err) {
			return nil, errorx.Err(errorx.NotExists, "数据不存在")
		}
		caseList = append(caseList, caseDetail)
		if i > 0 {
			if caseList[i-1].KnowledgeId != caseDetail.KnowledgeId {
				return nil, errors.New("不允许合并不同文档的用例: " + caseDetail.KnowledgeId + " : " + caseList[i-1].KnowledgeId)
			}
			if caseList[i-1].DirId != caseDetail.DirId {
				return nil, errors.New("不允许合并不同目录的用例: " + caseDetail.DirId + " : " + caseList[i-1].DirId)
			}
		}
	}

	// 获取用例数据
	var caseTable [][]*httpc.BlackBoxCaseData
	for _, id := range in.CaseIds {
		revision, err := l.SvcCtx.BlackBoxCaseRevisionModel.FindLastByCaseId(
			l.Ctx, id,
		)
		if err != nil {
			return nil, err
		}
		if revision.RevisionId == "" {
			return nil, errors.New("该用例没有数据,无法合并: " + id)
		}
		dbData, err := l.SvcCtx.BlackBoxCaseDataModel.FindByRevisionId(
			l.Ctx, revision.RevisionId,
		)
		if err != nil {
			return nil, err
		}
		if len(dbData) == 0 {
			return nil, errors.New("该版本没有数据,无法合并: " + revision.RevisionId)
		}
		// 讲数据库数据复制到请求body中
		var caseTableList []*httpc.BlackBoxCaseData
		for _, d := range dbData {
			caseTableData := httpc.BlackBoxCaseData{
				OrderId:      d.OrderId,
				CaseName:     d.CaseName,
				Requirement:  d.Requirement,
				PreCondition: d.PreCondition,
				CaseStep:     d.CaseStep,
				ExpectResult: d.ExpectResult,
				Terminal:     d.Terminal,
				CaseLevel:    d.CaseLevel,
				Tag:          d.Tag,
			}
			caseTableList = append(caseTableList, &caseTableData)
		}

		caseTable = append(caseTable, caseTableList)
	}

	// 调用AI接口合并数据
	caseData, err := httpc.MergeCases(l.SvcCtx.ExternalAiDomain, httpc.MergeCasesReq{CaseTable: caseTable})
	if err != nil {
		return nil, err
	}

	var caseResId string
	err = l.SvcCtx.BlackBoxCaseModel.Trans(
		l.Ctx, func(context context.Context, session sqlx.Session) error {
			// 创建用例
			createLogic := NewCreateBlackBoxCaseLogic(l.Ctx, l.SvcCtx)
			createLogic.CurrentUser = l.CurrentUser
			caseOut, err := createLogic.CreateBlackBoxCase(&pb.CreateBlackBoxCaseReq{
				CaseName:    in.CaseName,
				DirId:       caseList[0].DirId,
				KnowledgeId: caseList[0].KnowledgeId,
				// 合并后的用例给AI属性默认值
				CaseContinueToWrite: 1,
				CaseModelCharacter:  "just_right",
			})
			if err != nil {
				return err
			}
			caseResId = caseOut.CaseId

			// 创建版本
			createRevisionLogic := blackboxcaserevisionservicelogic.NewCreateBlackBoxCaseRevisionLogic(l.Ctx, l.SvcCtx)
			createRevisionLogic.CurrentUser = l.CurrentUser
			revisionOut, err := createRevisionLogic.CreateBlackBoxCaseRevision(&pb.CreateBlackBoxCaseRevisionReq{
				RevisionName: public.GenerateRevisionName(),
				CaseId:       caseOut.CaseId,
			})
			if err != nil {
				return err
			}
			for i := range *caseData.Data {
				(*caseData.Data)[i].RevisionId = revisionOut.RevisionId
				(*caseData.Data)[i].CaseId = caseOut.CaseId
			}

			// 创建版本数据
			createDataLogic := blackboxcasedataservicelogic.NewCreateBlackBoxCaseDataLogic(l.Ctx, l.SvcCtx)
			createDataLogic.CurrentUser = l.CurrentUser
			createDataLogic.CreateBlackBoxCaseData(&pb.CreateBlackBoxCaseDataReq{
				Items: *caseData.Data,
			})

			return nil
		},
	)

	if err != nil {
		return nil, err
	}

	return &pb.MergeBlackBoxCaseDataResp{CaseId: caseResId}, nil
}
