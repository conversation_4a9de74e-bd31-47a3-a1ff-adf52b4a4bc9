package blackboxcaseservicelogic

import (
	"context"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/metrics"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
)

type CreateBlackBoxCaseLogic struct {
	*logic.BaseLogic
}

func NewCreateBlackBoxCaseLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CreateBlackBoxCaseLogic {
	return &CreateBlackBoxCaseLogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),
	}
}

// CreateBlackBoxCase create a black box case
func (l *CreateBlackBoxCaseLogic) CreateBlackBoxCase(in *pb.CreateBlackBoxCaseReq) (out *pb.CreateBlackBoxCaseResp, err error) {
	if l.CurrentUser == nil {
		return nil, nil
	}

	data, err := l.create(in)
	if err != nil {
		return nil, err
	}

	return &pb.CreateBlackBoxCaseResp{CaseId: data.CaseId}, nil
}

func (l *CreateBlackBoxCaseLogic) create(req *pb.CreateBlackBoxCaseReq) (
	*model.BlackBoxCase, error,
) {
	if l.CurrentUser == nil {
		return nil, nil
	}
	caseId, err := l.GenerateAiCaseId()
	if err != nil {
		return nil, err
	}

	caseItem := &model.BlackBoxCase{
		DirId:                     req.GetDirId(),
		CaseId:                    caseId,
		CaseName:                  req.GetCaseName(),
		CaseContinueToWrite:       req.GetCaseContinueToWrite(),
		CaseModelCharacter:        req.GetCaseModelCharacter(),
		CaseRemarks:               req.GetCaseRemarks(),
		KnowledgeId:               req.KnowledgeId,
		KnowledgeParagraphTitleId: req.GetKnowledgeParagraphTitleId(),
		CreatedBy:                 l.CurrentUser.Account,
		ReferenceDoc:              "{}",
		KnowledgeParagraphTitles:  "{}",
	}

	_, err = l.SvcCtx.BlackBoxCaseModel.Insert(l.Ctx, nil, caseItem)
	if err != nil {
		return nil, err
	}

	// 刷新metrics
	metrics.CaseTotalGauge.Inc([]string{""}...)
	return caseItem, nil
}
