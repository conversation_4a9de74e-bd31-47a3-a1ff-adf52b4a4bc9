package blackboxcaseservicelogic

import (
	"context"
	"strings"

	"github.com/zeromicro/go-zero/core/stores/sqlx"

	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/metrics"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
)

type DeleteBlackBoxCaseLogic struct {
	*logic.BaseLogic
}

func NewDeleteBlackBoxCaseLogic(ctx context.Context, svcCtx *svc.ServiceContext) *DeleteBlackBoxCaseLogic {
	return &DeleteBlackBoxCaseLogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),
	}
}

// DeleteBlackBoxCase create a black box case
func (l *DeleteBlackBoxCaseLogic) DeleteBlackBoxCase(in *pb.DeleteBlackBoxCaseReq) (out *pb.DeleteBlackBoxCaseResp, err error) {
	dbData, err := l.SvcCtx.BlackBoxCaseModel.FindOneByCaseId(
		l.Ctx, in.GetCaseId(),
	)

	if !l.CheckBlackBoxCaseDbData(dbData, err) {
		return nil, errorx.Err(errorx.NotExists, "数据不存在")
	}

	revisionList, err := l.SvcCtx.BlackBoxCaseRevisionModel.FindByCaseId(l.Ctx, dbData.CaseId)
	if err != nil {
		return nil, err
	}
	err = l.SvcCtx.BlackBoxCaseModel.Trans(
		l.Ctx, func(context context.Context, session sqlx.Session) error {
			if err = l.SvcCtx.BlackBoxCaseModel.LogicDelete(l.Ctx, session, dbData.Id); err != nil {
				return err
			}

			for _, revision := range revisionList {
				if err = l.SvcCtx.BlackBoxCaseDataModel.LogicDeleteByRevisionId(l.Ctx, session, revision.RevisionId); err != nil {
					return nil
				}
			}
			return nil
		},
	)

	// 刷新metrics
	// metricsTotal, err := l.SvcCtx.BlackBoxCaseModel.FindCount(l.Ctx, l.SvcCtx.BlackBoxCaseModel.SelectCountAllBuilder())
	// if err != nil {
	// 	return nil, err
	// }
	// metrics.CaseTotalGauge.Set(float64(metricsTotal), []string{""}...)

	// 刷新采纳用例总数
	revisonList, err := l.SvcCtx.BlackBoxCaseRevisionModel.FindCaseIdByAdopted(l.Ctx)
	if err != nil {
		l.BaseLogic.Error(err)
	}
	var caseIds []string
	var revisionIds []string
	for _, revision := range revisonList {
		caseIds = append(caseIds, revision.CaseId)
		revisionIds = append(revisionIds, revision.RevisionId)
	}
	caseIds = stringx.Distinct(caseIds)
	revisionIds = stringx.Distinct(revisionIds)
	l.SvcCtx.Redis.Set("ai_adopted_revision_ids", strings.Join(revisionIds, ","))
	metrics.CaseAdoptTotalGauge.Set(float64(len(caseIds)), []string{""}...)

	return &pb.DeleteBlackBoxCaseResp{}, nil
}
