package blackboxcaseservicelogic

import (
	"context"

	"github.com/zeromicro/go-zero/core/stores/sqlx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
)

type UpdateBlackBoxCaseAILogic struct {
	*logic.BaseLogic
}

func NewUpdateBlackBoxCaseAILogic(ctx context.Context, svcCtx *svc.ServiceContext) *UpdateBlackBoxCaseAILogic {
	return &UpdateBlackBoxCaseAILogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),
	}
}

// UpdateBlackBoxCase create a black box case
func (l *UpdateBlackBoxCaseAILogic) UpdateBlackBoxCaseAI(in *pb.UpdateBlackBoxCaseAIReq) (out *pb.UpdateBlackBoxCaseAIResp, err error) {
	if l.CurrentUser == nil {
		return nil, nil
	}
	dbData, err := l.SvcCtx.BlackBoxCaseModel.FindOneByCaseId(
		l.Ctx, in.GetCaseId(),
	)
	if !l.CheckBlackBoxCaseDbData(dbData, err) {
		return nil, errorx.Err(errorx.NotExists, "数据不存在")
	}

	err = l.SvcCtx.BlackBoxCaseModel.Trans(
		l.Ctx, func(context context.Context, session sqlx.Session) error {
			dbData.CaseName = in.CaseName
			dbData.CaseContinueToWrite = in.CaseContinueToWrite
			dbData.CaseModelCharacter = in.CaseModelCharacter
			err = l.SvcCtx.BlackBoxCaseModel.UpdateByObj(l.Ctx, session, dbData)
			if err != nil {
				return err
			}
			return nil
		},
	)
	if err != nil {
		return nil, err
	}

	return &pb.UpdateBlackBoxCaseAIResp{}, nil
}
