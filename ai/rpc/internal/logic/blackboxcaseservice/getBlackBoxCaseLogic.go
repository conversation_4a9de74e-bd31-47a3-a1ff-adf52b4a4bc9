package blackboxcaseservicelogic

import (
	"context"
	"encoding/json"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/public/httpc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"
)

type GetBlackBoxCaseLogic struct {
	*logic.BaseLogic
}

func NewGetBlackBoxCaseLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetBlackBoxCaseLogic {
	return &GetBlackBoxCaseLogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),
	}
}

// GetBlackBoxCase gets a black box case
func (l *GetBlackBoxCaseLogic) GetBlackBoxCase(in *pb.GetBlackBoxCaseReq) (out *pb.GetBlackBoxCaseResp, err error) {
	resp := &pb.GetBlackBoxCaseResp{Item: &pb.BlackBoxCase{}}
	dbData, err := l.SvcCtx.BlackBoxCaseModel.FindOneByCaseId(
		l.Ctx, in.GetCaseId(),
	)
	if !l.CheckBlackBoxCaseDbData(dbData, err) {
		return nil, errorx.Err(errorx.NotExists, "数据不存在")
	}

	if err = utils.Copy(resp.Item, dbData, l.Converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy black box case [%+v] to response, error: %+v",
			dbData, err,
		)
	}

	// V2 ADD
	knowledgeIds := []string{dbData.KnowledgeId}
	knowledgeRef, err := httpc.GetKnowledge(l.SvcCtx.ExternalAiDomain, knowledgeIds)
	if err != nil {
		return nil, err
	}
	for _, knowledge := range *knowledgeRef.Data {
		if knowledge.KnowledgeId == dbData.KnowledgeId {
			resp.Item.KnowledgeName = knowledge.Title
			resp.Item.KnowledgeContent = knowledge.FeishuUrl
		}
	}

	knowledgePgTitles := make([]*pb.KnowledgeDocPgTitle, 0)
	err = json.Unmarshal([]byte(dbData.KnowledgeParagraphTitles), &knowledgePgTitles)
	if err != nil {
		l.Logger.Warnf("[GetBlackBoxCaseLogic.GetBlackBoxCase] failed to unmarshal knowledge paragraph title text, error: %+v", err)
	} else {
		resp.Item.KnowledgeParagraphTitle = knowledgePgTitles
	}

	return resp, nil
}
