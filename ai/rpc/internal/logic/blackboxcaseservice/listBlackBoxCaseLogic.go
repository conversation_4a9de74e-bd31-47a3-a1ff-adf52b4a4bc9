package blackboxcaseservicelogic

import (
	"context"
	"math"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/metrics"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/public/httpc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"
)

type ListBlackBoxCaseLogic struct {
	*logic.BaseLogic
}

func NewListBlackBoxCaseLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ListBlackBoxCaseLogic {
	return &ListBlackBoxCaseLogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),
	}
}

// ListBlackBoxCase list black box case
func (l *ListBlackBoxCaseLogic) ListBlackBoxCase(in *pb.ListBlackBoxCaseReq) (out *pb.ListBlackBoxCaseResp, err error) {
	queryObj := model.ListBlackBoxCaseQueryObj{
		DirId:      in.GetDirId(),
		CaseName:   in.GetCaseName(),
		Pagination: in.GetPagination(),
	}

	dbDataList, err := l.SvcCtx.BlackBoxCaseModel.FindByQueryObj(l.Ctx, queryObj)
	if err != nil {
		return nil, err
	}

	total, err := l.SvcCtx.BlackBoxCaseModel.CountByQueryObj(l.Ctx, queryObj)
	if err != nil {
		return nil, err
	}

	out = &pb.ListBlackBoxCaseResp{Items: make([]*pb.BlackBoxCase, 0, len(dbDataList))}
	out.Items = make([]*pb.BlackBoxCase, 0, len(dbDataList))
	out.TotalCount = uint64(total)
	out.TotalPage = 1

	knowledgeIds := []string{}
	for _, db := range dbDataList {
		item := &pb.BlackBoxCase{}
		if err = utils.Copy(item, db, l.Converters...); err != nil {
			return nil, errors.Wrapf(
				errorx.Err(errorx.CopyToStructFailure, err.Error()),
				"failed to copy db[%+v] to response, error: %+v", db, err,
			)
		}
		knowledgeIds = append(knowledgeIds, db.KnowledgeId)
		out.Items = append(out.Items, item)
	}

	// 批量获取文档详情
	knowledgeRef, err := httpc.GetKnowledge(l.SvcCtx.ExternalAiDomain, knowledgeIds)
	if err != nil {
		return nil, err
	}

	// 把文档列表放入map后续使用
	knowledgeMap := make(map[string]httpc.ListKnowledgeResp)
	for _, knowledge := range *knowledgeRef.Data {
		knowledgeMap[knowledge.KnowledgeId] = *knowledge
	}

	// 更新文档相关字段
	for _, item := range out.Items {
		item.KnowledgeName = knowledgeMap[item.KnowledgeId].Title
		item.KnowledgeContent = knowledgeMap[item.KnowledgeId].FeishuUrl
	}

	pagination := in.GetPagination()
	if pagination != nil {
		out.CurrentPage = pagination.GetCurrentPage()
		out.PageSize = pagination.GetPageSize()
		out.TotalPage = uint64(math.Ceil(float64(out.TotalCount) / float64(out.PageSize)))
	}

	// 刷新访问metrics
	err = internal.UpdateMetricsRedis(l.SvcCtx, l.CurrentUser)
	if err != nil {
		println(err)
	}
	metrics.CaseListTotalGauge.Set(float64(1), l.CurrentUser.Account, l.CurrentUser.Fullname)

	err = internal.UpdateUsersMetrics(l.SvcCtx, l.CurrentUser)
	if err != nil {
		println(err)
	}

	//err = internal.AddUserPvMetricsRedis(l.Ctx, l.SvcCtx, l.CurrentUser)
	//if err != nil {
	//	l.Logger.Errorf("ListBlackBoxCaseLogic.ListBlackBoxCase AddUserPvMetricsRedis error:%s", err.Error())
	//}

	return out, nil
}
