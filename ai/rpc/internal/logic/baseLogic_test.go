package logic

import (
	"context"
	"fmt"
	"testing"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/svc"
)

func TestCheckChain(t *testing.T) {
	background := context.Background()
	logic := NewBaseLogic(background, svc.MockServiceContext())
	// 测试数据
	testData := &Data{
		ProjectId:   "project_id:Kqllt5-9fA-I5UOdhjA5d",
		AssistantId: "ai:assistant_id:Lijt7wjUUXuYwXCWarxM3",
		SessionId:   "ai:session_id:3kMDLXZFcu0QP6c1FgtgT",
		DocumentId:  "ai:document_id:OVB-arbYpbWwpt6gQRP5u",
		IsValid:     false,
	}
	// 创建责任链节点
	assistantCheckChain := NewAssistantCheckChain(logic)
	documentCheckChain := NewDocumentCheckChain(logic)
	sessionCheckChain := NewSessionCheckChain(logic)
	documentSessionRelationshipCheckChain := NewDocumentSessionRelationshipCheckChain(logic)
	// 设置责任链的顺序
	sessionCheckChain.SetNext(documentSessionRelationshipCheckChain)
	documentCheckChain.SetNext(sessionCheckChain)
	assistantCheckChain.SetNext(documentCheckChain)

	// 处理数据
	err := assistantCheckChain.Handle(testData)
	// 输出结果
	if testData.IsValid {
		fmt.Println("Data is valid")
	} else {
		fmt.Println("Data is invalid")
	}
	fmt.Println(err)
}
