package logic

import (
	"context"
	"strings"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/mr"
	"github.com/zeromicro/go-zero/core/stores/sqlc"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zyedidia/generic"
	"github.com/zyedidia/generic/hashmap"
	"github.com/zyedidia/generic/set"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/userinfo"
	commonutils "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/utils"
)

type (
	BaseLogic struct {
		logx.Logger
		Ctx         context.Context
		SvcCtx      *svc.ServiceContext
		CurrentUser *userinfo.UserInfo
		Converters  []commonutils.TypeConverter
	}

	Data struct {
		ProjectId   string `protobuf:"bytes,1,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`       // 项目ID
		AssistantId string `protobuf:"bytes,2,opt,name=assistant_id,json=assistantId,proto3" json:"assistant_id,omitempty"` // 助手ID
		SessionId   string `protobuf:"bytes,3,opt,name=session_id,json=sessionId,proto3" json:"session_id,omitempty"`       // 会话ID
		DocumentId  string `protobuf:"bytes,4,opt,name=document_id,json=documentId,proto3" json:"document_id,omitempty"`    // 文档ID
		IsValid     bool
	}
	HandlerChain interface {
		SetNext(handler HandlerChain)
		Handle(data *Data) error
	}

	// 定义基础责任链节点结构
	BaseHandlerChain struct {
		baseLogic   *BaseLogic
		nextHandler HandlerChain
	}
)

func NewBaseLogic(ctx context.Context, svcCtx *svc.ServiceContext) *BaseLogic {
	return &BaseLogic{
		Logger: logx.WithContext(ctx),
		Ctx:    ctx,
		SvcCtx: svcCtx,

		CurrentUser: userinfo.FromContext(ctx),

		Converters: []commonutils.TypeConverter{},
	}
}

func (b *BaseHandlerChain) SetNext(handler HandlerChain) {
	b.nextHandler = handler
}

// 定义检查数据是否为正数的责任链节点
type AssistantCheckChain struct {
	BaseHandlerChain
}

func NewAssistantCheckChain(baseLogic *BaseLogic) HandlerChain {
	return &AssistantCheckChain{
		BaseHandlerChain{
			baseLogic: baseLogic,
		},
	}
}

func (p *AssistantCheckChain) Handle(data *Data) error {
	dbAssistantData, err := p.baseLogic.SvcCtx.BlackBoxCaseAssistantModel.FindOneByProjectIdAssistantId(
		p.baseLogic.Ctx, data.ProjectId, data.AssistantId,
	)
	data.IsValid = p.baseLogic.CheckBlackBoxCaseAssistantDbData(dbAssistantData, err)
	if !data.IsValid {
		return errorx.Err(errorx.NotExists, "助手数据不存在")
	}
	if p.nextHandler != nil && data.IsValid {
		err = p.nextHandler.Handle(data)
	}
	return err
}

type DocumentCheckChain struct {
	BaseHandlerChain
}

func NewDocumentCheckChain(baseLogic *BaseLogic) HandlerChain {
	return &DocumentCheckChain{
		BaseHandlerChain{
			baseLogic: baseLogic,
		},
	}
}

func (p *DocumentCheckChain) Handle(data *Data) error {
	var errR = errorx.Err(errorx.NotExists, "文档数据不存在")
	dbDocumentData, err := p.baseLogic.SvcCtx.BlackBoxCaseDocumentModel.FindOneByProjectIdAssistantIdDocumentId(
		p.baseLogic.Ctx, data.ProjectId, data.AssistantId, data.DocumentId,
	)
	data.IsValid = p.baseLogic.CheckBlackBoxCaseDocumentDbData(dbDocumentData, err)
	if !data.IsValid {
		return errR
	}
	if p.nextHandler != nil {
		err = p.nextHandler.Handle(data)
	}
	return err
}

type SessionCheckChain struct {
	BaseHandlerChain
}

func NewSessionCheckChain(baseLogic *BaseLogic) HandlerChain {
	return &SessionCheckChain{
		BaseHandlerChain{
			baseLogic: baseLogic,
		},
	}
}

func (p *SessionCheckChain) Handle(data *Data) error {
	var errR = errorx.Err(errorx.NotExists, "会话数据不存在")
	dbDocumentData, err := p.baseLogic.SvcCtx.BlackBoxCaseSessionModel.FindOneByProjectIdAssistantIdSessionId(
		p.baseLogic.Ctx, data.ProjectId, data.AssistantId, data.SessionId,
	)
	data.IsValid = p.baseLogic.CheckBlackBoxCaseSessionDbData(dbDocumentData, err)
	if !data.IsValid {
		return errR
	}
	if p.nextHandler != nil {
		err = p.nextHandler.Handle(data)
	}
	return err
}

type DocumentSessionRelationshipCheckChain struct {
	BaseHandlerChain
}

func NewDocumentSessionRelationshipCheckChain(baseLogic *BaseLogic) HandlerChain {
	return &DocumentSessionRelationshipCheckChain{
		BaseHandlerChain{
			baseLogic: baseLogic,
		},
	}
}

func (p *DocumentSessionRelationshipCheckChain) Handle(data *Data) error {
	var errR = errorx.Err(errorx.AlreadyExists, "当前会话已存在文档")
	assistantDocumentSessionRelationship, err := p.baseLogic.SvcCtx.BlackBoxCaseAssistantDocumentSessionRelationshipModel.FindOneByProjectIdAssistantIdSessionId(
		p.baseLogic.Ctx, data.ProjectId, data.SessionId, data.DocumentId,
	)
	data.IsValid = !p.baseLogic.checkBlackBoxCaseSessionDocumentDbData(assistantDocumentSessionRelationship, err)
	if !data.IsValid {
		return errR
	}
	if p.nextHandler != nil {
		err = p.nextHandler.Handle(data)
	}
	return err
}

func (l *BaseLogic) GenerateAiDocumentId(projectId string, assistantId string) (string, error) {
	// projectId不使用DocumentId唯一键
	g := commonutils.NewUniqueIdGenerator(
		commonutils.WithGenerateFunc(utils.GenAiDocumentId), commonutils.WithIsUniqueFunc(
			func(id string) bool {
				r, err := l.SvcCtx.BlackBoxCaseDocumentModel.FindOneByProjectIdAssistantIdDocumentId(
					l.Ctx, projectId, assistantId, id,
				)
				if errors.Is(err, sqlx.ErrNotFound) || r == nil {
					return true
				}
				return false
			},
		),
	)
	id := g.Next()
	if id == "" {
		return "", errors.WithStack(
			errorx.Err(
				errorx.GenerateUniqueIdFailure, "failed to generate api plan id, please try it later",
			),
		)
	}

	return id, nil
}

func (l *BaseLogic) GenerateAiSessionId(projectId string, assistantId string) (string, error) {
	// projectId不使用DocumentId唯一键
	g := commonutils.NewUniqueIdGenerator(
		commonutils.WithGenerateFunc(utils.GenAiSessionId), commonutils.WithIsUniqueFunc(
			func(id string) bool {
				r, err := l.SvcCtx.BlackBoxCaseSessionModel.FindOneByProjectIdAssistantIdSessionId(
					l.Ctx, projectId, assistantId, id,
				)
				if errors.Is(err, sqlx.ErrNotFound) || r == nil {
					return true
				}
				return false
			},
		),
	)
	id := g.Next()
	if id == "" {
		return "", errors.WithStack(
			errorx.Err(
				errorx.GenerateUniqueIdFailure, "failed to generate api plan id, please try it later",
			),
		)
	}

	return id, nil
}

func (l *BaseLogic) CheckBlackBoxCaseMapDocumentDbData(data *model.BlackBoxCaseMapDocument, err error) bool {
	return CheckData(data, err)
}

func (l *BaseLogic) CheckBlackBoxCaseMapDbData(data *model.BlackBoxCaseMap, err error) bool {
	return CheckData(data, err)
}

func (l *BaseLogic) CheckBlackBoxCaseDirV2DbData(data *model.BlackBoxCaseDirV2, err error) bool {
	return CheckData(data, err)
}

func (l *BaseLogic) CheckBlackBoxCaseDirDbData(data *model.BlackBoxCaseDir, err error) bool {
	return CheckData(data, err)
}

func (l *BaseLogic) CheckBlackBoxCaseDbData(data *model.BlackBoxCase, err error) bool {
	return CheckData(data, err)
}

func (l *BaseLogic) CheckBlackBoxCaseKnowledgeDbData(data *model.BlackBoxCaseKnowledge, err error) bool {
	return CheckData(data, err)
}

func (l *BaseLogic) CheckBlackBoxCaseDataDbData(data *model.BlackBoxCaseData, err error) bool {
	return CheckData(data, err)
}

func (l *BaseLogic) CheckBlackBoxCaseRevisionDbData(data *model.BlackBoxCaseRevision, err error) bool {
	return CheckData(data, err)
}

func (l *BaseLogic) CheckBlackBoxCaseAssistantDbData(data *model.BlackBoxCaseAssistant, err error) bool {
	return CheckData(data, err)
}

func (l *BaseLogic) CheckBlackBoxCaseDocumentDbData(data *model.BlackBoxCaseDocument, err error) bool {
	return CheckData(data, err)
}
func (l *BaseLogic) checkBlackBoxCaseSessionDocumentDbData(
	data *model.BlackBoxCaseAssistantDocumentSessionRelationship, err error,
) bool {
	return CheckData(data, err)
}
func (l *BaseLogic) CheckBlackBoxCaseSessionDbData(
	data *model.BlackBoxCaseSession, err error,
) bool {
	return CheckData(data, err)
}

func (l *BaseLogic) GetDocIdsForStr(str string) []string {
	if len(str) == 0 {
		return make([]string, 0)
	}
	split := strings.Split(str, ",")
	setEle := set.NewHashset(uint64(len(split)), generic.Equals[string], generic.HashString)
	for _, s := range split {
		setEle.Put(s)
	}
	return setEle.Keys()
}

func (l *BaseLogic) getDocIdsForStr2Set(str string) set.Set[string] {
	split := strings.Split(str, ",")
	setEle := set.NewHashset(uint64(len(split)), generic.Equals[string], generic.HashString)
	if len(str) == 0 {
		return setEle
	}
	for _, s := range split {
		setEle.Put(s)
	}
	return setEle
}

func (l *BaseLogic) MergeDocIds2Str(strList []string, ids ...string) string {
	if strList == nil {
		strList = make([]string, 0)
	}
	strList = append(strList, ids...)
	setEle := set.NewHashset(uint64(len(strList)), generic.Equals[string], generic.HashString)
	for _, s := range strList {
		setEle.Put(s)
	}
	return strings.Join(setEle.Keys(), ",")
}

func (l *BaseLogic) DeleteDocByDocIdList(projectId, assistantId, currentUser string, docIds ...string) error {
	docIdSetEle := set.NewHashset(uint64(len(docIds)), generic.Equals[string], generic.HashString)
	for _, id := range docIds {
		docIdSetEle.Put(id)
	}
	if docIdSetEle.Size() <= 0 {
		return nil
	}
	dbAssistantData, err := l.SvcCtx.BlackBoxCaseAssistantModel.FindOneByProjectIdAssistantId(
		l.Ctx, projectId, assistantId,
	)
	if err != nil {
		return err
	}
	if !l.CheckBlackBoxCaseAssistantDbData(dbAssistantData, err) {
		return errorx.Err(errorx.NotExists, "助手数据不存在")
	}
	// 删除文档
	queryList, err := l.SvcCtx.BlackBoxCaseDocumentModel.FindByDocumentIdList(l.Ctx, docIds)
	if err != nil {
		return err
	}
	setEle, hashmapEle := l.buildFindByDocumentSetMap(queryList)
	// 删除助手中关联文档
	intersectionSet := docIdSetEle.Intersection(setEle)
	if intersectionSet.Size() <= 0 {
		return nil
	}
	fn := func(context context.Context, session sqlx.Session) error {
		assistantDocIds := set.NewHashset(uint64(intersectionSet.Size()), generic.Equals[string], generic.HashString)
		err := mr.MapReduceVoid[*model.BlackBoxCaseDocument, string](
			func(source chan<- *model.BlackBoxCaseDocument) {
				for _, remove := range intersectionSet.Keys() {
					if remove == "" {
						continue
					}
					item, ok := hashmapEle.Get(remove)
					if !ok || item.Id == 0 {
						continue
					}
					source <- item
				}
			}, func(item *model.BlackBoxCaseDocument, writer mr.Writer[string], cancel func(error)) {
				var err error
				defer func() {
					if err != nil {
						cancel(err)
					}
				}()
				if item == nil {
					return
				}
				// 同步删除外部文档调用接口
				if err = l.SvcCtx.BlackBoxCaseDocumentModel.LogicDelete(
					context, session, item.Id,
				); err != nil {
					err = errors.Wrapf(
						errorx.Err(errorx.DBError, err.Error()),
						"failed to remove deleteDocByDocIdList by project_id[%s], plan_id[%s], document_id[%s] , error: %+v",
						item.ProjectId, item.AssistantId, item.DocumentId, err,
					)
					return
				}
				// 删除会话中使用这个文档的会话关联表
				if err = l.SvcCtx.BlackBoxCaseAssistantDocumentSessionRelationshipModel.DeleteByProjectIdDocumentIds(
					l.Ctx, session, projectId, item.DocumentId,
				); err != nil {
					err = errors.Wrapf(
						errorx.Err(errorx.DBError, err.Error()),
						"failed to remove deleteDocByDocIdList by project_id[%s], plan_id[%s], document_id[%s] , error: %+v",
						item.ProjectId, item.AssistantId, item.DocumentId, err,
					)
					return
				}
				writer.Write(item.DocumentId)
			}, func(pipe <-chan string, cancel func(error)) {
				var err error
				defer func() {
					if err != nil {
						cancel(err)
					}
				}()
				// 汇总要删除的数据
				for p := range pipe {
					assistantDocIds.Put(p)
				}
			},
		)
		if err != nil {
			return err
		}
		// 删除助手下文档数据
		difference := l.getDocIdsForStr2Set(dbAssistantData.DocumentIds.String).Difference(assistantDocIds)
		err = l.SvcCtx.BlackBoxCaseAssistantModel.UpdateDocumentIdsById(
			l.Ctx, session, dbAssistantData.Id, l.MergeDocIds2Str(difference.Keys()),
		)
		if err != nil {
			return err
		}
		return nil
	}
	err = l.SvcCtx.BlackBoxCaseDocumentModel.Trans(
		l.Ctx, func(context context.Context, session sqlx.Session) error {
			return fn(context, session)
		},
	)
	return err
}

func (l *BaseLogic) buildFindByDocumentSetMap(blackBoxCaseDocumentList []*model.BlackBoxCaseDocument) (
	set.Set[string], *hashmap.Map[string, *model.BlackBoxCaseDocument],
) {
	setEle := set.NewHashset(uint64(len(blackBoxCaseDocumentList)), generic.Equals[string], generic.HashString)
	hashmapEle := hashmap.New[string, *model.BlackBoxCaseDocument](
		uint64(len(blackBoxCaseDocumentList)), generic.Equals[string], generic.HashString,
	)
	for _, data := range blackBoxCaseDocumentList {
		setEle.Put(data.DocumentId)
		hashmapEle.Put(data.DocumentId, data)
	}
	return setEle, hashmapEle
}

func (l *BaseLogic) GenerateAiAssistantId(projectId string) (string, error) {
	g := commonutils.NewUniqueIdGenerator(
		commonutils.WithGenerateFunc(utils.GenAiAssistantId), commonutils.WithIsUniqueFunc(
			func(id string) bool {
				r, err := l.SvcCtx.BlackBoxCaseAssistantModel.FindOneByProjectIdAssistantId(l.Ctx, projectId, id)
				if errors.Is(err, model.ErrNotFound) || r == nil {
					return true
				}
				return false
			},
		),
	)
	aiAssistantId := g.Next()
	if aiAssistantId == "" {
		return "", errors.WithStack(
			errorx.Err(
				errorx.GenerateUniqueIdFailure, "failed to generate api plan id, please try it later",
			),
		)
	}

	return aiAssistantId, nil
}

func (l *BaseLogic) GenerateAiDirId(projectId string) (string, error) {
	g := commonutils.NewUniqueIdGenerator(
		commonutils.WithGenerateFunc(utils.GenAiDirId), commonutils.WithIsUniqueFunc(
			func(id string) bool {
				r, err := l.SvcCtx.BlackBoxCaseDirModel.FindOneByProjectIdDirId(l.Ctx, projectId, id)
				if errors.Is(err, model.ErrNotFound) || r == nil {
					return true
				}
				return false
			},
		),
	)
	aiDirId := g.Next()
	if aiDirId == "" {
		return "", errors.WithStack(
			errorx.Err(
				errorx.GenerateUniqueIdFailure, "failed to generate api plan id, please try it later",
			),
		)
	}

	return aiDirId, nil
}

func (l *BaseLogic) GenerateAiCaseId() (string, error) {
	// projectId不使用DocumentId唯一键
	g := commonutils.NewUniqueIdGenerator(
		commonutils.WithGenerateFunc(utils.GenAiCaseId), commonutils.WithIsUniqueFunc(
			func(id string) bool {
				r, err := l.SvcCtx.BlackBoxCaseModel.FindOneByCaseId(
					l.Ctx, id,
				)
				if errors.Is(err, sqlx.ErrNotFound) || r == nil {
					return true
				}
				return false
			},
		),
	)
	id := g.Next()
	if id == "" {
		return "", errors.WithStack(
			errorx.Err(
				errorx.GenerateUniqueIdFailure, "failed to generate api plan id, please try it later",
			),
		)
	}

	return id, nil
}

func (l *BaseLogic) GenerateAiRefId() (string, error) {
	g := commonutils.NewUniqueIdGenerator(
		commonutils.WithGenerateFunc(utils.GenAiRefId), commonutils.WithIsUniqueFunc(
			func(id string) bool {
				r, err := l.SvcCtx.BlackBoxCaseRefModel.FindOneByCaseRefId(l.Ctx, id)
				if errors.Is(err, model.ErrNotFound) || r == nil {
					return true
				}
				return false
			},
		),
	)
	aiRefId := g.Next()
	if aiRefId == "" {
		return "", errors.WithStack(
			errorx.Err(
				errorx.GenerateUniqueIdFailure, "failed to generate api plan id, please try it later",
			),
		)
	}

	return aiRefId, nil
}

func (l *BaseLogic) GenerateTwBetaId() (string, error) {
	g := commonutils.NewUniqueIdGenerator(
		commonutils.WithGenerateFunc(utils.GenTwBetaId), commonutils.WithIsUniqueFunc(
			func(id string) bool {
				r, err := l.SvcCtx.BlackBoxCaseTwBetaModel.FindOneByBetaCaseId(l.Ctx, id)
				if errors.Is(err, model.ErrNotFound) || r == nil {
					return true
				}
				return false
			},
		),
	)
	twBetaId := g.Next()
	if twBetaId == "" {
		return "", errors.WithStack(
			errorx.Err(
				errorx.GenerateUniqueIdFailure, "failed to generate api plan id, please try it later",
			),
		)
	}
	return twBetaId, nil
}

func (l *BaseLogic) GenerateAiKnowledgeId() (string, error) {
	g := commonutils.NewUniqueIdGenerator(
		commonutils.WithGenerateFunc(utils.GenAiKnowledgeId), commonutils.WithIsUniqueFunc(
			func(id string) bool {
				r, err := l.SvcCtx.BlackBoxCaseKnowledgeModel.FindOneByKnowledgeId(l.Ctx, id)
				if errors.Is(err, model.ErrNotFound) || r == nil {
					return true
				}
				return false
			},
		),
	)
	aiKnowledgeId := g.Next()
	if aiKnowledgeId == "" {
		return "", errors.WithStack(
			errorx.Err(
				errorx.GenerateUniqueIdFailure, "failed to generate api plan id, please try it later",
			),
		)
	}

	return aiKnowledgeId, nil
}

func (l *BaseLogic) GenerateAiRevisionId() (string, error) {
	g := commonutils.NewUniqueIdGenerator(
		commonutils.WithGenerateFunc(utils.GenAiRevisionId), commonutils.WithIsUniqueFunc(
			func(id string) bool {
				r, err := l.SvcCtx.BlackBoxCaseRevisionModel.FindOneByRevisionId(l.Ctx, id)
				if errors.Is(err, model.ErrNotFound) || r == nil {
					return true
				}
				return false
			},
		),
	)
	aiRevisionId := g.Next()
	if aiRevisionId == "" {
		return "", errors.WithStack(
			errorx.Err(
				errorx.GenerateUniqueIdFailure, "failed to generate api plan id, please try it later",
			),
		)
	}

	return aiRevisionId, nil
}

func (l *BaseLogic) GenerateAiDataId() (string, error) {
	g := commonutils.NewUniqueIdGenerator(
		commonutils.WithGenerateFunc(utils.GenAiDataId), commonutils.WithIsUniqueFunc(
			func(id string) bool {
				r, err := l.SvcCtx.BlackBoxCaseDataModel.FindOneByCaseDataId(l.Ctx, id)
				if errors.Is(err, model.ErrNotFound) || r == nil {
					return true
				}
				return false
			},
		),
	)
	aiDataId := g.Next()
	if aiDataId == "" {
		return "", errors.WithStack(
			errorx.Err(
				errorx.GenerateUniqueIdFailure, "failed to generate api plan id, please try it later",
			),
		)
	}

	return aiDataId, nil
}

func (l *BaseLogic) GenerateAiMapId() (string, error) {
	g := commonutils.NewUniqueIdGenerator(
		commonutils.WithGenerateFunc(utils.GenAiMapId), commonutils.WithIsUniqueFunc(
			func(id string) bool {
				r, err := l.SvcCtx.BlackBoxCaseMapModel.FindByMapId(l.Ctx, id)
				if errors.Is(err, model.ErrNotFound) || r == nil || len(r) == 0 {
					return true
				}
				return false
			},
		),
	)
	aiDataId := g.Next()
	if aiDataId == "" {
		return "", errors.WithStack(
			errorx.Err(
				errorx.GenerateUniqueIdFailure, "failed to generate api plan id, please try it later",
			),
		)
	}

	return aiDataId, nil
}

func CheckData[T any](data *T, err error) bool {
	if errors.Is(err, sqlc.ErrNotFound) || data == nil {
		return false
	} else if err != nil {
		return false
	}
	return true
}
