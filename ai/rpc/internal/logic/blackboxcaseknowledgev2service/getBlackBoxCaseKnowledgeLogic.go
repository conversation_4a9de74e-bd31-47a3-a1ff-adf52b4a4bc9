package blackboxcaseknowledgev2servicelogic

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"
)

type GetBlackBoxCaseKnowledgeLogic struct {
	*logic.BaseLogic
}

func NewGetBlackBoxCaseKnowledgeLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetBlackBoxCaseKnowledgeLogic {
	return &GetBlackBoxCaseKnowledgeLogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),
	}
}

// GetBlackBoxCaseKnowledge get blackbox case knowledge
func (l *GetBlackBoxCaseKnowledgeLogic) GetBlackBoxCaseKnowledge(in *pb.GetBlackBoxCaseKnowledgeV2Req) (out *pb.GetBlackBoxCaseKnowledgeV2Resp, err error) {
	if l.CurrentUser == nil {
		return nil, nil
	}

	resp := &pb.GetBlackBoxCaseKnowledgeV2Resp{}
	dbData, err := l.SvcCtx.BlackBoxCaseMapModel.FindOneByCaseId(
		l.Ctx, in.GetCaseId(),
	)
	if !l.CheckBlackBoxCaseMapDbData(dbData, err) {
		// return nil, errorx.Err(errorx.NotExists, "数据不存在")
		return resp, nil
	}

	if err = utils.Copy(resp, dbData, l.Converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy blackbox case map[%+v] to response, error: %+v",
			dbData, err,
		)
	}

	return resp, nil
}
