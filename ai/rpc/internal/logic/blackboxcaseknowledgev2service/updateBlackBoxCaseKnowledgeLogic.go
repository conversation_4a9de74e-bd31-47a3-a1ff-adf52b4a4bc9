package blackboxcaseknowledgev2servicelogic

import (
	"context"

	"github.com/zeromicro/go-zero/core/stores/sqlx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
)

type UpdateBlackBoxCaseKnowledgeLogic struct {
	*logic.BaseLogic
}

func NewUpdateBlackBoxCaseKnowledgeLogic(
	ctx context.Context, svcCtx *svc.ServiceContext,
) *UpdateBlackBoxCaseKnowledgeLogic {
	return &UpdateBlackBoxCaseKnowledgeLogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),
	}
}

// UpdateBlackBoxCaseKnowledge update blackbox case knowledge
func (l *UpdateBlackBoxCaseKnowledgeLogic) UpdateBlackBoxCaseKnowledge(in *pb.UpdateBlackBoxCaseKnowledgeV2Req) (
	out *pb.UpdateBlackBoxCaseKnowledgeV2Resp, err error,
) {
	if l.CurrentUser == nil {
		return nil, nil
	}

	dbData, err := l.SvcCtx.BlackBoxCaseMapModel.FindOneByCaseId(
		l.Ctx, in.GetCaseId(),
	)
	if !l.CheckBlackBoxCaseMapDbData(dbData, err) {
		if _, err = l.create(in); err != nil {
			return nil, err
		} else {
			return &pb.UpdateBlackBoxCaseKnowledgeV2Resp{}, nil
		}
	}

	err = l.SvcCtx.BlackBoxCaseDirModel.Trans(
		l.Ctx, func(context context.Context, session sqlx.Session) error {
			dbData.UpdatedBy = l.CurrentUser.Account
			dbData.Tags = in.GetTags()
			dbData.Experiences = in.GetExperiences()

			_, err = l.SvcCtx.BlackBoxCaseMapModel.Update(l.Ctx, session, dbData)
			if err != nil {
				return err
			}
			return nil
		},
	)

	return &pb.UpdateBlackBoxCaseKnowledgeV2Resp{}, err
}

func (l *UpdateBlackBoxCaseKnowledgeLogic) create(in *pb.UpdateBlackBoxCaseKnowledgeV2Req) (
	*model.BlackBoxCaseMap, error,
) {
	if l.CurrentUser == nil {
		return nil, nil
	}

	// mapId, err := l.GenerateAiMapId()
	// if err != nil {
	// 	return nil, err
	// }

	model := &model.BlackBoxCaseMap{
		ProjectId: in.GetProjectId(),
		DirId:     in.GetDirId(),
		CaseId:    in.GetCaseId(),
		//MapId:       mapId,
		Data:        "{}",
		Tags:        in.GetTags(),
		Experiences: in.GetExperiences(),
		CreatedBy:   l.CurrentUser.Account,
	}

	_, err := l.SvcCtx.BlackBoxCaseMapModel.Insert(l.Ctx, nil, model)
	if err != nil {
		return nil, err
	}
	return model, nil
}
