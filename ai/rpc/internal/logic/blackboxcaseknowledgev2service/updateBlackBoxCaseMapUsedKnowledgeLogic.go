package blackboxcaseknowledgev2servicelogic

import (
	"context"
	"database/sql"

	"github.com/zeromicro/go-zero/core/stores/sqlx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
)

type UpdateBlackBoxCaseMapUsedKnowledgeLogic struct {
	*logic.BaseLogic
}

func NewUpdateBlackBoxCaseMapUsedKnowledgeLogic(
	ctx context.Context, svcCtx *svc.ServiceContext,
) *UpdateBlackBoxCaseMapUsedKnowledgeLogic {
	return &UpdateBlackBoxCaseMapUsedKnowledgeLogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),
	}
}

// UpdateBlackBoxCaseMapUsedKnowledge update blackbox case knowledge
func (l *UpdateBlackBoxCaseMapUsedKnowledgeLogic) UpdateBlackBoxCaseMapUsedKnowledge(in *pb.UpdateBlackBoxCaseMapUsedKnowledgeReq) (
	out *pb.UpdateBlackBoxCaseMapUsedKnowledgeResp, err error,
) {
	if l.CurrentUser == nil {
		return nil, nil
	}

	dbData, err := l.SvcCtx.BlackBoxCaseMapModel.FindOneByCaseId(
		l.Ctx, in.GetCaseId(),
	)
	if !l.CheckBlackBoxCaseMapDbData(dbData, err) {
		return nil, errorx.Err(errorx.NotExists, "数据不存在")
	}

	err = l.SvcCtx.BlackBoxCaseDirModel.Trans(
		l.Ctx, func(context context.Context, session sqlx.Session) error {
			dbData.UpdatedBy = l.CurrentUser.Account
			dbData.UsedKnowledge = sql.NullString{
				String: in.GetUsedKnowledge(),
				Valid:  in.GetUsedKnowledge() != "",
			}

			_, err = l.SvcCtx.BlackBoxCaseMapModel.Update(l.Ctx, session, dbData)
			if err != nil {
				return err
			}
			return nil
		},
	)

	return &pb.UpdateBlackBoxCaseMapUsedKnowledgeResp{}, nil
}
