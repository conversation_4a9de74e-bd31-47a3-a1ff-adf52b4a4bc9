package blackboxcasedataservicelogic

import (
	"context"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
)

type GenerateBlackBoxCaseRefLogic struct {
	*logic.BaseLogic
}

func NewGenerateBlackBoxCaseRefLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GenerateBlackBoxCaseRefLogic {
	return &GenerateBlackBoxCaseRefLogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),
	}
}

// ReplaceBlackBoxCaseData replace black box case
func (l *GenerateBlackBoxCaseRefLogic) GenerateBlackBoxCaseRef(in *pb.GenerateBlackBoxCaseRefReq) (out *pb.GenerateBlackBoxCaseRefResp, err error) {
	if l.CurrentUser == nil {
		return nil, nil
	}

	data, err := l.create(in)
	if err != nil {
		return nil, err
	}

	return &pb.GenerateBlackBoxCaseRefResp{CaseRefId: data.CaseRefId}, nil
}

func (l *GenerateBlackBoxCaseRefLogic) create(in *pb.GenerateBlackBoxCaseRefReq) (
	*model.BlackBoxCaseRef, error,
) {
	if l.CurrentUser == nil {
		return nil, nil
	}

	refId, err := l.GenerateAiRefId()
	if err != nil {
		return nil, err
	}

	ref := &model.BlackBoxCaseRef{
		CaseRefId:   refId,
		CreatedBy:   l.CurrentUser.Account,
		CaseRefType: in.CaseRefType,
	}

	_, err = l.SvcCtx.BlackBoxCaseRefModel.Insert(l.Ctx, nil, ref)
	if err != nil {
		return nil, err
	}
	return ref, nil
}
