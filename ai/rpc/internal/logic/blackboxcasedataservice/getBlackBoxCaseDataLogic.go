package blackboxcasedataservicelogic

import (
	"context"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
)

type GetBlackBoxCaseDataLogic struct {
	*logic.BaseLogic
}

func NewGetBlackBoxCaseDataLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetBlackBoxCaseDataLogic {
	return &GetBlackBoxCaseDataLogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),
	}
}

// GetBlackBoxCaseData gets a black box case
func (l *GetBlackBoxCaseDataLogic) GetBlackBoxCaseData(in *pb.GetBlackBoxCaseDataReq) (out *pb.GetBlackBoxCaseDataResp, err error) {
	// todo: add your logic here and delete this line

	return &pb.GetBlackBoxCaseDataResp{}, nil
}
