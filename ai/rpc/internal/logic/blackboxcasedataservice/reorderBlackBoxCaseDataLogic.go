package blackboxcasedataservicelogic

import (
	"context"

	"github.com/zeromicro/go-zero/core/stores/sqlx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/public/httpc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
)

type ReorderBlackBoxCaseDataLogic struct {
	*logic.BaseLogic
}

func NewReorderBlackBoxCaseDataLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ReorderBlackBoxCaseDataLogic {
	return &ReorderBlackBoxCaseDataLogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),
	}
}

// ReorderBlackBoxCaseData list black box case
func (l *ReorderBlackBoxCaseDataLogic) ReorderBlackBoxCaseData(in *pb.ReorderBlackBoxCaseDataReq) (out *pb.ReorderBlackBoxCaseDataResp, err error) {
	// 获取版本详情拿到用例id
	revision, err := l.SvcCtx.BlackBoxCaseRevisionModel.FindOneByRevisionId(
		l.Ctx, in.RevisionId,
	)
	if err != nil {
		return nil, err
	}
	// 获取用例详情拿到文档id
	caseDetail, err := l.SvcCtx.BlackBoxCaseModel.FindOneByCaseId(
		l.Ctx, revision.CaseId,
	)
	if err != nil {
		return nil, err
	}
	// 获取版本数据
	listDataLogic := NewListBlackBoxCaseDataLogic(l.Ctx, l.SvcCtx)
	caseData, err := listDataLogic.ListBlackBoxCaseData(&pb.ListBlackBoxCaseDataReq{
		RevisionId: in.RevisionId,
	})
	if err != nil {
		return nil, err
	}

	// 调用ai接口排序
	newItems, err := httpc.ReorderCaseData(l.SvcCtx.ExternalAiDomain, httpc.ReorderCaseDataReq{
		KnowledgeId: caseDetail.KnowledgeId,
		CaseTable:   caseData.Items,
	})
	if err != nil {
		return nil, err
	}

	// 放入map方便后续使用
	caseMap := make(map[string]string)
	for _, item := range *newItems.Data {
		caseMap[item.CaseDataId] = item.OrderId
	}

	// 根据caseDataId找到对应数据,只更新orderId
	err = l.SvcCtx.BlackBoxCaseDataModel.Trans(
		l.Ctx, func(context context.Context, session sqlx.Session) error {
			for _, data := range caseData.Items {
				// orderId变更才进行更新
				if data.OrderId != caseMap[data.CaseDataId] {
					caseDataDb := model.BlackBoxCaseData{
						OrderId:    caseMap[data.CaseDataId],
						CaseDataId: data.CaseDataId,
					}
					err = l.SvcCtx.BlackBoxCaseDataModel.UpdateByObj(l.Ctx, session, &caseDataDb)
					if err != nil {
						return err
					}
				}
			}
			return nil
		},
	)
	if err != nil {
		return nil, err
	}

	return &pb.ReorderBlackBoxCaseDataResp{}, nil
}
