package blackboxcasedataservicelogic

import (
	"context"
	"database/sql"

	"github.com/zeromicro/go-zero/core/stores/sqlx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
)

type DeleteBlackBoxCaseDataLogic struct {
	*logic.BaseLogic
}

func NewDeleteBlackBoxCaseDataLogic(ctx context.Context, svcCtx *svc.ServiceContext) *DeleteBlackBoxCaseDataLogic {
	return &DeleteBlackBoxCaseDataLogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),
	}
}

// DeleteBlackBoxCaseData create a black box case
func (l *DeleteBlackBoxCaseDataLogic) DeleteBlackBoxCaseData(in *pb.DeleteBlackBoxCaseDataReq) (out *pb.DeleteBlackBoxCaseDataResp, err error) {
	dbData, err := l.SvcCtx.BlackBoxCaseDataModel.FindOneByCaseDataId(
		l.Ctx, in.GetCaseDataId(),
	)

	if !l.CheckBlackBoxCaseDataDbData(dbData, err) {
		return nil, errorx.Err(errorx.NotExists, "数据不存在")
	}

	dbData.DeletedBy = sql.NullString{
		String: l.CurrentUser.Account,
		Valid:  l.CurrentUser.Account != "",
	}
	err = l.SvcCtx.BlackBoxCaseDataModel.Trans(
		l.Ctx, func(context context.Context, session sqlx.Session) error {
			if err = l.SvcCtx.BlackBoxCaseDataModel.LogicDeleteAndStatic(l.Ctx, session, dbData.Id); err != nil {
				return err
			}
			return nil
		},
	)

	return &pb.DeleteBlackBoxCaseDataResp{}, nil
}
