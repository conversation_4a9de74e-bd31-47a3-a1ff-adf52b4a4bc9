package blackboxcasedataservicelogic

import (
	"context"

	"github.com/zeromicro/go-zero/core/logx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
)

type UpdateBlackBoxCaseDataOrderLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewUpdateBlackBoxCaseDataOrderLogic(ctx context.Context, svcCtx *svc.ServiceContext) *UpdateBlackBoxCaseDataOrderLogic {
	return &UpdateBlackBoxCaseDataOrderLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// UpdateBlackBoxCaseData create a black box case
func (l *UpdateBlackBoxCaseDataOrderLogic) UpdateBlackBoxCaseDataOrder(in *pb.UpdateBlackBoxCaseDataOrderReq) (out *pb.UpdateBlackBoxCaseDataOrderResp, err error) {
	// todo: add your logic here and delete this line

	return &pb.UpdateBlackBoxCaseDataOrderResp{}, nil
}
