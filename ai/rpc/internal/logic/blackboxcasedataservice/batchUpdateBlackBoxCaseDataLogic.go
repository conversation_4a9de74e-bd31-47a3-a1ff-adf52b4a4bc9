package blackboxcasedataservicelogic

import (
	"context"

	"github.com/zeromicro/go-zero/core/stores/sqlx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
)

type BatchUpdateBlackBoxCaseDataLogic struct {
	*logic.BaseLogic
}

func NewBatchUpdateBlackBoxCaseDataLogic(ctx context.Context, svcCtx *svc.ServiceContext) *BatchUpdateBlackBoxCaseDataLogic {
	return &BatchUpdateBlackBoxCaseDataLogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),
	}
}

// BatchUpdateBlackBoxCaseData create a black box case
func (l *BatchUpdateBlackBoxCaseDataLogic) BatchUpdateBlackBoxCaseData(in *pb.BatchUpdateBlackBoxCaseDataReq) (out *pb.BatchUpdateBlackBoxCaseDataResp, err error) {
	if l.CurrentUser == nil {
		return nil, nil
	}

	// 放入map在更新数据库时可根据是否变化来判断是否需要update
	caseDataMap := make(map[string]string)
	for _, item := range in.Items {
		caseDataMap[item.CaseDataId] = item.OrderId
	}

	caseData, err := l.SvcCtx.BlackBoxCaseDataModel.FindOneByCaseDataId(l.Ctx, in.Items[0].CaseDataId)
	if err != nil {
		return nil, err
	}
	caseDataList, err := l.SvcCtx.BlackBoxCaseDataModel.FindByRevisionId(l.Ctx, caseData.RevisionId)
	if err != nil {
		return nil, err
	}
	err = l.SvcCtx.BlackBoxCaseDataModel.Trans(
		l.Ctx, func(context context.Context, session sqlx.Session) error {
			for _, item := range caseDataList {
				if item.OrderId != caseDataMap[item.CaseDataId] {
					dbData := model.BlackBoxCaseData{}
					dbData.UpdatedBy = l.CurrentUser.Account
					dbData.OrderId = caseDataMap[item.CaseDataId]
					dbData.CaseDataId = item.CaseDataId
					err = l.SvcCtx.BlackBoxCaseDataModel.UpdateByObj(l.Ctx, session, &dbData)
					if err != nil {
						return err
					}
				}
			}
			return nil
		},
	)
	if err != nil {
		return nil, err
	}

	return &pb.BatchUpdateBlackBoxCaseDataResp{}, nil
}
