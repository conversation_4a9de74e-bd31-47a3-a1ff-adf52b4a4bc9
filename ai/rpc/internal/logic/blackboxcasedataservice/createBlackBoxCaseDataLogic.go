package blackboxcasedataservicelogic

import (
	"context"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
)

type CreateBlackBoxCaseDataLogic struct {
	*logic.BaseLogic
}

func NewCreateBlackBoxCaseDataLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CreateBlackBoxCaseDataLogic {
	return &CreateBlackBoxCaseDataLogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),
	}
}

// CreateBlackBoxCaseData create a black box case
func (l *CreateBlackBoxCaseDataLogic) CreateBlackBoxCaseData(in *pb.CreateBlackBoxCaseDataReq) (out *pb.CreateBlackBoxCaseDataResp, err error) {
	if l.CurrentUser == nil {
		return nil, nil
	}

	_, err = l.create(in)
	if err != nil {
		return nil, err
	}

	return &pb.CreateBlackBoxCaseDataResp{}, nil
}

func (l *CreateBlackBoxCaseDataLogic) create(req *pb.CreateBlackBoxCaseDataReq) (
	*model.BlackBoxCaseData, error,
) {
	if l.CurrentUser == nil {
		return nil, nil
	}

	for _, item := range req.Items {
		DataId, err := l.GenerateAiDataId()
		if err != nil {
			return nil, err
		}
		Data := &model.BlackBoxCaseData{
			CaseDataId:   DataId,
			RevisionId:   item.RevisionId,
			OrderId:      item.OrderId,
			Requirement:  item.Requirement,
			PreCondition: item.PreCondition,
			CaseStep:     item.CaseStep,
			ExpectResult: item.ExpectResult,
			Terminal:     item.Terminal,
			CaseLevel:    item.CaseLevel,
			Tag:          item.Tag,
			CaseName:     item.CaseName,
			CreatedBy:    l.CurrentUser.Account,
			Version:      item.Version,
		}

		_, err = l.SvcCtx.BlackBoxCaseDataModel.Insert(l.Ctx, nil, Data)
		if err != nil {
			return nil, err
		}
	}

	return nil, nil
}
