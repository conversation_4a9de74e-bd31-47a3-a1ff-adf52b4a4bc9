package blackboxcasedataservicelogic

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"github.com/zeromicro/go-zero/core/logx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
)

type ListBlackBoxCaseDataByCaseIdsLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewListBlackBoxCaseDataByCaseIdsLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ListBlackBoxCaseDataByCaseIdsLogic {
	return &ListBlackBoxCaseDataByCaseIdsLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// ListBlackBoxCaseDataByCaseIds list black box case by caseIds
func (l *ListBlackBoxCaseDataByCaseIdsLogic) ListBlackBoxCaseDataByCaseIds(in *pb.ListBlackBoxCaseDataByCaseIdsReq) (out *pb.ListBlackBoxCaseDataByCaseIdsResp, err error) {
	dbDataList, err := l.svcCtx.BlackBoxCaseDataModel.FindByCaseIds(l.ctx, in.GetCaseIds())
	if err != nil {
		return nil, err
	}

	out = &pb.ListBlackBoxCaseDataByCaseIdsResp{Items: make([]*pb.BlackBoxCaseData, 0, len(dbDataList))}
	for _, db := range dbDataList {
		item := &pb.BlackBoxCaseData{}
		if err = utils.Copy(item, db); err != nil {
			return nil, errors.Wrapf(
				errorx.Err(errorx.CopyToStructFailure, err.Error()),
				"failed to copy db[%+v] to response, error: %+v", db, err,
			)
		}
		out.Items = append(out.Items, item)
	}

	return out, nil
}
