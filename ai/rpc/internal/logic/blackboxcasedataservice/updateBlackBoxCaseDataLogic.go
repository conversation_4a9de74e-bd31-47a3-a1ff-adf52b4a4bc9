package blackboxcasedataservicelogic

import (
	"context"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/stores/sqlx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/public/util"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
)

type UpdateBlackBoxCaseDataLogic struct {
	*logic.BaseLogic
}

func NewUpdateBlackBoxCaseDataLogic(ctx context.Context, svcCtx *svc.ServiceContext) *UpdateBlackBoxCaseDataLogic {
	return &UpdateBlackBoxCaseDataLogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),
	}
}

// UpdateBlackBoxCaseData create a black box case
func (l *UpdateBlackBoxCaseDataLogic) UpdateBlackBoxCaseData(in *pb.UpdateBlackBoxCaseDataReq) (
	out *pb.UpdateBlackBoxCaseDataResp, err error,
) {
	if l.CurrentUser == nil {
		return nil, nil
	}
	dbData, err := l.SvcCtx.BlackBoxCaseDataModel.FindOneByCaseDataId(
		l.Ctx, in.CaseDataId,
	)
	if !l.CheckBlackBoxCaseDataDbData(dbData, err) {
		return nil, errorx.Err(errorx.NotExists, "数据不存在")
	}

	err = l.SvcCtx.BlackBoxCaseDataModel.Trans(
		l.Ctx, func(context context.Context, session sqlx.Session) error {
			item := &model.BlackBoxCaseData{Id: dbData.Id, CaseId: dbData.CaseId, RevisionId: dbData.RevisionId}
			if err = utils.Copy(item, in, l.Converters...); err != nil {
				return errors.Wrapf(
					errorx.Err(errorx.CopyToStructFailure, err.Error()),
					"failed to copy db[%+v] to response, error: %+v", in, err,
				)
			}
			item.UpdatedBy = l.CurrentUser.Account
			// 设置临时修改状态字段,为了指标统计,避免采纳后用例被修改影响结果.采纳后才更新正式字段
			item.UpdatedHumanTmp = util.BoolToInt64(true)
			_, err = l.SvcCtx.BlackBoxCaseDataModel.Update(l.Ctx, session, item)
			if err != nil {
				return err
			}
			return nil
		},
	)

	return &pb.UpdateBlackBoxCaseDataResp{}, nil
}
