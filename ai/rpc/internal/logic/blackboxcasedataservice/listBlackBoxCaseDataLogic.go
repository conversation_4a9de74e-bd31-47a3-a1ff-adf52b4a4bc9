package blackboxcasedataservicelogic

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/public/httpc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
)

type ListBlackBoxCaseDataLogic struct {
	*logic.BaseLogic
}

func NewListBlackBoxCaseDataLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ListBlackBoxCaseDataLogic {
	return &ListBlackBoxCaseDataLogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),
	}
}

// ListBlackBoxCaseData list black box case
func (l *ListBlackBoxCaseDataLogic) ListBlackBoxCaseData(in *pb.ListBlackBoxCaseDataReq) (
	out *pb.ListBlackBoxCaseDataResp, err error,
) {
	dbDataList := make([]*model.BlackBoxCaseData, 0)
	if in.GetCaseId() != "" {
		dbDataList, err = l.SvcCtx.BlackBoxCaseDataModel.FindByCaseId(
			l.Ctx, in.GetCaseId(),
		)
	} else if in.GetCaseId() == "" && in.GetRevisionId() != "" {
		dbDataList, err = l.SvcCtx.BlackBoxCaseDataModel.FindByRevisionId(
			l.Ctx, in.GetRevisionId(),
		)
	} else {
		return nil, errors.New("case_id和revision_id不能同时传入")
	}

	if err != nil {
		return nil, err
	}

	out = &pb.ListBlackBoxCaseDataResp{Items: make([]*pb.BlackBoxCaseData, 0, len(dbDataList)), Content: ""}
	for _, db := range dbDataList {
		item := &pb.BlackBoxCaseData{}
		if err = utils.Copy(item, db, l.Converters...); err != nil {
			return nil, errors.Wrapf(
				errorx.Err(errorx.CopyToStructFailure, err.Error()),
				"failed to copy db[%+v] to response, error: %+v", db, err,
			)
		}
		if item.CaseId != "" {
			item.IsKeep = true
		} else {
			item.IsKeep = false
		}
		out.Items = append(out.Items, item)
	}

	// 如果是markdown格式,通过ai接口转换
	if in.Type == "markdown" {
		dataContent, err := httpc.Json2MD(
			l.SvcCtx.ExternalAiDomain, httpc.CaseTableReq{
				CaseTable: out.Items,
			},
		)
		if err != nil {
			return nil, err
		}
		out.Content = *dataContent.Data
		out.Items = []*pb.BlackBoxCaseData{}
	}

	return out, nil
}
