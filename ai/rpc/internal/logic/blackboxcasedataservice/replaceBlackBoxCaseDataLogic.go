package blackboxcasedataservicelogic

import (
	"context"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/stores/sqlx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"
)

type ReplaceBlackBoxCaseDataLogic struct {
	*logic.BaseLogic
}

func NewReplaceBlackBoxCaseDataLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ReplaceBlackBoxCaseDataLogic {
	return &ReplaceBlackBoxCaseDataLogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),
	}
}

// ReplaceBlackBoxCaseData replace black box case
func (l *ReplaceBlackBoxCaseDataLogic) ReplaceBlackBoxCaseData(in *pb.ReplaceBlackBoxCaseDataReq) (out *pb.ReplaceBlackBoxCaseDataResp, err error) {
	if l.CurrentUser == nil {
		return nil, nil
	}

	err = l.SvcCtx.BlackBoxCaseDataModel.Trans(
		l.Ctx, func(context context.Context, session sqlx.Session) error {
			// 删除原有数据
			var caseDataIds []string
			for _, item := range in.GetItems() {
				caseDataIds = append(caseDataIds, item.CaseDataId)
			}
			l.SvcCtx.BlackBoxCaseDataModel.DeleteByDataIds(l.Ctx, session, caseDataIds)

			// 插入新数据
			appendLogic := NewAppendBlackBoxCaseDataLogic(l.Ctx, l.SvcCtx)
			_, err = appendLogic.AppendBlackBoxCaseData(&pb.AppendBlackBoxCaseDataReq{
				Items:      in.NewItems,
				RevisionId: in.RevisionId,
			})
			return err
		},
	)
	if err != nil {
		return nil, err
	}

	// 重新排序
	// reorderLogic := NewReorderBlackBoxCaseDataLogic(l.Ctx, l.SvcCtx)
	// _, err = reorderLogic.ReorderBlackBoxCaseData(&pb.ReorderBlackBoxCaseDataReq{
	// 	RevisionId: in.RevisionId,
	// })
	// if err != nil {
	// 	return nil, err
	// }

	// 由于事务中查询还包含传入的新数据,需要在事务外重新进行一次查询
	appendRes := &pb.AppendBlackBoxCaseDataResp{}
	dbDataList, err := l.SvcCtx.BlackBoxCaseDataModel.FindByRevisionId(l.Ctx, in.RevisionId)
	if err != nil {
		return nil, err
	}
	for _, data := range dbDataList {
		item := &pb.BlackBoxCaseData{}
		if err = utils.Copy(item, data, l.Converters...); err != nil {
			return nil, errors.Wrapf(
				errorx.Err(errorx.CopyToStructFailure, err.Error()),
				"failed to copy black box case assistant[%+v] to response, error: %+v",
				data, err,
			)
		}
		appendRes.Items = append(appendRes.Items, item)
	}

	return &pb.ReplaceBlackBoxCaseDataResp{NewItems: in.NewItems, Items: appendRes.Items}, nil
}
