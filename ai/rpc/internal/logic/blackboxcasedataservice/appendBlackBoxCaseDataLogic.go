package blackboxcasedataservicelogic

import (
	"context"
	"strconv"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"
)

type AppendBlackBoxCaseDataLogic struct {
	*logic.BaseLogic
}

func NewAppendBlackBoxCaseDataLogic(ctx context.Context, svcCtx *svc.ServiceContext) *AppendBlackBoxCaseDataLogic {
	return &AppendBlackBoxCaseDataLogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),
	}
}

// AppendBlackBoxCaseData append black box case
func (l *AppendBlackBoxCaseDataLogic) AppendBlackBoxCaseData(in *pb.AppendBlackBoxCaseDataReq) (out *pb.AppendBlackBoxCaseDataResp, err error) {
	if l.CurrentUser == nil {
		return nil, nil
	}
	dbData, err := l.SvcCtx.BlackBoxCaseRevisionModel.FindOneByRevisionId(
		l.Ctx, in.GetRevisionId(),
	)
	if !l.CheckBlackBoxCaseRevisionDbData(dbData, err) {
		return nil, errorx.Err(errorx.NotExists, "历史版本数据不存在")
	}

	// TODO 对序号做自动排序
	// 获取当前版本数量,对新增的order_id进行修改,避免重复
	maxId, err := l.SvcCtx.BlackBoxCaseDataModel.FindMaxOrderIdByRevisionId(l.Ctx, in.RevisionId)
	if err != nil {
		return nil, err
	}
	// 将当前数据追加到目标版本中
	resp := &pb.AppendBlackBoxCaseDataResp{NewItems: in.Items, Items: make([]*pb.BlackBoxCaseData, 0)}
	for i, item := range in.GetItems() {
		newItem := &model.BlackBoxCaseData{}

		caseDataId, err := l.GenerateAiDataId()
		if err != nil {
			return nil, err
		}
		// 将item也赋予caseDataId用于前端高亮展示
		item.CaseDataId = caseDataId
		newItem.CaseDataId = caseDataId
		newItem.RevisionId = in.GetRevisionId()
		// 追加的数据不属于保留的
		// newItem.CaseId = sql.NullString{
		// 	String: item.CaseDataId,
		// 	Valid:  item.CaseDataId != "",
		// }
		newItem.OrderId = strconv.FormatInt(maxId+int64(i)+1, 10)
		newItem.Requirement = item.Requirement
		newItem.PreCondition = item.PreCondition
		newItem.CaseStep = item.CaseStep
		newItem.ExpectResult = item.ExpectResult
		newItem.Terminal = item.Terminal
		newItem.CaseLevel = item.CaseLevel
		newItem.Tag = item.Tag
		newItem.CaseName = item.CaseName
		newItem.CreatedBy = l.CurrentUser.Account

		_, err = l.SvcCtx.BlackBoxCaseDataModel.Insert(l.Ctx, nil, newItem)
		if err != nil {
			return nil, err
		}
	}

	// 将新的版本数据取出作为items返回
	dbDataList, err := l.SvcCtx.BlackBoxCaseDataModel.FindByRevisionId(l.Ctx, in.RevisionId)
	if err != nil {
		return nil, err
	}
	for _, data := range dbDataList {
		item := &pb.BlackBoxCaseData{}
		if err = utils.Copy(item, data, l.Converters...); err != nil {
			return nil, errors.Wrapf(
				errorx.Err(errorx.CopyToStructFailure, err.Error()),
				"failed to copy black box case assistant[%+v] to response, error: %+v",
				data, err,
			)
		}
		resp.Items = append(resp.Items, item)
	}
	return resp, nil
}
