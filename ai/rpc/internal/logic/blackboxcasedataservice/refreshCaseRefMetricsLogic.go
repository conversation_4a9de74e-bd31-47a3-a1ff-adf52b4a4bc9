package blackboxcasedataservicelogic

import (
	"context"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/metrics"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
)

type RefreshCaseRefMetricsLogic struct {
	*logic.BaseLogic
}

func NewRefreshCaseRefMetricsLogic(ctx context.Context, svcCtx *svc.ServiceContext) *RefreshCaseRefMetricsLogic {
	return &RefreshCaseRefMetricsLogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),
	}
}

// RefreshCaseRefMetrics replace black box case
func (l *RefreshCaseRefMetricsLogic) RefreshCaseRefMetrics(in *pb.RefreshCaseRefMetricsReq) (out *pb.RefreshCaseRefMetricsResp, err error) {
	total, err := l.SvcCtx.BlackBoxCaseRefModel.FindCountByType(l.Ctx, in.CaseRefType)
	if err != nil {
		return nil, err
	}
	m := metrics.CaseRefTotalGauge
	m.Set(float64(total), []string{in.CaseRefType}...)
	return &pb.RefreshCaseRefMetricsResp{}, nil
}
