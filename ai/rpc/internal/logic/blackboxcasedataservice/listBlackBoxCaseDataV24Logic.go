package blackboxcasedataservicelogic

import (
	"context"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/model/dto"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/public/httpc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
)

type ListBlackBoxCaseDataV24Logic struct {
	*logic.BaseLogic
}

func NewListBlackBoxCaseDataV24Logic(ctx context.Context, svcCtx *svc.ServiceContext) *ListBlackBoxCaseDataV24Logic {
	return &ListBlackBoxCaseDataV24Logic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),
	}
}

// ListBlackBoxCaseDataV24 list black box case
func (l *ListBlackBoxCaseDataV24Logic) ListBlackBoxCaseDataV24(in *pb.ListBlackBoxCaseDataReq) (out *pb.ListBlackBoxCaseDataResp, err error) {
	dbDataList := make([]*model.BlackBoxCaseData, 0)
	if in.GetCaseId() != "" {
		dbDataList, err = l.SvcCtx.BlackBoxCaseDataModel.FindByCaseId(
			l.Ctx, in.GetCaseId(),
		)
	} else if in.GetCaseId() == "" && in.GetRevisionId() != "" {
		dbDataList, err = l.SvcCtx.BlackBoxCaseDataModel.FindByRevisionId(
			l.Ctx, in.GetRevisionId(),
		)
	} else {
		return nil, errors.New("case_id和revision_id不能同时传入")
	}

	if err != nil {
		return nil, err
	}

	out = &pb.ListBlackBoxCaseDataResp{}
	items := make([]*dto.BlackBoxCaseData, 0, len(dbDataList))
	for _, db := range dbDataList {
		item := &dto.BlackBoxCaseData{}
		if err = utils.Copy(item, db, l.Converters...); err != nil {
			return nil, errors.Wrapf(
				errorx.Err(errorx.CopyToStructFailure, err.Error()),
				"failed to copy db[%+v] to response, error: %+v", db, err,
			)
		}
		if item.CaseId != "" {
			item.IsKeep = true
		} else {
			item.IsKeep = false
		}
		items = append(items, item)
		out.Items = append(out.Items, &pb.BlackBoxCaseData{
			Id:           item.Id,
			CaseId:       item.CaseId,
			RevisionId:   item.RevisionId,
			CaseDataId:   item.CaseDataId,
			OrderId:      item.OrderId,
			Requirement:  item.Requirement,
			PreCondition: item.PreCondition,
			CaseStep:     item.CaseStep,
			ExpectResult: item.ExpectResult,
			Terminal:     item.Terminal,
			CaseLevel:    item.CaseLevel,
			Tag:          item.Tag,
			IsKeep:       item.IsKeep,
			CaseName:     item.CaseName,
			Deleted:      item.Deleted,
			CreatedBy:    item.CreatedBy,
			UpdatedBy:    item.UpdatedBy,
			DeletedBy:    item.DeletedBy,
			CreatedAt:    item.CreatedAt,
			UpdatedAt:    item.UpdatedAt,
			DeletedAt:    item.DeletedAt,
		})
	}

	// 如果是markdown格式,通过ai接口转换
	if in.Type == "markdown" {
		dataContent, err := httpc.Json2MDV24(
			l.SvcCtx.ExternalAiDomain, httpc.Json2MDV24Req{
				CaseTable: items,
			},
		)
		if err != nil {
			return nil, err
		}
		out.Content = *dataContent.Data
		out.Items = []*pb.BlackBoxCaseData{}
	}

	return out, nil
}
