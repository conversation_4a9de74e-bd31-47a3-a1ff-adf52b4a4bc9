package blackboxcasedataservicelogic

import (
	"context"
	"fmt"

	"github.com/zeromicro/go-zero/core/stores/sqlx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
)

type ClearKeepBlackBoxCaseDataLogic struct {
	*logic.BaseLogic
}

func NewClearKeepBlackBoxCaseDataLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ClearKeepBlackBoxCaseDataLogic {
	return &ClearKeepBlackBoxCaseDataLogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),
	}
}

// KeepBlackBoxCaseData keep black box case
func (l *ClearKeepBlackBoxCaseDataLogic) ClearKeepBlackBoxCaseData(in *pb.ClearKeepBlackBoxCaseDataReq) (out *pb.ClearKeepBlackBoxCaseDataResp, err error) {
	if l.CurrentUser == nil {
		return nil, nil
	}
	dbData, err := l.SvcCtx.BlackBoxCaseDataModel.FindByCaseId(
		l.Ctx, in.CaseId,
	)
	var ids []string
	for _, data := range dbData {
		ids = append(ids, fmt.Sprintf("%d", data.Id))
	}

	err = l.SvcCtx.BlackBoxCaseDataModel.Trans(
		l.Ctx, func(context context.Context, session sqlx.Session) error {
			err = l.SvcCtx.BlackBoxCaseDataModel.CancelKeepDataByIds(l.Ctx, session, ids)
			if err != nil {
				return err
			}
			return nil
		},
	)

	return &pb.ClearKeepBlackBoxCaseDataResp{}, nil
}
