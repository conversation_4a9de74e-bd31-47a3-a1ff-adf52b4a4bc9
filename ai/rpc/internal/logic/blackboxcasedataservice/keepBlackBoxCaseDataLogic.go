package blackboxcasedataservicelogic

import (
	"context"
	"database/sql"

	"github.com/zeromicro/go-zero/core/stores/sqlx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
)

type KeepBlackBoxCaseDataLogic struct {
	*logic.BaseLogic
}

func NewKeepBlackBoxCaseDataLogic(ctx context.Context, svcCtx *svc.ServiceContext) *KeepBlackBoxCaseDataLogic {
	return &KeepBlackBoxCaseDataLogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),
	}
}

// KeepBlackBoxCaseData keep black box case
func (l *KeepBlackBoxCaseDataLogic) KeepBlackBoxCaseData(in *pb.KeepBlackBoxCaseDataReq) (out *pb.KeepBlackBoxCaseDataResp, err error) {
	if l.CurrentUser == nil {
		return nil, nil
	}
	dbData, err := l.SvcCtx.BlackBoxCaseDataModel.FindOneByCaseDataId(
		l.Ctx, in.GetCaseDataId(),
	)
	if !l.CheckBlackBoxCaseDataDbData(dbData, err) {
		return nil, errorx.Err(errorx.NotExists, "数据不存在")
	}

	err = l.SvcCtx.BlackBoxCaseDataModel.Trans(
		l.Ctx, func(context context.Context, session sqlx.Session) error {
			if in.GetAction() == "keep" {
				revision, err := l.SvcCtx.BlackBoxCaseRevisionModel.FindOneByCaseDataId(l.Ctx, in.GetCaseDataId())
				if err != nil {
					return err
				}
				dbData.CaseId = sql.NullString{
					String: revision.CaseId,
					Valid:  revision.CaseId != "",
				}
			} else if in.GetAction() == "cancel" {
				dbData.CaseId = sql.NullString{
					String: "",
				}
			}
			dbData.UpdatedBy = l.CurrentUser.Account
			_, err = l.SvcCtx.BlackBoxCaseDataModel.Update(l.Ctx, session, dbData)
			if err != nil {
				return err
			}
			return nil
		},
	)

	return &pb.KeepBlackBoxCaseDataResp{}, nil
}
