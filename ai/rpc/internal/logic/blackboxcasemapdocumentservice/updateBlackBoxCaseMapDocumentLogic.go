package blackboxcasemapdocumentservicelogic

import (
	"context"
	"database/sql"

	"github.com/zeromicro/go-zero/core/stores/sqlx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
)

type UpdateBlackBoxCaseMapDocumentLogic struct {
	*logic.BaseLogic
}

func NewUpdateBlackBoxCaseMapDocumentLogic(
	ctx context.Context, svcCtx *svc.ServiceContext,
) *UpdateBlackBoxCaseMapDocumentLogic {
	return &UpdateBlackBoxCaseMapDocumentLogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),
	}
}

// UpdateBlackBoxCaseMapDocument create blackbox case map document
func (l *UpdateBlackBoxCaseMapDocumentLogic) UpdateBlackBoxCaseMapDocument(in *pb.BlackBoxCaseMapDocument) (
	out *pb.UpdateBlackBoxCaseMapDocumentResp, err error,
) {
	if l.CurrentUser == nil {
		return nil, nil
	}

	dbData, err := l.SvcCtx.BlackBoxCaseMapDocumentModel.FindOneByCaseIdFuncId(
		l.Ctx, in.GetCaseId(), in.GetFuncId(),
	)
	if !l.CheckBlackBoxCaseMapDocumentDbData(dbData, err) {
		if _, err = l.create(in); err != nil {
			return nil, err
		} else {
			return &pb.UpdateBlackBoxCaseMapDocumentResp{}, nil
		}
	}

	err = l.SvcCtx.BlackBoxCaseMapDocumentModel.Trans(
		l.Ctx, func(context context.Context, session sqlx.Session) error {
			dbData.UpdatedBy = l.CurrentUser.Account
			dbData.FuncName = in.GetFuncName()
			if in.GetFuncDoc() != "" {
				dbData.FuncDoc = sql.NullString{
					String: in.GetFuncDoc(),
					Valid:  true,
				}
			}
			if in.GetExpeDoc() != "" {
				dbData.ExpeDoc = sql.NullString{
					String: in.GetExpeDoc(),
					Valid:  true,
				}
			}

			_, err = l.SvcCtx.BlackBoxCaseMapDocumentModel.Update(l.Ctx, session, dbData)
			if err != nil {
				return err
			}
			return nil
		},
	)

	return &pb.UpdateBlackBoxCaseMapDocumentResp{}, nil
}

func (l *UpdateBlackBoxCaseMapDocumentLogic) create(in *pb.BlackBoxCaseMapDocument) (
	*model.BlackBoxCaseMapDocument, error,
) {
	if l.CurrentUser == nil {
		return nil, nil
	}

	model := &model.BlackBoxCaseMapDocument{
		ProjectId: in.GetProjectId(),
		DirId:     in.GetDirId(),
		CaseId:    in.GetCaseId(),
		FuncId:    in.GetFuncId(),
		FuncName:  in.GetFuncName(),
		CreatedBy: l.CurrentUser.Account,
	}

	if in.GetFuncDoc() != "" {
		model.FuncDoc = sql.NullString{
			String: in.GetFuncDoc(),
			Valid:  true,
		}
	} else {
		model.FuncDoc = sql.NullString{
			String: "",
			Valid:  false,
		}
	}

	if in.GetExpeDoc() != "" {
		model.ExpeDoc = sql.NullString{
			String: in.GetExpeDoc(),
			Valid:  true,
		}
	} else {
		model.ExpeDoc = sql.NullString{
			String: "",
			Valid:  false,
		}
	}

	_, err := l.SvcCtx.BlackBoxCaseMapDocumentModel.Insert(l.Ctx, nil, model)
	if err != nil {
		return nil, err
	}
	return model, nil
}
