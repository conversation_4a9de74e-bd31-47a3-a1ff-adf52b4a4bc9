package blackboxcasemapdocumentservicelogic

import (
	"context"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/stores/sqlc"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"
)

type GetBlackBoxCaseMapDocumentLogic struct {
	*logic.BaseLogic
}

func NewGetBlackBoxCaseMapDocumentLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetBlackBoxCaseMapDocumentLogic {
	return &GetBlackBoxCaseMapDocumentLogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),
	}
}

// GetBlackBoxCaseMapDocument get blackbox case map document
func (l *GetBlackBoxCaseMapDocumentLogic) GetBlackBoxCaseMapDocument(in *pb.GetBlackBoxCaseMapDocumentReq) (out *pb.BlackBoxCaseMapDocument, err error) {
	if l.CurrentUser == nil {
		return nil, nil
	}

	resp := &pb.BlackBoxCaseMapDocument{}
	dbData, err := l.SvcCtx.BlackBoxCaseMapDocumentModel.FindOneByCaseIdFuncId(
		l.Ctx, in.GetCaseId(), in.GetFuncId(),
	)
	if !l.CheckBlackBoxCaseMapDocumentDbData(dbData, err) {
		if errors.Is(err, sqlc.ErrNotFound) {
			return resp, nil
		}
		return nil, errorx.Err(errorx.NotExists, "数据不存在")
	}

	if err = utils.Copy(resp, dbData, l.Converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy blackbox case map[%+v] to response, error: %+v",
			dbData, err,
		)
	}
	return resp, nil
}
