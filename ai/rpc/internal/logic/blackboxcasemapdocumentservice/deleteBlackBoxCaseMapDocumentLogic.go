package blackboxcasemapdocumentservicelogic

import (
	"context"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/stores/sqlc"
	"github.com/zeromicro/go-zero/core/stores/sqlx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
)

type DeleteBlackBoxCaseMapDocumentLogic struct {
	*logic.BaseLogic
}

func NewDeleteBlackBoxCaseMapDocumentLogic(ctx context.Context, svcCtx *svc.ServiceContext) *DeleteBlackBoxCaseMapDocumentLogic {
	return &DeleteBlackBoxCaseMapDocumentLogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),
	}
}

// DeleteBlackBoxCaseMapDocument delete blackbox case map document
func (l *DeleteBlackBoxCaseMapDocumentLogic) DeleteBlackBoxCaseMapDocument(in *pb.DeleteBlackBoxCaseMapDocumentReq) (out *pb.DeleteBlackBoxCaseMapDocumentResp, err error) {
	var dbDataList []*model.BlackBoxCaseMapDocument

	if in.FuncId == "" {
		dbDataList, err = l.SvcCtx.BlackBoxCaseMapDocumentModel.FindByCaseId(
			l.Ctx, in.GetCaseId(),
		)
		if err != nil {
			return nil, err
		}
	} else {
		dbData, err := l.SvcCtx.BlackBoxCaseMapDocumentModel.FindOneByCaseIdFuncId(
			l.Ctx, in.GetCaseId(), in.GetFuncId(),
		)
		if !l.CheckBlackBoxCaseMapDocumentDbData(dbData, err) {
			if errors.Is(err, sqlc.ErrNotFound) {
				return &pb.DeleteBlackBoxCaseMapDocumentResp{}, nil
			}
			return nil, errorx.Err(errorx.NotExists, "数据不存在")
		}
		dbDataList = append(dbDataList, dbData)
	}

	for _, dbData := range dbDataList {
		err = l.SvcCtx.BlackBoxCaseMapDocumentModel.Trans(
			l.Ctx, func(context context.Context, session sqlx.Session) error {
				// TODO 删除关联表数据
				if err = l.SvcCtx.BlackBoxCaseMapDocumentModel.Delete(l.Ctx, session, dbData.Id); err != nil {
					return err
				}
				return nil
			},
		)
		if err != nil {
			return nil, err
		}
	}

	return &pb.DeleteBlackBoxCaseMapDocumentResp{}, nil
}
