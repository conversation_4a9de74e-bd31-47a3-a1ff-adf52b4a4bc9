package blackboxcasemapdocumentservicelogic

import (
	"context"
	"database/sql"

	"github.com/zeromicro/go-zero/core/stores/sqlx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
)

type UpdateBlackBoxCaseMapRecallDocumentLogic struct {
	*logic.BaseLogic
}

func NewUpdateBlackBoxCaseMapRecallDocumentLogic(
	ctx context.Context, svcCtx *svc.ServiceContext,
) *UpdateBlackBoxCaseMapRecallDocumentLogic {
	return &UpdateBlackBoxCaseMapRecallDocumentLogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),
	}
}

// UpdateBlackBoxCaseMapRecallDocument update blackbox case map document
func (l *UpdateBlackBoxCaseMapRecallDocumentLogic) UpdateBlackBoxCaseMapRecallDocument(in *pb.UpdateBlackBoxCaseMapRecallDocumentReq) (
	out *pb.UpdateBlackBoxCaseMapRecallDocumentResp, err error,
) {
	if l.CurrentUser == nil {
		return nil, nil
	}

	dbData, err := l.SvcCtx.BlackBoxCaseMapModel.FindOneByCaseId(
		l.Ctx, in.GetCaseId(),
	)
	if !l.CheckBlackBoxCaseMapDbData(dbData, err) {
		return nil, errorx.Err(errorx.NotExists, "数据不存在")
	}

	err = l.SvcCtx.BlackBoxCaseDirModel.Trans(
		l.Ctx, func(context context.Context, session sqlx.Session) error {
			dbData.UpdatedBy = l.CurrentUser.Account
			dbData.RecallDocument = sql.NullString{
				String: in.GetRecallDocument(),
				Valid:  in.GetRecallDocument() != "",
			}

			_, err = l.SvcCtx.BlackBoxCaseMapModel.Update(l.Ctx, session, dbData)
			if err != nil {
				return err
			}
			return nil
		},
	)

	return &pb.UpdateBlackBoxCaseMapRecallDocumentResp{}, nil
}
