package blackboxcasemapdocumentservicelogic

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"
)

type ListBlackBoxCaseMapDocumentsLogic struct {
	*logic.BaseLogic
}

func NewListBlackBoxCaseMapDocumentsLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ListBlackBoxCaseMapDocumentsLogic {
	return &ListBlackBoxCaseMapDocumentsLogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),
	}
}

// ListBlackBoxCaseMapDocuments list blackbox case map documents
func (l *ListBlackBoxCaseMapDocumentsLogic) ListBlackBoxCaseMapDocuments(in *pb.ListBlackBoxCaseMapDocumentsReq) (out *pb.ListBlackBoxCaseMapDocumentsResp, err error) {
	dbDataList, err := l.SvcCtx.BlackBoxCaseMapDocumentModel.FindByCaseId(
		l.Ctx, in.GetCaseId(),
	)
	if err != nil {
		return nil, err
	}

	out = &pb.ListBlackBoxCaseMapDocumentsResp{Items: make([]*pb.BlackBoxCaseMapDocument, 0, len(dbDataList))}
	for _, db := range dbDataList {
		item := &pb.BlackBoxCaseMapDocument{}
		if err = utils.Copy(item, db, l.Converters...); err != nil {
			return nil, errors.Wrapf(
				errorx.Err(errorx.CopyToStructFailure, err.Error()),
				"failed to copy db[%+v] to response, error: %+v", db, err,
			)
		}
		out.Items = append(out.Items, item)
	}
	return out, nil
}
