package blackboxcaserevisionservicelogic

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"github.com/zeromicro/go-zero/core/logx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
)

type GetBlackBoxCaseRevisionByRevisionIdLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewGetBlackBoxCaseRevisionByRevisionIdLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetBlackBoxCaseRevisionByRevisionIdLogic {
	return &GetBlackBoxCaseRevisionByRevisionIdLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// GetBlackBoxCaseRevisionByRevisionId get black box case revision by revisionId
func (l *GetBlackBoxCaseRevisionByRevisionIdLogic) GetBlackBoxCaseRevisionByRevisionId(in *pb.GetBlackBoxCaseRevisionByRevisionIdReq) (out *pb.GetBlackBoxCaseRevisionByRevisionIdResp, err error) {
	dbData, err := l.svcCtx.BlackBoxCaseRevisionModel.FindOneByRevisionId(
		l.ctx, in.GetRevisionId(),
	)
	if err != nil {
		return nil, err
	}

	item := &pb.BlackBoxCaseRevision{}
	if err = utils.Copy(item, dbData); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy db[%+v] to response, error: %+v", dbData, err,
		)
	}

	return &pb.GetBlackBoxCaseRevisionByRevisionIdResp{
		RevisionData: item,
	}, nil
}
