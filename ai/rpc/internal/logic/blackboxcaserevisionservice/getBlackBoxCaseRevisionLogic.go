package blackboxcaserevisionservicelogic

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"
)

type GetBlackBoxCaseRevisionLogic struct {
	*logic.BaseLogic
}

func NewGetBlackBoxCaseRevisionLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetBlackBoxCaseRevisionLogic {
	return &GetBlackBoxCaseRevisionLogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),
	}
}

// GetBlackBoxCaseRevision gets a black box case
func (l *GetBlackBoxCaseRevisionLogic) GetBlackBoxCaseRevision(in *pb.GetBlackBoxCaseRevisionReq) (out *pb.GetBlackBoxCaseRevisionResp, err error) {
	resp := &pb.GetBlackBoxCaseRevisionResp{Item: &pb.BlackBoxCaseRevision{}}
	// dbData, err := l.SvcCtx.BlackBoxCaseRevisionModel.FindOneByCaseIdRevisionId(
	// 	l.Ctx, "ai:case_id:9AemyjmG2AvhFvzJaSenA", in.GetRevisionId(),
	// )
	dbData, err := l.SvcCtx.BlackBoxCaseRevisionModel.FindOneByRevisionId(
		l.Ctx, in.GetRevisionId(),
	)
	if !l.CheckBlackBoxCaseRevisionDbData(dbData, err) {
		return nil, errorx.Err(errorx.NotExists, "数据不存在")
	}

	if err = utils.Copy(resp.Item, dbData, l.Converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy black box case [%+v] to response, error: %+v",
			dbData, err,
		)
	}
	return resp, nil
}
