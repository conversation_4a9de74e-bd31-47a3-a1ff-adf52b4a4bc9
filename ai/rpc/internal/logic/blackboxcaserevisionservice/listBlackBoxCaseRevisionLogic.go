package blackboxcaserevisionservicelogic

import (
	"context"
	"encoding/json"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"
)

type ListBlackBoxCaseRevisionLogic struct {
	*logic.BaseLogic
}

func NewListBlackBoxCaseRevisionLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ListBlackBoxCaseRevisionLogic {
	return &ListBlackBoxCaseRevisionLogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),
	}
}

// ListBlackBoxCaseRevision list black box case
func (l *ListBlackBoxCaseRevisionLogic) ListBlackBoxCaseRevision(in *pb.ListBlackBoxCaseRevisionReq) (out *pb.ListBlackBoxCaseRevisionResp, err error) {
	dbDataList, err := l.SvcCtx.BlackBoxCaseRevisionModel.FindByCaseId(
		l.Ctx, in.GetCaseId(),
	)
	if err != nil {
		return nil, err
	}

	out = &pb.ListBlackBoxCaseRevisionResp{Items: make([]*pb.BlackBoxCaseRevision, 0, len(dbDataList))}
	for _, db := range dbDataList {
		item := &pb.BlackBoxCaseRevision{}
		if err = utils.Copy(item, db, l.Converters...); err != nil {
			return nil, errors.Wrapf(
				errorx.Err(errorx.CopyToStructFailure, err.Error()),
				"failed to copy db[%+v] to response, error: %+v", db, err,
			)
		}
		titles := make([]string, 0, 8)
		err = json.Unmarshal([]byte(db.KnowledgeParagraphTitle), &titles)
		if err != nil {
			l.Logger.Warnf("[ListBlackBoxCaseRevisionLogic.ListBlackBoxCaseRevision] failed to unmarshal knowledgeParagraphTitle, error: %+v", err)
		}
		item.KnowledgeParagraphTitle = titles
		out.Items = append(out.Items, item)
	}
	return out, nil
}
