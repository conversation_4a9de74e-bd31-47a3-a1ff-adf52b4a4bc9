package blackboxcaserevisionservicelogic

import (
	"context"
	"encoding/json"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
)

type CreateBlackBoxCaseRevisionLogic struct {
	*logic.BaseLogic
}

func NewCreateBlackBoxCaseRevisionLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CreateBlackBoxCaseRevisionLogic {
	return &CreateBlackBoxCaseRevisionLogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),
	}
}

// CreateBlackBoxCaseRevision create a black box case
func (l *CreateBlackBoxCaseRevisionLogic) CreateBlackBoxCaseRevision(in *pb.CreateBlackBoxCaseRevisionReq) (out *pb.CreateBlackBoxCaseRevisionResp, err error) {
	if l.CurrentUser == nil {
		return nil, nil
	}

	data, err := l.create(in)
	if err != nil {
		return nil, err
	}

	return &pb.CreateBlackBoxCaseRevisionResp{RevisionId: data.RevisionId}, nil
}

func (l *CreateBlackBoxCaseRevisionLogic) create(req *pb.CreateBlackBoxCaseRevisionReq) (*model.BlackBoxCaseRevision, error) {
	if l.CurrentUser == nil {
		return nil, nil
	}

	var revisionId string
	var err error
	if req.RevisionId != "" {
		revisionId = req.RevisionId
	} else {
		revisionId, err = l.GenerateAiRevisionId()
		if err != nil {
			return nil, err
		}
	}

	marshal, _ := json.Marshal(req.KnowledgeParagraphTitle)

	revision := &model.BlackBoxCaseRevision{
		CaseId:                      req.GetCaseId(),
		RevisionId:                  revisionId,
		RevisionName:                req.RevisionName,
		CreatedBy:                   l.CurrentUser.Account,
		KnowledgeParagraphTitleText: req.KnowledgeParagraphTitleText,
		KnowledgeId:                 req.KnowledgeId,
		CaseRefId:                   req.CaseRefId,
		KnowledgeParagraphTitle:     string(marshal),
	}

	_, err = l.SvcCtx.BlackBoxCaseRevisionModel.Insert(l.Ctx, nil, revision)
	if err != nil {
		return nil, err
	}
	return revision, nil
}
