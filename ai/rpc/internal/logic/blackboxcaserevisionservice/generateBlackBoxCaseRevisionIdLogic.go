package blackboxcaserevisionservicelogic

import (
	"context"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
)

type GenerateBlackBoxCaseRevisionIdLogic struct {
	*logic.BaseLogic
}

func NewGenerateBlackBoxCaseRevisionIdLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GenerateBlackBoxCaseRevisionIdLogic {
	return &GenerateBlackBoxCaseRevisionIdLogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),
	}
}

// GenerateBlackBoxCaseRevisionId list black box case
func (l *GenerateBlackBoxCaseRevisionIdLogic) GenerateBlackBoxCaseRevisionId(in *pb.GenerateBlackBoxCaseRevisionIdReq) (out *pb.GenerateBlackBoxCaseRevisionIdResp, err error) {
	revisionId, err := l.GenerateAiRevisionId()
	return &pb.GenerateBlackBoxCaseRevisionIdResp{RevisionId: revisionId}, err
}
