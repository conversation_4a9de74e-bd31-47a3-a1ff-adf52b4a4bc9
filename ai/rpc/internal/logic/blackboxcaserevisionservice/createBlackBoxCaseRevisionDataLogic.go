package blackboxcaserevisionservicelogic

import (
	"context"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/public/httpc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/logic"
	blackboxcasedataservicelogic "gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/logic/blackboxcasedataservice"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
)

type CreateBlackBoxCaseRevisionDataLogic struct {
	*logic.BaseLogic
}

func NewCreateBlackBoxCaseRevisionDataLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CreateBlackBoxCaseRevisionDataLogic {
	return &CreateBlackBoxCaseRevisionDataLogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),
	}
}

// CreateBlackBoxCaseRevisionData create a black box case data
func (l *CreateBlackBoxCaseRevisionDataLogic) CreateBlackBoxCaseRevisionData(in *pb.CreateBlackBoxCaseRevisionDataReq) (out *pb.CreateBlackBoxCaseRevisionDataResp, err error) {
	if in.KnowledgeId == "" {
		caseDetail, err := l.SvcCtx.BlackBoxCaseModel.FindOneByCaseId(
			l.Ctx, in.GetCaseId(),
		)
		if err != nil {
			return out, nil
		}
		in.KnowledgeId = caseDetail.KnowledgeId
	}
	createLogic := NewCreateBlackBoxCaseRevisionLogic(l.Ctx, l.SvcCtx)
	revisionOut, err := createLogic.CreateBlackBoxCaseRevision(&pb.CreateBlackBoxCaseRevisionReq{
		CaseId:                      in.CaseId,
		RevisionName:                in.RevisionName,
		KnowledgeId:                 in.KnowledgeId,
		KnowledgeParagraphTitleText: in.KnowledgeParagraphTitleText,
		KnowledgeParagraphTitle:     in.GetKnowledgeParagraphTitle(),
	})
	if err != nil {
		return out, nil
	}

	createDataLogic := blackboxcasedataservicelogic.NewCreateBlackBoxCaseDataLogic(l.Ctx, l.SvcCtx)
	// 若items有值,根据入参创建新的数据,否则使用当前所有保留数据来创建
	if len(in.Items) == 0 {
		listDataLogic := blackboxcasedataservicelogic.NewListBlackBoxCaseDataLogic(l.Ctx, l.SvcCtx)
		dataList, err := listDataLogic.ListBlackBoxCaseData(&pb.ListBlackBoxCaseDataReq{
			CaseId: in.CaseId,
		})
		if err != nil {
			return out, nil
		}
		// 调用ai接口排序
		newItems, err := httpc.ReorderCaseData(l.SvcCtx.ExternalAiDomain, httpc.ReorderCaseDataReq{
			KnowledgeId: in.KnowledgeId,
			CaseTable:   dataList.Items,
		})
		if err != nil {
			return nil, err
		}

		in.Items = *newItems.Data
	}

	for i := range in.Items {
		in.Items[i].RevisionId = revisionOut.RevisionId
		// 新建数据不保留
		in.Items[i].CaseId = ""
	}
	createDataLogic.CreateBlackBoxCaseData(&pb.CreateBlackBoxCaseDataReq{
		Items: in.Items,
	})

	return &pb.CreateBlackBoxCaseRevisionDataResp{RevisionId: revisionOut.RevisionId}, nil
}
