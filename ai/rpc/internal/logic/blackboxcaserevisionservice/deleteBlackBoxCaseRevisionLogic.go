package blackboxcaserevisionservicelogic

import (
	"context"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
)

type DeleteBlackBoxCaseRevisionLogic struct {
	*logic.BaseLogic
}

func NewDeleteBlackBoxCaseRevisionLogic(ctx context.Context, svcCtx *svc.ServiceContext) *DeleteBlackBoxCaseRevisionLogic {
	return &DeleteBlackBoxCaseRevisionLogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),
	}
}

// DeleteBlackBoxCaseRevision create a black box case
func (l *DeleteBlackBoxCaseRevisionLogic) DeleteBlackBoxCaseRevision(in *pb.DeleteBlackBoxCaseRevisionReq) (out *pb.DeleteBlackBoxCaseRevisionResp, err error) {
	// todo: add your logic here and delete this line

	return &pb.DeleteBlackBoxCaseRevisionResp{}, nil
}
