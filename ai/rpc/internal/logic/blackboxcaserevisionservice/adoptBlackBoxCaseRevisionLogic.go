package blackboxcaserevisionservicelogic

import (
	"context"
	"strings"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/metrics"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
)

type AdoptBlackBoxCaseRevisionLogic struct {
	*logic.BaseLogic
}

func NewAdoptBlackBoxCaseRevisionLogic(ctx context.Context, svcCtx *svc.ServiceContext) *AdoptBlackBoxCaseRevisionLogic {
	return &AdoptBlackBoxCaseRevisionLogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),
	}
}

// AdoptBlackBoxCaseRevision adopt a black box case
func (l *AdoptBlackBoxCaseRevisionLogic) AdoptBlackBoxCaseRevision(in *pb.AdoptBlackBoxCaseRevisionReq) (out *pb.AdoptBlackBoxCaseRevisionResp, err error) {
	err = l.SvcCtx.BlackBoxCaseDataModel.AdoptedByRevisionId(l.Ctx, nil, in.RevisionId)
	if err != nil {
		return nil, err
	}

	// 刷新metrics
	adoptedTotal, err := l.SvcCtx.BlackBoxCaseDataModel.FindCountByAdopted(l.Ctx)
	if err != nil {
		l.BaseLogic.Logger.Error(err)
	}
	adoptedUpdatedTotal, err := l.SvcCtx.BlackBoxCaseDataModel.FindCountByUpdatedAdopted(l.Ctx)
	if err != nil {
		l.BaseLogic.Logger.Error(err)
	}
	// 被修改过的用例数据
	metrics.CaseDataAdoptTotalGauge.Set(float64(adoptedUpdatedTotal), []string{"true"}...)
	// 未被修改过的,采纳的数据
	metrics.CaseDataAdoptTotalGauge.Set(float64(adoptedTotal-adoptedUpdatedTotal), []string{"false"}...)

	// 采纳用例统计
	revisionIds, err := l.SvcCtx.Redis.Get("ai_adopted_revision_ids")
	if err != nil {
		l.BaseLogic.Logger.Error(err)
	}
	// 从缓存中获取采纳的版本id列表,原始数据在rpc/metrics.go中设置
	if !strings.Contains(revisionIds, in.RevisionId) {
		if revisionIds == "" {
			revisionIds = in.RevisionId
		} else {
			revisionIds += "," + in.RevisionId
		}
		err = l.SvcCtx.Redis.Set("ai_adopted_revision_ids", revisionIds)
		if err != nil {
			l.BaseLogic.Logger.Error(err)
		}
		metrics.CaseAdoptTotalGauge.Inc([]string{""}...)
	}

	return &pb.AdoptBlackBoxCaseRevisionResp{}, nil
}
