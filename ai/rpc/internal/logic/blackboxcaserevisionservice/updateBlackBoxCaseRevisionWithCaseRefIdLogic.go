package blackboxcaserevisionservicelogic

import (
	"context"
	"github.com/zeromicro/go-zero/core/jsonx"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/stores/sqlx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
)

type UpdateBlackBoxCaseRevisionWithCaseRefIdLogic struct {
	*logic.BaseLogic
}

func NewUpdateBlackBoxCaseRevisionWithCaseRefIdLogic(ctx context.Context, svcCtx *svc.ServiceContext) *UpdateBlackBoxCaseRevisionWithCaseRefIdLogic {
	return &UpdateBlackBoxCaseRevisionWithCaseRefIdLogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),
	}
}

// UpdateBlackBoxCaseRevisionWithCaseRefId update a black box case with case ref id
func (l *UpdateBlackBoxCaseRevisionWithCaseRefIdLogic) UpdateBlackBoxCaseRevisionWithCaseRefId(in *pb.UpdateBlackBoxCaseRevisionWithCaseRefIdReq) (out *pb.UpdateBlackBoxCaseRevisionWithCaseRefIdResp, err error) {
	if l.CurrentUser == nil {
		return nil, nil
	}
	dbData, err := l.SvcCtx.BlackBoxCaseRevisionModel.FindOneByCaseRefId(
		l.Ctx, in.GetBlackBoxCaseRevision().GetCaseRefId(),
	)
	if !l.CheckBlackBoxCaseRevisionDbData(dbData, err) {
		return nil, errorx.Err(errorx.NotExists, "数据不存在")
	}

	err = l.SvcCtx.BlackBoxCaseRevisionModel.Trans(
		l.Ctx, func(context context.Context, session sqlx.Session) error {
			item := &model.BlackBoxCaseRevision{}
			if err = utils.Copy(item, in.GetBlackBoxCaseRevision(), l.Converters...); err != nil {
				return errors.Wrapf(
					errorx.Err(errorx.CopyToStructFailure, err.Error()),
					"failed to copy db[%+v] to response, error: %+v", in, err,
				)
			}
			item.Id = dbData.Id
			if item.RevisionName != "" {
				dbData.RevisionName = item.RevisionId
			}
			if item.KnowledgeParagraphTitleText != "" {
				dbData.KnowledgeParagraphTitleText = item.KnowledgeParagraphTitleText
			}

			if item.KnowledgeId != "" {
				dbData.KnowledgeId = item.KnowledgeId
			}

			if item.CaseRefId != "" {
				dbData.CaseRefId = item.CaseRefId
			}

			if item.Coverage != 0 {
				dbData.Coverage = item.Coverage
			}

			if item.AddCount != 0 {
				dbData.AddCount = item.AddCount
			}

			if item.UpdateCount != 0 {
				dbData.UpdateCount = item.UpdateCount
			}

			_, err = l.SvcCtx.BlackBoxCaseRevisionModel.Update(l.Ctx, session, dbData)
			if err != nil {
				return err
			}
			return nil
		},
	)
	if err != nil {
		return nil, err
	}

	return &pb.UpdateBlackBoxCaseRevisionWithCaseRefIdResp{}, nil
}

func (l *UpdateBlackBoxCaseRevisionWithCaseRefIdLogic) ConvertKnowledgePgTitle() (err error) {
	if l.CurrentUser == nil {
		return nil
	}

	cases, err := l.SvcCtx.BlackBoxCaseRevisionModel.FindNoCacheByQuery(l.Ctx, l.SvcCtx.BlackBoxCaseRevisionModel.SelectBuilder())
	if err != nil {
		return err
	}

	updated := make([]*model.BlackBoxCaseRevision, 0)

	for _, blackboxCase := range cases {
		if blackboxCase.KnowledgeParagraphTitleText != "" {
			knowledgeDocPgTitle := make([]string, 0)
			knowledgeDocPgTitle = append(knowledgeDocPgTitle, blackboxCase.KnowledgeParagraphTitleText)
			blackboxCase.KnowledgeParagraphTitle = string(jsonx.MarshalIgnoreError(knowledgeDocPgTitle))
			updated = append(updated, blackboxCase)
		}
	}

	for _, boxCase := range updated {
		if _, err = l.SvcCtx.BlackBoxCaseRevisionModel.UpdateTX(l.Ctx, nil, boxCase); err != nil {
			l.Logger.Warnf(
				"failed to update the black box case,id: %d, error: %+v", boxCase.Id, err,
			)
			continue
		}
	}

	return nil
}
