package blackboxcaseassistantservicelogic

import (
	"context"
	"database/sql"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
)

type CreateBlackBoxCaseAssistantLogic struct {
	*logic.BaseLogic
}

func NewCreateBlackBoxCaseAssistantLogic(
	ctx context.Context, svcCtx *svc.ServiceContext,
) *CreateBlackBoxCaseAssistantLogic {
	return &CreateBlackBoxCaseAssistantLogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),
	}
}

// CreateBlackBoxCaseAssistant creates a black box case assistant
func (l *CreateBlackBoxCaseAssistantLogic) CreateBlackBoxCaseAssistant(in *pb.CreateBlackBoxCaseAssistantReq) (
	out *pb.CreateBlackBoxCaseAssistantResp, err error,
) {
	data, err := l.create(in)
	if err != nil {
		return nil, err
	}
	return &pb.CreateBlackBoxCaseAssistantResp{
		ProjectId:   data.ProjectId,
		AssistantId: data.AssistantId,
	}, nil
}

func (l *CreateBlackBoxCaseAssistantLogic) create(req *pb.CreateBlackBoxCaseAssistantReq) (
	*model.BlackBoxCaseAssistant, error,
) {
	if l.CurrentUser == nil {
		return nil, nil
	}
	assistantId, err := l.GenerateAiAssistantId(req.GetProjectId())
	if err != nil {
		return nil, err
	}
	assistant := &model.BlackBoxCaseAssistant{
		ProjectId:     req.GetProjectId(),
		AssistantId:   assistantId,
		AssistantName: req.GetAssistantName(),
		AssistantDescription: sql.NullString{
			String: req.GetAssistantDescription(),
			Valid:  req.GetAssistantDescription() != "",
		},

		WorkSpaceType:    int64(req.GetWorkSpaceType()),
		WorkPriorityType: int64(req.GetWorkPriorityType()),
		SessionRound:     int64(req.GetSessionRound()),
		CreatedBy:        l.CurrentUser.Account,
	}
	if pb.WorkSpaceType_WORK_SPACE_TYPE_PERSONAL == pb.WorkSpaceType(req.GetWorkSpaceType()) {
		assistant.Account = sql.NullString{
			String: l.CurrentUser.Account,
			Valid:  l.CurrentUser.Account != "",
		}
	}
	_, err = l.SvcCtx.BlackBoxCaseAssistantModel.Insert(l.Ctx, nil, assistant)
	if err != nil {
		return nil, err
	}
	return assistant, nil
}
