package blackboxcaseassistantservicelogic

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
)

type GetBlackBoxCaseAssistantLogic struct {
	*logic.BaseLogic
}

func NewGetBlackBoxCaseAssistantLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetBlackBoxCaseAssistantLogic {
	return &GetBlackBoxCaseAssistantLogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),
	}
}

// GetBlackBoxCaseAssistant gets a black box case assistant
func (l *GetBlackBoxCaseAssistantLogic) GetBlackBoxCaseAssistant(in *pb.GetBlackBoxCaseAssistantReq) (
	resp *pb.GetBlackBoxCaseAssistantResp, err error,
) {
	resp = &pb.GetBlackBoxCaseAssistantResp{Item: &pb.BlackBoxCaseAssistant{}}
	dbData, err := l.SvcCtx.BlackBoxCaseAssistantModel.FindOneByProjectIdAssistantId(
		l.Ctx, in.GetProjectId(), in.GetAssistantId(),
	)
	if !l.CheckBlackBoxCaseAssistantDbData(dbData, err) {
		return nil, nil
	}
	if err = utils.Copy(resp.Item, dbData, l.Converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy black box case assistant[%+v] to response, error: %+v",
			dbData, err,
		)
	}
	return resp, nil
}
