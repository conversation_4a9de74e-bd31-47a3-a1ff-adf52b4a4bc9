package blackboxcaseassistantservicelogic

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
)

type SearchBlackBoxCaseAssistantLogic struct {
	*logic.BaseLogic
}

func NewSearchBlackBoxCaseAssistantLogic(
	ctx context.Context, svcCtx *svc.ServiceContext,
) *SearchBlackBoxCaseAssistantLogic {
	return &SearchBlackBoxCaseAssistantLogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),
	}
}

// SearchBlackBoxCaseAssistant searches for black box case assistants
func (l *SearchBlackBoxCaseAssistantLogic) SearchBlackBoxCaseAssistant(in *pb.SearchBlackBoxCaseAssistantReq) (
	resp *pb.SearchBlackBoxCaseAssistantResp, err error,
) {
	if l.CurrentUser == nil {
		return nil, nil
	}
	dbDataList, err := l.SvcCtx.BlackBoxCaseAssistantModel.FindByProjectId(
		l.Ctx, in.GetProjectId(),
	)
	if err != nil {
		return nil, err
	}
	resp = &pb.SearchBlackBoxCaseAssistantResp{Items: make([]*pb.BlackBoxCaseAssistant, 0, len(dbDataList))}
	for _, data := range dbDataList {
		item := &pb.BlackBoxCaseAssistant{}
		if pb.WorkSpaceType_WORK_SPACE_TYPE_PERSONAL == pb.WorkSpaceType(data.WorkSpaceType) && l.CurrentUser.Account != data.Account.String {
			continue
		}
		if err = utils.Copy(item, data, l.Converters...); err != nil {
			return nil, errors.Wrapf(
				errorx.Err(errorx.CopyToStructFailure, err.Error()),
				"failed to copy black box case assistant[%+v] to response, error: %+v",
				data, err,
			)
		}

		resp.Items = append(resp.Items, item)
	}
	return resp, nil
}
