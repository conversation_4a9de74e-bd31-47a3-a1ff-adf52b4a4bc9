package blackboxcaseassistantservicelogic

import (
	"context"
	"database/sql"

	"github.com/zeromicro/go-zero/core/stores/sqlx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
)

type UpdateBlackBoxCaseAssistantLogic struct {
	*logic.BaseLogic
}

func NewUpdateBlackBoxCaseAssistantLogic(
	ctx context.Context, SvcCtx *svc.ServiceContext,
) *UpdateBlackBoxCaseAssistantLogic {
	return &UpdateBlackBoxCaseAssistantLogic{
		BaseLogic: logic.NewBaseLogic(ctx, SvcCtx),
	}
}

// UpdateBlackBoxCaseAssistant updates a black box case assistant
func (l *UpdateBlackBoxCaseAssistantLogic) UpdateBlackBoxCaseAssistant(in *pb.UpdateBlackBoxCaseAssistantReq) (
	out *pb.UpdateBlackBoxCaseAssistantResp, err error,
) {
	if l.CurrentUser == nil {
		return nil, nil
	}
	dbData, err := l.SvcCtx.BlackBoxCaseAssistantModel.FindOneByProjectIdAssistantId(
		l.Ctx, in.GetProjectId(), in.GetAssistantId(),
	)
	if !l.CheckBlackBoxCaseAssistantDbData(dbData, err) {
		return nil, errorx.Err(errorx.NotExists, "数据不存在")
	}
	dbData.AssistantName = in.GetAssistantName()
	dbData.AssistantDescription = sql.NullString{
		String: in.GetAssistantDescription(),
		Valid:  in.GetAssistantDescription() != "",
	}
	dbData.SessionRound = int64(in.GetSessionRound())
	dbData.WorkPriorityType = int64(in.GetWorkPriorityType())
	dbData.UpdatedBy = l.CurrentUser.Account
	// 暂时不可以更换公共或个人
	/*dbData.WorkSpaceType = int64(in.GetWorkSpaceType())
	if pb.WorkSpaceType_WORK_SPACE_TYPE_PUBLIC == in.GetWorkSpaceType() {
		dbData.Account = sql.NullString{
			String: "",
			Valid:  true,
		}
	} else {
		dbData.Account = sql.NullString{
			String: l.CurrentUser.Account,
			Valid:  true,
		}
	}*/
	// 更新会话全部轮数
	return &pb.UpdateBlackBoxCaseAssistantResp{}, l.SvcCtx.BlackBoxCaseAssistantModel.Trans(
		l.Ctx, func(context context.Context, session sqlx.Session) error {
			_, err = l.SvcCtx.BlackBoxCaseAssistantModel.Update(context, session, dbData)
			if err != nil {
				return err
			}
			if dbData.WorkPriorityType != int64(in.GetWorkPriorityType()) {
				err = l.SvcCtx.BlackBoxCaseSessionModel.UpdateSessionRoundByProjectIdAssistantId(
					context, session, in.GetProjectId(), in.GetAssistantId(), int64(in.GetSessionRound()),
				)
				if err != nil {
					return err
				}
			}
			return nil
		},
	)
}
