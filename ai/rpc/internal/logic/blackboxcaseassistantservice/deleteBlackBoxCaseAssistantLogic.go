package blackboxcaseassistantservicelogic

import (
	"context"

	"github.com/zeromicro/go-zero/core/stores/sqlx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/public/httpc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
)

type DeleteBlackBoxCaseAssistantLogic struct {
	*logic.BaseLogic
}

func NewDeleteBlackBoxCaseAssistantLogic(
	ctx context.Context, SvcCtx *svc.ServiceContext,
) *DeleteBlackBoxCaseAssistantLogic {
	return &DeleteBlackBoxCaseAssistantLogic{
		BaseLogic: logic.NewBaseLogic(ctx, SvcCtx),
	}
}

// DeleteBlackBoxCaseAssistant deletes a black box case assistant
func (l *DeleteBlackBoxCaseAssistantLogic) DeleteBlackBoxCaseAssistant(in *pb.DeleteBlackBoxCaseAssistantReq) (
	out *pb.DeleteBlackBoxCaseAssistantResp, err error,
) {
	dbData, err := l.SvcCtx.BlackBoxCaseAssistantModel.FindOneByProjectIdAssistantId(
		l.Ctx, in.GetProjectId(), in.GetAssistantId(),
	)
	if !l.CheckBlackBoxCaseAssistantDbData(dbData, err) {
		return nil, errorx.Err(errorx.NotExists, "数据不存在")
	}
	err = l.SvcCtx.BlackBoxCaseAssistantModel.Trans(
		l.Ctx, func(context context.Context, session sqlx.Session) error {
			// 删除会话
			queryList, err := l.SvcCtx.BlackBoxCaseSessionModel.FindByProjectIdAssistantIdAccount(
				l.Ctx, in.GetProjectId(), in.GetAssistantId(), l.CurrentUser.Account,
			)
			if err != nil {
				return err
			}
			if len(queryList) > 0 {
				var sessionIds []string
				for _, item := range queryList {
					sessionIds = append(sessionIds, item.SessionId)
				}
				err := httpc.BatchDeleteSession(l.SvcCtx.ExternalAiDomain, sessionIds)
				if err != nil {
					return err
				}
			}
			// 删除关联文档
			if dbData.DocumentIds.Valid {
				docIdListStr := dbData.DocumentIds.String
				docIdList := l.GetDocIdsForStr(docIdListStr)
				err := httpc.BatchDeleteDocument(l.SvcCtx.ExternalAiDomain, docIdList)
				if err != nil {
					return err
				}
				err = l.DeleteDocByDocIdList(
					in.GetProjectId(), in.GetAssistantId(), l.CurrentUser.Account, docIdList...,
				)
				if err != nil {
					return err
				}
			}
			if err = l.SvcCtx.BlackBoxCaseAssistantModel.Delete(l.Ctx, session, dbData.Id); err != nil {
				return err
			}

			if err := l.SvcCtx.BlackBoxCaseAssistantDocumentSessionRelationshipModel.DeleteByProjectIdAssistantId(
				l.Ctx, session, in.GetProjectId(), in.GetAssistantId(),
			); err != nil {
				return err
			}

			if err := l.SvcCtx.BlackBoxCaseSessionModel.DeleteByProjectIdAssistantId(
				l.Ctx, session, in.GetProjectId(), in.GetAssistantId(),
			); err != nil {
				return err
			}
			l.Debugf(
				"deleteBlackBoxCaseAssistantLogic没有存在的错误, projectId[%s], assistantId[%s]",
				in.GetProjectId(), in.GetAssistantId(),
			)
			return nil
		},
	)
	return &pb.DeleteBlackBoxCaseAssistantResp{}, err
}

func (l *DeleteBlackBoxCaseAssistantLogic) checkDeleteBlackBoxCaseAssistant(projectId, assistantId, sessionId, documentId string) (
	b bool, err error,
) {
	testData := &logic.Data{
		ProjectId:   projectId,
		AssistantId: assistantId,
		SessionId:   sessionId,
		DocumentId:  documentId,
		IsValid:     false,
	}
	assistantCheckChain := logic.NewAssistantCheckChain(l.BaseLogic)
	assistantCheckChain.SetNext(nil)
	return testData.IsValid, assistantCheckChain.Handle(testData)
}
