package blackboxgenerationrecordservicelogic

import (
	"context"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/model"

	"github.com/zeromicro/go-zero/core/logx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
)

type AddBlackBoxCaseNameUpdatedCountLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewAddBlackBoxCaseNameUpdatedCountLogic(ctx context.Context, svcCtx *svc.ServiceContext) *AddBlackBoxCaseNameUpdatedCountLogic {
	return &AddBlackBoxCaseNameUpdatedCountLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// AddBlackBoxCaseNameUpdatedCount add blackbox case name updated count
func (l *AddBlackBoxCaseNameUpdatedCountLogic) AddBlackBoxCaseNameUpdatedCount(in *pb.AddBlackBoxCaseNameUpdatedCountReq) (out *pb.AddBlackBoxCaseNameUpdatedCountResp, err error) {
	err = l.svcCtx.BlackBoxGenerationRecordModel.Save(l.ctx, &model.BlackBoxGenerationRecord{
		CaseNameUpdateCount: 1,
	})
	if err != nil {
		return nil, err
	}

	return &pb.AddBlackBoxCaseNameUpdatedCountResp{}, nil
}
