package blackboxgenerationrecordservicelogic

import (
	"context"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/model"

	"github.com/zeromicro/go-zero/core/logx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
)

type AddBlackBoxPreConditionUpdatedCountLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewAddBlackBoxPreConditionUpdatedCountLogic(ctx context.Context, svcCtx *svc.ServiceContext) *AddBlackBoxPreConditionUpdatedCountLogic {
	return &AddBlackBoxPreConditionUpdatedCountLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// AddBlackBoxPreConditionUpdatedCount add blackbox precondition updated count
func (l *AddBlackBoxPreConditionUpdatedCountLogic) AddBlackBoxPreConditionUpdatedCount(in *pb.AddBlackBoxPreConditionUpdatedCountReq) (out *pb.AddBlackBoxPreConditionUpdatedCountResp, err error) {
	err = l.svcCtx.BlackBoxGenerationRecordModel.Save(l.ctx, &model.BlackBoxGenerationRecord{
		PreconditionUpdateCount: 1,
	})
	if err != nil {
		return nil, err
	}

	return &pb.AddBlackBoxPreConditionUpdatedCountResp{}, nil
}
