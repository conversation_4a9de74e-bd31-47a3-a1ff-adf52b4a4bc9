package blackboxgenerationrecordservicelogic

import (
	"context"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/model"

	"github.com/zeromicro/go-zero/core/logx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
)

type AddBlackBoxCaseStepUpdatedCountLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewAddBlackBoxCaseStepUpdatedCountLogic(ctx context.Context, svcCtx *svc.ServiceContext) *AddBlackBoxCaseStepUpdatedCountLogic {
	return &AddBlackBoxCaseStepUpdatedCountLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// AddBlackBoxCaseStepUpdatedCount add blackbox case step updated count
func (l *AddBlackBoxCaseStepUpdatedCountLogic) AddBlackBoxCaseStepUpdatedCount(in *pb.AddBlackBoxCaseStepUpdatedCountReq) (out *pb.AddBlackBoxCaseStepUpdatedCountResp, err error) {
	err = l.svcCtx.BlackBoxGenerationRecordModel.Save(l.ctx, &model.BlackBoxGenerationRecord{
		CaseStepUpdateCount: 1,
	})
	if err != nil {
		return nil, err
	}

	return &pb.AddBlackBoxCaseStepUpdatedCountResp{}, nil
}
