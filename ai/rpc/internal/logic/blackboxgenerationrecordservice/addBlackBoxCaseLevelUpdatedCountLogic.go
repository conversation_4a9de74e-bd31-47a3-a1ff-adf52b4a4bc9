package blackboxgenerationrecordservicelogic

import (
	"context"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/model"

	"github.com/zeromicro/go-zero/core/logx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
)

type AddBlackBoxCaseLevelUpdatedCountLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewAddBlackBoxCaseLevelUpdatedCountLogic(ctx context.Context, svcCtx *svc.ServiceContext) *AddBlackBoxCaseLevelUpdatedCountLogic {
	return &AddBlackBoxCaseLevelUpdatedCountLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// AddBlackBoxCaseLevelUpdatedCount add blackbox case level updated count
func (l *AddBlackBoxCaseLevelUpdatedCountLogic) AddBlackBoxCaseLevelUpdatedCount(in *pb.AddBlackBoxCaseLevelUpdatedCountReq) (out *pb.AddBlackBoxCaseLevelUpdatedCountReq, err error) {
	err = l.svcCtx.BlackBoxGenerationRecordModel.Save(l.ctx, &model.BlackBoxGenerationRecord{
		CaseLevelUpdateCount: 1,
	})
	if err != nil {
		return nil, err
	}

	return &pb.AddBlackBoxCaseLevelUpdatedCountReq{}, nil
}
