package blackboxgenerationrecordservicelogic

import (
	"context"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/model"

	"github.com/zeromicro/go-zero/core/logx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
)

type AddBlackBoxExpectResultUpdatedCountLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewAddBlackBoxExpectResultUpdatedCountLogic(ctx context.Context, svcCtx *svc.ServiceContext) *AddBlackBoxExpectResultUpdatedCountLogic {
	return &AddBlackBoxExpectResultUpdatedCountLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// AddBlackBoxExpectResultUpdatedCount add blackbox expect result updated count
func (l *AddBlackBoxExpectResultUpdatedCountLogic) AddBlackBoxExpectResultUpdatedCount(in *pb.AddBlackBoxExpectResultUpdatedCountReq) (out *pb.AddBlackBoxExpectResultUpdatedCountReq, err error) {
	err = l.svcCtx.BlackBoxGenerationRecordModel.Save(l.ctx, &model.BlackBoxGenerationRecord{
		ExpectResultUpdateCount: 1,
	})
	if err != nil {
		return nil, err
	}

	return &pb.AddBlackBoxExpectResultUpdatedCountReq{}, nil
}
