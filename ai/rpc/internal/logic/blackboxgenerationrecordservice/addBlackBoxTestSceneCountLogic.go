package blackboxgenerationrecordservicelogic

import (
	"context"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/model"

	"github.com/zeromicro/go-zero/core/logx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
)

type AddBlackBoxTestSceneCountLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewAddBlackBoxTestSceneCountLogic(ctx context.Context, svcCtx *svc.ServiceContext) *AddBlackBoxTestSceneCountLogic {
	return &AddBlackBoxTestSceneCountLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// AddBlackBoxTestSceneCount add blackbox test scene count
func (l *AddBlackBoxTestSceneCountLogic) AddBlackBoxTestSceneCount(in *pb.AddBlackBoxTestSceneCountReq) (out *pb.AddBlackBoxTestSceneCountResp, err error) {
	err = l.svcCtx.BlackBoxGenerationRecordModel.Save(l.ctx, &model.BlackBoxGenerationRecord{
		TestSceneCount: 1,
	})
	if err != nil {
		return nil, err
	}

	return &pb.AddBlackBoxTestSceneCountResp{}, nil
}
