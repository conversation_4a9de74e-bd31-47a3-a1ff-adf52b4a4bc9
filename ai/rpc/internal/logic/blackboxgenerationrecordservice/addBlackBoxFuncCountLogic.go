package blackboxgenerationrecordservicelogic

import (
	"context"

	"github.com/zeromicro/go-zero/core/logx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/model"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
)

type AddBlackBoxFuncCountLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewAddBlackBoxFuncCountLogic(ctx context.Context, svcCtx *svc.ServiceContext) *AddBlackBoxFuncCountLogic {
	return &AddBlackBoxFuncCountLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// AddBlackBoxFuncCount add blackbox func count
func (l *AddBlackBoxFuncCountLogic) AddBlackBoxFuncCount(in *pb.AddBlackBoxFuncCountReq) (out *pb.AddBlackBoxFuncCountResp, err error) {
	err = l.svcCtx.BlackBoxGenerationRecordModel.Save(l.ctx, &model.BlackBoxGenerationRecord{
		FunctionPointCount:      1,
		TestSceneCount:          0,
		SupplementDocumentCount: 0,
	})
	if err != nil {
		return nil, err
	}

	return &pb.AddBlackBoxFuncCountResp{}, nil
}
