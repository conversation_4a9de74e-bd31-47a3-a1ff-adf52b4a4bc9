package blackboxgenerationrecordservicelogic

import (
	"context"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/model"

	"github.com/zeromicro/go-zero/core/logx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
)

type AddBlackBoxSupplementDocumentCountLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewAddBlackBoxSupplementDocumentCountLogic(ctx context.Context, svcCtx *svc.ServiceContext) *AddBlackBoxSupplementDocumentCountLogic {
	return &AddBlackBoxSupplementDocumentCountLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// AddBlackBoxSupplementDocumentCount add blackbox supplement document count
func (l *AddBlackBoxSupplementDocumentCountLogic) AddBlackBoxSupplementDocumentCount(in *pb.AddBlackBoxSupplementDocumentCountReq) (out *pb.AddBlackBoxSupplementDocumentCountResp, err error) {
	err = l.svcCtx.BlackBoxGenerationRecordModel.Save(l.ctx, &model.BlackBoxGenerationRecord{
		SupplementDocumentCount: 1,
	})
	if err != nil {
		return nil, err
	}

	return &pb.AddBlackBoxSupplementDocumentCountResp{}, nil
}
