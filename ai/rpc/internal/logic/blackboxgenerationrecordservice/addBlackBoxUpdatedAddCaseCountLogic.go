package blackboxgenerationrecordservicelogic

import (
	"context"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/model"

	"github.com/zeromicro/go-zero/core/logx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
)

type AddBlackBoxUpdatedAddCaseCountLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewAddBlackBoxUpdatedAddCaseCountLogic(ctx context.Context, svcCtx *svc.ServiceContext) *AddBlackBoxUpdatedAddCaseCountLogic {
	return &AddBlackBoxUpdatedAddCaseCountLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// AddBlackBoxUpdatedAddCaseCount add blackbox updated add case count
func (l *AddBlackBoxUpdatedAddCaseCountLogic) AddBlackBoxUpdatedAddCaseCount(in *pb.AddBlackBoxUpdatedAddCaseCountReq) (out *pb.AddBlackBoxUpdatedAddCaseCountResp, err error) {
	err = l.svcCtx.BlackBoxGenerationRecordModel.Save(l.ctx, &model.BlackBoxGenerationRecord{
		UpdatedAddCount: in.GetCount(),
	})
	if err != nil {
		return nil, err
	}

	return &pb.AddBlackBoxUpdatedAddCaseCountResp{}, nil
}
