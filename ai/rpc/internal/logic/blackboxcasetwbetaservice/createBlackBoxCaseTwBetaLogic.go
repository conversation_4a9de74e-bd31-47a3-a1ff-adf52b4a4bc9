package blackboxcasetwbetaservicelogic

import (
	"context"
	"database/sql"

	"github.com/zeromicro/go-zero/core/stores/sqlx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/model"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/public/httpc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/logic"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
)

type CreateBlackBoxCaseTwBetaLogic struct {
	*logic.BaseLogic
}

func NewCreateBlackBoxCaseTwBetaLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CreateBlackBoxCaseTwBetaLogic {
	return &CreateBlackBoxCaseTwBetaLogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),
	}
}

// CreateBlackBoxCaseTwBeta list black box case of tw beta
func (l *CreateBlackBoxCaseTwBetaLogic) CreateBlackBoxCaseTwBeta(in *pb.CreateBlackBoxCaseTwBetaReq) (
	*pb.CreateBlackBoxCaseTwBetaResp, error,
) {
	twBetaIfo, err := l.SvcCtx.BlackBoxCaseTwBetaModel.FindOneByRevisionId(
		l.Ctx, in.Revision.RevisionId,
	)
	if err != nil {
		return nil, err
	}

	if twBetaIfo != nil {
		return nil, nil
	}

	// 新增
	betaCaseId, err := l.GenerateTwBetaId()
	if err != nil {
		return nil, err
	}

	caseInfo, err := l.SvcCtx.BlackBoxCaseModel.FindOneByCaseId(
		l.Ctx, in.Revision.CaseId,
	)
	if !l.CheckBlackBoxCaseDbData(caseInfo, err) {
		return nil, errorx.Err(errorx.NotExists, "用例不存在")
	}

	err, knowledge, resp, err2, done := l.getRevisionKnowledge(in, err)
	if done {
		return resp, err2
	}

	mindKnowledge, err := httpc.QueryTwBetaMindKnowledge(l.SvcCtx.ExternalAiDomain, in.Revision.CaseRefId)
	if err != nil {
		return nil, err
	}

	dataContent, err := httpc.Json2MD(
		l.SvcCtx.ExternalAiDomain, httpc.CaseTableReq{
			CaseTable: in.Items,
		},
	)
	if err != nil {
		return nil, err
	}
	err = l.SvcCtx.BlackBoxCaseTwBetaModel.Trans(
		l.Ctx, func(context context.Context, session sqlx.Session) error {
			_, err = l.SvcCtx.BlackBoxCaseTwBetaModel.Insert(
				l.Ctx, session, &model.BlackBoxCaseTwBeta{
					BetaCaseId:           betaCaseId,
					BetaCaseName:         knowledge.KnowledgeName + "-" + in.Revision.KnowledgeParagraphTitleText + "-" + in.Revision.RevisionName,
					RevisionId:           in.Revision.RevisionId,
					CaseContent:          *dataContent.Data,
					DocumentName:         knowledge.KnowledgeName,
					DocumentUrl:          knowledge.KnowledgeContent,
					DocumentChapterTitle: in.Revision.KnowledgeParagraphTitleText,
					DocumentContent:      (*mindKnowledge.Data).Knowledge,
					ReferenceDoc: sql.NullString{
						String: (*mindKnowledge.Data).SupplementKnowledge,
						Valid:  true,
					},
					KnowledgeFixSugg: caseInfo.KnowledgeFixSugg,
					CaseRefId:        in.Revision.CaseRefId,
					Status:           0,
					Deleted:          0,
				},
			)
			if err != nil {
				return err
			}
			return nil
		},
	)
	if err != nil {
		return nil, err
	}
	_, err = httpc.CreateTwBetaMind(
		l.SvcCtx.ExternalAiDomain, &httpc.CreateTwBetaMindReq{
			CaseId:        in.Revision.CaseRefId,
			CaseVersionId: betaCaseId,
		},
	)
	if err != nil {
		return nil, err
	}
	return &pb.CreateBlackBoxCaseTwBetaResp{}, nil
}

func (l *CreateBlackBoxCaseTwBetaLogic) getRevisionKnowledge(in *pb.CreateBlackBoxCaseTwBetaReq, err error) (
	error, *pb.BlackBoxCaseKnowledge, *pb.CreateBlackBoxCaseTwBetaResp, error, bool,
) {
	document, err := l.SvcCtx.BlackBoxCaseKnowledgeModel.FindOneByKnowledgeId(
		l.Ctx, in.Revision.KnowledgeId,
	)
	if !l.CheckBlackBoxCaseKnowledgeDbData(document, err) {
		return nil, nil, nil, errorx.Err(errorx.NotExists, "文档不存在"), true
	}

	knowledgeIds := []string{in.Revision.KnowledgeId}
	knowledgeRef, err := httpc.GetKnowledge(l.SvcCtx.ExternalAiDomain, knowledgeIds)
	if err != nil {
		return nil, nil, nil, err, true
	}

	if knowledgeRef.Data == nil || len(*knowledgeRef.Data) == 0 {
		return nil, nil, nil, errorx.Err(errorx.NotExists, "文档不存在"), true
	}

	knowledge := &pb.BlackBoxCaseKnowledge{}
	if err = utils.Copy(knowledge, document, l.Converters...); err != nil {
		return nil, nil, nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy db[%+v] to response, error: %+v", document, err,
		), true
	}

	knowledgeMap := make(map[string]httpc.ListKnowledgeResp)
	for _, knowledge := range *knowledgeRef.Data {
		knowledgeMap[knowledge.KnowledgeId] = *knowledge
	}
	knowledge.KnowledgeName = knowledgeMap[in.Revision.KnowledgeId].Title
	knowledge.KnowledgeContent = knowledgeMap[in.Revision.KnowledgeId].FeishuUrl
	knowledge.KnowledgeStatus = knowledgeMap[in.Revision.KnowledgeId].Status
	knowledge.KnowledgeParagraphTitle = knowledgeMap[in.Revision.KnowledgeId].JsonParagraphTitle
	knowledge.KnowledgeErrorMsg = knowledgeMap[in.Revision.KnowledgeId].ErrorMsg
	knowledge.KnowledgeType = "feishu"
	return err, knowledge, nil, nil, false
}
