package blackboxcasetwbetaservicelogic

import (
	"context"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/public/httpc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/logic"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
)

type UpdateBlackBoxCaseTwBetaMindContentLogic struct {
	*logic.BaseLogic
}

func NewUpdateBlackBoxCaseTwBetaMindContentLogic(ctx context.Context, svcCtx *svc.ServiceContext) *UpdateBlackBoxCaseTwBetaMindContentLogic {
	return &UpdateBlackBoxCaseTwBetaMindContentLogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),
	}
}

func (l *UpdateBlackBoxCaseTwBetaMindContentLogic) UpdateBlackBoxCaseTwBetaMindContent(in *pb.UpdateBlackBoxCaseMindTwBetaReq) (*pb.UpdateBlackBoxCaseMindTwBetaResp, error) {
	_, err := httpc.UpdateTwBetaMindContent(l.SvcCtx.ExternalAiDomain, in.MindId, in.MindContent)
	if err != nil {
		return nil, err
	}
	return &pb.UpdateBlackBoxCaseMindTwBetaResp{}, nil
}
