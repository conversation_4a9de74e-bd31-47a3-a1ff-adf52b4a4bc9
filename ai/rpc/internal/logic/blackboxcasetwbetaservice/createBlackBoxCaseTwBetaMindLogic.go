package blackboxcasetwbetaservicelogic

import (
	"context"
	"errors"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/public/httpc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/logic"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
)

type CreateBlackBoxCaseTwBetaMindLogic struct {
	*logic.BaseLogic
}

func NewCreateBlackBoxCaseTwBetaMindLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CreateBlackBoxCaseTwBetaMindLogic {
	return &CreateBlackBoxCaseTwBetaMindLogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),
	}
}

// CreateBlackBoxCaseTwBetaMind create black box case of tw beta mind
func (l *CreateBlackBoxCaseTwBetaMindLogic) CreateBlackBoxCaseTwBetaMind(in *pb.CreateBlackBoxCaseTwBetaMindReq) (*pb.CreateBlackBoxCaseTwBetaMindResp, error) {
	betaCase, err := l.SvcCtx.BlackBoxCaseTwBetaModel.FindOneByBetaCaseId(l.Ctx, in.BetaCaseId)
	if err != nil {
		return nil, err
	}
	if betaCase == nil {
		return nil, errors.New("beta Case not found")
	}
	_, err = httpc.CreateTwBetaMind(l.SvcCtx.ExternalAiDomain, &httpc.CreateTwBetaMindReq{
		CaseId:        betaCase.CaseRefId,
		CaseVersionId: betaCase.BetaCaseId,
		ProductModule: in.ProductModule,
		AcCheckList:   in.AcCheckList,
		TcCheckList:   in.TcCheckList,
	})
	if err != nil {
		return nil, err
	}
	return &pb.CreateBlackBoxCaseTwBetaMindResp{}, nil
}
