package blackboxcasetwbetaservicelogic

import (
	"context"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/public/httpc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/logic"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
)

type GetBlackBoxCaseTwBetaCaseContentLogic struct {
	*logic.BaseLogic
}

func NewGetBlackBoxCaseTwBetaCaseContentLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetBlackBoxCaseTwBetaCaseContentLogic {
	return &GetBlackBoxCaseTwBetaCaseContentLogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),
	}
}

func (l *GetBlackBoxCaseTwBetaCaseContentLogic) GetBlackBoxCaseTwBetaCaseContent(in *pb.GetBlackBoxCaseTwBetaContentReq) (*pb.GetBlackBoxCaseTwBetaContentResp, error) {
	res, err := httpc.QueryTwBetaCaseContent(l.SvcCtx.ExternalAiDomain, in.MindId)
	if err != nil {
		return nil, err
	}
	return &pb.GetBlackBoxCaseTwBetaContentResp{Data: *res.Data}, nil
}
