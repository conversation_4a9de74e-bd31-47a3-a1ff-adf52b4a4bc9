package blackboxcasetwbetaservicelogic

import (
	"context"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/public/httpc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/svc"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
)

type ListBlackBoxCaseTwBetaMindCheckLogic struct {
	*logic.BaseLogic
}

func NewListBlackBoxCaseTwBetaMindCheckLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ListBlackBoxCaseTwBetaMindCheckLogic {
	return &ListBlackBoxCaseTwBetaMindCheckLogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),
	}
}

// ListBlackBoxCaseTwBetaMindCheck list black box case of tw beta mind check
func (l *ListBlackBoxCaseTwBetaMindCheckLogic) ListBlackBoxCaseTwBetaMindCheck(in *pb.ListBlackBoxCaseTwBetaMindCheckReq) (*pb.ListBlackBoxCaseTwBetaMindCheckResp, error) {
	res, err := httpc.QueryTwBetaMindCheckList(l.SvcCtx.ExternalAiDomain, in.ProductModule)
	if err != nil {
		return nil, err
	}
	return &pb.ListBlackBoxCaseTwBetaMindCheckResp{
		AcCheckList: (*res.Data).AcCheckList,
		TcCheckList: (*res.Data).TcCheckList,
	}, nil
}
