package blackboxcasetwbetaservicelogic

import (
	"context"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/public/httpc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/logic"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
)

type GetBlackBoxCaseTwBetaMindContentLogic struct {
	*logic.BaseLogic
}

func NewGetBlackBoxCaseTwBetaMindContentLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetBlackBoxCaseTwBetaMindContentLogic {
	return &GetBlackBoxCaseTwBetaMindContentLogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),
	}
}

// GetBlackBoxCaseTwBetaMindContent get black box case of tw beta mind content
func (l *GetBlackBoxCaseTwBetaMindContentLogic) GetBlackBoxCaseTwBetaMindContent(in *pb.GetBlackBoxCaseTwBetaMindContentReq) (*pb.GetBlackBoxCaseTwBetaMindContentResp, error) {
	res, err := httpc.QueryTwBetaMindContent(l.SvcCtx.ExternalAiDomain, in.Id, in.MindType)
	if err != nil {
		return nil, err
	}
	return &pb.GetBlackBoxCaseTwBetaMindContentResp{Data: *res.Data}, nil
}
