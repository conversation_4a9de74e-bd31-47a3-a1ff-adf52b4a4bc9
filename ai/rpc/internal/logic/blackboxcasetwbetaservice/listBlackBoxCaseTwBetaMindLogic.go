package blackboxcasetwbetaservicelogic

import (
	"context"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/public/httpc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/logic"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
)

type ListBlackBoxCaseTwBetaMindLogic struct {
	*logic.BaseLogic
}

func NewListBlackBoxCaseTwBetaMindLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ListBlackBoxCaseTwBetaMindLogic {
	return &ListBlackBoxCaseTwBetaMindLogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),
	}
}

// ListBlackBoxCaseTwBetaMind list black box case of tw beta mind
func (l *ListBlackBoxCaseTwBetaMindLogic) ListBlackBoxCaseTwBetaMind(in *pb.ListBlackBoxCaseTwBetaMindReq) (*pb.ListBlackBoxCaseTwBetaMindResp, error) {
	res, err := httpc.QueryTwBetaMindList(l.SvcCtx.ExternalAiDomain, in.BetaCaseId)
	if err != nil {
		return nil, err
	}
	var mindList []*pb.BlackBoxCaseTwBetaMind
	for _, mind := range *res.Data {
		mindList = append(mindList, &pb.BlackBoxCaseTwBetaMind{
			Id:            mind.Id,
			CreatedAt:     mind.CreatedAt,
			ProductModule: mind.ProductModule,
			AcCheckList:   mind.AcCheckList,
			TcCheckList:   mind.TcCheckList,
			Status:        mind.Status,
		})
	}
	return &pb.ListBlackBoxCaseTwBetaMindResp{
		Items: mindList,
	}, nil
}
