package blackboxcasetwbetaservicelogic

import (
	"context"
	"math"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
)

type ListBlackBoxCaseTwBetaLogic struct {
	*logic.BaseLogic
}

func NewListBlackBoxCaseTwBetaLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ListBlackBoxCaseTwBetaLogic {
	return &ListBlackBoxCaseTwBetaLogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),
	}
}

// ListBlackBoxCaseTwBeta list black box case of tw beta
func (l *ListBlackBoxCaseTwBetaLogic) ListBlackBoxCaseTwBeta(in *pb.ListBlackBoxCaseTwBetaReq) (*pb.ListBlackBoxCaseTwBetaResp, error) {
	queryObj := model.ListBlackBoxCaseTwBetaQueryObj{
		BetaCaseName: in.BetaCaseName,
		Pagination:   in.GetPagination(),
	}

	dbDataList, err := l.SvcCtx.BlackBoxCaseTwBetaModel.FindByQueryObj(l.Ctx, queryObj)
	if err != nil {
		return nil, err
	}

	total, err := l.SvcCtx.BlackBoxCaseTwBetaModel.CountByQueryObj(l.Ctx, queryObj)
	if err != nil {
		return nil, err
	}

	var out = &pb.ListBlackBoxCaseTwBetaResp{Items: make([]*pb.BlackBoxCaseTwBeta, 0, len(dbDataList))}
	for _, db := range dbDataList {
		item := &pb.BlackBoxCaseTwBeta{}
		if err = utils.Copy(item, db, l.Converters...); err != nil {
			return nil, errors.Wrapf(
				errorx.Err(errorx.CopyToStructFailure, err.Error()),
				"failed to copy db[%+v] to response, error: %+v", db, err,
			)
		}
		out.Items = append(out.Items, item)
	}

	out.TotalCount = uint64(total)
	out.TotalPage = uint64(math.Ceil(float64(total) / float64(in.Pagination.PageSize)))

	return out, nil
}
