package blackboxcasedocumentservicelogic

import (
	"context"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/public/httpc"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
)

type DeleteBlackBoxCaseDocumentForAssistantLogic struct {
	*logic.BaseLogic
}

func NewDeleteBlackBoxCaseDocumentForAssistantLogic(
	ctx context.Context, svcCtx *svc.ServiceContext,
) *DeleteBlackBoxCaseDocumentForAssistantLogic {
	return &DeleteBlackBoxCaseDocumentForAssistantLogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),
	}
}

// DeleteBlackBoxCaseDocumentForAssistant deletes a black box case document for assistant
func (l *DeleteBlackBoxCaseDocumentForAssistantLogic) DeleteBlackBoxCaseDocumentForAssistant(req *pb.DeleteBlackBoxCaseDocumentForAssistantReq) (
	resp *pb.DeleteBlackBoxCaseDocumentForAssistantResp, err error,
) {
	resp = new(pb.DeleteBlackBoxCaseDocumentForAssistantResp)
	if l.CurrentUser == nil {
		return nil, nil
	}
	// 内部：统一删除接口 todo 要清理缓存
	err = l.DeleteDocByDocIdList(req.GetProjectId(), req.GetAssistantId(), l.CurrentUser.Account, req.GetDocumentId())
	if err != nil {
		return nil, err
	}
	// todo 外部：同步删除外部文档调用接口
	// 调用test-genius接口
	err = httpc.DeleteDocument(l.SvcCtx.ExternalAiDomain, req.DocumentId)
	if err != nil {
		return nil, err
	}

	return resp, nil
}
