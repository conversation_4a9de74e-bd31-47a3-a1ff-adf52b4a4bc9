package blackboxcasedocumentservicelogic

import (
	"context"
	"database/sql"

	"github.com/zeromicro/go-zero/core/stores/sqlx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/public/httpc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
)

type CreateBlackBoxCaseDocumentForAssistantLogic struct {
	*logic.BaseLogic
}

func NewCreateBlackBoxCaseDocumentForAssistantLogic(
	ctx context.Context, svcCtx *svc.ServiceContext,
) *CreateBlackBoxCaseDocumentForAssistantLogic {
	return &CreateBlackBoxCaseDocumentForAssistantLogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),
	}
}

// CreateBlackBoxCaseDocumentForAssistant creates a black box case document for assistant
func (l *CreateBlackBoxCaseDocumentForAssistantLogic) CreateBlackBoxCaseDocumentForAssistant(req *pb.CreateBlackBoxCaseDocumentForAssistantReq) (
	out *pb.CreateBlackBoxCaseDocumentForAssistantResp, err error,
) {
	resp := new(pb.CreateBlackBoxCaseDocumentForAssistantResp)
	if l.CurrentUser == nil {
		return nil, nil
	}
	dbAssistantData, err := l.SvcCtx.BlackBoxCaseAssistantModel.FindOneByProjectIdAssistantId(
		l.Ctx, req.GetProjectId(), req.GetAssistantId(),
	)
	if !l.CheckBlackBoxCaseAssistantDbData(dbAssistantData, err) {
		return nil, errorx.Err(errorx.NotExists, "数据不存在")
	}
	docIdDbList := make([]string, 0, len(req.Items))
	docIdDbMap := make(map[string]string, len(req.Items))
	if err = l.SvcCtx.BlackBoxCaseDocumentModel.Trans(
		l.Ctx, func(context context.Context, session sqlx.Session) error {
			// 创建文档
			for _, item := range req.Items {
				documentId, err := l.create(req, item, session)
				if err != nil {
					return err
				}
				switch item.GetDocumentType() {
				case pb.DocumentType_DOCUMENT_TYPE_TEXT:
					docIdDbMap[documentId] = item.DocumentText
				default:
					docIdDbMap[documentId] = item.DocumentUrl
				}
				docIdDbList = append(docIdDbList, documentId)
			}
			if len(docIdDbList) > 0 {
				// 更新助手关联文档外键
				// var ids string
				if strs := dbAssistantData.DocumentIds.String; len(strs) == 0 {
					dbAssistantData.DocumentIds = sql.NullString{
						String: l.MergeDocIds2Str([]string{}, docIdDbList...),
						Valid:  true,
					}
				} else {
					strList := l.GetDocIdsForStr(strs)
					dbAssistantData.DocumentIds = sql.NullString{
						String: l.MergeDocIds2Str(strList, docIdDbList...),
						Valid:  true,
					}
				}
				_, err := l.SvcCtx.BlackBoxCaseAssistantModel.Update(
					l.Ctx, session, dbAssistantData,
				)
				if err != nil {
					return err
				}
				_ = docIdDbMap
			}
			return nil
		},
	); err != nil {
		return nil, err
	}

	resp.DocumentIdList = docIdDbList
	resp.AssistantId = req.GetAssistantId()
	resp.ProjectId = req.GetProjectId()
	return resp, nil
}

func (l *CreateBlackBoxCaseDocumentForAssistantLogic) create(
	req *pb.CreateBlackBoxCaseDocumentForAssistantReq, item *pb.CreateBlackBoxCaseDocumentForAssistant,
	session sqlx.Session,
) (string, error) {
	documentId, err := l.GenerateAiDocumentId(req.GetProjectId(), req.GetAssistantId())
	if err != nil {
		return "", err
	}
	data := &model.BlackBoxCaseDocument{
		ProjectId:    req.GetProjectId(),
		AssistantId:  req.GetAssistantId(),
		DocumentId:   documentId,
		DocumentName: item.GetDocumentName(),
		DocumentDescription: sql.NullString{
			String: item.GetDocumentDescription(),
			Valid:  true,
		},
		DocumentUrl: sql.NullString{
			String: item.GetDocumentUrl(),
			Valid:  true,
		},
		DocumentText: sql.NullString{
			String: item.GetDocumentText(),
			Valid:  true,
		},
		DocumentType: int64(item.DocumentType),
		Status:       int64(pb.DocumentStatus_DOCUMENT_STATUS_PROCESSING),
		CreatedBy:    l.CurrentUser.Account,
	}
	// 增量文档
	_, err = l.SvcCtx.BlackBoxCaseDocumentModel.Insert(l.Ctx, session, data)
	if err != nil {
		return "", err
	}

	// 调用test-genius创建文档接口
	err = httpc.CreateDocument(l.SvcCtx.ExternalAiDomain, data)
	if err != nil {
		return "", err
	}

	return documentId, nil
}
