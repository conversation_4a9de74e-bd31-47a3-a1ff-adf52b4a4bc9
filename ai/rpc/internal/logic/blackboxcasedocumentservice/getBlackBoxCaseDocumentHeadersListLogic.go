package blackboxcasedocumentservicelogic

import (
	"context"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/public/httpc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/logic"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
)

type GetBlackBoxCaseDocumentHeadersListLogic struct {
	*logic.BaseLogic
}

func NewGetBlackBoxCaseDocumentHeadersListLogic(
	ctx context.Context, svcCtx *svc.ServiceContext,
) *GetBlackBoxCaseDocumentHeadersListLogic {
	return &GetBlackBoxCaseDocumentHeadersListLogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),
	}
}

// GetBlackBoxCaseDocumentHeadersList get black box case document Headers list
func (l *GetBlackBoxCaseDocumentHeadersListLogic) GetBlackBoxCaseDocumentHeadersList(
	in *pb.GetBlackBoxCaseDocumentHeadersListReq,
) (out *pb.GetBlackBoxCaseDocumentHeadersListResp, err error) {
	err, resp := httpc.GetBlackBoxCaseDocumentHeadersList(l.SvcCtx.ExternalAiDomain, in)
	if err != nil {
		return nil, err
	}
	return &pb.GetBlackBoxCaseDocumentHeadersListResp{
		Items: *resp.Data,
	}, nil
}
