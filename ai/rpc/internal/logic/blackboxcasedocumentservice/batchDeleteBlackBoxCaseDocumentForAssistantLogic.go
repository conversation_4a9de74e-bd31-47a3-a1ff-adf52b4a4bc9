package blackboxcasedocumentservicelogic

import (
	"context"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
)

type BatchDeleteBlackBoxCaseDocumentForAssistantLogic struct {
	*logic.BaseLogic
}

func NewBatchDeleteBlackBoxCaseDocumentForAssistantLogic(
	ctx context.Context, svcCtx *svc.ServiceContext,
) *BatchDeleteBlackBoxCaseDocumentForAssistantLogic {
	return &BatchDeleteBlackBoxCaseDocumentForAssistantLogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),
	}
}

// BatchDeleteBlackBoxCaseDocumentForAssistant batch deletes black box case documents for assistant
func (l *BatchDeleteBlackBoxCaseDocumentForAssistantLogic) BatchDeleteBlackBoxCaseDocumentForAssistant(req *pb.BatchDeleteBlackBoxCaseDocumentForAssistantReq) (
	resp *pb.BatchDeleteBlackBoxCaseDocumentForAssistantResp, err error,
) {
	resp = new(pb.BatchDeleteBlackBoxCaseDocumentForAssistantResp)
	if l.CurrentUser == nil {
		return nil, nil
	}
	// 内部：统一删除接口
	err = l.DeleteDocByDocIdList(
		req.GetProjectId(), req.GetAssistantId(), l.CurrentUser.Account, req.GetDocumentIds()...,
	)
	if err != nil {
		return nil, err
	}
	// todo 外部：同步删除外部文档调用接口
	return resp, nil
}
