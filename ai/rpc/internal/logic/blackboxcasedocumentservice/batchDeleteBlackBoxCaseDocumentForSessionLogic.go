package blackboxcasedocumentservicelogic

import (
	"context"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
)

type BatchDeleteBlackBoxCaseDocumentForSessionLogic struct {
	*logic.BaseLogic
}

func NewBatchDeleteBlackBoxCaseDocumentForSessionLogic(
	ctx context.Context, SvcCtx *svc.ServiceContext,
) *BatchDeleteBlackBoxCaseDocumentForSessionLogic {
	return &BatchDeleteBlackBoxCaseDocumentForSessionLogic{
		BaseLogic: logic.NewBaseLogic(ctx, SvcCtx),
	}
}

// BatchDeleteBlackBoxCaseDocumentForSession batch deletes black box case documents for session
func (l *BatchDeleteBlackBoxCaseDocumentForSessionLogic) BatchDeleteBlackBoxCaseDocumentForSession(req *pb.BatchDeleteBlackBoxCaseDocumentForSessionReq) (
	resp *pb.BatchDeleteBlackBoxCaseDocumentForSessionResp, err error,
) {
	resp = new(pb.BatchDeleteBlackBoxCaseDocumentForSessionResp)
	if l.CurrentUser == nil {
		return nil, nil
	}
	b, err := l.checkBatchDeleteBlackBoxCaseDocumentForSession(
		req.GetProjectId(), req.GetAssistantId(), req.GetSessionId(), "",
	)
	if !b {
		return resp, err
	}
	/*dbAssistantData, err := l.SvcCtx.BlackBoxCaseAssistantModel.FindOneByProjectIdAssistantId(
		l.Ctx, req.GetProjectId(), req.GetAssistantId(),
	)

	dbSessionData, err := l.SvcCtx.BlackBoxCaseSessionModel.FindOneByProjectIdSessionId(
		l.Ctx, req.GetProjectId(), req.GetSessionId(),
	)*/
	// 内部：统一删除接口
	err = l.SvcCtx.BlackBoxCaseAssistantDocumentSessionRelationshipModel.DeleteByProjectIdAssistantIdSessionIdDocumentIds(
		l.Ctx, nil, req.GetProjectId(), req.GetAssistantId(), req.GetSessionId(), req.GetDocumentIds()...,
	)
	if err != nil {
		return nil, err
	}
	// todo 外部：同步删除外部文档调用接口
	return resp, nil
}
func (l *BatchDeleteBlackBoxCaseDocumentForSessionLogic) checkBatchDeleteBlackBoxCaseDocumentForSession(projectId, assistantId, sessionId, documentId string) (
	b bool, err error,
) {
	testData := &logic.Data{
		ProjectId:   projectId,
		AssistantId: assistantId,
		SessionId:   sessionId,
		DocumentId:  documentId,
		IsValid:     false,
	}
	assistantCheckChain := logic.NewAssistantCheckChain(l.BaseLogic)
	sessionCheckChain := logic.NewSessionCheckChain(l.BaseLogic)
	assistantCheckChain.SetNext(sessionCheckChain)
	return testData.IsValid, assistantCheckChain.Handle(testData)
}
