package blackboxcasedocumentservicelogic

import (
	"context"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
)

type CreateBlackBoxCaseDocumentForSessionRecvLogic struct {
	*logic.BaseLogic
}

func NewCreateBlackBoxCaseDocumentForSessionRecvLogic(
	ctx context.Context, svcCtx *svc.ServiceContext,
) *CreateBlackBoxCaseDocumentForSessionRecvLogic {
	return &CreateBlackBoxCaseDocumentForSessionRecvLogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),
	}
}

// CreateBlackBoxCaseDocumentForSessionRecv creates a black box case document for session recv
func (l *CreateBlackBoxCaseDocumentForSessionRecvLogic) CreateBlackBoxCaseDocumentForSessionRecv(in *pb.CreateBlackBoxCaseDocumentForSessionRecvReq) (
	out *pb.CreateBlackBoxCaseDocumentForSessionRecvResp, err error,
) {
	// todo: add your logic here and delete this line

	return &pb.CreateBlackBoxCaseDocumentForSessionRecvResp{}, nil
}
