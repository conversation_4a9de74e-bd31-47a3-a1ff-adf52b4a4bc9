package blackboxcasedocumentservicelogic

import (
	"context"
	"math"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
)

type SearchBlackBoxCaseDocumentForAssistantLogic struct {
	*logic.BaseLogic
}

func NewSearchBlackBoxCaseDocumentForAssistantLogic(
	ctx context.Context, svcCtx *svc.ServiceContext,
) *SearchBlackBoxCaseDocumentForAssistantLogic {
	return &SearchBlackBoxCaseDocumentForAssistantLogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),
	}
}

// SearchBlackBoxCaseDocumentForAssistant searches for black box case documents for assistant
func (l *SearchBlackBoxCaseDocumentForAssistantLogic) SearchBlackBoxCaseDocumentForAssistant(req *pb.SearchBlackBoxCaseDocumentForAssistantReq) (
	resp *pb.SearchBlackBoxCaseDocumentForAssistantResp, err error,
) {
	resp = &pb.SearchBlackBoxCaseDocumentForAssistantResp{}
	b, err := l.checkSearchBlackBoxCaseDocumentForAssistant(
		req.GetProjectId(), req.GetAssistantId(),
	)
	if !b {
		return nil, err
	}

	dbAssistantData, err := l.SvcCtx.BlackBoxCaseAssistantModel.FindOneByProjectIdAssistantId(
		l.Ctx, req.GetProjectId(), req.GetAssistantId(),
	)
	if err != nil {
		return nil, err
	}

	docIdListStr := dbAssistantData.DocumentIds.String
	str := l.GetDocIdsForStr(docIdListStr)
	queryList, err := l.SvcCtx.BlackBoxCaseDocumentModel.FindByQueryObj(
		l.Ctx,
		model.SearchBlackBoxCaseDocumentForAssistant{
			ProjectId:      req.GetProjectId(),
			AssistantId:    req.GetAssistantId(),
			Pagination:     req.GetPagination(),
			DocumentIdList: str,
		},
	)
	if err != nil {
		return nil, err
	}

	resp.TotalCount = uint64(len(str))
	resp.TotalPage = 1
	resp.Items = make([]*pb.BlackBoxCaseDocument, 0, len(queryList))
	for _, db := range queryList {
		item := &pb.BlackBoxCaseDocument{}
		if err = utils.Copy(item, db, l.Converters...); err != nil {
			return nil, errors.Wrapf(
				errorx.Err(errorx.CopyToStructFailure, err.Error()),
				"failed to copy db[%+v] to response, error: %+v", db, err,
			)
		}
		resp.Items = append(resp.Items, item)
	}
	pagination := req.GetPagination()
	if pagination != nil {
		resp.CurrentPage = pagination.GetCurrentPage()
		resp.PageSize = pagination.GetPageSize()
		resp.TotalPage = uint64(math.Ceil(float64(resp.TotalCount) / float64(resp.PageSize)))
	}
	return resp, nil
}

func (l *SearchBlackBoxCaseDocumentForAssistantLogic) checkSearchBlackBoxCaseDocumentForAssistant(projectId, assistantId string) (
	b bool, err error,
) {
	testData := &logic.Data{
		ProjectId:   projectId,
		AssistantId: assistantId,
		SessionId:   "sessionId",
		DocumentId:  "documentId",
		IsValid:     false,
	}
	assistantCheckChain := logic.NewAssistantCheckChain(l.BaseLogic)
	assistantCheckChain.SetNext(nil)

	err = assistantCheckChain.Handle(testData)
	if err != nil {
		return false, err
	}

	return testData.IsValid, err
}
