package blackboxcasedocumentservicelogic

import (
	"context"

	"github.com/zeromicro/go-zero/core/stores/sqlx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/public/httpc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/logic"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
)

type ReloadBlackBoxCaseDocumentLogic struct {
	*logic.BaseLogic
}

func NewReloadBlackBoxCaseDocumentLogic(
	ctx context.Context, svcCtx *svc.ServiceContext,
) *ReloadBlackBoxCaseDocumentLogic {
	return &ReloadBlackBoxCaseDocumentLogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),
	}
}

// ReloadBlackBoxCaseDocument reload black box case document
func (l *ReloadBlackBoxCaseDocumentLogic) ReloadBlackBoxCaseDocument(req *pb.ReloadBlackBoxCaseDocumentReq) (
	out *pb.ReloadBlackBoxCaseDocumentResp, err error,
) {
	if l.CurrentUser == nil {
		return nil, nil
	}
	dbAssistantData, err := l.SvcCtx.BlackBoxCaseAssistantModel.FindOneByProjectIdAssistantId(
		l.Ctx, req.GetProjectId(), req.GetAssistantId(),
	)
	if !l.CheckBlackBoxCaseAssistantDbData(dbAssistantData, err) {
		return nil, errorx.Err(errorx.NotExists, "助手不存在")
	}

	document, err := l.SvcCtx.BlackBoxCaseDocumentModel.FindOneByDocumentId(l.Ctx, req.DocumentId)
	if err != nil {
		return nil, err
	}
	if document == nil {
		return nil, errorx.Err(errorx.NotExists, "文档不存在")
	}

	fn := func(context context.Context, session sqlx.Session) error {
		document.Status = 0
		_, err = l.SvcCtx.BlackBoxCaseDocumentModel.Update(context, session, document)
		return err
	}

	err = l.SvcCtx.BlackBoxCaseDocumentModel.Trans(
		l.Ctx, func(context context.Context, session sqlx.Session) error {
			return fn(context, session)
		},
	)
	if err != nil {
		return nil, err
	}
	// 调用接口重新加载文档
	err = httpc.ReloadDocument(l.SvcCtx.ExternalAiDomain, req.DocumentId)
	if err != nil {
		return nil, err
	}
	return &pb.ReloadBlackBoxCaseDocumentResp{}, nil
}
