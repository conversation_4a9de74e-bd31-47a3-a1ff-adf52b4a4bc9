package blackboxcasedocumentservicelogic

import (
	"context"
	"math"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
)

type SearchBlackBoxCaseDocumentForSessionLogic struct {
	*logic.BaseLogic
}

func NewSearchBlackBoxCaseDocumentForSessionLogic(
	ctx context.Context, svcCtx *svc.ServiceContext,
) *SearchBlackBoxCaseDocumentForSessionLogic {
	return &SearchBlackBoxCaseDocumentForSessionLogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),
	}
}

// SearchBlackBoxCaseDocumentForSession searches for black box case documents for session
func (l *SearchBlackBoxCaseDocumentForSessionLogic) SearchBlackBoxCaseDocumentForSession(req *pb.SearchBlackBoxCaseDocumentForSessionReq) (
	resp *pb.SearchBlackBoxCaseDocumentForSessionResp, err error,
) {
	resp = &pb.SearchBlackBoxCaseDocumentForSessionResp{}
	b, err := l.checkSearchBlackBoxCaseDocumentForSession(
		req.GetProjectId(), req.GetAssistantId(), req.GetSessionId(),
	)
	if !b {
		return resp, err
	}
	/*dbAssistantData, err := l.SvcCtx.BlackBoxCaseAssistantModel.FindOneByProjectIdAssistantId(
		l.Ctx, req.GetProjectId(), req.GetAssistantId(),
	)
	dbSessionData, err := l.SvcCtx.BlackBoxCaseSessionModel.FindOneByProjectIdSessionId(
		l.Ctx, req.GetProjectId(), req.GetSessionId(),
	)*/
	queryList, err := l.SvcCtx.BlackBoxCaseAssistantDocumentSessionRelationshipModel.FindJoinDocByProjectIdAssistantIdSessionId(
		l.Ctx, req.GetProjectId(), req.GetAssistantId(), req.GetSessionId(),
	)
	if err != nil {
		return nil, err
	}
	resp.TotalCount = uint64(len(queryList))
	resp.TotalPage = 1
	resp.Items = make([]*pb.BlackBoxCaseDocument, 0, len(queryList))
	for _, db := range queryList {
		item := &pb.BlackBoxCaseDocument{}
		if err = utils.Copy(item, db, l.Converters...); err != nil {
			return nil, errors.Wrapf(
				errorx.Err(errorx.CopyToStructFailure, err.Error()),
				"failed to copy db[%+v] to response, error: %+v", db, err,
			)
		}
		resp.Items = append(resp.Items, item)
	}
	pagination := req.GetPagination()
	if pagination != nil {
		resp.CurrentPage = pagination.GetCurrentPage()
		resp.PageSize = pagination.GetPageSize()
		resp.TotalPage = uint64(math.Ceil(float64(resp.TotalCount) / float64(resp.PageSize)))
	}
	return resp, nil
}

func (l *SearchBlackBoxCaseDocumentForSessionLogic) checkSearchBlackBoxCaseDocumentForSession(projectId, assistantId, sessionId string) (
	b bool, err error,
) {
	testData := &logic.Data{
		ProjectId:   projectId,
		AssistantId: assistantId,
		SessionId:   sessionId,
		IsValid:     false,
	}
	assistantCheckChain := logic.NewAssistantCheckChain(l.BaseLogic)
	sessionCheckChain := logic.NewSessionCheckChain(l.BaseLogic)
	sessionCheckChain.SetNext(nil)
	assistantCheckChain.SetNext(sessionCheckChain)
	return testData.IsValid, assistantCheckChain.Handle(testData)
}
