package blackboxcasedocumentservicelogic

import (
	"context"
	"database/sql"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/public/httpc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
)

type UpdateBlackBoxCaseDocumentLogic struct {
	*logic.BaseLogic
}

func NewUpdateBlackBoxCaseDocumentLogic(
	ctx context.Context, svcCtx *svc.ServiceContext,
) *UpdateBlackBoxCaseDocumentLogic {
	return &UpdateBlackBoxCaseDocumentLogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),
	}
}

// UpdateBlackBoxCaseDocument updates a black box case document
func (l *UpdateBlackBoxCaseDocumentLogic) UpdateBlackBoxCaseDocument(in *pb.UpdateBlackBoxCaseDocumentReq) (
	out *pb.UpdateBlackBoxCaseDocumentResp, err error,
) {
	if l.CurrentUser == nil {
		return nil, nil
	}
	dbData, err := l.SvcCtx.BlackBoxCaseDocumentModel.FindOneByProjectIdAssistantIdDocumentId(
		l.Ctx, in.GetProjectId(), in.GetAssistantId(), in.GetDocumentId(),
	)
	if !l.CheckBlackBoxCaseDocumentDbData(dbData, err) {
		return nil, errorx.Err(errorx.NotExists, "数据不存在")
	}
	dbData.DocumentName = in.GetDocumentName()
	dbData.DocumentDescription = sql.NullString{
		String: in.GetDocumentDescription(),
		Valid:  in.GetDocumentDescription() != "",
	}
	dbData.UpdatedBy = l.CurrentUser.Account
	_, err = l.SvcCtx.BlackBoxCaseDocumentModel.Update(l.Ctx, nil, dbData)

	err = httpc.UpdateDocument(l.SvcCtx.ExternalAiDomain, dbData)
	if err != nil {
		return nil, err
	}
	return &pb.UpdateBlackBoxCaseDocumentResp{}, err
}
