package blackboxcasedocumentservicelogic

import (
	"context"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/public/httpc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
)

type CreateBlackBoxCaseDocumentForSessionLogic struct {
	*logic.BaseLogic
}

func NewCreateBlackBoxCaseDocumentForSessionLogic(
	ctx context.Context, svcCtx *svc.ServiceContext,
) *CreateBlackBoxCaseDocumentForSessionLogic {
	return &CreateBlackBoxCaseDocumentForSessionLogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),
	}
}

// CreateBlackBoxCaseDocumentForSession creates a black box case document for session
func (l *CreateBlackBoxCaseDocumentForSessionLogic) CreateBlackBoxCaseDocumentForSession(req *pb.CreateBlackBoxCaseDocumentForSessionReq) (
	resp *pb.CreateBlackBoxCaseDocumentForSessionResp, err error,
) {
	resp = new(pb.CreateBlackBoxCaseDocumentForSessionResp)
	if l.CurrentUser == nil {
		return nil, nil
	}
	b, err := l.checkCreateBlackBoxCaseDocumentForSession(
		req.GetProjectId(), req.GetAssistantId(), req.GetSessionId(), req.GetDocumentId(),
	)
	if !b {
		return resp, err
	}
	_, err = l.SvcCtx.BlackBoxCaseAssistantDocumentSessionRelationshipModel.Insert(
		l.Ctx, nil, &model.BlackBoxCaseAssistantDocumentSessionRelationship{
			ProjectId:   req.GetProjectId(),
			AssistantId: req.GetAssistantId(),
			SessionId:   req.GetSessionId(),
			DocumentId:  req.GetDocumentId(),
			CreatedBy:   l.CurrentUser.Account,
			UpdatedBy:   l.CurrentUser.Account,
		},
	)
	if err != nil {
		return nil, err
	}

	err = httpc.CreateDocumentSessionRelation(l.SvcCtx.ExternalAiDomain, req.GetSessionId(), req.GetDocumentId())
	if err != nil {
		return nil, err
	}
	return resp, nil
}
func (l *CreateBlackBoxCaseDocumentForSessionLogic) checkCreateBlackBoxCaseDocumentForSession(projectId, assistantId, sessionId, documentId string) (
	b bool, err error,
) {
	testData := &logic.Data{
		ProjectId:   projectId,
		AssistantId: assistantId,
		SessionId:   sessionId,
		DocumentId:  documentId,
		IsValid:     false,
	}
	assistantCheckChain := logic.NewAssistantCheckChain(l.BaseLogic)
	documentCheckChain := logic.NewDocumentCheckChain(l.BaseLogic)
	sessionCheckChain := logic.NewSessionCheckChain(l.BaseLogic)
	documentSessionRelationshipCheckChain := logic.NewDocumentSessionRelationshipCheckChain(l.BaseLogic)
	sessionCheckChain.SetNext(documentSessionRelationshipCheckChain)
	documentCheckChain.SetNext(sessionCheckChain)
	assistantCheckChain.SetNext(documentCheckChain)
	return testData.IsValid, assistantCheckChain.Handle(testData)
}
