package blackboxcasedocumentservicelogic

import (
	"context"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/public/httpc"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
)

type DeleteBlackBoxCaseDocumentForSessionLogic struct {
	*logic.BaseLogic
}

func NewDeleteBlackBoxCaseDocumentForSessionLogic(
	ctx context.Context, svcCtx *svc.ServiceContext,
) *DeleteBlackBoxCaseDocumentForSessionLogic {
	return &DeleteBlackBoxCaseDocumentForSessionLogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),
	}
}

// DeleteBlackBoxCaseDocumentForSession deletes a black box case document for session
func (l *DeleteBlackBoxCaseDocumentForSessionLogic) DeleteBlackBoxCaseDocumentForSession(req *pb.DeleteBlackBoxCaseDocumentForSessionReq) (
	resp *pb.DeleteBlackBoxCaseDocumentForSessionResp, err error,
) {
	resp = new(pb.DeleteBlackBoxCaseDocumentForSessionResp)
	if l.CurrentUser == nil {
		return nil, nil
	}
	b, err := l.checkDeleteBlackBoxCaseDocumentForSession(
		req.GetProjectId(), req.GetAssistantId(), req.GetSessionId(), req.GetDocumentId(),
	)
	if !b {
		return resp, err
	}
	/*dbAssistantData, err := l.SvcCtx.BlackBoxCaseAssistantModel.FindOneByProjectIdAssistantId(
		l.Ctx, req.GetProjectId(), req.GetAssistantId(),
	)
	if !l.CheckBlackBoxCaseAssistantDbData(dbAssistantData, err) {
		return nil, errorx.Err(errorx.NotExists, "助手数据不存在")
	}
	dbDocumentData, err := l.SvcCtx.BlackBoxCaseDocumentModel.FindOneByProjectIdAssistantIdDocumentId(
		l.Ctx, req.GetProjectId(), req.GetAssistantId(), req.GetDocumentId(),
	)
	if !l.CheckBlackBoxCaseDocumentDbData(dbDocumentData, err) {
		return nil, errorx.Err(errorx.NotExists, "文档数据不存在")
	}*/
	// 内部：统一删除接口
	err = l.SvcCtx.BlackBoxCaseAssistantDocumentSessionRelationshipModel.DeleteByProjectIdAssistantIdSessionIdDocumentIds(
		l.Ctx, nil, req.GetProjectId(), req.GetAssistantId(), req.GetSessionId(), req.GetDocumentId(),
	)
	if err != nil {
		return nil, err
	}
	err = httpc.DeleteDocumentSessionRelation(l.SvcCtx.ExternalAiDomain, req.GetSessionId(), req.GetDocumentId())
	if err != nil {
		return nil, err
	}
	return resp, nil
}

func (l *DeleteBlackBoxCaseDocumentForSessionLogic) checkDeleteBlackBoxCaseDocumentForSession(projectId, assistantId, sessionId, documentId string) (
	b bool, err error,
) {
	testData := &logic.Data{
		ProjectId:   projectId,
		AssistantId: assistantId,
		SessionId:   sessionId,
		DocumentId:  documentId,
		IsValid:     false,
	}
	assistantCheckChain := logic.NewAssistantCheckChain(l.BaseLogic)
	sessionCheckChain := logic.NewSessionCheckChain(l.BaseLogic)
	documentCheckChain := logic.NewDocumentCheckChain(l.BaseLogic)
	documentCheckChain.SetNext(sessionCheckChain)
	assistantCheckChain.SetNext(documentCheckChain)
	return testData.IsValid, assistantCheckChain.Handle(testData)
}
