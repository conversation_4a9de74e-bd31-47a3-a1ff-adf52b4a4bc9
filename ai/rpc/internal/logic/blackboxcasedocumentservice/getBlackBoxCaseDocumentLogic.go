package blackboxcasedocumentservicelogic

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
)

type GetBlackBoxCaseDocumentLogic struct {
	*logic.BaseLogic
}

func NewGetBlackBoxCaseDocumentLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetBlackBoxCaseDocumentLogic {
	return &GetBlackBoxCaseDocumentLogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),
	}
}

// GetBlackBoxCaseDocument gets a black box case document
func (l *GetBlackBoxCaseDocumentLogic) GetBlackBoxCaseDocument(in *pb.GetBlackBoxCaseDocumentReq) (
	out *pb.GetBlackBoxCaseDocumentResp, err error,
) {
	resp := &pb.GetBlackBoxCaseDocumentResp{Item: &pb.BlackBoxCaseDocument{}}
	dbData, err := l.SvcCtx.BlackBoxCaseDocumentModel.FindOneByProjectIdAssistantIdDocumentId(
		l.Ctx,
		in.GetProjectId(),
		in.GetAssistantId(),
		in.GetDocumentId(),
	)
	if !l.CheckBlackBoxCaseDocumentDbData(dbData, err) {
		return nil, nil
	}
	if err = utils.Copy(resp.Item, dbData, l.Converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy black box case assistant[%+v] to response, error: %+v",
			dbData, err,
		)
	}
	return resp, nil
}
