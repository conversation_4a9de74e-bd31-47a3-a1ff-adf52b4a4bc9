package blackboxcasedocumentservicelogic

import (
	"context"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
)

type UpdateBlackBoxCaseDocumentStatusForAssistantLogic struct {
	*logic.BaseLogic
}

func NewUpdateBlackBoxCaseDocumentStatusForAssistantLogic(
	ctx context.Context, svcCtx *svc.ServiceContext,
) *UpdateBlackBoxCaseDocumentStatusForAssistantLogic {
	return &UpdateBlackBoxCaseDocumentStatusForAssistantLogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),
	}
}

// UpdateBlackBoxCaseDocumentStatusForAssistant update black box case document status for assistant
func (l *UpdateBlackBoxCaseDocumentStatusForAssistantLogic) UpdateBlackBoxCaseDocumentStatusForAssistant(in *pb.UpdateBlackBoxCaseDocumentStatusForAssistantReq) (
	out *pb.UpdateBlackBoxCaseDocumentStatusForAssistantResp, err error,
) {
	dbData, err := l.SvcCtx.BlackBoxCaseDocumentModel.FindOneByDocumentId(
		l.Ctx, in.GetDocumentId(),
	)
	if !l.CheckBlackBoxCaseDocumentDbData(dbData, err) {
		return nil, errorx.Err(errorx.NotExists, "文档数据不存在")
	}
	dbData.Status = int64(in.GetStatus())
	dbData.UpdatedBy = "sys"
	_, err = l.SvcCtx.BlackBoxCaseDocumentModel.Update(l.Ctx, nil, dbData)
	return &pb.UpdateBlackBoxCaseDocumentStatusForAssistantResp{}, err
}
