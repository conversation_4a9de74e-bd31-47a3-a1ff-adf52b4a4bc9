package blackboxcasemapservicelogic

import (
	"context"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/stores/sqlc"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"
)

type GetBlackBoxCaseMapLogic struct {
	*logic.BaseLogic
}

func NewGetBlackBoxCaseMapLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetBlackBoxCaseMapLogic {
	return &GetBlackBoxCaseMapLogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),
	}
}

// GetBlackBoxCaseMap get blackbox case map
func (l *GetBlackBoxCaseMapLogic) GetBlackBoxCaseMap(in *pb.GetBlackBoxCaseMapReq) (out *pb.GetBlackBoxCaseMapResp, err error) {
	if l.CurrentUser == nil {
		return nil, nil
	}

	resp := &pb.GetBlackBoxCaseMapResp{}
	dbData, err := l.SvcCtx.BlackBoxCaseMapModel.FindOneByCaseId(
		l.Ctx, in.GetCaseId(),
	)
	if !l.CheckBlackBoxCaseMapDbData(dbData, err) {
		if errors.Is(err, sqlc.ErrNotFound) {
			return &pb.GetBlackBoxCaseMapResp{}, nil
		}
		return nil, errorx.Err(errorx.NotExists, "数据不存在")
	}

	if err = utils.Copy(resp, dbData, l.Converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy blackbox case map[%+v] to response, error: %+v",
			dbData, err,
		)
	}

	return resp, nil
}
