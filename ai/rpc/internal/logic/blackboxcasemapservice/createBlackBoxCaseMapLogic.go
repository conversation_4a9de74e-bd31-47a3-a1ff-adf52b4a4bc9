package blackboxcasemapservicelogic

import (
	"context"
	"database/sql"

	"github.com/zeromicro/go-zero/core/jsonx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/model"

	"github.com/zeromicro/go-zero/core/stores/sqlx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
)

type CreateBlackBoxCaseMapLogic struct {
	*logic.BaseLogic
	currentLogic *BaseLogic
}

func NewCreateBlackBoxCaseMapLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CreateBlackBoxCaseMapLogic {
	return &CreateBlackBoxCaseMapLogic{
		BaseLogic:    logic.NewBaseLogic(ctx, svcCtx),
		currentLogic: newBaseLogic(ctx, svcCtx),
	}
}

// CreateBlackBoxCaseMap blackbox case map
func (l *CreateBlackBoxCaseMapLogic) CreateBlackBoxCaseMap(in *pb.CreateBlackBoxCaseMapReq) (out *pb.CreateBlackBoxCaseMapResp, err error) {
	if l.CurrentUser == nil {
		return nil, nil
	}

	dbData := &model.BlackBoxCaseMap{}

	err = l.SvcCtx.BlackBoxCaseDirModel.Trans(
		l.Ctx, func(context context.Context, session sqlx.Session) error {
			dbData.UpdatedBy = l.CurrentUser.Account
			dbData.ProjectId = in.GetProjectId()
			dbData.DirId = in.GetDirId()
			dbData.MapId, err = l.GenerateAiMapId()
			if err != nil {
				l.Logger.Errorf("CreateBlackBoxCaseMapLogic.Create: generate ai map id error:%+v", err)
				return err
			}
			dbData.CaseId = in.GetCaseId()
			dbData.Data = jsonx.MarshalToStringIgnoreError(in.GetData())
			dbData.Tags = jsonx.MarshalToStringIgnoreError(in.GetTags())
			dbData.Experiences = jsonx.MarshalToStringIgnoreError(in.GetExperiences())
			dbData.UsedKnowledge = sql.NullString{
				String: jsonx.MarshalToStringIgnoreError(in.GetUsedKnowledge()),
				Valid:  jsonx.MarshalToStringIgnoreError(in.GetUsedKnowledge()) != "",
			}
			dbData.RecallDocument = sql.NullString{
				String: jsonx.MarshalToStringIgnoreError(in.GetRecallDocument()),
				Valid:  jsonx.MarshalToStringIgnoreError(in.GetRecallDocument()) != "",
			}

			_, err = l.SvcCtx.BlackBoxCaseMapModel.Insert(l.Ctx, session, dbData)
			if err != nil {
				l.Logger.Errorf("CreateBlackBoxCaseMapLogic.Create: insert black box case map error:%+v", err)
				return err
			}
			return nil
		},
	)
	if err != nil {
		return nil, err
	}

	return &pb.CreateBlackBoxCaseMapResp{}, nil
}
