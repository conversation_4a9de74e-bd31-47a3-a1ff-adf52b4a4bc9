package blackboxcasemapservicelogic

import (
	"context"

	"github.com/zeromicro/go-zero/core/stores/sqlx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
)

type CreateBlackBoxCaseMapIdLogic struct {
	*logic.BaseLogic
}

func NewCreateBlackBoxCaseMapIdLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CreateBlackBoxCaseMapIdLogic {
	return &CreateBlackBoxCaseMapIdLogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),
	}
}

// CreateBlackBoxCaseMapId create blackbox case map_id
func (l *CreateBlackBoxCaseMapIdLogic) CreateBlackBoxCaseMapId(in *pb.CreateBlackBoxCaseMapIdReq) (
	out *pb.CreateBlackBoxCaseMapIdResp, err error,
) {
	if l.CurrentUser == nil {
		return nil, nil
	}

	mapId, err := l.GenerateAiMapId()
	if err != nil {
		return nil, err
	}

	dbData, err := l.SvcCtx.BlackBoxCaseMapModel.FindOneByCaseId(
		l.Ctx, in.GetCaseId(),
	)
	if !l.CheckBlackBoxCaseMapDbData(dbData, err) {
		return nil, errorx.Err(errorx.NotExists, "数据不存在")
	}

	err = l.SvcCtx.BlackBoxCaseDirModel.Trans(
		l.Ctx, func(context context.Context, session sqlx.Session) error {
			dbData.UpdatedBy = l.CurrentUser.Account
			dbData.MapId = mapId

			_, err = l.SvcCtx.BlackBoxCaseMapModel.Update(l.Ctx, session, dbData)
			if err != nil {
				return err
			}
			return nil
		},
	)

	return &pb.CreateBlackBoxCaseMapIdResp{MapId: mapId}, err
}
