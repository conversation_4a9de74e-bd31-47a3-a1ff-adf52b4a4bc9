package blackboxcasemapservicelogic

import (
	"context"

	"github.com/zeromicro/go-zero/core/stores/sqlx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
)

type UpdateBlackBoxCaseMapLogic struct {
	*logic.BaseLogic
}

func NewUpdateBlackBoxCaseMapLogic(ctx context.Context, svcCtx *svc.ServiceContext) *UpdateBlackBoxCaseMapLogic {
	return &UpdateBlackBoxCaseMapLogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),
	}
}

// UpdateBlackBoxCaseMap update blackbox case map
func (l *UpdateBlackBoxCaseMapLogic) UpdateBlackBoxCaseMap(in *pb.UpdateBlackBoxCaseMapReq) (out *pb.UpdateBlackBoxCaseMapResp, err error) {
	if l.CurrentUser == nil {
		return nil, nil
	}

	dbData, err := l.SvcCtx.BlackBoxCaseMapModel.FindOneByCaseId(
		l.Ctx, in.GetCaseId(),
	)
	if !l.CheckBlackBoxCaseMapDbData(dbData, err) {
		if _, err = l.create(in); err != nil {
			return nil, err
		} else {
			return &pb.UpdateBlackBoxCaseMapResp{}, nil
		}
	}

	err = l.SvcCtx.BlackBoxCaseDirModel.Trans(
		l.Ctx, func(context context.Context, session sqlx.Session) error {
			dbData.UpdatedBy = l.CurrentUser.Account
			dbData.Data = in.Data

			_, err = l.SvcCtx.BlackBoxCaseMapModel.Update(l.Ctx, session, dbData)
			if err != nil {
				return err
			}
			return nil
		},
	)

	return &pb.UpdateBlackBoxCaseMapResp{}, nil
}

func (l *UpdateBlackBoxCaseMapLogic) create(in *pb.UpdateBlackBoxCaseMapReq) (
	*model.BlackBoxCaseMap, error,
) {
	if l.CurrentUser == nil {
		return nil, nil
	}

	model := &model.BlackBoxCaseMap{
		ProjectId:   in.GetProjectId(),
		DirId:       in.GetDirId(),
		CaseId:      in.GetCaseId(),
		Data:        in.GetData(),
		Tags:        "[]",
		Experiences: "[]",
		CreatedBy:   l.CurrentUser.Account,
	}

	_, err := l.SvcCtx.BlackBoxCaseMapModel.Insert(l.Ctx, nil, model)
	if err != nil {
		return nil, err
	}
	return model, nil
}
