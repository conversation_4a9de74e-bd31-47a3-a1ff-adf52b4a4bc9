package blackboxcasemapservicelogic

import (
	"context"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
)

type GetCompleteBlackBoxCaseMapLogic struct {
	*logic.BaseLogic
}

func NewGetCompleteBlackBoxCaseMapLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetCompleteBlackBoxCaseMapLogic {
	return &GetCompleteBlackBoxCaseMapLogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),
	}
}

// GetCompleteBlackBoxCaseMap get complete blackbox case map
func (l *GetCompleteBlackBoxCaseMapLogic) GetCompleteBlackBoxCaseMap(in *pb.GetCompleteBlackBoxCaseMapReq) (out *pb.GetCompleteBlackBoxCaseMapResp, err error) {
	resp := &pb.GetCompleteBlackBoxCaseMapResp{}
	dbData, err := l.BaseLogic.SvcCtx.BlackBoxCaseMapModel.FindOneByCaseId(
		l.Ctx, in.GetCaseId(),
	)
	if !l.CheckBlackBoxCaseMapDbData(dbData, err) {
		return nil, errorx.Err(errorx.NotExists, "数据不存在")
	}

	resp.MapId = dbData.MapId
	resp.Data = dbData.Data
	resp.Experiences = dbData.Experiences
	resp.Tags = dbData.Tags
	resp.UsedKnowledge = dbData.UsedKnowledge.String
	resp.RecallDocument = dbData.RecallDocument.String
	resp.CreatedBy = dbData.CreatedBy
	resp.UpdatedBy = dbData.UpdatedBy
	resp.DeletedBy = dbData.DeletedBy.String
	resp.ProjectId = dbData.ProjectId
	resp.DirId = dbData.DirId
	resp.CaseId = dbData.CaseId

	return resp, nil
}
