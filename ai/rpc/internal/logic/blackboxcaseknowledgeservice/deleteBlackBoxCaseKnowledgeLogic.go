package blackboxcaseknowledgeservicelogic

import (
	"context"

	"github.com/zeromicro/go-zero/core/stores/sqlx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/public/httpc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
)

type DeleteBlackBoxCaseKnowledgeLogic struct {
	*logic.BaseLogic
}

func NewDeleteBlackBoxCaseKnowledgeLogic(ctx context.Context, svcCtx *svc.ServiceContext) *DeleteBlackBoxCaseKnowledgeLogic {
	return &DeleteBlackBoxCaseKnowledgeLogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),
	}
}

// DeleteBlackBoxCaseKnowledge create a black box case
func (l *DeleteBlackBoxCaseKnowledgeLogic) DeleteBlackBoxCaseKnowledge(in *pb.DeleteBlackBoxCaseKnowledgeReq) (out *pb.DeleteBlackBoxCaseKnowledgeResp, err error) {
	dbData, err := l.SvcCtx.BlackBoxCaseKnowledgeModel.FindOneByKnowledgeId(
		l.Ctx, in.GetKnowledgeId(),
	)

	if !l.CheckBlackBoxCaseKnowledgeDbData(dbData, err) {
		return nil, errorx.Err(errorx.NotExists, "数据不存在")
	}

	err = l.SvcCtx.BlackBoxCaseKnowledgeModel.Trans(
		l.Ctx, func(context context.Context, session sqlx.Session) error {
			if err = l.SvcCtx.BlackBoxCaseKnowledgeModel.Delete(l.Ctx, session, dbData.Id); err != nil {
				return err
			}
			err = httpc.DeleteKnowledge(l.SvcCtx.ExternalAiDomain, in.KnowledgeId)
			if err != nil {
				return err
			}
			return nil
		},
	)

	return &pb.DeleteBlackBoxCaseKnowledgeResp{}, nil
}
