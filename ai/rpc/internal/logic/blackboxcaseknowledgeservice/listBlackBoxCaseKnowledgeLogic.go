package blackboxcaseknowledgeservicelogic

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/public/httpc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"
)

type ListBlackBoxCaseKnowledgeLogic struct {
	*logic.BaseLogic
}

func NewListBlackBoxCaseKnowledgeLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ListBlackBoxCaseKnowledgeLogic {
	return &ListBlackBoxCaseKnowledgeLogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),
	}
}

// ListBlackBoxCaseKnowledge list black box case
func (l *ListBlackBoxCaseKnowledgeLogic) ListBlackBoxCaseKnowledge(in *pb.ListBlackBoxCaseKnowledgeReq) (out *pb.ListBlackBoxCaseKnowledgeResp, err error) {
	dbDataList, err := l.SvcCtx.BlackBoxCaseKnowledgeModel.FindByDirId(
		l.Ctx, in.GetDirId(),
	)
	if err != nil {
		return nil, err
	}

	out = &pb.ListBlackBoxCaseKnowledgeResp{Items: make([]*pb.BlackBoxCaseKnowledge, 0, len(dbDataList))}
	knowledgeIds := []string{}
	for _, db := range dbDataList {
		item := &pb.BlackBoxCaseKnowledge{}
		if err = utils.Copy(item, db, l.Converters...); err != nil {
			return nil, errors.Wrapf(
				errorx.Err(errorx.CopyToStructFailure, err.Error()),
				"failed to copy db[%+v] to response, error: %+v", db, err,
			)
		}
		knowledgeIds = append(knowledgeIds, db.KnowledgeId)
		// 调用AI接口获取详情
		// knowledgeRef, err := httpc.GetKnowledge(l.SvcCtx.ExternalAiDomain, []string{db.KnowledgeId})
		// if err != nil {
		// 	return nil, err
		// }
		// item.KnowledgeName = knowledgeRef.Data.FeishuTitle
		// item.KnowledgeContent = knowledgeRef.Data.FeishuUrl
		// item.KnowledgeStatus = knowledgeRef.Data.Status
		// item.KnowledgeParagraphTitle = knowledgeRef.Data.JsonParagraphTitle
		// item.KnowledgeType = "feishu"
		out.Items = append(out.Items, item)
	}

	// 批量获取文档详情
	knowledgeRef, err := httpc.GetKnowledge(l.SvcCtx.ExternalAiDomain, knowledgeIds)
	if err != nil {
		return nil, err
	}

	// 把文档列表放入map后续使用
	knowledgeMap := make(map[string]httpc.ListKnowledgeResp)
	for _, knowledge := range *knowledgeRef.Data {
		knowledgeMap[knowledge.KnowledgeId] = *knowledge
	}

	// 更新文档相关字段
	for _, item := range out.Items {
		item.KnowledgeName = knowledgeMap[item.KnowledgeId].Title
		item.KnowledgeContent = knowledgeMap[item.KnowledgeId].FeishuUrl
		item.KnowledgeStatus = knowledgeMap[item.KnowledgeId].Status
		item.KnowledgeParagraphTitle = knowledgeMap[item.KnowledgeId].JsonParagraphTitle
		item.KnowledgeErrorMsg = knowledgeMap[item.KnowledgeId].ErrorMsg
		item.KnowledgeType = "feishu"
	}
	return out, nil
}
