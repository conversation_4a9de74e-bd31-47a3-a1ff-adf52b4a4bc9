package blackboxcaseknowledgeservicelogic

import (
	"context"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/stores/sqlx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
)

type UpdateBlackBoxCaseKnowledgeLogic struct {
	*logic.BaseLogic
}

func NewUpdateBlackBoxCaseKnowledgeLogic(
	ctx context.Context, svcCtx *svc.ServiceContext,
) *UpdateBlackBoxCaseKnowledgeLogic {
	return &UpdateBlackBoxCaseKnowledgeLogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),
	}
}

// UpdateBlackBoxCaseKnowledge create a black box case
func (l *UpdateBlackBoxCaseKnowledgeLogic) UpdateBlackBoxCaseKnowledge(in *pb.UpdateBlackBoxCaseKnowledgeReq) (
	out *pb.UpdateBlackBoxCaseKnowledgeResp, err error,
) {
	if l.CurrentUser == nil {
		return nil, nil
	}
	dbData, err := l.SvcCtx.BlackBoxCaseKnowledgeModel.FindOneByKnowledgeId(
		l.Ctx, in.GetKnowledgeId(),
	)
	if !l.CheckBlackBoxCaseKnowledgeDbData(dbData, err) {
		return nil, errorx.Err(errorx.NotExists, "数据不存在")
	}

	err = l.SvcCtx.BlackBoxCaseKnowledgeModel.Trans(
		l.Ctx, func(context context.Context, session sqlx.Session) error {
			item := &model.BlackBoxCaseKnowledge{Id: dbData.Id}
			if err = utils.Copy(item, in, l.Converters...); err != nil {
				return errors.Wrapf(
					errorx.Err(errorx.CopyToStructFailure, err.Error()),
					"failed to copy db[%+v] to response, error: %+v", in, err,
				)
			}
			item.UpdatedBy = l.CurrentUser.Account
			_, err = l.SvcCtx.BlackBoxCaseKnowledgeModel.Update(l.Ctx, session, item)
			if err != nil {
				return err
			}
			return nil
		},
	)

	return &pb.UpdateBlackBoxCaseKnowledgeResp{}, nil
}
