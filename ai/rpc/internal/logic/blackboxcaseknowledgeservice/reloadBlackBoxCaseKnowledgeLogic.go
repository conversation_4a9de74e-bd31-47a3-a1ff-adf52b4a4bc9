package blackboxcaseknowledgeservicelogic

import (
	"context"

	"github.com/zeromicro/go-zero/core/logx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/public/httpc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
)

type ReloadBlackBoxCaseKnowledgeLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewReloadBlackBoxCaseKnowledgeLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ReloadBlackBoxCaseKnowledgeLogic {
	return &ReloadBlackBoxCaseKnowledgeLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// ReloadBlackBoxCaseKnowledge list black box case
func (l *ReloadBlackBoxCaseKnowledgeLogic) ReloadBlackBoxCaseKnowledge(in *pb.ReloadBlackBoxCaseKnowledgeReq) (out *pb.ReloadBlackBoxCaseKnowledgeResp, err error) {
	err = httpc.ReloadKnowledge(l.svcCtx.ExternalAiDomain, in.KnowledgeId)
	if err != nil {
		return nil, err
	}
	return &pb.ReloadBlackBoxCaseKnowledgeResp{}, nil
}
