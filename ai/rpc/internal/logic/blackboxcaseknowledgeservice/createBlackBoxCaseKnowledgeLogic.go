package blackboxcaseknowledgeservicelogic

import (
	"context"
	"errors"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/public/httpc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
)

type CreateBlackBoxCaseKnowledgeLogic struct {
	*logic.BaseLogic
}

func NewCreateBlackBoxCaseKnowledgeLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CreateBlackBoxCaseKnowledgeLogic {
	return &CreateBlackBoxCaseKnowledgeLogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),
	}
}

// CreateBlackBoxCaseKnowledge create a black box case
func (l *CreateBlackBoxCaseKnowledgeLogic) CreateBlackBoxCaseKnowledge(in *pb.CreateBlackBoxCaseKnowledgeReq) (out *pb.CreateBlackBoxCaseKnowledgeResp, err error) {
	if l.CurrentUser == nil {
		return nil, nil
	}

	data, err := l.create(in)
	if err != nil {
		return nil, err
	}

	return &pb.CreateBlackBoxCaseKnowledgeResp{KnowledgeId: data.KnowledgeId}, nil
}

func (l *CreateBlackBoxCaseKnowledgeLogic) create(req *pb.CreateBlackBoxCaseKnowledgeReq) (
	*model.BlackBoxCaseKnowledge, error,
) {
	if l.CurrentUser == nil {
		return nil, nil
	}

	knowledgeId, err := l.GenerateAiKnowledgeId()
	if err != nil {
		return nil, err
	}

	// TODO 标记ref关联id
	knowledgeRef, err := httpc.CreateKnowledge(
		l.SvcCtx.ExternalAiDomain,
		httpc.CreateKnowledgeReq{
			KnowledgeId: knowledgeId,
			Title:       req.KnowledgeName,
			Url:         req.KnowledgeContent,
		},
	)
	if err != nil {
		return nil, err
	}
	if knowledgeRef.Code != 0 {
		return nil, errors.New("调用AI接口异常: " + knowledgeRef.Message)
	}
	knowledge := &model.BlackBoxCaseKnowledge{
		DirId:       req.GetDirId(),
		KnowledgeId: knowledgeId,
		// TODO 标记ref关联id
		CreatedBy: l.CurrentUser.Account,
	}

	_, err = l.SvcCtx.BlackBoxCaseKnowledgeModel.Insert(l.Ctx, nil, knowledge)
	if err != nil {
		return nil, err
	}
	return knowledge, nil
}
