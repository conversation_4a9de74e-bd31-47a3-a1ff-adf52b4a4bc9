package blackboxcaseknowledgeservicelogic

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/public/httpc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"
)

type GetBlackBoxCaseKnowledgeLogic struct {
	*logic.BaseLogic
}

func NewGetBlackBoxCaseKnowledgeLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetBlackBoxCaseKnowledgeLogic {
	return &GetBlackBoxCaseKnowledgeLogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),
	}
}

// GetBlackBoxCaseKnowledge gets a black box case
func (l *GetBlackBoxCaseKnowledgeLogic) GetBlackBoxCaseKnowledge(in *pb.GetBlackBoxCaseKnowledgeReq) (out *pb.GetBlackBoxCaseKnowledgeResp, err error) {
	resp := &pb.GetBlackBoxCaseKnowledgeResp{Item: &pb.BlackBoxCaseKnowledge{}}
	dbData, err := l.SvcCtx.BlackBoxCaseKnowledgeModel.FindOneByKnowledgeId(
		l.Ctx, in.GetKnowledgeId(),
	)
	if !l.CheckBlackBoxCaseKnowledgeDbData(dbData, err) {
		return nil, errorx.Err(errorx.NotExists, "数据不存在")
	}

	if err = utils.Copy(resp.Item, dbData, l.Converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy black box case [%+v] to response, error: %+v",
			dbData, err,
		)
	}
	knowledgeRef, err := httpc.GetKnowledge(l.SvcCtx.ExternalAiDomain, []string{dbData.KnowledgeId})
	if err != nil {
		return nil, err
	}
	resp.Item.KnowledgeName = (*knowledgeRef.Data)[0].FeishuTitle
	resp.Item.KnowledgeContent = (*knowledgeRef.Data)[0].FeishuUrl
	resp.Item.KnowledgeParagraphTitle = (*knowledgeRef.Data)[0].JsonParagraphTitle
	resp.Item.KnowledgeStatus = (*knowledgeRef.Data)[0].Status
	resp.Item.KnowledgeType = "feishu"
	return resp, nil
}
