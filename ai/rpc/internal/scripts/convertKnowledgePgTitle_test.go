package scripts

import (
	"context"
	"github.com/zeromicro/go-zero/core/conf"
	"github.com/zeromicro/go-zero/core/logx"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/config"
	blackboxcaserevisionservicelogic "gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/logic/blackboxcaserevisionservice"
	blackboxcaseservicelogic "gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/logic/blackboxcaseservice"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/userinfo"
	"testing"
)

func TestConvertBlackboxCaseKnowledgePgTitle(t *testing.T) {
	var c config.Config
	conf.MustLoad("./rpc/etc/ai.yaml", &c)
	ctx := userinfo.WithContext(
		context.Background(), &userinfo.UserInfo{
			Account:  "T3279",
			Fullname: "邱俊锋",
			DeptName: "平台研发组",
			Email:    "<EMAIL>",
		},
	)

	svcCtx := svc.NewServiceContext(c)
	logic := blackboxcaseservicelogic.NewUpdateBlackBoxCaseLogic(ctx, svcCtx)
	err := logic.ConvertKnowledgePgTitle()
	if err != nil {
		logx.Errorf("err: %v", err)
	}

}

func TestConvertBlackboxCaseRevisionKnowledgePgTitle(t *testing.T) {
	var c config.Config
	conf.MustLoad("./rpc/etc/ai.yaml", &c)
	ctx := userinfo.WithContext(
		context.Background(), &userinfo.UserInfo{
			Account:  "T3279",
			Fullname: "邱俊锋",
			DeptName: "平台研发组",
			Email:    "<EMAIL>",
		},
	)

	svcCtx := svc.NewServiceContext(c)
	logic := blackboxcaserevisionservicelogic.NewUpdateBlackBoxCaseRevisionWithCaseRefIdLogic(ctx, svcCtx)
	err := logic.ConvertKnowledgePgTitle()
	if err != nil {
		logx.Errorf("err: %v", err)
	}

}
