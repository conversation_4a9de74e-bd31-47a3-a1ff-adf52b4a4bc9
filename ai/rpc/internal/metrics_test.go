package internal

import (
	"context"
	"testing"

	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/redis"
	"github.com/zeromicro/go-zero/zrpc"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/config"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/userinfo"
)

func TestAddUserPvMetricsRedis(t *testing.T) {
	type args struct {
		ctx            context.Context
		serviceContext *svc.ServiceContext
		userInfo       *userinfo.UserInfo
	}

	redisConf := redis.RedisConf{
		Host: "************:6379",
		Type: "node",
		Pass: "",
		DB:   19,
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "test-1",
			args: args{
				ctx: context.Background(),
				serviceContext: svc.NewServiceContext(config.Config{
					RpcServerConf: zrpc.RpcServerConf{
						Redis: redis.RedisKeyConf{
							RedisConf: redisConf,
							Key:       "rpc.ai",
						},
					},
					Cache: []cache.NodeConf{
						{
							RedisConf: redisConf,
						},
					},
				}),
				userInfo: &userinfo.UserInfo{Fullname: "qiujunfeng"},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err := AddUserPvMetricsRedis(tt.args.ctx, tt.args.serviceContext, tt.args.userInfo); (err != nil) != tt.wantErr {
				t.Errorf("AddUserPvMetricsRedis() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}
