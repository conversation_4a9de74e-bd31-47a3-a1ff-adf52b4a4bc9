package svc

import (
	"github.com/zeromicro/go-zero/core/stores/redis"
	"github.com/zeromicro/go-zero/core/stores/sqlx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/config"
)

type ServiceContext struct {
	Config config.Config

	DB                                                    sqlx.SqlConn
	Redis                                                 *redis.Redis
	ExternalAiDomain                                      string
	BlackBoxCaseModel                                     model.BlackBoxCaseModel
	BlackBoxCaseDirModel                                  model.BlackBoxCaseDirModel
	BlackBoxCaseDataModel                                 model.BlackBoxCaseDataModel
	BlackBoxCaseRefModel                                  model.BlackBoxCaseRefModel
	BlackBoxCaseRevisionModel                             model.BlackBoxCaseRevisionModel
	BlackBoxCaseKnowledgeModel                            model.BlackBoxCaseKnowledgeModel
	BlackBoxCaseAssistantModel                            model.BlackBoxCaseAssistantModel
	BlackBoxCaseDocumentModel                             model.BlackBoxCaseDocumentModel
	BlackBoxCaseSessionModel                              model.BlackBoxCaseSessionModel
	BlackBoxCaseAssistantDocumentRelationshipModel        model.BlackBoxCaseAssistantDocumentRelationshipModel
	BlackBoxCaseAssistantDocumentSessionRelationshipModel model.BlackBoxCaseAssistantDocumentSessionRelationshipModel
	BlackBoxCaseTwBetaModel                               model.BlackBoxCaseTwBetaModel
	// v2
	BlackBoxCaseDirV2Model        model.BlackBoxCaseDirV2Model
	BlackBoxCaseMapModel          model.BlackBoxCaseMapModel
	BlackBoxCaseMapDocumentModel  model.BlackBoxCaseMapDocumentModel
	BlackBoxGenerationRecordModel model.BlackBoxGenerationRecordModel
}

func NewServiceContext(c config.Config) *ServiceContext {
	sqlConn := sqlx.NewMysql(c.DB.DataSource)
	return &ServiceContext{
		Config:           c,
		ExternalAiDomain: c.ExternalAiDomain,
		DB:               sqlConn,
		Redis:            redis.MustNewRedis(c.Redis.RedisConf, redis.WithDB(c.Redis.RedisConf.DB)),

		BlackBoxCaseModel:          model.NewBlackBoxCaseModel(sqlConn, c.Cache),
		BlackBoxCaseDirModel:       model.NewBlackBoxCaseDirModel(sqlConn, c.Cache),
		BlackBoxCaseDataModel:      model.NewBlackBoxCaseDataModel(sqlConn, c.Cache),
		BlackBoxCaseRefModel:       model.NewBlackBoxCaseRefModel(sqlConn, c.Cache),
		BlackBoxCaseRevisionModel:  model.NewBlackBoxCaseRevisionModel(sqlConn, c.Cache),
		BlackBoxCaseKnowledgeModel: model.NewBlackBoxCaseKnowledgeModel(sqlConn, c.Cache),
		BlackBoxCaseAssistantModel: model.NewBlackBoxCaseAssistantModel(sqlConn, c.Cache),
		BlackBoxCaseDocumentModel:  model.NewBlackBoxCaseDocumentModel(sqlConn, c.Cache),
		BlackBoxCaseSessionModel:   model.NewBlackBoxCaseSessionModel(sqlConn, c.Cache),
		BlackBoxCaseAssistantDocumentRelationshipModel: model.NewBlackBoxCaseAssistantDocumentRelationshipModel(
			sqlConn, c.Cache,
		),
		BlackBoxCaseAssistantDocumentSessionRelationshipModel: model.NewBlackBoxCaseAssistantDocumentSessionRelationshipModel(
			sqlConn, c.Cache,
		),
		BlackBoxCaseTwBetaModel: model.NewBlackBoxCaseTwBetaModel(sqlConn, c.Cache),
		// v2
		BlackBoxCaseDirV2Model:        model.NewBlackBoxCaseDirV2Model(sqlConn, c.Cache),
		BlackBoxCaseMapModel:          model.NewBlackBoxCaseMapModel(sqlConn, c.Cache),
		BlackBoxCaseMapDocumentModel:  model.NewBlackBoxCaseMapDocumentModel(sqlConn, c.Cache),
		BlackBoxGenerationRecordModel: model.NewBlackBoxGenerationRecordModel(sqlConn, c.Cache),
	}
}
