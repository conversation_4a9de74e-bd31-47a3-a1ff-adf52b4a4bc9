package svc

import (
	"github.com/zeromicro/go-zero/core/stores/redis"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/zrpc"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/types"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/public/mock"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/config"
)

func MockServiceContext() *ServiceContext {
	mock.MockLog()
	mysql, cacheConf := mock.MockMysql()
	sqlConn := sqlx.NewMysql(mysql)
	svc := &ServiceContext{
		Config: config.Config{
			RpcServerConf:    zrpc.RpcServerConf{},
			DB:               types.DBConfig{},
			Cache:            nil,
			UserRpc:          zrpc.RpcClientConf{},
			ExternalAiDomain: "",
		},

		Redis:                      redis.MustNewRedis(mock.MockRedisConfV2()),
		DB:                         sqlConn,
		BlackBoxCaseAssistantModel: model.NewBlackBoxCaseAssistantModel(sqlConn, cacheConf),
		BlackBoxCaseDocumentModel:  model.NewBlackBoxCaseDocumentModel(sqlConn, cacheConf),
		BlackBoxCaseSessionModel:   model.NewBlackBoxCaseSessionModel(sqlConn, cacheConf),
		BlackBoxCaseAssistantDocumentRelationshipModel: model.NewBlackBoxCaseAssistantDocumentRelationshipModel(
			sqlConn, cacheConf,
		),
		BlackBoxCaseAssistantDocumentSessionRelationshipModel: model.NewBlackBoxCaseAssistantDocumentSessionRelationshipModel(
			sqlConn, cacheConf,
		),
	}
	return svc
}
