package task_7day_check

import (
	"context"
	"database/sql"
	"strings"
	"sync"
	"time"

	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/mr"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/threading"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/redislock"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/beat/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/model"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/redis"
	commontask "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/redis/task"
)

type Task7dayCheck struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewTask7dayCheck(ctx context.Context, svcCtx *svc.ServiceContext) *Task7dayCheck {
	return &Task7dayCheck{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (task *Task7dayCheck) Context() context.Context {
	return task.ctx
}

func (task *Task7dayCheck) ServiceContext() *svc.ServiceContext {
	return task.svcCtx
}

func (task *Task7dayCheck) Run() (err error) {
	return task.run()
}

func (task *Task7dayCheck) run() (err error) {
	lock, err := redislock.NewRedisLockAndAcquire(
		task.svcCtx.Redis, redis.ConstRedisDispatcherTaskInfo7dayLockKey, redislock.WithExpire(60*time.Minute),
	)
	if err != nil {
		return nil
	}
	defer func() {
		// release redis lock
		e := lock.Release()
		if e != nil {
			return
		}
	}()
	var wg sync.WaitGroup
	now := time.Now()
	logx.WithContext(task.ctx).Infof("task Task7dayCheck, 开始处理7天任务,开始时间:%s", now)
	keys, err := task.svcCtx.TaskInfoProcessor.ScanTaskInfoAllKey(task.ctx)
	if err != nil {
		logx.WithContext(task.ctx).Errorf("task Task7dayCheck,error:%s", err)
		return err
	}

	for _, key := range keys {
		wg.Add(1)
		projectId := key
		f := func() {
			defer func() {
				wg.Done()
			}()
			logx.WithContext(task.ctx).Debugf(
				"task Task7dayCheck, 开始处理7天任务,当前处理的projectId:%s", projectId,
			)
			taskIdList, err := task.svcCtx.TaskInfoProcessor.FindMoreThan7DayByProjectId(task.ctx, projectId)
			if err != nil {
				logx.WithContext(task.ctx).Errorf(
					"task Task7dayCheck,当前处理的projectId: %s,error:%s", projectId, err,
				)
				return
			}
			logx.WithContext(task.ctx).Debugf(
				"task Task7dayCheck, 开始处理7天任务,当前处理的projectId:%s,taskList:[%s]", projectId, taskIdList,
			)
			// 获取缓存保存数据库
			txf := func(context context.Context, session sqlx.Session) error {
				taskInfoRecords := make([]*model.TaskInfoRecord, 0, len(taskIdList))
				/*for _, taksId := range taskIdList {
					if b, item := task.svcCtx.TaskInfoProcessor.CheckTaskInfoPoint(task.ctx, projectId, taksId); b {
						taskInfoRecords = append(taskInfoRecords, build(item))
					}
				}*/
				err := mr.MapReduceVoid[string, *commontask.InfoItem](
					func(source chan<- string) {
						for _, taskId := range taskIdList {
							source <- taskId
						}
					}, func(item string, writer mr.Writer[*commontask.InfoItem], cancel func(error)) {
						var err error
						defer func() {
							if err != nil {
								cancel(err)
							}
						}()
						if b, item := task.svcCtx.TaskInfoProcessor.CheckTaskInfoPoint(
							task.ctx, projectId, item,
						); b {
							writer.Write(item)
						}
					}, func(pipe <-chan *commontask.InfoItem, cancel func(error)) {
						var err error
						defer func() {
							if err != nil {
								cancel(err)
							}
						}()
						for item := range pipe {
							taskInfoRecords = append(taskInfoRecords, build(item))
						}
					}, mr.WithContext(context), mr.WithWorkers(len(taskIdList)),
				)
				if err != nil {
					logx.WithContext(task.ctx).Errorf(
						"task Task7dayCheck,获取缓存保存数据库,当前处理的projectId: %s, MapReduceVoid error:%s",
						projectId, err,
					)
					return err
				}
				_, err = task.svcCtx.TaskInfoRecordModel.BatchInsert(task.ctx, session, taskInfoRecords)
				if err != nil {
					logx.WithContext(task.ctx).Errorf(
						"task Task7dayCheck,获取缓存保存数据库,当前处理的projectId: %s,error:%s",
						projectId, err,
					)
					return err
				}
				// 缓存维护
				err = task.svcCtx.TaskInfoProcessor.HandleMoreThan7Day(task.ctx, projectId, taskIdList)
				if err != nil {
					logx.WithContext(task.ctx).Errorf(
						"task Task7dayCheck,当前处理的projectId: %s,error:%s", projectId, err,
					)
					return err
				}
				return nil
			}

			err = task.svcCtx.TaskInfoRecordModel.Trans(task.ctx, txf)
			if err != nil {
				logx.WithContext(task.ctx).Errorf(
					"task Task7dayCheck,当前处理的projectId: %s,error:%s", projectId, err,
				)
				return
			}
		}
		threading.GoSafeCtx(
			task.ctx, f,
		)
	}
	wg.Wait()
	logx.WithContext(task.ctx).Infof("task Task7dayCheck, 开始处理7天任务,耗时:%s", time.Since(now))
	return nil
}

func build(item *commontask.InfoItem) *model.TaskInfoRecord {
	record := &model.TaskInfoRecord{
		ProjectId:          item.ProjectId,
		PlanId:             item.PlanId,
		PlanName:           item.PlanName,
		TriggerMode:        item.TriggerMode,
		TaskId:             item.TaskId,
		PriorityType:       int64(item.PriorityType),
		TaskExecuteStatus:  int64(item.TaskExecuteStatus),
		TaskExecutedResult: int64(item.TaskExecutedResult),
		TotalCase:          item.TotalCase,
		FinishedCase:       item.FinishedCase,
		SuccessCase:        item.SuccessCase,
		TotalSuite:         item.TotalSuite,
		FinishedSuite:      item.FinishedSuite,
		SuccessSuite:       item.SuccessSuite,
		ExecutedBy:         item.ExecuteBy,
		CostTime:           item.CostTime,
		WaitTime:           item.WaitTime,
		StartedAt:          item.StartedAt,
		EndedAt: sql.NullInt64{
			Int64: item.EndedAt,
			Valid: true,
		},
		UpdateAt: sql.NullInt64{
			Int64: item.UpdateAt,
			Valid: true,
		},
	}
	if item.PlanMetaData != "" {
		record.PlanMetaData = sql.NullString{
			String: item.PlanMetaData,
			Valid:  true,
		}
	} else {
		record.PlanMetaData = sql.NullString{
			String: "{}",
			Valid:  false,
		}
	}
	return record
}

// return :{projectId}
func (m *Task7dayCheck) genProjectIdSuffix(projectId string) string {
	builder := strings.Builder{}
	builder.WriteString(":")
	builder.WriteString(projectId)
	return builder.String()
}
