package task_7day_check

import (
	"github.com/zeromicro/go-zero/core/service"
	"github.com/zeromicro/go-zero/core/stores/redis"
	"github.com/zeromicro/go-zero/core/stores/sqlx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/beat/internal/config"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/beat/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/common/mock"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/model"

	rediscommon "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/redis"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/redis/task"
)

func Mock_ServiceContext() *svc.ServiceContext {
	mock.Mock_Log()
	mysql, cacheConf := mock.Mock_Mysql()
	sqlConn := sqlx.NewMysql(mysql)
	return &svc.ServiceContext{
		Config: config.Config{
			ServiceConf: service.ServiceConf{
				Name: "mqc.dispatcher",
			},
		},
		RedisNode:     mock.Mock_Redis(),
		Redis:         redis.MustNewRedis(mock.Mock_RedisConfV2()),
		ReporterRpc:   mock.Mock_ReporterRpc(),
		DispatcherRpc: mock.Mock_DispatcherRpc(),
		TaskInfoProcessor: task.NewTaskInfoProcessor(
			rediscommon.NewRCacheService(
				mock.Mock_RedisConfV2(), rediscommon.DispatcherRedisKey,
			),
		),
		TaskInfoRecordModel: model.NewTaskInfoRecordModel(sqlConn, cacheConf),
	}
}

func MockTask7DayCheck() *Task7dayCheck {
	return NewTask7dayCheck(mock.Mock_Context(), Mock_ServiceContext())
}
