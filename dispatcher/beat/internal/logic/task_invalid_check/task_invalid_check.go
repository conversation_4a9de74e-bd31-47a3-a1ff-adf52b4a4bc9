package task_invalid_check

import (
	"context"
	"fmt"
	"sync"
	"time"

	"github.com/zeromicro/go-zero/core/logx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/redislock"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/beat/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/common/dtctl"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/common/utils"
	dispatcherpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/rpc/pb"
	managerpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
	reporter "gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/client/reporter"
)

type TaskInvalidCheck struct {
	logx.Logger
	ctx           context.Context
	svcCtx        *svc.ServiceContext
	Lock          sync.RWMutex // 任务颗粒度的读写锁
	tearDownFuncs []func()     // 注册释放函数
}

func NewTaskInvalidCheck(ctx context.Context, svcCtx *svc.ServiceContext) *TaskInvalidCheck {
	return &TaskInvalidCheck{
		Logger:        logx.WithContext(ctx),
		ctx:           ctx,
		svcCtx:        svcCtx,
		tearDownFuncs: make([]func(), 0, 10),
	}
}

func (task *TaskInvalidCheck) RegisterTeardownFunc(f func()) {
	task.Lock.Lock()
	defer task.Lock.Unlock()
	task.tearDownFuncs = append(task.tearDownFuncs, f)
}

func (task *TaskInvalidCheck) Context() context.Context {
	task.Lock.RLock()
	defer task.Lock.RUnlock()
	return task.ctx
}

func (task *TaskInvalidCheck) ServiceContext() *svc.ServiceContext {
	task.Lock.RLock()
	defer task.Lock.RUnlock()
	return task.svcCtx
}

func (task *TaskInvalidCheck) Setup() {
}

func (task *TaskInvalidCheck) Teardown() {
	for _, f := range task.tearDownFuncs {
		f()
	}
}

func (task *TaskInvalidCheck) Run() (err error) {
	task.Setup()

	err = task.run()

	task.Teardown()

	return err
}

func (task *TaskInvalidCheck) CanRun() bool {
	// 上一次执行完的时间
	last, err := dtctl.GetScanFinishTime(task.ctx, task.svcCtx.RedisNode)
	if err != nil {
		task.Errorf("GetScanFinishTime err: %s", err)
		return false
	}

	return time.Since(last) >= time.Minute*10
}

func (task *TaskInvalidCheck) run() (err error) {
	// 获取全局锁
	key := "lock:dispatcher:beat:task_invalid_check"
	lock, err := redislock.NewRedisLockAndAcquire(task.svcCtx.Redis, key, redislock.WithExpire(10*60*time.Second))
	if err != nil {
		return nil
	}
	defer func() {
		// release redis lock
		e := lock.Release()
		if e != nil {
			return
		}
	}()

	if !task.CanRun() {
		return nil
	}

	all, err := dtctl.ScanDispatchTaskControls(task.ctx, task.svcCtx.RedisNode, 16)
	if err != nil {
		return err
	}

	// 处理优先级: interface > suite > plan，因为计划需要依赖集合的结果是正确的
	plan, suite, inter := task.Split(all)
	err = task.MultiCheck(inter)
	if err != nil {
		task.Errorf("MultiCheck interface err: %s", err)
		return err
	}

	err = task.MultiCheck(suite)
	if err != nil {
		task.Errorf("MultiCheck suite err: %s", err)
		return err
	}

	err = task.MultiCheck(plan)
	if err != nil {
		task.Errorf("MultiCheck plan err: %s", err)
		return err
	}

	_ = dtctl.SetScanFinishTime(task.ctx, task.svcCtx.RedisNode, time.Now())

	return nil
}

// Split 分类任务类型
func (task *TaskInvalidCheck) Split(all []*dtctl.DispatchTaskControl) (plan, suite, inter []*dtctl.DispatchTaskControl) {
	plan = make([]*dtctl.DispatchTaskControl, 0, len(all))
	suite = make([]*dtctl.DispatchTaskControl, 0, len(all))
	inter = make([]*dtctl.DispatchTaskControl, 0, len(all))
	for _, ctl := range all {
		mgr := ctl.GetMgrMember()
		switch mgr.ComponentType {
		case managerpb.ApiExecutionDataType_API_PLAN:
			plan = append(plan, ctl)
		case managerpb.ApiExecutionDataType_API_SUITE:
			suite = append(suite, ctl)
		case managerpb.ApiExecutionDataType_INTERFACE_CASE:
			inter = append(inter, ctl)
		}
	}
	return plan, suite, inter
}

// MultiCheck 并发检查
func (task *TaskInvalidCheck) MultiCheck(ctls []*dtctl.DispatchTaskControl) (err error) {
	// 并发做
	multiCount := 16 // 并发量
	err = utils.MultiDo(
		task.Context(),
		ctls,
		func(item any) (rerr error) {
			ctl, ok := item.(*dtctl.DispatchTaskControl)
			if !ok {
				return fmt.Errorf("无效的item类型: %T", item)
			}

			starttime, rerr := ctl.Starttime()
			if rerr != nil {
				task.Errorf("获取starttime失败, err:%s", rerr)
				return rerr
			}

			if time.Duration(time.Now().Unix()-starttime) < time.Hour*12 {
				return nil
			}

			rerr = task.CheckOne(ctl)
			if rerr != nil {
				task.Errorf("CheckOne err: %s", rerr)
			}

			return nil
		}, multiCount,
	)
	return err
}

func (task *TaskInvalidCheck) CheckOne(ctl *dtctl.DispatchTaskControl) (err error) {
	// 获得所有的成员最终状态
	children, err := task.GetChildState(ctl)
	if err != nil {
		return err
	}

	// 更新最终状态
	state := dispatcherpb.ComponentState_Success
	total := int64(len(children))
	var fail int64 = 0
	for _, child := range children {
		if int64(dispatcherpb.ComponentState_value[child.GetStatus()]) >= int64(dispatcherpb.ComponentState_Failure) {
			state = dispatcherpb.ComponentState_Failure
			fail += 1
		}
	}
	succ := total - fail

	err = task.UpdateState(ctl, state, succ, fail)
	if err != nil {
		return err
	}

	// 删除info记录
	err = ctl.Clear()
	if err != nil {
		return err
	}

	return nil
}

func (task *TaskInvalidCheck) GetChildState(ctl *dtctl.DispatchTaskControl) (
	children []*reporter.GetChildrenRecordResponse_ChildRecord, err error,
) {
	task_id, err := ctl.TaskId()
	if err != nil {
		return nil, fmt.Errorf("获取task_id失败: %s", err)
	}

	execute_id, err := ctl.ExecuteId()
	if err != nil {
		return nil, fmt.Errorf("获取execute_id失败: %s", err)
	}

	project_id, err := ctl.ProjectId()
	if err != nil {
		return nil, fmt.Errorf("获取project_id失败: %s", err)
	}

	execute_type, err := ctl.ExecuteType()
	if err != nil {
		return nil, fmt.Errorf("获取execute_type失败: %s", err)
	}

	// 读取recorder.rpc，获取member的最终状态
	resp, err := task.ServiceContext().ReporterRpc.GetChild(
		task.Context(), &reporter.GetChildrenRecordRequest{
			TaskId:             task_id,
			ExecuteId:          execute_id,
			ProjectId:          project_id,
			ExecuteType:        execute_type,
			ComponentId:        ctl.GetMgrMember().ComponentId,
			ComponentType:      ctl.GetMgrMember().ComponentType.String(),
			ComponentExecuteId: ctl.GetMgrMember().ComponentExecuteId,
		},
	)
	if err != nil {
		return nil, fmt.Errorf("获取child失败")
	}
	return resp.GetChildRecordArray(), nil
}

func (task *TaskInvalidCheck) UpdateState(
	ctl *dtctl.DispatchTaskControl, state dispatcherpb.ComponentState, succ, fail int64,
) (err error) {
	switch ctl.GetMgrMember().ComponentType {
	case managerpb.ApiExecutionDataType_API_PLAN:
		return task.UpdateState_Plan(ctl, state, succ, fail)
	case managerpb.ApiExecutionDataType_API_SUITE:
		return task.UpdateState_Suite(ctl, state, succ, fail)
	case managerpb.ApiExecutionDataType_INTERFACE_CASE:
		return task.UpdateState_Interface(ctl, state, succ, fail)
	}
	return fmt.Errorf("无效的类型: %s", ctl.GetMgrMember().ComponentType)
}

func (task *TaskInvalidCheck) UpdateState_Plan(
	ctl *dtctl.DispatchTaskControl, state dispatcherpb.ComponentState, succ, fail int64,
) (err error) {
	_, err = task.ServiceContext().ReporterRpc.ModifyPlanRecord(
		task.Context(), &reporter.PutPlanRecordRequest{
			TaskId:        ctl.TaskIdIgnoreError(),
			ExecuteId:     ctl.ExecuteIdIgnoreError(),
			ProjectId:     ctl.ProjectIdIgnoreError(),
			PlanId:        ctl.GetMgrMember().ComponentId,
			PlanExecuteId: ctl.GetMgrMember().ComponentExecuteId,
			SuccessSuite:  succ,
			FailureSuite:  fail,
			Status:        state.String(),
			EndedAt:       time.Now().UnixMilli(),
		},
	)
	return err
}

func (task *TaskInvalidCheck) UpdateState_Suite(
	ctl *dtctl.DispatchTaskControl, state dispatcherpb.ComponentState, succ, fail int64,
) (err error) {
	_, err = task.ServiceContext().ReporterRpc.ModifySuiteRecord(
		task.Context(), &reporter.PutSuiteRecordRequest{
			TaskId:         ctl.TaskIdIgnoreError(),
			ExecuteId:      ctl.ExecuteIdIgnoreError(),
			ProjectId:      ctl.ProjectIdIgnoreError(),
			SuiteId:        ctl.GetMgrMember().ComponentId,
			SuiteExecuteId: ctl.GetMgrMember().ComponentExecuteId,
			SuccessCase:    succ,
			FailureCase:    fail,
			Status:         state.String(),
			EndedAt:        time.Now().UnixMilli(),
		},
	)
	return err
}

func (task *TaskInvalidCheck) UpdateState_Interface(
	ctl *dtctl.DispatchTaskControl, state dispatcherpb.ComponentState, succ, fail int64,
) (err error) {
	_, err = task.ServiceContext().ReporterRpc.ModifyInterfaceRecord(
		task.Context(), &reporter.PutInterfaceRecordRequest{
			TaskId:             ctl.TaskIdIgnoreError(),
			ExecuteId:          ctl.ExecuteIdIgnoreError(),
			ProjectId:          ctl.ProjectIdIgnoreError(),
			InterfaceId:        ctl.GetMgrMember().ComponentId,
			InterfaceExecuteId: ctl.GetMgrMember().ComponentExecuteId,
			SuccessCase:        succ,
			FailureCase:        fail,
			Status:             state.String(),
			EndedAt:            time.Now().UnixMilli(),
		},
	)
	return err
}
