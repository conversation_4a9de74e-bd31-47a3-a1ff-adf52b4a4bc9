package task_invalid_check

import (
	"github.com/zeromicro/go-zero/core/service"
	"github.com/zeromicro/go-zero/core/stores/redis"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/beat/internal/config"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/beat/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/common/mock"
)

func Mock_ServiceContext() *svc.ServiceContext {
	mock.Mock_Log()

	return &svc.ServiceContext{
		Config: config.Config{
			ServiceConf: service.ServiceConf{
				Name: "mqc.dispatcher",
			},
		},
		RedisNode:     mock.Mock_Redis(),
		Redis:         redis.MustNewRedis(mock.Mock_RedisConf()),
		ReporterRpc:   mock.Mock_ReporterRpc(),
		DispatcherRpc: mock.Mock_DispatcherRpc(),
	}
}

func Mock_TaskInvalidCheck() *TaskInvalidCheck {
	return NewTaskInvalidCheck(mock.Mock_Context(), Mock_ServiceContext())
}
