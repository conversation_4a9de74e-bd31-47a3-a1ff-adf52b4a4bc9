package config

import (
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/service"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/redis"
	"github.com/zeromicro/go-zero/zrpc"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/types"
)

type Config struct {
	service.ServiceConf

	Redis redis.RedisConf
	DB    types.DBConfig
	Cache cache.CacheConf

	Dispatcher zrpc.RpcClientConf
	Reporter   zrpc.RpcClientConf
}

func (c Config) LogConfig() logx.LogConf {
	return c.ServiceConf.Log
}

func (c Config) ListenOn() string {
	return "no need to listen"
}
