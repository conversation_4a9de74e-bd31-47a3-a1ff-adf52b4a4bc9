package task_7day_check

import (
	"context"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/beat/internal/logic/task_7day_check"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/beat/internal/svc"
)

func CreateHandler(svcCtx *svc.ServiceContext) func() {
	return func() {
		logic := task_7day_check.NewTask7dayCheck(context.Background(), svcCtx)
		err := logic.Run()
		if err != nil {
			logic.Errorf("运行失败：%s", err)
			return
		}

		logic.Infof("运行成功")
	}
}
