package main

import (
	"fmt"

	"github.com/spf13/cobra"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/version"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/beat/server"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/cmd"
)

var configFile = new(string)

func main() {
	root := cmd.NewRootCommand()
	root.Args = cobra.NoArgs
	root.RunE = func(cmd *cobra.Command, args []string) error {
		s, err := server.NewBeatServer(*configFile)
		if err != nil {
			return err
		}

		defer s.Stop()

		fmt.Printf("Starting mqc server...\n")
		s.Start()

		return nil
	}

	vi := version.NewVersionInfo()
	root.Version = vi.String()

	flags := root.Flags()
	flags.SortFlags = false
	flags.StringVarP(configFile, "beat-config", "f", "etc/dispatcher.yaml", "the config file of service")

	cobra.CheckErr(root.Execute())
}
