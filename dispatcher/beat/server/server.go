package server

import (
	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/conf"
	"github.com/zeromicro/go-zero/core/service"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/log"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/server"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/beat/internal/config"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/beat/internal/handler"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/beat/internal/svc"
)

// NewBeatServer for single server startup
func NewBeatServer(configFile string) (*server.Server, error) {
	return server.NewServer(Options(configFile)...)
}

// NewServer for combine server startup
func NewServer(c server.Config, w *log.ZapWriter) (service.Service, error) {
	cc, ok := c.(config.Config)
	if !ok {
		return nil, errors.Errorf("failed to new mqc server, cause by the config[%T] isn't a mqc config", c)
	}

	err := cc.ServiceConf.SetUp()
	if err != nil {
		return nil, errors.Errorf("failed to setup service config, error: %+v", err)
	}

	ctx := svc.NewServiceContext(cc)
	err = handler.RegisterHandlers(ctx.Scheduler, ctx)
	if err != nil {
		return nil, errors.Errorf("failed to register handler, error: %+v", err)
	}

	// 需要在 `ServiceConf.SetUp` 后再设置 `Writer`
	log.SetWriter(w)

	return ctx.Scheduler, nil
}

// NewConfig new a config of server
func NewConfig(configFile string) server.Config {
	var c config.Config
	conf.MustLoad(configFile, &c)

	return c
}

// Options as a param of `server.NewServer`
func Options(configFile string) []server.Option {
	return []server.Option{
		server.WithNewConfigFunc(func() server.Config {
			return NewConfig(configFile)
		}),
		server.WithNewServiceFunc(NewServer),
	}
}
