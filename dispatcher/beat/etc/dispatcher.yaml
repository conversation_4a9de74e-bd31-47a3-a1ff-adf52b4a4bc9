Name: beat.dispatcher

Log:
  ServiceName: beat.dispatcher
  Encoding: plain
  Level: info
  path: /app/logs/dispatcher

#Prometheus:
#  Host: 0.0.0.0
#  Port: 20324
#  Path: /metrics
#
#Telemetry:
#  Name: beat.dispatcher
#  Endpoint: http://127.0.0.1:14268/api/traces
#  Sampler: 1.0
#  Batcher: jaeger
#
#DevServer:
#  Enabled: true
#  Port: 20334

Redis:
  Host: 127.0.0.1:6379
  Type: node
  Pass:
  DB: 4

Cache:
  - Host: 127.0.0.1:6379
    Type: node
    Pass:
    DB: 4

DB:
  DataSource: root:Quwan@2020@tcp(127.0.0.1:3306)/dispatcher?charset=utf8mb4&parseTime=true&loc=Asia%2FShanghai

Dispatcher:
  #  Etcd:
  #    Hosts:
  #      - 127.0.0.1:2379
  #    Key: rpc.dispatcher
  Endpoints:
    - 127.0.0.1:20311
  NonBlock: true
  Timeout: 0

Reporter:
  #  Etcd:
  #    Hosts:
  #      - 127.0.0.1:2379
  #    Key: rpc.reporter
  Endpoints:
    - 127.0.0.1:20511
  NonBlock: true
  Timeout: 0
