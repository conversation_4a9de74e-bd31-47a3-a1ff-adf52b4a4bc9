Name: mqc.dispatcher

Log:
  ServiceName: mqc.dispatcher
  Encoding: plain
  Level: info
  Path: /app/logs/dispatcher

#Prometheus:
#  Host: 0.0.0.0
#  Port: 20323
#  Path: /metrics
#
#Telemetry:
#  Name: mqc.dispatcher
#  Endpoint: http://127.0.0.1:14268/api/traces
#  Sampler: 1.0
#  Batcher: jaeger
#
#DevServer:
#  Enabled: true
#  Port: 20333

Redis:
  Host: 127.0.0.1:6379
  Type: node
  Pass:
  DB: 4

Dispatcher:
  Endpoints:
    - 127.0.0.1:20311
  NonBlock: true
  Timeout: 0

LarkProxy:
  Endpoints:
    - 127.0.0.1:21311
  NonBlock: true

Manager:
  Endpoints:
    - 127.0.0.1:20211
  NonBlock: true
  Timeout: 0

Notifier:
  Endpoints:
    - 127.0.0.1:10311
  NonBlock: true
  Timeout: 0

Reporter:
  Endpoints:
    - 127.0.0.1:20511
  NonBlock: true
  Timeout: 0

User:
  Endpoints:
    - 127.0.0.1:10111
  NonBlock: true
  Timeout: 0

Consumer:
  Broker: 127.0.0.1:6379
  Backend: 127.0.0.1:6379
  Queue: mqc:dispatcher
  ConsumerTag: mqc_dispatcher
  IsEnableMetricsExporter: false # 新增加 暂时不需要开启
  IsMonitorHttp: false # 新增加 选择性开启
  Db: 5
  MaxWorker: 0

DispatcherProducer:
  Broker: 127.0.0.1:6379
  Backend: 127.0.0.1:6379
  Queue: mqc:dispatcher
  Db: 5

DispatcherTaskProducer:
  Broker: 127.0.0.1:6379
  Backend: 127.0.0.1:6379
  Queue: mq:dispatcher_task
  Db: 5

ManagerProducer:
  Broker: 127.0.0.1:6379
  Backend: 127.0.0.1:6379
  Queue: mqc:manager
  Db: 5

ReporterProducer:
  Broker: 127.0.0.1:6379
  Backend: 127.0.0.1:6379
  Queue: mqc:reporter
  Db: 5

UIWorkerProducer:
  Broker: 127.0.0.1:6379
  Backend: 127.0.0.1:6379
  Queue: mqc:uiworker
  Db: 5

PerfWorkerProducer:
  Broker: 127.0.0.1:6379
  Backend: 127.0.0.1:6379
  Queue: mqc:perfworker
  Db: 5

ProbeBaseURL: https://testing-dev-quality.ttyuyin.com # 测试环境
#ProbeBaseURL: https://dev-quality.ttyuyin.com # 生产环境

AppInsightBaseURL: https://tt-telemetry.ttyuyin.com

SLA:
  BaseURL: 'http://10.211.0.61:10089'
