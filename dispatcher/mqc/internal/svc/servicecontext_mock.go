package svc

import (
	"github.com/zeromicro/go-zero/core/service"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/common/mock"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/mqc/internal/config"
)

func Mock_ServiceContext() *ServiceContext {
	mock.Mock_Log()

	return &ServiceContext{
		Config: config.Config{
			ServiceConf: service.ServiceConf{
				Name: "mqc.dispatcher",
			},
		},
		Redis:              mock.Mock_Redis(),
		ManagerRpc:         mock.Mock_ManagerRpc(),
		UserRpc:            mock.Mock_UserRpc(),
		NotifierRpc:        mock.Mock_NotifyRpc(),
		ReporterRpc:        mock.Mock_ReporterRpc(),
		DispatcherRpc:      mock.Mock_DispatcherRpc(),
		DispatcherProducer: mock.Mock_DispatcherProducer(),
	}
}
