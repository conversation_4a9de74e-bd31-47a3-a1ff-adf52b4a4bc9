package config

import (
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/service"
	"github.com/zeromicro/go-zero/core/stores/redis"
	"github.com/zeromicro/go-zero/zrpc"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/thirdparty/sla"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
	consumerv2 "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworkerv2/consumer"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworkerv2/producer"
)

type Config struct {
	service.ServiceConf

	Redis redis.RedisConf

	Dispatcher zrpc.RpcClientConf
	LarkProxy  zrpc.RpcClientConf
	Manager    zrpc.RpcClientConf
	Notifier   zrpc.RpcClientConf
	Reporter   zrpc.RpcClientConf
	User       zrpc.RpcClientConf

	Consumer               consumerv2.Config
	DispatcherProducer     producer.Config
	DispatcherTaskProducer producer.Config
	ManagerProducer        producer.Config
	ReporterProducer       producer.Config
	UIWorkerProducer       producer.Config
	PerfWorkerProducer     producer.Config

	ProbeBaseURL      string
	AppInsightBaseURL string

	SLA sla.Config
}

func (c Config) LogConfig() logx.LogConf {
	return c.ServiceConf.Log
}

func (c Config) ListenOn() string {
	return constants.NoNeedToListen
}
