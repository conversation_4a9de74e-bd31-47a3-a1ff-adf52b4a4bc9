package handler

import (
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworkerv2/consumer"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	simple "gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/mqc/internal/handler/_simple"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/mqc/internal/handler/callback"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/mqc/internal/handler/interfacedocument"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/mqc/internal/handler/perfplan"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/mqc/internal/handler/perfreport"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/mqc/internal/handler/perfsuite"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/mqc/internal/handler/plan"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/mqc/internal/handler/precisioninterfacedocument"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/mqc/internal/handler/precisionsuite"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/mqc/internal/handler/service"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/mqc/internal/handler/stabilityReport"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/mqc/internal/handler/suite"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/mqc/internal/handler/uiplan"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/mqc/internal/handler/uireport"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/mqc/internal/handler/uisuite"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/mqc/internal/svc"
)

func RegisterHandlers(server *consumer.Consumer, serverCtx *svc.ServiceContext) error {
	return server.RegisterHandlers(
		consumer.NewTaskHandlerOjb("simple", simple.NewProcessor(serverCtx)),

		// 用例回调
		consumer.NewTaskHandlerOjb(constants.MQTaskTypeDispatcherCallback, callback.NewProcessor(serverCtx)),
		// 计划
		consumer.NewTaskHandlerOjb(constants.MQTaskTypeDispatcherPlan, plan.NewProcessor(serverCtx)),
		// 集合
		consumer.NewTaskHandlerOjb(constants.MQTaskTypeDispatcherSuite, suite.NewProcessor(serverCtx)),
		// 精准测试集合
		consumer.NewTaskHandlerOjb(
			constants.MQTaskTypeDispatcherPrecisionSuite, precisionsuite.NewProcessor(serverCtx),
		),
		// 接口
		consumer.NewTaskHandlerOjb(constants.MQTaskTypeDispatcherInterface, interfacedocument.NewProcessor(serverCtx)),
		// 精准测试接口
		consumer.NewTaskHandlerOjb(
			constants.MQTaskTypeDispatcherPrecisionInterface, precisioninterfacedocument.NewProcessor(serverCtx),
		),
		// 精准测试服务
		consumer.NewTaskHandlerOjb(constants.MQTaskTypeDispatcherService, service.NewProcessor(serverCtx)),
		// UI计划
		consumer.NewTaskHandlerOjb(constants.MQTaskTypeDispatcherUIPlan, uiplan.NewProcessor(serverCtx)),
		// UI集合
		consumer.NewTaskHandlerOjb(constants.MQTaskTypeDispatcherUISuite, uisuite.NewProcessor(serverCtx)),
		// UI报告
		consumer.NewTaskHandlerOjb(constants.MQTaskTypeUIWorkerGenerateReportResult, uireport.NewProcessor(serverCtx)),
		// 执行通知
		consumer.NewTaskHandlerOjb(
			constants.MQTaskTypeDispatcherPublishTaskCaseExecuting, uiplan.NewTaskProcessor(serverCtx),
		),
		// 压力测试执行通知
		consumer.NewTaskHandlerOjb(
			constants.MQTaskTypeDispatcherPublishPerfTaskCaseExecuting, perfplan.NewTaskProcessor(serverCtx),
		),
		// 压力测试计划
		consumer.NewTaskHandlerOjb(constants.MQTaskTypeDispatcherPerfPlan, perfplan.NewProcessor(serverCtx)),
		// 压力测试集合
		consumer.NewTaskHandlerOjb(constants.MQTaskTypeDispatcherPerfSuite, perfsuite.NewProcessor(serverCtx)),
		// 发送压测通知
		consumer.NewTaskHandlerOjb(
			constants.MQTaskTypeDispatcherSendPerfNotification, perfreport.NewProcessor(serverCtx),
		),
		// 发送稳定性测试通知
		consumer.NewTaskHandlerOjb(
			constants.MQTaskTypeDispatcherSendStabilityNotification, stabilityReport.NewProcessor(serverCtx),
		),
	)
}
