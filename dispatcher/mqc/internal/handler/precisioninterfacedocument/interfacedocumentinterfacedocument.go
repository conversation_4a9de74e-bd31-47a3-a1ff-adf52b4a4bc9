package precisioninterfacedocument

import (
	"context"

	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/trace"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworkerv2/base"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/mqc/internal/logic/precisioninterfacedocument"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/mqc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/rpc/pb"
)

// Deprecated: use new api.
func CreateHandler(svcCtx *svc.ServiceContext) func(data []byte) (string, error) {
	return func(data []byte) (string, error) {
		taskData := &pb.DistributeReq{}
		err := protobuf.UnmarshalJSON(data, taskData)
		if err != nil {
			logx.Errorf(
				"failed to unmarshal the payload of precision inteface document task, payload: %s, error: %+v",
				data, err,
			)
			return constants.FAILURE, err
		}

		logic := precisioninterfacedocument.NewPrecisionInterfaceDocument(context.Background(), svcCtx) // ctx done
		err = logic.Run(taskData)
		if err != nil {
			logx.Errorf(
				"failed to execute precision inteface document task, task_id: %s, error: %+v", taskData.GetTaskId(),
				err,
			)
			return constants.FAILURE, err
		}

		return constants.SUCCESS, nil
	}
}

type Processor struct {
	svcCtx *svc.ServiceContext
}

func NewProcessor(svcCtx *svc.ServiceContext) (call base.Handler) {
	return &Processor{
		svcCtx: svcCtx,
	}
}

func (processor *Processor) ProcessTask(ctx context.Context, task *base.Task) (result []byte, err error) {
	logger := logx.WithContext(ctx)
	logger.Debugf(
		"processor trace_id: %s, span_id: %s, task_name: %s",
		trace.TraceIDFromContext(ctx), trace.SpanIDFromContext(ctx), task.Typename,
	)

	taskData := &pb.DistributeReq{}
	err = protobuf.UnmarshalJSON(task.Payload, taskData)
	if err != nil {
		logger.Errorf(
			"failed to unmarshal the payload of precision interface document task, payload: %s, error: %+v",
			task.Payload, err,
		)
		return []byte(constants.FAILURE), err
	}

	logic := precisioninterfacedocument.NewPrecisionInterfaceDocument(ctx, processor.svcCtx)
	err = logic.Run(taskData)
	if err != nil {
		logger.Errorf(
			"failed to execute precision interface document task, task_id: %s, error: %+v", taskData.GetTaskId(), err,
		)
		return []byte(constants.FAILURE), err
	}

	return []byte(constants.SUCCESS), nil
}
