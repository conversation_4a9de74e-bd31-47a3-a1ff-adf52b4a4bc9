package perfreport

import (
	"context"

	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/trace"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworkerv2/base"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/mqc/internal/logic/perfreport"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/mqc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/rpc/pb"
)

var _ base.Handler = (*Processor)(nil)

type Processor struct {
	svcCtx *svc.ServiceContext
}

func NewProcessor(svcCtx *svc.ServiceContext) base.Handler {
	return &Processor{
		svcCtx: svcCtx,
	}
}

func (processor *Processor) ProcessTask(ctx context.Context, task *base.Task) (result []byte, err error) {
	logger := logx.WithContext(ctx)
	logger.Debugf(
		"processor trace_id: %s, span_id: %s, task_name: %s",
		trace.TraceIDFromContext(ctx), trace.SpanIDFromContext(ctx), task.Typename,
	)
	defer func() {
		if r := recover(); r != nil {
			logger.Errorf("processor recover result: %+v", r)
		}
	}()

	taskData := &pb.PerfReportCallback{}
	err = protobuf.UnmarshalJSON(task.Payload, taskData)
	if err != nil {
		logger.Errorf("failed to unmarshal the payload of perf report task, payload: %s, error: %+v", task.Payload, err)
		return []byte(constants.FAILURE), err
	}
	logger.Infof("got a perf report task, payload: %s", protobuf.MarshalJSONIgnoreError(taskData))

	err = perfreport.NewPerfReport(ctx, processor.svcCtx).Run(taskData)
	if err != nil {
		logger.Errorf("failed to execute perf report task, task_id: %s, error: %+v", taskData.GetTaskId(), err)
		return []byte(constants.FAILURE), err
	}

	return []byte(constants.SUCCESS), nil
}
