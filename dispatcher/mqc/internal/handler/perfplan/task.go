package perfplan

import (
	"context"
	"time"

	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/trace"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworkerv2/base"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	commonpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/mqc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/rpc/pb"
	reporterpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
)

type TaskProcessor struct {
	svcCtx *svc.ServiceContext
}

func NewTaskProcessor(svcCtx *svc.ServiceContext) (call base.Handler) {
	return &TaskProcessor{
		svcCtx: svcCtx,
	}
}

func (processor *TaskProcessor) ProcessTask(ctx context.Context, task *base.Task) (result []byte, err error) {
	logger := logx.WithContext(ctx)
	logger.Debugf(
		"processor trace_id: %s, span_id: %s, task_name: %s",
		trace.TraceIDFromContext(ctx), trace.SpanIDFromContext(ctx), task.Typename,
	)

	callReq := &pb.DistributeReq{}
	err = protobuf.UnmarshalJSON(task.Payload, callReq)
	if err != nil {
		logger.Errorf("failed to unmarshal the payload of task, payload: %s, error: %+v", task.Payload, err)
		return []byte(constants.FAILURE), err
	}

	// TaskInfoProcessor维护数据
	now := time.Now().UnixMilli()
	err = processor.svcCtx.TaskInfoProcessor.UpdateTaskExecuteStatus(
		ctx, callReq.GetProjectId(), callReq.GetTaskId(), commonpb.ExecuteStatus_TES_EXECUTING, now,
	)
	ec, ok := errorx.FromError(err)
	if ok && ec != nil && (ec.Code() == errorx.AlreadyExists || ec.Code() == errorx.AcquireRedisLockFailure) {
		return []byte(ec.String()), nil
	}
	if err != nil {
		logger.Errorf(
			"failed to update task execution status, task_id: %s, project_id: %s, error: %+v",
			callReq.GetTaskId(), callReq.GetProjectId(), err,
		)
		return []byte(err.Error()), err
	}

	req := &reporterpb.CreatePerfPlanRecordReq{
		TaskId:      callReq.GetTaskId(),
		ExecuteId:   callReq.GetPerfPlan().GetPerfPlanId(),
		ProjectId:   callReq.GetProjectId(),
		PlanId:      callReq.GetPerfPlan().GetPerfPlanId(),
		PlanName:    callReq.GetPerfPlan().PerfPlan.GetName(),
		TriggerMode: callReq.GetTriggerMode(),
	}
	_, err = processor.svcCtx.PerfReporterRpc.CreatePerfPlanRecord(ctx, req)
	if err != nil {
		logger.Errorf(
			"failed to modify perf plan record, task_id: %s, execute_id: %s, project_id: %s, plan_id: %s, error: %+v",
			callReq.GetTaskId(), callReq.GetPerfPlan().GetPerfPlanExecuteId(), callReq.GetProjectId(),
			callReq.GetPerfPlan().GetPerfPlanId(), err,
		)
		return []byte(err.Error()), err
	}

	return []byte(constants.SUCCESS), nil
}
