package perfsuite

import (
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/mqc/internal/logic/baselogic"
	managerpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type PerfSuite_Log struct {
	*baselogic.DispatchLogic_Log
	CaseExecutionMode int64 `json:"case_execution_mode"` // 用例执行方式
}

type PerfSuiteLogWriter struct {
	*baselogic.DispatchLogicLogWriter

	log *PerfSuite_Log
}

func NewPerfSuiteLogWriter(bw *baselogic.DispatchLogicLogWriter) *PerfSuiteLogWriter {
	writer := &PerfSuiteLogWriter{
		DispatchLogicLogWriter: bw,
		log: &PerfSuite_Log{
			DispatchLogic_Log: bw.Log(),
		},
	}
	return writer
}

func (writer *PerfSuiteLogWriter) toJson() string {
	return baselogic.Marshal<PERSON><PERSON>(writer.log)
}

func (writer *PerfSuiteLogWriter) setCaseExecutionMode(mode managerpb.ExecutionMode) {
	writer.log.CaseExecutionMode = int64(mode)
}
