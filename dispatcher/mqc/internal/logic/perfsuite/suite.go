package perfsuite

import (
	"context"
	"fmt"
	"time"

	"github.com/zeromicro/go-zero/core/logx"
	"google.golang.org/protobuf/types/known/timestamppb"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/codes"
	commonpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/common/callbackclient"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/common/dtctl"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/common/errcode"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/mqc/internal/logic/baselogic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/mqc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/rpc/client/dispatcher"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/rpc/pb"
	managerpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
	reporterpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
)

type PerfSuite struct {
	*baselogic.DispatchLogic
	logW *PerfSuiteLogWriter
}

func NewPerfSuite(ctx context.Context, svcCtx *svc.ServiceContext) *PerfSuite {
	return &PerfSuite{
		DispatchLogic: baselogic.NewDispatchLogic(ctx, svcCtx),
	}
}

func (x *PerfSuite) Run(req *pb.DistributeReq) (err error) {
	x.Setup(req)

	err = x.run()

	x.Teardown()

	return err
}

func (x *PerfSuite) Setup(req *pb.DistributeReq) {
	x.DispatchLogic.Setup(req)
	x.logW = NewPerfSuiteLogWriter(x.DispatchLogic.LogWriter())
	x.SetDtCtl(x.GetDtControl())
	x.logW.setCaseExecutionMode(managerpb.ExecutionMode_EM_PARALLEL) // 默认为并行
}

func (x *PerfSuite) run() (err error) {
	defer func() {
		// 空集合默认成功
		if x.Total() == 0 {
			x.StateM().UpdateStateManager(pb.ComponentState_Success, errcode.GetErrCode(errorx.OK))
		}

		if r := recover(); r != any(nil) {
			err = x.UpdatePanicStatef(codes.TaskPanic, "%v", r)
		}

		// 更新执行状态
		_, err = x.record()
		if err != nil {
			err = x.UpdatePanicStatef(codes.TaskRecordFailure, "Suite.record: %s", err)
		}

		// 如果是空集合，则直接回调
		err = x.Callback(err)
		if err != nil {
			err = x.UpdatePanicStatef(codes.TaskRecordFailure, "Suite.Callback: %s", err)
		}

		x.Infof("Suite[%s] : %s", x.Source().GetPerfSuite().GetPerfSuiteExecuteId(), x.Content())
	}()

	err = x.Publish(x.GetChildMembers(), x.PublishOne)
	if err != nil {
		return err
	}

	return nil
}

func (x *PerfSuite) GetDtControl() *dtctl.DispatchTaskControl {
	source := x.Source()
	member := &dtctl.DispatchTaskMember{
		ComponentId:        source.GetPerfSuite().GetPerfSuiteId(),
		ComponentType:      managerpb.ApiExecutionDataType_PERF_SUITE,
		Version:            "",
		ComponentExecuteId: source.GetPerfSuite().GetPerfSuiteExecuteId(),
	}
	return dtctl.NewDispatchTaskControl(x.Context(), x.ServiceContext().Redis, member)
}

func (x *PerfSuite) GetChildMembers() []*dtctl.DispatchTaskMember {
	if len(x.Source().GetPerfSuite().GetPerfCases()) == 0 {
		return nil
	}

	members := make([]*dtctl.DispatchTaskMember, 0, len(x.Source().GetPerfSuite().GetPerfCases()))
	for idx, item := range x.Source().GetPerfSuite().GetPerfCases() {
		case_ := item.GetPerfCase()
		if !x.ValidCase(case_) {
			continue
		}

		x.IncrTotal()
		members = append(
			members, &dtctl.DispatchTaskMember{
				ComponentId:   case_.GetCaseId(),
				ComponentType: item.GetType(),
				Index:         idx,
			},
		)
	}
	return members
}

func (x *PerfSuite) PublishOne(member *dtctl.DispatchTaskMember) (*dispatcher.PublishResp, error) {
	perfCase := x.Source().GetPerfSuite().GetPerfCases()[member.Index]
	in := &dispatcher.PublishReq{
		TriggerMode:  x.Source().GetTriggerMode(),
		TriggerRule:  x.Source().GetTriggerRule(),
		ProjectId:    x.Source().GetProjectId(),
		TaskId:       x.Source().GetTaskId(),
		ExecuteId:    member.ComponentExecuteId,
		ExecuteType:  x.Source().GetExecuteType(),
		PublishType:  pb.PublishType_PublishType_PERF_CASE,
		UserId:       x.Source().GetUserId(),
		PriorityType: x.Source().GetPriorityType(),
		Debug:        x.Source().GetDebug(),
		Data: &pb.PublishReq_PerfCase{
			PerfCase: &pb.PerfCasePublishInfo{
				PerfCaseId:         member.ComponentId,
				PerfCaseExecuteId:  member.ComponentExecuteId,
				PerfSuiteId:        x.Source().GetPerfSuite().GetPerfSuiteId(),
				PerfSuiteExecuteId: x.Source().GetPerfSuite().GetPerfSuiteExecuteId(),
				PerfPlanId:         x.Source().GetPerfSuite().GetPerfPlanId(),
				PerfPlanExecuteId:  x.Source().GetPerfSuite().GetPerfPlanExecuteId(),
				PerfCase:           perfCase,
				MetaData:           x.Source().GetPerfSuite().GetMetaData(),
				PerfPlanInfo:       x.Source().GetPerfSuite().GetPerfPlanInfo(),
			},
		},
	}
	return x.AsyncPublishTask(in, member)
}

func (x *PerfSuite) record() (resp *reporterpb.ModifyPerfSuiteRecordResp, err error) {
	x.updateTaskInfo()

	var endtime time.Time
	if x.Total() == 0 || x.StateM().State() == pb.ComponentState_Panic {
		endtime = time.Now()
	}
	resp, err = x.ServiceContext().PerfReporterRpc.ModifyPerfSuiteRecord(
		x.Context(), &reporterpb.ModifyPerfSuiteRecordReq{
			TaskId:        x.Source().GetTaskId(),
			ProjectId:     x.Source().GetProjectId(),
			ExecuteId:     x.Source().GetPerfSuite().GetPerfSuiteExecuteId(),
			SuiteId:       x.Source().GetPerfSuite().GetPerfSuiteId(),
			PlanExecuteId: x.Source().GetPerfSuite().GetPerfPlanExecuteId(),
			Status:        x.StateM().State().String(),
			EndedAt:       timestamppb.New(endtime),
		},
	)
	if err != nil {
		return nil, err
	}

	return resp, err
}

func (x *PerfSuite) updateTaskInfo() {
	// 先更新总数
	_ = x.ServiceContext().TaskInfoProcessor.UpdateTotalCaseForIncrNum(
		x.Context(), x.Source().GetProjectId(), x.Source().GetTaskId(), int(x.Total()),
	)
	// 只要关注这三个状态
	var t commonpb.ExecutedResult
	switch x.StateM().State() {
	case pb.ComponentState_Panic:
		t = commonpb.ExecutedResult_TER_PANIC
	case pb.ComponentState_Failure:
		// t=commonpb.ExecutedResult_TER_PANIC
	case pb.ComponentState_Success:
		// t = commonpb.ExecutedResult_TER_SUCCESS
	}
	if t == commonpb.ExecutedResult_TER_INIT {
		return
	}
	logx.WithContext(x.Context()).Infof("PerfSuite updateTaskInfo[%s] : taskId:%s", t, x.Source().GetTaskId())
	// 用例只记录总数和panic情况
	_ = x.ServiceContext().TaskInfoProcessor.UpdateTaskExecutedResult(
		x.Context(), x.Source().GetProjectId(), x.Source().GetTaskId(), t, time.Now().UnixMilli(),
	)
}

func (x *PerfSuite) Callback(err error) error {
	if x.Total() != 0 && err == nil {
		return nil
	}

	cb := &pb.CallbackReq{
		TriggerMode:  x.Source().GetTriggerMode(),
		TriggerRule:  x.Source().GetTriggerRule(),
		ProjectId:    x.Source().GetProjectId(),
		TaskId:       x.Source().GetTaskId(),
		ExecuteType:  x.Source().GetExecuteType(),
		CallbackType: pb.CallbackType_CallbackType_PERF_SUITE,
		Data: &pb.CallbackReq_PerfSuite{
			PerfSuite: &pb.PerfSuiteCallbackData{
				PerfPlanId:         x.Source().GetPerfSuite().GetPerfPlanId(),
				PerfPlanExecuteId:  x.Source().GetPerfSuite().GetPerfPlanExecuteId(),
				PerfSuiteId:        x.Source().GetPerfSuite().GetPerfSuiteId(),
				PerfSuiteExecuteId: x.Source().GetPerfSuite().GetPerfSuiteExecuteId(),
				SuiteState:         x.StateM().State(),
				MetaData:           x.Source().GetPerfSuite().GetMetaData(),
			},
		},
	}

	return callbackclient.CallBack(
		x.Context(),
		x.ServiceContext().DispatcherProducer,
		x.ServiceContext().Config.Name,
		x.ServiceContext().Config.DispatcherProducer.Queue,
		x.UUID(),
		cb,
	)
}

// ValidCase 有效的case
func (x *PerfSuite) ValidCase(case_ *managerpb.PerfCaseComponent) bool {
	if x.Source().GetExecuteType() == managerpb.ApiExecutionDataType_PERF_PLAN {
		// 当执行计划时，用例的状态为`enable`才算有效
		return case_.GetState() == managerpb.CommonState_CS_ENABLE
	}

	return true
}

func (x *PerfSuite) UUID() string {
	return fmt.Sprintf("%s::%s", x.Source().GetTaskId(), x.Source().GetPerfSuite().GetPerfSuiteExecuteId())
}

func (x *PerfSuite) Content() string {
	return x.logW.toJson()
}
