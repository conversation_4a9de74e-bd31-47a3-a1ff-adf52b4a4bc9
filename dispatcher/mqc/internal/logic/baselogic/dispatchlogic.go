package baselogic

import (
	"context"
	"time"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworkerv2/base"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/codes"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/mq"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/state"
	cutils "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/utils"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/common/dtctl"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/common/errcode"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/common/utils"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/mqc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/rpc/client/dispatcher"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/rpc/pb"
	managerpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type PublishHandler func(member *dtctl.DispatchTaskMember) (*dispatcher.PublishResp, error)

type DispatchLogic struct {
	*BaseLogic
	logW *DispatchLogicLogWriter

	source *pb.DistributeReq
	ctl    *dtctl.DispatchTaskControl
	total  int64
}

func NewDispatchLogic(ctx context.Context, svcCtx *svc.ServiceContext) *DispatchLogic {
	return &DispatchLogic{
		BaseLogic: NewBaseLogic(ctx, svcCtx),
	}
}

func (logic *DispatchLogic) Setup(req *pb.DistributeReq) {
	logic.BaseLogic.Setup()
	logic.source = req
	logic.logW = NewDispatchLogicLogWriter(logic.BaseLogic.LogWriter())
	logic.logW.setTriggerMode(req.GetTriggerMode().String())
	logic.logW.setTriggerRule(req.GetTriggerRule())
	logic.logW.setPriorityType(req.GetPriorityType())
}

func (logic *DispatchLogic) SetDtCtl(ctl *dtctl.DispatchTaskControl) {
	logic.ctl = ctl
}

func (logic *DispatchLogic) Source() *pb.DistributeReq {
	return logic.source
}

func (logic *DispatchLogic) ExecuteType() managerpb.ApiExecutionDataType {
	return logic.Source().GetExecuteType()
}

func (logic *DispatchLogic) ProjectId() string {
	return logic.Source().GetProjectId()
}

func (logic *DispatchLogic) ExecuteUserId() string {
	return logic.Source().GetUserId()
}

func (logic *DispatchLogic) ExecuteUserName() string {
	return logic.Source().GetUser()
}

func (logic *DispatchLogic) Debug() bool {
	return logic.Source().GetDebug()
}

func (logic *DispatchLogic) IncrTotal() {
	logic.total += 1
}

func (logic *DispatchLogic) Total() int64 {
	return logic.total
}

func (logic *DispatchLogic) LogWriter() *DispatchLogicLogWriter {
	return logic.logW
}

func (logic *DispatchLogic) Publish(members []*dtctl.DispatchTaskMember, handler PublishHandler) (err error) {
	logic.StateM().UpdateStateManager(pb.ComponentState_Started, errcode.GetErrCode(errorx.OK))

	if len(members) == 0 {
		// 空对象跳过执行
		return nil
	}

	// 记录info
	taskData := logic.Source()
	err = logic.ctl.CreateInfo(
		taskData.GetTaskId(), taskData.GetProjectId(), taskData.GetExecuteType().String(), logic.Total(),
		taskData.PriorityType,
	)
	if err != nil {
		return logic.UpdatePanicStatef(codes.TaskDBFailure, "CreateInfo: %s", err)
	}

	// 均为并发
	return logic.ParallelPublish(members, handler)
}

// ParallelPublish 并发执行
func (logic *DispatchLogic) ParallelPublish(members []*dtctl.DispatchTaskMember, handler PublishHandler) (err error) {
	// 并发 + 重试
	multiCount := 16 // 并发量
	retryCount := 3  // 重试次数
	err = utils.MultiDo(
		logic.Context(),
		members,
		func(item any) (rerr error) {
			ecode := errcode.GetErrCode(errorx.OK)

			var member *dtctl.DispatchTaskMember
			var resp *dispatcher.PublishResp

			defer func() {
				logic.logW.setMember(member, ecode)
			}()

			member, ok := item.(*dtctl.DispatchTaskMember)
			if !ok {
				ecode = errcode.GetErrCodeDebugf(codes.TaskPublishFailure, "无效的key类型: %T", item)
				return state.Debug2Error(ecode.Debug())
			}

			// 配置成员执行id
			member.ComponentExecuteId = cutils.GenExecuteId()

			// 推送消息: 异步推送
			rerr = utils.RetryDo(
				retryCount, func() error {
					var perr error
					resp, perr = handler(member)
					return perr
				},
			)
			if rerr != nil {
				ecode = errcode.GetErrCodeDebugf(codes.TaskPublishFailure, "%s", rerr)
				return state.Debug2Error(ecode.Debug())
			}

			// 创建成员
			rerr = logic.ctl.CreateMemberList(
				&dtctl.DispatchTaskMember{
					ComponentId:        member.ComponentId,
					ComponentType:      member.ComponentType,
					Version:            member.Version,
					ComponentExecuteId: resp.GetExecuteId(),
					Data:               member.Data,
				},
			)
			if rerr != nil {
				ecode = errcode.GetErrCodeDebugf(codes.TaskDBFailure, "创建成员失败: %s", rerr)
				return state.Debug2Error(ecode.Debug())
			}

			return nil
		}, multiCount,
	)
	if err != nil {
		return logic.UpdatePanicStatef(codes.TaskPublishFailure, "创建接口用例执行请求失败，具体错误报告请看子报错")
	}

	return nil
}

// AsyncPublishTask 异步推送执行任务
func (logic *DispatchLogic) AsyncPublishTask(
	in *dispatcher.PublishReq, member *dtctl.DispatchTaskMember,
) (resp *dispatcher.PublishResp, err error) {
	payload, err := protobuf.MarshalJSON(in)
	if err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.SerializationError, err.Error()),
			"任务参数序列化失败, error: %s", err,
		)
	}

	task := base.NewTask(
		constants.MQTaskTypeDispatcherPublishTask, payload,
		base.WithMaxRetryOptions(0),
		base.WithRetentionOptions(time.Second*300),
	)
	_, err = logic.ServiceContext().DispatcherTaskProducer.Send(
		logic.ctx, task, mq.ConvertPbEnumerationToQueuePriority(in.GetPriorityType()),
	)
	if err != nil {
		return nil, errors.Wrapf(errorx.Err(errorx.InternalError, err.Error()), "发送数据到mq失败, error: %s", err)
	}

	//if in.GetPriorityType() != common.PriorityType_Default {
	//	newTask := base.NewTask(
	//		constants.MQTaskTypeDispatcherPublishTask, payload,
	//		base.WithMaxRetryOptions(0),
	//		base.WithRetentionOptions(time.Second*300),
	//	)
	//	_, err = logic.ServiceContext().DispatcherTaskProducer.Send(
	//		logic.ctx, newTask, mq.ConvertPbEnumerationToQueuePriority(in.PriorityType),
	//	)
	//	if err != nil {
	//		return nil, errors.Wrapf(errorx.Err(errorx.InternalError, err.Error()), "发送数据到mq失败, error: %s", err)
	//	}
	//} else {
	//	taskSign := &tasks.Signature{
	//		UUID:       fmt.Sprintf("%s::%s", in.GetTaskId(), member.ComponentExecuteId),
	//		RoutingKey: constants.MQNameDispatcherTask,
	//		Name:       constants.MQTaskTypeDispatcherPublishTask,
	//		Args: []tasks.Arg{
	//			{Value: payload, Type: "[]byte"},
	//		},
	//	}
	//	// 发布任务
	//	_, err = logic.ServiceContext().DispatcherProducer.AsyncPush(
	//		logic.Context(), taskSign, logic.ServiceContext().Config.Name,
	//	)
	//	if err != nil {
	//		return nil, errors.Wrapf(errorx.Err(errorx.InternalError, err.Error()), "发送数据到mq失败, error: %s", err)
	//	}
	//}

	return &dispatcher.PublishResp{
		ProjectId: in.GetProjectId(),
		TaskId:    in.GetTaskId(),
		ExecuteId: member.ComponentExecuteId,
		Version:   member.Version,
	}, nil
}

func (logic *DispatchLogic) SendTaskToSelf(task *base.Task) error {
	_, err := logic.svcCtx.DispatcherProducer.Send(logic.ctx, task, base.QueuePriorityDefault)
	if err != nil {
		return errors.Wrapf(
			errorx.Err(errorx.InternalError, err.Error()),
			"failed to send %q task to self, queue: %s, payload: %s, error: %+v",
			task.Typename, task.Queue, task.Payload, err,
		)
	}

	return nil
}
