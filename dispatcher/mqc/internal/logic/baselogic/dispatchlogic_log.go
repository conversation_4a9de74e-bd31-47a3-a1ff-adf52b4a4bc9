package baselogic

import (
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/state"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/common/dtctl"
)

type DispatchLogic_Log struct {
	*BaseLogic_Log_Common

	Members      []DispatchLogic_Log_Member `json:"members"`
	TriggerMode  string                     `json:"trigger_mode"`  // 触发模式
	TriggerRule  string                     `json:"trigger_rule"`  // 触发规则
	PriorityType pb.PriorityType            `json:"priority_type"` // 优先级
}

type DispatchLogic_Log_Member struct {
	BaseLogic_Log_Common_Error
	Source *dtctl.DispatchTaskMember `json:"source"`
}

type DispatchLogicLogWriter struct {
	*BaseLogicLogWriter
	log *DispatchLogic_Log
}

func NewDispatchLogicLogWriter(bw *BaseLogicLogWriter) *DispatchLogicLogWriter {
	writer := &DispatchLogicLogWriter{
		BaseLogicLogWriter: bw,
		log: &DispatchLogic_Log{
			BaseLogic_Log_Common: bw.log,
			Members:              make([]DispatchLogic_Log_Member, 0, 10),
		},
	}
	return writer
}

func (writer *DispatchLogicLogWriter) Log() *DispatchLogic_Log {
	return writer.log
}

func (writer *DispatchLogicLogWriter) toJson() string {
	return MarshalJson(writer.log)
}

func (writer *DispatchLogicLogWriter) setTriggerMode(mode string) {
	writer.log.TriggerMode = mode
}

func (writer *DispatchLogicLogWriter) setTriggerRule(rule string) {
	writer.log.TriggerRule = rule
}

func (writer *DispatchLogicLogWriter) setPriorityType(priorityType pb.PriorityType) {
	writer.log.PriorityType = priorityType
}

func (writer *DispatchLogicLogWriter) setMember(key *dtctl.DispatchTaskMember, ecode state.IErrCode) {
	writer.DoLock(
		func() {
			writer.log.Members = append(
				writer.log.Members, DispatchLogic_Log_Member{
					BaseLogic_Log_Common_Error: GetBaseNodeLogCommonError(ecode.Code(), ecode.String(), ecode.Debug()),
					Source:                     key,
				},
			)
		},
	)
}
