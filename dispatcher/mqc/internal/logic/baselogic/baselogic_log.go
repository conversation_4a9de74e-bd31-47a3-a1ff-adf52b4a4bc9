package baselogic

import (
	"sync"

	"github.com/zeromicro/go-zero/core/jsonx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/state"
)

func MarshalJson(v any) string {
	return jsonx.MarshalToStringIgnoreError(v)
}

type BaseLogicLogWriter struct {
	lock sync.Mutex
	log  *BaseLogic_Log_Common
}

func NewBaseLogicLogWriter() *BaseLogicLogWriter {
	writer := &BaseLogicLogWriter{
		log: &BaseLogic_Log_Common{},
	}
	return writer
}

func (writer *BaseLogicLogWriter) DoLock(f func()) {
	writer.lock.Lock()
	defer writer.lock.Unlock()
	f()
}

func (writer *BaseLogicLogWriter) SetCommon(sm *state.StateManager) {
	writer.DoLock(
		func() {
			// 直接修改指针变量内的值
			c := GetBaseLogicLogCommon(sm)
			_ = utils.Copy(writer.log, &c)
		},
	)
}

type BaseLogic_Log_Common_Error struct {
	Code    errorx.Code `json:"code"`    // 错误码
	Message string      `json:"message"` // 提示信息,提供code的具体信息
	Debug   string      `json:"debug"`   // 用于展示具体的错误报告
}

func GetBaseNodeLogCommonError(code errorx.Code, msg, debug string) BaseLogic_Log_Common_Error {
	l := BaseLogic_Log_Common_Error{
		Code:    code,
		Message: msg,
		Debug:   debug,
	}
	return l
}

type BaseLogic_Log_Common struct {
	BaseLogic_Log_Common_Error
}

func GetBaseLogicLogCommon(sm *state.StateManager) BaseLogic_Log_Common {
	common := BaseLogic_Log_Common{
		BaseLogic_Log_Common_Error: GetBaseNodeLogCommonError(sm.Code(), sm.Message(), sm.Debug()),
	}

	return common
}
