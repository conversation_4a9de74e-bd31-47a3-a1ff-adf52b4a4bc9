package baselogic

import (
	"context"
	"fmt"
	"sync"

	"github.com/zeromicro/go-zero/core/logx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/state"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/common/errcode"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/mqc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/rpc/pb"
)

type BaseLogic struct {
	logx.Logger
	ctx           context.Context
	lock          sync.RWMutex // 任务颗粒度的读写锁
	tearDownFuncs []func()     // 注册释放函数
	stateM        *state.StateManager

	svcCtx *svc.ServiceContext
	logW   *BaseLogicLogWriter
}

func NewBaseLogic(ctx context.Context, svcCtx *svc.ServiceContext) *BaseLogic {
	return &BaseLogic{
		Logger:        logx.WithContext(ctx),
		ctx:           ctx,
		svcCtx:        svcCtx,
		tearDownFuncs: make([]func(), 0, 10),
	}
}

func (l *BaseLogic) Setup() {
	l.logW = NewBaseLogicLogWriter()
	l.stateM = state.NewStateManager(pb.ComponentState_Init, errcode.GetErrCode(errorx.OK))
}

func (l *BaseLogic) DoLock(f func()) {
	l.lock.Lock()
	defer l.lock.Unlock()
	f()
}

func (l *BaseLogic) DoRLock(f func()) {
	l.lock.TryLock()

	l.lock.RLock()
	defer l.lock.RUnlock()
	f()
}

func (l *BaseLogic) RegisterTeardownFunc(f func()) {
	l.tearDownFuncs = append(l.tearDownFuncs, f)
}

func (l *BaseLogic) Context() context.Context {
	return l.ctx
}

func (l *BaseLogic) ServiceContext() *svc.ServiceContext {
	return l.svcCtx
}

func (l *BaseLogic) Teardown() {
	for _, f := range l.tearDownFuncs {
		f()
	}
}

func (l *BaseLogic) LogWriter() *BaseLogicLogWriter {
	return l.logW
}

func (l *BaseLogic) StateM() *state.StateManager {
	return l.stateM
}

func (l *BaseLogic) UpdateState(state pb.ComponentState, code errorx.Code) bool {
	return l.StateM().UpdateStateManager(state, errcode.GetErrCode(code))
}

func (l *BaseLogic) UpdateStatef(state pb.ComponentState, code errorx.Code, msg string, args ...any) {
	ok := l.UpdateState(state, code)
	if ok {
		l.StateM().SetDebugf(msg, args...)
	}
}

func (l *BaseLogic) UpdateFailureStatef(code errorx.Code, msg string, args ...any) error {
	l.UpdateStatef(pb.ComponentState_Failure, code, msg, args...)
	l.Errorf(msg, args...)
	return fmt.Errorf(msg, args...)
}

func (l *BaseLogic) UpdatePanicStatef(code errorx.Code, msg string, args ...any) error {
	l.UpdateStatef(pb.ComponentState_Panic, code, msg, args...)
	l.Errorf(msg, args...)
	return fmt.Errorf(msg, args...)
}
