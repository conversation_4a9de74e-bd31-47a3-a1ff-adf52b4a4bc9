package stabilityReport

import (
	"testing"
	ttemplate "text/template"
	"time"

	"github.com/zeromicro/go-zero/core/jsonx"

	commonpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
)

func Test_getContent(t *testing.T) {
	now := time.Now()

	type args struct {
		notifyT *ttemplate.Template
		item    any
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name: "stability report template",
			args: args{
				notifyT: stabilityReportTemplate,
				item: &StabilityNotificationItem{
					PlanName:      "123",
					ModifyPlanURL: "[123](https://www.baidu.com)",
					AppVersion:    "v1.0.0",
					AppVerHerf:    "[v1.0.0](https://www.baidu.com)",
					TriggerMode:   "手动执行",
					StartedAt:     now,
					EndedAt:       now,
					Duration:      time.Hour,
					SuccessDevice: 10,
					TotalDevice:   10,
					Status: &statusItem{
						Status:   statusOfSuccess,
						StatusZH: statusTranslations[statusOfSuccess],
						Color:    statusColors[statusOfSuccess],
					},
					Executor: &commonpb.PerfUserInfo{
						Account:    "T1704",
						Fullname:   "韩子健",
						Email:      "<EMAIL>",
						LarkUserId: "ou_f6e9af0a88f68c86f6e52dae314c4649",
					},
					ExceptionDevices: jsonx.MarshalToStringIgnoreError(
						[]exceptionDevice{
							{
								Udid:       "abc1",
								Name:       "1111",
								CrashCount: 111,
								AnrCount:   111,
								Artifacts:  "11111",
							},
							{
								Udid:       "abc2",
								Name:       "1112",
								CrashCount: 112,
								AnrCount:   112,
								Artifacts:  "11122",
							},
							{
								Udid:       "abc3",
								Name:       "1113",
								CrashCount: 113,
								AnrCount:   113,
								Artifacts:  "11133",
							},
						},
					),
					ReportURL: "https://www.baidu.com", // base_url, plan_id, task_id, project_id, execute_id
					TableAttribute: &tableAttribute{
						RowHeight: _32px,
						PageSize:  5,
					},
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				got, err := templateParse(tt.args.notifyT, tt.args.item)
				if (err != nil) != tt.wantErr {
					t.Fatalf("templateParse() error = %v, wantErr %v", err, tt.wantErr)
				}

				t.Logf("content: \n%s", got)
			},
		)
	}
}
