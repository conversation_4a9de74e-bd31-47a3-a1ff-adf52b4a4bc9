package stabilityReport

const (
	htmlBreakLabel = "<br/>"

	textLinkFormat      = "[%s](%s)"
	modifyPlanURLFormat = "%s/#/stability-test/testplan/edit?project_id=%s&from_router=projectIndex&stability_plan_id=%s&mode=edit" // base_url, project_id, plan_id
	reportURLFormat     = "%s/#/stability-test/testplan/report?plan_id=%s&task_id=%s&project_id=%s&execute_id=%s"                   // base_url, plan_id, task_id, project_id, execute_id
	downloadURLFormat   = "%s/reports/stability_test/reports/%s/%s/%s"                                                              // base_url, task_id, udid, file_name
	htmlLinkFormat      = "**<link icon='download_outlined' url='%s'>%s</link>**"                                                   // download_url, log_name
)

type rowHeight string

const (
	_32px rowHeight = "32px"
	_54px rowHeight = "54px"
	_77px rowHeight = "77px"
	_99px rowHeight = "99px"
)

const (
	filenameOfCrashDumpLog = "crash-dump.log"
	filenameOfFastbotLog   = "fastbot.log"
	filenameOfOOMTracesLog = "oom-traces.log"
	filenameOfAppLogs      = "app_logs.zip"
)

type artifactZH string

const (
	filenameOfCrashDumpLogZH artifactZH = "Crash日志"
	filenameOfFastbotLogZH   artifactZH = "Fastbot日志"
	filenameOfOOMTracesLogZH artifactZH = "ANR日志"
	filenameOfAppLogsZH      artifactZH = "APP日志"
	filenameOfOtherLogsZH    artifactZH = "其他日志"
)

const (
	statusOfPending = "Pending"
	statusOfInit    = "Init"
	statusOfStarted = "Started"
	statusOfSuccess = "Success"
	statusOfWaiting = "Waiting"
	statusOfWarning = "Warning"
	statusOfSkip    = "Skip"
	statusOfFailure = "Failure"
	statusOfPanic   = "Panic"
	statusOfStop    = "Stop"
	statusOfInvalid = "Invalid"
)

type statusZH string

const (
	pending statusZH = "待处理"
	initial statusZH = "初始化中"
	started statusZH = "开始处理"
	success statusZH = "成功"
	waiting statusZH = "等待中"
	warning statusZH = "警告"
	skipped statusZH = "跳过"
	failure statusZH = "失败"
	crash   statusZH = "崩溃"
	stopped statusZH = "终止"
	invalid statusZH = "无效"
	unknown statusZH = "未知"
)

type color string

const (
	blue   color = "blue"   // 蓝色
	green  color = "green"  // 绿色
	grey   color = "grey"   // 灰色
	red    color = "red"    // 红色
	yellow color = "yellow" // 黄色
)
