package stabilityReport

import (
	"time"

	commonpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
)

type (
	statusItem struct {
		Status   string
		StatusZH statusZH
		Color    color
	}

	exceptionDevice struct {
		Udid       string `json:"udid"`
		Name       string `json:"name"`
		CrashCount int64  `json:"crash_count"`
		AnrCount   int64  `json:"anr_count"`
		Artifacts  string `json:"artifacts"`
	}

	tableAttribute struct {
		RowHeight rowHeight
		PageSize  int64
	}

	StabilityNotificationItem struct {
		PlanName         string
		ModifyPlanURL    string
		TriggerMode      commonpb.TriggerModeZH
		AppVersion       string
		AppVerHerf       string
		StartedAt        time.Time
		EndedAt          time.Time
		Duration         time.Duration
		SuccessDevice    int64
		TotalDevice      int64
		Status           *statusItem
		Executor         *commonpb.PerfUserInfo
		ExceptionDevices string
		ReportURL        string
		TableAttribute   *tableAttribute
	}
)
