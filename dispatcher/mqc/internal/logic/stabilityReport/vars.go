package stabilityReport

import (
	_ "embed"
	ttemplate "text/template"
	"time"

	"github.com/hashicorp/golang-lru/v2/expirable"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	commonpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/template"
)

var (
	//go:embed stabilityReport.template
	stabilityReportContentTemplate string

	stabilityReportTemplate = ttemplate.Must(template.Parse("stabilityReport.template", stabilityReportContentTemplate))

	statusTranslations = map[string]statusZH{
		statusOfPending: pending,
		statusOfInit:    initial,
		statusOfStarted: started,
		statusOfSuccess: success,
		statusOfWaiting: waiting,
		statusOfWarning: warning,
		statusOfSkip:    skipped,
		statusOfFailure: failure,
		statusOfPanic:   crash,
		statusOfStop:    stopped,
		statusOfInvalid: invalid,
	}

	statusColors = map[statusZH]color{
		pending: blue,
		initial: blue,
		started: blue,
		success: green,
		waiting: "",
		warning: yellow,
		skipped: "",
		failure: red,
		crash:   grey,
		stopped: grey,
		invalid: grey,
		unknown: grey,
	}

	artifactTranslations = map[string]artifactZH{
		filenameOfCrashDumpLog: filenameOfCrashDumpLogZH,
		filenameOfFastbotLog:   filenameOfFastbotLogZH,
		filenameOfOOMTracesLog: filenameOfOOMTracesLogZH,
		filenameOfAppLogs:      filenameOfAppLogsZH,
	}

	pageSizes = map[rowHeight]int64{
		_32px: 5,
		_54px: 4,
		_77px: 3,
		_99px: 2,
	}

	userInfoCache = expirable.NewLRU[string, *commonpb.PerfUserInfo](
		constants.ConstDefaultMakeMapSize, onEvict, 24*time.Hour,
	)
)
