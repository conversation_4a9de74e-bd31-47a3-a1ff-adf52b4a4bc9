package stabilityReport

import (
	"bytes"
	"context"
	"fmt"
	ttemplate "text/template"
	"time"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"
	"github.com/zeromicro/go-zero/core/lang"
	"github.com/zeromicro/go-zero/core/logx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
	notifierpb "gitlab.ttyuyin.com/TestDevelopment/qet-backend-middleware/notifier/rpc/pb"
	userpb "gitlab.ttyuyin.com/TestDevelopment/qet-backend-middleware/user/rpc/pb"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/codes"
	commonpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/mqc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/rpc/pb"
	larkproxypb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/larkproxy/rpc/pb"
	managerpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
	reporterpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
)

type StabilityReport struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewStabilityReport(ctx context.Context, svcCtx *svc.ServiceContext) *StabilityReport {
	return &StabilityReport{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (x *StabilityReport) Run(in *pb.StabilityReportCallback) error {
	defer func() {
		if r := recover(); r != nil {
			x.Errorf("processor recover result in StabilityReport.Run: %+v", r)
		}
	}()

	var (
		taskID    = in.GetTaskId()
		executeID = in.GetExecuteId()
		projectID = in.GetProjectId()
	)

	x.Infof("fetch the stability plan report, task_id: %s, execute_id: %s, project_id: %s", taskID, executeID, projectID)

	planRecord, err := x.fetchPlanRecord(taskID, executeID, projectID)
	if err != nil {
		return errors.Wrapf(
			errorx.Err(codes.TaskNotifyFailure, err.Error()),
			"failed to fetch stability plan record, task_id: %s, execute_id: %s, project_id: %s",
			taskID, executeID, projectID,
		)
	}

	x.Infof("search the stability device report, task_id: %s, execute_id: %s, project_id: %s", taskID, executeID, projectID)

	deviceRecord, err := x.searchDeviceRecord(taskID, executeID, projectID)
	if err != nil {
		return errors.Wrapf(
			errorx.Err(codes.TaskNotifyFailure, err.Error()),
			"failed to search stability device record, task_id: %s, execute_id: %s, project_id: %s",
			taskID, executeID, projectID,
		)
	}

	planID := planRecord.GetItem().GetPlanId()
	x.Infof("fetch the stability plan notify items, plan_id: %s, project_id: %s", planID, projectID)

	notifyItems, err := x.fetchPlanNotifyItems(projectID, planID)
	if err != nil {
		return errors.Wrapf(
			errorx.Err(codes.TaskNotifyFailure, err.Error()),
			"failed to fetch stability plan notify items, project_id: %s, plan_id: %s",
			projectID, planID,
		)
	}

	x.Infof("new the stability report notify content, plan_record: %+v, device_record: %+v", planRecord, deviceRecord)

	content, err := x.newNotifyContent(planRecord, deviceRecord)
	if err != nil {
		return errors.Wrapf(
			errorx.Err(codes.ParseOrRenderTemplateFailure, err.Error()),
			"failed to new the stability notification content, task_id: %s, error: %+v",
			taskID, err,
		)
	}

	x.Infof("send the stability report notify content, notify_items: %+v, content: %s", notifyItems, content)

	if _, err = x.svcCtx.NotifierRpc.Notify(
		x.ctx, &notifierpb.NotifyReq{
			Items: []*notifierpb.NotifyItem{
				makeNotifyItemsByContent(notifyItems, content),
			},
		},
	); err != nil {
		return errors.Wrapf(
			errorx.Err(codes.TaskNotifyFailure, err.Error()),
			"failed to send the stability notification by `notifier` service, task_id: %s, error: %+v",
			taskID, err,
		)
	}

	x.Info("success to send the stability report notify content!!!")

	return nil
}

func (x *StabilityReport) newNotifyContent(
	planRecord *reporterpb.GetStabilityPlanRecordResp,
	deviceRecord *reporterpb.SearchStabilityDeviceRecordResp,
) (string, error) {
	var (
		planItem     = planRecord.GetItem()
		planMetaData = planRecord.GetMetaData()

		taskID    = planItem.GetTaskId()
		executeID = planItem.GetExecuteId()
		projectID = planItem.GetProjectId()
		planID    = planItem.GetPlanId()
		planName  = planItem.GetPlanName()

		modifyPlanURL = fmt.Sprintf(modifyPlanURLFormat, x.svcCtx.Config.ProbeBaseURL, projectID, planID) // base_url, project_id, plan_id
		statusItem    = &statusItem{Status: planItem.GetStatus()}

		exceptionDevices  []exceptionDevice
		maxNumOfArtifacts int
		rowHeight         rowHeight
	)

	if zh, ok := statusTranslations[statusItem.Status]; ok {
		statusItem.StatusZH = zh
	} else {
		statusItem.StatusZH = unknown
	}
	statusItem.Color = statusColors[statusItem.StatusZH]

	for _, item := range deviceRecord.GetItems() {
		if item.GetCrashCount() > 0 || item.GetAnrCount() > 0 {
			var artifacts string
			for _, artifact := range item.GetArtifacts() {
				downloadURL := fmt.Sprintf(downloadURLFormat, x.svcCtx.Config.ProbeBaseURL, taskID, item.GetDevice().GetUdid(), artifact)
				artifactZh, ok := artifactTranslations[artifact]
				if !ok {
					artifactZh = filenameOfOtherLogsZH
				}
				htmlLink := fmt.Sprintf(htmlLinkFormat, downloadURL, artifactZh)
				if len(artifacts) == 0 {
					artifacts = htmlLink
				} else {
					artifacts += htmlBreakLabel + htmlLink
				}
			}
			if maxNumOfArtifacts < len(item.GetArtifacts()) {
				maxNumOfArtifacts = len(item.GetArtifacts())
			}
			exceptionDevices = append(exceptionDevices, exceptionDevice{
				Udid:       item.GetDevice().GetUdid(),
				Name:       item.GetDevice().GetName(),
				CrashCount: item.GetCrashCount(),
				AnrCount:   item.GetAnrCount(),
				Artifacts:  artifacts,
			})
		}
	}

	switch maxNumOfArtifacts {
	case 1:
		rowHeight = _32px
	case 2:
		rowHeight = _54px
	case 3:
		rowHeight = _77px
	default:
		rowHeight = _99px
	}

	item := &StabilityNotificationItem{
		PlanName:      planName,
		ModifyPlanURL: fmt.Sprintf(textLinkFormat, planName, modifyPlanURL),
		AppVersion:    planMetaData.GetAppVersion(),
		AppVerHerf:    fmt.Sprintf(textLinkFormat, planMetaData.GetAppVersion(), planMetaData.GetAppDownloadLink()),
		TriggerMode:   planItem.GetTriggerMode().ConvertToZH(),
		StartedAt:     time.UnixMilli(planItem.GetStartedAt()),
		EndedAt:       time.UnixMilli(planItem.GetEndedAt()),
		Duration:      time.Duration(planItem.GetCostTime()) * time.Second,
		SuccessDevice: planItem.GetSuccessDevice(),
		TotalDevice:   planItem.GetTotalDevice(),
		Status:        statusItem,
		Executor:      x.fetchUserInfoByAccount(planItem.GetExecutedBy()),
		ReportURL:     fmt.Sprintf(reportURLFormat, x.svcCtx.Config.ProbeBaseURL, planID, taskID, projectID, executeID), // base_url, plan_id, task_id, project_id, execute_id
		TableAttribute: &tableAttribute{
			RowHeight: rowHeight,
			PageSize:  pageSizes[rowHeight],
		},
	}

	if len(exceptionDevices) > 0 {
		item.ExceptionDevices = jsonx.MarshalToStringIgnoreError(exceptionDevices)
	}

	x.Infof("success to new the stability notification item: %+v", item)

	return templateParse(stabilityReportTemplate, item)
}

func templateParse(t *ttemplate.Template, item any) (string, error) {
	buf := new(bytes.Buffer)
	defer buf.Reset()

	if err := t.Execute(buf, item); err != nil {
		return "", err
	}

	return buf.String(), nil
}

func makeNotifyItemsByContent(items []*managerpb.NotifyItem, content string) *notifierpb.NotifyItem {
	receivers := make([]*notifierpb.LarkReceiver, 0, len(items))
	for _, item := range items {
		if item.GetNotifyType() != managerpb.NotifyType_LARK_CHAT {
			continue
		}

		receivers = append(
			receivers, &notifierpb.LarkReceiver{
				Id:   item.GetReceiver(),
				Type: notifierpb.LarkReceiveIdType_CHAT_ID,
			},
		)
	}

	return &notifierpb.NotifyItem{
		Item: &notifierpb.NotifyItem_LarkCustomApp{
			LarkCustomApp: &notifierpb.LarkCustomApp{
				Receivers: receivers,
				MsgType:   notifierpb.LarkMessageType_INTERACTIVE,
				Content:   content,
			},
		},
	}
}

func (x *StabilityReport) fetchPlanRecord(taskID, executeID, projectID string) (
	*reporterpb.GetStabilityPlanRecordResp, error,
) {
	record, err := x.svcCtx.StabilityReporterRpc.GetStabilityPlanRecord(
		x.ctx, &reporterpb.GetStabilityPlanRecordReq{
			TaskId:    taskID,
			ExecuteId: executeID,
			ProjectId: projectID,
		})
	if err != nil {
		return nil, err
	}
	return record, nil
}

func (x *StabilityReport) searchDeviceRecord(taskID, executeID, projectID string) (
	*reporterpb.SearchStabilityDeviceRecordResp, error,
) {
	record, err := x.svcCtx.StabilityReporterRpc.SearchStabilityDeviceRecord(
		x.ctx, &reporterpb.SearchStabilityDeviceRecordReq{
			TaskId:    taskID,
			ExecuteId: executeID,
			ProjectId: projectID,
		})
	if err != nil {
		return nil, err
	}
	return record, nil
}

func (x *StabilityReport) fetchPlanNotifyItems(projectID, planID string) ([]*managerpb.NotifyItem, error) {
	out, err := x.svcCtx.ManagerRpc.GetPlanNotify(
		x.ctx, &managerpb.GetPlanNotifyReq{
			ProjectId: projectID,
			PlanId:    planID,
		},
	)
	if err != nil {
		return nil, err
	}

	cache := make(map[string]lang.PlaceholderType, len(out.GetItems()))
	items := make([]*managerpb.NotifyItem, 0, len(out.GetItems()))
	for _, item := range out.GetItems() {
		if item.GetNotifyType() != managerpb.NotifyType_LARK_CHAT {
			x.Warnf(
				"stability notification does not support this notify type, item: %s",
				protobuf.MarshalJSONIgnoreError(item),
			)
			continue
		}

		if _, ok := cache[item.GetReceiver()]; !ok {
			cache[item.GetReceiver()] = lang.Placeholder
			items = append(items, item)
		}
	}

	return items, nil
}

func onEvict(key string, value *commonpb.PerfUserInfo) {
	logx.Debugf("evict from lru, key: %s, value: %s", key, protobuf.MarshalJSONIgnoreError(value))
}

func (x *StabilityReport) fetchUserInfoByAccount(account string) *commonpb.PerfUserInfo {
	user, ok := userInfoCache.Get(account)
	if ok && user.GetLarkUserId() != "" {
		return user
	}

	defer func() {
		if user != nil && user.GetLarkUserId() != "" {
			userInfoCache.Add(account, user)
		}
	}()

	user = &commonpb.PerfUserInfo{
		Account: account,
	}

	out1, err := x.svcCtx.UserRpc.GetUser(
		x.ctx, &userpb.GetUserReq{
			Account: account,
		},
	)
	if err != nil {
		return user
	}

	user.Fullname = out1.GetUserInfo().GetFullname()
	user.Email = out1.GetUserInfo().GetEmail()

	out2, err := x.svcCtx.LarkProxyRpc.GetBatchUserID(
		x.ctx, &larkproxypb.GetBatchUserIDReq{
			Emails: []string{out1.GetUserInfo().GetEmail()},
		},
	)
	if err != nil || len(out2.GetItems()) == 0 {
		return user
	}

	user.LarkUserId = out2.GetItems()[0].GetUserId()
	return user
}
