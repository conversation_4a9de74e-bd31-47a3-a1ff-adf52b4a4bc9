package perfreport

import (
	"time"

	commonpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
)

type (
	baseNotificationItem struct {
		PlanName      string
		ModifyPlanURL string
		Protocol      commonpb.ProtocolZH
		TargetEnv     commonpb.TargetEnvironmentZH
		TriggerMode   commonpb.TriggerModeZH
		PerfTaskType  commonpb.PerfTaskTypeZH
		StartedAt     time.Time
		Duration      time.Duration
		Executor      *commonpb.PerfUserInfo
	}
	previewNotificationItem struct {
		baseNotificationItem

		Developers   []*commonpb.PerfUserInfo
		Maintainers  []*commonpb.PerfUserInfo
		Cases        string
		CasesWithCmd bool
	}
	startedNotificationItem struct {
		baseNotificationItem

		Developers   []*commonpb.PerfUserInfo
		Maintainers  []*commonpb.PerfUserInfo
		Cases        string
		CasesWithCmd bool
		Services     string
		RecordsURL   string
	}
	finishedNotificationItem struct {
		baseNotificationItem

		EndedAt      time.Time
		Status       *statusItem
		Cases        string
		CasesWithCmd bool
		Services     string
		Monitors     string
		ReportURL    string
	}
	advancedNotificationItem struct {
		baseNotificationItem

		Developers   []*commonpb.PerfUserInfo
		Maintainers  []*commonpb.PerfUserInfo
		Cases        string
		CasesWithCmd bool
	}
	statusItem struct {
		Status   string
		StatusZH statusZH
		Color    color
		Detail   *stopDetail
	}
	stopDetail struct {
		StopType   stopTypeZH
		Reason     string
		MetricType string
		Service    string
		Namespace  string
		Method     string
		Threshold  string
		ReachedAt  time.Time
		LatestAt   time.Time
	}
	caseItem struct {
		SuiteName string `json:"suite_name"`
		CaseName  string `json:"case_name"`
		TargetRps int64  `json:"target_rps"`
		Cmds      string `json:"cmds,omitempty"`
	}
	serviceItem struct {
		TelemetryURL string `json:"telemetry_url"`
	}
	monitorItem struct {
		MonitorURL string                    `json:"monitor_url"`
		Type       commonpb.MonitorUrlTypeZH `json:"type"`
	}
)
