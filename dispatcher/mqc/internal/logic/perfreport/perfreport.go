package perfreport

import (
	"bytes"
	"context"
	"fmt"
	"strconv"
	"strings"
	ttemplate "text/template"
	"time"

	"github.com/gorhill/cronexpr"
	"github.com/pkg/errors"
	"github.com/redis/go-redis/v9"
	"github.com/zeromicro/go-zero/core/jsonx"
	"github.com/zeromicro/go-zero/core/lang"
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/trace"
	"github.com/zyedidia/generic"
	"github.com/zyedidia/generic/set"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
	notifierpb "gitlab.ttyuyin.com/TestDevelopment/qet-backend-middleware/notifier/rpc/pb"
	userpb "gitlab.ttyuyin.com/TestDevelopment/qet-backend-middleware/user/rpc/pb"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/codes"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	commonpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/thirdparty/appInsight"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/common/utils"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/mqc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/rpc/pb"
	larkproxypb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/larkproxy/rpc/pb"
	managerpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
	reporterpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
)

type PerfReport struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewPerfReport(ctx context.Context, svcCtx *svc.ServiceContext) *PerfReport {
	return &PerfReport{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (x *PerfReport) saveCallbackData(data *pb.PerfReportCallback) {
	var (
		taskID = data.GetTaskId()
		key    = fmt.Sprintf("%s:%s", common.ConstCachePerfPlanNotificationCallbackDataTaskIDPrefix, taskID)
	)

	// save the callback data into cache
	value := protobuf.MarshalJSONToStringIgnoreError(data)
	result, err := x.svcCtx.Redis.SetNX(x.ctx, key, value, 3*time.Hour).Result()
	if err != nil {
		if !errors.Is(err, redis.Nil) {
			x.Errorf("failed to save the callback data into cache, key: %s, value: %s, error: %+v", key, value, err)
		}

		return
	}

	x.Infof("save the callback data into cache successfully, key: %s, value: %s, result: %t", key, value, result)
}

func (x *PerfReport) getCallbackData(taskID string) *pb.PerfReportCallback {
	var (
		data pb.PerfReportCallback

		key = fmt.Sprintf("%s:%s", common.ConstCachePerfPlanNotificationCallbackDataTaskIDPrefix, taskID)
	)

	// try to get the callback data from cache
	result, err := x.svcCtx.Redis.Get(x.ctx, key).Result()
	if err != nil {
		if !errors.Is(err, redis.Nil) {
			x.Errorf("failed to get the callback data from cache, key: %s, error: %+v", key, err)
		}

		return nil
	}

	x.Infof("get the callback data from cache successfully, key: %s, result: %s", key, result)
	if err = protobuf.UnmarshalJSONFromString(result, &data); err != nil {
		x.Errorf("failed to unmarshal the callback data from cache, data: %s, error: %+v", result, err)
		return nil
	}

	return &data
}

func (x *PerfReport) removeCallbackData(taskID string) {
	key := fmt.Sprintf("%s:%s", common.ConstCachePerfPlanNotificationCallbackDataTaskIDPrefix, taskID)

	// remove the callback data from cache
	result, err := x.svcCtx.Redis.Del(x.ctx, key).Result()
	if err != nil {
		if !errors.Is(err, redis.Nil) {
			x.Errorf("failed to remove the callback data from cache, key: %s, error: %+v", key, err)
		}

		return
	}

	x.Infof("remove the callback data from cache successfully, key: %s, result: %d", key, result)
}

func (x *PerfReport) getPerfPlan(projectID, planID string) (*managerpb.PerfPlanV2, error) {
	out, err := x.svcCtx.ManagerRpc.ViewPerfPlan(
		x.ctx, &managerpb.ViewPerfPlanV2Req{
			ProjectId: projectID,
			PlanId:    planID,
		},
	)
	if err != nil {
		return nil, err
	}

	return out.GetPlan(), nil
}

func (x *PerfReport) getPlanNotifyItems(projectID, planID string) ([]*managerpb.NotifyItem, error) {
	out, err := x.svcCtx.ManagerRpc.GetPlanNotify(
		x.ctx, &managerpb.GetPlanNotifyReq{
			ProjectId: projectID,
			PlanId:    planID,
		},
	)
	if err != nil {
		return nil, err
	}

	cache := make(map[string]lang.PlaceholderType, len(out.GetItems()))
	items := make([]*managerpb.NotifyItem, 0, len(out.GetItems()))
	for _, item := range out.GetItems() {
		if item.GetNotifyType() != managerpb.NotifyType_LARK_CHAT {
			x.Warnf(
				"perf notification does not support this notify type, item: %s", protobuf.MarshalJSONIgnoreError(item),
			)
			continue
		}

		if _, ok := cache[item.GetReceiver()]; !ok {
			cache[item.GetReceiver()] = lang.Placeholder
			items = append(items, item)
		}
	}

	return items, nil
}

func (x *PerfReport) getPerfPlanRecord(taskID, executeID, projectID string) (*reporterpb.PerfPlanRecord, error) {
	record, err := x.svcCtx.PerfReporterRpc.GetPerfPlanRecord(
		x.ctx, &reporterpb.GetPerfPlanRecordReq{
			TaskId:    taskID,
			ExecuteId: executeID,
			ProjectId: projectID,
		},
	)
	if err != nil {
		return nil, err
	}

	return record.GetRecord(), nil
}

func (x *PerfReport) getUserInfoByAccount(account string) *commonpb.PerfUserInfo {
	user, ok := userInfoCache.Get(account)
	if ok && user.GetLarkUserId() != "" {
		return user
	}

	defer func() {
		if user != nil && user.GetLarkUserId() != "" {
			userInfoCache.Add(account, user)
		}
	}()

	user = &commonpb.PerfUserInfo{
		Account: account,
	}

	out1, err := x.svcCtx.UserRpc.GetUser(
		x.ctx, &userpb.GetUserReq{
			Account: account,
		},
	)
	if err != nil {
		return user
	}

	user.Fullname = out1.GetUserInfo().GetFullname()
	user.Email = out1.GetUserInfo().GetEmail()

	out2, err := x.svcCtx.LarkProxyRpc.GetBatchUserID(
		x.ctx, &larkproxypb.GetBatchUserIDReq{
			Emails: []string{out1.GetUserInfo().GetEmail()},
		},
	)
	if err != nil || len(out2.GetItems()) == 0 {
		return user
	}

	user.LarkUserId = out2.GetItems()[0].GetUserId()
	return user
}

func (x *PerfReport) getUserLeaderByAccount(account string) *commonpb.PerfUserInfo {
	out1, err := x.svcCtx.UserRpc.GetUserLeader(
		x.ctx, &userpb.GetUserLeaderReq{
			Account: account,
		},
	)
	if err != nil {
		return nil
	}

	leader, ok := userInfoCache.Get(out1.GetUserInfo().GetAccount())
	if ok && leader.GetLarkUserId() != "" {
		return leader
	}

	defer func() {
		if leader != nil && leader.GetLarkUserId() != "" {
			userInfoCache.Add(leader.GetAccount(), leader)
		}
	}()

	leader = &commonpb.PerfUserInfo{
		Account:  out1.GetUserInfo().GetAccount(),
		Fullname: out1.GetUserInfo().GetFullname(),
		Email:    out1.GetUserInfo().GetEmail(),
	}

	out2, err := x.svcCtx.LarkProxyRpc.GetBatchUserID(
		x.ctx, &larkproxypb.GetBatchUserIDReq{
			Emails: []string{leader.GetEmail()},
		},
	)
	if err != nil || len(out2.GetItems()) == 0 {
		return leader
	}

	leader.LarkUserId = out2.GetItems()[0].GetUserId()
	return leader
}

func (x *PerfReport) getLeaderLarkIdsFromDevelopers(developers []*commonpb.PerfUserInfo) []string {
	larkUserIDSet := set.NewHashset[string](
		constants.ConstDefaultMakeSliceSize, generic.Equals[string], generic.HashString,
	)

	for _, developer := range developers {
		leader := x.getUserLeaderByAccount(developer.GetAccount())
		if leader != nil && leader.GetLarkUserId() != "" {
			larkUserIDSet.Put(leader.GetLarkUserId())
		}
	}

	return larkUserIDSet.Keys()
}

func (x *PerfReport) getOthersFromPerfLarkMember(projectId string) []string {
	larkUserIDSet := set.NewHashset[string](
		constants.ConstDefaultMakeSliceSize, generic.Equals[string], generic.HashString,
	)

	resp, err := x.svcCtx.ManagerRpc.SearchPerfLarkMember(
		x.ctx, &managerpb.SearchPerfLarkMemberReq{ProjectId: projectId},
	)
	if err != nil {
		return larkUserIDSet.Keys()
	}

	for _, item := range resp.GetItems() {
		if item != nil && item.GetLarkId() != "" {
			larkUserIDSet.Put(item.GetLarkId())
		}
	}

	return larkUserIDSet.Keys()
}

func (x *PerfReport) createLarkChat(taskID, chatName string, members []string) (string, error) {
	out, err := x.svcCtx.LarkProxyRpc.CreateChat(
		x.ctx, &larkproxypb.CreateChatReq{
			Uuid:        trace.TraceIDFromContext(x.ctx),
			Name:        chatName,
			Description: "",
			UserIdList:  members,
		},
	)
	if err != nil {
		return "", err
	}

	x.Debugf("create lark chat, task_id: %s, resp: %s", taskID, protobuf.MarshalJSONIgnoreError(out))

	chatID := out.GetData().GetChatId()
	if chatID == "" {
		return "", errorx.Errorf(
			codes.TaskNotifyFailure,
			"got an empty chat id from the response of create lark chat, task_id: %s",
			taskID,
		)
	}

	return chatID, nil
}

func (x *PerfReport) updatePerfPlan(projectID, planID, chatID string) error {
	_, err := x.svcCtx.ManagerRpc.UpdatePerfPlanByChatID(
		x.ctx, &managerpb.UpdatePerfPlanByChatIDReq{
			ProjectId: projectID,
			PlanId:    planID,
			ChatId:    chatID,
		},
	)
	return err
}

func (x *PerfReport) addMembersToLarkChat(taskID, chatID string, members []string) error {
	out, err := x.svcCtx.LarkProxyRpc.CreateChatMembers(
		x.ctx, &larkproxypb.CreateChatMembersReq{
			ChatId:     chatID,
			UserIdList: members,
		},
	)
	if err != nil {
		return err
	}

	x.Debugf(
		"create lark chat members, task_id: %s, chat_id: %s, members: %s, resp: %s",
		taskID, chatID, jsonx.MarshalIgnoreError(members), protobuf.MarshalJSONIgnoreError(out),
	)
	return nil
}

func (x *PerfReport) generateStatusItem(taskID, status string) *statusItem {
	item := &statusItem{
		Status: status,
	}
	if zh, ok := statusTranslations[item.Status]; ok {
		item.StatusZH = zh
	} else {
		item.StatusZH = unknown
	}
	item.Color = statusColors[item.StatusZH]

	if strings.EqualFold(item.Status, statusOfStop) {
		metadata, err := utils.GetStopMetadata(x.ctx, x.svcCtx.Redis, taskID)
		if err != nil {
			x.Errorf("failed to get stop metadata, task_id: %s, error: %+v", taskID, err)
			return item
		}

		item.Detail = &stopDetail{}
		switch metadata.GetStopType() {
		case pb.StopType_StopType_Manual:
			item.Detail.StopType = manual
		case pb.StopType_StopType_Auto:
			item.Detail.StopType = automatic
		default:
		}

		if metadata.GetReason() != "" {
			item.Detail.Reason = metadata.GetReason()
		}
		if metadata.GetRule() != nil {
			rule := metadata.GetRule()
			item.Detail.MetricType = rule.GetMetricType()
			item.Detail.Service = rule.GetService()
			item.Detail.Namespace = rule.GetNamespace()
			item.Detail.Method = rule.GetMethod()
			item.Detail.ReachedAt = time.UnixMilli(rule.GetReachedAt())
			item.Detail.LatestAt = time.UnixMilli(rule.GetLatestAt())

			threshold := strconv.FormatFloat(rule.GetThreshold(), 'f', 2, 64)
			switch rule.GetMetricType() {
			case string(appInsight.MetricTypeOfFailRatio):
				item.Detail.Threshold = threshold + "%"
			case string(appInsight.MetricTypeOfP99), string(appInsight.MetricTypeOfP95),
				string(appInsight.MetricTypeOfP90), string(appInsight.MetricTypeOfP75),
				string(appInsight.MetricTypeOfP50):
				item.Detail.Threshold = threshold + "ms"
			default:
				item.Detail.Threshold = threshold
			}
		}
	}

	return item
}

func (x *PerfReport) generateCaseItems(cases []*pb.PerfReportCallback_PerfCase) ([]*caseItem, bool) {
	var (
		items   = make([]*caseItem, 0, len(cases))
		withCmd bool
	)

	for _, v := range cases {
		cmds := ""
		for _, cmd := range v.GetCmds() {
			cmds += fmt.Sprintf("%d,", cmd)
		}
		if len(cmds) > 0 {
			cmds = cmds[:len(cmds)-1]
			withCmd = true
		}

		items = append(
			items, &caseItem{
				SuiteName: v.GetSuiteName(),
				CaseName:  v.GetCaseName(),
				TargetRps: v.GetTargetRps(),
				Cmds:      cmds,
			},
		)
	}

	if withCmd {
		for _, item := range items {
			if item.Cmds == "" {
				item.Cmds = "-"
			}
		}
	}

	return items, withCmd
}

func (x *PerfReport) generateServiceItems(
	targetEnv commonpb.TargetEnvironment, services []*commonpb.PerfServiceMetaData, timestamps ...int64,
) []*serviceItem {
	var (
		startedAt, endedAt int64

		items = make([]*serviceItem, 0, len(services))
		count = len(timestamps)
	)

	if count == 0 {
		startedAt = time.Now().UnixMilli()
	} else if count == 1 {
		startedAt = timestamps[0]
	} else {
		startedAt = timestamps[0]
		endedAt = timestamps[1]
	}

	for _, service := range services {
		if service.GetName() == "" || service.GetNamespace() == "" {
			continue
		}

		name := service.GetName() + "." + service.GetNamespace()
		if targetEnv == commonpb.TargetEnvironment_TE_CANARY ||
			targetEnv == commonpb.TargetEnvironment_TE_STAGING {
			name = service.GetName() + "-" + stagingServiceSuffix + "." + service.GetNamespace()
		}

		var url string
		if endedAt == 0 {
			url = fmt.Sprintf(
				telemetryURLWithoutEndTSFormat, x.svcCtx.Config.AppInsightBaseURL, name, startedAt,
			)
		} else {
			url = fmt.Sprintf(
				telemetryURLWithTimeRangeFormat, x.svcCtx.Config.AppInsightBaseURL, name, startedAt, endedAt,
			)
		}

		items = append(
			items, &serviceItem{
				TelemetryURL: fmt.Sprintf(textLinkFormat, name, url),
			},
		)
	}

	return items
}

func (x *PerfReport) generateMonitorItems(monitors []*reporterpb.MonitorUrl, startedAt, endedAt int64) []*monitorItem {
	items := make([]*monitorItem, 0, len(monitors))
	for _, monitor := range monitors {
		if monitor.GetName() == "" || monitor.GetUrl() == "" {
			continue
		}

		var url string
		switch monitor.GetType() {
		case commonpb.MonitorUrlType_MUT_GRAFANA:
			url = fmt.Sprintf("%s&from=%d&to=%d", monitor.GetUrl(), startedAt, endedAt)
		case commonpb.MonitorUrlType_MUT_APP_INSIGHT:
			url = fmt.Sprintf("%s&start_ts=%d&end_ts=%d", monitor.GetUrl(), startedAt, endedAt)
		default:
			continue
		}

		items = append(
			items, &monitorItem{
				MonitorURL: fmt.Sprintf(textLinkFormat, monitor.GetName(), url),
				Type:       monitor.GetType().ConvertToZH(),
			},
		)
	}

	return items
}

func (x *PerfReport) Run(in *pb.PerfReportCallback) error {
	var (
		taskID    = in.GetTaskId()
		executeID = in.GetPlanExecuteId()
		projectID = in.GetProjectId()
		planID    = in.GetPlanId()
		stage     = in.GetStage()

		members                 []string
		developers, maintainers []*commonpb.PerfUserInfo
		creator, executor       *commonpb.PerfUserInfo
		record                  *reporterpb.PerfPlanRecord
	)

	perfPlan, err := x.getPerfPlan(projectID, planID)
	if err != nil {
		return errors.Wrapf(
			errorx.Err(codes.TaskNotifyFailure, err.Error()),
			"failed to get perf plan, project_id: %s, plan_id: %s",
			projectID, planID,
		)
	}

	if stage != pb.StageType_ST_ADVANCED {
		key := fmt.Sprintf(
			"%s:%s:%s", common.ConstLockPerfPlanNotificationTaskIDStagePrefix, taskID, protobuf.GetEnumStringOf(stage),
		)
		ok, err := x.svcCtx.Redis.SetNX(x.ctx, key, planID, 10*time.Minute).Result()
		if err != nil {
			x.Errorf("failed to set the perf plan notification key, key: %s, value: %s, error: %+v", key, planID, err)
			return err
		} else if !ok {
			x.Infof("the task of sending the perf notification has been handled, no need to do it again, key: %s", key)
			return nil
		}

		defer func() {
			switch stage {
			case pb.StageType_ST_PREVIEW:
				x.saveCallbackData(in)
			case pb.StageType_ST_FINISHED:
				x.removeCallbackData(taskID)
			}
		}()

		record, err = x.getPerfPlanRecord(taskID, executeID, projectID)
		if err != nil {
			return errors.Wrapf(
				errorx.Err(codes.TaskNotifyFailure, err.Error()),
				"failed to get perf plan execution record, task_id: %s, execute_id: %s, project_id: %s",
				taskID, executeID, projectID,
			)
		}

		creator = x.getUserInfoByAccount(perfPlan.GetCreatedBy())
		executor = x.getUserInfoByAccount(record.GetExecutedBy())
		developers, maintainers, members = getPerfUserInfoFromServices(record.GetServices())
	} else {
		creator = x.getUserInfoByAccount(perfPlan.GetCreatedBy())
		executor = x.getUserInfoByAccount(perfPlan.GetMaintainedBy())
		developers, maintainers, members = getPerfUserInfoFromServices(in.GetServices())
	}

	creatorID := creator.GetLarkUserId()
	// leaders := x.getLeaderLarkIdsFromDevelopers(developers)
	others := x.getOthersFromPerfLarkMember(projectID)
	members = distinct(members, others, []string{creatorID, executor.GetLarkUserId()})
	membersStr := jsonx.MarshalIgnoreError(members)

	var items []*managerpb.NotifyItem
	if perfPlan.GetCreateLarkChat() {
		x.Debugf("members to be added to the lark chat, task_id: %s, members: %s", taskID, membersStr)

		var chatID string
		if perfPlan.GetLarkChatId() == "" {
			// create lark chat and add managers to lark char
			chatID, err = x.createLarkChatAndSetManagers(taskID, perfPlan.GetName(), creatorID, members)
			if err != nil {
				return err
			}
			x.Infof(
				"create lark chat and set managers successfully, task_id: %s, project_id: %s, chat_id: %s, manager: %s, members: %s",
				taskID, projectID, chatID, creatorID, membersStr,
			)

			// update perf plan by chat id
			if err = x.updatePerfPlan(projectID, planID, chatID); err != nil {
				x.Errorf(
					"failed to update perf plan by chat id, task_id: %s, project_id: %s, plan_id: %s, chat_id: %s, error: %+v",
					taskID, projectID, planID, chatID, err,
				)
			}
		} else {
			// add members to lark chat
			chatID = perfPlan.GetLarkChatId()
			if err = x.addMembersToLarkChat(taskID, chatID, members); err != nil {
				x.Errorf(
					"failed to add members to lark chat, task_id: %s, chat_id: %s, members: %s, error: %+v",
					taskID, chatID, membersStr, err,
				)
			}
		}

		if chatID != "" {
			items = []*managerpb.NotifyItem{
				{
					NotifyMode: managerpb.NotifyMode_ALWAYS_NOTIFY,
					NotifyType: managerpb.NotifyType_LARK_CHAT,
					Receiver:   chatID,
				},
			}
		}
	} else {
		items, err = x.getPlanNotifyItems(projectID, planID)
		if err != nil {
			return errors.Wrapf(
				errorx.Err(codes.TaskNotifyFailure, err.Error()),
				"failed to get perf plan notify items, project_id: %s, plan_id: %s",
				projectID, planID,
			)
		}
	}

	if len(items) == 0 {
		x.Warnf(
			"no need to send notification cause by not found any notify items, project_id: %s, plan_id: %s",
			projectID, planID,
		)
		return nil
	}

	if stage != pb.StageType_ST_PREVIEW && stage != pb.StageType_ST_ADVANCED {
		if data := x.getCallbackData(taskID); data != nil && len(in.GetCases()) == 0 {
			in.Cases = data.GetCases()
		}
	}

	switch stage {
	case pb.StageType_ST_PREVIEW:
		return x.sendPreviewNotification(in, record, items, developers, maintainers)
	case pb.StageType_ST_STARTED:
		return x.sendStartedNotification(in, record, items, developers, maintainers)
	case pb.StageType_ST_FINISHED:
		return x.sendFinishedNotification(in, record, items, developers, maintainers)
	case pb.StageType_ST_ADVANCED:
		return x.sendAdvancedNotification(in, perfPlan, items, developers, maintainers)
	default:
		return errorx.Errorf(
			errorx.ValidateParamError, "invalid stage type, task_id: %s, stage: %s", taskID, stage,
		)
	}
}

func (x *PerfReport) sendPreviewNotification(
	in *pb.PerfReportCallback, record *reporterpb.PerfPlanRecord, items []*managerpb.NotifyItem,
	developers, maintainers []*commonpb.PerfUserInfo,
) error {
	taskID := in.GetTaskId()

	content, err := x.getPreviewContent(in, record, developers, maintainers)
	if err != nil {
		return errors.Wrapf(
			errorx.Err(codes.ParseOrRenderTemplateFailure, err.Error()),
			"failed to get the preview notification content, task_id: %s, error: %+v",
			taskID, err,
		)
	}

	if _, err = x.svcCtx.NotifierRpc.Notify(
		x.ctx, &notifierpb.NotifyReq{
			Items: []*notifierpb.NotifyItem{
				makeNotifyItemsByContent(items, content),
			},
		},
	); err != nil {
		return errors.Wrapf(
			errorx.Err(codes.TaskNotifyFailure, err.Error()),
			"failed to send the preview notification by `notifier` service, task_id: %s, error: %+v",
			taskID, err,
		)
	}

	return nil
}

func (x *PerfReport) getPreviewContent(
	in *pb.PerfReportCallback, record *reporterpb.PerfPlanRecord, developers, maintainers []*commonpb.PerfUserInfo,
) (string, error) {
	var (
		projectID = in.GetProjectId()
		planID    = in.GetPlanId()

		planName      = record.GetPlanName()
		modifyPlanURL = fmt.Sprintf(modifyPlanURLFormat, x.svcCtx.Config.ProbeBaseURL, projectID, planID)
	)

	item := &previewNotificationItem{
		baseNotificationItem: baseNotificationItem{
			PlanName:      planName,
			ModifyPlanURL: fmt.Sprintf(textLinkFormat, planName, modifyPlanURL),
			Protocol:      record.GetProtocol().ConvertToZH(),
			TargetEnv:     record.GetTargetEnv().ConvertToZH(),
			TriggerMode:   record.GetTriggerMode().ConvertToZH(),
			PerfTaskType:  record.GetTaskType().ConvertToZH(),
			StartedAt:     time.UnixMilli(record.GetStartedAt()),
			Duration:      time.Duration(record.GetTargetDuration()) * time.Second,
			Executor:      x.getUserInfoByAccount(record.GetExecutedBy()),
		},
	}

	if len(developers) > 0 {
		item.Developers = developers
	}
	if len(maintainers) > 0 {
		item.Maintainers = maintainers
	}
	if cases, withCmd := x.generateCaseItems(in.GetCases()); len(cases) > 0 {
		item.Cases = jsonx.MarshalToStringIgnoreError(cases)
		item.CasesWithCmd = withCmd
	}

	return getContent(previewTemplate, item)
}

func (x *PerfReport) sendStartedNotification(
	in *pb.PerfReportCallback, record *reporterpb.PerfPlanRecord, items []*managerpb.NotifyItem,
	developers, maintainers []*commonpb.PerfUserInfo,
) error {
	taskID := in.GetTaskId()

	content, err := x.getStartedContent(in, record, developers, maintainers)
	if err != nil {
		return errors.Wrapf(
			errorx.Err(codes.ParseOrRenderTemplateFailure, err.Error()),
			"failed to get the started notification content, task_id: %s, error: %+v",
			taskID, err,
		)
	}

	if _, err = x.svcCtx.NotifierRpc.Notify(
		x.ctx, &notifierpb.NotifyReq{
			Items: []*notifierpb.NotifyItem{
				makeNotifyItemsByContent(items, content),
			},
		},
	); err != nil {
		return errors.Wrapf(
			errorx.Err(codes.TaskNotifyFailure, err.Error()),
			"failed to send the started notification by `notifier` service, task_id: %s, error: %+v",
			taskID, err,
		)
	}

	return nil
}

func (x *PerfReport) getStartedContent(
	in *pb.PerfReportCallback, record *reporterpb.PerfPlanRecord, developers, maintainers []*commonpb.PerfUserInfo,
) (string, error) {
	var (
		projectID = in.GetProjectId()
		planID    = in.GetPlanId()

		planName      = record.GetPlanName()
		modifyPlanURL = fmt.Sprintf(modifyPlanURLFormat, x.svcCtx.Config.ProbeBaseURL, projectID, planID)
		targetEnv     = record.GetTargetEnv()
		startedAt     = record.GetStartedAt()
		duration      = time.Duration(record.GetTargetDuration()) * time.Second
	)

	item := &startedNotificationItem{
		baseNotificationItem: baseNotificationItem{
			PlanName:      planName,
			ModifyPlanURL: fmt.Sprintf(textLinkFormat, planName, modifyPlanURL),
			Protocol:      record.GetProtocol().ConvertToZH(),
			TargetEnv:     targetEnv.ConvertToZH(),
			TriggerMode:   record.GetTriggerMode().ConvertToZH(),
			PerfTaskType:  record.GetTaskType().ConvertToZH(),
			StartedAt:     time.UnixMilli(startedAt),
			Duration:      duration,
			Executor:      x.getUserInfoByAccount(record.GetExecutedBy()),
		},
		RecordsURL: fmt.Sprintf(recordsURLFormat, x.svcCtx.Config.ProbeBaseURL, projectID, planID),
	}

	if len(developers) > 0 {
		item.Developers = developers
	}
	if len(maintainers) > 0 {
		item.Maintainers = maintainers
	}
	if cases, withCmd := x.generateCaseItems(in.GetCases()); len(cases) > 0 {
		item.Cases = jsonx.MarshalToStringIgnoreError(cases)
		item.CasesWithCmd = withCmd
	}

	if targetEnv == commonpb.TargetEnvironment_TE_PRODUCTION ||
		targetEnv == commonpb.TargetEnvironment_TE_CANARY ||
		targetEnv == commonpb.TargetEnvironment_TE_STAGING {
		if services := x.generateServiceItems(targetEnv, record.GetServices(), startedAt); len(services) > 0 {
			item.Services = jsonx.MarshalToStringIgnoreError(services)
		}
	}

	return getContent(startedTemplate, item)
}

func (x *PerfReport) sendFinishedNotification(
	in *pb.PerfReportCallback, record *reporterpb.PerfPlanRecord, items []*managerpb.NotifyItem,
	developers, maintainers []*commonpb.PerfUserInfo,
) error {
	taskID := in.GetTaskId()

	content, err := x.getFinishedContent(in, record, developers, maintainers)
	if err != nil {
		return errors.Wrapf(
			errorx.Err(codes.ParseOrRenderTemplateFailure, err.Error()),
			"failed to get the finished notification content, task_id: %s, error: %+v",
			taskID, err,
		)
	}

	if _, err = x.svcCtx.NotifierRpc.Notify(
		x.ctx, &notifierpb.NotifyReq{
			Items: []*notifierpb.NotifyItem{
				makeNotifyItemsByContent(items, content),
			},
		},
	); err != nil {
		return errors.Wrapf(
			errorx.Err(codes.TaskNotifyFailure, err.Error()),
			"failed to send the finished notification by `notifier` service, task_id: %s, error: %+v",
			taskID, err,
		)
	}

	return nil
}

func (x *PerfReport) getFinishedContent(
	in *pb.PerfReportCallback, record *reporterpb.PerfPlanRecord, _, _ []*commonpb.PerfUserInfo,
) (string, error) {
	var (
		taskID    = in.GetTaskId()
		executeID = in.GetPlanExecuteId()
		projectID = in.GetProjectId()
		planID    = in.GetPlanId()

		planName      = record.GetPlanName()
		modifyPlanURL = fmt.Sprintf(modifyPlanURLFormat, x.svcCtx.Config.ProbeBaseURL, projectID, planID)
		targetEnv     = record.GetTargetEnv()
		startedAt     = record.GetStartedAt()
		endedAt       = record.GetEndedAt()
	)

	item := &finishedNotificationItem{
		baseNotificationItem: baseNotificationItem{
			PlanName:      planName,
			ModifyPlanURL: fmt.Sprintf(textLinkFormat, planName, modifyPlanURL),
			Protocol:      record.GetProtocol().ConvertToZH(),
			TargetEnv:     targetEnv.ConvertToZH(),
			TriggerMode:   record.GetTriggerMode().ConvertToZH(),
			PerfTaskType:  record.GetTaskType().ConvertToZH(),
			StartedAt:     time.UnixMilli(startedAt),
			Duration:      time.Duration(record.GetCostTime()) * time.Millisecond,
			Executor:      x.getUserInfoByAccount(record.GetExecutedBy()),
		},
		EndedAt:   time.UnixMilli(endedAt),
		Status:    x.generateStatusItem(taskID, record.GetStatus()),
		ReportURL: fmt.Sprintf(reportURLFormat, x.svcCtx.Config.ProbeBaseURL, taskID, executeID, projectID),
	}

	if cases, withCmd := x.generateCaseItems(in.GetCases()); len(cases) > 0 {
		item.Cases = jsonx.MarshalToStringIgnoreError(cases)
		item.CasesWithCmd = withCmd
	}

	if targetEnv == commonpb.TargetEnvironment_TE_PRODUCTION ||
		targetEnv == commonpb.TargetEnvironment_TE_CANARY ||
		targetEnv == commonpb.TargetEnvironment_TE_STAGING {
		if services := x.generateServiceItems(targetEnv, record.GetServices(), startedAt, endedAt); len(services) > 0 {
			item.Services = jsonx.MarshalToStringIgnoreError(services)
		}

		if monitors := x.generateMonitorItems(record.GetMonitorUrls(), startedAt, endedAt); len(monitors) > 0 {
			item.Monitors = jsonx.MarshalToStringIgnoreError(monitors)
		}
	}

	return getContent(finishedTemplate, item)
}

func (x *PerfReport) sendAdvancedNotification(
	in *pb.PerfReportCallback, perfPlan *managerpb.PerfPlanV2, items []*managerpb.NotifyItem,
	developers, maintainers []*commonpb.PerfUserInfo,
) error {
	planID := in.GetPlanId()

	content, err := x.getAdvancedContent(in, perfPlan, developers, maintainers)
	if err != nil {
		return errors.Wrapf(
			errorx.Err(codes.ParseOrRenderTemplateFailure, err.Error()),
			"failed to get the advanced notification content, plan_id: %s, error: %+v",
			planID, err,
		)
	}

	if _, err = x.svcCtx.NotifierRpc.Notify(
		x.ctx, &notifierpb.NotifyReq{
			Items: []*notifierpb.NotifyItem{
				makeNotifyItemsByContent(items, content),
			},
		},
	); err != nil {
		return errors.Wrapf(
			errorx.Err(codes.TaskNotifyFailure, err.Error()),
			"failed to send the advanced notification by `notifier` service, plan_id: %s, error: %+v",
			planID, err,
		)
	}

	return nil
}

func (x *PerfReport) getAdvancedContent(
	in *pb.PerfReportCallback, perfPlan *managerpb.PerfPlanV2, developers, maintainers []*commonpb.PerfUserInfo,
) (string, error) {
	var (
		projectID = in.GetProjectId()
		planID    = in.GetPlanId()

		planName      = perfPlan.GetName()
		cronSpec      = perfPlan.GetCronExpression()
		modifyPlanURL = fmt.Sprintf(modifyPlanURLFormat, x.svcCtx.Config.ProbeBaseURL, projectID, planID)
	)

	schedule, err := cronexpr.Parse(cronSpec)
	if err != nil {
		return "", errors.Wrapf(
			errorx.Err(errorx.CronExpressionParseError, err.Error()),
			"failed to parse the cron expression, cron: %s, project_id: %s, plan_id: %s, error: %+v",
			cronSpec, projectID, planID, err,
		)
	}

	item := &advancedNotificationItem{
		baseNotificationItem: baseNotificationItem{
			PlanName:      planName,
			ModifyPlanURL: fmt.Sprintf(textLinkFormat, planName, modifyPlanURL),
			Protocol:      perfPlan.GetProtocol().ConvertToZH(),
			TargetEnv:     perfPlan.GetTargetEnv().ConvertToZH(),
			PerfTaskType:  commonpb.RunZH,
			StartedAt:     schedule.Next(time.Now()),
			Duration:      time.Duration(perfPlan.GetDuration()) * time.Second,
			Executor:      x.getUserInfoByAccount(perfPlan.GetMaintainedBy()),
		},
	}

	if len(developers) > 0 {
		item.Developers = developers
	}
	if len(maintainers) > 0 {
		item.Maintainers = maintainers
	}
	if cases, withCmd := x.generateCaseItems(in.GetCases()); len(cases) > 0 {
		item.Cases = jsonx.MarshalToStringIgnoreError(cases)
		item.CasesWithCmd = withCmd
	}

	return getContent(advancedTemplate, item)
}

func getPerfUserInfoFromServices(services []*commonpb.PerfServiceMetaData) (
	developers, maintainers []*commonpb.PerfUserInfo, larkUserIDs []string,
) {
	developerSet := set.NewHashset[*commonpb.PerfUserInfo](
		constants.ConstDefaultMakeSliceSize,
		commonpb.PerfServiceMetaDataUserInfoEquals,
		commonpb.PerfServiceMetaDataUserInfoHash,
	)
	maintainerSet := set.NewHashset[*commonpb.PerfUserInfo](
		constants.ConstDefaultMakeSliceSize,
		commonpb.PerfServiceMetaDataUserInfoEquals,
		commonpb.PerfServiceMetaDataUserInfoHash,
	)
	larkUserIDSet := set.NewHashset[string](
		constants.ConstDefaultMakeSliceSize, generic.Equals[string], generic.HashString,
	)

	for _, service := range services {
		for _, developer := range service.GetDevelopers() {
			if developer.GetAccount() != "" && developer.GetLarkUserId() != "" {
				userInfoCache.Add(
					developer.GetAccount(), &commonpb.PerfUserInfo{
						Account:    developer.GetAccount(),
						Fullname:   developer.GetFullname(),
						Email:      developer.GetEmail(),
						LarkUserId: developer.GetLarkUserId(),
					},
				)
			}
			if developer.GetLarkUserId() != "" {
				developerSet.Put(developer)
				larkUserIDSet.Put(developer.GetLarkUserId())
			}
		}
		for _, maintainer := range service.GetMaintainers() {
			if maintainer.GetAccount() != "" && maintainer.GetLarkUserId() != "" {
				userInfoCache.Add(
					maintainer.GetAccount(), &commonpb.PerfUserInfo{
						Account:    maintainer.GetAccount(),
						Fullname:   maintainer.GetFullname(),
						Email:      maintainer.GetEmail(),
						LarkUserId: maintainer.GetLarkUserId(),
					},
				)
			}
			if maintainer.GetLarkUserId() != "" {
				maintainerSet.Put(maintainer)
				larkUserIDSet.Put(maintainer.GetLarkUserId())
			}
		}
		for _, databaseAdministrator := range service.GetDatabaseAdministrators() {
			if databaseAdministrator.GetAccount() != "" && databaseAdministrator.GetLarkUserId() != "" {
				userInfoCache.Add(
					databaseAdministrator.GetAccount(), &commonpb.PerfUserInfo{
						Account:    databaseAdministrator.GetAccount(),
						Fullname:   databaseAdministrator.GetFullname(),
						Email:      databaseAdministrator.GetEmail(),
						LarkUserId: databaseAdministrator.GetLarkUserId(),
					},
				)
			}
			if databaseAdministrator.GetLarkUserId() != "" {
				larkUserIDSet.Put(databaseAdministrator.GetLarkUserId())
			}
		}
	}

	return developerSet.Keys(), maintainerSet.Keys(), larkUserIDSet.Keys()
}

func getContent(notifyT *ttemplate.Template, item any) (string, error) {
	buf := new(bytes.Buffer)
	defer buf.Reset()

	if err := notifyT.Execute(buf, item); err != nil {
		return "", err
	}

	return buf.String(), nil
}

func makeNotifyItemsByContent(items []*managerpb.NotifyItem, content string) *notifierpb.NotifyItem {
	receivers := make([]*notifierpb.LarkReceiver, 0, len(items))
	for _, item := range items {
		if item.GetNotifyType() != managerpb.NotifyType_LARK_CHAT {
			continue
		}

		receivers = append(
			receivers, &notifierpb.LarkReceiver{
				Id:   item.GetReceiver(),
				Type: notifierpb.LarkReceiveIdType_CHAT_ID,
			},
		)
	}

	return &notifierpb.NotifyItem{
		Item: &notifierpb.NotifyItem_LarkCustomApp{
			LarkCustomApp: &notifierpb.LarkCustomApp{
				Receivers: receivers,
				MsgType:   notifierpb.LarkMessageType_INTERACTIVE,
				Content:   content,
			},
		},
	}
}

func onEvict(key string, value *commonpb.PerfUserInfo) {
	logx.Debugf("evict from lru, key: %s, value: %s", key, protobuf.MarshalJSONIgnoreError(value))
}

func distinct(members ...[]string) []string {
	larkUserIDSet := set.NewHashset[string](
		constants.ConstDefaultMakeSliceSize, generic.Equals[string], generic.HashString,
	)

	for _, member := range members {
		for _, m := range member {
			larkUserIDSet.Put(m)
		}
	}

	return larkUserIDSet.Keys()
}

func (x *PerfReport) createLarkChatAndSetManagers(taskID, name, creatorID string, members []string) (
	chatID string, err error,
) {
	// create lark chat
	chatID, err = x.createLarkChat(taskID, name, members)
	if err != nil {
		return chatID, err
	}

	// add managers to lark chat
	if e := x.addManagersToLarkChat(taskID, chatID, []string{creatorID}); e != nil {
		x.Errorf(
			"failed to add managers to lark chat, task_id: %s, chat_id: %s, managers: %s, error: %+v",
			taskID, chatID, creatorID, e,
		)
	}

	return chatID, nil
}

func (x *PerfReport) addManagersToLarkChat(taskID, chatID string, managers []string) error {
	out, err := x.svcCtx.LarkProxyRpc.CreateChatManagers(
		x.ctx, &larkproxypb.CreateChatManagersReq{
			ChatId:        chatID,
			ManagerIdList: managers,
		},
	)
	if err != nil {
		return err
	}

	x.Debugf(
		"create lark chat managers, task_id: %s, chat_id: %s, managers: %s, resp: %s",
		taskID, chatID, jsonx.MarshalIgnoreError(managers), protobuf.MarshalJSONIgnoreError(out),
	)
	return nil
}
