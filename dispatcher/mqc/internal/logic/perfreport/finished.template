{
    "config": {
        "update_multi": true
    },
    "i18n_elements": {
        "zh_cn": [
            {
                "tag": "column_set",
                "flex_mode": "none",
                "horizontal_spacing": "16px",
                "horizontal_align": "left",
                "columns": [
                    {
                        "tag": "column",
                        "width": "weighted",
                        "vertical_align": "top",
                        "vertical_spacing": "8px",
                        "elements": [
                            {
                                "tag": "markdown",
                                "content": "**计划名称：**{{ if .ModifyPlanURL }}{{ .ModifyPlanURL }}{{ else }}{{ .PlanName }}{{ end }}",
                                "text_align": "left",
                                "text_size": "normal",
                                "icon": {
                                    "tag": "standard_icon",
                                    "token": "livestream-content_outlined",
                                    "color": "grey"
                                }
                            },
                            {
                                "tag": "markdown",
                                "content": "**使用协议：**{{ .Protocol }}",
                                "text_align": "left",
                                "text_size": "normal",
                                "icon": {
                                    "tag": "standard_icon",
                                    "token": "language_outlined",
                                    "color": "grey"
                                }
                            },
                            {
                                "tag": "markdown",
                                "content": "**目标环境：**{{ .TargetEnv }}",
                                "text_align": "left",
                                "text_size": "normal",
                                "icon": {
                                    "tag": "standard_icon",
                                    "token": "cloud_outlined",
                                    "color": "grey"
                                }
                            },
                            {
                                "tag": "markdown",
                                "content": "**触发模式：**{{ .TriggerMode }}",
                                "text_align": "left",
                                "text_size": "normal",
                                "icon": {
                                    "tag": "standard_icon",
                                    "token": "cursor_outlined",
                                    "color": "grey"
                                }
                            },
                            {
                                "tag": "markdown",
                                "content": "**任务类型：**{{ .PerfTaskType }}",
                                "text_align": "left",
                                "text_size": "normal",
                                "icon": {
                                    "tag": "standard_icon",
                                    "token": "sent_outlined",
                                    "color": "grey"
                                }
                            },
                            {
                                "tag": "markdown",
                                "content": "**开始时间：**{{ .StartedAt.Format "2006-01-02 15:04:05" }}",
                                "text_align": "left",
                                "text_size": "normal",
                                "icon": {
                                    "tag": "standard_icon",
                                    "token": "calendar-date_outlined",
                                    "color": "grey"
                                }
                            },
                            {
                                "tag": "markdown",
                                "content": "**结束时间：**{{ .EndedAt.Format "2006-01-02 15:04:05" }}",
                                "text_align": "left",
                                "text_size": "normal",
                                "icon": {
                                    "tag": "standard_icon",
                                    "token": "calendar-date_outlined",
                                    "color": "grey"
                                }
                            },
                            {
                                "tag": "markdown",
                                "content": "**执行时长：**{{ .Duration }}",
                                "text_align": "left",
                                "text_size": "normal",
                                "icon": {
                                    "tag": "standard_icon",
                                    "token": "time_outlined",
                                    "color": "grey"
                                }
                            },
                            {
                                "tag": "markdown",
                                "content": "**状态：**<font color='{{ .Status.Color }}'>{{ .Status.StatusZH }}</font>{{ if and .Status.Detail .Status.Detail.StopType }}（{{ .Status.Detail.StopType }}）{{ end }}",
                                "text_align": "left",
                                "text_size": "normal",
                                "icon": {
                                    "tag": "standard_icon",
                                    "token": "status-vacation_outlined",
                                    "color": "grey"
                                }
                            },
                            {{- if and .Status.Detail .Status.Detail.Reason }}
                            {
                                "tag": "column_set",
                                "horizontal_spacing": "0px",
                                "horizontal_align": "left",
                                "columns": [
                                    {
                                        "tag": "column",
                                        "width": "24px",
                                        "elements": [],
                                        "padding": "0px 0px 0px 0px",
                                        "vertical_align": "top",
                                        "vertical_spacing": "0px"
                                    },
                                    {
                                        "tag": "column",
                                        "width": "auto",
                                        "elements": [
                                            {
                                                "tag": "note",
                                                "elements": [
                                                    {
                                                        "tag": "plain_text",
                                                        "content": "停止原因：{{ .Status.Detail.Reason }}\n指标类型：{{ .Status.Detail.MetricType }}\n服务名称：{{ .Status.Detail.Service }}\n命名空间：{{ .Status.Detail.Namespace }}\n接口名称：{{ .Status.Detail.Method }}\n规则阀值：{{ .Status.Detail.Threshold }}\n满足时间：{{ .Status.Detail.ReachedAt.Format "2006-01-02 15:04:05" }} ~ {{ .Status.Detail.LatestAt.Format "2006-01-02 15:04:05" }}"
                                                    }
                                                ]
                                            }
                                        ],
                                        "padding": "0px 0px 0px 0px",
                                        "vertical_align": "top",
                                        "vertical_spacing": "0px"
                                    }
                                ],
                                "margin": "0px 0px 0px 0px"
                            },
                            {{- end }}
                            {
                                "tag": "markdown",
                                "content": "**操作人：**{{ if .Executor.LarkUserId }}<person id = '{{ .Executor.LarkUserId }}' show_name = true show_avatar = true style = 'normal'></person>{{ else if .Executor.Fullname }}{{ .Executor.Fullname }}{{ else }}{{ .Executor.Account }}{{ end }}",
                                "text_align": "left",
                                "text_size": "normal",
                                "icon": {
                                    "tag": "standard_icon",
                                    "token": "member_outlined",
                                    "color": "grey"
                                }
                            }
                        ],
                        "weight": 1
                    }
                ]
            },
            {{- if .Cases }}
            {
                "tag": "hr"
            },
            {
                "tag": "markdown",
                "content": "**涉及的用例**",
                "text_align": "left",
                "text_size": "normal",
                "icon": {
                    "tag": "standard_icon",
                    "token": "table-group_outlined",
                    "color": "grey"
                }
            },
            {
                "tag": "table",
                "columns": [
                    {
                        "data_type": "text",
                        "name": "suite_name",
                        "display_name": "场景名称",
                        "horizontal_align": "left",
                        "width": "auto"
                    },
                    {
                        "data_type": "text",
                        "name": "case_name",
                        "display_name": "用例名称",
                        "horizontal_align": "left",
                        "width": "auto"
                    },
                    {{- if .CasesWithCmd }}
                    {
                        "data_type": "text",
                        "name": "cmds",
                        "display_name": "涉及的命令号",
                        "horizontal_align": "left",
                        "width": "auto"
                    },
                    {{- end }}
                    {
                        "data_type": "number",
                        "name": "target_rps",
                        "display_name": "目标RPS",
                        "horizontal_align": "right",
                        "width": "auto",
                        "format": {
                            "precision": 0
                        }
                    }
                ],
                "rows": {{ .Cases }},
                "row_height": "low",
                "header_style": {
                    "background_style": "none",
                    "bold": true,
                    "lines": 1
                },
                "page_size": 10
            },
            {{- end }}
            {{- if .Services }}
            {
                "tag": "hr"
            },
            {
                "tag": "markdown",
                "content": "**涉及的服务**",
                "text_align": "left",
                "text_size": "normal",
                "icon": {
                    "tag": "standard_icon",
                    "token": "table-group_outlined",
                    "color": "grey"
                }
            },
            {
                "tag": "table",
                "columns": [
                    {
                        "data_type": "markdown",
                        "name": "telemetry_url",
                        "display_name": "服务",
                        "horizontal_align": "left",
                        "width": "auto"
                    }
                ],
                "rows": {{ .Services }},
                "row_height": "low",
                "header_style": {
                    "background_style": "none",
                    "bold": true,
                    "lines": 1
                },
                "page_size": 10
            },
            {{- end }}
            {{- if .Monitors }}
            {
                "tag": "hr"
            },
            {
                "tag": "markdown",
                "content": "**监控地址**",
                "text_align": "left",
                "text_size": "normal",
                "icon": {
                    "tag": "standard_icon",
                    "token": "alarm_outlined",
                    "color": "grey"
                }
            },
            {
                "tag": "table",
                "columns": [
                    {
                        "data_type": "markdown",
                        "name": "monitor_url",
                        "display_name": "名称",
                        "horizontal_align": "left",
                        "width": "auto"
                    },
                    {
                        "data_type": "text",
                        "name": "type",
                        "display_name": "类型",
                        "horizontal_align": "left",
                        "width": "auto"
                    }
                ],
                "rows": {{ .Monitors }},
                "row_height": "low",
                "header_style": {
                    "background_style": "none",
                    "bold": true,
                    "lines": 1
                },
                "page_size": 10
            },
            {{- end }}
            {
                "tag": "hr"
            },
            {
                "tag": "action",
                "actions": [
                    {
                        "tag": "button",
                        "text": {
                            "tag": "plain_text",
                            "content": "压测报告"
                        },
                        "type": "primary",
                        "width": "default",
                        "size": "small",
                        "behaviors": [
                            {
                                "type": "open_url",
                                "default_url": "{{ .ReportURL }}",
                                "pc_url": "",
                                "ios_url": "",
                                "android_url": ""
                            }
                        ]
                    }
                ]
            }
        ]
    },
    "i18n_header": {
        "zh_cn": {
            "title": {
                "tag": "plain_text",
                "content": "【压测报告】"
            },
            "subtitle": {
                "tag": "plain_text",
                "content": ""
            },
            "template": "{{ .Status.Color }}",
            "ud_icon": {
                "tag": "standard_icon",
                "token": "livestream-ing_filled"
            }
        }
    }
}
