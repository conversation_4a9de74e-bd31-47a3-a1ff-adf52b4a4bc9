package perfreport

import (
	"testing"
	ttemplate "text/template"
	"time"

	"github.com/zeromicro/go-zero/core/jsonx"

	commonpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
)

func Test_getContent(t *testing.T) {
	now := time.Now()

	type args struct {
		notifyT *ttemplate.Template
		item    any
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name: "preview without develops, maintainers, cases",
			args: args{
				notifyT: previewTemplate,
				item: &previewNotificationItem{
					baseNotificationItem: baseNotificationItem{
						PlanName:     "年度盛典-【正式压测】",
						StartedAt:    now.Add(time.Minute),
						Duration:     120 * time.Second,
						TriggerMode:  commonpb.ManualZH,
						PerfTaskType: commonpb.RunZH,
						Executor: &commonpb.PerfUserInfo{
							Account:    "T1704",
							Fullname:   "韩子健",
							Email:      "<EMAIL>",
							LarkUserId: "ou_f6e9af0a88f68c86f6e52dae314c4649",
						},
					},
				},
			},
			wantErr: false,
		},
		{
			name: "preview without maintainers, cases",
			args: args{
				notifyT: previewTemplate,
				item: &previewNotificationItem{
					baseNotificationItem: baseNotificationItem{
						PlanName:     "年度盛典-【正式压测】",
						StartedAt:    now.Add(time.Minute),
						Duration:     120 * time.Second,
						TriggerMode:  commonpb.ManualZH,
						PerfTaskType: commonpb.RunZH,
						Executor: &commonpb.PerfUserInfo{
							Account:    "T1704",
							Fullname:   "韩子健",
							Email:      "<EMAIL>",
							LarkUserId: "ou_f6e9af0a88f68c86f6e52dae314c4649",
						},
					},
					Developers: []*commonpb.PerfUserInfo{
						{
							Account:  "T1",
							Fullname: "张三",
							Email:    "<EMAIL>",
						},
						{
							Account:  "T2",
							Fullname: "李四",
							Email:    "<EMAIL>",
						},
						{
							Account:    "T1985",
							Fullname:   "王子沛",
							Email:      "<EMAIL>",
							LarkUserId: "ou_06b1be4cfbd252784f62e5a2199c62fa",
						},
					},
				},
			},
			wantErr: false,
		},
		{
			name: "preview with cases",
			args: args{
				notifyT: previewTemplate,
				item: &previewNotificationItem{
					baseNotificationItem: baseNotificationItem{
						PlanName:     "年度盛典-【正式压测】",
						StartedAt:    now.Add(time.Minute),
						Duration:     120 * time.Second,
						TriggerMode:  commonpb.ManualZH,
						PerfTaskType: commonpb.RunZH,
						Executor: &commonpb.PerfUserInfo{
							Account:    "T1704",
							Fullname:   "韩子健",
							Email:      "<EMAIL>",
							LarkUserId: "ou_f6e9af0a88f68c86f6e52dae314c4649",
						},
					},
					Developers: []*commonpb.PerfUserInfo{
						{
							Account:  "T1",
							Fullname: "张三",
							Email:    "<EMAIL>",
						},
						{
							Account:  "T2",
							Fullname: "李四",
							Email:    "<EMAIL>",
						},
						{
							Account:    "T1985",
							Fullname:   "王子沛",
							Email:      "<EMAIL>",
							LarkUserId: "ou_06b1be4cfbd252784f62e5a2199c62fa",
						},
					},
					Cases: jsonx.MarshalToStringIgnoreError(
						[]*caseItem{
							{
								SuiteName: "K测试",
								CaseName:  "K测试用例",
								TargetRps: 100,
								Cmds:      "345,5678,8901",
							},
							{
								SuiteName: "channel_revenue",
								CaseName:  "426 - 房间榜单（房间顶部的在线榜）",
								TargetRps: 120,
							},
						},
					),
					CasesWithCmd: true,
				},
			},
			wantErr: false,
		},
		{
			name: "started with cases",
			args: args{
				notifyT: startedTemplate,
				item: &startedNotificationItem{
					baseNotificationItem: baseNotificationItem{
						PlanName:      "K测试计划",
						ModifyPlanURL: "https://testing-dev-quality.ttyuyin.com/#/project/pressure-test/pressure-testing-plan/pressure-testing-plan-edit?project_id=project_id:Kqllt5-9fA-I5UOdhjA5d&plan_id=perf_plan_id:OjeYCrheOUVGhjv5U2BwM",
						Protocol:      commonpb.TTProtocolZH,
						TargetEnv:     commonpb.ProductionZH,
						TriggerMode:   commonpb.ManualZH,
						PerfTaskType:  commonpb.RunZH,
						StartedAt:     now.Add(-30 * time.Minute),
						Executor: &commonpb.PerfUserInfo{
							Account:    "T1704",
							Fullname:   "韩子健",
							Email:      "<EMAIL>",
							LarkUserId: "ou_f6e9af0a88f68c86f6e52dae314c4649",
						},
					},
					Developers: []*commonpb.PerfUserInfo{
						{
							Account:  "T1",
							Fullname: "张三",
							Email:    "<EMAIL>",
						},
						{
							Account:  "T2",
							Fullname: "李四",
							Email:    "<EMAIL>",
						},
						{
							Account:    "T1985",
							Fullname:   "王子沛",
							Email:      "<EMAIL>",
							LarkUserId: "ou_06b1be4cfbd252784f62e5a2199c62fa",
						},
					},
					Cases: jsonx.MarshalToStringIgnoreError(
						[]*caseItem{
							{
								SuiteName: "K测试",
								CaseName:  "K测试用例",
								TargetRps: 100,
								Cmds:      "345,5678,8901",
							},
							{
								SuiteName: "channel_revenue",
								CaseName:  "426 - 房间榜单（房间顶部的在线榜）",
								TargetRps: 120,
							},
						},
					),
					CasesWithCmd: true,
				},
			},
			wantErr: false,
		},
		{
			name: "finish with stop detail",
			args: args{
				notifyT: finishedTemplate,
				item: &finishedNotificationItem{
					baseNotificationItem: baseNotificationItem{
						PlanName:      "K测试计划",
						ModifyPlanURL: "https://testing-dev-quality.ttyuyin.com/#/project/pressure-test/pressure-testing-plan/pressure-testing-plan-edit?project_id=project_id:Kqllt5-9fA-I5UOdhjA5d&plan_id=perf_plan_id:OjeYCrheOUVGhjv5U2BwM",
						Protocol:      commonpb.TTProtocolZH,
						TargetEnv:     commonpb.ProductionZH,
						TriggerMode:   commonpb.ManualZH,
						PerfTaskType:  commonpb.RunZH,
						StartedAt:     now.Add(-30 * time.Minute),
						Duration:      3*time.Minute + 45325*time.Millisecond,
						Executor: &commonpb.PerfUserInfo{
							Account:    "T1704",
							Fullname:   "韩子健",
							Email:      "<EMAIL>",
							LarkUserId: "ou_f6e9af0a88f68c86f6e52dae314c4649",
						},
					},
					EndedAt: now.Add(-30*time.Minute + 3*time.Minute + 45325*time.Millisecond),
					Status: &statusItem{
						Status:   statusOfStop,
						StatusZH: stopped,
						Color:    grey,
						Detail: &stopDetail{
							StopType:   automatic,
							Reason:     "满足压测停止规则",
							MetricType: "p95",
							Service:    "channel-rank-logic",
							Namespace:  "quicksilver",
							Method:     "ga.api.channel_rank.ChannelRankLogic.ChannelGetMemberList",
							ReachedAt:  now.Add(-28 * time.Minute),
							LatestAt:   now.Add(-28*time.Minute + 1*time.Minute + 30*time.Second),
						},
					},
					Services:  "",
					Monitors:  "",
					ReportURL: "https://testing-dev-quality.ttyuyin.com/#/project/pressure-test/pressure-test-report/index?project_id=project_id:Kqllt5-9fA-I5UOdhjA5d&execute_id=execute_id:efH9-NXA8_f-4RYy7zGn5&task_id=task_id:_pItu3DA5sgINJr-ct-t4",
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				got, err := getContent(tt.args.notifyT, tt.args.item)
				if (err != nil) != tt.wantErr {
					t.Fatalf("getContent() error = %v, wantErr %v", err, tt.wantErr)
				}

				t.Logf("content: \n%s", got)
			},
		)
	}
}
