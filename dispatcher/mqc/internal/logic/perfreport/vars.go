package perfreport

import (
	_ "embed"
	ttemplate "text/template"
	"time"

	"github.com/hashicorp/golang-lru/v2/expirable"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	commonpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/template"
)

var (
	//go:embed preview.template
	previewContentTemplate string
	//go:embed started.template
	startedContentTemplate string
	//go:embed finished.template
	finishedContentTemplate string
	//go:embed advanced.template
	advancedContentTemplate string

	previewTemplate  = ttemplate.Must(template.Parse("preview.template", previewContentTemplate))
	startedTemplate  = ttemplate.Must(template.Parse("started.template", startedContentTemplate))
	finishedTemplate = ttemplate.Must(template.Parse("finished.template", finishedContentTemplate))
	advancedTemplate = ttemplate.Must(template.Parse("advanced.template", advancedContentTemplate))

	statusTranslations = map[string]statusZH{
		statusOfPending: pending,
		statusOfInit:    initial,
		statusOfStarted: started,
		statusOfSuccess: success,
		statusOfWaiting: waiting,
		statusOfWarning: warning,
		statusOfSkip:    skipped,
		statusOfFailure: failure,
		statusOfPanic:   crash,
		statusOfStop:    stopped,
		statusOfInvalid: invalid,
	}
	statusColors = map[statusZH]color{
		pending: blue,
		initial: blue,
		started: blue,
		success: green,
		waiting: "",
		warning: yellow,
		skipped: "",
		failure: red,
		crash:   grey,
		stopped: grey,
		invalid: grey,
	}

	userInfoCache = expirable.NewLRU[string, *commonpb.PerfUserInfo](
		constants.ConstDefaultMakeMapSize, onEvict, 24*time.Hour,
	)
)
