package perfreport

const (
	textLinkFormat                  = "[%s](%s)"
	telemetryURLWithoutEndTSFormat  = "%s/#/application-dashboard?name=%s&cluster=&start_ts=%d&tabIndex=2"                                          // base_url, service, namespace, started_at
	telemetryURLWithTimeRangeFormat = "%s/#/application-dashboard?name=%s&cluster=&start_ts=%d&end_ts=%d&tabIndex=2"                                // base_url, service, namespace, started_at, ended_at
	modifyPlanURLFormat             = "%s/#/project/pressure-test/pressure-testing-plan/pressure-testing-plan-edit?project_id=%s&plan_id=%s"        // base_url, project_id, plan_id
	recordsURLFormat                = "%s/#/project/pressure-test/pressure-test-operation-record?permissions-type=Project&project_id=%s&plan_id=%s" // base_url, project_id, plan_id
	reportURLFormat                 = "%s/#/project/pressure-test/pressure-test-report/index?task_id=%s&execute_id=%s&project_id=%s"                // base_url, task_id, execute_id, project_id

	stagingServiceSuffix = "staging"
)

const (
	statusOfPending = "Pending"
	statusOfInit    = "Init"
	statusOfStarted = "Started"
	statusOfSuccess = "Success"
	statusOfWaiting = "Waiting"
	statusOfWarning = "Warning"
	statusOfSkip    = "Skip"
	statusOfFailure = "Failure"
	statusOfPanic   = "Panic"
	statusOfStop    = "Stop"
	statusOfInvalid = "Invalid"
)

type statusZH string

const (
	pending statusZH = "待处理"
	initial statusZH = "初始化中"
	started statusZH = "开始处理"
	success statusZH = "成功"
	waiting statusZH = "等待中"
	warning statusZH = "警告"
	skipped statusZH = "跳过"
	failure statusZH = "失败"
	crash   statusZH = "崩溃"
	stopped statusZH = "终止"
	invalid statusZH = "无效"
	unknown statusZH = "未知"
)

type color string

const (
	blue   color = "blue"   // 蓝色
	green  color = "green"  // 绿色
	grey   color = "grey"   // 灰色
	red    color = "red"    // 红色
	yellow color = "yellow" // 黄色
)

type stopTypeZH string

const (
	manual    stopTypeZH = "手动停止"
	automatic stopTypeZH = "自动停止"
)
