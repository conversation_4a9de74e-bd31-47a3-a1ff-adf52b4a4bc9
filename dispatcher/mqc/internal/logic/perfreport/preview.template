{
    "config": {
        "update_multi": true
    },
    "i18n_elements": {
        "zh_cn": [
            {
                "tag": "column_set",
                "flex_mode": "none",
                "horizontal_spacing": "16px",
                "horizontal_align": "left",
                "columns": [
                    {
                        "tag": "column",
                        "width": "weighted",
                        "vertical_align": "top",
                        "vertical_spacing": "8px",
                        "elements": [
                            {
                                "tag": "markdown",
                                "content": "**计划名称：**{{ if .ModifyPlanURL }}{{ .ModifyPlanURL }}{{ else }}{{ .PlanName }}{{ end }}",
                                "text_align": "left",
                                "text_size": "normal",
                                "icon": {
                                    "tag": "standard_icon",
                                    "token": "livestream-content_outlined",
                                    "color": "grey"
                                }
                            },
                            {
                                "tag": "markdown",
                                "content": "**使用协议：**{{ .Protocol }}",
                                "text_align": "left",
                                "text_size": "normal",
                                "icon": {
                                    "tag": "standard_icon",
                                    "token": "language_outlined",
                                    "color": "grey"
                                }
                            },
                            {
                                "tag": "markdown",
                                "content": "**目标环境：**{{ .TargetEnv }}",
                                "text_align": "left",
                                "text_size": "normal",
                                "icon": {
                                    "tag": "standard_icon",
                                    "token": "cloud_outlined",
                                    "color": "grey"
                                }
                            },
                            {
                                "tag": "markdown",
                                "content": "**触发模式：**{{ .TriggerMode }}",
                                "text_align": "left",
                                "text_size": "normal",
                                "icon": {
                                    "tag": "standard_icon",
                                    "token": "cursor_outlined",
                                    "color": "grey"
                                }
                            },
                            {
                                "tag": "markdown",
                                "content": "**任务类型：**{{ .PerfTaskType }}",
                                "text_align": "left",
                                "text_size": "normal",
                                "icon": {
                                    "tag": "standard_icon",
                                    "token": "sent_outlined",
                                    "color": "grey"
                                }
                            },
                            {
                                "tag": "markdown",
                                "content": "**开始时间：**{{ .StartedAt.Format "2006-01-02 15:04:05" }}",
                                "text_align": "left",
                                "text_size": "normal",
                                "icon": {
                                    "tag": "standard_icon",
                                    "token": "calendar-date_outlined",
                                    "color": "grey"
                                }
                            },
                            {
                                "tag": "markdown",
                                "content": "**持续时间：**{{ .Duration }}",
                                "text_align": "left",
                                "text_size": "normal",
                                "icon": {
                                    "tag": "standard_icon",
                                    "token": "time_outlined",
                                    "color": "grey"
                                }
                            },
                            {
                                "tag": "markdown",
                                "content": "**操作人：**{{ if .Executor.LarkUserId }}<person id = '{{ .Executor.LarkUserId }}' show_name = true show_avatar = true style = 'normal'></person>{{ else if .Executor.Fullname }}{{ .Executor.Fullname }}{{ else }}{{ .Executor.Account }}{{ end }}",
                                "text_align": "left",
                                "text_size": "normal",
                                "icon": {
                                    "tag": "standard_icon",
                                    "token": "member_outlined",
                                    "color": "grey"
                                }
                            }
                            {{- if .Developers }},
                            {
                                "tag": "markdown",
                                "content": "**涉及的研发人员：**<at ids={{ range $index, $item := .Developers }}{{ if gt $index 0 }},{{ end }}{{ $item.LarkUserId }}{{ end }}></at>",
                                "text_align": "left",
                                "text_size": "normal",
                                "icon": {
                                    "tag": "standard_icon",
                                    "token": "nearby-group_outlined",
                                    "color": "grey"
                                }
                            }
                            {{- end }}
                            {{- if .Maintainers }},
                            {
                                "tag": "markdown",
                                "content": "**涉及的运维人员：**<at ids={{ range $index, $item := .Maintainers }}{{ if gt $index 0 }},{{ end }}{{ $item.LarkUserId }}{{ end }}></at>",
                                "text_align": "left",
                                "text_size": "normal",
                                "icon": {
                                    "tag": "standard_icon",
                                    "token": "nearby-group_outlined",
                                    "color": "grey"
                                }
                            }
                            {{- end }}
                        ],
                        "weight": 1
                    }
                ]
            }
            {{- if .Cases }},
            {
                "tag": "hr"
            },
            {
                "tag": "markdown",
                "content": "**涉及的用例**",
                "text_align": "left",
                "text_size": "normal",
                "icon": {
                    "tag": "standard_icon",
                    "token": "table-group_outlined",
                    "color": "grey"
                }
            },
            {
                "tag": "table",
                "columns": [
                    {
                        "data_type": "text",
                        "name": "suite_name",
                        "display_name": "场景名称",
                        "horizontal_align": "left",
                        "width": "auto"
                    },
                    {
                        "data_type": "text",
                        "name": "case_name",
                        "display_name": "用例名称",
                        "horizontal_align": "left",
                        "width": "auto"
                    },
                    {{- if .CasesWithCmd }}
                    {
                        "data_type": "text",
                        "name": "cmds",
                        "display_name": "涉及的命令号",
                        "horizontal_align": "left",
                        "width": "auto"
                    },
                    {{- end }}
                    {
                        "data_type": "number",
                        "name": "target_rps",
                        "display_name": "目标RPS",
                        "horizontal_align": "right",
                        "width": "auto",
                        "format": {
                            "precision": 0
                        }
                    }
                ],
                "rows": {{ .Cases }},
                "row_height": "low",
                "header_style": {
                    "background_style": "none",
                    "bold": true,
                    "lines": 1
                },
                "page_size": 10
            }
            {{- end }}
        ]
    },
    "i18n_header": {
        "zh_cn": {
            "title": {
                "tag": "plain_text",
                "content": "【压测预告】"
            },
            "subtitle": {
                "tag": "plain_text",
                "content": ""
            },
            "template": "blue",
            "ud_icon": {
                "tag": "standard_icon",
                "token": "livestream-ing_filled"
            }
        }
    }
}
