package suite

import (
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/mqc/internal/logic/baselogic"
	managerPb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type Suite_Log struct {
	*baselogic.DispatchLogic_Log
	CaseExecutionMode int64 `json:"case_execution_mode"` // 用例执行方式
}

type SuiteLogWriter struct {
	*baselogic.DispatchLogicLogWriter

	log *Suite_Log
}

func NewSuiteLogWriter(bw *baselogic.DispatchLogicLogWriter) *SuiteLogWriter {
	writer := &SuiteLogWriter{
		DispatchLogicLogWriter: bw,
		log: &Suite_Log{
			DispatchLogic_Log: bw.Log(),
		},
	}
	return writer
}

func (writer *SuiteLogWriter) toJson() string {
	return baselogic.MarshalJson(writer.log)
}

func (writer *SuiteLogWriter) setCaseExecutionMode(mode managerPb.ExecutionMode) {
	writer.log.CaseExecutionMode = int64(mode)
}
