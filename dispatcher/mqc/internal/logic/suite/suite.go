package suite

import (
	"context"
	"fmt"
	"time"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/codes"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/common/callbackclient"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/common/dtctl"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/common/errcode"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/mqc/internal/logic/baselogic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/mqc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/rpc/client/dispatcher"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/rpc/pb"
	managerpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
	reporterpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
)

type Suite struct {
	*baselogic.DispatchLogic
	logW *SuiteLogWriter
}

func NewSuite(ctx context.Context, svcCtx *svc.ServiceContext) *Suite {
	return &Suite{
		DispatchLogic: baselogic.NewDispatchLogic(ctx, svcCtx),
	}
}

func (x *Suite) Run(req *pb.DistributeReq) (err error) {
	x.Setup(req)

	err = x.run()

	x.Teardown()

	return err
}

func (x *Suite) Setup(req *pb.DistributeReq) {
	x.DispatchLogic.Setup(req)
	x.logW = NewSuiteLogWriter(x.DispatchLogic.LogWriter())
	x.SetDtCtl(x.GetDtControl())
	x.logW.setCaseExecutionMode(req.GetSuite().GetSuite().GetCaseExecutionMode())
}

func (x *Suite) run() (err error) {
	defer func() {
		// 空集合默认成功
		if x.Total() == 0 {
			x.StateM().UpdateStateManager(pb.ComponentState_Success, errcode.GetErrCode(errorx.OK))
		}

		if r := recover(); r != any(nil) {
			err = x.UpdatePanicStatef(codes.TaskPanic, "%v", r)
		}

		// 更新执行状态
		_, err = x.record()
		if err != nil {
			err = x.UpdatePanicStatef(codes.TaskRecordFailure, "Suite.record: %s", err)
		}

		// 如果是空集合/异常，则直接回调
		err = x.Callback(err)
		if err != nil {
			err = x.UpdatePanicStatef(codes.TaskRecordFailure, "Suite.Callback: %s", err)
		}

		x.Infof("Suite[%s] : %s", x.Source().GetSuite().GetSuiteExecuteId(), x.Content())
	}()

	err = x.Publish(x.GetChildMembers(), x.PublishOne)
	if err != nil {
		return err
	}

	return nil
}

func (x *Suite) GetDtControl() *dtctl.DispatchTaskControl {
	source := x.Source()
	member := &dtctl.DispatchTaskMember{
		ComponentId:        source.GetSuite().GetSuiteId(),
		ComponentType:      managerpb.ApiExecutionDataType_API_SUITE,
		Version:            "",
		ComponentExecuteId: source.GetSuite().GetSuiteExecuteId(),
	}
	return dtctl.NewDispatchTaskControl(x.Context(), x.ServiceContext().Redis, member)
}

func (x *Suite) GetChildMembers() []*dtctl.DispatchTaskMember {
	if len(x.Source().GetSuite().GetCases()) == 0 {
		return nil
	}

	members := make([]*dtctl.DispatchTaskMember, 0, len(x.Source().GetSuite().GetCases()))
	for idx, item := range x.Source().GetSuite().GetCases() {
		var componentId, version string
		switch item.GetType() {
		case managerpb.ApiExecutionDataType_API_CASE:
			case_ := item.GetCase()
			if !x.ValidCase(case_) {
				continue
			}
			componentId = case_.GetCaseId()
			version = case_.GetVersion()
		case managerpb.ApiExecutionDataType_INTERFACE_CASE:
			case_ := item.GetInterfaceCase()
			if !x.ValidInterfaceCase(case_) {
				continue
			}
			componentId = case_.GetCaseId()
			version = case_.GetVersion()
		}

		x.IncrTotal()
		members = append(
			members, &dtctl.DispatchTaskMember{
				ComponentId:   componentId,
				ComponentType: item.GetType(),
				Version:       version,
				Index:         idx,
				Data:          item,
			},
		)
	}
	return members
}

func (x *Suite) PublishOne(member *dtctl.DispatchTaskMember) (*dispatcher.PublishResp, error) {
	suite := x.Source().GetSuite()
	case_ := suite.GetCases()[member.Index]

	in := &dispatcher.PublishReq{
		TriggerMode: x.Source().GetTriggerMode(),
		TriggerRule: x.Source().GetTriggerRule(),
		ProjectId:   x.Source().GetProjectId(),
		TaskId:      x.Source().GetTaskId(),
		ExecuteId:   member.ComponentExecuteId,
		ExecuteType: x.Source().GetExecuteType(),
		UserId:      x.Source().GetUserId(),
		PurposeType: x.Source().GetPurposeType(),
		Debug:       x.Source().GetDebug(),
	}

	switch member.ComponentType {
	case managerpb.ApiExecutionDataType_API_CASE:
		in.PublishType = pb.PublishType_PublishType_API_CASE
		in.Data = &pb.PublishReq_Case{
			Case: &pb.CasePublishInfo{
				CaseId:         member.ComponentId,
				GeneralConfig:  x.Source().GetGeneralConfig(),
				AccountConfig:  x.Source().GetAccountConfig(),
				Version:        member.Version,
				SuiteId:        suite.GetSuiteId(),
				SuiteExecuteId: suite.GetSuiteExecuteId(),
				CaseName:       case_.GetCase().GetName(),
				MaintainedBy:   case_.GetCase().GetMaintainedBy(),
				SuiteName:      suite.GetSuite().GetName(),
				PlanId:         suite.GetPlanId(),
				PlanExecuteId:  suite.GetPlanExecuteId(),
				PlanName:       suite.GetPlanName(),
			},
		}
	case managerpb.ApiExecutionDataType_INTERFACE_CASE:
		in.PublishType = pb.PublishType_PublishType_INTERFACE_CASE
		in.Data = &pb.PublishReq_InterfaceCase{
			InterfaceCase: &pb.InterfaceCasePublishInfo{
				InterfaceCaseId:    member.ComponentId,
				GeneralConfig:      x.Source().GetGeneralConfig(),
				AccountConfig:      x.Source().GetAccountConfig(),
				Version:            member.Version,
				InterfaceId:        suite.GetSuiteId(), // 注：由于是API集合，因此这里设置的是`suite_id`（不是`document_id`）
				InterfaceExecuteId: suite.GetSuiteExecuteId(),
				DocumentId:         case_.GetInterfaceCase().GetDocumentId(),
				CaseName:           case_.GetInterfaceCase().GetName(),
				MaintainedBy:       case_.GetInterfaceCase().GetMaintainedBy(),
				DocumentName:       suite.GetSuite().GetName(), // 注：由于是API集合，因此这里设置的是`suite_name`
				PlanId:             suite.GetPlanId(),
				PlanExecuteId:      suite.GetPlanExecuteId(),
				PlanName:           suite.GetPlanName(),
			},
		}
	default:
		return nil, errors.Errorf("invalid case type: %s", member.ComponentType.String())
	}

	return x.AsyncPublishTask(in, member)
}

func (x *Suite) record() (resp *reporterpb.ModifySuiteRecordResponse, err error) {
	var endedAt int64 = 0
	content := x.Content()
	if x.Total() == 0 || x.StateM().State() == pb.ComponentState_Panic {
		endedAt = time.Now().UnixMilli()
		content = jsonx.MarshalToStringIgnoreError(x.StateM().Debug())
	}
	resp, err = x.ServiceContext().ReporterRpc.ModifySuiteRecord(
		x.Context(), &reporterpb.PutSuiteRecordRequest{
			TaskId:         x.Source().GetTaskId(),
			ExecuteId:      x.Source().GetSuite().GetSuiteExecuteId(),
			ExecuteType:    x.Source().GetExecuteType().String(),
			ProjectId:      x.Source().GetProjectId(),
			SuiteId:        x.Source().GetSuite().GetSuiteId(),
			SuiteExecuteId: x.Source().GetSuite().GetSuiteExecuteId(),
			SuiteName:      x.Source().GetSuite().GetSuite().GetName(),
			PlanExecuteId:  x.Source().GetSuite().GetPlanExecuteId(),
			Status:         x.StateM().State().String(),
			TotalCase:      x.Total(),
			Content:        content,
			StartedAt:      time.Now().UnixMilli(),
			EndedAt:        endedAt,
			ExecutedBy:     x.Source().GetUserId(),
		},
	)
	if err != nil {
		return nil, err
	}

	return
}

func (x *Suite) Callback(err error) error {
	// 空集合、异常需要直接回调
	if x.Total() != 0 && err == nil {
		return nil
	}

	cb := &pb.CallbackReq{
		TriggerMode:  x.Source().GetTriggerMode(),
		TriggerRule:  x.Source().GetTriggerRule(),
		ProjectId:    x.Source().GetProjectId(),
		TaskId:       x.Source().GetTaskId(),
		ExecuteType:  x.Source().GetExecuteType(),
		CallbackType: pb.CallbackType_CallbackType_API_SUITE,
		PurposeType:  x.Source().GetPurposeType(),
		Data: &pb.CallbackReq_Suite{
			Suite: &pb.SuiteCallbackData{
				PlanId:         x.Source().GetSuite().GetPlanId(),
				PlanExecuteId:  x.Source().GetSuite().GetPlanExecuteId(),
				SuiteId:        x.Source().GetSuite().GetSuiteId(),
				SuiteExecuteId: x.Source().GetSuite().GetSuiteExecuteId(),
				SuiteState:     x.StateM().State(),
			},
		},
	}

	return callbackclient.CallBack(
		x.Context(),
		x.ServiceContext().DispatcherProducer,
		x.ServiceContext().Config.Name,
		x.ServiceContext().Config.DispatcherProducer.Queue,
		x.UUID(),
		cb,
	)
}

// ValidCase 有效的case
func (x *Suite) ValidCase(case_ *managerpb.CaseComponent) bool {
	if x.Source().GetExecuteType() == managerpb.ApiExecutionDataType_API_PLAN {
		// 当执行计划时，用例的固有状态和引用状态均为`enable`才算有效
		return (case_.GetState() == managerpb.ResourceState_RS_PUBLISHED || case_.GetState() == managerpb.ResourceState_RS_ENABLED) && case_.GetReferenceState() == managerpb.CommonState_CS_ENABLE
	}

	// [2024-04-16] delete this rule:
	// 其他类型 固有状态 为true则有效
	// return case_.GetState() == managerpb.ResourceState_RS_PUBLISHED || case_.GetState() == managerpb.ResourceState_RS_ENABLED
	return true
}

// ValidInterfaceCase 有效的接口用例
func (x *Suite) ValidInterfaceCase(case_ *managerpb.InterfaceCaseComponent) bool {
	if x.Source().GetExecuteType() == managerpb.ApiExecutionDataType_API_PLAN {
		// 计划执行类型，固有状态 和 引用状态 均为enable，才属于有效
		return (case_.GetState() == managerpb.ResourceState_RS_PUBLISHED || case_.GetState() == managerpb.ResourceState_RS_ENABLED) && case_.GetReferenceState() == managerpb.CommonState_CS_ENABLE
	}

	// [2024-04-16] delete this rule:
	// 其他类型 固有状态 为true则有效
	// return case_.GetState() == managerpb.ResourceState_RS_PUBLISHED || case_.GetState() == managerpb.ResourceState_RS_ENABLED
	return true
}

func (x *Suite) UUID() string {
	return fmt.Sprintf("%s::%s", x.Source().GetTaskId(), x.Source().GetSuite().GetSuiteExecuteId())
}

func (x *Suite) Content() string {
	return x.logW.toJson()
}
