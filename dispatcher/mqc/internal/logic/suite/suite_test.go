package suite

import (
	"testing"
)

func TestSuite(t *testing.T) {
	var case_num int64 = 5

	handler := Mock_Suite()
	request := Mock_Suite_DistributeReq(int(case_num))
	err := handler.<PERSON>(request)
	if err != nil {
		t.<PERSON><PERSON>("%s", err)
		t.<PERSON>ail<PERSON><PERSON>()
	}
}

func TestEmptySuite(t *testing.T) {
	var case_num int64 = 0

	handler := Mock_Suite()
	request := Mock_Suite_DistributeReq(int(case_num))
	err := handler.<PERSON>(request)
	if err != nil {
		t.<PERSON><PERSON><PERSON>("%s", err)
		t.<PERSON>ail<PERSON><PERSON>()
	}
}
