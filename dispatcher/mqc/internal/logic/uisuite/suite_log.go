package uisuite

import (
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/mqc/internal/logic/baselogic"
	managerpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type UISuite_Log struct {
	*baselogic.DispatchLogic_Log
	CaseExecutionMode int64 `json:"case_execution_mode"` // 用例执行方式
}

type UISuiteLogWriter struct {
	*baselogic.DispatchLogicLogWriter

	log *UISuite_Log
}

func NewUISuiteLogWriter(bw *baselogic.DispatchLogicLogWriter) *UISuiteLogWriter {
	writer := &UISuiteLogWriter{
		DispatchLogicLogWriter: bw,
		log: &UISuite_Log{
			DispatchLogic_Log: bw.Log(),
		},
	}
	return writer
}

func (writer *UISuiteLogWriter) to<PERSON><PERSON>() string {
	return baselogic.<PERSON><PERSON><PERSON>(writer.log)
}

func (writer *UISuiteLogWriter) setCaseExecutionMode(mode managerpb.ExecutionMode) {
	writer.log.CaseExecutionMode = int64(mode)
}
