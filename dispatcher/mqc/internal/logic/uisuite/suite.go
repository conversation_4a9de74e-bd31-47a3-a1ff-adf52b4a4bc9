package uisuite

import (
	"context"
	"fmt"
	"time"

	"github.com/zeromicro/go-zero/core/jsonx"
	"github.com/zeromicro/go-zero/core/logx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/codes"
	commonpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/common/callbackclient"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/common/dtctl"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/common/errcode"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/mqc/internal/logic/baselogic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/mqc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/rpc/client/dispatcher"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/rpc/pb"
	managerpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
	reporterpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
)

type UISuite struct {
	*baselogic.DispatchLogic
	logW *UISuiteLogWriter
}

func NewUISuite(ctx context.Context, svcCtx *svc.ServiceContext) *UISuite {
	return &UISuite{
		DispatchLogic: baselogic.NewDispatchLogic(ctx, svcCtx),
	}
}

func (x *UISuite) Run(req *pb.DistributeReq) (err error) {
	x.Setup(req)

	err = x.run()

	x.Teardown()

	return err
}

func (x *UISuite) Setup(req *pb.DistributeReq) {
	x.DispatchLogic.Setup(req)
	x.logW = NewUISuiteLogWriter(x.DispatchLogic.LogWriter())
	x.SetDtCtl(x.GetDtControl())
	x.logW.setCaseExecutionMode(managerpb.ExecutionMode_EM_PARALLEL) // 默认为并行
}

func (x *UISuite) run() (err error) {
	defer func() {
		// 空集合默认成功
		if x.Total() == 0 {
			x.StateM().UpdateStateManager(pb.ComponentState_Success, errcode.GetErrCode(errorx.OK))
		}

		if r := recover(); r != any(nil) {
			err = x.UpdatePanicStatef(codes.TaskPanic, "%v", r)
		}

		// 更新执行状态
		_, err = x.record()
		if err != nil {
			err = x.UpdatePanicStatef(codes.TaskRecordFailure, "Suite.record: %s", err)
		}

		// 如果是空集合，则直接回调
		err = x.Callback(err)
		if err != nil {
			err = x.UpdatePanicStatef(codes.TaskRecordFailure, "Suite.Callback: %s", err)
		}

		x.Infof("Suite[%s] : %s", x.Source().GetUiSuite().GetUiSuiteExecuteId(), x.Content())
	}()

	err = x.Publish(x.GetChildMembers(), x.PublishOne)
	if err != nil {
		return err
	}

	return nil
}

func (x *UISuite) GetDtControl() *dtctl.DispatchTaskControl {
	source := x.Source()
	member := &dtctl.DispatchTaskMember{
		ComponentId:        source.GetUiSuite().GetUiSuiteId(),
		ComponentType:      managerpb.ApiExecutionDataType_UI_SUITE,
		Version:            "",
		ComponentExecuteId: source.GetUiSuite().GetUiSuiteExecuteId(),
	}
	return dtctl.NewDispatchTaskControl(x.Context(), x.ServiceContext().Redis, member)
}

func (x *UISuite) GetChildMembers() []*dtctl.DispatchTaskMember {
	if len(x.Source().GetUiSuite().GetUiCases()) == 0 {
		return nil
	}

	members := make([]*dtctl.DispatchTaskMember, 0, len(x.Source().GetUiSuite().GetUiCases()))
	for idx, item := range x.Source().GetUiSuite().GetUiCases() {
		case_ := item.GetUiCase()
		if !x.ValidCase(case_) {
			continue
		}

		x.IncrTotal()
		members = append(
			members, &dtctl.DispatchTaskMember{
				ComponentId:   case_.GetCaseId(),
				ComponentType: item.GetType(),
				Index:         idx,
			},
		)
	}
	return members
}

func (x *UISuite) PublishOne(member *dtctl.DispatchTaskMember) (*dispatcher.PublishResp, error) {
	uiCase := x.Source().GetUiSuite().GetUiCases()[member.Index]
	in := &dispatcher.PublishReq{
		TriggerMode:  x.Source().GetTriggerMode(),
		TriggerRule:  x.Source().GetTriggerRule(),
		ProjectId:    x.Source().GetProjectId(),
		TaskId:       x.Source().GetTaskId(),
		ExecuteId:    member.ComponentExecuteId,
		ExecuteType:  x.Source().GetExecuteType(),
		PublishType:  pb.PublishType_PublishType_UI_CASE,
		UserId:       x.Source().GetUserId(),
		PriorityType: x.Source().GetPriorityType(),
		Debug:        x.Source().GetDebug(),
		Data: &pb.PublishReq_UiCase{
			UiCase: &pb.UICasePublishInfo{
				UiCaseId:         member.ComponentId,
				UiSuiteId:        x.Source().GetUiSuite().GetUiSuiteId(),
				UiSuiteExecuteId: x.Source().GetUiSuite().GetUiSuiteExecuteId(),
				UiPlanId:         x.Source().GetUiSuite().GetUiPlanId(),
				UiPlanExecuteId:  x.Source().GetUiSuite().GetUiPlanExecuteId(),
				UiCase:           uiCase,
				MetaData:         x.Source().GetUiSuite().GetMetaData(),
			},
		},
	}
	return x.AsyncPublishTask(in, member)
}

func (x *UISuite) record() (resp *reporterpb.ModifyUISuiteRecordResponse, err error) {
	x.updateTaskInfo()
	var endtime int64 = 0
	content := x.Content()
	if x.Total() == 0 || x.StateM().State() == pb.ComponentState_Panic {
		endtime = time.Now().UnixMilli()
		content = jsonx.MarshalToStringIgnoreError(x.StateM().Debug())
	}
	resp, err = x.ServiceContext().UIReporterRpc.ModifyUISuiteRecord(
		x.Context(), &reporterpb.PutUISuiteRecordRequest{
			TaskId:        x.Source().GetTaskId(),
			ProjectId:     x.Source().GetProjectId(),
			ExecuteId:     x.Source().GetUiSuite().GetUiSuiteExecuteId(),
			SuiteId:       x.Source().GetUiSuite().GetUiSuiteId(),
			SuiteName:     x.Source().GetUiSuite().GetUiSuite().GetName(),
			PlanExecuteId: x.Source().GetUiSuite().GetUiPlanExecuteId(),
			Status:        x.StateM().State().String(),
			ExecutedBy:    x.Source().GetUserId(),
			StartedAt:     time.Now().UnixMilli(),
			EndedAt:       endtime,
			TotalCase:     x.Total(),
			Content:       content,
		},
	)
	if err != nil {
		return nil, err
	}

	return
}

func (x *UISuite) updateTaskInfo() {
	// 先更新总数
	_ = x.ServiceContext().TaskInfoProcessor.UpdateTotalCaseForIncrNum(
		x.Context(), x.Source().GetProjectId(), x.Source().GetTaskId(), int(x.Total()),
	)
	// 只要关注这三个状态
	var t commonpb.ExecutedResult
	switch x.StateM().State() {
	case pb.ComponentState_Panic:
		t = commonpb.ExecutedResult_TER_PANIC
	case pb.ComponentState_Failure:
		// t=commonpb.ExecutedResult_TER_PANIC
	case pb.ComponentState_Success:
		// t = commonpb.ExecutedResult_TER_SUCCESS
	}
	if t == commonpb.ExecutedResult_TER_INIT {
		return
	}
	logx.WithContext(x.Context()).Infof("UISuite updateTaskInfo[%s] : taskId:%s", t, x.Source().GetTaskId())
	// 用例只记录总数和panic情况
	_ = x.ServiceContext().TaskInfoProcessor.UpdateTaskExecutedResult(
		x.Context(), x.Source().GetProjectId(), x.Source().GetTaskId(), t, time.Now().UnixMilli(),
	)
}

func (x *UISuite) Callback(err error) error {
	if x.Total() != 0 && err == nil {
		return nil
	}

	cb := &pb.CallbackReq{
		TriggerMode:  x.Source().GetTriggerMode(),
		TriggerRule:  x.Source().GetTriggerRule(),
		ProjectId:    x.Source().GetProjectId(),
		TaskId:       x.Source().GetTaskId(),
		ExecuteType:  x.Source().GetExecuteType(),
		CallbackType: pb.CallbackType_CallbackType_UI_SUITE,
		Data: &pb.CallbackReq_UiSuite{
			UiSuite: &pb.UISuiteCallbackData{
				UiPlanId:         x.Source().GetUiSuite().GetUiPlanId(),
				UiPlanExecuteId:  x.Source().GetUiSuite().GetUiPlanExecuteId(),
				UiSuiteId:        x.Source().GetUiSuite().GetUiSuiteId(),
				UiSuiteExecuteId: x.Source().GetUiSuite().GetUiSuiteExecuteId(),
				SuiteState:       x.StateM().State(),
				MetaData:         x.Source().GetUiSuite().GetMetaData(),
			},
		},
	}

	return callbackclient.CallBack(
		x.Context(),
		x.ServiceContext().DispatcherProducer,
		x.ServiceContext().Config.Name,
		x.ServiceContext().Config.DispatcherProducer.Queue,
		x.UUID(),
		cb,
	)
}

// ValidCase 有效的case
func (x *UISuite) ValidCase(case_ *managerpb.UICaseComponent) bool {
	if x.Source().GetExecuteType() == managerpb.ApiExecutionDataType_UI_PLAN {
		// 当执行计划时，用例的状态为`enable`才算有效
		return case_.GetState() == managerpb.CommonState_CS_ENABLE
	}

	return true
}

func (x *UISuite) UUID() string {
	return fmt.Sprintf("%s::%s", x.Source().GetTaskId(), x.Source().GetUiSuite().GetUiSuiteExecuteId())
}

func (x *UISuite) Content() string {
	return x.logW.toJson()
}
