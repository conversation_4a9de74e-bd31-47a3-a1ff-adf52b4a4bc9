package precisionsuite

import (
	"context"
	"fmt"
	"time"

	"github.com/zeromicro/go-zero/core/jsonx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/codes"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/common/callbackclient"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/common/dtctl"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/common/errcode"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/mqc/internal/logic/baselogic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/mqc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/rpc/client/dispatcher"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/rpc/pb"
	managerpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
	reporterpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
)

type PrecisionSuite struct {
	*baselogic.DispatchLogic
	logW *SuiteLogWriter
}

func NewPrecisionSuite(ctx context.Context, svcCtx *svc.ServiceContext) *PrecisionSuite {
	return &PrecisionSuite{
		DispatchLogic: baselogic.NewDispatchLogic(ctx, svcCtx),
	}
}

func (x *PrecisionSuite) Run(req *pb.DistributeReq) (err error) {
	x.Setup(req)

	err = x.run()

	x.Teardown()

	return err
}

func (x *PrecisionSuite) Setup(req *pb.DistributeReq) {
	x.DispatchLogic.Setup(req)
	x.logW = NewSuiteLogWriter(x.DispatchLogic.LogWriter())
	x.SetDtCtl(x.GetDtControl())
	x.logW.setCaseExecutionMode(req.GetSuite().GetSuite().GetCaseExecutionMode())
}

func (x *PrecisionSuite) run() (err error) {
	defer func() {
		if r := recover(); r != any(nil) {
			err = x.UpdatePanicStatef(codes.TaskPanic, "%v", r)
		}

		// 空集合默认成功
		if x.Total() == 0 {
			x.StateM().UpdateStateManager(pb.ComponentState_Success, errcode.GetErrCode(errorx.OK))
		}

		// 更新执行状态
		_, err = x.record()
		if err != nil {
			err = x.UpdatePanicStatef(codes.TaskRecordFailure, "PrecisionSuite.record: %s", err)
		}

		// 如果是空集合，则直接回调
		err = x.Callback(err)
		if err != nil {
			err = x.UpdatePanicStatef(codes.TaskRecordFailure, "PrecisionSuite.Callback: %s", err)
		}

		x.Infof("PrecisionSuite[%s] : %s", x.Source().GetSuite().GetSuiteExecuteId(), x.Content())
	}()

	err = x.Publish(x.GetChildMembers(), x.PublishOne)
	if err != nil {
		return err
	}

	return nil
}

func (x *PrecisionSuite) GetDtControl() *dtctl.DispatchTaskControl {
	source := x.Source()
	member := &dtctl.DispatchTaskMember{
		ComponentId:        source.GetSuite().GetSuiteId(),
		ComponentType:      managerpb.ApiExecutionDataType_API_SUITE,
		Version:            "",
		ComponentExecuteId: source.GetSuite().GetSuiteExecuteId(),
	}
	return dtctl.NewDispatchTaskControl(x.Context(), x.ServiceContext().Redis, member)
}

func (x *PrecisionSuite) GetChildMembers() []*dtctl.DispatchTaskMember {
	if len(x.Source().GetSuite().GetCases()) == 0 {
		return nil
	}

	members := make([]*dtctl.DispatchTaskMember, 0, len(x.Source().GetSuite().GetCases()))
	for idx, item := range x.Source().GetSuite().GetCases() {
		case_ := item.GetCase()
		if !x.ValidCase(case_) {
			continue
		}

		x.IncrTotal()
		members = append(
			members, &dtctl.DispatchTaskMember{
				ComponentId:   case_.GetCaseId(),
				ComponentType: item.GetType(),
				Version:       case_.GetVersion(),
				Index:         idx,
				Data:          item,
			},
		)
	}
	return members
}

func (x *PrecisionSuite) PublishOne(member *dtctl.DispatchTaskMember) (*dispatcher.PublishResp, error) {
	suite := x.Source().GetSuite()
	case_ := suite.GetCases()[member.Index]

	in := &dispatcher.PublishReq{
		TriggerMode: x.Source().GetTriggerMode(),
		TriggerRule: x.Source().GetTriggerRule(),
		ProjectId:   x.Source().GetProjectId(),
		TaskId:      x.Source().GetTaskId(),
		ExecuteId:   member.ComponentExecuteId,
		ExecuteType: x.Source().GetExecuteType(),
		PublishType: pb.PublishType_PublishType_API_CASE,
		UserId:      x.Source().GetUserId(),
		Debug:       x.Source().GetDebug(),
		Data: &pb.PublishReq_Case{
			Case: &pb.CasePublishInfo{
				CaseId:         member.ComponentId,
				GeneralConfig:  x.Source().GetGeneralConfig(),
				AccountConfig:  x.Source().GetAccountConfig(),
				Version:        member.Version,
				SuiteId:        suite.GetSuiteId(),
				SuiteExecuteId: suite.GetSuiteExecuteId(),
				CaseName:       case_.GetCase().GetName(),
				MaintainedBy:   case_.GetCase().GetMaintainedBy(),
				SuiteName:      suite.GetSuite().GetName(),
				PlanId:         suite.GetPlanId(),
				PlanExecuteId:  suite.GetPlanExecuteId(),
				PlanName:       suite.GetPlanName(),
			},
		},
	}
	return x.AsyncPublishTask(in, member)
}

func (x *PrecisionSuite) record() (resp *reporterpb.ModifySuiteRecordResponse, err error) {
	var endedAt int64 = 0
	content := x.Content()
	if x.Total() == 0 || x.StateM().State() == pb.ComponentState_Panic {
		endedAt = time.Now().UnixMilli()
		content = jsonx.MarshalToStringIgnoreError(x.StateM().Debug())
	}
	resp, err = x.ServiceContext().ReporterRpc.ModifySuiteRecord(
		x.Context(), &reporterpb.PutSuiteRecordRequest{
			TaskId:         x.Source().GetTaskId(),
			ExecuteId:      x.Source().GetSuite().GetSuiteExecuteId(),
			ExecuteType:    x.Source().GetExecuteType().String(),
			ProjectId:      x.Source().GetProjectId(),
			SuiteId:        x.Source().GetSuite().GetSuiteId(),
			SuiteExecuteId: x.Source().GetSuite().GetSuiteExecuteId(),
			SuiteName:      x.Source().GetSuite().GetSuite().GetName(),
			PlanExecuteId:  x.Source().GetSuite().GetPlanExecuteId(),
			Status:         x.StateM().State().String(),
			TotalCase:      x.Total(),
			Content:        content,
			StartedAt:      time.Now().UnixMilli(),
			EndedAt:        endedAt,
			ExecutedBy:     x.Source().GetUserId(),
		},
	)
	if err != nil {
		return nil, err
	}

	return
}

func (x *PrecisionSuite) Callback(err error) error {
	// 空集合、异常需要直接回调
	if x.Total() != 0 && err == nil {
		return nil
	}

	cb := &pb.CallbackReq{
		TriggerMode:  x.Source().GetTriggerMode(),
		TriggerRule:  x.Source().GetTriggerRule(),
		ProjectId:    x.Source().GetProjectId(),
		TaskId:       x.Source().GetTaskId(),
		ExecuteType:  x.Source().GetExecuteType(),
		CallbackType: pb.CallbackType_CallbackType_API_SUITE,
		Data: &pb.CallbackReq_Suite{
			Suite: &pb.SuiteCallbackData{
				PlanId:         x.Source().GetSuite().GetPlanId(),
				PlanExecuteId:  x.Source().GetSuite().GetPlanExecuteId(),
				SuiteId:        x.Source().GetSuite().GetSuiteId(),
				SuiteExecuteId: x.Source().GetSuite().GetSuiteExecuteId(),
				SuiteState:     x.StateM().State(),
			},
		},
	}

	return callbackclient.CallBack(
		x.Context(),
		x.ServiceContext().DispatcherProducer,
		x.ServiceContext().Config.Name,
		x.ServiceContext().Config.DispatcherProducer.Queue,
		x.UUID(),
		cb,
	)
}

// ValidCase 有效的case
func (x *PrecisionSuite) ValidCase(case_ *managerpb.CaseComponent) bool {
	if x.Source().GetExecuteType() == managerpb.ApiExecutionDataType_API_PLAN {
		// 计划执行类型，固有状态 和 引用状态 均为enable，才属于有效
		return (case_.GetState() == managerpb.ResourceState_RS_PUBLISHED || case_.GetState() == managerpb.ResourceState_RS_ENABLED) && case_.GetReferenceState() == managerpb.CommonState_CS_ENABLE
	}

	// [2024-04-16] delete this rule:
	// 其他类型 固有状态 为true则有效
	// return case_.GetState() == managerpb.ResourceState_RS_PUBLISHED || case_.GetState() == managerpb.ResourceState_RS_ENABLED
	return true
}

func (x *PrecisionSuite) UUID() string {
	return fmt.Sprintf("%s::%s", x.Source().GetTaskId(), x.Source().GetSuite().GetSuiteExecuteId())
}

func (x *PrecisionSuite) Content() string {
	return x.logW.toJson()
}
