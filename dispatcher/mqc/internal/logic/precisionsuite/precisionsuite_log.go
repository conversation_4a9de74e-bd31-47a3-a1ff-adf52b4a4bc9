package precisionsuite

import (
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/mqc/internal/logic/baselogic"
	managerpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type PrecisionSuite_Log struct {
	*baselogic.DispatchLogic_Log
	CaseExecutionMode int64 `json:"case_execution_mode"` // 用例执行方式
}

type SuiteLogWriter struct {
	*baselogic.DispatchLogicLogWriter

	log *PrecisionSuite_Log
}

func NewSuiteLogWriter(bw *baselogic.DispatchLogicLogWriter) *SuiteLogWriter {
	writer := &SuiteLogWriter{
		DispatchLogicLogWriter: bw,
		log: &PrecisionSuite_Log{
			DispatchLogic_Log: bw.Log(),
		},
	}
	return writer
}

func (writer *SuiteLogWriter) toJson() string {
	return baselogic.<PERSON><PERSON><PERSON>(writer.log)
}

func (writer *SuiteLogWriter) setCaseExecutionMode(mode managerpb.ExecutionMode) {
	writer.log.CaseExecutionMode = int64(mode)
}
