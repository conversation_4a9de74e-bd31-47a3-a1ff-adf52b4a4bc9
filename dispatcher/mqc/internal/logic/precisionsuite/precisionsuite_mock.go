package precisionsuite

import (
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/utils"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/common/mock"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/mqc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/rpc/pb"
	managerpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

func Mock_Suite_DistributeReq(caseNum int) *pb.DistributeReq {
	param := mock.Mock_DistributeReq(managerpb.ApiExecutionDataType_API_SUITE)
	param.GeneralConfig = mock.Mock_GeneralConfig(param.GetProjectId())
	param.AccountConfig = mock.Mock_AccountConfig(param.GetProjectId())

	param.GetSuite().Cases = make([]*managerpb.ApiExecutionData, caseNum)
	for i := 0; i < caseNum; i++ {
		caseId := utils.GenCaseId()
		param.GetSuite().Cases[i] = &managerpb.ApiExecutionData{
			Id:   caseId,
			Type: managerpb.ApiExecutionDataType_API_CASE,
			Data: &managerpb.ApiExecutionData_Case{
				Case: &managerpb.CaseComponent{
					ProjectId: param.GetProjectId(),
					CaseId:    caseId,
					Version:   utils.GenVersion(),
					State:     managerpb.ResourceState_RS_PUBLISHED,
				},
			},
		}
	}

	return param
}

func Mock_Suite() *PrecisionSuite {
	return NewPrecisionSuite(mock.Mock_Context(), svc.Mock_ServiceContext())
}
