package callback

import (
	"fmt"
	"time"

	"github.com/zeromicro/go-zero/core/jsonx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/codes"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/common/callbackclient"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/common/dtctl"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/rpc/pb"
	managerpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
	reporterpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
)

type UICaseNode struct {
	*BaseNode
}

func NewUICaseNode(task *Callback) *UICaseNode {
	node := &UICaseNode{
		BaseNode: NewBaseNode(task),
	}
	return node
}

func (node *UICaseNode) Type() managerpb.ApiExecutionDataType {
	return managerpb.ApiExecutionDataType_UI_CASE
}

func (node *UICaseNode) GetDtControl() *dtctl.DispatchTaskControl {
	if node.ctl != nil {
		return node.ctl
	}

	source := node.Task().Source()
	member := &dtctl.DispatchTaskMember{
		ComponentId:        source.GetUiCase().GetUiSuiteId(),
		ComponentType:      managerpb.ApiExecutionDataType_UI_SUITE,
		ComponentExecuteId: source.GetUiCase().GetUiSuiteExecuteId(),
	}
	node.ctl = dtctl.NewDispatchTaskControl(node.Task().Context(), node.Task().ServiceContext().Redis, member)
	return node.ctl
}

func (node *UICaseNode) GetMember() *dtctl.DispatchTaskMember {
	source := node.Task().Source()
	member := &dtctl.DispatchTaskMember{
		ComponentId:        source.GetUiCase().GetUiCaseId(),
		ComponentType:      managerpb.ApiExecutionDataType_UI_CASE,
		ComponentExecuteId: source.GetUiCase().GetUiCaseExecuteId(),
	}
	return member
}

func (node *UICaseNode) State() pb.ComponentState {
	return node.Task().source.GetUiCase().GetCaseState()
}

func (node *UICaseNode) SetState(s pb.ComponentState) {
	node.Task().source.GetUiCase().CaseState = s
}

func (node *UICaseNode) TaskInfoProcessorSync() {
	source := node.Task().Source()
	switch node.State() {
	case pb.ComponentState_Failure, pb.ComponentState_Stop, pb.ComponentState_Panic:
	case pb.ComponentState_Success:
		err := node.BaseNode.task.ServiceContext().TaskInfoProcessor.UpdateSuccessCaseForIncr(
			node.BaseNode.task.Context(), source.GetProjectId(), source.GetTaskId(),
		)
		if err != nil {
			node.task.Errorf(
				"failed to update the `success_case` field, task_id: %s, execute_id: %s, case_id: %s, error: %+v",
				source.GetTaskId(), source.GetUiCase().GetUiCaseExecuteId(), source.GetUiCase().GetUiCaseId(), err,
			)
		}
	default:
		return
	}

	err := node.BaseNode.task.ServiceContext().TaskInfoProcessor.UpdateFinishedCaseForIncr(
		node.BaseNode.task.Context(), source.GetProjectId(), source.GetTaskId(),
	)
	if err != nil {
		node.task.Errorf(
			"failed to update the `finished_case` field, task_id: %s, execute_id: %s, case_id: %s, error: %+v",
			source.GetTaskId(), source.GetUiCase().GetUiCaseExecuteId(), source.GetUiCase().GetUiCaseId(), err,
		)
	}
}

func (node *UICaseNode) Record() (err error) {
	source := node.Task().Source()
	_, err = node.Task().ServiceContext().UIReporterRpc.ModifyUISuiteRecord(
		node.Task().Context(), &reporterpb.PutUISuiteRecordRequest{
			TaskId:        source.GetTaskId(),
			ExecuteId:     source.GetUiCase().GetUiSuiteExecuteId(), // 这里是父节点的execute_id
			ProjectId:     source.GetProjectId(),
			SuiteId:       source.GetUiCase().GetUiSuiteId(),
			PlanExecuteId: source.GetUiCase().GetUiSuiteExecuteId(),
			SuccessCase:   node.Task().Success(),
			FailureCase:   node.Task().Failure(),
			Status:        node.Task().StateM().State().String(),
			Content:       jsonx.MarshalToStringIgnoreError(node.Task().StateM().Debug()),
			EndedAt:       time.Now().UnixMilli(),
		},
	)
	return err
}

func (node *UICaseNode) RecordCallback() (err error) {
	source := node.Task().Source()
	_, err = node.Task().ServiceContext().UIReporterRpc.ModifyUICaseRecord(
		node.Task().Context(), &reporterpb.PutUICaseRecordRequest{
			TaskId:         source.GetTaskId(),
			ProjectId:      source.GetProjectId(),
			ExecuteId:      source.GetUiCase().GetUiCaseExecuteId(),
			CaseId:         source.GetUiCase().GetUiCaseId(),
			SuiteExecuteId: source.GetUiCase().GetUiSuiteExecuteId(),
			Status:         source.GetUiCase().GetCaseState().String(),
			EndedAt:        time.Now().UnixMilli(),
			Callback:       node.Task().Content(),
		},
	)
	return err
}

func (node *UICaseNode) Teardown() (err error) {
	if node.Task().Source().GetExecuteType() != managerpb.ApiExecutionDataType_UI_PLAN {
		return nil
	}

	// 回调plan
	source := node.Task().Source()
	cb := &pb.CallbackReq{
		TriggerMode:  source.GetTriggerMode(),
		TriggerRule:  source.GetTriggerRule(),
		ProjectId:    source.GetProjectId(),
		TaskId:       source.GetTaskId(),
		ExecuteType:  source.GetExecuteType(),
		CallbackType: pb.CallbackType_CallbackType_UI_SUITE,
		Data: &pb.CallbackReq_UiSuite{
			UiSuite: &pb.UISuiteCallbackData{
				UiPlanId:         source.GetUiCase().GetUiPlanId(),
				UiPlanExecuteId:  source.GetUiCase().GetUiPlanExecuteId(),
				UiSuiteId:        source.GetUiCase().GetUiSuiteId(),
				UiSuiteExecuteId: source.GetUiCase().GetUiSuiteExecuteId(),
				SuiteState:       node.Task().StateM().State(),
				MetaData:         source.GetUiCase().GetMetaData(),
			},
		},
	}

	err = callbackclient.CallBack(
		node.Task().Context(),
		node.Task().ServiceContext().DispatcherProducer,
		node.Task().ServiceContext().Config.Name,
		node.Task().ServiceContext().Config.DispatcherProducer.Queue,
		node.getSuiteUUID(),
		cb,
	)
	if err != nil {
		return node.Task().UpdateFailureStatef(codes.TaskPublishFailure, "%s", err)
	}

	return nil
}

func (node *UICaseNode) getSuiteUUID() string {
	source := node.Task().Source()
	return fmt.Sprintf("%s::%s", source.GetTaskId(), source.GetCase().GetSuiteExecuteId())
}
