package callback

import (
	"time"

	"github.com/zeromicro/go-zero/core/jsonx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/common/dtctl"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/rpc/pb"
	managerpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
	reporterpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
)

type ServiceNode struct {
	*BaseNode
}

func NewServiceNode(task *Callback) *ServiceNode {
	node := &ServiceNode{
		BaseNode: NewBaseNode(task),
	}
	return node
}

func (node *ServiceNode) Type() managerpb.ApiExecutionDataType {
	return managerpb.ApiExecutionDataType_API_SERVICE
}

func (node *ServiceNode) GetDtControl() *dtctl.DispatchTaskControl {
	if node.ctl != nil {
		return node.ctl
	}

	source := node.Task().Source()
	member := &dtctl.DispatchTaskMember{
		ComponentId:        source.GetService().GetPlanId(),
		ComponentType:      managerpb.ApiExecutionDataType_API_PLAN,
		Version:            "",
		ComponentExecuteId: source.GetService().GetPlanExecuteId(),
	}
	node.ctl = dtctl.NewDispatchTaskControl(node.Task().Context(), node.Task().ServiceContext().Redis, member)
	return node.ctl
}

func (node *ServiceNode) GetMember() *dtctl.DispatchTaskMember {
	source := node.Task().Source()
	member := &dtctl.DispatchTaskMember{
		ComponentId:        source.GetService().GetServiceId(),
		ComponentType:      managerpb.ApiExecutionDataType_API_SERVICE,
		Version:            "",
		ComponentExecuteId: source.GetService().GetServiceExecuteId(),
	}
	return member
}

func (node *ServiceNode) State() pb.ComponentState {
	return node.Task().source.GetService().GetServiceState()
}

func (node *ServiceNode) SetState(s pb.ComponentState) {
	node.Task().source.GetService().ServiceState = s
}

func (node *ServiceNode) TaskInfoProcessorSync() {
}

func (node *ServiceNode) Record() error {
	source := node.Task().Source()
	caseInfos, err := node.Task().ServiceContext().ReporterRpc.GetPlanCasesInfo(
		node.Task().Context(), &reporterpb.GetPlanCasesInfoRequest{
			ProjectId: source.GetProjectId(),
			TaskId:    source.GetTaskId(),
			ExecuteId: source.GetService().GetPlanExecuteId(),
		},
	)
	if err != nil {
		return err
	}

	_, err = node.Task().ServiceContext().ReporterRpc.ModifyPlanRecord(
		node.Task().Context(), &reporterpb.PutPlanRecordRequest{
			TaskId:        source.GetTaskId(),
			ExecuteId:     source.GetService().GetPlanExecuteId(), // 这里是父节点的execute_id
			ProjectId:     source.GetProjectId(),
			PlanId:        source.GetService().GetPlanId(),
			PlanExecuteId: source.GetService().GetPlanExecuteId(),
			SuccessSuite:  node.Task().Success(),
			FailureSuite:  node.Task().Failure(),
			TotalCase:     caseInfos.GetTotalCase(),
			SuccessCase:   caseInfos.GetSuccessCase(),
			FailureCase:   caseInfos.GetFailureCase(),
			Status:        node.Task().StateM().State().String(),
			Content:       jsonx.MarshalToStringIgnoreError(node.Task().StateM().Debug()),
			EndedAt:       time.Now().UnixMilli(),
		},
	)
	return err
}

func (node *ServiceNode) RecordCallback() error {
	source := node.Task().Source()
	_, err := node.Task().ServiceContext().ReporterRpc.ModifyServiceRecord(
		node.Task().Context(), &reporterpb.PutServiceRecordRequest{
			TaskId:           source.GetTaskId(),
			ExecuteId:        source.GetService().GetServiceExecuteId(),
			ExecuteType:      source.GetExecuteType().String(),
			ProjectId:        source.GetProjectId(),
			ServiceId:        source.GetService().GetServiceId(),
			ServiceExecuteId: source.GetService().GetServiceExecuteId(),
			Callback:         node.Task().Content(),
			Status:           source.GetService().GetServiceState().String(),
			EndedAt:          time.Now().UnixMilli(),
		},
	)
	return err
}

func (node *ServiceNode) Teardown() error {
	req := &notifyRequest{
		sCtx:   node.Task().ServiceContext(),
		ctx:    node.Task().Context(),
		source: node.Task().Source(),
	}

	err := SendNotify(req)
	if err != nil {
		node.Task().Errorf("SendNotify fail, task_id[%s], err: %s", node.Task().Source().GetTaskId(), err)
	}
	return nil
}
