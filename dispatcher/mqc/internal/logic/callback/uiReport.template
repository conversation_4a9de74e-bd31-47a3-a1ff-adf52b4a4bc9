{
    "schema": "2.0",
    "config": {
        "update_multi": true,
        "style": {
            "text_size": {
                "normal_v2": {
                    "default": "normal",
                    "pc": "normal",
                    "mobile": "heading"
                }
            }
        }
    },
    "body": {
        "direction": "vertical",
        "padding": "12px 12px 12px 12px",
        "elements": [
            {
                "tag": "column_set",
                "horizontal_spacing": "8px",
                "horizontal_align": "left",
                "columns": [
                    {
                        "tag": "column",
                        "width": "weighted",
                        "elements": [
                            {
                                "tag": "markdown",
                                "content": "**计划名称：** [{{ .PlanName }}]({{ .ModifyPlanURL }})",
                                "text_align": "left",
                                "text_size": "normal_v2",
                                "margin": "0px 0px 0px 0px",
                                "icon": {
                                    "tag": "standard_icon",
                                    "token": "livestream-content_outlined",
                                    "color": "grey"
                                }
                            },
                            {
                                "tag": "markdown",
                                "content": "**任务ID：** {{ .TaskId }}",
                                "text_align": "left",
                                "text_size": "normal_v2",
                                "margin": "0px 0px 0px 0px",
                                "icon": {
                                    "tag": "standard_icon",
                                    "token": "view-task_outlined",
                                    "color": "grey"
                                }
                            },
                            {
                                "tag": "markdown",
                                "content": "**APP版本：** {{ .AppVersion }}",
                                "text_align": "left",
                                "text_size": "normal_v2",
                                "margin": "0px 0px 0px 0px",
                                "icon": {
                                    "tag": "standard_icon",
                                    "token": "version_outlined",
                                    "color": "grey"
                                }
                            },
                            {
                                "tag": "markdown",
                                "content": "**APP下载地址：** {{ .AppDownloadUrl }}",
                                "text_align": "left",
                                "text_size": "normal_v2",
                                "margin": "0px 0px 0px 0px",
                                "icon": {
                                    "tag": "standard_icon",
                                    "token": "cloud-download_outlined",
                                    "color": "grey"
                                }
                            },
                            {
                                "tag": "markdown",
                                "content": "**开始时间：** {{ .StartedAt }}",
                                "text_align": "left",
                                "text_size": "normal_v2",
                                "margin": "0px 0px 0px 0px",
                                "icon": {
                                    "tag": "standard_icon",
                                    "token": "calendar_outlined",
                                    "color": "grey"
                                }
                            },
                            {
                                "tag": "markdown",
                                "content": "**结束时间：** {{ .EndedAt }}",
                                "text_align": "left",
                                "text_size": "normal_v2",
                                "margin": "0px 0px 0px 0px",
                                "icon": {
                                    "tag": "standard_icon",
                                    "token": "calendar-done_outlined",
                                    "color": "grey"
                                }
                            },
                            {
                                "tag": "markdown",
                                "content": "**触发模式：** {{ .TriggerMode }}",
                                "text_align": "left",
                                "text_size": "normal_v2",
                                "margin": "0px 0px 0px 0px",
                                "icon": {
                                    "tag": "standard_icon",
                                    "token": "cursor_outlined",
                                    "color": "grey"
                                }
                            },
                            {
                                "tag": "markdown",
                                "content": "**执行时长：** {{ .CostTime }}",
                                "text_align": "left",
                                "text_size": "normal_v2",
                                "margin": "0px 0px 0px 0px",
                                "icon": {
                                    "tag": "standard_icon",
                                    "token": "time_outlined",
                                    "color": "grey"
                                }
                            },
                            {
                                "tag": "markdown",
                                "content": "**成功用例数：** {{ .SuccessCase }}",
                                "text_align": "left",
                                "text_size": "normal_v2",
                                "margin": "0px 0px 0px 0px",
                                "icon": {
                                    "tag": "standard_icon",
                                    "token": "phone_outlined",
                                    "color": "grey"
                                }
                            },
                            {
                                "tag": "markdown",
                                "content": "**执行用例数：** {{ .TotalCase }}",
                                "text_align": "left",
                                "text_size": "normal_v2",
                                "margin": "0px 0px 0px 0px",
                                "icon": {
                                    "tag": "standard_icon",
                                    "token": "phone_outlined",
                                    "color": "grey"
                                }
                            },
                            {
                                "tag": "markdown",
                                "content": "**成功率：** {{ .PassRate }}",
                                "text_align": "left",
                                "text_size": "normal_v2",
                                "margin": "0px 0px 0px 0px",
                                "icon": {
                                    "tag": "standard_icon",
                                    "token": "poll_outlined",
                                    "color": "grey"
                                }
                            },
                            {
                                "tag": "markdown",
                                "content": "**状态：** <font color='{{ .Color }}'>{{ .PlanState }}</font>",
                                "text_align": "left",
                                "text_size": "normal_v2",
                                "margin": "0px 0px 0px 0px",
                                "icon": {
                                    "tag": "standard_icon",
                                    "token": "status-vacation_outlined",
                                    "color": "grey"
                                }
                            },
                            {
                                "tag": "markdown",
                                "content": "**执行人：** {{ if .ExecutorLarkId }}<person id = '{{ .ExecutorLarkId }}' show_name = true show_avatar = true style = 'normal'></person>{{ else }}{{ .Executor }}{{ end }}",
                                "text_align": "left",
                                "text_size": "normal_v2",
                                "margin": "0px 0px 0px 0px",
                                "icon": {
                                    "tag": "standard_icon",
                                    "token": "member_outlined",
                                    "color": "grey"
                                }
                            }
                        ],
                        "vertical_spacing": "8px",
                        "horizontal_align": "left",
                        "vertical_align": "top",
                        "weight": 1
                    }
                ],
                "margin": "0px 0px 0px 0px"
            },
            {{- if .SLAContent.Notify }}
            {
                "tag": "hr",
                "margin": "0px 0px 0px 0px"
            },
            {
                "tag": "column_set",
                "horizontal_spacing": "8px",
                "horizontal_align": "left",
                "columns": [
                    {
                        "tag": "column",
                        "width": "weighted",
                        "elements": [
                            {
                                "tag": "markdown",
                                "content": "**SLA基准版本：** {{ .SLAContent.BaseVersion }}",
                                "text_align": "left",
                                "text_size": "normal_v2",
                                "margin": "0px 0px 0px 0px",
                                "icon": {
                                    "tag": "standard_icon",
                                    "token": "tab-down_outlined",
                                    "color": "grey"
                                }
                            },
                            {{- if .SLAContent.Developers }}
                            {
                                "tag": "markdown",
                                "content": "**SLA开发人员：** <at ids={{ range $index, $item := .SLAContent.Developers }}{{ if gt $index 0 }},{{ end }}{{ $item.LarkId }}{{ end }}></at>",
                                "text_align": "left",
                                "text_size": "normal_v2",
                                "margin": "0px 0px 0px 0px",
                                "icon": {
                                    "tag": "standard_icon",
                                    "token": "group_outlined",
                                    "color": "grey"
                                }
                            },
                            {{- end }}
                            {
                                "tag": "markdown",
                                "content": "**SLA数据列表：**",
                                "text_align": "left",
                                "text_size": "normal_v2",
                                "margin": "0px 0px 0px 0px",
                                "icon": {
                                    "tag": "standard_icon",
                                    "token": "disorder-list_outlined",
                                    "color": "grey"
                                }
                            }
                        ],
                        "vertical_align": "top",
                        "weight": 1
                    }
                ],
                "margin": "0px 0px 0px 0px"
            },
            {
                "tag": "table",
                "columns": [
                    {
                        "data_type": "text",
                        "name": "Udid",
                        "display_name": "设备编号",
                        "horizontal_align": "left",
                        "width": "26%"
                    },
                    {
                        "data_type": "markdown",
                        "name": "AvgFinishLaunchedTime",
                        "display_name": "冷启动耗时(ms)",
                        "horizontal_align": "left",
                        "width": "22%"
                    },
                    {
                        "data_type": "markdown",
                        "name": "AvgAutoLoginTime",
                        "display_name": "自动登录耗时(ms)",
                        "horizontal_align": "left",
                        "width": "24%"
                    },
                    {
                        "data_type": "markdown",
                        "name": "AvgNewHomePageTime",
                        "display_name": "进入新版首页耗时(ms)",
                        "horizontal_align": "left",
                        "width": "29%"
                    }
                ],
                "rows": {{ .SLAContent.SLATable }},
                "row_height": "low",
                "header_style": {
                    "background_style": "none",
                    "bold": true,
                    "lines": 1
                },
                {{- if .SLAContent.Developers }}
                "page_size": 3
                {{- else }}
                "page_size": 4
                {{- end }}
            },
            {{- end }}
            {
                "tag": "hr",
                "margin": "0px 0px 0px 0px"
            },
            {
                "tag": "column_set",
                "horizontal_spacing": "28px",
                "horizontal_align": "left",
                "columns": [
                    {
                        "tag": "column",
                        "width": "weighted",
                        "elements": [
                            {
                                "tag": "button",
                                "text": {
                                    "tag": "plain_text",
                                    "content": "平台报告"
                                },
                                "type": "primary",
                                "width": "fill",
                                "size": "medium",
                                "icon": {
                                    "tag": "standard_icon",
                                    "token": "lookup_outlined"
                                },
                                "behaviors": [
                                    {
                                        "type": "open_url",
                                        "default_url": "{{ .ProbeReportURL }}",
                                        "pc_url": "",
                                        "ios_url": "",
                                        "android_url": ""
                                    }
                                ]
                            }
                        ],
                        "direction": "horizontal",
                        "horizontal_spacing": "8px",
                        "vertical_spacing": "8px",
                        "horizontal_align": "left",
                        "vertical_align": "top",
                        "weight": 1
                    },
                    {
                        "tag": "column",
                        "width": "weighted",
                        "elements": [
                            {
                                "tag": "button",
                                "text": {
                                    "tag": "plain_text",
                                    "content": "Allure报告"
                                },
                                "type": "primary",
                                "width": "fill",
                                "size": "medium",
                                "icon": {
                                    "tag": "standard_icon",
                                    "token": "googledrive_outlined"
                                },
                                "behaviors": [
                                    {
                                        "type": "open_url",
                                        "default_url": "{{ .ReportViewUrl }}",
                                        "pc_url": "",
                                        "ios_url": "",
                                        "android_url": ""
                                    }
                                ],
                                "margin": "0px 0px 0px 0px"
                            }
                        ],
                        "vertical_align": "top",
                        "weight": 1
                    }
                ],
                "margin": "0px 99px 0px 0px"
            }
        ]
    },
    "header": {
        "title": {
            "tag": "plain_text",
            "content": "UI测试报告"
        },
        "subtitle": {
            "tag": "plain_text",
            "content": ""
        },
        "template": "blue",
        "padding": "12px 12px 12px 12px"
    }
}