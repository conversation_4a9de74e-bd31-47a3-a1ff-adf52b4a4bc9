package callback

import (
	"time"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"
	"github.com/zeromicro/go-zero/core/logx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworkerv2/base"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/common/dtctl"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/rpc/pb"
	managerpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
	reporterpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
)

type UISuiteNode struct {
	*BaseNode
}

func NewUISuiteNode(task *Callback) *UISuiteNode {
	node := &UISuiteNode{
		BaseNode: NewBaseNode(task),
	}
	return node
}

func (node *UISuiteNode) Type() managerpb.ApiExecutionDataType {
	return managerpb.ApiExecutionDataType_UI_SUITE
}

func (node *UISuiteNode) GetDtControl() *dtctl.DispatchTaskControl {
	if node.ctl != nil {
		return node.ctl
	}

	source := node.Task().Source()
	member := &dtctl.DispatchTaskMember{
		ComponentId:        source.GetUiSuite().GetUiPlanId(),
		ComponentType:      managerpb.ApiExecutionDataType_UI_PLAN,
		ComponentExecuteId: source.GetUiSuite().GetUiPlanExecuteId(),
	}
	node.ctl = dtctl.NewDispatchTaskControl(node.Task().Context(), node.Task().ServiceContext().Redis, member)
	return node.ctl
}

func (node *UISuiteNode) GetMember() *dtctl.DispatchTaskMember {
	source := node.Task().Source()
	member := &dtctl.DispatchTaskMember{
		ComponentId:        source.GetUiSuite().GetUiSuiteId(),
		ComponentType:      managerpb.ApiExecutionDataType_UI_SUITE,
		ComponentExecuteId: source.GetUiSuite().GetUiSuiteExecuteId(),
	}
	return member
}

func (node *UISuiteNode) State() pb.ComponentState {
	return node.Task().source.GetUiSuite().GetSuiteState()
}

func (node *UISuiteNode) SetState(s pb.ComponentState) {
	node.Task().source.GetUiSuite().SuiteState = s
}

func (node *UISuiteNode) TaskInfoProcessorSync() {
	source := node.Task().Source()
	switch node.State() {
	case pb.ComponentState_Failure, pb.ComponentState_Stop, pb.ComponentState_Panic:
	case pb.ComponentState_Success:
		_, err := node.BaseNode.task.ServiceContext().TaskInfoProcessor.UpdateSuccessSuiteForIncr(
			node.BaseNode.task.Context(), source.GetProjectId(), source.GetTaskId(),
		)
		if err != nil {
			logx.WithContext(node.BaseNode.task.Context()).Errorf("UISuiteNode TaskInfoProcessorSync, error: %+v", err)
		}
	default:
		return
	}
	_, err := node.BaseNode.task.ServiceContext().TaskInfoProcessor.UpdateFinishedSuiteForIncr(
		node.BaseNode.task.Context(), source.GetProjectId(), source.GetTaskId(),
	)
	if err != nil {
		logx.WithContext(node.BaseNode.task.Context()).Errorf("UISuiteNode TaskInfoProcessorSync, error: %+v", err)
	}

	/*if node.Task().total == int64(fval) {
		// 全部集合处理完成
	}*/
}

func (node *UISuiteNode) Record() (err error) {
	source := node.Task().Source()
	// caseInfos, err := node.Task().ServiceContext().UIReporterRpc.GetUIPlanCasesInfo(node.Task().Context(), &reporterpb.GetUIPlanCasesInfoRequest{
	//	ProjectId: source.GetProjectId(),
	//	TaskId:    source.GetTaskId(),
	//	ExecuteId: source.GetUiSuite().GetUiPlanExecuteId(),
	// })
	// if err != nil {
	//	return err
	// }

	content := node.Task().Content()
	if node.Task().StateM().State() == pb.ComponentState_Panic {
		content = jsonx.MarshalToStringIgnoreError(node.Task().StateM().Debug())
	}

	_, err = node.Task().ServiceContext().UIReporterRpc.ModifyUIPlanRecord(
		node.Task().Context(), &reporterpb.PutUIPlanRecordRequest{
			ProjectId:     source.GetProjectId(),
			PlanId:        source.GetUiSuite().GetUiPlanId(),
			TaskId:        source.GetTaskId(),
			ExecuteId:     source.GetUiSuite().GetUiPlanExecuteId(), // 这里是父节点的execute_id
			FinishedSuite: node.Task().finish,
			SuccessSuite:  node.Task().Success(),
			// TotalCase:     caseInfos.GetTotalCase(),
			// FinishedCase:  caseInfos.GetFinishedCase(),
			// SuccessCase:   caseInfos.GetSuccessCase(),
			Status:  node.Task().StateM().State().String(),
			Content: content,
			EndedAt: time.Now().UnixMilli(), // 可以更新结束时间因为 suite全部执行完了
		},
	)
	return err
}

func (node *UISuiteNode) RecordCallback() (err error) {
	source := node.Task().Source()
	_, err = node.Task().ServiceContext().UIReporterRpc.ModifyUISuiteRecord(
		node.Task().Context(), &reporterpb.PutUISuiteRecordRequest{
			TaskId:        source.GetTaskId(),
			ExecuteId:     source.GetUiSuite().GetUiSuiteExecuteId(),
			ProjectId:     source.GetProjectId(),
			SuiteId:       source.GetUiSuite().GetUiSuiteId(),
			PlanExecuteId: source.GetUiSuite().GetUiPlanExecuteId(),
			Callback:      node.Task().Content(),
			Status:        source.GetUiSuite().GetSuiteState().String(),
			EndedAt:       time.Now().UnixMilli(),
		},
	)
	return err
}

func (node *UISuiteNode) Teardown() error {
	return node.mergeUIReport()
}

func (node *UISuiteNode) mergeUIReport() error {
	source := node.Task().Source()

	taskInfo := &pb.GenerateReportTaskInfo{
		ProjectId: source.GetProjectId(),
		PlanId:    source.GetUiSuite().GetUiPlanId(),
		TaskId:    source.GetTaskId(),
		UiCase: &pb.UICaseWorkerInfo{
			UiSuiteId:        source.GetUiSuite().GetUiSuiteId(),
			UiSuiteExecuteId: source.GetUiSuite().GetUiSuiteExecuteId(),
			UiPlanId:         source.GetUiSuite().GetUiPlanId(),
			UiPlanExecuteId:  source.GetUiSuite().GetUiPlanExecuteId(),
			MetaData:         source.GetUiSuite().GetMetaData(),
		},
	}
	task := base.NewTask(
		constants.MQTaskTypeUIWorkerGenerateReport,
		protobuf.MarshalJSONIgnoreError(taskInfo),
		base.WithMaxRetryOptions(0),
	)

	node.ctl.Infof("send %q task to ui worker, task: %s", constants.MQTaskTypeUIWorkerGenerateReport, task.Payload)

	_, err := node.Task().ServiceContext().UIWorkerProducer.Send(
		node.Task().Context(), task, base.QueuePriorityDefault,
	)
	if err != nil {
		return errors.Wrapf(errorx.Err(errorx.InternalError, err.Error()), "发送数据到mq失败, error: %+v", err)
	}

	return nil
}
