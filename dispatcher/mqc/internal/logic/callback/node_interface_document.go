package callback

import (
	"time"

	"github.com/zeromicro/go-zero/core/jsonx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/common/dtctl"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/rpc/pb"
	managerpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
	reporterpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
)

type InterfaceDocumentNode struct {
	*BaseNode
}

func NewInterfaceDocumentNode(task *Callback) *InterfaceDocumentNode {
	node := &InterfaceDocumentNode{
		BaseNode: NewBaseNode(task),
	}
	return node
}

func (node *InterfaceDocumentNode) Type() managerpb.ApiExecutionDataType {
	return managerpb.ApiExecutionDataType_INTERFACE_DOCUMENT
}

func (node *InterfaceDocumentNode) GetDtControl() *dtctl.DispatchTaskControl {
	if node.ctl != nil {
		return node.ctl
	}

	source := node.Task().Source()
	member := &dtctl.DispatchTaskMember{
		ComponentId:        source.GetInterfaceDocument().GetPlanId(),
		ComponentType:      managerpb.ApiExecutionDataType_API_PLAN,
		Version:            "",
		ComponentExecuteId: source.GetInterfaceDocument().GetPlanExecuteId(),
	}
	node.ctl = dtctl.NewDispatchTaskControl(node.Task().Context(), node.Task().ServiceContext().Redis, member)
	return node.ctl
}

func (node *InterfaceDocumentNode) GetMember() *dtctl.DispatchTaskMember {
	source := node.Task().Source()
	member := &dtctl.DispatchTaskMember{
		ComponentId:        source.GetInterfaceDocument().GetInterfaceId(),
		ComponentType:      managerpb.ApiExecutionDataType_INTERFACE_DOCUMENT,
		Version:            "",
		ComponentExecuteId: source.GetInterfaceDocument().GetInterfaceExecuteId(),
	}
	return member
}

func (node *InterfaceDocumentNode) State() pb.ComponentState {
	return node.Task().source.GetInterfaceDocument().GetInterfaceState()
}

func (node *InterfaceDocumentNode) SetState(s pb.ComponentState) {
	node.Task().source.GetInterfaceDocument().InterfaceState = s
}

func (node *InterfaceDocumentNode) TaskInfoProcessorSync() {
}

func (node *InterfaceDocumentNode) Record() error {
	source := node.Task().Source()

	caseInfos, err := node.Task().ServiceContext().ReporterRpc.GetPlanCasesInfo(
		node.Task().Context(), &reporterpb.GetPlanCasesInfoRequest{
			ProjectId: source.GetProjectId(),
			TaskId:    source.GetTaskId(),
			ExecuteId: source.GetInterfaceDocument().GetPlanExecuteId(),
		},
	)
	if err != nil {
		return err
	}

	_, err = node.Task().ServiceContext().ReporterRpc.ModifyPlanRecord(
		node.Task().Context(), &reporterpb.PutPlanRecordRequest{
			TaskId:        source.GetTaskId(),
			ExecuteId:     source.GetInterfaceDocument().GetPlanExecuteId(), // 这里是父节点的execute_id
			ProjectId:     source.GetProjectId(),
			PlanId:        source.GetInterfaceDocument().GetPlanId(),
			PlanExecuteId: source.GetInterfaceDocument().GetPlanExecuteId(),
			SuccessSuite:  node.Task().Success(),
			FailureSuite:  node.Task().Failure(),
			TotalCase:     caseInfos.GetTotalCase(),
			SuccessCase:   caseInfos.GetSuccessCase(),
			FailureCase:   caseInfos.GetFailureCase(),
			Status:        node.Task().StateM().State().String(),
			Content:       jsonx.MarshalToStringIgnoreError(node.Task().StateM().Debug()),
			EndedAt:       time.Now().UnixMilli(),
		},
	)
	return err
}

func (node *InterfaceDocumentNode) RecordCallback() error {
	source := node.Task().Source()
	_, err := node.Task().ServiceContext().ReporterRpc.ModifyInterfaceRecord(
		node.Task().Context(), &reporterpb.PutInterfaceRecordRequest{
			TaskId:             source.GetTaskId(),
			ExecuteId:          source.GetInterfaceDocument().GetInterfaceExecuteId(),
			ExecuteType:        source.GetExecuteType().String(),
			ProjectId:          source.GetProjectId(),
			InterfaceId:        source.GetInterfaceDocument().GetInterfaceId(),
			InterfaceExecuteId: source.GetInterfaceDocument().GetInterfaceExecuteId(),
			Callback:           node.Task().Content(),
			Status:             source.GetInterfaceDocument().GetInterfaceState().String(),
			EndedAt:            time.Now().UnixMilli(),
		},
	)
	return err
}

func (node *InterfaceDocumentNode) Teardown() error {
	notifyRequest := &notifyRequest{
		sCtx:   node.Task().ServiceContext(),
		ctx:    node.Task().Context(),
		source: node.Task().Source(),
	}

	return SendNotify(notifyRequest)
}
