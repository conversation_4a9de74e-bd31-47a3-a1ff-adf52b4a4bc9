package callback

import (
	"fmt"
	"time"

	"github.com/zeromicro/go-zero/core/jsonx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/codes"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/common/callbackclient"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/common/dtctl"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/rpc/pb"
	managerpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
	reporterpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
)

type ServiceApiCaseNode struct {
	*ApiCaseNode
}

func NewServiceApiCaseNode(task *Callback) *ServiceApiCaseNode {
	node := &ServiceApiCaseNode{
		ApiCaseNode: NewApiCaseNode(task),
	}
	return node
}

func (node *ServiceApiCaseNode) GetDtControl() *dtctl.DispatchTaskControl {
	if node.ctl != nil {
		return node.ctl
	}

	source := node.Task().Source()
	member := &dtctl.DispatchTaskMember{
		ComponentId:        source.GetCase().GetSuiteId(),
		ComponentType:      managerpb.ApiExecutionDataType_API_SERVICE,
		Version:            "",
		ComponentExecuteId: source.GetCase().GetSuiteExecuteId(),
	}
	node.ctl = dtctl.NewDispatchTaskControl(node.Task().Context(), node.Task().ServiceContext().Redis, member)
	return node.ctl
}

func (node *ServiceApiCaseNode) Record() error {
	source := node.Task().Source()
	_, err := node.Task().ServiceContext().ReporterRpc.ModifyServiceRecord(
		node.Task().Context(), &reporterpb.PutServiceRecordRequest{
			TaskId:           source.GetTaskId(),
			ExecuteId:        source.GetCase().GetSuiteExecuteId(), // 这里是父节点的execute_id
			ExecuteType:      source.GetExecuteType().String(),
			ProjectId:        source.GetProjectId(),
			ServiceId:        source.GetCase().GetSuiteId(),
			ServiceExecuteId: source.GetCase().GetSuiteExecuteId(),
			SuccessCase:      node.Task().Success(),
			FailureCase:      node.Task().Failure(),
			Status:           node.Task().StateM().State().String(),
			Content:          jsonx.MarshalToStringIgnoreError(node.Task().StateM().Debug()),
			EndedAt:          time.Now().UnixMilli(),
		},
	)
	return err
}

func (node *ServiceApiCaseNode) Teardown() error {
	if node.Task().Source().GetExecuteType() != managerpb.ApiExecutionDataType_API_PLAN {
		return nil
	}

	// 回调plan
	source := node.Task().Source()

	// 获取计划执行数据
	plan, err := node.Task().ServiceContext().ReporterRpc.GetParent(
		node.Task().Context(), &reporterpb.GetParentRecordRequest{
			TaskId:             source.GetTaskId(),
			ExecuteId:          source.GetCase().GetSuiteExecuteId(),
			ProjectId:          source.GetProjectId(),
			ExecuteType:        source.GetExecuteType().String(),
			ComponentId:        source.GetCase().GetSuiteId(),
			ComponentType:      managerpb.ApiExecutionDataType_API_SERVICE.String(),
			ComponentExecuteId: source.GetCase().GetSuiteExecuteId(),
		},
	)
	if err != nil {
		// 如果不存在plan，则无需回调
		if errorx.Convert(err).Code() == errorx.NotExists {
			return nil
		}

		return err
	}

	cb := &pb.CallbackReq{
		TriggerMode:  source.GetTriggerMode(),
		TriggerRule:  source.GetTriggerRule(),
		ProjectId:    source.GetProjectId(),
		TaskId:       source.GetTaskId(),
		ExecuteType:  source.GetExecuteType(),
		CallbackType: pb.CallbackType_CallbackType_API_SERVICE,
		PurposeType:  source.GetPurposeType(),
		Data: &pb.CallbackReq_Service{
			Service: &pb.ServiceCallbackData{
				PlanId:           plan.GetComponentId(),
				PlanExecuteId:    plan.GetComponentExecuteId(),
				ServiceId:        source.GetCase().GetSuiteId(),
				ServiceExecuteId: source.GetCase().GetSuiteExecuteId(),
				ServiceState:     node.Task().StateM().State(),
			},
		},
	}

	err = callbackclient.CallBack(
		node.Task().Context(),
		node.Task().ServiceContext().DispatcherProducer,
		node.Task().ServiceContext().Config.Name,
		node.Task().ServiceContext().Config.DispatcherProducer.Queue,
		node.getSuiteUUID(),
		cb,
	)
	if err != nil {
		return node.Task().UpdateFailureStatef(codes.TaskPublishFailure, "%s", err)
	}

	return nil
}

func (node *ServiceApiCaseNode) getSuiteUUID() string {
	source := node.Task().Source()
	return fmt.Sprintf("%s::%s", source.GetTaskId(), source.GetCase().GetSuiteExecuteId())
}
