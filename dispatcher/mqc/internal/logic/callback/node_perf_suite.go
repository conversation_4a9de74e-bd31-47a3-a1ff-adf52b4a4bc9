package callback

import (
	"time"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/logx"
	"google.golang.org/protobuf/types/known/timestamppb"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworkerv2/base"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/common/dtctl"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/rpc/pb"
	managerpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
	reporterpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
)

type PerfSuiteNode struct {
	*BaseNode
}

func NewPerfSuiteNode(task *Callback) *PerfSuiteNode {
	node := &PerfSuiteNode{
		BaseNode: NewBaseNode(task),
	}
	return node
}

func (node *PerfSuiteNode) Type() managerpb.ApiExecutionDataType {
	return managerpb.ApiExecutionDataType_PERF_SUITE
}

func (node *PerfSuiteNode) GetDtControl() *dtctl.DispatchTaskControl {
	if node.ctl != nil {
		return node.ctl
	}

	source := node.Task().Source()
	member := &dtctl.DispatchTaskMember{
		ComponentId:        source.GetPerfSuite().GetPerfPlanId(),
		ComponentType:      managerpb.ApiExecutionDataType_PERF_PLAN,
		ComponentExecuteId: source.GetPerfSuite().GetPerfPlanExecuteId(),
	}
	node.ctl = dtctl.NewDispatchTaskControl(node.Task().Context(), node.Task().ServiceContext().Redis, member)
	return node.ctl
}

func (node *PerfSuiteNode) GetMember() *dtctl.DispatchTaskMember {
	source := node.Task().Source()
	member := &dtctl.DispatchTaskMember{
		ComponentId:        source.GetPerfSuite().GetPerfSuiteId(),
		ComponentType:      managerpb.ApiExecutionDataType_PERF_SUITE,
		ComponentExecuteId: source.GetPerfSuite().GetPerfSuiteExecuteId(),
	}
	return member
}

func (node *PerfSuiteNode) State() pb.ComponentState {
	return node.Task().source.GetPerfSuite().GetSuiteState()
}

func (node *PerfSuiteNode) SetState(s pb.ComponentState) {
	node.Task().source.GetPerfSuite().SuiteState = s
}

func (node *PerfSuiteNode) TaskInfoProcessorSync() {
	source := node.Task().Source()
	switch node.State() {
	case pb.ComponentState_Failure, pb.ComponentState_Stop, pb.ComponentState_Panic:
	case pb.ComponentState_Success:
		_, err := node.BaseNode.task.ServiceContext().TaskInfoProcessor.UpdateSuccessSuiteForIncr(
			node.BaseNode.task.Context(), source.GetProjectId(), source.GetTaskId(),
		)
		if err != nil {
			logx.WithContext(node.BaseNode.task.Context()).Errorf(
				"PerfSuiteNode TaskInfoProcessorSync, error: %+v", err,
			)
		}
	default:
		return
	}
	_, err := node.BaseNode.task.ServiceContext().TaskInfoProcessor.UpdateFinishedSuiteForIncr(
		node.BaseNode.task.Context(), source.GetProjectId(), source.GetTaskId(),
	)
	if err != nil {
		logx.WithContext(node.BaseNode.task.Context()).Errorf("PerfSuiteNode TaskInfoProcessorSync, error: %+v", err)
	}
}

func (node *PerfSuiteNode) Record() (err error) {
	source := node.Task().Source()
	state := node.Task().StateM().State()

	var errMsg *reporterpb.ErrorMessage
	if state == pb.ComponentState_Panic {
		record, err := node.Task().ServiceContext().PerfReporterRpc.GetPerfSuiteRecord(
			node.Task().Context(), &reporterpb.GetPerfSuiteRecordReq{
				TaskId:    source.GetTaskId(),
				ExecuteId: source.GetPerfSuite().GetPerfSuiteExecuteId(),
				ProjectId: source.GetProjectId(),
			},
		)
		if err != nil {
			return err
		}
		errMsg = record.GetRecord().GetErrMsg()
	}

	_, err = node.Task().ServiceContext().PerfReporterRpc.ModifyPerfPlanRecord(
		node.Task().Context(), &reporterpb.ModifyPerfPlanRecordReq{
			TaskId:    source.GetTaskId(),
			ExecuteId: source.GetPerfSuite().GetPerfPlanExecuteId(), // 这里是父节点的execute_id
			ProjectId: source.GetProjectId(),
			PlanId:    source.GetPerfSuite().GetPerfPlanId(),
			Status:    state.String(),
			EndedAt:   timestamppb.New(time.Now()),
			ErrMsg:    errMsg,
		},
	)
	return err
}

func (node *PerfSuiteNode) RecordCallback() (err error) {
	source := node.Task().Source()
	_, err = node.Task().ServiceContext().PerfReporterRpc.ModifyPerfSuiteRecord(
		node.Task().Context(), &reporterpb.ModifyPerfSuiteRecordReq{
			TaskId:        source.GetTaskId(),
			ExecuteId:     source.GetPerfSuite().GetPerfSuiteExecuteId(),
			ProjectId:     source.GetProjectId(),
			SuiteId:       source.GetPerfSuite().GetPerfSuiteId(),
			PlanExecuteId: source.GetPerfSuite().GetPerfPlanExecuteId(),
			Status:        source.GetPerfSuite().GetSuiteState().String(),
			EndedAt:       timestamppb.New(time.Now()),
		},
	)
	return err
}

func (node *PerfSuiteNode) Teardown() error {
	return node.callbackToPerfWorker()
}

func (node *PerfSuiteNode) callbackToPerfWorker() error {
	source := node.Task().Source()

	taskInfo := &pb.FinalHandleTaskInfo{
		ProjectId: source.GetProjectId(),
		PlanId:    source.GetPerfSuite().GetPerfPlanId(),
		TaskId:    source.GetTaskId(),
		ExecuteId: source.GetPerfSuite().GetPerfPlanExecuteId(),
	}
	task := base.NewTask(
		constants.MQTaskTypePerfWorkerFinalHandle,
		protobuf.MarshalJSONIgnoreError(taskInfo),
		base.WithMaxRetryOptions(0),
		base.WithRetentionOptions(5*time.Minute),
	)

	node.ctl.Infof("send %q task to perf worker, task: %s", constants.MQTaskTypePerfWorkerFinalHandle, task.Payload)

	_, err := node.Task().ServiceContext().PerfWorkerProducer.Send(
		node.Task().Context(), task, base.QueuePriorityDefault,
	)
	if err != nil {
		return errors.Wrapf(errorx.Err(errorx.InternalError, err.Error()), "发送数据到mq失败, error: %+v", err)
	}

	return nil
}
