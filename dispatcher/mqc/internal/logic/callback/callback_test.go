package callback

import (
	"fmt"
	"testing"

	"github.com/zeromicro/go-zero/core/logx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworker/consumer"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/rpc/pb"
)

func TestConsumer(t *testing.T) {
	c := consumer.NewConsumer(
		consumer.ConsumerConfig{
			Broker:      "192.168.118.128:6379",
			Backend:     "192.168.118.128:6379",
			Queue:       "mqc:uiworker:unittest",
			Db:          4,
			ConsumerTag: "test_consumer",
		},
	)

	err := c.RegisterTasks(
		map[string]any{
			"ui_case": func(data []byte) (string, error) {
				fmt.Printf("data = %s\n", data)

				taskData := &pb.CallbackReq{}
				err := protobuf.UnmarshalJSON(data, taskData)
				if err != nil {
					logx.Errorf("订阅源数据反序列化失败：%s", err)
					return "fail", err
				}

				fmt.Println("callback =", taskData)

				return "success", nil
			},
		},
	)
	if err != nil {
		t.Errorf("err: %s", err)
		t.FailNow()
	}

	c.Start()
}
