package callback

import (
	"testing"
	ttemplate "text/template"
	"time"

	pb1 "gitlab.ttyuyin.com/TestDevelopment/qet-backend-middleware/user/rpc/pb"
)

func Test_UITemplateParse(t *testing.T) {
	now := time.Now()
	slaTable := `[
  {
    "Udid": "b008ad06",
    "AvgFinishLaunchedTime": "<font color='yellow'>1805（2.0%）</font>",
    "AvgAutoLoginTime": "<font color='green'>267（-88.9%）</font>",
    "AvgNewHomePageTime": "<font color='red'>617（12.2%）</font>"
  },
  {
    "Udid": "9TM9K23405007545",
    "AvgFinishLaunchedTime": "<font color='yellow'>1889（6.5%）</font>",
    "AvgAutoLoginTime": "<font color='yellow'>272（7.1%）</font>",
    "AvgNewHomePageTime": "<font color='black'>505（0%）</font>"
  },
  {
    "Udid": "AFTP9X3A04W00789",
    "AvgFinishLaunchedTime": "<font color='yellow'>2365（0.6%）</font>",
    "AvgAutoLoginTime": "<font color='green'>287（-30.3%）</font>",
    "AvgNewHomePageTime": "<font color='black'>613（0%）</font>"
  },
  {
    "Udid": "AFTP9X3A04W00789",
    "AvgFinishLaunchedTime": "<font color='yellow'>2365（0.6%）</font>",
    "AvgAutoLoginTime": "<font color='green'>287（-30.3%）</font>",
    "AvgNewHomePageTime": "<font color='black'>613（0%）</font>"
  },
  {
    "Udid": "AFTP9X3A04W00789",
    "AvgFinishLaunchedTime": "<font color='red'>2365（0.6%）</font>",
    "AvgAutoLoginTime": "<font color='green'>287（-30.3%）</font>",
    "AvgNewHomePageTime": "<font color='red'>613（0%）</font>"
  }
]`
	type args struct {
		notifyT *ttemplate.Template
		item    any
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name: "ui report template",
			args: args{
				notifyT: uiReportTemplate,
				item: &notifyContent{
					PlanName:       "123",
					ModifyPlanURL:  "https://www.baidu.com",
					TaskId:         "123456",
					AppVersion:     "v1.0.0",
					AppDownloadUrl: "https://www.baidu.com",
					StartedAt:      now.Format("2006-01-02 15:04:05"),
					EndedAt:        now.Format("2006-01-02 15:04:05"),
					TriggerMode:    "手动执行",
					CostTime:       "1h20m0s",
					SuccessCase:    100,
					TotalCase:      100,
					PassRate:       "100%",
					Color:          "green",
					PlanState:      "成功",
					ExecutorLarkId: "c1a1bbdd",
					Executor:       "胡倩芸",
					ProbeReportURL: "https://www.baidu.com",
					ReportViewUrl:  "https://www.baidu.com",
					SLAContent: &slaContent{
						Notify:      true,
						BaseVersion: "v2.0.0",
						Developers: []*pb1.UserInfo{
							{
								LarkId: "c1a1bbdd",
							},
						},
						SLATable: slaTable,
					},
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				got, err := templateParse(tt.args.notifyT, tt.args.item)
				if (err != nil) != tt.wantErr {
					t.Fatalf("templateParse() error = %v, wantErr %v", err, tt.wantErr)
				}

				t.Logf("content: \n%s", got)
			},
		)
	}
}
