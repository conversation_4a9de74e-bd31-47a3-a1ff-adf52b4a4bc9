package callback

import (
	"context"
	"fmt"
	"html/template"
	"strconv"
	"strings"
	"text/tabwriter"
	"time"

	"github.com/zeromicro/go-zero/core/jsonx"
	"github.com/zeromicro/go-zero/core/lang"
	"github.com/zeromicro/go-zero/core/logx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-middleware/notifier/rpc/client/notificationservice"
	notifierpb "gitlab.ttyuyin.com/TestDevelopment/qet-backend-middleware/notifier/rpc/pb"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-middleware/user/rpc/client/userservice"
	pb1 "gitlab.ttyuyin.com/TestDevelopment/qet-backend-middleware/user/rpc/pb"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/codes"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	commonpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/thirdparty/sla"
	commontypes "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/mqc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/client/notifyservice"
	managerpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
	reporterpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
)

type UIPlanRecord struct {
	PlanId         string
	PlanName       string
	TriggerMode    string
	PlatformType   commonpb.PlatformType
	AppVersion     string
	AppDownloadUrl string
	ExecutedBy     string
	StartedAt      int64
	EndedAt        int64
	CostTime       string
	TotalSuite     int64
	FinishedSuite  int64
	SuccessSuite   int64
	TotalCase      int64
	FinishedCase   int64
	SuccessCase    int64
	Domain         string
}

func getUIPlanRecord(uiNotifyRequest *UINotifyRequest) (*UIPlanRecord, error) {
	// 计划执行信息
	var err error

	respPlanRecord, err := uiNotifyRequest.SCtx.UIReporterRpc.ViewUIPlanRecord(
		uiNotifyRequest.Ctx, &reporterpb.ViewUIPlanRecordRequest{
			ProjectId: uiNotifyRequest.ProjectId,
			TaskId:    uiNotifyRequest.TaskId,
			ExecuteId: uiNotifyRequest.PlanExecuteId,
		},
	)
	if err != nil {
		return nil, err
	}

	// hours := respPlanRecord.CostTime / 3600000
	// minutes := (respPlanRecord.CostTime % 3600000) / 60000
	// seconds := (respPlanRecord.CostTime % 60000) / 1000
	// costTime := fmt.Sprintf("%dh %dm %ds", hours, minutes, seconds)
	// costTime := fmt.Sprintf("%02dh:%02dm:%02ds", hours, minutes, seconds)

	duration := time.Duration(respPlanRecord.GetCostTime() * 1000 * 1000)
	costTime := duration.String()
	uiPlanMeta := new(managerpb.UIPlanComponent)
	// err = uiNotifyRequest.SCtx.TaskInfoProcessor.GetPlanMetaData(
	// 	uiNotifyRequest.Ctx, uiNotifyRequest.ProjectId, uiNotifyRequest.TaskId, uiPlanMeta,
	// )
	err = protobuf.UnmarshalJSONFromString(respPlanRecord.GetExecuteData(), uiPlanMeta)
	if err != nil {
		return nil, err
	}

	planRecord := &UIPlanRecord{
		PlanId:         respPlanRecord.GetPlanId(),
		PlanName:       respPlanRecord.GetPlanName(),
		TriggerMode:    respPlanRecord.GetTriggerMode(),
		ExecutedBy:     respPlanRecord.GetExecutedBy(),
		StartedAt:      respPlanRecord.GetStartedAt(),
		EndedAt:        respPlanRecord.GetEndedAt(),
		CostTime:       costTime,
		TotalSuite:     respPlanRecord.GetTotalSuite(),
		SuccessSuite:   respPlanRecord.GetSuccessSuite(),
		FinishedSuite:  respPlanRecord.GetFinishedSuite(),
		TotalCase:      respPlanRecord.GetTotalCase(),
		SuccessCase:    respPlanRecord.GetSuccessCase(),
		FinishedCase:   respPlanRecord.GetFinishedCase(),
		PlatformType:   uiPlanMeta.GetMetaData().GetPlatformType(),
		AppVersion:     uiPlanMeta.GetMetaData().GetAppVersion(),
		AppDownloadUrl: uiPlanMeta.GetMetaData().GetAppDownloadLink(),
		Domain:         uiNotifyRequest.SCtx.ProbeBaseURL,
	}

	return planRecord, err
}

func getUiReceivers(
	item *managerpb.NotifyItem, planRecord *UIPlanRecord, allReceiversMap map[string]string,
	onlyFailReceiversMap map[string]string, receivers []string,
) ([]string, map[string]string, map[string]string) {
	receiver := item.GetReceiver()
	notifyMode := item.GetNotifyMode()

	if notifyMode == managerpb.NotifyMode_ALWAYS_NOTIFY {
		_, ok := allReceiversMap[receiver]
		if !ok { // 新的通知对象
			receivers = append(receivers, receiver)
			allReceiversMap[receiver] = receiver
		}
	} else if planRecord.SuccessSuite != planRecord.TotalSuite {
		_, ok := onlyFailReceiversMap[receiver]
		if !ok { // 新的通知对象
			receivers = append(receivers, receiver)
			onlyFailReceiversMap[receiver] = receiver
		}
	}
	return receivers, allReceiversMap, onlyFailReceiversMap
}

func dealUINotifyItem(uiNotifyRequest *UINotifyRequest, planRecord *UIPlanRecord) (
	larkGroupReceivers []string,
	emailReceivers []string, err error,
) {
	// 计划通知人

	respNotify, err := uiNotifyRequest.SCtx.ManagerRpc.GetPlanNotify(
		uiNotifyRequest.Ctx,
		&notifyservice.GetPlanNotifyReq{
			ProjectId: uiNotifyRequest.ProjectId,
			PlanId:    uiNotifyRequest.PlanId,
		},
	)
	if err != nil {
		return nil, nil, err
	} else if len(respNotify.Items) == 0 {
		logx.Warnf("从manager获取到的计划通知人个数为0")
		return nil, nil, nil
	}

	// 接收人列表
	allLarkGroupAddressMap := make(map[string]string)      // 保存已经处理过的仅执行完成时通知飞书群
	onlyFailLarkGroupAddressMap := make(map[string]string) // 保存已经处理过的仅执行失败时通知飞书群
	allEmailAddressMap := make(map[string]string)          // 保存已经处理过的仅执行完成时通知邮箱
	onlyFailEmailAddressMap := make(map[string]string)     // 保存已经处理过的仅执行失败时通知邮箱

	for _, item := range respNotify.Items {
		switch item.GetNotifyType() {
		case managerpb.NotifyType_LARK_GROUP:
			larkGroupReceivers, allLarkGroupAddressMap, onlyFailLarkGroupAddressMap = getUiReceivers(
				item, planRecord, allLarkGroupAddressMap, onlyFailLarkGroupAddressMap, larkGroupReceivers,
			)
		case managerpb.NotifyType_EMAIL:
			emailReceivers, allEmailAddressMap, onlyFailEmailAddressMap = getUiReceivers(
				item, planRecord, allEmailAddressMap, onlyFailEmailAddressMap, emailReceivers,
			)
		}
	}
	return larkGroupReceivers, emailReceivers, nil
}

func getUIPlanExecutor(notifyRequest *UINotifyRequest, planRecord *UIPlanRecord) (string, string, error) {
	if planRecord.ExecutedBy == "empty" {
		return "admin", "", nil
	}
	userListResp, err := notifyRequest.SCtx.UserRpc.GetUser(
		notifyRequest.Ctx, &userservice.GetUserReq{
			Account: planRecord.ExecutedBy,
		},
	)
	if err != nil {
		return "", "", err
	}
	return userListResp.GetUserInfo().GetFullname(), userListResp.GetUserInfo().GetLarkId(), nil
}

func generateUIPlanRawContent(notifyRequest *UINotifyRequest, planRecord *UIPlanRecord) (
	rawContent *notifyContent, err error,
) {
	// 计划执行人姓名
	executor, executorLarkId, err := getUIPlanExecutor(notifyRequest, planRecord)
	if err != nil {
		return nil, err
	}

	// 测试计划信息
	//planInfo, err := notifyRequest.SCtx.ManagerRpc.ViewUiPlan(
	//	notifyRequest.Ctx,
	//	&managerpb.ViewUiPlanReq{
	//		ProjectId: notifyRequest.ProjectId,
	//		PlanId:    notifyRequest.PlanId,
	//	},
	//)
	//if err != nil {
	//	return nil, err
	//}

	// 计划执行触发模式
	var triggerModeString string
	switch planRecord.TriggerMode {
	case commonpb.TriggerMode_MANUAL.String():
		triggerModeString = "手动执行"
	case commonpb.TriggerMode_SCHEDULE.String():
		triggerModeString = "定时触发"
	case commonpb.TriggerMode_INTERFACE.String():
		triggerModeString = "接口触发"
	default:
		triggerModeString = "未知"
	}

	startTime := time.Unix(planRecord.StartedAt/1000, 0)
	endTime := time.Unix(planRecord.EndedAt/1000, 0)

	planState := "成功"
	color := "green"
	if planRecord.SuccessSuite != planRecord.TotalSuite {
		planState = "失败"
		color = "red"
	}

	var passRate string
	if planRecord.SuccessCase == 0 || planRecord.TotalCase == 0 {
		passRate = "0%"
	} else {
		passRate = fmt.Sprintf("%.2f%%", float64(planRecord.SuccessCase)/float64(planRecord.TotalCase)*100)
	}

	urlPlanDetail := fmt.Sprintf(
		"%s/#/ui-test/testplan/edit?project_id=%s&plan_id=%s&mode=edit",
		planRecord.Domain, notifyRequest.ProjectId, notifyRequest.PlanId,
	)
	probeReportURL := fmt.Sprintf(
		"%s/#/ui-test/testplan/reporter?project_id=%s&plan_id=%s&task_id=%s&execute_id=%s",
		planRecord.Domain, notifyRequest.ProjectId, notifyRequest.PlanId, notifyRequest.TaskId, notifyRequest.PlanExecuteId,
	)

	rawContent = &notifyContent{
		PlanName:       planRecord.PlanName,
		ModifyPlanURL:  urlPlanDetail,
		PlanId:         notifyRequest.PlanId,
		TaskId:         notifyRequest.TaskId,
		PlanExecuteId:  notifyRequest.PlanExecuteId,
		ProjectId:      notifyRequest.ProjectId,
		ExecuteId:      notifyRequest.PlanExecuteId,
		StartedAt:      startTime.Format("2006-01-02 15:04:05"),
		EndedAt:        endTime.Format("2006-01-02 15:04:05"),
		CostTime:       planRecord.CostTime,
		TotalCase:      planRecord.TotalCase,
		SuccessCase:    planRecord.SuccessCase,
		PassRate:       passRate,
		TriggerMode:    triggerModeString,
		PlanState:      planState,
		Color:          color,
		Executor:       executor,
		ExecutorLarkId: executorLarkId,
		ProbeReportURL: probeReportURL,
		ReportViewUrl:  notifyRequest.ReportViewUrl,
		AppVersion:     planRecord.AppVersion,
		AppDownloadUrl: planRecord.AppDownloadUrl,
		Domain:         planRecord.Domain,
	}
	// fmt.Println("rawContent!!!!!!!")
	// fmt.Println(rawContent.PlanState)
	// fmt.Println(rawContent.Color)

	return rawContent, nil
}

func generateUIPlanLarkGroupContent(rawContent *notifyContent) string {
	var planState string
	if rawContent.PlanState == "成功" {
		planState = "<font color=\"green\">成功</font>"
	} else {
		planState = "<font color=\"red\">失败</font>"
	}
	urlPlanDetail := fmt.Sprintf(
		"%s/#/ui-test/testplan/edit?project_id=%s&plan_id=%s&mode=edit",
		rawContent.Domain, rawContent.ProjectId, rawContent.PlanId,
	)
	content := fmt.Sprintf(
		"**UI测试计划：**[%s](%s)\n**任务ID：**%s\n**APP版本：**%s\n**APP下载地址：**%s\n**开始时间：**%s\n**结束时间：**%s\n"+
			"**触发模式：**%s\n**执行时长：**%s\n**成功用例数：**%d\n**执行用例数：**%d\n**成功率：**%s\n**状态：**%s\n**执行人：**%s",
		rawContent.PlanName, urlPlanDetail, rawContent.TaskId, rawContent.AppVersion, rawContent.AppDownloadUrl,
		rawContent.StartedAt, rawContent.EndedAt, rawContent.TriggerMode, rawContent.CostTime, rawContent.SuccessCase,
		rawContent.TotalCase, rawContent.PassRate, planState, rawContent.Executor,
	)

	probeReportURL := fmt.Sprintf(
		"%s/#/ui-test/testplan/reporter?project_id=%s&plan_id=%s&task_id=%s&execute_id=%s",
		rawContent.Domain, rawContent.ProjectId, rawContent.PlanId, rawContent.TaskId, rawContent.ExecuteId,
	)
	allureReportURL := rawContent.ReportViewUrl

	// 飞书群卡片消息 参考 https://open.feishu.cn/tool/cardbuilder?templateId=ctp_AAfBtyXFHXBt
	contentMap := map[string]any{
		"config": map[string]any{
			"wide_screen_mode": true,
		},
		"header": map[string]any{
			"template": "blue",
			"title": map[string]any{
				"content": "测试结果通知",
				"tag":     "plain_text",
			},
		},
		"elements": []map[string]any{
			{
				"tag":     "markdown",
				"content": content,
			},
			{
				"tag": "action",
				"actions": []map[string]any{
					{
						"tag": "button",
						"text": map[string]any{
							"tag":     "plain_text",
							"content": "查看平台报告",
						},
						"type": "primary",
						"multi_url": map[string]any{
							"url":         probeReportURL,
							"pc_url":      "",
							"android_url": "",
							"ios_url":     "",
						},
					},
					{
						"tag": "button",
						"text": map[string]any{
							"tag":     "plain_text",
							"content": "查看Allure报告",
						},
						"type": "primary",
						"multi_url": map[string]any{
							"url":         allureReportURL,
							"pc_url":      "",
							"android_url": "",
							"ios_url":     "",
						},
					},
				},
			},
		},
	}
	// 使用自定义的JsonMarshal而不是json包的Marshal能实现json序列化不转义
	// 参考https://blog.csdn.net/lgh1700/article/details/104522878
	b, _ := jsonMarshal(contentMap)
	contentStr := string(b)

	// fmt.Println("飞书！！！！！！1：")
	// fmt.Println(contentStr)

	return contentStr
}

type uiPlanEmailContent struct {
	PlanName       string `json:"planName"`
	PlanRecordUrl  string `json:"planRecordUrl"`
	TaskId         string `json:"taskId"`
	AppVersion     string `json:"appVersion"`
	AppDownloadUrl string `json:"appDownloadUrl"`
	StartedAt      string `json:"startedAt"`
	EndedAt        string `json:"endedAt"`
	TriggerMode    string `json:"triggerMode"`
	CostTime       string `json:"costTime"`
	SuccessCase    int64  `json:"successCase"`
	TotalCase      int64  `json:"totalCase"`
	PassRate       string `json:"passRate"`
	PlanState      string `json:"planState"`
	Color          string `json:"color"`
	Executor       string `json:"executor"`
	NoCaseServices string `json:"noCaseServices"`
}

func generateUIPlanEmailContent(rawContent *notifyContent) string {
	contentTemplate := `
		<table align="left" cellspacing="0" class="hostname" style="text-align:left">
			<tr>
				<td width=110><b>UI测试计划:</b>&nbsp;&nbsp;</td>
				<td><a href="{{ .PlanRecordUrl }}">{{ .PlanName }}</a></td>
			</tr>
			<tr>
				<td width=110><b>任务ID:</b>&nbsp;&nbsp;</td>
				<td>{{ .TaskId }}</td>
			</tr>
			<tr>
				<td width=110><b>APP版本:</b>&nbsp;&nbsp;</td>
				<td>{{ .AppVersion }}</td>
			</tr>
			<tr>
				<td width=110><b>APP下载地址:</b>&nbsp;&nbsp;</td>
				<td>{{ .AppDownloadUrl }}</td>
			</tr>
			<tr>
				<td width=110><b>开始时间:</b>&nbsp;&nbsp;</td>
				<td>{{ .StartedAt }}</td>
			</tr>
			<tr>
				<td width=110><b>结束时间:</b>&nbsp;&nbsp;</td>
				<td>{{ .EndedAt }}</td>
			</tr>
			<tr>
				<td width=110><b>触发模式:</b>&nbsp;&nbsp;</td>
				<td>{{ .TriggerMode }}</td>
			</tr>
			<tr>
				<td width=110><b>执行时长:</b>&nbsp;&nbsp;</td>
				<td>{{ .CostTime }}</td>
			</tr>
            <tr>
				<td width=110><b>成功用例数:</b>&nbsp;&nbsp;</td>
				<td>{{ .SuccessCase }}</td>
			</tr>
			<tr>
				<td width=110><b>执行用例数:</b>&nbsp;&nbsp;</td>
				<td>{{ .TotalCase }}</td>
			</tr>
			<tr>
				<td width=110><b>成功率:</b>&nbsp;&nbsp;</td>
				<td>{{ .PassRate }}</td>
			</tr>
			<tr>
				<td width=110><b>状态:</b>&nbsp;&nbsp;</td>
				<td><font color="{{.Color}}">{{ .PlanState }}</font></td>
			</tr>
			<tr>
				<td width=110><b>执行人:</b>&nbsp;&nbsp;</td>
				<td>{{ .Executor }}</td>
			</tr>
		</table>`

	tpl, _ := template.New("content").Parse(contentTemplate)

	planRecordUrl := rawContent.ReportViewUrl

	var stringBuilder strings.Builder
	wr := tabwriter.NewWriter(&stringBuilder, 0, 0, 0, ' ', 0)

	data := &uiPlanEmailContent{
		PlanName:       rawContent.PlanName,
		PlanRecordUrl:  planRecordUrl,
		TaskId:         rawContent.TaskId,
		AppVersion:     rawContent.AppVersion,
		AppDownloadUrl: rawContent.AppDownloadUrl,
		StartedAt:      rawContent.StartedAt,
		EndedAt:        rawContent.EndedAt,
		TriggerMode:    rawContent.TriggerMode,
		CostTime:       rawContent.CostTime,
		SuccessCase:    rawContent.SuccessCase,
		TotalCase:      rawContent.TotalCase,
		PassRate:       rawContent.PassRate,
		Color:          rawContent.Color,
		PlanState:      rawContent.PlanState,
		Executor:       rawContent.Executor,
	}
	_ = tpl.Execute(wr, data)
	_ = wr.Flush()

	contentStr := stringBuilder.String()

	// fmt.Println("email!!!!!!!!!!!!!!!:")
	// fmt.Println(contentStr)

	return contentStr
}

func realSendUINotify(
	notifyRequest *UINotifyRequest, logic *Callback, planRecord *UIPlanRecord, larkGroupReceivers,
	emailReceivers []string,
) error {
	var notifyItems []*notifierpb.NotifyItem

	// 生成通知内容
	rawContent, err := generateUIPlanRawContent(notifyRequest, planRecord)
	if err != nil {
		err = logic.UpdateFailureStatef(codes.TaskNotifyFailure, "%s", err)
		return err
	}

	// 添加SLA内容
	slaContent, err := generateUIPlanSLAContent(notifyRequest, planRecord)
	if err != nil {
		logic.Errorf("failed to generate SLA content, err: %+v", err)
	}
	rawContent.SLAContent = slaContent

	// 飞书群
	if len(larkGroupReceivers) > 0 {
		// content := generateUIPlanLarkGroupContent(rawContent)
		content, err := templateParse(uiReportTemplate, rawContent)
		if err != nil {
			logic.Errorf("failed to parse ui report template, err: %+v", err)
			return err
		}
		for _, larkGroupUrl := range larkGroupReceivers {
			notifyItem := notifierpb.NotifyItem{
				Item: &notifierpb.NotifyItem_LarkGroupChat{
					LarkGroupChat: &notificationservice.LarkGroupChat{
						WebhookUrl: larkGroupUrl,
						MsgType:    notifierpb.LarkMessageType_INTERACTIVE,
						Content:    content,
					},
				},
			}
			notifyItems = append(notifyItems, &notifyItem)
		}
	}

	// 邮件
	if len(emailReceivers) > 0 {
		content := generateUIPlanEmailContent(rawContent)
		notifyItem := notifierpb.NotifyItem{
			Item: &notifierpb.NotifyItem_Email{
				Email: &notificationservice.Email{
					Sender:    "",
					Receivers: emailReceivers,
					Subject:   fmt.Sprintf("测试计划「%s」执行结果通知", rawContent.PlanName),
					Content:   content,
				},
			},
		}
		notifyItems = append(notifyItems, &notifyItem)
	}

	_, err = notifyRequest.SCtx.NotifierRpc.Notify(
		notifyRequest.Ctx, &notificationservice.NotifyReq{Items: notifyItems},
	)
	if err != nil {
		err = logic.UpdateFailureStatef(codes.TaskNotifyFailure, "%s", err)
	}
	return err
}

type UINotifyRequest struct {
	SCtx          *svc.ServiceContext
	Ctx           context.Context
	ProjectId     string
	PlanId        string
	TaskId        string
	PlanExecuteId string
	ReportViewUrl string
}

func SendUINotify(notifyRequest *UINotifyRequest) error {
	logic := NewCallback(notifyRequest.Ctx, notifyRequest.SCtx)

	// 计划执行信息
	planRecord, err := getUIPlanRecord(notifyRequest)
	if err != nil {
		err = logic.UpdateFailureStatef(codes.TaskNotifyFailure, "%s", err)
		return err
	}

	// 计划发送通知的对象
	larkGroupReceivers, emailReceivers, err := dealUINotifyItem(notifyRequest, planRecord)
	if err != nil {
		err = logic.UpdateFailureStatef(codes.TaskNotifyFailure, "%s", err)
		return err
	}
	if len(larkGroupReceivers) == 0 && len(emailReceivers) == 0 {
		return nil
	}

	return realSendUINotify(notifyRequest, logic, planRecord, larkGroupReceivers, emailReceivers)
}

type slaTableItem struct {
	Udid                  string
	AvgFinishLaunchedTime string
	AvgAutoLoginTime      string
	AvgNewHomePageTime    string
}

type slaContent struct {
	Notify      bool
	BaseVersion string
	Developers  []*pb1.UserInfo
	SLATable    string
}

// SLA阈值，基准版本，开发人员，数据列表
func generateUIPlanSLAContent(notifyRequest *UINotifyRequest, planRecord *UIPlanRecord) (*slaContent, error) {
	slaContent := &slaContent{}
	platform, err := platformTypeToString(planRecord.PlatformType)
	if err != nil {
		return slaContent, err
	}

	testVersion := formatVersion(platform, planRecord.AppVersion)
	rsp1, err := notifyRequest.SCtx.ManagerRpc.GetSlaThreshold(notifyRequest.Ctx, &managerpb.GetSlaThresholdReq{
		ProjectId: notifyRequest.ProjectId,
	})
	if err != nil {
		return slaContent, err
	}
	branchType := inferBranchType(platform, testVersion)
	baseLine := fetchBaseLine(rsp1.Thresholds, planRecord.PlatformType, branchType)

	rsp2, err := notifyRequest.SCtx.SLAClient.GetComparisonData(&sla.GetComparisonDataReq{
		Platform:    platform,
		TaskID:      notifyRequest.TaskId,
		TestVersion: testVersion,
		BaseLine:    baseLine,
	})
	if err != nil {
		return slaContent, err
	}

	if len(rsp2.DeviceComparisonData) == 0 {
		return slaContent, nil
	} else {
		slaContent.Notify = true
		slaContent.BaseVersion = rsp2.ParticipatingVersion.BaseVersion
	}

	slaTable, devNotify := generateUIPlanSLATable(rsp2)
	if len(slaTable) > 0 {
		slaContent.SLATable = jsonx.MarshalToStringIgnoreError(slaTable)
	}

	if devNotify {
		rsp3, err := notifyRequest.SCtx.ManagerRpc.SearchSlaNotifier(notifyRequest.Ctx, &managerpb.SearchSlaNotifierReq{
			ProjectId: notifyRequest.ProjectId,
		})
		if err != nil {
			return slaContent, err
		}
		slaContent.Developers = selectDevelopers(planRecord.PlatformType, rsp3.GetItems())
	}

	return slaContent, nil
}

func selectDevelopers(pt commonpb.PlatformType, users []*pb1.UserInfo) []*pb1.UserInfo {
	var developers []*pb1.UserInfo
	for _, user := range users {
		switch pt {
		case commonpb.PlatformType_ANDROID:
			if strings.Contains(user.GetFullDeptName(), "Android") {
				developers = append(developers, user)
			}
		case commonpb.PlatformType_IOS:
			if strings.Contains(user.GetFullDeptName(), "iOS") {
				developers = append(developers, user)
			}
		}
	}
	return developers
}

func generateUIPlanSLATable(resp *sla.GetComparisonDataResp) ([]*slaTableItem, bool) {
	var (
		table  []*slaTableItem
		notify bool

		finishLaunchedMaps = make(map[string]lang.PlaceholderType)
		autoLoginMaps      = make(map[string]lang.PlaceholderType)
		newHomePageMaps    = make(map[string]lang.PlaceholderType)
	)

	ipon := resp.DeviceIsPassedOrNotData
	if len(ipon.FinishLaunchedTimeFailList) > 0 {
		notify = true
		for _, udid := range ipon.FinishLaunchedTimeFailList {
			finishLaunchedMaps[udid] = lang.Placeholder
		}
	}
	if len(ipon.AutoLoginFailList) > 0 {
		notify = true
		for _, udid := range ipon.AutoLoginFailList {
			autoLoginMaps[udid] = lang.Placeholder
		}
	}
	if len(ipon.NewHomePageFailList) > 0 {
		notify = true
		for _, udid := range ipon.NewHomePageFailList {
			newHomePageMaps[udid] = lang.Placeholder
		}
	}

	for _, dcd := range resp.DeviceComparisonData {
		testData := dcd.TestData
		if testData != nil {
			udid := testData.DeviceName
			table = append(table, &slaTableItem{
				Udid: udid,
				AvgFinishLaunchedTime: fmt.Sprintf(
					htmlFontFormat,
					setSLADataColor(udid, finishLaunchedMaps, testData.FinishLaunchedPercent),
					fmt.Sprintf("%d（%.1f%%）", testData.AvgFinishLaunchedTime, testData.FinishLaunchedPercent),
				),
				AvgAutoLoginTime: fmt.Sprintf(
					htmlFontFormat,
					setSLADataColor(udid, autoLoginMaps, testData.AutoLoginPercent),
					fmt.Sprintf("%d（%.1f%%）", testData.AvgAutoLogin, testData.AutoLoginPercent),
				),
				AvgNewHomePageTime: fmt.Sprintf(
					htmlFontFormat,
					setSLADataColor(udid, newHomePageMaps, testData.NewHomePagePercent),
					fmt.Sprintf("%d（%.1f%%）", testData.AvgNewHomePage, testData.NewHomePagePercent),
				),
			})
		}
	}

	return table, notify
}

func setSLADataColor(udid string, failMaps map[string]lang.PlaceholderType, percent float64) color {
	if _, ok := failMaps[udid]; ok {
		return red
	}
	switch {
	case percent > 0:
		return yellow
	case percent < 0:
		return green
	default:
		return black
	}
}

func platformTypeToString(pt commonpb.PlatformType) (string, error) {
	switch pt {
	case commonpb.PlatformType_ANDROID:
		return "Android", nil
	case commonpb.PlatformType_IOS:
		return "IOS", nil
	default:
		return "", fmt.Errorf("unabled to support the platform_type: %s", pt.String())
	}
}

func formatVersion(platform, version string) string {
	if strings.Contains(version, "-") {
		switch platform {
		case "Android":
			// 6.67.5-18809 => 6.67.5 18809
			version = strings.ReplaceAll(version, "-", " ")
		case "IOS":
			// 6.68.0-3917062 => Ver_6.68.0/build_3917062
			version = strings.ReplaceAll("Ver_"+version, "-", "/build_")
		}
	}
	return version
}

func inferBranchType(platform, version string) commonpb.BranchType {
	bt := commonpb.BranchType_BranchType_RELEASE
	switch platform {
	case "Android":
		vb := strings.Split(version, " ")
		if len(vb) > 1 {
			build, err := strconv.Atoi(vb[1])
			if err == nil && build > 30000 {
				bt = commonpb.BranchType_BranchType_TESTING
			}
		}
	case "IOS":
		// IOS只有生产包
	}
	return bt
}

func fetchBaseLine(thresholds []*managerpb.SlaThreshold, pt commonpb.PlatformType, bt commonpb.BranchType) *commontypes.SLABaseLine {
	baseLine := &commontypes.SLABaseLine{}
	for _, threshold := range thresholds {
		if threshold.PlatformType == pt && threshold.BranchType == bt {
			switch threshold.GetName() {
			case string(constants.SLA_Threshold_Finish_Launched):
				baseLine.FinishLaunchedLine = threshold.GetValue()
			case string(constants.SLA_Threshold_Auto_Login):
				baseLine.AutoLoginLine = threshold.GetValue()
			case string(constants.SLA_Threshold_New_Home_Page):
				baseLine.NewHomePageLine = threshold.GetValue()
			}
		}
	}
	return baseLine
}
