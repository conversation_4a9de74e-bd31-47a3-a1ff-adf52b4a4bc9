package callback

import (
	"fmt"
	"time"

	"google.golang.org/protobuf/types/known/timestamppb"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/codes"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/common/callbackclient"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/common/dtctl"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/rpc/pb"
	managerpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
	reporterpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
)

type PerfCaseNode struct {
	*BaseNode
}

func NewPerfCaseNode(task *Callback) *PerfCaseNode {
	node := &PerfCaseNode{
		BaseNode: NewBaseNode(task),
	}
	return node
}

func (node *PerfCaseNode) Type() managerpb.ApiExecutionDataType {
	return managerpb.ApiExecutionDataType_PERF_CASE
}

func (node *PerfCaseNode) GetDtControl() *dtctl.DispatchTaskControl {
	if node.ctl != nil {
		return node.ctl
	}

	source := node.Task().Source()
	member := &dtctl.DispatchTaskMember{
		ComponentId:        source.GetPerfCase().GetPerfSuiteId(),
		ComponentType:      managerpb.ApiExecutionDataType_PERF_SUITE,
		ComponentExecuteId: source.GetPerfCase().GetPerfSuiteExecuteId(),
	}
	node.ctl = dtctl.NewDispatchTaskControl(node.Task().Context(), node.Task().ServiceContext().Redis, member)
	return node.ctl
}

func (node *PerfCaseNode) GetMember() *dtctl.DispatchTaskMember {
	source := node.Task().Source()
	member := &dtctl.DispatchTaskMember{
		ComponentId:        source.GetPerfCase().GetPerfCaseId(),
		ComponentType:      managerpb.ApiExecutionDataType_PERF_CASE,
		ComponentExecuteId: source.GetPerfCase().GetPerfCaseExecuteId(),
	}
	return member
}

func (node *PerfCaseNode) State() pb.ComponentState {
	return node.Task().source.GetPerfCase().GetCaseState()
}

func (node *PerfCaseNode) SetState(s pb.ComponentState) {
	node.Task().source.GetPerfCase().CaseState = s
}

func (node *PerfCaseNode) TaskInfoProcessorSync() {
	source := node.Task().Source()
	switch node.State() {
	case pb.ComponentState_Failure, pb.ComponentState_Stop, pb.ComponentState_Panic:
	case pb.ComponentState_Success:
		err := node.BaseNode.task.ServiceContext().TaskInfoProcessor.UpdateSuccessCaseForIncr(
			node.BaseNode.task.Context(), source.GetProjectId(), source.GetTaskId(),
		)
		if err != nil {
			node.task.Errorf(
				"failed to update the `success_case` field, task_id: %s, execute_id: %s, case_id: %s, error: %+v",
				source.GetTaskId(), source.GetPerfCase().GetPerfCaseExecuteId(), source.GetPerfCase().GetPerfCaseId(),
				err,
			)
		}
	default:
		return
	}

	err := node.BaseNode.task.ServiceContext().TaskInfoProcessor.UpdateFinishedCaseForIncr(
		node.BaseNode.task.Context(), source.GetProjectId(), source.GetTaskId(),
	)
	if err != nil {
		node.task.Errorf(
			"failed to update the `finished_case` field, task_id: %s, execute_id: %s, case_id: %s, error: %+v",
			source.GetTaskId(), source.GetPerfCase().GetPerfCaseExecuteId(), source.GetPerfCase().GetPerfCaseId(), err,
		)
	}
}

func (node *PerfCaseNode) Record() (err error) {
	source := node.Task().Source()
	state := node.Task().StateM().State()

	var errMsg *reporterpb.ErrorMessage
	if state == pb.ComponentState_Panic {
		record, err := node.Task().ServiceContext().PerfReporterRpc.GetPerfCaseRecord(
			node.Task().Context(), &reporterpb.GetPerfCaseRecordReq{
				TaskId:    source.GetTaskId(),
				ExecuteId: source.GetPerfCase().GetPerfCaseExecuteId(),
				ProjectId: source.GetProjectId(),
			},
		)
		if err != nil {
			return err
		}
		errMsg = record.GetRecord().GetErrMsg()
	}

	_, err = node.Task().ServiceContext().PerfReporterRpc.ModifyPerfSuiteRecord(
		node.Task().Context(), &reporterpb.ModifyPerfSuiteRecordReq{
			TaskId:        source.GetTaskId(),
			ExecuteId:     source.GetPerfCase().GetPerfSuiteExecuteId(), // 这里是父节点的execute_id
			ProjectId:     source.GetProjectId(),
			SuiteId:       source.GetPerfCase().GetPerfSuiteId(),
			PlanExecuteId: source.GetPerfCase().GetPerfSuiteExecuteId(),
			Status:        state.String(),
			EndedAt:       timestamppb.New(time.Now()),
			ErrMsg:        errMsg,
		},
	)
	return err
}

func (node *PerfCaseNode) RecordCallback() (err error) {
	source := node.Task().Source()
	_, err = node.Task().ServiceContext().PerfReporterRpc.ModifyPerfCaseRecord(
		node.Task().Context(), &reporterpb.ModifyPerfCaseRecordReq{
			TaskId:         source.GetTaskId(),
			ProjectId:      source.GetProjectId(),
			ExecuteId:      source.GetPerfCase().GetPerfCaseExecuteId(),
			CaseId:         source.GetPerfCase().GetPerfCaseId(),
			SuiteExecuteId: source.GetPerfCase().GetPerfSuiteExecuteId(),
			Status:         source.GetPerfCase().GetCaseState().String(),
			EndedAt:        timestamppb.New(time.Now()),
		},
	)
	return err
}

func (node *PerfCaseNode) Teardown() (err error) {
	if node.Task().Source().GetExecuteType() != managerpb.ApiExecutionDataType_PERF_PLAN {
		return nil
	}

	source := node.Task().Source()
	cb := &pb.CallbackReq{
		TriggerMode:  source.GetTriggerMode(),
		TriggerRule:  source.GetTriggerRule(),
		ProjectId:    source.GetProjectId(),
		TaskId:       source.GetTaskId(),
		ExecuteType:  source.GetExecuteType(),
		CallbackType: pb.CallbackType_CallbackType_PERF_SUITE,
		Data: &pb.CallbackReq_PerfSuite{
			PerfSuite: &pb.PerfSuiteCallbackData{
				PerfPlanId:         source.GetPerfCase().GetPerfPlanId(),
				PerfPlanExecuteId:  source.GetPerfCase().GetPerfPlanExecuteId(),
				PerfSuiteId:        source.GetPerfCase().GetPerfSuiteId(),
				PerfSuiteExecuteId: source.GetPerfCase().GetPerfSuiteExecuteId(),
				SuiteState:         node.Task().StateM().State(),
				MetaData:           source.GetPerfCase().GetMetaData(),
			},
		},
	}

	err = callbackclient.CallBack(
		node.Task().Context(),
		node.Task().ServiceContext().DispatcherProducer,
		node.Task().ServiceContext().Config.Name,
		node.Task().ServiceContext().Config.DispatcherProducer.Queue,
		node.getSuiteUUID(),
		cb,
	)
	if err != nil {
		return node.Task().UpdateFailureStatef(codes.TaskPublishFailure, "%s", err)
	}

	return nil
}

func (node *PerfCaseNode) getSuiteUUID() string {
	source := node.Task().Source()
	return fmt.Sprintf("%s::%s", source.GetTaskId(), source.GetCase().GetSuiteExecuteId())
}
