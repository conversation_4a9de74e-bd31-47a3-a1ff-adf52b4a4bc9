package callback

import (
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/codes"
	commonpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
	commonutils "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/utils"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/common/dtctl"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/common/utils"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/mqc/internal/logic/baselogic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/mqc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/rpc/pb"
)

type Callback struct {
	*baselogic.BaseLogic

	source *pb.CallbackReq
	logW   *CaseCallbackLogWriter
	ctl    *dtctl.DispatchTaskControl

	total   int64
	finish  int64
	success int64
	failure int64
}

func NewCallback(ctx context.Context, svcCtx *svc.ServiceContext) *Callback {
	return &Callback{
		BaseLogic: baselogic.NewBaseLogic(ctx, svcCtx),
	}
}

func (x *Callback) Setup(req *pb.CallbackReq) {
	x.BaseLogic.Setup()
	x.source = req
	x.logW = NewCaseCallbackLogWriter(x.BaseLogic.LogWriter())
}

func (x *Callback) Source() *pb.CallbackReq {
	return x.source
}

func (x *Callback) Run(req *pb.CallbackReq) (err error) {
	x.Setup(req)

	err = x.run()

	x.Teardown() // Callback没有好这个函数注册

	return err
}

func (x *Callback) run() (err error) {
	x.logW.setMqRequest(x.Source())

	x.Infof("Callback.run(), source=%s", jsonx.MarshalToStringIgnoreError(x.Source()))

	// 精准测试类型
	if x.Source().GetPurposeType() == commonpb.PurposeType_PRECISION_TESTING {
		switch x.Source().GetCallbackType() {
		case pb.CallbackType_CallbackType_API_CASE:
			return x.RunNode(NewServiceApiCaseNode(x))
		case pb.CallbackType_CallbackType_INTERFACE_CASE:
			return x.RunNode(NewServiceInterfaceCaseNode(x))
		case pb.CallbackType_CallbackType_API_SERVICE:
			return x.RunNode(NewServiceNode(x))
		}
	}

	switch x.Source().GetCallbackType() {
	case pb.CallbackType_CallbackType_API_CASE:
		return x.RunNode(NewApiCaseNode(x))
	case pb.CallbackType_CallbackType_API_SUITE:
		return x.RunNode(NewSuiteNode(x))
	case pb.CallbackType_CallbackType_INTERFACE_CASE:
		if strings.HasPrefix(x.Source().GetInterfaceCase().GetInterfaceId(), commonutils.ConstSuiteIdPrefix) {
			return x.RunNode(NewInterfaceCaseInSuiteNode(x))
		}
		return x.RunNode(NewInterfaceCaseNode(x))
	case pb.CallbackType_CallbackType_INTERFACE_DOCUMENT:
		return x.RunNode(NewInterfaceDocumentNode(x))
	case pb.CallbackType_CallbackType_UI_CASE:
		return x.RunNode(NewUICaseNode(x))
	case pb.CallbackType_CallbackType_UI_SUITE:
		return x.RunNode(NewUISuiteNode(x))
	case pb.CallbackType_CallbackType_PERF_CASE:
		return x.RunNode(NewPerfCaseNode(x))
	case pb.CallbackType_CallbackType_PERF_SUITE:
		return x.RunNode(NewPerfSuiteNode(x))
	}

	return errors.Errorf("未实现类型: %s", x.Source().GetCallbackType().String())
}

func (x *Callback) RunNode(node Node) (err error) {
	defer func() {
		x.Infof("Callback : %s", x.Content())
		_ = node.RecordCallback()

		if err != nil {
			_ = x.Panic(node, err)
		}
	}()

	// 如果是异常节点，则直接结束
	if node.State() == pb.ComponentState_Panic {
		return x.Panic(node, fmt.Errorf("异常回调"))
	}

	// 如果任务已经停止，则直接返回
	yes, err := utils.GetStopStatus(x.Context(), x.ServiceContext().Redis, x.Source().GetTaskId())
	if err != nil {
		return node.Task().UpdateFailureStatef(codes.TaskDBFailure, "GetStopStatus: %s", err)
	}
	if yes {
		x.UpdateState(pb.ComponentState_Stop, errorx.OK)
		return nil
	}

	ctl := node.GetDtControl()
	// 添加完成的成员状态
	err = ctl.AddMemberState(node.State(), node.GetMember())
	if err != nil {
		return node.Task().UpdateFailureStatef(codes.TaskDBFailure, "AddMemberState: %s", err)
	}
	// 记录
	x.total, _ = ctl.Total()
	x.finish, _ = ctl.FinishCount()
	x.logW.setTotal(x.total)
	x.logW.setFinished(x.finish)
	// 同步任务信息
	node.TaskInfoProcessorSync()

	// 如果不是最后一次回调，则退出
	if !ctl.Finished() {
		return nil
	}

	return x.FinishNode(node)
}

// FinishNode 最后一条回调时的执行
func (x *Callback) FinishNode(node Node) (err error) {
	defer func() {
		x.Infof("Callback.FinishNode : %s", x.Content())

		err = x.Close(node)
	}()

	if x.StateM().State() == pb.ComponentState_Panic {
		return nil
	}

	ctl := node.GetDtControl()
	total, err := ctl.Total()
	if err != nil {
		return x.UpdateFailureStatef(codes.TaskDBFailure, "Total: %s", err)
	}

	x.failure, err = ctl.FailureCount()
	if err != nil {
		return x.UpdateFailureStatef(codes.TaskDBFailure, "FailureCount: %s", err)
	}

	x.success = total - x.failure
	x.logW.setSucc(x.success)
	x.logW.setFail(x.failure)

	if x.failure > 0 {
		x.UpdateState(pb.ComponentState_Failure, codes.TaskExecuteCaseFailure)
	}

	x.UpdateState(pb.ComponentState_Success, errorx.OK)
	return nil
}

func (x *Callback) Close(node Node) (err error) {
	err = node.Record()
	if err != nil {
		return x.UpdateFailureStatef(codes.TaskRecordFailure, "%s", err)
	}

	err = node.GetDtControl().Clear()
	if err != nil {
		return x.UpdateFailureStatef(codes.TaskDBFailure, "Clear: %s", err)
	}

	return node.Teardown()
}

func (x *Callback) Content() string {
	return x.logW.toJson()
}

func (x *Callback) Success() int64 {
	return x.success
}

func (x *Callback) Failure() int64 {
	return x.failure
}

func (x *Callback) Panic(node Node, e error) (err error) {
	_ = x.UpdatePanicStatef(
		codes.TaskPanic, "Callback panic node: type: %s, id: %s, execute_id: %s, err: %s",
		node.GetMember().ComponentType, node.GetMember().ComponentId, node.GetMember().ComponentExecuteId, e,
	)
	err = utils.SetStopStatus(
		x.Context(), x.ServiceContext().Redis, x.Source().GetTaskId(), &pb.StopMetadata{
			StopType: pb.StopType_StopType_Auto,
			Reason:   "异常回调",
		},
	)
	if err != nil {
		return err
	}
	x.Infof(
		"Callback Panic[%s] : taskId: %s, error: %+v", commonpb.ExecutedResult_TER_PANIC, x.Source().GetTaskId(), e,
	)

	// 有一个恐慌 直接进入终态
	_ = x.ServiceContext().TaskInfoProcessor.UpdateTaskExecutedResult(
		x.Context(), x.Source().GetProjectId(), x.Source().GetTaskId(), commonpb.ExecutedResult_TER_PANIC,
		time.Now().UnixMilli(),
	)
	return x.FinishNode(node)
}
