{
    "schema": "2.0",
    "config": {
        "update_multi": true,
        "style": {
            "text_size": {
                "normal_v2": {
                    "default": "normal",
                    "pc": "normal",
                    "mobile": "heading"
                }
            }
        }
    },
    "body": {
        "direction": "vertical",
        "padding": "12px 12px 12px 12px",
        "elements": [
            {
                "tag": "column_set",
                "horizontal_spacing": "8px",
                "horizontal_align": "left",
                "columns": [
                    {
                        "tag": "column",
                        "width": "weighted",
                        "elements": [
                            {
                                "tag": "markdown",
                                "content": "**计划名称：** [{{ .PlanName }}]",
                                "text_align": "left",
                                "text_size": "normal_v2",
                                "margin": "0px 0px 0px 0px",
                                "icon": {
                                    "tag": "standard_icon",
                                    "token": "livestream-content_outlined",
                                    "color": "grey"
                                }
                            },
                            {
                                "tag": "markdown",
                                "content": "**任务ID：** {{ .TaskId }}",
                                "text_align": "left",
                                "text_size": "normal_v2",
                                "margin": "0px 0px 0px 0px",
                                "icon": {
                                    "tag": "standard_icon",
                                    "token": "view-task_outlined",
                                    "color": "grey"
                                }
                            },
                            {
                                "tag": "markdown",
                                "content": "**开始时间：** {{ .StartedAt }}",
                                "text_align": "left",
                                "text_size": "normal_v2",
                                "margin": "0px 0px 0px 0px",
                                "icon": {
                                    "tag": "standard_icon",
                                    "token": "calendar_outlined",
                                    "color": "grey"
                                }
                            },
                            {
                                "tag": "markdown",
                                "content": "**结束时间：** {{ .EndedAt }}",
                                "text_align": "left",
                                "text_size": "normal_v2",
                                "margin": "0px 0px 0px 0px",
                                "icon": {
                                    "tag": "standard_icon",
                                    "token": "calendar-done_outlined",
                                    "color": "grey"
                                }
                            },
                            {
                                "tag": "markdown",
                                "content": "**触发模式：** {{ .TriggerMode }}",
                                "text_align": "left",
                                "text_size": "normal_v2",
                                "margin": "0px 0px 0px 0px",
                                "icon": {
                                    "tag": "standard_icon",
                                    "token": "cursor_outlined",
                                    "color": "grey"
                                }
                            },
                            {
                                "tag": "markdown",
                                "content": "**执行时长：** {{ .CostTime }}",
                                "text_align": "left",
                                "text_size": "normal_v2",
                                "margin": "0px 0px 0px 0px",
                                "icon": {
                                    "tag": "standard_icon",
                                    "token": "time_outlined",
                                    "color": "grey"
                                }
                            },
                            {
                                "tag": "markdown",
                                "content": "**成功用例数：** {{ .SuccessCase }}",
                                "text_align": "left",
                                "text_size": "normal_v2",
                                "margin": "0px 0px 0px 0px",
                                "icon": {
                                    "tag": "standard_icon",
                                    "token": "phone_outlined",
                                    "color": "grey"
                                }
                            },
                            {
                                "tag": "markdown",
                                "content": "**执行用例数：** {{ .TotalCase }}",
                                "text_align": "left",
                                "text_size": "normal_v2",
                                "margin": "0px 0px 0px 0px",
                                "icon": {
                                    "tag": "standard_icon",
                                    "token": "phone_outlined",
                                    "color": "grey"
                                }
                            },
                            {
                                "tag": "markdown",
                                "content": "**成功率：** {{ .PassRate }}",
                                "text_align": "left",
                                "text_size": "normal_v2",
                                "margin": "0px 0px 0px 0px",
                                "icon": {
                                    "tag": "standard_icon",
                                    "token": "poll_outlined",
                                    "color": "grey"
                                }
                            },
                            {
                                "tag": "markdown",
                                "content": "**状态：** <font color='{{ .Color }}'>{{ .PlanState }}</font>",
                                "text_align": "left",
                                "text_size": "normal_v2",
                                "margin": "0px 0px 0px 0px",
                                "icon": {
                                    "tag": "standard_icon",
                                    "token": "status-vacation_outlined",
                                    "color": "grey"
                                }
                            },
                            {{ if .Services }}
                            {
                                "tag": "markdown",
                                "content": "**涉及服务：** {{ .Services }}",
                                "text_align": "left",
                                "text_size": "normal_v2",
                                "margin": "0px 0px 0px 0px",
                                "icon": {
                                    "tag": "standard_icon",
                                    "token": "member_outlined",
                                    "color": "grey"
                                }
                            },
                            {{ end }}
                            {{ if .ApproverLarkIds }}
                            {
                                "tag": "markdown",
                                "content": "**审批人：** <person id = '{{ .ApproverLarkIds }}' show_name = true show_avatar = true style = 'normal'></person>",
                                "text_align": "left",
                                "text_size": "normal_v2",
                                "margin": "0px 0px 0px 0px",
                                "icon": {
                                    "tag": "standard_icon",
                                    "token": "member_outlined",
                                    "color": "grey"
                                }
                            },
                            {{ end }}
                            {
                                "tag": "markdown",
                                "content": "**执行人：** {{ if .ExecutorLarkId }}<person id = '{{ .ExecutorLarkId }}' show_name = true show_avatar = true style = 'normal'></person>{{ else }}{{ .Executor }}{{ end }}",
                                "text_align": "left",
                                "text_size": "normal_v2",
                                "margin": "0px 0px 0px 0px",
                                "icon": {
                                    "tag": "standard_icon",
                                    "token": "member_outlined",
                                    "color": "grey"
                                }
                            }
                        ],
                        "vertical_spacing": "8px",
                        "horizontal_align": "left",
                        "vertical_align": "top",
                        "weight": 1
                    }
                ],
                "margin": "0px 0px 0px 0px"
            },
            {
                "tag": "hr",
                "margin": "0px 0px 0px 0px"
            },
            {{ if .FailedCaseInfo }}
            {
                "tag": "column_set",
                "horizontal_spacing": "8px",
                "horizontal_align": "left",
                "columns": [
                    {
                        "tag": "column",
                        "width": "weighted",
                        "elements": [
                            {
                                "tag": "markdown",
                                "content": "**失败用例列表：**",
                                "text_align": "left",
                                "text_size": "normal_v2",
                                "margin": "0px 0px 0px 0px",
                                "icon": {
                                    "tag": "standard_icon",
                                    "token": "disorder-list_outlined",
                                    "color": "grey"
                                }
                            }
                        ],
                        "vertical_spacing": "8px",
                        "horizontal_align": "left",
                        "vertical_align": "top",
                        "weight": 1
                    }
                ],
                "margin": "0px 0px 0px 0px"
            },
            {
                "tag": "table",
                "columns": [
                    {
                        "data_type": "text",
                        "name": "CaseName",
                        "display_name": "用例",
                        "horizontal_align": "left",
                        "width": "auto"
                    },
                    {
                        "data_type": "text",
                        "name": "Status",
                        "display_name": "状态",
                        "horizontal_align": "left",
                        "width": "auto"
                    },
                    {
                        "data_type": "persons",
                        "name": "Maintainer",
                        "display_name": "负责人",
                        "horizontal_align": "left",
                        "width": "auto"
                    }
                ],
                "rows": [
                    {{range $index, $item := .FailedCaseInfo.Items}}
                    {{ if gt $index 0 }},{{ end }}
                    {
                        "CaseName": "{{ $item.CaseName }}",
                        "Status": "{{ $item.Status }}",
                        "Maintainer": [
                            "{{ $item.LarkId }}"
                        ]
                    }
                    {{ end }}
                ],
                "row_height": "low",
                "header_style": {
                    "background_style": "none",
                    "bold": true,
                    "lines": 1
                },
                "page_size": 3
            },
            {
                "tag": "markdown",
                "content": "**失败用例负责人：**<at ids={{ range $index, $item := .FailedCaseInfo.LarkIds }}{{ if gt $index 0 }},{{ end }}{{ $item }}{{ end }}></at>",
                "text_align": "left",
                "text_size": "normal_v2",
                "margin": "0px 0px 0px 0px",
                "icon": {
                    "tag": "standard_icon",
                    "token": "group_outlined",
                    "color": "grey"
                }
            },
            {{ end }}
            {
                "tag": "hr",
                "margin": "0px 0px 0px 0px"
            },
            {
                "tag": "column_set",
                "horizontal_spacing": "28px",
                "horizontal_align": "left",
                "columns": [
                    {
                        "tag": "column",
                        "width": "weighted",
                        "elements": [
                            {
                                "tag": "button",
                                "text": {
                                    "tag": "plain_text",
                                    "content": "详细报告"
                                },
                                "type": "primary",
                                "width": "default",
                                "size": "medium",
                                "icon": {
                                    "tag": "standard_icon",
                                    "token": "lookup_outlined"
                                },
                                "behaviors": [
                                    {
                                        "type": "open_url",
                                        "default_url": "{{ .ProbeReportURL }}",
                                        "pc_url": "",
                                        "ios_url": "",
                                        "android_url": ""
                                    }
                                ]
                            }
                        ],
                        "direction": "horizontal",
                        "horizontal_spacing": "8px",
                        "vertical_spacing": "8px",
                        "horizontal_align": "left",
                        "vertical_align": "top",
                        "weight": 1
                    }
                ],
                "margin": "0px 99px 0px 0px"
            }
        ]
    },
    "header": {
        "title": {
            "tag": "plain_text",
            "content": "接口测试报告"
        },
        "subtitle": {
            "tag": "plain_text",
            "content": ""
        },
        "template": "blue",
        "padding": "12px 12px 12px 12px"
    }
}