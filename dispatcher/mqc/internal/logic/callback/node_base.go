package callback

import (
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/common/dtctl"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/rpc/pb"
	managerpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type Node interface {
	Task() *Callback
	Type() managerpb.ApiExecutionDataType
	GetDtControl() *dtctl.DispatchTaskControl // 当前层级 下一层是子层
	GetMember() *dtctl.DispatchTaskMember
	Record() error         // 记录执行结果
	RecordCallback() error // 记录回调信息
	Teardown() error
	State() pb.ComponentState
	SetState(pb.ComponentState)
	TaskInfoProcessorSync()
}

type BaseNode struct {
	task *Callback
	ctl  *dtctl.DispatchTaskControl
}

func NewBaseNode(task *Callback) *BaseNode {
	node := &BaseNode{
		task: task,
	}
	return node
}

func (node *BaseNode) Task() *Callback {
	return node.task
}
