package callback

import (
	"encoding/base64"

	"github.com/zeromicro/go-zero/core/service"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/common/mock"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/mqc/internal/config"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/mqc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/rpc/pb"
)

func Mock_ServiceContext() *svc.ServiceContext {
	mock.Mock_Log()

	return &svc.ServiceContext{
		Config: config.Config{
			ServiceConf: service.ServiceConf{
				Name: "mqc.dispatcher",
			},
		},
		Redis:         mock.Mock_Redis(),
		ReporterRpc:   mock.Mock_ReporterRpc(),
		DispatcherRpc: mock.Mock_DispatcherRpc(),
	}
}

func Mock_default_Callback() *Callback {
	return NewCallback(mock.Mock_Context(), Mock_ServiceContext())
}

func Mock_CallbackMqReq_Base64(base64pb string) *pb.CallbackReq {
	cbpb, err := base64.StdEncoding.DecodeString(base64pb)
	if err != nil {
		panic(any("无效的task信息,不是base64, err:" + err.Error()))
	}

	cbData := &pb.CallbackReq{}
	err = protobuf.UnmarshalJSON(cbpb, cbData)
	if err != nil {
		panic(any("无效的taskpb, err:" + err.Error()))
	}

	return cbData
}
