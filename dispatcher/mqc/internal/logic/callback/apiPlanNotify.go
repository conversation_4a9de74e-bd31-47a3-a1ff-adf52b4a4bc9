package callback

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"html/template"
	"strings"
	"text/tabwriter"
	"time"

	"github.com/zeromicro/go-zero/core/logx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-middleware/notifier/rpc/client/notificationservice"
	notifierpb "gitlab.ttyuyin.com/TestDevelopment/qet-backend-middleware/notifier/rpc/pb"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-middleware/user/rpc/client/userservice"

	commonpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/mqc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/rpc/pb"
	larkproxypb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/larkproxy/rpc/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/client/notifyservice"
	managerpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
	reporterpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
)

type PlanRecord struct {
	PlanId                 string
	PlanName               string
	TriggerMode            string
	ExecutedBy             string
	StartedAt              int64
	EndedAt                int64
	CostTime               string
	TotalSuite             int64
	SuccessSuite           int64
	FailureSuite           int64
	TotalCase              int64
	SuccessCase            int64
	FailureCase            int64
	NoCaseServiceStr       string
	Services               []string
	Approvers              []string
	SuiteItems             []*SuiteItem
	InterfaceDocumentItems []*InterfaceDocumentItem
	ServiceItems           []*ServiceItem
}

type SuiteItem struct {
	TaskId         string
	ExecuteId      string
	ProjectId      string
	SuiteExecuteId string
	SuiteId        string
	SuiteName      string
	TotalCase      int64
	SuccessCase    int64
	FailureCase    int64
	StartedAt      int64
	EndedAt        int64
	CostTime       int64
	Status         string
}

type InterfaceDocumentItem struct {
	TaskId             string
	ExecuteId          string
	ProjectId          string
	InterfaceExecuteId string
	InterfaceId        string
	InterfaceName      string
	TotalCase          int64
	SuccessCase        int64
	FailureCase        int64
	StartedAt          int64
	EndedAt            int64
	CostTime           int64
	Status             string
}

type ServiceItem struct {
	TaskId           string
	ExecuteId        string
	ProjectId        string
	ServiceExecuteId string
	ServiceId        string
	ServiceName      string
	TotalCase        int64
	SuccessCase      int64
	FailureCase      int64
	StartedAt        int64
	EndedAt          int64
	CostTime         int64
	Status           string
}

type failedCaseInfoItem struct {
	PlanName   string
	CaseId     string
	CaseName   string
	StartedAt  int64
	EndedAt    int64
	CostTime   int64
	Status     string
	Type       pb.CallbackType
	Maintainer string
	LarkId     string
}

type failedCaseInfo struct {
	Items   []*failedCaseInfoItem
	LarkIds []string
}

func getPlanRecord(req *notifyRequest) (*PlanRecord, error) {
	// 计划执行信息
	var err error

	var planExecuteId string
	switch req.source.GetCallbackType() {
	case pb.CallbackType_CallbackType_API_SUITE:
		planExecuteId = req.source.GetSuite().GetPlanExecuteId()
	case pb.CallbackType_CallbackType_INTERFACE_DOCUMENT:
		planExecuteId = req.source.GetInterfaceDocument().GetPlanExecuteId()
	case pb.CallbackType_CallbackType_API_SERVICE:
		planExecuteId = req.source.GetService().GetPlanExecuteId()
	default:
		return nil, errorx.Errorf(
			errorx.InternalError,
			"invalid callback type, exepected: [%s or %s or %s], but got %s",
			pb.CallbackType_CallbackType_API_SUITE, pb.CallbackType_CallbackType_INTERFACE_DOCUMENT,
			pb.CallbackType_CallbackType_API_SERVICE, req.source.GetCallbackType(),
		)
	}

	respPlanRecord, err := req.sCtx.ReporterRpc.GetPlanRecord(
		req.ctx, &reporterpb.GetPlanRecordRequest{
			ProjectId:     req.source.GetProjectId(),
			TaskId:        req.source.GetTaskId(),
			PlanExecuteId: planExecuteId,
		},
	)
	if err != nil {
		return nil, err
	}

	// hours := respPlanRecord.CostTime / 3600000
	// minutes := (respPlanRecord.CostTime % 3600000) / 60000
	// seconds := (respPlanRecord.CostTime % 60000) / 1000
	// costTime := fmt.Sprintf("%dh %dm %ds", hours, minutes, seconds)
	// costTime := fmt.Sprintf("%02dh:%02dm:%02ds", hours, minutes, seconds)

	duration := time.Duration(respPlanRecord.GetCostTime() * 1000 * 1000)
	costTime := duration.String()

	var services []string
	var noCaseServices []string
	for _, value := range respPlanRecord.GetServiceCasesContent().GetValues() {
		serviceCases := value.GetStructValue().GetFields()
		cases := serviceCases["cases"].GetListValue().GetValues()
		service := serviceCases["service"]
		if len(cases) == 0 {
			noCaseServices = append(noCaseServices, service.String())
		}
		services = append(services, service.GetStringValue())
	}
	noCaseServiceStr := strings.Join(noCaseServices, "、")

	planRecord := &PlanRecord{
		PlanId:           respPlanRecord.GetPlanId(),
		PlanName:         respPlanRecord.GetPlanName(),
		TriggerMode:      respPlanRecord.GetTriggerMode(),
		ExecutedBy:       respPlanRecord.GetExecutedBy(),
		StartedAt:        respPlanRecord.GetStartedAt(),
		EndedAt:          respPlanRecord.GetEndedAt(),
		CostTime:         costTime,
		TotalSuite:       respPlanRecord.GetTotalSuite(),
		SuccessSuite:     respPlanRecord.GetSuccessSuite(),
		FailureSuite:     respPlanRecord.GetFailureSuite(),
		TotalCase:        respPlanRecord.GetTotalCase(),
		SuccessCase:      respPlanRecord.GetSuccessCase(),
		FailureCase:      respPlanRecord.GetFailureCase(),
		NoCaseServiceStr: noCaseServiceStr,
		Services:         services,
		Approvers:        respPlanRecord.GetApprovers(),
		SuiteItems: func() []*SuiteItem {
			suiteItem := make([]*SuiteItem, 0, len(respPlanRecord.GetSuiteItems()))
			for _, item := range respPlanRecord.GetSuiteItems() {
				suiteItem = append(suiteItem, &SuiteItem{
					TaskId:         item.GetTaskId(),
					ExecuteId:      item.GetExecuteId(),
					ProjectId:      item.GetProjectId(),
					SuiteExecuteId: item.GetSuiteExecuteId(),
					SuiteId:        item.GetSuiteId(),
					SuiteName:      item.GetSuiteName(),
					TotalCase:      item.GetTotalCase(),
					SuccessCase:    item.GetSuccessCase(),
					FailureCase:    item.GetFailureCase(),
					StartedAt:      item.GetStartedAt(),
					EndedAt:        item.GetEndedAt(),
					CostTime:       item.GetCostTime(),
					Status:         item.GetStatus(),
				})
			}
			return suiteItem
		}(),
		InterfaceDocumentItems: func() []*InterfaceDocumentItem {
			interfaceDocumentItem := make([]*InterfaceDocumentItem, 0, len(respPlanRecord.GetInterfaceDocumentItems()))
			for _, item := range respPlanRecord.GetInterfaceDocumentItems() {
				interfaceDocumentItem = append(interfaceDocumentItem, &InterfaceDocumentItem{
					TaskId:             item.GetTaskId(),
					ExecuteId:          item.GetExecuteId(),
					ProjectId:          item.GetProjectId(),
					InterfaceExecuteId: item.GetInterfaceExecuteId(),
					InterfaceId:        item.GetInterfaceId(),
					InterfaceName:      item.GetInterfaceName(),
					TotalCase:          item.GetTotalCase(),
					SuccessCase:        item.GetSuccessCase(),
					FailureCase:        item.GetFailureCase(),
					StartedAt:          item.GetStartedAt(),
					EndedAt:            item.GetEndedAt(),
					CostTime:           item.GetCostTime(),
					Status:             item.GetStatus(),
				})
			}
			return interfaceDocumentItem
		}(),
		ServiceItems: func() []*ServiceItem {
			serviceItem := make([]*ServiceItem, 0, len(respPlanRecord.GetServiceItems()))
			for _, item := range respPlanRecord.GetServiceItems() {
				serviceItem = append(serviceItem, &ServiceItem{
					TaskId:           item.GetTaskId(),
					ExecuteId:        item.GetExecuteId(),
					ProjectId:        item.GetProjectId(),
					ServiceExecuteId: item.GetServiceExecuteId(),
					ServiceId:        item.GetServiceId(),
					ServiceName:      item.GetServiceName(),
					TotalCase:        item.GetTotalCase(),
					SuccessCase:      item.GetSuccessCase(),
					FailureCase:      item.GetFailureCase(),
					StartedAt:        item.GetStartedAt(),
					EndedAt:          item.GetEndedAt(),
					CostTime:         item.GetCostTime(),
					Status:           item.GetStatus(),
				})
			}
			return serviceItem
		}(),
	}
	logx.Infof("PlanRecord: %+v", planRecord)

	// totalSuite := respPlanRecord.TotalSuite
	// successSuite := respPlanRecord.SuccessSuite
	// proportion := float64(successSuite) / float64(totalSuite)
	// passRateFloat, _ = decimal.NewFromFloat(proportion * 100).RoundFloor(2).Float64()
	// passRate = fmt.Sprintf("%.2f%%(%d / %d)", passRateFloat, successSuite, totalSuite)

	return planRecord, err
}

func getFailedCases(req *notifyRequest, planRecord *PlanRecord) *failedCaseInfo {
	if planRecord.FailureCase == 0 {
		return nil
	}

	failedCases := &failedCaseInfo{
		Items:   make([]*failedCaseInfoItem, 0),
		LarkIds: make([]string, 0),
	}
	// 去重
	larkIdsMap := make(map[string]struct{})

	const successStatus = "Success"
	for _, suiteDoc := range planRecord.SuiteItems {
		logx.Infof("suiteDoc: %+v", suiteDoc)
		if suiteDoc.FailureCase > 0 {
			suiteResp, err := req.sCtx.ReporterRpc.Cli.GetSuiteRecord(
				req.ctx, &reporterpb.GetSuiteRecordRequest{
					ProjectId:      req.source.GetProjectId(),
					TaskId:         req.source.GetTaskId(),
					SuiteExecuteId: suiteDoc.ExecuteId,
				},
			)
			if err != nil {
				logx.Errorf("Failed to get interface record for interface_execute_id[%s]: %v",
					suiteDoc.ExecuteId, err)
				continue
			}
			logx.Infof("suiteResp: %+v", suiteResp)

			for _, caseItem := range suiteResp.GetCaseItems() {
				if caseItem.GetStatus() != successStatus {
					maintainer := caseItem.GetMaintainedBy()
					userListResp, err := req.sCtx.UserRpc.GetUser(
						req.ctx, &userservice.GetUserReq{
							Account: caseItem.GetMaintainedBy(),
						},
					)
					if err != nil {
						logx.Errorf("Failed to get user info for account[%s] ExecutedBy[%s]: %v", maintainer, planRecord.ExecutedBy, err)
						continue
					}
					larkId := userListResp.GetUserInfo().GetLarkId()
					larkIdsMap[larkId] = struct{}{}

					failedCaseItem := failedCaseInfoItem{
						PlanName:   planRecord.PlanName,
						CaseId:     caseItem.GetCaseId(),
						CaseName:   caseItem.GetCaseName(),
						StartedAt:  caseItem.GetStartedAt(),
						EndedAt:    caseItem.GetEndedAt(),
						CostTime:   caseItem.GetCostTime(),
						Status:     caseItem.GetStatus(),
						Type:       pb.CallbackType_CallbackType_API_SUITE,
						Maintainer: maintainer,
						LarkId:     larkId,
					}
					failedCases.Items = append(failedCases.Items, &failedCaseItem)
					logx.Infof("SuiteItems Failed case: %+v", failedCaseItem)
				}
			}
		}
	}

	for _, interfaceDoc := range planRecord.InterfaceDocumentItems {
		logx.Infof("interfaceDoc: %+v", interfaceDoc)
		if interfaceDoc.FailureCase > 0 {
			interfaceResp, err := req.sCtx.ReporterRpc.GetInterfaceRecord(
				req.ctx, &reporterpb.GetInterfaceRecordRequest{
					ProjectId:          req.source.GetProjectId(),
					TaskId:             req.source.GetTaskId(),
					InterfaceExecuteId: interfaceDoc.ExecuteId,
				},
			)
			if err != nil {
				logx.Errorf("Failed to get interface record for interface_execute_id[%s]: %v",
					interfaceDoc.ExecuteId, err)
				continue
			}
			logx.Infof("interfaceResp: %+v", interfaceResp)

			for _, caseItem := range interfaceResp.GetCaseItems() {
				if caseItem.GetStatus() != successStatus {
					maintainer := caseItem.GetMaintainedBy()
					userListResp, err := req.sCtx.UserRpc.GetUser(
						req.ctx, &userservice.GetUserReq{
							Account: caseItem.GetMaintainedBy(),
						},
					)
					if err != nil {
						logx.Errorf("Failed to get user info for account[%s] ExecutedBy[%s]: %v", maintainer, planRecord.ExecutedBy, err)
						continue
					}
					larkId := userListResp.GetUserInfo().GetLarkId()
					larkIdsMap[larkId] = struct{}{}

					failedCaseItem := failedCaseInfoItem{
						PlanName:   planRecord.PlanName,
						CaseId:     caseItem.GetCaseId(),
						CaseName:   caseItem.GetCaseName(),
						StartedAt:  caseItem.GetStartedAt(),
						EndedAt:    caseItem.GetEndedAt(),
						CostTime:   caseItem.GetCostTime(),
						Status:     caseItem.GetStatus(),
						Type:       pb.CallbackType_CallbackType_INTERFACE_DOCUMENT,
						Maintainer: maintainer,
						LarkId:     larkId,
					}
					failedCases.Items = append(failedCases.Items, &failedCaseItem)
					logx.Infof("InterfaceDocumentItems Failed case: %+v", failedCaseItem)
				}
			}
		}
	}

	for _, serviceDoc := range planRecord.ServiceItems {
		logx.Infof("serviceDoc: %+v", serviceDoc)
		if serviceDoc.FailureCase > 0 {
			serviceResp, err := req.sCtx.ReporterRpc.GetServiceRecord(
				req.ctx, &reporterpb.GetServiceRecordRequest{
					ProjectId:        req.source.GetProjectId(),
					TaskId:           req.source.GetTaskId(),
					ServiceExecuteId: serviceDoc.ExecuteId,
				},
			)
			if err != nil {
				logx.Errorf("Failed to get interface record for interface_execute_id[%s]: %v",
					serviceDoc.ExecuteId, err)
				continue
			}
			logx.Infof("serviceResp: %+v", serviceResp)

			for _, caseItem := range serviceResp.GetCaseItems() {
				if caseItem.GetStatus() != successStatus {
					maintainer := caseItem.GetMaintainedBy()
					userListResp, err := req.sCtx.UserRpc.GetUser(
						req.ctx, &userservice.GetUserReq{
							Account: caseItem.GetMaintainedBy(),
						},
					)
					if err != nil {
						logx.Errorf("Failed to get user info for account[%s] ExecutedBy[%s]: %v", maintainer, planRecord.ExecutedBy, err)
						continue
					}
					larkId := userListResp.GetUserInfo().GetLarkId()
					larkIdsMap[larkId] = struct{}{}

					failedCaseItem := failedCaseInfoItem{
						PlanName:   planRecord.PlanName,
						CaseId:     caseItem.GetCaseId(),
						CaseName:   caseItem.GetCaseName(),
						StartedAt:  caseItem.GetStartedAt(),
						EndedAt:    caseItem.GetEndedAt(),
						CostTime:   caseItem.GetCostTime(),
						Status:     caseItem.GetStatus(),
						Type:       pb.CallbackType_CallbackType_API_SERVICE,
						Maintainer: maintainer,
						LarkId:     larkId,
					}
					failedCases.Items = append(failedCases.Items, &failedCaseItem)
					logx.Infof("ServiceItems Failed case: %+v", failedCaseItem)
				}
			}
		}
	}
	for larkId := range larkIdsMap {
		failedCases.LarkIds = append(failedCases.LarkIds, larkId)
	}
	const limit = 10
	if len(failedCases.Items) > limit {
		failedCases.Items = failedCases.Items[:limit]
	}

	return failedCases
}

func getReceivers(
	item *managerpb.NotifyItem, planRecord *PlanRecord, allReceiversMap map[string]string,
	onlyFailReceiversMap map[string]string, receivers []string,
) ([]string, map[string]string, map[string]string) {
	receiver := item.GetReceiver()
	notifyMode := item.GetNotifyMode()

	if notifyMode == managerpb.NotifyMode_ALWAYS_NOTIFY {
		_, ok := allReceiversMap[receiver]
		if !ok { // 新的通知对象
			receivers = append(receivers, receiver)
			allReceiversMap[receiver] = receiver
		}
	} else if planRecord.SuccessSuite != planRecord.TotalSuite {
		_, ok := onlyFailReceiversMap[receiver]
		if !ok { // 新的通知对象
			receivers = append(receivers, receiver)
			onlyFailReceiversMap[receiver] = receiver
		}
	}
	return receivers, allReceiversMap, onlyFailReceiversMap
}

func dealNotifyItem(req *notifyRequest, planRecord *PlanRecord) (
	larkGroupReceivers, emailReceivers []string, err error,
) {
	// 计划通知人
	var planId string
	switch req.source.GetCallbackType() {
	case pb.CallbackType_CallbackType_API_SUITE:
		planId = req.source.GetSuite().GetPlanId()
	case pb.CallbackType_CallbackType_INTERFACE_DOCUMENT:
		planId = req.source.GetInterfaceDocument().GetPlanId()
	case pb.CallbackType_CallbackType_API_SERVICE:
		planId = req.source.GetService().GetPlanId()
	default:
		return nil, nil, errorx.Errorf(
			errorx.InternalError,
			"invalid callback type, exepected: [%s or %s or %s], but got %s",
			pb.CallbackType_CallbackType_API_SUITE, pb.CallbackType_CallbackType_INTERFACE_DOCUMENT,
			pb.CallbackType_CallbackType_API_SERVICE, req.source.GetCallbackType(),
		)
	}

	respNotify, err := req.sCtx.ManagerRpc.GetPlanNotify(
		req.ctx,
		&notifyservice.GetPlanNotifyReq{
			ProjectId: req.source.GetProjectId(),
			PlanId:    planId,
		},
	)
	if err != nil {
		return nil, nil, err
	} else if len(respNotify.Items) == 0 {
		logx.Warnf("从manager获取到的计划通知人个数为0")
		return nil, nil, nil
	}

	// 接收人列表
	allLarkGroupAddressMap := make(map[string]string)      // 保存已经处理过的仅执行完成时通知飞书群
	onlyFailLarkGroupAddressMap := make(map[string]string) // 保存已经处理过的仅执行失败时通知飞书群
	allEmailAddressMap := make(map[string]string)          // 保存已经处理过的仅执行完成时通知邮箱
	onlyFailEmailAddressMap := make(map[string]string)     // 保存已经处理过的仅执行失败时通知邮箱

	for _, item := range respNotify.Items {
		switch item.GetNotifyType() {
		case managerpb.NotifyType_LARK_GROUP:
			larkGroupReceivers, allLarkGroupAddressMap, onlyFailLarkGroupAddressMap = getReceivers(
				item, planRecord, allLarkGroupAddressMap, onlyFailLarkGroupAddressMap, larkGroupReceivers,
			)
		case managerpb.NotifyType_EMAIL:
			emailReceivers, allEmailAddressMap, onlyFailEmailAddressMap = getReceivers(
				item, planRecord, allEmailAddressMap, onlyFailEmailAddressMap, emailReceivers,
			)
		}
	}
	return larkGroupReceivers, emailReceivers, nil
}

func getExecutor(notifyRequest *notifyRequest, planRecord *PlanRecord) (string, string, error) {
	if planRecord.ExecutedBy == "empty" {
		return "admin", "", nil
	}
	userListResp, err := notifyRequest.sCtx.UserRpc.GetUser(
		notifyRequest.ctx, &userservice.GetUserReq{
			Account: planRecord.ExecutedBy,
		},
	)
	if err != nil {
		return "", "", err
	}
	larkId := userListResp.GetUserInfo().GetLarkId()
	return userListResp.UserInfo.Fullname, larkId, nil
}

func getApprovers(notifyRequest *notifyRequest, planRecord *PlanRecord) (string, string, error) {
	var approvers, approver_emails, approver_lark_ids []string
	for _, approver := range planRecord.Approvers {
		userListResp, err := notifyRequest.sCtx.UserRpc.GetUser(
			notifyRequest.ctx, &userservice.GetUserReq{
				Account: approver,
			},
		)
		if err != nil {
			return "", "", err
		}
		approvers = append(approvers, userListResp.UserInfo.Fullname)
		approver_emails = append(approver_emails, userListResp.UserInfo.Email)
	}

	if len(approver_emails) > 0 {
		larkListResp, err := notifyRequest.sCtx.LarkProxyRpc.GetBatchUserID(
			notifyRequest.ctx, &larkproxypb.GetBatchUserIDReq{
				Emails: approver_emails,
			},
		)
		if err != nil || len(larkListResp.GetItems()) == 0 {
			return "", "", err
		}
		for _, item := range larkListResp.GetItems() {
			approver_lark_ids = append(approver_lark_ids, item.GetUserId())
		}
	}

	return strings.Join(approvers, "、"), strings.Join(approver_lark_ids, ","), nil
}

type notifyContent struct {
	PlanName        string
	ModifyPlanURL   string
	PlanId          string
	TaskId          string
	PlanExecuteId   string
	ProjectId       string
	ExecuteId       string
	StartedAt       string
	EndedAt         string
	CostTime        string
	TotalCase       int64
	SuccessCase     int64
	PassRate        string
	TriggerMode     string
	PlanState       string
	Color           string
	Executor        string
	ExecutorLarkId  string
	Services        string
	Approvers       string
	ApproverLarkIds string
	ProbeReportURL  string
	ReportViewUrl   string
	AppVersion      string
	AppDownloadUrl  string
	Domain          string
	SLAContent      *slaContent
	FailedCaseInfo  *failedCaseInfo
}

func generateRawContent(req *notifyRequest, planRecord *PlanRecord) (rawContent *notifyContent, err error) {
	// 计划执行人姓名
	executor, executorLarkId, err := getExecutor(req, planRecord)
	if err != nil {
		return nil, err
	}

	// 涉及的服务
	services := strings.Join(planRecord.Services, ", ")

	// 工单审批人姓名
	approvers, approver_lark_ids, err := getApprovers(req, planRecord)
	if err != nil {
		return nil, err
	}

	// 测试计划信息
	//planInfo, err := req.sCtx.ManagerRpc.ViewApiPlan(
	//	req.ctx,
	//	&managerpb.ViewApiPlanReq{
	//		ProjectId: req.source.GetProjectId(),
	//		PlanId:    planRecord.PlanId,
	//	},
	//)
	//if err != nil {
	//	return nil, err
	//}

	// 计划执行触发模式
	var triggerModeString string
	switch planRecord.TriggerMode {
	case commonpb.TriggerMode_MANUAL.String():
		triggerModeString = "手动执行"
	case commonpb.TriggerMode_SCHEDULE.String():
		triggerModeString = "定时触发"
	case commonpb.TriggerMode_INTERFACE.String():
		triggerModeString = "接口触发"
	default:
		triggerModeString = "未知"
	}

	startTime := time.Unix(planRecord.StartedAt/1000, 0)
	endTime := time.Unix(planRecord.EndedAt/1000, 0)

	planState := "成功"
	color := "green"
	if planRecord.SuccessSuite != planRecord.TotalSuite {
		planState = "失败"
		color = "red"
	}

	var planId, planExecuteId string
	switch req.source.GetCallbackType() {
	case pb.CallbackType_CallbackType_API_SUITE:
		planId = req.source.GetSuite().GetPlanId()
		planExecuteId = req.source.GetSuite().GetPlanExecuteId()
	case pb.CallbackType_CallbackType_INTERFACE_DOCUMENT:
		planId = req.source.GetInterfaceDocument().GetPlanId()
		planExecuteId = req.source.GetInterfaceDocument().GetPlanExecuteId()
	case pb.CallbackType_CallbackType_API_SERVICE:
		planId = req.source.GetService().GetPlanId()
		planExecuteId = req.source.GetService().GetPlanExecuteId()
	default:
		return nil, errorx.Errorf(
			errorx.InternalError,
			"invalid callback type, exepected: [%s or %s or %s], but got %s",
			pb.CallbackType_CallbackType_API_SUITE, pb.CallbackType_CallbackType_INTERFACE_DOCUMENT,
			pb.CallbackType_CallbackType_API_SERVICE, req.source.GetCallbackType(),
		)
	}

	var passRate string
	if planRecord.SuccessCase == 0 || planRecord.TotalCase == 0 {
		passRate = "0%"
	} else {
		passRate = fmt.Sprintf("%.2f%%", float64(planRecord.SuccessCase)/float64(planRecord.TotalCase)*100)
	}

	failedCaseInfo := getFailedCases(req, planRecord)

	probeReportURL := fmt.Sprintf(
		"%s/#/api-test/testplan/reporter?plan_id=%s&task_id=%s&plan_execute_id=%s&project_id=%s&execute_id=%s",
		req.sCtx.ProbeBaseURL, planId, req.source.GetTaskId(), planExecuteId, req.source.GetProjectId(),
		planExecuteId,
	)

	rawContent = &notifyContent{
		PlanName:        planRecord.PlanName,
		PlanId:          planId,
		TaskId:          req.source.GetTaskId(),
		PlanExecuteId:   planExecuteId,
		ProjectId:       req.source.GetProjectId(),
		ExecuteId:       planExecuteId,
		StartedAt:       startTime.Format("2006-01-02 15:04:05"),
		EndedAt:         endTime.Format("2006-01-02 15:04:05"),
		CostTime:        planRecord.CostTime,
		TotalCase:       planRecord.TotalCase,
		SuccessCase:     planRecord.SuccessCase,
		PassRate:        passRate,
		TriggerMode:     triggerModeString,
		PlanState:       planState,
		Color:           color,
		Executor:        executor,
		ExecutorLarkId:  executorLarkId,
		Services:        services,
		Approvers:       approvers,
		ApproverLarkIds: approver_lark_ids,
		ProbeReportURL:  probeReportURL,
		FailedCaseInfo:  failedCaseInfo,
	}

	// fmt.Println("rawContent!!!!!!!")
	// fmt.Println(rawContent.PlanState)
	// fmt.Println(rawContent.Color)

	return rawContent, nil
}

func jsonMarshal(t any) ([]byte, error) {
	buffer := &bytes.Buffer{}
	encoder := json.NewEncoder(buffer)
	encoder.SetEscapeHTML(false)
	err := encoder.Encode(t)
	return buffer.Bytes(), err
}

func generateLarkGroupContent(probeDomain string, rawContent *notifyContent) string {
	var planState string
	if rawContent.PlanState == "成功" {
		planState = "<font color=\"green\">成功</font>"
	} else {
		planState = "<font color=\"red\">失败</font>"
	}
	content := fmt.Sprintf(
		"**测试计划：**%s\n**开始时间：**%s\n**结束时间：**%s\n**触发模式：**%s\n"+
			"**执行时长：**%s\n**成功用例数：**%d\n**执行用例数：**%d\n**成功率：**%s\n**状态：**%s\n**执行人：**%s",
		rawContent.PlanName, rawContent.StartedAt, rawContent.EndedAt, rawContent.TriggerMode,
		rawContent.CostTime, rawContent.SuccessCase, rawContent.TotalCase, rawContent.PassRate, planState,
		rawContent.Executor,
	)

	if rawContent.Services != "" {
		content += fmt.Sprintf("\n**涉及的服务：**%s", rawContent.Services)
	}

	if rawContent.ApproverLarkIds != "" {
		content += fmt.Sprintf("\n**审批人：**<at ids=%s></at>", rawContent.ApproverLarkIds)
	}

	// 添加失败用例信息
	if rawContent.FailedCaseInfo != nil {
		if len(rawContent.FailedCaseInfo.Items) > 0 {
			content += "\n\n**失败用例详情：**"

			for i, failedCase := range rawContent.FailedCaseInfo.Items {
				if i >= 10 { // 限制显示的失败用例数量，避免消息过长
					content += fmt.Sprintf("\n%d. 还有 %d 个失败用例...", i+1, len(rawContent.FailedCaseInfo.Items)-i)
					break
				}

				content += fmt.Sprintf("\n%d. **%s** (%s)", i+1, failedCase.PlanName, failedCase.CaseName)
				if failedCase.Maintainer != "" && failedCase.Maintainer != "empty" {
					content += fmt.Sprintf(" - 负责人：<at ids=%s></at>", failedCase.LarkId)
				}
			}
		}
	}

	probeReportURL := fmt.Sprintf(
		"%s/#/api-test/testplan/reporter?plan_id=%s&task_id=%s&plan_execute_id=%s&project_id=%s&execute_id=%s",
		probeDomain, rawContent.PlanId, rawContent.TaskId, rawContent.PlanExecuteId, rawContent.ProjectId,
		rawContent.ExecuteId,
	)

	// 飞书群卡片消息 参考 https://open.feishu.cn/tool/cardbuilder?templateId=ctp_AAfBtyXFHXBt
	contentMap := map[string]any{
		"config": map[string]any{
			"wide_screen_mode": true,
		},
		"header": map[string]any{
			"template": "blue",
			"title": map[string]any{
				"content": "测试结果通知",
				"tag":     "plain_text",
			},
		},
		"elements": []map[string]any{
			{
				"tag":     "markdown",
				"content": content,
			},
			{
				"tag": "action",
				"actions": []map[string]any{
					{
						"tag": "button",
						"text": map[string]any{
							"tag":     "plain_text",
							"content": "查看测试报告",
						},
						"type": "primary",
						"multi_url": map[string]any{
							"url":         probeReportURL,
							"pc_url":      "",
							"android_url": "",
							"ios_url":     "",
						},
					},
				},
			},
		},
	}
	// 使用自定义的JsonMarshal而不是json包的Marshal能实现json序列化不转义
	// 参考https://blog.csdn.net/lgh1700/article/details/104522878
	b, _ := jsonMarshal(contentMap)
	contentStr := string(b)

	// fmt.Println("飞书！！！！！！1：")
	// fmt.Println(contentStr)

	return contentStr
}

type emailContent struct {
	PlanName       string `json:"planName"`
	PlanRecordUrl  string `json:"planRecordUrl"`
	StartedAt      string `json:"startedAt"`
	EndedAt        string `json:"endedAt"`
	TriggerMode    string `json:"triggerMode"`
	CostTime       string `json:"costTime"`
	SuccessCase    int64  `json:"successCase"`
	TotalCase      int64  `json:"totalCase"`
	PassRate       string `json:"passRate"`
	PlanState      string `json:"planState"`
	Color          string `json:"color"`
	Executor       string `json:"executor"`
	Services       string `json:"services"`
	Approvers      string `json:"approvers"`
	NoCaseServices string `json:"noCaseServices"`
}

func generateEmailContent(probeDomain string, rawContent *notifyContent) string {
	contentTemplate := `
		<table align="left" cellspacing="0" class="hostname" style="text-align:left">
			<tr>
				<td width=85><b>测试计划:</b>&nbsp;&nbsp;</td>
				<td><a href="{{ .PlanRecordUrl }}">{{ .PlanName }}</a></td>
			</tr>
			<tr>
				<td width=85><b>开始时间:</b>&nbsp;&nbsp;</td>
				<td>{{ .StartedAt }}</td>
			</tr>
			<tr>
				<td width=85><b>结束时间:</b>&nbsp;&nbsp;</td>
				<td>{{ .EndedAt }}</td>
			</tr>
			<tr>
				<td width=85><b>触发模式:</b>&nbsp;&nbsp;</td>
				<td>{{ .TriggerMode }}</td>
			</tr>
			<tr>
				<td width=85><b>执行时长:</b>&nbsp;&nbsp;</td>
				<td>{{ .CostTime }}</td>
			</tr>
			<tr>
				<td width=85><b>成功用例数:</b>&nbsp;&nbsp;</td>
				<td>{{ .SuccessCase }}</td>
			</tr>
			<tr>
				<td width=85><b>执行用例数:</b>&nbsp;&nbsp;</td>
				<td>{{ .TotalCase }}</td>
			</tr>
			<tr>
				<td width=85><b>成功率:</b>&nbsp;&nbsp;</td>
				<td>{{ .PassRate }}</td>
			</tr>
			<tr>
				<td width=85><b>状态:</b>&nbsp;&nbsp;</td>
				<td><font color="{{ .Color }}">{{ .PlanState }}</font></td>
			</tr>
			<tr>
				<td width=85><b>执行人:</b>&nbsp;&nbsp;</td>
				<td>{{ .Executor }}</td>
			</tr>
		{{- if not (eq .Services "") }}
			<tr>
				<td width=85><b>涉及的服务:</b>&nbsp;&nbsp;</td>
				<td>{{ .Services }}</td>
			</tr>
		{{- end }}
		{{- if not (eq .Approvers "") }}
			<tr>
				<td width=85><b>审批人:</b>&nbsp;&nbsp;</td>
				<td>{{ .Approvers }}</td>
			</tr>
		{{- end }}
		</table>`

	tpl, _ := template.New("content").Parse(contentTemplate)

	planRecordUrl := fmt.Sprintf(
		"%s/#/api-test/testplan/reporter?plan_id=%s&task_id=%s"+
			"&plan_execute_id=%s&project_id=%s&execute_id=%s",
		probeDomain, rawContent.PlanId, rawContent.TaskId,
		rawContent.PlanExecuteId, rawContent.ProjectId, rawContent.ExecuteId,
	)

	var stringBuilder strings.Builder
	wr := tabwriter.NewWriter(&stringBuilder, 0, 0, 0, ' ', 0)

	data := &emailContent{
		PlanName:      rawContent.PlanName,
		PlanRecordUrl: planRecordUrl,
		StartedAt:     rawContent.StartedAt,
		EndedAt:       rawContent.EndedAt,
		TriggerMode:   rawContent.TriggerMode,
		CostTime:      rawContent.CostTime,
		SuccessCase:   rawContent.SuccessCase,
		TotalCase:     rawContent.TotalCase,
		PassRate:      rawContent.PassRate,
		Color:         rawContent.Color,
		PlanState:     rawContent.PlanState,
		Executor:      rawContent.Executor,
		Services:      rawContent.Services,
		Approvers:     rawContent.Approvers,
	}
	_ = tpl.Execute(wr, data)
	_ = wr.Flush()

	contentStr := stringBuilder.String()

	// fmt.Println("email!!!!!!!!!!!!!!!:")
	// fmt.Println(contentStr)

	return contentStr
}

func realSendNotify(
	req *notifyRequest, planRecord *PlanRecord, larkGroupReceivers,
	emailReceivers []string,
) error {
	var notifyItems []*notifierpb.NotifyItem

	// 生成通知内容
	rawContent, err := generateRawContent(req, planRecord)
	if err != nil {
		return err
	}

	// 飞书群
	if len(larkGroupReceivers) > 0 {
		// content := generateLarkGroupContent(req.sCtx.ProbeBaseURL, rawContent)
		content, err := templateParse(interfaceReportTemplate, rawContent)
		if err != nil {
			return errorx.Errorf(
				errorx.InternalError, "failed to parse ui report template, err: %+v", err,
			)
		}
		for _, larkGroupUrl := range larkGroupReceivers {
			notifyItem := notifierpb.NotifyItem{
				Item: &notifierpb.NotifyItem_LarkGroupChat{
					LarkGroupChat: &notificationservice.LarkGroupChat{
						WebhookUrl: larkGroupUrl,
						MsgType:    notifierpb.LarkMessageType_INTERACTIVE,
						Content:    content,
					},
				},
			}
			notifyItems = append(notifyItems, &notifyItem)
		}
	}

	// 邮件
	if len(emailReceivers) > 0 {
		content := generateEmailContent(req.sCtx.ProbeBaseURL, rawContent)
		notifyItem := notifierpb.NotifyItem{
			Item: &notifierpb.NotifyItem_Email{
				Email: &notificationservice.Email{
					Sender:    "",
					Receivers: emailReceivers,
					Subject:   fmt.Sprintf("测试计划「%s」执行结果通知", rawContent.PlanName),
					Content:   content,
				},
			},
		}
		notifyItems = append(notifyItems, &notifyItem)
	}

	_, err = req.sCtx.NotifierRpc.Notify(req.ctx, &notificationservice.NotifyReq{Items: notifyItems})
	return err
}

type notifyRequest struct {
	sCtx   *svc.ServiceContext
	ctx    context.Context
	source *pb.CallbackReq
}

func SendNotify(req *notifyRequest) error {
	// 计划执行信息
	planRecord, err := getPlanRecord(req)
	if err != nil {
		return err
	}

	// 计划发送通知的对象
	larkGroupReceivers, emailReceivers, err := dealNotifyItem(req, planRecord)
	if err != nil {
		return err
	}
	if len(larkGroupReceivers) == 0 && len(emailReceivers) == 0 {
		return nil
	}

	return realSendNotify(req, planRecord, larkGroupReceivers, emailReceivers)
}
