package callback

import (
	"fmt"
	"time"

	"github.com/zeromicro/go-zero/core/jsonx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworkerv2/base"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/codes"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/common/callbackclient"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/common/dtctl"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/rpc/pb"
	managerpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
	reporterpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
)

type InterfaceCaseInSuiteNode struct {
	*BaseNode
}

func NewInterfaceCaseInSuiteNode(task *Callback) *InterfaceCaseInSuiteNode {
	node := &InterfaceCaseInSuiteNode{
		BaseNode: NewBaseNode(task),
	}
	return node
}

func (node *InterfaceCaseInSuiteNode) Type() managerpb.ApiExecutionDataType {
	return managerpb.ApiExecutionDataType_INTERFACE_CASE
}

func (node *InterfaceCaseInSuiteNode) GetDtControl() *dtctl.DispatchTaskControl {
	if node.ctl != nil {
		return node.ctl
	}

	source := node.Task().Source()
	member := &dtctl.DispatchTaskMember{
		ComponentId:        source.GetInterfaceCase().GetInterfaceId(),
		ComponentType:      managerpb.ApiExecutionDataType_API_SUITE,
		Version:            "",
		ComponentExecuteId: source.GetInterfaceCase().GetInterfaceExecuteId(),
	}
	node.ctl = dtctl.NewDispatchTaskControl(node.Task().Context(), node.Task().ServiceContext().Redis, member)
	return node.ctl
}

func (node *InterfaceCaseInSuiteNode) GetMember() *dtctl.DispatchTaskMember {
	source := node.Task().Source()
	member := &dtctl.DispatchTaskMember{
		ComponentId:        source.GetInterfaceCase().GetInterfaceCaseId(),
		ComponentType:      managerpb.ApiExecutionDataType_INTERFACE_CASE,
		Version:            source.GetInterfaceCase().GetVersion(),
		ComponentExecuteId: source.GetInterfaceCase().GetInterfaceCaseExecuteId(),
	}
	return member
}

func (node *InterfaceCaseInSuiteNode) State() pb.ComponentState {
	return node.Task().source.GetInterfaceCase().GetInterfaceCaseState()
}

func (node *InterfaceCaseInSuiteNode) TaskInfoProcessorSync() {
	source := node.Task().Source()
	switch node.State() {
	case pb.ComponentState_Failure:
	case pb.ComponentState_Invalid:
	case pb.ComponentState_Panic:
	default:
		return
	}

	// 向`reporter`发送失败用例消息
	node.sendMsg2ReporterCaseFailStatPlan(source)
	// 向`manager`发送失败用例消息
	node.sendMsg2ManagerCaseFailStat(source)
}

func (node *InterfaceCaseInSuiteNode) sendMsg2ReporterCaseFailStatPlan(source *pb.CallbackReq) {
	var (
		projectID = source.GetProjectId()
		taskID    = source.GetTaskId()
		executeID = source.GetInterfaceCase().GetInterfaceCaseExecuteId()
		caseID    = source.GetInterfaceCase().GetInterfaceCaseId()
	)

	req := &reporterpb.CaseFailForPlanStatForMq{
		ProjectId: projectID,
		TaskId:    taskID,
		ExecuteId: executeID,
		CaseId:    caseID,
		PlanId:    "", // 通过`taskId`查询对应的`planId`
		CaseType:  constants.CaseTypeInterfaceCase,
	}
	payload := protobuf.MarshalJSONIgnoreError(req)
	task := base.NewTask(
		constants.MQTaskTypeReporterCaseFailLogResult, payload,
		base.WithMaxRetryOptions(2),
		base.WithRetentionOptions(time.Minute*10),
	)
	node.BaseNode.task.Debugf(
		"ready to send task to `reporter`, project_id: %s, task_id: %s, execute_id: %s, case_id: %s, payload: %s",
		projectID, taskID, executeID, caseID, payload,
	)

	_, err := node.BaseNode.task.ServiceContext().ReporterProducer.Send(
		node.Task().Context(), task, base.QueuePriorityDefault,
	)
	if err != nil {
		node.BaseNode.task.Errorf(
			"failed to send task to `reporter`, project_id: %s, task_id: %s, execute_id: %s, case_id: %s, error: %+v",
			projectID, taskID, executeID, caseID, err,
		)
	}
}

func (node *InterfaceCaseInSuiteNode) sendMsg2ManagerCaseFailStat(source *pb.CallbackReq) {
	var (
		projectID = source.GetProjectId()
		taskID    = source.GetTaskId()
		executeID = source.GetInterfaceCase().GetInterfaceCaseExecuteId()
		caseID    = source.GetInterfaceCase().GetInterfaceCaseId()
	)

	req := &managerpb.CaseFailStatForMq{
		ProjectId: projectID,
		BranchId:  "", // 通过`caseId`查询对应的`branchId`
		ExecuteId: executeID,
		CaseId:    caseID,
		CaseType:  constants.CaseTypeInterfaceCase,
	}
	payload := protobuf.MarshalJSONIgnoreError(req)
	task := base.NewTask(
		constants.MQTaskTypeManagerHandleCaseFailStatResult, payload,
		base.WithMaxRetryOptions(2),
		base.WithRetentionOptions(time.Minute*10),
	)
	node.BaseNode.task.Debugf(
		"ready to send task to `manager`, project_id: %s, task_id: %s, execute_id: %s, case_id: %s, payload: %s",
		projectID, taskID, executeID, caseID, payload,
	)

	_, err := node.BaseNode.task.ServiceContext().ManagerProducer.SendDelay(
		node.Task().Context(), task, 10*time.Second, base.QueuePriorityDefault,
	)
	if err != nil {
		node.BaseNode.task.Errorf(
			"failed to send task to `manager`, project_id: %s, task_id: %s, execute_id: %s, case_id: %s, error: %+v",
			projectID, taskID, executeID, caseID, err,
		)
	}
}

func (node *InterfaceCaseInSuiteNode) SetState(s pb.ComponentState) {
	node.Task().source.GetInterfaceCase().InterfaceCaseState = s
}

func (node *InterfaceCaseInSuiteNode) Record() error {
	source := node.Task().Source()
	_, err := node.Task().ServiceContext().ReporterRpc.ModifySuiteRecord(
		node.Task().Context(), &reporterpb.PutSuiteRecordRequest{
			TaskId:         source.GetTaskId(),
			ExecuteId:      source.GetInterfaceCase().GetInterfaceExecuteId(), // 这里是父节点的execute_id
			ExecuteType:    source.GetExecuteType().String(),
			ProjectId:      source.GetProjectId(),
			SuiteId:        source.GetInterfaceCase().GetInterfaceId(),
			SuiteExecuteId: source.GetInterfaceCase().GetInterfaceExecuteId(),
			SuccessCase:    node.Task().Success(),
			FailureCase:    node.Task().Failure(),
			Status:         node.Task().StateM().State().String(),
			Content:        jsonx.MarshalToStringIgnoreError(node.Task().StateM().Debug()),
			EndedAt:        time.Now().UnixMilli(),
		},
	)

	return err
}

func (node *InterfaceCaseInSuiteNode) RecordCallback() error {
	source := node.Task().Source()
	_, err := node.Task().ServiceContext().ReporterRpc.ModifyCaseRecord(
		node.Task().Context(), &reporterpb.PutRecordRequest{
			TaskId:             source.GetTaskId(),
			ExecuteId:          source.GetInterfaceCase().GetInterfaceCaseExecuteId(),
			ExecuteType:        source.GetExecuteType().String(),
			ProjectId:          source.GetProjectId(),
			ComponentId:        source.GetInterfaceCase().GetInterfaceCaseId(),
			ComponentType:      managerpb.ApiExecutionDataType_INTERFACE_CASE.String(),
			ComponentExecuteId: source.GetInterfaceCase().GetInterfaceCaseExecuteId(),
			Version:            source.GetInterfaceCase().GetVersion(),
			Times:              1,
			Callback:           node.Task().Content(),
			Status:             source.GetInterfaceCase().GetInterfaceCaseState().String(),
			EndedAt:            time.Now().UnixMilli(),
		},
	)
	return err
}

func (node *InterfaceCaseInSuiteNode) Teardown() error {
	if node.Task().Source().GetExecuteType() != managerpb.ApiExecutionDataType_API_PLAN {
		return nil
	}

	// 回调plan
	source := node.Task().Source()

	// 获取计划执行数据
	plan, err := node.Task().ServiceContext().ReporterRpc.GetParent(
		node.Task().Context(), &reporterpb.GetParentRecordRequest{
			TaskId:             source.GetTaskId(),
			ExecuteId:          source.GetInterfaceCase().GetInterfaceExecuteId(),
			ProjectId:          source.GetProjectId(),
			ExecuteType:        source.GetExecuteType().String(),
			ComponentId:        source.GetInterfaceCase().GetInterfaceId(),
			ComponentType:      managerpb.ApiExecutionDataType_API_SUITE.String(),
			ComponentExecuteId: source.GetInterfaceCase().GetInterfaceExecuteId(),
		},
	)
	if err != nil {
		// 如果不存在plan，则无需回调
		if errorx.Convert(err).Code() == errorx.NotExists {
			return nil
		}

		return err
	}

	cb := &pb.CallbackReq{
		TriggerMode:  source.GetTriggerMode(),
		TriggerRule:  source.GetTriggerRule(),
		ProjectId:    source.GetProjectId(),
		TaskId:       source.GetTaskId(),
		ExecuteType:  source.GetExecuteType(),
		CallbackType: pb.CallbackType_CallbackType_API_SUITE,
		Data: &pb.CallbackReq_Suite{
			Suite: &pb.SuiteCallbackData{
				PlanId:         plan.GetComponentId(),
				PlanExecuteId:  plan.GetComponentExecuteId(),
				SuiteId:        source.GetInterfaceCase().GetInterfaceId(),
				SuiteExecuteId: source.GetInterfaceCase().GetInterfaceExecuteId(),
				SuiteState:     node.Task().StateM().State(),
			},
		},
	}

	err = callbackclient.CallBack(
		node.Task().Context(),
		node.Task().ServiceContext().DispatcherProducer,
		node.Task().ServiceContext().Config.Name,
		node.Task().ServiceContext().Config.DispatcherProducer.Queue,
		node.getInterfaceDocumentUUID(),
		cb,
	)
	if err != nil {
		return node.Task().UpdateFailureStatef(codes.TaskPublishFailure, "%s", err)
	}

	return nil
}

func (node *InterfaceCaseInSuiteNode) getInterfaceDocumentUUID() string {
	source := node.Task().Source()
	return fmt.Sprintf("%s::%s", source.GetTaskId(), source.GetInterfaceCase().GetInterfaceExecuteId())
}
