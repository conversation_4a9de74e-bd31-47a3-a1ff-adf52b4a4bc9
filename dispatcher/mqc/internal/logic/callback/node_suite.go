package callback

import (
	"time"

	"github.com/zeromicro/go-zero/core/jsonx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/common/dtctl"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/rpc/pb"
	managerpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
	reporterpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
)

type SuiteNode struct {
	*BaseNode
}

func NewSuiteNode(task *Callback) *SuiteNode {
	node := &SuiteNode{
		BaseNode: NewBaseNode(task),
	}
	return node
}

func (node *SuiteNode) Type() managerpb.ApiExecutionDataType {
	return managerpb.ApiExecutionDataType_API_SUITE
}

func (node *SuiteNode) GetDtControl() *dtctl.DispatchTaskControl {
	if node.ctl != nil {
		return node.ctl
	}

	source := node.Task().Source()
	member := &dtctl.DispatchTaskMember{
		ComponentId:        source.GetSuite().GetPlanId(),
		ComponentType:      managerpb.ApiExecutionDataType_API_PLAN,
		Version:            "",
		ComponentExecuteId: source.GetSuite().GetPlanExecuteId(),
	}
	node.ctl = dtctl.NewDispatchTaskControl(node.Task().Context(), node.Task().ServiceContext().Redis, member)
	return node.ctl
}

func (node *SuiteNode) GetMember() *dtctl.DispatchTaskMember {
	source := node.Task().Source()
	member := &dtctl.DispatchTaskMember{
		ComponentId:        source.GetSuite().GetSuiteId(),
		ComponentType:      managerpb.ApiExecutionDataType_API_SUITE,
		Version:            "",
		ComponentExecuteId: source.GetSuite().GetSuiteExecuteId(),
	}
	return member
}

func (node *SuiteNode) State() pb.ComponentState {
	return node.Task().source.GetSuite().GetSuiteState()
}

func (node *SuiteNode) SetState(s pb.ComponentState) {
	node.Task().source.GetSuite().SuiteState = s
}

func (node *SuiteNode) TaskInfoProcessorSync() {
}

func (node *SuiteNode) Record() error {
	source := node.Task().Source()
	caseInfos, err := node.Task().ServiceContext().ReporterRpc.GetPlanCasesInfo(
		node.Task().Context(), &reporterpb.GetPlanCasesInfoRequest{
			ProjectId: source.GetProjectId(),
			TaskId:    source.GetTaskId(),
			ExecuteId: source.GetSuite().GetPlanExecuteId(),
		},
	)
	if err != nil {
		return err
	}

	_, err = node.Task().ServiceContext().ReporterRpc.ModifyPlanRecord(
		node.Task().Context(), &reporterpb.PutPlanRecordRequest{
			TaskId:        source.GetTaskId(),
			ExecuteId:     source.GetSuite().GetPlanExecuteId(), // 这里是父节点的execute_id
			ProjectId:     source.GetProjectId(),
			PlanId:        source.GetSuite().GetPlanId(),
			PlanExecuteId: source.GetSuite().GetPlanExecuteId(),
			SuccessSuite:  node.Task().Success(),
			FailureSuite:  node.Task().Failure(),
			TotalCase:     caseInfos.GetTotalCase(),
			SuccessCase:   caseInfos.GetSuccessCase(),
			FailureCase:   caseInfos.GetFailureCase(),
			Status:        node.Task().StateM().State().String(),
			Content:       jsonx.MarshalToStringIgnoreError(node.Task().StateM().Debug()),
			EndedAt:       time.Now().UnixMilli(),
		},
	)
	return err
}

func (node *SuiteNode) RecordCallback() error {
	source := node.Task().Source()
	_, err := node.Task().ServiceContext().ReporterRpc.ModifySuiteRecord(
		node.Task().Context(), &reporterpb.PutSuiteRecordRequest{
			TaskId:         source.GetTaskId(),
			ExecuteId:      source.GetSuite().GetSuiteExecuteId(),
			ExecuteType:    source.GetExecuteType().String(),
			ProjectId:      source.GetProjectId(),
			SuiteId:        source.GetSuite().GetSuiteId(),
			SuiteExecuteId: source.GetSuite().GetSuiteExecuteId(),
			Callback:       node.Task().Content(),
			Status:         source.GetSuite().GetSuiteState().String(),
			EndedAt:        time.Now().UnixMilli(),
		},
	)
	return err
}

func (node *SuiteNode) Teardown() error {
	req := &notifyRequest{
		sCtx:   node.Task().ServiceContext(),
		ctx:    node.Task().Context(),
		source: node.Task().Source(),
	}

	err := SendNotify(req)
	if err != nil {
		node.Task().Errorf("SendNotify fail, task_id[%s], err: %s", node.Task().Source().GetTaskId(), err)
	}
	return nil
}
