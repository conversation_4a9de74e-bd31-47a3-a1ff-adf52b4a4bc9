package callback

import (
	_ "embed"
	ttemplate "text/template"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/template"
)

var (
	//go:embed uiReport.template
	uiReportContentTemplate string
	//go:embed interfaceReport.template
	interfaceReportContentTemplate string

	uiReportTemplate        = ttemplate.Must(template.Parse("uiReport.template", uiReportContentTemplate))
	interfaceReportTemplate = ttemplate.Must(template.Parse("interfaceReport.template", interfaceReportContentTemplate))
)
