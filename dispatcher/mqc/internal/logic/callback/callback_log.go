package callback

import (
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/mqc/internal/logic/baselogic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/rpc/pb"
)

type CaseCallback_Log struct {
	*baselogic.BaseLogic_Log_Common
	Request  *pb.CallbackReq `json:"request"`
	Total    int64           `json:"total"`
	Finished int64           `json:"finished"`
	Success  int64           `json:"success"`
	Failure  int64           `json:"failure"`
}

type CaseCallback_Log_ICase struct {
	baselogic.BaseLogic_Log_Common_Error
	Source *pb.ComponentExecuteKey `json:"source"`
}

type CaseCallbackLogWriter struct {
	*baselogic.BaseLogicLogWriter

	log *CaseCallback_Log
}

func NewCaseCallbackLogWriter(bw *baselogic.BaseLogicLogWriter) *CaseCallbackLogWriter {
	writer := &CaseCallbackLogWriter{
		BaseLogicLogWriter: bw,
		log:                &CaseCallback_Log{},
	}
	return writer
}

func (writer *CaseCallbackLogWriter) setMqRequest(req *pb.CallbackReq) {
	writer.DoLock(func() {
		writer.log.Request = req
	})
}

func (writer *CaseCallbackLogWriter) setTotal(total int64) {
	writer.DoLock(func() {
		writer.log.Total = total
	})
}

func (writer *CaseCallbackLogWriter) setFinished(fin int64) {
	writer.DoLock(func() {
		writer.log.Finished = fin
	})
}

func (writer *CaseCallbackLogWriter) setSucc(succ int64) {
	writer.DoLock(func() {
		writer.log.Success = succ
	})
}

func (writer *CaseCallbackLogWriter) setFail(fail int64) {
	writer.DoLock(func() {
		writer.log.Failure = fail
	})
}

func (writer *CaseCallbackLogWriter) toJson() string {
	var data string
	writer.DoLock(func() {
		data = baselogic.MarshalJson(writer.log)
	})
	return data
}
