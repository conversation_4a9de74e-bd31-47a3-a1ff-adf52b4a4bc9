package unittest

import (
	"testing"

	"github.com/stretchr/testify/assert"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/common/dtctl"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/common/mock"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/mqc/internal/logic/callback"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/mqc/internal/logic/interfacedocument"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/rpc/pb"
	managerpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

func TestInterfaceCase(t *testing.T) {
	var case_num int64 = 5
	interface_handler := interfacedocument.Mock_default_InterfaceDocument()
	interface_request := interfacedocument.Mock_InterfaceDocument_DistributeReq(int(case_num))

	err := interface_handler.Run(interface_request)
	if err != nil {
		t.Errorf("%s", err)
		t.FailNow()
	}

	ctl := interface_handler.GetDtControl()
	// info ok?
	total, err := ctl.Total()
	if err != nil {
		t.Errorf("%s", err)
		t.FailNow()
	}
	assert.Equal(t, total, case_num, "Total不相等")

	// member ok?
	members, err := ctl.GetAllMembers()
	if err != nil {
		t.Errorf("%s", err)
		t.FailNow()
	}
	assert.Equal(t, len(members), int(case_num), "Member数量存在问题")

	// callback
	parent := &dtctl.DispatchTaskMember{
		ComponentId:        interface_request.GetInterfaceDocument().GetInterfaceDocumentId(),
		ComponentType:      managerpb.ApiExecutionDataType_INTERFACE_DOCUMENT,
		ComponentExecuteId: interface_request.GetInterfaceDocument().GetInterfaceDocumentExecuteId(),
	}

	children := make([]*dtctl.DispatchTaskMember, len(members))
	for idx, mem := range members {
		children[idx] = &dtctl.DispatchTaskMember{
			ComponentId:        mem.ComponentId,
			ComponentType:      mem.ComponentType,
			Version:            mem.Version,
			ComponentExecuteId: mem.ComponentExecuteId,
			Index:              idx,
		}
	}

	callback_handler := callback.Mock_default_Callback()
	for _, child := range children {
		callback_request := mock.Mock_CallbackMqReq(parent, child, pb.ComponentState_Success)
		err := callback_handler.Run(callback_request)
		if err != nil {
			t.Errorf("%s", err)
			t.FailNow()
		}
	}
}
