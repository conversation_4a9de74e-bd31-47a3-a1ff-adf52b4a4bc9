package interfacedocument

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
)

func TestInterfaceDocument(t *testing.T) {
	var case_num int64 = 5

	handler := Mock_default_InterfaceDocument()
	request := Mock_InterfaceDocument_DistributeReq(int(case_num))

	err := handler.Run(request)
	if err != nil {
		t.<PERSON><PERSON>("%s", err)
		t.<PERSON>ailNow()
	}

	ctl := handler.GetDtControl()
	// info ok?
	total, err := ctl.Total()
	if err != nil {
		t.<PERSON>rrorf("%s", err)
		t.FailNow()
	}
	assert.Equal(t, total, case_num, "Total不相等")

	starttime, err := ctl.Starttime()
	if err != nil {
		t.<PERSON><PERSON>rf("%s", err)
		t.<PERSON>ailNow()
	}
	assert.LessOrEqual(t, starttime, time.Now().Unix(), "时间戳存在问题")

	// member ok?
	members, err := ctl.GetAllMembers()
	if err != nil {
		t.Errorf("%s", err)
		t.<PERSON>ail<PERSON>ow()
	}

	assert.Equal(t, len(members), int(case_num), "Member数量存在问题")

	// publish ok?
}
