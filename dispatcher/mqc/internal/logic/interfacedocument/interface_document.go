package interfacedocument

import (
	"context"
	"fmt"
	"time"

	"github.com/zeromicro/go-zero/core/jsonx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/codes"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/common/callbackclient"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/common/dtctl"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/common/errcode"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/mqc/internal/logic/baselogic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/mqc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/rpc/client/dispatcher"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/rpc/pb"
	managerpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
	reporterpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
)

type InterfaceDocument struct {
	*baselogic.DispatchLogic
	logW *InterfaceCaseLogWriter
}

func NewInterfaceDocument(ctx context.Context, svcCtx *svc.ServiceContext) *InterfaceDocument {
	return &InterfaceDocument{
		DispatchLogic: baselogic.NewDispatchLogic(ctx, svcCtx),
	}
}

func (x *InterfaceDocument) Run(req *pb.DistributeReq) (err error) {
	x.Setup(req)

	err = x.run()

	x.Teardown()

	return err
}

func (x *InterfaceDocument) Setup(req *pb.DistributeReq) {
	x.DispatchLogic.Setup(req)
	x.logW = NewInterfaceCaseLogWriter(x.DispatchLogic.LogWriter())
	x.SetDtCtl(x.GetDtControl())
	x.logW.setCaseExecutionMode(req.GetPlan().GetPlan().GetCaseExecutionMode())
}

func (x *InterfaceDocument) run() (err error) {
	defer func() {
		// 空集合默认成功
		if x.Total() == 0 {
			x.StateM().UpdateStateManager(pb.ComponentState_Success, errcode.GetErrCode(errorx.OK))
		}

		if r := recover(); r != any(nil) {
			err = x.UpdatePanicStatef(codes.TaskPanic, "%v", r)
		}

		// 更新执行状态
		_, err = x.record()
		if err != nil {
			err = x.UpdatePanicStatef(codes.TaskRecordFailure, "InterfaceDocument.record: %s", err)
		}

		// 如果是空集合，则直接回调
		err = x.Callback(err)
		if err != nil {
			err = x.UpdatePanicStatef(codes.TaskRecordFailure, "Suite.Callback: %s", err)
		}

		x.Infof(
			"InterfaceDocument[%s] : %s", x.Source().GetInterfaceDocument().GetInterfaceDocumentExecuteId(),
			x.Content(),
		)
	}()

	err = x.Publish(x.GetChildMembers(), x.PublishOne)
	if err != nil {
		return err
	}

	return err
}

func (x *InterfaceDocument) GetDtControl() *dtctl.DispatchTaskControl {
	source := x.Source()
	member := &dtctl.DispatchTaskMember{
		ComponentId:        source.GetInterfaceDocument().GetInterfaceDocumentId(),
		ComponentType:      managerpb.ApiExecutionDataType_INTERFACE_DOCUMENT,
		Version:            "",
		ComponentExecuteId: source.GetInterfaceDocument().GetInterfaceDocumentExecuteId(),
	}
	return dtctl.NewDispatchTaskControl(x.Context(), x.ServiceContext().Redis, member)
}

func (x *InterfaceDocument) GetChildMembers() []*dtctl.DispatchTaskMember {
	if len(x.Source().GetInterfaceDocument().GetInterfaceCases()) == 0 {
		return nil
	}

	members := make([]*dtctl.DispatchTaskMember, 0, len(x.Source().GetInterfaceDocument().GetInterfaceCases()))
	for idx, item := range x.Source().GetInterfaceDocument().GetInterfaceCases() {
		case_ := item.GetInterfaceCase()
		if !x.ValidCase(case_) {
			continue
		}

		x.IncrTotal()
		members = append(
			members, &dtctl.DispatchTaskMember{
				ComponentId:   case_.GetCaseId(),
				ComponentType: item.GetType(),
				Version:       case_.GetVersion(),
				Index:         idx,
				Data:          item,
			},
		)
	}

	return members
}

func (x *InterfaceDocument) PublishOne(member *dtctl.DispatchTaskMember) (*dispatcher.PublishResp, error) {
	document := x.Source().GetInterfaceDocument()
	case_ := document.GetInterfaceCases()[member.Index]

	in := &dispatcher.PublishReq{
		TriggerMode: x.Source().GetTriggerMode(),
		TriggerRule: x.Source().GetTriggerRule(),
		ProjectId:   x.Source().GetProjectId(),
		TaskId:      x.Source().GetTaskId(),
		ExecuteId:   member.ComponentExecuteId,
		ExecuteType: x.Source().GetExecuteType(),
		PublishType: pb.PublishType_PublishType_INTERFACE_CASE,
		UserId:      x.Source().GetUserId(),
		Debug:       x.Source().GetDebug(),
		Data: &pb.PublishReq_InterfaceCase{
			InterfaceCase: &pb.InterfaceCasePublishInfo{
				InterfaceCaseId:    member.ComponentId,
				GeneralConfig:      x.Source().GetGeneralConfig(),
				AccountConfig:      x.Source().GetAccountConfig(),
				Version:            member.Version,
				InterfaceId:        document.GetInterfaceDocumentId(),
				InterfaceExecuteId: document.GetInterfaceDocumentExecuteId(),
				DocumentId:         document.GetInterfaceDocumentId(),
				CaseName:           case_.GetInterfaceCase().GetName(),
				MaintainedBy:       case_.GetInterfaceCase().GetMaintainedBy(),
				DocumentName:       document.GetInterfaceDocument().GetName(),
				PlanId:             document.GetPlanId(),
				PlanExecuteId:      document.GetPlanExecuteId(),
				PlanName:           document.GetPlanName(),
			},
		},
	}
	return x.AsyncPublishTask(in, member)
}

func (x *InterfaceDocument) record() (resp *reporterpb.ModifyInterfaceRecordResponse, err error) {
	var endedAt int64 = 0
	content := x.Content()
	if x.Total() == 0 || x.StateM().State() == pb.ComponentState_Panic {
		endedAt = time.Now().UnixMilli()
		content = jsonx.MarshalToStringIgnoreError(x.StateM().Debug())
	}
	resp, err = x.ServiceContext().ReporterRpc.ModifyInterfaceRecord(
		x.Context(), &reporterpb.PutInterfaceRecordRequest{
			TaskId:             x.Source().GetTaskId(),
			ExecuteId:          x.Source().GetInterfaceDocument().GetInterfaceDocumentExecuteId(),
			ExecuteType:        x.Source().GetExecuteType().String(),
			ProjectId:          x.Source().GetProjectId(),
			InterfaceId:        x.Source().GetInterfaceDocument().GetInterfaceDocumentId(),
			InterfaceExecuteId: x.Source().GetInterfaceDocument().GetInterfaceDocumentExecuteId(),
			InterfaceName:      x.Source().GetInterfaceDocument().GetInterfaceDocument().GetName(),
			PlanExecuteId:      x.Source().GetInterfaceDocument().GetPlanExecuteId(),
			Status:             x.StateM().State().String(),
			TotalCase:          x.Total(),
			Content:            content,
			StartedAt:          time.Now().UnixMilli(),
			EndedAt:            endedAt,
			ExecutedBy:         x.Source().GetUserId(),
		},
	)
	if err != nil {
		return nil, err
	}

	return
}

func (x *InterfaceDocument) Callback(err error) error {
	// 空集合、异常需要直接回调
	if x.Total() != 0 && err == nil {
		return nil
	}

	cb := &pb.CallbackReq{
		TriggerMode:  x.Source().GetTriggerMode(),
		TriggerRule:  x.Source().GetTriggerRule(),
		ProjectId:    x.Source().GetProjectId(),
		TaskId:       x.Source().GetTaskId(),
		ExecuteType:  x.Source().GetExecuteType(),
		CallbackType: pb.CallbackType_CallbackType_INTERFACE_DOCUMENT,
		Data: &pb.CallbackReq_InterfaceDocument{
			InterfaceDocument: &pb.InterfaceDocumentCallbackData{
				PlanId:             x.Source().GetInterfaceDocument().GetPlanId(),
				PlanExecuteId:      x.Source().GetInterfaceDocument().GetPlanExecuteId(),
				InterfaceId:        x.Source().GetInterfaceDocument().GetInterfaceDocumentId(),
				InterfaceExecuteId: x.Source().GetInterfaceDocument().GetInterfaceDocumentExecuteId(),
				InterfaceState:     x.StateM().State(),
			},
		},
	}

	return callbackclient.CallBack(
		x.Context(),
		x.ServiceContext().DispatcherProducer,
		x.ServiceContext().Config.Name,
		x.ServiceContext().Config.DispatcherProducer.Queue,
		x.UUID(),
		cb,
	)
}

// ValidCase 有效的case
func (x *InterfaceDocument) ValidCase(case_ *managerpb.InterfaceCaseComponent) bool {
	if x.Source().GetExecuteType() == managerpb.ApiExecutionDataType_API_PLAN {
		// 计划执行类型，固有状态 和 引用状态 均为enable，才属于有效
		return (case_.GetState() == managerpb.ResourceState_RS_PUBLISHED || case_.GetState() == managerpb.ResourceState_RS_ENABLED) && case_.GetReferenceState() == managerpb.CommonState_CS_ENABLE
	}

	// [2024-04-16] delete this rule:
	// 其他类型 固有状态 为true则有效
	// return case_.GetState() == managerpb.ResourceState_RS_PUBLISHED || case_.GetState() == managerpb.ResourceState_RS_ENABLED
	return true
}

func (x *InterfaceDocument) UUID() string {
	return fmt.Sprintf(
		"%s::%s", x.Source().GetTaskId(), x.Source().GetInterfaceDocument().GetInterfaceDocumentExecuteId(),
	)
}

func (x *InterfaceDocument) Content() string {
	return x.logW.toJson()
}
