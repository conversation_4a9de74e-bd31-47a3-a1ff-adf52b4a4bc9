package uireport

import (
	"context"
	"encoding/json"
	"errors"
	"net/http"
	"reflect"
	"strings"
	"time"

	"github.com/valyala/fasthttp"
	"github.com/zeromicro/go-zero/core/jsonx"
	"github.com/zeromicro/go-zero/core/logx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"

	commonpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/mqc/internal/logic/callback"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/mqc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/rpc/pb"
	managerpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
	reporterpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
)

type UIReport struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewUIReport(ctx context.Context, svcCtx *svc.ServiceContext) *UIReport {
	return &UIReport{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (x *UIReport) Run(req *pb.UIReportCallback) (err error) {
	_ = x.callback(req)

	err = x.saveReport(req)
	if err != nil {
		return err
	}

	notifyRequest := &callback.UINotifyRequest{
		SCtx:          x.svcCtx,
		Ctx:           x.ctx,
		ProjectId:     req.ProjectId,
		PlanId:        req.PlanId,
		TaskId:        req.TaskId,
		PlanExecuteId: req.PlanExecuteId,
		ReportViewUrl: req.ReportViewUrl,
	}
	err = callback.SendUINotify(notifyRequest)
	if err != nil {
		return err
	}

	return nil
}

func (x *UIReport) saveReport(req *pb.UIReportCallback) (err error) {
	// 记录用例数
	caseInfos, err := x.svcCtx.UIReporterRpc.GetUIPlanCasesInfo(
		x.ctx, &reporterpb.GetUIPlanCasesInfoRequest{
			ProjectId: req.GetProjectId(),
			TaskId:    req.GetTaskId(),
			ExecuteId: req.GetPlanExecuteId(),
		},
	)
	if err != nil {
		return err
	}

	out, err := x.svcCtx.UIReporterRpc.Cli.ViewUIPlanRecord(
		x.ctx, &reporterpb.ViewUIPlanRecordRequest{
			TaskId:    req.GetTaskId(),
			ProjectId: req.GetProjectId(),
			ExecuteId: req.GetPlanExecuteId(),
		},
	)
	if err != nil {
		return err
	}

	content := out.GetContent()
	data := make(map[string]any)
	_ = jsonx.UnmarshalFromString(content, &data)

	data["report_view_url"] = req.GetReportViewUrl()
	data["report_download_url"] = req.GetReportDownloadUrl()
	out.Content, err = jsonx.MarshalToString(data)
	if err != nil {
		return err
	}
	endTime := time.Now().UnixMilli()
	x.TaskInfoProcessorFinal(req, out, endTime)
	_, err = x.svcCtx.UIReporterRpc.ModifyUIPlanRecord(
		x.ctx, &reporterpb.PutUIPlanRecordRequest{
			ProjectId:  out.GetProjectId(),
			PlanId:     out.GetPlanId(),
			PlanName:   out.GetPlanName(),
			TaskId:     out.GetTaskId(),
			ExecuteId:  out.GetExecuteId(),
			Status:     out.GetStatus(),
			ExecutedBy: out.GetExecutedBy(),
			StartedAt:  out.GetStartedAt(),
			// EndedAt:       out.GetEndedAt(),
			TotalSuite:    out.GetTotalSuite(),
			FinishedSuite: out.GetFinishedSuite(),
			SuccessSuite:  out.GetSuccessSuite(),
			TotalCase:     caseInfos.GetTotalCase(),
			SuccessCase:   caseInfos.GetSuccessCase(),
			FinishedCase:  caseInfos.GetFinishedCase(),
			Content:       out.GetContent(),
			Finished:      1,
			ExecuteData:   out.GetExecuteData(),
			EndedAt:       endTime,
		},
	)
	if err != nil {
		return err
	}

	return nil
}

func (x *UIReport) TaskInfoProcessorFinal(
	req *pb.UIReportCallback, out *reporterpb.ViewUIPlanRecordResponse, endTime int64,
) {
	_ = x.svcCtx.TaskInfoProcessor.UpdateReportViewUrl(
		x.ctx, out.GetProjectId(), out.GetTaskId(), req.GetReportViewUrl(),
	)
	_ = x.svcCtx.TaskInfoProcessor.UpdateReportDownloadUrl(
		x.ctx, out.GetProjectId(), out.GetTaskId(), req.GetReportDownloadUrl(),
	)
	var taskExecutedResult commonpb.ExecutedResult
	switch out.GetStatus() {
	case pb.ComponentState_Success.String():
		taskExecutedResult = commonpb.ExecutedResult_TER_SUCCESS
	case pb.ComponentState_Failure.String():
		taskExecutedResult = commonpb.ExecutedResult_TER_FAILURE
	case pb.ComponentState_Stop.String(), pb.ComponentState_Panic.String():
		taskExecutedResult = commonpb.ExecutedResult_TER_PANIC
	default:
		return
	}
	_ = x.svcCtx.TaskInfoProcessor.UpdateTaskExecutedResult(
		x.ctx, out.GetProjectId(), out.GetTaskId(), taskExecutedResult, endTime,
	)
}

type CallBackData struct {
	ProjectId            string `json:"project_id"`            // 项目ID
	PlanId               string `json:"plan_id"`               // 计划ID
	Name                 string `json:"name"`                  // 名称
	Description          string `json:"description"`           // 描述
	Type                 string `json:"type"`                  // 计划类型（手动、定时、接口）
	CronExpression       string `json:"cron_expression"`       // 定时触发计划的Cron表达式
	ExecutionMode        string `json:"execution_mode"`        // 执行方式
	PlatformType         string `json:"platform_type"`         // 测试系统（Android、IOS）
	PackageName          string `json:"package_name"`          // 包名，用于启动APP
	AppDownloadLink      string `json:"app_download_link"`     // APP下载地址
	AppVersion           string `json:"app_version"`           // APP版本
	ExecutionEnvironment string `json:"execution_environment"` // 执行环境
	FailRetry            int8   `json:"fail_retry"`            // 失败重试（0次、1次、2次）
	MaintainedBy         string `json:"maintained_by"`         // 维护者
	CreatedBy            string `json:"created_by"`            // 创建者
	UpdatedBy            string `json:"updated_by"`            // 更新者
	CreatedAt            int64  `json:"created_at"`            // 创建时间
	UpdatedAt            int64  `json:"updated_at"`            // 更新时间
	TaskId               string `json:"task_id"`               // 任务ID
	TotalCase            int64  `json:"total_case"`            // 测试用例总数
	SuccessCase          int64  `json:"success_case"`          // 成功用例总数
	ReportViewUrl        string `json:"report_view_url"`       // 报告浏览地址
	ReportDownloadUrl    string `json:"report_download_url"`   // 报告下载地址
	GitConfigId          string `json:"git_config_id"`         // git配置id
}

func (x *UIReport) callback(in *pb.UIReportCallback) (err error) {
	pResp, err := x.svcCtx.ManagerRpc.ViewUiPlan(
		x.ctx, &managerpb.ViewUiPlanReq{
			ProjectId: in.GetProjectId(),
			PlanId:    in.GetPlanId(),
		},
	)
	if err != nil {
		return err
	}
	planRecord, err := x.svcCtx.UIReporterRpc.ViewUIPlanRecord(
		x.ctx, &reporterpb.ViewUIPlanRecordRequest{
			ProjectId: in.GetProjectId(),
			TaskId:    in.GetTaskId(),
			ExecuteId: in.GetPlanExecuteId(),
		},
	)
	if err != nil {
		return err
	}

	planExecuteData := &managerpb.UIPlanComponent{}
	err = protobuf.UnmarshalJSONFromString(planRecord.ExecuteData, planExecuteData)
	if err != nil {
		return err
	}

	plan := pResp.Plan
	callbackUrl := planExecuteData.GetMetaData().GetCallbackUrl()
	if callbackUrl == "" {
		return nil
	}
	data := CallBackData{
		ProjectId:            planExecuteData.GetProjectId(),
		PlanId:               planExecuteData.GetPlanId(),
		Name:                 planExecuteData.GetName(),
		Description:          planExecuteData.GetDescription(),
		Type:                 planExecuteData.GetType().String(),
		CronExpression:       plan.GetCronExpression(),
		ExecutionMode:        planExecuteData.GetMetaData().GetCaseExecutionMode().String(),
		PlatformType:         planExecuteData.GetMetaData().GetPlatformType().String(),
		PackageName:          planExecuteData.GetMetaData().GetPackageName(),
		AppDownloadLink:      planExecuteData.GetMetaData().GetAppDownloadLink(),
		AppVersion:           planExecuteData.GetMetaData().GetAppVersion(),
		ExecutionEnvironment: planExecuteData.GetMetaData().GetExecutionEnvironment(),
		FailRetry:            int8(planExecuteData.GetMetaData().GetFailRetry()),
		MaintainedBy:         planExecuteData.GetMaintainedBy(),
		CreatedBy:            planExecuteData.GetCreatedBy(),
		UpdatedBy:            planExecuteData.GetUpdatedBy(),
		CreatedAt:            plan.GetCreatedAt(),
		UpdatedAt:            plan.GetUpdatedAt(),
		TaskId:               in.GetTaskId(),
		TotalCase:            planRecord.GetTotalCase(),
		SuccessCase:          planRecord.GetSuccessCase(),
		ReportDownloadUrl:    in.GetReportDownloadUrl(),
		ReportViewUrl:        in.GetReportViewUrl(),
		GitConfigId:          planExecuteData.GetMetaData().GetGitConfig().GetConfigId(),
	}
	jsonData, err := json.Marshal(data)
	if err != nil {
		x.Logger.Errorf(
			"json marshal error: project_id: %s, plan_id: %s, task_id: %s",
			in.GetProjectId(), in.GetPlanId(), in.GetTaskId(),
		)
		return err
	}
	s, err := jsonx.MarshalToString(data)
	if err != nil {
		s = "MarshalToString error"
	}

	x.Logger.Infof(
		"callback json info: project_id: %s, plan_id: %s, task_id: %s, json: %s",
		in.GetProjectId(), in.GetPlanId(), in.GetTaskId(), s,
	)
	for _, url := range strings.Split(callbackUrl, ";") {
		x.Logger.Infof(
			"callback url info: project_id: %s, plan_id: %s, task_id: %s, url: %s",
			in.GetProjectId(), in.GetPlanId(), in.GetTaskId(), url,
		)
		maxIdleConnDuration, _ := time.ParseDuration("1h")
		req := fasthttp.AcquireRequest()
		req.SetRequestURI(url)
		req.Header.SetMethod("POST")
		req.Header.SetContentTypeBytes([]byte("application/json"))
		req.SetBodyRaw(jsonData)

		resp := fasthttp.AcquireResponse()

		fClient := &fasthttp.Client{
			MaxConnsPerHost:     100,
			MaxIdleConnDuration: maxIdleConnDuration,
		}

		err = fClient.DoTimeout(req, resp, 15*time.Second)
		if err != nil {
			errName, known := httpConnError(err)
			if known {
				x.Logger.Errorf(
					"third part callback known error %v: project_id: %s, plan_id: %s, task_id: %s",
					errName, in.GetProjectId(), in.GetPlanId(), in.GetTaskId(),
				)
			} else {
				x.Logger.Errorf(
					"third part callback unknown error %v: project_id: %s, plan_id: %s, task_id: %s, error: %v",
					errName, in.GetProjectId(), in.GetPlanId(), in.GetTaskId(), err,
				)
			}
		}

		statusCode := resp.StatusCode()
		respBody := resp.String()
		x.Logger.Infof(
			"third part callback response: project_id: %s, plan_id: %s, task_id: %s, body: %s", in.GetProjectId(),
			in.GetPlanId(), in.GetTaskId(), respBody,
		)

		if statusCode != http.StatusOK {
			x.Logger.Infof(
				"third part callback invalid HTTP response code: project_id: %s, plan_id: %s, task_id: %s, code: %d",
				in.GetProjectId(), in.GetPlanId(), in.GetTaskId(), statusCode,
			)
		} else {
			x.Logger.Infof(
				"third part callback success: project_id: %s, plan_id: %s, task_id: %s", in.GetProjectId(),
				in.GetPlanId(), in.GetTaskId(),
			)
		}
		fasthttp.ReleaseRequest(req)
		fasthttp.ReleaseResponse(resp)
	}
	return nil
}

func httpConnError(err error) (string, bool) {
	var (
		errName string
		known   = true
	)

	switch {
	case errors.Is(err, fasthttp.ErrTimeout):
		errName = "timeout"
	case errors.Is(err, fasthttp.ErrNoFreeConns):
		errName = "conn_limit"
	case errors.Is(err, fasthttp.ErrConnectionClosed):
		errName = "conn_close"
	case reflect.TypeOf(err).String() == "*net.OpError":
		errName = "timeout"
	default:
		known = false
	}

	return errName, known
}
