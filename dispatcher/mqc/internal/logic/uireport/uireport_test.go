package uireport

import (
	"encoding/json"
	"fmt"
	"net/http"
	"strings"
	"testing"
	"time"

	"github.com/valyala/fasthttp"
	"github.com/zeromicro/go-zero/core/jsonx"
	"github.com/zeromicro/go-zero/core/logx"
)

func TestCallBack(t *testing.T) {
	callbackUrl := "http://api-proxy.ttyuyin.com/router/v1/client/create"
	data := CallBackData{
		ProjectId:            "project_id:test",
		PlanId:               "plan_id:test",
		Name:                 "name:test",
		Description:          "desc:test",
		Type:                 "type:test",
		CronExpression:       "* * * * *",
		ExecutionMode:        "mode:test",
		PlatformType:         "platform:test",
		PackageName:          "package:test",
		AppDownloadLink:      "download:test",
		AppVersion:           "version:test",
		ExecutionEnvironment: "env:test",
		FailRetry:            int8(1),
		MaintainedBy:         "T1111",
		CreatedBy:            "T1111",
		UpdatedBy:            "T1111",
		CreatedAt:            1702970921928,
		UpdatedAt:            1702970921928,
		TaskId:               "task_id:test",
		TotalCase:            100,
		SuccessCase:          50,
		ReportDownloadUrl:    "report_url:test",
		ReportViewUrl:        "view_url:test",
		GitConfigId:          "config_id:test",
	}
	jsonData, err := json.Marshal(data)
	if err != nil {
		logx.Errorf(
			"json marshal error: project_id: %s, plan_id: %s, task_id: %s",
			data.ProjectId, data.PlanId, data.TaskId,
		)
		t.Errorf("err: %s", err)
		t.FailNow()
	}
	s, err := jsonx.MarshalToString(data)
	if err != nil {
		s = "MarshalToString error"
	}

	logx.Infof(
		"callback json info: project_id: %s, plan_id: %s, task_id: %s, json: %s",
		data.ProjectId, data.PlanId, data.TaskId, s,
	)

	for _, url := range strings.Split(callbackUrl, ";") {
		logx.Infof(
			"callback url info: project_id: %s, plan_id: %s, task_id: %s, url: %s",
			data.ProjectId, data.PlanId, data.TaskId, url,
		)
		maxIdleConnDuration, _ := time.ParseDuration("1h")
		req := fasthttp.AcquireRequest()
		req.SetRequestURI(url)
		req.Header.SetMethod("POST")
		req.Header.SetContentTypeBytes([]byte("application/json"))
		req.SetBodyRaw(jsonData)

		resp := fasthttp.AcquireResponse()

		fClient := &fasthttp.Client{
			MaxConnsPerHost:     100,
			MaxIdleConnDuration: maxIdleConnDuration,
		}

		err = fClient.DoTimeout(req, resp, 15*time.Second)
		if err != nil {
			errName, known := httpConnError(err)
			if known {
				logx.Errorf(
					"third part callback known error %v: project_id: %s, plan_id: %s, task_id: %s",
					errName, data.ProjectId, data.PlanId, data.TaskId,
				)
			} else {
				logx.Errorf(
					"third part callback unknown error %v: project_id: %s, plan_id: %s, task_id: %s, error: %v",
					errName, data.ProjectId, data.PlanId, data.TaskId, err,
				)
			}
		}

		statusCode := resp.StatusCode()
		respBody := resp.Body()
		logx.Infof(
			"third part callback response: project_id: %s, plan_id: %s, task_id: %s, body: %s",
			data.ProjectId, data.PlanId, data.TaskId, respBody,
		)

		if statusCode != http.StatusOK {
			logx.Infof(
				"third part callback invalid HTTP response code: project_id: %s, plan_id: %s, task_id: %s, code: %d",
				data.ProjectId, data.PlanId, data.TaskId, statusCode,
			)
		} else {
			logx.Infof(
				"third part callback success: project_id: %s, plan_id: %s, task_id: %s",
				data.ProjectId, data.PlanId, data.TaskId,
			)
		}
		fasthttp.ReleaseRequest(req)
		fasthttp.ReleaseResponse(resp)
	}
	if err != nil {
		t.Errorf("err: %s", err)
		t.FailNow()
	}
}

func TestRequest(t *testing.T) {
	// 创建一个请求对象
	req := fasthttp.AcquireRequest()
	defer fasthttp.ReleaseRequest(req)

	// 设置请求的URL
	req.SetRequestURI("http://api-proxy.ttyuyin.com/router/v1/client/create")

	// 设置请求方法为POST
	req.Header.SetMethod("POST")

	// 设置请求体内容
	req.SetBody([]byte("这是请求的内容"))

	// 创建一个响应对象
	resp := fasthttp.AcquireResponse()
	defer fasthttp.ReleaseResponse(resp)

	// 发送请求并接收响应
	if err := fasthttp.Do(req, resp); err != nil {
		fmt.Printf("请求发送失败: %s\n", err)
		return
	}

	// 打印响应内容
	fmt.Printf("响应内容: %s\n", resp.Body())
}
