package perfplan

import (
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/mqc/internal/logic/baselogic"
	managerpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type PerfPlan_Log struct {
	*baselogic.DispatchLogic_Log
	SuiteExecutionMode int64           `json:"suite_execution_mode"` // 集合执行方式
	CaseExecutionMode  int64           `json:"case_execution_mode"`  // 用例执行方式
	PriorityType       pb.PriorityType `json:"priority_type"`        // 优先级
}

type PerfPlanLogWriter struct {
	*baselogic.DispatchLogicLogWriter

	log *PerfPlan_Log
}

func NewPerfPlanLogWriter(bw *baselogic.DispatchLogicLogWriter) *PerfPlanLogWriter {
	writer := &PerfPlanLogWriter{
		DispatchLogicLogWriter: bw,
		log: &PerfPlan_Log{
			DispatchLogic_Log: bw.Log(),
		},
	}
	return writer
}

func (writer *PerfPlanLogWriter) toJson() string {
	return baselogic.MarshalJson(writer.log)
}

func (writer *PerfPlanLogWriter) setSuiteExecutionMode(mode managerpb.ExecutionMode) {
	writer.log.SuiteExecutionMode = int64(mode)
}

func (writer *PerfPlanLogWriter) setCaseExecutionMode(mode managerpb.ExecutionMode) {
	writer.log.CaseExecutionMode = int64(mode)
}

func (writer *PerfPlanLogWriter) setPriorityType(priorityType pb.PriorityType) {
	writer.log.PriorityType = priorityType
}
