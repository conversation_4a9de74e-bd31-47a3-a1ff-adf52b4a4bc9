package perfplan

import (
	"context"
	"fmt"
	"sort"
	"time"

	"google.golang.org/protobuf/types/known/timestamppb"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworkerv2/base"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/codes"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	commonpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/common/dtctl"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/common/errcode"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/mqc/internal/logic/baselogic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/mqc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/rpc/client/dispatcher"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/rpc/pb"
	managerpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
	reporterpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
)

type PerfPlan struct {
	*baselogic.DispatchLogic
	logW *PerfPlanLogWriter
}

func NewPerfPlan(ctx context.Context, svcCtx *svc.ServiceContext) *PerfPlan {
	return &PerfPlan{
		DispatchLogic: baselogic.NewDispatchLogic(ctx, svcCtx),
	}
}

func (x *PerfPlan) Run(ctx context.Context, req *pb.DistributeReq) (err error) {
	x.Setup(req)

	err = x.run(ctx)

	x.Teardown()

	return err
}

func (x *PerfPlan) Setup(req *pb.DistributeReq) {
	x.DispatchLogic.Setup(req)
	x.logW = NewPerfPlanLogWriter(x.DispatchLogic.LogWriter())
	x.SetDtCtl(x.GetDtControl())
	x.logW.setPriorityType(req.GetPriorityType())
}

func (x *PerfPlan) run(_ context.Context) (err error) {
	defer func() {
		// 空计划默认成功
		if x.Total() == 0 {
			x.StateM().UpdateStateManager(pb.ComponentState_Success, errcode.GetErrCode(errorx.OK))
		}

		if r := recover(); r != any(nil) {
			err = x.UpdatePanicStatef(codes.TaskPanic, "%v", r)
		}

		// 更新执行状态
		_, err = x.record()
		if err != nil {
			err = x.UpdatePanicStatef(codes.TaskRecordFailure, "Plan.record: %s", err)
		}

		x.Infof("Plan[%s] : %s", x.Source().GetPerfPlan().GetPerfPlanId(), x.Content())

		// 发送压测预告通知
		x.sendPreviewNotification()
	}()

	err = x.Publish(x.GetSuiteMembers(), x.PublishSuiteOne)
	return err
}

func (x *PerfPlan) GetDtControl() *dtctl.DispatchTaskControl {
	source := x.Source()
	member := &dtctl.DispatchTaskMember{
		ComponentId:        source.GetPerfPlan().GetPerfPlanId(),
		ComponentType:      managerpb.ApiExecutionDataType_PERF_PLAN,
		Version:            "",
		ComponentExecuteId: source.GetPerfPlan().GetPerfPlanExecuteId(),
	}
	return dtctl.NewDispatchTaskControl(x.Context(), x.ServiceContext().Redis, member)
}

func (x *PerfPlan) GetSuiteMembers() []*dtctl.DispatchTaskMember {
	suites := x.Source().GetPerfPlan().GetPerfSuites()
	if len(suites) == 0 {
		return nil
	}

	members := make([]*dtctl.DispatchTaskMember, 0, len(suites))
	for idx, item := range suites {
		suite := item.GetPerfSuite()
		if !x.ValidSuite(suite) {
			continue
		}

		x.IncrTotal()
		members = append(
			members, &dtctl.DispatchTaskMember{
				ComponentId:   suite.GetSuiteId(),
				ComponentType: item.GetType(),
				Index:         idx,
			},
		)
	}

	return members
}

func (x *PerfPlan) PublishSuiteOne(member *dtctl.DispatchTaskMember) (*dispatcher.PublishResp, error) {
	cases := make([]*managerpb.ApiExecutionData, 0, 1)
	suite := x.Source().GetPerfPlan().GetPerfSuites()[member.Index]
	if len(suite.GetChildren()) > 0 {
		cases = suite.GetChildren()[0].GetChild()
	}

	in := &dispatcher.PublishReq{
		TriggerMode:  x.Source().GetTriggerMode(),
		TriggerRule:  x.Source().GetTriggerRule(),
		ProjectId:    x.Source().GetProjectId(),
		TaskId:       x.Source().GetTaskId(),
		ExecuteId:    member.ComponentExecuteId,
		ExecuteType:  x.Source().GetExecuteType(),
		PublishType:  pb.PublishType_PublishType_PERF_SUITE,
		UserId:       x.Source().GetUserId(),
		Debug:        x.Source().GetDebug(),
		PriorityType: x.Source().GetPriorityType(),
		Data: &pb.PublishReq_PerfSuite{
			PerfSuite: &pb.PerfSuitePublishInfo{
				PerfSuiteId:       member.ComponentId,
				PerfPlanId:        x.Source().GetPerfPlan().GetPerfPlanId(),
				PerfPlanExecuteId: x.Source().GetPerfPlan().GetPerfPlanExecuteId(),
				PerfSuite:         suite,
				PerfCases:         cases,
				MetaData:          x.Source().GetPerfPlan().GetPerfPlan().GetMetaData(),
				PerfPlanInfo:      x.Source().GetPerfPlan().GetPerfPlanInfo(),
			},
		},
	}
	return x.AsyncPublishTask(in, member)
}

func (x *PerfPlan) record() (resp *reporterpb.ModifyPerfPlanRecordResp, err error) {
	x.updateTaskInfo()
	req := &reporterpb.ModifyPerfPlanRecordReq{
		ProjectId: x.Source().GetProjectId(),
		PlanId:    x.Source().GetPerfPlan().GetPerfPlanId(),
		TaskId:    x.Source().GetTaskId(),
		ExecuteId: x.Source().GetPerfPlan().GetPerfPlanExecuteId(),
		Status:    x.StateM().State().String(),
	}
	if x.Total() == 0 || x.StateM().State() == pb.ComponentState_Panic {
		req.EndedAt = timestamppb.New(time.Now())
	}

	resp, err = x.ServiceContext().PerfReporterRpc.ModifyPerfPlanRecord(
		x.Context(), req,
	)
	if err != nil {
		return nil, err
	}

	return
}

func (x *PerfPlan) updateTaskInfo() {
	// 先更新总数
	_ = x.ServiceContext().TaskInfoProcessor.UpdateTotalSuite(
		x.Context(), x.Source().GetProjectId(), x.Source().GetTaskId(), x.Total(),
	)
	// 只要关注这三个状态
	var t commonpb.ExecutedResult
	switch x.StateM().State() {
	case pb.ComponentState_Panic:
		t = commonpb.ExecutedResult_TER_PANIC
	case pb.ComponentState_Failure:
		t = commonpb.ExecutedResult_TER_FAILURE
	case pb.ComponentState_Success:
		t = commonpb.ExecutedResult_TER_SUCCESS
	}
	if t == commonpb.ExecutedResult_TER_INIT {
		return
	}
	// 用例只记录总数和panic情况
	x.Infof(
		"PerfPlan updateTaskInfo[%s] : taskId:%s,TaskExecutedResult", t, x.Source().GetTaskId(),
	)
	_ = x.ServiceContext().TaskInfoProcessor.UpdateTaskExecutedResult(
		x.Context(), x.Source().GetProjectId(), x.Source().GetTaskId(), t, time.Now().UnixMilli(),
	)
}

func (x *PerfPlan) sendPreviewNotification() {
	var (
		taskID    = x.Source().GetTaskId()
		executeID = x.Source().GetPerfPlan().GetPerfPlanExecuteId()
		projectID = x.Source().GetProjectId()
		planID    = x.Source().GetPerfPlan().GetPerfPlanId()

		executeType = x.Source().GetPerfPlan().GetPerfPlanInfo().GetExecuteType()
		needToSend  = x.Source().GetPerfPlan().GetPerfPlanInfo().GetSendPreviewNotification()
	)

	cases := make([]*pb.PerfReportCallback_PerfCase, 0, constants.ConstDefaultMakeSliceSize)
	suites := x.Source().GetPerfPlan().GetPerfSuites()
	for _, item1 := range suites {
		suite := item1.GetPerfSuite()
		if suite == nil {
			continue
		}
		if len(item1.GetChildren()) == 0 {
			continue
		}

		for _, item2 := range item1.GetChildren()[0].GetChild() {
			case_ := item2.GetPerfCase()
			if case_ == nil {
				continue
			}
			if case_.GetState() != managerpb.CommonState_CS_ENABLE {
				continue
			}

			cmds := make([]uint32, 0, len(case_.GetSerialSteps())+len(case_.GetParallelSteps()))
			for _, steps := range [][]*commonpb.PerfCaseStepV2{
				case_.GetSerialSteps(),
				case_.GetParallelSteps(),
			} {
				for _, step := range steps {
					if cmd := step.GetCmd(); cmd != 0 {
						cmds = append(cmds, cmd)
					}
				}
			}
			sort.Slice(
				cmds, func(i, j int) bool {
					return cmds[i] < cmds[j]
				},
			)

			cases = append(
				cases, &pb.PerfReportCallback_PerfCase{
					SuiteName: suite.GetName(),
					CaseName:  case_.GetName(),
					TargetRps: case_.GetTargetRps(),
					Cmds:      cmds,
				},
			)
		}
	}

	payload := protobuf.MarshalJSONIgnoreError(
		&pb.PerfReportCallback{
			ProjectId:     projectID,
			TaskId:        taskID,
			PlanId:        planID,
			PlanExecuteId: executeID,
			Stage:         pb.StageType_ST_PREVIEW,
			Cases:         cases,
		},
	)

	// save the callback data into cache
	key := fmt.Sprintf("%s:%s", common.ConstCachePerfPlanNotificationCallbackDataTaskIDPrefix, taskID)
	result, err := x.ServiceContext().Redis.Set(x.Context(), key, payload, 3*time.Hour).Result()
	if err != nil {
		x.Errorf("failed to save the callback data into cache, key: %s, value: %s, error: %+v", key, payload, err)
	} else {
		x.Infof(
			"save the callback data into cache successfully, key: %s, value: %s, result: %s",
			key, payload, result,
		)
	}

	if executeType != commonpb.PerfTaskType_RUN {
		x.Infof(
			"sending the preview notification is only required when running the perf plan task, task_id: %s, execute_id: %s, project_id: %s, plan_id: %s, execute_type: %s",
			taskID, executeID, projectID, planID, executeType,
		)
		return
	}

	if !needToSend {
		x.Infof(
			"no need to send perf preview notification, task_id: %s, execute_id: %s, project_id: %s, plan_id: %s, send_preview_notification: %t",
			taskID, executeID, projectID, planID, needToSend,
		)
		return
	}

	if err := x.SendTaskToSelf(
		base.NewTask(
			constants.MQTaskTypeDispatcherSendPerfNotification,
			payload,
			base.WithMaxRetryOptions(0),
			base.WithRetentionOptions(5*time.Minute),
		),
	); err != nil {
		x.Errorf(
			"failed to send perf preview notification task to dispatcher, task_id: %s, execute_id: %s, project_id: %s, plan_id: %s, error: %+v",
			taskID, executeID, projectID, planID, err,
		)
	} else {
		x.Infof(
			"send perf preview notification task successfully, task_id: %s, execute_id: %s, project_id: %s, plan_id: %s",
			taskID, executeID, projectID, planID,
		)
	}
}

// ValidSuite 有效的suite
func (x *PerfPlan) ValidSuite(_ *managerpb.PerfSuiteComponent) bool {
	// 固有状态 和 引用状态 均为enable，才属于有效
	// return suite.GetState() == managerpb.CommonState_CS_ENABLE
	return true
}

func (x *PerfPlan) Content() string {
	return x.logW.toJson()
}
