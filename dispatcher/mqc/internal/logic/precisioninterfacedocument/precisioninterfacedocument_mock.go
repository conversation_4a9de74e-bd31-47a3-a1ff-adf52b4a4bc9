package precisioninterfacedocument

import (
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/utils"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/common/mock"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/mqc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/rpc/pb"
	managerpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

func Mock_InterfaceDocument_DistributeReq(caseNum int) *pb.DistributeReq {
	param := mock.Mock_DistributeReq(managerpb.ApiExecutionDataType_INTERFACE_DOCUMENT)
	param.GeneralConfig = mock.Mock_GeneralConfig(param.GetProjectId())
	param.AccountConfig = mock.Mock_AccountConfig(param.GetProjectId())

	param.GetInterfaceDocument().InterfaceCases = make([]*managerpb.ApiExecutionData, caseNum)
	for i := 0; i < caseNum; i++ {
		icaseId := utils.GenInterfaceCaseId()
		param.GetInterfaceDocument().InterfaceCases[i] = &managerpb.ApiExecutionData{
			Id:   icaseId,
			Type: managerpb.ApiExecutionDataType_INTERFACE_CASE,
			Data: &managerpb.ApiExecutionData_InterfaceCase{
				InterfaceCase: &managerpb.InterfaceCaseComponent{
					ProjectId:  param.GetProjectId(),
					DocumentId: param.GetInterfaceDocument().GetInterfaceDocumentId(),
					CaseId:     icaseId,
					Version:    utils.GenVersion(),
					State:      managerpb.ResourceState_RS_PUBLISHED,
				},
			},
		}
	}

	return param
}

func Mock_default_InterfaceDocument() *PrecisionInterfaceDocument {
	return NewPrecisionInterfaceDocument(mock.Mock_Context(), svc.Mock_ServiceContext())
}
