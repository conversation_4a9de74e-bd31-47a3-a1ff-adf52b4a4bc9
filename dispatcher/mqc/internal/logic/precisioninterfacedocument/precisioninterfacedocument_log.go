package precisioninterfacedocument

import (
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/mqc/internal/logic/baselogic"
	managerpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type InterfaceCase_Log struct {
	*baselogic.DispatchLogic_Log
	CaseExecutionMode int64 `json:"case_execution_mode"` // 用例执行方式
}

type InterfaceCaseLogWriter struct {
	*baselogic.DispatchLogicLogWriter

	log *InterfaceCase_Log
}

func NewInterfaceCaseLogWriter(bw *baselogic.DispatchLogicLogWriter) *InterfaceCaseLogWriter {
	writer := &InterfaceCaseLogWriter{
		DispatchLogicLogWriter: bw,
		log: &InterfaceCase_Log{
			DispatchLogic_Log: bw.Log(),
		},
	}
	return writer
}

func (writer *InterfaceCaseLogWriter) to<PERSON><PERSON>() string {
	return baselogic.<PERSON><PERSON><PERSON>(writer.log)
}

func (writer *InterfaceCaseLogWriter) setCaseExecutionMode(mode managerpb.ExecutionMode) {
	writer.log.CaseExecutionMode = int64(mode)
}
