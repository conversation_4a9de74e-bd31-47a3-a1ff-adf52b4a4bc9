package uiplan

import (
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/utils"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/common/mock"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/mqc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/rpc/pb"
	managerpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

func Mock_Plan_DistributeReq(suiteNum, idocNum int) *pb.DistributeReq {
	param := mock.Mock_DistributeReq(managerpb.ApiExecutionDataType_API_PLAN)
	param.GeneralConfig = mock.Mock_GeneralConfig(param.GetProjectId())
	param.AccountConfig = mock.Mock_AccountConfig(param.GetProjectId())

	param.GetPlan().Suites = make([]*managerpb.ApiExecutionData, suiteNum)
	for i := 0; i < suiteNum; i++ {
		suiteId := utils.GenSuiteId()
		param.GetPlan().Suites[i] = &managerpb.ApiExecutionData{
			Id:   suiteId,
			Type: managerpb.ApiExecutionDataType_API_SUITE,
			Data: &managerpb.ApiExecutionData_Suite{
				Suite: &managerpb.SuiteComponent{
					ProjectId:         param.GetProjectId(),
					SuiteId:           suiteId,
					State:             managerpb.CommonState_CS_ENABLE,
					ReferenceState:    managerpb.CommonState_CS_ENABLE,
					CaseExecutionMode: managerpb.ExecutionMode_EM_PARALLEL,
				},
			},
		}
	}

	param.GetPlan().InterfaceDocument = make([]*managerpb.ApiExecutionData, idocNum)
	for i := 0; i < idocNum; i++ {
		idocId := utils.GenInterfaceDocumentId()
		param.GetPlan().InterfaceDocument[i] = &managerpb.ApiExecutionData{
			Id:   idocId,
			Type: managerpb.ApiExecutionDataType_INTERFACE_DOCUMENT,
			Data: &managerpb.ApiExecutionData_InterfaceDocument{
				InterfaceDocument: &managerpb.InterfaceDocumentComponent{
					ProjectId:         param.GetProjectId(),
					DocumentId:        idocId,
					State:             managerpb.CommonState_CS_ENABLE,
					ReferenceState:    managerpb.CommonState_CS_ENABLE,
					CaseExecutionMode: managerpb.ExecutionMode_EM_PARALLEL,
				},
			},
		}
	}

	return param
}

func Mock_Plan() *UIPlan {
	return NewUIPlan(mock.Mock_Context(), svc.Mock_ServiceContext())
}
