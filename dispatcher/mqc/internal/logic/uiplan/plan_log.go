package uiplan

import (
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/mqc/internal/logic/baselogic"
	managerpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type UIPlan_Log struct {
	*baselogic.DispatchLogic_Log
	SuiteExecutionMode int64           `json:"suite_execution_mode"` // 集合执行方式
	CaseExecutionMode  int64           `json:"case_execution_mode"`  // 用例执行方式
	PriorityType       pb.PriorityType `json:"priority_type"`        // 优先级
}

type UIPlanLogWriter struct {
	*baselogic.DispatchLogicLogWriter

	log *UIPlan_Log
}

func NewUIPlanLogWriter(bw *baselogic.DispatchLogicLogWriter) *UIPlanLogWriter {
	writer := &UIPlanLogWriter{
		DispatchLogicLogWriter: bw,
		log: &UIPlan_Log{
			DispatchLogic_Log: bw.Log(),
		},
	}
	return writer
}

func (writer *UIPlanLogWriter) toJson() string {
	return baselogic.MarshalJson(writer.log)
}

func (writer *UIPlanLogWriter) setSuiteExecutionMode(mode managerpb.ExecutionMode) {
	writer.log.SuiteExecutionMode = int64(mode)
}

func (writer *UIPlanLogWriter) setCaseExecutionMode(mode managerpb.ExecutionMode) {
	writer.log.CaseExecutionMode = int64(mode)
}

func (writer *UIPlanLogWriter) setPriorityType(priorityType pb.PriorityType) {
	writer.log.PriorityType = priorityType
}
