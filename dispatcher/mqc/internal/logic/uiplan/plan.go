package uiplan

import (
	"context"
	"time"

	"github.com/zeromicro/go-zero/core/jsonx"
	"github.com/zeromicro/go-zero/core/logx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/codes"
	commonpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/common/dtctl"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/common/errcode"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/mqc/internal/logic/baselogic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/mqc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/rpc/client/dispatcher"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/rpc/pb"
	managerpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
	reporterpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
)

type UIPlan struct {
	*baselogic.DispatchLogic
	logW *UIPlanLogWriter
}

func NewUIPlan(ctx context.Context, svcCtx *svc.ServiceContext) *UIPlan {
	return &UIPlan{
		DispatchLogic: baselogic.NewDispatchLogic(ctx, svcCtx),
	}
}

func (x *UIPlan) Run(ctx context.Context, req *pb.DistributeReq) (err error) {
	x.Setup(req)

	err = x.run(ctx)

	x.Teardown()

	return err
}

func (x *UIPlan) Setup(req *pb.DistributeReq) {
	x.DispatchLogic.Setup(req)
	x.logW = NewUIPlanLogWriter(x.DispatchLogic.LogWriter())
	x.SetDtCtl(x.GetDtControl())
	x.logW.setSuiteExecutionMode(req.GetUiPlan().GetUiPlan().GetMetaData().GetSuiteExecutionMode())
	x.logW.setCaseExecutionMode(req.GetUiPlan().GetUiPlan().GetMetaData().GetCaseExecutionMode())
	x.logW.setPriorityType(req.GetPriorityType())
}

func (x *UIPlan) run(_ context.Context) (err error) {
	defer func() {
		// 空计划默认成功
		if x.Total() == 0 {
			x.StateM().UpdateStateManager(pb.ComponentState_Success, errcode.GetErrCode(errorx.OK))
		}

		if r := recover(); r != any(nil) {
			err = x.UpdatePanicStatef(codes.TaskPanic, "%v", r)
		}

		// 更新执行状态
		_, err = x.record()
		if err != nil {
			err = x.UpdatePanicStatef(codes.TaskRecordFailure, "Plan.record: %s", err)
		}

		x.Infof("Plan[%s] : %s", x.Source().GetUiPlan().GetUiPlanId(), x.Content())
	}()
	if err != nil {
		return err
	}
	err = x.Publish(x.GetSuiteMembers(), x.PublishSuiteOne)
	if err != nil {
		return err
	}

	return err
}

func (x *UIPlan) GetDtControl() *dtctl.DispatchTaskControl {
	source := x.Source()
	member := &dtctl.DispatchTaskMember{
		ComponentId:        source.GetUiPlan().GetUiPlanId(),
		ComponentType:      managerpb.ApiExecutionDataType_UI_PLAN,
		Version:            "",
		ComponentExecuteId: source.GetUiPlan().GetUiPlanExecuteId(),
	}
	return dtctl.NewDispatchTaskControl(x.Context(), x.ServiceContext().Redis, member)
}

func (x *UIPlan) GetSuiteMembers() []*dtctl.DispatchTaskMember {
	if len(x.Source().GetUiPlan().GetUiSuites()) == 0 {
		return nil
	}

	members := make([]*dtctl.DispatchTaskMember, 0, len(x.Source().GetUiPlan().GetUiSuites()))
	for idx, item := range x.Source().GetUiPlan().GetUiSuites() {
		suite := item.GetUiSuite()
		if !x.ValidSuite(suite) {
			continue
		}

		x.IncrTotal()
		members = append(
			members, &dtctl.DispatchTaskMember{
				ComponentId:   suite.GetSuiteId(),
				ComponentType: item.GetType(),
				Index:         idx,
			},
		)
	}

	return members
}

func (x *UIPlan) PublishSuiteOne(member *dtctl.DispatchTaskMember) (*dispatcher.PublishResp, error) {
	cases := make([]*managerpb.ApiExecutionData, 0, 1)
	suite := x.Source().GetUiPlan().GetUiSuites()[member.Index]
	if len(suite.GetChildren()) > 0 {
		cases = suite.GetChildren()[0].GetChild()
	}

	in := &dispatcher.PublishReq{
		TriggerMode:  x.Source().GetTriggerMode(),
		TriggerRule:  x.Source().GetTriggerRule(),
		ProjectId:    x.Source().GetProjectId(),
		TaskId:       x.Source().GetTaskId(),
		ExecuteId:    member.ComponentExecuteId,
		ExecuteType:  x.Source().GetExecuteType(),
		PublishType:  pb.PublishType_PublishType_UI_SUITE,
		UserId:       x.Source().GetUserId(),
		Debug:        x.Source().GetDebug(),
		PriorityType: x.Source().GetPriorityType(),
		Data: &pb.PublishReq_UiSuite{
			UiSuite: &pb.UISuitePublishInfo{
				UiSuiteId:       member.ComponentId,
				UiPlanId:        x.Source().GetUiPlan().GetUiPlanId(),
				UiPlanExecuteId: x.Source().GetUiPlan().GetUiPlanExecuteId(),
				UiCases:         cases,
				UiSuite:         suite,
				MetaData:        x.Source().GetUiPlan().GetMetaData(),
			},
		},
	}
	return x.AsyncPublishTask(in, member)
}

func (x *UIPlan) record() (resp *reporterpb.ModifyUIPlanRecordResponse, err error) {
	x.updateTaskInfo()
	var endtime int64 = 0
	content := x.Content()
	if x.Total() == 0 || x.StateM().State() == pb.ComponentState_Panic {
		endtime = time.Now().UnixMilli()
		content = jsonx.MarshalToStringIgnoreError(x.StateM().Debug())
	}
	resp, err = x.ServiceContext().UIReporterRpc.ModifyUIPlanRecord(
		x.Context(), &reporterpb.PutUIPlanRecordRequest{
			ProjectId:  x.Source().GetProjectId(),
			PlanId:     x.Source().GetUiPlan().GetUiPlanId(),
			PlanName:   x.Source().GetUiPlan().GetUiPlan().GetName(),
			TaskId:     x.Source().GetTaskId(),
			ExecuteId:  x.Source().GetUiPlan().GetUiPlanExecuteId(),
			Status:     x.StateM().State().String(),
			ExecutedBy: x.Source().GetUserId(),
			// StartedAt:  time.Now().UnixMilli(),
			EndedAt:    endtime,
			TotalSuite: x.Total(),
			Content:    content,
		},
	)
	if err != nil {
		return nil, err
	}

	return
}

func (x *UIPlan) updateTaskInfo() {
	// 先更新总数
	_ = x.ServiceContext().TaskInfoProcessor.UpdateTotalSuite(
		x.Context(), x.Source().GetProjectId(), x.Source().GetTaskId(), x.Total(),
	)
	// 只要关注这三个状态
	var t commonpb.ExecutedResult
	switch x.StateM().State() {
	case pb.ComponentState_Panic:
		t = commonpb.ExecutedResult_TER_PANIC
	case pb.ComponentState_Failure:
		t = commonpb.ExecutedResult_TER_FAILURE
	case pb.ComponentState_Success:
		t = commonpb.ExecutedResult_TER_SUCCESS
	}
	if t == commonpb.ExecutedResult_TER_INIT {
		return
	}
	// 用例只记录总数和panic情况
	logx.WithContext(x.Context()).Infof(
		"UIPlan updateTaskInfo[%s] : taskId:%s,TaskExecutedResult", t, x.Source().GetTaskId(),
	)
	_ = x.ServiceContext().TaskInfoProcessor.UpdateTaskExecutedResult(
		x.Context(), x.Source().GetProjectId(), x.Source().GetTaskId(), t, time.Now().UnixMilli(),
	)
}

// ValidSuite 有效的suite
func (x *UIPlan) ValidSuite(suite *managerpb.UISuiteComponent) bool {
	// 固有状态 和 引用状态 均为enable，才属于有效
	// return suite.GetState() == managerpb.CommonState_CS_ENABLE
	return true
}

func (x *UIPlan) Content() string {
	return x.logW.toJson()
}
