package service

import (
	"context"
	"fmt"
	"time"

	"github.com/zeromicro/go-zero/core/jsonx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/codes"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/common/callbackclient"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/common/dtctl"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/common/errcode"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/mqc/internal/logic/baselogic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/mqc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/rpc/client/dispatcher"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/rpc/pb"
	managerpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
	reporterpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
)

type Service struct {
	*baselogic.DispatchLogic
	logW *ServiceLogWriter
}

func NewService(ctx context.Context, svcCtx *svc.ServiceContext) *Service {
	return &Service{
		DispatchLogic: baselogic.NewDispatchLogic(ctx, svcCtx),
	}
}

func (x *Service) Run(req *pb.DistributeReq) (err error) {
	x.Setup(req)

	err = x.run()

	x.Teardown()

	return err
}

func (x *Service) Setup(req *pb.DistributeReq) {
	x.DispatchLogic.Setup(req)
	x.logW = NewSuiteLogWriter(x.DispatchLogic.LogWriter())
	x.SetDtCtl(x.GetDtControl())
}

func (x *Service) run() (err error) {
	defer func() {
		x.Infof("Service source = %s", x.Source())

		// 空集合默认成功
		if x.Total() == 0 {
			x.StateM().UpdateStateManager(pb.ComponentState_Success, errcode.GetErrCode(errorx.OK))
		}

		if r := recover(); r != any(nil) {
			err = x.UpdatePanicStatef(codes.TaskPanic, "%v", r)
		}

		// 更新执行状态
		_, err = x.record()
		if err != nil {
			err = x.UpdatePanicStatef(codes.TaskRecordFailure, "Service.record: %s", err)
		}

		// 如果是空集合/异常，则直接回调
		err = x.Callback(err)
		if err != nil {
			err = x.UpdatePanicStatef(codes.TaskRecordFailure, "Service.Callback: %s", err)
		}

		x.Infof("Service[%s] : %s", x.Source().GetService().GetServiceId(), x.Content())
	}()

	err = x.Publish(x.GetChildCaseMembers(), x.PublishCaseOne)
	if err != nil {
		return err
	}

	err = x.Publish(x.GetChildInterfaceCaseMembers(), x.PublishInterfaceCaseOne)
	if err != nil {
		return err
	}

	return nil
}

func (x *Service) GetDtControl() *dtctl.DispatchTaskControl {
	source := x.Source()
	member := &dtctl.DispatchTaskMember{
		ComponentId:        source.GetService().GetServiceId(),
		ComponentType:      managerpb.ApiExecutionDataType_API_SERVICE,
		Version:            "",
		ComponentExecuteId: source.GetService().GetServiceExecuteId(),
	}
	return dtctl.NewDispatchTaskControl(x.Context(), x.ServiceContext().Redis, member)
}

func (x *Service) GetChildCaseMembers() []*dtctl.DispatchTaskMember {
	if len(x.Source().GetService().GetCases()) == 0 {
		return nil
	}

	members := make([]*dtctl.DispatchTaskMember, 0, len(x.Source().GetService().GetCases()))
	for idx, item := range x.Source().GetService().GetCases() {
		case_ := item.GetCase()
		if !x.ValidCase(case_) {
			continue
		}

		x.IncrTotal()
		members = append(
			members, &dtctl.DispatchTaskMember{
				ComponentId:   case_.GetCaseId(),
				ComponentType: item.GetType(),
				Version:       case_.GetVersion(),
				Index:         idx,
				Data:          item,
			},
		)
	}
	return members
}

func (x *Service) GetChildInterfaceCaseMembers() []*dtctl.DispatchTaskMember {
	if len(x.Source().GetService().GetInterfaceCases()) == 0 {
		return nil
	}

	members := make([]*dtctl.DispatchTaskMember, 0, len(x.Source().GetService().GetInterfaceCases()))
	for idx, item := range x.Source().GetService().GetInterfaceCases() {
		case_ := item.GetInterfaceCase()
		if !x.ValidInterfaceCase(case_) {
			continue
		}

		x.IncrTotal()
		members = append(
			members, &dtctl.DispatchTaskMember{
				ComponentId:   case_.GetCaseId(),
				ComponentType: item.GetType(),
				Version:       case_.GetVersion(),
				Index:         idx,
				Data:          item,
			},
		)
	}
	return members
}

func (x *Service) PublishCaseOne(member *dtctl.DispatchTaskMember) (*dispatcher.PublishResp, error) {
	service := x.Source().GetService()
	case_ := service.GetCases()[member.Index]

	in := &dispatcher.PublishReq{
		TriggerMode: x.Source().GetTriggerMode(),
		TriggerRule: x.Source().GetTriggerRule(),
		ProjectId:   x.Source().GetProjectId(),
		TaskId:      x.Source().GetTaskId(),
		ExecuteId:   member.ComponentExecuteId,
		ExecuteType: x.Source().GetExecuteType(),
		PublishType: pb.PublishType_PublishType_API_CASE,
		UserId:      x.Source().GetUserId(),
		PurposeType: x.Source().GetPurposeType(),
		Debug:       x.Source().GetDebug(),
		Data: &pb.PublishReq_Case{
			Case: &pb.CasePublishInfo{
				CaseId:         member.ComponentId,
				GeneralConfig:  x.Source().GetGeneralConfig(),
				AccountConfig:  x.Source().GetAccountConfig(),
				Version:        member.Version,
				SuiteId:        service.GetServiceId(), // 注：由于是虚拟集合，因此这里设置的是`service_id`
				SuiteExecuteId: service.GetServiceExecuteId(),
				CaseName:       case_.GetCase().GetName(),
				MaintainedBy:   case_.GetCase().GetMaintainedBy(),
				SuiteName:      service.GetServiceName(), // 注：由于是虚拟集合，因此这里设置的是`service_name`
				PlanId:         service.GetPlanId(),
				PlanExecuteId:  service.GetPlanExecuteId(),
				PlanName:       service.GetPlanName(),
			},
		},
	}
	return x.AsyncPublishTask(in, member)
}

func (x *Service) PublishInterfaceCaseOne(member *dtctl.DispatchTaskMember) (*dispatcher.PublishResp, error) {
	service := x.Source().GetService()
	case_ := service.GetInterfaceCases()[member.Index]

	in := &dispatcher.PublishReq{
		TriggerMode: x.Source().GetTriggerMode(),
		TriggerRule: x.Source().GetTriggerRule(),
		ProjectId:   x.Source().GetProjectId(),
		TaskId:      x.Source().GetTaskId(),
		ExecuteId:   member.ComponentExecuteId,
		ExecuteType: x.Source().GetExecuteType(),
		PublishType: pb.PublishType_PublishType_INTERFACE_CASE,
		UserId:      x.Source().GetUserId(),
		PurposeType: x.Source().GetPurposeType(),
		Debug:       x.Source().GetDebug(),
		Data: &pb.PublishReq_InterfaceCase{
			InterfaceCase: &pb.InterfaceCasePublishInfo{
				InterfaceCaseId:    member.ComponentId,
				GeneralConfig:      x.Source().GetGeneralConfig(),
				AccountConfig:      x.Source().GetAccountConfig(),
				Version:            member.Version,
				InterfaceId:        service.GetServiceId(), // 注：由于是虚拟集合，因此这里设置的是`service_id`（不是`document_id`）
				InterfaceExecuteId: service.GetServiceExecuteId(),
				DocumentId:         case_.GetInterfaceCase().GetDocumentId(),
				CaseName:           case_.GetInterfaceCase().GetName(),
				MaintainedBy:       case_.GetInterfaceCase().GetMaintainedBy(),
				DocumentName:       service.GetServiceName(), // 注：由于是虚拟集合，因此这里设置的是`service_name`
				PlanId:             service.GetPlanId(),
				PlanExecuteId:      service.GetPlanExecuteId(),
				PlanName:           service.GetPlanName(),
			},
		},
	}
	return x.AsyncPublishTask(in, member)
}

func (x *Service) record() (resp *reporterpb.ModifyServiceRecordResponse, err error) {
	var endedAt int64 = 0
	content := x.Content()
	if x.Total() == 0 || x.StateM().State() == pb.ComponentState_Panic {
		endedAt = time.Now().UnixMilli()
		content = jsonx.MarshalToStringIgnoreError(x.StateM().Debug())
	}
	resp, err = x.ServiceContext().ReporterRpc.ModifyServiceRecord(
		x.Context(), &reporterpb.PutServiceRecordRequest{
			TaskId:           x.Source().GetTaskId(),
			ExecuteId:        x.Source().GetService().GetServiceExecuteId(),
			ExecuteType:      x.Source().GetExecuteType().String(),
			ProjectId:        x.Source().GetProjectId(),
			ServiceId:        x.Source().GetService().GetServiceId(),
			ServiceExecuteId: x.Source().GetService().GetServiceExecuteId(),
			ServiceName:      x.Source().GetService().GetService().GetServiceName(),
			PlanExecuteId:    x.Source().GetService().GetPlanExecuteId(),
			Status:           x.StateM().State().String(),
			TotalCase:        x.Total(),
			Content:          content,
			StartedAt:        time.Now().UnixMilli(),
			EndedAt:          endedAt,
			ExecutedBy:       x.Source().GetUserId(),
		},
	)
	if err != nil {
		return nil, err
	}

	return
}

func (x *Service) Callback(err error) error {
	// 空集合、异常需要直接回调
	if x.Total() != 0 && err == nil {
		return nil
	}

	cb := &pb.CallbackReq{
		TriggerMode:  x.Source().GetTriggerMode(),
		TriggerRule:  x.Source().GetTriggerRule(),
		ProjectId:    x.Source().GetProjectId(),
		TaskId:       x.Source().GetTaskId(),
		ExecuteType:  x.Source().GetExecuteType(),
		CallbackType: pb.CallbackType_CallbackType_API_SERVICE,
		PurposeType:  x.Source().GetPurposeType(),
		Data: &pb.CallbackReq_Service{
			Service: &pb.ServiceCallbackData{
				PlanId:           x.Source().GetService().GetPlanId(),
				PlanExecuteId:    x.Source().GetService().GetPlanExecuteId(),
				ServiceId:        x.Source().GetService().GetServiceId(),
				ServiceExecuteId: x.Source().GetService().GetServiceExecuteId(),
				ServiceState:     x.StateM().State(),
			},
		},
	}

	return callbackclient.CallBack(
		x.Context(),
		x.ServiceContext().DispatcherProducer,
		x.ServiceContext().Config.Name,
		x.ServiceContext().Config.DispatcherProducer.Queue,
		x.UUID(),
		cb,
	)
}

// ValidCase 有效的case
func (x *Service) ValidCase(case_ *managerpb.CaseComponent) bool {
	if x.Source().GetExecuteType() == managerpb.ApiExecutionDataType_API_PLAN {
		// 计划执行类型，固有状态 和 引用状态 均为enable，才属于有效
		return (case_.GetState() == managerpb.ResourceState_RS_PUBLISHED || case_.GetState() == managerpb.ResourceState_RS_ENABLED) && case_.GetReferenceState() == managerpb.CommonState_CS_ENABLE
	}

	// 其他类型 固有状态 为true则有效
	return case_.GetState() == managerpb.ResourceState_RS_PUBLISHED || case_.GetState() == managerpb.ResourceState_RS_ENABLED
}

// ValidInterfaceCase 有效的接口用例
func (x *Service) ValidInterfaceCase(case_ *managerpb.InterfaceCaseComponent) bool {
	if x.Source().GetExecuteType() == managerpb.ApiExecutionDataType_API_PLAN {
		// 计划执行类型，固有状态 和 引用状态 均为enable，才属于有效
		return (case_.GetState() == managerpb.ResourceState_RS_PUBLISHED || case_.GetState() == managerpb.ResourceState_RS_ENABLED) && case_.GetReferenceState() == managerpb.CommonState_CS_ENABLE
	}

	// [2024-04-16] delete this rule:
	// 其他类型 固有状态 为true则有效
	// return case_.GetState() == managerpb.ResourceState_RS_PUBLISHED || case_.GetState() == managerpb.ResourceState_RS_ENABLED
	return true
}

func (x *Service) UUID() string {
	return fmt.Sprintf("%s::%s", x.Source().GetTaskId(), x.Source().GetSuite().GetSuiteExecuteId())
}

func (x *Service) Content() string {
	return x.logW.toJson()
}
