package service

import (
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/mqc/internal/logic/baselogic"
)

type Service_Log struct {
	*baselogic.DispatchLogic_Log
}

type ServiceLogWriter struct {
	*baselogic.DispatchLogicLogWriter

	log *Service_Log
}

func NewSuiteLogWriter(bw *baselogic.DispatchLogicLogWriter) *ServiceLogWriter {
	writer := &ServiceLogWriter{
		DispatchLogicLogWriter: bw,
		log: &Service_Log{
			DispatchLogic_Log: bw.Log(),
		},
	}
	return writer
}

func (writer *ServiceLogWriter) to<PERSON><PERSON>() string {
	return baselogic.<PERSON><PERSON><PERSON>(writer.log)
}
