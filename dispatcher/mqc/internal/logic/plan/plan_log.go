package plan

import (
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/mqc/internal/logic/baselogic"
	managerpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type Plan_Log struct {
	*baselogic.DispatchLogic_Log
	SuiteExecutionMode int64 `json:"suite_execution_mode"` // 集合执行方式
	CaseExecutionMode  int64 `json:"case_execution_mode"`  // 用例执行方式
}

type PlanLogWriter struct {
	*baselogic.DispatchLogicLogWriter

	log *Plan_Log
}

func NewPlanLogWriter(bw *baselogic.DispatchLogicLogWriter) *PlanLogWriter {
	writer := &PlanLogWriter{
		DispatchLogicLogWriter: bw,
		log: &Plan_Log{
			DispatchLogic_Log: bw.Log(),
		},
	}
	return writer
}

func (writer *PlanLogWriter) to<PERSON><PERSON>() string {
	return baselogic.<PERSON><PERSON><PERSON>(writer.log)
}

func (writer *PlanLogWriter) setSuiteExecutionMode(mode managerpb.ExecutionMode) {
	writer.log.SuiteExecutionMode = int64(mode)
}

func (writer *PlanLogWriter) setCaseExecutionMode(mode managerpb.ExecutionMode) {
	writer.log.CaseExecutionMode = int64(mode)
}
