package plan

import (
	"context"
	"time"

	"github.com/zeromicro/go-zero/core/jsonx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/codes"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/common/dtctl"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/common/errcode"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/mqc/internal/logic/baselogic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/mqc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/rpc/pb"
	managerpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
	reporterpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
)

type Plan struct {
	*baselogic.DispatchLogic
	logW *PlanLogWriter
}

func NewPlan(ctx context.Context, svcCtx *svc.ServiceContext) *Plan {
	return &Plan{
		DispatchLogic: baselogic.NewDispatchLogic(ctx, svcCtx),
	}
}

func (x *Plan) Run(req *pb.DistributeReq) (err error) {
	x.Setup(req)

	err = x.run()

	x.Teardown()

	return err
}

func (x *Plan) Setup(req *pb.DistributeReq) {
	x.DispatchLogic.Setup(req)
	x.logW = NewPlanLogWriter(x.DispatchLogic.LogWriter())
	x.SetDtCtl(x.GetDtControl())
	x.logW.setSuiteExecutionMode(req.GetPlan().GetPlan().GetSuiteExecutionMode())
	x.logW.setCaseExecutionMode(req.GetPlan().GetPlan().GetCaseExecutionMode())
}

func (x *Plan) run() (err error) {
	defer func() {
		// 空计划默认成功
		if x.Total() == 0 {
			x.StateM().UpdateStateManager(pb.ComponentState_Success, errcode.GetErrCode(errorx.OK))
		}

		if panicmsg := recover(); panicmsg != any(nil) {
			err = x.UpdatePanicStatef(codes.TaskPanic, "%v", panicmsg)
		}

		// 更新执行状态
		_, err = x.record()
		if err != nil {
			err = x.UpdatePanicStatef(codes.TaskRecordFailure, "Plan.record: %s", err)
		}

		x.Infof("Plan[%s] : %s", x.Source().GetPlan().GetPlanId(), x.Content())
	}()

	err = x.Publish(x.GetSuiteMembers(), x.PublishSuiteOne)
	if err != nil {
		return err
	}

	err = x.Publish(x.GetInterfaceDocumentMembers(), x.PublishInterfaceDocumentOne)
	if err != nil {
		return err
	}

	err = x.Publish(x.GetServiceMembers(), x.PublishServiceOne)
	if err != nil {
		return err
	}

	return err
}

func (x *Plan) GetDtControl() *dtctl.DispatchTaskControl {
	source := x.Source()
	member := &dtctl.DispatchTaskMember{
		ComponentId:        source.GetPlan().GetPlanId(),
		ComponentType:      managerpb.ApiExecutionDataType_API_PLAN,
		Version:            "",
		ComponentExecuteId: source.GetPlan().GetPlanExecuteId(),
	}
	return dtctl.NewDispatchTaskControl(x.Context(), x.ServiceContext().Redis, member)
}

func (x *Plan) GetSuiteMembers() []*dtctl.DispatchTaskMember {
	if len(x.Source().GetPlan().GetSuites()) == 0 {
		return nil
	}

	members := make([]*dtctl.DispatchTaskMember, 0, len(x.Source().GetPlan().GetSuites()))
	for idx, item := range x.Source().GetPlan().GetSuites() {
		suite := item.GetSuite()
		if !x.ValidSuite(suite) {
			continue
		}

		x.IncrTotal()
		members = append(
			members, &dtctl.DispatchTaskMember{
				ComponentId:   suite.GetSuiteId(),
				ComponentType: item.GetType(),
				Index:         idx,
				Data:          item,
			},
		)
	}

	return members
}

func (x *Plan) GetInterfaceDocumentMembers() []*dtctl.DispatchTaskMember {
	if len(x.Source().GetPlan().GetInterfaceDocument()) == 0 {
		return nil
	}

	members := make([]*dtctl.DispatchTaskMember, 0, len(x.Source().GetPlan().GetInterfaceDocument()))
	for idx, item := range x.Source().GetPlan().GetInterfaceDocument() {
		document := item.GetInterfaceDocument()
		if !x.ValidInterfaceDocument(document) {
			continue
		}

		x.IncrTotal()
		members = append(
			members, &dtctl.DispatchTaskMember{
				ComponentId:   document.GetDocumentId(),
				ComponentType: item.GetType(),
				Index:         idx,
				Data:          item,
			},
		)
	}
	return members
}

func (x *Plan) GetServiceMembers() []*dtctl.DispatchTaskMember {
	if len(x.Source().GetPlan().GetServices()) == 0 {
		return nil
	}

	members := make([]*dtctl.DispatchTaskMember, 0, len(x.Source().GetPlan().GetServices()))
	for idx, item := range x.Source().GetPlan().GetServices() {
		service := item.GetService()
		x.IncrTotal()
		members = append(
			members, &dtctl.DispatchTaskMember{
				ComponentId:   service.GetServiceId(),
				ComponentType: item.GetType(),
				Index:         idx,
				Data:          item,
			},
		)
	}

	return members
}

func (x *Plan) PublishSuiteOne(member *dtctl.DispatchTaskMember) (*pb.PublishResp, error) {
	plan := x.Source().GetPlan()
	suite := plan.GetSuites()[member.Index]
	cases := make([]*managerpb.ApiExecutionData, 0, 1)
	if len(suite.GetChildren()) > 0 {
		cases = suite.GetChildren()[0].GetChild()
	}

	in := &pb.PublishReq{
		TriggerMode: x.Source().GetTriggerMode(),
		TriggerRule: x.Source().GetTriggerRule(),
		ProjectId:   x.Source().GetProjectId(),
		TaskId:      x.Source().GetTaskId(),
		ExecuteId:   member.ComponentExecuteId,
		ExecuteType: x.Source().GetExecuteType(),
		PublishType: pb.PublishType_PublishType_API_SUITE,
		UserId:      x.Source().GetUserId(),
		PurposeType: x.Source().GetPurposeType(),
		Debug:       x.Source().GetDebug(),
		Data: &pb.PublishReq_Suite{
			Suite: &pb.SuitePublishInfo{
				CaseExecutionMode: plan.GetPlan().GetCaseExecutionMode(),
				SuiteId:           member.ComponentId,
				GeneralConfig:     x.Source().GetGeneralConfig(),
				AccountConfig:     x.Source().GetAccountConfig(),
				PlanId:            plan.GetPlanId(),
				PlanExecuteId:     plan.GetPlanExecuteId(),
				Cases:             cases,
				PlanName:          plan.GetPlan().GetName(),
			},
		},
	}
	return x.AsyncPublishTask(in, member)
}

func (x *Plan) PublishInterfaceDocumentOne(member *dtctl.DispatchTaskMember) (*pb.PublishResp, error) {
	plan := x.Source().GetPlan()
	document := plan.GetInterfaceDocument()[member.Index]
	cases := make([]*managerpb.ApiExecutionData, 0, 1)
	if len(document.GetChildren()) > 0 {
		cases = document.GetChildren()[0].GetChild()
	}

	in := &pb.PublishReq{
		TriggerMode: x.Source().GetTriggerMode(),
		TriggerRule: x.Source().GetTriggerRule(),
		ProjectId:   x.Source().GetProjectId(),
		TaskId:      x.Source().GetTaskId(),
		ExecuteId:   member.ComponentExecuteId,
		ExecuteType: x.Source().GetExecuteType(),
		PublishType: pb.PublishType_PublishType_INTERFACE_DOCUMENT,
		UserId:      x.Source().GetUserId(),
		PurposeType: x.Source().GetPurposeType(),
		Debug:       x.Source().GetDebug(),
		Data: &pb.PublishReq_Interface{
			Interface: &pb.InterfaceDocumentPublishInfo{
				CaseExecutionMode:   plan.GetPlan().GetCaseExecutionMode(),
				InterfaceDocumentId: member.ComponentId,
				GeneralConfig:       x.Source().GetGeneralConfig(),
				AccountConfig:       x.Source().GetAccountConfig(),
				InterfaceCases:      cases,
				PlanId:              plan.GetPlanId(),
				PlanExecuteId:       plan.GetPlanExecuteId(),
				PlanName:            plan.GetPlan().GetName(),
			},
		},
	}
	return x.AsyncPublishTask(in, member)
}

func (x *Plan) PublishServiceOne(member *dtctl.DispatchTaskMember) (*pb.PublishResp, error) {
	plan := x.Source().GetPlan()
	service := plan.GetServices()[member.Index]

	cases := make([]*managerpb.ApiExecutionData, 0, 1)
	interfaceCases := make([]*managerpb.ApiExecutionData, 0, 1)
	if len(service.GetChildren()) > 0 && len(service.GetChildren()[0].GetChild()) > 0 {
		children := service.GetChildren()[0].GetChild()
		cases = make([]*managerpb.ApiExecutionData, 0, len(children))
		interfaceCases = make([]*managerpb.ApiExecutionData, 0, len(children))

		for i, c := range children {
			switch c.GetType() {
			case managerpb.ApiExecutionDataType_API_CASE:
				cases = append(cases, children[i])
			case managerpb.ApiExecutionDataType_INTERFACE_CASE:
				interfaceCases = append(interfaceCases, children[i])
			}
		}
	}

	in := &pb.PublishReq{
		TriggerMode: x.Source().GetTriggerMode(),
		TriggerRule: x.Source().GetTriggerRule(),
		ProjectId:   x.Source().GetProjectId(),
		TaskId:      x.Source().GetTaskId(),
		ExecuteId:   member.ComponentExecuteId,
		ExecuteType: x.Source().GetExecuteType(),
		PublishType: pb.PublishType_PublishType_API_SERVICE,
		UserId:      x.Source().GetUserId(),
		PurposeType: x.Source().GetPurposeType(),
		Debug:       x.Source().GetDebug(),
		Data: &pb.PublishReq_Service{
			Service: &pb.ServicePublishInfo{
				ServiceId:      member.ComponentId,
				ServiceName:    service.GetService().GetServiceName(),
				GeneralConfig:  x.Source().GetGeneralConfig(),
				AccountConfig:  x.Source().GetAccountConfig(),
				PlanId:         plan.GetPlanId(),
				PlanExecuteId:  plan.GetPlanExecuteId(),
				Cases:          cases,
				InterfaceCases: interfaceCases,
				PlanName:       plan.GetPlan().GetName(),
			},
		},
	}
	return x.AsyncPublishTask(in, member)
}

func (x *Plan) record() (resp *reporterpb.ModifyPlanRecordResponse, err error) {
	var endedAt int64 = 0
	content := x.Content()
	if x.Total() == 0 || x.StateM().State() == pb.ComponentState_Panic {
		endedAt = time.Now().UnixMilli()
		content = jsonx.MarshalToStringIgnoreError(x.StateM().Debug())
	}
	resp, err = x.ServiceContext().ReporterRpc.ModifyPlanRecord(
		x.Context(), &reporterpb.PutPlanRecordRequest{
			TaskId:        x.Source().GetTaskId(),
			ExecuteId:     x.Source().GetPlan().GetPlanExecuteId(),
			ProjectId:     x.Source().GetProjectId(),
			PlanId:        x.Source().GetPlan().GetPlanId(),
			PlanExecuteId: x.Source().GetPlan().GetPlanExecuteId(),
			PlanName:      x.Source().GetPlan().GetPlan().GetName(),
			Status:        x.StateM().State().String(),
			TotalSuite:    x.Total(),
			Content:       content,
			StartedAt:     time.Now().UnixMilli(),
			EndedAt:       endedAt,
			ExecutedBy:    x.Source().GetUserId(),
		},
	)
	if err != nil {
		return nil, err
	}

	return
}

// ValidSuite 有效的suite
func (x *Plan) ValidSuite(suite *managerpb.SuiteComponent) bool {
	// 固有状态 和 引用状态 均为enable，才属于有效
	return suite.GetState() == managerpb.CommonState_CS_ENABLE && suite.GetReferenceState() == managerpb.CommonState_CS_ENABLE
}

// ValidInterfaceDocument 有效的接口文档
func (x *Plan) ValidInterfaceDocument(idoc *managerpb.InterfaceDocumentComponent) bool {
	// 固有状态 和 引用状态 均为enable，才属于有效
	return idoc.GetState() == managerpb.CommonState_CS_ENABLE && idoc.GetReferenceState() == managerpb.CommonState_CS_ENABLE
}

func (x *Plan) Content() string {
	return x.logW.toJson()
}
