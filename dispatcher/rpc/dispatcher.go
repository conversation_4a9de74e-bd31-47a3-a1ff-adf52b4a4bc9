package main

import (
	"fmt"

	"github.com/spf13/cobra"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/version"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/cmd"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/rpc/server"
)

var configFile = new(string)

func main() {
	root := cmd.NewRootCommand()
	root.Args = cobra.NoArgs
	root.RunE = func(cmd *cobra.Command, args []string) error {
		s, err := server.NewRpcServer(*configFile)
		if err != nil {
			return err
		}

		defer s.Stop()

		fmt.Printf("Starting rpc server at %s...\n", s.Config().ListenOn())
		s.Start()

		return nil
	}

	vi := version.NewVersionInfo()
	root.Version = vi.String()

	flags := root.Flags()
	flags.SortFlags = false
	flags.StringVarP(configFile, "config", "f", "etc/dispatcher.yaml", "the config file")

	cobra.CheckErr(root.Execute())
}
