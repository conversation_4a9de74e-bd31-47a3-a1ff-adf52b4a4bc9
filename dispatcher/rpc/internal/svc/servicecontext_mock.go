package svc

import (
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/common/mock"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/rpc/internal/config"
)

func Mock_ServiceContext() *ServiceContext {
	mock.Mock_Log()

	svc := &ServiceContext{
		Config: config.Config{
			UIWorkerProducer:   mock.Mock_UIApiworkerProducerConf(),
			ApiWorkerProducer:  mock.Mock_ApiworkerProducerConf(),
			DispatcherProducer: mock.Mock_DispatcherProducerConf(),
		},

		Redis:              mock.Mock_Redis(),
		ReporterRPC:        mock.Mock_ReporterRpc(),
		ManagerRPC:         mock.Mock_ManagerRpc(),
		ApiWorkerProducer:  mock.Mock_ApiworkerProducer(),
		UIWorkerProducer:   mock.Mock_UIApiworkerProducer(),
		DispatcherProducer: mock.Mock_DispatcherProducer(),
	}

	return svc
}
