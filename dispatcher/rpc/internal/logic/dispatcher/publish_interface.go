package dispatcherlogic

import (
	"context"
	"time"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworkerv2/base"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/common/callbackclient"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/rpc/pb"
	managerpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
	reporterpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
)

type InterfacePublisher struct {
	*BasePublisher
}

func NewInterfacePublisher(ctx context.Context, svcCtx *svc.ServiceContext) *InterfacePublisher {
	return &InterfacePublisher{
		BasePublisher: NewBasePublisher(ctx, svcCtx),
	}
}

func (l *InterfacePublisher) CreateRecord(in *pb.PublishReq) (err error) {
	_, err = l.svcCtx.ReporterRPC.CreateInterfaceRecord(
		l.ctx, &reporterpb.PutInterfaceRecordRequest{
			TaskId:             l.taskId,
			ExecuteId:          l.executeId,
			ExecuteType:        in.GetExecuteType().String(),
			ProjectId:          in.GetProjectId(),
			GeneralConfig:      jsonx.MarshalToStringIgnoreError(Pb2ApiGeneralConfig(in.GetInterface().GetGeneralConfig())),
			AccountConfig:      jsonx.MarshalToStringIgnoreError(Pb2ApiAccountConfig(in.GetInterface().GetAccountConfig())),
			InterfaceId:        in.GetInterface().GetInterfaceDocumentId(),
			PlanExecuteId:      in.GetInterface().GetPlanExecuteId(),
			InterfaceExecuteId: l.executeId,
			ExecutedBy:         in.GetUserId(),
			StartedAt:          time.Now().UnixMilli(),
		},
	)
	if err != nil {
		return errors.Wrapf(
			errorx.Err(errorx.InternalError, err.Error()), "请求reporter rpc服务出现错误, error: %s", err,
		)
	}
	return
}

func (l *InterfacePublisher) Publish(in *pb.PublishReq) (resp *pb.PublishResp, err error) {
	return l.publish(in)
}

func (l *InterfacePublisher) publish(in *pb.PublishReq) (resp *pb.PublishResp, err error) {
	apiExecutionDataStructure, err := l.getApiExecutionDataStructure(
		&managerpb.GetApiExecutionDataStructureReq{
			ProjectId: in.GetProjectId(),
			Type:      managerpb.ApiExecutionDataType_INTERFACE_DOCUMENT,
			Id:        in.GetInterface().GetInterfaceDocumentId(),
			Extra:     nil,
		},
	)
	if err != nil {
		return nil, err
	}

	_, err = l.record(in, apiExecutionDataStructure)
	if err != nil {
		return resp, err
	}

	if !l.IsValid(apiExecutionDataStructure) || l.isStop {
		return &pb.PublishResp{TaskId: l.taskId, ExecuteId: l.executeId}, nil
	}

	taskData := l.getDistributeReq(in, apiExecutionDataStructure)
	payload, err := protobuf.MarshalJSON(taskData)
	if err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.SerializationError, err.Error()), "api任务参数序列化失败, error: %s", err,
		)
	}

	_, err = l.svcCtx.DispatcherProducer.Send(
		l.ctx, base.NewTask(
			constants.MQTaskTypeDispatcherInterface,
			payload,
			base.WithRetentionOptions(time.Minute*2),
			base.WithMaxRetryOptions(3),
		), base.QueuePriorityDefault,
	)
	if err != nil {
		return nil, errors.Wrapf(errorx.Err(errorx.InternalError, err.Error()), "发送数据到mq失败, error: %s", err)
	}

	return &pb.PublishResp{
		TaskId:    l.taskId,
		ExecuteId: l.executeId,
		Version:   l.getVersion(apiExecutionDataStructure),
	}, nil
}

func (l *InterfacePublisher) getDistributeReq(in *pb.PublishReq, data *managerpb.ApiExecutionData) *pb.DistributeReq {
	document := in.GetInterface()

	return &pb.DistributeReq{
		TriggerMode:    in.GetTriggerMode(),
		TriggerRule:    in.GetTriggerRule(),
		ProjectId:      in.GetProjectId(),
		TaskId:         l.taskId,
		ExecuteType:    in.GetExecuteType(),
		DistributeType: pb.DistributeType_DistributeType_INTERFACE_DOCUMENT,
		GeneralConfig:  document.GetGeneralConfig(),
		AccountConfig:  document.GetAccountConfig(),
		UserId:         in.GetUserId(),
		Debug:          in.GetDebug(),
		Data: &pb.DistributeReq_InterfaceDocument{
			InterfaceDocument: &pb.InterfaceDocumentDistributeData{
				InterfaceDocumentId:        document.GetInterfaceDocumentId(),
				InterfaceDocumentExecuteId: l.executeId,
				PlanId:                     document.GetPlanId(),
				PlanExecuteId:              document.GetPlanExecuteId(),
				State:                      pb.ComponentState_Pending,
				InterfaceDocument:          data.GetInterfaceDocument(),
				InterfaceCases:             l.getCases(in),
				PlanName:                   document.GetPlanName(),
			},
		},
	}
}

func (l *InterfacePublisher) record(
	in *pb.PublishReq, data *managerpb.ApiExecutionData,
) (resp *reporterpb.ModifyInterfaceRecordResponse, err error) {
	status := l.GetRecordStatus(l.IsValid(data))
	start, end := l.GetRecordTime(status)

	resp, err = l.svcCtx.ReporterRPC.ModifyInterfaceRecord(
		l.ctx, &reporterpb.PutInterfaceRecordRequest{
			TaskId:             l.taskId,
			ExecuteId:          l.executeId,
			ExecuteType:        in.GetExecuteType().String(),
			ProjectId:          in.GetProjectId(),
			GeneralConfig:      jsonx.MarshalToStringIgnoreError(Pb2ApiGeneralConfig(in.GetInterface().GetGeneralConfig())),
			AccountConfig:      jsonx.MarshalToStringIgnoreError(Pb2ApiAccountConfig(in.GetInterface().GetAccountConfig())),
			InterfaceId:        in.GetInterface().GetInterfaceDocumentId(),
			InterfaceName:      data.GetInterfaceDocument().GetName(),
			PlanExecuteId:      in.GetInterface().GetPlanExecuteId(),
			InterfaceExecuteId: l.executeId,
			Status:             status.String(),
			ExecutedBy:         in.GetUserId(),
			StartedAt:          start,
			EndedAt:            end,
		},
	)
	if err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.InternalError, err.Error()), "请求reporter rpc服务出现错误, error: %s", err,
		)
	}
	return
}

func (l *InterfacePublisher) getCases(in *pb.PublishReq) []*managerpb.ApiExecutionData {
	cases := make([]*managerpb.ApiExecutionData, 0, 1)
	if len(in.GetInterface().GetInterfaceCases()) > 0 {
		return in.GetInterface().GetInterfaceCases()
	}
	return cases
}

// IsValid 是否有效
func (l *InterfacePublisher) IsValid(data *managerpb.ApiExecutionData) bool {
	// 调试不受固有状态影响
	return true
}

// Panic 异常处理
func (l *InterfacePublisher) Panic(in *pb.PublishReq, err error) {
	if err == nil {
		return
	}

	// 直接更新状态
	_, _ = l.svcCtx.ReporterRPC.ModifyInterfaceRecord(
		l.ctx, &reporterpb.PutInterfaceRecordRequest{
			TaskId:             l.taskId,
			ProjectId:          in.GetProjectId(),
			ExecuteId:          l.executeId,
			ExecuteType:        in.GetExecuteType().String(),
			InterfaceId:        in.GetInterface().GetInterfaceDocumentId(),
			InterfaceExecuteId: l.executeId,
			Status:             pb.ComponentState_Panic.String(),
			ExecutedBy:         in.GetUserId(),
			Content:            jsonx.MarshalToStringIgnoreError(err.Error()),
			EndedAt:            time.Now().UnixMilli(),
		},
	)
	if in.GetExecuteType() == managerpb.ApiExecutionDataType_INTERFACE_DOCUMENT {
		return
	}

	// 发起回调
	cb := &pb.CallbackReq{
		TriggerMode:  in.GetTriggerMode(),
		TriggerRule:  in.GetTriggerRule(),
		ProjectId:    in.GetProjectId(),
		TaskId:       l.taskId,
		ExecuteType:  in.GetExecuteType(),
		CallbackType: pb.CallbackType_CallbackType_INTERFACE_DOCUMENT,
		PurposeType:  in.GetPurposeType(),
		Data: &pb.CallbackReq_InterfaceDocument{
			InterfaceDocument: &pb.InterfaceDocumentCallbackData{
				PlanId:             in.GetInterface().GetPlanId(),
				PlanExecuteId:      in.GetInterface().GetPlanExecuteId(),
				InterfaceId:        in.GetInterface().GetInterfaceDocumentId(),
				InterfaceExecuteId: l.executeId,
				InterfaceState:     pb.ComponentState_Panic,
			},
		},
	}

	_ = callbackclient.CallBack(
		l.ctx,
		l.svcCtx.DispatcherProducer,
		l.svcCtx.Config.Name,
		l.svcCtx.Config.DispatcherProducer.Queue,
		l.getUUID(),
		cb,
	)
}
