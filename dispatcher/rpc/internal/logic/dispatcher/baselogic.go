package dispatcherlogic

import (
	"context"
	"time"

	"github.com/zeromicro/go-zero/core/logx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/userinfo"
	qetutils "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	commonpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/rpc/pb"
)

var (
	workingTimeLeft, _  = time.Parse("15:04", constants.ConstWorkingTimeRangeOfLeft)
	workingTimeRight, _ = time.Parse("15:04", constants.ConstWorkingTimeRangeOfRight)
)

type BaseLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext

	currentUser *userinfo.UserInfo
	converters  []qetutils.TypeConverter
}

func newBaseLogic(ctx context.Context, svcCtx *svc.ServiceContext) *BaseLogic {
	return &BaseLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,

		currentUser: userinfo.FromContext(ctx),
		converters:  []qetutils.TypeConverter{},
	}
}

func (l *BaseLogic) checkPerfPlanExecution(in *pb.PublishReq) error {
	var (
		projectID   = in.GetProjectId()
		executeType = in.GetExecuteType()
		triggerMode = in.GetTriggerMode()
	)

	if in.GetPublishType() != pb.PublishType_PublishType_PERF_PLAN {
		return nil
	}

	perfPlan := in.GetPerfPlan()
	if perfPlan == nil {
		return errorx.Errorf(
			errorx.ValidateParamError,
			"the perf plan data cannot be null, project_id: %s, execute_type: %s",
			projectID, executeType,
		)
	}
	planID := perfPlan.GetPerfPlanId()

	if triggerMode == commonpb.TriggerMode_MANUAL {
		// [2025-07-04] 手动执行压测计划不受时间限制
		return nil
	} else if triggerMode == commonpb.TriggerMode_INTERFACE {
		return errorx.Errorf(
			errorx.ProhibitedBehavior,
			"the perf plan cannot be triggered by api, project_id: %s, execute_type: %s, plan_id: %s, trigger_mode: %s",
			projectID, executeType, planID, triggerMode,
		)
	}

	perfPlanInfo := perfPlan.GetPerfPlanInfo()
	if perfPlanInfo == nil {
		return errorx.Errorf(
			errorx.ValidateParamError,
			"the perf plan info cannot be null, project_id: %s, plan_type: %s, plan_id: %s",
			projectID, executeType, planID,
		)
	}

	now := time.Now()
	startedAt := now
	if estimatedTime := perfPlanInfo.GetEstimatedTime(); estimatedTime != 0 {
		startedAt = time.Unix(estimatedTime, 0)
		if now.After(startedAt) {
			return errorx.Errorf(
				errorx.ProhibitedBehavior,
				"the specified start time cannot be earlier than now, project_id: %s, execute_type: %s, plan_id: %s",
				projectID, executeType, planID,
			)
		} else if startedAt.Sub(now) > 30*time.Minute {
			return errorx.Errorf(
				errorx.ProhibitedBehavior,
				"the specified start time cannot be later than 30 minutes from now, project_id: %s, execute_type: %s, plan_id: %s",
				projectID, executeType, planID,
			)
		}
	}

	// check whether the start time is outside working hours
	if startedAt.Hour() < workingTimeLeft.Hour() ||
		(startedAt.Hour() == workingTimeLeft.Hour() && startedAt.Minute() < workingTimeLeft.Minute()) ||
		startedAt.Hour() > workingTimeRight.Hour() ||
		(startedAt.Hour() == workingTimeRight.Hour() && startedAt.Minute() > workingTimeRight.Minute()) {
		return errorx.Errorf(
			errorx.ProhibitedBehavior,
			"the start time cannot be outside working hours, %s not in [%s, %s]",
			startedAt.Format("15:04"), constants.ConstWorkingTimeRangeOfLeft, constants.ConstWorkingTimeRangeOfRight,
		)
	}

	// check whether the start time is on weekends
	if startedAt.Weekday() == time.Saturday || startedAt.Weekday() == time.Sunday {
		return errorx.Errorf(
			errorx.ProhibitedBehavior,
			"the start time cannot be on weekends, %s is weekend",
			startedAt.Format("2006-01-02"),
		)
	}

	return nil
}
