package dispatcherlogic

import (
	"context"
	"time"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworkerv2/base"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/common/callbackclient"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/rpc/pb"
	managerpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
	reporterpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
)

type PrecisionSuitePublisher struct {
	*BasePublisher
}

// Deprecated: invalid publish type
func NewPrecisionSuitePublisher(ctx context.Context, svcCtx *svc.ServiceContext) *PrecisionSuitePublisher {
	return &PrecisionSuitePublisher{
		BasePublisher: NewBasePublisher(ctx, svcCtx),
	}
}

func (l *PrecisionSuitePublisher) CreateRecord(in *pb.PublishReq) (err error) {
	_, err = l.svcCtx.ReporterRPC.CreateSuiteRecord(
		l.ctx, &reporterpb.PutSuiteRecordRequest{
			TaskId:         l.taskId,
			ExecuteId:      l.executeId,
			ProjectId:      in.GetProjectId(),
			ExecuteType:    in.GetExecuteType().String(),
			GeneralConfig:  jsonx.MarshalToStringIgnoreError(Pb2ApiGeneralConfig(in.GetPrecisionSuite().GetGeneralConfig())),
			AccountConfig:  jsonx.MarshalToStringIgnoreError(Pb2ApiAccountConfig(in.GetPrecisionSuite().GetAccountConfig())),
			SuiteId:        in.GetPrecisionSuite().GetSuiteId(),
			PlanExecuteId:  in.GetPrecisionSuite().GetPlanExecuteId(),
			SuiteExecuteId: l.executeId,
			ExecutedBy:     in.GetUserId(),
			StartedAt:      time.Now().UnixMilli(),
		},
	)
	if err != nil {
		return errors.Wrapf(
			errorx.Err(errorx.InternalError, err.Error()), "请求reporter rpc服务出现错误, error: %s", err,
		)
	}
	return nil
}

func (l *PrecisionSuitePublisher) Publish(in *pb.PublishReq) (resp *pb.PublishResp, err error) {
	return l.publish(in)
}

func (l *PrecisionSuitePublisher) publish(in *pb.PublishReq) (resp *pb.PublishResp, err error) {
	_, err = l.record(in)
	if err != nil {
		return resp, err
	}

	if !l.IsValid() || l.isStop {
		return &pb.PublishResp{TaskId: l.taskId, ExecuteId: l.executeId}, nil
	}

	// 创建任务请求
	taskData := l.getDistributeReq(in)
	payload, err := protobuf.MarshalJSON(taskData)
	if err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.SerializationError, err.Error()), "api任务参数序列化失败, error: %s", err,
		)
	}

	_, err = l.svcCtx.DispatcherProducer.Send(
		l.ctx, base.NewTask(
			constants.MQTaskTypeDispatcherPrecisionSuite,
			payload,
			base.WithRetentionOptions(time.Minute*2),
			base.WithMaxRetryOptions(0),
		), base.QueuePriorityDefault,
	)
	if err != nil {
		return nil, errors.Wrapf(errorx.Err(errorx.InternalError, err.Error()), "发送数据到mq失败, error: %s", err)
	}

	return &pb.PublishResp{
		TaskId:    l.taskId,
		ExecuteId: l.executeId,
		Version:   "",
	}, nil
}

func (l *PrecisionSuitePublisher) getDistributeReq(in *pb.PublishReq) *pb.DistributeReq {
	suite := in.GetPrecisionSuite()

	return &pb.DistributeReq{
		TriggerMode:    in.GetTriggerMode(),
		TriggerRule:    in.GetTriggerRule(),
		ProjectId:      in.GetProjectId(),
		TaskId:         l.taskId,
		ExecuteType:    in.GetExecuteType(),
		DistributeType: pb.DistributeType_DistributeType_API_SUITE,
		GeneralConfig:  suite.GetGeneralConfig(),
		AccountConfig:  suite.GetAccountConfig(),
		UserId:         in.GetUserId(),
		Debug:          in.GetDebug(),
		Data: &pb.DistributeReq_Suite{
			Suite: &pb.SuiteDistributeData{
				SuiteId:        suite.GetSuiteId(),
				SuiteExecuteId: l.executeId,
				PlanId:         suite.GetPlanId(),
				PlanExecuteId:  suite.GetPlanExecuteId(),
				State:          pb.ComponentState_Pending,
				Suite: &managerpb.SuiteComponent{
					ProjectId:         in.GetProjectId(),
					SuiteId:           suite.GetSuiteId(),
					Name:              suite.GetSuiteName(),
					State:             managerpb.CommonState_CS_ENABLE,
					ReferenceState:    managerpb.CommonState_CS_ENABLE,
					CaseExecutionMode: managerpb.ExecutionMode_EM_PARALLEL,
				},
				Cases:    l.getCases(in),
				PlanName: suite.GetPlanName(),
			},
		},
	}
}

func (l *PrecisionSuitePublisher) record(in *pb.PublishReq) (resp *reporterpb.ModifySuiteRecordResponse, err error) {
	status := l.GetRecordStatus(l.IsValid())
	start, end := l.GetRecordTime(status)

	resp, err = l.svcCtx.ReporterRPC.ModifySuiteRecord(
		l.ctx, &reporterpb.PutSuiteRecordRequest{
			TaskId:         l.taskId,
			ExecuteId:      l.executeId,
			ProjectId:      in.GetProjectId(),
			ExecuteType:    in.GetExecuteType().String(),
			GeneralConfig:  jsonx.MarshalToStringIgnoreError(Pb2ApiGeneralConfig(in.GetPrecisionSuite().GetGeneralConfig())),
			AccountConfig:  jsonx.MarshalToStringIgnoreError(Pb2ApiAccountConfig(in.GetPrecisionSuite().GetAccountConfig())),
			SuiteId:        in.GetPrecisionSuite().GetSuiteId(),
			SuiteName:      in.GetPrecisionSuite().GetSuiteName(),
			PlanExecuteId:  in.GetPrecisionSuite().GetPlanExecuteId(),
			SuiteExecuteId: l.executeId,
			Status:         status.String(),
			ExecutedBy:     in.GetUserId(),
			StartedAt:      start,
			EndedAt:        end,
		},
	)
	if err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.InternalError, err.Error()), "请求reporter rpc服务出现错误, error: %s", err,
		)
	}
	return
}

func (l *PrecisionSuitePublisher) getCases(in *pb.PublishReq) []*managerpb.ApiExecutionData {
	cases := make([]*managerpb.ApiExecutionData, 0, 1)
	if len(in.GetPrecisionSuite().GetCases()) > 0 {
		return in.GetPrecisionSuite().GetCases()
	}

	return cases
}

// IsValid 是否有效
func (l *PrecisionSuitePublisher) IsValid() bool {
	// 调试不受固有状态影响
	return true
}

// Panic 异常处理
func (l *PrecisionSuitePublisher) Panic(in *pb.PublishReq, err error) {
	if err == nil {
		return
	}

	_, _ = l.svcCtx.ReporterRPC.ModifySuiteRecord(
		l.ctx, &reporterpb.PutSuiteRecordRequest{
			TaskId:         l.taskId,
			ExecuteId:      l.executeId,
			ProjectId:      in.GetProjectId(),
			ExecuteType:    in.GetExecuteType().String(),
			SuiteId:        in.GetPrecisionSuite().GetSuiteId(),
			PlanExecuteId:  in.GetPrecisionSuite().GetPlanExecuteId(),
			SuiteExecuteId: l.executeId,
			Status:         pb.ComponentState_Panic.String(),
			ExecutedBy:     in.GetUserId(),
			EndedAt:        time.Now().UnixMilli(),
			Content:        jsonx.MarshalToStringIgnoreError(err.Error()),
		},
	)

	// 发起回调
	cb := &pb.CallbackReq{
		TriggerMode:  in.GetTriggerMode(),
		TriggerRule:  in.GetTriggerRule(),
		ProjectId:    in.GetProjectId(),
		TaskId:       l.taskId,
		ExecuteType:  in.GetExecuteType(),
		CallbackType: pb.CallbackType_CallbackType_API_SUITE,
		Data: &pb.CallbackReq_Suite{
			Suite: &pb.SuiteCallbackData{
				PlanId:         in.GetPrecisionSuite().GetPlanId(),
				PlanExecuteId:  in.GetPrecisionSuite().GetPlanExecuteId(),
				SuiteId:        in.GetPrecisionSuite().GetSuiteId(),
				SuiteExecuteId: l.executeId,
				SuiteState:     pb.ComponentState_Panic,
			},
		},
	}

	_ = callbackclient.CallBack(
		l.ctx,
		l.svcCtx.DispatcherProducer,
		l.svcCtx.Config.Name,
		l.svcCtx.Config.DispatcherProducer.Queue,
		l.getUUID(),
		cb,
	)
}
