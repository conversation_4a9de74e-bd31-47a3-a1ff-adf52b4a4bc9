package dispatcherlogic

import (
	"context"
	"testing"

	commonpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/utils"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/rpc/pb"
	managerpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

func TestPublishUICase(t *testing.T) {
	projectId := utils.GenProjectId()
	req := &pb.PublishReq{
		TriggerMode: commonpb.TriggerMode_MANUAL,
		ProjectId:   projectId,
		ExecuteType: managerpb.ApiExecutionDataType_UI_CASE,
		PublishType: pb.PublishType_PublishType_UI_CASE,
		Data: &pb.PublishReq_UiCase{
			UiCase: &pb.UICasePublishInfo{
				UiCaseId:         utils.GenCaseId(),
				UiCaseExecuteId:  utils.GenExecuteId(),
				UiSuiteId:        utils.GenSuiteId(),
				UiSuiteExecuteId: utils.GenExecuteId(),
				UiPlanId:         utils.GenPlanId(),
				UiPlanExecuteId:  utils.GenExecuteId(),
			},
		},
	}

	logic := NewPublishLogic(context.Background(), svc.Mock_ServiceContext())
	_, err := logic.Publish(req)
	if err != nil {
		t.Errorf("%s", err)
		t.FailNow()
	}
}
