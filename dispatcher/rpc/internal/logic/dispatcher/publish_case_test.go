package dispatcherlogic

import (
	"context"
	"testing"

	commonpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/utils"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/common/mock"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/rpc/pb"
	managerpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

func TestPublishCase(t *testing.T) {
	projectId := utils.GenProjectId()
	req := &pb.PublishReq{
		TriggerMode: commonpb.TriggerMode_MANUAL,
		ProjectId:   projectId,
		ExecuteType: managerpb.ApiExecutionDataType_API_CASE,
		PublishType: pb.PublishType_PublishType_API_CASE,
		Data: &pb.PublishReq_Case{
			Case: &pb.CasePublishInfo{
				CaseId:        utils.GenCaseId(),
				GeneralConfig: mock.Mock_GeneralConfig(projectId),
				AccountConfig: mock.Mock_AccountConfig(projectId),
				Version:       utils.GenVersion(),
			},
		},
	}

	logic := NewPublishLogic(context.Background(), svc.Mock_ServiceContext())
	_, err := logic.Publish(req)
	if err != nil {
		t.Errorf("%s", err)
		t.FailNow()
	}
}
