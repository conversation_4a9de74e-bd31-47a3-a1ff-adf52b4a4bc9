package dispatcherlogic

import (
	"context"
	"strconv"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/rpc/pb"
)

type SearchTaskInfoLogic struct {
	*BaseLogic
}

func NewSearchTaskInfoLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SearchTaskInfoLogic {
	return &SearchTaskInfoLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *SearchTaskInfoLogic) SearchTaskInfo(in *pb.SearchTaskInfoReq) (out *pb.SearchTaskInfoResp, err error) {
	out = &pb.SearchTaskInfoResp{}
	processor := l.svcCtx.TaskInfoProcessor
	taskPageType := pb.TaskPageType_PT_ALL
	if single := in.Condition.GetSingle(); single != nil {
		if other := single.GetOther(); other != nil {
			if single.GetField() == "page_type" {
				value := other.GetValue()
				i, err := strconv.ParseInt(value, 10, 32)
				if err != nil {
					return nil, err
				}
				taskPageType = pb.TaskPageType(i)
			}
		}
	}
	page, err := processor.ListTaskInfoForPage(
		l.ctx, in.ProjectId, int32(in.GetPagination().GetCurrentPage()), int32(in.GetPagination().GetPageSize()),
		taskPageType,
	)
	if err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()), "failed to find Task Info with project_id[%s], error: %+v",
			in.GetProjectId(), err,
		)
	}
	count, err := processor.GetTaskInfoCount(l.ctx, in.ProjectId, taskPageType)
	if err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()), "failed to find Task Info with project_id[%s], error: %+v",
			in.GetProjectId(), err,
		)
	}
	out.TotalCount = uint64(count)
	out.PageSize = in.GetPagination().GetPageSize()
	out.CurrentPage = in.GetPagination().GetCurrentPage()
	var pages uint64
	if uint64(count)%out.PageSize == 0 {
		pages = uint64(count) / out.PageSize
	} else {
		pages = uint64(count)/out.PageSize + 1
	}
	out.TotalPage = pages
	out.Items = make([]*pb.SearchTaskInfoItem, 0, len(page))
	for _, taskInfo := range page {
		item := &pb.SearchTaskInfoItem{}
		if err = utils.Copy(item, taskInfo, l.converters...); err != nil {
			return nil, errors.Wrapf(
				errorx.Err(errorx.CopyToStructFailure, err.Error()),
				"failed to copy taskInfo[%+v] to response, error: %+v", taskInfo, err,
			)
		}
		out.Items = append(out.Items, item)
	}

	return out, nil
}
