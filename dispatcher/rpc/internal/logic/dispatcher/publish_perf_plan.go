package dispatcherlogic

import (
	"context"
	"time"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/logx"
	"google.golang.org/protobuf/types/known/timestamppb"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworkerv2/base"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/mq"
	commonpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/rpc/pb"
	managerpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
	reporterpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
)

type PerfPlanPublisher struct {
	*BasePublisher

	apiExecutionData *managerpb.ApiExecutionData
}

func NewPerfPlanPublisher(ctx context.Context, svcCtx *svc.ServiceContext) *PerfPlanPublisher {
	return &PerfPlanPublisher{
		BasePublisher: NewBasePublisher(ctx, svcCtx),
	}
}

func (l *PerfPlanPublisher) CreateRecord(in *pb.PublishReq) (err error) {
	mgrData, err := l.handleMgrData(in)
	if err != nil {
		return errors.Wrapf(
			err, "请求handleMgrData rpc服务出现错误, error: %s", err,
		)
	}

	var (
		executionMode commonpb.PerfTaskExecutionMode

		startedAt = time.Now()
		status    = pb.ComponentState_Pending.String()
	)

	switch in.GetPerfPlan().GetPerfPlanInfo().GetExecuteType() {
	case commonpb.PerfTaskType_RUN:
		executionMode = commonpb.PerfTaskExecutionMode_BY_DURATION
	case commonpb.PerfTaskType_DEBUG:
		executionMode = commonpb.PerfTaskExecutionMode_BY_TIMES
	default:
		return errorx.Errorf(
			errorx.ValidateParamError, "invalid execute type: %s", in.GetPerfPlan().GetPerfPlanInfo().GetExecuteType(),
		)
	}

	if in.GetPerfPlan().GetPerfPlanInfo().GetEstimatedTime() != 0 {
		startedAt = time.Unix(in.GetPerfPlan().GetPerfPlanInfo().GetEstimatedTime(), 0)
		status = constants.PerfExecuteStatePreview.String()
	}

	l.apiExecutionData = mgrData
	l.Infof("mgrData: %s", protobuf.MarshalJSONIgnoreError(mgrData))

	_, err = l.svcCtx.PerfReporterRPC.CreatePerfPlanRecord(
		l.ctx, &reporterpb.CreatePerfPlanRecordReq{
			TaskId:    l.taskId,
			ExecuteId: l.executeId,

			ProjectId:      in.GetProjectId(),
			PlanId:         in.GetPerfPlan().GetPerfPlanId(),
			PlanName:       mgrData.GetPerfPlan().GetName(),
			TriggerMode:    in.GetTriggerMode(),
			TargetMaxRps:   mgrData.GetPerfPlan().GetMetaData().GetTargetMaxRps(),
			TargetDuration: mgrData.GetPerfPlan().GetMetaData().GetDuration(),
			Protocol:       mgrData.GetPerfPlan().GetMetaData().GetProtocol(),
			TargetEnv:      mgrData.GetPerfPlan().GetMetaData().GetTargetEnv(),

			Status:        status,
			TaskType:      in.GetPerfPlan().GetPerfPlanInfo().GetExecuteType(),
			ExecutionMode: executionMode,
			Services:      mgrData.GetPerfPlan().GetMetaData().GetServices(),

			ExecutedBy: in.GetUserId(),
			StartedAt:  timestamppb.New(startedAt),
		},
	)
	if err != nil {
		return errors.Wrapf(
			errorx.Err(errorx.InternalError, err.Error()), "请求reporter rpc服务出现错误, error: %s", err,
		)
	}
	return nil
}

func (l *PerfPlanPublisher) Publish(in *pb.PublishReq) (resp *pb.PublishResp, err error) {
	return l.publish(in)
}

func (l *PerfPlanPublisher) handleMgrData(in *pb.PublishReq) (*managerpb.ApiExecutionData, error) {
	mgrData, err := l.getApiExecutionData(
		&managerpb.GetApiExecutionDataReq{
			ProjectId: in.GetProjectId(),
			Type:      managerpb.ApiExecutionDataType_PERF_PLAN,
			Id:        in.GetPerfPlan().GetPerfPlanId(),
		},
	)
	if err != nil {
		return nil, err
	}

	if in.GetTriggerMode() == commonpb.TriggerMode_SCHEDULE {
		if mgrData.GetPerfPlan().GetMaintainedBy() != "" {
			in.UserId = mgrData.GetPerfPlan().GetMaintainedBy()
		} else {
			in.UserId = mgrData.GetPerfPlan().GetCreatedBy()
		}
	}

	return mgrData, nil
}

func (l *PerfPlanPublisher) publish(in *pb.PublishReq) (resp *pb.PublishResp, err error) {
	mgrData := l.apiExecutionData
	if mgrData == nil {
		mgrData, err = l.handleMgrData(in)
		if err != nil {
			return resp, err
		}
	}

	_, err = l.record(in, mgrData)
	if err != nil {
		return resp, err
	}

	if !l.IsValid(mgrData) || l.isStop {
		return &pb.PublishResp{TaskId: l.taskId, ExecuteId: l.executeId}, nil
	}

	// 创建任务请求
	taskData := l.getDistributeReq(in, mgrData)
	payload, err := protobuf.MarshalJSON(taskData)
	if err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.SerializationError, err.Error()), "压测任务参数序列化失败, error: %s", err,
		)
	}

	// 直接使用taskData中的优先级因为已经在上层判断设置
	task := base.NewTask(
		constants.MQTaskTypeDispatcherPerfPlan, payload,
		base.WithMaxRetryOptions(0),
		base.WithRetentionOptions(time.Second*300),
	)

	_, err = l.svcCtx.DispatcherProducer.Send(
		l.ctx, task, mq.ConvertPbEnumerationToQueuePriority(taskData.PriorityType),
	)
	if err != nil {
		return nil, errors.Wrapf(errorx.Err(errorx.InternalError, err.Error()), "发送数据到mq失败, error: %s", err)
	}

	return &pb.PublishResp{
		TaskId:    l.taskId,
		ExecuteId: l.executeId,
		Version:   "",
	}, nil
}

func (l *PerfPlanPublisher) getDistributeReq(in *pb.PublishReq, data *managerpb.ApiExecutionData) *pb.DistributeReq {
	req := &pb.DistributeReq{
		TriggerMode:    in.GetTriggerMode(),
		TriggerRule:    in.GetTriggerRule(),
		ProjectId:      in.GetProjectId(),
		TaskId:         l.taskId,
		ExecuteType:    in.GetExecuteType(),
		DistributeType: pb.DistributeType_DistributeType_PERF_PLAN,
		GeneralConfig:  data.GetPlan().GetGeneralConfig(),
		AccountConfig:  data.GetPlan().GetAccountConfigs(),
		UserId:         in.GetUserId(),
		Debug:          in.GetDebug(),
		Data: &pb.DistributeReq_PerfPlan{
			PerfPlan: &pb.PerfPlanDistributeData{
				PerfPlanId:        in.GetPerfPlan().GetPerfPlanId(),
				PerfPlanExecuteId: l.executeId,
				State:             pb.ComponentState_Pending,
				PerfPlan:          data.GetPerfPlan(),
				PerfSuites:        l.getPerfSuites(data),
				PerfPlanInfo:      in.GetPerfPlan().GetPerfPlanInfo(),
			},
		},
	}

	return req
}

func (l *PerfPlanPublisher) record(
	in *pb.PublishReq, data *managerpb.ApiExecutionData,
) (resp *reporterpb.ModifyPerfPlanRecordResp, err error) {
	status := l.GetRecordStatus(l.IsValid(data))

	resp, err = l.svcCtx.PerfReporterRPC.ModifyPerfPlanRecord(
		l.ctx, &reporterpb.ModifyPerfPlanRecordReq{
			ProjectId: in.GetProjectId(),
			PlanId:    in.GetPerfPlan().GetPerfPlanId(),
			TaskId:    l.taskId,
			ExecuteId: l.executeId,
			Status:    status.String(),
		},
	)
	if err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.InternalError, err.Error()), "请求reporter rpc服务出现错误, error: %s", err,
		)
	}

	return
}

func (l *PerfPlanPublisher) getPerfSuites(data *managerpb.ApiExecutionData) []*managerpb.ApiExecutionData {
	if len(data.GetChildren()) == 0 {
		return make([]*managerpb.ApiExecutionData, 0)
	}

	return data.GetChildren()[0].GetChild()
}

// IsValid 是否有效
func (l *PerfPlanPublisher) IsValid(data *managerpb.ApiExecutionData) bool {
	return data.GetUiPlan().GetState() != managerpb.CommonState_CS_DISABLE
}

// Panic 异常处理
func (l *PerfPlanPublisher) Panic(in *pb.PublishReq, err error) {
	if err == nil {
		return
	}
	_, _ = l.svcCtx.PerfReporterRPC.ModifyPerfPlanRecord(
		l.ctx, &reporterpb.ModifyPerfPlanRecordReq{
			TaskId:    l.taskId,
			ProjectId: in.GetProjectId(),
			PlanId:    in.GetPerfPlan().GetPerfPlanId(),
			ExecuteId: l.executeId,
			Status:    pb.ComponentState_Panic.String(),
			EndedAt:   timestamppb.New(time.Now()),
		},
	)
	logx.WithContext(l.ctx).Infof(
		"PerfPlanPublisher Panic[%s] : taskId:%s TaskExecutedResult", commonpb.ExecutedResult_TER_PANIC, l.taskId,
	)
	_ = l.svcCtx.TaskInfoProcessor.UpdateTaskExecutedResult(
		context.Background(), in.GetProjectId(), in.GetTaskId(), commonpb.ExecutedResult_TER_PANIC,
		time.Now().UnixMilli(),
	)
}
