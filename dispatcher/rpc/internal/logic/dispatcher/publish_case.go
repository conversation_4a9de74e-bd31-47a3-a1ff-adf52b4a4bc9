package dispatcherlogic

import (
	"context"
	"time"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworkerv2/base"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/common/callbackclient"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/rpc/pb"
	managerpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
	reporterpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
)

type CasePublisher struct {
	*BasePublisher
}

func NewCasePublisher(ctx context.Context, svcCtx *svc.ServiceContext) *CasePublisher {
	return &CasePublisher{
		BasePublisher: NewBasePublisher(ctx, svcCtx),
	}
}

func (l *CasePublisher) CreateRecord(in *pb.PublishReq) (err error) {
	_, err = l.svcCtx.ReporterRPC.Create(
		l.ctx, &reporterpb.PutRecordRequest{
			TaskId:                   l.taskId,
			ProjectId:                in.GetProjectId(),
			ExecuteId:                l.executeId,
			ExecuteType:              in.GetExecuteType().String(),
			GeneralConfig:            jsonx.MarshalToStringIgnoreError(Pb2ApiGeneralConfig(in.GetCase().GetGeneralConfig())),
			AccountConfig:            jsonx.MarshalToStringIgnoreError(Pb2ApiAccountConfig(in.GetCase().GetAccountConfig())),
			ComponentId:              in.GetCase().GetCaseId(),
			ComponentType:            managerpb.ApiExecutionDataType_API_CASE.String(),
			ComponentExecuteId:       l.executeId,
			ParentComponentId:        in.GetCase().GetSuiteId(),
			ParentComponentExecuteId: in.GetCase().GetSuiteExecuteId(),
			Version:                  in.GetCase().GetVersion(),
			Times:                    1,
			ExecutedBy:               in.GetUserId(),
			StartedAt:                time.Now().UnixMilli(),
			IsRoot:                   1,
		},
	)
	if err != nil {
		return errors.Wrapf(
			errorx.Err(errorx.InternalError, err.Error()), "请求reporter rpc服务出现错误, error: %s", err,
		)
	}

	return nil
}

func (l *CasePublisher) Publish(in *pb.PublishReq) (resp *pb.PublishResp, err error) {
	return l.publish(in)
}

func (l *CasePublisher) publish(in *pb.PublishReq) (resp *pb.PublishResp, err error) {
	apiExecutionData, err := l.getApiExecutionData(
		&managerpb.GetApiExecutionDataReq{
			ProjectId: in.GetProjectId(),
			Type:      managerpb.ApiExecutionDataType_API_CASE,
			Id:        in.GetCase().GetCaseId(),
			Version:   in.GetCase().GetVersion(),
		},
	)
	if err != nil {
		return resp, err
	}

	_, err = l.record(in, apiExecutionData)
	if err != nil {
		return resp, err
	}

	if !l.IsValid(apiExecutionData) || l.isStop {
		return &pb.PublishResp{TaskId: l.taskId, ExecuteId: l.executeId}, nil
	}

	taskData := l.getWorkerReq(in, apiExecutionData)
	payload, err := protobuf.MarshalJSON(taskData)
	if err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.SerializationError, err.Error()), "api任务参数序列化失败, error: %s", err,
		)
	}

	task := base.NewTask(
		constants.MQTaskTypeAPIWorkerCase,
		payload,
		base.WithRetentionOptions(time.Minute*2),
		base.WithMaxRetryOptions(0),
	)
	if in.GetCase().GetSuiteId() != "" {
		task.SetCallback(constants.MQTaskTypeDispatcherCallback, l.svcCtx.Config.DispatcherProducer.Queue)
	}
	_, err = l.svcCtx.ApiWorkerProducer.Send(
		l.ctx, task, base.QueuePriorityDefault,
	)
	if err != nil {
		return nil, errors.Wrapf(errorx.Err(errorx.InternalError, err.Error()), "发送数据到mq失败, error: %s", err)
	}

	return &pb.PublishResp{
		TaskId:    l.taskId,
		ExecuteId: l.executeId,
		Version:   l.getVersion(apiExecutionData),
	}, nil
}

func (l *CasePublisher) getWorkerReq(in *pb.PublishReq, data *managerpb.ApiExecutionData) *pb.WorkerReq {
	case_ := in.GetCase()

	return &pb.WorkerReq{
		TriggerMode:   in.GetTriggerMode(),
		TriggerRule:   in.GetTriggerRule(),
		ProjectId:     in.GetProjectId(),
		TaskId:        l.taskId,
		ExecuteId:     l.executeId,
		ExecuteType:   in.GetExecuteType(),
		WorkerType:    pb.WorkerType_WorkerType_API_CASE,
		GeneralConfig: case_.GetGeneralConfig(),
		AccountConfig: case_.GetAccountConfig(),
		UserId:        in.GetUserId(),
		NodeData:      data,
		Debug:         in.GetDebug(),
		PurposeType:   in.GetPurposeType(),
		Data: &pb.WorkerReq_Case{
			Case: &pb.CaseWorkerInfo{
				CaseId:         case_.GetCaseId(),
				CaseExecuteId:  l.executeId,
				SuiteId:        case_.GetSuiteId(),
				SuiteExecuteId: case_.GetSuiteExecuteId(),
				Version:        l.getVersion(data),
				SuiteName:      case_.GetSuiteName(),
				PlanId:         case_.GetPlanId(),
				PlanExecuteId:  case_.GetPlanExecuteId(),
				PlanName:       case_.GetPlanName(),
			},
		},
	}
}

func (l *CasePublisher) record(
	in *pb.PublishReq, data *managerpb.ApiExecutionData,
) (resp *reporterpb.ModifyRecordResponse, err error) {
	status := l.GetRecordStatus(l.IsValid(data))
	start, end := l.GetRecordTime(status)

	resp, err = l.svcCtx.ReporterRPC.ModifyCaseRecord(
		l.ctx, &reporterpb.PutRecordRequest{
			TaskId:                   l.taskId,
			ProjectId:                in.GetProjectId(),
			ExecuteId:                l.executeId,
			ExecuteType:              in.GetExecuteType().String(),
			GeneralConfig:            jsonx.MarshalToStringIgnoreError(Pb2ApiGeneralConfig(in.GetCase().GetGeneralConfig())),
			AccountConfig:            jsonx.MarshalToStringIgnoreError(Pb2ApiAccountConfig(in.GetCase().GetAccountConfig())),
			ComponentId:              data.GetId(),
			ComponentType:            data.GetType().String(),
			ComponentName:            l.getComponentName(data),
			ComponentExecuteId:       l.executeId,
			ParentComponentId:        in.GetCase().GetSuiteId(),
			ParentComponentExecuteId: in.GetCase().GetSuiteExecuteId(),
			Version:                  l.getVersion(data),
			Times:                    1,
			Status:                   status.String(),
			ExecutedBy:               in.GetUserId(),
			MaintainedBy:             l.getMaintainer(data),
			StartedAt:                start,
			EndedAt:                  end,
			IsRoot:                   1,
		},
	)
	if err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.InternalError, err.Error()), "请求reporter rpc服务出现错误, error: %s", err,
		)
	}

	return resp, nil
}

// IsValid 是否有效
func (l *CasePublisher) IsValid(data *managerpb.ApiExecutionData) bool {
	// 调试不受固有状态影响
	return true
}

// Panic 异常处理
func (l *CasePublisher) Panic(in *pb.PublishReq, err error) {
	if err == nil {
		return
	}

	// 直接更新状态
	_, _ = l.svcCtx.ReporterRPC.ModifyCaseRecord(
		l.ctx, &reporterpb.PutRecordRequest{
			TaskId:             l.taskId,
			ProjectId:          in.GetProjectId(),
			ExecuteId:          l.executeId,
			ExecuteType:        in.GetExecuteType().String(),
			ComponentExecuteId: l.executeId,
			Times:              1,
			Status:             pb.ComponentState_Panic.String(),
			Content:            jsonx.MarshalToStringIgnoreError(err.Error()),
			ExecutedBy:         in.GetUserId(),
			IsRoot:             1,
			ComponentId:        in.GetCase().GetCaseId(),
			ComponentType:      managerpb.ApiExecutionDataType_API_CASE.String(),
			EndedAt:            time.Now().UnixMilli(),
		},
	)

	if in.GetExecuteType() == managerpb.ApiExecutionDataType_API_CASE {
		return
	}

	// 发起回调
	cb := &pb.CallbackReq{
		TriggerMode:  in.GetTriggerMode(),
		TriggerRule:  in.GetTriggerRule(),
		ProjectId:    in.GetProjectId(),
		TaskId:       l.taskId,
		ExecuteType:  in.GetExecuteType(),
		CallbackType: pb.CallbackType_CallbackType_API_CASE,
		PurposeType:  in.GetPurposeType(),
		Data: &pb.CallbackReq_Case{
			Case: &pb.CaseCallbackData{
				SuiteId:        in.GetCase().GetSuiteId(),
				SuiteExecuteId: in.GetCase().GetSuiteExecuteId(),
				CaseId:         in.GetCase().GetCaseId(),
				CaseExecuteId:  l.executeId,
				CaseState:      pb.ComponentState_Panic,
				Version:        in.GetCase().GetVersion(),
			},
		},
	}

	_ = callbackclient.CallBack(
		l.ctx,
		l.svcCtx.DispatcherProducer,
		l.svcCtx.Config.Name,
		l.svcCtx.Config.DispatcherProducer.Queue,
		l.getUUID(),
		cb,
	)
}
