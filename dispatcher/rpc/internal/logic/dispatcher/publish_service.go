package dispatcherlogic

import (
	"context"
	"time"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworkerv2/base"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/common/callbackclient"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/rpc/pb"
	managerpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
	reporterpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
)

type ServicePublisher struct {
	*BasePublisher
}

func NewServicePublisher(ctx context.Context, svcCtx *svc.ServiceContext) *ServicePublisher {
	return &ServicePublisher{
		BasePublisher: NewBasePublisher(ctx, svcCtx),
	}
}

func (l *ServicePublisher) CreateRecord(in *pb.PublishReq) (err error) {
	_, err = l.svcCtx.ReporterRPC.CreateServiceRecord(
		l.ctx, &reporterpb.PutServiceRecordRequest{
			TaskId:           l.taskId,
			ExecuteId:        l.executeId,
			ProjectId:        in.GetProjectId(),
			ExecuteType:      in.GetExecuteType().String(),
			GeneralConfig:    jsonx.MarshalToStringIgnoreError(Pb2ApiGeneralConfig(in.GetService().GetGeneralConfig())),
			AccountConfig:    jsonx.MarshalToStringIgnoreError(Pb2ApiAccountConfig(in.GetService().GetAccountConfig())),
			ServiceId:        in.GetService().GetServiceId(),
			ServiceName:      in.GetService().GetServiceName(),
			PlanExecuteId:    in.GetService().GetPlanExecuteId(),
			ServiceExecuteId: l.executeId,
			ExecutedBy:       in.GetUserId(),
			StartedAt:        time.Now().UnixMilli(),
		},
	)
	if err != nil {
		return errors.Wrapf(
			errorx.Err(errorx.InternalError, err.Error()), "请求reporter rpc服务出现错误, error: %s", err,
		)
	}
	return nil
}

func (l *ServicePublisher) Publish(in *pb.PublishReq) (resp *pb.PublishResp, err error) {
	return l.publish(in)
}

func (l *ServicePublisher) publish(in *pb.PublishReq) (resp *pb.PublishResp, err error) {
	_, err = l.record(in)
	if err != nil {
		return resp, err
	}

	if !l.IsValid() || l.isStop {
		return &pb.PublishResp{TaskId: l.taskId, ExecuteId: l.executeId}, nil
	}

	// 创建任务请求
	taskData := l.getDistributeReq(in)
	payload, err := protobuf.MarshalJSON(taskData)
	if err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.SerializationError, err.Error()), "api任务参数序列化失败, error: %s", err,
		)
	}

	task := base.NewTask(
		constants.MQTaskTypeDispatcherService,
		payload,
		base.WithRetentionOptions(time.Minute*2),
		base.WithMaxRetryOptions(0),
	)
	_, err = l.svcCtx.DispatcherProducer.Send(
		l.ctx, task, base.QueuePriorityDefault,
	)
	if err != nil {
		return nil, errors.Wrapf(errorx.Err(errorx.InternalError, err.Error()), "发送数据到mq失败, error: %s", err)
	}

	return &pb.PublishResp{
		TaskId:    l.taskId,
		ExecuteId: l.executeId,
		Version:   "",
	}, nil
}

func (l *ServicePublisher) getDistributeReq(in *pb.PublishReq) *pb.DistributeReq {
	service := in.GetService()

	return &pb.DistributeReq{
		TriggerMode:    in.GetTriggerMode(),
		TriggerRule:    in.GetTriggerRule(),
		ProjectId:      in.GetProjectId(),
		TaskId:         l.taskId,
		ExecuteType:    in.GetExecuteType(),
		DistributeType: pb.DistributeType_DistributeType_API_SERVICE,
		GeneralConfig:  service.GetGeneralConfig(),
		AccountConfig:  service.GetAccountConfig(),
		UserId:         in.GetUserId(),
		Debug:          in.GetDebug(),
		PurposeType:    in.GetPurposeType(),
		Data: &pb.DistributeReq_Service{
			Service: &pb.ServiceDistributeData{
				ServiceId:        service.GetServiceId(),
				ServiceExecuteId: l.executeId,
				ServiceName:      service.GetServiceName(),
				PlanId:           service.GetPlanId(),
				PlanExecuteId:    service.GetPlanExecuteId(),
				Service: &managerpb.ServiceComponent{
					ProjectId:   in.GetProjectId(),
					ServiceName: service.GetServiceName(),
				},
				Cases:          service.GetCases(),
				InterfaceCases: service.GetInterfaceCases(),
				PlanName:       service.GetPlanName(),
			},
		},
	}
}

func (l *ServicePublisher) record(in *pb.PublishReq) (resp *reporterpb.ModifyServiceRecordResponse, err error) {
	status := l.GetRecordStatus(l.IsValid())
	start, end := l.GetRecordTime(status)

	resp, err = l.svcCtx.ReporterRPC.ModifyServiceRecord(
		l.ctx, &reporterpb.PutServiceRecordRequest{
			TaskId:           l.taskId,
			ExecuteId:        l.executeId,
			ProjectId:        in.GetProjectId(),
			ExecuteType:      in.GetExecuteType().String(),
			GeneralConfig:    jsonx.MarshalToStringIgnoreError(Pb2ApiGeneralConfig(in.GetService().GetGeneralConfig())),
			AccountConfig:    jsonx.MarshalToStringIgnoreError(Pb2ApiAccountConfig(in.GetService().GetAccountConfig())),
			ServiceId:        in.GetService().GetServiceId(),
			ServiceName:      in.GetService().GetServiceName(),
			PlanExecuteId:    in.GetService().GetPlanExecuteId(),
			ServiceExecuteId: l.executeId,
			Status:           status.String(),
			ExecutedBy:       in.GetUserId(),
			StartedAt:        start,
			EndedAt:          end,
		},
	)
	if err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.InternalError, err.Error()), "请求reporter rpc服务出现错误, error: %s", err,
		)
	}
	return
}

// IsValid 是否有效
func (l *ServicePublisher) IsValid() bool {
	// 调试不受固有状态影响
	return true
}

// Panic 异常处理
func (l *ServicePublisher) Panic(in *pb.PublishReq, err error) {
	if err == nil {
		return
	}

	_, _ = l.svcCtx.ReporterRPC.ModifyServiceRecord(
		l.ctx, &reporterpb.PutServiceRecordRequest{
			TaskId:           l.taskId,
			ExecuteId:        l.executeId,
			ProjectId:        in.GetProjectId(),
			ExecuteType:      in.GetExecuteType().String(),
			ServiceId:        in.GetService().GetServiceId(),
			PlanExecuteId:    in.GetService().GetPlanExecuteId(),
			ServiceExecuteId: l.executeId,
			Status:           pb.ComponentState_Panic.String(),
			ExecutedBy:       in.GetUserId(),
			EndedAt:          time.Now().UnixMilli(),
			Content:          jsonx.MarshalToStringIgnoreError(err.Error()),
		},
	)

	// 发起回调
	cb := &pb.CallbackReq{
		TriggerMode:  in.GetTriggerMode(),
		TriggerRule:  in.GetTriggerRule(),
		ProjectId:    in.GetProjectId(),
		TaskId:       l.taskId,
		ExecuteType:  in.GetExecuteType(),
		CallbackType: pb.CallbackType_CallbackType_API_SERVICE,
		PurposeType:  in.GetPurposeType(),
		Data: &pb.CallbackReq_Service{
			Service: &pb.ServiceCallbackData{
				PlanId:           in.GetService().GetPlanId(),
				PlanExecuteId:    in.GetService().GetPlanExecuteId(),
				ServiceId:        in.GetService().GetServiceId(),
				ServiceExecuteId: l.executeId,
				ServiceState:     pb.ComponentState_Panic,
			},
		},
	}

	_ = callbackclient.CallBack(
		l.ctx,
		l.svcCtx.DispatcherProducer,
		l.svcCtx.Config.Name,
		l.svcCtx.Config.DispatcherProducer.Queue,
		l.getUUID(),
		cb,
	)
}
