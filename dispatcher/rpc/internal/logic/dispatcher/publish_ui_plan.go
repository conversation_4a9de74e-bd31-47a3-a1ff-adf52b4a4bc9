package dispatcherlogic

import (
	"context"
	"fmt"
	"strconv"
	"strings"
	"time"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"
	"github.com/zeromicro/go-zero/core/logx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworkerv2/base"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sqlbuilder/rpc"
	userpb "gitlab.ttyuyin.com/TestDevelopment/qet-backend-middleware/user/rpc/pb"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/mq"
	commonpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/redis/task"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/rpc/pb"
	managerpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
	reporterpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
)

type UIPlanPublisher struct {
	*BasePublisher

	apiExecutionData *managerpb.ApiExecutionData
}

func NewUIPlanPublisher(ctx context.Context, svcCtx *svc.ServiceContext) *UIPlanPublisher {
	return &UIPlanPublisher{
		BasePublisher: NewBasePublisher(ctx, svcCtx),
	}
}

func (l *UIPlanPublisher) CreateRecord(in *pb.PublishReq) (err error) {
	mgrData, err := l.handleMgrData(in)
	if err != nil {
		return errors.Wrapf(
			err, "请求handleMgrData rpc服务出现错误, error: %s", err,
		)
	}
	l.apiExecutionData = mgrData
	_, err = l.svcCtx.UIReporterRPC.CreateUIPlanRecord(
		l.ctx, &reporterpb.PutUIPlanRecordRequest{
			ProjectId:  in.GetProjectId(),
			PlanId:     in.GetUiPlan().GetUiPlanId(),
			TaskId:     l.taskId,
			ExecuteId:  l.executeId,
			ExecutedBy: in.GetUserId(),
			// StartedAt:   time.Now().UnixMilli(),
			TriggerMode:  in.GetTriggerMode().String(),
			PriorityType: mgrData.GetUiPlan().GetMetaData().GetPriorityType(),
		},
	)
	if err != nil {
		return errors.Wrapf(
			errorx.Err(errorx.InternalError, err.Error()), "请求reporter rpc服务出现错误, error: %s", err,
		)
	}
	return nil
}

func (l *UIPlanPublisher) Publish(in *pb.PublishReq) (resp *pb.PublishResp, err error) {
	return l.publish(in)
}

func (l *UIPlanPublisher) handleMgrData(in *pb.PublishReq) (*managerpb.ApiExecutionData, error) {
	mgrData, err := l.getApiExecutionData(
		&managerpb.GetApiExecutionDataReq{
			ProjectId: in.GetProjectId(),
			Type:      managerpb.ApiExecutionDataType_UI_PLAN,
			Id:        in.GetUiPlan().GetUiPlanId(),
			Extra: &managerpb.GetApiExecutionDataReq_UiPlanExtraData{
				UiPlanExtraData: &managerpb.UiPlanExtraData{
					Devices:  in.GetUiPlan().GetUiPlanInfo().GetDevices(),
					Together: in.GetUiPlan().GetUiPlanInfo().GetTogether(),
				},
			},
		},
	)
	if err != nil {
		return nil, err
	}

	metaData := mgrData.GetUiPlan().GetMetaData()
	// 当优先级存在数据优先使用传入的优先级[定时和手动调用保持一致]
	if l.priorityType != commonpb.PriorityType_Default {
		metaData.PriorityType = l.priorityType
	}
	// 修改运行app地址与版本
	appDownloadUrl := in.GetUiPlan().GetUiPlanInfo().GetAppDownloadUrl()
	if appDownloadUrl != "" {
		metaData.AppDownloadLink = appDownloadUrl
	}
	appVersion := in.GetUiPlan().GetUiPlanInfo().GetAppVersion()
	if appVersion != "" {
		metaData.AppVersion = appVersion
	}
	// 修改回调地址
	callbackUrl := in.GetUiPlan().GetCallbackUrl()
	if callbackUrl != "" {
		metaData.CallbackUrl = callbackUrl
	}

	// 修改执行人
	switch in.GetTriggerMode() {
	case commonpb.TriggerMode_INTERFACE:
		triggerAccount := in.GetTriggerUser().GetAccount()
		triggerEmail := in.GetTriggerUser().GetEmail()
		if triggerAccount != "" {
			in.UserId = triggerAccount
		} else if triggerEmail != "" {
			condition := rpc.Condition{
				Single: &rpc.SingleCondition{
					Field:   "email",
					Compare: "EQ",
					Other: &rpc.Other{
						Value: triggerEmail,
					},
				},
			}
			resp, err := l.svcCtx.UserRPC.UserList(l.ctx, &userpb.UserListReq{Condition: &condition})
			if err != nil {
				return nil, err
			}
			if len(resp.Items) == 0 {
				return nil, errors.New(fmt.Sprintf("执行人邮箱:「%s」找不到对应的工号", triggerEmail))
			}
			item := resp.Items[0]
			in.UserId = item.GetAccount()
		} else {
			if mgrData.GetUiPlan().GetMaintainedBy() != "" {
				in.UserId = mgrData.GetUiPlan().GetMaintainedBy()
			} else {
				in.UserId = mgrData.GetUiPlan().GetCreatedBy()
			}
		}
	case commonpb.TriggerMode_SCHEDULE:
		if mgrData.GetUiPlan().GetMaintainedBy() != "" {
			in.UserId = mgrData.GetUiPlan().GetMaintainedBy()
		} else {
			in.UserId = mgrData.GetUiPlan().GetCreatedBy()
		}
	}

	argsMap := map[string]string{
		constants.TestArgProjectId:    in.GetProjectId(),
		constants.TestArgPlanId:       in.GetUiPlan().GetUiPlanId(),
		constants.TestArgName:         mgrData.GetUiPlan().GetName(),
		constants.TestArgPlatformType: metaData.GetPlatformType().String(),
		constants.TestArgPackageName:  metaData.GetPackageName(),
		// constants.TestArgAppDownloadLink:      metaData.GetAppDownloadLink(),
		// constants.TestArgAppVersion:           metaData.GetAppVersion(),
		constants.TestArgAppName:              metaData.GetAppName(),
		constants.TestArgExecutionEnvironment: metaData.GetExecutionEnvironment(),
		constants.TestArgFailRetry:            strconv.Itoa(int(metaData.GetFailRetry().Number())),
	}
	for i := range metaData.TestArgs {
		for argName, argValue := range argsMap {
			metaData.TestArgs[i] = strings.Replace(metaData.TestArgs[i], argName, argValue, -1)
		}
	}

	return mgrData, nil
}

func (l *UIPlanPublisher) publish(in *pb.PublishReq) (resp *pb.PublishResp, err error) {
	mgrData := l.apiExecutionData
	if mgrData == nil {
		mgrData, err = l.handleMgrData(in)
		if err != nil {
			return resp, err
		}
	}
	_, err = l.record(in, mgrData)
	if err != nil {
		return resp, err
	}

	if !l.IsValid(mgrData) || l.isStop {
		return &pb.PublishResp{TaskId: l.taskId, ExecuteId: l.executeId}, nil
	}

	// 创建任务请求
	taskData := l.getDistributeReq(in, mgrData)
	payload, err := protobuf.MarshalJSON(taskData)
	if err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.SerializationError, err.Error()), "ui任务参数序列化失败, error: %s", err,
		)
	}

	uiSuites := taskData.GetUiPlan().GetUiSuites()
	// 创建任务数据
	err = l.svcCtx.TaskInfoProcessor.CreateTaskInfo(
		l.ctx, &task.InfoItem{
			ProjectId:    taskData.GetProjectId(),
			ExecuteId:    l.executeId,
			TaskId:       taskData.GetTaskId(),
			PlanId:       taskData.GetUiPlan().GetUiPlanId(),
			PlanName:     taskData.GetUiPlan().GetUiPlan().GetName(),
			TriggerMode:  taskData.GetTriggerMode().String(),
			ExecuteBy:    taskData.GetUserId(),
			CreateTime:   time.Now().UnixMilli(),
			PriorityType: int8(taskData.GetPriorityType()),
			TotalCase:    0,
			TotalSuite:   int64(len(uiSuites)),
			// StartedAt:    time.Now().UnixMilli(),
			PlanMetaData: protobuf.MarshalJSONToStringIgnoreError(mgrData.GetUiPlan()),
		},
	)
	if err != nil {
		return nil, errors.Wrapf(errorx.Err(errorx.InternalError, err.Error()), "CreateTaskInfo失败, error: %s", err)
	}

	// 直接使用taskData中的优先级因为已经在上层判断设置
	newTask := base.NewTask(
		constants.MQTaskTypeDispatcherUIPlan, payload,
		base.WithMaxRetryOptions(0),
		base.WithRetentionOptions(time.Second*300),
	)
	// newTask.SetCallback("callback", "topic_simple_test")
	_, err = l.svcCtx.DispatcherProducer.Send(
		l.ctx, newTask, mq.ConvertPbEnumerationToQueuePriority(taskData.PriorityType),
	)
	if err != nil {
		return nil, errors.Wrapf(errorx.Err(errorx.InternalError, err.Error()), "发送数据到mq失败, error: %s", err)
	}

	return &pb.PublishResp{
		TaskId:    l.taskId,
		ExecuteId: l.executeId,
		Version:   "",
	}, nil
}

func (l *UIPlanPublisher) getDistributeReq(in *pb.PublishReq, data *managerpb.ApiExecutionData) *pb.DistributeReq {
	suites := l.getSuite(data)
	// 直接使用mata中的优先级因为已经在上层判断设置
	return &pb.DistributeReq{
		TriggerMode:    in.GetTriggerMode(),
		TriggerRule:    in.GetTriggerRule(),
		ProjectId:      in.GetProjectId(),
		TaskId:         l.taskId,
		ExecuteType:    in.GetExecuteType(),
		DistributeType: pb.DistributeType_DistributeType_UI_PLAN,
		GeneralConfig:  data.GetPlan().GetGeneralConfig(),
		AccountConfig:  data.GetPlan().GetAccountConfigs(),
		UserId:         in.GetUserId(),
		PriorityType:   data.GetUiPlan().GetMetaData().GetPriorityType(),
		Debug:          in.GetDebug(),
		Data: &pb.DistributeReq_UiPlan{
			UiPlan: &pb.UIPlanDistributeData{
				UiPlanId:        in.GetUiPlan().GetUiPlanId(),
				UiPlanExecuteId: l.executeId,
				State:           pb.ComponentState_Pending,
				UiPlan:          data.GetUiPlan(),
				UiSuites:        suites,
				MetaData:        data.GetUiPlan().GetMetaData(),
			},
		},
	}
}

func (l *UIPlanPublisher) record(
	in *pb.PublishReq, data *managerpb.ApiExecutionData,
) (resp *reporterpb.ModifyUIPlanRecordResponse, err error) {
	status := l.GetRecordStatus(l.IsValid(data))
	// 不算开始时间 进行中和开始时间由用例真正执行来更新 结束为最后一个用例结束
	_, end := l.GetRecordTime(status)

	resp, err = l.svcCtx.UIReporterRPC.ModifyUIPlanRecord(
		l.ctx, &reporterpb.PutUIPlanRecordRequest{
			ProjectId:  in.GetProjectId(),
			PlanId:     in.GetUiPlan().GetUiPlanId(),
			PlanName:   data.GetUiPlan().GetName(),
			TaskId:     l.taskId,
			ExecuteId:  l.executeId,
			Status:     status.String(),
			ExecutedBy: in.GetUserId(),
			// StartedAt:   start,
			EndedAt:     end, // 保留这里记录end 因为 有可能这个流程出现暂停
			ExecuteData: protobuf.MarshalJSONToStringIgnoreError(data.GetUiPlan()),
		},
	)
	if err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.InternalError, err.Error()), "请求reporter rpc服务出现错误, error: %s", err,
		)
	}

	return
}

func (l *UIPlanPublisher) getSuite(data *managerpb.ApiExecutionData) (suites []*managerpb.ApiExecutionData) {
	suites = make([]*managerpb.ApiExecutionData, 0, 1)
	if len(data.GetChildren()) == 0 {
		return
	}

	suites = data.GetChildren()[0].GetChild()
	return suites
}

// IsValid 是否有效
func (l *UIPlanPublisher) IsValid(data *managerpb.ApiExecutionData) bool {
	return data.GetUiPlan().GetState() != managerpb.CommonState_CS_DISABLE
}

// Panic 异常处理
func (l *UIPlanPublisher) Panic(in *pb.PublishReq, err error) {
	if err == nil {
		return
	}

	// 直接更新状态
	_, _ = l.svcCtx.UIReporterRPC.ModifyUIPlanRecord(
		l.ctx, &reporterpb.PutUIPlanRecordRequest{
			TaskId:     l.taskId,
			ProjectId:  in.GetProjectId(),
			PlanId:     in.GetUiPlan().GetUiPlanId(),
			ExecuteId:  l.executeId,
			Status:     pb.ComponentState_Panic.String(),
			ExecutedBy: in.GetUserId(),
			Content:    jsonx.MarshalToStringIgnoreError(err.Error()),
			EndedAt:    time.Now().UnixMilli(),
		},
	)
	logx.WithContext(l.ctx).Infof(
		"UIPlanPublisher Panic[%s] : taskId:%s TaskExecutedResult", commonpb.ExecutedResult_TER_PANIC, l.taskId,
	)
	_ = l.svcCtx.TaskInfoProcessor.UpdateTaskExecutedResult(
		context.Background(), in.GetProjectId(), in.GetTaskId(), commonpb.ExecutedResult_TER_PANIC,
		time.Now().UnixMilli(),
	)
}
