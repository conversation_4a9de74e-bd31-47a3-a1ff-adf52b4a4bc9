package dispatcherlogic

import (
	"context"
	"strings"
	"time"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/logx"
	"google.golang.org/protobuf/types/known/timestamppb"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworkerv2/base"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	commonpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/common/utils"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/rpc/pb"
	managerpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
	reporterpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
)

type StopLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewStopLogic(ctx context.Context, svcCtx *svc.ServiceContext) *StopLogic {
	return &StopLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

func (l *StopLogic) Stop(in *pb.StopReq) (resp *pb.StopResp, err error) {
	switch in.GetExecuteType() {
	case managerpb.ApiExecutionDataType_UI_PLAN:
		err = l.stopTaskOfUIPlan(in)
	case managerpb.ApiExecutionDataType_PERF_PLAN:
		err = l.stopTaskOfPerfPlan(in)
	case managerpb.ApiExecutionDataType_STABILITY_PLAN:
		err = l.stopTaskOfStabilityPlan(in)
	case managerpb.ApiExecutionDataType_UI_AGENT_COMPONENT:
		err = l.stopTaskOfUIAgentComponent(in)
	default:
		err = errorx.Errorf(
			errorx.ValidateParamError,
			"unsupport execute type, project_id: %s, plan_id: %s, task_id: %s, execute_id: %s, execute_type: %s",
			in.GetProjectId(), in.GetId(), in.GetTaskId(), in.GetExecuteId(), in.GetExecuteType().String(),
		)
	}

	return &pb.StopResp{}, err
}

func (l *StopLogic) stopTaskOfUIPlan(in *pb.StopReq) error {
	var (
		projectID = in.GetProjectId()
		planID    = in.GetId()
		taskID    = in.GetTaskId()
		executeID = in.GetExecuteId()
	)

	out, err := l.svcCtx.UIReporterRPC.ViewUIPlanRecord(
		l.ctx, &reporterpb.ViewUIPlanRecordRequest{
			TaskId:    taskID,
			ProjectId: projectID,
			ExecuteId: executeID,
		},
	)
	if err != nil {
		return err
	}

	if strings.EqualFold(out.GetStatus(), pb.ComponentState_Stop.String()) {
		l.Infof(
			"the ui task has been stopped, project_id: %s, plan_id: %s, task_id: %s, execute_id: %s",
			projectID, planID, taskID, executeID,
		)
		return nil
	}

	err = utils.SetStopStatus(l.ctx, l.svcCtx.Redis, in.GetTaskId(), in.GetMetadata())
	if err != nil {
		return errors.Wrapf(
			errorx.Err(errorx.RedisError, err.Error()),
			"failed to send the stop signal to ui task, project_id: %s, plan_id: %s, task_id: %s, execute_id: %s, error: %+v",
			projectID, planID, taskID, executeID, err,
		)
	}

	endTime := time.Now().UnixMilli()
	err = l.svcCtx.TaskInfoProcessor.UpdateTaskExecuteStatus(
		l.ctx, projectID, taskID, commonpb.ExecuteStatus_TES_STOP, endTime,
	)
	if ec, ok := errorx.FromError(err); ok && ec != nil && (ec.Code() == errorx.AlreadyExists || ec.Code() == errorx.AcquireRedisLockFailure) {
		l.Warnf(
			"failed to update the execute status of ui task, project_id: %s, plan_id: %s, task_id: %s, execute_id: %s, status: %s, error: %+v",
			projectID, planID, taskID, executeID, commonpb.ExecuteStatus_TES_STOP, ec,
		)
	} else if err != nil {
		return errors.Wrapf(
			errorx.Err(errorx.RedisError, err.Error()),
			"failed to update the execute status of ui task, project_id: %s, plan_id: %s, task_id: %s, execute_id: %s, status: %s, error: %+v",
			projectID, planID, taskID, executeID, commonpb.ExecuteStatus_TES_STOP, err,
		)
	}

	_, err = l.svcCtx.UIReporterRPC.ModifyUIPlanRecord(
		l.ctx, &reporterpb.PutUIPlanRecordRequest{
			ProjectId: projectID,
			TaskId:    taskID,
			PlanId:    planID,
			ExecuteId: executeID,
			Status:    pb.ComponentState_Stop.String(),
			// EndedAt:   endTime,
			Finished: 1,
		},
	)
	if err != nil {
		return err
	}

	taskInfo := &pb.GenerateReportTaskInfo{
		ProjectId: projectID,
		PlanId:    planID,
		TaskId:    taskID,
		UiCase: &pb.UICaseWorkerInfo{
			UiPlanId:        planID,
			UiPlanExecuteId: executeID,
		},
	}
	task := base.NewTask(
		constants.MQTaskTypeUIWorkerGenerateReport,
		protobuf.MarshalJSONIgnoreError(taskInfo),
		base.WithMaxRetryOptions(0),
	)

	_, err = l.svcCtx.UIWorkerProducer.Send(l.ctx, task, base.QueuePriorityDefault)
	if err != nil {
		return errors.Wrapf(
			errorx.Err(errorx.InternalError, err.Error()),
			"failed to send the report generation task, project_id: %s, plan_id: %s, task_id: %s, execute_id: %s, error: %+v",
			projectID, planID, taskID, executeID, err,
		)
	}

	return nil
}

func (l *StopLogic) stopTaskOfPerfPlan(in *pb.StopReq) error {
	var (
		projectID = in.GetProjectId()
		planID    = in.GetId()
		taskID    = in.GetTaskId()
		executeID = in.GetExecuteId()
	)

	out, err := l.svcCtx.PerfReporterRPC.GetPerfPlanRecord(
		l.ctx, &reporterpb.GetPerfPlanRecordReq{
			TaskId:    taskID,
			ExecuteId: executeID,
			ProjectId: projectID,
		},
	)
	if err != nil {
		return err
	}

	if strings.EqualFold(out.GetRecord().GetStatus(), pb.ComponentState_Stop.String()) {
		l.Infof(
			"the perf task has been stopped, project_id: %s, plan_id: %s, task_id: %s, execute_id: %s",
			projectID, planID, taskID, executeID,
		)
		return nil
	}

	err = utils.SetStopStatus(l.ctx, l.svcCtx.Redis, in.GetTaskId(), in.GetMetadata())
	if err != nil {
		return errors.Wrapf(
			errorx.Err(errorx.RedisError, err.Error()),
			"failed to send the stop signal to perf task, project_id: %s, plan_id: %s, task_id: %s, execute_id: %s, error: %+v",
			projectID, planID, taskID, executeID, err,
		)
	}

	now := time.Now()
	err = l.svcCtx.TaskInfoProcessor.UpdateTaskExecuteStatus(
		l.ctx, projectID, taskID, commonpb.ExecuteStatus_TES_STOP, now.UnixMilli(),
	)
	if ec, ok := errorx.FromError(err); ok && ec != nil && (ec.Code() == errorx.AlreadyExists || ec.Code() == errorx.AcquireRedisLockFailure) {
		l.Warnf(
			"failed to update the execute status of perf task, project_id: %s, plan_id: %s, task_id: %s, execute_id: %s, status: %s, error: %+v",
			projectID, planID, taskID, executeID, commonpb.ExecuteStatus_TES_STOP, ec,
		)
	} else if err != nil {
		return errors.Wrapf(
			errorx.Err(errorx.RedisError, err.Error()),
			"failed to update the execute status of pref task, project_id: %s, plan_id: %s, task_id: %s, execute_id: %s, status: %s, error: %+v",
			projectID, planID, taskID, executeID, commonpb.ExecuteStatus_TES_STOP, err,
		)
	}

	_, err = l.svcCtx.PerfReporterRPC.ModifyPerfPlanRecord(
		l.ctx, &reporterpb.ModifyPerfPlanRecordReq{
			ProjectId: projectID,
			TaskId:    taskID,
			PlanId:    planID,
			ExecuteId: executeID,

			Status:  pb.ComponentState_Stop.String(),
			EndedAt: timestamppb.New(now),
		},
	)
	if err != nil {
		return err
	}

	taskInfo := &pb.FinalHandleTaskInfo{
		ProjectId: projectID,
		PlanId:    planID,
		TaskId:    taskID,
		ExecuteId: executeID,
	}
	task := base.NewTask(
		constants.MQTaskTypePerfWorkerFinalHandle,
		protobuf.MarshalJSONIgnoreError(taskInfo),
		base.WithMaxRetryOptions(0),
	)

	_, err = l.svcCtx.PerfWorkerProducer.Send(l.ctx, task, base.QueuePriorityDefault)
	if err != nil {
		return errors.Wrapf(
			errorx.Err(errorx.InternalError, err.Error()),
			"failed to send the final handle task, project_id: %s, plan_id: %s, task_id: %s, execute_id: %s, error: %+v",
			projectID, planID, taskID, executeID, err,
		)
	}

	return nil
}

func (l *StopLogic) stopTaskOfStabilityPlan(in *pb.StopReq) error {
	var (
		projectID = in.GetProjectId()
		planID    = in.GetId()
		taskID    = in.GetTaskId()
		executeID = in.GetExecuteId()
	)

	out, err := l.svcCtx.StabilityReporterRPC.GetStabilityPlanRecord(
		l.ctx, &reporterpb.GetStabilityPlanRecordReq{
			TaskId:    taskID,
			ExecuteId: executeID,
			ProjectId: projectID,
		},
	)
	if err != nil {
		return err
	}

	if strings.EqualFold(out.GetItem().GetStatus(), pb.ComponentState_Stop.String()) {
		l.Infof(
			"the stability task has been stopped, project_id: %s, plan_id: %s, task_id: %s, execute_id: %s",
			projectID, planID, taskID, executeID,
		)
		return nil
	}

	if err = utils.SetStopStatus(l.ctx, l.svcCtx.Redis, in.GetTaskId(), in.GetMetadata()); err != nil {
		return errors.Wrapf(
			errorx.Err(errorx.RedisError, err.Error()),
			"failed to send the stop signal to stability task, project_id: %s, plan_id: %s, task_id: %s, execute_id: %s, error: %+v",
			projectID, planID, taskID, executeID, err,
		)
	}

	if _, err = l.svcCtx.StabilityReporterRPC.ModifyStabilityPlanRecord(
		l.ctx, &reporterpb.PutStabilityPlanRecordReq{
			ProjectId: projectID,
			TaskId:    taskID,
			PlanId:    planID,
			ExecuteId: executeID,

			Status:  pb.ComponentState_Stop.String(),
			EndedAt: timestamppb.New(time.Now()),
		},
	); err != nil {
		return err
	}

	return nil
}

func (l *StopLogic) stopTaskOfUIAgentComponent(in *pb.StopReq) error {
	var (
		projectID   = in.GetProjectId()
		componentID = in.GetId()
		taskID      = in.GetTaskId()
		executeID   = in.GetExecuteId()
	)

	out, err := l.svcCtx.UIAgentReporterRPC.GetUIAgentComponentRecord(
		l.ctx, &reporterpb.GetUIAgentComponentRecordReq{
			TaskId:    taskID,
			ExecuteId: executeID,
			ProjectId: projectID,
		},
	)
	if err != nil {
		return err
	}

	if strings.EqualFold(out.GetRecord().GetStatus(), pb.ComponentState_Stop.String()) {
		l.Infof(
			"the ui agent task has been stopped, project_id: %s, component_id: %s, task_id: %s, execute_id: %s",
			projectID, componentID, taskID, executeID,
		)
		return nil
	}

	if err = utils.SetStopStatus(l.ctx, l.svcCtx.Redis, in.GetTaskId(), in.GetMetadata()); err != nil {
		return errors.Wrapf(
			errorx.Err(errorx.RedisError, err.Error()),
			"failed to send the stop signal to ui agent task, project_id: %s, component_id: %s, task_id: %s, execute_id: %s, error: %+v",
			projectID, componentID, taskID, executeID, err,
		)
	}

	if _, err = l.svcCtx.UIAgentReporterRPC.ModifyUIAgentComponentRecord(
		l.ctx, &reporterpb.ModifyUIAgentComponentRecordReq{
			TaskId:      taskID,
			ExecuteId:   executeID,
			ProjectId:   projectID,
			ComponentId: componentID,
			Status:      pb.ComponentState_Stop.String(),
			EndedAt:     timestamppb.New(time.Now()),
		},
	); err != nil {
		return err
	}

	return nil
}
