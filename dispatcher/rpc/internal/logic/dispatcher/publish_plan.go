package dispatcherlogic

import (
	"context"
	"fmt"
	"time"

	"github.com/RichardKnop/machinery/v2/tasks"
	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"
	"github.com/zeromicro/go-zero/core/logx"

	qetconstants "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworkerv2/base"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sqlbuilder/rpc"
	userpb "gitlab.ttyuyin.com/TestDevelopment/qet-backend-middleware/user/rpc/pb"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	commonpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/rpc/pb"
	managerpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
	reporterpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
)

type PlanPublisher struct {
	*BasePublisher
}

func NewPlanPublisher(ctx context.Context, svcCtx *svc.ServiceContext) *PlanPublisher {
	return &PlanPublisher{
		BasePublisher: NewBasePublisher(ctx, svcCtx),
	}
}

func (l *PlanPublisher) CreateRecord(in *pb.PublishReq) (err error) {
	_, err = l.svcCtx.ReporterRPC.CreatePlanRecord(
		l.ctx, &reporterpb.PutPlanRecordRequest{
			TaskId:        l.taskId,
			ExecuteId:     l.executeId,
			ProjectId:     in.GetProjectId(),
			PlanId:        in.GetPlan().GetPlanId(),
			PlanExecuteId: l.executeId,
			ExecutedBy:    in.GetUserId(),
			StartedAt:     time.Now().UnixMilli(),
			TriggerMode:   in.GetTriggerMode().String(),
		},
	)
	if err != nil {
		return errors.Wrapf(
			errorx.Err(errorx.InternalError, err.Error()), "请求reporter rpc服务出现错误, error: %s", err,
		)
	}

	return nil
}

func (l *PlanPublisher) Publish(in *pb.PublishReq) (resp *pb.PublishResp, err error) {
	return l.publish(in)
}

func (l *PlanPublisher) handleMgrData(in *pb.PublishReq) (*managerpb.ApiExecutionData, error) {
	mgrData, err := l.getApiExecutionDataStructure(
		&managerpb.GetApiExecutionDataStructureReq{
			ProjectId: in.GetProjectId(),
			Type:      managerpb.ApiExecutionDataType_API_PLAN,
			Id:        in.GetPlan().GetPlanId(),
			Extra: &managerpb.GetApiExecutionDataStructureReq_ApiPlanExtraData{
				ApiPlanExtraData: &managerpb.ApiPlanExtraData{
					Services: in.GetPlan().GetApiPlanInfo().GetServices(),
				},
			},
		},
	)
	if err != nil {
		return nil, err
	}

	// 将计划用途设置为全局配置项
	planPurpose := mgrData.GetPlan().GetPurpose()
	generalConfig := mgrData.GetPlan().GetGeneralConfig()

	if generalConfig != nil {
		variables := generalConfig.GetVariables()
		planPurposeVar := &commonpb.GeneralConfigVar{
			Key:   "plan_purpose",
			Value: planPurpose.String(),
		}
		variables = append(variables, planPurposeVar)
		generalConfig.Variables = variables

		// 判断是否需要替换子环境
		subEnvName := in.GetSubEnvInfo().GetSubEnvName()
		if subEnvName != "" {
			for i, variable := range generalConfig.GetVariables() {
				if variable.Key == "sub_env_name" {
					generalConfig.Variables[i].Value = subEnvName
					break
				}
			}
		}
	}

	// 修改执行人
	switch in.GetTriggerMode() {
	case commonpb.TriggerMode_INTERFACE:
		triggerAccount := in.GetTriggerUser().GetAccount()
		triggerEmail := in.GetTriggerUser().GetEmail()
		if triggerAccount != "" {
			in.UserId = triggerAccount
		} else if triggerEmail != "" {
			condition := rpc.Condition{
				Single: &rpc.SingleCondition{
					Field:   "email",
					Compare: qetconstants.EQ,
					Other: &rpc.Other{
						Value: triggerEmail,
					},
				},
			}
			resp, err := l.svcCtx.UserRPC.UserList(l.ctx, &userpb.UserListReq{Condition: &condition})
			if err != nil {
				return nil, err
			}
			if len(resp.Items) == 0 {
				return nil, errors.New(fmt.Sprintf("根据ci/cd传递的执行人邮箱:「%s」找不到对应的工号", triggerEmail))
			}
			item := resp.Items[0]
			in.UserId = item.GetAccount()
		} else {
			if mgrData.GetPlan().GetMaintainedBy() != "" {
				in.UserId = mgrData.GetPlan().GetMaintainedBy()
			} else {
				in.UserId = mgrData.GetPlan().GetCreatedBy()
			}
		}
	case commonpb.TriggerMode_SCHEDULE:
		if mgrData.GetPlan().GetMaintainedBy() != "" {
			in.UserId = mgrData.GetPlan().GetMaintainedBy()
		} else {
			in.UserId = mgrData.GetPlan().GetCreatedBy()
		}
	}

	return mgrData, err
}

func (l *PlanPublisher) publish(in *pb.PublishReq) (resp *pb.PublishResp, err error) {
	mgrData, err := l.handleMgrData(in)
	if err != nil {
		return resp, err
	}

	_, err = l.record(in, mgrData)
	if err != nil {
		return resp, err
	}

	if !l.IsValid(mgrData) || l.isStop {
		return &pb.PublishResp{TaskId: l.taskId, ExecuteId: l.executeId}, nil
	}

	// 创建任务请求
	taskData := l.getDistributeReq(in, mgrData)
	payload, err := protobuf.MarshalJSON(taskData)
	if err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.SerializationError, err.Error()), "api任务参数序列化失败, error: %s", err,
		)
	}

	_, err = l.svcCtx.DispatcherProducer.Send(
		l.ctx, base.NewTask(
			constants.MQTaskTypeDispatcherPlan, payload,
			base.WithRetentionOptions(time.Minute*2),
			base.WithMaxRetryOptions(3),
		), base.QueuePriorityDefault,
	)
	if err != nil {
		return nil, errors.Wrapf(errorx.Err(errorx.InternalError, err.Error()), "发送数据到mq失败, error: %s", err)
	}

	serviceCasesContent := mgrData.GetServiceCasesContent()

	// 发布监控任务
	err = l.publishMonitorTask(in, serviceCasesContent)
	if err != nil {
		logx.Error(err)
	}

	return &pb.PublishResp{
		TaskId:    l.taskId,
		ExecuteId: l.executeId,
		Version:   l.getVersion(mgrData),
	}, nil
}

func (l *PlanPublisher) publishMonitorTask(
	in *pb.PublishReq, serviceCasesContent []*managerpb.ApiExecutionData_ServiceCasesData,
) error {
	if in.GetPlan().GetCallbackUrl() == "" {
		return nil
	}

	var timeout int64 = 30 * 60 // 默认半小时
	if in.GetPlan().GetCallbackTimeout() != 0 {
		timeout = in.GetPlan().GetCallbackTimeout()
	}

	var noCaseServices []string
	for _, serviceCasesData := range serviceCasesContent {
		if len(serviceCasesData.Cases) == 0 {
			noCaseServices = append(noCaseServices, serviceCasesData.Service)
		}
	}

	taskData := &pb.PlanMonitorReq{
		CallbackUrl:   in.GetPlan().GetCallbackUrl(),
		Timeout:       timeout,
		ProjectId:     in.ProjectId,
		TaskId:        l.taskId,
		PlanExecuteId: l.executeId,
		TestInfo: &pb.PlanMonitorReq_TestInfo{
			NoCaseServices: noCaseServices,
		},
	}
	arg, err := protobuf.MarshalJSON(taskData)
	if err != nil {
		return errors.Wrapf(errorx.Err(errorx.InternalError, err.Error()), "监控任务参数序列化失败, error: %s", err)
	}

	taskSign := &tasks.Signature{
		UUID:       l.getUUID(),
		RoutingKey: l.svcCtx.Config.WorkerProducer.Queue,
		Name:       constants.MQTaskTypeWorkerPlanMonitor,
		Args: []tasks.Arg{
			{Value: arg, Type: "[]byte"},
		},
	}

	_, err = l.svcCtx.WorkerProducer.AsyncPush(l.ctx, taskSign, l.svcCtx.Config.Name)
	if err != nil {
		return errors.Wrapf(errorx.Err(errorx.InternalError, err.Error()), "发送数据到mq失败, error: %s", err)
	}

	return nil
}

func (l *PlanPublisher) getDistributeReq(in *pb.PublishReq, data *managerpb.ApiExecutionData) *pb.DistributeReq {
	suites, inters, services := l.getSuiteOrInterfaces(data)

	return &pb.DistributeReq{
		TriggerMode:    in.GetTriggerMode(),
		TriggerRule:    in.GetTriggerRule(),
		ProjectId:      in.GetProjectId(),
		TaskId:         l.taskId,
		ExecuteType:    in.GetExecuteType(),
		DistributeType: pb.DistributeType_DistributeType_API_PLAN,
		GeneralConfig:  data.GetPlan().GetGeneralConfig(),
		AccountConfig:  data.GetPlan().GetAccountConfigs(),
		UserId:         in.GetUserId(),
		Debug:          in.GetDebug(),
		PurposeType:    data.GetPlan().GetPurpose(),
		Data: &pb.DistributeReq_Plan{
			Plan: &pb.PlanDistributeData{
				PlanId:            in.GetPlan().GetPlanId(),
				PlanExecuteId:     l.executeId,
				State:             pb.ComponentState_Pending,
				Plan:              data.GetPlan(),
				Suites:            suites,
				InterfaceDocument: inters,
				Services:          services,
			},
		},
	}
}

func (l *PlanPublisher) record(
	in *pb.PublishReq, data *managerpb.ApiExecutionData,
) (resp *reporterpb.ModifyPlanRecordResponse, err error) {
	status := l.GetRecordStatus(l.IsValid(data))
	start, end := l.GetRecordTime(status)

	serviceCasesContent := data.GetServiceCasesContent()
	serviceCasesContentStr, _ := protobuf.MarshalJSONWithMessagesToString(serviceCasesContent)

	// 添加审批人（接口触发）
	var approved_by string
	if len(in.GetApprovers()) > 0 {
		approvers := []string{}
		for _, approver := range in.GetApprovers() {
			if approver.Account != "" {
				approvers = append(approvers, approver.Account)
			} else if approver.Email != "" {
				condition := rpc.Condition{
					Single: &rpc.SingleCondition{
						Field:   "email",
						Compare: "EQ",
						Other: &rpc.Other{
							Value: approver.Email,
						},
					},
				}
				resp, err := l.svcCtx.UserRPC.UserList(l.ctx, &userpb.UserListReq{Condition: &condition})
				if err != nil {
					return nil, err
				}
				if len(resp.Items) == 0 {
					return nil, errors.Errorf("根据ci/cd传递的审批人邮箱:「%s」找不到对应的工号", approver.Email)
				}
				approvers = append(approvers, resp.Items[0].GetAccount())
			}
		}
		approved_by = jsonx.MarshalToStringIgnoreError(approvers)
	}

	resp, err = l.svcCtx.ReporterRPC.ModifyPlanRecord(
		l.ctx, &reporterpb.PutPlanRecordRequest{
			TaskId:              l.taskId,
			ExecuteId:           l.executeId,
			ProjectId:           in.GetProjectId(),
			GeneralConfig:       jsonx.MarshalToStringIgnoreError(Pb2ApiGeneralConfig(data.GetPlan().GetGeneralConfig())),
			AccountConfig:       jsonx.MarshalToStringIgnoreError(Pb2ApiAccountConfig(data.GetPlan().GetAccountConfigs())),
			PlanId:              in.GetPlan().GetPlanId(),
			PlanName:            data.GetPlan().GetName(),
			PlanPurpose:         data.GetPlan().GetPurpose().String(),
			PlanExecuteId:       l.executeId,
			Status:              status.String(),
			ServiceCasesContent: serviceCasesContentStr,
			ExecutedBy:          in.GetUserId(),
			ApprovedBy:          approved_by,
			StartedAt:           start,
			EndedAt:             end,
		},
	)
	if err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.InternalError, err.Error()), "请求reporter rpc服务出现错误, error: %s", err,
		)
	}

	return resp, err
}

func (l *PlanPublisher) getSuiteOrInterfaces(data *managerpb.ApiExecutionData) (
	suites, documents, services []*managerpb.ApiExecutionData,
) {
	if len(data.GetChildren()) == 0 {
		return suites, documents, services
	}

	children := data.GetChildren()[0].GetChild()
	if len(children) > 0 {
		suites = make([]*managerpb.ApiExecutionData, 0, len(children))
		documents = make([]*managerpb.ApiExecutionData, 0, len(children))
		services = make([]*managerpb.ApiExecutionData, 0, len(children))

		for _, child := range children {
			switch child.GetType() {
			case managerpb.ApiExecutionDataType_API_SUITE:
				suites = append(suites, child)
			case managerpb.ApiExecutionDataType_INTERFACE_DOCUMENT:
				documents = append(documents, child)
			case managerpb.ApiExecutionDataType_API_SERVICE:
				services = append(services, child)
			}
		}
	}

	return suites, documents, services
}

// IsValid 是否有效
func (l *PlanPublisher) IsValid(data *managerpb.ApiExecutionData) bool {
	// 失效的计划不能执行（拦截定时计划）
	return data.GetPlan().GetState() != managerpb.CommonState_CS_DISABLE
}

// Panic 异常处理
func (l *PlanPublisher) Panic(in *pb.PublishReq, err error) {
	if err == nil {
		return
	}

	// 直接更新状态
	_, _ = l.svcCtx.ReporterRPC.ModifyPlanRecord(
		l.ctx, &reporterpb.PutPlanRecordRequest{
			TaskId:        l.taskId,
			ProjectId:     in.GetProjectId(),
			ExecuteId:     l.executeId,
			PlanId:        in.GetPlan().GetPlanId(),
			PlanExecuteId: l.executeId,
			Status:        pb.ComponentState_Panic.String(),
			ExecutedBy:    in.GetUserId(),
			Content:       jsonx.MarshalToStringIgnoreError(err.Error()),
			EndedAt:       time.Now().UnixMilli(),
		},
	)
}
