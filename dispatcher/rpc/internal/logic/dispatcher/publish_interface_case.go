package dispatcherlogic

import (
	"context"
	"time"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworkerv2/base"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/common/callbackclient"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/rpc/pb"
	managerpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
	reporterpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
)

type InterfaceCasePublisher struct {
	*BasePublisher
}

func NewInterfaceCasePublisher(ctx context.Context, svcCtx *svc.ServiceContext) *InterfaceCasePublisher {
	return &InterfaceCasePublisher{
		BasePublisher: NewBasePublisher(ctx, svcCtx),
	}
}

func (l *InterfaceCasePublisher) CreateRecord(in *pb.PublishReq) (err error) {
	_, err = l.svcCtx.ReporterRPC.Create(
		l.ctx, &reporterpb.PutRecordRequest{
			TaskId:                   l.taskId,
			ProjectId:                in.GetProjectId(),
			ExecuteId:                l.executeId,
			ExecuteType:              in.GetExecuteType().String(),
			GeneralConfig:            jsonx.MarshalToStringIgnoreError(Pb2ApiGeneralConfig(in.GetInterfaceCase().GetGeneralConfig())),
			AccountConfig:            jsonx.MarshalToStringIgnoreError(Pb2ApiAccountConfig(in.GetInterfaceCase().GetAccountConfig())),
			ComponentId:              in.GetInterfaceCase().GetInterfaceCaseId(),
			ComponentType:            managerpb.ApiExecutionDataType_INTERFACE_CASE.String(),
			ComponentExecuteId:       l.executeId,
			ParentComponentId:        in.GetInterfaceCase().GetInterfaceId(),
			ParentComponentExecuteId: in.GetInterfaceCase().GetInterfaceExecuteId(),
			Version:                  in.GetInterfaceCase().GetVersion(),
			Times:                    1,
			ExecutedBy:               in.GetUserId(),
			StartedAt:                time.Now().UnixMilli(),
			IsRoot:                   1,
		},
	)
	if err != nil {
		return errors.Wrapf(
			errorx.Err(errorx.InternalError, err.Error()), "请求reporter rpc服务出现错误, error: %s", err,
		)
	}
	return nil
}

func (l *InterfaceCasePublisher) Publish(in *pb.PublishReq) (resp *pb.PublishResp, err error) {
	return l.publish(in)
}

func (l *InterfaceCasePublisher) publish(in *pb.PublishReq) (resp *pb.PublishResp, err error) {
	data := in.GetInterfaceCase()
	apiExecutionData, err := l.getApiExecutionData(
		&managerpb.GetApiExecutionDataReq{
			ProjectId: in.GetProjectId(),
			Type:      managerpb.ApiExecutionDataType_INTERFACE_CASE,
			Id:        data.GetInterfaceCaseId(),
			Version:   data.GetVersion(),
		},
	)
	if err != nil {
		return resp, err
	}

	_, err = l.record(in, apiExecutionData)
	if err != nil {
		return resp, err
	}

	if !l.IsValid(apiExecutionData) || l.isStop {
		return &pb.PublishResp{TaskId: l.taskId, ExecuteId: l.executeId}, nil
	}

	// 创建任务请求
	taskData := l.getWorkerReq(in, apiExecutionData)
	payload, err := protobuf.MarshalJSON(taskData)
	if err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.SerializationError, err.Error()), "api任务参数序列化失败, error: %s", err,
		)
	}

	task := base.NewTask(
		constants.MQTaskTypeAPIWorkerInterfaceCase,
		payload,
		base.WithRetentionOptions(time.Minute*2),
		base.WithMaxRetryOptions(0),
	)
	if in.GetInterfaceCase().GetInterfaceId() != "" {
		task.SetCallback(constants.MQTaskTypeDispatcherCallback, l.svcCtx.Config.DispatcherProducer.Queue)
	}
	_, err = l.svcCtx.ApiWorkerProducer.Send(
		l.ctx, task, base.QueuePriorityDefault,
	)
	if err != nil {
		return nil, errors.Wrapf(errorx.Err(errorx.InternalError, err.Error()), "发送数据到mq失败, error: %s", err)
	}

	return &pb.PublishResp{
		TaskId:    l.taskId,
		ExecuteId: l.executeId,
		Version:   l.getVersion(apiExecutionData),
	}, nil
}

func (l *InterfaceCasePublisher) getWorkerReq(in *pb.PublishReq, data *managerpb.ApiExecutionData) *pb.WorkerReq {
	case_ := in.GetInterfaceCase()

	return &pb.WorkerReq{
		TriggerMode:   in.GetTriggerMode(),
		TriggerRule:   in.GetTriggerRule(),
		ProjectId:     in.GetProjectId(),
		TaskId:        l.taskId,
		ExecuteId:     l.executeId,
		ExecuteType:   in.GetExecuteType(),
		WorkerType:    pb.WorkerType_WorkerType_INTERFACE_CASE,
		GeneralConfig: case_.GetGeneralConfig(),
		AccountConfig: case_.GetAccountConfig(),
		UserId:        in.GetUserId(),
		NodeData:      data,
		Debug:         in.GetDebug(),
		PurposeType:   in.GetPurposeType(),
		Data: &pb.WorkerReq_InterfaceCase{
			InterfaceCase: &pb.InterfaceCaseWorkerInfo{
				InterfaceCaseId:        case_.GetInterfaceCaseId(),
				InterfaceCaseExecuteId: l.executeId,
				InterfaceId:            case_.GetInterfaceId(),
				InterfaceExecuteId:     case_.GetInterfaceExecuteId(),
				Version:                l.getVersion(data),
				DocumentId:             case_.GetDocumentId(),
				DocumentName:           case_.GetDocumentName(),
				PlanId:                 case_.GetPlanId(),
				PlanExecuteId:          case_.GetPlanExecuteId(),
				PlanName:               case_.GetPlanName(),
			},
		},
	}
}

func (l *InterfaceCasePublisher) record(
	in *pb.PublishReq, data *managerpb.ApiExecutionData,
) (resp *reporterpb.ModifyRecordResponse, err error) {
	status := l.GetRecordStatus(l.IsValid(data))
	start, end := l.GetRecordTime(status)

	resp, err = l.svcCtx.ReporterRPC.ModifyCaseRecord(
		l.ctx, &reporterpb.PutRecordRequest{
			TaskId:                   l.taskId,
			ProjectId:                in.GetProjectId(),
			ExecuteId:                l.executeId,
			ExecuteType:              in.GetExecuteType().String(),
			GeneralConfig:            jsonx.MarshalToStringIgnoreError(Pb2ApiGeneralConfig(in.GetInterfaceCase().GetGeneralConfig())),
			AccountConfig:            jsonx.MarshalToStringIgnoreError(Pb2ApiAccountConfig(in.GetInterfaceCase().GetAccountConfig())),
			ComponentId:              data.GetId(),
			ComponentType:            data.GetType().String(),
			ComponentName:            l.getComponentName(data),
			ComponentExecuteId:       l.executeId,
			ParentComponentId:        in.GetInterfaceCase().GetInterfaceId(),
			ParentComponentExecuteId: in.GetInterfaceCase().GetInterfaceExecuteId(),
			Version:                  l.getVersion(data),
			Times:                    1,
			Status:                   status.String(),
			ExecutedBy:               in.GetUserId(),
			MaintainedBy:             l.getMaintainer(data),
			StartedAt:                start,
			EndedAt:                  end,
			IsRoot:                   1,
		},
	)
	if err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.InternalError, err.Error()), "请求reporter rpc服务出现错误, error: %s", err,
		)
	}

	return resp, nil
}

// IsValid 是否有效
func (l *InterfaceCasePublisher) IsValid(data *managerpb.ApiExecutionData) bool {
	// 调试不受固有状态影响
	return true
}

// Panic 异常处理
func (l *InterfaceCasePublisher) Panic(in *pb.PublishReq, err error) {
	if err == nil {
		return
	}

	// 直接更新状态
	_, _ = l.svcCtx.ReporterRPC.ModifyCaseRecord(
		l.ctx, &reporterpb.PutRecordRequest{
			TaskId:             l.taskId,
			ProjectId:          in.GetProjectId(),
			ExecuteId:          l.executeId,
			ExecuteType:        in.GetExecuteType().String(),
			ComponentId:        in.GetInterfaceCase().GetInterfaceCaseId(),
			ComponentType:      managerpb.ApiExecutionDataType_INTERFACE_CASE.String(),
			ComponentExecuteId: l.executeId,
			Times:              1,
			Status:             pb.ComponentState_Panic.String(),
			Content:            jsonx.MarshalToStringIgnoreError(err.Error()),
			ExecutedBy:         in.GetUserId(),
			IsRoot:             1,
			EndedAt:            time.Now().UnixMilli(),
		},
	)
	if in.GetExecuteType() == managerpb.ApiExecutionDataType_INTERFACE_CASE {
		return
	}

	// 发起回调
	cb := &pb.CallbackReq{
		TriggerMode:  in.GetTriggerMode(),
		TriggerRule:  in.GetTriggerRule(),
		ProjectId:    in.GetProjectId(),
		TaskId:       l.taskId,
		ExecuteType:  in.GetExecuteType(),
		CallbackType: pb.CallbackType_CallbackType_INTERFACE_CASE,
		PurposeType:  in.GetPurposeType(),
		Data: &pb.CallbackReq_InterfaceCase{
			InterfaceCase: &pb.InterfaceCaseCallbackData{
				InterfaceId:            in.GetInterfaceCase().GetInterfaceId(),
				InterfaceExecuteId:     in.GetInterfaceCase().GetInterfaceExecuteId(),
				InterfaceCaseId:        in.GetInterfaceCase().GetInterfaceCaseId(),
				InterfaceCaseExecuteId: l.executeId,
				InterfaceCaseState:     pb.ComponentState_Panic,
				Version:                in.GetCase().GetVersion(),
			},
		},
	}

	_ = callbackclient.CallBack(
		l.ctx,
		l.svcCtx.DispatcherProducer,
		l.svcCtx.Config.Name,
		l.svcCtx.Config.DispatcherProducer.Queue,
		l.getUUID(),
		cb,
	)
}
