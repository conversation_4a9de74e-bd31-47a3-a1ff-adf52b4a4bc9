package tasks

import (
	"context"
	"runtime/debug"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/trace"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworkerv2/base"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	dispatcherlogic "gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/rpc/internal/logic/dispatcher"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/rpc/pb"
)

type PublishTaskLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewPublishTaskLogic(ctx context.Context, svcCtx *svc.ServiceContext) *PublishTaskLogic {
	return &PublishTaskLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *PublishTaskLogic) Consume(payload []byte) (err error) {
	var in pb.PublishReq
	if err := protobuf.UnmarshalJSON(payload, &in); err != nil {
		return errors.Wrapf(
			errorx.Err(errorx.SerializationError, err.Error()),
			"failed to unmarshal publish task payload[%s], error: %+v", payload, err,
		)
	}

	logic := dispatcherlogic.NewPublishLogic(l.ctx, l.svcCtx)
	_, err = logic.Publish(&in)
	return err
}

// Deprecated: use new api.
func PublishTask(svcCtx *svc.ServiceContext) TaskBytesWithContext {
	return func(ctx context.Context, payload []byte) error {
		return NewPublishTaskLogic(ctx, svcCtx).Consume(payload)
	}
}

type ProcessorOfPublishTask struct {
	svcCtx *svc.ServiceContext
}

func NewProcessorOfPublishTask(svcCtx *svc.ServiceContext) (call base.Handler) {
	return &ProcessorOfPublishTask{
		svcCtx: svcCtx,
	}
}

func (processor *ProcessorOfPublishTask) ProcessTask(ctx context.Context, task *base.Task) (result []byte, err error) {
	logger := logx.WithContext(ctx)
	logger.Debugf(
		"processor trace_id: %s, span_id: %s, task_name: %s",
		trace.TraceIDFromContext(ctx), trace.SpanIDFromContext(ctx), task.Typename,
	)
	defer func() {
		if r := recover(); r != nil {
			logger.Errorf("processor panic: %+v, stack: %s", r, debug.Stack())
		}
	}()

	if err = NewPublishTaskLogic(ctx, processor.svcCtx).Consume(task.Payload); err != nil {
		return []byte(constants.FAILURE), err
	}

	return []byte(constants.SUCCESS), nil
}
