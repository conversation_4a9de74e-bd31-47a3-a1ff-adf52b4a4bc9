package tasks

import (
	"context"
	"runtime/debug"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/trace"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworkerv2/base"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	commonpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
	dispatcherlogic "gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/rpc/internal/logic/dispatcher"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/rpc/pb"
	managerpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type PeriodicPlanTaskLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewPeriodicPlanTaskLogic(ctx context.Context, svcCtx *svc.ServiceContext) *PeriodicPlanTaskLogic {
	return &PeriodicPlanTaskLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *PeriodicPlanTaskLogic) Consume(payload string) error {
	var (
		info commonpb.PeriodicPlanTaskInfo
		out  *pb.PublishResp
		err  error
	)

	if err = protobuf.UnmarshalJSONFromString(payload, &info); err != nil {
		return errors.Wrapf(
			errorx.Err(errorx.SerializationError, err.Error()),
			"failed to unmarshal periodic plan task, payload: %s, error: %+v", payload, err,
		)
	}

	var req *pb.PublishReq
	switch info.GetPlanType() {
	case commonpb.PlanType_API:
		req = &pb.PublishReq{
			TriggerMode: commonpb.TriggerMode_SCHEDULE,
			TriggerRule: info.GetCronExpression(),
			ProjectId:   info.GetProjectId(),
			ExecuteType: managerpb.ApiExecutionDataType_API_PLAN,
			PublishType: pb.PublishType_PublishType_API_PLAN,
			Data: &pb.PublishReq_Plan{
				Plan: &pb.PlanPublishInfo{
					PlanId: info.GetPlanId(),
				},
			},
		}
	case commonpb.PlanType_UI:
		req = &pb.PublishReq{
			TriggerMode: commonpb.TriggerMode_SCHEDULE,
			TriggerRule: info.GetCronExpression(),
			ProjectId:   info.GetProjectId(),
			ExecuteType: managerpb.ApiExecutionDataType_UI_PLAN,
			PublishType: pb.PublishType_PublishType_UI_PLAN,
			Data: &pb.PublishReq_UiPlan{
				UiPlan: &pb.UIPlanPublishInfo{
					UiPlanId: info.GetPlanId(),
				},
			},
		}
	case commonpb.PlanType_PERF:
		req = &pb.PublishReq{
			TriggerMode: commonpb.TriggerMode_SCHEDULE,
			TriggerRule: info.GetCronExpression(),
			ProjectId:   info.GetProjectId(),
			ExecuteType: managerpb.ApiExecutionDataType_PERF_PLAN,
			PublishType: pb.PublishType_PublishType_PERF_PLAN,
			Data: &pb.PublishReq_PerfPlan{
				PerfPlan: &pb.PerfPlanPublishInfo{
					PerfPlanId: info.GetPlanId(),
					PerfPlanInfo: &pb.PerfPlanInfo{
						ExecuteType:             commonpb.PerfTaskType_RUN,
						EstimatedTime:           0,
						SendPreviewNotification: true,
					},
				},
			},
		}
	case commonpb.PlanType_STABILITY:
		req = &pb.PublishReq{
			TriggerMode: commonpb.TriggerMode_SCHEDULE,
			TriggerRule: info.GetCronExpression(),
			ProjectId:   info.GetProjectId(),
			ExecuteType: managerpb.ApiExecutionDataType_STABILITY_PLAN,
			PublishType: pb.PublishType_PublishType_STABILITY_PLAN,
			Data: &pb.PublishReq_StabilityPlan{
				StabilityPlan: &pb.StabilityPlanPublishInfo{
					StabilityPlanId: info.GetPlanId(),
				},
			},
		}
	default:
		return errorx.Errorf(
			errorx.ValidateParamError, "invalid plan type: %s", info.GetPlanType(),
		)
	}

	out, err = dispatcherlogic.NewPublishLogic(l.ctx, l.svcCtx).Publish(req)
	if err != nil {
		return err
	}

	l.Infof("publish periodic plan task successfully, resp: %s", protobuf.MarshalJSONIgnoreError(out))
	return nil
}

// Deprecated: use new api.
func PeriodicPlanTask(svcCtx *svc.ServiceContext) TaskWithContext {
	return func(ctx context.Context, payload string) error {
		return NewPeriodicPlanTaskLogic(ctx, svcCtx).Consume(payload)
	}
}

type ProcessorOfPeriodicPlanTask struct {
	svcCtx *svc.ServiceContext
}

func NewProcessorOfPeriodicPlanTask(svcCtx *svc.ServiceContext) base.Handler {
	return &ProcessorOfPeriodicPlanTask{
		svcCtx: svcCtx,
	}
}

func (p *ProcessorOfPeriodicPlanTask) ProcessTask(ctx context.Context, task *base.Task) ([]byte, error) {
	logger := logx.WithContext(ctx)
	logger.Debugf(
		"processor trace_id: %s, span_id: %s, task_name: %s",
		trace.TraceIDFromContext(ctx), trace.SpanIDFromContext(ctx), task.Typename,
	)
	defer func() {
		if r := recover(); r != nil {
			logger.Errorf("processor panic: %+v, stack: %s", r, debug.Stack())
		}
	}()

	if err := NewPeriodicPlanTaskLogic(ctx, p.svcCtx).Consume(string(task.Payload)); err != nil {
		return []byte(constants.FAILURE), err
	}

	return []byte(constants.SUCCESS), nil
}
