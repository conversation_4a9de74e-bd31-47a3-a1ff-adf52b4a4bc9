package internal

import (
	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/proc"
	"github.com/zeromicro/go-zero/core/threading"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworkerv2/consumer"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/rpc/internal/tasks"
)

func SetupOperation(svcCtx *svc.ServiceContext) error {
	if err := registerTasksToBeatConsumer(svcCtx); err != nil {
		return errors.Wrap(err, "failed to register tasks")
	}
	launchBeatConsumer(svcCtx.BeatConsumer)

	if err := registerTasksToTaskConsumer(svcCtx); err != nil {
		return errors.Wrap(err, "failed to register tasks")
	}
	launchTaskConsumer(svcCtx.TaskConsumer)

	return nil
}

// registerTasksToBeatConsumer 注册定时任务
func registerTasksToBeatConsumer(svcCtx *svc.ServiceContext) error {
	return svcCtx.BeatConsumer.RegisterHandlers(
		// 定时触发的测试计划执行任务
		consumer.NewTaskHandlerOjb(
			constants.MQTaskTypeDispatcherPeriodicPlanTask, tasks.NewProcessorOfPeriodicPlanTask(svcCtx),
		),
	)
}

// registerTasksToTaskConsumer 注册处理任务
func registerTasksToTaskConsumer(svcCtx *svc.ServiceContext) error {
	return svcCtx.TaskConsumer.RegisterHandlers(
		// 分发任务
		consumer.NewTaskHandlerOjb(
			constants.MQTaskTypeDispatcherPublishTask, tasks.NewProcessorOfPublishTask(svcCtx),
		),
	)
}

func launchBeatConsumer(consumer *consumer.Consumer) {
	threading.GoSafe(
		func() {
			logx.Info("starting the beat consumer")
			consumer.Start()
		},
	)

	proc.AddShutdownListener(
		func() {
			logx.Info("stopping the beat consumer")
			consumer.Stop()
		},
	)
}

func launchTaskConsumer(consumer *consumer.Consumer) {
	threading.GoSafe(
		func() {
			logx.Info("starting the task consumer")
			consumer.Start()
		},
	)

	proc.AddShutdownListener(
		func() {
			logx.Info("stopping the task consumer")
			consumer.Stop()
		},
	)
}
