// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: dispatcher/distribute.proto

package pb

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	pb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"

	pb1 "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = pb.TriggerMode(0)

	_ = pb1.ApiExecutionDataType(0)
)

// Validate checks the field values on DistributeReq with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *DistributeReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DistributeReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in DistributeReqMultiError, or
// nil if none found.
func (m *DistributeReq) ValidateAll() error {
	return m.validate(true)
}

func (m *DistributeReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TriggerMode

	// no validation rules for TriggerRule

	// no validation rules for ProjectId

	// no validation rules for TaskId

	// no validation rules for ExecuteType

	// no validation rules for DistributeType

	if all {
		switch v := interface{}(m.GetGeneralConfig()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DistributeReqValidationError{
					field:  "GeneralConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DistributeReqValidationError{
					field:  "GeneralConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetGeneralConfig()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DistributeReqValidationError{
				field:  "GeneralConfig",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetAccountConfig() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, DistributeReqValidationError{
						field:  fmt.Sprintf("AccountConfig[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, DistributeReqValidationError{
						field:  fmt.Sprintf("AccountConfig[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return DistributeReqValidationError{
					field:  fmt.Sprintf("AccountConfig[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for User

	// no validation rules for UserId

	// no validation rules for PurposeType

	// no validation rules for PriorityType

	// no validation rules for Debug

	switch v := m.Data.(type) {
	case *DistributeReq_Plan:
		if v == nil {
			err := DistributeReqValidationError{
				field:  "Data",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetPlan()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, DistributeReqValidationError{
						field:  "Plan",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, DistributeReqValidationError{
						field:  "Plan",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetPlan()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return DistributeReqValidationError{
					field:  "Plan",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *DistributeReq_Suite:
		if v == nil {
			err := DistributeReqValidationError{
				field:  "Data",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetSuite()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, DistributeReqValidationError{
						field:  "Suite",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, DistributeReqValidationError{
						field:  "Suite",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetSuite()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return DistributeReqValidationError{
					field:  "Suite",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *DistributeReq_InterfaceDocument:
		if v == nil {
			err := DistributeReqValidationError{
				field:  "Data",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetInterfaceDocument()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, DistributeReqValidationError{
						field:  "InterfaceDocument",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, DistributeReqValidationError{
						field:  "InterfaceDocument",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetInterfaceDocument()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return DistributeReqValidationError{
					field:  "InterfaceDocument",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *DistributeReq_UiPlan:
		if v == nil {
			err := DistributeReqValidationError{
				field:  "Data",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetUiPlan()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, DistributeReqValidationError{
						field:  "UiPlan",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, DistributeReqValidationError{
						field:  "UiPlan",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetUiPlan()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return DistributeReqValidationError{
					field:  "UiPlan",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *DistributeReq_UiSuite:
		if v == nil {
			err := DistributeReqValidationError{
				field:  "Data",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetUiSuite()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, DistributeReqValidationError{
						field:  "UiSuite",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, DistributeReqValidationError{
						field:  "UiSuite",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetUiSuite()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return DistributeReqValidationError{
					field:  "UiSuite",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *DistributeReq_Service:
		if v == nil {
			err := DistributeReqValidationError{
				field:  "Data",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetService()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, DistributeReqValidationError{
						field:  "Service",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, DistributeReqValidationError{
						field:  "Service",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetService()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return DistributeReqValidationError{
					field:  "Service",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *DistributeReq_PerfPlan:
		if v == nil {
			err := DistributeReqValidationError{
				field:  "Data",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetPerfPlan()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, DistributeReqValidationError{
						field:  "PerfPlan",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, DistributeReqValidationError{
						field:  "PerfPlan",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetPerfPlan()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return DistributeReqValidationError{
					field:  "PerfPlan",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *DistributeReq_PerfSuite:
		if v == nil {
			err := DistributeReqValidationError{
				field:  "Data",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetPerfSuite()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, DistributeReqValidationError{
						field:  "PerfSuite",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, DistributeReqValidationError{
						field:  "PerfSuite",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetPerfSuite()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return DistributeReqValidationError{
					field:  "PerfSuite",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return DistributeReqMultiError(errors)
	}

	return nil
}

// DistributeReqMultiError is an error wrapping multiple validation errors
// returned by DistributeReq.ValidateAll() if the designated constraints
// aren't met.
type DistributeReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DistributeReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DistributeReqMultiError) AllErrors() []error { return m }

// DistributeReqValidationError is the validation error returned by
// DistributeReq.Validate if the designated constraints aren't met.
type DistributeReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DistributeReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DistributeReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DistributeReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DistributeReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DistributeReqValidationError) ErrorName() string { return "DistributeReqValidationError" }

// Error satisfies the builtin error interface
func (e DistributeReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDistributeReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DistributeReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DistributeReqValidationError{}

// Validate checks the field values on PlanDistributeData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *PlanDistributeData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PlanDistributeData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PlanDistributeDataMultiError, or nil if none found.
func (m *PlanDistributeData) ValidateAll() error {
	return m.validate(true)
}

func (m *PlanDistributeData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for PlanId

	// no validation rules for PlanExecuteId

	// no validation rules for State

	if all {
		switch v := interface{}(m.GetPlan()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PlanDistributeDataValidationError{
					field:  "Plan",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PlanDistributeDataValidationError{
					field:  "Plan",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPlan()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PlanDistributeDataValidationError{
				field:  "Plan",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetSuites() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, PlanDistributeDataValidationError{
						field:  fmt.Sprintf("Suites[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, PlanDistributeDataValidationError{
						field:  fmt.Sprintf("Suites[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return PlanDistributeDataValidationError{
					field:  fmt.Sprintf("Suites[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetInterfaceDocument() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, PlanDistributeDataValidationError{
						field:  fmt.Sprintf("InterfaceDocument[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, PlanDistributeDataValidationError{
						field:  fmt.Sprintf("InterfaceDocument[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return PlanDistributeDataValidationError{
					field:  fmt.Sprintf("InterfaceDocument[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetServices() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, PlanDistributeDataValidationError{
						field:  fmt.Sprintf("Services[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, PlanDistributeDataValidationError{
						field:  fmt.Sprintf("Services[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return PlanDistributeDataValidationError{
					field:  fmt.Sprintf("Services[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return PlanDistributeDataMultiError(errors)
	}

	return nil
}

// PlanDistributeDataMultiError is an error wrapping multiple validation errors
// returned by PlanDistributeData.ValidateAll() if the designated constraints
// aren't met.
type PlanDistributeDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PlanDistributeDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PlanDistributeDataMultiError) AllErrors() []error { return m }

// PlanDistributeDataValidationError is the validation error returned by
// PlanDistributeData.Validate if the designated constraints aren't met.
type PlanDistributeDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PlanDistributeDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PlanDistributeDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PlanDistributeDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PlanDistributeDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PlanDistributeDataValidationError) ErrorName() string {
	return "PlanDistributeDataValidationError"
}

// Error satisfies the builtin error interface
func (e PlanDistributeDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPlanDistributeData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PlanDistributeDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PlanDistributeDataValidationError{}

// Validate checks the field values on SuiteDistributeData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SuiteDistributeData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SuiteDistributeData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SuiteDistributeDataMultiError, or nil if none found.
func (m *SuiteDistributeData) ValidateAll() error {
	return m.validate(true)
}

func (m *SuiteDistributeData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SuiteId

	// no validation rules for SuiteExecuteId

	// no validation rules for PlanId

	// no validation rules for PlanExecuteId

	// no validation rules for State

	if all {
		switch v := interface{}(m.GetSuite()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SuiteDistributeDataValidationError{
					field:  "Suite",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SuiteDistributeDataValidationError{
					field:  "Suite",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSuite()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SuiteDistributeDataValidationError{
				field:  "Suite",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetCases() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SuiteDistributeDataValidationError{
						field:  fmt.Sprintf("Cases[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SuiteDistributeDataValidationError{
						field:  fmt.Sprintf("Cases[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SuiteDistributeDataValidationError{
					field:  fmt.Sprintf("Cases[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for PlanName

	if len(errors) > 0 {
		return SuiteDistributeDataMultiError(errors)
	}

	return nil
}

// SuiteDistributeDataMultiError is an error wrapping multiple validation
// errors returned by SuiteDistributeData.ValidateAll() if the designated
// constraints aren't met.
type SuiteDistributeDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SuiteDistributeDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SuiteDistributeDataMultiError) AllErrors() []error { return m }

// SuiteDistributeDataValidationError is the validation error returned by
// SuiteDistributeData.Validate if the designated constraints aren't met.
type SuiteDistributeDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SuiteDistributeDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SuiteDistributeDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SuiteDistributeDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SuiteDistributeDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SuiteDistributeDataValidationError) ErrorName() string {
	return "SuiteDistributeDataValidationError"
}

// Error satisfies the builtin error interface
func (e SuiteDistributeDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSuiteDistributeData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SuiteDistributeDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SuiteDistributeDataValidationError{}

// Validate checks the field values on InterfaceDocumentDistributeData with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *InterfaceDocumentDistributeData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on InterfaceDocumentDistributeData with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// InterfaceDocumentDistributeDataMultiError, or nil if none found.
func (m *InterfaceDocumentDistributeData) ValidateAll() error {
	return m.validate(true)
}

func (m *InterfaceDocumentDistributeData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for InterfaceDocumentId

	// no validation rules for InterfaceDocumentExecuteId

	// no validation rules for PlanId

	// no validation rules for PlanExecuteId

	// no validation rules for State

	if all {
		switch v := interface{}(m.GetInterfaceDocument()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InterfaceDocumentDistributeDataValidationError{
					field:  "InterfaceDocument",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InterfaceDocumentDistributeDataValidationError{
					field:  "InterfaceDocument",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetInterfaceDocument()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InterfaceDocumentDistributeDataValidationError{
				field:  "InterfaceDocument",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetInterfaceCases() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, InterfaceDocumentDistributeDataValidationError{
						field:  fmt.Sprintf("InterfaceCases[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, InterfaceDocumentDistributeDataValidationError{
						field:  fmt.Sprintf("InterfaceCases[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return InterfaceDocumentDistributeDataValidationError{
					field:  fmt.Sprintf("InterfaceCases[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for PlanName

	if len(errors) > 0 {
		return InterfaceDocumentDistributeDataMultiError(errors)
	}

	return nil
}

// InterfaceDocumentDistributeDataMultiError is an error wrapping multiple
// validation errors returned by InterfaceDocumentDistributeData.ValidateAll()
// if the designated constraints aren't met.
type InterfaceDocumentDistributeDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m InterfaceDocumentDistributeDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m InterfaceDocumentDistributeDataMultiError) AllErrors() []error { return m }

// InterfaceDocumentDistributeDataValidationError is the validation error
// returned by InterfaceDocumentDistributeData.Validate if the designated
// constraints aren't met.
type InterfaceDocumentDistributeDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e InterfaceDocumentDistributeDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e InterfaceDocumentDistributeDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e InterfaceDocumentDistributeDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e InterfaceDocumentDistributeDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e InterfaceDocumentDistributeDataValidationError) ErrorName() string {
	return "InterfaceDocumentDistributeDataValidationError"
}

// Error satisfies the builtin error interface
func (e InterfaceDocumentDistributeDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sInterfaceDocumentDistributeData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = InterfaceDocumentDistributeDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = InterfaceDocumentDistributeDataValidationError{}

// Validate checks the field values on UIPlanDistributeData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UIPlanDistributeData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UIPlanDistributeData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UIPlanDistributeDataMultiError, or nil if none found.
func (m *UIPlanDistributeData) ValidateAll() error {
	return m.validate(true)
}

func (m *UIPlanDistributeData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for UiPlanId

	// no validation rules for UiPlanExecuteId

	// no validation rules for State

	if all {
		switch v := interface{}(m.GetUiPlan()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UIPlanDistributeDataValidationError{
					field:  "UiPlan",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UIPlanDistributeDataValidationError{
					field:  "UiPlan",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUiPlan()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UIPlanDistributeDataValidationError{
				field:  "UiPlan",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetUiSuites() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, UIPlanDistributeDataValidationError{
						field:  fmt.Sprintf("UiSuites[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, UIPlanDistributeDataValidationError{
						field:  fmt.Sprintf("UiSuites[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return UIPlanDistributeDataValidationError{
					field:  fmt.Sprintf("UiSuites[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetMetaData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UIPlanDistributeDataValidationError{
					field:  "MetaData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UIPlanDistributeDataValidationError{
					field:  "MetaData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMetaData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UIPlanDistributeDataValidationError{
				field:  "MetaData",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UIPlanDistributeDataMultiError(errors)
	}

	return nil
}

// UIPlanDistributeDataMultiError is an error wrapping multiple validation
// errors returned by UIPlanDistributeData.ValidateAll() if the designated
// constraints aren't met.
type UIPlanDistributeDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UIPlanDistributeDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UIPlanDistributeDataMultiError) AllErrors() []error { return m }

// UIPlanDistributeDataValidationError is the validation error returned by
// UIPlanDistributeData.Validate if the designated constraints aren't met.
type UIPlanDistributeDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UIPlanDistributeDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UIPlanDistributeDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UIPlanDistributeDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UIPlanDistributeDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UIPlanDistributeDataValidationError) ErrorName() string {
	return "UIPlanDistributeDataValidationError"
}

// Error satisfies the builtin error interface
func (e UIPlanDistributeDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUIPlanDistributeData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UIPlanDistributeDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UIPlanDistributeDataValidationError{}

// Validate checks the field values on UISuiteDistributeData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UISuiteDistributeData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UISuiteDistributeData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UISuiteDistributeDataMultiError, or nil if none found.
func (m *UISuiteDistributeData) ValidateAll() error {
	return m.validate(true)
}

func (m *UISuiteDistributeData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for UiSuiteId

	// no validation rules for UiSuiteExecuteId

	// no validation rules for UiPlanId

	// no validation rules for UiPlanExecuteId

	// no validation rules for State

	if all {
		switch v := interface{}(m.GetUiSuite()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UISuiteDistributeDataValidationError{
					field:  "UiSuite",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UISuiteDistributeDataValidationError{
					field:  "UiSuite",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUiSuite()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UISuiteDistributeDataValidationError{
				field:  "UiSuite",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetUiCases() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, UISuiteDistributeDataValidationError{
						field:  fmt.Sprintf("UiCases[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, UISuiteDistributeDataValidationError{
						field:  fmt.Sprintf("UiCases[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return UISuiteDistributeDataValidationError{
					field:  fmt.Sprintf("UiCases[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetMetaData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UISuiteDistributeDataValidationError{
					field:  "MetaData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UISuiteDistributeDataValidationError{
					field:  "MetaData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMetaData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UISuiteDistributeDataValidationError{
				field:  "MetaData",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UISuiteDistributeDataMultiError(errors)
	}

	return nil
}

// UISuiteDistributeDataMultiError is an error wrapping multiple validation
// errors returned by UISuiteDistributeData.ValidateAll() if the designated
// constraints aren't met.
type UISuiteDistributeDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UISuiteDistributeDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UISuiteDistributeDataMultiError) AllErrors() []error { return m }

// UISuiteDistributeDataValidationError is the validation error returned by
// UISuiteDistributeData.Validate if the designated constraints aren't met.
type UISuiteDistributeDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UISuiteDistributeDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UISuiteDistributeDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UISuiteDistributeDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UISuiteDistributeDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UISuiteDistributeDataValidationError) ErrorName() string {
	return "UISuiteDistributeDataValidationError"
}

// Error satisfies the builtin error interface
func (e UISuiteDistributeDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUISuiteDistributeData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UISuiteDistributeDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UISuiteDistributeDataValidationError{}

// Validate checks the field values on ServiceDistributeData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ServiceDistributeData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ServiceDistributeData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ServiceDistributeDataMultiError, or nil if none found.
func (m *ServiceDistributeData) ValidateAll() error {
	return m.validate(true)
}

func (m *ServiceDistributeData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ServiceId

	// no validation rules for ServiceExecuteId

	// no validation rules for ServiceName

	// no validation rules for PlanId

	// no validation rules for PlanExecuteId

	if all {
		switch v := interface{}(m.GetService()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ServiceDistributeDataValidationError{
					field:  "Service",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ServiceDistributeDataValidationError{
					field:  "Service",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetService()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ServiceDistributeDataValidationError{
				field:  "Service",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetCases() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ServiceDistributeDataValidationError{
						field:  fmt.Sprintf("Cases[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ServiceDistributeDataValidationError{
						field:  fmt.Sprintf("Cases[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ServiceDistributeDataValidationError{
					field:  fmt.Sprintf("Cases[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetInterfaceCases() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ServiceDistributeDataValidationError{
						field:  fmt.Sprintf("InterfaceCases[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ServiceDistributeDataValidationError{
						field:  fmt.Sprintf("InterfaceCases[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ServiceDistributeDataValidationError{
					field:  fmt.Sprintf("InterfaceCases[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for PlanName

	if len(errors) > 0 {
		return ServiceDistributeDataMultiError(errors)
	}

	return nil
}

// ServiceDistributeDataMultiError is an error wrapping multiple validation
// errors returned by ServiceDistributeData.ValidateAll() if the designated
// constraints aren't met.
type ServiceDistributeDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ServiceDistributeDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ServiceDistributeDataMultiError) AllErrors() []error { return m }

// ServiceDistributeDataValidationError is the validation error returned by
// ServiceDistributeData.Validate if the designated constraints aren't met.
type ServiceDistributeDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ServiceDistributeDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ServiceDistributeDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ServiceDistributeDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ServiceDistributeDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ServiceDistributeDataValidationError) ErrorName() string {
	return "ServiceDistributeDataValidationError"
}

// Error satisfies the builtin error interface
func (e ServiceDistributeDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sServiceDistributeData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ServiceDistributeDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ServiceDistributeDataValidationError{}

// Validate checks the field values on PerfPlanDistributeData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *PerfPlanDistributeData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PerfPlanDistributeData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PerfPlanDistributeDataMultiError, or nil if none found.
func (m *PerfPlanDistributeData) ValidateAll() error {
	return m.validate(true)
}

func (m *PerfPlanDistributeData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for PerfPlanId

	// no validation rules for PerfPlanExecuteId

	// no validation rules for State

	if all {
		switch v := interface{}(m.GetPerfPlan()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PerfPlanDistributeDataValidationError{
					field:  "PerfPlan",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PerfPlanDistributeDataValidationError{
					field:  "PerfPlan",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPerfPlan()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PerfPlanDistributeDataValidationError{
				field:  "PerfPlan",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetPerfSuites() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, PerfPlanDistributeDataValidationError{
						field:  fmt.Sprintf("PerfSuites[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, PerfPlanDistributeDataValidationError{
						field:  fmt.Sprintf("PerfSuites[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return PerfPlanDistributeDataValidationError{
					field:  fmt.Sprintf("PerfSuites[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetPerfPlanInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PerfPlanDistributeDataValidationError{
					field:  "PerfPlanInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PerfPlanDistributeDataValidationError{
					field:  "PerfPlanInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPerfPlanInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PerfPlanDistributeDataValidationError{
				field:  "PerfPlanInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return PerfPlanDistributeDataMultiError(errors)
	}

	return nil
}

// PerfPlanDistributeDataMultiError is an error wrapping multiple validation
// errors returned by PerfPlanDistributeData.ValidateAll() if the designated
// constraints aren't met.
type PerfPlanDistributeDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PerfPlanDistributeDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PerfPlanDistributeDataMultiError) AllErrors() []error { return m }

// PerfPlanDistributeDataValidationError is the validation error returned by
// PerfPlanDistributeData.Validate if the designated constraints aren't met.
type PerfPlanDistributeDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PerfPlanDistributeDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PerfPlanDistributeDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PerfPlanDistributeDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PerfPlanDistributeDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PerfPlanDistributeDataValidationError) ErrorName() string {
	return "PerfPlanDistributeDataValidationError"
}

// Error satisfies the builtin error interface
func (e PerfPlanDistributeDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPerfPlanDistributeData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PerfPlanDistributeDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PerfPlanDistributeDataValidationError{}

// Validate checks the field values on PerfSuiteDistributeData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *PerfSuiteDistributeData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PerfSuiteDistributeData with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PerfSuiteDistributeDataMultiError, or nil if none found.
func (m *PerfSuiteDistributeData) ValidateAll() error {
	return m.validate(true)
}

func (m *PerfSuiteDistributeData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for PerfSuiteId

	// no validation rules for PerfSuiteExecuteId

	// no validation rules for PerfPlanId

	// no validation rules for PerfPlanExecuteId

	// no validation rules for State

	if all {
		switch v := interface{}(m.GetPerfSuite()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PerfSuiteDistributeDataValidationError{
					field:  "PerfSuite",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PerfSuiteDistributeDataValidationError{
					field:  "PerfSuite",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPerfSuite()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PerfSuiteDistributeDataValidationError{
				field:  "PerfSuite",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetPerfCases() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, PerfSuiteDistributeDataValidationError{
						field:  fmt.Sprintf("PerfCases[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, PerfSuiteDistributeDataValidationError{
						field:  fmt.Sprintf("PerfCases[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return PerfSuiteDistributeDataValidationError{
					field:  fmt.Sprintf("PerfCases[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetMetaData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PerfSuiteDistributeDataValidationError{
					field:  "MetaData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PerfSuiteDistributeDataValidationError{
					field:  "MetaData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMetaData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PerfSuiteDistributeDataValidationError{
				field:  "MetaData",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPerfPlanInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PerfSuiteDistributeDataValidationError{
					field:  "PerfPlanInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PerfSuiteDistributeDataValidationError{
					field:  "PerfPlanInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPerfPlanInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PerfSuiteDistributeDataValidationError{
				field:  "PerfPlanInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return PerfSuiteDistributeDataMultiError(errors)
	}

	return nil
}

// PerfSuiteDistributeDataMultiError is an error wrapping multiple validation
// errors returned by PerfSuiteDistributeData.ValidateAll() if the designated
// constraints aren't met.
type PerfSuiteDistributeDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PerfSuiteDistributeDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PerfSuiteDistributeDataMultiError) AllErrors() []error { return m }

// PerfSuiteDistributeDataValidationError is the validation error returned by
// PerfSuiteDistributeData.Validate if the designated constraints aren't met.
type PerfSuiteDistributeDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PerfSuiteDistributeDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PerfSuiteDistributeDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PerfSuiteDistributeDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PerfSuiteDistributeDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PerfSuiteDistributeDataValidationError) ErrorName() string {
	return "PerfSuiteDistributeDataValidationError"
}

// Error satisfies the builtin error interface
func (e PerfSuiteDistributeDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPerfSuiteDistributeData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PerfSuiteDistributeDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PerfSuiteDistributeDataValidationError{}
