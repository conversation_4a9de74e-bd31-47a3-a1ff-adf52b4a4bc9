// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: dispatcher/perfreport.proto

package pb

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on PerfReportCallback with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *PerfReportCallback) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PerfReportCallback with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PerfReportCallbackMultiError, or nil if none found.
func (m *PerfReportCallback) ValidateAll() error {
	return m.validate(true)
}

func (m *PerfReportCallback) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ProjectId

	// no validation rules for TaskId

	// no validation rules for PlanId

	// no validation rules for PlanExecuteId

	// no validation rules for Stage

	for idx, item := range m.GetCases() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, PerfReportCallbackValidationError{
						field:  fmt.Sprintf("Cases[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, PerfReportCallbackValidationError{
						field:  fmt.Sprintf("Cases[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return PerfReportCallbackValidationError{
					field:  fmt.Sprintf("Cases[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetServices() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, PerfReportCallbackValidationError{
						field:  fmt.Sprintf("Services[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, PerfReportCallbackValidationError{
						field:  fmt.Sprintf("Services[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return PerfReportCallbackValidationError{
					field:  fmt.Sprintf("Services[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return PerfReportCallbackMultiError(errors)
	}

	return nil
}

// PerfReportCallbackMultiError is an error wrapping multiple validation errors
// returned by PerfReportCallback.ValidateAll() if the designated constraints
// aren't met.
type PerfReportCallbackMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PerfReportCallbackMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PerfReportCallbackMultiError) AllErrors() []error { return m }

// PerfReportCallbackValidationError is the validation error returned by
// PerfReportCallback.Validate if the designated constraints aren't met.
type PerfReportCallbackValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PerfReportCallbackValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PerfReportCallbackValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PerfReportCallbackValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PerfReportCallbackValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PerfReportCallbackValidationError) ErrorName() string {
	return "PerfReportCallbackValidationError"
}

// Error satisfies the builtin error interface
func (e PerfReportCallbackValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPerfReportCallback.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PerfReportCallbackValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PerfReportCallbackValidationError{}

// Validate checks the field values on PerfReportCallback_PerfCase with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *PerfReportCallback_PerfCase) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PerfReportCallback_PerfCase with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PerfReportCallback_PerfCaseMultiError, or nil if none found.
func (m *PerfReportCallback_PerfCase) ValidateAll() error {
	return m.validate(true)
}

func (m *PerfReportCallback_PerfCase) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SuiteName

	// no validation rules for CaseName

	// no validation rules for TargetRps

	if len(errors) > 0 {
		return PerfReportCallback_PerfCaseMultiError(errors)
	}

	return nil
}

// PerfReportCallback_PerfCaseMultiError is an error wrapping multiple
// validation errors returned by PerfReportCallback_PerfCase.ValidateAll() if
// the designated constraints aren't met.
type PerfReportCallback_PerfCaseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PerfReportCallback_PerfCaseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PerfReportCallback_PerfCaseMultiError) AllErrors() []error { return m }

// PerfReportCallback_PerfCaseValidationError is the validation error returned
// by PerfReportCallback_PerfCase.Validate if the designated constraints
// aren't met.
type PerfReportCallback_PerfCaseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PerfReportCallback_PerfCaseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PerfReportCallback_PerfCaseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PerfReportCallback_PerfCaseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PerfReportCallback_PerfCaseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PerfReportCallback_PerfCaseValidationError) ErrorName() string {
	return "PerfReportCallback_PerfCaseValidationError"
}

// Error satisfies the builtin error interface
func (e PerfReportCallback_PerfCaseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPerfReportCallback_PerfCase.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PerfReportCallback_PerfCaseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PerfReportCallback_PerfCaseValidationError{}
