// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.1
// 	protoc        v5.29.2
// source: dispatcher/uiworker.proto

package pb

import (
	reflect "reflect"
	sync "sync"

	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"

	pb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
	pb1 "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type UITestTaskInfo struct {
	state     protoimpl.MessageState `protogen:"open.v1"`
	ProjectId string                 `protobuf:"bytes,1,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"` // 项目ID
	PlanId    string                 `protobuf:"bytes,2,opt,name=plan_id,json=planId,proto3" json:"plan_id,omitempty"`          // ui计划ID
	TaskId    string                 `protobuf:"bytes,3,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"`          // 任务ID
	// UI测试任务基础信息
	TestTarget          string           `protobuf:"bytes,11,opt,name=testTarget,proto3" json:"testTarget,omitempty"`                                                       // 测试目标，可以是任意级别的，例如集合/用例
	TestFrameworkUrl    string           `protobuf:"bytes,12,opt,name=test_framework_url,json=testFrameworkUrl,proto3" json:"test_framework_url,omitempty"`                 // 测试框架git地址，带token
	TestFrameworkBranch string           `protobuf:"bytes,13,opt,name=test_framework_branch,json=testFrameworkBranch,proto3" json:"test_framework_branch,omitempty"`        // 测试框架分支名称
	DeviceType          pb.DeviceType    `protobuf:"varint,14,opt,name=device_type,json=deviceType,proto3,enum=common.DeviceType" json:"device_type,omitempty"`             // 设备类型（真机、云手机）
	PlatformType        pb.PlatformType  `protobuf:"varint,15,opt,name=platform_type,json=platformType,proto3,enum=common.PlatformType" json:"platform_type,omitempty"`     // 平台类型（Android、iOS）
	AppDownloadLink     string           `protobuf:"bytes,16,opt,name=app_download_link,json=appDownloadLink,proto3" json:"app_download_link,omitempty"`                    // APP下载地址
	TestLanguage        pb.TestLanguage  `protobuf:"varint,17,opt,name=test_language,json=testLanguage,proto3,enum=common.TestLanguage" json:"test_language,omitempty"`     // 测试语言
	TestLanguageVersion string           `protobuf:"bytes,18,opt,name=test_language_version,json=testLanguageVersion,proto3" json:"test_language_version,omitempty"`        // 测试语言版本
	TestFramework       pb.TestFramework `protobuf:"varint,19,opt,name=test_framework,json=testFramework,proto3,enum=common.TestFramework" json:"test_framework,omitempty"` // 测试框架
	TestArgs            []string         `protobuf:"bytes,20,rep,name=test_args,json=testArgs,proto3" json:"test_args,omitempty"`                                           // 附加参数
	Devices             []string         `protobuf:"bytes,21,rep,name=devices,proto3" json:"devices,omitempty"`                                                             // 选择的设备编号
	PackageName         string           `protobuf:"bytes,22,opt,name=package_name,json=packageName,proto3" json:"package_name,omitempty"`                                  // 包名，用于启动app
	AppVersion          string           `protobuf:"bytes,23,opt,name=app_version,json=appVersion,proto3" json:"app_version,omitempty"`                                     // APP版本
	// 回调所需参数
	TriggerMode  pb.TriggerMode           `protobuf:"varint,31,opt,name=trigger_mode,json=triggerMode,proto3,enum=common.TriggerMode" json:"trigger_mode,omitempty"`
	TriggerRule  string                   `protobuf:"bytes,32,opt,name=trigger_rule,json=triggerRule,proto3" json:"trigger_rule,omitempty"`
	ExecuteType  pb1.ApiExecutionDataType `protobuf:"varint,33,opt,name=execute_type,json=executeType,proto3,enum=manager.ApiExecutionDataType" json:"execute_type,omitempty"`
	CallbackType CallbackType             `protobuf:"varint,34,opt,name=callback_type,json=callbackType,proto3,enum=dispatcher.CallbackType" json:"callback_type,omitempty"`
	UiCase       *UICaseWorkerInfo        `protobuf:"bytes,35,opt,name=ui_case,json=uiCase,proto3" json:"ui_case,omitempty"`
	// 任务执行参数
	PriorityType pb.PriorityType `protobuf:"varint,41,opt,name=priority_type,json=priorityType,proto3,enum=common.PriorityType" json:"priority_type,omitempty"` // 优先级
	// 其它参数
	ExecutedBy    string `protobuf:"bytes,51,opt,name=executed_by,json=executedBy,proto3" json:"executed_by,omitempty"` // 执行人的用户ID
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UITestTaskInfo) Reset() {
	*x = UITestTaskInfo{}
	mi := &file_dispatcher_uiworker_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UITestTaskInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UITestTaskInfo) ProtoMessage() {}

func (x *UITestTaskInfo) ProtoReflect() protoreflect.Message {
	mi := &file_dispatcher_uiworker_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UITestTaskInfo.ProtoReflect.Descriptor instead.
func (*UITestTaskInfo) Descriptor() ([]byte, []int) {
	return file_dispatcher_uiworker_proto_rawDescGZIP(), []int{0}
}

func (x *UITestTaskInfo) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *UITestTaskInfo) GetPlanId() string {
	if x != nil {
		return x.PlanId
	}
	return ""
}

func (x *UITestTaskInfo) GetTaskId() string {
	if x != nil {
		return x.TaskId
	}
	return ""
}

func (x *UITestTaskInfo) GetTestTarget() string {
	if x != nil {
		return x.TestTarget
	}
	return ""
}

func (x *UITestTaskInfo) GetTestFrameworkUrl() string {
	if x != nil {
		return x.TestFrameworkUrl
	}
	return ""
}

func (x *UITestTaskInfo) GetTestFrameworkBranch() string {
	if x != nil {
		return x.TestFrameworkBranch
	}
	return ""
}

func (x *UITestTaskInfo) GetDeviceType() pb.DeviceType {
	if x != nil {
		return x.DeviceType
	}
	return pb.DeviceType(0)
}

func (x *UITestTaskInfo) GetPlatformType() pb.PlatformType {
	if x != nil {
		return x.PlatformType
	}
	return pb.PlatformType(0)
}

func (x *UITestTaskInfo) GetAppDownloadLink() string {
	if x != nil {
		return x.AppDownloadLink
	}
	return ""
}

func (x *UITestTaskInfo) GetTestLanguage() pb.TestLanguage {
	if x != nil {
		return x.TestLanguage
	}
	return pb.TestLanguage(0)
}

func (x *UITestTaskInfo) GetTestLanguageVersion() string {
	if x != nil {
		return x.TestLanguageVersion
	}
	return ""
}

func (x *UITestTaskInfo) GetTestFramework() pb.TestFramework {
	if x != nil {
		return x.TestFramework
	}
	return pb.TestFramework(0)
}

func (x *UITestTaskInfo) GetTestArgs() []string {
	if x != nil {
		return x.TestArgs
	}
	return nil
}

func (x *UITestTaskInfo) GetDevices() []string {
	if x != nil {
		return x.Devices
	}
	return nil
}

func (x *UITestTaskInfo) GetPackageName() string {
	if x != nil {
		return x.PackageName
	}
	return ""
}

func (x *UITestTaskInfo) GetAppVersion() string {
	if x != nil {
		return x.AppVersion
	}
	return ""
}

func (x *UITestTaskInfo) GetTriggerMode() pb.TriggerMode {
	if x != nil {
		return x.TriggerMode
	}
	return pb.TriggerMode(0)
}

func (x *UITestTaskInfo) GetTriggerRule() string {
	if x != nil {
		return x.TriggerRule
	}
	return ""
}

func (x *UITestTaskInfo) GetExecuteType() pb1.ApiExecutionDataType {
	if x != nil {
		return x.ExecuteType
	}
	return pb1.ApiExecutionDataType(0)
}

func (x *UITestTaskInfo) GetCallbackType() CallbackType {
	if x != nil {
		return x.CallbackType
	}
	return CallbackType_CallbackType_UNKNOWN
}

func (x *UITestTaskInfo) GetUiCase() *UICaseWorkerInfo {
	if x != nil {
		return x.UiCase
	}
	return nil
}

func (x *UITestTaskInfo) GetPriorityType() pb.PriorityType {
	if x != nil {
		return x.PriorityType
	}
	return pb.PriorityType(0)
}

func (x *UITestTaskInfo) GetExecutedBy() string {
	if x != nil {
		return x.ExecutedBy
	}
	return ""
}

// 理应有报告类型，例如Allure，没空加~
type GenerateReportTaskInfo struct {
	state     protoimpl.MessageState `protogen:"open.v1"`
	ProjectId string                 `protobuf:"bytes,1,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"` // 项目ID
	PlanId    string                 `protobuf:"bytes,2,opt,name=plan_id,json=planId,proto3" json:"plan_id,omitempty"`          // ui计划ID
	TaskId    string                 `protobuf:"bytes,3,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"`          // 任务ID
	// 回调所需参数
	UiCase        *UICaseWorkerInfo `protobuf:"bytes,4,opt,name=ui_case,json=uiCase,proto3" json:"ui_case,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GenerateReportTaskInfo) Reset() {
	*x = GenerateReportTaskInfo{}
	mi := &file_dispatcher_uiworker_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GenerateReportTaskInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GenerateReportTaskInfo) ProtoMessage() {}

func (x *GenerateReportTaskInfo) ProtoReflect() protoreflect.Message {
	mi := &file_dispatcher_uiworker_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GenerateReportTaskInfo.ProtoReflect.Descriptor instead.
func (*GenerateReportTaskInfo) Descriptor() ([]byte, []int) {
	return file_dispatcher_uiworker_proto_rawDescGZIP(), []int{1}
}

func (x *GenerateReportTaskInfo) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *GenerateReportTaskInfo) GetPlanId() string {
	if x != nil {
		return x.PlanId
	}
	return ""
}

func (x *GenerateReportTaskInfo) GetTaskId() string {
	if x != nil {
		return x.TaskId
	}
	return ""
}

func (x *GenerateReportTaskInfo) GetUiCase() *UICaseWorkerInfo {
	if x != nil {
		return x.UiCase
	}
	return nil
}

var File_dispatcher_uiworker_proto protoreflect.FileDescriptor

var file_dispatcher_uiworker_proto_rawDesc = []byte{
	0x0a, 0x19, 0x64, 0x69, 0x73, 0x70, 0x61, 0x74, 0x63, 0x68, 0x65, 0x72, 0x2f, 0x75, 0x69, 0x77,
	0x6f, 0x72, 0x6b, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0a, 0x64, 0x69, 0x73,
	0x70, 0x61, 0x74, 0x63, 0x68, 0x65, 0x72, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74,
	0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x11, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x12, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2f, 0x62, 0x61, 0x73,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x19, 0x64, 0x69, 0x73, 0x70, 0x61, 0x74, 0x63,
	0x68, 0x65, 0x72, 0x2f, 0x63, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x17, 0x64, 0x69, 0x73, 0x70, 0x61, 0x74, 0x63, 0x68, 0x65, 0x72, 0x2f, 0x77,
	0x6f, 0x72, 0x6b, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xe6, 0x09, 0x0a, 0x0e,
	0x55, 0x49, 0x54, 0x65, 0x73, 0x74, 0x54, 0x61, 0x73, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x39,
	0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x1a, 0xfa, 0x42, 0x17, 0x72, 0x15, 0x32, 0x13, 0x28, 0x3f, 0x3a, 0x5e, 0x70,
	0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x3a, 0x2e, 0x2b, 0x3f, 0x29, 0x52, 0x09,
	0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x12, 0x33, 0x0a, 0x07, 0x70, 0x6c, 0x61,
	0x6e, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x1a, 0xfa, 0x42, 0x17, 0x72,
	0x15, 0x32, 0x13, 0x28, 0x3f, 0x3a, 0x5e, 0x75, 0x69, 0x5f, 0x70, 0x6c, 0x61, 0x6e, 0x5f, 0x69,
	0x64, 0x3a, 0x2e, 0x2b, 0x3f, 0x29, 0x52, 0x06, 0x70, 0x6c, 0x61, 0x6e, 0x49, 0x64, 0x12, 0x30,
	0x0a, 0x07, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x17, 0xfa, 0x42, 0x14, 0x72, 0x12, 0x32, 0x10, 0x28, 0x3f, 0x3a, 0x5e, 0x74, 0x61, 0x73, 0x6b,
	0x5f, 0x69, 0x64, 0x3a, 0x2e, 0x2b, 0x3f, 0x29, 0x52, 0x06, 0x74, 0x61, 0x73, 0x6b, 0x49, 0x64,
	0x12, 0x1e, 0x0a, 0x0a, 0x74, 0x65, 0x73, 0x74, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x18, 0x0b,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x74, 0x65, 0x73, 0x74, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74,
	0x12, 0x36, 0x0a, 0x12, 0x74, 0x65, 0x73, 0x74, 0x5f, 0x66, 0x72, 0x61, 0x6d, 0x65, 0x77, 0x6f,
	0x72, 0x6b, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x42, 0x08, 0xfa, 0x42,
	0x05, 0x72, 0x03, 0x18, 0xff, 0x01, 0x52, 0x10, 0x74, 0x65, 0x73, 0x74, 0x46, 0x72, 0x61, 0x6d,
	0x65, 0x77, 0x6f, 0x72, 0x6b, 0x55, 0x72, 0x6c, 0x12, 0x3c, 0x0a, 0x15, 0x74, 0x65, 0x73, 0x74,
	0x5f, 0x66, 0x72, 0x61, 0x6d, 0x65, 0x77, 0x6f, 0x72, 0x6b, 0x5f, 0x62, 0x72, 0x61, 0x6e, 0x63,
	0x68, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x72, 0x03, 0x18, 0xff,
	0x01, 0x52, 0x13, 0x74, 0x65, 0x73, 0x74, 0x46, 0x72, 0x61, 0x6d, 0x65, 0x77, 0x6f, 0x72, 0x6b,
	0x42, 0x72, 0x61, 0x6e, 0x63, 0x68, 0x12, 0x3d, 0x0a, 0x0b, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x12, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x42,
	0x08, 0xfa, 0x42, 0x05, 0x82, 0x01, 0x02, 0x10, 0x01, 0x52, 0x0a, 0x64, 0x65, 0x76, 0x69, 0x63,
	0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x43, 0x0a, 0x0d, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72,
	0x6d, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x54, 0x79,
	0x70, 0x65, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x82, 0x01, 0x02, 0x10, 0x01, 0x52, 0x0c, 0x70, 0x6c,
	0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x12, 0x37, 0x0a, 0x11, 0x61, 0x70,
	0x70, 0x5f, 0x64, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x5f, 0x6c, 0x69, 0x6e, 0x6b, 0x18,
	0x10, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0b, 0xfa, 0x42, 0x08, 0x72, 0x06, 0x18, 0xff, 0x01, 0xd0,
	0x01, 0x01, 0x52, 0x0f, 0x61, 0x70, 0x70, 0x44, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x4c,
	0x69, 0x6e, 0x6b, 0x12, 0x45, 0x0a, 0x0d, 0x74, 0x65, 0x73, 0x74, 0x5f, 0x6c, 0x61, 0x6e, 0x67,
	0x75, 0x61, 0x67, 0x65, 0x18, 0x11, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x4c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65,
	0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82, 0x01, 0x04, 0x18, 0x01, 0x18, 0x02, 0x52, 0x0c, 0x74, 0x65,
	0x73, 0x74, 0x4c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x12, 0x3b, 0x0a, 0x15, 0x74, 0x65,
	0x73, 0x74, 0x5f, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x5f, 0x76, 0x65, 0x72, 0x73,
	0x69, 0x6f, 0x6e, 0x18, 0x12, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02,
	0x18, 0x32, 0x52, 0x13, 0x74, 0x65, 0x73, 0x74, 0x4c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65,
	0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x46, 0x0a, 0x0e, 0x74, 0x65, 0x73, 0x74, 0x5f,
	0x66, 0x72, 0x61, 0x6d, 0x65, 0x77, 0x6f, 0x72, 0x6b, 0x18, 0x13, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x15, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x46, 0x72, 0x61,
	0x6d, 0x65, 0x77, 0x6f, 0x72, 0x6b, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x82, 0x01, 0x02, 0x18, 0x01,
	0x52, 0x0d, 0x74, 0x65, 0x73, 0x74, 0x46, 0x72, 0x61, 0x6d, 0x65, 0x77, 0x6f, 0x72, 0x6b, 0x12,
	0x25, 0x0a, 0x09, 0x74, 0x65, 0x73, 0x74, 0x5f, 0x61, 0x72, 0x67, 0x73, 0x18, 0x14, 0x20, 0x03,
	0x28, 0x09, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x92, 0x01, 0x02, 0x28, 0x01, 0x52, 0x08, 0x74, 0x65,
	0x73, 0x74, 0x41, 0x72, 0x67, 0x73, 0x12, 0x22, 0x0a, 0x07, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65,
	0x73, 0x18, 0x15, 0x20, 0x03, 0x28, 0x09, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x92, 0x01, 0x02, 0x28,
	0x01, 0x52, 0x07, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x73, 0x12, 0x2b, 0x0a, 0x0c, 0x70, 0x61,
	0x63, 0x6b, 0x61, 0x67, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x16, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x08, 0xfa, 0x42, 0x05, 0x72, 0x03, 0x18, 0xff, 0x01, 0x52, 0x0b, 0x70, 0x61, 0x63, 0x6b,
	0x61, 0x67, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x2c, 0x0a, 0x0b, 0x61, 0x70, 0x70, 0x5f, 0x76,
	0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x17, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0b, 0xfa, 0x42,
	0x08, 0x72, 0x06, 0x18, 0xff, 0x01, 0xd0, 0x01, 0x01, 0x52, 0x0a, 0x61, 0x70, 0x70, 0x56, 0x65,
	0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x36, 0x0a, 0x0c, 0x74, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72,
	0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x18, 0x1f, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x13, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x4d, 0x6f, 0x64, 0x65,
	0x52, 0x0b, 0x74, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x4d, 0x6f, 0x64, 0x65, 0x12, 0x21, 0x0a,
	0x0c, 0x74, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x5f, 0x72, 0x75, 0x6c, 0x65, 0x18, 0x20, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0b, 0x74, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x52, 0x75, 0x6c, 0x65,
	0x12, 0x40, 0x0a, 0x0c, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x21, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1d, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72,
	0x2e, 0x41, 0x70, 0x69, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74,
	0x61, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0b, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x3d, 0x0a, 0x0d, 0x63, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x22, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x18, 0x2e, 0x64, 0x69, 0x73, 0x70,
	0x61, 0x74, 0x63, 0x68, 0x65, 0x72, 0x2e, 0x43, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x54,
	0x79, 0x70, 0x65, 0x52, 0x0c, 0x63, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x35, 0x0a, 0x07, 0x75, 0x69, 0x5f, 0x63, 0x61, 0x73, 0x65, 0x18, 0x23, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x64, 0x69, 0x73, 0x70, 0x61, 0x74, 0x63, 0x68, 0x65, 0x72, 0x2e,
	0x55, 0x49, 0x43, 0x61, 0x73, 0x65, 0x57, 0x6f, 0x72, 0x6b, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f,
	0x52, 0x06, 0x75, 0x69, 0x43, 0x61, 0x73, 0x65, 0x12, 0x39, 0x0a, 0x0d, 0x70, 0x72, 0x69, 0x6f,
	0x72, 0x69, 0x74, 0x79, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x29, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x14, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x72, 0x69, 0x6f, 0x72, 0x69, 0x74,
	0x79, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0c, 0x70, 0x72, 0x69, 0x6f, 0x72, 0x69, 0x74, 0x79, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x64, 0x5f,
	0x62, 0x79, 0x18, 0x33, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74,
	0x65, 0x64, 0x42, 0x79, 0x22, 0xf1, 0x01, 0x0a, 0x16, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74,
	0x65, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x54, 0x61, 0x73, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x12,
	0x39, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x1a, 0xfa, 0x42, 0x17, 0x72, 0x15, 0x32, 0x13, 0x28, 0x3f, 0x3a, 0x5e,
	0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x3a, 0x2e, 0x2b, 0x3f, 0x29, 0x52,
	0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x12, 0x33, 0x0a, 0x07, 0x70, 0x6c,
	0x61, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x1a, 0xfa, 0x42, 0x17,
	0x72, 0x15, 0x32, 0x13, 0x28, 0x3f, 0x3a, 0x5e, 0x75, 0x69, 0x5f, 0x70, 0x6c, 0x61, 0x6e, 0x5f,
	0x69, 0x64, 0x3a, 0x2e, 0x2b, 0x3f, 0x29, 0x52, 0x06, 0x70, 0x6c, 0x61, 0x6e, 0x49, 0x64, 0x12,
	0x30, 0x0a, 0x07, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x17, 0xfa, 0x42, 0x14, 0x72, 0x12, 0x32, 0x10, 0x28, 0x3f, 0x3a, 0x5e, 0x74, 0x61, 0x73,
	0x6b, 0x5f, 0x69, 0x64, 0x3a, 0x2e, 0x2b, 0x3f, 0x29, 0x52, 0x06, 0x74, 0x61, 0x73, 0x6b, 0x49,
	0x64, 0x12, 0x35, 0x0a, 0x07, 0x75, 0x69, 0x5f, 0x63, 0x61, 0x73, 0x65, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x64, 0x69, 0x73, 0x70, 0x61, 0x74, 0x63, 0x68, 0x65, 0x72, 0x2e,
	0x55, 0x49, 0x43, 0x61, 0x73, 0x65, 0x57, 0x6f, 0x72, 0x6b, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f,
	0x52, 0x06, 0x75, 0x69, 0x43, 0x61, 0x73, 0x65, 0x42, 0x44, 0x5a, 0x42, 0x67, 0x69, 0x74, 0x6c,
	0x61, 0x62, 0x2e, 0x74, 0x74, 0x79, 0x75, 0x79, 0x69, 0x6e, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x54,
	0x65, 0x73, 0x74, 0x44, 0x65, 0x76, 0x65, 0x6c, 0x6f, 0x70, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x70,
	0x72, 0x6f, 0x62, 0x65, 0x2d, 0x62, 0x61, 0x63, 0x6b, 0x65, 0x6e, 0x64, 0x2f, 0x64, 0x69, 0x73,
	0x70, 0x61, 0x74, 0x63, 0x68, 0x65, 0x72, 0x2f, 0x72, 0x70, 0x63, 0x2f, 0x70, 0x62, 0x62, 0x06,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_dispatcher_uiworker_proto_rawDescOnce sync.Once
	file_dispatcher_uiworker_proto_rawDescData = file_dispatcher_uiworker_proto_rawDesc
)

func file_dispatcher_uiworker_proto_rawDescGZIP() []byte {
	file_dispatcher_uiworker_proto_rawDescOnce.Do(func() {
		file_dispatcher_uiworker_proto_rawDescData = protoimpl.X.CompressGZIP(file_dispatcher_uiworker_proto_rawDescData)
	})
	return file_dispatcher_uiworker_proto_rawDescData
}

var file_dispatcher_uiworker_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_dispatcher_uiworker_proto_goTypes = []any{
	(*UITestTaskInfo)(nil),         // 0: dispatcher.UITestTaskInfo
	(*GenerateReportTaskInfo)(nil), // 1: dispatcher.GenerateReportTaskInfo
	(pb.DeviceType)(0),             // 2: common.DeviceType
	(pb.PlatformType)(0),           // 3: common.PlatformType
	(pb.TestLanguage)(0),           // 4: common.TestLanguage
	(pb.TestFramework)(0),          // 5: common.TestFramework
	(pb.TriggerMode)(0),            // 6: common.TriggerMode
	(pb1.ApiExecutionDataType)(0),  // 7: manager.ApiExecutionDataType
	(CallbackType)(0),              // 8: dispatcher.CallbackType
	(*UICaseWorkerInfo)(nil),       // 9: dispatcher.UICaseWorkerInfo
	(pb.PriorityType)(0),           // 10: common.PriorityType
}
var file_dispatcher_uiworker_proto_depIdxs = []int32{
	2,  // 0: dispatcher.UITestTaskInfo.device_type:type_name -> common.DeviceType
	3,  // 1: dispatcher.UITestTaskInfo.platform_type:type_name -> common.PlatformType
	4,  // 2: dispatcher.UITestTaskInfo.test_language:type_name -> common.TestLanguage
	5,  // 3: dispatcher.UITestTaskInfo.test_framework:type_name -> common.TestFramework
	6,  // 4: dispatcher.UITestTaskInfo.trigger_mode:type_name -> common.TriggerMode
	7,  // 5: dispatcher.UITestTaskInfo.execute_type:type_name -> manager.ApiExecutionDataType
	8,  // 6: dispatcher.UITestTaskInfo.callback_type:type_name -> dispatcher.CallbackType
	9,  // 7: dispatcher.UITestTaskInfo.ui_case:type_name -> dispatcher.UICaseWorkerInfo
	10, // 8: dispatcher.UITestTaskInfo.priority_type:type_name -> common.PriorityType
	9,  // 9: dispatcher.GenerateReportTaskInfo.ui_case:type_name -> dispatcher.UICaseWorkerInfo
	10, // [10:10] is the sub-list for method output_type
	10, // [10:10] is the sub-list for method input_type
	10, // [10:10] is the sub-list for extension type_name
	10, // [10:10] is the sub-list for extension extendee
	0,  // [0:10] is the sub-list for field type_name
}

func init() { file_dispatcher_uiworker_proto_init() }
func file_dispatcher_uiworker_proto_init() {
	if File_dispatcher_uiworker_proto != nil {
		return
	}
	file_dispatcher_callback_proto_init()
	file_dispatcher_worker_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_dispatcher_uiworker_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_dispatcher_uiworker_proto_goTypes,
		DependencyIndexes: file_dispatcher_uiworker_proto_depIdxs,
		MessageInfos:      file_dispatcher_uiworker_proto_msgTypes,
	}.Build()
	File_dispatcher_uiworker_proto = out.File
	file_dispatcher_uiworker_proto_rawDesc = nil
	file_dispatcher_uiworker_proto_goTypes = nil
	file_dispatcher_uiworker_proto_depIdxs = nil
}
