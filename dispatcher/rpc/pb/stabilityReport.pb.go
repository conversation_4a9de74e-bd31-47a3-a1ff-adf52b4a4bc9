// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.1
// 	protoc        v5.29.2
// source: dispatcher/stabilityReport.proto

package pb

import (
	reflect "reflect"
	sync "sync"

	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type StabilityReportCallback struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ProjectId     string                 `protobuf:"bytes,1,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"` // 项目ID
	TaskId        string                 `protobuf:"bytes,2,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"`          // 任务ID
	ExecuteId     string                 `protobuf:"bytes,3,opt,name=execute_id,json=executeId,proto3" json:"execute_id,omitempty"` // 执行ID
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *StabilityReportCallback) Reset() {
	*x = StabilityReportCallback{}
	mi := &file_dispatcher_stabilityReport_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StabilityReportCallback) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StabilityReportCallback) ProtoMessage() {}

func (x *StabilityReportCallback) ProtoReflect() protoreflect.Message {
	mi := &file_dispatcher_stabilityReport_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StabilityReportCallback.ProtoReflect.Descriptor instead.
func (*StabilityReportCallback) Descriptor() ([]byte, []int) {
	return file_dispatcher_stabilityReport_proto_rawDescGZIP(), []int{0}
}

func (x *StabilityReportCallback) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *StabilityReportCallback) GetTaskId() string {
	if x != nil {
		return x.TaskId
	}
	return ""
}

func (x *StabilityReportCallback) GetExecuteId() string {
	if x != nil {
		return x.ExecuteId
	}
	return ""
}

var File_dispatcher_stabilityReport_proto protoreflect.FileDescriptor

var file_dispatcher_stabilityReport_proto_rawDesc = []byte{
	0x0a, 0x20, 0x64, 0x69, 0x73, 0x70, 0x61, 0x74, 0x63, 0x68, 0x65, 0x72, 0x2f, 0x73, 0x74, 0x61,
	0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x12, 0x0a, 0x64, 0x69, 0x73, 0x70, 0x61, 0x74, 0x63, 0x68, 0x65, 0x72, 0x1a, 0x17,
	0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xc5, 0x01, 0x0a, 0x17, 0x53, 0x74, 0x61, 0x62,
	0x69, 0x6c, 0x69, 0x74, 0x79, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x43, 0x61, 0x6c, 0x6c, 0x62,
	0x61, 0x63, 0x6b, 0x12, 0x3d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x1e, 0xfa, 0x42, 0x1b, 0x72, 0x19, 0x32, 0x17,
	0x28, 0x3f, 0x3a, 0x5e, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x3a, 0x2e,
	0x2b, 0x3f, 0x7c, 0x5e, 0x31, 0x24, 0x29, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74,
	0x49, 0x64, 0x12, 0x30, 0x0a, 0x07, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x17, 0xfa, 0x42, 0x14, 0x72, 0x12, 0x32, 0x10, 0x28, 0x3f, 0x3a, 0x5e,
	0x74, 0x61, 0x73, 0x6b, 0x5f, 0x69, 0x64, 0x3a, 0x2e, 0x2b, 0x3f, 0x29, 0x52, 0x06, 0x74, 0x61,
	0x73, 0x6b, 0x49, 0x64, 0x12, 0x39, 0x0a, 0x0a, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x5f,
	0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x1a, 0xfa, 0x42, 0x17, 0x72, 0x15, 0x32,
	0x13, 0x28, 0x3f, 0x3a, 0x5e, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x5f, 0x69, 0x64, 0x3a,
	0x2e, 0x2b, 0x3f, 0x29, 0x52, 0x09, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x49, 0x64, 0x42,
	0x44, 0x5a, 0x42, 0x67, 0x69, 0x74, 0x6c, 0x61, 0x62, 0x2e, 0x74, 0x74, 0x79, 0x75, 0x79, 0x69,
	0x6e, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x54, 0x65, 0x73, 0x74, 0x44, 0x65, 0x76, 0x65, 0x6c, 0x6f,
	0x70, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x70, 0x72, 0x6f, 0x62, 0x65, 0x2d, 0x62, 0x61, 0x63, 0x6b,
	0x65, 0x6e, 0x64, 0x2f, 0x64, 0x69, 0x73, 0x70, 0x61, 0x74, 0x63, 0x68, 0x65, 0x72, 0x2f, 0x72,
	0x70, 0x63, 0x2f, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_dispatcher_stabilityReport_proto_rawDescOnce sync.Once
	file_dispatcher_stabilityReport_proto_rawDescData = file_dispatcher_stabilityReport_proto_rawDesc
)

func file_dispatcher_stabilityReport_proto_rawDescGZIP() []byte {
	file_dispatcher_stabilityReport_proto_rawDescOnce.Do(func() {
		file_dispatcher_stabilityReport_proto_rawDescData = protoimpl.X.CompressGZIP(file_dispatcher_stabilityReport_proto_rawDescData)
	})
	return file_dispatcher_stabilityReport_proto_rawDescData
}

var file_dispatcher_stabilityReport_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_dispatcher_stabilityReport_proto_goTypes = []any{
	(*StabilityReportCallback)(nil), // 0: dispatcher.StabilityReportCallback
}
var file_dispatcher_stabilityReport_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_dispatcher_stabilityReport_proto_init() }
func file_dispatcher_stabilityReport_proto_init() {
	if File_dispatcher_stabilityReport_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_dispatcher_stabilityReport_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_dispatcher_stabilityReport_proto_goTypes,
		DependencyIndexes: file_dispatcher_stabilityReport_proto_depIdxs,
		MessageInfos:      file_dispatcher_stabilityReport_proto_msgTypes,
	}.Build()
	File_dispatcher_stabilityReport_proto = out.File
	file_dispatcher_stabilityReport_proto_rawDesc = nil
	file_dispatcher_stabilityReport_proto_goTypes = nil
	file_dispatcher_stabilityReport_proto_depIdxs = nil
}
