// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: dispatcher/stabilityReport.proto

package pb

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on StabilityReportCallback with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *StabilityReportCallback) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on StabilityReportCallback with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// StabilityReportCallbackMultiError, or nil if none found.
func (m *StabilityReportCallback) ValidateAll() error {
	return m.validate(true)
}

func (m *StabilityReportCallback) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if !_StabilityReportCallback_ProjectId_Pattern.MatchString(m.GetProjectId()) {
		err := StabilityReportCallbackValidationError{
			field:  "ProjectId",
			reason: "value does not match regex pattern \"(?:^project_id:.+?|^1$)\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_StabilityReportCallback_TaskId_Pattern.MatchString(m.GetTaskId()) {
		err := StabilityReportCallbackValidationError{
			field:  "TaskId",
			reason: "value does not match regex pattern \"(?:^task_id:.+?)\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_StabilityReportCallback_ExecuteId_Pattern.MatchString(m.GetExecuteId()) {
		err := StabilityReportCallbackValidationError{
			field:  "ExecuteId",
			reason: "value does not match regex pattern \"(?:^execute_id:.+?)\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return StabilityReportCallbackMultiError(errors)
	}

	return nil
}

// StabilityReportCallbackMultiError is an error wrapping multiple validation
// errors returned by StabilityReportCallback.ValidateAll() if the designated
// constraints aren't met.
type StabilityReportCallbackMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m StabilityReportCallbackMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m StabilityReportCallbackMultiError) AllErrors() []error { return m }

// StabilityReportCallbackValidationError is the validation error returned by
// StabilityReportCallback.Validate if the designated constraints aren't met.
type StabilityReportCallbackValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e StabilityReportCallbackValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e StabilityReportCallbackValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e StabilityReportCallbackValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e StabilityReportCallbackValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e StabilityReportCallbackValidationError) ErrorName() string {
	return "StabilityReportCallbackValidationError"
}

// Error satisfies the builtin error interface
func (e StabilityReportCallbackValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sStabilityReportCallback.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = StabilityReportCallbackValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = StabilityReportCallbackValidationError{}

var _StabilityReportCallback_ProjectId_Pattern = regexp.MustCompile("(?:^project_id:.+?|^1$)")

var _StabilityReportCallback_TaskId_Pattern = regexp.MustCompile("(?:^task_id:.+?)")

var _StabilityReportCallback_ExecuteId_Pattern = regexp.MustCompile("(?:^execute_id:.+?)")
