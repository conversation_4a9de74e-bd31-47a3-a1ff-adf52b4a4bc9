// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: dispatcher/callback.proto

package pb

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	pb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"

	pb1 "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = pb.TriggerMode(0)

	_ = pb1.ApiExecutionDataType(0)
)

// Validate checks the field values on CallbackReq with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *CallbackReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CallbackReq with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in CallbackReqMultiError, or
// nil if none found.
func (m *CallbackReq) ValidateAll() error {
	return m.validate(true)
}

func (m *CallbackReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TriggerMode

	// no validation rules for TriggerRule

	// no validation rules for ProjectId

	// no validation rules for TaskId

	// no validation rules for ExecuteType

	// no validation rules for CallbackType

	// no validation rules for PurposeType

	// no validation rules for Debug

	switch v := m.Data.(type) {
	case *CallbackReq_Case:
		if v == nil {
			err := CallbackReqValidationError{
				field:  "Data",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetCase()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CallbackReqValidationError{
						field:  "Case",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CallbackReqValidationError{
						field:  "Case",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetCase()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CallbackReqValidationError{
					field:  "Case",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *CallbackReq_Suite:
		if v == nil {
			err := CallbackReqValidationError{
				field:  "Data",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetSuite()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CallbackReqValidationError{
						field:  "Suite",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CallbackReqValidationError{
						field:  "Suite",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetSuite()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CallbackReqValidationError{
					field:  "Suite",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *CallbackReq_InterfaceCase:
		if v == nil {
			err := CallbackReqValidationError{
				field:  "Data",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetInterfaceCase()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CallbackReqValidationError{
						field:  "InterfaceCase",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CallbackReqValidationError{
						field:  "InterfaceCase",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetInterfaceCase()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CallbackReqValidationError{
					field:  "InterfaceCase",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *CallbackReq_InterfaceDocument:
		if v == nil {
			err := CallbackReqValidationError{
				field:  "Data",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetInterfaceDocument()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CallbackReqValidationError{
						field:  "InterfaceDocument",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CallbackReqValidationError{
						field:  "InterfaceDocument",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetInterfaceDocument()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CallbackReqValidationError{
					field:  "InterfaceDocument",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *CallbackReq_UiCase:
		if v == nil {
			err := CallbackReqValidationError{
				field:  "Data",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetUiCase()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CallbackReqValidationError{
						field:  "UiCase",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CallbackReqValidationError{
						field:  "UiCase",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetUiCase()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CallbackReqValidationError{
					field:  "UiCase",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *CallbackReq_UiSuite:
		if v == nil {
			err := CallbackReqValidationError{
				field:  "Data",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetUiSuite()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CallbackReqValidationError{
						field:  "UiSuite",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CallbackReqValidationError{
						field:  "UiSuite",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetUiSuite()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CallbackReqValidationError{
					field:  "UiSuite",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *CallbackReq_Service:
		if v == nil {
			err := CallbackReqValidationError{
				field:  "Data",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetService()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CallbackReqValidationError{
						field:  "Service",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CallbackReqValidationError{
						field:  "Service",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetService()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CallbackReqValidationError{
					field:  "Service",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *CallbackReq_PerfCase:
		if v == nil {
			err := CallbackReqValidationError{
				field:  "Data",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetPerfCase()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CallbackReqValidationError{
						field:  "PerfCase",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CallbackReqValidationError{
						field:  "PerfCase",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetPerfCase()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CallbackReqValidationError{
					field:  "PerfCase",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *CallbackReq_PerfSuite:
		if v == nil {
			err := CallbackReqValidationError{
				field:  "Data",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetPerfSuite()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CallbackReqValidationError{
						field:  "PerfSuite",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CallbackReqValidationError{
						field:  "PerfSuite",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetPerfSuite()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CallbackReqValidationError{
					field:  "PerfSuite",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return CallbackReqMultiError(errors)
	}

	return nil
}

// CallbackReqMultiError is an error wrapping multiple validation errors
// returned by CallbackReq.ValidateAll() if the designated constraints aren't met.
type CallbackReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CallbackReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CallbackReqMultiError) AllErrors() []error { return m }

// CallbackReqValidationError is the validation error returned by
// CallbackReq.Validate if the designated constraints aren't met.
type CallbackReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CallbackReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CallbackReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CallbackReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CallbackReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CallbackReqValidationError) ErrorName() string { return "CallbackReqValidationError" }

// Error satisfies the builtin error interface
func (e CallbackReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCallbackReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CallbackReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CallbackReqValidationError{}

// Validate checks the field values on SuiteCallbackData with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *SuiteCallbackData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SuiteCallbackData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SuiteCallbackDataMultiError, or nil if none found.
func (m *SuiteCallbackData) ValidateAll() error {
	return m.validate(true)
}

func (m *SuiteCallbackData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for PlanId

	// no validation rules for PlanExecuteId

	// no validation rules for SuiteId

	// no validation rules for SuiteExecuteId

	// no validation rules for SuiteState

	if len(errors) > 0 {
		return SuiteCallbackDataMultiError(errors)
	}

	return nil
}

// SuiteCallbackDataMultiError is an error wrapping multiple validation errors
// returned by SuiteCallbackData.ValidateAll() if the designated constraints
// aren't met.
type SuiteCallbackDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SuiteCallbackDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SuiteCallbackDataMultiError) AllErrors() []error { return m }

// SuiteCallbackDataValidationError is the validation error returned by
// SuiteCallbackData.Validate if the designated constraints aren't met.
type SuiteCallbackDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SuiteCallbackDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SuiteCallbackDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SuiteCallbackDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SuiteCallbackDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SuiteCallbackDataValidationError) ErrorName() string {
	return "SuiteCallbackDataValidationError"
}

// Error satisfies the builtin error interface
func (e SuiteCallbackDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSuiteCallbackData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SuiteCallbackDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SuiteCallbackDataValidationError{}

// Validate checks the field values on InterfaceDocumentCallbackData with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *InterfaceDocumentCallbackData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on InterfaceDocumentCallbackData with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// InterfaceDocumentCallbackDataMultiError, or nil if none found.
func (m *InterfaceDocumentCallbackData) ValidateAll() error {
	return m.validate(true)
}

func (m *InterfaceDocumentCallbackData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for PlanId

	// no validation rules for PlanExecuteId

	// no validation rules for InterfaceId

	// no validation rules for InterfaceExecuteId

	// no validation rules for InterfaceState

	if len(errors) > 0 {
		return InterfaceDocumentCallbackDataMultiError(errors)
	}

	return nil
}

// InterfaceDocumentCallbackDataMultiError is an error wrapping multiple
// validation errors returned by InterfaceDocumentCallbackData.ValidateAll()
// if the designated constraints aren't met.
type InterfaceDocumentCallbackDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m InterfaceDocumentCallbackDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m InterfaceDocumentCallbackDataMultiError) AllErrors() []error { return m }

// InterfaceDocumentCallbackDataValidationError is the validation error
// returned by InterfaceDocumentCallbackData.Validate if the designated
// constraints aren't met.
type InterfaceDocumentCallbackDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e InterfaceDocumentCallbackDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e InterfaceDocumentCallbackDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e InterfaceDocumentCallbackDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e InterfaceDocumentCallbackDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e InterfaceDocumentCallbackDataValidationError) ErrorName() string {
	return "InterfaceDocumentCallbackDataValidationError"
}

// Error satisfies the builtin error interface
func (e InterfaceDocumentCallbackDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sInterfaceDocumentCallbackData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = InterfaceDocumentCallbackDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = InterfaceDocumentCallbackDataValidationError{}

// Validate checks the field values on InterfaceCaseCallbackData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *InterfaceCaseCallbackData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on InterfaceCaseCallbackData with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// InterfaceCaseCallbackDataMultiError, or nil if none found.
func (m *InterfaceCaseCallbackData) ValidateAll() error {
	return m.validate(true)
}

func (m *InterfaceCaseCallbackData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for InterfaceId

	// no validation rules for InterfaceExecuteId

	// no validation rules for InterfaceCaseId

	// no validation rules for InterfaceCaseExecuteId

	// no validation rules for InterfaceCaseState

	// no validation rules for Version

	if len(errors) > 0 {
		return InterfaceCaseCallbackDataMultiError(errors)
	}

	return nil
}

// InterfaceCaseCallbackDataMultiError is an error wrapping multiple validation
// errors returned by InterfaceCaseCallbackData.ValidateAll() if the
// designated constraints aren't met.
type InterfaceCaseCallbackDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m InterfaceCaseCallbackDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m InterfaceCaseCallbackDataMultiError) AllErrors() []error { return m }

// InterfaceCaseCallbackDataValidationError is the validation error returned by
// InterfaceCaseCallbackData.Validate if the designated constraints aren't met.
type InterfaceCaseCallbackDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e InterfaceCaseCallbackDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e InterfaceCaseCallbackDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e InterfaceCaseCallbackDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e InterfaceCaseCallbackDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e InterfaceCaseCallbackDataValidationError) ErrorName() string {
	return "InterfaceCaseCallbackDataValidationError"
}

// Error satisfies the builtin error interface
func (e InterfaceCaseCallbackDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sInterfaceCaseCallbackData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = InterfaceCaseCallbackDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = InterfaceCaseCallbackDataValidationError{}

// Validate checks the field values on CaseCallbackData with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *CaseCallbackData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CaseCallbackData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CaseCallbackDataMultiError, or nil if none found.
func (m *CaseCallbackData) ValidateAll() error {
	return m.validate(true)
}

func (m *CaseCallbackData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SuiteId

	// no validation rules for SuiteExecuteId

	// no validation rules for CaseId

	// no validation rules for CaseExecuteId

	// no validation rules for CaseState

	// no validation rules for Version

	if len(errors) > 0 {
		return CaseCallbackDataMultiError(errors)
	}

	return nil
}

// CaseCallbackDataMultiError is an error wrapping multiple validation errors
// returned by CaseCallbackData.ValidateAll() if the designated constraints
// aren't met.
type CaseCallbackDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CaseCallbackDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CaseCallbackDataMultiError) AllErrors() []error { return m }

// CaseCallbackDataValidationError is the validation error returned by
// CaseCallbackData.Validate if the designated constraints aren't met.
type CaseCallbackDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CaseCallbackDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CaseCallbackDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CaseCallbackDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CaseCallbackDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CaseCallbackDataValidationError) ErrorName() string { return "CaseCallbackDataValidationError" }

// Error satisfies the builtin error interface
func (e CaseCallbackDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCaseCallbackData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CaseCallbackDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CaseCallbackDataValidationError{}

// Validate checks the field values on UICaseCallbackData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UICaseCallbackData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UICaseCallbackData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UICaseCallbackDataMultiError, or nil if none found.
func (m *UICaseCallbackData) ValidateAll() error {
	return m.validate(true)
}

func (m *UICaseCallbackData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for UiPlanId

	// no validation rules for UiPlanExecuteId

	// no validation rules for UiSuiteId

	// no validation rules for UiSuiteExecuteId

	// no validation rules for UiCaseId

	// no validation rules for UiCaseExecuteId

	// no validation rules for CaseState

	if all {
		switch v := interface{}(m.GetMetaData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UICaseCallbackDataValidationError{
					field:  "MetaData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UICaseCallbackDataValidationError{
					field:  "MetaData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMetaData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UICaseCallbackDataValidationError{
				field:  "MetaData",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UICaseCallbackDataMultiError(errors)
	}

	return nil
}

// UICaseCallbackDataMultiError is an error wrapping multiple validation errors
// returned by UICaseCallbackData.ValidateAll() if the designated constraints
// aren't met.
type UICaseCallbackDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UICaseCallbackDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UICaseCallbackDataMultiError) AllErrors() []error { return m }

// UICaseCallbackDataValidationError is the validation error returned by
// UICaseCallbackData.Validate if the designated constraints aren't met.
type UICaseCallbackDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UICaseCallbackDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UICaseCallbackDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UICaseCallbackDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UICaseCallbackDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UICaseCallbackDataValidationError) ErrorName() string {
	return "UICaseCallbackDataValidationError"
}

// Error satisfies the builtin error interface
func (e UICaseCallbackDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUICaseCallbackData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UICaseCallbackDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UICaseCallbackDataValidationError{}

// Validate checks the field values on UISuiteCallbackData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UISuiteCallbackData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UISuiteCallbackData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UISuiteCallbackDataMultiError, or nil if none found.
func (m *UISuiteCallbackData) ValidateAll() error {
	return m.validate(true)
}

func (m *UISuiteCallbackData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for UiPlanId

	// no validation rules for UiPlanExecuteId

	// no validation rules for UiSuiteId

	// no validation rules for UiSuiteExecuteId

	// no validation rules for SuiteState

	if all {
		switch v := interface{}(m.GetMetaData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UISuiteCallbackDataValidationError{
					field:  "MetaData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UISuiteCallbackDataValidationError{
					field:  "MetaData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMetaData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UISuiteCallbackDataValidationError{
				field:  "MetaData",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UISuiteCallbackDataMultiError(errors)
	}

	return nil
}

// UISuiteCallbackDataMultiError is an error wrapping multiple validation
// errors returned by UISuiteCallbackData.ValidateAll() if the designated
// constraints aren't met.
type UISuiteCallbackDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UISuiteCallbackDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UISuiteCallbackDataMultiError) AllErrors() []error { return m }

// UISuiteCallbackDataValidationError is the validation error returned by
// UISuiteCallbackData.Validate if the designated constraints aren't met.
type UISuiteCallbackDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UISuiteCallbackDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UISuiteCallbackDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UISuiteCallbackDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UISuiteCallbackDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UISuiteCallbackDataValidationError) ErrorName() string {
	return "UISuiteCallbackDataValidationError"
}

// Error satisfies the builtin error interface
func (e UISuiteCallbackDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUISuiteCallbackData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UISuiteCallbackDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UISuiteCallbackDataValidationError{}

// Validate checks the field values on ServiceCallbackData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ServiceCallbackData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ServiceCallbackData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ServiceCallbackDataMultiError, or nil if none found.
func (m *ServiceCallbackData) ValidateAll() error {
	return m.validate(true)
}

func (m *ServiceCallbackData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for PlanId

	// no validation rules for PlanExecuteId

	// no validation rules for ServiceId

	// no validation rules for ServiceExecuteId

	// no validation rules for ServiceState

	if len(errors) > 0 {
		return ServiceCallbackDataMultiError(errors)
	}

	return nil
}

// ServiceCallbackDataMultiError is an error wrapping multiple validation
// errors returned by ServiceCallbackData.ValidateAll() if the designated
// constraints aren't met.
type ServiceCallbackDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ServiceCallbackDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ServiceCallbackDataMultiError) AllErrors() []error { return m }

// ServiceCallbackDataValidationError is the validation error returned by
// ServiceCallbackData.Validate if the designated constraints aren't met.
type ServiceCallbackDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ServiceCallbackDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ServiceCallbackDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ServiceCallbackDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ServiceCallbackDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ServiceCallbackDataValidationError) ErrorName() string {
	return "ServiceCallbackDataValidationError"
}

// Error satisfies the builtin error interface
func (e ServiceCallbackDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sServiceCallbackData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ServiceCallbackDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ServiceCallbackDataValidationError{}

// Validate checks the field values on PerfCaseCallbackData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *PerfCaseCallbackData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PerfCaseCallbackData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PerfCaseCallbackDataMultiError, or nil if none found.
func (m *PerfCaseCallbackData) ValidateAll() error {
	return m.validate(true)
}

func (m *PerfCaseCallbackData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for PerfPlanId

	// no validation rules for PerfPlanExecuteId

	// no validation rules for PerfSuiteId

	// no validation rules for PerfSuiteExecuteId

	// no validation rules for PerfCaseId

	// no validation rules for PerfCaseExecuteId

	// no validation rules for CaseState

	if all {
		switch v := interface{}(m.GetMetaData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PerfCaseCallbackDataValidationError{
					field:  "MetaData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PerfCaseCallbackDataValidationError{
					field:  "MetaData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMetaData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PerfCaseCallbackDataValidationError{
				field:  "MetaData",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return PerfCaseCallbackDataMultiError(errors)
	}

	return nil
}

// PerfCaseCallbackDataMultiError is an error wrapping multiple validation
// errors returned by PerfCaseCallbackData.ValidateAll() if the designated
// constraints aren't met.
type PerfCaseCallbackDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PerfCaseCallbackDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PerfCaseCallbackDataMultiError) AllErrors() []error { return m }

// PerfCaseCallbackDataValidationError is the validation error returned by
// PerfCaseCallbackData.Validate if the designated constraints aren't met.
type PerfCaseCallbackDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PerfCaseCallbackDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PerfCaseCallbackDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PerfCaseCallbackDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PerfCaseCallbackDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PerfCaseCallbackDataValidationError) ErrorName() string {
	return "PerfCaseCallbackDataValidationError"
}

// Error satisfies the builtin error interface
func (e PerfCaseCallbackDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPerfCaseCallbackData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PerfCaseCallbackDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PerfCaseCallbackDataValidationError{}

// Validate checks the field values on PerfSuiteCallbackData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *PerfSuiteCallbackData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PerfSuiteCallbackData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PerfSuiteCallbackDataMultiError, or nil if none found.
func (m *PerfSuiteCallbackData) ValidateAll() error {
	return m.validate(true)
}

func (m *PerfSuiteCallbackData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for PerfPlanId

	// no validation rules for PerfPlanExecuteId

	// no validation rules for PerfSuiteId

	// no validation rules for PerfSuiteExecuteId

	// no validation rules for SuiteState

	if all {
		switch v := interface{}(m.GetMetaData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PerfSuiteCallbackDataValidationError{
					field:  "MetaData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PerfSuiteCallbackDataValidationError{
					field:  "MetaData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMetaData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PerfSuiteCallbackDataValidationError{
				field:  "MetaData",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return PerfSuiteCallbackDataMultiError(errors)
	}

	return nil
}

// PerfSuiteCallbackDataMultiError is an error wrapping multiple validation
// errors returned by PerfSuiteCallbackData.ValidateAll() if the designated
// constraints aren't met.
type PerfSuiteCallbackDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PerfSuiteCallbackDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PerfSuiteCallbackDataMultiError) AllErrors() []error { return m }

// PerfSuiteCallbackDataValidationError is the validation error returned by
// PerfSuiteCallbackData.Validate if the designated constraints aren't met.
type PerfSuiteCallbackDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PerfSuiteCallbackDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PerfSuiteCallbackDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PerfSuiteCallbackDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PerfSuiteCallbackDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PerfSuiteCallbackDataValidationError) ErrorName() string {
	return "PerfSuiteCallbackDataValidationError"
}

// Error satisfies the builtin error interface
func (e PerfSuiteCallbackDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPerfSuiteCallbackData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PerfSuiteCallbackDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PerfSuiteCallbackDataValidationError{}
