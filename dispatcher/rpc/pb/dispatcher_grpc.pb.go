// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.29.2
// source: dispatcher/dispatcher.proto

package pb

import (
	context "context"

	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	Dispatcher_Publish_FullMethodName        = "/dispatcher.Dispatcher/Publish"
	Dispatcher_Stop_FullMethodName           = "/dispatcher.Dispatcher/Stop"
	Dispatcher_SearchTaskInfo_FullMethodName = "/dispatcher.Dispatcher/SearchTaskInfo"
)

// DispatcherClient is the client API for Dispatcher service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type DispatcherClient interface {
	Publish(ctx context.Context, in *PublishReq, opts ...grpc.CallOption) (*PublishResp, error)
	Stop(ctx context.Context, in *StopReq, opts ...grpc.CallOption) (*StopResp, error)
	SearchTaskInfo(ctx context.Context, in *SearchTaskInfoReq, opts ...grpc.CallOption) (*SearchTaskInfoResp, error)
}

type dispatcherClient struct {
	cc grpc.ClientConnInterface
}

func NewDispatcherClient(cc grpc.ClientConnInterface) DispatcherClient {
	return &dispatcherClient{cc}
}

func (c *dispatcherClient) Publish(ctx context.Context, in *PublishReq, opts ...grpc.CallOption) (*PublishResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(PublishResp)
	err := c.cc.Invoke(ctx, Dispatcher_Publish_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dispatcherClient) Stop(ctx context.Context, in *StopReq, opts ...grpc.CallOption) (*StopResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(StopResp)
	err := c.cc.Invoke(ctx, Dispatcher_Stop_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dispatcherClient) SearchTaskInfo(ctx context.Context, in *SearchTaskInfoReq, opts ...grpc.CallOption) (*SearchTaskInfoResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SearchTaskInfoResp)
	err := c.cc.Invoke(ctx, Dispatcher_SearchTaskInfo_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// DispatcherServer is the server API for Dispatcher service.
// All implementations must embed UnimplementedDispatcherServer
// for forward compatibility.
type DispatcherServer interface {
	Publish(context.Context, *PublishReq) (*PublishResp, error)
	Stop(context.Context, *StopReq) (*StopResp, error)
	SearchTaskInfo(context.Context, *SearchTaskInfoReq) (*SearchTaskInfoResp, error)
	mustEmbedUnimplementedDispatcherServer()
}

// UnimplementedDispatcherServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedDispatcherServer struct{}

func (UnimplementedDispatcherServer) Publish(context.Context, *PublishReq) (*PublishResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Publish not implemented")
}
func (UnimplementedDispatcherServer) Stop(context.Context, *StopReq) (*StopResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Stop not implemented")
}
func (UnimplementedDispatcherServer) SearchTaskInfo(context.Context, *SearchTaskInfoReq) (*SearchTaskInfoResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SearchTaskInfo not implemented")
}
func (UnimplementedDispatcherServer) mustEmbedUnimplementedDispatcherServer() {}
func (UnimplementedDispatcherServer) testEmbeddedByValue()                    {}

// UnsafeDispatcherServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to DispatcherServer will
// result in compilation errors.
type UnsafeDispatcherServer interface {
	mustEmbedUnimplementedDispatcherServer()
}

func RegisterDispatcherServer(s grpc.ServiceRegistrar, srv DispatcherServer) {
	// If the following call pancis, it indicates UnimplementedDispatcherServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&Dispatcher_ServiceDesc, srv)
}

func _Dispatcher_Publish_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PublishReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DispatcherServer).Publish(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Dispatcher_Publish_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DispatcherServer).Publish(ctx, req.(*PublishReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Dispatcher_Stop_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(StopReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DispatcherServer).Stop(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Dispatcher_Stop_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DispatcherServer).Stop(ctx, req.(*StopReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Dispatcher_SearchTaskInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SearchTaskInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DispatcherServer).SearchTaskInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Dispatcher_SearchTaskInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DispatcherServer).SearchTaskInfo(ctx, req.(*SearchTaskInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

// Dispatcher_ServiceDesc is the grpc.ServiceDesc for Dispatcher service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Dispatcher_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "dispatcher.Dispatcher",
	HandlerType: (*DispatcherServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Publish",
			Handler:    _Dispatcher_Publish_Handler,
		},
		{
			MethodName: "Stop",
			Handler:    _Dispatcher_Stop_Handler,
		},
		{
			MethodName: "SearchTaskInfo",
			Handler:    _Dispatcher_SearchTaskInfo_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "dispatcher/dispatcher.proto",
}
