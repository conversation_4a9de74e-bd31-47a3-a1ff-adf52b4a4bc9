// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: dispatcher/perfworker.proto

package pb

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	pb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"

	pb1 "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = pb.Protocol(0)

	_ = pb1.ApiExecutionDataType(0)
)

// Validate checks the field values on PerfTestTaskInfo with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *PerfTestTaskInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PerfTestTaskInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PerfTestTaskInfoMultiError, or nil if none found.
func (m *PerfTestTaskInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *PerfTestTaskInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if !_PerfTestTaskInfo_ProjectId_Pattern.MatchString(m.GetProjectId()) {
		err := PerfTestTaskInfoValidationError{
			field:  "ProjectId",
			reason: "value does not match regex pattern \"(?:^project_id:.+?)\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_PerfTestTaskInfo_PlanId_Pattern.MatchString(m.GetPlanId()) {
		err := PerfTestTaskInfoValidationError{
			field:  "PlanId",
			reason: "value does not match regex pattern \"(?:^perf_plan_id:.+?)\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_PerfTestTaskInfo_TaskId_Pattern.MatchString(m.GetTaskId()) {
		err := PerfTestTaskInfoValidationError{
			field:  "TaskId",
			reason: "value does not match regex pattern \"(?:^task_id:.+?)\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_PerfTestTaskInfo_ExecuteId_Pattern.MatchString(m.GetExecuteId()) {
		err := PerfTestTaskInfoValidationError{
			field:  "ExecuteId",
			reason: "value does not match regex pattern \"(?:^execute_id:.+?)\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for TestTarget

	if utf8.RuneCountInString(m.GetTestFrameworkUrl()) > 255 {
		err := PerfTestTaskInfoValidationError{
			field:  "TestFrameworkUrl",
			reason: "value length must be at most 255 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetTestFrameworkBranch()) > 255 {
		err := PerfTestTaskInfoValidationError{
			field:  "TestFrameworkBranch",
			reason: "value length must be at most 255 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if _, ok := _PerfTestTaskInfo_Protocol_NotInLookup[m.GetProtocol()]; ok {
		err := PerfTestTaskInfoValidationError{
			field:  "Protocol",
			reason: "value must not be in list [PROTOCOL_NULL]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for TargetEnv

	if all {
		switch v := interface{}(m.GetProtobufTarget()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PerfTestTaskInfoValidationError{
					field:  "ProtobufTarget",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PerfTestTaskInfoValidationError{
					field:  "ProtobufTarget",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetProtobufTarget()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PerfTestTaskInfoValidationError{
				field:  "ProtobufTarget",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if m.GetGeneralConfig() == nil {
		err := PerfTestTaskInfoValidationError{
			field:  "GeneralConfig",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetGeneralConfig()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PerfTestTaskInfoValidationError{
					field:  "GeneralConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PerfTestTaskInfoValidationError{
					field:  "GeneralConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetGeneralConfig()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PerfTestTaskInfoValidationError{
				field:  "GeneralConfig",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if m.GetDuration() < 0 {
		err := PerfTestTaskInfoValidationError{
			field:  "Duration",
			reason: "value must be greater than or equal to 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetTimes() < 0 {
		err := PerfTestTaskInfoValidationError{
			field:  "Times",
			reason: "value must be greater than or equal to 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for TriggerMode

	// no validation rules for TriggerRule

	// no validation rules for ExecuteType

	// no validation rules for CallbackType

	if all {
		switch v := interface{}(m.GetPerfCase()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PerfTestTaskInfoValidationError{
					field:  "PerfCase",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PerfTestTaskInfoValidationError{
					field:  "PerfCase",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPerfCase()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PerfTestTaskInfoValidationError{
				field:  "PerfCase",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// skipping validation for keepalive

	if m.GetRateLimits() == nil {
		err := PerfTestTaskInfoValidationError{
			field:  "RateLimits",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetRateLimits()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PerfTestTaskInfoValidationError{
					field:  "RateLimits",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PerfTestTaskInfoValidationError{
					field:  "RateLimits",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRateLimits()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PerfTestTaskInfoValidationError{
				field:  "RateLimits",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return PerfTestTaskInfoMultiError(errors)
	}

	return nil
}

// PerfTestTaskInfoMultiError is an error wrapping multiple validation errors
// returned by PerfTestTaskInfo.ValidateAll() if the designated constraints
// aren't met.
type PerfTestTaskInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PerfTestTaskInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PerfTestTaskInfoMultiError) AllErrors() []error { return m }

// PerfTestTaskInfoValidationError is the validation error returned by
// PerfTestTaskInfo.Validate if the designated constraints aren't met.
type PerfTestTaskInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PerfTestTaskInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PerfTestTaskInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PerfTestTaskInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PerfTestTaskInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PerfTestTaskInfoValidationError) ErrorName() string { return "PerfTestTaskInfoValidationError" }

// Error satisfies the builtin error interface
func (e PerfTestTaskInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPerfTestTaskInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PerfTestTaskInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PerfTestTaskInfoValidationError{}

var _PerfTestTaskInfo_ProjectId_Pattern = regexp.MustCompile("(?:^project_id:.+?)")

var _PerfTestTaskInfo_PlanId_Pattern = regexp.MustCompile("(?:^perf_plan_id:.+?)")

var _PerfTestTaskInfo_TaskId_Pattern = regexp.MustCompile("(?:^task_id:.+?)")

var _PerfTestTaskInfo_ExecuteId_Pattern = regexp.MustCompile("(?:^execute_id:.+?)")

var _PerfTestTaskInfo_Protocol_NotInLookup = map[pb.Protocol]struct{}{
	0: {},
}

// Validate checks the field values on FinalHandleTaskInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *FinalHandleTaskInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FinalHandleTaskInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// FinalHandleTaskInfoMultiError, or nil if none found.
func (m *FinalHandleTaskInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *FinalHandleTaskInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if !_FinalHandleTaskInfo_ProjectId_Pattern.MatchString(m.GetProjectId()) {
		err := FinalHandleTaskInfoValidationError{
			field:  "ProjectId",
			reason: "value does not match regex pattern \"(?:^project_id:.+?)\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_FinalHandleTaskInfo_PlanId_Pattern.MatchString(m.GetPlanId()) {
		err := FinalHandleTaskInfoValidationError{
			field:  "PlanId",
			reason: "value does not match regex pattern \"(?:^perf_plan_id:.+?)\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_FinalHandleTaskInfo_TaskId_Pattern.MatchString(m.GetTaskId()) {
		err := FinalHandleTaskInfoValidationError{
			field:  "TaskId",
			reason: "value does not match regex pattern \"(?:^task_id:.+?)\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_FinalHandleTaskInfo_ExecuteId_Pattern.MatchString(m.GetExecuteId()) {
		err := FinalHandleTaskInfoValidationError{
			field:  "ExecuteId",
			reason: "value does not match regex pattern \"(?:^execute_id:.+?)\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return FinalHandleTaskInfoMultiError(errors)
	}

	return nil
}

// FinalHandleTaskInfoMultiError is an error wrapping multiple validation
// errors returned by FinalHandleTaskInfo.ValidateAll() if the designated
// constraints aren't met.
type FinalHandleTaskInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FinalHandleTaskInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FinalHandleTaskInfoMultiError) AllErrors() []error { return m }

// FinalHandleTaskInfoValidationError is the validation error returned by
// FinalHandleTaskInfo.Validate if the designated constraints aren't met.
type FinalHandleTaskInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FinalHandleTaskInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FinalHandleTaskInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FinalHandleTaskInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FinalHandleTaskInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FinalHandleTaskInfoValidationError) ErrorName() string {
	return "FinalHandleTaskInfoValidationError"
}

// Error satisfies the builtin error interface
func (e FinalHandleTaskInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFinalHandleTaskInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FinalHandleTaskInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FinalHandleTaskInfoValidationError{}

var _FinalHandleTaskInfo_ProjectId_Pattern = regexp.MustCompile("(?:^project_id:.+?)")

var _FinalHandleTaskInfo_PlanId_Pattern = regexp.MustCompile("(?:^perf_plan_id:.+?)")

var _FinalHandleTaskInfo_TaskId_Pattern = regexp.MustCompile("(?:^task_id:.+?)")

var _FinalHandleTaskInfo_ExecuteId_Pattern = regexp.MustCompile("(?:^execute_id:.+?)")
