// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.1
// 	protoc        v5.29.2
// source: dispatcher/perfreport.proto

package pb

import (
	reflect "reflect"
	sync "sync"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"

	pb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type PerfReportCallback struct {
	state         protoimpl.MessageState         `protogen:"open.v1"`
	ProjectId     string                         `protobuf:"bytes,1,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`               // 项目ID
	TaskId        string                         `protobuf:"bytes,2,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"`                        // 任务ID
	PlanId        string                         `protobuf:"bytes,3,opt,name=plan_id,json=planId,proto3" json:"plan_id,omitempty"`                        // 计划ID
	PlanExecuteId string                         `protobuf:"bytes,4,opt,name=plan_execute_id,json=planExecuteId,proto3" json:"plan_execute_id,omitempty"` // 计划执行ID
	Stage         StageType                      `protobuf:"varint,11,opt,name=stage,proto3,enum=dispatcher.StageType" json:"stage,omitempty"`            // 阶段
	Cases         []*PerfReportCallback_PerfCase `protobuf:"bytes,12,rep,name=cases,proto3" json:"cases,omitempty"`                                       // 用例列表
	Services      []*pb.PerfServiceMetaData      `protobuf:"bytes,13,rep,name=services,proto3" json:"services,omitempty"`                                 // 计划涉及的服务的元数据（`提前阶段`专用）
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PerfReportCallback) Reset() {
	*x = PerfReportCallback{}
	mi := &file_dispatcher_perfreport_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PerfReportCallback) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PerfReportCallback) ProtoMessage() {}

func (x *PerfReportCallback) ProtoReflect() protoreflect.Message {
	mi := &file_dispatcher_perfreport_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PerfReportCallback.ProtoReflect.Descriptor instead.
func (*PerfReportCallback) Descriptor() ([]byte, []int) {
	return file_dispatcher_perfreport_proto_rawDescGZIP(), []int{0}
}

func (x *PerfReportCallback) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *PerfReportCallback) GetTaskId() string {
	if x != nil {
		return x.TaskId
	}
	return ""
}

func (x *PerfReportCallback) GetPlanId() string {
	if x != nil {
		return x.PlanId
	}
	return ""
}

func (x *PerfReportCallback) GetPlanExecuteId() string {
	if x != nil {
		return x.PlanExecuteId
	}
	return ""
}

func (x *PerfReportCallback) GetStage() StageType {
	if x != nil {
		return x.Stage
	}
	return StageType_ST_NULL
}

func (x *PerfReportCallback) GetCases() []*PerfReportCallback_PerfCase {
	if x != nil {
		return x.Cases
	}
	return nil
}

func (x *PerfReportCallback) GetServices() []*pb.PerfServiceMetaData {
	if x != nil {
		return x.Services
	}
	return nil
}

type PerfReportCallback_PerfCase struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	SuiteName     string                 `protobuf:"bytes,1,opt,name=suite_name,json=suiteName,proto3" json:"suite_name,omitempty"`  // 集合名称
	CaseName      string                 `protobuf:"bytes,2,opt,name=case_name,json=caseName,proto3" json:"case_name,omitempty"`     // 用例名称
	TargetRps     int64                  `protobuf:"varint,3,opt,name=target_rps,json=targetRps,proto3" json:"target_rps,omitempty"` // 目标的RPS
	Cmds          []uint32               `protobuf:"varint,11,rep,packed,name=cmds,proto3" json:"cmds,omitempty"`                    // 涉及的命令号（`TT`专用）
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PerfReportCallback_PerfCase) Reset() {
	*x = PerfReportCallback_PerfCase{}
	mi := &file_dispatcher_perfreport_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PerfReportCallback_PerfCase) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PerfReportCallback_PerfCase) ProtoMessage() {}

func (x *PerfReportCallback_PerfCase) ProtoReflect() protoreflect.Message {
	mi := &file_dispatcher_perfreport_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PerfReportCallback_PerfCase.ProtoReflect.Descriptor instead.
func (*PerfReportCallback_PerfCase) Descriptor() ([]byte, []int) {
	return file_dispatcher_perfreport_proto_rawDescGZIP(), []int{0, 0}
}

func (x *PerfReportCallback_PerfCase) GetSuiteName() string {
	if x != nil {
		return x.SuiteName
	}
	return ""
}

func (x *PerfReportCallback_PerfCase) GetCaseName() string {
	if x != nil {
		return x.CaseName
	}
	return ""
}

func (x *PerfReportCallback_PerfCase) GetTargetRps() int64 {
	if x != nil {
		return x.TargetRps
	}
	return 0
}

func (x *PerfReportCallback_PerfCase) GetCmds() []uint32 {
	if x != nil {
		return x.Cmds
	}
	return nil
}

var File_dispatcher_perfreport_proto protoreflect.FileDescriptor

var file_dispatcher_perfreport_proto_rawDesc = []byte{
	0x0a, 0x1b, 0x64, 0x69, 0x73, 0x70, 0x61, 0x74, 0x63, 0x68, 0x65, 0x72, 0x2f, 0x70, 0x65, 0x72,
	0x66, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0a, 0x64,
	0x69, 0x73, 0x70, 0x61, 0x74, 0x63, 0x68, 0x65, 0x72, 0x1a, 0x15, 0x64, 0x69, 0x73, 0x70, 0x61,
	0x74, 0x63, 0x68, 0x65, 0x72, 0x2f, 0x62, 0x61, 0x73, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x11, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x70, 0x65, 0x72, 0x66, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x22, 0xad, 0x03, 0x0a, 0x12, 0x50, 0x65, 0x72, 0x66, 0x52, 0x65, 0x70, 0x6f,
	0x72, 0x74, 0x43, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72,
	0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x74, 0x61, 0x73,
	0x6b, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x74, 0x61, 0x73, 0x6b,
	0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x70, 0x6c, 0x61, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x70, 0x6c, 0x61, 0x6e, 0x49, 0x64, 0x12, 0x26, 0x0a, 0x0f, 0x70,
	0x6c, 0x61, 0x6e, 0x5f, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x70, 0x6c, 0x61, 0x6e, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74,
	0x65, 0x49, 0x64, 0x12, 0x2b, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x67, 0x65, 0x18, 0x0b, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x15, 0x2e, 0x64, 0x69, 0x73, 0x70, 0x61, 0x74, 0x63, 0x68, 0x65, 0x72, 0x2e,
	0x53, 0x74, 0x61, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x05, 0x73, 0x74, 0x61, 0x67, 0x65,
	0x12, 0x3d, 0x0a, 0x05, 0x63, 0x61, 0x73, 0x65, 0x73, 0x18, 0x0c, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x27, 0x2e, 0x64, 0x69, 0x73, 0x70, 0x61, 0x74, 0x63, 0x68, 0x65, 0x72, 0x2e, 0x50, 0x65, 0x72,
	0x66, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x43, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x2e,
	0x50, 0x65, 0x72, 0x66, 0x43, 0x61, 0x73, 0x65, 0x52, 0x05, 0x63, 0x61, 0x73, 0x65, 0x73, 0x12,
	0x37, 0x0a, 0x08, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x18, 0x0d, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x1b, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x65, 0x72, 0x66, 0x53,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x4d, 0x65, 0x74, 0x61, 0x44, 0x61, 0x74, 0x61, 0x52, 0x08,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x1a, 0x79, 0x0a, 0x08, 0x50, 0x65, 0x72, 0x66,
	0x43, 0x61, 0x73, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x75, 0x69, 0x74, 0x65, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x75, 0x69, 0x74, 0x65, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x61, 0x73, 0x65, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x1d, 0x0a, 0x0a, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x72, 0x70, 0x73, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x52, 0x70, 0x73, 0x12,
	0x12, 0x0a, 0x04, 0x63, 0x6d, 0x64, 0x73, 0x18, 0x0b, 0x20, 0x03, 0x28, 0x0d, 0x52, 0x04, 0x63,
	0x6d, 0x64, 0x73, 0x42, 0x44, 0x5a, 0x42, 0x67, 0x69, 0x74, 0x6c, 0x61, 0x62, 0x2e, 0x74, 0x74,
	0x79, 0x75, 0x79, 0x69, 0x6e, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x54, 0x65, 0x73, 0x74, 0x44, 0x65,
	0x76, 0x65, 0x6c, 0x6f, 0x70, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x70, 0x72, 0x6f, 0x62, 0x65, 0x2d,
	0x62, 0x61, 0x63, 0x6b, 0x65, 0x6e, 0x64, 0x2f, 0x64, 0x69, 0x73, 0x70, 0x61, 0x74, 0x63, 0x68,
	0x65, 0x72, 0x2f, 0x72, 0x70, 0x63, 0x2f, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x33,
}

var (
	file_dispatcher_perfreport_proto_rawDescOnce sync.Once
	file_dispatcher_perfreport_proto_rawDescData = file_dispatcher_perfreport_proto_rawDesc
)

func file_dispatcher_perfreport_proto_rawDescGZIP() []byte {
	file_dispatcher_perfreport_proto_rawDescOnce.Do(func() {
		file_dispatcher_perfreport_proto_rawDescData = protoimpl.X.CompressGZIP(file_dispatcher_perfreport_proto_rawDescData)
	})
	return file_dispatcher_perfreport_proto_rawDescData
}

var file_dispatcher_perfreport_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_dispatcher_perfreport_proto_goTypes = []any{
	(*PerfReportCallback)(nil),          // 0: dispatcher.PerfReportCallback
	(*PerfReportCallback_PerfCase)(nil), // 1: dispatcher.PerfReportCallback.PerfCase
	(StageType)(0),                      // 2: dispatcher.StageType
	(*pb.PerfServiceMetaData)(nil),      // 3: common.PerfServiceMetaData
}
var file_dispatcher_perfreport_proto_depIdxs = []int32{
	2, // 0: dispatcher.PerfReportCallback.stage:type_name -> dispatcher.StageType
	1, // 1: dispatcher.PerfReportCallback.cases:type_name -> dispatcher.PerfReportCallback.PerfCase
	3, // 2: dispatcher.PerfReportCallback.services:type_name -> common.PerfServiceMetaData
	3, // [3:3] is the sub-list for method output_type
	3, // [3:3] is the sub-list for method input_type
	3, // [3:3] is the sub-list for extension type_name
	3, // [3:3] is the sub-list for extension extendee
	0, // [0:3] is the sub-list for field type_name
}

func init() { file_dispatcher_perfreport_proto_init() }
func file_dispatcher_perfreport_proto_init() {
	if File_dispatcher_perfreport_proto != nil {
		return
	}
	file_dispatcher_base_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_dispatcher_perfreport_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_dispatcher_perfreport_proto_goTypes,
		DependencyIndexes: file_dispatcher_perfreport_proto_depIdxs,
		MessageInfos:      file_dispatcher_perfreport_proto_msgTypes,
	}.Build()
	File_dispatcher_perfreport_proto = out.File
	file_dispatcher_perfreport_proto_rawDesc = nil
	file_dispatcher_perfreport_proto_goTypes = nil
	file_dispatcher_perfreport_proto_depIdxs = nil
}
