// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: dispatcher/uiworker.proto

package pb

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	pb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"

	pb1 "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = pb.DeviceType(0)

	_ = pb1.ApiExecutionDataType(0)
)

// Validate checks the field values on UITestTaskInfo with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *UITestTaskInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UITestTaskInfo with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in UITestTaskInfoMultiError,
// or nil if none found.
func (m *UITestTaskInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *UITestTaskInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if !_UITestTaskInfo_ProjectId_Pattern.MatchString(m.GetProjectId()) {
		err := UITestTaskInfoValidationError{
			field:  "ProjectId",
			reason: "value does not match regex pattern \"(?:^project_id:.+?)\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_UITestTaskInfo_PlanId_Pattern.MatchString(m.GetPlanId()) {
		err := UITestTaskInfoValidationError{
			field:  "PlanId",
			reason: "value does not match regex pattern \"(?:^ui_plan_id:.+?)\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_UITestTaskInfo_TaskId_Pattern.MatchString(m.GetTaskId()) {
		err := UITestTaskInfoValidationError{
			field:  "TaskId",
			reason: "value does not match regex pattern \"(?:^task_id:.+?)\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for TestTarget

	if utf8.RuneCountInString(m.GetTestFrameworkUrl()) > 255 {
		err := UITestTaskInfoValidationError{
			field:  "TestFrameworkUrl",
			reason: "value length must be at most 255 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetTestFrameworkBranch()) > 255 {
		err := UITestTaskInfoValidationError{
			field:  "TestFrameworkBranch",
			reason: "value length must be at most 255 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if _, ok := pb.DeviceType_name[int32(m.GetDeviceType())]; !ok {
		err := UITestTaskInfoValidationError{
			field:  "DeviceType",
			reason: "value must be one of the defined enum values",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if _, ok := pb.PlatformType_name[int32(m.GetPlatformType())]; !ok {
		err := UITestTaskInfoValidationError{
			field:  "PlatformType",
			reason: "value must be one of the defined enum values",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetAppDownloadLink() != "" {

		if utf8.RuneCountInString(m.GetAppDownloadLink()) > 255 {
			err := UITestTaskInfoValidationError{
				field:  "AppDownloadLink",
				reason: "value length must be at most 255 runes",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if _, ok := _UITestTaskInfo_TestLanguage_InLookup[m.GetTestLanguage()]; !ok {
		err := UITestTaskInfoValidationError{
			field:  "TestLanguage",
			reason: "value must be in list [TestLanguage_PYTHON TestLanguage_GOLANG]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetTestLanguageVersion()) > 50 {
		err := UITestTaskInfoValidationError{
			field:  "TestLanguageVersion",
			reason: "value length must be at most 50 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if _, ok := _UITestTaskInfo_TestFramework_InLookup[m.GetTestFramework()]; !ok {
		err := UITestTaskInfoValidationError{
			field:  "TestFramework",
			reason: "value must be in list [TestFramework_PYTEST]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(m.GetTestArgs()) > 0 {

	}

	if len(m.GetDevices()) > 0 {

	}

	if utf8.RuneCountInString(m.GetPackageName()) > 255 {
		err := UITestTaskInfoValidationError{
			field:  "PackageName",
			reason: "value length must be at most 255 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetAppVersion() != "" {

		if utf8.RuneCountInString(m.GetAppVersion()) > 255 {
			err := UITestTaskInfoValidationError{
				field:  "AppVersion",
				reason: "value length must be at most 255 runes",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	// no validation rules for TriggerMode

	// no validation rules for TriggerRule

	// no validation rules for ExecuteType

	// no validation rules for CallbackType

	if all {
		switch v := interface{}(m.GetUiCase()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UITestTaskInfoValidationError{
					field:  "UiCase",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UITestTaskInfoValidationError{
					field:  "UiCase",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUiCase()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UITestTaskInfoValidationError{
				field:  "UiCase",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for PriorityType

	// no validation rules for ExecutedBy

	if len(errors) > 0 {
		return UITestTaskInfoMultiError(errors)
	}

	return nil
}

// UITestTaskInfoMultiError is an error wrapping multiple validation errors
// returned by UITestTaskInfo.ValidateAll() if the designated constraints
// aren't met.
type UITestTaskInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UITestTaskInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UITestTaskInfoMultiError) AllErrors() []error { return m }

// UITestTaskInfoValidationError is the validation error returned by
// UITestTaskInfo.Validate if the designated constraints aren't met.
type UITestTaskInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UITestTaskInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UITestTaskInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UITestTaskInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UITestTaskInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UITestTaskInfoValidationError) ErrorName() string { return "UITestTaskInfoValidationError" }

// Error satisfies the builtin error interface
func (e UITestTaskInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUITestTaskInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UITestTaskInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UITestTaskInfoValidationError{}

var _UITestTaskInfo_ProjectId_Pattern = regexp.MustCompile("(?:^project_id:.+?)")

var _UITestTaskInfo_PlanId_Pattern = regexp.MustCompile("(?:^ui_plan_id:.+?)")

var _UITestTaskInfo_TaskId_Pattern = regexp.MustCompile("(?:^task_id:.+?)")

var _UITestTaskInfo_TestLanguage_InLookup = map[pb.TestLanguage]struct{}{
	1: {},
	2: {},
}

var _UITestTaskInfo_TestFramework_InLookup = map[pb.TestFramework]struct{}{
	1: {},
}

// Validate checks the field values on GenerateReportTaskInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GenerateReportTaskInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GenerateReportTaskInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GenerateReportTaskInfoMultiError, or nil if none found.
func (m *GenerateReportTaskInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *GenerateReportTaskInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if !_GenerateReportTaskInfo_ProjectId_Pattern.MatchString(m.GetProjectId()) {
		err := GenerateReportTaskInfoValidationError{
			field:  "ProjectId",
			reason: "value does not match regex pattern \"(?:^project_id:.+?)\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_GenerateReportTaskInfo_PlanId_Pattern.MatchString(m.GetPlanId()) {
		err := GenerateReportTaskInfoValidationError{
			field:  "PlanId",
			reason: "value does not match regex pattern \"(?:^ui_plan_id:.+?)\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_GenerateReportTaskInfo_TaskId_Pattern.MatchString(m.GetTaskId()) {
		err := GenerateReportTaskInfoValidationError{
			field:  "TaskId",
			reason: "value does not match regex pattern \"(?:^task_id:.+?)\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetUiCase()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GenerateReportTaskInfoValidationError{
					field:  "UiCase",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GenerateReportTaskInfoValidationError{
					field:  "UiCase",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUiCase()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GenerateReportTaskInfoValidationError{
				field:  "UiCase",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GenerateReportTaskInfoMultiError(errors)
	}

	return nil
}

// GenerateReportTaskInfoMultiError is an error wrapping multiple validation
// errors returned by GenerateReportTaskInfo.ValidateAll() if the designated
// constraints aren't met.
type GenerateReportTaskInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GenerateReportTaskInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GenerateReportTaskInfoMultiError) AllErrors() []error { return m }

// GenerateReportTaskInfoValidationError is the validation error returned by
// GenerateReportTaskInfo.Validate if the designated constraints aren't met.
type GenerateReportTaskInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GenerateReportTaskInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GenerateReportTaskInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GenerateReportTaskInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GenerateReportTaskInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GenerateReportTaskInfoValidationError) ErrorName() string {
	return "GenerateReportTaskInfoValidationError"
}

// Error satisfies the builtin error interface
func (e GenerateReportTaskInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGenerateReportTaskInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GenerateReportTaskInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GenerateReportTaskInfoValidationError{}

var _GenerateReportTaskInfo_ProjectId_Pattern = regexp.MustCompile("(?:^project_id:.+?)")

var _GenerateReportTaskInfo_PlanId_Pattern = regexp.MustCompile("(?:^ui_plan_id:.+?)")

var _GenerateReportTaskInfo_TaskId_Pattern = regexp.MustCompile("(?:^task_id:.+?)")
