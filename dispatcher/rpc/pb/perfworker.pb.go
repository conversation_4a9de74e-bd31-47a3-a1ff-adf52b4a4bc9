// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.1
// 	protoc        v5.29.2
// source: dispatcher/perfworker.proto

package pb

import (
	reflect "reflect"
	sync "sync"

	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"

	pb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
	pb1 "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type PerfTestTaskInfo struct {
	state               protoimpl.MessageState   `protogen:"open.v1"`
	ProjectId           string                   `protobuf:"bytes,1,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`                                  // 项目ID
	PlanId              string                   `protobuf:"bytes,2,opt,name=plan_id,json=planId,proto3" json:"plan_id,omitempty"`                                           // 计划ID
	TaskId              string                   `protobuf:"bytes,3,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"`                                           // 任务ID
	ExecuteId           string                   `protobuf:"bytes,4,opt,name=execute_id,json=executeId,proto3" json:"execute_id,omitempty"`                                  // 执行ID
	TestTarget          string                   `protobuf:"bytes,11,opt,name=test_target,json=testTarget,proto3" json:"test_target,omitempty"`                              // Deprecated: 测试目标，可以是任意级别的，例如集合/用例
	TestFrameworkUrl    string                   `protobuf:"bytes,12,opt,name=test_framework_url,json=testFrameworkUrl,proto3" json:"test_framework_url,omitempty"`          // Deprecated: 测试框架git地址，带token
	TestFrameworkBranch string                   `protobuf:"bytes,13,opt,name=test_framework_branch,json=testFrameworkBranch,proto3" json:"test_framework_branch,omitempty"` // Deprecated: 测试框架分支名称
	Protocol            pb.Protocol              `protobuf:"varint,21,opt,name=protocol,proto3,enum=common.Protocol" json:"protocol,omitempty"`                              // 协议
	TargetEnv           pb.TargetEnvironment     `protobuf:"varint,22,opt,name=target_env,json=targetEnv,proto3,enum=common.TargetEnvironment" json:"target_env,omitempty"`  // 目标环境
	ProtobufTarget      *pb.ProtobufTarget       `protobuf:"bytes,23,opt,name=protobuf_target,json=protobufTarget,proto3" json:"protobuf_target,omitempty"`                  // Deprecated: Protobuf相关信息
	GeneralConfig       *pb.GeneralConfig        `protobuf:"bytes,24,opt,name=general_config,json=generalConfig,proto3" json:"general_config,omitempty"`                     // 通用配置
	Duration            uint32                   `protobuf:"varint,27,opt,name=duration,proto3" json:"duration,omitempty"`                                                   // 压测时长
	Times               uint32                   `protobuf:"varint,28,opt,name=times,proto3" json:"times,omitempty"`                                                         // 压测次数
	TriggerMode         pb.TriggerMode           `protobuf:"varint,31,opt,name=trigger_mode,json=triggerMode,proto3,enum=common.TriggerMode" json:"trigger_mode,omitempty"`
	TriggerRule         string                   `protobuf:"bytes,32,opt,name=trigger_rule,json=triggerRule,proto3" json:"trigger_rule,omitempty"`
	ExecuteType         pb1.ApiExecutionDataType `protobuf:"varint,33,opt,name=execute_type,json=executeType,proto3,enum=manager.ApiExecutionDataType" json:"execute_type,omitempty"`
	CallbackType        CallbackType             `protobuf:"varint,34,opt,name=callback_type,json=callbackType,proto3,enum=dispatcher.CallbackType" json:"callback_type,omitempty"`
	PerfCase            *PerfCaseWorkerInfo      `protobuf:"bytes,35,opt,name=perf_case,json=perfCase,proto3" json:"perf_case,omitempty"`       // 压测测试用例基础信息
	Keepalive           *pb.PerfKeepalive        `protobuf:"bytes,36,opt,name=keepalive,proto3" json:"keepalive,omitempty"`                     // Deprecated: 压测保活参数
	RateLimits          *pb.PerfRateLimits       `protobuf:"bytes,37,opt,name=rate_limits,json=rateLimits,proto3" json:"rate_limits,omitempty"` // 压测相关的限流配置（包括：认证接口、心跳接口）
	unknownFields       protoimpl.UnknownFields
	sizeCache           protoimpl.SizeCache
}

func (x *PerfTestTaskInfo) Reset() {
	*x = PerfTestTaskInfo{}
	mi := &file_dispatcher_perfworker_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PerfTestTaskInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PerfTestTaskInfo) ProtoMessage() {}

func (x *PerfTestTaskInfo) ProtoReflect() protoreflect.Message {
	mi := &file_dispatcher_perfworker_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PerfTestTaskInfo.ProtoReflect.Descriptor instead.
func (*PerfTestTaskInfo) Descriptor() ([]byte, []int) {
	return file_dispatcher_perfworker_proto_rawDescGZIP(), []int{0}
}

func (x *PerfTestTaskInfo) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *PerfTestTaskInfo) GetPlanId() string {
	if x != nil {
		return x.PlanId
	}
	return ""
}

func (x *PerfTestTaskInfo) GetTaskId() string {
	if x != nil {
		return x.TaskId
	}
	return ""
}

func (x *PerfTestTaskInfo) GetExecuteId() string {
	if x != nil {
		return x.ExecuteId
	}
	return ""
}

func (x *PerfTestTaskInfo) GetTestTarget() string {
	if x != nil {
		return x.TestTarget
	}
	return ""
}

func (x *PerfTestTaskInfo) GetTestFrameworkUrl() string {
	if x != nil {
		return x.TestFrameworkUrl
	}
	return ""
}

func (x *PerfTestTaskInfo) GetTestFrameworkBranch() string {
	if x != nil {
		return x.TestFrameworkBranch
	}
	return ""
}

func (x *PerfTestTaskInfo) GetProtocol() pb.Protocol {
	if x != nil {
		return x.Protocol
	}
	return pb.Protocol(0)
}

func (x *PerfTestTaskInfo) GetTargetEnv() pb.TargetEnvironment {
	if x != nil {
		return x.TargetEnv
	}
	return pb.TargetEnvironment(0)
}

func (x *PerfTestTaskInfo) GetProtobufTarget() *pb.ProtobufTarget {
	if x != nil {
		return x.ProtobufTarget
	}
	return nil
}

func (x *PerfTestTaskInfo) GetGeneralConfig() *pb.GeneralConfig {
	if x != nil {
		return x.GeneralConfig
	}
	return nil
}

func (x *PerfTestTaskInfo) GetDuration() uint32 {
	if x != nil {
		return x.Duration
	}
	return 0
}

func (x *PerfTestTaskInfo) GetTimes() uint32 {
	if x != nil {
		return x.Times
	}
	return 0
}

func (x *PerfTestTaskInfo) GetTriggerMode() pb.TriggerMode {
	if x != nil {
		return x.TriggerMode
	}
	return pb.TriggerMode(0)
}

func (x *PerfTestTaskInfo) GetTriggerRule() string {
	if x != nil {
		return x.TriggerRule
	}
	return ""
}

func (x *PerfTestTaskInfo) GetExecuteType() pb1.ApiExecutionDataType {
	if x != nil {
		return x.ExecuteType
	}
	return pb1.ApiExecutionDataType(0)
}

func (x *PerfTestTaskInfo) GetCallbackType() CallbackType {
	if x != nil {
		return x.CallbackType
	}
	return CallbackType_CallbackType_UNKNOWN
}

func (x *PerfTestTaskInfo) GetPerfCase() *PerfCaseWorkerInfo {
	if x != nil {
		return x.PerfCase
	}
	return nil
}

func (x *PerfTestTaskInfo) GetKeepalive() *pb.PerfKeepalive {
	if x != nil {
		return x.Keepalive
	}
	return nil
}

func (x *PerfTestTaskInfo) GetRateLimits() *pb.PerfRateLimits {
	if x != nil {
		return x.RateLimits
	}
	return nil
}

type FinalHandleTaskInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ProjectId     string                 `protobuf:"bytes,1,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"` // 项目ID
	PlanId        string                 `protobuf:"bytes,2,opt,name=plan_id,json=planId,proto3" json:"plan_id,omitempty"`          // 计划ID
	TaskId        string                 `protobuf:"bytes,3,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"`          // 任务ID
	ExecuteId     string                 `protobuf:"bytes,4,opt,name=execute_id,json=executeId,proto3" json:"execute_id,omitempty"` // 执行ID
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FinalHandleTaskInfo) Reset() {
	*x = FinalHandleTaskInfo{}
	mi := &file_dispatcher_perfworker_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FinalHandleTaskInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FinalHandleTaskInfo) ProtoMessage() {}

func (x *FinalHandleTaskInfo) ProtoReflect() protoreflect.Message {
	mi := &file_dispatcher_perfworker_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FinalHandleTaskInfo.ProtoReflect.Descriptor instead.
func (*FinalHandleTaskInfo) Descriptor() ([]byte, []int) {
	return file_dispatcher_perfworker_proto_rawDescGZIP(), []int{1}
}

func (x *FinalHandleTaskInfo) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *FinalHandleTaskInfo) GetPlanId() string {
	if x != nil {
		return x.PlanId
	}
	return ""
}

func (x *FinalHandleTaskInfo) GetTaskId() string {
	if x != nil {
		return x.TaskId
	}
	return ""
}

func (x *FinalHandleTaskInfo) GetExecuteId() string {
	if x != nil {
		return x.ExecuteId
	}
	return ""
}

var File_dispatcher_perfworker_proto protoreflect.FileDescriptor

var file_dispatcher_perfworker_proto_rawDesc = []byte{
	0x0a, 0x1b, 0x64, 0x69, 0x73, 0x70, 0x61, 0x74, 0x63, 0x68, 0x65, 0x72, 0x2f, 0x70, 0x65, 0x72,
	0x66, 0x77, 0x6f, 0x72, 0x6b, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0a, 0x64,
	0x69, 0x73, 0x70, 0x61, 0x74, 0x63, 0x68, 0x65, 0x72, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64,
	0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x13, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x63, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x11, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f,
	0x65, 0x6e, 0x75, 0x6d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x11, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2f, 0x70, 0x65, 0x72, 0x66, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x11, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x74, 0x61, 0x73, 0x6b, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x19, 0x64, 0x69, 0x73, 0x70, 0x61, 0x74, 0x63, 0x68, 0x65, 0x72, 0x2f, 0x63, 0x61, 0x6c,
	0x6c, 0x62, 0x61, 0x63, 0x6b, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x64, 0x69, 0x73,
	0x70, 0x61, 0x74, 0x63, 0x68, 0x65, 0x72, 0x2f, 0x77, 0x6f, 0x72, 0x6b, 0x65, 0x72, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x12, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2f, 0x62, 0x61,
	0x73, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xe2, 0x08, 0x0a, 0x10, 0x50, 0x65, 0x72,
	0x66, 0x54, 0x65, 0x73, 0x74, 0x54, 0x61, 0x73, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x39, 0x0a,
	0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x1a, 0xfa, 0x42, 0x17, 0x72, 0x15, 0x32, 0x13, 0x28, 0x3f, 0x3a, 0x5e, 0x70, 0x72,
	0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x3a, 0x2e, 0x2b, 0x3f, 0x29, 0x52, 0x09, 0x70,
	0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x12, 0x35, 0x0a, 0x07, 0x70, 0x6c, 0x61, 0x6e,
	0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x1c, 0xfa, 0x42, 0x19, 0x72, 0x17,
	0x32, 0x15, 0x28, 0x3f, 0x3a, 0x5e, 0x70, 0x65, 0x72, 0x66, 0x5f, 0x70, 0x6c, 0x61, 0x6e, 0x5f,
	0x69, 0x64, 0x3a, 0x2e, 0x2b, 0x3f, 0x29, 0x52, 0x06, 0x70, 0x6c, 0x61, 0x6e, 0x49, 0x64, 0x12,
	0x30, 0x0a, 0x07, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x17, 0xfa, 0x42, 0x14, 0x72, 0x12, 0x32, 0x10, 0x28, 0x3f, 0x3a, 0x5e, 0x74, 0x61, 0x73,
	0x6b, 0x5f, 0x69, 0x64, 0x3a, 0x2e, 0x2b, 0x3f, 0x29, 0x52, 0x06, 0x74, 0x61, 0x73, 0x6b, 0x49,
	0x64, 0x12, 0x39, 0x0a, 0x0a, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x5f, 0x69, 0x64, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x1a, 0xfa, 0x42, 0x17, 0x72, 0x15, 0x32, 0x13, 0x28, 0x3f,
	0x3a, 0x5e, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x5f, 0x69, 0x64, 0x3a, 0x2e, 0x2b, 0x3f,
	0x29, 0x52, 0x09, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b,
	0x74, 0x65, 0x73, 0x74, 0x5f, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x18, 0x0b, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0a, 0x74, 0x65, 0x73, 0x74, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x12, 0x36, 0x0a,
	0x12, 0x74, 0x65, 0x73, 0x74, 0x5f, 0x66, 0x72, 0x61, 0x6d, 0x65, 0x77, 0x6f, 0x72, 0x6b, 0x5f,
	0x75, 0x72, 0x6c, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x72, 0x03,
	0x18, 0xff, 0x01, 0x52, 0x10, 0x74, 0x65, 0x73, 0x74, 0x46, 0x72, 0x61, 0x6d, 0x65, 0x77, 0x6f,
	0x72, 0x6b, 0x55, 0x72, 0x6c, 0x12, 0x3c, 0x0a, 0x15, 0x74, 0x65, 0x73, 0x74, 0x5f, 0x66, 0x72,
	0x61, 0x6d, 0x65, 0x77, 0x6f, 0x72, 0x6b, 0x5f, 0x62, 0x72, 0x61, 0x6e, 0x63, 0x68, 0x18, 0x0d,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x72, 0x03, 0x18, 0xff, 0x01, 0x52, 0x13,
	0x74, 0x65, 0x73, 0x74, 0x46, 0x72, 0x61, 0x6d, 0x65, 0x77, 0x6f, 0x72, 0x6b, 0x42, 0x72, 0x61,
	0x6e, 0x63, 0x68, 0x12, 0x36, 0x0a, 0x08, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x18,
	0x15, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x10, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50,
	0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x82, 0x01, 0x02, 0x20,
	0x00, 0x52, 0x08, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x12, 0x38, 0x0a, 0x0a, 0x74,
	0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x65, 0x6e, 0x76, 0x18, 0x16, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x19, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x45,
	0x6e, 0x76, 0x69, 0x72, 0x6f, 0x6e, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x09, 0x74, 0x61, 0x72, 0x67,
	0x65, 0x74, 0x45, 0x6e, 0x76, 0x12, 0x3f, 0x0a, 0x0f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x5f, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x18, 0x17, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x52, 0x0e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x12, 0x46, 0x0a, 0x0e, 0x67, 0x65, 0x6e, 0x65, 0x72, 0x61,
	0x6c, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x18, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x6c, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52,
	0x0d, 0x67, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x6c, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x23,
	0x0a, 0x08, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x1b, 0x20, 0x01, 0x28, 0x0d,
	0x42, 0x07, 0xfa, 0x42, 0x04, 0x2a, 0x02, 0x28, 0x00, 0x52, 0x08, 0x64, 0x75, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x12, 0x1d, 0x0a, 0x05, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x18, 0x1c, 0x20, 0x01,
	0x28, 0x0d, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x2a, 0x02, 0x28, 0x00, 0x52, 0x05, 0x74, 0x69, 0x6d,
	0x65, 0x73, 0x12, 0x36, 0x0a, 0x0c, 0x74, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x5f, 0x6d, 0x6f,
	0x64, 0x65, 0x18, 0x1f, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x13, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2e, 0x54, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x4d, 0x6f, 0x64, 0x65, 0x52, 0x0b, 0x74,
	0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x4d, 0x6f, 0x64, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x74, 0x72,
	0x69, 0x67, 0x67, 0x65, 0x72, 0x5f, 0x72, 0x75, 0x6c, 0x65, 0x18, 0x20, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0b, 0x74, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x52, 0x75, 0x6c, 0x65, 0x12, 0x40, 0x0a,
	0x0c, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x21, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x1d, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x41, 0x70,
	0x69, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x54, 0x79,
	0x70, 0x65, 0x52, 0x0b, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x3d, 0x0a, 0x0d, 0x63, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x22, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x18, 0x2e, 0x64, 0x69, 0x73, 0x70, 0x61, 0x74, 0x63,
	0x68, 0x65, 0x72, 0x2e, 0x43, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x54, 0x79, 0x70, 0x65,
	0x52, 0x0c, 0x63, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x54, 0x79, 0x70, 0x65, 0x12, 0x3b,
	0x0a, 0x09, 0x70, 0x65, 0x72, 0x66, 0x5f, 0x63, 0x61, 0x73, 0x65, 0x18, 0x23, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1e, 0x2e, 0x64, 0x69, 0x73, 0x70, 0x61, 0x74, 0x63, 0x68, 0x65, 0x72, 0x2e, 0x50,
	0x65, 0x72, 0x66, 0x43, 0x61, 0x73, 0x65, 0x57, 0x6f, 0x72, 0x6b, 0x65, 0x72, 0x49, 0x6e, 0x66,
	0x6f, 0x52, 0x08, 0x70, 0x65, 0x72, 0x66, 0x43, 0x61, 0x73, 0x65, 0x12, 0x3d, 0x0a, 0x09, 0x6b,
	0x65, 0x65, 0x70, 0x61, 0x6c, 0x69, 0x76, 0x65, 0x18, 0x24, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x65, 0x72, 0x66, 0x4b, 0x65, 0x65, 0x70,
	0x61, 0x6c, 0x69, 0x76, 0x65, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x08, 0x01, 0x52,
	0x09, 0x6b, 0x65, 0x65, 0x70, 0x61, 0x6c, 0x69, 0x76, 0x65, 0x12, 0x41, 0x0a, 0x0b, 0x72, 0x61,
	0x74, 0x65, 0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x73, 0x18, 0x25, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x16, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x65, 0x72, 0x66, 0x52, 0x61, 0x74,
	0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x73, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10,
	0x01, 0x52, 0x0a, 0x72, 0x61, 0x74, 0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x73, 0x22, 0xf4, 0x01,
	0x0a, 0x13, 0x46, 0x69, 0x6e, 0x61, 0x6c, 0x48, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x54, 0x61, 0x73,
	0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x39, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x1a, 0xfa, 0x42, 0x17, 0x72, 0x15,
	0x32, 0x13, 0x28, 0x3f, 0x3a, 0x5e, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64,
	0x3a, 0x2e, 0x2b, 0x3f, 0x29, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64,
	0x12, 0x35, 0x0a, 0x07, 0x70, 0x6c, 0x61, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x1c, 0xfa, 0x42, 0x19, 0x72, 0x17, 0x32, 0x15, 0x28, 0x3f, 0x3a, 0x5e, 0x70, 0x65,
	0x72, 0x66, 0x5f, 0x70, 0x6c, 0x61, 0x6e, 0x5f, 0x69, 0x64, 0x3a, 0x2e, 0x2b, 0x3f, 0x29, 0x52,
	0x06, 0x70, 0x6c, 0x61, 0x6e, 0x49, 0x64, 0x12, 0x30, 0x0a, 0x07, 0x74, 0x61, 0x73, 0x6b, 0x5f,
	0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x17, 0xfa, 0x42, 0x14, 0x72, 0x12, 0x32,
	0x10, 0x28, 0x3f, 0x3a, 0x5e, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x69, 0x64, 0x3a, 0x2e, 0x2b, 0x3f,
	0x29, 0x52, 0x06, 0x74, 0x61, 0x73, 0x6b, 0x49, 0x64, 0x12, 0x39, 0x0a, 0x0a, 0x65, 0x78, 0x65,
	0x63, 0x75, 0x74, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x1a, 0xfa,
	0x42, 0x17, 0x72, 0x15, 0x32, 0x13, 0x28, 0x3f, 0x3a, 0x5e, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74,
	0x65, 0x5f, 0x69, 0x64, 0x3a, 0x2e, 0x2b, 0x3f, 0x29, 0x52, 0x09, 0x65, 0x78, 0x65, 0x63, 0x75,
	0x74, 0x65, 0x49, 0x64, 0x42, 0x44, 0x5a, 0x42, 0x67, 0x69, 0x74, 0x6c, 0x61, 0x62, 0x2e, 0x74,
	0x74, 0x79, 0x75, 0x79, 0x69, 0x6e, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x54, 0x65, 0x73, 0x74, 0x44,
	0x65, 0x76, 0x65, 0x6c, 0x6f, 0x70, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x70, 0x72, 0x6f, 0x62, 0x65,
	0x2d, 0x62, 0x61, 0x63, 0x6b, 0x65, 0x6e, 0x64, 0x2f, 0x64, 0x69, 0x73, 0x70, 0x61, 0x74, 0x63,
	0x68, 0x65, 0x72, 0x2f, 0x72, 0x70, 0x63, 0x2f, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x33,
}

var (
	file_dispatcher_perfworker_proto_rawDescOnce sync.Once
	file_dispatcher_perfworker_proto_rawDescData = file_dispatcher_perfworker_proto_rawDesc
)

func file_dispatcher_perfworker_proto_rawDescGZIP() []byte {
	file_dispatcher_perfworker_proto_rawDescOnce.Do(func() {
		file_dispatcher_perfworker_proto_rawDescData = protoimpl.X.CompressGZIP(file_dispatcher_perfworker_proto_rawDescData)
	})
	return file_dispatcher_perfworker_proto_rawDescData
}

var file_dispatcher_perfworker_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_dispatcher_perfworker_proto_goTypes = []any{
	(*PerfTestTaskInfo)(nil),      // 0: dispatcher.PerfTestTaskInfo
	(*FinalHandleTaskInfo)(nil),   // 1: dispatcher.FinalHandleTaskInfo
	(pb.Protocol)(0),              // 2: common.Protocol
	(pb.TargetEnvironment)(0),     // 3: common.TargetEnvironment
	(*pb.ProtobufTarget)(nil),     // 4: common.ProtobufTarget
	(*pb.GeneralConfig)(nil),      // 5: common.GeneralConfig
	(pb.TriggerMode)(0),           // 6: common.TriggerMode
	(pb1.ApiExecutionDataType)(0), // 7: manager.ApiExecutionDataType
	(CallbackType)(0),             // 8: dispatcher.CallbackType
	(*PerfCaseWorkerInfo)(nil),    // 9: dispatcher.PerfCaseWorkerInfo
	(*pb.PerfKeepalive)(nil),      // 10: common.PerfKeepalive
	(*pb.PerfRateLimits)(nil),     // 11: common.PerfRateLimits
}
var file_dispatcher_perfworker_proto_depIdxs = []int32{
	2,  // 0: dispatcher.PerfTestTaskInfo.protocol:type_name -> common.Protocol
	3,  // 1: dispatcher.PerfTestTaskInfo.target_env:type_name -> common.TargetEnvironment
	4,  // 2: dispatcher.PerfTestTaskInfo.protobuf_target:type_name -> common.ProtobufTarget
	5,  // 3: dispatcher.PerfTestTaskInfo.general_config:type_name -> common.GeneralConfig
	6,  // 4: dispatcher.PerfTestTaskInfo.trigger_mode:type_name -> common.TriggerMode
	7,  // 5: dispatcher.PerfTestTaskInfo.execute_type:type_name -> manager.ApiExecutionDataType
	8,  // 6: dispatcher.PerfTestTaskInfo.callback_type:type_name -> dispatcher.CallbackType
	9,  // 7: dispatcher.PerfTestTaskInfo.perf_case:type_name -> dispatcher.PerfCaseWorkerInfo
	10, // 8: dispatcher.PerfTestTaskInfo.keepalive:type_name -> common.PerfKeepalive
	11, // 9: dispatcher.PerfTestTaskInfo.rate_limits:type_name -> common.PerfRateLimits
	10, // [10:10] is the sub-list for method output_type
	10, // [10:10] is the sub-list for method input_type
	10, // [10:10] is the sub-list for extension type_name
	10, // [10:10] is the sub-list for extension extendee
	0,  // [0:10] is the sub-list for field type_name
}

func init() { file_dispatcher_perfworker_proto_init() }
func file_dispatcher_perfworker_proto_init() {
	if File_dispatcher_perfworker_proto != nil {
		return
	}
	file_dispatcher_callback_proto_init()
	file_dispatcher_worker_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_dispatcher_perfworker_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_dispatcher_perfworker_proto_goTypes,
		DependencyIndexes: file_dispatcher_perfworker_proto_depIdxs,
		MessageInfos:      file_dispatcher_perfworker_proto_msgTypes,
	}.Build()
	File_dispatcher_perfworker_proto = out.File
	file_dispatcher_perfworker_proto_rawDesc = nil
	file_dispatcher_perfworker_proto_goTypes = nil
	file_dispatcher_perfworker_proto_depIdxs = nil
}
