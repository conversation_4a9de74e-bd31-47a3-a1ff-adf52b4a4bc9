syntax = "v1"

// 新增配置参数
type GeneralConfigVar {
    Key   string `json:"key"`
    Value string `json:"value"`
}

type ApiGeneralConfig {
    ProjectId   string             `json:"project_id"`  // 项目ID
    ConfigId    string             `json:"config_id"`   // 通用配置ID
    Name        string             `json:"name"`        // 通用配置名称
    Description string             `json:"description"` // 通用配置描述
    BaseUrl     string             `json:"base_url"`    // HTTP请求基础URL
    Verify      bool               `json:"verify"`      // 是否验证服务器的TLS证书
    Variables   []GeneralConfigVar `json:"variables"`
}

type ApiAccountConfig {
    ProjectId    string `json:"project_id"`        // 项目ID
    ConfigId     string `json:"config_id"`          // 池账号配置ID
    Name         string `json:"name"`                   // 账号配置名称
    Description  string `json:"description"`     // 池账号配置描述
    ProductType  int64 `json:"product_type"`     // 产品类型
    ProductName  string `json:"product_name"`    // 产品名称
    PoolEnvTable string `json:"pool_env_table"` // 账号池环境表名称
    PoolEnvName  string `json:"pool_env_name"`   // 账号池环境名称
}

type ComponentKey {
    Id      string `json:"id"`                         // 执行对象的ID
    Type    int64  `json:"type"`                       // 执行对象的类型
    Version string `json:"version,omitempty,optional"` // 执行对象的版本号
}

type UIAgentComponent {
    Name          string                       `json:"name"`           // 组件名称
    ApplicationId string                       `json:"application_id"` // 应用配置ID
    StepByStep    bool                         `json:"step_by_step"`   // 是否分步执行
    Steps         []*UIAgentComponentStep      `json:"steps"`          // 步骤列表
    Expectation   *UIAgentComponentExpectation `json:"expectation"`    // 期望结果
    Variables     []*KeyValuePair              `json:"variables"`      // 变量列表
    Device        *UIAgentDevice               `json:"device"`         // 设备信息
    Reinstall     bool                         `json:"reinstall"`      // 是否重新安装
    Restart       bool                         `json:"restart"`        // 是否重启应用
}

type UIAgentComponentStep {
    Content     string                       `json:"content" validate:"required"`
    Expectation *UIAgentComponentExpectation `json:"expectation,optional"`
}

type UIAgentComponentExpectation {
    Text  string `json:"text,optional"`
    Image string `json:"image,optional"`
}

type KeyValuePair {
    Key   string `json:"key" validate:"required"`
    Value string `json:"value" validate:"required"`
}

type UIAgentDeviceInfo {
	ProjectDevice *UIAgentProjectDeviceInfo `json:"project_device,omitempty,optional"` // 项目设备
	UserDevice    *UIAgentUserDeviceInfo    `json:"user_device,omitempty,optional"`    // 用户设备
}

type UIAgentProjectDeviceInfo {
	UDID  string `json:"udid"`  // 设备编号
	Token string `json:"token"` // 令牌（占用设备后得到的令牌）
}

type UIAgentUserDeviceInfo {
	DeviceType    int8   `json:"device_type"`    // 设备类型（真机、云手机）
	PlatformType  int8   `json:"platform_type"`  // 平台类型（Android、iOS）
	UDID          string `json:"udid"`           // 设备编号
	RemoteAddress string `json:"remote_address"` // 远程连接地址
}

type SubEnvInfo {
    SubEnvName string `json:"sub_env_name" validate:"required" zh:"子环境名称"`
}

type TriggerUser {
    Email   string `json:"email" validate:"required" zh:"TT邮箱"`
    Account string `json:"account,omitempty,optional" zh:"TT工号"`
}

type UiPlanInfo {
    AppDownloadUrl string   `json:"app_download_url,omitempty,optional" validate:"omitempty,url,lte=255" zh:"App下载地址"`
    AppVersion     string   `json:"app_version,omitempty,optional" zh:"App版本信息"`
    Devices        []string `json:"devices,omitempty,optional" zh:"设备列表"`
    Together       bool     `json:"together,omitempty,optional" zh:"选择的设备是否一起执行"`
}

type PerfPlanInfo {
    ExecuteType   int8  `json:"execute_type" validate:"oneof=1 2" zh:"执行类型 (执行、调试)"`
    EstimatedTime int64 `json:"estimated_time,omitempty,optional" zh:"预计执行时间(秒级时间戳,0为立即执行)"`
}

type ApproverUser {
    Email   string `json:"email" validate:"required" zh:"TT邮箱"`
    Account string `json:"account,omitempty,optional" zh:"TT工号"`
}

type StopMetadata {
	StopType int32       `json:"stop_type,default=1" validate:"oneof=1 2" zh:"停止类型"`
	Reason   string      `json:"reason" zh:"停止原因"`
	Detail   *StopDetail `json:"detail,optional,omitempty" zh:"停止详细信息"`
}
type StopDetail {
	Rule *StopDetailOfPerfStopRule `json:"rule,optional,omitempty" zh:"压测停止规则"`
}
type StopDetailOfPerfStopRule {
	MetricType string        `json:"metric_type" validate:"required" zh:"指标类型"`
	Service    string        `json:"service" validate:"required" zh:"服务名称"`
	Namespace  string        `json:"namespace" validate:"required" zh:"命名空间"`
	Method     string        `json:"method" validate:"required" zh:"接口名称"`
	Threshold  float64       `json:"threshold" validate:"required" zh:"规则中的阀值"`
	ReachedAt  int64         `json:"reached_at" validate:"required" zh:"达到规则中的阀值的时间"`
	LatestAt   int64         `json:"latestAt" validate:"gtfield=ReachedAt" zh:"达到规则中的持续时长的时间"`
	Points     []MetricPoint `json:"points" validate:"lte=1" zh:"满足规则后的指标点"`
}
type MetricPoint {
	Timestamp int64
	Value     float64
}

type (
    PublishReq {
        ComponentKey
        ProjectId        string             `json:"project_id"`                             // 项目ID
        ExecuteMode      int32              `json:"execute_mode"`                           // 用例执行模式
        AccountConfig    []ApiAccountConfig `json:"account_config,omitempty,optional"`      // 池账号配置
        GeneralConfig    ApiGeneralConfig   `json:"general_config,omitempty,optional"`      // 通用配置
        Debug            bool               `json:"debug,omitempty,optional"`               // 是否验证服务器的TLS证书
        InterfaceCaseIds []ComponentKey     `json:"interface_case_ids,omitempty,optional"`  // 接口用例id列表，当type!=5时，该值为null
        UIAgentComponent *UIAgentComponent  `json:"ui_agent_component,omitempty,optional"`  // UI Agent组件
    }

    PublishResp {
        TaskId    string `json:"task_id"`
        ExecuteId string `json:"execute_id"`
        Version   string `json:"version"`
    }

    PlanReq {
        PlanId          string         `json:"plan_id" validate:"required" zh:"计划ID"`
        PlanType        int64          `json:"plan_type,omitempty,optional,default=4" zh:"执行对象的类型"`
        ProjectId       string         `json:"project_id" validate:"required" zh:"项目ID"`
        TriggerMode     string         `json:"trigger_mode" validate:"required" zh:"执行模式"`
        Debug           bool           `json:"debug,omitempty,optional" zh:"是否开启debug"`
        SubEnvInfo      *SubEnvInfo    `json:"sub_env_info,omitempty,optional" zh:"子环境信息"`
        TriggerUser     *TriggerUser   `json:"trigger_user,omitempty,optional" zh:"触发测试计划执行的人的信息"`
        CallbackUrl     string         `json:"callback_url,omitempty,optional" zh:"回调地址"`
        CallbackTimeout int64          `json:"callback_timeout,omitempty,optional" zh:"回调超时时间"`
        Services        []string       `json:"services,omitempty,optional" zh:"CI/CD升级的服务列表"`
        UiPlanInfo      *UiPlanInfo    `json:"ui_plan_info,omitempty,optional" zh:"UI测试计划额外信息"`
        PriorityType    int8           `json:"priority_type,omitempty,optional" validate:"oneof=0 1 2 3 4" zh:"优先级（Default、Middle、High、Ultra、Low)"`
        PerfPlanInfo    *PerfPlanInfo  `json:"perf_plan_info,omitempty,optional" zh:"压力测试计划额外信息"`
        Approvers       []ApproverUser `json:"approvers,omitempty,optional" zh:"审批测试执行工单的人的信息"`
    }

    PlanResp {
        TaskId    string `json:"task_id"`
        ExecuteId string `json:"execute_id"`
    }

    StopReq {
        ProjectId string `json:"project_id" validate:"required" zh:"项目ID"`
        TaskId    string `json:"task_id" validate:"required" zh:"任务ID"`
        Id        string `json:"id" validate:"required" zh:"执行对象的ID"`
        Type      int64  `json:"type" validate:"required" zh:"执行对象的类型"`
        ExecuteId string `json:"execute_id" validate:"required" zh:"执行ID"`

        Metadata *StopMetadata `json:"metadata,optional,omitempty" zh:"元数据"`
    }

    StopResp {
    }
)
