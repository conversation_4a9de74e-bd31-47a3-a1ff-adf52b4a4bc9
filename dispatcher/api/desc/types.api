syntax = "v1"

type Condition {
    Single *SingleCondition `json:"single,omitempty,optional"`
    Group  *GroupCondition  `json:"group,omitempty,optional"`
}

type GroupCondition {
    Relationship string       `json:"relationship" validate:"oneof=AND OR"`
    Conditions   []*Condition `json:"conditions"`
}

type Between {
    Start string `json:"start"`
    End   string `json:"end"`
}

type Other {
    Value string `json:"value"`
}

type SingleCondition {
    Field   string   `json:"field" validate:"required"`
    Compare string   `json:"compare,default=EQ" validate:"oneof=EQ NE LT LE GT GE LIKE IN BETWEEN"`
    In      []string `json:"in,omitempty,optional"`
    Between *Between `json:"between,omitempty,optional"`
    Other   *Other   `json:"other,omitempty,optional"`
}

type Pagination {
    CurrentPage uint64 `json:"current_page,default=1" validate:"gte=1"`
    PageSize    uint64 `json:"page_size,default=10" validate:"gte=1"`
}

type SortField {
    Field string `json:"field" validate:"required"`
    Order string `json:"order,default=ASC"`
}

type KeyValuePair {
    Key   string `json:"key" validate:"required"`
    Value string `json:"value" validate:"required"`
}

type FullUserInfo {
    Account      string `json:"account"`
    Fullname     string `json:"fullname"`
    DeptId       string `json:"dept_id"`
    DeptName     string `json:"dept_name"`
    FullDeptName string `json:"full_dept_name"`
    Email        string `json:"email"`
    Mobile       string `json:"mobile"`
    Photo        string `json:"photo"`
    Enabled      bool   `json:"enabled"`
}
