syntax = "v1"

import "types.api"

type SearchTaskInfoItem  {
    ProjectId string `json:"project_id"`                                               // 项目ID
    TaskId string `json:"task_id"`                                                     // 任务id
    PlanId string `json:"plan_id"`                                                     // 计划ID
    PlanName string `json:"plan_name"`                                                 // 计划名称

    TriggerMode string `json:"trigger_mode"`                                           //触发类型
    TaskExecuteStatus int8 `json:"task_execute_status"`                                //执行状态(0排队中,1执行中,2已完成,3已停止)
    PriorityType int8 `json:"priority_type"`                                           //优先级策略(0 1 2 3 4 =》Default、Middle、High、Ultra、Low)
    TaskExecutedResult int8 `json:"task_executed_result"`                              //执行结果(0缺省,1成功,2失败,3异常)

    TotalCase int64 `json:"total_case"`                                                // 总测试用例数
    FinishedCase int64 `json:"finished_case"`                                          // 已经执行的测试用例数
    SuccessCase int64 `json:"success_case"`                                            // 执行成功的测试用例数

    TotalSuite int64 `json:"total_suite"`                                              // 测试集合总数
    FinishedSuite int64 `json:"finished_suite"`                                        // 执行完的测试集合数
    SuccessSuite int64 `json:"success_suite"`                                          // 执行成功的测试集合数

    ExecuteBy *FullUserInfo `json:"execute_by"`                                        // 创建者

    CostTime int64 `json:"cost_time"`                                                  // 执行耗时
    CreateTime int64 `json:"create_time"`                                              // 创建时间
    WaitTime int64 `json:"wait_time"`                                                  // 排队耗时
    StartedAt int64 `json:"started_at"`                                                // 开始时间
    EndedAt int64 `json:"ended_at"`                                                    // 结束时间

    ExecuteId string `json:"execute_id"  redis:"execute_id"`                           // 计划ID
    UpdateAt int64 `json:"update_at"  redis:"update_at"`                               // 更新时间
    ReportViewUrl string `json:"report_view_url"  redis:"report_view_url"`             // 查看地址
    ReportDownloadUrl string `json:"report_download_url"  redis:"report_download_url"` // 下载地址
}

// 查询任务信息列表
type (
    SearchTaskInfoReq {
        ProjectId string `json:"project_id" validate:"required" zh:"项目ID"`
        Condition *Condition `json:"condition,omitempty,optional" zh:"查询条件"`
        Pagination *Pagination `json:"pagination,omitempty,optional" zh:"查询分页"`
        Sort []*SortField `json:"sort,omitempty,optional" zh:"查询排序"`
    }
    SearchTaskInfoResp {
        CurrentPage uint64 `json:"current_page"`
        PageSize uint64 `json:"page_size"`
        TotalCount uint64 `json:"total_count"`
        TotalPage uint64 `json:"total_page"`
        Items []*SearchTaskInfoItem `json:"items"`
    }
)
