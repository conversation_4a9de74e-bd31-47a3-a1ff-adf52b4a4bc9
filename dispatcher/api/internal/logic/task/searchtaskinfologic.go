package task

import (
	"context"
	"fmt"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/logx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/userinfo"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"
	usercommon "gitlab.ttyuyin.com/TestDevelopment/qet-backend-middleware/user/common"

	commonpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/rpc/pb"
)

type SearchTaskInfoLogic struct {
	logx.Logger
	ctx         context.Context
	svcCtx      *svc.ServiceContext
	currentUser *userinfo.UserInfo
	converters  []utils.TypeConverter
}

func NewSearchTaskInfoLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SearchTaskInfoLogic {
	return &SearchTaskInfoLogic{
		currentUser: userinfo.FromContext(ctx),
		Logger:      logx.WithContext(ctx),
		ctx:         ctx,
		svcCtx:      svcCtx,

		converters: []utils.TypeConverter{
			commonpb.StringToTriggerMode(),
			commonpb.TriggerModeToString(),
			usercommon.StringToUserInfo(ctx, svcCtx.UserRpc, nil),
		},
	}
}

func (l *SearchTaskInfoLogic) SearchTaskInfo(req *types.SearchTaskInfoReq) (resp *types.SearchTaskInfoResp, err error) {
	in := &pb.SearchTaskInfoReq{}
	if err = utils.Copy(in, req, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy data[%+v] to request, error: %+v", req,
			err,
		)
	}

	out, err := l.svcCtx.DispatcherRpc.SearchTaskInfo(l.ctx, in)
	if err != nil {
		return nil, errorx.Err(errorx.GrpcCanceled, fmt.Sprintf("%s", err))
	}

	resp = &types.SearchTaskInfoResp{Items: []*types.SearchTaskInfoItem{}}
	if err = utils.Copy(resp, out, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy data[%+v] to response, error: %+v",
			out, err,
		)
	}

	return
}
