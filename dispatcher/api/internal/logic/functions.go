package logic

import (
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	commonpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
)

// Deprecated: Use `commonpb.StringToTriggerMode` instead.
func StringToTriggerMode() utils.TypeConverter {
	return utils.StringToPBEnum(commonpb.TriggerMode_NULL)
}

// Deprecated: Use `commonpb.TriggerModeToString` instead.
func TriggerModeToString() utils.TypeConverter {
	return utils.PBEnumToString(commonpb.TriggerMode_NULL)
}
