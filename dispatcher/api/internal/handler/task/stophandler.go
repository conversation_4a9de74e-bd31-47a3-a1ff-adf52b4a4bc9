package task

import (
	"net/http"

	"github.com/zeromicro/go-zero/rest/httpx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/api/internal/logic/task"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/api/internal/types"
)

func StopHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.StopReq
		if err := httpx.Parse(r, &req); err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
			return
		}

		l := task.NewStopLogic(r.Context(), svcCtx)
		resp, err := l.Stop(&req)
		if err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
		} else {
			httpx.OkJsonCtx(r.Context(), w, resp)
		}
	}
}
