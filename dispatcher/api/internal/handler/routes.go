// Code generated by goctl. DO NOT EDIT.
// goctl 1.8.3

package handler

import (
	"net/http"

	task "gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/api/internal/handler/task"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/api/internal/svc"

	"github.com/zeromicro/go-zero/rest"
)

func RegisterHandlers(server *rest.Server, serverCtx *svc.ServiceContext) {
	server.AddRoutes(
		[]rest.Route{
			{
				// publish task
				Method:  http.MethodPost,
				Path:    "/task/publish",
				Handler: task.PublishHandler(serverCtx),
			},
			{
				// publish plan task
				Method:  http.MethodPost,
				Path:    "/task/plan",
				Handler: task.PlanHandler(serverCtx),
			},
			{
				// stop task
				Method:  http.MethodPost,
				Path:    "/task/stop",
				Handler: task.StopHandler(serverCtx),
			},
		},
		rest.WithPrefix("/dispatcher/v1"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				// search info task plans
				Method:  http.MethodPost,
				Path:    "/task/info/search",
				Handler: task.SearchTaskInfoHandler(serverCtx),
			},
		},
		rest.WithPrefix("/dispatcher/v1"),
	)
}
