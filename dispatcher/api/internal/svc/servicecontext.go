package svc

import (
	zrpcout "github.com/zeromicro/go-zero/zrpc"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/interceptors/clientinterceptors"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-middleware/user/rpc/client/userservice"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/api/internal/config"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/common/zrpc"
)

type ServiceContext struct {
	Config config.Config

	Validator *utils.Validator

	DispatcherRpc *zrpc.DispatcherRpc
	ManagerRpc    *zrpc.ManagerRPC
	UserRpc       userservice.UserService
}

func NewServiceContext(c config.Config) *ServiceContext {
	return &ServiceContext{
		Config:        c,
		Validator:     utils.NewValidator(c.Validator.Locale),
		DispatcherRpc: zrpc.NewDispatcherRpc(c.<PERSON>atcher),
		ManagerRpc:    zrpc.NewManagerRPC(c.Manager),
		UserRpc: userservice.NewUserService(
			zrpcout.MustNewClient(
				c.User, clientinterceptors.UnaryUserInfoClientOption(),
			),
		),
	}
}
