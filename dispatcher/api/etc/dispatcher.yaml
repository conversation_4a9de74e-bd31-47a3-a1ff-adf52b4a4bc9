Name: api.dispatcher
Host: 0.0.0.0
Port: 20301
Verbose: true
Timeout: 0

Log:
  ServiceName: api.dispatcher
  Encoding: plain
  Level: info
  Path: /app/logs/dispatcher

#Prometheus:
#  Host: 0.0.0.0
#  Port: 20321
#  Path: /metrics
#
#Telemetry:
#  Name: api.dispatcher
#  Endpoint: http://127.0.0.1:14268/api/traces
#  Sampler: 1.0
#  Batcher: jaeger
#
#DevServer:
#  Enabled: true
#  Port: 20331

Validator:
  Locale: zh

Dispatcher:
#  Etcd:
#    Hosts:
#      - 127.0.0.1:2379
#    Key: rpc.dispatcher
  Endpoints:
    - 127.0.0.1:20311
  NonBlock: true
  Timeout: 0

Manager:
#  Etcd:
#    Hosts:
#      - 127.0.0.1:2379
#    Key: rpc.manager
  Endpoints:
    - 127.0.0.1:20211
  NonBlock: true
  Timeout: 0

User:
  #  Etcd:
  #    Hosts:
  #      - 127.0.0.1:2379
  #    Key: rpc.user
  Endpoints:
    - 127.0.0.1:10111
  NonBlock: true
  Timeout: 0
