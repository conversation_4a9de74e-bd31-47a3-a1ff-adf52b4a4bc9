package model

import (
	"fmt"
	"testing"

	"github.com/Masterminds/squirrel"
)

func TestName(t *testing.T) {
	fmt.Println(taskInfoRecordInsertFields)
	data := &TaskInfoRecord{}
	sql, a, err := squirrel.Insert("task_info_record").Columns(taskInfoRecordInsertFields...).Values(
		data.ProjectId, data.PlanId, data.PlanName, data.TriggerMode, data.TaskId, data.PriorityType,
		data.TaskExecuteStatus, data.TaskExecutedResult, data.TotalCase, data.FinishedCase, data.SuccessCase,
		data.TotalSuite, data.FinishedSuite, data.SuccessSuite, data.ExecutedBy, data.CostTime, data.WaitTime,
		data.StartedAt, data.EndedAt, data.UpdateAt,
	).ToSql()
	fmt.Println(sql)
	fmt.Println(a)
	fmt.Println(err)
}
