// Code generated by goctl. DO NOT EDIT.

package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlc"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
)

var (
	taskInfoRecordTableName           = "`task_info_record`"
	taskInfoRecordFieldNames          = builder.RawFieldNames(&TaskInfoRecord{})
	taskInfoRecordRows                = strings.Join(taskInfoRecordFieldNames, ",")
	taskInfoRecordRowsExpectAutoSet   = strings.Join(stringx.Remove(taskInfoRecordFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), ",")
	taskInfoRecordRowsWithPlaceHolder = strings.Join(stringx.Remove(taskInfoRecordFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), "=?,") + "=?"

	cacheDispatcherTaskInfoRecordIdPrefix = "cache:dispatcher:taskInfoRecord:id:"
)

type (
	taskInfoRecordModel interface {
		Insert(ctx context.Context, session sqlx.Session, data *TaskInfoRecord) (sql.Result, error)
		FindOne(ctx context.Context, id int64) (*TaskInfoRecord, error)
		Update(ctx context.Context, session sqlx.Session, data *TaskInfoRecord) (sql.Result, error)
		Delete(ctx context.Context, session sqlx.Session, id int64) error

		LogicDelete(ctx context.Context, session sqlx.Session, id int64) error
	}

	defaultTaskInfoRecordModel struct {
		sqlc.CachedConn
		table string
	}

	TaskInfoRecord struct {
		Id                 int64          `db:"id"`                   // 自增id
		ProjectId          string         `db:"project_id"`           // 项目id
		PlanId             string         `db:"plan_id"`              // 计划id
		PlanName           string         `db:"plan_name"`            // 计划名称
		TriggerMode        string         `db:"trigger_mode"`         // 触发模式
		TaskId             string         `db:"task_id"`              // 任务id
		PriorityType       int64          `db:"priority_type"`        // 优先级策略
		TaskExecuteStatus  int64          `db:"task_execute_status"`  // 0排队中,1执行中,2已完成,3已停止
		TaskExecutedResult int64          `db:"task_executed_result"` // 执行结果(1成功,2失败,3异常)
		TotalCase          int64          `db:"total_case"`           // 测试用例总数
		FinishedCase       int64          `db:"finished_case"`        // 执行完成的测试用例数
		SuccessCase        int64          `db:"success_case"`         // 执行成功的测试用例数
		TotalSuite         int64          `db:"total_suite"`          // 测试集合总数
		FinishedSuite      int64          `db:"finished_suite"`       // 执行完的测试集合数
		SuccessSuite       int64          `db:"success_suite"`        // 执行成功的测试集合数
		ExecutedBy         string         `db:"executed_by"`          // 执行人id
		CreatedAt          time.Time      `db:"created_at"`           // 创建时间(戳)
		CostTime           int64          `db:"cost_time"`            // 执行耗时(豪秒)
		WaitTime           int64          `db:"wait_time"`            // 排队耗时
		StartedAt          int64          `db:"started_at"`           // 开始执行的时间(戳)
		EndedAt            sql.NullInt64  `db:"ended_at"`             // 结束执行的时间(戳)
		UpdateAt           sql.NullInt64  `db:"update_at"`            // 更新时间
		PlanMetaData       sql.NullString `db:"plan_meta_data"`       // 计划元数据
	}
)

func newTaskInfoRecordModel(conn sqlx.SqlConn, c cache.CacheConf, opts ...cache.Option) *defaultTaskInfoRecordModel {
	return &defaultTaskInfoRecordModel{
		CachedConn: sqlc.NewConn(conn, c, opts...),
		table:      "`task_info_record`",
	}
}

func (m *defaultTaskInfoRecordModel) Delete(ctx context.Context, session sqlx.Session, id int64) error {
	dispatcherTaskInfoRecordIdKey := fmt.Sprintf("%s%v", cacheDispatcherTaskInfoRecordIdPrefix, id)
	_, err := m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("delete from %s where `id` = ?", m.table)
		if session != nil {
			return session.ExecCtx(ctx, query, id)
		}
		return conn.ExecCtx(ctx, query, id)
	}, dispatcherTaskInfoRecordIdKey)
	return err
}

func (m *defaultTaskInfoRecordModel) LogicDelete(ctx context.Context, session sqlx.Session, id int64) error {
	dispatcherTaskInfoRecordIdKey := fmt.Sprintf("%s%v", cacheDispatcherTaskInfoRecordIdPrefix, id)
	_, err := m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set `deleted` = ?, `deleted_at` = ? where `id` = ?", m.table)
		if session != nil {
			return session.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
		}
		return conn.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
	}, dispatcherTaskInfoRecordIdKey)
	return err
}

func (m *defaultTaskInfoRecordModel) FindOne(ctx context.Context, id int64) (*TaskInfoRecord, error) {
	dispatcherTaskInfoRecordIdKey := fmt.Sprintf("%s%v", cacheDispatcherTaskInfoRecordIdPrefix, id)
	var resp TaskInfoRecord
	err := m.QueryRowCtx(ctx, &resp, dispatcherTaskInfoRecordIdKey, func(ctx context.Context, conn sqlx.SqlConn, v any) error {
		query := fmt.Sprintf("select %s from %s where `id` = ? and `deleted` = ? limit 1", taskInfoRecordRows, m.table)
		return conn.QueryRowCtx(ctx, v, query, id, constants.NotDeleted)
	})
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultTaskInfoRecordModel) Insert(ctx context.Context, session sqlx.Session, data *TaskInfoRecord) (sql.Result, error) {
	dispatcherTaskInfoRecordIdKey := fmt.Sprintf("%s%v", cacheDispatcherTaskInfoRecordIdPrefix, data.Id)

	return m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", m.table, taskInfoRecordRowsExpectAutoSet)
		if session != nil {
			return session.ExecCtx(ctx, query, data.ProjectId, data.PlanId, data.PlanName, data.TriggerMode, data.TaskId, data.PriorityType, data.TaskExecuteStatus, data.TaskExecutedResult, data.TotalCase, data.FinishedCase, data.SuccessCase, data.TotalSuite, data.FinishedSuite, data.SuccessSuite, data.ExecutedBy, data.CostTime, data.WaitTime, data.StartedAt, data.EndedAt, data.UpdateAt, data.PlanMetaData)
		}
		return conn.ExecCtx(ctx, query, data.ProjectId, data.PlanId, data.PlanName, data.TriggerMode, data.TaskId, data.PriorityType, data.TaskExecuteStatus, data.TaskExecutedResult, data.TotalCase, data.FinishedCase, data.SuccessCase, data.TotalSuite, data.FinishedSuite, data.SuccessSuite, data.ExecutedBy, data.CostTime, data.WaitTime, data.StartedAt, data.EndedAt, data.UpdateAt, data.PlanMetaData)
	}, dispatcherTaskInfoRecordIdKey)
}

func (m *defaultTaskInfoRecordModel) Update(ctx context.Context, session sqlx.Session, data *TaskInfoRecord) (sql.Result, error) {

	dispatcherTaskInfoRecordIdKey := fmt.Sprintf("%s%v", cacheDispatcherTaskInfoRecordIdPrefix, data.Id)

	return m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, taskInfoRecordRowsWithPlaceHolder)
		if session != nil {
			return session.ExecCtx(ctx, query, data.ProjectId, data.PlanId, data.PlanName, data.TriggerMode, data.TaskId, data.PriorityType, data.TaskExecuteStatus, data.TaskExecutedResult, data.TotalCase, data.FinishedCase, data.SuccessCase, data.TotalSuite, data.FinishedSuite, data.SuccessSuite, data.ExecutedBy, data.CostTime, data.WaitTime, data.StartedAt, data.EndedAt, data.UpdateAt, data.PlanMetaData, data.Id)
		}
		return conn.ExecCtx(ctx, query, data.ProjectId, data.PlanId, data.PlanName, data.TriggerMode, data.TaskId, data.PriorityType, data.TaskExecuteStatus, data.TaskExecutedResult, data.TotalCase, data.FinishedCase, data.SuccessCase, data.TotalSuite, data.FinishedSuite, data.SuccessSuite, data.ExecutedBy, data.CostTime, data.WaitTime, data.StartedAt, data.EndedAt, data.UpdateAt, data.PlanMetaData, data.Id)
	}, dispatcherTaskInfoRecordIdKey)
}

func (m *defaultTaskInfoRecordModel) formatPrimary(primary any) string {
	return fmt.Sprintf("%s%v", cacheDispatcherTaskInfoRecordIdPrefix, primary)
}

func (m *defaultTaskInfoRecordModel) queryPrimary(ctx context.Context, conn sqlx.SqlConn, v, primary any) error {
	query := fmt.Sprintf("select %s from %s where `id` = ? limit 1", taskInfoRecordRows, m.table)
	return conn.QueryRowCtx(ctx, v, query, primary)
}

func (m *defaultTaskInfoRecordModel) tableName() string {
	return m.table
}
