package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"

	"github.com/Masterminds/squirrel"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/types"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/db"
)

var (
	_ TaskInfoRecordModel = (*customTaskInfoRecordModel)(nil)

	taskInfoRecordInsertFields = stringx.Remove(
		taskInfoRecordFieldNames, "`id`", "`create_time`", "`update_time`", "`deleted_by`", "`created_at`",
		"`updated_at`", "`deleted_at`",
	)
	taskInfoRecordRowsExpectAutoSetV2 = strings.Join(taskInfoRecordInsertFields, ",")

	cacheDispatcherTaskInfoIdPrefix     = "cache:Dispatcher:TaskInfo:id:"
	cacheDispatcherTaskInfoTaskIdPrefix = "cache:Dispatcher:TaskInfo:TaskId:"
)

type (
	// TaskInfoRecordModel is an interface to be customized, add more methods here,
	// and implement the added methods in customTaskInfoRecordModel.
	TaskInfoRecordModel interface {
		taskInfoRecordModel
		types.DBModel

		Trans(ctx context.Context, fn func(context context.Context, session sqlx.Session) error) error
		InsertBuilder(data *TaskInfoRecord) squirrel.InsertBuilder
		UpdateBuilder(data *TaskInfoRecord) squirrel.UpdateBuilder
		SelectBuilder() squirrel.SelectBuilder
		SelectCountBuilder() squirrel.SelectBuilder
		InsertTX(ctx context.Context, session sqlx.Session, data *TaskInfoRecord) (
			sql.Result, error,
		)
		BatchInsert(
			ctx context.Context, session sqlx.Session, dataList []*TaskInfoRecord,
		) (int64, error)
		FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error)
		FindNoCacheByQuery(ctx context.Context, selectBuilder squirrel.SelectBuilder) ([]*TaskInfoRecord, error)
	}

	customTaskInfoRecordModel struct {
		*defaultTaskInfoRecordModel
	}
)

// NewTaskInfoRecordModel returns a model for the database table.
func NewTaskInfoRecordModel(conn sqlx.SqlConn, c cache.CacheConf, opts ...cache.Option) TaskInfoRecordModel {
	return &customTaskInfoRecordModel{
		defaultTaskInfoRecordModel: newTaskInfoRecordModel(conn, c, opts...),
	}
}

func (m *customTaskInfoRecordModel) Table() string {
	return m.table
}

func (m *customTaskInfoRecordModel) Fields() []string {
	return taskInfoRecordFieldNames
}

func (m *customTaskInfoRecordModel) Trans(
	ctx context.Context, fn func(context context.Context, session sqlx.Session) error,
) error {
	return m.TransactCtx(ctx, fn)
}

func (m *customTaskInfoRecordModel) InsertBuilder(data *TaskInfoRecord) squirrel.InsertBuilder {
	return squirrel.Insert(m.table).Columns(taskInfoRecordInsertFields...).Values(
		data.ProjectId, data.PlanId, data.PlanName, data.TriggerMode, data.TaskId, data.PriorityType,
		data.TaskExecuteStatus, data.TaskExecutedResult, data.TotalCase, data.FinishedCase, data.SuccessCase,
		data.TotalSuite, data.FinishedSuite, data.SuccessSuite, data.ExecutedBy, data.CostTime, data.WaitTime,
		data.StartedAt, data.EndedAt, data.UpdateAt,
	)
}

func (m *customTaskInfoRecordModel) UpdateBuilder(data *TaskInfoRecord) squirrel.UpdateBuilder {
	eq := squirrel.Eq{
		// TODO: fill the update values
	}
	return squirrel.Update(m.table).SetMap(eq).Where("`id` = ?", data.Id)
}

func (m *customTaskInfoRecordModel) SelectBuilder() squirrel.SelectBuilder {
	return squirrel.Select(taskInfoRecordFieldNames...).Where("`deleted` = ?", constants.NotDeleted).From(m.table)
}

func (m *customTaskInfoRecordModel) SelectCountBuilder() squirrel.SelectBuilder {
	return squirrel.Select("count(*)").Where("`deleted` = ?", constants.NotDeleted).From(m.table)
}

func (m *customTaskInfoRecordModel) FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error) {
	query, values, err := countBuilder.ToSql()
	if err != nil {
		return 0, err
	}

	var resp int64
	err = m.QueryRowNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return 0, err
	}
}

func (m *customTaskInfoRecordModel) FindNoCacheByQuery(
	ctx context.Context, selectBuilder squirrel.SelectBuilder,
) ([]*TaskInfoRecord, error) {
	query, values, err := selectBuilder.ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*TaskInfoRecord
	err = m.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

func (m *customTaskInfoRecordModel) InsertTX(ctx context.Context, session sqlx.Session, data *TaskInfoRecord) (
	sql.Result, error,
) {
	dispatcherTaskInfoIdKey := fmt.Sprintf("%s%v", cacheDispatcherTaskInfoIdPrefix, data.Id)
	dispatcherTaskInfoTaskIdKey := fmt.Sprintf("%s:%v", cacheDispatcherTaskInfoTaskIdPrefix, data.TaskId)

	return m.ExecCtx(
		ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
			query, values, err := m.InsertBuilder(data).ToSql()
			if err != nil {
				return nil, err
			}
			if session != nil {
				return conn.ExecCtx(ctx, query, values...)
			}
			return conn.ExecCtx(ctx, query, values...)
		}, dispatcherTaskInfoIdKey, dispatcherTaskInfoTaskIdKey,
	)
}

func (m *customTaskInfoRecordModel) BatchInsert(
	ctx context.Context, session sqlx.Session, dataList []*TaskInfoRecord,
) (int64, error) {
	err := db.BatchSplit(
		dataList, db.DefaultBatchSplitSize, func(dataList []*TaskInfoRecord) error {
			valueStrings := make([]string, 0, len(dataList))
			valueArgs := make([]any, 0)
			for _, data := range dataList {
				valueStrings = append(valueStrings, "(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)")
				valueArgs = append(valueArgs, data.ProjectId)
				valueArgs = append(valueArgs, data.PlanId)
				valueArgs = append(valueArgs, data.PlanName)
				valueArgs = append(valueArgs, data.TriggerMode)
				valueArgs = append(valueArgs, data.TaskId)
				valueArgs = append(valueArgs, data.PriorityType)
				valueArgs = append(valueArgs, data.TaskExecuteStatus)
				valueArgs = append(valueArgs, data.TaskExecutedResult)
				valueArgs = append(valueArgs, data.TotalCase)
				valueArgs = append(valueArgs, data.FinishedCase)
				valueArgs = append(valueArgs, data.SuccessCase)
				valueArgs = append(valueArgs, data.TotalSuite)
				valueArgs = append(valueArgs, data.FinishedSuite)
				valueArgs = append(valueArgs, data.SuccessSuite)
				valueArgs = append(valueArgs, data.ExecutedBy)
				valueArgs = append(valueArgs, data.CostTime)
				valueArgs = append(valueArgs, data.WaitTime)
				valueArgs = append(valueArgs, data.StartedAt)
				valueArgs = append(valueArgs, data.EndedAt.Int64)
				valueArgs = append(valueArgs, data.UpdateAt.Int64)
				valueArgs = append(valueArgs, data.PlanMetaData.String)
			}
			result, err := m.ExecCtx(
				ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
					stmt := fmt.Sprintf(
						"insert into %s (%s) values  %s", m.table, taskInfoRecordRowsExpectAutoSetV2,
						strings.Join(valueStrings, ","),
					)
					if session != nil {
						return session.ExecCtx(ctx, stmt, valueArgs...)
					}
					return conn.ExecCtx(ctx, stmt, valueArgs...)
				},
			)
			if err != nil {
				return err
			}
			_, err = result.RowsAffected()
			if err != nil {
				return err
			}
			return nil
		},
	)
	return 0, err
}
