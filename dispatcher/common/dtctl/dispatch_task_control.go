package dtctl

import (
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/pkg/errors"
	"github.com/redis/go-redis/v9"
	"github.com/zeromicro/go-zero/core/logx"

	commonpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/utils"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/rpc/pb"
	managerpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

const (
	CONST_SCAN_COUNT_ONE_TIME = 16
)

type DispatchTaskMember struct {
	ComponentId        string                         `json:"component_id"`
	ComponentType      managerpb.ApiExecutionDataType `json:"component_type"`
	Version            string                         `json:"version,omitempty"`
	ComponentExecuteId string                         `json:"component_execute_id,omitempty"`
	Index              int                            `json:"index"` // 所处的child位置
	Data               *managerpb.ApiExecutionData    `json:"data,omitempty"`
}

func GetMemberKey(member *DispatchTaskMember) string {
	return fmt.Sprintf(
		"%s::%s::%s::%s", member.ComponentType.String(), member.ComponentId, member.Version, member.ComponentExecuteId,
	)
}

func ParseMemberKey(key string) (member *DispatchTaskMember, err error) {
	items := strings.Split(key, "::")
	if len(items) != 4 {
		return nil, fmt.Errorf("无效的memberKey: %s", key)
	}

	member = &DispatchTaskMember{
		ComponentId:        items[1],
		ComponentType:      managerpb.ApiExecutionDataType(managerpb.ApiExecutionDataType_value[items[0]]),
		Version:            items[2],
		ComponentExecuteId: items[3],
	}
	return
}

// ScanDispatchTaskControls 扫描redis，提取出存在的DispatchTaskControl
func ScanDispatchTaskControls(
	ctx context.Context, cli redis.UniversalClient, count int64,
) (recorders []*DispatchTaskControl, err error) {
	if count == 0 || count > CONST_SCAN_COUNT_ONE_TIME {
		count = CONST_SCAN_COUNT_ONE_TIME
	}

	recorders = make([]*DispatchTaskControl, 0, 64)
	var cursor uint64 = 0
	for {
		var curkeys []string
		curkeys, cursor, err = cli.ScanType(
			ctx, cursor, fmt.Sprintf("%s::*", utils.TaskInfoPrefix), count, "hash",
		).Result()
		if err != nil {
			return nil, err
		}

		for _, key := range curkeys {
			trimkey := strings.TrimPrefix(key, fmt.Sprintf("%s::", utils.TaskInfoPrefix))
			mgrMember, err := ParseMemberKey(trimkey)
			if err != nil {
				return nil, err
			}
			recorders = append(recorders, NewDispatchTaskControl(ctx, cli, mgrMember))
		}

		if cursor == 0 {
			break
		}
	}

	return recorders, nil
}

func SetScanFinishTime(ctx context.Context, cli redis.UniversalClient, t time.Time) (err error) {
	return cli.Set(ctx, utils.TaskScanTime, t.Unix(), 0).Err()
}

func GetScanFinishTime(ctx context.Context, cli redis.UniversalClient) (t time.Time, err error) {
	if cli.Exists(ctx, utils.TaskScanTime).Val() == 0 {
		// 如果key不存在，则为第一次写入
		t = time.Now().Add(-time.Hour * 24)
		return t, nil
	}

	unixsec, err := cli.Get(ctx, utils.TaskScanTime).Int64()
	if err != nil {
		return t, err
	}

	t = time.Unix(unixsec, 0)
	return t, nil
}

// DispatchTaskControl 记录集合/计划/接口用例的执行结果
type DispatchTaskControl struct {
	logx.Logger
	ctx context.Context
	cli redis.UniversalClient

	mgrMember *DispatchTaskMember

	task_id      string
	execute_id   string
	project_id   string
	execute_type string
	total        int64
	starttime    int64 // unix秒级时间戳
	finish       int64
	priority     commonpb.PriorityType
}

func (recorder *DispatchTaskControl) CreateInfo(
	taskID, projectID, executeType string, total int64, priorityType commonpb.PriorityType,
) (err error) {
	starttime := time.Now().Unix()
	key := utils.GenTaskInfoKey(GetMemberKey(recorder.mgrMember))
	b, err := recorder.cli.HMSet(
		recorder.ctx, key,
		utils.TaskInfoKeyTaskId, taskID,
		utils.TaskInfoKeyExecuteId, recorder.mgrMember.ComponentExecuteId,
		utils.TaskInfoKeyProjectId, projectID,
		utils.TaskInfoKeyExecuteType, executeType,
		utils.TaskInfoKeyTotal, total,
		utils.TaskInfoKeyStarttime, starttime,
		utils.TaskInfoKeyPriorityType, int32(priorityType),
	).Result()
	if err != nil {
		return err
	}

	if !b {
		return fmt.Errorf("创建执行信息失败")
	}

	recorder.total = total
	recorder.starttime = starttime
	return nil
}

func NewDispatchTaskControl(
	ctx context.Context, cli redis.UniversalClient, mgrMember *DispatchTaskMember,
) *DispatchTaskControl {
	recorder := &DispatchTaskControl{
		Logger:    logx.WithContext(ctx),
		ctx:       ctx,
		cli:       cli,
		mgrMember: mgrMember,
	}
	return recorder
}

func (recorder *DispatchTaskControl) CreateMemberList(member *DispatchTaskMember) (err error) {
	mgrkey := utils.GenTaskMemberKey(GetMemberKey(recorder.mgrMember))
	memkey := GetMemberKey(member)
	_, err = recorder.cli.SAdd(recorder.ctx, mgrkey, memkey).Result()
	return
}

func (recorder *DispatchTaskControl) GetAllMembers() (members []*DispatchTaskMember, err error) {
	mgrkey := utils.GenTaskMemberKey(GetMemberKey(recorder.mgrMember))
	memkeys, err := recorder.cli.SMembers(recorder.ctx, mgrkey).Result()
	if err != nil {
		return
	}

	members = make([]*DispatchTaskMember, len(memkeys))
	for idx, mkey := range memkeys {
		member, err := ParseMemberKey(mkey)
		if err != nil {
			return nil, err
		}
		members[idx] = member
	}
	return members, nil
}

func (recorder *DispatchTaskControl) TaskId() (taskID string, err error) {
	if recorder.task_id != "" {
		return recorder.task_id, nil
	}

	infokey := utils.GenTaskInfoKey(GetMemberKey(recorder.mgrMember))
	recorder.task_id, err = recorder.cli.HGet(recorder.ctx, infokey, utils.TaskInfoKeyTaskId).Result()
	if err != nil {
		return "", err
	}

	if recorder.task_id == "" {
		return "", fmt.Errorf("task_id值为空")
	}

	return recorder.task_id, nil
}

func (recorder *DispatchTaskControl) TaskIdIgnoreError() (taskID string) {
	taskID, _ = recorder.TaskId()
	return taskID
}

func (recorder *DispatchTaskControl) ExecuteId() (executeID string, err error) {
	if recorder.execute_id != "" {
		return recorder.execute_id, nil
	}

	infokey := utils.GenTaskInfoKey(GetMemberKey(recorder.mgrMember))
	recorder.execute_id, err = recorder.cli.HGet(recorder.ctx, infokey, utils.TaskInfoKeyExecuteId).Result()
	if err != nil {
		return "", err
	}

	if recorder.execute_id == "" {
		return "", fmt.Errorf("execute_id值为空")
	}

	return recorder.execute_id, nil
}

func (recorder *DispatchTaskControl) ExecuteIdIgnoreError() (executeID string) {
	executeID, _ = recorder.ExecuteId()
	return executeID
}

func (recorder *DispatchTaskControl) ProjectId() (projectID string, err error) {
	if recorder.project_id != "" {
		return recorder.project_id, nil
	}

	infokey := utils.GenTaskInfoKey(GetMemberKey(recorder.mgrMember))
	recorder.project_id, err = recorder.cli.HGet(recorder.ctx, infokey, utils.TaskInfoKeyProjectId).Result()
	if err != nil {
		return "", err
	}

	if recorder.project_id == "" {
		return "", fmt.Errorf("project_id值为空")
	}

	return recorder.project_id, nil
}

func (recorder *DispatchTaskControl) ProjectIdIgnoreError() (projectID string) {
	projectID, _ = recorder.ProjectId()
	return projectID
}

func (recorder *DispatchTaskControl) ExecuteType() (executeType string, err error) {
	if recorder.execute_type != "" {
		return recorder.execute_type, nil
	}

	infokey := utils.GenTaskInfoKey(GetMemberKey(recorder.mgrMember))
	recorder.execute_type, err = recorder.cli.HGet(recorder.ctx, infokey, utils.TaskInfoKeyExecuteType).Result()
	if err != nil {
		return "", err
	}

	if recorder.execute_type == "" {
		return "", fmt.Errorf("execute_type值为空")
	}

	return recorder.execute_type, nil
}

func (recorder *DispatchTaskControl) ExecuteTypeIgnoreError() (executeType string) {
	executeType, _ = recorder.ExecuteType()
	return executeType
}

func (recorder *DispatchTaskControl) Total() (total int64, err error) {
	if recorder.total > 0 {
		return recorder.total, nil
	}

	infokey := utils.GenTaskInfoKey(GetMemberKey(recorder.mgrMember))
	recorder.total, err = recorder.cli.HGet(recorder.ctx, infokey, utils.TaskInfoKeyTotal).Int64()
	if err != nil {
		return 0, err
	}
	if recorder.total == 0 {
		return 0, errors.Errorf("total值为0")
	}

	return recorder.total, nil
}

func (recorder *DispatchTaskControl) Starttime() (unixsec int64, err error) {
	if recorder.starttime > 0 {
		return recorder.starttime, nil
	}

	infokey := utils.GenTaskInfoKey(GetMemberKey(recorder.mgrMember))
	unixsec, err = recorder.cli.HGet(recorder.ctx, infokey, utils.TaskInfoKeyStarttime).Int64()
	if err != nil {
		return 0, err
	}

	if unixsec == 0 {
		return 0, fmt.Errorf("unix时间戳为0")
	}
	return unixsec, nil
}

func (recorder *DispatchTaskControl) PriorityType() (ty commonpb.PriorityType, err error) {
	if recorder.priority > 0 {
		return recorder.priority, nil
	}

	infokey := utils.GenTaskInfoKey(GetMemberKey(recorder.mgrMember))
	v, err := recorder.cli.HGet(recorder.ctx, infokey, utils.TaskInfoKeyPriorityType).Int64()
	if err != nil {
		return 0, err
	}
	priorityType := commonpb.PriorityType(v)
	recorder.priority = priorityType
	return priorityType, nil
}

// AddMemberState 记录成员的执行结果状态
func (recorder *DispatchTaskControl) AddMemberState(state pb.ComponentState, member *DispatchTaskMember) (err error) {
	// 避免未有info就添加成员
	if _, err = recorder.Total(); err != nil {
		return errors.Errorf("获取total值失败: %+v", err)
	}

	listkey := utils.GenTaskStatusKey(GetMemberKey(recorder.mgrMember))

	// 使用lua的原因: 避免并发执行时，多个协程ZCARD获取到一样的值
	lua := redis.NewScript(
		`
		local listkey = KEYS[1]
		local score = ARGV[1]
		local memberkey = ARGV[2]
	
		local succ = redis.call('ZADD', listkey, score, memberkey)
		if succ == 0 then 
			return 0
		end

		local finish = redis.call('ZCARD',  listkey)
		return finish
	`,
	)

	memberkey := GetMemberKey(member)
	recorder.finish, err = lua.Run(recorder.ctx, recorder.cli, []string{listkey}, int64(state), memberkey).Int64()
	if err != nil {
		return err
	}

	if recorder.finish == 0 {
		return errors.Errorf("重复写入相同成员: %s", memberkey)
	}

	return nil
}

func (recorder *DispatchTaskControl) FinishCount() (total int64, err error) {
	if recorder.finish > 0 {
		return recorder.finish, nil
	}

	listkey := utils.GenTaskStatusKey(GetMemberKey(recorder.mgrMember))
	recorder.finish, err = recorder.cli.ZCard(recorder.ctx, listkey).Result()
	if err != nil {
		return
	}

	return recorder.finish, nil
}

func (recorder *DispatchTaskControl) Finished() bool {
	total, err := recorder.Total()
	if err != nil {
		return false
	}

	finish, err := recorder.FinishCount()
	if err != nil {
		return false
	}

	if total != finish {
		return false
	}

	return true
}

func (recorder *DispatchTaskControl) FailureCount() (count int64, err error) {
	listkey := utils.GenTaskStatusKey(GetMemberKey(recorder.mgrMember))
	count, err = recorder.cli.ZCount(
		recorder.ctx, listkey, fmt.Sprintf("%d", pb.ComponentState_Failure), "+inf",
	).Result()
	if err != nil {
		return
	}

	return count, nil
}

func (recorder *DispatchTaskControl) GetFinishedMember() (members []*DispatchTaskMember, err error) {
	listkey := utils.GenTaskStatusKey(GetMemberKey(recorder.mgrMember))
	mkeys, err := recorder.cli.ZRange(recorder.ctx, listkey, 0, -1).Result()
	if err != nil {
		return nil, err
	}

	members = make([]*DispatchTaskMember, len(mkeys))
	for idx, mkey := range mkeys {
		member, err := ParseMemberKey(mkey)
		if err != nil {
			return nil, err
		}
		members[idx] = member
	}
	return members, nil
}

func (recorder *DispatchTaskControl) Clear() (err error) {
	mgrsubkey := GetMemberKey(recorder.mgrMember)
	statuskey := utils.GenTaskStatusKey(mgrsubkey)
	infokey := utils.GenTaskInfoKey(mgrsubkey)
	memerkey := utils.GenTaskMemberKey(mgrsubkey)
	_, err = recorder.cli.Del(recorder.ctx, statuskey, infokey, memerkey).Result()
	return
}

func (recorder *DispatchTaskControl) GetMgrMember() *DispatchTaskMember {
	return recorder.mgrMember
}
