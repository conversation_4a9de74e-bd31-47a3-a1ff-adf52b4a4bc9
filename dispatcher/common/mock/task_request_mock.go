package mock

import (
	commonpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/utils"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/common/dtctl"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/rpc/pb"
	managerpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/userinfo"
)

func Mock_GeneralConfig(project_id string) *commonpb.GeneralConfig {
	return &commonpb.GeneralConfig{
		ProjectId: project_id,
		ConfigId:  utils.GenGeneralConfigId(),
		Name:      "unit-test-general-config",
	}
}

func Mock_AccountConfig(project_id string) []*commonpb.AccountConfig {
	account := make([]*commonpb.AccountConfig, 1)
	account[0] = &commonpb.AccountConfig{
		ProjectId:    project_id,
		ConfigId:     utils.GenAccountConfigId(),
		Name:         "unit-test-account-config",
		ProductType:  17,
		ProductName:  "unit-test-product",
		PoolEnvTable: "pool-env-table",
		PoolEnvName:  "pool-env-name",
	}
	return account
}

func Mock_DistributeReq(typ managerpb.ApiExecutionDataType) *pb.DistributeReq {
	req := &pb.DistributeReq{
		TriggerMode: commonpb.TriggerMode_MANUAL,
		ProjectId:   utils.GenProjectId(),
		TaskId:      utils.GenTaskId(),
		ExecuteType: typ,
		User:        "unit-test",
		UserId:      userinfo.Admin().Account,
	}

	switch typ {
	case managerpb.ApiExecutionDataType_INTERFACE_DOCUMENT:
		req.DistributeType = pb.DistributeType_DistributeType_INTERFACE_DOCUMENT
		req.Data = &pb.DistributeReq_InterfaceDocument{
			InterfaceDocument: &pb.InterfaceDocumentDistributeData{
				InterfaceDocumentId:        utils.GenInterfaceDocumentId(),
				InterfaceDocumentExecuteId: utils.GenExecuteId(),
			},
		}
	case managerpb.ApiExecutionDataType_API_SUITE:
		req.DistributeType = pb.DistributeType_DistributeType_API_SUITE
		req.Data = &pb.DistributeReq_Suite{
			Suite: &pb.SuiteDistributeData{
				SuiteId:        utils.GenSuiteId(),
				SuiteExecuteId: utils.GenExecuteId(),
			},
		}
	case managerpb.ApiExecutionDataType_API_PLAN:
		req.DistributeType = pb.DistributeType_DistributeType_API_PLAN
		req.Data = &pb.DistributeReq_Plan{
			Plan: &pb.PlanDistributeData{
				PlanId:        utils.GenPlanId(),
				PlanExecuteId: utils.GenExecuteId(),
			},
		}
	}

	return req
}

func Mock_CallbackMqReq(parent, current *dtctl.DispatchTaskMember, state pb.ComponentState) *pb.CallbackReq {
	callback_type := pb.CallbackType_CallbackType_UNKNOWN
	switch current.ComponentType {
	case managerpb.ApiExecutionDataType_API_CASE:
		callback_type = pb.CallbackType_CallbackType_API_CASE
	case managerpb.ApiExecutionDataType_API_SUITE:
		callback_type = pb.CallbackType_CallbackType_API_SUITE
	case managerpb.ApiExecutionDataType_INTERFACE_CASE:
		callback_type = pb.CallbackType_CallbackType_INTERFACE_CASE
	case managerpb.ApiExecutionDataType_INTERFACE_DOCUMENT:
		callback_type = pb.CallbackType_CallbackType_INTERFACE_DOCUMENT
	}

	req := &pb.CallbackReq{
		TriggerMode:  commonpb.TriggerMode_MANUAL,
		ProjectId:    utils.GenProjectId(),
		TaskId:       utils.GenTaskId(),
		ExecuteType:  parent.ComponentType,
		CallbackType: callback_type,
	}

	switch callback_type {
	case pb.CallbackType_CallbackType_API_CASE:
		req.Data = &pb.CallbackReq_Case{
			Case: &pb.CaseCallbackData{
				SuiteId:        parent.ComponentId,
				SuiteExecuteId: parent.ComponentExecuteId,
				CaseId:         current.ComponentId,
				CaseExecuteId:  current.ComponentExecuteId,
				CaseState:      state,
				Version:        current.Version,
			},
		}
	case pb.CallbackType_CallbackType_INTERFACE_CASE:
		req.Data = &pb.CallbackReq_InterfaceCase{
			InterfaceCase: &pb.InterfaceCaseCallbackData{
				InterfaceId:            parent.ComponentId,
				InterfaceExecuteId:     parent.ComponentExecuteId,
				InterfaceCaseId:        current.ComponentId,
				InterfaceCaseExecuteId: current.ComponentExecuteId,
				InterfaceCaseState:     state,
				Version:                current.Version,
			},
		}
	case pb.CallbackType_CallbackType_API_SUITE:
		req.Data = &pb.CallbackReq_Suite{
			Suite: &pb.SuiteCallbackData{
				SuiteId:        current.ComponentId,
				SuiteExecuteId: current.ComponentExecuteId,
				PlanId:         parent.ComponentId,
				PlanExecuteId:  parent.ComponentExecuteId,
				SuiteState:     state,
			},
		}
	case pb.CallbackType_CallbackType_INTERFACE_DOCUMENT:
		req.Data = &pb.CallbackReq_InterfaceDocument{
			InterfaceDocument: &pb.InterfaceDocumentCallbackData{
				InterfaceId:        current.ComponentId,
				InterfaceExecuteId: current.ComponentExecuteId,
				PlanId:             parent.ComponentId,
				PlanExecuteId:      parent.ComponentExecuteId,
				InterfaceState:     state,
			},
		}
	}

	return req
}
