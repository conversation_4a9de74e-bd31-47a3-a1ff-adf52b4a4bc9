package zrpc

import (
	"context"
	"flag"

	"github.com/zeromicro/go-zero/zrpc"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/interceptors/clientinterceptors"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/client/reporter"
)

type ReporterRPC struct {
	conf zrpc.RpcClientConf

	Name string
	Cli  reporter.Reporter
}

func NewReporterRPC(conf zrpc.RpcClientConf) *ReporterRPC {
	return &ReporterRPC{
		conf: conf,
		Name: conf.Etcd.Key,
		Cli:  reporter.NewReporter(zrpc.MustNewClient(conf, clientinterceptors.UnaryUserInfoClientOption())),
	}
}

func (cli *ReporterRPC) Create(ctx context.Context, req *reporter.PutRecordRequest) (
	*reporter.CreateRecordResponse, error,
) {
	if flag.Lookup("test.v") != nil {
		return mock_default_reporterRpc_Create(req)
	}

	return cli.Cli.CreateRecord(ctx, req)
}

func (cli *ReporterRPC) Modify(ctx context.Context, req *reporter.PutRecordRequest) (
	*reporter.ModifyRecordResponse, error,
) {
	if flag.Lookup("test.v") != nil {
		return mock_default_reporterRpc_Modify(req)
	}

	return cli.Cli.ModifyRecord(ctx, req)
}

func (cli *ReporterRPC) Get(
	ctx context.Context, req *reporter.GetExecuteRecordRequest,
) (*reporter.GetExecuteRecordResponse, error) {
	if flag.Lookup("test.v") != nil {
		return mock_default_reporterRpc_Get(req)
	}

	return cli.Cli.GetExecuteRecord(ctx, req)
}

func (cli *ReporterRPC) GetParent(
	ctx context.Context, req *reporter.GetParentRecordRequest,
) (*reporter.GetParentRecordResponse, error) {
	if flag.Lookup("test.v") != nil {
		return mock_default_reporterRpc_GetParent(req)
	}

	return cli.Cli.GetParentRecord(ctx, req)
}

func (cli *ReporterRPC) GetChild(
	ctx context.Context, req *reporter.GetChildrenRecordRequest,
) (*reporter.GetChildrenRecordResponse, error) {
	if flag.Lookup("test.v") != nil {
		return mock_default_reporterRpc_GetChild(req)
	}

	return cli.Cli.GetChildrenRecord(ctx, req)
}

func (cli *ReporterRPC) CreateCaseRecord(
	ctx context.Context, req *reporter.PutRecordRequest,
) (*reporter.CreateRecordResponse, error) {
	if flag.Lookup("test.v") != nil {
		return mockDefaultReporterRPCCreateCaseRecord(req)
	}

	return cli.Cli.CreateRecord(ctx, req)
}

func (cli *ReporterRPC) ModifyCaseRecord(
	ctx context.Context, req *reporter.PutRecordRequest,
) (*reporter.ModifyRecordResponse, error) {
	if flag.Lookup("test.v") != nil {
		return mockDefaultReporterRPCModifyCaseRecord(req)
	}

	return cli.Cli.ModifyRecord(ctx, req)
}

func (cli *ReporterRPC) CreateInterfaceRecord(
	ctx context.Context, req *reporter.PutInterfaceRecordRequest,
) (*reporter.CreateInterfaceRecordResponse, error) {
	if flag.Lookup("test.v") != nil {
		return mock_default_reporterRpc_CreateInterfaceRecord(req)
	}

	return cli.Cli.CreateInterfaceRecord(ctx, req)
}

func (cli *ReporterRPC) ModifyInterfaceRecord(
	ctx context.Context, req *reporter.PutInterfaceRecordRequest,
) (*reporter.ModifyInterfaceRecordResponse, error) {
	if flag.Lookup("test.v") != nil {
		return mock_default_reporterRpc_ModifyInterfaceRecord(req)
	}

	return cli.Cli.ModifyInterfaceRecord(ctx, req)
}

func (cli *ReporterRPC) CreateSuiteRecord(
	ctx context.Context, req *reporter.PutSuiteRecordRequest,
) (*reporter.CreateSuiteRecordResponse, error) {
	if flag.Lookup("test.v") != nil {
		return mock_default_reporterRpc_CreateSuiteRecord(req)
	}

	return cli.Cli.CreateSuiteRecord(ctx, req)
}

func (cli *ReporterRPC) ModifySuiteRecord(
	ctx context.Context, req *reporter.PutSuiteRecordRequest,
) (*reporter.ModifySuiteRecordResponse, error) {
	if flag.Lookup("test.v") != nil {
		return mock_default_reporterRpc_ModifySuiteRecord(req)
	}

	return cli.Cli.ModifySuiteRecord(ctx, req)
}

func (cli *ReporterRPC) CreateServiceRecord(
	ctx context.Context, req *reporter.PutServiceRecordRequest,
) (*reporter.CreateServiceRecordResponse, error) {
	if flag.Lookup("test.v") != nil {
		return mock_default_reporterRpc_CreateServiceRecord(req)
	}

	return cli.Cli.CreateServiceRecord(ctx, req)
}

func (cli *ReporterRPC) ModifyServiceRecord(
	ctx context.Context, req *reporter.PutServiceRecordRequest,
) (*reporter.ModifyServiceRecordResponse, error) {
	if flag.Lookup("test.v") != nil {
		return mock_default_reporterRpc_ModifyServiceRecord(req)
	}

	return cli.Cli.ModifyServiceRecord(ctx, req)
}

func (cli *ReporterRPC) CreatePlanRecord(
	ctx context.Context, req *reporter.PutPlanRecordRequest,
) (*reporter.CreatePlanRecordResponse, error) {
	if flag.Lookup("test.v") != nil {
		return mock_default_reporterRpc_CreatePlanRecord(req)
	}

	return cli.Cli.CreatePlanRecord(ctx, req)
}

func (cli *ReporterRPC) ModifyPlanRecord(
	ctx context.Context, req *reporter.PutPlanRecordRequest,
) (*reporter.ModifyPlanRecordResponse, error) {
	if flag.Lookup("test.v") != nil {
		return mock_default_reporterRpc_ModifyPlanRecord(req)
	}

	return cli.Cli.ModifyPlanRecord(ctx, req)
}

func (cli *ReporterRPC) GetPlanRecord(
	ctx context.Context, req *reporter.GetPlanRecordRequest,
) (*reporter.GetPlanRecordResponse, error) {
	if flag.Lookup("test.v") != nil {
		return mock_default_reporterRpc_GetPlanRecord(req)
	}

	return cli.Cli.GetPlanRecord(ctx, req)
}

func (cli *ReporterRPC) GetPlanCasesInfo(
	ctx context.Context, req *reporter.GetPlanCasesInfoRequest,
) (*reporter.GetPlanCasesInfoResponse, error) {
	return cli.Cli.GetPlanCasesInfo(ctx, req)
}

func (cli *ReporterRPC) GetInterfaceRecord(
	ctx context.Context, req *reporter.GetInterfaceRecordRequest,
) (*reporter.GetInterfaceRecordResponse, error) {
	if flag.Lookup("test.v") != nil {
		return mock_default_reporterRpc_GetInterfaceRecord(req)
	}

	return cli.Cli.GetInterfaceRecord(ctx, req)
}

func (cli *ReporterRPC) GetServiceRecord(
	ctx context.Context, req *reporter.GetServiceRecordRequest,
) (*reporter.GetServiceRecordResponse, error) {
	if flag.Lookup("test.v") != nil {
		return mock_default_reporterRpc_GetServiceRecord(req)
	}

	return cli.Cli.GetServiceRecord(ctx, req)
}
