package zrpc

import (
	"context"

	"github.com/zeromicro/go-zero/zrpc"
	"google.golang.org/grpc"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/interceptors/clientinterceptors"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/client/uiagentreporter"
)

type UIAgentReporterRPC struct {
	conf zrpc.RpcClientConf

	Name string
	Cli  uiagentreporter.UIAgentReporter
}

func NewUIAgentReporterRPC(conf zrpc.RpcClientConf) *UIAgentReporterRPC {
	return &UIAgentReporterRPC{
		conf: conf,

		Name: conf.Etcd.Key,
		Cli: uiagentreporter.NewUIAgentReporter(
			zrpc.MustNewClient(
				conf, clientinterceptors.UnaryUserInfoClientOption(),
			),
		),
	}
}

func (cli *UIAgentReporterRPC) CreateUIAgentComponentRecord(
	ctx context.Context, req *uiagentreporter.CreateUIAgentComponentRecordReq, opts ...grpc.CallOption,
) (*uiagentreporter.CreateUIAgentComponentRecordResp, error) {
	return cli.Cli.CreateUIAgentComponentRecord(ctx, req, opts...)
}

func (cli *UIAgentReporterRPC) ModifyUIAgentComponentRecord(
	ctx context.Context, req *uiagentreporter.ModifyUIAgentComponentRecordReq, opts ...grpc.CallOption,
) (*uiagentreporter.ModifyUIAgentComponentRecordResp, error) {
	return cli.Cli.ModifyUIAgentComponentRecord(ctx, req, opts...)
}

func (cli *UIAgentReporterRPC) GetUIAgentComponentRecord(
	ctx context.Context, req *uiagentreporter.GetUIAgentComponentRecordReq, opts ...grpc.CallOption,
) (*uiagentreporter.GetUIAgentComponentRecordResp, error) {
	return cli.Cli.GetUIAgentComponentRecord(ctx, req, opts...)
}
