package zrpc

import (
	"context"
	"flag"
	"fmt"

	"github.com/zeromicro/go-zero/zrpc"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/interceptors/clientinterceptors"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/utils"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/rpc/client/dispatcher"
	dispatcherpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/rpc/pb"
)

type DispatcherRpc struct {
	conf zrpc.RpcClientConf

	Name string
	Cli  dispatcher.Dispatcher
}

func NewDispatcherRpc(conf zrpc.RpcClientConf) *DispatcherRpc {
	cli := &DispatcherRpc{
		conf: conf,
		Name: conf.Etcd.Key,
		Cli:  dispatcher.NewDispatcher(zrpc.MustNewClient(conf, clientinterceptors.UnaryUserInfoClientOption())),
	}

	return cli
}

func (cli *DispatcherRpc) Publish(ctx context.Context, req *dispatcherpb.PublishReq) (
	*dispatcherpb.PublishResp, error,
) {
	if flag.Lookup("test.v") != nil {
		return mockDefaultDispatcherRpcPublish(req)
	}

	return cli.Cli.Publish(ctx, req)
}

func mockDefaultDispatcherRpcPublish(req *dispatcherpb.PublishReq) (*dispatcherpb.PublishResp, error) {
	fmt.Println(req) // golangci-lint扫出来这个变量没被用到
	resp := &dispatcherpb.PublishResp{}
	resp.TaskId = utils.GenTaskId()
	resp.ExecuteId = utils.GenExecuteId()
	return resp, nil
}

func (cli *DispatcherRpc) Stop(ctx context.Context, req *dispatcherpb.StopReq) (*dispatcherpb.StopResp, error) {
	if flag.Lookup("test.v") != nil {
		return &dispatcherpb.StopResp{}, nil
	}

	return cli.Cli.Stop(ctx, req)
}

func (cli *DispatcherRpc) SearchTaskInfo(
	ctx context.Context, in *dispatcherpb.SearchTaskInfoReq,
) (*dispatcherpb.SearchTaskInfoResp, error) {
	/*if flag.Lookup("test.v") != nil {
		return &dispatcherpb.SearchTaskInfoResp{}, nil
	}*/

	return cli.Cli.SearchTaskInfo(ctx, in)
}
