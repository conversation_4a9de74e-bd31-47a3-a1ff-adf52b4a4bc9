package zrpc

import (
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/utils"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

func mock_default_managerRpc_GetApiExecutionData(req *pb.GetApiExecutionDataReq) (*pb.ApiExecutionData, error) {
	resp := &pb.ApiExecutionData{
		Type: req.GetType(),
		Data: &pb.ApiExecutionData_Case{},
	}

	switch req.GetType() {
	case pb.ApiExecutionDataType_API_CASE:
		resp.Id = utils.GenCaseId()
		resp.Data = &pb.ApiExecutionData_Case{
			Case: &pb.CaseComponent{
				ProjectId: utils.GenProjectId(),
				CaseId:    resp.GetId(),
				Name:      "unittest_case",
				Version:   utils.GenVersion(),
			},
		}
	case pb.ApiExecutionDataType_API_COMPONENT_GROUP:
		resp.Id = utils.GenComponentGroupId()
		resp.Data = &pb.ApiExecutionData_Group{
			Group: &pb.ComponentGroupComponent{
				ProjectId:        utils.GenProjectId(),
				ComponentGroupId: resp.GetId(),
				Name:             "unittest_component_group",
				Version:          utils.GenVersion(),
			},
		}
	case pb.ApiExecutionDataType_API_SUITE:
		resp.Id = utils.GenSuiteId()
		resp.Data = &pb.ApiExecutionData_Suite{
			Suite: &pb.SuiteComponent{
				ProjectId: utils.GenProjectId(),
				SuiteId:   resp.GetId(),
				Name:      "unittest_component_suite",
			},
		}
	case pb.ApiExecutionDataType_API_PLAN:
		resp.Id = utils.GenPlanId()
		resp.Data = &pb.ApiExecutionData_Plan{
			Plan: &pb.PlanComponent{
				ProjectId: utils.GenProjectId(),
				PlanId:    resp.GetId(),
				Name:      "unittest_component_plan",
			},
		}
	case pb.ApiExecutionDataType_INTERFACE_CASE:
		resp.Id = utils.GenInterfaceCaseId()
		resp.Data = &pb.ApiExecutionData_InterfaceCase{
			InterfaceCase: &pb.InterfaceCaseComponent{
				ProjectId: utils.GenProjectId(),
				CaseId:    resp.GetId(),
				Name:      "unittest_interface_case",
				Version:   utils.GenVersion(),
			},
		}
	}

	return resp, nil
}

func mock_default_managerRpc_GetApiExecutionDataStructure(_ *pb.GetApiExecutionDataStructureReq) (*pb.ApiExecutionData, error) {
	return &pb.ApiExecutionData{}, nil
}

func mock_default_managerRpc_GetPlanNotify(_ *pb.GetPlanNotifyReq) (*pb.GetPlanNotifyResp, error) {
	return &pb.GetPlanNotifyResp{}, nil
}
