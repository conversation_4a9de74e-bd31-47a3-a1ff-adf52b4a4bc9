package utils

import (
	"context"
	"fmt"
	"time"

	"github.com/redis/go-redis/v9"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/rpc/pb"
)

const (
	CONST_STOP_STATUS_PREFIX = "task::stopstatus::"

	MAX_STOP_STATUS_DURATION_SECOND = time.Hour * 24
)

func genStopStatusKey(taskId string) string {
	return fmt.Sprintf("%s%s", CONST_STOP_STATUS_PREFIX, taskId)
}

func SetStopStatus(
	ctx context.Context, cli redis.UniversalClient, taskId string, metadata *pb.StopMetadata,
) error {
	return cli.Set(
		ctx,
		genStopStatusKey(taskId),
		protobuf.MarshalJSONToStringIgnoreError(metadata),
		MAX_STOP_STATUS_DURATION_SECOND,
	).Err()
}

func GetStopStatus(ctx context.Context, cli redis.UniversalClient, taskId string) (bool, error) {
	result, err := cli.Exists(ctx, genStopStatusKey(taskId)).Result()
	if err != nil {
		return false, err
	}

	if result > 0 {
		return true, nil
	}
	return false, nil
}

func GetStopMetadata(ctx context.Context, cli redis.UniversalClient, taskID string) (*pb.StopMetadata, error) {
	result, err := cli.Get(ctx, genStopStatusKey(taskID)).Result()
	if err != nil {
		return nil, err
	}

	metadata := &pb.StopMetadata{}
	if err = protobuf.UnmarshalJSONFromString(result, metadata); err != nil {
		return nil, err
	}

	return metadata, nil
}
