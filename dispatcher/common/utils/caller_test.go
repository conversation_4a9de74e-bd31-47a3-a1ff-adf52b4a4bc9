package utils

import (
	"context"
	"fmt"
	"testing"
)

func TestMultiDo(t *testing.T) {
	array := []string{
		"abc",
		"123",
	}

	err := MultiDo(context.Background(), array, func(item interface{}) error {
		key, ok := item.(string)
		if !ok {
			return fmt.<PERSON><PERSON><PERSON>("无效的key类型: %T", item)
		}

		return RetryDo(3, func() error {
			_, perr := fmt.Println(key)
			return perr
		})
	}, 16)
	if err != nil {
		t.<PERSON><PERSON><PERSON>("%s", err)
		t.<PERSON><PERSON><PERSON>()
	}
}
