package utils

import (
	"context"
	"fmt"
	"reflect"
	"time"

	//"github.com/hashicorp/go-multierror"
	//"github.com/zeromicro/go-zero/core/mr"
	//
	//"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/rpc/pb"

	"github.com/hashicorp/go-multierror"
	"github.com/zeromicro/go-zero/core/mr"
)

const (
	MaxMultiCount = 128
	MaxRetryCount = 3
)

func MultiDo(ctx context.Context, array any, do func(item any) error, multiCount int) (err error) {
	if multiCount > MaxMultiCount {
		multiCount = MaxMultiCount
	}

	rv := reflect.ValueOf(array)
	if rv.Kind() != reflect.Slice && rv.Kind() != reflect.Array {
		return fmt.Errorf("参数必须是数组类型")
	}

	// 并发 + 重试
	mr.ForEach(
		func(source chan<- any) {
			for i := 0; i < rv.Len(); i++ {
				source <- rv.Index(i).Interface()
			}
		}, func(item any) {
			nerr := do(item)
			if nerr != nil {
				err = multierror.Append(err, nerr)
			}
		}, mr.WithContext(ctx), mr.WithWorkers(multiCount),
	)
	if err != nil {
		return
	}

	return nil
}

func RetryDo(retry int, do func() error) (err error) {
	if retry > MaxRetryCount {
		retry = MaxRetryCount
	}

	var times int
	for times < retry {
		times++

		err = do()
		if err == nil {
			return nil
		}
		time.Sleep(time.Millisecond * 100)
	}
	return err
}
