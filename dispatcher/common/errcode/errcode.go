package errcode

import (
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/state"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
)

var codeToStrMap = map[errorx.Code]string{
	errorx.OK:            "Success",
	errorx.Failure:       "Failure",
	errorx.InternalError: "Internal Error",
}

func GetErrCode(code errorx.Code) state.IErrCode {
	return state.GetErrCode(code, codeToStrMap[code], "")
}

func GetErrCodeDebugf(code errorx.Code, format string, args ...any) state.IErrCode {
	return state.GetErrCode(code, codeToStrMap[code], format, args...)
}
