package discovery

import (
	"context"

	"google.golang.org/grpc"

	discoverypb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/discovery/rpc/pb"
)

type IClient interface {
	HealthCheck() error
	IsDiscovery() bool

	// DownloadApp 下载应用
	DownloadApp(
		ctx context.Context, in *discoverypb.DownloadAppReq, opts ...grpc.CallOption,
	) (*discoverypb.DownloadAppResp, error)
	// InstallApp 安装应用
	InstallApp(
		ctx context.Context, in *discoverypb.InstallAppReq, opts ...grpc.CallOption,
	) (*discoverypb.InstallAppResp, error)
	// RemoveApp 删除应用文件
	RemoveApp(ctx context.Context, in *discoverypb.RemoveAppReq, opts ...grpc.CallOption) (
		*discoverypb.RemoveAppResp, error,
	)
}
