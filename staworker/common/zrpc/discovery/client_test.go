package discovery

import (
	"testing"

	"github.com/zeromicro/go-zero/zrpc"
)

func Test_HealthCheck(t *testing.T) {
	type args struct {
		target string
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name: "0.0.0.0:21511",
			args: args{
				"0.0.0.0:21511",
			},
			wantErr: false,
		},
		{
			name: "192.168.38.1:21511",
			args: args{
				"192.168.38.1:21511",
			},
			wantErr: true,
		},
		{
			name: "192.168.36.100:21511",
			args: args{
				"192.168.36.100:21511",
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				c := NewRPCClient(
					zrpc.RpcClientConf{
						// Endpoints: []string{tt.args.target},
						Target:   tt.args.target,
						NonBlock: true,
					},
				)
				if err := c.HealthCheck(); (err != nil) != tt.wantErr {
					t.Errorf("healthCheck() error = %v, wantErr %v", err, tt.wantErr)
				}
			},
		)
	}
}
