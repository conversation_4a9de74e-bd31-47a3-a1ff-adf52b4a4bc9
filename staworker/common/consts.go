package common

import "time"

const (
	NameOfStabilityWorker = "staworker"
	NameOfStabilityTool   = "statool"

	ConstLogFieldKeyOfTaskID    = "task_id"
	ConstLogFieldKeyOfExecuteID = "execute_id"

	ConstAppDownloadPath         = "apps"
	ConstStabilityTestReportPath = "reports"

	ConstPrefixOfRandomDevice = "random-device:"

	ConstTimeoutOfWaitTask           = 2 * time.Minute
	ConstTimeoutOfSendTask           = 2 * time.Second
	ConstTimeoutOfInvokeRPC          = 5 * time.Second
	ConstTimeoutOfDownloadAppPackage = 5 * time.Minute
	ConstTimeoutOfInstallApp         = 12 * time.Minute
	ConstTimeoutOfBusinessLogic      = 2 * time.Minute

	ConstNotAvailableDeviceUdid = "N/A" // 无可用设备的udid标识
)
