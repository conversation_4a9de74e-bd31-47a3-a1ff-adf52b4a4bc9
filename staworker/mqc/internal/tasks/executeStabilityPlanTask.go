package tasks

import (
	"context"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/trace"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworkerv2/base"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	dispatcherpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/rpc/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/staworker/mqc/internal/logic/staplan"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/staworker/mqc/internal/svc"
)

var _ base.Handler = (*StabilityPlanTaskProcessor)(nil)

type StabilityPlanTaskProcessor struct {
	svcCtx *svc.ServiceContext
}

func NewStabilityPlanTaskProcessor(svcCtx *svc.ServiceContext) base.Handler {
	return &StabilityPlanTaskProcessor{
		svcCtx: svcCtx,
	}
}

func (p *StabilityPlanTaskProcessor) ProcessTask(ctx context.Context, task *base.Task) (result []byte, err error) {
	logger := logx.WithContext(ctx)
	logger.Debugf(
		"processor trace_id: %s, span_id: %s, task_name: %s",
		trace.TraceIDFromContext(ctx), trace.SpanIDFromContext(ctx), task.Typename,
	)
	defer func() {
		if r := recover(); r != nil {
			logger.Errorf("processor recover result: %+v", r)
		}
	}()

	logger.Infof("stability plan task payload: %s", task.Payload)

	var req dispatcherpb.WorkerReq
	if err = protobuf.UnmarshalJSON(task.Payload, &req); err != nil {
		return []byte(constants.FAILURE), errors.Wrapf(
			errorx.Err(errorx.SerializationError, err.Error()),
			"failed to unmarshal the payload of stability plan task, payload: %s, error: %+v",
			task.Payload, err,
		)
	}

	ctx = updateContext(ctx, req.GetTaskId(), req.GetExecuteId(), req.GetUser())
	if err = staplan.NewExecuteStabilityPlanTaskLogic(ctx, p.svcCtx, &req).Execute(); err != nil {
		logger.Errorf(
			"failed to execute the stability plan task, task_id: %s, execute_id: %s, error: %+v",
			req.GetTaskId(), req.GetExecuteId(), err,
		)
	}

	return []byte(constants.SUCCESS), nil
}
