package staplan_test

import (
	"errors"
	"testing"
	"time"

	"github.com/zeromicro/go-zero/core/threading"
)

func TestGoSafePanic(t *testing.T) {
	threading.GoSafe(func() {
		for i := 0; i < 10; i++ {
			if i == 5 {
				panic("崩溃测试")
			}
			t.<PERSON>g(i)
		}
	})

	// 防止程序立即退出
	time.Sleep(10 * time.Second)
}

func TestGoSafeFunc(t *testing.T) {
	go GoSafeFunc(t)

	time.Sleep(10 * time.Second)
}

func GoSafeFunc(t *testing.T) {
	defer func() {
		if r := recover(); r != nil {
			t.Logf("崩溃返回")
		}
	}()
	t.Logf("协程开始")
	if err := GoRoutine(t); err != nil {
		t.Logf("协程崩溃返回，错误：%v", err)
	}
	t.<PERSON>gf("协程返回")
}

func GoRoutine(t *testing.T) (err error) {
	defer func() {
		if r := recover(); r != nil {
			t.Logf("协程崩溃回调")
			err = errors.New("协程崩溃回调")
		}
	}()
	t.Logf("运行协程")
	panic("协程奔溃")
	t.Logf("结束协程")
	return
}
