package staplan

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/lang"
	"github.com/zeromicro/go-zero/core/logx"
	"go.uber.org/atomic"
	"google.golang.org/protobuf/types/known/timestamppb"
	batchv1 "k8s.io/api/batch/v1"
	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/resource"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/util/intstr"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworkerv2/base"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/mq"
	commonpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/thirdparty/constack/v1alpha"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/utils"
	dispatcherutils "gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/common/utils"
	dispatcherpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/rpc/pb"
	managerpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/perfworker/common/util"
	reporterpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/staworker/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/staworker/mqc/internal/svc"
)

type BaseLogic struct {
	logx.Logger
	ctx    context.Context
	cancel context.CancelFunc
	svcCtx *svc.ServiceContext

	state  *atomic.String
	stopCh chan lang.PlaceholderType

	taskReq  *dispatcherpb.WorkerReq
	metaData *managerpb.StabilityPlanMetaData

	udids    []string      // 选择设备
	count    int32         // 设备数量
	timeout  time.Duration // 超时时间
	shutdown bool          // 是否宕机

	jobName string // Job名称
	queue   string // statool队列名称
}

func NewBaseLogic(
	ctx context.Context, svcCtx *svc.ServiceContext, req *dispatcherpb.WorkerReq,
) *BaseLogic {
	var (
		taskID        = req.GetTaskId()
		taskIDSuffix  = strings.TrimPrefix(taskID, utils.ConstTaskIdPrefix)
		jobNameSuffix = strings.Replace(strings.ToLower(taskIDSuffix), "_", "-", -1)

		metaData = req.GetStabilityPlan().GetMetaData()
		devices  = metaData.GetDevices()

		count int32
		udids []string
	)

	for strings.HasSuffix(jobNameSuffix, "-") {
		jobNameSuffix = strings.TrimSuffix(jobNameSuffix, "-")
	}

	switch devices.GetDevices().(type) {
	case *commonpb.StabilityCustomDevices_Udids:
		for _, udid := range devices.GetUdids().GetValues() {
			udids = append(udids, udid.GetStringValue())
		}
		count = int32(len(udids))
	case *commonpb.StabilityCustomDevices_Count:
		count = int32(devices.GetCount())
		for i := 0; i < int(count); i++ {
			udids = append(udids, common.ConstPrefixOfRandomDevice+taskIDSuffix)
		}
	}

	timeout := time.Duration(metaData.GetDuration())*time.Minute +
		defaultStabilityTaskTimeout*time.Second +
		common.ConstTimeoutOfDownloadAppPackage +
		common.ConstTimeoutOfInstallApp

	return &BaseLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,

		state:  atomic.NewString(dispatcherpb.ComponentState_Init.String()),
		stopCh: make(chan lang.PlaceholderType),

		taskReq:  req,
		metaData: metaData,

		udids:    udids,
		count:    count,
		timeout:  timeout,
		shutdown: false,

		jobName: fmt.Sprintf("%s-%s", common.NameOfStabilityTool, jobNameSuffix),
		queue:   constants.MQTaskTypeStabilityWorkerExecuteCaseTask + ":" + taskID,
	}
}

func (l *BaseLogic) teardown() (err error) {
	// 重启会恢复至原来步骤继续运行，因此暂时不能进行teardown操作
	if l.shutdown {
		return nil
	}

	var (
		taskID    = l.taskReq.GetTaskId()
		executeID = l.taskReq.GetExecuteId()
		projectID = l.taskReq.GetProjectId()

		status  = l.state.String()
		appInfo = l.fetchAppInfo()
	)

	defer func() {
		if r := recover(); r != nil {
			l.Errorf("teardown recover result: %+v", r)
		}
		if err != nil {
			l.Errorf(
				"failed to teardown the stability plan task, task_id: %s, execute_id: %s, error: %+v",
				taskID, executeID, err,
			)
		}
	}()

	if status == dispatcherpb.ComponentState_Started.String() {
		l.state.CompareAndSwap(l.state.String(), dispatcherpb.ComponentState_Success.String())
		status = l.state.String()
	}

	l.Infof(
		"the stability plan task status in last: %s, task_id: %s, execute_id: %s, project_id: %s",
		status, taskID, executeID, projectID,
	)

	// set stop status to shutdown statool when exceptions
	if status != dispatcherpb.ComponentState_Stop.String() &&
		status != dispatcherpb.ComponentState_Success.String() {
		l.setStopStatus()
	}

	ctx1, cancel1 := utils.NewTimeoutContext(l.ctx, 10*time.Second)
	defer cancel1()
	// modify stability plan record
	if _, err = l.svcCtx.ReporterRPC.ModifyStabilityPlanRecord(
		ctx1, &reporterpb.PutStabilityPlanRecordReq{
			TaskId:    taskID,
			ExecuteId: executeID,
			ProjectId: projectID,
			Status:    l.state.String(),
			EndedAt:   timestamppb.New(time.Now()),
			AppInfo:   appInfo,
		},
	); err != nil {
		return errors.Errorf(
			"failed to modify stability plan record, task_id: %s, execute_id: %s, error: %+v",
			taskID, executeID, err,
		)
	}

	// send callback to dispatcher
	task := base.NewTask(
		constants.MQTaskTypeDispatcherSendStabilityNotification,
		protobuf.MarshalJSONIgnoreError(&dispatcherpb.StabilityReportCallback{
			ProjectId: projectID,
			TaskId:    taskID,
			ExecuteId: executeID,
		}),
		base.WithMaxRetryOptions(0),
		base.WithRetentionOptions(8*time.Hour),
	)

	ctx2, cancel2 := utils.NewTimeoutContext(l.ctx, 10*time.Second)
	defer cancel2()
	_, err = l.svcCtx.DispatcherProducer.Send(
		ctx2, task, base.QueuePriorityDefault,
	)
	if err != nil {
		return errors.Wrapf(
			errorx.Err(errorx.InternalError, err.Error()),
			"failed to send the stability reporter notification to dispatcher, queue: %s, payload: %s, error: %+v",
			task.Queue, task.Payload, err,
		)
	}

	return nil
}

func (l *BaseLogic) setStopStatus() {
	ctx, cancel := utils.NewTimeoutContext(l.ctx, 5*time.Second)
	defer cancel()

	status := l.state.String()
	taskID := l.taskReq.GetTaskId()
	metaData := &dispatcherpb.StopMetadata{
		StopType: dispatcherpb.StopType_StopType_Auto,
	}

	err := dispatcherutils.SetStopStatus(ctx, l.svcCtx.DispatcherRedis, taskID, metaData)
	if err != nil {
		l.Errorf(
			"failed to set stop status, status: %s, task_id: %s, error: %+v",
			status, taskID, err,
		)
	}
}

func (l *BaseLogic) fetchAppInfo() *commonpb.AppInfo {
	var (
		taskID    = l.taskReq.GetTaskId()
		executeID = l.taskReq.GetExecuteId()
	)

	ctx, cancel := utils.NewTimeoutContext(l.ctx, 5*time.Second)
	defer cancel()

	key := fmt.Sprintf("%s:%s", constants.ConstCachePrefixOfAppInfo, taskID)
	appInfo, err := l.svcCtx.Redis.GetCtx(ctx, key)
	if err != nil {
		l.Errorf(
			"failed to get app_info from redis, app_info: %s, task_id: %s, execute_id: %s, error: %+v",
			appInfo, taskID, executeID, err,
		)
		return nil
	}

	if appInfo == "" {
		l.Errorf(
			"fetch app_info is empty from redis, task_id: %s, execute_id: %s",
			taskID, executeID,
		)
		return nil
	} else {
		var info commonpb.AppInfo
		if err = protobuf.UnmarshalJSONFromString(appInfo, &info); err != nil {
			l.Errorf(
				"failed to unmarshal app_info, app_info: %s, task_id: %s, execute_id: %s, error: %+v",
				appInfo, taskID, executeID, err,
			)
			return nil
		}
		return &info
	}
}

func (l *BaseLogic) execute() (err error) {
	defer func() {
		if r := recover(); r != nil || err != nil {
			l.Errorf("processor execute result: %+v, error: %+v", r, err)
			if l.state.String() != dispatcherpb.ComponentState_Stop.String() {
				l.state.CompareAndSwap(l.state.String(), dispatcherpb.ComponentState_Panic.String())
			}
		}
	}()

	var (
		taskID    = l.taskReq.GetTaskId()
		executeID = l.taskReq.GetExecuteId()
	)

	if err = l.createK8sResources(); err != nil {
		l.Errorf(
			"failed to create k8s resources, task_id: %s, execute_id: %s, error: %+v",
			taskID, executeID, err,
		)
		return err
	}

	if err = l.sendTaskToConsumers(); err != nil {
		l.Errorf(
			"failed to send task to consumers, task_id: %s, execute_id: %s, error: %+v",
			taskID, executeID, err,
		)
		return err
	}

	if err = l.waitForJobSuccess(); err != nil {
		l.Errorf(
			"failed to wait for job success, task_id: %s, execute_id: %s, error: %+v",
			taskID, executeID, err,
		)
		return err
	}

	return nil
}

func (l *BaseLogic) waitForJobSuccess() (err error) {
	var (
		taskID    = l.taskReq.GetTaskId()
		executeID = l.taskReq.GetExecuteId()
	)

	timer := time.NewTimer(l.timeout)
	defer timer.Stop()

	ticker := time.NewTicker(10 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-l.ctx.Done():
			l.Warnf(
				"got a context done signal while waiting for the job to be success, task_id: %s, execute_id: %s, job: %s, error: %+v",
				taskID, executeID, l.jobName, l.ctx.Err(),
			)
			return l.ctx.Err()
		case <-l.stopCh:
			l.Warnf(
				"got a stop signal while waiting for the job to be success, task_id: %s, execute_id: %s, job: %s",
				taskID, executeID, l.jobName,
			)
			return errors.Errorf(
				"got a stop signal while waiting for the job to be success, task_id: %s, execute_id: %s, job: %s",
				taskID, executeID, l.jobName,
			)
		case <-timer.C: // 如果超时，则退出
			l.Errorf(
				"timeout while waiting for the job to be success, task_id: %s, execute_id: %s, job: %s",
				taskID, executeID, l.jobName,
			)
			return errors.Errorf(
				"timeout while waiting for the job to be success, task_id: %s, execute_id: %s, job: %s",
				taskID, executeID, l.jobName,
			)
		case <-ticker.C: // 每当定时器触发时执行
			l.Infof("ticker for checking the job status")
			success, err := l.checkJobStatus()
			if err != nil {
				l.Warnf(
					"failed to check the job status, task_id: %s, execute_id: %s, job: %s, error: %s",
					taskID, executeID, l.jobName, err.Error(),
				)
				// 如果发现Job资源不存在，则退出
				if strings.HasSuffix(err.Error(), fmt.Sprintf(k8sNotFoundFormat, ResourceJob, GroupJob, l.jobName)) {
					return nil
				}
				continue
			}

			if success {
				l.Infof(
					"success to wait for the job to be completed, task_id: %s, execute_id: %s, job: %s",
					taskID, executeID, l.jobName,
				)
				return nil
			}
		}
	}
}

func (l *BaseLogic) checkJobStatus() (bool, error) {
	job, err := l.getJobResource()
	if err != nil {
		return false, err
	}

	pods, err := l.listPodWithJob()
	if err != nil {
		return false, err
	}

	pods_count := len(pods)
	succeed_count := int(job.Status.Succeeded)

	if pods_count == 0 {
		return false, nil
	}
	l.Infof(
		"check job[%s] succeeded count: %d, pods' count: %d",
		l.jobName, succeed_count, pods_count,
	)

	return succeed_count == pods_count, nil
}

func (l *BaseLogic) getJobResource() (*batchv1.Job, error) {
	out, err := l.svcCtx.ConstackGrpcClient.Get(
		l.ctx, &v1alpha.GetRequest{
			Cluster:   l.svcCtx.Config.Job.ClusterName,
			Namespace: l.svcCtx.Config.Job.Namespace,
			Name:      l.jobName,
			GroupVersionResource: &v1alpha.GroupVersionResource{
				Group:    GroupJob,
				Version:  VersionJob,
				Resource: ResourceJob,
			},
		},
	)
	if err != nil {
		l.Errorf(
			"failed to fetch the job, name: %s, namespace: %s, err: %v",
			l.jobName, l.svcCtx.Config.Job.Namespace, err,
		)
		return nil, fmt.Errorf("failed to fetch the job, name: %s, namespace: %s, err: %v",
			l.jobName, l.svcCtx.Config.Job.Namespace, err)
	}

	job := &batchv1.Job{}
	err = json.Unmarshal([]byte(out.GetData()), job)
	if err != nil {
		l.Errorf("failed to convert the job, name: %s, namespace: %s, err: %v",
			l.jobName, l.svcCtx.Config.Job.Namespace, err,
		)
		return job, fmt.Errorf("failed to convert the job, name: %s, namespace: %s, err: %v",
			l.jobName, l.svcCtx.Config.Job.Namespace, err)
	}

	return job, nil
}

func (l *BaseLogic) sendTaskToConsumers() (err error) {
	l.state.CompareAndSwap(l.state.String(), dispatcherpb.ComponentState_Started.String())
	// 重启恢复至原来步骤继续运行，因此不需要进行该重复操作，防止有脏数据
	if len(l.queue) == 0 {
		return nil
	}

	var (
		taskID    = l.taskReq.GetTaskId()
		executeID = l.taskReq.GetExecuteId()
		projectID = l.taskReq.GetProjectId()
	)

	// 1. update plan record status to started
	if l.state.String() == dispatcherpb.ComponentState_Started.String() {
		if _, err = l.svcCtx.ReporterRPC.ModifyStabilityPlanRecord(
			l.ctx, &reporterpb.PutStabilityPlanRecordReq{
				TaskId:    taskID,
				ExecuteId: executeID,
				ProjectId: projectID,
				Status:    l.state.String(),
				StartedAt: timestamppb.New(time.Now()),
			},
		); err != nil {
			return errors.Errorf(
				"failed to modify stability plan record, task_id: %s, execute_id: %s, error: %+v",
				taskID, executeID, err,
			)
		}
	}

	for _, udid := range l.udids {
		caseExecuteID := utils.GenExecuteId()

		// 2. create device record(status: pending)
		if _, err = l.svcCtx.ReporterRPC.CreateStabilityDeviceRecord(
			l.ctx, &reporterpb.PutStabilityDeviceRecordReq{
				TaskId:        taskID,
				ExecuteId:     caseExecuteID,
				ProjectId:     projectID,
				PlanExecuteId: executeID,
				Udid:          udid,
				Status:        dispatcherpb.ComponentState_Pending.String(),
				ExecutedBy:    l.taskReq.GetUserId(),
			},
		); err != nil {
			return errors.Errorf(
				"failed to create stability device record, task_id: %s, execute_id: %s, error: %+v",
				taskID, executeID, err,
			)
		}

		// 3. send task to consumer
		taskData := l.getTaskInfo(caseExecuteID, udid)
		payload, err := protobuf.MarshalJSON(taskData)
		if err != nil {
			return errors.Wrapf(
				errorx.Err(errorx.SerializationError, err.Error()), "faile to marshal stability case task info, error: %s", err,
			)
		}

		task := base.NewTask(
			l.queue, payload,
			base.WithMaxRetryOptions(0),
			base.WithTimeoutOptions(l.timeout),
			base.WithRetentionOptions(l.timeout+time.Hour),
		)

		_, err = l.svcCtx.StabilityWorkerProducer.Send(
			l.ctx, task, mq.ConvertPbEnumerationToQueuePriority(l.taskReq.GetPriorityType()),
		)
		if err != nil {
			return errors.Wrapf(
				errorx.Err(errorx.InternalError, err.Error()),
				"failed to send the stability case task to statool, queue: %s, payload: %s, error: %+v",
				task.Queue, task.Payload, err,
			)
		}
	}

	return nil
}

func (l *BaseLogic) getTaskInfo(executeID, udid string) *commonpb.StabilityCaseTaskInfo {
	info := &commonpb.StabilityCaseTaskInfo{
		ProjectId:       l.taskReq.GetProjectId(),
		PlanId:          l.taskReq.GetStabilityPlan().GetStabilityPlanId(),
		TaskId:          l.taskReq.GetTaskId(),
		ExecuteId:       executeID,
		PlanExecuteId:   l.taskReq.GetExecuteId(),
		TriggerMode:     l.taskReq.GetTriggerMode(),
		AccountConfig:   l.metaData.GetAccountConfig(),
		DeviceType:      l.metaData.GetDeviceType(),
		PlatformType:    l.metaData.GetPlatformType(),
		Udid:            udid,
		PackageName:     l.metaData.GetPackageName(),
		AppDownloadLink: l.metaData.GetAppDownloadLink(),
		Activities:      l.metaData.GetActivities(),
		CustomScript:    l.metaData.GetCustomScript(),
		Duration:        l.metaData.GetDuration(),
		ExecutedBy:      l.taskReq.GetUser(),
	}

	return info
}

func (l *BaseLogic) createK8sResources() (err error) {
	var (
		taskID    = l.taskReq.GetTaskId()
		executeID = l.taskReq.GetExecuteId()
	)

	IsExist, err := l.checkPvcIfExist()
	if err != nil {
		l.Errorf(
			"failed to check pvc, task_id: %s, execute_id: %s, name: %s, error: %+v",
			taskID, executeID, l.svcCtx.Config.Job.PvcName, err,
		)
		return err
	}
	if !IsExist {
		return fmt.Errorf("pvc[%s] not found", l.svcCtx.Config.Job.PvcName)
	}

	job := l.generateJobYaml()
	if err = l.createJobResource(job); err != nil {
		l.Errorf(
			"failed to create job resource, task_id: %s, execute_id: %s, error: %+v",
			taskID, executeID, err,
		)
		return err
	}

	if err = l.createServiceResource(9101); err != nil {
		l.Warnf(
			"failed to create service resource, task_id: %s, execute_id: %s, error: %+v",
			taskID, executeID, err,
		)
	}

	if err = l.waitForPodsRunning(); err != nil {
		l.Errorf(
			"failed to wait for pods running, task_id: %s, execute_id: %s, error: %+v",
			taskID, executeID, err,
		)
		return err
	}

	return nil
}

func (l *BaseLogic) waitForPodsRunning() (err error) {
	var (
		taskID    = l.taskReq.GetTaskId()
		executeID = l.taskReq.GetExecuteId()
	)

	timer := time.NewTimer(10 * time.Minute)
	defer timer.Stop()

	ticker := time.NewTicker(5 * time.Second)
	defer ticker.Stop()

	finished := make(map[string]lang.PlaceholderType, l.count)
	sent := make(map[string]lang.PlaceholderType, l.count)
	for {
		select {
		case <-l.ctx.Done():
			l.Warnf(
				"got a context done signal while waiting for the pods to be running, task_id: %s, execute_id: %s, job: %s, error: %+v",
				taskID, executeID, l.jobName, l.ctx.Err(),
			)
			return l.ctx.Err()
		case <-l.stopCh:
			l.Warnf(
				"got a stop signal while waiting for the pods to be running, task_id: %s, execute_id: %s, job: %s",
				taskID, executeID, l.jobName,
			)
			return errors.Errorf(
				"got a stop signal while waiting for the pods to be running, task_id: %s, execute_id: %s, job: %s",
				taskID, executeID, l.jobName,
			)
		case <-timer.C:
			l.Errorf(
				"timeout while waiting for the pods to be running, task_id: %s, execute_id: %s, job: %s",
				taskID, executeID, l.jobName,
			)
			return errors.Errorf(
				"timeout while waiting for the pods to be running, task_id: %s, execute_id: %s, job: %s",
				taskID, executeID, l.jobName,
			)
		case <-ticker.C:
			if len(finished) >= int(l.count) {
				l.Infof(
					"finished to wait for the pods to be running, task_id: %s, execute_id: %s, job: %s, sent: %d, finished: %d",
					taskID, executeID, l.jobName, len(sent), len(finished),
				)
				return nil
			}

			pods, err := l.listPodWithJob()
			if err != nil {
				return err
			}

			for _, pod := range pods {
				if pod.Status.Phase == corev1.PodPending {
					continue
				} else if _, ok := finished[pod.Name]; !ok {
					finished[pod.Name] = lang.Placeholder
				}

				if pod.Status.Phase != corev1.PodRunning {
					continue
				}

				if _, ok := sent[pod.Name]; !ok {
					sent[pod.Name] = lang.Placeholder
					l.Infof(
						"the pod has been running, task_id: %s, execute_id: %s, job: %s, index: %d, pod: %s, status: %s",
						taskID, executeID, l.jobName, len(sent), pod.Name, pod.Status.Phase,
					)
				}
			}
		}
	}
}

func (l *BaseLogic) listPodWithJob() (pods []*corev1.Pod, err error) {
	pods = make([]*corev1.Pod, 0)
	out, err := l.svcCtx.ConstackGrpcClient.List(
		l.ctx, &v1alpha.ListRequest{
			Cluster:   l.svcCtx.Config.Job.ClusterName,
			Namespace: l.svcCtx.Config.Job.Namespace,
			GroupVersionResource: &v1alpha.GroupVersionResource{
				Group:    GroupPod,
				Version:  VersionPod,
				Resource: ResourcePod,
			},
		},
	)
	if err != nil {
		l.Errorf("failed to list the pods, namespace:%s, err:%v", l.svcCtx.Config.Job.Namespace, err)
		return nil, fmt.Errorf("failed to list the pods, namespace:%s, err:%v", l.svcCtx.Config.Job.Namespace, err)
	}

	err = json.Unmarshal([]byte(out.GetData()), &pods)
	if err != nil {
		l.Errorf("failed to convert the pods, namespace:%s, err:%v", l.svcCtx.Config.Job.Namespace, err)
		return nil, fmt.Errorf("failed to convert the pods, namespace:%s, err:%v", l.svcCtx.Config.Job.Namespace, err)
	}

	resultPods := make([]*corev1.Pod, 0, len(pods))

	for _, pod := range pods {
		if pod.GetLabels() == nil {
			continue
		}

		if pod.Labels["job-name"] == l.jobName {
			resultPods = append(resultPods, pod)
		}
	}

	return resultPods, nil
}

func (l *BaseLogic) createServiceResource(ports ...int) (err error) {
	svcPorts := make([]corev1.ServicePort, 0)
	for _, port := range ports {
		svcPorts = append(
			svcPorts, corev1.ServicePort{
				Name:     fmt.Sprintf("port-%d", port),
				Protocol: corev1.ProtocolTCP,
				Port:     int32(port),
				TargetPort: intstr.IntOrString{
					Type:   0,
					IntVal: int32(port),
				},
			},
		)
	}

	k8sSvc := &corev1.Service{
		TypeMeta: metav1.TypeMeta{
			Kind:       "Service",
			APIVersion: "v1",
		},
		ObjectMeta: metav1.ObjectMeta{
			Name:      common.NameOfStabilityTool,
			Namespace: l.svcCtx.Config.Job.Namespace,
		},
		Spec: corev1.ServiceSpec{
			Ports:    svcPorts,
			Selector: map[string]string{"app": common.NameOfStabilityTool},
		},
	}
	data, _ := json.Marshal(k8sSvc)
	_, err = l.svcCtx.ConstackGrpcClient.Apply(
		l.ctx, &v1alpha.ApplyRequest{
			Cluster:   l.svcCtx.Config.Job.ClusterName,
			Namespace: l.svcCtx.Config.Job.Namespace,
			Data:      string(data),
		},
	)
	if err != nil {
		l.Errorf(
			"failed to apply service resource, name: %s, namespace: %s, err: %v",
			common.NameOfStabilityTool, l.svcCtx.Config.Job.Namespace, err,
		)
		return fmt.Errorf(
			"failed to apply service resource, name: %s, namespace: %s, err: %v",
			common.NameOfStabilityTool, l.svcCtx.Config.Job.Namespace, err,
		)
	}

	return nil
}

func (l *BaseLogic) checkPvcIfExist() (bool, error) {
	out, err := l.svcCtx.ConstackGrpcClient.Get(
		l.ctx, &v1alpha.GetRequest{
			Cluster:   l.svcCtx.Config.Job.ClusterName,
			Namespace: l.svcCtx.Config.Job.Namespace,
			Name:      l.svcCtx.Config.Job.PvcName,
			GroupVersionResource: &v1alpha.GroupVersionResource{
				Group:    GroupPvc,
				Version:  VersionPvc,
				Resource: ResourcePvc,
			},
		},
	)
	if err != nil {
		return false, err
	}

	if out != nil {
		return true, nil
	}

	return false, nil
}

func (l *BaseLogic) generateJobYaml() (job *batchv1.Job) {
	var (
		taskID              = l.taskReq.GetTaskId()
		executeID           = l.taskReq.GetExecuteId()
		taskIDLabelValue    = util.GenerateLabelKey(taskID)
		executeIDLabelValue = util.GenerateLabelKey(executeID)

		backoffLimit            = int32(0)
		ttlSecondsAfterFinished = int32(defaultStabilityTaskTimeout)

		activeDeadlineSeconds *int64
	)

	job = &batchv1.Job{
		TypeMeta: metav1.TypeMeta{
			Kind:       "Job",
			APIVersion: "batch/v1",
		},
		ObjectMeta: metav1.ObjectMeta{
			Name:      l.jobName,
			Namespace: l.svcCtx.Config.Job.Namespace,
			Labels: map[string]string{
				"app":        common.NameOfStabilityTool,
				"task-id":    taskIDLabelValue,
				"execute-id": executeIDLabelValue,
			},
		},
		Spec: batchv1.JobSpec{
			Parallelism:           &l.count,
			Completions:           &l.count,
			ActiveDeadlineSeconds: activeDeadlineSeconds,
			BackoffLimit:          &backoffLimit,
			Template: corev1.PodTemplateSpec{
				ObjectMeta: metav1.ObjectMeta{
					// 不注入sidecar
					Annotations: map[string]string{
						"sidecar.istio.io/inject":                        "false",
						"telemetry.mesh.quwan.io/customMetricsContainer": common.NameOfStabilityTool,
						"telemetry.mesh.quwan.io/customMetricsPath":      "/metrics",
						"telemetry.mesh.quwan.io/customMetricsPort":      "9101",
						"telemetry.mesh.quwan.io/customMetricsScrape":    "true",
					},
					Labels: map[string]string{
						"app":        common.NameOfStabilityTool,
						"task-id":    taskIDLabelValue,
						"execute-id": executeIDLabelValue,
					},
				},
				Spec: corev1.PodSpec{
					Containers: []corev1.Container{
						{
							Name:    common.NameOfStabilityTool,
							Image:   l.svcCtx.Config.Job.Image,
							Command: []string{"/app/staworker"},
							Args:    []string{"--mqc-config", "/app/etc/mqc-staworker.yaml"},
							Env:     l.generateJobEnv(),
							Resources: corev1.ResourceRequirements{
								Limits: corev1.ResourceList{
									corev1.ResourceCPU:    resource.MustParse(l.svcCtx.Config.Job.Resources.Limits.Cpu),
									corev1.ResourceMemory: resource.MustParse(l.svcCtx.Config.Job.Resources.Limits.Memory),
								},
								Requests: corev1.ResourceList{
									corev1.ResourceCPU:    resource.MustParse(l.svcCtx.Config.Job.Resources.Requests.Cpu),
									corev1.ResourceMemory: resource.MustParse(l.svcCtx.Config.Job.Resources.Requests.Memory),
								},
							},
							VolumeMounts: []corev1.VolumeMount{
								{
									Name:      volumeName,
									MountPath: l.svcCtx.Config.Job.MountPath,
								},
								{
									Name:      "config",
									MountPath: "/app/etc",
								},
							},
							ImagePullPolicy: corev1.PullAlways,
							ReadinessProbe: &corev1.Probe{
								FailureThreshold:    3,
								InitialDelaySeconds: 10,
								PeriodSeconds:       10,
								SuccessThreshold:    1,
								TimeoutSeconds:      1,
								ProbeHandler: corev1.ProbeHandler{
									HTTPGet: &corev1.HTTPGetAction{
										Path:   "/healthz",
										Port:   intstr.FromInt32(9101),
										Scheme: corev1.URISchemeHTTP,
									},
								},
							},
						},
					},
					RestartPolicy: corev1.RestartPolicyNever,
					SecurityContext: &corev1.PodSecurityContext{
						Sysctls: []corev1.Sysctl{
							{
								Name:  "net.ipv4.ip_local_port_range",
								Value: "2048 64000",
							},
						},
					},
					Volumes: []corev1.Volume{
						{
							Name: volumeName,
							VolumeSource: corev1.VolumeSource{
								PersistentVolumeClaim: &corev1.PersistentVolumeClaimVolumeSource{
									ClaimName: l.svcCtx.Config.Job.PvcName,
								},
							},
						},
						{
							Name: "config",
							VolumeSource: corev1.VolumeSource{
								ConfigMap: &corev1.ConfigMapVolumeSource{
									LocalObjectReference: corev1.LocalObjectReference{
										Name: "staworker-cicd",
									},
								},
							},
						},
					},
				},
			},
			TTLSecondsAfterFinished: &ttlSecondsAfterFinished,
		},
	}

	return job
}

func (l *BaseLogic) generateJobEnv() (envs []corev1.EnvVar) {
	envs = make([]corev1.EnvVar, 0)

	envs = append(
		envs,
		corev1.EnvVar{
			Name:  "WORKER_NAME",
			Value: common.NameOfStabilityTool,
		},
		corev1.EnvVar{
			Name:  "TASK_ID",
			Value: l.taskReq.GetTaskId(),
		},
		corev1.EnvVar{
			Name:  "EXECUTE_ID",
			Value: l.taskReq.GetExecuteId(),
		},
		corev1.EnvVar{
			Name:  "TASK_TYPE_NAME",
			Value: l.queue,
		},
	)

	return envs
}

func (l *BaseLogic) createJobResource(job *batchv1.Job) (err error) {
	data, _ := json.Marshal(job)
	_, err = l.svcCtx.ConstackGrpcClient.Create(
		l.ctx, &v1alpha.CreateRequest{
			Cluster:   l.svcCtx.Config.Job.ClusterName,
			Namespace: l.svcCtx.Config.Job.Namespace,
			Data:      string(data),
		},
	)
	if err != nil {
		if strings.HasSuffix(err.Error(), fmt.Sprintf(k8sAlreadyExistsFormat, ResourceJob, GroupJob, l.jobName)) {
			l.Infof(k8sAlreadyExistsFormat+" | skipping job resource creation!!!", ResourceJob, GroupJob, l.jobName)
			job, err := l.getJobResource()
			if err != nil {
				return err
			}
			l.queue = ""
			l.timeout = l.timeout - time.Since(job.CreationTimestamp.Time) + defaultStabilityTaskTimeout*time.Second
			return nil
		}
		return fmt.Errorf(
			"failed to create job resource, name: %s, namespace: %s, err: %v",
			job.Name, job.Namespace, err,
		)
	}

	return nil
}
