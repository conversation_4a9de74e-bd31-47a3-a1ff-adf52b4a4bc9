package device

import (
	"bytes"
	"fmt"
	"strings"
	"sync"
	"testing"
	"time"
)

// MockWriter 模拟文件写入器
type MockWriter struct {
	buffer bytes.Buffer
	mutex  sync.Mutex
}

func (mw *MockWriter) Write(p []byte) (n int, err error) {
	mw.mutex.Lock()
	defer mw.mutex.Unlock()
	return mw.buffer.Write(p)
}

func (mw *MockWriter) String() string {
	mw.mutex.Lock()
	defer mw.mutex.Unlock()
	return mw.buffer.String()
}

func TestStreamInterceptor_BasicFunctionality(t *testing.T) {
	mockFile := &MockWriter{}
	var receivedLines []string
	var linesMutex sync.Mutex

	lineCallback := func(line string) {
		linesMutex.Lock()
		defer linesMutex.Unlock()
		receivedLines = append(receivedLines, line)
	}

	interceptor := NewStreamInterceptor(mockFile, lineCallback)
	defer interceptor.Close()

	// 测试完整行
	testData := "line1\nline2\nline3\n"
	n, err := interceptor.Write([]byte(testData))
	if err != nil {
		t.Fatalf("Write failed: %v", err)
	}
	if n != len(testData) {
		t.Fatalf("Expected to write %d bytes, got %d", len(testData), n)
	}

	// 等待处理完成
	time.Sleep(100 * time.Millisecond)

	// 验证文件内容
	fileContent := mockFile.String()
	if fileContent != testData {
		t.Errorf("Expected file content %q, got %q", testData, fileContent)
	}

	// 验证回调接收的行
	linesMutex.Lock()
	expectedLines := []string{"line1", "line2", "line3"}
	if len(receivedLines) != len(expectedLines) {
		t.Errorf("Expected %d lines, got %d", len(expectedLines), len(receivedLines))
	}
	for i, expected := range expectedLines {
		if i < len(receivedLines) && receivedLines[i] != expected {
			t.Errorf("Expected line %d to be %q, got %q", i, expected, receivedLines[i])
		}
	}
	linesMutex.Unlock()
}

func TestStreamInterceptor_PartialLines(t *testing.T) {
	mockFile := &MockWriter{}
	var receivedLines []string
	var linesMutex sync.Mutex

	lineCallback := func(line string) {
		linesMutex.Lock()
		defer linesMutex.Unlock()
		receivedLines = append(receivedLines, line)
	}

	interceptor := NewStreamInterceptor(mockFile, lineCallback)
	defer interceptor.Close()

	// 测试部分行数据
	_, _ = interceptor.Write([]byte("partial"))
	_, _ = interceptor.Write([]byte(" line"))
	_, _ = interceptor.Write([]byte(" data\n"))
	_, _ = interceptor.Write([]byte("another"))
	_, _ = interceptor.Write([]byte(" partial"))

	// 等待处理
	time.Sleep(100 * time.Millisecond)

	// 验证只有完整行被发送到回调
	linesMutex.Lock()
	if len(receivedLines) != 1 {
		t.Errorf("Expected 1 complete line, got %d", len(receivedLines))
	}
	if len(receivedLines) > 0 && receivedLines[0] != "partial line data" {
		t.Errorf("Expected line to be 'partial line data', got %q", receivedLines[0])
	}
	linesMutex.Unlock()

	// 关闭拦截器，应该处理剩余的缓冲数据
	interceptor.Close()

	// 等待关闭完成
	time.Sleep(100 * time.Millisecond)

	// 验证剩余数据也被处理
	linesMutex.Lock()
	if len(receivedLines) != 2 {
		t.Errorf("Expected 2 lines after close, got %d", len(receivedLines))
	}
	if len(receivedLines) > 1 && receivedLines[1] != "another partial" {
		t.Errorf("Expected remaining line to be 'another partial', got %q", receivedLines[1])
	}
	linesMutex.Unlock()
}

func TestStreamInterceptor_StderrHandling(t *testing.T) {
	mockFile := &MockWriter{}
	var receivedLines []string

	lineCallback := func(line string) {
		receivedLines = append(receivedLines, line)
	}

	interceptor := NewStreamInterceptor(mockFile, lineCallback)
	defer interceptor.Close()

	stderrWriter := NewStderrWriter(interceptor)

	// 测试stderr写入
	stderrData := "error message\nwarning message\n"
	n, err := stderrWriter.Write([]byte(stderrData))
	if err != nil {
		t.Fatalf("Stderr write failed: %v", err)
	}
	if n != len(stderrData) {
		t.Fatalf("Expected to write %d bytes to stderr, got %d", len(stderrData), n)
	}

	// 等待处理
	time.Sleep(100 * time.Millisecond)

	// 验证stderr数据写入文件但不触发回调
	fileContent := mockFile.String()
	if fileContent != stderrData {
		t.Errorf("Expected file content %q, got %q", stderrData, fileContent)
	}

	// 验证没有行被发送到回调（stderr不进行行处理）
	if len(receivedLines) != 0 {
		t.Errorf("Expected no lines from stderr, got %d", len(receivedLines))
	}
}

func TestStreamInterceptor_ConcurrentWrites(t *testing.T) {
	mockFile := &MockWriter{}
	var receivedLines []string
	var linesMutex sync.Mutex

	lineCallback := func(line string) {
		linesMutex.Lock()
		defer linesMutex.Unlock()
		receivedLines = append(receivedLines, line)
	}

	interceptor := NewStreamInterceptor(mockFile, lineCallback)
	defer interceptor.Close()

	// 并发写入测试
	var wg sync.WaitGroup
	numGoroutines := 10
	linesPerGoroutine := 5

	for i := 0; i < numGoroutines; i++ {
		wg.Add(1)
		go func(id int) {
			defer wg.Done()
			for j := 0; j < linesPerGoroutine; j++ {
				line := fmt.Sprintf("goroutine-%d-line-%d\n", id, j)
				_, _ = interceptor.Write([]byte(line))
			}
		}(i)
	}

	wg.Wait()

	// 等待所有数据处理完成
	time.Sleep(200 * time.Millisecond)

	// 验证接收到的行数
	linesMutex.Lock()
	expectedLineCount := numGoroutines * linesPerGoroutine
	if len(receivedLines) != expectedLineCount {
		t.Errorf("Expected %d lines, got %d", expectedLineCount, len(receivedLines))
	}
	linesMutex.Unlock()

	// 验证文件内容包含所有数据
	fileContent := mockFile.String()
	for i := 0; i < numGoroutines; i++ {
		for j := 0; j < linesPerGoroutine; j++ {
			expectedLine := fmt.Sprintf("goroutine-%d-line-%d", i, j)
			if !strings.Contains(fileContent, expectedLine) {
				t.Errorf("File content missing line: %s", expectedLine)
			}
		}
	}
}

func TestStreamInterceptor_DataOrderConsistency(t *testing.T) {
	mockFile := &MockWriter{}
	var receivedLines []string
	var linesMutex sync.Mutex

	lineCallback := func(line string) {
		linesMutex.Lock()
		defer linesMutex.Unlock()
		receivedLines = append(receivedLines, line)
	}

	interceptor := NewStreamInterceptor(mockFile, lineCallback)
	defer interceptor.Close()

	// 按顺序写入数据
	testLines := []string{"first line", "second line", "third line"}
	for _, line := range testLines {
		_, _ = interceptor.Write([]byte(line + "\n"))
	}

	// 等待处理完成
	time.Sleep(100 * time.Millisecond)

	// 验证文件中的数据顺序
	fileContent := mockFile.String()
	expectedFileContent := strings.Join(testLines, "\n") + "\n"
	if fileContent != expectedFileContent {
		t.Errorf("File content order mismatch. Expected:\n%s\nGot:\n%s", expectedFileContent, fileContent)
	}

	// 验证回调接收的行顺序
	linesMutex.Lock()
	if len(receivedLines) != len(testLines) {
		t.Errorf("Expected %d lines in callback, got %d", len(testLines), len(receivedLines))
	}
	for i, expected := range testLines {
		if i < len(receivedLines) && receivedLines[i] != expected {
			t.Errorf("Line order mismatch at index %d. Expected %q, got %q", i, expected, receivedLines[i])
		}
	}
	linesMutex.Unlock()
}

func TestStreamInterceptor_MemoryEfficiency(t *testing.T) {
	mockFile := &MockWriter{}
	var receivedLines []string
	var linesMutex sync.Mutex

	lineCallback := func(line string) {
		linesMutex.Lock()
		defer linesMutex.Unlock()
		receivedLines = append(receivedLines, line)
	}

	interceptor := NewStreamInterceptor(mockFile, lineCallback)
	defer interceptor.Close()

	// 写入大量完整行数据（顺序写入，避免并发问题）
	var allData bytes.Buffer
	for i := 0; i < 1000; i++ {
		line := fmt.Sprintf("line %d\n", i)
		allData.WriteString(line)
	}

	// 一次性写入所有完整行
	_, _ = interceptor.Write(allData.Bytes())

	// 写入一个不完整的行
	_, _ = interceptor.Write([]byte("incomplete"))

	// 等待处理完成
	time.Sleep(200 * time.Millisecond)

	// 验证缓冲区只包含不完整的行
	interceptor.mutex.Lock()
	bufferContent := interceptor.buffer.String()
	interceptor.mutex.Unlock()

	if bufferContent != "incomplete" {
		t.Errorf("Expected buffer to contain only 'incomplete', got %q", bufferContent)
	}

	// 验证所有完整行都被处理
	linesMutex.Lock()
	actualLineCount := len(receivedLines)
	linesMutex.Unlock()

	if actualLineCount != 1000 {
		t.Errorf("Expected 1000 complete lines, got %d", actualLineCount)
		// 打印前几行和后几行用于调试
		linesMutex.Lock()
		if len(receivedLines) > 0 {
			t.Logf("First line: %q", receivedLines[0])
			if len(receivedLines) > 1 {
				t.Logf("Last line: %q", receivedLines[len(receivedLines)-1])
			}
		}
		linesMutex.Unlock()
	}

	// 验证缓冲区大小是最小的
	expectedBufferSize := len("incomplete")
	actualBufferSize := interceptor.buffer.Len()
	if actualBufferSize != expectedBufferSize {
		t.Errorf("Expected buffer size %d, got %d", expectedBufferSize, actualBufferSize)
	}
}

func TestStreamInterceptor_MultipleIncompleteLines(t *testing.T) {
	mockFile := &MockWriter{}
	var receivedLines []string
	var linesMutex sync.Mutex

	lineCallback := func(line string) {
		linesMutex.Lock()
		defer linesMutex.Unlock()
		receivedLines = append(receivedLines, line)
	}

	interceptor := NewStreamInterceptor(mockFile, lineCallback)
	defer interceptor.Close()

	// 写入混合数据：完整行 + 不完整行 + 完整行
	_, _ = interceptor.Write([]byte("line1\npartial"))
	_, _ = interceptor.Write([]byte("_line2\nline3\npartial2"))

	// 等待处理完成
	time.Sleep(100 * time.Millisecond)

	// 验证完整行被正确处理
	linesMutex.Lock()
	expectedLines := []string{"line1", "partial_line2", "line3"}
	if len(receivedLines) != len(expectedLines) {
		t.Errorf("Expected %d lines, got %d", len(expectedLines), len(receivedLines))
	}
	for i, expected := range expectedLines {
		if i < len(receivedLines) && receivedLines[i] != expected {
			t.Errorf("Line %d mismatch. Expected %q, got %q", i, expected, receivedLines[i])
		}
	}
	linesMutex.Unlock()

	// 验证缓冲区只包含最后的不完整行
	interceptor.mutex.Lock()
	bufferContent := interceptor.buffer.String()
	interceptor.mutex.Unlock()

	if bufferContent != "partial2" {
		t.Errorf("Expected buffer to contain 'partial2', got %q", bufferContent)
	}
}

func BenchmarkStreamInterceptor_MemoryUsage(b *testing.B) {
	mockFile := &MockWriter{}
	lineCallback := func(line string) {
		// 简单处理，避免影响性能测试
	}

	interceptor := NewStreamInterceptor(mockFile, lineCallback)
	defer interceptor.Close()

	// 准备测试数据：大量完整行 + 一个不完整行
	var testData bytes.Buffer
	for i := 0; i < 10000; i++ {
		testData.WriteString(fmt.Sprintf("line %d\n", i))
	}
	testData.WriteString("incomplete")

	data := testData.Bytes()

	b.ResetTimer()
	b.ReportAllocs()

	for i := 0; i < b.N; i++ {
		_, _ = interceptor.Write(data)

		// 验证缓冲区只包含不完整的行
		interceptor.mutex.Lock()
		bufferSize := interceptor.buffer.Len()
		interceptor.mutex.Unlock()

		if bufferSize != len("incomplete") {
			b.Errorf("Expected buffer size %d, got %d", len("incomplete"), bufferSize)
		}

		// 清理缓冲区为下次测试准备
		interceptor.mutex.Lock()
		interceptor.buffer.Reset()
		interceptor.mutex.Unlock()
	}
}

func BenchmarkBufferMethods(b *testing.B) {
	data := []byte("incomplete line data")

	b.Run(
		"CurrentMethod", func(b *testing.B) {
			buffer := &bytes.Buffer{}
			for i := 0; i < b.N; i++ {
				newBuffer := &bytes.Buffer{}
				newBuffer.Write(data)
				buffer = newBuffer
			}

			b.Logf("buffer: %d, %s", buffer.Len(), buffer.Bytes())
		},
	)

	b.Run(
		"NewBufferMethod", func(b *testing.B) {
			buffer := &bytes.Buffer{}
			for i := 0; i < b.N; i++ {
				buffer = bytes.NewBuffer(data)
			}

			b.Logf("buffer: %d, %s", buffer.Len(), buffer.Bytes())
		},
	)

	b.Run(
		"ResetMethod", func(b *testing.B) {
			buffer := &bytes.Buffer{}
			for i := 0; i < b.N; i++ {
				buffer.Reset()
				buffer.Write(data)
			}

			b.Logf("buffer: %d, %s", buffer.Len(), buffer.Bytes())
		},
	)
}
