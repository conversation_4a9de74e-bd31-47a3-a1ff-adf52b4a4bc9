package device

import (
	"io"

	"github.com/dlclark/regexp2"

	commonpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
)

type IDevice interface {
	io.Closer

	IDeviceInfo
	IDeviceAction
}

type IDeviceInfo interface {
	DeviceType() commonpb.DeviceType
	PlatformType() commonpb.PlatformType
	UDID() string
	RemoteAddress() string
}

type IDeviceAction interface {
	AppInstall(appPath string) error
	AppUninstall(appName string) error
	AppLaunch(appName string, args ...string) error
}

type findAndMatch struct {
	cmd string
	re  *regexp2.Regexp
}

type DisplayInfo struct {
	Width  int `json:"width"`
	Height int `json:"height"`
}

type Activity struct {
	PackageName  string `json:"package_name"`
	ActivityName string `json:"activity_name"`
	Pid          int64  `json:"pid"`
}

// ActivityStatistics Activity统计
// 注：这里不要增加`json`标签，要加也必须跟字段名一致
type ActivityStatistics struct {
	TestedActivity []string
	TotalActivity  []string
	Coverage       float64
}

type Artifact struct {
	Type        commonpb.ArtifactType // 类型
	FileName    string                // 文件名
	FilePath    string                // 文件路径
	Description string                // 描述
}

type Result struct {
	ActivityStatistics ActivityStatistics
	Artifacts          []Artifact
	ANRCount           int
	CrashCount         int
}
