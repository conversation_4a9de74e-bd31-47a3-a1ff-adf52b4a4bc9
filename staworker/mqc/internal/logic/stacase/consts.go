package stacase

import "time"

const (
	intervalOfWatchStopSignal = 5 * time.Second

	keyOfAcquireDevices       = "acquire_devices"
	keyOfCheckScreenState     = "check_screen_state"
	keyOfCheckWifiState       = "check_wifi_state"
	keyOfDownloadAppPackage   = "download_app_package"
	keyOfAcquireAccounts      = "acquire_accounts"
	keyOfInstallApp           = "install_app"
	keyOfClearAppLogs         = "clear_app_logs"
	keyOfExecuteBusinessLogic = "execute_business_logic"
	keyOfRunStabilityTest     = "run_stability_test"
	keyOfSaveAppLogs          = "save_app_logs"
	keyOfReleaseAccounts      = "release_accounts"
	keyOfReleaseDevices       = "release_devices"

	fastInputMethod      = "com.github.uiautomator/.FastInputIME"
	filenameOfAppLogs    = "app_logs.zip"
	descriptionOfAppLogs = "应用日志"
)

type desc = string

const (
	descOfAcquireDevicesEN desc = "acquire devices"
	descOfAcquireDevicesZH desc = "获取设备"

	descOfCheckScreenStateEN desc = "check screen state"
	descOfCheckScreenStateZH desc = "检查设备屏幕状态"

	descOfCheckWifiStateEN desc = "check WIFI state"
	descOfCheckWifiStateZH desc = "检查设备WIFI状态"

	descOfDownloadAppPackageEN desc = "download the app package"
	descOfDownloadAppPackageZH desc = "下载测试包"

	descOfAcquireAccountsEN desc = "acquire accounts"
	descOfAcquireAccountsZH desc = "获取账号"

	descOfInstallAppEN desc = "install the app"
	descOfInstallAppZH desc = "安装测试包"

	descOfClearAppLogsEN desc = "clear the app logs"
	descOfClearAppLogsZH desc = "清理应用日志"

	descOfExecuteBusinessLogicEN desc = "execute business logic"
	descOfExecuteBusinessLogicZH desc = "执行前置的业务逻辑"

	descOfRunStabilityTestEN desc = "run stability test"
	descOfRunStabilityTestZH desc = "运行稳定性测试"

	descOfSaveAppLogsEN = "save the app logs"
	descOfSaveAppLogsZH = "保存应用日志"

	descOfReleaseAccountsEN desc = "release accounts"
	descOfReleaseAccountsZH desc = "释放账号"

	descOfReleaseDevicesEN desc = "release devices"
	descOfReleaseDevicesZH desc = "释放设备"
)
