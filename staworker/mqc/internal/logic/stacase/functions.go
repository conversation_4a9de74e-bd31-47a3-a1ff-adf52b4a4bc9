package stacase

import (
	commonpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/staworker/mqc/internal/logic/stacase/device"
)

func ConvertToStabilityResult(input device.Result) *commonpb.StabilityResult {
	out := &commonpb.StabilityResult{
		ActivityStatistics: &commonpb.ActivityStatistics{
			TestedActivity: input.ActivityStatistics.TestedActivity,
			TotalActivity:  input.ActivityStatistics.TotalActivity,
			Coverage:       input.ActivityStatistics.Coverage,
		},
		Artifacts:  make([]*commonpb.Artifact, 0, len(input.Artifacts)),
		AnrCount:   uint32(input.ANRCount),
		CrashCount: uint32(input.CrashCount),
	}

	for _, artifact := range input.Artifacts {
		out.Artifacts = append(
			out.Artifacts, &commonpb.Artifact{
				Type:        artifact.Type,
				FileName:    artifact.FileName,
				FilePath:    artifact.FilePath,
				Description: artifact.Description,
			},
		)
	}

	return out
}
