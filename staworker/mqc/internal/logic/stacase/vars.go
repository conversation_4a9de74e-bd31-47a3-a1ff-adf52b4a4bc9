package stacase

import (
	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"

	commonpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
)

var (
	errorOfStopSignal            = errors.New("got a stop signal")
	errorOfEmptyAppPath          = errors.New("the app path is empty")
	errorOfNullStep              = errors.New("the test step is null")
	errorOfNullDevice            = errors.New("the device has not been initialized")
	errorOfNullScreenshotChannel = errors.New("the screenshot channel is null")
)

var _ error = (*errorOfInvalidStage)(nil)

type errorOfInvalidStage struct {
	stage commonpb.TestStage
}

func (e *errorOfInvalidStage) Error() string {
	return "invalid stage: " + protobuf.GetEnumStringOf(e.stage)
}
