package products

import (
	"context"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/staworker/mqc/internal/logic/stacase/device"
)

type NewBusinessLogicFunc func(context.Context, device.IDevice) (BusinessLogic, error)

type BusinessLogic interface {
	Launch(ctx context.Context) error
	Stop(ctx context.Context) error

	Login(ctx context.Context, account, password string) error

	ClearLogs() error
	GetLogs(localPath string) error
}
