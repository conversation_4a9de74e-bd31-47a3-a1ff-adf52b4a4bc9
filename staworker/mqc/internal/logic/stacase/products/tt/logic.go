package tt

import (
	"context"

	"github.com/pkg/errors"

	commonpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/staworker/mqc/internal/logic/stacase/device"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/staworker/mqc/internal/logic/stacase/products"
)

func NewLogic(ctx context.Context, device_ device.IDevice) (products.BusinessLogic, error) {
	switch device_.PlatformType() {
	case commonpb.PlatformType_ANDROID, commonpb.PlatformType_HarmonyOS:
		d, ok := device_.(*device.AndroidDevice)
		if !ok {
			return nil, errors.Errorf("expected %T, but got %T", (*device.AndroidDevice)(nil), device_)
		}

		l := NewAndroidLogic(ctx, d)
		return l, nil
	default:
		return nil, errors.Errorf("unsupported platform type: %v", device_.PlatformType())
	}
}
