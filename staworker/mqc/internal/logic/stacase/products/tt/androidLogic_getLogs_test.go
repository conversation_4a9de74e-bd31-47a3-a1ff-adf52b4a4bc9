package tt

import (
	"archive/zip"
	"context"
	"fmt"
	"io"
	"os"
	"path/filepath"
	"strings"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"github.com/zeromicro/go-zero/core/logx"
)

// MockDeviceNew simulates Android device behavior for testing the new implementation
type MockDeviceNew struct {
	serial           string
	existsFunc       func(path string) bool
	listFunc         func(remotePath string) ([]*mockFileInfo, error)
	readFileFunc     func(remoteFile string, writer io.Writer) error
	shouldFailOnList bool
	shouldFailOnRead bool
}

// mockFileInfo simulates gadb.FileInfo for testing
type mockFileInfo struct {
	name    string
	size    int64
	mode    os.FileMode
	modTime time.Time
	isDir   bool
}

func (m *mockFileInfo) Name() string       { return m.name }
func (m *mockFileInfo) Size() int64        { return m.size }
func (m *mockFileInfo) Mode() os.FileMode  { return m.mode }
func (m *mockFileInfo) ModTime() time.Time { return m.modTime }
func (m *mockFileInfo) IsDir() bool        { return m.isDir }
func (m *mockFileInfo) IsFile() bool       { return !m.isDir }
func (m *mockFileInfo) Path() string       { return m.name }
func (m *mockFileInfo) Sys() any           { return nil }

// DirectoryItemTest represents a directory item to be processed in tests
type DirectoryItemTest struct {
	RemotePath  string // Full path on device
	ZipBasePath string // Base path in zip file
}

func (m *MockDeviceNew) UDID() string {
	return m.serial
}

func (m *MockDeviceNew) Exists(path string) bool {
	if m.existsFunc != nil {
		return m.existsFunc(path)
	}
	return true // Default: path exists
}

func (m *MockDeviceNew) List(remotePath string) ([]*mockFileInfo, error) {
	if m.listFunc != nil {
		return m.listFunc(remotePath)
	}
	if m.shouldFailOnList {
		return nil, fmt.Errorf("mock list error")
	}
	// Default: return empty list
	return []*mockFileInfo{}, nil
}

func (m *MockDeviceNew) ReadFile(remoteFile string, writer io.Writer) error {
	if m.readFileFunc != nil {
		return m.readFileFunc(remoteFile, writer)
	}
	if m.shouldFailOnRead {
		return fmt.Errorf("mock read file error")
	}
	// Default: write some test content
	_, err := writer.Write([]byte("mock file content"))
	return err
}

// TestAndroidLogicNew is a test version that uses the new List/ReadFile approach
type TestAndroidLogicNew struct {
	*AndroidLogic
	mockDevice *MockDeviceNew
}

// createZipFromDeviceFiles creates a zip file by directly reading files from device using List and ReadFile
func (t *TestAndroidLogicNew) createZipFromDeviceFiles(localPath string) error {
	udid := t.mockDevice.UDID()

	// Create the zip file
	zipFile, err := os.Create(localPath)
	if err != nil {
		return fmt.Errorf("failed to create zip file, udid: %s, path: %s, error: %v", udid, localPath, err)
	}
	defer zipFile.Close()

	// Create a zip writer
	zipWriter := zip.NewWriter(zipFile)
	defer zipWriter.Close()

	// Iteratively traverse and add files to zip
	if err := t.iterativeTraverseAndAddToZip(zipWriter, pathOfCacheLogs); err != nil {
		return fmt.Errorf(
			"failed to traverse and add files to zip, udid: %s, path: %s, error: %v", udid, pathOfCacheLogs, err,
		)
	}

	t.Infof("successfully created zip from device files, udid: %s", udid)
	return nil
}

// iterativeTraverseAndAddToZip iteratively traverses a remote directory and adds files to zip
func (t *TestAndroidLogicNew) iterativeTraverseAndAddToZip(zipWriter *zip.Writer, rootPath string) error {
	udid := t.mockDevice.UDID()

	// Initialize the queue with the root directory
	queue := []DirectoryItemTest{
		{RemotePath: rootPath, ZipBasePath: ""},
	}

	// Process directories iteratively using a queue
	for len(queue) > 0 {
		// Dequeue the first item
		current := queue[0]
		queue = queue[1:]

		// List files and directories in the current remote path
		fileInfos, err := t.mockDevice.List(current.RemotePath)
		if err != nil {
			return fmt.Errorf("failed to list directory, udid: %s, path: %s, error: %v", udid, current.RemotePath, err)
		}

		for _, fileInfo := range fileInfos {
			// Skip current and parent directory entries
			if fileInfo.Name() == "." || fileInfo.Name() == ".." {
				continue
			}

			// Construct full remote path
			fullRemotePath := filepath.Join(current.RemotePath, fileInfo.Name())

			// Construct zip entry path (use forward slashes for zip)
			zipEntryPath := current.ZipBasePath
			if zipEntryPath != "" {
				zipEntryPath += "/"
			}
			zipEntryPath += fileInfo.Name()

			if fileInfo.IsDir() {
				// Create directory entry in zip
				dirHeader := &zip.FileHeader{
					Name:   zipEntryPath + "/",
					Method: zip.Store,
				}
				dirHeader.SetModTime(fileInfo.ModTime())

				if _, err := zipWriter.CreateHeader(dirHeader); err != nil {
					return fmt.Errorf(
						"failed to create directory entry in zip, udid: %s, path: %s, error: %v", udid, zipEntryPath,
						err,
					)
				}

				// Add subdirectory to queue for later processing
				queue = append(
					queue, DirectoryItemTest{
						RemotePath:  fullRemotePath,
						ZipBasePath: zipEntryPath,
					},
				)

				t.Infof(
					"queued directory for processing, udid: %s, remote: %s, zip: %s", udid, fullRemotePath, zipEntryPath,
				)
			} else if fileInfo.IsFile() {
				// Create file entry in zip
				fileHeader := &zip.FileHeader{
					Name:   zipEntryPath,
					Method: zip.Deflate,
				}
				fileHeader.SetModTime(fileInfo.ModTime())

				writer, err := zipWriter.CreateHeader(fileHeader)
				if err != nil {
					return fmt.Errorf(
						"failed to create file entry in zip, udid: %s, path: %s, error: %v", udid, zipEntryPath, err,
					)
				}

				// Read file content from device and write to zip
				if err := t.mockDevice.ReadFile(fullRemotePath, writer); err != nil {
					return fmt.Errorf(
						"failed to read file from device, udid: %s, path: %s, error: %v", udid, fullRemotePath, err,
					)
				}

				t.Infof(
					"added file to zip, udid: %s, remote: %s, zip: %s, size: %d", udid, fullRemotePath, zipEntryPath,
					fileInfo.Size(),
				)
			}
		}
	}

	return nil
}

func (t *TestAndroidLogicNew) GetLogs(localPath string) error {
	udid := t.mockDevice.UDID()

	// Ensure the local directory exists
	localDir := filepath.Dir(localPath)
	if err := os.MkdirAll(localDir, 0o755); err != nil {
		return fmt.Errorf("failed to create local directory, udid: %s, path: %s, error: %v", udid, localDir, err)
	}

	// Check if the logs directory exists on device
	if !t.mockDevice.Exists(pathOfCacheLogs) {
		return fmt.Errorf("logs directory does not exist on device, udid: %s, path: %s", udid, pathOfCacheLogs)
	}

	// Create zip file directly from device files using List and ReadFile
	if err := t.createZipFromDeviceFiles(localPath); err != nil {
		return fmt.Errorf("failed to create zip from device, udid: %s, path: %s, error: %v", udid, localPath, err)
	}

	t.Infof("successfully created zip file from device logs, udid: %s, path: %s", udid, localPath)
	return nil
}

// Helper function to create a test AndroidLogic with mock device
func createTestAndroidLogicNew(mockDevice *MockDeviceNew) *TestAndroidLogicNew {
	return &TestAndroidLogicNew{
		AndroidLogic: &AndroidLogic{
			Logger: logx.WithContext(context.Background()),
			ctx:    context.Background(),
		},
		mockDevice: mockDevice,
	}
}

func TestAndroidLogic_GetLogs_NewImplementation(t *testing.T) {
	tests := []struct {
		name            string
		setupMock       func() *MockDeviceNew
		localPath       string
		expectedError   string
		shouldCreateZip bool
		verifyZip       bool
	}{
		{
			name: "Success - Simple files",
			setupMock: func() *MockDeviceNew {
				return &MockDeviceNew{
					serial: "test-device-001",
					existsFunc: func(path string) bool {
						return path == pathOfCacheLogs
					},
					listFunc: func(remotePath string) ([]*mockFileInfo, error) {
						if remotePath == pathOfCacheLogs {
							return []*mockFileInfo{
								{name: "app.log", size: 100, isDir: false, modTime: time.Now()},
								{name: "error.log", size: 50, isDir: false, modTime: time.Now()},
							}, nil
						}
						return []*mockFileInfo{}, nil
					},
					readFileFunc: func(remoteFile string, writer io.Writer) error {
						content := "Log content for " + filepath.Base(remoteFile)
						_, err := writer.Write([]byte(content))
						return err
					},
				}
			},
			localPath:       "/tmp/test_logs_simple_new.zip",
			shouldCreateZip: true,
			verifyZip:       true,
		},
		{
			name: "Success - Nested directories",
			setupMock: func() *MockDeviceNew {
				return &MockDeviceNew{
					serial: "test-device-002",
					existsFunc: func(path string) bool {
						return path == pathOfCacheLogs
					},
					listFunc: func(remotePath string) ([]*mockFileInfo, error) {
						if remotePath == pathOfCacheLogs {
							return []*mockFileInfo{
								{name: "app.log", size: 100, isDir: false, modTime: time.Now()},
								{name: "debug", size: 0, isDir: true, modTime: time.Now()},
							}, nil
						} else if strings.HasSuffix(remotePath, "debug") {
							return []*mockFileInfo{
								{name: "verbose.log", size: 200, isDir: false, modTime: time.Now()},
								{name: "network.log", size: 150, isDir: false, modTime: time.Now()},
							}, nil
						}
						return []*mockFileInfo{}, nil
					},
					readFileFunc: func(remoteFile string, writer io.Writer) error {
						content := "Log content for " + filepath.Base(remoteFile)
						_, err := writer.Write([]byte(content))
						return err
					},
				}
			},
			localPath:       "/tmp/test_logs_nested_new.zip",
			shouldCreateZip: true,
			verifyZip:       true,
		},
		{
			name: "Error - Logs directory does not exist",
			setupMock: func() *MockDeviceNew {
				return &MockDeviceNew{
					serial: "test-device-003",
					existsFunc: func(path string) bool {
						return false // No paths exist
					},
				}
			},
			localPath:     "/tmp/test_logs_no_dir_new.zip",
			expectedError: "logs directory does not exist on device",
		},
		{
			name: "Error - List operation fails",
			setupMock: func() *MockDeviceNew {
				return &MockDeviceNew{
					serial:           "test-device-004",
					shouldFailOnList: true,
					existsFunc: func(path string) bool {
						return path == pathOfCacheLogs
					},
				}
			},
			localPath:     "/tmp/test_logs_list_fail_new.zip",
			expectedError: "mock list error",
		},
		{
			name: "Error - ReadFile operation fails",
			setupMock: func() *MockDeviceNew {
				return &MockDeviceNew{
					serial:           "test-device-005",
					shouldFailOnRead: true,
					existsFunc: func(path string) bool {
						return path == pathOfCacheLogs
					},
					listFunc: func(remotePath string) ([]*mockFileInfo, error) {
						if remotePath == pathOfCacheLogs {
							return []*mockFileInfo{
								{name: "app.log", size: 100, isDir: false, modTime: time.Now()},
							}, nil
						}
						return []*mockFileInfo{}, nil
					},
				}
			},
			localPath:     "/tmp/test_logs_read_fail_new.zip",
			expectedError: "mock read file error",
		},
		{
			name: "Success - Deep nested directories (iterative test)",
			setupMock: func() *MockDeviceNew {
				return &MockDeviceNew{
					serial: "test-device-006",
					existsFunc: func(path string) bool {
						return path == pathOfCacheLogs
					},
					listFunc: func(remotePath string) ([]*mockFileInfo, error) {
						switch remotePath {
						case pathOfCacheLogs:
							return []*mockFileInfo{
								{name: "root.log", size: 50, isDir: false, modTime: time.Now()},
								{name: "level1", size: 0, isDir: true, modTime: time.Now()},
							}, nil
						case filepath.Join(pathOfCacheLogs, "level1"):
							return []*mockFileInfo{
								{name: "level1.log", size: 100, isDir: false, modTime: time.Now()},
								{name: "level2", size: 0, isDir: true, modTime: time.Now()},
							}, nil
						case filepath.Join(pathOfCacheLogs, "level1", "level2"):
							return []*mockFileInfo{
								{name: "level2.log", size: 150, isDir: false, modTime: time.Now()},
								{name: "level3", size: 0, isDir: true, modTime: time.Now()},
							}, nil
						case filepath.Join(pathOfCacheLogs, "level1", "level2", "level3"):
							return []*mockFileInfo{
								{name: "deep.log", size: 200, isDir: false, modTime: time.Now()},
							}, nil
						}
						return []*mockFileInfo{}, nil
					},
					readFileFunc: func(remoteFile string, writer io.Writer) error {
						content := "Content for " + filepath.Base(remoteFile)
						_, err := writer.Write([]byte(content))
						return err
					},
				}
			},
			localPath:       "/tmp/test_logs_deep_nested_new.zip",
			shouldCreateZip: true,
			verifyZip:       true,
		},
	}

	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				// Setup
				mockDevice := tt.setupMock()
				logic := createTestAndroidLogicNew(mockDevice)

				// Ensure test directory exists
				testDir := filepath.Dir(tt.localPath)
				require.NoError(t, os.MkdirAll(testDir, 0o755))

				// Cleanup
				defer func() {
					os.Remove(tt.localPath)
				}()

				// Execute
				err := logic.GetLogs(tt.localPath)

				// Verify
				if tt.expectedError != "" {
					assert.Error(t, err)
					assert.Contains(t, err.Error(), tt.expectedError)
				} else {
					assert.NoError(t, err)

					if tt.shouldCreateZip {
						assert.FileExists(t, tt.localPath)

						if tt.verifyZip {
							// Verify it's a valid zip file
							_, err := zip.OpenReader(tt.localPath)
							assert.NoError(t, err, "Created file should be a valid zip")
						}
					}
				}
			},
		)
	}
}
