package registry

import (
	"context"
	"sync"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/staworker/mqc/internal/logic/stacase/device"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/staworker/mqc/internal/logic/stacase/products"
)

var (
	_ products.BusinessLogic = (*NoopBusinessLogic)(nil)

	ErrUnSupportedProduct = errors.New("unsupported product")
)

type registry struct {
	cache map[string]products.NewBusinessLogicFunc

	lock sync.RWMutex
}

var r = &registry{
	cache: make(map[string]products.NewBusinessLogicFunc),
}

func Register(name string, fn products.NewBusinessLogicFunc) {
	r.lock.Lock()
	defer r.lock.Unlock()

	r.cache[name] = fn
}

func Has(name string) bool {
	r.lock.RLock()
	defer r.lock.RUnlock()

	_, ok := r.cache[name]
	return ok
}

func NewLogic(ctx context.Context, name string, device_ device.IDevice) (products.BusinessLogic, error) {
	r.lock.RLock()
	defer r.lock.RUnlock()

	fn, ok := r.cache[name]
	if !ok {
		return &NoopBusinessLogic{}, ErrUnSupportedProduct
	}

	return fn(ctx, device_)
}

type NoopBusinessLogic struct{}

func (l *NoopBusinessLogic) Launch(ctx context.Context) error {
	return nil
}

func (l *NoopBusinessLogic) Stop(ctx context.Context) error {
	return nil
}

func (l *NoopBusinessLogic) Login(ctx context.Context, account, password string) error {
	return nil
}

func (l *NoopBusinessLogic) ClearLogs() error {
	return nil
}

func (l *NoopBusinessLogic) GetLogs(localPath string) error {
	return nil
}
