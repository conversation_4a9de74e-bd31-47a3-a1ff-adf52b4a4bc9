package stacase

import (
	"context"
	"fmt"
	"os"
	"path/filepath"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/electricbubble/gidevice/pkg/ipa"
	"github.com/pkg/errors"
	"github.com/redis/go-redis/v9"
	"github.com/shogo82148/androidbinary/apk"
	"github.com/zeromicro/go-zero/core/jsonx"
	"github.com/zeromicro/go-zero/core/lang"
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/mr"
	"github.com/zeromicro/go-zero/core/threading"
	"github.com/zeromicro/go-zero/zrpc"
	"google.golang.org/protobuf/types/known/timestamppb"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworkerv2/base"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/singleflight"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sqlbuilder/rpc"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/timewheel"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	accountcommon "gitlab.ttyuyin.com/TestDevelopment/probe-backend/account/common"
	accountpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/account/rpc/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/collector"
	commonconsts "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/logger"
	commonpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pkgpuller"
	commonutils "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/utils"
	devicehubcommon "gitlab.ttyuyin.com/TestDevelopment/probe-backend/devicehub/common"
	devicehubpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/devicehub/rpc/pb"
	discoverypb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/discovery/rpc/pb"
	dispatcherutils "gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/common/utils"
	dispatcherpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/rpc/pb"
	managercommon "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	managerpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
	reporterpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/staworker/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/staworker/mqc/internal/logic/stacase/device"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/staworker/mqc/internal/logic/stacase/products"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/staworker/mqc/internal/logic/stacase/registry"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/staworker/mqc/internal/svc"
)

type ExecuteStabilityCaseTaskLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext

	logger *logger.StepLogger
	stopCh chan lang.PlaceholderType

	taskInfo *commonpb.StabilityCaseTaskInfo
	state    dispatcherpb.ComponentState

	key        string
	appPath    string                // 待测试的App文件
	appName    string                // 待测试的App名称（Android: package_name, iOS: bundle_id）
	reportPath string                // 测试报告的存放路径
	devices    []*devicehubpb.Device // 占用的设备列表（注：正常情况下只会占用一个设备）
	accounts   []*Account            // 占用的账号列表（注：正常情况下只会占用一个账号）
	result     device.Result         // 稳定性测试结果

	initDeviceOnce sync.Once
	device         *device.AndroidDevice // 注：后续支持`iOS`时，这里需要改成接口
	businessLogic  products.BusinessLogic
}

func NewExecuteStabilityCaseTaskLogic(
	ctx context.Context, svcCtx *svc.ServiceContext, taskInfo *commonpb.StabilityCaseTaskInfo,
) *ExecuteStabilityCaseTaskLogic {
	l := &ExecuteStabilityCaseTaskLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,

		logger: logger.NewStepLogger(ctx, svcCtx.Config.Log),
		stopCh: make(chan lang.PlaceholderType),

		taskInfo: taskInfo,
		state:    dispatcherpb.ComponentState_Pending,

		devices:  make([]*devicehubpb.Device, 0, 1),
		accounts: make([]*Account, 0, 1),
	}
	l.init()
	return l
}

func (l *ExecuteStabilityCaseTaskLogic) init() {
	taskID := l.taskInfo.GetTaskId()
	l.key = taskID
	if ss := strings.Split(taskID, ":"); len(ss) >= 2 {
		l.key = ss[1]
	}

	suffix := ""
	switch l.taskInfo.GetPlatformType() {
	case commonpb.PlatformType_ANDROID, commonpb.PlatformType_HarmonyOS:
		suffix = commonconsts.ConstSuffixOfApk
	case commonpb.PlatformType_IOS:
		suffix = commonconsts.ConstSuffixOfIpa
	default:
		l.Warnf("invalid platform type: %s", l.taskInfo.GetPlatformType().String())
	}

	l.appPath = filepath.Join(
		l.svcCtx.Config.LocalPath, common.ConstAppDownloadPath, fmt.Sprintf("%s%s", l.key, suffix),
	)
	l.reportPath = filepath.Join(l.svcCtx.Config.LocalPath, common.ConstStabilityTestReportPath, taskID)
}

func (l *ExecuteStabilityCaseTaskLogic) Execute() (err error) {
	defer func() {
		if r := recover(); r != nil {
			l.state = dispatcherpb.ComponentState_Panic
			l.Errorf(
				"got a panic while executing the stability case task, task_id: %s, error: %+v",
				l.taskInfo.GetTaskId(), r,
			)
		} else if err != nil {
			var target *errorOfInvalidStage
			if errors.Is(err, errorOfEmptyAppPath) ||
				errors.Is(err, errorOfNullStep) ||
				errors.As(err, &target) {
				l.state = dispatcherpb.ComponentState_Panic
			} else if errors.Is(err, errorOfStopSignal) {
				l.state = dispatcherpb.ComponentState_Stop
			} else {
				l.state = dispatcherpb.ComponentState_Failure
			}
		} else {
			l.state = dispatcherpb.ComponentState_Success
		}

		// 结束执行
		if e := l.sendTaskStatusToReporter(); e != nil {
			l.Error(e)
		}
	}()

	// 开始执行
	l.state = dispatcherpb.ComponentState_Started
	if err = l.sendTaskStatusToReporter(); err != nil {
		return err
	}

	threading.GoSafeCtx(l.ctx, l.watchStopSignal)

	defer func() {
		_ = l.runSteps(l.teardownSteps())
	}()

	if err = l.runSteps(l.setupSteps()); err != nil {
		return err
	}

	if err = l.runSteps(l.testSteps()); err != nil {
		return err
	}

	return nil
}

func (l *ExecuteStabilityCaseTaskLogic) watchStopSignal() {
	ticker := timewheel.NewTicker(intervalOfWatchStopSignal)
	defer ticker.Stop()

	for {
		select {
		case <-l.ctx.Done():
			return
		case <-ticker.C:
			if err := l.isStopped(); err != nil {
				close(l.stopCh)
				return
			}
		}
	}
}

func (l *ExecuteStabilityCaseTaskLogic) isStopped() (err error) {
	taskID := l.taskInfo.GetTaskId()

	defer func() {
		if r := recover(); r != nil {
			l.state = dispatcherpb.ComponentState_Panic
			err = errors.Errorf("got a panic while checking the stop status, task_id: %s, error: %+v", taskID, r)
		}
	}()

	stop, err := dispatcherutils.GetStopStatus(l.ctx, l.svcCtx.DispatcherRedis, taskID)
	if err != nil {
		l.state = dispatcherpb.ComponentState_Panic
		return errors.Wrapf(err, "failed to get the stop status of task, task_id: %s", taskID)
	} else if stop {
		l.state = dispatcherpb.ComponentState_Stop
		return errors.Errorf("got a stop signal of task, task_id: %s", taskID)
	}

	return nil
}

func (l *ExecuteStabilityCaseTaskLogic) downloadAppPackage() (err error) {
	var (
		passed      = l.svcCtx.DiscoveryRPC.HealthCheck()
		byDiscovery = l.svcCtx.DiscoveryRPC.IsDiscovery()

		appInfo *commonpb.AppInfo
	)

	if passed == nil && byDiscovery {
		appInfo, err = l.downloadAppByDiscovery()
	} else {
		appInfo, err = l.downloadAppLocally()
	}
	if err != nil {
		return err
	} else if appInfo == nil {
		return errors.Errorf(
			"got a null app info after downloading the app package, package_name: %s, link: %s, by_discovery: %v, %t",
			l.taskInfo.GetPackageName(), l.taskInfo.GetAppDownloadLink(), passed, byDiscovery,
		)
	}

	l.appName = appInfo.GetName()
	l.cacheAppInfo(appInfo)

	l.logger.Infof(
		"finish to download the app package, link: %s, version: %s, size: %d, app_name: %s, app_path: %s, by_discovery: %v, %t",
		appInfo.GetDownloadLink(), appInfo.GetVersion(), appInfo.GetSize(), l.appName, l.appPath, passed, byDiscovery,
	)
	return nil
}

func (l *ExecuteStabilityCaseTaskLogic) downloadAppLocally() (*commonpb.AppInfo, error) {
	var (
		packageName = l.taskInfo.GetPackageName()
		link        = l.taskInfo.GetAppDownloadLink()

		version, name string
	)

	stat, err := os.Stat(l.appPath)
	if err == nil && !stat.IsDir() && stat.Size() > 0 {
		l.logger.Infof(
			"the app package has been downloaded, package_name: %s, link: %s, path: %s, size: %d",
			packageName, link, l.appPath, stat.Size(),
		)
	} else {
		ctx, cancel := commonutils.NewTimeoutContext(l.ctx, common.ConstTimeoutOfDownloadAppPackage)
		defer cancel()

		if link != "" {
			err = utils.DownloadFromUrl(ctx, link, l.appPath)
		} else {
			var puller pkgpuller.AppPkgPuller
			puller, err = pkgpuller.NewAppPkgPuller(ctx, pkgpuller.AppPkgNameType(packageName), l.appPath)
			if err != nil {
				return nil, errors.Errorf(
					"failed to new app package puller, package_name: %s, path: %s, error: %+v",
					packageName, l.appPath, err,
				)
			}

			link, err = puller.Pull()
		}
		if err != nil {
			return nil, errors.Errorf(
				"failed to download the app package, package_name: %s, link: %s, path: %s, error: %+v",
				packageName, link, l.appPath, err,
			)
		}

		stat, err = os.Stat(l.appPath)
		if err != nil {
			return nil, errors.Errorf(
				"the app package was not found locally after download, package_name: %s, link: %s, path: %s, error: %+v",
				packageName, link, l.appPath, err,
			)
		}
	}

	switch l.taskInfo.GetPlatformType() {
	case commonpb.PlatformType_ANDROID, commonpb.PlatformType_HarmonyOS:
		info, err := apk.OpenFile(l.appPath)
		if err != nil {
			l.logger.Errorf(
				"failed to open the apk file, package_name: %s, link: %s, path: %s, size: %d, error: %+v",
				packageName, link, l.appPath, stat.Size(), err,
			)
		} else {
			name = info.PackageName()
			version = info.Manifest().VersionName
		}
	case commonpb.PlatformType_IOS:
		info, err := ipa.Info(l.appPath)
		if err != nil {
			l.logger.Errorf(
				"failed to open the ipa file, package_name: %s, link: %s, path: %s, size: %d, error: %+v",
				packageName, link, l.appPath, stat.Size(), err,
			)
		} else {
			name = info.CFBundleIdentifier
			version = info.CFBundleShortVersionString
			if len(info.CFBundleVersion) > 0 {
				version += "-" + info.CFBundleVersion
			}
		}
	}

	return &commonpb.AppInfo{
		DownloadLink: link,
		Version:      version,
		Name:         name,
		Size:         stat.Size(),
	}, nil
}

func (l *ExecuteStabilityCaseTaskLogic) downloadAppByDiscovery() (*commonpb.AppInfo, error) {
	ctx, cancel := commonutils.NewTimeoutContext(l.ctx, common.ConstTimeoutOfDownloadAppPackage)
	defer cancel()

	out, err := l.svcCtx.DiscoveryRPC.DownloadApp(
		ctx, &discoverypb.DownloadAppReq{
			PlatformType: l.taskInfo.GetPlatformType(),
			AppName:      l.taskInfo.GetPackageName(),
			Link:         l.taskInfo.GetAppDownloadLink(),
			Filename:     filepath.Base(l.appPath),
		},
	)
	if err != nil {
		return nil, err
	}

	return &commonpb.AppInfo{
		DownloadLink: out.GetLink(),
		Version:      out.GetVersion(),
		Name:         out.GetAppName(),
		Size:         out.GetSize(),
	}, nil
}

func (l *ExecuteStabilityCaseTaskLogic) cacheAppInfo(info *commonpb.AppInfo) {
	if info == nil || info.GetDownloadLink() == "" || info.GetVersion() == "" {
		return
	}

	key := fmt.Sprintf("%s:%s", commonconsts.ConstCachePrefixOfAppInfo, l.taskInfo.GetTaskId())
	value := protobuf.MarshalJSONToStringIgnoreError(info)
	result, err := l.svcCtx.Redis.SetnxExCtx(l.ctx, key, value, 24*60*60)
	if err != nil {
		if !errors.Is(err, redis.Nil) {
			l.Errorf("failed to cache the app info, key: %s, value: %s, error: %+v", key, value, err)
		}

		return
	}

	l.Infof("cache the app info successfully, key: %s, value: %s, result: %t", key, value, result)
}

func (l *ExecuteStabilityCaseTaskLogic) getAppInfo() (*commonpb.AppInfo, error) {
	if info, err := l.getAppInfoByCache(); err == nil {
		return info, nil
	}

	return l.getAppInfoByPackage()
}

func (l *ExecuteStabilityCaseTaskLogic) getAppInfoByCache() (*commonpb.AppInfo, error) {
	key := fmt.Sprintf("%s:%s", commonconsts.ConstCachePrefixOfAppInfo, l.taskInfo.GetTaskId())
	result, err := l.svcCtx.Redis.GetCtx(l.ctx, key)
	if err != nil && !errors.Is(err, redis.Nil) {
		l.Errorf("failed to get the app info, key: %s, error: %+v", key, err)
		return nil, err
	}

	info := &commonpb.AppInfo{}
	if err = protobuf.UnmarshalJSONFromString(result, info); err != nil {
		l.Errorf("failed to unmarshal the app info, key: %s, value: %s, error: %+v", key, result, err)
		return nil, err
	}

	l.Infof("get the app info successfully, key: %s, value: %s", key, result)
	return info, nil
}

func (l *ExecuteStabilityCaseTaskLogic) getAppInfoByPackage() (*commonpb.AppInfo, error) {
	if l.appPath == "" {
		return nil, errorOfEmptyAppPath
	}

	stat, err := os.Stat(l.appPath)
	if err != nil {
		return nil, err
	} else if stat.IsDir() || stat.Size() == 0 {
		return nil, errorOfEmptyAppPath
	}

	var (
		platformType = l.taskInfo.GetPlatformType()
		packageName  = l.taskInfo.GetPackageName()

		version string
	)

	switch platformType {
	case commonpb.PlatformType_ANDROID, commonpb.PlatformType_HarmonyOS:
		info, err := apk.OpenFile(l.appPath)
		if err != nil {
			l.logger.Errorf(
				"failed to open the apk file, package_name: %s, path: %s, size: %d, error: %+v",
				packageName, l.appPath, stat.Size(), err,
			)
		} else {
			l.appName = info.PackageName()
			version = info.Manifest().VersionName
		}
	case commonpb.PlatformType_IOS:
		info, err := ipa.Info(l.appPath)
		if err != nil {
			l.logger.Errorf(
				"failed to open the ipa file, package_name: %s, path: %s, size: %d, error: %+v",
				packageName, l.appPath, stat.Size(), err,
			)
		} else {
			l.appName = info.CFBundleIdentifier
			version = info.CFBundleShortVersionString
			if len(info.CFBundleVersion) > 0 {
				version += "-" + info.CFBundleVersion
			}
		}
	default:
		return nil, errors.Errorf("unsupported platform type: %s", protobuf.GetEnumStringOf(platformType))
	}

	return &commonpb.AppInfo{
		DownloadLink: l.taskInfo.GetAppDownloadLink(), // 可能是空
		Version:      version,
		Name:         l.appName,
	}, nil
}

func (l *ExecuteStabilityCaseTaskLogic) acquireAccount(ctx context.Context) (err error) {
	accountConfig := l.taskInfo.GetAccountConfig()

	//defer func() {
	//	if err != nil {
	//		_ = l.releaseAccount(ctx)
	//	}
	//}()

	out, err := l.svcCtx.AccountRPC.AcquireAccount(
		ctx, &accountpb.QueryAccountPoolEnvDataRequest{
			PoolEnvTable:  accountConfig.GetPoolEnvTable(),
			ExpectedCount: 1,
		},
		zrpc.WithCallTimeout(accountcommon.ConstRPCAcquireAccountTimeout),
	)
	if err != nil {
		return err
	}

	if len(out.GetMatchData()) == 0 {
		return errors.Errorf("no accounts were occupied, pool_env_table %s", accountConfig.GetPoolEnvTable())
	}
	l.logger.Infof("acquired accounts: %s", protobuf.MarshalJSONIgnoreError(out))

	for _, account := range out.GetMatchData() {
		item := &Account{}
		for _, column := range account.GetAccount() {
			switch column.GetField() {
			case accountcommon.BuiltinTableFieldOfAccount:
				item.Username = column.GetValue()
				item.Token = column.GetLockValue()
			case accountcommon.BuiltinTableFieldOfPassword:
				item.Password = column.GetValue()
			default:
			}

			if item.Username != "" && item.Password != "" && item.Token != "" {
				break
			}
		}

		if item.Username != "" && item.Password != "" && item.Token != "" {
			l.accounts = append(l.accounts, item)
		}
	}

	return nil
}

func (l *ExecuteStabilityCaseTaskLogic) releaseAccount(ctx context.Context) (err error) {
	if len(l.accounts) == 0 {
		return nil
	}

	accountConfig := l.taskInfo.GetAccountConfig()
	in := &accountpb.ReleaseTestAccountRequest{
		ReleaseTasAccountArray: []*accountpb.ReleaseTestAccountRequest_PoolAccount{
			{
				PoolEnvTable: accountConfig.GetPoolEnvTable(),
				AccountArray: make([]*accountpb.ReleaseTestAccountRequest_Account, 0, len(l.accounts)),
			},
		},
	}
	for _, account := range l.accounts {
		in.ReleaseTasAccountArray[0].AccountArray = append(
			in.ReleaseTasAccountArray[0].AccountArray, &accountpb.ReleaseTestAccountRequest_Account{
				Account:   account.Username,
				LockValue: account.Token,
			},
		)
	}

	_, err = l.svcCtx.AccountRPC.ReleaseAccount(ctx, in)
	if err != nil {
		l.logger.Errorf(
			"failed to release the account, accounts: %s, error: %+v",
			jsonx.MarshalIgnoreError(l.accounts), err,
		)
	}

	return err
}

func (l *ExecuteStabilityCaseTaskLogic) acquireDevice(ctx context.Context) (err error) {
	var (
		projectID = l.taskInfo.GetProjectId()

		deviceType   = l.taskInfo.GetDeviceType()
		platformType = l.taskInfo.GetPlatformType()
		udid         = l.taskInfo.GetUdid()
		duration     = time.Duration(l.taskInfo.GetDuration()) * time.Minute
	)

	//defer func() {
	//	if err != nil {
	//		_ = l.releaseDevice(ctx)
	//	}
	//}()

	condition := &rpc.Condition{
		Group: &rpc.GroupCondition{
			Relationship: constants.AND,
			Conditions: []*rpc.Condition{
				{
					Single: &rpc.SingleCondition{
						Field:   string(devicehubcommon.DeviceFieldOfType),
						Compare: constants.EQ,
						Other: &rpc.Other{
							Value: strconv.Itoa(int(deviceType.Number())),
						},
					},
				},
				{
					Single: &rpc.SingleCondition{
						Field:   string(devicehubcommon.DeviceFieldOfPlatform),
						Compare: constants.EQ,
						Other: &rpc.Other{
							Value: strconv.Itoa(int(platformType.Number())),
						},
					},
				},
			},
		},
	}
	if !strings.HasPrefix(udid, common.ConstPrefixOfRandomDevice) {
		condition.Group.Conditions = append(
			condition.Group.Conditions, &rpc.Condition{
				Single: &rpc.SingleCondition{
					Field:   string(devicehubcommon.DeviceFieldOfUDID),
					Compare: constants.EQ,
					Other: &rpc.Other{
						Value: udid,
					},
				},
			},
		)
	}

	out, err := l.svcCtx.ManagerRPC.AcquireProjectDeviceByCondition(
		ctx, &managerpb.AcquireProjectDeviceByConditionReq{
			ProjectId:  projectID,
			Usage:      commonpb.DeviceUsage_STABILITY_TESTING,
			Condition:  condition,
			Count:      1,
			Expiration: int64((duration + common.ConstTimeoutOfDownloadAppPackage + common.ConstTimeoutOfInstallApp + common.ConstTimeoutOfBusinessLogic + time.Minute).Seconds()),
		},
		zrpc.WithCallTimeout(devicehubcommon.ConstRPCAcquireDeviceTimeout),
	)
	if err != nil {
		return errors.Wrapf(
			err,
			"failed to acquire project device, device_type: %s, platform_type: %s",
			deviceType.String(), platformType.String(),
		)
	}

	if len(out.GetDevices()) == 0 {
		return errors.Errorf(
			"no project devices were occupied, device_type: %s, platform_type: %s",
			deviceType.String(), platformType.String(),
		)
	}
	l.logger.Infof("acquired project devices: %s", protobuf.MarshalJSONIgnoreError(out))

	for _, d := range out.GetDevices() {
		if d == nil || d.GetDevice() == nil {
			continue
		}

		l.devices = append(l.devices, d.GetDevice())
	}

	return nil
}

func (l *ExecuteStabilityCaseTaskLogic) releaseDevice(ctx context.Context) (err error) {
	if err = mr.MapReduceVoid[*devicehubpb.Device, any](
		func(source chan<- *devicehubpb.Device) {
			for _, d := range l.devices {
				if d == nil || d.GetUdid() == "" || d.GetToken() == "" {
					continue
				}

				source <- d
			}
		},
		func(item *devicehubpb.Device, writer mr.Writer[any], cancel func(error)) {
			var (
				udid  = item.GetUdid()
				token = item.GetToken()
			)

			l.logger.Infof("ready to release the project device, udid: %s, token: %s", udid, token)

			ctx1, cancel1 := commonutils.NewTimeoutContext(ctx, managercommon.ConstRPCReleaseProjectDeviceTimeout)
			defer cancel1()

			if _, err := l.svcCtx.ManagerRPC.ReleaseProjectDevice(
				ctx1, &managerpb.ReleaseProjectDeviceReq{
					ProjectId: l.taskInfo.GetProjectId(),
					Usage:     commonpb.DeviceUsage_STABILITY_TESTING,
					Udid:      udid,
					Token:     token,
				},
			); err != nil {
				l.logger.Errorf(
					"failed to release the project device by grpc, udid: %s, token: %s, error: %+v",
					udid, token, err,
				)

				l.logger.Infof("try to release the project device by mq, udid: %s, token: %s", udid, token)
				ctx2, cancel2 := commonutils.NewTimeoutContext(ctx, devicehubcommon.ConstRPCReleaseDeviceTimeout)
				defer cancel2()

				if _, err := l.svcCtx.DeviceHubProducer.Send(
					ctx2, base.NewTask(
						commonconsts.MQTaskTypeDeviceHubHandleReleaseDevice,
						protobuf.MarshalJSONIgnoreError(
							&devicehubpb.ReleaseDeviceReq{
								Udid:  udid,
								Token: token,
							},
						),
					), base.QueuePriorityDefault,
				); err != nil {
					l.logger.Errorf(
						"failed to release the project device by mq, udid: %s, token: %s, error: %+v",
						udid, token, err,
					)
					return
				}
			}

			l.logger.Infof("finish to release the project device, udid: %s, token: %s", udid, token)
		},
		func(pipe <-chan any, cancel func(error)) {
		},
	); err != nil {
		l.logger.Errorf(
			"got an error while releasing the project device, devices: %s, error: %+v",
			protobuf.MarshalJSONWithMessagesToStringIgnoreError(l.devices), err,
		)
	}

	return err
}

func (l *ExecuteStabilityCaseTaskLogic) getDevice(udid string) (*devicehubpb.Device, error) {
	d, err := l.svcCtx.ManagerRPC.GetProjectDevice(
		l.ctx, &managerpb.GetProjectDeviceReq{
			ProjectId: l.taskInfo.GetProjectId(),
			Usage:     commonpb.DeviceUsage_STABILITY_TESTING,
			Udid:      udid,
		},
	)
	if err != nil {
		return nil, err
	}

	return d.GetDevice().GetDevice(), nil
}

func (l *ExecuteStabilityCaseTaskLogic) initDevice() error {
	var (
		deviceType   = l.taskInfo.GetDeviceType()
		platformType = l.taskInfo.GetPlatformType()

		err error
	)

	if len(l.devices) == 0 {
		return errors.Errorf(
			"no devices were acquired, device_type: %s, platform_type: %s",
			deviceType.String(), platformType.String(),
		)
	}

	l.initDeviceOnce.Do(
		func() {
			device_ := l.devices[0] // 只取第一个
			udid := device_.GetUdid()
			addr := device_.GetRemoteAddress()
			if !strings.HasSuffix(l.reportPath, udid) {
				l.reportPath = filepath.Join(l.reportPath, udid)
			}

			breakLoop := false
			for i := 0; i < 3 && !breakLoop; i++ {
				switch platformType {
				case commonpb.PlatformType_ANDROID, commonpb.PlatformType_HarmonyOS:
					l.device, err = device.NewAndroidDevice(
						l.ctx, deviceType, udid, addr,
						device.WithAPKPath(l.appPath),
						device.WithActivities(l.taskInfo.GetActivities()...),
					)
					if err != nil {
						if d, e := l.getDevice(udid); e == nil {
							if addr_ := d.GetRemoteAddress(); addr_ != addr {
								l.logger.Infof(
									"the remote address of device has changed, udid: %s, old: %s, new: %s",
									udid, addr, addr_,
								)
								l.devices[0] = d
								addr = addr_
							}
						} else {
							l.Errorf("failed to get project device by udid, udid: %s, error: %+v", udid, e)
						}

						time.Sleep(time.Second)
						continue
					}
				default:
					err = errors.Errorf("unsupported platform type: %s", platformType.String())
				}
				breakLoop = true
			}
		},
	)

	return err
}

func (l *ExecuteStabilityCaseTaskLogic) wakeupAndUnlockDevice() error {
	if l.device == nil {
		return errorOfNullDevice
	}

	udid := l.device.UDID()
	if !l.device.IsScreenOn() {
		l.device.Wakeup()
		time.Sleep(time.Second)
		if !l.device.IsScreenOn() {
			return errors.Errorf("failed to wakeup the device, udid: %s", udid)
		}
	} else {
		l.logger.Infof("the screen of device is already on, udid: %s", udid)
	}

	if l.device.IsLocked() {
		l.device.Unlock()
		time.Sleep(time.Second)
		if l.device.IsLocked() {
			return errors.Errorf("failed to unlock the device, udid: %s", udid)
		}
	} else {
		l.logger.Infof("the device is already unlocked, udid: %s", udid)
	}

	return nil
}

func (l *ExecuteStabilityCaseTaskLogic) enableWifi() error {
	if l.device == nil {
		return errorOfNullDevice
	}

	udid := l.device.UDID()
	if l.device.IsWifiOn() {
		l.logger.Infof("the wifi of device is already on, udid: %s", udid)
		return nil
	}

	if err := l.device.SetWifi(true); err != nil {
		return err
	}
	time.Sleep(5 * time.Second)
	if !l.device.IsWifiOn() {
		return errors.Errorf("failed to enable wifi, udid: %s", udid)
	}

	return nil
}

func (l *ExecuteStabilityCaseTaskLogic) installApp(ctx context.Context) (err error) {
	if l.device == nil {
		return errorOfNullDevice
	}

	udid := l.device.UDID()
	resultCh := make(chan error, 1)
	// 注：这里不使用`threading.GoSafeCtx`方法，避免产生`panic`的时候没有把`err`通过`resultCh`返回，导致外层`select`阻塞
	go func() {
		var (
			passed      = l.svcCtx.DiscoveryRPC.HealthCheck()
			byDiscovery = l.svcCtx.DiscoveryRPC.IsDiscovery()

			e error
		)

		defer func() {
			if r := recover(); r != nil {
				l.logger.Errorf(
					"failed to install app, udid: %s, path: %s, by_discovery: %v, %t, error: %+v",
					udid, l.appPath, passed, byDiscovery, r,
				)
				e = errors.New(fmt.Sprintf("%v", r))
			} else if e != nil {
				l.logger.Errorf(
					"failed to install app, udid: %s, path: %s, by_discovery: %v, %t, error: %+v",
					udid, l.appPath, passed, byDiscovery, e,
				)
			} else {
				l.logger.Infof(
					"install app successfully, udid: %s, path: %s, by_discovery: %v, %t",
					udid, l.appPath, passed, byDiscovery,
				)
			}
			resultCh <- e
		}()

		if passed == nil && byDiscovery {
			_, e = l.svcCtx.DiscoveryRPC.InstallApp(
				ctx, &discoverypb.InstallAppReq{
					PlatformType:  l.taskInfo.GetPlatformType(),
					Udid:          udid,
					RemoteAddress: l.device.RemoteAddress(),
					Filename:      filepath.Base(l.appPath),
				},
			)
		} else {
			e = l.device.AppInstall(l.appPath)
		}
	}()

	select {
	case <-ctx.Done():
		err = ctx.Err()
		l.Infof("got a done signal while installing the app, udid: %s, path: %s", udid, l.appPath)
	case err = <-resultCh:
	}

	return err
}

func (l *ExecuteStabilityCaseTaskLogic) removeAppLogs() error {
	if l.device == nil || l.businessLogic == nil {
		return nil
	} else if _, ok := l.businessLogic.(*registry.NoopBusinessLogic); ok {
		l.logger.Infof("no need to clear app logs by noop business logic, app_name: %s", l.appName)
		return nil
	}

	return l.businessLogic.ClearLogs()
}

func (l *ExecuteStabilityCaseTaskLogic) getAppLogs() error {
	if l.device == nil || l.businessLogic == nil {
		return nil
	} else if _, ok := l.businessLogic.(*registry.NoopBusinessLogic); ok {
		l.logger.Infof("no need to get app logs by noop business logic, app_name: %s", l.appName)
		return nil
	}

	appLogsPath := filepath.Join(l.reportPath, filenameOfAppLogs)
	if err := l.businessLogic.GetLogs(appLogsPath); err != nil {
		return err
	}

	l.result.Artifacts = append(
		l.result.Artifacts, device.Artifact{
			Type:        commonpb.ArtifactType_ArtifactType_LOG,
			FileName:    filenameOfAppLogs,
			FilePath:    appLogsPath,
			Description: descriptionOfAppLogs,
		},
	)
	return nil
}

func (l *ExecuteStabilityCaseTaskLogic) executeBusinessLogic(ctx context.Context) error {
	if l.businessLogic == nil {
		l.logger.Infof("no business logic found for the app, app_name: %s", l.appName)
		return nil
	} else if _, ok := l.businessLogic.(*registry.NoopBusinessLogic); ok {
		l.logger.Infof("no need to execute business logic by noop business logic, app_name: %s", l.appName)
		return nil
	} else if len(l.accounts) == 0 {
		return errors.Errorf(
			"no accounts were acquired, pool_env_table: %s", l.taskInfo.GetAccountConfig().GetPoolEnvTable(),
		)
	}

	var (
		account_ = l.accounts[0] // 取第一个
		username = account_.Username
		password = account_.Password
	)

	if err := l.businessLogic.Login(ctx, username, password); err != nil {
		return err
	}
	l.logger.Infof("finish to login the app, app_name: %s", l.appName)

	return nil
}

func (l *ExecuteStabilityCaseTaskLogic) runStabilityTest(ctx context.Context) error {
	var (
		platformType = l.taskInfo.GetPlatformType()
		duration     = l.taskInfo.GetDuration()

		err error
	)

	threading.GoSafeCtx(
		ctx, func() {
			if err := l.device.CollectPerfData(
				ctx, l.appName, collector.WithCallback(l.sendPerfDataToReporter),
			); err != nil {
				l.logger.Errorf(
					"failed to collect the perf data, serial: %s, package_name: %s, error: %+v",
					l.device.UDID(), l.appName, err,
				)
			}
		},
	)

	switch platformType {
	case commonpb.PlatformType_ANDROID, commonpb.PlatformType_HarmonyOS:
		l.result, err = l.device.MonkeyTest(ctx, l.key, l.appName, int64(duration), l.reportPath)
		if err != nil {
			return err
		}

		l.logger.Infof("finish to run stability test, result: %s", jsonx.MarshalIgnoreError(l.result))
	default:
		return errors.Errorf("unsupported platform type: %s", platformType.String())
	}

	return nil
}

func (l *ExecuteStabilityCaseTaskLogic) sendTaskStatusToReporter() error {
	ctx, cancel := commonutils.NewTimeoutContext(l.ctx, common.ConstTimeoutOfInvokeRPC)
	defer cancel()

	var (
		taskID        = l.taskInfo.GetTaskId()
		executeID     = l.taskInfo.GetExecuteId()
		projectID     = l.taskInfo.GetProjectId()
		planExecuteID = l.taskInfo.GetPlanExecuteId()
		udid          = l.taskInfo.GetUdid()
		executedBy    = l.taskInfo.GetExecutedBy()

		req        *reporterpb.PutStabilityDeviceRecordReq
		deviceInfo string
	)

	switch l.state {
	case dispatcherpb.ComponentState_Pending, dispatcherpb.ComponentState_Init, dispatcherpb.ComponentState_Started:
		req = &reporterpb.PutStabilityDeviceRecordReq{
			TaskId:        taskID,
			ExecuteId:     executeID,
			ProjectId:     projectID,
			PlanExecuteId: planExecuteID,
			Udid:          udid,
			Status:        l.state.String(),
			ExecutedBy:    executedBy,
			StartedAt:     timestamppb.New(time.Now()),
		}
	default:
		if len(l.devices) == 0 {
			if l.state != dispatcherpb.ComponentState_Stop && l.state != dispatcherpb.ComponentState_Panic {
				l.state = dispatcherpb.ComponentState_Skip
			}
		} else {
			udid = l.devices[0].GetUdid()
			deviceInfo = protobuf.MarshalJSONToStringIgnoreError(l.devices[0])
		}

		req = &reporterpb.PutStabilityDeviceRecordReq{
			TaskId:        taskID,
			ExecuteId:     executeID,
			ProjectId:     projectID,
			PlanExecuteId: planExecuteID,
			Udid:          udid,
			Device:        deviceInfo,
			Status:        l.state.String(),
			CrashCount:    uint32(l.result.CrashCount),
			AnrCount:      uint32(l.result.ANRCount),
			Result:        ConvertToStabilityResult(l.result),
			ExecutedBy:    executedBy,
			EndedAt:       timestamppb.New(time.Now()),
		}
	}

	if _, err := l.svcCtx.ReporterRPC.ModifyStabilityDeviceRecord(ctx, req); err != nil {
		l.logger.Errorf(
			"failed to modify stability device record, task_id: %s, execute_id: %s, udid: %s, error: %+v",
			taskID, executeID, udid, err,
		)

		l.logger.Infof(
			"try to modify stability device record by mq, task_id: %s, execute_id: %s, udid: %s", taskID, executeID,
			udid,
		)
		ctx2, cancel2 := commonutils.NewTimeoutContext(l.ctx, common.ConstTimeoutOfSendTask)
		defer cancel2()

		if _, err := l.svcCtx.ReporterProducer.Send(
			ctx2, base.NewTask(
				commonconsts.MQTaskTypeReporterModifyStabilityDeviceRecordTask,
				protobuf.MarshalJSONIgnoreError(req),
			), base.QueuePriorityDefault,
		); err != nil {
			l.logger.Errorf(
				"failed to modify stability device record by mq, task_id: %s, execute_id: %s, udid: %s, error: %+v",
				taskID, executeID, udid, err,
			)
			return err
		}
	}

	return nil
}

func (l *ExecuteStabilityCaseTaskLogic) sendPerfDataToReporter(
	dataType collector.DataType, interval time.Duration, points []collector.PointData,
) {
	var (
		taskID     = l.taskInfo.GetTaskId()
		executeID  = l.taskInfo.GetExecuteId()
		projectID  = l.taskInfo.GetProjectId()
		executedBy = l.taskInfo.GetExecutedBy()
	)

	fn := func(point collector.PointData) {
		ctx, cancel := context.WithTimeout(l.ctx, common.ConstTimeoutOfSendTask)
		defer cancel()

		payload := protobuf.MarshalJSONIgnoreError(
			&commonpb.SaveDevicePerfDataTaskInfo{
				TaskId:     taskID,
				ExecuteId:  executeID,
				ProjectId:  projectID,
				Udid:       l.device.UDID(),
				Usage:      commonpb.DeviceUsage_STABILITY_TESTING,
				DataType:   dataType.ConvertToPerfDataType(),
				Interval:   interval.Milliseconds(),
				Series:     string(point.Series),
				Unit:       string(point.Unit),
				X:          point.X,
				Y:          point.Y,
				ExecutedBy: executedBy,
			},
		)
		if _, err := l.svcCtx.ReporterProducer.Send(
			ctx, base.NewTask(
				commonconsts.MQTaskTypeReporterSaveDevicePerfDataTask,
				payload,
				base.WithRetentionOptions(time.Minute),
			), base.QueuePriorityDefault,
		); err != nil {
			l.logger.Errorf(
				"failed to send the perf data to reporter, task_id: %s, payload: %s, error: %+v",
				taskID, payload, err,
			)
		}
	}
	for _, point := range points {
		fn(point)
	}
}

func (l *ExecuteStabilityCaseTaskLogic) sendStepDataToReporter(data *commonpb.SaveDeviceStepTaskInfo) {
	ctx, cancel := commonutils.NewTimeoutContext(l.ctx, common.ConstTimeoutOfSendTask)
	defer cancel()

	task := base.NewTask(
		commonconsts.MQTaskTypeReporterSaveDeviceStepTask,
		protobuf.MarshalJSONIgnoreError(data),
		base.WithRetentionOptions(5*time.Minute),
	)
	if _, err := l.svcCtx.ReporterProducer.Send(ctx, task, base.QueuePriorityDefault); err != nil {
		l.Errorf(
			"failed to send the task to mq, typename: %s, payload: %s, error: %+v", task.Typename, task.Payload, err,
		)
	}
}

func (l *ExecuteStabilityCaseTaskLogic) setupSteps() *StageSteps {
	return &StageSteps{
		Stage: commonpb.TestStage_SETUP,
		Steps: []*Step{
			{
				Key: keyOfAcquireDevices,
				Desc: StepDesc{
					EN: descOfAcquireDevicesEN,
					ZH: descOfAcquireDevicesZH,
				},
				Func: func(ctx context.Context) error {
					if err := l.acquireDevice(ctx); err != nil {
						return err
					}

					return l.initDevice()
				},
			},
			{
				Key: keyOfCheckScreenState,
				Desc: StepDesc{
					EN: descOfCheckScreenStateEN,
					ZH: descOfCheckScreenStateZH,
				},
				Func: func(ctx context.Context) error {
					return l.wakeupAndUnlockDevice()
				},
			},
			{
				Key: keyOfCheckWifiState,
				Desc: StepDesc{
					EN: descOfCheckWifiStateEN,
					ZH: descOfCheckWifiStateZH,
				},
				Func: func(ctx context.Context) error {
					return l.enableWifi()
				},
			},
			{
				Key: keyOfDownloadAppPackage,
				Desc: StepDesc{
					EN: descOfDownloadAppPackageEN,
					ZH: descOfDownloadAppPackageZH,
				},
				Func: func(ctx context.Context) error {
					// 单点下载
					if _, err := l.svcCtx.SingleFlight.Do(
						ctx,
						fmt.Sprintf("%s:%s", keyOfDownloadAppPackage, l.key),
						func() (any, error) {
							if err := l.downloadAppPackage(); err != nil {
								return commonconsts.FAILURE, err
							}
							return commonconsts.SUCCESS, nil
						},
						singleflight.WithWaitTimeout(common.ConstTimeoutOfDownloadAppPackage),
					); err != nil {
						return err
					}

					// 获取App信息
					info, err := l.getAppInfo()
					if err != nil {
						return err
					}
					l.appName = info.GetName()

					// 通过App名称创建对应的业务逻辑
					l.businessLogic, err = registry.NewLogic(l.ctx, l.appName, l.device)
					if err != nil {
						if errors.Is(err, registry.ErrUnSupportedProduct) {
							l.Infof("no business logic found for the app, app_name: %s", l.appName)
						} else {
							return err
						}
					} else if l.businessLogic == nil {
						return errors.Errorf("got an null business logic for the app, app_name: %s", l.appName)
					} else if _, ok := l.businessLogic.(*registry.NoopBusinessLogic); ok {
						l.Infof("found a noop business logic for the app, app_name: %s", l.appName)
					}
					return nil
				},
			},
			{
				Key: keyOfAcquireAccounts,
				Desc: StepDesc{
					EN: descOfAcquireAccountsEN,
					ZH: descOfAcquireAccountsZH,
				},
				Func: l.acquireAccount,
			},
			{
				Key: keyOfInstallApp,
				Desc: StepDesc{
					EN: descOfInstallAppEN,
					ZH: descOfInstallAppZH,
				},
				Func:               l.installApp,
				Timeout:            common.ConstTimeoutOfInstallApp,
				ScreenshotInterval: 2 * time.Second,
			},
			{
				Key: keyOfClearAppLogs,
				Desc: StepDesc{
					EN: descOfClearAppLogsEN,
					ZH: descOfClearAppLogsZH,
				},
				Func: func(ctx context.Context) error {
					if err := l.removeAppLogs(); err != nil {
						return err
					}

					// 删除应用日志后，确保应用被退出，后续重新启动应用时才会重新创建应用日志
					if l.appName != "" {
						_ = l.device.AppStop(l.appName)
					} else if l.businessLogic != nil {
						_ = l.businessLogic.Stop(ctx)
					}
					return nil
				},
			},
		},
	}
}

func (l *ExecuteStabilityCaseTaskLogic) testSteps() *StageSteps {
	return &StageSteps{
		Stage: commonpb.TestStage_TEST,
		Steps: []*Step{
			{
				Key: keyOfExecuteBusinessLogic,
				Desc: StepDesc{
					EN: descOfExecuteBusinessLogicEN,
					ZH: descOfExecuteBusinessLogicZH,
				},
				Func: func(ctx context.Context) error {
					if err := l.device.SetIME(fastInputMethod, true); err != nil {
						l.Errorf("failed to enable the input method, method: %s, error: %+v", fastInputMethod, err)
					}
					defer func() {
						if err := l.device.SetIME(fastInputMethod, false); err != nil {
							l.Errorf("failed to set the input method, method: %s, error: %+v", fastInputMethod, err)
						}
					}()

					if !registry.Has(l.appName) {
						l.Infof("no business logic found for the app, app_name: %s", l.appName)
						return nil
					}
					return l.executeBusinessLogic(ctx)
				},
				ScreenshotInterval: time.Second,
			},
			{
				Key: keyOfRunStabilityTest,
				Desc: StepDesc{
					EN: descOfRunStabilityTestEN,
					ZH: descOfRunStabilityTestZH,
				},
				Func: l.runStabilityTest,
				Timeout: func() time.Duration {
					duration := l.taskInfo.GetDuration()

					// 增加2分钟作为兜底超时时间
					return time.Duration(duration+2) * time.Minute
				}(),
			},
		},
	}
}

func (l *ExecuteStabilityCaseTaskLogic) teardownSteps() *StageSteps {
	return &StageSteps{
		Stage: commonpb.TestStage_TEARDOWN,
		Steps: []*Step{
			{
				Key: keyOfSaveAppLogs,
				Desc: StepDesc{
					EN: descOfSaveAppLogsEN,
					ZH: descOfSaveAppLogsZH,
				},
				Func: func(ctx context.Context) error {
					return l.getAppLogs()
				},
			},
			{
				Key: keyOfReleaseAccounts,
				Desc: StepDesc{
					EN: descOfReleaseAccountsEN,
					ZH: descOfReleaseAccountsZH,
				},
				Func:    l.releaseAccount,
				Timeout: accountcommon.ConstRPCReleaseAccountTimeout,
			},
			{
				Key: keyOfReleaseDevices,
				Desc: StepDesc{
					EN: descOfReleaseDevicesEN,
					ZH: descOfReleaseDevicesZH,
				},
				Func: l.releaseDevice,
			},
		},
	}
}

func (l *ExecuteStabilityCaseTaskLogic) runSteps(stageSteps *StageSteps) (err error) {
	var (
		taskID = l.taskInfo.GetTaskId()
		udid   = l.taskInfo.GetUdid()

		stage      = stageSteps.Stage
		strOfStage = protobuf.GetEnumStringOf(stage)
	)

	defer func() {
		if r := recover(); r != nil {
			if stage == commonpb.TestStage_TEARDOWN {
				l.Errorf(
					"got a panic while running the teardown steps, task_id: %s, udid: %s, error: %+v", taskID, udid, r,
				)
			} else {
				l.state = dispatcherpb.ComponentState_Panic
				err = errors.Errorf(
					"got a panic while running the %s steps, task_id: %s, udid: %s, error: %+v",
					strOfStage, taskID, udid, r,
				)
			}
		}
	}()

	if stage == commonpb.TestStage_TS_NULL {
		return errors.Errorf("invalid stage: %v", stage)
	} else if len(stageSteps.Steps) == 0 {
		return nil
	}

	if l.device != nil {
		udid = l.device.UDID()
	}

	l.Infof("start to run the %s steps, task_id: %s, udid: %s", strOfStage, taskID, udid)
	defer func() {
		l.Infof("finish running the %s steps, task_id: %s, udid: %s", strOfStage, taskID, udid)
	}()

	for i, step := range stageSteps.Steps {
		if step == nil || step.Func == nil {
			continue
		}

		if err = l.runStep(stage, i, step); err != nil {
			l.Errorf(
				"failed to run the %s step, task_id: %s, udid: %s, step: %q, error: %+v",
				strOfStage, taskID, udid, step.Desc.EN, err,
			)
			if stage != commonpb.TestStage_TEARDOWN {
				return err
			}
		}
	}

	return nil
}

func (l *ExecuteStabilityCaseTaskLogic) runStep(stage commonpb.TestStage, index int, step *Step) (err error) {
	// 参数验证
	if err = l.validateStepParams(stage, step); err != nil {
		return err
	}

	// 初始化步骤执行环境
	stepEnv := l.initializeStepEnvironment(stage, step, index)
	defer stepEnv.cancel()

	// 启动截图录制（如果需要）
	go func() {
		l.startScreenshotRecording(stepEnv)
	}()

	// 执行步骤函数
	go func() {
		l.executeStepFunction(stepEnv)
	}()

	// 等待步骤完成并处理结果
	return l.waitForStepCompletion(stepEnv)
}

// stepExecutionEnvironment 步骤执行环境
type stepExecutionEnvironment struct {
	stage commonpb.TestStage
	step  *Step
	index int

	startedAt time.Time
	status    dispatcherpb.ComponentState

	doneCh     chan lang.PlaceholderType
	artifactCh chan *commonpb.Artifact
	artifacts  []*commonpb.Artifact

	ctx    context.Context
	cancel context.CancelFunc
	err    error
}

// validateStepParams 验证步骤参数
func (l *ExecuteStabilityCaseTaskLogic) validateStepParams(stage commonpb.TestStage, step *Step) error {
	if stage == commonpb.TestStage_TS_NULL {
		return &errorOfInvalidStage{stage: stage}
	}
	if step == nil || step.Func == nil {
		return errorOfNullStep
	}
	return nil
}

// initializeStepEnvironment 初始化步骤执行环境
func (l *ExecuteStabilityCaseTaskLogic) initializeStepEnvironment(
	stage commonpb.TestStage, step *Step, index int,
) *stepExecutionEnvironment {
	env := &stepExecutionEnvironment{
		stage: stage,
		step:  step,
		index: index,

		startedAt: time.Now(),
		status:    dispatcherpb.ComponentState_Failure,

		doneCh:     make(chan lang.PlaceholderType),
		artifactCh: make(chan *commonpb.Artifact, 1),
		artifacts:  make([]*commonpb.Artifact, 0, commonconsts.ConstDefaultMakeSliceSize),
	}

	// 创建上下文
	if step.Timeout > 0 {
		if stage != commonpb.TestStage_TEARDOWN {
			env.ctx, env.cancel = context.WithTimeout(l.ctx, step.Timeout)
		} else {
			env.ctx, env.cancel = commonutils.NewTimeoutContext(l.ctx, step.Timeout)
		}
	} else {
		env.ctx, env.cancel = context.WithCancel(l.ctx)
	}

	return env
}

// startScreenshotRecording 启动截图录制
func (l *ExecuteStabilityCaseTaskLogic) startScreenshotRecording(env *stepExecutionEnvironment) {
	defer close(env.artifactCh)

	if env.step.ScreenshotInterval <= 0 {
		return
	}

	var (
		strOfStage = protobuf.GetEnumStringOf(env.stage)

		ch    <-chan device.Artifact
		count int
		e     error
	)

	defer func() {
		if r := recover(); r != nil {
			e = errors.New(fmt.Sprintf("%v", r))
		}

		if e != nil {
			l.logger.Errorf(
				"failed to take screenshots, stage: %q, step: %q, error: %+v", strOfStage, env.step.Desc.EN, e,
			)
		} else {
			l.logger.Infof(
				"finish to take screenshots, stage: %q, step: %q, count: %d", strOfStage, env.step.Desc.EN, count,
			)
		}
	}()

	l.logger.Infof("start to take screenshots, stage: %q, step: %q", strOfStage, env.step.Desc.EN)

	ch, e = l.device.ScreenRecording(
		env.ctx, l.key, env.step.Desc.ZH, env.step.ScreenshotInterval, l.reportPath,
	)
	if e != nil {
		return
	}
	if ch == nil {
		e = errorOfNullScreenshotChannel
		return
	}

	for {
		select {
		case <-env.ctx.Done():
			return
		case <-l.stopCh:
			return
		case v, ok := <-ch:
			if !ok {
				l.Infof("the screenshot channel has been closed, stage: %q, step: %q", strOfStage, env.step.Desc.EN)
				return
			}

			l.logger.Debugf(
				"collected screenshot artifact, stage: %q, step: %q, file: %s",
				strOfStage, env.step.Desc.EN, v.FileName,
			)

			env.artifactCh <- &commonpb.Artifact{
				Type:        v.Type,
				FileName:    v.FileName,
				FilePath:    v.FilePath,
				Description: v.Description,
			}
			count++
		}
	}
}

// executeStepFunction 执行步骤函数
func (l *ExecuteStabilityCaseTaskLogic) executeStepFunction(env *stepExecutionEnvironment) {
	strOfStage := protobuf.GetEnumStringOf(env.stage)

	defer func() {
		if r := recover(); r != nil {
			env.err = errors.New(fmt.Sprintf("%v", r))
		}

		if env.err != nil {
			l.logger.Errorf(
				"failed to run the test step, stage: %q, step: %q, error: %+v",
				strOfStage, env.step.Desc.EN, env.err,
			)
		} else {
			env.status = dispatcherpb.ComponentState_Success
			l.logger.Infof("succeed to run the test step, stage: %q, step: %q", strOfStage, env.step.Desc.EN)
		}

		close(env.doneCh)
	}()

	env.err = env.step.Func(env.ctx)
}

// waitForStepCompletion 等待步骤完成并处理结果
func (l *ExecuteStabilityCaseTaskLogic) waitForStepCompletion(env *stepExecutionEnvironment) error {
	strOfStage := protobuf.GetEnumStringOf(env.stage)

	defer func() {
		// 如果是后置步骤，这里不取消上下文，依赖后面的兜底时间尝试等待后置步骤执行完成
		if env.stage != commonpb.TestStage_TEARDOWN {
			env.cancel() // 取消步骤执行
		}

		// 尝试等待步骤执行结束
		timer := timewheel.NewTimer(5 * time.Second)
		defer timer.Stop()
		select {
		case <-env.doneCh:
		case <-timer.C: // 超时保护：如果5秒内协程还没退出，记录警告但不再等待
			l.logger.Warnf("wait for the step completed timeout, stage: %q, step: %q", strOfStage, env.step.Desc.EN)
		}

		// 保存步骤数据
		l.saveStepData(env)
	}()

	// 等待步骤完成或处理artifacts
	for {
		select {
		case <-l.ctx.Done(): // 任务完成
			l.logger.Infof(
				"got a done signal while running the test step, stage: %q, step: %q", strOfStage, env.step.Desc.EN,
			)
			return l.ctx.Err()
		case <-l.stopCh: // 任务终止
			l.logger.Infof(
				"got a stop signal while running the test step, stage: %q, step: %q", strOfStage, env.step.Desc.EN,
			)
			return errorOfStopSignal
		case <-env.doneCh: // 步骤完成
			return env.err
		case v, ok := <-env.artifactCh:
			if ok && v != nil {
				env.artifacts = append(env.artifacts, v)
			}
		}
	}
}

// saveStepData 保存步骤数据
func (l *ExecuteStabilityCaseTaskLogic) saveStepData(env *stepExecutionEnvironment) {
	udid := l.taskInfo.GetUdid()
	if l.device != nil {
		udid = l.device.UDID()
	}

	// 休眠2秒等待步骤日志写入
	time.Sleep(2 * time.Second)
	content := l.logger.Sync()

	l.sendStepDataToReporter(
		&commonpb.SaveDeviceStepTaskInfo{
			TaskId:     l.taskInfo.GetTaskId(),
			ExecuteId:  l.taskInfo.GetExecuteId(),
			ProjectId:  l.taskInfo.GetProjectId(),
			Udid:       udid,
			Usage:      commonpb.DeviceUsage_STABILITY_TESTING,
			Stage:      env.stage,
			Index:      int64(env.index),
			Name:       env.step.Desc.ZH,
			Status:     env.status.String(),
			Content:    content,
			Artifacts:  env.artifacts,
			ExecutedBy: l.taskInfo.GetExecutedBy(),
			StartedAt:  timestamppb.New(env.startedAt),
			EndedAt:    timestamppb.New(time.Now()),
		},
	)
}
