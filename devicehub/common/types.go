package common

type HuaweiCloudConfig struct {
	AccessKeyID     string                     // AK
	SecretAccessKey string                     // SK
	ServerID        string                     `json:",omitempty,optional"` // 云手机服务器唯一标识，注：当前只使用一台服务器上的云手机，其它服务器上的云手机用于其它用途，因此不能把全部云手机信息同步下来
	ReservePhones   []HuaweiCloudPhoneMetadata `json:",omitempty,optional"` // 预留的云手机列表，注：`TT`测试组同学需要预留部分云手机用于调试，因此需要排查预留的云手机，避免
	SyncStrategy    SyncStrategy               `json:",omitempty,optional"`
	RestartStrategy RestartStrategy            `json:",omitempty,optional"`
}

type HuaweiCloudPhoneMetadata struct {
	PhoneID   string `json:",omitempty" zh:"云手机的唯一标识，不超过32个字符"`
	PhoneName string `json:",omitempty" zh:"云手机的名称，不超过65个字符"`
}

type ScheduledStrategy struct {
	CronExpression string
}

type SyncStrategy struct {
	ScheduledStrategy
}

type RestartStrategy struct {
	ScheduledStrategy
}

// AlertConfig 通知配置
// `ChatIDOfLark`非空：通过平台机器人发送通知到指定的群组（前提：平台机器人已加入该群组）
// `WebhookOfLark`非空：通过飞书群聊机器人发送通知
type AlertConfig struct {
	ChatIDOfLark  string // 飞书群组ID
	WebhookOfLark string // 飞书群聊机器人的`Webhook`地址
}

type Device struct {
	UDID          string          `json:"udid" copier:"Udid" zh:"设备编号"`
	Name          string          `json:"name" zh:"设备名称"`
	Type          int8            `json:"type,default=1" zh:"设备类型（真机、云手机）"`
	Platform      string          `json:"platform,default=Android" zh:"平台（Android、iOS、HarmonyOS）"`
	Brand         string          `json:"brand,default=UNKNOWN" zh:"品牌"`
	Model         string          `json:"model,default=UNKNOWN" zh:"型号"`
	Version       string          `json:"version,default=UNKNOWN" zh:"版本"`
	Serial        string          `json:"serial,default=UNKNOWN" zh:"序列号"`
	Provider      string          `json:"provider" zh:"设备所属"`
	ProviderType  int8            `json:"provider_type" zh:"设备所属类型（ATX、HUAWEICLOUD）"`
	RemoteAddress string          `json:"remote_address" zh:"远程连接地址"`
	State         string          `json:"state,default=IDLE" zh:"状态（空闲中、使用中、释放中、下线中、已预留）"`
	Metadata      *DeviceMetadata `json:"metadata" zh:"设备元数据"`
	Token         string          `json:"token" zh:"令牌"`
	StartedAt     int64           `json:"started_at" zh:"开始时间"`
	Duration      int64           `json:"duration" zh:"使用时长"`
	// CreatedBy     *userinfo.FullUserInfo `json:"created_by"`
	// UpdatedBy     *userinfo.FullUserInfo `json:"updated_by"`
	CreatedAt int64 `json:"created_at"`
	UpdatedAt int64 `json:"updated_at"`
}

type DeviceMetadata struct {
	CPHAddress string `json:"cph_address" copier:"CphAddress" zh:"华为云手机内网监听地址"`
}
