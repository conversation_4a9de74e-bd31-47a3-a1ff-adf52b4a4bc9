package common

import (
	"fmt"
	"strings"

	commonpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
)

func ConvertToPlatformType(platform string) commonpb.PlatformType {
	switch strings.ToLower(platform) {
	case strings.ToLower(string(Android)):
		return commonpb.PlatformType_ANDROID
	case strings.ToLower(string(IOS)):
		return commonpb.PlatformType_IOS
	case strings.ToLower(string(HarmonyOS)):
		return commonpb.PlatformType_HarmonyOS
	default:
		return commonpb.PlatformType_PT_NULL
	}
}

func ConvertToDeviceTypeZH[T int64 | commonpb.DeviceType | DeviceType](t T) DeviceTypeZH {
	switch DeviceType(t) {
	case RealPhone:
		return RealPhoneZH
	case CloudPhone:
		return CloudPhoneZH
	default:
		return ""
	}
}

func ConvertToDevicePlatform[P int64 | commonpb.PlatformType | string](p P) DevicePlatform {
	var pt commonpb.PlatformType

	switch v := any(p).(type) {
	case string:
		switch strings.ToLower(v) {
		case strings.ToLower(string(Android)):
			return Android
		case strings.ToLower(string(IOS)):
			return IOS
		case strings.ToLower(string(HarmonyOS)):
			return HarmonyOS
		default:
			return ""
		}
	case int64:
		pt = commonpb.PlatformType(v)
	case commonpb.PlatformType:
		pt = v
	default:
		return ""
	}

	switch pt {
	case commonpb.PlatformType_ANDROID:
		return Android
	case commonpb.PlatformType_IOS:
		return IOS
	case commonpb.PlatformType_HarmonyOS:
		return HarmonyOS
	default:
		return ""
	}
}

func ConvertToDeviceStateZH[S DeviceState | string](s S) DeviceStateZH {
	switch v := any(s).(type) {
	case string:
		return ConvertToDeviceStateZH(DeviceState(v))
	case DeviceState:
		switch v {
		case Idle:
			return IdleZH
		case InUse:
			return InUseZH
		case Releasing:
			return ReleasingZH
		case Offline:
			return OfflineZH
		case Reserved:
			return ReservedZH
		default:
			return ""
		}
	default:
		return ""
	}
}

func GenerateLockKeyByUDID(udid string) string {
	return fmt.Sprintf("%s:%s", ConstLockDeviceUDIDPrefix, udid)
}

func GenerateLockKeyByProvider(provider string) string {
	return fmt.Sprintf("%s:%s", ConstLockDeviceProviderPrefix, provider)
}
