package notificationservice

import (
	"context"

	"github.com/zeromicro/go-zero/zrpc"
	"google.golang.org/grpc"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/interceptors/clientinterceptors"

	client "gitlab.ttyuyin.com/TestDevelopment/qet-backend-middleware/notifier/rpc/client/notificationservice"
)

type NotifierRPC struct {
	conf zrpc.RpcClientConf
	name string

	client client.NotificationService
}

func NewNotifierRPC(c zrpc.RpcClientConf) *NotifierRPC {
	return &NotifierRPC{
		conf:   c,
		name:   c.Etcd.Key,
		client: client.NewNotificationService(zrpc.MustNewClient(c, clientinterceptors.UnaryUserInfoClientOption())),
	}
}

func (c *NotifierRPC) Notify(ctx context.Context, req *client.NotifyReq, opts ...grpc.CallOption) (
	*client.NotifyResp, error,
) {
	return c.client.Notify(ctx, req, opts...)
}
