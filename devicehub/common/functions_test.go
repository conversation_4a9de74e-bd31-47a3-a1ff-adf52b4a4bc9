package common_test

import (
	"testing"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/devicehub/common"
)

type (
	IDeviceState interface {
		common.DeviceState | string
	}

	deviceStateArgs[S IDeviceState] struct {
		s S
	}

	deviceStateTestCase[S IDeviceState] struct {
		name string
		args deviceStateArgs[S]
		want common.DeviceStateZH
	}
)

func runConvertToDeviceStateZHTests[S IDeviceState](t *testing.T, tests []deviceStateTestCase[S]) {
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				if got := common.ConvertToDeviceStateZH(tt.args.s); got != tt.want {
					t.<PERSON>rrorf("ConvertToDeviceStateZH() = %v, want %v", got, tt.want)
				}
			},
		)
	}
}

func TestConvertToDeviceStateZH(t *testing.T) {
	//testsForInt64 := []deviceStateTestCase[int64]{
	//	{
	//		name: "int64 - 1",
	//		args: deviceStateArgs[int64]{
	//			s: 1,
	//		},
	//		want: common.IdleZH,
	//	},
	//	{
	//		name: "int64 - 2",
	//		args: deviceStateArgs[int64]{
	//			s: 2,
	//		},
	//		want: common.InUseZH,
	//	},
	//}
	//testsForPBDeviceState := []deviceStateTestCase[pb.DeviceState]{
	//	{
	//		name: "pb.DeviceState_IDLE",
	//		args: deviceStateArgs[pb.DeviceState]{
	//			s: pb.DeviceState_IDLE,
	//		},
	//		want: common.IdleZH,
	//	},
	//	{
	//		name: "pb.DeviceState_RELEASING",
	//		args: deviceStateArgs[pb.DeviceState]{
	//			s: pb.DeviceState_RELEASING,
	//		},
	//		want: common.ReleasingZH,
	//	},
	//}
	testsForDeviceState := []deviceStateTestCase[common.DeviceState]{
		{
			name: "common.Idle",
			args: deviceStateArgs[common.DeviceState]{
				s: common.Idle,
			},
			want: common.IdleZH,
		},
		{
			name: "common.Offline",
			args: deviceStateArgs[common.DeviceState]{
				s: common.Offline,
			},
			want: common.OfflineZH,
		},
		{
			name: "common.Reserved",
			args: deviceStateArgs[common.DeviceState]{
				s: common.Reserved,
			},
			want: common.ReservedZH,
		},
	}
	testsForString := []deviceStateTestCase[string]{
		{
			name: "string - IN_USE",
			args: deviceStateArgs[string]{
				s: "IN_USE",
			},
			want: common.InUseZH,
		},
		{
			name: "string - OFFLINE",
			args: deviceStateArgs[string]{
				s: "OFFLINE",
			},
			want: common.OfflineZH,
		},
	}

	// runConvertToDeviceStateZHTests(t, testsForInt64)
	// runConvertToDeviceStateZHTests(t, testsForPBDeviceState)
	runConvertToDeviceStateZHTests(t, testsForDeviceState)
	runConvertToDeviceStateZHTests(t, testsForString)
}
