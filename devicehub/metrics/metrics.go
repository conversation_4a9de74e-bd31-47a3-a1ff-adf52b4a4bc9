package metrics

import (
	"sync"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/metrics"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/devicehub/common"
)

const (
	constMetricNameDeviceState     = "device_state"
	constMetricHelpDeviceState     = "tracks the state of the device (e.g., IDLE, IN_USE, RELEASING, OFFLINE, RESERVED)."
	constMetricLabelDeviceUDID     = "device_udid"
	constMetricLabelDeviceName     = "device_name"
	constMetricLabelDeviceType     = "device_type"
	constMetricLabelDevicePlatform = "device_platform"
	constMetricLabelDeviceState    = "device_state"
)

var (
	initOnce sync.Once

	deviceStateGauge metrics.MetricGaugeHandler
)

func InitMetrics() {
	initOnce.Do(
		func() {
			gaugeOpts := metrics.MetricGaugeVecOpts
			gaugeOpts.Name = constMetricNameDeviceState
			gaugeOpts.Help = constMetricHelpDeviceState
			gaugeOpts.Labels = []string{
				constMetricLabelDeviceUDID,
				constMetricLabelDeviceName,
				constMetricLabelDeviceType,
				constMetricLabelDevicePlatform,
				constMetricLabelDeviceState,
			}
			deviceStateGauge = metrics.NewMetricGauge(&gaugeOpts)
		},
	)
}

func SetDeviceState(
	udid, name string, typ common.DeviceTypeZH, platform common.DevicePlatform, state common.DeviceState,
) {
	for _, s := range common.DeviceStates {
		if state == s {
			deviceStateGauge.Set(1, udid, name, string(typ), string(platform), string(state))
		} else {
			deviceStateGauge.Set(0, udid, name, string(typ), string(platform), string(s))
		}
	}
}
