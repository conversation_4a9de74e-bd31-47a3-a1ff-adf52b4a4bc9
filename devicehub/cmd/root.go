package cmd

import (
	"github.com/spf13/cobra"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/command"
)

const (
	rootCmdUse   = "devicehub"
	rootCmdShort = "DeviceHub is one of the microservices of the Quality Platform"
	rootCmdLong  = `DeviceHub is one of the microservices of the Quality Platform. 
The main function is the management of the mobile devices, such as Android mobile, iPhone mobile...`
)

func NewRootCommand() *cobra.Command {
	return command.NewRootCommand(rootCmdUse, rootCmdShort, rootCmdLong)
}
