// Code generated by goctl. DO NOT EDIT.
// versions:
//  goctl version: 1.8.3

package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlc"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
)

var (
	alertTableName           = "`alert`"
	alertFieldNames          = builder.RawFieldNames(&Alert{})
	alertRows                = strings.Join(alertFieldNames, ",")
	alertRowsExpectAutoSet   = strings.Join(stringx.Remove(alertFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), ",")
	alertRowsWithPlaceHolder = strings.Join(stringx.Remove(alertFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), "=?,") + "=?"

	cacheDevicehubAlertIdPrefix      = "cache:devicehub:alert:id:"
	cacheDevicehubAlertAlertIdPrefix = "cache:devicehub:alert:alertId:"
)

type (
	alertModel interface {
		Insert(ctx context.Context, session sqlx.Session, data *Alert) (sql.Result, error)
		FindOne(ctx context.Context, id int64) (*Alert, error)
		FindOneByAlertId(ctx context.Context, alertId string) (*Alert, error)
		Update(ctx context.Context, session sqlx.Session, data *Alert) (sql.Result, error)
		Delete(ctx context.Context, session sqlx.Session, id int64) error
		LogicDelete(ctx context.Context, session sqlx.Session, id int64) error
	}

	defaultAlertModel struct {
		sqlc.CachedConn
		table string
	}

	Alert struct {
		Id        int64          `db:"id"`         // 自增ID
		AlertId   string         `db:"alert_id"`   // 告警ID
		RuleId    string         `db:"rule_id"`    // 规则ID
		Udid      string         `db:"udid"`       // 设备编号
		Times     int64          `db:"times"`      // 已告警的次数
		StartedAt time.Time      `db:"started_at"` // 开始时间
		Deleted   int64          `db:"deleted"`    // 逻辑删除标识（未删除、已删除）
		CreatedBy string         `db:"created_by"` // 创建者的用户ID
		UpdatedBy string         `db:"updated_by"` // 最近一次更新者的用户ID
		DeletedBy sql.NullString `db:"deleted_by"` // 删除者的用户ID
		CreatedAt time.Time      `db:"created_at"` // 创建时间
		UpdatedAt time.Time      `db:"updated_at"` // 更新时间
		DeletedAt sql.NullTime   `db:"deleted_at"` // 删除时间
	}
)

func newAlertModel(conn sqlx.SqlConn, c cache.CacheConf, opts ...cache.Option) *defaultAlertModel {
	return &defaultAlertModel{
		CachedConn: sqlc.NewConn(conn, c, opts...),
		table:      "`alert`",
	}
}

func (m *defaultAlertModel) Delete(ctx context.Context, session sqlx.Session, id int64) error {
	data, err := m.FindOne(ctx, id)
	if err != nil {
		return err
	}

	devicehubAlertAlertIdKey := fmt.Sprintf("%s%v", cacheDevicehubAlertAlertIdPrefix, data.AlertId)
	devicehubAlertIdKey := fmt.Sprintf("%s%v", cacheDevicehubAlertIdPrefix, id)
	_, err = m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("delete from %s where `id` = ?", m.table)
		if session != nil {
			return session.ExecCtx(ctx, query, id)
		}
		return conn.ExecCtx(ctx, query, id)
	}, devicehubAlertAlertIdKey, devicehubAlertIdKey)
	return err
}

func (m *defaultAlertModel) LogicDelete(ctx context.Context, session sqlx.Session, id int64) error {
	data, err := m.FindOne(ctx, id)
	if err != nil {
		return err
	}

	devicehubAlertAlertIdKey := fmt.Sprintf("%s%v", cacheDevicehubAlertAlertIdPrefix, data.AlertId)
	devicehubAlertIdKey := fmt.Sprintf("%s%v", cacheDevicehubAlertIdPrefix, id)
	_, err = m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set `deleted` = ?, `deleted_at` = ? where `id` = ?", m.table)
		if session != nil {
			return session.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
		}
		return conn.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
	}, devicehubAlertAlertIdKey, devicehubAlertIdKey)
	return err
}

func (m *defaultAlertModel) FindOne(ctx context.Context, id int64) (*Alert, error) {
	devicehubAlertIdKey := fmt.Sprintf("%s%v", cacheDevicehubAlertIdPrefix, id)
	var resp Alert
	err := m.QueryRowCtx(ctx, &resp, devicehubAlertIdKey, func(ctx context.Context, conn sqlx.SqlConn, v any) error {
		query := fmt.Sprintf("select %s from %s where `id` = ? and `deleted` = ? limit 1", alertRows, m.table)
		return conn.QueryRowCtx(ctx, v, query, id, constants.NotDeleted)
	})
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultAlertModel) FindOneByAlertId(ctx context.Context, alertId string) (*Alert, error) {
	devicehubAlertAlertIdKey := fmt.Sprintf("%s%v", cacheDevicehubAlertAlertIdPrefix, alertId)
	var resp Alert
	err := m.QueryRowIndexCtx(ctx, &resp, devicehubAlertAlertIdKey, m.formatPrimary, func(ctx context.Context, conn sqlx.SqlConn, v any) (i any, e error) {
		query := fmt.Sprintf("select %s from %s where `alert_id` = ? and `deleted` = ? limit 1", alertRows, m.table)
		if err := conn.QueryRowCtx(ctx, &resp, query, alertId, constants.NotDeleted); err != nil {
			return nil, err
		}
		return resp.Id, nil
	}, m.queryPrimary)
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultAlertModel) Insert(ctx context.Context, session sqlx.Session, data *Alert) (sql.Result, error) {
	devicehubAlertAlertIdKey := fmt.Sprintf("%s%v", cacheDevicehubAlertAlertIdPrefix, data.AlertId)
	devicehubAlertIdKey := fmt.Sprintf("%s%v", cacheDevicehubAlertIdPrefix, data.Id)

	return m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?, ?, ?, ?, ?, ?)", m.table, alertRowsExpectAutoSet)
		if session != nil {
			return session.ExecCtx(ctx, query, data.AlertId, data.RuleId, data.Udid, data.Times, data.StartedAt, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy)
		}
		return conn.ExecCtx(ctx, query, data.AlertId, data.RuleId, data.Udid, data.Times, data.StartedAt, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy)
	}, devicehubAlertAlertIdKey, devicehubAlertIdKey)
}

func (m *defaultAlertModel) Update(ctx context.Context, session sqlx.Session, newData *Alert) (sql.Result, error) {
	data, err := m.FindOne(ctx, newData.Id)
	if err != nil {
		return nil, err
	}

	devicehubAlertAlertIdKey := fmt.Sprintf("%s%v", cacheDevicehubAlertAlertIdPrefix, data.AlertId)
	devicehubAlertIdKey := fmt.Sprintf("%s%v", cacheDevicehubAlertIdPrefix, data.Id)

	return m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, alertRowsWithPlaceHolder)
		if session != nil {
			return session.ExecCtx(ctx, query, newData.AlertId, newData.RuleId, newData.Udid, newData.Times, newData.StartedAt, newData.Deleted, newData.CreatedBy, newData.UpdatedBy, newData.DeletedBy, newData.Id)
		}
		return conn.ExecCtx(ctx, query, newData.AlertId, newData.RuleId, newData.Udid, newData.Times, newData.StartedAt, newData.Deleted, newData.CreatedBy, newData.UpdatedBy, newData.DeletedBy, newData.Id)
	}, devicehubAlertAlertIdKey, devicehubAlertIdKey)
}

func (m *defaultAlertModel) formatPrimary(primary any) string {
	return fmt.Sprintf("%s%v", cacheDevicehubAlertIdPrefix, primary)
}

func (m *defaultAlertModel) queryPrimary(ctx context.Context, conn sqlx.SqlConn, v, primary any) error {
	query := fmt.Sprintf("select %s from %s where `id` = ? limit 1", alertRows, m.table)
	return conn.QueryRowCtx(ctx, v, query, primary)
}

func (m *defaultAlertModel) tableName() string {
	return m.table
}
