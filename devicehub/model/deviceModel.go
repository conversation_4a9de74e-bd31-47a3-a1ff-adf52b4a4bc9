package model

import (
	"context"
	"database/sql"
	"fmt"

	"github.com/Masterminds/squirrel"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/types"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/devicehub/common"
)

var (
	_ DeviceModel = (*customDeviceModel)(nil)

	deviceInsertFields = stringx.Remove(
		deviceFieldNames, "`id`", "`create_time`", "`update_time`", "`deleted_by`", "`created_at`", "`updated_at`",
		"`deleted_at`",
	)
)

type (
	// DeviceModel is an interface to be customized, add more methods here,
	// and implement the added methods in customDeviceModel.
	DeviceModel interface {
		deviceModel
		types.DBModel

		Trans(ctx context.Context, fn func(context context.Context, session sqlx.Session) error) error
		InsertBuilder(data *Device) squirrel.InsertBuilder
		UpdateBuilder(data *Device) squirrel.UpdateBuilder
		SelectBuilder() squirrel.SelectBuilder
		SelectCountBuilder() squirrel.SelectBuilder
		FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error)
		FindNoCacheByQuery(ctx context.Context, selectBuilder squirrel.SelectBuilder) ([]*Device, error)

		InsertTX(ctx context.Context, session sqlx.Session, data *Device) (sql.Result, error)
		UpdateTX(ctx context.Context, session sqlx.Session, data *Device) (sql.Result, error)
		DeleteTX(ctx context.Context, session sqlx.Session, id int64) error
		FindByProvider(ctx context.Context, provider string, providerType int64) ([]*Device, error)
		UpdateToOfflineByProvider(
			ctx context.Context, session sqlx.Session, provider string, providerType int64,
		) (sql.Result, error)
		FindIdleDevices(ctx context.Context) ([]*Device, error)
		FindAll(ctx context.Context) ([]*Device, error)
		UpdateAllToOffline(ctx context.Context, session sqlx.Session) (sql.Result, error)
	}

	customDeviceModel struct {
		*defaultDeviceModel
	}
)

// NewDeviceModel returns a model for the database table.
func NewDeviceModel(conn sqlx.SqlConn, c cache.CacheConf, opts ...cache.Option) DeviceModel {
	return &customDeviceModel{
		defaultDeviceModel: newDeviceModel(conn, c, opts...),
	}
}

func (m *customDeviceModel) Table() string {
	return m.table
}

func (m *customDeviceModel) Fields() []string {
	return deviceFieldNames
}

func (m *customDeviceModel) Trans(
	ctx context.Context, fn func(context context.Context, session sqlx.Session) error,
) error {
	return m.TransactCtx(ctx, fn)
}

func (m *customDeviceModel) InsertBuilder(data *Device) squirrel.InsertBuilder {
	return squirrel.Insert(m.table).Columns(deviceInsertFields...).Values(
		data.Udid, data.Name, data.Type, data.Platform, data.Brand, data.Model, data.Serial, data.Version,
		data.Provider, data.ProviderType, data.RemoteAddress, data.PreviousState, data.State, data.Metadata, data.Token,
		data.StartedAt, data.Expiration, data.Deleted, data.CreatedBy, data.UpdatedBy,
	)
}

func (m *customDeviceModel) UpdateBuilder(data *Device) squirrel.UpdateBuilder {
	eq := squirrel.Eq{
		"`name`":           data.Name,
		"`version`":        data.Version,
		"`provider`":       data.Provider,
		"`provider_type`":  data.ProviderType,
		"`remote_address`": data.RemoteAddress,
		"`previous_state`": data.PreviousState,
		"`state`":          data.State,
		"`metadata`":       data.Metadata,
		"`token`":          data.Token,
		"`started_at`":     data.StartedAt,
		"`expiration`":     data.Expiration,
		"`deleted`":        data.Deleted,
		"`updated_by`":     data.UpdatedBy,
		"`deleted_by`":     data.DeletedBy,
		"`deleted_at`":     data.DeletedAt,
	}
	return squirrel.Update(m.table).SetMap(eq).Where("`id` = ?", data.Id)
}

func (m *customDeviceModel) SelectBuilder() squirrel.SelectBuilder {
	return squirrel.Select(deviceFieldNames...).Where("`deleted` = ?", constants.NotDeleted).From(m.table)
}

func (m *customDeviceModel) SelectCountBuilder() squirrel.SelectBuilder {
	return squirrel.Select("count(*)").Where("`deleted` = ?", constants.NotDeleted).From(m.table)
}

func (m *customDeviceModel) FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error) {
	query, values, err := countBuilder.ToSql()
	if err != nil {
		return 0, err
	}

	var resp int64
	err = m.QueryRowNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return 0, err
	}
}

func (m *customDeviceModel) FindNoCacheByQuery(ctx context.Context, selectBuilder squirrel.SelectBuilder) (
	[]*Device, error,
) {
	query, values, err := selectBuilder.ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*Device
	err = m.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

func (m *customDeviceModel) InsertTX(ctx context.Context, session sqlx.Session, data *Device) (sql.Result, error) {
	devicehubDeviceIdKey := fmt.Sprintf("%s%v", cacheDevicehubDeviceIdPrefix, data.Id)
	devicehubDeviceUdidKey := fmt.Sprintf("%s%v", cacheDevicehubDeviceUdidPrefix, data.Udid)

	return m.ExecCtx(
		ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
			query, values, err := m.InsertBuilder(data).ToSql()
			if err != nil {
				return nil, err
			}
			if session != nil {
				return session.ExecCtx(ctx, query, values...)
			}
			return conn.ExecCtx(ctx, query, values...)
		}, devicehubDeviceIdKey, devicehubDeviceUdidKey,
	)
}

func (m *customDeviceModel) UpdateTX(ctx context.Context, session sqlx.Session, newData *Device) (sql.Result, error) {
	data, err := m.FindOne(ctx, newData.Id)
	if err != nil {
		return nil, err
	}

	devicehubDeviceIdKey := fmt.Sprintf("%s%v", cacheDevicehubDeviceIdPrefix, data.Id)
	devicehubDeviceUdidKey := fmt.Sprintf("%s%v", cacheDevicehubDeviceUdidPrefix, data.Udid)

	return m.ExecCtx(
		ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
			query, values, err := m.UpdateBuilder(newData).ToSql()
			if err != nil {
				return nil, err
			}
			if session != nil {
				return session.ExecCtx(ctx, query, values...)
			}
			return conn.ExecCtx(ctx, query, values...)
		}, devicehubDeviceIdKey, devicehubDeviceUdidKey,
	)
}

func (m *customDeviceModel) DeleteTX(ctx context.Context, session sqlx.Session, id int64) error {
	data, err := m.FindOne(ctx, id)
	if err != nil {
		return err
	}

	devicehubDeviceIdKey := fmt.Sprintf("%s%v", cacheDevicehubDeviceIdPrefix, id)
	devicehubDeviceUdidKey := fmt.Sprintf("%s%v", cacheDevicehubDeviceUdidPrefix, data.Udid)

	_, err = m.ExecCtx(
		ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
			stmt, values, err := squirrel.Delete(m.table).
				Where("`id` = ?", id).
				ToSql()
			if err != nil {
				return nil, err
			}

			if session != nil {
				return session.ExecCtx(ctx, stmt, values...)
			}
			return conn.ExecCtx(ctx, stmt, values...)
		}, devicehubDeviceIdKey, devicehubDeviceUdidKey,
	)

	return err
}

func (m *customDeviceModel) FindByProvider(ctx context.Context, provider string, providerType int64) (
	[]*Device, error,
) {
	sb := m.SelectBuilder().Where("`provider` = ? AND `provider_type` = ?", provider, providerType)

	return m.FindNoCacheByQuery(ctx, sb)
}

func (m *customDeviceModel) UpdateToOfflineByProvider(
	ctx context.Context, session sqlx.Session, provider string, providerType int64,
) (sql.Result, error) {
	keys := m.getKeysByProvider(ctx, provider, providerType)

	return m.ExecCtx(
		ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
			query := fmt.Sprintf(
				"UPDATE %s SET `provider` = ?, `provider_type` = ?, `remote_address` = ?, `previous_state` = `state`, `state` = ?, `metadata` = ? WHERE `provider` = ? AND `provider_type` = ? AND `state` != ? AND `deleted` = ?",
				m.table,
			)
			values := []any{
				nil, nil, nil, common.Offline, nil, provider, providerType, common.Offline, constants.NotDeleted,
			}

			if session != nil {
				return session.ExecCtx(ctx, query, values...)
			}
			return conn.ExecCtx(ctx, query, values...)
		}, keys...,
	)
}

func (m *customDeviceModel) FindIdleDevices(ctx context.Context) ([]*Device, error) {
	sb := m.SelectBuilder().Where("`state` = ?", common.Idle)

	return m.FindNoCacheByQuery(ctx, sb)
}

func (m *customDeviceModel) FindAll(ctx context.Context) ([]*Device, error) {
	return m.FindNoCacheByQuery(ctx, m.SelectBuilder())
}

func (m *customDeviceModel) UpdateAllToOffline(ctx context.Context, session sqlx.Session) (sql.Result, error) {
	keys := m.getAllKeys(ctx)

	return m.ExecCtx(
		ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
			query := fmt.Sprintf(
				"UPDATE %s SET `provider` = ?, `provider_type` = ?, `remote_address` = ?, `previous_state` = `state`, `state` = ?, `metadata` = ?, `token` = ?, `started_at` = ?, `expiration` = ? WHERE `state` != ? AND `deleted` = ?",
				m.table,
			)
			values := []any{nil, nil, nil, common.Offline, nil, nil, nil, nil, common.Offline, constants.NotDeleted}

			if session != nil {
				return session.ExecCtx(ctx, query, values...)
			}
			return conn.ExecCtx(ctx, query, values...)
		}, keys...,
	)
}

func (m *customDeviceModel) getKeysByProvider(ctx context.Context, provider string, providerType int64) []string {
	data, err := m.FindByProvider(ctx, provider, providerType)
	if err != nil {
		return make([]string, 0)
	}

	keys := make([]string, 0, len(data)*2)
	for _, d := range data {
		keys = append(
			keys,
			fmt.Sprintf("%s%v", cacheDevicehubDeviceIdPrefix, d.Id),
			fmt.Sprintf("%s%v", cacheDevicehubDeviceUdidPrefix, d.Udid),
		)
	}

	return keys
}

func (m *customDeviceModel) getAllKeys(ctx context.Context) []string {
	data, err := m.FindAll(ctx)
	if err != nil {
		return make([]string, 0)
	}

	keys := make([]string, 0, len(data)*2)
	for _, d := range data {
		keys = append(
			keys,
			fmt.Sprintf("%s%v", cacheDevicehubDeviceIdPrefix, d.Id),
			fmt.Sprintf("%s%v", cacheDevicehubDeviceUdidPrefix, d.Udid),
		)
	}

	return keys
}
