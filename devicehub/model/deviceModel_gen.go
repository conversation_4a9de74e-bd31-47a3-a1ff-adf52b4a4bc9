// Code generated by goctl. DO NOT EDIT.
// versions:
//  goctl version: 1.8.3

package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlc"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
)

var (
	deviceTableName           = "`device`"
	deviceFieldNames          = builder.RawFieldNames(&Device{})
	deviceRows                = strings.Join(deviceFieldNames, ",")
	deviceRowsExpectAutoSet   = strings.Join(stringx.Remove(deviceFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), ",")
	deviceRowsWithPlaceHolder = strings.Join(stringx.Remove(deviceFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), "=?,") + "=?"

	cacheDevicehubDeviceIdPrefix   = "cache:devicehub:device:id:"
	cacheDevicehubDeviceUdidPrefix = "cache:devicehub:device:udid:"
)

type (
	deviceModel interface {
		Insert(ctx context.Context, session sqlx.Session, data *Device) (sql.Result, error)
		FindOne(ctx context.Context, id int64) (*Device, error)
		FindOneByUdid(ctx context.Context, udid string) (*Device, error)
		Update(ctx context.Context, session sqlx.Session, data *Device) (sql.Result, error)
		Delete(ctx context.Context, session sqlx.Session, id int64) error
		LogicDelete(ctx context.Context, session sqlx.Session, id int64) error
	}

	defaultDeviceModel struct {
		sqlc.CachedConn
		table string
	}

	Device struct {
		Id            int64          `db:"id"`             // 自增ID
		Udid          string         `db:"udid"`           // 设备编号
		Name          string         `db:"name"`           // 设备名称
		Type          int64          `db:"type"`           // 设备类型（真机、云手机）
		Platform      int64          `db:"platform"`       // 平台（Android、iOS、HarmonyOS）
		Brand         string         `db:"brand"`          // 品牌
		Model         string         `db:"model"`          // 型号
		Serial        string         `db:"serial"`         // 序列号
		Version       string         `db:"version"`        // 版本
		Provider      sql.NullString `db:"provider"`       // 设备所属
		ProviderType  sql.NullInt64  `db:"provider_type"`  // 设备所属类型（ATX、HUAWEICLOUD）
		RemoteAddress sql.NullString `db:"remote_address"` // 远程连接地址
		PreviousState sql.NullString `db:"previous_state"` // 前一个状态（空闲中、使用中、释放中、已下线、已预留）
		State         string         `db:"state"`          // 状态（空闲中、使用中、释放中、已下线、已预留）
		Metadata      sql.NullString `db:"metadata"`       // 设备元数据
		Token         sql.NullString `db:"token"`          // 令牌，用于释放设备时的验证
		StartedAt     sql.NullTime   `db:"started_at"`     // 开始时间
		Expiration    sql.NullInt64  `db:"expiration"`     // 最大占用时长（秒）
		Deleted       int64          `db:"deleted"`        // 逻辑删除标识（未删除、已删除）
		CreatedBy     string         `db:"created_by"`     // 创建者的用户ID
		UpdatedBy     string         `db:"updated_by"`     // 最近一次更新者的用户ID
		DeletedBy     sql.NullString `db:"deleted_by"`     // 删除者的用户ID
		CreatedAt     time.Time      `db:"created_at"`     // 创建时间
		UpdatedAt     time.Time      `db:"updated_at"`     // 更新时间
		DeletedAt     sql.NullTime   `db:"deleted_at"`     // 删除时间
	}
)

func newDeviceModel(conn sqlx.SqlConn, c cache.CacheConf, opts ...cache.Option) *defaultDeviceModel {
	return &defaultDeviceModel{
		CachedConn: sqlc.NewConn(conn, c, opts...),
		table:      "`device`",
	}
}

func (m *defaultDeviceModel) Delete(ctx context.Context, session sqlx.Session, id int64) error {
	data, err := m.FindOne(ctx, id)
	if err != nil {
		return err
	}

	devicehubDeviceIdKey := fmt.Sprintf("%s%v", cacheDevicehubDeviceIdPrefix, id)
	devicehubDeviceUdidKey := fmt.Sprintf("%s%v", cacheDevicehubDeviceUdidPrefix, data.Udid)
	_, err = m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("delete from %s where `id` = ?", m.table)
		if session != nil {
			return session.ExecCtx(ctx, query, id)
		}
		return conn.ExecCtx(ctx, query, id)
	}, devicehubDeviceIdKey, devicehubDeviceUdidKey)
	return err
}

func (m *defaultDeviceModel) LogicDelete(ctx context.Context, session sqlx.Session, id int64) error {
	data, err := m.FindOne(ctx, id)
	if err != nil {
		return err
	}

	devicehubDeviceIdKey := fmt.Sprintf("%s%v", cacheDevicehubDeviceIdPrefix, id)
	devicehubDeviceUdidKey := fmt.Sprintf("%s%v", cacheDevicehubDeviceUdidPrefix, data.Udid)
	_, err = m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set `deleted` = ?, `deleted_at` = ? where `id` = ?", m.table)
		if session != nil {
			return session.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
		}
		return conn.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
	}, devicehubDeviceIdKey, devicehubDeviceUdidKey)
	return err
}

func (m *defaultDeviceModel) FindOne(ctx context.Context, id int64) (*Device, error) {
	devicehubDeviceIdKey := fmt.Sprintf("%s%v", cacheDevicehubDeviceIdPrefix, id)
	var resp Device
	err := m.QueryRowCtx(ctx, &resp, devicehubDeviceIdKey, func(ctx context.Context, conn sqlx.SqlConn, v any) error {
		query := fmt.Sprintf("select %s from %s where `id` = ? and `deleted` = ? limit 1", deviceRows, m.table)
		return conn.QueryRowCtx(ctx, v, query, id, constants.NotDeleted)
	})
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultDeviceModel) FindOneByUdid(ctx context.Context, udid string) (*Device, error) {
	devicehubDeviceUdidKey := fmt.Sprintf("%s%v", cacheDevicehubDeviceUdidPrefix, udid)
	var resp Device
	err := m.QueryRowIndexCtx(ctx, &resp, devicehubDeviceUdidKey, m.formatPrimary, func(ctx context.Context, conn sqlx.SqlConn, v any) (i any, e error) {
		query := fmt.Sprintf("select %s from %s where `udid` = ? and `deleted` = ? limit 1", deviceRows, m.table)
		if err := conn.QueryRowCtx(ctx, &resp, query, udid, constants.NotDeleted); err != nil {
			return nil, err
		}
		return resp.Id, nil
	}, m.queryPrimary)
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultDeviceModel) Insert(ctx context.Context, session sqlx.Session, data *Device) (sql.Result, error) {
	devicehubDeviceIdKey := fmt.Sprintf("%s%v", cacheDevicehubDeviceIdPrefix, data.Id)
	devicehubDeviceUdidKey := fmt.Sprintf("%s%v", cacheDevicehubDeviceUdidPrefix, data.Udid)

	return m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", m.table, deviceRowsExpectAutoSet)
		if session != nil {
			return session.ExecCtx(ctx, query, data.Udid, data.Name, data.Type, data.Platform, data.Brand, data.Model, data.Serial, data.Version, data.Provider, data.ProviderType, data.RemoteAddress, data.PreviousState, data.State, data.Metadata, data.Token, data.StartedAt, data.Expiration, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy)
		}
		return conn.ExecCtx(ctx, query, data.Udid, data.Name, data.Type, data.Platform, data.Brand, data.Model, data.Serial, data.Version, data.Provider, data.ProviderType, data.RemoteAddress, data.PreviousState, data.State, data.Metadata, data.Token, data.StartedAt, data.Expiration, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy)
	}, devicehubDeviceIdKey, devicehubDeviceUdidKey)
}

func (m *defaultDeviceModel) Update(ctx context.Context, session sqlx.Session, newData *Device) (sql.Result, error) {
	data, err := m.FindOne(ctx, newData.Id)
	if err != nil {
		return nil, err
	}

	devicehubDeviceIdKey := fmt.Sprintf("%s%v", cacheDevicehubDeviceIdPrefix, data.Id)
	devicehubDeviceUdidKey := fmt.Sprintf("%s%v", cacheDevicehubDeviceUdidPrefix, data.Udid)

	return m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, deviceRowsWithPlaceHolder)
		if session != nil {
			return session.ExecCtx(ctx, query, newData.Udid, newData.Name, newData.Type, newData.Platform, newData.Brand, newData.Model, newData.Serial, newData.Version, newData.Provider, newData.ProviderType, newData.RemoteAddress, newData.PreviousState, newData.State, newData.Metadata, newData.Token, newData.StartedAt, newData.Expiration, newData.Deleted, newData.CreatedBy, newData.UpdatedBy, newData.DeletedBy, newData.Id)
		}
		return conn.ExecCtx(ctx, query, newData.Udid, newData.Name, newData.Type, newData.Platform, newData.Brand, newData.Model, newData.Serial, newData.Version, newData.Provider, newData.ProviderType, newData.RemoteAddress, newData.PreviousState, newData.State, newData.Metadata, newData.Token, newData.StartedAt, newData.Expiration, newData.Deleted, newData.CreatedBy, newData.UpdatedBy, newData.DeletedBy, newData.Id)
	}, devicehubDeviceIdKey, devicehubDeviceUdidKey)
}

func (m *defaultDeviceModel) formatPrimary(primary any) string {
	return fmt.Sprintf("%s%v", cacheDevicehubDeviceIdPrefix, primary)
}

func (m *defaultDeviceModel) queryPrimary(ctx context.Context, conn sqlx.SqlConn, v, primary any) error {
	query := fmt.Sprintf("select %s from %s where `id` = ? limit 1", deviceRows, m.table)
	return conn.QueryRowCtx(ctx, v, query, primary)
}

func (m *defaultDeviceModel) tableName() string {
	return m.table
}
