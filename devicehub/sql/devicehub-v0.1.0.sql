CREATE DATABASE IF NOT EXISTS `devicehub` DEFAULT CHARSET = utf8mb4 COLLATE utf8mb4_0900_ai_ci;

USE `devicehub`;

CREATE TABLE IF NOT EXISTS `device`
(
    `id`             INT(11)      NOT NULL AUTO_INCREMENT COMMENT '自增ID',
    `udid`           VARCHAR(64)  NOT NULL COMMENT '设备编号',
    `name`           VARCHAR(64)  NOT NULL COMMENT '设备名称',
    `type`           TINYINT(1)   NOT NULL DEFAULT 1 COMMENT '设备类型（真机、云手机）',
    `platform`       TINYINT(1)   NOT NULL DEFAULT 1 COMMENT '平台（Android、iOS、HarmonyOS）',
    `brand`          VARCHAR(64)  NOT NULL DEFAULT 'UNKNOWN' COMMENT '品牌',
    `model`          VARCHAR(64)  NOT NULL DEFAULT 'UNKNOWN' COMMENT '型号',
    `serial`         VARCHAR(64)  NOT NULL DEFAULT 'UNKNOWN' COMMENT '序列号',
    `version`        VARCHAR(64)  NOT NULL DEFAULT 'UNKNOWN' COMMENT '版本',
    `provider`       VARCHAR(128) NULL COMMENT '设备所属',
    `provider_type`  TINYINT(1)   NULL COMMENT '设备所属类型（ATX、HUAWEICLOUD）',
    `remote_address` VARCHAR(128) NULL COMMENT '远程连接地址',
    `state`          VARCHAR(16)  NOT NULL DEFAULT 'IDLE' COMMENT '状态（空闲中、使用中、释放中、已下线、已预留）',
    `token`          VARCHAR(64)  NULL COMMENT '令牌，用于释放设备时的验证',
    `started_at`     TIMESTAMP    NULL COMMENT '开始时间',
    `deleted`        TINYINT(1)   NOT NULL DEFAULT 0 COMMENT '逻辑删除标识（未删除、已删除）',
    `created_by`     VARCHAR(64)  NOT NULL COMMENT '创建者的用户ID',
    `updated_by`     VARCHAR(64)  NOT NULL COMMENT '最近一次更新者的用户ID',
    `deleted_by`     VARCHAR(64)  NULL COMMENT '删除者的用户ID',
    `created_at`     TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at`     TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at`     TIMESTAMP    NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uix_device_udid` (`udid`),
    KEY `ix_device_udid_deleted` (`udid`, `deleted`),
    KEY `ix_device_type_platform_deleted` (`type`, `platform`, `deleted`),
    KEY `ix_device_provider_provider_type` (`provider`, `provider_type`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT ='设备表';
