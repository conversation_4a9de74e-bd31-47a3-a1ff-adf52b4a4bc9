package svc

import (
	"time"

	"github.com/bufbuild/protovalidate-go"
	red "github.com/redis/go-redis/v9"
	"github.com/zeromicro/go-zero/core/stores/redis"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"google.golang.org/protobuf/reflect/protoreflect"
	"google.golang.org/protobuf/reflect/protoregistry"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/cronscheduler"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworkerv2/consumer"
	qetredis "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/redis"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/devicehub/common/zrpc/notifier/notificationservice"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/devicehub/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/devicehub/rpc/internal/config"
)

type ServiceContext struct {
	Config config.Config

	DB        sqlx.SqlConn
	Redis     *redis.Redis
	RedisNode red.UniversalClient
	Validator protovalidate.Validator
	Scheduler *cronscheduler.Scheduler
	CPHClient ICPHClient

	NotifierRPC *notificationservice.NotifierRPC

	DeviceHubConsumer *consumer.Consumer

	AlertModel  model.AlertModel
	DeviceModel model.DeviceModel
	RuleModel   model.RuleModel
}

func NewServiceContext(c config.Config) *ServiceContext {
	sqlConn := sqlx.NewMysql(c.DB.DataSource)
	scheduler := cronscheduler.NewScheduler()
	scheduler.Scheduler.ChangeLocation(time.Local) // change to local timezone

	return &ServiceContext{
		Config: c,

		// Middleware
		DB:        sqlConn,
		Redis:     redis.MustNewRedis(c.Redis.RedisConf, redis.WithDB(c.Redis.RedisConf.DB)),
		RedisNode: qetredis.NewClient(c.Redis.RedisConf),
		Validator: newProtoValidator(),
		Scheduler: scheduler,
		CPHClient: newCPHClient(c),

		// RPC
		NotifierRPC: notificationservice.NewNotifierRPC(c.Notifier),

		// MQ
		DeviceHubConsumer: consumer.NewConsumer(c.DeviceHubConsumer),

		// Model
		AlertModel:  model.NewAlertModel(sqlConn, c.Cache),
		DeviceModel: model.NewDeviceModel(sqlConn, c.Cache),
		RuleModel:   model.NewRuleModel(sqlConn, c.Cache),
	}
}

func newProtoValidator() protovalidate.Validator {
	mds := make([]protoreflect.MessageDescriptor, 0, protoregistry.GlobalTypes.NumMessages())
	protoregistry.GlobalTypes.RangeMessages(
		func(mt protoreflect.MessageType) bool {
			mds = append(mds, mt.Descriptor())
			return true
		},
	)

	v, _ := protovalidate.New(protovalidate.WithMessageDescriptors(mds...), protovalidate.WithDisableLazy())
	return v
}
