package svc

import (
	"github.com/huaweicloud/huaweicloud-sdk-go-v3/core/auth/basic"
	cph "github.com/huaweicloud/huaweicloud-sdk-go-v3/services/cph/v1"
	cphmodel "github.com/huaweicloud/huaweicloud-sdk-go-v3/services/cph/v1/model"
	"github.com/huaweicloud/huaweicloud-sdk-go-v3/services/cph/v1/region"
	"github.com/zeromicro/go-zero/core/logx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/devicehub/rpc/internal/config"
)

var (
	_ ICPHClient = (*cph.CphClient)(nil)
	_ ICPHClient = (*NoopCPHClient)(nil)
)

type ICPHClient interface {
	ListCloudPhones(*cphmodel.ListCloudPhonesRequest) (*cphmodel.ListCloudPhonesResponse, error)
	ShowCloudPhoneDetail(*cphmodel.ShowCloudPhoneDetailRequest) (*cphmodel.ShowCloudPhoneDetailResponse, error)
	RestartCloudPhone(*cphmodel.RestartCloudPhoneRequest) (*cphmodel.RestartCloudPhoneResponse, error)
	ShowJob(*cphmodel.ShowJobRequest) (*cphmodel.ShowJobResponse, error)
}

func newCPHClient(c config.Config) ICPHClient {
	hcc := c.HuaweiCloud
	if hcc.AccessKeyID == "" || hcc.SecretAccessKey == "" {
		logx.Infof(
			"new a noop cph client cause by AK or SK is empty, AK: %q, SK: %q", hcc.AccessKeyID, hcc.SecretAccessKey,
		)
		return NoopCPHClient{}
	}

	auth := basic.NewCredentialsBuilder().WithAk(hcc.AccessKeyID).WithSk(hcc.SecretAccessKey).Build()
	return cph.NewCphClient(cph.CphClientBuilder().WithCredential(auth).WithRegion(region.CN_NORTH_4).Build())
}

type NoopCPHClient struct{}

func (_ NoopCPHClient) ListCloudPhones(_ *cphmodel.ListCloudPhonesRequest) (*cphmodel.ListCloudPhonesResponse, error) {
	return nil, nil
}

func (_ NoopCPHClient) ShowCloudPhoneDetail(_ *cphmodel.ShowCloudPhoneDetailRequest) (
	*cphmodel.ShowCloudPhoneDetailResponse, error,
) {
	return nil, nil
}

func (_ NoopCPHClient) RestartCloudPhone(_ *cphmodel.RestartCloudPhoneRequest) (
	*cphmodel.RestartCloudPhoneResponse, error,
) {
	return nil, nil
}

func (_ NoopCPHClient) ShowJob(*cphmodel.ShowJobRequest) (*cphmodel.ShowJobResponse, error) {
	return nil, nil
}
