package config

import (
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/zrpc"

	consumerv2 "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworkerv2/consumer"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/types"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/devicehub/common"
)

type Config struct {
	zrpc.RpcServerConf

	DB    types.DBConfig
	Cache cache.CacheConf

	Notifier zrpc.RpcClientConf

	DeviceHubConsumer consumerv2.Config

	HuaweiCloud common.HuaweiCloudConfig
	Alert       common.AlertConfig
}

func (c Config) ListenOn() string {
	return c.RpcServerConf.ListenOn
}

func (c Config) LogConfig() logx.LogConf {
	return c.RpcServerConf.ServiceConf.Log
}
