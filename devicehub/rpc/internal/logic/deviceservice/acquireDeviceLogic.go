package deviceservicelogic

import (
	"context"
	"database/sql"
	"time"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"
	"github.com/zeromicro/go-zero/core/stores/sqlx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/redislock"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/codes"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/devicehub/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/devicehub/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/devicehub/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/devicehub/rpc/pb"
)

type AcquireDeviceLogic struct {
	*BaseLogic
}

func NewAcquireDeviceLogic(ctx context.Context, svcCtx *svc.ServiceContext) *AcquireDeviceLogic {
	return &AcquireDeviceLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// AcquireDevice 占用设备
func (l *AcquireDeviceLogic) AcquireDevice(in *pb.AcquireDeviceReq) (out *pb.AcquireDeviceResp, err error) {
	return l.AcquireDeviceForInternal(in, true)
}

func (l *AcquireDeviceLogic) AcquireDeviceForInternal(
	in *pb.AcquireDeviceReq, withLock bool,
) (out *pb.AcquireDeviceResp, err error) {
	out = &pb.AcquireDeviceResp{}

	var (
		udid = in.GetUdid()

		device *model.Device
	)

	// check the device data before locking
	device, err = l.checkDeviceByUDID(udid)
	if err != nil {
		return nil, err
	} else if device.State != string(common.Idle) {
		return nil, errorx.Errorf(
			codes.AcquireDeviceFailure,
			"the state of device is not %q, udid: %s, state: %s",
			common.Idle, udid, device.State,
		)
	}

	fn := func() error {
		// double-check the device data after locking
		device, err = l.checkDeviceByUDID(udid)
		if err != nil {
			return err
		} else if device.State != string(common.Idle) {
			return errorx.Errorf(
				codes.AcquireDeviceFailure,
				"the state of device is not %q, udid: %s, state: %s",
				common.Idle, udid, device.State,
			)
		}

		device.PreviousState = sql.NullString{
			String: device.State,
			Valid:  true,
		}
		device.State = string(common.InUse)
		device.Token = sql.NullString{
			String: utils.GenNanoId(""),
			Valid:  true,
		}
		device.StartedAt = sql.NullTime{
			Time:  time.Now(),
			Valid: true,
		}
		device.Expiration = sql.NullInt64{
			Int64: in.GetExpiration(),
			Valid: in.GetExpiration() != 0,
		}

		return l.svcCtx.DeviceModel.Trans(
			l.ctx, func(context context.Context, session sqlx.Session) error {
				if _, err := l.svcCtx.DeviceModel.Update(context, session, device); err != nil {
					return errors.Wrapf(
						errorx.Err(errorx.DBError, err.Error()),
						"failed to modify the device, table: %s, device: %s, error: %+v",
						l.svcCtx.DeviceModel.Table(), jsonx.MarshalToStringIgnoreError(device), err,
					)
				}

				return nil
			},
		)
	}

	if withLock {
		key := common.GenerateLockKeyByUDID(udid)
		err = l.callWithLock(key, fn, redislock.WithExpire(constLockExpireTime))
	} else {
		err = fn()
	}
	if err != nil {
		return nil, err
	}

	out.Device = &pb.Device{}
	if err = utils.Copy(out.GetDevice(), device, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy device to response, data: %s, error: %+v",
			jsonx.MarshalToStringIgnoreError(device), err,
		)
	}

	return out, nil
}
