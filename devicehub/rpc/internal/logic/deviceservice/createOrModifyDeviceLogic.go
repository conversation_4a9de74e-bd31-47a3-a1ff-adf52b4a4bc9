package deviceservicelogic

import (
	"context"
	"database/sql"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"golang.org/x/exp/slices"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/redislock"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/userinfo"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/devicehub/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/devicehub/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/devicehub/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/devicehub/rpc/pb"
)

// only the following state change scenarios are allowed in the device modifying logic
// √ idle => idle or offline or reserved
// √ in_use => in_use or offline or reserved
// √ releasing => idle or releasing or offline or reserved
// √ offline => idle or offline or reserved
// √ reserved => idle or offline or reserved
var stateTransitionRulesOfModifyDeviceLogic = map[common.DeviceState][]common.DeviceState{
	common.Idle:      {common.Idle, common.Offline, common.Reserved},
	common.InUse:     {common.InUse, common.Offline, common.Reserved},
	common.Releasing: {common.Idle, common.Releasing, common.Offline, common.Reserved},
	common.Offline:   {common.Idle, common.Offline, common.Reserved},
	common.Reserved:  {common.Idle, common.Offline, common.Reserved},
}

type CreateOrModifyDeviceLogic struct {
	*BaseLogic
}

func NewCreateOrModifyDeviceLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CreateOrModifyDeviceLogic {
	return &CreateOrModifyDeviceLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// CreateOrModifyDevice 创建或编辑设备
func (l *CreateOrModifyDeviceLogic) CreateOrModifyDevice(in *pb.CreateOrModifyDeviceReq) (
	out *pb.CreateOrModifyDeviceResp, err error,
) {
	udid := in.GetUdid()
	user := userinfo.System()

	needToNotify := false
	key := common.GenerateLockKeyByUDID(udid)
	fn := func() error {
		var (
			device *model.Device
			fn     func(ctx context.Context, session sqlx.Session, data *model.Device) (sql.Result, error)
		)

		// checking if the device corresponding to the UDID already exists
		device, err = l.svcCtx.DeviceModel.FindOneByUdid(l.ctx, udid)
		if err != nil {
			if !errors.Is(err, model.ErrNotFound) {
				return errors.Wrapf(
					errorx.Err(errorx.DBError, err.Error()),
					"failed to find the device, udid: %s, error: %+v", udid, err,
				)
			}

			// create the device data in the database
			device = &model.Device{
				Udid:     udid,
				Name:     in.GetName(),
				Type:     int64(in.GetType()),
				Platform: int64(in.GetPlatform()),
				Brand:    in.GetBrand(),
				Model:    in.GetModel(),
				Version:  in.GetVersion(),
				Serial:   in.GetSerial(),
				Provider: sql.NullString{
					String: in.GetProvider(),
					Valid:  in.GetProvider() != "",
				},
				ProviderType: sql.NullInt64{
					Int64: int64(in.GetProviderType()),
					Valid: in.GetProviderType() != 0,
				},
				RemoteAddress: sql.NullString{
					String: in.GetRemoteAddress(),
					Valid:  in.GetRemoteAddress() != "",
				},
				State: string(in.GetState().ConvertTo()),
				Metadata: sql.NullString{
					String: protobuf.MarshalJSONToStringIgnoreError(in.GetMetadata()),
					Valid:  in.GetMetadata() != nil,
				},
				CreatedBy: user.Account,
				UpdatedBy: user.Account,
			}
			fn = l.svcCtx.DeviceModel.Insert
		} else {
			if device.Provider.Valid && device.Provider.String != in.GetProvider() {
				l.Warnf(
					"modify the provider of device, udid: %s, provider: %s => %s",
					udid, device.Provider.String, in.GetProvider(),
				)
			}

			// modify the device data in the database
			if in.GetName() != "" {
				device.Name = in.GetName()
			}

			// `type`、`platform`、`brand`、`model`、`serial` cannot to be modified
			if in.GetVersion() != "" {
				device.Version = in.GetVersion()
			}
			if in.GetProvider() != "" {
				device.Provider = sql.NullString{
					String: in.GetProvider(),
					Valid:  true,
				}
				device.ProviderType = sql.NullInt64{
					Int64: int64(in.GetProviderType()),
					Valid: in.GetProviderType() != 0,
				}
			}
			if in.GetRemoteAddress() != "" {
				device.RemoteAddress = sql.NullString{
					String: in.GetRemoteAddress(),
					Valid:  true,
				}
			}
			if in.GetMetadata() != nil {
				device.Metadata = sql.NullString{
					String: protobuf.MarshalJSONToStringIgnoreError(in.GetMetadata()),
					Valid:  true,
				}
			}

			state := in.GetState().ConvertTo()
			if l.isValidStateTransition(common.DeviceState(device.State), state) {
				if common.DeviceState(device.State) == state {
					l.Infof(
						"no need to modify the state of device, udid: %s, state: %s => %s", udid, device.State, state,
					)
				} else {
					l.Infof("modify the state of device, udid: %s, state: %s => %s", udid, device.State, state)
					device.PreviousState = sql.NullString{
						String: device.State,
						Valid:  true,
					}
					device.State = string(state)
				}
			}
			if !slices.Contains(
				[]common.DeviceState{common.InUse, common.Releasing}, common.DeviceState(device.State),
			) {
				device.Token = sql.NullString{}
				device.StartedAt = sql.NullTime{}
				device.Expiration = sql.NullInt64{}
			}

			fn = l.svcCtx.DeviceModel.Update
		}

		if err = l.svcCtx.DeviceModel.Trans(
			l.ctx, func(context context.Context, session sqlx.Session) error {
				if _, err := fn(context, session, device); err != nil {
					return errors.Wrapf(
						errorx.Err(errorx.DBError, err.Error()),
						"failed to create or modify the device, table: %s, device: %s, error: %+v",
						l.svcCtx.DeviceModel.Table(), jsonx.MarshalToStringIgnoreError(device), err,
					)
				}

				return nil
			},
		); err != nil {
			return err
		}

		if device.State == string(common.Idle) {
			needToNotify = true
		}

		return nil
	}

	if err = l.callWithLock(
		key, fn, redislock.WithExpire(common.ConstLockExpireTime), redislock.WithTimeout(common.ConstLockTimeout),
	); err != nil {
		return nil, err
	}

	if needToNotify {
		l.idleDevicesNotify()
	}

	return &pb.CreateOrModifyDeviceResp{}, nil
}

func (l *CreateOrModifyDeviceLogic) isValidStateTransition(before, after common.DeviceState) bool {
	if allowed, ok := stateTransitionRulesOfModifyDeviceLogic[before]; ok {
		return slices.Contains[[]common.DeviceState, common.DeviceState](allowed, after)
	}

	return false
}
