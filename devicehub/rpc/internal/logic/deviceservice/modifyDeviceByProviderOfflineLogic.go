package deviceservicelogic

import (
	"context"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/stores/sqlx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/redislock"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/devicehub/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/devicehub/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/devicehub/rpc/pb"
)

type ModifyDeviceByProviderOfflineLogic struct {
	*BaseLogic
}

func NewModifyDeviceByProviderOfflineLogic(
	ctx context.Context, svcCtx *svc.ServiceContext,
) *ModifyDeviceByProviderOfflineLogic {
	return &ModifyDeviceByProviderOfflineLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// ModifyDeviceByProviderOffline 编辑下线的`provider`管理的设备
func (l *ModifyDeviceByProviderOfflineLogic) ModifyDeviceByProviderOffline(in *pb.ModifyDeviceByProviderOfflineReq) (
	out *pb.ModifyDeviceByProviderOfflineResp, err error,
) {
	provider := in.GetProvider()
	providerType := in.GetProviderType()

	// no locking is applied to the devices being modified here,
	// because no device information will be reported by the provider when it goes offline

	key := common.GenerateLockKeyByProvider(provider)
	fn := func() (err error) {
		return l.svcCtx.DeviceModel.Trans(
			l.ctx, func(context context.Context, session sqlx.Session) error {
				if _, err := l.svcCtx.DeviceModel.UpdateToOfflineByProvider(
					context, session, provider, int64(providerType),
				); err != nil {
					return errors.Wrapf(
						errorx.Err(errorx.DBError, err.Error()),
						"failed to modify device by provider, table: %s, provider: %s, provider_type: %s, error: %+v",
						l.svcCtx.DeviceModel.Table(), provider, providerType.String(), err,
					)
				}

				return nil
			},
		)
	}

	if err = l.callWithLock(key, fn, redislock.WithExpire(constLockExpireTime)); err != nil {
		return nil, err
	}

	return &pb.ModifyDeviceByProviderOfflineResp{}, nil
}
