package deviceservicelogic

import (
	"context"
	"math"

	"github.com/Masterminds/squirrel"
	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sqlbuilder"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/devicehub/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/devicehub/rpc/pb"
)

type SearchDeviceLogic struct {
	*BaseLogic
}

func NewSearchDeviceLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SearchDeviceLogic {
	return &SearchDeviceLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// SearchDevice 搜索设备
func (l *SearchDeviceLogic) SearchDevice(in *pb.SearchDeviceReq) (out *pb.SearchDeviceResp, err error) {
	out = &pb.SearchDeviceResp{}

	selectBuilder, countBuilder := l.generateSearchSqlBuilder(in)

	count, err := l.svcCtx.DeviceModel.FindCount(l.ctx, countBuilder)
	if err != nil {
		return nil, errors.Wrapf(errorx.Err(errorx.DBError, err.Error()), "failed to count devices, error: %+v", err)
	}
	out.TotalCount = uint64(count)
	out.TotalPage = 1

	devices, err := l.svcCtx.DeviceModel.FindNoCacheByQuery(l.ctx, selectBuilder)
	if err != nil {
		return nil, errors.Wrapf(errorx.Err(errorx.DBError, err.Error()), "failed to find devices, error: %+v", err)
	}

	out.Items = make([]*pb.Device, 0, len(devices))
	for _, device := range devices {
		item := &pb.Device{}
		if err = utils.Copy(item, device, l.converters...); err != nil {
			return nil, errors.Wrapf(
				errorx.Err(errorx.CopyToStructFailure, err.Error()),
				"failed to copy device to response, data: %s, error: %+v",
				jsonx.MarshalToStringIgnoreError(device), err,
			)
		}

		// 注：[2025-08-07] 这个逻辑放在`api`层做
		//if item.Token != "" {
		//	// hide the `token`
		//	item.Token = constants.SensitiveWorld
		//}
		out.Items = append(out.Items, item)
	}

	pagination := in.GetPagination()
	if pagination != nil {
		out.CurrentPage = pagination.GetCurrentPage()
		out.PageSize = pagination.GetPageSize()
		out.TotalPage = uint64(math.Ceil(float64(out.TotalCount) / float64(out.PageSize)))
	}

	return out, nil
}

func (l *SearchDeviceLogic) generateSearchSqlBuilder(req *pb.SearchDeviceReq) (sb, scb squirrel.SelectBuilder) {
	m := l.svcCtx.DeviceModel

	sb = sqlbuilder.SearchOptions(
		m.SelectBuilder(),
		sqlbuilder.WithCondition(m, req.GetCondition()),
		sqlbuilder.WithPagination(m, req.GetPagination()),
		sqlbuilder.WithSort(m, req.GetSort()),
	)
	scb = sqlbuilder.SearchOptions(
		m.SelectCountBuilder(),
		sqlbuilder.WithCondition(m, req.GetCondition()),
	)

	return sb, scb
}
