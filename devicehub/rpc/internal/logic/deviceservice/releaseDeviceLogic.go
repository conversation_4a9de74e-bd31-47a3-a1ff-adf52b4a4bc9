package deviceservicelogic

import (
	"context"
	"database/sql"
	"fmt"
	"net/http"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"golang.org/x/exp/slices"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/redislock"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/transport/http/go-resty"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/codes"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/devicehub/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/devicehub/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/devicehub/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/devicehub/rpc/pb"
)

const (
	// Deprecated: use releaseDevicePath instead.
	coldDevicePath    = "/cold?udid=%s"
	releaseDevicePath = "/release?udid=%s"
)

type ReleaseDeviceLogic struct {
	*BaseLogic
}

func NewReleaseDeviceLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ReleaseDeviceLogic {
	return &ReleaseDeviceLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// ReleaseDevice 释放设备
func (l *ReleaseDeviceLogic) ReleaseDevice(in *pb.ReleaseDeviceReq) (out *pb.ReleaseDeviceResp, err error) {
	return l.ReleaseDeviceForInternal(in, true)
}

func (l *ReleaseDeviceLogic) ReleaseDeviceForInternal(
	in *pb.ReleaseDeviceReq, withLock bool,
) (out *pb.ReleaseDeviceResp, err error) {
	udid := in.GetUdid()
	token := in.GetToken()

	needToNotify := false
	fn := func() (err error) {
		var device *model.Device
		device, err = l.svcCtx.DeviceModel.FindOneByUdid(l.ctx, udid)
		if err != nil {
			if !errors.Is(err, model.ErrNotFound) {
				return errors.Wrapf(
					errorx.Err(errorx.DBError, err.Error()),
					"failed to find the device, udid: %s, error: %+v", udid, err,
				)
			} else {
				l.Warnf("the device to be released does not exist, udid: %s", udid)
				return nil
			}
		}

		if device.Token.Valid && device.Token.String != token {
			return errorx.Errorf(
				codes.ReleaseDeviceFailure,
				"the token does not belong to the target device, so you cannot release the device, udid: %s, token: %s",
				udid, token,
			)
		}

		atxFlag := false
		if device.Provider.Valid && device.ProviderType.Valid && device.ProviderType.Int64 == int64(common.ATX) {
			if slices.Contains(
				[]common.DeviceState{common.InUse, common.Releasing}, common.DeviceState(device.State),
			) {
				device.PreviousState = sql.NullString{
					String: device.State,
					Valid:  true,
				}
				device.State = string(common.Releasing)
				atxFlag = true
			} else {
				device.Token = sql.NullString{}
				device.StartedAt = sql.NullTime{}
				device.Expiration = sql.NullInt64{}
			}
		} else {
			if !slices.Contains(
				[]common.DeviceState{common.Offline, common.Reserved}, common.DeviceState(device.State),
			) {
				// the offline device does not need to update its state
				device.PreviousState = sql.NullString{
					String: device.State,
					Valid:  true,
				}
				device.State = string(common.Idle)
			}
			device.Token = sql.NullString{}
			device.StartedAt = sql.NullTime{}
			device.Expiration = sql.NullInt64{}
		}

		if err = l.svcCtx.DeviceModel.Trans(
			l.ctx, func(context context.Context, session sqlx.Session) error {
				if _, err := l.svcCtx.DeviceModel.Update(context, session, device); err != nil {
					return errors.Wrapf(
						errorx.Err(errorx.DBError, err.Error()),
						"failed to modify the device, table: %s, device: %s, error: %+v",
						l.svcCtx.DeviceModel.Table(), jsonx.MarshalToStringIgnoreError(device), err,
					)
				}

				return nil
			},
		); err != nil {
			return err
		}

		if atxFlag {
			var (
				c    *resty.Client
				req  *resty.Request
				resp *resty.Response
			)

			c = resty.NewClientWithContext(
				l.ctx, resty.ClientConf{
					BaseURL: device.Provider.String,
				},
			)

			req = c.NewRequest(
				resty.SetMethod(http.MethodPost), resty.SetURL(fmt.Sprintf(releaseDevicePath, device.Udid)),
			)
			resp, err = c.Send(req, 4*common.ConstRPCReleaseDeviceTimeout/5)
			if err != nil {
				return errors.Wrapf(
					errorx.Err(errorx.CallExternalAPIFailure, err.Error()),
					"failed to send the request to the provider, url: %s, error: %+v", req.URL, err,
				)
			}

			l.Infof(
				"got a response from the provider, url: %s, resp: %s, status: %s",
				req.URL, resp.String(), resp.Status(),
			)
		}

		if device.State == string(common.Idle) {
			needToNotify = true
		}

		return nil
	}

	if withLock {
		key := common.GenerateLockKeyByUDID(udid)
		err = l.callWithLock(
			key, fn, redislock.WithExpire(common.ConstLockExpireTime), redislock.WithTimeout(common.ConstLockTimeout),
		)
	} else {
		err = fn()
	}
	if err != nil {
		return nil, err
	}

	if needToNotify {
		l.idleDevicesNotify()
	}

	return &pb.ReleaseDeviceResp{}, nil
}
