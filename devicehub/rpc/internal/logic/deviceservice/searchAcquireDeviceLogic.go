package deviceservicelogic

import (
	"context"
	"math/rand"
	"time"

	"github.com/Masterminds/squirrel"
	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/mr"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sqlbuilder"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/codes"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/devicehub/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/devicehub/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/devicehub/rpc/pb"
)

type SearchAcquireDeviceLogic struct {
	*BaseLogic

	adl *AcquireDeviceLogic
	rdl *ReleaseDeviceLogic

	r *rand.Rand
}

func NewSearchAcquireDeviceLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SearchAcquireDeviceLogic {
	return &SearchAcquireDeviceLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),

		adl: NewAcquireDeviceLogic(ctx, svcCtx),
		rdl: NewReleaseDeviceLogic(ctx, svcCtx),

		r: rand.New(rand.NewSource(time.Now().Unix())),
	}
}

// SearchAcquireDevice 占用设备（通过查询条件）
func (l *SearchAcquireDeviceLogic) SearchAcquireDevice(in *pb.SearchAcquireDeviceReq) (
	out *pb.SearchAcquireDeviceResp, err error,
) {
	selectBuilder := l.generateSearchSqlBuilder(in)
	devices, err := l.svcCtx.DeviceModel.FindNoCacheByQuery(l.ctx, selectBuilder)
	if err != nil {
		return nil, errors.Wrapf(errorx.Err(errorx.DBError, err.Error()), "failed to find devices, error: %+v", err)
	}

	count := in.GetCount()
	if len(devices) < int(count) {
		return nil, errorx.Errorf(
			codes.InsufficientDevicesError,
			"not enough devices that meet the requirements, expected: %d, but got: %d",
			count, len(devices),
		)
	}

	acquired := make([]*pb.Device, 0, count)
	defer func() {
		if err != nil && len(acquired) > 0 {
			l.deferFuncOfReleaseDevices(acquired)
		}
	}()

	// pseudo-randomizes the order of devices
	l.r.Shuffle(len(devices), func(i, j int) { devices[i], devices[j] = devices[j], devices[i] })
	for _, device := range devices {
		if device.Udid == "" {
			continue
		} else if device.State != string(common.Idle) {
			l.Warnf("the state of device is not %q, udid: %s, state: %s", common.Idle, device.Udid, device.State)
			continue
		}

		var resp *pb.AcquireDeviceResp
		resp, err = l.adl.AcquireDevice(
			&pb.AcquireDeviceReq{
				Udid:       device.Udid,
				Expiration: in.GetExpiration(),
			},
		)
		if err != nil {
			// return immediately when encountering a system error
			if errorx.IsSystemError(errorx.Convert(errors.Cause(err)).Code()) {
				return nil, err
			}

			l.Warnf("failed to acquire device, error: %s", err.Error())
			continue
		} else {
			l.Infof("acquire the device successfully, device: %s", protobuf.MarshalJSONIgnoreError(resp.GetDevice()))
		}

		acquired = append(acquired, resp.GetDevice())
		if len(acquired) == int(count) {
			break
		}
	}

	if len(acquired) < int(count) {
		return nil, errorx.Errorf(
			codes.InsufficientDevicesError,
			"not enough devices that meet the requirements, expected: %d, but got: %d",
			count, len(acquired),
		)
	}

	return &pb.SearchAcquireDeviceResp{
		Devices: acquired,
	}, nil
}

func (l *SearchAcquireDeviceLogic) generateSearchSqlBuilder(req *pb.SearchAcquireDeviceReq) (sb squirrel.SelectBuilder) {
	m := l.svcCtx.DeviceModel

	sb = sqlbuilder.SearchOptions(
		m.SelectBuilder().Where("`state` = ?", common.Idle), // only acquire idle devices
		sqlbuilder.WithCondition(m, req.GetCondition()),
	)

	return sb
}

func (l *SearchAcquireDeviceLogic) deferFuncOfReleaseDevices(devices []*pb.Device) {
	_ = mr.MapReduceVoid[*pb.Device, any](
		func(source chan<- *pb.Device) {
			for _, device := range devices {
				source <- device
			}
		}, func(item *pb.Device, writer mr.Writer[any], cancel func(error)) {
			if item == nil {
				return
			}

			if _, err := l.rdl.ReleaseDevice(
				&pb.ReleaseDeviceReq{
					Udid:  item.GetUdid(),
					Token: item.GetToken(),
				},
			); err != nil {
				l.Errorf("%s", err.Error())
			}
		}, func(pipe <-chan any, cancel func(error)) {
		},
	)
}
