package internal

import (
	"bytes"
	"context"
	"database/sql"
	_ "embed"
	ttemplate "text/template"
	"time"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"
	"github.com/zeromicro/go-zero/core/lang"
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/mr"
	"github.com/zeromicro/go-zero/core/proc"
	"github.com/zeromicro/go-zero/core/stores/sqlx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/caller"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/redislock"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/timewheel"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/userinfo"
	notifierpb "gitlab.ttyuyin.com/TestDevelopment/qet-backend-middleware/notifier/rpc/pb"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/codes"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/template"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/utils"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/devicehub/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/devicehub/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/devicehub/rpc/internal/svc"
)

const (
	expireOfMonitorTask = 30 * time.Second
	daysOfWatch         = 7
)

var (
	_ IMonitor = (*NoopMonitor)(nil)
	_ IMonitor = (*Monitor)(nil)

	//go:embed alert.template
	alertContentTemplate string
	//go:embed recovery.template
	recoveryContentTemplate string

	alertTemplate    = ttemplate.Must(template.Parse("alertTemplate", alertContentTemplate))
	recoveryTemplate = ttemplate.Must(template.Parse("recoveryTemplate", recoveryContentTemplate))
)

type (
	IMonitor interface {
		Watch()
	}

	NoopMonitor struct{}

	Monitor struct {
		logx.Logger
		ctx    context.Context
		svcCtx *svc.ServiceContext

		quitCh chan lang.PlaceholderType
	}

	notificationItem struct {
		PlatformType    common.DevicePlatform `json:"platform_type"`     // 平台类型
		DeviceType      common.DeviceTypeZH   `json:"device_type"`       // 设备类型
		DeviceID        string                `json:"device_id"`         // 设备ID
		DeviceName      string                `json:"device_name"`       // 设备名称
		PreviousState   string                `json:"previous_state"`    // 设备上一次状态
		State           string                `json:"state"`             // 设备当前状态
		PreviousStateZH common.DeviceStateZH  `json:"previous_state_zh"` // 设备上一次状态
		StateZH         common.DeviceStateZH  `json:"state_zh"`          // 设备当前状态
		Token           string                `json:"token"`             // 令牌，用于释放设备时的验证
		RuleDuration    time.Duration         `json:"rule_duration"`     // 告警规则配置的持续时间
		Duration        time.Duration         `json:"duration"`          // 持续时间
		Times           int64                 `json:"times"`             // 告警次数
		UpdatedAt       time.Time             `json:"updated_at"`        // 更新时间

		WithCallback bool                           `json:"with_callback"` // 是否需要回调
		CallbackType constants.LarkCardCallbackType `json:"callback_type"` // 回调类型
	}
)

func newMonitor(svcCtx *svc.ServiceContext) IMonitor {
	if svcCtx.Config.Alert.WebhookOfLark == "" {
		return &NoopMonitor{}
	}

	ctx := context.Background()
	m := &Monitor{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,

		quitCh: make(chan lang.PlaceholderType, 1),
	}

	proc.AddShutdownListener(m.stop)
	return m
}

func (m *NoopMonitor) Watch() {}

func (m *Monitor) stop() {
	close(m.quitCh)
}

func (m *Monitor) Watch() {
	ticker := timewheel.NewTicker(2 * expireOfMonitorTask)
	defer ticker.Stop()

	var (
		key = common.ConstLockWatchDevice
		fn  = func() error {
			m.watch()
			return nil
		}
	)

	for {
		select {
		case <-m.quitCh:
			m.Info("got a quit signal while monitoring devices")
			return
		case <-ticker.C:
			err := caller.LockWithOptionDo(
				m.svcCtx.Redis, key, fn, redislock.WithExpire(expireOfMonitorTask),
			)
			if err != nil {
				if re, ok := errorx.RootError(err); !ok || re.Code() != errorx.AcquireRedisLockFailure {
					m.Errorf("failed to acquire the redis lock with key[%s], error: %+v", key, err)
				} else {
					m.Info("another service is monitoring devices")
				}
				continue
			}
		}
	}
}

func (m *Monitor) watch() {
	ctx, cancel := context.WithTimeout(m.ctx, expireOfMonitorTask)
	defer cancel()

	rules, err := m.svcCtx.RuleModel.FindAll(ctx)
	if err != nil {
		m.Errorf("failed to find alert rules, error: %+v", err)
		return
	} else if len(rules) == 0 {
		m.Info("not found any alert rules")
		return
	}

	// only retrieve devices that have been modified in the last 7 days
	devices, err := m.svcCtx.DeviceModel.FindNoCacheByQuery(
		ctx,
		m.svcCtx.DeviceModel.SelectBuilder().Where("`updated_at` >= ?", time.Now().AddDate(0, 0, -daysOfWatch)),
	)
	if err != nil {
		m.Errorf("failed to find devices, error: %+v", err)
		return
	} else if len(devices) == 0 {
		m.Infof("not found any devices that have been modified in the last %d days", daysOfWatch)
	}

	alerts, err := m.svcCtx.AlertModel.FindAll(ctx)
	if err != nil {
		m.Errorf("failed to find alert records, error: %+v", err)
		return
	}
	cache := make(map[string]*model.Alert, len(alerts))
	for _, alert := range alerts {
		if alert.RuleId == "" || alert.Udid == "" {
			continue
		}

		cache[alert.RuleId+":"+alert.Udid] = alert
	}

	_ = mr.MapReduceVoid[*model.Device, any](
		func(source chan<- *model.Device) {
			for _, device := range devices {
				if device.Udid == "" {
					continue
				}

				source <- device
			}
		}, func(item *model.Device, writer mr.Writer[any], cancel func(error)) {
			for _, rule := range rules {
				if e := m.checkForAlert(ctx, item, rule, cache); e != nil {
					m.Errorf("failed to alert, device: %s, rule: %s, error: %+v", item.Udid, rule.RuleId, e)
				}

				if e := m.checkForRecovery(ctx, item, rule, cache); e != nil {
					m.Errorf("failed to recovery, device: %s, rule: %s, error: %+v", item.Udid, rule.RuleId, e)
				}
			}
		}, func(pipe <-chan any, cancel func(error)) {
		}, mr.WithContext(ctx),
	)
}

func (m *Monitor) checkForAlert(
	ctx context.Context, device *model.Device, rule *model.Rule, alerts map[string]*model.Alert,
) error {
	// if the device is in use and has a expiration time, use the expiration time as the duration of rule
	if device.State == rule.DestinationState &&
		device.State == string(common.InUse) &&
		device.Expiration.Valid && device.Expiration.Int64 > 0 {
		rule.Duration = device.Expiration.Int64
	}
	duration := time.Duration(rule.Duration) * time.Second

	if device.PreviousState.String == rule.SourceState &&
		device.State == rule.DestinationState &&
		time.Since(device.UpdatedAt) >= duration {
		v, ok := alerts[rule.RuleId+":"+device.Udid]
		if !ok {
			// first match of the alert rule
			return m.alert(ctx, device, rule, nil)
		} else if v.Times >= rule.Times {
			// the alert limit has been reached
			return nil
		} else if time.Since(v.UpdatedAt) >= duration {
			// the nth match of the alert rule
			return m.alert(ctx, device, rule, v)
		}
	}

	// not match the alert rule
	return nil
}

func (m *Monitor) checkForRecovery(
	ctx context.Context, device *model.Device, rule *model.Rule, alerts map[string]*model.Alert,
) error {
	v, ok := alerts[rule.RuleId+":"+device.Udid]
	if !ok {
		return nil
	}

	if device.State != rule.DestinationState || time.Since(device.UpdatedAt) < time.Duration(rule.Duration)*time.Second {
		return m.recovery(ctx, device, rule, v)
	}

	return nil
}

func (m *Monitor) alert(ctx context.Context, device *model.Device, rule *model.Rule, alert *model.Alert) error {
	var (
		user = userinfo.System()
		now  = time.Now()

		fn func(context.Context, sqlx.Session, *model.Alert) (sql.Result, error)
	)

	if alert == nil {
		alert = &model.Alert{
			AlertId:   utils.GenAlertID(),
			RuleId:    rule.RuleId,
			Udid:      device.Udid,
			Times:     1,
			StartedAt: device.UpdatedAt,
			Deleted:   0,
			CreatedBy: user.Account,
			UpdatedBy: user.Account,
			CreatedAt: now,
			UpdatedAt: now,
		}
		fn = m.svcCtx.AlertModel.Insert
	} else {
		alert.Times += 1
		alert.UpdatedAt = now
		fn = m.svcCtx.AlertModel.Update
	}

	return m.svcCtx.AlertModel.Trans(
		ctx, func(context context.Context, session sqlx.Session) error {
			if _, err := fn(context, session, alert); err != nil {
				return errors.Wrapf(
					errorx.Err(errorx.DBError, err.Error()),
					"failed to create or modify the alert, table: %s, alert: %s, error: %+v",
					m.svcCtx.AlertModel.Table(), jsonx.MarshalToStringIgnoreError(alert), err,
				)
			}

			content, err := m.getAlertContent(device, rule, alert)
			if err != nil {
				return errors.Wrapf(
					errorx.Err(codes.ParseOrRenderTemplateFailure, err.Error()),
					"failed to get the notification content, device: %s, error: %+v", device.Udid, err,
				)
			}

			if _, err = m.svcCtx.NotifierRPC.Notify(
				ctx, &notifierpb.NotifyReq{
					Items: []*notifierpb.NotifyItem{
						m.makeNotifyItemByContent(content),
					},
				},
			); err != nil {
				return err
			}

			return nil
		},
	)
}

func (m *Monitor) recovery(ctx context.Context, device *model.Device, rule *model.Rule, alert *model.Alert) error {
	if alert == nil {
		return nil
	}

	return m.svcCtx.AlertModel.Trans(
		ctx, func(context context.Context, session sqlx.Session) error {
			if err := m.svcCtx.AlertModel.LogicDelete(context, session, alert.Id); err != nil {
				return errors.Wrapf(
					errorx.Err(errorx.DBError, err.Error()),
					"failed to delete the alert, table: %s, alert: %s, error: %+v",
					m.svcCtx.AlertModel.Table(), jsonx.MarshalToStringIgnoreError(alert), err,
				)
			}

			content, err := m.getRecoveryContent(device, rule, alert)
			if err != nil {
				return errors.Wrapf(
					errorx.Err(codes.ParseOrRenderTemplateFailure, err.Error()),
					"failed to get the notification content, device: %s, error: %+v", device.Udid, err,
				)
			}

			if _, err = m.svcCtx.NotifierRPC.Notify(
				ctx, &notifierpb.NotifyReq{
					Items: []*notifierpb.NotifyItem{
						m.makeNotifyItemByContent(content),
					},
				},
			); err != nil {
				return err
			}

			return nil
		},
	)
}

func (m *Monitor) makeNotifyItemByContent(content string) *notifierpb.NotifyItem {
	if m.svcCtx.Config.Alert.ChatIDOfLark != "" {
		return &notifierpb.NotifyItem{
			Item: &notifierpb.NotifyItem_LarkCustomApp{
				LarkCustomApp: &notifierpb.LarkCustomApp{
					Receivers: []*notifierpb.LarkReceiver{
						{
							Id:   m.svcCtx.Config.Alert.ChatIDOfLark,
							Type: notifierpb.LarkReceiveIdType_CHAT_ID,
						},
					},
					MsgType: notifierpb.LarkMessageType_INTERACTIVE,
					Content: content,
				},
			},
		}
	}

	return &notifierpb.NotifyItem{
		Item: &notifierpb.NotifyItem_LarkGroupChat{
			LarkGroupChat: &notifierpb.LarkGroupChat{
				WebhookUrl: m.svcCtx.Config.Alert.WebhookOfLark,
				MsgType:    notifierpb.LarkMessageType_INTERACTIVE,
				Content:    content,
			},
		},
	}
}

func (m *Monitor) getAlertContent(device *model.Device, rule *model.Rule, alert *model.Alert) (string, error) {
	return getContent(
		alertTemplate, &notificationItem{
			PlatformType:    common.ConvertToDevicePlatform(device.Platform),
			DeviceType:      common.ConvertToDeviceTypeZH(device.Type),
			DeviceID:        device.Udid,
			DeviceName:      device.Name,
			PreviousState:   rule.SourceState,
			State:           rule.DestinationState,
			PreviousStateZH: common.ConvertToDeviceStateZH(rule.SourceState),
			StateZH:         common.ConvertToDeviceStateZH(rule.DestinationState),
			Token:           device.Token.String,
			RuleDuration:    time.Duration(rule.Duration) * time.Second,
			Duration:        time.Since(alert.StartedAt).Truncate(time.Second),
			Times:           alert.Times,
			UpdatedAt:       device.UpdatedAt,
			WithCallback:    m.svcCtx.Config.Alert.ChatIDOfLark != "",
			CallbackType:    constants.LarkCardCallbackTypeReleaseDevice,
		},
	)
}

func (m *Monitor) getRecoveryContent(device *model.Device, rule *model.Rule, alert *model.Alert) (string, error) {
	return getContent(
		recoveryTemplate, &notificationItem{
			PlatformType:    common.ConvertToDevicePlatform(device.Platform),
			DeviceType:      common.ConvertToDeviceTypeZH(device.Type),
			DeviceID:        device.Udid,
			DeviceName:      device.Name,
			PreviousState:   rule.DestinationState,
			State:           device.State,
			PreviousStateZH: common.ConvertToDeviceStateZH(rule.DestinationState),
			StateZH:         common.ConvertToDeviceStateZH(device.State),
			Token:           device.Token.String,
			RuleDuration:    time.Duration(rule.Duration) * time.Second,
			Duration:        time.Since(alert.StartedAt).Truncate(time.Second),
			Times:           alert.Times,
			WithCallback:    m.svcCtx.Config.Alert.ChatIDOfLark != "",
			UpdatedAt:       device.UpdatedAt,
		},
	)
}

func getContent(notifyT *ttemplate.Template, item *notificationItem) (string, error) {
	buf := new(bytes.Buffer)
	defer buf.Reset()

	if err := notifyT.Execute(buf, item); err != nil {
		return "", err
	}

	return buf.String(), nil
}
