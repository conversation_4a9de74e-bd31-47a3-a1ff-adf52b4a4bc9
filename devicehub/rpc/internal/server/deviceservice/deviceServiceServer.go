// Code generated by goctl. DO NOT EDIT.
// goctl 1.8.3
// Source: devicehub.proto

package server

import (
	"context"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"

	deviceservicelogic "gitlab.ttyuyin.com/TestDevelopment/probe-backend/devicehub/rpc/internal/logic/deviceservice"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/devicehub/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/devicehub/rpc/pb"
)

type DeviceServiceServer struct {
	svcCtx *svc.ServiceContext
	pb.UnimplementedDeviceServiceServer
}

func NewDeviceServiceServer(svcCtx *svc.ServiceContext) *DeviceServiceServer {
	return &DeviceServiceServer{
		svcCtx: svcCtx,
	}
}

// CreateOrModifyDevice 创建或编辑设备
func (s *DeviceServiceServer) CreateOrModifyDevice(ctx context.Context, in *pb.CreateOrModifyDeviceReq) (*pb.CreateOrModifyDeviceResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := deviceservicelogic.NewCreateOrModifyDeviceLogic(ctx, s.svcCtx)

	return l.CreateOrModifyDevice(in)
}

// ModifyDeviceByProviderOffline 编辑下线的`provider`管理的设备
func (s *DeviceServiceServer) ModifyDeviceByProviderOffline(ctx context.Context, in *pb.ModifyDeviceByProviderOfflineReq) (*pb.ModifyDeviceByProviderOfflineResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := deviceservicelogic.NewModifyDeviceByProviderOfflineLogic(ctx, s.svcCtx)

	return l.ModifyDeviceByProviderOffline(in)
}

// GetDevice 获取设备（通过`udid`）
func (s *DeviceServiceServer) GetDevice(ctx context.Context, in *pb.GetDeviceReq) (*pb.GetDeviceResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := deviceservicelogic.NewGetDeviceLogic(ctx, s.svcCtx)

	return l.GetDevice(in)
}

// SearchDevice 搜索设备
func (s *DeviceServiceServer) SearchDevice(ctx context.Context, in *pb.SearchDeviceReq) (*pb.SearchDeviceResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := deviceservicelogic.NewSearchDeviceLogic(ctx, s.svcCtx)

	return l.SearchDevice(in)
}

// AcquireDevice 占用设备（通过`udid`）
func (s *DeviceServiceServer) AcquireDevice(ctx context.Context, in *pb.AcquireDeviceReq) (*pb.AcquireDeviceResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := deviceservicelogic.NewAcquireDeviceLogic(ctx, s.svcCtx)

	return l.AcquireDevice(in)
}

// SearchAcquireDevice 占用设备（通过查询条件）
func (s *DeviceServiceServer) SearchAcquireDevice(ctx context.Context, in *pb.SearchAcquireDeviceReq) (*pb.SearchAcquireDeviceResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := deviceservicelogic.NewSearchAcquireDeviceLogic(ctx, s.svcCtx)

	return l.SearchAcquireDevice(in)
}

// ReleaseDevice 释放设备（通过`udid`）
func (s *DeviceServiceServer) ReleaseDevice(ctx context.Context, in *pb.ReleaseDeviceReq) (*pb.ReleaseDeviceResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := deviceservicelogic.NewReleaseDeviceLogic(ctx, s.svcCtx)

	return l.ReleaseDevice(in)
}
