package internal

import (
	"context"
	"fmt"
	"net/http"
	"sync"
	"time"

	"github.com/Masterminds/squirrel"
	cphmodel "github.com/huaweicloud/huaweicloud-sdk-go-v3/services/cph/v1/model"
	"github.com/redis/go-redis/v9"
	"github.com/zeromicro/go-zero/core/jsonx"
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/mr"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/caller"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/redislock"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/timewheel"

	commonpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/devicehub/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/devicehub/model"
	deviceservicelogic "gitlab.ttyuyin.com/TestDevelopment/probe-backend/devicehub/rpc/internal/logic/deviceservice"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/devicehub/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/devicehub/rpc/pb"
)

const (
	lockKeyOfSync    = "lock:device:cph:sync"
	lockKeyOfRestart = "lock:device:cph:restart"
	chanKeyOfSync    = "chan:device:cph:sync"
	chanKeyOfRestart = "chan:device:cph:restart"

	expireOfSyncTask       = 2 * time.Minute
	expireOfRestartTask    = 10 * time.Minute
	expireOfRestartSubTask = 5 * time.Minute

	defaultSyncCron    = "0 0/1 * * ?" // every hour
	defaultRestartCron = "5 1 * * ?"   // every day 1:05
)

type CPHHandler struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext

	reserved sync.Map
}

func newCPHHandler(svcCtx *svc.ServiceContext) *CPHHandler {
	ctx := context.Background()
	h := &CPHHandler{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}

	for _, phone := range svcCtx.Config.HuaweiCloud.ReservePhones {
		if phone.PhoneID == "" {
			continue
		}

		h.reserved.Store(phone.PhoneID, phone)
	}

	return h
}

func (h *CPHHandler) Sync() error {
	var (
		ctx    context.Context
		cancel context.CancelFunc

		result string
	)

	ctx, cancel = context.WithTimeout(h.ctx, expireOfSyncTask)
	defer cancel()

	// acquire redis lock
	lock, err := redislock.NewRedisLockAndAcquire(h.svcCtx.Redis, lockKeyOfSync, redislock.WithExpire(expireOfSyncTask))
	if err != nil {
		if re, ok := errorx.RootError(err); !ok || re.Code() != errorx.AcquireRedisLockFailure {
			h.Errorf("failed to acquire the redis lock, key: %s, error: %+v", lockKeyOfSync, err)
			return err
		}

		h.Info("another service is executing the sync task of Huawei Cloud Phones, wait for the execution result")
		ps := h.svcCtx.RedisNode.Subscribe(ctx, chanKeyOfSync)
		defer func(ps *redis.PubSub) {
			err := ps.Close()
			if err != nil {
				h.Errorf("failed to close PubSub, error: %+v", err)
			}
		}(ps)

		select {
		case msg, ok := <-ps.Channel():
			if ok {
				h.Infof("the result of Huawei Cloud Phones sync task: %+v", msg)
			} else {
				h.Error("the channel of PubSub has been closed")
			}
		case <-ctx.Done():
			h.Errorf("waiting for the result of Huawei Cloud Phones sync task timed out, error: %+v", ctx.Err())
		}

		return nil
	}

	defer func() {
		// publish task result
		if val, err := h.svcCtx.RedisNode.Publish(ctx, chanKeyOfSync, result).Result(); err != nil {
			h.Errorf(
				"failed to send the result of Huawei Cloud Phones sync task to the channel[%s], error: %+v",
				chanKeyOfSync, err,
			)
		} else {
			h.Infof(
				"succeeded to send the result of Huawei Cloud Phones sync task to the channel[%s], number of subscriber: %d",
				chanKeyOfSync, val,
			)
		}

		// release redis lock
		e := lock.Release()
		if e != nil {
			if re, ok := errorx.RootError(e); ok && re.Code() != errorx.ReleaseRedisLockFailure && err == nil {
				err = e
			}
		}
	}()

	if err = mr.MapReduceVoid[cphmodel.Phone, any](
		h.generateFuncOfSync, h.mapperFuncOfSync, h.reducerFuncOfSync,
	); err != nil {
		result = fmt.Sprintf("the result of Huawei Cloud Phones sync task is FAILURE, error: %+v", err)
		h.Error(result)
	} else {
		result = "the result of Huawei Cloud Phones sync task is SUCCESS"
		h.Infof(result)
	}

	return err
}

func (h *CPHHandler) generateFuncOfSync(source chan<- cphmodel.Phone) {
	offset := int32(0)
	req := &cphmodel.ListCloudPhonesRequest{
		Offset:   &offset,
		ServerId: &h.svcCtx.Config.HuaweiCloud.ServerID,
	}

	for {
		resp, err := h.svcCtx.CPHClient.ListCloudPhones(req)
		if err != nil {
			h.Errorf(
				"failed to call `ListCloudPhones`, req: %s, error: %+v", jsonx.MarshalToStringIgnoreError(req), err,
			)
			break
		} else if resp.HttpStatusCode != http.StatusOK {
			h.Warnf(
				"the status code of `ListCloudPhones` is not %s, status: %d %s",
				http.StatusText(http.StatusOK), resp.HttpStatusCode, http.StatusText(resp.HttpStatusCode),
			)
		}

		if offset == 0 {
			h.Infof("the number of Huawei Cloud Phones: %d", *resp.Count)
		}
		if *resp.Count == 0 || len(*resp.Phones) == 0 {
			// break the for loop if the total count or the current page count is zero
			break
		}

		for _, phone := range *resp.Phones {
			if phone.PhoneId == nil || *phone.PhoneId == "" {
				continue
			}

			source <- phone
		}

		offset += *resp.Count
	}
}

func (h *CPHHandler) mapperFuncOfSync(item cphmodel.Phone, writer mr.Writer[any], cancel func(error)) {
	in := &pb.CreateOrModifyDeviceReq{
		Udid:     *item.PhoneId,
		Type:     commonpb.DeviceType_CLOUD_PHONE,
		Platform: commonpb.PlatformType_ANDROID, // all cloud phones are Android platform
		Brand:    common.ConstBrandOfHuaweiCloud,
	}

	if item.PhoneName != nil {
		in.Name = *item.PhoneName
	}
	if item.PhoneModelName != nil {
		in.Model = *item.PhoneModelName
	}
	if item.ImageId != nil {
		in.Version = *item.ImageId
	}
	if item.ServerId != nil {
		in.Provider = *item.ServerId // for cloud phones, temporarily use the `ServerId` as the `Provider`
	}
	if item.Status != nil {
		switch *item.Status {
		case int32(common.CPHStatusRunning):
			in.State = pb.DeviceState_IDLE
		default:
			h.Infof("Huawei Cloud Phone with abnormal status: %s", jsonx.MarshalToStringIgnoreError(item))
			in.State = pb.DeviceState_OFFLINE
		}
	}

	resp, err := h.svcCtx.CPHClient.ShowCloudPhoneDetail(&cphmodel.ShowCloudPhoneDetailRequest{PhoneId: *item.PhoneId})
	if err != nil {
		h.Errorf("failed to call `ShowCloudPhoneDetail`, phone_id: %s, error: %+v", *item.PhoneId, err)

		in.State = pb.DeviceState_OFFLINE
	} else {
		if resp.HttpStatusCode != http.StatusOK {
			h.Warnf(
				"the status code of `ShowCloudPhoneDetail` is not %s, status: %d %s",
				http.StatusText(http.StatusOK), resp.HttpStatusCode, http.StatusText(resp.HttpStatusCode),
			)
		}

		if resp.AccessInfos != nil {
			for _, info := range *resp.AccessInfos {
				if info.Type == nil || info.ServerIp == nil || info.AccessPort == nil {
					continue
				} else if *info.Type != common.ConstPortTypeOfADB {
					continue
				}

				in.RemoteAddress = fmt.Sprintf("%s:%d", *info.ServerIp, *info.AccessPort)
				in.Metadata = &pb.DeviceMetadata{
					CphAddress: fmt.Sprintf("%s:%d", *info.PhoneIp, *info.ListenPort),
				}
			}
			if in.RemoteAddress == "" {
				h.Infof("Huawei Cloud Phone with empty remote address: %s", jsonx.MarshalToStringIgnoreError(item))
				in.State = pb.DeviceState_OFFLINE
			}
		}
	}

	if _, ok := h.reserved.Load(*item.PhoneId); ok {
		in.State = pb.DeviceState_RESERVED
	}

	var (
		_ctx    context.Context
		_cancel context.CancelFunc
	)
	if h.svcCtx.Config.RpcServerConf.Timeout > 0 {
		_ctx, _cancel = context.WithTimeout(
			h.ctx, time.Duration(h.svcCtx.Config.RpcServerConf.Timeout)*time.Millisecond,
		)
		defer _cancel()
	} else {
		_ctx = h.ctx
	}

	if _, err = deviceservicelogic.NewCreateOrModifyDeviceLogic(_ctx, h.svcCtx).CreateOrModifyDevice(in); err != nil {
		h.Errorf("failed to call `CreateOrModifyDevice`, error: %+v", err)
	}
}

func (h *CPHHandler) reducerFuncOfSync(pipe <-chan any, cancel func(error)) {
}

func (h *CPHHandler) RegisterScheduledTasks() (err error) {
	tasks := make(map[string]func(), 2)

	// register scheduled task to synchronize Huawei Cloud Phone information
	syncCron := defaultSyncCron
	if h.svcCtx.Config.HuaweiCloud.SyncStrategy.CronExpression != "" {
		syncCron = h.svcCtx.Config.HuaweiCloud.SyncStrategy.CronExpression
	}
	tasks[syncCron] = func() {
		if err := h.Sync(); err != nil {
			h.Errorf("failed to sync Huawei Cloud Phones, error: %+v", err)
		}
	}

	// register scheduled task to reboot Huawei Cloud Phones
	// to avoid redundant execution of the task to reboot Huawei Cloud Phones by `devicehub` in different environments,
	// this task will not be executed if no scheduling strategy is configured
	if h.svcCtx.Config.HuaweiCloud.RestartStrategy.CronExpression != "" {
		tasks[h.svcCtx.Config.HuaweiCloud.RestartStrategy.CronExpression] = func() {
			if err := h.Restart(); err != nil {
				h.Errorf("failed to restart Huawei Cloud Phones, error: %+v", err)
			}
		}
	}

	if err = h.svcCtx.Scheduler.RegisterTasks(tasks); err != nil {
		return err
	}

	// start the cron scheduler
	h.svcCtx.Scheduler.Start()

	return nil
}

func (h *CPHHandler) Restart() error {
	var (
		ctx    context.Context
		cancel context.CancelFunc

		result string
	)

	ctx, cancel = context.WithTimeout(h.ctx, expireOfRestartTask)
	defer cancel()

	// acquire redis lock
	lock, err := redislock.NewRedisLockAndAcquire(
		h.svcCtx.Redis, lockKeyOfRestart, redislock.WithExpire(expireOfRestartTask),
	)
	if err != nil {
		if re, ok := errorx.RootError(err); !ok || re.Code() != errorx.AcquireRedisLockFailure {
			h.Errorf("failed to acquire the redis lock with key[%s], error: %+v", lockKeyOfRestart, err)
			return err
		}

		h.Info("another service is executing the restart task of Huawei Cloud Phones, wait for the execution result")
		ps := h.svcCtx.RedisNode.Subscribe(ctx, chanKeyOfRestart)
		defer func(ps *redis.PubSub) {
			err := ps.Close()
			if err != nil {
				h.Errorf("failed to close PubSub, error: %+v", err)
			}
		}(ps)

		select {
		case msg, ok := <-ps.Channel():
			if ok {
				h.Infof("the result of Huawei Cloud Phones restart task: %+v", msg)
			} else {
				h.Error("the channel of PubSub has been closed")
			}
		case <-ctx.Done():
			h.Errorf("waiting for the result of Huawei Cloud Phones restart task timed out, error: %+v", ctx.Err())
		}

		return nil
	}

	defer func() {
		// publish task result
		if val, err := h.svcCtx.RedisNode.Publish(ctx, chanKeyOfRestart, result).Result(); err != nil {
			h.Errorf(
				"failed to send the result of Huawei Cloud Phones restart task to the channel[%s], error: %+v",
				chanKeyOfRestart, err,
			)
		} else {
			h.Infof(
				"succeeded to send the result of Huawei Cloud Phones restart task to the channel[%s], number of subscriber: %d",
				chanKeyOfRestart, val,
			)
		}

		// release redis lock
		e := lock.Release()
		if e != nil {
			if re, ok := errorx.RootError(e); ok && re.Code() != errorx.ReleaseRedisLockFailure && err == nil {
				err = e
			}
		}
	}()

	if err = mr.MapReduceVoid[*model.Device, *model.Device](
		h.generateFuncOfRestart, h.mapperFuncOfRestart, h.reducerFuncOfRestart,
	); err != nil {
		result = fmt.Sprintf("the result of Huawei Cloud Phones restart task is FAILURE, error: %+v", err)
		h.Error(result)
	} else {
		result = "the result of Huawei Cloud Phones restart task is SUCCESS"
		h.Infof(result)
	}

	return err
}

func (h *CPHHandler) generateFuncOfRestart(source chan<- *model.Device) {
	var (
		_ctx    context.Context
		_cancel context.CancelFunc
	)
	if h.svcCtx.Config.RpcServerConf.Timeout > 0 {
		_ctx, _cancel = context.WithTimeout(
			h.ctx, time.Duration(h.svcCtx.Config.RpcServerConf.Timeout)*time.Millisecond,
		)
		defer _cancel()
	} else {
		_ctx = h.ctx
	}

	sb := h.svcCtx.DeviceModel.SelectBuilder().Where(
		squirrel.Eq{
			"`type`":  commonpb.DeviceType_CLOUD_PHONE,
			"`brand`": common.ConstBrandOfHuaweiCloud,
			"`state`": common.Idle,
		},
	)
	devices, err := h.svcCtx.DeviceModel.FindNoCacheByQuery(_ctx, sb)
	if err != nil {
		h.Errorf(
			"failed to find Huawei Cloud Phones, type: %s, platform: %s, state: %s, error: %+v",
			commonpb.DeviceType_CLOUD_PHONE.String(), common.ConstBrandOfHuaweiCloud, common.Idle, err,
		)

		return
	}

	if len(devices) == 0 {
		h.Infof("no Huawei Cloud Phones need to be restarted")
		return
	}

	for _, device := range devices {
		source <- device
	}
}

func (h *CPHHandler) mapperFuncOfRestart(item *model.Device, writer mr.Writer[*model.Device], cancel func(error)) {
	if item == nil {
		return
	}

	udid := item.Udid
	key := common.GenerateLockKeyByUDID(udid)
	fn := func() (err error) {
		device, err := h.acquireDevice(udid)
		if err != nil {
			return err
		}

		defer func() {
			err = h.releaseDevice(device.Udid, device.Token)
		}()

		return h.restartDevice(device.Udid)
	}

	if err := caller.LockWithOptionDo(
		h.svcCtx.Redis, key, fn, redislock.WithExpire(expireOfRestartSubTask),
	); err != nil {
		h.Errorf("failed to restart Huawei Cloud Phone, udid: %s, name: %s, error: %+v", udid, item.Name, err)

		return
	}

	writer.Write(item)
}

func (h *CPHHandler) reducerFuncOfRestart(pipe <-chan *model.Device, cancel func(error)) {
	count := 0
	for item := range pipe {
		if item == nil {
			continue
		}

		h.Debugf("restart Huawei Cloud Phone successfully, udid: %s, name: %s", item.Udid, item.Name)
		count += 1
	}

	if count == 1 {
		h.Infof("%d Huawei Cloud Phone has been restarted successfully", count)
	} else {
		h.Infof("%d Huawei Cloud Phones have been restarted successfully", count)
	}
}

func (h *CPHHandler) acquireDevice(udid string) (*pb.Device, error) {
	var (
		_ctx    context.Context
		_cancel context.CancelFunc
	)
	if h.svcCtx.Config.RpcServerConf.Timeout > 0 {
		_ctx, _cancel = context.WithTimeout(
			h.ctx, time.Duration(h.svcCtx.Config.RpcServerConf.Timeout)*time.Millisecond,
		)
		defer _cancel()
	} else {
		_ctx = h.ctx
	}

	out, err := deviceservicelogic.NewAcquireDeviceLogic(
		_ctx, h.svcCtx,
	).AcquireDeviceForInternal(&pb.AcquireDeviceReq{Udid: udid}, false)
	if err != nil {
		return nil, err
	}

	return out.GetDevice(), nil
}

func (h *CPHHandler) releaseDevice(udid, token string) (err error) {
	var (
		_ctx    context.Context
		_cancel context.CancelFunc
	)
	if h.svcCtx.Config.RpcServerConf.Timeout > 0 {
		_ctx, _cancel = context.WithTimeout(
			h.ctx, time.Duration(h.svcCtx.Config.RpcServerConf.Timeout)*time.Millisecond,
		)
		defer _cancel()
	} else {
		_ctx = h.ctx
	}

	_, err = deviceservicelogic.NewReleaseDeviceLogic(
		_ctx, h.svcCtx,
	).ReleaseDeviceForInternal(&pb.ReleaseDeviceReq{Udid: udid, Token: token}, false)

	return err
}

func (h *CPHHandler) restartDevice(udid string) (err error) {
	var (
		resp1 *cphmodel.RestartCloudPhoneResponse
		resp2 *cphmodel.ShowJobResponse

		manufacturer = `{"ro.product.manufacturer":"Huawei"}`
	)

	resp1, err = h.svcCtx.CPHClient.RestartCloudPhone(
		&cphmodel.RestartCloudPhoneRequest{
			Body: &cphmodel.RestartCloudPhoneRequestBody{
				Phones: []cphmodel.PhoneProperty{
					{
						PhoneId:  udid,
						Property: &manufacturer,
					},
				},
			},
		},
	)
	if err != nil {
		return err
	}
	h.Debugf("the response of `RestartCloudPhone`: %s", jsonx.MarshalToStringIgnoreError(resp1))

	if resp1.Jobs == nil || len(*resp1.Jobs) == 0 {
		return nil
	}

	job := (*resp1.Jobs)[0]
	if job.JobId == nil || *job.JobId == "" {
		return nil
	}

	// query task execution status
	exit := false
	timer := timewheel.NewTimer(expireOfRestartSubTask - time.Second)
	for !exit {
		select {
		case <-timer.C:
			h.Warnf(
				"time is up, the task to restart Huawei Cloud Phone is still not completed, udid: %s, job_id: %s",
				udid, *job.JobId,
			)

			exit = true
		default:
			resp2, err = h.svcCtx.CPHClient.ShowJob(
				&cphmodel.ShowJobRequest{
					JobId: *job.JobId,
				},
			)
			if err != nil {
				exit = true
				break
			}
			h.Debugf("the response of `ShowJob`: %s", jsonx.MarshalToStringIgnoreError(resp2))

			if resp2.Status != nil && *resp2.Status != int32(common.CPHJobStatusRunning) {
				exit = true
				break
			}

			time.Sleep(time.Second)
		}
	}

	return err
}
