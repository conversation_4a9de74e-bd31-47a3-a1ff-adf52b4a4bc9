package tasks

import (
	"context"
	"runtime/debug"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/trace"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworkerv2/base"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	deviceservicelogic "gitlab.ttyuyin.com/TestDevelopment/probe-backend/devicehub/rpc/internal/logic/deviceservice"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/devicehub/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/devicehub/rpc/pb"
)

var _ base.Handler = (*ProcessorReleaseDevice)(nil)

type HandleReleaseDeviceTaskLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewHandleReleaseDeviceTaskLogic(ctx context.Context, svcCtx *svc.ServiceContext) *HandleReleaseDeviceTaskLogic {
	return &HandleReleaseDeviceTaskLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *HandleReleaseDeviceTaskLogic) Consume(payload []byte) (err error) {
	if len(payload) == 0 {
		return nil
	}
	l.Debugf("the payload of release device task: %s", payload)

	var info pb.ReleaseDeviceReq
	if err = protobuf.UnmarshalJSON(payload, &info); err != nil {
		return errors.Wrapf(
			errorx.Err(errorx.SerializationError, err.Error()),
			"failed to unmarshal the payload of release device task, payload: %s, error: %+v",
			payload, err,
		)
	}

	_, err = deviceservicelogic.NewReleaseDeviceLogic(l.ctx, l.svcCtx).ReleaseDevice(&info)
	return err
}

type ProcessorReleaseDevice struct {
	svcCtx *svc.ServiceContext
}

func NewProcessorReleaseDevice(svcCtx *svc.ServiceContext) base.Handler {
	return &ProcessorReleaseDevice{
		svcCtx: svcCtx,
	}
}

func (p *ProcessorReleaseDevice) ProcessTask(ctx context.Context, task *base.Task) (result []byte, err error) {
	logger := logx.WithContext(ctx)
	logger.Debugf(
		"processor trace_id: %s, span_id: %s, task_name: %s",
		trace.TraceIDFromContext(ctx), trace.SpanIDFromContext(ctx), task.Typename,
	)
	defer func() {
		if r := recover(); r != nil {
			logger.Errorf("processor panic: %+v, stack: %s", r, debug.Stack())
		}
	}()

	if err = NewHandleReleaseDeviceTaskLogic(ctx, p.svcCtx).Consume(task.Payload); err != nil {
		logger.Errorf("processor err: %+v", err)
		return nil, err
	}

	return []byte(constants.SUCCESS), nil
}
