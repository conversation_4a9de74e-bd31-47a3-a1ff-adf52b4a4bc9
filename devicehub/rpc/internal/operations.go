package internal

import (
	"context"
	"time"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/proc"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/threading"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	consumerv2 "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworkerv2/consumer"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/devicehub/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/devicehub/rpc/internal/tasks"
)

const (
	timeoutOfContext = 2 * time.Second
)

func InitOperations(svcCtx *svc.ServiceContext) {
	setUpOperation(svcCtx)
	teardownOperation(svcCtx)
}

func setUpOperation(svcCtx *svc.ServiceContext) {
	registerTasksAndLaunchConsumer(svcCtx)

	updateAllDevicesToOffline(svcCtx)

	monitorDevices(svcCtx)
	handleCloudPhones(svcCtx)
	handleRealPhones(svcCtx)
}

func teardownOperation(svcCtx *svc.ServiceContext) {
	// stop the consumer when shutdown
	proc.AddShutdownListener(
		func() {
			logx.Info("stopping the devicehub consumer")
			svcCtx.DeviceHubConsumer.Stop()
		},
	)
	// stop the cron scheduler when shutdown
	proc.AddShutdownListener(svcCtx.Scheduler.Stop)
}

func registerTasksAndLaunchConsumer(svcCtx *svc.ServiceContext) {
	if err := svcCtx.DeviceHubConsumer.RegisterHandlers(
		consumerv2.NewTaskHandlerOjb(
			constants.MQTaskTypeDeviceHubHandleReleaseDevice, tasks.NewProcessorReleaseDevice(svcCtx),
		),
	); err != nil {
		logx.Errorf("failed to register tasks to device hub consumer, error: %+v", err)
	}

	threading.GoSafe(
		func() {
			logx.Info("starting the devicehub consumer")
			svcCtx.DeviceHubConsumer.Start()
		},
	)
}

func updateAllDevicesToOffline(svcCtx *svc.ServiceContext) {
	ctx, cancel := context.WithTimeout(context.Background(), timeoutOfContext)
	defer cancel()

	if err := svcCtx.DeviceModel.Trans(
		ctx, func(context context.Context, session sqlx.Session) error {
			if _, err := svcCtx.DeviceModel.UpdateAllToOffline(context, session); err != nil {
				return errors.Wrapf(
					errorx.Err(errorx.DBError, err.Error()),
					"failed to modify device, table: %s, error: %+v",
					svcCtx.DeviceModel.Table(), err,
				)
			}

			return nil
		},
	); err != nil {
		logx.Errorf("got an error while doing teardown operation, error: %+v", err)
	}
}

func monitorDevices(svcCtx *svc.ServiceContext) {
	threading.GoSafe(newMonitor(svcCtx).Watch)
}

func handleCloudPhones(svcCtx *svc.ServiceContext) {
	if _, ok := svcCtx.CPHClient.(svc.NoopCPHClient); ok {
		logx.Infof("no need to handle Huawei Cloud Phones cause by current cph client is %T", svcCtx.CPHClient)
		return
	}

	handler := newCPHHandler(svcCtx)

	// get all Huawei Cloud Phones
	if err := handler.Sync(); err != nil {
		logx.Errorf("failed to sync Huawei Cloud Phones, error: %+v", err)
	}

	if err := handler.RegisterScheduledTasks(); err != nil {
		logx.Errorf("failed to register scheduled tasks of Huawei Cloud Phones, error: %+v", err)
	}
}

func handleRealPhones(_ *svc.ServiceContext) {
	// TODO: get all real phones managed by atx android or ios provider
}
