package internal

import (
	"context"
	"testing"
	ttemplate "text/template"
	"time"

	"github.com/zeromicro/go-zero/core/conf"

	notifierpb "gitlab.ttyuyin.com/TestDevelopment/qet-backend-middleware/notifier/rpc/pb"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/devicehub/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/devicehub/rpc/internal/config"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/devicehub/rpc/internal/svc"
)

func Test_getContent(t *testing.T) {
	now := time.Now()

	type args struct {
		notifyT *ttemplate.Template
		item    *notificationItem
	}
	tests := []struct {
		name    string
		args    args
		want    string
		wantErr bool
	}{
		{
			name: "alert without callback",
			args: args{
				notifyT: alertTemplate,
				item: &notificationItem{
					PlatformType:    common.Android,
					DeviceType:      common.RealPhoneZH,
					DeviceID:        "device_id:1",
					DeviceName:      "设备1",
					PreviousState:   "IDLE",
					State:           "OFFLINE",
					PreviousStateZH: "空闲中",
					StateZH:         "已下线",
					Token:           "",
					RuleDuration:    5 * time.Minute,
					Duration:        6 * time.Minute,
					Times:           1,
					UpdatedAt:       now,
					CallbackType:    "",
				},
			},
			want:    "",
			wantErr: false,
		},
		{
			name: "alert with callback",
			args: args{
				notifyT: alertTemplate,
				item: &notificationItem{
					PlatformType:    common.Android,
					DeviceType:      common.RealPhoneZH,
					DeviceID:        "device_id:2",
					DeviceName:      "设备2",
					PreviousState:   "IDLE",
					State:           "IN_USE",
					PreviousStateZH: "空闲中",
					StateZH:         "使用中",
					Token:           "token_device_id:2",
					RuleDuration:    2 * time.Hour,
					Duration:        2 * time.Hour,
					Times:           1,
					UpdatedAt:       now,
					CallbackType:    constants.LarkCardCallbackTypeReleaseDevice,
				},
			},
			want:    "",
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				got, err := getContent(tt.args.notifyT, tt.args.item)
				if (err != nil) != tt.wantErr {
					t.Errorf("getContent() error = %v, wantErr %v", err, tt.wantErr)
					return
				}
				t.Logf("content: \n%s", got)
				//if got != tt.want {
				//	t.Errorf("getContent() got = %v, want %v", got, tt.want)
				//}
			},
		)
	}
}

func Test_notify(t *testing.T) {
	var c config.Config
	conf.MustLoad("../etc/devicehub.yaml", &c)

	svcCtx := svc.NewServiceContext(c)
	_, err := svcCtx.NotifierRPC.Notify(
		context.Background(), &notifierpb.NotifyReq{
			Items: []*notifierpb.NotifyItem{
				{
					Item: &notifierpb.NotifyItem_LarkCustomApp{
						LarkCustomApp: &notifierpb.LarkCustomApp{
							Receivers: []*notifierpb.LarkReceiver{
								{
									Id:   "oc_5623c34d6fd6f87e5b8ee04764677080",
									Type: notifierpb.LarkReceiveIdType_CHAT_ID,
								},
							},
							MsgType: notifierpb.LarkMessageType_INTERACTIVE,
							Content: `{
            "config": {},
            "i18n_elements": {
                "zh_cn": [
                    {
                        "tag": "column_set",
                        "flex_mode": "none",
                        "background_style": "default",
                        "columns": [
                            {
                                "tag": "column",
                                "width": "weighted",
                                "vertical_align": "top",
                                "elements": [
                                    {
                                        "tag": "markdown",
                                        "content": "**#️⃣ 设备ID：**device_id:2",
                                        "text_align": "left",
                                        "text_size": "normal"
                                    }
                                ],
                                "weight": 1
                            },
                            {
                                "tag": "column",
                                "width": "weighted",
                                "vertical_align": "top",
                                "elements": [
                                    {
                                        "tag": "markdown",
                                        "content": "**📱 设备名称：**设备2",
                                        "text_align": "left",
                                        "text_size": "normal"
                                    }
                                ],
                                "weight": 1
                            }
                        ],
                        "margin": "16px 0px 0px 0px"
                    },
                    {
                        "tag": "column_set",
                        "flex_mode": "none",
                        "background_style": "default",
                        "columns": [
                            {
                                "tag": "column",
                                "width": "weighted",
                                "vertical_align": "top",
                                "elements": [
                                    {
                                        "tag": "markdown",
                                        "content": "**⭕️ 前一个状态：**空闲中",
                                        "text_align": "left",
                                        "text_size": "normal"
                                    }
                                ],
                                "weight": 1
                            },
                            {
                                "tag": "column",
                                "width": "weighted",
                                "vertical_align": "top",
                                "elements": [
                                    {
                                        "tag": "markdown",
                                        "content": "**❌ 当前状态：**使用中",
                                        "text_align": "left",
                                        "text_size": "normal"
                                    }
                                ],
                                "weight": 1
                            }
                        ],
                        "margin": "16px 0px 0px 0px"
                    },
                    {
                        "tag": "column_set",
                        "flex_mode": "none",
                        "background_style": "default",
                        "columns": [
                            {
                                "tag": "column",
                                "width": "weighted",
                                "vertical_align": "top",
                                "elements": [
                                    {
                                        "tag": "markdown",
                                        "content": "**🔄 持续时间：**2h0m0s",
                                        "text_align": "left",
                                        "text_size": "normal"
                                    }
                                ],
                                "weight": 1
                            },
        					{
                                "tag": "column",
                                "width": "weighted",
                                "vertical_align": "top",
                                "elements": [
                                    {
                                        "tag": "markdown",
                                        "content": "**🔢 告警次数：**第1次",
                                        "text_align": "left",
                                        "text_size": "normal"
                                    }
                                ],
                                "weight": 1
                            }
                        ],
                        "margin": "16px 0px 0px 0px"
                    },
        			{
                        "tag": "column_set",
                        "flex_mode": "none",
                        "background_style": "default",
                        "columns": [
                            {
                                "tag": "column",
                                "width": "weighted",
                                "vertical_align": "top",
                                "elements": [
                                    {
                                        "tag": "markdown",
                                        "content": "**🕐 故障时间：**2024-09-29 16:48:15",
                                        "text_align": "left",
                                        "text_size": "normal"
                                    }
                                ],
                                "weight": 1
                            },
                            {
                                "tag": "column",
                                "width": "weighted",
                                "vertical_align": "top",
                                "elements": [
                                    {
                                        "tag": "markdown",
                                        "content": "",
                                        "text_align": "left",
                                        "text_size": "normal"
                                    }
                                ],
                                "weight": 1
                            }
                        ],
                        "margin": "16px 0px 0px 0px"
                    },
                    {
                        "tag": "hr"
                    },
                    {
                        "tag": "markdown",
                        "content": "**📋 告警内容：**\nAndroid真机「device_id:2 : 设备2」状态从「空闲中」变为「使用中」已持续2h0m0s",
                        "text_align": "left",
                        "text_size": "normal"
                    },
                    {
                        "tag": "hr"
                    },
                    {
                        "tag": "action",
                        "actions": [
                            {
                                "tag": "button",
                                "text": {
                                    "tag": "plain_text",
                                    "content": "释放设备"
                                },
                                "type": "default",
                                "width": "default",
                                "size": "medium",
                                "icon": {
                                    "tag": "standard_icon",
                                    "token": "tab-release_outlined"
                                },
                                "confirm": {
                                    "title": {
                                        "tag": "plain_text",
                                        "content": "释放设备"
                                    },
                                    "text": {
                                        "tag": "plain_text",
                                        "content": "是否确认释放设备「设备2」"
                                    }
                                },
                                "behaviors": [
                                    {
                                        "type": "callback",
                                        "value": {
                                            "type": "ReleaseDevice",
                                            "udid": "device_id:2",
                                            "token": "token_device_id:2"
                                        }
                                    }
                                ]
                            }
                        ]
                    }
                    
                ]
            },
            "i18n_header": {
                "zh_cn": {
                    "title": {
                        "tag": "plain_text",
                        "content": "设备状态为「使用中」已超过2h0m0s"
                    },
                    "subtitle": {
                        "tag": "plain_text",
                        "content": ""
                    },
                    "template": "red",
                    "ud_icon": {
                        "tag": "standard_icon",
                        "token": "alarm_outlined"
                    }
                }
            }
        }`,
						},
					},
				},
			},
		},
	)
	if err != nil {
		t.Fatal(err)
	}
}
