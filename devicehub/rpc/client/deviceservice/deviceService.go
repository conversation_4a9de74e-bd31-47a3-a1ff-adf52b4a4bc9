// Code generated by goctl. DO NOT EDIT.
// goctl 1.8.3
// Source: devicehub.proto

package deviceservice

import (
	"context"

	"github.com/zeromicro/go-zero/zrpc"
	"google.golang.org/grpc"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/devicehub/rpc/pb"
)

type (
	AcquireDeviceReq                  = pb.AcquireDeviceReq
	AcquireDeviceResp                 = pb.AcquireDeviceResp
	CreateOrModifyDeviceReq           = pb.CreateOrModifyDeviceReq
	CreateOrModifyDeviceResp          = pb.CreateOrModifyDeviceResp
	GetDeviceReq                      = pb.GetDeviceReq
	GetDeviceResp                     = pb.GetDeviceResp
	ModifyDeviceByProviderOfflineReq  = pb.ModifyDeviceByProviderOfflineReq
	ModifyDeviceByProviderOfflineResp = pb.ModifyDeviceByProviderOfflineResp
	ReleaseDeviceReq                  = pb.ReleaseDeviceReq
	ReleaseDeviceResp                 = pb.ReleaseDeviceResp
	SearchAcquireDeviceReq            = pb.SearchAcquireDeviceReq
	SearchAcquireDeviceResp           = pb.SearchAcquireDeviceResp
	SearchDeviceReq                   = pb.SearchDeviceReq
	SearchDeviceResp                  = pb.SearchDeviceResp

	DeviceService interface {
		// CreateOrModifyDevice 创建或编辑设备
		CreateOrModifyDevice(ctx context.Context, in *CreateOrModifyDeviceReq, opts ...grpc.CallOption) (*CreateOrModifyDeviceResp, error)
		// ModifyDeviceByProviderOffline 编辑下线的`provider`管理的设备
		ModifyDeviceByProviderOffline(ctx context.Context, in *ModifyDeviceByProviderOfflineReq, opts ...grpc.CallOption) (*ModifyDeviceByProviderOfflineResp, error)
		// GetDevice 获取设备（通过`udid`）
		GetDevice(ctx context.Context, in *GetDeviceReq, opts ...grpc.CallOption) (*GetDeviceResp, error)
		// SearchDevice 搜索设备
		SearchDevice(ctx context.Context, in *SearchDeviceReq, opts ...grpc.CallOption) (*SearchDeviceResp, error)
		// AcquireDevice 占用设备（通过`udid`）
		AcquireDevice(ctx context.Context, in *AcquireDeviceReq, opts ...grpc.CallOption) (*AcquireDeviceResp, error)
		// SearchAcquireDevice 占用设备（通过查询条件）
		SearchAcquireDevice(ctx context.Context, in *SearchAcquireDeviceReq, opts ...grpc.CallOption) (*SearchAcquireDeviceResp, error)
		// ReleaseDevice 释放设备（通过`udid`）
		ReleaseDevice(ctx context.Context, in *ReleaseDeviceReq, opts ...grpc.CallOption) (*ReleaseDeviceResp, error)
	}

	defaultDeviceService struct {
		cli zrpc.Client
	}
)

func NewDeviceService(cli zrpc.Client) DeviceService {
	return &defaultDeviceService{
		cli: cli,
	}
}

// CreateOrModifyDevice 创建或编辑设备
func (m *defaultDeviceService) CreateOrModifyDevice(ctx context.Context, in *CreateOrModifyDeviceReq, opts ...grpc.CallOption) (*CreateOrModifyDeviceResp, error) {
	client := pb.NewDeviceServiceClient(m.cli.Conn())
	return client.CreateOrModifyDevice(ctx, in, opts...)
}

// ModifyDeviceByProviderOffline 编辑下线的`provider`管理的设备
func (m *defaultDeviceService) ModifyDeviceByProviderOffline(ctx context.Context, in *ModifyDeviceByProviderOfflineReq, opts ...grpc.CallOption) (*ModifyDeviceByProviderOfflineResp, error) {
	client := pb.NewDeviceServiceClient(m.cli.Conn())
	return client.ModifyDeviceByProviderOffline(ctx, in, opts...)
}

// GetDevice 获取设备（通过`udid`）
func (m *defaultDeviceService) GetDevice(ctx context.Context, in *GetDeviceReq, opts ...grpc.CallOption) (*GetDeviceResp, error) {
	client := pb.NewDeviceServiceClient(m.cli.Conn())
	return client.GetDevice(ctx, in, opts...)
}

// SearchDevice 搜索设备
func (m *defaultDeviceService) SearchDevice(ctx context.Context, in *SearchDeviceReq, opts ...grpc.CallOption) (*SearchDeviceResp, error) {
	client := pb.NewDeviceServiceClient(m.cli.Conn())
	return client.SearchDevice(ctx, in, opts...)
}

// AcquireDevice 占用设备（通过`udid`）
func (m *defaultDeviceService) AcquireDevice(ctx context.Context, in *AcquireDeviceReq, opts ...grpc.CallOption) (*AcquireDeviceResp, error) {
	client := pb.NewDeviceServiceClient(m.cli.Conn())
	return client.AcquireDevice(ctx, in, opts...)
}

// SearchAcquireDevice 占用设备（通过查询条件）
func (m *defaultDeviceService) SearchAcquireDevice(ctx context.Context, in *SearchAcquireDeviceReq, opts ...grpc.CallOption) (*SearchAcquireDeviceResp, error) {
	client := pb.NewDeviceServiceClient(m.cli.Conn())
	return client.SearchAcquireDevice(ctx, in, opts...)
}

// ReleaseDevice 释放设备（通过`udid`）
func (m *defaultDeviceService) ReleaseDevice(ctx context.Context, in *ReleaseDeviceReq, opts ...grpc.CallOption) (*ReleaseDeviceResp, error) {
	client := pb.NewDeviceServiceClient(m.cli.Conn())
	return client.ReleaseDevice(ctx, in, opts...)
}
