// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.1
// 	protoc        v5.29.2
// source: devicehub/devicehub.proto

package pb

import (
	reflect "reflect"
	sync "sync"

	_ "buf.build/gen/go/bufbuild/protovalidate/protocolbuffers/go/buf/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"

	pb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
	rpc "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sqlbuilder/rpc"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type CreateOrModifyDeviceReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Udid          string                 `protobuf:"bytes,1,opt,name=udid,proto3" json:"udid,omitempty"`                                                                   // 设备编号
	Name          string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`                                                                   // 设备名称
	Type          pb.DeviceType          `protobuf:"varint,3,opt,name=type,proto3,enum=common.DeviceType" json:"type,omitempty"`                                           // 设备类型
	Platform      pb.PlatformType        `protobuf:"varint,4,opt,name=platform,proto3,enum=common.PlatformType" json:"platform,omitempty"`                                 // 平台
	Brand         string                 `protobuf:"bytes,5,opt,name=brand,proto3" json:"brand,omitempty"`                                                                 // 品牌
	Model         string                 `protobuf:"bytes,6,opt,name=model,proto3" json:"model,omitempty"`                                                                 // 型号
	Serial        string                 `protobuf:"bytes,7,opt,name=serial,proto3" json:"serial,omitempty"`                                                               // 序列号
	Version       string                 `protobuf:"bytes,8,opt,name=version,proto3" json:"version,omitempty"`                                                             // 版本
	Provider      string                 `protobuf:"bytes,9,opt,name=provider,proto3" json:"provider,omitempty"`                                                           // 设备所属
	ProviderType  ProviderType           `protobuf:"varint,10,opt,name=provider_type,json=providerType,proto3,enum=devicehub.ProviderType" json:"provider_type,omitempty"` // 设备所属类型
	RemoteAddress string                 `protobuf:"bytes,11,opt,name=remote_address,json=remoteAddress,proto3" json:"remote_address,omitempty"`                           // 远程连接地址
	State         DeviceState            `protobuf:"varint,12,opt,name=state,proto3,enum=devicehub.DeviceState" json:"state,omitempty"`                                    // 状态
	Metadata      *DeviceMetadata        `protobuf:"bytes,13,opt,name=metadata,proto3" json:"metadata,omitempty"`                                                          // 设备元数据
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateOrModifyDeviceReq) Reset() {
	*x = CreateOrModifyDeviceReq{}
	mi := &file_devicehub_devicehub_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateOrModifyDeviceReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateOrModifyDeviceReq) ProtoMessage() {}

func (x *CreateOrModifyDeviceReq) ProtoReflect() protoreflect.Message {
	mi := &file_devicehub_devicehub_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateOrModifyDeviceReq.ProtoReflect.Descriptor instead.
func (*CreateOrModifyDeviceReq) Descriptor() ([]byte, []int) {
	return file_devicehub_devicehub_proto_rawDescGZIP(), []int{0}
}

func (x *CreateOrModifyDeviceReq) GetUdid() string {
	if x != nil {
		return x.Udid
	}
	return ""
}

func (x *CreateOrModifyDeviceReq) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CreateOrModifyDeviceReq) GetType() pb.DeviceType {
	if x != nil {
		return x.Type
	}
	return pb.DeviceType(0)
}

func (x *CreateOrModifyDeviceReq) GetPlatform() pb.PlatformType {
	if x != nil {
		return x.Platform
	}
	return pb.PlatformType(0)
}

func (x *CreateOrModifyDeviceReq) GetBrand() string {
	if x != nil {
		return x.Brand
	}
	return ""
}

func (x *CreateOrModifyDeviceReq) GetModel() string {
	if x != nil {
		return x.Model
	}
	return ""
}

func (x *CreateOrModifyDeviceReq) GetSerial() string {
	if x != nil {
		return x.Serial
	}
	return ""
}

func (x *CreateOrModifyDeviceReq) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *CreateOrModifyDeviceReq) GetProvider() string {
	if x != nil {
		return x.Provider
	}
	return ""
}

func (x *CreateOrModifyDeviceReq) GetProviderType() ProviderType {
	if x != nil {
		return x.ProviderType
	}
	return ProviderType_PT_NULL
}

func (x *CreateOrModifyDeviceReq) GetRemoteAddress() string {
	if x != nil {
		return x.RemoteAddress
	}
	return ""
}

func (x *CreateOrModifyDeviceReq) GetState() DeviceState {
	if x != nil {
		return x.State
	}
	return DeviceState_DS_NULL
}

func (x *CreateOrModifyDeviceReq) GetMetadata() *DeviceMetadata {
	if x != nil {
		return x.Metadata
	}
	return nil
}

type CreateOrModifyDeviceResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateOrModifyDeviceResp) Reset() {
	*x = CreateOrModifyDeviceResp{}
	mi := &file_devicehub_devicehub_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateOrModifyDeviceResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateOrModifyDeviceResp) ProtoMessage() {}

func (x *CreateOrModifyDeviceResp) ProtoReflect() protoreflect.Message {
	mi := &file_devicehub_devicehub_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateOrModifyDeviceResp.ProtoReflect.Descriptor instead.
func (*CreateOrModifyDeviceResp) Descriptor() ([]byte, []int) {
	return file_devicehub_devicehub_proto_rawDescGZIP(), []int{1}
}

type ModifyDeviceByProviderOfflineReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Provider      string                 `protobuf:"bytes,1,opt,name=provider,proto3" json:"provider,omitempty"`                                                          // 设备所属
	ProviderType  ProviderType           `protobuf:"varint,2,opt,name=provider_type,json=providerType,proto3,enum=devicehub.ProviderType" json:"provider_type,omitempty"` // 设备所属类型
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ModifyDeviceByProviderOfflineReq) Reset() {
	*x = ModifyDeviceByProviderOfflineReq{}
	mi := &file_devicehub_devicehub_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ModifyDeviceByProviderOfflineReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModifyDeviceByProviderOfflineReq) ProtoMessage() {}

func (x *ModifyDeviceByProviderOfflineReq) ProtoReflect() protoreflect.Message {
	mi := &file_devicehub_devicehub_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModifyDeviceByProviderOfflineReq.ProtoReflect.Descriptor instead.
func (*ModifyDeviceByProviderOfflineReq) Descriptor() ([]byte, []int) {
	return file_devicehub_devicehub_proto_rawDescGZIP(), []int{2}
}

func (x *ModifyDeviceByProviderOfflineReq) GetProvider() string {
	if x != nil {
		return x.Provider
	}
	return ""
}

func (x *ModifyDeviceByProviderOfflineReq) GetProviderType() ProviderType {
	if x != nil {
		return x.ProviderType
	}
	return ProviderType_PT_NULL
}

type ModifyDeviceByProviderOfflineResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ModifyDeviceByProviderOfflineResp) Reset() {
	*x = ModifyDeviceByProviderOfflineResp{}
	mi := &file_devicehub_devicehub_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ModifyDeviceByProviderOfflineResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModifyDeviceByProviderOfflineResp) ProtoMessage() {}

func (x *ModifyDeviceByProviderOfflineResp) ProtoReflect() protoreflect.Message {
	mi := &file_devicehub_devicehub_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModifyDeviceByProviderOfflineResp.ProtoReflect.Descriptor instead.
func (*ModifyDeviceByProviderOfflineResp) Descriptor() ([]byte, []int) {
	return file_devicehub_devicehub_proto_rawDescGZIP(), []int{3}
}

type GetDeviceReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Udid          string                 `protobuf:"bytes,1,opt,name=udid,proto3" json:"udid,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetDeviceReq) Reset() {
	*x = GetDeviceReq{}
	mi := &file_devicehub_devicehub_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetDeviceReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDeviceReq) ProtoMessage() {}

func (x *GetDeviceReq) ProtoReflect() protoreflect.Message {
	mi := &file_devicehub_devicehub_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDeviceReq.ProtoReflect.Descriptor instead.
func (*GetDeviceReq) Descriptor() ([]byte, []int) {
	return file_devicehub_devicehub_proto_rawDescGZIP(), []int{4}
}

func (x *GetDeviceReq) GetUdid() string {
	if x != nil {
		return x.Udid
	}
	return ""
}

type GetDeviceResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Device        *Device                `protobuf:"bytes,1,opt,name=device,proto3" json:"device,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetDeviceResp) Reset() {
	*x = GetDeviceResp{}
	mi := &file_devicehub_devicehub_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetDeviceResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDeviceResp) ProtoMessage() {}

func (x *GetDeviceResp) ProtoReflect() protoreflect.Message {
	mi := &file_devicehub_devicehub_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDeviceResp.ProtoReflect.Descriptor instead.
func (*GetDeviceResp) Descriptor() ([]byte, []int) {
	return file_devicehub_devicehub_proto_rawDescGZIP(), []int{5}
}

func (x *GetDeviceResp) GetDevice() *Device {
	if x != nil {
		return x.Device
	}
	return nil
}

type SearchDeviceReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Condition     *rpc.Condition         `protobuf:"bytes,1,opt,name=condition,proto3" json:"condition,omitempty"`   // 查询条件
	Pagination    *rpc.Pagination        `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"` // 查询分页
	Sort          []*rpc.SortField       `protobuf:"bytes,3,rep,name=sort,proto3" json:"sort,omitempty"`             // 查询排序
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SearchDeviceReq) Reset() {
	*x = SearchDeviceReq{}
	mi := &file_devicehub_devicehub_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SearchDeviceReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchDeviceReq) ProtoMessage() {}

func (x *SearchDeviceReq) ProtoReflect() protoreflect.Message {
	mi := &file_devicehub_devicehub_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchDeviceReq.ProtoReflect.Descriptor instead.
func (*SearchDeviceReq) Descriptor() ([]byte, []int) {
	return file_devicehub_devicehub_proto_rawDescGZIP(), []int{6}
}

func (x *SearchDeviceReq) GetCondition() *rpc.Condition {
	if x != nil {
		return x.Condition
	}
	return nil
}

func (x *SearchDeviceReq) GetPagination() *rpc.Pagination {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *SearchDeviceReq) GetSort() []*rpc.SortField {
	if x != nil {
		return x.Sort
	}
	return nil
}

type SearchDeviceResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	CurrentPage   uint64                 `protobuf:"varint,1,opt,name=current_page,json=currentPage,proto3" json:"current_page,omitempty"`
	PageSize      uint64                 `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	TotalCount    uint64                 `protobuf:"varint,3,opt,name=total_count,json=totalCount,proto3" json:"total_count,omitempty"`
	TotalPage     uint64                 `protobuf:"varint,4,opt,name=total_page,json=totalPage,proto3" json:"total_page,omitempty"`
	Items         []*Device              `protobuf:"bytes,5,rep,name=items,proto3" json:"items,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SearchDeviceResp) Reset() {
	*x = SearchDeviceResp{}
	mi := &file_devicehub_devicehub_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SearchDeviceResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchDeviceResp) ProtoMessage() {}

func (x *SearchDeviceResp) ProtoReflect() protoreflect.Message {
	mi := &file_devicehub_devicehub_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchDeviceResp.ProtoReflect.Descriptor instead.
func (*SearchDeviceResp) Descriptor() ([]byte, []int) {
	return file_devicehub_devicehub_proto_rawDescGZIP(), []int{7}
}

func (x *SearchDeviceResp) GetCurrentPage() uint64 {
	if x != nil {
		return x.CurrentPage
	}
	return 0
}

func (x *SearchDeviceResp) GetPageSize() uint64 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *SearchDeviceResp) GetTotalCount() uint64 {
	if x != nil {
		return x.TotalCount
	}
	return 0
}

func (x *SearchDeviceResp) GetTotalPage() uint64 {
	if x != nil {
		return x.TotalPage
	}
	return 0
}

func (x *SearchDeviceResp) GetItems() []*Device {
	if x != nil {
		return x.Items
	}
	return nil
}

type AcquireDeviceReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Udid          string                 `protobuf:"bytes,1,opt,name=udid,proto3" json:"udid,omitempty"`              // 设备编号
	Expiration    int64                  `protobuf:"varint,2,opt,name=expiration,proto3" json:"expiration,omitempty"` // 最大占用时长（秒）
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AcquireDeviceReq) Reset() {
	*x = AcquireDeviceReq{}
	mi := &file_devicehub_devicehub_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AcquireDeviceReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AcquireDeviceReq) ProtoMessage() {}

func (x *AcquireDeviceReq) ProtoReflect() protoreflect.Message {
	mi := &file_devicehub_devicehub_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AcquireDeviceReq.ProtoReflect.Descriptor instead.
func (*AcquireDeviceReq) Descriptor() ([]byte, []int) {
	return file_devicehub_devicehub_proto_rawDescGZIP(), []int{8}
}

func (x *AcquireDeviceReq) GetUdid() string {
	if x != nil {
		return x.Udid
	}
	return ""
}

func (x *AcquireDeviceReq) GetExpiration() int64 {
	if x != nil {
		return x.Expiration
	}
	return 0
}

type AcquireDeviceResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Device        *Device                `protobuf:"bytes,1,opt,name=device,proto3" json:"device,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AcquireDeviceResp) Reset() {
	*x = AcquireDeviceResp{}
	mi := &file_devicehub_devicehub_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AcquireDeviceResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AcquireDeviceResp) ProtoMessage() {}

func (x *AcquireDeviceResp) ProtoReflect() protoreflect.Message {
	mi := &file_devicehub_devicehub_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AcquireDeviceResp.ProtoReflect.Descriptor instead.
func (*AcquireDeviceResp) Descriptor() ([]byte, []int) {
	return file_devicehub_devicehub_proto_rawDescGZIP(), []int{9}
}

func (x *AcquireDeviceResp) GetDevice() *Device {
	if x != nil {
		return x.Device
	}
	return nil
}

type SearchAcquireDeviceReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Condition     *rpc.Condition         `protobuf:"bytes,1,opt,name=condition,proto3" json:"condition,omitempty"`    // 查询条件
	Count         uint32                 `protobuf:"varint,2,opt,name=count,proto3" json:"count,omitempty"`           // 申请占用的设备数量
	Expiration    int64                  `protobuf:"varint,3,opt,name=expiration,proto3" json:"expiration,omitempty"` // 最大占用时长（秒）
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SearchAcquireDeviceReq) Reset() {
	*x = SearchAcquireDeviceReq{}
	mi := &file_devicehub_devicehub_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SearchAcquireDeviceReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchAcquireDeviceReq) ProtoMessage() {}

func (x *SearchAcquireDeviceReq) ProtoReflect() protoreflect.Message {
	mi := &file_devicehub_devicehub_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchAcquireDeviceReq.ProtoReflect.Descriptor instead.
func (*SearchAcquireDeviceReq) Descriptor() ([]byte, []int) {
	return file_devicehub_devicehub_proto_rawDescGZIP(), []int{10}
}

func (x *SearchAcquireDeviceReq) GetCondition() *rpc.Condition {
	if x != nil {
		return x.Condition
	}
	return nil
}

func (x *SearchAcquireDeviceReq) GetCount() uint32 {
	if x != nil {
		return x.Count
	}
	return 0
}

func (x *SearchAcquireDeviceReq) GetExpiration() int64 {
	if x != nil {
		return x.Expiration
	}
	return 0
}

type SearchAcquireDeviceResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Devices       []*Device              `protobuf:"bytes,1,rep,name=devices,proto3" json:"devices,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SearchAcquireDeviceResp) Reset() {
	*x = SearchAcquireDeviceResp{}
	mi := &file_devicehub_devicehub_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SearchAcquireDeviceResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchAcquireDeviceResp) ProtoMessage() {}

func (x *SearchAcquireDeviceResp) ProtoReflect() protoreflect.Message {
	mi := &file_devicehub_devicehub_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchAcquireDeviceResp.ProtoReflect.Descriptor instead.
func (*SearchAcquireDeviceResp) Descriptor() ([]byte, []int) {
	return file_devicehub_devicehub_proto_rawDescGZIP(), []int{11}
}

func (x *SearchAcquireDeviceResp) GetDevices() []*Device {
	if x != nil {
		return x.Devices
	}
	return nil
}

type ReleaseDeviceReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Udid          string                 `protobuf:"bytes,1,opt,name=udid,proto3" json:"udid,omitempty"`
	Token         string                 `protobuf:"bytes,2,opt,name=token,proto3" json:"token,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ReleaseDeviceReq) Reset() {
	*x = ReleaseDeviceReq{}
	mi := &file_devicehub_devicehub_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ReleaseDeviceReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReleaseDeviceReq) ProtoMessage() {}

func (x *ReleaseDeviceReq) ProtoReflect() protoreflect.Message {
	mi := &file_devicehub_devicehub_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReleaseDeviceReq.ProtoReflect.Descriptor instead.
func (*ReleaseDeviceReq) Descriptor() ([]byte, []int) {
	return file_devicehub_devicehub_proto_rawDescGZIP(), []int{12}
}

func (x *ReleaseDeviceReq) GetUdid() string {
	if x != nil {
		return x.Udid
	}
	return ""
}

func (x *ReleaseDeviceReq) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

type ReleaseDeviceResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ReleaseDeviceResp) Reset() {
	*x = ReleaseDeviceResp{}
	mi := &file_devicehub_devicehub_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ReleaseDeviceResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReleaseDeviceResp) ProtoMessage() {}

func (x *ReleaseDeviceResp) ProtoReflect() protoreflect.Message {
	mi := &file_devicehub_devicehub_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReleaseDeviceResp.ProtoReflect.Descriptor instead.
func (*ReleaseDeviceResp) Descriptor() ([]byte, []int) {
	return file_devicehub_devicehub_proto_rawDescGZIP(), []int{13}
}

var File_devicehub_devicehub_proto protoreflect.FileDescriptor

var file_devicehub_devicehub_proto_rawDesc = []byte{
	0x0a, 0x19, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x68, 0x75, 0x62, 0x2f, 0x64, 0x65, 0x76, 0x69,
	0x63, 0x65, 0x68, 0x75, 0x62, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x09, 0x64, 0x65, 0x76,
	0x69, 0x63, 0x65, 0x68, 0x75, 0x62, 0x1a, 0x1b, 0x62, 0x75, 0x66, 0x2f, 0x76, 0x61, 0x6c, 0x69,
	0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x73, 0x71, 0x6c, 0x62, 0x75, 0x69, 0x6c, 0x64, 0x65, 0x72, 0x2f,
	0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x11, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x16, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x68, 0x75, 0x62, 0x2f, 0x64, 0x65, 0x76, 0x69, 0x63,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xe7, 0x08, 0x0a, 0x17, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x4f, 0x72, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65,
	0x52, 0x65, 0x71, 0x12, 0x1d, 0x0a, 0x04, 0x75, 0x64, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x09, 0xba, 0x48, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x40, 0x52, 0x04, 0x75, 0x64,
	0x69, 0x64, 0x12, 0x20, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x0c, 0xba, 0x48, 0x09, 0xd8, 0x01, 0x01, 0x72, 0x04, 0x10, 0x01, 0x18, 0x40, 0x52, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x12, 0x5e, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x12, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x44, 0x65, 0x76, 0x69,
	0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x42, 0x36, 0xba, 0x48, 0x33, 0xba, 0x01, 0x30, 0x0a, 0x04,
	0x74, 0x79, 0x70, 0x65, 0x12, 0x1d, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x20, 0x74, 0x79, 0x70,
	0x65, 0x20, 0x6d, 0x75, 0x73, 0x74, 0x20, 0x62, 0x65, 0x20, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66,
	0x69, 0x65, 0x64, 0x1a, 0x09, 0x74, 0x68, 0x69, 0x73, 0x20, 0x21, 0x3d, 0x20, 0x30, 0x52, 0x04,
	0x74, 0x79, 0x70, 0x65, 0x12, 0x70, 0x0a, 0x08, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e,
	0x50, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x42, 0x3e, 0xba, 0x48,
	0x3b, 0xba, 0x01, 0x38, 0x0a, 0x08, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x12, 0x21,
	0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x20, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x20,
	0x6d, 0x75, 0x73, 0x74, 0x20, 0x62, 0x65, 0x20, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x65,
	0x64, 0x1a, 0x09, 0x74, 0x68, 0x69, 0x73, 0x20, 0x21, 0x3d, 0x20, 0x30, 0x52, 0x08, 0x70, 0x6c,
	0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x12, 0x22, 0x0a, 0x05, 0x62, 0x72, 0x61, 0x6e, 0x64, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0c, 0xba, 0x48, 0x09, 0xd8, 0x01, 0x01, 0x72, 0x04, 0x10,
	0x01, 0x18, 0x40, 0x52, 0x05, 0x62, 0x72, 0x61, 0x6e, 0x64, 0x12, 0x22, 0x0a, 0x05, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0c, 0xba, 0x48, 0x09, 0xd8, 0x01,
	0x01, 0x72, 0x04, 0x10, 0x01, 0x18, 0x40, 0x52, 0x05, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x24,
	0x0a, 0x06, 0x73, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0c,
	0xba, 0x48, 0x09, 0xd8, 0x01, 0x01, 0x72, 0x04, 0x10, 0x01, 0x18, 0x40, 0x52, 0x06, 0x73, 0x65,
	0x72, 0x69, 0x61, 0x6c, 0x12, 0x26, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0c, 0xba, 0x48, 0x09, 0xd8, 0x01, 0x01, 0x72, 0x04, 0x10,
	0x01, 0x18, 0x40, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x29, 0x0a, 0x08,
	0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0d,
	0xba, 0x48, 0x0a, 0xd8, 0x01, 0x01, 0x72, 0x05, 0x10, 0x01, 0x18, 0x80, 0x01, 0x52, 0x08, 0x70,
	0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x12, 0x7f, 0x0a, 0x0d, 0x70, 0x72, 0x6f, 0x76, 0x69,
	0x64, 0x65, 0x72, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x17,
	0x2e, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x68, 0x75, 0x62, 0x2e, 0x50, 0x72, 0x6f, 0x76, 0x69,
	0x64, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x42, 0x41, 0xba, 0x48, 0x3e, 0xba, 0x01, 0x3b, 0x0a,
	0x0d, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x12, 0x1f,
	0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x20, 0x74, 0x79, 0x70, 0x65, 0x20, 0x6d, 0x75,
	0x73, 0x74, 0x20, 0x62, 0x65, 0x20, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x65, 0x64, 0x1a,
	0x09, 0x74, 0x68, 0x69, 0x73, 0x20, 0x21, 0x3d, 0x20, 0x30, 0x52, 0x0c, 0x70, 0x72, 0x6f, 0x76,
	0x69, 0x64, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x12, 0x34, 0x0a, 0x0e, 0x72, 0x65, 0x6d, 0x6f,
	0x74, 0x65, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x0d, 0xba, 0x48, 0x0a, 0xd8, 0x01, 0x01, 0x72, 0x05, 0x10, 0x01, 0x18, 0x80, 0x01, 0x52,
	0x0d, 0x72, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x5f,
	0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x16, 0x2e,
	0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x68, 0x75, 0x62, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65,
	0x53, 0x74, 0x61, 0x74, 0x65, 0x42, 0x31, 0xba, 0x48, 0x2e, 0xba, 0x01, 0x2b, 0x0a, 0x05, 0x73,
	0x74, 0x61, 0x74, 0x65, 0x12, 0x17, 0x73, 0x74, 0x61, 0x74, 0x65, 0x20, 0x6d, 0x75, 0x73, 0x74,
	0x20, 0x62, 0x65, 0x20, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x65, 0x64, 0x1a, 0x09, 0x74,
	0x68, 0x69, 0x73, 0x20, 0x21, 0x3d, 0x20, 0x30, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x12,
	0x3a, 0x0a, 0x08, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x18, 0x0d, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x19, 0x2e, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x68, 0x75, 0x62, 0x2e, 0x44, 0x65,
	0x76, 0x69, 0x63, 0x65, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x42, 0x03, 0xba, 0x48,
	0x00, 0x52, 0x08, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x3a, 0xa3, 0x02, 0xba, 0x48,
	0x9f, 0x02, 0x1a, 0x9c, 0x02, 0x0a, 0x20, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x6d, 0x65,
	0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x77, 0x69, 0x74, 0x68, 0x5f, 0x63, 0x70, 0x68, 0x5f,
	0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x56, 0x74, 0x68, 0x65, 0x20, 0x64, 0x65, 0x76,
	0x69, 0x63, 0x65, 0x20, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x20, 0x6d, 0x75, 0x73,
	0x74, 0x20, 0x68, 0x61, 0x76, 0x65, 0x20, 0x60, 0x63, 0x70, 0x68, 0x5f, 0x61, 0x64, 0x64, 0x72,
	0x65, 0x73, 0x73, 0x60, 0x20, 0x77, 0x68, 0x65, 0x6e, 0x20, 0x69, 0x74, 0x20, 0x69, 0x73, 0x20,
	0x61, 0x20, 0x48, 0x55, 0x41, 0x57, 0x45, 0x49, 0x20, 0x43, 0x6c, 0x6f, 0x75, 0x64, 0x20, 0x50,
	0x68, 0x6f, 0x6e, 0x65, 0x20, 0x6f, 0x66, 0x20, 0x41, 0x6e, 0x64, 0x72, 0x6f, 0x69, 0x64, 0x1a,
	0x9f, 0x01, 0x74, 0x68, 0x69, 0x73, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x20, 0x3d, 0x3d, 0x20, 0x32,
	0x20, 0x26, 0x26, 0x20, 0x74, 0x68, 0x69, 0x73, 0x2e, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72,
	0x6d, 0x20, 0x3d, 0x3d, 0x20, 0x31, 0x20, 0x26, 0x26, 0x20, 0x74, 0x68, 0x69, 0x73, 0x2e, 0x62,
	0x72, 0x61, 0x6e, 0x64, 0x20, 0x3d, 0x3d, 0x20, 0x27, 0x48, 0x55, 0x41, 0x57, 0x45, 0x49, 0x43,
	0x4c, 0x4f, 0x55, 0x44, 0x27, 0x20, 0x3f, 0x20, 0x28, 0x68, 0x61, 0x73, 0x28, 0x74, 0x68, 0x69,
	0x73, 0x2e, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x2e, 0x63, 0x70, 0x68, 0x5f, 0x61,
	0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x29, 0x20, 0x3f, 0x20, 0x27, 0x27, 0x20, 0x3a, 0x20, 0x27,
	0x48, 0x55, 0x41, 0x57, 0x45, 0x49, 0x20, 0x43, 0x6c, 0x6f, 0x75, 0x64, 0x20, 0x50, 0x68, 0x6f,
	0x6e, 0x65, 0x20, 0x6d, 0x75, 0x73, 0x74, 0x20, 0x68, 0x61, 0x76, 0x65, 0x20, 0x60, 0x63, 0x70,
	0x68, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x60, 0x27, 0x29, 0x20, 0x3a, 0x20, 0x27,
	0x27, 0x22, 0x1a, 0x0a, 0x18, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4f, 0x72, 0x4d, 0x6f, 0x64,
	0x69, 0x66, 0x79, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x52, 0x65, 0x73, 0x70, 0x22, 0xcb, 0x01,
	0x0a, 0x20, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x42, 0x79,
	0x50, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x4f, 0x66, 0x66, 0x6c, 0x69, 0x6e, 0x65, 0x52,
	0x65, 0x71, 0x12, 0x26, 0x0a, 0x08, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x0a, 0xba, 0x48, 0x07, 0x72, 0x05, 0x10, 0x01, 0x18, 0x80, 0x01,
	0x52, 0x08, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x12, 0x7f, 0x0a, 0x0d, 0x70, 0x72,
	0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x17, 0x2e, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x68, 0x75, 0x62, 0x2e, 0x50, 0x72,
	0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x42, 0x41, 0xba, 0x48, 0x3e, 0xba,
	0x01, 0x3b, 0x0a, 0x0d, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x12, 0x1f, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x20, 0x74, 0x79, 0x70, 0x65,
	0x20, 0x6d, 0x75, 0x73, 0x74, 0x20, 0x62, 0x65, 0x20, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69,
	0x65, 0x64, 0x1a, 0x09, 0x74, 0x68, 0x69, 0x73, 0x20, 0x21, 0x3d, 0x20, 0x30, 0x52, 0x0c, 0x70,
	0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x22, 0x23, 0x0a, 0x21, 0x4d,
	0x6f, 0x64, 0x69, 0x66, 0x79, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x42, 0x79, 0x50, 0x72, 0x6f,
	0x76, 0x69, 0x64, 0x65, 0x72, 0x4f, 0x66, 0x66, 0x6c, 0x69, 0x6e, 0x65, 0x52, 0x65, 0x73, 0x70,
	0x22, 0x2d, 0x0a, 0x0c, 0x47, 0x65, 0x74, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x52, 0x65, 0x71,
	0x12, 0x1d, 0x0a, 0x04, 0x75, 0x64, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09,
	0xba, 0x48, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x40, 0x52, 0x04, 0x75, 0x64, 0x69, 0x64, 0x22,
	0x3a, 0x0a, 0x0d, 0x47, 0x65, 0x74, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x52, 0x65, 0x73, 0x70,
	0x12, 0x29, 0x0a, 0x06, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x11, 0x2e, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x68, 0x75, 0x62, 0x2e, 0x44, 0x65, 0x76,
	0x69, 0x63, 0x65, 0x52, 0x06, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x22, 0xb6, 0x01, 0x0a, 0x0f,
	0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x52, 0x65, 0x71, 0x12,
	0x33, 0x0a, 0x09, 0x63, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x15, 0x2e, 0x73, 0x71, 0x6c, 0x62, 0x75, 0x69, 0x6c, 0x64, 0x65, 0x72, 0x2e,
	0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x09, 0x63, 0x6f, 0x6e, 0x64, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x36, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x73, 0x71, 0x6c, 0x62, 0x75,
	0x69, 0x6c, 0x64, 0x65, 0x72, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x52, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x36, 0x0a, 0x04,
	0x73, 0x6f, 0x72, 0x74, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x73, 0x71, 0x6c,
	0x62, 0x75, 0x69, 0x6c, 0x64, 0x65, 0x72, 0x2e, 0x53, 0x6f, 0x72, 0x74, 0x46, 0x69, 0x65, 0x6c,
	0x64, 0x42, 0x0b, 0xba, 0x48, 0x08, 0x92, 0x01, 0x05, 0x22, 0x03, 0xd8, 0x01, 0x01, 0x52, 0x04,
	0x73, 0x6f, 0x72, 0x74, 0x22, 0xbb, 0x01, 0x0a, 0x10, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x44,
	0x65, 0x76, 0x69, 0x63, 0x65, 0x52, 0x65, 0x73, 0x70, 0x12, 0x21, 0x0a, 0x0c, 0x63, 0x75, 0x72,
	0x72, 0x65, 0x6e, 0x74, 0x5f, 0x70, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52,
	0x0b, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x50, 0x61, 0x67, 0x65, 0x12, 0x1b, 0x0a, 0x09,
	0x70, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52,
	0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x74, 0x6f, 0x74,
	0x61, 0x6c, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0a,
	0x74, 0x6f, 0x74, 0x61, 0x6c, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x74, 0x6f,
	0x74, 0x61, 0x6c, 0x5f, 0x70, 0x61, 0x67, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x04, 0x52, 0x09,
	0x74, 0x6f, 0x74, 0x61, 0x6c, 0x50, 0x61, 0x67, 0x65, 0x12, 0x27, 0x0a, 0x05, 0x69, 0x74, 0x65,
	0x6d, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x64, 0x65, 0x76, 0x69, 0x63,
	0x65, 0x68, 0x75, 0x62, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x52, 0x05, 0x69, 0x74, 0x65,
	0x6d, 0x73, 0x22, 0x5a, 0x0a, 0x10, 0x41, 0x63, 0x71, 0x75, 0x69, 0x72, 0x65, 0x44, 0x65, 0x76,
	0x69, 0x63, 0x65, 0x52, 0x65, 0x71, 0x12, 0x1d, 0x0a, 0x04, 0x75, 0x64, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xba, 0x48, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x40, 0x52,
	0x04, 0x75, 0x64, 0x69, 0x64, 0x12, 0x27, 0x0a, 0x0a, 0x65, 0x78, 0x70, 0x69, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xba, 0x48, 0x04, 0x22, 0x02,
	0x28, 0x00, 0x52, 0x0a, 0x65, 0x78, 0x70, 0x69, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x3e,
	0x0a, 0x11, 0x41, 0x63, 0x71, 0x75, 0x69, 0x72, 0x65, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x52,
	0x65, 0x73, 0x70, 0x12, 0x29, 0x0a, 0x06, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x68, 0x75, 0x62, 0x2e,
	0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x52, 0x06, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x22, 0x95,
	0x01, 0x0a, 0x16, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x41, 0x63, 0x71, 0x75, 0x69, 0x72, 0x65,
	0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x52, 0x65, 0x71, 0x12, 0x33, 0x0a, 0x09, 0x63, 0x6f, 0x6e,
	0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x73,
	0x71, 0x6c, 0x62, 0x75, 0x69, 0x6c, 0x64, 0x65, 0x72, 0x2e, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x52, 0x09, 0x63, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1d,
	0x0a, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x42, 0x07, 0xba,
	0x48, 0x04, 0x2a, 0x02, 0x28, 0x01, 0x52, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x27, 0x0a,
	0x0a, 0x65, 0x78, 0x70, 0x69, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x03, 0x42, 0x07, 0xba, 0x48, 0x04, 0x22, 0x02, 0x28, 0x00, 0x52, 0x0a, 0x65, 0x78, 0x70, 0x69,
	0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x46, 0x0a, 0x17, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68,
	0x41, 0x63, 0x71, 0x75, 0x69, 0x72, 0x65, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x52, 0x65, 0x73,
	0x70, 0x12, 0x2b, 0x0a, 0x07, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x11, 0x2e, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x68, 0x75, 0x62, 0x2e, 0x44,
	0x65, 0x76, 0x69, 0x63, 0x65, 0x52, 0x07, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x73, 0x22, 0x52,
	0x0a, 0x10, 0x52, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x52,
	0x65, 0x71, 0x12, 0x1d, 0x0a, 0x04, 0x75, 0x64, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x09, 0xba, 0x48, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x40, 0x52, 0x04, 0x75, 0x64, 0x69,
	0x64, 0x12, 0x1f, 0x0a, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x09, 0xba, 0x48, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x40, 0x52, 0x05, 0x74, 0x6f, 0x6b,
	0x65, 0x6e, 0x22, 0x13, 0x0a, 0x11, 0x52, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x44, 0x65, 0x76,
	0x69, 0x63, 0x65, 0x52, 0x65, 0x73, 0x70, 0x32, 0xeb, 0x04, 0x0a, 0x0d, 0x44, 0x65, 0x76, 0x69,
	0x63, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x5f, 0x0a, 0x14, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x4f, 0x72, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x44, 0x65, 0x76, 0x69, 0x63,
	0x65, 0x12, 0x22, 0x2e, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x68, 0x75, 0x62, 0x2e, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x4f, 0x72, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x44, 0x65, 0x76, 0x69,
	0x63, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x23, 0x2e, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x68, 0x75,
	0x62, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4f, 0x72, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x79,
	0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x52, 0x65, 0x73, 0x70, 0x12, 0x7a, 0x0a, 0x1d, 0x4d, 0x6f,
	0x64, 0x69, 0x66, 0x79, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x42, 0x79, 0x50, 0x72, 0x6f, 0x76,
	0x69, 0x64, 0x65, 0x72, 0x4f, 0x66, 0x66, 0x6c, 0x69, 0x6e, 0x65, 0x12, 0x2b, 0x2e, 0x64, 0x65,
	0x76, 0x69, 0x63, 0x65, 0x68, 0x75, 0x62, 0x2e, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x44, 0x65,
	0x76, 0x69, 0x63, 0x65, 0x42, 0x79, 0x50, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x4f, 0x66,
	0x66, 0x6c, 0x69, 0x6e, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x2c, 0x2e, 0x64, 0x65, 0x76, 0x69, 0x63,
	0x65, 0x68, 0x75, 0x62, 0x2e, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x44, 0x65, 0x76, 0x69, 0x63,
	0x65, 0x42, 0x79, 0x50, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x4f, 0x66, 0x66, 0x6c, 0x69,
	0x6e, 0x65, 0x52, 0x65, 0x73, 0x70, 0x12, 0x3e, 0x0a, 0x09, 0x47, 0x65, 0x74, 0x44, 0x65, 0x76,
	0x69, 0x63, 0x65, 0x12, 0x17, 0x2e, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x68, 0x75, 0x62, 0x2e,
	0x47, 0x65, 0x74, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x18, 0x2e, 0x64,
	0x65, 0x76, 0x69, 0x63, 0x65, 0x68, 0x75, 0x62, 0x2e, 0x47, 0x65, 0x74, 0x44, 0x65, 0x76, 0x69,
	0x63, 0x65, 0x52, 0x65, 0x73, 0x70, 0x12, 0x47, 0x0a, 0x0c, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68,
	0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x12, 0x1a, 0x2e, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x68,
	0x75, 0x62, 0x2e, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x52,
	0x65, 0x71, 0x1a, 0x1b, 0x2e, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x68, 0x75, 0x62, 0x2e, 0x53,
	0x65, 0x61, 0x72, 0x63, 0x68, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x52, 0x65, 0x73, 0x70, 0x12,
	0x4a, 0x0a, 0x0d, 0x41, 0x63, 0x71, 0x75, 0x69, 0x72, 0x65, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65,
	0x12, 0x1b, 0x2e, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x68, 0x75, 0x62, 0x2e, 0x41, 0x63, 0x71,
	0x75, 0x69, 0x72, 0x65, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x1c, 0x2e,
	0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x68, 0x75, 0x62, 0x2e, 0x41, 0x63, 0x71, 0x75, 0x69, 0x72,
	0x65, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x52, 0x65, 0x73, 0x70, 0x12, 0x5c, 0x0a, 0x13, 0x53,
	0x65, 0x61, 0x72, 0x63, 0x68, 0x41, 0x63, 0x71, 0x75, 0x69, 0x72, 0x65, 0x44, 0x65, 0x76, 0x69,
	0x63, 0x65, 0x12, 0x21, 0x2e, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x68, 0x75, 0x62, 0x2e, 0x53,
	0x65, 0x61, 0x72, 0x63, 0x68, 0x41, 0x63, 0x71, 0x75, 0x69, 0x72, 0x65, 0x44, 0x65, 0x76, 0x69,
	0x63, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x22, 0x2e, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x68, 0x75,
	0x62, 0x2e, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x41, 0x63, 0x71, 0x75, 0x69, 0x72, 0x65, 0x44,
	0x65, 0x76, 0x69, 0x63, 0x65, 0x52, 0x65, 0x73, 0x70, 0x12, 0x4a, 0x0a, 0x0d, 0x52, 0x65, 0x6c,
	0x65, 0x61, 0x73, 0x65, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x12, 0x1b, 0x2e, 0x64, 0x65, 0x76,
	0x69, 0x63, 0x65, 0x68, 0x75, 0x62, 0x2e, 0x52, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x44, 0x65,
	0x76, 0x69, 0x63, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x1c, 0x2e, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65,
	0x68, 0x75, 0x62, 0x2e, 0x52, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x44, 0x65, 0x76, 0x69, 0x63,
	0x65, 0x52, 0x65, 0x73, 0x70, 0x42, 0x43, 0x5a, 0x41, 0x67, 0x69, 0x74, 0x6c, 0x61, 0x62, 0x2e,
	0x74, 0x74, 0x79, 0x75, 0x79, 0x69, 0x6e, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x54, 0x65, 0x73, 0x74,
	0x44, 0x65, 0x76, 0x65, 0x6c, 0x6f, 0x70, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x70, 0x72, 0x6f, 0x62,
	0x65, 0x2d, 0x62, 0x61, 0x63, 0x6b, 0x65, 0x6e, 0x64, 0x2f, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65,
	0x68, 0x75, 0x62, 0x2f, 0x72, 0x70, 0x63, 0x2f, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x33,
}

var (
	file_devicehub_devicehub_proto_rawDescOnce sync.Once
	file_devicehub_devicehub_proto_rawDescData = file_devicehub_devicehub_proto_rawDesc
)

func file_devicehub_devicehub_proto_rawDescGZIP() []byte {
	file_devicehub_devicehub_proto_rawDescOnce.Do(func() {
		file_devicehub_devicehub_proto_rawDescData = protoimpl.X.CompressGZIP(file_devicehub_devicehub_proto_rawDescData)
	})
	return file_devicehub_devicehub_proto_rawDescData
}

var file_devicehub_devicehub_proto_msgTypes = make([]protoimpl.MessageInfo, 14)
var file_devicehub_devicehub_proto_goTypes = []any{
	(*CreateOrModifyDeviceReq)(nil),           // 0: devicehub.CreateOrModifyDeviceReq
	(*CreateOrModifyDeviceResp)(nil),          // 1: devicehub.CreateOrModifyDeviceResp
	(*ModifyDeviceByProviderOfflineReq)(nil),  // 2: devicehub.ModifyDeviceByProviderOfflineReq
	(*ModifyDeviceByProviderOfflineResp)(nil), // 3: devicehub.ModifyDeviceByProviderOfflineResp
	(*GetDeviceReq)(nil),                      // 4: devicehub.GetDeviceReq
	(*GetDeviceResp)(nil),                     // 5: devicehub.GetDeviceResp
	(*SearchDeviceReq)(nil),                   // 6: devicehub.SearchDeviceReq
	(*SearchDeviceResp)(nil),                  // 7: devicehub.SearchDeviceResp
	(*AcquireDeviceReq)(nil),                  // 8: devicehub.AcquireDeviceReq
	(*AcquireDeviceResp)(nil),                 // 9: devicehub.AcquireDeviceResp
	(*SearchAcquireDeviceReq)(nil),            // 10: devicehub.SearchAcquireDeviceReq
	(*SearchAcquireDeviceResp)(nil),           // 11: devicehub.SearchAcquireDeviceResp
	(*ReleaseDeviceReq)(nil),                  // 12: devicehub.ReleaseDeviceReq
	(*ReleaseDeviceResp)(nil),                 // 13: devicehub.ReleaseDeviceResp
	(pb.DeviceType)(0),                        // 14: common.DeviceType
	(pb.PlatformType)(0),                      // 15: common.PlatformType
	(ProviderType)(0),                         // 16: devicehub.ProviderType
	(DeviceState)(0),                          // 17: devicehub.DeviceState
	(*DeviceMetadata)(nil),                    // 18: devicehub.DeviceMetadata
	(*Device)(nil),                            // 19: devicehub.Device
	(*rpc.Condition)(nil),                     // 20: sqlbuilder.Condition
	(*rpc.Pagination)(nil),                    // 21: sqlbuilder.Pagination
	(*rpc.SortField)(nil),                     // 22: sqlbuilder.SortField
}
var file_devicehub_devicehub_proto_depIdxs = []int32{
	14, // 0: devicehub.CreateOrModifyDeviceReq.type:type_name -> common.DeviceType
	15, // 1: devicehub.CreateOrModifyDeviceReq.platform:type_name -> common.PlatformType
	16, // 2: devicehub.CreateOrModifyDeviceReq.provider_type:type_name -> devicehub.ProviderType
	17, // 3: devicehub.CreateOrModifyDeviceReq.state:type_name -> devicehub.DeviceState
	18, // 4: devicehub.CreateOrModifyDeviceReq.metadata:type_name -> devicehub.DeviceMetadata
	16, // 5: devicehub.ModifyDeviceByProviderOfflineReq.provider_type:type_name -> devicehub.ProviderType
	19, // 6: devicehub.GetDeviceResp.device:type_name -> devicehub.Device
	20, // 7: devicehub.SearchDeviceReq.condition:type_name -> sqlbuilder.Condition
	21, // 8: devicehub.SearchDeviceReq.pagination:type_name -> sqlbuilder.Pagination
	22, // 9: devicehub.SearchDeviceReq.sort:type_name -> sqlbuilder.SortField
	19, // 10: devicehub.SearchDeviceResp.items:type_name -> devicehub.Device
	19, // 11: devicehub.AcquireDeviceResp.device:type_name -> devicehub.Device
	20, // 12: devicehub.SearchAcquireDeviceReq.condition:type_name -> sqlbuilder.Condition
	19, // 13: devicehub.SearchAcquireDeviceResp.devices:type_name -> devicehub.Device
	0,  // 14: devicehub.DeviceService.CreateOrModifyDevice:input_type -> devicehub.CreateOrModifyDeviceReq
	2,  // 15: devicehub.DeviceService.ModifyDeviceByProviderOffline:input_type -> devicehub.ModifyDeviceByProviderOfflineReq
	4,  // 16: devicehub.DeviceService.GetDevice:input_type -> devicehub.GetDeviceReq
	6,  // 17: devicehub.DeviceService.SearchDevice:input_type -> devicehub.SearchDeviceReq
	8,  // 18: devicehub.DeviceService.AcquireDevice:input_type -> devicehub.AcquireDeviceReq
	10, // 19: devicehub.DeviceService.SearchAcquireDevice:input_type -> devicehub.SearchAcquireDeviceReq
	12, // 20: devicehub.DeviceService.ReleaseDevice:input_type -> devicehub.ReleaseDeviceReq
	1,  // 21: devicehub.DeviceService.CreateOrModifyDevice:output_type -> devicehub.CreateOrModifyDeviceResp
	3,  // 22: devicehub.DeviceService.ModifyDeviceByProviderOffline:output_type -> devicehub.ModifyDeviceByProviderOfflineResp
	5,  // 23: devicehub.DeviceService.GetDevice:output_type -> devicehub.GetDeviceResp
	7,  // 24: devicehub.DeviceService.SearchDevice:output_type -> devicehub.SearchDeviceResp
	9,  // 25: devicehub.DeviceService.AcquireDevice:output_type -> devicehub.AcquireDeviceResp
	11, // 26: devicehub.DeviceService.SearchAcquireDevice:output_type -> devicehub.SearchAcquireDeviceResp
	13, // 27: devicehub.DeviceService.ReleaseDevice:output_type -> devicehub.ReleaseDeviceResp
	21, // [21:28] is the sub-list for method output_type
	14, // [14:21] is the sub-list for method input_type
	14, // [14:14] is the sub-list for extension type_name
	14, // [14:14] is the sub-list for extension extendee
	0,  // [0:14] is the sub-list for field type_name
}

func init() { file_devicehub_devicehub_proto_init() }
func file_devicehub_devicehub_proto_init() {
	if File_devicehub_devicehub_proto != nil {
		return
	}
	file_devicehub_device_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_devicehub_devicehub_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   14,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_devicehub_devicehub_proto_goTypes,
		DependencyIndexes: file_devicehub_devicehub_proto_depIdxs,
		MessageInfos:      file_devicehub_devicehub_proto_msgTypes,
	}.Build()
	File_devicehub_devicehub_proto = out.File
	file_devicehub_devicehub_proto_rawDesc = nil
	file_devicehub_devicehub_proto_goTypes = nil
	file_devicehub_devicehub_proto_depIdxs = nil
}
