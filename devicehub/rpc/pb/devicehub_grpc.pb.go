// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.29.2
// source: devicehub/devicehub.proto

package pb

import (
	context "context"

	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	DeviceService_CreateOrModifyDevice_FullMethodName          = "/devicehub.DeviceService/CreateOrModifyDevice"
	DeviceService_ModifyDeviceByProviderOffline_FullMethodName = "/devicehub.DeviceService/ModifyDeviceByProviderOffline"
	DeviceService_GetDevice_FullMethodName                     = "/devicehub.DeviceService/GetDevice"
	DeviceService_SearchDevice_FullMethodName                  = "/devicehub.DeviceService/SearchDevice"
	DeviceService_AcquireDevice_FullMethodName                 = "/devicehub.DeviceService/AcquireDevice"
	DeviceService_SearchAcquireDevice_FullMethodName           = "/devicehub.DeviceService/SearchAcquireDevice"
	DeviceService_ReleaseDevice_FullMethodName                 = "/devicehub.DeviceService/ReleaseDevice"
)

// DeviceServiceClient is the client API for DeviceService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// DeviceService 设备服务
type DeviceServiceClient interface {
	// CreateOrModifyDevice 创建或编辑设备
	CreateOrModifyDevice(ctx context.Context, in *CreateOrModifyDeviceReq, opts ...grpc.CallOption) (*CreateOrModifyDeviceResp, error)
	// ModifyDeviceByProviderOffline 编辑下线的`provider`管理的设备
	ModifyDeviceByProviderOffline(ctx context.Context, in *ModifyDeviceByProviderOfflineReq, opts ...grpc.CallOption) (*ModifyDeviceByProviderOfflineResp, error)
	// GetDevice 获取设备（通过`udid`）
	GetDevice(ctx context.Context, in *GetDeviceReq, opts ...grpc.CallOption) (*GetDeviceResp, error)
	// SearchDevice 搜索设备
	SearchDevice(ctx context.Context, in *SearchDeviceReq, opts ...grpc.CallOption) (*SearchDeviceResp, error)
	// AcquireDevice 占用设备（通过`udid`）
	AcquireDevice(ctx context.Context, in *AcquireDeviceReq, opts ...grpc.CallOption) (*AcquireDeviceResp, error)
	// SearchAcquireDevice 占用设备（通过查询条件）
	SearchAcquireDevice(ctx context.Context, in *SearchAcquireDeviceReq, opts ...grpc.CallOption) (*SearchAcquireDeviceResp, error)
	// ReleaseDevice 释放设备（通过`udid`）
	ReleaseDevice(ctx context.Context, in *ReleaseDeviceReq, opts ...grpc.CallOption) (*ReleaseDeviceResp, error)
}

type deviceServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewDeviceServiceClient(cc grpc.ClientConnInterface) DeviceServiceClient {
	return &deviceServiceClient{cc}
}

func (c *deviceServiceClient) CreateOrModifyDevice(ctx context.Context, in *CreateOrModifyDeviceReq, opts ...grpc.CallOption) (*CreateOrModifyDeviceResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateOrModifyDeviceResp)
	err := c.cc.Invoke(ctx, DeviceService_CreateOrModifyDevice_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *deviceServiceClient) ModifyDeviceByProviderOffline(ctx context.Context, in *ModifyDeviceByProviderOfflineReq, opts ...grpc.CallOption) (*ModifyDeviceByProviderOfflineResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ModifyDeviceByProviderOfflineResp)
	err := c.cc.Invoke(ctx, DeviceService_ModifyDeviceByProviderOffline_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *deviceServiceClient) GetDevice(ctx context.Context, in *GetDeviceReq, opts ...grpc.CallOption) (*GetDeviceResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetDeviceResp)
	err := c.cc.Invoke(ctx, DeviceService_GetDevice_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *deviceServiceClient) SearchDevice(ctx context.Context, in *SearchDeviceReq, opts ...grpc.CallOption) (*SearchDeviceResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SearchDeviceResp)
	err := c.cc.Invoke(ctx, DeviceService_SearchDevice_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *deviceServiceClient) AcquireDevice(ctx context.Context, in *AcquireDeviceReq, opts ...grpc.CallOption) (*AcquireDeviceResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(AcquireDeviceResp)
	err := c.cc.Invoke(ctx, DeviceService_AcquireDevice_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *deviceServiceClient) SearchAcquireDevice(ctx context.Context, in *SearchAcquireDeviceReq, opts ...grpc.CallOption) (*SearchAcquireDeviceResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SearchAcquireDeviceResp)
	err := c.cc.Invoke(ctx, DeviceService_SearchAcquireDevice_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *deviceServiceClient) ReleaseDevice(ctx context.Context, in *ReleaseDeviceReq, opts ...grpc.CallOption) (*ReleaseDeviceResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ReleaseDeviceResp)
	err := c.cc.Invoke(ctx, DeviceService_ReleaseDevice_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// DeviceServiceServer is the server API for DeviceService service.
// All implementations must embed UnimplementedDeviceServiceServer
// for forward compatibility.
//
// DeviceService 设备服务
type DeviceServiceServer interface {
	// CreateOrModifyDevice 创建或编辑设备
	CreateOrModifyDevice(context.Context, *CreateOrModifyDeviceReq) (*CreateOrModifyDeviceResp, error)
	// ModifyDeviceByProviderOffline 编辑下线的`provider`管理的设备
	ModifyDeviceByProviderOffline(context.Context, *ModifyDeviceByProviderOfflineReq) (*ModifyDeviceByProviderOfflineResp, error)
	// GetDevice 获取设备（通过`udid`）
	GetDevice(context.Context, *GetDeviceReq) (*GetDeviceResp, error)
	// SearchDevice 搜索设备
	SearchDevice(context.Context, *SearchDeviceReq) (*SearchDeviceResp, error)
	// AcquireDevice 占用设备（通过`udid`）
	AcquireDevice(context.Context, *AcquireDeviceReq) (*AcquireDeviceResp, error)
	// SearchAcquireDevice 占用设备（通过查询条件）
	SearchAcquireDevice(context.Context, *SearchAcquireDeviceReq) (*SearchAcquireDeviceResp, error)
	// ReleaseDevice 释放设备（通过`udid`）
	ReleaseDevice(context.Context, *ReleaseDeviceReq) (*ReleaseDeviceResp, error)
	mustEmbedUnimplementedDeviceServiceServer()
}

// UnimplementedDeviceServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedDeviceServiceServer struct{}

func (UnimplementedDeviceServiceServer) CreateOrModifyDevice(context.Context, *CreateOrModifyDeviceReq) (*CreateOrModifyDeviceResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateOrModifyDevice not implemented")
}
func (UnimplementedDeviceServiceServer) ModifyDeviceByProviderOffline(context.Context, *ModifyDeviceByProviderOfflineReq) (*ModifyDeviceByProviderOfflineResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ModifyDeviceByProviderOffline not implemented")
}
func (UnimplementedDeviceServiceServer) GetDevice(context.Context, *GetDeviceReq) (*GetDeviceResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetDevice not implemented")
}
func (UnimplementedDeviceServiceServer) SearchDevice(context.Context, *SearchDeviceReq) (*SearchDeviceResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SearchDevice not implemented")
}
func (UnimplementedDeviceServiceServer) AcquireDevice(context.Context, *AcquireDeviceReq) (*AcquireDeviceResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AcquireDevice not implemented")
}
func (UnimplementedDeviceServiceServer) SearchAcquireDevice(context.Context, *SearchAcquireDeviceReq) (*SearchAcquireDeviceResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SearchAcquireDevice not implemented")
}
func (UnimplementedDeviceServiceServer) ReleaseDevice(context.Context, *ReleaseDeviceReq) (*ReleaseDeviceResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ReleaseDevice not implemented")
}
func (UnimplementedDeviceServiceServer) mustEmbedUnimplementedDeviceServiceServer() {}
func (UnimplementedDeviceServiceServer) testEmbeddedByValue()                       {}

// UnsafeDeviceServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to DeviceServiceServer will
// result in compilation errors.
type UnsafeDeviceServiceServer interface {
	mustEmbedUnimplementedDeviceServiceServer()
}

func RegisterDeviceServiceServer(s grpc.ServiceRegistrar, srv DeviceServiceServer) {
	// If the following call pancis, it indicates UnimplementedDeviceServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&DeviceService_ServiceDesc, srv)
}

func _DeviceService_CreateOrModifyDevice_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateOrModifyDeviceReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DeviceServiceServer).CreateOrModifyDevice(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DeviceService_CreateOrModifyDevice_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DeviceServiceServer).CreateOrModifyDevice(ctx, req.(*CreateOrModifyDeviceReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _DeviceService_ModifyDeviceByProviderOffline_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ModifyDeviceByProviderOfflineReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DeviceServiceServer).ModifyDeviceByProviderOffline(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DeviceService_ModifyDeviceByProviderOffline_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DeviceServiceServer).ModifyDeviceByProviderOffline(ctx, req.(*ModifyDeviceByProviderOfflineReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _DeviceService_GetDevice_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetDeviceReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DeviceServiceServer).GetDevice(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DeviceService_GetDevice_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DeviceServiceServer).GetDevice(ctx, req.(*GetDeviceReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _DeviceService_SearchDevice_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SearchDeviceReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DeviceServiceServer).SearchDevice(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DeviceService_SearchDevice_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DeviceServiceServer).SearchDevice(ctx, req.(*SearchDeviceReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _DeviceService_AcquireDevice_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AcquireDeviceReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DeviceServiceServer).AcquireDevice(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DeviceService_AcquireDevice_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DeviceServiceServer).AcquireDevice(ctx, req.(*AcquireDeviceReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _DeviceService_SearchAcquireDevice_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SearchAcquireDeviceReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DeviceServiceServer).SearchAcquireDevice(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DeviceService_SearchAcquireDevice_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DeviceServiceServer).SearchAcquireDevice(ctx, req.(*SearchAcquireDeviceReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _DeviceService_ReleaseDevice_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReleaseDeviceReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DeviceServiceServer).ReleaseDevice(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DeviceService_ReleaseDevice_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DeviceServiceServer).ReleaseDevice(ctx, req.(*ReleaseDeviceReq))
	}
	return interceptor(ctx, in, info, handler)
}

// DeviceService_ServiceDesc is the grpc.ServiceDesc for DeviceService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var DeviceService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "devicehub.DeviceService",
	HandlerType: (*DeviceServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateOrModifyDevice",
			Handler:    _DeviceService_CreateOrModifyDevice_Handler,
		},
		{
			MethodName: "ModifyDeviceByProviderOffline",
			Handler:    _DeviceService_ModifyDeviceByProviderOffline_Handler,
		},
		{
			MethodName: "GetDevice",
			Handler:    _DeviceService_GetDevice_Handler,
		},
		{
			MethodName: "SearchDevice",
			Handler:    _DeviceService_SearchDevice_Handler,
		},
		{
			MethodName: "AcquireDevice",
			Handler:    _DeviceService_AcquireDevice_Handler,
		},
		{
			MethodName: "SearchAcquireDevice",
			Handler:    _DeviceService_SearchAcquireDevice_Handler,
		},
		{
			MethodName: "ReleaseDevice",
			Handler:    _DeviceService_ReleaseDevice_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "devicehub/devicehub.proto",
}
