// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.1
// 	protoc        v5.29.2
// source: devicehub/device.proto

package pb

import (
	reflect "reflect"
	sync "sync"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"

	pb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type DeviceState int32

const (
	DeviceState_DS_NULL   DeviceState = 0 // NULL
	DeviceState_IDLE      DeviceState = 1 // 空闲中
	DeviceState_IN_USE    DeviceState = 2 // 使用中
	DeviceState_RELEASING DeviceState = 3 // 释放中
	DeviceState_OFFLINE   DeviceState = 4 // 已下线
	DeviceState_RESERVED  DeviceState = 5 // 已预留
)

// Enum value maps for DeviceState.
var (
	DeviceState_name = map[int32]string{
		0: "DS_NULL",
		1: "IDLE",
		2: "IN_USE",
		3: "RELEASING",
		4: "OFFLINE",
		5: "RESERVED",
	}
	DeviceState_value = map[string]int32{
		"DS_NULL":   0,
		"IDLE":      1,
		"IN_USE":    2,
		"RELEASING": 3,
		"OFFLINE":   4,
		"RESERVED":  5,
	}
)

func (x DeviceState) Enum() *DeviceState {
	p := new(DeviceState)
	*p = x
	return p
}

func (x DeviceState) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DeviceState) Descriptor() protoreflect.EnumDescriptor {
	return file_devicehub_device_proto_enumTypes[0].Descriptor()
}

func (DeviceState) Type() protoreflect.EnumType {
	return &file_devicehub_device_proto_enumTypes[0]
}

func (x DeviceState) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use DeviceState.Descriptor instead.
func (DeviceState) EnumDescriptor() ([]byte, []int) {
	return file_devicehub_device_proto_rawDescGZIP(), []int{0}
}

type ProviderType int32

const (
	ProviderType_PT_NULL     ProviderType = 0 // NULL
	ProviderType_ATX         ProviderType = 1 // atx-*-provider
	ProviderType_HUAWEICLOUD ProviderType = 2 // 华为云
)

// Enum value maps for ProviderType.
var (
	ProviderType_name = map[int32]string{
		0: "PT_NULL",
		1: "ATX",
		2: "HUAWEICLOUD",
	}
	ProviderType_value = map[string]int32{
		"PT_NULL":     0,
		"ATX":         1,
		"HUAWEICLOUD": 2,
	}
)

func (x ProviderType) Enum() *ProviderType {
	p := new(ProviderType)
	*p = x
	return p
}

func (x ProviderType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ProviderType) Descriptor() protoreflect.EnumDescriptor {
	return file_devicehub_device_proto_enumTypes[1].Descriptor()
}

func (ProviderType) Type() protoreflect.EnumType {
	return &file_devicehub_device_proto_enumTypes[1]
}

func (x ProviderType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ProviderType.Descriptor instead.
func (ProviderType) EnumDescriptor() ([]byte, []int) {
	return file_devicehub_device_proto_rawDescGZIP(), []int{1}
}

type Device struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Udid          string                 `protobuf:"bytes,1,opt,name=udid,proto3" json:"udid,omitempty"`
	Name          string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Type          pb.DeviceType          `protobuf:"varint,3,opt,name=type,proto3,enum=common.DeviceType" json:"type,omitempty"`
	Platform      pb.PlatformType        `protobuf:"varint,4,opt,name=platform,proto3,enum=common.PlatformType" json:"platform,omitempty"`
	Brand         string                 `protobuf:"bytes,5,opt,name=brand,proto3" json:"brand,omitempty"`
	Model         string                 `protobuf:"bytes,6,opt,name=model,proto3" json:"model,omitempty"`
	Version       string                 `protobuf:"bytes,7,opt,name=version,proto3" json:"version,omitempty"`
	Serial        string                 `protobuf:"bytes,8,opt,name=serial,proto3" json:"serial,omitempty"`
	Provider      string                 `protobuf:"bytes,9,opt,name=provider,proto3" json:"provider,omitempty"`
	ProviderType  ProviderType           `protobuf:"varint,10,opt,name=provider_type,json=providerType,proto3,enum=devicehub.ProviderType" json:"provider_type,omitempty"`
	RemoteAddress string                 `protobuf:"bytes,11,opt,name=remote_address,json=remoteAddress,proto3" json:"remote_address,omitempty"`
	State         DeviceState            `protobuf:"varint,12,opt,name=state,proto3,enum=devicehub.DeviceState" json:"state,omitempty"`
	Metadata      *DeviceMetadata        `protobuf:"bytes,13,opt,name=metadata,proto3" json:"metadata,omitempty"`
	Token         string                 `protobuf:"bytes,14,opt,name=token,proto3" json:"token,omitempty"`
	StartedAt     int64                  `protobuf:"varint,15,opt,name=started_at,json=startedAt,proto3" json:"started_at,omitempty"`
	Duration      int64                  `protobuf:"varint,16,opt,name=duration,proto3" json:"duration,omitempty"`
	CreatedBy     string                 `protobuf:"bytes,96,opt,name=created_by,json=createdBy,proto3" json:"created_by,omitempty"`  // 创建者
	UpdatedBy     string                 `protobuf:"bytes,97,opt,name=updated_by,json=updatedBy,proto3" json:"updated_by,omitempty"`  // 更新者
	CreatedAt     int64                  `protobuf:"varint,98,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"` // 创建时间
	UpdatedAt     int64                  `protobuf:"varint,99,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"` // 更新时间
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Device) Reset() {
	*x = Device{}
	mi := &file_devicehub_device_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Device) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Device) ProtoMessage() {}

func (x *Device) ProtoReflect() protoreflect.Message {
	mi := &file_devicehub_device_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Device.ProtoReflect.Descriptor instead.
func (*Device) Descriptor() ([]byte, []int) {
	return file_devicehub_device_proto_rawDescGZIP(), []int{0}
}

func (x *Device) GetUdid() string {
	if x != nil {
		return x.Udid
	}
	return ""
}

func (x *Device) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Device) GetType() pb.DeviceType {
	if x != nil {
		return x.Type
	}
	return pb.DeviceType(0)
}

func (x *Device) GetPlatform() pb.PlatformType {
	if x != nil {
		return x.Platform
	}
	return pb.PlatformType(0)
}

func (x *Device) GetBrand() string {
	if x != nil {
		return x.Brand
	}
	return ""
}

func (x *Device) GetModel() string {
	if x != nil {
		return x.Model
	}
	return ""
}

func (x *Device) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *Device) GetSerial() string {
	if x != nil {
		return x.Serial
	}
	return ""
}

func (x *Device) GetProvider() string {
	if x != nil {
		return x.Provider
	}
	return ""
}

func (x *Device) GetProviderType() ProviderType {
	if x != nil {
		return x.ProviderType
	}
	return ProviderType_PT_NULL
}

func (x *Device) GetRemoteAddress() string {
	if x != nil {
		return x.RemoteAddress
	}
	return ""
}

func (x *Device) GetState() DeviceState {
	if x != nil {
		return x.State
	}
	return DeviceState_DS_NULL
}

func (x *Device) GetMetadata() *DeviceMetadata {
	if x != nil {
		return x.Metadata
	}
	return nil
}

func (x *Device) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

func (x *Device) GetStartedAt() int64 {
	if x != nil {
		return x.StartedAt
	}
	return 0
}

func (x *Device) GetDuration() int64 {
	if x != nil {
		return x.Duration
	}
	return 0
}

func (x *Device) GetCreatedBy() string {
	if x != nil {
		return x.CreatedBy
	}
	return ""
}

func (x *Device) GetUpdatedBy() string {
	if x != nil {
		return x.UpdatedBy
	}
	return ""
}

func (x *Device) GetCreatedAt() int64 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

func (x *Device) GetUpdatedAt() int64 {
	if x != nil {
		return x.UpdatedAt
	}
	return 0
}

type DeviceMetadata struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	CphAddress    string                 `protobuf:"bytes,1,opt,name=cph_address,json=cphAddress,proto3" json:"cph_address,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeviceMetadata) Reset() {
	*x = DeviceMetadata{}
	mi := &file_devicehub_device_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeviceMetadata) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeviceMetadata) ProtoMessage() {}

func (x *DeviceMetadata) ProtoReflect() protoreflect.Message {
	mi := &file_devicehub_device_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeviceMetadata.ProtoReflect.Descriptor instead.
func (*DeviceMetadata) Descriptor() ([]byte, []int) {
	return file_devicehub_device_proto_rawDescGZIP(), []int{1}
}

func (x *DeviceMetadata) GetCphAddress() string {
	if x != nil {
		return x.CphAddress
	}
	return ""
}

type IdleDevicesNotifyInfo struct {
	state                protoimpl.MessageState `protogen:"open.v1"`
	RealPhonesOfAndroid  uint32                 `protobuf:"varint,1,opt,name=real_phones_of_android,json=realPhonesOfAndroid,proto3" json:"real_phones_of_android,omitempty"`
	CloudPhonesOfAndroid uint32                 `protobuf:"varint,2,opt,name=cloud_phones_of_android,json=cloudPhonesOfAndroid,proto3" json:"cloud_phones_of_android,omitempty"`
	RealPhonesOfIos      uint32                 `protobuf:"varint,3,opt,name=real_phones_of_ios,json=realPhonesOfIos,proto3" json:"real_phones_of_ios,omitempty"`
	CloudPhonesOfIos     uint32                 `protobuf:"varint,4,opt,name=cloud_phones_of_ios,json=cloudPhonesOfIos,proto3" json:"cloud_phones_of_ios,omitempty"`
	unknownFields        protoimpl.UnknownFields
	sizeCache            protoimpl.SizeCache
}

func (x *IdleDevicesNotifyInfo) Reset() {
	*x = IdleDevicesNotifyInfo{}
	mi := &file_devicehub_device_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *IdleDevicesNotifyInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IdleDevicesNotifyInfo) ProtoMessage() {}

func (x *IdleDevicesNotifyInfo) ProtoReflect() protoreflect.Message {
	mi := &file_devicehub_device_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IdleDevicesNotifyInfo.ProtoReflect.Descriptor instead.
func (*IdleDevicesNotifyInfo) Descriptor() ([]byte, []int) {
	return file_devicehub_device_proto_rawDescGZIP(), []int{2}
}

func (x *IdleDevicesNotifyInfo) GetRealPhonesOfAndroid() uint32 {
	if x != nil {
		return x.RealPhonesOfAndroid
	}
	return 0
}

func (x *IdleDevicesNotifyInfo) GetCloudPhonesOfAndroid() uint32 {
	if x != nil {
		return x.CloudPhonesOfAndroid
	}
	return 0
}

func (x *IdleDevicesNotifyInfo) GetRealPhonesOfIos() uint32 {
	if x != nil {
		return x.RealPhonesOfIos
	}
	return 0
}

func (x *IdleDevicesNotifyInfo) GetCloudPhonesOfIos() uint32 {
	if x != nil {
		return x.CloudPhonesOfIos
	}
	return 0
}

var File_devicehub_device_proto protoreflect.FileDescriptor

var file_devicehub_device_proto_rawDesc = []byte{
	0x0a, 0x16, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x68, 0x75, 0x62, 0x2f, 0x64, 0x65, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x09, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65,
	0x68, 0x75, 0x62, 0x1a, 0x11, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x65, 0x6e, 0x75, 0x6d,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x9b, 0x05, 0x0a, 0x06, 0x44, 0x65, 0x76, 0x69, 0x63,
	0x65, 0x12, 0x12, 0x0a, 0x04, 0x75, 0x64, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x75, 0x64, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x26, 0x0a, 0x04, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x12, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70,
	0x65, 0x12, 0x30, 0x0a, 0x08, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x6c, 0x61,
	0x74, 0x66, 0x6f, 0x72, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x52, 0x08, 0x70, 0x6c, 0x61, 0x74, 0x66,
	0x6f, 0x72, 0x6d, 0x12, 0x14, 0x0a, 0x05, 0x62, 0x72, 0x61, 0x6e, 0x64, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x62, 0x72, 0x61, 0x6e, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x12,
	0x18, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x65, 0x72,
	0x69, 0x61, 0x6c, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x65, 0x72, 0x69, 0x61,
	0x6c, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x18, 0x09, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x12, 0x3c, 0x0a,
	0x0d, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0a,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x17, 0x2e, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x68, 0x75, 0x62,
	0x2e, 0x50, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0c, 0x70,
	0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x12, 0x25, 0x0a, 0x0e, 0x72,
	0x65, 0x6d, 0x6f, 0x74, 0x65, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x0b, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0d, 0x72, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x41, 0x64, 0x64, 0x72, 0x65,
	0x73, 0x73, 0x12, 0x2c, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x16, 0x2e, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x68, 0x75, 0x62, 0x2e, 0x44, 0x65,
	0x76, 0x69, 0x63, 0x65, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65,
	0x12, 0x35, 0x0a, 0x08, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x18, 0x0d, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x19, 0x2e, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x68, 0x75, 0x62, 0x2e, 0x44,
	0x65, 0x76, 0x69, 0x63, 0x65, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x52, 0x08, 0x6d,
	0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e,
	0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x1d, 0x0a,
	0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0f, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x1a, 0x0a, 0x08,
	0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x10, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08,
	0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x64, 0x5f, 0x62, 0x79, 0x18, 0x60, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x64, 0x42, 0x79, 0x12, 0x1d, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x64, 0x5f, 0x62, 0x79, 0x18, 0x61, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x75, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x64, 0x42, 0x79, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x64, 0x5f, 0x61, 0x74, 0x18, 0x62, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64,
	0x5f, 0x61, 0x74, 0x18, 0x63, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x64, 0x41, 0x74, 0x22, 0x31, 0x0a, 0x0e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x4d, 0x65,
	0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x70, 0x68, 0x5f, 0x61, 0x64,
	0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x70, 0x68,
	0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x22, 0xdf, 0x01, 0x0a, 0x15, 0x49, 0x64, 0x6c, 0x65,
	0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x73, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x49, 0x6e, 0x66,
	0x6f, 0x12, 0x33, 0x0a, 0x16, 0x72, 0x65, 0x61, 0x6c, 0x5f, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x73,
	0x5f, 0x6f, 0x66, 0x5f, 0x61, 0x6e, 0x64, 0x72, 0x6f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x13, 0x72, 0x65, 0x61, 0x6c, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x73, 0x4f, 0x66, 0x41,
	0x6e, 0x64, 0x72, 0x6f, 0x69, 0x64, 0x12, 0x35, 0x0a, 0x17, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x5f,
	0x70, 0x68, 0x6f, 0x6e, 0x65, 0x73, 0x5f, 0x6f, 0x66, 0x5f, 0x61, 0x6e, 0x64, 0x72, 0x6f, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x14, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x50, 0x68,
	0x6f, 0x6e, 0x65, 0x73, 0x4f, 0x66, 0x41, 0x6e, 0x64, 0x72, 0x6f, 0x69, 0x64, 0x12, 0x2b, 0x0a,
	0x12, 0x72, 0x65, 0x61, 0x6c, 0x5f, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x73, 0x5f, 0x6f, 0x66, 0x5f,
	0x69, 0x6f, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0f, 0x72, 0x65, 0x61, 0x6c, 0x50,
	0x68, 0x6f, 0x6e, 0x65, 0x73, 0x4f, 0x66, 0x49, 0x6f, 0x73, 0x12, 0x2d, 0x0a, 0x13, 0x63, 0x6c,
	0x6f, 0x75, 0x64, 0x5f, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x73, 0x5f, 0x6f, 0x66, 0x5f, 0x69, 0x6f,
	0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x10, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x50, 0x68,
	0x6f, 0x6e, 0x65, 0x73, 0x4f, 0x66, 0x49, 0x6f, 0x73, 0x2a, 0x5a, 0x0a, 0x0b, 0x44, 0x65, 0x76,
	0x69, 0x63, 0x65, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x0b, 0x0a, 0x07, 0x44, 0x53, 0x5f, 0x4e,
	0x55, 0x4c, 0x4c, 0x10, 0x00, 0x12, 0x08, 0x0a, 0x04, 0x49, 0x44, 0x4c, 0x45, 0x10, 0x01, 0x12,
	0x0a, 0x0a, 0x06, 0x49, 0x4e, 0x5f, 0x55, 0x53, 0x45, 0x10, 0x02, 0x12, 0x0d, 0x0a, 0x09, 0x52,
	0x45, 0x4c, 0x45, 0x41, 0x53, 0x49, 0x4e, 0x47, 0x10, 0x03, 0x12, 0x0b, 0x0a, 0x07, 0x4f, 0x46,
	0x46, 0x4c, 0x49, 0x4e, 0x45, 0x10, 0x04, 0x12, 0x0c, 0x0a, 0x08, 0x52, 0x45, 0x53, 0x45, 0x52,
	0x56, 0x45, 0x44, 0x10, 0x05, 0x2a, 0x35, 0x0a, 0x0c, 0x50, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65,
	0x72, 0x54, 0x79, 0x70, 0x65, 0x12, 0x0b, 0x0a, 0x07, 0x50, 0x54, 0x5f, 0x4e, 0x55, 0x4c, 0x4c,
	0x10, 0x00, 0x12, 0x07, 0x0a, 0x03, 0x41, 0x54, 0x58, 0x10, 0x01, 0x12, 0x0f, 0x0a, 0x0b, 0x48,
	0x55, 0x41, 0x57, 0x45, 0x49, 0x43, 0x4c, 0x4f, 0x55, 0x44, 0x10, 0x02, 0x42, 0x43, 0x5a, 0x41,
	0x67, 0x69, 0x74, 0x6c, 0x61, 0x62, 0x2e, 0x74, 0x74, 0x79, 0x75, 0x79, 0x69, 0x6e, 0x2e, 0x63,
	0x6f, 0x6d, 0x2f, 0x54, 0x65, 0x73, 0x74, 0x44, 0x65, 0x76, 0x65, 0x6c, 0x6f, 0x70, 0x6d, 0x65,
	0x6e, 0x74, 0x2f, 0x70, 0x72, 0x6f, 0x62, 0x65, 0x2d, 0x62, 0x61, 0x63, 0x6b, 0x65, 0x6e, 0x64,
	0x2f, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x68, 0x75, 0x62, 0x2f, 0x72, 0x70, 0x63, 0x2f, 0x70,
	0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_devicehub_device_proto_rawDescOnce sync.Once
	file_devicehub_device_proto_rawDescData = file_devicehub_device_proto_rawDesc
)

func file_devicehub_device_proto_rawDescGZIP() []byte {
	file_devicehub_device_proto_rawDescOnce.Do(func() {
		file_devicehub_device_proto_rawDescData = protoimpl.X.CompressGZIP(file_devicehub_device_proto_rawDescData)
	})
	return file_devicehub_device_proto_rawDescData
}

var file_devicehub_device_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_devicehub_device_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_devicehub_device_proto_goTypes = []any{
	(DeviceState)(0),              // 0: devicehub.DeviceState
	(ProviderType)(0),             // 1: devicehub.ProviderType
	(*Device)(nil),                // 2: devicehub.Device
	(*DeviceMetadata)(nil),        // 3: devicehub.DeviceMetadata
	(*IdleDevicesNotifyInfo)(nil), // 4: devicehub.IdleDevicesNotifyInfo
	(pb.DeviceType)(0),            // 5: common.DeviceType
	(pb.PlatformType)(0),          // 6: common.PlatformType
}
var file_devicehub_device_proto_depIdxs = []int32{
	5, // 0: devicehub.Device.type:type_name -> common.DeviceType
	6, // 1: devicehub.Device.platform:type_name -> common.PlatformType
	1, // 2: devicehub.Device.provider_type:type_name -> devicehub.ProviderType
	0, // 3: devicehub.Device.state:type_name -> devicehub.DeviceState
	3, // 4: devicehub.Device.metadata:type_name -> devicehub.DeviceMetadata
	5, // [5:5] is the sub-list for method output_type
	5, // [5:5] is the sub-list for method input_type
	5, // [5:5] is the sub-list for extension type_name
	5, // [5:5] is the sub-list for extension extendee
	0, // [0:5] is the sub-list for field type_name
}

func init() { file_devicehub_device_proto_init() }
func file_devicehub_device_proto_init() {
	if File_devicehub_device_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_devicehub_device_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_devicehub_device_proto_goTypes,
		DependencyIndexes: file_devicehub_device_proto_depIdxs,
		EnumInfos:         file_devicehub_device_proto_enumTypes,
		MessageInfos:      file_devicehub_device_proto_msgTypes,
	}.Build()
	File_devicehub_device_proto = out.File
	file_devicehub_device_proto_rawDesc = nil
	file_devicehub_device_proto_goTypes = nil
	file_devicehub_device_proto_depIdxs = nil
}
