// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: devicehub/devicehub.proto

package pb

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	pb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = pb.DeviceType(0)
)

// Validate checks the field values on CreateOrModifyDeviceReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateOrModifyDeviceReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateOrModifyDeviceReq with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateOrModifyDeviceReqMultiError, or nil if none found.
func (m *CreateOrModifyDeviceReq) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateOrModifyDeviceReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Udid

	// no validation rules for Name

	// no validation rules for Type

	// no validation rules for Platform

	// no validation rules for Brand

	// no validation rules for Model

	// no validation rules for Serial

	// no validation rules for Version

	// no validation rules for Provider

	// no validation rules for ProviderType

	// no validation rules for RemoteAddress

	// no validation rules for State

	if all {
		switch v := interface{}(m.GetMetadata()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateOrModifyDeviceReqValidationError{
					field:  "Metadata",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateOrModifyDeviceReqValidationError{
					field:  "Metadata",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMetadata()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateOrModifyDeviceReqValidationError{
				field:  "Metadata",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreateOrModifyDeviceReqMultiError(errors)
	}

	return nil
}

// CreateOrModifyDeviceReqMultiError is an error wrapping multiple validation
// errors returned by CreateOrModifyDeviceReq.ValidateAll() if the designated
// constraints aren't met.
type CreateOrModifyDeviceReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateOrModifyDeviceReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateOrModifyDeviceReqMultiError) AllErrors() []error { return m }

// CreateOrModifyDeviceReqValidationError is the validation error returned by
// CreateOrModifyDeviceReq.Validate if the designated constraints aren't met.
type CreateOrModifyDeviceReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateOrModifyDeviceReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateOrModifyDeviceReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateOrModifyDeviceReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateOrModifyDeviceReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateOrModifyDeviceReqValidationError) ErrorName() string {
	return "CreateOrModifyDeviceReqValidationError"
}

// Error satisfies the builtin error interface
func (e CreateOrModifyDeviceReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateOrModifyDeviceReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateOrModifyDeviceReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateOrModifyDeviceReqValidationError{}

// Validate checks the field values on CreateOrModifyDeviceResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateOrModifyDeviceResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateOrModifyDeviceResp with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateOrModifyDeviceRespMultiError, or nil if none found.
func (m *CreateOrModifyDeviceResp) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateOrModifyDeviceResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return CreateOrModifyDeviceRespMultiError(errors)
	}

	return nil
}

// CreateOrModifyDeviceRespMultiError is an error wrapping multiple validation
// errors returned by CreateOrModifyDeviceResp.ValidateAll() if the designated
// constraints aren't met.
type CreateOrModifyDeviceRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateOrModifyDeviceRespMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateOrModifyDeviceRespMultiError) AllErrors() []error { return m }

// CreateOrModifyDeviceRespValidationError is the validation error returned by
// CreateOrModifyDeviceResp.Validate if the designated constraints aren't met.
type CreateOrModifyDeviceRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateOrModifyDeviceRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateOrModifyDeviceRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateOrModifyDeviceRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateOrModifyDeviceRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateOrModifyDeviceRespValidationError) ErrorName() string {
	return "CreateOrModifyDeviceRespValidationError"
}

// Error satisfies the builtin error interface
func (e CreateOrModifyDeviceRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateOrModifyDeviceResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateOrModifyDeviceRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateOrModifyDeviceRespValidationError{}

// Validate checks the field values on ModifyDeviceByProviderOfflineReq with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *ModifyDeviceByProviderOfflineReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ModifyDeviceByProviderOfflineReq with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// ModifyDeviceByProviderOfflineReqMultiError, or nil if none found.
func (m *ModifyDeviceByProviderOfflineReq) ValidateAll() error {
	return m.validate(true)
}

func (m *ModifyDeviceByProviderOfflineReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Provider

	// no validation rules for ProviderType

	if len(errors) > 0 {
		return ModifyDeviceByProviderOfflineReqMultiError(errors)
	}

	return nil
}

// ModifyDeviceByProviderOfflineReqMultiError is an error wrapping multiple
// validation errors returned by
// ModifyDeviceByProviderOfflineReq.ValidateAll() if the designated
// constraints aren't met.
type ModifyDeviceByProviderOfflineReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ModifyDeviceByProviderOfflineReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ModifyDeviceByProviderOfflineReqMultiError) AllErrors() []error { return m }

// ModifyDeviceByProviderOfflineReqValidationError is the validation error
// returned by ModifyDeviceByProviderOfflineReq.Validate if the designated
// constraints aren't met.
type ModifyDeviceByProviderOfflineReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ModifyDeviceByProviderOfflineReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ModifyDeviceByProviderOfflineReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ModifyDeviceByProviderOfflineReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ModifyDeviceByProviderOfflineReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ModifyDeviceByProviderOfflineReqValidationError) ErrorName() string {
	return "ModifyDeviceByProviderOfflineReqValidationError"
}

// Error satisfies the builtin error interface
func (e ModifyDeviceByProviderOfflineReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sModifyDeviceByProviderOfflineReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ModifyDeviceByProviderOfflineReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ModifyDeviceByProviderOfflineReqValidationError{}

// Validate checks the field values on ModifyDeviceByProviderOfflineResp with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *ModifyDeviceByProviderOfflineResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ModifyDeviceByProviderOfflineResp
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// ModifyDeviceByProviderOfflineRespMultiError, or nil if none found.
func (m *ModifyDeviceByProviderOfflineResp) ValidateAll() error {
	return m.validate(true)
}

func (m *ModifyDeviceByProviderOfflineResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return ModifyDeviceByProviderOfflineRespMultiError(errors)
	}

	return nil
}

// ModifyDeviceByProviderOfflineRespMultiError is an error wrapping multiple
// validation errors returned by
// ModifyDeviceByProviderOfflineResp.ValidateAll() if the designated
// constraints aren't met.
type ModifyDeviceByProviderOfflineRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ModifyDeviceByProviderOfflineRespMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ModifyDeviceByProviderOfflineRespMultiError) AllErrors() []error { return m }

// ModifyDeviceByProviderOfflineRespValidationError is the validation error
// returned by ModifyDeviceByProviderOfflineResp.Validate if the designated
// constraints aren't met.
type ModifyDeviceByProviderOfflineRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ModifyDeviceByProviderOfflineRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ModifyDeviceByProviderOfflineRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ModifyDeviceByProviderOfflineRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ModifyDeviceByProviderOfflineRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ModifyDeviceByProviderOfflineRespValidationError) ErrorName() string {
	return "ModifyDeviceByProviderOfflineRespValidationError"
}

// Error satisfies the builtin error interface
func (e ModifyDeviceByProviderOfflineRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sModifyDeviceByProviderOfflineResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ModifyDeviceByProviderOfflineRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ModifyDeviceByProviderOfflineRespValidationError{}

// Validate checks the field values on GetDeviceReq with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GetDeviceReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetDeviceReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in GetDeviceReqMultiError, or
// nil if none found.
func (m *GetDeviceReq) ValidateAll() error {
	return m.validate(true)
}

func (m *GetDeviceReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Udid

	if len(errors) > 0 {
		return GetDeviceReqMultiError(errors)
	}

	return nil
}

// GetDeviceReqMultiError is an error wrapping multiple validation errors
// returned by GetDeviceReq.ValidateAll() if the designated constraints aren't met.
type GetDeviceReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetDeviceReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetDeviceReqMultiError) AllErrors() []error { return m }

// GetDeviceReqValidationError is the validation error returned by
// GetDeviceReq.Validate if the designated constraints aren't met.
type GetDeviceReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetDeviceReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetDeviceReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetDeviceReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetDeviceReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetDeviceReqValidationError) ErrorName() string { return "GetDeviceReqValidationError" }

// Error satisfies the builtin error interface
func (e GetDeviceReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetDeviceReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetDeviceReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetDeviceReqValidationError{}

// Validate checks the field values on GetDeviceResp with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GetDeviceResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetDeviceResp with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in GetDeviceRespMultiError, or
// nil if none found.
func (m *GetDeviceResp) ValidateAll() error {
	return m.validate(true)
}

func (m *GetDeviceResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetDevice()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetDeviceRespValidationError{
					field:  "Device",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetDeviceRespValidationError{
					field:  "Device",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDevice()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetDeviceRespValidationError{
				field:  "Device",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetDeviceRespMultiError(errors)
	}

	return nil
}

// GetDeviceRespMultiError is an error wrapping multiple validation errors
// returned by GetDeviceResp.ValidateAll() if the designated constraints
// aren't met.
type GetDeviceRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetDeviceRespMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetDeviceRespMultiError) AllErrors() []error { return m }

// GetDeviceRespValidationError is the validation error returned by
// GetDeviceResp.Validate if the designated constraints aren't met.
type GetDeviceRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetDeviceRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetDeviceRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetDeviceRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetDeviceRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetDeviceRespValidationError) ErrorName() string { return "GetDeviceRespValidationError" }

// Error satisfies the builtin error interface
func (e GetDeviceRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetDeviceResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetDeviceRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetDeviceRespValidationError{}

// Validate checks the field values on SearchDeviceReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *SearchDeviceReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SearchDeviceReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SearchDeviceReqMultiError, or nil if none found.
func (m *SearchDeviceReq) ValidateAll() error {
	return m.validate(true)
}

func (m *SearchDeviceReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetCondition()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SearchDeviceReqValidationError{
					field:  "Condition",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SearchDeviceReqValidationError{
					field:  "Condition",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCondition()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SearchDeviceReqValidationError{
				field:  "Condition",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPagination()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SearchDeviceReqValidationError{
					field:  "Pagination",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SearchDeviceReqValidationError{
					field:  "Pagination",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPagination()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SearchDeviceReqValidationError{
				field:  "Pagination",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetSort() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SearchDeviceReqValidationError{
						field:  fmt.Sprintf("Sort[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SearchDeviceReqValidationError{
						field:  fmt.Sprintf("Sort[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SearchDeviceReqValidationError{
					field:  fmt.Sprintf("Sort[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return SearchDeviceReqMultiError(errors)
	}

	return nil
}

// SearchDeviceReqMultiError is an error wrapping multiple validation errors
// returned by SearchDeviceReq.ValidateAll() if the designated constraints
// aren't met.
type SearchDeviceReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SearchDeviceReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SearchDeviceReqMultiError) AllErrors() []error { return m }

// SearchDeviceReqValidationError is the validation error returned by
// SearchDeviceReq.Validate if the designated constraints aren't met.
type SearchDeviceReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SearchDeviceReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SearchDeviceReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SearchDeviceReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SearchDeviceReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SearchDeviceReqValidationError) ErrorName() string { return "SearchDeviceReqValidationError" }

// Error satisfies the builtin error interface
func (e SearchDeviceReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSearchDeviceReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SearchDeviceReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SearchDeviceReqValidationError{}

// Validate checks the field values on SearchDeviceResp with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *SearchDeviceResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SearchDeviceResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SearchDeviceRespMultiError, or nil if none found.
func (m *SearchDeviceResp) ValidateAll() error {
	return m.validate(true)
}

func (m *SearchDeviceResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CurrentPage

	// no validation rules for PageSize

	// no validation rules for TotalCount

	// no validation rules for TotalPage

	for idx, item := range m.GetItems() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SearchDeviceRespValidationError{
						field:  fmt.Sprintf("Items[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SearchDeviceRespValidationError{
						field:  fmt.Sprintf("Items[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SearchDeviceRespValidationError{
					field:  fmt.Sprintf("Items[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return SearchDeviceRespMultiError(errors)
	}

	return nil
}

// SearchDeviceRespMultiError is an error wrapping multiple validation errors
// returned by SearchDeviceResp.ValidateAll() if the designated constraints
// aren't met.
type SearchDeviceRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SearchDeviceRespMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SearchDeviceRespMultiError) AllErrors() []error { return m }

// SearchDeviceRespValidationError is the validation error returned by
// SearchDeviceResp.Validate if the designated constraints aren't met.
type SearchDeviceRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SearchDeviceRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SearchDeviceRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SearchDeviceRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SearchDeviceRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SearchDeviceRespValidationError) ErrorName() string { return "SearchDeviceRespValidationError" }

// Error satisfies the builtin error interface
func (e SearchDeviceRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSearchDeviceResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SearchDeviceRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SearchDeviceRespValidationError{}

// Validate checks the field values on AcquireDeviceReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *AcquireDeviceReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AcquireDeviceReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AcquireDeviceReqMultiError, or nil if none found.
func (m *AcquireDeviceReq) ValidateAll() error {
	return m.validate(true)
}

func (m *AcquireDeviceReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Udid

	// no validation rules for Expiration

	if len(errors) > 0 {
		return AcquireDeviceReqMultiError(errors)
	}

	return nil
}

// AcquireDeviceReqMultiError is an error wrapping multiple validation errors
// returned by AcquireDeviceReq.ValidateAll() if the designated constraints
// aren't met.
type AcquireDeviceReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AcquireDeviceReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AcquireDeviceReqMultiError) AllErrors() []error { return m }

// AcquireDeviceReqValidationError is the validation error returned by
// AcquireDeviceReq.Validate if the designated constraints aren't met.
type AcquireDeviceReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AcquireDeviceReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AcquireDeviceReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AcquireDeviceReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AcquireDeviceReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AcquireDeviceReqValidationError) ErrorName() string { return "AcquireDeviceReqValidationError" }

// Error satisfies the builtin error interface
func (e AcquireDeviceReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAcquireDeviceReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AcquireDeviceReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AcquireDeviceReqValidationError{}

// Validate checks the field values on AcquireDeviceResp with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *AcquireDeviceResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AcquireDeviceResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AcquireDeviceRespMultiError, or nil if none found.
func (m *AcquireDeviceResp) ValidateAll() error {
	return m.validate(true)
}

func (m *AcquireDeviceResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetDevice()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AcquireDeviceRespValidationError{
					field:  "Device",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AcquireDeviceRespValidationError{
					field:  "Device",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDevice()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AcquireDeviceRespValidationError{
				field:  "Device",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return AcquireDeviceRespMultiError(errors)
	}

	return nil
}

// AcquireDeviceRespMultiError is an error wrapping multiple validation errors
// returned by AcquireDeviceResp.ValidateAll() if the designated constraints
// aren't met.
type AcquireDeviceRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AcquireDeviceRespMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AcquireDeviceRespMultiError) AllErrors() []error { return m }

// AcquireDeviceRespValidationError is the validation error returned by
// AcquireDeviceResp.Validate if the designated constraints aren't met.
type AcquireDeviceRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AcquireDeviceRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AcquireDeviceRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AcquireDeviceRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AcquireDeviceRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AcquireDeviceRespValidationError) ErrorName() string {
	return "AcquireDeviceRespValidationError"
}

// Error satisfies the builtin error interface
func (e AcquireDeviceRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAcquireDeviceResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AcquireDeviceRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AcquireDeviceRespValidationError{}

// Validate checks the field values on SearchAcquireDeviceReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SearchAcquireDeviceReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SearchAcquireDeviceReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SearchAcquireDeviceReqMultiError, or nil if none found.
func (m *SearchAcquireDeviceReq) ValidateAll() error {
	return m.validate(true)
}

func (m *SearchAcquireDeviceReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetCondition()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SearchAcquireDeviceReqValidationError{
					field:  "Condition",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SearchAcquireDeviceReqValidationError{
					field:  "Condition",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCondition()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SearchAcquireDeviceReqValidationError{
				field:  "Condition",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Count

	// no validation rules for Expiration

	if len(errors) > 0 {
		return SearchAcquireDeviceReqMultiError(errors)
	}

	return nil
}

// SearchAcquireDeviceReqMultiError is an error wrapping multiple validation
// errors returned by SearchAcquireDeviceReq.ValidateAll() if the designated
// constraints aren't met.
type SearchAcquireDeviceReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SearchAcquireDeviceReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SearchAcquireDeviceReqMultiError) AllErrors() []error { return m }

// SearchAcquireDeviceReqValidationError is the validation error returned by
// SearchAcquireDeviceReq.Validate if the designated constraints aren't met.
type SearchAcquireDeviceReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SearchAcquireDeviceReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SearchAcquireDeviceReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SearchAcquireDeviceReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SearchAcquireDeviceReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SearchAcquireDeviceReqValidationError) ErrorName() string {
	return "SearchAcquireDeviceReqValidationError"
}

// Error satisfies the builtin error interface
func (e SearchAcquireDeviceReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSearchAcquireDeviceReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SearchAcquireDeviceReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SearchAcquireDeviceReqValidationError{}

// Validate checks the field values on SearchAcquireDeviceResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SearchAcquireDeviceResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SearchAcquireDeviceResp with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SearchAcquireDeviceRespMultiError, or nil if none found.
func (m *SearchAcquireDeviceResp) ValidateAll() error {
	return m.validate(true)
}

func (m *SearchAcquireDeviceResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetDevices() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SearchAcquireDeviceRespValidationError{
						field:  fmt.Sprintf("Devices[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SearchAcquireDeviceRespValidationError{
						field:  fmt.Sprintf("Devices[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SearchAcquireDeviceRespValidationError{
					field:  fmt.Sprintf("Devices[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return SearchAcquireDeviceRespMultiError(errors)
	}

	return nil
}

// SearchAcquireDeviceRespMultiError is an error wrapping multiple validation
// errors returned by SearchAcquireDeviceResp.ValidateAll() if the designated
// constraints aren't met.
type SearchAcquireDeviceRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SearchAcquireDeviceRespMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SearchAcquireDeviceRespMultiError) AllErrors() []error { return m }

// SearchAcquireDeviceRespValidationError is the validation error returned by
// SearchAcquireDeviceResp.Validate if the designated constraints aren't met.
type SearchAcquireDeviceRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SearchAcquireDeviceRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SearchAcquireDeviceRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SearchAcquireDeviceRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SearchAcquireDeviceRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SearchAcquireDeviceRespValidationError) ErrorName() string {
	return "SearchAcquireDeviceRespValidationError"
}

// Error satisfies the builtin error interface
func (e SearchAcquireDeviceRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSearchAcquireDeviceResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SearchAcquireDeviceRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SearchAcquireDeviceRespValidationError{}

// Validate checks the field values on ReleaseDeviceReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ReleaseDeviceReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ReleaseDeviceReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ReleaseDeviceReqMultiError, or nil if none found.
func (m *ReleaseDeviceReq) ValidateAll() error {
	return m.validate(true)
}

func (m *ReleaseDeviceReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Udid

	// no validation rules for Token

	if len(errors) > 0 {
		return ReleaseDeviceReqMultiError(errors)
	}

	return nil
}

// ReleaseDeviceReqMultiError is an error wrapping multiple validation errors
// returned by ReleaseDeviceReq.ValidateAll() if the designated constraints
// aren't met.
type ReleaseDeviceReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ReleaseDeviceReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ReleaseDeviceReqMultiError) AllErrors() []error { return m }

// ReleaseDeviceReqValidationError is the validation error returned by
// ReleaseDeviceReq.Validate if the designated constraints aren't met.
type ReleaseDeviceReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ReleaseDeviceReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ReleaseDeviceReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ReleaseDeviceReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ReleaseDeviceReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ReleaseDeviceReqValidationError) ErrorName() string { return "ReleaseDeviceReqValidationError" }

// Error satisfies the builtin error interface
func (e ReleaseDeviceReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sReleaseDeviceReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ReleaseDeviceReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ReleaseDeviceReqValidationError{}

// Validate checks the field values on ReleaseDeviceResp with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ReleaseDeviceResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ReleaseDeviceResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ReleaseDeviceRespMultiError, or nil if none found.
func (m *ReleaseDeviceResp) ValidateAll() error {
	return m.validate(true)
}

func (m *ReleaseDeviceResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return ReleaseDeviceRespMultiError(errors)
	}

	return nil
}

// ReleaseDeviceRespMultiError is an error wrapping multiple validation errors
// returned by ReleaseDeviceResp.ValidateAll() if the designated constraints
// aren't met.
type ReleaseDeviceRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ReleaseDeviceRespMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ReleaseDeviceRespMultiError) AllErrors() []error { return m }

// ReleaseDeviceRespValidationError is the validation error returned by
// ReleaseDeviceResp.Validate if the designated constraints aren't met.
type ReleaseDeviceRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ReleaseDeviceRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ReleaseDeviceRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ReleaseDeviceRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ReleaseDeviceRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ReleaseDeviceRespValidationError) ErrorName() string {
	return "ReleaseDeviceRespValidationError"
}

// Error satisfies the builtin error interface
func (e ReleaseDeviceRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sReleaseDeviceResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ReleaseDeviceRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ReleaseDeviceRespValidationError{}
