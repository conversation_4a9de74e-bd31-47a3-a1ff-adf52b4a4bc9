package pb

import (
	"database/sql"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/devicehub/common"
)

var (
	sqlNullString  = sql.NullString{}
	deviceMetadata = (*DeviceMetadata)(nil)
)

func (x DeviceState) ConvertTo() common.DeviceState {
	switch x {
	case DeviceState_IDLE:
		return common.Idle
	case DeviceState_IN_USE:
		return common.InUse
	case DeviceState_RELEASING:
		return common.Releasing
	case DeviceState_OFFLINE:
		return common.Offline
	case DeviceState_RESERVED:
		return common.Reserved
	default:
		return ""
	}
}

func StringToDeviceState() utils.TypeConverter {
	return utils.StringToPBEnum(DeviceState_DS_NULL)
}

func DeviceStateToString() utils.TypeConverter {
	return utils.PBEnumToString(DeviceState_DS_NULL)
}

func SqlNullStringToDeviceMetadata() utils.TypeConverter {
	return utils.TypeConverter{
		SrcType: sqlNullString,
		DstType: deviceMetadata,
		Fn: func(src any) (any, error) {
			if s, ok := src.(sql.NullString); !ok || !s.Valid || s.String == "" {
				return nil, nil
			} else {
				var o DeviceMetadata

				err := utils.StringToAny(s.String, &o)

				return &o, err
			}
		},
	}
}
