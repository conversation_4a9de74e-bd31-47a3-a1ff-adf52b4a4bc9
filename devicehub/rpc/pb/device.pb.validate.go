// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: devicehub/device.proto

package pb

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	pb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = pb.DeviceType(0)
)

// Validate checks the field values on Device with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Device) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Device with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in DeviceMultiError, or nil if none found.
func (m *Device) ValidateAll() error {
	return m.validate(true)
}

func (m *Device) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Udid

	// no validation rules for Name

	// no validation rules for Type

	// no validation rules for Platform

	// no validation rules for Brand

	// no validation rules for Model

	// no validation rules for Version

	// no validation rules for Serial

	// no validation rules for Provider

	// no validation rules for ProviderType

	// no validation rules for RemoteAddress

	// no validation rules for State

	if all {
		switch v := interface{}(m.GetMetadata()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DeviceValidationError{
					field:  "Metadata",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DeviceValidationError{
					field:  "Metadata",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMetadata()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DeviceValidationError{
				field:  "Metadata",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Token

	// no validation rules for StartedAt

	// no validation rules for Duration

	// no validation rules for CreatedBy

	// no validation rules for UpdatedBy

	// no validation rules for CreatedAt

	// no validation rules for UpdatedAt

	if len(errors) > 0 {
		return DeviceMultiError(errors)
	}

	return nil
}

// DeviceMultiError is an error wrapping multiple validation errors returned by
// Device.ValidateAll() if the designated constraints aren't met.
type DeviceMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeviceMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeviceMultiError) AllErrors() []error { return m }

// DeviceValidationError is the validation error returned by Device.Validate if
// the designated constraints aren't met.
type DeviceValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeviceValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeviceValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeviceValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeviceValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeviceValidationError) ErrorName() string { return "DeviceValidationError" }

// Error satisfies the builtin error interface
func (e DeviceValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDevice.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeviceValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeviceValidationError{}

// Validate checks the field values on DeviceMetadata with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *DeviceMetadata) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeviceMetadata with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in DeviceMetadataMultiError,
// or nil if none found.
func (m *DeviceMetadata) ValidateAll() error {
	return m.validate(true)
}

func (m *DeviceMetadata) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CphAddress

	if len(errors) > 0 {
		return DeviceMetadataMultiError(errors)
	}

	return nil
}

// DeviceMetadataMultiError is an error wrapping multiple validation errors
// returned by DeviceMetadata.ValidateAll() if the designated constraints
// aren't met.
type DeviceMetadataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeviceMetadataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeviceMetadataMultiError) AllErrors() []error { return m }

// DeviceMetadataValidationError is the validation error returned by
// DeviceMetadata.Validate if the designated constraints aren't met.
type DeviceMetadataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeviceMetadataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeviceMetadataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeviceMetadataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeviceMetadataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeviceMetadataValidationError) ErrorName() string { return "DeviceMetadataValidationError" }

// Error satisfies the builtin error interface
func (e DeviceMetadataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeviceMetadata.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeviceMetadataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeviceMetadataValidationError{}

// Validate checks the field values on IdleDevicesNotifyInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *IdleDevicesNotifyInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on IdleDevicesNotifyInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// IdleDevicesNotifyInfoMultiError, or nil if none found.
func (m *IdleDevicesNotifyInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *IdleDevicesNotifyInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for RealPhonesOfAndroid

	// no validation rules for CloudPhonesOfAndroid

	// no validation rules for RealPhonesOfIos

	// no validation rules for CloudPhonesOfIos

	if len(errors) > 0 {
		return IdleDevicesNotifyInfoMultiError(errors)
	}

	return nil
}

// IdleDevicesNotifyInfoMultiError is an error wrapping multiple validation
// errors returned by IdleDevicesNotifyInfo.ValidateAll() if the designated
// constraints aren't met.
type IdleDevicesNotifyInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m IdleDevicesNotifyInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m IdleDevicesNotifyInfoMultiError) AllErrors() []error { return m }

// IdleDevicesNotifyInfoValidationError is the validation error returned by
// IdleDevicesNotifyInfo.Validate if the designated constraints aren't met.
type IdleDevicesNotifyInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e IdleDevicesNotifyInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e IdleDevicesNotifyInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e IdleDevicesNotifyInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e IdleDevicesNotifyInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e IdleDevicesNotifyInfoValidationError) ErrorName() string {
	return "IdleDevicesNotifyInfoValidationError"
}

// Error satisfies the builtin error interface
func (e IdleDevicesNotifyInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sIdleDevicesNotifyInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = IdleDevicesNotifyInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = IdleDevicesNotifyInfoValidationError{}
