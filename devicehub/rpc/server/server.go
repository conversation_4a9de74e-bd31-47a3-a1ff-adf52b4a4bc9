package server

import (
	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/conf"
	"github.com/zeromicro/go-zero/core/service"
	"github.com/zeromicro/go-zero/zrpc"
	"google.golang.org/grpc"
	"google.golang.org/grpc/reflection"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/interceptors/serverinterceptors"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/log"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/server"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/devicehub/rpc/internal"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/devicehub/rpc/internal/config"
	deviceservice "gitlab.ttyuyin.com/TestDevelopment/probe-backend/devicehub/rpc/internal/server/deviceservice"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/devicehub/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/devicehub/rpc/pb"
)

// NewRpcServer for single server startup
func NewRpcServer(configFile string) (*server.Server, error) {
	return server.NewServer(Options(configFile)...)
}

// NewServer for combine server startup
func NewServer(c server.Config, w *log.ZapWriter) (service.Service, server.TearDownFunc, error) {
	cc, ok := c.(config.Config)
	if !ok {
		return nil, nil, errors.Errorf("failed to new rpc server, cause by the config[%T] isn't a rpc config", c)
	}

	ctx := svc.NewServiceContext(cc)
	s := zrpc.MustNewServer(
		cc.RpcServerConf, func(grpcServer *grpc.Server) {
			pb.RegisterDeviceServiceServer(grpcServer, deviceservice.NewDeviceServiceServer(ctx))

			if cc.Mode == service.DevMode || cc.Mode == service.TestMode {
				reflection.Register(grpcServer)
			}
		},
	)

	internal.InitOperations(ctx)

	// 需要在 `ServiceConf.SetUp` 后再设置 `Writer`
	log.SetWriter(w)

	tdf := server.TearDownFunc(
		func() {
			s.AddUnaryInterceptors(serverinterceptors.ValidateWithCustomValidatorUnaryServerInterceptor(ctx.Validator))
			s.AddStreamInterceptors(serverinterceptors.ValidateWithCustomValidatorStreamServerInterceptor(ctx.Validator))
		},
	)

	return s, tdf, nil
}

// NewConfig new a config of server
func NewConfig(configFile string) server.Config {
	var c config.Config
	conf.MustLoad(configFile, &c)

	return c
}

// Options as a param of `server.NewServer`
func Options(configFile string) []server.Option {
	return []server.Option{
		server.WithNewConfigFunc(
			func() server.Config {
				return NewConfig(configFile)
			},
		),
		server.WithNewServiceAndTearDownFunc(NewServer),
		server.WithRpcServerMiddlewaresConf(
			server.RpcServerInterceptorsConf{
				UnUseUserInfo: true,
			},
		),
	}
}
