package types

type HandshakeCommandMessage struct {
	Name     string `json:"name"`
	URL      string `json:"url"`
	Secret   string `json:"secret"`
	Priority int32  `json:"priority"`
	Owner    string `json:"owner,optional"`
}

type UpdateCommandMessage struct {
	UDID       string      `json:"udid"`
	Platform   string      `json:"platform"`
	Properties *Properties `json:"properties,optional"` // 设备下线时，`provider`不会上报`properties`
	Provider   *Provider   `json:"provider"`
}

type Properties struct {
	Name    string `json:"name"`    // 设备名称
	Brand   string `json:"brand"`   // 品牌
	Model   string `json:"model"`   // 型号
	Serial  string `json:"serial"`  // 序列号
	Version string `json:"version"` // 版本
}

type Provider struct {
	// Android
	ATXAgentAddress      string `json:"atxAgentAddress,optional"`      // `ATX Agent`连接地址
	RemoteConnectAddress string `json:"remoteConnectAddress,optional"` // 远端连接地址
	WhatsInputAddress    string `json:"whatsInputAddress,optional"`    // `Whats Input`连接地址

	// iOS
	WdaURL string `json:"wdaUrl,optional"` // `WDA`连接地址
}
