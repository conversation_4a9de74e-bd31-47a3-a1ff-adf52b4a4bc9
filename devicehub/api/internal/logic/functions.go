package logic

import (
	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	commonpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/devicehub/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/devicehub/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/devicehub/rpc/pb"
)

var (
	apiDeviceMetadata = (*types.DeviceMetadata)(nil)
	rpcDeviceMetadata = (*pb.DeviceMetadata)(nil)
)

// Deprecated: use `commonpb.PlatformTypeToString` instead.
func PlatformTypeToString() utils.TypeConverter {
	return utils.TypeConverter{
		SrcType: commonpb.PlatformType_PT_NULL,
		DstType: utils.String,
		Fn: func(src any) (any, error) {
			e, ok := src.(commonpb.PlatformType)
			if !ok {
				return nil, errors.Errorf(
					"source type[%T] is not matching with [%T]", src, commonpb.PlatformType_PT_NULL,
				)
			}

			switch e {
			case commonpb.PlatformType_ANDROID:
				return string(common.Android), nil
			case commonpb.PlatformType_IOS:
				return string(common.IOS), nil
			case commonpb.PlatformType_HarmonyOS:
				return string(common.HarmonyOS), nil
			default:
				return "", errors.Errorf("invalid operate system: %s", e.String())
			}
		},
	}
}

func RpcDeviceMetadataToApiDeviceMetadata() utils.TypeConverter {
	return utils.TypeConverter{
		SrcType: rpcDeviceMetadata,
		DstType: apiDeviceMetadata,
		Fn: func(src any) (any, error) {
			m, ok := src.(*pb.DeviceMetadata)
			if !ok {
				return nil, errors.Errorf("source type[%T] is not matching with [%T]", src, rpcDeviceMetadata)
			}

			return &types.DeviceMetadata{
				CPHAddress: m.GetCphAddress(),
			}, nil
		},
	}
}
