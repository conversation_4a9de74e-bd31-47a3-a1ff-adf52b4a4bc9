package device

import (
	"context"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/devicehub/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/devicehub/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/devicehub/rpc/pb"
)

type SearchAcquireDeviceLogic struct {
	*BaseLogic
}

func NewSearchAcquireDeviceLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SearchAcquireDeviceLogic {
	return &SearchAcquireDeviceLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *SearchAcquireDeviceLogic) SearchAcquireDevice(req *types.SearchAcquireDeviceReq) (
	resp *types.SearchAcquireDeviceResp, err error,
) {
	in := &pb.SearchAcquireDeviceReq{}
	if err = utils.Copy(in, req, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy data to request, data: %s, error: %+v",
			jsonx.MarshalToStringIgnoreError(req), err,
		)
	}

	out, err := l.svcCtx.DeviceHubRPC.SearchAcquireDevice(l.ctx, in)
	if err != nil {
		return nil, err
	}

	// avoid returning null to the front end
	resp = &types.SearchAcquireDeviceResp{Devices: []*types.Device{}}
	if err = utils.Copy(resp, out, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy data to response, data: %s, error: %+v",
			protobuf.MarshalJSONToStringIgnoreError(out), err,
		)
	}

	return resp, nil
}
