package device

import (
	"context"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"
	"github.com/zeromicro/go-zero/zrpc"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/devicehub/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/devicehub/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/devicehub/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/devicehub/rpc/pb"
)

type ReleaseDeviceLogic struct {
	*BaseLogic
}

func NewReleaseDeviceLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ReleaseDeviceLogic {
	return &ReleaseDeviceLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *ReleaseDeviceLogic) ReleaseDevice(req *types.ReleaseDeviceReq) (resp *types.ReleaseDeviceResp, err error) {
	in := &pb.ReleaseDeviceReq{}
	if err = utils.Copy(in, req, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy data to request, data: %s, error: %+v",
			jsonx.MarshalToStringIgnoreError(req), err,
		)
	}

	_, err = l.svcCtx.DeviceHubRPC.ReleaseDevice(l.ctx, in, zrpc.WithCallTimeout(common.ConstRPCReleaseDeviceTimeout))
	if err != nil {
		return nil, err
	}

	return &types.ReleaseDeviceResp{}, nil
}
