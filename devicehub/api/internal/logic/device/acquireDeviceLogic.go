package device

import (
	"context"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/devicehub/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/devicehub/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/devicehub/rpc/pb"
)

type AcquireDeviceLogic struct {
	*BaseLogic
}

func NewAcquireDeviceLogic(ctx context.Context, svcCtx *svc.ServiceContext) *AcquireDeviceLogic {
	return &AcquireDeviceLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *AcquireDeviceLogic) AcquireDevice(req *types.AcquireDeviceReq) (resp *types.AcquireDeviceResp, err error) {
	resp = &types.AcquireDeviceResp{}

	in := &pb.AcquireDeviceReq{}
	if err = utils.Copy(in, req, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy data to request, data: %s, error: %+v",
			jsonx.MarshalToStringIgnoreError(req), err,
		)
	}

	out, err := l.svcCtx.DeviceHubRPC.AcquireDevice(l.ctx, in)
	if err != nil {
		return nil, err
	}

	if err = utils.Copy(resp, out, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy data to response, data: %s, error: %+v",
			protobuf.MarshalJSONToStringIgnoreError(out), err,
		)
	}

	return resp, nil
}
