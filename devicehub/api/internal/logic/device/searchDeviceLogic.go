package device

import (
	"context"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/devicehub/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/devicehub/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/devicehub/rpc/pb"
)

type SearchDeviceLogic struct {
	*BaseLogic
}

func NewSearchDeviceLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SearchDeviceLogic {
	return &SearchDeviceLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *SearchDeviceLogic) SearchDevice(req *types.SearchDeviceReq) (resp *types.SearchDeviceResp, err error) {
	in := &pb.SearchDeviceReq{}
	if err = utils.Copy(in, req, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy data to request, data: %s, error: %+v",
			jsonx.MarshalToStringIgnoreError(req), err,
		)
	}

	out, err := l.svcCtx.DeviceHubRPC.SearchDevice(l.ctx, in)
	if err != nil {
		return nil, err
	}

	// avoid returning null to the front end
	resp = &types.SearchDeviceResp{Items: []*types.Device{}}
	if err = utils.Copy(resp, out, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy data to response, data: %s, error: %+v",
			protobuf.MarshalJSONToStringIgnoreError(out), err,
		)
	}

	for _, item := range resp.Items {
		if item.Token != "" {
			// hide the `token`
			item.Token = constants.SensitiveWorld
		}
	}

	return resp, nil
}
