package websocket

import (
	"net/http"

	ws "github.com/gorilla/websocket"
	"github.com/zeromicro/go-zero/core/logx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/devicehub/api/internal/logic/websocket"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/devicehub/api/internal/svc"
)

var upgrader = ws.Upgrader{
	ReadBufferSize:  1024,
	WriteBufferSize: 1024,
}

func AtxProviderWSHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		conn, err := upgrader.Upgrade(w, r, nil)
		if err != nil {
			logx.WithContext(r.Context()).Errorf(
				"failed to upgrade the HTTP server connection to the WebSocket protocol, error: %v", err,
			)
			return
		}

		websocket.NewATXProviderWSLogic(r.Context(), svcCtx, conn).ProviderWS()
	}
}
