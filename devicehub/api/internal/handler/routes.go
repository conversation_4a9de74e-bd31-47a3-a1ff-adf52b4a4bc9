// Code generated by goctl. DO NOT EDIT.
package handler

import (
	"net/http"
	"time"

	device "gitlab.ttyuyin.com/TestDevelopment/probe-backend/devicehub/api/internal/handler/device"
	websocket "gitlab.ttyuyin.com/TestDevelopment/probe-backend/devicehub/api/internal/handler/websocket"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/devicehub/api/internal/svc"

	"github.com/zeromicro/go-zero/rest"
)

func RegisterHandlers(server *rest.Server, serverCtx *svc.ServiceContext) {
	server.AddRoutes(
		[]rest.Route{
			{
				// atx provider heartbeat websocket handler
				Method:  http.MethodGet,
				Path:    "/heartbeat",
				Handler: websocket.AtxProviderWSHandler(serverCtx),
			},
		},
		rest.WithPrefix("/websocket"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				// search devices
				Method:  http.MethodPost,
				Path:    "/device/search",
				Handler: device.SearchDeviceHandler(serverCtx),
			},
			{
				// acquire device
				Method:  http.MethodPost,
				Path:    "/device/acquire",
				Handler: device.AcquireDeviceHandler(serverCtx),
			},
			{
				// release device
				Method:  http.MethodPost,
				Path:    "/device/release",
				Handler: device.ReleaseDeviceHandler(serverCtx),
			},
			{
				// search acquire device
				Method:  http.MethodPost,
				Path:    "/device/search_acquire",
				Handler: device.SearchAcquireDeviceHandler(serverCtx),
			},
		},
		rest.WithPrefix("/devicehub/v1"),
		rest.WithTimeout(8000*time.Millisecond),
	)
}
