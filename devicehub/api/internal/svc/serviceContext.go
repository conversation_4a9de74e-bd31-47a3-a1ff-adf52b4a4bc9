package svc

import (
	"github.com/zeromicro/go-zero/zrpc"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/devicehub/api/internal/config"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/devicehub/rpc/client/deviceservice"
)

type ServiceContext struct {
	Config config.Config

	Validator *utils.Validator

	DeviceHubRPC deviceservice.DeviceService
}

func NewServiceContext(c config.Config) *ServiceContext {
	return &ServiceContext{
		Config: c,

		Validator: utils.NewValidator(c.Validator.Locale),

		DeviceHubRPC: deviceservice.NewDeviceService(zrpc.MustNewClient(c.<PERSON>ceHub)),
	}
}
