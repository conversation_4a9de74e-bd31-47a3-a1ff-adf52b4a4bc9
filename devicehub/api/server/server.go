package server

import (
	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/conf"
	"github.com/zeromicro/go-zero/core/service"
	"github.com/zeromicro/go-zero/rest"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/log"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/server"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/devicehub/api/internal/config"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/devicehub/api/internal/handler"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/devicehub/api/internal/svc"
)

// NewApiServer for single server startup
func NewApiServer(configFile string) (*server.Server, error) {
	return server.NewServer(Options(configFile)...)
}

// NewServer for combine server startup
func NewServer(c server.Config, w *log.ZapWriter) (service.Service, error) {
	cc, ok := c.(config.Config)
	if !ok {
		return nil, errors.Errorf("failed to new api server, cause by the config[%T] isn't a api config", c)
	}

	ctx := svc.NewServiceContext(cc)
	s := rest.MustNewServer(cc.RestConf)
	handler.RegisterHandlers(s, ctx)

	// 需要在 `ServiceConf.SetUp` 后再设置 `Writer`
	log.SetWriter(w)

	return s, nil
}

// NewConfig new a config of server
func NewConfig(configFile string) server.Config {
	var c config.Config
	conf.MustLoad(configFile, &c)

	return c
}

// Options as a param of `server.NewServer`
func Options(configFile string) []server.Option {
	return []server.Option{
		server.WithNewConfigFunc(
			func() server.Config {
				return NewConfig(configFile)
			},
		),
		server.WithNewServiceFunc(NewServer),
		server.WithApiServerMiddlewaresConf(
			server.ApiServerMiddlewaresConf{
				UnUseAuth: true,
			},
		),
	}
}
