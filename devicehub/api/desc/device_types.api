syntax = "v1"

import "types.api"

type (
    Device {
        UDID          string          `json:"udid" copier:"Udid" zh:"设备编号"`
        Name          string          `json:"name" zh:"设备名称"`
        Type          int8            `json:"type,default=1" zh:"设备类型（真机、云手机）"`
        Platform      string          `json:"platform,default=Android" zh:"平台（Android、iOS、HarmonyOS）"`
        Brand         string          `json:"brand,default=UNKNOWN" zh:"品牌"`
        Model         string          `json:"model,default=UNKNOWN" zh:"型号"`
        Version       string          `json:"version,default=UNKNOWN" zh:"版本"`
        Serial        string          `json:"serial,default=UNKNOWN" zh:"序列号"`
        Provider      string          `json:"provider" zh:"设备所属"`
        ProviderType  int8            `json:"provider_type" zh:"设备所属类型（ATX、HUAWEICLOUD）"`
        RemoteAddress string          `json:"remote_address" zh:"远程连接地址"`
        State         string          `json:"state,default=IDLE" zh:"状态（空闲中、使用中、释放中、下线中、已预留）"`
        Metadata      *DeviceMetadata `json:"metadata" zh:"设备元数据"`
        Token         string          `json:"token" zh:"令牌"`
        StartedAt     int64           `json:"started_at" zh:"开始时间"`
        Duration      int64           `json:"duration" zh:"使用时长"`
        //CreatedBy     *FullUserInfo   `json:"created_by"`
        //UpdatedBy     *FullUserInfo   `json:"updated_by"`
        CreatedAt     int64           `json:"created_at"`
        UpdatedAt     int64           `json:"updated_at"`
    }

    DeviceMetadata {
        CPHAddress string `json:"cph_address" zh:"华为云手机内网监听地址"`
    }
)

type (
    SearchDeviceReq {
        Condition  *Condition   `json:"condition,omitempty,optional" zh:"查询条件"`
        Pagination *Pagination  `json:"pagination,omitempty,optional" zh:"查询分页"`
        Sort       []*SortField `json:"sort,omitempty,optional" zh:"查询排序"`
    }
    SearchDeviceResp {
        CurrentPage uint64    `json:"current_page"`
        PageSize    uint64    `json:"page_size"`
        TotalCount  uint64    `json:"total_count"`
        TotalPage   uint64    `json:"total_page"`
        Items       []*Device `json:"items"`
    }
)

type (
    AcquireDeviceReq {
        UDID       string `json:"udid" validate:"required" copier:"Udid" zh:"设备编号"`
        Expiration uint32 `json:"expiration,omitempty,optional" zh:"最大占用时长（秒）"`
    }
    AcquireDeviceResp {
        *Device
    }
)

type (
    SearchAcquireDeviceReq {
        Condition  *Condition `json:"condition,omitempty,optional" zh:"查询条件"`
        Count      uint32     `json:"count,default=1" zh:"申请占用的设备数量"`
        Expiration uint32     `json:"expiration,omitempty,optional" zh:"最大占用时长（秒）"`
    }
    SearchAcquireDeviceResp {
        Devices []*Device `json:"devices"`
    }
)

type (
    ReleaseDeviceReq {
        UDID  string `json:"udid" validate:"required" copier:"Udid" zh:"设备编号"`
        Token string `json:"token" validate:"required" zh:"令牌"`
    }
    ReleaseDeviceResp {}
)
