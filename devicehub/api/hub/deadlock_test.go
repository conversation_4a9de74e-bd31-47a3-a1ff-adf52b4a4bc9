package hub

import (
	"fmt"
	"sync"
	"testing"
	"time"

	"go.uber.org/atomic"
)

// MockClientWithUnregister 模拟在Close时会调用UnRegister的客户端
type MockClientWithUnregister struct {
	key    string
	active *atomic.Bool
	closed *atomic.Bool
	hub    *Hub
	mu     sync.Mutex
}

func NewMockClientWithUnregister(key string, hub *Hub) *MockClientWithUnregister {
	return &MockClientWithUnregister{
		key:    key,
		active: atomic.NewBool(true),
		closed: atomic.NewBool(false),
		hub:    hub,
	}
}

func (m *MockClientWithUnregister) Key() string {
	return m.key
}

func (m *MockClientWithUnregister) IsActive() bool {
	return m.active.Load()
}

func (m *MockClientWithUnregister) Close() error {
	m.mu.Lock()
	defer m.mu.Unlock()

	if m.closed.Load() {
		return nil
	}

	m.active.Store(false)
	m.closed.Store(true)

	// 模拟ATXProviderWSLogic的行为：在Close时调用UnRegister
	if m.hub != nil {
		m.hub.UnRegister(m)
	}

	return nil
}

func (m *MockClientWithUnregister) IsClosed() bool {
	return m.closed.Load()
}

// TestDeadlockPrevention 测试死锁预防
func TestDeadlockPrevention(t *testing.T) {
	hub := NewHub()
	defer func() {
		// 模拟正常运行一段时间
		time.Sleep(5 * time.Second)
		hub.Close()
	}()

	providerURL := "http://test-provider:3600"

	// 创建第一个客户端
	client1 := NewMockClientWithUnregister(providerURL, hub)
	hub.Register(client1)

	// 验证第一个客户端注册成功
	if !hub.IsActiveProvider(client1) {
		t.Error("client1 should be active")
	}

	// 创建第二个客户端，这会触发第一个客户端的关闭
	// 在修复前，这里会发生死锁
	client2 := NewMockClientWithUnregister(providerURL, hub)

	// 使用goroutine和超时来检测死锁
	done := make(chan bool, 1)
	go func() {
		hub.Register(client2)
		done <- true
	}()

	// 等待注册完成，如果超时则说明发生了死锁
	select {
	case <-done:
		// 注册成功，没有死锁
	case <-time.After(5 * time.Second):
		t.Fatal("Register operation timed out, possible deadlock detected")
	}

	// 验证第二个客户端注册成功
	if !hub.IsActiveProvider(client2) {
		t.Error("client2 should be active")
	}

	// 验证第一个客户端被关闭
	if !client1.IsClosed() {
		t.Error("client1 should be closed")
	}

	// 验证第一个客户端不再是活跃的
	if hub.IsActiveProvider(client1) {
		t.Error("client1 should not be active after being replaced")
	}
}

// TestConcurrentRegistrations 测试并发注册
func TestConcurrentRegistrations(t *testing.T) {
	hub := NewHub()
	defer func() {
		// 模拟正常运行一段时间
		time.Sleep(5 * time.Second)
		hub.Close()
	}()

	providerURL := "http://test-provider:3600"
	numClients := 10

	var wg sync.WaitGroup
	clients := make([]*MockClientWithUnregister, numClients)

	// 并发注册多个客户端
	for i := 0; i < numClients; i++ {
		wg.Add(1)
		go func(index int) {
			defer wg.Done()
			clients[index] = NewMockClientWithUnregister(providerURL, hub)
			hub.Register(clients[index])
		}(i)
	}

	// 等待所有注册完成
	done := make(chan bool, 1)
	go func() {
		wg.Wait()
		done <- true
	}()

	// 检测是否有死锁
	select {
	case <-done:
		// 所有注册完成，没有死锁
	case <-time.After(10 * time.Second):
		t.Fatal("Concurrent registrations timed out, possible deadlock detected")
	}

	// 验证只有一个客户端是活跃的
	activeCount := 0
	var activeClient *MockClientWithUnregister
	for _, client := range clients {
		if client != nil && hub.IsActiveProvider(client) {
			activeCount++
			activeClient = client
		}
	}

	if activeCount != 1 {
		t.Errorf("Expected exactly 1 active client, got %d", activeCount)
	}

	if activeClient == nil {
		t.Error("No active client found")
	}
}

// TestHubCloseDeadlockPrevention 测试Hub.Close的死锁预防
func TestHubCloseDeadlockPrevention(t *testing.T) {
	hub := NewHub()

	// 注册一些客户端
	for i := 0; i < 5; i++ {
		client := NewMockClientWithUnregister(fmt.Sprintf("provider-%d", i), hub)
		hub.Register(client)
	}

	// 关闭Hub，这会触发所有客户端的Close，进而调用UnRegister
	// 在修复前，这里会发生死锁
	done := make(chan bool, 1)
	go func() {
		hub.Close()
		done <- true
	}()

	// 检测是否有死锁
	select {
	case <-done:
		// 关闭成功，没有死锁
	case <-time.After(5 * time.Second):
		t.Fatal("Hub.Close() timed out, possible deadlock detected")
	}
}
