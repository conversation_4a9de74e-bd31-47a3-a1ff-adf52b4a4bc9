package hub

import (
	"context"
	"io"
	"sync"
	"sync/atomic"

	"github.com/zeromicro/go-zero/core/lang"
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/proc"
)

var hub = NewHub()

func Register(c IClient) {
	hub.Register(c)
}

func UnRegister(c IClient) {
	hub.UnRegister(c)
}

func IsActiveProvider(c IClient) bool {
	return hub.IsActiveProvider(c)
}

type Hub struct {
	logx.Logger

	mutex            sync.RWMutex
	clients          map[string]IClient               // key: IClient.Key(), value: IClient
	anonymousClients map[IClient]lang.PlaceholderType // save the clients that IClient.Key() returns empty string

	closing *atomic.Bool
}

type IClient interface {
	io.Closer

	Key() string // 返回客户端的唯一标识，空字符串表示匿名客户端
	IsActive() bool
}

func NewHub() *Hub {
	h := &Hub{
		Logger: logx.WithContext(context.Background()),

		clients:          make(map[string]IClient),
		anonymousClients: make(map[IClient]lang.PlaceholderType),
		closing:          new(atomic.Bool),
	}

	proc.AddShutdownListener(h.Close)
	return h
}

func (h *Hub) Register(c IClient) {
	if h.closing.Load() {
		return
	}

	key := c.Key()
	var clientToClose IClient
	defer func() {
		// 在锁外关闭旧客户端，避免死锁
		if clientToClose != nil {
			h.Infof("closing existing client for key: %s", key)
			_ = clientToClose.Close()
		}
	}()

	h.mutex.Lock()
	defer h.mutex.Unlock()

	if key == "" {
		// 匿名客户端，存储在anonymousClients中
		h.anonymousClients[c] = lang.Placeholder
		h.Info("registered anonymous client")
	} else {
		// 有key的客户端，如果已存在同key的连接，标记需要关闭
		if existingClient, exists := h.clients[key]; exists && existingClient != c && existingClient.IsActive() {
			h.Infof("will close existing client for key: %s", key)
			clientToClose = existingClient
		}

		h.clients[key] = c
		h.Infof("registered client for key: %s", key)
	}
}

func (h *Hub) UnRegister(c IClient) {
	if h.closing.Load() {
		return
	}

	h.mutex.Lock()
	defer h.mutex.Unlock()

	key := c.Key()
	if key == "" {
		// 匿名客户端
		delete(h.anonymousClients, c)
		h.Info("unregistered anonymous client")
	} else {
		// 有key的客户端，只有当前注册的连接才能注销
		if currentClient, exists := h.clients[key]; exists && currentClient == c {
			delete(h.clients, key)
			h.Infof("unregistered client for key: %s", key)
		}
	}
}

func (h *Hub) IsActiveProvider(c IClient) bool {
	if h.closing.Load() {
		return false
	}

	h.mutex.RLock()
	defer h.mutex.RUnlock()

	key := c.Key()
	if key == "" {
		return false // 匿名客户端不是provider
	}

	currentClient, exists := h.clients[key]
	return exists && currentClient == c && currentClient.IsActive()
}

func (h *Hub) IsActiveClient(c IClient) bool {
	if h.closing.Load() {
		return false
	}

	h.mutex.RLock()
	defer h.mutex.RUnlock()

	key := c.Key()
	if key == "" {
		return c.IsActive()
	}

	currentClient, exists := h.clients[key]
	return exists && currentClient == c && currentClient.IsActive()
}

func (h *Hub) Close() {
	clientsToClose := make([]IClient, 0, len(h.clients)+len(h.anonymousClients))
	defer func() {
		// 在锁外关闭所有客户端，避免死锁
		for _, clientToClose := range clientsToClose {
			if clientToClose != nil {
				h.Infof("closing existing client for key: %s", clientToClose.Key())
				_ = clientToClose.Close()
			}
		}
	}()

	h.mutex.Lock()
	defer h.mutex.Unlock()

	h.closing.Store(true)

	// 收集需要关闭的客户端
	for key, c := range h.clients {
		clientsToClose = append(clientsToClose, c)
		delete(h.clients, key)
	}

	// 收集匿名客户端
	for c := range h.anonymousClients {
		clientsToClose = append(clientsToClose, c)
		delete(h.anonymousClients, c)
	}
}
