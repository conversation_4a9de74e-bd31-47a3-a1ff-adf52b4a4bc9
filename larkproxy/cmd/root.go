package cmd

import (
	"github.com/spf13/cobra"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/command"
)

const (
	rootCmdUse   = "larkproxy"
	rootCmdShort = "LarkProxy is one of the microservices of the Quality Platform"
	rootCmdLong  = `LarkProxy is one of the microservices of the Quality Platform. 
The main function is to act as an agent for Lark API and events and callbacks.`
)

func NewRootCommand() *cobra.Command {
	return command.NewRootCommand(rootCmdUse, rootCmdShort, rootCmdLong)
}
