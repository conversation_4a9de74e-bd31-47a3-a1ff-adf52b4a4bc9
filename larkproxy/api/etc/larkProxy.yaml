Name: api.larkproxy
Host: 0.0.0.0
Port: 21301
Verbose: true
Timeout: 0

Log:
  ServiceName: api.larkproxy
  Encoding: plain
  Level: info
  Path: /app/logs/larkproxy
  Stat: false

DevServer:
  Port: 21321
  EnablePprof: false

AuthSkipForUrls:
  - /larkproxy/v1/webhook/event
  - /larkproxy/v1/webhook/card

DeviceHub:
  EndPoints:
    - probe-test.ttyuyin.com:8000
  NonBlock: true
  Timeout: 0

LarkProxy:
  EndPoints:
    - 127.0.0.1:21311
  NonBlock: true
  Timeout: 0

Producer:
  Broker: 127.0.0.1:6379
  Backend: 127.0.0.1:6379
  Queue: mqc:larkproxy
  Db: 20

LarkCustomApp:
  # 正式版
  AppID: cli_a369eb0a70fe500b
  AppSecret: QJRZ0ECRJkMVR2omBH9EbUGoYsPczMLS
  VerificationToken: pqVGgBQ8g1DEaUBfqCrZ0dUsMCVhGPnx
  EventEncryptKey: 0iclKohbbknR0Uq0Dz7hBdJcQQHwrMZA
  # 测试版
  #AppID: cli_a36ed4f3ba3f100c
  #AppSecret: aKtiOYTAsTYhrqt4IBndRdYT6rS0Mkrj
  #VerificationToken: pqVGgBQ8g1DEaUBfqCrZ0dUsMCVhGPnx
  #EventEncryptKey: kK1TxkDgPCvYDuH8RUCp7zEgsW3dlOVf
