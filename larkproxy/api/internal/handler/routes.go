// Code generated by goctl. DO NOT EDIT.
// goctl 1.7.5

package handler

import (
	"net/http"

	chats "gitlab.ttyuyin.com/TestDevelopment/probe-backend/larkproxy/api/internal/handler/chats"
	users "gitlab.ttyuyin.com/TestDevelopment/probe-backend/larkproxy/api/internal/handler/users"
	webhook "gitlab.ttyuyin.com/TestDevelopment/probe-backend/larkproxy/api/internal/handler/webhook"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/larkproxy/api/internal/svc"

	"github.com/zeromicro/go-zero/rest"
)

func RegisterHandlers(server *rest.Server, serverCtx *svc.ServiceContext) {
	server.AddRoutes(
		[]rest.Route{
			{
				Method:  http.MethodPost,
				Path:    "/webhook/event",
				Handler: webhook.LarkEventWebhookHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/webhook/card",
				Handler: webhook.LarkCardWebhookHandler(serverCtx),
			},
		},
		rest.WithPrefix("/larkproxy/v1"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				Method:  http.MethodPost,
				Path:    "/contact/users/batch_get_id",
				Handler: users.GetBatchUserIDHandler(serverCtx),
			},
		},
		rest.WithPrefix("/larkproxy/v1"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				Method:  http.MethodGet,
				Path:    "/im/chat",
				Handler: chats.GetIMChatHandler(serverCtx),
			},
			{
				Method:  http.MethodGet,
				Path:    "/im/chats",
				Handler: chats.GetIMChatsHandler(serverCtx),
			},
			{
				Method:  http.MethodGet,
				Path:    "/im/chats/members",
				Handler: chats.GetChatMembersHandler(serverCtx),
			},
		},
		rest.WithPrefix("/larkproxy/v1"),
	)
}
