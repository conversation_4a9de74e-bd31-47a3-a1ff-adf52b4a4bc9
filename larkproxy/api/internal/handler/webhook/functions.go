package webhook

import (
	"io"
	"net/http"

	larkevent "github.com/larksuite/oapi-sdk-go/v3/event"
)

func convertToEventReq(r *http.Request) (*larkevent.EventReq, error) {
	rawBody, err := io.ReadAll(r.Body)
	if err != nil {
		return nil, err
	}

	return &larkevent.EventReq{
		Header:     r.<PERSON>,
		Body:       rawBody,
		RequestURI: r.RequestURI,
	}, nil
}

func writeToResponse(w http.ResponseWriter, resp *larkevent.EventResp) error {
	w.WriteHeader(resp.StatusCode)
	for k, vs := range resp.Header {
		for _, v := range vs {
			w.Header().Add(k, v)
		}
	}

	if len(resp.Body) > 0 {
		_, err := w.Write(resp.Body)
		return err
	}

	return nil
}
