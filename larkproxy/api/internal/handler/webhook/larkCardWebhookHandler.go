package webhook

import (
	"net/http"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/larkproxy/api/internal/logic/webhook"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/larkproxy/api/internal/svc"
)

func LarkCardWebhookHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		req, err := convertToEventReq(r)
		if err != nil {
			w.WriteHeader(http.StatusInternalServerError)
			_, _ = w.Write([]byte(err.Error()))
			return
		}

		l := webhook.NewLarkCardWebhookLogic(r.Context(), svcCtx)
		resp := l.LarkCardWebhook(req)
		_ = writeToResponse(w, resp)
	}
}
