package types

import larkim "github.com/larksuite/oapi-sdk-go/v3/service/im/v1"

type Chat struct {
	ChatId      string `json:"chat_id"`
	Avatar      string `json:"avatar"`
	Name        string `json:"name"`
	Description string `json:"description"`
	External    bool   `json:"external"`
	Status      string `json:"status" copier:"ChatStatus"`
}

type GetIMChatReq struct {
	ChatId string `form:"chat_id" validate:"required,startswith=oc_"`
}

type GetIMChatResp struct {
	*Chat
}

type GetIMChatsReq struct {
	WithCurrentUser bool `form:"with_current_user,optional"`
}

type GetIMChatsResp struct {
	Items []*larkim.ListChat `json:"items"`
}

type GetChatMembersReq struct {
	ChatId string `form:"chat_id" validate:"required,startswith=oc_"`
}

type GetChatMembersResp struct {
	Items []*larkim.ListMember `json:"items"`
}
