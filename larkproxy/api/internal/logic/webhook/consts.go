package webhook

const (
	keyOfCallbackType = "callback_type"
	keyOfElements     = "elements"
)

type ToastType string

const (
	toastTypeOfInfo    ToastType = "info"
	toastTypeOfSuccess ToastType = "success"
	toastTypeOfError   ToastType = "error"
	toastTypeOfWarning ToastType = "warning"
)

type CardType string

const (
	cardTypeOfRaw      CardType = "raw"
	cardTypeOfTemplate CardType = "template"
)
