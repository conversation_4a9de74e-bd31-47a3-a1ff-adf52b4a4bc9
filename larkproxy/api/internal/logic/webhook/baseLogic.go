package webhook

import (
	"context"
	"fmt"
	"time"

	lark "github.com/larksuite/oapi-sdk-go/v3"
	"github.com/larksuite/oapi-sdk-go/v3/event/dispatcher/callback"
	larkapplication "github.com/larksuite/oapi-sdk-go/v3/service/application/v6"
	larkim "github.com/larksuite/oapi-sdk-go/v3/service/im/v1"
	"github.com/zeromicro/go-zero/core/logx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	commonlark "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/lark"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/larkproxy/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/larkproxy/common"
)

type BaseLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext

	larkClient *lark.Client
}

func newBaseLogic(ctx context.Context, svcCtx *svc.ServiceContext) *BaseLogic {
	return &BaseLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,

		larkClient: lark.NewClient(
			svcCtx.Config.LarkCustomApp.AppID,
			svcCtx.Config.LarkCustomApp.AppSecret,
			lark.WithEnableTokenCache(true),
			lark.WithLogger(commonlark.NewLogger(ctx)),
			lark.WithReqTimeout(time.Duration(svcCtx.Config.Timeout)*time.Millisecond),
		),
	}
}

func (l *BaseLogic) doHandle(
	chatID, messageID string, action *callback.CallBackAction,
) (*callback.CardActionTriggerResponse, error) {
	if chatID == "" {
		return l.makeEmptyChatIDResp(), nil
	} else if messageID == "" {
		return l.makeEmptyMessageIDResp(), nil
	}

	callbackType, ok := action.Value[keyOfCallbackType].(string)
	if !ok {
		return l.makeUnknownCallbackTypeResp(action.Value[keyOfCallbackType]), nil
	}

	switch callbackType {
	case string(constants.LarkCardCallbackTypeReleaseDevice):
		return newReleaseDeviceLogic(l).Handle(chatID, messageID, action)
	default:
		return l.makeUnknownCallbackTypeResp(callbackType), nil
	}
}

func (l *BaseLogic) getCardContent(chatID, messageID string) string {
	apiPath := common.ConstLarkAPIPathOfGetMessage

	resp, err := l.larkClient.Im.Message.Get(l.ctx, larkim.NewGetMessageReqBuilder().MessageId(messageID).Build())
	if err != nil {
		l.Errorf("failed to get message content by calling lark api, api: %q, error: %+v", apiPath, err)
		return ""
	} else if resp.Code != 0 {
		l.Errorf(
			"failed to get message content by calling lark api, api: %q, code: %d, message: %s",
			apiPath, resp.Code, resp.Msg,
		)
		return ""
	} else if resp.Data == nil || len(resp.Data.Items) == 0 {
		return ""
	}

	for _, item := range resp.Data.Items {
		if item == nil || item.MessageId == nil || item.ChatId == nil || item.Body == nil || item.Body.Content == nil {
			// 无效消息
			continue
		}
		if item.Deleted != nil && *item.Deleted {
			// 消息已被撤回
			continue
		}
		if *item.MessageId != messageID || *item.ChatId != chatID {
			// 不是目标消息
			continue
		}
		if *item.Body.Content == "" {
			// 无效消息
			continue
		}

		return *item.Body.Content
	}

	return ""
}

func (l *BaseLogic) makeNullEventResp() *callback.CardActionTriggerResponse {
	zhCN := "从飞书服务器获取到空事件"
	enUS := "got a null event from Lark server"

	return &callback.CardActionTriggerResponse{
		Toast: &callback.Toast{
			Type:    string(toastTypeOfError),
			Content: zhCN,
			I18nContent: map[string]string{
				larkapplication.I18nKeyZhCn: zhCN,
				larkapplication.I18nKeyEnUs: enUS,
			},
		},
	}
}

func (l *BaseLogic) makeEmptyChatIDResp() *callback.CardActionTriggerResponse {
	zhCN := "从飞书服务器获取到空的群组ID"
	enUS := "got an empty chat ID from Lark server"

	return &callback.CardActionTriggerResponse{
		Toast: &callback.Toast{
			Type:    string(toastTypeOfError),
			Content: zhCN,
			I18nContent: map[string]string{
				larkapplication.I18nKeyZhCn: zhCN,
				larkapplication.I18nKeyEnUs: enUS,
			},
		},
	}
}

func (l *BaseLogic) makeEmptyMessageIDResp() *callback.CardActionTriggerResponse {
	zhCN := "从飞书服务器获取到空的消息ID"
	enUS := "got an empty message ID from Lark server"

	return &callback.CardActionTriggerResponse{
		Toast: &callback.Toast{
			Type:    string(toastTypeOfError),
			Content: zhCN,
			I18nContent: map[string]string{
				larkapplication.I18nKeyZhCn: zhCN,
				larkapplication.I18nKeyEnUs: enUS,
			},
		},
	}
}

func (l *BaseLogic) makeUnknownCallbackTypeResp(actionType any) *callback.CardActionTriggerResponse {
	zhCN := fmt.Sprintf("从飞书服务器获取到未知的`callback type`: %v", actionType)
	enUS := fmt.Sprintf("got a unknown callback type from Lark server, type: %v", actionType)

	return &callback.CardActionTriggerResponse{
		Toast: &callback.Toast{
			Type:    string(toastTypeOfWarning),
			Content: zhCN,
			I18nContent: map[string]string{
				larkapplication.I18nKeyZhCn: zhCN,
				larkapplication.I18nKeyEnUs: enUS,
			},
		},
	}
}
