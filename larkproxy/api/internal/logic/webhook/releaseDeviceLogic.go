package webhook

import (
	"bytes"
	_ "embed"
	"fmt"
	ttemplate "text/template"

	"github.com/devfeel/mapper"
	"github.com/larksuite/oapi-sdk-go/v3/event/dispatcher/callback"
	larkapplication "github.com/larksuite/oapi-sdk-go/v3/service/application/v6"
	"github.com/zeromicro/go-zero/core/jsonx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/template"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/devicehub/common"
	devicehubpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/devicehub/rpc/pb"
)

var (
	_ LarkCardCallbackHandler = (*releaseDeviceLogic)(nil)

	//go:embed releaseDevice.template
	releaseDeviceContentTemplate string

	releaseDeviceTemplate = ttemplate.Must(template.Parse("releaseDeviceTemplate", releaseDeviceContentTemplate))
)

type (
	releaseDeviceLogic struct {
		*BaseLogic
	}

	releaseDeviceValue struct {
		PlatformType    common.DevicePlatform `json:"platform_type"`     // 平台类型
		DeviceType      common.DeviceTypeZH   `json:"device_type"`       // 设备类型
		DeviceID        string                `json:"device_id"`         // 设备ID
		DeviceName      string                `json:"device_name"`       // 设备名称
		PreviousState   string                `json:"previous_state"`    // 设备上一次状态
		State           string                `json:"state"`             // 设备当前状态
		PreviousStateZH common.DeviceStateZH  `json:"previous_state_zh"` // 设备上一次状态
		StateZH         common.DeviceStateZH  `json:"state_zh"`          // 设备当前状态
		Token           string                `json:"token"`             // 令牌，用于释放设备时的验证
		RuleDuration    string                `json:"rule_duration"`     // 告警规则配置的持续时间
		Duration        string                `json:"duration"`          // 持续时间
		Times           string                `json:"times"`             // 告警次数
		UpdatedAt       string                `json:"updated_at"`        // 更新时间
	}
)

func newReleaseDeviceLogic(baseLogic *BaseLogic) *releaseDeviceLogic {
	return &releaseDeviceLogic{
		BaseLogic: baseLogic,
	}
}

func (l *releaseDeviceLogic) Handle(
	chatID, messageID string, action *callback.CallBackAction,
) (*callback.CardActionTriggerResponse, error) {
	return l.releaseDevice(chatID, messageID, action.Value)
}

func (l *releaseDeviceLogic) releaseDevice(
	_, _ string, value map[string]any,
) (*callback.CardActionTriggerResponse, error) {
	var value_ releaseDeviceValue
	if err := mapper.MapperMap(value, &value_); err != nil {
		return l.makeFailedResp(err), nil
	}

	in := &devicehubpb.ReleaseDeviceReq{
		Udid:  value_.DeviceID,
		Token: value_.Token,
	}
	if err := in.ValidateAll(); err != nil {
		return l.makeFailedResp(err), nil
	}

	if _, err := l.svcCtx.DeviceHubRPC.ReleaseDevice(l.ctx, in); err != nil {
		return l.makeFailedResp(err), nil
	}

	return l.makeSucceedResp(&value_), nil
}

func (l *releaseDeviceLogic) getCardContent(value *releaseDeviceValue) string {
	buf := new(bytes.Buffer)
	defer buf.Reset()

	if err := releaseDeviceTemplate.Execute(buf, value); err != nil {
		l.Errorf(
			"failed to execute the releaseDevice template, value: %s, error: %+v", jsonx.MarshalIgnoreError(value), err,
		)
		return ""
	}

	return buf.String()
}

func (l *releaseDeviceLogic) updateCardContent(content string) *callback.Card {
	if content == "" {
		return nil
	}

	var m map[string]any
	if err := jsonx.UnmarshalFromString(content, &m); err != nil {
		l.Errorf("failed to unmarshal the card content, content: %s, error: %+v", content, err)
		return nil
	}

	return &callback.Card{
		Type: string(cardTypeOfRaw),
		Data: m,
	}
}

func (l *releaseDeviceLogic) makeFailedResp(err error) *callback.CardActionTriggerResponse {
	zhCN := fmt.Sprintf("释放设备失败，原因：%s", err.Error())
	enUS := fmt.Sprintf("failed to release device, error: %+v", err)

	return &callback.CardActionTriggerResponse{
		Toast: &callback.Toast{
			Type:    string(toastTypeOfError),
			Content: zhCN,
			I18nContent: map[string]string{
				larkapplication.I18nKeyZhCn: zhCN,
				larkapplication.I18nKeyEnUs: enUS,
			},
		},
	}
}

func (l *releaseDeviceLogic) makeSucceedResp(value *releaseDeviceValue) *callback.CardActionTriggerResponse {
	zhCN := fmt.Sprintf("释放设备[%s]成功", value.DeviceName)
	enUS := fmt.Sprintf("succeed to release device, udid: %s, name: %s", value.DeviceID, value.DeviceName)

	return &callback.CardActionTriggerResponse{
		Toast: &callback.Toast{
			Type:    string(toastTypeOfSuccess),
			Content: zhCN,
			I18nContent: map[string]string{
				larkapplication.I18nKeyZhCn: zhCN,
				larkapplication.I18nKeyEnUs: enUS,
			},
		},
		Card: l.updateCardContent(l.getCardContent(value)),
	}
}
