package webhook

import (
	"testing"

	"github.com/devfeel/mapper"
	"github.com/zeromicro/go-zero/core/jsonx"
)

func TestUnmarshalCardContent(t *testing.T) {
	content := `{"title":"设备状态为「使用中」已超过2h0m0s","elements":[[{"tag":"text","text":"#️⃣ 设备ID："},{"tag":"text","text":"device_id:2"},{"tag":"text","text":"📱 设备名称："},{"tag":"text","text":"设备2"}],[{"tag":"text","text":"⭕️ 前一个状态："},{"tag":"text","text":"空闲中"},{"tag":"text","text":"❌ 当前状态："},{"tag":"text","text":"使用中"}],[{"tag":"text","text":"🔄 持续时间："},{"tag":"text","text":"2h0m0s"},{"tag":"text","text":"🔢 告警次数："},{"tag":"text","text":"第1次"}],[{"tag":"text","text":"🕐 故障时间："},{"tag":"text","text":"2024-09-29 17:17:12"},{"tag":"text","text":" "}],[{"tag":"hr"}],[{"tag":"text","text":"📋 告警内容："},{"tag":"text","text":"\\nAndroid真机「device_id:2 : 设备2」状态从「空闲中」变为「使用中」已持续2h0m0s"}],[{"tag":"hr"}],[{"tag":"button","text":"释放设备","type":"default"}]]}`
	var m map[string]any
	err := jsonx.UnmarshalFromString(content, &m)
	if err != nil {
		t.Fatal(err)
	}

	t.Logf("message card: %+v", m)

	elements, ok := m[keyOfElements]
	if !ok {
		t.Errorf("not found key: %s", keyOfElements)
		return
	}

	es, ok := elements.([]any)
	if !ok {
		t.Errorf("not match type, expected []any, but got %T", elements)
		return
	}
	if len(es) < 2 {
		return
	}

	m[keyOfElements] = es[0 : len(es)-2]

	t.Logf("message card: %+v", m)
}

func TestUnmarshalReleaseDeviceValue(t *testing.T) {
	in := map[string]any{
		"callback_type":     "ReleaseType",
		"platform_type":     "Android",
		"device_type":       "云手机",
		"device_id":         "03a2a56dbb1148488bc3b3c1519d4b56",
		"device_name":       "cph-hw-bj-TT-game-1-00005",
		"previous_state":    "IDLE",
		"state":             "IN_USE",
		"previous_state_zh": "空闲中",
		"state_zh":          "使用中",
		"token":             "xxx",
		"rule_duration":     "2h0m0s",
		"duration":          "2h0m29s",
		"times":             "1",
		"updated_at":        "2024-09-30 11:04:11",
	}
	var out releaseDeviceValue
	if err := mapper.MapperMap(in, &out); err != nil {
		t.Fatal(err)
	}

	t.Logf("value: %s", jsonx.MarshalIgnoreError(out))
}
