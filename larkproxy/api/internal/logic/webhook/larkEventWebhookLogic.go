package webhook

import (
	"context"
	"time"

	larkevent "github.com/larksuite/oapi-sdk-go/v3/event"
	"github.com/larksuite/oapi-sdk-go/v3/event/dispatcher"
	larkim "github.com/larksuite/oapi-sdk-go/v3/service/im/v1"
	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworkerv2/base"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	commonlark "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/lark"
	commonpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/larkproxy/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/larkproxy/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/larkproxy/rpc/pb"
)

var (
	errorOfNullEvent   = errors.New("got a null event from Lark server")
	errorOfEmptyChatID = errors.New("got an empty chat ID from Lark server")
)

type LarkEventWebhookLogic struct {
	*BaseLogic
}

func NewLarkEventWebhookLogic(ctx context.Context, svcCtx *svc.ServiceContext) *LarkEventWebhookLogic {
	return &LarkEventWebhookLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *LarkEventWebhookLogic) LarkEventWebhook(req *types.LarkEventWebhookReq) *types.LarkEventWebhookResp {
	var (
		verificationToken = l.svcCtx.Config.LarkCustomApp.VerificationToken
		encryptKey        = l.svcCtx.Config.LarkCustomApp.EventEncryptKey
	)

	d := dispatcher.NewEventDispatcher(verificationToken, encryptKey).
		OnP1MessageReceiveV1(l.messageReceiveV1).
		OnP1MessageReadV1(l.messageReadV1).
		OnP2MessageReceiveV1(l.messageReceiveV2).
		OnP2MessageReadV1(l.messageReadV2).
		OnP2ChatDisbandedV1(l.chatDisbanded).
		OnP2ChatUpdatedV1(l.chatUpdated).
		OnP2ChatMemberBotDeletedV1(l.chatMemberBotDeleted)
	d.InitConfig(larkevent.WithLogger(commonlark.NewLogger(l.ctx)))

	return d.Handle(l.ctx, req)
}

func (l *LarkEventWebhookLogic) messageReceiveV1(ctx context.Context, event *larkim.P1MessageReceiveV1) error {
	// handling the message event, here simply printing the content of the message
	l.Infof("got a callback event, type: message, event: %s", jsonx.MarshalIgnoreError(event))
	return nil
}

func (l *LarkEventWebhookLogic) messageReadV1(ctx context.Context, event *larkim.P1MessageReadV1) error {
	// handling the message event, here simply printing the content of the message
	l.Infof("got a callback event, type: message_read, event: %s", jsonx.MarshalIgnoreError(event))
	return nil
}

func (l *LarkEventWebhookLogic) messageReceiveV2(ctx context.Context, event *larkim.P2MessageReceiveV1) error {
	// handling the message event, here simply printing the content of the message
	l.Infof("got a callback event, type: im.message.receive_v1, event: %s", jsonx.MarshalIgnoreError(event))
	return nil
}

func (l *LarkEventWebhookLogic) messageReadV2(ctx context.Context, event *larkim.P2MessageReadV1) error {
	// handling the message event, here simply printing the content of the message
	l.Infof("got a callback event, type: im.message.message_read_v1, event: %s", jsonx.MarshalIgnoreError(event))
	return nil
}

func (l *LarkEventWebhookLogic) chatDisbanded(ctx context.Context, event *larkim.P2ChatDisbandedV1) error {
	l.Infof("got a callback event, type: im.chat.disbanded_v1, event: %s", jsonx.MarshalIgnoreError(event))

	if event == nil || event.Event == nil {
		return errorOfNullEvent
	}

	info, err := l.convertToChatDisbandedTaskInfo(event.Event)
	if err != nil {
		return err
	}

	payload := protobuf.MarshalJSONIgnoreError(info)
	if _, err = l.svcCtx.Producer.Send(
		ctx,
		base.NewTask(
			constants.MQTaskTypeLarkProxyChatDisbanded,
			payload,
			base.WithRetentionOptions(24*time.Hour),
		),
		base.QueuePriorityDefault,
	); err != nil {
		l.Errorf(
			"failed to send task to mq, type: %s, payload: %s, error: %+v",
			constants.MQTaskTypeLarkProxyChatDisbanded, payload, err,
		)
		return errors.New("failed to send the chat disbanded task")
	}

	return nil
}

func (l *LarkEventWebhookLogic) chatUpdated(ctx context.Context, event *larkim.P2ChatUpdatedV1) error {
	l.Infof("got a callback event, type: im.chat.updated_v1, event: %s", jsonx.MarshalIgnoreError(event))

	if event == nil || event.Event == nil {
		return errorOfNullEvent
	}

	info, err := l.convertToChatUpdatedTaskInfo(event.Event)
	if err != nil {
		return err
	}

	payload := protobuf.MarshalJSONIgnoreError(info)
	if _, err = l.svcCtx.Producer.Send(
		ctx, base.NewTask(
			constants.MQTaskTypeLarkProxyChatUpdated,
			payload,
			base.WithRetentionOptions(time.Hour),
		), base.QueuePriorityDefault,
	); err != nil {
		l.Errorf(
			"failed to send task to mq, type: %s, payload: %s, error: %+v",
			constants.MQTaskTypeLarkProxyChatUpdated, payload, err,
		)
		return errors.New("failed to send the chat updated task")
	}

	return nil
}

func (l *LarkEventWebhookLogic) chatMemberBotDeleted(
	ctx context.Context, event *larkim.P2ChatMemberBotDeletedV1,
) error {
	l.Infof("got a callback event, type: im.chat.member.bot_deleted_v1, event: %s", jsonx.MarshalIgnoreError(event))

	if event == nil || event.Event == nil {
		return errorOfNullEvent
	}

	info, err := l.convertToChatBotDeletedTaskInfo(event.Event)
	if err != nil {
		return err
	}

	payload := protobuf.MarshalJSONIgnoreError(info)
	if _, err = l.svcCtx.Producer.Send(
		ctx, base.NewTask(
			constants.MQTaskTypeLarkProxyChatBotDeleted,
			payload,
			base.WithRetentionOptions(time.Hour),
		), base.QueuePriorityDefault,
	); err != nil {
		l.Errorf(
			"failed to send task to mq, type: %s, payload: %s, error: %+v",
			constants.MQTaskTypeLarkProxyChatBotDeleted, payload, err,
		)
		return errors.New("failed to send the chat bot deleted task")
	}

	return nil
}

func (l *LarkEventWebhookLogic) convertToChatDisbandedTaskInfo(data *larkim.P2ChatDisbandedV1Data) (
	*commonpb.LarkChatDisbandedTaskInfo, error,
) {
	if data == nil {
		return nil, errorOfNullEvent
	}

	if data.ChatId == nil || *data.ChatId == "" {
		return nil, errorOfEmptyChatID
	}

	info := &commonpb.LarkChatDisbandedTaskInfo{
		ChatId: *data.ChatId,
	}

	if data.Name != nil {
		info.Name = *data.Name
	}
	if data.External != nil {
		info.External = *data.External
	}

	return info, nil
}

func (l *LarkEventWebhookLogic) convertToChatUpdatedTaskInfo(data *larkim.P2ChatUpdatedV1Data) (
	*commonpb.LarkChatUpdatedTaskInfo, error,
) {
	if data == nil {
		return nil, errorOfNullEvent
	}

	if data.ChatId == nil || *data.ChatId == "" {
		return nil, errorOfEmptyChatID
	}

	out, err := l.svcCtx.IMChatsRPC.GetChat(
		l.ctx, &pb.GetChatReq{
			ChatId: *data.ChatId,
		},
	)
	if err != nil {
		return nil, err
	} else if out.GetData() == nil {
		return nil, errors.Errorf("failed to get chat info, chat_id: %s", *data.ChatId)
	}

	chat := out.GetData()
	return &commonpb.LarkChatUpdatedTaskInfo{
		ChatId:      *data.ChatId,
		Avatar:      chat.GetAvatar(),
		Name:        chat.GetName(),
		Description: chat.GetDescription(),
		External:    chat.GetExternal(),
	}, nil
}

func (l *LarkEventWebhookLogic) convertToChatBotDeletedTaskInfo(data *larkim.P2ChatMemberBotDeletedV1Data) (
	*commonpb.LarkChatBotDeletedTaskInfo, error,
) {
	if data == nil {
		return nil, errorOfNullEvent
	}

	if data.ChatId == nil || *data.ChatId == "" {
		return nil, errorOfEmptyChatID
	}

	info := &commonpb.LarkChatBotDeletedTaskInfo{
		ChatId: *data.ChatId,
	}

	if data.Name != nil {
		info.Name = *data.Name
	}
	if data.External != nil {
		info.External = *data.External
	}

	return info, nil
}
