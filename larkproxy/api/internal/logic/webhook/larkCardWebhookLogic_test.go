package webhook

import (
	"context"
	"testing"

	larkevent "github.com/larksuite/oapi-sdk-go/v3/event"
	"github.com/zeromicro/go-zero/core/logx"

	commonlark "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/lark"
)

func TestLarkLogger(t *testing.T) {
	ctx := context.Background()
	l1 := logx.WithContext(ctx)
	l2 := commonlark.NewLogger(ctx)
	l3 := &commonlark.Logger{
		Logger: l1,
	}

	l1.Info("logx logger")
	l2.Info(ctx, "lark logger")
	l3.Info(ctx, "lark logger with logx logger")
	t.Log("testing logger")
}

func TestCardWebhook_EventDecrypt(t *testing.T) {
	encrypt := "6IsXFIAr0YdnBBAyZFbb9ArZ2uhRAsY/OlBiEvks3h1tDzCpNmXOBNEpZVHaJL57tpbLlKwLaIhs7QJ3bnHgLXmJH7vO5vKkBW0/YLTXxRqtbrhyrn2OF6O+D4qvVfi9b5w9gmBpyQMPnjeao93+jjQSy+I1umkVj31b0K4Pzuza22xaV/J2EYinzGhCWqAjkPG2yRqOLmR0x3G8onH0HvsUdmkgbWMHkcGQE/hzq8ww8E0p+LmZOUj+SBzWNyg+yVWORznFFC6L7G921J98GFrYwbelCH6Ip6TABKXwGhwKvmwpnAKQvpjyxwZU5shcMYhP/jZRPPlz3jWrg7TQ3cauGf5NhLJslKS537xk6vxj4Qz2FqJJjS50COjPhr8HwkArhHm/HvfGQpMuXZ40dwG9PlrdMrp4+PepbTHrGxpB4ZdlONvBg3kqqChv/VAE/kp7TF4w2Fy6auHJGeVF1/5c7lsT2f39+Ge0vQANt4iltn14A53TYhHHfy/6Tp5q673H4c4MDYh8irHQ61bFKha7K3HNWnLnk8HNlbL9PN0p4ue74o3H0yrIB5D3U/pzyxZK9SBZlb9V3NDqaj0tq4B9aF0YTXnf69JRm06CXPDTA2IiaGPVRV68O2SVvI55RNenZqMCM8ij+JiUViw6MMsSMq1DPbbyDsErwz2qv/AP89h3VClKkm3Me/GuCm1orVW8S3QvLz6AxueVyJND1/kjZsgp7Hr9DyNUO9xA481+qu1oychQERCqKjtLkHDJ0jt2UuDaImyMAT7Yw3kbuNN501AIaTBV50oPqJTjO0Gra9rx3XSzi/OHBN6nYqUTzX4tHjXaHKvlfG3SF72GEI1KQ3GELiZMzi8gRe28zpB6m9CUus4TthSfo78CD2/Eikfk9CB+nhO1PEKXrOi2CHahn4NX0gJ61kDR7tTiCxzRZgF7AUPjaCeGYu4WYHmBzeeYTOWMiCMzuWWXyCqqK4ghhsCOTKStg1aWTZsZMYc="
	key := "0iclKohbbknR0Uq0Dz7hBdJcQQHwrMZA"
	decrypt, err := larkevent.EventDecrypt(encrypt, key)
	if err != nil {
		t.Fatal(err)
	}
	t.Logf("decrypt: %s", decrypt)
}
