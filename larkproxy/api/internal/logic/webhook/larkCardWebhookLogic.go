package webhook

import (
	"context"

	larkcard "github.com/larksuite/oapi-sdk-go/v3/card"
	larkevent "github.com/larksuite/oapi-sdk-go/v3/event"
	"github.com/larksuite/oapi-sdk-go/v3/event/dispatcher"
	"github.com/larksuite/oapi-sdk-go/v3/event/dispatcher/callback"
	"github.com/zeromicro/go-zero/core/jsonx"

	commonlark "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/lark"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/larkproxy/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/larkproxy/api/internal/types"
)

type LarkCardWebhookLogic struct {
	*BaseLogic
}

func NewLarkCardWebhookLogic(ctx context.Context, svcCtx *svc.ServiceContext) *LarkCardWebhookLogic {
	return &LarkCardWebhookLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// LarkCardWebhook 卡片回传交互
// https://open.feishu.cn/document/uAjLw4CM/ukzMukzMukzM/feishu-cards/card-callback-communication#4e84d1ba
func (l *LarkCardWebhookLogic) LarkCardWebhook(req *types.LarkCardWebhookReq) *types.LarkCardWebhookResp {
	h := dispatcher.NewEventDispatcher(
		l.svcCtx.Config.LarkCustomApp.VerificationToken, l.svcCtx.Config.LarkCustomApp.EventEncryptKey,
	).OnP2CardActionTrigger(
		func(ctx context.Context, event *callback.CardActionTriggerEvent) (
			resp *callback.CardActionTriggerResponse, err error,
		) {
			// handling the message event, here simply printing the content of the message
			l.Infof("got a card callback event, type: card.action.trigger, event: %s", jsonx.MarshalIgnoreError(event))
			defer func() {
				if resp != nil {
					l.Infof(
						"return a card callback response, type: card.action.trigger, resp: %s",
						jsonx.MarshalIgnoreError(resp),
					)
				}
			}()

			if event == nil || event.Event == nil || event.Event.Action == nil || event.Event.Context == nil {
				return l.makeNullEventResp(), nil
			}

			return l.doHandle(event.Event.Context.OpenChatID, event.Event.Context.OpenMessageID, event.Event.Action)
		},
	).OnP2CardURLPreviewGet(
		func(ctx context.Context, event *callback.URLPreviewGetEvent) (*callback.URLPreviewGetResponse, error) {
			// handling the message event, here simply printing the content of the message
			l.Infof("got a card callback event, type: url.preview.get, event: %s", jsonx.MarshalIgnoreError(event))
			return nil, nil
		},
	)
	h.InitConfig(larkevent.WithLogger(commonlark.NewLogger(l.ctx)))

	return h.Handle(l.ctx, req)
}

// LarkCardWebhookV1 消息卡片回传交互（旧）
// https://open.feishu.cn/document/ukTMukTMukTM/uYzM3QjL2MzN04iNzcDN/configuring-card-callbacks/card-callback-structure#4e84d1ba
// Deprecated: use LarkCardWebhook instead
func (l *LarkCardWebhookLogic) LarkCardWebhookV1(req *types.LarkCardWebhookReq) *types.LarkCardWebhookResp {
	h := larkcard.NewCardActionHandler(
		l.svcCtx.Config.LarkCustomApp.VerificationToken, l.svcCtx.Config.LarkCustomApp.EventEncryptKey,
		func(ctx context.Context, action *larkcard.CardAction) (any, error) {
			l.Infof("got a card callback action, action: %s", jsonx.MarshalIgnoreError(action))

			if action == nil || action.Action == nil || action.Action.Value == nil {
				return l.makeNullEventResp(), nil
			}

			return l.doHandle(
				action.OpenChatId, action.OpenMessageID, &callback.CallBackAction{
					Value:      action.Action.Value,
					Tag:        action.Action.Tag,
					Option:     action.Action.Option,
					Timezone:   action.Action.Timezone,
					Name:       action.Action.Name,
					FormValue:  action.Action.FormValue,
					InputValue: action.Action.InputValue,
					Options:    action.Action.Options,
					Checked:    action.Action.Checked,
				},
			)
		},
	)
	h.InitConfig(larkevent.WithLogger(commonlark.NewLogger(l.ctx)))

	return h.Handle(l.ctx, req)
}
