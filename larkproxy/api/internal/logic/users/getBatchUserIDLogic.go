package users

import (
	"context"

	larkcontact "github.com/larksuite/oapi-sdk-go/v3/service/contact/v3"
	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/larkproxy/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/larkproxy/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/larkproxy/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/larkproxy/rpc/pb"
)

type GetBatchUserIDLogic struct {
	*BaseLogic
}

func NewGetBatchUserIDLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetBatchUserIDLogic {
	return &GetBatchUserIDLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx, common.ConstLarkAPIPathOfListUsers),
	}
}

func (l *GetBatchUserIDLogic) GetBatchUserID(req *types.GetBatchUserIDReq) (resp *types.GetBatchUserIDResp, err error) {
	out, err := l.svcCtx.ContactUserRPC.GetBatchUserID(
		l.ctx, &pb.GetBatchUserIDReq{
			Emails: req.Emails,
		},
	)
	if err != nil {
		return nil, err
	}

	resp = &types.GetBatchUserIDResp{Items: make([]*larkcontact.UserContactInfo, 0, len(out.GetItems()))}
	if err = utils.Copy(resp, out, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy data to response, data: %s, error: %+v",
			protobuf.MarshalJSONIgnoreError(out), err,
		)
	}

	return resp, nil
}
