package users

import (
	"context"
	"time"

	lark "github.com/larksuite/oapi-sdk-go/v3"
	"github.com/zeromicro/go-zero/core/logx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/userinfo"
	commonutils "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	commonlark "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/lark"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/larkproxy/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/larkproxy/common"
)

type BaseLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext

	apiPath    common.LarkAPIPath
	larkClient *lark.Client

	currentUser *userinfo.UserInfo

	converters []commonutils.TypeConverter
}

func newBaseLogic(ctx context.Context, svcCtx *svc.ServiceContext, apiPath common.LarkAPIPath) *BaseLogic {
	return &BaseLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,

		apiPath: apiPath,
		larkClient: lark.NewClient(
			svcCtx.Config.LarkCustomApp.AppID,
			svcCtx.Config.LarkCustomApp.AppSecret,
			lark.WithEnableTokenCache(true),
			lark.WithLogger(commonlark.NewLogger(ctx)),
			lark.WithReqTimeout(time.Duration(svcCtx.Config.Timeout)*time.Millisecond),
		),

		currentUser: userinfo.FromContext(ctx),

		converters: []commonutils.TypeConverter{},
	}
}
