package chats

import (
	"context"

	larkim "github.com/larksuite/oapi-sdk-go/v3/service/im/v1"
	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/larkproxy/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/larkproxy/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/larkproxy/rpc/pb"
)

type GetIMChatsLogic struct {
	*BaseLogic
}

func NewGetIMChatsLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetIMChatsLogic {
	return &GetIMChatsLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *GetIMChatsLogic) GetIMChats(req *types.GetIMChatsReq) (resp *types.GetIMChatsResp, err error) {
	out, err := l.svcCtx.IMChatsRPC.ListChat(
		l.ctx, &pb.ListChatReq{
			WithCurrentUser: req.WithCurrentUser,
		},
	)
	if err != nil {
		return nil, err
	}

	resp = &types.GetIMChatsResp{Items: make([]*larkim.ListChat, 0, len(out.GetItems()))}
	if err = utils.Copy(resp, out, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy data to response, data: %s, error: %+v",
			protobuf.MarshalJSONIgnoreError(out), err,
		)
	}

	return resp, nil
}
