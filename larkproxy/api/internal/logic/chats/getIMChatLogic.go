package chats

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/larkproxy/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/larkproxy/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/larkproxy/rpc/pb"
)

type GetIMChatLogic struct {
	*BaseLogic
}

func NewGetIMChatLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetIMChatLogic {
	return &GetIMChatLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *GetIMChatLogic) GetIMChat(req *types.GetIMChatReq) (resp *types.GetIMChatResp, err error) {
	out, err := l.svcCtx.IMChatsRPC.GetChat(
		l.ctx, &pb.GetChatReq{
			ChatId: req.ChatId,
		},
	)
	if err != nil {
		return nil, err
	}

	resp = &types.GetIMChatResp{Chat: &types.Chat{ChatId: req.ChatId}}
	if err = utils.Copy(resp.Chat, out.GetData(), l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy data to response, data: %s, error: %+v",
			protobuf.MarshalJSONIgnoreError(out), err,
		)
	}

	return resp, nil
}
