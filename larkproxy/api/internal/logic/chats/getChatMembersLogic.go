package chats

import (
	"context"

	larkim "github.com/larksuite/oapi-sdk-go/v3/service/im/v1"
	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/larkproxy/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/larkproxy/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/larkproxy/rpc/pb"
)

type GetChatMembersLogic struct {
	*BaseLogic
}

func NewGetChatMembersLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetChatMembersLogic {
	return &GetChatMembersLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *GetChatMembersLogic) GetChatMembers(req *types.GetChatMembersReq) (resp *types.GetChatMembersResp, err error) {
	out, err := l.svcCtx.IMChatMembersRPC.GetChatMembers(
		l.ctx, &pb.GetChatMembersReq{
			ChatId: req.ChatId,
		},
	)
	if err != nil {
		return nil, err
	}

	resp = &types.GetChatMembersResp{Items: make([]*larkim.ListMember, 0, len(out.GetItems()))}
	if err = utils.Copy(resp, out, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy data to response, data: %s, error: %+v",
			protobuf.MarshalJSONIgnoreError(out), err,
		)
	}

	return resp, nil
}
