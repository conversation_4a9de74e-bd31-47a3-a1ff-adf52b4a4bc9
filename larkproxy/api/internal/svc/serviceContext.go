package svc

import (
	"github.com/zeromicro/go-zero/zrpc"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/interceptors/clientinterceptors"
	producerv2 "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworkerv2/producer"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/larkproxy/api/internal/config"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/larkproxy/common/zrpc/devicehub"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/larkproxy/rpc/client/larkcontactuserservice"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/larkproxy/rpc/client/larkimchatmembersservice"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/larkproxy/rpc/client/larkimchatsservice"
)

type ServiceContext struct {
	Config config.Config

	Validator *utils.Validator

	DeviceHubRPC     *devicehub.DeviceServiceRPC
	ContactUserRPC   larkcontactuserservice.LarkContactUserService
	IMChatMembersRPC larkimchatmembersservice.LarkIMChatMembersService
	IMChatsRPC       larkimchatsservice.LarkIMChatsService

	Producer *producerv2.Producer
}

func NewServiceContext(c config.Config) *ServiceContext {
	return &ServiceContext{
		Config: c,

		Validator: utils.NewValidator(c.Validator.Locale),

		// RPC
		DeviceHubRPC: devicehub.NewDeviceServiceRPC(c.DeviceHub),
		ContactUserRPC: larkcontactuserservice.NewLarkContactUserService(
			zrpc.MustNewClient(
				c.LarkProxy, clientinterceptors.UnaryUserInfoClientOption(),
			),
		),
		IMChatMembersRPC: larkimchatmembersservice.NewLarkIMChatMembersService(
			zrpc.MustNewClient(
				c.LarkProxy, clientinterceptors.UnaryUserInfoClientOption(),
			),
		),
		IMChatsRPC: larkimchatsservice.NewLarkIMChatsService(
			zrpc.MustNewClient(
				c.LarkProxy, clientinterceptors.UnaryUserInfoClientOption(),
			),
		),

		// MQ
		Producer: producerv2.NewProducer(c.Producer),
	}
}
