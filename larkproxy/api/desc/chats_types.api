syntax = "v1"

type (
    Chat {}

    GetIMChatReq {
        ChatId string `form:"chat_id" validate:"required,startswith=oc_"`
    }
    GetIMChatResp {
        *Chat
    }
)

type (
    ListChat {}

    GetIMChatsReq {
        WithCurrentUser bool `form:"with_current_user,optional"`
    }
    GetIMChatsResp {
        Items []*ListChat `json:"items"`
    }
)

type (
    ListMember {}

    GetChatMembersReq {
        ChatId string `form:"chat_id" validate:"required,startswith=oc_"`
    }
    GetChatMembersResp {
        Items []*ListMember `json:"items"`
    }
)
