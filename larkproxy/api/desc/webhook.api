syntax = "v1"

@server (
    prefix: larkproxy/v1
    group: webhook
)
service larkProxy {
    @handler larkEventWebhook
    post /webhook/event (LarkEventWebhookReq) returns (LarkEventWebhookResp)

    @handler larkCardWebhook
    post /webhook/card (LarkCardWebhookReq) returns (LarkCardWebhookResp)
}

type (
    LarkEventWebhookReq{}
    LarkEventWebhookResp{}
)

type (
    LarkCardWebhookReq{}
    LarkCardWebhookResp{}
)
