package devicehub

import (
	"context"

	"github.com/zeromicro/go-zero/zrpc"
	"google.golang.org/grpc"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/interceptors/clientinterceptors"

	client "gitlab.ttyuyin.com/TestDevelopment/probe-backend/devicehub/rpc/client/deviceservice"
)

type DeviceServiceRPC struct {
	conf zrpc.RpcClientConf

	client client.DeviceService
}

func NewDeviceServiceRPC(c zrpc.RpcClientConf) *DeviceServiceRPC {
	return &DeviceServiceRPC{
		conf:   c,
		client: client.NewDeviceService(zrpc.MustNewClient(c, clientinterceptors.UnaryUserInfoClientOption())),
	}
}

func (c *DeviceServiceRPC) ReleaseDevice(
	ctx context.Context, req *client.ReleaseDeviceReq, opts ...grpc.CallOption,
) (resp *client.ReleaseDeviceResp, err error) {
	return c.client.ReleaseDevice(ctx, req, opts...)
}
