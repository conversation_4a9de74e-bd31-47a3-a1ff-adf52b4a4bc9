package common

const (
	ConstIMChatsSize     = 32
	ConstChatMembersSize = 64
)

type LarkAPIPath string

const (
	ConstLarkAPIPathOfListUsers LarkAPIPath = "GET /open-apis/contact/v3/users/batch_get_id"

	ConstLarkAPIPathOfGetMessage LarkAPIPath = "GET /open-apis/im/v1/messages/:message_id"

	ConstLarkAPIPathOfCreateChat LarkAPIPath = "POST /open-apis/im/v1/chats"            // 创建群
	ConstLarkAPIPathOfDeleteChat LarkAPIPath = "DELETE /open-apis/im/v1/chats/:chat_id" // 解散群
	ConstLarkAPIPathOfGetChat    LarkAPIPath = "GET /open-apis/im/v1/chats/:chat_id"    // 获取群信息
	ConstLarkAPIPathOfListChats  LarkAPIPath = "GET /open-apis/im/v1/chats"             // 获取用户或机器人所在的群列表

	ConstLarkAPIPathOfCreateChatMembers LarkAPIPath = "POST /open-apis/im/v1/chats/:chat_id/members" // 将用户或机器人拉入群聊
	ConstLarkAPIPathOfListMembers       LarkAPIPath = "GET /open-apis/im/v1/chats/:chat_id/members"  // 获取群成员列表

	ConstLarkAPIPathOfCreateChatManagers LarkAPIPath = "POST /open-apis/im/v1/chats/:chat_id/managers/add_managers" // 指定群管理员
)
