// Code generated by goctl. DO NOT EDIT.
// goctl 1.7.5
// Source: larkproxy.proto

package larkimchatsservice

import (
	"context"

	"github.com/zeromicro/go-zero/zrpc"
	"google.golang.org/grpc"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/larkproxy/rpc/pb"
)

type (
	CreateChatManagersReq  = pb.CreateChatManagersReq
	CreateChatManagersResp = pb.CreateChatManagersResp
	CreateChatMembersReq   = pb.CreateChatMembersReq
	CreateChatMembersResp  = pb.CreateChatMembersResp
	CreateChatReq          = pb.CreateChatReq
	CreateChatResp         = pb.CreateChatResp
	DeleteChatMembersReq   = pb.DeleteChatMembersReq
	DeleteChatMembersResp  = pb.DeleteChatMembersResp
	DeleteChatReq          = pb.DeleteChatReq
	DeleteChatResp         = pb.DeleteChatResp
	GetBatchUserIDReq      = pb.GetBatchUserIDReq
	GetBatchUserIDResp     = pb.GetBatchUserIDResp
	GetChatMembersReq      = pb.GetChatMembersReq
	GetChatMembersResp     = pb.GetChatMembersResp
	GetChatReq             = pb.GetChatReq
	GetChatResp            = pb.GetChatResp
	ListChatReq            = pb.ListChatReq
	ListChatResp           = pb.ListChatResp

	LarkIMChatsService interface {
		// CreateChat 创建群
		CreateChat(ctx context.Context, in *CreateChatReq, opts ...grpc.CallOption) (*CreateChatResp, error)
		// DeleteChat 解散群
		DeleteChat(ctx context.Context, in *DeleteChatReq, opts ...grpc.CallOption) (*DeleteChatResp, error)
		// GetChat 获取群信息
		GetChat(ctx context.Context, in *GetChatReq, opts ...grpc.CallOption) (*GetChatResp, error)
		// ListChat 获取用户或机器人所在的群列表
		ListChat(ctx context.Context, in *ListChatReq, opts ...grpc.CallOption) (*ListChatResp, error)
	}

	defaultLarkIMChatsService struct {
		cli zrpc.Client
	}
)

func NewLarkIMChatsService(cli zrpc.Client) LarkIMChatsService {
	return &defaultLarkIMChatsService{
		cli: cli,
	}
}

// CreateChat 创建群
func (m *defaultLarkIMChatsService) CreateChat(ctx context.Context, in *CreateChatReq, opts ...grpc.CallOption) (*CreateChatResp, error) {
	client := pb.NewLarkIMChatsServiceClient(m.cli.Conn())
	return client.CreateChat(ctx, in, opts...)
}

// DeleteChat 解散群
func (m *defaultLarkIMChatsService) DeleteChat(ctx context.Context, in *DeleteChatReq, opts ...grpc.CallOption) (*DeleteChatResp, error) {
	client := pb.NewLarkIMChatsServiceClient(m.cli.Conn())
	return client.DeleteChat(ctx, in, opts...)
}

// GetChat 获取群信息
func (m *defaultLarkIMChatsService) GetChat(ctx context.Context, in *GetChatReq, opts ...grpc.CallOption) (*GetChatResp, error) {
	client := pb.NewLarkIMChatsServiceClient(m.cli.Conn())
	return client.GetChat(ctx, in, opts...)
}

// ListChat 获取用户或机器人所在的群列表
func (m *defaultLarkIMChatsService) ListChat(ctx context.Context, in *ListChatReq, opts ...grpc.CallOption) (*ListChatResp, error) {
	client := pb.NewLarkIMChatsServiceClient(m.cli.Conn())
	return client.ListChat(ctx, in, opts...)
}
