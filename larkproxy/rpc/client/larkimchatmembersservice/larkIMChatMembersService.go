// Code generated by goctl. DO NOT EDIT.
// goctl 1.7.5
// Source: larkproxy.proto

package larkimchatmembersservice

import (
	"context"

	"github.com/zeromicro/go-zero/zrpc"
	"google.golang.org/grpc"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/larkproxy/rpc/pb"
)

type (
	CreateChatManagersReq  = pb.CreateChatManagersReq
	CreateChatManagersResp = pb.CreateChatManagersResp
	CreateChatMembersReq   = pb.CreateChatMembersReq
	CreateChatMembersResp  = pb.CreateChatMembersResp
	CreateChatReq          = pb.CreateChatReq
	CreateChatResp         = pb.CreateChatResp
	DeleteChatMembersReq   = pb.DeleteChatMembersReq
	DeleteChatMembersResp  = pb.DeleteChatMembersResp
	DeleteChatReq          = pb.DeleteChatReq
	DeleteChatResp         = pb.DeleteChatResp
	GetBatchUserIDReq      = pb.GetBatchUserIDReq
	GetBatchUserIDResp     = pb.GetBatchUserIDResp
	GetChatMembersReq      = pb.GetChatMembersReq
	GetChatMembersResp     = pb.GetChatMembersResp
	GetChatReq             = pb.GetChatReq
	GetChatResp            = pb.GetChatResp
	ListChatReq            = pb.ListChatReq
	ListChatResp           = pb.ListChatResp

	LarkIMChatMembersService interface {
		// CreateChatMembers 将用户或机器人拉入群聊
		CreateChatMembers(ctx context.Context, in *CreateChatMembersReq, opts ...grpc.CallOption) (*CreateChatMembersResp, error)
		// DeleteChatMembers 将用户或机器人移出群聊
		DeleteChatMembers(ctx context.Context, in *DeleteChatMembersReq, opts ...grpc.CallOption) (*DeleteChatMembersResp, error)
		// GetChatMembers 获取群成员列表
		GetChatMembers(ctx context.Context, in *GetChatMembersReq, opts ...grpc.CallOption) (*GetChatMembersResp, error)
	}

	defaultLarkIMChatMembersService struct {
		cli zrpc.Client
	}
)

func NewLarkIMChatMembersService(cli zrpc.Client) LarkIMChatMembersService {
	return &defaultLarkIMChatMembersService{
		cli: cli,
	}
}

// CreateChatMembers 将用户或机器人拉入群聊
func (m *defaultLarkIMChatMembersService) CreateChatMembers(ctx context.Context, in *CreateChatMembersReq, opts ...grpc.CallOption) (*CreateChatMembersResp, error) {
	client := pb.NewLarkIMChatMembersServiceClient(m.cli.Conn())
	return client.CreateChatMembers(ctx, in, opts...)
}

// DeleteChatMembers 将用户或机器人移出群聊
func (m *defaultLarkIMChatMembersService) DeleteChatMembers(ctx context.Context, in *DeleteChatMembersReq, opts ...grpc.CallOption) (*DeleteChatMembersResp, error) {
	client := pb.NewLarkIMChatMembersServiceClient(m.cli.Conn())
	return client.DeleteChatMembers(ctx, in, opts...)
}

// GetChatMembers 获取群成员列表
func (m *defaultLarkIMChatMembersService) GetChatMembers(ctx context.Context, in *GetChatMembersReq, opts ...grpc.CallOption) (*GetChatMembersResp, error) {
	client := pb.NewLarkIMChatMembersServiceClient(m.cli.Conn())
	return client.GetChatMembers(ctx, in, opts...)
}
