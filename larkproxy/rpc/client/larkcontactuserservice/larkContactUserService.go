// Code generated by goctl. DO NOT EDIT.
// goctl 1.7.5
// Source: larkproxy.proto

package larkcontactuserservice

import (
	"context"

	"github.com/zeromicro/go-zero/zrpc"
	"google.golang.org/grpc"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/larkproxy/rpc/pb"
)

type (
	CreateChatManagersReq  = pb.CreateChatManagersReq
	CreateChatManagersResp = pb.CreateChatManagersResp
	CreateChatMembersReq   = pb.CreateChatMembersReq
	CreateChatMembersResp  = pb.CreateChatMembersResp
	CreateChatReq          = pb.CreateChatReq
	CreateChatResp         = pb.CreateChatResp
	DeleteChatMembersReq   = pb.DeleteChatMembersReq
	DeleteChatMembersResp  = pb.DeleteChatMembersResp
	DeleteChatReq          = pb.DeleteChatReq
	DeleteChatResp         = pb.DeleteChatResp
	GetBatchUserIDReq      = pb.GetBatchUserIDReq
	GetBatchUserIDResp     = pb.GetBatchUserIDResp
	GetChatMembersReq      = pb.GetChatMembersReq
	GetChatMembersResp     = pb.GetChatMembersResp
	GetChatReq             = pb.GetChatReq
	GetChatResp            = pb.GetChatResp
	ListChatReq            = pb.ListChatReq
	ListChatResp           = pb.ListChatResp

	LarkContactUserService interface {
		GetBatchUserID(ctx context.Context, in *GetBatchUserIDReq, opts ...grpc.CallOption) (*GetBatchUserIDResp, error)
	}

	defaultLarkContactUserService struct {
		cli zrpc.Client
	}
)

func NewLarkContactUserService(cli zrpc.Client) LarkContactUserService {
	return &defaultLarkContactUserService{
		cli: cli,
	}
}

func (m *defaultLarkContactUserService) GetBatchUserID(ctx context.Context, in *GetBatchUserIDReq, opts ...grpc.CallOption) (*GetBatchUserIDResp, error) {
	client := pb.NewLarkContactUserServiceClient(m.cli.Conn())
	return client.GetBatchUserID(ctx, in, opts...)
}
