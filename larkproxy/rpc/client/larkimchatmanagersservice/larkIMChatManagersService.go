// Code generated by goctl. DO NOT EDIT.
// goctl 1.7.5
// Source: larkproxy.proto

package larkimchatmanagersservice

import (
	"context"

	"github.com/zeromicro/go-zero/zrpc"
	"google.golang.org/grpc"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/larkproxy/rpc/pb"
)

type (
	CreateChatManagersReq  = pb.CreateChatManagersReq
	CreateChatManagersResp = pb.CreateChatManagersResp
	CreateChatMembersReq   = pb.CreateChatMembersReq
	CreateChatMembersResp  = pb.CreateChatMembersResp
	CreateChatReq          = pb.CreateChatReq
	CreateChatResp         = pb.CreateChatResp
	DeleteChatMembersReq   = pb.DeleteChatMembersReq
	DeleteChatMembersResp  = pb.DeleteChatMembersResp
	DeleteChatReq          = pb.DeleteChatReq
	DeleteChatResp         = pb.DeleteChatResp
	GetBatchUserIDReq      = pb.GetBatchUserIDReq
	GetBatchUserIDResp     = pb.GetBatchUserIDResp
	GetChatMembersReq      = pb.GetChatMembersReq
	GetChatMembersResp     = pb.GetChatMembersResp
	GetChatReq             = pb.GetChatReq
	GetChatResp            = pb.GetChatResp
	ListChatReq            = pb.ListChatReq
	ListChatResp           = pb.ListChatResp

	LarkIMChatManagersService interface {
		// CreateChatMembers 指定群管理员
		CreateChatManagers(ctx context.Context, in *CreateChatManagersReq, opts ...grpc.CallOption) (*CreateChatManagersResp, error)
	}

	defaultLarkIMChatManagersService struct {
		cli zrpc.Client
	}
)

func NewLarkIMChatManagersService(cli zrpc.Client) LarkIMChatManagersService {
	return &defaultLarkIMChatManagersService{
		cli: cli,
	}
}

// CreateChatMembers 指定群管理员
func (m *defaultLarkIMChatManagersService) CreateChatManagers(ctx context.Context, in *CreateChatManagersReq, opts ...grpc.CallOption) (*CreateChatManagersResp, error) {
	client := pb.NewLarkIMChatManagersServiceClient(m.cli.Conn())
	return client.CreateChatManagers(ctx, in, opts...)
}
