package server

import (
	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/conf"
	"github.com/zeromicro/go-zero/core/service"
	"github.com/zeromicro/go-zero/zrpc"
	"google.golang.org/grpc"
	"google.golang.org/grpc/reflection"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/interceptors/serverinterceptors"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/log"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/server"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/larkproxy/rpc/internal/config"
	larkcontactuserservice "gitlab.ttyuyin.com/TestDevelopment/probe-backend/larkproxy/rpc/internal/server/larkcontactuserservice"
	larkimchatmanagersservice "gitlab.ttyuyin.com/TestDevelopment/probe-backend/larkproxy/rpc/internal/server/larkimchatmanagersservice"
	larkimchatmembersservice "gitlab.ttyuyin.com/TestDevelopment/probe-backend/larkproxy/rpc/internal/server/larkimchatmembersservice"
	larkimchatsservice "gitlab.ttyuyin.com/TestDevelopment/probe-backend/larkproxy/rpc/internal/server/larkimchatsservice"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/larkproxy/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/larkproxy/rpc/pb"
)

// NewRpcServer for single server startup
func NewRpcServer(configFile string) (*server.Server, error) {
	return server.NewServer(Options(configFile)...)
}

// NewServer for combine server startup
func NewServer(c server.Config, w *log.ZapWriter) (service.Service, server.TearDownFunc, error) {
	cc, ok := c.(config.Config)
	if !ok {
		return nil, nil, errors.Errorf("failed to new rpc server, cause by the config[%T] isn't a rpc config", c)
	}

	ctx := svc.NewServiceContext(cc)
	s := zrpc.MustNewServer(
		cc.RpcServerConf, func(grpcServer *grpc.Server) {
			pb.RegisterLarkContactUserServiceServer(
				grpcServer, larkcontactuserservice.NewLarkContactUserServiceServer(ctx),
			)
			pb.RegisterLarkIMChatMembersServiceServer(
				grpcServer, larkimchatmembersservice.NewLarkIMChatMembersServiceServer(ctx),
			)
			pb.RegisterLarkIMChatsServiceServer(grpcServer, larkimchatsservice.NewLarkIMChatsServiceServer(ctx))
			pb.RegisterLarkIMChatManagersServiceServer(
				grpcServer, larkimchatmanagersservice.NewLarkIMChatManagersServiceServer(ctx),
			)

			if cc.Mode == service.DevMode || cc.Mode == service.TestMode {
				reflection.Register(grpcServer)
			}
		},
	)

	// 需要在 `ServiceConf.SetUp` 后再设置 `Writer`
	log.SetWriter(w)

	tdf := server.TearDownFunc(
		func() {
			s.AddUnaryInterceptors(serverinterceptors.ValidateWithCustomValidatorUnaryServerInterceptor(ctx.Validator))
			s.AddStreamInterceptors(serverinterceptors.ValidateWithCustomValidatorStreamServerInterceptor(ctx.Validator))
		},
	)

	return s, tdf, nil
}

// NewConfig new a config of server
func NewConfig(configFile string) server.Config {
	var c config.Config
	conf.MustLoad(configFile, &c)

	return c
}

// Options as a param of `server.NewServer`
func Options(configFile string) []server.Option {
	return []server.Option{
		server.WithNewConfigFunc(
			func() server.Config {
				return NewConfig(configFile)
			},
		),
		server.WithNewServiceAndTearDownFunc(NewServer),
		//server.WithRpcServerMiddlewaresConf(
		//	server.RpcServerInterceptorsConf{
		//		UnUseUserInfo: true,
		//	},
		//),
	}
}
