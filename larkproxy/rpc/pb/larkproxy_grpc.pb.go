// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.29.2
// source: larkproxy/larkproxy.proto

package pb

import (
	context "context"

	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	LarkContactUserService_GetBatchUserID_FullMethodName = "/larkproxy.LarkContactUserService/GetBatchUserID"
)

// LarkContactUserServiceClient is the client API for LarkContactUserService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// LarkContactUserService 飞书用户服务
type LarkContactUserServiceClient interface {
	GetBatchUserID(ctx context.Context, in *GetBatchUserIDReq, opts ...grpc.CallOption) (*GetBatchUserIDResp, error)
}

type larkContactUserServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewLarkContactUserServiceClient(cc grpc.ClientConnInterface) LarkContactUserServiceClient {
	return &larkContactUserServiceClient{cc}
}

func (c *larkContactUserServiceClient) GetBatchUserID(ctx context.Context, in *GetBatchUserIDReq, opts ...grpc.CallOption) (*GetBatchUserIDResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetBatchUserIDResp)
	err := c.cc.Invoke(ctx, LarkContactUserService_GetBatchUserID_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// LarkContactUserServiceServer is the server API for LarkContactUserService service.
// All implementations must embed UnimplementedLarkContactUserServiceServer
// for forward compatibility.
//
// LarkContactUserService 飞书用户服务
type LarkContactUserServiceServer interface {
	GetBatchUserID(context.Context, *GetBatchUserIDReq) (*GetBatchUserIDResp, error)
	mustEmbedUnimplementedLarkContactUserServiceServer()
}

// UnimplementedLarkContactUserServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedLarkContactUserServiceServer struct{}

func (UnimplementedLarkContactUserServiceServer) GetBatchUserID(context.Context, *GetBatchUserIDReq) (*GetBatchUserIDResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetBatchUserID not implemented")
}
func (UnimplementedLarkContactUserServiceServer) mustEmbedUnimplementedLarkContactUserServiceServer() {
}
func (UnimplementedLarkContactUserServiceServer) testEmbeddedByValue() {}

// UnsafeLarkContactUserServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to LarkContactUserServiceServer will
// result in compilation errors.
type UnsafeLarkContactUserServiceServer interface {
	mustEmbedUnimplementedLarkContactUserServiceServer()
}

func RegisterLarkContactUserServiceServer(s grpc.ServiceRegistrar, srv LarkContactUserServiceServer) {
	// If the following call pancis, it indicates UnimplementedLarkContactUserServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&LarkContactUserService_ServiceDesc, srv)
}

func _LarkContactUserService_GetBatchUserID_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetBatchUserIDReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LarkContactUserServiceServer).GetBatchUserID(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: LarkContactUserService_GetBatchUserID_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LarkContactUserServiceServer).GetBatchUserID(ctx, req.(*GetBatchUserIDReq))
	}
	return interceptor(ctx, in, info, handler)
}

// LarkContactUserService_ServiceDesc is the grpc.ServiceDesc for LarkContactUserService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var LarkContactUserService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "larkproxy.LarkContactUserService",
	HandlerType: (*LarkContactUserServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetBatchUserID",
			Handler:    _LarkContactUserService_GetBatchUserID_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "larkproxy/larkproxy.proto",
}

const (
	LarkIMChatsService_CreateChat_FullMethodName = "/larkproxy.LarkIMChatsService/CreateChat"
	LarkIMChatsService_DeleteChat_FullMethodName = "/larkproxy.LarkIMChatsService/DeleteChat"
	LarkIMChatsService_GetChat_FullMethodName    = "/larkproxy.LarkIMChatsService/GetChat"
	LarkIMChatsService_ListChat_FullMethodName   = "/larkproxy.LarkIMChatsService/ListChat"
)

// LarkIMChatsServiceClient is the client API for LarkIMChatsService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// LarkIMChatsService 飞书群组服务
type LarkIMChatsServiceClient interface {
	// CreateChat 创建群
	CreateChat(ctx context.Context, in *CreateChatReq, opts ...grpc.CallOption) (*CreateChatResp, error)
	// DeleteChat 解散群
	DeleteChat(ctx context.Context, in *DeleteChatReq, opts ...grpc.CallOption) (*DeleteChatResp, error)
	// GetChat 获取群信息
	GetChat(ctx context.Context, in *GetChatReq, opts ...grpc.CallOption) (*GetChatResp, error)
	// ListChat 获取用户或机器人所在的群列表
	ListChat(ctx context.Context, in *ListChatReq, opts ...grpc.CallOption) (*ListChatResp, error)
}

type larkIMChatsServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewLarkIMChatsServiceClient(cc grpc.ClientConnInterface) LarkIMChatsServiceClient {
	return &larkIMChatsServiceClient{cc}
}

func (c *larkIMChatsServiceClient) CreateChat(ctx context.Context, in *CreateChatReq, opts ...grpc.CallOption) (*CreateChatResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateChatResp)
	err := c.cc.Invoke(ctx, LarkIMChatsService_CreateChat_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *larkIMChatsServiceClient) DeleteChat(ctx context.Context, in *DeleteChatReq, opts ...grpc.CallOption) (*DeleteChatResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DeleteChatResp)
	err := c.cc.Invoke(ctx, LarkIMChatsService_DeleteChat_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *larkIMChatsServiceClient) GetChat(ctx context.Context, in *GetChatReq, opts ...grpc.CallOption) (*GetChatResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetChatResp)
	err := c.cc.Invoke(ctx, LarkIMChatsService_GetChat_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *larkIMChatsServiceClient) ListChat(ctx context.Context, in *ListChatReq, opts ...grpc.CallOption) (*ListChatResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListChatResp)
	err := c.cc.Invoke(ctx, LarkIMChatsService_ListChat_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// LarkIMChatsServiceServer is the server API for LarkIMChatsService service.
// All implementations must embed UnimplementedLarkIMChatsServiceServer
// for forward compatibility.
//
// LarkIMChatsService 飞书群组服务
type LarkIMChatsServiceServer interface {
	// CreateChat 创建群
	CreateChat(context.Context, *CreateChatReq) (*CreateChatResp, error)
	// DeleteChat 解散群
	DeleteChat(context.Context, *DeleteChatReq) (*DeleteChatResp, error)
	// GetChat 获取群信息
	GetChat(context.Context, *GetChatReq) (*GetChatResp, error)
	// ListChat 获取用户或机器人所在的群列表
	ListChat(context.Context, *ListChatReq) (*ListChatResp, error)
	mustEmbedUnimplementedLarkIMChatsServiceServer()
}

// UnimplementedLarkIMChatsServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedLarkIMChatsServiceServer struct{}

func (UnimplementedLarkIMChatsServiceServer) CreateChat(context.Context, *CreateChatReq) (*CreateChatResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateChat not implemented")
}
func (UnimplementedLarkIMChatsServiceServer) DeleteChat(context.Context, *DeleteChatReq) (*DeleteChatResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteChat not implemented")
}
func (UnimplementedLarkIMChatsServiceServer) GetChat(context.Context, *GetChatReq) (*GetChatResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetChat not implemented")
}
func (UnimplementedLarkIMChatsServiceServer) ListChat(context.Context, *ListChatReq) (*ListChatResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListChat not implemented")
}
func (UnimplementedLarkIMChatsServiceServer) mustEmbedUnimplementedLarkIMChatsServiceServer() {}
func (UnimplementedLarkIMChatsServiceServer) testEmbeddedByValue()                            {}

// UnsafeLarkIMChatsServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to LarkIMChatsServiceServer will
// result in compilation errors.
type UnsafeLarkIMChatsServiceServer interface {
	mustEmbedUnimplementedLarkIMChatsServiceServer()
}

func RegisterLarkIMChatsServiceServer(s grpc.ServiceRegistrar, srv LarkIMChatsServiceServer) {
	// If the following call pancis, it indicates UnimplementedLarkIMChatsServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&LarkIMChatsService_ServiceDesc, srv)
}

func _LarkIMChatsService_CreateChat_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateChatReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LarkIMChatsServiceServer).CreateChat(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: LarkIMChatsService_CreateChat_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LarkIMChatsServiceServer).CreateChat(ctx, req.(*CreateChatReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _LarkIMChatsService_DeleteChat_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteChatReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LarkIMChatsServiceServer).DeleteChat(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: LarkIMChatsService_DeleteChat_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LarkIMChatsServiceServer).DeleteChat(ctx, req.(*DeleteChatReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _LarkIMChatsService_GetChat_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetChatReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LarkIMChatsServiceServer).GetChat(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: LarkIMChatsService_GetChat_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LarkIMChatsServiceServer).GetChat(ctx, req.(*GetChatReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _LarkIMChatsService_ListChat_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListChatReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LarkIMChatsServiceServer).ListChat(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: LarkIMChatsService_ListChat_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LarkIMChatsServiceServer).ListChat(ctx, req.(*ListChatReq))
	}
	return interceptor(ctx, in, info, handler)
}

// LarkIMChatsService_ServiceDesc is the grpc.ServiceDesc for LarkIMChatsService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var LarkIMChatsService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "larkproxy.LarkIMChatsService",
	HandlerType: (*LarkIMChatsServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateChat",
			Handler:    _LarkIMChatsService_CreateChat_Handler,
		},
		{
			MethodName: "DeleteChat",
			Handler:    _LarkIMChatsService_DeleteChat_Handler,
		},
		{
			MethodName: "GetChat",
			Handler:    _LarkIMChatsService_GetChat_Handler,
		},
		{
			MethodName: "ListChat",
			Handler:    _LarkIMChatsService_ListChat_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "larkproxy/larkproxy.proto",
}

const (
	LarkIMChatMembersService_CreateChatMembers_FullMethodName = "/larkproxy.LarkIMChatMembersService/CreateChatMembers"
	LarkIMChatMembersService_DeleteChatMembers_FullMethodName = "/larkproxy.LarkIMChatMembersService/DeleteChatMembers"
	LarkIMChatMembersService_GetChatMembers_FullMethodName    = "/larkproxy.LarkIMChatMembersService/GetChatMembers"
)

// LarkIMChatMembersServiceClient is the client API for LarkIMChatMembersService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// LarkIMChatMembersService 飞书群组成员服务
type LarkIMChatMembersServiceClient interface {
	// CreateChatMembers 将用户或机器人拉入群聊
	CreateChatMembers(ctx context.Context, in *CreateChatMembersReq, opts ...grpc.CallOption) (*CreateChatMembersResp, error)
	// DeleteChatMembers 将用户或机器人移出群聊
	DeleteChatMembers(ctx context.Context, in *DeleteChatMembersReq, opts ...grpc.CallOption) (*DeleteChatMembersResp, error)
	// GetChatMembers 获取群成员列表
	GetChatMembers(ctx context.Context, in *GetChatMembersReq, opts ...grpc.CallOption) (*GetChatMembersResp, error)
}

type larkIMChatMembersServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewLarkIMChatMembersServiceClient(cc grpc.ClientConnInterface) LarkIMChatMembersServiceClient {
	return &larkIMChatMembersServiceClient{cc}
}

func (c *larkIMChatMembersServiceClient) CreateChatMembers(ctx context.Context, in *CreateChatMembersReq, opts ...grpc.CallOption) (*CreateChatMembersResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateChatMembersResp)
	err := c.cc.Invoke(ctx, LarkIMChatMembersService_CreateChatMembers_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *larkIMChatMembersServiceClient) DeleteChatMembers(ctx context.Context, in *DeleteChatMembersReq, opts ...grpc.CallOption) (*DeleteChatMembersResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DeleteChatMembersResp)
	err := c.cc.Invoke(ctx, LarkIMChatMembersService_DeleteChatMembers_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *larkIMChatMembersServiceClient) GetChatMembers(ctx context.Context, in *GetChatMembersReq, opts ...grpc.CallOption) (*GetChatMembersResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetChatMembersResp)
	err := c.cc.Invoke(ctx, LarkIMChatMembersService_GetChatMembers_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// LarkIMChatMembersServiceServer is the server API for LarkIMChatMembersService service.
// All implementations must embed UnimplementedLarkIMChatMembersServiceServer
// for forward compatibility.
//
// LarkIMChatMembersService 飞书群组成员服务
type LarkIMChatMembersServiceServer interface {
	// CreateChatMembers 将用户或机器人拉入群聊
	CreateChatMembers(context.Context, *CreateChatMembersReq) (*CreateChatMembersResp, error)
	// DeleteChatMembers 将用户或机器人移出群聊
	DeleteChatMembers(context.Context, *DeleteChatMembersReq) (*DeleteChatMembersResp, error)
	// GetChatMembers 获取群成员列表
	GetChatMembers(context.Context, *GetChatMembersReq) (*GetChatMembersResp, error)
	mustEmbedUnimplementedLarkIMChatMembersServiceServer()
}

// UnimplementedLarkIMChatMembersServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedLarkIMChatMembersServiceServer struct{}

func (UnimplementedLarkIMChatMembersServiceServer) CreateChatMembers(context.Context, *CreateChatMembersReq) (*CreateChatMembersResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateChatMembers not implemented")
}
func (UnimplementedLarkIMChatMembersServiceServer) DeleteChatMembers(context.Context, *DeleteChatMembersReq) (*DeleteChatMembersResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteChatMembers not implemented")
}
func (UnimplementedLarkIMChatMembersServiceServer) GetChatMembers(context.Context, *GetChatMembersReq) (*GetChatMembersResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetChatMembers not implemented")
}
func (UnimplementedLarkIMChatMembersServiceServer) mustEmbedUnimplementedLarkIMChatMembersServiceServer() {
}
func (UnimplementedLarkIMChatMembersServiceServer) testEmbeddedByValue() {}

// UnsafeLarkIMChatMembersServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to LarkIMChatMembersServiceServer will
// result in compilation errors.
type UnsafeLarkIMChatMembersServiceServer interface {
	mustEmbedUnimplementedLarkIMChatMembersServiceServer()
}

func RegisterLarkIMChatMembersServiceServer(s grpc.ServiceRegistrar, srv LarkIMChatMembersServiceServer) {
	// If the following call pancis, it indicates UnimplementedLarkIMChatMembersServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&LarkIMChatMembersService_ServiceDesc, srv)
}

func _LarkIMChatMembersService_CreateChatMembers_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateChatMembersReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LarkIMChatMembersServiceServer).CreateChatMembers(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: LarkIMChatMembersService_CreateChatMembers_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LarkIMChatMembersServiceServer).CreateChatMembers(ctx, req.(*CreateChatMembersReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _LarkIMChatMembersService_DeleteChatMembers_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteChatMembersReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LarkIMChatMembersServiceServer).DeleteChatMembers(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: LarkIMChatMembersService_DeleteChatMembers_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LarkIMChatMembersServiceServer).DeleteChatMembers(ctx, req.(*DeleteChatMembersReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _LarkIMChatMembersService_GetChatMembers_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetChatMembersReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LarkIMChatMembersServiceServer).GetChatMembers(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: LarkIMChatMembersService_GetChatMembers_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LarkIMChatMembersServiceServer).GetChatMembers(ctx, req.(*GetChatMembersReq))
	}
	return interceptor(ctx, in, info, handler)
}

// LarkIMChatMembersService_ServiceDesc is the grpc.ServiceDesc for LarkIMChatMembersService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var LarkIMChatMembersService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "larkproxy.LarkIMChatMembersService",
	HandlerType: (*LarkIMChatMembersServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateChatMembers",
			Handler:    _LarkIMChatMembersService_CreateChatMembers_Handler,
		},
		{
			MethodName: "DeleteChatMembers",
			Handler:    _LarkIMChatMembersService_DeleteChatMembers_Handler,
		},
		{
			MethodName: "GetChatMembers",
			Handler:    _LarkIMChatMembersService_GetChatMembers_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "larkproxy/larkproxy.proto",
}

const (
	LarkIMChatManagersService_CreateChatManagers_FullMethodName = "/larkproxy.LarkIMChatManagersService/CreateChatManagers"
)

// LarkIMChatManagersServiceClient is the client API for LarkIMChatManagersService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// LarkIMChatManagersService 飞书群管理员服务
type LarkIMChatManagersServiceClient interface {
	// CreateChatMembers 指定群管理员
	CreateChatManagers(ctx context.Context, in *CreateChatManagersReq, opts ...grpc.CallOption) (*CreateChatManagersResp, error)
}

type larkIMChatManagersServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewLarkIMChatManagersServiceClient(cc grpc.ClientConnInterface) LarkIMChatManagersServiceClient {
	return &larkIMChatManagersServiceClient{cc}
}

func (c *larkIMChatManagersServiceClient) CreateChatManagers(ctx context.Context, in *CreateChatManagersReq, opts ...grpc.CallOption) (*CreateChatManagersResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateChatManagersResp)
	err := c.cc.Invoke(ctx, LarkIMChatManagersService_CreateChatManagers_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// LarkIMChatManagersServiceServer is the server API for LarkIMChatManagersService service.
// All implementations must embed UnimplementedLarkIMChatManagersServiceServer
// for forward compatibility.
//
// LarkIMChatManagersService 飞书群管理员服务
type LarkIMChatManagersServiceServer interface {
	// CreateChatMembers 指定群管理员
	CreateChatManagers(context.Context, *CreateChatManagersReq) (*CreateChatManagersResp, error)
	mustEmbedUnimplementedLarkIMChatManagersServiceServer()
}

// UnimplementedLarkIMChatManagersServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedLarkIMChatManagersServiceServer struct{}

func (UnimplementedLarkIMChatManagersServiceServer) CreateChatManagers(context.Context, *CreateChatManagersReq) (*CreateChatManagersResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateChatManagers not implemented")
}
func (UnimplementedLarkIMChatManagersServiceServer) mustEmbedUnimplementedLarkIMChatManagersServiceServer() {
}
func (UnimplementedLarkIMChatManagersServiceServer) testEmbeddedByValue() {}

// UnsafeLarkIMChatManagersServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to LarkIMChatManagersServiceServer will
// result in compilation errors.
type UnsafeLarkIMChatManagersServiceServer interface {
	mustEmbedUnimplementedLarkIMChatManagersServiceServer()
}

func RegisterLarkIMChatManagersServiceServer(s grpc.ServiceRegistrar, srv LarkIMChatManagersServiceServer) {
	// If the following call pancis, it indicates UnimplementedLarkIMChatManagersServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&LarkIMChatManagersService_ServiceDesc, srv)
}

func _LarkIMChatManagersService_CreateChatManagers_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateChatManagersReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LarkIMChatManagersServiceServer).CreateChatManagers(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: LarkIMChatManagersService_CreateChatManagers_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LarkIMChatManagersServiceServer).CreateChatManagers(ctx, req.(*CreateChatManagersReq))
	}
	return interceptor(ctx, in, info, handler)
}

// LarkIMChatManagersService_ServiceDesc is the grpc.ServiceDesc for LarkIMChatManagersService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var LarkIMChatManagersService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "larkproxy.LarkIMChatManagersService",
	HandlerType: (*LarkIMChatManagersServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateChatManagers",
			Handler:    _LarkIMChatManagersService_CreateChatManagers_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "larkproxy/larkproxy.proto",
}
