// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: larkproxy/im_chats.proto

package pb

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on CreateChatRespData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateChatRespData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateChatRespData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateChatRespDataMultiError, or nil if none found.
func (m *CreateChatRespData) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateChatRespData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ChatId

	// no validation rules for Avatar

	// no validation rules for Name

	// no validation rules for Description

	if len(errors) > 0 {
		return CreateChatRespDataMultiError(errors)
	}

	return nil
}

// CreateChatRespDataMultiError is an error wrapping multiple validation errors
// returned by CreateChatRespData.ValidateAll() if the designated constraints
// aren't met.
type CreateChatRespDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateChatRespDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateChatRespDataMultiError) AllErrors() []error { return m }

// CreateChatRespDataValidationError is the validation error returned by
// CreateChatRespData.Validate if the designated constraints aren't met.
type CreateChatRespDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateChatRespDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateChatRespDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateChatRespDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateChatRespDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateChatRespDataValidationError) ErrorName() string {
	return "CreateChatRespDataValidationError"
}

// Error satisfies the builtin error interface
func (e CreateChatRespDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateChatRespData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateChatRespDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateChatRespDataValidationError{}

// Validate checks the field values on GetChatRespData with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *GetChatRespData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetChatRespData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetChatRespDataMultiError, or nil if none found.
func (m *GetChatRespData) ValidateAll() error {
	return m.validate(true)
}

func (m *GetChatRespData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ChatId

	// no validation rules for Avatar

	// no validation rules for Name

	// no validation rules for Description

	// no validation rules for External

	// no validation rules for Status

	if len(errors) > 0 {
		return GetChatRespDataMultiError(errors)
	}

	return nil
}

// GetChatRespDataMultiError is an error wrapping multiple validation errors
// returned by GetChatRespData.ValidateAll() if the designated constraints
// aren't met.
type GetChatRespDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetChatRespDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetChatRespDataMultiError) AllErrors() []error { return m }

// GetChatRespDataValidationError is the validation error returned by
// GetChatRespData.Validate if the designated constraints aren't met.
type GetChatRespDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetChatRespDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetChatRespDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetChatRespDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetChatRespDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetChatRespDataValidationError) ErrorName() string { return "GetChatRespDataValidationError" }

// Error satisfies the builtin error interface
func (e GetChatRespDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetChatRespData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetChatRespDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetChatRespDataValidationError{}

// Validate checks the field values on ListChatRespData with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ListChatRespData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListChatRespData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListChatRespDataMultiError, or nil if none found.
func (m *ListChatRespData) ValidateAll() error {
	return m.validate(true)
}

func (m *ListChatRespData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ChatId

	// no validation rules for Avatar

	// no validation rules for Name

	// no validation rules for Description

	// no validation rules for External

	// no validation rules for Status

	if len(errors) > 0 {
		return ListChatRespDataMultiError(errors)
	}

	return nil
}

// ListChatRespDataMultiError is an error wrapping multiple validation errors
// returned by ListChatRespData.ValidateAll() if the designated constraints
// aren't met.
type ListChatRespDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListChatRespDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListChatRespDataMultiError) AllErrors() []error { return m }

// ListChatRespDataValidationError is the validation error returned by
// ListChatRespData.Validate if the designated constraints aren't met.
type ListChatRespDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListChatRespDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListChatRespDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListChatRespDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListChatRespDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListChatRespDataValidationError) ErrorName() string { return "ListChatRespDataValidationError" }

// Error satisfies the builtin error interface
func (e ListChatRespDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListChatRespData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListChatRespDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListChatRespDataValidationError{}
