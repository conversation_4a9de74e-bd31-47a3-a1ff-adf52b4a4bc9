// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.1
// 	protoc        v5.29.2
// source: larkproxy/im_chat_managers.proto

package pb

import (
	reflect "reflect"
	sync "sync"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type CreateChatManagersRespData struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	ChatBotManagers []string               `protobuf:"bytes,1,rep,name=chat_bot_managers,json=chatBotManagers,proto3" json:"chat_bot_managers,omitempty"` // 机器人类型管理员ID
	ChatManagers    []string               `protobuf:"bytes,2,rep,name=chat_managers,json=chatManagers,proto3" json:"chat_managers,omitempty"`            // 用户类型管理员ID
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *CreateChatManagersRespData) Reset() {
	*x = CreateChatManagersRespData{}
	mi := &file_larkproxy_im_chat_managers_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateChatManagersRespData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateChatManagersRespData) ProtoMessage() {}

func (x *CreateChatManagersRespData) ProtoReflect() protoreflect.Message {
	mi := &file_larkproxy_im_chat_managers_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateChatManagersRespData.ProtoReflect.Descriptor instead.
func (*CreateChatManagersRespData) Descriptor() ([]byte, []int) {
	return file_larkproxy_im_chat_managers_proto_rawDescGZIP(), []int{0}
}

func (x *CreateChatManagersRespData) GetChatBotManagers() []string {
	if x != nil {
		return x.ChatBotManagers
	}
	return nil
}

func (x *CreateChatManagersRespData) GetChatManagers() []string {
	if x != nil {
		return x.ChatManagers
	}
	return nil
}

var File_larkproxy_im_chat_managers_proto protoreflect.FileDescriptor

var file_larkproxy_im_chat_managers_proto_rawDesc = []byte{
	0x0a, 0x20, 0x6c, 0x61, 0x72, 0x6b, 0x70, 0x72, 0x6f, 0x78, 0x79, 0x2f, 0x69, 0x6d, 0x5f, 0x63,
	0x68, 0x61, 0x74, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x12, 0x09, 0x6c, 0x61, 0x72, 0x6b, 0x70, 0x72, 0x6f, 0x78, 0x79, 0x22, 0x6d, 0x0a,
	0x1a, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x43, 0x68, 0x61, 0x74, 0x4d, 0x61, 0x6e, 0x61, 0x67,
	0x65, 0x72, 0x73, 0x52, 0x65, 0x73, 0x70, 0x44, 0x61, 0x74, 0x61, 0x12, 0x2a, 0x0a, 0x11, 0x63,
	0x68, 0x61, 0x74, 0x5f, 0x62, 0x6f, 0x74, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x73,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0f, 0x63, 0x68, 0x61, 0x74, 0x42, 0x6f, 0x74, 0x4d,
	0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x73, 0x12, 0x23, 0x0a, 0x0d, 0x63, 0x68, 0x61, 0x74, 0x5f,
	0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0c,
	0x63, 0x68, 0x61, 0x74, 0x4d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x73, 0x42, 0x43, 0x5a, 0x41,
	0x67, 0x69, 0x74, 0x6c, 0x61, 0x62, 0x2e, 0x74, 0x74, 0x79, 0x75, 0x79, 0x69, 0x6e, 0x2e, 0x63,
	0x6f, 0x6d, 0x2f, 0x54, 0x65, 0x73, 0x74, 0x44, 0x65, 0x76, 0x65, 0x6c, 0x6f, 0x70, 0x6d, 0x65,
	0x6e, 0x74, 0x2f, 0x70, 0x72, 0x6f, 0x62, 0x65, 0x2d, 0x62, 0x61, 0x63, 0x6b, 0x65, 0x6e, 0x64,
	0x2f, 0x6c, 0x61, 0x72, 0x6b, 0x70, 0x72, 0x6f, 0x78, 0x79, 0x2f, 0x72, 0x70, 0x63, 0x2f, 0x70,
	0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_larkproxy_im_chat_managers_proto_rawDescOnce sync.Once
	file_larkproxy_im_chat_managers_proto_rawDescData = file_larkproxy_im_chat_managers_proto_rawDesc
)

func file_larkproxy_im_chat_managers_proto_rawDescGZIP() []byte {
	file_larkproxy_im_chat_managers_proto_rawDescOnce.Do(func() {
		file_larkproxy_im_chat_managers_proto_rawDescData = protoimpl.X.CompressGZIP(file_larkproxy_im_chat_managers_proto_rawDescData)
	})
	return file_larkproxy_im_chat_managers_proto_rawDescData
}

var file_larkproxy_im_chat_managers_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_larkproxy_im_chat_managers_proto_goTypes = []any{
	(*CreateChatManagersRespData)(nil), // 0: larkproxy.CreateChatManagersRespData
}
var file_larkproxy_im_chat_managers_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_larkproxy_im_chat_managers_proto_init() }
func file_larkproxy_im_chat_managers_proto_init() {
	if File_larkproxy_im_chat_managers_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_larkproxy_im_chat_managers_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_larkproxy_im_chat_managers_proto_goTypes,
		DependencyIndexes: file_larkproxy_im_chat_managers_proto_depIdxs,
		MessageInfos:      file_larkproxy_im_chat_managers_proto_msgTypes,
	}.Build()
	File_larkproxy_im_chat_managers_proto = out.File
	file_larkproxy_im_chat_managers_proto_rawDesc = nil
	file_larkproxy_im_chat_managers_proto_goTypes = nil
	file_larkproxy_im_chat_managers_proto_depIdxs = nil
}
