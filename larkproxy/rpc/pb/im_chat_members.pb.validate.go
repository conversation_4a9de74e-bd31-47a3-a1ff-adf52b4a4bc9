// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: larkproxy/im_chat_members.proto

package pb

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on CreateChatMembersRespData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateChatMembersRespData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateChatMembersRespData with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateChatMembersRespDataMultiError, or nil if none found.
func (m *CreateChatMembersRespData) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateChatMembersRespData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return CreateChatMembersRespDataMultiError(errors)
	}

	return nil
}

// CreateChatMembersRespDataMultiError is an error wrapping multiple validation
// errors returned by CreateChatMembersRespData.ValidateAll() if the
// designated constraints aren't met.
type CreateChatMembersRespDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateChatMembersRespDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateChatMembersRespDataMultiError) AllErrors() []error { return m }

// CreateChatMembersRespDataValidationError is the validation error returned by
// CreateChatMembersRespData.Validate if the designated constraints aren't met.
type CreateChatMembersRespDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateChatMembersRespDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateChatMembersRespDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateChatMembersRespDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateChatMembersRespDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateChatMembersRespDataValidationError) ErrorName() string {
	return "CreateChatMembersRespDataValidationError"
}

// Error satisfies the builtin error interface
func (e CreateChatMembersRespDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateChatMembersRespData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateChatMembersRespDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateChatMembersRespDataValidationError{}

// Validate checks the field values on GetChatMembersRespData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetChatMembersRespData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetChatMembersRespData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetChatMembersRespDataMultiError, or nil if none found.
func (m *GetChatMembersRespData) ValidateAll() error {
	return m.validate(true)
}

func (m *GetChatMembersRespData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for MemberIdType

	// no validation rules for MemberId

	// no validation rules for Name

	if len(errors) > 0 {
		return GetChatMembersRespDataMultiError(errors)
	}

	return nil
}

// GetChatMembersRespDataMultiError is an error wrapping multiple validation
// errors returned by GetChatMembersRespData.ValidateAll() if the designated
// constraints aren't met.
type GetChatMembersRespDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetChatMembersRespDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetChatMembersRespDataMultiError) AllErrors() []error { return m }

// GetChatMembersRespDataValidationError is the validation error returned by
// GetChatMembersRespData.Validate if the designated constraints aren't met.
type GetChatMembersRespDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetChatMembersRespDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetChatMembersRespDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetChatMembersRespDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetChatMembersRespDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetChatMembersRespDataValidationError) ErrorName() string {
	return "GetChatMembersRespDataValidationError"
}

// Error satisfies the builtin error interface
func (e GetChatMembersRespDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetChatMembersRespData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetChatMembersRespDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetChatMembersRespDataValidationError{}
