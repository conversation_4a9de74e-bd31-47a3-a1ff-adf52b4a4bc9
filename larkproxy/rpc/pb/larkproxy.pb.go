// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.1
// 	protoc        v5.29.2
// source: larkproxy/larkproxy.proto

package pb

import (
	reflect "reflect"
	sync "sync"

	_ "buf.build/gen/go/bufbuild/protovalidate/protocolbuffers/go/buf/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type GetBatchUserIDReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Emails        []string               `protobuf:"bytes,1,rep,name=emails,proto3" json:"emails,omitempty"` // 邮箱
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetBatchUserIDReq) Reset() {
	*x = GetBatchUserIDReq{}
	mi := &file_larkproxy_larkproxy_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetBatchUserIDReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetBatchUserIDReq) ProtoMessage() {}

func (x *GetBatchUserIDReq) ProtoReflect() protoreflect.Message {
	mi := &file_larkproxy_larkproxy_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetBatchUserIDReq.ProtoReflect.Descriptor instead.
func (*GetBatchUserIDReq) Descriptor() ([]byte, []int) {
	return file_larkproxy_larkproxy_proto_rawDescGZIP(), []int{0}
}

func (x *GetBatchUserIDReq) GetEmails() []string {
	if x != nil {
		return x.Emails
	}
	return nil
}

type GetBatchUserIDResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Items         []*UserContactInfo     `protobuf:"bytes,1,rep,name=items,proto3" json:"items,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetBatchUserIDResp) Reset() {
	*x = GetBatchUserIDResp{}
	mi := &file_larkproxy_larkproxy_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetBatchUserIDResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetBatchUserIDResp) ProtoMessage() {}

func (x *GetBatchUserIDResp) ProtoReflect() protoreflect.Message {
	mi := &file_larkproxy_larkproxy_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetBatchUserIDResp.ProtoReflect.Descriptor instead.
func (*GetBatchUserIDResp) Descriptor() ([]byte, []int) {
	return file_larkproxy_larkproxy_proto_rawDescGZIP(), []int{1}
}

func (x *GetBatchUserIDResp) GetItems() []*UserContactInfo {
	if x != nil {
		return x.Items
	}
	return nil
}

type CreateChatReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Uuid          string                 `protobuf:"bytes,1,opt,name=uuid,proto3" json:"uuid,omitempty"`                                  // 用于创建群组请求去重
	Name          string                 `protobuf:"bytes,11,opt,name=name,proto3" json:"name,omitempty"`                                 // 群名称
	Description   string                 `protobuf:"bytes,12,opt,name=description,proto3" json:"description,omitempty"`                   // 群描述
	UserIdList    []string               `protobuf:"bytes,13,rep,name=user_id_list,json=userIdList,proto3" json:"user_id_list,omitempty"` // 创建群时邀请的群成员
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateChatReq) Reset() {
	*x = CreateChatReq{}
	mi := &file_larkproxy_larkproxy_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateChatReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateChatReq) ProtoMessage() {}

func (x *CreateChatReq) ProtoReflect() protoreflect.Message {
	mi := &file_larkproxy_larkproxy_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateChatReq.ProtoReflect.Descriptor instead.
func (*CreateChatReq) Descriptor() ([]byte, []int) {
	return file_larkproxy_larkproxy_proto_rawDescGZIP(), []int{2}
}

func (x *CreateChatReq) GetUuid() string {
	if x != nil {
		return x.Uuid
	}
	return ""
}

func (x *CreateChatReq) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CreateChatReq) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *CreateChatReq) GetUserIdList() []string {
	if x != nil {
		return x.UserIdList
	}
	return nil
}

type CreateChatResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Data          *CreateChatRespData    `protobuf:"bytes,1,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateChatResp) Reset() {
	*x = CreateChatResp{}
	mi := &file_larkproxy_larkproxy_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateChatResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateChatResp) ProtoMessage() {}

func (x *CreateChatResp) ProtoReflect() protoreflect.Message {
	mi := &file_larkproxy_larkproxy_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateChatResp.ProtoReflect.Descriptor instead.
func (*CreateChatResp) Descriptor() ([]byte, []int) {
	return file_larkproxy_larkproxy_proto_rawDescGZIP(), []int{3}
}

func (x *CreateChatResp) GetData() *CreateChatRespData {
	if x != nil {
		return x.Data
	}
	return nil
}

type DeleteChatReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ChatId        string                 `protobuf:"bytes,1,opt,name=chat_id,json=chatId,proto3" json:"chat_id,omitempty"` // 群ID
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteChatReq) Reset() {
	*x = DeleteChatReq{}
	mi := &file_larkproxy_larkproxy_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteChatReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteChatReq) ProtoMessage() {}

func (x *DeleteChatReq) ProtoReflect() protoreflect.Message {
	mi := &file_larkproxy_larkproxy_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteChatReq.ProtoReflect.Descriptor instead.
func (*DeleteChatReq) Descriptor() ([]byte, []int) {
	return file_larkproxy_larkproxy_proto_rawDescGZIP(), []int{4}
}

func (x *DeleteChatReq) GetChatId() string {
	if x != nil {
		return x.ChatId
	}
	return ""
}

type DeleteChatResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteChatResp) Reset() {
	*x = DeleteChatResp{}
	mi := &file_larkproxy_larkproxy_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteChatResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteChatResp) ProtoMessage() {}

func (x *DeleteChatResp) ProtoReflect() protoreflect.Message {
	mi := &file_larkproxy_larkproxy_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteChatResp.ProtoReflect.Descriptor instead.
func (*DeleteChatResp) Descriptor() ([]byte, []int) {
	return file_larkproxy_larkproxy_proto_rawDescGZIP(), []int{5}
}

type GetChatReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ChatId        string                 `protobuf:"bytes,1,opt,name=chat_id,json=chatId,proto3" json:"chat_id,omitempty"` // 群ID
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetChatReq) Reset() {
	*x = GetChatReq{}
	mi := &file_larkproxy_larkproxy_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetChatReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetChatReq) ProtoMessage() {}

func (x *GetChatReq) ProtoReflect() protoreflect.Message {
	mi := &file_larkproxy_larkproxy_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetChatReq.ProtoReflect.Descriptor instead.
func (*GetChatReq) Descriptor() ([]byte, []int) {
	return file_larkproxy_larkproxy_proto_rawDescGZIP(), []int{6}
}

func (x *GetChatReq) GetChatId() string {
	if x != nil {
		return x.ChatId
	}
	return ""
}

type GetChatResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Data          *GetChatRespData       `protobuf:"bytes,1,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetChatResp) Reset() {
	*x = GetChatResp{}
	mi := &file_larkproxy_larkproxy_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetChatResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetChatResp) ProtoMessage() {}

func (x *GetChatResp) ProtoReflect() protoreflect.Message {
	mi := &file_larkproxy_larkproxy_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetChatResp.ProtoReflect.Descriptor instead.
func (*GetChatResp) Descriptor() ([]byte, []int) {
	return file_larkproxy_larkproxy_proto_rawDescGZIP(), []int{7}
}

func (x *GetChatResp) GetData() *GetChatRespData {
	if x != nil {
		return x.Data
	}
	return nil
}

type ListChatReq struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	WithCurrentUser bool                   `protobuf:"varint,1,opt,name=with_current_user,json=withCurrentUser,proto3" json:"with_current_user,omitempty"` // 是否需要当前用户在群里
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *ListChatReq) Reset() {
	*x = ListChatReq{}
	mi := &file_larkproxy_larkproxy_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListChatReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListChatReq) ProtoMessage() {}

func (x *ListChatReq) ProtoReflect() protoreflect.Message {
	mi := &file_larkproxy_larkproxy_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListChatReq.ProtoReflect.Descriptor instead.
func (*ListChatReq) Descriptor() ([]byte, []int) {
	return file_larkproxy_larkproxy_proto_rawDescGZIP(), []int{8}
}

func (x *ListChatReq) GetWithCurrentUser() bool {
	if x != nil {
		return x.WithCurrentUser
	}
	return false
}

type ListChatResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Items         []*ListChatRespData    `protobuf:"bytes,1,rep,name=items,proto3" json:"items,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListChatResp) Reset() {
	*x = ListChatResp{}
	mi := &file_larkproxy_larkproxy_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListChatResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListChatResp) ProtoMessage() {}

func (x *ListChatResp) ProtoReflect() protoreflect.Message {
	mi := &file_larkproxy_larkproxy_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListChatResp.ProtoReflect.Descriptor instead.
func (*ListChatResp) Descriptor() ([]byte, []int) {
	return file_larkproxy_larkproxy_proto_rawDescGZIP(), []int{9}
}

func (x *ListChatResp) GetItems() []*ListChatRespData {
	if x != nil {
		return x.Items
	}
	return nil
}

type CreateChatMembersReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ChatId        string                 `protobuf:"bytes,1,opt,name=chat_id,json=chatId,proto3" json:"chat_id,omitempty"`               // 群ID
	UserIdList    []string               `protobuf:"bytes,2,rep,name=user_id_list,json=userIdList,proto3" json:"user_id_list,omitempty"` // 邀请的群成员
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateChatMembersReq) Reset() {
	*x = CreateChatMembersReq{}
	mi := &file_larkproxy_larkproxy_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateChatMembersReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateChatMembersReq) ProtoMessage() {}

func (x *CreateChatMembersReq) ProtoReflect() protoreflect.Message {
	mi := &file_larkproxy_larkproxy_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateChatMembersReq.ProtoReflect.Descriptor instead.
func (*CreateChatMembersReq) Descriptor() ([]byte, []int) {
	return file_larkproxy_larkproxy_proto_rawDescGZIP(), []int{10}
}

func (x *CreateChatMembersReq) GetChatId() string {
	if x != nil {
		return x.ChatId
	}
	return ""
}

func (x *CreateChatMembersReq) GetUserIdList() []string {
	if x != nil {
		return x.UserIdList
	}
	return nil
}

type CreateChatMembersResp struct {
	state         protoimpl.MessageState     `protogen:"open.v1"`
	Data          *CreateChatMembersRespData `protobuf:"bytes,1,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateChatMembersResp) Reset() {
	*x = CreateChatMembersResp{}
	mi := &file_larkproxy_larkproxy_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateChatMembersResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateChatMembersResp) ProtoMessage() {}

func (x *CreateChatMembersResp) ProtoReflect() protoreflect.Message {
	mi := &file_larkproxy_larkproxy_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateChatMembersResp.ProtoReflect.Descriptor instead.
func (*CreateChatMembersResp) Descriptor() ([]byte, []int) {
	return file_larkproxy_larkproxy_proto_rawDescGZIP(), []int{11}
}

func (x *CreateChatMembersResp) GetData() *CreateChatMembersRespData {
	if x != nil {
		return x.Data
	}
	return nil
}

type DeleteChatMembersReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteChatMembersReq) Reset() {
	*x = DeleteChatMembersReq{}
	mi := &file_larkproxy_larkproxy_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteChatMembersReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteChatMembersReq) ProtoMessage() {}

func (x *DeleteChatMembersReq) ProtoReflect() protoreflect.Message {
	mi := &file_larkproxy_larkproxy_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteChatMembersReq.ProtoReflect.Descriptor instead.
func (*DeleteChatMembersReq) Descriptor() ([]byte, []int) {
	return file_larkproxy_larkproxy_proto_rawDescGZIP(), []int{12}
}

type DeleteChatMembersResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteChatMembersResp) Reset() {
	*x = DeleteChatMembersResp{}
	mi := &file_larkproxy_larkproxy_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteChatMembersResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteChatMembersResp) ProtoMessage() {}

func (x *DeleteChatMembersResp) ProtoReflect() protoreflect.Message {
	mi := &file_larkproxy_larkproxy_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteChatMembersResp.ProtoReflect.Descriptor instead.
func (*DeleteChatMembersResp) Descriptor() ([]byte, []int) {
	return file_larkproxy_larkproxy_proto_rawDescGZIP(), []int{13}
}

type GetChatMembersReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ChatId        string                 `protobuf:"bytes,1,opt,name=chat_id,json=chatId,proto3" json:"chat_id,omitempty"` // 群ID
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetChatMembersReq) Reset() {
	*x = GetChatMembersReq{}
	mi := &file_larkproxy_larkproxy_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetChatMembersReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetChatMembersReq) ProtoMessage() {}

func (x *GetChatMembersReq) ProtoReflect() protoreflect.Message {
	mi := &file_larkproxy_larkproxy_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetChatMembersReq.ProtoReflect.Descriptor instead.
func (*GetChatMembersReq) Descriptor() ([]byte, []int) {
	return file_larkproxy_larkproxy_proto_rawDescGZIP(), []int{14}
}

func (x *GetChatMembersReq) GetChatId() string {
	if x != nil {
		return x.ChatId
	}
	return ""
}

type GetChatMembersResp struct {
	state         protoimpl.MessageState    `protogen:"open.v1"`
	Items         []*GetChatMembersRespData `protobuf:"bytes,1,rep,name=items,proto3" json:"items,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetChatMembersResp) Reset() {
	*x = GetChatMembersResp{}
	mi := &file_larkproxy_larkproxy_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetChatMembersResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetChatMembersResp) ProtoMessage() {}

func (x *GetChatMembersResp) ProtoReflect() protoreflect.Message {
	mi := &file_larkproxy_larkproxy_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetChatMembersResp.ProtoReflect.Descriptor instead.
func (*GetChatMembersResp) Descriptor() ([]byte, []int) {
	return file_larkproxy_larkproxy_proto_rawDescGZIP(), []int{15}
}

func (x *GetChatMembersResp) GetItems() []*GetChatMembersRespData {
	if x != nil {
		return x.Items
	}
	return nil
}

type CreateChatManagersReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ChatId        string                 `protobuf:"bytes,1,opt,name=chat_id,json=chatId,proto3" json:"chat_id,omitempty"`                        // 群ID
	ManagerIdList []string               `protobuf:"bytes,2,rep,name=manager_id_list,json=managerIdList,proto3" json:"manager_id_list,omitempty"` // 指定的群管理员
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateChatManagersReq) Reset() {
	*x = CreateChatManagersReq{}
	mi := &file_larkproxy_larkproxy_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateChatManagersReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateChatManagersReq) ProtoMessage() {}

func (x *CreateChatManagersReq) ProtoReflect() protoreflect.Message {
	mi := &file_larkproxy_larkproxy_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateChatManagersReq.ProtoReflect.Descriptor instead.
func (*CreateChatManagersReq) Descriptor() ([]byte, []int) {
	return file_larkproxy_larkproxy_proto_rawDescGZIP(), []int{16}
}

func (x *CreateChatManagersReq) GetChatId() string {
	if x != nil {
		return x.ChatId
	}
	return ""
}

func (x *CreateChatManagersReq) GetManagerIdList() []string {
	if x != nil {
		return x.ManagerIdList
	}
	return nil
}

type CreateChatManagersResp struct {
	state         protoimpl.MessageState      `protogen:"open.v1"`
	Data          *CreateChatManagersRespData `protobuf:"bytes,1,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateChatManagersResp) Reset() {
	*x = CreateChatManagersResp{}
	mi := &file_larkproxy_larkproxy_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateChatManagersResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateChatManagersResp) ProtoMessage() {}

func (x *CreateChatManagersResp) ProtoReflect() protoreflect.Message {
	mi := &file_larkproxy_larkproxy_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateChatManagersResp.ProtoReflect.Descriptor instead.
func (*CreateChatManagersResp) Descriptor() ([]byte, []int) {
	return file_larkproxy_larkproxy_proto_rawDescGZIP(), []int{17}
}

func (x *CreateChatManagersResp) GetData() *CreateChatManagersRespData {
	if x != nil {
		return x.Data
	}
	return nil
}

var File_larkproxy_larkproxy_proto protoreflect.FileDescriptor

var file_larkproxy_larkproxy_proto_rawDesc = []byte{
	0x0a, 0x19, 0x6c, 0x61, 0x72, 0x6b, 0x70, 0x72, 0x6f, 0x78, 0x79, 0x2f, 0x6c, 0x61, 0x72, 0x6b,
	0x70, 0x72, 0x6f, 0x78, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x09, 0x6c, 0x61, 0x72,
	0x6b, 0x70, 0x72, 0x6f, 0x78, 0x79, 0x1a, 0x1b, 0x62, 0x75, 0x66, 0x2f, 0x76, 0x61, 0x6c, 0x69,
	0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x1c, 0x6c, 0x61, 0x72, 0x6b, 0x70, 0x72, 0x6f, 0x78, 0x79, 0x2f, 0x63,
	0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x1f, 0x6c, 0x61, 0x72, 0x6b, 0x70, 0x72, 0x6f, 0x78, 0x79, 0x2f, 0x69, 0x6d, 0x5f,
	0x63, 0x68, 0x61, 0x74, 0x5f, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x18, 0x6c, 0x61, 0x72, 0x6b, 0x70, 0x72, 0x6f, 0x78, 0x79, 0x2f, 0x69, 0x6d,
	0x5f, 0x63, 0x68, 0x61, 0x74, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x20, 0x6c, 0x61,
	0x72, 0x6b, 0x70, 0x72, 0x6f, 0x78, 0x79, 0x2f, 0x69, 0x6d, 0x5f, 0x63, 0x68, 0x61, 0x74, 0x5f,
	0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x3d,
	0x0a, 0x11, 0x47, 0x65, 0x74, 0x42, 0x61, 0x74, 0x63, 0x68, 0x55, 0x73, 0x65, 0x72, 0x49, 0x44,
	0x52, 0x65, 0x71, 0x12, 0x28, 0x0a, 0x06, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x09, 0x42, 0x10, 0xba, 0x48, 0x0d, 0x92, 0x01, 0x0a, 0x08, 0x01, 0x18, 0x01, 0x22,
	0x04, 0x72, 0x02, 0x60, 0x01, 0x52, 0x06, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x73, 0x22, 0x46, 0x0a,
	0x12, 0x47, 0x65, 0x74, 0x42, 0x61, 0x74, 0x63, 0x68, 0x55, 0x73, 0x65, 0x72, 0x49, 0x44, 0x52,
	0x65, 0x73, 0x70, 0x12, 0x30, 0x0a, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x6c, 0x61, 0x72, 0x6b, 0x70, 0x72, 0x6f, 0x78, 0x79, 0x2e, 0x55,
	0x73, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x05,
	0x69, 0x74, 0x65, 0x6d, 0x73, 0x22, 0xb9, 0x01, 0x0a, 0x0d, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x43, 0x68, 0x61, 0x74, 0x52, 0x65, 0x71, 0x12, 0x1b, 0x0a, 0x04, 0x75, 0x75, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xba, 0x48, 0x04, 0x72, 0x02, 0x18, 0x32, 0x52, 0x04,
	0x75, 0x75, 0x69, 0x64, 0x12, 0x1d, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x0b, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x09, 0xba, 0x48, 0x06, 0x72, 0x04, 0x10, 0x02, 0x18, 0x3c, 0x52, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x12, 0x29, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xba, 0x48, 0x04, 0x72, 0x02, 0x18,
	0x64, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x41,
	0x0a, 0x0c, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x0d,
	0x20, 0x03, 0x28, 0x09, 0x42, 0x1f, 0xba, 0x48, 0x1c, 0x92, 0x01, 0x19, 0x08, 0x00, 0x22, 0x15,
	0x72, 0x13, 0x32, 0x11, 0x5e, 0x6f, 0x75, 0x5f, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x5d,
	0x7b, 0x33, 0x32, 0x7d, 0x24, 0x52, 0x0a, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x4c, 0x69, 0x73,
	0x74, 0x22, 0x43, 0x0a, 0x0e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x43, 0x68, 0x61, 0x74, 0x52,
	0x65, 0x73, 0x70, 0x12, 0x31, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1d, 0x2e, 0x6c, 0x61, 0x72, 0x6b, 0x70, 0x72, 0x6f, 0x78, 0x79, 0x2e, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x43, 0x68, 0x61, 0x74, 0x52, 0x65, 0x73, 0x70, 0x44, 0x61, 0x74, 0x61,
	0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0x42, 0x0a, 0x0d, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65,
	0x43, 0x68, 0x61, 0x74, 0x52, 0x65, 0x71, 0x12, 0x31, 0x0a, 0x07, 0x63, 0x68, 0x61, 0x74, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x18, 0xba, 0x48, 0x15, 0x72, 0x13, 0x32,
	0x11, 0x5e, 0x6f, 0x63, 0x5f, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x5d, 0x7b, 0x33, 0x32,
	0x7d, 0x24, 0x52, 0x06, 0x63, 0x68, 0x61, 0x74, 0x49, 0x64, 0x22, 0x10, 0x0a, 0x0e, 0x44, 0x65,
	0x6c, 0x65, 0x74, 0x65, 0x43, 0x68, 0x61, 0x74, 0x52, 0x65, 0x73, 0x70, 0x22, 0x3f, 0x0a, 0x0a,
	0x47, 0x65, 0x74, 0x43, 0x68, 0x61, 0x74, 0x52, 0x65, 0x71, 0x12, 0x31, 0x0a, 0x07, 0x63, 0x68,
	0x61, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x18, 0xba, 0x48, 0x15,
	0x72, 0x13, 0x32, 0x11, 0x5e, 0x6f, 0x63, 0x5f, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x5d,
	0x7b, 0x33, 0x32, 0x7d, 0x24, 0x52, 0x06, 0x63, 0x68, 0x61, 0x74, 0x49, 0x64, 0x22, 0x3d, 0x0a,
	0x0b, 0x47, 0x65, 0x74, 0x43, 0x68, 0x61, 0x74, 0x52, 0x65, 0x73, 0x70, 0x12, 0x2e, 0x0a, 0x04,
	0x64, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x6c, 0x61, 0x72,
	0x6b, 0x70, 0x72, 0x6f, 0x78, 0x79, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x68, 0x61, 0x74, 0x52, 0x65,
	0x73, 0x70, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0x39, 0x0a, 0x0b,
	0x4c, 0x69, 0x73, 0x74, 0x43, 0x68, 0x61, 0x74, 0x52, 0x65, 0x71, 0x12, 0x2a, 0x0a, 0x11, 0x77,
	0x69, 0x74, 0x68, 0x5f, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x75, 0x73, 0x65, 0x72,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0f, 0x77, 0x69, 0x74, 0x68, 0x43, 0x75, 0x72, 0x72,
	0x65, 0x6e, 0x74, 0x55, 0x73, 0x65, 0x72, 0x22, 0x41, 0x0a, 0x0c, 0x4c, 0x69, 0x73, 0x74, 0x43,
	0x68, 0x61, 0x74, 0x52, 0x65, 0x73, 0x70, 0x12, 0x31, 0x0a, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x6c, 0x61, 0x72, 0x6b, 0x70, 0x72, 0x6f,
	0x78, 0x79, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x68, 0x61, 0x74, 0x52, 0x65, 0x73, 0x70, 0x44,
	0x61, 0x74, 0x61, 0x52, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x22, 0x8c, 0x01, 0x0a, 0x14, 0x43,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x43, 0x68, 0x61, 0x74, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73,
	0x52, 0x65, 0x71, 0x12, 0x31, 0x0a, 0x07, 0x63, 0x68, 0x61, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x18, 0xba, 0x48, 0x15, 0x72, 0x13, 0x32, 0x11, 0x5e, 0x6f, 0x63,
	0x5f, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x5d, 0x7b, 0x33, 0x32, 0x7d, 0x24, 0x52, 0x06,
	0x63, 0x68, 0x61, 0x74, 0x49, 0x64, 0x12, 0x41, 0x0a, 0x0c, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69,
	0x64, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x42, 0x1f, 0xba, 0x48,
	0x1c, 0x92, 0x01, 0x19, 0x08, 0x01, 0x22, 0x15, 0x72, 0x13, 0x32, 0x11, 0x5e, 0x6f, 0x75, 0x5f,
	0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x5d, 0x7b, 0x33, 0x32, 0x7d, 0x24, 0x52, 0x0a, 0x75,
	0x73, 0x65, 0x72, 0x49, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x22, 0x51, 0x0a, 0x15, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x43, 0x68, 0x61, 0x74, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x52, 0x65,
	0x73, 0x70, 0x12, 0x38, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x24, 0x2e, 0x6c, 0x61, 0x72, 0x6b, 0x70, 0x72, 0x6f, 0x78, 0x79, 0x2e, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x43, 0x68, 0x61, 0x74, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x52, 0x65,
	0x73, 0x70, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0x16, 0x0a, 0x14,
	0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x43, 0x68, 0x61, 0x74, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72,
	0x73, 0x52, 0x65, 0x71, 0x22, 0x17, 0x0a, 0x15, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x43, 0x68,
	0x61, 0x74, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x52, 0x65, 0x73, 0x70, 0x22, 0x46, 0x0a,
	0x11, 0x47, 0x65, 0x74, 0x43, 0x68, 0x61, 0x74, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x52,
	0x65, 0x71, 0x12, 0x31, 0x0a, 0x07, 0x63, 0x68, 0x61, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x18, 0xba, 0x48, 0x15, 0x72, 0x13, 0x32, 0x11, 0x5e, 0x6f, 0x63, 0x5f,
	0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x5d, 0x7b, 0x33, 0x32, 0x7d, 0x24, 0x52, 0x06, 0x63,
	0x68, 0x61, 0x74, 0x49, 0x64, 0x22, 0x4d, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x43, 0x68, 0x61, 0x74,
	0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x52, 0x65, 0x73, 0x70, 0x12, 0x37, 0x0a, 0x05, 0x69,
	0x74, 0x65, 0x6d, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x6c, 0x61, 0x72,
	0x6b, 0x70, 0x72, 0x6f, 0x78, 0x79, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x68, 0x61, 0x74, 0x4d, 0x65,
	0x6d, 0x62, 0x65, 0x72, 0x73, 0x52, 0x65, 0x73, 0x70, 0x44, 0x61, 0x74, 0x61, 0x52, 0x05, 0x69,
	0x74, 0x65, 0x6d, 0x73, 0x22, 0x93, 0x01, 0x0a, 0x15, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x43,
	0x68, 0x61, 0x74, 0x4d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x73, 0x52, 0x65, 0x71, 0x12, 0x31,
	0x0a, 0x07, 0x63, 0x68, 0x61, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x18, 0xba, 0x48, 0x15, 0x72, 0x13, 0x32, 0x11, 0x5e, 0x6f, 0x63, 0x5f, 0x5b, 0x61, 0x2d, 0x7a,
	0x30, 0x2d, 0x39, 0x5d, 0x7b, 0x33, 0x32, 0x7d, 0x24, 0x52, 0x06, 0x63, 0x68, 0x61, 0x74, 0x49,
	0x64, 0x12, 0x47, 0x0a, 0x0f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x5f,
	0x6c, 0x69, 0x73, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x42, 0x1f, 0xba, 0x48, 0x1c, 0x92,
	0x01, 0x19, 0x08, 0x01, 0x22, 0x15, 0x72, 0x13, 0x32, 0x11, 0x5e, 0x6f, 0x75, 0x5f, 0x5b, 0x61,
	0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x5d, 0x7b, 0x33, 0x32, 0x7d, 0x24, 0x52, 0x0d, 0x6d, 0x61, 0x6e,
	0x61, 0x67, 0x65, 0x72, 0x49, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x22, 0x53, 0x0a, 0x16, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x43, 0x68, 0x61, 0x74, 0x4d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x73,
	0x52, 0x65, 0x73, 0x70, 0x12, 0x39, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x25, 0x2e, 0x6c, 0x61, 0x72, 0x6b, 0x70, 0x72, 0x6f, 0x78, 0x79, 0x2e, 0x43,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x43, 0x68, 0x61, 0x74, 0x4d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72,
	0x73, 0x52, 0x65, 0x73, 0x70, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x32,
	0x67, 0x0a, 0x16, 0x4c, 0x61, 0x72, 0x6b, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x55, 0x73,
	0x65, 0x72, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x4d, 0x0a, 0x0e, 0x47, 0x65, 0x74,
	0x42, 0x61, 0x74, 0x63, 0x68, 0x55, 0x73, 0x65, 0x72, 0x49, 0x44, 0x12, 0x1c, 0x2e, 0x6c, 0x61,
	0x72, 0x6b, 0x70, 0x72, 0x6f, 0x78, 0x79, 0x2e, 0x47, 0x65, 0x74, 0x42, 0x61, 0x74, 0x63, 0x68,
	0x55, 0x73, 0x65, 0x72, 0x49, 0x44, 0x52, 0x65, 0x71, 0x1a, 0x1d, 0x2e, 0x6c, 0x61, 0x72, 0x6b,
	0x70, 0x72, 0x6f, 0x78, 0x79, 0x2e, 0x47, 0x65, 0x74, 0x42, 0x61, 0x74, 0x63, 0x68, 0x55, 0x73,
	0x65, 0x72, 0x49, 0x44, 0x52, 0x65, 0x73, 0x70, 0x32, 0x91, 0x02, 0x0a, 0x12, 0x4c, 0x61, 0x72,
	0x6b, 0x49, 0x4d, 0x43, 0x68, 0x61, 0x74, 0x73, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12,
	0x41, 0x0a, 0x0a, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x43, 0x68, 0x61, 0x74, 0x12, 0x18, 0x2e,
	0x6c, 0x61, 0x72, 0x6b, 0x70, 0x72, 0x6f, 0x78, 0x79, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x43, 0x68, 0x61, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x19, 0x2e, 0x6c, 0x61, 0x72, 0x6b, 0x70, 0x72,
	0x6f, 0x78, 0x79, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x43, 0x68, 0x61, 0x74, 0x52, 0x65,
	0x73, 0x70, 0x12, 0x41, 0x0a, 0x0a, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x43, 0x68, 0x61, 0x74,
	0x12, 0x18, 0x2e, 0x6c, 0x61, 0x72, 0x6b, 0x70, 0x72, 0x6f, 0x78, 0x79, 0x2e, 0x44, 0x65, 0x6c,
	0x65, 0x74, 0x65, 0x43, 0x68, 0x61, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x19, 0x2e, 0x6c, 0x61, 0x72,
	0x6b, 0x70, 0x72, 0x6f, 0x78, 0x79, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x43, 0x68, 0x61,
	0x74, 0x52, 0x65, 0x73, 0x70, 0x12, 0x38, 0x0a, 0x07, 0x47, 0x65, 0x74, 0x43, 0x68, 0x61, 0x74,
	0x12, 0x15, 0x2e, 0x6c, 0x61, 0x72, 0x6b, 0x70, 0x72, 0x6f, 0x78, 0x79, 0x2e, 0x47, 0x65, 0x74,
	0x43, 0x68, 0x61, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x16, 0x2e, 0x6c, 0x61, 0x72, 0x6b, 0x70, 0x72,
	0x6f, 0x78, 0x79, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x68, 0x61, 0x74, 0x52, 0x65, 0x73, 0x70, 0x12,
	0x3b, 0x0a, 0x08, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x68, 0x61, 0x74, 0x12, 0x16, 0x2e, 0x6c, 0x61,
	0x72, 0x6b, 0x70, 0x72, 0x6f, 0x78, 0x79, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x68, 0x61, 0x74,
	0x52, 0x65, 0x71, 0x1a, 0x17, 0x2e, 0x6c, 0x61, 0x72, 0x6b, 0x70, 0x72, 0x6f, 0x78, 0x79, 0x2e,
	0x4c, 0x69, 0x73, 0x74, 0x43, 0x68, 0x61, 0x74, 0x52, 0x65, 0x73, 0x70, 0x32, 0x99, 0x02, 0x0a,
	0x18, 0x4c, 0x61, 0x72, 0x6b, 0x49, 0x4d, 0x43, 0x68, 0x61, 0x74, 0x4d, 0x65, 0x6d, 0x62, 0x65,
	0x72, 0x73, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x56, 0x0a, 0x11, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x43, 0x68, 0x61, 0x74, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x12, 0x1f,
	0x2e, 0x6c, 0x61, 0x72, 0x6b, 0x70, 0x72, 0x6f, 0x78, 0x79, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x43, 0x68, 0x61, 0x74, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x52, 0x65, 0x71, 0x1a,
	0x20, 0x2e, 0x6c, 0x61, 0x72, 0x6b, 0x70, 0x72, 0x6f, 0x78, 0x79, 0x2e, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x43, 0x68, 0x61, 0x74, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x52, 0x65, 0x73,
	0x70, 0x12, 0x56, 0x0a, 0x11, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x43, 0x68, 0x61, 0x74, 0x4d,
	0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x12, 0x1f, 0x2e, 0x6c, 0x61, 0x72, 0x6b, 0x70, 0x72, 0x6f,
	0x78, 0x79, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x43, 0x68, 0x61, 0x74, 0x4d, 0x65, 0x6d,
	0x62, 0x65, 0x72, 0x73, 0x52, 0x65, 0x71, 0x1a, 0x20, 0x2e, 0x6c, 0x61, 0x72, 0x6b, 0x70, 0x72,
	0x6f, 0x78, 0x79, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x43, 0x68, 0x61, 0x74, 0x4d, 0x65,
	0x6d, 0x62, 0x65, 0x72, 0x73, 0x52, 0x65, 0x73, 0x70, 0x12, 0x4d, 0x0a, 0x0e, 0x47, 0x65, 0x74,
	0x43, 0x68, 0x61, 0x74, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x12, 0x1c, 0x2e, 0x6c, 0x61,
	0x72, 0x6b, 0x70, 0x72, 0x6f, 0x78, 0x79, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x68, 0x61, 0x74, 0x4d,
	0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x52, 0x65, 0x71, 0x1a, 0x1d, 0x2e, 0x6c, 0x61, 0x72, 0x6b,
	0x70, 0x72, 0x6f, 0x78, 0x79, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x68, 0x61, 0x74, 0x4d, 0x65, 0x6d,
	0x62, 0x65, 0x72, 0x73, 0x52, 0x65, 0x73, 0x70, 0x32, 0x76, 0x0a, 0x19, 0x4c, 0x61, 0x72, 0x6b,
	0x49, 0x4d, 0x43, 0x68, 0x61, 0x74, 0x4d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x73, 0x53, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x59, 0x0a, 0x12, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x43,
	0x68, 0x61, 0x74, 0x4d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x73, 0x12, 0x20, 0x2e, 0x6c, 0x61,
	0x72, 0x6b, 0x70, 0x72, 0x6f, 0x78, 0x79, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x43, 0x68,
	0x61, 0x74, 0x4d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x73, 0x52, 0x65, 0x71, 0x1a, 0x21, 0x2e,
	0x6c, 0x61, 0x72, 0x6b, 0x70, 0x72, 0x6f, 0x78, 0x79, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x43, 0x68, 0x61, 0x74, 0x4d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x73, 0x52, 0x65, 0x73, 0x70,
	0x42, 0x43, 0x5a, 0x41, 0x67, 0x69, 0x74, 0x6c, 0x61, 0x62, 0x2e, 0x74, 0x74, 0x79, 0x75, 0x79,
	0x69, 0x6e, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x54, 0x65, 0x73, 0x74, 0x44, 0x65, 0x76, 0x65, 0x6c,
	0x6f, 0x70, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x70, 0x72, 0x6f, 0x62, 0x65, 0x2d, 0x62, 0x61, 0x63,
	0x6b, 0x65, 0x6e, 0x64, 0x2f, 0x6c, 0x61, 0x72, 0x6b, 0x70, 0x72, 0x6f, 0x78, 0x79, 0x2f, 0x72,
	0x70, 0x63, 0x2f, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_larkproxy_larkproxy_proto_rawDescOnce sync.Once
	file_larkproxy_larkproxy_proto_rawDescData = file_larkproxy_larkproxy_proto_rawDesc
)

func file_larkproxy_larkproxy_proto_rawDescGZIP() []byte {
	file_larkproxy_larkproxy_proto_rawDescOnce.Do(func() {
		file_larkproxy_larkproxy_proto_rawDescData = protoimpl.X.CompressGZIP(file_larkproxy_larkproxy_proto_rawDescData)
	})
	return file_larkproxy_larkproxy_proto_rawDescData
}

var file_larkproxy_larkproxy_proto_msgTypes = make([]protoimpl.MessageInfo, 18)
var file_larkproxy_larkproxy_proto_goTypes = []any{
	(*GetBatchUserIDReq)(nil),          // 0: larkproxy.GetBatchUserIDReq
	(*GetBatchUserIDResp)(nil),         // 1: larkproxy.GetBatchUserIDResp
	(*CreateChatReq)(nil),              // 2: larkproxy.CreateChatReq
	(*CreateChatResp)(nil),             // 3: larkproxy.CreateChatResp
	(*DeleteChatReq)(nil),              // 4: larkproxy.DeleteChatReq
	(*DeleteChatResp)(nil),             // 5: larkproxy.DeleteChatResp
	(*GetChatReq)(nil),                 // 6: larkproxy.GetChatReq
	(*GetChatResp)(nil),                // 7: larkproxy.GetChatResp
	(*ListChatReq)(nil),                // 8: larkproxy.ListChatReq
	(*ListChatResp)(nil),               // 9: larkproxy.ListChatResp
	(*CreateChatMembersReq)(nil),       // 10: larkproxy.CreateChatMembersReq
	(*CreateChatMembersResp)(nil),      // 11: larkproxy.CreateChatMembersResp
	(*DeleteChatMembersReq)(nil),       // 12: larkproxy.DeleteChatMembersReq
	(*DeleteChatMembersResp)(nil),      // 13: larkproxy.DeleteChatMembersResp
	(*GetChatMembersReq)(nil),          // 14: larkproxy.GetChatMembersReq
	(*GetChatMembersResp)(nil),         // 15: larkproxy.GetChatMembersResp
	(*CreateChatManagersReq)(nil),      // 16: larkproxy.CreateChatManagersReq
	(*CreateChatManagersResp)(nil),     // 17: larkproxy.CreateChatManagersResp
	(*UserContactInfo)(nil),            // 18: larkproxy.UserContactInfo
	(*CreateChatRespData)(nil),         // 19: larkproxy.CreateChatRespData
	(*GetChatRespData)(nil),            // 20: larkproxy.GetChatRespData
	(*ListChatRespData)(nil),           // 21: larkproxy.ListChatRespData
	(*CreateChatMembersRespData)(nil),  // 22: larkproxy.CreateChatMembersRespData
	(*GetChatMembersRespData)(nil),     // 23: larkproxy.GetChatMembersRespData
	(*CreateChatManagersRespData)(nil), // 24: larkproxy.CreateChatManagersRespData
}
var file_larkproxy_larkproxy_proto_depIdxs = []int32{
	18, // 0: larkproxy.GetBatchUserIDResp.items:type_name -> larkproxy.UserContactInfo
	19, // 1: larkproxy.CreateChatResp.data:type_name -> larkproxy.CreateChatRespData
	20, // 2: larkproxy.GetChatResp.data:type_name -> larkproxy.GetChatRespData
	21, // 3: larkproxy.ListChatResp.items:type_name -> larkproxy.ListChatRespData
	22, // 4: larkproxy.CreateChatMembersResp.data:type_name -> larkproxy.CreateChatMembersRespData
	23, // 5: larkproxy.GetChatMembersResp.items:type_name -> larkproxy.GetChatMembersRespData
	24, // 6: larkproxy.CreateChatManagersResp.data:type_name -> larkproxy.CreateChatManagersRespData
	0,  // 7: larkproxy.LarkContactUserService.GetBatchUserID:input_type -> larkproxy.GetBatchUserIDReq
	2,  // 8: larkproxy.LarkIMChatsService.CreateChat:input_type -> larkproxy.CreateChatReq
	4,  // 9: larkproxy.LarkIMChatsService.DeleteChat:input_type -> larkproxy.DeleteChatReq
	6,  // 10: larkproxy.LarkIMChatsService.GetChat:input_type -> larkproxy.GetChatReq
	8,  // 11: larkproxy.LarkIMChatsService.ListChat:input_type -> larkproxy.ListChatReq
	10, // 12: larkproxy.LarkIMChatMembersService.CreateChatMembers:input_type -> larkproxy.CreateChatMembersReq
	12, // 13: larkproxy.LarkIMChatMembersService.DeleteChatMembers:input_type -> larkproxy.DeleteChatMembersReq
	14, // 14: larkproxy.LarkIMChatMembersService.GetChatMembers:input_type -> larkproxy.GetChatMembersReq
	16, // 15: larkproxy.LarkIMChatManagersService.CreateChatManagers:input_type -> larkproxy.CreateChatManagersReq
	1,  // 16: larkproxy.LarkContactUserService.GetBatchUserID:output_type -> larkproxy.GetBatchUserIDResp
	3,  // 17: larkproxy.LarkIMChatsService.CreateChat:output_type -> larkproxy.CreateChatResp
	5,  // 18: larkproxy.LarkIMChatsService.DeleteChat:output_type -> larkproxy.DeleteChatResp
	7,  // 19: larkproxy.LarkIMChatsService.GetChat:output_type -> larkproxy.GetChatResp
	9,  // 20: larkproxy.LarkIMChatsService.ListChat:output_type -> larkproxy.ListChatResp
	11, // 21: larkproxy.LarkIMChatMembersService.CreateChatMembers:output_type -> larkproxy.CreateChatMembersResp
	13, // 22: larkproxy.LarkIMChatMembersService.DeleteChatMembers:output_type -> larkproxy.DeleteChatMembersResp
	15, // 23: larkproxy.LarkIMChatMembersService.GetChatMembers:output_type -> larkproxy.GetChatMembersResp
	17, // 24: larkproxy.LarkIMChatManagersService.CreateChatManagers:output_type -> larkproxy.CreateChatManagersResp
	16, // [16:25] is the sub-list for method output_type
	7,  // [7:16] is the sub-list for method input_type
	7,  // [7:7] is the sub-list for extension type_name
	7,  // [7:7] is the sub-list for extension extendee
	0,  // [0:7] is the sub-list for field type_name
}

func init() { file_larkproxy_larkproxy_proto_init() }
func file_larkproxy_larkproxy_proto_init() {
	if File_larkproxy_larkproxy_proto != nil {
		return
	}
	file_larkproxy_contact_user_proto_init()
	file_larkproxy_im_chat_members_proto_init()
	file_larkproxy_im_chats_proto_init()
	file_larkproxy_im_chat_managers_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_larkproxy_larkproxy_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   18,
			NumExtensions: 0,
			NumServices:   4,
		},
		GoTypes:           file_larkproxy_larkproxy_proto_goTypes,
		DependencyIndexes: file_larkproxy_larkproxy_proto_depIdxs,
		MessageInfos:      file_larkproxy_larkproxy_proto_msgTypes,
	}.Build()
	File_larkproxy_larkproxy_proto = out.File
	file_larkproxy_larkproxy_proto_rawDesc = nil
	file_larkproxy_larkproxy_proto_goTypes = nil
	file_larkproxy_larkproxy_proto_depIdxs = nil
}
