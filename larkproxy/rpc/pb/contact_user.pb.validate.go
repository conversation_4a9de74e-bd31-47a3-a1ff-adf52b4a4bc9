// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: larkproxy/contact_user.proto

package pb

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on UserContactInfo with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *UserContactInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UserContactInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UserContactInfoMultiError, or nil if none found.
func (m *UserContactInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *UserContactInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for UserId

	// no validation rules for Mobile

	// no validation rules for Email

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UserContactInfoValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UserContactInfoValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UserContactInfoValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UserContactInfoMultiError(errors)
	}

	return nil
}

// UserContactInfoMultiError is an error wrapping multiple validation errors
// returned by UserContactInfo.ValidateAll() if the designated constraints
// aren't met.
type UserContactInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UserContactInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UserContactInfoMultiError) AllErrors() []error { return m }

// UserContactInfoValidationError is the validation error returned by
// UserContactInfo.Validate if the designated constraints aren't met.
type UserContactInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UserContactInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UserContactInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UserContactInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UserContactInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UserContactInfoValidationError) ErrorName() string { return "UserContactInfoValidationError" }

// Error satisfies the builtin error interface
func (e UserContactInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUserContactInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UserContactInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UserContactInfoValidationError{}

// Validate checks the field values on UserStatus with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *UserStatus) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UserStatus with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in UserStatusMultiError, or
// nil if none found.
func (m *UserStatus) ValidateAll() error {
	return m.validate(true)
}

func (m *UserStatus) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for IsFrozen

	// no validation rules for IsResigned

	// no validation rules for IsActivated

	// no validation rules for IsExited

	// no validation rules for IsUnjoin

	if len(errors) > 0 {
		return UserStatusMultiError(errors)
	}

	return nil
}

// UserStatusMultiError is an error wrapping multiple validation errors
// returned by UserStatus.ValidateAll() if the designated constraints aren't met.
type UserStatusMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UserStatusMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UserStatusMultiError) AllErrors() []error { return m }

// UserStatusValidationError is the validation error returned by
// UserStatus.Validate if the designated constraints aren't met.
type UserStatusValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UserStatusValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UserStatusValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UserStatusValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UserStatusValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UserStatusValidationError) ErrorName() string { return "UserStatusValidationError" }

// Error satisfies the builtin error interface
func (e UserStatusValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUserStatus.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UserStatusValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UserStatusValidationError{}
