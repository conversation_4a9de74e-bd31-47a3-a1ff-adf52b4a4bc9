// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: larkproxy/larkproxy.proto

package pb

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on GetBatchUserIDReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *GetBatchUserIDReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetBatchUserIDReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetBatchUserIDReqMultiError, or nil if none found.
func (m *GetBatchUserIDReq) ValidateAll() error {
	return m.validate(true)
}

func (m *GetBatchUserIDReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return GetBatchUserIDReqMultiError(errors)
	}

	return nil
}

// GetBatchUserIDReqMultiError is an error wrapping multiple validation errors
// returned by GetBatchUserIDReq.ValidateAll() if the designated constraints
// aren't met.
type GetBatchUserIDReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetBatchUserIDReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetBatchUserIDReqMultiError) AllErrors() []error { return m }

// GetBatchUserIDReqValidationError is the validation error returned by
// GetBatchUserIDReq.Validate if the designated constraints aren't met.
type GetBatchUserIDReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetBatchUserIDReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetBatchUserIDReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetBatchUserIDReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetBatchUserIDReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetBatchUserIDReqValidationError) ErrorName() string {
	return "GetBatchUserIDReqValidationError"
}

// Error satisfies the builtin error interface
func (e GetBatchUserIDReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetBatchUserIDReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetBatchUserIDReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetBatchUserIDReqValidationError{}

// Validate checks the field values on GetBatchUserIDResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetBatchUserIDResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetBatchUserIDResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetBatchUserIDRespMultiError, or nil if none found.
func (m *GetBatchUserIDResp) ValidateAll() error {
	return m.validate(true)
}

func (m *GetBatchUserIDResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetItems() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetBatchUserIDRespValidationError{
						field:  fmt.Sprintf("Items[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetBatchUserIDRespValidationError{
						field:  fmt.Sprintf("Items[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetBatchUserIDRespValidationError{
					field:  fmt.Sprintf("Items[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetBatchUserIDRespMultiError(errors)
	}

	return nil
}

// GetBatchUserIDRespMultiError is an error wrapping multiple validation errors
// returned by GetBatchUserIDResp.ValidateAll() if the designated constraints
// aren't met.
type GetBatchUserIDRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetBatchUserIDRespMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetBatchUserIDRespMultiError) AllErrors() []error { return m }

// GetBatchUserIDRespValidationError is the validation error returned by
// GetBatchUserIDResp.Validate if the designated constraints aren't met.
type GetBatchUserIDRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetBatchUserIDRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetBatchUserIDRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetBatchUserIDRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetBatchUserIDRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetBatchUserIDRespValidationError) ErrorName() string {
	return "GetBatchUserIDRespValidationError"
}

// Error satisfies the builtin error interface
func (e GetBatchUserIDRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetBatchUserIDResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetBatchUserIDRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetBatchUserIDRespValidationError{}

// Validate checks the field values on CreateChatReq with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *CreateChatReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateChatReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in CreateChatReqMultiError, or
// nil if none found.
func (m *CreateChatReq) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateChatReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Uuid

	// no validation rules for Name

	// no validation rules for Description

	if len(errors) > 0 {
		return CreateChatReqMultiError(errors)
	}

	return nil
}

// CreateChatReqMultiError is an error wrapping multiple validation errors
// returned by CreateChatReq.ValidateAll() if the designated constraints
// aren't met.
type CreateChatReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateChatReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateChatReqMultiError) AllErrors() []error { return m }

// CreateChatReqValidationError is the validation error returned by
// CreateChatReq.Validate if the designated constraints aren't met.
type CreateChatReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateChatReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateChatReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateChatReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateChatReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateChatReqValidationError) ErrorName() string { return "CreateChatReqValidationError" }

// Error satisfies the builtin error interface
func (e CreateChatReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateChatReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateChatReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateChatReqValidationError{}

// Validate checks the field values on CreateChatResp with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *CreateChatResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateChatResp with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in CreateChatRespMultiError,
// or nil if none found.
func (m *CreateChatResp) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateChatResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateChatRespValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateChatRespValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateChatRespValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreateChatRespMultiError(errors)
	}

	return nil
}

// CreateChatRespMultiError is an error wrapping multiple validation errors
// returned by CreateChatResp.ValidateAll() if the designated constraints
// aren't met.
type CreateChatRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateChatRespMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateChatRespMultiError) AllErrors() []error { return m }

// CreateChatRespValidationError is the validation error returned by
// CreateChatResp.Validate if the designated constraints aren't met.
type CreateChatRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateChatRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateChatRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateChatRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateChatRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateChatRespValidationError) ErrorName() string { return "CreateChatRespValidationError" }

// Error satisfies the builtin error interface
func (e CreateChatRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateChatResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateChatRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateChatRespValidationError{}

// Validate checks the field values on DeleteChatReq with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *DeleteChatReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteChatReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in DeleteChatReqMultiError, or
// nil if none found.
func (m *DeleteChatReq) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteChatReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ChatId

	if len(errors) > 0 {
		return DeleteChatReqMultiError(errors)
	}

	return nil
}

// DeleteChatReqMultiError is an error wrapping multiple validation errors
// returned by DeleteChatReq.ValidateAll() if the designated constraints
// aren't met.
type DeleteChatReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteChatReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteChatReqMultiError) AllErrors() []error { return m }

// DeleteChatReqValidationError is the validation error returned by
// DeleteChatReq.Validate if the designated constraints aren't met.
type DeleteChatReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteChatReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteChatReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteChatReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteChatReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteChatReqValidationError) ErrorName() string { return "DeleteChatReqValidationError" }

// Error satisfies the builtin error interface
func (e DeleteChatReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteChatReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteChatReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteChatReqValidationError{}

// Validate checks the field values on DeleteChatResp with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *DeleteChatResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteChatResp with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in DeleteChatRespMultiError,
// or nil if none found.
func (m *DeleteChatResp) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteChatResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return DeleteChatRespMultiError(errors)
	}

	return nil
}

// DeleteChatRespMultiError is an error wrapping multiple validation errors
// returned by DeleteChatResp.ValidateAll() if the designated constraints
// aren't met.
type DeleteChatRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteChatRespMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteChatRespMultiError) AllErrors() []error { return m }

// DeleteChatRespValidationError is the validation error returned by
// DeleteChatResp.Validate if the designated constraints aren't met.
type DeleteChatRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteChatRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteChatRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteChatRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteChatRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteChatRespValidationError) ErrorName() string { return "DeleteChatRespValidationError" }

// Error satisfies the builtin error interface
func (e DeleteChatRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteChatResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteChatRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteChatRespValidationError{}

// Validate checks the field values on GetChatReq with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GetChatReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetChatReq with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in GetChatReqMultiError, or
// nil if none found.
func (m *GetChatReq) ValidateAll() error {
	return m.validate(true)
}

func (m *GetChatReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ChatId

	if len(errors) > 0 {
		return GetChatReqMultiError(errors)
	}

	return nil
}

// GetChatReqMultiError is an error wrapping multiple validation errors
// returned by GetChatReq.ValidateAll() if the designated constraints aren't met.
type GetChatReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetChatReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetChatReqMultiError) AllErrors() []error { return m }

// GetChatReqValidationError is the validation error returned by
// GetChatReq.Validate if the designated constraints aren't met.
type GetChatReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetChatReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetChatReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetChatReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetChatReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetChatReqValidationError) ErrorName() string { return "GetChatReqValidationError" }

// Error satisfies the builtin error interface
func (e GetChatReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetChatReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetChatReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetChatReqValidationError{}

// Validate checks the field values on GetChatResp with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GetChatResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetChatResp with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in GetChatRespMultiError, or
// nil if none found.
func (m *GetChatResp) ValidateAll() error {
	return m.validate(true)
}

func (m *GetChatResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetChatRespValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetChatRespValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetChatRespValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetChatRespMultiError(errors)
	}

	return nil
}

// GetChatRespMultiError is an error wrapping multiple validation errors
// returned by GetChatResp.ValidateAll() if the designated constraints aren't met.
type GetChatRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetChatRespMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetChatRespMultiError) AllErrors() []error { return m }

// GetChatRespValidationError is the validation error returned by
// GetChatResp.Validate if the designated constraints aren't met.
type GetChatRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetChatRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetChatRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetChatRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetChatRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetChatRespValidationError) ErrorName() string { return "GetChatRespValidationError" }

// Error satisfies the builtin error interface
func (e GetChatRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetChatResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetChatRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetChatRespValidationError{}

// Validate checks the field values on ListChatReq with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ListChatReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListChatReq with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ListChatReqMultiError, or
// nil if none found.
func (m *ListChatReq) ValidateAll() error {
	return m.validate(true)
}

func (m *ListChatReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for WithCurrentUser

	if len(errors) > 0 {
		return ListChatReqMultiError(errors)
	}

	return nil
}

// ListChatReqMultiError is an error wrapping multiple validation errors
// returned by ListChatReq.ValidateAll() if the designated constraints aren't met.
type ListChatReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListChatReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListChatReqMultiError) AllErrors() []error { return m }

// ListChatReqValidationError is the validation error returned by
// ListChatReq.Validate if the designated constraints aren't met.
type ListChatReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListChatReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListChatReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListChatReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListChatReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListChatReqValidationError) ErrorName() string { return "ListChatReqValidationError" }

// Error satisfies the builtin error interface
func (e ListChatReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListChatReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListChatReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListChatReqValidationError{}

// Validate checks the field values on ListChatResp with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ListChatResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListChatResp with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ListChatRespMultiError, or
// nil if none found.
func (m *ListChatResp) ValidateAll() error {
	return m.validate(true)
}

func (m *ListChatResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetItems() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListChatRespValidationError{
						field:  fmt.Sprintf("Items[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListChatRespValidationError{
						field:  fmt.Sprintf("Items[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListChatRespValidationError{
					field:  fmt.Sprintf("Items[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ListChatRespMultiError(errors)
	}

	return nil
}

// ListChatRespMultiError is an error wrapping multiple validation errors
// returned by ListChatResp.ValidateAll() if the designated constraints aren't met.
type ListChatRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListChatRespMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListChatRespMultiError) AllErrors() []error { return m }

// ListChatRespValidationError is the validation error returned by
// ListChatResp.Validate if the designated constraints aren't met.
type ListChatRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListChatRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListChatRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListChatRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListChatRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListChatRespValidationError) ErrorName() string { return "ListChatRespValidationError" }

// Error satisfies the builtin error interface
func (e ListChatRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListChatResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListChatRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListChatRespValidationError{}

// Validate checks the field values on CreateChatMembersReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateChatMembersReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateChatMembersReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateChatMembersReqMultiError, or nil if none found.
func (m *CreateChatMembersReq) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateChatMembersReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ChatId

	if len(errors) > 0 {
		return CreateChatMembersReqMultiError(errors)
	}

	return nil
}

// CreateChatMembersReqMultiError is an error wrapping multiple validation
// errors returned by CreateChatMembersReq.ValidateAll() if the designated
// constraints aren't met.
type CreateChatMembersReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateChatMembersReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateChatMembersReqMultiError) AllErrors() []error { return m }

// CreateChatMembersReqValidationError is the validation error returned by
// CreateChatMembersReq.Validate if the designated constraints aren't met.
type CreateChatMembersReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateChatMembersReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateChatMembersReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateChatMembersReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateChatMembersReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateChatMembersReqValidationError) ErrorName() string {
	return "CreateChatMembersReqValidationError"
}

// Error satisfies the builtin error interface
func (e CreateChatMembersReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateChatMembersReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateChatMembersReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateChatMembersReqValidationError{}

// Validate checks the field values on CreateChatMembersResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateChatMembersResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateChatMembersResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateChatMembersRespMultiError, or nil if none found.
func (m *CreateChatMembersResp) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateChatMembersResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateChatMembersRespValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateChatMembersRespValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateChatMembersRespValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreateChatMembersRespMultiError(errors)
	}

	return nil
}

// CreateChatMembersRespMultiError is an error wrapping multiple validation
// errors returned by CreateChatMembersResp.ValidateAll() if the designated
// constraints aren't met.
type CreateChatMembersRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateChatMembersRespMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateChatMembersRespMultiError) AllErrors() []error { return m }

// CreateChatMembersRespValidationError is the validation error returned by
// CreateChatMembersResp.Validate if the designated constraints aren't met.
type CreateChatMembersRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateChatMembersRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateChatMembersRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateChatMembersRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateChatMembersRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateChatMembersRespValidationError) ErrorName() string {
	return "CreateChatMembersRespValidationError"
}

// Error satisfies the builtin error interface
func (e CreateChatMembersRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateChatMembersResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateChatMembersRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateChatMembersRespValidationError{}

// Validate checks the field values on DeleteChatMembersReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DeleteChatMembersReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteChatMembersReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeleteChatMembersReqMultiError, or nil if none found.
func (m *DeleteChatMembersReq) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteChatMembersReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return DeleteChatMembersReqMultiError(errors)
	}

	return nil
}

// DeleteChatMembersReqMultiError is an error wrapping multiple validation
// errors returned by DeleteChatMembersReq.ValidateAll() if the designated
// constraints aren't met.
type DeleteChatMembersReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteChatMembersReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteChatMembersReqMultiError) AllErrors() []error { return m }

// DeleteChatMembersReqValidationError is the validation error returned by
// DeleteChatMembersReq.Validate if the designated constraints aren't met.
type DeleteChatMembersReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteChatMembersReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteChatMembersReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteChatMembersReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteChatMembersReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteChatMembersReqValidationError) ErrorName() string {
	return "DeleteChatMembersReqValidationError"
}

// Error satisfies the builtin error interface
func (e DeleteChatMembersReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteChatMembersReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteChatMembersReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteChatMembersReqValidationError{}

// Validate checks the field values on DeleteChatMembersResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DeleteChatMembersResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteChatMembersResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeleteChatMembersRespMultiError, or nil if none found.
func (m *DeleteChatMembersResp) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteChatMembersResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return DeleteChatMembersRespMultiError(errors)
	}

	return nil
}

// DeleteChatMembersRespMultiError is an error wrapping multiple validation
// errors returned by DeleteChatMembersResp.ValidateAll() if the designated
// constraints aren't met.
type DeleteChatMembersRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteChatMembersRespMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteChatMembersRespMultiError) AllErrors() []error { return m }

// DeleteChatMembersRespValidationError is the validation error returned by
// DeleteChatMembersResp.Validate if the designated constraints aren't met.
type DeleteChatMembersRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteChatMembersRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteChatMembersRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteChatMembersRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteChatMembersRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteChatMembersRespValidationError) ErrorName() string {
	return "DeleteChatMembersRespValidationError"
}

// Error satisfies the builtin error interface
func (e DeleteChatMembersRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteChatMembersResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteChatMembersRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteChatMembersRespValidationError{}

// Validate checks the field values on GetChatMembersReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *GetChatMembersReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetChatMembersReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetChatMembersReqMultiError, or nil if none found.
func (m *GetChatMembersReq) ValidateAll() error {
	return m.validate(true)
}

func (m *GetChatMembersReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ChatId

	if len(errors) > 0 {
		return GetChatMembersReqMultiError(errors)
	}

	return nil
}

// GetChatMembersReqMultiError is an error wrapping multiple validation errors
// returned by GetChatMembersReq.ValidateAll() if the designated constraints
// aren't met.
type GetChatMembersReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetChatMembersReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetChatMembersReqMultiError) AllErrors() []error { return m }

// GetChatMembersReqValidationError is the validation error returned by
// GetChatMembersReq.Validate if the designated constraints aren't met.
type GetChatMembersReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetChatMembersReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetChatMembersReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetChatMembersReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetChatMembersReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetChatMembersReqValidationError) ErrorName() string {
	return "GetChatMembersReqValidationError"
}

// Error satisfies the builtin error interface
func (e GetChatMembersReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetChatMembersReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetChatMembersReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetChatMembersReqValidationError{}

// Validate checks the field values on GetChatMembersResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetChatMembersResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetChatMembersResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetChatMembersRespMultiError, or nil if none found.
func (m *GetChatMembersResp) ValidateAll() error {
	return m.validate(true)
}

func (m *GetChatMembersResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetItems() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetChatMembersRespValidationError{
						field:  fmt.Sprintf("Items[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetChatMembersRespValidationError{
						field:  fmt.Sprintf("Items[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetChatMembersRespValidationError{
					field:  fmt.Sprintf("Items[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetChatMembersRespMultiError(errors)
	}

	return nil
}

// GetChatMembersRespMultiError is an error wrapping multiple validation errors
// returned by GetChatMembersResp.ValidateAll() if the designated constraints
// aren't met.
type GetChatMembersRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetChatMembersRespMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetChatMembersRespMultiError) AllErrors() []error { return m }

// GetChatMembersRespValidationError is the validation error returned by
// GetChatMembersResp.Validate if the designated constraints aren't met.
type GetChatMembersRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetChatMembersRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetChatMembersRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetChatMembersRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetChatMembersRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetChatMembersRespValidationError) ErrorName() string {
	return "GetChatMembersRespValidationError"
}

// Error satisfies the builtin error interface
func (e GetChatMembersRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetChatMembersResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetChatMembersRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetChatMembersRespValidationError{}

// Validate checks the field values on CreateChatManagersReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateChatManagersReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateChatManagersReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateChatManagersReqMultiError, or nil if none found.
func (m *CreateChatManagersReq) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateChatManagersReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ChatId

	if len(errors) > 0 {
		return CreateChatManagersReqMultiError(errors)
	}

	return nil
}

// CreateChatManagersReqMultiError is an error wrapping multiple validation
// errors returned by CreateChatManagersReq.ValidateAll() if the designated
// constraints aren't met.
type CreateChatManagersReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateChatManagersReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateChatManagersReqMultiError) AllErrors() []error { return m }

// CreateChatManagersReqValidationError is the validation error returned by
// CreateChatManagersReq.Validate if the designated constraints aren't met.
type CreateChatManagersReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateChatManagersReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateChatManagersReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateChatManagersReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateChatManagersReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateChatManagersReqValidationError) ErrorName() string {
	return "CreateChatManagersReqValidationError"
}

// Error satisfies the builtin error interface
func (e CreateChatManagersReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateChatManagersReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateChatManagersReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateChatManagersReqValidationError{}

// Validate checks the field values on CreateChatManagersResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateChatManagersResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateChatManagersResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateChatManagersRespMultiError, or nil if none found.
func (m *CreateChatManagersResp) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateChatManagersResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateChatManagersRespValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateChatManagersRespValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateChatManagersRespValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreateChatManagersRespMultiError(errors)
	}

	return nil
}

// CreateChatManagersRespMultiError is an error wrapping multiple validation
// errors returned by CreateChatManagersResp.ValidateAll() if the designated
// constraints aren't met.
type CreateChatManagersRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateChatManagersRespMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateChatManagersRespMultiError) AllErrors() []error { return m }

// CreateChatManagersRespValidationError is the validation error returned by
// CreateChatManagersResp.Validate if the designated constraints aren't met.
type CreateChatManagersRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateChatManagersRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateChatManagersRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateChatManagersRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateChatManagersRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateChatManagersRespValidationError) ErrorName() string {
	return "CreateChatManagersRespValidationError"
}

// Error satisfies the builtin error interface
func (e CreateChatManagersRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateChatManagersResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateChatManagersRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateChatManagersRespValidationError{}
