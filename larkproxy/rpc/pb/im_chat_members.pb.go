// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.1
// 	protoc        v5.29.2
// source: larkproxy/im_chat_members.proto

package pb

import (
	reflect "reflect"
	sync "sync"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type CreateChatMembersRespData struct {
	state                 protoimpl.MessageState `protogen:"open.v1"`
	InvalidIdList         []string               `protobuf:"bytes,1,rep,name=invalid_id_list,json=invalidIdList,proto3" json:"invalid_id_list,omitempty"`                           // 无效成员列表
	NotExistedIdList      []string               `protobuf:"bytes,2,rep,name=not_existed_id_list,json=notExistedIdList,proto3" json:"not_existed_id_list,omitempty"`                // ID不存在的成员列表
	PendingApprovalIdList []string               `protobuf:"bytes,3,rep,name=pending_approval_id_list,json=pendingApprovalIdList,proto3" json:"pending_approval_id_list,omitempty"` // 等待群主或管理员审批的成员ID列表
	unknownFields         protoimpl.UnknownFields
	sizeCache             protoimpl.SizeCache
}

func (x *CreateChatMembersRespData) Reset() {
	*x = CreateChatMembersRespData{}
	mi := &file_larkproxy_im_chat_members_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateChatMembersRespData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateChatMembersRespData) ProtoMessage() {}

func (x *CreateChatMembersRespData) ProtoReflect() protoreflect.Message {
	mi := &file_larkproxy_im_chat_members_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateChatMembersRespData.ProtoReflect.Descriptor instead.
func (*CreateChatMembersRespData) Descriptor() ([]byte, []int) {
	return file_larkproxy_im_chat_members_proto_rawDescGZIP(), []int{0}
}

func (x *CreateChatMembersRespData) GetInvalidIdList() []string {
	if x != nil {
		return x.InvalidIdList
	}
	return nil
}

func (x *CreateChatMembersRespData) GetNotExistedIdList() []string {
	if x != nil {
		return x.NotExistedIdList
	}
	return nil
}

func (x *CreateChatMembersRespData) GetPendingApprovalIdList() []string {
	if x != nil {
		return x.PendingApprovalIdList
	}
	return nil
}

type GetChatMembersRespData struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	MemberIdType  string                 `protobuf:"bytes,1,opt,name=member_id_type,json=memberIdType,proto3" json:"member_id_type,omitempty"` // 成员的用户ID类型
	MemberId      string                 `protobuf:"bytes,2,opt,name=member_id,json=memberId,proto3" json:"member_id,omitempty"`               // 成员的用户ID
	Name          string                 `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`                                       // 成员的名字
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetChatMembersRespData) Reset() {
	*x = GetChatMembersRespData{}
	mi := &file_larkproxy_im_chat_members_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetChatMembersRespData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetChatMembersRespData) ProtoMessage() {}

func (x *GetChatMembersRespData) ProtoReflect() protoreflect.Message {
	mi := &file_larkproxy_im_chat_members_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetChatMembersRespData.ProtoReflect.Descriptor instead.
func (*GetChatMembersRespData) Descriptor() ([]byte, []int) {
	return file_larkproxy_im_chat_members_proto_rawDescGZIP(), []int{1}
}

func (x *GetChatMembersRespData) GetMemberIdType() string {
	if x != nil {
		return x.MemberIdType
	}
	return ""
}

func (x *GetChatMembersRespData) GetMemberId() string {
	if x != nil {
		return x.MemberId
	}
	return ""
}

func (x *GetChatMembersRespData) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

var File_larkproxy_im_chat_members_proto protoreflect.FileDescriptor

var file_larkproxy_im_chat_members_proto_rawDesc = []byte{
	0x0a, 0x1f, 0x6c, 0x61, 0x72, 0x6b, 0x70, 0x72, 0x6f, 0x78, 0x79, 0x2f, 0x69, 0x6d, 0x5f, 0x63,
	0x68, 0x61, 0x74, 0x5f, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x12, 0x09, 0x6c, 0x61, 0x72, 0x6b, 0x70, 0x72, 0x6f, 0x78, 0x79, 0x22, 0xab, 0x01, 0x0a,
	0x19, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x43, 0x68, 0x61, 0x74, 0x4d, 0x65, 0x6d, 0x62, 0x65,
	0x72, 0x73, 0x52, 0x65, 0x73, 0x70, 0x44, 0x61, 0x74, 0x61, 0x12, 0x26, 0x0a, 0x0f, 0x69, 0x6e,
	0x76, 0x61, 0x6c, 0x69, 0x64, 0x5f, 0x69, 0x64, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x09, 0x52, 0x0d, 0x69, 0x6e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x49, 0x64, 0x4c, 0x69,
	0x73, 0x74, 0x12, 0x2d, 0x0a, 0x13, 0x6e, 0x6f, 0x74, 0x5f, 0x65, 0x78, 0x69, 0x73, 0x74, 0x65,
	0x64, 0x5f, 0x69, 0x64, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52,
	0x10, 0x6e, 0x6f, 0x74, 0x45, 0x78, 0x69, 0x73, 0x74, 0x65, 0x64, 0x49, 0x64, 0x4c, 0x69, 0x73,
	0x74, 0x12, 0x37, 0x0a, 0x18, 0x70, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x61, 0x70, 0x70,
	0x72, 0x6f, 0x76, 0x61, 0x6c, 0x5f, 0x69, 0x64, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x03, 0x20,
	0x03, 0x28, 0x09, 0x52, 0x15, 0x70, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x41, 0x70, 0x70, 0x72,
	0x6f, 0x76, 0x61, 0x6c, 0x49, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x22, 0x6f, 0x0a, 0x16, 0x47, 0x65,
	0x74, 0x43, 0x68, 0x61, 0x74, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x52, 0x65, 0x73, 0x70,
	0x44, 0x61, 0x74, 0x61, 0x12, 0x24, 0x0a, 0x0e, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x5f, 0x69,
	0x64, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x6d, 0x65,
	0x6d, 0x62, 0x65, 0x72, 0x49, 0x64, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x6d, 0x65,
	0x6d, 0x62, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6d,
	0x65, 0x6d, 0x62, 0x65, 0x72, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x42, 0x43, 0x5a, 0x41, 0x67,
	0x69, 0x74, 0x6c, 0x61, 0x62, 0x2e, 0x74, 0x74, 0x79, 0x75, 0x79, 0x69, 0x6e, 0x2e, 0x63, 0x6f,
	0x6d, 0x2f, 0x54, 0x65, 0x73, 0x74, 0x44, 0x65, 0x76, 0x65, 0x6c, 0x6f, 0x70, 0x6d, 0x65, 0x6e,
	0x74, 0x2f, 0x70, 0x72, 0x6f, 0x62, 0x65, 0x2d, 0x62, 0x61, 0x63, 0x6b, 0x65, 0x6e, 0x64, 0x2f,
	0x6c, 0x61, 0x72, 0x6b, 0x70, 0x72, 0x6f, 0x78, 0x79, 0x2f, 0x72, 0x70, 0x63, 0x2f, 0x70, 0x62,
	0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_larkproxy_im_chat_members_proto_rawDescOnce sync.Once
	file_larkproxy_im_chat_members_proto_rawDescData = file_larkproxy_im_chat_members_proto_rawDesc
)

func file_larkproxy_im_chat_members_proto_rawDescGZIP() []byte {
	file_larkproxy_im_chat_members_proto_rawDescOnce.Do(func() {
		file_larkproxy_im_chat_members_proto_rawDescData = protoimpl.X.CompressGZIP(file_larkproxy_im_chat_members_proto_rawDescData)
	})
	return file_larkproxy_im_chat_members_proto_rawDescData
}

var file_larkproxy_im_chat_members_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_larkproxy_im_chat_members_proto_goTypes = []any{
	(*CreateChatMembersRespData)(nil), // 0: larkproxy.CreateChatMembersRespData
	(*GetChatMembersRespData)(nil),    // 1: larkproxy.GetChatMembersRespData
}
var file_larkproxy_im_chat_members_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_larkproxy_im_chat_members_proto_init() }
func file_larkproxy_im_chat_members_proto_init() {
	if File_larkproxy_im_chat_members_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_larkproxy_im_chat_members_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_larkproxy_im_chat_members_proto_goTypes,
		DependencyIndexes: file_larkproxy_im_chat_members_proto_depIdxs,
		MessageInfos:      file_larkproxy_im_chat_members_proto_msgTypes,
	}.Build()
	File_larkproxy_im_chat_members_proto = out.File
	file_larkproxy_im_chat_members_proto_rawDesc = nil
	file_larkproxy_im_chat_members_proto_goTypes = nil
	file_larkproxy_im_chat_members_proto_depIdxs = nil
}
