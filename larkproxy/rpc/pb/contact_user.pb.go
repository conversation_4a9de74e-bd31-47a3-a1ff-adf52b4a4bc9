// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.1
// 	protoc        v5.29.2
// source: larkproxy/contact_user.proto

package pb

import (
	reflect "reflect"
	sync "sync"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type UserContactInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserId        string                 `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"` // 用户ID
	Mobile        string                 `protobuf:"bytes,2,opt,name=mobile,proto3" json:"mobile,omitempty"`               // 手机号
	Email         string                 `protobuf:"bytes,3,opt,name=email,proto3" json:"email,omitempty"`                 // 邮箱
	Status        *UserStatus            `protobuf:"bytes,4,opt,name=status,proto3" json:"status,omitempty"`               // 用户状态
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UserContactInfo) Reset() {
	*x = UserContactInfo{}
	mi := &file_larkproxy_contact_user_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UserContactInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserContactInfo) ProtoMessage() {}

func (x *UserContactInfo) ProtoReflect() protoreflect.Message {
	mi := &file_larkproxy_contact_user_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserContactInfo.ProtoReflect.Descriptor instead.
func (*UserContactInfo) Descriptor() ([]byte, []int) {
	return file_larkproxy_contact_user_proto_rawDescGZIP(), []int{0}
}

func (x *UserContactInfo) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *UserContactInfo) GetMobile() string {
	if x != nil {
		return x.Mobile
	}
	return ""
}

func (x *UserContactInfo) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *UserContactInfo) GetStatus() *UserStatus {
	if x != nil {
		return x.Status
	}
	return nil
}

type UserStatus struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	IsFrozen      bool                   `protobuf:"varint,1,opt,name=is_frozen,json=isFrozen,proto3" json:"is_frozen,omitempty"`          // 是否暂停
	IsResigned    bool                   `protobuf:"varint,2,opt,name=is_resigned,json=isResigned,proto3" json:"is_resigned,omitempty"`    // 是否离职
	IsActivated   bool                   `protobuf:"varint,3,opt,name=is_activated,json=isActivated,proto3" json:"is_activated,omitempty"` // 是否激活
	IsExited      bool                   `protobuf:"varint,4,opt,name=is_exited,json=isExited,proto3" json:"is_exited,omitempty"`          // 是否主动退出
	IsUnjoin      bool                   `protobuf:"varint,5,opt,name=is_unjoin,json=isUnjoin,proto3" json:"is_unjoin,omitempty"`          // 是否未加入
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UserStatus) Reset() {
	*x = UserStatus{}
	mi := &file_larkproxy_contact_user_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UserStatus) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserStatus) ProtoMessage() {}

func (x *UserStatus) ProtoReflect() protoreflect.Message {
	mi := &file_larkproxy_contact_user_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserStatus.ProtoReflect.Descriptor instead.
func (*UserStatus) Descriptor() ([]byte, []int) {
	return file_larkproxy_contact_user_proto_rawDescGZIP(), []int{1}
}

func (x *UserStatus) GetIsFrozen() bool {
	if x != nil {
		return x.IsFrozen
	}
	return false
}

func (x *UserStatus) GetIsResigned() bool {
	if x != nil {
		return x.IsResigned
	}
	return false
}

func (x *UserStatus) GetIsActivated() bool {
	if x != nil {
		return x.IsActivated
	}
	return false
}

func (x *UserStatus) GetIsExited() bool {
	if x != nil {
		return x.IsExited
	}
	return false
}

func (x *UserStatus) GetIsUnjoin() bool {
	if x != nil {
		return x.IsUnjoin
	}
	return false
}

var File_larkproxy_contact_user_proto protoreflect.FileDescriptor

var file_larkproxy_contact_user_proto_rawDesc = []byte{
	0x0a, 0x1c, 0x6c, 0x61, 0x72, 0x6b, 0x70, 0x72, 0x6f, 0x78, 0x79, 0x2f, 0x63, 0x6f, 0x6e, 0x74,
	0x61, 0x63, 0x74, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x09,
	0x6c, 0x61, 0x72, 0x6b, 0x70, 0x72, 0x6f, 0x78, 0x79, 0x22, 0x87, 0x01, 0x0a, 0x0f, 0x55, 0x73,
	0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x17, 0x0a,
	0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x6d, 0x6f, 0x62, 0x69, 0x6c, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x12, 0x14,
	0x0a, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x65,
	0x6d, 0x61, 0x69, 0x6c, 0x12, 0x2d, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x6c, 0x61, 0x72, 0x6b, 0x70, 0x72, 0x6f, 0x78, 0x79,
	0x2e, 0x55, 0x73, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x22, 0xa7, 0x01, 0x0a, 0x0a, 0x55, 0x73, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x12, 0x1b, 0x0a, 0x09, 0x69, 0x73, 0x5f, 0x66, 0x72, 0x6f, 0x7a, 0x65, 0x6e, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x69, 0x73, 0x46, 0x72, 0x6f, 0x7a, 0x65, 0x6e, 0x12,
	0x1f, 0x0a, 0x0b, 0x69, 0x73, 0x5f, 0x72, 0x65, 0x73, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x0a, 0x69, 0x73, 0x52, 0x65, 0x73, 0x69, 0x67, 0x6e, 0x65, 0x64,
	0x12, 0x21, 0x0a, 0x0c, 0x69, 0x73, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x76, 0x61, 0x74, 0x65, 0x64,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x69, 0x73, 0x41, 0x63, 0x74, 0x69, 0x76, 0x61,
	0x74, 0x65, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x69, 0x73, 0x5f, 0x65, 0x78, 0x69, 0x74, 0x65, 0x64,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x69, 0x73, 0x45, 0x78, 0x69, 0x74, 0x65, 0x64,
	0x12, 0x1b, 0x0a, 0x09, 0x69, 0x73, 0x5f, 0x75, 0x6e, 0x6a, 0x6f, 0x69, 0x6e, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x08, 0x69, 0x73, 0x55, 0x6e, 0x6a, 0x6f, 0x69, 0x6e, 0x42, 0x43, 0x5a,
	0x41, 0x67, 0x69, 0x74, 0x6c, 0x61, 0x62, 0x2e, 0x74, 0x74, 0x79, 0x75, 0x79, 0x69, 0x6e, 0x2e,
	0x63, 0x6f, 0x6d, 0x2f, 0x54, 0x65, 0x73, 0x74, 0x44, 0x65, 0x76, 0x65, 0x6c, 0x6f, 0x70, 0x6d,
	0x65, 0x6e, 0x74, 0x2f, 0x70, 0x72, 0x6f, 0x62, 0x65, 0x2d, 0x62, 0x61, 0x63, 0x6b, 0x65, 0x6e,
	0x64, 0x2f, 0x6c, 0x61, 0x72, 0x6b, 0x70, 0x72, 0x6f, 0x78, 0x79, 0x2f, 0x72, 0x70, 0x63, 0x2f,
	0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_larkproxy_contact_user_proto_rawDescOnce sync.Once
	file_larkproxy_contact_user_proto_rawDescData = file_larkproxy_contact_user_proto_rawDesc
)

func file_larkproxy_contact_user_proto_rawDescGZIP() []byte {
	file_larkproxy_contact_user_proto_rawDescOnce.Do(func() {
		file_larkproxy_contact_user_proto_rawDescData = protoimpl.X.CompressGZIP(file_larkproxy_contact_user_proto_rawDescData)
	})
	return file_larkproxy_contact_user_proto_rawDescData
}

var file_larkproxy_contact_user_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_larkproxy_contact_user_proto_goTypes = []any{
	(*UserContactInfo)(nil), // 0: larkproxy.UserContactInfo
	(*UserStatus)(nil),      // 1: larkproxy.UserStatus
}
var file_larkproxy_contact_user_proto_depIdxs = []int32{
	1, // 0: larkproxy.UserContactInfo.status:type_name -> larkproxy.UserStatus
	1, // [1:1] is the sub-list for method output_type
	1, // [1:1] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_larkproxy_contact_user_proto_init() }
func file_larkproxy_contact_user_proto_init() {
	if File_larkproxy_contact_user_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_larkproxy_contact_user_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_larkproxy_contact_user_proto_goTypes,
		DependencyIndexes: file_larkproxy_contact_user_proto_depIdxs,
		MessageInfos:      file_larkproxy_contact_user_proto_msgTypes,
	}.Build()
	File_larkproxy_contact_user_proto = out.File
	file_larkproxy_contact_user_proto_rawDesc = nil
	file_larkproxy_contact_user_proto_goTypes = nil
	file_larkproxy_contact_user_proto_depIdxs = nil
}
