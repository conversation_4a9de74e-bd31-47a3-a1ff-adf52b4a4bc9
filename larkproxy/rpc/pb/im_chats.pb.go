// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.1
// 	protoc        v5.29.2
// source: larkproxy/im_chats.proto

package pb

import (
	reflect "reflect"
	sync "sync"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type CreateChatRespData struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ChatId        string                 `protobuf:"bytes,1,opt,name=chat_id,json=chatId,proto3" json:"chat_id,omitempty"` // 群组ID
	Avatar        string                 `protobuf:"bytes,2,opt,name=avatar,proto3" json:"avatar,omitempty"`               // 群头像URL
	Name          string                 `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`                   // 群名称
	Description   string                 `protobuf:"bytes,4,opt,name=description,proto3" json:"description,omitempty"`     // 群描述
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateChatRespData) Reset() {
	*x = CreateChatRespData{}
	mi := &file_larkproxy_im_chats_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateChatRespData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateChatRespData) ProtoMessage() {}

func (x *CreateChatRespData) ProtoReflect() protoreflect.Message {
	mi := &file_larkproxy_im_chats_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateChatRespData.ProtoReflect.Descriptor instead.
func (*CreateChatRespData) Descriptor() ([]byte, []int) {
	return file_larkproxy_im_chats_proto_rawDescGZIP(), []int{0}
}

func (x *CreateChatRespData) GetChatId() string {
	if x != nil {
		return x.ChatId
	}
	return ""
}

func (x *CreateChatRespData) GetAvatar() string {
	if x != nil {
		return x.Avatar
	}
	return ""
}

func (x *CreateChatRespData) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CreateChatRespData) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

type GetChatRespData struct {
	state       protoimpl.MessageState `protogen:"open.v1"`
	ChatId      string                 `protobuf:"bytes,1,opt,name=chat_id,json=chatId,proto3" json:"chat_id,omitempty"` // 群组ID
	Avatar      string                 `protobuf:"bytes,2,opt,name=avatar,proto3" json:"avatar,omitempty"`               // 群头像URL
	Name        string                 `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`                   // 群名称
	Description string                 `protobuf:"bytes,4,opt,name=description,proto3" json:"description,omitempty"`     // 群描述
	External    bool                   `protobuf:"varint,5,opt,name=external,proto3" json:"external,omitempty"`          // 是否是外部群

	Status        string `protobuf:"bytes,6,opt,name=status,proto3" json:"status,omitempty" copier:"ChatStatus"` // 群状态
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetChatRespData) Reset() {
	*x = GetChatRespData{}
	mi := &file_larkproxy_im_chats_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetChatRespData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetChatRespData) ProtoMessage() {}

func (x *GetChatRespData) ProtoReflect() protoreflect.Message {
	mi := &file_larkproxy_im_chats_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetChatRespData.ProtoReflect.Descriptor instead.
func (*GetChatRespData) Descriptor() ([]byte, []int) {
	return file_larkproxy_im_chats_proto_rawDescGZIP(), []int{1}
}

func (x *GetChatRespData) GetChatId() string {
	if x != nil {
		return x.ChatId
	}
	return ""
}

func (x *GetChatRespData) GetAvatar() string {
	if x != nil {
		return x.Avatar
	}
	return ""
}

func (x *GetChatRespData) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *GetChatRespData) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *GetChatRespData) GetExternal() bool {
	if x != nil {
		return x.External
	}
	return false
}

func (x *GetChatRespData) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

type ListChatRespData struct {
	state       protoimpl.MessageState `protogen:"open.v1"`
	ChatId      string                 `protobuf:"bytes,1,opt,name=chat_id,json=chatId,proto3" json:"chat_id,omitempty"` // 群组ID
	Avatar      string                 `protobuf:"bytes,2,opt,name=avatar,proto3" json:"avatar,omitempty"`               // 群头像URL
	Name        string                 `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`                   // 群名称
	Description string                 `protobuf:"bytes,4,opt,name=description,proto3" json:"description,omitempty"`     // 群描述
	External    bool                   `protobuf:"varint,5,opt,name=external,proto3" json:"external,omitempty"`          // 是否是外部群

	Status        string `protobuf:"bytes,6,opt,name=status,proto3" json:"status,omitempty" copier:"ChatStatus"` // 群状态
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListChatRespData) Reset() {
	*x = ListChatRespData{}
	mi := &file_larkproxy_im_chats_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListChatRespData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListChatRespData) ProtoMessage() {}

func (x *ListChatRespData) ProtoReflect() protoreflect.Message {
	mi := &file_larkproxy_im_chats_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListChatRespData.ProtoReflect.Descriptor instead.
func (*ListChatRespData) Descriptor() ([]byte, []int) {
	return file_larkproxy_im_chats_proto_rawDescGZIP(), []int{2}
}

func (x *ListChatRespData) GetChatId() string {
	if x != nil {
		return x.ChatId
	}
	return ""
}

func (x *ListChatRespData) GetAvatar() string {
	if x != nil {
		return x.Avatar
	}
	return ""
}

func (x *ListChatRespData) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ListChatRespData) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *ListChatRespData) GetExternal() bool {
	if x != nil {
		return x.External
	}
	return false
}

func (x *ListChatRespData) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

var File_larkproxy_im_chats_proto protoreflect.FileDescriptor

var file_larkproxy_im_chats_proto_rawDesc = []byte{
	0x0a, 0x18, 0x6c, 0x61, 0x72, 0x6b, 0x70, 0x72, 0x6f, 0x78, 0x79, 0x2f, 0x69, 0x6d, 0x5f, 0x63,
	0x68, 0x61, 0x74, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x09, 0x6c, 0x61, 0x72, 0x6b,
	0x70, 0x72, 0x6f, 0x78, 0x79, 0x22, 0x7b, 0x0a, 0x12, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x43,
	0x68, 0x61, 0x74, 0x52, 0x65, 0x73, 0x70, 0x44, 0x61, 0x74, 0x61, 0x12, 0x17, 0x0a, 0x07, 0x63,
	0x68, 0x61, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x63, 0x68,
	0x61, 0x74, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x12, 0x12, 0x0a, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x22, 0xac, 0x01, 0x0a, 0x0f, 0x47, 0x65, 0x74, 0x43, 0x68, 0x61, 0x74, 0x52, 0x65,
	0x73, 0x70, 0x44, 0x61, 0x74, 0x61, 0x12, 0x17, 0x0a, 0x07, 0x63, 0x68, 0x61, 0x74, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x63, 0x68, 0x61, 0x74, 0x49, 0x64, 0x12,
	0x16, 0x0a, 0x06, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64,
	0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1a, 0x0a,
	0x08, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x08, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x22, 0xad, 0x01, 0x0a, 0x10, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x68, 0x61, 0x74, 0x52, 0x65,
	0x73, 0x70, 0x44, 0x61, 0x74, 0x61, 0x12, 0x17, 0x0a, 0x07, 0x63, 0x68, 0x61, 0x74, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x63, 0x68, 0x61, 0x74, 0x49, 0x64, 0x12,
	0x16, 0x0a, 0x06, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64,
	0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1a, 0x0a,
	0x08, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x08, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x42, 0x43, 0x5a, 0x41, 0x67, 0x69, 0x74, 0x6c, 0x61, 0x62, 0x2e, 0x74, 0x74, 0x79, 0x75,
	0x79, 0x69, 0x6e, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x54, 0x65, 0x73, 0x74, 0x44, 0x65, 0x76, 0x65,
	0x6c, 0x6f, 0x70, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x70, 0x72, 0x6f, 0x62, 0x65, 0x2d, 0x62, 0x61,
	0x63, 0x6b, 0x65, 0x6e, 0x64, 0x2f, 0x6c, 0x61, 0x72, 0x6b, 0x70, 0x72, 0x6f, 0x78, 0x79, 0x2f,
	0x72, 0x70, 0x63, 0x2f, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_larkproxy_im_chats_proto_rawDescOnce sync.Once
	file_larkproxy_im_chats_proto_rawDescData = file_larkproxy_im_chats_proto_rawDesc
)

func file_larkproxy_im_chats_proto_rawDescGZIP() []byte {
	file_larkproxy_im_chats_proto_rawDescOnce.Do(func() {
		file_larkproxy_im_chats_proto_rawDescData = protoimpl.X.CompressGZIP(file_larkproxy_im_chats_proto_rawDescData)
	})
	return file_larkproxy_im_chats_proto_rawDescData
}

var file_larkproxy_im_chats_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_larkproxy_im_chats_proto_goTypes = []any{
	(*CreateChatRespData)(nil), // 0: larkproxy.CreateChatRespData
	(*GetChatRespData)(nil),    // 1: larkproxy.GetChatRespData
	(*ListChatRespData)(nil),   // 2: larkproxy.ListChatRespData
}
var file_larkproxy_im_chats_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_larkproxy_im_chats_proto_init() }
func file_larkproxy_im_chats_proto_init() {
	if File_larkproxy_im_chats_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_larkproxy_im_chats_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_larkproxy_im_chats_proto_goTypes,
		DependencyIndexes: file_larkproxy_im_chats_proto_depIdxs,
		MessageInfos:      file_larkproxy_im_chats_proto_msgTypes,
	}.Build()
	File_larkproxy_im_chats_proto = out.File
	file_larkproxy_im_chats_proto_rawDesc = nil
	file_larkproxy_im_chats_proto_goTypes = nil
	file_larkproxy_im_chats_proto_depIdxs = nil
}
