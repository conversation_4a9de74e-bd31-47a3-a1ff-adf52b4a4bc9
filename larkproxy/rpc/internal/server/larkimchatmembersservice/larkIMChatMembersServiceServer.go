// Code generated by goctl. DO NOT EDIT.
// goctl 1.7.5
// Source: larkproxy.proto

package server

import (
	"context"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"

	larkimchatmembersservicelogic "gitlab.ttyuyin.com/TestDevelopment/probe-backend/larkproxy/rpc/internal/logic/larkimchatmembersservice"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/larkproxy/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/larkproxy/rpc/pb"
)

type LarkIMChatMembersServiceServer struct {
	svcCtx *svc.ServiceContext
	pb.UnimplementedLarkIMChatMembersServiceServer
}

func NewLarkIMChatMembersServiceServer(svcCtx *svc.ServiceContext) *LarkIMChatMembersServiceServer {
	return &LarkIMChatMembersServiceServer{
		svcCtx: svcCtx,
	}
}

// CreateChatMembers 将用户或机器人拉入群聊
func (s *LarkIMChatMembersServiceServer) CreateChatMembers(ctx context.Context, in *pb.CreateChatMembersReq) (*pb.CreateChatMembersResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := larkimchatmembersservicelogic.NewCreateChatMembersLogic(ctx, s.svcCtx)

	return l.CreateChatMembers(in)
}

// DeleteChatMembers 将用户或机器人移出群聊
func (s *LarkIMChatMembersServiceServer) DeleteChatMembers(ctx context.Context, in *pb.DeleteChatMembersReq) (*pb.DeleteChatMembersResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := larkimchatmembersservicelogic.NewDeleteChatMembersLogic(ctx, s.svcCtx)

	return l.DeleteChatMembers(in)
}

// GetChatMembers 获取群成员列表
func (s *LarkIMChatMembersServiceServer) GetChatMembers(ctx context.Context, in *pb.GetChatMembersReq) (*pb.GetChatMembersResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := larkimchatmembersservicelogic.NewGetChatMembersLogic(ctx, s.svcCtx)

	return l.GetChatMembers(in)
}
