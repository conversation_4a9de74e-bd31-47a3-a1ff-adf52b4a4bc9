// Code generated by goctl. DO NOT EDIT.
// goctl 1.7.5
// Source: larkproxy.proto

package server

import (
	"context"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"

	larkimchatsservicelogic "gitlab.ttyuyin.com/TestDevelopment/probe-backend/larkproxy/rpc/internal/logic/larkimchatsservice"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/larkproxy/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/larkproxy/rpc/pb"
)

type LarkIMChatsServiceServer struct {
	svcCtx *svc.ServiceContext
	pb.UnimplementedLarkIMChatsServiceServer
}

func NewLarkIMChatsServiceServer(svcCtx *svc.ServiceContext) *LarkIMChatsServiceServer {
	return &LarkIMChatsServiceServer{
		svcCtx: svcCtx,
	}
}

// CreateChat 创建群
func (s *LarkIMChatsServiceServer) CreateChat(ctx context.Context, in *pb.CreateChatReq) (*pb.CreateChatResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := larkimchatsservicelogic.NewCreateChatLogic(ctx, s.svcCtx)

	return l.CreateChat(in)
}

// DeleteChat 解散群
func (s *LarkIMChatsServiceServer) DeleteChat(ctx context.Context, in *pb.DeleteChatReq) (*pb.DeleteChatResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := larkimchatsservicelogic.NewDeleteChatLogic(ctx, s.svcCtx)

	return l.DeleteChat(in)
}

// GetChat 获取群信息
func (s *LarkIMChatsServiceServer) GetChat(ctx context.Context, in *pb.GetChatReq) (*pb.GetChatResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := larkimchatsservicelogic.NewGetChatLogic(ctx, s.svcCtx)

	return l.GetChat(in)
}

// ListChat 获取用户或机器人所在的群列表
func (s *LarkIMChatsServiceServer) ListChat(ctx context.Context, in *pb.ListChatReq) (*pb.ListChatResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := larkimchatsservicelogic.NewListChatLogic(ctx, s.svcCtx)

	return l.ListChat(in)
}
