// Code generated by goctl. DO NOT EDIT.
// goctl 1.7.5
// Source: larkproxy.proto

package server

import (
	"context"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"

	larkimchatmanagersservicelogic "gitlab.ttyuyin.com/TestDevelopment/probe-backend/larkproxy/rpc/internal/logic/larkimchatmanagersservice"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/larkproxy/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/larkproxy/rpc/pb"
)

type LarkIMChatManagersServiceServer struct {
	svcCtx *svc.ServiceContext
	pb.UnimplementedLarkIMChatManagersServiceServer
}

func NewLarkIMChatManagersServiceServer(svcCtx *svc.ServiceContext) *LarkIMChatManagersServiceServer {
	return &LarkIMChatManagersServiceServer{
		svcCtx: svcCtx,
	}
}

// CreateChatMembers 指定群管理员
func (s *LarkIMChatManagersServiceServer) CreateChatManagers(ctx context.Context, in *pb.CreateChatManagersReq) (*pb.CreateChatManagersResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := larkimchatmanagersservicelogic.NewCreateChatManagersLogic(ctx, s.svcCtx)

	return l.CreateChatManagers(in)
}
