// Code generated by goctl. DO NOT EDIT.
// goctl 1.7.5
// Source: larkproxy.proto

package server

import (
	"context"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"

	larkcontactuserservicelogic "gitlab.ttyuyin.com/TestDevelopment/probe-backend/larkproxy/rpc/internal/logic/larkcontactuserservice"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/larkproxy/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/larkproxy/rpc/pb"
)

type LarkContactUserServiceServer struct {
	svcCtx *svc.ServiceContext
	pb.UnimplementedLarkContactUserServiceServer
}

func NewLarkContactUserServiceServer(svcCtx *svc.ServiceContext) *LarkContactUserServiceServer {
	return &LarkContactUserServiceServer{
		svcCtx: svcCtx,
	}
}

func (s *LarkContactUserServiceServer) GetBatchUserID(ctx context.Context, in *pb.GetBatchUserIDReq) (*pb.GetBatchUserIDResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := larkcontactuserservicelogic.NewGetBatchUserIDLogic(ctx, s.svcCtx)

	return l.GetBatchUserID(in)
}
