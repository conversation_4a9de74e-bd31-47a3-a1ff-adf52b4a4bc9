package larkcontactuserservicelogic

import (
	"context"

	larkcontact "github.com/larksuite/oapi-sdk-go/v3/service/contact/v3"
	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/larkproxy/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/larkproxy/rpc/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/larkproxy/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/larkproxy/rpc/pb"
)

type GetBatchUserIDLogic struct {
	*logic.BaseLogic

	ctx    context.Context
	svcCtx *svc.ServiceContext

	apiPath common.LarkAPIPath
}

func NewGetBatchUserIDLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetBatchUserIDLogic {
	return &GetBatchUserIDLogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),

		ctx:    ctx,
		svcCtx: svcCtx,

		apiPath: common.ConstLarkAPIPathOfListUsers,
	}
}

func (l *GetBatchUserIDLogic) GetBatchUserID(in *pb.GetBatchUserIDReq) (out *pb.GetBatchUserIDResp, err error) {
	req := larkcontact.NewBatchGetIdUserReqBuilder().
		Body(
			larkcontact.NewBatchGetIdUserReqBodyBuilder().
				Emails(in.GetEmails()).
				Build(),
		).
		Build()

	resp, err := l.LarkClient.Contact.User.BatchGetId(l.ctx, req)
	if err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CallExternalAPIFailure, err.Error()),
			"failed to batch get user id by calling lark api, api: %q, error: %+v",
			l.apiPath, err,
		)
	} else if resp.Code != 0 {
		return nil, errorx.Errorf(
			errorx.CallExternalAPIFailure,
			"failed to batch get user id by calling lark api, api: %q, code: %d, message: %s",
			l.apiPath, resp.Code, resp.Msg,
		)
	} else if resp.Data == nil {
		return &pb.GetBatchUserIDResp{Items: make([]*pb.UserContactInfo, 0)}, nil
	}

	out = &pb.GetBatchUserIDResp{Items: make([]*pb.UserContactInfo, 0, len(resp.Data.UserList))}
	for _, user := range resp.Data.UserList {
		item := &pb.UserContactInfo{}
		if err = utils.Copy(item, user); err != nil {
			return nil, errors.Wrapf(
				errorx.Err(errorx.CopyToStructFailure, err.Error()),
				"failed to copy data to response, data: %s, error: %+v",
				jsonx.MarshalIgnoreError(user), err,
			)
		}

		out.Items = append(out.Items, item)
	}

	return out, nil
}
