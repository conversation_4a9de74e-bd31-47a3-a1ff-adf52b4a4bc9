package larkimchatsservicelogic

import (
	"context"
	"strings"

	larkim "github.com/larksuite/oapi-sdk-go/v3/service/im/v1"
	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/larkproxy/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/larkproxy/rpc/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/larkproxy/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/larkproxy/rpc/pb"
)

type CreateChatLogic struct {
	*logic.BaseLogic

	ctx    context.Context
	svcCtx *svc.ServiceContext

	apiPath common.LarkAPIPath
}

func NewCreateChatLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CreateChatLogic {
	return &CreateChatLogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),

		ctx:    ctx,
		svcCtx: svcCtx,

		apiPath: common.ConstLarkAPIPathOfCreateChat,
	}
}

// CreateChat 创建群
func (l *CreateChatLogic) CreateChat(in *pb.CreateChatReq) (out *pb.CreateChatResp, err error) {
	var (
		uuid        = in.GetUuid()
		name        = in.GetName()
		description = in.GetDescription()
		userIdList  = in.GetUserIdList()
	)

	builder := larkim.NewCreateChatReqBuilder()
	if uuid != "" {
		builder = builder.Uuid(uuid)
	}

	if !strings.HasPrefix(in.GetName(), defaultChatNamePrefix) {
		name = defaultChatNamePrefix + name
	}

	req := builder.
		Body(
			larkim.NewCreateChatReqBodyBuilder().
				Name(name).
				Description(description).
				UserIdList(userIdList).
				Build(),
		).
		Build()

	resp, err := l.LarkClient.Im.Chat.Create(l.ctx, req)
	if err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CallExternalAPIFailure, err.Error()),
			"failed to create chat by calling lark api, api: %q, error: %+v",
			l.apiPath, err,
		)
	} else if resp.Code != 0 {
		return nil, errorx.Errorf(
			errorx.CallExternalAPIFailure,
			"failed to create chat by calling lark api, api: %q, code: %d, message: %s",
			l.apiPath, resp.Code, resp.Msg,
		)
	} else if resp.Data == nil {
		return nil, errorx.Errorf(
			errorx.CallExternalAPIFailure,
			"failed to create chat by calling lark api, api: %q, code: %d, message: %s, data: %s",
			l.apiPath, resp.Code, resp.Msg, jsonx.MarshalIgnoreError(resp.Data),
		)
	}

	out = &pb.CreateChatResp{Data: &pb.CreateChatRespData{}}
	if err = utils.Copy(out.Data, resp.Data); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy data to response, data: %s, error: %+v",
			jsonx.MarshalIgnoreError(resp.Data), err,
		)
	}

	return out, nil
}
