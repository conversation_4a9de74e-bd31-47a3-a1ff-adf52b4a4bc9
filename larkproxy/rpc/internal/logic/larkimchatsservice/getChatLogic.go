package larkimchatsservicelogic

import (
	"context"

	larkim "github.com/larksuite/oapi-sdk-go/v3/service/im/v1"
	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/larkproxy/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/larkproxy/rpc/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/larkproxy/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/larkproxy/rpc/pb"
)

type GetChatLogic struct {
	*logic.BaseLogic

	ctx    context.Context
	svcCtx *svc.ServiceContext

	apiPath common.LarkAPIPath
}

func NewGetChatLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetChatLogic {
	return &GetChatLogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),

		ctx:    ctx,
		svcCtx: svcCtx,

		apiPath: common.ConstLarkAPIPathOfGetChat,
	}
}

// GetChat 获取群信息
func (l *GetChatLogic) GetChat(in *pb.GetChatReq) (out *pb.GetChatResp, err error) {
	req := larkim.NewGetChatReqBuilder().ChatId(in.GetChatId()).Build()
	resp, err := l.LarkClient.Im.Chat.Get(l.ctx, req)
	if err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CallExternalAPIFailure, err.Error()),
			"failed to get chat by calling lark api, api: %q, error: %+v",
			l.apiPath, err,
		)
	} else if !resp.Success() {
		return nil, errorx.Errorf(
			errorx.CallExternalAPIFailure,
			"failed to get chat by calling lark api, api: %q, code: %d, message: %s",
			l.apiPath, resp.Code, resp.Msg,
		)
	} else if resp.Data == nil {
		return nil, errorx.Errorf(
			errorx.CallExternalAPIFailure,
			"failed to get chat by calling lark api, api: %q, code: %d, message: %s, data: %s",
			l.apiPath, resp.Code, resp.Msg, jsonx.MarshalIgnoreError(resp.Data),
		)
	}

	out = &pb.GetChatResp{Data: &pb.GetChatRespData{ChatId: in.GetChatId()}}
	if err = utils.Copy(out.Data, resp.Data); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy data to response, data: %s, error: %+v",
			jsonx.MarshalIgnoreError(resp.Data), err,
		)
	}

	return out, nil
}
