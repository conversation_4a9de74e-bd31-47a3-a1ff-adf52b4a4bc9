package larkimchatsservicelogic

import (
	"context"

	"github.com/zeromicro/go-zero/core/logx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/larkproxy/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/larkproxy/rpc/pb"
)

type DeleteChatLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewDeleteChatLogic(ctx context.Context, svcCtx *svc.ServiceContext) *DeleteChatLogic {
	return &DeleteChatLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// DeleteChat 解散群
func (l *DeleteChatLogic) DeleteChat(in *pb.DeleteChatReq) (out *pb.DeleteChatResp, err error) {
	// todo: add your logic here and delete this line

	return &pb.DeleteChatResp{}, nil
}
