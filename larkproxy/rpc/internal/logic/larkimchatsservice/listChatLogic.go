package larkimchatsservicelogic

import (
	"context"

	larkim "github.com/larksuite/oapi-sdk-go/v3/service/im/v1"
	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/mr"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/larkproxy/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/larkproxy/rpc/internal/logic"
	users "gitlab.ttyuyin.com/TestDevelopment/probe-backend/larkproxy/rpc/internal/logic/larkcontactuserservice"
	members "gitlab.ttyuyin.com/TestDevelopment/probe-backend/larkproxy/rpc/internal/logic/larkimchatmembersservice"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/larkproxy/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/larkproxy/rpc/pb"
)

type ListChatLogic struct {
	*logic.BaseLogic

	ctx    context.Context
	svcCtx *svc.ServiceContext

	getBatchUserIDLogic *users.GetBatchUserIDLogic
	getChatMembersLogic *members.GetChatMembersLogic

	apiPath    common.LarkAPIPath
	larkUserID string
}

func NewListChatLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ListChatLogic {
	return &ListChatLogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),

		ctx:    ctx,
		svcCtx: svcCtx,

		getBatchUserIDLogic: users.NewGetBatchUserIDLogic(ctx, svcCtx),
		getChatMembersLogic: members.NewGetChatMembersLogic(ctx, svcCtx),

		apiPath: common.ConstLarkAPIPathOfListChats,
	}
}

// ListChat 获取用户或机器人所在的群列表
func (l *ListChatLogic) ListChat(in *pb.ListChatReq) (out *pb.ListChatResp, err error) {
	var (
		builder   = larkim.NewListChatReqBuilder()
		pageToken = ""
	)
	out = &pb.ListChatResp{Items: make([]*pb.ListChatRespData, 0, common.ConstIMChatsSize)}

	if in.GetWithCurrentUser() {
		l.getUserID()
	}

	// `*larkim.ListChatIterator`存在死循环缺陷（`nextPageToken`的判断存在问题），所以这里不能使用这个迭代器
	for {
		listChatReq := builder.PageToken(pageToken).Build()
		listChatResp, err := l.LarkClient.Im.Chat.List(l.ctx, listChatReq)
		if err != nil {
			return nil, errors.Wrapf(
				errorx.Err(errorx.CallExternalAPIFailure, err.Error()),
				"failed to list chats by calling lark api, api: %q, error: %+v",
				l.apiPath, err,
			)
		} else if listChatResp.Code != 0 {
			return nil, errorx.Errorf(
				errorx.CallExternalAPIFailure,
				"failed to list chats by calling lark api, api: %q, code: %d, message: %s",
				l.apiPath, listChatResp.Code, listChatResp.Msg,
			)
		} else if listChatResp.Data == nil || len(listChatResp.Data.Items) == 0 {
			break
		}

		_ = mr.MapReduceVoid[*larkim.ListChat, *larkim.ListChat](
			func(source chan<- *larkim.ListChat) {
				for _, item := range listChatResp.Data.Items {
					if item.ChatId == nil || *item.ChatId == "" {
						continue
					}

					source <- item
				}
			}, func(item *larkim.ListChat, writer mr.Writer[*larkim.ListChat], cancel func(error)) {
				if item == nil {
					return
				}

				if in.GetWithCurrentUser() && !l.isInChat(item) {
					return
				}

				writer.Write(item)
			}, func(pipe <-chan *larkim.ListChat, cancel func(error)) {
				for item := range pipe {
					v := convertToChatData(item)
					if v == nil {
						continue
					}

					out.Items = append(out.Items, v)
				}
			},
		)

		if listChatResp.Data.HasMore != nil && *listChatResp.Data.HasMore &&
			listChatResp.Data.PageToken != nil && *listChatResp.Data.PageToken != "" {
			pageToken = *listChatResp.Data.PageToken
		} else {
			break
		}
	}

	return out, nil
}

func (l *ListChatLogic) getUserID() {
	if l.CurrentUser == nil || l.CurrentUser.Email == "" {
		return
	}

	resp, err := l.getBatchUserIDLogic.GetBatchUserID(
		&pb.GetBatchUserIDReq{
			Emails: []string{l.CurrentUser.Email},
		},
	)
	if err != nil {
		l.Error(err)
		return
	} else if len(resp.GetItems()) == 0 {
		l.Warnf("not found the user_id by email: %s", l.CurrentUser.Email)
		return
	}

	l.larkUserID = resp.GetItems()[0].GetUserId()
}

func (l *ListChatLogic) isInChat(chat *larkim.ListChat) bool {
	if l.larkUserID == "" || chat == nil || *chat.ChatId == "" {
		return false
	}

	resp, err := l.getChatMembersLogic.GetChatMembers(
		&pb.GetChatMembersReq{
			ChatId: *chat.ChatId,
		},
	)
	if err != nil {
		l.Error(err)
		return false
	}

	for _, item := range resp.GetItems() {
		if item.GetMemberId() == l.larkUserID {
			return true
		}
	}

	return false
}

func convertToChatData(in *larkim.ListChat) *pb.ListChatRespData {
	if in == nil || in.ChatId == nil || *in.ChatId == "" || in.Name == nil || *in.Name == "" {
		return nil
	}

	out := &pb.ListChatRespData{
		ChatId: *in.ChatId,
		Name:   *in.Name,
	}
	if in.Avatar != nil {
		out.Avatar = *in.Avatar
	}
	if in.Description != nil {
		out.Description = *in.Description
	}
	if in.External != nil {
		out.External = *in.External
	}
	if in.ChatStatus != nil {
		out.Status = *in.ChatStatus
	}

	return out
}
