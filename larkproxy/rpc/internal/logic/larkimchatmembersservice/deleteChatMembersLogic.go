package larkimchatmembersservicelogic

import (
	"context"

	"github.com/zeromicro/go-zero/core/logx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/larkproxy/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/larkproxy/rpc/pb"
)

type DeleteChatMembersLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewDeleteChatMembersLogic(ctx context.Context, svcCtx *svc.ServiceContext) *DeleteChatMembersLogic {
	return &DeleteChatMembersLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// DeleteChatMembers 将用户或机器人移出群聊
func (l *DeleteChatMembersLogic) DeleteChatMembers(in *pb.DeleteChatMembersReq) (out *pb.DeleteChatMembersResp, err error) {
	// todo: add your logic here and delete this line

	return &pb.DeleteChatMembersResp{}, nil
}
