package larkimchatmembersservicelogic

import (
	"context"

	larkim "github.com/larksuite/oapi-sdk-go/v3/service/im/v1"
	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/larkproxy/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/larkproxy/rpc/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/larkproxy/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/larkproxy/rpc/pb"
)

type GetChatMembersLogic struct {
	*logic.BaseLogic

	ctx    context.Context
	svcCtx *svc.ServiceContext

	apiPath common.LarkAPIPath
}

func NewGetChatMembersLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetChatMembersLogic {
	return &GetChatMembersLogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),

		ctx:    ctx,
		svcCtx: svcCtx,

		apiPath: common.ConstLarkAPIPathOfListMembers,
	}
}

// GetChatMembers 获取群成员列表
func (l *GetChatMembersLogic) GetChatMembers(in *pb.GetChatMembersReq) (out *pb.GetChatMembersResp, err error) {
	var (
		builder   = larkim.NewGetChatMembersReqBuilder().ChatId(in.GetChatId())
		pageToken = ""
	)
	out = &pb.GetChatMembersResp{Items: make([]*pb.GetChatMembersRespData, 0, common.ConstChatMembersSize)}

	// `*larkim.GetChatMembersIterator`存在死循环缺陷（`nextPageToken`的判断存在问题），所以这里不能使用这个迭代器
	for {
		getChatMembersReq := builder.PageToken(pageToken).Build()
		getChatMembersResp, err := l.LarkClient.Im.ChatMembers.Get(l.ctx, getChatMembersReq)
		if err != nil {
			return nil, errors.Wrapf(
				errorx.Err(errorx.CallExternalAPIFailure, err.Error()),
				"failed to get chat members by calling lark api, api: %q, error: %+v",
				l.apiPath, err,
			)
		} else if getChatMembersResp.Code != 0 {
			return nil, errorx.Errorf(
				errorx.CallExternalAPIFailure,
				"failed to get chat members by calling lark api, api: %q, code: %d, message: %s",
				l.apiPath, getChatMembersResp.Code, getChatMembersResp.Msg,
			)
		} else if getChatMembersResp.Data == nil || len(getChatMembersResp.Data.Items) == 0 {
			break
		}

		for _, item := range getChatMembersResp.Data.Items {
			if item == nil || item.MemberIdType == nil || item.MemberId == nil || item.Name == nil {
				continue
			}

			out.Items = append(
				out.Items, &pb.GetChatMembersRespData{
					MemberIdType: *item.MemberIdType,
					MemberId:     *item.MemberId,
					Name:         *item.Name,
				},
			)
		}

		if getChatMembersResp.Data.HasMore != nil && *getChatMembersResp.Data.HasMore &&
			getChatMembersResp.Data.PageToken != nil && *getChatMembersResp.Data.PageToken != "" {
			pageToken = *getChatMembersResp.Data.PageToken
		} else {
			break
		}
	}

	return out, nil
}
