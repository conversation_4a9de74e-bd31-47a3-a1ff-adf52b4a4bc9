package larkimchatmembersservicelogic

import (
	"context"

	larkim "github.com/larksuite/oapi-sdk-go/v3/service/im/v1"
	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/larkproxy/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/larkproxy/rpc/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/larkproxy/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/larkproxy/rpc/pb"
)

type CreateChatMembersLogic struct {
	*logic.BaseLogic

	ctx    context.Context
	svcCtx *svc.ServiceContext

	apiPath common.LarkAPIPath
}

func NewCreateChatMembersLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CreateChatMembersLogic {
	return &CreateChatMembersLogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),

		ctx:    ctx,
		svcCtx: svcCtx,

		apiPath: common.ConstLarkAPIPathOfCreateChatMembers,
	}
}

// CreateChatMembers 将用户或机器人拉入群聊
func (l *CreateChatMembersLogic) CreateChatMembers(in *pb.CreateChatMembersReq) (
	out *pb.CreateChatMembersResp, err error,
) {
	req := larkim.NewCreateChatMembersReqBuilder().
		ChatId(in.GetChatId()).
		Body(
			larkim.NewCreateChatMembersReqBodyBuilder().
				IdList(in.GetUserIdList()).
				Build(),
		).
		Build()

	resp, err := l.LarkClient.Im.ChatMembers.Create(l.ctx, req)
	if err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CallExternalAPIFailure, err.Error()),
			"failed to add member to chat by calling lark api, api: %q, error: %+v",
			l.apiPath, err,
		)
	} else if resp.Code != 0 {
		return nil, errorx.Errorf(
			errorx.CallExternalAPIFailure,
			"failed to add member to chat by calling lark api, api: %q, code: %d, message: %s",
			l.apiPath, resp.Code, resp.Msg,
		)
	} else if resp.Data == nil {
		return nil, errorx.Errorf(
			errorx.CallExternalAPIFailure,
			"failed to add member to chat by calling lark api, api: %q, code: %d, message: %s, data: %s",
			l.apiPath, resp.Code, resp.Msg, jsonx.MarshalIgnoreError(resp.Data),
		)
	}

	out = &pb.CreateChatMembersResp{Data: &pb.CreateChatMembersRespData{}}
	if err = utils.Copy(out.Data, resp.Data); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy data to response, data: %s, error: %+v",
			jsonx.MarshalIgnoreError(resp.Data), err,
		)
	}

	return out, nil
}
