package logic

import (
	"context"
	"time"

	lark "github.com/larksuite/oapi-sdk-go/v3"
	"github.com/zeromicro/go-zero/core/logx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/userinfo"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	commonlark "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/lark"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/larkproxy/rpc/internal/svc"
)

type BaseLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext

	LarkClient *lark.Client

	CurrentUser *userinfo.UserInfo

	Converters []utils.TypeConverter
}

func NewBaseLogic(ctx context.Context, svcCtx *svc.ServiceContext) *BaseLogic {
	return &BaseLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,

		LarkClient: lark.NewClient(
			svcCtx.Config.LarkCustomApp.AppID,
			svcCtx.Config.LarkCustomApp.AppSecret,
			lark.WithEnableTokenCache(true),
			lark.WithLogger(commonlark.NewLogger(ctx)),
			lark.WithReqTimeout(time.Duration(svcCtx.Config.Timeout)*time.Millisecond),
		),

		CurrentUser: userinfo.FromContext(ctx),

		Converters: []utils.TypeConverter{},
	}
}
