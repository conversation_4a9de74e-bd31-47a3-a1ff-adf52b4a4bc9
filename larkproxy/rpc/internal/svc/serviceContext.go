package svc

import (
	"github.com/bufbuild/protovalidate-go"
	"google.golang.org/protobuf/reflect/protoreflect"
	"google.golang.org/protobuf/reflect/protoregistry"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/larkproxy/rpc/internal/config"
)

type ServiceContext struct {
	Config config.Config

	Validator protovalidate.Validator
}

func NewServiceContext(c config.Config) *ServiceContext {
	return &ServiceContext{
		Config: c,

		Validator: newProtoValidator(),
	}
}

func newProtoValidator() protovalidate.Validator {
	mds := make([]protoreflect.MessageDescriptor, 0, protoregistry.GlobalTypes.NumMessages())
	protoregistry.GlobalTypes.RangeMessages(
		func(mt protoreflect.MessageType) bool {
			mds = append(mds, mt.Descriptor())
			return true
		},
	)

	v, _ := protovalidate.New(protovalidate.WithMessageDescriptors(mds...), protovalidate.WithDisableLazy())
	return v
}
