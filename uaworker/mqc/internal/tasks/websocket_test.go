package tasks

import (
	"context"
	"crypto/tls"
	"net/http"
	"testing"
	"time"

	ws "github.com/gorilla/websocket"
	"github.com/zeromicro/go-zero/core/lang"
	"github.com/zeromicro/go-zero/core/proc"
)

func TestWSScrcpy(t *testing.T) {
	ctx := context.Background()
	// url := "wss://************:8443/?action=proxy-adb&remote=tcp:8886&udid=*************:20034"
	// url := "ws://************:8000/?action=proxy-adb&remote=tcp:8886&udid=*************:20034"
	url := "ws://************/ws-scrcpy/?action=proxy-adb&remote=tcp:8886&udid=*************:20034"
	conn, _, err := (&ws.Dialer{
		Proxy: http.ProxyFromEnvironment,
		TLSClientConfig: &tls.Config{
			InsecureSkipVerify: true,
		},
		HandshakeTimeout: 45 * time.Second,
	}).DialContext(ctx, url, nil)
	if err != nil {
		t.Fatal(err)
	}

	stopCh := make(chan lang.PlaceholderType)
	proc.AddShutdownListener(
		func() {
			close(stopCh)
		},
	)
	go func() {
		ticker := time.NewTicker(30 * time.Second)
		defer ticker.Stop()

		for {
			select {
			case <-stopCh:
				return
			case <-ticker.C:
				if err := conn.WriteMessage(ws.BinaryMessage, []byte("ping")); err != nil {
					t.Error(err)
					return
				}
			}
		}
	}()

	for {
		select {
		case <-stopCh:
			return
		default:
			_, message, err := conn.ReadMessage()
			if err != nil {
				t.Error(err)
				return
			}
			t.Logf("recv: %s", string(message))
		}
	}
}
