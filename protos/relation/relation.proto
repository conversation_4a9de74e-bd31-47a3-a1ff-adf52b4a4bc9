syntax = "proto3";

package relation;
option go_package = "gitlab.ttyuyin.com/TestDevelopment/probe-backend/relation/rpc/pb";

import "validate/validate.proto";

import "protobuf/options.proto";


service RelationService {
  //Deprecated: use `BindTestCaseToServiceV2` instead.
  rpc BindTestCaseToService(BindTestCaseToServiceRequest) returns (BindTestCaseToServiceResponse);
  //Deprecated: use `BindCaseToService` instead.
  rpc BindTestCaseToServiceV2(BindTestCaseToServiceV2Request) returns (BindTestCaseToServiceV2Response);
  //Deprecated: use `GetCaseByService` instead.
  rpc GetServiceBindTestCase(GetServiceBindTestCaseRequest) returns (GetServiceBindTestCaseResponse);

  //BindCaseToService 绑定服务与测试用例
  rpc BindCaseToService(BindCaseToServiceReq) returns (BindCaseToServiceResp);
  //GetServiceByMethod 通过方法名称获取关联的服务
  rpc GetServiceByMethod(GetServiceByMethodReq) returns (GetServiceByMethodResp);
  //GetCaseByService 通过服务名称获取关联的测试用例
  rpc GetCaseByService(GetCaseByServiceReq) returns (GetCaseByServiceResp);
  //GetTeamByService 通过服务名称获取关联的团队
  rpc GetTeamByService(GetTeamByServiceReq) returns (GetTeamByServiceResp);
  //GetAllServiceTeams 获取全部服务与团队关系
  rpc GetAllServiceTeams(GetAllServiceTeamsReq) returns (GetAllServiceTeamsResp);
  //GetAllServiceMethods 获取全部服务与接口关系
  rpc GetAllServiceMethods(GetAllServiceMethodsReq) returns (GetAllServiceMethodsResp);
  //GetCaseOnlyByService 只通过服务名称获取关联的测试用例
  rpc GetCaseOnlyByService(GetCaseOnlyByServiceReq) returns (GetCaseOnlyByServiceResp);
}

message BindTestCaseToServiceRequest {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目id，不可为空
  string general_config_id = 2 [(validate.rules).string = {pattern: "(?:^general_config_id:.+?)"}]; // 通用配置ID
  string service_name = 3 [(validate.rules).string = {ignore_empty: true, min_len: 1, max_len: 64}];  // 服务名称
  string interface_path = 4 [(validate.rules).string = {ignore_empty: true, min_len: 1, max_len: 256}];  // 接口路径
  string cmd = 5 [(validate.rules).string = {ignore_empty: true, min_len: 1, max_len: 64}];  // 命令号，可不传递
  string case_id = 6 [(validate.rules).string = {ignore_empty: true, pattern: "(?:^case_id:.+?|^interface_case_id:.+?)"}]; // 测试用例id
}
message BindTestCaseToServiceResponse {

}

message BindTestCaseToServiceV2Request {
  string request_url = 1;
  bytes precision_info = 2;
  bytes request_body = 3;
  bytes response_body = 4;
}
message BindTestCaseToServiceV2Response {
}

message Relation {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目id，不可为空
  string general_config_id = 2 [(validate.rules).string = {pattern: "(?:^general_config_id:.+?)"}]; // 通用配置ID
  string service_name = 3 [(validate.rules).string = {ignore_empty: true, min_len: 1, max_len: 64}];  // 服务名称
  string interface_path = 4 [(validate.rules).string = {ignore_empty: true, min_len: 1, max_len: 256}];  // 接口路径
  string cmd = 5 [(validate.rules).string = {ignore_empty: true, min_len: 1, max_len: 64}];  // 命令号，可不传递
  string case_id = 6 [(validate.rules).string = {ignore_empty: true, pattern: "(?:^case_id:.+?|^interface_case_id:.+?)"}]; // 测试用例id
}

message GetServiceBindTestCaseRequest {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目id，不可为空
  repeated string service_names = 2;  // 服务名称，不可为空
  string general_config_id = 3 [(validate.rules).string = {pattern: "(?:^general_config_id:.+?)"}]; // 通用配置ID
}
message GetServiceBindTestCaseResponse {
  repeated Relation relations = 1;
}


message PrecisionInfo {
  string project_id =1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}];
  string general_config_id = 2 [(validate.rules).string = {pattern: "(?:^general_config_id:.+?)"}];
  string document_id = 3 [(validate.rules).string = {ignore_empty: true, pattern: "(?:^interface_document_id:.+?)"}];
  string case_id = 4 [(validate.rules).string = {pattern: "(?:^case_id:.+?|^interface_case_id:.+?)"}];
}

message BindCaseToServiceReq {
  string request_url = 1 [(validate.rules).string = {min_len: 2}];
  PrecisionInfo precision_info = 2 [(validate.rules).message = {required: true}];
  string request_body = 3;
  string response_body = 4;
}
message BindCaseToServiceResp {}

enum MethodType {
  MT_UNKNOWN = 0;

  GRPC = 1 [(options.enum_value_alias) = "gRPC"];
  HTTP = 2;
}

message ServiceMethodRelation {
  string service = 1; // 服务名称
  string namespace = 2; // 命名空间
  MethodType type = 3; // 接口类型
  string method = 4; // 接口名称

  string req_path = 11; // 请求路径
  uint32 cmd = 12; // 命令号
  bool deprecated = 13; // 是否已弃用
  string query_path = 14; // 请求路径（查询天相）

  string created_by = 96; // 创建者
  string updated_by = 97; // 更新者
  int64 created_at = 98;  // 创建时间
  int64 updated_at = 99;  // 更新时间
}

message GetServiceByMethodReq {
  string method = 1;
}
message GetServiceByMethodResp {
  repeated ServiceMethodRelation relations = 1;
}

message ServiceCaseRelation {
  string project_id = 1; // 项目ID
  string general_config_id = 2; // 通用配置ID
  string service = 3; // 服务名称
  string namespace = 4; // 命名空间
  string method = 5; // 接口名称
  string req_path = 6; // 请求路径
  uint32 cmd = 7; // 命令号
  string to_service = 8; // 经过的服务名称
  string document_id = 9; // 接口文档ID
  string case_id = 10; // 用例ID

  string created_by = 96; // 创建者
  string updated_by = 97; // 更新者
  int64 created_at = 98;  // 创建时间
  int64 updated_at = 99;  // 更新时间
}

message GetCaseByServiceReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  string general_config_id = 2 [(validate.rules).string = {pattern: "(?:^general_config_id:.+?)"}]; // 通用配置ID
  string service = 3 [(validate.rules).string = {min_len: 1}]; // 服务名称列表
}
message GetCaseByServiceResp {
  repeated ServiceCaseRelation relations = 1;
}

message GetCaseOnlyByServiceReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  string service = 2 [(validate.rules).string = {min_len: 1}]; // 服务名称列表
}
message GetCaseOnlyByServiceResp {
  repeated ServiceCaseRelation relations = 1;
}

message ServiceTeamRelation {
  string service = 1; // 服务名称
  string namespace = 2; // 命名空间
  string team_name = 3; // 团队名称
  string team_description = 4; // 团队描述

  string created_by = 96; // 创建者
  string updated_by = 97; // 更新者
  int64 created_at = 98;  // 创建时间
  int64 updated_at = 99;  // 更新时间
}

message GetTeamByServiceReq {
  string service = 1 [(validate.rules).string = {min_len: 1}]; // 服务名称
}
message GetTeamByServiceResp {
  repeated ServiceTeamRelation relations = 1;
}

message GetAllServiceTeamsReq {}
message GetAllServiceTeamsResp {
  repeated ServiceTeamRelation relations = 1;
}

message GetAllServiceMethodsReq {}
message GetAllServiceMethodsResp {
  repeated ServiceMethodRelation relations = 1;
}