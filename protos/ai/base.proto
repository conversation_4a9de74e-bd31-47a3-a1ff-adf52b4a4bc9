syntax = "proto3";

package ai;

import "google/protobuf/struct.proto";

option go_package = "gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb";

message Relation {
	string id = 1;
	string type = 2;
	repeated google.protobuf.ListValue children = 3;
	string reference_id = 4;
}

enum WorkPriorityType {
	WORK_PRIORITY_TYPE_ACCURACY_PRIORITY = 0; //准确优先
	WORK_PRIORITY_TYPE_SPEED_PRIORITY = 1;    //速度优先
}

enum WorkSpaceType {
	WORK_SPACE_TYPE_PERSONAL = 0; // 个人空间
	WORK_SPACE_TYPE_PUBLIC = 1;   // 公共空间
}

enum MindType {
	MIND_TYPE_CASE = 0; // case
	MIND_TYPE_REVIEW = 1; // review
}

enum SessionStatus {
	SESSION_STATUS_ACTIVE = 0;   // 使用中
	SESSION_STATUS_ARCHIVED = 1; // 归档
}

enum DocumentType {
	DOCUMENT_TYPE_TEXT = 0;   // 纯文本
	DOCUMENT_TYPE_FEISHU = 1; // 飞书
	DOCUMENT_TYPE_DOC = 2;    // doc
}

enum DocumentStatus {
	DOCUMENT_STATUS_PROCESSING = 0;   // 处理中
	DOCUMENT_STATUS_COMPLETED = 1;    // 完成
	DOCUMENT_STATUS_FAILED = 2;       // 失败
}