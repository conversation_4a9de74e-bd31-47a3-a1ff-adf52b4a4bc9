syntax = "proto3";

package ai;

import "ai/base.proto";
import "sqlbuilder/search.proto";
import "validate/validate.proto";

option go_package = "gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb";

// 1
message BlackBoxCaseAssistant {
  int32 id = 1; // 自增ID
  string project_id = 2; // 项目ID
  string assistant_id = 3; // 助手ID
  string assistant_name = 4; // 助手名称
  string assistant_description = 5; // 助手描述
  string account = 6; // 用户id【仅在个人空间存在此数据】
  WorkSpaceType work_space_type = 7; // 工作空间类型
  WorkPriorityType work_priority_type = 8; // 工作优先级类型
  int32 session_round = 9; // 对话轮数
  repeated BlackBoxCaseDocument black_box_case_document_list = 10; // 文档集合
  string created_by = 11; // 创建者的用户ID
  string updated_by = 12; // 最近一次更新者的用户ID
  string deleted_by = 13; // 删除者的用户ID
  int64 created_at = 14; // 创建时间
  int64 updated_at = 15; // 更新时间
  int64 deleted_at = 16; // 删除时间
}

message BlackBoxCaseTwBeta {
  int64 id = 1;
  string beta_case_id = 2;
  string beta_case_name = 3;
  string revision_id = 4;
  string case_content = 5;
  string document_name = 6;
  string document_url = 7;
  string document_chapter_title = 8;
  string document_content = 9;
  string reference_doc = 10;
  string knowledge_fix_sugg = 11;
  string case_ref_id = 12;
  int64 deleted = 13;
  string created_by = 14;
  string updated_by = 15;
  string deleted_by = 16;
  int64 created_at = 17;
  int64 updated_at = 18;
  int64 deleted_at = 19;
}

message BlackBoxCaseTwBetaMind {
  int64 id = 1;
  string product_module = 2;
  repeated BlackBoxCaseTwBetaMindCheck ac_check_list = 3;
  repeated BlackBoxCaseTwBetaMindCheck tc_check_list = 4;
  string status = 5; // 状态 succeed, failed, processing
  int64 created_at = 6;
}

message BlackBoxCaseTwBetaMindCheck {
  string use_case = 1;
  string check_point = 2;
}

message SearchBlackBoxCaseAssistantReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
}

message SearchBlackBoxCaseAssistantResp {
  repeated BlackBoxCaseAssistant items = 1;
}

message UpdateBlackBoxCaseAssistantReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  string assistant_id = 2 [(validate.rules).string = {pattern: "(?:^ai:assistant_id:.+?|^1$)"}]; // 助手ID
  string assistant_name = 3 [(validate.rules).string = {
    min_len: 1
    max_len: 64
  }]; // 助手名称
  string assistant_description = 4 [(validate.rules).string = {
    min_len: 0
    max_len: 255
  }]; // 助手描述
  WorkSpaceType work_space_type = 5; // 工作空间类型
  WorkPriorityType work_priority_type = 6; // 工作优先级类型
  int32 session_round = 7; // 对话轮数
}

message UpdateBlackBoxCaseAssistantResp {}

message DeleteBlackBoxCaseAssistantReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  string assistant_id = 2 [(validate.rules).string = {pattern: "(?:^ai:assistant_id:.+?|^1$)"}]; // 助手ID
}

message DeleteBlackBoxCaseAssistantResp {}

message GetBlackBoxCaseAssistantReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  string assistant_id = 2 [(validate.rules).string = {pattern: "(?:^ai:assistant_id:.+?|^1$)"}]; // 助手ID
}

message GetBlackBoxCaseAssistantResp {
  BlackBoxCaseAssistant item = 1;
}

message CreateBlackBoxCaseAssistantReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  //string assistant_id = 2 [(validate.rules).string = {pattern: "(?:^ai:assistant_id:.+?|^1$)"}];                            // 助手ID
  string assistant_name = 3 [(validate.rules).string = {
    min_len: 1
    max_len: 64
  }]; // 助手名称
  string assistant_description = 4 [(validate.rules).string = {
    min_len: 0
    max_len: 255
  }]; // 助手描述
  int32 work_space_type = 5; // 工作空间类型
  int32 work_priority_type = 6; // 工作优先级类型
  int32 session_round = 7; // 对话轮数
}

message CreateBlackBoxCaseAssistantResp {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  string assistant_id = 2 [(validate.rules).string = {pattern: "(?:^ai:assistant_id:.+?|^1$)"}]; // 助手ID
}

// 2
message SearchCaseReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  sqlbuilder.Condition condition = 3; // 查询条件
  sqlbuilder.Pagination pagination = 4; // 查询分页
  repeated sqlbuilder.SortField sort = 5 [(validate.rules).repeated.ignore_empty = true]; // 查询排序
}

message BatchDeleteCaseFailLogReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  // repeated DeleteCaseFailLog delete_case_fail_log_list = 2 [(validate.rules).repeated = {min_items: 1, items: {message: {required: true}}}];
}

message BlackBoxCaseDocument {
  int32 id = 1; // 自增ID
  string project_id = 2 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  string assistant_id = 3; // 助手ID
  string document_id = 4; // 文档ID
  string document_name = 5; // 文档名称
  string document_description = 6; // 文档描述
  string document_url = 7; // 文档地址
  string document_text = 8; // 文档文本
  DocumentType document_type = 9; // 文档类型
  string external_document_id = 10; // 外部文档ID
  DocumentStatus status = 11; // 文档状态
  string created_by = 12; // 创建者的用户ID
  string updated_by = 13; // 最近一次更新者的用户ID
  string deleted_by = 14; // 删除者的用户ID
  int64 created_at = 15; // 创建时间
  int64 updated_at = 16; // 更新时间
  int64 deleted_at = 17; // 删除时间
}

message GetBlackBoxCaseDocumentHeadersListReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  string assistant_id = 2 [(validate.rules).string = {pattern: "(?:^ai:assistant_id:.+?|^1$)"}]; // 助手ID
  string session_id = 3 [(validate.rules).string = {pattern: "(?:^ai:session_id:.+?|^1$)"}];
  string document_id = 4 [(validate.rules).string = {pattern: "(?:^ai:document_id:.+?|^1$)"}]; // 文档ID
}

message GetBlackBoxCaseDocumentHeadersListResp {
  repeated string items = 1; // 标题列表
}

message SearchBlackBoxCaseDocumentForAssistantReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  string assistant_id = 2 [(validate.rules).string = {pattern: "(?:^ai:assistant_id:.+?|^1$)"}]; // 助手ID
  sqlbuilder.Pagination pagination = 3; // 查询分页
}

message SearchBlackBoxCaseDocumentForAssistantResp {
  uint64 current_page = 1; // 当前页数
  uint64 page_size = 2; // 每页大小
  uint64 total_count = 3; // 总数
  uint64 total_page = 4; // 总页数
  repeated BlackBoxCaseDocument items = 5; // 文档列表
}

message SearchBlackBoxCaseDocumentForSessionReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  string assistant_id = 2 [(validate.rules).string = {pattern: "(?:^ai:assistant_id:.+?|^1$)"}]; // 助手ID
  string session_id = 3 [(validate.rules).string = {pattern: "(?:^ai:session_id:.+?|^1$)"}]; // 会话ID
  sqlbuilder.Pagination pagination = 4; // 查询分页
}

message SearchBlackBoxCaseDocumentForSessionResp {
  uint64 current_page = 1; // 当前页数
  uint64 page_size = 2; // 每页大小
  uint64 total_count = 3; // 总数
  uint64 total_page = 4; // 总页数
  repeated BlackBoxCaseDocument items = 5; // 文档列表
}

message UpdateBlackBoxCaseDocumentReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  string assistant_id = 2 [(validate.rules).string = {pattern: "(?:^ai:assistant_id:.+?|^1$)"}]; // 助手ID
  string document_id = 3 [(validate.rules).string = {pattern: "(?:^ai:document_id:.+?|^1$)"}]; // 文档ID
  string document_name = 4 [(validate.rules).string = {
    min_len: 1
    max_len: 64
  }]; // 文档名称
  string document_description = 5 [(validate.rules).string = {
    min_len: 0
    max_len: 255
  }]; // 文档描述
}

message UpdateBlackBoxCaseDocumentResp {}

message DeleteBlackBoxCaseDocumentForAssistantReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  string assistant_id = 2 [(validate.rules).string = {pattern: "(?:^ai:assistant_id:.+?|^1$)"}]; // 助手ID
  string document_id = 3 [(validate.rules).string = {pattern: "(?:^ai:document_id:.+?|^1$)"}]; // 文档ID
}

message DeleteBlackBoxCaseDocumentForAssistantResp {}

message BatchDeleteBlackBoxCaseDocumentForAssistantReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  string assistant_id = 2 [(validate.rules).string = {pattern: "(?:^ai:assistant_id:.+?|^1$)"}]; // 助手ID
  repeated string document_ids = 3 [(validate.rules).repeated = {
    min_items: 1
    unique: true
    items: {
      string: {pattern: "(?:^ai:document_id:.+?)"}
    }
  }]; // 文档ID集合
}

message BatchDeleteBlackBoxCaseDocumentForAssistantResp {}

message DeleteBlackBoxCaseDocumentForSessionReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  string assistant_id = 2 [(validate.rules).string = {pattern: "(?:^ai:assistant_id:.+?|^1$)"}]; // 助手ID
  string session_id = 3 [(validate.rules).string = {pattern: "(?:^ai:session_id:.+?|^1$)"}]; // 会话ID
  string document_id = 4 [(validate.rules).string = {pattern: "(?:^ai:document_id:.+?|^1$)"}]; // 文档ID
}

message DeleteBlackBoxCaseDocumentForSessionResp {}

message BatchDeleteBlackBoxCaseDocumentForSessionReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  string assistant_id = 2 [(validate.rules).string = {pattern: "(?:^ai:assistant_id:.+?|^1$)"}]; // 助手ID
  string session_id = 3 [(validate.rules).string = {pattern: "(?:^ai:session_id:.+?|^1$)"}]; // 会话ID
  repeated string document_ids = 4 [(validate.rules).repeated = {
    min_items: 1
    unique: true
    items: {
      string: {pattern: "(?:^ai:document_id:.+?)"}
    }
  }]; // 文档ID集合
}

message BatchDeleteBlackBoxCaseDocumentForSessionResp {}

message GetBlackBoxCaseDocumentReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  string assistant_id = 2 [(validate.rules).string = {pattern: "(?:^ai:assistant_id:.+?|^1$)"}]; // 助手ID
  string document_id = 3 [(validate.rules).string = {pattern: "(?:^ai:document_id:.+?|^1$)"}]; // 文档ID
}

message GetBlackBoxCaseDocumentResp {
  BlackBoxCaseDocument item = 1; // 文档
}

message CreateBlackBoxCaseDocumentForAssistantReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  string assistant_id = 2 [(validate.rules).string = {pattern: "(?:^ai:assistant_id:.+?|^1$)"}]; // 助手ID
  repeated CreateBlackBoxCaseDocumentForAssistant items = 3; // 文档
}

message CreateBlackBoxCaseDocumentForAssistant {
  string document_name = 1 [(validate.rules).string = {
    min_len: 1
    max_len: 64
  }]; // 文档名称
  string document_description = 2 [(validate.rules).string = {
    min_len: 0
    max_len: 255
  }]; // 文档描述
  string document_url = 3 [(validate.rules).string = {
    min_len: 0
    max_len: 255
  }]; // 文档地址
  string document_text = 4 [(validate.rules).string = {
    min_len: 0
    max_len: 255
  }]; // 文档文本
  DocumentType document_type = 5; // 文档类型
}

message CreateBlackBoxCaseDocumentForAssistantResp {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  string assistant_id = 2 [(validate.rules).string = {pattern: "(?:^ai:assistant_id:.+?|^1$)"}]; // 助手ID
  repeated string document_id_list = 3 [(validate.rules).repeated = {
    min_items: 1
    unique: true
    items: {
      string: {pattern: "(?:^ai:document_id:.+?)"}
    }
  }]; // 文档ID集合
}

message UpdateBlackBoxCaseDocumentStatusForAssistantReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  string document_id = 2 [(validate.rules).string = {pattern: "(?:^ai:document_id:.+?|^1$)"}]; // 文档ID
  DocumentStatus status = 3; // 文档状态
}

message UpdateBlackBoxCaseDocumentStatusForAssistantResp {}

message CreateBlackBoxCaseDocumentForSessionReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  string assistant_id = 2 [(validate.rules).string = {pattern: "(?:^ai:assistant_id:.+?|^1$)"}]; // 助手ID
  string session_id = 3 [(validate.rules).string = {pattern: "(?:^ai:session_id:.+?|^1$)"}]; // 会话ID
  string document_id = 4 [(validate.rules).string = {pattern: "(?:^ai:document_id:.+?|^1$)"}]; // 文档ID
}

message CreateBlackBoxCaseDocumentForSessionResp {}

message CreateBlackBoxCaseDocumentForSessionRecvReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  string assistant_id = 2 [(validate.rules).string = {pattern: "(?:^ai:assistant_id:.+?|^1$)"}]; // 助手ID
  string session_id = 3 [(validate.rules).string = {pattern: "(?:^ai:session_id:.+?|^1$)"}]; // 会话ID
  string msg_document_text = 4; // 消息文档文本
}

message CreateBlackBoxCaseDocumentForSessionRecvResp {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  string assistant_id = 2 [(validate.rules).string = {pattern: "(?:^ai:assistant_id:.+?|^1$)"}]; // 助手ID
  string document_id = 3 [(validate.rules).string = {pattern: "(?:^ai:document_id:.+?|^1$)"}]; // 文档ID
}

// 3
message BlackBoxCaseSession {
  int32 id = 1; // 自增ID
  string project_id = 2; // 项目ID
  string assistant_id = 3; // 助手ID
  string session_id = 4; // 会话ID
  string session_name = 5; // 会话名称
  string session_description = 6; // 会话描述
  int32 session_round = 7; // 对话轮数[继承会话配置轮数]
  string document_ids = 8; // 文档集合[继承会话文档的子集]
  SessionStatus status = 9; // 会话状态
  string created_by = 10; // 创建者的用户ID
  string updated_by = 11; // 最近一次更新者的用户ID
  string deleted_by = 12; // 删除者的用户ID
  int64 created_at = 13; // 创建时间
  int64 updated_at = 14; // 更新时间
  int64 deleted_at = 15; // 删除时间
}

message SearchBlackBoxCaseSessionReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  string assistant_id = 2 [(validate.rules).string = {pattern: "(?:^ai:assistant_id:.+?|^1$)"}]; // 助手ID
}

message SearchBlackBoxCaseSessionResp {
  repeated BlackBoxCaseSession items = 1; // 会话列表
}

message UpdateBlackBoxCaseSessionReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  string assistant_id = 2 [(validate.rules).string = {pattern: "(?:^ai:assistant_id:.+?|^1$)"}];
  WorkSpaceType work_space_type = 3; // 工作空间类型
  string session_id = 4 [(validate.rules).string = {pattern: "(?:^ai:session_id:.+?|^1$)"}]; // 会话ID
  string session_name = 5 [(validate.rules).string = {
    min_len: 1
    max_len: 64
  }]; // 会话名称
  string session_description = 6 [(validate.rules).string = {
    min_len: 0
    max_len: 255
  }]; // 会话描述
}

message UpdateBlackBoxCaseSessionResp {}

message DeleteBlackBoxCaseSessionReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  string assistant_id = 2 [(validate.rules).string = {pattern: "(?:^ai:assistant_id:.+?|^1$)"}];
  string session_id = 3 [(validate.rules).string = {pattern: "(?:^ai:session_id:.+?|^1$)"}]; // 会话ID
}

message DeleteBlackBoxCaseSessionResp {}

message GetBlackBoxCaseSessionReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  string assistant_id = 2 [(validate.rules).string = {pattern: "(?:^ai:assistant_id:.+?|^1$)"}];
  string session_id = 3 [(validate.rules).string = {pattern: "(?:^ai:session_id:.+?|^1$)"}]; // 会话ID
}

message GetBlackBoxCaseSessionResp {
  BlackBoxCaseSession item = 1; // 会话
}

message SendBlackBoxCaseForSessionReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  string assistant_id = 2 [(validate.rules).string = {pattern: "(?:^ai:assistant_id:.+?|^1$)"}];
  string session_id = 3 [(validate.rules).string = {pattern: "(?:^ai:session_id:.+?|^1$)"}]; // 会话ID
  string message = 4; // 消息
  string conversation_id = 5; // 对话 id
}

message SendBlackBoxCaseForSessionResp {
  BlackBoxCaseSessionMessage message = 1; // 消息
  string event = 2; // 事件类型 [start | running | done]
}

message GetBlackBoxCaseSessionHistoryMessageListReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  string assistant_id = 2 [(validate.rules).string = {pattern: "(?:^ai:assistant_id:.+?|^1$)"}];
  string session_id = 3 [(validate.rules).string = {pattern: "(?:^ai:session_id:.+?|^1$)"}]; // 会话ID
  int32 session_round = 4; // 对话轮数
}

message GetBlackBoxCaseSessionHistoryMessageListResp {
  repeated BlackBoxCaseSessionMessage messages = 1; // 消息列表
}

message RemoveBlackBoxCaseSessionHistoryMessageListReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  string assistant_id = 2 [(validate.rules).string = {pattern: "(?:^ai:assistant_id:.+?|^1$)"}];
  string session_id = 3 [(validate.rules).string = {pattern: "(?:^ai:session_id:.+?|^1$)"}]; // 会话ID
}

message RemoveBlackBoxCaseSessionHistoryMessageListResp {}

message CreateBlackBoxCaseSessionReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  string assistant_id = 2 [(validate.rules).string = {pattern: "(?:^ai:assistant_id:.+?|^1$)"}];
  string session_name = 3 [(validate.rules).string = {
    min_len: 1
    max_len: 64
  }]; // 会话名称
  string session_description = 4 [(validate.rules).string = {
    min_len: 0
    max_len: 255
  }]; // 会话描述
}

message CreateBlackBoxCaseSessionResp {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  string assistant_id = 2 [(validate.rules).string = {pattern: "(?:^ai:assistant_id:.+?|^1$)"}];
  string session_id = 3 [(validate.rules).string = {pattern: "(?:^ai:session_id:.+?|^1$)"}]; // 会话ID
}

message BlackBoxCaseSessionMessage {
  BlackBoxCaseSessionMsgContent content = 1; // 消息内容
  BlackBoxCaseSessionMsgMetadata metadata = 2; // 消息元数据
}

message BlackBoxCaseSessionMsgContent {
  string human = 1; // human
  string ai = 2; // ai
}

message BlackBoxCaseSessionMsgMetadata {
  string content_type = 1; // 内容类型 table, text
  string session_id = 2; // 会话ID
  string created_at = 3; // 创建时间
  string conversation_id = 4; // 对话id
  BlackBoxCaseSessionConversationState state = 5;
}

message BlackBoxCaseSessionConversationState {
  string state = 1;
  string state_desc = 2;
}

message GetBlackBoxCaseConversationAIStateReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  string assistant_id = 2 [(validate.rules).string = {pattern: "(?:^ai:assistant_id:.+?|^1$)"}]; // 助手ID
  string session_id = 3 [(validate.rules).string = {pattern: "(?:^ai:session_id:.+?|^1$)"}];
  string conversation_id = 4;
}

message GetBlackBoxCaseConversationAIStateResp {
  string state = 1;
  string state_desc = 2;
}

message GetBlackBoxCaseConversationAIMessageReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  string assistant_id = 2 [(validate.rules).string = {pattern: "(?:^ai:assistant_id:.+?|^1$)"}]; // 助手ID
  string session_id = 3 [(validate.rules).string = {pattern: "(?:^ai:session_id:.+?|^1$)"}];
  string conversation_id = 4;
}

message GetBlackBoxCaseConversationAIMessageResp {
  string state = 1;
  string state_desc = 2;
  string message = 3;
}

message GetBlackBoxCaseModifyTestCaseReferenceContentListReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  string assistant_id = 2 [(validate.rules).string = {pattern: "(?:^ai:assistant_id:.+?|^1$)"}]; // 助手ID
  string session_id = 3 [(validate.rules).string = {pattern: "(?:^ai:session_id:.+?|^1$)"}];
  string conversation_id = 4;
}

message GetBlackBoxCaseModifyTestCaseReferenceContentListResp {
  string modify_suggestion = 1;
  repeated BlackBoxCaseReferenceContent reference_content = 2;
}

message BlackBoxCaseReferenceContent {
  string content_type = 1;
  repeated string doc_headers = 2;
  string doc_id = 3;
  string text = 4;
  string text_header = 5;
}

message CreateBlackBoxCaseModifyTestCaseReferenceContentReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  string assistant_id = 2 [(validate.rules).string = {pattern: "(?:^ai:assistant_id:.+?|^1$)"}]; // 助手ID
  string session_id = 3 [(validate.rules).string = {pattern: "(?:^ai:session_id:.+?|^1$)"}];
  string conversation_id = 4;
  repeated BlackBoxCaseReferenceContent reference_content = 5;
}

message CreateBlackBoxCaseModifyTestCaseReferenceContentResp {}

message ReplaceBlackBoxCaseReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  string assistant_id = 2 [(validate.rules).string = {pattern: "(?:^ai:assistant_id:.+?|^1$)"}]; // 助手ID
  string session_id = 3 [(validate.rules).string = {pattern: "(?:^ai:session_id:.+?|^1$)"}];
  string conversation_id = 4;
  string content = 5;
}

message ReplaceBlackBoxCaseResp {}

message MergeBlackBoxCaseReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  string assistant_id = 2 [(validate.rules).string = {pattern: "(?:^ai:assistant_id:.+?|^1$)"}]; // 助手ID
  string session_id = 3 [(validate.rules).string = {pattern: "(?:^ai:session_id:.+?|^1$)"}];
  repeated string conversation_ids = 4;
}

message MergeBlackBoxCaseResp {
  repeated BlackBoxCaseSessionMessage messages = 1;
}

message TransferBlackBoxCase2XMindReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}];
  string assistant_id = 2 [(validate.rules).string = {pattern: "(?:^ai:assistant_id:.+?|^1$)"}];
  string session_id = 3 [(validate.rules).string = {pattern: "(?:^ai:session_id:.+?|^1$)"}];
  string conversation_id = 4;
  MindType mind_type = 5;
}

message TransferBlackBoxCase2XMindResp {
  string data = 1;
}

message GetCreateBlackBoxCaseAIMessageReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}];
  string assistant_id = 2 [(validate.rules).string = {pattern: "(?:^ai:assistant_id:.+?|^1$)"}];
  string session_id = 3 [(validate.rules).string = {pattern: "(?:^ai:session_id:.+?|^1$)"}];
  string conversation_id = 4;
}

message GetCreateBlackBoxCaseAIMessageResp {
  string message = 1;
}

// 4
message BlackBoxCaseDir {
  int32 id = 1; // 自增ID
  string dir_id = 2; // 目录ID
  string project_id = 3; // 项目ID
  string dir_name = 4; // 目录名称
  string dir_description = 5; // 目录描述
  string created_by = 6; // 创建者的用户ID
  string updated_by = 7; // 最近一次更新者的用户ID
  string deleted_by = 8; // 删除者的用户ID
  int64 created_at = 9; // 创建时间
  int64 updated_at = 10; // 更新时间
  int64 deleted_at = 11; // 删除时间
}

message CreateBlackBoxCaseDirReq {
  string project_id = 1;
  string dir_name = 2;
  // string dir_description = 3;
}

message CreateBlackBoxCaseDirResp {
  string dir_id = 1;
}

message DeleteBlackBoxCaseDirReq {
  string project_id = 1;
  string dir_id = 2;
}

message DeleteBlackBoxCaseDirResp {}

message UpdateBlackBoxCaseDirReq {
  string project_id = 1;
  string dir_id = 2;
  string dir_name = 3;
  // string dir_description = 4;
}

message UpdateBlackBoxCaseDirResp {}

message GetBlackBoxCaseDirReq {
  string project_id = 1;
  string dir_id = 2;
}

message GetBlackBoxCaseDirResp {
  BlackBoxCaseDir item = 1;
}

message ListBlackBoxCaseDirsReq {
  string project_id = 1;
}

message ListBlackBoxCaseDirsResp {
  repeated BlackBoxCaseDir items = 1;
}

// 5
message BlackBoxCase {
  int32 id = 1; // 自增ID
  string dir_id = 2; // 目录ID
  string case_id = 4; // 用例ID
  string case_name = 5; // 用例名称
  int64 case_continue_to_write = 6; // 用例续写次数
  string case_model_character = 7; // 用例模型性格
  string knowledge_id = 8; // 知识文档关联ID
  string case_remarks = 9; // 知识文档备注
  string knowledge_name = 10; // 知识文档名称
  string knowledge_content = 11; // 知识文档内容/链接
  int64 knowledge_paragraph_title_id = 12; // 创建所选文档标题id
  string reference_doc = 13; // 修改关联文档信息
  bool enable_reference = 14; // 是否关联文档
  string knowledge_fix_sugg = 15; // 修改意见
  string created_by = 21; // 创建者的用户ID
  string updated_by = 22; // 最近一次更新者的用户ID
  string deleted_by = 23; // 删除者的用户ID
  int64 created_at = 24; // 创建时间
  int64 updated_at = 25; // 更新时间
  int64 deleted_at = 26; // 删除时间
  string knowledge_paragraph_title_text = 27; // 知识文档段落标题
  string knowledge_size = 28; // 用例文档尺寸
  repeated KnowledgeDocPgTitle knowledge_paragraph_title = 29; // 知识文档段落标题列表
}

message KnowledgeDocPgTitle {
  int64 id = 1; // 标题id
  string title = 2;
  repeated string demand_points = 3;
}

message CreateBlackBoxCaseReq {
  string dir_id = 2;
  string case_name = 3;
  int64 case_continue_to_write = 4;
  string case_model_character = 5;
  string knowledge_id = 6;
  string case_remarks = 7;
  int64 knowledge_paragraph_title_id = 8;
}
message CreateBlackBoxCaseResp {
  string case_id = 1;
}

message DeleteBlackBoxCaseReq {
  string dir_id = 2;
  string case_id = 3;
}

message DeleteBlackBoxCaseResp {}

message UpdateBlackBoxCaseReq {
  BlackBoxCase item = 1;
}
message UpdateBlackBoxCaseResp {}

message UpdateBlackBoxCaseAIReq {
  string case_id = 1;
  string case_name = 2;
  int64 case_continue_to_write = 3;
  string case_model_character = 4;
}
message UpdateBlackBoxCaseAIResp {}

message GetBlackBoxCaseReq {
  string dir_id = 2;
  string case_id = 3;
}

message GetBlackBoxCaseResp {
  BlackBoxCase item = 1;
}

message ListBlackBoxCaseReq {
  string dir_id = 2;
  string case_name = 3;
  sqlbuilder.Pagination pagination = 4;
}
message ListBlackBoxCaseResp {
  uint64 current_page = 1; // 当前页数
  uint64 page_size = 2; // 每页大小
  uint64 total_count = 3; // 总数
  uint64 total_page = 4; // 总页数
  repeated BlackBoxCase items = 5;
}

message MergeBlackBoxCaseDataReq {
  repeated string case_ids = 1;
  string case_name = 2;
}
message MergeBlackBoxCaseDataResp {
  string case_id = 1;
}

message QueryIncBlackBoxCaseReq {
  string case_id = 1;
}
message QueryIncBlackBoxCaseResp {}

message ReorderBlackBoxCaseDataReq {
  string revision_id = 1;
}
message ReorderBlackBoxCaseDataResp {}

message ListBlackBoxCaseDataByCaseIdsReq {
  repeated string case_ids = 1;
}

message ListBlackBoxCaseDataByCaseIdsResp {
  repeated BlackBoxCaseData items = 1;
}

// 6
message BlackBoxCaseKnowledge {
  int32 id = 1; // 自增ID
  string dir_id = 3; // 目录ID
  string knowledge_id = 4; // 文档ID
  string knowledge_name = 6; // 文档名称
  string knowledge_type = 7; // 文档类型
  string knowledge_content = 8; // 文档内容
  string knowledge_status = 9; // 文档状态
  string knowledge_paragraph_title = 10; // 文档标题
  string knowledge_error_msg = 11; // 文档错误信息
  string created_by = 20; // 创建者的用户ID
  string updated_by = 21; // 最近一次更新者的用户ID
  string deleted_by = 22; // 删除者的用户ID
  int64 created_at = 23; // 创建时间
  int64 updated_at = 24; // 更新时间
  int64 deleted_at = 25; // 删除时间
}

message CreateBlackBoxCaseKnowledgeReq {
  string dir_id = 2;
  string knowledge_name = 3;
  string knowledge_type = 4;
  string knowledge_content = 5;
}

message CreateBlackBoxCaseKnowledgeResp {
  string knowledge_id = 1;
}

message DeleteBlackBoxCaseKnowledgeReq {
  string knowledge_id = 1;
}

message DeleteBlackBoxCaseKnowledgeResp {}

message ReloadBlackBoxCaseKnowledgeReq {
  string knowledge_id = 1;
}

message ReloadBlackBoxCaseKnowledgeResp {}

message UpdateBlackBoxCaseKnowledgeReq {
  string dir_id = 2;
  string knowledge_id = 3;
  string knowledge_name = 4;
  string knowledge_type = 5;
  string knowledge_content = 6;
}

message UpdateBlackBoxCaseKnowledgeResp {}

message GetBlackBoxCaseKnowledgeReq {
  string dir_id = 2;
  string knowledge_id = 3;
}

message GetBlackBoxCaseKnowledgeResp {
  BlackBoxCaseKnowledge item = 1;
}

message ListBlackBoxCaseKnowledgeReq {
  string dir_id = 2;
}

message ListBlackBoxCaseKnowledgeResp {
  repeated BlackBoxCaseKnowledge items = 5;
}

// 7
message BlackBoxCaseRevision {
  int32 id = 1; // 自增ID
  string case_id = 2; // 用例ID
  string revision_id = 3; // 版本ID
  string revision_name = 4; // 版本名称
  string knowledge_id = 5; // 知识文档ID
  string knowledge_paragraph_title_text = 6; // 知识文档段落标题
  string case_ref_id = 7; // 用例管理ID
  int64 coverage = 8; // 覆盖率
  string deleted = 20; // 是否删除
  string created_by = 21; // 创建者的用户ID
  string updated_by = 22; // 最近一次更新者的用户ID
  string deleted_by = 23; // 删除者的用户ID
  int64 created_at = 24; // 创建时间
  int64 updated_at = 25; // 更新时间
  int64 deleted_at = 26; // 删除时间
  int64 add_count = 27; // 新增数量
  int64 update_count = 28; // 修改数量
  repeated string knowledge_paragraph_title= 29;
}

message CreateBlackBoxCaseRevisionReq {
  string case_id = 1;
  string revision_name = 2;
  string knowledge_id = 3;
  string knowledge_paragraph_title_text = 4;
  string revision_id = 5;
  string case_ref_id = 6;
  repeated string knowledge_paragraph_title= 7;
}
message CreateBlackBoxCaseRevisionResp {
  string revision_id = 1;
}

message CreateBlackBoxCaseRevisionDataReq {
  string case_id = 1;
  string revision_name = 2;
  string knowledge_id = 3;
  string knowledge_paragraph_title_text = 4;
  repeated BlackBoxCaseData items = 5;
  repeated string knowledge_paragraph_title= 6;
}
message CreateBlackBoxCaseRevisionDataResp {
  string revision_id = 1;
}

message DeleteBlackBoxCaseRevisionReq {
  string revision_id = 3;
}
message DeleteBlackBoxCaseRevisionResp {}

message UpdateBlackBoxCaseRevisionReq {
  BlackBoxCaseRevision blackBoxCaseRevision = 1;
}
message UpdateBlackBoxCaseRevisionResp {}

message UpdateBlackBoxCaseRevisionWithCaseRefIdReq {
  BlackBoxCaseRevision blackBoxCaseRevision = 1;
}
message UpdateBlackBoxCaseRevisionWithCaseRefIdResp {}

message AdoptBlackBoxCaseRevisionReq {
  string revision_id = 2;
}
message AdoptBlackBoxCaseRevisionResp {}

message GetBlackBoxCaseRevisionReq {
  string revision_id = 2;
}
message GetBlackBoxCaseRevisionResp {
  BlackBoxCaseRevision item = 1;
}

message ListBlackBoxCaseRevisionReq {
  string case_id = 1;
}
message ListBlackBoxCaseRevisionResp {
  repeated BlackBoxCaseRevision items = 5;
}

message GetBlackBoxCaseRevisionByRevisionIdReq {
  string revision_id = 1;
}
message GetBlackBoxCaseRevisionByRevisionIdResp {
  BlackBoxCaseRevision revision_data = 1;
}

message GenerateBlackBoxCaseRevisionIdReq {}
message GenerateBlackBoxCaseRevisionIdResp {
  string revision_id = 1;
}

// 8
message BlackBoxCaseData {
  int32 id = 1; // 自增ID
  string case_id = 2; // 用例ID
  string revision_id = 3; // 版本ID
  string case_data_id = 4; // 数据ID
  string order_id = 5; // 序号
  string requirement = 6; // 需求名称
  string pre_condition = 7; // 前置条件
  string case_step = 8; // 用例步骤
  string expect_result = 9; // 预期结果
  string terminal = 10; // 终端
  string case_level = 11; // 用例等级
  string tag = 12; // 标识
  bool is_keep = 13; // 是否保留
  string case_name = 14; // 用例名称
  string deleted = 20; // 是否删除
  string created_by = 21; // 创建者的用户ID
  string updated_by = 22; // 最近一次更新者的用户ID
  string deleted_by = 23; // 删除者的用户ID
  int64 created_at = 24; // 创建时间
  int64 updated_at = 25; // 更新时间
  int64 deleted_at = 26; // 删除时间
  string version = 27; // 版本
}

message CreateBlackBoxCaseDataReq {
  repeated BlackBoxCaseData items = 1;
}
message CreateBlackBoxCaseDataResp {}

message BatchUpdateBlackBoxCaseDataReq {
  repeated BlackBoxCaseData items = 1;
}
message BatchUpdateBlackBoxCaseDataResp {}

message DeleteBlackBoxCaseDataReq {
  string case_data_id = 1;
}

message DeleteBlackBoxCaseDataResp {}

message UpdateBlackBoxCaseDataReq {
  string case_data_id = 4;
  string order_id = 5;
  string requirement = 6;
  string pre_condition = 7;
  string case_step = 8;
  string expect_result = 9;
  string terminal = 10;
  string case_level = 11;
  string tag = 12;
  string case_name = 13;
  string version = 14;
  string edited_field = 15;
}

message UpdateBlackBoxCaseDataResp {}

message UpdateBlackBoxCaseDataOrderReq {
  string revision_id = 1;
}

message UpdateBlackBoxCaseDataOrderResp {}

message GetBlackBoxCaseDataReq {
  string case_data_id = 1;
}

message GetBlackBoxCaseDataResp {
  BlackBoxCaseData item = 1;
}

message ListBlackBoxCaseDataReq {
  string case_id = 1;
  string revision_id = 2;
  bool is_keep = 3;
  string type = 4;
}

message ListBlackBoxCaseDataResp {
  repeated BlackBoxCaseData items = 1;
  string content = 3;
}

message KeepBlackBoxCaseDataReq {
  string case_data_id = 1;
  string action = 2;
}
message KeepBlackBoxCaseDataResp {}

message ClearKeepBlackBoxCaseDataReq {
  string case_id = 1;
}
message ClearKeepBlackBoxCaseDataResp {}

message AppendBlackBoxCaseDataReq {
  repeated BlackBoxCaseData items = 1;
  string revision_id = 2;
}

message AppendBlackBoxCaseDataResp {
  repeated BlackBoxCaseData items = 1;
  repeated BlackBoxCaseData new_items = 2;
}

message ReplaceBlackBoxCaseDataReq {
  repeated BlackBoxCaseData items = 1;
  repeated BlackBoxCaseData new_items = 2;
  string revision_id = 3;
}

message ReplaceBlackBoxCaseDataResp {
  repeated BlackBoxCaseData items = 1;
  repeated BlackBoxCaseData new_items = 2;
}

message GenerateBlackBoxCaseRefReq {
  string case_ref_type = 1;
}

message GenerateBlackBoxCaseRefResp {
  string case_ref_id = 1;
}

message RefreshCaseRefMetricsReq {
  string case_ref_type = 1;
}
message RefreshCaseRefMetricsResp {}

message ReloadBlackBoxCaseDocumentReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  string assistant_id = 2 [(validate.rules).string = {pattern: "(?:^ai:assistant_id:.+?|^1$)"}]; // 助手ID
  string document_id = 3 [(validate.rules).string = {pattern: "(?:^ai:document_id:.+?|^1$)"}]; // 文档ID
}

message ReloadBlackBoxCaseDocumentResp {}

message ListBlackBoxCaseTwBetaReq {
  string beta_case_name = 1;
  sqlbuilder.Pagination pagination = 2;
}

message ListBlackBoxCaseTwBetaResp {
  uint64 current_page = 1; // 当前页数
  uint64 page_size = 2; // 每页大小
  uint64 total_count = 3; // 总数
  uint64 total_page = 4; // 总页数
  repeated BlackBoxCaseTwBeta items = 5;
}

message CreateBlackBoxCaseTwBetaMindReq {
  string beta_case_id = 1;
  string product_module = 2;
  repeated BlackBoxCaseTwBetaMindCheck ac_check_list = 3;
  repeated BlackBoxCaseTwBetaMindCheck tc_check_list = 4;
}

message CreateBlackBoxCaseTwBetaMindResp {}

message ListBlackBoxCaseTwBetaMindReq {
  string beta_case_id = 1;
}

message ListBlackBoxCaseTwBetaMindResp {
  repeated BlackBoxCaseTwBetaMind items = 1;
}

message GetBlackBoxCaseTwBetaMindContentReq {
  int64 id = 1;
  string mind_type = 2;
}

message GetBlackBoxCaseTwBetaMindContentResp {
  string data = 1;
}

message ListBlackBoxCaseTwBetaMindCheckReq {
  string product_module = 1;
}

message ListBlackBoxCaseTwBetaMindCheckResp {
  repeated BlackBoxCaseTwBetaMindCheck ac_check_list = 1;
  repeated BlackBoxCaseTwBetaMindCheck tc_check_list = 2;
}

message DeleteBlackBoxCaseTwBetaReq {
  string beta_case_id = 1;
}

message DeleteBlackBoxCaseTwBetaResp {}

message CreateBlackBoxCaseTwBetaReq {
  BlackBoxCaseRevision revision = 1;
  repeated BlackBoxCaseData items = 2;
}

message CreateBlackBoxCaseTwBetaResp {}

message UpdateBlackBoxCaseMindTwBetaReq {
  uint64 mindId = 1;
  string mindContent = 2;
}

message UpdateBlackBoxCaseMindTwBetaResp {}

message GetBlackBoxCaseTwBetaContentReq {
  uint64 mindId = 1;
}

message GetBlackBoxCaseTwBetaContentResp {
  string data = 1;
}

message GetBlackBoxCaseKnowledgeV2Req {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}];
  string dir_id = 2;
  string case_id = 3;
}

message GetBlackBoxCaseKnowledgeV2Resp {
  string tags = 1;
  string experiences = 2;
}

message UpdateBlackBoxCaseKnowledgeV2Req {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}];
  string dir_id = 2;
  string case_id = 3;
  string tags = 4;
  string experiences = 5;
}

message UpdateBlackBoxCaseKnowledgeV2Resp {}

message UpdateBlackBoxCaseMapUsedKnowledgeReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}];
  string dir_id = 2;
  string case_id = 3;
  string used_knowledge = 4;
}
message UpdateBlackBoxCaseMapUsedKnowledgeResp {}

message GetBlackBoxCaseMapReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}];
  string dir_id = 2;
  string case_id = 3;
}
message GetBlackBoxCaseMapResp {
  string data = 1;
}

message GetCompleteBlackBoxCaseMapReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}];
  string dir_id = 2;
  string case_id = 3;
}
message GetCompleteBlackBoxCaseMapResp {
  string project_id = 1; // 项目ID
  string dir_id = 2; // 目录ID
  string case_id = 3; // 用例ID
  string map_id = 4; // 评审导图ID
  string data = 5; // 评审导图数据
  string tags = 6; // 术语标签
  string experiences = 7; // 测试经验
  string created_by = 8; // 创建者的用户ID
  string updated_by = 9; // 最近一次更新者的用户ID
  string deleted_by = 10; // 删除者的用户ID
  string used_knowledge = 11; // 已使用的知识
  string recall_document = 12; // 召回文档
}

message UpdateBlackBoxCaseMapReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}];
  string dir_id = 2;
  string case_id = 3;
  string data = 4;
}
message UpdateBlackBoxCaseMapResp {}

message CreateBlackBoxCaseMapReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}];
  string dir_id = 2;
  string case_id = 3;
  string data = 4; // 需要外部自行marshal
  BlackBoxKnowledgeTag tags = 6;
  repeated BlackBoxKnowledgeExperience experiences = 7;
  BlackBoxKnowledgeUsedKnowledge used_knowledge = 8;
  repeated BlackBoxKnowledgeRecallDocument recall_document = 9;
}
message CreateBlackBoxCaseMapResp {}

message CreateBlackBoxCaseMapIdReq {
  string case_id = 1;
}
message CreateBlackBoxCaseMapIdResp {
  string map_id = 1;
}

message BlackBoxKnowledgeTag {
  int64 id = 1;
  string name = 2;
}

message BlackBoxKnowledgeExperience {
  string abnormal_focus_point = 1;
  string category = 2;
  string id = 3;
  string normal_focus_point = 4;
  string project = 5;
  string project_id = 6;
  int32 score = 7;
  repeated string tags = 8;
  string test_experience = 9;
}

message BlackBoxKnowledgeRecallDocument {
  string title = 1;
  string content = 2;
}

message BlackBoxKnowledgeUsedKnowledge {
  string terms = 1; // markdown table
  string experiences = 2; // markdown table
}

message BlackBoxCaseMapDocument {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}];
  string dir_id = 2;
  string case_id = 3;
  string func_id = 4;
  string func_name = 5;
  string func_doc = 6;
  string expe_doc = 7;
}

message ListBlackBoxCaseMapDocumentsReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}];
  string dir_id = 2;
  string case_id = 3;
}
message ListBlackBoxCaseMapDocumentsResp {
  repeated BlackBoxCaseMapDocument items = 1;
}

message GetBlackBoxCaseMapDocumentReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}];
  string dir_id = 2;
  string case_id = 3;
  string func_id = 4;
}
// message GetBlackBoxCaseMapDocumentResp {}

// message UpdateBlackBoxCaseMapDocumentReq {}
message UpdateBlackBoxCaseMapDocumentResp {}

message DeleteBlackBoxCaseMapDocumentReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}];
  string dir_id = 2;
  string case_id = 3;
  string func_id = 4;
}
message DeleteBlackBoxCaseMapDocumentResp {}

message UpdateBlackBoxCaseMapRecallDocumentReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}];
  string dir_id = 2;
  string case_id = 3;
  string recall_document = 4;
}
message UpdateBlackBoxCaseMapRecallDocumentResp {}

message AddBlackBoxFuncCountReq {}
message AddBlackBoxFuncCountResp {}

message AddBlackBoxTestSceneCountReq {}
message AddBlackBoxTestSceneCountResp {}

message AddBlackBoxSupplementDocumentCountReq {}
message AddBlackBoxSupplementDocumentCountResp {}

message AddBlackBoxBaseMapCountReq {}
message AddBlackBoxBaseMapCountResp {}

message AddBlackBoxUpdatedAddCaseCountReq {
  int64 count = 1;
}
message AddBlackBoxUpdatedAddCaseCountResp {}

message AddBlackBoxCaseNameUpdatedCountReq {}
message AddBlackBoxCaseNameUpdatedCountResp {}

message AddBlackBoxPreConditionUpdatedCountReq {}
message AddBlackBoxPreConditionUpdatedCountResp {}

message AddBlackBoxCaseStepUpdatedCountReq {}
message AddBlackBoxCaseStepUpdatedCountResp {}

message AddBlackBoxExpectResultUpdatedCountReq {}
message AddBlackBoxExpectResultUpdatedCountResp {}

message AddBlackBoxCaseLevelUpdatedCountReq {}
message AddBlackBoxCaseLevelUpdatedCountResp {}

service BlackBoxCaseTwBetaService {
  // GetBlackBoxCaseTwBetaCaseContent
  rpc GetBlackBoxCaseTwBetaCaseContent(GetBlackBoxCaseTwBetaContentReq) returns (GetBlackBoxCaseTwBetaContentResp);

  // UpdateBlackBoxCaseTwBetaMindContent
  rpc UpdateBlackBoxCaseTwBetaMindContent(UpdateBlackBoxCaseMindTwBetaReq) returns (UpdateBlackBoxCaseMindTwBetaResp);

  // CreateBlackBoxCaseTwBeta list black box case of tw beta
  rpc CreateBlackBoxCaseTwBeta(CreateBlackBoxCaseTwBetaReq) returns (CreateBlackBoxCaseTwBetaResp);

  // ListBlackBoxCaseTwBeta list black box case of tw beta
  rpc ListBlackBoxCaseTwBeta(ListBlackBoxCaseTwBetaReq) returns (ListBlackBoxCaseTwBetaResp);

  // CreateBlackBoxCaseTwBetaMind create black box case of tw beta mind
  rpc createBlackBoxCaseTwBetaMind(CreateBlackBoxCaseTwBetaMindReq) returns (CreateBlackBoxCaseTwBetaMindResp);

  // ListBlackBoxCaseTwBetaMind list black box case of tw beta mind
  rpc ListBlackBoxCaseTwBetaMind(ListBlackBoxCaseTwBetaMindReq) returns (ListBlackBoxCaseTwBetaMindResp);

  // GetBlackBoxCaseTwBetaMindContent get black box case of tw beta mind content
  rpc GetBlackBoxCaseTwBetaMindContent(GetBlackBoxCaseTwBetaMindContentReq) returns (GetBlackBoxCaseTwBetaMindContentResp);

  // ListBlackBoxCaseTwBetaMindCheck list black box case of tw beta mind check
  rpc ListBlackBoxCaseTwBetaMindCheck(ListBlackBoxCaseTwBetaMindCheckReq) returns (ListBlackBoxCaseTwBetaMindCheckResp);

  // DeleteBlackBoxCaseTwBeta delete black box case of tw beta
  rpc DeleteBlackBoxCaseTwBeta(DeleteBlackBoxCaseTwBetaReq) returns (DeleteBlackBoxCaseTwBetaResp);
}

// BlackBoxCaseAssistantService
service BlackBoxCaseAssistantService {
  // CreateBlackBoxCaseAssistant creates a black box case assistant
  rpc CreateBlackBoxCaseAssistant(CreateBlackBoxCaseAssistantReq) returns (CreateBlackBoxCaseAssistantResp);

  // UpdateBlackBoxCaseAssistant updates a black box case assistant
  rpc UpdateBlackBoxCaseAssistant(UpdateBlackBoxCaseAssistantReq) returns (UpdateBlackBoxCaseAssistantResp);

  // DeleteBlackBoxCaseAssistant deletes a black box case assistant
  rpc DeleteBlackBoxCaseAssistant(DeleteBlackBoxCaseAssistantReq) returns (DeleteBlackBoxCaseAssistantResp);

  // GetBlackBoxCaseAssistant gets a black box case assistant
  rpc GetBlackBoxCaseAssistant(GetBlackBoxCaseAssistantReq) returns (GetBlackBoxCaseAssistantResp);

  // SearchBlackBoxCaseAssistant searches for black box case assistants
  rpc SearchBlackBoxCaseAssistant(SearchBlackBoxCaseAssistantReq) returns (SearchBlackBoxCaseAssistantResp);
}

// BlackBoxCaseDocumentService
service BlackBoxCaseDocumentService {
  // CreateBlackBoxCaseDocumentForAssistant creates a black box case document for assistant
  rpc CreateBlackBoxCaseDocumentForAssistant(CreateBlackBoxCaseDocumentForAssistantReq) returns (CreateBlackBoxCaseDocumentForAssistantResp);

  // CreateBlackBoxCaseDocumentForSession creates a black box case document for session
  rpc CreateBlackBoxCaseDocumentForSession(CreateBlackBoxCaseDocumentForSessionReq) returns (CreateBlackBoxCaseDocumentForSessionResp);

  // CreateBlackBoxCaseDocumentForSessionRecv creates a black box case document for session recv
  rpc CreateBlackBoxCaseDocumentForSessionRecv(CreateBlackBoxCaseDocumentForSessionRecvReq) returns (CreateBlackBoxCaseDocumentForSessionRecvResp);

  // UpdateBlackBoxCaseDocumentStatusForAssistant update black box case document status for assistant
  rpc UpdateBlackBoxCaseDocumentStatusForAssistant(UpdateBlackBoxCaseDocumentStatusForAssistantReq) returns (UpdateBlackBoxCaseDocumentStatusForAssistantResp);

  // GetBlackBoxCaseDocument gets a black box case document
  rpc GetBlackBoxCaseDocument(GetBlackBoxCaseDocumentReq) returns (GetBlackBoxCaseDocumentResp);

  // UpdateBlackBoxCaseDocument updates a black box case document
  rpc UpdateBlackBoxCaseDocument(UpdateBlackBoxCaseDocumentReq) returns (UpdateBlackBoxCaseDocumentResp);

  // ReloadBlackBoxCaseDocument reload black box case document
  rpc ReloadBlackBoxCaseDocument(ReloadBlackBoxCaseDocumentReq) returns (ReloadBlackBoxCaseDocumentResp);

  // DeleteBlackBoxCaseDocumentForAssistant deletes a black box case document for assistant
  rpc DeleteBlackBoxCaseDocumentForAssistant(DeleteBlackBoxCaseDocumentForAssistantReq) returns (DeleteBlackBoxCaseDocumentForAssistantResp);

  // BatchDeleteBlackBoxCaseDocumentForAssistant batch deletes black box case documents for assistant
  rpc BatchDeleteBlackBoxCaseDocumentForAssistant(BatchDeleteBlackBoxCaseDocumentForAssistantReq) returns (BatchDeleteBlackBoxCaseDocumentForAssistantResp);

  // DeleteBlackBoxCaseDocumentForSession deletes a black box case document for session
  rpc DeleteBlackBoxCaseDocumentForSession(DeleteBlackBoxCaseDocumentForSessionReq) returns (DeleteBlackBoxCaseDocumentForSessionResp);

  // BatchDeleteBlackBoxCaseDocumentForSession batch deletes black box case documents for session
  rpc BatchDeleteBlackBoxCaseDocumentForSession(BatchDeleteBlackBoxCaseDocumentForSessionReq) returns (BatchDeleteBlackBoxCaseDocumentForSessionResp);

  // SearchBlackBoxCaseDocumentForAssistant searches for black box case documents for assistant
  rpc SearchBlackBoxCaseDocumentForAssistant(SearchBlackBoxCaseDocumentForAssistantReq) returns (SearchBlackBoxCaseDocumentForAssistantResp);

  // SearchBlackBoxCaseDocumentForSession searches for black box case documents for session
  rpc SearchBlackBoxCaseDocumentForSession(SearchBlackBoxCaseDocumentForSessionReq) returns (SearchBlackBoxCaseDocumentForSessionResp);

  // GetBlackBoxCaseDocumentHeadersList get black box case document Headers list
  rpc GetBlackBoxCaseDocumentHeadersList(GetBlackBoxCaseDocumentHeadersListReq) returns (GetBlackBoxCaseDocumentHeadersListResp);
}

// BlackBoxCaseSessionService
service BlackBoxCaseSessionService {
  // CreateBlackBoxCaseSession creates a black box case session
  rpc CreateBlackBoxCaseSession(CreateBlackBoxCaseSessionReq) returns (CreateBlackBoxCaseSessionResp);

  // UpdateBlackBoxCaseSession updates a black box case session
  rpc UpdateBlackBoxCaseSession(UpdateBlackBoxCaseSessionReq) returns (UpdateBlackBoxCaseSessionResp);

  // DeleteBlackBoxCaseSession deletes a black box case session
  rpc DeleteBlackBoxCaseSession(DeleteBlackBoxCaseSessionReq) returns (DeleteBlackBoxCaseSessionResp);

  // GetBlackBoxCaseSession gets a black box case session
  rpc GetBlackBoxCaseSession(GetBlackBoxCaseSessionReq) returns (GetBlackBoxCaseSessionResp);

  // SearchBlackBoxCaseSession searches for black box case sessions
  rpc SearchBlackBoxCaseSession(SearchBlackBoxCaseSessionReq) returns (SearchBlackBoxCaseSessionResp);

  // GetBlackBoxCaseSessionHistoryMessageList gets black box case session history message list
  rpc GetBlackBoxCaseSessionHistoryMessageList(GetBlackBoxCaseSessionHistoryMessageListReq) returns (GetBlackBoxCaseSessionHistoryMessageListResp);

  // RemoveBlackBoxCaseSessionHistoryMessageList removes black box case session history message list
  rpc RemoveBlackBoxCaseSessionHistoryMessageList(RemoveBlackBoxCaseSessionHistoryMessageListReq) returns (RemoveBlackBoxCaseSessionHistoryMessageListResp);

  // SendBlackBoxCaseForSession sends black box case for session
  rpc SendBlackBoxCaseForSession(SendBlackBoxCaseForSessionReq) returns (SendBlackBoxCaseForSessionResp);

  // GetBlackBoxCaseConversationAIState get black box case conversation ai state
  rpc GetBlackBoxCaseConversationAIState(GetBlackBoxCaseConversationAIStateReq) returns (GetBlackBoxCaseConversationAIStateResp);

  // GetBlackBoxCaseConversationAIMessage get black box case conversation ai message
  rpc GetBlackBoxCaseConversationAIMessage(GetBlackBoxCaseConversationAIMessageReq) returns (GetBlackBoxCaseConversationAIMessageResp);

  // GetBlackBoxCaseConversationAIMessageList get black box case modify reference content list
  rpc GetBlackBoxCaseModifyTestCaseReferenceContentList(GetBlackBoxCaseModifyTestCaseReferenceContentListReq) returns (GetBlackBoxCaseModifyTestCaseReferenceContentListResp);

  // CreateBlackBoxCaseModifyTestCaseReferenceContent create black box case modify reference content
  rpc CreateBlackBoxCaseModifyTestCaseReferenceContent(CreateBlackBoxCaseModifyTestCaseReferenceContentReq) returns (CreateBlackBoxCaseModifyTestCaseReferenceContentResp);

  // ReplaceBlackBoxCase replace black box case
  rpc ReplaceBlackBoxCase(ReplaceBlackBoxCaseReq) returns (ReplaceBlackBoxCaseResp);

  // MergeBlackBoxCase merge black box case
  rpc MergeBlackBoxCase(MergeBlackBoxCaseReq) returns (MergeBlackBoxCaseResp);

  // TransferBlackBoxCase2XMind transfer black box case
  rpc TransferBlackBoxCase2XMind(TransferBlackBoxCase2XMindReq) returns (TransferBlackBoxCase2XMindResp);

  // GetBlackBoxCaseAIMessageList get black box case conversation ai message
  rpc GetBlackBoxCaseAIMessage(GetCreateBlackBoxCaseAIMessageReq) returns (GetCreateBlackBoxCaseAIMessageResp);
}

// BlackBoxCaseDirService
service BlackBoxCaseDirService {
  // CreateBlackBoxCaseDir create a black box case dir
  rpc CreateBlackBoxCaseDir(CreateBlackBoxCaseDirReq) returns (CreateBlackBoxCaseDirResp);

  // DeleteBlackBoxCaseDir create a black box case dir
  rpc DeleteBlackBoxCaseDir(DeleteBlackBoxCaseDirReq) returns (DeleteBlackBoxCaseDirResp);

  // UpdateBlackBoxCaseDir create a black box case dir
  rpc UpdateBlackBoxCaseDir(UpdateBlackBoxCaseDirReq) returns (UpdateBlackBoxCaseDirResp);

  // GetBlackBoxCaseDir gets a black box case dir
  rpc GetBlackBoxCaseDir(GetBlackBoxCaseDirReq) returns (GetBlackBoxCaseDirResp);

  // ListBlackBoxCaseDirs list black box case dirs
  rpc ListBlackBoxCaseDirs(ListBlackBoxCaseDirsReq) returns (ListBlackBoxCaseDirsResp);
}

// BlackBoxCaseService
service BlackBoxCaseService {
  // CreateBlackBoxCase create a black box case
  rpc CreateBlackBoxCase(CreateBlackBoxCaseReq) returns (CreateBlackBoxCaseResp);

  // DeleteBlackBoxCase create a black box case
  rpc DeleteBlackBoxCase(DeleteBlackBoxCaseReq) returns (DeleteBlackBoxCaseResp);

  // UpdateBlackBoxCase create a black box case
  rpc UpdateBlackBoxCase(UpdateBlackBoxCaseReq) returns (UpdateBlackBoxCaseResp);

  // UpdateBlackBoxCase create a black box case
  rpc UpdateBlackBoxCaseAI(UpdateBlackBoxCaseAIReq) returns (UpdateBlackBoxCaseAIResp);

  // GetBlackBoxCase gets a black box case
  rpc GetBlackBoxCase(GetBlackBoxCaseReq) returns (GetBlackBoxCaseResp);

  // ListBlackBoxCase list black box case
  rpc ListBlackBoxCase(ListBlackBoxCaseReq) returns (ListBlackBoxCaseResp);

  // MergeBlackBoxCaseData list black box case
  rpc MergeBlackBoxCaseData(MergeBlackBoxCaseDataReq) returns (MergeBlackBoxCaseDataResp);

  // QueryIncBlackBoxCase list black box case
  rpc QueryIncBlackBoxCase(QueryIncBlackBoxCaseReq) returns (QueryIncBlackBoxCaseResp);
}

// BlackBoxCaseKnowledgeService
service BlackBoxCaseKnowledgeService {
  // CreateBlackBoxCaseKnowledge create a black box case
  rpc CreateBlackBoxCaseKnowledge(CreateBlackBoxCaseKnowledgeReq) returns (CreateBlackBoxCaseKnowledgeResp);

  // DeleteBlackBoxCaseKnowledge create a black box case
  rpc DeleteBlackBoxCaseKnowledge(DeleteBlackBoxCaseKnowledgeReq) returns (DeleteBlackBoxCaseKnowledgeResp);

  // UpdateBlackBoxCaseKnowledge create a black box case
  rpc UpdateBlackBoxCaseKnowledge(UpdateBlackBoxCaseKnowledgeReq) returns (UpdateBlackBoxCaseKnowledgeResp);

  // GetBlackBoxCaseKnowledge gets a black box case
  rpc GetBlackBoxCaseKnowledge(GetBlackBoxCaseKnowledgeReq) returns (GetBlackBoxCaseKnowledgeResp);

  // ListBlackBoxCaseKnowledge list black box case
  rpc ListBlackBoxCaseKnowledge(ListBlackBoxCaseKnowledgeReq) returns (ListBlackBoxCaseKnowledgeResp);

  // ReloadBlackBoxCaseKnowledge list black box case
  rpc ReloadBlackBoxCaseKnowledge(ReloadBlackBoxCaseKnowledgeReq) returns (ReloadBlackBoxCaseKnowledgeResp);
}

// BlackBoxCaseRevisionService
service BlackBoxCaseRevisionService {
  // CreateBlackBoxCaseRevision create a black box case
  rpc CreateBlackBoxCaseRevision(CreateBlackBoxCaseRevisionReq) returns (CreateBlackBoxCaseRevisionResp);

  // CreateBlackBoxCaseRevisionData create a black box case data
  rpc CreateBlackBoxCaseRevisionData(CreateBlackBoxCaseRevisionDataReq) returns (CreateBlackBoxCaseRevisionDataResp);

  // DeleteBlackBoxCaseRevision create a black box case
  rpc DeleteBlackBoxCaseRevision(DeleteBlackBoxCaseRevisionReq) returns (DeleteBlackBoxCaseRevisionResp);

  // UpdateBlackBoxCaseRevision create a black box case
  rpc UpdateBlackBoxCaseRevision(UpdateBlackBoxCaseRevisionReq) returns (UpdateBlackBoxCaseRevisionResp);

  // UpdateBlackBoxCaseRevisionWithCaseRefId update a black box case with case ref id
  rpc UpdateBlackBoxCaseRevisionWithCaseRefId(UpdateBlackBoxCaseRevisionWithCaseRefIdReq) returns (UpdateBlackBoxCaseRevisionWithCaseRefIdResp);

  // AdoptBlackBoxCaseRevision adopt a black box case
  rpc AdoptBlackBoxCaseRevision(AdoptBlackBoxCaseRevisionReq) returns (AdoptBlackBoxCaseRevisionResp);

  // GetBlackBoxCaseRevision gets a black box case
  rpc GetBlackBoxCaseRevision(GetBlackBoxCaseRevisionReq) returns (GetBlackBoxCaseRevisionResp);

  // ListBlackBoxCaseRevision list black box case
  rpc ListBlackBoxCaseRevision(ListBlackBoxCaseRevisionReq) returns (ListBlackBoxCaseRevisionResp);

  // GenerateBlackBoxCaseRevisionId list black box case
  rpc GenerateBlackBoxCaseRevisionId(GenerateBlackBoxCaseRevisionIdReq) returns (GenerateBlackBoxCaseRevisionIdResp);

  // GetBlackBoxCaseRevisionByRevisionId get black box case revision by revisionId
  rpc GetBlackBoxCaseRevisionByRevisionId(GetBlackBoxCaseRevisionByRevisionIdReq) returns (GetBlackBoxCaseRevisionByRevisionIdResp);
}

// BlackBoxCaseDataService
service BlackBoxCaseDataService {
  // CreateBlackBoxCaseData create a black box case
  rpc CreateBlackBoxCaseData(CreateBlackBoxCaseDataReq) returns (CreateBlackBoxCaseDataResp);

  // DeleteBlackBoxCaseData create a black box case
  rpc DeleteBlackBoxCaseData(DeleteBlackBoxCaseDataReq) returns (DeleteBlackBoxCaseDataResp);

  // UpdateBlackBoxCaseData create a black box case
  rpc UpdateBlackBoxCaseData(UpdateBlackBoxCaseDataReq) returns (UpdateBlackBoxCaseDataResp);

  // UpdateBlackBoxCaseData create a black box case
  rpc UpdateBlackBoxCaseDataOrder(UpdateBlackBoxCaseDataOrderReq) returns (UpdateBlackBoxCaseDataOrderResp);

  // BatchUpdateBlackBoxCaseData create a black box case
  rpc BatchUpdateBlackBoxCaseData(BatchUpdateBlackBoxCaseDataReq) returns (BatchUpdateBlackBoxCaseDataResp);

  // GetBlackBoxCaseData gets a black box case
  rpc GetBlackBoxCaseData(GetBlackBoxCaseDataReq) returns (GetBlackBoxCaseDataResp);

  // ListBlackBoxCaseData list black box case
  rpc ListBlackBoxCaseData(ListBlackBoxCaseDataReq) returns (ListBlackBoxCaseDataResp);

  // ListBlackBoxCaseDataV24 list black box case
  rpc ListBlackBoxCaseDataV24(ListBlackBoxCaseDataReq) returns (ListBlackBoxCaseDataResp);

  // KeepBlackBoxCaseData keep black box case
  rpc KeepBlackBoxCaseData(KeepBlackBoxCaseDataReq) returns (KeepBlackBoxCaseDataResp);

  // KeepBlackBoxCaseData keep black box case
  rpc ClearKeepBlackBoxCaseData(ClearKeepBlackBoxCaseDataReq) returns (ClearKeepBlackBoxCaseDataResp);

  // AppendBlackBoxCaseData append black box case
  rpc AppendBlackBoxCaseData(AppendBlackBoxCaseDataReq) returns (AppendBlackBoxCaseDataResp);

  // ReplaceBlackBoxCaseData replace black box case
  rpc ReplaceBlackBoxCaseData(ReplaceBlackBoxCaseDataReq) returns (ReplaceBlackBoxCaseDataResp);

  // ReplaceBlackBoxCaseData replace black box case
  rpc GenerateBlackBoxCaseRef(GenerateBlackBoxCaseRefReq) returns (GenerateBlackBoxCaseRefResp);

  // RefreshCaseRefMetrics replace black box case
  rpc RefreshCaseRefMetrics(RefreshCaseRefMetricsReq) returns (RefreshCaseRefMetricsResp);

  // ReorderBlackBoxCaseData list black box case
  rpc ReorderBlackBoxCaseData(ReorderBlackBoxCaseDataReq) returns (ReorderBlackBoxCaseDataResp);

  // ListBlackBoxCaseDataByCaseIds list black box case by caseIds
  rpc ListBlackBoxCaseDataByCaseIds(ListBlackBoxCaseDataByCaseIdsReq) returns (ListBlackBoxCaseDataByCaseIdsResp);
}

// BlackBoxCaseDirectoryService
service BlackBoxCaseDirectoryService {
  // ListBlackBoxCaseDirectories list blackbox case directories
  rpc ListBlackBoxCaseDirectories(ListBlackBoxCaseDirsReq) returns (ListBlackBoxCaseDirsResp);

  // CreateBlackBoxCaseDirectory create blackbox case directory
  rpc CreateBlackBoxCaseDirectory(CreateBlackBoxCaseDirReq) returns (CreateBlackBoxCaseDirResp);

  // DeleteBlackBoxCaseDirectory delete blackbox case directory
  rpc DeleteBlackBoxCaseDirectory(DeleteBlackBoxCaseDirReq) returns (DeleteBlackBoxCaseDirResp);
}

// BlackBoxCaseKnowledgeV2Service
service BlackBoxCaseKnowledgeV2Service {
  // GetBlackBoxCaseKnowledge get blackbox case knowledge
  rpc GetBlackBoxCaseKnowledge(GetBlackBoxCaseKnowledgeV2Req) returns (GetBlackBoxCaseKnowledgeV2Resp);

  // UpdateBlackBoxCaseKnowledge update blackbox case knowledge
  rpc UpdateBlackBoxCaseKnowledge(UpdateBlackBoxCaseKnowledgeV2Req) returns (UpdateBlackBoxCaseKnowledgeV2Resp);

  // UpdateBlackBoxCaseMapUsedKnowledge update blackbox case knowledge
  rpc UpdateBlackBoxCaseMapUsedKnowledge(UpdateBlackBoxCaseMapUsedKnowledgeReq) returns (UpdateBlackBoxCaseMapUsedKnowledgeResp);
}

// BlackBoxCaseMapService
service BlackBoxCaseMapService {
  // GetBlackBoxCaseMap get blackbox case map
  rpc GetBlackBoxCaseMap(GetBlackBoxCaseMapReq) returns (GetBlackBoxCaseMapResp);

  // UpdateBlackBoxCaseMap update blackbox case map
  rpc UpdateBlackBoxCaseMap(UpdateBlackBoxCaseMapReq) returns (UpdateBlackBoxCaseMapResp);

  // CreateBlackBoxCaseMap create blackbox case map
  rpc CreateBlackBoxCaseMap(CreateBlackBoxCaseMapReq) returns (CreateBlackBoxCaseMapResp);

  // GetCompleteBlackBoxCaseMap get complete blackbox case map
  rpc GetCompleteBlackBoxCaseMap(GetCompleteBlackBoxCaseMapReq) returns (GetCompleteBlackBoxCaseMapResp);

  // CreateBlackBoxCaseMapId create blackbox case map_id
  rpc CreateBlackBoxCaseMapId(CreateBlackBoxCaseMapIdReq) returns (CreateBlackBoxCaseMapIdResp);
}

// BlackBoxCaseMapDocumentService
service BlackBoxCaseMapDocumentService {
  // ListBlackBoxCaseMapDocuments list blackbox case map documents
  rpc ListBlackBoxCaseMapDocuments(ListBlackBoxCaseMapDocumentsReq) returns (ListBlackBoxCaseMapDocumentsResp);

  // GetBlackBoxCaseMapDocument get blackbox case map document
  rpc GetBlackBoxCaseMapDocument(GetBlackBoxCaseMapDocumentReq) returns (BlackBoxCaseMapDocument);

  // UpdateBlackBoxCaseMapDocument update blackbox case map document
  rpc UpdateBlackBoxCaseMapDocument(BlackBoxCaseMapDocument) returns (UpdateBlackBoxCaseMapDocumentResp);

  // DeleteBlackBoxCaseMapDocument delete blackbox case map document
  rpc DeleteBlackBoxCaseMapDocument(DeleteBlackBoxCaseMapDocumentReq) returns (DeleteBlackBoxCaseMapDocumentResp);

  // UpdateBlackBoxCaseMapRecallDocument update blackbox case map document
  rpc UpdateBlackBoxCaseMapRecallDocument(UpdateBlackBoxCaseMapRecallDocumentReq) returns (UpdateBlackBoxCaseMapRecallDocumentResp);
}

service BlackBoxGenerationRecordService {
  // AddBlackBoxFuncCount add blackbox func count
  rpc AddBlackBoxFuncCount(AddBlackBoxFuncCountReq) returns (AddBlackBoxFuncCountResp);

  // AddBlackBoxTestSceneCount add blackbox test scene count
  rpc AddBlackBoxTestSceneCount(AddBlackBoxTestSceneCountReq) returns (AddBlackBoxTestSceneCountResp);

  // AddBlackBoxSupplementDocumentCount add blackbox supplement document count
  rpc AddBlackBoxSupplementDocumentCount(AddBlackBoxSupplementDocumentCountReq) returns (AddBlackBoxSupplementDocumentCountResp);

  // AddBlackBoxBaseMapCount add blackbox base map count
  rpc AddBlackBoxBaseMapCount(AddBlackBoxBaseMapCountReq) returns (AddBlackBoxBaseMapCountResp);

  // AddBlackBoxUpdatedAddCaseCount add blackbox updated add case count
  rpc AddBlackBoxUpdatedAddCaseCount(AddBlackBoxUpdatedAddCaseCountReq) returns (AddBlackBoxUpdatedAddCaseCountResp);

  // AddBlackBoxCaseNameUpdatedCount add blackbox case name updated count
  rpc AddBlackBoxCaseNameUpdatedCount(AddBlackBoxCaseNameUpdatedCountReq) returns (AddBlackBoxCaseNameUpdatedCountResp);

  // AddBlackBoxPreConditionUpdatedCount add blackbox precondition updated count
  rpc AddBlackBoxPreConditionUpdatedCount(AddBlackBoxPreConditionUpdatedCountReq) returns (AddBlackBoxPreConditionUpdatedCountResp);

  // AddBlackBoxCaseStepUpdatedCount add blackbox case step updated count
  rpc AddBlackBoxCaseStepUpdatedCount(AddBlackBoxCaseStepUpdatedCountReq) returns (AddBlackBoxCaseStepUpdatedCountResp);

  // AddBlackBoxExpectResultUpdatedCount add blackbox expect result updated count
  rpc AddBlackBoxExpectResultUpdatedCount(AddBlackBoxExpectResultUpdatedCountReq) returns (AddBlackBoxExpectResultUpdatedCountReq);

  // AddBlackBoxCaseLevelUpdatedCount add blackbox case level updated count
  rpc AddBlackBoxCaseLevelUpdatedCount(AddBlackBoxCaseLevelUpdatedCountReq) returns (AddBlackBoxCaseLevelUpdatedCountReq);
}
