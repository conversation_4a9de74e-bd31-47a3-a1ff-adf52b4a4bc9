syntax = "proto3";

package dispatcher;

option go_package = "gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/rpc/pb";

import "validate/validate.proto";

message StabilityReportCallback {
	string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
	string task_id = 2 [(validate.rules).string = {pattern: "(?:^task_id:.+?)"}]; // 任务ID
	string execute_id = 3 [(validate.rules).string = {pattern: "(?:^execute_id:.+?)"}]; // 执行ID
}