syntax = "proto3";

package dispatcher;

option go_package = "gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/rpc/pb";

import "common/enum.proto";
import "manager/base.proto";
import "manager/component.proto";
import "dispatcher/base.proto";


enum CallbackType {
  CallbackType_UNKNOWN = 0;

  CallbackType_API_CASE = 1; // API测试用例
  CallbackType_API_SUITE = 2; // API测试集合
  CallbackType_INTERFACE_CASE = 3; // 接口用例
  CallbackType_INTERFACE_DOCUMENT = 4; // 接口文档（即接口集合）
  CallbackType_UI_CASE = 5; // UI测试用例
  CallbackType_UI_SUITE = 6; // UI测试集合
  CallbackType_API_SERVICE = 7; // API精准测试服务
  CallbackType_PERF_CASE = 8; // 压力测试用例
  CallbackType_PERF_SUITE = 9; // 压力测试集合
}

message CallbackReq {
  common.TriggerMode trigger_mode = 1; // 触发模式
  string trigger_rule = 2; // 触发规则
  string project_id = 3;  // 项目ID
  string task_id = 4; // 任务ID
  manager.ApiExecutionDataType execute_type = 5;  // 执行类型
  CallbackType callback_type = 6; // 回调类型
  common.PurposeType purpose_type = 7; // 计划用途

  oneof data {
    CaseCallbackData case = 31;
    SuiteCallbackData suite = 32;
    InterfaceCaseCallbackData interface_case = 33;
    InterfaceDocumentCallbackData interface_document = 34;
    UICaseCallbackData ui_case = 35;
    UISuiteCallbackData ui_suite = 36;
    ServiceCallbackData service = 37;
    PerfCaseCallbackData perf_case = 38;
    PerfSuiteCallbackData perf_suite = 39;
  }

  bool debug = 99;
}

// SuiteCallbackData 集合回调
message SuiteCallbackData {
  string plan_id = 1; // 计划ID
  string plan_execute_id = 2; // 计划执行ID
  string suite_id = 3; // 回调的集合ID
  string suite_execute_id = 4; // 回调的集合执行ID
  ComponentState suite_state = 5; // 回调的集合执行状态
}

// InterfaceDocumentCallbackData 接口文档回调
message InterfaceDocumentCallbackData {
  string plan_id = 1; // 计划ID
  string plan_execute_id = 2; // 计划执行ID
  string interface_id = 3;  // 回调的接口文档ID
  string interface_execute_id = 4; // 回调的集合文档执行ID
  ComponentState interface_state = 5; // 回调的接口文档执行状态
}

// InterfaceCaseCallbackData 接口用例回调
message InterfaceCaseCallbackData {
  string interface_id = 1; // 接口ID
  string interface_execute_id = 2; // 接口执行ID
  string interface_case_id = 3; // 回调的接口用例ID
  string interface_case_execute_id = 4; // 回调的接口用例执行ID
  ComponentState interface_case_state = 5; // 回调的接口用例执行状态
  string version = 6; // 版本
}

// CaseCallbackData 用例回调
message CaseCallbackData {
  string suite_id = 1; // 集合ID
  string suite_execute_id = 2; // 集合执行ID
  string case_id = 3; // 用例ID
  string case_execute_id = 4; // 用例执行ID
  ComponentState case_state = 5; // 用例执行状态
  string version = 6; // 版本
}

// UICaseCallbackData UI用例回调
message UICaseCallbackData {
  string ui_plan_id = 1;  // UI计划ID
  string ui_plan_execute_id = 2; // UI计划执行ID
  string ui_suite_id = 3; // UI集合ID
  string ui_suite_execute_id = 4; // UI集合执行ID
  string ui_case_id = 5; // 回调的UI用例ID
  string ui_case_execute_id = 6; // 回调的UI用例执行ID
  ComponentState case_state = 7;  // 回调的UI用例执行状态
  manager.UIPlanMetaData meta_data = 8; // UI计划元数据
}

// UISuiteCallbackData UI集合回调
message UISuiteCallbackData {
  string ui_plan_id = 1; // UI计划ID
  string ui_plan_execute_id = 2; // UI计划执行ID
  string ui_suite_id = 3; // 回调的UI集合ID
  string ui_suite_execute_id = 4; // 回调的UI集合执行ID
  ComponentState suite_state = 5; // 回调的UI集合执行状态
  manager.UIPlanMetaData meta_data = 6; // UI计划元数据
}

message ServiceCallbackData {
  string plan_id = 1; // 计划ID
  string plan_execute_id = 2; // 计划执行ID
  string service_id = 3; // 回调的服务ID
  string service_execute_id = 4; // 回调的服务执行ID
  ComponentState service_state = 5; // 集合执行状态
}

// PerfCaseCallbackData 压测用例回调
message PerfCaseCallbackData {
  string perf_plan_id = 1;  // 压力计划ID
  string perf_plan_execute_id = 2; // 压力计划执行ID
  string perf_suite_id = 3; // 压力集合ID
  string perf_suite_execute_id = 4; //压力集合执行ID
  string perf_case_id = 5; // 回调的压力用例ID
  string perf_case_execute_id = 6; // 回调的压力用例执行ID
  ComponentState case_state = 7;  // 回调的压力用例执行状态
  manager.PerfPlanMetaData meta_data = 8; // 压力计划元数据
}

// PerfSuiteCallbackData 集合回调
message PerfSuiteCallbackData {
  string perf_plan_id = 1; // 压力计划ID
  string perf_plan_execute_id = 2; // 压力计划执行ID
  string perf_suite_id = 3; // 回调的压力集合ID
  string perf_suite_execute_id = 4; // 回调的压力集合执行ID
  ComponentState suite_state = 5; // 回调的压力集合执行状态
  manager.PerfPlanMetaData meta_data = 6; // 压力计划元数据
}
