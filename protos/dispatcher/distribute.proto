syntax = "proto3";

package dispatcher;

option go_package = "gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/rpc/pb";

import "common/config.proto";
import "common/enum.proto";
import "manager/base.proto";
import "manager/manager.proto";
import "manager/component.proto";
import "dispatcher/base.proto";


enum DistributeType {
  DistributeType_UNKNOWN = 0;

  DistributeType_API_SUITE = 1; // API测试集合
  DistributeType_API_PLAN = 2; // API测试计划
  DistributeType_INTERFACE_DOCUMENT = 3; // 接口文档（即接口集合）
  DistributeType_UI_SUITE = 4; // UI测试集合
  DistributeType_UI_PLAN = 5; // UI测试计划
  DistributeType_API_SERVICE = 6; // 精准测试服务
  DistributeType_PERF_PLAN = 7; // 压力测试计划
  DistributeType_PERF_SUITE = 8; // 压力测试集合
}

message DistributeReq {
  common.TriggerMode trigger_mode = 1; // 触发模式
  string trigger_rule = 2; // 触发规则
  string project_id = 3; // 项目ID
  string task_id = 4; // 任务ID
  manager.ApiExecutionDataType execute_type = 5; // 执行类型
  DistributeType distribute_type = 6; // 分发类型
  common.GeneralConfig general_config = 7; // 通用配置
  repeated common.AccountConfig account_config = 8; // 池账号配置
  string user = 9; // 用户
  string user_id = 10; // 用户ID
  common.PurposeType purpose_type = 11; // 计划用途
	common.PriorityType  priority_type = 12; // 优先级

  oneof data {
      PlanDistributeData plan = 31;
      SuiteDistributeData suite = 32;
      InterfaceDocumentDistributeData interface_document = 33;
      UIPlanDistributeData ui_plan = 34;
      UISuiteDistributeData ui_suite = 35;
      ServiceDistributeData service = 36;
      PerfPlanDistributeData perf_plan = 37;
      PerfSuiteDistributeData perf_suite = 38;
  }
  bool debug = 99;
}

message PlanDistributeData {
  string plan_id = 1; // 计划ID
  string plan_execute_id = 2; // 计划执行ID
  ComponentState state = 3; // 当前状态
  manager.PlanComponent plan = 4; // 计划信息
  repeated manager.ApiExecutionData suites = 5; // 其下集合
  repeated manager.ApiExecutionData interface_document = 6; // 其下接口文档
  repeated manager.ApiExecutionData services = 7; // 其下精准测试服务
}

message SuiteDistributeData {
  string suite_id = 1; // 集合ID
  string suite_execute_id = 2; // 集合执行ID
  string plan_id = 3; // 计划ID
  string plan_execute_id = 4; // 计划执行ID
  ComponentState state = 5; // 集合状态
  manager.SuiteComponent suite = 6; // 集合信息
  repeated manager.ApiExecutionData cases = 7; // 其下执行用例

  string plan_name = 11; // 计划名称
}

message InterfaceDocumentDistributeData {
  string interface_document_id = 1; // 接口文档ID
  string interface_document_execute_id = 2; // 接口文档执行ID
  string plan_id = 3; // 计划ID
  string plan_execute_id = 4; // 计划执行ID
  ComponentState state = 5; // 当前状态
  manager.InterfaceDocumentComponent interface_document = 6; // 接口文档信息
  repeated manager.ApiExecutionData interface_cases = 7; // 其下执行接口用例

  string plan_name = 11; // 计划名称
}

message UIPlanDistributeData {
  string ui_plan_id = 1; // 计划id
  string ui_plan_execute_id = 2; // 计划执行id
  ComponentState state = 3;  // 当前状态
  manager.UIPlanComponent ui_plan = 4; // 计划信息
  repeated manager.ApiExecutionData ui_suites = 5; // 其下集合
  manager.UIPlanMetaData meta_data = 6; // ui用例数据
}

message UISuiteDistributeData {
  string ui_suite_id = 1; // 集合id
  string ui_suite_execute_id = 2; // 集合执行id
  string ui_plan_id = 3; // 计划id
  string ui_plan_execute_id = 4; // 计划执行id
  ComponentState state = 5;  // 集合状态
  manager.UISuiteComponent ui_suite = 6; // 集合信息
  repeated manager.ApiExecutionData ui_cases = 7;  // 其下执行用例
  manager.UIPlanMetaData meta_data = 8; // ui用例数据
}

// ServiceDistributeData 精准测试服务
message ServiceDistributeData {
  string service_id = 1; // 服务ID
  string service_execute_id = 2; // 服务执行ID
  string service_name = 3; // 服务名称
  string plan_id = 4; // 计划ID
  string plan_execute_id = 5; // 计划执行ID
  manager.ServiceComponent service = 6; // 集合信息
  repeated manager.ApiExecutionData cases = 7; // 其下执行用例
  repeated manager.ApiExecutionData interface_cases = 8; // 其下执行接口用例

  string plan_name = 11; // 计划名称
}

message PerfPlanDistributeData {
  string perf_plan_id = 1; // 计划ID
  string perf_plan_execute_id = 2; // 计划执行ID

  ComponentState state = 11;  // 计划状态
  manager.PerfPlanComponent perf_plan = 12; // 计划数据
  repeated manager.ApiExecutionData perf_suites = 13; // 集合数据
//  manager.PerfPlanMetaData meta_data = 6; // 压力用例数据

  PerfPlanInfo perf_plan_info = 21;  // 计划执行信息
}

message PerfSuiteDistributeData {
  string perf_suite_id = 1; // 集合ID
  string perf_suite_execute_id = 2; // 集合执行ID
  string perf_plan_id = 3; // 计划ID
  string perf_plan_execute_id = 4; // 计划执行ID

  ComponentState state = 11;  // 集合状态
  manager.PerfSuiteComponent perf_suite = 12; // 集合数据
  repeated manager.ApiExecutionData perf_cases = 13;  // 用例数据
  manager.PerfPlanMetaData meta_data = 14; // 计划元数据

  PerfPlanInfo perf_plan_info = 21; // 计划执行信息
//  manager.PerfPlanComponent perf_plan = 10; // 计划数据
}
