syntax = "proto3";

package dispatcher;

option go_package = "gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/rpc/pb";

import "dispatcher/base.proto";
import "common/perf.proto";


message PerfReportCallback {
  message PerfCase {
    string suite_name = 1; // 集合名称
    string case_name = 2; // 用例名称
    int64 target_rps = 3; // 目标的RPS

    repeated uint32 cmds = 11; // 涉及的命令号（`TT`专用）
  }

  string project_id = 1;  // 项目ID
  string task_id = 2; // 任务ID
  string plan_id = 3; // 计划ID
  string plan_execute_id = 4; // 计划执行ID

  StageType stage = 11; // 阶段
  repeated PerfCase cases = 12; // 用例列表
  repeated common.PerfServiceMetaData services = 13; // 计划涉及的服务的元数据（`提前阶段`专用）
}
