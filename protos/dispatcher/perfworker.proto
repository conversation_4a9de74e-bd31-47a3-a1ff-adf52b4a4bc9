syntax = "proto3";

package dispatcher;

option go_package = "gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/rpc/pb";

import "validate/validate.proto";

import "common/config.proto";
import "common/enum.proto";
import "common/perf.proto";
import "common/task.proto";
import "dispatcher/callback.proto";
import "dispatcher/worker.proto";
import "manager/base.proto";


message PerfTestTaskInfo {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?)"}]; // 项目ID
  string plan_id = 2 [(validate.rules).string = {pattern: "(?:^perf_plan_id:.+?)"}]; // 计划ID
  string task_id = 3 [(validate.rules).string = {pattern: "(?:^task_id:.+?)"}]; // 任务ID
  string execute_id = 4 [(validate.rules).string = {pattern: "(?:^execute_id:.+?)"}]; // 执行ID

  string test_target = 11; // Deprecated: 测试目标，可以是任意级别的，例如集合/用例
  string test_framework_url = 12 [(validate.rules).string = {max_len: 255}]; // Deprecated: 测试框架git地址，带token
  string test_framework_branch = 13 [(validate.rules).string = {max_len: 255}]; // Deprecated: 测试框架分支名称

  common.Protocol protocol = 21 [(validate.rules).enum = {not_in: [0]}]; // 协议
  common.TargetEnvironment target_env = 22; // 目标环境
  common.ProtobufTarget protobuf_target = 23; // Deprecated: Protobuf相关信息
  common.GeneralConfig general_config = 24 [(validate.rules).message = {required: true}]; // 通用配置

  uint32 duration = 27 [(validate.rules).uint32 = {gte: 0}]; // 压测时长
  uint32 times = 28 [(validate.rules).uint32 = {gte: 0}]; // 压测次数

  common.TriggerMode trigger_mode = 31;
  string trigger_rule = 32;
  manager.ApiExecutionDataType execute_type = 33;
  CallbackType callback_type = 34;
  PerfCaseWorkerInfo perf_case = 35; // 压测测试用例基础信息
  common.PerfKeepalive keepalive = 36 [(validate.rules).message = {skip: true}]; // Deprecated: 压测保活参数
  common.PerfRateLimits rate_limits = 37 [(validate.rules).message = {required: true}]; // 压测相关的限流配置（包括：认证接口、心跳接口）
}

message FinalHandleTaskInfo {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?)"}]; // 项目ID
  string plan_id = 2 [(validate.rules).string = {pattern: "(?:^perf_plan_id:.+?)"}]; // 计划ID
  string task_id = 3 [(validate.rules).string = {pattern: "(?:^task_id:.+?)"}]; // 任务ID
  string execute_id = 4 [(validate.rules).string = {pattern: "(?:^execute_id:.+?)"}]; // 执行ID
}
