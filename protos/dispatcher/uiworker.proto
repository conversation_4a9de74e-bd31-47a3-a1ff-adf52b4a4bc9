syntax = "proto3";

package dispatcher;

option go_package = "gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/rpc/pb";

import "validate/validate.proto";

import "common/enum.proto";
import "manager/base.proto";
import "dispatcher/callback.proto";
import "dispatcher/worker.proto";


message UITestTaskInfo {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?)"}]; // 项目ID
  string plan_id = 2 [(validate.rules).string = {pattern: "(?:^ui_plan_id:.+?)"}]; // ui计划ID
  string task_id = 3 [(validate.rules).string = {pattern: "(?:^task_id:.+?)"}]; // 任务ID

  // UI测试任务基础信息
  string testTarget = 11; // 测试目标，可以是任意级别的，例如集合/用例
  string test_framework_url = 12 [(validate.rules).string = {max_len: 255}]; // 测试框架git地址，带token
  string test_framework_branch = 13 [(validate.rules).string = {max_len: 255}]; // 测试框架分支名称
  common.DeviceType device_type = 14 [(validate.rules).enum.defined_only = true]; // 设备类型（真机、云手机）
  common.PlatformType platform_type = 15 [(validate.rules).enum.defined_only = true]; // 平台类型（Android、iOS）
  string app_download_link = 16 [(validate.rules).string = {ignore_empty: true, max_len: 255}]; // APP下载地址
  common.TestLanguage test_language = 17 [(validate.rules).enum = {in: [1, 2]}]; // 测试语言
  string test_language_version = 18 [(validate.rules).string = {max_len: 50}]; // 测试语言版本
  common.TestFramework test_framework = 19 [(validate.rules).enum = {in: [1]}]; // 测试框架
  repeated string test_args = 20 [(validate.rules).repeated = {ignore_empty: true}]; // 附加参数
  repeated string devices = 21 [(validate.rules).repeated = {ignore_empty: true}]; // 选择的设备编号
  string package_name = 22 [(validate.rules).string = {max_len: 255}]; // 包名，用于启动app
  string app_version = 23 [(validate.rules).string = {ignore_empty: true, max_len: 255}]; // APP版本

  // 回调所需参数
  common.TriggerMode trigger_mode = 31;
  string trigger_rule = 32;
  manager.ApiExecutionDataType execute_type = 33;
  CallbackType callback_type = 34;
  UICaseWorkerInfo ui_case = 35;

  // 任务执行参数
	common.PriorityType  priority_type = 41; // 优先级

  // 其它参数
  string executed_by = 51; // 执行人的用户ID
}

// 理应有报告类型，例如Allure，没空加~
message GenerateReportTaskInfo {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?)"}]; // 项目ID
  string plan_id = 2 [(validate.rules).string = {pattern: "(?:^ui_plan_id:.+?)"}]; // ui计划ID
  string task_id = 3 [(validate.rules).string = {pattern: "(?:^task_id:.+?)"}]; // 任务ID

  // 回调所需参数
  UICaseWorkerInfo ui_case = 4;
}
