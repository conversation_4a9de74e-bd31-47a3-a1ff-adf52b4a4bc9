syntax = "proto3";

package account;

option go_package = "gitlab.ttyuyin.com/TestDevelopment/probe-backend/account/rpc/pb";

import "validate/validate.proto";

import "sqlbuilder/search.proto";

service Account {
  rpc QueryAccountPoolEnvData(QueryAccountPoolEnvDataRequest) returns (QueryAccountPoolEnvDataResponse);
  rpc ReleaseTestAccount(ReleaseTestAccountRequest) returns (ReleaseTestAccountResponse);
}

message QueryAccountPoolEnvDataRequest {
  string pool_env_table = 1 [(validate.rules).string = {min_len: 1}]; // 真实的池账号环境表名，如"t_tt_1637584556169"
  sqlbuilder.Condition condition = 2; // 筛选条件
  repeated string selected_column_id_array = 3 [(validate.rules).repeated = {ignore_empty: true, unique: true, items: {string: {min_len: 1}}}]; // 筛选的字段列表，不传递则表示只筛选account和password字段
  int64 expected_count = 4 [(validate.rules).int64 = {gte: 1, lte: 1000}]; // 预期筛选数量，正整数，不传递默认为1
  int64 allow_less_than_expected_count = 5 [(validate.rules).int64 = {gte: 0}]; // 是否允许查询数据调数小于筛选数量， 1为允许，2为不允许，默认为不允许
}

message QueryAccountPoolEnvDataResponse {
  message Column {
     string field = 1;  // 字段名称
     string value = 2;  // 字段值
     string lock_value = 3; // redis锁值
     ColumnType column_type = 4; // 字段类型
  }
  message Account {
    repeated Column account = 1;
  }
  int64 expected_count = 1; // 预期筛选数量
  int64 match_count = 2;  // 实际筛选到的数据
  repeated Account match_data = 3;  // 实际筛选到数据
}

enum ColumnType {
  UNKNOWN = 0;  // 留空
  VARCHAR = 1;  // 对应go的string
  INT = 2;  // 对应go的int64
  FLOAT = 3;  // 对应go的float
  TINYINT = 4;  // 对应go的int64
  DATETIME = 5; // 对应go的string，19位的字符串表示时间，如"2021-01-01 01:01:01"
  TIMESTAMP = 6;  // 对应go的int64，10位时间戳数字， 如**********，等价表示"1970-01-20 11:20:42"
}

message ReleaseTestAccountRequest {
  message Account {
    string account = 1 [(validate.rules).string = {min_len: 1}];
    string lock_value = 2 [(validate.rules).string = {ignore_empty: true, min_len: 1}];
  }
  message PoolAccount {
    string pool_env_table = 1 [(validate.rules).string = {min_len: 1}]; // 真实的池账号环境表名，如"t_tt_1637584556169"
    repeated Account account_array = 2 [(validate.rules).repeated = {max_items: 1000, items: {message: {required: true}}}]; // 池账号account字段列表，如["*********", "*********", "*********"]
  }
  repeated PoolAccount release_tas_account_array = 1 [(validate.rules).repeated = {max_items: 10, items: {message: {required: true}}}]; // 被集合引用的真实tas测试账号数据
}

message ReleaseTestAccountResponse {
}
