syntax = "proto3";

package configurator;

option go_package = "gitlab.ttyuyin.com/TestDevelopment/probe-backend/configurator/rpc/pb";

import "validate/validate.proto";

import "sqlbuilder/search.proto";


message Namespace {
  string name = 1;
  string description = 2;
  string created_by = 3;
  string updated_by = 4;
  int64 created_at = 5;
  int64 updated_at = 6;
}

message Item {
  string namespace = 1;
  string key = 2;
  string value = 3;
  string description = 4;
  string created_by = 5;
  string updated_by = 6;
  int64 created_at = 7;
  int64 updated_at = 8;
}

message CreateNamespaceReq {
  string name = 1 [(validate.rules).string = {min_len: 1, max_len: 64}];
  string description = 2 [(validate.rules).string = {ignore_empty: true, max_len: 255}];
}
message CreateNamespaceResp {
  Namespace namespace = 1;
}

message SearchNamespaceReq {
  sqlbuilder.Condition condition = 1; // 查询条件
  sqlbuilder.Pagination pagination = 2; // 查询分页
  repeated sqlbuilder.SortField sort = 3 [(validate.rules).repeated.ignore_empty = true]; // 查询排序
}
message SearchNamespaceResp {
  uint64 current_page = 1;
  uint64 page_size = 2;
  uint64 total_count = 3;
  uint64 total_page = 4;
  repeated Namespace items = 5;
}

message ViewNamespaceReq {
  string name = 1 [(validate.rules).string = {min_len: 1, max_len: 64}];
}
message ViewNamespaceResp {
  Namespace namespace = 1;
}

message CreateItemReq {
  string namespace = 1 [(validate.rules).string = {min_len: 1, max_len: 64}];
  string key = 2 [(validate.rules).string = {min_len: 1, max_len: 64}];
  string value = 3;
  string description = 4 [(validate.rules).string = {ignore_empty: true, max_len: 255}];
}
message CreateItemResp {
  Item item = 1;
}

message ModifyItemReq {
  string namespace = 1 [(validate.rules).string = {min_len: 1, max_len: 64}];
  string key = 2 [(validate.rules).string = {min_len: 1, max_len: 64}];
  string value = 3;
  string description = 4 [(validate.rules).string = {ignore_empty: true, max_len: 255}];
}
message ModifyItemResp {
  Item item = 1;
}

message GetItemReq {
  string namespace_id = 1;
  string item_key = 2;
}
message GetItemResp {
  Item item = 1;
}


//ConfigurationService 配置服务
service ConfigurationService {
  //CreateNamespace 创建命名空间
  rpc CreateNamespace(CreateNamespaceReq) returns (CreateNamespaceResp);
  //SearchNamespace 搜索命名空间
  rpc SearchNamespace(SearchNamespaceReq) returns (SearchNamespaceResp);
  //ViewNamespace 查看命名空间
  rpc ViewNamespace(ViewNamespaceReq) returns (ViewNamespaceResp);

  //CreateItem 创建配置项
  rpc CreateItem(CreateItemReq) returns (CreateItemResp);
  //ModifyItem 编辑配置项
  rpc ModifyItem(ModifyItemReq) returns (ModifyItemResp);
  //GetItem 获取配置项
  rpc GetItem(GetItemReq) returns (GetItemResp);
}
