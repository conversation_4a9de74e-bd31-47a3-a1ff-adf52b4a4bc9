syntax = "proto3";

package larkproxy;

option go_package = "gitlab.ttyuyin.com/TestDevelopment/probe-backend/larkproxy/rpc/pb";


message UserContactInfo {
  string user_id = 1;    // 用户ID
  string mobile = 2;     // 手机号
  string email = 3;      // 邮箱
  UserStatus status = 4; // 用户状态
}

message UserStatus {
  bool is_frozen = 1;    // 是否暂停
  bool is_resigned = 2;  // 是否离职
  bool is_activated = 3; // 是否激活
  bool is_exited = 4;    // 是否主动退出
  bool is_unjoin = 5;    // 是否未加入
}
