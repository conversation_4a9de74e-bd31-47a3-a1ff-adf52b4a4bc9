syntax = "proto3";

package larkproxy;

option go_package = "gitlab.ttyuyin.com/TestDevelopment/probe-backend/larkproxy/rpc/pb";

import "buf/validate/validate.proto";

import "larkproxy/contact_user.proto";
import "larkproxy/im_chat_members.proto";
import "larkproxy/im_chats.proto";
import "larkproxy/im_chat_managers.proto";


//LarkContactUserService 飞书用户服务
service LarkContactUserService {
  rpc GetBatchUserID(GetBatchUserIDReq) returns (GetBatchUserIDResp);
}

message GetBatchUserIDReq {
  repeated string emails = 1 [(buf.validate.field).repeated = {min_items: 1, unique: true, items: {string: {email: true}}}]; // 邮箱
}
message GetBatchUserIDResp {
 repeated UserContactInfo items = 1;
}


//LarkIMChatsService 飞书群组服务
service LarkIMChatsService {
  //CreateChat 创建群
  rpc CreateChat(CreateChatReq) returns (CreateChatResp);
  //DeleteChat 解散群
  rpc DeleteChat(DeleteChatReq) returns (DeleteChatResp);
  //GetChat 获取群信息
  rpc GetChat(GetChatReq) returns (GetChatResp);
  //ListChat 获取用户或机器人所在的群列表
  rpc ListChat(ListChatReq) returns (ListChatResp);
}

message CreateChatReq {
  string uuid = 1 [(buf.validate.field).string = {max_len: 50}]; // 用于创建群组请求去重

  string name = 11 [(buf.validate.field).string = {min_len: 2, max_len: 60}]; // 群名称
  string description = 12 [(buf.validate.field).string = {max_len: 100}]; // 群描述
  repeated string user_id_list = 13 [(buf.validate.field).repeated = {min_items: 0, items: {string: {pattern: "^ou_[a-z0-9]{32}$"}}}]; // 创建群时邀请的群成员
}
message CreateChatResp {
  CreateChatRespData data = 1;
}

message DeleteChatReq {
  string chat_id = 1 [(buf.validate.field).string = {pattern: "^oc_[a-z0-9]{32}$"}]; // 群ID
}
message DeleteChatResp {}

message GetChatReq {
  string chat_id = 1 [(buf.validate.field).string = {pattern: "^oc_[a-z0-9]{32}$"}]; // 群ID
}
message GetChatResp {
  GetChatRespData data = 1;
}

message ListChatReq {
  bool with_current_user = 1; // 是否需要当前用户在群里
}
message ListChatResp {
  repeated ListChatRespData items = 1;
}


//LarkIMChatMembersService 飞书群组成员服务
service LarkIMChatMembersService {
  //CreateChatMembers 将用户或机器人拉入群聊
  rpc CreateChatMembers(CreateChatMembersReq) returns (CreateChatMembersResp);
  //DeleteChatMembers 将用户或机器人移出群聊
  rpc DeleteChatMembers(DeleteChatMembersReq) returns (DeleteChatMembersResp);
  //GetChatMembers 获取群成员列表
  rpc GetChatMembers(GetChatMembersReq) returns (GetChatMembersResp);
}

message CreateChatMembersReq {
  string chat_id = 1 [(buf.validate.field).string = {pattern: "^oc_[a-z0-9]{32}$"}]; // 群ID
  repeated string user_id_list = 2 [(buf.validate.field).repeated = {min_items: 1, items: {string: {pattern: "^ou_[a-z0-9]{32}$"}}}]; // 邀请的群成员
}
message CreateChatMembersResp {
  CreateChatMembersRespData data = 1;
}

message DeleteChatMembersReq {}
message DeleteChatMembersResp {}

message GetChatMembersReq {
  string chat_id = 1 [(buf.validate.field).string = {pattern: "^oc_[a-z0-9]{32}$"}]; // 群ID
}
message GetChatMembersResp {
  repeated GetChatMembersRespData items = 1;
}

//LarkIMChatManagersService 飞书群管理员服务
service LarkIMChatManagersService {
  //CreateChatMembers 指定群管理员
  rpc CreateChatManagers(CreateChatManagersReq) returns (CreateChatManagersResp);
}

message CreateChatManagersReq {
  string chat_id = 1 [(buf.validate.field).string = {pattern: "^oc_[a-z0-9]{32}$"}]; // 群ID
  repeated string manager_id_list = 2 [(buf.validate.field).repeated = {min_items: 1, items: {string: {pattern: "^ou_[a-z0-9]{32}$"}}}]; // 指定的群管理员
}
message CreateChatManagersResp {
  CreateChatManagersRespData data = 1;
}