syntax = "proto3";

package larkproxy;

option go_package = "gitlab.ttyuyin.com/TestDevelopment/probe-backend/larkproxy/rpc/pb";


message CreateChatMembersRespData {
  repeated string invalid_id_list = 1; // 无效成员列表
  repeated string not_existed_id_list = 2; // ID不存在的成员列表
  repeated string pending_approval_id_list = 3; // 等待群主或管理员审批的成员ID列表
}

message GetChatMembersRespData {
  string member_id_type = 1; // 成员的用户ID类型
  string member_id = 2; // 成员的用户ID
  string name = 3; // 成员的名字
}
