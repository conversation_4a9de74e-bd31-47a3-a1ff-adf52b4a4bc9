syntax = "proto3";

package larkproxy;

option go_package = "gitlab.ttyuyin.com/TestDevelopment/probe-backend/larkproxy/rpc/pb";


message CreateChatRespData {
  string chat_id = 1; // 群组ID
  string avatar = 2; // 群头像URL
  string name = 3; // 群名称
  string description = 4; // 群描述
}

message GetChatRespData {
  string chat_id = 1; // 群组ID
  string avatar = 2; // 群头像URL
  string name = 3; // 群名称
  string description = 4; // 群描述
  bool external = 5; // 是否是外部群
  // @gotags: copier:"ChatStatus"
  string status = 6; // 群状态
}

message ListChatRespData {
  string chat_id = 1; // 群组ID
  string avatar = 2; // 群头像URL
  string name = 3; // 群名称
  string description = 4; // 群描述
  bool external = 5; // 是否是外部群
  // @gotags: copier:"ChatStatus"
  string status = 6; // 群状态
}
