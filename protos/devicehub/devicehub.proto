syntax = "proto3";

package devicehub;

option go_package = "gitlab.ttyuyin.com/TestDevelopment/probe-backend/devicehub/rpc/pb";

import "buf/validate/validate.proto";

import "sqlbuilder/search.proto";
import "common/enum.proto";
import "devicehub/device.proto";


//DeviceService 设备服务
service DeviceService {
  //CreateOrModifyDevice 创建或编辑设备
  rpc CreateOrModifyDevice(CreateOrModifyDeviceReq) returns (CreateOrModifyDeviceResp);
  //ModifyDeviceByProviderOffline 编辑下线的`provider`管理的设备
  rpc ModifyDeviceByProviderOffline(ModifyDeviceByProviderOfflineReq) returns (ModifyDeviceByProviderOfflineResp);
  //GetDevice 获取设备（通过`udid`）
  rpc GetDevice(GetDeviceReq) returns (GetDeviceResp);
  //SearchDevice 搜索设备
  rpc SearchDevice(SearchDeviceReq) returns (SearchDeviceResp);
  //AcquireDevice 占用设备（通过`udid`）
  rpc AcquireDevice(AcquireDeviceReq) returns (AcquireDeviceResp);
  //SearchAcquireDevice 占用设备（通过查询条件）
  rpc SearchAcquireDevice(SearchAcquireDeviceReq) returns (SearchAcquireDeviceResp);
  //ReleaseDevice 释放设备（通过`udid`）
  rpc ReleaseDevice(ReleaseDeviceReq) returns (ReleaseDeviceResp);
}

message CreateOrModifyDeviceReq {
  string udid = 1 [(buf.validate.field).string = {min_len: 1, max_len: 64}]; // 设备编号
  string name = 2 [(buf.validate.field) = {ignore: IGNORE_IF_UNPOPULATED, string: {min_len: 1, max_len: 64}}]; // 设备名称
  common.DeviceType type = 3 [(buf.validate.field).cel = {
    id: "type",
    message: "device type must be specified",
    // Validate that type is specified.
    // Note that `this`, as a enum, can be directly compared with an int.
    // In this case, 0 is the variant DT_NULL from the definition below.
    expression: "this != 0"
  }]; // 设备类型
  common.PlatformType platform = 4 [(buf.validate.field).cel = {
    id: "platform",
    message: "device platform must be specified",
    // Validate that type is specified.
    // Note that `this`, as a enum, can be directly compared with an int.
    // In this case, 0 is the variant PT_NULL from the definition below.
    expression: "this != 0"
  }]; // 平台
  string brand = 5 [(buf.validate.field) = {ignore: IGNORE_IF_UNPOPULATED, string: {min_len: 1, max_len: 64}}]; // 品牌
  string model = 6 [(buf.validate.field) = {ignore: IGNORE_IF_UNPOPULATED, string: {min_len: 1, max_len: 64}}]; // 型号
  string serial = 7 [(buf.validate.field) = {ignore: IGNORE_IF_UNPOPULATED, string: {min_len: 1, max_len: 64}}]; // 序列号
  string version = 8 [(buf.validate.field) = {ignore: IGNORE_IF_UNPOPULATED, string: {min_len: 1, max_len: 64}}]; // 版本
  string provider = 9 [(buf.validate.field) = {ignore: IGNORE_IF_UNPOPULATED, string: {min_len: 1, max_len: 128}}]; // 设备所属
  ProviderType provider_type = 10 [(buf.validate.field).cel = {
    id: "provider_type",
    message: "provider type must be specified",
    // Validate that type is specified.
    // Note that `this`, as a enum, can be directly compared with an int.
    // In this case, 0 is the variant PT_NULL from the definition below.
    expression: "this != 0"
  }]; // 设备所属类型
  string remote_address = 11 [(buf.validate.field) = {ignore: IGNORE_IF_UNPOPULATED, string: {min_len: 1, max_len: 128}}]; // 远程连接地址
  DeviceState state = 12 [(buf.validate.field).cel = {
    id: "state",
    message: "state must be specified",
    expression: "this != 0"
  }]; // 状态
  DeviceMetadata metadata = 13 [(buf.validate.field) = {}]; // 设备元数据

  option (buf.validate.message).cel = {
    id: "device_metadata_with_cph_address",
    message: "the device metadata must have `cph_address` when it is a HUAWEI Cloud Phone of Android",
    expression:
        "this.type == 2 && this.platform == 1 && this.brand == 'HUAWEICLOUD' "
        "? (has(this.metadata.cph_address) ? '' : 'HUAWEI Cloud Phone must have `cph_address`') "
        ": ''"
  };
}
message CreateOrModifyDeviceResp {}

message ModifyDeviceByProviderOfflineReq {
  string provider = 1 [(buf.validate.field).string = {min_len: 1, max_len: 128}]; // 设备所属
  ProviderType provider_type = 2 [(buf.validate.field).cel = {
    id: "provider_type",
    message: "provider type must be specified",
    // Validate that type is specified.
    // Note that `this`, as a enum, can be directly compared with an int.
    // In this case, 0 is the variant PT_NULL from the definition below.
    expression: "this != 0"
  }]; // 设备所属类型
}
message ModifyDeviceByProviderOfflineResp {}

message GetDeviceReq {
  string udid = 1 [(buf.validate.field).string = {min_len: 1, max_len: 64}];
}
message GetDeviceResp {
  Device device = 1;
}

message SearchDeviceReq {
  sqlbuilder.Condition condition = 1; // 查询条件
  sqlbuilder.Pagination pagination = 2; // 查询分页
  repeated sqlbuilder.SortField sort = 3 [(buf.validate.field).repeated.items.ignore = IGNORE_IF_UNPOPULATED]; // 查询排序
}
message SearchDeviceResp {
  uint64 current_page = 1;
  uint64 page_size = 2;
  uint64 total_count = 3;
  uint64 total_page = 4;
  repeated Device items = 5;
}

message AcquireDeviceReq {
  string udid = 1 [(buf.validate.field).string = {min_len: 1, max_len: 64}]; // 设备编号
  int64 expiration = 2 [(buf.validate.field).int64.gte = 0]; // 最大占用时长（秒）
}
message AcquireDeviceResp {
  Device device = 1;
}

message SearchAcquireDeviceReq {
  sqlbuilder.Condition condition = 1; // 查询条件
  uint32 count = 2 [(buf.validate.field).uint32.gte = 1]; // 申请占用的设备数量
  int64 expiration = 3 [(buf.validate.field).int64.gte = 0]; // 最大占用时长（秒）
}
message SearchAcquireDeviceResp {
  repeated Device devices = 1;
}

message ReleaseDeviceReq {
  string udid = 1 [(buf.validate.field).string = {min_len: 1, max_len: 64}];
  string token = 2 [(buf.validate.field).string = {min_len: 1, max_len: 64}];
}
message ReleaseDeviceResp {}
