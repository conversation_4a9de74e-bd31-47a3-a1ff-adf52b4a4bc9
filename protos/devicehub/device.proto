syntax = "proto3";

package devicehub;

option go_package = "gitlab.ttyuyin.com/TestDevelopment/probe-backend/devicehub/rpc/pb";

import "common/enum.proto";


enum DeviceState {
  DS_NULL = 0;   // NULL
  IDLE = 1;      // 空闲中
  IN_USE = 2;    // 使用中
  RELEASING = 3; // 释放中
  OFFLINE = 4;   // 已下线
  RESERVED = 5;  // 已预留
}

enum ProviderType {
  PT_NULL = 0;     // NULL
  ATX = 1;         // atx-*-provider
  HUAWEICLOUD = 2; // 华为云
}

message Device {
  string udid = 1;
  string name = 2;
  common.DeviceType type = 3;
  common.PlatformType platform = 4;
  string brand = 5;
  string model = 6;
  string version = 7;
  string serial = 8;
  string provider = 9;
  ProviderType provider_type = 10;
  string remote_address = 11;
  DeviceState state = 12;
  DeviceMetadata metadata = 13;
  string token = 14;
  int64 started_at = 15;
  int64 duration = 16;

  string created_by = 96; // 创建者
  string updated_by = 97; // 更新者
  int64  created_at = 98; // 创建时间
  int64  updated_at = 99; // 更新时间
}

message DeviceMetadata {
  string cph_address = 1;
}

message IdleDevicesNotifyInfo {
  uint32 real_phones_of_android = 1;
  uint32 cloud_phones_of_android = 2;
  uint32 real_phones_of_ios = 3;
  uint32 cloud_phones_of_ios = 4;
}
