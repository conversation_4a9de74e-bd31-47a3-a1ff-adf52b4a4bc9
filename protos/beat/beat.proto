syntax = "proto3";

package beat;

option go_package = "gitlab.ttyuyin.com/TestDevelopment/probe-backend/beat/rpc/pb";

import "validate/validate.proto";


service PlanService {
  //Create 创建计划定时任务
  rpc Create(CreatePlanReq) returns (CreatePlanResp);
  //Remove 删除计划定时任务
  rpc Remove(RemovePlanReq) returns (RemovePlanResp);
}

message CreatePlanReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  string plan_id = 2 [(validate.rules).string = {ignore_empty: true, pattern: "(?:^plan_id:.+?$)"}]; // 计划ID
  string crontab = 3; // crontab语法
  bool debug = 99; // 是否debug模式
}
message CreatePlanResp {}

message RemovePlanReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  string plan_id = 2 [(validate.rules).string = {ignore_empty: true, pattern: "(?:^plan_id:.+?$)"}]; // 计划ID
  bool debug = 99; // 是否debug模式
}
message RemovePlanResp {}


service PeriodicTaskService {
  //CreateOrModifyPeriodicTask 创建或编辑定时任务
  rpc CreateOrModifyPeriodicTask(CreateOrModifyPeriodicTaskReq) returns (CreateOrModifyPeriodicTaskResp);
  //RemovePeriodicTask 删除定时任务
  rpc RemovePeriodicTask(RemovePeriodicTaskReq) returns (RemovePeriodicTaskResp);
  //StartPeriodicTask 启动定时任务
  rpc StartPeriodicTask(StartPeriodicTaskReq) returns (StartPeriodicTaskResp);
  //StopPeriodicTask 停止定时任务
  rpc StopPeriodicTask(StopPeriodicTaskReq) returns (StopPeriodicTaskResp);
}

message CreateOrModifyPeriodicTaskReq {
  string name = 1 [(validate.rules).string = {min_len: 1, max_len: 64}];
  string queue = 2 [(validate.rules).string = {min_len: 1, max_len: 64}];
  string spec = 3 [(validate.rules).string = {min_len: 9}];
  string payload = 4;
}
message CreateOrModifyPeriodicTaskResp{}

message RemovePeriodicTaskReq {
  string name = 1 [(validate.rules).string = {min_len: 1, max_len: 64}];
}
message RemovePeriodicTaskResp {}

message StartPeriodicTaskReq {
  string name = 1 [(validate.rules).string = {min_len: 1, max_len: 64}];
}
message StartPeriodicTaskResp {}

message StopPeriodicTaskReq{
  string name = 1 [(validate.rules).string = {min_len: 1, max_len: 64}];
}
message StopPeriodicTaskResp{}
