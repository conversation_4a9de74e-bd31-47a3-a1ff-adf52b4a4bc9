syntax = "proto3";

package reporter;

option go_package = "gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb";

import "validate/validate.proto";

import "common/enum.proto";
import "common/limit.proto";
import "common/load.proto";
import "common/perf.proto";
import "reporter/common.proto";


message PerfPlanRecord {
  string task_id = 1;    // 任务ID
  string execute_id = 2; // 压测计划执行ID

  string project_id = 11;                   // 项目ID
  string plan_id = 12;                      // 计划ID
  string plan_name = 13;                    // 计划名称
  common.TriggerMode trigger_mode = 14;     // 触发方式（手动、定时、接口）
  int64 target_max_rps = 15;                // 目标最大的RPS
  uint32 target_duration = 16;              // 目标压测持续时长（单位为秒）
  common.Protocol protocol = 17;            // 协议
  common.TargetEnvironment target_env = 18; // 目标环境

  string status = 31;                                // 执行状态（结果）
  common.PerfTaskType task_type = 32;                // 任务类型（执行、调试）
  common.PerfTaskExecutionMode execution_mode = 33;  // 执行方式（按时长、按次数）
  repeated common.PerfServiceMetaData services = 34; // 服务的元数据
  int64 cost_time = 35;                              // 执行耗时（单位为毫秒）
  repeated MonitorUrl monitor_urls = 36;             // 监控面板地址
  string executed_by = 37;                           // 执行者
  int64 started_at = 38;                             // 开始时间
  int64 ended_at = 39;                               // 结束时间

  repeated APIMetric api_metrics = 51; // 接口指标信息
  ErrorMessage err_msg = 52;           // 错误信息

  string created_by = 96; // 创建者
  string updated_by = 97; // 更新者
  int64  created_at = 98; // 创建时间
  int64  updated_at = 99; // 更新时间
}

message PerfSuiteRecord {
  string task_id = 1;          // 任务ID
  string execute_id = 2;       // 压测集合执行ID
  string plan_execute_id = 4;  // 压测计划执行ID

  string project_id = 11;  // 项目ID
  string suite_id = 12;    // 集合ID
  string suite_name = 13;  // 集合名称

  string status = 31;      // 执行状态（结果）
  int64 cost_time = 32;    // 执行耗时（单位为毫秒）
  string executed_by = 33; // 执行者
  int64 started_at = 34;   // 开始时间
  int64 ended_at = 35;     // 结束时间

  ErrorMessage err_msg = 52; // 错误信息

  string created_by = 96; // 创建者
  string updated_by = 97; // 更新者
  int64  created_at = 98; // 创建时间
  int64  updated_at = 99; // 更新时间
}

message PerfCaseRecord {
  string task_id = 1;          // 任务ID
  string execute_id = 2;       // 压测用例执行ID
  string suite_execute_id = 3; // 压测集合执行ID
  string plan_execute_id = 4;  // 压测计划执行ID

  string project_id = 11; // 项目ID
  string plan_id = 12;    // 计划ID
  string plan_name = 13;  // 计划名称
  string suite_id = 14;   // 集合ID
  string suite_name = 15; // 集合名称
  string case_id = 16;    // 用例ID
  string case_name = 17;  // 用例名称

  repeated PerfCaseStepInfo steps = 21;     // 用例步骤
  PerfDataInfo perf_data = 22;              // 压测数据
  common.LoadGenerator load_generator = 23; // 施压机资源

  string status = 31;      // 执行状态（结果）
  int64 cost_time = 32;    // 执行耗时（单位为毫秒）
  string executed_by = 33; // 执行者
  int64 started_at = 34;   // 开始时间
  int64 ended_at = 35;     // 结束时间

  repeated APIMetric api_metrics = 51; // 接口指标信息
  ErrorMessage err_msg = 52;           // 错误信息

  string created_by = 96; // 创建者
  string updated_by = 97; // 更新者
  int64  created_at = 98; // 创建时间
  int64  updated_at = 99; // 更新时间
}

message PerfCaseStepInfo {
  string name = 1 [(validate.rules).string = {min_len: 1, max_len: 64}]; // 步骤名称
  common.PerfCaseStepType type = 2 [(validate.rules).enum = {not_in: [0]}]; // 步骤类型
  repeated common.RateLimitV2 rate_limits = 3 [(validate.rules).repeated = {min_items: 1, items: {message: {required: true}}}]; // 限流配置
  string api_name = 4 [(validate.rules).string = {max_len: 255}]; // 接口名称
}

message PerfDataInfo {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  string data_id = 2 [(validate.rules).string = {ignore_empty: true, pattern: "(?:^perf_data_id:.+?)"}]; // 压测数据ID
  string name = 3 [(validate.rules).string = {ignore_empty: true, max_len: 64}]; // 压测数据名称
  uint32 number_of_vu = 4 [(validate.rules).uint32 = {gt: 0}]; // 虚拟用户数
}

message MonitorUrl {
  string name = 1;                // 名称
  common.MonitorUrlType type = 2; // 类型
  string url = 3;                 // 跳转地址
}

message APIMetric {
  message ResponseResult {
    message KeyValuePair {
      string key = 1;   // 键
      string value = 2; // 值
    }

    string result = 1;                   // 响应结果
    uint64 count = 2;                    // 响应总数（样本总数）
    double sum = 3;                       // 总响应耗时（单位为毫秒）（样本值的大小总和）
    repeated KeyValuePair quantiles = 4; // 响应耗时百分位统计数据
    repeated KeyValuePair buckets = 5;   // 响应耗时分布统计数据
  }

  string api_name = 1;                     // 接口名称
  int64 req_successful = 2;                // 请求成功次数
  int64 req_failed = 3;                    // 请求失败次数
  repeated ResponseResult resp_suites = 4; // 响应结果集
}

message SearchPerfPlanRecordItem {
  string task_id = 1;    // 任务ID
  string execute_id = 2; // 压测计划执行ID

  string project_id = 11;                   // 项目ID
  string plan_id = 12;                      // 计划ID
  string plan_name = 13;                    // 计划名称
  common.TriggerMode trigger_mode = 14;     // 触发方式（手动、定时、接口）
  int64 target_max_rps = 15;                // 目标最大的RPS
  uint32 target_duration = 16;              // 目标压测持续时长（单位为秒）
  common.Protocol protocol = 17;            // 协议
  common.TargetEnvironment target_env = 18; // 目标环境

  string status = 31;                               // 执行状态（结果）
  common.PerfTaskType task_type = 32;               // 任务类型（执行、调试）
  common.PerfTaskExecutionMode execution_mode = 33; // 执行方式（按时长、按次数）
  int64 cost_time = 34;                             // 执行耗时（单位为毫秒）
  bool has_monitor_url = 35;                        // 监控面板地址是否非空
  string executed_by = 36;                          // 执行者
  int64 started_at = 37;                            // 开始时间
  int64 ended_at = 38;                              // 结束时间
  ErrorMessage err_msg = 39;                        // 错误信息

  string created_by = 96; // 创建者
  string updated_by = 97; // 更新者
  int64  created_at = 98; // 创建时间
  int64  updated_at = 99; // 更新时间
}
