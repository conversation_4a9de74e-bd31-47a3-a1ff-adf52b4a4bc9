syntax = "proto3";

package reporter;

option go_package = "gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb";

import "common/config.proto";
import "common/enum.proto";


message UIPlanRecord {
  string task_id = 1;    // 任务ID
  string execute_id = 2; // UI计划执行ID

  string project_id = 11;                   // 项目ID
  string plan_id = 12;                      // 计划ID
  string plan_name = 13;                    // 计划名称
  common.TriggerMode type = 14;             // 计划类型（手动、定时、接口）
  common.PriorityType priority_type = 15;   // 优先级
  common.DeviceType device_type = 16;       // 设备类型（真机、云手机）
  common.PlatformType platform_type = 17;   // 平台类型（Android、iOS）
  common.GitConfig git_config = 18;         // Git配置
  string package_name = 19;                 // 包名
  string callback_url = 20;                 // 回调地址
  string app_download_link = 21;            // APP下载地址
  string app_version = 22;                  // APP版本
  string app_name = 23;                     // 应用名称
  common.TestLanguage test_language = 24;   // 测试语言
  string test_language_version = 25;        // 测试语言版本
  common.TestFramework test_framework = 26; // 测试框架
  repeated string test_args = 27;           // 附加参数
  string execution_environment = 28;        // 执行环境
  repeated string devices = 29;             // 设备列表
  bool together = 30;                       // 选择的设备是否一起执行

  string status = 41;       // 执行状态（结果）
  int64 total_suite = 42;   // 总的集合数
  int64 success_suite = 43; // 执行成功的集合数
  int64 failure_suite = 44; // 执行失败的集合数
  int64 total_case = 45;    // 总的用例数
  int64 success_case = 46;  // 执行成功的用例数
  int64 failure_case = 47;  // 执行失败的用例数
  int64 cost_time = 48;     // 耗时
  int64 started_at = 49;    // 开始时间
  int64 ended_at = 50;      // 结束时间
  string executed_by = 51;  // 执行者
}

message UISuiteRecord {
  string task_id = 1;         // 任务ID
  string execute_id = 2;      // UI集合执行ID
  string plan_execute_id = 3; // UI计划执行ID

  string project_id = 11;  // 项目ID
  string suite_id = 12;    // 集合ID
  string suite_name = 13;  // 集合名称
  string udid = 14;        // 设备编号

  string status = 21;      // 执行状态（结果）
  int64 total_case = 22;   // 总的用例数
  int64 success_case = 23; // 执行成功的用例数
  int64 failure_case = 24; // 执行失败的用例数
  int64 cost_time = 25;    // 耗时
  int64 started_at = 26;   // 开始时间
  int64 ended_at = 27;     // 结束时间
  string executed_by = 28; // 执行者
}

message UICaseRecord {
  string task_id = 1;          // 任务ID
  string execute_id = 2;       // UI用例执行ID
  string suite_execute_id = 3; // UI集合执行ID

  string project_id = 11; // 项目ID
  string case_id = 12;    // 用例ID
  string case_name = 13;  // 用例名称
  string udid = 14;       // 设备编号

  string status = 21;      // 执行状态（结果）
  int64 cost_time = 22;    // 耗时
  int64 started_at = 23;   // 开始时间
  int64 ended_at = 24;     // 结束时间
  string executed_by = 25; // 执行者
}

message UICaseStep {
  string task_id = 1;         // 任务ID
  string step_id = 2;         // 步骤ID
  common.TestStage stage = 3; // 阶段
  string name = 4;            // 步骤名称
  string status = 5;          // 执行状态（结果）
  string content = 6;         // 步骤内容
  int64 started_at = 7;       // 开始时间
  int64 ended_at = 8;         // 结束时间
  int64 index = 9;            // 步骤索引
}

message UIDeviceRecord {
  string task_id = 1;         // 任务ID
  string plan_execute_id = 2; // UI计划执行ID

  string project_id = 11; // 项目ID
  string udid = 12;       // 设备编号

  string status = 21;      // 执行状态（结果）
  int64 total_case = 22;   // 总的用例数
  int64 success_case = 23; // 执行成功的用例数
  int64 failure_case = 24; // 执行失败的用例数
  int64 cost_time = 25;    // 耗时
  int64 started_at = 26;   // 开始时间
  int64 ended_at = 27;     // 结束时间
  string executed_by = 28; // 执行者
}
