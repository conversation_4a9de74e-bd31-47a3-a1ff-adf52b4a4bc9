syntax = "proto3";

package manager;

option go_package = "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb";


message AccountConfiguration {
  string project_id = 1; // 项目ID
  string config_id = 2; // 池账号配置ID
  string name = 3; // 池账号配置名称
  string description = 4; // 池账号配置描述
  int64 product_type = 5; // 产品类型
  string product_name = 6; // 产品名称
  string pool_env_table = 7; // 账号池环境表名称
  string pool_env_name = 8; // 账号池环境名称
  string created_by = 9; // 创建者
  string updated_by = 10; // 更新者
  int64 created_at = 11; // 创建时间
  int64 updated_at = 12; // 更新时间
}
