syntax = "proto3";

package manager;

option go_package = "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb";

//import "google/protobuf/any.proto";
import "google/protobuf/struct.proto";


// Node 点
message Node {
  string id = 1;
  string type = 2;
  string label = 3;
  string itemType = 4;
  double width = 5;
  double height = 6;
  repeated double size = 7;
  double x = 8;
  double y = 9;
  repeated google.protobuf.ListValue anchorPoints = 10;
  string icon = 11;
  google.protobuf.Struct style = 12;
  google.protobuf.Struct labelCfg = 13;
  string comboId = 14;
  google.protobuf.Struct data = 15;
  int32 order = 16 [json_name = "_order"]; // 注：不能定义为`int64`，否则序列化的时候会被转为`string`，再反序列化的时候就会报错
  int32 layoutOrder = 17; // 注：不能定义为`int64`，否则序列化的时候会被转为`string`，再反序列化的时候就会报错
}

// Edge 线
message Edge {
  string id = 1;
  string type = 2;
  string label = 3;
  string source = 4;
  double sourceAnchor = 5;
  string target = 6;
  double targetAnchor = 7;
  int32 lineAppendWidth = 8;
  string clazz = 9;
  google.protobuf.Struct attrs = 10;
  google.protobuf.Struct style = 11;
  google.protobuf.Struct labelCfg = 12;
  google.protobuf.Struct startPoint = 13;
  google.protobuf.Struct endPoint = 14;
}

// Combo 框
message Combo {
  message ComboChild {
    string id = 1;
    string comboId = 2;
    string itemType = 3;
    int32 depth = 4; // 注：不能定义为`int64`，否则序列化的时候会被转为`string`，再反序列化的时候就会报错
  }

  string id = 1;
  string type = 2;
  string label = 3;
  string itemType = 4;
  double x = 5;
  double y = 6;
  repeated google.protobuf.ListValue anchorPoints = 7;
  string icon = 8;
  google.protobuf.Struct style = 9;
  google.protobuf.Struct labelCfg = 10;
  int32 depth = 11; // 注：不能定义为`int64`，否则序列化的时候会被转为`string`，再反序列化的时候就会报错
  repeated double padding = 12;
  bool collapsed = 13;
  repeated ComboChild children = 14;
  google.protobuf.Struct data = 15;
}

message ListFloat {
  repeated float list = 1;
}
