syntax = "proto3";

package manager;

option go_package = "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb";


message GitProjectTreeNode {
  string project_id = 1; // 项目ID
  string git_config_id = 2; // Git配置ID
  string path = 3; // 节点路径
  string parent_path = 4; // 父节点路径
  string name = 5; // 节点名称
  string alias = 7; // 节点别名
  string type = 8; // 节点类型
  repeated string tags = 9; // 标签
  string created_by = 10; // 创建者
  string updated_by = 11; // 更新者
  int64 created_at = 12; // 创建时间
  int64 updated_at = 13; // 更新时间
}
