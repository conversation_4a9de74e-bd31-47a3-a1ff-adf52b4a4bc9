syntax = "proto3";

package manager;

option go_package = "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb";

import "validate/validate.proto";

import "common/enum.proto";
import "common/limit.proto";
import "common/perf.proto";
import "manager/base.proto";


message PerfPlan {
  string project_id = 1; // 项目ID
  string plan_id = 2; // 计划ID
  string name = 3; // 计划名称
  string description = 4; // 计划描述
  common.TriggerMode type = 5; // 计划类型（手动）
  repeated string tags = 6; // 标签
  CommonState state = 7; // 状态
  common.Protocol protocol = 8; // 协议
  string protobuf_config_id = 9; // Protobuf配置ID
  string general_config_id = 10; // 通用配置ID
  string account_config_id = 11; // 池账号配置ID
  uint32 duration = 12; // 压测持续时长
  common.TargetEnvironment target_env = 13; // 目标环境
  common.PerfKeepalive keepalive = 14; // 保活参数
  uint32 delay = 15; // 延迟执行时间
  string maintained_by = 16; // 维护者

  string created_by = 96; // 创建者
  string updated_by = 97; // 更新者
  int64  created_at = 98; // 创建时间
  int64  updated_at = 99; // 更新时间
}

message SearchPerfPlanItem {
  string project_id = 1; // 项目ID
  string plan_id = 2; // 计划ID
  string name = 3; // 计划名称
  string description = 4; // 计划描述
  common.TriggerMode type = 5; // 计划类型（手动）
  repeated string tags = 6; // 标签
  CommonState state = 7; // 状态
  common.Protocol protocol = 8; // 协议
  uint32 duration = 9; // 压测持续时长
  common.TargetEnvironment target_env = 10; // 目标环境
  uint32 number_of_case = 11; // 压测用例数量
  uint32 number_of_api = 12; // 压测接口数量
  repeated StatsOfApi stats_of_api = 13; // 压测接口统计信息
  string maintained_by = 14; // 维护者

  string created_by = 96; // 创建者
  string updated_by = 97; // 更新者
  int64  created_at = 98; // 创建时间
  int64  updated_at = 99; // 更新时间
}

message StatsOfApi {
  int64 target_rps = 1; // 目标的RPS
  uint32 number_of_api = 2; // 接口数量
}

message SearchCaseInPerfPlanItem {
  string project_id = 1;  // 项目ID
  string case_id = 2;     // 压测用例ID
  string name = 3;        // 压测用例名称
  string description = 4; // 压测用例描述
  string extension = 5;   // 压测用例文件的扩展名
  string hash = 6;        // 压测用例文件的一致性哈希值
  uint32 size = 7;        // 压测用例文件的大小

  // 限流配置
  int64 target_rps = 11;     // 目标的RPS
  int64 initial_rps = 12;    // 初始的RPS
  int64 step_height = 13;    // 每次改变RPS的量
  string step_duration = 14; // 改变后的RPS的持续时间

  // 压测数据配置
  string perf_data_id = 21; // 压测数据ID
  uint32 number_of_vu = 22; // 虚拟用户数

  // 施压机资源配置
  uint32 number_of_lg = 31;       // 施压机数量
  string requests_of_cpu = 32;    // 最小分配的CPU资源
  string requests_of_memory = 33; // 最小分配的内存资源
  string limits_of_cpu = 34;      // 最大分配的CPU资源
  string limits_of_memory = 35;   // 最大分配的内存资源

  string created_by = 96; // 创建者
  string updated_by = 97; // 更新者
  int64 created_at = 98;  // 创建时间
  int64 updated_at = 99;  // 更新时间
}

message PerfPlanV2 {
  string project_id = 1; // 项目ID
  string plan_id = 2; // 计划ID
  string category_id = 3; // 分类ID

  string name = 11; // 计划名称
  string description = 12; // 计划描述
  common.TriggerMode type = 13; // 计划类型（手动、定时）
  string cron_expression = 14; // 定时触发计划的Cron表达式
  repeated string tags = 15; // 标签
  common.Protocol protocol = 16; // 协议
  common.TargetEnvironment target_env = 17; // 目标环境
  string protobuf_config_id = 18; // Deprecated: Protobuf配置ID
  string general_config_id = 19; // 通用配置ID
  string account_config_id = 20; // 池账号配置ID
  repeated common.RateLimitV2 auth_rate_limits = 21; // TT登录压测场景专用的登录接口限流配置

  bool custom_duration = 31; // 是否自定义压测持续时长
  uint32 duration = 32; // 压测持续时长
  bool create_lark_chat = 33; // 是否需要自动拉群
  string lark_chat_id = 34; // 通过自动拉群创建的飞书群ID
  bool advanced_notification = 35; // 是否需要提前通知

  CommonState state = 41; // 状态
  string maintained_by = 42; // 维护者

  string created_by = 96; // 创建者
  string updated_by = 97; // 更新者
  int64 created_at = 98;  // 创建时间
  int64 updated_at = 99;  // 更新时间
}

message SearchPerfPlanV2Item {
  string project_id = 1; // 项目ID
  string plan_id = 2; // 计划ID
  string category_id = 3; // 分类ID

  string name = 11; // 计划名称
  string description = 12; // 计划描述
  common.TriggerMode type = 13; // 计划类型（手动、定时）
  string cron_expression = 14; // 定时触发计划的Cron表达式
  repeated string tags = 15; // 标签
  common.Protocol protocol = 16; // 协议
  common.TargetEnvironment target_env = 17; // 目标环境

  bool custom_duration = 31; // 是否自定义压测持续时长
  uint32 duration = 32; // 压测持续时长

  uint32 number_of_cases = 41; // 用例数量
  uint32 number_of_steps = 42; // 步骤数量（串行和并行）
  repeated StatsOfStep stats_of_step = 43; // 步骤统计信息

  CommonState state = 51; // 状态
  string maintained_by = 52; // 维护者

  string created_by = 96; // 创建者
  string updated_by = 97; // 更新者
  int64 created_at = 98;  // 创建时间
  int64 updated_at = 99;  // 更新时间
}

message StatsOfStep {
  int64 target_rps = 1; // 目标的RPS
  uint32 number_of_steps = 2; // 步骤数量
}

message PerfPlanCaseV2Item {
  string case_id = 1 [(validate.rules).string = {pattern: "(?:^perf_case_id:.+?)", max_len: 64}]; // 用例ID

  // 压测数据配置
  string perf_data_id = 11 [(validate.rules).string = {ignore_empty: true, pattern: "(?:^perf_data_id:.+?)", max_len: 64}]; // 压测数据ID
  bool custom_vu = 12; // 是否自定义虚拟用户数
  uint32 number_of_vu = 13 [(validate.rules).uint32 = {gte: 0}]; // 虚拟用户数

  // 施压机资源配置
  bool custom_lg = 21; // 是否自定义施压机资源
  uint32 number_of_lg = 22 [(validate.rules).uint32 = {gte: 0}]; // 施压机数量
  string requests_of_cpu = 23 [(validate.rules).string = {ignore_empty: true, min_len: 1}];    // 最小分配的CPU资源
  string requests_of_memory = 24 [(validate.rules).string = {ignore_empty: true, min_len: 1}]; // 最小分配的内存资源
  string limits_of_cpu = 25 [(validate.rules).string = {ignore_empty: true, min_len: 1}];      // 最大分配的CPU资源
  string limits_of_memory = 26 [(validate.rules).string = {ignore_empty: true, min_len: 1}];   // 最大分配的内存资源

  // 用例限流配置
  repeated common.RateLimitV2 rate_limits = 27; // 限流配置
}

message SearchCaseInPerfPlanV2Item {
  string project_id = 1;  // 项目ID
  string category_id = 2; // 所属分类ID
  string case_id = 3;     // 用例ID

  string name = 11;                                   // 用例名称
  string description = 12;                            // 用例描述
  repeated string tags = 13;                          // 标签
  common.Protocol protocol = 14;                      // 协议
  repeated common.RateLimitV2 rate_limits = 15;       // 限流配置
  repeated common.PerfCaseStepV2 serial_steps = 16;   // 串行步骤列表
  repeated common.PerfCaseStepV2 parallel_steps = 17; // 并行步骤列表

  uint32 number_of_steps = 21; // 测试步骤数
  int64 target_rps = 22;       // 目标的RPS

  // 压测数据配置
  string perf_data_id = 31; // 压测数据ID
  bool custom_vu = 32;      // 是否自定义虚拟用户数
  uint32 number_of_vu = 33; // 虚拟用户数

  // 施压机资源配置
  bool custom_lg = 41;            // 是否自定义施压机资源
  uint32 number_of_lg = 42;       // 施压机数量
  string requests_of_cpu = 43;    // 最小分配的CPU资源
  string requests_of_memory = 44; // 最小分配的内存资源
  string limits_of_cpu = 45;      // 最大分配的CPU资源
  string limits_of_memory = 46;   // 最大分配的内存资源

  CommonState state = 51;    // 状态
  string maintained_by = 52; // 维护者

  string created_by = 96; // 创建者
  string updated_by = 97; // 更新者
  int64 created_at = 98;  // 创建时间
  int64 updated_at = 99;  // 更新时间
}

message SearchProtobufInPerfPlanV2Item {
  string project_id = 1; // 项目ID
  string config_id = 2; // Protobuf配置ID

  string name = 11; // Protobuf配置名称
  string description = 12; // Protobuf配置描述
  string git_config_id = 13; // Git配置ID
  string import_path = 14; // 导入路径
  repeated string exclude_paths = 15; // 排除的路径列表
  repeated string exclude_files = 16; // 排除的文件列表
  repeated string dependencies = 17; // 依赖列表

  string created_by = 96; // 创建者
  string updated_by = 97; // 更新者
  int64 created_at = 98; // 创建时间
  int64 updated_at = 99; // 更新时间
}

message SearchRuleInPerfPlanV2Item {
  string project_id = 1; // 项目ID
  string rule_id = 2; // 规则ID

  string name = 11; // 规则名称
  string description = 12; // 规则描述
  common.MetricType metric_type = 13; // 指标类型
  double threshold = 14; // 阀值
  uint32 duration = 15; // 持续时间

  CommonState state = 21; // 状态

  string created_by = 96; // 创建者
  string updated_by = 97; // 更新者
  int64  created_at = 98; // 创建时间
  int64  updated_at = 99; // 更新时间
}
