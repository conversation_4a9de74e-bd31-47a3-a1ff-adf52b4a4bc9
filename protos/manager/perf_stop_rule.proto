syntax = "proto3";

package manager;

option go_package = "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb";

import "common/enum.proto";
import "manager/base.proto";


message PerfStopRule {
  string project_id = 1; // 项目ID
  string rule_id = 2; // 规则ID

  string name = 11; // 规则名称
  string description = 12; // 规则描述
  common.MetricType metric_type = 13; // 指标类型
  double threshold = 14; // 阀值
  uint32 duration = 15; // 持续时间

  CommonState state = 21; // 状态

  string created_by = 96; // 创建者
  string updated_by = 97; // 更新者
  int64  created_at = 98; // 创建时间
  int64  updated_at = 99; // 更新时间
}
