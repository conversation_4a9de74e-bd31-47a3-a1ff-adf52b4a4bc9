syntax = "proto3";

package manager;
import "validate/validate.proto";

option go_package = "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb";


message NotifyItem {
  NotifyMode notify_mode = 1; // 通知模式
  NotifyType notify_type = 2; // 通知类型
  string receiver = 3; // 接受者
}

enum NotifyMode {
  NOTIFY_MODE_NULL = 0;
  ALWAYS_NOTIFY = 1;
  ONLY_FALSE_NOTIFY = 2;
}

enum NotifyType {
  NOTIFY_TYPE_NULL = 0;
  LARK_GROUP = 1; // 通过自定义机器人的Webhook发送消息到飞书群
  EMAIL = 2;
  LARK_CHAT = 3; // 通过自建应用所在的飞书群ID发送消息到飞书群
}

message CreateNotifyItem {
  string receiver_name = 1 [(validate.rules).string = {max_len: 64}];
  string receiver = 2 [(validate.rules).string = {pattern: "(?:^https://open\\.feishu\\.cn/open-apis|[\\w]+@[A-Za-z0-9]+(.[A-Za-z0-9]+){1,2})|^oc_[a-z0-9]{32}$"}];
}

message Notify {
  string project_id = 1;
  string plan_id = 2;
  string notify_id = 3;
  NotifyMode notify_mode = 4;
  NotifyType notify_type = 5;
  string receiver_name = 6;
  string receiver = 7;
  string created_by = 8; // 创建者
  string updated_by = 9; // 更新者
  int64  created_at = 10; // 创建时间
  int64  updated_at = 11; // 更新时间
}
