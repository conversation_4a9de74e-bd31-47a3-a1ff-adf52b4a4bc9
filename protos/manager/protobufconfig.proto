syntax = "proto3";

package manager;

option go_package = "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb";


message ProtobufConfiguration {
  string project_id = 1; // 项目ID
  string config_id = 2; // Protobuf配置ID
  string name = 3; // Protobuf配置名称
  string description = 4; // Protobuf配置描述
  string git_config_id = 5; // Git配置ID
  string import_path = 6; // 导入路径
  repeated string exclude_paths = 7; // 排除的路径列表
  repeated string exclude_files = 8; // 排除的文件列表
  repeated string dependencies = 9; // 依赖列表

  string created_by = 96; // 创建者
  string updated_by = 97; // 更新者
  int64 created_at = 98; // 创建时间
  int64 updated_at = 99; // 更新时间
}
