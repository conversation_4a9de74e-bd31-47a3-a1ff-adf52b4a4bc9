syntax = "proto3";

package manager;

option go_package = "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb";

import "validate/validate.proto";


message ApiPlanExtraData {
  repeated string services = 1 [(validate.rules).repeated = {ignore_empty: true, items: {string: {min_len: 1}}}]; // 服务列表，用于精准测试
}

message UiPlanExtraData {
  repeated string devices = 1 [(validate.rules).repeated = {ignore_empty: true, items: {string: {min_len: 1, max_len: 64}}}]; // 设备列表
  bool together = 2; // 选择的设备是否一起执行
}
