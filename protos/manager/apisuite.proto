syntax = "proto3";

package manager;

option go_package = "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb";

import "validate/validate.proto";

import "manager/base.proto";


message ApiSuite {
  string project_id = 1; // 项目ID
  string category_id = 2; // 所属分类ID
  string suite_id = 3; // 集合ID
  string name = 4; // 集合名称
  string description = 5; // 集合描述
  int64 priority = 6; // 优先级
  repeated string tags = 7; // 标签
  CommonState state = 8; // 状态
  ExecutionMode case_execution_mode = 9; // 用例执行方式
  string maintained_by = 10; // 维护者
  string created_by = 11; // 创建者
  string updated_by = 12; // 更新者
  int64  created_at = 13; // 创建时间
  int64  updated_at = 14; // 更新时间
}

message SearchApiSuiteReferenceItem {
  string project_id = 1; // 项目ID
  string suite_id = 2; // 集合ID
  string reference_type = 3; // 引用对象类型
  string reference_id = 4; // 引用对象ID
  string name = 5; // 引用对象名称
  string description = 6; // 引用对象描述
  int64 priority = 7; // 优先级
  repeated string tags = 8; // 标签
  CommonState state = 9; // 状态
  CommonState reference_state = 10; // 引用状态
  string maintained_by = 11; // 维护者
  string created_by = 12; // 创建者
  string updated_by = 13; // 更新者
  int64  created_at = 14; // 创建时间
  int64  updated_at = 15; // 更新时间
}

message SearchCaseInApiSuiteItem {
  string project_id = 1; // 项目ID
  string category_id = 2; // 场景用例的所属分类ID
  string document_id = 3; // 接口用例的接口文档ID
  string case_type = 4 [(validate.rules).string = {in: ["API_CASE", "INTERFACE_CASE"]}]; // 用例类型
  string case_id = 5; // 用例ID
  string name = 6; // 用例名称
  string description = 7; // 用例描述
  int64 priority = 8; // 优先级
  repeated string tags = 9; // 标签
  ResourceState state = 10; // 状态
  AccountConfig account_config = 11; // 池账号配置信息
  string version = 12; // 用例版本
  string maintained_by = 13; // 维护者
  string created_by = 14; // 创建者
  string updated_by = 15; // 更新者
  int64  created_at = 16; // 创建时间
  int64  updated_at = 17; // 更新时间
}

message CaseTypeId {
  string case_type = 1 [(validate.rules).string = {in: ["API_CASE", "INTERFACE_CASE"]}]; // 用例类型
  string case_id = 2; // 用例ID
}

