syntax = "proto3";

package manager;

option go_package = "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb";


message PerfLarkChat {
  string project_id = 1; // 项目ID
  string chat_id = 2; // 飞书群组ID

  string name = 11; // 飞书群组名称
  string avatar = 12; // 飞书群组头像URL
  string description = 13; // 飞书群组描述
  bool external = 14; // 是否是外部群
  string status = 15; // 飞书群组状态

  string created_by = 96; // 创建者
  string updated_by = 97; // 更新者
  int64  created_at = 98; // 创建时间
  int64  updated_at = 99; // 更新时间
}
