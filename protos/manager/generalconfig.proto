syntax = "proto3";

package manager;

option go_package = "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb";

import "common/config.proto";


message GeneralConfiguration {
  string project_id = 1; // 项目ID
  string config_id = 2; // 通用配置ID
  string type = 3; // 类型
  string name = 4; // 通用配置名称
  string description = 5; // 通用配置描述
  string base_url = 6; // HTTP请求基础URL
  bool verify = 7; // 是否验证服务器的TLS证书
  repeated common.GeneralConfigVar variables = 8; // 通用配置变量列表

  string created_by = 96; // 创建者
  string updated_by = 97; // 更新者
  int64 created_at = 98; // 创建时间
  int64 updated_at = 99; // 更新时间
}
