syntax = "proto3";

package manager;

option go_package = "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb";

import "validate/validate.proto";

import "common/enum.proto";
import "devicehub/device.proto";


message BindDevice {
  string udid = 1 [(validate.rules).string = {min_len: 1, max_len: 64}]; // 设备编号
  common.DeviceUsage usage = 2 [(validate.rules).enum = {not_in: [0]}]; // 设备用途
}

message DeviceRelationship {
  string udid = 1 [(validate.rules).string = {min_len: 1, max_len: 64}]; // 设备编号
  int64 count = 2 [(validate.rules).int64 = {gte: 0}]; // 关联的计划数量（包括：UI测试计划、稳定性测试计划）
}

message ProjectDevice {
  devicehub.Device device = 1;

  string project_id = 11; // 项目ID
  common.DeviceUsage usage = 12; // 设备用途
}

message SearchProjectDeviceReferenceItem {
  string project_id = 1; // 项目ID
  string udid = 2;       // 设备编号

  string reference_type = 11; // 引用对象类型（UI测试计划、稳定性测试计划）
  string reference_id = 12;   // 引用对象ID
  string category_id = 13;    // 引用对象分类ID
  string name = 14;           // 引用对象名称
  string description = 15;    // 引用对象描述
  string maintained_by = 16;  // 维护者

  string created_by = 96; // 创建者
  string updated_by = 97; // 更新者
  int64  created_at = 98; // 创建时间
  int64  updated_at = 99; // 更新时间
}

message DeleteDisabledDeviceTaskInfo {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
}
