syntax = "proto3";

package manager;

option go_package = "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb";

import "validate/validate.proto";

import "common/enum.proto";
import "manager/base.proto";


message UiPlan {
  string project_id = 1;                    // 项目ID
  string plan_id = 2;                       // 计划ID
  string name = 3;                          // 计划名称
  string description = 4;                   // 描述
  common.TriggerMode type = 5;              // 计划类型（手动、定时、接口）
  string cron_expression = 6;               // 定时触发计划的Cron表达式
  common.PriorityType priority_type = 7;    // 优先级
  string git_config_id = 8;                 // Git配置ID
  ExecutionMode execution_mode = 9;         // 执行方式（并行、串行）
  common.DeviceType device_type = 10;       // 设备类型（真机、云手机）
  common.PlatformType platform_type = 11;   // 平台类型（Android、iOS）
  string package_name = 12;                 // 包名，用于启动APP
  string callback_url = 13;                 // 回调地址
  string app_download_link = 14;            // APP下载地址
  string app_version = 15;                  // APP版本
  string app_name = 16;                     // 应用名称
  common.TestLanguage test_language = 17;   // 测试语言
  string test_language_version = 18;        // 测试语言版本
  common.TestFramework test_framework = 19; // 测试框架
  repeated string test_args = 20;           // 附加参数
  string execution_environment = 21;        // 执行环境
  common.FailRetry  fail_retry = 22;        // 失败重试（0次、1次、2次）、
  CommonState state = 23;                   // 状态
  string maintained_by = 24;                // 维护者
  string category_id = 25;                  // 分类ID
  repeated string devices = 26;             // 设备列表
  bool together = 27;                       // 选择的设备是否一起执行

  string created_by = 96;                   // 创建者
  string updated_by = 97;                   // 更新者
  int64  created_at = 98;                   // 创建时间
  int64  updated_at = 99;                   // 更新时间
}

message CasePathItem {
  string CasePath = 1 [(validate.rules).string = {pattern: "(?:^case_path:.+?)"}]; // 用例路径
}

message Path {
  string path = 1; // 路径
}

message UICaseTreeNode {
  string project_id = 1; // 项目ID
  string git_config_id = 2; // Git配置ID
  string path = 3; // 节点路径（相对于根路径）
  string parent_path = 4; // 父节点路径
  string name = 5; // 节点名称
  string alias = 6; // 节点别名
  string type = 7; // 节点类型(目录、文件、包、模块、类、函数)
  repeated string tags = 8; // 标签
  string created_by = 9; // 创建者
  string updated_by = 10; // 更新者
  int64 created_at = 11; // 创建时间
  int64 updated_at = 12; // 更新时间
  int64 amount = 13; // 叶子节点数量
  repeated UICaseTreeNode children = 14; // 子节点
}

message SearchCaseInUIPlanItem {
  string project_id = 1; // 项目ID
  string git_config_id = 2; // Git配置ID
  string path = 3; // 节点路径（相对于根路径）
  string parent_path = 4; // 父节点路径
  string name = 5; // 节点名称
  string alias = 6; // 节点别名
  string type = 7; // 节点类型（目录、文件、包、模块、类、函数）
  repeated string tags = 8; // 标签
  string created_by = 9; // 创建者
  string updated_by = 10; // 更新者
  int64 created_at = 11; // 创建时间
  int64 updated_at = 12; // 更新时间
}

message SearchCaseNotInUIPlanItem {
  string project_id = 1; // 项目ID
  string git_config_id = 2; // Git配置ID
  string path = 3; // 节点路径（相对于根路径）
  string parent_path = 4; // 父节点路径
  string name = 5; // 节点名称
  string alias = 6; // 节点别名
  string type = 7; // 节点类型（目录、文件、包、模块、类、函数）
  repeated string tags = 8; // 标签
  string created_by = 9; // 创建者
  string updated_by = 10; // 更新者
  int64 created_at = 11; // 创建时间
  int64 updated_at = 12; // 更新时间
}
