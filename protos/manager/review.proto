syntax = "proto3";

package manager;

option go_package = "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb";

import "protobuf/options.proto";


// ReviewResourceType 审核资源类型
enum ReviewResourceType {
  RRT_NULL = 0; // NULL

  RRT_COMPONENT_GROUP = 1 [(options.enum_value_alias) = "COMPONENT_GROUP"]; // 组件组
  RRT_CASE = 2 [(options.enum_value_alias) = "CASE"]; // 用例

  RRT_SETUP_COMPONENT = 11 [(options.enum_value_alias) = "SETUP"]; // 前置组件
  RRT_TEARDOWN_COMPONENT = 12 [(options.enum_value_alias) = "TEARDOWN"]; // 后置组件
  RRT_BUSINESS_COMPONENT = 13 [(options.enum_value_alias) = "GROUP"]; // 业务组件
  RRT_API_CASE = 14 [(options.enum_value_alias) = "API_CASE"]; // 场景用例
  RRT_INTERFACE_CASE = 15 [(options.enum_value_alias) = "INTERFACE_CASE"]; // 接口用例
}

// ReviewResourceEvent 审核资源事件
enum ReviewResourceEvent {
  RRE_NULL = 0; // NULL

  RRE_ASSIGNED_TO_THE_RESPONSIBLE_PERSON = 1 [(options.enum_value_alias)= "AssignedToTheResponsiblePerson"]; // 分配负责人
  RRE_APPLY_FOR_REVIEW_AFTER_IMPLEMENTATION = 2[(options.enum_value_alias) = "ApplyForReviewAfterImplementation"]; // 实现后申请审核
  RRE_APPLY_FOR_REVIEW_AFTER_MAINTENANCE = 3 [(options.enum_value_alias) = "ApplyForReviewAfterMaintenance"]; // 维护后申请审核
  RRE_REVIEW_APPROVED = 4 [(options.enum_value_alias) = "ReviewApproved"]; // 审核通过
  RRE_REVIEW_REJECTED = 5 [(options.enum_value_alias) = "ReviewRejected"]; // 审核驳回
}

// ReviewStatus 审核状态
enum ReviewStatus {
  REVIEW_STATUS_NULL = 0; // NULL（避免跟`base.proto:ResourceState.RS_NULL`冲突，因此前缀改为`REVIEW_STATUS_`）

  REVIEW_STATUS_PENDING = 1 [(options.enum_value_alias) = "PENDING"]; // 待审核
  REVIEW_STATUS_REVOKED = 2 [(options.enum_value_alias) = "REVOKED"]; // 已撤销
  REVIEW_STATUS_APPROVED = 3 [(options.enum_value_alias) = "APPROVED"]; // 已通过
  REVIEW_STATUS_REJECTED = 4 [(options.enum_value_alias) = "REJECTED"]; // 已驳回
}

// ReviewRecord 审核记录
message ReviewRecord {
  string project_id = 1; // 项目ID
  string review_id = 2; // 审核ID
  string resource_branch = 3; // 资源所属分支（分类树ID、接口文档ID）
  ReviewResourceType resource_parent_type = 4; // 资源父类型（组件组、用例）
  ReviewResourceType resource_type = 5; // 资源类型（业务组件、前置组件、后置组件、场景用例、接口用例）
  string resource_id = 6; // 资源ID（组件组ID、场景用例ID、接口用例ID）
  string resource_name = 7; // 资源名称（组件组名称、场景用例名称、接口用例名称）
  string remark_of_pending = 8; // 申请时的备注
  string remark_of_revoked = 9; // 撤回时的备注
  string remark_of_reviewed = 10; // 审批时的备注
  repeated string assigned_reviewers = 11; // 指派的审核者
  ReviewStatus status = 12; // 审核状态

  string created_by = 96;
  string updated_by = 97;
  int64  created_at = 98;
  int64  updated_at = 99;
}
