syntax = "proto3";

package manager;

option go_package = "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb";

import "validate/validate.proto";

import "common/enum.proto";
import "common/limit.proto";
import "common/perf.proto";
import "manager/base.proto";


message PerfCase {
  string project_id = 1;  // 项目ID
  string case_id = 2;     // 压测用例ID
  string name = 3;        // 压测用例名称
  string description = 4; // 压测用例描述
  string extension = 5;   // 压测用例文件的扩展名
  string hash = 6;        // 压测用例文件的一致性哈希值
  uint32 size = 7;        // 压测用例文件的大小

  // 限流配置
  int64 target_rps = 11;    // 目标的RPS
  int64 initial_rps = 12;   // 初始的RPS
  int64 step_height = 13;   // 每次改变RPS的量
  int64 step_duration = 14; // 改变后的RPS的持续时间

  // 压测数据配置
  string perf_data_id = 21; // 压测数据ID
  uint32 number_of_vu = 22; // 虚拟用户数

  // 施压机资源配置
  uint32 number_of_lg = 31;       // 施压机数量
  string requests_of_cpu = 32;    // 最小分配的CPU资源
  string requests_of_memory = 33; // 最小分配的内存资源
  string limits_of_cpu = 34;      // 最大分配的CPU资源
  string limits_of_memory = 35;   // 最大分配的内存资源

  string created_by = 96; // 创建者
  string updated_by = 97; // 更新者
  int64 created_at = 98;  // 创建时间
  int64 updated_at = 99;  // 更新时间
}

message PerfCaseV2 {
  string project_id = 1;  // 项目ID
  string category_id = 2; // 所属分类ID
  string case_id = 3;     // 用例ID

  string name = 11;                             // 用例名称
  string description = 12;                      // 用例描述
  repeated string tags = 13;                    // 标签
  common.Protocol protocol = 14;                // 协议
  repeated common.RateLimitV2 rate_limits = 15; // 限流配置

  repeated common.PerfCaseStepV2 setup_steps = 21;    // 前置步骤列表
  repeated common.PerfCaseStepV2 serial_steps = 22;   // 串行步骤列表
  repeated common.PerfCaseStepV2 parallel_steps = 23; // 并行步骤列表
  repeated common.PerfCaseStepV2 teardown_steps = 24; // 后置步骤列表
  uint32 number_of_steps = 25;                        // 测试步骤数
  int64 target_rps = 26;                              // 目标的RPS

  CommonState state = 31; // 状态
  string maintained_by = 32; // 维护者

  string created_by = 96; // 创建者
  string updated_by = 97; // 更新者
  int64 created_at = 98;  // 创建时间
  int64 updated_at = 99;  // 更新时间
}

message SearchPerfCaseV2Item {
  string project_id = 1;  // 项目ID
  string category_id = 2; // 所属分类ID
  string case_id = 3;     // 压测用例ID

  string name = 11;                                   // 压测用例名称
  string description = 12;                            // 压测用例描述
  repeated string tags = 13;                          // 标签
  common.Protocol protocol = 14;                      // 协议
  repeated common.RateLimitV2 rate_limits = 15;       // 限流配置
  repeated common.PerfCaseStepV2 serial_steps = 16;   // 串行步骤列表
  repeated common.PerfCaseStepV2 parallel_steps = 17; // 并行步骤列表


  uint32 number_of_steps = 21; // 测试步骤数

  CommonState state = 31; // 状态
  string maintained_by = 32; // 压测用例维护者

  string created_by = 96; // 创建者
  string updated_by = 97; // 更新者
  int64 created_at = 98;  // 创建时间
  int64 updated_at = 99;  // 更新时间
}

message UpdatePerfPlanByCaseTaskInfo {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  string plan_id = 2 [(validate.rules).string = {pattern: "(?:^perf_plan_id:.+?)"}]; // 计划ID
  string case_id = 3 [(validate.rules).string = {pattern: "(?:^perf_case_id:.+?)"}]; // 用例ID
}
