syntax = "proto3";

package manager;

option go_package = "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb";

import "manager/base.proto";
import "manager/element.proto";


message ApiCase {
  string project_id = 1; // 项目ID
  string category_id = 2; // 所属分类ID
  string case_id = 3; // 用例ID
  string name = 4; // 用例名称
  string description = 5; // 用例描述
  int64 priority = 6; // 优先级
  repeated string tags = 7; // 标签
  ResourceState state = 8; // 状态
  AccountConfig account_config = 9; // 池账号配置信息
  string version = 10; // 用例版本
  string maintained_by = 11; // 维护者
  string created_by = 12; // 创建者
  string updated_by = 13; // 更新者
  int64  created_at = 14; // 创建时间
  int64  updated_at = 15; // 更新时间
  repeated Node nodes = 16; // 点
  repeated Edge edges = 17; // 线
  repeated Combo combos = 18; // 框
}

message SearchApiCaseReferenceItem {
  string project_id = 1; // 项目ID
  string case_id = 2; // 用例ID
  string reference_type = 3; // 引用对象类型
  string reference_id = 4; // 引用对象ID
  string name = 5; // 引用对象名称
  string description = 6; // 引用对象描述
  int64 priority = 7; // 优先级
  repeated string tags = 8; // 引用对象标签
  CommonState state = 9; // 引用对象状态（API集合的状态）
  string maintained_by = 10; // 维护者
  string created_by = 11; // 创建者
  string updated_by = 12; // 更新者
  int64  created_at = 13; // 创建时间
  int64  updated_at = 14; // 更新时间
}
