syntax = "proto3";

package manager;

option go_package = "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb";


message PerfData {
  string project_id = 1; // 项目ID
  string data_id = 2; // 压测数据ID
  string name = 3; // 压测数据名称
  string description = 4; // 压测数据描述
  string extension = 5; // 压测数据文件的扩展名
  string hash = 6; // 压测数据文件的一致性哈希值
  uint32 size = 7; // 压测数据文件的大小
  uint32 number_of_vu = 8; // 虚拟用户数

  string created_by = 96; // 创建者
  string updated_by = 97; // 更新者
  int64 created_at = 98; // 创建时间
  int64 updated_at = 99; // 更新时间
}
