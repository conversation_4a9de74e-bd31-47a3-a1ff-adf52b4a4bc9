syntax = "proto3";

package manager;

option go_package = "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb";


message CategoryNode {
  string project_id = 1;
  string category_id = 2;
  string type = 3;
  string category_type = 4;
  string root_type = 5;
  string node_type = 6;
  string node_id = 7;
  string name = 8;
  string description = 9;
  bool builtin = 10;
}

message Category {
  string project_id = 1;
  string category_id = 2;
  string type = 3;
  string category_type = 4;
  string root_type = 5;
  string node_type = 6;
  string node_id = 7;
  string name = 8;
  string description = 9;
  bool builtin = 10;
  string created_by = 11;
  string updated_by = 12;
  int64 created_at = 13;
  int64 updated_at = 14;
  int64 amount = 15;
  repeated Category children = 16;
}
