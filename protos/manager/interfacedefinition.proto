syntax = "proto3";

package manager;

option go_package = "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb";

import "google/protobuf/struct.proto";
import "validate/validate.proto";

import "manager/base.proto";
import "manager/category.proto";
import "manager/component.proto";
import "manager/element.proto";
import "reporter/reporter.proto";


message EnumValDesc {
  google.protobuf.Value value = 1;
  string description = 2;
}

message RefSchema {
  string schemaId = 1;
  string fullName = 2;
  string displayName = 3;
  repeated CategoryNode categoryPath = 4;
}

message TextDescExample {
  string description = 1;
  string example = 2;
}

message BodyData {
  string type = 1 [(validate.rules).string = {in: ["none", "multipart/form-data", "application/x-www-form-urlencoded", "application/json", "text/plain"]}]; // 请求体类型
  repeated Schema form = 2; // 请求体表单信息
  Schema json = 3; // 请求体JSON信息
  TextDescExample text = 4; // 请求体文本信息
}

message ResponseData {
  string status_code = 1; // 状态码
  string description = 2; // 响应描述
  repeated Schema headers = 3; // 响应头信息
  BodyData body = 4; // 响应体信息
}

message Schema {
  string title = 1 [(validate.rules).string.ignore_empty = true]; // 字段名称
  string type = 2 [(validate.rules).string = {in: ["string", "integer", "number", "boolean", "array", "object", "null", "any", "allOf", "anyOf", "oneOf", "custom", "schema", "file"]}]; // 字段类型
  string description = 3; // 字段描述
  int32 index = 4; // 字段序号 // 注：不能定义为`int64`，否则序列化的时候会被转为`string`，再反序列化的时候就会报错
  bool fieldRequired = 5; // 字段是否必需
  bool deprecated = 6; // 字段是否不建议使用
  google.protobuf.Value default = 7; // 字段默认值
  google.protobuf.Value example = 8; // 字段样例
  string format = 9 [(validate.rules).string = {ignore_empty: true, in: ["int32", "int64", "float", "double", "password", "date-time", "date", "time", "duration", "email", "hostname", "ipv4", "ipv6", "uri", "regex"]}]; // 字段值格式
  int32 multipleOf = 10; // 注：不能定义为`int64`，否则序列化的时候会被转为`string`，再反序列化的时候就会报错
  float maximum = 11;
  float exclusiveMaximum = 12;
  float minimum = 13;
  float exclusiveMinimum = 14;
  int32 maxLength = 15; // 注：不能定义为`int64`，否则序列化的时候会被转为`string`，再反序列化的时候就会报错
  int32 minLength = 16; // 注：不能定义为`int64`，否则序列化的时候会被转为`string`，再反序列化的时候就会报错
  string pattern = 17;
  int32 maxItems = 18; // 注：不能定义为`int64`，否则序列化的时候会被转为`string`，再反序列化的时候就会报错
  int32 minItems = 19; // 注：不能定义为`int64`，否则序列化的时候会被转为`string`，再反序列化的时候就会报错
  bool uniqueItems = 20;
  repeated google.protobuf.Value enum = 21;
  repeated EnumValDesc enums = 22;
  Schema items = 23;
  RefSchema ref = 24;
  map<string, Schema> properties = 25;
  repeated string required = 26;
  string sortKey = 27; // 前端创建的字段
  string raw = 28;
}

message InterfaceSchema {
  string project_id = 1; // 项目ID
  string category_id = 2; // 分类ID
  string schema_id = 3; // 数据模型ID
  string full_name = 4; // 数据模型完整名称
  string name = 5; // 数据模型名称
  string description = 6; // 数据模型描述
  string mode = 7; // 创建方式
  string import_type = 8; // 导入类型
  Schema data = 9; // 数据模型数据
  string created_by = 10;
  string updated_by = 11;
  int64 created_at = 12;
  int64 updated_at = 13;
}

message Document {
  repeated Schema headers = 1; // 请求头信息
  repeated Schema path_params = 2; // 路径参数信息
  repeated Schema query_params = 3; // 查询参数信息
  BodyData body = 4; // 请求体信息
  map<string, ResponseData> responses = 5; // 响应信息
}

message InterfaceDocument {
  string project_id = 1; // 项目ID
  string category_id = 2; // 分类ID
  string document_id = 3; // 接口ID
  string name = 4; // 接口名称
  string description = 5; // 接口描述
  string type = 6; // 接口类型
  string mode = 7; // 创建方式
  string import_type = 8; // 导入类型
  int64 status = 9; // 接口状态
  int64 priority = 10; // 优先级
  repeated string tags = 11; // 标签
  CommonState state = 12; // 接口集合状态
  ExecutionMode case_execution_mode = 13; // 用例执行方式
  string service = 14; // 服务名称
  string path = 15; // 接口路径
  string method = 16; // 接口方法
  Document data = 17; // 接口详细数据
  string maintained_by = 18; // 维护者
  string created_by = 19; // 创建者
  string updated_by = 20; // 更新者
  int64 created_at = 21; // 创建时间
  int64 updated_at = 22; // 更新时间
}

message InputParameter {
  string name = 1 [(validate.rules).string = {ignore_empty: true, min_len: 1}]; // 入参变量名称
  string description = 2; // 入参变量描述
  VariableSource source = 3 [(validate.rules).enum.defined_only = true]; // 来源
  VariableManual manual = 4 [(validate.rules).message.required = true]; // 手工填写
  VariableExport export = 5 [(validate.rules).message.required = true]; // 前面节点导出
  VariableEnvironment environment = 6 [(validate.rules).message.required = true]; // 通用配置
  VariableFunction function = 7 [(validate.rules).message.required = true]; // 函数处理
}

message OutputParameter {
  string name = 1 [(validate.rules).string = {ignore_empty: true, min_len: 1}]; // 出参变量名称
  ResponseSource source = 2 [(validate.rules).enum.defined_only = true]; // 来源
  VariableHeader headers = 3 [(validate.rules).message.required = true]; // 响应头
  VariableBody body = 4 [(validate.rules).message.required = true]; // 响应体
}

message InterfaceConfig {
  string project_id = 1; // 项目ID
  string document_id = 2; // 接口ID
  string config_id = 3; // 配置ID
  string name = 4; // 配置名称
  string description = 5; // 配置描述
  string path = 6; // 接口路径
  string method = 7; // 接口方法
  Document data = 8; // 接口配置数据
  repeated InputParameter input_parameters = 9 [json_name = "imports"]; // 入参列表
  repeated OutputParameter output_parameters = 10 [json_name = "exports"]; // 出参列表
  string created_by = 11; // 创建者
  string updated_by = 12; // 更新者
  int64 created_at = 13; // 创建时间
  int64 updated_at = 14; // 更新时间
}

message InterfaceCase {
  string project_id = 1; // 项目ID
  string document_id = 2; // 接口ID
  string case_id = 3; // 用例ID
  string name = 4; // 用例名称
  string description = 5; // 用例描述
  int64 priority = 6; // 优先级
  repeated string tags = 7; // 标签
  ResourceState state = 8; // 状态
  AccountConfig account_config = 9; // 池账号配置信息
  string version = 10; // 用例版本
  reporter.GetCaseLatestRecordResponse.RecordCaseRecord latest_record = 11;
  string maintained_by = 12; // 维护者
  string created_by = 13; // 创建者
  string updated_by = 14; // 更新者
  int64  created_at = 15; // 创建时间
  int64  updated_at = 16; // 更新时间
  repeated Node nodes = 17; // 点
  repeated Edge edges = 18; // 线
  repeated Combo combos = 19; // 框
}

// WithConfig 使用接口配置的方式
enum WithConfig {
  DO_NOT_USE = 0; // 不使用
  AUTOMATIC = 1; // 自动选择
  SPECIFIED = 2; // 使用指定的
}

message SearchInterfaceDocumentReferenceItem {
  string project_id = 1; // 项目ID
  string document_id = 2; // 接口ID
  string reference_type = 3; // 引用对象类型
  string reference_id = 4; // 引用对象ID
  string name = 5; // 引用对象名称
  string description = 6; // 引用对象描述
  int64 priority = 7; // 优先级
  repeated string tags = 8; // 引用对象标签
  CommonState state = 9; // 引用对象状态（接口集合的状态）
  CommonState reference_state = 10; // 引用状态（接口集合的引用状态）
  string maintained_by = 11; // 维护者
  string created_by = 12; // 创建者
  string updated_by = 13; // 更新者
  int64  created_at = 14; // 创建时间
  int64  updated_at = 15; // 更新时间
}

message UpdateInterfaceCoverageTaskInfo {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  int64 keep_days = 2 [(validate.rules).int64 = {gte: 1, lte: 365}]; // 保留天数
}

message UpdateInterfaceDefinitionTaskInfo {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  string type = 2 [(validate.rules).string = {in: ["OpenApi", "gRPC", "YApi", "TT", "TTMeta", "Recommend"]}]; // 接口类型
  string local_path = 3 [(validate.rules).string = {min_len: 1}]; // 本地路径
  repeated string dep_local_paths = 4 [(validate.rules).repeated = {ignore_empty: true, items: {string: {min_len: 1}}}]; // 依赖本地路径
}

message SearchInterfaceCaseReferenceItem {
  string project_id = 1; // 项目ID
  string case_id = 2; // 用例ID
  string reference_type = 3; // 引用对象类型
  string reference_id = 4; // 引用对象ID
  string name = 5; // 引用对象名称
  string description = 6; // 引用对象描述
  int64 priority = 7; // 优先级
  repeated string tags = 8; // 引用对象标签
  CommonState state = 9; // 引用对象状态（API集合的状态）
  string maintained_by = 10; // 维护者
  string created_by = 11; // 创建者
  string updated_by = 12; // 更新者
  int64  created_at = 13; // 创建时间
  int64  updated_at = 14; // 更新时间
}

message InterfaceCoverageData {
  repeated uint32 number_of_apis = 1; // 接口数量
  repeated uint32 number_of_cases = 2; // 用例数量
  repeated string counted_at = 3; // 统计日期
}
