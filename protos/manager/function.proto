syntax = "proto3";

package manager;

option go_package = "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb";


// FunctionType 函数类型
enum FunctionType {
  BUILTIN = 0; // 内置
  CUSTOM = 1; // 自定义
}

// CodeLanguage 编程语言
enum CodeLanguage {
  GOLANG = 0; // GoLang
  PYTHON = 1; // Python
}

// ParameterOrReturnType 参数或返回值类型
enum ParameterOrReturnType {
  STRING = 0; // 字符串
  NUMBER = 1; // 数字
  ARRAY = 2; // 列表
  OBJECT = 3; // 对象
  BOOLEAN = 4; // 布尔
  NULL = 5; // 空值
  ANY = 6; // 任意类型
}

message Parameter {
  string name = 1; // 参数名称
  string description = 2; // 参数描述
  ParameterOrReturnType type = 3; // 参数类型
  string default = 4; // 参数默认值
  bool variadic = 5; // 是否可变参数
}

message Return {
  string name = 1; // 返回值名称
  string description = 2; // 返回值描述
  ParameterOrReturnType type = 3; // 返回值类型
}

message DataProcessingFunction {
  string project_id = 1; // 项目ID
  string name = 2; // 函数名称
  FunctionType type = 3; // 函数类型
  string category = 4; // 函数分类
  string description = 5; // 函数描述
  CodeLanguage language = 6; // 编程语言
  string content = 7; // 函数内容
  repeated Parameter parameters = 8; // 参数列表
  repeated Return returns = 9; // 返回值列表
  string example = 10; // 函数使用例子
  string version = 11; // 函数版本
  string created_by = 12;
  string updated_by = 13;
  int64  created_at = 14;
  int64  updated_at = 15;
}
