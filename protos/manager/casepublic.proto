syntax = "proto3";

package manager;

option go_package = "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb";

import "manager/base.proto";
import "manager/element.proto";


message Case {
	string project_id = 1; // 项目ID
	string branch_id = 2; // 分支ID
	string case_id = 3; // 用例ID
	string name = 4; // 用例名称
	string description = 5; // 用例描述
	int64 priority = 6; // 优先级
	repeated string tags = 7; // 标签
	string state = 8; // 状态
	AccountConfig account_config = 9; // 池账号配置信息
	string version = 10; // 用例版本
	string maintained_by = 11; // 维护者
	string created_by = 12; // 创建者
	string updated_by = 13; // 更新者
	int64  created_at = 14; // 创建时间
	int64  updated_at = 15; // 更新时间
	repeated Node nodes = 16;
	repeated Edge edges = 17;
	repeated Combo combos = 18;
	string case_type = 19; // 用例ID
}

message FailCase {
	string project_id = 1; // 项目ID
	string branch_id = 2; // 分支ID
	string case_id = 3; // 用例ID
	string name = 4; // 用例名称
	string description = 5; // 用例描述
	int64 priority = 6; // 优先级
	repeated string tags = 7; // 标签
	string state = 8; // 状态
	AccountConfig account_config = 9; // 池账号配置信息
	string version = 10; // 用例版本
	string maintained_by = 11; // 维护者
	string created_by = 12; // 创建者
	string updated_by = 13; // 更新者
	int64  created_at = 14; // 创建时间
	int64  updated_at = 15; // 更新时间
	repeated Node nodes = 16;
	repeated Edge edges = 17;
	repeated Combo combos = 18;
	string case_type = 19; // 用例类型
	uint32 fail_count = 21; // 失败次数
	uint32 rank = 22; // 排名
}

message CaseFailStatForMq {
	string project_id = 1; // 项目ID
	string branch_id = 2; // 分支ID
	string execute_id = 3; // 执行ID
	string case_id = 4; // 用例ID
	string case_type = 5; // 用例类型
}
