syntax = "proto3";

package manager;

option go_package = "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb";


message AdvancedSearchField {
  string field_id = 1;
  string project_id = 2;
  string field_type = 3;
  SceneType scene_type = 4 ;  // 场景类型
  string front_name = 5;   // 前端显示名称
  string field_name = 6;  // 集合描述
  int64 created_at = 7;  // 创建时间
  int64 updated_at = 8;  // 更新时间
}

message AdvancedSearchCondition {
  string condition_id = 1;
  string front_name = 2 ;  // 前端显示名称
  string compare = 3;   // 对比方式
  int64 created_at = 4; // 创建时间
  int64 updated_at = 5;  // 更新时间
}

enum SceneType {
  ST_NULL = 0;
  ST_API_SUITE = 1;
  ST_INTERFACE_DOCUMENT = 2;
}

// 预留给高级搜索字段管理用
enum FieldType {
  FT_NULL = 0;
  FT_STRING = 1;
  FT_TIMESTAMP = 2;
}