syntax = "proto3";

package manager;

option go_package = "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb";

import "validate/validate.proto";

import "common/enum.proto";
import "manager/base.proto";


message ApiPlan {
  string project_id = 1; // 项目ID
  string plan_id = 2; // 计划ID
  string name = 3; // 计划名称
  string description = 4; // 集合描述
  int64 priority = 5; // 优先级
  repeated string tags = 6; // 标签
  CommonState state = 7; // 状态
  common.TriggerMode type = 8; // 计划类型（手动、定时、接口）
  common.PurposeType purpose = 9; // 计划用途(常规、精准测试)
  string cron_expression = 10; // 定时触发计划的Cron表达式
  string general_config_id = 11; // 通用配置ID
  repeated string account_config_ids = 12; // 池账号配置ID列表
  ExecutionMode suite_execution_mode = 13;  // 集合执行方式
  ExecutionMode case_execution_mode = 14; // 用例执行方式
  string maintained_by = 15; // 维护者
  string created_by = 16; // 创建者
  string updated_by = 17; // 更新者
  int64  created_at = 18; // 创建时间
  int64  updated_at = 19; // 更新时间
  string category_id = 20; // 分类ID
}

message SuiteTypeId {
  string suite_type = 1 [(validate.rules).string = {in: ["API_SUITE", "INTERFACE_DOCUMENT"]}]; // 集合类型
  string suite_id = 2 [(validate.rules).string = {pattern: "(?:^suite_id:.+?|^interface_document_id:.+?)"}]; // 集合ID
}

message SearchApiPlanItem {
  string project_id = 1; // 项目ID
  string plan_id = 2; // 计划ID
  string name = 3; // 计划名称
  string description = 4; // 计划描述
  int64 priority = 5; // 优先级
  repeated string tags = 6; // 标签
  CommonState state = 7; // 状态
  common.TriggerMode type = 8; // 计划类型（手动、定时、接口）
  common.PurposeType purpose = 9; // 计划用途
  string cron_expression = 10; // 定时触发计划的Cron表达式
  ExecutionMode suite_execution_mode = 11; // 集合执行方式
  ExecutionMode case_execution_mode = 12; // 用例执行方式
  int64 suite_included = 13; // 包含的集合数
  string maintained_by = 14; // 维护者
  string created_by = 15; // 创建者
  string updated_by = 16; // 更新者
  int64  created_at = 17; // 创建时间
  int64  updated_at = 18; // 更新时间
  string category_id = 19; // 分类ID
}

message SearchSuiteInApiPlanItem {
  string project_id = 1; // 项目ID
  string suite_type = 2; // 集合类型
  string suite_id = 3; // 集合ID
  string name = 4; // 集合名称
  string description = 5; // 集合描述
  int64 priority = 6; // 优先级
  repeated string tags = 7; // 标签
  CommonState state = 8; // 状态
  CommonState reference_state = 9; // 引用状态
  int64 case_included = 10; // 包含的用例数
  int64 case_skipped = 11; // 跳过的用例数
  string maintained_by = 12; // 维护者
  string created_by = 13; // 创建者
  string updated_by = 14; // 更新者
  int64  created_at = 15; // 创建时间
  int64  updated_at = 16; // 更新时间
}

message SearchSuiteNotInApiPlanItem {
  string project_id = 1; // 项目ID
  string category_id = 2; // 分类ID
  string suite_id = 3; // 集合ID
  string name = 4; // 集合名称
  string description = 5; // 集合描述
  int64 priority = 6; // 优先级
  repeated string tags = 7; // 标签
  CommonState state = 8; // 状态
  string maintained_by = 9; // 维护者
  string created_by = 10; // 创建者
  string updated_by = 11; // 更新者
  int64  created_at = 12; // 创建时间
  int64  updated_at = 13; // 更新时间
}

message SearchCaseInApiPlanItem {
  string project_id = 1; // 项目ID
//  string category_id = 2; // 分类ID
  string case_id = 3; // 用例ID
  string name = 4; // 用例名称
  string description = 5; // 用例描述
  int64 priority = 6; // 优先级
  repeated string tags = 7; // 标签
  ResourceState state = 8; // 状态
  CommonState reference_state = 9; // 引用状态
  string maintained_by = 10; // 维护者
  string created_by = 11; // 创建者
  string updated_by = 12; // 更新者
  int64  created_at = 13; // 创建时间
  int64  updated_at = 14; // 更新时间
  string case_type = 15 [(validate.rules).string = {in: ["API_CASE", "INTERFACE_CASE"]}]; // 用例类型
}

message AdvancedSearchSuiteItem {
  string project_id = 1;
  string category_id = 2;
  string suite_id = 3;
  string name = 4;
  string description = 5;
  int64 priority = 6; // 优先级
  repeated string tags = 7; // 标签
  int64 state = 8;
  int64 case_count = 9;
  string maintained_by = 10;
  string created_by = 11;
  string updated_by = 12;
  int64 created_at = 13;
  int64 updated_at = 14;
}
