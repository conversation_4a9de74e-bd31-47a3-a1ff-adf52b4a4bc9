syntax = "proto3";

package manager;

option go_package = "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb";


message GitConfiguration {
  string project_id = 1; // 项目ID
  string config_id = 2; // Git配置ID
  string type = 3; // Git类型（GitLab、GitHub、Gitee）
  string name = 4; // Git配置名称
  string description = 5; // Git配置描述
  string url = 6; // Git项目URL（http）
  string access_token = 7; // Git项目访问令牌
  string branch = 8; // Git项目分支名称
  string purpose = 9; // 用途
  
  string created_by = 96; // 创建者
  string updated_by = 97; // 更新者
  int64 created_at = 98; // 创建时间
  int64 updated_at = 99; // 更新时间
}
