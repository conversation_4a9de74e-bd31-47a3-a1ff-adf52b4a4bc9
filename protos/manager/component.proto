syntax = "proto3";

package manager;

option go_package = "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb";

import "validate/validate.proto";

import "common/config.proto";
import "common/enum.proto";
import "common/limit.proto";
import "common/load.proto";
import "common/perf.proto";
import "common/lark.proto";
import "common/stability.proto";
import "common/ui_agent.proto";
import "manager/base.proto";
import "manager/function.proto";


// VariableSource 变量来源
enum VariableSource {
  MANUAL = 0; // 手写
  EXPORT = 1; // 出参
  ENVIRONMENT = 2; // 通用配置
  FUNCTION = 3; // 数据处理函数
}
message VariableManual {
  string value = 1 [(validate.rules).string = {ignore_empty: true, min_len: 1}]; // 手写的值
}
message VariableExport {
  string node_id = 1 [(validate.rules).string = {ignore_empty: true, min_len: 1}]; // 来源节点ID
  string value = 2 [(validate.rules).string = {ignore_empty: true, min_len: 1}]; // 来源节点的出参变量名称
}
message VariableEnvironment {
  string value = 1 [(validate.rules).string = {ignore_empty: true, min_len: 1}]; // 通用配置中的变量名称
}
message VariableFunction {
  message Parameter {
    string name = 1 [(validate.rules).string = {ignore_empty: true, min_len: 1}]; // 参数名称
    VariableSource source = 2 [(validate.rules).enum = {not_in: [3]}];
    VariableManual manual = 3 [(validate.rules).message.required = true];
    VariableExport export = 4 [(validate.rules).message.required = true];
    VariableEnvironment environment = 5 [(validate.rules).message.required = true];
    VariableFunction function = 6 [(validate.rules).message.skip = true];
  }

  string name = 1 [(validate.rules).string = {ignore_empty: true, min_len: 1}]; // 函数名称
  FunctionType type = 2 [(validate.rules).enum.defined_only = true]; // 函数类型
  repeated Parameter parameters = 3 [(validate.rules).repeated.ignore_empty = true]; // 参数列表
}
message VariableHeader {
  string key = 1 [(validate.rules).string = {ignore_empty: true, min_len: 1}]; // 响应头的键
}
message VariableBody {
  ExtractType type = 1 [(validate.rules).enum.defined_only = true]; // 提取类型
  string expression = 2 [(validate.rules).string = {ignore_empty: true, min_len: 1}]; // 提取表达式
}

// Import 入参
message Import {
  string name = 1 [(validate.rules).string = {ignore_empty: true, min_len: 1}]; // 入参变量名称
  VariableSource source = 2 [(validate.rules).enum.defined_only = true];
  VariableManual manual = 3 [(validate.rules).message.required = true];
  VariableExport export = 4 [(validate.rules).message.required = true];
  VariableEnvironment environment = 5 [(validate.rules).message.required = true];
  VariableFunction function = 6; // 为了兼容旧数据，这里不能做`validate`
  string description = 7;
}

// Export 出参
message Export {
  string name = 1 [(validate.rules).string = {ignore_empty: true, min_len: 1}]; // 出参变量名称
  VariableExport export = 2 [(validate.rules).message.required = true];
  string description = 3;
}

// ConditionType 条件类型
enum ConditionType {
  SINGLE = 0; // 单条件
  GROUP = 1; // 条件组
}

// Relationship 逻辑关系
enum Relationship {
  AND = 0; // 且
  OR = 1; // 或
}

// ExtractType 提取类型
enum ExtractType {
  JMESPATH = 0; // JMESPath
  REGEX = 1; // 正则
}

// ResponseSource 响应信息提取来源
enum ResponseSource {
  HEADERS = 0;
  BODY = 1;
  STATUS_CODE = 2;
}

// DataSourceType 数据源类型
enum DataSourceType {
  MYSQL = 0; // MySQL
  POSTGRESQL = 1; // PostgreSQL
}

// ComponentGroupComponent 组件组组件（注：把组件组抽象为一个组件）
message ComponentGroupComponent {
  string project_id = 1; // 项目ID
  string component_group_id = 2; // 组件组ID
  string name = 3; // 组件组名称
  string description = 4; // 组件组描述
  string version = 5; // 版本
  repeated Import imports = 6; // 入参列表
  repeated Export exports = 7; // 出参列表
}

// CaseComponent 用例组件（注：把用例抽象为一个组件）
message CaseComponent {
  string project_id = 1; // 项目ID
  string case_id = 2; // 用例ID
  string name = 3; // 用例名称
  string description = 4; // 用例描述
  ResourceState state = 5; // 用例状态
  CommonState reference_state = 6; // 用例引用状态（获取API计划执行结构时，此字段才有效）
  string version = 7; // 版本
  string maintained_by = 8; // 维护者的用户ID
}

// SuiteComponent 集合组件（注：把集合抽象为一个组件）
message SuiteComponent {
  string project_id = 1; // 项目ID
  string suite_id = 2; // 集合ID
  string name = 3; // 集合名称
  string description = 4; // 集合描述
  CommonState state = 5; // 集合状态
  CommonState reference_state = 6; // 集合引用状态（获取API计划执行结构时，此字段才有效）
  ExecutionMode case_execution_mode = 7; // 用例执行方式
}

// PlanComponent 计划组件（注：把计划抽象为一个组件）
message PlanComponent {
  string project_id = 1; // 项目ID
  string plan_id = 2; // 计划ID
  string name = 3; // 计划名称
  string description = 4; // 计划描述
  CommonState state = 5; // 计划状态
  common.TriggerMode type = 6; // 计划类型（触发模式）
  common.PurposeType purpose = 7; // 计划用途
  common.GeneralConfig general_config = 8; // 通用配置
  repeated common.AccountConfig account_configs = 9; // 池账号配置列表
  ExecutionMode suite_execution_mode = 10; // 集合执行方式
  ExecutionMode case_execution_mode = 11; // 用例执行方式
  string maintained_by = 12; // 维护者的用户ID
  string created_by = 13; // 创建者的用户ID
  string updated_by = 14; // 更新者的用户ID
}

// InterfaceCaseComponent 接口用例组件（注：把接口用例抽象为一个组件）
message InterfaceCaseComponent {
  string project_id = 1; // 项目ID
  string document_id = 2; // 接口ID
  string case_id = 3; // 接口用例ID
  string name = 4; // 接口用例名称
  string description = 5; // 接口用例描述
  ResourceState state = 6; // 接口用例状态
  CommonState reference_state = 7; // 接口用例引用状态（获取API计划执行结构时，此字段才有效）
  string version = 8; // 版本
  string maintained_by = 9; // 维护者的用户ID
}

// InterfaceDocumentComponent 接口文档（集合）组件（注：把接口集合抽象为一个组件）
message InterfaceDocumentComponent {
  string project_id = 1; // 项目ID
  string document_id = 2; // 接口ID
  string name = 3; // 接口名称
  string description = 4; // 接口描述
  CommonState state = 5; // 接口集合状态
  CommonState reference_state = 6; // 接口集合引用状态（获取API计划执行结构时，此字段才有效）
  ExecutionMode case_execution_mode = 7; // 用例执行方式
}

// UICaseComponent UI用例组件（注：把UI用例抽象为一个组件）
message UICaseComponent {
  string project_id = 1; // 项目ID
  string case_id = 2; // 用例ID
  string name = 3; // 用例名称
  string alias = 4; // 用例别名
  string path = 5; // 用例路径
  CommonState state = 6; // 用例状态

  string udid = 11; // 指定的设备编号
}

// UISuiteComponent UI集合组件（注：把UI组合抽象为一个组件）
message UISuiteComponent {
  string project_id = 1; // 项目ID
  string suite_id = 2; // 集合ID
  string name = 3; // 集合名称
  string alias = 4; // 集合别名
  string path = 5; // 集合路径
  CommonState state = 6; // 集合状态
}

message UIPlanMetaData {
  common.PriorityType priority_type = 1; // 优先级
  common.GitConfig git_config = 2; // Git配置
  ExecutionMode suite_execution_mode = 3; // 集合执行方式
  ExecutionMode case_execution_mode = 4; // 用例执行方式
  common.DeviceType device_type = 5; // 设备类型（真机、云手机）
  common.PlatformType platform_type = 6; // 平台类型（Android、iOS）
  string package_name = 7; // 包名，用于启动APP
  string app_name = 8; // 应用名称
  string callback_url = 9; // 回调地址
  string app_download_link = 10; // App下载地址
  string app_version = 11; // App版本
  common.TestLanguage test_language = 12; // 测试语言 0：Python 1：Golang
  string test_language_version = 13; // 测试语言版本
  common.TestFramework test_framework = 14; // 测试框架 1：pytest
  repeated string test_args = 15; // 附加参数
  string execution_environment = 16; // 执行环境
  common.FailRetry fail_retry = 17; // 失败重试

  repeated string devices = 21; // 设备列表
  bool together = 22; // 选择的设备是否一起执行
}

// UIPlanComponent UI计划组件（注：把UI计划抽象为一个组件）
message UIPlanComponent {
  string project_id = 1; // 项目ID
  string plan_id = 2; // 计划ID
  string name = 3; // 计划名称
  string description = 4; // 计划描述
  CommonState state = 5; // 计划状态
  common.TriggerMode type = 6; // 计划类型（触发模式）
  UIPlanMetaData meta_data = 7; // 元数据
  string maintained_by = 8; // 维护者的用户ID
  string created_by = 9; // 创建者的用户ID
  string updated_by = 10; // 更新者的用户ID
}

message ServiceComponent {
  string project_id = 1;  // 项目id
  string service_name = 2; // 服务名
  string service_id = 3; // 服务id
}

// PerfDataComponent 压测数据组件（注：把压测数据抽象为一个组件）
message PerfDataComponent {
  string project_id = 1; // 项目ID
  string data_id = 2; // 压测数据ID
  string name = 3; // 压测数据名称
  string description = 4; // 压测数据描述
  string extension = 5; // 压测数据文件的扩展名
  string hash = 6; // 压测数据文件的一致性哈希值
  uint32 size = 7; // 压测数据文件的大小
  string path = 8; // 压测数据文件的路径
  uint32 number_of_vu = 9; // 虚拟用户数
}

// PerfCaseComponent 压测用例组件（注：把压测用例抽象为一个组件）
message PerfCaseComponent {
  string project_id = 1; // 项目ID
  string case_id = 2; // 用例ID

  string name = 3; // 用例名称
  string description = 4; // 用例描述
  string extension = 5; // Deprecated: 压测用例文件的扩展名
  string hash = 6; // Deprecated: 压测用例文件的一致性哈希值
  uint32 size = 7; // Deprecated: 压测用例文件的大小
  string path = 8; // Deprecated: 压测用例文件的路径
  int64 target_rps = 9; // 目标的RPS
  repeated common.RateLimitV2 rate_limits = 10; // 限流配置

  repeated common.PerfCaseStepV2 setup_steps = 21;    // 前置步骤列表
  repeated common.PerfCaseStepV2 serial_steps = 22;   // 串行步骤列表
  repeated common.PerfCaseStepV2 parallel_steps = 23; // 并行步骤列表
  repeated common.PerfCaseStepV2 teardown_steps = 24; // 后置步骤列表

  PerfDataComponent perf_data = 31; // 压测数据
  uint32 number_of_vu = 32; // 虚拟用户数

  common.LoadGenerator load_generator = 41; // 施压机资源

  CommonState state = 51; // 用例状态
}

// PerfSuiteComponent 压测集合组件（注：把压测集合抽象为一个组件）
message PerfSuiteComponent {
  string project_id = 1; // 项目ID
  string suite_id = 2; // 集合ID

  string name = 11; // 集合名称
  string description = 12; // 集合描述

  CommonState state = 21; // 集合状态
}

// PerfPlanMetaData 压测计划元数据
message PerfPlanMetaData {
  common.Protocol protocol = 1; // 协议
  common.TargetEnvironment target_env = 2; // 目标环境
  repeated common.ProtobufConfig protobuf_configs = 3; // Protobuf配置列表
  common.GeneralConfig general_config = 4; // 通用配置
  common.AccountConfig account_config = 5; // 池账号配置
  int64 target_max_rps = 6; // 目标最大的RPS
  uint32 duration = 7; // 压测持续时长
  common.PerfKeepalive keepalive = 8; // Deprecated: 保活参数
  uint32 delay = 9; // Deprecated: 延迟执行时间

  common.PerfRateLimits rate_limits = 21; // 压测相关的限流配置（包括：认证接口、心跳接口）
  repeated common.PerfServiceMetaData services = 22; // 压测相关的服务的元数据
  repeated common.PerfStopRule rules = 23; // 压测停止规则列表
}

// PerfPlanComponent 压测计划组件（注：把压测计划抽象为一个组件）
message PerfPlanComponent {
  string project_id = 1; // 项目ID
  string plan_id = 2; // 计划ID

  string name = 11; // 计划名称
  string description = 12; // 计划描述
  common.TriggerMode type = 13; // 计划类型（触发模式）
  PerfPlanMetaData meta_data = 14; // 元数据

  CommonState state = 21; // 计划状态
  string maintained_by = 22; // 维护者的用户ID

  string created_by = 96; // 创建者的用户ID
  string updated_by = 97; // 更新者的用户ID
}

// StabilityPlanMetaData 稳定性测试计划元数据
message StabilityPlanMetaData {
  common.AccountConfig account_config = 1; // 池账号配置
  repeated common.LarkChat lark_chats = 2; // 飞书群组列表

  common.DeviceType device_type = 11; // 设备类型（真机、云手机）
  common.PlatformType platform_type = 12; // 平台类型（Android、iOS）
  common.StabilityCustomDevices devices = 13; // 自定义设备

  string package_name = 21; // App包名
  string app_version = 22; // App版本
  string app_download_link = 23; // App下载地址
  repeated string activities = 24; // 指定的Activity列表
  common.StabilityCustomScript custom_script = 25; // 自定义脚本

  uint32 duration = 31; // 执行时长
}

// StabilityPlanComponent 稳定性测试计划组件（注：把稳定测试性计划抽象为一个组件）
message StabilityPlanComponent {
  string project_id = 1; // 项目ID
  string plan_id = 2; // 计划ID

  string name = 11; // 计划名称
  string description = 12; // 计划描述
  common.TriggerMode type = 13; // 计划类型（触发模式）
  StabilityPlanMetaData meta_data = 14; // 元数据

  CommonState state = 21; // 计划状态
  string maintained_by = 22; // 维护者的用户ID

  string created_by = 96; // 创建者的用户ID
  string updated_by = 97; // 更新者的用户ID
}

// UIAgentComponentComponent `UI Agent`组件
message UIAgentComponentComponent {
  string project_id = 1; // 项目ID
  string component_id = 2; // 组件ID

  string name = 11; // 组件名称
  string description = 12; // 组件描述
  common.ApplicationConfig application_config = 13; // 应用配置
  bool step_by_step = 14; // 是否分步执行
  repeated common.UIAgentComponentStep steps = 15; // 步骤列表
  common.UIAgentComponentExpectation expectation = 16; // 期望结果
  repeated common.GeneralConfigVar variables = 17; // 变量列表

  CommonState state = 31; // 组件状态
  string maintained_by = 32; // 维护者的用户ID

  string created_by = 96; // 创建者的用户ID
  string updated_by = 97; // 更新者的用户ID
}

// UIAgentCaseComponent `UI Agent`用例组件（注：把`UI Agent`用例抽象为一个组件）
message UIAgentCaseComponent {}

// UIAgentPlanComponent `UI Agent`计划组件（注：把`UI Agent`计划抽象为一个组件）
message UIAgentPlanComponent {}

// StartComponent START组件
message StartComponent {}

// EndComponent END组件
message EndComponent {}

// SetupComponent 前置组件 - 框
message SetupComponent {
  repeated Import imports = 1 [(validate.rules).repeated.ignore_empty = true];
  repeated Export exports = 2 [(validate.rules).repeated.ignore_empty = true];
}

// TeardownComponent 后置组件 - 框
message TeardownComponent {
  repeated Import imports = 1 [(validate.rules).repeated.ignore_empty = true];
}

// BusinessSingleComponent 业务单请求 - 框
message BusinessSingleComponent {
  repeated Import imports = 1 [(validate.rules).repeated.ignore_empty = true];
  repeated Export exports = 2 [(validate.rules).repeated.ignore_empty = true];
}

// BusinessGroupComponent 业务行为组 - 框
message BusinessGroupComponent {
  repeated Import imports = 1 [(validate.rules).repeated.ignore_empty = true];
  repeated Export exports = 2 [(validate.rules).repeated.ignore_empty = true];
}

// LoopComponent 循环控制组件 - 框
message LoopComponent {
  enum Type {
    FOR = 0; // 次数循环
    FOR_EACH = 1; // ForEach循环
    WHILE = 2; // While循环
  }
  message For {
    int64 times = 1 [(validate.rules).int64.gt = 0]; // 循环次数
  }
  message ForEach {
    string name = 1 [(validate.rules).string = {ignore_empty: true, min_len: 1}]; // 循环对象变量名称
    VariableSource source = 2 [(validate.rules).enum.defined_only = true];
    VariableManual manual = 3 [(validate.rules).message.required = true];
    VariableExport export = 4 [(validate.rules).message.required = true];
    VariableEnvironment environment = 5 [(validate.rules).message.required = true];
    VariableFunction function = 6; // 为了兼容旧数据，这里不能做`validate`
  }
  message While {
    message SingleCondition {
      message Value {
        string name = 1 [(validate.rules).string = {ignore_empty: true, min_len: 1}]; // 条件判断[左|右]值变量名称
        VariableSource source = 2 [(validate.rules).enum.defined_only = true];
        VariableManual manual = 3 [(validate.rules).message.required = true];
        VariableExport export = 4 [(validate.rules).message.required = true];
        VariableEnvironment environment = 5 [(validate.rules).message.required = true];
        VariableFunction function = 6; // 为了兼容旧数据，这里不能做`validate`
      }

      Value left = 1 [(validate.rules).message.required = true]; // 左值
      string compare = 2 [(validate.rules).string = {in: ["EQ", "NE", "LT", "LE", "GT", "GE", "CONTAINS", "NOT_CONTAINS", "RE"]}]; // 比较方式
      Value right = 3 [(validate.rules).message.required = true]; // 右值
    }
    message GroupCondition {
      Relationship relationship = 1 [(validate.rules).enum.defined_only = true];
      repeated While conditions = 2 [(validate.rules).repeated.ignore_empty = true];
    }

    ConditionType type = 1 [(validate.rules).enum.defined_only = true];
    SingleCondition single = 2 [(validate.rules).message.required = true]; // 单条件
    GroupCondition group = 3 [(validate.rules).message.required = true]; // 条件组
  }

  Type type = 1 [(validate.rules).enum.defined_only = true]; // 循环类型
  int64 timeout = 2 [(validate.rules).int64.gte = 0]; // 循环超时时间
  For for = 3 [(validate.rules).message.required = true];
  ForEach for_each = 4 [(validate.rules).message.required = true];
  While while = 5 [(validate.rules).message.required = true];
}

// HttpRequestComponent HTTP请求组件
message HttpRequestComponent {
  message Authorization {
    enum Type {
      NO_AUTH = 0;
      API_KEY = 1;
      BEARER_TOKEN = 2;
      BASIC_AUTH = 3;
    }
    message ApiKey {
      enum AddTo {
        HEADERS = 0;
        QUERY_PARAMS = 1;
      }

      string key = 1 [(validate.rules).string = {ignore_empty: true, min_len: 1}];
      string value = 2 [(validate.rules).string = {ignore_empty: true, min_len: 1}];
      AddTo add_to = 3 [(validate.rules).enum.defined_only = true];
    }
    message BearerToken {
      string token = 1 [(validate.rules).string = {ignore_empty: true, min_len: 1}];
    }
    message BasicAuth {
      string username = 1 [(validate.rules).string = {ignore_empty: true, min_len: 1}];
      string password = 2 [(validate.rules).string = {ignore_empty: true, min_len: 1}];
    }

    Type type = 1 [(validate.rules).enum.defined_only = true]; // 授权类型
    ApiKey api_key = 2 [(validate.rules).message.required = true];
    BearerToken bearer_token = 3 [(validate.rules).message.required = true];
    BasicAuth basic_auth = 4 [(validate.rules).message.required = true];
  }

  message KeyValueDesc {
    string key = 1 [(validate.rules).string = {ignore_empty: true, min_len: 1}]; // 键
    string value = 2 [(validate.rules).string = {ignore_empty: true, min_len: 1}]; // 值
    string description = 3; // 描述
  }

  message Body {
    enum ContentType {
      NONE = 0; // none
      MULTIPART_FORM_DATA = 1; // multipart/form-data
      APPLICATION_FORM_URLENCODED = 2; // application/x-www-form-urlencoded
      TEXT_PLAIN = 3; // raw: text/plain
      APPLICATION_JSON = 4; // raw: application/json
    }

    ContentType type = 1 [(validate.rules).enum.defined_only = true];
    repeated KeyValueDesc form_data = 2 [(validate.rules).repeated.ignore_empty = true];
    string raw = 3;
  }

  message Timeout {
    int64 connect_timeout = 1 [(validate.rules).int64.gte = 0]; // 连接超时时间
    int64 request_timeout = 2 [(validate.rules).int64.gte = 0]; // 请求超时时间
    int64 response_timeout = 3 [(validate.rules).int64.gte = 0]; // 响应超时时间
  }

  message Assertion {
    message Headers {
      string key = 1 [(validate.rules).string = {ignore_empty: true, min_len: 1}]; // 响应头的键
      string compare = 2 [(validate.rules).string = {ignore_empty: true, in: ["EQ", "NE", "LEN_EQ", "LEN_NE", "LEN_LT", "LEN_LE", "LEN_GT", "LEN_GE", "CONTAINS", "NOT_CONTAINS", "STARTS_WITH", "ENDS_WITH", "RE"]}]; // 比较方式
      string expression = 3 [(validate.rules).string = {ignore_empty: true, min_len: 1}]; // 期望值或正则表达式
    }
    message Body {
      enum Type {
        JMESPATH = 0; // JMESPath
        REGEX = 1; // 正则
      }
      message JMESPath {
        string expression = 1 [(validate.rules).string = {ignore_empty: true, min_len: 1}]; // JMESPath表达式
        string compare = 2 [(validate.rules).string = {ignore_empty: true, in: ["EQ", "NE", "LT", "LE", "GT", "GE", "LEN_EQ", "LEN_NE", "LEN_LT", "LEN_LE", "LEN_GT", "LEN_GE", "CONTAINS", "NOT_CONTAINS", "RE"]}]; // 比较方式
        string expectation = 3 [(validate.rules).string = {ignore_empty: true, min_len: 1}]; // 期望值
      }
      message Regex {
        string expression = 1 [(validate.rules).string = {ignore_empty: true, min_len: 1}]; // Regex表达式
      }

      Type type = 1 [(validate.rules).enum.defined_only = true]; // 提取类型
      JMESPath jmespath = 2 [(validate.rules).message.required = true];
      Regex regex = 3 [(validate.rules).message.required = true];
    }
    message StatusCode {
      string compare = 1 [(validate.rules).string = {ignore_empty: true, in: ["EQ", "NE", "LT", "LE", "GT", "GE", "RE"]}]; // 比较方式
      string expectation = 2 [(validate.rules).string = {ignore_empty: true, min_len: 1}]; // 期望值或正则表达式
    }

    ResponseSource source = 1 [(validate.rules).enum.defined_only = true]; // 来源
    Headers headers = 2 [(validate.rules).message.required = true]; // 响应头
    Body body = 3 [(validate.rules).message.required = true]; // 响应体
    StatusCode status_code = 4 [(validate.rules).message.required = true]; // 响应状态码
  }

  message Export {
    message Headers {
      string key = 1 [(validate.rules).string = {ignore_empty: true, min_len: 1}]; // 响应头的键
    }
    message Body {
      enum Type {
        JMESPATH = 0; // JMESPath
        REGEX = 1; // 正则
      }

      Type type = 1 [(validate.rules).enum.defined_only = true]; // 提取类型
      string expression = 2 [(validate.rules).string = {ignore_empty: true, min_len: 1}]; // 提取表达式
    }

    string name = 1 [(validate.rules).string = {ignore_empty: true, min_len: 1}]; // 出参变量名称
    ResponseSource source = 2 [(validate.rules).enum.defined_only = true]; // 来源
    Headers headers = 3 [(validate.rules).message.required = true]; // 响应头
    Body body = 4 [(validate.rules).message.required = true]; // 响应体
  }

  string url = 1 [(validate.rules).string.min_len = 2]; // HTTP请求Url（注：由于这里可能使用到变量，因此不能使用`uri_ref`进行验证）
  string method = 2 [(validate.rules).string = {in: ["GET", "POST", "PUT", "DELETE"]}]; // HTTP请求Method
  Authorization authorization = 3 [(validate.rules).message.required = true];
  repeated KeyValueDesc headers = 4 [(validate.rules).repeated.ignore_empty = true];
  repeated KeyValueDesc query_params = 5 [(validate.rules).repeated.ignore_empty = true];
  Body body = 6 [(validate.rules).message.required = true];
  Timeout timeout = 7 [(validate.rules).message.required = true];
  repeated Assertion assertions = 8 [(validate.rules).repeated.ignore_empty = true];
  repeated Import imports = 9 [(validate.rules).repeated.ignore_empty = true];
  repeated Export exports = 10 [(validate.rules).repeated.ignore_empty = true];
}

// ReferenceComponent 引用组件组组件（业务单请求组件组、业务行为组组件组）
message ReferenceComponent {
  string reference_id = 1 [(validate.rules).string.min_len = 1]; // 引用ID，即被引用的组件组的ID
  string version = 2; // 引用版本，即被引用的组件组的版本
  repeated Import imports = 3 [(validate.rules).repeated.ignore_empty = true]; // 入参列表
  repeated Export exports = 4 [(validate.rules).repeated.ignore_empty = true]; // 出参列表
  string reference_component_id = 5; // 引用组件组中的组件ID，即被引用的组件组中实际被执行的组件ID
}

// ConditionComponent 条件控制组件
message ConditionComponent {
  message SingleCondition {
    message Value {
      VariableSource source = 1 [(validate.rules).enum.defined_only = true];
      VariableManual manual = 2 [(validate.rules).message.required = true];
      VariableExport export = 3 [(validate.rules).message.required = true];
      VariableEnvironment environment = 4 [(validate.rules).message.required = true];
      VariableFunction function = 5; // 为了兼容旧数据，这里不能做`validate`
    }

    Value left = 1 [(validate.rules).message.required = true]; // 左值
    string compare = 2 [(validate.rules).string = {ignore_empty: true, in: ["EQ", "NE", "LT", "LE", "GT", "GE", "LEN_EQ", "LEN_NE", "LEN_LT", "LEN_LE", "LEN_GT", "LEN_GE", "CONTAINS", "NOT_CONTAINS", "RE"]}]; // 比较方式
    Value right = 3 [(validate.rules).message.required = true]; // 右值
  }
  message GroupCondition {
    Relationship relationship = 1 [(validate.rules).enum.defined_only = true];
    repeated ConditionComponent conditions = 2 [(validate.rules).repeated.ignore_empty = true];
  }

  ConditionType type = 1 [(validate.rules).enum.defined_only = true];
  SingleCondition single = 2; // 单条件
  GroupCondition group = 3; // 条件组
}

// WaitComponent 等待控制组件
message WaitComponent {
  enum Type {
    SLEEP = 0; // 休眠
    MANUAL = 1; // 人工
  }
  message Sleep {
    int64 time = 1 [(validate.rules).int64.gte = 0]; // 休眠时间
  }
  message Manual {
    enum Action {
      CONTINUE = 0; // 继续
      TERMINATE = 1; // 终止
    }
    int64 max_waiting_time = 1 [(validate.rules).int64.gte = 0]; // 最大等待人工处理时间
    Action default_action = 2 [(validate.rules).enum.defined_only = true]; // 默认处理方式（当等待时间达到最大值时都没有人工介入则按默认处理方式执行）
  }

  Type type = 1 [(validate.rules).enum.defined_only = true]; // 等待类型
  Sleep sleep = 2 [(validate.rules).message.required = true];
  Manual manual = 3 [(validate.rules).message.skip = true];
}

// AssertComponent 断言判断组件
message AssertComponent {
  message Actual {
    string node_id = 1 [(validate.rules).string = {ignore_empty: true, min_len: 1}]; // 出参节点ID
    string value = 2 [(validate.rules).string = {ignore_empty: true, min_len: 1}]; // 节点的出参变量名称
  }
  message Expected {
    VariableSource source = 1 [(validate.rules).enum.defined_only = true]; // 期望值来源
    VariableManual manual = 2 [(validate.rules).message.required = true];
    VariableExport export = 3 [(validate.rules).message.required = true];
    VariableEnvironment environment = 4 [(validate.rules).message.required = true];
    VariableFunction function = 5; // 为了兼容旧数据，这里不能做`validate`
  }
  message Assertion {
    Actual actual = 1 [(validate.rules).message.required = true]; // 实际值
    string compare = 2 [(validate.rules).string = {ignore_empty: true, in: ["EQ", "NE", "LT", "LE", "GT", "GE", "LEN_EQ", "LEN_NE", "LEN_LT", "LEN_LE", "LEN_GT", "LEN_GE", "CONTAINS", "NOT_CONTAINS", "RE"]}]; // 比较方式
    Expected expected = 3 [(validate.rules).message.required = true]; // 期望值
  }

  repeated Assertion assertions = 1 [(validate.rules).repeated.ignore_empty = true];
}

// PoolAccountComponent 池账号组件
message PoolAccountComponent {
  message SingleCondition {
    message Between {
      string start = 1 [(validate.rules).string = {ignore_empty: true, min_len: 1}];
      string end = 2 [(validate.rules).string = {ignore_empty: true, min_len: 1}];
    }
    message Other {
      string value = 1 [(validate.rules).string = {ignore_empty: true, min_len: 1}];
    }

    string field = 1 [(validate.rules).string = {ignore_empty: true, min_len: 1}];
    string compare = 2 [(validate.rules).string = {ignore_empty: true, in: ["EQ", "NE", "LT", "LE", "GT", "GE", "LIKE", "IN", "BETWEEN"]}];
    repeated string in = 3 [(validate.rules).repeated.ignore_empty = true];
    Between between = 4 [(validate.rules).message.required = true];
    Other other = 5 [(validate.rules).message.required = true];
  }
  message GroupCondition {
    Relationship relationship = 1 [(validate.rules).enum.defined_only = true];
    repeated Condition conditions = 2 [(validate.rules).repeated.ignore_empty = true];
  }
  message Condition {
    ConditionType type = 1 [(validate.rules).enum.defined_only = true];
    SingleCondition single = 2;
    GroupCondition group = 3;
  }
  message KeyValPair {
    string key = 1 [(validate.rules).string = {ignore_empty: true, min_len: 1}];
    string value = 2 [(validate.rules).string = {ignore_empty: true, min_len: 1}];
  }

  int64 product_type = 1 [(validate.rules).int64.gte = 0];
  Condition condition = 2 [(validate.rules).message.required = true];
  repeated KeyValPair exports = 3 [(validate.rules).repeated.min_items = 2];
}

// DataProcessingComponent 数据处理组件
message DataProcessingComponent {
  message Process {
    string name = 1 [(validate.rules).string = {ignore_empty: true, min_len: 1}]; // 出参变量名称
    VariableFunction function = 2 [(validate.rules).message.required = true]; // 数据处理函数
  }

  repeated Process processes = 1 [(validate.rules).repeated.ignore_empty = true];
}

// DataDrivenComponent 数据驱动组件
message DataDrivenComponent {}

// SqlExecutionComponent SQL执行组件
message SqlExecutionComponent {
  message Value {
    VariableSource source = 1 [(validate.rules).enum.defined_only = true]; // 期望值来源
    VariableManual manual = 2 [(validate.rules).message.required = true];
    VariableExport export = 3 [(validate.rules).message.required = true];
    VariableEnvironment environment = 4 [(validate.rules).message.required = true];
    VariableFunction function = 5; // 为了兼容旧数据，这里不能做`validate`
  }
  message Sql {
    DataSourceType type = 1 [(validate.rules).enum.defined_only = true]; // 数据源类型
    Value host = 2 [(validate.rules).message.required = true]; // 主机地址
    Value port = 3 [(validate.rules).message.required = true]; // 端口
    Value user = 4 [(validate.rules).message.required = true]; // 用户名
    Value password = 5 [(validate.rules).message.required = true]; // 密码
    Value database = 6 [(validate.rules).message.required = true]; // 数据库名称
    string sql = 7 [(validate.rules).string.min_len = 13]; // 待执行的SQL（min_len: len("delete from t")）
    string name = 8 [(validate.rules).string = {ignore_empty: true, min_len: 1}]; // 出参变量名称
    int64 timeout = 9[(validate.rules).int64.gt = 0]; // SQL执行超时时间（单位为毫秒）
  }

  repeated Sql sqls = 1 [(validate.rules).repeated.ignore_empty = true];
}
