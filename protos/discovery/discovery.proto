syntax = "proto3";

package discovery;

option go_package = "gitlab.ttyuyin.com/TestDevelopment/probe-backend/discovery/rpc/pb";

import "buf/validate/validate.proto";

import "common/enum.proto";


service DiscoveryService {
  //DownloadApp 下载应用
  rpc DownloadApp(DownloadAppReq) returns (DownloadAppResp);
  //InstallApp 安装应用
  rpc InstallApp(InstallAppReq) returns (InstallAppResp);
  //RemoveApp 删除应用文件
  rpc RemoveApp(RemoveAppReq) returns (RemoveAppResp);
}

message DownloadAppReq {
  common.PlatformType platform_type = 1 [(buf.validate.field).enum = {not_in: [0]}]; // 平台类型（Android、iOS）
  string app_name = 2 [(buf.validate.field) = {ignore: IGNORE_IF_UNPOPULATED, string: {min_len: 1}}]; // 应用名称（Android: package_name; iOS: bundle_id）
  string link = 3 [(buf.validate.field) = {ignore: IGNORE_IF_UNPOPULATED, string: {uri: true}}]; // 下载地址
  string filename = 4 [(buf.validate.field) = {ignore: IGNORE_IF_UNPOPULATED, string: {min_len: 1}}]; // 指定下载后保存的文件名
}
message DownloadAppResp {
  string filename = 1; // 下载后的文件名
  string link = 2; // 下载地址
  string version = 3; // 应用版本
  string app_name = 4; // 应用名称（Android: package_name; iOS: bundle_id）
  int64 size = 5; // 应用大小
}

message InstallAppReq {
  common.PlatformType platform_type = 1 [(buf.validate.field).enum = {not_in: [0]}]; // 平台类型（Android、iOS）
  string udid = 2 [(buf.validate.field).string = {min_len: 1}]; // 设备编号
  string remote_address = 3 [(buf.validate.field) = {ignore: IGNORE_IF_UNPOPULATED, string: {min_len: 1}}]; // 设备远程地址
  string filename = 4 [(buf.validate.field).string = {min_len: 1}]; // 应用文件名
}
message InstallAppResp {
}

message RemoveAppReq {
  string filename = 1 [(buf.validate.field).string = {min_len: 1}]; // 应用文件名
}
message RemoveAppResp {}
