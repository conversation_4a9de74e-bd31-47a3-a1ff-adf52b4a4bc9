syntax = "proto3";

package common;

option go_package = "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb";


enum NodeType {
  NT_NULL = 0; // NULL

  NT_DIRECTORY = 1; // 目录
  NT_FILE = 2; // 文件
  NT_PACKAGE = 3; // 包
  NT_MODULE = 4; // 模块
  NT_CLASS = 5; // 类
  NT_FUNCTION = 6; // 函数
}

message Node {
  string path = 1; // 节点路径（相对于根路径）
  string name = 2; // 节点名称
  string alias = 3; // 节点别名
  NodeType type = 4; // 节点类型
  repeated string tags = 5; // 节点标签列表
  repeated Node children = 6; // 子节点列表
}
