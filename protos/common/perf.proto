syntax = "proto3";

package common;

option go_package = "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb";

import "google/protobuf/struct.proto";
import "validate/validate.proto";

import "common/enum.proto";
import "common/limit.proto";


// PerfKeepalive 压测保活参数
message PerfKeepalive {
  message AuthConfig {
    RateLimit rate_limit = 1 [(validate.rules).message = {required: true}];

    string key = 99;
  }
  message HeartbeatConfig {
    RateLimit rate_limit = 1 [(validate.rules).message = {required: true}];
    uint32 interval = 2 [(validate.rules).uint32 = {gt: 0}];

    string key = 99;
  }

  AuthConfig auth = 1;           // 认证接口的限流配置
  HeartbeatConfig heartbeat = 2; // 心跳接口的限流配置
}

// PerfCaseStep 压测步骤
message PerfCaseStep {
  message Export {
    // @gotags: json:"name" yaml:"Name"
    string name = 1 [(validate.rules).string = {min_len: 1}]; // 变量名
    // @gotags: json:"expression" yaml:"Expression"
    string expression = 2 [(validate.rules).string = {min_len: 1}]; // 表达式
  }

  // @gotags: json:"name" yaml:"Name"
  string name = 1 [(validate.rules).string = {min_len: 1, max_len: 64}]; // 步骤名称
  // @gotags: json:"rate_limit" yaml:"RateLimit"
  RateLimit rate_limit = 2 [(validate.rules).message = {required: true}]; // 限流配置
  // @gotags: json:"url" yaml:"Url"
  string url = 3 [(validate.rules).string = {ignore_empty: true, max_len: 255}]; // 请求URL
  // @gotags: json:"method" yaml:"Method"
  string method = 4 [(validate.rules).string = {min_len: 3, max_len: 255}]; // 请求方法
  // @gotags: json:"headers" yaml:"Headers"
  map<string, string> headers = 5 [(validate.rules).map = {ignore_empty: true, keys: {string: {min_len: 1}}}]; // 请求头
  // @gotags: json:"body" yaml:"Body"
  string body = 6 [(validate.rules).string = {ignore_empty: true, max_len: 1024}]; // 请求体
  // @gotags: json:"exports" yaml:"Exports"
  repeated Export exports = 7 [(validate.rules).repeated = {ignore_empty: true, items: {message: {required: true}}}]; // 出参列表
  // @gotags: json:"sleep" yaml:"Sleep"
  string sleep = 8 [(validate.rules).string = {ignore_empty: true, min_len: 2}]; // 休眠时间

  string key = 99;
}

// PerfCaseContent 压测用例内容
message PerfCaseContent {
  // @gotags: json:"setup_steps" yaml:"SetupSteps"
  repeated PerfCaseStep setup_steps = 1 [(validate.rules).repeated = {ignore_empty: true, items: {message: {required: true}}}];    // 前置步骤列表
  // @gotags: json:"serial_steps" yaml:"SerialSteps"
  repeated PerfCaseStep serial_steps = 2 [(validate.rules).repeated = {ignore_empty: true, items: {message: {required: true}}}];   // 串行步骤列表
  // @gotags: json:"parallel_steps" yaml:"ParallelSteps"
  repeated PerfCaseStep parallel_steps = 3 [(validate.rules).repeated = {ignore_empty: true, items: {message: {required: true}}}]; // 并行步骤列表
  // @gotags: json:"teardown_steps" yaml:"TeardownSteps"
  repeated PerfCaseStep teardown_steps = 4 [(validate.rules).repeated = {ignore_empty: true, items: {message: {required: true}}}]; // 后置步骤列表
}

// PerfDataContent 压测数据内容
message PerfDataContent {
  message Line {
    // @gotags: json:"auth_data" yaml:"AuthData" csv:"auth_data"
    google.protobuf.Struct auth_data = 1 [(validate.rules).message = {required: true}]; // 认证数据
    // @gotags: json:"request_data" yaml:"RequestData" csv:"request_data"
    google.protobuf.ListValue request_data = 2; // 请求数据
  }

  repeated Line lines = 1 [(validate.rules).repeated = {min_items: 1, items: {message: {required: true}}}];
}

// PerfRateLimits 压测相关的限流配置
message PerfRateLimits {
  message AuthConfig {
    repeated RateLimitV2 rate_limits = 1 [(validate.rules).repeated = {items: {message: {required: true}}}];

    string key = 99;
  }
  message HeartbeatConfig {
    repeated RateLimitV2 rate_limits = 1 [(validate.rules).repeated = {items: {message: {required: true}}}];
    uint32 interval = 2 [(validate.rules).uint32 = {gt: 0}];

    string key = 99;
  }

  AuthConfig auth = 1;           // 认证接口的限流配置
  HeartbeatConfig heartbeat = 2; // 心跳接口的限流配置
}

// PerfCaseStepV2 压测步骤
message PerfCaseStepV2 {
  message Export {
    // @gotags: json:"name" yaml:"Name"
    string name = 1 [(validate.rules).string = {min_len: 1}]; // 变量名
    // @gotags: json:"expression" yaml:"Expression"
    string expression = 2 [(validate.rules).string = {min_len: 1}]; // 表达式
  }

  // @gotags: json:"name" yaml:"Name"
  string name = 1 [(validate.rules).string = {min_len: 1, max_len: 64}]; // 步骤名称
  // @gotags: json:"rate_limits" yaml:"RateLimits"
  repeated RateLimitV2 rate_limits = 2 [(validate.rules).repeated = {ignore_empty: true, items: {message: {required: true}}}]; // 限流配置
  // @gotags: json:"url" yaml:"Url"
  string url = 3 [(validate.rules).string = {ignore_empty: true, max_len: 255}]; // 请求URL
  // @gotags: json:"method" yaml:"Method"
  string method = 4 [(validate.rules).string = {min_len: 3, max_len: 255}]; // 请求方法
  // @gotags: json:"headers" yaml:"Headers"
  map<string, string> headers = 5 [(validate.rules).map = {ignore_empty: true, keys: {string: {min_len: 1}}}]; // 请求头
  // @gotags: json:"body" yaml:"Body"
  string body = 6 [(validate.rules).string = {ignore_empty: true, max_len: 1024}]; // 请求体
  // @gotags: json:"exports" yaml:"Exports"
  repeated Export exports = 7 [(validate.rules).repeated = {ignore_empty: true, items: {message: {required: true}}}]; // 出参列表
  // @gotags: json:"sleep" yaml:"Sleep"
  string sleep = 8 [(validate.rules).string = {ignore_empty: true, min_len: 2}]; // 休眠时间
  
  string key = 99;
  string service = 100; // 服务名称
  string namespace = 101; // 命名空间
  uint32 cmd = 102; // 命令号（`TT`专用）
  string grpc_path = 103; // gRPC路径（`TT`专用）
  bool deprecated = 104; // 是否已弃用（`TT`专用）
  optional int64 reference_qps = 105; // QPS参考值
  string query_path = 106; // 请求路径（查询天相）
}

// PerfCaseContentV2 压测用例内容
message PerfCaseContentV2 {
  // @gotags: json:"setup_steps" yaml:"SetupSteps"
  repeated PerfCaseStepV2 setup_steps = 1 [(validate.rules).repeated = {ignore_empty: true, items: {message: {required: true}}}];    // 前置步骤列表
  // @gotags: json:"serial_steps" yaml:"SerialSteps"
  repeated PerfCaseStepV2 serial_steps = 2 [(validate.rules).repeated = {ignore_empty: true, items: {message: {required: true}}}];   // 串行步骤列表
  // @gotags: json:"parallel_steps" yaml:"ParallelSteps"
  repeated PerfCaseStepV2 parallel_steps = 3 [(validate.rules).repeated = {ignore_empty: true, items: {message: {required: true}}}]; // 并行步骤列表
  // @gotags: json:"teardown_steps" yaml:"TeardownSteps"
  repeated PerfCaseStepV2 teardown_steps = 4 [(validate.rules).repeated = {ignore_empty: true, items: {message: {required: true}}}]; // 后置步骤列表
}

// PerfServiceMetaData 压测涉及的服务的元数据（注：TT相关协议专用）
message PerfServiceMetaData {
  string name = 1; // 服务名称
  string namespace = 2; // 命名空间
  repeated PerfUserInfo developers = 3; // 研发人员
  repeated PerfUserInfo maintainers = 4; // 运维人员
  repeated PerfUserInfo database_administrators = 5; // 数据库管理人员
}

// PerfUserInfo 压测涉及的用户的信息
message PerfUserInfo {
  string account = 1; // 账号
  string fullname = 2; // 全名
  string email = 3; // 邮箱
  string lark_user_id = 4; // 飞书的用户ID
}

// PerfStopRule 压测停止规则
message PerfStopRule {
  MetricType metric_type = 1; // 指标类型
  double threshold = 2; // 阈值
  uint32 duration = 3; // 持续时间（秒）
}
