syntax = "proto3";

package common;

option go_package = "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb";

import "google/protobuf/struct.proto";
import "validate/validate.proto";

import "common/config.proto";
import "common/enum.proto";


// StabilityCustomDevices 稳定性测试自定义设备
message StabilityCustomDevices {
  oneof devices {
    google.protobuf.ListValue udids = 1; // 设备编号列表（指定设备）
    uint32 count = 2 [(validate.rules).uint32 = {ignore_empty: true, gte: 0}]; // 设备数量（随机选择设备）
  }
}

// StabilityCustomScript 稳定性测试自定义脚本
message StabilityCustomScript {
  oneof script {
    GitConfig git_config = 1; // 自定义脚本（Git仓库）
    string image = 2 [(validate.rules).string = {ignore_empty: true, min_len: 1}]; // 自定义脚本（镜像）
  }
}

// ActivityStatistics Activity统计
message ActivityStatistics {
  repeated string tested_activity = 1; // 被测试的Activity
  repeated string total_activity = 2;  // 总的Activity
  double coverage = 3;                 // 覆盖率
}

// Artifact 产出物
message Artifact {
  ArtifactType type = 1;  // 类型
  string file_name = 2;   // 文件名
  string file_path = 3;   // 文件路径
  string description = 4; // 描述
}

// StabilityResult 稳定性测试结果
message StabilityResult {
  ActivityStatistics activity_statistics = 1; // Activity统计
  repeated Artifact artifacts = 2;            // 产出物
  uint32 anr_count = 3;                       // ANR数量
  uint32 crash_count = 4;                     // Crash数量
}
