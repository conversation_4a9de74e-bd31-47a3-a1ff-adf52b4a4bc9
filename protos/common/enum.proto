syntax = "proto3";

package common;

option go_package = "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb";

import "protobuf/options.proto";


// TriggerMode 触发模式
enum TriggerMode {
	NULL = 0; // NULL
	MANUAL = 1; // 手动触发
	SCHEDULE = 2; // 定时器触发
	INTERFACE = 3; // 接口触发
}

// PurposeType 计划用途
enum PurposeType {
	UNDEFINED = 0;         // 未定义
	NORMAL = 1;            // 常规
	PRECISION_TESTING = 2; // 精准测试
}

// DeviceType 设备类型
enum DeviceType {
	DT_NULL = 0; // NULL

	REAL_PHONE = 1;  // 真机
	CLOUD_PHONE = 2; // 云手机
}

// PlatformType 平台类型
enum PlatformType {
	PT_NULL = 0;   // NULL

	ANDROID = 1   [(options.enum_value_alias) = "Android"];   // 安卓
	IOS = 2       [(options.enum_value_alias) = "iOS"];       // IOS
	HarmonyOS = 3 [(options.enum_value_alias) = "HarmonyOS"]; // HarmonyOS
}

// OperationType 操作类型
enum OperationType {
	OT_NULL = 0; // NULL

	ADD = 1;    // 添加
	REMOVE = 2; // 删除
}

// DeviceUsage 设备用途
enum DeviceUsage {
	DU_NULL = 0; // NULL

	UI_TESTING = 1 [(options.enum_value_alias) = "UI"]; // UI测试
	STABILITY_TESTING = 2 [(options.enum_value_alias) = "STABILITY"]; // 稳定性测试
}

// FailRetry 失败重试
enum FailRetry {
	ZERO = 0; // 0次
	ONE = 1;  // 1次
	TWO = 2;  // 2次
}

// PlanType 计划类型
enum PlanType {
	API = 0;       // API测试计划
	UI = 1;        // UI测试计划
	PERF = 2;      // 压力测试计划
	STABILITY = 3; // 稳定性测试计划
	UI_AGENT = 4;  // UI Agent测试计划
}

// 优先级类型
enum PriorityType {
	Default = 0; // 缺省
	Middle = 1;  // 对照消息优先级默认:"default":10%
	High = 2;    // 对照消息优先级默认:"high":38%
	Ultra = 3;   // 对照消息优先级默认:"ultra":50%
	Low = 4;     // 对照消息优先级默认:"low":7%
}

// TestLanguage 测试语言
enum TestLanguage {
	TestLanguage_NULL = 0;
	TestLanguage_PYTHON = 1; // Python
	TestLanguage_GOLANG = 2; // Golang
}

// TestFramework 测试框架
enum TestFramework {
	TestFramework_NULL = 0;
	TestFramework_PYTEST = 1; // pytest
}

// ExecuteStatus 执行状态
enum ExecuteStatus{
	TES_INIT = 0; // 排队中
	TES_EXECUTING = 1; // 执行中
	TES_FINISH = 2; // 已完成(终态)
	TES_STOP = 3; // 已停止(终态)
}

// ExecutedResult 执行结果
enum ExecutedResult{
	TER_INIT = 0; // 缺省
	TER_SUCCESS = 1; // 成功(终态)
	TER_FAILURE = 2; // 失败(终态)
	TER_PANIC = 3; // 异常(终态)
}

// TestStage 测试阶段
enum TestStage {
	TS_NULL = 0;  // NULL
	SETUP = 1    [(options.enum_value_alias) = "setup"];    // 前置步骤
	TEST = 2     [(options.enum_value_alias) = "test"];     // 测试步骤
	TEARDOWN = 3 [(options.enum_value_alias) = "teardown"]; // 后置步骤
}

// Protocol 协议
enum Protocol {
	PROTOCOL_NULL = 0; // NULL

	// Common Protocol
	PROTOCOL_HTTP = 1 [(options.enum_value_alias) = "HTTP"]; // HTTP
	PROTOCOL_GRPC = 2 [(options.enum_value_alias) = "gRPC"]; // gRPC

	// Private Protocol
	PROTOCOL_TT = 21 [(options.enum_value_alias) = "TT"]; // TT私有协议
	PROTOCOL_TT_AUTH = 22 [(options.enum_value_alias) = "TT_AUTH"]; // TT登录压测场景专用
}

// TargetEnvironment 目标环境
enum TargetEnvironment {
	TE_NULL = 0; // NULL
	TE_DEVELOPMENT = 1 [(options.enum_value_alias) = "DEVELOPMENT"]; // 开发环境
	TE_TESTING = 2 [(options.enum_value_alias) = "TESTING"];         // 测试环境
	TE_STAGING = 3 [(options.enum_value_alias) = "STAGING"];         // 预发布环境
	TE_CANARY = 4 [(options.enum_value_alias) = "CANARY"];           // 灰度环境
	TE_PRODUCTION = 5 [(options.enum_value_alias) = "PRODUCTION"];   // 生产环境
}

// PerfTaskType 压测任务类型
enum PerfTaskType {
	PTT_NULL = 0;

	RUN = 1; // 执行
	DEBUG = 2; // 调试
}

// PerfTaskExecutionMode 压测任务执行方式
enum PerfTaskExecutionMode {
  PTEM_NULL = 0;

  BY_DURATION = 1; // 按时长
  BY_TIMES = 2; // 按次数
}

// PerfCaseStepType 压测用例步骤类型
enum PerfCaseStepType {
	PerfCaseStepType_NULL = 0; // NULL
	PerfCaseStepType_SETUP = 1 [(options.enum_value_alias) = "SETUP"];       // 前置步骤
	PerfCaseStepType_SERIAL = 2 [(options.enum_value_alias) = "SERIAL"];     // 串行步骤
	PerfCaseStepType_PARALLEL = 3 [(options.enum_value_alias) = "PARALLEL"]; // 并行步骤
	PerfCaseStepType_TEARDOWN = 4 [(options.enum_value_alias) = "TEARDOWN"]; // 后置步骤
}

// MonitorUrlType 监控地址类型
enum MonitorUrlType {
	MUT_NULL = 0;

	MUT_GRAFANA = 1 [(options.enum_value_alias) = "Grafana"]; // Grafana
	MUT_APP_INSIGHT = 2 [(options.enum_value_alias) = "AppInsight"]; // 天相
}

// MetricType 指标类型
enum MetricType {
	MetricType_NULL = 0;

	MetricType_QPS = 1 [(options.enum_value_alias) = "qps"]; // qps
	MetricType_FailRatio = 2 [(options.enum_value_alias) = "fail_ratio"]; // 失败率
	MetricType_P99 = 3 [(options.enum_value_alias) = "p99"]; // P99
	MetricType_P95 = 4 [(options.enum_value_alias) = "p95"]; // P95
	MetricType_P90 = 5 [(options.enum_value_alias) = "p90"]; // P90
	MetricType_P75 = 6 [(options.enum_value_alias) = "p75"]; // P75
	MetricType_P50 = 7 [(options.enum_value_alias) = "p50"]; // P50
}

// PerfDataType 性能数据类型
enum PerfDataType {
  PerfDataType_NULL = 0;

  PerfDataType_CPU = 1 [(options.enum_value_alias) = "CPU"]; // CPU
  PerfDataType_MEMORY = 2 [(options.enum_value_alias) = "MEMORY"]; // 内存
  PerfDataType_FPS = 3 [(options.enum_value_alias) = "FPS"]; // 帧率
  PerfDataType_DISK = 4 [(options.enum_value_alias) = "DISK"]; // 磁盘
  PerfDataType_NETWORK = 5 [(options.enum_value_alias) = "NETWORK"]; // 网络
}

// ArtifactType 产出物类型
enum ArtifactType {
	ArtifactType_NULL = 0;

	ArtifactType_LOG = 1 [(options.enum_value_alias) = "LOG"]; // 日志
	ArtifactType_SCREENSHOT = 2 [(options.enum_value_alias) = "SCREENSHOT"]; // 截图
}

// BranchType 平台类型
enum BranchType {
	BranchType_NULL = 0;   // NULL

	BranchType_RELEASE = 1   [(options.enum_value_alias) = "release"]; // 安卓
	BranchType_TESTING = 2   [(options.enum_value_alias) = "testing"]; // IOS
}

// PromptPurpose Prompt用途
enum PromptPurpose {
	PromptPurpose_NULL = 0;

	PromptPurpose_UI_AGENT = 1 [(options.enum_value_alias) = "UI_AGENT"]; // UI Agent
}

// PromptCategory Prompt分类
enum PromptCategory {
	PromptCategory_NULL = 0;

	PromptCategory_BACKGROUND = 1 [(options.enum_value_alias) = "BACKGROUND"]; // 背景
	PromptCategory_UI_COMPONENT = 2 [(options.enum_value_alias) = "UI_COMPONENT"]; // UI组件
	PromptCategory_EXCEPTION_HANDLING = 3 [(options.enum_value_alias) = "EXCEPTION_HANDLING"]; // 异常处理
}

// ExecuteType 执行类型
enum ExecuteType {
	ET_NULL = 0;

	ET_EXECUTE = 1 [(options.enum_value_alias) = "EXECUTE"]; // 执行
	ET_DEBUG = 2 [(options.enum_value_alias) = "DEBUG"]; // 调试
}
