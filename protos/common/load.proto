syntax = "proto3";

package common;

option go_package = "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb";

import "validate/validate.proto";


message LoadGenerator {
  uint32 number_of_lg = 1 [(validate.rules).uint32 = {ignore_empty: true, gte: 0}];           // 施压机数量
  string requests_of_cpu = 2 [(validate.rules).string = {ignore_empty: true, min_len: 1}];    // 最小分配的CPU资源
  string requests_of_memory = 3 [(validate.rules).string = {ignore_empty: true, min_len: 1}]; // 最小分配的内存资源
  string limits_of_cpu = 4 [(validate.rules).string = {ignore_empty: true, min_len: 1}];      // 最大分配的CPU资源
  string limits_of_memory = 5 [(validate.rules).string = {ignore_empty: true, min_len: 1}];   // 最大分配的内存资源
}
