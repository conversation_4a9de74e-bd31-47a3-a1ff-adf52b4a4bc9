syntax = "proto3";

package common;

option go_package = "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb";

import "validate/validate.proto";


message RateLimit {
  // @gotags: json:"target_rps" yaml:"TargetRPS"
  int64 target_rps = 1 [(validate.rules).int64 = {gt: 0}]; // 目标的RPS
  // @gotags: json:"initial_rps" yaml:"InitialRPS"
  int64 initial_rps = 2 [(validate.rules).int64 = {ignore_empty: true, gt: 0}]; // 初始的RPS
  // @gotags: json:"step_height" yaml:"StepHeight"
  int64 step_height = 3 [(validate.rules).int64 = {ignore_empty: true, not_in: [0]}]; // 每次改变RPS的量
  // @gotags: json:"step_duration" yaml:"StepDuration"
  string step_duration = 4 [(validate.rules).string = {ignore_empty: true, min_len: 2}]; // 改变后的RPS的持续时间
}

message RateLimitV2 {
  // @gotags: json:"target_rps" yaml:"TargetRPS"
  int64 target_rps = 1 [(validate.rules).int64 = {gt: 0}]; // 目标的RPS
  // @gotags: json:"initial_rps" yaml:"InitialRPS"
  int64 initial_rps = 2 [(validate.rules).int64 = {ignore_empty: true, gt: 0}]; // 初始的RPS
  // @gotags: json:"change_duration" yaml:"ChangeDuration"
  string change_duration = 3 [(validate.rules).string = {ignore_empty: true, min_len: 2}]; // 加压持续时间（从初始值到目标值）
  // @gotags: json:"target_duration" yaml:"TargetDuration"
  string target_duration = 4 [(validate.rules).string = {ignore_empty: true, min_len: 2}]; // 施压持续时间（经过加压时间后维持的时间）
}
