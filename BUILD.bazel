# Copyright 2023 Buf Technologies, Inc.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

load("@bazel_gazelle//:def.bzl", "gazelle", "gazelle_binary")

package(default_visibility = ["//visibility:public"])

gazelle_binary(
    name = "gazelle-buf",
    languages = [
        "@bazel_gazelle//language/proto",  # Built-in rule from gazelle for Protos.
        # Any languages that depend on Gazelle's proto plugin must come after it.
        "@rules_buf//gazelle/buf:buf",  # Loads the Buf extension
    ],
    visibility = ["//visibility:public"],
)

# gazelle:exclude proto/protovalidate-testing/tests
gazelle(
    name = "gazelle",
    gazelle = ":gazelle-buf",
)
