package model

import (
	"context"
	"database/sql"
	"fmt"
	"time"

	"github.com/Masterminds/squirrel"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sqlbuilder"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/types"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/userinfo"

	commonconsts "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/db"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/utils"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
)

var (
	_ CaseFailStatModel = (*customCaseFailStatModel)(nil)

	caseFailStatInsertFields = stringx.Remove(
		caseFailStatFieldNames, "`id`", "`create_time`", "`update_time`", "`deleted_by`", "`created_at`",
		"`updated_at`", "`deleted_at`",
	)
)

type (
	// CaseFailStatModel is an interface to be customized, add more methods here,
	// and implement the added methods in customCaseFailStatModel.
	CaseFailStatModel interface {
		caseFailStatModel
		types.DBModel

		Trans(ctx context.Context, fn func(context context.Context, session sqlx.Session) error) error
		InsertBuilder(data *CaseFailStat) squirrel.InsertBuilder
		UpdateBuilder(data *CaseFailStat) squirrel.UpdateBuilder
		SelectBuilder() squirrel.SelectBuilder
		SelectCountBuilder() squirrel.SelectBuilder
		FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error)
		FindNoCacheByQuery(ctx context.Context, selectBuilder squirrel.SelectBuilder) ([]*CaseFailStat, error)

		InsertTX(ctx context.Context, session sqlx.Session, data *CaseFailStat) (sql.Result, error)
		UpdateTX(ctx context.Context, session sqlx.Session, data *CaseFailStat) (sql.Result, error)

		FindNoCacheByQueryV2(ctx context.Context, selectBuilder squirrel.SelectBuilder) ([]*CaseFailStatV2, error)
		FindByPageQuery(
			ctx context.Context, pageQuery db.PageQuery, diyQueryList []db.DiyQuery,
		) (db.PageInfo[*CaseFailStat], error)
		FindNoCacheByProjectIdCaseIdCaseType(
			ctx context.Context, projectId, caseId, caseType string,
		) (*CaseFailStat, error)
		LogicDeleteByProjectIdCaseIdCaseType(
			ctx context.Context, session sqlx.Session, projectId, caseId, caseType string,
		) (sql.Result, error)
		GenerateSearchFailLogCaseQuery(req SearchFailLogCaseReq) (
			searchFailLogCaseSelectBuilder, searchFailLogCaseCountBuilder,
		)
		UpdateByProjectIdCaseIdCaseTypeForFailCountIncr(
			ctx context.Context, session sqlx.Session, projectId, caseId, caseType string,
		) (sql.Result, error)

		FindAll(ctx context.Context) ([]*CaseFailStat, error)
		FindBeforeNDaysRecords(ctx context.Context, days int) ([]*CaseFailStat, error)
		DeleteBeforeNDaysRecords(ctx context.Context, session sqlx.Session, days int) error
	}

	CaseFailStatV2 struct {
		Id            int64          `db:"id"`
		ProjectId     string         `db:"project_id"`     // 项目ID
		CaseId        string         `db:"case_id"`        // 用例ID
		Name          string         `db:"name"`           // 用例名称
		Description   sql.NullString `db:"description"`    // 用例描述
		Priority      int64          `db:"priority"`       // 优先级（NULL、P0、P1、P2、P3...）
		Tags          sql.NullString `db:"tags"`           // 标签
		State         string         `db:"state"`          // 用例状态（生效、失效）
		AccountConfig string         `db:"account_config"` // 池账号配置数
		Version       string         `db:"version"`        // 用例版本
		Structure     string         `db:"structure"`      // 用例中各节点的关系结构
		Latest        int64          `db:"latest"`         // 是否最新版本
		Deleted       int64          `db:"deleted"`        // 逻辑删除标识（未删除、已删除）
		MaintainedBy  sql.NullString `db:"maintained_by"`  // 维护者的用户ID
		CreatedBy     string         `db:"created_by"`     // 创建者的用户ID
		UpdatedBy     string         `db:"updated_by"`     // 最近一次更新者的用户ID
		DeletedBy     sql.NullString `db:"deleted_by"`     // 删除者的用户ID
		CreatedAt     time.Time      `db:"created_at"`     // 创建时间
		UpdatedAt     time.Time      `db:"updated_at"`     // 更新时间
		DeletedAt     sql.NullTime   `db:"deleted_at"`     // 删除时间
		BranchId      string         `db:"branch_id"`      // 分支ID
		CaseType      string         `db:"case_type"`      // 用例类型[API_CASE，INTERFACE_CASE]
		FailCount     int64          `db:"fail_count"`     // 失败次数
	}

	customCaseFailStatModel struct {
		*defaultCaseFailStatModel
		a ApiCaseModel
		i InterfaceCaseModel
	}
	searchFailLogCaseSelectBuilder struct {
		squirrel.SelectBuilder
	}

	searchFailLogCaseCountBuilder struct {
		squirrel.SelectBuilder
	}
)

// NewCaseFailStatModel returns a model for the database table.
func NewCaseFailStatModel(conn sqlx.SqlConn, c cache.CacheConf, opts ...cache.Option) CaseFailStatModel {
	return &customCaseFailStatModel{
		defaultCaseFailStatModel: newCaseFailStatModel(conn, c, opts...),
		a:                        NewApiCaseModel(conn, c),
		i:                        NewInterfaceCaseModel(conn, c),
	}
}

func (m *customCaseFailStatModel) Table() string {
	return m.table
}

func (m *customCaseFailStatModel) Fields() []string {
	return caseFailStatFieldNames
}

func (m *customCaseFailStatModel) Trans(
	ctx context.Context, fn func(context context.Context, session sqlx.Session) error,
) error {
	return m.TransactCtx(ctx, fn)
}

func (m *customCaseFailStatModel) InsertBuilder(data *CaseFailStat) squirrel.InsertBuilder {
	ib := squirrel.Insert(m.table).Columns(caseFailStatInsertFields...)
	values := []any{
		data.ProjectId,
		data.CaseId,
		data.BranchId,
		data.CaseType,
		data.FailCount,
		data.Version,
		data.Deleted,
		data.CreatedBy,
		data.UpdatedBy,
	}
	if !data.CreatedAt.IsZero() {
		ib = ib.Columns("`created_at`")
		values = append(values, data.CreatedAt)
	}
	if !data.UpdatedAt.IsZero() {
		ib = ib.Columns("`updated_at`")
		values = append(values, data.UpdatedAt)
	}

	return ib.Values(values...)
}

func (m *customCaseFailStatModel) UpdateBuilder(data *CaseFailStat) squirrel.UpdateBuilder {
	eq := squirrel.Eq{
		"`fail_count`": data.FailCount,
		"`version`":    data.Version,
		"`deleted`":    data.Deleted,
		"`updated_by`": data.UpdatedBy,
		"`deleted_by`": data.DeletedBy,
		"`deleted_at`": data.DeletedAt,
	}
	if !data.UpdatedAt.IsZero() {
		eq["`updated_at`"] = data.UpdatedAt
	}
	return squirrel.Update(m.table).SetMap(eq).Where("`id` = ?", data.Id)
}

func (m *customCaseFailStatModel) SelectBuilder() squirrel.SelectBuilder {
	return squirrel.Select(caseFailStatFieldNames...).Where("`deleted` = ?", constants.NotDeleted).From(m.table)
}

func (m *customCaseFailStatModel) SelectCountBuilder() squirrel.SelectBuilder {
	return squirrel.Select("count(*)").Where("`deleted` = ?", constants.NotDeleted).From(m.table)
}

func (m *customCaseFailStatModel) FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error) {
	query, values, err := countBuilder.ToSql()
	if err != nil {
		return 0, err
	}

	var resp int64
	err = m.QueryRowNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return 0, err
	}
}

func (m *customCaseFailStatModel) FindNoCacheByQuery(
	ctx context.Context, selectBuilder squirrel.SelectBuilder,
) ([]*CaseFailStat, error) {
	query, values, err := selectBuilder.ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*CaseFailStat
	err = m.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

func (m *customCaseFailStatModel) InsertTX(
	ctx context.Context, session sqlx.Session, data *CaseFailStat,
) (
	sql.Result, error,
) {
	managerCaseFailStatIdKey := fmt.Sprintf("%s%v", cacheManagerCaseFailStatIdPrefix, data.Id)

	return m.ExecCtx(
		ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
			query, values, err := m.InsertBuilder(data).ToSql()
			if err != nil {
				return nil, err
			}
			if session != nil {
				return conn.ExecCtx(ctx, query, values...)
			}
			return conn.ExecCtx(ctx, query, values...)
		}, managerCaseFailStatIdKey,
	)
}

func (m *customCaseFailStatModel) UpdateTX(ctx context.Context, session sqlx.Session, newData *CaseFailStat) (
	sql.Result, error,
) {
	data, err := m.FindOne(ctx, newData.Id)
	if err != nil {
		return nil, err
	}

	managerCaseFailStatIdKey := fmt.Sprintf("%s%v", cacheManagerCaseFailStatIdPrefix, data.Id)

	return m.ExecCtx(
		ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
			query, values, err := m.UpdateBuilder(newData).ToSql()
			if err != nil {
				return nil, err
			}
			if session != nil {
				return session.ExecCtx(ctx, query, values...)
			}
			return conn.ExecCtx(ctx, query, values...)
		}, managerCaseFailStatIdKey,
	)
}

func (m *customCaseFailStatModel) FindNoCacheByQueryV2(
	ctx context.Context, selectBuilder squirrel.SelectBuilder,
) ([]*CaseFailStatV2, error) {
	query, values, err := selectBuilder.ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*CaseFailStatV2
	err = m.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

// FindByPageQuery 分页查询列配置列表
func (m *customCaseFailStatModel) FindByPageQuery(
	ctx context.Context, pageQuery db.PageQuery, diyQueryList []db.DiyQuery,
) (db.PageInfo[*CaseFailStat], error) {
	newPageInfo := db.NewPageInfo[*CaseFailStat]()
	err := newPageInfo.FindByPageQueryV2(ctx, pageQuery, diyQueryList, m, m.CachedConn)
	if err != nil {
		return db.PageInfo[*CaseFailStat]{}, err
	}
	return *newPageInfo, nil
}

func (m *customCaseFailStatModel) FindNoCacheByProjectIdCaseIdCaseType(
	ctx context.Context, projectId, caseId, caseType string,
) (*CaseFailStat, error) {
	sb := m.SelectBuilder().Where(
		"`project_id` = ? AND `case_id` = ? AND `case_type` = ?", projectId, caseId, caseType,
	).OrderBy("`updated_at` DESC").Limit(1)
	resp, err := m.FindNoCacheByQuery(ctx, sb)
	if err != nil {
		return nil, err
	} else if len(resp) == 0 {
		return nil, nil
	}
	return resp[0], nil
}

// LogicDeleteByProjectIdCaseIdCaseType 逻辑删除
func (m *customCaseFailStatModel) LogicDeleteByProjectIdCaseIdCaseType(
	ctx context.Context, session sqlx.Session, projectId, caseId, caseType string,
) (sql.Result, error) {
	keys := m.getKeysByCaseID(ctx, projectId, caseId, caseType)

	account := userinfo.System().Account
	if u := userinfo.FromContext(ctx); u != nil {
		account = u.Account
	}

	return m.ExecCtx(
		ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
			stmt, values, err := squirrel.Update(m.table).
				SetMap(
					squirrel.Eq{
						"`deleted`":    constants.HasDeleted,
						"`deleted_by`": account,
						"`deleted_at`": time.Now(),
					},
				).
				Where("`project_id` = ? AND `case_id` = ? AND `case_type` = ?", projectId, caseId, caseType).
				ToSql()
			if err != nil {
				return nil, err
			}

			if session != nil {
				return session.ExecCtx(ctx, stmt, values...)
			}
			return conn.ExecCtx(ctx, stmt, values...)
		}, keys...,
	)
}

func (m *customCaseFailStatModel) GenerateSearchFailLogCaseQuery(req SearchFailLogCaseReq) (
	searchFailLogCaseSelectBuilder, searchFailLogCaseCountBuilder,
) {
	/*
		SQL:
		SELECT t.*
		FROM (SELECT ac.*,
		             cfs.`fail_count`,
		             cfs.`case_type`,
		             cfs.`updated_at` AS `uptime`,
		             ac.`category_id` AS `branch_id`
		      FROM `case_fail_stat` AS cfs
		               LEFT JOIN `api_case` AS ac ON
		          ac.`project_id` = cfs.`project_id` AND
		          ac.`case_id` = cfs.`case_id` AND
		          ac.`deleted` = cfs.`deleted`
		      WHERE cfs.`project_id` = 'project_id:Kqllt5-9fA-I5UOdhjA5d'
		        AND cfs.`case_type` = 'API_CASE'
		        AND ac.`latest` = 1
		        AND cfs.`deleted` = 0
		        AND cfs.`updated_at` > DATE_SUB(CURDATE(), INTERVAL 7 DAY)
		      UNION
		      SELECT ic.*,
		             cfs.`fail_count`,
		             cfs.`case_type`,
		             cfs.`updated_at` AS `uptime`,
		             ic.`document_id` AS `branch_id`
		      FROM `case_fail_stat` AS cfs
		               LEFT JOIN `interface_case` AS ic ON
		          ic.`project_id` = cfs.`project_id` AND
		          ic.`case_id` = cfs.`case_id` AND
		          ic.`deleted` = cfs.`deleted`
		      WHERE cfs.`project_id` = 'project_id:Kqllt5-9fA-I5UOdhjA5d'
		        AND cfs.`case_type` = 'INTERFACE_CASE'
		        AND ic.`latest` = 1
		        AND cfs.`deleted` = 0
		        AND cfs.`updated_at` > DATE_SUB(CURDATE(), INTERVAL 7 DAY)
		     ) AS t
		ORDER BY t.`uptime` DESC
		LIMIT 100 OFFSET 0;

		SELECT COUNT(*)
		FROM (SELECT ac.*,
		             cfs.`fail_count`,
		             cfs.`case_type`,
		             cfs.`updated_at` AS `uptime`
		      FROM `case_fail_stat` AS cfs
		               LEFT JOIN `api_case` AS ac ON
		          ac.`project_id` = cfs.`project_id` AND
		          ac.`case_id` = cfs.`case_id` AND
		          ac.`deleted` = cfs.`deleted`
		      WHERE cfs.`project_id` = 'project_id:Kqllt5-9fA-I5UOdhjA5d'
		        AND cfs.`case_type` = 'API_CASE'
		        AND ac.`latest` = 1
		        AND cfs.`updated_at` > DATE_SUB(CURDATE(), INTERVAL 7 DAY)
		        AND cfs.`deleted` = 0
		      UNION
		      SELECT ic.*,
		             cfs.`fail_count`,
		             cfs.`case_type`,
		             cfs.`updated_at` AS `uptime`
		      FROM `case_fail_stat` AS cfs
		               LEFT JOIN `interface_case` AS ic ON
		          ic.`project_id` = cfs.`project_id` AND
		          ic.`case_id` = cfs.`case_id` AND
		          ic.`deleted` = cfs.`deleted`
		      WHERE cfs.`project_id` = 'project_id:Kqllt5-9fA-I5UOdhjA5d'
		        AND cfs.`case_type` = 'INTERFACE_CASE'
		        AND ic.`latest` = 1
		        AND cfs.`updated_at` > DATE_SUB(CURDATE(), INTERVAL 7 DAY)
		        AND cfs.`deleted` = 0) as t;
	*/

	var sb, scb squirrel.SelectBuilder
	caseFailStatAs := "cfs"
	// api
	apiCaseTableAs := "ac"
	apiCaseToFields := utils.AddTableNameToFields(
		apiCaseTableAs, notReleasedCaseFieldNamesForApiCase,
	)
	apiCaseToFields = append(
		apiCaseToFields,
		"cfs.`fail_count`",
		"cfs.`case_type`",
		"cfs.`updated_at` AS `uptime`",
		"cfs.`id` AS `cfs_id`",
		"ac.`category_id` AS `branch_id`",
	)
	apiCaseSqlTmp := squirrel.Select(
		apiCaseToFields...,
	).From(m.Table()+" AS "+caseFailStatAs).LeftJoin(
		fmt.Sprintf(
			"%s AS ac ON ac.`project_id` = cfs.`project_id` AND ac.`case_id` = cfs.`case_id` AND ac.`deleted` = cfs.`deleted`",
			m.a.Table(),
		),
	).Where(
		"cfs.`project_id` = ? AND cfs.`case_type` = ? AND ac.`latest` = ? AND cfs.`deleted` = ? AND cfs.`updated_at` > DATE_SUB(CURDATE(), INTERVAL ? DAY)",
		req.ProjectId, commonconsts.CaseTypeApiCase, constants.IsLatestVersion, constants.NotDeleted,
		common.ConstCaseFailStatNDays,
	)
	apiCaseSqlTmpQuery := sqlbuilder.SearchOptionsWithAlias(
		apiCaseSqlTmp,
		apiCaseTableAs, sqlbuilder.WithCondition(m.a, req.Condition),
	)
	// interface
	interfaceCaseTableAs := "ic"
	interfaceCaseToFields := utils.AddTableNameToFields(
		interfaceCaseTableAs, notReleasedCaseFieldNamesForInterfaceCase,
	)
	interfaceCaseToFields = append(
		interfaceCaseToFields,
		"cfs.`fail_count`",
		"cfs.`case_type`",
		"cfs.`updated_at` AS `uptime`",
		"cfs.`id` AS `cfs_id`",
		"ic.`document_id` AS `branch_id`",
	)
	interfaceCaseSqlTmp := squirrel.Select(
		interfaceCaseToFields...,
	).From(m.Table()+" AS "+caseFailStatAs).LeftJoin(
		fmt.Sprintf(
			"%s AS ic ON ic.`project_id` = cfs.`project_id` AND ic.`case_id` = cfs.`case_id` AND ic.`deleted` = cfs.`deleted`",
			m.i.Table(),
		),
	).Where(
		"cfs.`project_id` = ? AND cfs.`case_type` = ? AND ic.`latest` = ? AND cfs.`deleted` = ? AND cfs.`updated_at` > DATE_SUB(CURDATE(), INTERVAL ? DAY)",
		req.ProjectId, commonconsts.CaseTypeInterfaceCase, constants.IsLatestVersion, constants.NotDeleted,
		common.ConstCaseFailStatNDays,
	)

	interfaceSqlTmpQuery := sqlbuilder.SearchOptionsWithAlias(
		interfaceCaseSqlTmp, interfaceCaseTableAs,
		sqlbuilder.WithCondition(m.i, req.Condition),
	)
	// 合并
	interfaceSqlTmpQueryToSql, args, _ := interfaceSqlTmpQuery.ToSql()
	unionSql := apiCaseSqlTmpQuery.Suffix("UNION "+interfaceSqlTmpQueryToSql, args...)
	// fail_count desc
	sb = squirrel.Select("t.*").FromSelect(unionSql, "t").OrderBy("t.`fail_count` DESC", "t.`cfs_id` DESC")
	sb = sqlbuilder.SearchOptionsWithAlias(sb, "t", sqlbuilder.WithPagination(m.a, req.Pagination))

	scb = squirrel.Select("COUNT(*)").FromSelect(unionSql, "t")

	return searchFailLogCaseSelectBuilder{SelectBuilder: sb}, searchFailLogCaseCountBuilder{SelectBuilder: scb}
}

func (m *customCaseFailStatModel) UpdateByProjectIdCaseIdCaseTypeForFailCountIncr(
	ctx context.Context, session sqlx.Session, projectId, caseId, caseType string,
) (sql.Result, error) {
	data, err := m.FindNoCacheByProjectIdCaseIdCaseType(ctx, projectId, caseId, caseType)
	if err != nil {
		return nil, err
	}
	if data == nil {
		return nil, errorx.Err(errorx.DBError, "要更新的数据不存在")
	}

	managerCaseFailStatIdKey := fmt.Sprintf("%s%v", cacheManagerCaseFailStatIdPrefix, data.Id)

	stmt, values, err := squirrel.Update(m.table).
		Set("`fail_count`", squirrel.Expr("`fail_count` + 1")).
		Set("`version`", squirrel.Expr("`version` + 1")).
		Where(
			"`project_id` = ? AND `case_id` = ? AND `case_type` = ? AND `version` = ? AND `deleted` = ?",
			projectId, caseId, caseType, data.Version, constants.NotDeleted,
		).
		ToSql()
	if err != nil {
		return nil, err
	}

	return m.ExecCtx(
		ctx, func(ctx context.Context, conn sqlx.SqlConn) (sql.Result, error) {
			if session != nil {
				return session.ExecCtx(ctx, stmt, values...)
			}
			return conn.ExecCtx(ctx, stmt, values...)
		}, managerCaseFailStatIdKey,
	)
}

func (m *customCaseFailStatModel) FindBeforeNDaysRecords(ctx context.Context, days int) ([]*CaseFailStat, error) {
	return m.FindNoCacheByQuery(
		ctx, m.SelectBuilder().Where("`updated_at` < DATE_SUB(CURDATE(), INTERVAL ? DAY)", days),
	)
}

func (m *customCaseFailStatModel) FindAll(ctx context.Context) ([]*CaseFailStat, error) {
	return m.FindNoCacheByQuery(ctx, m.SelectBuilder())
}

func (m *customCaseFailStatModel) DeleteBeforeNDaysRecords(
	ctx context.Context, session sqlx.Session, days int,
) error {
	keys := m.getKeysByBeforeNDays(ctx, days)

	_, err := m.ExecCtx(
		ctx, func(ctx context.Context, conn sqlx.SqlConn) (sql.Result, error) {
			stmt, values, err := squirrel.Delete(m.table).
				Where("`updated_at` < DATE_SUB(CURDATE(), INTERVAL ? DAY)", days).
				ToSql()
			if err != nil {
				return nil, err
			}

			if session != nil {
				return session.ExecCtx(ctx, stmt, values...)
			}
			return conn.ExecCtx(ctx, stmt, values...)
		}, keys...,
	)
	return err
}

func (m *customCaseFailStatModel) getKeysByCaseID(ctx context.Context, projectID, caseID, caseType string) []string {
	sb := m.SelectBuilder().Where("`project_id` = ? AND `case_id` = ? AND `case_type` = ?", projectID, caseID, caseType)
	cs, err := m.FindNoCacheByQuery(ctx, sb)
	if err != nil {
		return make([]string, 0)
	}

	keys := make([]string, 0, len(cs))
	for _, c := range cs {
		keys = append(keys, fmt.Sprintf("%s%v", cacheManagerCaseFailStatIdPrefix, c.Id))
	}

	return keys
}

func (m *customCaseFailStatModel) getKeysByBeforeNDays(ctx context.Context, days int) []string {
	cs, err := m.FindBeforeNDaysRecords(ctx, days)
	if err != nil {
		return make([]string, 0)
	}

	keys := make([]string, 0, len(cs))
	for _, c := range cs {
		keys = append(keys, fmt.Sprintf("%s%v", cacheManagerCaseFailStatIdPrefix, c.Id))
	}

	return keys
}
