package model

import (
	"context"
	"database/sql"
	"fmt"
	"time"

	"github.com/Masterminds/squirrel"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/types"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/userinfo"
)

var (
	_ FunctionModel = (*customFunctionModel)(nil)

	functionInsertFields = stringx.Remove(
		functionFieldNames, "`id`", "`create_time`", "`update_time`", "`deleted_by`", "`created_at`", "`updated_at`",
		"`deleted_at`",
	)

	// cacheManagerFunctionProjectIdNameTypeLatestPrefix = "cache:manager:function:projectId:name:type:latest:"
)

type (
	// FunctionModel is an interface to be customized, add more methods here,
	// and implement the added methods in customFunctionModel.
	FunctionModel interface {
		functionModel
		types.DBModel

		Trans(ctx context.Context, fn func(context context.Context, session sqlx.Session) error) error
		InsertBuilder(data *Function) squirrel.InsertBuilder
		UpdateBuilder(data *Function) squirrel.UpdateBuilder
		SelectBuilder() squirrel.SelectBuilder
		SelectCountBuilder() squirrel.SelectBuilder
		FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error)
		FindNoCacheByQuery(ctx context.Context, builder squirrel.SelectBuilder) ([]*Function, error)

		InsertTX(ctx context.Context, session sqlx.Session, data *Function) (sql.Result, error)
		UpdateTX(ctx context.Context, session sqlx.Session, data *Function) (sql.Result, error)
		FindLatestOneNoCache(ctx context.Context, projectId, name, tp string) (*Function, error)
		RemoveByNameAndType(ctx context.Context, session sqlx.Session, projectId, name, tp string) (sql.Result, error)
	}

	customFunctionModel struct {
		*defaultFunctionModel
	}
)

// NewFunctionModel returns a model for the database table.
func NewFunctionModel(conn sqlx.SqlConn, c cache.CacheConf) FunctionModel {
	return &customFunctionModel{
		defaultFunctionModel: newFunctionModel(conn, c),
	}
}

func (m *customFunctionModel) Table() string {
	return m.table
}

func (m *customFunctionModel) Fields() []string {
	return functionFieldNames
}

func (m *customFunctionModel) Trans(
	ctx context.Context, fn func(context context.Context, session sqlx.Session) error,
) error {
	return m.TransactCtx(ctx, fn)
}

func (m *customFunctionModel) InsertBuilder(data *Function) squirrel.InsertBuilder {
	zeroTime := time.Time{}
	if data.CreatedAt == zeroTime {
		return squirrel.Insert(m.table).Columns(functionInsertFields...).Values(
			data.ProjectId, data.Name, data.Type, data.Category, data.Description, data.Language, data.Content,
			data.Parameters, data.Returns, data.Example, data.Version, data.Latest, data.Deleted, data.CreatedBy,
			data.UpdatedBy,
		)
	} else {
		return squirrel.Insert(m.table).Columns(functionInsertFields...).Columns("`created_at`").Values(
			data.ProjectId, data.Name, data.Type, data.Category, data.Description, data.Language, data.Content,
			data.Parameters, data.Returns, data.Example, data.Version, data.Latest, data.Deleted, data.CreatedBy,
			data.UpdatedBy, data.CreatedAt,
		)
	}
}

func (m *customFunctionModel) UpdateBuilder(data *Function) squirrel.UpdateBuilder {
	eq := squirrel.Eq{
		"`category`":    data.Category,
		"`description`": data.Description,
		"`language`":    data.Language,
		"`content`":     data.Content,
		"`parameters`":  data.Parameters,
		"`returns`":     data.Returns,
		"`example`":     data.Example,
		"`latest`":      data.Latest,
		"`deleted`":     data.Deleted,
		"`updated_by`":  data.UpdatedBy,
		"`deleted_by`":  data.DeletedBy,
		"`deleted_at`":  data.DeletedAt,
	}
	return squirrel.Update(m.table).SetMap(eq).Where("`id` = ?", data.Id)
}

func (m *customFunctionModel) SelectBuilder() squirrel.SelectBuilder {
	return squirrel.Select(functionFieldNames...).Where("`deleted` = ?", constants.NotDeleted).From(m.table)
}

func (m *customFunctionModel) SelectCountBuilder() squirrel.SelectBuilder {
	return squirrel.Select("count(*)").Where("`deleted` = ?", constants.NotDeleted).From(m.table)
}

func (m *customFunctionModel) FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error) {
	query, values, err := countBuilder.ToSql()
	if err != nil {
		return 0, err
	}

	var resp int64
	err = m.QueryRowNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return 0, err
	}
}

func (m *customFunctionModel) FindNoCacheByQuery(ctx context.Context, builder squirrel.SelectBuilder) (
	[]*Function, error,
) {
	query, values, err := builder.ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*Function
	err = m.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

func (m *customFunctionModel) InsertTX(ctx context.Context, session sqlx.Session, data *Function) (sql.Result, error) {
	managerFunctionIdKey := fmt.Sprintf("%s%v", cacheManagerFunctionIdPrefix, data.Id)
	managerFunctionProjectIdNameTypeVersionKey := fmt.Sprintf(
		"%s%v:%v:%v:%v", cacheManagerFunctionProjectIdNameTypeVersionPrefix, data.ProjectId, data.Name, data.Type,
		data.Version,
	)
	// managerFunctionProjectIdNameTypeLatestKey := fmt.Sprintf("%s%v:%v:%v:%v", cacheManagerFunctionProjectIdNameTypeLatestPrefix, data.ProjectId, data.Name, data.Type, constants.IsLatestVersion)

	return m.ExecCtx(
		ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
			query, values, err := m.InsertBuilder(data).ToSql()
			if err != nil {
				return nil, err
			}
			if session != nil {
				return session.ExecCtx(ctx, query, values...)
			}
			return conn.ExecCtx(ctx, query, values...)
		}, managerFunctionIdKey, managerFunctionProjectIdNameTypeVersionKey,
	)
}

func (m *customFunctionModel) UpdateTX(ctx context.Context, session sqlx.Session, data *Function) (sql.Result, error) {
	managerFunctionIdKey := fmt.Sprintf("%s%v", cacheManagerFunctionIdPrefix, data.Id)
	managerFunctionProjectIdNameTypeVersionKey := fmt.Sprintf(
		"%s%v:%v:%v:%v", cacheManagerFunctionProjectIdNameTypeVersionPrefix, data.ProjectId, data.Name, data.Type,
		data.Version,
	)
	// managerFunctionProjectIdNameTypeLatestKey := fmt.Sprintf("%s%v:%v:%v:%v", cacheManagerFunctionProjectIdNameTypeLatestPrefix, data.ProjectId, data.Name, data.Type, constants.IsLatestVersion)

	return m.ExecCtx(
		ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
			query, values, err := m.UpdateBuilder(data).ToSql()
			if err != nil {
				return nil, err
			}
			if session != nil {
				return session.ExecCtx(ctx, query, values...)
			}
			return conn.ExecCtx(ctx, query, values...)
		}, managerFunctionIdKey, managerFunctionProjectIdNameTypeVersionKey,
	)
}

func (m *customFunctionModel) FindLatestOneNoCache(ctx context.Context, projectId, name, tp string) (*Function, error) {
	sb := m.SelectBuilder().Where(
		"`project_id` = ? AND `name` = ? AND `type` = ? AND `latest` = ?", projectId, name, tp,
		constants.IsLatestVersion,
	).OrderBy("`updated_at` DESC").Limit(1)
	resp, err := m.FindNoCacheByQuery(ctx, sb)
	if err != nil {
		return nil, err
	} else if len(resp) == 0 {
		return nil, ErrNotFound
	}
	return resp[0], nil
}

//func (m *customFunctionModel) FindLatestOne(ctx context.Context, projectId, name, tp string) (*Function, error) {
//	managerFunctionProjectIdNameTypeLatestKey := fmt.Sprintf("%s%v:%v:%v:%v", cacheManagerFunctionProjectIdNameTypeLatestPrefix, projectId, name, tp, constants.IsLatestVersion)
//
//	var resp Function
//	err := m.QueryRowCtx(ctx, &resp, managerFunctionProjectIdNameTypeLatestKey, func(ctx context.Context, conn sqlx.SqlConn, v interface{}) error {
//		query, values, err := m.SelectBuilder().Where("`project_id` = ? and `name` = ? and `type` = ? and `latest` = ?", projectId, name, tp, constants.IsLatestVersion).OrderBy("`updated_at` DESC").Limit(1).ToSql()
//		if err != nil {
//			return err
//		}
//
//		if err = conn.QueryRowCtx(ctx, &resp, query, values...); err != nil {
//			return err
//		}
//
//		return nil
//	})
//	switch err {
//	case nil:
//		return &resp, nil
//	default:
//		return nil, err
//	}
//}

func (m *customFunctionModel) RemoveByNameAndType(
	ctx context.Context, session sqlx.Session, projectId, name, tp string,
) (sql.Result, error) {
	keys := m.getKeysByNameAndType(ctx, projectId, name, tp)

	return m.ExecCtx(
		ctx, func(ctx context.Context, conn sqlx.SqlConn) (sql.Result, error) {
			/*
				SQL:
				UPDATE `function`
				SET `deleted` = ?, `deleted_by` = ?, `deleted_at` = ?
				WHERE `project_id` = ?
				  AND `name` = ?
				  AND `type` = ?
			*/
			stmt, values, err := squirrel.Update(m.table).
				SetMap(
					squirrel.Eq{
						"`deleted`":    constants.HasDeleted,
						"`deleted_by`": userinfo.FromContext(ctx).Account,
						"`deleted_at`": time.Now(),
					},
				).
				Where("`project_id` = ? AND `name` = ? AND `type` = ?", projectId, name, tp).
				ToSql()
			if err != nil {
				return nil, err
			}

			if session != nil {
				return session.ExecCtx(ctx, stmt, values...)
			}
			return conn.ExecCtx(ctx, stmt, values...)
		}, keys...,
	)
}

func (m *customFunctionModel) getKeysByNameAndType(ctx context.Context, projectId, name, tp string) []string {
	sb := m.SelectBuilder().Where(
		"`project_id` = ? AND `name` = ? AND `type` = ? AND `deleted` = ?", projectId, name, tp, constants.NotDeleted,
	)
	cs, err := m.FindNoCacheByQuery(ctx, sb)
	if err != nil {
		return make([]string, 0)
	}

	keys := make([]string, 0, len(cs)*2)
	for _, c := range cs {
		keys = append(
			keys,
			fmt.Sprintf("%s%v", cacheManagerFunctionIdPrefix, c.Id),
			fmt.Sprintf(
				"%s%v:%v:%v:%v", cacheManagerFunctionProjectIdNameTypeVersionPrefix, c.ProjectId, c.Name, c.Type,
				c.Version,
			),
			// fmt.Sprintf("%s%v:%v:%v:%v", cacheManagerFunctionProjectIdNameTypeLatestPrefix, c.ProjectId, c.Name, c.Type, constants.IsLatestVersion),
		)
	}

	return keys
}
