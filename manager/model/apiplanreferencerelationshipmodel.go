package model

import (
	"context"
	"database/sql"
	"fmt"
	"time"

	"github.com/Masterminds/squirrel"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/types"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/userinfo"
)

var (
	_ ApiPlanReferenceRelationshipModel = (*customApiPlanReferenceRelationshipModel)(nil)

	apiPlanReferenceRelationshipInsertFields = stringx.Remove(
		apiPlanReferenceRelationshipFieldNames, "`id`", "`create_time`", "`update_time`", "`deleted_by`",
		"`created_at`", "`updated_at`", "`deleted_at`",
	)
)

type (
	// ApiPlanReferenceRelationshipModel is an interface to be customized, add more methods here,
	// and implement the added methods in customApiPlanReferenceRelationshipModel.
	ApiPlanReferenceRelationshipModel interface {
		apiPlanReferenceRelationshipModel
		types.DBModel

		Trans(ctx context.Context, fn func(context context.Context, session sqlx.Session) error) error
		InsertBuilder(data *ApiPlanReferenceRelationship) squirrel.InsertBuilder
		UpdateBuilder(data *ApiPlanReferenceRelationship) squirrel.UpdateBuilder
		SelectBuilder() squirrel.SelectBuilder
		SelectCountBuilder() squirrel.SelectBuilder
		FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error)
		FindNoCacheByQuery(ctx context.Context, builder squirrel.SelectBuilder) ([]*ApiPlanReferenceRelationship, error)

		FindOneByPlanIdReference(
			ctx context.Context, projectId, planId, referenceParentId, referenceType, referenceId string,
		) (*ApiPlanReferenceRelationship, error)
		FindReferenceByReference(
			ctx context.Context, projectId, referenceType, referenceId string, includeChildren bool,
		) ([]*ApiPlanReferenceRelationship, error)
		FindReferenceByPlanId(ctx context.Context, projectId, planId string) ([]*ApiPlanReferenceRelationship, error)
		FindReferenceByPlanIdReferenceParentId(
			ctx context.Context, projectId, planId, referenceParentId string,
		) ([]*ApiPlanReferenceRelationship, error)
		FindReferenceByPlanIdReference(
			ctx context.Context, projectId, planId, referenceParentId, referenceType, referenceId string,
			includeChildren bool,
		) ([]*ApiPlanReferenceRelationship, error)
		RemoveByReferenceId(
			ctx context.Context, session sqlx.Session, projectId, referenceType, referenceId string,
		) (sql.Result, error)
		RemoveByPlanId(ctx context.Context, session sqlx.Session, projectId, planId string) (sql.Result, error)
		RemoveByPlanIdReference(
			ctx context.Context, session sqlx.Session,
			projectId, planId, referenceParentId, referenceType, referenceId string,
		) (sql.Result, error)
	}

	customApiPlanReferenceRelationshipModel struct {
		*defaultApiPlanReferenceRelationshipModel
	}
)

// NewApiPlanReferenceRelationshipModel returns a model for the database table.
func NewApiPlanReferenceRelationshipModel(conn sqlx.SqlConn, c cache.CacheConf) ApiPlanReferenceRelationshipModel {
	return &customApiPlanReferenceRelationshipModel{
		defaultApiPlanReferenceRelationshipModel: newApiPlanReferenceRelationshipModel(conn, c),
	}
}

func (m *customApiPlanReferenceRelationshipModel) Table() string {
	return m.table
}

func (m *customApiPlanReferenceRelationshipModel) Fields() []string {
	return apiPlanReferenceRelationshipFieldNames
}

func (m *customApiPlanReferenceRelationshipModel) Trans(
	ctx context.Context, fn func(context context.Context, session sqlx.Session) error,
) error {
	return m.TransactCtx(ctx, fn)
}

func (m *customApiPlanReferenceRelationshipModel) InsertBuilder(data *ApiPlanReferenceRelationship) squirrel.InsertBuilder {
	return squirrel.Insert(m.table).Columns(apiPlanReferenceRelationshipInsertFields...).Values(
		data.ProjectId, data.ReferenceParentId, data.ReferenceType, data.ReferenceId, data.PlanId, data.State,
		data.Deleted, data.CreatedBy, data.UpdatedBy,
	)
}

func (m *customApiPlanReferenceRelationshipModel) UpdateBuilder(data *ApiPlanReferenceRelationship) squirrel.UpdateBuilder {
	eq := squirrel.Eq{
		"`state`":      data.State,
		"`deleted`":    data.Deleted,
		"`updated_by`": data.UpdatedBy,
		"`deleted_by`": data.DeletedBy,
		"`deleted_at`": data.DeletedAt,
	}
	return squirrel.Update(m.table).SetMap(eq).Where("`id` = ?", data.Id)
}

func (m *customApiPlanReferenceRelationshipModel) SelectBuilder() squirrel.SelectBuilder {
	return squirrel.Select(apiPlanReferenceRelationshipFieldNames...).Where(
		"`deleted` = ?", constants.NotDeleted,
	).From(m.table)
}

func (m *customApiPlanReferenceRelationshipModel) SelectCountBuilder() squirrel.SelectBuilder {
	return squirrel.Select("count(*)").Where("`deleted` = ?", constants.NotDeleted).From(m.table)
}

func (m *customApiPlanReferenceRelationshipModel) FindCount(
	ctx context.Context, countBuilder squirrel.SelectBuilder,
) (int64, error) {
	query, values, err := countBuilder.ToSql()
	if err != nil {
		return 0, err
	}

	var resp int64
	err = m.QueryRowNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return 0, err
	}
}

func (m *customApiPlanReferenceRelationshipModel) FindNoCacheByQuery(
	ctx context.Context, builder squirrel.SelectBuilder,
) ([]*ApiPlanReferenceRelationship, error) {
	query, values, err := builder.ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*ApiPlanReferenceRelationship
	err = m.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

func (m *customApiPlanReferenceRelationshipModel) FindOneByPlanIdReference(
	ctx context.Context, projectId, planId, referenceParentId, referenceType, referenceId string,
) (*ApiPlanReferenceRelationship, error) {
	sb := m.SelectBuilder().Where(
		"`project_id` = ? AND `reference_type` = ? AND `reference_id` = ? AND `plan_id` = ?", projectId, referenceType,
		referenceId, planId,
	)
	if referenceParentId != "" {
		sb = sb.Where("`reference_parent_id` = ?", referenceParentId)
	}
	sb = sb.OrderBy("`updated_at` DESC").Limit(1)

	resp, err := m.FindNoCacheByQuery(ctx, sb)
	if err != nil {
		return nil, err
	} else if len(resp) == 0 {
		return nil, ErrNotFound
	}
	return resp[0], nil
}

// FindReferenceByReference 根据引用ID查询引用关系（返回的引用关系可能涉及多个API计划）
func (m *customApiPlanReferenceRelationshipModel) FindReferenceByReference(
	ctx context.Context, projectId, referenceType, referenceId string, includeChildren bool,
) ([]*ApiPlanReferenceRelationship, error) {
	var sb squirrel.SelectBuilder

	if includeChildren {
		sb = m.SelectBuilder().Where(
			"`project_id` = ? AND ((`reference_type` = ? AND `reference_id` = ?) OR (`reference_parent_id` = )",
			projectId, referenceType, referenceId, referenceId,
		)
	} else {
		sb = m.SelectBuilder().Where(
			"`project_id` = ? AND `reference_type` = ? AND `reference_id` = ?", projectId, referenceType, referenceId,
		)
	}

	return m.FindNoCacheByQuery(ctx, sb)
}

// FindReferenceByPlanId 根据API计划ID查询引用关系（返回指定计划下的全部引用关系）
func (m *customApiPlanReferenceRelationshipModel) FindReferenceByPlanId(
	ctx context.Context, projectId, planId string,
) ([]*ApiPlanReferenceRelationship, error) {
	/*
		SQL
		SELECT *
		FROM `api_plan_reference_relationship`
		WHERE `project_id` = ?
		  AND `plan_id` = ?
		  AND `deleted` = ?
	*/

	sb := m.SelectBuilder().Where("`project_id` = ? AND `plan_id` = ?", projectId, planId)

	return m.FindNoCacheByQuery(ctx, sb)
}

// FindReferenceByPlanIdReferenceParentId 根据API计划ID和引用父ID查询引用关系（返回指定计划下指定父引用下的引用关系）
func (m *customApiPlanReferenceRelationshipModel) FindReferenceByPlanIdReferenceParentId(
	ctx context.Context, projectId, planId, referenceParentId string,
) ([]*ApiPlanReferenceRelationship, error) {
	sb := m.SelectBuilder().Where(
		"`project_id` = ? AND `plan_id` = ? AND `reference_parent_id` = ?", projectId, planId, referenceParentId,
	)

	return m.FindNoCacheByQuery(ctx, sb)
}

// FindReferenceByPlanIdReference 根据API计划ID和引用ID查询引用关系（返回的引用关系只涉及指定的API计划）
func (m *customApiPlanReferenceRelationshipModel) FindReferenceByPlanIdReference(
	ctx context.Context, projectId, planId, referenceParentId, referenceType, referenceId string, includeChildren bool,
) ([]*ApiPlanReferenceRelationship, error) {
	sb := m.SelectBuilder().Where("`project_id` = ? AND `plan_id` = ?", projectId, planId)

	if referenceParentId != "" {
		sb = sb.Where(
			"`reference_parent_id` = ? AND `reference_type` = ? AND `reference_id` = ?", referenceParentId,
			referenceType, referenceId,
		)
	} else if includeChildren {
		sb = sb.Where(
			"((`reference_type` = ? AND `reference_id` = ?) OR (`reference_parent_id` = ?))", referenceType,
			referenceId, referenceId,
		)
	} else {
		sb = sb.Where("`reference_type` = ? AND `reference_id` = ?", referenceType, referenceId)
	}

	return m.FindNoCacheByQuery(ctx, sb)
}

// RemoveByReferenceId 根据引用ID删除引用关系（可能涉及多个API计划）
func (m *customApiPlanReferenceRelationshipModel) RemoveByReferenceId(
	ctx context.Context, session sqlx.Session, projectId, referenceType, referenceId string,
) (sql.Result, error) {
	// 注：对于删除API计划下集合的引用关系时，必须把集合下用例的引用关系同时删除
	keys := m.getKeysByReferenceId(ctx, projectId, referenceType, referenceId, true)

	return m.ExecCtx(
		ctx, func(ctx context.Context, conn sqlx.SqlConn) (sql.Result, error) {
			/*
				SQL:
				UPDATE `api_plan_reference_relationship`
				SET `deleted` = ?, `deleted_by` = ?, `deleted_at` = ?
				WHERE `project_id` = ?
				  AND ((`reference_type` = ? AND `reference_id` = ?) OR (`reference_parent_id` = ?))
			*/

			stmt, values, err := squirrel.Update(m.table).
				SetMap(
					squirrel.Eq{
						"`deleted`":    constants.HasDeleted,
						"`deleted_by`": userinfo.FromContext(ctx).Account,
						"`deleted_at`": time.Now(),
					},
				).
				Where(
					"`project_id` = ? AND ((`reference_type` = ? AND `reference_id` = ?) OR (`reference_parent_id` = ?))",
					projectId, referenceType, referenceId, referenceId,
				).
				ToSql()
			if err != nil {
				return nil, err
			}

			if session != nil {
				return session.ExecCtx(ctx, stmt, values...)
			}
			return conn.ExecCtx(ctx, stmt, values...)
		}, keys...,
	)
}

// RemoveByPlanId 根据API计划ID删除引用关系（指定API计划下的全部引用关系）
func (m *customApiPlanReferenceRelationshipModel) RemoveByPlanId(
	ctx context.Context, session sqlx.Session, projectId, planId string,
) (sql.Result, error) {
	keys := m.getKeysByPlanId(ctx, projectId, planId)

	return m.ExecCtx(
		ctx, func(ctx context.Context, conn sqlx.SqlConn) (sql.Result, error) {
			/*
				SQL:
				UPDATE `api_plan_reference_relationship`
				SET `deleted` = ?, `deleted_by` = ?, `deleted_at` = ?
				WHERE `project_id` = ?
				  AND `plan_id` = ?
			*/

			stmt, values, err := squirrel.Update(m.table).
				SetMap(
					squirrel.Eq{
						"`deleted`":    constants.HasDeleted,
						"`deleted_by`": userinfo.FromContext(ctx).Account,
						"`deleted_at`": time.Now(),
					},
				).
				Where("`project_id` = ? AND `plan_id` = ?", projectId, planId).
				ToSql()
			if err != nil {
				return nil, err
			}

			if session != nil {
				return session.ExecCtx(ctx, stmt, values...)
			}
			return conn.ExecCtx(ctx, stmt, values...)
		}, keys...,
	)
}

// RemoveByPlanIdReference 根据API计划ID和引用ID删除引用关系（只涉及指定的API计划下指定的引用关系）
func (m *customApiPlanReferenceRelationshipModel) RemoveByPlanIdReference(
	ctx context.Context, session sqlx.Session, projectId, planId, referenceParentId, referenceType, referenceId string,
) (sql.Result, error) {
	// 注：对于删除API计划下集合的引用关系时，必须把集合下用例的引用关系同时删除
	keys := m.getKeysByPlanIdReference(ctx, projectId, planId, referenceParentId, referenceType, referenceId, true)

	return m.ExecCtx(
		ctx, func(ctx context.Context, conn sqlx.SqlConn) (sql.Result, error) {
			/*
				SQL:
				UPDATE `api_plan_reference_relationship`
				SET `deleted` = ?, `deleted_by` = ?, `deleted_at` = ?
				WHERE `project_id` = ?
				  AND `reference_parent_id` = ?
				  AND `reference_type` = ?
				  AND `reference_id` = ?

				UPDATE `api_plan_reference_relationship`
				SET `deleted` = ?, `deleted_by` = ?, `deleted_at` = ?
				WHERE `project_id` = ?
				  AND ((`reference_type` = ? AND `reference_id` = ?) OR (`reference_parent_id` = ?))
			*/

			ub := squirrel.Update(m.table).
				SetMap(
					squirrel.Eq{
						"`deleted`":    constants.HasDeleted,
						"`deleted_by`": userinfo.FromContext(ctx).Account,
						"`deleted_at`": time.Now(),
					},
				).
				Where("`project_id` = ? AND `plan_id` = ?", projectId, planId)

			if referenceParentId != "" {
				// 删除子引用关系，如：API集合中移除API用例，删除接口用例
				ub = ub.Where(
					"`reference_parent_id` = ? AND `reference_type` = ? AND `reference_id` = ?", referenceParentId,
					referenceType, referenceId,
				)
			} else {
				// 删除父、子引用关系，如：删除API集合，删除接口文档
				ub = ub.Where(
					"((`reference_type` = ? AND `reference_id` = ?) OR (`reference_parent_id` = ?))", referenceType,
					referenceId, referenceId,
				)
			}

			stmt, values, err := ub.ToSql()
			if err != nil {
				return nil, err
			}

			if session != nil {
				return session.ExecCtx(ctx, stmt, values...)
			}
			return conn.ExecCtx(ctx, stmt, values...)
		}, keys...,
	)
}

func (m *customApiPlanReferenceRelationshipModel) getKeysByReferenceId(
	ctx context.Context, projectId, referenceType, referenceId string, includeChildren bool,
) []string {
	cs, err := m.FindReferenceByReference(ctx, projectId, referenceType, referenceId, includeChildren)
	if err != nil {
		return make([]string, 0)
	}

	keys := make([]string, 0, len(cs))
	for _, c := range cs {
		keys = append(keys, fmt.Sprintf("%s%v", cacheManagerApiPlanReferenceRelationshipIdPrefix, c.Id))
	}

	return keys
}

func (m *customApiPlanReferenceRelationshipModel) getKeysByPlanId(
	ctx context.Context, projectId, planId string,
) []string {
	cs, err := m.FindReferenceByPlanId(ctx, projectId, planId)
	if err != nil {
		return make([]string, 0)
	}

	keys := make([]string, 0, len(cs))
	for _, c := range cs {
		keys = append(keys, fmt.Sprintf("%s%v", cacheManagerApiPlanReferenceRelationshipIdPrefix, c.Id))
	}

	return keys
}

func (m *customApiPlanReferenceRelationshipModel) getKeysByPlanIdReference(
	ctx context.Context, projectId, planId, referenceParentId, referenceType, referenceId string, includeChildren bool,
) []string {
	cs, err := m.FindReferenceByPlanIdReference(
		ctx, projectId, planId, referenceParentId, referenceType, referenceId, includeChildren,
	)
	if err != nil {
		return make([]string, 0)
	}

	keys := make([]string, 0, len(cs))
	for _, c := range cs {
		keys = append(keys, fmt.Sprintf("%s%v", cacheManagerApiPlanReferenceRelationshipIdPrefix, c.Id))
	}

	return keys
}
