package model

import (
	"context"
	"database/sql"
	"fmt"

	"github.com/Masterminds/squirrel"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlc"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/types"
)

var (
	_ GitProjectTreeModel = (*customGitProjectTreeModel)(nil)

	gitProjectTreeInsertFields = stringx.Remove(
		gitProjectTreeFieldNames, "`id`", "`create_time`", "`update_time`", "`deleted_by`", "`created_at`",
		"`updated_at`", "`deleted_at`",
	)

	cacheManagerGitProjectTreeProjectIDGitConfigIDPathPrefix = "cache:manager:gitProjectTree:projectID:gitConfigID:path:"
)

type (
	// GitProjectTreeModel is an interface to be customized, add more methods here,
	// and implement the added methods in customGitProjectTreeModel.
	GitProjectTreeModel interface {
		gitProjectTreeModel
		types.DBModel

		Trans(ctx context.Context, fn func(context context.Context, session sqlx.Session) error) error
		InsertBuilder(data *GitProjectTree) squirrel.InsertBuilder
		UpdateBuilder(data *GitProjectTree) squirrel.UpdateBuilder
		SelectBuilder() squirrel.SelectBuilder
		SelectCountBuilder() squirrel.SelectBuilder
		FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error)
		FindNoCacheByQuery(ctx context.Context, selectBuilder squirrel.SelectBuilder) ([]*GitProjectTree, error)

		InsertTX(ctx context.Context, session sqlx.Session, data *GitProjectTree) (sql.Result, error)
		UpdateTX(ctx context.Context, session sqlx.Session, data *GitProjectTree) (sql.Result, error)
		FindAll(ctx context.Context, projectID, configID string) ([]*GitProjectTree, error)
		FindAllPath(ctx context.Context, projectID, configID string) ([]*UiCasePath, error)
		FindAllWithAlias(ctx context.Context, projectID, configID string) ([]*GitProjectTree, error)
		FindOneByPath(ctx context.Context, projectID, configID, path string) (*GitProjectTree, error)
		FindRootNodeOfTree(ctx context.Context, projectID, configID string) (*GitProjectTree, error)
		FindRemovedPathNoCacheByQuery(
			ctx context.Context, projectId, gitConfigId, path string,
		) (*GitProjectTree, error)
	}

	customGitProjectTreeModel struct {
		*defaultGitProjectTreeModel
	}
)

// NewGitProjectTreeModel returns a model for the database table.
func NewGitProjectTreeModel(conn sqlx.SqlConn, c cache.CacheConf, opts ...cache.Option) GitProjectTreeModel {
	return &customGitProjectTreeModel{
		defaultGitProjectTreeModel: newGitProjectTreeModel(conn, c, opts...),
	}
}

func (m *customGitProjectTreeModel) Table() string {
	return m.table
}

func (m *customGitProjectTreeModel) Fields() []string {
	return gitProjectTreeFieldNames
}

func (m *customGitProjectTreeModel) Trans(
	ctx context.Context, fn func(context context.Context, session sqlx.Session) error,
) error {
	return m.TransactCtx(ctx, fn)
}

func (m *customGitProjectTreeModel) InsertBuilder(data *GitProjectTree) squirrel.InsertBuilder {
	return squirrel.Insert(m.table).Columns(gitProjectTreeInsertFields...).Values(
		data.ProjectId, data.GitConfigId, data.Path, data.ParentPath, data.Name, data.Alias, data.Type, data.Tags,
		data.TriggeredBy, data.TriggeredAt, data.Deleted, data.CreatedBy, data.UpdatedBy,
	)
}

func (m *customGitProjectTreeModel) UpdateBuilder(data *GitProjectTree) squirrel.UpdateBuilder {
	eq := squirrel.Eq{
		"`alias`":        data.Alias,
		"`tags`":         data.Tags,
		"`triggered_by`": data.TriggeredBy,
		"`triggered_at`": data.TriggeredAt,
		"`deleted`":      data.Deleted,
		"`updated_by`":   data.UpdatedBy,
		"`deleted_by`":   data.DeletedBy,
		"`deleted_at`":   data.DeletedAt,
	}
	return squirrel.Update(m.table).SetMap(eq).Where("`id` = ?", data.Id)
}

func (m *customGitProjectTreeModel) SelectBuilder() squirrel.SelectBuilder {
	return squirrel.Select(gitProjectTreeFieldNames...).Where("`deleted` = ?", constants.NotDeleted).From(m.table)
}

func (m *customGitProjectTreeModel) SelectCountBuilder() squirrel.SelectBuilder {
	return squirrel.Select("count(*)").Where("`deleted` = ?", constants.NotDeleted).From(m.table)
}

func (m *customGitProjectTreeModel) FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error) {
	query, values, err := countBuilder.ToSql()
	if err != nil {
		return 0, err
	}

	var resp int64
	err = m.QueryRowNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return 0, err
	}
}

func (m *customGitProjectTreeModel) FindNoCacheByQuery(
	ctx context.Context, selectBuilder squirrel.SelectBuilder,
) ([]*GitProjectTree, error) {
	query, values, err := selectBuilder.ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*GitProjectTree
	err = m.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

func (m *customGitProjectTreeModel) InsertTX(
	ctx context.Context, session sqlx.Session, data *GitProjectTree,
) (sql.Result, error) {
	managerGitProjectTreeIDKey := fmt.Sprintf("%s%v", cacheManagerGitProjectTreeIdPrefix, data.Id)
	managerGitProjectTreeProjectIDGitConfigIDPathKey := fmt.Sprintf(
		"%s%v:%v:%v", cacheManagerGitProjectTreeProjectIDGitConfigIDPathPrefix, data.ProjectId, data.GitConfigId,
		data.Path,
	)

	return m.ExecCtx(
		ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
			query, values, err := m.InsertBuilder(data).ToSql()
			if err != nil {
				return nil, err
			}
			if session != nil {
				return session.ExecCtx(ctx, query, values...)
			}
			return conn.ExecCtx(ctx, query, values...)
		}, managerGitProjectTreeIDKey, managerGitProjectTreeProjectIDGitConfigIDPathKey,
	)
}

func (m *customGitProjectTreeModel) UpdateTX(
	ctx context.Context, session sqlx.Session, newData *GitProjectTree,
) (sql.Result, error) {
	data, err := m.FindOne(ctx, newData.Id)
	if err != nil {
		return nil, err
	}

	managerGitProjectTreeIDKey := fmt.Sprintf("%s%v", cacheManagerGitProjectTreeIdPrefix, data.Id)
	managerGitProjectTreeProjectIDGitConfigIDPathKey := fmt.Sprintf(
		"%s%v:%v:%v", cacheManagerGitProjectTreeProjectIDGitConfigIDPathPrefix, data.ProjectId, data.GitConfigId,
		data.Path,
	)

	return m.ExecCtx(
		ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
			query, values, err := m.UpdateBuilder(newData).ToSql()
			if err != nil {
				return nil, err
			}
			if session != nil {
				return session.ExecCtx(ctx, query, values...)
			}
			return conn.ExecCtx(ctx, query, values...)
		}, managerGitProjectTreeIDKey, managerGitProjectTreeProjectIDGitConfigIDPathKey,
	)
}

func (m *customGitProjectTreeModel) FindAll(ctx context.Context, projectID, configID string) (
	[]*GitProjectTree, error,
) {
	return m.FindNoCacheByQuery(
		ctx,
		m.SelectBuilder().
			Where("`project_id` = ? AND `git_config_id` = ?", projectID, configID).
			OrderBy("`path` DESC"),
	)
}

func (m *customGitProjectTreeModel) FindAllWithAlias(
	ctx context.Context, projectID, configID string,
) ([]*GitProjectTree, error) {
	return m.FindNoCacheByQuery(
		ctx,
		m.SelectBuilder().Where(
			"`project_id` = ? AND `git_config_id` = ? AND `alias` IS NOT NULL", projectID, configID,
		),
	)
}

func (m *customGitProjectTreeModel) FindOneByPath(
	ctx context.Context, projectID, configID, path string,
) (*GitProjectTree, error) {
	managerGitProjectTreeProjectIDGitConfigIDPathKey := fmt.Sprintf(
		"%s%v:%v:%v", cacheManagerGitProjectTreeProjectIDGitConfigIDPathPrefix, projectID, configID, path,
	)

	var resp GitProjectTree
	err := m.QueryRowIndexCtx(
		ctx, &resp, managerGitProjectTreeProjectIDGitConfigIDPathKey, m.formatPrimary,
		func(ctx context.Context, conn sqlx.SqlConn, v any) (any, error) {
			query, values, err := m.SelectBuilder().Where(
				"`project_id` = ? AND `git_config_id` = ? AND `path` = ?", projectID, configID, path,
			).OrderBy("`updated_at` DESC").Limit(1).ToSql()
			if err != nil {
				return nil, err
			}

			if err = conn.QueryRowCtx(ctx, &resp, query, values...); err != nil {
				return nil, err
			}

			return resp.Id, nil
		}, m.queryPrimary,
	)
	switch err {
	case nil:
		return &resp, nil
	default:
		return nil, err
	}
}

func (m *customGitProjectTreeModel) FindRootNodeOfTree(
	ctx context.Context, projectID, configID string,
) (*GitProjectTree, error) {
	r, err := m.FindNoCacheByQuery(
		ctx,
		m.SelectBuilder().
			Where("`project_id` = ? AND `git_config_id` = ? AND `parent_path` IS NULL", projectID, configID).
			OrderBy("`updated_at` DESC").
			Limit(1),
	)
	if err != nil {
		return nil, err
	} else if len(r) == 0 {
		return nil, ErrNotFound
	}
	return r[0], nil
}

func (m *customGitProjectTreeModel) FindAllPath(ctx context.Context, projectID, configID string) (
	[]*UiCasePath, error,
) {
	query, values, err := squirrel.Select("path").From(m.table).Where(
		"`project_id` = ? AND `git_config_id` = ? AND `deleted` = ?", projectID, configID, constants.NotDeleted,
	).ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*UiCasePath
	err = m.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

func (m *customGitProjectTreeModel) GenRemovedPathSelectBuilder(projectId, gitConfigId, path string) squirrel.SelectBuilder {
	return squirrel.Select(gitProjectTreeFieldNames...).Where(
		"`deleted` = ? and `project_id` = ? and `path` = ? and `git_config_id` = ?",
		constants.HasDeleted,
		projectId,
		path,
		gitConfigId,
	).From(m.table).
		OrderBy("updated_at desc").
		Limit(1)
}

func (m *customGitProjectTreeModel) FindRemovedPathNoCacheByQuery(
	ctx context.Context, projectId, gitConfigId, path string,
) (*GitProjectTree, error) {
	query, values, err := m.GenRemovedPathSelectBuilder(projectId, gitConfigId, path).ToSql()
	if err != nil {
		return nil, err
	}
	resp := new(GitProjectTree)
	err = m.QueryRowNoCacheCtx(ctx, resp, query, values...)
	switch err {
	case sqlc.ErrNotFound:
		return nil, nil
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}
