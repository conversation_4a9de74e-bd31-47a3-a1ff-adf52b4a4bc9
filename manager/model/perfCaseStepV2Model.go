package model

import (
	"context"
	"database/sql"
	"fmt"

	"github.com/Masterminds/squirrel"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/types"
)

var (
	_ PerfCaseStepV2Model = (*customPerfCaseStepV2Model)(nil)

	perfCaseStepV2InsertFields = stringx.Remove(
		perfCaseStepV2FieldNames, "`id`", "`create_time`", "`update_time`", "`deleted_by`", "`created_at`",
		"`updated_at`", "`deleted_at`",
	)
)

type (
	// PerfCaseStepV2Model is an interface to be customized, add more methods here,
	// and implement the added methods in customPerfCaseStepV2Model.
	PerfCaseStepV2Model interface {
		perfCaseStepV2Model
		types.DBModel

		Trans(ctx context.Context, fn func(context context.Context, session sqlx.Session) error) error
		InsertBuilder(data *PerfCaseStepV2) squirrel.InsertBuilder
		UpdateBuilder(data *PerfCaseStepV2) squirrel.UpdateBuilder
		SelectBuilder() squirrel.SelectBuilder
		SelectCountBuilder() squirrel.SelectBuilder
		FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error)
		FindNoCacheByQuery(ctx context.Context, selectBuilder squirrel.SelectBuilder) ([]*PerfCaseStepV2, error)

		FindByCaseID(ctx context.Context, projectID, caseID string) ([]*PerfCaseStepV2, error)
		DeleteByCaseID(ctx context.Context, session sqlx.Session, projectID, caseID string) (sql.Result, error)
	}

	customPerfCaseStepV2Model struct {
		*defaultPerfCaseStepV2Model

		conn sqlx.SqlConn
	}
)

// NewPerfCaseStepV2Model returns a model for the database table.
func NewPerfCaseStepV2Model(conn sqlx.SqlConn, c cache.CacheConf, opts ...cache.Option) PerfCaseStepV2Model {
	return &customPerfCaseStepV2Model{
		defaultPerfCaseStepV2Model: newPerfCaseStepV2Model(conn, c, opts...),
		conn:                       conn,
	}
}

func (m *customPerfCaseStepV2Model) Table() string {
	return m.table
}

func (m *customPerfCaseStepV2Model) Fields() []string {
	return perfCaseStepV2FieldNames
}

func (m *customPerfCaseStepV2Model) Trans(
	ctx context.Context, fn func(context context.Context, session sqlx.Session) error,
) error {
	return m.TransactCtx(ctx, fn)
}

func (m *customPerfCaseStepV2Model) InsertBuilder(data *PerfCaseStepV2) squirrel.InsertBuilder {
	return squirrel.Insert(m.table).Columns(perfCaseStepV2InsertFields...).Values(
		data.ProjectId, data.CaseId, data.StepId, data.Type, data.Index, data.Name, data.RateLimits, data.Url,
		data.Method, data.Headers, data.Body, data.Exports, data.Sleep, data.State, data.Deleted, data.CreatedBy,
		data.UpdatedBy,
	)
}

func (m *customPerfCaseStepV2Model) UpdateBuilder(data *PerfCaseStepV2) squirrel.UpdateBuilder {
	eq := squirrel.Eq{
		"`index`":       data.Index,
		"`name`":        data.Name,
		"`rate_limits`": data.RateLimits,
		"`url`":         data.Url,
		"`method`":      data.Method,
		"`headers`":     data.Headers,
		"`body`":        data.Body,
		"`exports`":     data.Exports,
		"`sleep`":       data.Sleep,
		"`state`":       data.State,
		"`deleted`":     data.Deleted,
		"`updated_by`":  data.UpdatedBy,
		"`deleted_by`":  data.DeletedBy,
		"`deleted_at`":  data.DeletedAt,
	}
	return squirrel.Update(m.table).SetMap(eq).Where("`id` = ?", data.Id)
}

func (m *customPerfCaseStepV2Model) SelectBuilder() squirrel.SelectBuilder {
	return squirrel.Select(perfCaseStepV2FieldNames...).Where("`deleted` = ?", constants.NotDeleted).From(m.table)
}

func (m *customPerfCaseStepV2Model) SelectCountBuilder() squirrel.SelectBuilder {
	return squirrel.Select("count(*)").Where("`deleted` = ?", constants.NotDeleted).From(m.table)
}

func (m *customPerfCaseStepV2Model) FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error) {
	query, values, err := countBuilder.ToSql()
	if err != nil {
		return 0, err
	}

	var resp int64
	err = m.QueryRowNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return 0, err
	}
}

func (m *customPerfCaseStepV2Model) FindNoCacheByQuery(
	ctx context.Context, selectBuilder squirrel.SelectBuilder,
) ([]*PerfCaseStepV2, error) {
	query, values, err := selectBuilder.ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*PerfCaseStepV2
	err = m.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

func (m *customPerfCaseStepV2Model) FindByCaseID(ctx context.Context, projectID, caseID string) (
	[]*PerfCaseStepV2, error,
) {
	return m.FindNoCacheByQuery(ctx, m.SelectBuilder().Where("`project_id` = ? AND `case_id` = ?", projectID, caseID))
}

func (m *customPerfCaseStepV2Model) DeleteByCaseID(
	ctx context.Context, session sqlx.Session, projectID, caseID string,
) (sql.Result, error) {
	keys := m.getKeysByCaseID(ctx, projectID, caseID)

	return m.ExecCtx(
		ctx, func(ctx context.Context, conn sqlx.SqlConn) (sql.Result, error) {
			/*
				SQL:
				DELETE FROM `perf_case_step_v2` WHERE `project_id` = ? AND `case_id` = ?
			*/

			stmt, values, err := squirrel.Delete(m.table).
				Where("`project_id` = ? AND `case_id` = ?", projectID, caseID).
				ToSql()
			if err != nil {
				return nil, err
			}

			if session != nil {
				return session.ExecCtx(ctx, stmt, values...)
			}
			return conn.ExecCtx(ctx, stmt, values...)
		}, keys...,
	)
}

func (m *customPerfCaseStepV2Model) getKeysByCaseID(ctx context.Context, projectID, caseID string) []string {
	cs, err := m.FindByCaseID(ctx, projectID, caseID)
	if err != nil {
		return make([]string, 0)
	}

	keys := make([]string, 0, len(cs)*2)
	for _, c := range cs {
		keys = append(
			keys,
			fmt.Sprintf("%s%v", cacheManagerPerfCaseStepV2IdPrefix, c.Id),
			fmt.Sprintf(
				"%s%v:%v:%v", cacheManagerPerfCaseStepV2ProjectIdCaseIdStepIdPrefix, c.ProjectId, c.CaseId, c.StepId,
			),
		)
	}

	return keys
}
