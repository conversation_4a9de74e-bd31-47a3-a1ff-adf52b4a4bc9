package model

import (
	"context"

	"github.com/Masterminds/squirrel"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/types"
)

var (
	_ PerfLarkMemberModel = (*customPerfLarkMemberModel)(nil)

	perfLarkMemberInsertFields = stringx.Remove(perfLarkMemberFieldNames, "`id`", "`create_time`", "`update_time`", "`deleted_by`", "`created_at`", "`updated_at`", "`deleted_at`")
)

type (
	// PerfLarkMemberModel is an interface to be customized, add more methods here,
	// and implement the added methods in customPerfLarkMemberModel.
	PerfLarkMemberModel interface {
		perfLarkMemberModel
		types.DBModel

		Trans(ctx context.Context, fn func(context context.Context, session sqlx.Session) error) error
		InsertBuilder(data *PerfLarkMember) squirrel.InsertBuilder
		UpdateBuilder(data *PerfLarkMember) squirrel.UpdateBuilder
		SelectBuilder() squirrel.SelectBuilder
		SelectCountBuilder() squirrel.SelectBuilder
		FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error)
		FindNoCacheByQuery(ctx context.Context, selectBuilder squirrel.SelectBuilder) ([]*PerfLarkMember, error)
	}

	customPerfLarkMemberModel struct {
		*defaultPerfLarkMemberModel

		conn sqlx.SqlConn
	}
)

// NewPerfLarkMemberModel returns a model for the database table.
func NewPerfLarkMemberModel(conn sqlx.SqlConn, c cache.CacheConf, opts ...cache.Option) PerfLarkMemberModel {
	return &customPerfLarkMemberModel{
		defaultPerfLarkMemberModel: newPerfLarkMemberModel(conn, c, opts...),
		conn:                       conn,
	}
}

func (m *customPerfLarkMemberModel) Table() string {
	return m.table
}

func (m *customPerfLarkMemberModel) Fields() []string {
	return perfLarkMemberFieldNames
}

func (m *customPerfLarkMemberModel) Trans(ctx context.Context, fn func(context context.Context, session sqlx.Session) error) error {
	return m.TransactCtx(ctx, fn)
}

func (m *customPerfLarkMemberModel) InsertBuilder(data *PerfLarkMember) squirrel.InsertBuilder {
	// TODO: fill the insert values
	return squirrel.Insert(m.table).Columns(perfLarkMemberInsertFields...).Values()
}

func (m *customPerfLarkMemberModel) UpdateBuilder(data *PerfLarkMember) squirrel.UpdateBuilder {
	eq := squirrel.Eq{
		// TODO: fill the update values
	}
	return squirrel.Update(m.table).SetMap(eq).Where("`id` = ?", data.Id)
}

func (m *customPerfLarkMemberModel) SelectBuilder() squirrel.SelectBuilder {
	return squirrel.Select(perfLarkMemberFieldNames...).Where("`deleted` = ?", constants.NotDeleted).From(m.table)
}

func (m *customPerfLarkMemberModel) SelectCountBuilder() squirrel.SelectBuilder {
	return squirrel.Select("count(*)").Where("`deleted` = ?", constants.NotDeleted).From(m.table)
}

func (m *customPerfLarkMemberModel) FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error) {
	query, values, err := countBuilder.ToSql()
	if err != nil {
		return 0, err
	}

	var resp int64
	err = m.QueryRowNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return 0, err
	}
}

func (m *customPerfLarkMemberModel) FindNoCacheByQuery(ctx context.Context, selectBuilder squirrel.SelectBuilder) ([]*PerfLarkMember, error) {
	query, values, err := selectBuilder.ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*PerfLarkMember
	err = m.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}
