package model

import (
	"context"
	"database/sql"
	"fmt"
	"time"

	"github.com/Masterminds/squirrel"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/types"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/userinfo"
)

var (
	_ GeneralConfigurationModel = (*customGeneralConfigurationModel)(nil)

	generalConfigurationInsertFields = stringx.Remove(
		generalConfigurationFieldNames, "`id`", "`create_time`", "`update_time`", "`deleted_by`", "`created_at`",
		"`updated_at`", "`deleted_at`",
	)
)

type (
	// GeneralConfigurationModel is an interface to be customized, add more methods here,
	// and implement the added methods in customGeneralConfigurationModel.
	GeneralConfigurationModel interface {
		generalConfigurationModel
		types.DBModel

		Trans(ctx context.Context, fn func(context context.Context, session sqlx.Session) error) error
		InsertBuilder(data *GeneralConfiguration) squirrel.InsertBuilder
		UpdateBuilder(data *GeneralConfiguration) squirrel.UpdateBuilder
		SelectBuilder() squirrel.SelectBuilder
		SelectCountBuilder() squirrel.SelectBuilder
		FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error)
		FindNoCacheByQuery(ctx context.Context, builder squirrel.SelectBuilder) ([]*GeneralConfiguration, error)

		RemoveByConfigId(ctx context.Context, session sqlx.Session, projectId, configId string) (sql.Result, error)
	}

	customGeneralConfigurationModel struct {
		*defaultGeneralConfigurationModel
	}
)

// NewGeneralConfigurationModel returns a model for the database table.
func NewGeneralConfigurationModel(conn sqlx.SqlConn, c cache.CacheConf) GeneralConfigurationModel {
	return &customGeneralConfigurationModel{
		defaultGeneralConfigurationModel: newGeneralConfigurationModel(conn, c),
	}
}

func (m *customGeneralConfigurationModel) Table() string {
	return m.table
}

func (m *customGeneralConfigurationModel) Fields() []string {
	return generalConfigurationFieldNames
}

func (m *customGeneralConfigurationModel) Trans(
	ctx context.Context, fn func(context context.Context, session sqlx.Session) error,
) error {
	return m.TransactCtx(ctx, fn)
}

func (m *customGeneralConfigurationModel) InsertBuilder(data *GeneralConfiguration) squirrel.InsertBuilder {
	return squirrel.Insert(m.table).Columns(generalConfigurationInsertFields...).Values(
		data.ProjectId, data.ConfigId, data.Type, data.Name, data.Description, data.BaseUrl, data.Verify,
		data.Variables, data.Deleted, data.CreatedBy, data.UpdatedBy,
	)
}

func (m *customGeneralConfigurationModel) UpdateBuilder(data *GeneralConfiguration) squirrel.UpdateBuilder {
	eq := squirrel.Eq{
		"`name`":        data.Name,
		"`description`": data.Description,
		"`base_url`":    data.BaseUrl,
		"`verify`":      data.Verify,
		"`variables`":   data.Variables,
		"`deleted`":     data.Deleted,
		"`updated_by`":  data.UpdatedBy,
		"`deleted_by`":  data.DeletedBy,
		"`deleted_at`":  data.DeletedAt,
	}
	return squirrel.Update(m.table).SetMap(eq).Where("`id` = ?", data.Id)
}

func (m *customGeneralConfigurationModel) SelectBuilder() squirrel.SelectBuilder {
	return squirrel.Select(generalConfigurationFieldNames...).Where("`deleted` = ?", constants.NotDeleted).From(m.table)
}

func (m *customGeneralConfigurationModel) SelectCountBuilder() squirrel.SelectBuilder {
	return squirrel.Select("count(*)").Where("`deleted` = ?", constants.NotDeleted).From(m.table)
}

func (m *customGeneralConfigurationModel) FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (
	int64, error,
) {
	query, values, err := countBuilder.ToSql()
	if err != nil {
		return 0, err
	}

	var resp int64
	err = m.QueryRowNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return 0, err
	}
}

func (m *customGeneralConfigurationModel) FindNoCacheByQuery(
	ctx context.Context, builder squirrel.SelectBuilder,
) ([]*GeneralConfiguration, error) {
	query, values, err := builder.ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*GeneralConfiguration
	err = m.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

func (m *customGeneralConfigurationModel) RemoveByConfigId(
	ctx context.Context, session sqlx.Session, projectId, configId string,
) (sql.Result, error) {
	keys := m.getKeysByConfigId(ctx, projectId, configId)

	return m.ExecCtx(
		ctx, func(ctx context.Context, conn sqlx.SqlConn) (sql.Result, error) {
			/*
				SQL:
				UPDATE `general_configuration`
				SET `deleted` = ?, `deleted_by` = ?, `deleted_at` = ?
				WHERE `project_id` = ?
				  AND `config_id` = ?
			*/
			stmt, values, err := squirrel.Update(m.table).
				SetMap(
					squirrel.Eq{
						"`deleted`":    constants.HasDeleted,
						"`deleted_by`": userinfo.FromContext(ctx).Account,
						"`deleted_at`": time.Now(),
					},
				).
				Where("`project_id` = ? AND `config_id` = ?", projectId, configId).
				ToSql()
			if err != nil {
				return nil, err
			}

			if session != nil {
				return session.ExecCtx(ctx, stmt, values...)
			}
			return conn.ExecCtx(ctx, stmt, values...)
		}, keys...,
	)
}

func (m *customGeneralConfigurationModel) getKeysByConfigId(ctx context.Context, projectId, configId string) []string {
	c, err := m.FindOneByProjectIdConfigId(ctx, projectId, configId)
	if err != nil {
		return make([]string, 0)
	}

	return []string{
		fmt.Sprintf("%s%v", cacheManagerGeneralConfigurationIdPrefix, c.Id),
		fmt.Sprintf("%s%v:%v", cacheManagerGeneralConfigurationProjectIdConfigIdPrefix, c.ProjectId, c.ConfigId),
	}
}
