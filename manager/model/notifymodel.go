package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/Masterminds/squirrel"
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sqlbuilder"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/types"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/userinfo"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/utils"
)

var (
	_ NotifyModel = (*customNotifyModel)(nil)

	notifyInsertFields = stringx.Remove(
		notifyFieldNames, "`id`", "`create_time`", "`update_time`", "`deleted_by`", "`created_at`", "`updated_at`",
		"`deleted_at`",
	)
)

type (
	// NotifyModel is an interface to be customized, add more methods here,
	// and implement the added methods in customNotifyModel.
	NotifyModel interface {
		notifyModel
		types.DBModel

		Trans(ctx context.Context, fn func(context context.Context, session sqlx.Session) error) error
		InsertBuilder(data *Notify) squirrel.InsertBuilder
		UpdateBuilder(data *Notify) squirrel.UpdateBuilder
		SelectBuilder() squirrel.SelectBuilder
		SelectCountBuilder() squirrel.SelectBuilder
		FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error)
		FindNoCacheByQuery(ctx context.Context, selectBuilder squirrel.SelectBuilder) ([]*Notify, error)

		BulkInsert(req CreateNotifyReq) ([]*NotifyId, error)
		BatchInsert(ctx context.Context, session sqlx.Session, items []*Notify) (sql.Result, error)
		SearchNotifySqlBuilder(req SearchNotifyReq) (searchNotifyBuilder, searchNotifyCountBuilder)
		FindCountNotify(ctx context.Context, countBuilder searchNotifyCountBuilder) (int64, error)
		FindNotify(ctx context.Context, selectBuilder searchNotifyBuilder) ([]*Notify, error)
		FindByPlanID(ctx context.Context, projectID, planID string) ([]*Notify, error)
		RemoveByNotifyId(ctx context.Context, session sqlx.Session, projectId, planId, notifyId string) (
			sql.Result, error,
		)
		RemoveByPlanId(ctx context.Context, session sqlx.Session, projectId, planId string) (sql.Result, error)
		RemoveByPlanIdPrefix(ctx context.Context, session sqlx.Session, projectId, planIdPrefix, receiver string) (
			sql.Result, error,
		)
	}

	customNotifyModel struct {
		*defaultNotifyModel

		conn sqlx.SqlConn
		bulk *sqlx.BulkInserter
	}

	NotifyId int64
)

// NewNotifyModel returns a model for the database table.
func NewNotifyModel(conn sqlx.SqlConn, c cache.CacheConf) NotifyModel {
	model := newNotifyModel(conn, c)
	fields := strings.Join(
		[]string{
			"`project_id`",
			"`plan_id`",
			"`notify_id`",
			"`notify_mode`",
			"`notify_type`",
			"`receiver_name`",
			"`receiver`",
			"`created_by`",
			"`updated_by`",
		}, ",",
	)
	insertSQL := fmt.Sprintf("INSERT INTO %s (%s) VALUES (?,?,?,?,?,?,?,?,?)", model.tableName(), fields)
	bulkInserter, err := sqlx.NewBulkInserter(conn, insertSQL)
	if err != nil {
		logx.Error(fmt.Sprintf("Init %s bulkInsert failed", model.tableName()))
		panic(fmt.Sprintf("Init %s bulkInsert failed", model.tableName()))
	}

	return &customNotifyModel{
		defaultNotifyModel: model,

		conn: conn,
		bulk: bulkInserter,
	}
}

func (m *customNotifyModel) Table() string {
	return m.table
}

func (m *customNotifyModel) Fields() []string {
	return notifyFieldNames
}

func (m *customNotifyModel) Trans(
	ctx context.Context, fn func(context context.Context, session sqlx.Session) error,
) error {
	return m.TransactCtx(ctx, fn)
}

func (m *customNotifyModel) InsertBuilder(data *Notify) squirrel.InsertBuilder {
	return squirrel.Insert(m.table).Columns(notifyInsertFields...).Values(
		data.ProjectId, data.PlanId, data.NotifyMode, data.NotifyType, data.ReceiverName, data.Receiver,
	)
}

func (m *customNotifyModel) UpdateBuilder(data *Notify) squirrel.UpdateBuilder {
	eq := squirrel.Eq{
		"`notify_mode`":   data.NotifyMode,
		"`notify_type`":   data.NotifyType,
		"`receiver_name`": data.ReceiverName,
		"`receiver`":      data.Receiver,
	}
	return squirrel.Update(m.table).SetMap(eq).Where("`id` = ?", data.Id)
}

func (m *customNotifyModel) SelectBuilder() squirrel.SelectBuilder {
	return squirrel.Select(notifyFieldNames...).Where("`deleted` = ?", constants.NotDeleted).From(m.table)
}

func (m *customNotifyModel) SelectCountBuilder() squirrel.SelectBuilder {
	return squirrel.Select("count(*)").Where("`deleted` = ?", constants.NotDeleted).From(m.table)
}

func (m *customNotifyModel) FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error) {
	query, values, err := countBuilder.ToSql()
	if err != nil {
		return 0, err
	}

	var resp int64
	err = m.QueryRowNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return 0, err
	}
}

func (m *customNotifyModel) FindNoCacheByQuery(ctx context.Context, selectBuilder squirrel.SelectBuilder) (
	[]*Notify, error,
) {
	query, values, err := selectBuilder.ToSql()
	if err != nil {
		return nil, err
	}
	var resp []*Notify
	err = m.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

func (m *customNotifyModel) BulkInsert(req CreateNotifyReq) ([]*NotifyId, error) {
	var (
		resp []*NotifyId
		err  error
	)

	for _, v := range req.ReceiverInfos {
		if err := m.bulk.Insert(
			req.ProjectId, req.PlanId, v.NotifyId, req.NotifyMode, req.NotifyType, v.ReceiverName, v.Receiver,
			req.CreatedBy, req.UpdatedBy,
		); err != nil {
			return nil, err
		}
	}
	m.bulk.Flush()
	return resp, err
}

func (m *customNotifyModel) BatchInsert(ctx context.Context, session sqlx.Session, items []*Notify) (
	sql.Result, error,
) {
	count := len(items)
	if count == 0 {
		return nil, nil
	} else if count == 1 {
		return m.Insert(ctx, session, items[0])
	}

	builder := squirrel.Insert(m.table).Columns(notifyInsertFields...)
	for _, item := range items {
		builder = builder.Values(
			item.ProjectId, item.PlanId, item.NotifyId, item.NotifyMode, item.NotifyType, item.ReceiverName,
			item.Receiver, item.Deleted, item.CreatedBy, item.UpdatedBy,
		)
	}

	return m.ExecCtx(
		ctx, func(ctx context.Context, conn sqlx.SqlConn) (sql.Result, error) {
			query, values, err := builder.ToSql()
			if err != nil {
				return nil, err
			}
			if session != nil {
				return session.ExecCtx(ctx, query, values...)
			}
			return conn.ExecCtx(ctx, query, values...)
		},
	)
}

type searchNotifyBuilder struct {
	squirrel.SelectBuilder
}

type searchNotifyCountBuilder struct {
	squirrel.SelectBuilder
}

func (m *customNotifyModel) SearchNotifySqlBuilder(req SearchNotifyReq) (
	searchNotifyBuilder, searchNotifyCountBuilder,
) {
	/*
			SQL:
			SELECT `id`,
		       `project_id`,
		       `plan_id`,
		       `notify_id`,
		       `notify_mode`,
		       `notify_type`,
		       `receiver_name`,
		       `receiver`,
		       `deleted`,
		       `created_by`,
		       `updated_by`,
		       `deleted_by`,
		       `created_at`,
		       `updated_at`,
		       `deleted_at`
			FROM `notify`
			WHERE `project_id` = ?
			  AND `plan_id` = ?
			  AND `deleted` = ?
	*/
	fields := []string{
		"`id`",
		"`project_id`",
		"`plan_id`",
		"`notify_id`",
		"`notify_mode`",
		"`notify_type`",
		"`receiver_name`",
		"`receiver`",
		"`deleted`",
		"`created_by`",
		"`updated_by`",
		"`deleted_by`",
		"`created_at`",
		"`updated_at`",
		"`deleted_at`",
	}
	var sb, scb squirrel.SelectBuilder
	sb = squirrel.Select(fields...).
		From(notifyTableName).
		Where("`project_id` = ? AND `plan_id` = ? AND `deleted` = ?", req.ProjectId, req.PlanId, constants.NotDeleted)

	sb = sqlbuilder.SearchOptions(sb, sqlbuilder.WithCondition(m, req.Condition))

	scb = squirrel.Select("COUNT(*)").FromSelect(sb, "t")
	sb = sqlbuilder.SearchOptions(
		sb, sqlbuilder.WithPagination(VirtualAdvancedSearchSuiteNotInApiPlanModel, req.Pagination),
		sqlbuilder.WithSort(VirtualAdvancedSearchSuiteNotInApiPlanModel, req.Sort),
	)
	return searchNotifyBuilder{SelectBuilder: sb}, searchNotifyCountBuilder{SelectBuilder: scb}
}

func (m *customNotifyModel) FindCountNotify(ctx context.Context, countBuilder searchNotifyCountBuilder) (int64, error) {
	return m.FindCount(ctx, countBuilder.SelectBuilder)
}

func (m *customNotifyModel) FindNotify(ctx context.Context, selectBuilder searchNotifyBuilder) ([]*Notify, error) {
	var resp []*Notify

	err := utils.FindRows(
		ctx, m.conn, selectBuilder.SelectBuilder, func() any {
			return &resp
		},
	)

	return resp, err
}

func (m *customNotifyModel) FindByPlanID(ctx context.Context, projectID, planID string) ([]*Notify, error) {
	return m.FindNoCacheByQuery(ctx, m.SelectBuilder().Where("`project_id` = ? AND `plan_id` = ?", projectID, planID))
}

func (m *customNotifyModel) RemoveByNotifyId(
	ctx context.Context, session sqlx.Session, projectId, planId, notifyId string,
) (sql.Result, error) {
	keys := m.getKeysByPlanId(ctx, projectId, planId)

	return m.ExecCtx(
		ctx, func(ctx context.Context, conn sqlx.SqlConn) (sql.Result, error) {
			/*
				SQL:
				UPDATE `notify`
				SET `deleted`    = ?,
					`deleted_by` = ?,
					`deleted_at` = ?
				WHERE `project_id` = ?
				  AND `plan_id` = ?
				  AND `notify_id` = ?
			*/

			ub := squirrel.Update(m.table).
				SetMap(
					squirrel.Eq{
						"`deleted`":    constants.HasDeleted,
						"`deleted_by`": userinfo.FromContext(ctx).Account,
						"`deleted_at`": time.Now(),
					},
				).
				Where("`project_id` = ? AND `plan_id` = ? AND `notify_id` = ?", projectId, planId, notifyId)

			stmt, values, err := ub.ToSql()
			if err != nil {
				return nil, err
			}

			if session != nil {
				return session.ExecCtx(ctx, stmt, values...)
			}
			return conn.ExecCtx(ctx, stmt, values...)
		}, keys...,
	)
}

// RemoveByPlanId 根据API计划ID删除通知
func (m *customNotifyModel) RemoveByPlanId(
	ctx context.Context, session sqlx.Session, projectId, planId string,
) (sql.Result, error) {
	keys := m.getKeysByPlanId(ctx, projectId, planId)

	return m.ExecCtx(
		ctx, func(ctx context.Context, conn sqlx.SqlConn) (sql.Result, error) {
			/*
				SQL:
				UPDATE `notify`
				SET `deleted`    = ?,
					`deleted_by` = ?,
					`deleted_at` = ?
				WHERE `project_id` = ?
				  AND `plan_id` = ?
			*/

			stmt, values, err := squirrel.Update(m.table).
				SetMap(
					squirrel.Eq{
						"`deleted`":    constants.HasDeleted,
						"`deleted_by`": userinfo.FromContext(ctx).Account,
						"`deleted_at`": time.Now(),
					},
				).
				Where("`project_id` = ? AND `plan_id` = ?", projectId, planId).
				ToSql()
			if err != nil {
				return nil, err
			}

			if session != nil {
				return session.ExecCtx(ctx, stmt, values...)
			}
			return conn.ExecCtx(ctx, stmt, values...)
		}, keys...,
	)
}

func (m *customNotifyModel) getKeysByPlanId(ctx context.Context, projectId, planId string) []string {
	sb, _ := m.SearchNotifySqlBuilder(
		SearchNotifyReq{
			ProjectId: projectId,
			PlanId:    planId,
		},
	)
	ns, err := m.FindNotify(ctx, sb)
	if err != nil {
		return make([]string, 0)
	}

	keys := make([]string, 0, len(ns))
	for _, n := range ns {
		keys = append(keys, fmt.Sprintf("%s%v", cacheManagerNotifyProjectIdNotifyIdPrefix, n.Id))
	}

	return keys
}

func (m *customNotifyModel) RemoveByPlanIdPrefix(ctx context.Context, session sqlx.Session, projectId, planIdPrefix, receiver string) (
	sql.Result, error,
) {
	keys := m.getKeysByPlanIdPrefix(ctx, projectId, planIdPrefix, receiver)

	return m.ExecCtx(
		ctx, func(ctx context.Context, conn sqlx.SqlConn) (sql.Result, error) {
			/*
				SQL:
				UPDATE `notify`
				SET `deleted`    = ?,
					`deleted_by` = ?,
					`deleted_at` = ?
				WHERE `project_id` = ?
				  AND `plan_id` like ?
				  AND `receiver` = ?
			*/

			stmt, values, err := squirrel.Update(m.table).
				SetMap(
					squirrel.Eq{
						"`deleted`":    constants.HasDeleted,
						"`deleted_by`": userinfo.FromContext(ctx).Account,
						"`deleted_at`": time.Now(),
					},
				).
				Where(
					"`project_id` = ? AND `plan_id` like ? AND `receiver` = ?",
					projectId, planIdPrefix+"%", receiver,
				).
				ToSql()
			if err != nil {
				return nil, err
			}

			if session != nil {
				return session.ExecCtx(ctx, stmt, values...)
			}
			return conn.ExecCtx(ctx, stmt, values...)
		}, keys...,
	)
}

func (m *customNotifyModel) getKeysByPlanIdPrefix(ctx context.Context, projectId, planIdPrefix, receiver string) []string {
	sb := m.SelectBuilder().Where(
		"`project_id` = ? AND `plan_id` like ? AND `receiver` = ?",
		projectId, planIdPrefix+"%", receiver,
	)
	ns, err := m.FindNoCacheByQuery(ctx, sb)
	if err != nil {
		return make([]string, 0)
	}

	keys := make([]string, 0, len(ns))
	for _, n := range ns {
		keys = append(keys, fmt.Sprintf("%s%v", cacheManagerNotifyProjectIdNotifyIdPrefix, n.Id))
	}

	return keys
}
