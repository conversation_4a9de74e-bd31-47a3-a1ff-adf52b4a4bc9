package model

import (
	"context"
	"database/sql"
	"fmt"
	"time"

	"github.com/Masterminds/squirrel"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/types"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/userinfo"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
)

var (
	_ InterfaceSchemaModel = (*customInterfaceSchemaModel)(nil)

	interfaceSchemaInsertFields = stringx.Remove(
		interfaceSchemaFieldNames, "`id`", "`create_time`", "`update_time`", "`deleted_by`", "`created_at`",
		"`updated_at`", "`deleted_at`",
	)

	cacheManagerInterfaceSchemaProjectIdFullNamePrefix = "cache:manager:interfaceSchema:projectId:fullName:"
)

type (
	// InterfaceSchemaModel is an interface to be customized, add more methods here,
	// and implement the added methods in customInterfaceSchemaModel.
	InterfaceSchemaModel interface {
		interfaceSchemaModel
		types.DBModel

		Trans(ctx context.Context, fn func(context context.Context, session sqlx.Session) error) error
		InsertBuilder(data *InterfaceSchema) squirrel.InsertBuilder
		UpdateBuilder(data *InterfaceSchema) squirrel.UpdateBuilder
		SelectBuilder() squirrel.SelectBuilder
		SelectCountBuilder() squirrel.SelectBuilder
		FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error)
		FindNoCacheByQuery(ctx context.Context, builder squirrel.SelectBuilder) ([]*InterfaceSchema, error)

		InsertTX(ctx context.Context, session sqlx.Session, data *InterfaceSchema) (sql.Result, error)
		UpdateTX(ctx context.Context, session sqlx.Session, data *InterfaceSchema) (sql.Result, error)
		FindAll(ctx context.Context, projectId string) ([]*InterfaceSchema, error)
		FindOneByProjectIdFullName(ctx context.Context, projectId, fullName string) (*InterfaceSchema, error)
		FindCountByCategoryId(ctx context.Context, cm CategoryModel, cond FindCountByCategoryCondition) (int64, error)
		RemoveBySchemaId(ctx context.Context, session sqlx.Session, projectId, schemaId string) (sql.Result, error)
	}

	customInterfaceSchemaModel struct {
		*defaultInterfaceSchemaModel
	}
)

// NewInterfaceSchemaModel returns a model for the database table.
func NewInterfaceSchemaModel(conn sqlx.SqlConn, c cache.CacheConf) InterfaceSchemaModel {
	return &customInterfaceSchemaModel{
		defaultInterfaceSchemaModel: newInterfaceSchemaModel(conn, c),
	}
}

func (m *customInterfaceSchemaModel) Table() string {
	return m.table
}

func (m *customInterfaceSchemaModel) Fields() []string {
	return interfaceSchemaFieldNames
}

func (m *customInterfaceSchemaModel) Trans(
	ctx context.Context, fn func(context context.Context, session sqlx.Session) error,
) error {
	return m.TransactCtx(ctx, fn)
}

func (m *customInterfaceSchemaModel) InsertBuilder(data *InterfaceSchema) squirrel.InsertBuilder {
	return squirrel.Insert(m.table).Columns(interfaceSchemaInsertFields...).Values(
		data.ProjectId, data.CategoryId, data.SchemaId, data.FullName, data.Name, data.Description, data.Mode,
		data.ImportType, data.Data, data.Deleted, data.CreatedBy, data.UpdatedBy,
	)
}

func (m *customInterfaceSchemaModel) UpdateBuilder(data *InterfaceSchema) squirrel.UpdateBuilder {
	eq := squirrel.Eq{
		"`category_id`": data.CategoryId,
		"`full_name`":   data.FullName,
		"`name`":        data.Name,
		"`description`": data.Description,
		"`data`":        data.Data,
		"`deleted`":     data.Deleted,
		"`updated_by`":  data.UpdatedBy,
		"`deleted_by`":  data.DeletedBy,
		"`deleted_at`":  data.DeletedAt,
	}
	return squirrel.Update(m.table).SetMap(eq).Where("`id` = ?", data.Id)
}

func (m *customInterfaceSchemaModel) SelectBuilder() squirrel.SelectBuilder {
	return squirrel.Select(interfaceSchemaFieldNames...).Where("`deleted` = ?", constants.NotDeleted).From(m.table)
}

func (m *customInterfaceSchemaModel) SelectCountBuilder() squirrel.SelectBuilder {
	return squirrel.Select("count(*)").Where("`deleted` = ?", constants.NotDeleted).From(m.table)
}

func (m *customInterfaceSchemaModel) FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (
	int64, error,
) {
	query, values, err := countBuilder.ToSql()
	if err != nil {
		return 0, err
	}

	var resp int64
	err = m.QueryRowNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return 0, err
	}
}

func (m *customInterfaceSchemaModel) FindNoCacheByQuery(
	ctx context.Context, builder squirrel.SelectBuilder,
) ([]*InterfaceSchema, error) {
	query, values, err := builder.ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*InterfaceSchema
	err = m.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

func (m *customInterfaceSchemaModel) InsertTX(
	ctx context.Context, session sqlx.Session, data *InterfaceSchema,
) (sql.Result, error) {
	managerInterfaceSchemaIdKey := fmt.Sprintf("%s%v", cacheManagerInterfaceSchemaIdPrefix, data.Id)
	managerInterfaceSchemaProjectIdSchemaIdKey := fmt.Sprintf(
		"%s%v:%v", cacheManagerInterfaceSchemaProjectIdSchemaIdPrefix, data.ProjectId, data.SchemaId,
	)
	managerInterfaceSchemaProjectIdFullNameKey := fmt.Sprintf(
		"%s%v:%v", cacheManagerInterfaceSchemaProjectIdFullNamePrefix, data.ProjectId, data.FullName,
	)

	return m.ExecCtx(
		ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
			query, values, err := m.InsertBuilder(data).ToSql()
			if err != nil {
				return nil, err
			}
			if session != nil {
				return session.ExecCtx(ctx, query, values...)
			}
			return conn.ExecCtx(ctx, query, values...)
		}, managerInterfaceSchemaIdKey, managerInterfaceSchemaProjectIdSchemaIdKey,
		managerInterfaceSchemaProjectIdFullNameKey,
	)
}

func (m *customInterfaceSchemaModel) UpdateTX(
	ctx context.Context, session sqlx.Session, newData *InterfaceSchema,
) (sql.Result, error) {
	data, err := m.FindOne(ctx, newData.Id)
	if err != nil {
		return nil, err
	}

	managerInterfaceSchemaIdKey := fmt.Sprintf("%s%v", cacheManagerInterfaceSchemaIdPrefix, data.Id)
	managerInterfaceSchemaProjectIdSchemaIdKey := fmt.Sprintf(
		"%s%v:%v", cacheManagerInterfaceSchemaProjectIdSchemaIdPrefix, data.ProjectId, data.SchemaId,
	)
	managerInterfaceSchemaProjectIdFullNameKey := fmt.Sprintf(
		"%s%v:%v", cacheManagerInterfaceSchemaProjectIdFullNamePrefix, data.ProjectId, data.FullName,
	)

	return m.ExecCtx(
		ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
			query, values, err := m.UpdateBuilder(newData).ToSql()
			if err != nil {
				return nil, err
			}
			if session != nil {
				return session.ExecCtx(ctx, query, values...)
			}
			return conn.ExecCtx(ctx, query, values...)
		}, managerInterfaceSchemaIdKey, managerInterfaceSchemaProjectIdSchemaIdKey,
		managerInterfaceSchemaProjectIdFullNameKey,
	)
}

func (m *customInterfaceSchemaModel) FindAll(ctx context.Context, projectId string) ([]*InterfaceSchema, error) {
	return m.FindNoCacheByQuery(ctx, m.SelectBuilder().Where("`project_id` = ?", projectId))
}

func (m *customInterfaceSchemaModel) FindOneByProjectIdFullName(
	ctx context.Context, projectId, fullName string,
) (*InterfaceSchema, error) {
	managerInterfaceSchemaProjectIdFullNameKey := fmt.Sprintf(
		"%s%v:%v", cacheManagerInterfaceSchemaProjectIdFullNamePrefix, projectId, fullName,
	)

	var resp InterfaceSchema
	err := m.QueryRowIndexCtx(
		ctx, &resp, managerInterfaceSchemaProjectIdFullNameKey, m.formatPrimary,
		func(ctx context.Context, conn sqlx.SqlConn, v any) (any, error) {
			query, values, err := m.SelectBuilder().Where(
				"`project_id` = ? and `full_name` = ?", projectId, fullName,
			).OrderBy("`updated_at` DESC").Limit(1).ToSql()
			if err != nil {
				return nil, err
			}

			if err = conn.QueryRowCtx(ctx, &resp, query, values...); err != nil {
				return nil, err
			}

			return resp.Id, nil
		}, m.queryPrimary,
	)
	switch err {
	case nil:
		return &resp, nil
	default:
		return nil, err
	}
}

func (m *customInterfaceSchemaModel) FindCountByCategoryId(
	ctx context.Context, cm CategoryModel, cond FindCountByCategoryCondition,
) (int64, error) {
	alias := "t"
	sb := squirrel.Select("COUNT(*)").
		From(fmt.Sprintf("%s AS %s", m.Table(), alias)).
		Where(
			fmt.Sprintf("%s.`project_id` = ? AND %s.`deleted` = ?", alias, alias), cond.ProjectId, constants.NotDeleted,
		)

	if cond.DrillDown {
		sub := cm.FindDescendantCategoriesSqlBuilder(
			GetCategoryTreeCondition{
				ProjectId:     cond.ProjectId,
				Type:          common.ConstCategoryTreeTypeInterfaceSchema,
				CategoryId:    cond.CategoryId,
				OnlyDirectory: true,
			},
		)

		sb = sb.JoinClause(
			sub.Prefix("LEFT JOIN (").Suffix(
				fmt.Sprintf(
					") AS t1 ON %s.`category_id` = t1.`category_id`", alias,
				),
			),
		).Where("t1.`category_id` IS NOT NULL")
	} else {
		sb = sb.Where(fmt.Sprintf("%s.`category_id` = ?", alias), cond.CategoryId)
	}

	return m.FindCount(ctx, sb)
}

func (m *customInterfaceSchemaModel) RemoveBySchemaId(
	ctx context.Context, session sqlx.Session, projectId, schemaId string,
) (sql.Result, error) {
	keys := m.getKeysBySchemaId(ctx, projectId, schemaId)

	return m.ExecCtx(
		ctx, func(ctx context.Context, conn sqlx.SqlConn) (sql.Result, error) {
			/*
				SQL:
				UPDATE `interface_schema`
				SET `deleted` = ?, `deleted_by` = ?, `deleted_at` = ?
				WHERE `project_id` = ?
				  AND `schema_id` = ?
			*/
			stmt, values, err := squirrel.Update(m.table).
				SetMap(
					squirrel.Eq{
						"`deleted`":    constants.HasDeleted,
						"`deleted_by`": userinfo.FromContext(ctx).Account,
						"`deleted_at`": time.Now(),
					},
				).
				Where("`project_id` = ? AND `schema_id` = ?", projectId, schemaId).
				ToSql()
			if err != nil {
				return nil, err
			}

			if session != nil {
				return session.ExecCtx(ctx, stmt, values...)
			}
			return conn.ExecCtx(ctx, stmt, values...)
		}, keys...,
	)
}

func (m *customInterfaceSchemaModel) getKeysBySchemaId(ctx context.Context, projectId, schemaId string) []string {
	sb := m.SelectBuilder().Where("`project_id` = ? AND `schema_id` = ?", projectId, schemaId)
	ds, err := m.FindNoCacheByQuery(ctx, sb)
	if err != nil {
		return make([]string, 0)
	}

	keys := make([]string, 0, len(ds)*3)
	for _, d := range ds {
		keys = append(
			keys,
			fmt.Sprintf("%s%v", cacheManagerInterfaceSchemaIdPrefix, d.Id),
			fmt.Sprintf("%s%v:%v", cacheManagerInterfaceSchemaProjectIdSchemaIdPrefix, d.ProjectId, d.SchemaId),
			fmt.Sprintf("%s%v:%v", cacheManagerInterfaceSchemaProjectIdFullNamePrefix, d.ProjectId, d.FullName),
		)
	}

	return keys
}
