package model

import (
	"context"
	"fmt"

	"github.com/zeromicro/go-zero/core/stores/sqlc"

	"github.com/Masterminds/squirrel"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/types"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/utils"
)

var (
	_ AdvancedSearchConditionModel = (*customAdvancedSearchConditionModel)(nil)

	advancedSearchConditionInsertFields = stringx.Remove(
		advancedSearchConditionFieldNames, "`id`", "`create_time`", "`update_time`", "`deleted_by`", "`created_at`",
		"`updated_at`", "`deleted_at`",
	)
)

type (
	// AdvancedSearchConditionModel is an interface to be customized, add more methods here,
	// and implement the added methods in customAdvancedSearchConditionModel.
	AdvancedSearchConditionModel interface {
		advancedSearchConditionModel
		types.DBModel

		Trans(ctx context.Context, fn func(context context.Context, session sqlx.Session) error) error
		InsertBuilder(data *AdvancedSearchCondition) squirrel.InsertBuilder
		UpdateBuilder(data *AdvancedSearchCondition) squirrel.UpdateBuilder
		SelectBuilder() squirrel.SelectBuilder
		SelectCountBuilder() squirrel.SelectBuilder
		FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error)
		FindNoCacheByQuery(ctx context.Context, selectBuilder squirrel.SelectBuilder) (
			[]*AdvancedSearchCondition, error,
		)
		FindSearchConditionByFieldId(
			ctx context.Context, req FindSearchConditionByFieldIdReq,
		) ([]*AdvancedSearchCondition, error)
	}

	customAdvancedSearchConditionModel struct {
		*defaultAdvancedSearchConditionModel
	}
)

// NewAdvancedSearchConditionModel returns a model for the database table.
func NewAdvancedSearchConditionModel(conn sqlx.SqlConn, c cache.CacheConf) AdvancedSearchConditionModel {
	return &customAdvancedSearchConditionModel{
		defaultAdvancedSearchConditionModel: newAdvancedSearchConditionModel(conn, c),
	}
}

func (m *customAdvancedSearchConditionModel) Table() string {
	return m.table
}

func (m *customAdvancedSearchConditionModel) Fields() []string {
	return advancedSearchConditionFieldNames
}

func (m *customAdvancedSearchConditionModel) Trans(
	ctx context.Context, fn func(context context.Context, session sqlx.Session) error,
) error {
	return m.TransactCtx(ctx, fn)
}

func (m *customAdvancedSearchConditionModel) InsertBuilder(data *AdvancedSearchCondition) squirrel.InsertBuilder {
	return squirrel.Insert(m.table).Columns(advancedSearchConditionInsertFields...).Values(data.FrontName, data.Compare)
}

func (m *customAdvancedSearchConditionModel) UpdateBuilder(data *AdvancedSearchCondition) squirrel.UpdateBuilder {
	eq := squirrel.Eq{
		"`front_name`": data.FrontName,
		"`compare`":    data.Compare,
		"`deleted`":    data.Deleted,
	}
	return squirrel.Update(m.table).SetMap(eq).Where("`id` = ?", data.Id)
}

func (m *customAdvancedSearchConditionModel) SelectBuilder() squirrel.SelectBuilder {
	return squirrel.Select(advancedSearchConditionFieldNames...).Where(
		"`deleted` = ?", constants.NotDeleted,
	).From(m.table)
}

func (m *customAdvancedSearchConditionModel) SelectCountBuilder() squirrel.SelectBuilder {
	return squirrel.Select("count(*)").Where("`deleted` = ?", constants.NotDeleted).From(m.table)
}

func (m *customAdvancedSearchConditionModel) FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (
	int64, error,
) {
	query, values, err := countBuilder.ToSql()
	if err != nil {
		return 0, err
	}

	var resp int64
	err = m.QueryRowNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return 0, err
	}
}

func (m *customAdvancedSearchConditionModel) FindNoCacheByQuery(
	ctx context.Context, selectBuilder squirrel.SelectBuilder,
) ([]*AdvancedSearchCondition, error) {
	query, values, err := selectBuilder.ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*AdvancedSearchCondition
	err = m.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

func (m *customAdvancedSearchConditionModel) FindSearchConditionByFieldId(
	ctx context.Context, req FindSearchConditionByFieldIdReq,
) ([]*AdvancedSearchCondition, error) {
	/*
		SQL:
		SELECT t1.`id`,
			   t1.`condition_id`,
			   t1.`front_name`,
			   t1.`compare`,
			   t1.`deleted`,
			   t1.`created_at`,
			   t1.`updated_at`
		FROM advanced_search_condition AS t1,
			 advanced_search_field_relationship AS t2
		WHERE t1.`deleted` = t2.`deleted`
		  AND t2.`field_id` = ?
		  AND t2.`project_id` = ?
		  AND t1.`condition_id` = t2.`condition_id`
		  AND t1.`deleted` = ?
	*/
	fields := []string{
		"`id`",
		"`condition_id`",
		"`front_name`",
		"`compare`",
		"`deleted`",
		"`created_at`",
		"`updated_at`",
	}
	query, values, err := squirrel.Select(utils.AddTableNameToFields("t1", fields)...).
		From(
			fmt.Sprintf(
				"%s AS t1, %s AS t2", advancedSearchConditionTableName, advancedSearchFieldRelationshipTableName,
			),
		).
		Where(
			"t1.`deleted` = t2.`deleted` AND t2.`field_id` = ? AND t2.`project_id` = ? AND t1.`condition_id` = t2.`condition_id` AND t1.`deleted` = ?",
			req.FieldId, req.ProjectId, constants.NotDeleted,
		).
		ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*AdvancedSearchCondition

	err = m.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}
