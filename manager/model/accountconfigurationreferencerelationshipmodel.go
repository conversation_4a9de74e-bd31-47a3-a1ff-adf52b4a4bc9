package model

import (
	"context"
	"database/sql"
	"fmt"
	"time"

	"github.com/Masterminds/squirrel"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/types"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/userinfo"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/utils"
)

var (
	_ AccountConfigurationReferenceRelationshipModel = (*customAccountConfigurationReferenceRelationshipModel)(nil)

	accountConfigurationReferenceRelationshipInsertFields = stringx.Remove(
		accountConfigurationReferenceRelationshipFieldNames, "`id`", "`create_time`", "`update_time`", "`deleted_by`",
		"`created_at`", "`updated_at`", "`deleted_at`",
	)
)

type (
	// AccountConfigurationReferenceRelationshipModel is an interface to be customized, add more methods here,
	// and implement the added methods in customAccountConfigurationReferenceRelationshipModel.
	AccountConfigurationReferenceRelationshipModel interface {
		accountConfigurationReferenceRelationshipModel
		types.DBModel

		Trans(ctx context.Context, fn func(context context.Context, session sqlx.Session) error) error
		InsertBuilder(data *AccountConfigurationReferenceRelationship) squirrel.InsertBuilder
		UpdateBuilder(data *AccountConfigurationReferenceRelationship) squirrel.UpdateBuilder
		SelectBuilder() squirrel.SelectBuilder
		SelectCountBuilder() squirrel.SelectBuilder
		FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error)
		FindNoCacheByQuery(
			ctx context.Context, builder squirrel.SelectBuilder,
		) ([]*AccountConfigurationReferenceRelationship, error)

		FindReferenceByReference(
			ctx context.Context, projectId, referenceType, referenceId string,
		) ([]*AccountConfigurationReferenceRelationship, error)
		FindReferenceByConfigId(
			ctx context.Context, acm AccountConfigurationModel, projectId, configId string,
		) ([]*AccountConfigurationReferenceRelationship, error)
		RemoveByReferenceId(
			ctx context.Context, session sqlx.Session, projectId, referenceType, referenceId string,
		) (sql.Result, error)
	}

	customAccountConfigurationReferenceRelationshipModel struct {
		*defaultAccountConfigurationReferenceRelationshipModel
	}
)

// NewAccountConfigurationReferenceRelationshipModel returns a model for the database table.
func NewAccountConfigurationReferenceRelationshipModel(
	conn sqlx.SqlConn, c cache.CacheConf,
) AccountConfigurationReferenceRelationshipModel {
	return &customAccountConfigurationReferenceRelationshipModel{
		defaultAccountConfigurationReferenceRelationshipModel: newAccountConfigurationReferenceRelationshipModel(
			conn, c,
		),
	}
}

func (m *customAccountConfigurationReferenceRelationshipModel) Table() string {
	return m.table
}

func (m *customAccountConfigurationReferenceRelationshipModel) Fields() []string {
	return accountConfigurationReferenceRelationshipFieldNames
}

func (m *customAccountConfigurationReferenceRelationshipModel) Trans(
	ctx context.Context, fn func(context context.Context, session sqlx.Session) error,
) error {
	return m.TransactCtx(ctx, fn)
}

func (m *customAccountConfigurationReferenceRelationshipModel) InsertBuilder(data *AccountConfigurationReferenceRelationship) squirrel.InsertBuilder {
	return squirrel.Insert(m.table).Columns(accountConfigurationReferenceRelationshipInsertFields...).Values(
		data.ProjectId, data.ReferenceType, data.ReferenceId, data.ConfigId, data.Deleted, data.CreatedBy,
		data.UpdatedBy,
	)
}

func (m *customAccountConfigurationReferenceRelationshipModel) UpdateBuilder(data *AccountConfigurationReferenceRelationship) squirrel.UpdateBuilder {
	eq := squirrel.Eq{
		"`deleted`":    data.Deleted,
		"`updated_by`": data.UpdatedBy,
		"`deleted_by`": data.DeletedBy,
		"`deleted_at`": data.DeletedAt,
	}
	return squirrel.Update(m.table).SetMap(eq).Where("`id` = ?", data.Id)
}

func (m *customAccountConfigurationReferenceRelationshipModel) SelectBuilder() squirrel.SelectBuilder {
	return squirrel.Select(accountConfigurationReferenceRelationshipFieldNames...).Where(
		"`deleted` = ?", constants.NotDeleted,
	).From(m.table)
}

func (m *customAccountConfigurationReferenceRelationshipModel) SelectCountBuilder() squirrel.SelectBuilder {
	return squirrel.Select("count(*)").Where("`deleted` = ?", constants.NotDeleted).From(m.table)
}

func (m *customAccountConfigurationReferenceRelationshipModel) FindCount(
	ctx context.Context, countBuilder squirrel.SelectBuilder,
) (int64, error) {
	query, values, err := countBuilder.ToSql()
	if err != nil {
		return 0, err
	}

	var resp int64
	err = m.QueryRowNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return 0, err
	}
}

func (m *customAccountConfigurationReferenceRelationshipModel) FindNoCacheByQuery(
	ctx context.Context, builder squirrel.SelectBuilder,
) ([]*AccountConfigurationReferenceRelationship, error) {
	query, values, err := builder.ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*AccountConfigurationReferenceRelationship
	err = m.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

func (m *customAccountConfigurationReferenceRelationshipModel) FindReferenceByReference(
	ctx context.Context, projectId, referenceType, referenceId string,
) ([]*AccountConfigurationReferenceRelationship, error) {
	sb := m.SelectBuilder().Where(
		"`project_id` = ? AND `reference_type` = ? AND `reference_id` = ?", projectId, referenceType, referenceId,
	)

	return m.FindNoCacheByQuery(ctx, sb)
}

func (m *customAccountConfigurationReferenceRelationshipModel) FindReferenceByConfigId(
	ctx context.Context, acm AccountConfigurationModel, projectId, configId string,
) ([]*AccountConfigurationReferenceRelationship, error) {
	/*
		SQL
		SELECT t1.*
		FROM `account_configuration_reference_relationship` AS t1, `account_configuration` AS t2
		WHERE t1.`project_id` = t2.`project_id`
		  AND t1.`config_id` = t2.`config_id`
		  AND t1.`project_id` = ?
		  AND t1.`config_id` = ?
		  AND t1.`deleted` = ?
		  AND t2.`deleted` = ?
	*/

	sb := squirrel.Select(utils.AddTableNameToFields("t1", accountConfigurationReferenceRelationshipFieldNames)...).
		From(fmt.Sprintf("%s AS t1, %s AS t2", m.table, acm.Table())).
		Where(
			"t1.`project_id` = t2.`project_id` AND t1.`config_id` = t2.`config_id` AND t1.`project_id` = ? AND t1.`config_id` = ? AND t1.`deleted` = ? AND t2.`deleted` = ?",
			projectId, configId, constants.NotDeleted, constants.NotDeleted,
		)

	return m.FindNoCacheByQuery(ctx, sb)
}

func (m *customAccountConfigurationReferenceRelationshipModel) RemoveByReferenceId(
	ctx context.Context, session sqlx.Session, projectId, referenceType, referenceId string,
) (sql.Result, error) {
	keys := m.getKeysByReferenceId(ctx, projectId, referenceType, referenceId)

	return m.ExecCtx(
		ctx, func(ctx context.Context, conn sqlx.SqlConn) (sql.Result, error) {
			/*
				SQL:
				UPDATE `account_configuration_reference_relationship`
				SET `deleted` = ?, `deleted_by` = ?, `deleted_at` = ?
				WHERE `project_id` = ?
				  AND `reference_type` = ?
				  AND `reference_id` = ?
			*/
			stmt, values, err := squirrel.Update(m.table).
				SetMap(
					squirrel.Eq{
						"`deleted`":    constants.HasDeleted,
						"`deleted_by`": userinfo.FromContext(ctx).Account,
						"`deleted_at`": time.Now(),
					},
				).
				Where(
					"`project_id` = ? AND `reference_type` = ? AND `reference_id` = ?",
					projectId, referenceType, referenceId,
				).
				ToSql()
			if err != nil {
				return nil, err
			}

			if session != nil {
				return session.ExecCtx(ctx, stmt, values...)
			}
			return conn.ExecCtx(ctx, stmt, values...)
		}, keys...,
	)
}

func (m *customAccountConfigurationReferenceRelationshipModel) getKeysByReferenceId(
	ctx context.Context, projectId, referenceType, referenceId string,
) []string {
	cs, err := m.FindReferenceByReference(ctx, projectId, referenceType, referenceId)
	if err != nil {
		return make([]string, 0)
	}

	keys := make([]string, 0, len(cs))
	for _, c := range cs {
		keys = append(keys, fmt.Sprintf("%s%v", cacheManagerAccountConfigurationReferenceRelationshipIdPrefix, c.Id))
	}

	return keys
}
