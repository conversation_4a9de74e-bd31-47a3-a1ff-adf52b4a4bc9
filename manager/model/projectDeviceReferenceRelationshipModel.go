package model

import (
	"context"
	"database/sql"
	"fmt"

	"github.com/Masterminds/squirrel"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/types"
)

var (
	_ ProjectDeviceReferenceRelationshipModel = (*customProjectDeviceReferenceRelationshipModel)(nil)

	projectDeviceReferenceRelationshipInsertFields = stringx.Remove(
		projectDeviceReferenceRelationshipFieldNames, "`id`", "`create_time`", "`update_time`", "`deleted_by`",
		"`created_at`", "`updated_at`", "`deleted_at`",
	)
)

type (
	// ProjectDeviceReferenceRelationshipModel is an interface to be customized, add more methods here,
	// and implement the added methods in customProjectDeviceReferenceRelationshipModel.
	ProjectDeviceReferenceRelationshipModel interface {
		projectDeviceReferenceRelationshipModel
		types.DBModel

		Trans(ctx context.Context, fn func(context context.Context, session sqlx.Session) error) error
		InsertBuilder(data *ProjectDeviceReferenceRelationship) squirrel.InsertBuilder
		UpdateBuilder(data *ProjectDeviceReferenceRelationship) squirrel.UpdateBuilder
		SelectBuilder() squirrel.SelectBuilder
		SelectCountBuilder() squirrel.SelectBuilder
		FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error)
		FindNoCacheByQuery(
			ctx context.Context, selectBuilder squirrel.SelectBuilder,
		) ([]*ProjectDeviceReferenceRelationship, error)

		FindByProjectID(ctx context.Context, projectID string) ([]*ProjectDeviceReferenceRelationship, error)
		FindByUDID(ctx context.Context, projectID, udid string) ([]*ProjectDeviceReferenceRelationship, error)
		FindByReference(
			ctx context.Context, projectID, referenceType, referenceID string,
		) ([]*ProjectDeviceReferenceRelationship, error)
		RemoveByUDID(ctx context.Context, session sqlx.Session, projectID, udid string) (sql.Result, error)
		RemoveByReference(
			ctx context.Context, session sqlx.Session, projectID, referenceType, referenceID string,
		) (sql.Result, error)
	}

	customProjectDeviceReferenceRelationshipModel struct {
		*defaultProjectDeviceReferenceRelationshipModel

		conn sqlx.SqlConn
	}
)

// NewProjectDeviceReferenceRelationshipModel returns a model for the database table.
func NewProjectDeviceReferenceRelationshipModel(
	conn sqlx.SqlConn, c cache.CacheConf, opts ...cache.Option,
) ProjectDeviceReferenceRelationshipModel {
	return &customProjectDeviceReferenceRelationshipModel{
		defaultProjectDeviceReferenceRelationshipModel: newProjectDeviceReferenceRelationshipModel(conn, c, opts...),
		conn: conn,
	}
}

func (m *customProjectDeviceReferenceRelationshipModel) Table() string {
	return m.table
}

func (m *customProjectDeviceReferenceRelationshipModel) Fields() []string {
	return projectDeviceReferenceRelationshipFieldNames
}

func (m *customProjectDeviceReferenceRelationshipModel) Trans(
	ctx context.Context, fn func(context context.Context, session sqlx.Session) error,
) error {
	return m.TransactCtx(ctx, fn)
}

func (m *customProjectDeviceReferenceRelationshipModel) InsertBuilder(data *ProjectDeviceReferenceRelationship) squirrel.InsertBuilder {
	return squirrel.Insert(m.table).Columns(projectDeviceReferenceRelationshipInsertFields...).Values(
		data.ProjectId, data.ReferenceType, data.ReferenceId, data.Udid, data.Deleted, data.CreatedBy, data.UpdatedBy,
	)
}

func (m *customProjectDeviceReferenceRelationshipModel) UpdateBuilder(data *ProjectDeviceReferenceRelationship) squirrel.UpdateBuilder {
	eq := squirrel.Eq{
		"`deleted`":    data.Deleted,
		"`updated_by`": data.UpdatedBy,
		"`deleted_by`": data.DeletedBy,
		"`deleted_at`": data.DeletedAt,
	}
	return squirrel.Update(m.table).SetMap(eq).Where("`id` = ?", data.Id)
}

func (m *customProjectDeviceReferenceRelationshipModel) SelectBuilder() squirrel.SelectBuilder {
	return squirrel.Select(projectDeviceReferenceRelationshipFieldNames...).Where(
		"`deleted` = ?", constants.NotDeleted,
	).From(m.table)
}

func (m *customProjectDeviceReferenceRelationshipModel) SelectCountBuilder() squirrel.SelectBuilder {
	return squirrel.Select("count(*)").Where("`deleted` = ?", constants.NotDeleted).From(m.table)
}

func (m *customProjectDeviceReferenceRelationshipModel) FindCount(
	ctx context.Context, countBuilder squirrel.SelectBuilder,
) (int64, error) {
	query, values, err := countBuilder.ToSql()
	if err != nil {
		return 0, err
	}

	var resp int64
	err = m.QueryRowNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return 0, err
	}
}

func (m *customProjectDeviceReferenceRelationshipModel) FindNoCacheByQuery(
	ctx context.Context, selectBuilder squirrel.SelectBuilder,
) ([]*ProjectDeviceReferenceRelationship, error) {
	query, values, err := selectBuilder.ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*ProjectDeviceReferenceRelationship
	err = m.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

func (m *customProjectDeviceReferenceRelationshipModel) FindByProjectID(
	ctx context.Context, projectID string,
) ([]*ProjectDeviceReferenceRelationship, error) {
	/*
		SQL:
		SELECT *
		FROM `project_device_reference_relationship`
		WHERE `project_id` = ?
		  AND `deleted` = ?
	*/

	return m.FindNoCacheByQuery(ctx, m.SelectBuilder().Where("`project_id` = ?", projectID))
}

func (m *customProjectDeviceReferenceRelationshipModel) FindByUDID(
	ctx context.Context, projectID, udid string,
) ([]*ProjectDeviceReferenceRelationship, error) {
	/*
		SQL:
		SELECT *
		FROM `project_device_reference_relationship`
		WHERE `project_id` = ?
		  AND `udid` = ?
		  AND `deleted` = ?
	*/

	return m.FindNoCacheByQuery(ctx, m.SelectBuilder().Where("`project_id` = ? AND `udid` = ?", projectID, udid))
}

func (m *customProjectDeviceReferenceRelationshipModel) FindByReference(
	ctx context.Context, projectID, referenceType, referenceID string,
) ([]*ProjectDeviceReferenceRelationship, error) {
	/*
		SQL:
		SELECT *
		FROM `project_device_reference_relationship`
		WHERE `project_id` = ?
		  AND `reference_type` = ?
		  AND `reference_id` = ?
		  AND `deleted` = ?
	*/

	return m.FindNoCacheByQuery(
		ctx, m.SelectBuilder().Where(
			"`project_id` = ? AND `reference_type` = ? AND `reference_id` = ?",
			projectID, referenceType, referenceID,
		),
	)
}

func (m *customProjectDeviceReferenceRelationshipModel) RemoveByUDID(
	ctx context.Context, session sqlx.Session, projectID, udid string,
) (sql.Result, error) {
	keys := m.getKeysByUDID(ctx, projectID, udid)

	return m.ExecCtx(
		ctx, func(ctx context.Context, conn sqlx.SqlConn) (sql.Result, error) {
			/*
				SQL:
				DELETE FROM `project_device_reference_relationship`
				WHERE `project_id` = ?
				  AND `udid` = ?
			*/

			stmt, values, err := squirrel.Delete(m.table).
				Where("`project_id` = ? AND `udid` = ?", projectID, udid).
				ToSql()
			if err != nil {
				return nil, err
			}

			if session != nil {
				return session.ExecCtx(ctx, stmt, values...)
			}
			return conn.ExecCtx(ctx, stmt, values...)
		}, keys...,
	)
}

func (m *customProjectDeviceReferenceRelationshipModel) getKeysByUDID(
	ctx context.Context, projectID, udid string,
) []string {
	cs, err := m.FindByUDID(ctx, projectID, udid)
	if err != nil {
		return make([]string, 0)
	}

	keys := make([]string, 0, len(cs))
	for _, c := range cs {
		keys = append(keys, fmt.Sprintf("%s%v", cacheManagerProjectDeviceReferenceRelationshipIdPrefix, c.Id))
	}

	return keys
}

func (m *customProjectDeviceReferenceRelationshipModel) RemoveByReference(
	ctx context.Context, session sqlx.Session, projectID, referenceType, referenceID string,
) (sql.Result, error) {
	keys := m.getKeysByReference(ctx, projectID, referenceType, referenceID)

	return m.ExecCtx(
		ctx, func(ctx context.Context, conn sqlx.SqlConn) (sql.Result, error) {
			/*
				SQL:
				DELETE FROM `project_device_reference_relationship`
				WHERE `project_id` = ?
				  AND `reference_type` = ?
				  AND `reference_id` = ?
			*/

			stmt, values, err := squirrel.Delete(m.table).
				Where(
					"`project_id` = ? AND `reference_type` = ? AND `reference_id` = ?",
					projectID, referenceType, referenceID,
				).
				ToSql()
			if err != nil {
				return nil, err
			}

			if session != nil {
				return session.ExecCtx(ctx, stmt, values...)
			}
			return conn.ExecCtx(ctx, stmt, values...)
		}, keys...,
	)
}

func (m *customProjectDeviceReferenceRelationshipModel) getKeysByReference(
	ctx context.Context, projectID, referenceType, referenceID string,
) []string {
	cs, err := m.FindByReference(ctx, projectID, referenceType, referenceID)
	if err != nil {
		return make([]string, 0)
	}

	keys := make([]string, 0, len(cs))
	for _, c := range cs {
		keys = append(keys, fmt.Sprintf("%s%v", cacheManagerProjectDeviceReferenceRelationshipIdPrefix, c.Id))
	}

	return keys
}
