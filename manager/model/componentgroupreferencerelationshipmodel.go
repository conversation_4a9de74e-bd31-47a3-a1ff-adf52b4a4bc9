package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/Masterminds/squirrel"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	qetconstants "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/types"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/userinfo"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/utils"
)

var (
	_ ComponentGroupReferenceRelationshipModel = (*customComponentGroupReferenceRelationshipModel)(nil)

	componentGroupReferenceRelationshipInsertFields = stringx.Remove(
		componentGroupReferenceRelationshipFieldNames, "`id`", "`create_time`", "`update_time`", "`deleted_by`",
		"`created_at`", "`updated_at`", "`deleted_at`",
	)
)

type (
	// ComponentGroupReferenceRelationshipModel is an interface to be customized, add more methods here,
	// and implement the added methods in customComponentGroupReferenceRelationshipModel.
	ComponentGroupReferenceRelationshipModel interface {
		componentGroupReferenceRelationshipModel
		types.DBModel

		Trans(ctx context.Context, fn func(context context.Context, session sqlx.Session) error) error
		InsertBuilder(data *ComponentGroupReferenceRelationship) squirrel.InsertBuilder
		UpdateBuilder(data *ComponentGroupReferenceRelationship) squirrel.UpdateBuilder
		SelectBuilder() squirrel.SelectBuilder
		SelectCountBuilder() squirrel.SelectBuilder
		FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error)
		FindNoCacheByQuery(ctx context.Context, builder squirrel.SelectBuilder) (
			[]*ComponentGroupReferenceRelationship, error,
		)

		FindLatestReferenceByComponentGroupId(
			ctx context.Context, cgm ComponentGroupModel, acm ApiCaseModel, icm InterfaceCaseModel,
			projectId, componentGroupId string,
		) ([]*ComponentGroupReferenceRelationship, error)
		RemoveByReferenceId(
			ctx context.Context, session sqlx.Session, projectId, referenceType, referenceId string,
		) (sql.Result, error)
	}

	customComponentGroupReferenceRelationshipModel struct {
		*defaultComponentGroupReferenceRelationshipModel
	}
)

// NewComponentGroupReferenceRelationshipModel returns a model for the database table.
func NewComponentGroupReferenceRelationshipModel(
	conn sqlx.SqlConn, c cache.CacheConf,
) ComponentGroupReferenceRelationshipModel {
	return &customComponentGroupReferenceRelationshipModel{
		defaultComponentGroupReferenceRelationshipModel: newComponentGroupReferenceRelationshipModel(conn, c),
	}
}

func (m *customComponentGroupReferenceRelationshipModel) Table() string {
	return m.table
}

func (m *customComponentGroupReferenceRelationshipModel) Fields() []string {
	return componentGroupReferenceRelationshipFieldNames
}

func (m *customComponentGroupReferenceRelationshipModel) Trans(
	ctx context.Context, fn func(context context.Context, session sqlx.Session) error,
) error {
	return m.TransactCtx(ctx, fn)
}

func (m *customComponentGroupReferenceRelationshipModel) InsertBuilder(data *ComponentGroupReferenceRelationship) squirrel.InsertBuilder {
	return squirrel.Insert(m.table).Columns(componentGroupReferenceRelationshipInsertFields...).Values(
		data.ProjectId, data.ReferenceType, data.ReferenceId, data.ReferenceVersion, data.ComponentGroupId,
		data.Deleted, data.CreatedBy, data.UpdatedBy,
	)
}

func (m *customComponentGroupReferenceRelationshipModel) UpdateBuilder(data *ComponentGroupReferenceRelationship) squirrel.UpdateBuilder {
	eq := squirrel.Eq{
		"`deleted`":    data.Deleted,
		"`updated_by`": data.UpdatedBy,
		"`deleted_by`": data.DeletedBy,
		"`deleted_at`": data.DeletedAt,
	}
	return squirrel.Update(m.table).SetMap(eq).Where("`id` = ?", data.Id)
}

func (m *customComponentGroupReferenceRelationshipModel) SelectBuilder() squirrel.SelectBuilder {
	return squirrel.Select(componentGroupReferenceRelationshipFieldNames...).Where(
		"`deleted` = ?", qetconstants.NotDeleted,
	).From(m.table)
}

func (m *customComponentGroupReferenceRelationshipModel) SelectCountBuilder() squirrel.SelectBuilder {
	return squirrel.Select("count(*)").Where("`deleted` = ?", qetconstants.NotDeleted).From(m.table)
}

func (m *customComponentGroupReferenceRelationshipModel) FindCount(
	ctx context.Context, countBuilder squirrel.SelectBuilder,
) (int64, error) {
	query, values, err := countBuilder.ToSql()
	if err != nil {
		return 0, err
	}

	var resp int64
	err = m.QueryRowNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return 0, err
	}
}

func (m *customComponentGroupReferenceRelationshipModel) FindNoCacheByQuery(
	ctx context.Context, builder squirrel.SelectBuilder,
) ([]*ComponentGroupReferenceRelationship, error) {
	query, values, err := builder.ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*ComponentGroupReferenceRelationship
	err = m.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

func (m *customComponentGroupReferenceRelationshipModel) FindLatestReferenceByComponentGroupId(
	ctx context.Context, cgm ComponentGroupModel, acm ApiCaseModel, icm InterfaceCaseModel,
	projectId, componentGroupId string,
) ([]*ComponentGroupReferenceRelationship, error) {
	/*
		SQL:
		SELECT t1.*
		FROM `component_group_reference_relationship` AS t1
			LEFT JOIN `component_group` AS t2
				ON t1.`project_id` = t2.`project_id`
				AND t1.`reference_type` = ?
				AND t1.`reference_id` = t2.`component_group_id`
				AND t1.`reference_version` = t2.`version`
				AND t2.`latest` = ?
				AND t2.`deleted` = ?
			LEFT JOIN `api_case` AS t3
				ON t1.`project_id` = t3.`project_id`
				AND t1.`reference_type` = ?
				AND t1.`reference_id` = t3.`case_id`
				AND t1.`reference_version` = t3.`version`
				AND t3.`latest` = ?
				AND t3.`deleted` = ?
			LEFT JOIN `interface_case` AS t4
			    ON t1.`project_id` = t4.`project_id`
			    AND t1.`reference_type` = ?
			    AND t1.`reference_id` = t4.`case_id`
			    AND t1.`reference_version` = t4.`version`
			    AND t4.`latest` = ?
			    AND t4.`deleted` = ?
		WHERE t1.`project_id` = ?
		  AND t1.`component_group_id` = ?
		  AND t1.`deleted` = ?
		  AND (t2.`component_group_id` IS NOT NULL OR t3.`case_id` IS NOT NULL OR t4.`case_id` IS NOT NULL)
	*/
	sb := squirrel.Select(
		strings.Join(
			utils.AddTableNameToFields("t1", componentGroupReferenceRelationshipFieldNames), ", ",
		),
	).From(
		fmt.Sprintf("%s AS t1", m.table),
	).LeftJoin(
		fmt.Sprintf(
			"%s AS t2 ON t1.`project_id` = t2.`project_id` AND t1.`reference_type` = ? AND t1.`reference_id` = t2.`component_group_id` AND t1.`reference_version` = t2.`version` AND t2.`latest` = ? AND t2.`deleted` = ?",
			cgm.Table(),
		), constants.ComponentGroup, qetconstants.IsLatestVersion, qetconstants.NotDeleted,
	).LeftJoin(
		fmt.Sprintf(
			"%s AS t3 ON t1.`project_id` = t3.`project_id` AND t1.`reference_type` = ? AND t1.`reference_id` = t3.`case_id` AND t1.`reference_version` = t3.`version` AND t3.`latest` = ? AND t3.`deleted` = ?",
			acm.Table(),
		), constants.ApiCase, qetconstants.IsLatestVersion, qetconstants.NotDeleted,
	).LeftJoin(
		fmt.Sprintf(
			"%s AS t4 ON t1.`project_id` = t4.`project_id` AND t1.`reference_type` = ? AND t1.`reference_id` = t4.`case_id` AND t1.`reference_version` = t4.`version` AND t4.`latest` = ? AND t4.`deleted` = ?",
			icm.Table(),
		), constants.InterfaceCase, qetconstants.IsLatestVersion, qetconstants.NotDeleted,
	).Where(
		"t1.`project_id` = ? AND t1.`component_group_id` = ? AND t1.`deleted` = ? AND (t2.`component_group_id` IS NOT NULL OR t3.`case_id` IS NOT NULL OR t4.`case_id` IS NOT NULL)",
		projectId, componentGroupId, qetconstants.NotDeleted,
	)

	return m.FindNoCacheByQuery(ctx, sb)
}

func (m *customComponentGroupReferenceRelationshipModel) RemoveByReferenceId(
	ctx context.Context, session sqlx.Session, projectId, referenceType, referenceId string,
) (sql.Result, error) {
	keys := m.getKeysByReferenceId(ctx, projectId, referenceType, referenceId)

	return m.ExecCtx(
		ctx, func(ctx context.Context, conn sqlx.SqlConn) (sql.Result, error) {
			/*
				SQL:
				UPDATE `component_group_reference_relationship`
				SET `deleted` = ?, `deleted_by` = ?, `deleted_at` = ?
				WHERE `project_id` = ?
				  AND `reference_type` = ?
				  AND `reference_id` = ?
			*/
			stmt, values, err := squirrel.Update(m.table).
				SetMap(
					squirrel.Eq{
						"`deleted`":    qetconstants.HasDeleted,
						"`deleted_by`": userinfo.FromContext(ctx).Account,
						"`deleted_at`": time.Now(),
					},
				).
				Where(
					"`project_id` = ? AND `reference_type` = ? AND `reference_id` = ?", projectId, referenceType,
					referenceId,
				).
				ToSql()
			if err != nil {
				return nil, err
			}

			if session != nil {
				return session.ExecCtx(ctx, stmt, values...)
			}
			return conn.ExecCtx(ctx, stmt, values...)
		}, keys...,
	)
}

func (m *customComponentGroupReferenceRelationshipModel) getKeysByReferenceId(
	ctx context.Context, projectId, referenceType, referenceId string,
) []string {
	sb := m.SelectBuilder().Where(
		"`project_id` = ? AND `reference_type` = ? AND `reference_id` = ?", projectId, referenceType, referenceId,
	)
	cs, err := m.FindNoCacheByQuery(ctx, sb)
	if err != nil {
		return make([]string, 0)
	}

	keys := make([]string, 0, len(cs))
	for _, c := range cs {
		keys = append(keys, fmt.Sprintf("%s%v", cacheManagerComponentGroupReferenceRelationshipIdPrefix, c.Id))
	}

	return keys
}
