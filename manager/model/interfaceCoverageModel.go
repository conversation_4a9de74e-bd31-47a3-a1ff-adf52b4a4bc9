package model

import (
	"context"
	"database/sql"
	"fmt"
	"time"

	"github.com/Masterminds/squirrel"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/types"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/utils"
)

var (
	_ InterfaceCoverageModel = (*customInterfaceCoverageModel)(nil)

	interfaceCoverageInsertFields = stringx.Remove(
		interfaceCoverageFieldNames, "`id`", "`create_time`", "`update_time`", "`deleted_by`", "`created_at`",
		"`updated_at`", "`deleted_at`",
	)
)

type (
	// InterfaceCoverageModel is an interface to be customized, add more methods here,
	// and implement the added methods in customInterfaceCoverageModel.
	InterfaceCoverageModel interface {
		interfaceCoverageModel
		types.DBModel

		Trans(ctx context.Context, fn func(context context.Context, session sqlx.Session) error) error
		InsertBuilder(data *InterfaceCoverage) squirrel.InsertBuilder
		UpdateBuilder(data *InterfaceCoverage) squirrel.UpdateBuilder
		SelectBuilder() squirrel.SelectBuilder
		SelectCountBuilder() squirrel.SelectBuilder
		FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error)
		FindNoCacheByQuery(ctx context.Context, selectBuilder squirrel.SelectBuilder) ([]*InterfaceCoverage, error)

		FindTeamByProjectID(ctx context.Context, projectID string) ([]string, error)
		FindCoverageData(ctx context.Context, projectID, team, from, to string) ([]*InterfaceCoverage, error)
		DeleteBeforeNDaysRecordsByProjectID(ctx context.Context, session sqlx.Session, projectID string, days int) error
	}

	customInterfaceCoverageModel struct {
		*defaultInterfaceCoverageModel

		conn sqlx.SqlConn
	}
)

// NewInterfaceCoverageModel returns a model for the database table.
func NewInterfaceCoverageModel(conn sqlx.SqlConn, c cache.CacheConf, opts ...cache.Option) InterfaceCoverageModel {
	return &customInterfaceCoverageModel{
		defaultInterfaceCoverageModel: newInterfaceCoverageModel(conn, c, opts...),
		conn:                          conn,
	}
}

func (m *customInterfaceCoverageModel) Table() string {
	return m.table
}

func (m *customInterfaceCoverageModel) Fields() []string {
	return interfaceCoverageFieldNames
}

func (m *customInterfaceCoverageModel) Trans(
	ctx context.Context, fn func(context context.Context, session sqlx.Session) error,
) error {
	return m.TransactCtx(ctx, fn)
}

func (m *customInterfaceCoverageModel) InsertBuilder(data *InterfaceCoverage) squirrel.InsertBuilder {
	return squirrel.Insert(m.table).Columns(interfaceCoverageInsertFields...).Values(
		data.ProjectId, data.Team, data.NumberOfApis, data.NumberOfCases, data.CountedAt, data.Deleted, data.CreatedBy,
		data.UpdatedBy,
	)
}

func (m *customInterfaceCoverageModel) UpdateBuilder(data *InterfaceCoverage) squirrel.UpdateBuilder {
	eq := squirrel.Eq{
		"`team`":            data.Team,
		"`number_of_apis`":  data.NumberOfApis,
		"`number_of_cases`": data.NumberOfCases,
		"`deleted`":         data.Deleted,
		"`updated_by`":      data.UpdatedBy,
		"`deleted_by`":      data.DeletedBy,
		"`deleted_at`":      data.DeletedAt,
	}
	return squirrel.Update(m.table).SetMap(eq).Where("`id` = ?", data.Id)
}

func (m *customInterfaceCoverageModel) SelectBuilder() squirrel.SelectBuilder {
	return squirrel.Select(interfaceCoverageFieldNames...).Where("`deleted` = ?", constants.NotDeleted).From(m.table)
}

func (m *customInterfaceCoverageModel) SelectCountBuilder() squirrel.SelectBuilder {
	return squirrel.Select("count(*)").Where("`deleted` = ?", constants.NotDeleted).From(m.table)
}

func (m *customInterfaceCoverageModel) FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (
	int64, error,
) {
	query, values, err := countBuilder.ToSql()
	if err != nil {
		return 0, err
	}

	var resp int64
	err = m.QueryRowNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return 0, err
	}
}

func (m *customInterfaceCoverageModel) FindNoCacheByQuery(
	ctx context.Context, selectBuilder squirrel.SelectBuilder,
) ([]*InterfaceCoverage, error) {
	query, values, err := selectBuilder.ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*InterfaceCoverage
	err = m.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

func (m *customInterfaceCoverageModel) FindTeamByProjectID(ctx context.Context, projectID string) ([]string, error) {
	sb := squirrel.Select("`team`").
		Distinct().
		From(m.table).
		Where("`project_id` = ? AND `deleted` = ?", projectID, constants.NotDeleted).
		OrderBy("`number_of_apis` DESC")

	var resp []string
	err := utils.FindRows(
		ctx, m.conn, sb, func() any {
			return &resp
		},
	)
	return resp, err
}

func (m *customInterfaceCoverageModel) FindCoverageData(
	ctx context.Context, projectID, team, from, to string,
) ([]*InterfaceCoverage, error) {
	return m.FindNoCacheByQuery(
		ctx,
		m.SelectBuilder().Where(
			"`project_id` = ? AND `team` = ? AND `counted_at` >= ? AND `counted_at` <= ?",
			projectID, team, from, to,
		),
	)
}

func (m *customInterfaceCoverageModel) DeleteBeforeNDaysRecordsByProjectID(
	ctx context.Context, session sqlx.Session, projectID string, days int,
) error {
	keys := m.getKeysOfBeforeNDaysRecordsByProjectID(ctx, projectID, days)

	_, err := m.ExecCtx(
		ctx, func(ctx context.Context, conn sqlx.SqlConn) (sql.Result, error) {
			stmt, values, err := squirrel.Delete(m.table).
				Where("`project_id` = ? AND `updated_at` < ?", projectID, time.Now().AddDate(0, 0, -days)).
				ToSql()
			if err != nil {
				return nil, err
			}

			if session != nil {
				return session.ExecCtx(ctx, stmt, values...)
			}

			return m.conn.ExecCtx(ctx, stmt, values...)
		}, keys...,
	)
	return err
}

func (m *customInterfaceCoverageModel) getKeysOfBeforeNDaysRecordsByProjectID(
	ctx context.Context, projectID string, days int,
) []string {
	rs, err := m.FindNoCacheByQuery(
		ctx,
		m.SelectBuilder().Where("`project_id` = ? AND `updated_at` < ?", projectID, time.Now().AddDate(0, 0, -days)),
	)
	if err != nil {
		return make([]string, 0)
	}

	keys := make([]string, 0, len(rs)*2)
	for _, r := range rs {
		keys = append(
			keys,
			fmt.Sprintf("%s%v", cacheManagerInterfaceCoverageIdPrefix, r.Id),
			fmt.Sprintf(
				"%s%v:%v:%v",
				cacheManagerInterfaceCoverageProjectIdTeamCountedAtPrefix, r.ProjectId, r.Team, r.CountedAt,
			),
		)
	}

	return keys
}
