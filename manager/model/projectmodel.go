package model

import (
	"context"

	"github.com/Masterminds/squirrel"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/types"
)

var (
	_ ProjectModel = (*customProjectModel)(nil)

	projectInsertFields = stringx.Remove(
		projectFieldNames, "`id`", "`create_time`", "`update_time`", "`deleted_by`", "`created_at`", "`updated_at`",
		"`deleted_at`",
	)
)

type (
	// ProjectModel is an interface to be customized, add more methods here,
	// and implement the added methods in customProjectModel.
	ProjectModel interface {
		projectModel
		types.DBModel

		Trans(ctx context.Context, fn func(context context.Context, session sqlx.Session) error) error
		InsertBuilder(data *Project) squirrel.InsertBuilder
		UpdateBuilder(data *Project) squirrel.UpdateBuilder
		SelectBuilder() squirrel.SelectBuilder
		SelectCountBuilder() squirrel.SelectBuilder
		FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error)
		FindNoCacheByQuery(ctx context.Context, builder squirrel.SelectBuilder) ([]*Project, error)

		FindAll(ctx context.Context) ([]*Project, error)
	}

	customProjectModel struct {
		*defaultProjectModel
	}
)

// NewProjectModel returns a model for the database table.
func NewProjectModel(conn sqlx.SqlConn, c cache.CacheConf) ProjectModel {
	return &customProjectModel{
		defaultProjectModel: newProjectModel(conn, c),
	}
}

func (m *customProjectModel) Table() string {
	return m.table
}

func (m *customProjectModel) Fields() []string {
	return projectFieldNames
}

func (m *customProjectModel) Trans(
	ctx context.Context, fn func(context context.Context, session sqlx.Session) error,
) error {
	return m.TransactCtx(ctx, fn)
}

func (m *customProjectModel) InsertBuilder(data *Project) squirrel.InsertBuilder {
	return squirrel.Insert(m.table).Columns(projectInsertFields...).Values(
		data.ProjectId, data.Name, data.Description, data.ReviewEnabled, data.CoverageEnabled, data.CoverageLarkChats,
		data.Deleted, data.CreatedBy, data.UpdatedBy,
	)
}

func (m *customProjectModel) UpdateBuilder(data *Project) squirrel.UpdateBuilder {
	eq := squirrel.Eq{
		"`name`":                data.Name,
		"`description`":         data.Description,
		"`review_enabled`":      data.ReviewEnabled,
		"`coverage_enabled`":    data.CoverageEnabled,
		"`coverage_lark_chats`": data.CoverageLarkChats,
		"`deleted`":             data.Deleted,
		"`updated_by`":          data.UpdatedBy,
		"`deleted_by`":          data.DeletedBy,
		"`deleted_at`":          data.DeletedAt,
	}
	return squirrel.Update(m.table).SetMap(eq).Where("`id` = ?", data.Id)
}

func (m *customProjectModel) SelectBuilder() squirrel.SelectBuilder {
	return squirrel.Select(projectFieldNames...).Where("`deleted` = ?", constants.NotDeleted).From(m.table)
}

func (m *customProjectModel) SelectCountBuilder() squirrel.SelectBuilder {
	return squirrel.Select("count(*)").Where("`deleted` = ?", constants.NotDeleted).From(m.table)
}

func (m *customProjectModel) FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error) {
	query, values, err := countBuilder.ToSql()
	if err != nil {
		return 0, err
	}

	var resp int64
	err = m.QueryRowNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return 0, err
	}
}

func (m *customProjectModel) FindNoCacheByQuery(ctx context.Context, builder squirrel.SelectBuilder) (
	[]*Project, error,
) {
	query, values, err := builder.ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*Project
	err = m.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

func (m *customProjectModel) FindAll(ctx context.Context) ([]*Project, error) {
	return m.FindNoCacheByQuery(ctx, m.SelectBuilder())
}
