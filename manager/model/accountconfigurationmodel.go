package model

import (
	"context"
	"database/sql"
	"fmt"
	"time"

	"github.com/Masterminds/squirrel"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/types"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/userinfo"
)

var (
	_ AccountConfigurationModel = (*customAccountConfigurationModel)(nil)

	accountConfigurationInsertFields = stringx.Remove(
		accountConfigurationFieldNames, "`id`", "`create_time`", "`update_time`", "`deleted_by`", "`created_at`",
		"`updated_at`", "`deleted_at`",
	)
)

type (
	// AccountConfigurationModel is an interface to be customized, add more methods here,
	// and implement the added methods in customAccountConfigurationModel.
	AccountConfigurationModel interface {
		accountConfigurationModel
		types.DBModel

		Trans(ctx context.Context, fn func(context context.Context, session sqlx.Session) error) error
		InsertBuilder(data *AccountConfiguration) squirrel.InsertBuilder
		UpdateBuilder(data *AccountConfiguration) squirrel.UpdateBuilder
		SelectBuilder() squirrel.SelectBuilder
		SelectCountBuilder() squirrel.SelectBuilder
		FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error)
		FindNoCacheByQuery(ctx context.Context, builder squirrel.SelectBuilder) ([]*AccountConfiguration, error)

		RemoveByConfigId(ctx context.Context, session sqlx.Session, projectId, configId string) (sql.Result, error)
	}

	customAccountConfigurationModel struct {
		*defaultAccountConfigurationModel
	}
)

// NewAccountConfigurationModel returns a model for the database table.
func NewAccountConfigurationModel(conn sqlx.SqlConn, c cache.CacheConf) AccountConfigurationModel {
	return &customAccountConfigurationModel{
		defaultAccountConfigurationModel: newAccountConfigurationModel(conn, c),
	}
}

func (m *customAccountConfigurationModel) Table() string {
	return m.table
}

func (m *customAccountConfigurationModel) Fields() []string {
	return accountConfigurationFieldNames
}

func (m *customAccountConfigurationModel) Trans(
	ctx context.Context, fn func(context context.Context, session sqlx.Session) error,
) error {
	return m.TransactCtx(ctx, fn)
}

func (m *customAccountConfigurationModel) InsertBuilder(data *AccountConfiguration) squirrel.InsertBuilder {
	return squirrel.Insert(m.table).Columns(accountConfigurationInsertFields...).Values(
		data.ProjectId, data.ConfigId, data.Name, data.Description, data.ProductType, data.ProductName,
		data.PoolEnvTable, data.PoolEnvName, data.Deleted, data.CreatedBy, data.UpdatedBy,
	)
}

func (m *customAccountConfigurationModel) UpdateBuilder(data *AccountConfiguration) squirrel.UpdateBuilder {
	eq := squirrel.Eq{
		"`name`":           data.Name,
		"`description`":    data.Description,
		"`product_type`":   data.ProductType,
		"`product_name`":   data.ProductName,
		"`pool_env_table`": data.PoolEnvTable,
		"`pool_env_name`":  data.PoolEnvName,
		"`deleted`":        data.Deleted,
		"`updated_by`":     data.UpdatedBy,
		"`deleted_by`":     data.DeletedBy,
		"`deleted_at`":     data.DeletedAt,
	}
	return squirrel.Update(m.table).SetMap(eq).Where("`id` = ?", data.Id)
}

func (m *customAccountConfigurationModel) SelectBuilder() squirrel.SelectBuilder {
	return squirrel.Select(accountConfigurationFieldNames...).Where("`deleted` = ?", constants.NotDeleted).From(m.table)
}

func (m *customAccountConfigurationModel) SelectCountBuilder() squirrel.SelectBuilder {
	return squirrel.Select("count(*)").Where("`deleted` = ?", constants.NotDeleted).From(m.table)
}

func (m *customAccountConfigurationModel) FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (
	int64, error,
) {
	query, values, err := countBuilder.ToSql()
	if err != nil {
		return 0, err
	}

	var resp int64
	err = m.QueryRowNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return 0, err
	}
}

func (m *customAccountConfigurationModel) FindNoCacheByQuery(
	ctx context.Context, builder squirrel.SelectBuilder,
) ([]*AccountConfiguration, error) {
	query, values, err := builder.ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*AccountConfiguration
	err = m.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

func (m *customAccountConfigurationModel) RemoveByConfigId(
	ctx context.Context, session sqlx.Session, projectId, configId string,
) (sql.Result, error) {
	keys := m.getKeysByConfigId(ctx, projectId, configId)

	return m.ExecCtx(
		ctx, func(ctx context.Context, conn sqlx.SqlConn) (sql.Result, error) {
			/*
				SQL:
				UPDATE `account_configuration`
				SET `deleted` = ?, `deleted_by` = ?, `deleted_at` = ?
				WHERE `project_id` = ?
				  AND `config_id` = ?
			*/
			stmt, values, err := squirrel.Update(m.table).
				SetMap(
					squirrel.Eq{
						"`deleted`":    constants.HasDeleted,
						"`deleted_by`": userinfo.FromContext(ctx).Account,
						"`deleted_at`": time.Now(),
					},
				).
				Where("`project_id` = ? AND `config_id` = ?", projectId, configId).
				ToSql()
			if err != nil {
				return nil, err
			}

			if session != nil {
				return session.ExecCtx(ctx, stmt, values...)
			}
			return conn.ExecCtx(ctx, stmt, values...)
		}, keys...,
	)
}

func (m *customAccountConfigurationModel) getKeysByConfigId(ctx context.Context, projectId, configId string) []string {
	c, err := m.FindOneByProjectIdConfigId(ctx, projectId, configId)
	if err != nil {
		return make([]string, 0)
	}

	return []string{
		fmt.Sprintf("%s%v", cacheManagerAccountConfigurationIdPrefix, c.Id),
		fmt.Sprintf("%s%v:%v", cacheManagerAccountConfigurationProjectIdConfigIdPrefix, c.ProjectId, c.ConfigId),
	}
}
