package model

import (
	"context"
	"database/sql"
	"fmt"
	"time"

	"github.com/Masterminds/squirrel"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/types"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/userinfo"
)

var (
	_ ApiCaseElementModel = (*customApiCaseElementModel)(nil)

	apiCaseElementInsertFields = stringx.Remove(
		apiCaseElementFieldNames, "`id`", "`create_time`", "`update_time`", "`deleted_by`", "`created_at`",
		"`updated_at`", "`deleted_at`",
	)
)

type (
	// ApiCaseElementModel is an interface to be customized, add more methods here,
	// and implement the added methods in customApiCaseElementModel.
	ApiCaseElementModel interface {
		apiCaseElementModel
		types.DBModel

		Trans(ctx context.Context, fn func(context context.Context, session sqlx.Session) error) error
		InsertBuilder(data *ApiCaseElement) squirrel.InsertBuilder
		UpdateBuilder(data *ApiCaseElement) squirrel.UpdateBuilder
		SelectBuilder() squirrel.SelectBuilder
		SelectCountBuilder() squirrel.SelectBuilder
		FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error)
		FindNoCacheByQuery(ctx context.Context, builder squirrel.SelectBuilder) ([]*ApiCaseElement, error)

		InsertTX(ctx context.Context, session sqlx.Session, data *ApiCaseElement) (sql.Result, error)
		UpdateTX(ctx context.Context, session sqlx.Session, data *ApiCaseElement) (sql.Result, error)
		RemoveByCaseId(ctx context.Context, session sqlx.Session, projectId, caseId string) (sql.Result, error)
	}

	customApiCaseElementModel struct {
		*defaultApiCaseElementModel
	}
)

// NewApiCaseElementModel returns a model for the database table.
func NewApiCaseElementModel(conn sqlx.SqlConn, c cache.CacheConf) ApiCaseElementModel {
	return &customApiCaseElementModel{
		defaultApiCaseElementModel: newApiCaseElementModel(conn, c),
	}
}

func (m *customApiCaseElementModel) Table() string {
	return m.table
}

func (m *customApiCaseElementModel) Fields() []string {
	return apiCaseElementFieldNames
}

func (m *customApiCaseElementModel) Trans(
	ctx context.Context, fn func(context context.Context, session sqlx.Session) error,
) error {
	return m.TransactCtx(ctx, fn)
}

func (m *customApiCaseElementModel) InsertBuilder(data *ApiCaseElement) squirrel.InsertBuilder {
	return squirrel.Insert(m.table).Columns(apiCaseElementInsertFields...).Values(
		data.ProjectId, data.CaseId, data.Version, data.ElementId, data.ElementType, data.Data, data.Deleted,
		data.CreatedBy, data.UpdatedBy,
	)
}

func (m *customApiCaseElementModel) UpdateBuilder(data *ApiCaseElement) squirrel.UpdateBuilder {
	eq := squirrel.Eq{
		"`data`":       data.Data,
		"`deleted`":    data.Deleted,
		"`updated_by`": data.UpdatedBy,
		"`deleted_by`": data.DeletedBy,
		"`deleted_at`": data.DeletedAt,
	}
	return squirrel.Update(m.table).SetMap(eq).Where("`id` = ?", data.Id)
}

func (m *customApiCaseElementModel) SelectBuilder() squirrel.SelectBuilder {
	return squirrel.Select(apiCaseElementFieldNames...).Where("`deleted` = ?", constants.NotDeleted).From(m.table)
}

func (m *customApiCaseElementModel) SelectCountBuilder() squirrel.SelectBuilder {
	return squirrel.Select("count(*)").Where("`deleted` = ?", constants.NotDeleted).From(m.table)
}

func (m *customApiCaseElementModel) FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error) {
	query, values, err := countBuilder.ToSql()
	if err != nil {
		return 0, err
	}

	var resp int64
	err = m.QueryRowNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return 0, err
	}
}

func (m *customApiCaseElementModel) FindNoCacheByQuery(
	ctx context.Context, builder squirrel.SelectBuilder,
) ([]*ApiCaseElement, error) {
	query, values, err := builder.ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*ApiCaseElement
	err = m.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

func (m *customApiCaseElementModel) InsertTX(
	ctx context.Context, session sqlx.Session, data *ApiCaseElement,
) (sql.Result, error) {
	managerApiCaseElementIdKey := fmt.Sprintf("%s%v", cacheManagerApiCaseElementIdPrefix, data.Id)
	managerApiCaseElementProjectIdCaseIdVersionElementIdKey := fmt.Sprintf(
		"%s%v:%v:%v:%v", cacheManagerApiCaseElementProjectIdCaseIdVersionElementIdPrefix, data.ProjectId, data.CaseId,
		data.Version, data.ElementId,
	)

	return m.ExecCtx(
		ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
			query, values, err := m.InsertBuilder(data).ToSql()
			if err != nil {
				return nil, err
			}
			if session != nil {
				return session.ExecCtx(ctx, query, values...)
			}
			return conn.ExecCtx(ctx, query, values...)
		}, managerApiCaseElementIdKey, managerApiCaseElementProjectIdCaseIdVersionElementIdKey,
	)
}

func (m *customApiCaseElementModel) UpdateTX(
	ctx context.Context, session sqlx.Session, data *ApiCaseElement,
) (sql.Result, error) {
	managerApiCaseElementIdKey := fmt.Sprintf("%s%v", cacheManagerApiCaseElementIdPrefix, data.Id)
	managerApiCaseElementProjectIdCaseIdVersionElementIdKey := fmt.Sprintf(
		"%s%v:%v:%v:%v", cacheManagerApiCaseElementProjectIdCaseIdVersionElementIdPrefix, data.ProjectId, data.CaseId,
		data.Version, data.ElementId,
	)

	return m.ExecCtx(
		ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
			query, values, err := m.UpdateBuilder(data).ToSql()
			if err != nil {
				return nil, err
			}
			if session != nil {
				return session.ExecCtx(ctx, query, values...)
			}
			return conn.ExecCtx(ctx, query, values...)
		}, managerApiCaseElementIdKey, managerApiCaseElementProjectIdCaseIdVersionElementIdKey,
	)
}

func (m *customApiCaseElementModel) RemoveByCaseId(
	ctx context.Context, session sqlx.Session, projectId, caseId string,
) (sql.Result, error) {
	keys := m.getKeysByCaseId(ctx, projectId, caseId)

	return m.ExecCtx(
		ctx, func(ctx context.Context, conn sqlx.SqlConn) (sql.Result, error) {
			/*
				SQL:
				UPDATE `api_case_element`
				SET `deleted` = 1, `deleted_by` = ?, `deleted_at` = ?
				WHERE `project_id` = ?
				  AND `case_id` = ?;
			*/
			stmt, values, err := squirrel.Update(m.table).
				SetMap(
					squirrel.Eq{
						"`deleted`":    constants.HasDeleted,
						"`deleted_by`": userinfo.FromContext(ctx).Account,
						"`deleted_at`": time.Now(),
					},
				).
				Where("`project_id` = ? AND `case_id` = ?", projectId, caseId).
				ToSql()
			if err != nil {
				return nil, err
			}

			if session != nil {
				return session.ExecCtx(ctx, stmt, values...)
			}
			return conn.ExecCtx(ctx, stmt, values...)
		}, keys...,
	)
}

func (m *customApiCaseElementModel) getKeysByCaseId(ctx context.Context, projectId, caseId string) []string {
	sb := m.SelectBuilder().Where("`project_id` = ? AND `case_id` = ?", projectId, caseId)
	cs, err := m.FindNoCacheByQuery(ctx, sb)
	if err != nil {
		return make([]string, 0)
	}

	keys := make([]string, 0, len(cs)*2)
	for _, c := range cs {
		keys = append(
			keys,
			fmt.Sprintf("%s%v", cacheManagerApiCaseElementIdPrefix, c.Id),
			fmt.Sprintf(
				"%s%v:%v:%v:%v", cacheManagerApiCaseElementProjectIdCaseIdVersionElementIdPrefix, c.ProjectId, c.CaseId,
				c.Version, c.ElementId,
			),
		)
	}

	return keys
}
