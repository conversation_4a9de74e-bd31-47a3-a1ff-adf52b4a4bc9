package model

import (
	"context"
	"database/sql"
	"fmt"
	"time"

	"github.com/Masterminds/squirrel"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/types"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/userinfo"
)

var (
	_ PerfPlanCaseRelationshipModel = (*customPerfPlanCaseRelationshipModel)(nil)

	perfPlanCaseRelationshipInsertFields = stringx.Remove(
		perfPlanCaseRelationshipFieldNames, "`id`", "`create_time`", "`update_time`", "`deleted_by`", "`created_at`",
		"`updated_at`", "`deleted_at`",
	)
)

type (
	// PerfPlanCaseRelationshipModel is an interface to be customized, add more methods here,
	// and implement the added methods in customPerfPlanCaseRelationshipModel.
	PerfPlanCaseRelationshipModel interface {
		perfPlanCaseRelationshipModel
		types.DBModel

		Trans(ctx context.Context, fn func(context context.Context, session sqlx.Session) error) error
		InsertBuilder(data *PerfPlanCaseRelationship) squirrel.InsertBuilder
		UpdateBuilder(data *PerfPlanCaseRelationship) squirrel.UpdateBuilder
		SelectBuilder() squirrel.SelectBuilder
		SelectCountBuilder() squirrel.SelectBuilder
		FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error)
		FindNoCacheByQuery(ctx context.Context, selectBuilder squirrel.SelectBuilder) (
			[]*PerfPlanCaseRelationship, error,
		)

		BatchInsert(ctx context.Context, session sqlx.Session, items []*PerfPlanCaseRelationship) (sql.Result, error)
		FindByCaseID(ctx context.Context, projectID, caseID string) ([]*PerfPlanCaseRelationship, error)
		FindByPlanID(ctx context.Context, projectID, planID string) ([]*PerfPlanCaseRelationship, error)
		RemoveByPlanID(ctx context.Context, session sqlx.Session, projectID, planID string) (sql.Result, error)
	}

	customPerfPlanCaseRelationshipModel struct {
		*defaultPerfPlanCaseRelationshipModel

		conn sqlx.SqlConn
	}
)

// NewPerfPlanCaseRelationshipModel returns a model for the database table.
func NewPerfPlanCaseRelationshipModel(
	conn sqlx.SqlConn, c cache.CacheConf, opts ...cache.Option,
) PerfPlanCaseRelationshipModel {
	return &customPerfPlanCaseRelationshipModel{
		defaultPerfPlanCaseRelationshipModel: newPerfPlanCaseRelationshipModel(conn, c, opts...),
		conn:                                 conn,
	}
}

func (m *customPerfPlanCaseRelationshipModel) Table() string {
	return m.table
}

func (m *customPerfPlanCaseRelationshipModel) Fields() []string {
	return perfPlanCaseRelationshipFieldNames
}

func (m *customPerfPlanCaseRelationshipModel) Trans(
	ctx context.Context, fn func(context context.Context, session sqlx.Session) error,
) error {
	return m.TransactCtx(ctx, fn)
}

func (m *customPerfPlanCaseRelationshipModel) InsertBuilder(data *PerfPlanCaseRelationship) squirrel.InsertBuilder {
	return squirrel.Insert(m.table).Columns(perfPlanCaseRelationshipInsertFields...).Values(
		data.ProjectId, data.PlanId, data.CaseId, data.RateLimits, data.TargetRps, data.PerfDataId, data.CustomVu,
		data.NumberOfVu, data.CustomLg, data.NumberOfLg, data.RequestsOfCpu, data.RequestsOfMemory, data.LimitsOfCpu,
		data.LimitsOfMemory, data.EstimatedDuration, data.State, data.Deleted, data.CreatedBy, data.UpdatedBy,
	)
}

func (m *customPerfPlanCaseRelationshipModel) UpdateBuilder(data *PerfPlanCaseRelationship) squirrel.UpdateBuilder {
	eq := squirrel.Eq{
		"`perf_data_id`":       data.PerfDataId,
		"`custom_vu`":          data.CustomVu,
		"`number_of_vu`":       data.NumberOfVu,
		"`custom_lg`":          data.CustomLg,
		"`number_of_lg`":       data.NumberOfLg,
		"`requests_of_cpu`":    data.RequestsOfCpu,
		"`requests_of_memory`": data.RequestsOfMemory,
		"`limits_of_cpu`":      data.LimitsOfCpu,
		"`limits_of_memory`":   data.LimitsOfMemory,
		"`estimated_duration`": data.EstimatedDuration,
		"`state`":              data.State,
		"`deleted`":            data.Deleted,
		"`updated_by`":         data.UpdatedBy,
		"`deleted_by`":         data.DeletedBy,
		"`deleted_at`":         data.DeletedAt,
	}
	return squirrel.Update(m.table).SetMap(eq).Where("`id` = ?", data.Id)
}

func (m *customPerfPlanCaseRelationshipModel) SelectBuilder() squirrel.SelectBuilder {
	return squirrel.Select(perfPlanCaseRelationshipFieldNames...).Where(
		"`deleted` = ?", constants.NotDeleted,
	).From(m.table)
}

func (m *customPerfPlanCaseRelationshipModel) SelectCountBuilder() squirrel.SelectBuilder {
	return squirrel.Select("count(*)").Where("`deleted` = ?", constants.NotDeleted).From(m.table)
}

func (m *customPerfPlanCaseRelationshipModel) FindCount(
	ctx context.Context, countBuilder squirrel.SelectBuilder,
) (int64, error) {
	query, values, err := countBuilder.ToSql()
	if err != nil {
		return 0, err
	}

	var resp int64
	err = m.QueryRowNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return 0, err
	}
}

func (m *customPerfPlanCaseRelationshipModel) FindNoCacheByQuery(
	ctx context.Context, selectBuilder squirrel.SelectBuilder,
) ([]*PerfPlanCaseRelationship, error) {
	query, values, err := selectBuilder.ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*PerfPlanCaseRelationship
	err = m.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

func (m *customPerfPlanCaseRelationshipModel) BatchInsert(
	ctx context.Context, session sqlx.Session, items []*PerfPlanCaseRelationship,
) (result sql.Result, err error) {
	count := len(items)
	if count == 0 {
		return nil, nil
	} else if count == 1 {
		return m.Insert(ctx, session, items[0])
	}

	var (
		baseInsertBuilder = squirrel.Insert(m.table).Columns(perfPlanCaseRelationshipInsertFields...)
		insertBuilder     squirrel.InsertBuilder
		size              = 0
	)

	for i, item := range items {
		if size == 0 {
			insertBuilder = baseInsertBuilder
		}

		insertBuilder = insertBuilder.Values(
			item.ProjectId, item.PlanId, item.CaseId, item.RateLimits, item.TargetRps, item.PerfDataId, item.CustomVu,
			item.NumberOfVu, item.CustomLg, item.NumberOfLg, item.RequestsOfCpu, item.RequestsOfMemory, item.LimitsOfCpu,
			item.LimitsOfMemory, item.EstimatedDuration, item.State, item.Deleted, item.CreatedBy, item.UpdatedBy,
		)
		size += 1

		if size == ConstBatchInsertSize || i == count-1 {
			result, err = m.ExecCtx(
				ctx, func(ctx context.Context, conn sqlx.SqlConn) (sql.Result, error) {
					query, values, err := insertBuilder.ToSql()
					if err != nil {
						return nil, err
					}
					if session != nil {
						return session.ExecCtx(ctx, query, values...)
					}
					return conn.ExecCtx(ctx, query, values...)
				},
			)
			if err != nil {
				return result, err
			}

			size = 0
		}
	}

	return result, err
}

func (m *customPerfPlanCaseRelationshipModel) FindByCaseID(
	ctx context.Context, projectID, caseID string,
) ([]*PerfPlanCaseRelationship, error) {
	/*
		SQL:
		SELECT *
		FROM `perf_plan_case_relationship`
		WHERE `project_id` = ?
		  AND `case_id` = ?
		  AND `deleted` = ?
	*/

	return m.FindNoCacheByQuery(ctx, m.SelectBuilder().Where("`project_id` = ? AND `case_id` = ?", projectID, caseID))
}

func (m *customPerfPlanCaseRelationshipModel) FindByPlanID(
	ctx context.Context, projectID, planID string,
) ([]*PerfPlanCaseRelationship, error) {
	/*
		SQL:
		SELECT *
		FROM `perf_plan_case_relationship`
		WHERE `project_id` = ?
		  AND `plan_id` = ?
		  AND `deleted` = ?
	*/

	return m.FindNoCacheByQuery(ctx, m.SelectBuilder().Where("`project_id` = ? AND `plan_id` = ?", projectID, planID))
}

func (m *customPerfPlanCaseRelationshipModel) RemoveByPlanID(
	ctx context.Context, session sqlx.Session, projectID, planID string,
) (sql.Result, error) {
	keys := m.getKeysByPlanID(ctx, projectID, planID)

	return m.ExecCtx(
		ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
			/*
				SQL:
				UPDATE `perf_plan_case_relationship`
				SET `deleted` = ?, `deleted_by` = ?, `deleted_at` = ?
				WHERE `project_id` = ?
				  AND `plan_id` = ?
			*/

			stmt, values, err := squirrel.Update(m.table).
				SetMap(
					squirrel.Eq{
						"`deleted`":    constants.HasDeleted,
						"`deleted_by`": userinfo.FromContext(ctx).Account,
						"`deleted_at`": time.Now(),
					},
				).
				Where("`project_id` = ? AND `plan_id` = ?", projectID, planID).
				ToSql()
			if err != nil {
				return nil, err
			}

			if session != nil {
				return session.ExecCtx(ctx, stmt, values...)
			}
			return conn.ExecCtx(ctx, stmt, values...)
		}, keys...,
	)
}

func (m *customPerfPlanCaseRelationshipModel) getKeysByPlanID(ctx context.Context, projectID, planID string) []string {
	cs, err := m.FindByPlanID(ctx, projectID, planID)
	if err != nil {
		return make([]string, 0)
	}

	keys := make([]string, 0, len(cs))
	for _, c := range cs {
		keys = append(keys, fmt.Sprintf("%s%v", cacheManagerPerfPlanCaseRelationshipIdPrefix, c.Id))
	}

	return keys
}
