package model

import (
	"context"
	"database/sql"
	"fmt"
	"time"

	"github.com/Masterminds/squirrel"
	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sqlbuilder"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/types"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/userinfo"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/utils"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
)

var (
	_                                   ComponentGroupModel = (*customComponentGroupModel)(nil)
	VirtualComponentGroupReferenceModel types.DBModel       = (*virtualComponentGroupReferenceModel)(nil)

	componentGroupInsertFields = stringx.Remove(
		componentGroupFieldNames, "`id`", "`create_time`", "`update_time`", "`deleted_by`", "`created_at`",
		"`updated_at`", "`deleted_at`",
	)

	// cacheManagerComponentGroupProjectIdComponentGroupIdLatestPrefix = "cache:manager:componentGroup:projectId:componentGroupId:latest:"

	virtualComponentGroupReferenceFieldNames = builder.RawFieldNames(&SearchComponentGroupReferenceItem{})
)

type (
	// ComponentGroupModel is an interface to be customized, add more methods here,
	// and implement the added methods in customComponentGroupModel.
	ComponentGroupModel interface {
		componentGroupModel
		types.DBModel

		Trans(ctx context.Context, fn func(context context.Context, session sqlx.Session) error) error
		InsertBuilder(data *ComponentGroup) squirrel.InsertBuilder
		UpdateBuilder(data *ComponentGroup) squirrel.UpdateBuilder
		SelectBuilder() squirrel.SelectBuilder
		SelectCountBuilder() squirrel.SelectBuilder
		FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error)
		FindNoCacheByQuery(ctx context.Context, builder squirrel.SelectBuilder) ([]*ComponentGroup, error)

		InsertTX(ctx context.Context, session sqlx.Session, data *ComponentGroup) (sql.Result, error)
		UpdateTX(ctx context.Context, session sqlx.Session, data *ComponentGroup) (sql.Result, error)
		UpdateAllToNotLatest(ctx context.Context, session sqlx.Session, projectId, componentGroupId string) (
			sql.Result, error,
		)
		FindLatestOneNoCache(ctx context.Context, projectId, componentGroupId string) (*ComponentGroup, error)
		FindCountByCategoryId(ctx context.Context, cm CategoryModel, cond FindCountByCategoryCondition) (int64, error)
		RemoveByComponentGroupId(
			ctx context.Context, session sqlx.Session, projectId, componentGroupId string,
		) (sql.Result, error)

		GenerateSearchComponentGroupReferenceSqlBuilder(req SearchComponentGroupReferenceReq) (
			searchComponentGroupReferenceSelectBuilder, searchComponentGroupReferenceCountBuilder,
		)
		FindCountComponentGroupReference(
			ctx context.Context, countBuilder searchComponentGroupReferenceCountBuilder,
		) (int64, error)
		FindComponentGroupReference(
			ctx context.Context, selectBuilder searchComponentGroupReferenceSelectBuilder,
		) ([]*SearchComponentGroupReferenceItem, error)
	}

	customComponentGroupModel struct {
		*defaultComponentGroupModel

		conn sqlx.SqlConn
	}
)

// NewComponentGroupModel returns a model for the database table.
func NewComponentGroupModel(conn sqlx.SqlConn, c cache.CacheConf) ComponentGroupModel {
	return &customComponentGroupModel{
		defaultComponentGroupModel: newComponentGroupModel(conn, c),
		conn:                       conn,
	}
}

func (m *customComponentGroupModel) Table() string {
	return m.table
}

func (m *customComponentGroupModel) Fields() []string {
	return componentGroupFieldNames
}

func (m *customComponentGroupModel) Trans(
	ctx context.Context, fn func(context context.Context, session sqlx.Session) error,
) error {
	return m.TransactCtx(ctx, fn)
}

func (m *customComponentGroupModel) InsertBuilder(data *ComponentGroup) squirrel.InsertBuilder {
	if data.CreatedAt == ZeroTime {
		return squirrel.Insert(m.table).Columns(componentGroupInsertFields...).Values(
			data.ProjectId, data.CategoryId, data.ComponentGroupId, data.ComponentGroupType, data.Name,
			data.Description, data.Priority, data.Tags, data.Imports, data.Exports, data.AccountConfig, data.Version,
			data.Structure, data.ReferenceStructure, data.Latest, data.Deleted, data.MaintainedBy, data.CreatedBy,
			data.UpdatedBy,
		)
	} else {
		return squirrel.Insert(m.table).Columns(componentGroupInsertFields...).Columns("`created_at`").Values(
			data.ProjectId, data.CategoryId, data.ComponentGroupId, data.ComponentGroupType, data.Name,
			data.Description, data.Priority, data.Tags, data.Imports, data.Exports, data.AccountConfig, data.Version,
			data.Structure, data.ReferenceStructure, data.Latest, data.Deleted, data.MaintainedBy, data.CreatedBy,
			data.UpdatedBy, data.CreatedAt,
		)
	}
}

func (m *customComponentGroupModel) UpdateBuilder(data *ComponentGroup) squirrel.UpdateBuilder {
	eq := squirrel.Eq{
		"`category_id`":         data.CategoryId,
		"`name`":                data.Name,
		"`description`":         data.Description,
		"`priority`":            data.Priority,
		"`tags`":                data.Tags,
		"`imports`":             data.Imports,
		"`exports`":             data.Exports,
		"`account_config`":      data.AccountConfig,
		"`version`":             data.Version,
		"`structure`":           data.Structure,
		"`reference_structure`": data.ReferenceStructure,
		"`latest`":              data.Latest,
		"`deleted`":             data.Deleted,
		"`maintained_by`":       data.MaintainedBy,
		"`updated_by`":          data.UpdatedBy,
		"`deleted_by`":          data.DeletedBy,
		"`deleted_at`":          data.DeletedAt,
	}
	return squirrel.Update(m.table).SetMap(eq).Where("`id` = ?", data.Id)
}

func (m *customComponentGroupModel) SelectBuilder() squirrel.SelectBuilder {
	return squirrel.Select(componentGroupFieldNames...).Where("`deleted` = ?", constants.NotDeleted).From(m.table)
}

func (m *customComponentGroupModel) SelectCountBuilder() squirrel.SelectBuilder {
	return squirrel.Select("count(*)").Where("`deleted` = ?", constants.NotDeleted).From(m.table)
}

func (m *customComponentGroupModel) FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error) {
	query, values, err := countBuilder.ToSql()
	if err != nil {
		return 0, err
	}

	var resp int64
	err = m.QueryRowNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return 0, err
	}
}

func (m *customComponentGroupModel) FindNoCacheByQuery(
	ctx context.Context, builder squirrel.SelectBuilder,
) ([]*ComponentGroup, error) {
	query, values, err := builder.ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*ComponentGroup
	err = m.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

func (m *customComponentGroupModel) InsertTX(
	ctx context.Context, session sqlx.Session, data *ComponentGroup,
) (sql.Result, error) {
	managerComponentGroupIdKey := fmt.Sprintf("%s%v", cacheManagerComponentGroupIdPrefix, data.Id)
	managerComponentGroupProjectIdComponentGroupIdVersionKey := fmt.Sprintf(
		"%s%v:%v:%v", cacheManagerComponentGroupProjectIdComponentGroupIdVersionPrefix, data.ProjectId,
		data.ComponentGroupId, data.Version,
	)
	// managerComponentGroupProjectIdComponentGroupIdLatestKey := fmt.Sprintf("%s%v:%v:%v", cacheManagerComponentGroupProjectIdComponentGroupIdLatestPrefix, data.ProjectId, data.ComponentGroupId, constants.IsLatestVersion)

	return m.ExecCtx(
		ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
			query, values, err := m.InsertBuilder(data).ToSql()
			if err != nil {
				return nil, err
			}
			if session != nil {
				return session.ExecCtx(ctx, query, values...)
			}
			return conn.ExecCtx(ctx, query, values...)
		}, managerComponentGroupIdKey, managerComponentGroupProjectIdComponentGroupIdVersionKey,
	)
}

func (m *customComponentGroupModel) UpdateTX(
	ctx context.Context, session sqlx.Session, newData *ComponentGroup,
) (sql.Result, error) {
	data, err := m.FindOne(ctx, newData.Id)
	if err != nil {
		return nil, err
	}

	managerComponentGroupIdKey := fmt.Sprintf("%s%v", cacheManagerComponentGroupIdPrefix, data.Id)
	managerComponentGroupProjectIdComponentGroupIdVersionKey := fmt.Sprintf(
		"%s%v:%v:%v", cacheManagerComponentGroupProjectIdComponentGroupIdVersionPrefix, data.ProjectId,
		data.ComponentGroupId, data.Version,
	)
	// managerComponentGroupProjectIdComponentGroupIdLatestKey := fmt.Sprintf("%s%v:%v:%v", cacheManagerComponentGroupProjectIdComponentGroupIdLatestPrefix, data.ProjectId, data.ComponentGroupId, constants.IsLatestVersion)

	return m.ExecCtx(
		ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
			query, values, err := m.UpdateBuilder(newData).ToSql()
			if err != nil {
				return nil, err
			}
			if session != nil {
				return session.ExecCtx(ctx, query, values...)
			}
			return conn.ExecCtx(ctx, query, values...)
		}, managerComponentGroupIdKey, managerComponentGroupProjectIdComponentGroupIdVersionKey,
	)
}

func (m *customComponentGroupModel) UpdateAllToNotLatest(
	ctx context.Context, session sqlx.Session, projectId, componentGroupId string,
) (sql.Result, error) {
	keys := m.getKeysByComponentGroupId(ctx, projectId, componentGroupId)

	ub := squirrel.Update(m.table).
		SetMap(squirrel.Eq{"`latest`": constants.IsNotLatestVersion}).
		Where(
			"`project_id` = ? AND `component_group_id` = ? AND `deleted` = ?",
			projectId, componentGroupId, constants.NotDeleted,
		)

	return m.ExecCtx(
		ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
			query, values, err := ub.ToSql()
			if err != nil {
				return nil, err
			}
			if session != nil {
				return session.ExecCtx(ctx, query, values...)
			}
			return conn.ExecCtx(ctx, query, values...)
		}, keys...,
	)
}

func (m *customComponentGroupModel) FindLatestOneNoCache(
	ctx context.Context, projectId, componentGroupId string,
) (*ComponentGroup, error) {
	sb := m.SelectBuilder().Where(
		"`project_id` = ? AND `component_group_id` = ? AND `latest` = ?",
		projectId, componentGroupId, constants.IsLatestVersion,
	).OrderBy("`updated_at` DESC").Limit(1)
	resp, err := m.FindNoCacheByQuery(ctx, sb)
	if err != nil {
		return nil, err
	} else if len(resp) == 0 {
		return nil, ErrNotFound
	}
	return resp[0], nil
}

//func (m *customComponentGroupModel) FindLatestOne(ctx context.Context, projectId, componentGroupId string) (*ComponentGroup, error) {
//	managerComponentGroupProjectIdComponentGroupIdLatestKey := fmt.Sprintf("%s%v:%v:%v", cacheManagerComponentGroupProjectIdComponentGroupIdLatestPrefix, projectId, componentGroupId, constants.IsLatestVersion)
//
//	var resp ComponentGroup
//	err := m.QueryRowCtx(ctx, &resp, managerComponentGroupProjectIdComponentGroupIdLatestKey, func(ctx context.Context, conn sqlx.SqlConn, v interface{}) error {
//		query, values, err := m.SelectBuilder().Where("`project_id` = ? and `component_group_id` = ? and `latest` = ?", projectId, componentGroupId, constants.IsLatestVersion).OrderBy("`updated_at` DESC").Limit(1).ToSql()
//		if err != nil {
//			return err
//		}
//
//		if err = conn.QueryRowCtx(ctx, &resp, query, values...); err != nil {
//			return err
//		}
//
//		return nil
//	})
//	switch err {
//	case nil:
//		return &resp, nil
//	default:
//		return nil, err
//	}
//}

func (m *customComponentGroupModel) FindCountByCategoryId(
	ctx context.Context, cm CategoryModel, cond FindCountByCategoryCondition,
) (int64, error) {
	alias := "t"
	sb := squirrel.Select("COUNT(*)").
		From(fmt.Sprintf("%s AS %s", m.Table(), alias)).
		Where(
			fmt.Sprintf("%s.`project_id` = ? AND %s.`latest` = ? AND %s.`deleted` = ?", alias, alias, alias),
			cond.ProjectId, constants.IsLatestVersion, constants.NotDeleted,
		)

	if cond.DrillDown {
		sub := cm.FindDescendantCategoriesSqlBuilder(
			GetCategoryTreeCondition{
				ProjectId:     cond.ProjectId,
				Type:          common.ConstCategoryTreeTypeComponentGroup,
				CategoryId:    cond.CategoryId,
				OnlyDirectory: true,
			},
		)

		sb = sb.JoinClause(
			sub.Prefix("LEFT JOIN (").Suffix(
				fmt.Sprintf(
					") AS t1 ON %s.`category_id` = t1.`category_id`", alias,
				),
			),
		).Where("t1.`category_id` IS NOT NULL")
	} else {
		sb = sb.Where(fmt.Sprintf("%s.`category_id` = ?", alias), cond.CategoryId)
	}

	return m.FindCount(ctx, sb)
}

func (m *customComponentGroupModel) RemoveByComponentGroupId(
	ctx context.Context, session sqlx.Session, projectId, componentGroupId string,
) (sql.Result, error) {
	keys := m.getKeysByComponentGroupId(ctx, projectId, componentGroupId)

	return m.ExecCtx(
		ctx, func(ctx context.Context, conn sqlx.SqlConn) (sql.Result, error) {
			/*
				SQL:
				UPDATE `component_group`
				SET `deleted` = ?, `deleted_by` = ?, `deleted_at` = ?
				WHERE `project_id` = ?
				  AND `component_group_id` = ?
			*/
			stmt, values, err := squirrel.Update(m.table).
				SetMap(
					squirrel.Eq{
						"`deleted`":    constants.HasDeleted,
						"`deleted_by`": userinfo.FromContext(ctx).Account,
						"`deleted_at`": time.Now(),
					},
				).
				Where("`project_id` = ? AND `component_group_id` = ?", projectId, componentGroupId).
				ToSql()
			if err != nil {
				return nil, err
			}

			if session != nil {
				return session.ExecCtx(ctx, stmt, values...)
			}
			return conn.ExecCtx(ctx, stmt, values...)
		}, keys...,
	)
}

func (m *customComponentGroupModel) getKeysByComponentGroupId(
	ctx context.Context, projectId, componentGroupId string,
) []string {
	sb := m.SelectBuilder().Where("`project_id` = ? AND `component_group_id` = ?", projectId, componentGroupId)
	cs, err := m.FindNoCacheByQuery(ctx, sb)
	if err != nil {
		return make([]string, 0)
	}

	keys := make([]string, 0, len(cs)*2)
	for _, c := range cs {
		keys = append(
			keys,
			fmt.Sprintf("%s%v", cacheManagerComponentGroupIdPrefix, c.Id),
			fmt.Sprintf(
				"%s%v:%v:%v", cacheManagerComponentGroupProjectIdComponentGroupIdVersionPrefix, c.ProjectId,
				c.ComponentGroupId, c.Version,
			),
			// fmt.Sprintf("%s%v:%v:%v", cacheManagerComponentGroupProjectIdComponentGroupIdLatestPrefix, c.ProjectId, c.ComponentGroupId, constants.IsLatestVersion),
		)
	}

	return keys
}

type virtualComponentGroupReferenceModel struct{}

func (m *virtualComponentGroupReferenceModel) Table() string {
	return "`virtual_component_group_reference`"
}

func (m *virtualComponentGroupReferenceModel) Fields() []string {
	return virtualComponentGroupReferenceFieldNames
}

type SearchComponentGroupReferenceItem struct {
	ProjectId                   string         `db:"project_id"`                     // 项目ID
	ComponentGroupId            string         `db:"component_group_id"`             // 组件组ID
	ReferenceType               string         `db:"reference_type"`                 // 引用对象类型（组件组、API用例、接口用例）
	ReferenceId                 string         `db:"reference_id"`                   // 引用对象ID（组件组ID、API用例ID、接口用例ID）
	ReferenceVersion            string         `db:"reference_version"`              // 引用对象版本（组件组版本、API用例版本、接口用例版本）
	ReferenceParentId           string         `db:"reference_parent_id"`            // 引用对象的父ID（引用对象类型为接口用例时才有值）
	ReferenceComponentGroupType string         `db:"reference_component_group_type"` // 引用对象的组件组类型（引用对象类型为组件组时才有值）
	Name                        string         `db:"name"`                           // 引用对象名称
	Description                 sql.NullString `db:"description"`                    // 引用对象描述
	Priority                    int64          `db:"priority"`                       // 优先级（NULL、P0、P1、P2、P3...）
	Tags                        sql.NullString `db:"tags"`                           // 标签
	State                       string         `db:"state"`                          // 引用对象状态（生效、失效）
	MaintainedBy                sql.NullString `db:"maintained_by"`                  // 维护者的用户ID
	CreatedBy                   string         `db:"created_by"`                     // 创建者的用户ID
	UpdatedBy                   string         `db:"updated_by"`                     // 最近一次更新者的用户ID
	CreatedAt                   time.Time      `db:"created_at"`                     // 创建时间
	UpdatedAt                   time.Time      `db:"updated_at"`                     // 更新时间
}

type searchComponentGroupReferenceSelectBuilder struct {
	squirrel.SelectBuilder
}

type searchComponentGroupReferenceCountBuilder struct {
	squirrel.SelectBuilder
}

func (m *customComponentGroupModel) GenerateSearchComponentGroupReferenceSqlBuilder(req SearchComponentGroupReferenceReq) (
	searchComponentGroupReferenceSelectBuilder, searchComponentGroupReferenceCountBuilder,
) {
	/*
		SQL:
		SELECT t.`project_id`,
		       t.`component_group_id`,
		       t.`reference_type`,
		       t.`reference_id`,
		       t.`reference_version`,
		       t.`reference_parent_id`,
		       t.`reference_component_group_type`,
		       t.`name`,
		       t.`description`,
		       t.`priority`,
		       t.`tags`,
		       t.`state`,
		       t.`maintained_by`,
		       t.`created_by`,
		       t.`updated_by`,
		       t.`created_at`,
		       t.`updated_at`
		FROM (SELECT t1.`project_id`,
		             t1.`component_group_id`,
		             t2.`reference_type`,
		             t2.`reference_id`,
		             t2.`reference_version`,
		             IF(`reference_type` = 'INTERFACE_CASE', t5.`document_id`, NULL)                                                                         AS `reference_parent_id`,
		             IF(`reference_type` = 'COMPONENT_GROUP', t3.`component_group_type`, NULL)                                                               AS `reference_component_group_type`,
		             IF(`reference_type` = 'COMPONENT_GROUP', t3.`name`, IF(`reference_type` = 'API_CASE', t4.`name`, t5.`name`))                            AS `name`,
		             IF(`reference_type` = 'COMPONENT_GROUP', t3.`description`, IF(`reference_type` = 'API_CASE', t4.`description`, t5.`description`))       AS `description`,
		             IF(`reference_type` = 'COMPONENT_GROUP', t3.`priority`, IF(`reference_type` = 'API_CASE', t4.`priority`, t5.`priority`))                AS `priority`,
		             IF(`reference_type` = 'COMPONENT_GROUP', t3.`tags`, IF(`reference_type` = 'API_CASE', t4.`tags`, t5.`tags`))                            AS `tags`,
		             IF(`reference_type` = 'COMPONENT_GROUP', 1, IF(`reference_type` = 'API_CASE', t4.`state`, t5.`state`))                                  AS `state`,
		             IF(`reference_type` = 'COMPONENT_GROUP', t3.`maintained_by`, IF(`reference_type` = 'API_CASE', t4.`maintained_by`, t5.`maintained_by`)) AS `maintained_by`,
		             IF(`reference_type` = 'COMPONENT_GROUP', t3.`created_by`, IF(`reference_type` = 'API_CASE', t4.`created_by`, t5.`created_by`))          AS `created_by`,
		             IF(`reference_type` = 'COMPONENT_GROUP', t3.`updated_by`, IF(`reference_type` = 'API_CASE', t4.`updated_by`, t5.`updated_by`))          AS `updated_by`,
		             IF(`reference_type` = 'COMPONENT_GROUP', t3.`created_at`, IF(`reference_type` = 'API_CASE', t4.`created_at`, t5.`created_at`))          AS `created_at`,
		             IF(`reference_type` = 'COMPONENT_GROUP', t3.`updated_at`, IF(`reference_type` = 'API_CASE', t4.`updated_at`, t5.`updated_at`))          AS `updated_at`
		      FROM `component_group` AS t1
		               INNER JOIN (SELECT `project_id`,
		                                  `reference_type`,
		                                  `reference_id`,
		                                  `reference_version`,
		                                  `component_group_id`,
		                                  `deleted`
		                           FROM `component_group_reference_relationship`
		                           WHERE `project_id` = ?
		                             AND `component_group_id` = ?
		                             AND `deleted` = 0
		                           GROUP BY `reference_type`, `reference_id`, `reference_version`) AS t2 ON t1.`project_id` = t2.`project_id` AND t1.`component_group_id` = t2.`component_group_id` AND t1.`deleted` = t2.`deleted`
		               LEFT JOIN `component_group` AS t3 ON t2.`reference_type` = 'COMPONENT_GROUP' AND t1.`project_id` = t3.`project_id` AND t2.`reference_id` = t3.`component_group_id` AND t2.`reference_version` = t3.`version` AND t1.`latest` = t3.`latest` AND t1.`deleted` = t3.`deleted`
		               LEFT JOIN `api_case` AS t4 ON t2.`reference_type` = 'API_CASE' AND t1.`project_id` = t4.`project_id` AND t2.`reference_id` = t4.`case_id` AND t2.`reference_version` = t4.`version` AND t1.`latest` = t4.`latest` AND t1.`deleted` = t4.`deleted`
		               LEFT JOIN `interface_case` AS t5 ON t2.`reference_type` = 'INTERFACE_CASE' AND t1.`project_id` = t5.`project_id` AND t2.`reference_id` = t5.`case_id` AND t2.`reference_version` = t5.`version` AND t1.`latest` = t5.`latest` AND t1.`deleted` = t5.`deleted`
		      WHERE t1.`project_id` = ?
		        AND t1.`component_group_id` = ?
		        AND t1.`latest` = 1
		        AND t1.`deleted` = 0
		        AND (t3.`id` IS NOT NULL OR t4.`id` IS NOT NULL OR t5.`id` IS NOT NULL)) AS t;
	*/

	sub := squirrel.Select(
		"`project_id`", "`reference_type`", "`reference_id`", "`reference_version`", "`component_group_id`",
		"`deleted`",
	).
		From(componentGroupReferenceRelationshipTableName).
		Where(
			"`project_id` = ? AND `component_group_id` = ? AND `deleted` = ?",
			req.ProjectId, req.ComponentGroupId, constants.NotDeleted,
		).
		GroupBy("`reference_type`", "`reference_id`", "`reference_version`")

	fields := []string{
		"t1.`project_id`",
		"t1.`component_group_id`",
		"t2.`reference_type`",
		"t2.`reference_id`",
		"t2.`reference_version`",
		fmt.Sprintf(
			"IF(`reference_type` = '%s', t5.`document_id`, '') AS `reference_parent_id`",
			common.ConstReferenceTypeInterfaceCase,
		),
		fmt.Sprintf(
			"IF(`reference_type` = '%s', t3.`component_group_type`, '') AS `reference_component_group_type`",
			common.ConstReferenceTypeComponentGroup,
		),
		fmt.Sprintf(
			"IF(`reference_type` = '%s', t3.`name`, IF(`reference_type` = '%s', t4.`name`, t5.`name`)) AS `name`",
			common.ConstReferenceTypeComponentGroup, common.ConstReferenceTypeApiCase,
		),
		fmt.Sprintf(
			"IF(`reference_type` = '%s', t3.`description`, IF(`reference_type` = '%s', t4.`description`, t5.`description`)) AS `description`",
			common.ConstReferenceTypeComponentGroup, common.ConstReferenceTypeApiCase,
		),
		fmt.Sprintf(
			"IF(`reference_type` = '%s', t3.`priority`, IF(`reference_type` = '%s', t4.`priority`, t5.`priority`)) AS `priority`",
			common.ConstReferenceTypeComponentGroup, common.ConstReferenceTypeApiCase,
		),
		fmt.Sprintf(
			"IF(`reference_type` = '%s', t3.`tags`, IF(`reference_type` = '%s', t4.`tags`, t5.`tags`)) AS `tags`",
			common.ConstReferenceTypeComponentGroup, common.ConstReferenceTypeApiCase,
		),
		fmt.Sprintf(
			"IF(`reference_type` = '%s', %d, IF(`reference_type` = '%s', t4.`state`, t5.`state`)) AS `state`",
			common.ConstReferenceTypeComponentGroup, constants.EnableStatus, common.ConstReferenceTypeApiCase,
		),
		fmt.Sprintf(
			"IF(`reference_type` = '%s', t3.`maintained_by`, IF(`reference_type` = '%s', t4.`maintained_by`, t5.`maintained_by`)) AS `maintained_by`",
			common.ConstReferenceTypeComponentGroup, common.ConstReferenceTypeApiCase,
		),
		fmt.Sprintf(
			"IF(`reference_type` = '%s', t3.`created_by`, IF(`reference_type` = '%s', t4.`created_by`, t5.`created_by`)) AS `created_by`",
			common.ConstReferenceTypeComponentGroup, common.ConstReferenceTypeApiCase,
		),
		fmt.Sprintf(
			"IF(`reference_type` = '%s', t3.`updated_by`, IF(`reference_type` = '%s', t4.`updated_by`, t5.`updated_by`)) AS `updated_by`",
			common.ConstReferenceTypeComponentGroup, common.ConstReferenceTypeApiCase,
		),
		fmt.Sprintf(
			"IF(`reference_type` = '%s', t3.`created_at`, IF(`reference_type` = '%s', t4.`created_at`, t5.`created_at`)) AS `created_at`",
			common.ConstReferenceTypeComponentGroup, common.ConstReferenceTypeApiCase,
		),
		fmt.Sprintf(
			"IF(`reference_type` = '%s', t3.`updated_at`, IF(`reference_type` = '%s', t4.`updated_at`, t5.`updated_at`)) AS `updated_at`",
			common.ConstReferenceTypeComponentGroup, common.ConstReferenceTypeApiCase,
		),
	}
	tmp := squirrel.Select(fields...).From(m.table+" AS t1").
		JoinClause(sub.Prefix("INNER JOIN (").Suffix(") AS t2 ON t1.`project_id` = t2.`project_id` AND t1.`component_group_id` = t2.`component_group_id` AND t1.`deleted` = t2.`deleted`")).
		// InnerJoin(componentGroupReferenceRelationshipTableName+" AS t2 ON t1.`project_id` = t2.`project_id` AND t1.`component_group_id` = t2.`component_group_id` AND t1.`deleted` = t2.`deleted`").
		LeftJoin(
			fmt.Sprintf(
				"%s AS t3 ON t2.`reference_type` = '%s' AND t1.`project_id` = t3.`project_id` AND t2.`reference_id` = t3.`component_group_id` AND t2.`reference_version` = t3.`version` AND t1.`latest` = t3.`latest` AND t1.`deleted` = t3.`deleted`",
				m.table, common.ConstReferenceTypeComponentGroup,
			),
		).
		LeftJoin(
			fmt.Sprintf(
				"%s AS t4 ON t2.`reference_type` = '%s' AND t1.`project_id` = t4.`project_id` AND t2.`reference_id` = t4.`case_id` AND t2.`reference_version` = t4.`version` AND t1.`latest` = t4.`latest` AND t1.`deleted` = t4.`deleted`",
				apiCaseTableName, common.ConstReferenceTypeApiCase,
			),
		).
		LeftJoin(
			fmt.Sprintf(
				"%s AS t5 ON t2.`reference_type` = '%s' AND t1.`project_id` = t5.`project_id` AND t2.`reference_id` = t5.`case_id` AND t2.`reference_version` = t5.`version` AND t1.`latest` = t5.`latest` AND t1.`deleted` = t5.`deleted`",
				interfaceCaseTableName, common.ConstReferenceTypeInterfaceCase,
			),
		).
		Where(
			"t1.`project_id` = ? AND t1.`component_group_id` = ? AND t1.`latest` = ? AND t1.`deleted` = ? AND (t3.`id` IS NOT NULL OR t4.`id` IS NOT NULL OR t5.`id` IS NOT NULL)",
			req.ProjectId, req.ComponentGroupId, constants.IsLatestVersion, constants.NotDeleted,
		)

	rm := VirtualComponentGroupReferenceModel

	alias := "t"
	sb := sqlbuilder.SearchOptionsWithAlias(
		squirrel.Select(utils.AddTableNameToFields(alias, rm.Fields())...).FromSelect(tmp, alias),
		alias,
		sqlbuilder.WithCondition(rm, req.Condition),
		sqlbuilder.WithPagination(rm, req.Pagination),
		sqlbuilder.WithSort(rm, req.Sort),
	)
	scb := sqlbuilder.SearchOptionsWithAlias(
		squirrel.Select("COUNT(*)").FromSelect(tmp, alias),
		alias,
		sqlbuilder.WithCondition(rm, req.Condition),
	)

	return searchComponentGroupReferenceSelectBuilder{sb}, searchComponentGroupReferenceCountBuilder{scb}
}

func (m *customComponentGroupModel) FindCountComponentGroupReference(
	ctx context.Context, countBuilder searchComponentGroupReferenceCountBuilder,
) (int64, error) {
	return m.FindCount(ctx, countBuilder.SelectBuilder)
}

func (m *customComponentGroupModel) FindComponentGroupReference(
	ctx context.Context, selectBuilder searchComponentGroupReferenceSelectBuilder,
) ([]*SearchComponentGroupReferenceItem, error) {
	var resp []*SearchComponentGroupReferenceItem

	err := utils.FindRows(
		ctx, m.conn, selectBuilder.SelectBuilder, func() any {
			return &resp
		},
	)

	return resp, err
}
