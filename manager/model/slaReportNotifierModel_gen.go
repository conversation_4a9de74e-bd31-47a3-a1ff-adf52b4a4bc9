// Code generated by goctl. DO NOT EDIT.
// versions:
//  goctl version: 1.8.3

package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlc"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
)

var (
	slaReportNotifierTableName           = "`sla_report_notifier`"
	slaReportNotifierFieldNames          = builder.RawFieldNames(&SlaReportNotifier{})
	slaReportNotifierRows                = strings.Join(slaReportNotifierFieldNames, ",")
	slaReportNotifierRowsExpectAutoSet   = strings.Join(stringx.Remove(slaReportNotifierFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), ",")
	slaReportNotifierRowsWithPlaceHolder = strings.Join(stringx.Remove(slaReportNotifierFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), "=?,") + "=?"

	cacheManagerSlaReportNotifierIdPrefix               = "cache:manager:slaReportNotifier:id:"
	cacheManagerSlaReportNotifierProjectIdAccountPrefix = "cache:manager:slaReportNotifier:projectId:account:"
)

type (
	slaReportNotifierModel interface {
		Insert(ctx context.Context, session sqlx.Session, data *SlaReportNotifier) (sql.Result, error)
		FindOne(ctx context.Context, id int64) (*SlaReportNotifier, error)
		FindOneByProjectIdAccount(ctx context.Context, projectId string, account string) (*SlaReportNotifier, error)
		Update(ctx context.Context, session sqlx.Session, data *SlaReportNotifier) (sql.Result, error)
		Delete(ctx context.Context, session sqlx.Session, id int64) error
		LogicDelete(ctx context.Context, session sqlx.Session, id int64) error
	}

	defaultSlaReportNotifierModel struct {
		sqlc.CachedConn
		table string
	}

	SlaReportNotifier struct {
		Id           int64          `db:"id"`             // 自增ID
		ProjectId    string         `db:"project_id"`     // 项目ID
		Account      string         `db:"account"`        // 用户名（工号）
		Fullname     string         `db:"fullname"`       // 姓名
		FullDeptName sql.NullString `db:"full_dept_name"` // 完整部门名称
		Email        sql.NullString `db:"email"`          // 邮箱
		LarkUserId   sql.NullString `db:"lark_user_id"`   // 飞书用户ID
		Deleted      int64          `db:"deleted"`        // 逻辑删除标识（未删除、已删除）
		CreatedBy    string         `db:"created_by"`     // 创建者的用户ID
		UpdatedBy    string         `db:"updated_by"`     // 最近一次更新者的用户ID
		DeletedBy    sql.NullString `db:"deleted_by"`     // 删除者的用户ID
		CreatedAt    time.Time      `db:"created_at"`     // 创建时间
		UpdatedAt    time.Time      `db:"updated_at"`     // 更新时间
		DeletedAt    sql.NullTime   `db:"deleted_at"`     // 删除时间
	}
)

func newSlaReportNotifierModel(conn sqlx.SqlConn, c cache.CacheConf, opts ...cache.Option) *defaultSlaReportNotifierModel {
	return &defaultSlaReportNotifierModel{
		CachedConn: sqlc.NewConn(conn, c, opts...),
		table:      "`sla_report_notifier`",
	}
}

func (m *defaultSlaReportNotifierModel) Delete(ctx context.Context, session sqlx.Session, id int64) error {
	data, err := m.FindOne(ctx, id)
	if err != nil {
		return err
	}

	managerSlaReportNotifierIdKey := fmt.Sprintf("%s%v", cacheManagerSlaReportNotifierIdPrefix, id)
	managerSlaReportNotifierProjectIdAccountKey := fmt.Sprintf("%s%v:%v", cacheManagerSlaReportNotifierProjectIdAccountPrefix, data.ProjectId, data.Account)
	_, err = m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("delete from %s where `id` = ?", m.table)
		if session != nil {
			return session.ExecCtx(ctx, query, id)
		}
		return conn.ExecCtx(ctx, query, id)
	}, managerSlaReportNotifierIdKey, managerSlaReportNotifierProjectIdAccountKey)
	return err
}

func (m *defaultSlaReportNotifierModel) LogicDelete(ctx context.Context, session sqlx.Session, id int64) error {
	data, err := m.FindOne(ctx, id)
	if err != nil {
		return err
	}

	managerSlaReportNotifierIdKey := fmt.Sprintf("%s%v", cacheManagerSlaReportNotifierIdPrefix, id)
	managerSlaReportNotifierProjectIdAccountKey := fmt.Sprintf("%s%v:%v", cacheManagerSlaReportNotifierProjectIdAccountPrefix, data.ProjectId, data.Account)
	_, err = m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set `deleted` = ?, `deleted_at` = ? where `id` = ?", m.table)
		if session != nil {
			return session.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
		}
		return conn.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
	}, managerSlaReportNotifierIdKey, managerSlaReportNotifierProjectIdAccountKey)
	return err
}

func (m *defaultSlaReportNotifierModel) FindOne(ctx context.Context, id int64) (*SlaReportNotifier, error) {
	managerSlaReportNotifierIdKey := fmt.Sprintf("%s%v", cacheManagerSlaReportNotifierIdPrefix, id)
	var resp SlaReportNotifier
	err := m.QueryRowCtx(ctx, &resp, managerSlaReportNotifierIdKey, func(ctx context.Context, conn sqlx.SqlConn, v any) error {
		query := fmt.Sprintf("select %s from %s where `id` = ? and `deleted` = ? limit 1", slaReportNotifierRows, m.table)
		return conn.QueryRowCtx(ctx, v, query, id, constants.NotDeleted)
	})
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultSlaReportNotifierModel) FindOneByProjectIdAccount(ctx context.Context, projectId string, account string) (*SlaReportNotifier, error) {
	managerSlaReportNotifierProjectIdAccountKey := fmt.Sprintf("%s%v:%v", cacheManagerSlaReportNotifierProjectIdAccountPrefix, projectId, account)
	var resp SlaReportNotifier
	err := m.QueryRowIndexCtx(ctx, &resp, managerSlaReportNotifierProjectIdAccountKey, m.formatPrimary, func(ctx context.Context, conn sqlx.SqlConn, v any) (i any, e error) {
		query := fmt.Sprintf("select %s from %s where `project_id` = ? and `account` = ? and `deleted` = ? limit 1", slaReportNotifierRows, m.table)
		if err := conn.QueryRowCtx(ctx, &resp, query, projectId, account, constants.NotDeleted); err != nil {
			return nil, err
		}
		return resp.Id, nil
	}, m.queryPrimary)
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultSlaReportNotifierModel) Insert(ctx context.Context, session sqlx.Session, data *SlaReportNotifier) (sql.Result, error) {
	managerSlaReportNotifierIdKey := fmt.Sprintf("%s%v", cacheManagerSlaReportNotifierIdPrefix, data.Id)
	managerSlaReportNotifierProjectIdAccountKey := fmt.Sprintf("%s%v:%v", cacheManagerSlaReportNotifierProjectIdAccountPrefix, data.ProjectId, data.Account)

	return m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", m.table, slaReportNotifierRowsExpectAutoSet)
		if session != nil {
			return session.ExecCtx(ctx, query, data.ProjectId, data.Account, data.Fullname, data.FullDeptName, data.Email, data.LarkUserId, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy)
		}
		return conn.ExecCtx(ctx, query, data.ProjectId, data.Account, data.Fullname, data.FullDeptName, data.Email, data.LarkUserId, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy)
	}, managerSlaReportNotifierIdKey, managerSlaReportNotifierProjectIdAccountKey)
}

func (m *defaultSlaReportNotifierModel) Update(ctx context.Context, session sqlx.Session, newData *SlaReportNotifier) (sql.Result, error) {
	data, err := m.FindOne(ctx, newData.Id)
	if err != nil {
		return nil, err
	}

	managerSlaReportNotifierIdKey := fmt.Sprintf("%s%v", cacheManagerSlaReportNotifierIdPrefix, data.Id)
	managerSlaReportNotifierProjectIdAccountKey := fmt.Sprintf("%s%v:%v", cacheManagerSlaReportNotifierProjectIdAccountPrefix, data.ProjectId, data.Account)

	return m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, slaReportNotifierRowsWithPlaceHolder)
		if session != nil {
			return session.ExecCtx(ctx, query, newData.ProjectId, newData.Account, newData.Fullname, newData.FullDeptName, newData.Email, newData.LarkUserId, newData.Deleted, newData.CreatedBy, newData.UpdatedBy, newData.DeletedBy, newData.Id)
		}
		return conn.ExecCtx(ctx, query, newData.ProjectId, newData.Account, newData.Fullname, newData.FullDeptName, newData.Email, newData.LarkUserId, newData.Deleted, newData.CreatedBy, newData.UpdatedBy, newData.DeletedBy, newData.Id)
	}, managerSlaReportNotifierIdKey, managerSlaReportNotifierProjectIdAccountKey)
}

func (m *defaultSlaReportNotifierModel) formatPrimary(primary any) string {
	return fmt.Sprintf("%s%v", cacheManagerSlaReportNotifierIdPrefix, primary)
}

func (m *defaultSlaReportNotifierModel) queryPrimary(ctx context.Context, conn sqlx.SqlConn, v, primary any) error {
	query := fmt.Sprintf("select %s from %s where `id` = ? limit 1", slaReportNotifierRows, m.table)
	return conn.QueryRowCtx(ctx, v, query, primary)
}

func (m *defaultSlaReportNotifierModel) tableName() string {
	return m.table
}
