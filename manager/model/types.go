package model

import (
	"context"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sqlbuilder"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
)

type CountByCategoryIdFunc func(projectId, categoryId string, drillDown bool) (int64, error)

type CountByCategoryIdModel interface {
	FindCountByCategoryId(ctx context.Context, cm CategoryModel, cond FindCountByCategoryCondition) (int64, error)
}

type ProjectDevicePlan interface {
	GetDevices() []string
	SetDevices([]string)
}

//type ApiExecutionData interface {
//	// Validate 验证API执行数据是否可被执行
//	Validate() bool
//}

type BaseSearchReq struct {
	ProjectID  string                 `json:"project_id"`
	Condition  sqlbuilder.Condition   `json:"condition"`
	Pagination sqlbuilder.Pagination  `json:"pagination"`
	Sort       []sqlbuilder.SortField `json:"sort"`
}

type SearchCategoryTreeCondition struct {
	ProjectId       string               `json:"project_id"`
	Type            string               `json:"type"`
	SearchCondition sqlbuilder.Condition `json:"search_condition"`
}

type GetCategoryTreeCondition struct {
	ProjectId       string               `json:"project_id"`
	Type            string               `json:"type"`
	CategoryId      string               `json:"category_id"`
	Depth           uint32               `json:"depth"`
	OnlyDirectory   bool                 `json:"only_directory"`
	ExcludeSelf     bool                 `json:"exclude_self"`
	SearchCondition sqlbuilder.Condition `json:"search_condition"`
}

type FindCountByCategoryCondition struct {
	ProjectId  string `json:"project_id"`
	CategoryId string `json:"category_id"`
	DrillDown  bool   `json:"drill_down"`
}

type SearchInterfaceDocumentReferenceReq struct {
	ProjectId  string                 `json:"project_id"`
	DocumentId string                 `json:"document_id"`
	Condition  sqlbuilder.Condition   `json:"condition"`
	Pagination sqlbuilder.Pagination  `json:"pagination"`
	Sort       []sqlbuilder.SortField `json:"sort"`
}

type SearchInterfaceCaseReferenceReq struct {
	ProjectId  string                 `json:"project_id"`
	DocumentId string                 `json:"document_id"`
	CaseId     string                 `json:"case_id"`
	Condition  sqlbuilder.Condition   `json:"condition"`
	Pagination sqlbuilder.Pagination  `json:"pagination"`
	Sort       []sqlbuilder.SortField `json:"sort"`
}

type SearchComponentGroupReferenceReq struct {
	ProjectId        string                 `json:"project_id"`
	ComponentGroupId string                 `json:"component_group_id"`
	Condition        sqlbuilder.Condition   `json:"condition"`
	Pagination       sqlbuilder.Pagination  `json:"pagination"`
	Sort             []sqlbuilder.SortField `json:"sort"`
}

type SearchApiCaseReferenceReq struct {
	ProjectId  string                 `json:"project_id"`
	CaseId     string                 `json:"case_id"`
	Condition  sqlbuilder.Condition   `json:"condition"`
	Pagination sqlbuilder.Pagination  `json:"pagination"`
	Sort       []sqlbuilder.SortField `json:"sort"`
}

type SearchNotReleasedCaseReq struct {
	ProjectId  string                 `json:"project_id"`
	Condition  sqlbuilder.Condition   `json:"condition"`
	Pagination sqlbuilder.Pagination  `json:"pagination"`
	Sort       []sqlbuilder.SortField `json:"sort"`
}

type SearchFailLogCaseReq struct {
	ProjectId  string                 `json:"project_id"`
	Condition  sqlbuilder.Condition   `json:"condition"`
	Pagination sqlbuilder.Pagination  `json:"pagination"`
	Sort       []sqlbuilder.SortField `json:"sort"`
}

type SearchApiCaseInApiSuiteReq struct {
	// ApiCaseModel          ApiCaseModel
	// ApiCaseReferenceModel ApiCaseReferenceRelationshipModel

	ProjectId  string                 `json:"project_id"`
	SuiteId    string                 `json:"suite_id"`
	Condition  sqlbuilder.Condition   `json:"condition"`
	Pagination sqlbuilder.Pagination  `json:"pagination"`
	Sort       []sqlbuilder.SortField `json:"sort"`
}

type SearchApiSuiteReferenceReq struct {
	ProjectId  string                 `json:"project_id"`
	SuiteId    string                 `json:"suite_id"`
	Condition  sqlbuilder.Condition   `json:"condition"`
	Pagination sqlbuilder.Pagination  `json:"pagination"`
	Sort       []sqlbuilder.SortField `json:"sort"`
}

type SearchCaseInApiSuiteReq struct {
	ProjectId  string                 `json:"project_id"`
	SuiteId    string                 `json:"suite_id"`
	Condition  sqlbuilder.Condition   `json:"condition"`
	Pagination sqlbuilder.Pagination  `json:"pagination"`
	Sort       []sqlbuilder.SortField `json:"sort"`
}

type SearchServiceCaseNotInApiSuiteReq struct {
	ProjectId        string                 `json:"project_id"`
	SuiteId          string                 `json:"suite_id"`
	ApiCaseIds       []string               `json:"api_case_ids"`
	InterfaceCaseIds []string               `json:"interface_case_ids"`
	Condition        sqlbuilder.Condition   `json:"condition"`
	Pagination       sqlbuilder.Pagination  `json:"pagination"`
	Sort             []sqlbuilder.SortField `json:"sort"`
}

type SearchApiPlanReq struct {
	// ApiPlanReferenceModel ApiPlanReferenceRelationshipModel

	ProjectId  string                 `json:"project_id"`
	Condition  sqlbuilder.Condition   `json:"condition"`
	Pagination sqlbuilder.Pagination  `json:"pagination"`
	Sort       []sqlbuilder.SortField `json:"sort"`
}

type SearchSuiteInApiPlanReq struct {
	ApiSuiteModel          ApiSuiteModel
	InterfaceDocumentModel InterfaceDocumentModel
	// ApiPlanReferenceModel  ApiPlanReferenceRelationshipModel

	ProjectId  string                 `json:"project_id"`
	PlanId     string                 `json:"plan_id"`
	SuiteType  common.ReferenceType   `json:"suite_type"`
	Condition  sqlbuilder.Condition   `json:"condition"`
	Pagination sqlbuilder.Pagination  `json:"pagination"`
	Sort       []sqlbuilder.SortField `json:"sort"`
}

type SearchSuiteNotInApiPlanReq struct {
	CategoryModel          CategoryModel
	CategoryTreeModel      CategoryTreeModel
	ApiSuiteModel          ApiSuiteModel
	InterfaceDocumentModel InterfaceDocumentModel
	// ApiPlanReferenceModel  ApiPlanReferenceRelationshipModel

	ProjectId  string               `json:"project_id"`
	PlanId     string               `json:"plan_id"`
	SuiteType  common.ReferenceType `json:"suite_type"`
	CategoryId string               `json:"category_id"`

	DrillDown bool `json:"drill_down"`

	Condition  sqlbuilder.Condition   `json:"condition"`
	Pagination sqlbuilder.Pagination  `json:"pagination"`
	Sort       []sqlbuilder.SortField `json:"sort"`
}

type SearchCaseInApiPlanReq struct {
	// ApiSuiteModel          ApiSuiteModel
	ApiCaseModel ApiCaseModel
	// InterfaceDocumentModel InterfaceDocumentModel
	InterfaceCaseModel InterfaceCaseModel
	// ApiPlanReferenceModel  ApiPlanReferenceRelationshipModel
	// ApiCaseReferenceModel  ApiCaseReferenceRelationshipModel

	ProjectId  string                 `json:"project_id"`
	PlanId     string                 `json:"plan_id"`
	SuiteType  common.ReferenceType   `json:"suite_type"`
	SuiteId    string                 `json:"suite_id"`
	Condition  sqlbuilder.Condition   `json:"condition"`
	Pagination sqlbuilder.Pagination  `json:"pagination"`
	Sort       []sqlbuilder.SortField `json:"sort"`
}

type FindGeneralConfigByPlanIdReq struct {
	// GeneralConfigModel GeneralConfigurationModel

	ProjectId string `json:"project_id"`
	PlanId    string `json:"plan_id"`
}

type FindAccountConfigByPlanIdReq struct {
	// AccountConfigModel          AccountConfigurationModel
	// AccountConfigReferenceModel AccountConfigurationReferenceRelationshipModel

	ProjectId string `json:"project_id"`
	PlanId    string `json:"plan_id"`
}

type FindSearchFieldBySceneTypeReq struct {
	ProjectId string `json:"project_id"`
	SceneType string `json:"scene_type"`
}

type FindSearchConditionByFieldIdReq struct {
	ProjectId string `json:"project_id"`
	FieldId   string `json:"field_id"`
}

type AdvancedSearchSuiteNotInApiPlanReq struct {
	ProjectId  string                 `json:"project_id"`
	PlanId     string                 `json:"plan_id"`
	SuiteType  common.ReferenceType   `json:"suite_type"`
	Condition  sqlbuilder.Condition   `json:"condition"`
	Pagination sqlbuilder.Pagination  `json:"pagination"`
	Sort       []sqlbuilder.SortField `json:"sort"`
}

type CreateNotifyReq struct {
	ProjectId     string           `json:"project_id"`
	PlanId        string           `json:"plan_id"`
	NotifyMode    string           `json:"notify_mode"`
	NotifyType    string           `json:"notify_type"`
	ReceiverInfos []*ReceiverItems `json:"receiver_infos"`
	CreatedBy     string           `json:"created_by"`
	UpdatedBy     string           `json:"updated_by"`
}

type ReceiverItems struct {
	NotifyId     string `json:"notify_id"`
	ReceiverName string `json:"receiver_name"`
	Receiver     string `json:"receiver"`
}

type SearchNotifyReq struct {
	ProjectId  string                 `json:"project_id"`
	PlanId     string                 `json:"plan_id"`
	Condition  sqlbuilder.Condition   `json:"condition"`
	Pagination sqlbuilder.Pagination  `json:"pagination"`
	Sort       []sqlbuilder.SortField `json:"sort"`
}

type SearchUiPlanReq struct {
	ProjectId  string                 `json:"project_id"`
	Condition  sqlbuilder.Condition   `json:"condition"`
	Pagination sqlbuilder.Pagination  `json:"pagination"`
	Sort       []sqlbuilder.SortField `json:"sort"`
}

type SearchGitConfigurationReq struct {
	ProjectId  string                 `json:"project_id"`
	Condition  sqlbuilder.Condition   `json:"condition"`
	Pagination sqlbuilder.Pagination  `json:"pagination"`
	Sort       []sqlbuilder.SortField `json:"sort"`
}

type UiCasePath struct {
	Path string
}

type SearchCaseInUIPlanReq struct {
	GitProjectTreeModel GitProjectTreeModel
	// UIPlanReferenceRelationshipModel UiPlanReferenceRelationshipModel

	ProjectID  string                 `json:"project_id"`
	PlanID     string                 `json:"plan_id"`
	Path       string                 `json:"path"`
	Condition  sqlbuilder.Condition   `json:"condition"`
	Pagination sqlbuilder.Pagination  `json:"pagination"`
	Sort       []sqlbuilder.SortField `json:"sort"`
}

type SearchCaseNotInUIPlanReq struct {
	GitProjectTreeModel GitProjectTreeModel

	ProjectID  string                 `json:"project_id"`
	PlanID     string                 `json:"plan_id"`
	Path       string                 `json:"path"`
	Condition  sqlbuilder.Condition   `json:"condition"`
	Pagination sqlbuilder.Pagination  `json:"pagination"`
	Sort       []sqlbuilder.SortField `json:"sort"`
}

type SearchReviewRecordReq struct {
	// ComponentGroupModel ComponentGroupModel
	// ApiCaseModel        ApiCaseModel
	// InterfaceCaseModel  InterfaceCaseModel

	ProjectID  string                 `json:"project_id"`
	Condition  sqlbuilder.Condition   `json:"condition"`
	Pagination sqlbuilder.Pagination  `json:"pagination"`
	Sort       []sqlbuilder.SortField `json:"sort"`
}

type SearchProtobufConfigurationReq struct {
	BaseSearchReq
}

type SearchPerfDataReq struct {
	BaseSearchReq
}

type SearchPerfPlanReq struct {
	BaseSearchReq
}

type SearchCaseInPerfPlanReq struct {
	BaseSearchReq

	PlanID string `json:"plan_id"`

	PerfCaseModel PerfCaseModel
}

type SearchPerfCaseV2Req struct {
	BaseSearchReq

	CategoryID string `json:"category_id"`
	DrillDown  bool   `json:"drill_down"`

	CategoryModel CategoryModel
}

type SearchPerfPlanV2Req struct {
	BaseSearchReq

	CategoryID string `json:"category_id"`
	DrillDown  bool   `json:"drill_down"`

	CategoryModel CategoryModel
}

type SearchCaseInPerfPlanV2Req struct {
	BaseSearchReq

	PlanID string `json:"plan_id"`

	PerfCaseV2Model PerfCaseV2Model
}

type SearchPerfStopRuleReq struct {
	BaseSearchReq
}

type SearchRuleInPerfPlanV2Req struct {
	BaseSearchReq

	PlanID string `json:"plan_id"`

	PerfStopRuleModel PerfStopRuleModel
}

type SearchProtobufConfigInPerfPlanV2Req struct {
	BaseSearchReq

	PlanID string `json:"plan_id"`

	ProtobufConfigModel ProtobufConfigurationModel
}

type SearchPerfLarkChatReq struct {
	BaseSearchReq
}

type UpdatePerfLarkChatReq struct {
	ChatID      string `json:"chat_id"`
	Name        string `json:"name"`
	Avatar      string `json:"avatar"`
	Description string `json:"description"`
	External    bool   `json:"external"`
}

type SearchProjectDeviceReq struct {
	BaseSearchReq
}

type SearchProjectDeviceReferenceReq struct {
	BaseSearchReq

	UDID string `json:"udid"`
}

type SearchStabilityPlanReq struct {
	BaseSearchReq

	CategoryID string `json:"category_id"`
	DrillDown  bool   `json:"drill_down"`

	CategoryModel CategoryModel
}

type SearchLarkChatReq struct {
	BaseSearchReq

	Type string `json:"type"`
}

type UpdateLarkChatReq struct {
	ChatID      string `json:"chat_id"`
	Name        string `json:"name"`
	Avatar      string `json:"avatar"`
	Description string `json:"description"`
	External    bool   `json:"external"`
}

type SearchPromptConfigurationReq struct {
	BaseSearchReq

	Purpose int64 `json:"purpose"`
}

type SearchPromptConfigurationReferenceReq struct {
	BaseSearchReq

	ConfigID string `json:"config_id"`
}

type SearchApplicationConfigurationReq struct {
	BaseSearchReq
}

type SearchApplicationConfigurationReferenceReq struct {
	BaseSearchReq

	ConfigID string `json:"config_id"`
}

type SearchUIAgentComponentReq struct {
	BaseSearchReq

	CategoryID string `json:"category_id"`
	DrillDown  bool   `json:"drill_down"`

	CategoryModel CategoryModel
}
