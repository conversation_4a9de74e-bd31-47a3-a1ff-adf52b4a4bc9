package model

import (
	"context"
	"database/sql"
	"fmt"
	"time"

	"github.com/Masterminds/squirrel"
	"github.com/zeromicro/go-zero/core/fx"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sqlbuilder"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/types"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/userinfo"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/utils"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
)

var (
	_ CategoryModel = (*customCategoryModel)(nil)

	categoryInsertFields = stringx.Remove(
		categoryFieldNames, "`id`", "`create_time`", "`update_time`", "`deleted_by`", "`created_at`", "`updated_at`",
		"`deleted_at`",
	)
)

type (
	// CategoryModel is an interface to be customized, add more methods here,
	// and implement the added methods in customCategoryModel.
	CategoryModel interface {
		categoryModel
		types.DBModel

		Trans(ctx context.Context, fn func(context context.Context, session sqlx.Session) error) error
		InsertBuilder(data *Category) squirrel.InsertBuilder
		UpdateBuilder(data *Category) squirrel.UpdateBuilder
		SelectBuilder() squirrel.SelectBuilder
		SelectCountBuilder() squirrel.SelectBuilder
		FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error)
		FindNoCacheByQuery(ctx context.Context, builder squirrel.SelectBuilder) ([]*Category, error)

		FindAncestralCategories(ctx context.Context, cond GetCategoryTreeCondition) ([]*Category, error)
		FindDescendantCategories(ctx context.Context, cond GetCategoryTreeCondition) ([]*Category, error)

		FindParentCategory(ctx context.Context, cond GetCategoryTreeCondition) (*Category, error)
		FindChildrenCategories(ctx context.Context, cond GetCategoryTreeCondition) ([]*Category, error)

		FindOneByProjectIdTypeName(ctx context.Context, projectId, tp, name string) (*Category, error)
		FindOneByNodeId(ctx context.Context, projectId, tp, nodeId string) (*Category, error)

		SearchTreeNodes(ctx context.Context, cond SearchCategoryTreeCondition) ([]*CategoryTreeNode, error)
		GetTreeNodes(ctx context.Context, cond GetCategoryTreeCondition) ([]*CategoryTreeNode, error)

		RemoveDescendantCategories(
			ctx context.Context, session sqlx.Session, projectId, tp, categoryId string,
		) (sql.Result, error)
		UpdateDescendantRootType(
			ctx context.Context, session sqlx.Session, projectId, tp, categoryId, rootType string,
		) (sql.Result, error)
		RemoveByNodeId(ctx context.Context, session sqlx.Session, projectId, tp, nodeId string) (sql.Result, error)

		FindDescendantCategoriesSqlBuilder(cond GetCategoryTreeCondition) squirrel.SelectBuilder
	}

	customCategoryModel struct {
		*defaultCategoryModel
	}
)

// NewCategoryModel returns a model for the database table.
func NewCategoryModel(conn sqlx.SqlConn, c cache.CacheConf) CategoryModel {
	return &customCategoryModel{
		defaultCategoryModel: newCategoryModel(conn, c),
	}
}

func (m *customCategoryModel) Table() string {
	return m.table
}

func (m *customCategoryModel) Fields() []string {
	return categoryFieldNames
}

func (m *customCategoryModel) Trans(
	ctx context.Context, fn func(context context.Context, session sqlx.Session) error,
) error {
	return m.TransactCtx(ctx, fn)
}

func (m *customCategoryModel) InsertBuilder(data *Category) squirrel.InsertBuilder {
	return squirrel.Insert(m.table).Columns(categoryInsertFields...).Values(
		data.ProjectId, data.Type, data.CategoryId, data.CategoryType, data.RootType, data.NodeType, data.NodeId,
		data.Name, data.Description, data.Builtin, data.Deleted, data.CreatedBy, data.UpdatedBy,
	)
}

func (m *customCategoryModel) UpdateBuilder(data *Category) squirrel.UpdateBuilder {
	eq := squirrel.Eq{
		"`name`":        data.Name,
		"`description`": data.Description,
		"`deleted`":     data.Deleted,
		"`updated_by`":  data.UpdatedBy,
		"`deleted_by`":  data.DeletedBy,
		"`deleted_at`":  data.DeletedAt,
	}
	return squirrel.Update(m.table).SetMap(eq).Where("`id` = ?", data.Id)
}

func (m *customCategoryModel) SelectBuilder() squirrel.SelectBuilder {
	return squirrel.Select(categoryFieldNames...).Where("`deleted` = ?", constants.NotDeleted).From(m.table)
}

func (m *customCategoryModel) SelectCountBuilder() squirrel.SelectBuilder {
	return squirrel.Select("count(*)").Where("`deleted` = ?", constants.NotDeleted).From(m.table)
}

func (m *customCategoryModel) FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error) {
	query, values, err := countBuilder.ToSql()
	if err != nil {
		return 0, err
	}

	var resp int64
	err = m.QueryRowNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return 0, err
	}
}

func (m *customCategoryModel) FindNoCacheByQuery(ctx context.Context, builder squirrel.SelectBuilder) (
	[]*Category, error,
) {
	query, values, err := builder.ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*Category
	err = m.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

func (m *customCategoryModel) FindAncestralCategories(ctx context.Context, cond GetCategoryTreeCondition) (
	[]*Category, error,
) {
	/*
		SQL:
		SELECT t1.*
		FROM `category` AS t1
			INNER JOIN `category_tree` AS t2
				ON t1.`project_id` = t2.`project_id` AND t1.`category_id` = t2.`ancestor`
		WHERE t1.`project_id` = ?
		  AND t1.`type` = ?
		  AND t2.`descendant` = ?
		  AND t1.`deleted` = ?
		  AND t2.`deleted` = ?;
	*/
	sb := squirrel.Select(utils.AddTableNameToFields("t1", categoryFieldNames)...).
		From(m.table+" AS t1").
		InnerJoin(categoryTreeTableName+" AS t2 ON t1.`project_id` = t2.`project_id` AND t1.`category_id` = t2.`ancestor`").
		Where(
			"t1.`project_id` = ? AND t1.`type` = ? AND t2.`descendant` = ? AND t1.`deleted` = ? AND t2.`deleted` = ?",
			cond.ProjectId, cond.Type, cond.CategoryId, constants.NotDeleted, constants.NotDeleted,
		)
	if cond.Depth > 0 {
		sb = sb.Where("t2.`depth` <= ?", cond.Depth)
	}
	if cond.ExcludeSelf {
		sb = sb.Where("t2.`depth` > 0")
	} else {
		sb = sb.Where("t2.`depth` >= 0")
	}
	if cond.OnlyDirectory {
		sb = sb.Where("t1.`category_type` = ?", common.ConstCategoryTypeDirectory)
	}
	sb = sb.OrderBy("t2.`depth` DESC, t2.`index` ASC, t1.`created_at` ASC")

	return m.FindNoCacheByQuery(ctx, sb)
}

func (m *customCategoryModel) FindDescendantCategories(ctx context.Context, cond GetCategoryTreeCondition) (
	[]*Category, error,
) {
	/*
		SQL:
		SELECT t1.*
		FROM `category` AS t1
		    INNER JOIN `category_tree` AS t2
		        ON t1.`project_id` = t2.`project_id` AND t1.`category_id` = t2.`descendant`
		WHERE t1.`project_id` = ?
		  AND t1.`type` = ?
		  AND t2.`ancestor` = ?
		  AND t1.`deleted` = ?
		  AND t2.`deleted` = ?;
	*/

	return m.FindNoCacheByQuery(ctx, m.FindDescendantCategoriesSqlBuilder(cond))
}

func (m *customCategoryModel) FindParentCategory(ctx context.Context, cond GetCategoryTreeCondition) (
	*Category, error,
) {
	c := GetCategoryTreeCondition{
		ProjectId:     cond.ProjectId,
		Type:          cond.Type,
		CategoryId:    cond.CategoryId,
		Depth:         1,
		OnlyDirectory: false,
		ExcludeSelf:   false,
	}
	r, err := m.FindAncestralCategories(ctx, c)
	if err != nil {
		return nil, err
	} else if len(r) == 0 {
		return nil, ErrNotFound
	}
	return r[0], nil
}

func (m *customCategoryModel) FindChildrenCategories(ctx context.Context, cond GetCategoryTreeCondition) (
	[]*Category, error,
) {
	c := GetCategoryTreeCondition{
		ProjectId:     cond.ProjectId,
		Type:          cond.Type,
		CategoryId:    cond.CategoryId,
		Depth:         1,
		OnlyDirectory: false,
		ExcludeSelf:   false,
	}
	return m.FindDescendantCategories(ctx, c)
}

func (m *customCategoryModel) FindOneByProjectIdTypeName(ctx context.Context, projectId, tp, name string) (
	*Category, error,
) {
	r, err := m.FindNoCacheByQuery(
		ctx, m.SelectBuilder().Where("`project_id` = ? AND `type` = ? AND `name` = ?", projectId, tp, name).Limit(1),
	)
	if err != nil {
		return nil, err
	} else if len(r) == 0 {
		return nil, ErrNotFound
	}
	return r[0], nil
}

func (m *customCategoryModel) FindOneByNodeId(ctx context.Context, projectId, tp, nodeId string) (*Category, error) {
	r, err := m.FindNoCacheByQuery(
		ctx,
		m.SelectBuilder().Where("`project_id` = ? AND `type` = ? AND `node_id` = ?", projectId, tp, nodeId).Limit(1),
	)
	if err != nil {
		return nil, err
	} else if len(r) == 0 {
		return nil, ErrNotFound
	}
	return r[0], nil
}

type CategoryTreeNode struct {
	Id           int64          `db:"id"`
	ProjectId    string         `db:"project_id"`    // 项目ID
	Type         string         `db:"type"`          // 类型，即所属的分类树类型（组件组、用例、集合）
	CategoryId   string         `db:"category_id"`   // 分类ID
	CategoryType string         `db:"category_type"` // 分类类型（目录、组件组）
	RootType     sql.NullString `db:"root_type"`     // 组件组根分类类型（业务组件、前后置组件）
	NodeType     sql.NullString `db:"node_type"`     // 节点类型（组件组：业务单请求组件、业务行为组组件、前置组件、后置组件，接口定义：文档、数据模型）
	NodeId       sql.NullString `db:"node_id"`       // 节点ID
	Name         string         `db:"name"`          // 分类名称
	Description  sql.NullString `db:"description"`   // 分类描述
	Builtin      int64          `db:"builtin"`       // 是否内建分类
	Deleted      int64          `db:"deleted"`       // 逻辑删除标识（未删除、已删除）
	CreatedBy    string         `db:"created_by"`    // 创建者的用户ID
	UpdatedBy    string         `db:"updated_by"`    // 最近一次更新者的用户ID
	DeletedBy    sql.NullString `db:"deleted_by"`    // 删除者的用户ID
	CreatedAt    time.Time      `db:"created_at"`    // 创建时间
	UpdatedAt    time.Time      `db:"updated_at"`    // 更新时间
	DeletedAt    sql.NullTime   `db:"deleted_at"`    // 删除时间
	ParentId     string         `db:"parent_id"`     // 父分类ID
	Depth        int64          `db:"depth"`         // 深度
	Index        int64          `db:"index"`         // 序号（同一深度下按序号升序排列）
	Amount       int64          `db:"amount"`        // 属于当前分类的叶子节点数量（注意：只计算直属孩子，不包括孙子等）
}

func (m *customCategoryModel) SearchTreeNodes(
	ctx context.Context, cond SearchCategoryTreeCondition,
) ([]*CategoryTreeNode, error) {
	/*
		SQL:
		SELECT t1.`id`,
		       t1.`project_id`,
		       t1.`type`,
		       t1.`category_id`,
		       t1.`category_type`,
		       t1.`root_type`,
		       t1.`node_type`,
		       t1.`node_id`,
		       t1.`name`,
		       t1.`description`,
		       t1.`builtin`,
		       t1.`deleted`,
		       t1.`created_by`,
		       t1.`updated_by`,
		       t1.`deleted_by`,
		       t1.`created_at`,
		       t1.`updated_at`,
		       t1.`deleted_at`,
		       t4.`ancestor` AS `parent_id`,
		       t4.`depth`,
		       t4.`index`,
		       0 AS `amount`
		FROM `category` AS t1
		         INNER JOIN (SELECT `id`,
		                            `project_id`,
		                            `type`,
		                            `category_id`,
		                            `category_type`,
		                            `root_type`,
		                            `node_type`,
		                            `node_id`,
		                            `name`,
		                            `description`,
		                            `builtin`,
		                            `deleted`,
		                            `created_by`,
		                            `updated_by`,
		                            `deleted_by`,
		                            `created_at`,
		                            `updated_at`,
		                            `deleted_at`
		                     FROM `category`
		                     WHERE `project_id` = ?
		                       AND `type` = ?
		                       AND `deleted` = ? [AND ...]) AS t2
		                    ON t1.`project_id` = t2.`project_id` AND t1.`type` = t2.`type` AND t1.`deleted` = t2.`deleted`
		         INNER JOIN `category_tree` AS t3
		                    ON t1.`project_id` = t3.`project_id` AND t1.`category_id` = t3.`ancestor` AND t2.`category_id` = t3.`descendant` AND t1.`deleted` = t3.`deleted`
		         INNER JOIN `category_tree` AS t4
		                    ON t1.`project_id` = t4.`project_id` AND t1.`category_id` = t4.`descendant` AND t1.`deleted` = t4.`deleted`
		WHERE (t4.`depth` = ? OR t1.`category_id` = t4.`ancestor` AND t4.`depth` = ?)
	*/

	// 注意：
	// 1. SQL没有使用`DISTINCT`或者`GROUP BY`进行去重，避免执行计划使用到`Using temporary`或者`Using temporary; Using filesort`；因此将会通过代码进行去重
	// 2. SQL没有使用`ORDER BY`对`depth`进行排序，避免执行计划使用到`Using temporary; Using filesort`；因此将会通过代码进行排序
	// 3. `amount`字段为`0`，业务层组装为树时进行即时计算

	fields := utils.AddTableNameToFields("t1", categoryFieldNames)
	fields = append(
		fields, []string{
			"t4.`ancestor` AS `parent_id`",
			"t4.`depth`",
			"t4.`index`",
			"0 AS `amount`",
		}...,
	)

	tmp := squirrel.Select(categoryFieldNames...).From(m.table).Where(
		"`project_id` = ? AND `type` = ? AND `deleted` = ?", cond.ProjectId, cond.Type, constants.NotDeleted,
	)
	tmp = sqlbuilder.SearchOptions(tmp, sqlbuilder.WithCondition(m, cond.SearchCondition))

	sb := squirrel.Select(fields...).
		From(m.table+" AS t1").
		JoinClause(tmp.Prefix("INNER JOIN (").Suffix(") AS t2 ON t1.`project_id` = t2.`project_id` AND t1.`type` = t2.`type` AND t1.`deleted` = t2.`deleted`")).
		InnerJoin(categoryTreeTableName+" AS t3 ON t1.`project_id` = t3.`project_id` AND t1.`category_id` = t3.`ancestor` AND t2.`category_id` = t3.`descendant` AND t1.`deleted` = t3.`deleted`").
		InnerJoin(categoryTreeTableName+" AS t4 ON t1.`project_id` = t4.`project_id` AND t1.`category_id` = t4.`descendant` AND t1.`deleted` = t4.`deleted`").
		Where("(t4.`depth` = ? OR t1.`category_id` = t4.`ancestor` AND t4.`depth` = ?)", 1, 0)

	query, values, err := sb.ToSql()
	if err != nil {
		return nil, err
	}

	var (
		nodes []*CategoryTreeNode
		resp  []*CategoryTreeNode
	)
	err = m.QueryRowsNoCacheCtx(ctx, &nodes, query, values...)
	switch err {
	case nil:
		resp = make([]*CategoryTreeNode, 0, len(nodes))
		// `stream`方式进行去重、排序
		fx.From(
			func(source chan<- any) {
				for _, node := range nodes {
					node := node
					source <- node
				}
			},
		).Distinct(
			func(item any) any {
				if item == nil {
					return ""
				} else if v, ok := item.(*CategoryTreeNode); !ok {
					return ""
				} else {
					return v.CategoryId + ":" + v.ParentId
				}
			},
		).Sort(
			func(a, b any) bool {
				if a == nil || b == nil {
					return false
				}

				v1, ok1 := a.(*CategoryTreeNode)
				v2, ok2 := b.(*CategoryTreeNode)
				if !ok1 || !ok2 {
					return false
				}

				// `depth` ASC, `index` ASC, `created_at` ASC
				if v1.Depth != v2.Depth {
					return v1.Depth < v2.Depth
				} else if v1.Index != v2.Index {
					return v1.Index < v2.Index
				} else if !v1.CreatedAt.Equal(v2.CreatedAt) {
					return v1.CreatedAt.Before(v2.CreatedAt)
				}
				return false
			},
		).ForEach(
			func(item any) {
				if v, ok := item.(*CategoryTreeNode); ok {
					resp = append(resp, v)
				}
			},
		)
		return resp, nil
	default:
		return nil, err
	}
}

func (m *customCategoryModel) GetTreeNodes(ctx context.Context, cond GetCategoryTreeCondition) (
	[]*CategoryTreeNode, error,
) {
	sb, err := m.generateGetTreeNodesSQLBuilder(cond)
	if err != nil {
		return nil, err
	}
	// 这里不能对`t1`进行过滤，否则无法根据查询后的节点生成回树
	// sb = sqlbuilder.SearchOptionsWithAlias(sb, "t1", sqlbuilder.WithCondition(m, cond.SearchCondition))

	// 当进行下述的排序时，执行计划会用到`Using temporary; Using filesort`，使SQL执行非常慢，在此问题解决前暂时使用程序进行排序
	// sb = sb.OrderBy("t.`depth` DESC", "t.`index` ASC", "t.`created_at` ASC")

	query, values, err := sb.ToSql()
	if err != nil {
		return nil, err
	}

	var (
		nodes []*CategoryTreeNode
		resp  []*CategoryTreeNode
	)
	err = m.QueryRowsNoCacheCtx(ctx, &nodes, query, values...)
	switch err {
	case nil:
		resp = make([]*CategoryTreeNode, 0, len(nodes))
		fx.From(
			func(source chan<- any) {
				for _, node := range nodes {
					node := node
					source <- node
				}
			},
		).Sort(
			func(a, b any) bool {
				if a == nil || b == nil {
					return false
				}

				v1, ok1 := a.(*CategoryTreeNode)
				v2, ok2 := b.(*CategoryTreeNode)
				if !ok1 || !ok2 {
					return false
				}

				// `depth` DESC, `index` ASC, `created_at` ASC
				if v1.Depth != v2.Depth {
					return v1.Depth > v2.Depth
				} else if v1.Index != v2.Index {
					return v1.Index < v2.Index
				} else if !v1.CreatedAt.Equal(v2.CreatedAt) {
					return v1.CreatedAt.Before(v2.CreatedAt)
				}
				return false
			},
		).ForEach(
			func(item any) {
				if v, ok := item.(*CategoryTreeNode); ok {
					resp = append(resp, v)
				}
			},
		)
		return resp, nil
	default:
		return nil, err
	}
}

func (m *customCategoryModel) generateGetTreeNodesSQLBuilder(cond GetCategoryTreeCondition) (
	sb squirrel.SelectBuilder, err error,
) {
	/*
		SQL:
		SELECT t1.`id`,
		       t1.`project_id`,
		       t1.`type`,
		       t1.`category_id`,
		       t1.`category_type`,
		       t1.`root_type`,
		       t1.`node_type`,
		       t1.`node_id`,
		       t1.`name`,
		       t1.`description`,
		       t1.`builtin`,
		       t1.`deleted`,
		       t1.`created_by`,
		       t1.`updated_by`,
		       t1.`deleted_by`,
		       t1.`created_at`,
		       t1.`updated_at`,
		       t1.`deleted_at`,
		       t3.`ancestor` AS `parent_id`,
		       t2.`depth`,
		       t3.`index`,
		       COUNT(t4.`id`) AS `amount`
		FROM `category` AS t1
		         INNER JOIN `category_tree` AS t2
		                    ON t1.`project_id` = t2.`project_id` AND t1.`category_id` = t2.`descendant` AND t1.`deleted` = t2.`deleted`
		         INNER JOIN `category_tree` AS t3
		                    ON t1.`project_id` = t3.`project_id` AND t1.`category_id` = t3.`descendant` AND t1.`deleted` = t3.`deleted`
		         LEFT JOIN `interface_document` AS t4
		                   ON t1.`project_id` = t4.`project_id` AND t1.`category_id` = t4.`category_id` AND t1.`deleted` = t4.`deleted`
		WHERE t1.`project_id` = ?
		  AND t1.`type` = ?
		  AND t2.`ancestor` = ?
		  AND t2.`depth` >= ?
		  AND (t3.`depth` = ? OR t2.`ancestor` = t3.`ancestor` AND t3.`depth` = ?)
		  AND t1.`deleted` = ?
		GROUP BY t1.`category_id`
		ORDER BY NULL
	*/

	var (
		leftJoinTableName string
		leftJoinOn        = "t1.`project_id` = t4.`project_id` AND t1.`category_id` = t4.`category_id` AND t1.`deleted` = t4.`deleted`"
		args              = make([]any, 0, 1)
	)
	switch cond.Type {
	case common.ConstCategoryTreeTypeInterfaceDocument:
		leftJoinTableName = interfaceDocumentTableName
	case common.ConstCategoryTreeTypeInterfaceSchema:
		leftJoinTableName = interfaceSchemaTableName
	case common.ConstCategoryTreeTypeComponentGroup:
		leftJoinTableName = componentGroupTableName
		leftJoinOn += " AND t4.`latest` = ?"
		args = append(args, constants.IsLatestVersion)
	case common.ConstCategoryTreeTypeApiCase:
		leftJoinTableName = apiCaseTableName
		leftJoinOn += " AND t4.`latest` = ?"
		args = append(args, constants.IsLatestVersion)
	case common.ConstCategoryTreeTypeApiSuite:
		leftJoinTableName = apiSuiteTableName
	case common.ConstCategoryTreeTypeApiPlan:
		leftJoinTableName = apiPlanTableName
	case common.ConstCategoryTreeTypeUiPlan:
		leftJoinTableName = uiPlanTableName
	case common.ConstCategoryTreeTypePerfCase:
		leftJoinTableName = perfCaseV2TableName
	case common.ConstCategoryTreeTypePerfPlan:
		leftJoinTableName = perfPlanV2TableName
	case common.ConstCategoryTreeTypeStabilityPlan:
		leftJoinTableName = stabilityPlanTableName
	case common.ConstCategoryTreeTypeUIAgentComponent:
		leftJoinTableName = uiAgentComponentTableName
	default:
		return sb, ErrNotSupport
	}

	fields := utils.AddTableNameToFields("t1", categoryFieldNames)
	fields = append(fields, "t3.`ancestor` AS `parent_id`", "t2.`depth`", "t3.`index`", "COUNT(t4.`id`) AS `amount`")
	sb = squirrel.Select(fields...).
		From(m.table+" AS t1").
		InnerJoin(categoryTreeTableName+" AS t2 ON t1.`project_id` = t2.`project_id` AND t1.`category_id` = t2.`descendant` AND t1.`deleted` = t2.`deleted`").
		InnerJoin(categoryTreeTableName+" AS t3 ON t1.`project_id` = t3.`project_id` AND t1.`category_id` = t3.`descendant` AND t1.`deleted` = t3.`deleted`").
		LeftJoin(leftJoinTableName+" AS t4 ON "+leftJoinOn, args...).
		Where(
			"t1.`project_id` = ? AND t1.`type` = ? AND t1.`deleted` = ? AND t2.`ancestor` = ? AND t2.`depth` >= ?",
			cond.ProjectId, cond.Type, constants.NotDeleted, cond.CategoryId, 0,
		).
		GroupBy("t1.`category_id`").
		OrderBy("NULL")
	if cond.Depth > 0 {
		sb = sb.Where("t2.`depth` <= ?", cond.Depth)
	}
	if cond.OnlyDirectory {
		sb = sb.Where("t1.`category_type` = ?", common.ConstCategoryTypeDirectory)
	}
	if cond.ExcludeSelf {
		sb = sb.Where("t3.`depth` = ?", 1)
	} else {
		sb = sb.Where("(t3.`depth` = ? OR t2.`ancestor` = t3.`ancestor` AND t3.`depth` = ?)", 1, 0)
	}

	return sb, nil
}

func (m *customCategoryModel) RemoveDescendantCategories(
	ctx context.Context, session sqlx.Session, projectId, tp, categoryId string,
) (sql.Result, error) {
	keys := m.getDescendantKeys(
		ctx, GetCategoryTreeCondition{
			ProjectId:  projectId,
			Type:       tp,
			CategoryId: categoryId,
		},
	)

	return m.ExecCtx(
		ctx, func(ctx context.Context, conn sqlx.SqlConn) (sql.Result, error) {
			/*
				SQL:
				UPDATE `category` AS t1
				    INNER JOIN `category_tree` AS t2
				    ON t1.`project_id` = t2.`project_id` AND t1.`category_id` = t2.`descendant`
				SET t1.`deleted` = 1, t1.`deleted_by` = ?, t1.`deleted_at` = ?
				WHERE t1.`project_id` = ?
				  AND t1.`type` = ?
				  AND t2.`ancestor` = ?
				  AND t1.`deleted` = ?
				  AND t2.`deleted` = ?;
			*/
			stmt, values, err := squirrel.Update(
				fmt.Sprintf(
					"%s AS t1 INNER JOIN %s AS t2 ON t1.`project_id` = t2.`project_id` AND t1.`category_id` = t2.`descendant`",
					m.table, categoryTreeTableName,
				),
			).
				SetMap(
					squirrel.Eq{
						"t1.`deleted`":    constants.HasDeleted,
						"t1.`deleted_by`": userinfo.FromContext(ctx).Account,
						"t1.`deleted_at`": time.Now(),
					},
				).
				Where(
					"t1.`project_id` = ? AND t1.`type` = ? AND t2.`ancestor` = ? AND t1.`deleted` = ? AND t2.`deleted` = ?",
					projectId, tp, categoryId, constants.NotDeleted, constants.NotDeleted,
				).
				ToSql()
			if err != nil {
				return nil, err
			}

			if session != nil {
				return session.ExecCtx(ctx, stmt, values...)
			}
			return conn.ExecCtx(ctx, stmt, values...)
		}, keys...,
	)
}

func (m *customCategoryModel) UpdateDescendantRootType(
	ctx context.Context, session sqlx.Session, projectId, tp, categoryId, rootType string,
) (sql.Result, error) {
	keys := m.getDescendantKeys(
		ctx, GetCategoryTreeCondition{
			ProjectId:  projectId,
			Type:       tp,
			CategoryId: categoryId,
		},
	)

	return m.ExecCtx(
		ctx, func(ctx context.Context, conn sqlx.SqlConn) (sql.Result, error) {
			/*
				SQL:
				UPDATE `category` AS t1
				    INNER JOIN `category_tree` AS t2
				    ON t1.`project_id` = t2.`project_id` AND t1.`category_id` = t2.`descendant`
				SET t1.`root_type` = ?
				WHERE t1.`project_id` = ?
				  AND t1.`type` = ?
				  AND t2.`ancestor` = ?
				  AND t1.`deleted` = ?
				  AND t2.`deleted` = ?;
			*/
			stmt, values, err := squirrel.Update(
				fmt.Sprintf(
					"%s AS t1 INNER JOIN %s AS t2 ON t1.`project_id` = t2.`project_id` AND t1.`category_id` = t2.`descendant`",
					m.table, categoryTreeTableName,
				),
			).
				Set("t1.`root_type`", rootType).
				Where(
					"t1.`project_id` = ? AND t1.`type` = ? AND t2.`ancestor` = ? AND t1.`deleted` = ? AND t2.`deleted` = ?",
					projectId, tp, categoryId, constants.NotDeleted, constants.NotDeleted,
				).
				ToSql()
			if err != nil {
				return nil, err
			}

			if session != nil {
				return session.ExecCtx(ctx, stmt, values...)
			}
			return conn.ExecCtx(ctx, stmt, values...)
		}, keys...,
	)
}

func (m *customCategoryModel) RemoveByNodeId(
	ctx context.Context, session sqlx.Session, projectId, tp, nodeId string,
) (sql.Result, error) {
	keys := m.getKeysByNodeId(ctx, projectId, tp, nodeId)

	return m.ExecCtx(
		ctx, func(ctx context.Context, conn sqlx.SqlConn) (sql.Result, error) {
			/*
				SQL:
				UPDATE `category`
				SET `deleted` = ?, `deleted_by` = ?, `deleted_at` = ?
				WHERE `project_id` = ?
				  AND `type` = ?
				  AND `node_id` = ?
			*/
			stmt, values, err := squirrel.Update(m.table).
				SetMap(
					squirrel.Eq{
						"`deleted`":    constants.HasDeleted,
						"`deleted_by`": userinfo.FromContext(ctx).Account,
						"`deleted_at`": time.Now(),
					},
				).
				Where("`project_id` = ? AND `type` = ? AND `node_id` = ?", projectId, tp, nodeId).
				ToSql()
			if err != nil {
				return nil, err
			}

			if session != nil {
				return session.ExecCtx(ctx, stmt, values...)
			}
			return conn.ExecCtx(ctx, stmt, values...)
		}, keys...,
	)
}

func (m *customCategoryModel) getDescendantKeys(ctx context.Context, cond GetCategoryTreeCondition) []string {
	cs, err := m.FindDescendantCategories(ctx, cond)
	if err != nil {
		return make([]string, 0)
	}

	keys := make([]string, 0, len(cs)*2)
	for _, c := range cs {
		keys = append(
			keys,
			fmt.Sprintf("%s%v", cacheManagerCategoryIdPrefix, c.Id),
			fmt.Sprintf(
				"%s%v:%v:%v", cacheManagerCategoryProjectIdTypeCategoryIdPrefix, c.ProjectId, c.Type, c.CategoryId,
			),
		)
	}

	return keys
}

func (m *customCategoryModel) getKeysByNodeId(ctx context.Context, projectId, tp, nodeId string) []string {
	sb := m.SelectBuilder().Where(
		"`project_id` = ? AND `type` = ? AND `node_id` = ? AND `deleted` = ?", projectId, tp, nodeId,
		constants.NotDeleted,
	)
	cs, err := m.FindNoCacheByQuery(ctx, sb)
	if err != nil {
		return make([]string, 0)
	}

	keys := make([]string, 0, len(cs)*2)
	for _, c := range cs {
		keys = append(
			keys,
			fmt.Sprintf("%s%v", cacheManagerCategoryIdPrefix, c.Id),
			fmt.Sprintf(
				"%s%v:%v:%v", cacheManagerCategoryProjectIdTypeCategoryIdPrefix, c.ProjectId, c.Type, c.CategoryId,
			),
		)
	}

	return keys
}

func (m *customCategoryModel) FindDescendantCategoriesSqlBuilder(cond GetCategoryTreeCondition) squirrel.SelectBuilder {
	/*
		SQL:
		SELECT t1.*
		FROM `category` AS t1
		    INNER JOIN `category_tree` AS t2
		        ON t1.`project_id` = t2.`project_id` AND t1.`category_id` = t2.`descendant`
		WHERE t1.`project_id` = ?
		  AND t1.`type` = ?
		  AND t2.`ancestor` = ?
		  AND t1.`deleted` = ?
		  AND t2.`deleted` = ?;
	*/

	sb := squirrel.Select(utils.AddTableNameToFields("t1", categoryFieldNames)...).
		From(m.table+" AS t1").
		InnerJoin(categoryTreeTableName+" AS t2 ON t1.`project_id` = t2.`project_id` AND t1.`category_id` = t2.`descendant`").
		Where(
			"t1.`project_id` = ? AND t1.`type` = ? AND t2.`ancestor` = ? AND t1.`deleted` = ? AND t2.`deleted` = ?",
			cond.ProjectId, cond.Type, cond.CategoryId, constants.NotDeleted, constants.NotDeleted,
		)
	if cond.Depth > 0 {
		sb = sb.Where("t2.`depth` <= ?", cond.Depth)
	}
	if cond.ExcludeSelf {
		sb = sb.Where("t2.`depth` > 0")
	} else {
		sb = sb.Where("t2.`depth` >= 0")
	}
	if cond.OnlyDirectory {
		sb = sb.Where("t1.`category_type` = ?", common.ConstCategoryTypeDirectory)
	}
	sb = sb.OrderBy("t2.`depth` DESC, t2.`index` ASC, t1.`created_at` ASC")

	return sb
}
