// Code generated by goctl. DO NOT EDIT.
// versions:
//  goctl version: 1.8.3

package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlc"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
)

var (
	slaThresholdTableName           = "`sla_threshold`"
	slaThresholdFieldNames          = builder.RawFieldNames(&SlaThreshold{})
	slaThresholdRows                = strings.Join(slaThresholdFieldNames, ",")
	slaThresholdRowsExpectAutoSet   = strings.Join(stringx.Remove(slaThresholdFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), ",")
	slaThresholdRowsWithPlaceHolder = strings.Join(stringx.Remove(slaThresholdFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), "=?,") + "=?"

	cacheManagerSlaThresholdIdPrefix                                  = "cache:manager:slaThreshold:id:"
	cacheManagerSlaThresholdProjectIdPlatformTypeBranchTypeNamePrefix = "cache:manager:slaThreshold:projectId:platformType:branchType:name:"
)

type (
	slaThresholdModel interface {
		Insert(ctx context.Context, session sqlx.Session, data *SlaThreshold) (sql.Result, error)
		FindOne(ctx context.Context, id int64) (*SlaThreshold, error)
		FindOneByProjectIdPlatformTypeBranchTypeName(ctx context.Context, projectId string, platformType int64, branchType int64, name string) (*SlaThreshold, error)
		Update(ctx context.Context, session sqlx.Session, data *SlaThreshold) (sql.Result, error)
		Delete(ctx context.Context, session sqlx.Session, id int64) error
		LogicDelete(ctx context.Context, session sqlx.Session, id int64) error
	}

	defaultSlaThresholdModel struct {
		sqlc.CachedConn
		table string
	}

	SlaThreshold struct {
		Id           int64          `db:"id"`            // 自增ID
		ProjectId    string         `db:"project_id"`    // 项目ID
		PlatformType int64          `db:"platform_type"` // 平台类型（Android、IOS）
		BranchType   int64          `db:"branch_type"`   // 分支类型（release、testing）
		Name         string         `db:"name"`          // 阈值指标
		Unit         string         `db:"unit"`          // 阈值单位
		Value        int64          `db:"value"`         // 阈值数值
		Deleted      int64          `db:"deleted"`       // 逻辑删除标识（未删除、已删除）
		CreatedBy    string         `db:"created_by"`    // 创建者的用户ID
		UpdatedBy    string         `db:"updated_by"`    // 最近一次更新者的用户ID
		DeletedBy    sql.NullString `db:"deleted_by"`    // 删除者的用户ID
		CreatedAt    time.Time      `db:"created_at"`    // 创建时间
		UpdatedAt    time.Time      `db:"updated_at"`    // 更新时间
		DeletedAt    sql.NullTime   `db:"deleted_at"`    // 删除时间
	}
)

func newSlaThresholdModel(conn sqlx.SqlConn, c cache.CacheConf, opts ...cache.Option) *defaultSlaThresholdModel {
	return &defaultSlaThresholdModel{
		CachedConn: sqlc.NewConn(conn, c, opts...),
		table:      "`sla_threshold`",
	}
}

func (m *defaultSlaThresholdModel) Delete(ctx context.Context, session sqlx.Session, id int64) error {
	data, err := m.FindOne(ctx, id)
	if err != nil {
		return err
	}

	managerSlaThresholdIdKey := fmt.Sprintf("%s%v", cacheManagerSlaThresholdIdPrefix, id)
	managerSlaThresholdProjectIdPlatformTypeBranchTypeNameKey := fmt.Sprintf("%s%v:%v:%v:%v", cacheManagerSlaThresholdProjectIdPlatformTypeBranchTypeNamePrefix, data.ProjectId, data.PlatformType, data.BranchType, data.Name)
	_, err = m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("delete from %s where `id` = ?", m.table)
		if session != nil {
			return session.ExecCtx(ctx, query, id)
		}
		return conn.ExecCtx(ctx, query, id)
	}, managerSlaThresholdIdKey, managerSlaThresholdProjectIdPlatformTypeBranchTypeNameKey)
	return err
}

func (m *defaultSlaThresholdModel) LogicDelete(ctx context.Context, session sqlx.Session, id int64) error {
	data, err := m.FindOne(ctx, id)
	if err != nil {
		return err
	}

	managerSlaThresholdIdKey := fmt.Sprintf("%s%v", cacheManagerSlaThresholdIdPrefix, id)
	managerSlaThresholdProjectIdPlatformTypeBranchTypeNameKey := fmt.Sprintf("%s%v:%v:%v:%v", cacheManagerSlaThresholdProjectIdPlatformTypeBranchTypeNamePrefix, data.ProjectId, data.PlatformType, data.BranchType, data.Name)
	_, err = m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set `deleted` = ?, `deleted_at` = ? where `id` = ?", m.table)
		if session != nil {
			return session.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
		}
		return conn.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
	}, managerSlaThresholdIdKey, managerSlaThresholdProjectIdPlatformTypeBranchTypeNameKey)
	return err
}

func (m *defaultSlaThresholdModel) FindOne(ctx context.Context, id int64) (*SlaThreshold, error) {
	managerSlaThresholdIdKey := fmt.Sprintf("%s%v", cacheManagerSlaThresholdIdPrefix, id)
	var resp SlaThreshold
	err := m.QueryRowCtx(ctx, &resp, managerSlaThresholdIdKey, func(ctx context.Context, conn sqlx.SqlConn, v any) error {
		query := fmt.Sprintf("select %s from %s where `id` = ? and `deleted` = ? limit 1", slaThresholdRows, m.table)
		return conn.QueryRowCtx(ctx, v, query, id, constants.NotDeleted)
	})
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultSlaThresholdModel) FindOneByProjectIdPlatformTypeBranchTypeName(ctx context.Context, projectId string, platformType int64, branchType int64, name string) (*SlaThreshold, error) {
	managerSlaThresholdProjectIdPlatformTypeBranchTypeNameKey := fmt.Sprintf("%s%v:%v:%v:%v", cacheManagerSlaThresholdProjectIdPlatformTypeBranchTypeNamePrefix, projectId, platformType, branchType, name)
	var resp SlaThreshold
	err := m.QueryRowIndexCtx(ctx, &resp, managerSlaThresholdProjectIdPlatformTypeBranchTypeNameKey, m.formatPrimary, func(ctx context.Context, conn sqlx.SqlConn, v any) (i any, e error) {
		query := fmt.Sprintf("select %s from %s where `project_id` = ? and `platform_type` = ? and `branch_type` = ? and `name` = ? and `deleted` = ? limit 1", slaThresholdRows, m.table)
		if err := conn.QueryRowCtx(ctx, &resp, query, projectId, platformType, branchType, name, constants.NotDeleted); err != nil {
			return nil, err
		}
		return resp.Id, nil
	}, m.queryPrimary)
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultSlaThresholdModel) Insert(ctx context.Context, session sqlx.Session, data *SlaThreshold) (sql.Result, error) {
	managerSlaThresholdIdKey := fmt.Sprintf("%s%v", cacheManagerSlaThresholdIdPrefix, data.Id)
	managerSlaThresholdProjectIdPlatformTypeBranchTypeNameKey := fmt.Sprintf("%s%v:%v:%v:%v", cacheManagerSlaThresholdProjectIdPlatformTypeBranchTypeNamePrefix, data.ProjectId, data.PlatformType, data.BranchType, data.Name)

	return m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", m.table, slaThresholdRowsExpectAutoSet)
		if session != nil {
			return session.ExecCtx(ctx, query, data.ProjectId, data.PlatformType, data.BranchType, data.Name, data.Unit, data.Value, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy)
		}
		return conn.ExecCtx(ctx, query, data.ProjectId, data.PlatformType, data.BranchType, data.Name, data.Unit, data.Value, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy)
	}, managerSlaThresholdIdKey, managerSlaThresholdProjectIdPlatformTypeBranchTypeNameKey)
}

func (m *defaultSlaThresholdModel) Update(ctx context.Context, session sqlx.Session, newData *SlaThreshold) (sql.Result, error) {
	data, err := m.FindOne(ctx, newData.Id)
	if err != nil {
		return nil, err
	}

	managerSlaThresholdIdKey := fmt.Sprintf("%s%v", cacheManagerSlaThresholdIdPrefix, data.Id)
	managerSlaThresholdProjectIdPlatformTypeBranchTypeNameKey := fmt.Sprintf("%s%v:%v:%v:%v", cacheManagerSlaThresholdProjectIdPlatformTypeBranchTypeNamePrefix, data.ProjectId, data.PlatformType, data.BranchType, data.Name)

	return m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, slaThresholdRowsWithPlaceHolder)
		if session != nil {
			return session.ExecCtx(ctx, query, newData.ProjectId, newData.PlatformType, newData.BranchType, newData.Name, newData.Unit, newData.Value, newData.Deleted, newData.CreatedBy, newData.UpdatedBy, newData.DeletedBy, newData.Id)
		}
		return conn.ExecCtx(ctx, query, newData.ProjectId, newData.PlatformType, newData.BranchType, newData.Name, newData.Unit, newData.Value, newData.Deleted, newData.CreatedBy, newData.UpdatedBy, newData.DeletedBy, newData.Id)
	}, managerSlaThresholdIdKey, managerSlaThresholdProjectIdPlatformTypeBranchTypeNameKey)
}

func (m *defaultSlaThresholdModel) formatPrimary(primary any) string {
	return fmt.Sprintf("%s%v", cacheManagerSlaThresholdIdPrefix, primary)
}

func (m *defaultSlaThresholdModel) queryPrimary(ctx context.Context, conn sqlx.SqlConn, v, primary any) error {
	query := fmt.Sprintf("select %s from %s where `id` = ? limit 1", slaThresholdRows, m.table)
	return conn.QueryRowCtx(ctx, v, query, primary)
}

func (m *defaultSlaThresholdModel) tableName() string {
	return m.table
}
