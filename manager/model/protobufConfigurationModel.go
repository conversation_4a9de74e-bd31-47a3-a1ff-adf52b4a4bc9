package model

import (
	"context"
	"database/sql"
	"fmt"
	"time"

	"github.com/Masterminds/squirrel"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sqlbuilder"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/types"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/userinfo"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/utils"
)

var (
	_ ProtobufConfigurationModel = (*customProtobufConfigurationModel)(nil)

	protobufConfigurationInsertFields = stringx.Remove(
		protobufConfigurationFieldNames, "`id`", "`create_time`", "`update_time`", "`deleted_by`", "`created_at`",
		"`updated_at`", "`deleted_at`",
	)
)

type (
	// ProtobufConfigurationModel is an interface to be customized, add more methods here,
	// and implement the added methods in customProtobufConfigurationModel.
	ProtobufConfigurationModel interface {
		protobufConfigurationModel
		types.DBModel

		Trans(ctx context.Context, fn func(context context.Context, session sqlx.Session) error) error
		InsertBuilder(data *ProtobufConfiguration) squirrel.InsertBuilder
		UpdateBuilder(data *ProtobufConfiguration) squirrel.UpdateBuilder
		SelectBuilder() squirrel.SelectBuilder
		SelectCountBuilder() squirrel.SelectBuilder
		FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error)
		FindNoCacheByQuery(ctx context.Context, selectBuilder squirrel.SelectBuilder) ([]*ProtobufConfiguration, error)

		FindCountBySearchReq(ctx context.Context, req SearchProtobufConfigurationReq) (int64, error)
		FindAllBySearchReq(ctx context.Context, req SearchProtobufConfigurationReq) ([]*ProtobufConfiguration, error)
		FindDependencies(ctx context.Context, projectID, configID string) ([]*ProtobufConfiguration, error)
		RemoveByConfigID(ctx context.Context, session sqlx.Session, projectID, configID string) (sql.Result, error)
	}

	customProtobufConfigurationModel struct {
		*defaultProtobufConfigurationModel

		conn sqlx.SqlConn
	}
)

// NewProtobufConfigurationModel returns a model for the database table.
func NewProtobufConfigurationModel(
	conn sqlx.SqlConn, c cache.CacheConf, opts ...cache.Option,
) ProtobufConfigurationModel {
	return &customProtobufConfigurationModel{
		defaultProtobufConfigurationModel: newProtobufConfigurationModel(conn, c, opts...),
		conn:                              conn,
	}
}

func (m *customProtobufConfigurationModel) Table() string {
	return m.table
}

func (m *customProtobufConfigurationModel) Fields() []string {
	return protobufConfigurationFieldNames
}

func (m *customProtobufConfigurationModel) Trans(
	ctx context.Context, fn func(context context.Context, session sqlx.Session) error,
) error {
	return m.TransactCtx(ctx, fn)
}

func (m *customProtobufConfigurationModel) InsertBuilder(data *ProtobufConfiguration) squirrel.InsertBuilder {
	return squirrel.Insert(m.table).Columns(protobufConfigurationInsertFields...).Values(
		data.ProjectId, data.ConfigId, data.Name, data.Description, data.GitConfigId, data.ImportPath,
		data.ExcludePaths, data.ExcludeFiles, data.Deleted, data.CreatedBy, data.UpdatedBy,
	)
}

func (m *customProtobufConfigurationModel) UpdateBuilder(data *ProtobufConfiguration) squirrel.UpdateBuilder {
	eq := squirrel.Eq{
		"`name`":          data.Name,
		"`description`":   data.Description,
		"`git_config_id`": data.GitConfigId,
		"`import_path`":   data.ImportPath,
		"`exclude_paths`": data.ExcludePaths,
		"`exclude_files`": data.ExcludeFiles,
		"`deleted`":       data.Deleted,
		"`updated_by`":    data.UpdatedBy,
		"`deleted_by`":    data.DeletedBy,
		"`deleted_at`":    data.DeletedAt,
	}
	return squirrel.Update(m.table).SetMap(eq).Where("`id` = ?", data.Id)
}

func (m *customProtobufConfigurationModel) SelectBuilder() squirrel.SelectBuilder {
	return squirrel.Select(protobufConfigurationFieldNames...).Where(
		"`deleted` = ?", constants.NotDeleted,
	).From(m.table)
}

func (m *customProtobufConfigurationModel) SelectCountBuilder() squirrel.SelectBuilder {
	return squirrel.Select("count(*)").Where("`deleted` = ?", constants.NotDeleted).From(m.table)
}

func (m *customProtobufConfigurationModel) FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (
	int64, error,
) {
	query, values, err := countBuilder.ToSql()
	if err != nil {
		return 0, err
	}

	var resp int64
	err = m.QueryRowNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return 0, err
	}
}

func (m *customProtobufConfigurationModel) FindNoCacheByQuery(
	ctx context.Context, selectBuilder squirrel.SelectBuilder,
) ([]*ProtobufConfiguration, error) {
	query, values, err := selectBuilder.ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*ProtobufConfiguration
	err = m.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

func (m *customProtobufConfigurationModel) FindCountBySearchReq(
	ctx context.Context, req SearchProtobufConfigurationReq,
) (int64, error) {
	return m.FindCount(
		ctx, sqlbuilder.SearchOptions(
			m.SelectCountBuilder().Where("`project_id` = ?", req.ProjectID),
			sqlbuilder.WithCondition(m, req.Condition),
		),
	)
}

func (m *customProtobufConfigurationModel) FindAllBySearchReq(
	ctx context.Context, req SearchProtobufConfigurationReq,
) ([]*ProtobufConfiguration, error) {
	return m.FindNoCacheByQuery(
		ctx, sqlbuilder.SearchOptions(
			m.SelectBuilder().Where("`project_id` = ?", req.ProjectID),
			sqlbuilder.WithCondition(m, req.Condition),
			sqlbuilder.WithPagination(m, req.Pagination),
			sqlbuilder.WithSort(m, req.Sort),
		),
	)
}

func (m *customProtobufConfigurationModel) FindDependencies(
	ctx context.Context, projectID, configID string,
) ([]*ProtobufConfiguration, error) {
	/*
		SQL:
		SELECT t1.*
		FROM `protobuf_configuration` AS t1
		INNER JOIN `protobuf_dependence` AS t2 ON
		    t1.`project_id` = t2.`project_id` AND
		    t1.`config_id` = t2.`dep_config_id` AND
		    t1.`deleted` = t2.`deleted`
		WHERE t1.`project_id` = ?
		  AND t2.`config_id` = ?
		  AND t1.`deleted` = ?
	*/
	return m.FindNoCacheByQuery(
		ctx, squirrel.Select(utils.AddTableNameToFields("t1", m.Fields())...).
			From(m.table+" AS t1").
			InnerJoin(protobufDependenceTableName+" AS t2 ON t1.`project_id` = t2.`project_id` AND t1.`config_id` = t2.`dep_config_id` AND t1.`deleted` = t2.`deleted`").
			Where(
				"t1.`project_id` = ? AND t2.`config_id` = ? AND t1.`deleted` = ?",
				projectID, configID, constants.NotDeleted,
			),
	)
}

func (m *customProtobufConfigurationModel) RemoveByConfigID(
	ctx context.Context, session sqlx.Session, projectID, configID string,
) (sql.Result, error) {
	keys := m.getKeysByConfigID(ctx, projectID, configID)

	return m.ExecCtx(
		ctx, func(ctx context.Context, conn sqlx.SqlConn) (sql.Result, error) {
			/*
				SQL:
				UPDATE `protobuf_configuration`
				SET `deleted` = ?, `deleted_by` = ?, `deleted_at` = ?
				WHERE `project_id` = ?
				  AND `config_id` = ?
			*/
			stmt, values, err := squirrel.Update(m.table).
				SetMap(
					squirrel.Eq{
						"`deleted`":    constants.HasDeleted,
						"`deleted_by`": userinfo.FromContext(ctx).Account,
						"`deleted_at`": time.Now(),
					},
				).
				Where("`project_id` = ? AND `config_id` = ?", projectID, configID).
				ToSql()
			if err != nil {
				return nil, err
			}

			if session != nil {
				return session.ExecCtx(ctx, stmt, values...)
			}
			return conn.ExecCtx(ctx, stmt, values...)
		}, keys...,
	)
}

func (m *customProtobufConfigurationModel) getKeysByConfigID(ctx context.Context, projectID, configID string) []string {
	c, err := m.FindOneByProjectIdConfigId(ctx, projectID, configID)
	if err != nil {
		return make([]string, 0)
	}

	return []string{
		fmt.Sprintf("%s%v", cacheManagerProtobufConfigurationIdPrefix, c.Id),
		fmt.Sprintf("%s%v:%v", cacheManagerProtobufConfigurationProjectIdConfigIdPrefix, c.ProjectId, c.ConfigId),
	}
}
