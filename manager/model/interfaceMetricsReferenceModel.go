package model

import (
	"context"
	"database/sql"
	"fmt"
	"time"

	"github.com/Masterminds/squirrel"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/types"
)

var (
	_ InterfaceMetricsReferenceModel = (*customInterfaceMetricsReferenceModel)(nil)

	interfaceMetricsReferenceInsertFields = stringx.Remove(interfaceMetricsReferenceFieldNames, "`id`", "`create_time`", "`update_time`", "`deleted_by`", "`created_at`", "`updated_at`", "`deleted_at`")
)

type (
	// InterfaceMetricsReferenceModel is an interface to be customized, add more methods here,
	// and implement the added methods in customInterfaceMetricsReferenceModel.
	InterfaceMetricsReferenceModel interface {
		interfaceMetricsReferenceModel
		types.DBModel

		Trans(ctx context.Context, fn func(context context.Context, session sqlx.Session) error) error
		InsertBuilder(data *InterfaceMetricsReference) squirrel.InsertBuilder
		UpdateBuilder(data *InterfaceMetricsReference) squirrel.UpdateBuilder
		SelectBuilder() squirrel.SelectBuilder
		SelectCountBuilder() squirrel.SelectBuilder
		FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error)
		FindNoCacheByQuery(ctx context.Context, selectBuilder squirrel.SelectBuilder) ([]*InterfaceMetricsReference, error)

		InsertTX(ctx context.Context, session sqlx.Session, data *InterfaceMetricsReference) (sql.Result, error)
		UpdateTX(ctx context.Context, session sqlx.Session, data *InterfaceMetricsReference) (sql.Result, error)

		FindBeforeDateRecords(ctx context.Context, date time.Time) ([]*InterfaceMetricsReference, error)
		DeleteBeforeDateRecords(ctx context.Context, session sqlx.Session, date time.Time) error
	}

	customInterfaceMetricsReferenceModel struct {
		*defaultInterfaceMetricsReferenceModel

		conn sqlx.SqlConn
	}
)

// NewInterfaceMetricsReferenceModel returns a model for the database table.
func NewInterfaceMetricsReferenceModel(conn sqlx.SqlConn, c cache.CacheConf, opts ...cache.Option) InterfaceMetricsReferenceModel {
	return &customInterfaceMetricsReferenceModel{
		defaultInterfaceMetricsReferenceModel: newInterfaceMetricsReferenceModel(conn, c, opts...),
		conn:                                  conn,
	}
}

func (m *customInterfaceMetricsReferenceModel) Table() string {
	return m.table
}

func (m *customInterfaceMetricsReferenceModel) Fields() []string {
	return interfaceMetricsReferenceFieldNames
}

func (m *customInterfaceMetricsReferenceModel) Trans(ctx context.Context, fn func(context context.Context, session sqlx.Session) error) error {
	return m.TransactCtx(ctx, fn)
}

func (m *customInterfaceMetricsReferenceModel) InsertBuilder(data *InterfaceMetricsReference) squirrel.InsertBuilder {
	ib := squirrel.Insert(m.table).Columns(interfaceMetricsReferenceInsertFields...)
	values := []any{
		data.ProjectId,
		data.Protocol,
		data.Method,
		data.ReferenceQps,
		data.Deleted,
		data.CreatedBy,
		data.UpdatedBy,
	}
	if !data.CreatedAt.IsZero() {
		ib = ib.Columns("`created_at`")
		values = append(values, data.CreatedAt)
	}
	if !data.UpdatedAt.IsZero() {
		ib = ib.Columns("`updated_at`")
		values = append(values, data.UpdatedAt)
	}

	return ib.Values(values...)
}

func (m *customInterfaceMetricsReferenceModel) UpdateBuilder(data *InterfaceMetricsReference) squirrel.UpdateBuilder {
	eq := squirrel.Eq{
		"`reference_qps`": data.ReferenceQps,
		"`deleted`":       data.Deleted,
		"`updated_by`":    data.UpdatedBy,
		"`deleted_by`":    data.DeletedBy,
		"`deleted_at`":    data.DeletedAt,
	}
	if !data.UpdatedAt.IsZero() {
		eq["`updated_at`"] = data.UpdatedAt
	}
	return squirrel.Update(m.table).SetMap(eq).Where("`id` = ?", data.Id)
}

func (m *customInterfaceMetricsReferenceModel) SelectBuilder() squirrel.SelectBuilder {
	return squirrel.Select(interfaceMetricsReferenceFieldNames...).Where("`deleted` = ?", constants.NotDeleted).From(m.table)
}

func (m *customInterfaceMetricsReferenceModel) SelectCountBuilder() squirrel.SelectBuilder {
	return squirrel.Select("count(*)").Where("`deleted` = ?", constants.NotDeleted).From(m.table)
}

func (m *customInterfaceMetricsReferenceModel) FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error) {
	query, values, err := countBuilder.ToSql()
	if err != nil {
		return 0, err
	}

	var resp int64
	err = m.QueryRowNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return 0, err
	}
}

func (m *customInterfaceMetricsReferenceModel) FindNoCacheByQuery(ctx context.Context, selectBuilder squirrel.SelectBuilder) ([]*InterfaceMetricsReference, error) {
	query, values, err := selectBuilder.ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*InterfaceMetricsReference
	err = m.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

func (m *customInterfaceMetricsReferenceModel) InsertTX(
	ctx context.Context, session sqlx.Session, data *InterfaceMetricsReference,
) (sql.Result, error) {
	managerInterfaceMetricsReferenceIdKey := fmt.Sprintf("%s%v", cacheManagerInterfaceMetricsReferenceIdPrefix, data.Id)
	managerInterfaceMetricsReferenceProjectIdProtocolMethodKey := fmt.Sprintf(
		"%s%v:%v:%v",
		cacheManagerInterfaceMetricsReferenceProjectIdProtocolMethodPrefix, data.ProjectId, data.Protocol, data.Method,
	)

	return m.ExecCtx(
		ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
			stmt, values, err := m.InsertBuilder(data).ToSql()
			if err != nil {
				return nil, err
			}
			if session != nil {
				return conn.ExecCtx(ctx, stmt, values...)
			}
			return conn.ExecCtx(ctx, stmt, values...)
		}, managerInterfaceMetricsReferenceIdKey, managerInterfaceMetricsReferenceProjectIdProtocolMethodKey,
	)
}

func (m *customInterfaceMetricsReferenceModel) UpdateTX(
	ctx context.Context, session sqlx.Session, newData *InterfaceMetricsReference,
) (sql.Result, error) {
	data, err := m.FindOne(ctx, newData.Id)
	if err != nil {
		return nil, err
	}

	managerInterfaceMetricsReferenceIdKey := fmt.Sprintf("%s%v", cacheManagerInterfaceMetricsReferenceIdPrefix, data.Id)
	managerInterfaceMetricsReferenceProjectIdProtocolMethodKey := fmt.Sprintf(
		"%s%v:%v:%v",
		cacheManagerInterfaceMetricsReferenceProjectIdProtocolMethodPrefix, data.ProjectId, data.Protocol, data.Method,
	)

	return m.ExecCtx(
		ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
			stmt, values, err := m.UpdateBuilder(newData).ToSql()
			if err != nil {
				return nil, err
			}
			if session != nil {
				return session.ExecCtx(ctx, stmt, values...)
			}
			return conn.ExecCtx(ctx, stmt, values...)
		}, managerInterfaceMetricsReferenceIdKey, managerInterfaceMetricsReferenceProjectIdProtocolMethodKey,
	)
}

func (m *customInterfaceMetricsReferenceModel) FindBeforeDateRecords(ctx context.Context, date time.Time) ([]*InterfaceMetricsReference, error) {
	return m.FindNoCacheByQuery(
		ctx, m.SelectBuilder().Where("`updated_at` < ?", date),
	)
}

func (m *customInterfaceMetricsReferenceModel) DeleteBeforeDateRecords(
	ctx context.Context, session sqlx.Session, date time.Time,
) error {
	keys := m.getKeysByBeforeDate(ctx, date)

	_, err := m.ExecCtx(
		ctx, func(ctx context.Context, conn sqlx.SqlConn) (sql.Result, error) {
			stmt, values, err := squirrel.Delete(m.table).
				Where("`updated_at` < ?", date).
				ToSql()
			if err != nil {
				return nil, err
			}

			if session != nil {
				return session.ExecCtx(ctx, stmt, values...)
			}
			return conn.ExecCtx(ctx, stmt, values...)
		}, keys...,
	)
	return err
}

func (m *customInterfaceMetricsReferenceModel) getKeysByBeforeDate(ctx context.Context, date time.Time) []string {
	cs, err := m.FindBeforeDateRecords(ctx, date)
	if err != nil {
		return make([]string, 0)
	}

	keys := make([]string, 0, len(cs))
	for _, c := range cs {
		keys = append(keys, fmt.Sprintf("%s%v", cacheManagerInterfaceMetricsReferenceIdPrefix, c.Id))
	}

	return keys
}
