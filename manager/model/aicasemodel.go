package model

import (
	"context"
	"database/sql"
	"time"

	"github.com/Masterminds/squirrel"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sqlbuilder"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/utils"
)

type (
	AICaseModel interface {
		GenerateSearchNotReleasedCaseSqlBuilder(req SearchNotReleasedCaseReq) (
			searchNotReleasedCaseSelectBuilder, searchNotReleasedCaseCountBuilder,
		)
		FindNoCacheByQuery(ctx context.Context, selectBuilder squirrel.SelectBuilder) (
			[]*AICase, error,
		)
		FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error)
	}
	AICase struct {
		Id            int64          `db:"id"`
		ProjectId     string         `db:"project_id"`     // 项目ID
		BranchId      string         `db:"branch_id"`      // 所属分类ID
		CaseId        string         `db:"case_id"`        // 用例ID
		CaseType      string         `db:"case_type"`      // 用例ID
		Name          string         `db:"name"`           // 用例名称
		Description   sql.NullString `db:"description"`    // 用例描述
		Priority      int64          `db:"priority"`       // 优先级（NULL、P0、P1、P2、P3...）
		Tags          sql.NullString `db:"tags"`           // 标签
		State         string         `db:"state"`          // 用例状态（生效、失效）
		AccountConfig string         `db:"account_config"` // 池账号配置数
		Version       string         `db:"version"`        // 用例版本
		Structure     string         `db:"structure"`      // 用例中各节点的关系结构
		Latest        int64          `db:"latest"`         // 是否最新版本
		Deleted       int64          `db:"deleted"`        // 逻辑删除标识（未删除、已删除）
		MaintainedBy  sql.NullString `db:"maintained_by"`  // 维护者的用户ID
		CreatedBy     string         `db:"created_by"`     // 创建者的用户ID
		UpdatedBy     string         `db:"updated_by"`     // 最近一次更新者的用户ID
		DeletedBy     sql.NullString `db:"deleted_by"`     // 删除者的用户ID
		CreatedAt     time.Time      `db:"created_at"`     // 创建时间
		UpdatedAt     time.Time      `db:"updated_at"`     // 更新时间
		DeletedAt     sql.NullTime   `db:"deleted_at"`     // 删除时间
	}

	customAICaseModel struct {
		a    ApiCaseModel
		i    InterfaceCaseModel
		conn sqlx.SqlConn
	}
)

func NewAICaseModel(conn sqlx.SqlConn, c cache.CacheConf) AICaseModel {
	return &customAICaseModel{
		a:    NewApiCaseModel(conn, c),
		i:    NewInterfaceCaseModel(conn, c),
		conn: conn,
	}
}

type searchNotReleasedCaseSelectBuilder struct {
	squirrel.SelectBuilder
}

type searchNotReleasedCaseCountBuilder struct {
	squirrel.SelectBuilder
}

func (m *customAICaseModel) GenerateSearchNotReleasedCaseSqlBuilder(req SearchNotReleasedCaseReq) (
	searchNotReleasedCaseSelectBuilder, searchNotReleasedCaseCountBuilder,
) {
	/*
			select t.*
			from (select ac.id,
			             ac.project_id,
			             ac.category_id as branch_id,
			             ac.case_id,
			             ac.name,
			             'API_CASE'     as case_type,
			             ac.description,
			             ac.priority,
			             ac.tags,
			             ac.state,
			             ac.account_config,
			             ac.version,
			             ac.structure,
			             ac.latest,
			             ac.deleted,
			             ac.maintained_by,
			             ac.created_by,
			             ac.updated_by,
			             ac.deleted_by,
			             ac.created_at,
			             ac.updated_at,
			             ac.deleted_at
			      from api_case ac
			      where ac.deleted = 0
			        and ac.latest = 1
			        and ac.state in (1, 2, 3)
			        and ac.project_id = 'project_id:Kqllt5-9fA-I5UOdhjA5d'
			        -- 高级查询
			        and ac.name like '%新%'
			      union
			      select ic.id,
			             ic.project_id,
			             ic.document_id   as branch_id,
			             ic.case_id,
			             ic.name,
			             'INTERFACE_CASE' as case_type,
			             ic.description,
			             ic.priority,
			             ic.tags,
			             ic.state,
			             ic.account_config,
			             ic.version,
			             ic.structure,
			             ic.latest,
			             ic.deleted,
			             ic.maintained_by,
			             ic.created_by,
			             ic.updated_by,
			             ic.deleted_by,
			             ic.created_at,
			             ic.updated_at,
			             ic.deleted_at
			      from interface_case ic
			      where ic.deleted = 0
			        and ic.latest = 1
			        and ic.state in (1, 2, 3)
			        and ic.project_id = 'project_id:Kqllt5-9fA-I5UOdhjA5d'
			        and ic.name like '%新%') as t
			order by t.updated_at desc
			limit 100 offset 0;

		SELECT COUNT(*)
		FROM (SELECT ac.`id`,
		             ac.`project_id`,
		             ac.`category_id`,
		             ac.`case_id`,
		             ac.`name`,
		             ac.`description`,
		             ac.`priority`,
		             ac.`tags`,
		             ac.`state`,
		             ac.`account_config`,
		             ac.`version`,
		             ac.`structure`,
		             ac.`latest`,
		             ac.`deleted`,
		             ac.`maintained_by`,
		             ac.`created_by`,
		             ac.`updated_by`,
		             ac.`deleted_by`,
		             ac.`created_at`,
		             ac.`updated_at`,
		             ac.`deleted_at`,
		             'API_CASE' as case_type
		      FROM `api_case` AS ac
		      WHERE ac.`project_id` = 'project_id:Kqllt5-9fA-I5UOdhjA5d'
		        AND ac.`state` = 'PUBLISHED'
		        AND ac.`latest` = 1
		        AND ac.`deleted` = 0
		        AND (ac.`name` LIKE '%公会%' AND ac.`maintained_by` IN ('T0169'))
		      UNION
		      SELECT ic.`id`,
		             ic.`project_id`,
		             ic.`document_id`,
		             ic.`case_id`,
		             ic.`name`,
		             ic.`description`,
		             ic.`priority`,
		             ic.`tags`,
		             ic.`state`,
		             ic.`account_config`,
		             ic.`version`,
		             ic.`structure`,
		             ic.`latest`,
		             ic.`deleted`,
		             ic.`maintained_by`,
		             ic.`created_by`,
		             ic.`updated_by`,
		             ic.`deleted_by`,
		             ic.`created_at`,
		             ic.`updated_at`,
		             ic.`deleted_at`,
		             'INTERFACE_CASE' as case_type
		      FROM `interface_case` AS ic
		      WHERE ic.`project_id` = 'project_id:Kqllt5-9fA-I5UOdhjA5d'
		        AND ic.`state` = 'PUBLISHED'
		        AND ic.`latest` = 1
		        AND ic.`deleted` = 0
		        AND (ic.`name` LIKE '%公会%' AND ic.`maintained_by` IN ('T0169'))) AS t;

	*/
	var sb, scb squirrel.SelectBuilder
	// api
	apiCaseTableAs := "ac"
	apiCaseToFields := utils.AddTableNameToFields(
		apiCaseTableAs, notReleasedCaseFieldNamesForApiCase,
	)
	apiCaseToFields = append(apiCaseToFields, "ac.category_id as branch_id", "'API_CASE' as case_type")
	apiCaseSqlTmp := squirrel.Select(
		apiCaseToFields...,
	).From(m.a.Table()+" AS "+apiCaseTableAs).Where(
		"ac.`project_id` = ? AND ac.`state` != ?  AND ac.`latest` = ? AND ac.`deleted` = ?", req.ProjectId,
		"PUBLISHED", constants.IsLatestVersion, constants.NotDeleted,
	)
	apiCaseSqlTmpQuery := sqlbuilder.SearchOptionsWithAlias(
		apiCaseSqlTmp,
		apiCaseTableAs, sqlbuilder.WithCondition(m.a, req.Condition),
		// sqlbuilder.WithSort(m.a, req.Sort),
	)
	// interface
	interfaceCaseTableAs := "ic"
	interfaceCaseToFields := utils.AddTableNameToFields(
		interfaceCaseTableAs, notReleasedCaseFieldNamesForInterfaceCase,
	)
	interfaceCaseToFields = append(
		interfaceCaseToFields, "ic.document_id as branch_id", "'INTERFACE_CASE' as case_type",
	)
	interfaceCaseSqlTmp := squirrel.Select(
		interfaceCaseToFields...,
	).From(m.i.Table()+" AS "+interfaceCaseTableAs).Where(
		"ic.`project_id` = ? AND ic.`state` != ?  AND ic.`latest` = ? AND ic.`deleted` = ?", req.ProjectId,
		"PUBLISHED", constants.IsLatestVersion, constants.NotDeleted,
	)

	interfaceSqlTmpQuery := sqlbuilder.SearchOptionsWithAlias(
		interfaceCaseSqlTmp, interfaceCaseTableAs,
		sqlbuilder.WithCondition(m.i, req.Condition),
		// sqlbuilder.WithSort(m.i, req.Sort),
	)
	// 合并
	interfaceSqlTmpQueryToSql, args, _ := interfaceSqlTmpQuery.ToSql()
	unionSql := apiCaseSqlTmpQuery.Suffix("UNION "+interfaceSqlTmpQueryToSql, args...)

	sb = squirrel.Select("t.*").FromSelect(unionSql, "t").OrderBy("t.updated_at DESC")
	sb = sqlbuilder.SearchOptionsWithAlias(sb, "t", sqlbuilder.WithPagination(m.a, req.Pagination))

	scb = squirrel.Select("COUNT(*)").FromSelect(unionSql, "t")

	return searchNotReleasedCaseSelectBuilder{SelectBuilder: sb}, searchNotReleasedCaseCountBuilder{SelectBuilder: scb}
}

func (m *customAICaseModel) FindNoCacheByQuery(
	ctx context.Context, selectBuilder squirrel.SelectBuilder,
) (
	[]*AICase, error,
) {
	query, values, err := selectBuilder.ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*AICase
	err = m.conn.QueryRowsCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

func (m *customAICaseModel) FindCount(
	ctx context.Context, countBuilder squirrel.SelectBuilder,
) (int64, error) {
	query, values, err := countBuilder.ToSql()
	if err != nil {
		return 0, err
	}

	var resp int64
	err = m.conn.QueryRowCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return 0, err
	}
}
