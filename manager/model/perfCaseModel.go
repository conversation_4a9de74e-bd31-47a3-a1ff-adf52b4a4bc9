package model

import (
	"context"

	"github.com/Masterminds/squirrel"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/types"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/utils"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
)

var (
	_ PerfCaseModel = (*customPerfCaseModel)(nil)

	perfCaseInsertFields = stringx.Remove(
		perfCaseFieldNames, "`id`", "`create_time`", "`update_time`", "`deleted_by`", "`created_at`", "`updated_at`",
		"`deleted_at`",
	)
)

type (
	// PerfCaseModel is an interface to be customized, add more methods here,
	// and implement the added methods in customPerfCaseModel.
	PerfCaseModel interface {
		perfCaseModel
		types.DBModel

		Trans(ctx context.Context, fn func(context context.Context, session sqlx.Session) error) error
		InsertBuilder(data *PerfCase) squirrel.InsertBuilder
		UpdateBuilder(data *PerfCase) squirrel.UpdateBuilder
		SelectBuilder() squirrel.SelectBuilder
		SelectCountBuilder() squirrel.SelectBuilder
		FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error)
		FindNoCacheByQuery(ctx context.Context, selectBuilder squirrel.SelectBuilder) ([]*PerfCase, error)

		FindByDataID(ctx context.Context, projectID, dataID string) ([]*PerfCase, error)
		FindAllByPlanID(ctx context.Context, projectID, planID string) ([]*PerfCase, error)
	}

	customPerfCaseModel struct {
		*defaultPerfCaseModel

		conn sqlx.SqlConn
	}
)

// NewPerfCaseModel returns a model for the database table.
func NewPerfCaseModel(conn sqlx.SqlConn, c cache.CacheConf, opts ...cache.Option) PerfCaseModel {
	return &customPerfCaseModel{
		defaultPerfCaseModel: newPerfCaseModel(conn, c, opts...),
		conn:                 conn,
	}
}

func (m *customPerfCaseModel) Table() string {
	return m.table
}

func (m *customPerfCaseModel) Fields() []string {
	return perfCaseFieldNames
}

func (m *customPerfCaseModel) Trans(
	ctx context.Context, fn func(context context.Context, session sqlx.Session) error,
) error {
	return m.TransactCtx(ctx, fn)
}

func (m *customPerfCaseModel) InsertBuilder(data *PerfCase) squirrel.InsertBuilder {
	return squirrel.Insert(m.table).Columns(perfCaseInsertFields...).Values(
		data.ProjectId, data.CaseId, data.Name, data.Description, data.Hash, data.Size, data.Path, data.TargetRps,
		data.InitialRps, data.StepHeight, data.StepDuration, data.PerfDataId, data.NumberOfVu, data.NumberOfLg,
		data.RequestsOfCpu, data.RequestsOfMemory, data.LimitsOfCpu, data.LimitsOfMemory, data.State, data.Deleted,
		data.MaintainedBy, data.CreatedBy, data.UpdatedBy,
	)
}

func (m *customPerfCaseModel) UpdateBuilder(data *PerfCase) squirrel.UpdateBuilder {
	eq := squirrel.Eq{
		"`name`":               data.Name,
		"`description`":        data.Description,
		"`hash`":               data.Hash,
		"`size`":               data.Size,
		"`path`":               data.Path,
		"`target_rps`":         data.TargetRps,
		"`initial_rps`":        data.InitialRps,
		"`step_height`":        data.StepHeight,
		"`step_duration`":      data.StepDuration,
		"`perf_data_id`":       data.PerfDataId,
		"`number_of_vu`":       data.NumberOfVu,
		"`number_of_lg`":       data.NumberOfLg,
		"`requests_of_cpu`":    data.RequestsOfCpu,
		"`requests_of_memory`": data.RequestsOfMemory,
		"`limits_of_cpu`":      data.LimitsOfCpu,
		"`limits_of_memory`":   data.LimitsOfMemory,
		"`state`":              data.State,
		"`deleted`":            data.Deleted,
		"`maintained_by`":      data.MaintainedBy,
		"`updated_by`":         data.UpdatedBy,
		"`deleted_by`":         data.DeletedBy,
		"`deleted_at`":         data.DeletedAt,
	}
	return squirrel.Update(m.table).SetMap(eq).Where("`id` = ?", data.Id)
}

func (m *customPerfCaseModel) SelectBuilder() squirrel.SelectBuilder {
	return squirrel.Select(perfCaseFieldNames...).Where("`deleted` = ?", constants.NotDeleted).From(m.table)
}

func (m *customPerfCaseModel) SelectCountBuilder() squirrel.SelectBuilder {
	return squirrel.Select("count(*)").Where("`deleted` = ?", constants.NotDeleted).From(m.table)
}

func (m *customPerfCaseModel) FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error) {
	query, values, err := countBuilder.ToSql()
	if err != nil {
		return 0, err
	}

	var resp int64
	err = m.QueryRowNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return 0, err
	}
}

func (m *customPerfCaseModel) FindNoCacheByQuery(
	ctx context.Context, selectBuilder squirrel.SelectBuilder,
) ([]*PerfCase, error) {
	query, values, err := selectBuilder.ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*PerfCase
	err = m.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

func (m *customPerfCaseModel) FindByDataID(ctx context.Context, projectID, dataID string) ([]*PerfCase, error) {
	return m.FindNoCacheByQuery(
		ctx, m.SelectBuilder().Where("`project_id` = ? AND `perf_data_id` = ?", projectID, dataID),
	)
}

func (m *customPerfCaseModel) FindAllByPlanID(ctx context.Context, projectID, planID string) ([]*PerfCase, error) {
	/*
		SQL:
		SELECT t1.*
		FROM `perf_case` AS t1
		INNER JOIN `perf_plan_reference_relationship` AS t2 ON
		    t1.`project_id` = t2.`project_id` AND
		    t2.`reference_type` = 'PERF_CASE' AND
		    t1.`case_id` = t2.`reference_id` AND
		    t1.`deleted` = t2.`deleted`
		WHERE t1.`project_id` = ?
		  AND t2.`plan_id` = ?
		  AND t1.`deleted` = ?
	*/
	return m.FindNoCacheByQuery(
		ctx, squirrel.Select(utils.AddTableNameToFields("t1", m.Fields())...).
			From(m.table+" AS t1").
			InnerJoin(
				perfPlanReferenceRelationshipTableName+" AS t2 ON t1.`project_id` = t2.`project_id` AND t2.`reference_type` = ? AND t1.`case_id` = t2.`reference_id` AND t1.`deleted` = t2.`deleted`",
				common.ConstReferenceTypePerfCase,
			).Where(
			"t1.`project_id` = ? AND t2.`plan_id` = ? AND t1.`deleted` = ?", projectID, planID, constants.NotDeleted,
		),
	)
}
