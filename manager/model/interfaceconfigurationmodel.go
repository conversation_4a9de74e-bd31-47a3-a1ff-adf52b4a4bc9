package model

import (
	"context"
	"database/sql"
	"fmt"
	"time"

	"github.com/Masterminds/squirrel"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/types"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/userinfo"
)

var (
	_ InterfaceConfigurationModel = (*customInterfaceConfigurationModel)(nil)

	interfaceConfigurationInsertFields = stringx.Remove(
		interfaceConfigurationFieldNames, "`id`", "`create_time`", "`update_time`", "`deleted_by`", "`created_at`",
		"`updated_at`", "`deleted_at`",
	)
)

type (
	// InterfaceConfigurationModel is an interface to be customized, add more methods here,
	// and implement the added methods in customInterfaceConfigurationModel.
	InterfaceConfigurationModel interface {
		interfaceConfigurationModel
		types.DBModel

		Trans(ctx context.Context, fn func(context context.Context, session sqlx.Session) error) error
		InsertBuilder(data *InterfaceConfiguration) squirrel.InsertBuilder
		UpdateBuilder(data *InterfaceConfiguration) squirrel.UpdateBuilder
		SelectBuilder() squirrel.SelectBuilder
		SelectCountBuilder() squirrel.SelectBuilder
		FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error)
		FindNoCacheByQuery(ctx context.Context, builder squirrel.SelectBuilder) ([]*InterfaceConfiguration, error)

		FindConfigByDocumentId(ctx context.Context, projectId, documentId string) ([]*InterfaceConfiguration, error)
		RemoveByDocumentId(ctx context.Context, session sqlx.Session, projectId, documentId string) (sql.Result, error)
	}

	customInterfaceConfigurationModel struct {
		*defaultInterfaceConfigurationModel
	}
)

// NewInterfaceConfigurationModel returns a model for the database table.
func NewInterfaceConfigurationModel(conn sqlx.SqlConn, c cache.CacheConf) InterfaceConfigurationModel {
	return &customInterfaceConfigurationModel{
		defaultInterfaceConfigurationModel: newInterfaceConfigurationModel(conn, c),
	}
}

func (m *customInterfaceConfigurationModel) Table() string {
	return m.table
}

func (m *customInterfaceConfigurationModel) Fields() []string {
	return interfaceConfigurationFieldNames
}

func (m *customInterfaceConfigurationModel) Trans(
	ctx context.Context, fn func(context context.Context, session sqlx.Session) error,
) error {
	return m.TransactCtx(ctx, fn)
}

func (m *customInterfaceConfigurationModel) InsertBuilder(data *InterfaceConfiguration) squirrel.InsertBuilder {
	return squirrel.Insert(m.table).Columns(interfaceConfigurationInsertFields...).Values(
		data.ProjectId, data.DocumentId, data.ConfigId, data.Name, data.Description, data.Path, data.Method, data.Data,
		data.InputParameters, data.OutputParameters, data.Deleted, data.CreatedBy, data.UpdatedBy,
	)
}

func (m *customInterfaceConfigurationModel) UpdateBuilder(data *InterfaceConfiguration) squirrel.UpdateBuilder {
	eq := squirrel.Eq{
		"`name`":              data.Name,
		"`description`":       data.Description,
		"`path`":              data.Path,
		"`method`":            data.Method,
		"`data`":              data.Data,
		"`input_parameters`":  data.InputParameters,
		"`output_parameters`": data.OutputParameters,
		"`deleted`":           data.Deleted,
		"`updated_by`":        data.UpdatedBy,
		"`deleted_by`":        data.DeletedBy,
		"`deleted_at`":        data.DeletedAt,
	}
	return squirrel.Update(m.table).SetMap(eq).Where("`id` = ?", data.Id)
}

func (m *customInterfaceConfigurationModel) SelectBuilder() squirrel.SelectBuilder {
	return squirrel.Select(interfaceConfigurationFieldNames...).Where(
		"`deleted` = ?", constants.NotDeleted,
	).From(m.table)
}

func (m *customInterfaceConfigurationModel) SelectCountBuilder() squirrel.SelectBuilder {
	return squirrel.Select("count(*)").Where("`deleted` = ?", constants.NotDeleted).From(m.table)
}

func (m *customInterfaceConfigurationModel) FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (
	int64, error,
) {
	query, values, err := countBuilder.ToSql()
	if err != nil {
		return 0, err
	}

	var resp int64
	err = m.QueryRowNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return 0, err
	}
}

func (m *customInterfaceConfigurationModel) FindNoCacheByQuery(
	ctx context.Context, builder squirrel.SelectBuilder,
) ([]*InterfaceConfiguration, error) {
	query, values, err := builder.ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*InterfaceConfiguration
	err = m.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

func (m *customInterfaceConfigurationModel) FindConfigByDocumentId(
	ctx context.Context, projectId, documentId string,
) ([]*InterfaceConfiguration, error) {
	sb := m.SelectBuilder().Where("`project_id` = ? AND `document_id` = ?", projectId, documentId)

	return m.FindNoCacheByQuery(ctx, sb)
}

func (m *customInterfaceConfigurationModel) RemoveByDocumentId(
	ctx context.Context, session sqlx.Session, projectId, documentId string,
) (sql.Result, error) {
	keys := m.getKeysByDocumentId(ctx, projectId, documentId)

	return m.ExecCtx(
		ctx, func(ctx context.Context, conn sqlx.SqlConn) (sql.Result, error) {
			/*
				SQL:
				UPDATE `interface_configuration`
				SET `deleted` = ?, `deleted_by` = ?, `deleted_at` = ?
				WHERE `project_id` = ?
				  AND `document_id` = ?
			*/
			stmt, values, err := squirrel.Update(m.table).
				SetMap(
					squirrel.Eq{
						"`deleted`":    constants.HasDeleted,
						"`deleted_by`": userinfo.FromContext(ctx).Account,
						"`deleted_at`": time.Now(),
					},
				).
				Where("`project_id` = ? AND `document_id` = ?", projectId, documentId).
				ToSql()
			if err != nil {
				return nil, err
			}

			if session != nil {
				return session.ExecCtx(ctx, stmt, values...)
			}
			return conn.ExecCtx(ctx, stmt, values...)
		}, keys...,
	)
}

func (m *customInterfaceConfigurationModel) getKeysByDocumentId(
	ctx context.Context, projectId, documentId string,
) []string {
	sb := m.SelectBuilder().Where("`project_id` = ? AND `document_id` = ?", projectId, documentId)
	cs, err := m.FindNoCacheByQuery(ctx, sb)
	if err != nil {
		return make([]string, 0)
	}

	keys := make([]string, 0, len(cs)*2)
	for _, c := range cs {
		keys = append(
			keys,
			fmt.Sprintf("%s%v", cacheManagerInterfaceConfigurationIdPrefix, c.Id),
			fmt.Sprintf(
				"%s%v:%v:%v", cacheManagerInterfaceConfigurationProjectIdDocumentIdConfigIdPrefix, c.ProjectId,
				c.DocumentId, c.ConfigId,
			),
		)
	}

	return keys
}
