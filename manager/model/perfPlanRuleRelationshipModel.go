package model

import (
	"context"
	"database/sql"
	"fmt"
	"time"

	"github.com/Masterminds/squirrel"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/types"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/userinfo"
)

var (
	_ PerfPlanRuleRelationshipModel = (*customPerfPlanRuleRelationshipModel)(nil)

	perfPlanRuleRelationshipInsertFields = stringx.Remove(
		perfPlanRuleRelationshipFieldNames, "`id`", "`create_time`", "`update_time`", "`deleted_by`", "`created_at`",
		"`updated_at`", "`deleted_at`",
	)
)

type (
	// PerfPlanRuleRelationshipModel is an interface to be customized, add more methods here,
	// and implement the added methods in customPerfPlanRuleRelationshipModel.
	PerfPlanRuleRelationshipModel interface {
		perfPlanRuleRelationshipModel
		types.DBModel

		Trans(ctx context.Context, fn func(context context.Context, session sqlx.Session) error) error
		InsertBuilder(data *PerfPlanRuleRelationship) squirrel.InsertBuilder
		UpdateBuilder(data *PerfPlanRuleRelationship) squirrel.UpdateBuilder
		SelectBuilder() squirrel.SelectBuilder
		SelectCountBuilder() squirrel.SelectBuilder
		FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error)
		FindNoCacheByQuery(ctx context.Context, selectBuilder squirrel.SelectBuilder) (
			[]*PerfPlanRuleRelationship, error,
		)

		FindByRuleID(ctx context.Context, projectID, ruleID string) ([]*PerfPlanRuleRelationship, error)
		FindByPlanID(ctx context.Context, projectID, planID string) ([]*PerfPlanRuleRelationship, error)
		RemoveByPlanID(ctx context.Context, session sqlx.Session, projectID, planID string) (sql.Result, error)
	}

	customPerfPlanRuleRelationshipModel struct {
		*defaultPerfPlanRuleRelationshipModel

		conn sqlx.SqlConn
	}
)

// NewPerfPlanRuleRelationshipModel returns a model for the database table.
func NewPerfPlanRuleRelationshipModel(
	conn sqlx.SqlConn, c cache.CacheConf, opts ...cache.Option,
) PerfPlanRuleRelationshipModel {
	return &customPerfPlanRuleRelationshipModel{
		defaultPerfPlanRuleRelationshipModel: newPerfPlanRuleRelationshipModel(conn, c, opts...),
		conn:                                 conn,
	}
}

func (m *customPerfPlanRuleRelationshipModel) Table() string {
	return m.table
}

func (m *customPerfPlanRuleRelationshipModel) Fields() []string {
	return perfPlanRuleRelationshipFieldNames
}

func (m *customPerfPlanRuleRelationshipModel) Trans(
	ctx context.Context, fn func(context context.Context, session sqlx.Session) error,
) error {
	return m.TransactCtx(ctx, fn)
}

func (m *customPerfPlanRuleRelationshipModel) InsertBuilder(data *PerfPlanRuleRelationship) squirrel.InsertBuilder {
	return squirrel.Insert(m.table).Columns(perfPlanRuleRelationshipInsertFields...).Values(
		data.ProjectId, data.PlanId, data.RuleId, data.Deleted, data.CreatedBy, data.UpdatedBy,
	)
}

func (m *customPerfPlanRuleRelationshipModel) UpdateBuilder(data *PerfPlanRuleRelationship) squirrel.UpdateBuilder {
	eq := squirrel.Eq{
		"`deleted`":    data.Deleted,
		"`updated_by`": data.UpdatedBy,
		"`deleted_by`": data.DeletedBy,
		"`deleted_at`": data.DeletedAt,
	}
	return squirrel.Update(m.table).SetMap(eq).Where("`id` = ?", data.Id)
}

func (m *customPerfPlanRuleRelationshipModel) SelectBuilder() squirrel.SelectBuilder {
	return squirrel.Select(perfPlanRuleRelationshipFieldNames...).Where(
		"`deleted` = ?", constants.NotDeleted,
	).From(m.table)
}

func (m *customPerfPlanRuleRelationshipModel) SelectCountBuilder() squirrel.SelectBuilder {
	return squirrel.Select("count(*)").Where("`deleted` = ?", constants.NotDeleted).From(m.table)
}

func (m *customPerfPlanRuleRelationshipModel) FindCount(
	ctx context.Context, countBuilder squirrel.SelectBuilder,
) (int64, error) {
	query, values, err := countBuilder.ToSql()
	if err != nil {
		return 0, err
	}

	var resp int64
	err = m.QueryRowNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return 0, err
	}
}

func (m *customPerfPlanRuleRelationshipModel) FindNoCacheByQuery(
	ctx context.Context, selectBuilder squirrel.SelectBuilder,
) ([]*PerfPlanRuleRelationship, error) {
	query, values, err := selectBuilder.ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*PerfPlanRuleRelationship
	err = m.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

func (m *customPerfPlanRuleRelationshipModel) FindByRuleID(
	ctx context.Context, projectID, ruleID string,
) ([]*PerfPlanRuleRelationship, error) {
	/*
		SQL:
		SELECT *
		FROM `perf_plan_rule_relationship`
		WHERE `project_id` = ?
		  AND `rule_id` = ?
		  AND `deleted` = ?
	*/

	return m.FindNoCacheByQuery(ctx, m.SelectBuilder().Where("`project_id` = ? AND `rule_id` = ?", projectID, ruleID))
}

func (m *customPerfPlanRuleRelationshipModel) FindByPlanID(
	ctx context.Context, projectID, planID string,
) ([]*PerfPlanRuleRelationship, error) {
	/*
		SQL:
		SELECT *
		FROM `perf_plan_rule_relationship`
		WHERE `project_id` = ?
		  AND `plan_id` = ?
		  AND `deleted` = ?
	*/

	return m.FindNoCacheByQuery(ctx, m.SelectBuilder().Where("`project_id` = ? AND `plan_id` = ?", projectID, planID))
}

func (m *customPerfPlanRuleRelationshipModel) RemoveByPlanID(
	ctx context.Context, session sqlx.Session, projectID, planID string,
) (sql.Result, error) {
	keys := m.getKeysByPlanID(ctx, projectID, planID)

	return m.ExecCtx(
		ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
			/*
				SQL:
				UPDATE `perf_plan_rule_relationship`
				SET `deleted` = ?, `deleted_by` = ?, `deleted_at` = ?
				WHERE `project_id` = ?
				  AND `plan_id` = ?
			*/

			stmt, values, err := squirrel.Update(m.table).
				SetMap(
					squirrel.Eq{
						"`deleted`":    constants.HasDeleted,
						"`deleted_by`": userinfo.FromContext(ctx).Account,
						"`deleted_at`": time.Now(),
					},
				).
				Where("`project_id` = ? AND `plan_id` = ?", projectID, planID).
				ToSql()
			if err != nil {
				return nil, err
			}

			if session != nil {
				return session.ExecCtx(ctx, stmt, values...)
			}
			return conn.ExecCtx(ctx, stmt, values...)
		}, keys...,
	)
}

func (m *customPerfPlanRuleRelationshipModel) getKeysByPlanID(ctx context.Context, projectID, planID string) []string {
	cs, err := m.FindByPlanID(ctx, projectID, planID)
	if err != nil {
		return make([]string, 0)
	}

	keys := make([]string, 0, len(cs))
	for _, c := range cs {
		keys = append(keys, fmt.Sprintf("%s%v", cacheManagerPerfPlanRuleRelationshipIdPrefix, c.Id))
	}

	return keys
}
