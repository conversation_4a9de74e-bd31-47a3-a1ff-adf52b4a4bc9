package model

import (
	"context"
	"database/sql"
	"fmt"
	"time"

	"github.com/Masterminds/squirrel"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/types"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/userinfo"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/utils"
)

var (
	_ ApiCaseReferenceRelationshipModel = (*customApiCaseReferenceRelationshipModel)(nil)

	apiCaseReferenceRelationshipInsertFields = stringx.Remove(
		apiCaseReferenceRelationshipFieldNames, "`id`", "`create_time`", "`update_time`", "`deleted_by`",
		"`created_at`", "`updated_at`", "`deleted_at`",
	)
)

type (
	// ApiCaseReferenceRelationshipModel is an interface to be customized, add more methods here,
	// and implement the added methods in customApiCaseReferenceRelationshipModel.
	ApiCaseReferenceRelationshipModel interface {
		apiCaseReferenceRelationshipModel
		types.DBModel

		Trans(ctx context.Context, fn func(context context.Context, session sqlx.Session) error) error
		InsertBuilder(data *ApiCaseReferenceRelationship) squirrel.InsertBuilder
		UpdateBuilder(data *ApiCaseReferenceRelationship) squirrel.UpdateBuilder
		SelectBuilder() squirrel.SelectBuilder
		SelectCountBuilder() squirrel.SelectBuilder
		FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error)
		FindNoCacheByQuery(ctx context.Context, builder squirrel.SelectBuilder) ([]*ApiCaseReferenceRelationship, error)

		BatchInsertTX(ctx context.Context, session sqlx.Session, datas []*ApiCaseReferenceRelationship) (
			sql.Result, error,
		)
		FindReferenceByReference(
			ctx context.Context, projectId, referenceType, referenceId string,
		) ([]*ApiCaseReferenceRelationship, error)
		FindReferenceByCaseId(
			ctx context.Context, acm ApiCaseModel, projectId, caseId string,
		) ([]*ApiCaseReferenceRelationship, error)
		RemoveByReferenceId(
			ctx context.Context, session sqlx.Session, projectId, referenceType, referenceId string,
		) (sql.Result, error)
	}

	customApiCaseReferenceRelationshipModel struct {
		*defaultApiCaseReferenceRelationshipModel
	}
)

// NewApiCaseReferenceRelationshipModel returns a model for the database table.
func NewApiCaseReferenceRelationshipModel(conn sqlx.SqlConn, c cache.CacheConf) ApiCaseReferenceRelationshipModel {
	return &customApiCaseReferenceRelationshipModel{
		defaultApiCaseReferenceRelationshipModel: newApiCaseReferenceRelationshipModel(conn, c),
	}
}

func (m *customApiCaseReferenceRelationshipModel) Table() string {
	return m.table
}

func (m *customApiCaseReferenceRelationshipModel) Fields() []string {
	return apiCaseReferenceRelationshipFieldNames
}

func (m *customApiCaseReferenceRelationshipModel) Trans(
	ctx context.Context, fn func(context context.Context, session sqlx.Session) error,
) error {
	return m.TransactCtx(ctx, fn)
}

func (m *customApiCaseReferenceRelationshipModel) InsertBuilder(data *ApiCaseReferenceRelationship) squirrel.InsertBuilder {
	return squirrel.Insert(m.table).Columns(apiCaseReferenceRelationshipInsertFields...).Values(
		data.ProjectId, data.ReferenceType, data.ReferenceId, data.CaseId, data.Deleted, data.CreatedBy, data.UpdatedBy,
	)
}

func (m *customApiCaseReferenceRelationshipModel) UpdateBuilder(data *ApiCaseReferenceRelationship) squirrel.UpdateBuilder {
	eq := squirrel.Eq{
		"`deleted`":    data.Deleted,
		"`updated_by`": data.UpdatedBy,
		"`deleted_by`": data.DeletedBy,
		"`deleted_at`": data.DeletedAt,
	}
	return squirrel.Update(m.table).SetMap(eq).Where("`id` = ?", data.Id)
}

func (m *customApiCaseReferenceRelationshipModel) SelectBuilder() squirrel.SelectBuilder {
	return squirrel.Select(apiCaseReferenceRelationshipFieldNames...).Where(
		"`deleted` = ?", constants.NotDeleted,
	).From(m.table)
}

func (m *customApiCaseReferenceRelationshipModel) SelectCountBuilder() squirrel.SelectBuilder {
	return squirrel.Select("count(*)").Where("`deleted` = ?", constants.NotDeleted).From(m.table)
}

func (m *customApiCaseReferenceRelationshipModel) FindCount(
	ctx context.Context, countBuilder squirrel.SelectBuilder,
) (int64, error) {
	query, values, err := countBuilder.ToSql()
	if err != nil {
		return 0, err
	}

	var resp int64
	err = m.QueryRowNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return 0, err
	}
}

func (m *customApiCaseReferenceRelationshipModel) FindNoCacheByQuery(
	ctx context.Context, builder squirrel.SelectBuilder,
) ([]*ApiCaseReferenceRelationship, error) {
	query, values, err := builder.ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*ApiCaseReferenceRelationship
	err = m.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

func (m *customApiCaseReferenceRelationshipModel) BatchInsertTX(
	ctx context.Context, session sqlx.Session, datas []*ApiCaseReferenceRelationship,
) (sql.Result, error) {
	dataLen := len(datas)
	if dataLen == 0 {
		return nil, nil
	} else if dataLen == 1 {
		return m.Insert(ctx, session, datas[0])
	}

	ib := squirrel.Insert(m.table).Columns(apiCaseReferenceRelationshipInsertFields...)

	for _, data := range datas {
		ib = ib.Values(
			data.ProjectId, data.ReferenceType, data.ReferenceId, data.CaseId, data.Deleted, data.CreatedBy,
			data.UpdatedBy,
		)
	}

	return m.ExecCtx(
		ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
			query, values, err := ib.ToSql()
			if err != nil {
				return nil, err
			}
			if session != nil {
				return session.ExecCtx(ctx, query, values...)
			}
			return conn.ExecCtx(ctx, query, values...)
		},
	)
}

// 通过suite_id查找集合下的用例
func (m *customApiCaseReferenceRelationshipModel) FindReferenceByReference(
	ctx context.Context, projectId, referenceType, referenceId string,
) ([]*ApiCaseReferenceRelationship, error) {
	sb := m.SelectBuilder().Where(
		"`project_id` = ? AND `reference_type` = ? AND `reference_id` = ?", projectId, referenceType, referenceId,
	)

	return m.FindNoCacheByQuery(ctx, sb)
}

// 通过case_id查找引用的集合，并确认case_id在api_case表中的有效性
func (m *customApiCaseReferenceRelationshipModel) FindReferenceByCaseId(
	ctx context.Context, acm ApiCaseModel, projectId, caseId string,
) ([]*ApiCaseReferenceRelationship, error) {
	/*
		SQL
		SELECT t1.*
		FROM `api_case_reference_relationship` AS t1, `api_case` AS t2
		WHERE t1.`project_id` = t2.`project_id`
		  AND t1.`case_id` = t2.`case_id`
		  AND t1.`project_id` = ?
		  AND t1.`case_id` = ?
		  AND t1.`deleted` = ?
		  AND t2.`latest` = ?
		  AND t2.`deleted` = ?
	*/

	sb := squirrel.Select(utils.AddTableNameToFields("t1", apiCaseReferenceRelationshipFieldNames)...).
		From(fmt.Sprintf("%s AS t1, %s AS t2", m.table, acm.Table())).
		Where(
			"t1.`project_id` = t2.`project_id` AND t1.`case_id` = t2.`case_id` AND t1.`project_id` = ? AND t1.`case_id` = ? AND t1.`deleted` = ? AND t2.`latest` = ? AND t2.`deleted` = ?",
			projectId, caseId, constants.NotDeleted, constants.IsLatestVersion, constants.NotDeleted,
		)

	return m.FindNoCacheByQuery(ctx, sb)
}

func (m *customApiCaseReferenceRelationshipModel) RemoveByReferenceId(
	ctx context.Context, session sqlx.Session, projectId, referenceType, referenceId string,
) (sql.Result, error) {
	keys := m.getKeysByReferenceId(ctx, projectId, referenceType, referenceId)

	return m.ExecCtx(
		ctx, func(ctx context.Context, conn sqlx.SqlConn) (sql.Result, error) {
			/*
				SQL:
				UPDATE `api_case_reference_relationship`
				SET `deleted` = ?, `deleted_by` = ?, `deleted_at` = ?
				WHERE `project_id` = ?
				  AND `reference_type` = ?
				  AND `reference_id` = ?
			*/
			stmt, values, err := squirrel.Update(m.table).
				SetMap(
					squirrel.Eq{
						"`deleted`":    constants.HasDeleted,
						"`deleted_by`": userinfo.FromContext(ctx).Account,
						"`deleted_at`": time.Now(),
					},
				).
				Where(
					"`project_id` = ? AND `reference_type` = ? AND `reference_id` = ?",
					projectId, referenceType, referenceId,
				).
				ToSql()
			if err != nil {
				return nil, err
			}

			if session != nil {
				return session.ExecCtx(ctx, stmt, values...)
			}
			return conn.ExecCtx(ctx, stmt, values...)
		}, keys...,
	)
}

func (m *customApiCaseReferenceRelationshipModel) getKeysByReferenceId(
	ctx context.Context, projectId, referenceType, referenceId string,
) []string {
	cs, err := m.FindReferenceByReference(ctx, projectId, referenceType, referenceId)
	if err != nil {
		return make([]string, 0)
	}

	keys := make([]string, 0, len(cs))
	for _, c := range cs {
		keys = append(keys, fmt.Sprintf("%s%v", cacheManagerApiCaseReferenceRelationshipIdPrefix, c.Id))
	}

	return keys
}
