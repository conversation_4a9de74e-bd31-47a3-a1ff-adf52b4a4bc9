package model

import (
	"context"
	"fmt"

	"github.com/Masterminds/squirrel"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlc"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/types"
)

var (
	_ AdvancedSearchFieldModel = (*customAdvancedSearchFieldModel)(nil)

	advancedSearchFieldInsertFields = stringx.Remove(advancedSearchFieldFieldNames, "`id`", "`create_time`", "`update_time`", "`deleted_by`", "`created_at`", "`updated_at`", "`deleted_at`")
)

type (
	// AdvancedSearchFieldModel is an interface to be customized, add more methods here,
	// and implement the added methods in customAdvancedSearchFieldModel.
	AdvancedSearchFieldModel interface {
		advancedSearchFieldModel
		types.DBModel

		Trans(ctx context.Context, fn func(context context.Context, session sqlx.Session) error) error
		InsertBuilder(data *AdvancedSearchField) squirrel.InsertBuilder
		UpdateBuilder(data *AdvancedSearchField) squirrel.UpdateBuilder
		SelectBuilder() squirrel.SelectBuilder
		SelectCountBuilder() squirrel.SelectBuilder
		FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error)
		FindNoCacheByQuery(ctx context.Context, selectBuilder squirrel.SelectBuilder) ([]*AdvancedSearchField, error)
		FindSearchFieldBySceneType(ctx context.Context, req FindSearchFieldBySceneTypeReq) ([]*AdvancedSearchField, error)
	}

	customAdvancedSearchFieldModel struct {
		*defaultAdvancedSearchFieldModel
	}
)

// NewAdvancedSearchFieldModel returns a model for the database table.
func NewAdvancedSearchFieldModel(conn sqlx.SqlConn, c cache.CacheConf) AdvancedSearchFieldModel {
	return &customAdvancedSearchFieldModel{
		defaultAdvancedSearchFieldModel: newAdvancedSearchFieldModel(conn, c),
	}
}

func (m *customAdvancedSearchFieldModel) Table() string {
	return m.table
}

func (m *customAdvancedSearchFieldModel) Fields() []string {
	return advancedSearchFieldFieldNames
}

func (m *customAdvancedSearchFieldModel) Trans(ctx context.Context, fn func(context context.Context, session sqlx.Session) error) error {
	return m.TransactCtx(ctx, fn)
}

func (m *customAdvancedSearchFieldModel) InsertBuilder(data *AdvancedSearchField) squirrel.InsertBuilder {
	return squirrel.Insert(m.table).Columns(advancedSearchFieldInsertFields...).Values(data.ProjectId, data.SceneType, data.FrontName, data.FieldName)
}

func (m *customAdvancedSearchFieldModel) UpdateBuilder(data *AdvancedSearchField) squirrel.UpdateBuilder {
	eq := squirrel.Eq{
		"`project_id`": data.ProjectId,
		"`scene_type`": data.SceneType,
		"`front_name`": data.FrontName,
		"`field_name`": data.FieldName,
		"`deleted`":    data.Deleted,
	}
	return squirrel.Update(m.table).SetMap(eq).Where("`id` = ?", data.Id)
}

func (m *customAdvancedSearchFieldModel) SelectBuilder() squirrel.SelectBuilder {
	return squirrel.Select(advancedSearchFieldFieldNames...).Where("`deleted` = ?", constants.NotDeleted).From(m.table)
}

func (m *customAdvancedSearchFieldModel) SelectCountBuilder() squirrel.SelectBuilder {
	return squirrel.Select("count(*)").Where("`deleted` = ?", constants.NotDeleted).From(m.table)
}

func (m *customAdvancedSearchFieldModel) FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error) {
	query, values, err := countBuilder.ToSql()
	if err != nil {
		return 0, err
	}

	var resp int64
	err = m.QueryRowNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return 0, err
	}
}

func (m *customAdvancedSearchFieldModel) FindNoCacheByQuery(ctx context.Context, selectBuilder squirrel.SelectBuilder) ([]*AdvancedSearchField, error) {
	query, values, err := selectBuilder.ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*AdvancedSearchField
	err = m.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

func (m *customAdvancedSearchFieldModel) FindSearchFieldBySceneType(ctx context.Context, req FindSearchFieldBySceneTypeReq) ([]*AdvancedSearchField, error) {
	/*
		SQL:
		SELECT `id`,
			   `field_id`,
			   `field_type`,
			   `project_id`,
			   `scene_type`,
			   `front_name`,
			   `field_name`,
			   `deleted`,
			   `created_at`,
			   `updated_at`
		FROM `advanced_search_field`
		WHERE `project_id` = ?
		  AND `scene_type` = ?
		  AND `deleted` = ?
	*/
	fields := []string{
		"`id`",
		"`field_id`",
		"`field_type`",
		"`project_id`",
		"`scene_type`",
		"`front_name`",
		"`field_name`",
		"`deleted`",
		"`created_at`",
		"`updated_at`",
	}
	query, values, err := squirrel.Select(fields...).
		From(fmt.Sprintf("%s ", advancedSearchFieldTableName)).
		Where("`project_id` = ? AND `scene_type` = ? AND `deleted` = ?", req.ProjectId, req.SceneType, constants.NotDeleted).
		ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*AdvancedSearchField

	err = m.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}
