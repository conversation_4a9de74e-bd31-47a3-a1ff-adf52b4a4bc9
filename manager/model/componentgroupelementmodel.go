package model

import (
	"context"
	"database/sql"
	"fmt"
	"time"

	"github.com/Masterminds/squirrel"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/types"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/userinfo"
)

var (
	_ ComponentGroupElementModel = (*customComponentGroupElementModel)(nil)

	componentGroupElementInsertFields = stringx.Remove(
		componentGroupElementFieldNames, "`id`", "`create_time`", "`update_time`", "`deleted_by`", "`created_at`",
		"`updated_at`", "`deleted_at`",
	)
)

type (
	// ComponentGroupElementModel is an interface to be customized, add more methods here,
	// and implement the added methods in customComponentGroupElementModel.
	ComponentGroupElementModel interface {
		componentGroupElementModel
		types.DBModel

		Trans(ctx context.Context, fn func(context context.Context, session sqlx.Session) error) error
		InsertBuilder(data *ComponentGroupElement) squirrel.InsertBuilder
		UpdateBuilder(data *ComponentGroupElement) squirrel.UpdateBuilder
		SelectBuilder() squirrel.SelectBuilder
		SelectCountBuilder() squirrel.SelectBuilder
		FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error)
		FindNoCacheByQuery(ctx context.Context, builder squirrel.SelectBuilder) ([]*ComponentGroupElement, error)

		InsertTX(ctx context.Context, session sqlx.Session, data *ComponentGroupElement) (sql.Result, error)
		UpdateTX(ctx context.Context, session sqlx.Session, data *ComponentGroupElement) (sql.Result, error)
		RemoveByComponentGroupId(
			ctx context.Context, session sqlx.Session, projectId, componentGroupId string,
		) (sql.Result, error)
	}

	customComponentGroupElementModel struct {
		*defaultComponentGroupElementModel
	}
)

// NewComponentGroupElementModel returns a model for the database table.
func NewComponentGroupElementModel(conn sqlx.SqlConn, c cache.CacheConf) ComponentGroupElementModel {
	return &customComponentGroupElementModel{
		defaultComponentGroupElementModel: newComponentGroupElementModel(conn, c),
	}
}

func (m *customComponentGroupElementModel) Table() string {
	return m.table
}

func (m *customComponentGroupElementModel) Fields() []string {
	return componentGroupElementFieldNames
}

func (m *customComponentGroupElementModel) Trans(
	ctx context.Context, fn func(context context.Context, session sqlx.Session) error,
) error {
	return m.TransactCtx(ctx, fn)
}

func (m *customComponentGroupElementModel) InsertBuilder(data *ComponentGroupElement) squirrel.InsertBuilder {
	return squirrel.Insert(m.table).Columns(componentGroupElementInsertFields...).Values(
		data.ProjectId, data.ComponentGroupId, data.Version, data.ElementId, data.ElementType, data.Data, data.Deleted,
		data.CreatedBy, data.UpdatedBy,
	)
}

func (m *customComponentGroupElementModel) UpdateBuilder(data *ComponentGroupElement) squirrel.UpdateBuilder {
	eq := squirrel.Eq{
		"`data`":       data.Data,
		"`deleted`":    data.Deleted,
		"`updated_by`": data.UpdatedBy,
		"`deleted_by`": data.DeletedBy,
		"`deleted_at`": data.DeletedAt,
	}
	return squirrel.Update(m.table).SetMap(eq).Where("`id` = ?", data.Id)
}

func (m *customComponentGroupElementModel) SelectBuilder() squirrel.SelectBuilder {
	return squirrel.Select(componentGroupElementFieldNames...).Where(
		"`deleted` = ?", constants.NotDeleted,
	).From(m.table)
}

func (m *customComponentGroupElementModel) SelectCountBuilder() squirrel.SelectBuilder {
	return squirrel.Select("count(*)").Where("`deleted` = ?", constants.NotDeleted).From(m.table)
}

func (m *customComponentGroupElementModel) FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (
	int64, error,
) {
	query, values, err := countBuilder.ToSql()
	if err != nil {
		return 0, err
	}

	var resp int64
	err = m.QueryRowNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return 0, err
	}
}

func (m *customComponentGroupElementModel) FindNoCacheByQuery(
	ctx context.Context, builder squirrel.SelectBuilder,
) ([]*ComponentGroupElement, error) {
	query, values, err := builder.ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*ComponentGroupElement
	err = m.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

func (m *customComponentGroupElementModel) InsertTX(
	ctx context.Context, session sqlx.Session, data *ComponentGroupElement,
) (sql.Result, error) {
	managerComponentGroupElementIdKey := fmt.Sprintf("%s%v", cacheManagerComponentGroupElementIdPrefix, data.Id)

	return m.ExecCtx(
		ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
			query, values, err := m.InsertBuilder(data).ToSql()
			if err != nil {
				return nil, err
			}
			if session != nil {
				return session.ExecCtx(ctx, query, values...)
			}
			return conn.ExecCtx(ctx, query, values...)
		}, managerComponentGroupElementIdKey,
	)
}

func (m *customComponentGroupElementModel) UpdateTX(
	ctx context.Context, session sqlx.Session, data *ComponentGroupElement,
) (sql.Result, error) {
	managerComponentGroupElementIdKey := fmt.Sprintf("%s%v", cacheManagerComponentGroupElementIdPrefix, data.Id)

	return m.ExecCtx(
		ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
			query, values, err := m.UpdateBuilder(data).ToSql()
			if err != nil {
				return nil, err
			}
			if session != nil {
				return session.ExecCtx(ctx, query, values...)
			}
			return conn.ExecCtx(ctx, query, values...)
		}, managerComponentGroupElementIdKey,
	)
}

func (m *customComponentGroupElementModel) RemoveByComponentGroupId(
	ctx context.Context, session sqlx.Session, projectId, componentGroupId string,
) (sql.Result, error) {
	keys := m.getKeysByComponentGroupId(ctx, projectId, componentGroupId)

	return m.ExecCtx(
		ctx, func(ctx context.Context, conn sqlx.SqlConn) (sql.Result, error) {
			/*
				SQL:
				UPDATE `component_group_element`
				SET `deleted` = 1, `deleted_by` = ?, `deleted_at` = ?
				WHERE `project_id` = ?
				  AND `component_group_id` = ?;
			*/
			stmt, values, err := squirrel.Update(m.table).
				SetMap(
					squirrel.Eq{
						"`deleted`":    constants.HasDeleted,
						"`deleted_by`": userinfo.FromContext(ctx).Account,
						"`deleted_at`": time.Now(),
					},
				).
				Where("`project_id` = ? AND `component_group_id` = ?", projectId, componentGroupId).
				ToSql()
			if err != nil {
				return nil, err
			}

			if session != nil {
				return session.ExecCtx(ctx, stmt, values...)
			}
			return conn.ExecCtx(ctx, stmt, values...)
		}, keys...,
	)
}

func (m *customComponentGroupElementModel) getKeysByComponentGroupId(
	ctx context.Context, projectId, componentGroupId string,
) []string {
	sb := m.SelectBuilder().Where(
		"`project_id` = ? AND `component_group_id` = ?", projectId, componentGroupId, constants.NotDeleted,
	)
	cs, err := m.FindNoCacheByQuery(ctx, sb)
	if err != nil {
		return make([]string, 0)
	}

	keys := make([]string, 0, len(cs))
	for _, c := range cs {
		keys = append(keys, fmt.Sprintf("%s%v", cacheManagerComponentGroupElementIdPrefix, c.Id))
	}

	return keys
}
