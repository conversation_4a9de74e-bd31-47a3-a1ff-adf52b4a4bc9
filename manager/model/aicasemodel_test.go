package model

import (
	"context"
	"fmt"
	"testing"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sqlbuilder/rpc"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

var strGenerateSearchNotReleasedCaseSqlBuilder = `{
  "project_id": "project_id:Kqllt5-9fA-I5UOdhjA5d",
  "pagination": {
    "current_page": 1,
    "page_size": 20
  },
  "condition": {
    "single": null,
    "group": {
      "relationship": "AND",
      "conditions": [
        {
          "single": {
            "field": "name",
            "compare": "LIKE",
            "other": {
              "value": "公会"
            }
          },
          "group": null
        },
        {
          "single": {
            "field": "maintained_by",
            "compare": "IN",
            "in": [
              "T0169"
            ]
          },
          "group": null
        }
      ]
    }
  },
  "sort": [
    {
      "field": "updated_at",
      "order": "DESC"
    }
  ]
}`

func TestGenerateSearchNotReleasedCaseSqlBuilder(t *testing.T) {
	query := new(pb.SearchCaseReq)
	err := protobuf.UnmarshalJSON([]byte(strGenerateSearchNotReleasedCaseSqlBuilder), query)
	if err != nil {
		fmt.Println(err)
		return
	}
	req := SearchNotReleasedCaseReq{
		ProjectId:  query.GetProjectId(),
		Condition:  query.GetCondition(),
		Pagination: query.GetPagination(),
		Sort:       rpc.ConvertSortFields(query.GetSort()),
	}
	userLikeRelationshipModel := NewAICaseModel(MockMysql(), MockCache())
	builder, countBuilder := userLikeRelationshipModel.GenerateSearchNotReleasedCaseSqlBuilder(req)
	byQuery, err := userLikeRelationshipModel.FindNoCacheByQuery(context.Background(), builder.SelectBuilder)
	if err != nil {
		fmt.Println(err)
		return
	}
	fmt.Println(byQuery)
	count, err := userLikeRelationshipModel.FindCount(context.Background(), countBuilder.SelectBuilder)
	if err != nil {
		fmt.Println(err)
		return
	}
	fmt.Println(count)
}
