package model

import (
	"context"
	"database/sql"
	"fmt"
	"time"

	"github.com/Masterminds/squirrel"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sqlbuilder"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/userinfo"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/types"
)

var (
	_ PerfStopRuleModel = (*customPerfStopRuleModel)(nil)

	perfStopRuleInsertFields = stringx.Remove(
		perfStopRuleFieldNames, "`id`", "`create_time`", "`update_time`", "`deleted_by`", "`created_at`",
		"`updated_at`", "`deleted_at`",
	)
)

type (
	// PerfStopRuleModel is an interface to be customized, add more methods here,
	// and implement the added methods in customPerfStopRuleModel.
	PerfStopRuleModel interface {
		perfStopRuleModel
		types.DBModel

		Trans(ctx context.Context, fn func(context context.Context, session sqlx.Session) error) error
		InsertBuilder(data *PerfStopRule) squirrel.InsertBuilder
		UpdateBuilder(data *PerfStopRule) squirrel.UpdateBuilder
		SelectBuilder() squirrel.SelectBuilder
		SelectCountBuilder() squirrel.SelectBuilder
		FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error)
		FindNoCacheByQuery(ctx context.Context, selectBuilder squirrel.SelectBuilder) ([]*PerfStopRule, error)

		FindCountByReq(ctx context.Context, req SearchPerfStopRuleReq) (int64, error)
		FindAllByReq(ctx context.Context, req SearchPerfStopRuleReq) ([]*PerfStopRule, error)

		FindByMetricType(ctx context.Context, projectID, metricType string) ([]*PerfStopRule, error)
		RemoveByRuleID(ctx context.Context, session sqlx.Session, projectID, ruleID string) (sql.Result, error)
	}

	customPerfStopRuleModel struct {
		*defaultPerfStopRuleModel

		conn sqlx.SqlConn
	}
)

// NewPerfStopRuleModel returns a model for the database table.
func NewPerfStopRuleModel(conn sqlx.SqlConn, c cache.CacheConf, opts ...cache.Option) PerfStopRuleModel {
	return &customPerfStopRuleModel{
		defaultPerfStopRuleModel: newPerfStopRuleModel(conn, c, opts...),
		conn:                     conn,
	}
}

func (m *customPerfStopRuleModel) Table() string {
	return m.table
}

func (m *customPerfStopRuleModel) Fields() []string {
	return perfStopRuleFieldNames
}

func (m *customPerfStopRuleModel) Trans(
	ctx context.Context, fn func(context context.Context, session sqlx.Session) error,
) error {
	return m.TransactCtx(ctx, fn)
}

func (m *customPerfStopRuleModel) InsertBuilder(data *PerfStopRule) squirrel.InsertBuilder {
	return squirrel.Insert(m.table).Columns(perfStopRuleInsertFields...).Values(
		data.ProjectId, data.RuleId, data.Name, data.Description, data.MetricType, data.Threshold, data.Duration,
		data.State, data.Deleted, data.CreatedBy, data.UpdatedBy,
	)
}

func (m *customPerfStopRuleModel) UpdateBuilder(data *PerfStopRule) squirrel.UpdateBuilder {
	eq := squirrel.Eq{
		"`name`":        data.Name,
		"`description`": data.Description,
		"`metric_type`": data.MetricType,
		"`threshold`":   data.Threshold,
		"`duration`":    data.Duration,
		"`state`":       data.State,
		"`deleted`":     data.Deleted,
		"`updated_by`":  data.UpdatedBy,
		"`deleted_by`":  data.DeletedBy,
		"`deleted_at`":  data.DeletedAt,
	}
	return squirrel.Update(m.table).SetMap(eq).Where("`id` = ?", data.Id)
}

func (m *customPerfStopRuleModel) SelectBuilder() squirrel.SelectBuilder {
	return squirrel.Select(perfStopRuleFieldNames...).Where("`deleted` = ?", constants.NotDeleted).From(m.table)
}

func (m *customPerfStopRuleModel) SelectCountBuilder() squirrel.SelectBuilder {
	return squirrel.Select("count(*)").Where("`deleted` = ?", constants.NotDeleted).From(m.table)
}

func (m *customPerfStopRuleModel) FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error) {
	query, values, err := countBuilder.ToSql()
	if err != nil {
		return 0, err
	}

	var resp int64
	err = m.QueryRowNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return 0, err
	}
}

func (m *customPerfStopRuleModel) FindNoCacheByQuery(
	ctx context.Context, selectBuilder squirrel.SelectBuilder,
) ([]*PerfStopRule, error) {
	query, values, err := selectBuilder.ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*PerfStopRule
	err = m.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

func (m *customPerfStopRuleModel) FindCountByReq(ctx context.Context, req SearchPerfStopRuleReq) (int64, error) {
	return m.FindCount(
		ctx, sqlbuilder.SearchOptions(
			m.SelectCountBuilder().Where("`project_id` = ?", req.ProjectID),
			sqlbuilder.WithCondition(m, req.Condition),
		),
	)
}

func (m *customPerfStopRuleModel) FindAllByReq(ctx context.Context, req SearchPerfStopRuleReq) (
	[]*PerfStopRule, error,
) {
	return m.FindNoCacheByQuery(
		ctx, sqlbuilder.SearchOptions(
			m.SelectBuilder().Where("`project_id` = ?", req.ProjectID),
			sqlbuilder.WithCondition(m, req.Condition),
			sqlbuilder.WithPagination(m, req.Pagination),
			sqlbuilder.WithSort(m, req.Sort),
		),
	)
}

func (m *customPerfStopRuleModel) FindByMetricType(ctx context.Context, projectID, metricType string) (
	[]*PerfStopRule, error,
) {
	return m.FindNoCacheByQuery(
		ctx, m.SelectBuilder().Where("`project_id` = ? AND `metric_type` = ?", projectID, metricType),
	)
}

func (m *customPerfStopRuleModel) RemoveByRuleID(
	ctx context.Context, session sqlx.Session, projectID, ruleID string,
) (sql.Result, error) {
	keys := m.getKeysByRuleID(ctx, projectID, ruleID)

	return m.ExecCtx(
		ctx, func(ctx context.Context, conn sqlx.SqlConn) (sql.Result, error) {
			/*
				SQL:
				UPDATE `perf_stop_rule`
				SET `deleted` = ?, `deleted_by` = ?, `deleted_at` = ?
				WHERE `project_id` = ?
				  AND `plan_id` = ?
			*/
			stmt, values, err := squirrel.Update(m.table).
				SetMap(
					squirrel.Eq{
						"`deleted`":    constants.HasDeleted,
						"`deleted_by`": userinfo.FromContext(ctx).Account,
						"`deleted_at`": time.Now(),
					},
				).
				Where("`project_id` = ? AND `rule_id` = ?", projectID, ruleID).
				ToSql()
			if err != nil {
				return nil, err
			}

			if session != nil {
				return session.ExecCtx(ctx, stmt, values...)
			}
			return conn.ExecCtx(ctx, stmt, values...)
		}, keys...,
	)
}

func (m *customPerfStopRuleModel) getKeysByRuleID(ctx context.Context, projectID, ruleID string) []string {
	perfStopRule, err := m.FindOneByProjectIdRuleId(ctx, projectID, ruleID)
	if err != nil {
		return make([]string, 0)
	}

	return []string{
		fmt.Sprintf("%s%v", cacheManagerPerfStopRuleIdPrefix, perfStopRule.Id),
		fmt.Sprintf(
			"%s%v:%v", cacheManagerPerfStopRuleProjectIdRuleIdPrefix, perfStopRule.ProjectId, perfStopRule.RuleId,
		),
	}
}
