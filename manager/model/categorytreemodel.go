package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/Masterminds/squirrel"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/types"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/userinfo"
)

var (
	_ CategoryTreeModel = (*customCategoryTreeModel)(nil)

	categoryTreeInsertFields = stringx.Remove(
		categoryTreeFieldNames, "`id`", "`create_time`", "`update_time`", "`deleted_by`", "`created_at`",
		"`updated_at`", "`deleted_at`",
	)
)

type (
	// CategoryTreeModel is an interface to be customized, add more methods here,
	// and implement the added methods in customCategoryTreeModel.
	CategoryTreeModel interface {
		categoryTreeModel
		types.DBModel

		Trans(ctx context.Context, fn func(context context.Context, session sqlx.Session) error) error
		InsertBuilder(data *CategoryTree) squirrel.InsertBuilder
		UpdateBuilder(data *CategoryTree) squirrel.UpdateBuilder
		SelectBuilder() squirrel.SelectBuilder
		SelectCountBuilder() squirrel.SelectBuilder
		FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error)
		FindNoCacheByQuery(ctx context.Context, builder squirrel.SelectBuilder) ([]*CategoryTree, error)

		FindMaxIndexInSameLevel(ctx context.Context, projectId, parentId string) (int64, error)
		FindIndexByProjectIdParentIdChildId(ctx context.Context, projectId, parentId, childId string) (int64, error)
		InsertTree(ctx context.Context, session sqlx.Session, parentId string, data *CategoryTree) (sql.Result, error)
		MoveTree(ctx context.Context, session sqlx.Session, data *CategoryTree, isSameLevel bool) error
		RemoveTree(ctx context.Context, session sqlx.Session, projectId, categoryId string) (sql.Result, error)
		RemoveByNodeId(
			ctx context.Context, session sqlx.Session, cm CategoryModel, projectId, tp, nodeId string,
		) (sql.Result, error)
	}

	customCategoryTreeModel struct {
		*defaultCategoryTreeModel
	}
)

// NewCategoryTreeModel returns a model for the database table.
func NewCategoryTreeModel(conn sqlx.SqlConn, c cache.CacheConf) CategoryTreeModel {
	return &customCategoryTreeModel{
		defaultCategoryTreeModel: newCategoryTreeModel(conn, c),
	}
}

func (m *customCategoryTreeModel) Table() string {
	return m.table
}

func (m *customCategoryTreeModel) Fields() []string {
	return categoryTreeFieldNames
}

func (m *customCategoryTreeModel) Trans(
	ctx context.Context, fn func(context context.Context, session sqlx.Session) error,
) error {
	return m.TransactCtx(ctx, fn)
}

func (m *customCategoryTreeModel) InsertBuilder(data *CategoryTree) squirrel.InsertBuilder {
	return squirrel.Insert(m.table).Columns(categoryTreeInsertFields...).Values(
		data.ProjectId, data.Ancestor, data.Descendant, data.Depth, data.Index, data.Deleted, data.CreatedBy,
		data.UpdatedBy,
	)
}

func (m *customCategoryTreeModel) UpdateBuilder(data *CategoryTree) squirrel.UpdateBuilder {
	eq := squirrel.Eq{
		"`ancestor`":   data.Ancestor,
		"`descendant`": data.Descendant,
		"`depth`":      data.Depth,
		"`index`":      data.Index,
		"`deleted`":    data.Deleted,
		"`updated_by`": data.UpdatedBy,
		"`deleted_by`": data.DeletedBy,
		"`deleted_at`": data.DeletedAt,
	}
	return squirrel.Update(m.table).SetMap(eq).Where("`id` = ?", data.Id)
}

func (m *customCategoryTreeModel) SelectBuilder() squirrel.SelectBuilder {
	return squirrel.Select(categoryTreeFieldNames...).Where("`deleted` = ?", constants.NotDeleted).From(m.table)
}

func (m *customCategoryTreeModel) SelectCountBuilder() squirrel.SelectBuilder {
	return squirrel.Select("count(*)").Where("`deleted` = ?", constants.NotDeleted).From(m.table)
}

func (m *customCategoryTreeModel) FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error) {
	query, values, err := countBuilder.ToSql()
	if err != nil {
		return 0, err
	}

	var resp int64
	err = m.QueryRowNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return 0, err
	}
}

func (m *customCategoryTreeModel) FindNoCacheByQuery(
	ctx context.Context, builder squirrel.SelectBuilder,
) ([]*CategoryTree, error) {
	query, values, err := builder.ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*CategoryTree
	err = m.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

func (m *customCategoryTreeModel) FindMaxIndexInSameLevel(ctx context.Context, projectId, parentId string) (
	int64, error,
) {
	query, values, err := squirrel.Select("max(`index`) AS `index`").Where(
		"`project_id` = ? AND `ancestor` = ? AND `depth` = 1 AND `deleted` = ?", projectId, parentId,
		constants.NotDeleted,
	).From(m.table).ToSql()
	if err != nil {
		return 0, err
	}

	var resp struct {
		Index sql.NullInt64 `db:"index"`
	}
	err = m.QueryRowNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		if resp.Index.Valid {
			return resp.Index.Int64, nil
		}
		return 0, ErrNotFound
	default:
		return 0, err
	}
}

func (m *customCategoryTreeModel) FindIndexByProjectIdParentIdChildId(
	ctx context.Context, projectId, parentId, childId string,
) (int64, error) {
	query, values, err := squirrel.Select("`index`").Where(
		"`project_id` = ? AND `ancestor` = ? AND `descendant` = ? AND `depth` = ? AND `deleted` = ?", projectId,
		parentId, childId, 1, constants.NotDeleted,
	).From(m.table).ToSql()
	if err != nil {
		return 0, err
	}

	var resp struct {
		Index sql.NullInt64 `db:"index"`
	}
	err = m.QueryRowNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		if resp.Index.Valid {
			return resp.Index.Int64, nil
		}
		return 0, ErrNotFound
	default:
		return 0, err
	}
}

func (m *customCategoryTreeModel) InsertTree(
	ctx context.Context, session sqlx.Session, parentId string, data *CategoryTree,
) (sql.Result, error) {
	index := data.Index
	data.Index = 1
	r, err := m.Insert(ctx, session, data)
	if err != nil {
		return r, err
	}

	if parentId != "" {
		var (
			stmt   string
			values []any
		)

		/*
			SQL:
			INSERT INTO `category_tree` (`project_id`, `ancestor`, `descendant`, `depth`, `index`, `deleted`, `created_by`, `updated_by`)
			SELECT `project_id`,
			       `ancestor`,
			       ?                     AS `descendant`,
			       `depth` + 1           AS `depth`,
			       IF(`depth` = 0, ?, 1) AS `index`,
			       `deleted`,
			       ?                     AS `created_by`,
			       ?                     AS `updated_by`
			FROM `category_tree`
			WHERE `project_id` = ?
			  AND `descendant` = ?
			  AND `deleted` = ?;
		*/
		insertFieldsStr := strings.Join(categoryTreeInsertFields, ", ")
		selectFieldsStr := "`project_id`, `ancestor`, ? AS `descendant`, `depth` + 1 AS `depth`, IF(`depth` = 0, ?, 1) AS `index`, `deleted`, ? AS `created_by`, ? AS `updated_by`"
		selectWhereStr := "`project_id` = ? AND `descendant` = ? AND `deleted` = ?"
		stmt = fmt.Sprintf(
			"INSERT INTO %s (%s) SELECT %s FROM %s WHERE %s", m.table, insertFieldsStr, selectFieldsStr, m.table,
			selectWhereStr,
		)
		values = []any{
			data.Descendant, index, data.CreatedBy, data.UpdatedBy, data.ProjectId, parentId, constants.NotDeleted,
		}

		if session != nil {
			_, err = session.ExecCtx(ctx, stmt, values...)
		} else {
			_, err = m.ExecNoCacheCtx(ctx, stmt, values...)
		}
	}

	return r, err
}

func (m *customCategoryTreeModel) MoveTree(
	ctx context.Context, session sqlx.Session, data *CategoryTree, isSameLevel bool,
) error {
	var err error

	// modify the index of younger brother categories to be `index - 1`
	if err = m.updateIndexBeforeMove(ctx, session, data); err != nil {
		return err
	}

	if !isSameLevel {
		// remove all paths through the source category
		if err = m.deleteForCrossLevelMove(ctx, session, data); err != nil {
			return err
		}

		// create all paths through the source category under the new root category
		if err = m.insertForCrossLevelMove(ctx, session, data); err != nil {
			return err
		}
	} else {
		// modify the index of source category
		if err = m.updateForSameLevelMove(ctx, session, data); err != nil {
			return err
		}
	}

	// modify the index of younger brother categories to be `index + 1`
	if err = m.updateIndexAfterMove(ctx, session, data); err != nil {
		return err
	}

	return nil
}

func (m *customCategoryTreeModel) updateIndexBeforeMove(
	ctx context.Context, session sqlx.Session, data *CategoryTree,
) (err error) {
	/*
		SQL:
		UPDATE `category_tree` AS t1
		    INNER JOIN `category_tree` AS t2
		        ON t1.`project_id` = t2.`project_id` AND t1.`ancestor` = t2.`ancestor`
		SET t1.`index` = t1.`index` - 1
		WHERE t1.`project_id` = ?
		  AND t2.`descendant` = ?
		  AND t1.`depth` = ?
		  AND t2.`depth` = ?
		  AND t1.`index` > t2.`index`
		  AND t1.`deleted` = ?
		  AND t2.`deleted` = ?;
	*/
	innerJoinOnStr := "t1.`project_id` = t2.`project_id` AND t1.`ancestor` = t2.`ancestor`"
	updateWhereStr := "t1.`project_id` = ? AND t2.`descendant` = ? AND t1.`depth` = ? AND t2.`depth` = ? AND t1.`index` > t2.`index` AND t1.`deleted` = ? AND t2.`deleted` = ?"
	stmt := fmt.Sprintf(
		"UPDATE %s AS t1 INNER JOIN %s AS t2 ON %s SET t1.`index` = t1.`index` - 1 WHERE %s", m.table, m.table,
		innerJoinOnStr, updateWhereStr,
	)
	values := []any{data.ProjectId, data.Descendant, 1, 1, constants.NotDeleted, constants.NotDeleted}

	if session != nil {
		_, err = session.ExecCtx(ctx, stmt, values...)
	} else {
		_, err = m.ExecNoCacheCtx(ctx, stmt, values...)
	}

	return err
}

func (m *customCategoryTreeModel) deleteForCrossLevelMove(
	ctx context.Context, session sqlx.Session, data *CategoryTree,
) (err error) {
	/*
		SQL:
		DELETE t1
		FROM `category_tree` AS t1
		    INNER JOIN `category_tree` AS t2
		        ON t1.`project_id` = t2.`project_id` AND t1.`descendant` = t2.`descendant`
		    LEFT JOIN `category_tree` AS t3
		        ON t2.`project_id` = t3.`project_id` AND t1.`ancestor` = t3.`descendant` AND t2.`ancestor` = t3.`ancestor`
		WHERE t1.`project_id` = ?
		  AND t2.`ancestor` = ?
		  AND t3.`ancestor` IS NULL
		  AND t1.`deleted` = ?
		  AND t2.`deleted` = ?;
	*/
	innerJoinOnStr := "t1.`project_id` = t2.`project_id` AND t1.`descendant` = t2.`descendant`"
	leftJoinOnStr := "t2.`project_id` = t3.`project_id` AND t1.`ancestor` = t3.`descendant` AND t2.`ancestor` = t3.`ancestor`"
	// 注：这里由于 t3.`ancestor` IS NULL , 因此 WHERE 条件不能加上 AND t3.`deleted` = ?
	deleteWhereStr := "t1.`project_id` = ? AND t2.`ancestor` = ? AND t3.`ancestor` IS NULL AND t1.`deleted` = ? AND t2.`deleted` = ?"
	stmt := fmt.Sprintf(
		"DELETE t1 FROM %s AS t1 INNER JOIN %s AS t2 ON %s LEFT JOIN %s AS t3 ON %s WHERE %s", m.table, m.table,
		innerJoinOnStr, m.table, leftJoinOnStr, deleteWhereStr,
	)
	values := []any{data.ProjectId, data.Descendant, constants.NotDeleted, constants.NotDeleted}

	if session != nil {
		_, err = session.ExecCtx(ctx, stmt, values...)
	} else {
		_, err = m.ExecNoCacheCtx(ctx, stmt, values...)
	}

	return err
}

func (m *customCategoryTreeModel) insertForCrossLevelMove(
	ctx context.Context, session sqlx.Session, data *CategoryTree,
) (err error) {
	/*
		SQL:
		INSERT INTO `category_tree` (`project_id`, `ancestor`, `descendant`, `depth`, `index`, `deleted`, `created_by`, `updated_by`)
		SELECT t1.`project_id`,
		       t1.`ancestor`,
		       t2.`descendant`,
		       t1.`depth` + t2.`depth` + 1               AS `depth`,
		       IF(t1.`depth` + t2.`depth` + 1 = 1, ?, 1) AS `index`,
		       t1.`deleted`,
		       ?                                         AS `created_by`,
		       ?                                         AS `updated_by`
		FROM `category_tree` AS t1,
		     `category_tree` AS t2
		WHERE t1.`project_id` = t2.`project_id`
		  AND t1.`project_id` = ?
		  AND t1.`descendant` = ?
		  AND t2.`ancestor` = ?
		  AND t1.`deleted` = ?
		  AND t2.`deleted` = ?;
	*/
	insertFieldsStr := strings.Join(categoryTreeInsertFields, ", ")
	selectFieldsStr := "t1.`project_id`, t1.`ancestor`, t2.`descendant`, t1.`depth` + t2.`depth` + 1 AS `depth`, IF(t1.`depth` + t2.`depth` + 1 = 1, ?, 1) AS `index`, t1.`deleted`, ? AS `created_by`, ? AS `updated_by`"
	selectWhereStr := "t1.`project_id` = t2.`project_id` AND t1.`project_id` = ? AND t1.`descendant` = ? AND t2.`ancestor` = ? AND t1.`deleted` = ? AND t2.`deleted` = ?"
	stmt := fmt.Sprintf(
		"INSERT INTO %s (%s) SELECT %s FROM %s AS t1, %s AS t2 WHERE %s", m.table, insertFieldsStr, selectFieldsStr,
		m.table, m.table, selectWhereStr,
	)
	values := []any{
		data.Index, data.CreatedBy, data.UpdatedBy, data.ProjectId, data.Ancestor, data.Descendant,
		constants.NotDeleted, constants.NotDeleted,
	}

	if session != nil {
		_, err = session.ExecCtx(ctx, stmt, values...)
	} else {
		_, err = m.ExecNoCacheCtx(ctx, stmt, values...)
	}

	return err
}

func (m *customCategoryTreeModel) updateForSameLevelMove(
	ctx context.Context, session sqlx.Session, data *CategoryTree,
) (err error) {
	/*
		SQL:
		UPDATE `category_tree`
		SET `index` = ?
		WHERE `project_id` = ?
		  AND `ancestor` = ?
		  AND `descendant` = ?
		  AND `depth` = ?
		  AND `deleted` = ?
		  AND `updated_by` = ?;
	*/
	updateWhereStr := "`project_id` = ? AND `ancestor` = ? AND `descendant` = ? AND `depth` = ? AND `deleted` = ? AND `updated_by` = ?"
	stmt := fmt.Sprintf("UPDATE %s SET `index` = ? WHERE %s", m.table, updateWhereStr)
	values := []any{
		data.Index, data.ProjectId, data.Ancestor, data.Descendant, data.Depth, constants.NotDeleted, data.UpdatedBy,
	}

	if session != nil {
		_, err = session.ExecCtx(ctx, stmt, values...)
	} else {
		_, err = m.ExecNoCacheCtx(ctx, stmt, values...)
	}

	return err
}

func (m *customCategoryTreeModel) updateIndexAfterMove(
	ctx context.Context, session sqlx.Session, data *CategoryTree,
) (err error) {
	/*
		SQL:
		UPDATE `category`
		SET `index` = `index` + 1
		WHERE `project_id` = ?
		  AND `ancestor` = ?
		  AND `descendant` != ?
		  AND `depth` = ?
		  AND `index` >= ?
		  AND `deleted` = ?;
	*/
	updateWhereStr := "`project_id` = ? AND `ancestor` = ? AND `descendant` != ? AND `depth` = ? AND `index` >= ? AND `deleted` = ?"
	stmt := fmt.Sprintf("UPDATE %s SET `index` = `index` + 1 WHERE %s", m.table, updateWhereStr)
	values := []any{data.ProjectId, data.Ancestor, data.Descendant, 1, data.Index, constants.NotDeleted}
	if session != nil {
		_, err = session.ExecCtx(ctx, stmt, values...)
	} else {
		_, err = m.ExecNoCacheCtx(ctx, stmt, values...)
	}

	return err
}

func (m *customCategoryTreeModel) RemoveTree(
	ctx context.Context, session sqlx.Session, projectId, categoryId string,
) (sql.Result, error) {
	/*
		SQL:
		UPDATE `category_tree` AS t1
			INNER JOIN `category_tree` AS t2
				ON t1.`project_id` = t2.`project_id` AND t1.`descendant` = t2.`descendant`
		SET t1.`deleted` = 1, t1.`deleted_by` = ?, t1.`deleted_at` = ?
		WHERE t1.`project_id` = ?
		  AND t2.`ancestor` = ?
		  AND t1.`deleted` = ?
		  AND t2.`deleted` = ?;
	*/
	stmt, values, err := squirrel.Update(
		fmt.Sprintf(
			"%s AS t1 INNER JOIN %s AS t2 ON t1.`project_id` = t2.`project_id` AND t1.`descendant` = t2.`descendant`",
			m.table, m.table,
		),
	).
		SetMap(
			squirrel.Eq{
				"t1.`deleted`":    constants.HasDeleted,
				"t1.`deleted_by`": userinfo.FromContext(ctx).Account,
				"t1.`deleted_at`": time.Now(),
			},
		).
		Where(
			"t1.`project_id` = ? AND t2.`ancestor` = ? AND t1.`deleted` = ? AND t2.`deleted` = ?", projectId, categoryId,
			constants.NotDeleted, constants.NotDeleted,
		).
		ToSql()
	if err != nil {
		return nil, err
	}

	if session != nil {
		return session.ExecCtx(ctx, stmt, values...)
	}
	return m.ExecNoCacheCtx(ctx, stmt, values...)
}

func (m *customCategoryTreeModel) RemoveByNodeId(
	ctx context.Context, session sqlx.Session, cm CategoryModel, projectId, tp, nodeId string,
) (sql.Result, error) {
	/*
		SQL:
		UPDATE `category_tree` AS t1
			INNER JOIN `category` AS t2
				ON t1.`project_id` = t2.`project_id` AND t1.`descendant` = t2.`category_id`
		SET t1.`deleted` = ?, t1.`deleted_by` = ?, t1.`deleted_at` = ?
		WHERE t1.`project_id` = ?
		  AND t2.`type` = ?
		  AND t2.`node_id` = ?
		  AND t2.`deleted` = ?;
	*/
	innerJoinOnStr := "t1.`project_id` = t2.`project_id` AND t1.`descendant` = t2.`category_id`"
	updateWhereStr := "t1.`project_id` = ? AND t2.`type` = ? AND t2.`node_id` = ? AND t2.`deleted` = ?"
	stmt := fmt.Sprintf(
		"UPDATE %s AS t1 INNER JOIN %s AS t2 ON %s SET t1.`deleted` = ?, t1.`deleted_by` = ?, t1.`deleted_at` = ? WHERE %s",
		m.table, cm.Table(), innerJoinOnStr, updateWhereStr,
	)
	values := []any{
		constants.HasDeleted, userinfo.FromContext(ctx).Account, time.Now(), projectId, tp, nodeId, constants.NotDeleted,
	}

	if session != nil {
		return session.ExecCtx(ctx, stmt, values...)
	}
	return m.ExecNoCacheCtx(ctx, stmt, values...)
}
