package model

import (
	"context"
	"database/sql"
	"fmt"
	"time"

	"github.com/Masterminds/squirrel"
	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sqlbuilder"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sqlbuilder/rpc"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/types"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/userinfo"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/utils"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
)

var (
	_                        ReviewRecordModel = (*customReviewRecordModel)(nil)
	VirtualReviewRecordModel types.DBModel     = (*virtualReviewRecordModel)(nil)

	reviewRecordInsertFields = stringx.Remove(
		reviewRecordFieldNames, "`id`", "`create_time`", "`update_time`", "`deleted_by`", "`created_at`",
		"`updated_at`", "`deleted_at`",
	)

	fullReviewRecordExtraFieldNames = []string{"`resource_branch`", "`resource_parent_type`", "`resource_name`"}
	virtualReviewRecordFieldNames   = append(reviewRecordFieldNames, fullReviewRecordExtraFieldNames...)
)

type (
	// ReviewRecordModel is an interface to be customized, add more methods here,
	// and implement the added methods in customReviewRecordModel.
	ReviewRecordModel interface {
		reviewRecordModel
		types.DBModel

		Trans(ctx context.Context, fn func(context context.Context, session sqlx.Session) error) error
		InsertBuilder(data *ReviewRecord) squirrel.InsertBuilder
		UpdateBuilder(data *ReviewRecord) squirrel.UpdateBuilder
		SelectBuilder() squirrel.SelectBuilder
		SelectCountBuilder() squirrel.SelectBuilder
		FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error)
		FindNoCacheByQuery(ctx context.Context, selectBuilder squirrel.SelectBuilder) ([]*ReviewRecord, error)

		InsertTX(ctx context.Context, session sqlx.Session, data *ReviewRecord) (sql.Result, error)
		UpdateTX(ctx context.Context, session sqlx.Session, data *ReviewRecord) (sql.Result, error)
		DeleteTX(ctx context.Context, session sqlx.Session, id int64) error

		FindReviewRecordsByResource(
			ctx context.Context, projectID string, resourceType common.ReviewResourceType, resourceID string,
			status []common.ReviewStatus,
		) ([]*ReviewRecord, error)
		RemoveByResource(
			ctx context.Context, session sqlx.Session, projectID string, resourceType common.ReviewResourceType,
			resourceID string,
		) (sql.Result, error)

		FindFullReviewRecordsByQuery(ctx context.Context, selectBuilder squirrel.SelectBuilder) (
			[]*FullReviewRecord, error,
		)
		FindFullReviewRecordByProjectIDReviewID(ctx context.Context, projectID, reviewID string) (
			*FullReviewRecord, error,
		)
		FindFullReviewRecordsByProjectID(
			ctx context.Context, projectID string, status []common.ReviewStatus,
		) ([]*FullReviewRecord, error)
		FindFullReviewRecordsByResource(
			ctx context.Context, projectID string, resourceType common.ReviewResourceType, resourceID string,
			status []common.ReviewStatus,
		) ([]*FullReviewRecord, error)

		GenerateSearchReviewRecordSQLBuilder(req SearchReviewRecordReq) (
			searchReviewRecordSelectBuilder, searchReviewRecordCountBuilder,
		)
		FindCountReviewRecords(ctx context.Context, countBuilder searchReviewRecordCountBuilder) (int64, error)
		FindReviewRecords(
			ctx context.Context, selectBuilder searchReviewRecordSelectBuilder,
		) ([]*FullReviewRecord, error)
	}

	customReviewRecordModel struct {
		*defaultReviewRecordModel

		conn sqlx.SqlConn
	}
)

// NewReviewRecordModel returns a model for the database table.
func NewReviewRecordModel(conn sqlx.SqlConn, c cache.CacheConf, opts ...cache.Option) ReviewRecordModel {
	return &customReviewRecordModel{
		defaultReviewRecordModel: newReviewRecordModel(conn, c, opts...),
		conn:                     conn,
	}
}

func (m *customReviewRecordModel) Table() string {
	return m.table
}

func (m *customReviewRecordModel) Fields() []string {
	return reviewRecordFieldNames
}

func (m *customReviewRecordModel) Trans(
	ctx context.Context, fn func(context context.Context, session sqlx.Session) error,
) error {
	return m.TransactCtx(ctx, fn)
}

func (m *customReviewRecordModel) InsertBuilder(data *ReviewRecord) squirrel.InsertBuilder {
	return squirrel.Insert(m.table).Columns(reviewRecordInsertFields...).Values(
		data.ProjectId, data.ReviewId, data.ResourceType, data.ResourceId, data.ResourceStatus, data.RemarkOfPending,
		data.RemarkOfRevoked, data.RemarkOfReviewed, data.AssignedReviewers, data.Status, data.Deleted, data.CreatedBy,
		data.UpdatedBy,
	)
}

func (m *customReviewRecordModel) UpdateBuilder(data *ReviewRecord) squirrel.UpdateBuilder {
	eq := squirrel.Eq{
		"`remark_of_pending`":  data.RemarkOfPending,
		"`remark_of_revoked`":  data.RemarkOfRevoked,
		"`remark_of_reviewed`": data.RemarkOfReviewed,
		"`assigned_reviewers`": data.AssignedReviewers,
		"`status`":             data.Status,
		"`deleted`":            data.Deleted,
		"`updated_by`":         data.UpdatedBy,
		"`deleted_by`":         data.DeletedBy,
		"`deleted_at`":         data.DeletedAt,
	}
	return squirrel.Update(m.table).SetMap(eq).Where("`id` = ?", data.Id)
}

func (m *customReviewRecordModel) SelectBuilder() squirrel.SelectBuilder {
	return squirrel.Select(reviewRecordFieldNames...).Where("`deleted` = ?", constants.NotDeleted).From(m.table)
}

func (m *customReviewRecordModel) SelectCountBuilder() squirrel.SelectBuilder {
	return squirrel.Select("count(*)").Where("`deleted` = ?", constants.NotDeleted).From(m.table)
}

func (m *customReviewRecordModel) FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error) {
	query, values, err := countBuilder.ToSql()
	if err != nil {
		return 0, err
	}

	var resp int64
	err = m.QueryRowNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return 0, err
	}
}

func (m *customReviewRecordModel) FindNoCacheByQuery(
	ctx context.Context, selectBuilder squirrel.SelectBuilder,
) ([]*ReviewRecord, error) {
	query, values, err := selectBuilder.ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*ReviewRecord
	err = m.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

func (m *customReviewRecordModel) InsertTX(ctx context.Context, session sqlx.Session, data *ReviewRecord) (
	sql.Result, error,
) {
	managerReviewRecordIdKey := fmt.Sprintf("%s%v", cacheManagerReviewRecordIdPrefix, data.Id)

	return m.ExecCtx(
		ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
			query, values, err := m.InsertBuilder(data).ToSql()
			if err != nil {
				return nil, err
			}
			if session != nil {
				return session.ExecCtx(ctx, query, values...)
			}
			return conn.ExecCtx(ctx, query, values...)
		}, managerReviewRecordIdKey,
	)
}

func (m *customReviewRecordModel) UpdateTX(ctx context.Context, session sqlx.Session, data *ReviewRecord) (
	sql.Result, error,
) {
	managerReviewRecordIdKey := fmt.Sprintf("%s%v", cacheManagerReviewRecordIdPrefix, data.Id)

	return m.ExecCtx(
		ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
			query, values, err := m.UpdateBuilder(data).ToSql()
			if err != nil {
				return nil, err
			}
			if session != nil {
				return session.ExecCtx(ctx, query, values...)
			}
			return conn.ExecCtx(ctx, query, values...)
		}, managerReviewRecordIdKey,
	)
}

func (m *customReviewRecordModel) DeleteTX(ctx context.Context, session sqlx.Session, id int64) error {
	managerReviewRecordIdKey := fmt.Sprintf("%s%v", cacheManagerReviewRecordIdPrefix, id)

	_, err := m.ExecCtx(
		ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
			query := fmt.Sprintf("delete from %s where `id` = ?", m.table)
			if session != nil {
				return session.ExecCtx(ctx, query, id)
			}
			return conn.ExecCtx(ctx, query, id)
		}, managerReviewRecordIdKey,
	)

	return err
}

func (m *customReviewRecordModel) FindReviewRecordsByResource(
	ctx context.Context, projectID string, resourceType common.ReviewResourceType, resourceID string,
	status []common.ReviewStatus,
) ([]*ReviewRecord, error) {
	sb := m.SelectBuilder().Where(
		"`project_id` = ? AND `resource_type` = ? AND `resource_id` = ?", projectID, resourceType, resourceID,
	)
	if len(status) != 0 {
		sb.Where(squirrel.Eq{"`status`": status})
	}

	return m.FindNoCacheByQuery(ctx, sb)
}

func (m *customReviewRecordModel) RemoveByResource(
	ctx context.Context, session sqlx.Session, projectID string, resourceType common.ReviewResourceType,
	resourceID string,
) (sql.Result, error) {
	keys := m.getKeysByResource(ctx, projectID, resourceType, resourceID)

	ub := squirrel.Update(m.table).
		SetMap(
			squirrel.Eq{
				"`deleted`":    constants.HasDeleted,
				"`deleted_by`": userinfo.FromContext(ctx).Account,
				"`deleted_at`": time.Now(),
			},
		).
		Where("`project_id` = ? AND `resource_type` = ? AND `resource_id` = ?", projectID, resourceType, resourceID)

	return m.ExecCtx(
		ctx, func(ctx context.Context, conn sqlx.SqlConn) (sql.Result, error) {
			/*
				SQL:
				UPDATE `review_record`
				SET `deleted` = ?, `deleted_by` = ?, `deleted_at` = ?
				WHERE `project_id` = ?
				  AND `resource_type` = ?
				  AND `resource_id` = ?
			*/
			stmt, values, err := ub.ToSql()
			if err != nil {
				return nil, err
			}

			if session != nil {
				return session.ExecCtx(ctx, stmt, values...)
			}
			return conn.ExecCtx(ctx, stmt, values...)
		}, keys...,
	)
}

type virtualReviewRecordModel struct{}

func (m *virtualReviewRecordModel) Table() string {
	return "`virtual_review_record`"
}

func (m *virtualReviewRecordModel) Fields() []string {
	return virtualReviewRecordFieldNames
}

type FullReviewRecord struct {
	ReviewRecord

	ResourceBranch     string `db:"resource_branch"`      // 资源所属分支，如：分类树ID、接口文档ID
	ResourceParentType string `db:"resource_parent_type"` // 资源父类型，如：组件组、用例
	ResourceName       string `db:"resource_name"`        // 资源名称，如：组件组名称、场景用例名称、接口用例名称
}

func (m *customReviewRecordModel) FindFullReviewRecordsByQuery(
	ctx context.Context, selectBuilder squirrel.SelectBuilder,
) ([]*FullReviewRecord, error) {
	query, values, err := selectBuilder.ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*FullReviewRecord
	err = m.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

func (m *customReviewRecordModel) FindFullReviewRecordByProjectIDReviewID(
	ctx context.Context, projectID, reviewID string,
) (*FullReviewRecord, error) {
	/*
		SQL:
		SELECT t1.`project_id`,
		       t1.`review_id`,
		       IF(t1.`resource_type` IN ('SETUP', 'TEARDOWN', 'GROUP'), t2.`category_id`, IF(t1.`resource_type` = 'API_CASE', t3.`category_id`, t4.`document_id`)) AS `resource_branch`,
		       IF(t1.`resource_type` IN ('SETUP', 'TEARDOWN', 'GROUP'), 'COMPONENT_GROUP', 'CASE') AS `resource_parent_type`,
		       t1.`resource_type`,
		       t1.`resource_id`,
		       IF(t1.`resource_type` IN ('SETUP', 'TEARDOWN', 'GROUP'), t2.`name`, IF(t1.`resource_type` = 'API_CASE', t3.`name`, t4.`name`)) AS `resource_name`,
		       t1.`resource_status`,
		       t1.`remark_of_pending`,
		       t1.`remark_of_revoked`,
		       t1.`remark_of_reviewed`,
		       t1.`assigned_reviewers`,
		       t1.`status`,
		       t1.`created_by`,
		       t1.`updated_by`,
		       t1.`created_at`,
		       t1.`updated_at`
		FROM `review_record` AS t1
		LEFT JOIN `component_group` AS t2
		    ON t1.`project_id` = t2.`project_id` AND
		       t1.`resource_type` IN ('SETUP', 'TEARDOWN', 'GROUP') AND
		       t1.`resource_id` = t2.`component_group_id` AND
		       t2.`latest` = 1 AND
		       t1.`deleted` = t2.`deleted`
		LEFT JOIN `api_case` AS t3
		    ON t1.`project_id` = t3.`project_id` AND
		       t1.`resource_type` = 'API_CASE' AND
		       t1.`resource_id` = t3.`case_id` AND
		       t3.`latest` = 1 AND
		       t1.`deleted` = t3.`deleted`
		LEFT JOIN `interface_case` AS t4
		    ON t1.`project_id` = t4.`project_id` AND
		       t1.`resource_type` = 'INTERFACE_CASE' AND
		       t1.`resource_id` = t4.`case_id` AND
		       t4.`latest` = 1 AND
		       t1.`deleted` = t4.`deleted`
		WHERE t1.`project_id` = ?
		  AND t1.`review_id` = ?
		  AND t1.`deleted` = ?
		  AND (t2.`id` IS NOT NULL OR t3.`id` IS NOT NULL OR t4.`id` IS NOT NULL)
	*/

	fields := utils.AddTableNameToFields("t1", reviewRecordFieldNames)

	sb := squirrel.Select(fields...).
		From(m.table+" AS t1").
		Column(
			"IF(t1.`resource_type` IN (?, ?, ?), t2.`category_id`, IF(t1.`resource_type` = ?, t3.`category_id`, t4.`document_id`)) AS `resource_branch`",
			common.ConstReviewResourceTypeSetupComponent, common.ConstReviewResourceTypeTeardownComponent,
			common.ConstReviewResourceTypeBusinessComponent, common.ConstReviewResourceTypeAPICase,
		).
		Column(
			"IF(t1.`resource_type` IN (?, ?, ?), ?, ?) AS `resource_parent_type`",
			common.ConstReviewResourceTypeSetupComponent, common.ConstReviewResourceTypeTeardownComponent,
			common.ConstReviewResourceTypeBusinessComponent, common.ConstReviewResourceTypeComponentGroup,
			common.ConstReviewResourceTypeCase,
		).
		Column(
			"IF(t1.`resource_type` IN (?, ?, ?), t2.`name`, IF(t1.`resource_type` = ?, t3.`name`, t4.`name`)) AS `resource_name`",
			common.ConstReviewResourceTypeSetupComponent, common.ConstReviewResourceTypeTeardownComponent,
			common.ConstReviewResourceTypeBusinessComponent, common.ConstReviewResourceTypeAPICase,
		).
		LeftJoin(
			fmt.Sprintf(
				"%s AS t2 ON t1.`project_id` = t2.`project_id` AND t1.`resource_type` IN (?, ?, ?) AND t1.`resource_id` = t2.`component_group_id` AND t2.`latest` = ? AND t1.`deleted` = t2.`deleted`",
				componentGroupTableName,
			),
			common.ConstReviewResourceTypeSetupComponent, common.ConstReviewResourceTypeTeardownComponent,
			common.ConstReviewResourceTypeBusinessComponent, constants.IsLatestVersion,
		).
		LeftJoin(
			fmt.Sprintf(
				"%s AS t3 ON t1.`project_id` = t3.`project_id` AND t1.`resource_type` = ? AND t1.`resource_id` = t3.`case_id` AND t3.`latest` = ? AND t1.`deleted` = t3.`deleted`",
				apiCaseTableName,
			),
			common.ConstReviewResourceTypeAPICase, constants.IsLatestVersion,
		).
		LeftJoin(
			fmt.Sprintf(
				"%s AS t4 ON t1.`project_id` = t4.`project_id` AND t1.`resource_type` = ? AND t1.`resource_id` = t4.`case_id` AND t4.`latest` = ? AND t1.`deleted` = t4.`deleted`",
				interfaceCaseTableName,
			),
			common.ConstReviewResourceTypeInterfaceCase, constants.IsLatestVersion,
		).
		Where(
			"t1.`project_id` = ? AND t1.`review_id` = ? AND t1.`deleted` = ? AND (t2.`id` IS NOT NULL OR t3.`id` IS NOT NULL OR t4.`id` IS NOT NULL)",
			projectID, reviewID, constants.NotDeleted,
		).
		OrderBy("`updated_at` DESC").
		Limit(1)

	query, values, err := sb.ToSql()
	if err != nil {
		return nil, err
	}

	var resp FullReviewRecord
	err = m.QueryRowNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return &resp, nil
	default:
		return nil, err
	}
}

func (m *customReviewRecordModel) FindFullReviewRecordsByProjectID(
	ctx context.Context, projectID string, status []common.ReviewStatus,
) ([]*FullReviewRecord, error) {
	req := SearchReviewRecordReq{
		ProjectID: projectID,
	}

	in := make([]string, 0, len(status))
	for _, s := range status {
		in = append(in, string(s))
	}
	if len(in) != 0 {
		req.Condition = &rpc.Condition{
			Single: &rpc.SingleCondition{
				Field:   "`status`",
				Compare: constants.DBIn,
				In:      in,
			},
		}
	}

	sb, _ := m.GenerateSearchReviewRecordSQLBuilder(req)
	return m.FindReviewRecords(ctx, sb)
}

func (m *customReviewRecordModel) FindFullReviewRecordsByResource(
	ctx context.Context, projectID string, resourceType common.ReviewResourceType, resourceID string,
	status []common.ReviewStatus,
) ([]*FullReviewRecord, error) {
	/*
		SQL:
		SELECT t1.`project_id`,
		       t1.`review_id`,
		       t2.`category_id` AS `resource_branch`,
		       'COMPONENT_GROUP' AS `resource_parent_type`,
		       t1.`resource_type`,
		       t1.`resource_id`,
		       t2.`name`,
		       t1.`resource_status`,
		       t1.`remark_of_pending`,
		       t1.`remark_of_revoked`,
		       t1.`remark_of_reviewed`,
		       t1.`assigned_reviewers`,
		       t1.`status`,
		       t1.`created_by`,
		       t1.`updated_by`,
		       t1.`created_at`,
		       t1.`updated_at`
		FROM `review_record` AS t1
		INNER JOIN `component_group` AS t2
		    ON t1.`project_id` = t2.`project_id` AND
		       t1.`resource_type` IN ('SETUP', 'TEARDOWN', 'GROUp') AND
		       t1.`resource_id` = t2.`component_group_id` AND
		       t2.`latest` = 1 AND
		       t1.`deleted` = t2.`deleted`
		WHERE t1.`project_id` = ?
		  AND t1.`resource_type` = 'API_CASE'
		  AND t1.`resource_id` = ?
		  AND t1.`deleted` = ?
	*/

	var (
		alias  = "t1"
		fields = utils.AddTableNameToFields(alias, reviewRecordFieldNames)
	)

	sb := squirrel.Select(fields...).
		From(m.table + " AS " + alias).
		Where(
			squirrel.Eq{
				alias + ".`project_id`":    projectID,
				alias + ".`resource_type`": resourceType,
				alias + ".`resource_id`":   resourceID,
				alias + ".`deleted`":       constants.NotDeleted,
			},
		)

	switch resourceType {
	case common.ConstReviewResourceTypeComponentGroup:
		sb = sb.Column("t2.`category_id` AS `resource_branch`").
			Column("? AS `resource_parent_type`", common.ConstReviewResourceTypeCase).
			Column("t2.`name` AS `resource_name`").
			InnerJoin(
				componentGroupTableName+" AS t2 ON t1.`project_id` = t2.`project_id` AND t1.`resource_id` = t2.`component_group_id` AND t2.`latest` = ? AND t1.`deleted` = t2.`deleted`",
				constants.IsLatestVersion,
			)
	case common.ConstReviewResourceTypeAPICase:
		sb = sb.Column("t2.`category_id` AS `resource_branch`").
			Column("? AS `resource_parent_type`", common.ConstReviewResourceTypeCase).
			Column("t2.`name` AS `resource_name`").
			InnerJoin(
				apiCaseTableName+" AS t2 ON t1.`project_id` = t2.`project_id` AND t1.`resource_id` = t2.`case_id` AND t2.`latest` = ? AND t1.`deleted` = t2.`deleted`",
				constants.IsLatestVersion,
			)
	case common.ConstReviewResourceTypeInterfaceCase:
		sb = sb.Column("t2.`document_id` AS `resource_branch`").
			Column("? AS `resource_parent_type`", common.ConstReviewResourceTypeCase).
			Column("t2.`name` AS `resource_name`").
			InnerJoin(
				interfaceCaseTableName+" AS t2 ON t1.`project_id` = t2.`project_id` AND t1.`resource_id` = t2.`case_id` AND t2.`latest` = ? AND t1.`deleted` = t2.`deleted`",
				constants.IsLatestVersion,
			)
	default:
		return nil, errors.WithStack(
			errorx.Errorf(
				errorx.DoesNotSupport, "the resource type[%s] doesn't support", resourceType,
			),
		)
	}

	if len(status) != 0 {
		sb = sb.Where(squirrel.Eq{alias + ".`status`": status})
	}

	return m.FindFullReviewRecordsByQuery(ctx, sb)
}

type searchReviewRecordSelectBuilder struct {
	squirrel.SelectBuilder
}

type searchReviewRecordCountBuilder struct {
	squirrel.SelectBuilder
}

func (m *customReviewRecordModel) GenerateSearchReviewRecordSQLBuilder(req SearchReviewRecordReq) (
	searchReviewRecordSelectBuilder, searchReviewRecordCountBuilder,
) {
	/*
		SQL:
		SELECT t.`project_id`,
		       t.`review_id`,
		       t.`resource_branch`,
		       t.`resource_parent_type`,
		       t.`resource_type`,
		       t.`resource_id`,
		       t.`resource_name`,
		       t.`resource_status`,
		       t.`remark_of_pending`,
		       t.`remark_of_revoked`,
		       t.`remark_of_reviewed`,
		       t.`assigned_reviewers`,
		       t.`status`,
		       t.`created_by`,
		       t.`updated_by`,
		       t.`created_at`,
		       t.`updated_at`
		FROM (
		         SELECT t1.`project_id`,
		                t1.`review_id`,
		                t2.`category_id` AS `resource_branch`,
		                'COMPONENT_GROUP' AS `resource_parent_type`,
		                t1.`resource_type`,
		                t1.`resource_id`,
		                t2.`name` AS `resource_name`,
		                t1.`resource_status`,
		                t1.`remark_of_pending`,
		                t1.`remark_of_revoked`,
		                t1.`remark_of_reviewed`,
		                t1.`assigned_reviewers`,
		                t1.`status`,
		                t1.`created_by`,
		                t1.`updated_by`,
		                t1.`created_at`,
		                t1.`updated_at`
		         FROM `review_record` AS t1
		                  INNER JOIN `component_group` AS t2
		                             ON t1.`project_id` = t2.`project_id` AND
		                                t1.`resource_type` IN ('SETUP', 'TEARDOWN', 'GROUP') AND
		                                t1.`resource_id` = t2.`component_group_id` AND
		                                t2.`latest` = 1 AND
		                                t1.`deleted` = t2.`deleted`
		         WHERE t1.`project_id` = ?
		           AND t1.`deleted` = ?
		         UNION
		         SELECT t1.`project_id`,
		                t1.`review_id`,
		                t2.`category_id` AS `resource_branch`,
		                'CASE' AS `resource_parent_type`,
		                t1.`resource_type`,
		                t1.`resource_id`,
		                t2.`name` AS `resource_name`,
		                t1.`resource_status`,
		                t1.`remark_of_pending`,
		                t1.`remark_of_revoked`,
		                t1.`remark_of_reviewed`,
		                t1.`assigned_reviewers`,
		                t1.`status`,
		                t1.`created_by`,
		                t1.`updated_by`,
		                t1.`created_at`,
		                t1.`updated_at`
		         FROM `review_record` AS t1
		                  INNER JOIN `api_case` AS t2
		                             ON t1.`project_id` = t2.`project_id` AND
		                                t1.`resource_type` = 'API_CASE' AND
		                                t1.`resource_id` = t2.`case_id` AND
		                                t2.`latest` = 1 AND
		                                t1.`deleted` = t2.`deleted`
		         WHERE t1.`project_id` = ?
		           AND t1.`deleted` = ?
		         UNION
		         SELECT t1.`project_id`,
		                t1.`review_id`,
		                t2.`document_id` AS `resource_branch`,
		                'CASE' AS `resource_parent_type`,
		                t1.`resource_type`,
		                t1.`resource_id`,
		                t2.`name` AS `resource_name`,
		                t1.`resource_status`,
		                t1.`remark_of_pending`,
		                t1.`remark_of_revoked`,
		                t1.`remark_of_reviewed`,
		                t1.`assigned_reviewers`,
		                t1.`status`,
		                t1.`created_by`,
		                t1.`updated_by`,
		                t1.`created_at`,
		                t1.`updated_at`
		         FROM `review_record` AS t1
		                  INNER JOIN `interface_case` AS t2
		                             ON t1.`project_id` = t2.`project_id` AND
		                                t1.`resource_type` = 'INTERFACE_CASE' AND
		                                t1.`resource_id` = t2.`case_id` AND
		                                t2.`latest` = 1 AND
		                                t1.`deleted` = t2.`deleted`
		         WHERE t1.`project_id` = ?
		           AND t1.`deleted` = ?
		) AS t
	*/

	// 注意：这里为了简化SQL，减少`join`表数量，所以没有对`category`、`interface_document`等表进行关联查询

	var sb, scb squirrel.SelectBuilder

	fields := utils.AddTableNameToFields("t1", reviewRecordFieldNames)
	componentGroupFields := append(
		fields,
		"t2.`category_id` AS `resource_branch`",
		fmt.Sprintf("'%s' AS `resource_parent_type`", common.ConstReviewResourceTypeComponentGroup),
		"t2.`name` AS `resource_name`",
	)
	apiCaseFields := append(
		fields,
		"t2.`category_id` AS `resource_branch`",
		fmt.Sprintf("'%s' AS `resource_parent_type`", common.ConstReviewResourceTypeCase),
		"t2.`name` AS `resource_name`",
	)
	interfaceCaseFields := append(
		fields,
		"t2.`document_id` AS `resource_branch`",
		fmt.Sprintf("'%s' AS `resource_parent_type`", common.ConstReviewResourceTypeCase),
		"t2.`name` AS `resource_name`",
	)

	rrm := VirtualReviewRecordModel

	sb1 := squirrel.Select(componentGroupFields...).
		From(m.table+" AS t1").
		InnerJoin(
			componentGroupTableName+" AS t2 ON t1.`project_id` = t2.`project_id` AND t1.`resource_type` IN (?, ?, ?) AND t1.`resource_id` = t2.`component_group_id` AND t2.`latest` = ? AND t1.`deleted` = t2.`deleted`",
			common.ConstReviewResourceTypeSetupComponent, common.ConstReviewResourceTypeTeardownComponent,
			common.ConstReviewResourceTypeBusinessComponent, constants.IsLatestVersion,
		).
		Where("t1.`project_id` = ? AND t1.`deleted` = ?", req.ProjectID, constants.NotDeleted)

	sb2 := squirrel.Select(apiCaseFields...).
		From(m.table+" AS t1").
		InnerJoin(
			apiCaseTableName+" AS t2 ON t1.`project_id` = t2.`project_id` AND t1.`resource_type` = ? AND t1.`resource_id` = t2.`case_id` AND t2.`latest` = ? AND t1.`deleted` = t2.`deleted`",
			common.ConstReviewResourceTypeAPICase, constants.IsLatestVersion,
		).
		Where("t1.`project_id` = ? AND t1.`deleted` = ?", req.ProjectID, constants.NotDeleted)

	sb3 := squirrel.Select(interfaceCaseFields...).
		From(m.table+" AS t1").
		InnerJoin(
			interfaceCaseTableName+" AS t2 ON t1.`project_id` = t2.`project_id` AND t1.`resource_type` = ? AND t1.`resource_id` = t2.`case_id` AND t2.`latest` = ? AND t1.`deleted` = t2.`deleted`",
			common.ConstReviewResourceTypeInterfaceCase, constants.IsLatestVersion,
		).
		Where("t1.`project_id` = ? AND t1.`deleted` = ?", req.ProjectID, constants.NotDeleted)

	query2, values2, _ := sb2.ToSql()
	query3, values3, _ := sb3.ToSql()
	tmp := sb1.Suffix("UNION "+query2, values2...).Suffix("UNION "+query3, values3...)

	alias := "t"
	sb = sqlbuilder.SearchOptionsWithAlias(
		squirrel.Select(
			utils.AddTableNameToFields(
				alias, virtualReviewRecordFieldNames,
			)...,
		).FromSelect(tmp, alias), alias, sqlbuilder.WithCondition(rrm, req.Condition),
		sqlbuilder.WithPagination(rrm, req.Pagination), sqlbuilder.WithSort(rrm, req.Sort),
	)
	scb = sqlbuilder.SearchOptionsWithAlias(
		squirrel.Select("COUNT(*)").FromSelect(tmp, alias), alias, sqlbuilder.WithCondition(rrm, req.Condition),
	)

	return searchReviewRecordSelectBuilder{SelectBuilder: sb}, searchReviewRecordCountBuilder{SelectBuilder: scb}
}

func (m *customReviewRecordModel) FindCountReviewRecords(
	ctx context.Context, countBuilder searchReviewRecordCountBuilder,
) (int64, error) {
	return m.FindCount(ctx, countBuilder.SelectBuilder)
}

func (m *customReviewRecordModel) FindReviewRecords(
	ctx context.Context, selectBuilder searchReviewRecordSelectBuilder,
) ([]*FullReviewRecord, error) {
	var resp []*FullReviewRecord

	err := utils.FindRows(
		ctx, m.conn, selectBuilder.SelectBuilder, func() any {
			return &resp
		},
	)

	return resp, err
}

func (m *customReviewRecordModel) getKeysByResource(
	ctx context.Context, projectID string, resourceType common.ReviewResourceType,
	resourceID string,
) []string {
	rs, err := m.FindReviewRecordsByResource(ctx, projectID, resourceType, resourceID, nil)
	if err != nil {
		return make([]string, 0)
	}

	keys := make([]string, 0, len(rs)*2)
	for _, r := range rs {
		keys = append(
			keys,
			fmt.Sprintf("%s%v", cacheManagerReviewRecordIdPrefix, r.Id),
			fmt.Sprintf("%s%v:%v", cacheManagerReviewRecordProjectIdReviewIdPrefix, r.ProjectId, r.ReviewId),
		)
	}

	return keys
}
