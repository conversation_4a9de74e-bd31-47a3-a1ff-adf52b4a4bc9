package model

import (
	"github.com/zeromicro/go-zero/core/jsonx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
)

func (x *ComponentGroup) ProjectID() string {
	return x.ProjectId
}

func (x *ComponentGroup) ResourceBranch() string {
	return x.CategoryId
}

func (x *ComponentGroup) ResourceParentType() common.ReviewResourceType {
	return common.ConstReviewResourceTypeComponentGroup
}

func (x *ComponentGroup) ResourceType() common.ReviewResourceType {
	return common.GetReviewResourceTypeByComponentGroupType(x.ComponentGroupType)
}

func (x *ComponentGroup) ResourceID() string {
	return x.ComponentGroupId
}

func (x *ComponentGroup) ResourceName() string {
	return x.Name
}

func (x *ComponentGroup) ResourceState() common.ResourceState {
	// TODO: return common.ResourceState(x.State)
	return common.ConstResourceStatePublished
}

func (x *ComponentGroup) Maintainer() string {
	return x.MaintainedBy.String
}

func (x *ApiCase) ProjectID() string {
	return x.ProjectId
}

func (x *ApiCase) ResourceBranch() string {
	return x.CategoryId
}

func (x *ApiCase) ResourceParentType() common.ReviewResourceType {
	return common.ConstReviewResourceTypeCase
}

func (x *ApiCase) ResourceType() common.ReviewResourceType {
	return common.ConstReviewResourceTypeAPICase
}

func (x *ApiCase) ResourceID() string {
	return x.CaseId
}

func (x *ApiCase) ResourceName() string {
	return x.Name
}

func (x *ApiCase) ResourceState() common.ResourceState {
	return common.ResourceState(x.State)
}

func (x *ApiCase) Maintainer() string {
	return x.MaintainedBy.String
}

func (x *InterfaceCase) ProjectID() string {
	return x.ProjectId
}

func (x *InterfaceCase) ResourceBranch() string {
	return x.DocumentId
}

func (x *InterfaceCase) ResourceParentType() common.ReviewResourceType {
	return common.ConstReviewResourceTypeCase
}

func (x *InterfaceCase) ResourceType() common.ReviewResourceType {
	return common.ConstReviewResourceTypeInterfaceCase
}

func (x *InterfaceCase) ResourceID() string {
	return x.CaseId
}

func (x *InterfaceCase) ResourceName() string {
	return x.Name
}

func (x *InterfaceCase) ResourceState() common.ResourceState {
	return common.ResourceState(x.State)
}

func (x *InterfaceCase) Maintainer() string {
	return x.MaintainedBy.String
}

func (x *UiPlan) GetDevices() []string {
	if !x.Devices.Valid || x.Devices.String == "" {
		return nil
	}

	var devices []string
	if err := jsonx.UnmarshalFromString(x.Devices.String, &devices); err != nil {
		return nil
	}

	return devices
}

func (x *UiPlan) SetDevices(devices []string) {
	s := jsonx.MarshalToStringIgnoreError(devices)
	x.Devices.String = s
	x.Devices.Valid = s != ""
}

func (x *StabilityPlan) GetDevices() []string {
	if !x.Devices.Valid || x.Devices.String == "" {
		return nil
	}

	var devices []string
	if err := jsonx.UnmarshalFromString(x.Devices.String, &devices); err != nil {
		return nil
	}

	return devices
}

func (x *StabilityPlan) SetDevices(devices []string) {
	s := jsonx.MarshalToStringIgnoreError(devices)
	x.Devices.String = s
	x.Devices.Valid = s != ""
}
