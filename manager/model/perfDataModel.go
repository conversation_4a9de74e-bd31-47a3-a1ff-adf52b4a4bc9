package model

import (
	"context"

	"github.com/Masterminds/squirrel"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sqlbuilder"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/types"
)

var (
	_ PerfDataModel = (*customPerfDataModel)(nil)

	perfDataInsertFields = stringx.Remove(
		perfDataFieldNames, "`id`", "`create_time`", "`update_time`", "`deleted_by`", "`created_at`", "`updated_at`",
		"`deleted_at`",
	)
)

type (
	// PerfDataModel is an interface to be customized, add more methods here,
	// and implement the added methods in customPerfDataModel.
	PerfDataModel interface {
		perfDataModel
		types.DBModel

		Trans(ctx context.Context, fn func(context context.Context, session sqlx.Session) error) error
		InsertBuilder(data *PerfData) squirrel.InsertBuilder
		UpdateBuilder(data *PerfData) squirrel.UpdateBuilder
		SelectBuilder() squirrel.SelectBuilder
		SelectCountBuilder() squirrel.SelectBuilder
		FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error)
		FindNoCacheByQuery(ctx context.Context, selectBuilder squirrel.SelectBuilder) ([]*PerfData, error)

		FindCountBySearchReq(ctx context.Context, req SearchPerfDataReq) (int64, error)
		FindAllBySearchReq(ctx context.Context, req SearchPerfDataReq) ([]*PerfData, error)
	}

	customPerfDataModel struct {
		*defaultPerfDataModel

		conn sqlx.SqlConn
	}
)

// NewPerfDataModel returns a model for the database table.
func NewPerfDataModel(conn sqlx.SqlConn, c cache.CacheConf, opts ...cache.Option) PerfDataModel {
	return &customPerfDataModel{
		defaultPerfDataModel: newPerfDataModel(conn, c, opts...),
		conn:                 conn,
	}
}

func (m *customPerfDataModel) Table() string {
	return m.table
}

func (m *customPerfDataModel) Fields() []string {
	return perfDataFieldNames
}

func (m *customPerfDataModel) Trans(
	ctx context.Context, fn func(context context.Context, session sqlx.Session) error,
) error {
	return m.TransactCtx(ctx, fn)
}

func (m *customPerfDataModel) InsertBuilder(data *PerfData) squirrel.InsertBuilder {
	return squirrel.Insert(m.table).Columns(perfDataInsertFields...).Values(
		data.ProjectId, data.DataId, data.Name, data.Description, data.Hash, data.Size, data.Path, data.NumberOfVu,
		data.Deleted, data.MaintainedBy, data.CreatedBy, data.UpdatedBy,
	)
}

func (m *customPerfDataModel) UpdateBuilder(data *PerfData) squirrel.UpdateBuilder {
	eq := squirrel.Eq{
		"`name`":          data.Name,
		"`description`":   data.Description,
		"`hash`":          data.Hash,
		"`size`":          data.Size,
		"`path`":          data.Path,
		"`number_of_vu`":  data.NumberOfVu,
		"`deleted`":       data.Deleted,
		"`maintained_by`": data.MaintainedBy,
		"`updated_by`":    data.UpdatedBy,
		"`deleted_by`":    data.DeletedBy,
		"`deleted_at`":    data.DeletedAt,
	}
	return squirrel.Update(m.table).SetMap(eq).Where("`id` = ?", data.Id)
}

func (m *customPerfDataModel) SelectBuilder() squirrel.SelectBuilder {
	return squirrel.Select(perfDataFieldNames...).Where("`deleted` = ?", constants.NotDeleted).From(m.table)
}

func (m *customPerfDataModel) SelectCountBuilder() squirrel.SelectBuilder {
	return squirrel.Select("count(*)").Where("`deleted` = ?", constants.NotDeleted).From(m.table)
}

func (m *customPerfDataModel) FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error) {
	query, values, err := countBuilder.ToSql()
	if err != nil {
		return 0, err
	}

	var resp int64
	err = m.QueryRowNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return 0, err
	}
}

func (m *customPerfDataModel) FindNoCacheByQuery(
	ctx context.Context, selectBuilder squirrel.SelectBuilder,
) ([]*PerfData, error) {
	query, values, err := selectBuilder.ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*PerfData
	err = m.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

func (m *customPerfDataModel) FindCountBySearchReq(ctx context.Context, req SearchPerfDataReq) (int64, error) {
	return m.FindCount(
		ctx, sqlbuilder.SearchOptions(
			m.SelectCountBuilder().Where("`project_id` = ?", req.ProjectID),
			sqlbuilder.WithCondition(m, req.Condition),
		),
	)
}

func (m *customPerfDataModel) FindAllBySearchReq(ctx context.Context, req SearchPerfDataReq) ([]*PerfData, error) {
	return m.FindNoCacheByQuery(
		ctx, sqlbuilder.SearchOptions(
			m.SelectBuilder().Where("`project_id` = ?", req.ProjectID),
			sqlbuilder.WithCondition(m, req.Condition),
			sqlbuilder.WithPagination(m, req.Pagination),
			sqlbuilder.WithSort(m, req.Sort),
		),
	)
}
