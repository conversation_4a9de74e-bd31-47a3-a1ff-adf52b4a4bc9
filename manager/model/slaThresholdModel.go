package model

import (
	"context"

	"github.com/Masterminds/squirrel"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/types"
)

var (
	_ SlaThresholdModel = (*customSlaThresholdModel)(nil)

	slaThresholdInsertFields = stringx.Remove(slaThresholdFieldNames, "`id`", "`create_time`", "`update_time`", "`deleted_by`", "`created_at`", "`updated_at`", "`deleted_at`")
)

type (
	// SlaThresholdModel is an interface to be customized, add more methods here,
	// and implement the added methods in customSlaThresholdModel.
	SlaThresholdModel interface {
		slaThresholdModel
		types.DBModel

		Trans(ctx context.Context, fn func(context context.Context, session sqlx.Session) error) error
		InsertBuilder(data *SlaThreshold) squirrel.InsertBuilder
		UpdateBuilder(data *SlaThreshold) squirrel.UpdateBuilder
		SelectBuilder() squirrel.SelectBuilder
		SelectCountBuilder() squirrel.SelectBuilder
		FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error)
		FindNoCacheByQuery(ctx context.Context, selectBuilder squirrel.SelectBuilder) ([]*SlaThreshold, error)

		FindAll(ctx context.Context, projectId string) ([]*SlaThreshold, error)
	}

	customSlaThresholdModel struct {
		*defaultSlaThresholdModel

		conn sqlx.SqlConn
	}
)

// NewSlaThresholdModel returns a model for the database table.
func NewSlaThresholdModel(conn sqlx.SqlConn, c cache.CacheConf, opts ...cache.Option) SlaThresholdModel {
	return &customSlaThresholdModel{
		defaultSlaThresholdModel: newSlaThresholdModel(conn, c, opts...),
		conn:                     conn,
	}
}

func (m *customSlaThresholdModel) Table() string {
	return m.table
}

func (m *customSlaThresholdModel) Fields() []string {
	return slaThresholdFieldNames
}

func (m *customSlaThresholdModel) Trans(ctx context.Context, fn func(context context.Context, session sqlx.Session) error) error {
	return m.TransactCtx(ctx, fn)
}

func (m *customSlaThresholdModel) InsertBuilder(data *SlaThreshold) squirrel.InsertBuilder {
	// TODO: fill the insert values
	return squirrel.Insert(m.table).Columns(slaThresholdInsertFields...).Values()
}

func (m *customSlaThresholdModel) UpdateBuilder(data *SlaThreshold) squirrel.UpdateBuilder {
	eq := squirrel.Eq{
		// TODO: fill the update values
	}
	return squirrel.Update(m.table).SetMap(eq).Where("`id` = ?", data.Id)
}

func (m *customSlaThresholdModel) SelectBuilder() squirrel.SelectBuilder {
	return squirrel.Select(slaThresholdFieldNames...).Where("`deleted` = ?", constants.NotDeleted).From(m.table)
}

func (m *customSlaThresholdModel) SelectCountBuilder() squirrel.SelectBuilder {
	return squirrel.Select("count(*)").Where("`deleted` = ?", constants.NotDeleted).From(m.table)
}

func (m *customSlaThresholdModel) FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error) {
	query, values, err := countBuilder.ToSql()
	if err != nil {
		return 0, err
	}

	var resp int64
	err = m.QueryRowNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return 0, err
	}
}

func (m *customSlaThresholdModel) FindNoCacheByQuery(ctx context.Context, selectBuilder squirrel.SelectBuilder) ([]*SlaThreshold, error) {
	query, values, err := selectBuilder.ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*SlaThreshold
	err = m.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

func (m *customSlaThresholdModel) FindAll(ctx context.Context, projectId string) ([]*SlaThreshold, error) {
	return m.FindNoCacheByQuery(ctx, m.SelectBuilder().Where("`project_id` = ?", projectId))
}
