package model

import (
	"context"
	"database/sql"
	"fmt"
	"time"

	"github.com/Masterminds/squirrel"
	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sqlbuilder"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/types"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/userinfo"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/utils"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
)

var (
	_ PerfPlanV2Model = (*customPerfPlanV2Model)(nil)

	perfPlanV2InsertFields = stringx.Remove(
		perfPlanV2FieldNames, "`id`", "`create_time`", "`update_time`", "`deleted_by`", "`created_at`", "`updated_at`",
		"`deleted_at`",
	)

	searchPerfPlanV2ItemFieldNames = builder.RawFieldNames(&SearchPerfPlanV2Item{})
)

type (
	// PerfPlanV2Model is an interface to be customized, add more methods here,
	// and implement the added methods in customPerfPlanV2Model.
	PerfPlanV2Model interface {
		perfPlanV2Model
		types.DBModel

		Trans(ctx context.Context, fn func(context context.Context, session sqlx.Session) error) error
		InsertBuilder(data *PerfPlanV2) squirrel.InsertBuilder
		UpdateBuilder(data *PerfPlanV2) squirrel.UpdateBuilder
		SelectBuilder() squirrel.SelectBuilder
		SelectCountBuilder() squirrel.SelectBuilder
		FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error)
		FindNoCacheByQuery(ctx context.Context, selectBuilder squirrel.SelectBuilder) ([]*PerfPlanV2, error)

		FindCountByReq(ctx context.Context, req SearchPerfPlanV2Req) (int64, error)
		FindAllByReq(ctx context.Context, req SearchPerfPlanV2Req) ([]*SearchPerfPlanV2Item, error)

		GenerateSearchPerfPlanV2SqlBuilder(req SearchPerfPlanV2Req) (searchPerfPlanV2SelectBuilder, searchPerfPlanV2CountBuilder)

		FindCountCasesInPerfPlan(ctx context.Context, req SearchCaseInPerfPlanV2Req) (int64, error)
		FindCasesInPerfPlan(ctx context.Context, req SearchCaseInPerfPlanV2Req) ([]*SearchCaseInPerfPlanV2Item, error)

		FindCountRulesInPerfPlan(ctx context.Context, req SearchRuleInPerfPlanV2Req) (int64, error)
		FindRulesInPerfPlan(ctx context.Context, req SearchRuleInPerfPlanV2Req) ([]*SearchRuleInPerfPlanV2Item, error)

		FindCountProtobufConfigsInPerfPlan(ctx context.Context, req SearchProtobufConfigInPerfPlanV2Req) (int64, error)
		FindProtobufConfigsInPerfPlan(
			ctx context.Context, req SearchProtobufConfigInPerfPlanV2Req,
		) ([]*SearchProtobufConfigInPerfPlanV2Item, error)

		FindByProtobufConfigID(ctx context.Context, projectID, configID string) ([]*PerfPlanV2, error)
		FindByGeneralConfigID(ctx context.Context, projectID, configID string) ([]*PerfPlanV2, error)
		FindByAccountConfigID(ctx context.Context, projectID, configID string) ([]*PerfPlanV2, error)
		RemoveByPlanID(ctx context.Context, session sqlx.Session, projectID, planID string) (sql.Result, error)

		FindCountByCategoryId(ctx context.Context, cm CategoryModel, cond FindCountByCategoryCondition) (int64, error)

		FindCountPerfPlansV2(ctx context.Context, countBuilder searchPerfPlanV2CountBuilder) (int64, error)
		FindPerfPlansV2(ctx context.Context, selectBuilder searchPerfPlanV2SelectBuilder) ([]*SearchPerfPlanV2Item, error)
	}

	customPerfPlanV2Model struct {
		*defaultPerfPlanV2Model

		conn sqlx.SqlConn
	}
)

// NewPerfPlanV2Model returns a model for the database table.
func NewPerfPlanV2Model(conn sqlx.SqlConn, c cache.CacheConf, opts ...cache.Option) PerfPlanV2Model {
	return &customPerfPlanV2Model{
		defaultPerfPlanV2Model: newPerfPlanV2Model(conn, c, opts...),
		conn:                   conn,
	}
}

func (m *customPerfPlanV2Model) Table() string {
	return m.table
}

func (m *customPerfPlanV2Model) Fields() []string {
	return perfPlanV2FieldNames
}

func (m *customPerfPlanV2Model) Trans(
	ctx context.Context, fn func(context context.Context, session sqlx.Session) error,
) error {
	return m.TransactCtx(ctx, fn)
}

func (m *customPerfPlanV2Model) InsertBuilder(data *PerfPlanV2) squirrel.InsertBuilder {
	return squirrel.Insert(m.table).Columns(perfPlanV2InsertFields...).Values(
		data.ProjectId, data.CategoryId, data.PlanId, data.Name, data.Description, data.Type, data.CronExpression,
		data.Tags, data.Protocol, data.TargetEnv, data.ProtobufConfigId, data.GeneralConfigId, data.AccountConfigId,
		data.AuthRateLimits, data.CustomDuration, data.Duration, data.CreateLarkChat, data.LarkChatId, data.AdvancedNotification,
		data.State, data.Deleted, data.MaintainedBy, data.CreatedBy, data.UpdatedBy,
	)
}

func (m *customPerfPlanV2Model) UpdateBuilder(data *PerfPlanV2) squirrel.UpdateBuilder {
	eq := squirrel.Eq{
		"`name`":                  data.Name,
		"`description`":           data.Description,
		"`type`":                  data.Type,
		"`cron_expression`":       data.CronExpression,
		"`tags`":                  data.Tags,
		"`protocol`":              data.Protocol,
		"`target_env`":            data.TargetEnv,
		"`protobuf_config_id`":    data.ProtobufConfigId,
		"`general_config_id`":     data.GeneralConfigId,
		"`account_config_id`":     data.AccountConfigId,
		"`auth_rate_limits`":      data.AuthRateLimits,
		"`custom_duration`":       data.CustomDuration,
		"`duration`":              data.Duration,
		"`create_lark_chat`":      data.CreateLarkChat,
		"`lark_chat_id`":          data.LarkChatId,
		"`advanced_notification`": data.AdvancedNotification,
		"`state`":                 data.State,
		"`deleted`":               data.Deleted,
		"`maintained_by`":         data.MaintainedBy,
		"`updated_by`":            data.UpdatedBy,
		"`deleted_by`":            data.DeletedBy,
		"`deleted_at`":            data.DeletedAt,
	}
	return squirrel.Update(m.table).SetMap(eq).Where("`id` = ?", data.Id)
}

func (m *customPerfPlanV2Model) SelectBuilder() squirrel.SelectBuilder {
	return squirrel.Select(perfPlanV2FieldNames...).Where("`deleted` = ?", constants.NotDeleted).From(m.table)
}

func (m *customPerfPlanV2Model) SelectCountBuilder() squirrel.SelectBuilder {
	return squirrel.Select("count(*)").Where("`deleted` = ?", constants.NotDeleted).From(m.table)
}

func (m *customPerfPlanV2Model) FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error) {
	query, values, err := countBuilder.ToSql()
	if err != nil {
		return 0, err
	}

	var resp int64
	err = m.QueryRowNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return 0, err
	}
}

func (m *customPerfPlanV2Model) FindNoCacheByQuery(
	ctx context.Context, selectBuilder squirrel.SelectBuilder,
) ([]*PerfPlanV2, error) {
	query, values, err := selectBuilder.ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*PerfPlanV2
	err = m.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

type (
	SearchPerfPlanV2Item struct {
		ProjectId      string         `db:"project_id"`      // 项目ID
		CategoryId     string         `db:"category_id"`     // 所属分类ID
		PlanId         string         `db:"plan_id"`         // 计划ID
		Name           string         `db:"name"`            // 计划名称
		Description    sql.NullString `db:"description"`     // 计划描述
		Type           string         `db:"type"`            // 计划类型（手动、定时、接口）
		CronExpression sql.NullString `db:"cron_expression"` // 定时触发计划的Cron表达式
		Tags           sql.NullString `db:"tags"`            // 标签
		Protocol       string         `db:"protocol"`        // 协议（TT私有协议、TT登录压测场景专用、通用gRPC协议、通用HTTP协议）
		TargetEnv      string         `db:"target_env"`      // 目标环境（生产环境、测试环境）
		CustomDuration int64          `db:"custom_duration"` // 是否自定义压测持续时长
		Duration       int64          `db:"duration"`        // 压测持续时长，单位为秒
		NumberOfCases  int64          `db:"number_of_cases"` // 用例数量
		NumberOfSteps  int64          `db:"number_of_steps"` // 步骤数量（串行和并行）
		StatsOfStep    string         `db:"stats_of_step"`   // 步骤统计信息
		State          int64          `db:"state"`           // 计划状态（生效、失效）
		MaintainedBy   sql.NullString `db:"maintained_by"`   // 维护者的用户ID
		CreatedBy      string         `db:"created_by"`      // 创建者的用户ID
		UpdatedBy      string         `db:"updated_by"`      // 最近一次更新者的用户ID
		CreatedAt      time.Time      `db:"created_at"`      // 创建时间
		UpdatedAt      time.Time      `db:"updated_at"`      // 更新时间
	}

	searchPerfPlanV2SelectBuilder struct {
		squirrel.SelectBuilder
	}
	searchPerfPlanV2CountBuilder struct {
		squirrel.SelectBuilder
	}
)

func (m *customPerfPlanV2Model) FindCountByReq(ctx context.Context, req SearchPerfPlanV2Req) (int64, error) {
	return m.FindCount(
		ctx, sqlbuilder.SearchOptions(
			m.SelectCountBuilder().Where("`project_id` = ?", req.ProjectID),
			sqlbuilder.WithCondition(m, req.Condition),
		),
	)
}

func (m *customPerfPlanV2Model) FindAllByReq(ctx context.Context, req SearchPerfPlanV2Req) (
	[]*SearchPerfPlanV2Item, error,
) {
	/*
		SQL:
		SELECT t1.`project_id`,
			   t1.`category_id`,
		       t1.`plan_id`,
		       t1.`name`,
		       t1.`description`,
		       t1.`type`,
		       t1.`cron_expression`,
		       t1.`tags`,
		       t1.`protocol`,
		       t1.`target_env`,
		       t1.`custom_duration`,
		       t1.`duration`,
		       t1.`number_of_cases`,
		       t1.`number_of_steps`,
		       JSON_ARRAYAGG(
		               JSON_OBJECT(
		                       'target_rps', t2.`target_rps`,
		                       'number_of_steps', t2.`rps_count`
		               )
		       ) AS `stats_of_step`,
		       t1.`state`,
		       t1.`maintained_by`,
		       t1.`created_by`,
		       t1.`updated_by`,
		       t1.`created_at`,
		       t1.`updated_at`
		FROM (SELECT t1.`project_id`,
					 t1.`category_id`,
		             t1.`plan_id`,
		             t1.`name`,
		             t1.`description`,
		             t1.`type`,
		             t1.`cron_expression`,
		             t1.`tags`,
		             t1.`protocol`,
		             t1.`target_env`,
		             t1.`custom_duration`,
		             t1.`duration`,
		             t1.`state`,
		             t1.`maintained_by`,
		             t1.`created_by`,
		             t1.`updated_by`,
		             t1.`created_at`,
		             t1.`updated_at`,
		             COUNT(DISTINCT t3.`case_id`) AS `number_of_cases`,
		             COUNT(DISTINCT t4.`step_id`) AS `number_of_steps`
		      FROM `perf_plan_v2` AS t1
		               INNER JOIN `perf_plan_case_relationship` AS t2
		                   ON t1.`project_id` = t2.`project_id` AND
		                      t1.`plan_id` = t2.`plan_id` AND
		                      t1.`deleted` = t2.`deleted`
		               INNER JOIN `perf_case_v2` AS t3
		                   ON t1.`project_id` = t3.`project_id` AND
		                      t2.`case_id` = t3.`case_id` AND
		                      t1.`deleted` = t3.`deleted`
		               LEFT JOIN `perf_case_step_v2` AS t4
		                   ON t1.`project_id` = t4.`project_id` AND
		                      t3.`case_id` = t4.`case_id` AND
		                      t1.`deleted` = t4.`deleted` AND
		                      t4.`type` IN (?, ?)
		      WHERE t1.`project_id` = ?
		        AND t1.`deleted` = ?
		      GROUP BY t1.`project_id`, t1.`plan_id`
		     ) AS t1
		     INNER JOIN
		     (SELECT t1.`project_id`,
		             t1.`plan_id`,
		             t2.`target_rps`,
		             COUNT(t2.`target_rps`) AS `rps_count`
		      FROM `perf_plan_v2` AS t1
		               INNER JOIN `perf_plan_case_relationship` AS t2
		                   ON t1.`project_id` = t2.`project_id` AND
		                      t1.`plan_id` = t2.`plan_id` AND
		                      t1.`deleted` = t2.`deleted`
		               INNER JOIN `perf_case_v2` AS t3 ON
		                   t1.`project_id` = t3.`project_id` AND
		                   t2.`case_id` = t3.`case_id` AND
		                   t1.`deleted` = t3.`deleted`
		               LEFT JOIN `perf_case_step_v2` AS t4
		                   ON t1.`project_id` = t4.`project_id` AND
		                      t3.`case_id` = t4.`case_id` AND
		                      t1.`deleted` = t4.`deleted` AND
		                      t4.`type` IN (?, ?)
		      WHERE t1.`project_id` = ?
		        AND t1.`deleted` = ?
		      GROUP BY t1.`project_id`, t1.`plan_id`, t2.`target_rps`
		     ) AS t2 ON t1.`project_id` = t2.`project_id` AND t1.`plan_id` = t2.`plan_id`
		GROUP BY t1.`project_id`, t1.`plan_id`;
	*/

	var (
		fields = []string{
			"t1.`project_id`",
			"t1.`category_id`",
			"t1.`plan_id`",
			"t1.`name`",
			"t1.`description`",
			"t1.`type`",
			"t1.`cron_expression`",
			"t1.`tags`",
			"t1.`protocol`",
			"t1.`target_env`",
			"t1.`custom_duration`",
			"t1.`duration`",
			"t1.`number_of_cases`",
			"t1.`number_of_steps`",
			"JSON_ARRAYAGG(JSON_OBJECT('target_rps', t2.`target_rps`, 'number_of_steps', t2.`rps_count`)) AS `stats_of_step`",
			"t1.`state`",
			"t1.`maintained_by`",
			"t1.`created_by`",
			"t1.`updated_by`",
			"t1.`created_at`",
			"t1.`updated_at`",
		}

		subFields1 = []string{
			"t1.`project_id`",
			"t1.`category_id`",
			"t1.`plan_id`",
			"t1.`name`",
			"t1.`description`",
			"t1.`type`",
			"t1.`cron_expression`",
			"t1.`tags`",
			"t1.`protocol`",
			"t1.`target_env`",
			"t1.`custom_duration`",
			"t1.`duration`",
			"t1.`state`",
			"t1.`maintained_by`",
			"t1.`created_by`",
			"t1.`updated_by`",
			"t1.`created_at`",
			"t1.`updated_at`",
			"COUNT(DISTINCT t3.`case_id`) AS `number_of_cases`",
			"COUNT(DISTINCT t4.`step_id`) AS `number_of_steps`",
		}

		subFields2 = []string{
			"t1.`project_id`",
			"t1.`plan_id`",
			"t2.`target_rps`",
			"COUNT(t2.`target_rps`) AS `rps_count`",
		}
	)

	sub1 := squirrel.Select(subFields1...).
		From(m.table+" AS t1").
		InnerJoin(perfPlanCaseRelationshipTableName+" AS t2 ON t1.`project_id` = t2.`project_id` AND t1.`plan_id` = t2.`plan_id` AND t1.`deleted` = t2.`deleted`").
		InnerJoin(perfCaseV2TableName+" AS t3 ON t1.`project_id` = t3.`project_id` AND t2.`case_id` = t3.`case_id` AND t1.`deleted` = t3.`deleted`").
		LeftJoin(
			perfCaseStepV2TableName+" AS t4 ON t1.`project_id` = t4.`project_id` AND t3.`case_id` = t4.`case_id` AND t1.`deleted` = t4.`deleted` AND t4.`type` IN (?, ?)",
			common.ConstPerfCaseStepTypeSerial, common.ConstPerfCaseStepTypeParallel,
		).
		Where("t1.`project_id` = ? AND t1.`deleted` = ?", req.ProjectID, constants.NotDeleted).
		GroupBy("t1.`project_id`", "t1.`plan_id`")
	sub1 = sqlbuilder.SearchOptionsWithAlias(sub1, "t1", sqlbuilder.WithCondition(m, req.Condition))

	sub2 := squirrel.Select(subFields2...).
		From(m.table+" AS t1").
		InnerJoin(
			perfPlanCaseRelationshipTableName+" AS t2 ON t1.`project_id` = t2.`project_id` AND t1.`plan_id` = t2.`plan_id` AND t1.`deleted` = t2.`deleted`",
		).
		InnerJoin(perfCaseV2TableName+" AS t3 ON t1.`project_id` = t3.`project_id` AND t2.`case_id` = t3.`case_id` AND t1.`deleted` = t3.`deleted`").
		LeftJoin(
			perfCaseStepV2TableName+" AS t4 ON t1.`project_id` = t4.`project_id` AND t3.`case_id` = t4.`case_id` AND t1.`deleted` = t4.`deleted` AND t4.`type` IN (?, ?)",
			common.ConstPerfCaseStepTypeSerial, common.ConstPerfCaseStepTypeParallel,
		).
		Where("t1.`project_id` = ? AND t1.`deleted` = ?", req.ProjectID, constants.NotDeleted).
		GroupBy("t1.`project_id`", "t1.`plan_id`", "t2.`target_rps`")
	sub2 = sqlbuilder.SearchOptionsWithAlias(sub2, "t1", sqlbuilder.WithCondition(m, req.Condition))

	sb := squirrel.Select(fields...).
		FromSelect(sub1, "t1").
		JoinClause(sub2.Prefix("INNER JOIN (").Suffix(") AS t2 ON t1.`project_id` = t2.`project_id` AND t1.`plan_id` = t2.`plan_id`")).
		GroupBy("t1.`project_id`", "t1.`plan_id`")
	sb = sqlbuilder.SearchOptionsWithAlias(
		sb, "t1", sqlbuilder.WithPagination(m, req.Pagination), sqlbuilder.WithSort(m, req.Sort),
	)

	var resp []*SearchPerfPlanV2Item
	err := utils.FindRows(
		ctx, m.conn, sb, func() any {
			return &resp
		},
	)
	return resp, err
}

func (m *customPerfPlanV2Model) GenerateSearchPerfPlanV2SqlBuilder(req SearchPerfPlanV2Req) (
	searchPerfPlanV2SelectBuilder, searchPerfPlanV2CountBuilder,
) {
	/*
				SQL:
				SELECT t1.`project_id`,
					   t1.`category_id`,
				       t1.`plan_id`,
				       t1.`name`,
				       t1.`description`,
				       t1.`type`,
				       t1.`cron_expression`,
				       t1.`tags`,
				       t1.`protocol`,
				       t1.`target_env`,
				       t1.`custom_duration`,
				       t1.`duration`,
				       t1.`number_of_cases`,
				       t1.`number_of_steps`,
				       JSON_ARRAYAGG(
				               JSON_OBJECT(
				                       'target_rps', t2.`target_rps`,
				                       'number_of_steps', t2.`rps_count`
				               )
				       ) AS `stats_of_step`,
				       t1.`state`,
				       t1.`maintained_by`,
				       t1.`created_by`,
				       t1.`updated_by`,
				       t1.`created_at`,
				       t1.`updated_at`
				FROM (SELECT t1.`project_id`,
							 t1.`category_id`,
				             t1.`plan_id`,
				             t1.`name`,
				             t1.`description`,
				             t1.`type`,
				             t1.`cron_expression`,
				             t1.`tags`,
				             t1.`protocol`,
				             t1.`target_env`,
				             t1.`custom_duration`,
				             t1.`duration`,
				             t1.`state`,
				             t1.`maintained_by`,
				             t1.`created_by`,
				             t1.`updated_by`,
				             t1.`created_at`,
				             t1.`updated_at`,
				             COUNT(DISTINCT t3.`case_id`) AS `number_of_cases`,
				             COUNT(DISTINCT t4.`step_id`) AS `number_of_steps`
				      FROM `perf_plan_v2` AS t1
				               INNER JOIN `perf_plan_case_relationship` AS t2
				                   ON t1.`project_id` = t2.`project_id` AND
				                      t1.`plan_id` = t2.`plan_id` AND
				                      t1.`deleted` = t2.`deleted`
				               INNER JOIN `perf_case_v2` AS t3
				                   ON t1.`project_id` = t3.`project_id` AND
				                      t2.`case_id` = t3.`case_id` AND
				                      t1.`deleted` = t3.`deleted`
				               LEFT JOIN `perf_case_step_v2` AS t4
				                   ON t1.`project_id` = t4.`project_id` AND
				                      t3.`case_id` = t4.`case_id` AND
				                      t1.`deleted` = t4.`deleted` AND
				                      t4.`type` IN (?, ?)
				      WHERE t1.`project_id` = ?
				        AND t1.`deleted` = ?
				      GROUP BY t1.`project_id`, t1.`plan_id`
				     ) AS t1
				     INNER JOIN
				     (SELECT t1.`project_id`,
				             t1.`plan_id`,
				             t2.`target_rps`,
				             COUNT(t2.`target_rps`) AS `rps_count`
				      FROM `perf_plan_v2` AS t1
				               INNER JOIN `perf_plan_case_relationship` AS t2
				                   ON t1.`project_id` = t2.`project_id` AND
				                      t1.`plan_id` = t2.`plan_id` AND
				                      t1.`deleted` = t2.`deleted`
				               INNER JOIN `perf_case_v2` AS t3 ON
				                   t1.`project_id` = t3.`project_id` AND
				                   t2.`case_id` = t3.`case_id` AND
				                   t1.`deleted` = t3.`deleted`
				               LEFT JOIN `perf_case_step_v2` AS t4
				                   ON t1.`project_id` = t4.`project_id` AND
				                      t3.`case_id` = t4.`case_id` AND
				                      t1.`deleted` = t4.`deleted` AND
				                      t4.`type` IN (?, ?)
				      WHERE t1.`project_id` = ?
				        AND t1.`deleted` = ?
				      GROUP BY t1.`project_id`, t1.`plan_id`, t2.`target_rps`
				     ) AS t2 ON t1.`project_id` = t2.`project_id` AND t1.`plan_id` = t2.`plan_id`
					 LEFT JOIN
		             (
		                SELECT t1.*
				        FROM `category` AS t1
				            INNER JOIN `category_tree` AS t2 ON
				                t1.`project_id` = t2.`project_id` AND
				                t1.`category_id` = t2.`descendant`
				        WHERE t1.`project_id` = ?
				          AND t1.`type` = ?
				          AND t2.`ancestor` = ?
				          AND t1.`deleted` = ?
				          AND t2.`deleted` = ?
				        ORDER BY t2.`depth` DESC, t2.`index` ASC, t1.`created_at` ASC
		             ) AS t3 ON t1.`category_id` = t3.`category_id`
		        WHERE t1.`project_id` = ?
		          AND t3.`category_id` IS NOT NULL
				GROUP BY t1.`project_id`, t1.`plan_id`;
	*/

	var (
		fields = []string{
			"t1.`project_id`",
			"t1.`category_id`",
			"t1.`plan_id`",
			"t1.`name`",
			"t1.`description`",
			"t1.`type`",
			"t1.`cron_expression`",
			"t1.`tags`",
			"t1.`protocol`",
			"t1.`target_env`",
			"t1.`custom_duration`",
			"t1.`duration`",
			"t1.`number_of_cases`",
			"t1.`number_of_steps`",
			"JSON_ARRAYAGG(JSON_OBJECT('target_rps', t2.`target_rps`, 'number_of_steps', t2.`rps_count`)) AS `stats_of_step`",
			"t1.`state`",
			"t1.`maintained_by`",
			"t1.`created_by`",
			"t1.`updated_by`",
			"t1.`created_at`",
			"t1.`updated_at`",
		}

		subFields1 = []string{
			"t1.`project_id`",
			"t1.`category_id`",
			"t1.`plan_id`",
			"t1.`name`",
			"t1.`description`",
			"t1.`type`",
			"t1.`cron_expression`",
			"t1.`tags`",
			"t1.`protocol`",
			"t1.`target_env`",
			"t1.`custom_duration`",
			"t1.`duration`",
			"t1.`state`",
			"t1.`maintained_by`",
			"t1.`created_by`",
			"t1.`updated_by`",
			"t1.`created_at`",
			"t1.`updated_at`",
			"COUNT(DISTINCT t3.`case_id`) AS `number_of_cases`",
			"COUNT(DISTINCT t4.`step_id`) AS `number_of_steps`",
		}

		subFields2 = []string{
			"t1.`project_id`",
			"t1.`plan_id`",
			"t2.`target_rps`",
			"COUNT(t2.`target_rps`) AS `rps_count`",
		}
	)

	sub1 := squirrel.Select(subFields1...).
		From(m.table+" AS t1").
		InnerJoin(perfPlanCaseRelationshipTableName+" AS t2 ON t1.`project_id` = t2.`project_id` AND t1.`plan_id` = t2.`plan_id` AND t1.`deleted` = t2.`deleted`").
		InnerJoin(perfCaseV2TableName+" AS t3 ON t1.`project_id` = t3.`project_id` AND t2.`case_id` = t3.`case_id` AND t1.`deleted` = t3.`deleted`").
		LeftJoin(
			perfCaseStepV2TableName+" AS t4 ON t1.`project_id` = t4.`project_id` AND t3.`case_id` = t4.`case_id` AND t1.`deleted` = t4.`deleted` AND t4.`type` IN (?, ?)",
			common.ConstPerfCaseStepTypeSerial, common.ConstPerfCaseStepTypeParallel,
		).
		Where("t1.`project_id` = ? AND t1.`deleted` = ?", req.ProjectID, constants.NotDeleted).
		GroupBy("t1.`project_id`", "t1.`plan_id`")
	sub1 = sqlbuilder.SearchOptionsWithAlias(sub1, "t1", sqlbuilder.WithCondition(m, req.Condition))

	sub2 := squirrel.Select(subFields2...).
		From(m.table+" AS t1").
		InnerJoin(
			perfPlanCaseRelationshipTableName+" AS t2 ON t1.`project_id` = t2.`project_id` AND t1.`plan_id` = t2.`plan_id` AND t1.`deleted` = t2.`deleted`",
		).
		InnerJoin(perfCaseV2TableName+" AS t3 ON t1.`project_id` = t3.`project_id` AND t2.`case_id` = t3.`case_id` AND t1.`deleted` = t3.`deleted`").
		LeftJoin(
			perfCaseStepV2TableName+" AS t4 ON t1.`project_id` = t4.`project_id` AND t3.`case_id` = t4.`case_id` AND t1.`deleted` = t4.`deleted` AND t4.`type` IN (?, ?)",
			common.ConstPerfCaseStepTypeSerial, common.ConstPerfCaseStepTypeParallel,
		).
		Where("t1.`project_id` = ? AND t1.`deleted` = ?", req.ProjectID, constants.NotDeleted).
		GroupBy("t1.`project_id`", "t1.`plan_id`", "t2.`target_rps`")
	sub2 = sqlbuilder.SearchOptionsWithAlias(sub2, "t1", sqlbuilder.WithCondition(m, req.Condition))

	sb := squirrel.Select(fields...).
		FromSelect(sub1, "t1").
		JoinClause(sub2.Prefix("INNER JOIN (").Suffix(") AS t2 ON t1.`project_id` = t2.`project_id` AND t1.`plan_id` = t2.`plan_id`"))

	// count的sql命令，请参考perfCaseV2Model.GenerateSearchPerfCaseV2SqlBuilder
	scb := squirrel.Select("COUNT(*)").
		From(m.table+" AS t").
		Where("t.`project_id` = ? AND t.`deleted` = ?", req.ProjectID, constants.NotDeleted)

	if req.DrillDown {
		sub3 := req.CategoryModel.FindDescendantCategoriesSqlBuilder(
			GetCategoryTreeCondition{
				ProjectId:     req.ProjectID,
				Type:          common.ConstCategoryTreeTypePerfPlan,
				CategoryId:    req.CategoryID,
				OnlyDirectory: true,
			},
		)

		sb = sb.JoinClause(
			sub3.Prefix("LEFT JOIN (").Suffix(") AS t3 ON t1.`category_id` = t3.`category_id`"),
		).
			Where("t3.`category_id` IS NOT NULL")

		scb = scb.JoinClause(
			sub3.Prefix("LEFT JOIN (").Suffix(") AS t1 ON t.`category_id` = t1.`category_id`"),
		).
			Where("t1.`category_id` IS NOT NULL")
	} else {
		sb = sb.Where("t1.`category_id` = ?", req.CategoryID)
		scb = scb.Where("t.`category_id` = ?", req.CategoryID)
	}

	sb = sb.GroupBy("t1.`project_id`", "t1.`plan_id`")
	sb = sqlbuilder.SearchOptionsWithAlias(
		sb, "t1",
		sqlbuilder.WithCondition(m, req.Condition),
		sqlbuilder.WithPagination(m, req.Pagination),
		sqlbuilder.WithSort(m, req.Sort),
	)

	scb = sqlbuilder.SearchOptionsWithAlias(scb, "t", sqlbuilder.WithCondition(m, req.Condition))

	return searchPerfPlanV2SelectBuilder{SelectBuilder: sb}, searchPerfPlanV2CountBuilder{SelectBuilder: scb}
}

type SearchCaseInPerfPlanV2Item struct {
	ProjectId        string         `db:"project_id"`         // 项目ID
	PlanId           string         `db:"plan_id"`            // 压测计划ID
	CategoryId       string         `db:"category_id"`        // 所属分类ID
	CaseId           string         `db:"case_id"`            // 压测用例ID
	Name             string         `db:"name"`               // 压测用例名称
	Description      sql.NullString `db:"description"`        // 压测用例描述
	Tags             sql.NullString `db:"tags"`               // 标签
	Protocol         string         `db:"protocol"`           // 协议（TT私有协议、TT登录压测场景专用、通用gRPC协议、通用HTTP协议）
	RateLimits       sql.NullString `db:"rate_limits"`        // 压测计划下的用例限流配置
	SerialSteps      sql.NullString `db:"serial_steps"`       // 串行步骤
	ParallelSteps    sql.NullString `db:"parallel_steps"`     // 并行步骤
	NumberOfSteps    int64          `db:"number_of_steps"`    // 测试步骤数
	TargetRps        int64          `db:"target_rps"`         // 目标的RPS
	PerfDataId       string         `db:"perf_data_id"`       // 压测数据ID
	CustomVu         int64          `db:"custom_vu"`          // 是否为自定义虚拟用户数
	NumberOfVu       int64          `db:"number_of_vu"`       // 虚拟用户数
	CustomLg         int64          `db:"custom_lg"`          // 是否为自定义施压机资源
	NumberOfLg       int64          `db:"number_of_lg"`       // 施压机数
	RequestsOfCpu    string         `db:"requests_of_cpu"`    // 最小分配的CPU资源
	RequestsOfMemory string         `db:"requests_of_memory"` // 最小分配的内存资源
	LimitsOfCpu      string         `db:"limits_of_cpu"`      // 最大分配的CPU资源
	LimitsOfMemory   string         `db:"limits_of_memory"`   // 最大分配的内存资源
	State            int64          `db:"state"`              // 计划状态（生效、失效）
	MaintainedBy     sql.NullString `db:"maintained_by"`      // 维护者的用户ID
	CreatedBy        string         `db:"created_by"`         // 创建者的用户ID
	UpdatedBy        string         `db:"updated_by"`         // 最近一次更新者的用户ID
	CreatedAt        time.Time      `db:"created_at"`         // 创建时间
	UpdatedAt        time.Time      `db:"updated_at"`         // 更新时间
}

func (m *customPerfPlanV2Model) FindCountCasesInPerfPlan(ctx context.Context, req SearchCaseInPerfPlanV2Req) (
	int64, error,
) {
	var (
		aliasT1 = "t1" // `perf_plan_v2`
		aliasT2 = "t2" // `perf_case_v2`
		aliasT3 = "t3" // `perf_plan_case_relationship`
	)

	sb := squirrel.Select("COUNT(*)").
		From(m.table+" AS "+aliasT1).
		InnerJoin(
			fmt.Sprintf(
				"%s AS %s ON %s.`project_id` = %s.`project_id` AND %s.`deleted` = %s.`deleted`",
				perfCaseV2TableName, aliasT2, aliasT1, aliasT2, aliasT1, aliasT2,
			),
		).
		InnerJoin(
			fmt.Sprintf(
				"%s AS %s ON %s.`project_id` = %s.`project_id` AND %s.`plan_id` = %s.`plan_id` AND %s.`case_id` = %s.`case_id` AND %s.`deleted` = %s.`deleted`",
				perfPlanCaseRelationshipTableName, aliasT3, aliasT1, aliasT3, aliasT1, aliasT3, aliasT2, aliasT3,
				aliasT1, aliasT3,
			),
		).
		Where(
			fmt.Sprintf("%s.`project_id` = ? AND %s.`plan_id` = ? AND %s.`deleted` = ?", aliasT1, aliasT1, aliasT1),
			req.ProjectID, req.PlanID, constants.NotDeleted,
		)
	sb = sqlbuilder.SearchOptionsWithAlias(sb, aliasT2, sqlbuilder.WithCondition(req.PerfCaseV2Model, req.Condition))

	return m.FindCount(ctx, sb)
}

func (m *customPerfPlanV2Model) FindCasesInPerfPlan(
	ctx context.Context, req SearchCaseInPerfPlanV2Req,
) ([]*SearchCaseInPerfPlanV2Item, error) {
	/*
		SQL:
		SELECT t1.`project_id`,
		       t1.`plan_id`,
		       t2.`category_id`,
		       t2.`case_id`,
		       t2.`name`,
		       t2.`description`,
		       t2.`tags`,
		       t2.`protocol`,
		       t3.`rate_limits`,
			   t2.`serial_steps`,
			   t2.`parallel_steps`,
		       t2.`number_of_steps`,
		       t3.`target_rps`,
		       t3.`perf_data_id`,
		       t3.`custom_vu`,
		       t3.`number_of_vu`,
		       t3.`custom_lg`,
		       t3.`number_of_lg`,
		       t3.`requests_of_cpu`,
		       t3.`requests_of_memory`,
		       t3.`limits_of_cpu`,
		       t3.`limits_of_memory`,
		       t2.`state`,
		       t2.`maintained_by`,
		       t2.`created_by`,
		       t2.`updated_by`,
		       t2.`created_at`,
		       t2.`updated_at`
		FROM `perf_plan_v2` AS t1
		         INNER JOIN `perf_case_v2` AS t2 ON
		    t1.`project_id` = t2.`project_id` AND
		    t1.`deleted` = t2.`deleted`
		         INNER JOIN `perf_plan_case_relationship` AS t3 ON
		    t1.`project_id` = t3.`project_id` AND
		    t1.`plan_id` = t3.`plan_id` AND
		    t2.`case_id` = t3.`case_id` AND
		    t1.`deleted` = t3.`deleted`
		WHERE t1.`project_id` = ?
		  AND t1.`plan_id` = ?
		  AND t1.`deleted` = ?
	*/

	var (
		aliasT1 = "t1" // `perf_plan_v2`
		aliasT2 = "t2" // `perf_case_v2`
		aliasT3 = "t3" // `perf_plan_case_relationship`
	)

	fields := []string{
		aliasT1 + ".`project_id`",
		aliasT1 + ".`plan_id`",
		aliasT2 + ".`category_id`",
		aliasT2 + ".`case_id`",
		aliasT2 + ".`name`",
		aliasT2 + ".`description`",
		aliasT2 + ".`tags`",
		aliasT2 + ".`protocol`",
		aliasT3 + ".`rate_limits`",
		aliasT2 + ".`serial_steps`",
		aliasT2 + ".`parallel_steps`",
		aliasT2 + ".`number_of_steps`",
		aliasT3 + ".`target_rps`",
		aliasT3 + ".`perf_data_id`",
		aliasT3 + ".`custom_vu`",
		aliasT3 + ".`number_of_vu`",
		aliasT3 + ".`custom_lg`",
		aliasT3 + ".`number_of_lg`",
		aliasT3 + ".`requests_of_cpu`",
		aliasT3 + ".`requests_of_memory`",
		aliasT3 + ".`limits_of_cpu`",
		aliasT3 + ".`limits_of_memory`",
		aliasT2 + ".`state`",
		aliasT2 + ".`maintained_by`",
		aliasT2 + ".`created_by`",
		aliasT2 + ".`updated_by`",
		aliasT2 + ".`created_at`",
		aliasT2 + ".`updated_at`",
	}
	sb := squirrel.Select(fields...).
		From(m.table+" AS "+aliasT1).
		InnerJoin(
			fmt.Sprintf(
				"%s AS %s ON %s.`project_id` = %s.`project_id` AND %s.`deleted` = %s.`deleted`",
				perfCaseV2TableName, aliasT2, aliasT1, aliasT2, aliasT1, aliasT2,
			),
		).
		InnerJoin(
			fmt.Sprintf(
				"%s AS %s ON %s.`project_id` = %s.`project_id` AND %s.`plan_id` = %s.`plan_id` AND %s.`case_id` = %s.`case_id` AND %s.`deleted` = %s.`deleted`",
				perfPlanCaseRelationshipTableName, aliasT3, aliasT1, aliasT3, aliasT1, aliasT3, aliasT2, aliasT3,
				aliasT1, aliasT3,
			),
		).
		Where(
			fmt.Sprintf("%s.`project_id` = ? AND %s.`plan_id` = ? AND %s.`deleted` = ?", aliasT1, aliasT1, aliasT1),
			req.ProjectID, req.PlanID, constants.NotDeleted,
		)

	sb = sqlbuilder.SearchOptionsWithAlias(
		sb, aliasT2,
		sqlbuilder.WithCondition(req.PerfCaseV2Model, req.Condition),
		sqlbuilder.WithPagination(req.PerfCaseV2Model, req.Pagination),
		sqlbuilder.WithSort(req.PerfCaseV2Model, req.Sort),
	)

	var resp []*SearchCaseInPerfPlanV2Item
	err := utils.FindRows(
		ctx, m.conn, sb, func() any {
			return &resp
		},
	)
	return resp, err
}

type SearchRuleInPerfPlanV2Item struct {
	ProjectId   string         `db:"project_id"`  // 项目ID
	PlanId      string         `db:"plan_id"`     // 压测计划ID
	RuleId      string         `db:"rule_id"`     // 压测停止规则ID
	Name        string         `db:"name"`        // 压测停止规则名称
	Description sql.NullString `db:"description"` // 压测停止规则描述
	MetricType  string         `db:"metric_type"` // 指标类型
	Threshold   float64        `db:"threshold"`   // 阈值，保留2位小数（不同的指标类型单位不同）
	Duration    int64          `db:"duration"`    // 持续时间，单位为秒
	State       int64          `db:"state"`       // 规则状态（生效、失效）
	CreatedBy   string         `db:"created_by"`  // 创建者的用户ID
	UpdatedBy   string         `db:"updated_by"`  // 最近一次更新者的用户ID
	CreatedAt   time.Time      `db:"created_at"`  // 创建时间
	UpdatedAt   time.Time      `db:"updated_at"`  // 更新时间
}

func (m *customPerfPlanV2Model) FindCountRulesInPerfPlan(ctx context.Context, req SearchRuleInPerfPlanV2Req) (
	int64, error,
) {
	var (
		aliasT1 = "t1" // `perf_plan_v2`
		aliasT2 = "t2" // `perf_stop_rule`
		aliasT3 = "t3" // `perf_plan_rule_relationship`
	)

	sb := squirrel.Select("COUNT(*)").
		From(m.table+" AS "+aliasT1).
		InnerJoin(
			fmt.Sprintf(
				"%s AS %s ON %s.`project_id` = %s.`project_id` AND %s.`deleted` = %s.`deleted`",
				perfStopRuleTableName, aliasT2, aliasT1, aliasT2, aliasT1, aliasT2,
			),
		).
		InnerJoin(
			fmt.Sprintf(
				"%s AS %s ON %s.`project_id` = %s.`project_id` AND %s.`plan_id` = %s.`plan_id` AND %s.`rule_id` = %s.`rule_id` AND %s.`deleted` = %s.`deleted`",
				perfPlanRuleRelationshipTableName, aliasT3, aliasT1, aliasT3, aliasT1, aliasT3, aliasT2, aliasT3,
				aliasT1, aliasT3,
			),
		).
		Where(
			fmt.Sprintf("%s.`project_id` = ? AND %s.`plan_id` = ? AND %s.`deleted` = ?", aliasT1, aliasT1, aliasT1),
			req.ProjectID, req.PlanID, constants.NotDeleted,
		)
	sb = sqlbuilder.SearchOptionsWithAlias(sb, aliasT2, sqlbuilder.WithCondition(req.PerfStopRuleModel, req.Condition))

	return m.FindCount(ctx, sb)
}

func (m *customPerfPlanV2Model) FindRulesInPerfPlan(
	ctx context.Context, req SearchRuleInPerfPlanV2Req,
) ([]*SearchRuleInPerfPlanV2Item, error) {
	/*
		SQL:
		SELECT t1.`project_id`,
		       t1.`plan_id`,
			   t2.`rule_id`,
		       t2.`name`,
		       t2.`description`,
		       t2.`metric_type`,
		       t2.`threshold`,
		       t2.`duration`,
		       t2.`state`,
		       t2.`created_by`,
		       t2.`updated_by`,
		       t2.`created_at`,
		       t2.`updated_at`
		FROM `perf_plan_v2` AS t1
		INNER JOIN `perf_stop_rule` AS t2 ON
		    t1.`project_id` = t2.`project_id` AND
		    t1.`deleted` = t2.`deleted`
		INNER JOIN `perf_plan_rule_relationship` AS t3 ON
		    t1.`project_id` = t3.`project_id` AND
		    t1.`plan_id` = t3.`plan_id` AND
		    t2.`rule_id` = t3.`rule_id` AND
		    t1.`deleted` = t3.`deleted`
		WHERE t1.`project_id` = ?
		  AND t1.`plan_id` = ?
		  AND t1.`deleted` = ?
	*/

	var (
		aliasT1 = "t1" // `perf_plan_v2`
		aliasT2 = "t2" // `perf_stop_rule`
		aliasT3 = "t3" // `perf_plan_rule_relationship`
	)

	fields := []string{
		aliasT1 + ".`project_id`",
		aliasT1 + ".`plan_id`",
		aliasT2 + ".`rule_id`",
		aliasT2 + ".`name`",
		aliasT2 + ".`description`",
		aliasT2 + ".`metric_type`",
		aliasT2 + ".`threshold`",
		aliasT2 + ".`duration`",
		aliasT2 + ".`state`",
		aliasT2 + ".`created_by`",
		aliasT2 + ".`updated_by`",
		aliasT2 + ".`created_at`",
		aliasT2 + ".`updated_at`",
	}
	sb := squirrel.Select(fields...).
		From(m.table+" AS "+aliasT1).
		InnerJoin(
			fmt.Sprintf(
				"%s AS %s ON %s.`project_id` = %s.`project_id` AND %s.`deleted` = %s.`deleted`",
				perfStopRuleTableName, aliasT2, aliasT1, aliasT2, aliasT1, aliasT2,
			),
		).
		InnerJoin(
			fmt.Sprintf(
				"%s AS %s ON %s.`project_id` = %s.`project_id` AND %s.`plan_id` = %s.`plan_id` AND %s.`rule_id` = %s.`rule_id` AND %s.`deleted` = %s.`deleted`",
				perfPlanRuleRelationshipTableName, aliasT3, aliasT1, aliasT3, aliasT1, aliasT3, aliasT2, aliasT3,
				aliasT1, aliasT3,
			),
		).
		Where(
			fmt.Sprintf("%s.`project_id` = ? AND %s.`plan_id` = ? AND %s.`deleted` = ?", aliasT1, aliasT1, aliasT1),
			req.ProjectID, req.PlanID, constants.NotDeleted,
		)

	sb = sqlbuilder.SearchOptionsWithAlias(
		sb, aliasT2,
		sqlbuilder.WithCondition(req.PerfStopRuleModel, req.Condition),
		sqlbuilder.WithPagination(req.PerfStopRuleModel, req.Pagination),
		sqlbuilder.WithSort(req.PerfStopRuleModel, req.Sort),
	)

	var resp []*SearchRuleInPerfPlanV2Item
	err := utils.FindRows(
		ctx, m.conn, sb, func() any {
			return &resp
		},
	)
	return resp, err
}

type SearchProtobufConfigInPerfPlanV2Item struct {
	ProjectId    string         `db:"project_id"`    // 项目ID
	ConfigId     string         `db:"config_id"`     // Protobuf配置ID
	Name         string         `db:"name"`          // Protobuf配置名称
	Description  sql.NullString `db:"description"`   // Protobuf配置描述
	GitConfigId  string         `db:"git_config_id"` // Git配置ID
	ImportPath   string         `db:"import_path"`   // 导入路径（相对路径）
	ExcludePaths sql.NullString `db:"exclude_paths"` // 排除的路径（相对路径）
	ExcludeFiles sql.NullString `db:"exclude_files"` // 排除的文件（相对路径）
	CreatedBy    string         `db:"created_by"`    // 创建者的用户ID
	UpdatedBy    string         `db:"updated_by"`    // 最近一次更新者的用户ID
	CreatedAt    time.Time      `db:"created_at"`    // 创建时间
	UpdatedAt    time.Time      `db:"updated_at"`    // 更新时间
}

func (m *customPerfPlanV2Model) FindCountProtobufConfigsInPerfPlan(
	ctx context.Context, req SearchProtobufConfigInPerfPlanV2Req,
) (int64, error) {
	var (
		aliasT1 = "t1" // `perf_plan_v2`
		aliasT2 = "t2" // `protobuf_configuration`
		aliasT3 = "t3" // `protobuf_configuration_reference_relationship`
	)

	sb := squirrel.Select("COUNT(*)").
		From(m.table+" AS "+aliasT1).
		InnerJoin(
			fmt.Sprintf(
				"%s AS %s ON %s.`project_id` = %s.`project_id` AND %s.`deleted` = %s.`deleted`",
				protobufConfigurationTableName, aliasT2, aliasT1, aliasT2, aliasT1, aliasT2,
			),
		).
		InnerJoin(
			fmt.Sprintf(
				"%s AS %s ON %s.`project_id` = %s.`project_id` AND %s.`reference_type` = '%s' AND %s.`plan_id` = %s.`reference_id` AND %s.`config_id` = %s.`config_id` AND %s.`deleted` = %s.`deleted`",
				protobufConfigurationReferenceRelationshipTableName, aliasT3, aliasT1, aliasT3, aliasT3,
				common.ConstReferenceTypePerfPlan, aliasT1, aliasT3, aliasT2, aliasT3, aliasT1, aliasT3,
			),
		).
		Where(
			fmt.Sprintf("%s.`project_id` = ? AND %s.`plan_id` = ? AND %s.`deleted` = ?", aliasT1, aliasT1, aliasT1),
			req.ProjectID, req.PlanID, constants.NotDeleted,
		)
	sb = sqlbuilder.SearchOptionsWithAlias(
		sb, aliasT2, sqlbuilder.WithCondition(req.ProtobufConfigModel, req.Condition),
	)

	return m.FindCount(ctx, sb)
}

func (m *customPerfPlanV2Model) FindProtobufConfigsInPerfPlan(
	ctx context.Context, req SearchProtobufConfigInPerfPlanV2Req,
) ([]*SearchProtobufConfigInPerfPlanV2Item, error) {
	/*
		SQL:
		SELECT t1.`project_id`,
		       t1.`plan_id`,
		       t2.`config_id`,
		       t2.`name`,
		       t2.`description`,
		       t2.`git_config_id`,
		       t2.`import_path`,
		       t2.`exclude_paths`,
		       t2.`exclude_files`,
		       t2.`created_by`,
		       t2.`updated_by`,
		       t2.`created_at`,
		       t2.`updated_at`
		FROM `perf_plan_v2` AS t1
		         INNER JOIN `protobuf_configuration` AS t2 ON
		    t1.`project_id` = t2.`project_id` AND
		    t1.`deleted` = t2.`deleted`
		         INNER JOIN `protobuf_configuration_reference_relationship` AS t3 ON
		    t1.`project_id` = t3.`project_id` AND
		    t3.`reference_type` = ? AND
		    t1.`plan_id` = t3.`reference_id` AND
		    t2.`config_id` = t3.`config_id` AND
		    t1.`deleted` = t3.`deleted`
		WHERE t1.`project_id` = ?
		  AND t1.`plan_id` = ?
		  AND t1.`deleted` =
	*/

	var (
		aliasT1 = "t1" // `perf_plan_v2`
		aliasT2 = "t2" // `protobuf_configuration`
		aliasT3 = "t3" // `protobuf_configuration_reference_relationship`
	)

	fields := []string{
		aliasT1 + ".`project_id`",
		aliasT1 + ".`plan_id`",
		aliasT2 + ".`config_id`",
		aliasT2 + ".`name`",
		aliasT2 + ".`description`",
		aliasT2 + ".`git_config_id`",
		aliasT2 + ".`import_path`",
		aliasT2 + ".`exclude_paths`",
		aliasT2 + ".`exclude_files`",
		aliasT2 + ".`created_by`",
		aliasT2 + ".`updated_by`",
		aliasT2 + ".`created_at`",
		aliasT2 + ".`updated_at`",
	}
	sb := squirrel.Select(fields...).
		From(m.table+" AS "+aliasT1).
		InnerJoin(
			fmt.Sprintf(
				"%s AS %s ON %s.`project_id` = %s.`project_id` AND %s.`deleted` = %s.`deleted`",
				protobufConfigurationTableName, aliasT2, aliasT1, aliasT2, aliasT1, aliasT2,
			),
		).
		InnerJoin(
			fmt.Sprintf(
				"%s AS %s ON %s.`project_id` = %s.`project_id` AND %s.`reference_type` = '%s' AND %s.`plan_id` = %s.`reference_id` AND %s.`config_id` = %s.`config_id` AND %s.`deleted` = %s.`deleted`",
				protobufConfigurationReferenceRelationshipTableName, aliasT3, aliasT1, aliasT3, aliasT3,
				common.ConstReferenceTypePerfPlan, aliasT1, aliasT3, aliasT2, aliasT3, aliasT1, aliasT3,
			),
		).
		Where(
			fmt.Sprintf("%s.`project_id` = ? AND %s.`plan_id` = ? AND %s.`deleted` = ?", aliasT1, aliasT1, aliasT1),
			req.ProjectID, req.PlanID, constants.NotDeleted,
		)

	sb = sqlbuilder.SearchOptionsWithAlias(
		sb, aliasT2,
		sqlbuilder.WithCondition(req.ProtobufConfigModel, req.Condition),
		sqlbuilder.WithPagination(req.ProtobufConfigModel, req.Pagination),
		sqlbuilder.WithSort(req.ProtobufConfigModel, req.Sort),
	)

	var resp []*SearchProtobufConfigInPerfPlanV2Item
	err := utils.FindRows(
		ctx, m.conn, sb, func() any {
			return &resp
		},
	)
	return resp, err
}

func (m *customPerfPlanV2Model) FindByProtobufConfigID(ctx context.Context, projectID, configID string) (
	[]*PerfPlanV2, error,
) {
	sb := squirrel.Select(utils.AddTableNameToFields("t1", perfPlanV2FieldNames)...).
		From(m.table+" AS t1").
		InnerJoin(protobufConfigurationTableName+" AS t2 ON t1.`project_id` = t2.`project_id` AND t1.`protobuf_config_id` = t2.`config_id` AND t1.`deleted` = t2.`deleted`").
		Where(
			"t1.`project_id` = ? AND t1.`protobuf_config_id` = ? AND t1.`deleted` = ?",
			projectID, configID, constants.NotDeleted,
		)

	return m.FindNoCacheByQuery(ctx, sb)
}

func (m *customPerfPlanV2Model) FindByGeneralConfigID(ctx context.Context, projectID, configID string) (
	[]*PerfPlanV2, error,
) {
	sb := squirrel.Select(utils.AddTableNameToFields("t1", perfPlanV2FieldNames)...).
		From(m.table+" AS t1").
		InnerJoin(generalConfigurationTableName+" AS t2 ON t1.`project_id` = t2.`project_id` AND t1.`general_config_id` = t2.`config_id` AND t1.`deleted` = t2.`deleted`").
		Where(
			"t1.`project_id` = ? AND t1.`general_config_id` = ? AND t1.`deleted` = ?",
			projectID, configID, constants.NotDeleted,
		)

	return m.FindNoCacheByQuery(ctx, sb)
}

func (m *customPerfPlanV2Model) FindByAccountConfigID(ctx context.Context, projectID, configID string) (
	[]*PerfPlanV2, error,
) {
	sb := squirrel.Select(utils.AddTableNameToFields("t1", perfPlanV2FieldNames)...).
		From(m.table+" AS t1").
		InnerJoin(accountConfigurationTableName+" AS t2 ON t1.`project_id` = t2.`project_id` AND t1.`account_config_id` = t2.`config_id` AND t1.`deleted` = t2.`deleted`").
		Where(
			"t1.`project_id` = ? AND t1.`account_config_id` = ? AND t1.`deleted` = ?",
			projectID, configID, constants.NotDeleted,
		)

	return m.FindNoCacheByQuery(ctx, sb)
}

func (m *customPerfPlanV2Model) RemoveByPlanID(
	ctx context.Context, session sqlx.Session, projectID, planID string,
) (sql.Result, error) {
	keys := m.getKeysByPlanID(ctx, projectID, planID)

	return m.ExecCtx(
		ctx, func(ctx context.Context, conn sqlx.SqlConn) (sql.Result, error) {
			/*
				SQL:
				UPDATE `perf_plan_v2`
				SET `deleted` = ?, `deleted_by` = ?, `deleted_at` = ?
				WHERE `project_id` = ?
				  AND `plan_id` = ?
			*/
			stmt, values, err := squirrel.Update(m.table).
				SetMap(
					squirrel.Eq{
						"`deleted`":    constants.HasDeleted,
						"`deleted_by`": userinfo.FromContext(ctx).Account,
						"`deleted_at`": time.Now(),
					},
				).
				Where("`project_id` = ? AND `plan_id` = ?", projectID, planID).
				ToSql()
			if err != nil {
				return nil, err
			}

			if session != nil {
				return session.ExecCtx(ctx, stmt, values...)
			}
			return conn.ExecCtx(ctx, stmt, values...)
		}, keys...,
	)
}

func (m *customPerfPlanV2Model) getKeysByPlanID(ctx context.Context, projectID, planID string) []string {
	perfPlan, err := m.FindOneByProjectIdPlanId(ctx, projectID, planID)
	if err != nil {
		return make([]string, 0)
	}

	return []string{
		fmt.Sprintf("%s%v", cacheManagerPerfPlanV2IdPrefix, perfPlan.Id),
		fmt.Sprintf("%s%v:%v", cacheManagerPerfPlanV2ProjectIdPlanIdPrefix, perfPlan.ProjectId, perfPlan.PlanId),
	}
}

func (m *customPerfPlanV2Model) FindCountByCategoryId(
	ctx context.Context, cm CategoryModel, cond FindCountByCategoryCondition,
) (int64, error) {
	var (
		aliasT  = "t"
		aliasT1 = "t1"
	)

	sb := squirrel.Select("COUNT(*)").
		From(m.table+" AS "+aliasT).
		Where(
			fmt.Sprintf("%s.`project_id` = ? AND %s.`deleted` = ?", aliasT, aliasT),
			cond.ProjectId, constants.NotDeleted,
		)

	if cond.DrillDown {
		sub := cm.FindDescendantCategoriesSqlBuilder(
			GetCategoryTreeCondition{
				ProjectId:     cond.ProjectId,
				Type:          common.ConstCategoryTreeTypePerfPlan,
				CategoryId:    cond.CategoryId,
				OnlyDirectory: true,
			},
		)

		sb = sb.JoinClause(
			sub.Prefix("LEFT JOIN (").Suffix(
				fmt.Sprintf(
					") AS %s ON %s.`category_id` = %s.`category_id`", aliasT1, aliasT, aliasT1,
				),
			),
		).Where(fmt.Sprintf("%s.`category_id` IS NOT NULL", aliasT1))
	} else {
		sb = sb.Where(fmt.Sprintf("%s.`category_id` = ?", aliasT), cond.CategoryId)
	}

	return m.FindCount(ctx, sb)
}

func (m *customPerfPlanV2Model) FindCountPerfPlansV2(
	ctx context.Context, countBuilder searchPerfPlanV2CountBuilder,
) (int64, error) {
	return m.FindCount(ctx, countBuilder.SelectBuilder)
}

func (m *customPerfPlanV2Model) FindPerfPlansV2(ctx context.Context, selectBuilder searchPerfPlanV2SelectBuilder) (
	[]*SearchPerfPlanV2Item, error,
) {
	var resp []*SearchPerfPlanV2Item

	err := utils.FindRows(
		ctx, m.conn, selectBuilder.SelectBuilder, func() any {
			return &resp
		},
	)

	return resp, err
}
