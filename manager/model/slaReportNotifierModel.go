package model

import (
	"context"

	"github.com/Masterminds/squirrel"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/types"
)

var (
	_ SlaReportNotifierModel = (*customSlaReportNotifierModel)(nil)

	slaReportNotifierInsertFields = stringx.Remove(slaReportNotifierFieldNames, "`id`", "`create_time`", "`update_time`", "`deleted_by`", "`created_at`", "`updated_at`", "`deleted_at`")
)

type (
	// SlaReportNotifierModel is an interface to be customized, add more methods here,
	// and implement the added methods in customSlaReportNotifierModel.
	SlaReportNotifierModel interface {
		slaReportNotifierModel
		types.DBModel

		Trans(ctx context.Context, fn func(context context.Context, session sqlx.Session) error) error
		InsertBuilder(data *SlaReportNotifier) squirrel.InsertBuilder
		UpdateBuilder(data *SlaReportNotifier) squirrel.UpdateBuilder
		SelectBuilder() squirrel.SelectBuilder
		SelectCountBuilder() squirrel.SelectBuilder
		FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error)
		FindNoCacheByQuery(ctx context.Context, selectBuilder squirrel.SelectBuilder) ([]*SlaReportNotifier, error)
	}

	customSlaReportNotifierModel struct {
		*defaultSlaReportNotifierModel

		conn sqlx.SqlConn
	}
)

// NewSlaReportNotifierModel returns a model for the database table.
func NewSlaReportNotifierModel(conn sqlx.SqlConn, c cache.CacheConf, opts ...cache.Option) SlaReportNotifierModel {
	return &customSlaReportNotifierModel{
		defaultSlaReportNotifierModel: newSlaReportNotifierModel(conn, c, opts...),
		conn:                          conn,
	}
}

func (m *customSlaReportNotifierModel) Table() string {
	return m.table
}

func (m *customSlaReportNotifierModel) Fields() []string {
	return slaReportNotifierFieldNames
}

func (m *customSlaReportNotifierModel) Trans(ctx context.Context, fn func(context context.Context, session sqlx.Session) error) error {
	return m.TransactCtx(ctx, fn)
}

func (m *customSlaReportNotifierModel) InsertBuilder(data *SlaReportNotifier) squirrel.InsertBuilder {
	// TODO: fill the insert values
	return squirrel.Insert(m.table).Columns(slaReportNotifierInsertFields...).Values()
}

func (m *customSlaReportNotifierModel) UpdateBuilder(data *SlaReportNotifier) squirrel.UpdateBuilder {
	eq := squirrel.Eq{
		// TODO: fill the update values
	}
	return squirrel.Update(m.table).SetMap(eq).Where("`id` = ?", data.Id)
}

func (m *customSlaReportNotifierModel) SelectBuilder() squirrel.SelectBuilder {
	return squirrel.Select(slaReportNotifierFieldNames...).Where("`deleted` = ?", constants.NotDeleted).From(m.table)
}

func (m *customSlaReportNotifierModel) SelectCountBuilder() squirrel.SelectBuilder {
	return squirrel.Select("count(*)").Where("`deleted` = ?", constants.NotDeleted).From(m.table)
}

func (m *customSlaReportNotifierModel) FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error) {
	query, values, err := countBuilder.ToSql()
	if err != nil {
		return 0, err
	}

	var resp int64
	err = m.QueryRowNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return 0, err
	}
}

func (m *customSlaReportNotifierModel) FindNoCacheByQuery(ctx context.Context, selectBuilder squirrel.SelectBuilder) ([]*SlaReportNotifier, error) {
	query, values, err := selectBuilder.ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*SlaReportNotifier
	err = m.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}
