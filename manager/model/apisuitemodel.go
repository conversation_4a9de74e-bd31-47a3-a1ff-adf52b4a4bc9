package model

import (
	"context"
	"database/sql"
	"fmt"
	"time"

	"github.com/Masterminds/squirrel"
	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sqlbuilder"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/types"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/userinfo"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/utils"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
)

var (
	_                                ApiSuiteModel = (*customApiSuiteModel)(nil)
	VirtualApiSuiteReferenceModel    types.DBModel = (*virtualApiSuiteReferenceModel)(nil)
	VirtualSearchCaseInApiSuiteModel types.DBModel = (*virtualSearchCaseInApiSuiteModel)(nil)

	apiSuiteInsertFields = stringx.Remove(
		apiSuiteFieldNames, "`id`", "`create_time`", "`update_time`", "`deleted_by`", "`created_at`", "`updated_at`",
		"`deleted_at`",
	)

	virtualApiSuiteReferenceFieldNames    = builder.RawFieldNames(&SearchApiSuiteReferenceItem{})
	virtualSearchCaseInApiSuiteFieldNames = builder.RawFieldNames(&SearchCaseInApiSuiteItem{})
)

type (
	// ApiSuiteModel is an interface to be customized, add more methods here,
	// and implement the added methods in customApiSuiteModel.
	ApiSuiteModel interface {
		apiSuiteModel
		types.DBModel

		Trans(ctx context.Context, fn func(context context.Context, session sqlx.Session) error) error
		InsertBuilder(data *ApiSuite) squirrel.InsertBuilder
		UpdateBuilder(data *ApiSuite) squirrel.UpdateBuilder
		SelectBuilder() squirrel.SelectBuilder
		SelectCountBuilder() squirrel.SelectBuilder
		FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error)
		FindNoCacheByQuery(ctx context.Context, builder squirrel.SelectBuilder) ([]*ApiSuite, error)

		InsertTX(ctx context.Context, session sqlx.Session, data *ApiSuite) (sql.Result, error)
		UpdateTX(ctx context.Context, session sqlx.Session, data *ApiSuite) (sql.Result, error)
		DeleteTX(ctx context.Context, session sqlx.Session, id int64) error
		FindCountByCategoryId(ctx context.Context, cm CategoryModel, cond FindCountByCategoryCondition) (int64, error)
		RemoveBySuiteId(ctx context.Context, session sqlx.Session, projectId, suiteId string) (sql.Result, error)

		GenerateSearchApiCaseInApiSuiteSqlBuilder(req SearchApiCaseInApiSuiteReq) (
			searchApiCaseInApiSuiteSelectBuilder, searchApiCaseInApiSuiteCountBuilder,
		)
		FindCountApiCasesInApiSuite(ctx context.Context, countBuilder searchApiCaseInApiSuiteCountBuilder) (
			int64, error,
		)
		FindApiCasesInApiSuite(
			ctx context.Context, selectBuilder searchApiCaseInApiSuiteSelectBuilder,
		) ([]*SearchApiCaseInApiSuiteItem, error)

		GenerateSearchApiSuiteReferenceSqlBuilder(req SearchApiSuiteReferenceReq) (
			searchApiSuiteReferenceSelectBuilder, searchApiSuiteReferenceCountBuilder,
		)
		FindCountApiSuiteReference(ctx context.Context, countBuilder searchApiSuiteReferenceCountBuilder) (int64, error)
		FindApiSuiteReference(
			ctx context.Context, selectBuilder searchApiSuiteReferenceSelectBuilder,
		) ([]*SearchApiSuiteReferenceItem, error)

		GenerateSearchCaseInApiSuiteSqlBuilder(req SearchCaseInApiSuiteReq) (
			searchCaseInApiSuiteSelectBuilder, searchCaseInApiSuiteCountBuilder,
		)
		FindCountCasesInApiSuite(ctx context.Context, countBuilder searchCaseInApiSuiteCountBuilder) (
			int64, error,
		)
		FindCasesInApiSuite(
			ctx context.Context, selectBuilder searchCaseInApiSuiteSelectBuilder,
		) ([]*SearchCaseInApiSuiteItem, error)

		GenerateSearchServiceCaseNotInApiSuiteSqlBuilder(req SearchServiceCaseNotInApiSuiteReq) (
			searchCaseInApiSuiteSelectBuilder, searchCaseInApiSuiteCountBuilder,
		)
	}

	customApiSuiteModel struct {
		*defaultApiSuiteModel

		conn sqlx.SqlConn
	}
)

// NewApiSuiteModel returns a model for the database table.
func NewApiSuiteModel(conn sqlx.SqlConn, c cache.CacheConf) ApiSuiteModel {
	return &customApiSuiteModel{
		defaultApiSuiteModel: newApiSuiteModel(conn, c),
		conn:                 conn,
	}
}

func (m *customApiSuiteModel) Table() string {
	return m.table
}

func (m *customApiSuiteModel) Fields() []string {
	return apiSuiteFieldNames
}

func (m *customApiSuiteModel) Trans(
	ctx context.Context, fn func(context context.Context, session sqlx.Session) error,
) error {
	return m.TransactCtx(ctx, fn)
}

func (m *customApiSuiteModel) InsertBuilder(data *ApiSuite) squirrel.InsertBuilder {
	return squirrel.Insert(m.table).Columns(apiSuiteInsertFields...).Values(
		data.ProjectId, data.CategoryId, data.SuiteId, data.Name, data.Description, data.Priority, data.Tags,
		data.State, data.CaseExecutionMode, data.Deleted, data.MaintainedBy, data.CreatedBy, data.UpdatedBy,
	)
}

func (m *customApiSuiteModel) UpdateBuilder(data *ApiSuite) squirrel.UpdateBuilder {
	eq := squirrel.Eq{
		"`category_id`":         data.CategoryId,
		"`name`":                data.Name,
		"`description`":         data.Description,
		"`priority`":            data.Priority,
		"`tags`":                data.Tags,
		"`state`":               data.State,
		"`case_execution_mode`": data.CaseExecutionMode,
		"`deleted`":             data.Deleted,
		"`maintained_by`":       data.MaintainedBy,
		"`updated_by`":          data.UpdatedBy,
		"`deleted_by`":          data.DeletedBy,
		"`deleted_at`":          data.DeletedAt,
	}
	return squirrel.Update(m.table).SetMap(eq).Where("`id` = ?", data.Id)
}

func (m *customApiSuiteModel) SelectBuilder() squirrel.SelectBuilder {
	return squirrel.Select(apiSuiteFieldNames...).Where("`deleted` = ?", constants.NotDeleted).From(m.table)
}

func (m *customApiSuiteModel) SelectCountBuilder() squirrel.SelectBuilder {
	return squirrel.Select("count(*)").Where("`deleted` = ?", constants.NotDeleted).From(m.table)
}

func (m *customApiSuiteModel) FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error) {
	query, values, err := countBuilder.ToSql()
	if err != nil {
		return 0, err
	}

	var resp int64
	err = m.QueryRowNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return 0, err
	}
}

func (m *customApiSuiteModel) FindNoCacheByQuery(ctx context.Context, builder squirrel.SelectBuilder) (
	[]*ApiSuite, error,
) {
	query, values, err := builder.ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*ApiSuite
	err = m.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

func (m *customApiSuiteModel) InsertTX(ctx context.Context, session sqlx.Session, data *ApiSuite) (sql.Result, error) {
	managerApiSuiteIdKey := fmt.Sprintf("%s%v", cacheManagerApiSuiteIdPrefix, data.Id)
	managerApiSuiteProjectIdSuiteIdKey := fmt.Sprintf(
		"%s%v:%v", cacheManagerApiSuiteProjectIdSuiteIdPrefix, data.ProjectId, data.SuiteId,
	)

	return m.ExecCtx(
		ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
			query, values, err := m.InsertBuilder(data).ToSql()
			if err != nil {
				return nil, err
			}
			if session != nil {
				return session.ExecCtx(ctx, query, values...)
			}
			return conn.ExecCtx(ctx, query, values...)
		}, managerApiSuiteIdKey, managerApiSuiteProjectIdSuiteIdKey,
	)
}

func (m *customApiSuiteModel) UpdateTX(ctx context.Context, session sqlx.Session, data *ApiSuite) (sql.Result, error) {
	managerApiSuiteIdKey := fmt.Sprintf("%s%v", cacheManagerApiSuiteIdPrefix, data.Id)
	managerApiSuiteProjectIdSuiteIdKey := fmt.Sprintf(
		"%s%v:%v", cacheManagerApiSuiteProjectIdSuiteIdPrefix, data.ProjectId, data.SuiteId,
	)

	return m.ExecCtx(
		ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
			query, values, err := m.UpdateBuilder(data).ToSql()
			if err != nil {
				return nil, err
			}
			if session != nil {
				return session.ExecCtx(ctx, query, values...)
			}
			return conn.ExecCtx(ctx, query, values...)
		}, managerApiSuiteIdKey, managerApiSuiteProjectIdSuiteIdKey,
	)
}

func (m *customApiSuiteModel) DeleteTX(ctx context.Context, session sqlx.Session, id int64) error {
	data, err := m.FindOne(ctx, id)
	if err != nil {
		return err
	}

	managerApiSuiteIdKey := fmt.Sprintf("%s%v", cacheManagerApiSuiteIdPrefix, id)
	managerApiSuiteProjectIdSuiteIdKey := fmt.Sprintf(
		"%s%v:%v", cacheManagerApiSuiteProjectIdSuiteIdPrefix, data.ProjectId, data.SuiteId,
	)

	_, err = m.ExecCtx(
		ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
			query := fmt.Sprintf("delete from %s where `id` = ?", m.table)
			if session != nil {
				return session.ExecCtx(ctx, query, id)
			}
			return conn.ExecCtx(ctx, query, id)
		}, managerApiSuiteIdKey, managerApiSuiteProjectIdSuiteIdKey,
	)

	return err
}

func (m *customApiSuiteModel) FindCountByCategoryId(
	ctx context.Context, cm CategoryModel, cond FindCountByCategoryCondition,
) (int64, error) {
	alias := "t"
	sb := squirrel.Select("COUNT(*)").
		From(fmt.Sprintf("%s AS %s", m.Table(), alias)).
		Where(
			fmt.Sprintf("%s.`project_id` = ? AND %s.`deleted` = ?", alias, alias), cond.ProjectId, constants.NotDeleted,
		)

	if cond.DrillDown {
		sub := cm.FindDescendantCategoriesSqlBuilder(
			GetCategoryTreeCondition{
				ProjectId:     cond.ProjectId,
				Type:          common.ConstCategoryTreeTypeApiSuite,
				CategoryId:    cond.CategoryId,
				OnlyDirectory: true,
			},
		)

		sb = sb.JoinClause(
			sub.Prefix("LEFT JOIN (").Suffix(
				fmt.Sprintf(
					") AS t1 ON %s.`category_id` = t1.`category_id`", alias,
				),
			),
		).Where("t1.`category_id` IS NOT NULL")
	} else {
		sb = sb.Where(fmt.Sprintf("%s.`category_id` = ?", alias), cond.CategoryId)
	}

	return m.FindCount(ctx, sb)
}

func (m *customApiSuiteModel) RemoveBySuiteId(
	ctx context.Context, session sqlx.Session, projectId, suiteId string,
) (sql.Result, error) {
	keys := m.getKeysBySuiteId(ctx, projectId, suiteId)

	return m.ExecCtx(
		ctx, func(ctx context.Context, conn sqlx.SqlConn) (sql.Result, error) {
			/*
				SQL:
				UPDATE `api_suite`
				SET `deleted` = ?, `deleted_by` = ?, `deleted_at` = ?
				WHERE `project_id` = ?
				  AND `suite_id` = ?
			*/
			stmt, values, err := squirrel.Update(m.table).
				SetMap(
					squirrel.Eq{
						"`deleted`":    constants.HasDeleted,
						"`deleted_by`": userinfo.FromContext(ctx).Account,
						"`deleted_at`": time.Now(),
					},
				).
				Where("`project_id` = ? AND `suite_id` = ?", projectId, suiteId).
				ToSql()
			if err != nil {
				return nil, err
			}

			if session != nil {
				return session.ExecCtx(ctx, stmt, values...)
			}
			return conn.ExecCtx(ctx, stmt, values...)
		}, keys...,
	)
}

func (m *customApiSuiteModel) getKeysBySuiteId(ctx context.Context, projectId, suiteId string) []string {
	apiSuite, err := m.FindOneByProjectIdSuiteId(ctx, projectId, suiteId)
	if err != nil {
		return make([]string, 0)
	}

	return []string{
		fmt.Sprintf("%s%v", cacheManagerApiSuiteIdPrefix, apiSuite.Id),
		fmt.Sprintf("%s%v:%v", cacheManagerApiSuiteProjectIdSuiteIdPrefix, apiSuite.ProjectId, apiSuite.SuiteId),
	}
}

type SearchApiCaseInApiSuiteItem struct {
	ApiCase
}

type searchApiCaseInApiSuiteSelectBuilder struct {
	squirrel.SelectBuilder
}

type searchApiCaseInApiSuiteCountBuilder struct {
	squirrel.SelectBuilder
}

func (m *customApiSuiteModel) GenerateSearchApiCaseInApiSuiteSqlBuilder(req SearchApiCaseInApiSuiteReq) (
	searchApiCaseInApiSuiteSelectBuilder, searchApiCaseInApiSuiteCountBuilder,
) {
	/*
		SQL:
		SELECT t2.*
		FROM `api_suite` AS t1,
		     `api_case` AS t2,
		     `api_case_reference_relationship` AS t3
		WHERE t1.`project_id` = t2.`project_id`
		  AND t1.`project_id` = t3.`project_id`
		  AND t1.`suite_id` = t3.`reference_id`
		  AND t2.`case_id` = t3.`case_id`
		  AND t1.`deleted` = t2.`deleted`
		  AND t1.`deleted` = t3.`deleted`
		  AND t1.`project_id` = ?
		  AND t1.`suite_id` = ?
		  AND t1.`deleted` = 0
		  AND t2.`latest` = 1
		  AND t3.`reference_type` = 'API_SUITE'
	*/

	var sb, scb squirrel.SelectBuilder

	sb = squirrel.Select(utils.AddTableNameToFields("t2", apiCaseFieldNames)...).
		From(
			fmt.Sprintf(
				"%s AS t1, %s AS t2, %s AS t3", m.table, apiCaseTableName, apiCaseReferenceRelationshipTableName,
			),
		).
		Where("t1.`project_id` = t2.`project_id` AND t1.`project_id` = t3.`project_id`").
		Where("t1.`suite_id` = t3.`reference_id` AND t2.`case_id` = t3.`case_id`").
		Where("t1.`deleted` = t2.`deleted` AND t1.`deleted` = t3.`deleted`").
		Where(
			"t1.`project_id` = ? AND t1.`suite_id` = ? AND t1.`deleted` = ? AND t2.`latest` = ? AND t3.`reference_type` = ?",
			req.ProjectId, req.SuiteId, constants.NotDeleted, constants.IsLatestVersion,
			common.ConstReferenceTypeApiSuite,
		)
	scb = squirrel.Select("COUNT(*)").
		From(
			fmt.Sprintf(
				"%s AS t1, %s AS t2, %s AS t3", m.table, apiCaseTableName, apiCaseReferenceRelationshipTableName,
			),
		).
		Where("t1.`project_id` = t2.`project_id` AND t1.`project_id` = t3.`project_id`").
		Where("t1.`suite_id` = t3.`reference_id` AND t2.`case_id` = t3.`case_id`").
		Where("t1.`deleted` = t2.`deleted` AND t1.`deleted` = t3.`deleted`").
		Where(
			"t1.`project_id` = ? AND t1.`suite_id` = ? AND t1.`deleted` = ? AND t2.`latest` = ? AND t3.`reference_type` = ?",
			req.ProjectId, req.SuiteId, constants.NotDeleted, constants.IsLatestVersion,
			common.ConstReferenceTypeApiSuite,
		)

	sb = sqlbuilder.SearchOptionsWithAlias(
		sb, "t2", sqlbuilder.WithCondition(m, req.Condition), sqlbuilder.WithPagination(m, req.Pagination),
		sqlbuilder.WithSort(m, req.Sort),
	)
	scb = sqlbuilder.SearchOptionsWithAlias(scb, "t2", sqlbuilder.WithCondition(m, req.Condition))

	return searchApiCaseInApiSuiteSelectBuilder{SelectBuilder: sb}, searchApiCaseInApiSuiteCountBuilder{SelectBuilder: scb}
}

func (m *customApiSuiteModel) FindCountApiCasesInApiSuite(
	ctx context.Context, countBuilder searchApiCaseInApiSuiteCountBuilder,
) (int64, error) {
	query, values, err := countBuilder.ToSql()
	if err != nil {
		return 0, err
	}

	var resp int64
	err = m.QueryRowNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return 0, err
	}
}

func (m *customApiSuiteModel) FindApiCasesInApiSuite(
	ctx context.Context, selectBuilder searchApiCaseInApiSuiteSelectBuilder,
) ([]*SearchApiCaseInApiSuiteItem, error) {
	query, values, err := selectBuilder.ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*SearchApiCaseInApiSuiteItem
	err = m.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

type virtualApiSuiteReferenceModel struct{}

func (m *virtualApiSuiteReferenceModel) Table() string {
	return "`virtual_api_suite_reference`"
}

func (m *virtualApiSuiteReferenceModel) Fields() []string {
	return virtualApiSuiteReferenceFieldNames
}

type SearchApiSuiteReferenceItem struct {
	ProjectId      string         `db:"project_id"`      // 项目ID
	SuiteId        string         `db:"suite_id"`        // 集合ID
	ReferenceType  string         `db:"reference_type"`  // 引用对象类型（API计划）
	ReferenceId    string         `db:"reference_id"`    // 引用对象ID（API计划ID）
	Name           string         `db:"name"`            // 引用对象名称
	Description    sql.NullString `db:"description"`     // 引用对象描述
	Priority       int64          `db:"priority"`        // 优先级（NULL、P0、P1、P2、P3...）
	Tags           sql.NullString `db:"tags"`            // 标签
	State          int64          `db:"state"`           // 引用对象状态（生效、失效）
	ReferenceState int64          `db:"reference_state"` // 集合的引用状态
	MaintainedBy   sql.NullString `db:"maintained_by"`   // 维护者的用户ID
	CreatedBy      string         `db:"created_by"`      // 创建者的用户ID
	UpdatedBy      string         `db:"updated_by"`      // 最近一次更新者的用户ID
	CreatedAt      time.Time      `db:"created_at"`      // 创建时间
	UpdatedAt      time.Time      `db:"updated_at"`      // 更新时间
}

type searchApiSuiteReferenceSelectBuilder struct {
	squirrel.SelectBuilder
}

type searchApiSuiteReferenceCountBuilder struct {
	squirrel.SelectBuilder
}

func (m *customApiSuiteModel) GenerateSearchApiSuiteReferenceSqlBuilder(req SearchApiSuiteReferenceReq) (
	searchApiSuiteReferenceSelectBuilder, searchApiSuiteReferenceCountBuilder,
) {
	/*
		SQL:
		SELECT t.`project_id`,
		       t.`suite_id`,
		       t.`reference_type`,
		       t.`reference_id`,
		       t.`name`,
		       t.`description`,
		       t.`priority`,
		       t.`tags`,
		       t.`state`,
		       t.`reference_state`,
		       t.`maintained_by`,
		       t.`created_by`,
		       t.`updated_by`,
		       t.`created_at`,
		       t.`updated_at`
		FROM (SELECT t1.`project_id`,
		             t1.`suite_id`,
		             'API_PLAN'   AS `reference_type`,
		             t3.`plan_id` AS `reference_id`,
		             t3.`name`,
		             t3.`description`,
		             t3.`priority`,
		             t3.`tags`,
		             t3.`state`,
		             t2.`state`   AS `reference_state`,
		             t3.`maintained_by`,
		             t3.`created_by`,
		             t3.`updated_by`,
		             t3.`created_at`,
		             t3.`updated_at`
		      FROM `api_suite` AS t1
		               INNER JOIN `api_plan_reference_relationship` AS t2 ON t1.`project_id` = t2.`project_id` AND t2.`reference_type` = 'API_SUITE' AND t1.`suite_id` = t2.`reference_id` AND t1.`deleted` = t2.`deleted`
		               LEFT JOIN `api_plan` AS t3 ON t1.`project_id` = t3.`project_id` AND t2.`plan_id` = t3.`plan_id` AND t1.`deleted` = t3.`deleted`
		      WHERE t1.`project_id` = ?
		        AND t1.`suite_id` = ?
		        AND t1.`deleted` = 0
		        AND t3.`id` IS NOT NULL) AS t
	*/

	fields := []string{
		"t1.`project_id`",
		"t1.`suite_id`",
		fmt.Sprintf("'%s' AS `reference_type`", common.ConstReferenceTypeApiPlan),
		"t3.`plan_id` AS `reference_id`",
		"t3.`name`",
		"t3.`description`",
		"t3.`priority`",
		"t3.`tags`",
		"t3.`state`",
		"t2.`state` AS `reference_state`",
		"t3.`maintained_by`",
		"t3.`created_by`",
		"t3.`updated_by`",
		"t3.`created_at`",
		"t3.`updated_at`",
	}
	tmp := squirrel.Select(fields...).From(m.table+" AS t1").
		InnerJoin(
			fmt.Sprintf(
				"%s AS t2 ON t1.`project_id` = t2.`project_id` AND t2.`reference_type` = '%s' AND t1.`suite_id` = t2.`reference_id` AND t1.`deleted` = t2.`deleted`",
				apiPlanReferenceRelationshipTableName, common.ConstReferenceTypeApiSuite,
			),
		).
		LeftJoin(apiPlanTableName+" AS t3 ON t1.`project_id` = t3.`project_id` AND t2.`plan_id` = t3.`plan_id` AND t1.`deleted` = t3.`deleted`").
		Where(
			"t1.`project_id` = ? AND t1.`suite_id` = ? AND t1.`deleted` = ? AND t3.`id` IS NOT NULL", req.ProjectId,
			req.SuiteId, constants.NotDeleted,
		)

	rm := VirtualApiSuiteReferenceModel

	alias := "t"
	sb := sqlbuilder.SearchOptionsWithAlias(
		squirrel.Select(utils.AddTableNameToFields(alias, rm.Fields())...).FromSelect(tmp, alias),
		alias,
		sqlbuilder.WithCondition(rm, req.Condition),
		sqlbuilder.WithPagination(rm, req.Pagination),
		sqlbuilder.WithSort(rm, req.Sort),
	)
	scb := sqlbuilder.SearchOptionsWithAlias(
		squirrel.Select("COUNT(*)").FromSelect(tmp, alias),
		alias,
		sqlbuilder.WithCondition(rm, req.Condition),
	)

	return searchApiSuiteReferenceSelectBuilder{sb}, searchApiSuiteReferenceCountBuilder{scb}
}

func (m *customApiSuiteModel) FindCountApiSuiteReference(
	ctx context.Context, countBuilder searchApiSuiteReferenceCountBuilder,
) (int64, error) {
	return m.FindCount(ctx, countBuilder.SelectBuilder)
}

func (m *customApiSuiteModel) FindApiSuiteReference(
	ctx context.Context, selectBuilder searchApiSuiteReferenceSelectBuilder,
) ([]*SearchApiSuiteReferenceItem, error) {
	var resp []*SearchApiSuiteReferenceItem

	err := utils.FindRows(
		ctx, m.conn, selectBuilder.SelectBuilder, func() any {
			return &resp
		},
	)

	return resp, err
}

// [2024-04-16] delete this method:
// Validate 判断API集合是否可被执行
//func (x *ApiSuite) Validate() bool {
//	// API集合固有状态为生效才能被执行
//	return x.State == int64(constants.EnableStatus)
//}

type virtualSearchCaseInApiSuiteModel struct{}

func (m *virtualSearchCaseInApiSuiteModel) Table() string {
	return "`virtual_search_case_in_api_suite`"
}

func (m *virtualSearchCaseInApiSuiteModel) Fields() []string {
	return virtualSearchCaseInApiSuiteFieldNames
}

type SearchCaseInApiSuiteItem struct {
	ProjectId     string         `db:"project_id"`     // 项目ID
	CategoryId    string         `db:"category_id"`    // 场景用例的所属分类ID
	DocumentId    string         `db:"document_id"`    // 接口用例的接口文档ID
	CaseType      string         `db:"case_type"`      // 用例类型
	CaseId        string         `db:"case_id"`        // 用例ID
	Name          string         `db:"name"`           // 用例名称
	Description   sql.NullString `db:"description"`    // 用例描述
	Priority      int64          `db:"priority"`       // 优先级（NULL、P0、P1、P2、P3...）
	Tags          sql.NullString `db:"tags"`           // 标签
	State         string         `db:"state"`          // 用例状态（新、待实现、待维护、待审核、已上线）
	AccountConfig string         `db:"account_config"` // 池账号配置数
	Version       string         `db:"version"`        // 用例版本
	MaintainedBy  sql.NullString `db:"maintained_by"`  // 维护者的用户ID
	CreatedBy     string         `db:"created_by"`     // 创建者的用户ID
	UpdatedBy     string         `db:"updated_by"`     // 最近一次更新者的用户ID
	CreatedAt     time.Time      `db:"created_at"`     // 创建时间
	UpdatedAt     time.Time      `db:"updated_at"`     // 更新时间
}

type searchCaseInApiSuiteSelectBuilder struct {
	squirrel.SelectBuilder
}

type searchCaseInApiSuiteCountBuilder struct {
	squirrel.SelectBuilder
}

func (m *customApiSuiteModel) GenerateSearchCaseInApiSuiteSqlBuilder(req SearchCaseInApiSuiteReq) (
	searchCaseInApiSuiteSelectBuilder, searchCaseInApiSuiteCountBuilder,
) {
	/*
		SELECT
		  t3.*
		FROM
		  (
		    SELECT
		      COALESCE(t1.`project_id`, t2.`project_id`) AS `project_id`,
		      IF(t1.`category_id` IS NULL, '', t1.`category_id`) AS `category_id`,
		      IF(t2.`document_id` IS NULL, '', t2.`document_id`) AS `document_id`,
			  t.`reference_type` AS `case_type`,
		      COALESCE(t1.`case_id`, t2.`case_id`) AS `case_id`,
		      COALESCE(t1.`name`, t2.`name`) AS `name`,
		      COALESCE(t1.`description`, t2.`description`) AS `description`,
		      COALESCE(t1.`priority`, t2.`priority`) AS `priority`,
		      COALESCE(t1.`tags`, t2.`tags`) AS `tags`,
		      COALESCE(t1.`state`, t2.`state`) AS `state`,
		      COALESCE(t1.`account_config`, t2.`account_config`) AS `account_config`,
		      COALESCE(t1.`version`, t2.`version`) AS `version`,
			  COALESCE(t1.`maintained_by`, t2.`maintained_by`) AS `maintained_by`,
		      COALESCE(t1.`created_by`, t2.`created_by`) AS `created_by`,
		      COALESCE(t1.`updated_by`, t2.`updated_by`) AS `updated_by`,
		      COALESCE(t1.`created_at`, t2.`created_at`) AS `created_at`,
		      COALESCE(t1.`updated_at`, t2.`updated_at`) AS `updated_at`
		    FROM
		      `api_suite_reference_relationship` AS t
		      LEFT JOIN `api_case` AS t1 ON t.`reference_type` = 'API_CASE'
		      AND t1.`latest` = 1
		      AND t.`reference_id` = t1.`case_id`
		      LEFT JOIN `interface_case` AS t2 ON t.`reference_type` = 'INTERFACE_CASE'
		      AND t2.`latest` = 1
		      AND t.`reference_id` = t2.`case_id`
		    WHERE
		      t.`deleted` = 0
		      AND t.`suite_id` = ?
		      AND t.`project_id` = ?
		  ) AS t3;
	*/

	fields := []string{
		"COALESCE(t1.`project_id`, t2.`project_id`) AS `project_id`",
		"IF(t1.`category_id` IS NULL, '', t1.`category_id`) AS `category_id`",
		"IF(t2.`document_id` IS NULL, '', t2.`document_id`) AS `document_id`",
		"t.`reference_type` AS `case_type`",
		"COALESCE(t1.`case_id`, t2.`case_id`) AS `case_id`",
		"COALESCE(t1.`name`, t2.`name`) AS `name`",
		"COALESCE(t1.`description`, t2.`description`) AS `description`",
		"COALESCE(t1.`priority`, t2.`priority`) AS `priority`",
		"COALESCE(t1.`tags`, t2.`tags`) AS `tags`",
		"COALESCE(t1.`state`, t2.`state`) AS `state`",
		"COALESCE(t1.`account_config`, t2.`account_config`) AS `account_config`",
		"COALESCE(t1.`version`, t2.`version`) AS `version`",
		"COALESCE(t1.`maintained_by`, t2.`maintained_by`) AS `maintained_by`",
		"COALESCE(t1.`created_by`, t2.`created_by`) AS `created_by`",
		"COALESCE(t1.`updated_by`, t2.`updated_by`) AS `updated_by`",
		"COALESCE(t1.`created_at`, t2.`created_at`) AS `created_at`",
		"COALESCE(t1.`updated_at`, t2.`updated_at`) AS `updated_at`",
	}

	tmp := squirrel.Select(fields...).
		From(apiSuiteReferenceRelationshipTableName+" AS t").
		LeftJoin(
			apiCaseTableName+" AS t1 ON t.`reference_type` = ? AND t1.`latest` = ? AND t.`reference_id` = t1.`case_id`",
			common.ConstReferenceTypeApiCase, constants.IsLatestVersion,
		).
		LeftJoin(
			interfaceCaseTableName+" AS t2 ON t.`reference_type` = ? AND t2.`latest` = ? AND t.`reference_id` = t2.`case_id`",
			common.ConstReferenceTypeInterfaceCase, constants.IsLatestVersion,
		).
		Where("t.`deleted` = ? AND t.`suite_id` = ? AND t.`project_id` = ?", constants.NotDeleted, req.SuiteId, req.ProjectId)

	alias := "t3"
	vm := VirtualSearchCaseInApiSuiteModel
	sb := sqlbuilder.SearchOptionsWithAlias(
		squirrel.Select(utils.AddTableNameToFields(alias, vm.Fields())...).FromSelect(tmp, alias),
		alias,
		sqlbuilder.WithCondition(vm, req.Condition),
		sqlbuilder.WithPagination(vm, req.Pagination),
		sqlbuilder.WithSort(vm, req.Sort),
	)
	scb := sqlbuilder.SearchOptionsWithAlias(
		squirrel.Select("COUNT(*)").FromSelect(tmp, alias),
		alias,
		sqlbuilder.WithCondition(vm, req.Condition),
	)

	return searchCaseInApiSuiteSelectBuilder{SelectBuilder: sb}, searchCaseInApiSuiteCountBuilder{SelectBuilder: scb}
}

func (m *customApiSuiteModel) FindCountCasesInApiSuite(
	ctx context.Context, countBuilder searchCaseInApiSuiteCountBuilder,
) (int64, error) {
	query, values, err := countBuilder.ToSql()
	if err != nil {
		return 0, err
	}

	var resp int64
	err = m.QueryRowNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return 0, err
	}
}

func (m *customApiSuiteModel) FindCasesInApiSuite(
	ctx context.Context, selectBuilder searchCaseInApiSuiteSelectBuilder,
) ([]*SearchCaseInApiSuiteItem, error) {
	query, values, err := selectBuilder.ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*SearchCaseInApiSuiteItem
	err = m.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

func (m *customApiSuiteModel) GenerateSearchServiceCaseNotInApiSuiteSqlBuilder(req SearchServiceCaseNotInApiSuiteReq) (
	searchCaseInApiSuiteSelectBuilder, searchCaseInApiSuiteCountBuilder,
) {
	/*
		SELECT
		  t1.*
		FROM(
		    SELECT
		      `project_id`,
		      `category_id`,
		      '' AS `document_id`,
		      'API_CASE' AS `case_type`,
		      `case_id`,
		      `name`,
		      `description`,
		      `priority`,
		      `tags`,
		      `state`,
		      `account_config`,
			  `version`,
			  `maintained_by`,
		      `created_by`,
		      `updated_by`,
		      `created_at`,
		      `updated_at`
		    FROM
		      `api_case`
		    WHERE
		      `project_id` = ?
		      AND `deleted` = 0
		      AND `latest` = 1
		      AND `case_id` IN (?, ?, ?)
		  ) AS t1
		  LEFT JOIN `api_suite_reference_relationship` AS t2 ON t1.`project_id` = t2.`project_id`
		  AND t1.`case_type` = t2.`reference_type`
		  AND t1.`case_id` = t2.`reference_id`
		  AND t2.`deleted` = 0
		  AND t2.`suite_id` = ?
		WHERE
		  t2.`reference_id` IS NULL
		GROUP BY
		  t1.`project_id`,
		  t1.`case_id`
		UNION
		SELECT
		  t1.*,
		  IF(t2.`reference_id` IS NULL, 0, 1) AS `added`
		FROM(
		    SELECT
		      `project_id`,
		      '' AS `category_id`,
		      `document_id`,
		      'INTERFACE_CASE' AS `case_type`,
		      `case_id`,
		      `name`,
		      `description`,
		      `priority`,
		      `tags`,
		      `state`,
		      `account_config`,
			  `version`,
			  `maintained_by`,
		      `created_by`,
		      `updated_by`,
		      `created_at`,
		      `updated_at`
		    FROM
		      `interface_case`
		    WHERE
		      `project_id` = ?
		      AND `deleted` = 0
		      AND `latest` = 1
		      AND `case_id` IN (?, ?, ?)
		  ) AS t1
		  LEFT JOIN `api_suite_reference_relationship` AS t2 ON t1.`project_id` = t2.`project_id`
		  AND t1.`case_type` = t2.`reference_type`
		  AND t1.`case_id` = t2.`reference_id`
		  AND t2.`deleted` = 0
		  AND t2.`suite_id` = ?
		WHERE
		  t2.`reference_id` IS NULL
		GROUP BY
		  t1.`project_id`,
		  t1.`case_id`;
	*/

	var tmp squirrel.SelectBuilder
	sb1 := m.generateSearchServiceCaseNotInApiSuiteUnitSqlBuilder(common.ConstReferenceTypeApiCase, req)
	sb2 := m.generateSearchServiceCaseNotInApiSuiteUnitSqlBuilder(common.ConstReferenceTypeInterfaceCase, req)
	query2, values2, _ := sb2.ToSql()
	if len(req.ApiCaseIds) > 0 {
		tmp = sb1
		if len(req.InterfaceCaseIds) > 0 {
			tmp = sb1.Suffix("UNION "+query2, values2...)
		}
	} else {
		tmp = sb2
	}

	alias := "t"
	vm := VirtualSearchCaseInApiSuiteModel
	sb := sqlbuilder.SearchOptionsWithAlias(
		squirrel.Select(utils.AddTableNameToFields(alias, vm.Fields())...).FromSelect(tmp, alias),
		alias,
		sqlbuilder.WithCondition(vm, req.Condition),
		sqlbuilder.WithPagination(vm, req.Pagination),
		sqlbuilder.WithSort(vm, req.Sort),
	)
	scb := sqlbuilder.SearchOptionsWithAlias(
		squirrel.Select("COUNT(*)").FromSelect(tmp, alias),
		alias,
		sqlbuilder.WithCondition(vm, req.Condition),
	)

	return searchCaseInApiSuiteSelectBuilder{SelectBuilder: sb}, searchCaseInApiSuiteCountBuilder{SelectBuilder: scb}
}

func (m *customApiSuiteModel) generateSearchServiceCaseNotInApiSuiteUnitSqlBuilder(
	caseType string, req SearchServiceCaseNotInApiSuiteReq,
) squirrel.SelectBuilder {
	fields1 := []string{
		"`project_id`",
		"`category_id`",
		"`document_id`",
		fmt.Sprintf("'%s' AS `case_type`", caseType),
		"`case_id`",
		"`name`",
		"`description`",
		"`priority`",
		"`tags`",
		"`state`",
		"`account_config`",
		"`version`",
		"`maintained_by`",
		"`created_by`",
		"`updated_by`",
		"`created_at`",
		"`updated_at`",
	}
	var (
		caseTableName string
		caseIds       []string
		sb            squirrel.SelectBuilder
	)
	if caseType == common.ConstReferenceTypeApiCase {
		fields1[2] = "'' AS `document_id`"
		caseTableName = apiCaseTableName
		caseIds = req.ApiCaseIds
	} else if caseType == common.ConstReferenceTypeInterfaceCase {
		fields1[1] = "'' AS `category_id`"
		caseTableName = interfaceCaseTableName
		caseIds = req.InterfaceCaseIds
	}

	if len(caseIds) == 0 {
		return sb
	}

	alias1 := "t1"
	sb1 := squirrel.Select(fields1...).From(caseTableName).Where(
		squirrel.Eq{
			"project_id": req.ProjectId,
			"deleted":    constants.NotDeleted,
			"latest":     constants.IsLatestVersion,
			"case_id":    caseIds,
		},
	)
	vm := VirtualSearchCaseInApiSuiteModel
	alias2 := "t2"
	fields := append(utils.AddTableNameToFields(alias1, vm.Fields()), fmt.Sprintf("IF(%s.`reference_id` IS NULL, 0, 1) AS `added`", alias2))
	sb = squirrel.Select(fields...).FromSelect(sb1, alias1).
		LeftJoin(
			fmt.Sprintf("%s AS %s ON %s.`project_id` = %s.`project_id` AND %s.`case_type` = %s.`reference_type` AND %s.`case_id` = %s.`reference_id` AND %s.`deleted` = ? AND %s.`suite_id` = ?",
				apiSuiteReferenceRelationshipTableName, alias2, alias1, alias2, alias1, alias2, alias1, alias2, alias2, alias2,
			),
			constants.NotDeleted, req.SuiteId,
		).
		Where(alias2+".`reference_id` IS NULL").
		GroupBy(alias1+".`project_id`", alias1+".`case_id`")
	return sb
}
