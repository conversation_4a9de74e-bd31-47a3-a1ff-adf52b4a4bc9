// Code generated by goctl. DO NOT EDIT.
// versions:
//  goctl version: 1.7.4

package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlc"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
)

var (
	perfStopRuleReferenceRelationshipTableName           = "`perf_stop_rule_reference_relationship`"
	perfStopRuleReferenceRelationshipFieldNames          = builder.RawFieldNames(&PerfStopRuleReferenceRelationship{})
	perfStopRuleReferenceRelationshipRows                = strings.Join(perfStopRuleReferenceRelationshipFieldNames, ",")
	perfStopRuleReferenceRelationshipRowsExpectAutoSet   = strings.Join(stringx.Remove(perfStopRuleReferenceRelationshipFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), ",")
	perfStopRuleReferenceRelationshipRowsWithPlaceHolder = strings.Join(stringx.Remove(perfStopRuleReferenceRelationshipFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), "=?,") + "=?"

	cacheManagerPerfStopRuleReferenceRelationshipIdPrefix = "cache:manager:perfStopRuleReferenceRelationship:id:"
)

type (
	perfStopRuleReferenceRelationshipModel interface {
		Insert(ctx context.Context, session sqlx.Session, data *PerfStopRuleReferenceRelationship) (sql.Result, error)
		FindOne(ctx context.Context, id int64) (*PerfStopRuleReferenceRelationship, error)
		Update(ctx context.Context, session sqlx.Session, data *PerfStopRuleReferenceRelationship) (sql.Result, error)
		Delete(ctx context.Context, session sqlx.Session, id int64) error
		LogicDelete(ctx context.Context, session sqlx.Session, id int64) error
	}

	defaultPerfStopRuleReferenceRelationshipModel struct {
		sqlc.CachedConn
		table string
	}

	PerfStopRuleReferenceRelationship struct {
		Id            int64          `db:"id"`
		ProjectId     string         `db:"project_id"`     // 项目ID
		ReferenceType string         `db:"reference_type"` // 引用类型（压测计划）
		ReferenceId   string         `db:"reference_id"`   // 引用ID（压测计划ID）
		RuleId        string         `db:"rule_id"`        // 压测停止规则ID
		Deleted       int64          `db:"deleted"`        // 逻辑删除标识（未删除、已删除）
		CreatedBy     string         `db:"created_by"`     // 创建者的用户ID
		UpdatedBy     string         `db:"updated_by"`     // 最近一次更新者的用户ID
		DeletedBy     sql.NullString `db:"deleted_by"`     // 删除者的用户ID
		CreatedAt     time.Time      `db:"created_at"`     // 创建时间
		UpdatedAt     time.Time      `db:"updated_at"`     // 更新时间
		DeletedAt     sql.NullTime   `db:"deleted_at"`     // 删除时间
	}
)

func newPerfStopRuleReferenceRelationshipModel(conn sqlx.SqlConn, c cache.CacheConf, opts ...cache.Option) *defaultPerfStopRuleReferenceRelationshipModel {
	return &defaultPerfStopRuleReferenceRelationshipModel{
		CachedConn: sqlc.NewConn(conn, c, opts...),
		table:      "`perf_stop_rule_reference_relationship`",
	}
}

func (m *defaultPerfStopRuleReferenceRelationshipModel) Delete(ctx context.Context, session sqlx.Session, id int64) error {
	managerPerfStopRuleReferenceRelationshipIdKey := fmt.Sprintf("%s%v", cacheManagerPerfStopRuleReferenceRelationshipIdPrefix, id)
	_, err := m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("delete from %s where `id` = ?", m.table)
		if session != nil {
			return session.ExecCtx(ctx, query, id)
		}
		return conn.ExecCtx(ctx, query, id)
	}, managerPerfStopRuleReferenceRelationshipIdKey)
	return err
}

func (m *defaultPerfStopRuleReferenceRelationshipModel) LogicDelete(ctx context.Context, session sqlx.Session, id int64) error {
	managerPerfStopRuleReferenceRelationshipIdKey := fmt.Sprintf("%s%v", cacheManagerPerfStopRuleReferenceRelationshipIdPrefix, id)
	_, err := m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set `deleted` = ?, `deleted_at` = ? where `id` = ?", m.table)
		if session != nil {
			return session.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
		}
		return conn.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
	}, managerPerfStopRuleReferenceRelationshipIdKey)
	return err
}

func (m *defaultPerfStopRuleReferenceRelationshipModel) FindOne(ctx context.Context, id int64) (*PerfStopRuleReferenceRelationship, error) {
	managerPerfStopRuleReferenceRelationshipIdKey := fmt.Sprintf("%s%v", cacheManagerPerfStopRuleReferenceRelationshipIdPrefix, id)
	var resp PerfStopRuleReferenceRelationship
	err := m.QueryRowCtx(ctx, &resp, managerPerfStopRuleReferenceRelationshipIdKey, func(ctx context.Context, conn sqlx.SqlConn, v any) error {
		query := fmt.Sprintf("select %s from %s where `id` = ? and `deleted` = ? limit 1", perfStopRuleReferenceRelationshipRows, m.table)
		return conn.QueryRowCtx(ctx, v, query, id, constants.NotDeleted)
	})
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultPerfStopRuleReferenceRelationshipModel) Insert(ctx context.Context, session sqlx.Session, data *PerfStopRuleReferenceRelationship) (sql.Result, error) {
	managerPerfStopRuleReferenceRelationshipIdKey := fmt.Sprintf("%s%v", cacheManagerPerfStopRuleReferenceRelationshipIdPrefix, data.Id)

	return m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?, ?, ?, ?, ?)", m.table, perfStopRuleReferenceRelationshipRowsExpectAutoSet)
		if session != nil {
			return session.ExecCtx(ctx, query, data.ProjectId, data.ReferenceType, data.ReferenceId, data.RuleId, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy)
		}
		return conn.ExecCtx(ctx, query, data.ProjectId, data.ReferenceType, data.ReferenceId, data.RuleId, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy)
	}, managerPerfStopRuleReferenceRelationshipIdKey)
}

func (m *defaultPerfStopRuleReferenceRelationshipModel) Update(ctx context.Context, session sqlx.Session, data *PerfStopRuleReferenceRelationship) (sql.Result, error) {

	managerPerfStopRuleReferenceRelationshipIdKey := fmt.Sprintf("%s%v", cacheManagerPerfStopRuleReferenceRelationshipIdPrefix, data.Id)

	return m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, perfStopRuleReferenceRelationshipRowsWithPlaceHolder)
		if session != nil {
			return session.ExecCtx(ctx, query, data.ProjectId, data.ReferenceType, data.ReferenceId, data.RuleId, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy, data.Id)
		}
		return conn.ExecCtx(ctx, query, data.ProjectId, data.ReferenceType, data.ReferenceId, data.RuleId, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy, data.Id)
	}, managerPerfStopRuleReferenceRelationshipIdKey)
}

func (m *defaultPerfStopRuleReferenceRelationshipModel) formatPrimary(primary any) string {
	return fmt.Sprintf("%s%v", cacheManagerPerfStopRuleReferenceRelationshipIdPrefix, primary)
}

func (m *defaultPerfStopRuleReferenceRelationshipModel) queryPrimary(ctx context.Context, conn sqlx.SqlConn, v, primary any) error {
	query := fmt.Sprintf("select %s from %s where `id` = ? limit 1", perfStopRuleReferenceRelationshipRows, m.table)
	return conn.QueryRowCtx(ctx, v, query, primary)
}

func (m *defaultPerfStopRuleReferenceRelationshipModel) tableName() string {
	return m.table
}
