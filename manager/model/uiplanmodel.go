package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/Masterminds/squirrel"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sqlbuilder"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/types"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/userinfo"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/utils"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
)

var (
	_ UiPlanModel = (*customUiPlanModel)(nil)

	uiPlanInsertFields = stringx.Remove(
		uiPlanFieldNames, "`id`", "`create_time`", "`update_time`", "`deleted_by`", "`created_at`", "`updated_at`",
		"`deleted_at`",
	)
	uiPlanUpdateFields = strings.Join(
		stringx.Remove(uiPlanFieldNames, "`id`", "`created_at`", "`deleted_at`"), "=?,",
	) + "=?"
)

type (
	// UiPlanModel is an interface to be customized, add more methods here,
	// and implement the added methods in customUiPlanModel.
	UiPlanModel interface {
		uiPlanModel
		types.DBModel

		Trans(ctx context.Context, fn func(context context.Context, session sqlx.Session) error) error
		InsertBuilder(data *UiPlan) squirrel.InsertBuilder
		UpdateBuilder(data *UiPlan) squirrel.UpdateBuilder
		SelectBuilder() squirrel.SelectBuilder
		SelectCountBuilder() squirrel.SelectBuilder
		FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error)
		FindNoCacheByQuery(ctx context.Context, selectBuilder squirrel.SelectBuilder) ([]*UiPlan, error)

		InsertTX(ctx context.Context, session sqlx.Session, data *UiPlan) (sql.Result, error)
		UpdateTX(ctx context.Context, session sqlx.Session, data *UiPlan) (sql.Result, error)
		DeleteTX(ctx context.Context, session sqlx.Session, id int64) error

		GenerateSearchUiPlanSqlBuilder(req SearchUiPlanWithCategoryReq) (
			searchUiPlanSelectBuilder, searchUiPlanCountBuilder,
		)
		FindCountUiPlans(ctx context.Context, countBuilder searchUiPlanCountBuilder) (int64, error)
		FindUiPlans(ctx context.Context, selectBuilder searchUiPlanSelectBuilder) ([]*SearchUiPlanItem, error)

		GenerateSearchCaseInUIPlanSqlBuilder(req SearchCaseInUIPlanReq) (
			searchCaseInUIPlanSelectBuilder, searchCaseInUIPlanCountBuilder,
		)
		FindCountCasesInUIPlan(ctx context.Context, countBuilder searchCaseInUIPlanCountBuilder) (int64, error)
		FindCasesInUIPlan(
			ctx context.Context, selectBuilder searchCaseInUIPlanSelectBuilder,
		) ([]*SearchCaseInUIPlanItem, error)

		GenerateSearchCaseNotInUIPlanSqlBuilder(req SearchCaseNotInUIPlanReq) (
			searchCaseNotInUIPlanSelectBuilder, searchCaseNotInUIPlanCountBuilder,
		)
		FindCountCasesNotInUIPlan(ctx context.Context, countBuilder searchCaseNotInUIPlanCountBuilder) (int64, error)
		FindCasesNotInUIPlan(
			ctx context.Context, selectBuilder searchCaseNotInUIPlanSelectBuilder,
		) ([]*SearchCaseNotInUIPlanItem, error)

		RemoveByPlanId(ctx context.Context, session sqlx.Session, projectId, planId string) (sql.Result, error)
		SearchUiPlanByProjectIdConfigId(ctx context.Context, projectId, configId string) ([]*UiPlan, error)

		FindCountByCategoryId(ctx context.Context, cm CategoryModel, cond FindCountByCategoryCondition) (int64, error)
	}

	customUiPlanModel struct {
		*defaultUiPlanModel

		conn sqlx.SqlConn
	}
)

// NewUiPlanModel returns a model for the database table.
func NewUiPlanModel(conn sqlx.SqlConn, c cache.CacheConf) UiPlanModel {
	return &customUiPlanModel{
		defaultUiPlanModel: newUiPlanModel(conn, c),
		conn:               conn,
	}
}

func (m *customUiPlanModel) Table() string {
	return m.table
}

func (m *customUiPlanModel) Fields() []string {
	return uiPlanFieldNames
}

func (m *customUiPlanModel) Trans(
	ctx context.Context, fn func(context context.Context, session sqlx.Session) error,
) error {
	return m.TransactCtx(ctx, fn)
}

func (m *customUiPlanModel) InsertBuilder(data *UiPlan) squirrel.InsertBuilder {
	return squirrel.Insert(m.table).Columns(uiPlanInsertFields...).Values(
		data.ProjectId, data.CategoryId, data.PlanId, data.Name, data.Description, data.Type, data.CronExpression,
		data.PriorityType, data.GitConfigId, data.ExecutionMode, data.DeviceType, data.PlatformType, data.Devices,
		data.Together, data.PackageName, data.AppName, data.CallbackUrl, data.AppDownloadLink, data.AppVersion,
		data.TestLanguage, data.TestLanguageVersion, data.TestFramework, data.TestArgs, data.ExecutionEnvironment,
		data.FailRetry, data.State, data.Deleted, data.MaintainedBy, data.CreatedBy, data.UpdatedBy,
	)
}

func (m *customUiPlanModel) UpdateBuilder(data *UiPlan) squirrel.UpdateBuilder {
	eq := squirrel.Eq{
		"`category_id`":           data.CategoryId,
		"`name`":                  data.Name,
		"`description`":           data.Description,
		"`type`":                  data.Type,
		"`cron_expression`":       data.CronExpression,
		"`priority_type`":         data.PriorityType,
		"`git_config_id`":         data.GitConfigId,
		"`execution_mode`":        data.ExecutionMode,
		"`device_type`":           data.DeviceType,
		"`platform_type`":         data.PlatformType,
		"`devices`":               data.Devices,
		"`together`":              data.Together,
		"`package_name`":          data.PackageName,
		"`callback_url`":          data.CallbackUrl,
		"`app_download_link`":     data.AppDownloadLink,
		"`app_version`":           data.AppVersion,
		"`app_name`":              data.AppName,
		"`test_language`":         data.TestLanguage,
		"`test_language_version`": data.TestLanguageVersion,
		"`test_framework`":        data.TestFramework,
		"`test_args`":             data.TestArgs,
		"`execution_environment`": data.ExecutionEnvironment,
		"`fail_retry`":            data.FailRetry,
		"`state`":                 data.State,
		"`deleted`":               data.Deleted,
		"`maintained_by`":         data.MaintainedBy,
		"`updated_by`":            data.UpdatedBy,
		"`deleted_by`":            data.DeletedBy,
		"`deleted_at`":            data.DeletedAt,
	}
	return squirrel.Update(m.table).SetMap(eq).Where("`id` = ?", data.Id)
}

func (m *customUiPlanModel) SelectBuilder() squirrel.SelectBuilder {
	return squirrel.Select(uiPlanFieldNames...).Where("`deleted` = ?", constants.NotDeleted).From(m.table)
}

func (m *customUiPlanModel) SelectCountBuilder() squirrel.SelectBuilder {
	return squirrel.Select("count(*)").Where("`deleted` = ?", constants.NotDeleted).From(m.table)
}

func (m *customUiPlanModel) FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error) {
	query, values, err := countBuilder.ToSql()
	if err != nil {
		return 0, err
	}

	var resp int64
	err = m.QueryRowNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return 0, err
	}
}

func (m *customUiPlanModel) FindNoCacheByQuery(ctx context.Context, selectBuilder squirrel.SelectBuilder) (
	[]*UiPlan, error,
) {
	query, values, err := selectBuilder.ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*UiPlan
	err = m.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

func (m *customUiPlanModel) InsertTX(ctx context.Context, session sqlx.Session, data *UiPlan) (sql.Result, error) {
	managerUiPlanIdKey := fmt.Sprintf("%s%v", cacheManagerUiPlanIdPrefix, data.Id)
	managerUiPlanProjectIdPlanIdKey := fmt.Sprintf(
		"%s%v:%v", cacheManagerUiPlanProjectIdPlanIdPrefix, data.ProjectId, data.PlanId,
	)

	return m.ExecCtx(
		ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
			query, values, err := m.InsertBuilder(data).ToSql()
			if err != nil {
				return nil, err
			}
			if session != nil {
				return conn.ExecCtx(ctx, query, values...)
			}
			return conn.ExecCtx(ctx, query, values...)
		}, managerUiPlanIdKey, managerUiPlanProjectIdPlanIdKey,
	)
}

func (m *customUiPlanModel) UpdateTX(ctx context.Context, session sqlx.Session, data *UiPlan) (sql.Result, error) {
	managerUiPlanIdKey := fmt.Sprintf("%s%v", cacheManagerUiPlanIdPrefix, data.Id)
	managerUiPlanProjectIdPlanIdKey := fmt.Sprintf(
		"%s%v:%v", cacheManagerUiPlanProjectIdPlanIdPrefix, data.ProjectId, data.PlanId,
	)

	return m.ExecCtx(
		ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
			query, values, err := m.UpdateBuilder(data).ToSql()
			if err != nil {
				return nil, err
			}
			if session != nil {
				return session.ExecCtx(ctx, query, values...)
			}
			return conn.ExecCtx(ctx, query, values...)
		}, managerUiPlanIdKey, managerUiPlanProjectIdPlanIdKey,
	)
}

func (m *customUiPlanModel) DeleteTX(ctx context.Context, session sqlx.Session, id int64) error {
	data, err := m.FindOne(ctx, id)
	if err != nil {
		return err
	}

	managerUiPlanIdKey := fmt.Sprintf("%s%v", cacheManagerUiPlanIdPrefix, id)
	managerUiPlanProjectIdPlanIdKey := fmt.Sprintf(
		"%s%v:%v", cacheManagerUiPlanProjectIdPlanIdPrefix, data.ProjectId, data.PlanId,
	)

	_, err = m.ExecCtx(
		ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
			query := fmt.Sprintf("delete from %s where `id` = ?", m.table)
			if session != nil {
				return session.ExecCtx(ctx, query, id)
			}
			return conn.ExecCtx(ctx, query, id)
		}, managerUiPlanIdKey, managerUiPlanProjectIdPlanIdKey,
	)

	return err
}

type SearchUiPlanItem struct {
	UiPlan
}

type SearchUiPlanWithCategoryReq struct {
	SearchUiPlanReq

	CategoryId string `json:"category_id"`
	DrillDown  bool   `json:"drill_down"`

	CategoryModel CategoryModel
}

type searchUiPlanSelectBuilder struct {
	squirrel.SelectBuilder
}

type searchUiPlanCountBuilder struct {
	squirrel.SelectBuilder
}

func (m *customUiPlanModel) GenerateSearchUiPlanSqlBuilder(req SearchUiPlanWithCategoryReq) (
	searchUiPlanSelectBuilder, searchUiPlanCountBuilder,
) {
	/*
		SQL:
		SELECT t.*
		FROM ui_plan AS t
			LEFT JOIN (
				SELECT t1.*
			    FROM category AS t1
					INNER JOIN category_tree AS t2 ON
						t1.`project_id` = t2.`project_id` AND
						t1.`category_id` = t2.`descendant`
			    WHERE t1.`project_id` = ?
					AND t1.`type` = ?
					AND t2.`ancestor` = ?
					AND t1.`deleted` = ?
					AND t2.`deleted` = ?
			    ORDER BY t2.`depth` DESC, t2.`index` ASC, t1.`created_at` ASC
			) AS t1 ON t.`category_id` = t1.`category_id`
		WHERE t.`project_id` = ?
			AND t.`deleted` = ?
			AND t1.`category_id` IS NOT NULL;
	*/

	var (
		aliasT  = "t"
		aliasT1 = "t1"
	)

	sb := squirrel.Select(utils.AddTableNameToFields(aliasT, m.Fields())...).
		From(m.table+" AS "+aliasT).
		Where(
			fmt.Sprintf("%s.`project_id` = ? AND %s.`deleted` = ?", aliasT, aliasT),
			req.ProjectId, constants.NotDeleted,
		)
	scb := squirrel.Select("COUNT(*)").
		From(m.table+" AS "+aliasT).
		Where(
			fmt.Sprintf("%s.`project_id` = ? AND %s.`deleted` = ?", aliasT, aliasT),
			req.ProjectId, constants.NotDeleted,
		)

	if req.DrillDown {
		sub := req.CategoryModel.FindDescendantCategoriesSqlBuilder(
			GetCategoryTreeCondition{
				ProjectId:     req.ProjectId,
				Type:          common.ConstCategoryTreeTypeUiPlan,
				CategoryId:    req.CategoryId,
				OnlyDirectory: true,
			},
		)

		sb = sb.JoinClause(
			sub.Prefix("LEFT JOIN (").Suffix(
				fmt.Sprintf(
					") AS %s ON %s.`category_id` = %s.`category_id`", aliasT1, aliasT, aliasT1,
				),
			),
		).Where(fmt.Sprintf("%s.`category_id` IS NOT NULL", aliasT1))
		scb = scb.JoinClause(
			sub.Prefix("LEFT JOIN (").Suffix(
				fmt.Sprintf(
					") AS %s ON %s.`category_id` = %s.`category_id`", aliasT1, aliasT, aliasT1,
				),
			),
		).Where(fmt.Sprintf("%s.`category_id` IS NOT NULL", aliasT1))
	} else {
		sb = sb.Where(fmt.Sprintf("%s.`category_id` = ?", aliasT), req.CategoryId)
		scb = scb.Where(fmt.Sprintf("%s.`category_id` = ?", aliasT), req.CategoryId)
	}

	sb = sqlbuilder.SearchOptionsWithAlias(
		sb, aliasT,
		sqlbuilder.WithCondition(m, req.Condition),
		sqlbuilder.WithPagination(m, req.Pagination),
		sqlbuilder.WithSort(m, req.Sort),
	)
	scb = sqlbuilder.SearchOptionsWithAlias(scb, aliasT, sqlbuilder.WithCondition(m, req.Condition))

	return searchUiPlanSelectBuilder{SelectBuilder: sb}, searchUiPlanCountBuilder{SelectBuilder: scb}
}

func (m *customUiPlanModel) FindCountUiPlans(ctx context.Context, countBuilder searchUiPlanCountBuilder) (
	int64, error,
) {
	return m.FindCount(ctx, countBuilder.SelectBuilder)
}

func (m *customUiPlanModel) FindUiPlans(
	ctx context.Context, selectBuilder searchUiPlanSelectBuilder,
) ([]*SearchUiPlanItem, error) {
	var resp []*SearchUiPlanItem

	err := utils.FindRows(
		ctx, m.conn, selectBuilder.SelectBuilder, func() any {
			return &resp
		},
	)

	return resp, err
}

type SearchCaseInUIPlanItem struct {
	ProjectId   string         `db:"project_id"`    // 项目ID
	GitConfigId string         `db:"git_config_id"` // Git配置ID
	Path        string         `db:"path"`          // 节点路径（相对于根路径）
	ParentPath  sql.NullString `db:"parent_path"`   // 父节点路径
	Name        string         `db:"name"`          // 节点名称
	Alias       sql.NullString `db:"alias"`         // 节点别名
	Type        string         `db:"type"`          // 节点类型（目录、文件、包、模块、类、函数）
	Tags        sql.NullString `db:"tags"`          // 标签
	CreatedBy   string         `db:"created_by"`    // 创建者的用户ID
	UpdatedBy   string         `db:"updated_by"`    // 最近一次更新者的用户ID
	CreatedAt   time.Time      `db:"created_at"`    // 创建时间
	UpdatedAt   time.Time      `db:"updated_at"`    // 更新时间
}

type searchCaseInUIPlanSelectBuilder struct {
	squirrel.SelectBuilder
}

type searchCaseInUIPlanCountBuilder struct {
	squirrel.SelectBuilder
}

func (m *customUiPlanModel) GenerateSearchCaseInUIPlanSqlBuilder(req SearchCaseInUIPlanReq) (
	searchCaseInUIPlanSelectBuilder, searchCaseInUIPlanCountBuilder,
) {
	/*
		SQL:
		SELECT t1.`project_id`,
		       t1.`git_config_id`,
		       t2.`path`,
		       t2.`parent_path`,
		       t2.`name`,
		       t2.`alias`,
		       t2.`type`,
		       t2.`tags`,
		       t2.`created_by`,
		       t2.`updated_by`,
		       t2.`created_at`,
		       t2.`updated_at`
		FROM `ui_plan` AS t1
		         INNER JOIN `git_project_tree` AS t2
		                    ON t1.`project_id` = t2.`project_id` AND
		                       t1.`git_config_id` = t2.`git_config_id` AND
		                       t1.`deleted` = t2.`deleted`
		         INNER JOIN `ui_plan_reference_relationship` AS t3
		                    ON t1.`project_id` = t3.`project_id` AND
		                       t1.`plan_id` = t3.`plan_id` AND
		                       t1.`git_config_id` = t3.`git_config_id` AND
		                       t1.`deleted` = t3.`deleted` AND
		                       t2.`path` = t3.`path`
		WHERE t1.`project_id` = ?
		  AND t1.`plan_id` = ?
		  AND t2.`path` LIKE ?
		  AND t1.`deleted` = ?
	*/

	fields := []string{
		"t1.`project_id`",
		"t1.`git_config_id`",
		"t2.`path`",
		"t2.`parent_path`",
		"t2.`name`",
		"t2.`alias`",
		"t2.`type`",
		"t2.`tags`",
		"t2.`created_by`",
		"t2.`updated_by`",
		"t2.`created_at`",
		"t2.`updated_at`",
	}

	gm := req.GitProjectTreeModel
	// um := req.UIPlanReferenceRelationshipModel

	sb1 := squirrel.Select(fields...).
		From(m.table+" AS t1").
		InnerJoin(gitProjectTreeTableName+" AS t2 ON t1.`project_id` = t2.`project_id` AND t1.`git_config_id` = t2.`git_config_id` AND t1.`deleted` = t2.`deleted`").
		InnerJoin(uiPlanReferenceRelationshipTableName+" AS t3 ON t1.`project_id` = t3.`project_id` AND t1.`plan_id` = t3.`plan_id` AND t1.`git_config_id` = t3.`git_config_id` AND t1.`deleted` = t3.`deleted` AND t2.`path` = t3.`path`").
		Where(
			"t1.`project_id` = ? AND t1.`plan_id` = ? AND t2.`path` LIKE ? AND t1.`deleted` = ?",
			req.ProjectID, req.PlanID, req.Path+"%", constants.NotDeleted,
		)
	sb1 = sqlbuilder.SearchOptionsWithAlias(sb1, "t2", sqlbuilder.WithCondition(gm, req.Condition))
	sb := sqlbuilder.SearchOptionsWithAlias(
		sb1, "t2", sqlbuilder.WithPagination(gm, req.Pagination), sqlbuilder.WithSort(gm, req.Sort),
	)
	scb := squirrel.Select("COUNT(*)").FromSelect(sb1, "tmp")

	return searchCaseInUIPlanSelectBuilder{SelectBuilder: sb}, searchCaseInUIPlanCountBuilder{SelectBuilder: scb}
}

func (m *customUiPlanModel) FindCountCasesInUIPlan(
	ctx context.Context, countBuilder searchCaseInUIPlanCountBuilder,
) (int64, error) {
	return m.FindCount(ctx, countBuilder.SelectBuilder)
}

func (m *customUiPlanModel) FindCasesInUIPlan(
	ctx context.Context, selectBuilder searchCaseInUIPlanSelectBuilder,
) ([]*SearchCaseInUIPlanItem, error) {
	var resp []*SearchCaseInUIPlanItem

	err := utils.FindRows(
		ctx, m.conn, selectBuilder.SelectBuilder, func() any {
			return &resp
		},
	)

	return resp, err
}

type SearchCaseNotInUIPlanItem struct {
	ProjectId   string         `db:"project_id"`    // 项目ID
	GitConfigId string         `db:"git_config_id"` // Git配置ID
	Path        string         `db:"path"`          // 节点路径（相对于根路径）
	ParentPath  sql.NullString `db:"parent_path"`   // 父节点路径
	Name        string         `db:"name"`          // 节点名称
	Alias       sql.NullString `db:"alias"`         // 节点别名
	Type        string         `db:"type"`          // 节点类型（目录、文件、包、模块、类、函数）
	Tags        sql.NullString `db:"tags"`          // 标签
	CreatedBy   string         `db:"created_by"`    // 创建者的用户ID
	UpdatedBy   string         `db:"updated_by"`    // 最近一次更新者的用户ID
	CreatedAt   time.Time      `db:"created_at"`    // 创建时间
	UpdatedAt   time.Time      `db:"updated_at"`    // 更新时间
}

type searchCaseNotInUIPlanSelectBuilder struct {
	squirrel.SelectBuilder
}

type searchCaseNotInUIPlanCountBuilder struct {
	squirrel.SelectBuilder
}

func (m *customUiPlanModel) GenerateSearchCaseNotInUIPlanSqlBuilder(req SearchCaseNotInUIPlanReq) (
	searchCaseNotInUIPlanSelectBuilder, searchCaseNotInUIPlanCountBuilder,
) {
	/*
		SQL:
		SELECT t1.`project_id`,
		       t1.`git_config_id`,
		       t1.`path`,
		       t1.`parent_path`,
		       t1.`name`,
		       t1.`alias`,
		       t1.`type`,
		       t1.`tags`,
		       t1.`created_by`,
		       t1.`updated_by`,
		       t1.`created_at`,
		       t1.`updated_at`
		FROM (
		    SELECT t1.`project_id`,
		           t1.`git_config_id`,
		           t2.`path`,
		           t2.`parent_path`,
		           t2.`name`,
		           t2.`alias`,
		           t2.`type`,
		           t2.`tags`,
		           t2.`created_by`,
		           t2.`updated_by`,
		           t2.`created_at`,
		           t2.`updated_at`
		    FROM `ui_plan` AS t1
		        INNER JOIN `git_project_tree` AS t2
		            ON t1.`project_id` = t2.`project_id` AND
		               t1.`git_config_id` = t2.`git_config_id` AND
		               t1.`deleted` = t2.`deleted`
		    WHERE t1.`project_id` = ?
		      AND t1.`plan_id` = ?
		      AND t1.`deleted` = ?
		      AND t2.`path` LIKE ?
		      AND t2.`type` = ?
		) AS t1
		LEFT JOIN (
		    SELECT t1.`project_id`,
		           t1.`git_config_id`,
		           t2.`path`,
		           t2.`parent_path`,
		           t2.`name`,
		           t2.`alias`,
		           t2.`type`,
		           t2.`tags`,
		           t2.`created_by`,
		           t2.`updated_by`,
		           t2.`created_at`,
		           t2.`updated_at`
		    FROM `ui_plan` AS t1
		        INNER JOIN `git_project_tree` AS t2
		            ON t1.`project_id` = t2.`project_id` AND
		               t1.`git_config_id` = t2.`git_config_id` AND
		               t1.`deleted` = t2.`deleted`
		        INNER JOIN `ui_plan_reference_relationship` AS t3
		            ON t1.`project_id` = t3.`project_id` AND
		               t1.`plan_id` = t3.`plan_id` AND
		               t1.`git_config_id` = t3.`git_config_id` AND
		               t1.`deleted` = t3.`deleted` AND
		               t2.`path` = t3.`path`
		    WHERE t1.`project_id` = ?
		      AND t1.`plan_id` = ?
		      AND t1.`deleted` = ?
		      AND t2.`path` LIKE ?
		) AS t2 ON t1.`project_id` = t2.`project_id` AND
		           t1.`git_config_id` = t2.`git_config_id` AND
		           t1.`path` = t2.`path`
		WHERE t2.`path` IS NULL
	*/

	gm := req.GitProjectTreeModel
	alias := "t1"

	fields1 := []string{
		alias + ".`project_id`",
		alias + ".`git_config_id`",
		alias + ".`path`",
		alias + ".`parent_path`",
		alias + ".`name`",
		alias + ".`alias`",
		alias + ".`type`",
		alias + ".`tags`",
		alias + ".`created_by`",
		alias + ".`updated_by`",
		alias + ".`created_at`",
		alias + ".`updated_at`",
	}
	fields2 := []string{
		"t1.`project_id`",
		"t1.`git_config_id`",
		"t2.`path`",
		"t2.`parent_path`",
		"t2.`name`",
		"t2.`alias`",
		"t2.`type`",
		"t2.`tags`",
		"t2.`created_by`",
		"t2.`updated_by`",
		"t2.`created_at`",
		"t2.`updated_at`",
	}

	sb1 := squirrel.Select(fields2...).
		From(m.table+" AS t1").
		InnerJoin(gitProjectTreeTableName+" AS t2 ON t1.`project_id` = t2.`project_id` AND t1.`git_config_id` = t2.`git_config_id` AND t1.`deleted` = t2.`deleted`").
		Where(
			"t1.`project_id` = ? AND t1.`plan_id` = ? AND t1.`deleted` = ? AND t2.`path` LIKE ? AND t2.`type` = ?",
			req.ProjectID, req.PlanID, constants.NotDeleted, req.Path+"%", common.ConstNodeTypeFunction,
		)
	sb2, _ := m.GenerateSearchCaseInUIPlanSqlBuilder(
		SearchCaseInUIPlanReq{
			GitProjectTreeModel: req.GitProjectTreeModel,
			ProjectID:           req.ProjectID,
			PlanID:              req.PlanID,
			Path:                req.Path,
		},
	)

	sb := squirrel.Select(fields1...).
		FromSelect(sb1, alias).
		JoinClause(sb2.Prefix("LEFT JOIN (").Suffix(") AS t2 ON t1.`project_id` = t2.`project_id` AND t1.`git_config_id` = t2.`git_config_id` AND t1.`path` = t2.`path`")).
		Where("t2.`path` IS NULL")
	scb := squirrel.Select("COUNT(*)").
		FromSelect(sb1, alias).
		JoinClause(sb2.Prefix("LEFT JOIN (").Suffix(") AS t2 ON t1.`project_id` = t2.`project_id` AND t1.`git_config_id` = t2.`git_config_id` AND t1.`path` = t2.`path`")).
		Where("t2.`path` IS NULL")

	sb = sqlbuilder.SearchOptionsWithAlias(
		sb, alias, sqlbuilder.WithCondition(gm, req.Condition), sqlbuilder.WithPagination(gm, req.Pagination),
		sqlbuilder.WithSort(gm, req.Sort),
	)
	scb = sqlbuilder.SearchOptionsWithAlias(scb, alias, sqlbuilder.WithCondition(gm, req.Condition))

	return searchCaseNotInUIPlanSelectBuilder{SelectBuilder: sb}, searchCaseNotInUIPlanCountBuilder{SelectBuilder: scb}
}

func (m *customUiPlanModel) FindCountCasesNotInUIPlan(
	ctx context.Context, countBuilder searchCaseNotInUIPlanCountBuilder,
) (int64, error) {
	return m.FindCount(ctx, countBuilder.SelectBuilder)
}

func (m *customUiPlanModel) FindCasesNotInUIPlan(
	ctx context.Context, selectBuilder searchCaseNotInUIPlanSelectBuilder,
) ([]*SearchCaseNotInUIPlanItem, error) {
	var resp []*SearchCaseNotInUIPlanItem

	err := utils.FindRows(
		ctx, m.conn, selectBuilder.SelectBuilder, func() any {
			return &resp
		},
	)

	return resp, err
}

func (m *customUiPlanModel) RemoveByPlanId(
	ctx context.Context, session sqlx.Session, projectId, planId string,
) (sql.Result, error) {
	keys := m.getKeysByPlanId(ctx, projectId, planId)

	return m.ExecCtx(
		ctx, func(ctx context.Context, conn sqlx.SqlConn) (sql.Result, error) {
			/*
				SQL:
				UPDATE `ui_plan`
				SET `deleted` = ?, `deleted_by` = ?, `deleted_at` = ?
				WHERE `project_id` = ?
				  AND `plan_id` = ?
			*/
			stmt, values, err := squirrel.Update(m.table).
				SetMap(
					squirrel.Eq{
						"`deleted`":    constants.HasDeleted,
						"`deleted_by`": userinfo.FromContext(ctx).Account,
						"`deleted_at`": time.Now(),
					},
				).
				Where("`project_id` = ? AND `plan_id` = ?", projectId, planId).
				ToSql()
			if err != nil {
				return nil, err
			}

			if session != nil {
				return session.ExecCtx(ctx, stmt, values...)
			}
			return conn.ExecCtx(ctx, stmt, values...)
		}, keys...,
	)
}

func (m *customUiPlanModel) SearchUiPlanByProjectIdConfigId(ctx context.Context, projectId, configId string) (
	[]*UiPlan, error,
) {
	return m.FindNoCacheByQuery(
		ctx, m.SelectBuilder().Where(
			"`project_id` = ? AND `git_config_id` = ? AND `deleted` = ?", projectId, configId, constants.NotDeleted,
		),
	)
}

func (m *customUiPlanModel) getKeysByPlanId(ctx context.Context, projectId, planId string) []string {
	uiPlan, err := m.FindOneByProjectIdPlanId(ctx, projectId, planId)
	if err != nil {
		return make([]string, 0)
	}

	return []string{
		fmt.Sprintf("%s%v", cacheManagerUiPlanIdPrefix, uiPlan.Id),
		fmt.Sprintf("%s%v:%v", cacheManagerUiPlanProjectIdPlanIdPrefix, uiPlan.ProjectId, uiPlan.PlanId),
	}
}

func (m *customUiPlanModel) FindCountByCategoryId(
	ctx context.Context, cm CategoryModel, cond FindCountByCategoryCondition,
) (int64, error) {
	var (
		aliasT  = "t"
		aliasT1 = "t1"
	)

	sb := squirrel.Select("COUNT(*)").
		From(m.table+" AS "+aliasT).
		Where(
			fmt.Sprintf("%s.`project_id` = ? AND %s.`deleted` = ?", aliasT, aliasT),
			cond.ProjectId, constants.NotDeleted,
		)

	if cond.DrillDown {
		sub := cm.FindDescendantCategoriesSqlBuilder(
			GetCategoryTreeCondition{
				ProjectId:     cond.ProjectId,
				Type:          common.ConstCategoryTreeTypeUiPlan,
				CategoryId:    cond.CategoryId,
				OnlyDirectory: true,
			},
		)

		sb = sb.JoinClause(
			sub.Prefix("LEFT JOIN (").Suffix(
				fmt.Sprintf(
					") AS %s ON %s.`category_id` = %s.`category_id`", aliasT1, aliasT, aliasT1,
				),
			),
		).Where(fmt.Sprintf("%s.`category_id` IS NOT NULL", aliasT1))
	} else {
		sb = sb.Where(fmt.Sprintf("%s.`category_id` = ?", aliasT), cond.CategoryId)
	}

	return m.FindCount(ctx, sb)
}
