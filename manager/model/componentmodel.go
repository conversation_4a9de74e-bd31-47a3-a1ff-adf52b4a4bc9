package model

import (
	"context"
	"database/sql"
	"fmt"
	"time"

	"github.com/Masterminds/squirrel"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/types"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/userinfo"
)

var (
	_ ComponentModel = (*customComponentModel)(nil)

	componentInsertFields = stringx.Remove(
		componentFieldNames, "`id`", "`create_time`", "`update_time`", "`deleted_by`", "`created_at`", "`updated_at`",
		"`deleted_at`",
	)
)

type (
	// ComponentModel is an interface to be customized, add more methods here,
	// and implement the added methods in customComponentModel.
	ComponentModel interface {
		componentModel
		types.DBModel

		Trans(ctx context.Context, fn func(context context.Context, session sqlx.Session) error) error
		InsertBuilder(data *Component) squirrel.InsertBuilder
		UpdateBuilder(data *Component) squirrel.UpdateBuilder
		SelectBuilder() squirrel.SelectBuilder
		SelectCountBuilder() squirrel.SelectBuilder
		FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error)
		FindNoCacheByQuery(ctx context.Context, builder squirrel.SelectBuilder) ([]*Component, error)

		InsertTX(ctx context.Context, session sqlx.Session, data *Component) (sql.Result, error)
		RemoveByParentId(ctx context.Context, session sqlx.Session, projectId, parentId, parentType string) (
			sql.Result, error,
		)
	}

	customComponentModel struct {
		*defaultComponentModel
	}
)

// NewComponentModel returns a model for the database table.
func NewComponentModel(conn sqlx.SqlConn, c cache.CacheConf) ComponentModel {
	return &customComponentModel{
		defaultComponentModel: newComponentModel(conn, c),
	}
}

func (m *customComponentModel) Table() string {
	return m.table
}

func (m *customComponentModel) Fields() []string {
	return componentFieldNames
}

func (m *customComponentModel) Trans(
	ctx context.Context, fn func(context context.Context, session sqlx.Session) error,
) error {
	return m.TransactCtx(ctx, fn)
}

func (m *customComponentModel) InsertBuilder(data *Component) squirrel.InsertBuilder {
	return squirrel.Insert(m.table).Columns(componentInsertFields...).Values(
		data.ProjectId, data.ParentId, data.ParentType, data.ParentVersion, data.ComponentId, data.ComponentType,
		data.Name, data.Description, data.Data, data.Deleted, data.CreatedBy, data.UpdatedBy,
	)
}

func (m *customComponentModel) UpdateBuilder(data *Component) squirrel.UpdateBuilder {
	eq := squirrel.Eq{
		"`name`":        data.Name,
		"`description`": data.Description,
		"`data`":        data.Data,
		"`deleted`":     data.Deleted,
		"`updated_by`":  data.UpdatedBy,
		"`deleted_by`":  data.DeletedBy,
		"`deleted_at`":  data.DeletedAt,
	}
	return squirrel.Update(m.table).SetMap(eq).Where("`id` = ?", data.Id)
}

func (m *customComponentModel) SelectBuilder() squirrel.SelectBuilder {
	return squirrel.Select(componentFieldNames...).Where("`deleted` = ?", constants.NotDeleted).From(m.table)
}

func (m *customComponentModel) SelectCountBuilder() squirrel.SelectBuilder {
	return squirrel.Select("count(*)").Where("`deleted` = ?", constants.NotDeleted).From(m.table)
}

func (m *customComponentModel) FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error) {
	query, values, err := countBuilder.ToSql()
	if err != nil {
		return 0, err
	}

	var resp int64
	err = m.QueryRowNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return 0, err
	}
}

func (m *customComponentModel) FindNoCacheByQuery(ctx context.Context, builder squirrel.SelectBuilder) (
	[]*Component, error,
) {
	query, values, err := builder.ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*Component
	err = m.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

func (m *customComponentModel) InsertTX(ctx context.Context, session sqlx.Session, data *Component) (
	sql.Result, error,
) {
	keys := make([]string, 0, 2)
	if data.Id > 0 {
		keys = append(keys, fmt.Sprintf("%s%v", cacheManagerComponentIdPrefix, data.Id))
	}
	if data.ProjectId != "" && data.ParentId != "" && data.ParentType != "" && data.ParentVersion != "" {
		keys = append(
			keys, fmt.Sprintf(
				"%s%v:%v:%v:%v:%v", cacheManagerComponentProjectIdParentIdParentTypeParentVersionComponentIdPrefix,
				data.ProjectId, data.ParentId, data.ParentType, data.ParentVersion, data.ComponentId,
			),
		)
	}

	return m.ExecCtx(
		ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
			query, values, err := m.InsertBuilder(data).ToSql()
			if err != nil {
				return nil, err
			}
			if session != nil {
				return session.ExecCtx(ctx, query, values...)
			}
			return conn.ExecCtx(ctx, query, values...)
		}, keys...,
	)
}

func (m *customComponentModel) RemoveByParentId(
	ctx context.Context, session sqlx.Session, projectId, parentId, parentType string,
) (sql.Result, error) {
	keys := m.getKeysByParentId(ctx, projectId, parentId, parentType)

	return m.ExecCtx(
		ctx, func(ctx context.Context, conn sqlx.SqlConn) (sql.Result, error) {
			/*
					SQL:
					UPDATE `component`
					SET `deleted` = 1, `deleted_by` = ?, `deleted_at` = ?
					WHERE `project_id` = ?
					  AND `parent_id` = ?
				      AND `parent_type` = ?;
			*/
			stmt, values, err := squirrel.Update(m.table).
				SetMap(
					squirrel.Eq{
						"`deleted`":    constants.HasDeleted,
						"`deleted_by`": userinfo.FromContext(ctx).Account,
						"`deleted_at`": time.Now(),
					},
				).
				Where("`project_id` = ? AND `parent_id` = ? AND `parent_type` = ?", projectId, parentId, parentType).
				ToSql()
			if err != nil {
				return nil, err
			}

			if session != nil {
				return session.ExecCtx(ctx, stmt, values...)
			}
			return conn.ExecCtx(ctx, stmt, values...)
		}, keys...,
	)
}

func (m *customComponentModel) getKeysByParentId(ctx context.Context, projectId, parentId, parentType string) []string {
	sb := m.SelectBuilder().Where(
		"`project_id` = ? AND `parent_id` = ? AND `parent_type` = ? AND `deleted` = ?", projectId, parentId, parentType,
		constants.NotDeleted,
	)
	cs, err := m.FindNoCacheByQuery(ctx, sb)
	if err != nil {
		return make([]string, 0)
	}

	keys := make([]string, 0, len(cs)*2)
	for _, c := range cs {
		keys = append(
			keys, fmt.Sprintf("%s%v", cacheManagerComponentIdPrefix, c.Id), fmt.Sprintf(
				"%s%v:%v:%v:%v:%v", cacheManagerComponentProjectIdParentIdParentTypeParentVersionComponentIdPrefix,
				c.ProjectId, c.ParentId, c.ParentType, c.ParentVersion, c.ComponentId,
			),
		)
	}

	return keys
}
