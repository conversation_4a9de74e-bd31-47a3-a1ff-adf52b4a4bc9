package model

import (
	"context"
	"database/sql"
	"fmt"
	"time"

	"github.com/Masterminds/squirrel"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sqlbuilder"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/types"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/utils"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
)

var (
	_ PerfCaseV2Model = (*customPerfCaseV2Model)(nil)

	perfCaseV2InsertFields = stringx.Remove(
		perfCaseV2FieldNames, "`id`", "`create_time`", "`update_time`", "`deleted_by`", "`created_at`", "`updated_at`",
		"`deleted_at`",
	)
)

type (
	// PerfCaseV2Model is an interface to be customized, add more methods here,
	// and implement the added methods in customPerfCaseV2Model.
	PerfCaseV2Model interface {
		perfCaseV2Model
		types.DBModel

		Trans(ctx context.Context, fn func(context context.Context, session sqlx.Session) error) error
		InsertBuilder(data *PerfCaseV2) squirrel.InsertBuilder
		UpdateBuilder(data *PerfCaseV2) squirrel.UpdateBuilder
		SelectBuilder() squirrel.SelectBuilder
		SelectCountBuilder() squirrel.SelectBuilder
		FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error)
		FindNoCacheByQuery(ctx context.Context, selectBuilder squirrel.SelectBuilder) ([]*PerfCaseV2, error)

		FindCountByCategoryId(ctx context.Context, cm CategoryModel, cond FindCountByCategoryCondition) (int64, error)

		GenerateSearchPerfCaseV2SqlBuilder(req SearchPerfCaseV2Req) (
			searchPerfCaseV2SelectBuilder, searchPerfCaseV2CountBuilder,
		)
		FindCountPerfCasesV2(ctx context.Context, countBuilder searchPerfCaseV2CountBuilder) (int64, error)
		FindPerfCasesV2(ctx context.Context, selectBuilder searchPerfCaseV2SelectBuilder) (
			[]*SearchPerfCaseV2Item, error,
		)

		FindAllByPlanID(ctx context.Context, projectID, planID string) ([]*PerfCaseV2, error)
	}

	customPerfCaseV2Model struct {
		*defaultPerfCaseV2Model

		conn sqlx.SqlConn
	}
)

// NewPerfCaseV2Model returns a model for the database table.
func NewPerfCaseV2Model(conn sqlx.SqlConn, c cache.CacheConf, opts ...cache.Option) PerfCaseV2Model {
	return &customPerfCaseV2Model{
		defaultPerfCaseV2Model: newPerfCaseV2Model(conn, c, opts...),
		conn:                   conn,
	}
}

func (m *customPerfCaseV2Model) Table() string {
	return m.table
}

func (m *customPerfCaseV2Model) Fields() []string {
	return perfCaseV2FieldNames
}

func (m *customPerfCaseV2Model) Trans(
	ctx context.Context, fn func(context context.Context, session sqlx.Session) error,
) error {
	return m.TransactCtx(ctx, fn)
}

func (m *customPerfCaseV2Model) InsertBuilder(data *PerfCaseV2) squirrel.InsertBuilder {
	return squirrel.Insert(m.table).Columns(perfCaseV2InsertFields...).Values(
		data.ProjectId, data.CategoryId, data.CaseId, data.Name, data.Description, data.Tags, data.Protocol,
		data.RateLimits, data.SetupSteps, data.SerialSteps, data.ParallelSteps, data.TeardownSteps, data.NumberOfSteps,
		data.State, data.Deleted, data.MaintainedBy, data.CreatedBy, data.UpdatedBy,
	)
}

func (m *customPerfCaseV2Model) UpdateBuilder(data *PerfCaseV2) squirrel.UpdateBuilder {
	eq := squirrel.Eq{
		"`name`":            data.Name,
		"`description`":     data.Description,
		"`tags`":            data.Tags,
		"`protocol`":        data.Protocol,
		"`rate_limits`":     data.RateLimits,
		"`setup_steps`":     data.SetupSteps,
		"`serial_steps`":    data.SerialSteps,
		"`parallel_steps`":  data.ParallelSteps,
		"`teardown_steps`":  data.TeardownSteps,
		"`number_of_steps`": data.NumberOfSteps,
		"`state`":           data.State,
		"`deleted`":         data.Deleted,
		"`maintained_by`":   data.MaintainedBy,
		"`updated_by`":      data.UpdatedBy,
		"`deleted_by`":      data.DeletedBy,
		"`deleted_at`":      data.DeletedAt,
	}
	return squirrel.Update(m.table).SetMap(eq).Where("`id` = ?", data.Id)
}

func (m *customPerfCaseV2Model) SelectBuilder() squirrel.SelectBuilder {
	return squirrel.Select(perfCaseV2FieldNames...).Where("`deleted` = ?", constants.NotDeleted).From(m.table)
}

func (m *customPerfCaseV2Model) SelectCountBuilder() squirrel.SelectBuilder {
	return squirrel.Select("count(*)").Where("`deleted` = ?", constants.NotDeleted).From(m.table)
}

func (m *customPerfCaseV2Model) FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error) {
	query, values, err := countBuilder.ToSql()
	if err != nil {
		return 0, err
	}

	var resp int64
	err = m.QueryRowNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return 0, err
	}
}

func (m *customPerfCaseV2Model) FindNoCacheByQuery(
	ctx context.Context, selectBuilder squirrel.SelectBuilder,
) ([]*PerfCaseV2, error) {
	query, values, err := selectBuilder.ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*PerfCaseV2
	err = m.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

func (m *customPerfCaseV2Model) FindCountByCategoryId(
	ctx context.Context, cm CategoryModel, cond FindCountByCategoryCondition,
) (int64, error) {
	var (
		aliasT  = "t"
		aliasT1 = "t1"
	)

	sb := squirrel.Select("COUNT(*)").
		From(m.table+" AS "+aliasT).
		Where(
			fmt.Sprintf("%s.`project_id` = ? AND %s.`deleted` = ?", aliasT, aliasT),
			cond.ProjectId, constants.NotDeleted,
		)

	if cond.DrillDown {
		sub := cm.FindDescendantCategoriesSqlBuilder(
			GetCategoryTreeCondition{
				ProjectId:     cond.ProjectId,
				Type:          common.ConstCategoryTreeTypePerfCase,
				CategoryId:    cond.CategoryId,
				OnlyDirectory: true,
			},
		)

		sb = sb.JoinClause(
			sub.Prefix("LEFT JOIN (").Suffix(
				fmt.Sprintf(
					") AS %s ON %s.`category_id` = %s.`category_id`", aliasT1, aliasT, aliasT1,
				),
			),
		).Where(fmt.Sprintf("%s.`category_id` IS NOT NULL", aliasT1))
	} else {
		sb = sb.Where(fmt.Sprintf("%s.`category_id` = ?", aliasT), cond.CategoryId)
	}

	return m.FindCount(ctx, sb)
}

type (
	SearchPerfCaseV2Item struct {
		ProjectId     string         `db:"project_id"`      // 项目ID
		CategoryId    string         `db:"category_id"`     // 所属分类ID
		CaseId        string         `db:"case_id"`         // 压测用例ID
		Name          string         `db:"name"`            // 压测用例名称
		Description   sql.NullString `db:"description"`     // 压测用例描述
		Tags          sql.NullString `db:"tags"`            // 标签
		Protocol      string         `db:"protocol"`        // 协议（TT私有协议、TT登录压测场景专用、通用gRPC协议、通用HTTP协议）
		RateLimits    string         `db:"rate_limits"`     // 限流配置
		SerialSteps   sql.NullString `db:"serial_steps"`    // 串行步骤
		ParallelSteps sql.NullString `db:"parallel_steps"`  // 并行步骤
		NumberOfSteps int64          `db:"number_of_steps"` // 测试步骤数
		State         int64          `db:"state"`           // 计划状态（生效、失效）
		MaintainedBy  sql.NullString `db:"maintained_by"`   // 维护者的用户ID
		CreatedBy     string         `db:"created_by"`      // 创建者的用户ID
		UpdatedBy     string         `db:"updated_by"`      // 最近一次更新者的用户ID
		CreatedAt     time.Time      `db:"created_at"`      // 创建时间
		UpdatedAt     time.Time      `db:"updated_at"`      // 更新时间
	}

	searchPerfCaseV2SelectBuilder struct {
		squirrel.SelectBuilder
	}
	searchPerfCaseV2CountBuilder struct {
		squirrel.SelectBuilder
	}
)

func (m *customPerfCaseV2Model) GenerateSearchPerfCaseV2SqlBuilder(req SearchPerfCaseV2Req) (
	searchPerfCaseV2SelectBuilder, searchPerfCaseV2CountBuilder,
) {
	/*
		SQL:
		SELECT t.`project_id`,
		       t.`category_id`,
		       t.`case_id`,
		       t.`name`,
		       t.`description`,
		       t.`tags`,
		       t.`protocol`,
		       t.`rate_limits`,
			   t.`serial_steps`,
			   t.`parallel_steps`,
		       t.`number_of_steps`,
		       t.`state`,
		       t.`maintained_by`,
		       t.`created_by`,
		       t.`updated_by`,
		       t.`created_at`,
		       t.`updated_at`
		FROM `perf_case_v2` AS t
		    LEFT JOIN (
		        SELECT t1.*
		        FROM `category` AS t1
		            INNER JOIN `category_tree` AS t2 ON
		                t1.`project_id` = t2.`project_id` AND
		                t1.`category_id` = t2.`descendant`
		        WHERE t1.`project_id` = ?
		          AND t1.`type` = ?
		          AND t2.`ancestor` = ?
		          AND t1.`deleted` = ?
		          AND t2.`deleted` = ?
		        ORDER BY t2.`depth` DESC, t2.`index` ASC, t1.`created_at` ASC
		    ) AS t1 ON t.`category_id` = t1.`category_id`
		WHERE t.`project_id` = ?
		  AND t.`deleted` = ?
		  AND t1.`category_id` IS NOT NULL;
	*/

	var (
		aliasT  = "t"
		aliasT1 = "t1"
		fields  = []string{
			"`project_id`",
			"`category_id`",
			"`case_id`",
			"`name`",
			"`description`",
			"`tags`",
			"`protocol`",
			"`rate_limits`",
			"`serial_steps`",
			"`parallel_steps`",
			"`number_of_steps`",
			"`state`",
			"`maintained_by`",
			"`created_by`",
			"`updated_by`",
			"`created_at`",
			"`updated_at`",
		}
	)

	sb := squirrel.Select(utils.AddTableNameToFields(aliasT, fields)...).
		From(m.table+" AS "+aliasT).
		Where(
			fmt.Sprintf("%s.`project_id` = ? AND %s.`deleted` = ?", aliasT, aliasT),
			req.ProjectID, constants.NotDeleted,
		)
	scb := squirrel.Select("COUNT(*)").
		From(m.table+" AS "+aliasT).
		Where(
			fmt.Sprintf("%s.`project_id` = ? AND %s.`deleted` = ?", aliasT, aliasT),
			req.ProjectID, constants.NotDeleted,
		)

	if req.DrillDown {
		sub := req.CategoryModel.FindDescendantCategoriesSqlBuilder(
			GetCategoryTreeCondition{
				ProjectId:     req.ProjectID,
				Type:          common.ConstCategoryTreeTypePerfCase,
				CategoryId:    req.CategoryID,
				OnlyDirectory: true,
			},
		)

		sb = sb.JoinClause(
			sub.Prefix("LEFT JOIN (").Suffix(
				fmt.Sprintf(
					") AS %s ON %s.`category_id` = %s.`category_id`", aliasT1, aliasT, aliasT1,
				),
			),
		).
			Where(fmt.Sprintf("%s.`category_id` IS NOT NULL", aliasT1))
		scb = scb.JoinClause(
			sub.Prefix("LEFT JOIN (").Suffix(
				fmt.Sprintf(
					") AS %s ON %s.`category_id` = %s.`category_id`", aliasT1, aliasT, aliasT1,
				),
			),
		).
			Where(fmt.Sprintf("%s.`category_id` IS NOT NULL", aliasT1))
	} else {
		sb = sb.Where(fmt.Sprintf("%s.`category_id` = ?", aliasT), req.CategoryID)
		scb = scb.Where(fmt.Sprintf("%s.`category_id` = ?", aliasT), req.CategoryID)
	}

	sb = sqlbuilder.SearchOptionsWithAlias(
		sb, aliasT,
		sqlbuilder.WithCondition(m, req.Condition),
		sqlbuilder.WithPagination(m, req.Pagination),
		sqlbuilder.WithSort(m, req.Sort),
	)
	scb = sqlbuilder.SearchOptionsWithAlias(scb, aliasT, sqlbuilder.WithCondition(m, req.Condition))

	return searchPerfCaseV2SelectBuilder{SelectBuilder: sb}, searchPerfCaseV2CountBuilder{SelectBuilder: scb}
}

func (m *customPerfCaseV2Model) FindCountPerfCasesV2(
	ctx context.Context, countBuilder searchPerfCaseV2CountBuilder,
) (int64, error) {
	return m.FindCount(ctx, countBuilder.SelectBuilder)
}

func (m *customPerfCaseV2Model) FindPerfCasesV2(ctx context.Context, selectBuilder searchPerfCaseV2SelectBuilder) (
	[]*SearchPerfCaseV2Item, error,
) {
	var resp []*SearchPerfCaseV2Item

	err := utils.FindRows(
		ctx, m.conn, selectBuilder.SelectBuilder, func() any {
			return &resp
		},
	)

	return resp, err
}

func (m *customPerfCaseV2Model) FindAllByPlanID(ctx context.Context, projectID, planID string) ([]*PerfCaseV2, error) {
	/*
		SQL:
		SELECT t1.*
		FROM `perf_case_v2` AS t1
		    INNER JOIN `perf_plan_case_relationship` AS t2 ON
		        t1.`project_id` = t2.`project_id` AND
		        t1.`case_id` = t2.`case_id` AND
		        t1.`deleted` = t2.`deleted`
		WHERE t1.`project_id` = ?
		  AND t2.`plan_id` = ?
		  AND t1.`deleted` = ?
	*/
	return m.FindNoCacheByQuery(
		ctx, squirrel.Select(utils.AddTableNameToFields("t1", perfCaseV2FieldNames)...).
			From(m.table+" AS t1").
			InnerJoin(perfPlanCaseRelationshipTableName+" AS t2 ON t1.`project_id` = t2.`project_id` AND t1.`case_id` = t2.`case_id` AND t1.`deleted` = t2.`deleted`").
			Where(
				"t1.`project_id` = ? AND t2.`plan_id` = ? AND t1.`deleted` = ?", projectID, planID, constants.NotDeleted,
			),
	)
}
