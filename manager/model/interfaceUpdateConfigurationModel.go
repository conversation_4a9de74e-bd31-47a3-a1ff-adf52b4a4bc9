package model

import (
	"context"

	"github.com/Masterminds/squirrel"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/types"
)

var (
	_ InterfaceUpdateConfigurationModel = (*customInterfaceUpdateConfigurationModel)(nil)

	interfaceUpdateConfigurationInsertFields = stringx.Remove(
		interfaceUpdateConfigurationFieldNames, "`id`", "`create_time`", "`update_time`", "`deleted_by`",
		"`created_at`", "`updated_at`", "`deleted_at`",
	)
)

type (
	// InterfaceUpdateConfigurationModel is an interface to be customized, add more methods here,
	// and implement the added methods in customInterfaceUpdateConfigurationModel.
	InterfaceUpdateConfigurationModel interface {
		interfaceUpdateConfigurationModel
		types.DBModel

		Trans(ctx context.Context, fn func(context context.Context, session sqlx.Session) error) error
		InsertBuilder(data *InterfaceUpdateConfiguration) squirrel.InsertBuilder
		UpdateBuilder(data *InterfaceUpdateConfiguration) squirrel.UpdateBuilder
		SelectBuilder() squirrel.SelectBuilder
		SelectCountBuilder() squirrel.SelectBuilder
		FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error)
		FindNoCacheByQuery(ctx context.Context, selectBuilder squirrel.SelectBuilder) (
			[]*InterfaceUpdateConfiguration, error,
		)

		FindAll(ctx context.Context) ([]*InterfaceUpdateConfiguration, error)
	}

	customInterfaceUpdateConfigurationModel struct {
		*defaultInterfaceUpdateConfigurationModel

		conn sqlx.SqlConn
	}
)

// NewInterfaceUpdateConfigurationModel returns a model for the database table.
func NewInterfaceUpdateConfigurationModel(
	conn sqlx.SqlConn, c cache.CacheConf, opts ...cache.Option,
) InterfaceUpdateConfigurationModel {
	return &customInterfaceUpdateConfigurationModel{
		defaultInterfaceUpdateConfigurationModel: newInterfaceUpdateConfigurationModel(conn, c, opts...),
		conn:                                     conn,
	}
}

func (m *customInterfaceUpdateConfigurationModel) Table() string {
	return m.table
}

func (m *customInterfaceUpdateConfigurationModel) Fields() []string {
	return interfaceUpdateConfigurationFieldNames
}

func (m *customInterfaceUpdateConfigurationModel) Trans(
	ctx context.Context, fn func(context context.Context, session sqlx.Session) error,
) error {
	return m.TransactCtx(ctx, fn)
}

func (m *customInterfaceUpdateConfigurationModel) InsertBuilder(data *InterfaceUpdateConfiguration) squirrel.InsertBuilder {
	return squirrel.Insert(m.table).Columns(interfaceUpdateConfigurationInsertFields...).Values(
		data.ProjectId, data.Type, data.LocalPath, data.DepLocalPaths, data.Deleted, data.CreatedBy, data.UpdatedBy,
	)
}

func (m *customInterfaceUpdateConfigurationModel) UpdateBuilder(data *InterfaceUpdateConfiguration) squirrel.UpdateBuilder {
	eq := squirrel.Eq{
		"`type`":            data.Type,
		"`local_path`":      data.LocalPath,
		"`dep_local_paths`": data.DepLocalPaths,
	}
	return squirrel.Update(m.table).SetMap(eq).Where("`id` = ?", data.Id)
}

func (m *customInterfaceUpdateConfigurationModel) SelectBuilder() squirrel.SelectBuilder {
	return squirrel.Select(interfaceUpdateConfigurationFieldNames...).Where(
		"`deleted` = ?", constants.NotDeleted,
	).From(m.table)
}

func (m *customInterfaceUpdateConfigurationModel) SelectCountBuilder() squirrel.SelectBuilder {
	return squirrel.Select("count(*)").Where("`deleted` = ?", constants.NotDeleted).From(m.table)
}

func (m *customInterfaceUpdateConfigurationModel) FindCount(
	ctx context.Context, countBuilder squirrel.SelectBuilder,
) (int64, error) {
	query, values, err := countBuilder.ToSql()
	if err != nil {
		return 0, err
	}

	var resp int64
	err = m.QueryRowNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return 0, err
	}
}

func (m *customInterfaceUpdateConfigurationModel) FindNoCacheByQuery(
	ctx context.Context, selectBuilder squirrel.SelectBuilder,
) ([]*InterfaceUpdateConfiguration, error) {
	query, values, err := selectBuilder.ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*InterfaceUpdateConfiguration
	err = m.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

func (m *customInterfaceUpdateConfigurationModel) FindAll(ctx context.Context) (
	[]*InterfaceUpdateConfiguration, error,
) {
	return m.FindNoCacheByQuery(ctx, m.SelectBuilder())
}
