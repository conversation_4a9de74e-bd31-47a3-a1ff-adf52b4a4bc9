package model

import (
	"context"
	"database/sql"
	"fmt"
	"time"

	"github.com/Masterminds/squirrel"
	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlc"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sqlbuilder"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/types"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/userinfo"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/utils"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
)

var (
	_                                           ApiPlanModel  = (*customApiPlanModel)(nil)
	VirtualSuitesInApiPlanModel                 types.DBModel = (*virtualSuitesInApiPlanModel)(nil)
	VirtualAdvancedSearchSuiteNotInApiPlanModel types.DBModel = (*virtualAdvancedSearchSuiteNotInApiPlanModel)(nil)
	VirtualCaseInApiPlanModel                   types.DBModel = (*virtualCaseInApiPlanModel)(nil)

	apiPlanInsertFields = stringx.Remove(
		apiPlanFieldNames, "`id`", "`create_time`", "`update_time`", "`deleted_by`", "`created_at`", "`updated_at`",
		"`deleted_at`",
	)

	virtualSuitesInApiPlanFieldNames                 = builder.RawFieldNames(&SearchSuiteInApiPlanItem{})
	virtualAdvancedSearchSuiteNotInApiPlanFieldNames = builder.RawFieldNames(&AdvancedSearchSuiteItem{})
	virtualCaseInApiPlanFieldNames                   = builder.RawFieldNames(&SearchCaseInApiPlanItem{})
)

type (
	// ApiPlanModel is an interface to be customized, add more methods here,
	// and implement the added methods in customApiPlanModel.
	ApiPlanModel interface {
		apiPlanModel
		types.DBModel

		Trans(ctx context.Context, fn func(context context.Context, session sqlx.Session) error) error
		InsertBuilder(data *ApiPlan) squirrel.InsertBuilder
		UpdateBuilder(data *ApiPlan) squirrel.UpdateBuilder
		SelectBuilder() squirrel.SelectBuilder
		SelectCountBuilder() squirrel.SelectBuilder
		FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error)
		FindNoCacheByQuery(ctx context.Context, builder squirrel.SelectBuilder) ([]*ApiPlan, error)

		InsertTX(ctx context.Context, session sqlx.Session, data *ApiPlan) (sql.Result, error)
		UpdateTX(ctx context.Context, session sqlx.Session, data *ApiPlan) (sql.Result, error)
		DeleteTX(ctx context.Context, session sqlx.Session, id int64) error

		GenerateSearchApiPlanSqlBuilder(req SearchApiPlanWithCategoryReq) (searchApiPlanSelectBuilder, searchApiPlanCountBuilder)
		FindCountApiPlans(ctx context.Context, countBuilder searchApiPlanCountBuilder) (int64, error)
		FindApiPlans(ctx context.Context, selectBuilder searchApiPlanSelectBuilder) ([]*SearchApiPlanItem, error)

		GenerateSearchSuiteInApiPlanSqlBuilder(req SearchSuiteInApiPlanReq) (
			searchSuiteInApiPlanSelectBuilder, searchSuiteInApiPlanCountBuilder,
		)
		FindCountSuitesInApiPlan(ctx context.Context, countBuilder searchSuiteInApiPlanCountBuilder) (int64, error)
		FindSuitesInApiPlan(
			ctx context.Context, selectBuilder searchSuiteInApiPlanSelectBuilder,
		) ([]*SearchSuiteInApiPlanItem, error)

		GenerateSearchSuiteNotInApiPlanSqlBuilder(req SearchSuiteNotInApiPlanReq) (
			searchSuiteNotInApiPlanSelectBuilder, searchSuiteNotInApiPlanCountBuilder,
		)
		FindCountSuitesNotInApiPlan(ctx context.Context, countBuilder searchSuiteNotInApiPlanCountBuilder) (
			int64, error,
		)
		FindSuitesNotInApiPlan(
			ctx context.Context, selectBuilder searchSuiteNotInApiPlanSelectBuilder,
		) ([]*SearchSuiteNotInApiPlanItem, error)

		GenerateSearchCaseInApiPlanSqlBuilder(req SearchCaseInApiPlanReq) (
			searchCaseInApiPlanSelectBuilder, searchCaseInApiPlanCountBuilder,
		)
		GenerateSearchCaseInApiPlanSqlBuilder2(req SearchCaseInApiPlanReq) (
			searchCaseInApiPlanSelectBuilder, searchCaseInApiPlanCountBuilder,
		)
		FindCountCasesInApiPlan(ctx context.Context, countBuilder searchCaseInApiPlanCountBuilder) (int64, error)
		FindCasesInApiPlan(
			ctx context.Context, selectBuilder searchCaseInApiPlanSelectBuilder,
		) ([]*SearchCaseInApiPlanItem, error)

		FindGeneralConfigByPlanId(ctx context.Context, req FindGeneralConfigByPlanIdReq) (*GeneralConfiguration, error)
		FindAccountConfigByPlanId(ctx context.Context, req FindAccountConfigByPlanIdReq) (
			[]*AccountConfiguration, error,
		)
		FindByGeneralConfigId(ctx context.Context, projectId, configId string) ([]*ApiPlan, error)
		RemoveByPlanId(ctx context.Context, session sqlx.Session, projectId, planId string) (sql.Result, error)

		GenerateAdvancedSearchSuiteNotInApiPlanSqlBuilder(req AdvancedSearchSuiteNotInApiPlanReq) (
			advancedSearchSuiteNotInApiPlanBuilder, advancedSearchSuiteNotInApiPlanCountBuilder,
		)
		GenerateAdvancedSearchSuiteNotInApiPlanSqlBuilder2(req AdvancedSearchSuiteNotInApiPlanReq) (
			advancedSearchSuiteNotInApiPlanBuilder, advancedSearchSuiteNotInApiPlanCountBuilder,
		)
		FindCountAdvanceSearchSuiteNotInApiPlan(
			ctx context.Context, countBuilder advancedSearchSuiteNotInApiPlanCountBuilder,
		) (int64, error)
		FindAdvanceSearchSuiteNotInApiPlan(
			ctx context.Context, selectBuilder advancedSearchSuiteNotInApiPlanBuilder,
		) ([]*AdvancedSearchSuiteItem, error)

		GenerateApiPlanSqlBuilderForProjectIdAndPlanIds(
			projectId string, planIds ...string,
		) squirrel.SelectBuilder

		FindCountByCategoryId(ctx context.Context, cm CategoryModel, cond FindCountByCategoryCondition) (int64, error)
	}

	customApiPlanModel struct {
		*defaultApiPlanModel

		conn sqlx.SqlConn
	}
)

// NewApiPlanModel returns a model for the database table.
func NewApiPlanModel(conn sqlx.SqlConn, c cache.CacheConf) ApiPlanModel {
	return &customApiPlanModel{
		defaultApiPlanModel: newApiPlanModel(conn, c),
		conn:                conn,
	}
}

func (m *customApiPlanModel) Table() string {
	return m.table
}

func (m *customApiPlanModel) Fields() []string {
	return apiPlanFieldNames
}

func (m *customApiPlanModel) Trans(
	ctx context.Context, fn func(context context.Context, session sqlx.Session) error,
) error {
	return m.TransactCtx(ctx, fn)
}

func (m *customApiPlanModel) InsertBuilder(data *ApiPlan) squirrel.InsertBuilder {
	return squirrel.Insert(m.table).Columns(apiPlanInsertFields...).Values(
		data.ProjectId, data.CategoryId, data.PlanId, data.Name, data.Description, data.Priority, data.Tags,
		data.State, data.Type, data.Purpose, data.CronExpression, data.GeneralConfigId, data.SuiteExecutionMode,
		data.CaseExecutionMode, data.Deleted, data.MaintainedBy, data.CreatedBy, data.UpdatedBy,
	)
}

func (m *customApiPlanModel) UpdateBuilder(data *ApiPlan) squirrel.UpdateBuilder {
	eq := squirrel.Eq{
		"`category_id`":          data.CategoryId,
		"`name`":                 data.Name,
		"`description`":          data.Description,
		"`priority`":             data.Priority,
		"`tags`":                 data.Tags,
		"`state`":                data.State,
		"`type`":                 data.Type,
		"`purpose`":              data.Purpose,
		"`cron_expression`":      data.CronExpression,
		"`general_config_id`":    data.GeneralConfigId,
		"`suite_execution_mode`": data.SuiteExecutionMode,
		"`case_execution_mode`":  data.CaseExecutionMode,
		"`deleted`":              data.Deleted,
		"`maintained_by`":        data.MaintainedBy,
		"`updated_by`":           data.UpdatedBy,
		"`deleted_by`":           data.DeletedBy,
		"`deleted_at`":           data.DeletedAt,
	}
	return squirrel.Update(m.table).SetMap(eq).Where("`id` = ?", data.Id)
}

func (m *customApiPlanModel) SelectBuilder() squirrel.SelectBuilder {
	return squirrel.Select(apiPlanFieldNames...).Where("`deleted` = ?", constants.NotDeleted).From(m.table)
}

func (m *customApiPlanModel) SelectCountBuilder() squirrel.SelectBuilder {
	return squirrel.Select("count(*)").Where("`deleted` = ?", constants.NotDeleted).From(m.table)
}

func (m *customApiPlanModel) FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error) {
	query, values, err := countBuilder.ToSql()
	if err != nil {
		return 0, err
	}

	var resp int64
	err = m.QueryRowNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return 0, err
	}
}

func (m *customApiPlanModel) FindNoCacheByQuery(ctx context.Context, builder squirrel.SelectBuilder) (
	[]*ApiPlan, error,
) {
	query, values, err := builder.ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*ApiPlan
	err = m.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

func (m *customApiPlanModel) InsertTX(ctx context.Context, session sqlx.Session, data *ApiPlan) (sql.Result, error) {
	managerApiPlanIdKey := fmt.Sprintf("%s%v", cacheManagerApiPlanIdPrefix, data.Id)
	managerApiPlanProjectIdPlanIdKey := fmt.Sprintf(
		"%s%v:%v", cacheManagerApiPlanProjectIdPlanIdPrefix, data.ProjectId, data.PlanId,
	)

	return m.ExecCtx(
		ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
			query, values, err := m.InsertBuilder(data).ToSql()
			if err != nil {
				return nil, err
			}
			if session != nil {
				return session.ExecCtx(ctx, query, values...)
			}
			return conn.ExecCtx(ctx, query, values...)
		}, managerApiPlanIdKey, managerApiPlanProjectIdPlanIdKey,
	)
}

func (m *customApiPlanModel) UpdateTX(ctx context.Context, session sqlx.Session, data *ApiPlan) (sql.Result, error) {
	managerApiPlanIdKey := fmt.Sprintf("%s%v", cacheManagerApiPlanIdPrefix, data.Id)
	managerApiPlanProjectIdPlanIdKey := fmt.Sprintf(
		"%s%v:%v", cacheManagerApiPlanProjectIdPlanIdPrefix, data.ProjectId, data.PlanId,
	)

	return m.ExecCtx(
		ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
			query, values, err := m.UpdateBuilder(data).ToSql()
			if err != nil {
				return nil, err
			}
			if session != nil {
				return session.ExecCtx(ctx, query, values...)
			}
			return conn.ExecCtx(ctx, query, values...)
		}, managerApiPlanIdKey, managerApiPlanProjectIdPlanIdKey,
	)
}

func (m *customApiPlanModel) DeleteTX(ctx context.Context, session sqlx.Session, id int64) error {
	data, err := m.FindOne(ctx, id)
	if err != nil {
		return err
	}

	managerApiPlanIdKey := fmt.Sprintf("%s%v", cacheManagerApiPlanIdPrefix, id)
	managerApiPlanProjectIdPlanIdKey := fmt.Sprintf(
		"%s%v:%v", cacheManagerApiPlanProjectIdPlanIdPrefix, data.ProjectId, data.PlanId,
	)

	_, err = m.ExecCtx(
		ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
			query := fmt.Sprintf("delete from %s where `id` = ?", m.table)
			if session != nil {
				return session.ExecCtx(ctx, query, id)
			}
			return conn.ExecCtx(ctx, query, id)
		}, managerApiPlanIdKey, managerApiPlanProjectIdPlanIdKey,
	)

	return err
}

type SearchApiPlanItem struct {
	ApiPlan

	SuiteIncluded int64 `db:"suite_included"`
}

type SearchApiPlanWithCategoryReq struct {
	SearchApiPlanReq

	CategoryId string `json:"category_id"`
	DrillDown  bool   `json:"drill_down"`

	CategoryModel CategoryModel
}

type searchApiPlanSelectBuilder struct {
	squirrel.SelectBuilder
}

type searchApiPlanCountBuilder struct {
	squirrel.SelectBuilder
}

func (m *customApiPlanModel) GenerateSearchApiPlanSqlBuilder(req SearchApiPlanWithCategoryReq) (
	searchApiPlanSelectBuilder, searchApiPlanCountBuilder,
) {
	/*
		SQL:
		SELECT t.*
		FROM (SELECT t1.*, SUM(IF(t2.`id` IS NULL, 0, 1)) AS `suite_included`
		      FROM `api_plan` AS t1
		               LEFT JOIN `api_plan_reference_relationship` AS t2
		                         ON t1.`project_id` = t2.`project_id` AND t1.`plan_id` = t2.`plan_id` AND
		                            t1.`deleted` = t2.`deleted` AND
		                            (t2.`reference_type` = 'API_SUITE' OR t2.`reference_type` = 'INTERFACE_DOCUMENT')
		      WHERE t1.`project_id` = ?
		        AND t1.`deleted` = 0
		      GROUP BY t1.`plan_id`) AS t
			LEFT JOIN (
			  SELECT t1.*
			    FROM category AS t1
					INNER JOIN category_tree AS t2 ON
						t1.`project_id` = t2.`project_id` AND
						t1.`category_id` = t2.`descendant`
			    WHERE t1.`project_id` = ?
					AND t1.`type` = ?
					AND t2.`ancestor` = ?
					AND t1.`deleted` = ?
					AND t2.`deleted` = ?
			    ORDER BY t2.`depth` DESC, t2.`index` ASC, t1.`created_at` ASC
			) AS t1 ON t.`category_id` = t1.`category_id`
		WHERE t.`project_id` = ?
			AND t.`deleted` = ?
			AND t1.`category_id` IS NOT NULL;

		SELECT COUNT(*)
		FROM (SELECT t1.*, SUM(IF(t2.`id` IS NULL, 0, 1)) AS `suite_included`
		      FROM `api_plan` AS t1
		               LEFT JOIN `api_plan_reference_relationship` AS t2
		                         ON t1.`project_id` = t2.`project_id` AND t1.`plan_id` = t2.`plan_id` AND
		                            t1.`deleted` = t2.`deleted` AND
		                            (t2.`reference_type` = 'API_SUITE' OR t2.`reference_type` = 'INTERFACE_DOCUMENT')
		      WHERE t1.`project_id` = ?
		        AND t1.`deleted` = 0
		      GROUP BY t1.`plan_id`) AS t
			LEFT JOIN (
			  SELECT t1.*
			    FROM category AS t1
					INNER JOIN category_tree AS t2 ON
						t1.`project_id` = t2.`project_id` AND
						t1.`category_id` = t2.`descendant`
			    WHERE t1.`project_id` = ?
					AND t1.`type` = ?
					AND t2.`ancestor` = ?
					AND t1.`deleted` = ?
					AND t2.`deleted` = ?
			    ORDER BY t2.`depth` DESC, t2.`index` ASC, t1.`created_at` ASC
			) AS t1 ON t.`category_id` = t1.`category_id`
		WHERE t.`project_id` = ?
			AND t.`deleted` = ?
			AND t1.`category_id` IS NOT NULL;
	*/

	var sb, scb squirrel.SelectBuilder

	leftJoinOnStr := "t1.`project_id` = t2.`project_id` AND t1.`plan_id` = t2.`plan_id` AND t1.`deleted` = t2.`deleted` AND (t2.`reference_type` = ? OR t2.`reference_type` = ?)"

	tmp := squirrel.Select(
		utils.AddTableNameToFields(
			"t1", apiPlanFieldNames,
		)...,
	).Columns("SUM(IF(t2.`id` IS NULL, 0, 1)) AS `suite_included`").
		From(m.table+" AS t1").
		LeftJoin(
			fmt.Sprintf("%s AS t2 ON %s", apiPlanReferenceRelationshipTableName, leftJoinOnStr),
			common.ConstReferenceTypeApiSuite, common.ConstReferenceTypeInterfaceDocument,
		).
		Where("t1.`project_id` = ? AND t1.`deleted` = ?", req.ProjectId, constants.NotDeleted).
		GroupBy("t1.`plan_id`")

	alias := "t"

	sb = squirrel.Select(
		utils.AddTableNameToFields(
			alias, apiPlanFieldNames,
		)...,
	).Columns(alias+".suite_included").FromSelect(tmp, alias)
	scb = squirrel.Select("COUNT(*)").FromSelect(tmp, alias)

	if req.DrillDown {
		sub := req.CategoryModel.FindDescendantCategoriesSqlBuilder(
			GetCategoryTreeCondition{
				ProjectId:     req.ProjectId,
				Type:          common.ConstCategoryTreeTypeApiPlan,
				CategoryId:    req.CategoryId,
				OnlyDirectory: true,
			},
		)

		sb = sb.JoinClause(
			sub.Prefix("LEFT JOIN (").Suffix(
				fmt.Sprintf(
					") AS t1 ON %s.`category_id` = t1.`category_id`", alias,
				),
			),
		).Where("t1.`category_id` IS NOT NULL")
		scb = scb.JoinClause(
			sub.Prefix("LEFT JOIN (").Suffix(
				fmt.Sprintf(
					") AS t1 ON %s.`category_id` = t1.`category_id`", alias,
				),
			),
		).Where("t1.`category_id` IS NOT NULL")
	} else {
		sb = sb.Where(fmt.Sprintf("%s.`category_id` = ?", alias), req.CategoryId)
		scb = scb.Where(fmt.Sprintf("%s.`category_id` = ?", alias), req.CategoryId)
	}

	sb = sqlbuilder.SearchOptionsWithAlias(
		sb, alias, sqlbuilder.WithCondition(m, req.Condition), sqlbuilder.WithPagination(m, req.Pagination),
		sqlbuilder.WithSort(m, req.Sort),
	)
	scb = sqlbuilder.SearchOptionsWithAlias(scb, alias, sqlbuilder.WithCondition(m, req.Condition))

	return searchApiPlanSelectBuilder{SelectBuilder: sb}, searchApiPlanCountBuilder{SelectBuilder: scb}
}

func (m *customApiPlanModel) FindCountApiPlans(ctx context.Context, countBuilder searchApiPlanCountBuilder) (
	int64, error,
) {
	return m.FindCount(ctx, countBuilder.SelectBuilder)
}

func (m *customApiPlanModel) FindApiPlans(
	ctx context.Context, selectBuilder searchApiPlanSelectBuilder,
) ([]*SearchApiPlanItem, error) {
	var resp []*SearchApiPlanItem

	err := utils.FindRows(
		ctx, m.conn, selectBuilder.SelectBuilder, func() any {
			return &resp
		},
	)

	return resp, err
}

type virtualSuitesInApiPlanModel struct{}

func (m *virtualSuitesInApiPlanModel) Table() string {
	return "`virtual_suites_in_api_plan`"
}

func (m *virtualSuitesInApiPlanModel) Fields() []string {
	return virtualSuitesInApiPlanFieldNames
}

type SearchSuiteInApiPlanItem struct {
	ProjectId      string         `db:"project_id"`      // 项目ID
	SuiteType      string         `db:"suite_type"`      // 集合类型
	SuiteId        string         `db:"suite_id"`        // 集合ID
	Name           string         `db:"name"`            // 集合名称
	Description    sql.NullString `db:"description"`     // 集合描述
	Priority       int64          `db:"priority"`        // 优先级（NULL、P0、P1、P2、P3...）
	Tags           sql.NullString `db:"tags"`            // 标签
	State          int64          `db:"state"`           // 集合状态（生效、失效）
	ReferenceState int64          `db:"reference_state"` // 集合引用状态（已使用、未使用）
	CaseIncluded   int64          `db:"case_included"`   // 包含的用例数
	CaseSkipped    int64          `db:"case_skipped"`    // 跳过的用例数
	MaintainedBy   sql.NullString `db:"maintained_by"`   // 维护者的用户ID
	CreatedBy      string         `db:"created_by"`      // 创建者的用户ID
	UpdatedBy      string         `db:"updated_by"`      // 最近一次更新者的用户ID
	CreatedAt      time.Time      `db:"created_at"`      // 创建时间
	UpdatedAt      time.Time      `db:"updated_at"`      // 更新时间
}

// [2024-04-16] delete this method:
// Validate 判断API计划中的集合是否可被执行
//func (x *SearchSuiteInApiPlanItem) Validate() bool {
//	// 集合固有状态为生效 且 集合的引用状态为已使用 才能被执行
//	return x.State == int64(constants.EnableStatus) && x.ReferenceState == int64(constants.EnableStatus)
//}

type searchSuiteInApiPlanSelectBuilder struct {
	squirrel.SelectBuilder
}

type searchSuiteInApiPlanCountBuilder struct {
	squirrel.SelectBuilder
}

func (m *customApiPlanModel) GenerateSearchSuiteInApiPlanSqlBuilder(req SearchSuiteInApiPlanReq) (
	searchSuiteInApiPlanSelectBuilder, searchSuiteInApiPlanCountBuilder,
) {
	/*
		SQL:
		SELECT t.`project_id`,
		       t.`suite_type`,
		       t.`suite_id`,
		       t.`name`,
		       t.`description`,
		       t.`priority`,
		       t.`tags`,
		       t.`state`,
		       t.`reference_state`,
		       t.`case_included`,
		       t.`case_skipped`,
		       t.`maintained_by`,
		       t.`created_by`,
		       t.`updated_by`,
		       t.`created_at`,
		       t.`updated_at`
		FROM (SELECT t1.`project_id`,
		             t3.`reference_type`           AS `suite_type`,
		             t2.`suite_id`,
		             t2.`name`,
		             t2.`description`,
		             t2.`priority`,
		             t2.`tags`,
		             t2.`state`,
		             t3.`state`                    AS `reference_state`,
		             COUNT(t4.`id`)                AS `case_included`,
		             SUM(IF(t4.`state` = 0, 1, 0)) AS `case_skipped`,
		             t2.`maintained_by`,
		             t2.`created_by`,
		             t2.`updated_by`,
		             t2.`created_at`,
		             t2.`updated_at`
		      FROM `api_plan` AS t1
		               INNER JOIN `api_suite` AS t2
		                          ON t1.`project_id` = t2.`project_id`
		                              AND t1.`deleted` = t2.`deleted`
		               INNER JOIN `api_plan_reference_relationship` AS t3
		                          ON t1.`project_id` = t3.`project_id`
		                              AND t1.`plan_id` = t3.`plan_id`
		                              AND t1.`deleted` = t3.`deleted`
		                              AND t2.`suite_id` = t3.`reference_id`
		                              AND t3.`reference_type` = 'API_SUITE'
		               LEFT JOIN `api_plan_reference_relationship` AS t4
		                         ON t1.`project_id` = t4.`project_id`
		                             AND t1.`plan_id` = t4.`plan_id`
		                             AND t1.`deleted` = t4.`deleted`
		                             AND t2.`suite_id` = t4.`reference_parent_id`
		                             AND t4.`reference_type` IN ('API_CASE', 'INTERFACE_CASE')
		      WHERE t1.`project_id` = ?
		        AND t1.`plan_id` = ?
		        AND t1.`deleted` = ?
		      GROUP BY t2.`suite_id`
		      UNION
		      SELECT t1.`project_id`,
		             t3.`reference_type`           AS `suite_type`,
		             t2.`document_id`              AS `suite_id`,
		             t2.`name`,
		             t2.`description`,
		             t2.`priority`,
		             t2.`tags`,
		             t2.`state`,
		             t3.`state`                    AS `reference_state`,
		             COUNT(t4.`id`)                AS `case_included`,
		             SUM(IF(t4.`state` = 0, 1, 0)) AS `case_skipped`,
		             t2.`maintained_by`,
		             t2.`created_by`,
		             t2.`updated_by`,
		             t2.`created_at`,
		             t2.`updated_at`
		      FROM `api_plan` AS t1
		               INNER JOIN `interface_document` AS t2
		                          ON t1.`project_id` = t2.`project_id`
		                              AND t1.`deleted` = t2.`deleted`
		               INNER JOIN `api_plan_reference_relationship` AS t3
		                          ON t1.`project_id` = t3.`project_id`
		                              AND t1.`plan_id` = t3.`plan_id`
		                              AND t1.`deleted` = t3.`deleted`
		                              AND t2.`document_id` = t3.`reference_id`
		                              AND t3.`reference_type` = 'INTERFACE_DOCUMENT'
		               LEFT JOIN `api_plan_reference_relationship` AS t4
		                         ON t1.`project_id` = t4.`project_id`
		                             AND t1.`plan_id` = t4.`plan_id`
		                             AND t1.`deleted` = t4.`deleted`
		                             AND t2.`document_id` = t4.`reference_parent_id`
		                             AND t4.`reference_type` = 'INTERFACE_CASE'
		      WHERE t1.`project_id` = ?
		        AND t1.`plan_id` = ?
		        AND t1.`deleted` = ?
		      GROUP BY t2.`document_id`) AS t
	*/

	// 注意：这里为了简化SQL，减少`join`表数量，所以没有对`api_case`、`api_case_reference_relationship`、`interface_case`等表进行关联查询

	var sb, scb squirrel.SelectBuilder

	apiSuiteFields := []string{
		"t1.`project_id`",
		"t3.`reference_type` AS `suite_type`",
		"t2.`suite_id`",
		"t2.`name`",
		"t2.`description`",
		"t2.`priority`",
		"t2.`tags`",
		"t2.`state`",
		"t3.`state` AS `reference_state`",
		"COUNT(t4.`id`) AS `case_included`",
		"SUM(IF(t4.`state` = 0, 1, 0)) AS `case_skipped`",
		"t2.`maintained_by`",
		"t2.`created_by`",
		"t2.`updated_by`",
		"t2.`created_at`",
		"t2.`updated_at`",
	}
	interfaceSuiteFields := []string{
		"t1.`project_id`",
		"t3.`reference_type` AS `suite_type`",
		"t2.`document_id` AS `suite_id`",
		"t2.`name`",
		"t2.`description`",
		"t2.`priority`",
		"t2.`tags`",
		"t2.`state`",
		"t3.`state` AS `reference_state`",
		"COUNT(t4.`id`) AS `case_included`",
		"SUM(IF(t4.`state` = 0, 1, 0)) AS `case_skipped`",
		"t2.`maintained_by`",
		"t2.`created_by`",
		"t2.`updated_by`",
		"t2.`created_at`",
		"t2.`updated_at`",
	}

	asm := req.ApiSuiteModel
	idm := req.InterfaceDocumentModel
	spm := VirtualSuitesInApiPlanModel

	sb1 := squirrel.Select(apiSuiteFields...).
		From(m.table+" AS t1").
		InnerJoin(apiSuiteTableName+" AS t2 ON t1.`project_id` = t2.`project_id` AND t1.`deleted` = t2.`deleted`").
		InnerJoin(
			apiPlanReferenceRelationshipTableName+" AS t3 ON t1.`project_id` = t3.`project_id` AND t1.`plan_id` = t3.`plan_id` AND t1.`deleted` = t3.`deleted` AND t2.`suite_id` = t3.`reference_id` AND t3.`reference_type` = ?",
			common.ConstReferenceTypeApiSuite,
		).
		LeftJoin(
			apiPlanReferenceRelationshipTableName+" AS t4 ON t1.`project_id` = t4.`project_id` AND t1.`plan_id` = t4.`plan_id` AND t1.`deleted` = t4.`deleted` AND t2.`suite_id` = t4.`reference_parent_id` AND t4.`reference_type` IN (?, ?)",
			common.ConstReferenceTypeApiCase, common.ConstReferenceTypeInterfaceCase,
		).
		Where(
			"t1.`project_id` = ? AND t1.`plan_id` = ? AND t1.`deleted` = ?", req.ProjectId, req.PlanId,
			constants.NotDeleted,
		).
		GroupBy("t2.`suite_id`")
	sb1 = sqlbuilder.SearchOptionsWithAlias(sb1, "t2", sqlbuilder.WithCondition(asm, req.Condition))

	sb2 := squirrel.Select(interfaceSuiteFields...).
		From(m.table+" AS t1").
		InnerJoin(interfaceDocumentTableName+" AS t2 ON t1.`project_id` = t2.`project_id` AND t1.`deleted` = t2.`deleted`").
		InnerJoin(
			apiPlanReferenceRelationshipTableName+" AS t3 ON t1.`project_id` = t3.`project_id` AND t1.`plan_id` = t3.`plan_id` AND t1.`deleted` = t3.`deleted` AND t2.`document_id` = t3.`reference_id` AND t3.`reference_type` = ?",
			common.ConstReferenceTypeInterfaceDocument,
		).
		LeftJoin(
			apiPlanReferenceRelationshipTableName+" AS t4 ON t1.`project_id` = t4.`project_id` AND t1.`plan_id` = t4.`plan_id` AND t1.`deleted` = t4.`deleted` AND t2.`document_id` = t4.`reference_parent_id` AND t4.`reference_type` = ?",
			common.ConstReferenceTypeInterfaceCase,
		).
		Where(
			"t1.`project_id` = ? AND t1.`plan_id` = ? AND t1.`deleted` = ?", req.ProjectId, req.PlanId,
			constants.NotDeleted,
		).
		GroupBy("t2.`document_id`")
	sb2 = sqlbuilder.SearchOptionsWithAlias(sb2, "t2", sqlbuilder.WithCondition(idm, req.Condition))

	alias := "t"
	if req.SuiteType == common.ConstReferenceTypeApiSuite {
		sb = sqlbuilder.SearchOptionsWithAlias(
			sb1, "t2", sqlbuilder.WithPagination(asm, req.Pagination), sqlbuilder.WithSort(asm, req.Sort),
		)
		scb = squirrel.Select("COUNT(*)").FromSelect(sb1, alias)
	} else if req.SuiteType == common.ConstReferenceTypeInterfaceDocument {
		sb = sqlbuilder.SearchOptionsWithAlias(
			sb2, "t2", sqlbuilder.WithPagination(idm, req.Pagination), sqlbuilder.WithSort(idm, req.Sort),
		)
		scb = squirrel.Select("COUNT(*)").FromSelect(sb2, alias)
	} else {
		query, values, _ := sb2.ToSql()
		tmp := sb1.Suffix("UNION "+query, values...)

		sb = sqlbuilder.SearchOptionsWithAlias(
			squirrel.Select(
				utils.AddTableNameToFields(
					alias, virtualSuitesInApiPlanFieldNames,
				)...,
			).FromSelect(tmp, alias), alias, sqlbuilder.WithPagination(spm, req.Pagination),
			sqlbuilder.WithSort(spm, req.Sort),
		)
		scb = squirrel.Select("COUNT(*)").FromSelect(tmp, alias)
	}

	return searchSuiteInApiPlanSelectBuilder{SelectBuilder: sb}, searchSuiteInApiPlanCountBuilder{SelectBuilder: scb}
}

func (m *customApiPlanModel) FindCountSuitesInApiPlan(
	ctx context.Context, countBuilder searchSuiteInApiPlanCountBuilder,
) (int64, error) {
	return m.FindCount(ctx, countBuilder.SelectBuilder)
}

func (m *customApiPlanModel) FindSuitesInApiPlan(
	ctx context.Context, selectBuilder searchSuiteInApiPlanSelectBuilder,
) ([]*SearchSuiteInApiPlanItem, error) {
	var resp []*SearchSuiteInApiPlanItem

	err := utils.FindRows(
		ctx, m.conn, selectBuilder.SelectBuilder, func() any {
			return &resp
		},
	)

	return resp, err
}

type SearchSuiteNotInApiPlanItem struct {
	ProjectId    string         `db:"project_id"`    // 项目ID
	CategoryId   string         `db:"category_id"`   // 分类ID
	SuiteId      string         `db:"suite_id"`      // 集合ID
	Name         string         `db:"name"`          // 集合名称
	Description  sql.NullString `db:"description"`   // 集合描述
	Priority     int64          `db:"priority"`      // 优先级（NULL、P0、P1、P2、P3...）
	Tags         sql.NullString `db:"tags"`          // 标签
	State        int64          `db:"state"`         // 集合状态
	MaintainedBy sql.NullString `db:"maintained_by"` // 维护者的用户ID
	CreatedBy    string         `db:"created_by"`    // 创建者的用户ID
	UpdatedBy    string         `db:"updated_by"`    // 最近一次更新者的用户ID
	CreatedAt    time.Time      `db:"created_at"`    // 创建时间
	UpdatedAt    time.Time      `db:"updated_at"`    // 更新时间
}

type searchSuiteNotInApiPlanSelectBuilder struct {
	squirrel.SelectBuilder
}

type searchSuiteNotInApiPlanCountBuilder struct {
	squirrel.SelectBuilder
}

func (m *customApiPlanModel) GenerateSearchSuiteNotInApiPlanSqlBuilder(req SearchSuiteNotInApiPlanReq) (
	searchSuiteNotInApiPlanSelectBuilder, searchSuiteNotInApiPlanCountBuilder,
) {
	/*
		SQL:
		SELECT t.*
		FROM api_suite AS t
			LEFT JOIN (
				SELECT t1.*
		        FROM category AS t1
		        	INNER JOIN category_tree AS t2 ON t1.`project_id` = t2.`project_id` AND t1.`category_id` = t2.`descendant`
				WHERE t1.`project_id` = ?
				  AND t1.`type` = 'API_SUITE'
				  AND t1.`category_type` = 'DIRECTORY'
		          AND t2.`ancestor` = ?
		          AND t1.`deleted` = ?
		          AND t2.`deleted` = ?
		        ORDER BY t2.`depth` DESC, t2.`index` ASC, t1.`created_at` ASC) AS t1 ON t.category_id = t1.category_id
			LEFT JOIN (
				SELECT t1.*
				FROM api_suite AS t1, api_plan_reference_relationship AS t2
				WHERE t1.`project_id` = t2.`project_id`
		          AND t1.`suite_id` = t2.`reference_id`
		          AND t1.`project_id` = ?
		          AND t1.`deleted` = ?
		          AND t2.`reference_type` = 'API_SUITE'
		          AND t2.`plan_id` = ?
		          AND t2.`deleted` = ?) AS t2 ON t.`suite_id` = t2.`suite_id`
		WHERE t.`project_id` = ?
		  AND t.`deleted` = ?
		  AND t1.`category_id` IS NOT NULL
		  AND t2.`suite_id` IS NULL;

		SELECT t.*
		FROM `interface_document` AS t
			LEFT JOIN (
				SELECT t1.*
		        FROM `category` AS t1
		        	INNER JOIN `category_tree` AS t2 ON t1.`project_id` = t2.`project_id` AND t1.`category_id` = t2.`descendant`
				WHERE t1.`project_id` = ?
		          AND t1.`type` = 'INTERFACE_DOCUMENT'
		          AND t1.`category_type` = 'DIRECTORY'
		          AND t2.`ancestor` = ?
		          AND t1.`deleted` = ?
		          AND t2.`deleted` = ?
		        ORDER BY t2.`depth` DESC, t2.`index` ASC, t1.`created_at` ASC) AS t1 ON t.category_id = t1.category_id
			LEFT JOIN (
				SELECT t1.*
		        FROM `interface_document` AS t1, `api_plan_reference_relationship` AS t2
				WHERE t1.`project_id` = t2.`project_id`
		          AND t1.`document_id` = t2.`reference_id`
		          AND t1.`project_id` = ?
		          AND t1.`deleted` = ?
		          AND t2.`reference_type` = 'INTERFACE_DOCUMENT'
		          AND t2.`plan_id` = ?
		          AND t2.`deleted` = ?) AS t2 ON t.`document_id` = t2.`document_id`
		WHERE t.`project_id` = ?
		  AND t.`deleted` = ?
		  AND t1.`category_id` IS NOT NULL
		  AND t2.`document_id` IS NULL;
	*/

	var (
		sb, scb squirrel.SelectBuilder

		table  string
		fields []string

		categoryTreeType common.CategoryTreeType
		suiteType        common.ReferenceType
		suiteIdName      string
	)

	alias := "t"

	if req.SuiteType == common.ConstReferenceTypeApiSuite {
		table = apiSuiteTableName
		fields = []string{
			alias + ".`project_id`",
			alias + ".`category_id`",
			alias + ".`suite_id`",
			alias + ".`name`",
			alias + ".`description`",
			alias + ".`priority`",
			alias + ".`tags`",
			alias + ".`state`",
			alias + ".`maintained_by`",
			alias + ".`created_by`",
			alias + ".`updated_by`",
			alias + ".`created_at`",
			alias + ".`updated_at`",
		}
		categoryTreeType = common.ConstCategoryTreeTypeApiSuite
		suiteType = common.ConstReferenceTypeApiSuite
		suiteIdName = "suite_id"
	} else {
		table = interfaceDocumentTableName
		fields = []string{
			alias + ".`project_id`",
			alias + ".`category_id`",
			alias + ".`document_id` AS `suite_id`",
			alias + ".`name`",
			alias + ".`description`",
			alias + ".`priority`",
			alias + ".`tags`",
			alias + ".`state`",
			alias + ".`maintained_by`",
			alias + ".`created_by`",
			alias + ".`updated_by`",
			alias + ".`created_at`",
			alias + ".`updated_at`",
		}
		categoryTreeType = common.ConstCategoryTreeTypeInterfaceDocument
		suiteType = common.ConstReferenceTypeInterfaceDocument
		suiteIdName = "document_id"
	}

	sb = squirrel.Select(fields...).
		From(fmt.Sprintf("%s AS %s", table, alias)).
		Where(
			fmt.Sprintf("%s.`project_id` = ? AND %s.`deleted` = ?", alias, alias), req.ProjectId, constants.NotDeleted,
		)
	scb = squirrel.Select("COUNT(*)").
		From(fmt.Sprintf("%s AS %s", table, alias)).
		Where(
			fmt.Sprintf("%s.`project_id` = ? AND %s.`deleted` = ?", alias, alias), req.ProjectId, constants.NotDeleted,
		)

	if req.DrillDown {
		sub1 := req.CategoryModel.FindDescendantCategoriesSqlBuilder(
			GetCategoryTreeCondition{
				ProjectId:     req.ProjectId,
				Type:          categoryTreeType,
				CategoryId:    req.CategoryId,
				OnlyDirectory: true,
			},
		)

		sb = sb.JoinClause(
			sub1.Prefix("LEFT JOIN (").Suffix(
				fmt.Sprintf(
					") AS t1 ON %s.`category_id` = t1.`category_id`", alias,
				),
			),
		).
			Where("t1.`id` IS NOT NULL")
		scb = scb.JoinClause(
			sub1.Prefix("LEFT JOIN (").Suffix(
				fmt.Sprintf(
					") AS t1 ON %s.`category_id` = t1.`category_id`", alias,
				),
			),
		).
			Where("t1.`id` IS NOT NULL")
	} else {
		sb = sb.Where(fmt.Sprintf("%s.`category_id` = ?", alias), req.CategoryId)
		scb = scb.Where(fmt.Sprintf("%s.`category_id` = ?", alias), req.CategoryId)
	}

	sub2, _ := m.GenerateSearchSuiteInApiPlanSqlBuilder(
		SearchSuiteInApiPlanReq{
			ApiSuiteModel:          req.ApiSuiteModel,
			InterfaceDocumentModel: req.InterfaceDocumentModel,
			ProjectId:              req.ProjectId,
			PlanId:                 req.PlanId,
			SuiteType:              suiteType,
		},
	)

	sb = sb.JoinClause(
		sub2.Prefix("LEFT JOIN (").Suffix(
			fmt.Sprintf(
				") AS t2 ON %s.`%s` = t2.`suite_id`", alias, suiteIdName,
			),
		),
	).
		Where("t2.`suite_id` IS NULL")
	scb = scb.JoinClause(
		sub2.Prefix("LEFT JOIN (").Suffix(
			fmt.Sprintf(
				") AS t2 ON %s.`%s` = t2.`suite_id`", alias, suiteIdName,
			),
		),
	).
		Where("t2.`suite_id` IS NULL")

	sb = sqlbuilder.SearchOptionsWithAlias(
		sb, alias, sqlbuilder.WithCondition(m, req.Condition), sqlbuilder.WithPagination(m, req.Pagination),
		sqlbuilder.WithSort(m, req.Sort),
	)
	scb = sqlbuilder.SearchOptionsWithAlias(scb, alias, sqlbuilder.WithCondition(m, req.Condition))

	return searchSuiteNotInApiPlanSelectBuilder{SelectBuilder: sb}, searchSuiteNotInApiPlanCountBuilder{SelectBuilder: scb}
}

func (m *customApiPlanModel) FindCountSuitesNotInApiPlan(
	ctx context.Context, countBuilder searchSuiteNotInApiPlanCountBuilder,
) (int64, error) {
	return m.FindCount(ctx, countBuilder.SelectBuilder)
}

func (m *customApiPlanModel) FindSuitesNotInApiPlan(
	ctx context.Context, selectBuilder searchSuiteNotInApiPlanSelectBuilder,
) ([]*SearchSuiteNotInApiPlanItem, error) {
	var resp []*SearchSuiteNotInApiPlanItem

	err := utils.FindRows(
		ctx, m.conn, selectBuilder.SelectBuilder, func() any {
			return &resp
		},
	)

	return resp, err
}

type virtualCaseInApiPlanModel struct{}

func (m *virtualCaseInApiPlanModel) Table() string {
	return "`virtual_case_in_api_plan`"
}

func (m *virtualCaseInApiPlanModel) Fields() []string {
	return virtualCaseInApiPlanFieldNames
}

type SearchCaseInApiPlanItem struct {
	ProjectId           string         `db:"project_id"`            // 项目ID
	SuiteId             string         `db:"suite_id"`              // 集合ID
	CaseType            string         `db:"case_type"`             // 用例类型
	CaseId              string         `db:"case_id"`               // 用例ID
	Name                string         `db:"name"`                  // 用例名称
	Description         sql.NullString `db:"description"`           // 用例描述
	Priority            int64          `db:"priority"`              // 优先级（NULL、P0、P1、P2、P3...）
	Tags                sql.NullString `db:"tags"`                  // 标签
	State               string         `db:"state"`                 // 用例状态
	SuiteReferenceState int64          `db:"suite_reference_state"` // 集合引用状态
	ReferenceState      int64          `db:"reference_state"`       // 用例引用状态
	MaintainedBy        sql.NullString `db:"maintained_by"`         // 维护者的用户ID
	CreatedBy           string         `db:"created_by"`            // 创建者的用户ID
	UpdatedBy           string         `db:"updated_by"`            // 最近一次更新者的用户ID
	CreatedAt           time.Time      `db:"created_at"`            // 创建时间
	UpdatedAt           time.Time      `db:"updated_at"`            // 更新时间
}

// [2024-04-16] delete this method:
// Validate 判断API计划中的用例是否可被执行
//func (x *SearchCaseInApiPlanItem) Validate() bool {
//	// 用例固有状态为生效 且 集合的引用状态为已使用 且 用例的引用状态为已使用 才能被执行
//	return x.State == int64(constants.EnableStatus) && x.SuiteReferenceState == int64(constants.EnableStatus) && x.ReferenceState == int64(constants.EnableStatus)
//}

type searchCaseInApiPlanSelectBuilder struct {
	squirrel.SelectBuilder
}

type searchCaseInApiPlanCountBuilder struct {
	squirrel.SelectBuilder
}

func (m *customApiPlanModel) GenerateSearchCaseInApiPlanSqlBuilder(req SearchCaseInApiPlanReq) (
	searchCaseInApiPlanSelectBuilder, searchCaseInApiPlanCountBuilder,
) {
	/*
		SQL:
		SELECT t3.`project_id`,
		       t2.`suite_id` AS `suite_id`,
		       'API_CASE' AS `case_type`,
		       t3.`case_id`,
		       t3.`name`,
		       t3.`description`,
		       t3.`priority`,
		       t3.`tags`,
		       t3.`state`,
		       t4.`state` AS `suite_reference_state`,
		       t5.`state` AS `reference_state`,
		       t3.`maintained_by`,
		       t3.`created_by`,
		       t3.`updated_by`,
		       t3.`created_at`,
		       t3.`updated_at`
		FROM `api_plan` AS t1,
		     `api_suite` AS t2,
		     `api_case` AS t3,
		     `api_plan_reference_relationship` AS t4,
		     `api_plan_reference_relationship` AS t5,
		     `api_case_reference_relationship` AS t6
		WHERE t1.`project_id` = t2.`project_id`
		  AND t1.`project_id` = t3.`project_id`
		  AND t1.`project_id` = t4.`project_id`
		  AND t1.`project_id` = t5.`project_id`
		  AND t1.`project_id` = t6.`project_id`
		  AND t1.`plan_id` = t4.`plan_id`
		  AND t1.`plan_id` = t5.`plan_id`
		  AND t1.`deleted` = t2.`deleted`
		  AND t1.`deleted` = t3.`deleted`
		  AND t1.`deleted` = t4.`deleted`
		  AND t1.`deleted` = t5.`deleted`
		  AND t1.`deleted` = t6.`deleted`
		  AND t2.`suite_id` = t4.`reference_id`
		  AND t4.`reference_type` = 'API_SUITE'
		  AND t2.`suite_id` = t5.`reference_parent_id`
		  AND t3.`case_id` = t5.`reference_id`
		  AND t5.`reference_type` = 'API_CASE'
		  AND t2.`suite_id` = t6.`reference_id`
		  AND t3.`case_id` = t6.`case_id`
		  AND t6.`reference_type` = 'API_SUITE'
		  AND t1.`project_id` = ?
		  AND t1.`plan_id` = ?
		  AND t1.`deleted` = ?
		  AND t2.`suite_id` = ?
		  AND t3.`latest` = ?;

		SELECT t3.`project_id`,
		       t2.`document_id` AS `suite_id`,
		       'INTERFACE_CASE' AS `case_type`,
		       t3.`case_id`,
		       t3.`name`,
		       t3.`description`,
		       t3.`priority`,
		       t3.`tags`,
		       t3.`state`,
		       t4.`state` AS `suite_reference_state`,
		       t5.`state` AS `reference_state`,
		       t3.`maintained_by`,
		       t3.`created_by`,
		       t3.`updated_by`,
		       t3.`created_at`,
		       t3.`updated_at`
		FROM `api_plan` AS t1,
		     `interface_document` AS t2,
		     `interface_case` AS t3,
		     `api_plan_reference_relationship` AS t4,
		     `api_plan_reference_relationship` AS t5
		WHERE t1.`project_id` = t2.`project_id`
		  AND t1.`project_id` = t3.`project_id`
		  AND t1.`project_id` = t4.`project_id`
		  AND t1.`project_id` = t5.`project_id`
		  AND t1.`plan_id` = t4.`plan_id`
		  AND t1.`plan_id` = t5.`plan_id`
		  AND t1.`deleted` = t2.`deleted`
		  AND t1.`deleted` = t3.`deleted`
		  AND t1.`deleted` = t4.`deleted`
		  AND t1.`deleted` = t5.`deleted`
		  AND t2.`document_id` = t3.`document_id`
		  AND t2.`document_id` = t4.`reference_id`
		  AND t4.`reference_type` = 'INTERFACE_DOCUMENT'
		  AND t2.`document_id` = t5.`reference_parent_id`
		  AND t3.`case_id` = t5.`reference_id`
		  AND t5.`reference_type` = 'INTERFACE_CASE'
		  AND t1.`project_id` = ?
		  AND t1.`plan_id` = ?
		  AND t1.`deleted` = ?
		  AND t2.`document_id` = ?
		  AND t3.`latest` = ?;
	*/

	var (
		sb, scb   squirrel.SelectBuilder
		caseModel types.DBModel

		suiteIDField                                                                      string
		caseType                                                                          common.ReferenceType
		fromStr, whereProjectIdStr, whereDeletedStr, whereT2Str, whereT3Str, whereT456Str string
		whereT456Args                                                                     []any
	)

	acm := req.ApiCaseModel
	icm := req.InterfaceCaseModel

	if req.SuiteType == common.ConstReferenceTypeApiSuite {
		caseModel = acm
		suiteIDField = "t2.`suite_id`"
		caseType = common.ConstReferenceTypeApiCase

		fromStr = fmt.Sprintf(
			"%s AS t1, %s AS t2, %s AS t3, %s AS t4, %s AS t5, %s AS t6",
			m.table, apiSuiteTableName, apiCaseTableName, apiPlanReferenceRelationshipTableName,
			apiPlanReferenceRelationshipTableName, apiCaseReferenceRelationshipTableName,
		)
		whereProjectIdStr = "t1.`project_id` = t2.`project_id` AND t1.`project_id` = t3.`project_id` AND t1.`project_id` = t4.`project_id` AND t1.`project_id` = t5.`project_id` AND t1.`project_id` = t6.`project_id`"
		whereDeletedStr = "t1.`deleted` = t2.`deleted` AND t1.`deleted` = t3.`deleted` AND t1.`deleted` = t4.`deleted` AND t1.`deleted` = t5.`deleted` AND t1.`deleted` = t6.`deleted`"
		whereT2Str = "t2.`suite_id` = t4.`reference_id` AND t2.`suite_id` = t5.`reference_parent_id` AND t2.`suite_id` = t6.`reference_id` AND t2.`suite_id` = ?"
		whereT3Str = "t3.`case_id` = t5.`reference_id` AND t3.`case_id` = t6.`case_id` AND t3.`latest` = ?"
		whereT456Str = "t4.`reference_type` = ? AND t5.`reference_type` = ? AND t6.`reference_type` = ?"
		whereT456Args = []any{
			common.ConstReferenceTypeApiSuite, common.ConstReferenceTypeApiCase, common.ConstReferenceTypeApiSuite,
		}
	} else {
		caseModel = icm
		suiteIDField = "t2.`document_id`"
		caseType = common.ConstReferenceTypeInterfaceCase

		fromStr = fmt.Sprintf(
			"%s AS t1, %s AS t2, %s AS t3, %s AS t4, %s AS t5",
			m.table, interfaceDocumentTableName, interfaceCaseTableName, apiPlanReferenceRelationshipTableName,
			apiPlanReferenceRelationshipTableName,
		)
		whereProjectIdStr = "t1.`project_id` = t2.`project_id` AND t1.`project_id` = t3.`project_id` AND t1.`project_id` = t4.`project_id` AND t1.`project_id` = t5.`project_id`"
		whereDeletedStr = "t1.`deleted` = t2.`deleted` AND t1.`deleted` = t3.`deleted` AND t1.`deleted` = t4.`deleted` AND t1.`deleted` = t5.`deleted`"
		whereT2Str = "t2.`document_id` = t3.`document_id` AND t2.`document_id` = t4.`reference_id` AND t2.`document_id` = t5.`reference_parent_id` AND t2.`document_id` = ?"
		whereT3Str = "t3.`case_id` = t5.`reference_id` AND t3.`latest` = ?"
		whereT456Str = "t4.`reference_type` = ? AND t5.`reference_type` = ?"
		whereT456Args = []any{common.ConstReferenceTypeInterfaceDocument, common.ConstReferenceTypeInterfaceCase}
	}

	fields := []string{
		"t3.`project_id`",
		suiteIDField + " AS `suite_id`",
		"'" + caseType + "' AS `case_type`",
		"t3.`case_id`",
		"t3.`name`",
		"t3.`description`",
		"t3.`priority`",
		"t3.`tags`",
		"t3.`state`",
		"t4.`state` AS `suite_reference_state`",
		"t5.`state` AS `reference_state`",
		"t3.`maintained_by`",
		"t3.`created_by`",
		"t3.`updated_by`",
		"t3.`created_at`",
		"t3.`updated_at`",
	}

	sb = squirrel.Select(fields...).
		From(fromStr).
		Where(whereProjectIdStr).
		Where("t1.`plan_id` = t4.`plan_id` AND t1.`plan_id` = t5.`plan_id`").
		Where(whereDeletedStr).
		Where(
			"t1.`project_id` = ? AND t1.`plan_id` = ? AND t1.`deleted` = ?",
			req.ProjectId, req.PlanId, constants.NotDeleted,
		).
		Where(whereT2Str, req.SuiteId).
		Where(whereT3Str, constants.IsLatestVersion).
		Where(whereT456Str, whereT456Args...)
	scb = squirrel.Select("COUNT(*)").
		From(fromStr).
		Where(whereProjectIdStr).
		Where("t1.`plan_id` = t4.`plan_id` AND t1.`plan_id` = t5.`plan_id`").
		Where(whereDeletedStr).
		Where(
			"t1.`project_id` = ? AND t1.`plan_id` = ? AND t1.`deleted` = ?",
			req.ProjectId, req.PlanId, constants.NotDeleted,
		).
		Where(whereT2Str, req.SuiteId).
		Where(whereT3Str, constants.IsLatestVersion).
		Where(whereT456Str, whereT456Args...)

	sb = sqlbuilder.SearchOptionsWithAlias(
		sb, "t3", sqlbuilder.WithCondition(caseModel, req.Condition),
		sqlbuilder.WithPagination(caseModel, req.Pagination), sqlbuilder.WithSort(caseModel, req.Sort),
	)
	scb = sqlbuilder.SearchOptionsWithAlias(scb, "t3", sqlbuilder.WithCondition(caseModel, req.Condition))

	return searchCaseInApiPlanSelectBuilder{SelectBuilder: sb}, searchCaseInApiPlanCountBuilder{SelectBuilder: scb}
}

func (m *customApiPlanModel) GenerateSearchCaseInApiPlanSqlBuilder2(req SearchCaseInApiPlanReq) (
	searchCaseInApiPlanSelectBuilder, searchCaseInApiPlanCountBuilder,
) {
	/*
		SELECT
		  t.*
		FROM(
		    SELECT
		      COALESCE(t2.`project_id`, t3.`project_id`) AS `project_id`,
		      t1.`reference_parent_id` AS `suite_id`,
		      t1.`reference_type` AS `case_type`,
		      COALESCE(t2.`case_id`, t3.`case_id`) AS `case_id`,
		      COALESCE(t2.`name`, t3.`name`) AS `name`,
		      COALESCE(t2.`description`, t3.`description`) AS `description`,
		      COALESCE(t2.`priority`, t3.`priority`) AS `priority`,
		      COALESCE(t2.`tags`, t3.`tags`) AS `tags`,
		      COALESCE(t2.`state`, t3.`state`) AS `state`,
		      '1' AS `suite_reference_state`,
		      t1.`state` AS `reference_state`,
		      COALESCE(t2.`maintained_by`, t3.`maintained_by`) AS `maintained_by`,
		      COALESCE(t2.`created_by`, t3.`created_by`) AS `created_by`,
			  COALESCE(t2.`updated_by`, t3.`updated_by`) AS `updated_by`,
		      COALESCE(t2.`created_at`, t3.`created_at`) AS `created_at`,
		      COALESCE(t2.`updated_at`, t3.`updated_at`) AS `updated_at`
		    FROM
		      `api_plan_reference_relationship` AS t1
		      LEFT JOIN `api_case` AS t2 ON t1.`reference_type` = 'API_CASE'
		      AND t2.`latest` = 1
		      AND t1.`reference_id` = t2.`case_id`
		      AND t1.`project_id` = t2.`project_id`
		      AND t1.`deleted` = t2.`deleted`
		      LEFT JOIN `interface_case` AS t3 ON t1.`reference_type` = 'INTERFACE_CASE'
		      AND t3.`latest` = 1
		      AND t1.`reference_id` = t3.`case_id`
		      AND t1.`project_id` = t3.`project_id`
		      AND t1.`deleted` = t3.`deleted`
		    WHERE
		      t1.`project_id` = ?
		      AND t1.`plan_id` = ?
			  AND t1.`reference_parent_id` = ?
		      AND t1.`deleted` = ?
		  ) AS t;
	*/

	fields := []string{
		"COALESCE(t2.`project_id`, t3.`project_id`) AS `project_id`",
		"t1.`reference_parent_id` AS `suite_id`",
		"t1.`reference_type` AS `case_type`",
		"COALESCE(t2.`case_id`, t3.`case_id`) AS `case_id`",
		"COALESCE(t2.`name`, t3.`name`) AS `name`",
		"COALESCE(t2.`description`, t3.`description`) AS `description`",
		"COALESCE(t2.`priority`, t3.`priority`) AS `priority`",
		"COALESCE(t2.`tags`, t3.`tags`) AS `tags`",
		"COALESCE(t2.`state`, t3.`state`) AS `state`",
		"'1' AS `suite_reference_state`",
		"t1.`state` AS `reference_state`",
		"COALESCE(t2.`maintained_by`, t3.`maintained_by`) AS `maintained_by`",
		"COALESCE(t2.`created_by`, t3.`created_by`) AS `created_by`",
		"COALESCE(t2.`updated_by`, t3.`updated_by`) AS `updated_by`",
		"COALESCE(t2.`created_at`, t3.`created_at`) AS `created_at`",
		"COALESCE(t2.`updated_at`, t3.`updated_at`) AS `updated_at`",
	}

	tmp := squirrel.Select(fields...).
		From(apiPlanReferenceRelationshipTableName+" AS t1").
		LeftJoin(
			apiCaseTableName+" AS t2 ON t1.`reference_type` = ? AND t2.`latest` = ? AND t1.`reference_id` = t2.`case_id` AND t1.`project_id` = t2.`project_id` AND t1.`deleted` = t2.`deleted`",
			common.ConstReferenceTypeApiCase, constants.IsLatestVersion,
		).
		LeftJoin(
			interfaceCaseTableName+" AS t3 ON t1.`reference_type` = ? AND t3.`latest` = ? AND t1.`reference_id` = t3.`case_id` AND t1.`project_id` = t3.`project_id` AND t1.`deleted` = t3.`deleted`",
			common.ConstReferenceTypeInterfaceCase, constants.IsLatestVersion,
		).
		Where(
			"t1.`project_id` = ? AND t1.`plan_id` = ? AND t1.`reference_parent_id` = ? AND t1.`deleted` = ?",
			req.ProjectId, req.PlanId, req.SuiteId, constants.NotDeleted,
		)

	alias := "t"
	vm := VirtualCaseInApiPlanModel
	sb := sqlbuilder.SearchOptionsWithAlias(
		squirrel.Select(utils.AddTableNameToFields(alias, vm.Fields())...).FromSelect(tmp, alias),
		alias,
		sqlbuilder.WithCondition(vm, req.Condition),
		sqlbuilder.WithPagination(vm, req.Pagination),
		sqlbuilder.WithSort(vm, req.Sort),
	)
	scb := sqlbuilder.SearchOptionsWithAlias(
		squirrel.Select("COUNT(*)").FromSelect(tmp, alias),
		alias,
		sqlbuilder.WithCondition(vm, req.Condition),
	)

	return searchCaseInApiPlanSelectBuilder{SelectBuilder: sb}, searchCaseInApiPlanCountBuilder{SelectBuilder: scb}
}

func (m *customApiPlanModel) FindCountCasesInApiPlan(
	ctx context.Context, countBuilder searchCaseInApiPlanCountBuilder,
) (int64, error) {
	return m.FindCount(ctx, countBuilder.SelectBuilder)
}

func (m *customApiPlanModel) FindCasesInApiPlan(
	ctx context.Context, selectBuilder searchCaseInApiPlanSelectBuilder,
) ([]*SearchCaseInApiPlanItem, error) {
	var resp []*SearchCaseInApiPlanItem

	err := utils.FindRows(
		ctx, m.conn, selectBuilder.SelectBuilder, func() any {
			return &resp
		},
	)

	return resp, err
}

type AdvancedSearchSuiteItem struct {
	ProjectId    string         `db:"project_id"`    // 项目ID
	CategoryId   string         `db:"category_id"`   // 分类ID
	SuiteId      string         `db:"suite_id"`      // 集合ID
	Name         string         `db:"name"`          // 接口名称
	Description  sql.NullString `db:"description"`   // 接口描述
	Priority     int64          `db:"priority"`      // 优先级（NULL、P0、P1、P2、P3...）
	Tags         sql.NullString `db:"tags"`          // 标签
	State        int8           `db:"state"`         // 集合状态
	CaseCount    int64          `db:"case_count"`    // 集合内用例数
	MaintainedBy sql.NullString `db:"maintained_by"` // 维护者的用户ID
	CreatedBy    string         `db:"created_by"`    // 创建者的用户ID
	UpdatedBy    string         `db:"updated_by"`    // 最近一次更新者的用户ID
	CreatedAt    time.Time      `db:"created_at"`    // 创建时间
	UpdatedAt    time.Time      `db:"updated_at"`    // 更新时间
}

type advancedSearchSuiteNotInApiPlanBuilder struct {
	squirrel.SelectBuilder
}

type advancedSearchSuiteNotInApiPlanCountBuilder struct {
	squirrel.SelectBuilder
}

type virtualAdvancedSearchSuiteNotInApiPlanModel struct{}

func (m *virtualAdvancedSearchSuiteNotInApiPlanModel) Table() string {
	return "`virtual_advanced_search_suite_not_in_plan`"
}

func (m *virtualAdvancedSearchSuiteNotInApiPlanModel) Fields() []string {
	return virtualAdvancedSearchSuiteNotInApiPlanFieldNames
}

func (m *customApiPlanModel) GenerateAdvancedSearchSuiteNotInApiPlanSqlBuilder(req AdvancedSearchSuiteNotInApiPlanReq) (
	advancedSearchSuiteNotInApiPlanBuilder, advancedSearchSuiteNotInApiPlanCountBuilder,
) {
	/*
		SQL:
		SELECT t3.`project_id`,
		       t3.`category_id`,
		       t3.`suite_id`,
		       t3.`name`,
		       t3.`description`,
		       t3.`priority`,
		       t3.`tags`,
		       t3.`state`,
		       t3.`case_count`,
		       t3.`maintained_by`,
		       t3.`created_by`,
		       t3.`updated_by`,
		       t3.`created_at`,
		       t3.`updated_at`
		FROM (SELECT t1.`project_id`,
		             t1.`category_id`,
		             t1.`suite_id`,
		             t1.`name`,
		             t1.`description`,
		             t1.`priority`,
		             t1.`tags`,
		             t1.`state`,
		             COUNT(t2.`case_id`) AS `case_count`,
		             t1.`maintained_by`,
		             t1.`created_by`,
		             t1.`updated_by`,
		             t1.`created_at`,
		             t1.`updated_at`
		      FROM `api_suite` AS t1
		               LEFT JOIN `api_case_reference_relationship` AS t2
		                         ON t1.`project_id` = t2.`project_id`
		                             AND t1.`suite_id` = t2.`reference_id`
		                             AND t1.`deleted` = t2.`deleted`
		               LEFT JOIN `api_plan_reference_relationship` AS t4
		                         ON t4.`plan_id` = ?
		                             AND t4.`project_id` = t1.`project_id`
		                             AND t4.`reference_id` = t1.`suite_id`
		                             AND t4.`deleted` = ?
		                             AND t4.`reference_type` = ?
		      WHERE t1.`project_id` = ?
		        AND t1.`deleted` = ?
		        AND t4.`reference_id` IS NULL
		      GROUP BY t1.`suite_id`) AS t3
		WHERE (t3.`name` LIKE ?)
		ORDER BY t3.`updated_at` DESC
		LIMIT 20 OFFSET 0
	*/

	var (
		sb, scb         squirrel.SelectBuilder
		fields, fields2 []string
		// suite_table case_table
		st, ct      string
		suiteIdName string
	)

	if req.SuiteType == common.ConstReferenceTypeApiSuite {
		st = apiSuiteTableName
		ct = apiCaseReferenceRelationshipTableName
		fields = []string{
			"t1.`project_id`",
			"t1.`category_id`",
			"t1.`suite_id`",
			"t1.`name`",
			"t1.`description`",
			"t1.`priority`",
			"t1.`tags`",
			"t1.`state`",
			"COUNT(t2.case_id) AS `case_count`",
			"t1.`maintained_by`",
			"t1.`created_by`",
			"t1.`updated_by`",
			"t1.`created_at`",
			"t1.`updated_at`",
		}
		suiteIdName = "suite_id"
		sb = squirrel.Select(fields...).
			From(st + " AS t1").
			LeftJoin(ct + " AS t2 ON t1.`project_id` = t2.`project_id` AND t1.`suite_id` = t2.`reference_id` AND t1.`deleted` = t2.`deleted`")
	} else {
		st = interfaceDocumentTableName
		ct = interfaceCaseTableName
		fields = []string{
			"t1.`project_id`",
			"t1.`category_id`",
			"t1.`document_id` AS `suite_id`",
			"t1.`name`",
			"t1.`description`",
			"t1.`priority`",
			"t1.`tags`",
			"t1.`state`",
			"COUNT(t2.case_id) AS `case_count`",
			"t1.`maintained_by`",
			"t1.`created_by`",
			"t1.`updated_by`",
			"t1.`created_at`",
			"t1.`updated_at`",
		}
		suiteIdName = "document_id"
		sb = squirrel.Select(fields...).
			From(st+" AS t1").
			LeftJoin(
				ct+" AS t2 ON t1.`project_id` = t2.`project_id` AND t1.`document_id` = t2.`document_id` AND t2.`latest` = ? AND t1.`deleted` = t2.`deleted`",
				constants.IsLatestVersion,
			)
	}

	fields2 = []string{
		"t3.`project_id`",
		"t3.`category_id`",
		"t3.`suite_id`",
		"t3.`name`",
		"t3.`description`",
		"t3.`priority`",
		"t3.`tags`",
		"t3.`state`",
		"t3.`case_count`",
		"t3.`maintained_by`",
		"t3.`created_by`",
		"t3.`updated_by`",
		"t3.`created_at`",
		"t3.`updated_at`",
	}
	sb = sb.LeftJoin(
		fmt.Sprintf(
			" `api_plan_reference_relationship` AS t4 on t4.`plan_id` = ? and t4.`project_id` = t1.`project_id` and t4.`reference_id` = t1.`%s` and t4.`deleted` = ? and t4.`reference_type` = ?",
			suiteIdName,
		), req.PlanId, constants.NotDeleted, req.SuiteType,
	).
		Where(
			" t1.project_id = ? AND t1.`deleted` = ? AND t4.`reference_id` IS NULL", req.ProjectId,
			constants.NotDeleted,
		).
		GroupBy(fmt.Sprintf("t1.`%s`", suiteIdName))
	sb = squirrel.Select(fields2...).FromSelect(sb, "t3")
	sb = sqlbuilder.SearchOptionsWithAlias(
		sb, "t3", sqlbuilder.WithCondition(VirtualAdvancedSearchSuiteNotInApiPlanModel, req.Condition),
	)
	scb = squirrel.Select("COUNT(*)").FromSelect(sb, "t")
	sb = sqlbuilder.SearchOptionsWithAlias(
		sb, "t3", sqlbuilder.WithPagination(VirtualAdvancedSearchSuiteNotInApiPlanModel, req.Pagination),
		sqlbuilder.WithSort(VirtualAdvancedSearchSuiteNotInApiPlanModel, req.Sort),
	)
	sbq, v, _ := sb.ToSql()
	fmt.Println(sbq, v)
	return advancedSearchSuiteNotInApiPlanBuilder{SelectBuilder: sb}, advancedSearchSuiteNotInApiPlanCountBuilder{SelectBuilder: scb}
}

func (m *customApiPlanModel) GenerateAdvancedSearchSuiteNotInApiPlanSqlBuilder2(req AdvancedSearchSuiteNotInApiPlanReq) (
	advancedSearchSuiteNotInApiPlanBuilder, advancedSearchSuiteNotInApiPlanCountBuilder,
) {
	/*
		SELECT
		  t3.`project_id`,
		  t3.`category_id`,
		  t3.`suite_id`,
		  t3.`name`,
		  t3.`description`,
		  t3.`priority`,
		  t3.`tags`,
		  t3.`state`,
		  t3.`case_count`,
		  t3.`maintained_by`,
		  t3.`created_by`,
		  t3.`updated_by`,
		  t3.`created_at`,
		  t3.`updated_at`
		FROM
		  (
		    SELECT
		      t1.`project_id`,
		      t1.`category_id`,
		      t1.`suite_id`,
		      t1.`name`,
		      t1.`description`,
		      t1.`priority`,
		      t1.`tags`,
		      t1.`state`,
		      COUNT(t2.`reference_id`) AS `case_count`,
		      t1.`maintained_by`,
		      t1.`created_by`,
		      t1.`updated_by`,
		      t1.`created_at`,
		      t1.`updated_at`
		    FROM
		      `api_suite` AS t1
		      LEFT JOIN `api_suite_reference_relationship` AS t2 ON t1.`project_id` = t2.`project_id`
		      AND t1.`suite_id` = t2.`suite_id`
		      AND t1.`deleted` = t2.`deleted`
		      LEFT JOIN `api_plan_reference_relationship` AS t4 ON t4.`plan_id` = ?
		      AND t4.`project_id` = t1.`project_id`
		      AND t4.`reference_id` = t1.`suite_id`
		      AND t4.`deleted` = ?
		      AND t4.`reference_type` = ?
		    WHERE
		      t1.`project_id` = ?
		      AND t1.`deleted` = ?
		      AND t4.`reference_id` IS NULL
		    GROUP BY
		      t1.`suite_id`
		  ) AS t3
		WHERE
		  (t3.`name` LIKE ?)
		ORDER BY
		  t3.`updated_at` DESC
		LIMIT
		  20 OFFSET 0
	*/

	var (
		sb, scb         squirrel.SelectBuilder
		fields, fields2 []string
		// suite_table case_table
		st, ct      string
		suiteIdName string
	)

	if req.SuiteType == common.ConstReferenceTypeApiSuite {
		st = apiSuiteTableName
		ct = apiSuiteReferenceRelationshipTableName
		fields = []string{
			"t1.`project_id`",
			"t1.`category_id`",
			"t1.`suite_id`",
			"t1.`name`",
			"t1.`description`",
			"t1.`priority`",
			"t1.`tags`",
			"t1.`state`",
			"COUNT(t2.`reference_id`) AS `case_count`",
			"t1.`maintained_by`",
			"t1.`created_by`",
			"t1.`updated_by`",
			"t1.`created_at`",
			"t1.`updated_at`",
		}
		suiteIdName = "suite_id"
		sb = squirrel.Select(fields...).
			From(st + " AS t1").
			LeftJoin(ct + " AS t2 ON t1.`project_id` = t2.`project_id` AND t1.`suite_id` = t2.`suite_id` AND t1.`deleted` = t2.`deleted`")
	} else {
		st = interfaceDocumentTableName
		ct = interfaceCaseTableName
		fields = []string{
			"t1.`project_id`",
			"t1.`category_id`",
			"t1.`document_id` AS `suite_id`",
			"t1.`name`",
			"t1.`description`",
			"t1.`priority`",
			"t1.`tags`",
			"t1.`state`",
			"COUNT(t2.case_id) AS `case_count`",
			"t1.`maintained_by`",
			"t1.`created_by`",
			"t1.`updated_by`",
			"t1.`created_at`",
			"t1.`updated_at`",
		}
		suiteIdName = "document_id"
		sb = squirrel.Select(fields...).
			From(st+" AS t1").
			LeftJoin(
				ct+" AS t2 ON t1.`project_id` = t2.`project_id` AND t1.`document_id` = t2.`document_id` AND t2.`latest` = ? AND t1.`deleted` = t2.`deleted`",
				constants.IsLatestVersion,
			)
	}

	fields2 = []string{
		"t3.`project_id`",
		"t3.`category_id`",
		"t3.`suite_id`",
		"t3.`name`",
		"t3.`description`",
		"t3.`priority`",
		"t3.`tags`",
		"t3.`state`",
		"t3.`case_count`",
		"t3.`maintained_by`",
		"t3.`created_by`",
		"t3.`updated_by`",
		"t3.`created_at`",
		"t3.`updated_at`",
	}
	sb = sb.LeftJoin(
		fmt.Sprintf(
			" `api_plan_reference_relationship` AS t4 on t4.`plan_id` = ? and t4.`project_id` = t1.`project_id` and t4.`reference_id` = t1.`%s` and t4.`deleted` = ? and t4.`reference_type` = ?",
			suiteIdName,
		), req.PlanId, constants.NotDeleted, req.SuiteType,
	).
		Where(
			" t1.project_id = ? AND t1.`deleted` = ? AND t4.`reference_id` IS NULL", req.ProjectId,
			constants.NotDeleted,
		).
		GroupBy(fmt.Sprintf("t1.`%s`", suiteIdName))
	sb = squirrel.Select(fields2...).FromSelect(sb, "t3")
	sb = sqlbuilder.SearchOptionsWithAlias(
		sb, "t3", sqlbuilder.WithCondition(VirtualAdvancedSearchSuiteNotInApiPlanModel, req.Condition),
	)
	scb = squirrel.Select("COUNT(*)").FromSelect(sb, "t")
	sb = sqlbuilder.SearchOptionsWithAlias(
		sb, "t3", sqlbuilder.WithPagination(VirtualAdvancedSearchSuiteNotInApiPlanModel, req.Pagination),
		sqlbuilder.WithSort(VirtualAdvancedSearchSuiteNotInApiPlanModel, req.Sort),
	)
	sbq, v, _ := sb.ToSql()
	fmt.Println(sbq, v)
	return advancedSearchSuiteNotInApiPlanBuilder{SelectBuilder: sb}, advancedSearchSuiteNotInApiPlanCountBuilder{SelectBuilder: scb}
}

func (m *customApiPlanModel) FindCountAdvanceSearchSuiteNotInApiPlan(
	ctx context.Context, countBuilder advancedSearchSuiteNotInApiPlanCountBuilder,
) (int64, error) {
	return m.FindCount(ctx, countBuilder.SelectBuilder)
}

func (m *customApiPlanModel) FindAdvanceSearchSuiteNotInApiPlan(
	ctx context.Context, selectBuilder advancedSearchSuiteNotInApiPlanBuilder,
) ([]*AdvancedSearchSuiteItem, error) {
	var resp []*AdvancedSearchSuiteItem

	err := utils.FindRows(
		ctx, m.conn, selectBuilder.SelectBuilder, func() any {
			return &resp
		},
	)

	return resp, err
}

func (m *customApiPlanModel) FindGeneralConfigByPlanId(
	ctx context.Context, req FindGeneralConfigByPlanIdReq,
) (*GeneralConfiguration, error) {
	/*
		SQL:
		SELECT t2.*
		FROM `api_plan` AS t1,
			 `general_configuration` AS t2
		WHERE t1.`project_id` = t2.`project_id`
		  AND t1.`general_config_id` = t2.`config_id`
		  AND t1.`deleted` = t2.`deleted`
		  AND t1.`project_id` = ?
		  AND t1.`plan_id` = ?
		  AND t1.`deleted` = ?
		ORDER BY t1.`updated_at` DESC, t2.`updated_at` DESC
		LIMIT 1
	*/

	query, values, err := squirrel.Select(utils.AddTableNameToFields("t2", generalConfigurationFieldNames)...).
		From(fmt.Sprintf("%s AS t1, %s AS t2", m.table, generalConfigurationTableName)).
		Where("t1.`project_id` = t2.`project_id` AND t1.`general_config_id` = t2.`config_id` AND t1.`deleted` = t2.`deleted`").
		Where(
			"t1.`project_id` = ? AND t1.`plan_id` = ? AND t1.`deleted` = ?",
			req.ProjectId, req.PlanId, constants.NotDeleted,
		).
		OrderBy("t1.`updated_at` DESC, t2.`updated_at` DESC").
		Limit(1).ToSql()
	if err != nil {
		return nil, err
	}

	var resp GeneralConfiguration

	err = m.QueryRowNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *customApiPlanModel) FindAccountConfigByPlanId(
	ctx context.Context, req FindAccountConfigByPlanIdReq,
) ([]*AccountConfiguration, error) {
	/*
		SQL:
		SELECT t2.*
		FROM `api_plan` AS t1,
		     `account_configuration` AS t2,
		     `account_configuration_reference_relationship` AS t3
		WHERE t1.`project_id` = t2.`project_id`
		  AND t1.`project_id` = t3.`project_id`
		  AND t1.`plan_id` = t3.`reference_id`
		  AND t2.`config_id` = t3.`config_id`
		  AND t3.`reference_type` = 'API_PLAN'
		  AND t1.`deleted` = t2.`deleted`
		  AND t1.`deleted` = t3.`deleted`
		  AND t1.`project_id` = ?
		  AND t1.`plan_id` = ?
		  AND t1.`deleted` = ?
	*/

	sb := squirrel.Select(utils.AddTableNameToFields("t2", accountConfigurationFieldNames)...).
		From(
			fmt.Sprintf(
				"%s AS t1, %s AS t2, %s AS t3", m.table, accountConfigurationTableName,
				accountConfigurationReferenceRelationshipTableName,
			),
		).
		Where("t1.`project_id` = t2.`project_id` AND t1.`project_id` = t3.`project_id`").
		Where(
			"t1.`plan_id` = t3.`reference_id` AND t2.`config_id` = t3.`config_id` AND t3.`reference_type` = ?",
			common.ConstReferenceTypeApiPlan,
		).
		Where("t1.`deleted` = t2.`deleted` AND t1.`deleted` = t3.`deleted`").
		Where(
			"t1.`project_id` = ? AND t1.`plan_id` = ? AND t1.`deleted` = ?",
			req.ProjectId, req.PlanId, constants.NotDeleted,
		)

	var resp []*AccountConfiguration

	err := utils.FindRows(
		ctx, m.conn, sb, func() any {
			return &resp
		},
	)

	return resp, err
}

func (m *customApiPlanModel) FindByGeneralConfigId(ctx context.Context, projectId, configId string) (
	[]*ApiPlan, error,
) {
	/*
		SQL:
		SELECT .*
		FROM `api_plan` AS t1, `general_configuration` AS t2
		WHERE t1.`project_id` = t2.`project_id`
		  AND t1.`general_config_id` = t2.`config_id`
		  AND t1.`deleted` = t2.`deleted`
		  AND t1.`project_id` = ?
		  AND t1.`general_config_id` = ?
		  AND t1.`deleted` = ?
	*/

	sb := squirrel.Select(utils.AddTableNameToFields("t1", apiPlanFieldNames)...).
		From(fmt.Sprintf("%s AS t1, %s AS t2", m.table, generalConfigurationTableName)).
		Where("t1.`project_id` = t2.`project_id` AND t1.`general_config_id` = t2.`config_id` AND t1.`deleted` = t2.`deleted`").
		Where(
			"t1.`project_id` = ? AND t1.`general_config_id` = ? AND t1.`deleted` = ?",
			projectId, configId, constants.NotDeleted,
		)

	return m.FindNoCacheByQuery(ctx, sb)
}

func (m *customApiPlanModel) RemoveByPlanId(
	ctx context.Context, session sqlx.Session, projectId, planId string,
) (sql.Result, error) {
	keys := m.getKeysByPlanId(ctx, projectId, planId)

	return m.ExecCtx(
		ctx, func(ctx context.Context, conn sqlx.SqlConn) (sql.Result, error) {
			/*
				SQL:
				UPDATE `api_plan`
				SET `deleted` = ?, `deleted_by` = ?, `deleted_at` = ?
				WHERE `project_id` = ?
				  AND `plan_id` = ?
			*/
			stmt, values, err := squirrel.Update(m.table).
				SetMap(
					squirrel.Eq{
						"`deleted`":    constants.HasDeleted,
						"`deleted_by`": userinfo.FromContext(ctx).Account,
						"`deleted_at`": time.Now(),
					},
				).
				Where("`project_id` = ? AND `plan_id` = ?", projectId, planId).
				ToSql()
			if err != nil {
				return nil, err
			}

			if session != nil {
				return session.ExecCtx(ctx, stmt, values...)
			}
			return conn.ExecCtx(ctx, stmt, values...)
		}, keys...,
	)
}

func (m *customApiPlanModel) getKeysByPlanId(ctx context.Context, projectId, planId string) []string {
	apiPlan, err := m.FindOneByProjectIdPlanId(ctx, projectId, planId)
	if err != nil {
		return make([]string, 0)
	}

	return []string{
		fmt.Sprintf("%s%v", cacheManagerApiPlanIdPrefix, apiPlan.Id),
		fmt.Sprintf("%s%v:%v", cacheManagerApiPlanProjectIdPlanIdPrefix, apiPlan.ProjectId, apiPlan.PlanId),
	}
}

func (m *customApiPlanModel) GenerateApiPlanSqlBuilderForProjectIdAndPlanIds(
	projectId string, planIds ...string,
) squirrel.SelectBuilder {
	selectBuilder := m.SelectBuilder().OrderBy(
		"`id`",
	).Where(
		"`projectId` = ? ", projectId,
	).Where(
		"`plan_id` in ( ? ) ", planIds,
	)
	return selectBuilder
}

func (m *customApiPlanModel) FindCountByCategoryId(
	ctx context.Context, cm CategoryModel, cond FindCountByCategoryCondition,
) (int64, error) {
	var (
		aliasT  = "t"
		aliasT1 = "t1"
	)

	sb := squirrel.Select("COUNT(*)").
		From(m.table+" AS "+aliasT).
		Where(
			fmt.Sprintf("%s.`project_id` = ? AND %s.`deleted` = ?", aliasT, aliasT),
			cond.ProjectId, constants.NotDeleted,
		)

	if cond.DrillDown {
		sub := cm.FindDescendantCategoriesSqlBuilder(
			GetCategoryTreeCondition{
				ProjectId:     cond.ProjectId,
				Type:          common.ConstCategoryTreeTypeApiPlan,
				CategoryId:    cond.CategoryId,
				OnlyDirectory: true,
			},
		)

		sb = sb.JoinClause(
			sub.Prefix("LEFT JOIN (").Suffix(
				fmt.Sprintf(
					") AS %s ON %s.`category_id` = %s.`category_id`", aliasT1, aliasT, aliasT1,
				),
			),
		).Where(fmt.Sprintf("%s.`category_id` IS NOT NULL", aliasT1))
	} else {
		sb = sb.Where(fmt.Sprintf("%s.`category_id` = ?", aliasT), cond.CategoryId)
	}

	return m.FindCount(ctx, sb)
}
