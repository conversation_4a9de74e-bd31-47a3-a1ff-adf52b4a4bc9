package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/Masterminds/squirrel"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	qetconstants "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/types"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/userinfo"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/utils"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
)

var (
	_ InterfaceSchemaReferenceRelationshipModel = (*customInterfaceSchemaReferenceRelationshipModel)(nil)

	interfaceSchemaReferenceRelationshipInsertFields = stringx.Remove(
		interfaceSchemaReferenceRelationshipFieldNames, "`id`", "`create_time`", "`update_time`", "`deleted_by`",
		"`created_at`", "`updated_at`", "`deleted_at`",
	)
)

type (
	// InterfaceSchemaReferenceRelationshipModel is an interface to be customized, add more methods here,
	// and implement the added methods in customInterfaceSchemaReferenceRelationshipModel.
	InterfaceSchemaReferenceRelationshipModel interface {
		interfaceSchemaReferenceRelationshipModel
		types.DBModel

		Trans(ctx context.Context, fn func(context context.Context, session sqlx.Session) error) error
		InsertBuilder(data *InterfaceSchemaReferenceRelationship) squirrel.InsertBuilder
		UpdateBuilder(data *InterfaceSchemaReferenceRelationship) squirrel.UpdateBuilder
		SelectBuilder() squirrel.SelectBuilder
		SelectCountBuilder() squirrel.SelectBuilder
		FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error)
		FindNoCacheByQuery(
			ctx context.Context, builder squirrel.SelectBuilder,
		) ([]*InterfaceSchemaReferenceRelationship, error)

		FindAll(ctx context.Context, projectId string) ([]*InterfaceSchemaReferenceRelationship, error)
		FindReferenceByReference(
			ctx context.Context, projectId, referenceType, referenceId string,
		) ([]*InterfaceSchemaReferenceRelationship, error)
		FindReferenceBySchemaId(
			ctx context.Context, ism InterfaceSchemaModel, idm InterfaceDocumentModel, projectId, schemaId string,
		) ([]*InterfaceSchemaReferenceRelationship, error)
		RemoveByReferenceId(
			ctx context.Context, session sqlx.Session, projectId, referenceType, referenceId string,
		) (sql.Result, error)
	}

	customInterfaceSchemaReferenceRelationshipModel struct {
		*defaultInterfaceSchemaReferenceRelationshipModel
	}
)

// NewInterfaceSchemaReferenceRelationshipModel returns a model for the database table.
func NewInterfaceSchemaReferenceRelationshipModel(
	conn sqlx.SqlConn, c cache.CacheConf,
) InterfaceSchemaReferenceRelationshipModel {
	return &customInterfaceSchemaReferenceRelationshipModel{
		defaultInterfaceSchemaReferenceRelationshipModel: newInterfaceSchemaReferenceRelationshipModel(conn, c),
	}
}

func (m *customInterfaceSchemaReferenceRelationshipModel) Table() string {
	return m.table
}

func (m *customInterfaceSchemaReferenceRelationshipModel) Fields() []string {
	return interfaceSchemaReferenceRelationshipFieldNames
}

func (m *customInterfaceSchemaReferenceRelationshipModel) Trans(
	ctx context.Context, fn func(context context.Context, session sqlx.Session) error,
) error {
	return m.TransactCtx(ctx, fn)
}

func (m *customInterfaceSchemaReferenceRelationshipModel) InsertBuilder(data *InterfaceSchemaReferenceRelationship) squirrel.InsertBuilder {
	return squirrel.Insert(m.table).Columns(interfaceSchemaReferenceRelationshipInsertFields...).Values(
		data.ProjectId, data.ReferenceType, data.ReferenceId, data.SchemaId, data.Deleted, data.CreatedBy,
		data.UpdatedBy,
	)
}

func (m *customInterfaceSchemaReferenceRelationshipModel) UpdateBuilder(data *InterfaceSchemaReferenceRelationship) squirrel.UpdateBuilder {
	eq := squirrel.Eq{
		"`deleted`":    data.Deleted,
		"`updated_by`": data.UpdatedBy,
		"`deleted_by`": data.DeletedBy,
		"`deleted_at`": data.DeletedAt,
	}
	return squirrel.Update(m.table).SetMap(eq).Where("`id` = ?", data.Id)
}

func (m *customInterfaceSchemaReferenceRelationshipModel) SelectBuilder() squirrel.SelectBuilder {
	return squirrel.Select(interfaceSchemaReferenceRelationshipFieldNames...).Where(
		"`deleted` = ?", qetconstants.NotDeleted,
	).From(m.table)
}

func (m *customInterfaceSchemaReferenceRelationshipModel) SelectCountBuilder() squirrel.SelectBuilder {
	return squirrel.Select("count(*)").Where("`deleted` = ?", qetconstants.NotDeleted).From(m.table)
}

func (m *customInterfaceSchemaReferenceRelationshipModel) FindCount(
	ctx context.Context, countBuilder squirrel.SelectBuilder,
) (int64, error) {
	query, values, err := countBuilder.ToSql()
	if err != nil {
		return 0, err
	}

	var resp int64
	err = m.QueryRowNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return 0, err
	}
}

func (m *customInterfaceSchemaReferenceRelationshipModel) FindNoCacheByQuery(
	ctx context.Context, builder squirrel.SelectBuilder,
) ([]*InterfaceSchemaReferenceRelationship, error) {
	query, values, err := builder.ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*InterfaceSchemaReferenceRelationship
	err = m.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

func (m *customInterfaceSchemaReferenceRelationshipModel) FindAll(
	ctx context.Context, projectId string,
) ([]*InterfaceSchemaReferenceRelationship, error) {
	return m.FindNoCacheByQuery(ctx, m.SelectBuilder().Where("`project_id` = ?", projectId))
}

func (m *customInterfaceSchemaReferenceRelationshipModel) FindReferenceByReference(
	ctx context.Context, projectId, referenceType, referenceId string,
) ([]*InterfaceSchemaReferenceRelationship, error) {
	sb := m.SelectBuilder().Where(
		"`project_id` = ? AND `reference_type` = ? AND `reference_id` = ?", projectId, referenceType, referenceId,
	)

	return m.FindNoCacheByQuery(ctx, sb)
}

func (m *customInterfaceSchemaReferenceRelationshipModel) FindReferenceBySchemaId(
	ctx context.Context, ism InterfaceSchemaModel, idm InterfaceDocumentModel, projectId, schemaId string,
) ([]*InterfaceSchemaReferenceRelationship, error) {
	/*
		SQL:
		SELECT t1.*
		FROM `interface_schema_reference_relationship` AS t1
			LEFT JOIN `interface_schema` AS t2
				ON t1.`project_id` = t2.`project_id`
				AND t1.`reference_type` = ?
				AND t1.`reference_id` = t2.`schema_id`
				AND t2.`deleted` = ?
			LEFT JOIN `interface_document` AS t3
				ON t1.`project_id` = t3.`project_id`
				AND t1.`reference_type` = ?
				AND t1.`reference_id` = t3.`document_id`
				AND t3.`deleted` = ?
		WHERE t1.`project_id` = ?
		  AND t1.`schema_id` = ?
		  AND t1.`deleted` = ?
		  AND (t2.`schema_id` IS NOT NULL OR t3.`document_id` IS NOT NULL)
	*/
	sb := squirrel.Select(
		strings.Join(
			utils.AddTableNameToFields("t1", interfaceSchemaReferenceRelationshipFieldNames), ", ",
		),
	).
		From(fmt.Sprintf("%s AS t1", m.table)).
		LeftJoin(
			fmt.Sprintf(
				"%s AS t2 ON t1.`project_id` = t2.`project_id` AND t1.`reference_type` = ? AND t1.`reference_id` = t2.`schema_id` AND t2.`deleted` = ?",
				ism.Table(),
			), common.ConstInterfaceDefinitionTypeSchema, qetconstants.NotDeleted,
		).
		LeftJoin(
			fmt.Sprintf(
				"%s AS t3 ON t1.`project_id` = t3.`project_id` AND t1.`reference_type` = ? AND t1.`reference_id` = t3.`document_id` AND t3.`deleted` = ?",
				idm.Table(),
			), common.ConstInterfaceDefinitionTypeDocument, qetconstants.NotDeleted,
		).
		Where(
			"t1.`project_id` = ? AND t1.`schema_id` = ? AND t1.`deleted` = ? AND (t2.`schema_id` IS NOT NULL OR t3.`document_id` IS NOT NULL)",
			projectId, schemaId, qetconstants.NotDeleted,
		)

	return m.FindNoCacheByQuery(ctx, sb)
}

func (m *customInterfaceSchemaReferenceRelationshipModel) RemoveByReferenceId(
	ctx context.Context, session sqlx.Session, projectId, referenceType, referenceId string,
) (sql.Result, error) {
	keys := m.getKeysByReferenceId(ctx, projectId, referenceType, referenceId)

	return m.ExecCtx(
		ctx, func(ctx context.Context, conn sqlx.SqlConn) (sql.Result, error) {
			/*
				SQL:
				UPDATE `interface_schema_reference_relationship`
				SET `deleted` = ?, `deleted_by` = ?, `deleted_at` = ?
				WHERE `project_id` = ?
				  AND `reference_type` = ?
				  AND `reference_id` = ?
			*/
			stmt, values, err := squirrel.Update(m.table).
				SetMap(
					squirrel.Eq{
						"`deleted`":    qetconstants.HasDeleted,
						"`deleted_by`": userinfo.FromContext(ctx).Account,
						"`deleted_at`": time.Now(),
					},
				).
				Where(
					"`project_id` = ? AND `reference_type` = ? AND `reference_id` = ?",
					projectId, referenceType, referenceId,
				).
				ToSql()
			if err != nil {
				return nil, err
			}

			if session != nil {
				return session.ExecCtx(ctx, stmt, values...)
			}
			return conn.ExecCtx(ctx, stmt, values...)
		}, keys...,
	)
}

func (m *customInterfaceSchemaReferenceRelationshipModel) getKeysByReferenceId(
	ctx context.Context, projectId, referenceType, referenceId string,
) []string {
	cs, err := m.FindReferenceByReference(ctx, projectId, referenceType, referenceId)
	if err != nil {
		return make([]string, 0)
	}

	keys := make([]string, 0, len(cs))
	for _, c := range cs {
		keys = append(keys, fmt.Sprintf("%s%v", cacheManagerInterfaceSchemaReferenceRelationshipIdPrefix, c.Id))
	}

	return keys
}
