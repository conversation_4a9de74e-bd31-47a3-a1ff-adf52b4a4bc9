package model

import (
	"context"

	"github.com/Masterminds/squirrel"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/types"
)

var (
	_ AdvancedSearchFieldRelationshipModel = (*customAdvancedSearchFieldRelationshipModel)(nil)

	advancedSearchFieldRelationshipInsertFields = stringx.Remove(advancedSearchFieldRelationshipFieldNames, "`id`", "`create_time`", "`update_time`", "`deleted_by`", "`created_at`", "`updated_at`", "`deleted_at`")
)

type (
	// AdvancedSearchFieldRelationshipModel is an interface to be customized, add more methods here,
	// and implement the added methods in customAdvancedSearchFieldRelationshipModel.
	AdvancedSearchFieldRelationshipModel interface {
		advancedSearchFieldRelationshipModel
		types.DBModel

		Trans(ctx context.Context, fn func(context context.Context, session sqlx.Session) error) error
		InsertBuilder(data *AdvancedSearchFieldRelationship) squirrel.InsertBuilder
		UpdateBuilder(data *AdvancedSearchFieldRelationship) squirrel.UpdateBuilder
		SelectBuilder() squirrel.SelectBuilder
		SelectCountBuilder() squirrel.SelectBuilder
		FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error)
		FindNoCacheByQuery(ctx context.Context, selectBuilder squirrel.SelectBuilder) ([]*AdvancedSearchFieldRelationship, error)
	}

	customAdvancedSearchFieldRelationshipModel struct {
		*defaultAdvancedSearchFieldRelationshipModel
	}
)

// NewAdvancedSearchFieldRelationshipModel returns a model for the database table.
func NewAdvancedSearchFieldRelationshipModel(conn sqlx.SqlConn, c cache.CacheConf) AdvancedSearchFieldRelationshipModel {
	return &customAdvancedSearchFieldRelationshipModel{
		defaultAdvancedSearchFieldRelationshipModel: newAdvancedSearchFieldRelationshipModel(conn, c),
	}
}

func (m *customAdvancedSearchFieldRelationshipModel) Table() string {
	return m.table
}

func (m *customAdvancedSearchFieldRelationshipModel) Fields() []string {
	return advancedSearchFieldRelationshipFieldNames
}

func (m *customAdvancedSearchFieldRelationshipModel) Trans(ctx context.Context, fn func(context context.Context, session sqlx.Session) error) error {
	return m.TransactCtx(ctx, fn)
}

func (m *customAdvancedSearchFieldRelationshipModel) InsertBuilder(data *AdvancedSearchFieldRelationship) squirrel.InsertBuilder {
	return squirrel.Insert(m.table).Columns(advancedSearchFieldRelationshipInsertFields...).Values(data.FieldId, data.ConditionId)
}

func (m *customAdvancedSearchFieldRelationshipModel) UpdateBuilder(data *AdvancedSearchFieldRelationship) squirrel.UpdateBuilder {
	eq := squirrel.Eq{
		"`field_id`":     data.FieldId,
		"`condition_id`": data.ConditionId,
		"`deleted`":      data.Deleted,
	}
	return squirrel.Update(m.table).SetMap(eq).Where("`id` = ?", data.Id)
}

func (m *customAdvancedSearchFieldRelationshipModel) SelectBuilder() squirrel.SelectBuilder {
	return squirrel.Select(advancedSearchFieldRelationshipFieldNames...).Where("`deleted` = ?", constants.NotDeleted).From(m.table)
}

func (m *customAdvancedSearchFieldRelationshipModel) SelectCountBuilder() squirrel.SelectBuilder {
	return squirrel.Select("count(*)").Where("`deleted` = ?", constants.NotDeleted).From(m.table)
}

func (m *customAdvancedSearchFieldRelationshipModel) FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error) {
	query, values, err := countBuilder.ToSql()
	if err != nil {
		return 0, err
	}

	var resp int64
	err = m.QueryRowNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return 0, err
	}
}

func (m *customAdvancedSearchFieldRelationshipModel) FindNoCacheByQuery(ctx context.Context, selectBuilder squirrel.SelectBuilder) ([]*AdvancedSearchFieldRelationship, error) {
	query, values, err := selectBuilder.ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*AdvancedSearchFieldRelationship
	err = m.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}
