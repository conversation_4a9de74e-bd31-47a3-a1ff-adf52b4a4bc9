package model

import (
	"context"
	"database/sql"
	"fmt"
	"time"

	"github.com/Masterminds/squirrel"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/types"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/userinfo"
)

var (
	_ InterfaceCaseElementModel = (*customInterfaceCaseElementModel)(nil)

	interfaceCaseElementInsertFields = stringx.Remove(
		interfaceCaseElementFieldNames, "`id`", "`create_time`", "`update_time`", "`deleted_by`", "`created_at`",
		"`updated_at`", "`deleted_at`",
	)
)

type (
	// InterfaceCaseElementModel is an interface to be customized, add more methods here,
	// and implement the added methods in customInterfaceCaseElementModel.
	InterfaceCaseElementModel interface {
		interfaceCaseElementModel
		types.DBModel

		Trans(ctx context.Context, fn func(context context.Context, session sqlx.Session) error) error
		InsertBuilder(data *InterfaceCaseElement) squirrel.InsertBuilder
		UpdateBuilder(data *InterfaceCaseElement) squirrel.UpdateBuilder
		SelectBuilder() squirrel.SelectBuilder
		SelectCountBuilder() squirrel.SelectBuilder
		FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error)
		FindNoCacheByQuery(ctx context.Context, builder squirrel.SelectBuilder) ([]*InterfaceCaseElement, error)

		InsertTX(ctx context.Context, session sqlx.Session, data *InterfaceCaseElement) (sql.Result, error)
		UpdateTX(ctx context.Context, session sqlx.Session, data *InterfaceCaseElement) (sql.Result, error)
		RemoveByCaseId(ctx context.Context, session sqlx.Session, projectId, caseId string) (sql.Result, error)
	}

	customInterfaceCaseElementModel struct {
		*defaultInterfaceCaseElementModel
	}
)

// NewInterfaceCaseElementModel returns a model for the database table.
func NewInterfaceCaseElementModel(conn sqlx.SqlConn, c cache.CacheConf) InterfaceCaseElementModel {
	return &customInterfaceCaseElementModel{
		defaultInterfaceCaseElementModel: newInterfaceCaseElementModel(conn, c),
	}
}

func (m *customInterfaceCaseElementModel) Table() string {
	return m.table
}

func (m *customInterfaceCaseElementModel) Fields() []string {
	return interfaceCaseElementFieldNames
}

func (m *customInterfaceCaseElementModel) Trans(
	ctx context.Context, fn func(context context.Context, session sqlx.Session) error,
) error {
	return m.TransactCtx(ctx, fn)
}

func (m *customInterfaceCaseElementModel) InsertBuilder(data *InterfaceCaseElement) squirrel.InsertBuilder {
	return squirrel.Insert(m.table).Columns(interfaceCaseElementInsertFields...).Values(
		data.ProjectId, data.CaseId, data.Version, data.ElementId, data.ElementType, data.Data, data.Deleted,
		data.CreatedBy, data.UpdatedBy,
	)
}

func (m *customInterfaceCaseElementModel) UpdateBuilder(data *InterfaceCaseElement) squirrel.UpdateBuilder {
	eq := squirrel.Eq{
		"`data`":       data.Data,
		"`deleted`":    data.Deleted,
		"`updated_by`": data.UpdatedBy,
		"`deleted_by`": data.DeletedBy,
		"`deleted_at`": data.DeletedAt,
	}
	return squirrel.Update(m.table).SetMap(eq).Where("`id` = ?", data.Id)
}

func (m *customInterfaceCaseElementModel) SelectBuilder() squirrel.SelectBuilder {
	return squirrel.Select(interfaceCaseElementFieldNames...).Where("`deleted` = ?", constants.NotDeleted).From(m.table)
}

func (m *customInterfaceCaseElementModel) SelectCountBuilder() squirrel.SelectBuilder {
	return squirrel.Select("count(*)").Where("`deleted` = ?", constants.NotDeleted).From(m.table)
}

func (m *customInterfaceCaseElementModel) FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (
	int64, error,
) {
	query, values, err := countBuilder.ToSql()
	if err != nil {
		return 0, err
	}

	var resp int64
	err = m.QueryRowNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return 0, err
	}
}

func (m *customInterfaceCaseElementModel) FindNoCacheByQuery(
	ctx context.Context, builder squirrel.SelectBuilder,
) ([]*InterfaceCaseElement, error) {
	query, values, err := builder.ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*InterfaceCaseElement
	err = m.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

func (m *customInterfaceCaseElementModel) InsertTX(
	ctx context.Context, session sqlx.Session, data *InterfaceCaseElement,
) (sql.Result, error) {
	managerInterfaceCaseElementIdKey := fmt.Sprintf("%s%v", cacheManagerInterfaceCaseElementIdPrefix, data.Id)
	managerInterfaceCaseElementProjectIdCaseIdVersionElementIdKey := fmt.Sprintf(
		"%s%v:%v:%v:%v", cacheManagerInterfaceCaseElementProjectIdCaseIdVersionElementIdPrefix, data.ProjectId,
		data.CaseId, data.Version, data.ElementId,
	)

	return m.ExecCtx(
		ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
			query, values, err := m.InsertBuilder(data).ToSql()
			if err != nil {
				return nil, err
			}
			if session != nil {
				return session.ExecCtx(ctx, query, values...)
			}
			return conn.ExecCtx(ctx, query, values...)
		}, managerInterfaceCaseElementIdKey, managerInterfaceCaseElementProjectIdCaseIdVersionElementIdKey,
	)
}

func (m *customInterfaceCaseElementModel) UpdateTX(
	ctx context.Context, session sqlx.Session, data *InterfaceCaseElement,
) (sql.Result, error) {
	managerInterfaceCaseElementIdKey := fmt.Sprintf("%s%v", cacheManagerInterfaceCaseElementIdPrefix, data.Id)
	managerInterfaceCaseElementProjectIdCaseIdVersionElementIdKey := fmt.Sprintf(
		"%s%v:%v:%v:%v", cacheManagerInterfaceCaseElementProjectIdCaseIdVersionElementIdPrefix, data.ProjectId,
		data.CaseId, data.Version, data.ElementId,
	)

	return m.ExecCtx(
		ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
			query, values, err := m.UpdateBuilder(data).ToSql()
			if err != nil {
				return nil, err
			}
			if session != nil {
				return session.ExecCtx(ctx, query, values...)
			}
			return conn.ExecCtx(ctx, query, values...)
		}, managerInterfaceCaseElementIdKey, managerInterfaceCaseElementProjectIdCaseIdVersionElementIdKey,
	)
}

func (m *customInterfaceCaseElementModel) RemoveByCaseId(
	ctx context.Context, session sqlx.Session, projectId, caseId string,
) (sql.Result, error) {
	keys := m.getKeysByCaseId(ctx, projectId, caseId)

	return m.ExecCtx(
		ctx, func(ctx context.Context, conn sqlx.SqlConn) (sql.Result, error) {
			/*
				SQL:
				UPDATE `api_case_element`
				SET `deleted` = 1, `deleted_by` = ?, `deleted_at` = ?
				WHERE `project_id` = ?
				  AND `case_id` = ?;
			*/
			stmt, values, err := squirrel.Update(m.table).
				SetMap(
					squirrel.Eq{
						"`deleted`":    constants.HasDeleted,
						"`deleted_by`": userinfo.FromContext(ctx).Account,
						"`deleted_at`": time.Now(),
					},
				).
				Where("`project_id` = ? AND `case_id` = ?", projectId, caseId).
				ToSql()
			if err != nil {
				return nil, err
			}

			if session != nil {
				return session.ExecCtx(ctx, stmt, values...)
			}
			return conn.ExecCtx(ctx, stmt, values...)
		}, keys...,
	)
}

func (m *customInterfaceCaseElementModel) getKeysByCaseId(ctx context.Context, projectId, caseId string) []string {
	sb := m.SelectBuilder().Where("`project_id` = ? AND `case_id` = ?", projectId, caseId)
	cs, err := m.FindNoCacheByQuery(ctx, sb)
	if err != nil {
		return make([]string, 0)
	}

	keys := make([]string, 0, len(cs)*2)
	for _, c := range cs {
		keys = append(
			keys,
			fmt.Sprintf("%s%v", cacheManagerInterfaceCaseElementIdPrefix, c.Id),
			fmt.Sprintf(
				"%s%v:%v:%v:%v", cacheManagerInterfaceCaseElementProjectIdCaseIdVersionElementIdPrefix, c.ProjectId,
				c.CaseId, c.Version, c.ElementId,
			),
		)
	}

	return keys
}
