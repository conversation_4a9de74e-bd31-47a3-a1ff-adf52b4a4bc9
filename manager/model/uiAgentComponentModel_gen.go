// Code generated by goctl. DO NOT EDIT.
// versions:
//  goctl version: 1.8.3

package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlc"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
)

var (
	uiAgentComponentTableName           = "`ui_agent_component`"
	uiAgentComponentFieldNames          = builder.RawFieldNames(&UiAgentComponent{})
	uiAgentComponentRows                = strings.Join(uiAgentComponentFieldNames, ",")
	uiAgentComponentRowsExpectAutoSet   = strings.Join(stringx.Remove(uiAgentComponentFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), ",")
	uiAgentComponentRowsWithPlaceHolder = strings.Join(stringx.Remove(uiAgentComponentFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), "=?,") + "=?"

	cacheManagerUiAgentComponentIdPrefix                   = "cache:manager:uiAgentComponent:id:"
	cacheManagerUiAgentComponentProjectIdComponentIdPrefix = "cache:manager:uiAgentComponent:projectId:componentId:"
)

type (
	uiAgentComponentModel interface {
		Insert(ctx context.Context, session sqlx.Session, data *UiAgentComponent) (sql.Result, error)
		FindOne(ctx context.Context, id int64) (*UiAgentComponent, error)
		FindOneByProjectIdComponentId(ctx context.Context, projectId string, componentId string) (*UiAgentComponent, error)
		Update(ctx context.Context, session sqlx.Session, data *UiAgentComponent) (sql.Result, error)
		Delete(ctx context.Context, session sqlx.Session, id int64) error
		LogicDelete(ctx context.Context, session sqlx.Session, id int64) error
	}

	defaultUiAgentComponentModel struct {
		sqlc.CachedConn
		table string
	}

	UiAgentComponent struct {
		Id               int64          `db:"id"`                 // 自增ID
		ProjectId        string         `db:"project_id"`         // 项目ID
		CategoryId       string         `db:"category_id"`        // 所属分类ID
		ComponentId      string         `db:"component_id"`       // 组件ID
		Name             string         `db:"name"`               // 组件名称
		Description      sql.NullString `db:"description"`        // 组件描述
		State            int64          `db:"state"`              // 组件状态（生效、失效）
		Tags             sql.NullString `db:"tags"`               // 标签
		PlatformType     int64          `db:"platform_type"`      // 平台类型（Android、IOS）
		ApplicationId    string         `db:"application_id"`     // 应用配置ID
		StepByStep       int64          `db:"step_by_step"`       // 是否分步执行
		Steps            string         `db:"steps"`              // 步骤
		Expectation      sql.NullString `db:"expectation"`        // 期望结果
		Variables        string         `db:"variables"`          // 变量列表
		LatestExecutedAt sql.NullTime   `db:"latest_executed_at"` // 最近一次执行时间
		LatestResult     int64          `db:"latest_result"`      // 状态（未执行、成功、失败）
		Deleted          int64          `db:"deleted"`            // 逻辑删除标识（未删除、已删除）
		MaintainedBy     sql.NullString `db:"maintained_by"`      // 维护者的用户ID
		CreatedBy        string         `db:"created_by"`         // 创建者的用户ID
		UpdatedBy        string         `db:"updated_by"`         // 最近一次更新者的用户ID
		DeletedBy        sql.NullString `db:"deleted_by"`         // 删除者的用户ID
		CreatedAt        time.Time      `db:"created_at"`         // 创建时间
		UpdatedAt        time.Time      `db:"updated_at"`         // 更新时间
		DeletedAt        sql.NullTime   `db:"deleted_at"`         // 删除时间
	}
)

func newUiAgentComponentModel(conn sqlx.SqlConn, c cache.CacheConf, opts ...cache.Option) *defaultUiAgentComponentModel {
	return &defaultUiAgentComponentModel{
		CachedConn: sqlc.NewConn(conn, c, opts...),
		table:      "`ui_agent_component`",
	}
}

func (m *defaultUiAgentComponentModel) Delete(ctx context.Context, session sqlx.Session, id int64) error {
	data, err := m.FindOne(ctx, id)
	if err != nil {
		return err
	}

	managerUiAgentComponentIdKey := fmt.Sprintf("%s%v", cacheManagerUiAgentComponentIdPrefix, id)
	managerUiAgentComponentProjectIdComponentIdKey := fmt.Sprintf("%s%v:%v", cacheManagerUiAgentComponentProjectIdComponentIdPrefix, data.ProjectId, data.ComponentId)
	_, err = m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("delete from %s where `id` = ?", m.table)
		if session != nil {
			return session.ExecCtx(ctx, query, id)
		}
		return conn.ExecCtx(ctx, query, id)
	}, managerUiAgentComponentIdKey, managerUiAgentComponentProjectIdComponentIdKey)
	return err
}

func (m *defaultUiAgentComponentModel) LogicDelete(ctx context.Context, session sqlx.Session, id int64) error {
	data, err := m.FindOne(ctx, id)
	if err != nil {
		return err
	}

	managerUiAgentComponentIdKey := fmt.Sprintf("%s%v", cacheManagerUiAgentComponentIdPrefix, id)
	managerUiAgentComponentProjectIdComponentIdKey := fmt.Sprintf("%s%v:%v", cacheManagerUiAgentComponentProjectIdComponentIdPrefix, data.ProjectId, data.ComponentId)
	_, err = m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set `deleted` = ?, `deleted_at` = ? where `id` = ?", m.table)
		if session != nil {
			return session.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
		}
		return conn.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
	}, managerUiAgentComponentIdKey, managerUiAgentComponentProjectIdComponentIdKey)
	return err
}

func (m *defaultUiAgentComponentModel) FindOne(ctx context.Context, id int64) (*UiAgentComponent, error) {
	managerUiAgentComponentIdKey := fmt.Sprintf("%s%v", cacheManagerUiAgentComponentIdPrefix, id)
	var resp UiAgentComponent
	err := m.QueryRowCtx(ctx, &resp, managerUiAgentComponentIdKey, func(ctx context.Context, conn sqlx.SqlConn, v any) error {
		query := fmt.Sprintf("select %s from %s where `id` = ? and `deleted` = ? limit 1", uiAgentComponentRows, m.table)
		return conn.QueryRowCtx(ctx, v, query, id, constants.NotDeleted)
	})
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultUiAgentComponentModel) FindOneByProjectIdComponentId(ctx context.Context, projectId string, componentId string) (*UiAgentComponent, error) {
	managerUiAgentComponentProjectIdComponentIdKey := fmt.Sprintf("%s%v:%v", cacheManagerUiAgentComponentProjectIdComponentIdPrefix, projectId, componentId)
	var resp UiAgentComponent
	err := m.QueryRowIndexCtx(ctx, &resp, managerUiAgentComponentProjectIdComponentIdKey, m.formatPrimary, func(ctx context.Context, conn sqlx.SqlConn, v any) (i any, e error) {
		query := fmt.Sprintf("select %s from %s where `project_id` = ? and `component_id` = ? and `deleted` = ? limit 1", uiAgentComponentRows, m.table)
		if err := conn.QueryRowCtx(ctx, &resp, query, projectId, componentId, constants.NotDeleted); err != nil {
			return nil, err
		}
		return resp.Id, nil
	}, m.queryPrimary)
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultUiAgentComponentModel) Insert(ctx context.Context, session sqlx.Session, data *UiAgentComponent) (sql.Result, error) {
	managerUiAgentComponentIdKey := fmt.Sprintf("%s%v", cacheManagerUiAgentComponentIdPrefix, data.Id)
	managerUiAgentComponentProjectIdComponentIdKey := fmt.Sprintf("%s%v:%v", cacheManagerUiAgentComponentProjectIdComponentIdPrefix, data.ProjectId, data.ComponentId)

	return m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", m.table, uiAgentComponentRowsExpectAutoSet)
		if session != nil {
			return session.ExecCtx(ctx, query, data.ProjectId, data.CategoryId, data.ComponentId, data.Name, data.Description, data.State, data.Tags, data.PlatformType, data.ApplicationId, data.StepByStep, data.Steps, data.Expectation, data.Variables, data.LatestExecutedAt, data.LatestResult, data.Deleted, data.MaintainedBy, data.CreatedBy, data.UpdatedBy, data.DeletedBy)
		}
		return conn.ExecCtx(ctx, query, data.ProjectId, data.CategoryId, data.ComponentId, data.Name, data.Description, data.State, data.Tags, data.PlatformType, data.ApplicationId, data.StepByStep, data.Steps, data.Expectation, data.Variables, data.LatestExecutedAt, data.LatestResult, data.Deleted, data.MaintainedBy, data.CreatedBy, data.UpdatedBy, data.DeletedBy)
	}, managerUiAgentComponentIdKey, managerUiAgentComponentProjectIdComponentIdKey)
}

func (m *defaultUiAgentComponentModel) Update(ctx context.Context, session sqlx.Session, newData *UiAgentComponent) (sql.Result, error) {
	data, err := m.FindOne(ctx, newData.Id)
	if err != nil {
		return nil, err
	}

	managerUiAgentComponentIdKey := fmt.Sprintf("%s%v", cacheManagerUiAgentComponentIdPrefix, data.Id)
	managerUiAgentComponentProjectIdComponentIdKey := fmt.Sprintf("%s%v:%v", cacheManagerUiAgentComponentProjectIdComponentIdPrefix, data.ProjectId, data.ComponentId)

	return m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, uiAgentComponentRowsWithPlaceHolder)
		if session != nil {
			return session.ExecCtx(ctx, query, newData.ProjectId, newData.CategoryId, newData.ComponentId, newData.Name, newData.Description, newData.State, newData.Tags, newData.PlatformType, newData.ApplicationId, newData.StepByStep, newData.Steps, newData.Expectation, newData.Variables, newData.LatestExecutedAt, newData.LatestResult, newData.Deleted, newData.MaintainedBy, newData.CreatedBy, newData.UpdatedBy, newData.DeletedBy, newData.Id)
		}
		return conn.ExecCtx(ctx, query, newData.ProjectId, newData.CategoryId, newData.ComponentId, newData.Name, newData.Description, newData.State, newData.Tags, newData.PlatformType, newData.ApplicationId, newData.StepByStep, newData.Steps, newData.Expectation, newData.Variables, newData.LatestExecutedAt, newData.LatestResult, newData.Deleted, newData.MaintainedBy, newData.CreatedBy, newData.UpdatedBy, newData.DeletedBy, newData.Id)
	}, managerUiAgentComponentIdKey, managerUiAgentComponentProjectIdComponentIdKey)
}

func (m *defaultUiAgentComponentModel) formatPrimary(primary any) string {
	return fmt.Sprintf("%s%v", cacheManagerUiAgentComponentIdPrefix, primary)
}

func (m *defaultUiAgentComponentModel) queryPrimary(ctx context.Context, conn sqlx.SqlConn, v, primary any) error {
	query := fmt.Sprintf("select %s from %s where `id` = ? limit 1", uiAgentComponentRows, m.table)
	return conn.QueryRowCtx(ctx, v, query, primary)
}

func (m *defaultUiAgentComponentModel) tableName() string {
	return m.table
}
