package model

import (
	"context"
	"database/sql"
	"fmt"
	"time"

	"github.com/Masterminds/squirrel"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/types"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/userinfo"
)

var (
	_ PerfStopRuleReferenceRelationshipModel = (*customPerfStopRuleReferenceRelationshipModel)(nil)

	perfStopRuleReferenceRelationshipInsertFields = stringx.Remove(
		perfStopRuleReferenceRelationshipFieldNames, "`id`", "`create_time`", "`update_time`", "`deleted_by`",
		"`created_at`", "`updated_at`", "`deleted_at`",
	)
)

type (
	// PerfStopRuleReferenceRelationshipModel is an interface to be customized, add more methods here,
	// and implement the added methods in customPerfStopRuleReferenceRelationshipModel.
	PerfStopRuleReferenceRelationshipModel interface {
		perfStopRuleReferenceRelationshipModel
		types.DBModel

		Trans(ctx context.Context, fn func(context context.Context, session sqlx.Session) error) error
		InsertBuilder(data *PerfStopRuleReferenceRelationship) squirrel.InsertBuilder
		UpdateBuilder(data *PerfStopRuleReferenceRelationship) squirrel.UpdateBuilder
		SelectBuilder() squirrel.SelectBuilder
		SelectCountBuilder() squirrel.SelectBuilder
		FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error)
		FindNoCacheByQuery(
			ctx context.Context, selectBuilder squirrel.SelectBuilder,
		) ([]*PerfStopRuleReferenceRelationship, error)

		FindByProjectID(ctx context.Context, projectID string) ([]*PerfStopRuleReferenceRelationship, error)
		FindByRuleID(ctx context.Context, projectID, ruleID string) ([]*PerfStopRuleReferenceRelationship, error)
		FindByReference(
			ctx context.Context, projectID, referenceType, referenceID string,
		) ([]*PerfStopRuleReferenceRelationship, error)
		RemoveByReference(
			ctx context.Context, session sqlx.Session, projectID, referenceType, referenceID string,
		) (sql.Result, error)
		DeleteByRuleID(ctx context.Context, session sqlx.Session, projectID, ruleID string) (sql.Result, error)
	}

	customPerfStopRuleReferenceRelationshipModel struct {
		*defaultPerfStopRuleReferenceRelationshipModel

		conn sqlx.SqlConn
	}
)

// NewPerfStopRuleReferenceRelationshipModel returns a model for the database table.
func NewPerfStopRuleReferenceRelationshipModel(
	conn sqlx.SqlConn, c cache.CacheConf, opts ...cache.Option,
) PerfStopRuleReferenceRelationshipModel {
	return &customPerfStopRuleReferenceRelationshipModel{
		defaultPerfStopRuleReferenceRelationshipModel: newPerfStopRuleReferenceRelationshipModel(conn, c, opts...),
		conn: conn,
	}
}

func (m *customPerfStopRuleReferenceRelationshipModel) Table() string {
	return m.table
}

func (m *customPerfStopRuleReferenceRelationshipModel) Fields() []string {
	return perfStopRuleReferenceRelationshipFieldNames
}

func (m *customPerfStopRuleReferenceRelationshipModel) Trans(
	ctx context.Context, fn func(context context.Context, session sqlx.Session) error,
) error {
	return m.TransactCtx(ctx, fn)
}

func (m *customPerfStopRuleReferenceRelationshipModel) InsertBuilder(data *PerfStopRuleReferenceRelationship) squirrel.InsertBuilder {
	return squirrel.Insert(m.table).Columns(perfStopRuleReferenceRelationshipInsertFields...).Values(
		data.ProjectId, data.ReferenceType, data.ReferenceId, data.RuleId, data.Deleted, data.CreatedBy, data.UpdatedBy,
	)
}

func (m *customPerfStopRuleReferenceRelationshipModel) UpdateBuilder(data *PerfStopRuleReferenceRelationship) squirrel.UpdateBuilder {
	eq := squirrel.Eq{
		"`deleted`":    data.Deleted,
		"`updated_by`": data.UpdatedBy,
		"`deleted_by`": data.DeletedBy,
		"`deleted_at`": data.DeletedAt,
	}
	return squirrel.Update(m.table).SetMap(eq).Where("`id` = ?", data.Id)
}

func (m *customPerfStopRuleReferenceRelationshipModel) SelectBuilder() squirrel.SelectBuilder {
	return squirrel.Select(perfStopRuleReferenceRelationshipFieldNames...).Where(
		"`deleted` = ?", constants.NotDeleted,
	).From(m.table)
}

func (m *customPerfStopRuleReferenceRelationshipModel) SelectCountBuilder() squirrel.SelectBuilder {
	return squirrel.Select("count(*)").Where("`deleted` = ?", constants.NotDeleted).From(m.table)
}

func (m *customPerfStopRuleReferenceRelationshipModel) FindCount(
	ctx context.Context, countBuilder squirrel.SelectBuilder,
) (int64, error) {
	query, values, err := countBuilder.ToSql()
	if err != nil {
		return 0, err
	}

	var resp int64
	err = m.QueryRowNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return 0, err
	}
}

func (m *customPerfStopRuleReferenceRelationshipModel) FindNoCacheByQuery(
	ctx context.Context, selectBuilder squirrel.SelectBuilder,
) ([]*PerfStopRuleReferenceRelationship, error) {
	query, values, err := selectBuilder.ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*PerfStopRuleReferenceRelationship
	err = m.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

func (m *customPerfStopRuleReferenceRelationshipModel) FindByProjectID(
	ctx context.Context, projectID string,
) ([]*PerfStopRuleReferenceRelationship, error) {
	return m.FindNoCacheByQuery(ctx, m.SelectBuilder().Where("`project_id` = ?", projectID))
}

func (m *customPerfStopRuleReferenceRelationshipModel) FindByRuleID(
	ctx context.Context, projectID, ruleID string,
) ([]*PerfStopRuleReferenceRelationship, error) {
	return m.FindNoCacheByQuery(ctx, m.SelectBuilder().Where("`project_id` = ? AND `rule_id` = ?", projectID, ruleID))
}

func (m *customPerfStopRuleReferenceRelationshipModel) FindByReference(
	ctx context.Context, projectID, referenceType, referenceID string,
) ([]*PerfStopRuleReferenceRelationship, error) {
	return m.FindNoCacheByQuery(
		ctx, m.SelectBuilder().
			Where(
				"`project_id` = ? AND `reference_type` = ? AND `reference_id` = ?",
				projectID, referenceType, referenceID,
			),
	)
}

func (m *customPerfStopRuleReferenceRelationshipModel) RemoveByReference(
	ctx context.Context, session sqlx.Session, projectID, referenceType, referenceID string,
) (sql.Result, error) {
	keys := m.getKeysByReference(ctx, projectID, referenceType, referenceID)

	return m.ExecCtx(
		ctx, func(ctx context.Context, conn sqlx.SqlConn) (sql.Result, error) {
			/*
				SQL:
				UPDATE `perf_stop_rule_reference_relationship`
				SET `deleted` = ?, `deleted_by` = ?, `deleted_at` = ?
				WHERE `project_id` = ?
				  AND `reference_type` = ?
				  AND `reference_id` = ?
			*/
			stmt, values, err := squirrel.Update(m.table).
				SetMap(
					squirrel.Eq{
						"`deleted`":    constants.HasDeleted,
						"`deleted_by`": userinfo.FromContext(ctx).Account,
						"`deleted_at`": time.Now(),
					},
				).
				Where(
					"`project_id` = ? AND `reference_type` = ? AND `reference_id` = ?",
					projectID, referenceType, referenceID,
				).
				ToSql()
			if err != nil {
				return nil, err
			}

			if session != nil {
				return session.ExecCtx(ctx, stmt, values...)
			}
			return conn.ExecCtx(ctx, stmt, values...)
		}, keys...,
	)
}

func (m *customPerfStopRuleReferenceRelationshipModel) getKeysByReference(
	ctx context.Context, projectID, referenceType, referenceID string,
) []string {
	cs, err := m.FindByReference(ctx, projectID, referenceType, referenceID)
	if err != nil {
		return make([]string, 0)
	}

	keys := make([]string, 0, len(cs))
	for _, c := range cs {
		keys = append(keys, fmt.Sprintf("%s%v", cacheManagerPerfStopRuleReferenceRelationshipIdPrefix, c.Id))
	}

	return keys
}

func (m *customPerfStopRuleReferenceRelationshipModel) DeleteByRuleID(
	ctx context.Context, session sqlx.Session, projectID, ruleID string,
) (sql.Result, error) {
	keys := m.getKeysByRuleID(ctx, projectID, ruleID)

	return m.ExecCtx(
		ctx, func(ctx context.Context, conn sqlx.SqlConn) (sql.Result, error) {
			/*
				SQL:
				DELETE FROM `perf_stop_rule` WHERE `project_id` = ? AND `rule_id` = ?
			*/

			stmt, values, err := squirrel.Delete(m.table).
				Where("`project_id` = ? AND `rule_id` = ?", projectID, ruleID).
				ToSql()
			if err != nil {
				return nil, err
			}

			if session != nil {
				return session.ExecCtx(ctx, stmt, values...)
			}
			return conn.ExecCtx(ctx, stmt, values...)
		}, keys...,
	)
}

func (m *customPerfStopRuleReferenceRelationshipModel) getKeysByRuleID(
	ctx context.Context, projectID, ruleID string,
) []string {
	cs, err := m.FindByRuleID(ctx, projectID, ruleID)
	if err != nil {
		return make([]string, 0)
	}

	keys := make([]string, 0, len(cs))
	for _, c := range cs {
		keys = append(
			keys,
			fmt.Sprintf("%s%v", cacheManagerPerfStopRuleReferenceRelationshipIdPrefix, c.Id),
		)
	}

	return keys
}
