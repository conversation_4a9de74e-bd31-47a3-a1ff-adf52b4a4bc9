package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/Masterminds/squirrel"
	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/types"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/userinfo"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/utils"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
)

var (
	_ TagReferenceRelationshipModel = (*customTagReferenceRelationshipModel)(nil)

	tagReferenceRelationshipInsertFields = stringx.Remove(
		tagReferenceRelationshipFieldNames, "`id`", "`create_time`", "`update_time`", "`deleted_by`", "`created_at`",
		"`updated_at`", "`deleted_at`",
	)
)

type (
	// TagReferenceRelationshipModel is an interface to be customized, add more methods here,
	// and implement the added methods in customTagReferenceRelationshipModel.
	TagReferenceRelationshipModel interface {
		tagReferenceRelationshipModel
		types.DBModel

		Trans(ctx context.Context, fn func(context context.Context, session sqlx.Session) error) error
		InsertBuilder(data *TagReferenceRelationship) squirrel.InsertBuilder
		UpdateBuilder(data *TagReferenceRelationship) squirrel.UpdateBuilder
		SelectBuilder() squirrel.SelectBuilder
		SelectCountBuilder() squirrel.SelectBuilder
		FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error)
		FindNoCacheByQuery(ctx context.Context, builder squirrel.SelectBuilder) ([]*TagReferenceRelationship, error)

		FindAll(ctx context.Context, projectId string) ([]*TagReferenceRelationship, error)
		FindReferenceByReference(
			ctx context.Context, projectId, referenceType, referenceId, referenceVersion string,
		) ([]*TagReferenceRelationship, error)
		FindReferenceByTagId(ctx context.Context, projectId, tagId string) ([]*TagReferenceRelationship, error)
		RemoveByReferenceId(
			ctx context.Context, session sqlx.Session, projectId, referenceType, referenceId, referenceVersion string,
		) (sql.Result, error)
	}

	customTagReferenceRelationshipModel struct {
		*defaultTagReferenceRelationshipModel
	}
)

// NewTagReferenceRelationshipModel returns a model for the database table.
func NewTagReferenceRelationshipModel(conn sqlx.SqlConn, c cache.CacheConf) TagReferenceRelationshipModel {
	return &customTagReferenceRelationshipModel{
		defaultTagReferenceRelationshipModel: newTagReferenceRelationshipModel(conn, c),
	}
}

func (m *customTagReferenceRelationshipModel) Table() string {
	return m.table
}

func (m *customTagReferenceRelationshipModel) Fields() []string {
	return tagReferenceRelationshipFieldNames
}

func (m *customTagReferenceRelationshipModel) Trans(
	ctx context.Context, fn func(context context.Context, session sqlx.Session) error,
) error {
	return m.TransactCtx(ctx, fn)
}

func (m *customTagReferenceRelationshipModel) InsertBuilder(data *TagReferenceRelationship) squirrel.InsertBuilder {
	return squirrel.Insert(m.table).Columns(tagReferenceRelationshipInsertFields...).Values(
		data.ProjectId, data.ReferenceType, data.ReferenceId, data.ReferenceVersion, data.TagId, data.Deleted,
		data.CreatedBy, data.UpdatedBy,
	)
}

func (m *customTagReferenceRelationshipModel) UpdateBuilder(data *TagReferenceRelationship) squirrel.UpdateBuilder {
	eq := squirrel.Eq{
		"`deleted`":    data.Deleted,
		"`updated_by`": data.UpdatedBy,
		"`deleted_by`": data.DeletedBy,
		"`deleted_at`": data.DeletedAt,
	}
	return squirrel.Update(m.table).SetMap(eq).Where("`id` = ?", data.Id)
}

func (m *customTagReferenceRelationshipModel) SelectBuilder() squirrel.SelectBuilder {
	return squirrel.Select(tagReferenceRelationshipFieldNames...).Where(
		"`deleted` = ?", constants.NotDeleted,
	).From(m.table)
}

func (m *customTagReferenceRelationshipModel) SelectCountBuilder() squirrel.SelectBuilder {
	return squirrel.Select("count(*)").Where("`deleted` = ?", constants.NotDeleted).From(m.table)
}

func (m *customTagReferenceRelationshipModel) FindCount(
	ctx context.Context, countBuilder squirrel.SelectBuilder,
) (int64, error) {
	query, values, err := countBuilder.ToSql()
	if err != nil {
		return 0, err
	}

	var resp int64
	err = m.QueryRowNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return 0, err
	}
}

func (m *customTagReferenceRelationshipModel) FindNoCacheByQuery(
	ctx context.Context, builder squirrel.SelectBuilder,
) ([]*TagReferenceRelationship, error) {
	query, values, err := builder.ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*TagReferenceRelationship
	err = m.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

func (m *customTagReferenceRelationshipModel) FindAll(
	ctx context.Context, projectId string,
) ([]*TagReferenceRelationship, error) {
	return m.FindNoCacheByQuery(ctx, m.SelectBuilder().Where("`project_id` = ?", projectId))
}

func (m *customTagReferenceRelationshipModel) FindReferenceByReference(
	ctx context.Context, projectId, referenceType, referenceId, referenceVersion string,
) ([]*TagReferenceRelationship, error) {
	var sb squirrel.SelectBuilder

	if referenceVersion == "" {
		// 引用版本为空，则无需区分引用类型来确定执行的SQL
		sb = m.SelectBuilder().Where(
			"`project_id` = ? AND `reference_type` = ? AND `reference_id` = ?", projectId, referenceType, referenceId,
		)
	} else {
		// 引用版本非空，则根据引用类型来确定执行的SQL
		switch referenceType {
		case common.ConstReferenceTypeComponentGroup, common.ConstReferenceTypeApiCase, common.ConstReferenceTypeInterfaceCase:
			sb = m.SelectBuilder().Where(
				"`project_id` = ? AND `reference_type` = ? AND `reference_id` = ? AND `reference_version` = ?",
				projectId, referenceType, referenceId, referenceVersion,
			)
		case common.ConstReferenceTypeApiSuite, common.ConstReferenceTypeApiPlan, common.ConstReferenceTypeInterfaceDocument:
			sb = m.SelectBuilder().Where(
				"`project_id` = ? AND `reference_type` = ? AND `reference_id` = ?",
				projectId, referenceType, referenceId,
			)
		default:
			return nil, errors.WithStack(
				errorx.Err(
					errorx.DoesNotSupport, fmt.Sprintf("the tag reference type[%s] doesn't support", referenceType),
				),
			)
		}
	}

	return m.FindNoCacheByQuery(ctx, sb)
}

func (m *customTagReferenceRelationshipModel) FindReferenceByTagId(
	ctx context.Context, projectId, tagId string,
) ([]*TagReferenceRelationship, error) {
	/*
		SQL:
		SELECT t1.* FROM `tag_reference_relationship` AS t1
		LEFT JOIN `tag` AS t2 ON t1.project_id = t2.project_id AND t2.type = 'COMPONENT_GROUP' AND t1.tag_id = t2.tag_id AND t1.deleted = t2.deleted
		LEFT JOIN `component_group` AS t3 ON t1.project_id = t3.project_id AND t1.reference_type = 'COMPONENT_GROUP' AND t1.reference_id = t3.component_group_id AND t1.reference_version = t3.version AND t1.deleted = t3.deleted
		WHERE t1.project_id = ? AND t1.tag_id = ? AND t1.deleted = ?
		UNION
		SELECT t1.* FROM `tag_reference_relationship` AS t1
		LEFT JOIN `tag` AS t2 ON t1.project_id = t2.project_id AND t2.type = 'CASE' AND t1.tag_id = t2.tag_id AND t1.deleted = t2.deleted
		LEFT JOIN `api_case` AS t3 ON t1.project_id = t3.project_id AND t1.reference_type = 'API_CASE' AND t1.reference_id = t3.case_id AND t1.reference_version = t3.version AND t1.deleted = t3.deleted
		WHERE t1.project_id = ? AND t1.tag_id = ? AND t1.deleted = ?
		UNION
		SELECT t1.* FROM `tag_reference_relationship` AS t1
		LEFT JOIN `tag` AS t2 ON t1.project_id = t2.project_id AND t2.type = 'SUITE' AND t1.tag_id = t2.tag_id AND t1.deleted = t2.deleted
		LEFT JOIN `api_suite` AS t3 ON t1.project_id = t3.project_id AND t1.reference_type = 'API_SUITE' AND t1.reference_id = t3.suite_id AND t1.deleted = t3.deleted
		WHERE t1.project_id = ? AND t1.tag_id = ? AND t1.deleted = ?
		UNION
		SELECT t1.* FROM `tag_reference_relationship` AS t1
		LEFT JOIN `tag` AS t2 ON t1.project_id = t2.project_id AND t2.type = 'PLAN' AND t1.tag_id = t2.tag_id AND t1.deleted = t2.deleted
		LEFT JOIN `api_plan` AS t3 ON t1.project_id = t3.project_id AND t1.reference_type = 'API_PLAN' AND t1.reference_id = t3.plan_id AND t1.deleted = t3.deleted
		WHERE t1.project_id = ? AND t1.tag_id = ? AND t1.deleted = ?
		UNION
		SELECT t1.* FROM `tag_reference_relationship` AS t1
		LEFT JOIN `tag` AS t2 ON t1.project_id = t2.project_id AND t2.type = 'INTERFACE_DOCUMENT' AND t1.tag_id = t2.tag_id AND t1.deleted = t2.deleted
		LEFT JOIN `interface_document` AS t3 ON t1.project_id = t3.project_id AND t1.reference_type = 'INTERFACE_DOCUMENT' AND t1.reference_id = t3.document_id AND t1.deleted = t3.deleted
		WHERE t1.project_id = ? AND t1.tag_id = ? AND t1.deleted = ?
		UNION
		SELECT t1.* FROM `tag_reference_relationship` AS t1
		LEFT JOIN `tag` AS t2 ON t1.project_id = t2.project_id AND t2.type = 'CASE' AND t1.tag_id = t2.tag_id AND t1.deleted = t2.deleted
		LEFT JOIN `interface_case` AS t3 ON t1.project_id = t3.project_id AND t1.reference_type = 'INTERFACE_CASE' AND t1.reference_id = t3.case_id AND t1.reference_version = t3.version AND t1.deleted = t3.deleted
		WHERE t1.project_id = ? AND t1.tag_id = ? AND t1.deleted = ?;
	*/
	selectFieldsStr := strings.Join(utils.AddTableNameToFields("t1", tagReferenceRelationshipFieldNames), ", ")
	tagLeftJoinOnStr := "t1.`project_id` = t2.`project_id` AND t2.`type` = ? AND t1.`tag_id` = t2.`tag_id` AND t1.`deleted` = t2.`deleted`"
	selectWhereStr := "t1.project_id = ? AND t1.tag_id = ? AND t1.deleted = ?"

	// `component_group`
	componentGroupLeftJoinOnStr := "t1.`project_id` = t3.`project_id` AND t1.`reference_type` = ? AND t1.`reference_id` = t3.`component_group_id` AND t1.`reference_version` = t3.`version` AND t1.`deleted` = t3.`deleted`"
	componentGroupQueryStr := fmt.Sprintf(
		"SELECT %s FROM %s AS t1 LEFT JOIN `tag` AS t2 ON %s LEFT JOIN `component_group` AS t3 ON %s WHERE %s",
		selectFieldsStr, m.table, tagLeftJoinOnStr, componentGroupLeftJoinOnStr, selectWhereStr,
	)

	// `api_case`
	apiCaseLeftJoinOnStr := "t1.`project_id` = t3.`project_id` AND t1.`reference_type` = ? AND t1.`reference_id` = t3.`case_id` AND t1.`reference_version` = t3.`version` AND t1.`deleted` = t3.`deleted`"
	apiCaseQueryStr := fmt.Sprintf(
		"SELECT %s FROM %s AS t1 LEFT JOIN `tag` AS t2 ON %s LEFT JOIN `api_case` AS t3 ON %s WHERE %s",
		selectFieldsStr, m.table, tagLeftJoinOnStr, apiCaseLeftJoinOnStr, selectWhereStr,
	)

	// `api_suite`
	apiSuiteLeftJoinOnStr := "t1.`project_id` = t3.`project_id` AND t1.`reference_type` = ? AND t1.`reference_id` = t3.`suite_id` AND t1.`deleted` = t3.`deleted`"
	apiSuiteQueryStr := fmt.Sprintf(
		"SELECT %s FROM %s AS t1 LEFT JOIN `tag` AS t2 ON %s LEFT JOIN `api_suite` AS t3 ON %s WHERE %s",
		selectFieldsStr, m.table, tagLeftJoinOnStr, apiSuiteLeftJoinOnStr, selectWhereStr,
	)

	// `api_plan`
	apiPlanLeftJoinOnStr := "t1.`project_id` = t3.`project_id` AND t1.`reference_type` = ? AND t1.`reference_id` = t3.`plan_id` AND t1.`deleted` = t3.`deleted`"
	apiPlanQueryStr := fmt.Sprintf(
		"SELECT %s FROM %s AS t1 LEFT JOIN `tag` AS t2 ON %s LEFT JOIN `api_plan` AS t3 ON %s WHERE %s",
		selectFieldsStr, m.table, tagLeftJoinOnStr, apiPlanLeftJoinOnStr, selectWhereStr,
	)

	// `interface_document`
	interfaceDocumentLeftJoinOnStr := "t1.`project_id` = t3.`project_id` AND t1.`reference_type` = ? AND t1.`reference_id` = t3.`document_id` AND t1.`deleted` = t3.`deleted`"
	interfaceDocumentLQueryStr := fmt.Sprintf(
		"SELECT %s FROM %s AS t1 LEFT JOIN `tag` AS t2 ON %s LEFT JOIN `interface_document` AS t3 ON %s WHERE %s",
		selectFieldsStr, m.table, tagLeftJoinOnStr, interfaceDocumentLeftJoinOnStr, selectWhereStr,
	)

	// `interface_case`
	interfaceCaseLeftJoinOnStr := "t1.`project_id` = t3.`project_id` AND t1.`reference_type` = ? AND t1.`reference_id` = t3.`case_id` AND t1.`reference_version` = t3.`version` AND t1.`deleted` = t3.`deleted`"
	interfaceCaseQueryStr := fmt.Sprintf(
		"SELECT %s FROM %s AS t1 LEFT JOIN `tag` AS t2 ON %s LEFT JOIN `interface_case` AS t3 ON %s WHERE %s",
		selectFieldsStr, m.table, tagLeftJoinOnStr, interfaceCaseLeftJoinOnStr, selectWhereStr,
	)

	query := fmt.Sprintf(
		"%s UNION %s UNION %s UNION %s UNION %s UNION %s;", componentGroupQueryStr, apiCaseQueryStr, apiSuiteQueryStr,
		apiPlanQueryStr, interfaceDocumentLQueryStr, interfaceCaseQueryStr,
	)
	values := []any{
		"COMPONENT_GROUP", "COMPONENT_GROUP", projectId, tagId, constants.NotDeleted,
		"CASE", "API_CASE", projectId, tagId, constants.NotDeleted,
		"SUITE", "API_SUITE", projectId, tagId, constants.NotDeleted,
		"PLAN", "API_PLAN", projectId, tagId, constants.NotDeleted,
		"INTERFACE_DOCUMENT", "INTERFACE_DOCUMENT", projectId, tagId, constants.NotDeleted,
		"CASE", "INTERFACE_CASE", projectId, tagId, constants.NotDeleted,
	}

	var resp []*TagReferenceRelationship
	err := m.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

func (m *customTagReferenceRelationshipModel) RemoveByReferenceId(
	ctx context.Context, session sqlx.Session, projectId, referenceType, referenceId, referenceVersion string,
) (sql.Result, error) {
	keys := m.getKeysByReferenceId(ctx, projectId, referenceType, referenceId, referenceVersion)

	var ub squirrel.UpdateBuilder

	if referenceVersion == "" {
		// 引用版本为空，则无需区分引用类型来确定执行的SQL
		ub = squirrel.Update(m.table).
			SetMap(
				squirrel.Eq{
					"`deleted`":    constants.HasDeleted,
					"`deleted_by`": userinfo.FromContext(ctx).Account,
					"`deleted_at`": time.Now(),
				},
			).
			Where(
				"`project_id` = ? AND `reference_type` = ? AND `reference_id` = ?", projectId, referenceType,
				referenceId,
			)
	} else {
		// 引用版本非空，则根据引用类型来确定执行的SQL
		switch referenceType {
		case common.ConstReferenceTypeComponentGroup, common.ConstReferenceTypeApiCase, common.ConstReferenceTypeInterfaceCase:
			ub = squirrel.Update(m.table).
				SetMap(
					squirrel.Eq{
						"`deleted`":    constants.HasDeleted,
						"`deleted_by`": userinfo.FromContext(ctx).Account,
						"`deleted_at`": time.Now(),
					},
				).
				Where(
					"`project_id` = ? AND `reference_type` = ? AND `reference_id` = ? AND `reference_version` = ?",
					projectId, referenceType, referenceId, referenceVersion,
				)
		case common.ConstReferenceTypeApiSuite, common.ConstReferenceTypeApiPlan, common.ConstReferenceTypeInterfaceDocument:
			ub = squirrel.Update(m.table).
				SetMap(
					squirrel.Eq{
						"`deleted`":    constants.HasDeleted,
						"`deleted_by`": userinfo.FromContext(ctx).Account,
						"`deleted_at`": time.Now(),
					},
				).
				Where(
					"`project_id` = ? AND `reference_type` = ? AND `reference_id` = ?",
					projectId, referenceType, referenceId,
				)
		default:
			return nil, errors.WithStack(
				errorx.Err(
					errorx.DoesNotSupport, fmt.Sprintf("the tag reference type[%s] doesn't support", referenceType),
				),
			)
		}
	}

	return m.ExecCtx(
		ctx, func(ctx context.Context, conn sqlx.SqlConn) (sql.Result, error) {
			/*
				SQL:
				UPDATE `tag_reference_relationship`
				SET `deleted` = ?, `deleted_by` = ?, `deleted_at` = ?
				WHERE `project_id` = ?
				  AND `reference_type` = ?
				  AND `reference_id` = ?
				 [AND `reference_version` = ?]
			*/
			stmt, values, err := ub.ToSql()
			if err != nil {
				return nil, err
			}

			if session != nil {
				return session.ExecCtx(ctx, stmt, values...)
			}
			return conn.ExecCtx(ctx, stmt, values...)
		}, keys...,
	)
}

func (m *customTagReferenceRelationshipModel) getKeysByReferenceId(
	ctx context.Context, projectId, referenceType, referenceId, referenceVersion string,
) []string {
	cs, err := m.FindReferenceByReference(ctx, projectId, referenceType, referenceId, referenceVersion)
	if err != nil {
		return make([]string, 0)
	}

	keys := make([]string, 0, len(cs))
	for _, c := range cs {
		keys = append(keys, fmt.Sprintf("%s%v", cacheManagerTagReferenceRelationshipIdPrefix, c.Id))
	}

	return keys
}
