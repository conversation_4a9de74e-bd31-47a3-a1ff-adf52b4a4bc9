package model

import (
	"context"
	"fmt"
	"math"
	"testing"
	"time"

	"github.com/Masterminds/squirrel"
	"github.com/zeromicro/go-zero/core/jsonx"
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/stores/sqlx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sqlbuilder/rpc"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/db"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

var (
	converters              []utils.TypeConverter
	strNewCaseFailStatModel = `{
  "project_id": "project_id:Kqllt5-9fA-I5UOdhjA5d",
  "pagination": {
    "current_page": 1,
    "page_size": 20
  },
  "condition": {
    "single": null,
    "group": {
      "relationship": "AND",
      "conditions": [
        {
          "single": {
            "field": "branch_id",
            "compare": "LIKE",
            "other": {
              "value": "38"
            }
          },
          "group": null
        },
        {
          "single": {
            "field": "created_by",
            "compare": "IN",
            "in": [
              "T0169"
            ]
          },
          "group": null
        }
      ]
    }
  },
  "sort": [
    {
      "field": "updated_at",
      "order": "DESC"
    }
  ]
}`
)

func TestFindByPageQuery(t *testing.T) {
	in := new(pb.SearchCaseFailLogReq)
	err := protobuf.UnmarshalJSON([]byte(strNewCaseFailStatModel), in)
	if err != nil {
		fmt.Println(err)
		return
	}

	OrderByStrings := make([]string, 0, len(in.GetSort()))
	for _, field := range in.GetSort() {
		OrderByStrings = append(OrderByStrings, fmt.Sprintf("%s %s", field.Field, field.Order))
	}
	query := db.PageQuery{
		Pagination: in.GetPagination(),
		Condition:  in.GetCondition(),
		OrderBy:    OrderByStrings,
	}
	queries := make([]db.DiyQuery, 0, 1)
	queries = append(
		queries, db.DiyQuery{
			Field: "project_id",
			Value: in.GetProjectId(),
		},
	)
	CaseFailStatModel := NewCaseFailStatModel(MockMysql(), MockCache())

	pageQuery, err := CaseFailStatModel.FindByPageQuery(context.Background(), query, queries)
	if err != nil {
		fmt.Println(err)
		return
	}
	fmt.Println(pageQuery)
}

func TestGenerateSearchFailLogCaseQuery(t *testing.T) {
	resp := &pb.SearchCaseFailLogResp{}
	in := new(pb.SearchCaseFailLogReq)
	err := protobuf.UnmarshalJSON([]byte(strNewCaseFailStatModel), in)
	if err != nil {
		fmt.Println(err)
		return
	}
	CaseFailStatModel := NewCaseFailStatModel(MockMysql(), MockCache())

	req := SearchFailLogCaseReq{
		ProjectId:  in.GetProjectId(),
		Condition:  in.GetCondition(),
		Pagination: in.GetPagination(),
		Sort:       rpc.ConvertSortFields(in.GetSort()),
	}
	ctx := context.Background()
	selectBuilder, countBuilder := CaseFailStatModel.GenerateSearchFailLogCaseQuery(req)

	count, err := CaseFailStatModel.FindCount(ctx, countBuilder.SelectBuilder)
	if err != nil {
		fmt.Println(err)
		return
	}
	resp.TotalCount = uint64(count)
	resp.TotalPage = 1

	cases, err := CaseFailStatModel.FindNoCacheByQueryV2(ctx, selectBuilder.SelectBuilder)
	if err != nil {
		fmt.Println(err)
		return
	}
	pagination := in.GetPagination()

	resp.Items = make([]*pb.FailCase, 0, len(cases))
	for i, _Case := range cases {
		item := &pb.FailCase{}
		if err = utils.Copy(item, _Case, converters...); err != nil {
			fmt.Println(err)
			return
		}
		if pagination != nil {
			item.Rank = uint32(pagination.CurrentPage-1)*uint32(pagination.PageSize) + uint32(i) + 1
		}

		resp.Items = append(resp.Items, item)
	}

	if pagination != nil {
		resp.CurrentPage = pagination.GetCurrentPage()
		resp.PageSize = pagination.GetPageSize()
		resp.TotalPage = uint64(math.Ceil(float64(resp.TotalCount) / float64(resp.PageSize)))
	}
	fmt.Println(resp)
}

func TestLogicDeleteByProjectIdCaseIdCaseType(t *testing.T) {
	in := new(pb.SearchCaseFailLogReq)
	err := protobuf.UnmarshalJSON([]byte(strNewCaseFailStatModel), in)
	if err != nil {
		fmt.Println(err)
		return
	}
	CaseFailStatModel := NewCaseFailStatModel(MockMysql(), MockCache())
	err = CaseFailStatModel.Trans(
		context.Background(), func(context context.Context, session sqlx.Session) error {
			_, err := CaseFailStatModel.LogicDeleteByProjectIdCaseIdCaseType(
				context, session, in.GetProjectId(), "", "",
			)
			if err != nil {
				return err
			}
			return nil
		},
	)
	if err != nil {
		fmt.Println(err)
		return
	}
}

func TestUpdateByProjectIdCaseIdCaseTypeIncrFailCount(t *testing.T) {
	CaseFailStatModel := NewCaseFailStatModel(MockMysql(), MockCache())
	ctx := context.Background()
	cacheCase, err2 := CaseFailStatModel.FindNoCacheByProjectIdCaseIdCaseType(
		ctx, "project_id:Kqllt5-9fA-I5UOdhjA5d", "case_id:0YNd3Jmd03dC43mc9K2W2", "API_CASE",
	)
	if err2 != nil {
		logx.WithContext(ctx).Warn(fmt.Sprintf("ProcessorCaseFailStatResult error:%s", err2))
		return
	}
	if cacheCase != nil {
	}

	result, err := CaseFailStatModel.UpdateByProjectIdCaseIdCaseTypeForFailCountIncr(
		ctx, nil, "project_id:Kqllt5-9fA-I5UOdhjA5d", "case_id:0YNd3Jmd03dC43mc9K2W2", "API_CASE",
	)
	if err != nil {
		logx.WithContext(ctx).Warn(fmt.Sprintf("ProcessorCaseFailStatResult error:%s", err))
		return
	}
	affected, err := result.RowsAffected()
	if err != nil {
		logx.WithContext(ctx).Warn(fmt.Sprintf("ProcessorCaseFailStatResult error:%s", err))
		return
	}
	fmt.Println(affected)
}

func TestInsertTX(t *testing.T) {
	CaseFailStatModel := NewCaseFailStatModel(MockMysql(), MockCache())
	ctx := context.Background()
	_, err := CaseFailStatModel.InsertTX(
		ctx, nil, &CaseFailStat{
			ProjectId: "1",
			CaseId:    "2",
			BranchId:  "3",
			CaseType:  "5",
			FailCount: 0,
			Version:   0,
			CreatedBy: "sys",
			UpdatedBy: "ysy",
		},
	)
	if err != nil {
		logx.WithContext(ctx).Warn(fmt.Sprintf("ProcessorCaseFailStatResult error:%s", err))
		return
	}
}

func TestInsertBuilder(t *testing.T) {
	type args struct {
		data *CaseFailStat
	}
	tests := []struct {
		name string
		args args
	}{
		{
			name: "both `created_at` and `updated_at` are zero",
			args: args{
				data: &CaseFailStat{
					ProjectId: "project_id:1",
					CaseId:    "case_id:1",
					BranchId:  "category_id:1",
					CaseType:  "API_CASE",
					FailCount: 1,
					Version:   1,
					CreatedBy: "system",
					UpdatedBy: "system",
				},
			},
		},
		{
			name: "`created_at` is not zero",
			args: args{
				data: &CaseFailStat{
					ProjectId: "project_id:1",
					CaseId:    "case_id:1",
					BranchId:  "category_id:1",
					CaseType:  "API_CASE",
					FailCount: 1,
					Version:   1,
					CreatedBy: "system",
					UpdatedBy: "system",
					CreatedAt: time.Now(),
				},
			},
		},
		{
			name: "`updated_at` is not zero",
			args: args{
				data: &CaseFailStat{
					ProjectId: "project_id:1",
					CaseId:    "case_id:1",
					BranchId:  "category_id:1",
					CaseType:  "API_CASE",
					FailCount: 1,
					Version:   1,
					CreatedBy: "system",
					UpdatedBy: "system",
					UpdatedAt: time.Now(),
				},
			},
		},
		{
			name: "neither `created_at` nor `updated_at` are zero",
			args: args{
				data: &CaseFailStat{
					ProjectId: "project_id:1",
					CaseId:    "case_id:1",
					BranchId:  "category_id:1",
					CaseType:  "API_CASE",
					FailCount: 1,
					Version:   1,
					CreatedBy: "system",
					UpdatedBy: "system",
					CreatedAt: time.Now().Add(-time.Hour),
					UpdatedAt: time.Now(),
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				data := tt.args.data

				ib := squirrel.Insert(caseFailStatTableName).Columns(caseFailStatInsertFields...)
				values := []any{
					data.ProjectId,
					data.CaseId,
					data.BranchId,
					data.CaseType,
					data.FailCount,
					data.Version,
					data.Deleted,
					data.CreatedBy,
					data.UpdatedBy,
				}
				if !data.CreatedAt.IsZero() {
					ib = ib.Columns("`created_at`")
					values = append(values, data.CreatedAt)
				}
				if !data.UpdatedAt.IsZero() {
					ib = ib.Columns("`updated_at`")
					values = append(values, data.UpdatedAt)
				}

				stmt, values, err := ib.Values(values...).ToSql()
				if err != nil {
					t.Fatal(err)
				}

				t.Logf("stmt: %s\nvalues: %s", stmt, jsonx.MarshalIgnoreError(values))
			},
		)
	}
}

func TestUpdateBuilder(t *testing.T) {
	stmt, values, err := squirrel.Update("`case_fail_stat`").
		Set("`fail_count`", "`fail_count` + 1").
		Set("`version`", squirrel.Expr("`version` + 1")).
		Where("`project_id = ?` AND `case_id` = ?", "1", "2").
		ToSql()
	if err != nil {
		t.Fatal(err)
	}

	t.Logf("stmt: %s\nvalues: %s", stmt, jsonx.MarshalIgnoreError(values))
}
