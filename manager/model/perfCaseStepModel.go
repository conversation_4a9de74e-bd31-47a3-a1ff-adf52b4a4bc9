package model

import (
	"context"
	"database/sql"
	"fmt"

	"github.com/Masterminds/squirrel"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/types"
)

var (
	_ PerfCaseStepModel = (*customPerfCaseStepModel)(nil)

	perfCaseStepInsertFields = stringx.Remove(
		perfCaseStepFieldNames, "`id`", "`create_time`", "`update_time`", "`deleted_by`", "`created_at`",
		"`updated_at`", "`deleted_at`",
	)
)

type (
	// PerfCaseStepModel is an interface to be customized, add more methods here,
	// and implement the added methods in customPerfCaseStepModel.
	PerfCaseStepModel interface {
		perfCaseStepModel
		types.DBModel

		Trans(ctx context.Context, fn func(context context.Context, session sqlx.Session) error) error
		InsertBuilder(data *PerfCaseStep) squirrel.InsertBuilder
		UpdateBuilder(data *PerfCaseStep) squirrel.UpdateBuilder
		SelectBuilder() squirrel.SelectBuilder
		SelectCountBuilder() squirrel.SelectBuilder
		FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error)
		FindNoCacheByQuery(ctx context.Context, selectBuilder squirrel.SelectBuilder) ([]*PerfCaseStep, error)

		FindByCaseID(ctx context.Context, projectID, caseID string) ([]*PerfCaseStep, error)
		DeleteByCaseID(ctx context.Context, session sqlx.Session, projectID, caseID string) (sql.Result, error)
	}

	customPerfCaseStepModel struct {
		*defaultPerfCaseStepModel

		conn sqlx.SqlConn
	}
)

// NewPerfCaseStepModel returns a model for the database table.
func NewPerfCaseStepModel(conn sqlx.SqlConn, c cache.CacheConf, opts ...cache.Option) PerfCaseStepModel {
	return &customPerfCaseStepModel{
		defaultPerfCaseStepModel: newPerfCaseStepModel(conn, c, opts...),
		conn:                     conn,
	}
}

func (m *customPerfCaseStepModel) Table() string {
	return m.table
}

func (m *customPerfCaseStepModel) Fields() []string {
	return perfCaseStepFieldNames
}

func (m *customPerfCaseStepModel) Trans(
	ctx context.Context, fn func(context context.Context, session sqlx.Session) error,
) error {
	return m.TransactCtx(ctx, fn)
}

func (m *customPerfCaseStepModel) InsertBuilder(data *PerfCaseStep) squirrel.InsertBuilder {
	return squirrel.Insert(m.table).Columns(perfCaseStepInsertFields...).Values(
		data.ProjectId, data.CaseId, data.StepId, data.Type, data.Name, data.TargetRps, data.InitialRps,
		data.StepHeight, data.StepDuration, data.Url, data.Method, data.Headers, data.Body, data.Exports, data.Sleep,
		data.State, data.Deleted, data.CreatedBy, data.UpdatedBy,
	)
}

func (m *customPerfCaseStepModel) UpdateBuilder(data *PerfCaseStep) squirrel.UpdateBuilder {
	eq := squirrel.Eq{
		"`name`":          data.Name,
		"`target_rps`":    data.TargetRps,
		"`initial_rps`":   data.InitialRps,
		"`step_height`":   data.StepHeight,
		"`step_duration`": data.StepDuration,
		"`url`":           data.Url,
		"`method`":        data.Method,
		"`headers`":       data.Headers,
		"`body`":          data.Body,
		"`exports`":       data.Exports,
		"`sleep`":         data.Sleep,
		"`state`":         data.State,
		"`deleted`":       data.Deleted,
		"`updated_by`":    data.UpdatedBy,
		"`deleted_by`":    data.DeletedBy,
		"`deleted_at`":    data.DeletedAt,
	}
	return squirrel.Update(m.table).SetMap(eq).Where("`id` = ?", data.Id)
}

func (m *customPerfCaseStepModel) SelectBuilder() squirrel.SelectBuilder {
	return squirrel.Select(perfCaseStepFieldNames...).Where("`deleted` = ?", constants.NotDeleted).From(m.table)
}

func (m *customPerfCaseStepModel) SelectCountBuilder() squirrel.SelectBuilder {
	return squirrel.Select("count(*)").Where("`deleted` = ?", constants.NotDeleted).From(m.table)
}

func (m *customPerfCaseStepModel) FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error) {
	query, values, err := countBuilder.ToSql()
	if err != nil {
		return 0, err
	}

	var resp int64
	err = m.QueryRowNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return 0, err
	}
}

func (m *customPerfCaseStepModel) FindNoCacheByQuery(
	ctx context.Context, selectBuilder squirrel.SelectBuilder,
) ([]*PerfCaseStep, error) {
	query, values, err := selectBuilder.ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*PerfCaseStep
	err = m.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

func (m *customPerfCaseStepModel) FindByCaseID(ctx context.Context, projectID, caseID string) ([]*PerfCaseStep, error) {
	return m.FindNoCacheByQuery(ctx, m.SelectBuilder().Where("`project_id` = ? AND `case_id` = ?", projectID, caseID))
}

func (m *customPerfCaseStepModel) DeleteByCaseID(
	ctx context.Context, session sqlx.Session, projectID, caseID string,
) (sql.Result, error) {
	keys := m.getKeysByCaseID(ctx, projectID, caseID)

	return m.ExecCtx(
		ctx, func(ctx context.Context, conn sqlx.SqlConn) (sql.Result, error) {
			/*
				SQL:
				DELETE FROM `perf_case_step` WHERE `project_id` = ? AND `case_id` = ?
			*/

			stmt, values, err := squirrel.Delete(m.table).
				Where("`project_id` = ? AND `case_id` = ?", projectID, caseID).
				ToSql()
			if err != nil {
				return nil, err
			}

			if session != nil {
				return session.ExecCtx(ctx, stmt, values...)
			}
			return conn.ExecCtx(ctx, stmt, values...)
		}, keys...,
	)
}

func (m *customPerfCaseStepModel) getKeysByCaseID(ctx context.Context, projectID, caseID string) []string {
	cs, err := m.FindByCaseID(ctx, projectID, caseID)
	if err != nil {
		return make([]string, 0)
	}

	keys := make([]string, 0, len(cs)*2)
	for _, c := range cs {
		keys = append(
			keys,
			fmt.Sprintf("%s%v", cacheManagerPerfCaseStepIdPrefix, c.Id),
			fmt.Sprintf(
				"%s%v:%v:%v", cacheManagerPerfCaseStepProjectIdCaseIdStepIdPrefix, c.ProjectId, c.CaseId, c.StepId,
			),
		)
	}

	return keys
}
