package model

import (
	"context"
	"database/sql"
	"fmt"
	"time"

	"github.com/Masterminds/squirrel"
	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sqlbuilder"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/types"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/userinfo"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/utils"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
)

var (
	_                            ApiCaseModel  = (*customApiCaseModel)(nil)
	VirtualApiCaseReferenceModel types.DBModel = (*virtualApiCaseReferenceModel)(nil)

	apiCaseInsertFields = stringx.Remove(
		apiCaseFieldNames, "`id`", "`create_time`", "`update_time`", "`deleted_by`", "`created_at`", "`updated_at`",
		"`deleted_at`",
	)
	notReleasedCaseFieldNamesForApiCase       = stringx.Remove(apiCaseFieldNames, "`category_id`")
	notReleasedCaseFieldNamesForInterfaceCase = stringx.Remove(interfaceCaseFieldNames, "`document_id`")

	// cacheManagerApiCaseProjectIdCaseIdLatestPrefix = "cache:manager:apiCase:projectId:caseId:latest:"

	virtualApiCaseReferenceFieldNames = builder.RawFieldNames(&SearchApiCaseReferenceItem{})
)

type (
	// ApiCaseModel is an interface to be customized, add more methods here,
	// and implement the added methods in customApiCaseModel.
	ApiCaseModel interface {
		apiCaseModel
		types.DBModel

		Trans(ctx context.Context, fn func(context context.Context, session sqlx.Session) error) error
		InsertBuilder(data *ApiCase) squirrel.InsertBuilder
		UpdateBuilder(data *ApiCase) squirrel.UpdateBuilder
		SelectBuilder() squirrel.SelectBuilder
		SelectCountBuilder() squirrel.SelectBuilder
		FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error)
		FindNoCacheByQuery(ctx context.Context, builder squirrel.SelectBuilder) ([]*ApiCase, error)

		InsertTX(ctx context.Context, session sqlx.Session, data *ApiCase) (sql.Result, error)
		UpdateTX(ctx context.Context, session sqlx.Session, data *ApiCase) (sql.Result, error)
		DeleteTX(ctx context.Context, session sqlx.Session, id int64) error
		UpdateAllToNotLatest(ctx context.Context, session sqlx.Session, projectId, caseId string) (sql.Result, error)
		FindLatestOneNoCache(ctx context.Context, projectId, caseId string) (*ApiCase, error)
		// FindLatestOneNoCacheV2(ctx context.Context, projectId, caseId string) (*ApiCaseV2, error)
		FindCountByCategoryId(ctx context.Context, cm CategoryModel, cond FindCountByCategoryCondition) (int64, error)
		FindLatestBySuiteId(ctx context.Context, projectId, suiteId string) ([]*ApiCase, error)
		RemoveByCaseId(ctx context.Context, session sqlx.Session, projectId, caseId string) (sql.Result, error)

		GenerateSearchApiCaseReferenceSqlBuilder(req SearchApiCaseReferenceReq) (
			searchApiCaseReferenceSelectBuilder, searchApiCaseReferenceCountBuilder,
		)
		GenerateSearchApiCaseReferenceSqlBuilder2(req SearchApiCaseReferenceReq) (
			searchApiCaseReferenceSelectBuilder, searchApiCaseReferenceCountBuilder,
		)
		FindCountApiCaseReference(ctx context.Context, countBuilder searchApiCaseReferenceCountBuilder) (int64, error)
		FindApiCaseReference(
			ctx context.Context, selectBuilder searchApiCaseReferenceSelectBuilder,
		) ([]*SearchApiCaseReferenceItem, error)
	}

	customApiCaseModel struct {
		*defaultApiCaseModel

		conn sqlx.SqlConn
	}
)

// NewApiCaseModel returns a model for the database table.
func NewApiCaseModel(conn sqlx.SqlConn, c cache.CacheConf) ApiCaseModel {
	return &customApiCaseModel{
		defaultApiCaseModel: newApiCaseModel(conn, c),
		conn:                conn,
	}
}

func (m *customApiCaseModel) Table() string {
	return m.table
}

func (m *customApiCaseModel) Fields() []string {
	return apiCaseFieldNames
}

func (m *customApiCaseModel) Trans(
	ctx context.Context, fn func(context context.Context, session sqlx.Session) error,
) error {
	return m.TransactCtx(ctx, fn)
}

func (m *customApiCaseModel) InsertBuilder(data *ApiCase) squirrel.InsertBuilder {
	if data.CreatedAt == ZeroTime {
		return squirrel.Insert(m.table).Columns(apiCaseInsertFields...).Values(
			data.ProjectId, data.CategoryId, data.CaseId, data.Name, data.Description, data.Priority, data.Tags,
			data.State, data.AccountConfig, data.Version, data.Structure, data.Latest, data.Deleted, data.MaintainedBy,
			data.CreatedBy, data.UpdatedBy,
		)
	} else {
		return squirrel.Insert(m.table).Columns(apiCaseInsertFields...).Columns("`created_at`").Values(
			data.ProjectId, data.CategoryId, data.CaseId, data.Name, data.Description, data.Priority, data.Tags,
			data.State, data.AccountConfig, data.Version, data.Structure, data.Latest, data.Deleted, data.MaintainedBy,
			data.CreatedBy, data.UpdatedBy, data.CreatedAt,
		)
	}
}

func (m *customApiCaseModel) UpdateBuilder(data *ApiCase) squirrel.UpdateBuilder {
	eq := squirrel.Eq{
		"`category_id`":    data.CategoryId,
		"`name`":           data.Name,
		"`description`":    data.Description,
		"`priority`":       data.Priority,
		"`tags`":           data.Tags,
		"`state`":          data.State,
		"`account_config`": data.AccountConfig,
		"`version`":        data.Version,
		"`structure`":      data.Structure,
		"`latest`":         data.Latest,
		"`deleted`":        data.Deleted,
		"`maintained_by`":  data.MaintainedBy,
		"`updated_by`":     data.UpdatedBy,
		"`deleted_by`":     data.DeletedBy,
		"`deleted_at`":     data.DeletedAt,
	}
	return squirrel.Update(m.table).SetMap(eq).Where("`id` = ?", data.Id)
}

func (m *customApiCaseModel) SelectBuilder() squirrel.SelectBuilder {
	return squirrel.Select(apiCaseFieldNames...).Where("`deleted` = ?", constants.NotDeleted).From(m.table)
}

func (m *customApiCaseModel) SelectCountBuilder() squirrel.SelectBuilder {
	return squirrel.Select("count(*)").Where("`deleted` = ?", constants.NotDeleted).From(m.table)
}

func (m *customApiCaseModel) FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error) {
	query, values, err := countBuilder.ToSql()
	if err != nil {
		return 0, err
	}

	var resp int64
	err = m.QueryRowNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return 0, err
	}
}

func (m *customApiCaseModel) FindNoCacheByQuery(ctx context.Context, builder squirrel.SelectBuilder) (
	[]*ApiCase, error,
) {
	query, values, err := builder.ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*ApiCase
	err = m.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

//func (m *customApiCaseModel) FindNoCacheByQueryV2(ctx context.Context, builder squirrel.SelectBuilder) (
//	[]*ApiCaseV2, error,
//) {
//	query, values, err := builder.ToSql()
//	if err != nil {
//		return nil, err
//	}
//
//	var resp []*ApiCaseV2
//	err = m.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
//	switch err {
//	case nil:
//		return resp, nil
//	default:
//		return nil, err
//	}
//}

func (m *customApiCaseModel) InsertTX(ctx context.Context, session sqlx.Session, data *ApiCase) (sql.Result, error) {
	managerApiCaseIdKey := fmt.Sprintf("%s%v", cacheManagerApiCaseIdPrefix, data.Id)
	managerApiCaseProjectIdCaseIdVersionKey := fmt.Sprintf(
		"%s%v:%v:%v", cacheManagerApiCaseProjectIdCaseIdVersionPrefix, data.ProjectId, data.CaseId, data.Version,
	)
	// managerApiCaseProjectIdCaseIdLatestKey := fmt.Sprintf("%s%v:%v:%v", cacheManagerApiCaseProjectIdCaseIdLatestPrefix, data.ProjectId, data.CaseId, constants.IsLatestVersion)

	return m.ExecCtx(
		ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
			query, values, err := m.InsertBuilder(data).ToSql()
			if err != nil {
				return nil, err
			}
			if session != nil {
				return session.ExecCtx(ctx, query, values...)
			}
			return conn.ExecCtx(ctx, query, values...)
		}, managerApiCaseIdKey, managerApiCaseProjectIdCaseIdVersionKey,
	)
}

func (m *customApiCaseModel) UpdateTX(ctx context.Context, session sqlx.Session, newData *ApiCase) (sql.Result, error) {
	data, err := m.FindOne(ctx, newData.Id)
	if err != nil {
		return nil, err
	}

	managerApiCaseIdKey := fmt.Sprintf("%s%v", cacheManagerApiCaseIdPrefix, data.Id)
	managerApiCaseProjectIdCaseIdVersionKey := fmt.Sprintf(
		"%s%v:%v:%v", cacheManagerApiCaseProjectIdCaseIdVersionPrefix, data.ProjectId, data.CaseId, data.Version,
	)
	// managerApiCaseProjectIdCaseIdLatestKey := fmt.Sprintf("%s%v:%v:%v", cacheManagerApiCaseProjectIdCaseIdLatestPrefix, data.ProjectId, data.CaseId, constants.IsLatestVersion)

	return m.ExecCtx(
		ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
			query, values, err := m.UpdateBuilder(newData).ToSql()
			if err != nil {
				return nil, err
			}
			if session != nil {
				return session.ExecCtx(ctx, query, values...)
			}
			return conn.ExecCtx(ctx, query, values...)
		}, managerApiCaseIdKey, managerApiCaseProjectIdCaseIdVersionKey,
	)
}

func (m *customApiCaseModel) DeleteTX(ctx context.Context, session sqlx.Session, id int64) error {
	data, err := m.FindOne(ctx, id)
	if err != nil {
		return err
	}

	managerApiCaseIdKey := fmt.Sprintf("%s%v", cacheManagerApiCaseIdPrefix, id)
	managerApiCaseProjectIdCaseIdVersionKey := fmt.Sprintf(
		"%s%v:%v:%v", cacheManagerApiCaseProjectIdCaseIdVersionPrefix, data.ProjectId, data.CaseId, data.Version,
	)
	// managerApiCaseProjectIdCaseIdLatestKey := fmt.Sprintf("%s%v:%v:%v", cacheManagerApiCaseProjectIdCaseIdLatestPrefix, data.ProjectId, data.CaseId, constants.IsLatestVersion)

	_, err = m.ExecCtx(
		ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
			query := fmt.Sprintf("delete from %s where `id` = ?", m.table)
			if session != nil {
				return session.ExecCtx(ctx, query, id)
			}
			return conn.ExecCtx(ctx, query, id)
		}, managerApiCaseIdKey, managerApiCaseProjectIdCaseIdVersionKey,
	)

	return err
}

func (m *customApiCaseModel) UpdateAllToNotLatest(
	ctx context.Context, session sqlx.Session, projectId, caseId string,
) (sql.Result, error) {
	keys := m.getKeysByCaseId(ctx, projectId, caseId)

	ub := squirrel.Update(m.table).
		SetMap(squirrel.Eq{"`latest`": constants.IsNotLatestVersion}).
		Where("`project_id` = ? AND `case_id` = ? AND `deleted` = ?", projectId, caseId, constants.NotDeleted)

	return m.ExecCtx(
		ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
			query, values, err := ub.ToSql()
			if err != nil {
				return nil, err
			}
			if session != nil {
				return session.ExecCtx(ctx, query, values...)
			}
			return conn.ExecCtx(ctx, query, values...)
		}, keys...,
	)
}

// func (m *customApiCaseModel) FindNoCacheByName(ctx context.Context, builder squirrel.SelectBuilder, name string) ([]*ApiCase, error) {
//	return m.FindNoCacheByQuery(ctx, builder.Where("`name` = ?", name))
// }

func (m *customApiCaseModel) FindLatestOneNoCache(ctx context.Context, projectId, caseId string) (*ApiCase, error) {
	sb := m.SelectBuilder().Where(
		"`project_id` = ? AND `case_id` = ? AND `latest` = ?", projectId, caseId, constants.IsLatestVersion,
	).OrderBy("`updated_at` DESC").Limit(1)
	resp, err := m.FindNoCacheByQuery(ctx, sb)
	if err != nil {
		return nil, err
	} else if len(resp) == 0 {
		return nil, ErrNotFound
	}
	return resp[0], nil
}

//func (m *customApiCaseModel) FindLatestOneNoCacheV2(ctx context.Context, projectId, caseId string) (*ApiCaseV2, error) {
//	sb := m.SelectBuilder().Where(
//		"`project_id` = ? AND `case_id` = ? AND `latest` = ?", projectId, caseId, constants.IsLatestVersion,
//	).OrderBy("`updated_at` DESC").Limit(1)
//	resp, err := m.FindNoCacheByQueryV2(ctx, sb)
//	if err != nil {
//		return nil, err
//	} else if len(resp) == 0 {
//		return nil, ErrNotFound
//	}
//	return resp[0], nil
//}

// func (m *customApiCaseModel) FindLatestOne(ctx context.Context, projectId, caseId string) (*ApiCase, error) {
//	managerApiCaseProjectIdCaseIdLatestKey := fmt.Sprintf("%s%v:%v:%v", cacheManagerApiCaseProjectIdCaseIdLatestPrefix, projectId, caseId, constants.IsLatestVersion)
//
//	var resp ApiCase
//	err := m.QueryRowCtx(ctx, &resp, managerApiCaseProjectIdCaseIdLatestKey, func(ctx context.Context, conn sqlx.SqlConn, v interface{}) error {
//		query, values, err := m.SelectBuilder().Where("`project_id` = ? and `case_id` = ? and `latest` = ?", projectId, caseId, constants.IsLatestVersion).OrderBy("`updated_at` DESC").Limit(1).ToSql()
//		if err != nil {
//			return err
//		}
//
//		if err = conn.QueryRowCtx(ctx, &resp, query, values...); err != nil {
//			return err
//		}
//
//		return nil
//	})
//	switch err {
//	case nil:
//		return &resp, nil
//	default:
//		return nil, err
//	}
// }

func (m *customApiCaseModel) FindCountByCategoryId(
	ctx context.Context, cm CategoryModel, cond FindCountByCategoryCondition,
) (int64, error) {
	alias := "t"
	sb := squirrel.Select("COUNT(*)").
		From(fmt.Sprintf("%s AS %s", m.Table(), alias)).
		Where(
			fmt.Sprintf("%s.`project_id` = ? AND %s.`latest` = ? AND %s.`deleted` = ?", alias, alias, alias),
			cond.ProjectId, constants.IsLatestVersion, constants.NotDeleted,
		)

	if cond.DrillDown {
		sub := cm.FindDescendantCategoriesSqlBuilder(
			GetCategoryTreeCondition{
				ProjectId:     cond.ProjectId,
				Type:          common.ConstCategoryTreeTypeApiCase,
				CategoryId:    cond.CategoryId,
				OnlyDirectory: true,
			},
		)

		sb = sb.JoinClause(
			sub.Prefix("LEFT JOIN (").Suffix(
				fmt.Sprintf(
					") AS t1 ON %s.`category_id` = t1.`category_id`", alias,
				),
			),
		).Where("t1.`category_id` IS NOT NULL")
	} else {
		sb = sb.Where(fmt.Sprintf("%s.`category_id` = ?", alias), cond.CategoryId)
	}

	return m.FindCount(ctx, sb)
}

func (m *customApiCaseModel) FindLatestBySuiteId(ctx context.Context, projectId, suiteId string) ([]*ApiCase, error) {
	sb := squirrel.Select(utils.AddTableNameToFields("t1", m.Fields())...).
		From(fmt.Sprintf("%s AS t1, %s AS t2", m.Table(), apiCaseReferenceRelationshipTableName)).
		Where("t1.`project_id` = t2.`project_id` AND t1.`case_id` = t2.`case_id` AND t1.`deleted` = t2.`deleted`").
		Where(
			"t1.`project_id` = ? AND t1.`latest` = ? AND t1.`deleted` = ? AND t2.`reference_type` = ? AND t2.`reference_id` = ?",
			projectId, constants.IsLatestVersion, constants.NotDeleted, common.ConstReferenceTypeApiSuite, suiteId,
		)

	return m.FindNoCacheByQuery(ctx, sb)
}

func (m *customApiCaseModel) RemoveByCaseId(
	ctx context.Context, session sqlx.Session, projectId, caseId string,
) (sql.Result, error) {
	keys := m.getKeysByCaseId(ctx, projectId, caseId)

	return m.ExecCtx(
		ctx, func(ctx context.Context, conn sqlx.SqlConn) (sql.Result, error) {
			/*
				SQL:
				UPDATE `api_case`
				SET `deleted` = ?, `deleted_by` = ?, `deleted_at` = ?
				WHERE `project_id` = ?
				  AND `case_id` = ?
			*/
			stmt, values, err := squirrel.Update(m.table).
				SetMap(
					squirrel.Eq{
						"`deleted`":    constants.HasDeleted,
						"`deleted_by`": userinfo.FromContext(ctx).Account,
						"`deleted_at`": time.Now(),
					},
				).
				Where("`project_id` = ? AND `case_id` = ?", projectId, caseId).
				ToSql()
			if err != nil {
				return nil, err
			}

			if session != nil {
				return session.ExecCtx(ctx, stmt, values...)
			}
			return conn.ExecCtx(ctx, stmt, values...)
		}, keys...,
	)
}

func (m *customApiCaseModel) getKeysByCaseId(ctx context.Context, projectId, caseId string) []string {
	sb := m.SelectBuilder().Where("`project_id` = ? AND `case_id` = ?", projectId, caseId)
	cs, err := m.FindNoCacheByQuery(ctx, sb)
	if err != nil {
		return make([]string, 0)
	}

	keys := make([]string, 0, len(cs)*2)
	for _, c := range cs {
		keys = append(
			keys,
			fmt.Sprintf("%s%v", cacheManagerApiCaseIdPrefix, c.Id),
			fmt.Sprintf(
				"%s%v:%v:%v", cacheManagerApiCaseProjectIdCaseIdVersionPrefix, c.ProjectId, c.CaseId, c.Version,
			),
			// fmt.Sprintf("%s%v:%v:%v", cacheManagerApiCaseProjectIdCaseIdLatestPrefix, c.ProjectId, c.CaseId, constants.IsLatestVersion),
		)
	}

	return keys
}

type virtualApiCaseReferenceModel struct{}

func (m *virtualApiCaseReferenceModel) Table() string {
	return "`virtual_api_case_reference`"
}

func (m *virtualApiCaseReferenceModel) Fields() []string {
	return virtualApiCaseReferenceFieldNames
}

type SearchApiCaseReferenceItem struct {
	ProjectId     string         `db:"project_id"`     // 项目ID
	CaseId        string         `db:"case_id"`        // 用例ID
	ReferenceType string         `db:"reference_type"` // 引用对象类型（API集合）
	ReferenceId   string         `db:"reference_id"`   // 引用对象ID（API集合ID）
	Name          string         `db:"name"`           // 引用对象名称
	Description   sql.NullString `db:"description"`    // 引用对象描述
	Priority      int64          `db:"priority"`       // 优先级（NULL、P0、P1、P2、P3...）
	Tags          sql.NullString `db:"tags"`           // 标签
	State         int64          `db:"state"`          // 引用对象状态（生效、失效）
	MaintainedBy  sql.NullString `db:"maintained_by"`  // 维护者的用户ID
	CreatedBy     string         `db:"created_by"`     // 创建者的用户ID
	UpdatedBy     string         `db:"updated_by"`     // 最近一次更新者的用户ID
	CreatedAt     time.Time      `db:"created_at"`     // 创建时间
	UpdatedAt     time.Time      `db:"updated_at"`     // 更新时间
}

type searchApiCaseReferenceSelectBuilder struct {
	squirrel.SelectBuilder
}

type searchApiCaseReferenceCountBuilder struct {
	squirrel.SelectBuilder
}

func (m *customApiCaseModel) GenerateSearchApiCaseReferenceSqlBuilder(req SearchApiCaseReferenceReq) (
	searchApiCaseReferenceSelectBuilder, searchApiCaseReferenceCountBuilder,
) {
	/*
		SQL:
		SELECT t.`project_id`,
		       t.`case_id`,
		       t.`reference_type`,
		       t.`reference_id`,
		       t.`name`,
		       t.`description`,
		       t.`priority`,
		       t.`tags`,
		       t.`state`,
		       t.`maintained_by`,
		       t.`created_by`,
		       t.`updated_by`,
		       t.`created_at`,
		       t.`updated_at`
		FROM (SELECT t1.`project_id`,
		             t1.`case_id`,
		             t2.`reference_type`,
		             t2.`reference_id`,
		             t3.`name`,
		             t3.`description`,
		             t3.`priority`,
		             t3.`tags`,
		             t3.`state`,
		             t3.`maintained_by`,
		             t3.`created_by`,
		             t3.`updated_by`,
		             t3.`created_at`,
		             t3.`updated_at`
		      FROM `api_case` AS t1
		               INNER JOIN `api_case_reference_relationship` AS t2 ON t1.`project_id` = t2.`project_id` AND t1.`case_id` = t2.`case_id` AND t1.`deleted` = t2.`deleted`
		               LEFT JOIN `api_suite` AS t3 ON t2.`reference_type` = 'API_SUITE' AND t1.`project_id` = t3.`project_id` AND t2.`reference_id` = t3.`suite_id` AND t1.`deleted` = t3.`deleted`
		      WHERE t1.`project_id` = ?
		        AND t1.`case_id` = ?
		        AND t1.`latest` = 1
		        AND t1.`deleted` = 0
		        AND t3.`id` IS NOT NULL) AS t
	*/

	fields := []string{
		"t1.`project_id`",
		"t1.`case_id`",
		"t2.`reference_type`",
		"t2.`reference_id`",
		"t3.`name`",
		"t3.`description`",
		"t3.`priority`",
		"t3.`tags`",
		"t3.`state`",
		"t3.`maintained_by`",
		"t3.`created_by`",
		"t3.`updated_by`",
		"t3.`created_at`",
		"t3.`updated_at`",
	}
	tmp := squirrel.Select(fields...).From(m.table+" AS t1").
		InnerJoin(apiCaseReferenceRelationshipTableName+" AS t2 ON t1.`project_id` = t2.`project_id` AND t1.`case_id` = t2.`case_id` AND t1.`deleted` = t2.`deleted`").
		LeftJoin(
			fmt.Sprintf(
				"%s AS t3 ON t2.`reference_type` = '%s' AND t1.`project_id` = t3.`project_id` AND t2.`reference_id` = t3.`suite_id` AND t1.`deleted` = t3.`deleted`",
				apiSuiteTableName, common.ConstReferenceTypeApiSuite,
			),
		).
		Where(
			"t1.`project_id` = ? AND t1.`case_id` = ? AND t1.`latest` = ? AND t1.`deleted` = ? AND t3.`id` IS NOT NULL",
			req.ProjectId, req.CaseId, constants.IsLatestVersion, constants.NotDeleted,
		)

	rm := VirtualApiCaseReferenceModel

	alias := "t"
	sb := sqlbuilder.SearchOptionsWithAlias(
		squirrel.Select(utils.AddTableNameToFields(alias, rm.Fields())...).FromSelect(tmp, alias),
		alias,
		sqlbuilder.WithCondition(rm, req.Condition),
		sqlbuilder.WithPagination(rm, req.Pagination),
		sqlbuilder.WithSort(rm, req.Sort),
	)
	scb := sqlbuilder.SearchOptionsWithAlias(
		squirrel.Select("COUNT(*)").FromSelect(tmp, alias),
		alias,
		sqlbuilder.WithCondition(rm, req.Condition),
	)

	return searchApiCaseReferenceSelectBuilder{sb}, searchApiCaseReferenceCountBuilder{scb}
}

func (m *customApiCaseModel) GenerateSearchApiCaseReferenceSqlBuilder2(req SearchApiCaseReferenceReq) (
	searchApiCaseReferenceSelectBuilder, searchApiCaseReferenceCountBuilder,
) {
	/*
		SELECT
		  t.`project_id`,
		  t.`case_id`,
		  t.`reference_type`,
		  t.`reference_id`,
		  t.`name`,
		  t.`description`,
		  t.`priority`,
		  t.`tags`,
		  t.`state`,
		  t.`maintained_by`,
		  t.`created_by`,
		  t.`updated_by`,
		  t.`created_at`,
		  t.`updated_at`
		FROM
		  (
		    SELECT
		      t1.`project_id`,
		      t1.`case_id`,
		      'API_SUITE' AS `reference_type`,
		      t2.`suite_id` AS `reference_id`,
		      t3.`name`,
		      t3.`description`,
		      t3.`priority`,
		      t3.`tags`,
		      t3.`state`,
		      t3.`maintained_by`,
		      t3.`created_by`,
		      t3.`updated_by`,
		      t3.`created_at`,
		      t3.`updated_at`
		    FROM
		      `api_case` AS t1
		      INNER JOIN `api_suite_reference_relationship` AS t2 ON t1.`project_id` = t2.`project_id`
		      AND t2.`reference_type` = 'API_CASE'
		      AND t1.`case_id` = t2.`reference_id`
		      AND t1.`deleted` = t2.`deleted`
		      LEFT JOIN `api_suite` AS t3 ON t1.`project_id` = t3.`project_id`
		      AND t2.`suite_id` = t3.`suite_id`
		      AND t1.`deleted` = t3.`deleted`
		    WHERE
		      t1.`project_id` = ?
		      AND t1.`case_id` = ?
		      AND t1.`latest` = 1
		      AND t1.`deleted` = 0
		      AND t3.`id` IS NOT NULL
		  ) AS t;
	*/

	fields := []string{
		"t1.`project_id`",
		"t1.`case_id`",
		"'API_SUITE' AS `reference_type`",
		"t2.`suite_id` AS `reference_id`",
		"t3.`name`",
		"t3.`description`",
		"t3.`priority`",
		"t3.`tags`",
		"t3.`state`",
		"t3.`maintained_by`",
		"t3.`created_by`",
		"t3.`updated_by`",
		"t3.`created_at`",
		"t3.`updated_at`",
	}
	tmp := squirrel.Select(fields...).From(m.table+" AS t1").
		InnerJoin(
			fmt.Sprintf("%s AS t2 ON t1.`project_id` = t2.`project_id` AND t2.`reference_type` = '%s' AND t1.`case_id` = t2.`reference_id` AND t1.`deleted` = t2.`deleted`",
				apiSuiteReferenceRelationshipTableName, common.ConstReferenceTypeApiCase,
			),
		).
		LeftJoin(
			apiSuiteTableName+" AS t3 ON t1.`project_id` = t3.`project_id` AND t2.`suite_id` = t3.`suite_id` AND t1.`deleted` = t3.`deleted`",
		).
		Where(
			"t1.`project_id` = ? AND t1.`case_id` = ? AND t1.`latest` = ? AND t1.`deleted` = ? AND t3.`id` IS NOT NULL",
			req.ProjectId, req.CaseId, constants.IsLatestVersion, constants.NotDeleted,
		)

	rm := VirtualApiCaseReferenceModel

	alias := "t"
	sb := sqlbuilder.SearchOptionsWithAlias(
		squirrel.Select(utils.AddTableNameToFields(alias, rm.Fields())...).FromSelect(tmp, alias),
		alias,
		sqlbuilder.WithCondition(rm, req.Condition),
		sqlbuilder.WithPagination(rm, req.Pagination),
		sqlbuilder.WithSort(rm, req.Sort),
	)
	scb := sqlbuilder.SearchOptionsWithAlias(
		squirrel.Select("COUNT(*)").FromSelect(tmp, alias),
		alias,
		sqlbuilder.WithCondition(rm, req.Condition),
	)

	return searchApiCaseReferenceSelectBuilder{sb}, searchApiCaseReferenceCountBuilder{scb}
}

func (m *customApiCaseModel) FindCountApiCaseReference(
	ctx context.Context, countBuilder searchApiCaseReferenceCountBuilder,
) (int64, error) {
	return m.FindCount(ctx, countBuilder.SelectBuilder)
}

func (m *customApiCaseModel) FindApiCaseReference(
	ctx context.Context, selectBuilder searchApiCaseReferenceSelectBuilder,
) ([]*SearchApiCaseReferenceItem, error) {
	var resp []*SearchApiCaseReferenceItem

	err := utils.FindRows(
		ctx, m.conn, selectBuilder.SelectBuilder, func() any {
			return &resp
		},
	)

	return resp, err
}

// [2024-04-16] delete this method:
// Validate 判断API用例是否可被执行
//func (x *ApiCase) Validate() bool {
//	// API用例的固有状态为生效才能被执行
//	return x.State == int64(constants.EnableStatus)
//}
