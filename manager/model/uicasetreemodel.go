package model

import (
	"context"
	"database/sql"
	"fmt"
	"time"

	"github.com/Masterminds/squirrel"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/types"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/userinfo"
)

var (
	_ UiCaseTreeModel = (*customUiCaseTreeModel)(nil)

	uiCaseTreeInsertFields = stringx.Remove(
		uiCaseTreeFieldNames, "`id`", "`create_time`", "`update_time`", "`deleted_by`", "`created_at`", "`updated_at`",
		"`deleted_at`",
	)
)

type (
	// UiCaseTreeModel is an interface to be customized, add more methods here,
	// and implement the added methods in customUiCaseTreeModel.
	UiCaseTreeModel interface {
		uiCaseTreeModel
		types.DBModel

		Trans(ctx context.Context, fn func(context context.Context, session sqlx.Session) error) error
		InsertBuilder(data *UiCaseTree) squirrel.InsertBuilder
		UpdateBuilder(data *UiCaseTree) squirrel.UpdateBuilder
		SelectBuilder() squirrel.SelectBuilder
		SelectCountBuilder() squirrel.SelectBuilder
		FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error)
		FindNoCacheByQuery(ctx context.Context, selectBuilder squirrel.SelectBuilder) ([]*UiCaseTree, error)

		FindAll(ctx context.Context, projectID, planID string) ([]*UiCaseTree, error)
		RemoveByProjectIdPlanId(ctx context.Context, session sqlx.Session, projectId, planId string) (sql.Result, error)
		FindAllPath(ctx context.Context, projectID, planId string) ([]*UiCasePath, error)
		RemoveByProjectIdPlanIdPath(
			ctx context.Context, session sqlx.Session, projectId, planId, path string,
		) (sql.Result, error)
	}

	customUiCaseTreeModel struct {
		*defaultUiCaseTreeModel
	}
)

// NewUiCaseTreeModel returns a model for the database table.
func NewUiCaseTreeModel(conn sqlx.SqlConn, c cache.CacheConf, opts ...cache.Option) UiCaseTreeModel {
	return &customUiCaseTreeModel{
		defaultUiCaseTreeModel: newUiCaseTreeModel(conn, c, opts...),
	}
}

func (m *customUiCaseTreeModel) Table() string {
	return m.table
}

func (m *customUiCaseTreeModel) Fields() []string {
	return uiCaseTreeFieldNames
}

func (m *customUiCaseTreeModel) Trans(
	ctx context.Context, fn func(context context.Context, session sqlx.Session) error,
) error {
	return m.TransactCtx(ctx, fn)
}

func (m *customUiCaseTreeModel) InsertBuilder(data *UiCaseTree) squirrel.InsertBuilder {
	return squirrel.Insert(m.table).Columns(uiCaseTreeInsertFields...).Values(
		data.ProjectId,
		data.PlanId, data.Path, data.ParentPath, data.Name, data.Alias, data.Type, data.Tags, data.Deleted,
		data.CreatedBy, data.CreatedAt, data.UpdatedBy, data.UpdatedAt,
	)
}

func (m *customUiCaseTreeModel) UpdateBuilder(data *UiCaseTree) squirrel.UpdateBuilder {
	eq := squirrel.Eq{
		"`plan_id`":    data.PlanId,
		"`project_id`": data.ProjectId,
		"`path`":       data.Path,
	}
	return squirrel.Update(m.table).SetMap(eq).Where("`id` = ?", data.Id)
}

func (m *customUiCaseTreeModel) SelectBuilder() squirrel.SelectBuilder {
	return squirrel.Select(uiCaseTreeFieldNames...).Where("`deleted` = ?", constants.NotDeleted).From(m.table)
}

func (m *customUiCaseTreeModel) SelectCountBuilder() squirrel.SelectBuilder {
	return squirrel.Select("count(*)").Where("`deleted` = ?", constants.NotDeleted).From(m.table)
}

func (m *customUiCaseTreeModel) FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error) {
	query, values, err := countBuilder.ToSql()
	if err != nil {
		return 0, err
	}

	var resp int64
	err = m.QueryRowNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return 0, err
	}
}

func (m *customUiCaseTreeModel) FindNoCacheByQuery(
	ctx context.Context, selectBuilder squirrel.SelectBuilder,
) ([]*UiCaseTree, error) {
	query, values, err := selectBuilder.ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*UiCaseTree
	err = m.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

func (m *customUiCaseTreeModel) FindAll(ctx context.Context, projectID, planID string) ([]*UiCaseTree, error) {
	return m.FindNoCacheByQuery(
		ctx, m.SelectBuilder().Where(
			"`project_id` = ? AND `plan_id` = ? AND `deleted` = ?", projectID, planID, constants.NotDeleted,
		).OrderBy(`parent_path ASC`),
	)
}

func (m *customUiCaseTreeModel) RemoveByProjectIdPlanId(
	ctx context.Context, session sqlx.Session, projectId, planId string,
) (sql.Result, error) {
	managerUiCaseProjectIdPlanIdKey := fmt.Sprintf("%s%v%v", cacheManagerUiCaseTreeIdPrefix, projectId, planId)
	keys := []string{managerUiCaseProjectIdPlanIdKey}

	return m.ExecCtx(
		ctx, func(ctx context.Context, conn sqlx.SqlConn) (sql.Result, error) {
			/*
				SQL:
				UPDATE `ui_case_tree`
				SET `deleted` = ?, `deleted_by` = ?, `deleted_at` = ?
				WHERE `project_id` = ?
				  AND `plan_id` = ?
			*/
			stmt, values, err := squirrel.Update(m.table).
				SetMap(
					squirrel.Eq{
						"`deleted`":    constants.HasDeleted,
						"`deleted_by`": userinfo.FromContext(ctx).Account,
						"`deleted_at`": time.Now(),
					},
				).
				Where("`project_id` = ? AND `plan_id` = ?", projectId, planId).
				ToSql()
			if err != nil {
				return nil, err
			}

			if session != nil {
				return session.ExecCtx(ctx, stmt, values...)
			}
			return conn.ExecCtx(ctx, stmt, values...)
		}, keys...,
	)
}

func (m *customUiCaseTreeModel) RemoveByProjectIdPlanIdPath(
	ctx context.Context, session sqlx.Session, projectId, planId, path string,
) (sql.Result, error) {
	managerUiCaseProjectIdPlanIdPathKey := fmt.Sprintf(
		"%s%v%v%v", cacheManagerUiCaseTreeIdPrefix, projectId, planId, path,
	)
	keys := []string{managerUiCaseProjectIdPlanIdPathKey}

	return m.ExecCtx(
		ctx, func(ctx context.Context, conn sqlx.SqlConn) (sql.Result, error) {
			/*
					SQL:
					UPDATE `ui_case_tree`
					SET `deleted` = ?, `deleted_by` = ?, `deleted_at` = ?
					WHERE `project_id` = ?
					  AND `plan_id` = ?
				      AND `path` = ?
			*/
			stmt, values, err := squirrel.Update(m.table).
				SetMap(
					squirrel.Eq{
						"`deleted`":    constants.HasDeleted,
						"`deleted_by`": userinfo.FromContext(ctx).Account,
						"`deleted_at`": time.Now(),
					},
				).
				Where("`project_id` = ? AND `plan_id` = ? AND `path` = ?", projectId, planId, path).
				ToSql()
			if err != nil {
				return nil, err
			}

			if session != nil {
				return session.ExecCtx(ctx, stmt, values...)
			}
			return conn.ExecCtx(ctx, stmt, values...)
		}, keys...,
	)
}

func (m *customUiCaseTreeModel) FindAllPath(ctx context.Context, projectID, planId string) ([]*UiCasePath, error) {
	query, values, err := squirrel.Select("path").From(m.table).Where(
		"`project_id` = ? AND `plan_id` = ? AND `deleted` = ?", projectID, planId, constants.NotDeleted,
	).ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*UiCasePath
	err = m.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}
