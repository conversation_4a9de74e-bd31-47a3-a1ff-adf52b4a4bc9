package model

import (
	"context"
	"database/sql"
	"fmt"
	"time"

	"github.com/Masterminds/squirrel"
	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sqlbuilder"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/types"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/utils"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
)

var (
	_                                  ProjectDeviceModel = (*customProjectDeviceModel)(nil)
	VirtualProjectDeviceReferenceModel types.DBModel      = (*virtualProjectDeviceReferenceModel)(nil)

	projectDeviceInsertFields = stringx.Remove(
		projectDeviceFieldNames, "`id`", "`create_time`", "`update_time`", "`deleted_by`", "`created_at`",
		"`updated_at`", "`deleted_at`",
	)

	virtualProjectDeviceReferenceFieldNames = builder.RawFieldNames(&SearchProjectDeviceReferenceItem{})
)

type (
	// ProjectDeviceModel is an interface to be customized, add more methods here,
	// and implement the added methods in customProjectDeviceModel.
	ProjectDeviceModel interface {
		projectDeviceModel
		types.DBModel

		Trans(ctx context.Context, fn func(context context.Context, session sqlx.Session) error) error
		InsertBuilder(data *ProjectDevice) squirrel.InsertBuilder
		UpdateBuilder(data *ProjectDevice) squirrel.UpdateBuilder
		SelectBuilder() squirrel.SelectBuilder
		SelectCountBuilder() squirrel.SelectBuilder
		FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error)
		FindNoCacheByQuery(ctx context.Context, selectBuilder squirrel.SelectBuilder) ([]*ProjectDevice, error)

		FindAll(ctx context.Context, projectID string) ([]*ProjectDevice, error)
		FindOneByUDID(ctx context.Context, projectID, udid string) (*ProjectDevice, error)
		FindCountBySearchReq(ctx context.Context, req SearchProjectDeviceReq) (int64, error)
		FindAllBySearchReq(ctx context.Context, req SearchProjectDeviceReq) ([]*ProjectDevice, error)
		FindCountReferenceBySearchReq(ctx context.Context, req SearchProjectDeviceReferenceReq) (int64, error)
		FindReferenceBySearchReq(
			ctx context.Context, req SearchProjectDeviceReferenceReq,
		) ([]*SearchProjectDeviceReferenceItem, error)

		FindAllByUDID(ctx context.Context, udid string) ([]*ProjectDevice, error)
		DeleteByUDID(ctx context.Context, session sqlx.Session, udid string) (sql.Result, error)

		RemoveByUDID(ctx context.Context, session sqlx.Session, projectID, udid string) (sql.Result, error)
	}

	customProjectDeviceModel struct {
		*defaultProjectDeviceModel

		conn sqlx.SqlConn
	}
)

// NewProjectDeviceModel returns a model for the database table.
func NewProjectDeviceModel(conn sqlx.SqlConn, c cache.CacheConf, opts ...cache.Option) ProjectDeviceModel {
	return &customProjectDeviceModel{
		defaultProjectDeviceModel: newProjectDeviceModel(conn, c, opts...),
		conn:                      conn,
	}
}

func (m *customProjectDeviceModel) Table() string {
	return m.table
}

func (m *customProjectDeviceModel) Fields() []string {
	return projectDeviceFieldNames
}

func (m *customProjectDeviceModel) Trans(
	ctx context.Context, fn func(context context.Context, session sqlx.Session) error,
) error {
	return m.TransactCtx(ctx, fn)
}

func (m *customProjectDeviceModel) InsertBuilder(data *ProjectDevice) squirrel.InsertBuilder {
	return squirrel.Insert(m.table).Columns(projectDeviceInsertFields...).Values(
		data.ProjectId, data.Udid, data.Usage, data.Deleted, data.CreatedBy, data.UpdatedBy,
	)
}

func (m *customProjectDeviceModel) UpdateBuilder(data *ProjectDevice) squirrel.UpdateBuilder {
	eq := squirrel.Eq{
		"`usage`":      data.Usage,
		"`deleted`":    data.Deleted,
		"`updated_by`": data.UpdatedBy,
		"`deleted_by`": data.DeletedBy,
		"`deleted_at`": data.DeletedAt,
	}
	return squirrel.Update(m.table).SetMap(eq).Where("`id` = ?", data.Id)
}

func (m *customProjectDeviceModel) SelectBuilder() squirrel.SelectBuilder {
	return squirrel.Select(projectDeviceFieldNames...).Where("`deleted` = ?", constants.NotDeleted).From(m.table)
}

func (m *customProjectDeviceModel) SelectCountBuilder() squirrel.SelectBuilder {
	return squirrel.Select("count(*)").Where("`deleted` = ?", constants.NotDeleted).From(m.table)
}

func (m *customProjectDeviceModel) FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error) {
	query, values, err := countBuilder.ToSql()
	if err != nil {
		return 0, err
	}

	var resp int64
	err = m.QueryRowNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return 0, err
	}
}

func (m *customProjectDeviceModel) FindNoCacheByQuery(
	ctx context.Context, selectBuilder squirrel.SelectBuilder,
) ([]*ProjectDevice, error) {
	query, values, err := selectBuilder.ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*ProjectDevice
	err = m.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

func (m *customProjectDeviceModel) FindAll(ctx context.Context, projectID string) ([]*ProjectDevice, error) {
	return m.FindNoCacheByQuery(ctx, m.SelectBuilder().Where("`project_id` = ?", projectID))
}

func (m *customProjectDeviceModel) FindOneByUDID(ctx context.Context, projectID, udid string) (*ProjectDevice, error) {
	resp, err := m.FindNoCacheByQuery(
		ctx, m.SelectBuilder().Where("`project_id` = ? AND `udid` = ?", projectID, udid),
	)
	if err != nil {
		return nil, err
	} else if len(resp) == 0 {
		return nil, ErrNotFound
	}
	return resp[0], nil
}

func (m *customProjectDeviceModel) FindCountBySearchReq(ctx context.Context, req SearchProjectDeviceReq) (
	int64, error,
) {
	return m.FindCount(
		ctx, sqlbuilder.SearchOptions(
			m.SelectCountBuilder().Where("`project_id` = ?", req.ProjectID),
			sqlbuilder.WithCondition(m, req.Condition),
		),
	)
}

func (m *customProjectDeviceModel) FindAllBySearchReq(
	ctx context.Context, req SearchProjectDeviceReq,
) ([]*ProjectDevice, error) {
	return m.FindNoCacheByQuery(
		ctx, sqlbuilder.SearchOptions(
			m.SelectBuilder().Where("`project_id` = ?", req.ProjectID),
			sqlbuilder.WithCondition(m, req.Condition),
			sqlbuilder.WithPagination(m, req.Pagination),
			sqlbuilder.WithSort(m, req.Sort),
		),
	)
}

type (
	virtualProjectDeviceReferenceModel struct{}

	SearchProjectDeviceReferenceItem struct {
		ProjectId     string         `db:"project_id"`     // 项目ID
		Udid          string         `db:"udid"`           // 设备编号
		ReferenceType string         `db:"reference_type"` // 引用对象类型（UI测试计划、稳定性测试计划）
		ReferenceId   string         `db:"reference_id"`   // 引用对象ID
		CategoryId    string         `db:"category_id"`    // 分类ID
		Name          string         `db:"name"`           // 引用对象名称
		Description   sql.NullString `db:"description"`    // 引用对象描述
		MaintainedBy  string         `db:"maintained_by"`  // 维护者的用户ID
		CreatedBy     string         `db:"created_by"`     // 创建者的用户ID
		UpdatedBy     string         `db:"updated_by"`     // 最近一次更新者的用户ID
		CreatedAt     time.Time      `db:"created_at"`     // 创建时间
		UpdatedAt     time.Time      `db:"updated_at"`     // 更新时间
	}
)

func (m *virtualProjectDeviceReferenceModel) Table() string {
	return "`virtual_project_device_reference`"
}

func (m *virtualProjectDeviceReferenceModel) Fields() []string {
	return virtualProjectDeviceReferenceFieldNames
}

func (m *customProjectDeviceModel) FindCountReferenceBySearchReq(
	ctx context.Context, req SearchProjectDeviceReferenceReq,
) (int64, error) {
	/*
		SQL:
		SELECT COUNT(*)
		FROM (SELECT t.`project_id`,
		             t.`udid`,
		             t.`reference_type`,
		             t.`reference_id`,
		             t.`category_id`,
		             t.`name`,
		             t.`description`,
		             t.`maintained_by`,
		             t.`created_by`,
		             t.`updated_by`,
		             t.`created_at`,
		             t.`updated_at`
		      FROM (SELECT t1.`project_id`,
		                   t1.`udid`,
		                   t2.`reference_type`,
		                   t2.`reference_id`,
		                   IF(t2.`reference_type` = 'UI_PLAN', t3.`category_id`, t4.`category_id`)     AS `category_id`,
		                   IF(t2.`reference_type` = 'UI_PLAN', t3.`name`, t4.`name`)                   AS `name`,
		                   IF(t2.`reference_type` = 'UI_PLAN', t3.`description`, t4.`description`)     AS `description`,
		                   IF(t2.`reference_type` = 'UI_PLAN', t3.`maintained_by`, t4.`maintained_by`) AS `maintained_by`,
		                   IF(t2.`reference_type` = 'UI_PLAN', t3.`created_by`, t4.`created_by`)       AS `created_by`,
		                   IF(t2.`reference_type` = 'UI_PLAN', t3.`updated_by`, t4.`updated_by`)       AS `updated_by`,
		                   IF(t2.`reference_type` = 'UI_PLAN', t3.`created_at`, t4.`created_at`)       AS `created_at`,
		                   IF(t2.`reference_type` = 'UI_PLAN', t3.`updated_at`, t4.`updated_at`)       AS `updated_at`
		            FROM `project_device` AS t1
		                     INNER JOIN `project_device_reference_relationship` AS t2
		                         ON t1.`project_id` = t2.`project_id` AND
		                            t1.`udid` = t2.`udid` AND
		                            t1.`deleted` = t2.`deleted`
		                     LEFT JOIN `ui_plan` AS t3
		                         ON t1.`project_id` = t3.`project_id` AND
		                            t1.`deleted` = t3.`deleted` AND
		                            t2.`reference_type` = 'UI_PLAN' AND
		                            t2.`reference_id` = t3.`plan_id`
		                     LEFT JOIN `stability_plan` AS t4
		                         ON t1.`project_id` = t4.`project_id` AND
		                            t1.`deleted` = t4.`deleted` AND
		                            t2.`reference_type` = 'STABILITY_PLAN' AND
		                            t2.`reference_id` = t4.`plan_id`
		            WHERE t1.`project_id` = ?
		              AND t1.`udid` = ?
		              AND t1.`deleted` = ?) AS t) AS t
	*/

	var (
		aliasT  = "t"
		aliasT1 = "t1" // `project_device`
		aliasT2 = "t2" // `project_device_reference_relationship`
		aliasT3 = "t3" // `ui_plan`
		aliasT4 = "t4" // `stability_plan`

		fields = []string{
			aliasT + ".`project_id`",
			aliasT + ".`udid`",
			aliasT + ".`reference_type`",
			aliasT + ".`reference_id`",
			aliasT + ".`category_id`",
			aliasT + ".`name`",
			aliasT + ".`description`",
			aliasT + ".`maintained_by`",
			aliasT + ".`created_by`",
			aliasT + ".`updated_by`",
			aliasT + ".`created_at`",
			aliasT + ".`updated_at`",
		}
		subFields = []string{
			aliasT1 + ".`project_id`",
			aliasT1 + ".`udid`",
			aliasT2 + ".`reference_type`",
			aliasT2 + ".`reference_id`",
			fmt.Sprintf(
				"IF(%s.`reference_type` = '%s', %s.`category_id`, %s.`category_id`) AS `category_id`",
				aliasT2, common.ConstReferenceTypeUIPlan, aliasT3, aliasT4,
			),
			fmt.Sprintf(
				"IF(%s.`reference_type` = '%s', %s.`name`, %s.`name`) AS `name`",
				aliasT2, common.ConstReferenceTypeUIPlan, aliasT3, aliasT4,
			),
			fmt.Sprintf(
				"IF(%s.`reference_type` = '%s', %s.`description`, %s.`description`) AS `description`",
				aliasT2, common.ConstReferenceTypeUIPlan, aliasT3, aliasT4,
			),
			fmt.Sprintf(
				"IF(%s.`reference_type` = '%s', %s.`maintained_by`, %s.`maintained_by`) AS `maintained_by`",
				aliasT2, common.ConstReferenceTypeUIPlan, aliasT3, aliasT4,
			),
			fmt.Sprintf(
				"IF(%s.`reference_type` = '%s', %s.`created_by`, %s.`created_by`) AS `created_by`",
				aliasT2, common.ConstReferenceTypeUIPlan, aliasT3, aliasT4,
			),
			fmt.Sprintf(
				"IF(%s.`reference_type` = '%s', %s.`updated_by`, %s.`updated_by`) AS `updated_by`",
				aliasT2, common.ConstReferenceTypeUIPlan, aliasT3, aliasT4,
			),
			fmt.Sprintf(
				"IF(%s.`reference_type` = '%s', %s.`created_at`, %s.`created_at`) AS `created_at`",
				aliasT2, common.ConstReferenceTypeUIPlan, aliasT3, aliasT4,
			),
			fmt.Sprintf(
				"IF(%s.`reference_type` = '%s', %s.`updated_at`, %s.`updated_at`) AS `updated_at`",
				aliasT2, common.ConstReferenceTypeUIPlan, aliasT3, aliasT4,
			),
		}

		tmp = VirtualProjectDeviceReferenceModel
	)

	sb := squirrel.Select(fields...).
		FromSelect(
			squirrel.Select(subFields...).
				From(m.table+" AS "+aliasT1).
				InnerJoin(
					fmt.Sprintf(
						"%s AS %s ON %s.`project_id` = %s.`project_id` AND %s.`udid` = %s.`udid` AND %s.`deleted` = %s.`deleted`",
						projectDeviceReferenceRelationshipTableName, aliasT2,
						aliasT1, aliasT2,
						aliasT1, aliasT2,
						aliasT1, aliasT2,
					),
				).
				LeftJoin(
					fmt.Sprintf(
						"%s AS %s ON %s.`project_id` = %s.`project_id` AND %s.`deleted` = %s.`deleted` AND %s.`reference_type` = '%s' AND %s.`reference_id` = %s.`plan_id`",
						uiPlanTableName, aliasT3,
						aliasT1, aliasT3,
						aliasT1, aliasT3,
						aliasT2, common.ConstReferenceTypeUIPlan,
						aliasT2, aliasT3,
					),
				).
				LeftJoin(
					fmt.Sprintf(
						"%s AS %s ON %s.`project_id` = %s.`project_id` AND %s.`deleted` = %s.`deleted` AND %s.`reference_type` = '%s' AND %s.`reference_id` = %s.`plan_id`",
						stabilityPlanTableName, aliasT4,
						aliasT1, aliasT4,
						aliasT1, aliasT4,
						aliasT2, common.ConstReferenceTypeStabilityPlan,
						aliasT2, aliasT4,
					),
				).
				Where(
					fmt.Sprintf(
						"%s.`project_id` = ? AND %s.`udid` = ? AND %s.`deleted` = ?", aliasT1, aliasT1, aliasT1,
					),
					req.ProjectID, req.UDID, constants.NotDeleted,
				), aliasT,
		)

	sb = squirrel.Select("COUNT(*)").
		FromSelect(sqlbuilder.SearchOptionsWithAlias(sb, aliasT, sqlbuilder.WithCondition(tmp, req.Condition)), aliasT)

	return m.FindCount(ctx, sb)
}

func (m *customProjectDeviceModel) FindReferenceBySearchReq(
	ctx context.Context, req SearchProjectDeviceReferenceReq,
) ([]*SearchProjectDeviceReferenceItem, error) {
	/*
		SQL:
		SELECT t.`project_id`,
		       t.`udid`,
		       t.`reference_type`,
		       t.`reference_id`,
		       t.`category_id`,
		       t.`name`,
		       t.`description`,
		       t.`maintained_by`,
		       t.`created_by`,
		       t.`updated_by`,
		       t.`created_at`,
		       t.`updated_at`
		FROM (SELECT t1.`project_id`,
		             t1.`udid`,
		             t2.`reference_type`,
		             t2.`reference_id`,
		             IF(t2.`reference_type` = 'UI_PLAN', t3.`category_id`, t4.`category_id`)     AS `category_id`,
		             IF(t2.`reference_type` = 'UI_PLAN', t3.`name`, t4.`name`)                   AS `name`,
		             IF(t2.`reference_type` = 'UI_PLAN', t3.`description`, t4.`description`)     AS `description`,
		             IF(t2.`reference_type` = 'UI_PLAN', t3.`maintained_by`, t4.`maintained_by`) AS `maintained_by`,
		             IF(t2.`reference_type` = 'UI_PLAN', t3.`created_by`, t4.`created_by`)       AS `created_by`,
		             IF(t2.`reference_type` = 'UI_PLAN', t3.`updated_by`, t4.`updated_by`)       AS `updated_by`,
		             IF(t2.`reference_type` = 'UI_PLAN', t3.`created_at`, t4.`created_at`)       AS `created_at`,
		             IF(t2.`reference_type` = 'UI_PLAN', t3.`updated_at`, t4.`updated_at`)       AS `updated_at`
		      FROM `project_device` AS t1
		          INNER JOIN `project_device_reference_relationship` AS t2
		              ON t1.`project_id` = t2.`project_id` AND
		                 t1.`udid` = t2.`udid` AND
		                 t1.`deleted` = t2.`deleted`
		          LEFT JOIN `ui_plan` AS t3
		              ON t1.`project_id` = t3.`project_id` AND
		                 t1.`deleted` = t3.`deleted` AND
		                 t2.`reference_type` = 'UI_PLAN' AND
		                 t2.`reference_id` = t3.`plan_id`
		          LEFT JOIN `stability_plan` AS t4
		              ON t1.`project_id` = t4.`project_id` AND
		                 t1.`deleted` = t4.`deleted` AND
		                 t2.`reference_type` = 'STABILITY_PLAN' AND
		                 t2.`reference_id` = t4.`plan_id`
		      WHERE t1.`project_id` = ?
		        AND t1.`udid` = ?
		        AND t1.`deleted` = ?) AS t
	*/

	var (
		aliasT  = "t"
		aliasT1 = "t1" // `project_device`
		aliasT2 = "t2" // `project_device_reference_relationship`
		aliasT3 = "t3" // `ui_plan`
		aliasT4 = "t4" // `stability_plan`

		fields = []string{
			aliasT + ".`project_id`",
			aliasT + ".`udid`",
			aliasT + ".`reference_type`",
			aliasT + ".`reference_id`",
			aliasT + ".`category_id`",
			aliasT + ".`name`",
			aliasT + ".`description`",
			aliasT + ".`maintained_by`",
			aliasT + ".`created_by`",
			aliasT + ".`updated_by`",
			aliasT + ".`created_at`",
			aliasT + ".`updated_at`",
		}
		subFields = []string{
			aliasT1 + ".`project_id`",
			aliasT1 + ".`udid`",
			aliasT2 + ".`reference_type`",
			aliasT2 + ".`reference_id`",
			fmt.Sprintf(
				"IF(%s.`reference_type` = '%s', %s.`category_id`, %s.`category_id`) AS `category_id`",
				aliasT2, common.ConstReferenceTypeUIPlan, aliasT3, aliasT4,
			),
			fmt.Sprintf(
				"IF(%s.`reference_type` = '%s', %s.`name`, %s.`name`) AS `name`",
				aliasT2, common.ConstReferenceTypeUIPlan, aliasT3, aliasT4,
			),
			fmt.Sprintf(
				"IF(%s.`reference_type` = '%s', %s.`description`, %s.`description`) AS `description`",
				aliasT2, common.ConstReferenceTypeUIPlan, aliasT3, aliasT4,
			),
			fmt.Sprintf(
				"IF(%s.`reference_type` = '%s', %s.`maintained_by`, %s.`maintained_by`) AS `maintained_by`",
				aliasT2, common.ConstReferenceTypeUIPlan, aliasT3, aliasT4,
			),
			fmt.Sprintf(
				"IF(%s.`reference_type` = '%s', %s.`created_by`, %s.`created_by`) AS `created_by`",
				aliasT2, common.ConstReferenceTypeUIPlan, aliasT3, aliasT4,
			),
			fmt.Sprintf(
				"IF(%s.`reference_type` = '%s', %s.`updated_by`, %s.`updated_by`) AS `updated_by`",
				aliasT2, common.ConstReferenceTypeUIPlan, aliasT3, aliasT4,
			),
			fmt.Sprintf(
				"IF(%s.`reference_type` = '%s', %s.`created_at`, %s.`created_at`) AS `created_at`",
				aliasT2, common.ConstReferenceTypeUIPlan, aliasT3, aliasT4,
			),
			fmt.Sprintf(
				"IF(%s.`reference_type` = '%s', %s.`updated_at`, %s.`updated_at`) AS `updated_at`",
				aliasT2, common.ConstReferenceTypeUIPlan, aliasT3, aliasT4,
			),
		}

		tmp = VirtualProjectDeviceReferenceModel
	)

	sb := squirrel.Select(fields...).
		FromSelect(
			squirrel.Select(subFields...).
				From(m.table+" AS "+aliasT1).
				InnerJoin(
					fmt.Sprintf(
						"%s AS %s ON %s.`project_id` = %s.`project_id` AND %s.`udid` = %s.`udid` AND %s.`deleted` = %s.`deleted`",
						projectDeviceReferenceRelationshipTableName, aliasT2,
						aliasT1, aliasT2,
						aliasT1, aliasT2,
						aliasT1, aliasT2,
					),
				).
				LeftJoin(
					fmt.Sprintf(
						"%s AS %s ON %s.`project_id` = %s.`project_id` AND %s.`deleted` = %s.`deleted` AND %s.`reference_type` = '%s' AND %s.`reference_id` = %s.`plan_id`",
						uiPlanTableName, aliasT3,
						aliasT1, aliasT3,
						aliasT1, aliasT3,
						aliasT2, common.ConstReferenceTypeUIPlan,
						aliasT2, aliasT3,
					),
				).
				LeftJoin(
					fmt.Sprintf(
						"%s AS %s ON %s.`project_id` = %s.`project_id` AND %s.`deleted` = %s.`deleted` AND %s.`reference_type` = '%s' AND %s.`reference_id` = %s.`plan_id`",
						stabilityPlanTableName, aliasT4,
						aliasT1, aliasT4,
						aliasT1, aliasT4,
						aliasT2, common.ConstReferenceTypeStabilityPlan,
						aliasT2, aliasT4,
					),
				).
				Where(
					fmt.Sprintf(
						"%s.`project_id` = ? AND %s.`udid` = ? AND %s.`deleted` = ?", aliasT1, aliasT1, aliasT1,
					),
					req.ProjectID, req.UDID, constants.NotDeleted,
				), aliasT,
		)

	sb = sqlbuilder.SearchOptionsWithAlias(
		sb, aliasT,
		sqlbuilder.WithCondition(tmp, req.Condition),
		sqlbuilder.WithPagination(tmp, req.Pagination),
		sqlbuilder.WithSort(tmp, req.Sort),
	)

	var (
		resp []*SearchProjectDeviceReferenceItem
		err  error
	)
	err = utils.FindRows(
		ctx, m.conn, sb, func() any {
			return &resp
		},
	)
	return resp, err
}

func (m *customProjectDeviceModel) FindAllByUDID(ctx context.Context, udid string) ([]*ProjectDevice, error) {
	return m.FindNoCacheByQuery(ctx, m.SelectBuilder().Where("`udid` = ?", udid))
}

func (m *customProjectDeviceModel) DeleteByUDID(ctx context.Context, session sqlx.Session, udid string) (
	sql.Result, error,
) {
	keys := m.getKeysByUDID(ctx, udid)

	return m.ExecCtx(
		ctx, func(ctx context.Context, conn sqlx.SqlConn) (sql.Result, error) {
			/*
				SQL:
				DELETE FROM `project_device` WHERE `udid` = ?
			*/

			stmt, values, err := squirrel.Delete(m.table).
				Where("`udid` = ?", udid).
				ToSql()
			if err != nil {
				return nil, err
			}

			if session != nil {
				return session.ExecCtx(ctx, stmt, values...)
			}
			return conn.ExecCtx(ctx, stmt, values...)
		}, keys...,
	)
}

func (m *customProjectDeviceModel) getKeysByUDID(ctx context.Context, udid string) []string {
	devices, err := m.FindAllByUDID(ctx, udid)
	if err != nil {
		return make([]string, 0)
	}

	keys := make([]string, 0, len(devices)*2)
	for _, device := range devices {
		keys = append(
			keys,
			fmt.Sprintf("%s%v", cacheManagerProjectDeviceIdPrefix, device.Id),
			fmt.Sprintf("%s%v:%v", cacheManagerProjectDeviceProjectIdUdidPrefix, device.ProjectId, device.Udid),
		)
	}

	return keys
}

func (m *customProjectDeviceModel) RemoveByUDID(
	ctx context.Context, session sqlx.Session, projectID, udid string,
) (sql.Result, error) {
	keys := m.getKeysByProjectIDAndUDID(ctx, projectID, udid)

	return m.ExecCtx(
		ctx, func(ctx context.Context, conn sqlx.SqlConn) (sql.Result, error) {
			/*
				SQL:
				DELETE FROM `project_device` WHERE `project_id` = ? AND `udid` = ?
			*/

			stmt, values, err := squirrel.Delete(m.table).
				Where("`project_id` = ? AND `udid` = ?", projectID, udid).
				ToSql()
			if err != nil {
				return nil, err
			}

			if session != nil {
				return session.ExecCtx(ctx, stmt, values...)
			}

			return m.conn.ExecCtx(ctx, stmt, values...)
		}, keys...,
	)
}

func (m *customProjectDeviceModel) getKeysByProjectIDAndUDID(ctx context.Context, projectID, udid string) []string {
	device, err := m.FindOneByUDID(ctx, projectID, udid)
	if err != nil {
		return make([]string, 0)
	}

	keys := make([]string, 0, 2)
	keys = append(
		keys,
		fmt.Sprintf("%s%v", cacheManagerProjectDeviceIdPrefix, device.Id),
		fmt.Sprintf("%s%v:%v", cacheManagerProjectDeviceProjectIdUdidPrefix, device.ProjectId, device.Udid),
	)
	return keys
}
