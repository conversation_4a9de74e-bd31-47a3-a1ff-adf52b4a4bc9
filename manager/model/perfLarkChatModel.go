package model

import (
	"context"
	"database/sql"
	"fmt"

	"github.com/Masterminds/squirrel"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sqlbuilder"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/types"
)

var (
	_ PerfLarkChatModel = (*customPerfLarkChatModel)(nil)

	perfLarkChatInsertFields = stringx.Remove(
		perfLarkChatFieldNames, "`id`", "`create_time`", "`update_time`", "`deleted_by`", "`created_at`",
		"`updated_at`", "`deleted_at`",
	)
)

type (
	// PerfLarkChatModel is an interface to be customized, add more methods here,
	// and implement the added methods in customPerfLarkChatModel.
	PerfLarkChatModel interface {
		perfLarkChatModel
		types.DBModel

		Trans(ctx context.Context, fn func(context context.Context, session sqlx.Session) error) error
		InsertBuilder(data *PerfLarkChat) squirrel.InsertBuilder
		UpdateBuilder(data *PerfLarkChat) squirrel.UpdateBuilder
		SelectBuilder() squirrel.SelectBuilder
		SelectCountBuilder() squirrel.SelectBuilder
		FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error)
		FindNoCacheByQuery(ctx context.Context, selectBuilder squirrel.SelectBuilder) ([]*PerfLarkChat, error)

		FindAll(ctx context.Context, projectID string) ([]*PerfLarkChat, error)
		FindOneByChatID(ctx context.Context, projectID, chatID string) (*PerfLarkChat, error)
		FindCountBySearchReq(ctx context.Context, req SearchPerfLarkChatReq) (int64, error)
		FindAllBySearchReq(ctx context.Context, req SearchPerfLarkChatReq) ([]*PerfLarkChat, error)

		FindAllByChatID(ctx context.Context, chatID string) ([]*PerfLarkChat, error)
		UpdateByChatID(ctx context.Context, session sqlx.Session, req UpdatePerfLarkChatReq) (sql.Result, error)
		DeleteByChatID(ctx context.Context, session sqlx.Session, chatID string) (sql.Result, error)

		RemoveByChatID(ctx context.Context, session sqlx.Session, projectID, chatID string) (sql.Result, error)
	}

	customPerfLarkChatModel struct {
		*defaultPerfLarkChatModel

		conn sqlx.SqlConn
	}
)

// NewPerfLarkChatModel returns a model for the database table.
func NewPerfLarkChatModel(conn sqlx.SqlConn, c cache.CacheConf, opts ...cache.Option) PerfLarkChatModel {
	return &customPerfLarkChatModel{
		defaultPerfLarkChatModel: newPerfLarkChatModel(conn, c, opts...),
		conn:                     conn,
	}
}

func (m *customPerfLarkChatModel) Table() string {
	return m.table
}

func (m *customPerfLarkChatModel) Fields() []string {
	return perfLarkChatFieldNames
}

func (m *customPerfLarkChatModel) Trans(
	ctx context.Context, fn func(context context.Context, session sqlx.Session) error,
) error {
	return m.TransactCtx(ctx, fn)
}

func (m *customPerfLarkChatModel) InsertBuilder(data *PerfLarkChat) squirrel.InsertBuilder {
	return squirrel.Insert(m.table).Columns(perfLarkChatInsertFields...).Values(
		data.ProjectId, data.ChatId, data.Name, data.Avatar, data.Description, data.External, data.Status, data.Deleted,
		data.CreatedBy, data.UpdatedBy,
	)
}

func (m *customPerfLarkChatModel) UpdateBuilder(data *PerfLarkChat) squirrel.UpdateBuilder {
	eq := squirrel.Eq{
		"`name`":        data.Name,
		"`avatar`":      data.Avatar,
		"`description`": data.Description,
		"`external`":    data.External,
		"`status`":      data.Status,
		"`deleted`":     data.Deleted,
		"`updated_by`":  data.UpdatedBy,
		"`deleted_by`":  data.DeletedBy,
		"`deleted_at`":  data.DeletedAt,
	}
	return squirrel.Update(m.table).SetMap(eq).Where("`id` = ?", data.Id)
}

func (m *customPerfLarkChatModel) SelectBuilder() squirrel.SelectBuilder {
	return squirrel.Select(perfLarkChatFieldNames...).Where("`deleted` = ?", constants.NotDeleted).From(m.table)
}

func (m *customPerfLarkChatModel) SelectCountBuilder() squirrel.SelectBuilder {
	return squirrel.Select("count(*)").Where("`deleted` = ?", constants.NotDeleted).From(m.table)
}

func (m *customPerfLarkChatModel) FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error) {
	query, values, err := countBuilder.ToSql()
	if err != nil {
		return 0, err
	}

	var resp int64
	err = m.QueryRowNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return 0, err
	}
}

func (m *customPerfLarkChatModel) FindNoCacheByQuery(
	ctx context.Context, selectBuilder squirrel.SelectBuilder,
) ([]*PerfLarkChat, error) {
	query, values, err := selectBuilder.ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*PerfLarkChat
	err = m.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

func (m *customPerfLarkChatModel) FindAll(ctx context.Context, projectID string) ([]*PerfLarkChat, error) {
	return m.FindNoCacheByQuery(ctx, m.SelectBuilder().Where("`project_id` = ?", projectID))
}

func (m *customPerfLarkChatModel) FindOneByChatID(ctx context.Context, projectID, chatID string) (
	*PerfLarkChat, error,
) {
	resp, err := m.FindNoCacheByQuery(
		ctx, m.SelectBuilder().Where("`project_id` = ? AND `chat_id` = ?", projectID, chatID),
	)
	if err != nil {
		return nil, err
	} else if len(resp) == 0 {
		return nil, ErrNotFound
	}
	return resp[0], nil
}

func (m *customPerfLarkChatModel) FindCountBySearchReq(ctx context.Context, req SearchPerfLarkChatReq) (int64, error) {
	return m.FindCount(
		ctx, sqlbuilder.SearchOptions(
			m.SelectCountBuilder().Where("`project_id` = ?", req.ProjectID),
			sqlbuilder.WithCondition(m, req.Condition),
		),
	)
}

func (m *customPerfLarkChatModel) FindAllBySearchReq(ctx context.Context, req SearchPerfLarkChatReq) (
	[]*PerfLarkChat, error,
) {
	return m.FindNoCacheByQuery(
		ctx, sqlbuilder.SearchOptions(
			m.SelectBuilder().Where("`project_id` = ?", req.ProjectID),
			sqlbuilder.WithCondition(m, req.Condition),
			sqlbuilder.WithPagination(m, req.Pagination),
			sqlbuilder.WithSort(m, req.Sort),
		),
	)
}

func (m *customPerfLarkChatModel) FindAllByChatID(ctx context.Context, chatID string) ([]*PerfLarkChat, error) {
	return m.FindNoCacheByQuery(ctx, m.SelectBuilder().Where("`chat_id` = ?", chatID))
}

func (m *customPerfLarkChatModel) UpdateByChatID(
	ctx context.Context, session sqlx.Session, req UpdatePerfLarkChatReq,
) (sql.Result, error) {
	keys := m.getKeysByChatID(ctx, req.ChatID)

	return m.ExecCtx(
		ctx, func(ctx context.Context, conn sqlx.SqlConn) (sql.Result, error) {
			/*
				SQL:
				UPDATE `perf_lark_chat`
				SET `name` = ?, `avatar` = ?, `description` = ?, `external` = ?
				WHERE `chat_id` = ?
			*/
			stmt, values, err := squirrel.Update(m.table).
				SetMap(
					squirrel.Eq{
						"`name`":        req.Name,
						"`avatar`":      req.Avatar,
						"`description`": req.Description,
						"`external`":    req.External,
					},
				).
				Where("`chat_id` = ?", req.ChatID).
				ToSql()
			if err != nil {
				return nil, err
			}

			if session != nil {
				return session.ExecCtx(ctx, stmt, values...)
			}
			return conn.ExecCtx(ctx, stmt, values...)
		}, keys...,
	)
}

func (m *customPerfLarkChatModel) DeleteByChatID(ctx context.Context, session sqlx.Session, chatID string) (
	sql.Result, error,
) {
	keys := m.getKeysByChatID(ctx, chatID)

	return m.ExecCtx(
		ctx, func(ctx context.Context, conn sqlx.SqlConn) (sql.Result, error) {
			/*
				SQL:
				DELETE FROM `perf_lark_chat` WHERE `chat_id` = ?
			*/

			stmt, values, err := squirrel.Delete(m.table).
				Where("`chat_id` = ?", chatID).
				ToSql()
			if err != nil {
				return nil, err
			}

			if session != nil {
				return session.ExecCtx(ctx, stmt, values...)
			}
			return conn.ExecCtx(ctx, stmt, values...)
		}, keys...,
	)
}

func (m *customPerfLarkChatModel) getKeysByChatID(ctx context.Context, chatID string) []string {
	chats, err := m.FindAllByChatID(ctx, chatID)
	if err != nil {
		return make([]string, 0)
	}

	keys := make([]string, 0, len(chats))
	for _, chat := range chats {
		keys = append(keys, fmt.Sprintf("%s%v", cacheManagerPerfLarkChatIdPrefix, chat.Id))
	}

	return keys
}

func (m *customPerfLarkChatModel) RemoveByChatID(
	ctx context.Context, session sqlx.Session, projectID, chatID string,
) (sql.Result, error) {
	keys := m.getKeysByProjectIDAndChatID(ctx, projectID, chatID)

	return m.ExecCtx(
		ctx, func(ctx context.Context, conn sqlx.SqlConn) (sql.Result, error) {
			/*
				SQL:
				DELETE FROM `perf_lark_chat` WHERE `project_id` = ? AND `chat_id` = ?
			*/

			stmt, values, err := squirrel.Delete(m.table).
				Where("`project_id` = ? AND `chat_id` = ?", projectID, chatID).
				ToSql()
			if err != nil {
				return nil, err
			}

			if session != nil {
				return session.ExecCtx(ctx, stmt, values...)
			}

			return m.conn.ExecCtx(ctx, stmt, values...)
		}, keys...,
	)
}

func (m *customPerfLarkChatModel) getKeysByProjectIDAndChatID(ctx context.Context, projectID, chatID string) []string {
	chat, err := m.FindOneByChatID(ctx, projectID, chatID)
	if err != nil {
		return make([]string, 0)
	}

	keys := make([]string, 0, 1)
	keys = append(keys, fmt.Sprintf("%s%v", cacheManagerPerfLarkChatIdPrefix, chat.Id))
	return keys
}
