package model

import (
	"context"
	"database/sql"
	"fmt"
	"time"

	"github.com/Masterminds/squirrel"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/types"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/userinfo"
)

var (
	_ ApiSuiteReferenceRelationshipModel = (*customApiSuiteReferenceRelationshipModel)(nil)

	apiSuiteReferenceRelationshipInsertFields = stringx.Remove(apiSuiteReferenceRelationshipFieldNames, "`id`", "`create_time`", "`update_time`", "`deleted_by`", "`created_at`", "`updated_at`", "`deleted_at`")
)

type (
	// ApiSuiteReferenceRelationshipModel is an interface to be customized, add more methods here,
	// and implement the added methods in customApiSuiteReferenceRelationshipModel.
	ApiSuiteReferenceRelationshipModel interface {
		apiSuiteReferenceRelationshipModel
		types.DBModel

		Trans(ctx context.Context, fn func(context context.Context, session sqlx.Session) error) error
		InsertBuilder(data *ApiSuiteReferenceRelationship) squirrel.InsertBuilder
		UpdateBuilder(data *ApiSuiteReferenceRelationship) squirrel.UpdateBuilder
		SelectBuilder() squirrel.SelectBuilder
		SelectCountBuilder() squirrel.SelectBuilder
		FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error)
		FindNoCacheByQuery(ctx context.Context, selectBuilder squirrel.SelectBuilder) ([]*ApiSuiteReferenceRelationship, error)

		BatchInsertTX(ctx context.Context, session sqlx.Session, datas []*ApiSuiteReferenceRelationship) (
			sql.Result, error,
		)
		FindReferenceBySuiteId(
			ctx context.Context, projectId, suiteId string,
		) ([]*ApiSuiteReferenceRelationship, error)
		FindReferenceByReference(
			ctx context.Context, projectId, referenceType, referenceId string,
		) ([]*ApiSuiteReferenceRelationship, error)
		RemoveBySuiteId(
			ctx context.Context, session sqlx.Session, projectId, suiteId string,
		) (sql.Result, error)
	}

	customApiSuiteReferenceRelationshipModel struct {
		*defaultApiSuiteReferenceRelationshipModel

		conn sqlx.SqlConn
	}
)

// NewApiSuiteReferenceRelationshipModel returns a model for the database table.
func NewApiSuiteReferenceRelationshipModel(conn sqlx.SqlConn, c cache.CacheConf, opts ...cache.Option) ApiSuiteReferenceRelationshipModel {
	return &customApiSuiteReferenceRelationshipModel{
		defaultApiSuiteReferenceRelationshipModel: newApiSuiteReferenceRelationshipModel(conn, c, opts...),
		conn: conn,
	}
}

func (m *customApiSuiteReferenceRelationshipModel) Table() string {
	return m.table
}

func (m *customApiSuiteReferenceRelationshipModel) Fields() []string {
	return apiSuiteReferenceRelationshipFieldNames
}

func (m *customApiSuiteReferenceRelationshipModel) Trans(ctx context.Context, fn func(context context.Context, session sqlx.Session) error) error {
	return m.TransactCtx(ctx, fn)
}

func (m *customApiSuiteReferenceRelationshipModel) InsertBuilder(data *ApiSuiteReferenceRelationship) squirrel.InsertBuilder {
	return squirrel.Insert(m.table).Columns(apiSuiteReferenceRelationshipInsertFields...).Values(
		data.ProjectId, data.ReferenceType, data.ReferenceId, data.SuiteId, data.Deleted, data.CreatedBy, data.UpdatedBy,
	)
}

func (m *customApiSuiteReferenceRelationshipModel) UpdateBuilder(data *ApiSuiteReferenceRelationship) squirrel.UpdateBuilder {
	eq := squirrel.Eq{
		"`deleted`":    data.Deleted,
		"`updated_by`": data.UpdatedBy,
		"`deleted_by`": data.DeletedBy,
		"`deleted_at`": data.DeletedAt,
	}
	return squirrel.Update(m.table).SetMap(eq).Where("`id` = ?", data.Id)
}

func (m *customApiSuiteReferenceRelationshipModel) SelectBuilder() squirrel.SelectBuilder {
	return squirrel.Select(apiSuiteReferenceRelationshipFieldNames...).Where("`deleted` = ?", constants.NotDeleted).From(m.table)
}

func (m *customApiSuiteReferenceRelationshipModel) SelectCountBuilder() squirrel.SelectBuilder {
	return squirrel.Select("count(*)").Where("`deleted` = ?", constants.NotDeleted).From(m.table)
}

func (m *customApiSuiteReferenceRelationshipModel) FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error) {
	query, values, err := countBuilder.ToSql()
	if err != nil {
		return 0, err
	}

	var resp int64
	err = m.QueryRowNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return 0, err
	}
}

func (m *customApiSuiteReferenceRelationshipModel) FindNoCacheByQuery(ctx context.Context, selectBuilder squirrel.SelectBuilder) ([]*ApiSuiteReferenceRelationship, error) {
	query, values, err := selectBuilder.ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*ApiSuiteReferenceRelationship
	err = m.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

func (m *customApiSuiteReferenceRelationshipModel) BatchInsertTX(
	ctx context.Context, session sqlx.Session, datas []*ApiSuiteReferenceRelationship,
) (sql.Result, error) {
	dataLen := len(datas)
	if dataLen == 0 {
		return nil, nil
	} else if dataLen == 1 {
		return m.Insert(ctx, session, datas[0])
	}

	ib := squirrel.Insert(m.table).Columns(apiSuiteReferenceRelationshipInsertFields...)

	for _, data := range datas {
		ib = ib.Values(
			data.ProjectId, data.ReferenceType, data.ReferenceId, data.SuiteId, data.Deleted, data.CreatedBy,
			data.UpdatedBy,
		)
	}

	return m.ExecCtx(
		ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
			query, values, err := ib.ToSql()
			if err != nil {
				return nil, err
			}
			if session != nil {
				return session.ExecCtx(ctx, query, values...)
			}
			return conn.ExecCtx(ctx, query, values...)
		},
	)
}

func (m *customApiSuiteReferenceRelationshipModel) FindReferenceBySuiteId(
	ctx context.Context, projectId, suiteId string,
) ([]*ApiSuiteReferenceRelationship, error) {
	sb := m.SelectBuilder().Where(
		"`project_id` = ? AND `suite_id` = ?", projectId, suiteId,
	)

	return m.FindNoCacheByQuery(ctx, sb)
}

func (m *customApiSuiteReferenceRelationshipModel) FindReferenceByReference(
	ctx context.Context, projectId, referenceType, referenceId string,
) ([]*ApiSuiteReferenceRelationship, error) {
	sb := m.SelectBuilder().Where(
		"`project_id` = ? AND `reference_type` = ? AND `reference_id` = ?", projectId, referenceType, referenceId,
	)

	return m.FindNoCacheByQuery(ctx, sb)
}

func (m *customApiSuiteReferenceRelationshipModel) RemoveBySuiteId(
	ctx context.Context, session sqlx.Session, projectId, suiteId string,
) (sql.Result, error) {
	keys := m.getKeysBySuiteId(ctx, projectId, suiteId)

	return m.ExecCtx(
		ctx, func(ctx context.Context, conn sqlx.SqlConn) (sql.Result, error) {
			/*
				SQL:
				UPDATE `api_suite_reference_relationship`
				SET `deleted` = ?, `deleted_by` = ?, `deleted_at` = ?
				WHERE `project_id` = ?
				  AND `suite_id` = ?
				  AND `deleted` = ?
			*/
			stmt, values, err := squirrel.Update(m.table).
				SetMap(
					squirrel.Eq{
						"`deleted`":    constants.HasDeleted,
						"`deleted_by`": userinfo.FromContext(ctx).Account,
						"`deleted_at`": time.Now(),
					},
				).
				Where(
					"`project_id` = ? AND `suite_id` = ? AND `deleted` = ?",
					projectId, suiteId, constants.NotDeleted,
				).
				ToSql()
			if err != nil {
				return nil, err
			}

			if session != nil {
				return session.ExecCtx(ctx, stmt, values...)
			}
			return conn.ExecCtx(ctx, stmt, values...)
		}, keys...,
	)
}

func (m *customApiSuiteReferenceRelationshipModel) getKeysBySuiteId(
	ctx context.Context, projectId, suiteId string,
) []string {
	cs, err := m.FindReferenceBySuiteId(ctx, projectId, suiteId)
	if err != nil {
		return make([]string, 0)
	}

	keys := make([]string, 0, len(cs))
	for _, c := range cs {
		keys = append(keys, fmt.Sprintf("%s%v", cacheManagerApiSuiteReferenceRelationshipIdPrefix, c.Id))
	}

	return keys
}
