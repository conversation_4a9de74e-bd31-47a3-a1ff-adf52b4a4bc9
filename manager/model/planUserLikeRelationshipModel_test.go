package model

import (
	"context"
	"fmt"
	"testing"

	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/redis"
	"github.com/zeromicro/go-zero/core/stores/sqlx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sqlbuilder/rpc"

	pb2 "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

func MockMysql() sqlx.SqlConn {
	// source := "root:Quwan@2020@tcp(127.0.0.1:3306)/manager?charset=utf8mb4&parseTime=true&loc=Asia%2FShanghai"
	source := "probe:Quwan@2020_TTinternation@tcp(************:3306)/manager?charset=utf8mb4&parseTime=true&loc=Asia%2FShanghai"
	return sqlx.NewMysql(source)
}

func MockRedisConf() redis.RedisConf {
	return redis.RedisConf{
		Host: "***************:6379",
		Type: "node",
		DB:   2,
	}
}

func MockCache() cache.CacheConf {
	return cache.CacheConf{
		{
			RedisConf: MockRedisConf(),
			Weight:    100,
		},
	}
}

var str = `{
  "project_id": "project_id:Kqllt5-9fA-I5UOdhjA5d",
  "pagination": {
    "current_page": 1,
    "page_size": 20
  },
  "condition": {
    "single": null,
    "group": {
      "relationship": "AND",
      "conditions": [
        {
          "single": {
            "field": "name",
            "compare": "LIKE",
            "other": {
              "value": "test"
            }
          },
          "group": null
        },
        {
          "single": {
            "field": "maintained_by",
            "compare": "IN",
            "in": [
              "T4440"
            ]
          },
          "group": null
        }
      ]
    }
  },
  "sort": [
    {
      "field": "updated_at",
      "order": "DESC"
    }
  ]
}`

func TestGenerateApiPlanJoinPlanLikeSqlBuilderForProjectIdAndQuery(t *testing.T) {
	userLikeRelationshipModel := NewPlanUserLikeRelationshipModel(MockMysql(), MockCache())
	query := new(pb.SearchApiPlanReq)
	err := protobuf.UnmarshalJSON([]byte(str), query)
	if err != nil {
		fmt.Println(err)
		return
	}

	searchApiPlanReq := SearchApiPlanReq{
		ProjectId:  query.GetProjectId(),
		Condition:  query.GetCondition(),
		Pagination: query.GetPagination(),
		Sort:       rpc.ConvertSortFields(query.GetSort()),
	}

	andQuery, countBuilder := userLikeRelationshipModel.GenerateApiPlanJoinPlanLikeSqlBuilderForProjectIdAndQuery(
		"project_id:Kqllt5-9fA-I5UOdhjA5d", "T4440", searchApiPlanReq,
	)
	byQuery, err := userLikeRelationshipModel.FindNoCacheJoinApiPlanByQuery(
		context.Background(), andQuery.SelectBuilder,
	)
	if err != nil {
		fmt.Println(err)
		return
	}
	fmt.Println(byQuery)
	count, err := userLikeRelationshipModel.FindCount(context.Background(), countBuilder.SelectBuilder)
	if err != nil {
		fmt.Println(err)
		return
	}
	fmt.Println(count)
}

func TestGenerateUiPlanJoinPlanLikeSqlBuilderForProjectIdAndQuery(t *testing.T) {
	userLikeRelationshipModel := NewPlanUserLikeRelationshipModel(MockMysql(), MockCache())
	query := new(pb.SearchApiPlanReq)
	err := protobuf.UnmarshalJSON([]byte(str), query)
	if err != nil {
		fmt.Println(err)
		return
	}

	searchUiPlanReq := SearchUiPlanReq{
		ProjectId:  query.GetProjectId(),
		Condition:  query.GetCondition(),
		Pagination: query.GetPagination(),
		Sort:       rpc.ConvertSortFields(query.GetSort()),
	}

	andQuery, countBuilder := userLikeRelationshipModel.GenerateUiPlanJoinPlanLikeSqlBuilderForProjectIdAndQuery(
		"project_id:Kqllt5-9fA-I5UOdhjA5d", "T4440", searchUiPlanReq,
	)
	byQuery, err := userLikeRelationshipModel.FindNoCacheJoinUiPlanByQuery(
		context.Background(), andQuery.SelectBuilder,
	)
	if err != nil {
		fmt.Println(err)
		return
	}
	fmt.Println(byQuery)
	count, err := userLikeRelationshipModel.FindCount(context.Background(), countBuilder.SelectBuilder)
	if err != nil {
		fmt.Println(err)
		return
	}
	fmt.Println(count)
}

func TestDeleteByProjectIdAndPlanIdAndPlanType(t *testing.T) {
	userLikeRelationshipModel := NewPlanUserLikeRelationshipModel(MockMysql(), MockCache())
	err := userLikeRelationshipModel.Trans(
		context.Background(), func(context context.Context, session sqlx.Session) error {
			err := userLikeRelationshipModel.DeleteByProjectIdAndPlanIdAndPlanType(
				context, session, "project_id:Kqllt5-9fA-I5UOdhjA5d", "ui_plan_id:1KilaSm-LlAQxo-R2YPfL",
				pb2.PlanType_UI,
			)
			return err
		},
	)
	if err != nil {
		fmt.Println(err)
	}
}
