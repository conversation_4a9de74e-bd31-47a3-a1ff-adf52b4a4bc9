package model

import (
	"context"
	"database/sql"
	"fmt"
	"time"

	"github.com/Masterminds/squirrel"
	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sqlbuilder"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/types"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/userinfo"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/utils"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
)

var (
	_                                      InterfaceDocumentModel = (*customInterfaceDocumentModel)(nil)
	VirtualInterfaceDocumentReferenceModel types.DBModel          = (*virtualInterfaceDocumentReferenceModel)(nil)

	interfaceDocumentInsertFields = stringx.Remove(
		interfaceDocumentFieldNames, "`id`", "`create_time`", "`update_time`", "`deleted_by`", "`created_at`",
		"`updated_at`", "`deleted_at`",
	)

	cacheManagerInterfaceDocumentProjectIdNamePrefix = "cache:manager:interfaceDocument:projectId:name:"

	virtualInterfaceDocumentReferenceFieldNames = builder.RawFieldNames(&SearchInterfaceDocumentReferenceItem{})
)

type (
	// InterfaceDocumentModel is an interface to be customized, add more methods here,
	// and implement the added methods in customInterfaceDocumentModel.
	InterfaceDocumentModel interface {
		interfaceDocumentModel
		types.DBModel

		Trans(ctx context.Context, fn func(context context.Context, session sqlx.Session) error) error
		InsertBuilder(data *InterfaceDocument) squirrel.InsertBuilder
		UpdateBuilder(data *InterfaceDocument) squirrel.UpdateBuilder
		SelectBuilder() squirrel.SelectBuilder
		SelectCountBuilder() squirrel.SelectBuilder
		FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error)
		FindNoCacheByQuery(ctx context.Context, builder squirrel.SelectBuilder) ([]*InterfaceDocument, error)

		InsertTX(ctx context.Context, session sqlx.Session, data *InterfaceDocument) (sql.Result, error)
		UpdateTX(ctx context.Context, session sqlx.Session, data *InterfaceDocument) (sql.Result, error)
		FindAll(ctx context.Context, projectId string) ([]*InterfaceDocument, error)
		FindOneByProjectIdName(ctx context.Context, projectId, name string) (*InterfaceDocument, error)
		FindCountByCategoryId(ctx context.Context, cm CategoryModel, cond FindCountByCategoryCondition) (int64, error)
		RemoveByDocumentId(ctx context.Context, session sqlx.Session, projectId, documentId string) (sql.Result, error)

		GenerateSearchInterfaceDocumentReferenceSqlBuilder(req SearchInterfaceDocumentReferenceReq) (
			searchInterfaceDocumentReferenceSelectBuilder, searchInterfaceDocumentReferenceCountBuilder,
		)
		FindCountInterfaceDocumentReference(
			ctx context.Context, countBuilder searchInterfaceDocumentReferenceCountBuilder,
		) (int64, error)
		FindInterfaceDocumentReference(
			ctx context.Context, selectBuilder searchInterfaceDocumentReferenceSelectBuilder,
		) ([]*SearchInterfaceDocumentReferenceItem, error)
	}

	customInterfaceDocumentModel struct {
		*defaultInterfaceDocumentModel
		conn sqlx.SqlConn
	}
)

// NewInterfaceDocumentModel returns a model for the database table.
func NewInterfaceDocumentModel(conn sqlx.SqlConn, c cache.CacheConf) InterfaceDocumentModel {
	return &customInterfaceDocumentModel{
		defaultInterfaceDocumentModel: newInterfaceDocumentModel(conn, c),
		conn:                          conn,
	}
}

func (m *customInterfaceDocumentModel) Table() string {
	return m.table
}

func (m *customInterfaceDocumentModel) Fields() []string {
	return interfaceDocumentFieldNames
}

func (m *customInterfaceDocumentModel) Trans(
	ctx context.Context, fn func(context context.Context, session sqlx.Session) error,
) error {
	return m.TransactCtx(ctx, fn)
}

func (m *customInterfaceDocumentModel) InsertBuilder(data *InterfaceDocument) squirrel.InsertBuilder {
	return squirrel.Insert(m.table).Columns(interfaceDocumentInsertFields...).Values(
		data.ProjectId, data.CategoryId, data.DocumentId, data.Name, data.Description, data.Type, data.Mode,
		data.ImportType, data.Status, data.Priority, data.Tags, data.State, data.CaseExecutionMode, data.Service,
		data.Path, data.Method, data.Data, data.Deleted, data.MaintainedBy, data.CreatedBy, data.UpdatedBy,
	)
}

func (m *customInterfaceDocumentModel) UpdateBuilder(data *InterfaceDocument) squirrel.UpdateBuilder {
	eq := squirrel.Eq{
		"`category_id`":         data.CategoryId,
		"`name`":                data.Name,
		"`description`":         data.Description,
		"`status`":              data.Status,
		"`priority`":            data.Priority,
		"`tags`":                data.Tags,
		"`state`":               data.State,
		"`case_execution_mode`": data.CaseExecutionMode,
		"`service`":             data.Service,
		"`path`":                data.Path,
		"`method`":              data.Method,
		"`data`":                data.Data,
		"`deleted`":             data.Deleted,
		"`maintained_by`":       data.MaintainedBy,
		"`updated_by`":          data.UpdatedBy,
		"`deleted_by`":          data.DeletedBy,
		"`deleted_at`":          data.DeletedAt,
	}
	return squirrel.Update(m.table).SetMap(eq).Where("`id` = ?", data.Id)
}

func (m *customInterfaceDocumentModel) SelectBuilder() squirrel.SelectBuilder {
	return squirrel.Select(interfaceDocumentFieldNames...).Where("`deleted` = ?", constants.NotDeleted).From(m.table)
}

func (m *customInterfaceDocumentModel) SelectCountBuilder() squirrel.SelectBuilder {
	return squirrel.Select("count(*)").Where("`deleted` = ?", constants.NotDeleted).From(m.table)
}

func (m *customInterfaceDocumentModel) FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (
	int64, error,
) {
	query, values, err := countBuilder.ToSql()
	if err != nil {
		return 0, err
	}

	var resp int64
	err = m.QueryRowNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return 0, err
	}
}

func (m *customInterfaceDocumentModel) FindNoCacheByQuery(
	ctx context.Context, builder squirrel.SelectBuilder,
) ([]*InterfaceDocument, error) {
	query, values, err := builder.ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*InterfaceDocument
	err = m.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

func (m *customInterfaceDocumentModel) InsertTX(
	ctx context.Context, session sqlx.Session, data *InterfaceDocument,
) (sql.Result, error) {
	managerInterfaceDocumentIdKey := fmt.Sprintf("%s%v", cacheManagerInterfaceDocumentIdPrefix, data.Id)
	managerInterfaceDocumentProjectIdDocumentIdKey := fmt.Sprintf(
		"%s%v:%v", cacheManagerInterfaceDocumentProjectIdDocumentIdPrefix, data.ProjectId, data.DocumentId,
	)
	managerInterfaceDocumentProjectIdNameKey := fmt.Sprintf(
		"%s%v:%v", cacheManagerInterfaceDocumentProjectIdNamePrefix, data.ProjectId, data.Name,
	)

	return m.ExecCtx(
		ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
			query, values, err := m.InsertBuilder(data).ToSql()
			if err != nil {
				return nil, err
			}
			if session != nil {
				return session.ExecCtx(ctx, query, values...)
			}
			return conn.ExecCtx(ctx, query, values...)
		}, managerInterfaceDocumentIdKey, managerInterfaceDocumentProjectIdDocumentIdKey,
		managerInterfaceDocumentProjectIdNameKey,
	)
}

func (m *customInterfaceDocumentModel) UpdateTX(
	ctx context.Context, session sqlx.Session, newData *InterfaceDocument,
) (sql.Result, error) {
	data, err := m.FindOne(ctx, newData.Id)
	if err != nil {
		return nil, err
	}

	managerInterfaceDocumentIdKey := fmt.Sprintf("%s%v", cacheManagerInterfaceDocumentIdPrefix, data.Id)
	managerInterfaceDocumentProjectIdDocumentIdKey := fmt.Sprintf(
		"%s%v:%v", cacheManagerInterfaceDocumentProjectIdDocumentIdPrefix, data.ProjectId, data.DocumentId,
	)
	managerInterfaceDocumentProjectIdNameKey := fmt.Sprintf(
		"%s%v:%v", cacheManagerInterfaceDocumentProjectIdNamePrefix, data.ProjectId, data.Name,
	)

	return m.ExecCtx(
		ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
			query, values, err := m.UpdateBuilder(newData).ToSql()
			if err != nil {
				return nil, err
			}
			if session != nil {
				return session.ExecCtx(ctx, query, values...)
			}
			return conn.ExecCtx(ctx, query, values...)
		}, managerInterfaceDocumentIdKey, managerInterfaceDocumentProjectIdDocumentIdKey,
		managerInterfaceDocumentProjectIdNameKey,
	)
}

func (m *customInterfaceDocumentModel) FindAll(ctx context.Context, projectId string) ([]*InterfaceDocument, error) {
	return m.FindNoCacheByQuery(ctx, m.SelectBuilder().Where("`project_id` = ?", projectId))
}

func (m *customInterfaceDocumentModel) FindOneByProjectIdName(
	ctx context.Context, projectId, name string,
) (*InterfaceDocument, error) {
	managerInterfaceDocumentProjectIdNameKey := fmt.Sprintf(
		"%s%v:%v", cacheManagerInterfaceDocumentProjectIdNamePrefix, projectId, name,
	)

	var resp InterfaceDocument
	err := m.QueryRowIndexCtx(
		ctx, &resp, managerInterfaceDocumentProjectIdNameKey, m.formatPrimary,
		func(ctx context.Context, conn sqlx.SqlConn, v any) (any, error) {
			query, values, err := m.SelectBuilder().Where(
				"`project_id` = ? and `name` = ?", projectId, name,
			).OrderBy("`updated_at` DESC").Limit(1).ToSql()
			if err != nil {
				return nil, err
			}

			if err = conn.QueryRowCtx(ctx, &resp, query, values...); err != nil {
				return nil, err
			}

			return resp.Id, nil
		}, m.queryPrimary,
	)
	switch err {
	case nil:
		return &resp, nil
	default:
		return nil, err
	}
}

func (m *customInterfaceDocumentModel) FindCountByCategoryId(
	ctx context.Context, cm CategoryModel, cond FindCountByCategoryCondition,
) (int64, error) {
	alias := "t"
	sb := squirrel.Select("COUNT(*)").
		From(fmt.Sprintf("%s AS %s", m.Table(), alias)).
		Where(
			fmt.Sprintf("%s.`project_id` = ? AND %s.`deleted` = ?", alias, alias), cond.ProjectId, constants.NotDeleted,
		)

	if cond.DrillDown {
		sub := cm.FindDescendantCategoriesSqlBuilder(
			GetCategoryTreeCondition{
				ProjectId:     cond.ProjectId,
				Type:          common.ConstCategoryTreeTypeInterfaceDocument,
				CategoryId:    cond.CategoryId,
				OnlyDirectory: true,
			},
		)

		sb = sb.JoinClause(
			sub.Prefix("LEFT JOIN (").Suffix(
				fmt.Sprintf(
					") AS t1 ON %s.`category_id` = t1.`category_id`", alias,
				),
			),
		).Where("t1.`category_id` IS NOT NULL")
	} else {
		sb = sb.Where(fmt.Sprintf("%s.`category_id` = ?", alias), cond.CategoryId)
	}

	return m.FindCount(ctx, sb)
}

func (m *customInterfaceDocumentModel) RemoveByDocumentId(
	ctx context.Context, session sqlx.Session, projectId, documentId string,
) (sql.Result, error) {
	keys := m.getKeysByDocumentId(ctx, projectId, documentId)

	return m.ExecCtx(
		ctx, func(ctx context.Context, conn sqlx.SqlConn) (sql.Result, error) {
			/*
				SQL:
				UPDATE `interface_document`
				SET `deleted` = ?, `deleted_by` = ?, `deleted_at` = ?
				WHERE `project_id` = ?
				  AND `document_id` = ?
			*/
			stmt, values, err := squirrel.Update(m.table).
				SetMap(
					squirrel.Eq{
						"`deleted`":    constants.HasDeleted,
						"`deleted_by`": userinfo.FromContext(ctx).Account,
						"`deleted_at`": time.Now(),
					},
				).
				Where("`project_id` = ? AND `document_id` = ?", projectId, documentId).
				ToSql()
			if err != nil {
				return nil, err
			}

			if session != nil {
				return session.ExecCtx(ctx, stmt, values...)
			}
			return conn.ExecCtx(ctx, stmt, values...)
		}, keys...,
	)
}

func (m *customInterfaceDocumentModel) getKeysByDocumentId(ctx context.Context, projectId, documentId string) []string {
	sb := m.SelectBuilder().Where("`project_id` = ? AND `document_id` = ?", projectId, documentId)
	ds, err := m.FindNoCacheByQuery(ctx, sb)
	if err != nil {
		return make([]string, 0)
	}

	keys := make([]string, 0, len(ds)*3)
	for _, d := range ds {
		keys = append(
			keys,
			fmt.Sprintf("%s%v", cacheManagerInterfaceDocumentIdPrefix, d.Id),
			fmt.Sprintf("%s%v:%v", cacheManagerInterfaceDocumentProjectIdDocumentIdPrefix, d.ProjectId, d.DocumentId),
			fmt.Sprintf("%s%v:%v", cacheManagerInterfaceDocumentProjectIdNamePrefix, d.ProjectId, d.Name),
		)
	}

	return keys
}

type virtualInterfaceDocumentReferenceModel struct{}

func (m *virtualInterfaceDocumentReferenceModel) Table() string {
	return "`virtual_interface_document_reference`"
}

func (m *virtualInterfaceDocumentReferenceModel) Fields() []string {
	return virtualInterfaceDocumentReferenceFieldNames
}

type SearchInterfaceDocumentReferenceItem struct {
	ProjectId      string         `db:"project_id"`      // 项目ID
	DocumentId     string         `db:"document_id"`     // 接口ID
	ReferenceType  string         `db:"reference_type"`  // 引用对象类型（API计划）
	ReferenceId    string         `db:"reference_id"`    // 引用对象ID（API计划ID）
	Name           string         `db:"name"`            // 引用对象名称
	Description    sql.NullString `db:"description"`     // 引用对象描述
	Priority       int64          `db:"priority"`        // 优先级（NULL、P0、P1、P2、P3...）
	Tags           sql.NullString `db:"tags"`            // 标签
	State          int64          `db:"state"`           // 引用对象状态（生效、失效）
	ReferenceState int64          `db:"reference_state"` // 集合的引用状态
	MaintainedBy   sql.NullString `db:"maintained_by"`   // 维护者的用户ID
	CreatedBy      string         `db:"created_by"`      // 创建者的用户ID
	UpdatedBy      string         `db:"updated_by"`      // 最近一次更新者的用户ID
	CreatedAt      time.Time      `db:"created_at"`      // 创建时间
	UpdatedAt      time.Time      `db:"updated_at"`      // 更新时间
}

type searchInterfaceDocumentReferenceSelectBuilder struct {
	squirrel.SelectBuilder
}

type searchInterfaceDocumentReferenceCountBuilder struct {
	squirrel.SelectBuilder
}

func (m *customInterfaceDocumentModel) GenerateSearchInterfaceDocumentReferenceSqlBuilder(req SearchInterfaceDocumentReferenceReq) (
	searchInterfaceDocumentReferenceSelectBuilder, searchInterfaceDocumentReferenceCountBuilder,
) {
	/*
		SQL:
		SELECT t.`project_id`,
		       t.`document_id`,
		       t.`reference_type`,
		       t.`reference_id`,
		       t.`name`,
		       t.`description`,
		       t.`priority`,
		       t.`tags`,
		       t.`state`,
		       t.`reference_state`,
		       t.`maintained_by`,
		       t.`created_by`,
		       t.`updated_by`,
		       t.`created_at`,
		       t.`updated_at`
		FROM (SELECT t1.`project_id`,
		             t1.`document_id`,
		             'API_PLAN'   AS `reference_type`,
		             t3.`plan_id` AS `reference_id`,
		             t3.`name`,
		             t3.`description`,
		             t3.`priority`,
		             t3.`tags`,
		             t3.`state`,
		             t2.`state`   AS `reference_state`,
		             t3.`maintained_by`,
		             t3.`created_by`,
		             t3.`updated_by`,
		             t3.`created_at`,
		             t3.`updated_at`
		      FROM `interface_document` AS t1
		               INNER JOIN `api_plan_reference_relationship` AS t2 ON t1.`project_id` = t2.`project_id` AND t2.`reference_type` = 'INTERFACE_DOCUMENT' AND t1.`document_id` = t2.`reference_id` AND t1.`deleted` = t2.`deleted`
		               LEFT JOIN `api_plan` AS t3 ON t1.`project_id` = t3.`project_id` AND t2.`plan_id` = t3.`plan_id` AND t1.`deleted` = t3.`deleted`
		      WHERE t1.`project_id` = ?
		        AND t1.`document_id` = ?
		        AND t1.`deleted` = 0
		        AND t3.`id` IS NOT NULL) AS t
	*/

	fields := []string{
		"t1.`project_id`",
		"t1.`document_id`",
		fmt.Sprintf("'%s' AS `reference_type`", common.ConstReferenceTypeApiPlan),
		"t3.`plan_id` AS `reference_id`",
		"t3.`name`",
		"t3.`description`",
		"t3.`priority`",
		"t3.`tags`",
		"t3.`state`",
		"t2.`state` AS `reference_state`",
		"t3.`maintained_by`",
		"t3.`created_by`",
		"t3.`updated_by`",
		"t3.`created_at`",
		"t3.`updated_at`",
	}
	tmp := squirrel.Select(fields...).From(m.table+" AS t1").
		InnerJoin(
			fmt.Sprintf(
				"%s AS t2 ON t1.`project_id` = t2.`project_id` AND t2.`reference_type` = '%s' AND t1.`document_id` = t2.`reference_id` AND t1.`deleted` = t2.`deleted`",
				apiPlanReferenceRelationshipTableName, common.ConstReferenceTypeInterfaceDocument,
			),
		).
		LeftJoin(apiPlanTableName+" AS t3 ON t1.`project_id` = t3.`project_id` AND t2.`plan_id` = t3.`plan_id` AND t1.`deleted` = t3.`deleted`").
		Where(
			"t1.`project_id` = ? AND t1.`document_id` = ? AND t1.`deleted` = ? AND t3.`id` IS NOT NULL", req.ProjectId,
			req.DocumentId, constants.NotDeleted,
		)

	rm := VirtualInterfaceDocumentReferenceModel

	alias := "t"
	sb := sqlbuilder.SearchOptionsWithAlias(
		squirrel.Select(utils.AddTableNameToFields(alias, rm.Fields())...).FromSelect(tmp, alias),
		alias,
		sqlbuilder.WithCondition(rm, req.Condition),
		sqlbuilder.WithPagination(rm, req.Pagination),
		sqlbuilder.WithSort(rm, req.Sort),
	)
	scb := sqlbuilder.SearchOptionsWithAlias(
		squirrel.Select("COUNT(*)").FromSelect(tmp, alias),
		alias,
		sqlbuilder.WithCondition(rm, req.Condition),
	)

	return searchInterfaceDocumentReferenceSelectBuilder{sb}, searchInterfaceDocumentReferenceCountBuilder{scb}
}

func (m *customInterfaceDocumentModel) FindCountInterfaceDocumentReference(
	ctx context.Context, countBuilder searchInterfaceDocumentReferenceCountBuilder,
) (int64, error) {
	return m.FindCount(ctx, countBuilder.SelectBuilder)
}

func (m *customInterfaceDocumentModel) FindInterfaceDocumentReference(
	ctx context.Context, selectBuilder searchInterfaceDocumentReferenceSelectBuilder,
) ([]*SearchInterfaceDocumentReferenceItem, error) {
	var resp []*SearchInterfaceDocumentReferenceItem

	err := utils.FindRows(
		ctx, m.conn, selectBuilder.SelectBuilder, func() any {
			return &resp
		},
	)

	return resp, err
}

// [2024-04-16] delete this method:
// Validate 判断接口集合是否可被执行
//func (x *InterfaceDocument) Validate() bool {
//	// 接口集合固有状态为生效才能被执行
//	return x.State == int64(constants.EnableStatus)
//}
