package model

import (
	"context"
	"database/sql"
	"fmt"
	"time"

	"github.com/Masterminds/squirrel"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/types"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/userinfo"
)

var (
	_ PerfPlanReferenceRelationshipModel = (*customPerfPlanReferenceRelationshipModel)(nil)

	perfPlanReferenceRelationshipInsertFields = stringx.Remove(
		perfPlanReferenceRelationshipFieldNames, "`id`", "`create_time`", "`update_time`", "`deleted_by`",
		"`created_at`", "`updated_at`", "`deleted_at`",
	)
)

type (
	// PerfPlanReferenceRelationshipModel is an interface to be customized, add more methods here,
	// and implement the added methods in customPerfPlanReferenceRelationshipModel.
	PerfPlanReferenceRelationshipModel interface {
		perfPlanReferenceRelationshipModel
		types.DBModel

		Trans(ctx context.Context, fn func(context context.Context, session sqlx.Session) error) error
		InsertBuilder(data *PerfPlanReferenceRelationship) squirrel.InsertBuilder
		UpdateBuilder(data *PerfPlanReferenceRelationship) squirrel.UpdateBuilder
		SelectBuilder() squirrel.SelectBuilder
		SelectCountBuilder() squirrel.SelectBuilder
		FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error)
		FindNoCacheByQuery(ctx context.Context, selectBuilder squirrel.SelectBuilder) (
			[]*PerfPlanReferenceRelationship, error,
		)

		BatchInsert(ctx context.Context, session sqlx.Session, items []*PerfPlanReferenceRelationship) (
			sql.Result, error,
		)
		FindReferenceByReference(
			ctx context.Context, projectID, referenceType, referenceID string,
		) ([]*PerfPlanReferenceRelationship, error)
		FindReferenceByPlanID(ctx context.Context, projectID, planID string) ([]*PerfPlanReferenceRelationship, error)
		RemoveByPlanID(ctx context.Context, session sqlx.Session, projectID, planID string) (sql.Result, error)
	}

	customPerfPlanReferenceRelationshipModel struct {
		*defaultPerfPlanReferenceRelationshipModel

		conn sqlx.SqlConn
	}
)

// NewPerfPlanReferenceRelationshipModel returns a model for the database table.
func NewPerfPlanReferenceRelationshipModel(
	conn sqlx.SqlConn, c cache.CacheConf, opts ...cache.Option,
) PerfPlanReferenceRelationshipModel {
	return &customPerfPlanReferenceRelationshipModel{
		defaultPerfPlanReferenceRelationshipModel: newPerfPlanReferenceRelationshipModel(conn, c, opts...),
		conn: conn,
	}
}

func (m *customPerfPlanReferenceRelationshipModel) Table() string {
	return m.table
}

func (m *customPerfPlanReferenceRelationshipModel) Fields() []string {
	return perfPlanReferenceRelationshipFieldNames
}

func (m *customPerfPlanReferenceRelationshipModel) Trans(
	ctx context.Context, fn func(context context.Context, session sqlx.Session) error,
) error {
	return m.TransactCtx(ctx, fn)
}

func (m *customPerfPlanReferenceRelationshipModel) InsertBuilder(data *PerfPlanReferenceRelationship) squirrel.InsertBuilder {
	return squirrel.Insert(m.table).Columns(perfPlanReferenceRelationshipInsertFields...).Values(
		data.ProjectId, data.ReferenceType, data.ReferenceId, data.PlanId, data.State, data.Deleted, data.CreatedBy,
		data.UpdatedBy,
	)
}

func (m *customPerfPlanReferenceRelationshipModel) UpdateBuilder(data *PerfPlanReferenceRelationship) squirrel.UpdateBuilder {
	eq := squirrel.Eq{
		"`state`":      data.State,
		"`deleted`":    data.Deleted,
		"`updated_by`": data.UpdatedBy,
		"`deleted_by`": data.DeletedBy,
		"`deleted_at`": data.DeletedAt,
	}
	return squirrel.Update(m.table).SetMap(eq).Where("`id` = ?", data.Id)
}

func (m *customPerfPlanReferenceRelationshipModel) SelectBuilder() squirrel.SelectBuilder {
	return squirrel.Select(perfPlanReferenceRelationshipFieldNames...).Where(
		"`deleted` = ?", constants.NotDeleted,
	).From(m.table)
}

func (m *customPerfPlanReferenceRelationshipModel) SelectCountBuilder() squirrel.SelectBuilder {
	return squirrel.Select("count(*)").Where("`deleted` = ?", constants.NotDeleted).From(m.table)
}

func (m *customPerfPlanReferenceRelationshipModel) FindCount(
	ctx context.Context, countBuilder squirrel.SelectBuilder,
) (int64, error) {
	query, values, err := countBuilder.ToSql()
	if err != nil {
		return 0, err
	}

	var resp int64
	err = m.QueryRowNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return 0, err
	}
}

func (m *customPerfPlanReferenceRelationshipModel) FindNoCacheByQuery(
	ctx context.Context, selectBuilder squirrel.SelectBuilder,
) ([]*PerfPlanReferenceRelationship, error) {
	query, values, err := selectBuilder.ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*PerfPlanReferenceRelationship
	err = m.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

func (m *customPerfPlanReferenceRelationshipModel) BatchInsert(
	ctx context.Context, session sqlx.Session, items []*PerfPlanReferenceRelationship,
) (
	sql.Result, error,
) {
	count := len(items)
	if count == 0 {
		return nil, nil
	} else if count == 1 {
		return m.Insert(ctx, session, items[0])
	}

	builder := squirrel.Insert(m.table).Columns(perfPlanReferenceRelationshipInsertFields...)
	for _, item := range items {
		builder = builder.Values(
			item.ProjectId, item.ReferenceType, item.ReferenceId, item.PlanId, item.State, item.Deleted, item.CreatedBy,
			item.UpdatedBy,
		)
	}

	return m.ExecCtx(
		ctx, func(ctx context.Context, conn sqlx.SqlConn) (sql.Result, error) {
			query, values, err := builder.ToSql()
			if err != nil {
				return nil, err
			}
			if session != nil {
				return session.ExecCtx(ctx, query, values...)
			}
			return conn.ExecCtx(ctx, query, values...)
		},
	)
}

func (m *customPerfPlanReferenceRelationshipModel) FindReferenceByReference(
	ctx context.Context, projectID, referenceType, referenceID string,
) ([]*PerfPlanReferenceRelationship, error) {
	/*
		SQL:
		SELECT *
		FROM `perf_plan_reference_relationship`
		WHERE `project_id` = ?
		  AND `reference_type` = ?
		  AND `reference_id` = ?
		  AND `deleted` = ?
	*/

	return m.FindNoCacheByQuery(
		ctx, m.SelectBuilder().Where(
			"`project_id` = ? AND `reference_type` = ? AND `reference_id` = ?", projectID, referenceType, referenceID,
		),
	)
}

// FindReferenceByPlanID 根据压测计划ID查询引用关系（返回指定计划下的全部引用关系）
func (m *customPerfPlanReferenceRelationshipModel) FindReferenceByPlanID(
	ctx context.Context, projectID, planID string,
) ([]*PerfPlanReferenceRelationship, error) {
	/*
		SQL:
		SELECT *
		FROM `perf_plan_reference_relationship`
		WHERE `project_id` = ?
		  AND `plan_id` = ?
		  AND `deleted` = ?
	*/

	return m.FindNoCacheByQuery(ctx, m.SelectBuilder().Where("`project_id` = ? AND `plan_id` = ?", projectID, planID))
}

// RemoveByPlanID 根据压测计划ID删除引用关系（指定压测计划下的全部引用关系）
func (m *customPerfPlanReferenceRelationshipModel) RemoveByPlanID(
	ctx context.Context, session sqlx.Session, projectID, planID string,
) (sql.Result, error) {
	keys := m.getKeysByPlanID(ctx, projectID, planID)

	return m.ExecCtx(
		ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
			/*
				SQL:
				UPDATE `perf_plan_reference_relationship`
				SET `deleted` = ?, `deleted_by` = ?, `deleted_at` = ?
				WHERE `project_id` = ?
				  AND `plan_id` = ?
			*/

			stmt, values, err := squirrel.Update(m.table).
				SetMap(
					squirrel.Eq{
						"`deleted`":    constants.HasDeleted,
						"`deleted_by`": userinfo.FromContext(ctx).Account,
						"`deleted_at`": time.Now(),
					},
				).
				Where("`project_id` = ? AND `plan_id` = ?", projectID, planID).
				ToSql()
			if err != nil {
				return nil, err
			}

			if session != nil {
				return session.ExecCtx(ctx, stmt, values...)
			}
			return conn.ExecCtx(ctx, stmt, values...)
		}, keys...,
	)
}

func (m *customPerfPlanReferenceRelationshipModel) getKeysByPlanID(
	ctx context.Context, projectID, planID string,
) []string {
	cs, err := m.FindReferenceByPlanID(ctx, projectID, planID)
	if err != nil {
		return make([]string, 0)
	}

	keys := make([]string, 0, len(cs))
	for _, c := range cs {
		keys = append(keys, fmt.Sprintf("%s%v", cacheManagerPerfPlanReferenceRelationshipIdPrefix, c.Id))
	}

	return keys
}
