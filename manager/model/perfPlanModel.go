package model

import (
	"context"
	"database/sql"
	"fmt"
	"time"

	"github.com/Masterminds/squirrel"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sqlbuilder"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/types"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/userinfo"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/utils"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
)

var (
	_ PerfPlanModel = (*customPerfPlanModel)(nil)

	perfPlanInsertFields = stringx.Remove(
		perfPlanFieldNames, "`id`", "`create_time`", "`update_time`", "`deleted_by`", "`created_at`", "`updated_at`",
		"`deleted_at`",
	)
)

type (
	// PerfPlanModel is an interface to be customized, add more methods here,
	// and implement the added methods in customPerfPlanModel.
	PerfPlanModel interface {
		perfPlanModel
		types.DBModel

		Trans(ctx context.Context, fn func(context context.Context, session sqlx.Session) error) error
		InsertBuilder(data *PerfPlan) squirrel.InsertBuilder
		UpdateBuilder(data *PerfPlan) squirrel.UpdateBuilder
		SelectBuilder() squirrel.SelectBuilder
		SelectCountBuilder() squirrel.SelectBuilder
		FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error)
		FindNoCacheByQuery(ctx context.Context, selectBuilder squirrel.SelectBuilder) ([]*PerfPlan, error)

		FindCountByReq(ctx context.Context, req SearchPerfPlanReq) (int64, error)
		FindAllByReq(ctx context.Context, req SearchPerfPlanReq) ([]*SearchPerfPlanItem, error)

		FindCountCasesInPerfPlan(ctx context.Context, req SearchCaseInPerfPlanReq) (int64, error)
		FindCasesInPerfPlan(ctx context.Context, req SearchCaseInPerfPlanReq) ([]*SearchCaseInPerfPlanItem, error)

		FindByProtobufConfigID(ctx context.Context, projectID, configID string) ([]*PerfPlan, error)
		FindByGeneralConfigID(ctx context.Context, projectID, configID string) ([]*PerfPlan, error)
		RemoveByPlanID(ctx context.Context, session sqlx.Session, projectID, planID string) (sql.Result, error)
	}

	customPerfPlanModel struct {
		*defaultPerfPlanModel

		conn sqlx.SqlConn
	}
)

// NewPerfPlanModel returns a model for the database table.
func NewPerfPlanModel(conn sqlx.SqlConn, c cache.CacheConf, opts ...cache.Option) PerfPlanModel {
	return &customPerfPlanModel{
		defaultPerfPlanModel: newPerfPlanModel(conn, c, opts...),
		conn:                 conn,
	}
}

func (m *customPerfPlanModel) Table() string {
	return m.table
}

func (m *customPerfPlanModel) Fields() []string {
	return perfPlanFieldNames
}

func (m *customPerfPlanModel) Trans(
	ctx context.Context, fn func(context context.Context, session sqlx.Session) error,
) error {
	return m.TransactCtx(ctx, fn)
}

func (m *customPerfPlanModel) InsertBuilder(data *PerfPlan) squirrel.InsertBuilder {
	return squirrel.Insert(m.table).Columns(perfPlanInsertFields...).Values(
		data.ProjectId, data.PlanId, data.Name, data.Description, data.Type, data.Tags, data.Protocol,
		data.ProtobufConfigId, data.GeneralConfigId, data.AccountConfigId, data.Duration, data.TargetEnv,
		data.Keepalive, data.Delay, data.State, data.Deleted, data.MaintainedBy, data.CreatedBy, data.UpdatedBy,
	)
}

func (m *customPerfPlanModel) UpdateBuilder(data *PerfPlan) squirrel.UpdateBuilder {
	eq := squirrel.Eq{
		"`name`":               data.Name,
		"`description`":        data.Description,
		"`type`":               data.Type,
		"`tags`":               data.Tags,
		"`protocol`":           data.Protocol,
		"`protobuf_config_id`": data.ProtobufConfigId,
		"`general_config_id`":  data.GeneralConfigId,
		"`account_config_id`":  data.AccountConfigId,
		"`duration`":           data.Duration,
		"`target_env`":         data.TargetEnv,
		"`keepalive`":          data.Keepalive,
		"`delay`":              data.Delay,
		"`state`":              data.State,
		"`deleted`":            data.Deleted,
		"`maintained_by`":      data.MaintainedBy,
		"`updated_by`":         data.UpdatedBy,
		"`deleted_by`":         data.DeletedBy,
		"`deleted_at`":         data.DeletedAt,
	}
	return squirrel.Update(m.table).SetMap(eq).Where("`id` = ?", data.Id)
}

func (m *customPerfPlanModel) SelectBuilder() squirrel.SelectBuilder {
	return squirrel.Select(perfPlanFieldNames...).Where("`deleted` = ?", constants.NotDeleted).From(m.table)
}

func (m *customPerfPlanModel) SelectCountBuilder() squirrel.SelectBuilder {
	return squirrel.Select("count(*)").Where("`deleted` = ?", constants.NotDeleted).From(m.table)
}

func (m *customPerfPlanModel) FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error) {
	query, values, err := countBuilder.ToSql()
	if err != nil {
		return 0, err
	}

	var resp int64
	err = m.QueryRowNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return 0, err
	}
}

func (m *customPerfPlanModel) FindNoCacheByQuery(
	ctx context.Context, selectBuilder squirrel.SelectBuilder,
) ([]*PerfPlan, error) {
	query, values, err := selectBuilder.ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*PerfPlan
	err = m.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

type SearchPerfPlanItem struct {
	ProjectId    string         `db:"project_id"`     // 项目ID
	PlanId       string         `db:"plan_id"`        // 计划ID
	Name         string         `db:"name"`           // 计划名称
	Description  sql.NullString `db:"description"`    // 计划描述
	Type         string         `db:"type"`           // 计划类型（手动、定时、接口）
	Tags         sql.NullString `db:"tags"`           // 标签
	Protocol     string         `db:"protocol"`       // 协议（TT私有协议、通用gRPC协议、通用HTTP协议）
	Duration     int64          `db:"duration"`       // 压测持续时长，单位为秒
	TargetEnv    string         `db:"target_env"`     // 目标环境（生产环境、测试环境）
	NumberOfCase uint32         `db:"number_of_case"` // 压测用例数量
	NumberOfApi  uint32         `db:"number_of_api"`  // 压测接口数量
	StatsOfApi   string         `db:"stats_of_api"`   // 压测接口统计信息
	State        int64          `db:"state"`          // 计划状态（生效、失效）
	MaintainedBy sql.NullString `db:"maintained_by"`  // 维护者的用户ID
	CreatedBy    string         `db:"created_by"`     // 创建者的用户ID
	UpdatedBy    string         `db:"updated_by"`     // 最近一次更新者的用户ID
	CreatedAt    time.Time      `db:"created_at"`     // 创建时间
	UpdatedAt    time.Time      `db:"updated_at"`     // 更新时间
}

func (m *customPerfPlanModel) FindCountByReq(ctx context.Context, req SearchPerfPlanReq) (int64, error) {
	return m.FindCount(
		ctx, sqlbuilder.SearchOptions(
			m.SelectCountBuilder().Where("`project_id` = ?", req.ProjectID),
			sqlbuilder.WithCondition(m, req.Condition),
		),
	)
}

func (m *customPerfPlanModel) FindAllByReq(ctx context.Context, req SearchPerfPlanReq) ([]*SearchPerfPlanItem, error) {
	/*
		SQL:
		SELECT t1.`project_id`,
		       t1.`plan_id`,
		       t1.`name`,
		       t1.`description`,
		       t1.`type`,
		       t1.`tags`,
		       t1.`protocol`,
		       t1.`duration`,
		       t1.`target_env`,
		       t1.`state`,
		       t1.`maintained_by`,
		       t1.`created_by`,
		       t1.`updated_by`,
		       t1.`created_at`,
		       t1.`updated_at`,
		       t1.`number_of_case`,
		       t1.`number_of_api`,
		       JSON_ARRAYAGG(
		               JSON_OBJECT(
		                       'target_rps', t2.`target_rps`,
		                       'number_of_api', t2.`rps_count`
		               )
		       ) AS `stats_of_api`
		FROM (SELECT t1.`project_id`,
		             t1.`plan_id`,
		             t1.`name`,
		             t1.`description`,
		             t1.`type`,
		             t1.`tags`,
		             t1.`protocol`,
		             t1.`duration`,
		             t1.`target_env`,
		             t1.`state`,
		             t1.`maintained_by`,
		             t1.`created_by`,
		             t1.`updated_by`,
		             t1.`created_at`,
		             t1.`updated_at`,
		             COUNT(DISTINCT t3.`case_id`) AS `number_of_case`,
		             COUNT(DISTINCT t4.`step_id`) AS `number_of_api`
		      FROM `perf_plan` AS t1
		               INNER JOIN `perf_plan_reference_relationship` AS t2
		                          ON t1.`project_id` = t2.`project_id` AND
		                             t1.`plan_id` = t2.`plan_id` AND
		                             t1.`deleted` = t2.`deleted` AND
		                             t2.`reference_type` = 'PERF_CASE'
		               INNER JOIN `perf_case` AS t3
		                          ON t1.`project_id` = t3.`project_id` AND
		                             t2.`reference_id` = t3.`case_id` AND
		                             t1.`deleted` = t3.`deleted`
		               INNER JOIN `perf_case_step` AS t4
		                          ON t1.`project_id` = t4.`project_id` AND
		                             t3.`case_id` = t4.`case_id` AND
		                             t1.`deleted` = t4.`deleted` AND
		                             t4.`type` IN (?, ?)
		      WHERE t1.`project_id` = ?
		        AND t1.`deleted` = ?
		      GROUP BY t1.`project_id`, t1.`plan_id`) AS t1
		         INNER JOIN
		     (SELECT t1.`project_id`,
		             t1.`plan_id`,
		             t4.`target_rps`,
		             COUNT(t4.`target_rps`) AS `rps_count`
		      FROM `perf_plan` AS t1
		               INNER JOIN `perf_plan_reference_relationship` AS t2
		                          ON t1.`project_id` = t2.`project_id` AND
		                             t1.`plan_id` = t2.`plan_id` AND
		                             t1.`deleted` = t2.`deleted` AND
		                             t2.`reference_type` = 'PERF_CASE'
		               INNER JOIN `perf_case` AS t3
		                          ON t1.`project_id` = t3.`project_id` AND
		                             t2.`reference_id` = t3.`case_id` AND
		                             t1.`deleted` = t3.`deleted`
		               INNER JOIN `perf_case_step` AS t4
		                          ON t1.`project_id` = t4.`project_id` AND
		                             t3.`case_id` = t4.`case_id` AND
		                             t1.`deleted` = t4.`deleted` AND
		                             t4.`type` IN (?, ?)
		      WHERE t1.`project_id` = ?
		        AND t1.`deleted` = ?
		      GROUP BY t1.`project_id`, t1.`plan_id`, t4.`target_rps`) AS t2
		     ON t1.`project_id` = t2.`project_id` AND t1.`plan_id` = t2.`plan_id`
		GROUP BY t1.`project_id`, t1.`plan_id`
	*/

	var (
		fields = []string{
			"t1.`project_id`",
			"t1.`plan_id`",
			"t1.`name`",
			"t1.`description`",
			"t1.`type`",
			"t1.`tags`",
			"t1.`protocol`",
			"t1.`duration`",
			"t1.`target_env`",
			"t1.`state`",
			"t1.`maintained_by`",
			"t1.`created_by`",
			"t1.`updated_by`",
			"t1.`created_at`",
			"t1.`updated_at`",
			"t1.`number_of_case`",
			"t1.`number_of_api`",
			"JSON_ARRAYAGG(JSON_OBJECT('target_rps', t2.`target_rps`, 'number_of_api', t2.`rps_count`)) AS `stats_of_api`",
		}

		subFields1 = []string{
			"t1.`project_id`",
			"t1.`plan_id`",
			"t1.`name`",
			"t1.`description`",
			"t1.`type`",
			"t1.`tags`",
			"t1.`protocol`",
			"t1.`duration`",
			"t1.`target_env`",
			"t1.`state`",
			"t1.`maintained_by`",
			"t1.`created_by`",
			"t1.`updated_by`",
			"t1.`created_at`",
			"t1.`updated_at`",
			"COUNT(DISTINCT t3.`case_id`) AS `number_of_case`",
			"COUNT(DISTINCT t4.`step_id`) AS `number_of_api`",
		}

		subFields2 = []string{
			"t1.`project_id`",
			"t1.`plan_id`",
			"t4.`target_rps`",
			"COUNT(t4.`target_rps`) AS `rps_count`",
		}
	)

	sub1 := squirrel.Select(subFields1...).
		From(m.table+" AS t1").
		InnerJoin(
			perfPlanReferenceRelationshipTableName+" AS t2 ON t1.`project_id` = t2.`project_id` AND t1.`plan_id` = t2.`plan_id` AND t1.`deleted` = t2.`deleted` AND t2.`reference_type` = ?",
			common.ConstReferenceTypePerfCase,
		).
		InnerJoin(perfCaseTableName+" AS t3 ON t1.`project_id` = t3.`project_id` AND t2.`reference_id` = t3.`case_id` AND t1.`deleted` = t3.`deleted`").
		InnerJoin(
			perfCaseStepTableName+" AS t4 ON t1.`project_id` = t4.`project_id` AND t3.`case_id` = t4.`case_id` AND t1.`deleted` = t4.`deleted` AND t4.`type` IN (?, ?)",
			common.ConstPerfCaseStepTypeSerial, common.ConstPerfCaseStepTypeParallel,
		).
		Where("t1.`project_id` = ? AND t1.`deleted` = ?", req.ProjectID, constants.NotDeleted).
		GroupBy("t1.`project_id`", "t1.`plan_id`")
	sub1 = sqlbuilder.SearchOptionsWithAlias(sub1, "t1", sqlbuilder.WithCondition(m, req.Condition))

	sub2 := squirrel.Select(subFields2...).
		From(m.table+" AS t1").
		InnerJoin(
			perfPlanReferenceRelationshipTableName+" AS t2 ON t1.`project_id` = t2.`project_id` AND t1.`plan_id` = t2.`plan_id` AND t1.`deleted` = t2.`deleted` AND t2.`reference_type` = ?",
			common.ConstReferenceTypePerfCase,
		).
		InnerJoin(perfCaseTableName+" AS t3 ON t1.`project_id` = t3.`project_id` AND t2.`reference_id` = t3.`case_id` AND t1.`deleted` = t3.`deleted`").
		InnerJoin(
			perfCaseStepTableName+" AS t4 ON t1.`project_id` = t4.`project_id` AND t3.`case_id` = t4.`case_id` AND t1.`deleted` = t4.`deleted` AND t4.`type` IN (?, ?)",
			common.ConstPerfCaseStepTypeSerial, common.ConstPerfCaseStepTypeParallel,
		).
		Where("t1.`project_id` = ? AND t1.`deleted` = ?", req.ProjectID, constants.NotDeleted).
		GroupBy("t1.`project_id`", "t1.`plan_id`", "t4.`target_rps`")
	sub2 = sqlbuilder.SearchOptionsWithAlias(sub2, "t1", sqlbuilder.WithCondition(m, req.Condition))

	sb := squirrel.Select(fields...).
		FromSelect(sub1, "t1").
		JoinClause(sub2.Prefix("INNER JOIN (").Suffix(") AS t2 ON t1.`project_id` = t2.`project_id` AND t1.`plan_id` = t2.`plan_id`")).
		GroupBy("t1.`project_id`", "t1.`plan_id`")
	sb = sqlbuilder.SearchOptionsWithAlias(
		sb, "t1", sqlbuilder.WithPagination(m, req.Pagination), sqlbuilder.WithSort(m, req.Sort),
	)

	var resp []*SearchPerfPlanItem
	err := utils.FindRows(
		ctx, m.conn, sb, func() any {
			return &resp
		},
	)
	return resp, err
}

type SearchCaseInPerfPlanItem struct {
	ProjectId        string         `db:"project_id"`         // 项目ID
	CaseId           string         `db:"case_id"`            // 压测用例ID
	Name             string         `db:"name"`               // 压测用例名称
	Description      sql.NullString `db:"description"`        // 压测用例描述
	Extension        string         `db:"extension"`          // 文件扩展名
	Hash             string         `db:"hash"`               // 压测用例文件的一致性哈希值（MD5）
	Size             int64          `db:"size"`               // 压测用例文件的大小
	Path             string         `db:"path"`               // 压测用例文件的路径
	TargetRps        int64          `db:"target_rps"`         // 目标的RPS
	InitialRps       int64          `db:"initial_rps"`        // 初始的RPS
	StepHeight       int64          `db:"step_height"`        // 每次改变RPS的量
	StepDuration     string         `db:"step_duration"`      // 改变后的RPS的持续时间
	PerfDataId       string         `db:"perf_data_id"`       // 压测数据ID
	NumberOfVu       int64          `db:"number_of_vu"`       // 虚拟用户数
	NumberOfLg       int64          `db:"number_of_lg"`       // 施压机数
	RequestsOfCpu    string         `db:"requests_of_cpu"`    // 最小分配的CPU资源
	RequestsOfMemory string         `db:"requests_of_memory"` // 最小分配的内存资源
	LimitsOfCpu      string         `db:"limits_of_cpu"`      // 最大分配的CPU资源
	LimitsOfMemory   string         `db:"limits_of_memory"`   // 最大分配的内存资源
	State            int64          `db:"state"`              // 计划状态（生效、失效）
	MaintainedBy     sql.NullString `db:"maintained_by"`      // 维护者的用户ID
	CreatedBy        string         `db:"created_by"`         // 创建者的用户ID
	UpdatedBy        string         `db:"updated_by"`         // 最近一次更新者的用户ID
	CreatedAt        time.Time      `db:"created_at"`         // 创建时间
	UpdatedAt        time.Time      `db:"updated_at"`         // 更新时间
}

func (m *customPerfPlanModel) FindCountCasesInPerfPlan(ctx context.Context, req SearchCaseInPerfPlanReq) (
	int64, error,
) {
	sb := squirrel.Select("COUNT(*)").
		From(m.table+" AS t1").
		InnerJoin(perfPlanReferenceRelationshipTableName+" AS t2 ON t1.`project_id` = t2.`project_id` AND t1.`plan_id` = t2.`plan_id` AND t1.`deleted` = t2.`deleted`").
		InnerJoin(
			perfCaseTableName+"AS t3 ON t1.`project_id` = t3.`project_id` AND t2.`reference_type` = ? AND t2.`reference_id` = t3.`case_id` AND t1.`deleted` = t3.`deleted`",
			common.ConstReferenceTypePerfCase,
		).Where(
		"t1.`project_id` = ? AND t1.`plan_id` = ? AND t1.`deleted` = ?",
		req.ProjectID, req.PlanID, constants.NotDeleted,
	)

	pcm := req.PerfCaseModel
	sb = sqlbuilder.SearchOptionsWithAlias(sb, "t3", sqlbuilder.WithCondition(pcm, req.Condition))

	return m.FindCount(ctx, sb)
}

func (m *customPerfPlanModel) FindCasesInPerfPlan(
	ctx context.Context, req SearchCaseInPerfPlanReq,
) ([]*SearchCaseInPerfPlanItem, error) {
	/*
		SQL:
		SELECT t3.`project_id`,
		       t3.`case_id`,
		       t3.`name`,
		       t3.`description`,
		       t3.`extension`,
		       t3.`hash`,
		       t3.`size`,
		       t3.`path`,
		       t3.`target_rps`,
		       t3.`initial_rps`,
		       t3.`step_height`,
		       t3.`step_duration`,
		       t3.`perf_data_id`,
		       t3.`number_of_vu`,
		       t3.`number_of_lg`,
		       t3.`requests_of_cpu`,
		       t3.`requests_of_memory`,
		       t3.`limits_of_cpu`,
		       t3.`limits_of_memory`,
		       t3.`state`,
		       t3.`maintained_by`,
		       t3.`created_by`,
		       t3.`updated_by`,
		       t3.`created_at`,
		       t3.`updated_at`
		FROM `perf_plan` AS t1
		INNER JOIN `perf_plan_reference_relationship` AS t2 ON
		    t1.`project_id` = t2.`project_id` AND
		    t1.`plan_id` = t2.`plan_id` AND
		    t1.`deleted` = t2.`deleted`
		INNER JOIN `perf_case` AS t3 ON
		    t1.`project_id` = t3.`project_id` AND
		    t2.`reference_type` = 'PERF_CASE' AND
		    t2.`reference_id` = t3.`case_id` AND
		    t1.`deleted` = t3.`deleted`
		WHERE t1.`project_id` = ?
		  AND t1.`plan_id` = ?
		  AND t1.`deleted` = ?
	*/

	fields := []string{
		"t3.`project_id`",
		"t3.`case_id`",
		"t3.`name`",
		"t3.`description`",
		"t3.`extension`",
		"t3.`hash`",
		"t3.`size`",
		"t3.`path`",
		"t3.`target_rps`",
		"t3.`initial_rps`",
		"t3.`step_height`",
		"t3.`step_duration`",
		"t3.`perf_data_id`",
		"t3.`number_of_vu`",
		"t3.`number_of_lg`",
		"t3.`requests_of_cpu`",
		"t3.`requests_of_memory`",
		"t3.`limits_of_cpu`",
		"t3.`limits_of_memory`",
		"t3.`state`",
		"t3.`maintained_by`",
		"t3.`created_by`",
		"t3.`updated_by`",
		"t3.`created_at`",
		"t3.`updated_at`",
	}
	sb := squirrel.Select(fields...).
		From(m.table+" AS t1").
		InnerJoin(perfPlanReferenceRelationshipTableName+" AS t2 ON t1.`project_id` = t2.`project_id` AND t1.`plan_id` = t2.`plan_id` AND t1.`deleted` = t2.`deleted`").
		InnerJoin(
			perfCaseTableName+"AS t3 ON t1.`project_id` = t3.`project_id` AND t2.`reference_type` = ? AND t2.`reference_id` = t3.`case_id` AND t1.`deleted` = t3.`deleted`",
			common.ConstReferenceTypePerfCase,
		).Where(
		"t1.`project_id` = ? AND t1.`plan_id` = ? AND t1.`deleted` = ?",
		req.ProjectID, req.PlanID, constants.NotDeleted,
	)

	pcm := req.PerfCaseModel
	sb = sqlbuilder.SearchOptionsWithAlias(
		sb, "t3",
		sqlbuilder.WithCondition(pcm, req.Condition),
		sqlbuilder.WithPagination(pcm, req.Pagination),
		sqlbuilder.WithSort(pcm, req.Sort),
	)

	var resp []*SearchCaseInPerfPlanItem
	err := utils.FindRows(
		ctx, m.conn, sb, func() any {
			return &resp
		},
	)
	return resp, err
}

func (m *customPerfPlanModel) FindByProtobufConfigID(ctx context.Context, projectID, configID string) (
	[]*PerfPlan, error,
) {
	sb := squirrel.Select(utils.AddTableNameToFields("t1", perfPlanFieldNames)...).
		From(m.table+" AS t1").
		InnerJoin(protobufConfigurationTableName+" AS t2 ON t1.`project_id` = t2.`project_id` AND t1.`protobuf_config_id` = t2.`config_id` AND t1.`deleted` = t2.`deleted`").
		Where(
			"t1.`project_id` = ? AND t1.`protobuf_config_id` = ? AND t1.`deleted` = ?",
			projectID, configID, constants.NotDeleted,
		)

	return m.FindNoCacheByQuery(ctx, sb)
}

func (m *customPerfPlanModel) FindByGeneralConfigID(ctx context.Context, projectID, configID string) (
	[]*PerfPlan, error,
) {
	sb := squirrel.Select(utils.AddTableNameToFields("t1", perfPlanFieldNames)...).
		From(m.table+" AS t1").
		InnerJoin(generalConfigurationTableName+" AS t2 ON t1.`project_id` = t2.`project_id` AND t1.`general_config_id` = t2.`config_id` AND t1.`deleted` = t2.`deleted`").
		Where(
			"t1.`project_id` = ? AND t1.`general_config_id` = ? AND t1.`deleted` = ?",
			projectID, configID, constants.NotDeleted,
		)

	return m.FindNoCacheByQuery(ctx, sb)
}

func (m *customPerfPlanModel) RemoveByPlanID(
	ctx context.Context, session sqlx.Session, projectID, planID string,
) (sql.Result, error) {
	keys := m.getKeysByPlanID(ctx, projectID, planID)

	return m.ExecCtx(
		ctx, func(ctx context.Context, conn sqlx.SqlConn) (sql.Result, error) {
			/*
				SQL:
				UPDATE `perf_plan`
				SET `deleted` = ?, `deleted_by` = ?, `deleted_at` = ?
				WHERE `project_id` = ?
				  AND `plan_id` = ?
			*/
			stmt, values, err := squirrel.Update(m.table).
				SetMap(
					squirrel.Eq{
						"`deleted`":    constants.HasDeleted,
						"`deleted_by`": userinfo.FromContext(ctx).Account,
						"`deleted_at`": time.Now(),
					},
				).
				Where("`project_id` = ? AND `plan_id` = ?", projectID, planID).
				ToSql()
			if err != nil {
				return nil, err
			}

			if session != nil {
				return session.ExecCtx(ctx, stmt, values...)
			}
			return conn.ExecCtx(ctx, stmt, values...)
		}, keys...,
	)
}

func (m *customPerfPlanModel) getKeysByPlanID(ctx context.Context, projectID, planID string) []string {
	perfPlan, err := m.FindOneByProjectIdPlanId(ctx, projectID, planID)
	if err != nil {
		return make([]string, 0)
	}

	return []string{
		fmt.Sprintf("%s%v", cacheManagerPerfPlanIdPrefix, perfPlan.Id),
		fmt.Sprintf("%s%v:%v", cacheManagerPerfPlanProjectIdPlanIdPrefix, perfPlan.ProjectId, perfPlan.PlanId),
	}
}
