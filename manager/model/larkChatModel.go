package model

import (
	"context"
	"database/sql"
	"fmt"

	"github.com/Masterminds/squirrel"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sqlbuilder"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/types"
)

var (
	_ LarkChatModel = (*customLarkChatModel)(nil)

	larkChatInsertFields = stringx.Remove(larkChatFieldNames, "`id`", "`create_time`", "`update_time`", "`deleted_by`", "`created_at`", "`updated_at`", "`deleted_at`")
)

type (
	// LarkChatModel is an interface to be customized, add more methods here,
	// and implement the added methods in customLarkChatModel.
	LarkChatModel interface {
		larkChatModel
		types.DBModel

		Trans(ctx context.Context, fn func(context context.Context, session sqlx.Session) error) error
		InsertBuilder(data *LarkChat) squirrel.InsertBuilder
		UpdateBuilder(data *LarkChat) squirrel.UpdateBuilder
		SelectBuilder() squirrel.SelectBuilder
		SelectCountBuilder() squirrel.SelectBuilder
		FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error)
		FindNoCacheByQuery(ctx context.Context, selectBuilder squirrel.SelectBuilder) ([]*LarkChat, error)

		FindAll(ctx context.Context, projectID, _type string) ([]*LarkChat, error)

		FindAllBySearchReq(ctx context.Context, req SearchLarkChatReq) ([]*LarkChat, error)
		FindCountBySearchReq(ctx context.Context, req SearchLarkChatReq) (int64, error)

		UpdateByChatID(ctx context.Context, session sqlx.Session, req UpdateLarkChatReq) (sql.Result, error)
		DeleteByChatID(ctx context.Context, session sqlx.Session, chatID string) (sql.Result, error)
	}

	customLarkChatModel struct {
		*defaultLarkChatModel

		conn sqlx.SqlConn
	}
)

// NewLarkChatModel returns a model for the database table.
func NewLarkChatModel(conn sqlx.SqlConn, c cache.CacheConf, opts ...cache.Option) LarkChatModel {
	return &customLarkChatModel{
		defaultLarkChatModel: newLarkChatModel(conn, c, opts...),
		conn:                 conn,
	}
}

func (m *customLarkChatModel) Table() string {
	return m.table
}

func (m *customLarkChatModel) Fields() []string {
	return larkChatFieldNames
}

func (m *customLarkChatModel) Trans(ctx context.Context, fn func(context context.Context, session sqlx.Session) error) error {
	return m.TransactCtx(ctx, fn)
}

func (m *customLarkChatModel) InsertBuilder(data *LarkChat) squirrel.InsertBuilder {
	// TODO: fill the insert values
	return squirrel.Insert(m.table).Columns(larkChatInsertFields...).Values()
}

func (m *customLarkChatModel) UpdateBuilder(data *LarkChat) squirrel.UpdateBuilder {
	eq := squirrel.Eq{
		// TODO: fill the update values
	}
	return squirrel.Update(m.table).SetMap(eq).Where("`id` = ?", data.Id)
}

func (m *customLarkChatModel) SelectBuilder() squirrel.SelectBuilder {
	return squirrel.Select(larkChatFieldNames...).Where("`deleted` = ?", constants.NotDeleted).From(m.table)
}

func (m *customLarkChatModel) SelectCountBuilder() squirrel.SelectBuilder {
	return squirrel.Select("count(*)").Where("`deleted` = ?", constants.NotDeleted).From(m.table)
}

func (m *customLarkChatModel) FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error) {
	query, values, err := countBuilder.ToSql()
	if err != nil {
		return 0, err
	}

	var resp int64
	err = m.QueryRowNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return 0, err
	}
}

func (m *customLarkChatModel) FindNoCacheByQuery(ctx context.Context, selectBuilder squirrel.SelectBuilder) ([]*LarkChat, error) {
	query, values, err := selectBuilder.ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*LarkChat
	err = m.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

func (m *customLarkChatModel) FindAll(ctx context.Context, projectID, _type string) ([]*LarkChat, error) {
	query := m.SelectBuilder().Where("`project_id` = ? AND `type` = ?", projectID, _type)
	return m.FindNoCacheByQuery(ctx, query)
}

func (m *customLarkChatModel) FindAllBySearchReq(ctx context.Context, req SearchLarkChatReq) (
	[]*LarkChat, error,
) {
	return m.FindNoCacheByQuery(
		ctx, sqlbuilder.SearchOptions(
			m.SelectBuilder().Where("`project_id` = ? AND `type` = ?", req.ProjectID, req.Type),
			sqlbuilder.WithCondition(m, req.Condition),
			sqlbuilder.WithPagination(m, req.Pagination),
			sqlbuilder.WithSort(m, req.Sort),
		),
	)
}

func (m *customLarkChatModel) FindCountBySearchReq(ctx context.Context, req SearchLarkChatReq) (int64, error) {
	return m.FindCount(
		ctx, sqlbuilder.SearchOptions(
			m.SelectCountBuilder().Where("`project_id` = ? AND `type` = ?", req.ProjectID, req.Type),
			sqlbuilder.WithCondition(m, req.Condition),
		),
	)
}

func (m *customLarkChatModel) DeleteByChatID(ctx context.Context, session sqlx.Session, chatID string) (
	sql.Result, error,
) {
	keys := m.getKeysByChatID(ctx, chatID)

	return m.ExecCtx(
		ctx, func(ctx context.Context, conn sqlx.SqlConn) (sql.Result, error) {
			/*
				SQL:
				DELETE FROM `perf_lark_chat` WHERE `chat_id` = ?
			*/

			stmt, values, err := squirrel.Delete(m.table).
				Where("`chat_id` = ?", chatID).
				ToSql()
			if err != nil {
				return nil, err
			}

			if session != nil {
				return session.ExecCtx(ctx, stmt, values...)
			}
			return conn.ExecCtx(ctx, stmt, values...)
		}, keys...,
	)
}

func (m *customLarkChatModel) getKeysByChatID(ctx context.Context, chatID string) []string {
	chats, err := m.FindAllByChatID(ctx, chatID)
	if err != nil {
		return make([]string, 0)
	}

	keys := make([]string, 0, len(chats))
	for _, chat := range chats {
		keys = append(keys, fmt.Sprintf("%s%v", cacheManagerLarkChatIdPrefix, chat.Id))
	}

	return keys
}

func (m *customLarkChatModel) FindAllByChatID(ctx context.Context, chatID string) ([]*LarkChat, error) {
	return m.FindNoCacheByQuery(ctx, m.SelectBuilder().Where("`chat_id` = ?", chatID))
}

func (m *customLarkChatModel) UpdateByChatID(
	ctx context.Context, session sqlx.Session, req UpdateLarkChatReq,
) (sql.Result, error) {
	keys := m.getKeysByChatID(ctx, req.ChatID)

	return m.ExecCtx(
		ctx, func(ctx context.Context, conn sqlx.SqlConn) (sql.Result, error) {
			/*
				SQL:
				UPDATE `perf_lark_chat`
				SET `name` = ?, `avatar` = ?, `description` = ?, `external` = ?
				WHERE `chat_id` = ?
			*/
			stmt, values, err := squirrel.Update(m.table).
				SetMap(
					squirrel.Eq{
						"`name`":        req.Name,
						"`avatar`":      req.Avatar,
						"`description`": req.Description,
						"`external`":    req.External,
					},
				).
				Where("`chat_id` = ?", req.ChatID).
				ToSql()
			if err != nil {
				return nil, err
			}

			if session != nil {
				return session.ExecCtx(ctx, stmt, values...)
			}
			return conn.ExecCtx(ctx, stmt, values...)
		}, keys...,
	)
}
