package model

import (
	"context"
	"database/sql"
	"fmt"
	"time"

	"github.com/Masterminds/squirrel"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/types"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/userinfo"
)

var (
	_ ProtobufDependenceModel = (*customProtobufDependenceModel)(nil)

	protobufDependenceInsertFields = stringx.Remove(
		protobufDependenceFieldNames, "`id`", "`create_time`", "`update_time`", "`deleted_by`", "`created_at`",
		"`updated_at`", "`deleted_at`",
	)
)

type (
	// ProtobufDependenceModel is an interface to be customized, add more methods here,
	// and implement the added methods in customProtobufDependenceModel.
	ProtobufDependenceModel interface {
		protobufDependenceModel
		types.DBModel

		Trans(ctx context.Context, fn func(context context.Context, session sqlx.Session) error) error
		InsertBuilder(data *ProtobufDependence) squirrel.InsertBuilder
		UpdateBuilder(data *ProtobufDependence) squirrel.UpdateBuilder
		SelectBuilder() squirrel.SelectBuilder
		SelectCountBuilder() squirrel.SelectBuilder
		FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error)
		FindNoCacheByQuery(ctx context.Context, selectBuilder squirrel.SelectBuilder) ([]*ProtobufDependence, error)

		BatchInsert(ctx context.Context, session sqlx.Session, dependencies []*ProtobufDependence) (sql.Result, error)
		FindAll(ctx context.Context, projectID string) ([]*ProtobufDependence, error)
		FindByConfigID(ctx context.Context, projectID, configID string) ([]*ProtobufDependence, error)
		FindByDepConfigID(ctx context.Context, projectID, depConfigID string) ([]*ProtobufDependence, error)
		RemoveByConfigID(ctx context.Context, session sqlx.Session, projectID, configID string) (sql.Result, error)
	}

	customProtobufDependenceModel struct {
		*defaultProtobufDependenceModel

		conn sqlx.SqlConn
	}
)

// NewProtobufDependenceModel returns a model for the database table.
func NewProtobufDependenceModel(conn sqlx.SqlConn, c cache.CacheConf, opts ...cache.Option) ProtobufDependenceModel {
	return &customProtobufDependenceModel{
		defaultProtobufDependenceModel: newProtobufDependenceModel(conn, c, opts...),
		conn:                           conn,
	}
}

func (m *customProtobufDependenceModel) Table() string {
	return m.table
}

func (m *customProtobufDependenceModel) Fields() []string {
	return protobufDependenceFieldNames
}

func (m *customProtobufDependenceModel) Trans(
	ctx context.Context, fn func(context context.Context, session sqlx.Session) error,
) error {
	return m.TransactCtx(ctx, fn)
}

func (m *customProtobufDependenceModel) InsertBuilder(data *ProtobufDependence) squirrel.InsertBuilder {
	return squirrel.Insert(m.table).Columns(protobufDependenceInsertFields...).Values(
		data.ProjectId, data.ConfigId, data.DepConfigId, data.Deleted, data.CreatedBy, data.UpdatedBy,
	)
}

func (m *customProtobufDependenceModel) UpdateBuilder(data *ProtobufDependence) squirrel.UpdateBuilder {
	eq := squirrel.Eq{
		"`deleted`":    data.Deleted,
		"`updated_by`": data.UpdatedBy,
		"`deleted_by`": data.DeletedBy,
		"`deleted_at`": data.DeletedAt,
	}
	return squirrel.Update(m.table).SetMap(eq).Where("`id` = ?", data.Id)
}

func (m *customProtobufDependenceModel) SelectBuilder() squirrel.SelectBuilder {
	return squirrel.Select(protobufDependenceFieldNames...).Where("`deleted` = ?", constants.NotDeleted).From(m.table)
}

func (m *customProtobufDependenceModel) SelectCountBuilder() squirrel.SelectBuilder {
	return squirrel.Select("count(*)").Where("`deleted` = ?", constants.NotDeleted).From(m.table)
}

func (m *customProtobufDependenceModel) FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (
	int64, error,
) {
	query, values, err := countBuilder.ToSql()
	if err != nil {
		return 0, err
	}

	var resp int64
	err = m.QueryRowNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return 0, err
	}
}

func (m *customProtobufDependenceModel) FindNoCacheByQuery(
	ctx context.Context, selectBuilder squirrel.SelectBuilder,
) ([]*ProtobufDependence, error) {
	query, values, err := selectBuilder.ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*ProtobufDependence
	err = m.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

func (m *customProtobufDependenceModel) BatchInsert(
	ctx context.Context, session sqlx.Session, dependencies []*ProtobufDependence,
) (sql.Result, error) {
	count := len(dependencies)
	if count == 0 {
		return nil, nil
	} else if count == 1 {
		return m.Insert(ctx, session, dependencies[0])
	}

	builder := squirrel.Insert(m.table).Columns(protobufDependenceInsertFields...)
	for _, dep := range dependencies {
		builder = builder.Values(
			dep.ProjectId, dep.ConfigId, dep.DepConfigId, dep.Deleted, dep.CreatedBy, dep.UpdatedBy,
		)
	}

	return m.ExecCtx(
		ctx, func(ctx context.Context, conn sqlx.SqlConn) (sql.Result, error) {
			query, values, err := builder.ToSql()
			if err != nil {
				return nil, err
			}
			if session != nil {
				return session.ExecCtx(ctx, query, values...)
			}
			return conn.ExecCtx(ctx, query, values...)
		},
	)
}

func (m *customProtobufDependenceModel) FindAll(ctx context.Context, projectID string) ([]*ProtobufDependence, error) {
	return m.FindNoCacheByQuery(ctx, m.SelectBuilder().Where("`project_id` = ?", projectID))
}

func (m *customProtobufDependenceModel) FindByConfigID(
	ctx context.Context, projectID, configID string,
) ([]*ProtobufDependence, error) {
	return m.FindNoCacheByQuery(
		ctx, m.SelectBuilder().Where("`project_id` = ? AND `config_id` = ?", projectID, configID),
	)
}

func (m *customProtobufDependenceModel) FindByDepConfigID(
	ctx context.Context, projectID, depConfigID string,
) ([]*ProtobufDependence, error) {
	return m.FindNoCacheByQuery(
		ctx, m.SelectBuilder().Where("`project_id` = ? AND `dep_config_id` = ?", projectID, depConfigID),
	)
}

func (m *customProtobufDependenceModel) RemoveByConfigID(
	ctx context.Context, session sqlx.Session, projectID, configID string,
) (
	sql.Result, error,
) {
	keys := m.getKeysByConfigID(ctx, projectID, configID)

	return m.ExecCtx(
		ctx, func(ctx context.Context, conn sqlx.SqlConn) (sql.Result, error) {
			/*
				SQL:
				UPDATE `protobuf_dependence`
				SET `deleted` = ?, `deleted_by` = ?, `deleted_at` = ?
				WHERE `project_id` = ?
				  AND `config_id` = ?
			*/

			stmt, values, err := squirrel.Update(m.table).
				SetMap(
					squirrel.Eq{
						"`deleted`":    constants.HasDeleted,
						"`deleted_by`": userinfo.FromContext(ctx).Account,
						"`deleted_at`": time.Now(),
					},
				).
				Where("`project_id` = ? AND `config_id` = ?", projectID, configID).
				ToSql()
			if err != nil {
				return nil, err
			}

			if session != nil {
				return session.ExecCtx(ctx, stmt, values...)
			}
			return conn.ExecCtx(ctx, stmt, values...)
		}, keys...,
	)
}

func (m *customProtobufDependenceModel) getKeysByConfigID(ctx context.Context, projectID, configID string) []string {
	cs, err := m.FindByConfigID(ctx, projectID, configID)
	if err != nil {
		return make([]string, 0)
	}

	keys := make([]string, 0, len(cs))
	for _, c := range cs {
		keys = append(keys, fmt.Sprintf("%s%v", cacheManagerProtobufDependenceIdPrefix, c.Id))
	}

	return keys
}
