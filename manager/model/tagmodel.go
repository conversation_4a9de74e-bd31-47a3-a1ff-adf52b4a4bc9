package model

import (
	"context"
	"database/sql"
	"fmt"
	"time"

	"github.com/Masterminds/squirrel"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/types"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/userinfo"
)

var (
	_ TagModel = (*customTagModel)(nil)

	tagInsertFields = stringx.Remove(
		tagFieldNames, "`id`", "`create_time`", "`update_time`", "`deleted_by`", "`created_at`", "`updated_at`",
		"`deleted_at`",
	)

	cacheManagerTagProjectIdTypeNamePrefix = "cache:manager:tag:projectId:type:name:"
)

type (
	// TagModel is an interface to be customized, add more methods here,
	// and implement the added methods in customTagModel.
	TagModel interface {
		tagModel
		types.DBModel

		Trans(ctx context.Context, fn func(context context.Context, session sqlx.Session) error) error
		InsertBuilder(data *Tag) squirrel.InsertBuilder
		UpdateBuilder(data *Tag) squirrel.UpdateBuilder
		SelectBuilder() squirrel.SelectBuilder
		SelectCountBuilder() squirrel.SelectBuilder
		FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error)
		FindNoCacheByQuery(ctx context.Context, builder squirrel.SelectBuilder) ([]*Tag, error)

		InsertTX(ctx context.Context, session sqlx.Session, data *Tag) (sql.Result, error)
		UpdateTX(ctx context.Context, session sqlx.Session, data *Tag) (sql.Result, error)
		FindOneByProjectIdTypeName(ctx context.Context, projectId, tp, name string) (*Tag, error)
		RemoveByTagId(ctx context.Context, session sqlx.Session, projectId, tagId string) (sql.Result, error)
	}

	customTagModel struct {
		*defaultTagModel
	}
)

// NewTagModel returns a model for the database table.
func NewTagModel(conn sqlx.SqlConn, c cache.CacheConf) TagModel {
	return &customTagModel{
		defaultTagModel: newTagModel(conn, c),
	}
}

func (m *customTagModel) Table() string {
	return m.table
}

func (m *customTagModel) Fields() []string {
	return tagFieldNames
}

func (m *customTagModel) Trans(
	ctx context.Context, fn func(context context.Context, session sqlx.Session) error,
) error {
	return m.TransactCtx(ctx, fn)
}

func (m *customTagModel) InsertBuilder(data *Tag) squirrel.InsertBuilder {
	return squirrel.Insert(m.table).Columns(tagInsertFields...).Values(
		data.ProjectId, data.Type, data.TagId, data.Name, data.Description, data.Status, data.Deleted, data.CreatedBy,
		data.UpdatedBy,
	)
}

func (m *customTagModel) UpdateBuilder(data *Tag) squirrel.UpdateBuilder {
	eq := squirrel.Eq{
		"`name`":        data.Name,
		"`description`": data.Description,
		"`status`":      data.Status,
		"`deleted`":     data.Deleted,
		"`updated_by`":  data.UpdatedBy,
		"`deleted_by`":  data.DeletedBy,
		"`deleted_at`":  data.DeletedAt,
	}
	return squirrel.Update(m.table).SetMap(eq).Where("`id` = ?", data.Id)
}

func (m *customTagModel) SelectBuilder() squirrel.SelectBuilder {
	return squirrel.Select(tagFieldNames...).Where("`deleted` = ?", constants.NotDeleted).From(m.table)
}

func (m *customTagModel) SelectCountBuilder() squirrel.SelectBuilder {
	return squirrel.Select("count(*)").Where("`deleted` = ?", constants.NotDeleted).From(m.table)
}

func (m *customTagModel) FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error) {
	query, values, err := countBuilder.ToSql()
	if err != nil {
		return 0, err
	}

	var resp int64
	err = m.QueryRowNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return 0, err
	}
}

func (m *customTagModel) FindNoCacheByQuery(ctx context.Context, builder squirrel.SelectBuilder) ([]*Tag, error) {
	query, values, err := builder.ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*Tag
	err = m.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

func (m *customTagModel) InsertTX(ctx context.Context, session sqlx.Session, data *Tag) (sql.Result, error) {
	managerTagIdKey := fmt.Sprintf("%s%v", cacheManagerTagIdPrefix, data.Id)
	managerTagProjectIdTagIdKey := fmt.Sprintf(
		"%s%v:%v", cacheManagerTagProjectIdTagIdPrefix, data.ProjectId, data.TagId,
	)
	managerTagProjectIdTypeNameKey := fmt.Sprintf(
		"%s%v:%v:%v", cacheManagerTagProjectIdTypeNamePrefix, data.ProjectId, data.Type, data.Name,
	)

	return m.ExecCtx(
		ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
			query, values, err := m.InsertBuilder(data).ToSql()
			if err != nil {
				return nil, err
			}
			if session != nil {
				return session.ExecCtx(ctx, query, values...)
			}
			return conn.ExecCtx(ctx, query, values...)
		}, managerTagIdKey, managerTagProjectIdTagIdKey, managerTagProjectIdTypeNameKey,
	)
}

func (m *customTagModel) UpdateTX(ctx context.Context, session sqlx.Session, newData *Tag) (sql.Result, error) {
	data, err := m.FindOne(ctx, newData.Id)
	if err != nil {
		return nil, err
	}

	managerTagIdKey := fmt.Sprintf("%s%v", cacheManagerTagIdPrefix, data.Id)
	managerTagProjectIdTagIdKey := fmt.Sprintf(
		"%s%v:%v", cacheManagerTagProjectIdTagIdPrefix, data.ProjectId, data.TagId,
	)
	managerTagProjectIdTypeNameKey := fmt.Sprintf(
		"%s%v:%v:%v", cacheManagerTagProjectIdTypeNamePrefix, data.ProjectId, data.Type, data.Name,
	)

	return m.ExecCtx(
		ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
			query, values, err := m.UpdateBuilder(newData).ToSql()
			if err != nil {
				return nil, err
			}
			if session != nil {
				return session.ExecCtx(ctx, query, values...)
			}
			return conn.ExecCtx(ctx, query, values...)
		}, managerTagIdKey, managerTagProjectIdTagIdKey, managerTagProjectIdTypeNameKey,
	)
}

func (m *customTagModel) FindOneByProjectIdTypeName(ctx context.Context, projectId, tp, name string) (*Tag, error) {
	managerTagProjectIdTypeNameKey := fmt.Sprintf(
		"%s%v:%v:%v", cacheManagerTagProjectIdTypeNamePrefix, projectId, tp, name,
	)

	var resp Tag
	err := m.QueryRowIndexCtx(
		ctx, &resp, managerTagProjectIdTypeNameKey, m.formatPrimary,
		func(ctx context.Context, conn sqlx.SqlConn, v any) (any, error) {
			query, values, err := m.SelectBuilder().Where(
				"`project_id` = ? AND `type` = ? AND `name` = ?", projectId, tp, name,
			).OrderBy("`updated_at` DESC").Limit(1).ToSql()
			if err != nil {
				return nil, err
			}

			if err = conn.QueryRowCtx(ctx, &resp, query, values...); err != nil {
				return nil, err
			}

			return resp.Id, nil
		}, m.queryPrimary,
	)
	switch err {
	case nil:
		return &resp, nil
	default:
		return nil, err
	}
}

func (m *customTagModel) RemoveByTagId(ctx context.Context, session sqlx.Session, projectId, tagId string) (
	sql.Result, error,
) {
	keys := m.getKeysByTagId(ctx, projectId, tagId)

	return m.ExecCtx(
		ctx, func(ctx context.Context, conn sqlx.SqlConn) (sql.Result, error) {
			/*
				SQL:
				UPDATE `tag`
				SET `deleted` = ?, `deleted_by` = ?, `deleted_at` = ?
				WHERE `project_id` = ?
				  AND `tag_id` = ?
			*/
			stmt, values, err := squirrel.Update(m.table).
				SetMap(
					squirrel.Eq{
						"`deleted`":    constants.HasDeleted,
						"`deleted_by`": userinfo.FromContext(ctx).Account,
						"`deleted_at`": time.Now(),
					},
				).
				Where("`project_id` = ? AND `tag_id` = ?", projectId, tagId).
				ToSql()
			if err != nil {
				return nil, err
			}

			if session != nil {
				return session.ExecCtx(ctx, stmt, values...)
			}
			return conn.ExecCtx(ctx, stmt, values...)
		}, keys...,
	)
}

func (m *customTagModel) getKeysByTagId(ctx context.Context, projectId, tagId string) []string {
	tag, err := m.FindOneByProjectIdTagId(ctx, projectId, tagId)
	if err != nil {
		return make([]string, 0)
	}

	return []string{
		fmt.Sprintf("%s%v", cacheManagerTagIdPrefix, tag.Id),
		fmt.Sprintf("%s%v:%v", cacheManagerTagProjectIdTagIdPrefix, tag.ProjectId, tag.TagId),
	}
}
