package model

import (
	"context"
	"database/sql"
	"fmt"
	"time"

	"github.com/Masterminds/squirrel"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/types"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/userinfo"
)

var (
	_ UiPlanReferenceRelationshipModel = (*customUiPlanReferenceRelationshipModel)(nil)

	uiPlanReferenceRelationshipInsertFields = stringx.Remove(
		uiPlanReferenceRelationshipFieldNames, "`id`", "`create_time`", "`update_time`", "`deleted_by`", "`created_at`",
		"`updated_at`", "`deleted_at`",
	)
)

type (
	// UiPlanReferenceRelationshipModel is an interface to be customized, add more methods here,
	// and implement the added methods in customUiPlanReferenceRelationshipModel.
	UiPlanReferenceRelationshipModel interface {
		uiPlanReferenceRelationshipModel
		types.DBModel

		Trans(ctx context.Context, fn func(context context.Context, session sqlx.Session) error) error
		InsertBuilder(data *UiPlanReferenceRelationship) squirrel.InsertBuilder
		UpdateBuilder(data *UiPlanReferenceRelationship) squirrel.UpdateBuilder
		SelectBuilder() squirrel.SelectBuilder
		SelectCountBuilder() squirrel.SelectBuilder
		FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error)
		FindNoCacheByQuery(ctx context.Context, selectBuilder squirrel.SelectBuilder) (
			[]*UiPlanReferenceRelationship, error,
		)

		FindReferenceByPlanId(ctx context.Context, projectId, planId string) ([]*UiPlanReferenceRelationship, error)
		RemoveByPlanId(ctx context.Context, session sqlx.Session, projectId, planId string) (sql.Result, error)
	}

	customUiPlanReferenceRelationshipModel struct {
		*defaultUiPlanReferenceRelationshipModel
	}
)

// NewUiPlanReferenceRelationshipModel returns a model for the database table.
func NewUiPlanReferenceRelationshipModel(
	conn sqlx.SqlConn, c cache.CacheConf, opts ...cache.Option,
) UiPlanReferenceRelationshipModel {
	return &customUiPlanReferenceRelationshipModel{
		defaultUiPlanReferenceRelationshipModel: newUiPlanReferenceRelationshipModel(conn, c, opts...),
	}
}

func (m *customUiPlanReferenceRelationshipModel) Table() string {
	return m.table
}

func (m *customUiPlanReferenceRelationshipModel) Fields() []string {
	return uiPlanReferenceRelationshipFieldNames
}

func (m *customUiPlanReferenceRelationshipModel) Trans(
	ctx context.Context, fn func(context context.Context, session sqlx.Session) error,
) error {
	return m.TransactCtx(ctx, fn)
}

func (m *customUiPlanReferenceRelationshipModel) InsertBuilder(data *UiPlanReferenceRelationship) squirrel.InsertBuilder {
	return squirrel.Insert(m.table).Columns(uiPlanReferenceRelationshipInsertFields...).Values(
		data.ProjectId, data.GitConfigId, data.Path, data.PlanId, data.Deleted, data.CreatedBy, data.UpdatedBy,
	)
}

func (m *customUiPlanReferenceRelationshipModel) UpdateBuilder(data *UiPlanReferenceRelationship) squirrel.UpdateBuilder {
	eq := squirrel.Eq{
		"`deleted`":    data.Deleted,
		"`updated_by`": data.UpdatedBy,
		"`deleted_by`": data.DeletedBy,
		"`deleted_at`": data.DeletedAt,
	}
	return squirrel.Update(m.table).SetMap(eq).Where("`id` = ?", data.Id)
}

func (m *customUiPlanReferenceRelationshipModel) SelectBuilder() squirrel.SelectBuilder {
	return squirrel.Select(uiPlanReferenceRelationshipFieldNames...).Where(
		"`deleted` = ?", constants.NotDeleted,
	).From(m.table)
}

func (m *customUiPlanReferenceRelationshipModel) SelectCountBuilder() squirrel.SelectBuilder {
	return squirrel.Select("count(*)").Where("`deleted` = ?", constants.NotDeleted).From(m.table)
}

func (m *customUiPlanReferenceRelationshipModel) FindCount(
	ctx context.Context, countBuilder squirrel.SelectBuilder,
) (int64, error) {
	query, values, err := countBuilder.ToSql()
	if err != nil {
		return 0, err
	}

	var resp int64
	err = m.QueryRowNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return 0, err
	}
}

func (m *customUiPlanReferenceRelationshipModel) FindNoCacheByQuery(
	ctx context.Context, selectBuilder squirrel.SelectBuilder,
) ([]*UiPlanReferenceRelationship, error) {
	query, values, err := selectBuilder.ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*UiPlanReferenceRelationship
	err = m.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

// FindReferenceByPlanId 根据UI计划ID查询引用关系（返回指定计划下的全部引用关系）
func (m *customUiPlanReferenceRelationshipModel) FindReferenceByPlanId(
	ctx context.Context, projectId, planId string,
) ([]*UiPlanReferenceRelationship, error) {
	/*
		SQL
		SELECT *
		FROM `ui_plan_reference_relationship`
		WHERE `project_id` = ?
		  AND `plan_id` = ?
		  AND `deleted` = ?
	*/

	sb := m.SelectBuilder().Where("`project_id` = ? AND `plan_id` = ?", projectId, planId)

	return m.FindNoCacheByQuery(ctx, sb)
}

func (m *customUiPlanReferenceRelationshipModel) RemoveByPlanId(
	ctx context.Context, session sqlx.Session, projectId, planId string,
) (sql.Result, error) {
	keys := m.getKeysByPlanId(ctx, projectId, planId)

	return m.ExecCtx(
		ctx, func(ctx context.Context, conn sqlx.SqlConn) (sql.Result, error) {
			/*
				SQL:
				UPDATE `ui_plan_reference_relationship`
				SET `deleted` = ?, `deleted_by` = ?, `deleted_at` = ?
				WHERE `project_id` = ?
				  AND `plan_id` = ?
			*/

			stmt, values, err := squirrel.Update(m.table).
				SetMap(
					squirrel.Eq{
						"`deleted`":    constants.HasDeleted,
						"`deleted_by`": userinfo.FromContext(ctx).Account,
						"`deleted_at`": time.Now(),
					},
				).
				Where("`project_id` = ? AND `plan_id` = ?", projectId, planId).
				ToSql()
			if err != nil {
				return nil, err
			}

			if session != nil {
				return session.ExecCtx(ctx, stmt, values...)
			}
			return conn.ExecCtx(ctx, stmt, values...)
		}, keys...,
	)
}

func (m *customUiPlanReferenceRelationshipModel) getKeysByPlanId(
	ctx context.Context, projectId, planId string,
) []string {
	cs, err := m.FindReferenceByPlanId(ctx, projectId, planId)
	if err != nil {
		return make([]string, 0)
	}

	keys := make([]string, 0, len(cs))
	for _, c := range cs {
		keys = append(keys, fmt.Sprintf("%s%v", cacheManagerUiPlanReferenceRelationshipIdPrefix, c.Id))
	}

	return keys
}
