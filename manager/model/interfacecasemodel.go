package model

import (
	"context"
	"database/sql"
	"fmt"
	"time"

	"github.com/Masterminds/squirrel"
	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/utils"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sqlbuilder"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/types"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/userinfo"
)

var (
	_                                  InterfaceCaseModel = (*customInterfaceCaseModel)(nil)
	VirtualInterfaceCaseReferenceModel types.DBModel      = (*virtualInterfaceCaseReferenceModel)(nil)

	interfaceCaseInsertFields = stringx.Remove(
		interfaceCaseFieldNames, "`id`", "`create_time`", "`update_time`", "`deleted_by`", "`created_at`",
		"`updated_at`", "`deleted_at`",
	)
	virtualInterfaceCaseReferenceFieldNames = builder.RawFieldNames(&SearchInterfaceCaseReferenceItem{})
)

type (
	// InterfaceCaseModel is an interface to be customized, add more methods here,
	// and implement the added methods in customInterfaceCaseModel.
	InterfaceCaseModel interface {
		interfaceCaseModel
		types.DBModel

		Trans(ctx context.Context, fn func(context context.Context, session sqlx.Session) error) error
		InsertBuilder(data *InterfaceCase) squirrel.InsertBuilder
		UpdateBuilder(data *InterfaceCase) squirrel.UpdateBuilder
		SelectBuilder() squirrel.SelectBuilder
		SelectCountBuilder() squirrel.SelectBuilder
		FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error)
		FindNoCacheByQuery(ctx context.Context, builder squirrel.SelectBuilder) ([]*InterfaceCase, error)

		InsertTX(ctx context.Context, session sqlx.Session, data *InterfaceCase) (sql.Result, error)
		UpdateTX(ctx context.Context, session sqlx.Session, data *InterfaceCase) (sql.Result, error)
		DeleteTX(ctx context.Context, session sqlx.Session, id int64) error
		UpdateAllToNotLatest(
			ctx context.Context, session sqlx.Session, projectId, documentId, caseId string,
		) (sql.Result, error)
		FindOneByProjectIdDocumentIdCaseIdVersion(
			ctx context.Context, projectId, documentId, caseId, version string,
		) (*InterfaceCase, error)
		FindLatestOneNoCache(ctx context.Context, projectId, documentId, caseId string) (*InterfaceCase, error)
		FindLatestByDocumentId(ctx context.Context, projectId, documentId string) ([]*InterfaceCase, error)
		RemoveByCaseId(ctx context.Context, session sqlx.Session, projectId, documentId, caseId string) (
			sql.Result, error,
		)
		FindLatestOneNoCacheForProjectIdCaseId(ctx context.Context, projectId, caseId string) (*InterfaceCase, error)
		GenerateSearchInterfaceCaseReferenceSqlBuilder(req SearchInterfaceCaseReferenceReq) (
			searchInterfaceCaseReferenceSelectBuilder, searchInterfaceCaseReferenceCountBuilder,
		)
		FindCountInterfaceCaseReference(ctx context.Context, countBuilder searchInterfaceCaseReferenceCountBuilder) (int64, error)
		FindInterfaceCaseReference(
			ctx context.Context, selectBuilder searchInterfaceCaseReferenceSelectBuilder,
		) ([]*SearchInterfaceCaseReferenceItem, error)
	}

	customInterfaceCaseModel struct {
		*defaultInterfaceCaseModel

		conn sqlx.SqlConn
	}
)

// NewInterfaceCaseModel returns a model for the database table.
func NewInterfaceCaseModel(conn sqlx.SqlConn, c cache.CacheConf) InterfaceCaseModel {
	return &customInterfaceCaseModel{
		defaultInterfaceCaseModel: newInterfaceCaseModel(conn, c),
		conn:                      conn,
	}
}

func (m *customInterfaceCaseModel) Table() string {
	return m.table
}

func (m *customInterfaceCaseModel) Fields() []string {
	return interfaceCaseFieldNames
}

func (m *customInterfaceCaseModel) Trans(
	ctx context.Context, fn func(context context.Context, session sqlx.Session) error,
) error {
	return m.TransactCtx(ctx, fn)
}

func (m *customInterfaceCaseModel) InsertBuilder(data *InterfaceCase) squirrel.InsertBuilder {
	if data.CreatedAt == ZeroTime {
		return squirrel.Insert(m.table).Columns(interfaceCaseInsertFields...).Values(
			data.ProjectId, data.DocumentId, data.CaseId, data.Name, data.Description, data.Priority, data.Tags,
			data.State, data.AccountConfig, data.Version, data.Structure, data.Latest, data.Deleted, data.MaintainedBy,
			data.CreatedBy, data.UpdatedBy,
		)
	} else {
		return squirrel.Insert(m.table).Columns(interfaceCaseInsertFields...).Columns("`created_at`").Values(
			data.ProjectId, data.DocumentId, data.CaseId, data.Name, data.Description, data.Priority, data.Tags,
			data.State, data.AccountConfig, data.Version, data.Structure, data.Latest, data.Deleted, data.MaintainedBy,
			data.CreatedBy, data.UpdatedBy, data.CreatedAt,
		)
	}
}

func (m *customInterfaceCaseModel) UpdateBuilder(data *InterfaceCase) squirrel.UpdateBuilder {
	eq := squirrel.Eq{
		"`name`":           data.Name,
		"`description`":    data.Description,
		"`priority`":       data.Priority,
		"`tags`":           data.Tags,
		"`state`":          data.State,
		"`account_config`": data.AccountConfig,
		"`version`":        data.Version,
		"`structure`":      data.Structure,
		"`latest`":         data.Latest,
		"`deleted`":        data.Deleted,
		"`maintained_by`":  data.MaintainedBy,
		"`updated_by`":     data.UpdatedBy,
		"`deleted_by`":     data.DeletedBy,
		"`deleted_at`":     data.DeletedAt,
	}
	return squirrel.Update(m.table).SetMap(eq).Where("`id` = ?", data.Id)
}

func (m *customInterfaceCaseModel) SelectBuilder() squirrel.SelectBuilder {
	return squirrel.Select(interfaceCaseFieldNames...).Where("`deleted` = ?", constants.NotDeleted).From(m.table)
}

func (m *customInterfaceCaseModel) SelectCountBuilder() squirrel.SelectBuilder {
	return squirrel.Select("count(*)").Where("`deleted` = ?", constants.NotDeleted).From(m.table)
}

func (m *customInterfaceCaseModel) FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error) {
	query, values, err := countBuilder.ToSql()
	if err != nil {
		return 0, err
	}

	var resp int64
	err = m.QueryRowNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return 0, err
	}
}

func (m *customInterfaceCaseModel) FindNoCacheByQuery(
	ctx context.Context, builder squirrel.SelectBuilder,
) ([]*InterfaceCase, error) {
	query, values, err := builder.ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*InterfaceCase
	err = m.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

func (m *customInterfaceCaseModel) InsertTX(ctx context.Context, session sqlx.Session, data *InterfaceCase) (
	sql.Result, error,
) {
	managerInterfaceCaseIdKey := fmt.Sprintf("%s%v", cacheManagerInterfaceCaseIdPrefix, data.Id)
	managerInterfaceCaseProjectIdDocumentIdCaseIdVersionKey := fmt.Sprintf(
		"%s%v:%v:%v:%v", cacheManagerInterfaceCaseProjectIdDocumentIdCaseIdVersionPrefix, data.ProjectId,
		data.DocumentId, data.CaseId, data.Version,
	)

	return m.ExecCtx(
		ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
			query, values, err := m.InsertBuilder(data).ToSql()
			if err != nil {
				return nil, err
			}
			if session != nil {
				return session.ExecCtx(ctx, query, values...)
			}
			return conn.ExecCtx(ctx, query, values...)
		}, managerInterfaceCaseIdKey, managerInterfaceCaseProjectIdDocumentIdCaseIdVersionKey,
	)
}

func (m *customInterfaceCaseModel) UpdateTX(
	ctx context.Context, session sqlx.Session, newData *InterfaceCase,
) (sql.Result, error) {
	data, err := m.FindOne(ctx, newData.Id)
	if err != nil {
		return nil, err
	}

	managerInterfaceCaseIdKey := fmt.Sprintf("%s%v", cacheManagerInterfaceCaseIdPrefix, data.Id)
	managerInterfaceCaseProjectIdDocumentIdCaseIdVersionKey := fmt.Sprintf(
		"%s%v:%v:%v:%v", cacheManagerInterfaceCaseProjectIdDocumentIdCaseIdVersionPrefix, data.ProjectId,
		data.DocumentId, data.CaseId, data.Version,
	)

	return m.ExecCtx(
		ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
			query, values, err := m.UpdateBuilder(newData).ToSql()
			if err != nil {
				return nil, err
			}
			if session != nil {
				return session.ExecCtx(ctx, query, values...)
			}
			return conn.ExecCtx(ctx, query, values...)
		}, managerInterfaceCaseIdKey, managerInterfaceCaseProjectIdDocumentIdCaseIdVersionKey,
	)
}

func (m *customInterfaceCaseModel) DeleteTX(ctx context.Context, session sqlx.Session, id int64) error {
	data, err := m.FindOne(ctx, id)
	if err != nil {
		return err
	}

	managerInterfaceCaseIdKey := fmt.Sprintf("%s%v", cacheManagerInterfaceCaseIdPrefix, id)
	managerInterfaceCaseProjectIdDocumentIdCaseIdVersionKey := fmt.Sprintf(
		"%s%v:%v:%v:%v", cacheManagerInterfaceCaseProjectIdDocumentIdCaseIdVersionPrefix, data.ProjectId,
		data.DocumentId, data.CaseId, data.Version,
	)

	_, err = m.ExecCtx(
		ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
			query := fmt.Sprintf("delete from %s where `id` = ?", m.table)
			if session != nil {
				return session.ExecCtx(ctx, query, id)
			}
			return conn.ExecCtx(ctx, query, id)
		}, managerInterfaceCaseIdKey, managerInterfaceCaseProjectIdDocumentIdCaseIdVersionKey,
	)

	return err
}

func (m *customInterfaceCaseModel) UpdateAllToNotLatest(
	ctx context.Context, session sqlx.Session, projectId, documentId, caseId string,
) (sql.Result, error) {
	keys := m.getKeysByCaseId(ctx, projectId, documentId, caseId)

	ub := squirrel.Update(m.table).
		SetMap(squirrel.Eq{"`latest`": constants.IsNotLatestVersion}).
		Where(
			"`project_id` = ? AND `document_id` = ? AND `case_id` = ? AND `deleted` = ?", projectId, documentId, caseId,
			constants.NotDeleted,
		)

	return m.ExecCtx(
		ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
			query, values, err := ub.ToSql()
			if err != nil {
				return nil, err
			}
			if session != nil {
				return session.ExecCtx(ctx, query, values...)
			}
			return conn.ExecCtx(ctx, query, values...)
		}, keys...,
	)
}

func (m *customInterfaceCaseModel) FindOneByProjectIdDocumentIdCaseIdVersion(
	ctx context.Context, projectId, documentId, caseId, version string,
) (*InterfaceCase, error) {
	if version == "" {
		// 版本为空，则默认查询最新版本
		return m.FindLatestOneNoCache(ctx, projectId, documentId, caseId)
	}
	if documentId != "" {
		// 接口ID和版本都非空，则通过唯一索引进行查询
		return m.defaultInterfaceCaseModel.FindOneByProjectIdDocumentIdCaseIdVersion(
			ctx, projectId, documentId, caseId, version,
		)
	}

	// 接口ID为空，版本非空，则尝试查询返回最近一条记录
	sb := m.SelectBuilder().Where(
		"`project_id` = ? AND `case_id` = ? AND `version` = ?", projectId, caseId, version,
	).OrderBy("`updated_at` DESC").Limit(1)
	resp, err := m.FindNoCacheByQuery(ctx, sb)
	if err != nil {
		return nil, err
	} else if len(resp) == 0 {
		return nil, ErrNotFound
	}
	return resp[0], nil
}

func (m *customInterfaceCaseModel) FindLatestOneNoCache(
	ctx context.Context, projectId, documentId, caseId string,
) (*InterfaceCase, error) {
	sb := m.SelectBuilder()

	if documentId != "" {
		sb = sb.Where(
			"`project_id` = ? AND `document_id` = ? AND `case_id` = ? AND `latest` = ?", projectId, documentId, caseId,
			constants.IsLatestVersion,
		).OrderBy("`updated_at` DESC").Limit(1)
	} else {
		// 允许 `document_id` 为空字符串
		sb = sb.Where(
			"`project_id` = ? AND `case_id` = ? AND `latest` = ?", projectId, caseId, constants.IsLatestVersion,
		).OrderBy("`updated_at` DESC").Limit(1)
	}
	resp, err := m.FindNoCacheByQuery(ctx, sb)
	if err != nil {
		return nil, err
	} else if len(resp) == 0 {
		return nil, ErrNotFound
	}
	return resp[0], nil
}

func (m *customInterfaceCaseModel) FindLatestOneNoCacheForProjectIdCaseId(
	ctx context.Context, projectId, caseId string,
) (*InterfaceCase, error) {
	sb := m.SelectBuilder()
	sb = sb.Where(
		"`project_id` = ? AND `case_id` = ? AND `latest` = ?", projectId, caseId, constants.IsLatestVersion,
	).OrderBy("`updated_at` DESC").Limit(1)
	resp, err := m.FindNoCacheByQuery(ctx, sb)
	if err != nil {
		return nil, err
	} else if len(resp) == 0 {
		return nil, ErrNotFound
	}
	return resp[0], nil
}

func (m *customInterfaceCaseModel) FindLatestByDocumentId(
	ctx context.Context, projectId, documentId string,
) ([]*InterfaceCase, error) {
	sb := m.SelectBuilder().Where(
		"`project_id` = ? AND `document_id` = ? AND `latest` = ?", projectId, documentId, constants.IsLatestVersion,
	)
	return m.FindNoCacheByQuery(ctx, sb)
}

func (m *customInterfaceCaseModel) RemoveByCaseId(
	ctx context.Context, session sqlx.Session, projectId, documentId, caseId string,
) (sql.Result, error) {
	keys := m.getKeysByCaseId(ctx, projectId, documentId, caseId)

	return m.ExecCtx(
		ctx, func(ctx context.Context, conn sqlx.SqlConn) (sql.Result, error) {
			/*
				SQL:
				UPDATE `interface_case`
				SET `deleted` = ?, `deleted_by` = ?, `deleted_at` = ?
				WHERE `project_id` = ?
				  AND `case_id` = ?
			*/
			stmt, values, err := squirrel.Update(m.table).
				SetMap(
					squirrel.Eq{
						"`deleted`":    constants.HasDeleted,
						"`deleted_by`": userinfo.FromContext(ctx).Account,
						"`deleted_at`": time.Now(),
					},
				).
				Where("`project_id` = ? AND `case_id` = ?", projectId, caseId).
				ToSql()
			if err != nil {
				return nil, err
			}

			if session != nil {
				return session.ExecCtx(ctx, stmt, values...)
			}
			return conn.ExecCtx(ctx, stmt, values...)
		}, keys...,
	)
}

func (m *customInterfaceCaseModel) getKeysByCaseId(ctx context.Context, projectId, documentId, caseId string) []string {
	sb := m.SelectBuilder().Where(
		"`project_id` = ? AND `document_id` = ? AND `case_id` = ?", projectId, documentId, caseId,
	)
	cs, err := m.FindNoCacheByQuery(ctx, sb)
	if err != nil {
		return make([]string, 0)
	}

	keys := make([]string, 0, len(cs)*2)
	for _, c := range cs {
		keys = append(
			keys,
			fmt.Sprintf("%s%v", cacheManagerInterfaceCaseIdPrefix, c.Id),
			fmt.Sprintf(
				"%s%v:%v:%v:%v", cacheManagerInterfaceCaseProjectIdDocumentIdCaseIdVersionPrefix, c.ProjectId,
				c.DocumentId, c.CaseId, c.Version,
			),
		)
	}

	return keys
}

type virtualInterfaceCaseReferenceModel struct{}

func (m *virtualInterfaceCaseReferenceModel) Table() string {
	return "`virtual_interface_case_reference`"
}

func (m *virtualInterfaceCaseReferenceModel) Fields() []string {
	return virtualInterfaceCaseReferenceFieldNames
}

type SearchInterfaceCaseReferenceItem struct {
	ProjectId     string         `db:"project_id"`     // 项目ID
	CaseId        string         `db:"case_id"`        // 用例ID
	ReferenceType string         `db:"reference_type"` // 引用对象类型（API集合）
	ReferenceId   string         `db:"reference_id"`   // 引用对象ID（API集合ID）
	Name          string         `db:"name"`           // 引用对象名称
	Description   sql.NullString `db:"description"`    // 引用对象描述
	Priority      int64          `db:"priority"`       // 优先级（NULL、P0、P1、P2、P3...）
	Tags          sql.NullString `db:"tags"`           // 标签
	State         int64          `db:"state"`          // 引用对象状态（生效、失效）
	MaintainedBy  sql.NullString `db:"maintained_by"`  // 维护者的用户ID
	CreatedBy     string         `db:"created_by"`     // 创建者的用户ID
	UpdatedBy     string         `db:"updated_by"`     // 最近一次更新者的用户ID
	CreatedAt     time.Time      `db:"created_at"`     // 创建时间
	UpdatedAt     time.Time      `db:"updated_at"`     // 更新时间
}

type searchInterfaceCaseReferenceSelectBuilder struct {
	squirrel.SelectBuilder
}

type searchInterfaceCaseReferenceCountBuilder struct {
	squirrel.SelectBuilder
}

func (m *customInterfaceCaseModel) GenerateSearchInterfaceCaseReferenceSqlBuilder(req SearchInterfaceCaseReferenceReq) (
	searchInterfaceCaseReferenceSelectBuilder, searchInterfaceCaseReferenceCountBuilder,
) {
	/*
	   SELECT
	     t.`project_id`,
	     t.`case_id`,
	     t.`reference_type`,
	     t.`reference_id`,
	     t.`name`,
	     t.`description`,
	     t.`priority`,
	     t.`tags`,
	     t.`state`,
	     t.`maintained_by`,
	     t.`created_by`,
	     t.`updated_by`,
	     t.`created_at`,
	     t.`updated_at`
	   FROM
	     (
	       SELECT
	         t1.`project_id`,
	         t1.`case_id`,
	         'API_SUITE' AS `reference_type`,
	         t2.`suite_id` AS `reference_id`,
	         t3.`name`,
	         t3.`description`,
	         t3.`priority`,
	         t3.`tags`,
	         t3.`state`,
	         t3.`maintained_by`,
	         t3.`created_by`,
	         t3.`updated_by`,
	         t3.`created_at`,
	         t3.`updated_at`
	       FROM
	         `interface_case` AS t1
	         INNER JOIN `api_suite_reference_relationship` AS t2 ON t1.`project_id` = t2.`project_id`
	         AND t2.`reference_type` = 'INTERFACE_CASE'
	         AND t1.`case_id` = t2.`reference_id`
	         AND t1.`deleted` = t2.`deleted`
	         LEFT JOIN `api_suite` AS t3 ON t1.`project_id` = t3.`project_id`
	         AND t2.`suite_id` = t3.`suite_id`
	         AND t1.`deleted` = t3.`deleted`
	       WHERE
	         t1.`project_id` = ?
	         AND t1.`case_id` = ?
	         AND t1.`latest` = 1
	         AND t1.`deleted` = 0
	         AND t3.`id` IS NOT NULL
	     ) AS t;
	*/

	fields := []string{
		"t1.`project_id`",
		"t1.`case_id`",
		"'API_SUITE' AS `reference_type`",
		"t2.`suite_id` AS `reference_id`",
		"t3.`name`",
		"t3.`description`",
		"t3.`priority`",
		"t3.`tags`",
		"t3.`state`",
		"t3.`maintained_by`",
		"t3.`created_by`",
		"t3.`updated_by`",
		"t3.`created_at`",
		"t3.`updated_at`",
	}
	tmp := squirrel.Select(fields...).From(m.table+" AS t1").
		InnerJoin(
			fmt.Sprintf("%s AS t2 ON t1.`project_id` = t2.`project_id` AND t2.`reference_type` = '%s' AND t1.`case_id` = t2.`reference_id` AND t1.`deleted` = t2.`deleted`",
				apiSuiteReferenceRelationshipTableName, common.ConstReferenceTypeInterfaceCase,
			),
		).
		LeftJoin(
			apiSuiteTableName+" AS t3 ON t1.`project_id` = t3.`project_id` AND t2.`suite_id` = t3.`suite_id` AND t1.`deleted` = t3.`deleted`",
		).
		Where(
			"t1.`project_id` = ? AND t1.`case_id` = ? AND t1.`latest` = ? AND t1.`deleted` = ? AND t3.`id` IS NOT NULL",
			req.ProjectId, req.CaseId, constants.IsLatestVersion, constants.NotDeleted,
		)

	rm := VirtualInterfaceCaseReferenceModel

	alias := "t"
	sb := sqlbuilder.SearchOptionsWithAlias(
		squirrel.Select(utils.AddTableNameToFields(alias, rm.Fields())...).FromSelect(tmp, alias),
		alias,
		sqlbuilder.WithCondition(rm, req.Condition),
		sqlbuilder.WithPagination(rm, req.Pagination),
		sqlbuilder.WithSort(rm, req.Sort),
	)
	scb := sqlbuilder.SearchOptionsWithAlias(
		squirrel.Select("COUNT(*)").FromSelect(tmp, alias),
		alias,
		sqlbuilder.WithCondition(rm, req.Condition),
	)

	return searchInterfaceCaseReferenceSelectBuilder{sb}, searchInterfaceCaseReferenceCountBuilder{scb}
}

func (m *customInterfaceCaseModel) FindCountInterfaceCaseReference(ctx context.Context, countBuilder searchInterfaceCaseReferenceCountBuilder) (int64, error) {
	return m.FindCount(ctx, countBuilder.SelectBuilder)
}

func (m *customInterfaceCaseModel) FindInterfaceCaseReference(
	ctx context.Context, selectBuilder searchInterfaceCaseReferenceSelectBuilder,
) ([]*SearchInterfaceCaseReferenceItem, error) {
	var resp []*SearchInterfaceCaseReferenceItem

	err := utils.FindRows(
		ctx, m.conn, selectBuilder.SelectBuilder, func() any {
			return &resp
		},
	)

	return resp, err
}

// [2024-04-16] delete this method:
// Validate 判断API用例是否可被执行
//func (x *InterfaceCase) Validate() bool {
//	// 接口用例的固有状态为生效才能被执行
//	return x.State == int64(constants.EnableStatus)
//}
