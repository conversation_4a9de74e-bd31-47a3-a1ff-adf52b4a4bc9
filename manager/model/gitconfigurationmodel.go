package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/Masterminds/squirrel"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sqlbuilder"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/types"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/userinfo"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/utils"
)

var (
	_ GitConfigurationModel = (*customGitConfigurationModel)(nil)

	gitConfigurationInsertFields = stringx.Remove(
		gitConfigurationFieldNames, "`id`", "`create_time`", "`update_time`", "`deleted_by`", "`created_at`",
		"`updated_at`", "`deleted_at`",
	)
	gitConfigurationUpdateFields = strings.Join(
		stringx.Remove(
			gitConfigurationFieldNames, "`id`", "`type`", "`url`", "`branch`", "`deleted`", "`created_by`",
			"`created_at`", "`deleted_by`", "`deleted_at`",
		), "=?,",
	) + "=?"
)

type (
	// GitConfigurationModel is an interface to be customized, add more methods here,
	// and implement the added methods in customGitConfigurationModel.
	GitConfigurationModel interface {
		gitConfigurationModel
		types.DBModel

		Trans(ctx context.Context, fn func(context context.Context, session sqlx.Session) error) error
		InsertBuilder(data *GitConfiguration) squirrel.InsertBuilder
		UpdateBuilder(data *GitConfiguration) squirrel.UpdateBuilder
		SelectBuilder() squirrel.SelectBuilder
		SelectCountBuilder() squirrel.SelectBuilder
		FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error)
		FindNoCacheByQuery(ctx context.Context, selectBuilder squirrel.SelectBuilder) ([]*GitConfiguration, error)

		InsertTX(ctx context.Context, session sqlx.Session, data *GitConfiguration) (sql.Result, error)
		UpdateTX(ctx context.Context, session sqlx.Session, data *GitConfiguration) (sql.Result, error)
		DeleteTX(ctx context.Context, session sqlx.Session, id int64) error

		GenerateSearchGitConfigurationSqlBuilder(req SearchGitConfigurationReq) (
			searchGitConfigurationSelectBuilder, searchGitConfigurationCountBuilder,
		)
		FindCountGitConfigurations(ctx context.Context, countBuilder searchGitConfigurationCountBuilder) (int64, error)
		FindGitConfigurations(
			ctx context.Context, selectBuilder searchGitConfigurationSelectBuilder,
		) ([]*GitConfiguration, error)

		FindGitConfigurationsByUrlAndBranch(ctx context.Context, Url, Branch string) ([]*GitConfiguration, error)
		RemoveByConfigId(ctx context.Context, session sqlx.Session, projectId, configId string) (sql.Result, error)
	}

	customGitConfigurationModel struct {
		*defaultGitConfigurationModel

		conn sqlx.SqlConn
	}
)

// NewGitConfigurationModel returns a model for the database table.
func NewGitConfigurationModel(conn sqlx.SqlConn, c cache.CacheConf, opts ...cache.Option) GitConfigurationModel {
	return &customGitConfigurationModel{
		defaultGitConfigurationModel: newGitConfigurationModel(conn, c, opts...),
		conn:                         conn,
	}
}

func (m *customGitConfigurationModel) Table() string {
	return m.table
}

func (m *customGitConfigurationModel) Fields() []string {
	return gitConfigurationFieldNames
}

func (m *customGitConfigurationModel) Trans(
	ctx context.Context, fn func(context context.Context, session sqlx.Session) error,
) error {
	return m.TransactCtx(ctx, fn)
}

func (m *customGitConfigurationModel) InsertBuilder(data *GitConfiguration) squirrel.InsertBuilder {
	return squirrel.Insert(m.table).Columns(gitConfigurationInsertFields...).Values(
		data.ProjectId, data.ConfigId, data.Type, data.Name, data.Description, data.Url, data.AccessToken, data.Branch,
		data.Purpose, data.Deleted, data.CreatedBy, data.UpdatedBy,
	)
}

func (m *customGitConfigurationModel) UpdateBuilder(data *GitConfiguration) squirrel.UpdateBuilder {
	eq := squirrel.Eq{
		"`name`":         data.Name,
		"`description`":  data.Description,
		"`access_token`": data.AccessToken,
		"`deleted`":      data.Deleted,
		"`updated_by`":   data.UpdatedBy,
		"`deleted_by`":   data.DeletedBy,
		"`deleted_at`":   data.DeletedAt,
	}
	return squirrel.Update(m.table).SetMap(eq).Where("`id` = ?", data.Id)
}

func (m *customGitConfigurationModel) SelectBuilder() squirrel.SelectBuilder {
	return squirrel.Select(gitConfigurationFieldNames...).Where("`deleted` = ?", constants.NotDeleted).From(m.table)
}

func (m *customGitConfigurationModel) SelectCountBuilder() squirrel.SelectBuilder {
	return squirrel.Select("count(*)").Where("`deleted` = ?", constants.NotDeleted).From(m.table)
}

func (m *customGitConfigurationModel) FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (
	int64, error,
) {
	query, values, err := countBuilder.ToSql()
	if err != nil {
		return 0, err
	}

	var resp int64
	err = m.QueryRowNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return 0, err
	}
}

func (m *customGitConfigurationModel) FindNoCacheByQuery(
	ctx context.Context, selectBuilder squirrel.SelectBuilder,
) ([]*GitConfiguration, error) {
	query, values, err := selectBuilder.ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*GitConfiguration
	err = m.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

func (m *customGitConfigurationModel) InsertTX(
	ctx context.Context, session sqlx.Session, data *GitConfiguration,
) (sql.Result, error) {
	managerGitConfigurationIdKey := fmt.Sprintf("%s%v", cacheManagerGitConfigurationIdPrefix, data.Id)
	managerGitConfigurationProjectIdPlanIdKey := fmt.Sprintf(
		"%s%v:%v", cacheManagerGitConfigurationProjectIdConfigIdPrefix, data.ProjectId, data.ConfigId,
	)

	return m.ExecCtx(
		ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
			query, values, err := m.InsertBuilder(data).ToSql()
			if err != nil {
				return nil, err
			}
			if session != nil {
				return conn.ExecCtx(ctx, query, values...)
			}
			return conn.ExecCtx(ctx, query, values...)
		}, managerGitConfigurationIdKey, managerGitConfigurationProjectIdPlanIdKey,
	)
}

func (m *customGitConfigurationModel) UpdateTX(
	ctx context.Context, session sqlx.Session, data *GitConfiguration,
) (sql.Result, error) {
	managerGitConfigurationIdKey := fmt.Sprintf("%s%v", cacheManagerGitConfigurationIdPrefix, data.Id)
	managerGitConfigurationProjectIdPlanIdKey := fmt.Sprintf(
		"%s%v:%v", cacheManagerGitConfigurationProjectIdConfigIdPrefix, data.ProjectId, data.ConfigId,
	)

	return m.ExecCtx(
		ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
			query, values, err := m.UpdateBuilder(data).ToSql()
			if err != nil {
				return nil, err
			}
			if session != nil {
				return session.ExecCtx(ctx, query, values...)
			}
			return conn.ExecCtx(ctx, query, values...)
		}, managerGitConfigurationIdKey, managerGitConfigurationProjectIdPlanIdKey,
	)
}

func (m *customGitConfigurationModel) DeleteTX(ctx context.Context, session sqlx.Session, id int64) error {
	data, err := m.FindOne(ctx, id)
	if err != nil {
		return err
	}

	managerGitConfigurationIdKey := fmt.Sprintf("%s%v", cacheManagerGitConfigurationIdPrefix, id)
	managerGitConfigurationProjectIdPlanIdKey := fmt.Sprintf(
		"%s%v:%v", cacheManagerGitConfigurationProjectIdConfigIdPrefix, data.ProjectId, data.ConfigId,
	)

	_, err = m.ExecCtx(
		ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
			query := fmt.Sprintf("delete from %s where `id` = ?", m.table)
			if session != nil {
				return session.ExecCtx(ctx, query, id)
			}
			return conn.ExecCtx(ctx, query, id)
		}, managerGitConfigurationIdKey, managerGitConfigurationProjectIdPlanIdKey,
	)

	return err
}

type searchGitConfigurationSelectBuilder struct {
	squirrel.SelectBuilder
}

type searchGitConfigurationCountBuilder struct {
	squirrel.SelectBuilder
}

func (m *customGitConfigurationModel) GenerateSearchGitConfigurationSqlBuilder(req SearchGitConfigurationReq) (
	searchGitConfigurationSelectBuilder, searchGitConfigurationCountBuilder,
) {
	var sb, scb squirrel.SelectBuilder

	tmp := squirrel.Select("*").
		From(m.table).
		Where("`project_id` = ? AND `deleted` = ?", req.ProjectId, constants.NotDeleted).
		GroupBy("config_id")

	alias := "t"

	sb = squirrel.Select(utils.AddTableNameToFields(alias, gitConfigurationFieldNames)...).FromSelect(tmp, alias)
	scb = squirrel.Select("COUNT(*)").FromSelect(tmp, alias)

	sb = sqlbuilder.SearchOptionsWithAlias(
		sb, alias, sqlbuilder.WithCondition(m, req.Condition), sqlbuilder.WithPagination(m, req.Pagination),
		sqlbuilder.WithSort(m, req.Sort),
	)
	scb = sqlbuilder.SearchOptionsWithAlias(scb, alias, sqlbuilder.WithCondition(m, req.Condition))

	return searchGitConfigurationSelectBuilder{SelectBuilder: sb}, searchGitConfigurationCountBuilder{SelectBuilder: scb}
}

func (m *customGitConfigurationModel) FindCountGitConfigurations(
	ctx context.Context, countBuilder searchGitConfigurationCountBuilder,
) (int64, error) {
	return m.FindCount(ctx, countBuilder.SelectBuilder)
}

func (m *customGitConfigurationModel) FindGitConfigurations(
	ctx context.Context, selectBuilder searchGitConfigurationSelectBuilder,
) ([]*GitConfiguration, error) {
	var resp []*GitConfiguration

	err := utils.FindRows(
		ctx, m.conn, selectBuilder.SelectBuilder, func() any {
			return &resp
		},
	)

	return resp, err
}

func (m *customGitConfigurationModel) FindGitConfigurationsByUrlAndBranch(
	ctx context.Context, Url, Branch string,
) ([]*GitConfiguration, error) {
	sb := m.SelectBuilder().Where("`url` = ? AND `branch` = ?", Url, Branch)
	return m.FindNoCacheByQuery(ctx, sb)
}

func (m *customGitConfigurationModel) RemoveByConfigId(
	ctx context.Context, session sqlx.Session, projectId, configId string,
) (sql.Result, error) {
	keys := m.getKeysByConfigId(ctx, projectId, configId)

	return m.ExecCtx(
		ctx, func(ctx context.Context, conn sqlx.SqlConn) (sql.Result, error) {
			/*
				SQL:
				UPDATE `git_configuration`
				SET `deleted` = ?, `deleted_by` = ?, `deleted_at` = ?
				WHERE `project_id` = ?
				  AND `plan_id` = ?
			*/
			stmt, values, err := squirrel.Update(m.table).
				SetMap(
					squirrel.Eq{
						"`deleted`":    constants.HasDeleted,
						"`deleted_by`": userinfo.FromContext(ctx).Account,
						"`deleted_at`": time.Now(),
					},
				).
				Where("`project_id` = ? AND `config_id` = ?", projectId, configId).
				ToSql()
			if err != nil {
				return nil, err
			}

			if session != nil {
				return session.ExecCtx(ctx, stmt, values...)
			}
			return conn.ExecCtx(ctx, stmt, values...)
		}, keys...,
	)
}

func (m *customGitConfigurationModel) getKeysByConfigId(ctx context.Context, projectId, configId string) []string {
	configuration, err := m.FindOneByProjectIdConfigId(ctx, projectId, configId)
	if err != nil {
		return make([]string, 0)
	}

	return []string{
		fmt.Sprintf("%s%v", cacheManagerGitConfigurationIdPrefix, configuration.Id),
		fmt.Sprintf(
			"%s%v:%v", cacheManagerGitConfigurationProjectIdConfigIdPrefix, configuration.ProjectId,
			configuration.ConfigId,
		),
	}
}
