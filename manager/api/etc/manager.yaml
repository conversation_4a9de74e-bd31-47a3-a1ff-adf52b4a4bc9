Name: api.manager
Host: 0.0.0.0
Port: 20201
Verbose: true
MaxBytes: 20971520 # 20MB
Timeout: 0

Log:
  ServiceName: api.manager
  Encoding: plain
  Level: info
  Path: /app/logs/manager

#Prometheus:
#  Host: 0.0.0.0
#  Port: 20221
#  Path: /metrics

#Telemetry:
#  Name: api.manager
#  Endpoint: http://127.0.0.1:14268/api/traces
#  Sampler: 1.0
#  Batcher: jaeger

#DevServer:
#  Enabled: true
#  Port: 20231

DB:
#  DataSource: root:Quwan@2020@tcp(127.0.0.1:3306)/manager?charset=utf8mb4&parseTime=true&loc=Asia%2FShanghai
  DataSource: probe:Quwan@2020_TTinternation@tcp(************:3306)/manager?charset=utf8mb4&parseTime=true&loc=Asia%2FShanghai

Redis:
  Host: 127.0.0.1:6379
  Type: node
  Pass:
  DB: 2

Cache:
  - Host: 127.0.0.1:6379
    Type: node
    Pass:
    DB: 2

Validator:
  Locale: zh

Manager:
#  Etcd:
#    Hosts:
#      - 127.0.0.1:2379
#    Key: rpc.manager
  Endpoints:
    - 127.0.0.1:20211
  NonBlock: true
  Timeout: 0

Permission:
#  Etcd:
#    Hosts:
#      - 127.0.0.1:2379
#    Key: rpc.permission
  Endpoints:
    - 127.0.0.1:10211
  NonBlock: true
  Timeout: 0

User:
#  Etcd:
#    Hosts:
#      - 127.0.0.1:2379
#    Key: rpc.user
  Endpoints:
    - 127.0.0.1:10111
  NonBlock: true
  Timeout: 0

GitLab:
  Token: ********************

LarkCustomApp:
  # 正式版
  AppID: cli_a369eb0a70fe500b
  AppSecret: QJRZ0ECRJkMVR2omBH9EbUGoYsPczMLS
  # 测试版
  #AppID: cli_a36ed4f3ba3f100c
  #AppSecret: aKtiOYTAsTYhrqt4IBndRdYT6rS0Mkrj

PVCPath: ${PVC_PATH}
PVCNFSPath: ${PVC_NFS_PATH}
