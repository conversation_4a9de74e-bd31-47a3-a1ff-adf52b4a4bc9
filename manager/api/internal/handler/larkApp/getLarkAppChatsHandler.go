package larkApp

import (
	"net/http"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/response"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/logic/larkApp"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/svc"
)

// Deprecated: use `chats.GetIMChatsHandler` of `larkproxy` instead.
func GetLarkAppChatsHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		l := larkApp.NewGetLarkAppChatsLogic(r.Context(), svcCtx)
		resp, err := l.GetLarkAppChats()
		response.MakeHttpResponse(r, w, resp, err)
	}
}
