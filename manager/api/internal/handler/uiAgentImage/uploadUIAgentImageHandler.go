package uiAgentImage

import (
	"mime/multipart"
	"net/http"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/rest/httpx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/response"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/logic/uiAgentImage"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/types"
)

func UploadUIAgentImageHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var (
			req types.UploadUIAgentImageReq
			err error
		)

		if err = httpx.Parse(r, &req); err != nil {
			response.MakeHttpResponse(r, w, nil, errors.Wrapf(errorx.Err(errorx.ParseParamError, err.Error()), "failed to parse parameters, error: %+v", err))
			return
		}

		if err = svcCtx.Validator.Validate.StructCtx(r.Context(), req); err != nil {
			response.MakeHttpResponse(r, w, nil, errors.Wrapf(errorx.Err(errorx.ValidateParamError, svcCtx.Validator.Translate(err)), "failed to validate parameters, error: %+v", err))
			return
		}

		// parse the multipart form
		if err = r.ParseMultipartForm(maxFileSize); err != nil {
			response.MakeHttpResponse(
				r, w, nil, errors.Wrapf(
					errorx.Err(errorx.ParseParamError, err.Error()),
					"failed to parse multipart form, error: %+v",
					err,
				),
			)
			return
		}

		// get the file from the multipart form
		req.ImageFile, req.ImageFileHeader, err = r.FormFile(formFileKey)
		if err != nil {
			response.MakeHttpResponse(
				r, w, nil, errors.Wrapf(
					errorx.Err(errorx.ParseParamError, err.Error()),
					"failed to get the %q parameter, error: %+v",
					formFileKey, err,
				),
			)
			return
		}
		defer func(file multipart.File) {
			if file != nil {
				_ = file.Close()
			}
		}(req.ImageFile)

		l := uiAgentImage.NewUploadUIAgentImageLogic(r.Context(), svcCtx)
		resp, err := l.UploadUIAgentImage(&req)
		response.MakeHttpResponse(r, w, resp, err)
	}
}
