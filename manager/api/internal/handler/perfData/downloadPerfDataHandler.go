package perfData

import (
	"net/http"
	"os"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/rest/httpx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/response"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/logic/perfData"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/types"
)

func DownloadPerfDataHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.DownloadPerfDataReq
		if err := httpx.Parse(r, &req); err != nil {
			response.MakeHttpResponse(
				r, w, nil, errors.Wrapf(
					errorx.Err(errorx.ParseParamError, err.Error()), "failed to parse parameters, error: %+v", err,
				),
			)
			return
		}

		if err := svcCtx.Validator.Validate.StructCtx(r.Context(), req); err != nil {
			response.MakeHttpResponse(
				r, w, nil, errors.Wrapf(
					errorx.Err(errorx.ValidateParamError, svcCtx.Validator.Translate(err)),
					"failed to validate parameters, error: %+v", err,
				),
			)
			return
		}

		var (
			filename, filepath string
			file               *os.File
			stat               os.FileInfo
			size               int64
			err                error
		)
		defer func() {
			if file != nil {
				_ = file.Close()
			}
		}()

		defer func() {
			response.MakeHTTPDownloadResponse(r, w, filename, size, file, err)
		}()

		l := perfData.NewDownloadPerfDataLogic(r.Context(), svcCtx)
		filename, filepath, err = l.DownloadPerfData(&req)
		if err != nil {
			return
		}

		file, err = os.Open(filepath)
		if err != nil {
			err = errors.Wrapf(
				errorx.Err(errorx.FileOperationFailure, err.Error()),
				"failed to open file, filename: %s, error: %+v",
				filename, err,
			)
			return
		}

		stat, err = file.Stat()
		if err != nil {
			err = errors.Wrapf(
				errorx.Err(errorx.FileOperationFailure, err.Error()),
				"failed to get the file info, filename: %s, error: %+v",
				filename, err,
			)
			return
		}

		size = stat.Size()
	}
}
