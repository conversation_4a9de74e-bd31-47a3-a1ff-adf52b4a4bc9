package internal

import (
	"context"
	"database/sql"
	"fmt"
	"mime"
	"strings"
	"time"

	"github.com/hashicorp/go-multierror"
	"github.com/pkg/errors"
	"github.com/r3labs/diff/v3"
	"github.com/redis/go-redis/v9"
	"github.com/zeromicro/go-zero/core/jsonx"
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/mr"
	"github.com/zeromicro/go-zero/core/stores/sqlx"

	qetconstants "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/middlewares"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/redislock"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/version"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/codes"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/template"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/utils"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
)

const (
	lockKey     = "lock:registerFunctions"
	channelName = "chan:registerFunctions"

	timeout    = 5 * time.Second
	maxWorkers = 100
)

var (
	projectVersion = version.NewVersionInfo().Version

	SkipUrls = []string{
		"/manager/v1/git_config/sync/webhook",
	}
)

func RegisterFunctions(svcCtx *svc.ServiceContext) {
	ctx, cancel := context.WithTimeout(context.Background(), timeout) // ctx done?
	defer cancel()

	// acquire redis lock
	lock, err := redislock.NewRedisLockAndAcquire(svcCtx.Redis, lockKey, redislock.WithExpire(timeout))
	if err != nil {
		if re, ok := errorx.RootError(err); !ok || re.Code() != errorx.AcquireRedisLockFailure {
			logx.Errorf("failed to acquire the redis lock with key[%s], error: %+v", lockKey, err)
			return
		}

		logx.Info("another service is executing builtin function registration, wait for the execution result")
		ps := svcCtx.RedisNode.Subscribe(ctx, channelName)
		defer func(ps *redis.PubSub) {
			err := ps.Close()
			if err != nil {
				logx.Errorf("failed to close PubSub, error: %+v", err)
			}
		}(ps)

		select {
		case msg, ok := <-ps.Channel():
			if ok {
				logx.Infof("the result of registering builtin functions: %+v", msg)
			} else {
				logx.Errorf("the channel of PubSub has been closed, chan: %s", channelName)
			}
		case <-ctx.Done():
			logx.Errorf("waiting for the result of registering builtin functions timed out, error: %+v", ctx.Err())
		}
		return
	}

	defer func() {
		// release redis lock
		e := lock.Release()
		if e != nil {
			// l.Logger.Error(e)
			if re, ok := errorx.RootError(e); ok && re.Code() != errorx.ReleaseRedisLockFailure && err == nil {
				err = e
			}
		}
	}()

	st, err := registerFunctions(ctx, svcCtx)
	result := fmt.Sprintf("the result of registering builtin functions: %s", jsonx.MarshalToStringIgnoreError(st))
	if err != nil {
		result = fmt.Sprintf("%s, error: \n%+v", result, err)
		logx.Error(result)
	} else {
		logx.Info(result)
	}

	if val, err := svcCtx.RedisNode.Publish(ctx, channelName, result).Result(); err != nil {
		logx.Errorf(
			"failed to send the result of registering builtin functions to the channel[%s], error: %+v",
			channelName, err,
		)
	} else {
		logx.Infof(
			"succeeded to send the result of registering builtin functions to the channel[%s], number of subscriber: %d",
			channelName, val,
		)
	}
}

func registerFunctions(ctx context.Context, svcCtx *svc.ServiceContext) (*common.Statistic, error) {
	funcDocs := template.FuncDocs()
	funcModel := svcCtx.FunctionModel
	funcVer := generateFunctionVersion()

	st := common.NewStatistic(int64(len(funcDocs)))

	return st, funcModel.Trans(
		ctx, func(context context.Context, session sqlx.Session) error {
			var err error

			workers := len(funcDocs)
			if workers > maxWorkers {
				workers = maxWorkers
			}

			_ = mr.MapReduceVoid[template.Function, any](
				func(source chan<- template.Function) {
					for _, fd := range funcDocs {
						source <- fd
					}
				}, func(item template.Function, writer mr.Writer[any], cancel func(error)) {
					var v *model.Function

					f, e := funcModel.FindLatestOneNoCache(
						context, common.ConstBuiltinGlobalProjectId, item.Name, constants.BUILTIN,
					)
					if e != nil && e != model.ErrNotFound {
						st.Failure.Inc()
						err = multierror.Append(
							err, errorx.Err(
								errorx.DBError,
								fmt.Sprintf("failed to find the builtin function[%s], error: %+v", item.Name, e),
							),
						)
					} else if e == model.ErrNotFound {
						st.Increased.Inc()
						v = &model.Function{
							ProjectId: common.ConstBuiltinGlobalProjectId,
							Name:      item.Name,
							Type:      constants.BUILTIN,
							Category:  item.Category,
							Description: sql.NullString{
								String: item.Desc,
								Valid:  item.Desc != "",
							},
							Language:   constants.GOLANG,
							Parameters: jsonx.MarshalToStringIgnoreError(item.Parameters),
							Returns:    jsonx.MarshalToStringIgnoreError(item.Returns),
							Example: sql.NullString{
								String: item.Example,
								Valid:  item.Example != "",
							},
							Version:   funcVer,
							Latest:    int64(qetconstants.IsLatestVersion),
							CreatedBy: "1", // 暂时hard code
							UpdatedBy: "1", // 暂时hard code
						}
					} else {
						if change, e := functionHasChange(item, *f); e != nil {
							st.Failure.Inc()
							err = multierror.Append(err, e)
						} else if change {
							st.Modified.Inc()
							f.Latest = int64(qetconstants.IsNotLatestVersion)
							if _, e = funcModel.UpdateTX(context, session, f); e != nil {
								err = multierror.Append(
									err, errorx.Err(
										errorx.DBError,
										fmt.Sprintf("failed to update the builtin function[%s], error: %+v", f.Name, e),
									),
								)
							} else {
								v = &model.Function{
									ProjectId: f.ProjectId,
									Name:      f.Name,
									Type:      f.Type,
									Category:  item.Category,
									Description: sql.NullString{
										String: item.Desc,
										Valid:  item.Desc != "",
									},
									Language:   f.Language,
									Parameters: jsonx.MarshalToStringIgnoreError(item.Parameters),
									Returns:    jsonx.MarshalToStringIgnoreError(item.Returns),
									Example: sql.NullString{
										String: item.Example,
										Valid:  item.Example != "",
									},
									Version:   funcVer,
									Latest:    int64(qetconstants.IsLatestVersion),
									CreatedBy: f.CreatedBy,
									UpdatedBy: "1", // 暂时hard code
									CreatedAt: f.CreatedAt,
								}
							}
						} else {
							st.Unchanged.Inc()
						}
					}

					if v != nil {
						if _, e := funcModel.InsertTX(context, session, v); e != nil {
							err = multierror.Append(
								err, errorx.Err(
									errorx.DBError,
									fmt.Sprintf("failed to insert the builtin function[%s], error: %+v", v.Name, e),
								),
							)
						}
					}
				}, func(pipe <-chan any, cancel func(error)) {
				}, mr.WithContext(ctx), mr.WithWorkers(workers),
			)

			return err
		},
	)
}

func functionHasChange(fd template.Function, f model.Function) (bool, error) {
	type funcDiff struct {
		Desc       string
		Parameters []template.Parameter
		Returns    []template.Return
		Example    string
		Category   constants.FunctionCategory
	}

	newFunc := funcDiff{
		Desc:       fd.Desc,
		Parameters: fd.Parameters,
		Returns:    fd.Returns,
		Example:    fd.Example,
		Category:   fd.Category,
	}

	var parameters []template.Parameter
	if err := jsonx.UnmarshalFromString(f.Parameters, &parameters); err != nil {
		return false, errors.Wrapf(
			errorx.Err(errorx.SerializationError, err.Error()),
			"failed to unmarshal function parameters[%s], error: %+v", f.Parameters, err,
		)
	}

	var returns []template.Return
	if err := jsonx.UnmarshalFromString(f.Returns, &returns); err != nil {
		return false, errors.Wrapf(
			errorx.Err(errorx.SerializationError, err.Error()), "failed to unmarshal function returns[%s], error: %+v",
			f.Returns, err,
		)
	}

	oldFunc := funcDiff{
		Desc:       f.Description.String,
		Parameters: parameters,
		Returns:    returns,
		Example:    f.Example.String,
		Category:   f.Category,
	}

	differ, err := diff.NewDiffer()
	if err != nil {
		return false, errors.Wrapf(
			errorx.Err(errorx.NewObjectFailure, err.Error()), "failed to new a differ object, error: %+v", err,
		)
	}

	if cl, err := differ.Diff(oldFunc, newFunc); err != nil {
		return false, errors.Wrapf(
			errorx.Err(codes.StructDiffFailure, err.Error()), "failed to diff builtin function[%s], error: %+v", f.Name,
			err,
		)
	} else if len(cl) != 0 {
		return true, nil
	}

	return false, nil
}

func generateFunctionVersion() string {
	v := utils.GenVersion()

	if projectVersion == "" {
		// 没有使用`Makefile`构建服务的话，项目版本为空字符串
		return v
	}

	vs := strings.Split(v, ":")
	vs[len(vs)-1] = projectVersion
	return strings.Join(vs, ":")
}

func RegisterSkipUrls() {
	for _, url := range SkipUrls {
		middlewares.SkipForUrl(url)
	}
}

func RegisterExtensionType() {
	for _, item := range []struct{ ext, mime string }{
		{ext: string(common.ConstFileExtensionYaml), mime: common.ConstMediaTypeTextVNDYaml},
		{ext: string(common.ConstFileExtensionYml), mime: common.ConstMediaTypeTextVNDYaml},
		{ext: string(common.ConstFileExtensionCsv), mime: common.ConstMediaTypeTextCsv},
	} {
		if err := mime.AddExtensionType(item.ext, item.mime); err != nil {
			logx.Errorf(
				"failed to set the MIME type associated with the extension ext, ext: %s, mime: %s", item.ext, item.mime,
			)
		}
	}
}
