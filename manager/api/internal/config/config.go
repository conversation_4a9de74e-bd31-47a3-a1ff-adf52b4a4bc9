package config

import (
	"fmt"

	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/redis"
	"github.com/zeromicro/go-zero/rest"
	"github.com/zeromicro/go-zero/zrpc"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/types"
)

type Config struct {
	rest.RestConf

	DB        types.DBConfig
	Redis     redis.RedisConf
	Cache     cache.CacheConf
	Validator types.ValidatorConfig

	Manager    zrpc.RpcClientConf
	Permission zrpc.RpcClientConf
	User       zrpc.RpcClientConf

	GitLab        types.GitLabConfig
	LarkCustomApp types.LarkCustomApp
	PVCPath       string
	PVCNFSPath    string `json:",optional"`
}

func (c Config) ListenOn() string {
	return fmt.Sprintf("%s:%d", c.<PERSON>, c.<PERSON>)
}

func (c Config) LogConfig() logx.LogConf {
	return c.RestConf.ServiceConf.Log
}
