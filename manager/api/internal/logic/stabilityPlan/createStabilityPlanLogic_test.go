package stabilityPlan_test

import (
	"testing"

	"github.com/zeromicro/go-zero/core/jsonx"
	structpb "google.golang.org/protobuf/types/known/structpb"

	commonpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
	common "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"
)

func TestCreateStabilityPlan(t *testing.T) {
	req := &types.CreateStabilityPlanReq{
		ProjectId:       "project_id:Kqllt5-9fA-I5UOdhjA5d",
		CategoryId:      "category_id:-Y0S0cIqp22qfgoblZE1C",
		Name:            "稳定性测试计划名称",
		Description:     "稳定性测试计划描述",
		State:           1,
		Type:            "INTERFACE",
		PriorityType:    2,
		CronExpression:  "18 0 * * ?",
		Tags:            []string{"T1", "T2", "T3"},
		AccountConfigId: "account_config_id:5-c994WbKNQPldoylUjO2",
		DeviceType:      1,
		PlatformType:    2,
		Devices: &common.StabilityDevices{
			Udids: []string{"LZYTYLZT9HFI6DLN", "PXUYD22628002359"},
		},
		PackageName:     "com.yiyou.ga",
		AppDownloadLink: "https://d-internal.ttyuyin.com/d/app/GAClient/download?code=Ni40NS4wQDQ4OTQ",
		Duration:        300,
		Activities: []string{
			"com.yiyou.ga.client.BlankActivity",
			"com.yiyou.ga.lab.internal.kol.KolInfoActivity",
			"com.yiyou.ga.lab.internal.LabHandleProgressActivity",
		},
		CustomScript: &common.StabilityCustomScript{
			Image: "docker.io/yiyou/stability-test-script:v1.0.0",
		},
		LarkChats: []*common.LarkChat{
			{
				ChatId: "oc_a09f0e0e0e0e0e0e0e0e0e0e0e0e0e0e",
				Name:   "测试群组一",
			},
			{
				ChatId: "oc_a09f0e0e0e0e0e0e0e0e0e0e0e0e0e0o",
				Name:   "测试群组二",
			},
			{
				ChatId: "oc_a09f0e0e0e0e0e0e0e0e0e0e0e0e0e0c",
				Name:   "测试群组三",
			},
		},
		MaintainedBy: "T5210",
	}

	converters := []utils.TypeConverter{
		commonpb.StringToTriggerMode(),
		// commonpb.TriggerModeToString(),
		// commonpb.StringToDeviceType(),
		// commonpb.DeviceTypeToString(),
		// commonpb.StringToPlatformType(),
		// commonpb.PlatformTypeToString(),
		// usercommon.StringToUserInfo(ctx, svcCtx.UserRPC, nil),
	}

	in := &pb.CreateStabilityPlanReq{}
	if err := utils.Copy(in, req, converters...); err != nil {
		t.Fatalf(
			"failed to copy data to request, data: %s, error: %+v",
			jsonx.MarshalIgnoreError(req), err,
		)
	}

	var udids []*structpb.Value
	for _, udid := range req.Devices.Udids {
		udids = append(udids, &structpb.Value{
			Kind: &structpb.Value_StringValue{
				StringValue: udid,
			},
		})
	}
	in.Devices = &commonpb.StabilityCustomDevices{
		Devices: &commonpb.StabilityCustomDevices_Udids{
			Udids: &structpb.ListValue{
				Values: udids,
			},
		},
	}

	if req.CustomScript != nil {
		in.CustomScript = &commonpb.StabilityCustomScript{
			Script: &commonpb.StabilityCustomScript_Image{
				Image: req.CustomScript.Image,
			},
		}
	}
	t.Logf("%+v", in)
}
