package stabilityPlan

import (
	"context"

	"github.com/zeromicro/go-zero/core/logx"
	structpb "google.golang.org/protobuf/types/known/structpb"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/userinfo"
	qetutils "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"
	usercommon "gitlab.ttyuyin.com/TestDevelopment/qet-backend-middleware/user/common"

	commonpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
	commontypes "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/svc"
)

type BaseLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext

	currentUser *userinfo.UserInfo

	converters []qetutils.TypeConverter
}

func newBaseLogic(ctx context.Context, svcCtx *svc.ServiceContext) *BaseLogic {
	return &BaseLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,

		currentUser: userinfo.FromContext(ctx),

		converters: []qetutils.TypeConverter{
			commonpb.StringToTriggerMode(),
			commonpb.TriggerModeToString(),
			usercommon.StringToUserInfo(ctx, svcCtx.UserRPC.Client(), nil),
		},
	}
}

func convertCustomDevicesRpc(devices *commontypes.StabilityDevices) *commonpb.StabilityCustomDevices {
	if devices != nil {
		if len(devices.Udids) > 0 {
			var values []*structpb.Value
			for _, udid := range devices.Udids {
				values = append(values, &structpb.Value{
					Kind: &structpb.Value_StringValue{
						StringValue: udid,
					},
				})
			}
			return &commonpb.StabilityCustomDevices{
				Devices: &commonpb.StabilityCustomDevices_Udids{
					Udids: &structpb.ListValue{
						Values: values,
					},
				},
			}
		}
		if devices.Count > 0 {
			return &commonpb.StabilityCustomDevices{
				Devices: &commonpb.StabilityCustomDevices_Count{
					Count: devices.Count,
				},
			}
		}
	}
	return nil
}

func convertCustomDevicesApi(device *commonpb.StabilityCustomDevices) *commontypes.StabilityDevices {
	var devices commontypes.StabilityDevices
	if device != nil {
		switch device.Devices.(type) {
		case *commonpb.StabilityCustomDevices_Udids:
			for _, value := range device.GetUdids().GetValues() {
				devices.Udids = append(devices.Udids, value.GetStringValue())
			}
		case *commonpb.StabilityCustomDevices_Count:
			devices.Count = device.GetCount()
		}
	}
	return &devices
}

func convertCustomScriptRpc(script *commontypes.StabilityCustomScript) *commonpb.StabilityCustomScript {
	if script != nil {
		return &commonpb.StabilityCustomScript{
			Script: &commonpb.StabilityCustomScript_Image{
				Image: script.Image,
			},
		}
	}
	return nil
}

func convertCustomScriptApi(script *commonpb.StabilityCustomScript) *commontypes.StabilityCustomScript {
	var ac *commontypes.StabilityCustomScript
	if script != nil {
		switch script.Script.(type) {
		case *commonpb.StabilityCustomScript_GitConfig:
			// TODO
		case *commonpb.StabilityCustomScript_Image:
			ac = &commontypes.StabilityCustomScript{
				Image: script.GetImage(),
			}
		}
	}
	return ac
}
