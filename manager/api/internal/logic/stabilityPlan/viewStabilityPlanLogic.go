package stabilityPlan

import (
	"context"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type ViewStabilityPlanLogic struct {
	*BaseLogic
}

// view stability test plan
func NewViewStabilityPlanLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ViewStabilityPlanLogic {
	return &ViewStabilityPlanLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *ViewStabilityPlanLogic) ViewStabilityPlan(req *types.ViewStabilityPlanReq) (
	resp *types.ViewStabilityPlanResp, err error,
) {
	in := &pb.ViewStabilityPlanReq{}

	if err = utils.Copy(in, req, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy data to request, data: %s, error: %+v",
			jsonx.MarshalIgnoreError(req), err,
		)
	}

	out, err := l.svcCtx.ManagerStabilityPlanPRC.ViewStabilityPlan(l.ctx, in)
	if err != nil {
		return nil, err
	}

	resp = &types.ViewStabilityPlanResp{StabilityPlan: &types.StabilityPlan{}}
	if err = utils.Copy(resp.StabilityPlan, out.GetPlan(), l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy data to response, data: %s, error: %+v",
			protobuf.MarshalJSONIgnoreError(out.GetPlan()), err,
		)
	}
	resp.Devices = convertCustomDevicesApi(out.GetPlan().GetDevices())
	resp.CustomScript = convertCustomScriptApi(out.GetPlan().GetCustomScript())

	return resp, nil
}
