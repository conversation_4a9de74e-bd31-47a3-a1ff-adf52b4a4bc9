package stabilityPlan

import (
	"context"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type SearchStabilityPlanLogic struct {
	*BaseLogic
}

// search stability test plans
func NewSearchStabilityPlanLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SearchStabilityPlanLogic {
	return &SearchStabilityPlanLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *SearchStabilityPlanLogic) SearchStabilityPlan(req *types.SearchStabilityPlanReq) (
	resp *types.SearchStabilityPlanResp, err error,
) {
	in := &pb.SearchStabilityPlanReq{}

	if err = utils.Copy(in, req, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy data to request, data: %s, error: %+v",
			jsonx.MarshalIgnoreError(req), err,
		)
	}

	out, err := l.svcCtx.ManagerStabilityPlanPRC.SearchStabilityPlan(l.ctx, in)
	if err != nil {
		return nil, err
	}

	resp = &types.SearchStabilityPlanResp{Items: []*types.StabilityPlan{}}
	if err = utils.Copy(resp, out, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy data to response, data: %s, error: %+v",
			protobuf.MarshalJSONIgnoreError(out), err,
		)
	}

	for idx, item := range resp.Items {
		item.Devices = convertCustomDevicesApi(out.Items[idx].GetDevices())
		item.CustomScript = convertCustomScriptApi(out.Items[idx].GetCustomScript())
	}

	return resp, nil
}
