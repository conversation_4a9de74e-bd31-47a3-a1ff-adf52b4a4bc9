package category

import (
	"context"

	"github.com/pkg/errors"
	"google.golang.org/grpc"
	"google.golang.org/grpc/encoding/gzip"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type SearchCategoryLogic struct {
	*BaseLogic
}

func NewSearchCategoryLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SearchCategoryLogic {
	return &SearchCategoryLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *SearchCategoryLogic) SearchCategory(req *types.SearchCategoryReq) (resp *types.SearchCategoryResp, err error) {
	in := &pb.SearchCategoryReq{}
	if err = utils.Copy(in, req, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy data[%+v] to request, error: %+v",
			req, err,
		)
	}

	// enable Gzip compression
	out, err := l.svcCtx.ManagerCategoryRPC.SearchCategory(l.ctx, in, grpc.UseCompressor(gzip.Name))
	if err != nil {
		return nil, err
	}

	// avoid returning null to the front end
	resp = &types.SearchCategoryResp{Items: []*types.Category{}}
	if err = utils.Copy(resp, out, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy data[%+v] to response, error: %+v",
			out, err,
		)
	}

	return resp, nil
}
