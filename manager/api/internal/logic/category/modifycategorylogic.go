package category

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type ModifyCategoryLogic struct {
	*BaseLogic
}

func NewModifyCategoryLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ModifyCategoryLogic {
	return &ModifyCategoryLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *ModifyCategoryLogic) ModifyCategory(req *types.ModifyCategoryReq) (resp *types.ModifyCategoryResp, err error) {
	in := &pb.ModifyCategoryReq{}
	if err = utils.Copy(in, req, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy data[%+v] to request, error: %+v",
			req, err,
		)
	}

	_, err = l.svcCtx.ManagerCategoryRPC.ModifyCategory(l.ctx, in)
	if err != nil {
		return nil, err
	}

	return &types.ModifyCategoryResp{}, nil
}

//func (l *ModifyCategoryLogic) ModifyCategoryForInternal(
//	ctx context.Context, session sqlx.Session, req *types.ModifyCategoryReq,
//) (err error) {
//	// validate the category_id in req
//	origin, err := model.CheckCategoryByCategoryId(
//		l.ctx, l.svcCtx.CategoryModel, req.ProjectId, req.Type, req.CategoryId,
//	)
//	if err != nil {
//		return err
//	} else if origin.Builtin != 0 {
//		return errors.WithStack(
//			errorx.Err(
//				errorx.ProhibitedBehavior, fmt.Sprintf("cannot modify the builtin category[%s]", origin.Name),
//			),
//		)
//	}
//
//	if origin.Name == req.Name && (origin.Description.String == req.Description) {
//		// nothing change, no need to update
//		return nil
//	}
//
//	// validate the name in req
//	if err = common.CheckCategoryNameByType(req.Name, req.Type); err != nil {
//		return err
//	}
//
//	// acquire redis lock
//	key := fmt.Sprintf(
//		"%s:%s:%s:%s", common.ConstLockCategoryProjectIdTypeCategoryIdPrefix, req.ProjectId, req.Type, req.CategoryId,
//	)
//	lock, err := redislock.NewRedisLockAndAcquire(l.svcCtx.Redis, key, redislock.WithExpire(common.ConstLockExpireTime))
//	if err != nil {
//		return err
//	}
//	defer func() {
//		// release redis lock
//		e := lock.Release()
//		if e != nil {
//			l.Logger.Error(e)
//			if re, ok := errorx.RootError(e); ok && re.Code() != errorx.ReleaseRedisLockFailure && err == nil {
//				err = e
//			}
//		}
//	}()
//
//	fn := func(context context.Context, session sqlx.Session) error {
//		origin.Name = req.Name
//		origin.Description = sql.NullString{
//			String: req.Description,
//			Valid:  req.Description != "",
//		}
//		origin.UpdatedBy = "1" // 暂时hard code
//		origin.UpdatedAt = time.Now()
//
//		if _, err = l.svcCtx.CategoryModel.Update(context, session, origin); err != nil {
//			return errors.Wrapf(
//				errorx.Err(errorx.DBError, err.Error()), "failed to update table[%s] with values[%+v], error: %+v",
//				l.svcCtx.CategoryModel.Table(), origin, err,
//			)
//		}
//
//		return nil
//	}
//
//	if session != nil {
//		if ctx == nil {
//			ctx = l.ctx
//		}
//		return fn(ctx, session)
//	} else {
//		return l.svcCtx.CategoryModel.Trans(l.ctx, fn)
//	}
//}
