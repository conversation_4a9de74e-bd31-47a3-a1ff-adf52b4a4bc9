package perfLarkChat

import (
	"context"

	"github.com/zeromicro/go-zero/core/logx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/userinfo"
	qetutils "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"
	usercommon "gitlab.ttyuyin.com/TestDevelopment/qet-backend-middleware/user/common"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/svc"
)

type BaseLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext

	currentUser *userinfo.UserInfo

	converters []qetutils.TypeConverter
}

func newBaseLogic(ctx context.Context, svcCtx *svc.ServiceContext) *BaseLogic {
	return &BaseLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,

		currentUser: userinfo.FromContext(ctx),

		converters: []qetutils.TypeConverter{
			usercommon.StringToUserInfo(ctx, svcCtx.UserRPC.Client(), nil),
		},
	}
}
