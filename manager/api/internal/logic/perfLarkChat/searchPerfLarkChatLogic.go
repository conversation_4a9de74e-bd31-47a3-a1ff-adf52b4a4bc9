package perfLarkChat

import (
	"context"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type SearchPerfLarkChatLogic struct {
	*BaseLogic
}

// Deprecated: use `larkChat.SearchLarkChat` instead.
func NewSearchPerfLarkChatLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SearchPerfLarkChatLogic {
	return &SearchPerfLarkChatLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *SearchPerfLarkChatLogic) SearchPerfLarkChat(req *types.SearchPerfLarkChatReq) (
	resp *types.SearchPerfLarkChatResp, err error,
) {
	in := &pb.SearchPerfLarkChatReq{}
	if err = utils.Copy(in, req, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy data to request, data: %s, error: %+v",
			jsonx.MarshalIgnoreError(req), err,
		)
	}

	out, err := l.svcCtx.ManagerPerfLarkChatRPC.SearchPerfLarkChat(l.ctx, in)
	if err != nil {
		return nil, err
	}

	// avoid returning null to the front end
	resp = &types.SearchPerfLarkChatResp{Items: []*types.PerfLarkChat{}}
	if err = utils.Copy(resp, out, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy data to response, data: %s, error: %+v",
			protobuf.MarshalJSONIgnoreError(out), err,
		)
	}

	return resp, nil
}
