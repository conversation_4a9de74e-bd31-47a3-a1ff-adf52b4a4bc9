package protobufConfig

import (
	"context"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type SearchProtobufConfigLogic struct {
	*BaseLogic
}

func NewSearchProtobufConfigLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SearchProtobufConfigLogic {
	return &SearchProtobufConfigLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *SearchProtobufConfigLogic) SearchProtobufConfig(req *types.SearchProtobufConfigReq) (
	resp *types.SearchProtobufConfigResp, err error,
) {
	in := &pb.SearchProtobufConfigurationReq{}
	if err = utils.Copy(in, req, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy data to request, data: %s, error: %+v",
			jsonx.MarshalIgnoreError(req), err,
		)
	}

	out, err := l.svcCtx.ManagerProtobufConfigurationRPC.SearchProtobufConfiguration(l.ctx, in)
	if err != nil {
		return nil, err
	}

	// avoid returning null to the front end
	resp = &types.SearchProtobufConfigResp{Items: []*types.ProtobufConfiguration{}}
	if err = utils.Copy(resp, out, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy data to response, data: %s, error: %+v",
			protobuf.MarshalJSONIgnoreError(out), err,
		)
	}

	for _, item := range resp.Items {
		if item != nil {
			// no need to return dependencies while searching protobuf configurations
			item.Dependencies = nil
		}
	}

	return resp, nil
}
