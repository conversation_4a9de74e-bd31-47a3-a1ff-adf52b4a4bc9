package protobufConfig

import (
	"context"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type CreateProtobufConfigLogic struct {
	*BaseLogic
}

func NewCreateProtobufConfigLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CreateProtobufConfigLogic {
	return &CreateProtobufConfigLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *CreateProtobufConfigLogic) CreateProtobufConfig(req *types.CreateProtobufConfigReq) (
	resp *types.CreateProtobufConfigResp, err error,
) {
	in := &pb.CreateProtobufConfigurationReq{}

	if err = utils.Copy(in, req, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy data to request, data: %s, error: %+v",
			jsonx.MarshalIgnoreError(req), err,
		)
	}

	out, err := l.svcCtx.ManagerProtobufConfigurationRPC.CreateProtobufConfiguration(l.ctx, in)
	if err != nil {
		return nil, err
	}

	return &types.CreateProtobufConfigResp{ConfigId: out.GetConfiguration().GetConfigId()}, nil
}
