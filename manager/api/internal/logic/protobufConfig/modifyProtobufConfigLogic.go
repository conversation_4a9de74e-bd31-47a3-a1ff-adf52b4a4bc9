package protobufConfig

import (
	"context"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type ModifyProtobufConfigLogic struct {
	*BaseLogic
}

func NewModifyProtobufConfigLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ModifyProtobufConfigLogic {
	return &ModifyProtobufConfigLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *ModifyProtobufConfigLogic) ModifyProtobufConfig(req *types.ModifyProtobufConfigReq) (
	resp *types.ModifyProtobufConfigResp, err error,
) {
	in := &pb.ModifyProtobufConfigurationReq{}
	if err = utils.Copy(in, req, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy data to request, data: %s, error: %+v",
			jsonx.MarshalIgnoreError(req), err,
		)
	}

	_, err = l.svcCtx.ManagerProtobufConfigurationRPC.ModifyProtobufConfiguration(l.ctx, in)
	if err != nil {
		return nil, err
	}

	return &types.ModifyProtobufConfigResp{}, nil
}
