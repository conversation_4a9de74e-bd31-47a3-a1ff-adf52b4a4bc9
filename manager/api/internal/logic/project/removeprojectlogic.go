package project

import (
	"context"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/types"
)

type RemoveProjectLogic struct {
	*BaseLogic
}

func NewRemoveProjectLogic(ctx context.Context, svcCtx *svc.ServiceContext) *RemoveProjectLogic {
	return &RemoveProjectLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *RemoveProjectLogic) RemoveProject(req *types.RemoveProjectReq) (resp *types.RemoveProjectResp, err error) {
	// todo: add your logic here and delete this line

	return
}
