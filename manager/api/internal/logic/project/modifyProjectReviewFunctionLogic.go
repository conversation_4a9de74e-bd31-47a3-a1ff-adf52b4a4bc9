package project

import (
	"context"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type ModifyProjectReviewFunctionLogic struct {
	*BaseLogic
}

func NewModifyProjectReviewFunctionLogic(
	ctx context.Context, svcCtx *svc.ServiceContext,
) *ModifyProjectReviewFunctionLogic {
	return &ModifyProjectReviewFunctionLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *ModifyProjectReviewFunctionLogic) ModifyProjectReviewFunction(req *types.ModifyProjectReviewFunctionReq) (
	resp *types.ModifyProjectReviewFunctionResp, err error,
) {
	in := &pb.ModifyProjectReviewFunctionReq{}
	if err = utils.Copy(in, req, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy data to request, data: %s, error: %+v",
			jsonx.MarshalIgnoreError(req), err,
		)
	}

	_, err = l.svcCtx.ManagerProjectRPC.ModifyProjectReviewFunction(l.ctx, in)
	if err != nil {
		return nil, err
	}

	return &types.ModifyProjectReviewFunctionResp{}, nil
}
