package project

import (
	"context"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type ModifyProjectLogic struct {
	*BaseLogic
}

func NewModifyProjectLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ModifyProjectLogic {
	return &ModifyProjectLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *ModifyProjectLogic) ModifyProject(req *types.ModifyProjectReq) (resp *types.ModifyProjectResp, err error) {
	in := &pb.ModifyProjectReq{}
	if err = utils.Copy(in, req, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy data to request, data: %s, error: %+v",
			jsonx.MarshalIgnoreError(req), err,
		)
	}

	_, err = l.svcCtx.ManagerProjectRPC.ModifyProject(l.ctx, in)
	if err != nil {
		return nil, err
	}

	return &types.ModifyProjectResp{}, nil
}
