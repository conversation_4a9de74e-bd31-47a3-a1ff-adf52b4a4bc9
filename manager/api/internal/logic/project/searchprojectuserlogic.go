package project

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/userinfo"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type SearchProjectUserLogic struct {
	*BaseLogic
}

func NewSearchProjectUserLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SearchProjectUserLogic {
	return &SearchProjectUserLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *SearchProjectUserLogic) SearchProjectUser(req *types.SearchProjectUserReq) (resp *types.SearchProjectUserResp, err error) {
	in := &pb.SearchProjectUserReq{}
	if err = utils.Copy(in, req); err != nil {
		return nil, errors.Wrapf(errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy data[%+v] to request, error: %+v", req, err)
	}

	out, err := l.svcCtx.ManagerProjectRPC.SearchProjectUser(l.ctx, in)
	if err != nil {
		return nil, err
	}

	// avoid returning null to the front end
	resp = &types.SearchProjectUserResp{Items: []*userinfo.FullUserInfo{}}
	if err = utils.Copy(resp, out, l.converters...); err != nil {
		return nil, errors.Wrapf(errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy data[%+v] to response, error: %+v", out, err)
	}

	return resp, nil
}
