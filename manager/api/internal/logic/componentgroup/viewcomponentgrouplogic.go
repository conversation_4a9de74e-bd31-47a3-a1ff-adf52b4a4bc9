package componentgroup

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type ViewComponentGroupLogic struct {
	*BaseLogic
}

func NewViewComponentGroupLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ViewComponentGroupLogic {
	return &ViewComponentGroupLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *ViewComponentGroupLogic) ViewComponentGroup(req *types.ViewComponentGroupReq) (
	resp *types.ViewComponentGroupResp, err error,
) {
	in := &pb.ViewComponentGroupReq{}
	if err = utils.Copy(in, req, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy data[%+v] to request, error: %+v",
			req, err,
		)
	}

	out, err := l.svcCtx.ManagerComponentGroupRPC.ViewComponentGroup(l.ctx, in)
	if err != nil {
		return nil, err
	}

	resp = &types.ViewComponentGroupResp{
		ComponentGroup: &types.ComponentGroup{
			Tags:   []string{},
			Nodes:  []*types.Node{},
			Edges:  []*types.Edge{},
			Combos: []*types.Combo{},
		},
		ReferencedRelations: []*types.Referenced{},
	}
	if err = utils.Copy(resp.ComponentGroup, out.ComponentGroup, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy data[%+v] to response, error: %+v",
			out.ComponentGroup, err,
		)
	}

	if err = utils.Copy(&resp.ReferencedRelations, out.ReferencedRelations, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy data[%+v] to response, error: %+v",
			out.ReferencedRelations, err,
		)
	}

	return resp, nil
}
