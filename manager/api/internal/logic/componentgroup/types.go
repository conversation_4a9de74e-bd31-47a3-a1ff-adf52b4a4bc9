package componentgroup

import (
	ct "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/types"
)

type createReferenceRelationshipInternalReq struct {
	svcCtx *svc.ServiceContext

	ProjectId        string         `json:"project_id"`
	ComponentGroupId string         `json:"component_group_id"`
	Version          string         `json:"version"`
	Relations        []*ct.Relation `json:"relations"`
}

type createElementAndComponentInternalReq struct {
	svcCtx *svc.ServiceContext

	ProjectId        string         `json:"project_id"`
	ComponentGroupId string         `json:"component_group_id"`
	Version          string         `json:"version"`
	Nodes            []*types.Node  `json:"nodes"`
	Edges            []*types.Edge  `json:"edges"`
	Combos           []*types.Combo `json:"combos"`
}

type searchComponentGroupInternalReq struct {
	svcCtx *svc.ServiceContext

	types.SearchComponentGroupReq

	DrillDown bool `json:"drill_down"`
}
