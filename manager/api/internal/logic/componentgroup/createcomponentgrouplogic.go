package componentgroup

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type CreateComponentGroupLogic struct {
	*BaseLogic
}

func NewCreateComponentGroupLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CreateComponentGroupLogic {
	return &CreateComponentGroupLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *CreateComponentGroupLogic) CreateComponentGroup(req *types.CreateComponentGroupReq) (
	resp *types.CreateComponentGroupResp, err error,
) {
	in := &pb.CreateComponentGroupReq{}
	if err = utils.Copy(in, req, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy data[%+v] to request, error: %+v", req,
			err,
		)
	}

	out, err := l.svcCtx.ManagerComponentGroupRPC.CreateComponentGroup(l.ctx, in)
	if err != nil {
		return nil, err
	}

	return &types.CreateComponentGroupResp{ComponentGroupId: out.GetComponentGroup().GetComponentGroupId()}, nil
}
