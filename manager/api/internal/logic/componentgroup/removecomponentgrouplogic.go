package componentgroup

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type RemoveComponentGroupLogic struct {
	*BaseLogic
}

func NewRemoveComponentGroupLogic(ctx context.Context, svcCtx *svc.ServiceContext) *RemoveComponentGroupLogic {
	return &RemoveComponentGroupLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *RemoveComponentGroupLogic) RemoveComponentGroup(req *types.RemoveComponentGroupReq) (
	resp *types.RemoveComponentGroupResp, err error,
) {
	in := &pb.RemoveComponentGroupReq{}
	if err = utils.Copy(in, req, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy data[%+v] to request, error: %+v",
			req, err,
		)
	}

	_, err = l.svcCtx.ManagerComponentGroupRPC.RemoveComponentGroup(l.ctx, in)
	if err != nil {
		return nil, err
	}

	return &types.RemoveComponentGroupResp{}, nil
}
