package apiservice

import (
	"context"
	"strings"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	usercommon "gitlab.ttyuyin.com/TestDevelopment/qet-backend-middleware/user/common"

	commonutils "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/utils"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"

	"github.com/zeromicro/go-zero/core/logx"
)

type ViewApiServiceCaseLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext

	converters []commonutils.TypeConverter
}

func NewViewApiServiceCaseLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ViewApiServiceCaseLogic {
	return &ViewApiServiceCaseLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,

		converters: []commonutils.TypeConverter{
			logic.StringToResourceState(),
			logic.ResourceStateToString(),
			logic.ApiNodesToRpcNodes(),
			logic.ApiEdgesToRpcEdges(),
			logic.ApiCombosToRpcCombos(),
			logic.ApiRelationsToRpcRelations(),
			logic.RpcNodesToApiNodes(),
			logic.RpcEdgesToApiEdges(),
			logic.RpcCombosToApiCombos(),
			logic.RpcRelationsToApiRelations(),
			usercommon.StringToUserInfo(ctx, svcCtx.UserRPC.Client(), nil),
		},
	}
}

func (l *ViewApiServiceCaseLogic) ViewApiServiceCase(req *types.ViewApiServiceCaseReq) (
	resp *types.ViewApiServiceCaseResp, err error,
) {
	resp = &types.ViewApiServiceCaseResp{}
	if strings.HasPrefix(req.CaseId, utils.ConstCaseIdPrefix) {
		in := &pb.ViewApiCaseReq{
			ProjectId: req.ProjectId,
			CaseId:    req.CaseId,
			Version:   req.Version,
		}

		out, err := l.svcCtx.ManagerApiCaseRPC.ViewApiCase(l.ctx, in)
		if err != nil {
			return nil, err
		}

		resp = &types.ViewApiServiceCaseResp{
			ApiServiceCase: &types.ApiServiceCase{
				Tags: []string{}, Nodes: []*types.Node{}, Edges: []*types.Edge{}, Combos: []*types.Combo{},
			},
		}
		if err = commonutils.Copy(resp.ApiServiceCase, out.Case, l.converters...); err != nil {
			return nil, errors.Wrapf(
				errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy data[%+v] to response, error: %+v",
				out, err,
			)
		}
	} else if strings.HasPrefix(req.CaseId, utils.ConstInterfaceCaseIdPrefix) {
		in := &pb.ViewInterfaceCaseReq{
			ProjectId: req.ProjectId,
			CaseId:    req.CaseId,
			Version:   req.Version,
		}

		out, err := l.svcCtx.ManagerInterfaceDefinitionRPC.ViewInterfaceCase(l.ctx, in)
		if err != nil {
			return nil, err
		}

		resp = &types.ViewApiServiceCaseResp{
			ApiServiceCase: &types.ApiServiceCase{
				Tags: []string{}, Nodes: []*types.Node{}, Edges: []*types.Edge{}, Combos: []*types.Combo{},
			},
		}
		if err = commonutils.Copy(resp.ApiServiceCase, out.Case, l.converters...); err != nil {
			return nil, errors.Wrapf(
				errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy data[%+v] to response, error: %+v",
				out, err,
			)
		}
	}

	return resp, nil
}
