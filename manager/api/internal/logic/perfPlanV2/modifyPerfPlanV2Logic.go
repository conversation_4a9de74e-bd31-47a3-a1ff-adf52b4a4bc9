package perfPlanV2

import (
	"context"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type ModifyPerfPlanV2Logic struct {
	*BaseLogic
}

func NewModifyPerfPlanV2Logic(ctx context.Context, svcCtx *svc.ServiceContext) *ModifyPerfPlanV2Logic {
	return &ModifyPerfPlanV2Logic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *ModifyPerfPlanV2Logic) ModifyPerfPlanV2(req *types.ModifyPerfPlanV2Req) (
	resp *types.ModifyPerfPlanV2Resp, err error,
) {
	in := &pb.ModifyPerfPlanV2Req{}
	if err = utils.Copy(in, req, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy data to request, data: %s, error: %+v",
			jsonx.MarshalIgnoreError(req), err,
		)
	}

	_, err = l.svcCtx.ManagerPerfPlanV2RPC.ModifyPerfPlanV2(l.ctx, in)
	if err != nil {
		return nil, err
	}

	return &types.ModifyPerfPlanV2Resp{}, nil
}
