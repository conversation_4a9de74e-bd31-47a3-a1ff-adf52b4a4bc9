package perfPlanV2

import (
	"context"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type SearchRuleInPerfPlanV2Logic struct {
	*BaseLogic
}

func NewSearchRuleInPerfPlanV2Logic(ctx context.Context, svcCtx *svc.ServiceContext) *SearchRuleInPerfPlanV2Logic {
	return &SearchRuleInPerfPlanV2Logic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *SearchRuleInPerfPlanV2Logic) SearchRuleInPerfPlanV2(req *types.SearchRuleInPerfPlanV2Req) (
	resp *types.SearchRuleInPerfPlanV2Resp, err error,
) {
	in := &pb.SearchRuleInPerfPlanV2Req{}
	if err = utils.Copy(in, req, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy data to request, data: %s, error: %+v",
			jsonx.MarshalIgnoreError(req), err,
		)
	}

	out, err := l.svcCtx.ManagerPerfPlanV2RPC.SearchRuleInPerfPlanV2(l.ctx, in)
	if err != nil {
		return nil, err
	}

	// avoid returning null to the front end
	resp = &types.SearchRuleInPerfPlanV2Resp{Items: []*types.SearchRuleInPerfPlanV2Item{}}
	if err = utils.Copy(resp, out, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy data to response, data: %s, error: %+v",
			protobuf.MarshalJSONIgnoreError(out), err,
		)
	}

	return resp, nil
}
