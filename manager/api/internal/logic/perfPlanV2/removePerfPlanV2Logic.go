package perfPlanV2

import (
	"context"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type RemovePerfPlanV2Logic struct {
	*BaseLogic
}

func NewRemovePerfPlanV2Logic(ctx context.Context, svcCtx *svc.ServiceContext) *RemovePerfPlanV2Logic {
	return &RemovePerfPlanV2Logic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *RemovePerfPlanV2Logic) RemovePerfPlanV2(req *types.RemovePerfPlanV2Req) (
	resp *types.RemovePerfPlanV2Resp, err error,
) {
	in := &pb.RemovePerfPlanV2Req{}
	if err = utils.Copy(in, req, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy data to request, data: %s, error: %+v",
			jsonx.MarshalIgnoreError(req), err,
		)
	}

	_, err = l.svcCtx.ManagerPerfPlanV2RPC.RemovePerfPlanV2(l.ctx, in)
	if err != nil {
		return nil, err
	}

	return &types.RemovePerfPlanV2Resp{}, nil
}
