package perfPlanV2

import (
	"context"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	commonTypes "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type ViewPerfPlanV2Logic struct {
	*BaseLogic
}

func NewViewPerfPlanV2Logic(ctx context.Context, svcCtx *svc.ServiceContext) *ViewPerfPlanV2Logic {
	return &ViewPerfPlanV2Logic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *ViewPerfPlanV2Logic) ViewPerfPlanV2(req *types.ViewPerfPlanV2Req) (resp *types.ViewPerfPlanV2Resp, err error) {
	in := &pb.ViewPerfPlanV2Req{}
	if err = utils.Copy(in, req, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy data to request, data: %s, error: %+v",
			jsonx.MarshalIgnoreError(req), err,
		)
	}

	out, err := l.svcCtx.ManagerPerfPlanV2RPC.ViewPerfPlanV2(l.ctx, in)
	if err != nil {
		return nil, err
	}

	resp = &types.ViewPerfPlanV2Resp{
		PerfPlanV2: &types.PerfPlanV2{
			AuthRateLimits: make([]*commonTypes.RateLimitV2, 0),
		},
	}
	if err = utils.Copy(resp.PerfPlanV2, out.GetPlan(), l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy data to response, data: %s, error: %+v",
			protobuf.MarshalJSONIgnoreError(out), err,
		)
	}

	return resp, nil
}
