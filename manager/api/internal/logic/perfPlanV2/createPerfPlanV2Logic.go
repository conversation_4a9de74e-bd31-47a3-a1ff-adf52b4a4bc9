package perfPlanV2

import (
	"context"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type CreatePerfPlanV2Logic struct {
	*BaseLogic
}

func NewCreatePerfPlanV2Logic(ctx context.Context, svcCtx *svc.ServiceContext) *CreatePerfPlanV2Logic {
	return &CreatePerfPlanV2Logic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *CreatePerfPlanV2Logic) CreatePerfPlanV2(req *types.CreatePerfPlanV2Req) (
	resp *types.CreatePerfPlanV2Resp, err error,
) {
	in := &pb.CreatePerfPlanV2Req{}

	if err = utils.Copy(in, req, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy data to request, data: %s, error: %+v",
			jsonx.MarshalIgnoreError(req), err,
		)
	}

	out, err := l.svcCtx.ManagerPerfPlanV2RPC.CreatePerfPlanV2(l.ctx, in)
	if err != nil {
		return nil, err
	}

	return &types.CreatePerfPlanV2Resp{PlanId: out.GetPlan().GetPlanId()}, nil
}
