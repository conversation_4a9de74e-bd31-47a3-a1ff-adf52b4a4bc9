package interfaceDefinition

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type CreateInterfaceDocumentLogic struct {
	*BaseLogic
}

func NewCreateInterfaceDocumentLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CreateInterfaceDocumentLogic {
	return &CreateInterfaceDocumentLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *CreateInterfaceDocumentLogic) CreateInterfaceDocument(req *types.CreateInterfaceDocumentReq) (
	resp *types.CreateInterfaceDocumentResp, err error,
) {
	in := &pb.CreateInterfaceDocumentReq{}
	if err = utils.Copy(in, req, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy data[%+v] to request, error: %+v", req,
			err,
		)
	}

	out, err := l.svcCtx.ManagerInterfaceDefinitionRPC.CreateInterfaceDocument(l.ctx, in)
	if err != nil {
		return nil, err
	}

	return &types.CreateInterfaceDocumentResp{DocumentId: out.GetDocument().GetDocumentId()}, nil
}
