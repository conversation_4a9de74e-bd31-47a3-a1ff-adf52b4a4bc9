package interfaceDefinition

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type ViewInterfaceSchemaLogic struct {
	*BaseLogic
}

func NewViewInterfaceSchemaLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ViewInterfaceSchemaLogic {
	return &ViewInterfaceSchemaLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *ViewInterfaceSchemaLogic) ViewInterfaceSchema(req *types.ViewInterfaceSchemaReq) (
	resp *types.ViewInterfaceSchemaResp, err error,
) {
	in := &pb.ViewInterfaceSchemaReq{}
	if err = utils.Copy(in, req, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy data[%+v] to request, error: %+v",
			req, err,
		)
	}

	out, err := l.svcCtx.ManagerInterfaceDefinitionRPC.ViewInterfaceSchema(l.ctx, in)
	if err != nil {
		return nil, err
	}

	resp = &types.ViewInterfaceSchemaResp{InterfaceSchema: &types.InterfaceSchema{}}
	if err = utils.Copy(resp.InterfaceSchema, out.Schema, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy data[%+v] to response, error: %+v",
			out, err,
		)
	}

	return resp, nil
}
