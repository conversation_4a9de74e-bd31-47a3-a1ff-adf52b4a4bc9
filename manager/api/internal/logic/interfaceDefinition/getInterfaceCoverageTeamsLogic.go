package interfaceDefinition

import (
	"context"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type GetInterfaceCoverageTeamsLogic struct {
	*BaseLogic
}

func NewGetInterfaceCoverageTeamsLogic(
	ctx context.Context, svcCtx *svc.ServiceContext,
) *GetInterfaceCoverageTeamsLogic {
	return &GetInterfaceCoverageTeamsLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *GetInterfaceCoverageTeamsLogic) GetInterfaceCoverageTeams(req *types.GetInterfaceCoverageTeamsReq) (
	resp *types.GetInterfaceCoverageTeamsResp, err error,
) {
	out, err := l.svcCtx.ManagerInterfaceDefinitionRPC.GetInterfaceCoverageTeams(
		l.ctx, &pb.GetInterfaceCoverageTeamsReq{
			ProjectId: req.ProjectId,
		},
	)
	if err != nil {
		return nil, err
	}

	return &types.GetInterfaceCoverageTeamsResp{
		Teams: out.GetTeams(),
	}, nil
}
