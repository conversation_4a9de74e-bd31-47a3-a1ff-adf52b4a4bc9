package interfaceDefinition

import (
	"context"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/zrpc"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type LocalImportInterfaceDefinitionLogic struct {
	*BaseLogic
}

func NewLocalImportInterfaceDefinitionLogic(
	ctx context.Context, svcCtx *svc.ServiceContext,
) *LocalImportInterfaceDefinitionLogic {
	return &LocalImportInterfaceDefinitionLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *LocalImportInterfaceDefinitionLogic) LocalImportInterfaceDefinition(req *types.LocalImportInterfaceDefinitionReq) (
	resp *types.LocalImportInterfaceDefinitionResp, err error,
) {
	in := &pb.LocalImportInterfaceDefinitionReq{}
	if err = utils.Copy(in, req, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy data[%+v] to request, error: %+v",
			req, err,
		)
	}

	out, err := l.svcCtx.ManagerInterfaceDefinitionRPC.LocalImportInterfaceDefinition(
		l.ctx, in, zrpc.WithCallTimeout(common.ConstImportInterfaceDefinitionTimeout),
	)
	if err != nil {
		return nil, err
	}

	return &types.LocalImportInterfaceDefinitionResp{
		Document: out.Document.ConvertTo(),
		Schema:   out.Schema.ConvertTo(),
	}, nil
}
