package interfaceDefinition

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type CreateInterfaceConfigLogic struct {
	*BaseLogic
}

func NewCreateInterfaceConfigLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CreateInterfaceConfigLogic {
	return &CreateInterfaceConfigLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *CreateInterfaceConfigLogic) CreateInterfaceConfig(req *types.CreateInterfaceConfigReq) (
	resp *types.CreateInterfaceConfigResp, err error,
) {
	in := &pb.CreateInterfaceConfigReq{}
	if err = utils.Copy(in, req, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy data[%+v] to request, error: %+v", req,
			err,
		)
	}

	out, err := l.svcCtx.ManagerInterfaceDefinitionRPC.CreateInterfaceConfig(l.ctx, in)
	if err != nil {
		return nil, err
	}

	return &types.CreateInterfaceConfigResp{ConfigId: out.GetConfig().GetConfigId()}, nil
}
