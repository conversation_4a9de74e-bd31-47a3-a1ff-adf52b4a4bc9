package interfaceDefinition

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type CreateInterfaceCaseLogic struct {
	*BaseLogic
}

func NewCreateInterfaceCaseLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CreateInterfaceCaseLogic {
	return &CreateInterfaceCaseLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *CreateInterfaceCaseLogic) CreateInterfaceCase(req *types.CreateInterfaceCaseReq) (
	resp *types.CreateInterfaceCaseResp, err error,
) {
	in := &pb.CreateInterfaceCaseReq{}
	if err = utils.Copy(in, req, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy data[%+v] to request, error: %+v", req,
			err,
		)
	}

	out, err := l.svcCtx.ManagerInterfaceDefinitionRPC.CreateInterfaceCase(l.ctx, in)
	if err != nil {
		return nil, err
	}

	return &types.CreateInterfaceCaseResp{CaseId: out.GetCase().GetCaseId()}, nil
}
