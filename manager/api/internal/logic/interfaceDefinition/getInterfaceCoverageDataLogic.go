package interfaceDefinition

import (
	"context"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type GetInterfaceCoverageDataLogic struct {
	*BaseLogic
}

func NewGetInterfaceCoverageDataLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetInterfaceCoverageDataLogic {
	return &GetInterfaceCoverageDataLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *GetInterfaceCoverageDataLogic) GetInterfaceCoverageData(req *types.GetInterfaceCoverageDataReq) (
	resp *types.GetInterfaceCoverageDataResp, err error,
) {
	out, err := l.svcCtx.ManagerInterfaceDefinitionRPC.GetInterfaceCoverageData(
		l.ctx, &pb.GetInterfaceCoverageDataReq{
			ProjectId: req.ProjectId,
			Team:      req.Team,
			From:      req.From,
			To:        req.To,
		},
	)
	if err != nil {
		return nil, err
	}

	return &types.GetInterfaceCoverageDataResp{
		InterfaceCoverageData: &types.InterfaceCoverageData{
			NumberOfApis:  out.GetData().GetNumberOfApis(),
			NumberOfCases: out.GetData().GetNumberOfCases(),
			CountedAt:     out.GetData().GetCountedAt(),
		},
	}, nil
}
