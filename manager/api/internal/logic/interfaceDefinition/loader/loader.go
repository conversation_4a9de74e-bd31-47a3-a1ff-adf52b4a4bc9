package loader

import (
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/types"
)

type Loader interface {
	Init() error
	RangeServices(f func(serviceName string, documents []DocumentRecorder, schemas []SchemaRecorder) bool)
	RangeSchemas(f func(apiName string, schema SchemaRecorder) bool)
	GetSchemaRecorderByFullName(fullName string) (SchemaRecorder, error)
	GetFieldSchemaByFullName(fullName string, excludeRoot bool) (types.FieldSchema, error)
}
