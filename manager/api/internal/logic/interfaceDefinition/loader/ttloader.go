package loader

import (
	"bufio"
	"bytes"
	"fmt"
	"io"
	"io/fs"
	"os"
	"path/filepath"
	"strconv"
	"strings"

	"github.com/jhump/protoreflect/desc/protoparse"
	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"
	"github.com/zeromicro/go-zero/core/lang"
	"github.com/zeromicro/go-zero/core/logx"
	"google.golang.org/protobuf/reflect/protodesc"
	"google.golang.org/protobuf/reflect/protoreflect"
	"google.golang.org/protobuf/reflect/protoregistry"
	"google.golang.org/protobuf/types/descriptorpb"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/utils"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
)

var _ Loader = (*TTLoader)(nil)

type TTLoaderOption func(l *TTLoader)

type TTLoader struct {
	rootPath          string
	excludeDirs       []string
	excludeFiles      []string
	ignoreCmdDefFile  bool
	ignoreFixJsonFile bool

	sourceFiles        []string
	protoregistryFiles *protoregistry.Files

	documentIdRecorders Recorders
	schemaIdRecorders   Recorders
	serviceRecorders    ServiceRecorders
	schemaRecorders     SchemaRecorders
	cmdRecorders        CmdRecorders
	fixRecorders        FixRecorders

	genDocumentIdFunc GenIdFunc
	genSchemaIdFunc   GenIdFunc
}

func NewTTLoader(rootPath string, options ...TTLoaderOption) Loader {
	l := &TTLoader{
		rootPath:            rootPath,
		sourceFiles:         make([]string, 0, common.ConstWalkDirMakeFiles),
		documentIdRecorders: make(Recorders),
		schemaIdRecorders:   make(Recorders),
		serviceRecorders:    make(ServiceRecorders),
		schemaRecorders:     make(SchemaRecorders),
		cmdRecorders:        make(CmdRecorders),
		fixRecorders:        make(FixRecorders),
		genDocumentIdFunc: func(name string, cache Recorders) string {
			// 相信生成的 `document_id` 重复概率很低，默认不做检查
			id := utils.GenInterfaceDocumentId()
			cache[id] = lang.Placeholder
			return id
		},
		genSchemaIdFunc: func(name string, cache Recorders) string {
			// 相信生成的 `schema_id` 重复概率很低，默认不做检查
			id := utils.GenInterfaceSchemaId()
			cache[id] = lang.Placeholder
			return id
		},
	}

	for _, option := range options {
		option(l)
	}

	return l
}

func WithExcludeDirs(dirs []string) TTLoaderOption {
	return func(l *TTLoader) {
		l.excludeDirs = dirs
	}
}

func WithExcludeFiles(files []string) TTLoaderOption {
	return func(l *TTLoader) {
		l.excludeFiles = files
	}
}

func WithIgnoreCmdDefFile() TTLoaderOption {
	return func(l *TTLoader) {
		l.ignoreCmdDefFile = true
	}
}

func WithIgnoreFixJsonFile() TTLoaderOption {
	return func(l *TTLoader) {
		l.ignoreFixJsonFile = true
	}
}

func WithGenDocumentIdFunc(f GenIdFunc) TTLoaderOption {
	return func(l *TTLoader) {
		l.genDocumentIdFunc = f
	}
}

func WithGenSchemaIdFunc(f GenIdFunc) TTLoaderOption {
	return func(l *TTLoader) {
		l.genSchemaIdFunc = f
	}
}

// Init 初始化TT接口定义加载器
func (l *TTLoader) Init() error {
	// 加载 `.proto` 文件
	err := l.loadProtos()
	if err != nil {
		return err
	}

	if !l.ignoreCmdDefFile {
		// 加载 `cmddef.h` 文件
		err = l.loadCmdDefFile()
		if err != nil {
			return err
		}
	}

	if !l.ignoreFixJsonFile {
		// 加载 `fix.json` 文件
		err = l.loadFixJsonFile()
		if err != nil {
			return err
		}
	}

	// 生成接口定义及数据模型
	l.generateDefinitionsAndSchemas()

	// 清空数据模型ID记录器
	// l.schemaIdRecorders = make(Recorders)

	return nil
}

func (l *TTLoader) walkDir() error {
	return filepath.Walk(
		l.rootPath, func(path string, info fs.FileInfo, err error) error {
			if err != nil {
				return err
			} else if info.IsDir() {
				if strings.HasPrefix(info.Name(), ".") {
					// 忽略目录 `.` 和 `..`
					return filepath.SkipDir
				}
				for _, d := range l.excludeDirs {
					if d == info.Name() || d == path {
						// 忽略排除的目录
						return filepath.SkipDir
					}
				}
			} else if !info.IsDir() && strings.HasSuffix(info.Name(), ConstProtobufFileExt) {
				l.sourceFiles = append(
					l.sourceFiles,
					strings.TrimPrefix(strings.ReplaceAll(path, l.rootPath, ""), string(filepath.Separator)),
				)
			}

			return nil
		},
	)
}

// loadProtos 加载 `.proto` 文件
func (l *TTLoader) loadProtos() error {
	err := l.walkDir()
	if err != nil {
		return err
	}

	parse := &protoparse.Parser{
		ImportPaths: []string{l.rootPath},
	}

	// `.proto`文件 => []*desc.FileDescriptor (github.com/jhump/protoreflect/desc)
	fileDescriptors, err := parse.ParseFiles(l.sourceFiles...)
	if err != nil {
		return err
	}

	// *desc.FileDescriptor => *descriptor.FileDescriptorProto (github.com/golang/protobuf/protoc-gen-go/descriptor)
	// *descriptor.FileDescriptorProto => *descriptorpb.FileDescriptorProto (google.golang.org/protobuf/types/descriptorpb)
	// []*descriptorpb.FileDescriptorProto => *descriptorpb.FileDescriptorSet (google.golang.org/protobuf/types/descriptorpb)
	fileDescriptorSet := &descriptorpb.FileDescriptorSet{
		File: make(
			[]*descriptorpb.FileDescriptorProto, 0, len(fileDescriptors),
		),
	}
	for _, fileDescriptor := range fileDescriptors {
		var exclude bool
		for _, f := range l.excludeFiles {
			if f == fileDescriptor.GetName() {
				// 忽略排除的文件
				exclude = true
				break
			}
		}
		if !exclude {
			fileDescriptorSet.File = append(fileDescriptorSet.File, fileDescriptor.AsFileDescriptorProto())
		}
	}

	// *descriptorpb.FileDescriptorSet => *protoregistry.Files (google.golang.org/protobuf/reflect/protoregistry)
	l.protoregistryFiles, err = protodesc.FileOptions{AllowUnresolvable: true}.NewFiles(fileDescriptorSet)

	return err
}

// generateDefinitionsAndSchemas 生成接口定义及数据模型
func (l *TTLoader) generateDefinitionsAndSchemas() {
	l.protoregistryFiles.RangeFiles(
		func(fileDescriptor protoreflect.FileDescriptor) bool {
			serviceName := strings.TrimSuffix(fileDescriptor.Path(), ConstProtobufFileExt)
			l.getServiceRecorderByServiceName(serviceName)

			messageDescriptors := fileDescriptor.Messages()
			for i := 0; i < messageDescriptors.Len(); i++ {
				messageDescriptor := messageDescriptors.Get(i)
				l.generateSchemaByMessageDescriptor(messageDescriptor)
			}

			return true
		},
	)
}

// generateSchemaByMessageDescriptor 通过 `MessageDescriptor` 生成 `*SchemaRecorder`
func (l *TTLoader) generateSchemaByMessageDescriptor(md protoreflect.MessageDescriptor) *SchemaRecorder {
	mdFullName := string(md.FullName())
	mdName := string(md.Name())
	srvName := getFileNameByMessageDescriptor(md)

	sr, ok := l.schemaRecorders[mdFullName]
	if ok {
		return sr
	}

	ps := &types.Schema{
		Title:      ConstJsonSchemaRootTitle,
		Type:       common.ConstInterfaceDefinitionFieldTypeObject,
		Properties: make(types.FieldSchema),
		Index:      1,
	}
	// 默认增加根节点 `root`
	sr = &SchemaRecorder{
		SchemaId: l.genSchemaIdFunc(mdFullName, l.schemaIdRecorders),
		FullName: mdFullName,
		Name:     mdName,
		Data:     ps,
	}
	// 避免循环引用导致死循环，提前设置
	l.schemaRecorders[mdFullName] = sr
	// 当前 `MessageDescriptor` 是请求或者响应消息则设置
	l.setDefinitionSchema(md, sr)

	if srvName != "" {
		srv := l.getServiceRecorderByServiceName(srvName)
		if _, ok = srv.SchemaRecorders[mdFullName]; !ok {
			srv.SchemaRecorders[mdFullName] = sr
		}
	}

	fds := md.Fields()

	for i := 0; i < fds.Len(); i++ {
		schema := ps
		fd := fds.Get(i)
		fdName := getName(fd)

		od := fd.ContainingOneof()
		if od != nil {
			// `od` 不等于nil，表示当前的 `FieldDescriptor` 属于 `oneof` 中
			odName := string(od.Name()) // `od.Name()` 返回的是 `oneof` 这个字段名称，如：data

			schema, ok = ps.Properties[odName]
			if !ok {
				// `oneof` 字段未在数据模型中则需要先创建
				schema = &types.Schema{
					Title:       odName,
					Type:        common.ConstInterfaceDefinitionFieldTypeOneOf,
					Description: string(od.FullName()),
					Properties:  make(types.FieldSchema, od.Fields().Len()),
					Index:       int32(len(ps.Properties) + 1),
				}
				ps.Properties[odName] = schema
			}
		}
		// 当前的 `FieldDescriptor` 创建到数据模型中（包括 `oneof` 字段的 `properties` 下）
		s := l.generateSchemaByFieldDescriptor(fd)
		s.Index = int32(len(schema.Properties) + 1)
		if s.FieldRequired {
			schema.Required = append(schema.Required, fdName)
		}
		if s.Ref != nil {
			if v, ok := l.schemaRecorders[s.Ref.FullName]; ok {
				sr.References = append(sr.References, v)
			}
		}
		schema.Properties[fdName] = s
	}

	return sr
}

// generateSchemaByFieldDescriptor 通过 `FieldDescriptor` 生成 `*types.Schema`
func (l *TTLoader) generateSchemaByFieldDescriptor(fd protoreflect.FieldDescriptor) *types.Schema {
	s := &types.Schema{
		Title:         getName(fd),
		Type:          getType(fd, false),
		Description:   string(fd.FullName()),
		FieldRequired: getRequired(fd),
		Default:       getDefault(fd),
		Format:        getFormat(fd),
	}

	if fd.IsList() {
		l.handleListType(fd, s)
	} else if fd.IsMap() {
		l.handleMapType(fd, s)
	} else if fd.Kind() == protoreflect.MessageKind {
		l.handleMessageType(fd, s)
	} else if fd.Kind() == protoreflect.EnumKind {
		l.handleEnumType(fd, s)
	}

	return s
}

func (l *TTLoader) setDefinitionSchema(md protoreflect.MessageDescriptor, sr *SchemaRecorder) {
	var (
		apiName string
		isReq   bool
		isResp  bool
	)

	mdName := string(md.Name())
	for _, sx := range []string{ConstReqMsgSuffix, ConstRequestMsgSuffix} {
		if strings.HasSuffix(mdName, sx) {
			apiName = strings.TrimSuffix(mdName, sx)
			isReq = true
			break
		}
	}
	for _, sx := range []string{ConstRspMsgSuffix, ConstRespMsgSuffix, ConstResponseMsgSuffix} {
		if strings.HasSuffix(mdName, sx) {
			apiName = strings.TrimSuffix(mdName, sx)
			isResp = true
			break
		}
	}
	if !isReq && !isResp {
		return
	}

	srvName := getFileNameByMessageDescriptor(md)
	if srvName == "" {
		return
	}

	srv := l.getServiceRecorderByServiceName(srvName)

	doc, ok := srv.DocumentRecorders[apiName]
	if !ok {
		doc = &DocumentRecorder{
			DocumentId: l.genDocumentIdFunc(apiName, l.documentIdRecorders),
			Service:    srvName,
			ApiName:    apiName,
		}
		srv.DocumentRecorders[apiName] = doc
	}

	if isReq {
		doc.ReqSchema = sr
	} else if isResp {
		doc.RespSchema = sr
	}

	if cmd, err := l.getCmd(apiName); err == nil {
		doc.Cmd = cmd
	}
}

func (l *TTLoader) handleListType(fd protoreflect.FieldDescriptor, s *types.Schema) {
	s.Items = &types.Schema{
		Title: "items",
		Type:  getType(fd, true),
	}

	if fd.Kind() == protoreflect.MessageKind {
		md := fd.Message()
		fullName := string(md.FullName())
		r, ok := l.schemaRecorders[fullName]
		if !ok {
			r = l.generateSchemaByMessageDescriptor(md)
		}
		s.Items.Description = fullName
		s.Items.Ref = &types.RefSchema{
			SchemaId:    r.SchemaId,
			FullName:    fullName,
			DisplayName: string(md.Name()),
		}
	}
}

func (l *TTLoader) handleMapType(fd protoreflect.FieldDescriptor, s *types.Schema) {
	s.Description = fmt.Sprintf("%s: map[%s, %s]", s.Description, fd.MapKey().FullName(), fd.MapValue().FullName())
}

func (l *TTLoader) handleMessageType(fd protoreflect.FieldDescriptor, s *types.Schema) {
	md := fd.Message()
	fullName := string(md.FullName())
	r, ok := l.schemaRecorders[fullName]
	if !ok {
		r = l.generateSchemaByMessageDescriptor(md)
	}
	s.Ref = &types.RefSchema{
		SchemaId:    r.SchemaId,
		FullName:    fullName,
		DisplayName: string(md.Name()),
	}
}

func (l *TTLoader) handleEnumType(fd protoreflect.FieldDescriptor, s *types.Schema) {
	evs := fd.Enum().Values()
	ll := evs.Len()

	s.Enum = make([]any, ll)
	s.Enums = make([]types.EnumValDesc, ll)

	for j := 0; j < ll; j++ {
		ev := evs.Get(j)
		s.Enum[j] = ev.Number()
		s.Enums[j] = types.EnumValDesc{
			Value:       ev.Number(),
			Description: string(ev.FullName()),
		}
	}
}

func getName(fd protoreflect.FieldDescriptor) string {
	// 接口数据模型字段名称采用蛇形命名，`FieldDescriptor.JSONName()`返回的是小驼峰命名
	//if fd.HasJSONName() {
	//	return fd.JSONName()
	//}

	return string(fd.Name())
}

func getType(fd protoreflect.FieldDescriptor, getInnerType bool) common.InterfaceDefinitionFieldType {
	if !getInnerType {
		if fd.IsList() {
			return common.ConstInterfaceDefinitionFieldTypeArray
		} else if fd.IsMap() {
			return common.ConstInterfaceDefinitionFieldTypeMap
		}
	}

	switch fd.Kind() {
	case protoreflect.StringKind, protoreflect.BytesKind:
		return common.ConstInterfaceDefinitionFieldTypeString
	case protoreflect.EnumKind, protoreflect.Int32Kind, protoreflect.Sint32Kind, protoreflect.Uint32Kind, protoreflect.Sfixed32Kind, protoreflect.Fixed32Kind,
		protoreflect.Int64Kind, protoreflect.Sint64Kind, protoreflect.Uint64Kind, protoreflect.Sfixed64Kind, protoreflect.Fixed64Kind:
		return common.ConstInterfaceDefinitionFieldTypeInteger
	case protoreflect.FloatKind, protoreflect.DoubleKind:
		return common.ConstInterfaceDefinitionFieldTypeNumber
	case protoreflect.BoolKind:
		return common.ConstInterfaceDefinitionFieldTypeBoolean
	case protoreflect.MessageKind:
		return common.ConstInterfaceDefinitionFieldTypeSchema
	}

	return common.ConstInterfaceDefinitionFieldTypeAny
}

func getRequired(fd protoreflect.FieldDescriptor) bool {
	if fd.Syntax() == protoreflect.Proto2 && fd.Cardinality() == protoreflect.Required {
		return true
	}

	return false
}

func getDefault(fd protoreflect.FieldDescriptor) any {
	if fd.HasDefault() {
		return fd.Default().Interface()
	}

	return nil
}

func getFormat(fd protoreflect.FieldDescriptor) common.InterfaceDefinitionFieldFormat {
	switch fd.Kind() {
	case protoreflect.EnumKind, protoreflect.Int32Kind, protoreflect.Sint32Kind, protoreflect.Uint32Kind, protoreflect.Sfixed32Kind, protoreflect.Fixed32Kind:
		return common.ConstInterfaceDefinitionFieldFormatInt32
	case protoreflect.Int64Kind, protoreflect.Sint64Kind, protoreflect.Uint64Kind, protoreflect.Sfixed64Kind, protoreflect.Fixed64Kind:
		return common.ConstInterfaceDefinitionFieldFormatInt64
	case protoreflect.FloatKind:
		return common.ConstInterfaceDefinitionFieldFormatFloat
	case protoreflect.DoubleKind:
		return common.ConstInterfaceDefinitionFieldFormatDouble
	default:
		return ""
	}
}

func getFileNameByMessageDescriptor(md protoreflect.MessageDescriptor) string {
	fd := md.ParentFile()
	if fd == nil {
		return ""
	}

	return strings.TrimSuffix(fd.Path(), ConstProtobufFileExt)
}

// 加载 `cmddef.h` 文件
func (l *TTLoader) loadCmdDefFile() error {
	cmdDefFile := filepath.Join(l.rootPath, ConstCmdDefFileName)
	fi, err := os.Stat(cmdDefFile)
	if os.IsNotExist(err) {
		return notFoundCmdDefFile
	} else if err != nil {
		return err
	} else if fi.IsDir() {
		return notFoundCmdDefFile
	}

	bs, err := os.ReadFile(cmdDefFile)
	if err != nil {
		return err
	}

	reader := bufio.NewReader(bytes.NewReader(bs))
	for {
		line, err := reader.ReadString('\n')
		if err != nil && err != io.EOF {
			return err
		} else if err == io.EOF {
			break
		}

		line = strings.TrimSpace(line)
		match, err := cmdRegexCompile.FindStringMatch(line)
		if err != nil {
			logx.Errorf("failed to find string match with the content[%s], error: %+v", line, err)
			continue
		} else if match == nil {
			// not match pattern
			continue
		}

		key := match.GroupByName(ConstGroupNameCmdName)
		if key == nil {
			logx.Errorf("failed to get the match group by name[%s] with the content[%s]", ConstGroupNameCmdName, line)
			continue
		}
		cmdKey := key.String()

		val := match.GroupByName(ConstGroupNameCmdValue)
		if val == nil {
			logx.Errorf("failed to get the match group by name[%s] with the content[%s]", ConstGroupNameCmdValue, line)
			continue
		}
		cmdVal, err := strconv.ParseUint(val.String(), 10, 32)
		if err != nil {
			logx.Errorf(
				"the content[%s] matches the pattern, but the cmd value[%s] cannot convert to integer, error: %+v",
				line, val.String(), err,
			)
			continue
		}

		l.cmdRecorders[cmdKey] = uint32(cmdVal)
	}

	return nil
}

// 加载 `fix.json` 文件
func (l *TTLoader) loadFixJsonFile() error {
	fixJsonFile := filepath.Join(l.rootPath, ConstFixJsonFileName)
	fi, err := os.Stat(fixJsonFile)
	if os.IsNotExist(err) {
		return notFoundFixJsonFile
	} else if err != nil {
		return err
	} else if fi.IsDir() {
		return notFoundFixJsonFile
	}

	bs, err := os.ReadFile(fixJsonFile)
	if err != nil {
		return err
	}

	var fos []FixObj
	err = jsonx.Unmarshal(bs, &fos)
	if err != nil {
		return err
	}

	for _, fo := range fos {
		if fo.ApiName == "" || fo.Cmd == 0 {
			continue
		}

		if _, ok := l.fixRecorders[fo.ApiName]; ok {
			continue
		}

		l.fixRecorders[fo.ApiName] = fo.Cmd
	}

	return nil
}

// 获取 `cmd` 值
func (l *TTLoader) getCmd(name string) (uint32, error) {
	var (
		cmdName string
		apiName string
	)

	cmdName = name
	apiName = cmdName
	match, err := suffixRegexCompile.FindStringMatch(name)
	if err == nil && match != nil {
		group := match.GroupByName(ConstGroupNameCmdName)
		if group == nil {
			logx.Errorf("failed to get the match group by name[%s]", ConstGroupNameCmdName)
		} else {
			cmdName = group.String()
			apiName = cmdName
		}
	}

	if !strings.HasPrefix(cmdName, ConstCmdNamePrefix) {
		cmdName = ConstCmdNamePrefix + cmdName
	}

	for _, n := range []string{cmdName, strings.ToUpper(cmdName)} {
		cmd, ok := l.cmdRecorders[n]
		if ok {
			return cmd, nil
		}
	}

	cmd, ok := l.fixRecorders[apiName]
	if ok {
		return cmd, nil
	}

	return 0, errors.Errorf("cannot find the cmd by the name[%s => %s, %s]", name, cmdName, apiName)
}

func (l *TTLoader) getServiceRecorderByServiceName(serviceName string) *ServiceRecorder {
	sr, ok := l.serviceRecorders[serviceName]
	if !ok {
		sr = &ServiceRecorder{
			DocumentRecorders: make(DocumentRecorders),
			SchemaRecorders:   make(SchemaRecorders),
		}
		l.serviceRecorders[serviceName] = sr
	}

	return sr
}

func (l *TTLoader) RangeServices(
	f func(
		serviceName string, documents []DocumentRecorder, schemas []SchemaRecorder,
	) bool,
) {
	for srvName, serviceRecorder := range l.serviceRecorders {
		documents := make([]DocumentRecorder, 0, len(serviceRecorder.DocumentRecorders))
		schemas := make([]SchemaRecorder, 0, len(serviceRecorder.SchemaRecorders))

		for _, dr := range serviceRecorder.DocumentRecorders {
			documents = append(documents, *dr)
		}
		for _, sr := range serviceRecorder.SchemaRecorders {
			schemas = append(schemas, *sr)
		}
		if !f(srvName, documents, schemas) {
			return
		}
	}
}

func (l *TTLoader) RangeSchemas(f func(apiName string, schema SchemaRecorder) bool) {
	for apiName, schema := range l.schemaRecorders {
		if !f(apiName, *schema) {
			return
		}
	}
}

func (l *TTLoader) GetSchemaRecorderByFullName(fullName string) (SchemaRecorder, error) {
	sr, ok := l.schemaRecorders[fullName]
	if !ok {
		return SchemaRecorder{}, errors.Errorf("schema[%s] not found", fullName)
	}

	return *sr, nil
}

func (l *TTLoader) GetFieldSchemaByFullName(fullName string, excludeRoot bool) (types.FieldSchema, error) {
	sr, err := l.GetSchemaRecorderByFullName(fullName)
	if err != nil {
		return nil, err
	}

	data := sr.Data
	if excludeRoot {
		return data.Properties, nil
	}

	return types.FieldSchema{ConstJsonSchemaRootTitle: data}, nil
}
