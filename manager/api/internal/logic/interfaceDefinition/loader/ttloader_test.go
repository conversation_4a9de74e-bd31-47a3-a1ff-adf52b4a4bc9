package loader

import (
	"testing"

	"github.com/zeromicro/go-zero/core/jsonx"
)

func TestTTLoader(t *testing.T) {
	tests := []struct {
		name string
		args struct {
			target  string
			opts    []TTLoaderOption
			schemas []string
		}
	}{
		{
			name: "probe",
			args: struct {
				target  string
				opts    []TTLoaderOption
				schemas []string
			}{
				target:  "/Users/<USER>/Workspace/GolandProjects/probe-backend/protos",
				opts:    []TTLoaderOption{WithIgnoreCmdDefFile(), WithIgnoreFixJsonFile()},
				schemas: []string{"manager.GetApiExecutionDataReq", "manager.ApiExecutionData"},
			},
		},
		{
			name: "tt",
			args: struct {
				target  string
				opts    []TTLoaderOption
				schemas []string
			}{
				target:  "/Users/<USER>/Workspace/GolandProjects/probe-backend/manager/tt_app",
				opts:    []TTLoaderOption{WithExcludeFiles([]string{"validate.proto"})},
				schemas: []string{"ga.ChannelEnterReq", "ga.ChannelEnterResp"},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			loader := NewTTLoader(tt.args.target, tt.args.opts...)
			err := loader.Init()
			if err != nil {
				t.Errorf("Init error: %+v", err)
				return
			}

			ttLoader, ok := loader.(*TTLoader)
			if !ok {
				t.Errorf("the type %T is not a *TTLoader", loader)
			}

			t.Logf("NumFiles: %d", ttLoader.protoregistryFiles.NumFiles())
			t.Logf("Services: %d", len(ttLoader.serviceRecorders))
			loader.RangeServices(func(serviceName string, documents []DocumentRecorder, schemas []SchemaRecorder) bool {
				t.Logf("Service: %s, %d, %d", serviceName, len(documents), len(schemas))
				return true
			})

			for _, s := range tt.args.schemas {
				t.Logf("\n%s", jsonx.MarshalToIndentStringIgnoreError(ttLoader.schemaRecorders[s], "", "  "))
			}
		})
	}
}
