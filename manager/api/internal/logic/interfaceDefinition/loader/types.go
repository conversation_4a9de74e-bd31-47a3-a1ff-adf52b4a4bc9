package loader

import (
	"github.com/zeromicro/go-zero/core/lang"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/types"
)

// ServiceRecorder 服务记录器
type ServiceRecorder struct {
	DocumentRecorders DocumentRecorders `json:"document_recorders"`
	SchemaRecorders   SchemaRecorders   `json:"schema_recorders"`
}

// DocumentRecorder 接口文档记录器
type DocumentRecorder struct {
	DocumentId string          `json:"document_id"`
	Service    string          `json:"service"`
	ApiName    string          `json:"api_name"`
	ReqSchema  *SchemaRecorder `json:"req_schema"`
	RespSchema *SchemaRecorder `json:"resp_schema"`
	Cmd        uint32          `json:"cmd"`
}

// SchemaRecorder 接口数据模型记录器
type SchemaRecorder struct {
	SchemaId   string            `json:"schema_id"`
	FullName   string            `json:"full_name"`
	Name       string            `json:"name"`
	Data       *types.Schema     `json:"data"`
	References []*SchemaRecorder `json:"references"`
}

type (
	Recorders         = map[string]lang.PlaceholderType
	ServiceRecorders  = map[string]*ServiceRecorder  // key: serviceName
	DocumentRecorders = map[string]*DocumentRecorder // key: apiName
	SchemaRecorders   = map[string]*SchemaRecorder   // key: fullName
	CmdRecorders      = map[string]uint32            // key: cmdKey
	FixRecorders      = map[string]uint32            // key: apiName
)

type GenIdFunc func(name string, cache Recorders) string

type FixObj struct {
	ApiName string `json:"api_name"`
	// ReqMessage  string `json:"req_message"`
	// RespMessage string `json:"resp_message"`
	Cmd uint32 `json:"cmd"`
}
