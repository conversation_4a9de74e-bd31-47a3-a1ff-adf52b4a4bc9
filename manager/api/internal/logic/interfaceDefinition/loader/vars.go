package loader

import (
	"fmt"

	"github.com/dlclark/regexp2"
	"github.com/pkg/errors"
)

var (
	notFoundCmdDefFile  = errors.Errorf("%s not found", ConstCmdDefFileName)
	notFoundFixJsonFile = errors.Errorf("%s not found", ConstFixJsonFileName)

	cmdRegexCompile    = regexp2.MustCompile(fmt.Sprintf(`^(?!//).*?((?<%s>CMD_\w+)\s*=\s*(?<%s>\d+)\s*;.*)`, ConstGroupNameCmdName, ConstGroupNameCmdValue), regexp2.None)
	suffixRegexCompile = regexp2.MustCompile(fmt.Sprintf(`^(?<%s>.*?)(req|rsp|resp)$`, ConstGroupNameCmdName), regexp2.IgnoreCase)
)

const (
	ConstProtobufFileExt = ".proto"
	ConstJsonFileExt     = ".json"
	ConstYamlFileExt     = ".yaml"

	ConstCmdDefFileName  = "cmddef.h"
	ConstFixJsonFileName = "fix.json"

	ConstGroupNameCmdName  = "cmd_name"
	ConstGroupNameCmdValue = "cmd_val"
	ConstCmdNamePrefix     = "CMD_"

	ConstReqMsgSuffix      = "Req"
	ConstRequestMsgSuffix  = "Request"
	ConstRspMsgSuffix      = "Rsp"
	ConstRespMsgSuffix     = "Resp"
	ConstResponseMsgSuffix = "Response"

	ConstJsonSchemaRootTitle = "root"
)
