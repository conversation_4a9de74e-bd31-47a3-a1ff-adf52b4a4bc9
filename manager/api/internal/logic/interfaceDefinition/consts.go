package interfaceDefinition

const (
	ConstLimiterRate = 100

	ConstMediaTypeApplicationJsonName = "application/json"

	ConstTDSHeaderContentType = "Content-Type"
	ConstTDSCommonCallPath    = "/common/call"

	ConstTDSCommonCallFieldClient    = "client"
	ConstTDSCommonCallFieldApiName   = "api_name"
	ConstTDSCommonCallFieldFixDict   = "fix_dict"
	ConstTDSCommonCallFieldProtoJson = "proto_json"
	ConstTDSCommonCallFieldProtoFile = "proto_file"
	ConstTDSCommonCallFieldProtoMsg  = "proto_msg"
	ConstTDSCommonCallFieldReqMsg    = "req_msg"
	ConstTDSCommonCallFieldStubName  = "stub_name"
	ConstTDSCommonCallFieldMetadata  = "metadata"
	ConstTDSCommonCallFieldHeaders   = "headers"
	ConstTDSCommonCallFieldMethod    = "method"

	ConstTDSCommonCallFieldCid          = "cid"
	ConstTDSCommonCallFieldAccount      = "account"
	ConstTDSCommonCallFieldUid          = "uid"
	ConstTDSCommonCallFieldUri          = "uri"
	ConstTDSCommonCallFieldDrvType      = "drv_type"
	ConstTDSCommonCallFieldProdType     = "prod_type"
	ConstTDSCommonCallFieldPlatformType = "platform_type"

	ConstTDSCommonCallFieldCmd      = "cmd"
	ConstTDSCommonCallFieldProtocol = "protocol"
	ConstTDSCommonCallFieldPacket   = "packet"

	ConstTDSCommonCallFieldCode        = "code"
	ConstTDSCommonCallFieldMessage     = "message"
	ConstTDSCommonCallFieldData        = "data"
	ConstTDSCommonCallFieldElapsedTime = "elapsed_time"
	ConstTDSCommonCallFieldRet         = "ret"
	ConstTDSCommonCallFieldTraceId     = "trace_id"
	ConstTDSCommonCallFieldApmId       = "apm_id"

	ConstMapKeyProjectIdDocumentId               = "key:projectId:documentId:%s:%s"
	ConstMapKeyProjectIdDocumentName             = "key:projectId:name:%s:%s"
	ConstMapKeyProjectIdSchemaId                 = "key:projectId:schemaId:%s:%s"
	ConstMapKeyProjectIdSchemaFullName           = "key:projectId:fullName:%s:%s"
	ConstMapKeyProjectIdReferenceTypeReferenceId = "key:projectId:referenceType:referenceId:%s:%s:%s"
)

// WithConfigMode 使用接口配置的方式
type WithConfigMode = int8

const (
	ConstWithConfigModeDoNotUse  WithConfigMode = iota // 不使用
	ConstWithConfigModeAutomatic                       // 自动选择
	ConstWithConfigModeSpecified                       // 使用指定的
)
