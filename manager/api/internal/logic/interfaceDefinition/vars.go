package interfaceDefinition

import (
	"net/http"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/logic/interfaceDefinition/loader"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
)

var (
	mediaTypeApplicationJsonSchema = &types.Schema{
		Title:         ConstTDSHeaderContentType,
		Type:          common.ConstInterfaceDefinitionFieldTypeString,
		Index:         1,
		FieldRequired: true,
		Default:       common.ConstMediaTypeApplicationJson,
		Example:       common.ConstMediaTypeApplicationJson,
	}

	tdsCommonCallReqSchema = &types.Schema{
		Title: loader.ConstJsonSchemaRootTitle,
		Type:  common.ConstInterfaceDefinitionFieldTypeObject,
		Properties: types.FieldSchema{
			ConstTDSCommonCallFieldClient: &types.Schema{
				Title:         ConstTDSCommonCallFieldClient,
				Type:          common.ConstInterfaceDefinitionFieldTypeSchema,
				Index:         1,
				FieldRequired: true,
				Ref: &types.RefSchema{
					SchemaId:    common.ConstTDSSearchClientSchemaId,
					FullName:    common.ConstTDSServiceName + "." + common.ConstTDSSearchClientSchemaName,
					DisplayName: common.ConstTDSSearchClientSchemaName,
				},
			},
			ConstTDSCommonCallFieldApiName: &types.Schema{
				Title:         ConstTDSCommonCallFieldApiName,
				Type:          common.ConstInterfaceDefinitionFieldTypeString,
				Description:   "Api Name",
				Index:         2,
				FieldRequired: true,
				Example:       "ChannelEnter",
			},
			ConstTDSCommonCallFieldFixDict: &types.Schema{
				Title:       ConstTDSCommonCallFieldFixDict,
				Type:        common.ConstInterfaceDefinitionFieldTypeSchema,
				Description: "Fix Dict",
				Index:       3,
				Ref: &types.RefSchema{
					SchemaId:    common.ConstTDSFixDictSchemaId,
					FullName:    common.ConstTDSServiceName + "." + common.ConstTDSFixDictSchemaName,
					DisplayName: common.ConstTDSFixDictSchemaName,
				},
			},
			ConstTDSCommonCallFieldProtoJson: &types.Schema{
				Title:       ConstTDSCommonCallFieldProtoJson,
				Type:        common.ConstInterfaceDefinitionFieldTypeObject,
				Description: "ProtoBuf Json",
				Index:       4,
			},
			ConstTDSCommonCallFieldProtoFile: &types.Schema{
				Title:       ConstTDSCommonCallFieldProtoFile,
				Type:        common.ConstInterfaceDefinitionFieldTypeString,
				Description: "Proto File",
				Index:       5,
			},
			ConstTDSCommonCallFieldProtoMsg: &types.Schema{
				Title:       ConstTDSCommonCallFieldProtoMsg,
				Type:        common.ConstInterfaceDefinitionFieldTypeString,
				Description: "Proto Msg",
				Index:       6,
			},
			ConstTDSCommonCallFieldReqMsg: &types.Schema{
				Title:       ConstTDSCommonCallFieldReqMsg,
				Type:        common.ConstInterfaceDefinitionFieldTypeString,
				Description: "Req Msg",
				Index:       7,
			},
			ConstTDSCommonCallFieldStubName: &types.Schema{
				Title:       ConstTDSCommonCallFieldStubName,
				Type:        common.ConstInterfaceDefinitionFieldTypeString,
				Description: "Stub Name",
				Index:       8,
			},
			ConstTDSCommonCallFieldMetadata: &types.Schema{
				Title:       ConstTDSCommonCallFieldMetadata,
				Type:        common.ConstInterfaceDefinitionFieldTypeString,
				Description: "Metadata",
				Index:       9,
			},
			ConstTDSCommonCallFieldHeaders: &types.Schema{
				Title:       ConstTDSCommonCallFieldHeaders,
				Type:        common.ConstInterfaceDefinitionFieldTypeString,
				Description: "Headers",
				Index:       10,
			},
			ConstTDSCommonCallFieldMethod: &types.Schema{
				Title:       ConstTDSCommonCallFieldMethod,
				Type:        common.ConstInterfaceDefinitionFieldTypeString,
				Description: "Method",
				Index:       11,
			},
		},
		Required: []string{ConstTDSCommonCallFieldClient, ConstTDSCommonCallFieldApiName},
	}
	tdsSearchClientSchema = &types.Schema{
		Title: loader.ConstJsonSchemaRootTitle,
		Type:  common.ConstInterfaceDefinitionFieldTypeObject,
		Properties: types.FieldSchema{
			ConstTDSCommonCallFieldCid: &types.Schema{
				Title:       ConstTDSCommonCallFieldCid,
				Type:        common.ConstInterfaceDefinitionFieldTypeInteger,
				Description: "Client ID",
				Index:       1,
			},
			ConstTDSCommonCallFieldAccount: &types.Schema{
				Title:       ConstTDSCommonCallFieldAccount,
				Type:        common.ConstInterfaceDefinitionFieldTypeString,
				Description: "Account",
				Index:       2,
			},
			ConstTDSCommonCallFieldUid: &types.Schema{
				Title:       ConstTDSCommonCallFieldUid,
				Type:        common.ConstInterfaceDefinitionFieldTypeInteger,
				Description: "User ID",
				Index:       3,
			},
			ConstTDSCommonCallFieldUri: &types.Schema{
				Title:       ConstTDSCommonCallFieldUri,
				Type:        common.ConstInterfaceDefinitionFieldTypeString,
				Description: "Uri",
				Index:       4,
			},
			ConstTDSCommonCallFieldDrvType: &types.Schema{
				Title:       ConstTDSCommonCallFieldDrvType,
				Type:        common.ConstInterfaceDefinitionFieldTypeInteger,
				Description: "Driver Type",
				Index:       5,
				Enum:        []any{1, 2, 3, 4, 5, 6, 7},
				Enums: []types.EnumValDesc{
					{
						Value:       1,
						Description: "HTTP",
					},
					{
						Value:       2,
						Description: "WS",
					},
					{
						Value:       3,
						Description: "TCP",
					},
					{
						Value:       4,
						Description: "KAFKA",
					},
					{
						Value:       5,
						Description: "MYSQL",
					},
					{
						Value:       6,
						Description: "REDIS",
					},
					{
						Value:       7,
						Description: "GRPC",
					},
				},
			},
			ConstTDSCommonCallFieldProdType: &types.Schema{
				Title:       ConstTDSCommonCallFieldProdType,
				Type:        common.ConstInterfaceDefinitionFieldTypeInteger,
				Description: "Product Type",
				Index:       6,
				Enum:        []any{1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15},
				Enums: []types.EnumValDesc{
					{
						Value:       1,
						Description: "TT_APP",
					},
					{
						Value:       2,
						Description: "YP_APP",
					},
					{
						Value:       3,
						Description: "KJ_APP",
					},
					{
						Value:       4,
						Description: "TT_WEB",
					},
					{
						Value:       5,
						Description: "YP_WEB",
					},
					{
						Value:       6,
						Description: "KJ_WEB",
					},
					{
						Value:       7,
						Description: "MP",
					},
					{
						Value:       8,
						Description: "DB",
					},
					{
						Value:       9,
						Description: "TTCHAT_APP",
					},
					{
						Value:       10,
						Description: "GRPC",
					},
					{
						Value:       11,
						Description: "KAFKA",
					},
					{
						Value:       12,
						Description: "REC",
					},
					{
						Value:       13,
						Description: "WEFLY",
					},
					{
						Value:       14,
						Description: "BPM",
					},
					{
						Value:       15,
						Description: "TD",
					},
				},
			},
			ConstTDSCommonCallFieldPlatformType: &types.Schema{
				Title:       ConstTDSCommonCallFieldPlatformType,
				Type:        common.ConstInterfaceDefinitionFieldTypeInteger,
				Description: "Platform Type",
				Index:       7,
				Enum:        []any{1, 3},
				Enums: []types.EnumValDesc{
					{
						Value:       1,
						Description: "MOBILE",
					},
					{
						Value:       3,
						Description: "PC",
					},
				},
			},
		},
	}
	tdsFixDictSchema = &types.Schema{
		Title: loader.ConstJsonSchemaRootTitle,
		Type:  common.ConstInterfaceDefinitionFieldTypeObject,
		Properties: types.FieldSchema{
			ConstTDSCommonCallFieldCmd: &types.Schema{
				Title:       ConstTDSCommonCallFieldCmd,
				Type:        common.ConstInterfaceDefinitionFieldTypeInteger,
				Description: "TT Cmd Value",
				Index:       1,
				Example:     423,
			},
			ConstTDSCommonCallFieldProtocol: &types.Schema{
				Title:       ConstTDSCommonCallFieldProtocol,
				Type:        common.ConstInterfaceDefinitionFieldTypeString,
				Description: "SD Protocol Value",
				Index:       2,
			},
			ConstTDSCommonCallFieldPacket: &types.Schema{
				Title:       ConstTDSCommonCallFieldPacket,
				Type:        common.ConstInterfaceDefinitionFieldTypeString,
				Description: "SD Packet Value",
				Index:       3,
			},
		},
	}
	tdsCommonRespSchema = &types.Schema{
		Title: loader.ConstJsonSchemaRootTitle,
		Type:  common.ConstInterfaceDefinitionFieldTypeObject,
		Properties: types.FieldSchema{
			ConstTDSCommonCallFieldCode: &types.Schema{
				Title:         ConstTDSCommonCallFieldCode,
				Type:          common.ConstInterfaceDefinitionFieldTypeInteger,
				Index:         1,
				FieldRequired: true,
				Default:       http.StatusOK,
			},
			ConstTDSCommonCallFieldMessage: &types.Schema{
				Title:         ConstTDSCommonCallFieldMessage,
				Type:          common.ConstInterfaceDefinitionFieldTypeString,
				Index:         2,
				FieldRequired: true,
				Default:       "请求成功",
			},
			ConstTDSCommonCallFieldData: &types.Schema{
				Title:         ConstTDSCommonCallFieldData,
				Type:          common.ConstInterfaceDefinitionFieldTypeAny,
				Index:         3,
				FieldRequired: true,
			},
			ConstTDSCommonCallFieldElapsedTime: &types.Schema{
				Title:  ConstTDSCommonCallFieldElapsedTime,
				Type:   common.ConstInterfaceDefinitionFieldTypeNumber,
				Index:  4,
				Format: common.ConstInterfaceDefinitionFieldFormatFloat,
			},
			ConstTDSCommonCallFieldRet: &types.Schema{
				Title: ConstTDSCommonCallFieldRet,
				Type:  common.ConstInterfaceDefinitionFieldTypeInteger,
				Index: 5,
			},
			ConstTDSCommonCallFieldTraceId: &types.Schema{
				Title:   ConstTDSCommonCallFieldTraceId,
				Type:    common.ConstInterfaceDefinitionFieldTypeString,
				Index:   6,
				Default: "",
			},
			ConstTDSCommonCallFieldApmId: &types.Schema{
				Title:   ConstTDSCommonCallFieldApmId,
				Type:    common.ConstInterfaceDefinitionFieldTypeString,
				Index:   7,
				Default: "",
			},
		},
		Required: []string{ConstTDSCommonCallFieldCode, ConstTDSCommonCallFieldMessage, ConstTDSCommonCallFieldData},
	}
	tdsErrorRespSchema = &types.Schema{
		Title: loader.ConstJsonSchemaRootTitle,
		Type:  common.ConstInterfaceDefinitionFieldTypeObject,
		Properties: types.FieldSchema{
			ConstTDSCommonCallFieldCode: &types.Schema{
				Title:         ConstTDSCommonCallFieldCode,
				Type:          common.ConstInterfaceDefinitionFieldTypeInteger,
				Index:         1,
				FieldRequired: true,
				Default:       http.StatusInternalServerError,
			},
			ConstTDSCommonCallFieldMessage: &types.Schema{
				Title:         ConstTDSCommonCallFieldMessage,
				Type:          common.ConstInterfaceDefinitionFieldTypeString,
				Index:         2,
				FieldRequired: true,
				Default:       "请求成功",
			},
			ConstTDSCommonCallFieldData: &types.Schema{
				Title:         ConstTDSCommonCallFieldData,
				Type:          common.ConstInterfaceDefinitionFieldTypeNull,
				Index:         3,
				FieldRequired: true,
			},
			ConstTDSCommonCallFieldElapsedTime: &types.Schema{
				Title:  ConstTDSCommonCallFieldElapsedTime,
				Type:   common.ConstInterfaceDefinitionFieldTypeNumber,
				Index:  4,
				Format: common.ConstInterfaceDefinitionFieldFormatFloat,
			},
			ConstTDSCommonCallFieldRet: &types.Schema{
				Title: ConstTDSCommonCallFieldRet,
				Type:  common.ConstInterfaceDefinitionFieldTypeInteger,
				Index: 5,
			},
			ConstTDSCommonCallFieldTraceId: &types.Schema{
				Title:   ConstTDSCommonCallFieldTraceId,
				Type:    common.ConstInterfaceDefinitionFieldTypeString,
				Index:   6,
				Default: "",
			},
			ConstTDSCommonCallFieldApmId: &types.Schema{
				Title:   ConstTDSCommonCallFieldApmId,
				Type:    common.ConstInterfaceDefinitionFieldTypeString,
				Index:   7,
				Default: "",
			},
		},
		Required: []string{ConstTDSCommonCallFieldCode, ConstTDSCommonCallFieldMessage, ConstTDSCommonCallFieldData},
	}
)
