package interfaceDefinition

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type MockInterfaceDocumentLogic struct {
	*BaseLogic
}

func NewMockInterfaceDocumentLogic(ctx context.Context, svcCtx *svc.ServiceContext) *MockInterfaceDocumentLogic {
	return &MockInterfaceDocumentLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *MockInterfaceDocumentLogic) MockInterfaceDocument(req *types.MockInterfaceDocumentReq) (
	resp *types.MockInterfaceDocumentResp, err error,
) {
	in := &pb.MockInterfaceDocumentReq{}
	if err = utils.Copy(in, req, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy data[%+v] to request, error: %+v",
			req, err,
		)
	}

	out, err := l.svcCtx.ManagerInterfaceDefinitionRPC.MockInterfaceDocument(l.ctx, in)
	if err != nil {
		return nil, err
	}

	resp = &types.MockInterfaceDocumentResp{HttpRequestComponent: &types.HttpRequestComponent{}}
	if err = utils.Copy(resp.HttpRequestComponent, out.Mock, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy data[%+v] to response, error: %+v",
			out, err,
		)
	}

	return resp, nil
}
