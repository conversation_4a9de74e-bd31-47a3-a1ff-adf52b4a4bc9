package interfaceDefinition

import (
	"testing"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
)

func TestNewSchemaByName(t *testing.T) {
	type args struct {
		name string
	}
	tests := []struct {
		name    string
		args    args
		want    *types.Schema
		wantErr bool
	}{
		{
			name: common.ConstTDSCommonCallReqSchemaName,
			args: args{
				name: common.ConstTDSCommonCallReqSchemaName,
			},
			want:    tdsCommonCallReqSchema,
			wantErr: false,
		},
		{
			name: common.ConstTDSSearchClientSchemaName,
			args: args{
				name: common.ConstTDSSearchClientSchemaName,
			},
			want:    tdsSearchClientSchema,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				got, err := NewSchemaByName(tt.args.name)
				if (err != nil) != tt.wantErr {
					t.Errorf("NewSchemaByName() error = %v, wantErr %v", err, tt.wantErr)
					return
				}
				t.Logf("NewSchemaByName() got = %T, %p; want %T, %p", got, got, tt.want, tt.want)
				defer ReleaseSchema(got)
				//if !reflect.DeepEqual(got, tt.want) {
				//	t.Errorf("NewSchemaByName() got = %v, want %v", got, tt.want)
				//}
			},
		)
	}
}
