package interfaceDefinition

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type ViewInterfaceDocumentLogic struct {
	*BaseLogic
}

func NewViewInterfaceDocumentLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ViewInterfaceDocumentLogic {
	return &ViewInterfaceDocumentLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *ViewInterfaceDocumentLogic) ViewInterfaceDocument(req *types.ViewInterfaceDocumentReq) (
	resp *types.ViewInterfaceDocumentResp, err error,
) {
	in := &pb.ViewInterfaceDocumentReq{}
	if err = utils.Copy(in, req, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy data[%+v] to request, error: %+v",
			req, err,
		)
	}

	out, err := l.svcCtx.ManagerInterfaceDefinitionRPC.ViewInterfaceDocument(l.ctx, in)
	if err != nil {
		return nil, err
	}

	resp = &types.ViewInterfaceDocumentResp{InterfaceDocument: &types.InterfaceDocument{Tags: []string{}}}
	if err = utils.Copy(resp.InterfaceDocument, out.Document, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy data[%+v] to response, error: %+v",
			out, err,
		)
	}

	return resp, nil
}
