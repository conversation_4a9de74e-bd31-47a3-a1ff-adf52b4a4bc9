package interfaceDefinition

import (
	"fmt"
	"net/http"
	"sync"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"
	"github.com/zeromicro/go-zero/core/logx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/logic/interfaceDefinition/loader"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
)

var schemaPool = sync.Pool{New: func() any { return newSchema() }}

func newSchema() *types.Schema {
	return &types.Schema{}
}

func findServiceCategory(serviceName string, categories []*model.Category) *model.Category {
	for _, c := range categories {
		if serviceName == c.Name {
			return c
		}
	}

	return nil
}

func marshalTDSDocument(document loader.DocumentRecorder) string {
	var (
		body, resp200, resp500, media *types.Schema
		err                           error
	)

	defer func() {
		if err != nil {
			logx.Error(err)
		}
	}()

	body, err = NewSchemaByName(common.ConstTDSCommonCallReqSchemaName)
	if err != nil {
		return ""
	}

	resp200, err = NewSchemaByName(common.ConstTDSCommonRespSchemaName)
	if err != nil {
		return ""
	}

	resp500, err = NewSchemaByName(common.ConstTDSErrorRespSchemaName)
	if err != nil {
		return ""
	}

	media, err = NewSchemaByName(ConstMediaTypeApplicationJsonName)
	if err != nil {
		return ""
	}

	defer func() {
		ReleaseSchema(body)
		ReleaseSchema(resp200)
		ReleaseSchema(resp500)
		ReleaseSchema(media)
	}()

	body.Properties[ConstTDSCommonCallFieldApiName].Default = document.ApiName
	if document.Cmd != 0 {
		body.Properties[ConstTDSCommonCallFieldFixDict].Default = map[string]any{
			ConstTDSCommonCallFieldCmd: document.Cmd,
		}
	}
	if document.ReqSchema != nil {
		body.Properties[ConstTDSCommonCallFieldProtoJson].Type = common.ConstInterfaceDefinitionFieldTypeSchema
		body.Properties[ConstTDSCommonCallFieldProtoJson].Ref = &types.RefSchema{
			SchemaId:    document.ReqSchema.SchemaId,
			FullName:    document.ReqSchema.FullName,
			DisplayName: document.ReqSchema.Name,
		}
	}
	if document.RespSchema != nil {
		resp200.Properties[ConstTDSCommonCallFieldData].Type = common.ConstInterfaceDefinitionFieldTypeSchema
		resp200.Properties[ConstTDSCommonCallFieldData].Ref = &types.RefSchema{
			SchemaId:    document.RespSchema.SchemaId,
			FullName:    document.RespSchema.FullName,
			DisplayName: document.RespSchema.Name,
		}
	}

	d := types.Document{
		Headers: types.ArraySchema{media},
		Body: &types.BodyData{
			Type: common.ConstMediaTypeApplicationJson,
			Json: body,
		},
		Responses: types.StatusResponse{
			fmt.Sprintf("%d", http.StatusOK): &types.ResponseData{
				StatusCode:  fmt.Sprintf("%d", http.StatusOK),
				Description: http.StatusText(http.StatusOK),
				Headers:     types.ArraySchema{media},
				Body: &types.BodyData{
					Type: common.ConstMediaTypeApplicationJson,
					Json: resp200,
				},
			},
			fmt.Sprintf("%d", http.StatusInternalServerError): &types.ResponseData{
				StatusCode:  fmt.Sprintf("%d", http.StatusInternalServerError),
				Description: http.StatusText(http.StatusInternalServerError),
				Headers:     types.ArraySchema{media},
				Body: &types.BodyData{
					Type: common.ConstMediaTypeApplicationJson,
					Json: resp500,
				},
			},
		},
	}

	return jsonx.MarshalToStringIgnoreError(d)
}

func findReferenceInDocument(document types.Document) (out []string) {
	if document.Body != nil && document.Body.Type == common.ConstMediaTypeApplicationJson && document.Body.Json != nil {
		out = append(out, findReferenceInSchema(*document.Body.Json)...)
	}

	for _, rd := range document.Responses {
		if rd.Body != nil && rd.Body.Type == common.ConstMediaTypeApplicationJson && rd.Body.Json != nil {
			out = append(out, findReferenceInSchema(*rd.Body.Json)...)
		}
	}

	return
}

func findReferenceInSchema(schema types.Schema) (out []string) {
	switch schema.Type {
	case common.ConstInterfaceDefinitionFieldTypeSchema:
		if schema.Ref != nil {
			out = append(out, schema.Ref.SchemaId)
		}
	case common.ConstInterfaceDefinitionFieldTypeArray:
		if schema.Items != nil {
			out = append(out, findReferenceInSchema(*schema.Items)...)
		}
	case common.ConstInterfaceDefinitionFieldTypeObject, common.ConstInterfaceDefinitionFieldTypeAllOf,
		common.ConstInterfaceDefinitionFieldTypeAnyOf, common.ConstInterfaceDefinitionFieldTypeOneOf:
		for _, s := range schema.Properties {
			if s == nil {
				continue
			}
			out = append(out, findReferenceInSchema(*s)...)
		}
	}

	return
}

//nolint:deadcode
func findReferenceInFieldSchema(fieldSchema types.FieldSchema) (out []string) {
	for _, schema := range fieldSchema {
		out = append(out, findReferenceInSchema(*schema)...)
	}

	return
}

func NewSchemaByName(name string) (*types.Schema, error) {
	var from *types.Schema

	switch name {
	case common.ConstTDSCommonCallReqSchemaName:
		from = tdsCommonCallReqSchema
	case common.ConstTDSSearchClientSchemaName:
		from = tdsSearchClientSchema
	case common.ConstTDSFixDictSchemaName:
		from = tdsFixDictSchema
	case common.ConstTDSCommonRespSchemaName:
		from = tdsCommonRespSchema
	case common.ConstTDSErrorRespSchemaName:
		from = tdsErrorRespSchema
	case ConstMediaTypeApplicationJsonName:
		from = mediaTypeApplicationJsonSchema
	default:
		return nil, errorx.Errorf(errorx.DoesNotSupport, "the schema name[%s] is not currently supported", name)
	}

	to := schemaPool.Get()
	if err := utils.CleanUp(to); err != nil {
		return nil, err
	}
	if err := utils.Copy(to, from); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to deep copy the schema[%s], error: %+v",
			name, err,
		)
	}

	return to.(*types.Schema), nil
}

func ReleaseSchema(s *types.Schema) {
	schemaPool.Put(s)
}
