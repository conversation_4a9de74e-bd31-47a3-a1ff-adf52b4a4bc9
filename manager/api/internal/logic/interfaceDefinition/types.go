package interfaceDefinition

import (
	commontypes "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/logic/interfaceDefinition/loader"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
)

type (
	DocumentMap        = map[string]*model.InterfaceDocument                    // key: ProjectId:DocumentId or ProjectId:Name
	SchemaMap          = map[string]*model.InterfaceSchema                      // key: ProjectId:SchemaId or ProjectId:FullName
	SchemaRefRelMap    = map[string]*model.InterfaceSchemaReferenceRelationship // key: ProjectId:SchemaId
	SchemaRefRelMapMap = map[string]SchemaRefRelMap                             // key: ProjectId:ReferenceType:ReferenceId
)

type importDocumentInternalReq struct {
	loader.DocumentRecorder

	ProjectId  string                               `json:"project_id"`
	CategoryId string                               `json:"category_id"`
	ImportType common.InterfaceDefinitionImportType `json:"import_type"`
}

type importSchemaInternalReq struct {
	loader.SchemaRecorder

	ProjectId  string                               `json:"project_id"`
	CategoryId string                               `json:"category_id"`
	ImportType common.InterfaceDefinitionImportType `json:"import_type"`
}

type createSchemaReferenceInternalReq struct {
	ProjectId     string `json:"project_id"`
	ReferenceType string `json:"reference_type"`
	ReferenceId   string `json:"reference_id"`
	SchemaId      string `json:"schema_id"`
}

type createComponentGroupReferenceInternalReq struct {
	ProjectId string                  `json:"project_id"`
	CaseId    string                  `json:"case_id"`
	Version   string                  `json:"version"`
	Relations []*commontypes.Relation `json:"relations"`
}

type createElementAndComponentInternalReq struct {
	ProjectId string         `json:"project_id"`
	CaseId    string         `json:"case_id"`
	Version   string         `json:"version"`
	Nodes     []*types.Node  `json:"nodes"`
	Edges     []*types.Edge  `json:"edges"`
	Combos    []*types.Combo `json:"combos"`
}

type createElementInternalReq struct {
	ProjectId string        `json:"project_id"`
	CaseId    string        `json:"case_id"`
	Version   string        `json:"version"`
	Element   types.Element `json:"element"`
}

type createComponentInternalReq struct {
	ProjectId string          `json:"project_id"`
	CaseId    string          `json:"case_id"`
	Version   string          `json:"version"`
	Component types.Component `json:"component"`
}

type searchInternalReq struct {
	types.SearchByCategoryId

	DrillDown bool `json:"drill_down"`
}

type CreateInterfaceDocumentInternalReq struct {
	types.CreateInterfaceDocumentReq

	DocumentId string                               `json:"document_id"`
	Mode       common.InterfaceDefinitionCreateMode `json:"mode"`
	ImportType common.InterfaceDefinitionImportType `json:"import_type"`
}

type CreateInterfaceSchemaInternalReq struct {
	types.CreateInterfaceSchemaReq

	SchemaId   string                               `json:"schema_id"`
	Mode       common.InterfaceDefinitionCreateMode `json:"mode"`
	ImportType common.InterfaceDefinitionImportType `json:"import_type"`
}
