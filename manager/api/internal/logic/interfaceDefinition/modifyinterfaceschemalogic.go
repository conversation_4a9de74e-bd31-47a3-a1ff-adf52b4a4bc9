package interfaceDefinition

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type ModifyInterfaceSchemaLogic struct {
	*BaseLogic
}

func NewModifyInterfaceSchemaLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ModifyInterfaceSchemaLogic {
	return &ModifyInterfaceSchemaLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *ModifyInterfaceSchemaLogic) ModifyInterfaceSchema(req *types.ModifyInterfaceSchemaReq) (
	resp *types.ModifyInterfaceSchemaResp, err error,
) {
	in := &pb.ModifyInterfaceSchemaReq{}
	if err = utils.Copy(in, req, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy data[%+v] to request, error: %+v",
			req, err,
		)
	}

	_, err = l.svcCtx.ManagerInterfaceDefinitionRPC.ModifyInterfaceSchema(l.ctx, in)
	if err != nil {
		return nil, err
	}

	return &types.ModifyInterfaceSchemaResp{}, nil
}
