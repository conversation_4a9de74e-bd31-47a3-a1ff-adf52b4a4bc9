package interfaceDefinition

import (
	"context"

	"github.com/zeromicro/go-zero/core/logx"

	commonutils "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"
	usercommon "gitlab.ttyuyin.com/TestDevelopment/qet-backend-middleware/user/common"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/svc"
)

type BaseLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext

	converters []commonutils.TypeConverter
}

func newBaseLogic(ctx context.Context, svcCtx *svc.ServiceContext) *BaseLogic {
	return &BaseLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,

		converters: []commonutils.TypeConverter{
			logic.StringToResourceState(),
			logic.ResourceStateToString(),
			logic.ApiNodesToRpcNodes(),
			logic.ApiEdgesToRpcEdges(),
			logic.ApiCombosToRpcCombos(),
			logic.ApiRelationsToRpcRelations(),
			logic.ApiDocumentToRpcDocument(),
			logic.ApiSchemaToRpcSchema(),
			logic.RpcNodesToApiNodes(),
			logic.RpcEdgesToApiEdges(),
			logic.RpcCombosToApiCombos(),
			logic.RpcRelationsToApiRelations(),
			logic.RpcDocumentToApiDocument(),
			logic.RpcSchemaToApiSchema(),
			usercommon.StringToUserInfo(ctx, svcCtx.UserRPC.Client(), nil),
		},
	}
}
