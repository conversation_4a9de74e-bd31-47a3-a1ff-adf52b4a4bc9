package interfaceDefinition

import (
	"context"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/types"
)

type RemoteImportInterfaceDefinitionLogic struct {
	*BaseLogic
}

func NewRemoteImportInterfaceDefinitionLogic(
	ctx context.Context, svcCtx *svc.ServiceContext,
) *RemoteImportInterfaceDefinitionLogic {
	return &RemoteImportInterfaceDefinitionLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *RemoteImportInterfaceDefinitionLogic) RemoteImportInterfaceDefinition(req *types.RemoteImportInterfaceDefinitionReq) (
	resp *types.RemoteImportInterfaceDefinitionResp, err error,
) {
	// todo: add your logic here and delete this line

	return
}
