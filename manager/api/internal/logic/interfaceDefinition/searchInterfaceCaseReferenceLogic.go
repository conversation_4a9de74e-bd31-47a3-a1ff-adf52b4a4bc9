package interfaceDefinition

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"
)

type SearchInterfaceCaseReferenceLogic struct {
	*BaseLogic
}

// search reference data of interface test case
func NewSearchInterfaceCaseReferenceLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SearchInterfaceCaseReferenceLogic {
	return &SearchInterfaceCaseReferenceLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *SearchInterfaceCaseReferenceLogic) SearchInterfaceCaseReference(req *types.SearchInterfaceCaseReferenceReq) (resp *types.SearchInterfaceCaseReferenceResp, err error) {
	in := &pb.SearchInterfaceCaseReferenceReq{}
	if err = utils.Copy(in, req, l.converters...); err != nil {
		return nil, errors.Wrapf(errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy data[%+v] to request, error: %+v", req, err)
	}

	out, err := l.svcCtx.ManagerInterfaceDefinitionRPC.SearchInterfaceCaseReference(l.ctx, in)
	if err != nil {
		return nil, err
	}

	// avoid returning null to the front end
	resp = &types.SearchInterfaceCaseReferenceResp{Items: []*types.SearchInterfaceCaseReferenceItem{}}
	if err = utils.Copy(resp, out, l.converters...); err != nil {
		return nil, errors.Wrapf(errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy data[%+v] to response, error: %+v", out, err)
	}

	return resp, nil
}
