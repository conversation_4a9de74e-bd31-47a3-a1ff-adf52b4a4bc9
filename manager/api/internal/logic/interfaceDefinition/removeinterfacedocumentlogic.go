package interfaceDefinition

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type RemoveInterfaceDocumentLogic struct {
	*BaseLogic
}

func NewRemoveInterfaceDocumentLogic(ctx context.Context, svcCtx *svc.ServiceContext) *RemoveInterfaceDocumentLogic {
	return &RemoveInterfaceDocumentLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *RemoveInterfaceDocumentLogic) RemoveInterfaceDocument(req *types.RemoveInterfaceDocumentReq) (
	resp *types.RemoveInterfaceDocumentResp, err error,
) {
	in := &pb.RemoveInterfaceDocumentReq{}
	if err = utils.Copy(in, req, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy data[%+v] to request, error: %+v",
			req, err,
		)
	}

	_, err = l.svcCtx.ManagerInterfaceDefinitionRPC.RemoveInterfaceDocument(l.ctx, in)
	if err != nil {
		return nil, err
	}

	return &types.RemoveInterfaceDocumentResp{}, nil
}
