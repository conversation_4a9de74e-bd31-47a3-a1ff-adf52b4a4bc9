package interfaceDefinition

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type ModifyInterfaceDocumentLogic struct {
	*BaseLogic
}

func NewModifyInterfaceDocumentLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ModifyInterfaceDocumentLogic {
	return &ModifyInterfaceDocumentLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *ModifyInterfaceDocumentLogic) ModifyInterfaceDocument(req *types.ModifyInterfaceDocumentReq) (
	resp *types.ModifyInterfaceDocumentResp, err error,
) {
	in := &pb.ModifyInterfaceDocumentReq{}
	if err = utils.Copy(in, req, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy data[%+v] to request, error: %+v",
			req, err,
		)
	}

	_, err = l.svcCtx.ManagerInterfaceDefinitionRPC.ModifyInterfaceDocument(l.ctx, in)
	if err != nil {
		return nil, err
	}

	return &types.ModifyInterfaceDocumentResp{}, nil
}
