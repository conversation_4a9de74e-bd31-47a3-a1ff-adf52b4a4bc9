package interfaceDefinition

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type SearchInterfaceDocumentLogic struct {
	*BaseLogic
}

func NewSearchInterfaceDocumentLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SearchInterfaceDocumentLogic {
	return &SearchInterfaceDocumentLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *SearchInterfaceDocumentLogic) SearchInterfaceDocument(req *types.SearchInterfaceDocumentReq) (
	resp *types.SearchInterfaceDocumentResp, err error,
) {
	in := &pb.SearchInterfaceDocumentReq{}
	if err = utils.Copy(in, req, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy data[%+v] to request, error: %+v",
			req, err,
		)
	}

	out, err := l.svcCtx.ManagerInterfaceDefinitionRPC.SearchInterfaceDocument(l.ctx, in)
	if err != nil {
		return nil, err
	}

	// avoid returning null to the front end
	resp = &types.SearchInterfaceDocumentResp{Items: []*types.InterfaceDocument{}}
	if err = utils.Copy(resp, out, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy data[%+v] to response, error: %+v",
			out, err,
		)
	}

	return resp, nil
}
