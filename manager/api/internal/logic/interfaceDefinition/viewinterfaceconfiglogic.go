package interfaceDefinition

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type ViewInterfaceConfigLogic struct {
	*BaseLogic
}

func NewViewInterfaceConfigLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ViewInterfaceConfigLogic {
	return &ViewInterfaceConfigLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *ViewInterfaceConfigLogic) ViewInterfaceConfig(req *types.ViewInterfaceConfigReq) (
	resp *types.ViewInterfaceConfigResp, err error,
) {
	in := &pb.ViewInterfaceConfigReq{}
	if err = utils.Copy(in, req, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy data[%+v] to request, error: %+v",
			req, err,
		)
	}

	out, err := l.svcCtx.ManagerInterfaceDefinitionRPC.ViewInterfaceConfig(l.ctx, in)
	if err != nil {
		return nil, err
	}

	resp = &types.ViewInterfaceConfigResp{
		InterfaceConfig: &types.InterfaceConfig{
			InputParameters: []*types.InputParameter{}, OutputParameters: []*types.OutputParameter{},
		},
	}
	if err = utils.Copy(resp.InterfaceConfig, out.Config, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy data[%+v] to response, error: %+v",
			out, err,
		)
	}

	return resp, nil
}
