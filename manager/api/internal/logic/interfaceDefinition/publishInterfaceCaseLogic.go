package interfaceDefinition

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type PublishInterfaceCaseLogic struct {
	*BaseLogic
}

func NewPublishInterfaceCaseLogic(ctx context.Context, svcCtx *svc.ServiceContext) *PublishInterfaceCaseLogic {
	return &PublishInterfaceCaseLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *PublishInterfaceCaseLogic) PublishInterfaceCase(req *types.PublishInterfaceCaseReq) (
	resp *types.PublishInterfaceCaseResp, err error,
) {
	in := &pb.PublishInterfaceCaseReq{}
	if err = utils.Copy(in, req, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy data[%+v] to request, error: %+v",
			req, err,
		)
	}

	_, err = l.svcCtx.ManagerInterfaceDefinitionRPC.PublishInterfaceCase(l.ctx, in)
	if err != nil {
		return nil, err
	}

	return &types.PublishInterfaceCaseResp{}, nil
}
