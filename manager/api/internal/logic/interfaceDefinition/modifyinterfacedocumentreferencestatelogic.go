package interfaceDefinition

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type ModifyInterfaceDocumentReferenceStateLogic struct {
	*BaseLogic
}

func NewModifyInterfaceDocumentReferenceStateLogic(
	ctx context.Context, svcCtx *svc.ServiceContext,
) *ModifyInterfaceDocumentReferenceStateLogic {
	return &ModifyInterfaceDocumentReferenceStateLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *ModifyInterfaceDocumentReferenceStateLogic) ModifyInterfaceDocumentReferenceState(req *types.ModifyInterfaceDocumentReferenceStateReq) (
	resp *types.ModifyInterfaceDocumentReferenceStateResp, err error,
) {
	in := &pb.ModifyInterfaceDocumentReferenceStateReq{}
	if err = utils.Copy(in, req, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy data[%+v] to request, error: %+v",
			req, err,
		)
	}

	_, err = l.svcCtx.ManagerInterfaceDefinitionRPC.ModifyInterfaceDocumentReferenceState(l.ctx, in)
	if err != nil {
		return nil, err
	}

	return &types.ModifyInterfaceDocumentReferenceStateResp{}, nil
}
