package gitconfig

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/types"
)

type ModifyGitConfigLogic struct {
	*BaseLogic
}

func NewModifyGitConfigLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ModifyGitConfigLogic {
	return &ModifyGitConfigLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *ModifyGitConfigLogic) ModifyGitConfig(req *types.ModifyGitConfigReq) (resp *types.ModifyGitConfigResp, err error) {
	in := &pb.ModifyGitConfigurationReq{}
	if err = utils.Copy(in, req, l.converters...); err != nil {
		return nil, errors.Wrapf(errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy data[%+v] to request, error: %+v", req, err)
	}

	_, err = l.svcCtx.ManagerGitConfigurationRPC.ModifyGitConfiguration(l.ctx, in)
	if err != nil {
		return nil, err
	}

	return &types.ModifyGitConfigResp{}, nil
}
