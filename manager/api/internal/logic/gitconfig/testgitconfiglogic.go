package gitconfig

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/types"
)

type TestGitConfigLogic struct {
	*BaseLogic
}

func NewTestGitConfigLogic(ctx context.Context, svcCtx *svc.ServiceContext) *TestGitConfigLogic {
	return &TestGitConfigLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *TestGitConfigLogic) TestGitConfig(req *types.TestGitConfigReq) (resp *types.TestGitConfigResp, err error) {
	in := &pb.TestGitConfigurationReq{}
	if err = utils.Copy(in, req, l.converters...); err != nil {
		return nil, errors.Wrapf(errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy data[%+v] to response, error: %+v", req, err)
	}

	out, err := l.svcCtx.ManagerGitConfigurationRPC.TestGitConfiguration(l.ctx, in)
	if err != nil {
		return nil, err
	}

	resp = &types.TestGitConfigResp{Branches: []string{}}
	if out.Branches != nil {
		if err = utils.Copy(resp, out, l.converters...); err != nil {
			return nil, errors.Wrapf(errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy data[%+v] to response, error: %+v", out, err)
		}
	}

	return resp, nil
}
