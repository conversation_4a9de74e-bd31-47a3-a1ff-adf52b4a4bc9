package gitconfig

import (
	"context"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type ViewGitConfigLogic struct {
	*BaseLogic
}

func NewViewGitConfigLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ViewGitConfigLogic {
	return &ViewGitConfigLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *ViewGitConfigLogic) ViewGitConfig(req *types.ViewGitConfigReq) (resp *types.ViewGitConfigResp, err error) {
	in := &pb.ViewGitConfigurationReq{}
	if err = utils.Copy(in, req, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy data to request, data: %s, error: %+v",
			jsonx.MarshalIgnoreError(req), err,
		)
	}

	out, err := l.svcCtx.ManagerGitConfigurationRPC.ViewGitConfiguration(l.ctx, in)
	if err != nil {
		return nil, err
	}

	resp = &types.ViewGitConfigResp{GitConfiguration: &types.GitConfiguration{}}
	if err = utils.Copy(resp.GitConfiguration, out.Configuration, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy data to response, data: %s, error: %+v",
			protobuf.MarshalJSONIgnoreError(out), err,
		)
	}

	if resp.AccessToken == l.svcCtx.Config.GitLab.Token {
		// desensitized personal access token of `probe`
		resp.AccessToken = ""
	}

	return resp, nil
}
