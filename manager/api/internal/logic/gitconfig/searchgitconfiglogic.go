package gitconfig

import (
	"context"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type SearchGitConfigLogic struct {
	*BaseLogic
}

func NewSearchGitConfigLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SearchGitConfigLogic {
	return &SearchGitConfigLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *SearchGitConfigLogic) SearchGitConfig(req *types.SearchGitConfigReq) (
	resp *types.SearchGitConfigResp, err error,
) {
	in := &pb.SearchGitConfigurationReq{}
	if err = utils.Copy(in, req, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy data to request, data: %s, error: %+v",
			jsonx.MarshalIgnoreError(req), err,
		)
	}

	out, err := l.svcCtx.ManagerGitConfigurationRPC.SearchGitConfiguration(l.ctx, in)
	if err != nil {
		return nil, err
	}
	// 避免将null返回到前端
	resp = &types.SearchGitConfigResp{Items: []*types.GitConfiguration{}}
	if err = utils.Copy(resp, out, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy data to response, data: %s, error: %+v",
			protobuf.MarshalJSONIgnoreError(out), err,
		)
	}

	for _, item := range resp.Items {
		if item.AccessToken == l.svcCtx.Config.GitLab.Token {
			// desensitized personal access token of `probe`
			item.AccessToken = ""
		}
	}

	return resp, nil
}
