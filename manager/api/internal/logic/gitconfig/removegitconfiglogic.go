package gitconfig

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type RemoveGitConfigLogic struct {
	*BaseLogic
}

func NewRemoveGitConfigLogic(ctx context.Context, svcCtx *svc.ServiceContext) *RemoveGitConfigLogic {
	return &RemoveGitConfigLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *RemoveGitConfigLogic) RemoveGitConfig(req *types.RemoveGitConfigReq) (resp *types.RemoveGitConfigResp, err error) {
	in := &pb.RemoveGitConfigurationReq{}
	if err = utils.Copy(in, req, l.converters...); err != nil {
		return nil, errors.Wrapf(errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy data[%+v] to request, error: %+v", req, err)
	}

	_, err = l.svcCtx.ManagerGitConfigurationRPC.RemoveGitConfiguration(l.ctx, in)
	if err != nil {
		return nil, err
	}

	return &types.RemoveGitConfigResp{}, nil
}
