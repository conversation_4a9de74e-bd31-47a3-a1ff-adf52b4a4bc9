package gitconfig

import (
	"context"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type SyncGitConfigByWebhookLogic struct {
	*BaseLogic
}

func NewSyncGitConfigByWebhookLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SyncGitConfigByWebhookLogic {
	return &SyncGitConfigByWebhookLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *SyncGitConfigByWebhookLogic) SyncGitConfigByWebhook(req *types.SyncGitConfigByWebhookReq) (
	resp *types.SyncGitConfigByWebhookResp, err error,
) {
	_, err = l.svcCtx.ManagerGitConfigurationRPC.SyncGitConfigurationByWebhook(
		l.ctx, &pb.SyncGitConfigurationByWebhookReq{
			GitUrl:       req.GitUrl,
			TargetBranch: req.TargetBranch,
			Email:        req.Email,
		},
	)
	if err != nil {
		return nil, err
	}

	return &types.SyncGitConfigByWebhookResp{}, nil
}
