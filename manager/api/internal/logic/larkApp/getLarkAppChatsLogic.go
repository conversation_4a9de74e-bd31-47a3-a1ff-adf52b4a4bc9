package larkApp

import (
	"context"
	"time"

	lark "github.com/larksuite/oapi-sdk-go/v3"
	larkim "github.com/larksuite/oapi-sdk-go/v3/service/im/v1"
	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/logx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"

	commonlark "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/lark"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/types"
)

// Deprecated: use `chats.GetIMChatsLogic` of `larkproxy` instead.
type GetLarkAppChatsLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext

	apiPath    LarkAPIPath
	larkClient *lark.Client
}

func NewGetLarkAppChatsLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetLarkAppChatsLogic {
	return &GetLarkAppChatsLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,

		apiPath: constLarkAPIPathOfListIMChats,
		larkClient: lark.NewClient(
			svcCtx.Config.LarkCustomApp.AppID,
			svcCtx.Config.LarkCustomApp.AppSecret,
			lark.WithEnableTokenCache(true),
			lark.WithLogger(commonlark.NewLogger(ctx)),
			lark.WithReqTimeout(time.Duration(svcCtx.Config.Timeout)*time.Millisecond),
		),
	}
}

func (l *GetLarkAppChatsLogic) GetLarkAppChats() (
	resp *types.GetLarkAppChatsResp, err error,
) {
	var (
		builder   = larkim.NewListChatReqBuilder()
		pageToken = ""
	)
	resp = &types.GetLarkAppChatsResp{Items: make([]*larkim.ListChat, 0, constIMChatsSize)}

	// `*larkim.ListChatIterator`存在死循环缺陷，所以这里不使用它的迭代器
	for {
		listChatReq := builder.PageToken(pageToken).Build()
		listChatResp, err := l.larkClient.Im.Chat.List(l.ctx, listChatReq)
		if err != nil {
			return nil, errors.Wrapf(
				errorx.Err(errorx.CallExternalAPIFailure, err.Error()),
				"failed to list chats by calling lark api, api: %q, error: %+v", l.apiPath, err,
			)
		} else if listChatResp.Code != 0 {
			return nil, errorx.Errorf(
				errorx.CallExternalAPIFailure,
				"failed to list chats by calling lark api, api: %q, code: %d, message: %s",
				string(l.apiPath), listChatResp.Code, listChatResp.Msg,
			)
		} else if len(listChatResp.Data.Items) == 0 {
			break
		}

		resp.Items = append(resp.Items, listChatResp.Data.Items...)
		if listChatResp.Data.HasMore != nil && *listChatResp.Data.HasMore &&
			listChatResp.Data.PageToken != nil && *listChatResp.Data.PageToken != "" {
			pageToken = *listChatResp.Data.PageToken
		} else {
			break
		}
	}

	return resp, nil
}
