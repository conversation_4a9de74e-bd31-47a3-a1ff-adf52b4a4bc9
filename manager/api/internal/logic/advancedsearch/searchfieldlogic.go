package advancedsearch

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type SearchFieldLogic struct {
	*BaseLogic
}

func NewSearchFieldLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SearchFieldLogic {
	return &SearchFieldLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *SearchFieldLogic) SearchField(req *types.SearchAdvancedSearchFieldReq) (resp *types.SearchAdvancedSearchFieldResp, err error) {
	in := &pb.SearchAdvancedSearchFieldReq{}
	if err = utils.Copy(in, req, l.converters...); err != nil {
		return nil, errors.Wrapf(errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy data[%+v] to request, error: %+v", req, err)
	}

	out, err := l.svcCtx.ManagerAdvancedSearchRPC.SearchAdvancedSearchField(l.ctx, in)
	if err != nil {
		return nil, err
	}

	resp = &types.SearchAdvancedSearchFieldResp{Items: []*types.AdvancedSearchField{}}
	if err = utils.Copy(resp, out, l.converters...); err != nil {
		return nil, errors.Wrapf(errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy data[%+v] to response, error: %+v", out, err)
	}

	return resp, nil
}
