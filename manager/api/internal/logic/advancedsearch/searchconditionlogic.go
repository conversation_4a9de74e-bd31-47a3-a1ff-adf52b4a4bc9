package advancedsearch

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type SearchConditionLogic struct {
	*BaseLogic
}

func NewSearchConditionLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SearchConditionLogic {
	return &SearchConditionLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *SearchConditionLogic) SearchCondition(req *types.SearchAdvancedSearchConditionReq) (resp *types.SearchAdvancedSearchConditionResp, err error) {
	in := &pb.SearchAdvancedSearchConditionReq{}
	if err = utils.Copy(in, req, l.converters...); err != nil {
		return nil, errors.Wrapf(errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy data[%+v] to request, error: %+v", req, err)
	}

	out, err := l.svcCtx.ManagerAdvancedSearchRPC.SearchAdvancedSearchCondition(l.ctx, in)
	if err != nil {
		return nil, err
	}

	resp = &types.SearchAdvancedSearchConditionResp{Items: []*types.AdvancedSearchCondition{}}
	if err = utils.Copy(resp, out, l.converters...); err != nil {
		return nil, errors.Wrapf(errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy data[%+v] to response, error: %+v", out, err)
	}

	return resp, nil
}
