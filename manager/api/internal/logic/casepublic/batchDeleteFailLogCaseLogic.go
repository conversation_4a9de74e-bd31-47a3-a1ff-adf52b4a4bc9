package casepublic

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type BatchDeleteFailLogCaseLogic struct {
	*BaseLogic
}

func NewBatchDeleteFailLogCaseLogic(ctx context.Context, svcCtx *svc.ServiceContext) *BatchDeleteFailLogCaseLogic {
	return &BatchDeleteFailLogCaseLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *BatchDeleteFailLogCaseLogic) BatchDeleteFailLogCase(req *types.BatchDeleteCaseFailLogReq) (
	resp *types.BatchDeleteCaseFailLogResp, err error,
) {
	in := &pb.BatchDeleteCaseFailLogReq{}
	if err = utils.Copy(in, req, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy data[%+v] to request, error: %+v", req,
			err,
		)
	}

	_, err = l.svcCtx.ManagerCaseCommonServiceRPC.BatchDeleteFailLogCase(l.ctx, in)
	if err != nil {
		return nil, err
	}
	return resp, nil
}
