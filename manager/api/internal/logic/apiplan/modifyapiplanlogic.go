package apiplan

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type ModifyApiPlanLogic struct {
	*BaseLogic
}

func NewModifyApiPlanLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ModifyApiPlanLogic {
	return &ModifyApiPlanLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *ModifyApiPlanLogic) ModifyApiPlan(req *types.ModifyApiPlanReq) (resp *types.ModifyApiPlanResp, err error) {
	in := &pb.ModifyApiPlanReq{}
	if err = utils.Copy(in, req, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy data[%+v] to request, error: %+v",
			req, err,
		)
	}

	_, err = l.svcCtx.ManagerApiPlanRPC.ModifyApiPlan(l.ctx, in)
	if err != nil {
		return nil, err
	}

	return &types.ModifyApiPlanResp{}, nil
}
