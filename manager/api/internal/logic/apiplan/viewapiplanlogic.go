package apiplan

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type ViewApiPlanLogic struct {
	*BaseLogic
}

func NewViewApiPlanLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ViewApiPlanLogic {
	return &ViewApiPlanLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *ViewApiPlanLogic) ViewApiPlan(req *types.ViewApiPlanReq) (resp *types.ViewApiPlanResp, err error) {
	in := &pb.ViewApiPlanReq{}
	if err = utils.Copy(in, req, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy data[%+v] to request, error: %+v",
			req, err,
		)
	}

	out, err := l.svcCtx.ManagerApiPlanRPC.ViewApiPlan(l.ctx, in)
	if err != nil {
		return nil, err
	}

	resp = &types.ViewApiPlanResp{ApiPlan: &types.ApiPlan{Tags: []string{}, AccountConfigIds: []string{}}}
	if err = utils.Copy(resp.ApiPlan, out.Plan, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy data[%+v] to response, error: %+v",
			out, err,
		)
	}

	return resp, nil
}
