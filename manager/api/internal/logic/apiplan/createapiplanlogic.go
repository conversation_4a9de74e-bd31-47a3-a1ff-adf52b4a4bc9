package apiplan

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type CreateApiPlanLogic struct {
	*BaseLogic
}

func NewCreateApiPlanLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CreateApiPlanLogic {
	return &CreateApiPlanLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *CreateApiPlanLogic) CreateApiPlan(req *types.CreateApiPlanReq) (resp *types.CreateApiPlanResp, err error) {
	in := &pb.CreateApiPlanReq{}
	if err = utils.Copy(in, req, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy data[%+v] to request, error: %+v", req,
			err,
		)
	}

	out, err := l.svcCtx.ManagerApiPlanRPC.CreateApiPlan(l.ctx, in)
	if err != nil {
		return nil, err
	}

	return &types.CreateApiPlanResp{PlanId: out.GetPlan().GetPlanId()}, nil
}
