package apiplan

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type ModifyApiPlanReferenceStateLogic struct {
	*BaseLogic
}

func NewModifyApiPlanReferenceStateLogic(
	ctx context.Context, svcCtx *svc.ServiceContext,
) *ModifyApiPlanReferenceStateLogic {
	return &ModifyApiPlanReferenceStateLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *ModifyApiPlanReferenceStateLogic) ModifyApiPlanReferenceState(req *types.ModifyApiPlanReferenceStateReq) (
	resp *types.ModifyApiPlanReferenceStateResp, err error,
) {
	in := &pb.ModifyApiPlanReferenceStateReq{}
	if err = utils.Copy(in, req, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy data[%+v] to request, error: %+v",
			req, err,
		)
	}

	_, err = l.svcCtx.ManagerApiPlanRPC.ModifyApiPlanReferenceState(l.ctx, in)
	if err != nil {
		return nil, err
	}

	return &types.ModifyApiPlanReferenceStateResp{}, nil
}
