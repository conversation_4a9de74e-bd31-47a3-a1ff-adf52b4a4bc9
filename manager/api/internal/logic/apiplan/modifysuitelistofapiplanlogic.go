package apiplan

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type ModifySuiteListOfApiPlanLogic struct {
	*BaseLogic
}

func NewModifySuiteListOfApiPlanLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ModifySuiteListOfApiPlanLogic {
	return &ModifySuiteListOfApiPlanLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *ModifySuiteListOfApiPlanLogic) ModifySuiteListOfApiPlan(req *types.ModifySuiteListOfApiPlanReq) (
	resp *types.ModifySuiteListOfApiPlanResp, err error,
) {
	if req.OperationType == common.ConstListOperatorTypeAdd {
		in := &pb.AddSuiteToApiPlanReq{}
		if err = utils.Copy(in, req, l.converters...); err != nil {
			return nil, errors.Wrapf(
				errorx.Err(errorx.CopyToStructFailure, err.Error()),
				"failed to copy data[%+v] to request, error: %+v",
				req, err,
			)
		}

		_, err = l.svcCtx.ManagerApiPlanRPC.AddSuiteToApiPlan(l.ctx, in)
	} else if req.OperationType == common.ConstListOperatorTypeRemove {
		in := &pb.RemoveSuiteFromApiPlanReq{}
		if err = utils.Copy(in, req, l.converters...); err != nil {
			return nil, errors.Wrapf(
				errorx.Err(errorx.CopyToStructFailure, err.Error()),
				"failed to copy data[%+v] to request, error: %+v",
				req, err,
			)
		}

		_, err = l.svcCtx.ManagerApiPlanRPC.RemoveSuiteFromApiPlan(l.ctx, in)
	} else {
		err = errorx.Errorf(
			errorx.DoesNotSupport, "the operator type[%s] doesn't support", req.OperationType,
		)
	}

	if err != nil {
		return nil, err
	}

	return &types.ModifySuiteListOfApiPlanResp{}, nil
}
