package apiplan

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type RemoveApiPlanLogic struct {
	*BaseLogic
}

func NewRemoveApiPlanLogic(ctx context.Context, svcCtx *svc.ServiceContext) *RemoveApiPlanLogic {
	return &RemoveApiPlanLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *RemoveApiPlanLogic) RemoveApiPlan(req *types.RemoveApiPlanReq) (resp *types.RemoveApiPlanResp, err error) {
	in := &pb.RemoveApiPlanReq{}
	if err = utils.Copy(in, req, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy data[%+v] to request, error: %+v",
			req, err,
		)
	}

	_, err = l.svcCtx.ManagerApiPlanRPC.RemoveApiPlan(l.ctx, in)
	if err != nil {
		return nil, err
	}

	return &types.RemoveApiPlanResp{}, nil
}
