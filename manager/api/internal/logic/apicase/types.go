package apicase

import (
	ct "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/types"
)

type createReferenceRelationshipInternalReq struct {
	svcCtx *svc.ServiceContext

	ProjectId string         `json:"project_id"`
	CaseId    string         `json:"case_id"`
	Version   string         `json:"version"`
	Relations []*ct.Relation `json:"relations"`
}

type createElementAndComponentInternalReq struct {
	svcCtx *svc.ServiceContext

	ProjectId string         `json:"project_id"`
	CaseId    string         `json:"case_id"`
	Version   string         `json:"version"`
	Nodes     []*types.Node  `json:"nodes"`
	Edges     []*types.Edge  `json:"edges"`
	Combos    []*types.Combo `json:"combos"`
}

type searchApiCaseInternalReq struct {
	svcCtx *svc.ServiceContext

	types.SearchApiCaseReq

	DrillDown bool `json:"drill_down"`
}
