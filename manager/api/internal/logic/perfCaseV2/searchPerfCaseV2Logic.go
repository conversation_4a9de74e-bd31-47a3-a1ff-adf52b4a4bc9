package perfCaseV2

import (
	"context"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type SearchPerfCaseV2Logic struct {
	*BaseLogic
}

func NewSearchPerfCaseV2Logic(ctx context.Context, svcCtx *svc.ServiceContext) *SearchPerfCaseV2Logic {
	return &SearchPerfCaseV2Logic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *SearchPerfCaseV2Logic) SearchPerfCaseV2(req *types.SearchPerfCaseV2Req) (
	resp *types.SearchPerfCaseV2Resp, err error,
) {
	in := &pb.SearchPerfCaseV2Req{}
	if err = utils.Copy(in, req, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy data to request, data: %s, error: %+v",
			jsonx.MarshalIgnoreError(req), err,
		)
	}

	out, err := l.svcCtx.ManagerPerfCaseV2RPC.SearchPerfCaseV2(l.ctx, in)
	if err != nil {
		return nil, err
	}

	// avoid returning null to the front end
	resp = &types.SearchPerfCaseV2Resp{Items: []*types.SearchPerfCaseV2Item{}}
	if err = utils.Copy(resp, out, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy data to response, data: %s, error: %+v",
			protobuf.MarshalJSONIgnoreError(out), err,
		)
	}

	return resp, nil
}
