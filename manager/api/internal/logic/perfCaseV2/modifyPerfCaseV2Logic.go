package perfCaseV2

import (
	"context"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type ModifyPerfCaseV2Logic struct {
	*BaseLogic
}

func NewModifyPerfCaseV2Logic(ctx context.Context, svcCtx *svc.ServiceContext) *ModifyPerfCaseV2Logic {
	return &ModifyPerfCaseV2Logic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *ModifyPerfCaseV2Logic) ModifyPerfCaseV2(req *types.ModifyPerfCaseV2Req) (
	resp *types.ModifyPerfCaseV2Resp, err error,
) {
	in := &pb.ModifyPerfCaseV2Req{}
	if err = utils.Copy(in, req, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy data to request, data: %s, error: %+v",
			jsonx.MarshalIgnoreError(req), err,
		)
	}

	_, err = l.svcCtx.ManagerPerfCaseV2RPC.ModifyPerfCaseV2(l.ctx, in)
	if err != nil {
		return nil, err
	}

	return &types.ModifyPerfCaseV2Resp{}, nil
}
