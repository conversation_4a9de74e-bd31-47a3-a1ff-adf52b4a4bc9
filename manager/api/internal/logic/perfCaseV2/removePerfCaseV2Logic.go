package perfCaseV2

import (
	"context"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type RemovePerfCaseV2Logic struct {
	*BaseLogic
}

func NewRemovePerfCaseV2Logic(ctx context.Context, svcCtx *svc.ServiceContext) *RemovePerfCaseV2Logic {
	return &RemovePerfCaseV2Logic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *RemovePerfCaseV2Logic) RemovePerfCaseV2(req *types.RemovePerfCaseV2Req) (
	resp *types.RemovePerfCaseV2Resp, err error,
) {
	in := &pb.RemovePerfCaseV2Req{}
	if err = utils.Copy(in, req, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy data to request, data: %s, error: %+v",
			jsonx.MarshalIgnoreError(req), err,
		)
	}

	_, err = l.svcCtx.ManagerPerfCaseV2RPC.RemovePerfCaseV2(l.ctx, in)
	if err != nil {
		return nil, err
	}

	return &types.RemovePerfCaseV2Resp{}, nil
}
