package perfCaseV2

import (
	"context"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	commonTypes "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type ViewPerfCaseV2Logic struct {
	*BaseLogic
}

func NewViewPerfCaseV2Logic(ctx context.Context, svcCtx *svc.ServiceContext) *ViewPerfCaseV2Logic {
	return &ViewPerfCaseV2Logic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *ViewPerfCaseV2Logic) ViewPerfCaseV2(req *types.ViewPerfCaseV2Req) (resp *types.ViewPerfCaseV2Resp, err error) {
	in := &pb.ViewPerfCaseV2Req{}
	if err = utils.Copy(in, req, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy data to request, data: %s, error: %+v",
			jsonx.MarshalIgnoreError(req), err,
		)
	}

	out, err := l.svcCtx.ManagerPerfCaseV2RPC.ViewPerfCaseV2(l.ctx, in)
	if err != nil {
		return nil, err
	}

	resp = &types.ViewPerfCaseV2Resp{
		PerfCaseV2: &types.PerfCaseV2{
			RateLimits:    make([]*commonTypes.RateLimitV2, 0),
			SetupSteps:    make([]*commonTypes.PerfCaseStepV2, 0),
			SerialSteps:   make([]*commonTypes.PerfCaseStepV2, 0),
			ParallelSteps: make([]*commonTypes.PerfCaseStepV2, 0),
			TeardownSteps: make([]*commonTypes.PerfCaseStepV2, 0),
		},
	}
	if err = utils.Copy(resp.PerfCaseV2, out.GetCase(), l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy data to response, data: %s, error: %+v",
			protobuf.MarshalJSONIgnoreError(out), err,
		)
	}

	return resp, nil
}
