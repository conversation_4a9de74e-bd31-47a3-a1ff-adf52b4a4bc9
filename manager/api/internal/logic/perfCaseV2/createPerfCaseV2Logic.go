package perfCaseV2

import (
	"context"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type CreatePerfCaseV2Logic struct {
	*BaseLogic
}

func NewCreatePerfCaseV2Logic(ctx context.Context, svcCtx *svc.ServiceContext) *CreatePerfCaseV2Logic {
	return &CreatePerfCaseV2Logic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *CreatePerfCaseV2Logic) CreatePerfCaseV2(req *types.CreatePerfCaseV2Req) (
	resp *types.CreatePerfCaseV2Resp, err error,
) {
	in := &pb.CreatePerfCaseV2Req{}
	if err = utils.Copy(in, req, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy data to request, data: %s, error: %+v",
			jsonx.MarshalIgnoreError(req), err,
		)
	}

	out, err := l.svcCtx.ManagerPerfCaseV2RPC.CreatePerfCaseV2(l.ctx, in)
	if err != nil {
		return nil, err
	}

	return &types.CreatePerfCaseV2Resp{CaseId: out.GetCase().GetCaseId()}, nil
}
