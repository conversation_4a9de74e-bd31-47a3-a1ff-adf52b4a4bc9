package perfData

import (
	"context"
	"database/sql"
	"encoding/hex"
	"mime/multipart"
	"net/http"
	"os"
	"path/filepath"
	"strings"
	"time"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"
	"github.com/zeromicro/go-zero/core/stores/sqlx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"

	commonutils "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/utils"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
)

type UploadPerfDataLogic struct {
	*BaseLogic
}

func NewUploadPerfDataLogic(ctx context.Context, svcCtx *svc.ServiceContext) *UploadPerfDataLogic {
	return &UploadPerfDataLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *UploadPerfDataLogic) UploadPerfData(
	req *types.UploadPerfDataReq, r *http.Request,
) (resp *types.UploadPerfDataResp, err error) {
	// validate the project_id in req
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, req.ProjectId); err != nil {
		return nil, err
	}

	// parse the multipart form in req
	if err = r.ParseMultipartForm(maxFileSize); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.ParseParamError, err.Error()),
			"failed to parse multipart form, error: %+v",
			err,
		)
	}

	// get the file from the multipart form
	file, fileHeader, err := r.FormFile(formFileKey)
	if err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.ParseParamError, err.Error()),
			"failed to get the %q parameter, error: %+v",
			formFileKey, err,
		)
	}
	defer func(file multipart.File) {
		_ = file.Close()
	}(file)

	// generate the perf data id
	dataID, err := l.generatePerfDataID(req.ProjectId)
	if err != nil {
		return nil, err
	}

	var localFilePath, otherFilePath string

	// save the file to the local file system
	localFilePath = filepath.Join(l.svcCtx.Config.PVCPath, subDir, dataID)
	if l.svcCtx.Config.PVCNFSPath != "" {
		// save the file to the other file system, e.g. HS NFS
		otherFilePath = filepath.Join(l.svcCtx.Config.PVCNFSPath, subDir, dataID)
	}
	defer func() {
		if err != nil {
			_ = os.Remove(localFilePath)
			if otherFilePath != "" {
				_ = os.Remove(otherFilePath)
			}
		}
	}()
	md5Hash, size, err := logic.SaveFile(file, localFilePath)
	if err != nil {
		return nil, err
	} else if size != fileHeader.Size {
		return nil, errorx.Errorf(
			errorx.FileOperationFailure,
			"the size of file is mismatch, filename: %s, expected: %d, actual: %d",
			fileHeader.Filename, fileHeader.Size, size,
		)
	}

	if otherFilePath != "" {
		// 双写
		err = commonutils.CopyFile(localFilePath, otherFilePath)
		if err != nil {
			// ignore error
			l.Errorf(
				"failed to save the perf data file to path, filename: %s, path: %s, error: %+v",
				fileHeader.Filename, otherFilePath, err,
			)
		}
	}

	// get the content from the perf data file
	data, err := commonutils.GetPerfDataFromReader(file)
	if err != nil {
		return nil, err
	}

	fileExt := filepath.Ext(fileHeader.Filename)
	now := time.Now()
	perfData := &model.PerfData{
		ProjectId: req.ProjectId,
		DataId:    dataID,
		Name:      strings.TrimSuffix(fileHeader.Filename, fileExt),
		Description: sql.NullString{
			String: fileHeader.Filename,
			Valid:  true,
		},
		Extension:  fileExt,
		Hash:       hex.EncodeToString(md5Hash.Sum(nil)),
		Size:       size,
		Path:       localFilePath,
		NumberOfVu: int64(len(data.GetLines())),
		MaintainedBy: sql.NullString{
			String: l.currentUser.Account,
			Valid:  true,
		},
		CreatedBy: l.currentUser.Account,
		UpdatedBy: l.currentUser.Account,
		CreatedAt: now,
		UpdatedAt: now,
	}
	if err = l.svcCtx.PerfDataModel.Trans(
		l.ctx, func(context context.Context, session sqlx.Session) error {
			if _, err := l.svcCtx.PerfDataModel.Insert(context, session, perfData); err != nil {
				return errors.Wrapf(
					errorx.Err(errorx.DBError, err.Error()),
					"failed to insert values to table, table: %s, values: %s, error: %+v",
					l.svcCtx.PerfDataModel.Table(), jsonx.MarshalIgnoreError(perfData), err,
				)
			}

			return nil
		},
	); err != nil {
		return nil, err
	}

	return &types.UploadPerfDataResp{DataId: dataID}, nil
}
