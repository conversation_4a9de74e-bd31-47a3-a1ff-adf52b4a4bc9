package perfData

import (
	"context"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
)

type DownloadPerfDataLogic struct {
	*BaseLogic
}

func NewDownloadPerfDataLogic(ctx context.Context, svcCtx *svc.ServiceContext) *DownloadPerfDataLogic {
	return &DownloadPerfDataLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *DownloadPerfDataLogic) DownloadPerfData(req *types.DownloadPerfDataReq) (
	filename, filepath string, err error,
) {
	// validate the project_id in req
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, req.ProjectId); err != nil {
		return
	}

	// validate the data_id in req
	perfData, err := model.CheckPerfDataByDataID(l.ctx, l.svcCtx.PerfDataModel, req.ProjectId, req.DataId)
	if err != nil {
		return
	}

	filename = perfData.Name + perfData.Extension
	filepath = perfData.Path

	return
}
