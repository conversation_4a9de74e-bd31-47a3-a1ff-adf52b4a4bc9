package perfData

import (
	"context"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type RemovePerfDataLogic struct {
	*BaseLogic
}

func NewRemovePerfDataLogic(ctx context.Context, svcCtx *svc.ServiceContext) *RemovePerfDataLogic {
	return &RemovePerfDataLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *RemovePerfDataLogic) RemovePerfData(req *types.RemovePerfDataReq) (resp *types.RemovePerfDataResp, err error) {
	in := &pb.RemovePerfDataReq{}
	if err = utils.Copy(in, req, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy data to request, data: %s, error: %+v",
			jsonx.MarshalIgnoreError(req), err,
		)
	}

	_, err = l.svcCtx.ManagerPerfDataRPC.RemovePerfData(l.ctx, in)
	if err != nil {
		return nil, err
	}

	return &types.RemovePerfDataResp{}, nil
}
