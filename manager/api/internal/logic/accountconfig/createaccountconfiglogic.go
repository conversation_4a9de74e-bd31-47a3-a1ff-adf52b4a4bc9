package accountconfig

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type CreateAccountConfigLogic struct {
	*BaseLogic
}

func NewCreateAccountConfigLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CreateAccountConfigLogic {
	return &CreateAccountConfigLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *CreateAccountConfigLogic) CreateAccountConfig(req *types.CreateAccountConfigReq) (
	resp *types.CreateAccountConfigResp, err error,
) {
	in := &pb.CreateAccountConfigurationReq{}
	if err = utils.Copy(in, req); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy data[%+v] to request, error: %+v", req,
			err,
		)
	}

	out, err := l.svcCtx.ManagerAccountConfigurationRPC.CreateAccountConfiguration(l.ctx, in)
	if err != nil {
		return nil, err
	}

	return &types.CreateAccountConfigResp{ConfigId: out.GetConfiguration().GetConfigId()}, nil
}
