package accountconfig

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type ViewAccountConfigLogic struct {
	*BaseLogic
}

func NewViewAccountConfigLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ViewAccountConfigLogic {
	return &ViewAccountConfigLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *ViewAccountConfigLogic) ViewAccountConfig(req *types.ViewAccountConfigReq) (
	resp *types.ViewAccountConfigResp, err error,
) {
	in := &pb.ViewAccountConfigurationReq{}
	if err = utils.Copy(in, req); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy data[%+v] to request, error: %+v",
			req, err,
		)
	}

	out, err := l.svcCtx.ManagerAccountConfigurationRPC.ViewAccountConfiguration(l.ctx, in)
	if err != nil {
		return nil, err
	}

	resp = &types.ViewAccountConfigResp{AccountConfiguration: &types.AccountConfiguration{}}
	if err = utils.Copy(resp.AccountConfiguration, out.Configuration, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy data[%+v] to response, error: %+v",
			out, err,
		)
	}

	return resp, nil
}
