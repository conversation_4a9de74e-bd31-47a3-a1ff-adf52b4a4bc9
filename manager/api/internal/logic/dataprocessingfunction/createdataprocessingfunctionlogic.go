package dataprocessingfunction

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type CreateDataProcessingFunctionLogic struct {
	*BaseLogic
}

func NewCreateDataProcessingFunctionLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CreateDataProcessingFunctionLogic {
	return &CreateDataProcessingFunctionLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *CreateDataProcessingFunctionLogic) CreateDataProcessingFunction(req *types.CreateOrModifyDataProcessingFunctionReq) (resp *types.CreateOrModifyDataProcessingFunctionResp, err error) {
	in := &pb.CreateOrModifyDataProcessingFunctionReq{}
	if err = utils.Copy(in, req, l.converters...); err != nil {
		return nil, errors.Wrapf(errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy data[%+v] to request, error: %+v", req, err)
	}

	_, err = l.svcCtx.ManagerDataProcessingFunctionRPC.CreateDataProcessingFunction(l.ctx, in)
	if err != nil {
		return nil, err
	}

	return &types.CreateOrModifyDataProcessingFunctionResp{}, nil
}
