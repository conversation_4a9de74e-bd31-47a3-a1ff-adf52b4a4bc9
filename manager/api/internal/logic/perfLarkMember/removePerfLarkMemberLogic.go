package perfLarkMember

import (
	"context"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"
)

type RemovePerfLarkMemberLogic struct {
	*BaseLogic
}

func NewRemovePerfLarkMemberLogic(ctx context.Context, svcCtx *svc.ServiceContext) *RemovePerfLarkMemberLogic {
	return &RemovePerfLarkMemberLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *RemovePerfLarkMemberLogic) RemovePerfLarkMember(req *types.RemovePerfLarkMemberReq) (resp *types.RemovePerfLarkMemberResp, err error) {
	in := &pb.RemovePerfLarkMemberReq{}

	if err = utils.Copy(in, req, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy data to request, data: %s, error: %+v",
			jsonx.MarshalIgnoreError(req), err,
		)
	}

	_, err = l.svcCtx.ManagerPerfLarkMemberRPC.RemovePerfLarkMember(l.ctx, in)
	if err != nil {
		return nil, err
	}

	return &types.RemovePerfLarkMemberResp{}, nil
}
