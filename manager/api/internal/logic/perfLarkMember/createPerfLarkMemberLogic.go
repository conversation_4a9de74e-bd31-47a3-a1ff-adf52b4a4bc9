package perfLarkMember

import (
	"context"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"
)

type CreatePerfLarkMemberLogic struct {
	*BaseLogic
}

func NewCreatePerfLarkMemberLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CreatePerfLarkMemberLogic {
	return &CreatePerfLarkMemberLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *CreatePerfLarkMemberLogic) CreatePerfLarkMember(req *types.CreatePerfLarkMemberReq) (resp *types.CreatePerfLarkMemberResp, err error) {
	in := &pb.CreatePerfLarkMemberReq{}

	if err = utils.Copy(in, req, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy data to request, data: %s, error: %+v",
			jsonx.MarshalIgnoreError(req), err,
		)
	}

	_, err = l.svcCtx.ManagerPerfLarkMemberRPC.CreatePerfLarkMember(l.ctx, in)
	if err != nil {
		return nil, err
	}

	return &types.CreatePerfLarkMemberResp{}, nil
}
