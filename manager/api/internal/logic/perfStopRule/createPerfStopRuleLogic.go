package perfStopRule

import (
	"context"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type CreatePerfStopRuleLogic struct {
	*BaseLogic
}

func NewCreatePerfStopRuleLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CreatePerfStopRuleLogic {
	return &CreatePerfStopRuleLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *CreatePerfStopRuleLogic) CreatePerfStopRule(req *types.CreatePerfStopRuleReq) (
	resp *types.CreatePerfStopRuleResp, err error,
) {
	in := &pb.CreatePerfStopRuleReq{}

	if err = utils.Copy(in, req, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy data to request, data: %s, error: %+v",
			jsonx.MarshalIgnoreError(req), err,
		)
	}

	out, err := l.svcCtx.ManagerPerfStopRuleRPC.CreatePerfStopRule(l.ctx, in)
	if err != nil {
		return nil, err
	}

	return &types.CreatePerfStopRuleResp{RuleId: out.GetRule().GetRuleId()}, nil
}
