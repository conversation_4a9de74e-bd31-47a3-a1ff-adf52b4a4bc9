package perfStopRule

import (
	"context"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type ViewPerfStopRuleLogic struct {
	*BaseLogic
}

func NewViewPerfStopRuleLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ViewPerfStopRuleLogic {
	return &ViewPerfStopRuleLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *ViewPerfStopRuleLogic) ViewPerfStopRule(req *types.ViewPerfStopRuleReq) (
	resp *types.ViewPerfStopRuleResp, err error,
) {
	in := &pb.ViewPerfStopRuleReq{}
	if err = utils.Copy(in, req, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy data to request, data: %s, error: %+v",
			jsonx.MarshalIgnoreError(req), err,
		)
	}

	out, err := l.svcCtx.ManagerPerfStopRuleRPC.ViewPerfStopRule(l.ctx, in)
	if err != nil {
		return nil, err
	}

	resp = &types.ViewPerfStopRuleResp{PerfStopRule: &types.PerfStopRule{}}
	if err = utils.Copy(resp.PerfStopRule, out.GetRule(), l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy data to response, data: %s, error: %+v",
			protobuf.MarshalJSONIgnoreError(out), err,
		)
	}

	return resp, nil
}
