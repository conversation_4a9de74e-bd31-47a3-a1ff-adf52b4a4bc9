package generalconfig

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	ctypes "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type ViewGeneralConfigLogic struct {
	*BaseLogic
}

func NewViewGeneralConfigLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ViewGeneralConfigLogic {
	return &ViewGeneralConfigLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *ViewGeneralConfigLogic) ViewGeneralConfig(req *types.ViewGeneralConfigReq) (resp *types.ViewGeneralConfigResp, err error) {
	in := &pb.ViewGeneralConfigurationReq{}
	if err = utils.Copy(in, req); err != nil {
		return nil, errors.Wrapf(errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy data[%+v] to request, error: %+v", req, err)
	}

	out, err := l.svcCtx.ManagerGeneralConfigurationRPC.ViewGeneralConfiguration(l.ctx, in)
	if err != nil {
		return nil, err
	}

	resp = &types.ViewGeneralConfigResp{GeneralConfiguration: &types.GeneralConfiguration{Variables: []*ctypes.KeyValuePair{}}}
	if err = utils.Copy(resp.GeneralConfiguration, out.Configuration, l.converters...); err != nil {
		return nil, errors.Wrapf(errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy data[%+v] to response, error: %+v", out, err)
	}

	return resp, nil
}
