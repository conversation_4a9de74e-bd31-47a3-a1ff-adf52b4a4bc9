package generalconfig

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type RemoveGeneralConfigLogic struct {
	*BaseLogic
}

func NewRemoveGeneralConfigLogic(ctx context.Context, svcCtx *svc.ServiceContext) *RemoveGeneralConfigLogic {
	return &RemoveGeneralConfigLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *RemoveGeneralConfigLogic) RemoveGeneralConfig(req *types.RemoveGeneralConfigReq) (resp *types.RemoveGeneralConfigResp, err error) {
	in := &pb.RemoveGeneralConfigurationReq{}
	if err = utils.Copy(in, req); err != nil {
		return nil, errors.Wrapf(errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy data[%+v] to request, error: %+v", req, err)
	}

	_, err = l.svcCtx.ManagerGeneralConfigurationRPC.RemoveGeneralConfiguration(l.ctx, in)
	if err != nil {
		return nil, err
	}

	return &types.RemoveGeneralConfigResp{}, nil
}
