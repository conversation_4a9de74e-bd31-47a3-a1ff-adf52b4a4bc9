package generalconfig

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type ModifyGeneralConfigLogic struct {
	*BaseLogic
}

func NewModifyGeneralConfigLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ModifyGeneralConfigLogic {
	return &ModifyGeneralConfigLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *ModifyGeneralConfigLogic) ModifyGeneralConfig(req *types.ModifyGeneralConfigReq) (
	resp *types.ModifyGeneralConfigResp, err error,
) {
	in := &pb.ModifyGeneralConfigurationReq{}
	if err = utils.Copy(in, req); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy data[%+v] to request, error: %+v",
			req, err,
		)
	}

	_, err = l.svcCtx.ManagerGeneralConfigurationRPC.ModifyGeneralConfiguration(l.ctx, in)
	if err != nil {
		return nil, err
	}

	return &types.ModifyGeneralConfigResp{}, nil
}
