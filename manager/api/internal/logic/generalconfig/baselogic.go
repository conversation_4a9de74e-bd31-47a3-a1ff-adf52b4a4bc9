package generalconfig

import (
	"context"

	"github.com/zeromicro/go-zero/core/logx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"
	usercommon "gitlab.ttyuyin.com/TestDevelopment/qet-backend-middleware/user/common"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/svc"
)

type BaseLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext

	converters []utils.TypeConverter
}

func newBaseLogic(ctx context.Context, svcCtx *svc.ServiceContext) *BaseLogic {
	return &BaseLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,

		converters: []utils.TypeConverter{
			usercommon.StringToUserInfo(ctx, svcCtx.UserRPC.Client(), nil),
		},
	}
}
