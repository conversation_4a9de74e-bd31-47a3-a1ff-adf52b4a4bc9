package generalconfig

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type CreateGeneralConfigLogic struct {
	*BaseLogic
}

func NewCreateGeneralConfigLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CreateGeneralConfigLogic {
	return &CreateGeneralConfigLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *CreateGeneralConfigLogic) CreateGeneralConfig(req *types.CreateGeneralConfigReq) (
	resp *types.CreateGeneralConfigResp, err error,
) {
	in := &pb.CreateGeneralConfigurationReq{}
	if err = utils.Copy(in, req); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy data[%+v] to request, error: %+v",
			req, err,
		)
	}

	out, err := l.svcCtx.ManagerGeneralConfigurationRPC.CreateGeneralConfiguration(l.ctx, in)
	if err != nil {
		return nil, err
	}

	return &types.CreateGeneralConfigResp{ConfigId: out.GetConfiguration().GetConfigId()}, nil
}
