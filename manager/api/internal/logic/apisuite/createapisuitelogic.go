package apisuite

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type CreateApiSuiteLogic struct {
	*BaseLogic
}

func NewCreateApiSuiteLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CreateApiSuiteLogic {
	return &CreateApiSuiteLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *CreateApiSuiteLogic) CreateApiSuite(req *types.CreateApiSuiteReq) (resp *types.CreateApiSuiteResp, err error) {
	in := &pb.CreateApiSuiteReq{}
	if err = utils.Copy(in, req, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy data[%+v] to request, error: %+v", req,
			err,
		)
	}

	out, err := l.svcCtx.ManagerApiSuiteRPC.CreateApiSuite(l.ctx, in)
	if err != nil {
		return nil, err
	}

	return &types.CreateApiSuiteResp{SuiteId: out.GetSuite().GetSuiteId()}, nil
}
