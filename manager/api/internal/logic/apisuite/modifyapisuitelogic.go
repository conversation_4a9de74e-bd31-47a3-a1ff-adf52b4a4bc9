package apisuite

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type ModifyApiSuiteLogic struct {
	*BaseLogic
}

func NewModifyApiSuiteLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ModifyApiSuiteLogic {
	return &ModifyApiSuiteLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *ModifyApiSuiteLogic) ModifyApiSuite(req *types.ModifyApiSuiteReq) (resp *types.ModifyApiSuiteResp, err error) {
	in := &pb.ModifyApiSuiteReq{}
	if err = utils.Copy(in, req, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy data[%+v] to request, error: %+v",
			req, err,
		)
	}

	_, err = l.svcCtx.ManagerApiSuiteRPC.ModifyApiSuite(l.ctx, in)
	if err != nil {
		return nil, err
	}

	return &types.ModifyApiSuiteResp{}, nil
}
