package apisuite

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type SearchCaseInApiSuiteLogic struct {
	*BaseLogic
}

// search cases in api suite
func NewSearchCaseInApiSuiteLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SearchCaseInApiSuiteLogic {
	return &SearchCaseInApiSuiteLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *SearchCaseInApiSuiteLogic) SearchCaseInApiSuite(req *types.SearchCaseInApiSuiteReq) (
	resp *types.SearchCaseInApiSuiteResp, err error,
) {
	in := &pb.SearchCaseInApiSuiteReq{}
	if err = utils.Copy(in, req, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy data[%+v] to request, error: %+v",
			req, err,
		)
	}

	out, err := l.svcCtx.ManagerApiSuiteRPC.SearchCaseInApiSuite(l.ctx, in)
	if err != nil {
		return nil, err
	}

	// avoid returning null to the front end
	resp = &types.SearchCaseInApiSuiteResp{Items: []*types.SearchCaseInApiSuiteItem{}}
	if err = utils.Copy(resp, out, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy data[%+v] to response, error: %+v",
			out, err,
		)
	}

	return
}
