package apisuite

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type ModifyCaseListOfApiSuiteLogic struct {
	*BaseLogic
}

// add cases to api suite or remove cases from api suite
func NewModifyCaseListOfApiSuiteLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ModifyCaseListOfApiSuiteLogic {
	return &ModifyCaseListOfApiSuiteLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *ModifyCaseListOfApiSuiteLogic) ModifyCaseListOfApiSuite(req *types.ModifyCaseListOfApiSuiteReq) (
	resp *types.ModifyCaseListOfApiSuiteResp, err error,
) {
	if req.OperationType == common.ConstListOperatorTypeAdd {
		in := &pb.AddCaseToApiSuiteReq{}
		if err = utils.Copy(in, req, l.converters...); err != nil {
			return nil, errors.Wrapf(
				errorx.Err(errorx.CopyToStructFailure, err.Error()),
				"failed to copy data[%+v] to request, error: %+v",
				req, err,
			)
		}

		_, err = l.svcCtx.ManagerApiSuiteRPC.AddCaseToApiSuite(l.ctx, in)
	} else if req.OperationType == common.ConstListOperatorTypeRemove {
		in := &pb.RemoveCaseFromApiSuiteReq{}
		if err = utils.Copy(in, req, l.converters...); err != nil {
			return nil, errors.Wrapf(
				errorx.Err(errorx.CopyToStructFailure, err.Error()),
				"failed to copy data[%+v] to request, error: %+v",
				req, err,
			)
		}

		_, err = l.svcCtx.ManagerApiSuiteRPC.RemoveCaseFromApiSuite(l.ctx, in)
	} else {
		err = errorx.Errorf(
			errorx.DoesNotSupport, "the operator type[%s] doesn't support", req.OperationType,
		)
	}

	if err != nil {
		return nil, err
	}

	return &types.ModifyCaseListOfApiSuiteResp{}, nil
}
