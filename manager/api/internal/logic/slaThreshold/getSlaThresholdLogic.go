package slaThreshold

import (
	"context"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	commonpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
	commontypes "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type GetSlaThresholdLogic struct {
	*BaseLogic
}

// 获取SLA阈值配置
func NewGetSlaThresholdLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetSlaThresholdLogic {
	return &GetSlaThresholdLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *GetSlaThresholdLogic) GetSlaThreshold(req *types.GetSlaThresholdReq) (resp *types.GetSlaThresholdResp, err error) {
	out, err := l.svcCtx.ManagerSlaThresholdRPC.GetSlaThreshold(l.ctx, &pb.GetSlaThresholdReq{
		ProjectId: req.ProjectId,
	})
	if err != nil {
		return nil, err
	}

	resp = &types.GetSlaThresholdResp{
		IosRelease:     &commontypes.SLABaseLine{},
		AndroidRelease: &commontypes.SLABaseLine{},
		AndroidTesting: &commontypes.SLABaseLine{},
	}
	for _, threshold := range out.GetThresholds() {
		splitThreshold(threshold, resp)
	}

	return resp, nil
}

func splitThreshold(slaThreshold *pb.SlaThreshold, resp *types.GetSlaThresholdResp) {
	if slaThreshold == nil || resp == nil {
		return
	}

	switch slaThreshold.GetPlatformType() {
	case commonpb.PlatformType_IOS:
		switch slaThreshold.GetBranchType() {
		case commonpb.BranchType_BranchType_RELEASE:
			setBaseLine(slaThreshold, resp.IosRelease)
		}
	case commonpb.PlatformType_ANDROID:
		switch slaThreshold.GetBranchType() {
		case commonpb.BranchType_BranchType_RELEASE:
			setBaseLine(slaThreshold, resp.AndroidRelease)
		case commonpb.BranchType_BranchType_TESTING:
			setBaseLine(slaThreshold, resp.AndroidTesting)
		}
	}
}

func setBaseLine(slaThreshold *pb.SlaThreshold, slaBaseLine *commontypes.SLABaseLine) {
	if slaThreshold == nil || slaBaseLine == nil {
		return
	}

	value := slaThreshold.GetValue()
	switch constants.SlaThresholdName(slaThreshold.GetName()) {
	case constants.SLA_Threshold_Finish_Launched:
		slaBaseLine.FinishLaunchedLine = value
	case constants.SLA_Threshold_Auto_Login:
		slaBaseLine.AutoLoginLine = value
	case constants.SLA_Threshold_New_Home_Page:
		slaBaseLine.NewHomePageLine = value
	}
}
