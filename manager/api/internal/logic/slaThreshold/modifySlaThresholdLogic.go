package slaThreshold

import (
	"context"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	commonpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type ModifySlaThresholdLogic struct {
	*BaseLogic
}

// 编辑SLA阈值配置
func NewModifySlaThresholdLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ModifySlaThresholdLogic {
	return &ModifySlaThresholdLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *ModifySlaThresholdLogic) ModifySlaThreshold(req *types.ModifySlaThresholdReq) (resp *types.ModifySlaThresholdResp, err error) {
	_, err = l.svcCtx.ManagerSlaThresholdRPC.ModifySlaThreshold(l.ctx, &pb.ModifySlaThresholdReq{
		Thresholds: makeThresholds(req),
	})
	if err != nil {
		return nil, err
	}

	return &types.ModifySlaThresholdResp{}, nil
}

func makeThresholds(req *types.ModifySlaThresholdReq) []*pb.SlaThreshold {
	var (
		projectID = req.ProjectId

		thresholds []*pb.SlaThreshold
	)

	if req.IosRelease != nil {
		kv := map[constants.SlaThresholdName]int64{
			constants.SLA_Threshold_Finish_Launched: req.IosRelease.FinishLaunchedLine,
			constants.SLA_Threshold_Auto_Login:      req.IosRelease.AutoLoginLine,
			constants.SLA_Threshold_New_Home_Page:   req.IosRelease.NewHomePageLine,
		}
		for key, value := range kv {
			thresholds = append(thresholds, newThreshold(
				projectID, commonpb.PlatformType_IOS, commonpb.BranchType_BranchType_RELEASE, key, "", value,
			))
		}
	}

	if req.AndroidRelease != nil {
		kv := map[constants.SlaThresholdName]int64{
			constants.SLA_Threshold_Finish_Launched: req.AndroidRelease.FinishLaunchedLine,
			constants.SLA_Threshold_Auto_Login:      req.AndroidRelease.AutoLoginLine,
			constants.SLA_Threshold_New_Home_Page:   req.AndroidRelease.NewHomePageLine,
		}
		for key, value := range kv {
			thresholds = append(thresholds, newThreshold(
				projectID, commonpb.PlatformType_ANDROID, commonpb.BranchType_BranchType_RELEASE, key, "", value,
			))
		}
	}

	if req.AndroidTesting != nil {
		kv := map[constants.SlaThresholdName]int64{
			constants.SLA_Threshold_Finish_Launched: req.AndroidTesting.FinishLaunchedLine,
			constants.SLA_Threshold_Auto_Login:      req.AndroidTesting.AutoLoginLine,
			constants.SLA_Threshold_New_Home_Page:   req.AndroidTesting.NewHomePageLine,
		}
		for key, value := range kv {
			thresholds = append(thresholds, newThreshold(
				projectID, commonpb.PlatformType_ANDROID, commonpb.BranchType_BranchType_TESTING, key, "", value,
			))
		}
	}

	return thresholds
}

func newThreshold(
	pid string, pt commonpb.PlatformType, bt commonpb.BranchType, name constants.SlaThresholdName, unit string, value int64,
) *pb.SlaThreshold {
	return &pb.SlaThreshold{
		ProjectId:    pid,
		PlatformType: pt,
		BranchType:   bt,
		Name:         string(name),
		Unit:         unit,
		Value:        value,
	}
}
