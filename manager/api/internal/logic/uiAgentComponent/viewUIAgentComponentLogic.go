package uiAgentComponent

import (
	"context"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	commontypes "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type ViewUIAgentComponentLogic struct {
	*BaseLogic
}

func NewViewUIAgentComponentLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ViewUIAgentComponentLogic {
	return &ViewUIAgentComponentLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *ViewUIAgentComponentLogic) ViewUIAgentComponent(req *types.ViewUIAgentComponentReq) (
	resp *types.ViewUIAgentComponentResp, err error,
) {
	in := &pb.ViewUIAgentComponentReq{}
	if err = utils.Copy(in, req, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy data to request, data: %s, error: %+v",
			jsonx.MarshalIgnoreError(req), err,
		)
	}

	out, err := l.svcCtx.ManagerUIAgentComponentRPC.ViewUIAgentComponent(l.ctx, in)
	if err != nil {
		return nil, err
	}

	resp = &types.ViewUIAgentComponentResp{
		UIAgentComponent: &types.UIAgentComponent{
			Steps:     []*commontypes.UIAgentComponentStep{},
			Variables: []*commontypes.KeyValuePair{},
		},
	}
	if err = utils.Copy(resp, out.GetComponent(), l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy data to response, data: %s, error: %+v",
			jsonx.MarshalIgnoreError(out.GetComponent()), err,
		)
	}

	return resp, nil
}
