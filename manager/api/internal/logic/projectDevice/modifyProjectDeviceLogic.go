package projectDevice

import (
	"context"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type ModifyProjectDeviceLogic struct {
	*BaseLogic
}

func NewModifyProjectDeviceLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ModifyProjectDeviceLogic {
	return &ModifyProjectDeviceLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *ModifyProjectDeviceLogic) ModifyProjectDevice(req *types.ModifyProjectDeviceReq) (
	resp *types.ModifyProjectDeviceResp, err error,
) {
	in := &pb.ModifyProjectDeviceReq{}
	if err = utils.Copy(in, req, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy data to request, data: %s, error: %+v",
			jsonx.MarshalIgnoreError(req), err,
		)
	}

	out, err := l.svcCtx.ManagerProjectDeviceRPC.ModifyProjectDevice(l.ctx, in)
	if err != nil {
		return nil, err
	}

	resp = &types.ModifyProjectDeviceResp{
		CreateItems:       make([]*types.BindDevice, 0, len(out.GetCreateItems())),
		UpdateItems:       make([]*types.BindDevice, 0, len(out.GetUpdateItems())),
		DeleteItems:       make([]*types.BindDevice, 0, len(out.GetDeleteItems())),
		IgnoreCreateItems: make([]*types.BindDevice, 0, len(out.GetIgnoreCreateItems())),
		IgnoreUpdateItems: make([]*types.DeviceRelationship, 0, len(out.GetIgnoreUpdateItems())),
		IgnoreDeleteItems: make([]*types.DeviceRelationship, 0, len(out.GetIgnoreDeleteItems())),
	}
	if err = utils.Copy(resp, out, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy data to response, data: %s, error: %+v",
			jsonx.MarshalIgnoreError(out), err,
		)
	}

	return resp, nil
}
