package projectDevice

import (
	"context"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type SearchProjectDeviceReferenceLogic struct {
	*BaseLogic
}

func NewSearchProjectDeviceReferenceLogic(
	ctx context.Context, svcCtx *svc.ServiceContext,
) *SearchProjectDeviceReferenceLogic {
	return &SearchProjectDeviceReferenceLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *SearchProjectDeviceReferenceLogic) SearchProjectDeviceReference(req *types.SearchProjectDeviceReferenceReq) (
	resp *types.SearchProjectDeviceReferenceResp, err error,
) {
	in := &pb.SearchProjectDeviceReferenceReq{}
	if err = utils.Copy(in, req, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy data to request, data: %s, error: %+v",
			jsonx.MarshalIgnoreError(req), err,
		)
	}

	out, err := l.svcCtx.ManagerProjectDeviceRPC.SearchProjectDeviceReference(l.ctx, in)
	if err != nil {
		return nil, err
	}

	// avoid returning null to the front end
	resp = &types.SearchProjectDeviceReferenceResp{Items: []*types.SearchProjectDeviceReferenceItem{}}
	if err = utils.Copy(resp, out, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy data to response, data: %s, error: %+v",
			protobuf.MarshalJSONIgnoreError(out), err,
		)
	}

	return resp, nil
}
