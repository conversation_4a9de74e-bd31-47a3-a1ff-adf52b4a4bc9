package uiAgentImage

import (
	"context"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
)

type DownloadUIAgentImageLogic struct {
	*BaseLogic
}

func NewDownloadUIAgentImageLogic(ctx context.Context, svcCtx *svc.ServiceContext) *DownloadUIAgentImageLogic {
	return &DownloadUIAgentImageLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *DownloadUIAgentImageLogic) DownloadUIAgentImage(req *types.DownloadUIAgentImageReq) (
	filename, filepath string, err error,
) {
	// validate the project_id in req
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, req.ProjectId); err != nil {
		return
	}

	// validate the image_id in req
	image, err := model.CheckUIAgentImageByImageID(l.ctx, l.svcCtx.UIAgentImageModel, req.ProjectId, req.ImageId)
	if err != nil {
		return
	}

	filename = image.Name + image.Extension
	filepath = image.Path

	return
}
