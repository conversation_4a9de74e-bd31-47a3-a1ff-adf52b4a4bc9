package uiAgentImage

import (
	"context"
	"database/sql"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/logx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/userinfo"
	qetutils "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/utils"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/svc"
)

type BaseLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext

	currentUser *userinfo.UserInfo

	converters []qetutils.TypeConverter
}

func newBaseLogic(ctx context.Context, svcCtx *svc.ServiceContext) *BaseLogic {
	return &BaseLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,

		currentUser: userinfo.FromContext(ctx),

		converters: []qetutils.TypeConverter{},
	}
}

func (l *BaseLogic) generateImageID(projectID string) (string, error) {
	g := qetutils.NewUniqueIdGenerator(
		qetutils.WithGenerateFunc(utils.GenUIAgentImageID), qetutils.WithIsUniqueFunc(
			func(id string) bool {
				r, err := l.svcCtx.UIAgentImageModel.FindOneByProjectIdImageId(l.ctx, projectID, id)
				if errors.Is(err, sql.ErrNoRows) || r == nil {
					return true
				}
				return false
			},
		),
	)
	imageID := g.Next()
	if imageID == "" {
		return "", errorx.Err(
			errorx.GenerateUniqueIdFailure, "failed to generate ui agent component image id, please try it later",
		)
	}

	return imageID, nil
}
