package perfPlan

import (
	"context"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type ViewPerfPlanLogic struct {
	*BaseLogic
}

func NewViewPerfPlanLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ViewPerfPlanLogic {
	return &ViewPerfPlanLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *ViewPerfPlanLogic) ViewPerfPlan(req *types.ViewPerfPlanReq) (resp *types.ViewPerfPlanResp, err error) {
	in := &pb.ViewPerfPlanReq{}
	if err = utils.Copy(in, req, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy data to request, data: %s, error: %+v",
			jsonx.MarshalIgnoreError(req), err,
		)
	}

	out, err := l.svcCtx.ManagerPerfPlanRPC.ViewPerfPlan(l.ctx, in)
	if err != nil {
		return nil, err
	}

	resp = &types.ViewPerfPlanResp{PerfPlan: &types.PerfPlan{}}
	if err = utils.Copy(resp.PerfPlan, out.GetPlan(), l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy data to response, data: %s, error: %+v",
			protobuf.MarshalJSONIgnoreError(out), err,
		)
	}

	return resp, nil
}
