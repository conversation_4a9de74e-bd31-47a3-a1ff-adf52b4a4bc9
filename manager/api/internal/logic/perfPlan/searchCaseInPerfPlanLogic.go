package perfPlan

import (
	"context"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type SearchCaseInPerfPlanLogic struct {
	*BaseLogic
}

func NewSearchCaseInPerfPlanLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SearchCaseInPerfPlanLogic {
	return &SearchCaseInPerfPlanLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *SearchCaseInPerfPlanLogic) SearchCaseInPerfPlan(req *types.SearchCaseInPerfPlanReq) (
	resp *types.SearchCaseInPerfPlanResp, err error,
) {
	in := &pb.SearchCaseInPerfPlanReq{}
	if err = utils.Copy(in, req, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy data to request, data: %s, error: %+v",
			jsonx.MarshalIgnoreError(req), err,
		)
	}

	out, err := l.svcCtx.ManagerPerfPlanRPC.SearchCaseInPerfPlan(l.ctx, in)
	if err != nil {
		return nil, err
	}

	// avoid returning null to the front end
	resp = &types.SearchCaseInPerfPlanResp{Items: []*types.SearchCaseInPerfPlanItem{}}
	if err = utils.Copy(resp, out, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy data to response, data: %s, error: %+v",
			protobuf.MarshalJSONIgnoreError(out), err,
		)
	}

	return resp, nil
}
