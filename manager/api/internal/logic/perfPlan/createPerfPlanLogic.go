package perfPlan

import (
	"context"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type CreatePerfPlanLogic struct {
	*BaseLogic
}

func NewCreatePerfPlanLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CreatePerfPlanLogic {
	return &CreatePerfPlanLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *CreatePerfPlanLogic) CreatePerfPlan(req *types.CreatePerfPlanReq) (resp *types.CreatePerfPlanResp, err error) {
	in := &pb.CreatePerfPlanReq{}

	if err = utils.Copy(in, req, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy data to request, data: %s, error: %+v",
			jsonx.MarshalIgnoreError(req), err,
		)
	}

	out, err := l.svcCtx.ManagerPerfPlanRPC.CreatePerfPlan(l.ctx, in)
	if err != nil {
		return nil, err
	}

	return &types.CreatePerfPlanResp{PlanId: out.GetPlan().GetPlanId()}, nil
}
