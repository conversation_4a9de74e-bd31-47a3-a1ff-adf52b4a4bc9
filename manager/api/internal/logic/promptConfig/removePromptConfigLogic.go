package promptConfig

import (
	"context"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type RemovePromptConfigLogic struct {
	*BaseLogic
}

func NewRemovePromptConfigLogic(ctx context.Context, svcCtx *svc.ServiceContext) *RemovePromptConfigLogic {
	return &RemovePromptConfigLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *RemovePromptConfigLogic) RemovePromptConfig(req *types.RemovePromptConfigReq) (
	resp *types.RemovePromptConfigResp, err error,
) {
	in := &pb.RemovePromptConfigurationReq{}
	if err = utils.Copy(in, req, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy data to request, data: %s, error: %+v",
			jsonx.MarshalIgnoreError(req), err,
		)
	}

	_, err = l.svcCtx.ManagerPromptConfigurationRPC.RemovePromptConfiguration(l.ctx, in)
	if err != nil {
		return nil, err
	}

	return &types.RemovePromptConfigResp{}, nil
}
