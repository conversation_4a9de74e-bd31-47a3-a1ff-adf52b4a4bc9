package promptConfig

import (
	"context"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type SearchPromptConfigLogic struct {
	*BaseLogic
}

func NewSearchPromptConfigLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SearchPromptConfigLogic {
	return &SearchPromptConfigLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *SearchPromptConfigLogic) SearchPromptConfig(req *types.SearchPromptConfigReq) (
	resp *types.SearchPromptConfigResp, err error,
) {
	in := &pb.SearchPromptConfigurationReq{}
	if err = utils.Copy(in, req, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy data to request, data: %s, error: %+v",
			jsonx.MarshalIgnoreError(req), err,
		)
	}

	out, err := l.svcCtx.ManagerPromptConfigurationRPC.SearchPromptConfiguration(l.ctx, in)
	if err != nil {
		return nil, err
	}

	// avoid returning null to the front end
	resp = &types.SearchPromptConfigResp{Items: []*types.PromptConfiguration{}}
	if err = utils.Copy(resp, out, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy data to response, data: %s, error: %+v",
			protobuf.MarshalJSONIgnoreError(out), err,
		)
	}

	return resp, nil
}
