package promptConfig

import (
	"context"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type CreatePromptConfigLogic struct {
	*BaseLogic
}

func NewCreatePromptConfigLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CreatePromptConfigLogic {
	return &CreatePromptConfigLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *CreatePromptConfigLogic) CreatePromptConfig(req *types.CreatePromptConfigReq) (
	resp *types.CreatePromptConfigResp, err error,
) {
	in := &pb.CreatePromptConfigurationReq{}
	if err = utils.Copy(in, req, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy data to request, data: %s, error: %+v",
			jsonx.MarshalIgnoreError(req), err,
		)
	}

	out, err := l.svcCtx.ManagerPromptConfigurationRPC.CreatePromptConfiguration(l.ctx, in)
	if err != nil {
		return nil, err
	}

	return &types.CreatePromptConfigResp{ConfigId: out.GetConfiguration().GetConfigId()}, nil
}
