package perfCase

import (
	"database/sql"
	"math"
	"time"

	"github.com/zeromicro/go-zero/core/jsonx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/cache"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/calculate"
	commonpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
	commontypes "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/types"
	commonutils "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/utils"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
)

func calculateNumberOfVirtualUsers(serial, parallel []*commonpb.PerfCaseStep) int64 {
	var (
		cost, svu, pvu int64
		rps            int64 = math.MaxInt64
		second               = int64(time.Second)
	)

	for _, step := range serial {
		cost += int64(calculate.ConstDefaultResponseTime + cache.Parse(step.GetSleep()))
		if rps > step.GetRateLimit().GetTargetRps() {
			rps = step.GetRateLimit().GetTargetRps()
		}
	}
	svu = (rps*cost + second - 1) / second // ceil(rps / (second / cost)) => ceil(rps * cost / second)

	for _, step := range parallel {
		cost = int64(calculate.ConstDefaultResponseTime + cache.Parse(step.GetSleep()))
		vu := (step.GetRateLimit().GetTargetRps()*cost + second - 1) / second // ceil(rps / (second / cost)) => ceil(rps * cost / second)
		if pvu < vu {
			pvu = vu
		}
	}

	if svu > pvu {
		return svu
	}

	return pvu
}

func calculateLoadGeneratorResource(vus int64, load commontypes.LoadGenerator) *commonpb.LoadGenerator {
	lg := &commonpb.LoadGenerator{
		NumberOfLg:       load.NumberOfLg,
		RequestsOfCpu:    load.RequestsOfCpu,
		RequestsOfMemory: load.RequestsOfMemory,
		LimitsOfCpu:      load.LimitsOfCpu,
		LimitsOfMemory:   load.LimitsOfMemory,
	}

	if lg.GetNumberOfLg() == 0 {
		// calculate the number of load generators by the number of virtual users
		lg.NumberOfLg = uint32(math.Ceil(float64(vus) / float64(calculate.ConstDefaultVUPerLG)))
	}
	if lg.GetRequestsOfCpu() == "" {
		lg.RequestsOfCpu = calculate.ConstDefaultRequestsOfCPU
	}
	if lg.GetRequestsOfMemory() == "" {
		lg.RequestsOfMemory = calculate.ConstDefaultRequestsOfMemory
	}
	if lg.GetLimitsOfCpu() == "" {
		lg.LimitsOfCpu = calculate.ConstDefaultLimitsOfCPU
	}
	if lg.GetLimitsOfMemory() == "" {
		lg.LimitsOfMemory = calculate.ConstDefaultLimitsOfMemory
	}

	return lg
}

func generatePerfCaseStepToChannel(
	source chan<- *model.PerfCaseStep, perfCase *model.PerfCase, stepType common.PerfCaseStepType,
	steps []*commonpb.PerfCaseStep,
) {
	for _, step := range steps {
		headers := jsonx.MarshalToStringIgnoreError(step.GetHeaders())                    // map[string]string
		exports := protobuf.MarshalJSONWithMessagesToStringIgnoreError(step.GetExports()) // []*PerfCaseStep_Export
		source <- &model.PerfCaseStep{
			ProjectId:    perfCase.ProjectId,
			CaseId:       perfCase.CaseId,
			StepId:       commonutils.GenPerfCaseStepID(),
			Type:         string(stepType),
			Name:         step.GetName(),
			TargetRps:    step.GetRateLimit().GetTargetRps(),
			InitialRps:   step.GetRateLimit().GetInitialRps(),
			StepHeight:   step.GetRateLimit().GetStepHeight(),
			StepDuration: step.GetRateLimit().GetStepDuration(),
			Url:          step.GetUrl(),
			Method:       step.GetMethod(),
			Headers: sql.NullString{
				String: headers,
				Valid:  headers != "",
			},
			Body: step.GetBody(),
			Exports: sql.NullString{
				String: exports,
				Valid:  exports != "",
			},
			Sleep:     step.GetSleep(),
			State:     int64(constants.EnableStatus),
			CreatedBy: perfCase.CreatedBy,
			UpdatedBy: perfCase.UpdatedBy,
			CreatedAt: perfCase.CreatedAt,
			UpdatedAt: perfCase.UpdatedAt,
		}
	}
}
