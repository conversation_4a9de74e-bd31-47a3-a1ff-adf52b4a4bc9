package perfCase

import (
	"context"
	"database/sql"
	"encoding/hex"
	"fmt"
	"hash"
	"net/http"
	"os"
	"path/filepath"
	"strings"
	"time"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"
	"github.com/zeromicro/go-zero/core/stores/sqlx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/caller"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"

	commonpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
	commontypes "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/types"
	commonutils "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/utils"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
)

type ModifyPerfCaseLogic struct {
	*BaseLogic
}

func NewModifyPerfCaseLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ModifyPerfCaseLogic {
	return &ModifyPerfCaseLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *ModifyPerfCaseLogic) ModifyPerfCase(
	req *types.ModifyPerfCaseReq, r *http.Request,
) (resp *types.ModifyPerfCaseResp, err error) {
	// validate the project_id in req
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, req.ProjectId); err != nil {
		return nil, err
	}

	perfCase, err := model.CheckPerfCaseByCaseID(l.ctx, l.svcCtx.PerfCaseModel, req.ProjectId, req.CaseId)
	if err != nil {
		return nil, err
	}

	key := fmt.Sprintf(
		"%s:%s:%s", common.ConstLockPerfCaseProjectIDCaseIDPrefix, req.ProjectId, req.CaseId,
	)
	if err = caller.LockDo(
		l.svcCtx.Redis, key, func() error {
			return l.modifyByRequest(req, r, perfCase)
		},
	); err != nil {
		return nil, err
	}

	return &types.ModifyPerfCaseResp{}, nil
}

func (l *ModifyPerfCaseLogic) modifyByRequest(
	req *types.ModifyPerfCaseReq, r *http.Request, origin *model.PerfCase,
) (err error) {
	perfCase := &model.PerfCase{
		Id:           origin.Id,
		ProjectId:    origin.ProjectId,
		CaseId:       origin.CaseId,
		Name:         origin.Name,
		Description:  origin.Description,
		Extension:    origin.Extension,
		Hash:         origin.Hash,
		Size:         origin.Size,
		Path:         origin.Path,
		TargetRps:    origin.TargetRps,
		InitialRps:   origin.InitialRps,
		StepHeight:   origin.StepHeight,
		StepDuration: origin.StepDuration,
		PerfDataId:   req.PerfDataId,
		State:        origin.State,
		MaintainedBy: origin.MaintainedBy,
		CreatedBy:    origin.CreatedBy,
		UpdatedBy:    l.currentUser.Account,
		CreatedAt:    origin.CreatedAt,
		UpdatedAt:    time.Now(),
	}

	// get the specified number of virtual users from the request
	vus := int64(req.NumberOfVu)
	if req.PerfDataId != "" {
		if perfData, err := model.CheckPerfDataByDataID(
			l.ctx, l.svcCtx.PerfDataModel, req.ProjectId, req.PerfDataId,
		); err != nil {
			return err
		} else {
			// get the number of virtual users from the perf data
			vus = perfData.NumberOfVu
		}
	}

	// parse the multipart form in req
	if err = r.ParseMultipartForm(maxFileSize); err != nil {
		return errors.Wrapf(
			errorx.Err(errorx.ParseParamError, err.Error()),
			"failed to parse multipart form, error: %+v",
			err,
		)
	}

	file, fileHeader, err := r.FormFile(formFileKey)
	defer func() {
		if file != nil {
			_ = file.Close()
		}
	}()
	if err != nil && !errors.Is(err, http.ErrMissingFile) {
		return errors.Wrapf(
			errorx.Err(errorx.ParseParamError, err.Error()),
			"failed to get the %q parameter, error: %+v",
			formFileKey, err,
		)
	}

	var data *commonpb.PerfCaseContent
	if !errors.Is(err, http.ErrMissingFile) {
		// upload a new perf case file
		var (
			md5Hash hash.Hash
			size    int64
		)

		// update the value of `name`, `description`, `extension` field of perf case
		fileExt := filepath.Ext(fileHeader.Filename)
		perfCase.Name = strings.TrimSuffix(fileHeader.Filename, fileExt)
		perfCase.Description = sql.NullString{
			String: fileHeader.Filename,
			Valid:  true,
		}
		perfCase.Extension = fileExt

		// save the file to the local file system
		localFilePath := filepath.Join(l.svcCtx.Config.PVCPath, subDir, commonutils.GenFileID())
		// save the file to the other file system, e.g. HS NFS
		otherFilePath := filepath.Join(l.svcCtx.Config.PVCNFSPath, subDir, commonutils.GenFileID())
		defer func() {
			if err != nil {
				_ = os.Remove(localFilePath)
				_ = os.Remove(otherFilePath)
			} else {
				_ = os.Remove(perfCase.Path)
				_ = os.Rename(localFilePath, perfCase.Path)
			}
		}()
		md5Hash, size, err = logic.SaveFile(file, localFilePath)
		if err != nil {
			return err
		} else if size != fileHeader.Size {
			return errorx.Errorf(
				errorx.FileOperationFailure,
				"the size of file is mismatch, filename: %s, expected: %d, actual: %d",
				fileHeader.Filename, fileHeader.Size, size,
			)
		}

		// 双写
		err = commonutils.CopyFile(localFilePath, otherFilePath)
		if err != nil {
			// ignore error
			l.Errorf(
				"failed to save the perf case file to path, filename: %s, path: %s, error: %+v",
				fileHeader.Filename, otherFilePath, err,
			)
		}

		// update the value of `hash` and `size` field of perf case
		perfCase.Hash = hex.EncodeToString(md5Hash.Sum(nil))
		perfCase.Size = size
	} else {
		// no new perf case file is updated
		file, err = os.Open(origin.Path)
		if err != nil {
			return errors.Wrapf(
				errorx.Err(errorx.FileOperationFailure, err.Error()),
				"failed to open the file of perf case, project_id: %s, case_id: %s, path: %s",
				req.ProjectId, req.CaseId, origin.Path,
			)
		}
	}

	// get the content from the perf case file
	data, err = commonutils.GetPerfCaseFromReader(file)
	if err != nil {
		return err
	}

	// calculate the maximum RPS by serial steps and parallel steps
	rl, err := commonutils.GetMaxRPSFromSerialStepsAndParallelSteps(data.GetSerialSteps(), data.GetParallelSteps())
	if err != nil {
		return err
	}

	// automatically calculate the number of virtual users
	if req.PerfDataId == "" && vus == 0 {
		vus = calculateNumberOfVirtualUsers(data.GetSerialSteps(), data.GetParallelSteps())
	}

	// update the value of `target_rps`, `initial_rps`, `step_height`, `step_duration` field of perf case
	perfCase.TargetRps = rl.GetTargetRps()
	perfCase.InitialRps = rl.GetInitialRps()
	perfCase.StepHeight = rl.GetStepHeight()
	perfCase.StepDuration = rl.GetStepDuration()

	// update the value of `number_of_vu` field of perf case
	perfCase.NumberOfVu = vus

	// update the value of `number_of_lg`, `requests_of_cpu`, `requests_of_memory`, `limits_of_cpu`, `limits_of_memory` field of perf case
	lg := calculateLoadGeneratorResource(
		vus, commontypes.LoadGenerator{
			NumberOfLg:       req.NumberOfLg,
			RequestsOfCpu:    req.RequestsOfCpu,
			RequestsOfMemory: req.RequestsOfMemory,
			LimitsOfCpu:      req.LimitsOfCpu,
			LimitsOfMemory:   req.LimitsOfMemory,
		},
	)
	perfCase.NumberOfLg = int64(lg.GetNumberOfLg())
	perfCase.RequestsOfCpu = lg.GetRequestsOfCpu()
	perfCase.RequestsOfMemory = lg.GetRequestsOfMemory()
	perfCase.LimitsOfCpu = lg.GetLimitsOfCpu()
	perfCase.LimitsOfMemory = lg.GetLimitsOfMemory()

	return l.modify(perfCase, data)
}

func (l *ModifyPerfCaseLogic) modify(perfCase *model.PerfCase, content *commonpb.PerfCaseContent) error {
	return l.svcCtx.PerfCaseModel.Trans(
		l.ctx, func(context context.Context, session sqlx.Session) error {
			if content != nil {
				// delete perf case steps
				if _, err := l.svcCtx.PerfCaseStepModel.DeleteByCaseID(
					context, session, perfCase.ProjectId, perfCase.CaseId,
				); err != nil {
					return errors.Wrapf(
						errorx.Err(errorx.DBError, err.Error()),
						"failed to remove perf case steps, project_id: %s, case_id: %s, error: %+v",
						perfCase.ProjectId, perfCase.CaseId, err,
					)
				}

				// create perf case steps
				if err := l.createPerfCaseSteps(context, session, perfCase, content); err != nil {
					return err
				}
			}

			// update perf case
			if _, err := l.svcCtx.PerfCaseModel.Update(context, session, perfCase); err != nil {
				return errors.Wrapf(
					errorx.Err(errorx.DBError, err.Error()),
					"failed to update values to table, table: %s, values: %s, error: %+v",
					l.svcCtx.PerfCaseModel.Table(), jsonx.MarshalIgnoreError(perfCase), err,
				)
			}

			return nil
		},
	)
}
