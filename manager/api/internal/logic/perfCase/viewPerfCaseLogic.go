package perfCase

import (
	"context"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type ViewPerfCaseLogic struct {
	*BaseLogic
}

func NewViewPerfCaseLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ViewPerfCaseLogic {
	return &ViewPerfCaseLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *ViewPerfCaseLogic) ViewPerfCase(req *types.ViewPerfCaseReq) (resp *types.ViewPerfCaseResp, err error) {
	in := &pb.ViewPerfCaseReq{}
	if err = utils.Copy(in, req, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy data to request, data: %s, error: %+v",
			jsonx.MarshalIgnoreError(req), err,
		)
	}

	out, err := l.svcCtx.ManagerPerfCaseRPC.ViewPerfCase(l.ctx, in)
	if err != nil {
		return nil, err
	}

	resp = &types.ViewPerfCaseResp{PerfCase: &types.PerfCase{}}
	if err = utils.Copy(resp.PerfCase, out.GetCase(), l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy data to response, data: %s, error: %+v",
			protobuf.MarshalJSONIgnoreError(out), err,
		)
	}

	return resp, nil
}
