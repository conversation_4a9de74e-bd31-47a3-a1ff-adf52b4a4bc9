package perfCase

import (
	"context"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
)

type DownloadPerfCaseLogic struct {
	*BaseLogic
}

func NewDownloadPerfCaseLogic(ctx context.Context, svcCtx *svc.ServiceContext) *DownloadPerfCaseLogic {
	return &DownloadPerfCaseLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *DownloadPerfCaseLogic) DownloadPerfCase(req *types.DownloadPerfCaseReq) (
	filename, filepath string, err error,
) {
	// validate the project_id in req
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, req.ProjectId); err != nil {
		return
	}

	// validate the case_id in req
	perfCase, err := model.CheckPerfCaseByCaseID(l.ctx, l.svcCtx.PerfCaseModel, req.ProjectId, req.CaseId)
	if err != nil {
		return
	}

	filename = perfCase.Name + perfCase.Extension
	filepath = perfCase.Path

	return
}
