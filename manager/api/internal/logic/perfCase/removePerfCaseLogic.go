package perfCase

import (
	"context"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type RemovePerfCaseLogic struct {
	*BaseLogic
}

func NewRemovePerfCaseLogic(ctx context.Context, svcCtx *svc.ServiceContext) *RemovePerfCaseLogic {
	return &RemovePerfCaseLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *RemovePerfCaseLogic) RemovePerfCase(req *types.RemovePerfCaseReq) (resp *types.RemovePerfCaseResp, err error) {
	in := &pb.RemovePerfCaseReq{}
	if err = utils.Copy(in, req, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy data to request, data: %s, error: %+v",
			jsonx.MarshalIgnoreError(req), err,
		)
	}

	_, err = l.svcCtx.ManagerPerfCaseRPC.RemovePerfCase(l.ctx, in)
	if err != nil {
		return nil, err
	}

	return &types.RemovePerfCaseResp{}, nil
}
