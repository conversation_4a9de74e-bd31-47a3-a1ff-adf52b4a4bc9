package perfCase

import (
	"context"
	"os"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	commontypes "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/types"
	commonutils "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/utils"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
)

type CalculatePerfCaseLogic struct {
	*BaseLogic
}

func NewCalculatePerfCaseLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CalculatePerfCaseLogic {
	return &CalculatePerfCaseLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *CalculatePerfCaseLogic) CalculatePerfCase(req *types.CalculatePerfCaseReq) (
	resp *types.CalculatePerfCaseResp, err error,
) {
	// validate the project_id in req
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, req.ProjectId); err != nil {
		return nil, err
	}

	perfCase, err := model.CheckPerfCaseByCaseID(l.ctx, l.svcCtx.PerfCaseModel, req.ProjectId, req.CaseId)
	if err != nil {
		return nil, err
	}

	// get the specified number of virtual users from the request
	vus := int64(req.NumberOfVu)
	if req.PerfDataId != "" {
		if perfData, err := model.CheckPerfDataByDataID(
			l.ctx, l.svcCtx.PerfDataModel, req.ProjectId, req.PerfDataId,
		); err != nil {
			return nil, err
		} else {
			// get the number of virtual users from the perf data
			vus = perfData.NumberOfVu
		}
	}

	// get the content from the perf case file
	file, err := os.Open(perfCase.Path)
	if err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.FileOperationFailure, err.Error()),
			"failed to open the file of perf case, project_id: %s, case_id: %s, path: %s",
			req.ProjectId, req.CaseId, perfCase.Path,
		)
	}
	data, err := commonutils.GetPerfCaseFromReader(file)
	if err != nil {
		return nil, err
	}

	// calculate the maximum RPS by serial steps and parallel steps
	rl, err := commonutils.GetMaxRPSFromSerialStepsAndParallelSteps(data.GetSerialSteps(), data.GetParallelSteps())
	if err != nil {
		return nil, err
	}

	// automatically calculate the number of virtual users
	if req.PerfDataId == "" && vus == 0 {
		vus = calculateNumberOfVirtualUsers(data.GetSerialSteps(), data.GetParallelSteps())
	}

	// calculate the load generator resource
	lg := calculateLoadGeneratorResource(
		vus, commontypes.LoadGenerator{
			NumberOfLg:       req.NumberOfLg,
			RequestsOfCpu:    req.RequestsOfCpu,
			RequestsOfMemory: req.RequestsOfMemory,
			LimitsOfCpu:      req.LimitsOfCpu,
			LimitsOfMemory:   req.LimitsOfMemory,
		},
	)

	perfCase.TargetRps = rl.GetTargetRps()
	perfCase.InitialRps = rl.GetInitialRps()
	perfCase.StepHeight = rl.GetStepHeight()
	perfCase.StepDuration = rl.GetStepDuration()
	perfCase.PerfDataId = req.PerfDataId
	perfCase.NumberOfVu = vus
	perfCase.NumberOfLg = int64(lg.GetNumberOfLg())
	perfCase.RequestsOfCpu = lg.GetRequestsOfCpu()
	perfCase.RequestsOfMemory = lg.GetRequestsOfMemory()
	perfCase.LimitsOfCpu = lg.GetLimitsOfCpu()
	perfCase.LimitsOfMemory = lg.GetLimitsOfMemory()

	resp = &types.CalculatePerfCaseResp{PerfCase: &types.PerfCase{}}
	if err = utils.Copy(resp.PerfCase, perfCase, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy perf case to response, perf case: %s, error: %+v",
			jsonx.MarshalIgnoreError(perfCase), err,
		)
	}

	return resp, nil
}
