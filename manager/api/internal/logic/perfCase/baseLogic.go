package perfCase

import (
	"context"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/mr"
	"github.com/zeromicro/go-zero/core/stores/sqlx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/userinfo"
	commonutils "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	commonpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/utils"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
)

type BaseLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext

	currentUser *userinfo.UserInfo

	converters []commonutils.TypeConverter
}

func newBaseLogic(ctx context.Context, svcCtx *svc.ServiceContext) *BaseLogic {
	return &BaseLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,

		currentUser: userinfo.FromContext(ctx),

		converters: []commonutils.TypeConverter{},
	}
}

func (l *BaseLogic) generatePerfCaseID(projectID string) (string, error) {
	g := commonutils.NewUniqueIdGenerator(
		commonutils.WithGenerateFunc(utils.GenPerfCaseID), commonutils.WithIsUniqueFunc(
			func(id string) bool {
				r, err := l.svcCtx.PerfCaseModel.FindOneByProjectIdCaseId(l.ctx, projectID, id)
				if errors.Is(err, model.ErrNotFound) || r == nil {
					return true
				}
				return false
			},
		),
	)
	caseID := g.Next()
	if caseID == "" {
		return "", errors.WithStack(
			errorx.Err(
				errorx.GenerateUniqueIdFailure, "failed to generate perf case id, please try it later",
			),
		)
	}

	return caseID, nil
}

func (l *BaseLogic) createPerfCaseSteps(
	ctx context.Context, session sqlx.Session, perfCase *model.PerfCase, content *commonpb.PerfCaseContent,
) error {
	return mr.MapReduceVoid[*model.PerfCaseStep, any](
		func(source chan<- *model.PerfCaseStep) {
			generatePerfCaseStepToChannel(
				source, perfCase, common.ConstPerfCaseStepTypeSetup, content.GetSetupSteps(),
			)
			generatePerfCaseStepToChannel(
				source, perfCase, common.ConstPerfCaseStepTypeSerial, content.GetSerialSteps(),
			)
			generatePerfCaseStepToChannel(
				source, perfCase, common.ConstPerfCaseStepTypeParallel, content.GetParallelSteps(),
			)
			generatePerfCaseStepToChannel(
				source, perfCase, common.ConstPerfCaseStepTypeTeardown, content.GetTeardownSteps(),
			)
		}, func(item *model.PerfCaseStep, writer mr.Writer[any], cancel func(error)) {
			if item == nil {
				return
			}

			var err error
			defer func() {
				if err != nil {
					cancel(err)
				}
			}()

			if _, err = l.svcCtx.PerfCaseStepModel.Insert(ctx, session, item); err != nil {
				err = errors.Wrapf(
					errorx.Err(errorx.DBError, err.Error()),
					"failed to insert values to table, table: %s, values: %s, error: %+v",
					l.svcCtx.PerfCaseStepModel.Table(), jsonx.MarshalIgnoreError(item), err,
				)
				return
			}
		}, func(pipe <-chan any, cancel func(error)) {
		},
	)
}
