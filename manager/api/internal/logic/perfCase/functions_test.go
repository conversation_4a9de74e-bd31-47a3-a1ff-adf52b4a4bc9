package perfCase

import (
	"math"
	"os"
	"testing"
	"time"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/calculate"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/utils"
)

func Test_calculateNumberOfVirtualUsers(t *testing.T) {
	type args struct {
		path string
	}
	tests := []struct {
		name string
		args args
		want int64
	}{
		{
			name: "normal perf case",
			args: args{
				path: "../../../../testdata/perf_case_test.yaml",
			},
			want: 3050,
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				file, err := os.Open(tt.args.path)
				defer func() {
					if file != nil {
						_ = file.Close()
					}
				}()
				if err != nil {
					t.Fatal(err)
				}

				perfCase, err := utils.GetPerfCaseFromReader(file)
				if err != nil {
					t.Fatal(err)
				}

				if got := calculateNumberOfVirtualUsers(
					perfCase.GetSerialSteps(), perfCase.GetParallelSteps(),
				); got != tt.want {
					t.Errorf("calculateNumberOfVirtualUsers() = %v, want %v", got, tt.want)
				}
			},
		)
	}
}

func TestVU(t *testing.T) {
	var rps int64 = 110

	cost := int64(calculate.ConstDefaultResponseTime + 5*time.Second)
	rate1 := int64(time.Second) / cost
	vu1 := (rps*cost + int64(time.Second) - 1) / int64(time.Second)
	t.Logf("vu: %d, rate: %d, cost: %d", vu1, rate1, cost)

	rate2 := float64(time.Second) / float64(cost)
	vu2 := int64(math.Ceil(float64(rps) / rate2))
	t.Logf("vu: %d, rate: %.2f, cost: %d", vu2, rate2, cost)
}
