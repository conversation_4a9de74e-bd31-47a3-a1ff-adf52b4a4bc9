package types

import (
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sqlbuilder/api"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/userinfo"
)

type Notify struct {
	Id           int64                  `json:"id"`
	ProjectId    string                 `json:"project_id"`    // 项目ID
	PlanId       string                 `json:"plan_id"`       // 计划ID
	NotifyId     string                 `json:"notify_id"`     // 通知ID
	NotifyMode   string                 `json:"notify_mode"`   // 通知模式
	NotifyType   string                 `json:"notify_type"`   // 通知类型
	ReceiverName string                 `json:"receiver_name"` // 接收者名称
	Receiver     string                 `json:"receiver"`      // 接收者主体
	CreatedBy    *userinfo.FullUserInfo `json:"created_by"`    // 创建者
	UpdatedBy    *userinfo.FullUserInfo `json:"updated_by"`    // 更新者
	CreatedAt    int64                  `json:"created_at"`    // 创建时间
	UpdatedAt    int64                  `json:"updated_at"`    // 更新时间
}

type ReceiverItems struct {
	ReceiverName string `json:"receiver_name" validate:"required,lte=64" zh:"接收者名称"`
	Receiver     string `json:"receiver" validate:"required,lte=255,url|email|startswith=oc_" zh:"接收者主体"`
}

type CreateNotifyReq struct {
	ProjectId     string           `json:"project_id" validate:"required" zh:"项目ID"`
	PlanId        string           `json:"plan_id" validate:"required" zh:"计划ID"`
	NotifyMode    string           `json:"notify_mode" validate:"required,oneof=ONLY_FALSE_NOTIFY ALWAYS_NOTIFY" zh:"通知模式"`
	NotifyType    string           `json:"notify_type" validate:"required,oneof=EMAIL LARK_GROUP LARK_CHAT" zh:"通知类型"`
	ReceiverInfos []*ReceiverItems `json:"receiver_infos" validate:"required,dive" zh:"接收者信息"`
}

type CreateNotifyResp struct {
	NotifyIds []string `json:"notify_ids"`
}

type RemoveNotifyReq struct {
	ProjectId string   `json:"project_id" validate:"required" zh:"项目ID"`
	PlanId    string   `json:"plan_id" validate:"required" zh:"计划ID"`
	NotifyIds []string `json:"notify_ids" validate:"gt=0" zh:"通知ID列表"`
}

type RemoveNotifyResp struct{}

type ModifyNotifyReq struct {
	ProjectId    string `json:"project_id" validate:"required" zh:"项目ID"`
	NotifyId     string `json:"notify_id" validate:"required" zh:"通知ID"`
	NotifyMode   string `json:"notify_mode" validate:"required,oneof=ONLY_FALSE_NOTIFY ALWAYS_NOTIFY" zh:"通知模式"`
	NotifyType   string `json:"notify_type" validate:"required,oneof=EMAIL LARK_GROUP LARK_CHAT" zh:"通知类型"`
	ReceiverName string `json:"receiver_name" validate:"required,lte=64" zh:"接收者名称"`
	Receiver     string `json:"receiver" validate:"required,lte=255,url|email|startswith=oc_" zh:"接收者主体"`
}

type ModifyNotifyResp struct{}

type SearchNotifyReq struct {
	ProjectId  string           `json:"project_id" validate:"required" zh:"项目ID"`
	PlanId     string           `json:"plan_id" validate:"required" zh:"计划ID"`
	Condition  *api.Condition   `json:"condition,omitempty,optional" zh:"查询条件"`
	Pagination *api.Pagination  `json:"pagination,omitempty,optional" zh:"查询分页"`
	Sort       []*api.SortField `json:"sort,omitempty,optional" zh:"查询排序"`
}

type SearchNotifyResp struct {
	CurrentPage uint64    `json:"current_page"`
	PageSize    uint64    `json:"page_size"`
	TotalCount  uint64    `json:"total_count"`
	TotalPage   uint64    `json:"total_page"`
	Items       []*Notify `json:"items"`
}
