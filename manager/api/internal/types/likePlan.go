package types

type LikePlanReq struct {
	ProjectId string `json:"project_id" validate:"required" zh:"项目ID"`
	PlanId    string `json:"plan_id" validate:"required" zh:"API计划ID"`
	IsLike    bool   `json:"is_like" zh:"是否点赞/收藏"`
	PlanType  int8   `json:"plan_type" zh:"计划类型"` // 计划类型(0缺省,1api,2ui)
}

type LikePlanResp struct{}

type CheckLikePlanReq struct {
	ProjectId string `form:"project_id" validate:"required" zh:"项目ID"`
	PlanId    string `form:"plan_id" validate:"required" zh:"API计划ID"`
	PlanType  int8   `form:"plan_type" zh:"计划类型"` // 计划类型(0api,1ui)
}

type CheckLikePlanResp struct {
	IsLike bool `json:"is_like" validate:"required" zh:"是否点赞/收藏"`
}
