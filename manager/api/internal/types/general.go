package types

import (
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sqlbuilder/api"
)

type SearchByCategoryId struct {
	ProjectId  string           `json:"project_id" validate:"required" zh:"项目ID"`
	CategoryId string           `json:"category_id" validate:"required" zh:"分类ID"`
	Condition  *api.Condition   `json:"condition,omitempty,optional" zh:"查询条件"`
	Pagination *api.Pagination  `json:"pagination,omitempty,optional" zh:"查询分页"`
	Sort       []*api.SortField `json:"sort,omitempty,optional" zh:"查询排序"`
}

// UpdateReference 更新引用，`component_group`和`api_case`都需要用到，因此定义在这里
type UpdateReference struct {
	ProjectId        string    `json:"project_id"`
	ReferenceType    string    `json:"reference_type"`
	ReferenceId      string    `json:"reference_id"`
	ReferenceVersion string    `json:"reference_version"`
	ComponentGroupId string    `json:"component_group_id"`
	Imports          []*Import `json:"imports"`
	Exports          []*Export `json:"exports"`
}
