package types

import (
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sqlbuilder/api"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/userinfo"
)

type CreatePerfLarkMemberReq struct {
	ProjectId string                   `json:"project_id" validate:"required" zh:"项目ID"`
	Items     []*userinfo.FullUserInfo `json:"items"`
}

type CreatePerfLarkMemberResp struct{}

type RemovePerfLarkMemberReq struct {
	ProjectId string `json:"project_id" validate:"required" zh:"项目ID"`
	Account   string `json:"account" validate:"required" zh:"用户名（工号）"`
}

type RemovePerfLarkMemberResp struct{}

type SearchPerfLarkMemberReq struct {
	ProjectId  string           `json:"project_id" validate:"required" zh:"项目ID"`
	Condition  *api.Condition   `json:"condition,omitempty,optional" zh:"查询条件"`
	Pagination *api.Pagination  `json:"pagination,omitempty,optional" zh:"查询分页"`
	Sort       []*api.SortField `json:"sort,omitempty,optional" zh:"查询排序"`
}

type SearchPerfLarkMemberResp struct {
	CurrentPage uint64                   `json:"current_page"`
	PageSize    uint64                   `json:"page_size"`
	TotalCount  uint64                   `json:"total_count"`
	TotalPage   uint64                   `json:"total_page"`
	Items       []*userinfo.FullUserInfo `json:"items"`
}
