package types

type Node struct {
	Id           string         `json:"id"`
	Type         string         `json:"type"`
	Label        string         `json:"label"`
	ItemType     string         `json:"itemType"`
	Width        float64        `json:"width"`
	Height       float64        `json:"height"`
	Size         []float64      `json:"size"`
	X            float64        `json:"x"`
	Y            float64        `json:"y"`
	AnchorPoints [][]float64    `json:"anchorPoints"`
	Icon         string         `json:"icon"`
	Style        map[string]any `json:"style"`
	LabelCfg     map[string]any `json:"labelCfg,optional"` // 为了兼容旧版本数据，所以这里允许不传
	ComboId      string         `json:"comboId,optional"`
	Data         map[string]any `json:"data,optional"`
	Order        int32          `json:"_order,optional"`
	LayoutOrder  int32          `json:"layoutOrder,optional"`
}

type Edge struct {
	Id              string         `json:"id"`
	Type            string         `json:"type"`
	Label           string         `json:"label"`
	Source          string         `json:"source"`
	SourceAnchor    float64        `json:"sourceAnchor"`
	Target          string         `json:"target"`
	TargetAnchor    float64        `json:"targetAnchor"`
	LineAppendWidth int32          `json:"lineAppendWidth"`
	Clazz           string         `json:"clazz,optional"` // 为了兼容旧版本数据，所以这里允许不传
	Attrs           map[string]any `json:"attrs"`
	Style           map[string]any `json:"style"`
	LabelCfg        map[string]any `json:"labelCfg"`
	StartPoint      map[string]any `json:"startPoint"`
	EndPoint        map[string]any `json:"endPoint"`
}

type Combo struct {
	Id           string         `json:"id"`
	Type         string         `json:"type"`
	Label        string         `json:"label"`
	ItemType     string         `json:"itemType"`
	X            float64        `json:"x"`
	Y            float64        `json:"y"`
	AnchorPoints [][]float64    `json:"anchorPoints"`
	Icon         string         `json:"icon"`
	LabelCfg     map[string]any `json:"labelCfg"`
	Style        map[string]any `json:"style"`
	Depth        int32          `json:"depth"`
	Padding      []float64      `json:"padding"`
	Collapsed    bool           `json:"collapsed"`
	Children     []*ComboChild  `json:"children"`
	Data         map[string]any `json:"data,optional"`
}

type ComboChild struct {
	Id       string `json:"id"`
	ComboId  string `json:"comboId"`
	ItemType string `json:"itemType"`
	Depth    int32  `json:"depth"`
}
