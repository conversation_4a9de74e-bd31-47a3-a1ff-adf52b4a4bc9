package types

import (
	"bytes"
	"encoding/json"
	"fmt"
	"reflect"
	"sort"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"
)

const (
	tagNameJson = "json"
)

func (r ModifyComponentGroupReq) MarshalJSON() ([]byte, error) {
	return marshalStruct(r)
}

func (r ModifyApiCaseReq) MarshalJSON() ([]byte, error) {
	return marshalStruct(r)
}

func getName(field reflect.StructField) string {
	name := field.Tag.Get(tagNameJson)
	if len(name) == 0 {
		name = field.Name
	}

	return fmt.Sprintf("%q", name)
}

func copySlice(src any) (any, error) {
	rt := reflect.TypeOf(src)
	rv := reflect.ValueOf(src)

	if rv.Kind() != reflect.Slice {
		return nil, errors.Errorf("not a slice: %T", src)
	}

	dst := reflect.MakeSlice(reflect.SliceOf(rt.<PERSON>em()), rv.Len(), rv.Cap())
	reflect.Copy(dst, rv)

	return dst.Interface(), nil
}

//nolint:deadcode
func deepCopySlice(src any) (any, error) {
	rt := reflect.TypeOf(src)
	rv := reflect.ValueOf(src)

	if rv.Kind() != reflect.Slice {
		return nil, errors.Errorf("not a slice: %T", src)
	}

	dst := reflect.MakeSlice(reflect.SliceOf(rt.Elem()), rv.Len(), rv.Cap())
	x := reflect.New(dst.Type())
	x.Elem().Set(dst)

	err := utils.Copy(x.Interface(), src)
	if err != nil {
		return nil, errors.Errorf("failed to copy slice, error: %+v", err)
	}

	return dst.Interface(), nil
}

func marshalStruct(structure any) ([]byte, error) {
	var buf bytes.Buffer

	buf.WriteByte('{')

	encoder := json.NewEncoder(&buf)
	encoder.SetEscapeHTML(true)

	rt := reflect.TypeOf(structure)
	rv := reflect.ValueOf(structure)
	for i := 0; i < rt.NumField(); i++ {
		if i > 0 {
			buf.WriteByte(',')
		}

		rtf := rt.Field(i)
		rvf := rv.Field(i)

		// key
		buf.WriteString(getName(rtf))

		buf.WriteByte(':')

		// value
		v := rvf.Interface()

		var (
			bs  []byte
			err error
		)
		switch rvf.Kind() {
		case reflect.Slice:
			bs, err = marshalSlice(v)
		case reflect.Struct:
			bs, err = marshalStruct(v)
		default:
			err = encoder.Encode(v)
		}
		if err != nil {
			return nil, err
		} else if bs != nil {
			buf.Write(bs)
		}
	}

	buf.WriteByte('}')

	return buf.Bytes(), nil
}

func marshalSlice(slice any) ([]byte, error) {
	ts, err := copySlice(slice)
	if err != nil {
		return nil, err
	}

	rv := reflect.ValueOf(ts)

	// sort the slice
	if rv.Len() > 1 {
		_, ok := rv.Index(0).Interface().(SortType)
		if ok {
			sort.Slice(ts, func(i, j int) bool {
				return rv.Index(i).Interface().(SortType).LessKey() < rv.Index(j).Interface().(SortType).LessKey()
			})
		}
	}

	var buf bytes.Buffer
	buf.WriteByte('[')

	encoder := json.NewEncoder(&buf)
	encoder.SetEscapeHTML(true)

	for i := 0; i < rv.Len(); i++ {
		if i > 0 {
			buf.WriteByte(',')
		}

		if err = encoder.Encode(rv.Index(i).Interface()); err != nil {
			return nil, err
		}
	}

	buf.WriteByte(']')

	return buf.Bytes(), nil
}
