package types

import (
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sqlbuilder/api"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/userinfo"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/types"
)

type PerfCaseV2 struct {
	ProjectId     string                  `json:"project_id"`
	CategoryId    string                  `json:"category_id"`
	CaseId        string                  `json:"case_id"`
	Name          string                  `json:"name"`
	Description   string                  `json:"description"`
	Tags          []string                `json:"tags"`
	Protocol      string                  `json:"protocol"`
	RateLimits    []*types.RateLimitV2    `json:"rate_limits"`
	SetupSteps    []*types.PerfCaseStepV2 `json:"setup_steps"`
	SerialSteps   []*types.PerfCaseStepV2 `json:"serial_steps"`
	ParallelSteps []*types.PerfCaseStepV2 `json:"parallel_steps"`
	TeardownSteps []*types.PerfCaseStepV2 `json:"teardown_steps"`
	NumberOfSteps uint32                  `json:"number_of_steps"`
	TargetRps     int64                   `json:"target_rps"`
	MaintainedBy  *userinfo.FullUserInfo  `json:"maintained_by"`
	CreatedBy     *userinfo.FullUserInfo  `json:"created_by"`
	UpdatedBy     *userinfo.FullUserInfo  `json:"updated_by"`
	CreatedAt     int64                   `json:"created_at"`
	UpdatedAt     int64                   `json:"updated_at"`
}

type SearchPerfCaseV2Item struct {
	ProjectId     string                  `json:"project_id"`
	CategoryId    string                  `json:"category_id"`
	CaseId        string                  `json:"case_id"`
	Name          string                  `json:"name"`
	Description   string                  `json:"description"`
	Tags          []string                `json:"tags"`
	Protocol      string                  `json:"protocol"`
	RateLimits    []*types.RateLimitV2    `json:"rate_limits"`
	SerialSteps   []*types.PerfCaseStepV2 `json:"serial_steps"`
	ParallelSteps []*types.PerfCaseStepV2 `json:"parallel_steps"`
	NumberOfSteps uint32                  `json:"number_of_steps"`
	MaintainedBy  *userinfo.FullUserInfo  `json:"maintained_by"`
	CreatedBy     *userinfo.FullUserInfo  `json:"created_by"`
	UpdatedBy     *userinfo.FullUserInfo  `json:"updated_by"`
	CreatedAt     int64                   `json:"created_at"`
	UpdatedAt     int64                   `json:"updated_at"`
}

type CreatePerfCaseV2Req struct {
	ProjectId     string                  `json:"project_id" validate:"required" zh:"项目ID"`
	CategoryId    string                  `json:"category_id" validate:"required" zh:"分类ID"`
	Name          string                  `json:"name" validate:"gte=1,lte=64" zh:"压测用例名称"`
	Description   string                  `json:"description" validate:"lte=255" zh:"压测用例描述"`
	Tags          []string                `json:"tags" validate:"gte=0" zh:"压测用例标签"`
	Protocol      string                  `json:"protocol" validate:"oneof=TT TT_AUTH gRPC HTTP" zh:"协议"`
	RateLimits    []*types.RateLimitV2    `json:"rate_limits" validate:"gte=1" zh:"限流配置"`
	SetupSteps    []*types.PerfCaseStepV2 `json:"setup_steps" zh:"前置步骤"`
	SerialSteps   []*types.PerfCaseStepV2 `json:"serial_steps" zh:"串行步骤"`
	ParallelSteps []*types.PerfCaseStepV2 `json:"parallel_steps" zh:"并行步骤"`
	TeardownSteps []*types.PerfCaseStepV2 `json:"teardown_steps" zh:"后置步骤"`
	MaintainedBy  string                  `json:"maintained_by" validate:"lte=64" zh:"压测用例维护者"`
}

type CreatePerfCaseV2Resp struct {
	CaseId string `json:"case_id"`
}

type RemovePerfCaseV2Req struct {
	ProjectId string   `json:"project_id" validate:"required" zh:"项目ID"`
	CaseIds   []string `json:"case_ids" validate:"gt=0" zh:"压测用例ID列表"`
}

type RemovePerfCaseV2Resp struct{}

type ModifyPerfCaseV2Req struct {
	ProjectId     string                  `json:"project_id" validate:"required" zh:"项目ID"`
	CategoryId    string                  `json:"category_id" validate:"required" zh:"分类ID"`
	CaseId        string                  `json:"case_id" validate:"required" zh:"压测用例ID"`
	Name          string                  `json:"name" validate:"gte=1,lte=64" zh:"压测用例名称"`
	Description   string                  `json:"description" validate:"lte=255" zh:"压测用例描述"`
	Tags          []string                `json:"tags" validate:"gte=0" zh:"压测用例标签"`
	Protocol      string                  `json:"protocol" validate:"oneof=TT TT_AUTH gRPC HTTP" zh:"协议"`
	RateLimits    []*types.RateLimitV2    `json:"rate_limits" validate:"gte=1" zh:"限流配置"`
	SetupSteps    []*types.PerfCaseStepV2 `json:"setup_steps" zh:"前置步骤"`
	SerialSteps   []*types.PerfCaseStepV2 `json:"serial_steps" zh:"串行步骤"`
	ParallelSteps []*types.PerfCaseStepV2 `json:"parallel_steps" zh:"并行步骤"`
	TeardownSteps []*types.PerfCaseStepV2 `json:"teardown_steps" zh:"后置步骤"`
	MaintainedBy  string                  `json:"maintained_by" validate:"lte=64" zh:"压测用例维护者"`
}

type ModifyPerfCaseV2Resp struct{}

type SearchPerfCaseV2Req struct {
	ProjectId  string           `json:"project_id" validate:"required" zh:"项目ID"`
	CategoryId string           `json:"category_id" validate:"required" zh:"分类ID"`
	Condition  *api.Condition   `json:"condition,omitempty,optional" zh:"查询条件"`
	Pagination *api.Pagination  `json:"pagination,omitempty,optional" zh:"查询分页"`
	Sort       []*api.SortField `json:"sort,omitempty,optional" zh:"查询排序"`
}

type SearchPerfCaseV2Resp struct {
	CurrentPage uint64                  `json:"current_page"`
	PageSize    uint64                  `json:"page_size"`
	TotalCount  uint64                  `json:"total_count"`
	TotalPage   uint64                  `json:"total_page"`
	Items       []*SearchPerfCaseV2Item `json:"items"`
}

type ViewPerfCaseV2Req struct {
	ProjectId string `form:"project_id" validate:"required" zh:"项目ID"`
	CaseId    string `form:"case_id" validate:"required" zh:"压测用例ID"`
}

type ViewPerfCaseV2Resp struct {
	*PerfCaseV2
}
