package types

import (
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sqlbuilder/api"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/userinfo"
)

type PerfLarkChat struct {
	ProjectId   string                 `json:"project_id"`  // 项目ID
	ChatId      string                 `json:"chat_id"`     // 飞书群组ID
	Name        string                 `json:"name"`        // 飞书群组名称
	Avatar      string                 `json:"avatar"`      // 飞书群组头像URL
	Description string                 `json:"description"` // 飞书群组描述
	External    bool                   `json:"external"`    // 是否是外部群
	Status      string                 `json:"status"`      // 飞书群组状态
	CreatedBy   *userinfo.FullUserInfo `json:"created_by"`  // 创建者
	UpdatedBy   *userinfo.FullUserInfo `json:"updated_by"`  // 更新者
	CreatedAt   int64                  `json:"created_at"`  // 创建时间
	UpdatedAt   int64                  `json:"updated_at"`  // 更新时间
}

type ModifyPerfLarkChatReq struct {
	ProjectId string   `json:"project_id" validate:"required" zh:"项目ID"`
	LarkChats []string `json:"lark_chats" validate:"gte=0" zh:"飞书群组ID列表"`
}

type ModifyPerfLarkChatResp struct{}

type SearchPerfLarkChatReq struct {
	ProjectId  string           `json:"project_id" validate:"required" zh:"项目ID"`
	Condition  *api.Condition   `json:"condition,omitempty,optional" zh:"查询条件"`
	Pagination *api.Pagination  `json:"pagination,omitempty,optional" zh:"查询分页"`
	Sort       []*api.SortField `json:"sort,omitempty,optional" zh:"查询排序"`
}

type SearchPerfLarkChatResp struct {
	CurrentPage uint64          `json:"current_page"`
	PageSize    uint64          `json:"page_size"`
	TotalCount  uint64          `json:"total_count"`
	TotalPage   uint64          `json:"total_page"`
	Items       []*PerfLarkChat `json:"items"`
}
