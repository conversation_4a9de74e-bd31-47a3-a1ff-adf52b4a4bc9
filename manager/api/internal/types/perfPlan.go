package types

import (
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sqlbuilder/api"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/userinfo"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/types"
)

type PerfPlan struct {
	ProjectId        string                 `json:"project_id"`         // 项目ID
	PlanId           string                 `json:"plan_id"`            // 计划ID
	Name             string                 `json:"name"`               // 计划名称
	Description      string                 `json:"description"`        // 计划描述
	Type             string                 `json:"type"`               // 计划类型（手动、定时、接口）
	Tags             []string               `json:"tags"`               // 标签
	State            int8                   `json:"state"`              // 状态
	Protocol         string                 `json:"protocol"`           // 协议（TT私有协议、通用gRPC协议、通用HTTP协议）
	ProtobufConfigId string                 `json:"protobuf_config_id"` // protobuf配置ID
	GeneralConfigId  string                 `json:"general_config_id"`  // 通用配置ID
	AccountConfigId  string                 `json:"account_config_id"`  // 池账号配置ID
	Duration         uint32                 `json:"duration"`           // 压测持续时间
	TargetEnv        string                 `json:"target_env"`         // 目标环境（生产环境、测试环境）
	Keepalive        *PerfKeepalive         `json:"keepalive"`          // 保活参数
	Delay            uint32                 `json:"delay"`              // 延迟执行时间
	MaintainedBy     *userinfo.FullUserInfo `json:"maintained_by"`      // 维护者
	CreatedBy        *userinfo.FullUserInfo `json:"created_by"`         // 创建者
	UpdatedBy        *userinfo.FullUserInfo `json:"updated_by"`         // 更新者
	CreatedAt        int64                  `json:"created_at"`         // 创建时间
	UpdatedAt        int64                  `json:"updated_at"`         // 更新时间
}

type AuthConfig struct {
	types.RateLimit
}
type HeartbeatConfig struct {
	types.RateLimit

	Interval uint32 `json:"interval" validate:"gt=0"`
}
type PerfKeepalive struct {
	Auth      AuthConfig      `json:"auth"`      // 认证接口限流配置
	Heartbeat HeartbeatConfig `json:"heartbeat"` // 心跳接口限流配置
}

type SearchPerfPlanItem struct {
	ProjectId    string                 `json:"project_id"`     // 项目ID
	PlanId       string                 `json:"plan_id"`        // 计划ID
	Name         string                 `json:"name"`           // 计划名称
	Description  string                 `json:"description"`    // 计划描述
	Type         string                 `json:"type"`           // 计划类型（手动、定时、接口）
	Tags         []string               `json:"tags"`           // 标签
	State        int8                   `json:"state"`          // 状态
	Protocol     string                 `json:"protocol"`       // 协议（TT私有协议、通用gRPC协议、通用HTTP协议）
	Duration     uint32                 `json:"duration"`       // 压测持续时间
	TargetEnv    string                 `json:"target_env"`     // 目标环境（生产环境、测试环境）
	NumberOfCase uint32                 `json:"number_of_case"` // 压测用例数量
	NumberOfApi  uint32                 `json:"number_of_api"`  // 压测接口数量
	StatsOfApi   []*StatsOfApi          `json:"stats_of_api"`   // 压测接口统计信息
	MaintainedBy *userinfo.FullUserInfo `json:"maintained_by"`  // 维护者
	CreatedBy    *userinfo.FullUserInfo `json:"created_by"`     // 创建者
	UpdatedBy    *userinfo.FullUserInfo `json:"updated_by"`     // 更新者
	CreatedAt    int64                  `json:"created_at"`     // 创建时间
	UpdatedAt    int64                  `json:"updated_at"`     // 更新时间
}

type StatsOfApi struct {
	TargetRps   uint32 `json:"target_rps"`    // 目标的RPS
	NumberOfApi uint32 `json:"number_of_api"` // 接口数量
}

type SearchCaseInPerfPlanItem struct {
	ProjectId   string `json:"project_id"`
	CaseId      string `json:"case_id"`
	Name        string `json:"name"`
	Description string `json:"description"`
	Extension   string `json:"extension"`
	Hash        string `json:"hash"`
	Size        uint32 `json:"size"`

	types.RateLimit     // 限流配置
	types.BasicPerfData // 压测数据配置
	types.LoadGenerator // 施压机资源配置

	CreatedBy *userinfo.FullUserInfo `json:"created_by"`
	UpdatedBy *userinfo.FullUserInfo `json:"updated_by"`
	CreatedAt int64                  `json:"created_at"`
	UpdatedAt int64                  `json:"updated_at"`
}

type CreatePerfPlanReq struct {
	ProjectId        string            `json:"project_id" validate:"required" zh:"项目ID"`
	Name             string            `json:"name" validate:"gte=1,lte=64" zh:"压测计划名称"`
	Description      string            `json:"description" validate:"lte=255" zh:"压测计划描述"`
	Type             string            `json:"type" validate:"oneof=MANUAL" zh:"计划类型"`
	Tags             []string          `json:"tags" validate:"gte=0" zh:"压测计划标签"`
	Protocol         string            `json:"protocol" validate:"oneof=TT gRPC HTTP" zh:"协议"`
	ProtobufConfigId string            `json:"protobuf_config_id" validate:"omitempty,gte=1,lte=64" zh:"protobuf配置ID"`
	GeneralConfigId  string            `json:"general_config_id" validate:"omitempty,gte=1,lte=64" zh:"通用配置ID"`
	AccountConfigId  string            `json:"account_config_id" validate:"omitempty,gte=1,lte=64" zh:"池账号配置ID"`
	Duration         uint32            `json:"duration" validate:"gte=1" zh:"压测持续时间"`
	TargetEnv        string            `json:"target_env" validate:"oneof=PRODUCTION CANARY STAGING TESTING DEVELOPMENT" zh:"目标环境"`
	Keepalive        *PerfKeepalive    `json:"keepalive" validate:"omitempty" zh:"保活参数"`
	Cases            []string          `json:"cases" validate:"gte=1" zh:"压测用例ID列表"`
	Delay            uint32            `json:"delay" validate:"gte=0" zh:"延迟执行时间"`
	LarkChats        []*types.LarkChat `json:"lark_chats" validate:"gte=1" zh:"飞书通知群组列表"`
	MaintainedBy     string            `json:"maintained_by" validate:"lte=64" zh:"压测计划维护者"`
}

type CreatePerfPlanResp struct {
	PlanId string `json:"plan_id"`
}

type RemovePerfPlanReq struct {
	ProjectId string   `json:"project_id" validate:"required" zh:"项目ID"`
	PlanIds   []string `json:"plan_ids" validate:"gt=0" zh:"压测计划ID列表"`
}

type RemovePerfPlanResp struct{}

type ModifyPerfPlanReq struct {
	ProjectId        string            `json:"project_id" validate:"required" zh:"项目ID"`
	PlanId           string            `json:"plan_id" validate:"required" zh:"压测计划ID"`
	Name             string            `json:"name" validate:"gte=1,lte=64" zh:"压测计划名称"`
	Description      string            `json:"description" validate:"lte=255" zh:"压测计划描述"`
	Type             string            `json:"type" validate:"oneof=MANUAL" zh:"计划类型"`
	Tags             []string          `json:"tags" validate:"gte=0" zh:"压测计划标签"`
	Protocol         string            `json:"protocol" validate:"oneof=TT gRPC HTTP" zh:"协议"`
	ProtobufConfigId string            `json:"protobuf_config_id" validate:"omitempty,gte=1,lte=64" zh:"protobuf配置ID"`
	GeneralConfigId  string            `json:"general_config_id" validate:"omitempty,gte=1,lte=64" zh:"通用配置ID"`
	AccountConfigId  string            `json:"account_config_id" validate:"omitempty,gte=1,lte=64" zh:"池账号配置ID"`
	Duration         uint32            `json:"duration" validate:"gte=1" zh:"压测持续时间"`
	TargetEnv        string            `json:"target_env" validate:"oneof=PRODUCTION CANARY STAGING TESTING DEVELOPMENT" zh:"目标环境"`
	Keepalive        *PerfKeepalive    `json:"keepalive" validate:"omitempty" zh:"保活参数"`
	Cases            []string          `json:"cases" validate:"gte=1" zh:"压测用例ID列表"`
	Delay            uint32            `json:"delay" validate:"gte=0" zh:"延迟执行时间"`
	LarkChats        []*types.LarkChat `json:"lark_chats" validate:"gte=1" zh:"飞书通知群组列表"`
	MaintainedBy     string            `json:"maintained_by" validate:"lte=64" zh:"压测计划维护者"`
}

type ModifyPerfPlanResp struct{}

type SearchPerfPlanReq struct {
	ProjectId  string           `json:"project_id" validate:"required" zh:"项目ID"`
	Condition  *api.Condition   `json:"condition,omitempty,optional" zh:"查询条件"`
	Pagination *api.Pagination  `json:"pagination,omitempty,optional" zh:"查询分页"`
	Sort       []*api.SortField `json:"sort,omitempty,optional" zh:"查询排序"`
}

type SearchPerfPlanResp struct {
	CurrentPage uint64                `json:"current_page"`
	PageSize    uint64                `json:"page_size"`
	TotalCount  uint64                `json:"total_count"`
	TotalPage   uint64                `json:"total_page"`
	Items       []*SearchPerfPlanItem `json:"items"`
}

type ViewPerfPlanReq struct {
	ProjectId string `form:"project_id" validate:"required" zh:"项目ID"`
	PlanId    string `form:"plan_id" validate:"required" zh:"压测计划ID"`
}

type ViewPerfPlanResp struct {
	*PerfPlan
}

type SearchCaseInPerfPlanReq struct {
	ProjectId  string           `json:"project_id" validate:"required" zh:"项目ID"`
	PlanId     string           `json:"plan_id" validate:"required" zh:"API计划ID"`
	Condition  *api.Condition   `json:"condition,omitempty,optional" zh:"查询条件"`
	Pagination *api.Pagination  `json:"pagination,omitempty,optional" zh:"查询分页"`
	Sort       []*api.SortField `json:"sort,omitempty,optional" zh:"查询排序"`
}

type SearchCaseInPerfPlanResp struct {
	CurrentPage uint64                      `json:"current_page"`
	PageSize    uint64                      `json:"page_size"`
	TotalCount  uint64                      `json:"total_count"`
	TotalPage   uint64                      `json:"total_page"`
	Items       []*SearchCaseInPerfPlanItem `json:"items"`
}
