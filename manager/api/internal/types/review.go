package types

import (
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sqlbuilder/api"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/userinfo"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
)

type ReviewRecord struct {
	ProjectId          string                    `json:"project_id"`
	ReviewId           string                    `json:"review_id"`
	ResourceBranch     string                    `json:"resource_branch"`
	ResourceParentType common.ReviewResourceType `json:"resource_parent_type"`
	ResourceType       common.ReviewResourceType `json:"resource_type"`
	ResourceId         string                    `json:"resource_id"`
	ResourceName       string                    `json:"resource_name"`
	RemarkOfPending    string                    `json:"remark_of_pending"`
	RemarkOfRevoked    string                    `json:"remark_of_revoked"`
	RemarkOfReviewed   string                    `json:"remark_of_reviewed"`
	AssignedReviewers  []*userinfo.FullUserInfo  `json:"assigned_reviewers"`
	Status             common.ReviewStatus       `json:"status"`
	CreatedBy          *userinfo.FullUserInfo    `json:"created_by"`
	UpdatedBy          *userinfo.FullUserInfo    `json:"updated_by"`
	CreatedAt          int64                     `json:"created_at"`
	UpdatedAt          int64                     `json:"updated_at"`
}

type CreateReviewRecordReq struct {
	ProjectId         string                    `json:"project_id" validate:"required" zh:"项目ID"`
	ResourceType      common.ReviewResourceType `json:"resource_type" validate:"required,oneof=SETUP TEARDOWN GROUP API_CASE INTERFACE_CASE" zh:"申请审核的资源类型"`
	ResourceId        string                    `json:"resource_id" validate:"required" zh:"申请审核的资源ID"`
	Remark            string                    `json:"remark" validate:"lte=1024" zh:"申请审核的备注"`
	AssignedReviewers []string                  `json:"assigned_reviewers" validate:"gt=0" zh:"指派的审核者"`
}

type CreateReviewRecordResp struct {
	ReviewId string `json:"review_id"`
}

type ModifyReviewRecordReq struct {
	ProjectId         string   `json:"project_id" validate:"required" zh:"项目ID"`
	ReviewId          string   `json:"review_id" validate:"required" zh:"审核ID"`
	Remark            string   `json:"remark" validate:"lte=1024" zh:"申请审核的备注"`
	AssignedReviewers []string `json:"assigned_reviewers" validate:"gt=0" zh:"指派的审核者"`
}

type ModifyReviewRecordResp struct{}

type RevokeReviewRecordReq struct {
	ProjectId string `json:"project_id" validate:"required" zh:"项目ID"`
	ReviewId  string `json:"review_id" validate:"required" zh:"审核ID"`
	Remark    string `json:"remark" validate:"lte=1024" zh:"撤回审核的备注"`
}

type RevokeReviewRecordResp struct{}

type ApproveReviewRecordReq struct {
	ProjectId string              `json:"project_id" validate:"required" zh:"项目ID"`
	ReviewId  string              `json:"review_id" validate:"required" zh:"审核ID"`
	Result    common.ReviewStatus `json:"result" validate:"required,oneof=APPROVED REJECTED" zh:"审批结果"`
	Remark    string              `json:"remark" validate:"lte=1024" zh:"审批审核的备注"`
}

type ApproveReviewRecordResp struct{}

type SearchReviewRecordReq struct {
	ProjectId  string           `json:"project_id" validate:"required" zh:"项目ID"`
	Condition  *api.Condition   `json:"condition,omitempty,optional" zh:"查询条件"`
	Pagination *api.Pagination  `json:"pagination,omitempty,optional" zh:"查询分页"`
	Sort       []*api.SortField `json:"sort,omitempty,optional" zh:"查询排序"`
}

type SearchReviewRecordResp struct {
	CurrentPage uint64          `json:"current_page"`
	PageSize    uint64          `json:"page_size"`
	TotalCount  uint64          `json:"total_count"`
	TotalPage   uint64          `json:"total_page"`
	Items       []*ReviewRecord `json:"items"`
}
