package types

import (
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sqlbuilder/api"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/userinfo"
)

type PerfData struct {
	ProjectId   string                 `json:"project_id"`
	DataId      string                 `json:"data_id"`
	Name        string                 `json:"name"`
	Description string                 `json:"description"`
	Extension   string                 `json:"extension"`
	Hash        string                 `json:"hash"`
	Size        uint32                 `json:"size"`
	NumberOfVu  uint32                 `json:"number_of_vu"`
	CreatedBy   *userinfo.FullUserInfo `json:"created_by"`
	UpdatedBy   *userinfo.FullUserInfo `json:"updated_by"`
	CreatedAt   int64                  `json:"created_at"`
	UpdatedAt   int64                  `json:"updated_at"`
}

type UploadPerfDataReq struct {
	ProjectId string `form:"project_id" validate:"required" zh:"项目ID"`
}

type UploadPerfDataResp struct {
	DataId string `json:"data_id"`
}

type DownloadPerfDataReq struct {
	ProjectId string `form:"project_id" validate:"required" zh:"项目ID"`
	DataId    string `form:"data_id" validate:"required" zh:"压测数据ID"`
}

type DownloadPerfDataResp struct{}

type RemovePerfDataReq struct {
	ProjectId string   `json:"project_id" validate:"required" zh:"项目ID"`
	DataIds   []string `json:"data_ids" validate:"gt=0" zh:"压测数据ID列表"`
}

type RemovePerfDataResp struct{}

type SearchPerfDataReq struct {
	ProjectId  string           `json:"project_id" validate:"required" zh:"项目ID"`
	Condition  *api.Condition   `json:"condition,omitempty,optional" zh:"查询条件"`
	Pagination *api.Pagination  `json:"pagination,omitempty,optional" zh:"查询分页"`
	Sort       []*api.SortField `json:"sort,omitempty,optional" zh:"查询排序"`
}

type SearchPerfDataResp struct {
	CurrentPage uint64      `json:"current_page"`
	PageSize    uint64      `json:"page_size"`
	TotalCount  uint64      `json:"total_count"`
	TotalPage   uint64      `json:"total_page"`
	Items       []*PerfData `json:"items"`
}
