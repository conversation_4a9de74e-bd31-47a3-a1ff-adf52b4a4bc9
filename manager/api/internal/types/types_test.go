package types

import (
	"encoding/json"
	"testing"

	"github.com/zeromicro/go-zero/core/jsonx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sqlbuilder/api"
)

func TestNode_UnmarshalJSON(t *testing.T) {
	in := `{
            "width": 80,
            "height": 40,
            "anchorPoints": [
                [
                    0.5,
                    0
                ],
                [
                    0.5,
                    1
                ],
                [
                    1,
                    0.5
                ],
                [
                    0,
                    0.5
                ]
            ],
            "type": "HTTP",
            "label": "单组件",
            "icon": "/img/task.df105b8d.svg",
            "style": {
                "active": {
                    "fill": "rgb(247, 250, 255)",
                    "stroke": "rgb(95, 149, 255)",
                    "lineWidth": 2,
                    "shadowColor": "rgb(95, 149, 255)",
                    "shadowBlur": 10
                },
                "selected": {
                    "fill": "rgb(255, 255, 255)",
                    "stroke": "rgb(95, 149, 255)",
                    "lineWidth": 4,
                    "shadowColor": "rgb(95, 149, 255)",
                    "shadowBlur": 10,
                    "text-shape": {
                        "fontWeight": 500
                    }
                },
                "highlight": {
                    "fill": "rgb(223, 234, 255)",
                    "stroke": "#4572d9",
                    "lineWidth": 2,
                    "text-shape": {
                        "fontWeight": 500
                    }
                },
                "inactive": {
                    "fill": "rgb(247, 250, 255)",
                    "stroke": "rgb(191, 213, 255)",
                    "lineWidth": 1
                },
                "disable": {
                    "fill": "rgb(250, 250, 250)",
                    "stroke": "rgb(224, 224, 224)",
                    "lineWidth": 1
                },
                "fill": "#e7f7fe",
                "stroke": "#9ed2fe",
                "fillOpacity": 3,
                "lineWidth": 1,
                "lineDash": []
            },
            "size": [
                80,
                40
            ],
            "itemType": "node",
            "x": 154,
            "y": 186,
            "id": "singleQuest_1c22qd-252263a5-d263-41e6-ad66-4afaf4835bfd",
            "data": {
                "name": "单组件",
                "description": "singleQuest_1c22qd-252263a5-d263-41e6-ad66-4afaf4835bfd",
                "nodeId": "singleQuest_1c22qd-252263a5-d263-41e6-ad66-4afaf4835bfd",
                "values1Arr": [
                    {
                        "value1key": "",
                        "value1": ""
                    }
                ],
                "url": "HTTP请求URL",
                "method": "HTTP请求Method，如：GET/POST/PUT/DELETE",
                "authorization": {
                    "type": 0,
                    "api_key": {},
                    "bearer_token": {},
                    "basic_auth": {}
                },
                "headers": [
                    {
                        "key": "Content-Type",
                        "value": "application/json",
                        "description": ""
                    }
                ],
                "query_params": [],
                "body": {
                    "content_type": "application/json",
                    "form_data": [],
                    "raw": "请求体内容，对应格式的字符串"
                },
                "timeout": {
                    "connect_timeout": 3000,
                    "request_timeout": 1500,
                    "response_timeout": 1500
                },
                "assertions": [
                    {
                        "source": "来源，可选值有：Headers/Body/StatusCode",
                        "headers": {
                            "key": "响应头的键",
                            "expression": "正则表达式"
                        },
                        "body": {
                            "type": "提取类型，可选值有：JSONPath/Regex",
                            "jsonpath": {
                                "expression": "JSONPath表达式",
                                "operator": "操作，可选值有：等于（EQ）、不等于（NE）、小于（LT）、小于等于（LE）、大于（GT）、大于等于（GE）、包含（CONTAINS）、不包含（NOT_CONTAINS）、正则匹配（RE）",
                                "expectation": "期望值，字符串"
                            },
                            "regex": {
                                "expression": "Regex表达式"
                            }
                        },
                        "status_code": {
                            "operator": "操作，可选值有：等于（EQ）、不等于（NE）、小于（LT）、小于等于（LE）、大于（GT）、大于等于（GE）、正则匹配（RE）",
                            "expectation": "期望值，字符串"
                        }
                    }
                ],
                "imports": [
                    {
                        "name": "入参变量名称",
                        "source": "来源，可选值有：Manual/Export/Account/Environment",
                        "manual": {
                            "value": "变量的值"
                        },
                        "export": {
                            "node_id": "来源节点ID",
                            "value": "来源节点的出参变量名称"
                        },
                        "account": {},
                        "environment": {}
                    }
                ],
                "exports": [
                    {
                        "name": "出参变量名称",
                        "source": "来源，可选值有：Headers/Body/StatusCode",
                        "headers": {
                            "key": "响应头的键"
                        },
                        "body": {
                            "type": "提取类型，可选值有：JSONPath/Regex",
                            "expression": "提取表达式"
                        }
                    }
                ]
            }
        }`

	n := &Node{}
	err := json.Unmarshal([]byte(in), n)
	if err != nil {
		t.Fatal(err)
	}

	t.Logf("data: %+v", n.Data)
}

func TestGroupCondition(t *testing.T) {
	in := `{
        "relationship": "AND",
        "conditions": [
            {
                "single": {
                    "field": "name",
                    "compare": "IN",
                    "in": ["allen", "bob", "charles"],
                    "between": null,
                    "other": null
                }
            }
        ]
    }`
	group := &api.GroupCondition{}
	err := jsonx.UnmarshalFromString(in, group)
	if err != nil {
		t.Fatal(err)
	}
	t.Logf("GroupCondition: %+v", group)
}
