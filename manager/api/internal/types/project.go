package types

import (
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sqlbuilder/api"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/userinfo"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/types"
)

type Project struct {
	ProjectId         string                 `json:"project_id"`
	Name              string                 `json:"name"`
	Description       string                 `json:"description"`
	ReviewEnabled     bool                   `json:"review_enabled"`
	CoverageEnabled   bool                   `json:"coverage_enabled"`
	CoverageLarkChats []*types.LarkChat      `json:"coverage_lark_chats"`
	CreatedBy         *userinfo.FullUserInfo `json:"created_by"`
	UpdatedBy         *userinfo.FullUserInfo `json:"updated_by"`
	CreatedAt         int64                  `json:"created_at"`
	UpdatedAt         int64                  `json:"updated_at"`
}

type (
	CreateProjectReq struct {
		Name          string `json:"name" validate:"gte=1,lte=64" zh:"项目名称"`
		Description   string `json:"description" validate:"lte=255" zh:"项目描述"`
		ReviewEnabled bool   `json:"review_enabled,omitempty,optional,default=false" validate:"omitempty" zh:"是否开启用例审核功能"`
	}
	CreateProjectResp struct {
		ProjectId string `json:"project_id"`
	}
)

type (
	RemoveProjectReq struct {
		ProjectId string `json:"project_id" validate:"required" zh:"项目ID"`
	}
	RemoveProjectResp struct{}
)

type (
	ModifyProjectReq struct {
		ProjectId         string            `json:"project_id" validate:"required" zh:"项目ID"`
		Name              string            `json:"name" validate:"gte=1,lte=64" zh:"项目名称"`
		Description       string            `json:"description" validate:"lte=255" zh:"项目描述"`
		ReviewEnabled     bool              `json:"review_enabled,omitempty,optional,default=false" validate:"omitempty" zh:"是否开启用例审核功能"`
		CoverageEnabled   bool              `json:"coverage_enabled,omitempty,optional,default=false" validate:"omitempty" zh:"是否开启接口用例覆盖率统计功能"`
		CoverageLarkChats []*types.LarkChat `json:"coverage_lark_chats,omitempty,optional" validate:"omitempty,lte=5" zh:"接口用例覆盖率飞书通知群组"`
	}
	ModifyProjectResp struct{}
)

type (
	SearchProjectReq struct {
		Condition  *api.Condition   `json:"condition,omitempty,optional" zh:"查询条件"`
		Pagination *api.Pagination  `json:"pagination,omitempty,optional" zh:"查询分页"`
		Sort       []*api.SortField `json:"sort,omitempty,optional" zh:"查询排序"`
	}
	SearchProjectResp struct {
		CurrentPage uint64     `json:"current_page"`
		PageSize    uint64     `json:"page_size"`
		TotalCount  uint64     `json:"total_count"`
		TotalPage   uint64     `json:"total_page"`
		Items       []*Project `json:"items"`
	}
)

type (
	ViewProjectReq struct {
		ProjectId string `form:"project_id" validate:"required" zh:"项目ID"`
	}
	ViewProjectResp struct {
		*Project
	}
)

type (
	SearchProjectUserReq struct {
		ProjectId  string           `json:"project_id" validate:"required" zh:"项目ID"`
		Condition  *api.Condition   `json:"condition,omitempty,optional" zh:"查询条件"`
		Pagination *api.Pagination  `json:"pagination,omitempty,optional" zh:"查询分页"`
		Sort       []*api.SortField `json:"sort,omitempty,optional" zh:"查询排序"`
	}
	SearchProjectUserResp struct {
		CurrentPage uint64                   `json:"current_page"`
		PageSize    uint64                   `json:"page_size"`
		TotalCount  uint64                   `json:"total_count"`
		TotalPage   uint64                   `json:"total_page"`
		Items       []*userinfo.FullUserInfo `json:"items"`
	}
)

type (
	ModifyProjectReviewFunctionReq struct {
		ProjectId     string `json:"project_id" validate:"required" zh:"项目ID"`
		ReviewEnabled bool   `json:"review_enabled" validate:"omitempty" zh:"是否开启用例审核功能"`
	}
	ModifyProjectReviewFunctionResp struct{}
)

type (
	ModifyProjectCoverageFunctionReq struct {
		ProjectId         string            `json:"project_id" validate:"required" zh:"项目ID"`
		CoverageEnabled   bool              `json:"coverage_enabled" validate:"omitempty" zh:"是否开启接口用例覆盖率统计功能"`
		CoverageLarkChats []*types.LarkChat `json:"coverage_lark_chats" validate:"lte=5" zh:"接口用例覆盖率飞书通知群组"`
	}
	ModifyProjectCoverageFunctionResp struct{}
)
