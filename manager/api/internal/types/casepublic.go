package types

import (
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sqlbuilder/api"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/userinfo"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/types"
)

type Case struct {
	ProjectId     string                 `json:"project_id"`
	BranchId      string                 `json:"branch_id"`
	CaseId        string                 `json:"case_id"`
	Name          string                 `json:"name"`
	Description   string                 `json:"description"`
	CaseType      string                 `json:"case_type"`
	Priority      int64                  `json:"priority"`
	Tags          []string               `json:"tags"`
	State         string                 `json:"state"`
	AccountConfig *types.AccountConfig   `json:"account_config"`
	Version       string                 `json:"version"`
	MaintainedBy  *userinfo.FullUserInfo `json:"maintained_by"`
	CreatedBy     *userinfo.FullUserInfo `json:"created_by"`
	UpdatedBy     *userinfo.FullUserInfo `json:"updated_by"`
	CreatedAt     int64                  `json:"created_at"`
	UpdatedAt     int64                  `json:"updated_at"`
	Nodes         []*Node                `json:"nodes"`
	Edges         []*Edge                `json:"edges"`
	Combos        []*Combo               `json:"combos"`
}

type FailCase struct {
	Case
	Rank      uint32 `json:"rank"`
	FailCount uint32 `json:"fail_count"`
}

type SearchCaseReq struct {
	ProjectId  string           `json:"project_id" validate:"required" zh:"项目ID"`
	Condition  *api.Condition   `json:"condition,omitempty,optional" zh:"查询条件"`
	Pagination *api.Pagination  `json:"pagination,omitempty,optional" zh:"查询分页"`
	Sort       []*api.SortField `json:"sort,omitempty,optional" zh:"查询排序"`
}

type SearchCaseResp struct {
	CurrentPage uint64  `json:"current_page"`
	PageSize    uint64  `json:"page_size"`
	TotalCount  uint64  `json:"total_count"`
	TotalPage   uint64  `json:"total_page"`
	Items       []*Case `json:"items"`
}

type SearchCaseFailLogReq struct {
	ProjectId  string           `json:"project_id" validate:"required" zh:"项目ID"`
	Condition  *api.Condition   `json:"condition,omitempty,optional" zh:"查询条件"`
	Pagination *api.Pagination  `json:"pagination,omitempty,optional" zh:"查询分页"`
	Sort       []*api.SortField `json:"sort,omitempty,optional" zh:"查询排序"`
}

type SearchCaseFailLogResp struct {
	CurrentPage uint64      `json:"current_page"`
	PageSize    uint64      `json:"page_size"`
	TotalCount  uint64      `json:"total_count"`
	TotalPage   uint64      `json:"total_page"`
	Items       []*FailCase `json:"items"`
}

type DeleteCaseFailLogReq struct {
	ProjectId string `json:"project_id" validate:"required" zh:"项目ID"`
	CaseId    string `json:"case_id"`
	CaseType  string `json:"case_type"`
}

type DeleteCaseFailLogResp struct{}

type BatchDeleteCaseFailLogReq struct {
	ProjectId             string              `json:"project_id" validate:"required" zh:"项目ID"`
	DeleteCaseFailLogList []DeleteCaseFailLog `json:"delete_case_fail_log_list"`
}

type BatchDeleteCaseFailLogResp struct{}

type DeleteCaseFailLog struct {
	CaseId   string `json:"case_id"`
	CaseType string `json:"case_type"`
}
