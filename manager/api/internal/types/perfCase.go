package types

import (
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/userinfo"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/types"
)

type PerfCase struct {
	ProjectId   string `json:"project_id"`
	CaseId      string `json:"case_id"`
	Name        string `json:"name"`
	Description string `json:"description"`
	Extension   string `json:"extension"`
	Hash        string `json:"hash"`
	Size        uint32 `json:"size"`

	types.RateLimit     // 限流配置
	types.BasicPerfData // 压测数据配置
	types.LoadGenerator // 施压机资源配置

	CreatedBy *userinfo.FullUserInfo `json:"created_by"`
	UpdatedBy *userinfo.FullUserInfo `json:"updated_by"`
	CreatedAt int64                  `json:"created_at"`
	UpdatedAt int64                  `json:"updated_at"`
}

type UploadPerfCaseReq struct {
	ProjectId        string `form:"project_id" validate:"required" zh:"项目ID"`
	PerfDataId       string `form:"perf_data_id,optional" zh:"压测数据ID"`
	NumberOfVu       uint32 `form:"number_of_vu,optional,default=0,range=[0:200000]" zh:"虚拟用户数"`
	NumberOfLg       uint32 `form:"number_of_lg,optional,default=0,range=[0:200]" zh:"施压机数量"`
	RequestsOfCpu    string `form:"requests_of_cpu,optional" zh:"最小分配的CPU资源"`
	RequestsOfMemory string `form:"requests_of_memory,optional" zh:"最小分配的内存资源"`
	LimitsOfCpu      string `form:"limits_of_cpu,optional" zh:"最大分配的CPU资源"`
	LimitsOfMemory   string `form:"limits_of_memory,optional" zh:"最大分配的内存资源"`
}

type UploadPerfCaseResp struct {
	*PerfCase
}

type DownloadPerfCaseReq struct {
	ProjectId string `form:"project_id" validate:"required" zh:"项目ID"`
	CaseId    string `form:"case_id" validate:"required" zh:"压测用例ID"`
}

type DownloadPerfCaseResp struct{}

type RemovePerfCaseReq struct {
	ProjectId string   `json:"project_id" validate:"required" zh:"项目ID"`
	CaseIds   []string `json:"case_ids" validate:"gt=0" zh:"压测用例ID列表"`
}

type RemovePerfCaseResp struct{}

type ModifyPerfCaseReq struct {
	ProjectId        string `form:"project_id" validate:"required" zh:"项目ID"`
	CaseId           string `form:"case_id" validate:"required" zh:"压测用例ID"`
	PerfDataId       string `form:"perf_data_id,optional" zh:"压测数据ID"`
	NumberOfVu       uint32 `form:"number_of_vu,optional,default=0,range=[0:200000]" zh:"虚拟用户数"`
	NumberOfLg       uint32 `form:"number_of_lg,optional,default=0,range=[0:200]" zh:"施压机数量"`
	RequestsOfCpu    string `form:"requests_of_cpu,optional" zh:"最小分配的CPU资源"`
	RequestsOfMemory string `form:"requests_of_memory,optional" zh:"最小分配的内存资源"`
	LimitsOfCpu      string `form:"limits_of_cpu,optional" zh:"最大分配的CPU资源"`
	LimitsOfMemory   string `form:"limits_of_memory,optional" zh:"最大分配的内存资源"`
}

type ModifyPerfCaseResp struct{}

type ViewPerfCaseReq struct {
	ProjectId string `form:"project_id" validate:"required" zh:"项目ID"`
	CaseId    string `form:"case_id" validate:"required" zh:"压测用例ID"`
}

type ViewPerfCaseResp struct {
	*PerfCase
}

type CalculatePerfCaseReq struct {
	ProjectId        string `form:"project_id" validate:"required" zh:"项目ID"`
	CaseId           string `form:"case_id" validate:"required" zh:"压测用例ID"`
	PerfDataId       string `form:"perf_data_id,optional" zh:"压测数据ID"`
	NumberOfVu       uint32 `form:"number_of_vu,optional,default=0,range=[0:200000]" zh:"虚拟用户数"`
	NumberOfLg       uint32 `form:"number_of_lg,optional,default=0,range=[0:200]" zh:"施压机数量"`
	RequestsOfCpu    string `form:"requests_of_cpu,optional" zh:"最小分配的CPU资源"`
	RequestsOfMemory string `form:"requests_of_memory,optional" zh:"最小分配的内存资源"`
	LimitsOfCpu      string `form:"limits_of_cpu,optional" zh:"最大分配的CPU资源"`
	LimitsOfMemory   string `form:"limits_of_memory,optional" zh:"最大分配的内存资源"`
}

type CalculatePerfCaseResp struct {
	*PerfCase
}
