package types

import (
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/types"
)

type ModifySlaThresholdReq struct {
	ProjectId      string             `json:"project_id" validate:"required" zh:"项目ID"`
	IosRelease     *types.SLABaseLine `json:"ios_release" validate:"required" zh:"IOS正式分支的阈值配置"`
	AndroidRelease *types.SLABaseLine `json:"android_release" validate:"required" zh:"Android正式分支的阈值配置"`
	AndroidTesting *types.SLABaseLine `json:"android_testing" validate:"required" zh:"Android测试分支的阈值配置"`
}

type ModifySlaThresholdResp struct{}

type GetSlaThresholdReq struct {
	ProjectId string `form:"project_id" validate:"required" zh:"项目ID"`
}

type GetSlaThresholdResp struct {
	IosRelease     *types.SLABaseLine `json:"ios_release" zh:"IOS正式分支的阈值配置"`
	AndroidRelease *types.SLABaseLine `json:"android_release" zh:"Android正式分支的阈值配置"`
	AndroidTesting *types.SLABaseLine `json:"android_testing" zh:"Android测试分支的阈值配置"`
}
