package types

import (
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sqlbuilder/api"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/userinfo"
)

type AccountConfiguration struct {
	ProjectId    string                 `json:"project_id"`
	ConfigId     string                 `json:"config_id"`
	Name         string                 `json:"name"`
	Description  string                 `json:"description"`
	ProductType  int64                  `json:"product_type"`
	ProductName  string                 `json:"product_name"`
	PoolEnvTable string                 `json:"pool_env_table"`
	PoolEnvName  string                 `json:"pool_env_name"`
	CreatedBy    *userinfo.FullUserInfo `json:"created_by"`
	UpdatedBy    *userinfo.FullUserInfo `json:"updated_by"`
	CreatedAt    int64                  `json:"created_at"`
	UpdatedAt    int64                  `json:"updated_at"`
}

type CreateAccountConfigReq struct {
	ProjectId    string `json:"project_id" validate:"required" zh:"项目ID"`
	Name         string `json:"name" validate:"gte=1,lte=64" zh:"池账号配置名称"`
	Description  string `json:"description" validate:"lte=255" zh:"池账号配置描述"`
	ProductType  int64  `json:"product_type" zh:"产品类型"`
	ProductName  string `json:"product_name" validate:"gte=1,lte=64" zh:"产品类型名称"`
	PoolEnvTable string `json:"pool_env_table" validate:"gte=1,lte=64" zh:"账号池环境数据库表名"`
	PoolEnvName  string `json:"pool_env_name" validate:"gte=1,lte=64" zh:"账号池环境名称"`
}

type CreateAccountConfigResp struct {
	ConfigId string `json:"config_id"`
}

type RemoveAccountConfigReq struct {
	ProjectId string   `json:"project_id"`
	ConfigIds []string `json:"config_ids"`
}

type RemoveAccountConfigResp struct{}

type ModifyAccountConfigReq struct {
	ProjectId    string `json:"project_id" validate:"required" zh:"项目ID"`
	ConfigId     string `json:"config_id" validate:"required" zh:"池账号配置ID"`
	Name         string `json:"name" validate:"gte=1,lte=64" zh:"池账号配置名称"`
	Description  string `json:"description" validate:"lte=255" zh:"池账号配置描述"`
	ProductType  int64  `json:"product_type" zh:"产品类型"`
	ProductName  string `json:"product_name" validate:"gte=1,lte=64" zh:"产品类型名称"`
	PoolEnvTable string `json:"pool_env_table" validate:"gte=1,lte=64" zh:"账号池环境数据库表名"`
	PoolEnvName  string `json:"pool_env_name" validate:"gte=1,lte=64" zh:"账号池环境名称"`
}

type ModifyAccountConfigResp struct{}

type SearchAccountConfigReq struct {
	ProjectId  string           `json:"project_id" validate:"required" zh:"项目ID"`
	Condition  *api.Condition   `json:"condition,omitempty,optional" zh:"查询条件"`
	Pagination *api.Pagination  `json:"pagination,omitempty,optional" zh:"查询分页"`
	Sort       []*api.SortField `json:"sort,omitempty,optional" zh:"查询排序"`
}

type SearchAccountConfigResp struct {
	CurrentPage uint64                  `json:"current_page"`
	PageSize    uint64                  `json:"page_size"`
	TotalCount  uint64                  `json:"total_count"`
	TotalPage   uint64                  `json:"total_page"`
	Items       []*AccountConfiguration `json:"items"`
}

type ViewAccountConfigReq struct {
	ProjectId string `form:"project_id" validate:"required" zh:"项目ID"`
	ConfigId  string `form:"config_id" validate:"required" zh:"池账号配置ID"`
}

type ViewAccountConfigResp struct {
	*AccountConfiguration
}
