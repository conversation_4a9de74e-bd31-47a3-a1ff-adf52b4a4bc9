package types

import "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"

type Element interface {
	//*Node | *Edge | *Combo

	ElementId() string
	ElementType() string
}

func (n *Node) ElementId() string {
	return n.Id
}

func (n *Node) ElementType() string {
	return constants.NODE
}

func (e *Edge) ElementId() string {
	return e.Id
}

func (e *Edge) ElementType() string {
	return constants.EDGE
}

func (c *Combo) ElementId() string {
	return c.Id
}

func (c *Combo) ElementType() string {
	return constants.COMBO
}

type Component interface {
	//*Node | *Combo

	ComponentId() string
	ComponentType() string
	ComponentName() string
	ComponentData() map[string]any
}

func (n *Node) ComponentId() string {
	return n.Id
}

func (n *Node) ComponentType() string {
	return n.Type
}

func (n *Node) ComponentName() string {
	return n.Label
}

func (n *Node) ComponentData() map[string]any {
	return n.Data
}

func (c *Combo) ComponentId() string {
	return c.Id
}

func (c *Combo) ComponentType() string {
	return c.Type
}

func (c *Combo) ComponentName() string {
	return c.Label
}

func (c *Combo) ComponentData() map[string]any {
	return c.Data
}

// Argument contains both input and output parameters
type Argument interface {
	*Import | *Export

	ArgName() string
	ArgDesc() string
}

func (i *Import) ArgName() string {
	return i.Name
}

func (i *Import) ArgDesc() string {
	return i.Description
}

func (e *Export) ArgName() string {
	return e.Name
}

func (e *Export) ArgDesc() string {
	return e.Description
}

type SortType interface {
	LessKey() string
}

//func (i *Import) LessKey() string {
//	return i.Name
//}
//
//func (e *Export) LessKey() string {
//	return e.Name
//}

func (n *Node) LessKey() string {
	return n.Id
}

func (e *Edge) LessKey() string {
	return e.Id
}

func (c *Combo) LessKey() string {
	return c.Id
}
