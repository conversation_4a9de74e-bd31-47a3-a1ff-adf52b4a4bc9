package types

import (
	"reflect"
	"sort"
	"testing"
)

func TestSortSlice(t *testing.T) {
	s1 := []string{"1zblhs-KsmlwCR50kc-Vve24UHw4", "1u0xrj-6Z0mYFGsTjnvONDNGRunJ", "62pour-Om4gK9QkchI8HYLwcJy6J", "1kcsyy-PaLPcLCj2O9uzzfiLPe3C", "47la9p-XRAwzJT3I5cJPwBOg2E1L"}
	s2, _ := copySlice(s1)

	rv := reflect.ValueOf(s2)

	t.Logf("src: %+v", s1)
	sort.Slice(s2, func(i, j int) bool {
		return rv.Index(i).String() < rv.Index(j).String()
	})
	t.Logf("dst: %+v", s2)
}
