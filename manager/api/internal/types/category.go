package types

import (
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sqlbuilder/api"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/userinfo"
)

type CategoryNode struct {
	ProjectId    string `json:"project_id"`
	CategoryId   string `json:"category_id"`
	Type         string `json:"type"`
	CategoryType string `json:"category_type"`
	RootType     string `json:"root_type"`
	NodeType     string `json:"node_type"`
	NodeId       string `json:"node_id"`
	Name         string `json:"name"`
	Description  string `json:"description"`
	Builtin      bool   `json:"builtin"`
}

type Category struct {
	ProjectId    string                 `json:"project_id"`
	CategoryId   string                 `json:"category_id"`
	Type         string                 `json:"type"`
	CategoryType string                 `json:"category_type"`
	RootType     string                 `json:"root_type"`
	NodeType     string                 `json:"node_type"`
	NodeId       string                 `json:"node_id"`
	Name         string                 `json:"name"`
	Description  string                 `json:"description"`
	Builtin      bool                   `json:"builtin"`
	CreatedBy    *userinfo.FullUserInfo `json:"created_by"`
	UpdatedBy    *userinfo.FullUserInfo `json:"updated_by"`
	CreatedAt    int64                  `json:"created_at"`
	UpdatedAt    int64                  `json:"updated_at"`
	Amount       int64                  `json:"amount"`
	Children     []*Category            `json:"children"`
}

type CreateCategoryReq struct {
	ProjectId   string `json:"project_id" validate:"required" zh:"项目ID"`
	Type        string `json:"type" validate:"required,oneof=INTERFACE_DOCUMENT INTERFACE_SCHEMA COMPONENT_GROUP API_CASE API_SUITE API_PLAN UI_PLAN PERF_CASE PERF_PLAN STABILITY_PLAN UI_AGENT_COMPONENT" zh:"分类树类型"`
	Name        string `json:"name" validate:"gte=1,lte=64" zh:"分类名称"`
	Description string `json:"description" validate:"lte=255" zh:"分类描述"`
	ParentId    string `json:"parent_id" validate:"required" zh:"父分类ID"`
	Index       int64  `json:"index,default=0" zh:"分类所在层的序号"`
}

type CreateCategoryResp struct {
	CategoryId string `json:"category_id"`
}

type RemoveCategoryReq struct {
	ProjectId  string `json:"project_id" validate:"required" zh:"项目ID"`
	Type       string `json:"type" validate:"required,oneof=INTERFACE_DOCUMENT INTERFACE_SCHEMA COMPONENT_GROUP API_CASE API_SUITE API_PLAN UI_PLAN PERF_CASE PERF_PLAN STABILITY_PLAN UI_AGENT_COMPONENT" zh:"分类树类型"`
	CategoryId string `json:"category_id" validate:"required" zh:"分类ID"`
}

type RemoveCategoryResp struct{}

type ModifyCategoryReq struct {
	ProjectId   string `json:"project_id" validate:"required" zh:"项目ID"`
	Type        string `json:"type" validate:"required,oneof=INTERFACE_DOCUMENT INTERFACE_SCHEMA COMPONENT_GROUP API_CASE API_SUITE API_PLAN UI_PLAN PERF_CASE PERF_PLAN STABILITY_PLAN UI_AGENT_COMPONENT" zh:"分类树类型"`
	CategoryId  string `json:"category_id" validate:"required" zh:"分类ID"`
	Name        string `json:"name" validate:"gte=1,lte=64" zh:"分类名称"`
	Description string `json:"description" validate:"lte=255" zh:"分类描述"`
}

type ModifyCategoryResp struct{}

type SearchCategoryReq struct {
	ProjectId  string           `json:"project_id" validate:"required" zh:"项目ID"`
	Type       string           `json:"type" validate:"required,oneof=INTERFACE_DOCUMENT INTERFACE_SCHEMA COMPONENT_GROUP API_CASE API_SUITE API_PLAN UI_PLAN PERF_CASE PERF_PLAN STABILITY_PLAN" zh:"分类树类型"`
	Condition  *api.Condition   `json:"condition,omitempty,optional" zh:"查询条件"`
	Pagination *api.Pagination  `json:"pagination,omitempty,optional" zh:"查询分页"`
	Sort       []*api.SortField `json:"sort,omitempty,optional" zh:"查询排序"`
}

type SearchCategoryResp struct {
	CurrentPage uint64      `json:"current_page"`
	PageSize    uint64      `json:"page_size"`
	TotalCount  uint64      `json:"total_count"`
	TotalPage   uint64      `json:"total_page"`
	Items       []*Category `json:"items"`
}

type MoveCategoryTreeReq struct {
	ProjectId string `json:"project_id" validate:"required" zh:"项目ID"`
	Type      string `json:"type" validate:"required,oneof=INTERFACE_DOCUMENT INTERFACE_SCHEMA COMPONENT_GROUP API_CASE API_SUITE API_PLAN UI_PLAN PERF_CASE PERF_PLAN STABILITY_PLAN UI_AGENT_COMPONENT" zh:"分类树类型"`
	MoveType  string `json:"move_type" validate:"required,oneof=BEFORE AFTER INNER" zh:"移到类型"`
	SourceId  string `json:"source_id" validate:"required" zh:"源分类ID"`
	TargetId  string `json:"target_id" validate:"required" zh:"目标分类ID"`
	SiblingId string `json:"sibling_id" zh:"兄弟分类ID"`
}

type MoveCategoryTreeResp struct{}

type GetCategoryTreeReq struct {
	ProjectId     string `form:"project_id" validate:"required" zh:"项目ID"`
	Type          string `form:"type" validate:"required,oneof=INTERFACE_DOCUMENT INTERFACE_SCHEMA COMPONENT_GROUP API_CASE API_SUITE API_PLAN UI_PLAN PERF_CASE PERF_PLAN STABILITY_PLAN UI_AGENT_COMPONENT" zh:"分类树类型"`
	CategoryId    string `form:"category_id,optional" zh:"分类ID"`
	Depth         uint32 `form:"depth" validate:"gte=0" zh:"获取分类树的最大深度"`
	OnlyDirectory bool   `form:"only_directory,default=false"`
	IncludeSelf   bool   `form:"include_self,omitempty,optional"`
}

type GetCategoryTreeResp = []*Category
