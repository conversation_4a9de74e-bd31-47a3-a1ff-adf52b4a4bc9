package types

import (
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sqlbuilder/api"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/userinfo"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	reporterpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
)

type (
	FieldName   = string
	FieldSchema = map[FieldName]*Schema
)

type ArraySchema = []*Schema

type (
	StatusCode     = string
	StatusResponse = map[StatusCode]*ResponseData
)

type EnumValDesc struct {
	Value       any    `json:"value"`
	Description string `json:"description"`
}

type RefSchema struct {
	SchemaId     string          `json:"schemaId"`
	FullName     string          `json:"fullName"`
	DisplayName  string          `json:"displayName"`
	CategoryPath []*CategoryNode `json:"categoryPath,omitempty,optional"`
}

type TextDescExample struct {
	Description string `json:"description"`
	Example     string `json:"example"`
}

type InterfaceSchema struct {
	ProjectId   string                 `json:"project_id"`
	CategoryId  string                 `json:"category_id"`
	SchemaId    string                 `json:"schema_id"`
	FullName    string                 `json:"full_name"`
	Name        string                 `json:"name"`
	Description string                 `json:"description"`
	Mode        string                 `json:"mode"`
	ImportType  string                 `json:"import_type"`
	Data        *Schema                `json:"data"`
	CreatedBy   *userinfo.FullUserInfo `json:"created_by"`
	UpdatedBy   *userinfo.FullUserInfo `json:"updated_by"`
	CreatedAt   int64                  `json:"created_at"`
	UpdatedAt   int64                  `json:"updated_at"`
}

type Schema struct {
	Title            string                `json:"title,omitempty,optional" zh:"字段名称"`
	Type             string                `json:"type" validate:"required,oneof=string integer number boolean array object null any allOf anyOf oneOf custom schema file" zh:"字段类型"`
	Description      string                `json:"description,omitempty,optional" zh:"字段描述"`
	Index            int32                 `json:"index,omitempty,optional" zh:"字段序号"`
	FieldRequired    bool                  `json:"fieldRequired,optional" zh:"字段是否必需"` // 前端要求此字段必须返回
	Deprecated       bool                  `json:"deprecated,omitempty,optional" zh:"字段是否不建议使用"`
	Default          any                   `json:"default,omitempty,optional" zh:"字段默认值"`
	Example          any                   `json:"example,omitempty,optional" zh:"字段样例"`
	Format           string                `json:"format,omitempty,optional" validate:"omitempty,oneof=int32 int64 float double password date-time date time duration email hostname ipv4 ipv6 uri regex" zh:"字段值格式"`
	MultipleOf       int32                 `json:"multipleOf,omitempty,optional"`
	Maximum          float64               `json:"maximum,omitempty,optional"`
	ExclusiveMaximum float64               `json:"exclusiveMaximum,omitempty,optional"`
	Minimum          float64               `json:"minimum,omitempty,optional"`
	ExclusiveMinimum float64               `json:"exclusiveMinimum,omitempty,optional"`
	MaxLength        int32                 `json:"maxLength,omitempty,optional"`
	MinLength        int32                 `json:"minLength,omitempty,optional"`
	Pattern          string                `json:"pattern,omitempty,optional"`
	MaxItems         int32                 `json:"maxItems,omitempty,optional"`
	MinItems         int32                 `json:"minItems,omitempty,optional"`
	UniqueItems      bool                  `json:"uniqueItems,omitempty,optional"`
	Enum             []any                 `json:"enum,omitempty,optional"`
	Enums            []EnumValDesc         `json:"enums,omitempty,optional"`
	Items            *Schema               `json:"items,omitempty,optional"`
	Ref              *RefSchema            `json:"ref,omitempty,optional"`
	Properties       map[FieldName]*Schema `json:"properties,omitempty,optional"`
	Required         []string              `json:"required,omitempty,optional"`
	SortKey          string                `json:"sortKey,omitempty,optional"` // 前端创建的字段
	Raw              string                `json:"raw,omitempty,optional"`
}

type InterfaceDocument struct {
	ProjectId    string                 `json:"project_id"`
	CategoryId   string                 `json:"category_id"`
	DocumentId   string                 `json:"document_id"`
	Name         string                 `json:"name"`
	Description  string                 `json:"description"`
	Type         string                 `json:"type"`
	Mode         string                 `json:"mode"`
	ImportType   string                 `json:"import_type"`
	Status       int64                  `json:"status,omitempty"`
	Priority     int64                  `json:"priority"`
	Tags         []string               `json:"tags"`
	State        int8                   `json:"state"`
	Service      string                 `json:"service"`
	Path         string                 `json:"path"`
	Method       string                 `json:"method"`
	Data         *Document              `json:"data"`
	MaintainedBy *userinfo.FullUserInfo `json:"maintained_by"`
	CreatedBy    *userinfo.FullUserInfo `json:"created_by"`
	UpdatedBy    *userinfo.FullUserInfo `json:"updated_by"`
	CreatedAt    int64                  `json:"created_at"`
	UpdatedAt    int64                  `json:"updated_at"`
}

type Document struct {
	Headers     ArraySchema    `json:"headers,omitempty,optional" zh:"请求头信息"`
	PathParams  ArraySchema    `json:"path_params,omitempty,optional" zh:"路径参数信息"`
	QueryParams ArraySchema    `json:"query_params,omitempty,optional" zh:"查询参数信息"`
	Body        *BodyData      `json:"body,omitempty,optional" zh:"请求体信息"`
	Responses   StatusResponse `json:"responses,omitempty,optional" zh:"响应信息"`
}

type BodyData struct {
	Type string           `json:"type" validate:"required" zh:"请求体类型"`
	Form ArraySchema      `json:"form,omitempty,optional" zh:"请求体表单信息"`
	Json *Schema          `json:"json,omitempty,optional" zh:"请求体JSON信息"`
	Text *TextDescExample `json:"text,omitempty,optional" zh:"请求体文本信息"`
}

type ResponseData struct {
	StatusCode  string      `json:"status_code" zh:"状态码"`
	Description string      `json:"description" zh:"响应描述"`
	Headers     ArraySchema `json:"headers,omitempty,optional" zh:"响应头信息"`
	Body        *BodyData   `json:"body,omitempty,optional" zh:"响应体信息"`
}

type InterfaceConfig struct {
	ProjectId        string                 `json:"project_id"`
	DocumentId       string                 `json:"document_id"`
	ConfigId         string                 `json:"config_id"`
	Name             string                 `json:"name"`
	Description      string                 `json:"description"`
	Path             string                 `json:"path"`
	Method           string                 `json:"method"`
	Data             *Config                `json:"data"`
	InputParameters  []*InputParameter      `json:"imports"`
	OutputParameters []*OutputParameter     `json:"exports"`
	CreatedBy        *userinfo.FullUserInfo `json:"created_by"`
	UpdatedBy        *userinfo.FullUserInfo `json:"updated_by"`
	CreatedAt        int64                  `json:"created_at"`
	UpdatedAt        int64                  `json:"updated_at"`
}

type Config = Document

type InputParameter struct {
	Name        string                   `json:"name" validate:"required" zh:"入参变量名称"`
	Description string                   `json:"description,optional" validate:"omitempty" zh:"入参变量描述"`
	Source      int8                     `json:"source" validate:"oneof=0 1 2 3" zh:"来源"`
	Manual      *types.VariableValue     `json:"manual" validate:"omitempty,structonly" zh:"手工填写"`
	Export      *types.VariableNodeValue `json:"export" validate:"omitempty,structonly" zh:"前面节点导出"`
	Environment *types.VariableValue     `json:"environment" validate:"omitempty,structonly" zh:"通用配置"`
	Function    *types.VariableFuncValue `json:"function" validate:"omitempty,structonly" zh:"函数处理"`
}

type OutputParameter struct {
	Name        string                     `json:"name" validate:"required" zh:"出参变量名称"`
	Description string                     `json:"description,optional" validate:"omitempty" zh:"出参变量描述"`
	Source      int8                       `json:"source" validate:"oneof=0 1 2" zh:"来源"`
	Headers     *types.VariableHeaderValue `json:"headers" validate:"omitempty,structonly" zh:"响应头"`
	Body        *types.VariableBodyValue   `json:"body" validate:"omitempty,structonly" zh:"响应体"`
}

type ExecutionRecord struct {
	*reporterpb.GetCaseLatestRecordResponse_RecordCaseRecord
}

type InterfaceCase struct {
	ProjectId     string                 `json:"project_id"`
	DocumentId    string                 `json:"document_id"`
	CaseId        string                 `json:"case_id"`
	Name          string                 `json:"name"`
	Description   string                 `json:"description"`
	Priority      int64                  `json:"priority"`
	Tags          []string               `json:"tags"`
	State         common.ResourceState   `json:"state"`
	AccountConfig *types.AccountConfig   `json:"account_config"`
	Version       string                 `json:"version"`
	LatestRecord  *ExecutionRecord       `json:"latest_record"`
	MaintainedBy  *userinfo.FullUserInfo `json:"maintained_by"`
	CreatedBy     *userinfo.FullUserInfo `json:"created_by"`
	UpdatedBy     *userinfo.FullUserInfo `json:"updated_by"`
	CreatedAt     int64                  `json:"created_at"`
	UpdatedAt     int64                  `json:"updated_at"`
	Nodes         []*Node                `json:"nodes"`
	Edges         []*Edge                `json:"edges"`
	Combos        []*Combo               `json:"combos"`
}

type SearchInterfaceDocumentReferenceItem struct {
	ProjectId      string                 `json:"project_id"`      // 项目ID
	DocumentId     string                 `json:"document_id"`     // 接口ID
	ReferenceType  string                 `json:"reference_type"`  // 引用对象类型
	ReferenceId    string                 `json:"reference_id"`    // 引用对象ID
	Name           string                 `json:"name"`            // 引用对象名称
	Description    string                 `json:"description"`     // 引用对象描述
	Priority       int64                  `json:"priority"`        // 优先级
	Tags           []string               `json:"tags"`            // 标签
	State          int8                   `json:"state"`           // 引用对象状态
	ReferenceState int8                   `json:"reference_state"` // 集合的引用状态
	MaintainedBy   *userinfo.FullUserInfo `json:"maintained_by"`   // 维护者
	CreatedBy      *userinfo.FullUserInfo `json:"created_by"`      // 创建者
	UpdatedBy      *userinfo.FullUserInfo `json:"updated_by"`      // 更新者
	CreatedAt      int64                  `json:"created_at"`      // 创建时间
	UpdatedAt      int64                  `json:"updated_at"`      // 更新时间
}

type SearchInterfaceCaseReferenceItem struct {
	ProjectId     string                 `json:"project_id"`     // 项目ID
	CaseId        string                 `json:"case_id"`        // 用例ID
	ReferenceType string                 `json:"reference_type"` // 引用对象类型
	ReferenceId   string                 `json:"reference_id"`   // 引用对象ID
	Name          string                 `json:"name"`           // 引用对象名称
	Description   string                 `json:"description"`    // 引用对象描述
	Priority      int64                  `json:"priority"`       // 优先级
	Tags          []string               `json:"tags"`           // 标签
	State         int8                   `json:"state"`          // 引用对象状态
	MaintainedBy  *userinfo.FullUserInfo `json:"maintained_by"`  // 维护者
	CreatedBy     *userinfo.FullUserInfo `json:"created_by"`     // 创建者
	UpdatedBy     *userinfo.FullUserInfo `json:"updated_by"`     // 更新者
	CreatedAt     int64                  `json:"created_at"`     // 创建时间
	UpdatedAt     int64                  `json:"updated_at"`     // 更新时间
}

type InterfaceCoverageData struct {
	NumberOfApis  []uint32 `json:"number_of_apis"`  // 接口数量
	NumberOfCases []uint32 `json:"number_of_cases"` // 用例数量
	CountedAt     []string `json:"counted_at"`      // 统计日期
}

// 接口导入相关的接口定义
type (
	Target struct {
		GitUrl string `json:"git_url,omitempty,optional" zh:"git地址"`
		Branch string `json:"branch,default=master" zh:"分支"`
		Path   string `json:"path,omitempty,optional" zh:"本地路径"`
	}
	LocalImportInterfaceDefinitionReq struct {
		ProjectId    string   `json:"project_id" validate:"required" zh:"项目ID"`
		Type         string   `json:"type" validate:"required,oneof=OpenApi gRPC YApi TT TTMeta Recommend" zh:"接口类型"`
		Target       Target   `json:"target" validate:"required" zh:"导入目标"`
		Dependencies []Target `json:"dependencies,omitempty,optional" validate:"gte=0,dive,required" zh:"导入目标关联的依赖"`
	}

	LocalImportInterfaceDefinitionResp struct {
		Document *common.Statistic `json:"document"`
		Schema   *common.Statistic `json:"schema"`
	}
)

type (
	RemoteImportInterfaceDefinitionReq  struct{}
	RemoteImportInterfaceDefinitionResp struct{}
)

// 接口文档相关的接口定义
type (
	CreateInterfaceDocumentReq struct {
		ProjectId    string    `json:"project_id" validate:"required" zh:"项目ID"`
		CategoryId   string    `json:"category_id" validate:"required" zh:"分类ID"`
		Name         string    `json:"name" validate:"gte=1,lte=64" zh:"接口名称"`
		Description  string    `json:"description" validate:"lte=255" zh:"接口描述"`
		Type         string    `json:"type" validate:"required,oneof=HTTP gRPC" zh:"接口类型"`
		Mode         string    `json:"mode,omitempty,optional,default=manual" validate:"omitempty,oneof=manual plugin" zh:"创建方式"`
		Status       int64     `json:"status" validate:"gte=1" zh:"接口状态"`
		Priority     int64     `json:"priority,optional,default=0" validate:"gte=0" zh:"优先级"`
		Tags         []string  `json:"tags" validate:"gte=0" zh:"接口标签"`
		Service      string    `json:"service" validate:"lte=64" zh:"服务名称"`
		Path         string    `json:"path" validate:"gte=1,lte=128" zh:"接口路径"`
		Method       string    `json:"method" validate:"gte=1,lte=64" zh:"接口方法"`
		Data         *Document `json:"data" validate:"required" zh:"接口详细数据"`
		MaintainedBy string    `json:"maintained_by" validate:"lte=64" zh:"接口维护者"`
	}
	CreateInterfaceDocumentResp struct {
		DocumentId string `json:"document_id"`
	}
)

type (
	RemoveInterfaceDocumentReq struct {
		ProjectId   string   `json:"project_id" validate:"required" zh:"项目ID"`
		DocumentIds []string `json:"document_ids" validate:"gt=0" zh:"接口文档ID列表"`
	}
	RemoveInterfaceDocumentResp struct{}
)

type (
	ModifyInterfaceDocumentReq struct {
		ProjectId    string    `json:"project_id" validate:"required" zh:"项目ID"`
		CategoryId   string    `json:"category_id" validate:"required" zh:"分类ID"`
		DocumentId   string    `json:"document_id" validate:"required" zh:"接口文档ID"`
		Name         string    `json:"name" validate:"gte=1,lte=64" zh:"接口名称"`
		Description  string    `json:"description" validate:"lte=255" zh:"接口描述"`
		Status       int64     `json:"status" validate:"gte=1" zh:"接口状态"`
		Priority     int64     `json:"priority,optional,default=0" validate:"gte=0" zh:"优先级"`
		Tags         []string  `json:"tags" validate:"gte=0" zh:"接口标签"`
		State        int8      `json:"state" validate:"oneof=1 2" zh:"接口集合状态"`
		Service      string    `json:"service" validate:"lte=64" zh:"服务名称"`
		Path         string    `json:"path" validate:"gte=1,lte=128" zh:"接口路径"`
		Method       string    `json:"method" validate:"gte=1,lte=64" zh:"接口方法"`
		Data         *Document `json:"data" validate:"required" zh:"接口详细数据"`
		MaintainedBy string    `json:"maintained_by" validate:"lte=64" zh:"接口维护者"`
	}
	ModifyInterfaceDocumentResp struct{}
)

type (
	SearchInterfaceDocumentReq struct {
		SearchByCategoryId
	}
	SearchInterfaceDocumentResp struct {
		CurrentPage uint64               `json:"current_page"`
		PageSize    uint64               `json:"page_size"`
		TotalCount  uint64               `json:"total_count"`
		TotalPage   uint64               `json:"total_page"`
		Items       []*InterfaceDocument `json:"items"`
	}
)

type (
	ViewInterfaceDocumentReq struct {
		ProjectId  string `form:"project_id" validate:"required" zh:"项目ID"`
		DocumentId string `form:"document_id" validate:"required" zh:"接口文档ID"`
	}
	ViewInterfaceDocumentResp struct {
		*InterfaceDocument
	}
)

type (
	MockInterfaceDocumentReq struct {
		ProjectId  string `form:"project_id" validate:"required" zh:"项目ID"`
		DocumentId string `form:"document_id" validate:"required" zh:"接口定义ID"`
		WithConfig int8   `form:"with_config" validate:"oneof=0 1 2" zh:"是否使用接口配置"`
		ConfigId   string `form:"config_id,optional" validate:"required_if=WithConfig 2" zh:"接口配置ID"`
	}
	MockInterfaceDocumentResp struct {
		*HttpRequestComponent
	}
)

type (
	SearchInterfaceDocumentReferenceReq struct {
		ProjectId  string           `json:"project_id" validate:"required" zh:"项目ID"`
		DocumentId string           `json:"document_id" validate:"required" zh:"接口ID"`
		Condition  *api.Condition   `json:"condition,omitempty,optional" zh:"查询条件"`
		Pagination *api.Pagination  `json:"pagination,omitempty,optional" zh:"查询分页"`
		Sort       []*api.SortField `json:"sort,omitempty,optional" zh:"查询排序"`
	}
	SearchInterfaceDocumentReferenceResp struct {
		CurrentPage uint64                                  `json:"current_page"`
		PageSize    uint64                                  `json:"page_size"`
		TotalCount  uint64                                  `json:"total_count"`
		TotalPage   uint64                                  `json:"total_page"`
		Items       []*SearchInterfaceDocumentReferenceItem `json:"items"`
	}
)

type (
	ModifyInterfaceDocumentReferenceStateReq struct {
		ProjectId  string   `json:"project_id" validate:"required" zh:"项目ID"`
		DocumentId string   `json:"document_id" validate:"required" zh:"接口集合ID"`
		PlanIds    []string `json:"plan_ids" validate:"gt=0" zh:"API计划ID列表"`
		State      int8     `json:"state" validate:"oneof=1 2" zh:"接口集合引用状态"`
	}
	ModifyInterfaceDocumentReferenceStateResp struct{}
)

// 接口数据模型相关的接口定义
type (
	CreateInterfaceSchemaReq struct {
		ProjectId   string  `json:"project_id" validate:"required" zh:"项目ID"`
		CategoryId  string  `json:"category_id" validate:"required" zh:"分类ID"`
		FullName    string  `json:"full_name" validate:"gte=1,lte=128" zh:"数据模型完整名称"`
		Name        string  `json:"name" validate:"gte=1,lte=64" zh:"数据模型名称"`
		Description string  `json:"description" validate:"lte=255" zh:"数据模型描述"`
		Data        *Schema `json:"data" validate:"required" zh:"数据模型数据"`
	}
	CreateInterfaceSchemaResp struct {
		SchemaId string `json:"schema_id"`
	}
)

type (
	RemoveInterfaceSchemaReq struct {
		ProjectId string   `json:"project_id" validate:"required" zh:"项目ID"`
		SchemaIds []string `json:"schema_ids" validate:"gt=0" zh:"接口数据模型ID列表"`
	}
	RemoveInterfaceSchemaResp struct{}
)

type (
	ModifyInterfaceSchemaReq struct {
		ProjectId   string  `json:"project_id" validate:"required" zh:"项目ID"`
		CategoryId  string  `json:"category_id" validate:"required" zh:"分类ID"`
		SchemaId    string  `json:"schema_id" validate:"required" zh:"数据模型ID"`
		Name        string  `json:"name" validate:"gte=1,lte=64" zh:"数据模型名称"`
		Description string  `json:"description" validate:"lte=255" zh:"数据模型描述"`
		Data        *Schema `json:"data" validate:"required" zh:"数据模型数据"`
	}
	ModifyInterfaceSchemaResp struct{}
)

type (
	SearchInterfaceSchemaReq struct {
		SearchByCategoryId
	}
	SearchInterfaceSchemaResp struct {
		CurrentPage uint64             `json:"current_page"`
		PageSize    uint64             `json:"page_size"`
		TotalCount  uint64             `json:"total_count"`
		TotalPage   uint64             `json:"total_page"`
		Items       []*InterfaceSchema `json:"items"`
	}
)

type (
	ViewInterfaceSchemaReq struct {
		ProjectId string `form:"project_id" validate:"required" zh:"项目ID"`
		SchemaId  string `form:"schema_id" validate:"required" zh:"接口数据模型ID"`
	}
	ViewInterfaceSchemaResp struct {
		*InterfaceSchema
	}
)

// 接口配置相关的接口定义
type (
	CreateInterfaceConfigReq struct {
		ProjectId        string             `json:"project_id" validate:"required" zh:"项目ID"`
		DocumentId       string             `json:"document_id" validate:"required" zh:"接口ID"`
		Name             string             `json:"name" validate:"gte=1,lte=64" zh:"接口配置名称"`
		Description      string             `json:"description" validate:"lte=255" zh:"接口配置描述"`
		Path             string             `json:"path" validate:"gte=1,lte=128" zh:"接口配置的请求路径"`
		Method           string             `json:"method" validate:"gte=1,lte=64" zh:"接口配置的请求方法"`
		Data             *Config            `json:"data" validate:"required" zh:"接口配置的数据"`
		InputParameters  []*InputParameter  `json:"imports" validate:"required,gte=0,dive,required" zh:"接口配置的输入参数"`
		OutputParameters []*OutputParameter `json:"exports" validate:"required,gte=0,dive,required" zh:"接口配置的输出参数"`
	}
	CreateInterfaceConfigResp struct {
		ConfigId string `json:"config_id"`
	}
)

type (
	RemoveInterfaceConfigReq struct {
		ProjectId  string   `json:"project_id" validate:"required" zh:"项目ID"`
		DocumentId string   `json:"document_id" validate:"required" zh:"接口ID"`
		ConfigIds  []string `json:"config_ids" validate:"gt=0" zh:"接口配置ID列表"`
	}
	RemoveInterfaceConfigResp struct{}
)

type (
	ModifyInterfaceConfigReq struct {
		ProjectId        string             `json:"project_id" validate:"required" zh:"项目ID"`
		DocumentId       string             `json:"document_id" validate:"required" zh:"接口ID"`
		ConfigId         string             `json:"config_id" validate:"required" zh:"接口配置ID"`
		Name             string             `json:"name" validate:"gte=1,lte=64" zh:"接口配置名称"`
		Description      string             `json:"description" validate:"lte=255" zh:"接口配置描述"`
		Path             string             `json:"path" validate:"gte=1,lte=128" zh:"接口配置的请求路径"`
		Method           string             `json:"method" validate:"gte=1,lte=64" zh:"接口配置的请求方法"`
		Data             *Config            `json:"data" validate:"required" zh:"接口配置的数据"`
		InputParameters  []*InputParameter  `json:"imports" validate:"required,gte=0,dive,required" zh:"接口配置的输入参数"`
		OutputParameters []*OutputParameter `json:"exports" validate:"required,gte=0,dive,required" zh:"接口配置的输出参数"`
	}
	ModifyInterfaceConfigResp struct{}
)

type (
	SearchInterfaceConfigReq struct {
		ProjectId  string           `json:"project_id" validate:"required" zh:"项目ID"`
		DocumentId string           `json:"document_id" validate:"required" zh:"接口ID"`
		Condition  *api.Condition   `json:"condition,omitempty,optional" zh:"查询条件"`
		Pagination *api.Pagination  `json:"pagination,omitempty,optional" zh:"查询分页"`
		Sort       []*api.SortField `json:"sort,omitempty,optional" zh:"查询排序"`
	}
	SearchInterfaceConfigResp struct {
		CurrentPage uint64             `json:"current_page"`
		PageSize    uint64             `json:"page_size"`
		TotalCount  uint64             `json:"total_count"`
		TotalPage   uint64             `json:"total_page"`
		Items       []*InterfaceConfig `json:"items"`
	}
)

type (
	ViewInterfaceConfigReq struct {
		ProjectId  string `form:"project_id" validate:"required" zh:"项目ID"`
		DocumentId string `form:"document_id" validate:"required" zh:"接口ID"`
		ConfigId   string `form:"config_id" validate:"required" zh:"接口配置ID"`
	}
	ViewInterfaceConfigResp struct {
		*InterfaceConfig
	}
)

// 接口用例相关的接口定义

type (
	CreateInterfaceCaseReq struct {
		ProjectId     string               `json:"project_id" validate:"required" zh:"项目ID"`
		DocumentId    string               `json:"document_id" validate:"required" zh:"接口ID"`
		Name          string               `json:"name" validate:"gte=1,lte=64" zh:"接口用例名称"`
		Description   string               `json:"description" validate:"lte=255" zh:"接口用例描述"`
		Priority      int64                `json:"priority" validate:"gte=0" zh:"优先级"`
		Tags          []string             `json:"tags" validate:"gte=0" zh:"接口用例标签"`
		AccountConfig *types.AccountConfig `json:"account_config" zh:"接口用例池账号配置数"`
		Nodes         []*Node              `json:"nodes" validate:"gt=2" zh:"接口用例画布中的节点列表"`
		Edges         []*Edge              `json:"edges" validate:"gt=1" zh:"接口用例画布中的线段列表"`
		Combos        []*Combo             `json:"combos" validate:"gte=0" zh:"接口用例画布中的组合列表"`
		Relations     []*types.Relation    `json:"relations" validate:"gte=2" zh:"接口用例画布中的节点与组合的关系列表"`
		MaintainedBy  string               `json:"maintained_by" validate:"lte=64" zh:"接口用例维护者"`
	}
	CreateInterfaceCaseResp struct {
		CaseId string `json:"case_id"`
	}
)

type (
	RemoveInterfaceCaseReq struct {
		ProjectId  string   `json:"project_id" validate:"required" zh:"项目ID"`
		DocumentId string   `json:"document_id" validate:"required" zh:"接口ID"`
		CaseIds    []string `json:"case_ids" validate:"gt=0" zh:"接口用例ID列表"`
	}
	RemoveInterfaceCaseResp struct{}
)

type (
	ModifyInterfaceCaseReq struct {
		ProjectId   string   `json:"project_id" validate:"required" zh:"项目ID"`
		DocumentId  string   `json:"document_id" validate:"required" zh:"接口ID"`
		CaseId      string   `json:"case_id" validate:"required" zh:"接口用例ID"`
		Name        string   `json:"name" validate:"gte=1,lte=64" zh:"接口用例名称"`
		Description string   `json:"description" validate:"lte=255" zh:"接口用例描述"`
		Priority    int64    `json:"priority" validate:"gte=0" zh:"优先级"`
		Tags        []string `json:"tags" validate:"gte=0" zh:"接口用例标签"`
		// State         int8                  `json:"state" validate:"oneof=1 2" zh:"接口用例状态"`
		AccountConfig *types.AccountConfig `json:"account_config" zh:"接口用例池账号配置数"`
		Nodes         []*Node              `json:"nodes" validate:"gt=2" zh:"接口用例画布中的节点列表"`
		Edges         []*Edge              `json:"edges" validate:"gt=1" zh:"接口用例画布中的线段列表"`
		Combos        []*Combo             `json:"combos" validate:"gte=0" zh:"接口用例画布中的组合列表"`
		Relations     []*types.Relation    `json:"relations" validate:"gte=2" zh:"接口用例画布中的节点与组合的关系列表"`
		MaintainedBy  string               `json:"maintained_by" validate:"lte=64" zh:"接口用例维护者"`
	}
	ModifyInterfaceCaseResp struct{}
)

type (
	SearchInterfaceCaseReq struct {
		ProjectId  string           `json:"project_id" validate:"required" zh:"项目ID"`
		DocumentId string           `json:"document_id" validate:"required" zh:"接口ID"`
		Condition  *api.Condition   `json:"condition,omitempty,optional" zh:"查询条件"`
		Pagination *api.Pagination  `json:"pagination,omitempty,optional" zh:"查询分页"`
		Sort       []*api.SortField `json:"sort,omitempty,optional" zh:"查询排序"`
	}
	SearchInterfaceCaseResp struct {
		CurrentPage uint64           `json:"current_page"`
		PageSize    uint64           `json:"page_size"`
		TotalCount  uint64           `json:"total_count"`
		TotalPage   uint64           `json:"total_page"`
		Items       []*InterfaceCase `json:"items"`
	}
)

type (
	ViewInterfaceCaseReq struct {
		ProjectId  string `form:"project_id" validate:"required" zh:"项目ID"`
		DocumentId string `form:"document_id,omitempty,optional" zh:"接口ID"`
		CaseId     string `form:"case_id" validate:"required" zh:"接口用例ID"`
		Version    string `form:"version,omitempty,optional" zh:"接口用例版本"`
	}
	ViewInterfaceCaseResp struct {
		*InterfaceCase
	}
)

type (
	MaintainInterfaceCaseReq struct {
		ProjectId    string `json:"project_id" validate:"required" zh:"项目ID"`
		DocumentId   string `json:"document_id" validate:"required" zh:"接口ID"`
		CaseId       string `json:"case_id" validate:"required" zh:"接口用例ID"`
		MaintainedBy string `json:"maintained_by" validate:"lte=64" zh:"接口用例维护者"`
	}
	MaintainInterfaceCaseResp struct{}
)

type (
	PublishInterfaceCaseReq struct {
		ProjectId  string `json:"project_id" validate:"required" zh:"项目ID"`
		DocumentId string `json:"document_id" validate:"required" zh:"接口ID"`
		CaseId     string `json:"case_id" validate:"required" zh:"接口用例ID"`
	}
	PublishInterfaceCaseResp struct{}
)

type (
	SearchInterfaceCaseReferenceReq struct {
		ProjectId  string           `json:"project_id" validate:"required" zh:"项目ID"`
		CaseId     string           `json:"case_id" validate:"required" zh:"接口用例ID"`
		Condition  *api.Condition   `json:"condition,omitempty,optional" zh:"查询条件"`
		Pagination *api.Pagination  `json:"pagination,omitempty,optional" zh:"查询分页"`
		Sort       []*api.SortField `json:"sort,omitempty,optional" zh:"查询排序"`
	}
	SearchInterfaceCaseReferenceResp struct {
		CurrentPage uint64                              `json:"current_page"`
		PageSize    uint64                              `json:"page_size"`
		TotalCount  uint64                              `json:"total_count"`
		TotalPage   uint64                              `json:"total_page"`
		Items       []*SearchInterfaceCaseReferenceItem `json:"items"`
	}
)

type (
	GetInterfaceCoverageTeamsReq struct {
		ProjectId string `form:"project_id" validate:"required" zh:"项目ID"`
	}
	GetInterfaceCoverageTeamsResp struct {
		Teams []string `json:"teams"`
	}
)

type (
	GetInterfaceCoverageDataReq struct {
		ProjectId string `form:"project_id" validate:"required" zh:"项目ID"`
		Team      string `form:"team" validate:"required" zh:"团队名称"`
		From      string `form:"from" validate:"required,datetime=2006-01-02" zh:"开始日期"`
		To        string `form:"to" validate:"required,datetime=2006-01-02" zh:"结束日期"`
	}
	GetInterfaceCoverageDataResp struct {
		*InterfaceCoverageData
	}
)
