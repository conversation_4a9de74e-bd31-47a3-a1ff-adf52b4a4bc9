package types

/*
IMPORTANT: 这个文件下的全部定义必须跟`manager.pb.go`中跟`HttpRequestComponent`相关的定义完全同步
原因：gRPC生成的定义都带上`omitempty`，但有些这段前端要求必须返回，因此在这里重复定义，只把`omitempty`去掉
*/

type HttpRequestComponentAuthorizationType = int32

const (
	ConstHttpRequestComponentAuthorizationNoAuth      HttpRequestComponentAuthorizationType = 0
	ConstHttpRequestComponentAuthorizationApiKey      HttpRequestComponentAuthorizationType = 1
	ConstHttpRequestComponentAuthorizationBearerToken HttpRequestComponentAuthorizationType = 2
	ConstHttpRequestComponentAuthorizationBasicAuth   HttpRequestComponentAuthorizationType = 3
)

type HttpRequestComponentAuthorizationApiKeyAddTo = int32

const (
	ConstHttpRequestComponentAuthorizationApiKeyHeaders     HttpRequestComponentAuthorizationApiKeyAddTo = 0
	ConstHttpRequestComponentAuthorizationApiKeyQueryParams HttpRequestComponentAuthorizationApiKeyAddTo = 1
)

type HttpRequestComponentBodyContentType = int32

const (
	ConstHttpRequestComponentBodyNone                      HttpRequestComponentBodyContentType = 0 // none
	ConstHttpRequestComponentBodyMultipartFormData         HttpRequestComponentBodyContentType = 1 // multipart/form-data
	ConstHttpRequestComponentBodyApplicationFormUrlencoded HttpRequestComponentBodyContentType = 2 // application/x-www-form-urlencoded
	ConstHttpRequestComponentBodyTextPlain                 HttpRequestComponentBodyContentType = 3 // raw: text/plain
	ConstHttpRequestComponentBodyApplicationJson           HttpRequestComponentBodyContentType = 4 // raw: application/json
)

// HttpRequestComponentResponseSource 响应信息提取来源
type HttpRequestComponentResponseSource = int32

const (
	ConstHttpRequestComponentResponseSourceHeaders    HttpRequestComponentResponseSource = 0
	ConstHttpRequestComponentResponseSourceBody       HttpRequestComponentResponseSource = 1
	ConstHttpRequestComponentResponseSourceStatusCode HttpRequestComponentResponseSource = 2
)

type HttpRequestComponentAssertionBodyType = int32

const (
	ConstHttpRequestComponentAssertionBodyJmespath HttpRequestComponentAssertionBodyType = 0 // JMESPath
	ConstHttpRequestComponentAssertionBodyRegex    HttpRequestComponentAssertionBodyType = 1 // 正则
)

type HttpRequestComponentExportBodyType = int32

const (
	ConstHttpRequestComponentExportBodyJmespath HttpRequestComponentExportBodyType = 0 // JMESPath
	ConstHttpRequestComponentExportBodyRegex    HttpRequestComponentExportBodyType = 1 // 正则
)

// HttpRequestComponent HTTP请求组件
type HttpRequestComponent struct {
	Url           string                              `json:"url"`    // HTTP请求Url（注：由于这里可能使用到变量，因此不能使用`uri_ref`进行验证）
	Method        string                              `json:"method"` // HTTP请求Method
	Authorization *HttpRequestComponentAuthorization  `json:"authorization"`
	Headers       []*HttpRequestComponentKeyValueDesc `json:"headers"`
	QueryParams   []*HttpRequestComponentKeyValueDesc `json:"query_params"`
	Body          *HttpRequestComponentBody           `json:"body"`
	Timeout       *HttpRequestComponentTimeout        `json:"timeout"`
	Assertions    []*HttpRequestComponentAssertion    `json:"assertions"`
	Imports       []*HttpRequestComponentImport       `json:"imports"`
	Exports       []*HttpRequestComponentExport       `json:"exports"`
}

type HttpRequestComponentAuthorization struct {
	Type        HttpRequestComponentAuthorizationType         `json:"type"` // 授权类型
	ApiKey      *HttpRequestComponentAuthorizationApiKey      `json:"api_key"`
	BearerToken *HttpRequestComponentAuthorizationBearerToken `json:"bearer_token"`
	BasicAuth   *HttpRequestComponentAuthorizationBasicAuth   `json:"basic_auth"`
}

type HttpRequestComponentAuthorizationApiKey struct {
	Key   string                                       `json:"key"`
	Value string                                       `json:"value"`
	AddTo HttpRequestComponentAuthorizationApiKeyAddTo `json:"add_to"`
}

type HttpRequestComponentAuthorizationBearerToken struct {
	Token string `json:"token"`
}

type HttpRequestComponentAuthorizationBasicAuth struct {
	Username string `json:"username"`
	Password string `json:"password"`
}

type HttpRequestComponentKeyValueDesc struct {
	Key         string `json:"key"`         // 键
	Value       string `json:"value"`       // 值
	Description string `json:"description"` // 描述
}

type HttpRequestComponentBody struct {
	Type     HttpRequestComponentBodyContentType `json:"type"`
	FormData []*HttpRequestComponentKeyValueDesc `json:"form_data"`
	Raw      string                              `json:"raw"`
}

type HttpRequestComponentTimeout struct {
	ConnectTimeout  int64 `json:"connect_timeout"`  // 连接超时时间
	RequestTimeout  int64 `json:"request_timeout"`  // 请求超时时间
	ResponseTimeout int64 `json:"response_timeout"` // 响应超时时间
}

type HttpRequestComponentAssertion struct {
	Source     HttpRequestComponentResponseSource       `json:"source"`      // 来源
	Headers    *HttpRequestComponentAssertionHeaders    `json:"headers"`     // 响应头
	Body       *HttpRequestComponentAssertionBody       `json:"body"`        // 响应体
	StatusCode *HttpRequestComponentAssertionStatusCode `json:"status_code"` // 响应状态码
}

type HttpRequestComponentAssertionHeaders struct {
	Key        string `json:"key"`        // 响应头的键
	Compare    string `json:"compare"`    // 比较方式
	Expression string `json:"expression"` // 期望值或正则表达式
}

type HttpRequestComponentAssertionBody struct {
	Type     HttpRequestComponentAssertionBodyType      `json:"type"` // 提取类型
	Jmespath *HttpRequestComponentAssertionBodyJmesPath `json:"jmespath"`
	Regex    *HttpRequestComponentAssertionBodyRegex    `json:"regex"`
}

type HttpRequestComponentAssertionBodyJmesPath struct {
	Expression  string `json:"expression"`  // JMESPath表达式
	Compare     string `json:"compare"`     // 比较方式
	Expectation string `json:"expectation"` // 期望值
}

type HttpRequestComponentAssertionBodyRegex struct {
	Expression string `json:"expression"` // Regex表达式
}

type HttpRequestComponentAssertionStatusCode struct {
	Compare     string `json:"compare"`     // 比较方式
	Expectation string `json:"expectation"` // 期望值或正则表达式
}

type HttpRequestComponentImport = Import

type HttpRequestComponentExport struct {
	Name        string                             `json:"name"`        // 出参变量名称
	Source      HttpRequestComponentResponseSource `json:"source"`      // 来源
	Headers     *HttpRequestComponentExportHeaders `json:"headers"`     // 响应头
	Body        *HttpRequestComponentExportBody    `json:"body"`        // 响应体
	Description string                             `json:"description"` // 出参变量描述
}

type HttpRequestComponentExportHeaders struct {
	Key string `json:"key"` // 响应头的键
}

type HttpRequestComponentExportBody struct {
	Type       HttpRequestComponentExportBodyType `json:"type"`       // 提取类型
	Expression string                             `json:"expression"` // 提取表达式
}
