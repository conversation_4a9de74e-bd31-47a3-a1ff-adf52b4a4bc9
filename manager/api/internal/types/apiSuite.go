package types

import (
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sqlbuilder/api"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/userinfo"
)

type ApiSuite struct {
	ProjectId         string                 `json:"project_id"`          // 项目ID
	CategoryId        string                 `json:"category_id"`         // 所属分类ID
	SuiteId           string                 `json:"suite_id"`            // 集合ID
	Name              string                 `json:"name"`                // 集合名称
	Description       string                 `json:"description"`         // 集合描述
	Priority          int64                  `json:"priority"`            // 优先级
	Tags              []string               `json:"tags"`                // 标签
	State             int8                   `json:"state"`               // 状态
	CaseExecutionMode int8                   `json:"case_execution_mode"` // 用例执行方式
	MaintainedBy      *userinfo.FullUserInfo `json:"maintained_by"`       // 维护者
	CreatedBy         *userinfo.FullUserInfo `json:"created_by"`          // 创建者
	UpdatedBy         *userinfo.FullUserInfo `json:"updated_by"`          // 更新者
	CreatedAt         int64                  `json:"created_at"`          // 创建时间
	UpdatedAt         int64                  `json:"updated_at"`          // 更新时间
}

type SearchApiSuiteReferenceItem struct {
	ProjectId      string                 `json:"project_id"`      // 项目ID
	SuiteId        string                 `json:"suite_id"`        // 集合ID
	ReferenceType  string                 `json:"reference_type"`  // 引用对象类型
	ReferenceId    string                 `json:"reference_id"`    // 引用对象ID
	Name           string                 `json:"name"`            // 引用对象名称
	Description    string                 `json:"description"`     // 引用对象描述
	Priority       int64                  `json:"priority"`        // 优先级
	Tags           []string               `json:"tags"`            // 标签
	State          int8                   `json:"state"`           // 状态
	ReferenceState int8                   `json:"reference_state"` // 引用状态
	MaintainedBy   *userinfo.FullUserInfo `json:"maintained_by"`   // 维护者
	CreatedBy      *userinfo.FullUserInfo `json:"created_by"`      // 创建者
	UpdatedBy      *userinfo.FullUserInfo `json:"updated_by"`      // 更新者
	CreatedAt      int64                  `json:"created_at"`      // 创建时间
	UpdatedAt      int64                  `json:"updated_at"`      // 更新时间
}

type SearchCaseInApiSuiteItem struct {
	ProjectId     string                 `json:"project_id"`
	CategoryId    string                 `json:"category_id,omitempty,optional" zh:"场景用例的所属分类ID"`
	DocumentId    string                 `json:"document_id,omitempty,optional" zh:"接口用例的接口文档ID"`
	CaseType      string                 `json:"case_type" validate:"required,oneof=API_CASE INTERFACE_CASE" zh:"用例类型"`
	CaseId        string                 `json:"case_id"`
	Name          string                 `json:"name"`
	Description   string                 `json:"description"`
	Priority      int64                  `json:"priority"`
	Tags          []string               `json:"tags"`
	State         common.ResourceState   `json:"state"`
	AccountConfig *types.AccountConfig   `json:"account_config"`
	Version       string                 `json:"version"`
	MaintainedBy  *userinfo.FullUserInfo `json:"maintained_by"`
	CreatedBy     *userinfo.FullUserInfo `json:"created_by"`
	UpdatedBy     *userinfo.FullUserInfo `json:"updated_by"`
	CreatedAt     int64                  `json:"created_at"`
	UpdatedAt     int64                  `json:"updated_at"`
}

type CreateApiSuiteReq struct {
	ProjectId         string   `json:"project_id" validate:"required" zh:"项目ID"`
	CategoryId        string   `json:"category_id" validate:"required" zh:"分类ID"`
	Name              string   `json:"name" validate:"gte=1,lte=64" zh:"API集合名称"`
	Description       string   `json:"description" validate:"lte=255" zh:"API集合描述"`
	Priority          int64    `json:"priority" validate:"gte=0" zh:"优先级"`
	Tags              []string `json:"tags" validate:"gte=0" zh:"API集合标签"`
	CaseExecutionMode int8     `json:"case_execution_mode" validate:"oneof=1 2" zh:"用例执行方式"`
	MaintainedBy      string   `json:"maintained_by" validate:"lte=64" zh:"API集合维护者"`
	CaseIds           []string `json:"case_ids,optional" validate:"omitempty,gte=0" zh:"导入的API集合ID列表"`
}

type CreateApiSuiteResp struct {
	SuiteId string `json:"suite_id"`
}

type RemoveApiSuiteReq struct {
	ProjectId string   `json:"project_id" validate:"required" zh:"项目ID"`
	SuiteIds  []string `json:"suite_ids" validate:"gt=0" zh:"API集合ID列表"`
}

type RemoveApiSuiteResp struct{}

type ModifyApiSuiteReq struct {
	ProjectId         string   `json:"project_id" validate:"required" zh:"项目ID"`
	CategoryId        string   `json:"category_id" validate:"required" zh:"分类ID"`
	SuiteId           string   `json:"suite_id" validate:"required" zh:"API集合ID"`
	Name              string   `json:"name" validate:"gte=1,lte=64" zh:"API集合名称"`
	Description       string   `json:"description" validate:"lte=255" zh:"API集合描述"`
	Priority          int64    `json:"priority" validate:"gte=0" zh:"优先级"`
	Tags              []string `json:"tags" validate:"gte=0" zh:"API集合标签"`
	State             int8     `json:"state" validate:"oneof=1 2" zh:"API集合状态"`
	CaseExecutionMode int8     `json:"case_execution_mode" validate:"oneof=1 2" zh:"用例执行方式"`
	MaintainedBy      string   `json:"maintained_by" validate:"lte=64" zh:"API集合维护者"`
}

type ModifyApiSuiteResp struct{}

type SearchApiSuiteReq struct {
	ProjectId  string           `json:"project_id" validate:"required" zh:"项目ID"`
	CategoryId string           `json:"category_id" validate:"required" zh:"分类ID"`
	Condition  *api.Condition   `json:"condition,omitempty,optional" zh:"查询条件"`
	Pagination *api.Pagination  `json:"pagination,omitempty,optional" zh:"查询分页"`
	Sort       []*api.SortField `json:"sort,omitempty,optional" zh:"查询排序"`
}

type SearchApiSuiteResp struct {
	CurrentPage uint64      `json:"current_page"`
	PageSize    uint64      `json:"page_size"`
	TotalCount  uint64      `json:"total_count"`
	TotalPage   uint64      `json:"total_page"`
	Items       []*ApiSuite `json:"items"`
}

type ViewApiSuiteReq struct {
	ProjectId string `form:"project_id" validate:"required" zh:"项目ID"`
	SuiteId   string `form:"suite_id" validate:"required" zh:"API集合ID"`
}

type ViewApiSuiteResp struct {
	*ApiSuite
}

type SearchApiCaseInApiSuiteReq struct {
	ProjectId  string           `json:"project_id" validate:"required" zh:"项目ID"`
	SuiteId    string           `json:"suite_id" validate:"required" zh:"集合ID"`
	Condition  *api.Condition   `json:"condition,omitempty,optional" zh:"查询条件"`
	Pagination *api.Pagination  `json:"pagination,omitempty,optional" zh:"查询分页"`
	Sort       []*api.SortField `json:"sort,omitempty,optional" zh:"查询排序"`
}

type SearchApiCaseInApiSuiteResp struct {
	CurrentPage uint64     `json:"current_page"`
	PageSize    uint64     `json:"page_size"`
	TotalCount  uint64     `json:"total_count"`
	TotalPage   uint64     `json:"total_page"`
	Items       []*ApiCase `json:"items"`
}

type SearchApiCaseNotInApiSuiteReq struct {
	ProjectId  string           `json:"project_id" validate:"required" zh:"项目ID"`
	SuiteId    string           `json:"suite_id" validate:"required" zh:"集合ID"`
	CategoryId string           `json:"category_id" validate:"required" zh:"用例分类树的分类ID"`
	Condition  *api.Condition   `json:"condition,omitempty,optional" zh:"查询条件"`
	Pagination *api.Pagination  `json:"pagination,omitempty,optional" zh:"查询分页"`
	Sort       []*api.SortField `json:"sort,omitempty,optional" zh:"查询排序"`
}

type SearchApiCaseNotInApiSuiteResp struct {
	CurrentPage uint64     `json:"current_page"`
	PageSize    uint64     `json:"page_size"`
	TotalCount  uint64     `json:"total_count"`
	TotalPage   uint64     `json:"total_page"`
	Items       []*ApiCase `json:"items"`
}

type ModifyApiCaseListOfApiSuiteReq struct {
	ProjectId     string   `json:"project_id" validate:"required" zh:"项目ID"`
	SuiteId       string   `json:"suite_id" validate:"required" zh:"集合ID"`
	CaseIds       []string `json:"case_ids" validate:"gt=0" zh:"待变更的API用例ID列表"`
	OperationType string   `json:"operation_type" validate:"oneof=ADD REMOVE" zh:"操作类型"`
}

type ModifyApiCaseListOfApiSuiteResp struct{}

type SearchApiSuiteReferenceReq struct {
	ProjectId  string           `json:"project_id" validate:"required" zh:"项目ID"`
	SuiteId    string           `json:"suite_id" validate:"required" zh:"集合ID"`
	Condition  *api.Condition   `json:"condition,omitempty,optional" zh:"查询条件"`
	Pagination *api.Pagination  `json:"pagination,omitempty,optional" zh:"查询分页"`
	Sort       []*api.SortField `json:"sort,omitempty,optional" zh:"查询排序"`
}

type SearchApiSuiteReferenceResp struct {
	CurrentPage uint64                         `json:"current_page"`
	PageSize    uint64                         `json:"page_size"`
	TotalCount  uint64                         `json:"total_count"`
	TotalPage   uint64                         `json:"total_page"`
	Items       []*SearchApiSuiteReferenceItem `json:"items"`
}

type ModifyApiSuiteReferenceStateReq struct {
	ProjectId string   `json:"project_id" validate:"required" zh:"项目ID"`
	SuiteId   string   `json:"suite_id" validate:"required" zh:"API集合ID"`
	PlanIds   []string `json:"plan_ids" validate:"gt=0" zh:"API计划ID列表"`
	State     int8     `json:"state" validate:"oneof=1 2" zh:"API集合引用状态"`
}

type ModifyApiSuiteReferenceStateResp struct{}

type SearchCaseInApiSuiteReq struct {
	ProjectId  string           `json:"project_id" validate:"required" zh:"项目ID"`
	SuiteId    string           `json:"suite_id" validate:"required" zh:"集合ID"`
	Condition  *api.Condition   `json:"condition,omitempty,optional" zh:"查询条件"`
	Pagination *api.Pagination  `json:"pagination,omitempty,optional" zh:"查询分页"`
	Sort       []*api.SortField `json:"sort,omitempty,optional" zh:"查询排序"`
}

type SearchCaseInApiSuiteResp struct {
	CurrentPage uint64                      `json:"current_page"`
	PageSize    uint64                      `json:"page_size"`
	TotalCount  uint64                      `json:"total_count"`
	TotalPage   uint64                      `json:"total_page"`
	Items       []*SearchCaseInApiSuiteItem `json:"items"`
}

type CaseTypeId struct {
	CaseType string `json:"case_type" validate:"required,oneof=API_CASE INTERFACE_CASE" zh:"用例类型"`
	CaseId   string `json:"case_id" validate:"required" zh:"用例ID"`
}

type ModifyCaseListOfApiSuiteReq struct {
	ProjectId     string        `json:"project_id" validate:"required" zh:"项目ID"`
	SuiteId       string        `json:"suite_id" validate:"required" zh:"集合ID"`
	CaseIds       []*CaseTypeId `json:"case_ids" validate:"gt=0" zh:"待变更的用例列表"`
	OperationType string        `json:"operation_type" validate:"oneof=ADD REMOVE" zh:"操作类型"`
}

type ModifyCaseListOfApiSuiteResp struct{}

type SearchServiceCaseNotInApiSuiteReq struct {
	ProjectId  string           `json:"project_id" validate:"required" zh:"项目ID"`
	SuiteId    string           `json:"suite_id" validate:"required" zh:"集合ID"`
	Services   []string         `json:"services" zh:"服务列表"`
	Condition  *api.Condition   `json:"condition,omitempty,optional" zh:"查询条件"`
	Pagination *api.Pagination  `json:"pagination,omitempty,optional" zh:"查询分页"`
	Sort       []*api.SortField `json:"sort,omitempty,optional" zh:"查询排序"`
}

type SearchServiceCaseNotInApiSuiteResp struct {
	CurrentPage uint64                      `json:"current_page"`
	PageSize    uint64                      `json:"page_size"`
	TotalCount  uint64                      `json:"total_count"`
	TotalPage   uint64                      `json:"total_page"`
	Items       []*SearchCaseInApiSuiteItem `json:"items"`
}
