package types

import (
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sqlbuilder/api"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/userinfo"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
)

type ApiCase struct {
	ProjectId     string                 `json:"project_id"`
	CategoryId    string                 `json:"category_id"`
	CaseId        string                 `json:"case_id"`
	Name          string                 `json:"name"`
	Description   string                 `json:"description"`
	Priority      int64                  `json:"priority"`
	Tags          []string               `json:"tags"`
	State         common.ResourceState   `json:"state"`
	AccountConfig *types.AccountConfig   `json:"account_config"`
	Version       string                 `json:"version"`
	MaintainedBy  *userinfo.FullUserInfo `json:"maintained_by"`
	CreatedBy     *userinfo.FullUserInfo `json:"created_by"`
	UpdatedBy     *userinfo.FullUserInfo `json:"updated_by"`
	CreatedAt     int64                  `json:"created_at"`
	UpdatedAt     int64                  `json:"updated_at"`
	Nodes         []*Node                `json:"nodes"`
	Edges         []*Edge                `json:"edges"`
	Combos        []*Combo               `json:"combos"`
}

type SearchApiCaseReferenceItem struct {
	ProjectId     string                 `json:"project_id"`     // 项目ID
	CaseId        string                 `json:"case_id"`        // 用例ID
	ReferenceType string                 `json:"reference_type"` // 引用对象类型
	ReferenceId   string                 `json:"reference_id"`   // 引用对象ID
	Name          string                 `json:"name"`           // 引用对象名称
	Description   string                 `json:"description"`    // 引用对象描述
	Priority      int64                  `json:"priority"`       // 优先级
	Tags          []string               `json:"tags"`           // 标签
	State         int8                   `json:"state"`          // 引用对象状态
	MaintainedBy  *userinfo.FullUserInfo `json:"maintained_by"`  // 维护者
	CreatedBy     *userinfo.FullUserInfo `json:"created_by"`     // 创建者
	UpdatedBy     *userinfo.FullUserInfo `json:"updated_by"`     // 更新者
	CreatedAt     int64                  `json:"created_at"`     // 创建时间
	UpdatedAt     int64                  `json:"updated_at"`     // 更新时间
}

type CreateApiCaseReq struct {
	ProjectId     string               `json:"project_id" validate:"required" zh:"项目ID"`
	CategoryId    string               `json:"category_id" validate:"required" zh:"分类ID"`
	Name          string               `json:"name" validate:"gte=1,lte=64" zh:"API用例名称"`
	Description   string               `json:"description" validate:"lte=255" zh:"API用例描述"`
	Priority      int64                `json:"priority" validate:"gte=0" zh:"优先级"`
	Tags          []string             `json:"tags" validate:"gte=0" zh:"API用例标签"`
	AccountConfig *types.AccountConfig `json:"account_config"`
	Nodes         []*Node              `json:"nodes" validate:"gt=2" zh:"API用例画布中的节点列表"`
	Edges         []*Edge              `json:"edges" validate:"gt=1" zh:"API用例画布中的线段列表"`
	Combos        []*Combo             `json:"combos" validate:"gte=0" zh:"API用例画布中的组合列表"`
	Relations     []*types.Relation    `json:"relations" validate:"gte=2" zh:"API用例画布中的节点与组合的关系列表"`
	MaintainedBy  string               `json:"maintained_by" validate:"lte=64" zh:"API用例维护者"`
}

type CreateApiCaseResp struct {
	CaseId string `json:"case_id"`
}

type RemoveApiCaseReq struct {
	ProjectId string   `json:"project_id" validate:"required" zh:"项目ID"`
	CaseIds   []string `json:"case_ids" validate:"gt=0" zh:"API用例ID列表"`
}

type RemoveApiCaseResp struct{}

type ModifyApiCaseReq struct {
	ProjectId   string   `json:"project_id" validate:"required" zh:"项目ID"`
	CategoryId  string   `json:"category_id" validate:"required" zh:"分类ID"`
	CaseId      string   `json:"case_id" validate:"required" zh:"API用例ID"`
	Name        string   `json:"name" validate:"gte=1,lte=64" zh:"API用例名称"`
	Description string   `json:"description" validate:"lte=255" zh:"API用例描述"`
	Priority    int64    `json:"priority" validate:"gte=0" zh:"优先级"`
	Tags        []string `json:"tags" validate:"gte=0" zh:"API用例标签"`
	// State         int8                  `json:"state" validate:"oneof=1 2" zh:"API用例状态"`
	AccountConfig *types.AccountConfig `json:"account_config"`
	Nodes         []*Node              `json:"nodes" validate:"gt=2" zh:"API用例画布中的节点列表"`
	Edges         []*Edge              `json:"edges" validate:"gt=1" zh:"API用例画布中的线段列表"`
	Combos        []*Combo             `json:"combos" validate:"gte=0" zh:"API用例画布中的组合列表"`
	Relations     []*types.Relation    `json:"relations" validate:"gte=2" zh:"API用例画布中的节点与组合的关系列表"`
	MaintainedBy  string               `json:"maintained_by" validate:"lte=64" zh:"API用例维护者"`
}

type ModifyApiCaseResp struct{}

type SearchApiCaseReq struct {
	ProjectId  string           `json:"project_id" validate:"required" zh:"项目ID"`
	CategoryId string           `json:"category_id" validate:"required" zh:"分类ID"`
	Condition  *api.Condition   `json:"condition,omitempty,optional"`
	Pagination *api.Pagination  `json:"pagination,omitempty,optional"`
	Sort       []*api.SortField `json:"sort,omitempty,optional"`
}

type SearchApiCaseResp struct {
	CurrentPage uint64     `json:"current_page"`
	PageSize    uint64     `json:"page_size"`
	TotalCount  uint64     `json:"total_count"`
	TotalPage   uint64     `json:"total_page"`
	Items       []*ApiCase `json:"items"`
}

type ViewApiCaseReq struct {
	ProjectId string `form:"project_id" validate:"required" zh:"项目ID"`
	CaseId    string `form:"case_id" validate:"required" zh:"API用例ID"`
	Version   string `form:"version,omitempty,optional"`
}

type ViewApiCaseResp struct {
	*ApiCase
}

type SearchApiCaseReferenceReq struct {
	ProjectId  string           `json:"project_id" validate:"required" zh:"项目ID"`
	CaseId     string           `json:"case_id" validate:"required" zh:"用例ID"`
	Condition  *api.Condition   `json:"condition,omitempty,optional" zh:"查询条件"`
	Pagination *api.Pagination  `json:"pagination,omitempty,optional" zh:"查询分页"`
	Sort       []*api.SortField `json:"sort,omitempty,optional" zh:"查询排序"`
}

type SearchApiCaseReferenceResp struct {
	CurrentPage uint64                        `json:"current_page"`
	PageSize    uint64                        `json:"page_size"`
	TotalCount  uint64                        `json:"total_count"`
	TotalPage   uint64                        `json:"total_page"`
	Items       []*SearchApiCaseReferenceItem `json:"items"`
}

type MaintainApiCaseReq struct {
	ProjectId    string `json:"project_id" validate:"required" zh:"项目ID"`
	CaseId       string `json:"case_id" validate:"required" zh:"用例ID"`
	MaintainedBy string `json:"maintained_by" validate:"lte=64" zh:"API用例维护者"`
}

type MaintainApiCaseResp struct{}

type PublishApiCaseReq struct {
	ProjectId string `json:"project_id" validate:"required" zh:"项目ID"`
	CaseId    string `json:"case_id" validate:"required" zh:"用例ID"`
}

type PublishApiCaseResp struct{}
