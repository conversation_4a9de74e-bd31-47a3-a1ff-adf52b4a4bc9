syntax = "v1"

import "interface_definition_types.api"

@server (
    prefix: manager/v1
    group: interfaceDefinition
    timeout: 10m
)
service manager {
    @doc "import interface definition by local"
    @handler LocalImportInterfaceDefinition
    post /interface_definition/local_import (LocalImportInterfaceDefinitionReq) returns (LocalImportInterfaceDefinitionResp)

    @doc "import interface definition by remote"
    @handler RemoteImportInterfaceDefinition
    post /interface_definition/remote_import (RemoteImportInterfaceDefinitionReq) returns (RemoteImportInterfaceDefinitionResp)
}

@server (
    prefix: manager/v1
    group: interfaceDefinition
)
service manager {
    @doc "create interface document"
    @handler CreateInterfaceDocument
    post /interface_definition/document/create (CreateInterfaceDocumentReq) returns (CreateInterfaceDocumentResp)

    @doc "delete interface document"
    @handler RemoveInterfaceDocument
    put /interface_definition/document/remove (RemoveInterfaceDocumentReq) returns (RemoveInterfaceDocumentResp)

    @doc "modify interface document"
    @handler ModifyInterfaceDocument
    put /interface_definition/document/modify (ModifyInterfaceDocumentReq) returns (ModifyInterfaceDocumentResp)

    @doc "search interface document"
    @handler SearchInterfaceDocument
    post /interface_definition/document/search (SearchInterfaceDocumentReq) returns (SearchInterfaceDocumentResp)

    @doc "view interface document"
    @handler ViewInterfaceDocument
    get /interface_definition/document/view (ViewInterfaceDocumentReq) returns (ViewInterfaceDocumentResp)

    @doc "generate data of interface case from interface document"
    @handler MockInterfaceDocument
    get /interface_definition/document/mock (MockInterfaceDocumentReq) returns (MockInterfaceDocumentResp)

    @doc "search reference data of interface document"
    @handler searchInterfaceDocumentReference
    post /interface_definition/document/reference/search (SearchInterfaceDocumentReferenceReq) returns (SearchInterfaceDocumentReferenceResp)

    @doc "modify reference state of interface document which is in the api plans"
    @handler modifyInterfaceDocumentReferenceState
    put /interface_definition/document/reference_state/modify (ModifyInterfaceDocumentReferenceStateReq) returns (ModifyInterfaceDocumentReferenceStateResp)
}

@server (
    prefix: manager/v1
    group: interfaceDefinition
)
service manager {
    @doc "create interface schema"
    @handler CreateInterfaceSchema
    post /interface_definition/schema/create (CreateInterfaceSchemaReq) returns (CreateInterfaceSchemaResp)

    @doc "delete interface schema"
    @handler RemoveInterfaceSchema
    put /interface_definition/schema/remove (RemoveInterfaceSchemaReq) returns (RemoveInterfaceSchemaResp)

    @doc "modify interface schema"
    @handler ModifyInterfaceSchema
    put /interface_definition/schema/modify (ModifyInterfaceSchemaReq) returns (ModifyInterfaceSchemaResp)

    @doc "search interface schema"
    @handler SearchInterfaceSchema
    post /interface_definition/schema/search (SearchInterfaceSchemaReq) returns (SearchInterfaceSchemaResp)

    @doc "view interface schema"
    @handler ViewInterfaceSchema
    get /interface_definition/schema/view (ViewInterfaceSchemaReq) returns (ViewInterfaceSchemaResp)
}

@server (
    prefix: manager/v1
    group: interfaceDefinition
)
service manager {
    @doc "create interface config"
    @handler CreateInterfaceConfig
    post /interface_definition/config/create (CreateInterfaceConfigReq) returns (CreateInterfaceConfigResp)

    @doc "delete interface config"
    @handler RemoveInterfaceConfig
    put /interface_definition/config/remove (RemoveInterfaceConfigReq) returns (RemoveInterfaceConfigResp)

    @doc "modify interface config"
    @handler ModifyInterfaceConfig
    put /interface_definition/config/modify (ModifyInterfaceConfigReq) returns (ModifyInterfaceConfigResp)

    @doc "search interface config"
    @handler SearchInterfaceConfig
    post /interface_definition/config/search (SearchInterfaceConfigReq) returns (SearchInterfaceConfigResp)

    @doc "view interface config"
    @handler ViewInterfaceConfig
    get /interface_definition/config/view (ViewInterfaceConfigReq) returns (ViewInterfaceConfigResp)
}

@server (
    prefix: manager/v1
    group: interfaceDefinition
)
service manager {
    @doc "create interface test case"
    @handler CreateInterfaceCase
    post /interface_definition/case/create (CreateInterfaceCaseReq) returns (CreateInterfaceCaseResp)

    @doc "delete interface test case"
    @handler RemoveInterfaceCase
    put /interface_definition/case/remove (RemoveInterfaceCaseReq) returns (RemoveInterfaceCaseResp)

    @doc "modify interface test case"
    @handler ModifyInterfaceCase
    put /interface_definition/case/modify (ModifyInterfaceCaseReq) returns (ModifyInterfaceCaseResp)

    @doc "search interface test cases"
    @handler SearchInterfaceCase
    post /interface_definition/case/search (SearchInterfaceCaseReq) returns (SearchInterfaceCaseResp)

    @doc "view interface test case"
    @handler ViewInterfaceCase
    get /interface_definition/case/view (ViewInterfaceCaseReq) returns (ViewInterfaceCaseResp)

    @doc "maintain interface test case"
    @handler maintainInterfaceCase
    put /interface_definition/case/maintain (MaintainInterfaceCaseReq) returns (MaintainInterfaceCaseResp)

    @doc "publish interface test case"
    @handler publishInterfaceCase
    put /interface_definition/case/publish (PublishInterfaceCaseReq) returns (PublishInterfaceCaseResp)

    @doc "search reference data of interface test case"
	@handler searchInterfaceCaseReference
	post /interface_definition/reference/search (SearchInterfaceCaseReferenceReq) returns (SearchInterfaceCaseReferenceResp)
}

@server (
    prefix: manager/v1
    group: interfaceDefinition
)
service manager {
    @handler GetInterfaceCoverageTeams
    get /interface_definition/coverage/team/get (GetInterfaceCoverageTeamsReq) returns (GetInterfaceCoverageTeamsResp)

    @handler GetInterfaceCoverageData
    get /interface_definition/coverage/data/get (GetInterfaceCoverageDataReq) returns (GetInterfaceCoverageDataResp)
}
