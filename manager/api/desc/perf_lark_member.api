syntax = "v1"
import "perf_lark_member_types.api"

@server (
    prefix: manager/v1
    group: perfLarkMember
)
service manager {
    @handler createPerfLarkMember
    post /perf_lark_member/create (CreatePerfLarkMemberReq) returns (CreatePerfLarkMemberResp)

    @handler removePerfLarkMember
    put /perf_lark_member/remove (RemovePerfLarkMemberReq) returns (RemovePerfLarkMemberResp)

    @handler searchPerfLarkMember
    post /perf_lark_member/search (SearchPerfLarkMemberReq) returns (SearchPerfLarkMemberResp)
}
