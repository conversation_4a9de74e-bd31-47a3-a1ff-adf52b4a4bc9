syntax = "v1"

import "types.api"

type (
    CreatePerfLarkMemberReq {
        ProjectId string          `json:"project_id" validate:"required" zh:"项目ID"`
        Items     []*FullUserInfo `json:"items"`
    }
    CreatePerfLarkMemberResp {
    }
)

type (
    RemovePerfLarkMemberReq {
        ProjectId string `json:"project_id" validate:"required" zh:"项目ID"`
        Account   string `json:"account" validate:"required" zh:"用户名（工号）"`
    }
    RemovePerfLarkMemberResp {
    }
)

type (
    SearchPerfLarkMemberReq {
        ProjectId  string       `json:"project_id" validate:"required" zh:"项目ID"`
        Condition  *Condition   `json:"condition,omitempty,optional" zh:"查询条件"`
        Pagination *Pagination  `json:"pagination,omitempty,optional" zh:"查询分页"`
        Sort       []*SortField `json:"sort,omitempty,optional" zh:"查询排序"`
    }
    SearchPerfLarkMemberResp {
        CurrentPage uint64          `json:"current_page"`
        PageSize    uint64          `json:"page_size"`
        TotalCount  uint64          `json:"total_count"`
        TotalPage   uint64          `json:"total_page"`
        Items       []*FullUserInfo `json:"items"`
    }
)