syntax = "v1"

import "project_types.api"

@server (
	prefix: manager/v1
	group: project
)
service manager {
	@handler createProject
	post /project/create (CreateProjectReq) returns (CreateProjectResp)

	@handler removeProject
	put /project/delete (RemoveProjectReq) returns (RemoveProjectResp)
	
	@handler modifyProject
	put /project/modify (ModifyProjectReq) returns (ModifyProjectResp)
	
	@handler searchProject
	post /project/search (SearchProjectReq) returns (SearchProjectResp)
	
	@handler viewProject
	get /project/view (ViewProjectReq) returns (ViewProjectResp)

	@handler searchProjectUser
	post /project/user/search (SearchProjectUserReq) returns (SearchProjectUserResp)

	@handler modifyProjectReviewFunction
	put /project/review/modify (ModifyProjectReviewFunctionReq) returns (ModifyProjectReviewFunctionResp)

    @handler modifyProjectCoverageFunction
	put /project/coverage/modify (ModifyProjectCoverageFunctionReq) returns (ModifyProjectCoverageFunctionResp)
}
