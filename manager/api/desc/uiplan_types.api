syntax = "v1"

import "types.api"

type (
    UiPlan {
        ProjectId            string        `json:"project_id"`            // 项目ID
        CategoryId           string        `json:"category_id"`           // 分类ID
        PlanId               string        `json:"plan_id"`               // 计划ID
        Name                 string        `json:"name"`                  // 名称
        Description          string        `json:"description"`           // 描述
        Type                 string        `json:"type"`                  // 计划类型（手动、定时、接口）
        CronExpression       string        `json:"cron_expression"`       // 定时触发计划的Cron表达式
        PriorityType         int8          `json:"priority_type"`         // 优先级
        GitConfigId          string        `json:"git_config_id"`         // Git配置
        ExecutionMode        int8          `json:"execution_mode"`        // 执行方式
        DeviceType           int8          `json:"device_type"`           // 设备类型（真机、云手机）
        PlatformType         int8          `json:"platform_type"`         // 测试系统（Android、IOS）
        Devices              []string      `json:"devices"`               // 设备列表
        Together             bool          `json:"together"`              // 选择的设备是否一起执行
        PackageName          string        `json:"package_name"`          // 包名，用于启动APP
        CallbackUrl          string        `json:"callback_url"`          // 回调地址
        AppDownloadLink      string        `json:"app_download_link"`     // APP下载地址
        AppVersion           string        `json:"app_version"`           // APP版本
        AppName              string        `json:"app_name"`              // 测试应用名称
        TestLanguage         int8          `json:"test_language"`         // 测试语言
        TestLanguageVersion  string        `json:"test_language_version"` // 测试语言版本
        TestFramework        int8          `json:"test_framework"`        // 测试框架
        TestArgs             []string      `json:"test_args"`             // 附加参数
        ExecutionEnvironment string        `json:"execution_environment"` // 执行环境
        FailRetry            int8          `json:"fail_retry"`            // 失败重试（0次、1次、2次）
        State                int8          `json:"state"`                 // 状态（1生效，2失效）
        Deleted              int8          `json:"deleted"`               // 逻辑删除标识（未删除、已删除）
        MaintainedBy         *FullUserInfo `json:"maintained_by"`         // 维护者
        CreatedBy            *FullUserInfo `json:"created_by"`            // 创建者
        UpdatedBy            *FullUserInfo `json:"updated_by"`            // 更新者
        CreatedAt            int64         `json:"created_at"`            // 创建时间
        UpdatedAt            int64         `json:"updated_at"`            // 更新时间
    }

    CasePathItem {
        CasePath string `json:"case_path" validate:"required" zh:"用例路径"`
    }

    UICaseTreeNode {
        ProjectId   string            `json:"project_id"`    // 项目ID
        GitConfigId string            `json:"git_config_id"` // Git配置ID
        Path        string            `json:"path"`          // 节点路径（相对于根路径）
        ParentPath  string            `json:"parent_path"`   // 父节点路径
        Name        string            `json:"name"`          // 节点名称
        Alias       string            `json:"alias"`         // 节点别名
        Type        string            `json:"type"`          // 节点类型（目录、文件、包、模块、类、函数）
        Tags        []string          `json:"tags"`          // 标签
        CreatedBy   *FullUserInfo     `json:"created_by"`    // 创建者
        UpdatedBy   *FullUserInfo     `json:"updated_by"`    // 更新者
        CreatedAt   int64             `json:"created_at"`    // 创建时间
        UpdatedAt   int64             `json:"updated_at"`    // 更新时间
        Amount      int64             `json:"amount"`        // 叶子节点数量
        Children    []*UICaseTreeNode `json:"children"`      // 子节点
    }

    SearchCaseInUIPlanItem {
        ProjectId   string        `json:"project_id"`    // 项目ID
        GitConfigId string        `json:"git_config_id"` // 计划ID
        Path        string        `json:"path"`          // 节点路径（相对于根路径）
        ParentPath  string        `json:"parent_path"`   // 父节点路径
        Name        string        `json:"name"`          // 节点名称
        Alias       string        `json:"alias"`         // 节点别名
        Type        string        `json:"type"`          // 节点类型（目录、文件、包、模块、类、函数）
        Tags        []string      `json:"tags"`          // 标签
        CreatedBy   *FullUserInfo `json:"created_by"`    // 创建者
        UpdatedBy   *FullUserInfo `json:"updated_by"`    // 更新者
        CreatedAt   int64         `json:"created_at"`    // 创建时间
        UpdatedAt   int64         `json:"updated_at"`    // 更新时间
    }

    SearchCaseNotInUIPlanItem {
        ProjectId   string        `json:"project_id"`    // 项目ID
        GitConfigId string        `json:"git_config_id"` // 计划ID
        Path        string        `json:"path"`          // 节点路径（相对于根路径）
        ParentPath  string        `json:"parent_path"`   // 父节点路径
        Name        string        `json:"name"`          // 节点名称
        Alias       string        `json:"alias"`         // 节点别名
        Type        string        `json:"type"`          // 节点类型（目录、文件、包、模块、类、函数）
        Tags        []string      `json:"tags"`          // 标签
        CreatedBy   *FullUserInfo `json:"created_by"`    // 创建者
        UpdatedBy   *FullUserInfo `json:"updated_by"`    // 更新者
        CreatedAt   int64         `json:"created_at"`    // 创建时间
        UpdatedAt   int64         `json:"updated_at"`    // 更新时间
    }
)


// 创建UI计划
type (
    CreateUiPlanReq {
        ProjectId            string          `json:"project_id" validate:"required" zh:"项目ID"`
        CategoryId           string          `json:"category_id" validate:"required" zh:"分类ID"`
        Name                 string          `json:"name" validate:"gte=1,lte=64" zh:"UI计划名称"`
        Description          string          `json:"description" validate:"lte=255" zh:"UI计划描述"`
        Type                 string          `json:"type" validate:"oneof=MANUAL SCHEDULE INTERFACE" zh:"计划类型"`
        CronExpression       string          `json:"cron_expression" validate:"omitempty,gte=9,lte=128" zh:"定时触发的Cron表达式"`
        PriorityType         int8            `json:"priority_type,omitempty,optional" validate:"oneof=0 1 2 3 4" zh:"优先级（Default、Middle、High、Ultra、Low)"`
        GitConfigId          string          `json:"git_config_id" validate:"lte=64" zh:"Git配置ID"`
        ExecutionMode        int8            `json:"execution_mode" validate:"oneof=1 2" zh:"执行方式"`
        DeviceType           int8            `json:"device_type" validate:"oneof=1 2" zh:"设备类型（真机、云手机）"`
        PlatformType         int8            `json:"platform_type" validate:"oneof=1 2" zh:"测试系统（Android、IOS）"`
        Devices              []string        `json:"devices" validate:"omitempty,gte=0" zh:"设备列表"`
        Together             bool            `json:"together" zh:"选择的设备是否一起执行"`
        PackageName          string          `json:"package_name" validate:"lte=255" zh:"包名，用于启动app"`
        CallbackUrl          string          `json:"callback_url" validate:"lte=255" zh:"回调地址"`
        AppDownloadLink      string          `json:"app_download_link" validate:"lte=255" zh:"APP下载地址"`
        AppVersion           string          `json:"app_version" validate:"lte=255" zh:"APP版本"`
        AppName              string          `json:"app_name" validate:"omitempty,lte=255" zh:"测试应用名称"`
        TestLanguage         int8            `json:"test_language" validate:"lte=255" zh:"测试语言"`
        TestLanguageVersion  string          `json:"test_language_version" validate:"lte=50" zh:"测试语言版本"`
        TestFramework        int8            `json:"test_framework" validate:"lte=50" zh:"测试框架"`
        TestArgs             []string        `json:"test_args" validate:"omitempty" zh:"附加参数"`
        ExecutionEnvironment string          `json:"execution_environment" validate:"lte=255" zh:"执行环境"`
        FailRetry            int8            `json:"fail_retry" validate:"oneof=0 1 2" zh:"失败重试（0次、1次、2次）"`
        MaintainedBy         string          `json:"maintained_by" validate:"lte=64" zh:"UI计划维护者"`
        CasePaths            []*CasePathItem `json:"case_paths,optional" validate:"omitempty,gte=0" zh:"导入的用例路径列表"`
    }
    CreateUiPlanResp {
        PlanId string `json:"plan_id"`
    }
)

// 删除UI计划
type (
    RemoveUiPlanReq {
        ProjectId string `json:"project_id" validate:"required" zh:"项目ID"`
        PlanIds []string `json:"plan_ids" validate:"gt=0" zh:"UI计划ID列表"`
    }
    RemoveUiPlanResp {
    }
)

// 编辑UI计划
type (
    ModifyUiPlanReq {
        ProjectId            string   `json:"project_id" validate:"required" zh:"项目ID"`
        CategoryId           string   `json:"category_id" validate:"required" zh:"分类ID"`
        PlanId               string   `json:"plan_id" validate:"required" zh:"UI计划ID"`
        Name                 string   `json:"name" validate:"gte=1,lte=64" zh:"UI计划名称"`
        Description          string   `json:"description" validate:"lte=255" zh:"UI计划描述"`
        Type                 string   `json:"type" validate:"oneof=MANUAL SCHEDULE INTERFACE" zh:"计划类型"`
        CronExpression       string   `json:"cron_expression" validate:"omitempty,gte=9,lte=128" zh:"定时触发的Cron表达式"`
        PriorityType         int8     `json:"priority_type,omitempty,optional" validate:"oneof=0 1 2 3 4" zh:"优先级（Default、Middle、High、Ultra、Low)"`
        GitConfigId          string   `json:"git_config_id" validate:"lte=64" zh:"Git配置ID"`
        ExecutionMode        int8     `json:"execution_mode" validate:"oneof=1 2" zh:"执行方式"`
        DeviceType           int8     `json:"device_type" validate:"oneof=1 2" zh:"设备类型（真机、云手机）"`
        PlatformType         int8     `json:"platform_type" validate:"oneof=1 2" zh:"测试系统（Android、IOS）"`
        Devices              []string `json:"devices" validate:"omitempty,gte=0" zh:"设备列表"`
        Together             bool     `json:"together" zh:"选择的设备是否一起执行"`
        PackageName          string   `json:"package_name" validate:"lte=255" zh:"包名，用于启动app"`
        CallbackUrl          string   `json:"callback_url" validate:"lte=255" zh:"回调地址"`
        AppDownloadLink      string   `json:"app_download_link" validate:"lte=255" zh:"APP下载地址"`
        AppVersion           string   `json:"app_version" validate:"lte=255" zh:"APP版本"`
        AppName              string   `json:"app_name" validate:"omitempty,lte=255" zh:"测试应用名称"`
        TestLanguage         int8     `json:"test_language" validate:"lte=255" zh:"测试语言"`
        TestLanguageVersion  string   `json:"test_language_version" validate:"lte=50" zh:"测试语言版本"`
        TestFramework        int8     `json:"test_framework" validate:"lte=50" zh:"测试框架"`
        TestArgs             []string `json:"test_args" validate:"omitempty" zh:"附加参数"`
        ExecutionEnvironment string   `json:"execution_environment" validate:"lte=255" zh:"执行环境"`
        FailRetry            int8     `json:"fail_retry" validate:"oneof=0 1 2" zh:"失败重试（0次、1次、2次）"`
        MaintainedBy         string   `json:"maintained_by" validate:"lte=64" zh:"UI计划维护者"`
        State int8 `json:"state" validate:"oneof=1 2" zh:"生效1，失效2"`                 // 状态（1生效，2失效）
    }
    ModifyUiPlanResp {
    }
)

// 查询UI计划
type (
    SearchUiPlanReq {
        ProjectId  string       `json:"project_id" validate:"required" zh:"项目ID"`
        CategoryId string       `json:"category_id,omitempty,optional" zh:"分类ID"`
        Condition  *Condition   `json:"condition,omitempty,optional" zh:"查询条件"`
        Pagination *Pagination  `json:"pagination,omitempty,optional" zh:"查询分页"`
        Sort       []*SortField `json:"sort,omitempty,optional" zh:"查询排序"`
    }
    SearchUiPlanResp {
        CurrentPage uint64    `json:"current_page"`
        PageSize    uint64    `json:"page_size"`
        TotalCount  uint64    `json:"total_count"`
        TotalPage   uint64    `json:"total_page"`
        Items       []*UiPlan `json:"items"`
    }
)

// 查看UI计划
type (
    ViewUiPlanReq {
        ProjectId string `form:"project_id" validate:"required" zh:"项目ID"`
        PlanId    string `form:"plan_id" validate:"required" zh:"UI计划ID"`
    }
    ViewUiPlanResp {
        *UiPlan
    }
)

// 获取UI计划的用例树
type (
    GetCaseTreeOfUIPlanReq {
        ProjectId string `form:"project_id" validate:"required" zh:"项目ID"`
        PlanId    string `form:"plan_id" validate:"required" zh:"UI计划ID"`
    }
    GetCaseTreeOfUIPlanResp {} // GetCaseTreeOfUIPlanResp = []*UICaseTreeNode
)

// 获取不在指定的UI计划中的用例树
type (
    GetCaseTreeOfNotAddedToUIPlanReq {
        ProjectId string `form:"project_id" validate:"required" zh:"项目ID"`
        PlanId    string `form:"plan_id" validate:"required" zh:"UI计划ID"`
    }
    GetCaseTreeOfNotAddedToUIPlanResp {} // GetCaseTreeOfNotAddedToUIPlanReq = []*UICaseTreeNode
)

// 搜索UI计划中的用例
type (
    SearchCaseInUIPlanReq {
        ProjectId  string       `json:"project_id" validate:"required" zh:"项目ID"`
        PlanId     string       `json:"plan_id" validate:"required" zh:"UI计划ID"`
        Path       string       `json:"path" validate:"required" zh:"当前节点路径"`
        Condition  *Condition   `json:"condition,omitempty,optional" zh:"查询条件"`
        Pagination *Pagination  `json:"pagination,omitempty,optional" zh:"查询分页"`
        Sort       []*SortField `json:"sort,omitempty,optional" zh:"查询排序"`
    }
    SearchCaseInUIPlanResp {
        CurrentPage uint64                    `json:"current_page"`
        PageSize    uint64                    `json:"page_size"`
        TotalCount  uint64                    `json:"total_count"`
        TotalPage   uint64                    `json:"total_page"`
        Items       []*SearchCaseInUIPlanItem `json:"items"`
    }
)

// 搜索UI计划中的用例
type (
    ListDisableCaseInUIPlanReq {
        ProjectId string `json:"project_id" validate:"required" zh:"项目ID"`
        PlanId    string `json:"plan_id" validate:"required" zh:"UI计划ID"`
    }
    ListDisableCaseInUIPlanResp {
        Items []*SearchCaseInUIPlanItem `json:"items"`
    }
)

// 搜索不在指定的UI计划中的用例
type (
    SearchCaseNotInUIPlanReq {
        ProjectId  string       `json:"project_id" validate:"required" zh:"项目ID"`
        PlanId     string       `json:"plan_id" validate:"required" zh:"UI计划ID"`
        Path       string       `json:"path" validate:"required" zh:"当前节点路径"`
        Condition  *Condition   `json:"condition,omitempty,optional" zh:"查询条件"`
        Pagination *Pagination  `json:"pagination,omitempty,optional" zh:"查询分页"`
        Sort       []*SortField `json:"sort,omitempty,optional" zh:"查询排序"`
    }
    SearchCaseNotInUIPlanResp {
        CurrentPage uint64                       `json:"current_page"`
        PageSize    uint64                       `json:"page_size"`
        TotalCount  uint64                       `json:"total_count"`
        TotalPage   uint64                       `json:"total_page"`
        Items       []*SearchCaseNotInUIPlanItem `json:"items"`
    }
)

// 编辑UI计划的用例列表
type (
    ModifyCaseListOfUIPlanReq {
        ProjectId     string   `json:"project_id" validate:"required" zh:"项目ID"`
        PlanId        string   `json:"plan_id" validate:"required" zh:"API计划ID"`
        CasePaths     []string `json:"case_paths" validate:"gt=0" zh:"待变更的集合ID列表"`
        OperationType string   `json:"operation_type" validate:"oneof=ADD REMOVE" zh:"操作类型"`
    }
    ModifyCaseListOfUIPlanResp {}
)
