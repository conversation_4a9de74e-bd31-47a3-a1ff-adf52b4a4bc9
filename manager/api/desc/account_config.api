syntax = "v1"

import "account_config_types.api"

@server (
    prefix: manager/v1
    group: accountconfig
)
service manager {
    @doc "create account config"
    @handler createAccountConfig
    post /account_config/create (CreateAccountConfigReq) returns (CreateAccountConfigResp)

    @doc "delete account config"
    @handler removeAccountConfig
    put /account_config/remove (RemoveAccountConfigReq) returns (RemoveAccountConfigResp)

    @doc "modify account config"
    @handler modifyAccountConfig
    put /account_config/modify (ModifyAccountConfigReq) returns (ModifyAccountConfigResp)

    @doc "search account configs"
    @handler searchAccountConfig
    post /account_config/search (SearchAccountConfigReq) returns (SearchAccountConfigResp)

    @doc "view account config"
    @handler viewAccountConfig
    get /account_config/view (ViewAccountConfigReq) returns (ViewAccountConfigResp)
}
