syntax = "v1"

import "apisuite_types.api"

@server (
    prefix: manager/v1
    group: apisuite
)
service manager {
    @doc "create api test suite"
    @handler createApiSuite
    post /api_suite/create (CreateApiSuiteReq) returns (CreateApiSuiteResp)

    @doc "delete api test suite"
    @handler removeApiSuite
    put /api_suite/remove (RemoveApiSuiteReq) returns (RemoveApiSuiteResp)

    @doc "modify api test suite"
    @handler modifyApiSuite
    put /api_suite/modify (ModifyApiSuiteReq) returns (ModifyApiSuiteResp)

    @doc "search api test suites"
    @handler searchApiSuite
    post /api_suite/search (SearchApiSuiteReq) returns (SearchApiSuiteResp)

    @doc "view api test suite"
    @handler viewApiSuite
    get /api_suite/view (ViewApiSuiteReq) returns (ViewApiSuiteResp)

    @doc "search api cases in api suite"
    @handler searchApiCaseInApiSuite
    post /api_suite/api_case/search (SearchApiCaseInApiSuiteReq) returns (SearchApiCaseInApiSuiteResp)

    @doc "search api cases not in api suite"
    @handler searchApiCaseNotInApiSuite
    post /api_suite/api_case/remaining_search (SearchApiCaseNotInApiSuiteReq) returns (SearchApiCaseNotInApiSuiteResp)

    @doc "add api cases to api suite or remove api cases from api suite"
    @handler modifyApiCaseListOfApiSuite
    put /api_suite/api_case/modify (ModifyApiCaseListOfApiSuiteReq) returns (ModifyApiCaseListOfApiSuiteResp)

    @doc "search reference data of api suite"
    @handler searchApiSuiteReference
    post /api_suite/reference/search (SearchApiSuiteReferenceReq) returns (SearchApiSuiteReferenceResp)

    @doc "modify reference state of api suite which is in the api plans"
    @handler modifyApiSuiteReferenceState
    put /api_suite/reference_state/modify (ModifyApiSuiteReferenceStateReq) returns (ModifyApiSuiteReferenceStateResp)

    @doc "search cases in api suite"
    @handler searchCaseInApiSuite
    post /api_suite/case/search (SearchCaseInApiSuiteReq) returns (SearchCaseInApiSuiteResp)

    @doc "add cases to api suite or remove cases from api suite"
    @handler modifyCaseListOfApiSuite
    put /api_suite/case/modify (ModifyCaseListOfApiSuiteReq) returns (ModifyCaseListOfApiSuiteResp)

    @doc "search service cases not in api suite"
    @handler searchServiceCaseNotInApiSuite
    post /api_suite/service_case/remaining_search (SearchServiceCaseNotInApiSuiteReq) returns (SearchServiceCaseNotInApiSuiteResp)
}
