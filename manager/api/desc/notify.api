syntax = "v1"

import "notify_types.api"

@server (
    prefix: manager/v1
    group: notify
)
service manager {
    @doc "create notify"
    @handler createNotify
    post /notify/create (CreateNotifyReq) returns (CreateNotifyResp)

    @doc "delete notify"
    @handler removeNotify
    put /notify/remove (RemoveNotifyReq) returns (RemoveNotifyResp)

    @doc "modify notify"
    @handler modifyNotify
    put /notify/modify (ModifyNotifyReq) returns (ModifyNotifyResp)

    @doc "search notify"
    @handler SearchNotify
    post /notify/search (SearchNotifyReq) returns (SearchNotifyResp)
}
