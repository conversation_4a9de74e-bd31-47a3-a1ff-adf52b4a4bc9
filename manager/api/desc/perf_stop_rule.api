syntax = "v1"

import "perf_stop_rule_types.api"

@server (
    prefix: manager/v1
    group: perfStopRule
)
service manager {
    @handler createPerfStopRule
    post /perf_stop_rule/create (CreatePerfStopRuleReq) returns (CreatePerfStopRuleResp)

    @handler removePerfStopRule
    put /perf_stop_rule/remove (RemovePerfStopRuleReq) returns (RemovePerfStopRuleResp)

    @handler modifyPerfStopRule
    put /perf_stop_rule/modify (ModifyPerfStopRuleReq) returns (ModifyPerfStopRuleResp)

    @handler searchPerfStopRule
    post /perf_stop_rule/search (SearchPerfStopRuleReq) returns (SearchPerfStopRuleResp)

    @handler viewPerfStopRule
    get /perf_stop_rule/view (ViewPerfStopRuleReq) returns (ViewPerfStopRuleResp)
}
