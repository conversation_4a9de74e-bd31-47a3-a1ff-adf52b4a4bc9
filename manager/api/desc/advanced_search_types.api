syntax = "v1"

import "types.api"

type AdvancedSearchField {
    FieldId string `json:"field_id"`      // 字段ID
    ProjectId string `json:"project_id"`  // 项目ID
    FieldType string `json:"field_type"`  // 字段类型
    SceneType string `json:"scene_type"`  // 所属组件
    FrontName string `json:"front_name"`  // 前端显示名称
    FieldName string `json:"field_name"`  // 集合描述
    CreatedAt int64 `json:"created_at"`   // 创建时间
    UpdatedAt int64 `json:"updated_at"`   // 更新时间
}

type (
    SearchAdvancedSearchFieldReq {
        ProjectId string `json:"project_id" validate:"required" zh:"项目ID"`
        SceneType string `json:"scene_type" validate:"required" zh:"场景类型"`
    }
    SearchAdvancedSearchFieldResp {
        Items []*AdvancedSearchField `json:"items"`
    }
)

type AdvancedSearchCondition {
    ConditionId string `json:"condition_id"`         // 对比条件ID
    FrontName string `json:"front_name"`             // 前端显示名称
    Compare string `json:"compare"`                  // 比较方式
    CreatedAt int64 `json:"created_at"`              // 创建时间
    UpdatedAt int64 `json:"updated_at"`              // 更新时间
}

type (
    SearchAdvancedSearchConditionReq {
        ProjectId string `json:"project_id" validate:"required" zh:"项目ID"`
        FieldId string `json:"field_id" validate:"required" zh:"字段ID"`
    }
    SearchAdvancedSearchConditionResp {
        Items []*AdvancedSearchCondition `json:"items"`
    }
)
