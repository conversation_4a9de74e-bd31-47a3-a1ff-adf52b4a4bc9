syntax = "v1"

import "types.api"

type (
    Parameter {
        Name        string `json:"name" validate:"gte=1,lte=64" zh:"参数名称"`
        Description string `json:"description" validate:"lte=255" zh:"参数描述"`
        Type        string `json:"type" validate:"required,oneof=STRING NUMBER ARRAY OBJECT BOOLEAN NULL ANY" zh:"参数类型"`
        Default     string `json:"default" zh:"参数默认值"`
        Variadic    bool   `json:"variadic,default=false" zh:"是否可变参数"`
    }
    Return {
        Name        string `json:"name" validate:"gte=1,lte=64" zh:"返回值名称"`
        Description string `json:"description" validate:"lte=255" zh:"返回值描述"`
        Type        string `json:"type" validate:"required,oneof=STRING NUMBER ARRAY OBJECT BOOLEAN NULL ANY" zh:"返回值类型"`
    }
    Function {
        ProjectId   string        `json:"project_id"`
        Name        string        `json:"name"`
        Type        string        `json:"type"`
        Category    string        `json:"category"`
        Description string        `json:"description"`
        Language    string        `json:"language"`
        Content     string        `json:"content"`
        Parameters  []*Parameter  `json:"parameters"`
        Returns     []*Return     `json:"returns"`
        Example     string        `json:"example"`
        Version     string        `json:"version"`
        CreatedBy   *FullUserInfo `json:"created_by"`
        UpdatedBy   *FullUserInfo `json:"updated_by"`
        CreatedAt   int64         `json:"created_at"`
        UpdatedAt   int64         `json:"updated_at"`
    }
)

// 创建或编辑自定义函数
type (
    CreateOrModifyDataProcessingFunctionReq {
        ProjectId   string       `json:"project_id" validate:"required" zh:"项目ID"`
        Name        string       `json:"name" validate:"gte=1,lte=64" zh:"函数名称"`
        Category    string       `json:"category,default=Other" validate:"required" zh:"函数分类"`
        Description string       `json:"description" validate:"lte=255" zh:"函数描述"`
        Language    string       `json:"language,default=PYTHON" validate:"required" zh:"编程语言"`
        Content     string       `json:"content" validate:"required" zh:"函数内容"`
        Parameters  []*Parameter `json:"parameters" validate:"gte=0,dive,required" zh:"参数列表"`
        Returns     []*Return    `json:"returns" validate:"gte=0,dive,required" zh:"返回值列表"`
        Example     string       `json:"example" zh:"函数使用例子"`
    }
    CreateOrModifyDataProcessingFunctionResp {}
)

// 删除自定义函数
type (
    RemoveDataProcessingFunctionReq {
        ProjectId string   `json:"project_id" validate:"required" zh:"项目ID"`
        Names     []string `json:"names" validate:"gt=0" zh:"函数名称列表"`
    }
    RemoveDataProcessingFunctionResp {}
)

// 搜索数据处理函数
type (
    SearchDataProcessingFunctionReq {
        ProjectId  string       `json:"project_id" validate:"required" zh:"项目ID"`
        Condition  *Condition   `json:"condition,omitempty,optional" zh:"查询条件"`
        Pagination *Pagination  `json:"pagination,omitempty,optional" zh:"查询分页"`
        Sort       []*SortField `json:"sort,omitempty,optional" zh:"查询排序"`
    }
    SearchDataProcessingFunctionResp {
        CurrentPage uint64      `json:"current_page"`
        PageSize    uint64      `json:"page_size"`
        TotalCount  uint64      `json:"total_count"`
        TotalPage   uint64      `json:"total_page"`
        Items       []*Function `json:"items"`
    }
)

// 查看数据处理函数
type (
    ViewDataProcessingFunctionReq {
        ProjectId string `form:"project_id" validate:"required" zh:"项目ID"`
        Name      string `form:"name" validate:"required" zh:"函数名称"`
        Type      string `form:"type,default=CUSTOM,options=BUILTIN|CUSTOM" validate:"required" zh:"函数类型"`
    }
    ViewDataProcessingFunctionResp {
        *Function
    }
)
