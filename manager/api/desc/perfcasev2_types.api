syntax = "v1"

import "types.api"

type PerfCaseV2 {
    ProjectId     string         `json:"project_id"`
    CategoryId    string         `json:"category_id"`
    CaseId        string         `json:"case_id"`
    Name          string         `json:"name"`
    Description   string         `json:"description"`
    Tags          []string       `json:"tags"`
    Protocol      string         `json:"protocol"`
    RateLimits    []*RateLimitV2 `json:"rate_limits"`

    SetupSteps    []*PerfCaseStepV2 `json:"setup_steps"`
    SerialSteps   []*PerfCaseStepV2 `json:"serial_steps"`
    ParallelSteps []*PerfCaseStepV2 `json:"parallel_steps"`
    TeardownSteps []*PerfCaseStepV2 `json:"teardown_steps"`
    NumberOfSteps uint32            `json:"number_of_steps"`
    TargetRps     int64             `json:"target_rps"`

    MaintainedBy *FullUserInfo `json:"maintained_by"`

    CreatedBy    *FullUserInfo `json:"created_by"`
    UpdatedBy    *FullUserInfo `json:"updated_by"`
    CreatedAt    int64         `json:"created_at"`
    UpdatedAt    int64         `json:"updated_at"`
}

type SearchPerfCaseV2Item {
	ProjectId     string            `json:"project_id"`
	CategoryId    string            `json:"category_id"`
	CaseId        string            `json:"case_id"`
	Name          string            `json:"name"`
	Description   string            `json:"description"`
	Tags          []string          `json:"tags"`
	Protocol      string            `json:"protocol"`
	RateLimits    []*RateLimitV2    `json:"rate_limits"`
	SerialSteps   []*PerfCaseStepV2 `json:"serial_steps"`
	ParallelSteps []*PerfCaseStepV2 `json:"parallel_steps"`

    NumberOfSteps uint32 `json:"number_of_steps"`

    MaintainedBy *FullUserInfo `json:"maintained_by"`

    CreatedBy    *FullUserInfo `json:"created_by"`
    UpdatedBy    *FullUserInfo `json:"updated_by"`
    CreatedAt    int64         `json:"created_at"`
    UpdatedAt    int64         `json:"updated_at"`
}

// 创建压测用例
type (
    CreatePerfCaseV2Req {
        ProjectId     string            `json:"project_id" validate:"required" zh:"项目ID"`
        CategoryId    string            `json:"category_id" validate:"required" zh:"分类ID"`
        Name          string            `json:"name" validate:"gte=1,lte=64" zh:"压测用例名称"`
        Description   string            `json:"description" validate:"lte=255" zh:"压测用例描述"`
        Tags          []string          `json:"tags" validate:"gte=0" zh:"压测用例标签"`
        Protocol      string            `json:"protocol" validate:"oneof=TT TT_AUTH gRPC HTTP" zh:"协议"`
        RateLimits    []*RateLimitV2    `json:"rate_limits" validate:"gte=1" zh:"限流配置"`
        SetupSteps    []*PerfCaseStepV2 `json:"setup_steps" zh:"前置步骤"`
        SerialSteps   []*PerfCaseStepV2 `json:"serial_steps" zh:"串行步骤"`
        ParallelSteps []*PerfCaseStepV2 `json:"parallel_steps" zh:"并行步骤"`
        TeardownSteps []*PerfCaseStepV2 `json:"teardown_steps" zh:"后置步骤"`
        MaintainedBy  string            `json:"maintained_by" validate:"lte=64" zh:"压测用例维护者"`
    }
    CreatePerfCaseV2Resp {
        CaseId string `json:"case_id"`
    }
)

// 删除压测用例
type (
    RemovePerfCaseV2Req {
	    ProjectId string   `json:"project_id" validate:"required" zh:"项目ID"`
	    CaseIds   []string `json:"case_ids" validate:"gt=0" zh:"压测用例ID列表"`
    }
    RemovePerfCaseV2Resp {}
)

// 编辑压测用例
type (
    ModifyPerfCaseV2Req {
        ProjectId     string            `json:"project_id" validate:"required" zh:"项目ID"`
        CategoryId    string            `json:"category_id" validate:"required" zh:"分类ID"`
        CaseId        string            `json:"case_id" validate:"required" zh:"压测用例ID"`
        Name          string            `json:"name" validate:"gte=1,lte=64" zh:"压测用例名称"`
        Description   string            `json:"description" validate:"lte=255" zh:"压测用例描述"`
        Tags          []string          `json:"tags" validate:"gte=0" zh:"压测用例标签"`
        Protocol      string            `json:"protocol" validate:"oneof=TT TT_AUTH gRPC HTTP" zh:"协议"`
        RateLimits    []*RateLimitV2    `json:"rate_limits" validate:"gte=1" zh:"限流配置"`
        SetupSteps    []*PerfCaseStepV2 `json:"setup_steps" zh:"前置步骤"`
        SerialSteps   []*PerfCaseStepV2 `json:"serial_steps" zh:"串行步骤"`
        ParallelSteps []*PerfCaseStepV2 `json:"parallel_steps" zh:"并行步骤"`
        TeardownSteps []*PerfCaseStepV2 `json:"teardown_steps" zh:"后置步骤"`
        MaintainedBy  string            `json:"maintained_by" validate:"lte=64" zh:"压测用例维护者"`
    }
    ModifyPerfCaseV2Resp {}
)

// 查询压测用例
type (
    SearchPerfCaseV2Req {
        ProjectId  string       `json:"project_id" validate:"required" zh:"项目ID"`
        CategoryId string       `json:"category_id" validate:"required" zh:"分类ID"`
        Condition  *Condition   `json:"condition,omitempty,optional" zh:"查询条件"`
        Pagination *Pagination  `json:"pagination,omitempty,optional" zh:"查询分页"`
        Sort       []*SortField `json:"sort,omitempty,optional" zh:"查询排序"`
    }
    SearchPerfCaseV2Resp {
        CurrentPage uint64                  `json:"current_page"`
        PageSize    uint64                  `json:"page_size"`
        TotalCount  uint64                  `json:"total_count"`
        TotalPage   uint64                  `json:"total_page"`
        Items       []*SearchPerfCaseV2Item `json:"items"`
    }
)

// 查看压测用例
type (
    ViewPerfCaseV2Req {
        ProjectId string `form:"project_id" validate:"required" zh:"项目ID"`
        CaseId    string `form:"case_id" validate:"required" zh:"压测用例ID"`
    }
    ViewPerfCaseV2Resp {
        *PerfCaseV2
    }
)
