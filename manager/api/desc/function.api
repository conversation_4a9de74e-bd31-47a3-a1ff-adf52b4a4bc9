syntax = "v1"

import "function_types.api"

@server (
    prefix: manager/v1
    group: dataprocessingfunction
)
service manager {
    @doc "create custom function"
    @handler createDataProcessingFunction
    post /function/create (CreateOrModifyDataProcessingFunctionReq) returns (CreateOrModifyDataProcessingFunctionResp)

    @doc "delete custom function"
    @handler removeDataProcessingFunction
    put /function/remove (RemoveDataProcessingFunctionReq) returns (RemoveDataProcessingFunctionResp)

    @doc "modify custom function"
    @handler modifyDataProcessingFunction
    put /function/modify (CreateOrModifyDataProcessingFunctionReq) returns (CreateOrModifyDataProcessingFunctionResp)

    @doc "search data processing function"
    @handler searchDataProcessingFunction
    post /function/search (SearchDataProcessingFunctionReq) returns (SearchDataProcessingFunctionResp)

    @doc "view data processing function"
    @handler viewDataProcessingFunction
    get /function/view (ViewDataProcessingFunctionReq) returns (ViewDataProcessingFunctionResp)
}
