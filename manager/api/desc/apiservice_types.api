syntax = "v1"

import "types.api"

type ApiServiceCase {
    ProjectId     string         `json:"project_id"`
    DocumentId    string         `json:"document_id"`
    CaseId        string         `json:"case_id"`
    Name          string         `json:"name"`
    Description   string         `json:"description"`
    Priority      int64          `json:"priority"`
    Tags          []string       `json:"tags"`
    AccountConfig *AccountConfig `json:"account_config"`
    Version       string         `json:"version"`
    MaintainedBy  *FullUserInfo  `json:"maintained_by,omitempty"`
    CreatedBy     *FullUserInfo  `json:"created_by"`
    UpdatedBy     *FullUserInfo  `json:"updated_by"`
    CreatedAt     int64          `json:"created_at"`
    UpdatedAt     int64          `json:"updated_at"`
    Nodes         []*Node        `json:"nodes"`
    Edges         []*Edge        `json:"edges"`
    Combos        []*Combo       `json:"combos"`
}

// 查看精准测试用例
type (
    ViewApiServiceCaseReq {
        ProjectId  string `form:"project_id" validate:"required" zh:"项目ID"`
        ServiceId  string `form:"service_id" validate:"required" zh:"精准测试服务ID"`
        CaseId     string `form:"case_id" validate:"required" zh:"用例ID"`
        Version    string `form:"version,omitempty,optional" zh:"接口用例版本"`
    }
    ViewApiServiceCaseResp {
        *ApiServiceCase
    }
)
