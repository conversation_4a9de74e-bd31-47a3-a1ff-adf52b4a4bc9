syntax = "v1"

import "uiplan_types.api"

@server (
    prefix: manager/v1
    group: uiplan
)
service manager {
    @doc "create ui test plan"
    @handler createUiPlan
    post /ui_plan/create (CreateUiPlanReq) returns (CreateUiPlanResp)

    @doc "delete ui test plan"
    @handler removeUiPlan
    put /ui_plan/remove (RemoveUiPlanReq) returns (RemoveUiPlanResp)

    @doc "modify ui test plan"
    @handler modifyUiPlan
    put /ui_plan/modify (ModifyUiPlanReq) returns (ModifyUiPlanResp)

    @doc "search ui test plans"
    @handler searchUiPlan
    post /ui_plan/search (SearchUiPlanReq) returns (SearchUiPlanResp)

    @doc "view ui test plan"
    @handler viewUiPlan
    get /ui_plan/view (ViewUiPlanReq) returns (ViewUiPlanResp)

    @doc "get ui test case tree of ui test plan"
    @handler getCaseTreeOfUIPlan
    get /ui_plan/case_tree/get (GetCaseTreeOfUIPlanReq) returns (GetCaseTreeOfUIPlanResp)

    @doc "get ui test case tree of not added to ui test plan"
    @handler getCaseTreeOfNotAddedToUIPlan
    get /ui_plan/case_tree/remaining_get (GetCaseTreeOfNotAddedToUIPlanReq) returns (GetCaseTreeOfNotAddedToUIPlanResp)

    @doc "search cases in ui test plan"
    @handler searchCaseInUIPlan
    post /ui_plan/case/search (SearchCaseInUIPlanReq) returns (SearchCaseInUIPlanResp)

    @doc "list disable cases in ui test plan"
    @handler listDisableCaseInUIPlan
    post /ui_plan/disable_case/list (ListDisableCaseInUIPlanReq) returns (ListDisableCaseInUIPlanResp)

    @doc "search cases not in ui test plan"
    @handler searchCaseNotInUIPlan
    post /ui_plan/case/remaining_search (SearchCaseNotInUIPlanReq) returns (SearchCaseNotInUIPlanResp)

    @doc "add cases to ui test plan or remove cases from ui test plan"
    @handler modifyCaseListOfUIPlan
    put /ui_plan/case/modify (ModifyCaseListOfUIPlanReq) returns (ModifyCaseListOfUIPlanResp)

    @doc "search like ui test plans"
    @handler searchLikeUiPlan
    post /ui_plan/like/search (SearchUiPlanReq) returns (SearchUiPlanResp)
}
