syntax = "v1"

import "types.api"

type PerfLarkChat {
    ProjectId   string         `json:"project_id"`  // 项目ID
    ChatId      string         `json:"chat_id"`     // 飞书群组ID

    Name        string         `json:"name"`        // 飞书群组名称
    Avatar      string         `json:"avatar"`      // 飞书群组头像URL
    Description string         `json:"description"` // 飞书群组描述
    External    bool           `json:"external"`    // 是否是外部群
    Status      string         `json:"status"`      // 飞书群组状态

    CreatedBy   *FullUserInfo  `json:"created_by"`  // 创建者
    UpdatedBy   *FullUserInfo  `json:"updated_by"`  // 更新者
    CreatedAt   int64          `json:"created_at"`  // 创建时间
    UpdatedAt   int64          `json:"updated_at"`  // 更新时间
}

// 编辑压测通知飞书群组列表
type (
    ModifyPerfLarkChatReq {
        ProjectId string   `json:"project_id" validate:"required" zh:"项目ID"`
        LarkChats []string `json:"lark_chats" validate:"gte=0" zh:"飞书群组ID列表"`
    }
    ModifyPerfLarkChatResp {}
)

// 搜索压测通知飞书群组
type (
    SearchPerfLarkChatReq {
        ProjectId  string       `json:"project_id" validate:"required" zh:"项目ID"`
        Condition  *Condition   `json:"condition,omitempty,optional" zh:"查询条件"`
        Pagination *Pagination  `json:"pagination,omitempty,optional" zh:"查询分页"`
        Sort       []*SortField `json:"sort,omitempty,optional" zh:"查询排序"`
    }
    SearchPerfLarkChatResp {
        CurrentPage uint64          `json:"current_page"`
        PageSize    uint64          `json:"page_size"`
        TotalCount  uint64          `json:"total_count"`
        TotalPage   uint64          `json:"total_page"`
        Items       []*PerfLarkChat `json:"items"`
    }
)
