syntax = "v1"

import "types.api"

type ReviewRecord {
    ProjectId          string          `json:"project_id"`
    ReviewId           string          `json:"review_id"`
    ResourceBranch     string          `json:"resource_branch"`
    ResourceParentType string          `json:"resource_parent_type"`
    ResourceType       string          `json:"resource_type"`
    ResourceId         string          `json:"resource_id"`
    ResourceName       string          `json:"resource_name"`
    RemarkOfPending    string          `json:"remark_of_pending"`
    RemarkOfRevoked    string          `json:"remark_of_revoked"`
    RemarkOfReviewed   string          `json:"remark_of_reviewed"`
    AssignedReviewers  []*FullUserInfo `json:"assigned_reviewers"`
    Status             string          `json:"status"`
    CreatedBy          *FullUserInfo   `json:"created_by"`
    UpdatedBy          *FullUserInfo   `json:"updated_by"`
    CreatedAt          int64           `json:"created_at"`
    UpdatedAt          int64           `json:"updated_at"`
}

// 申请审核
type (
    CreateReviewRecordReq {
        ProjectId         string   `json:"project_id" validate:"required" zh:"项目ID"`
        ResourceType      string   `json:"resource_type" validate:"required,oneof=SETUP TEARDOWN GROUP API_CASE INTERFACE_CASE" zh:"申请审核的资源类型"`
        ResourceId        string   `json:"resource_id" validate:"required" zh:"申请审核的资源ID"`
        Remark            string   `json:"remark" validate:"lte=1024" zh:"申请审核的备注"`
        AssignedReviewers []string `json:"assigned_reviewers" validate:"gt=0" zh:"指派的审核者"`
    }
    CreateReviewRecordResp {
        ReviewId string `json:"review_id"`
    }
)

// 编辑审核
type (
    ModifyReviewRecordReq {
        ProjectId         string   `json:"project_id" validate:"required" zh:"项目ID"`
        ReviewId          string   `json:"review_id" validate:"required" zh:"审核ID"`
        Remark            string   `json:"remark" validate:"lte=1024" zh:"申请审核的备注"`
        AssignedReviewers []string `json:"assigned_reviewers" validate:"gt=0" zh:"指派的审核者"`
    }
    ModifyReviewRecordResp {}
)

// 撤回审核
type (
    RevokeReviewRecordReq {
        ProjectId string `json:"project_id" validate:"required" zh:"项目ID"`
        ReviewId  string `json:"review_id" validate:"required" zh:"审核ID"`
        Remark    string `json:"remark" validate:"lte=1024" zh:"撤回审核的备注"`
    }
    RevokeReviewRecordResp {}
)

// 审批审核
type (
    ApproveReviewRecordReq {
        ProjectId string `json:"project_id" validate:"required" zh:"项目ID"`
        ReviewId  string `json:"review_id" validate:"required" zh:"审核ID"`
        Result    string `json:"result" validate:"required,oneof=APPROVED REJECTED" zh:"审批结果"`
        Remark    string `json:"remark" validate:"lte=1024" zh:"审批审核的备注"`
    }
    ApproveReviewRecordResp {}
)

// 搜索审核记录
type (
    SearchReviewRecordReq {
        ProjectId  string       `json:"project_id" validate:"required" zh:"项目ID"`
        Condition  *Condition   `json:"condition,omitempty,optional" zh:"查询条件"`
        Pagination *Pagination  `json:"pagination,omitempty,optional" zh:"查询分页"`
        Sort       []*SortField `json:"sort,omitempty,optional" zh:"查询排序"`
    }
    SearchReviewRecordResp {
        CurrentPage uint64          `json:"current_page"`
        PageSize    uint64          `json:"page_size"`
        TotalCount  uint64          `json:"total_count"`
        TotalPage   uint64          `json:"total_page"`
        Items       []*ReviewRecord `json:"items"`
    }
)
