syntax = "v1"

import "advanced_search_types.api"

@server (
    prefix: manager/v1
    group: advancedsearch
)
service manager {
    @doc "search advanced search field"
    @handler SearchField
    post /advanced_search/field/search (SearchAdvancedSearchFieldReq) returns (SearchAdvancedSearchFieldResp)

    @doc "search advanced search condition"
    @handler SearchCondition
    post /advanced_search/condition/search (SearchAdvancedSearchConditionReq) returns (SearchAdvancedSearchConditionResp)

}
