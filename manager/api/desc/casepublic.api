syntax = "v1"

import "casepublic_types.api"

@server (
	prefix: manager/v1
	group: casepublic
)
service manager {
	@doc "search not released cases"
	@handler searchNotReleasedCase
	post /case/not_released/search (SearchCaseReq) returns (SearchCaseResp)

	@doc "search fail log cases"
	@handler searchFailLogCase
	post /case/fail_log/search (SearchCaseFailLogReq) returns (SearchCaseFailLogResp)

	@doc "delete fail log case"
	@handler deleteFailLogCase
	delete /case/fail_log/del (DeleteCaseFailLogReq) returns (DeleteCaseFailLogResp)

	@doc "batch delete fail log case "
	@handler batchDeleteFailLogCase
	delete /case/fail_log/batch/del (BatchDeleteCaseFailLogReq) returns (BatchDeleteCaseFailLogResp)
}
