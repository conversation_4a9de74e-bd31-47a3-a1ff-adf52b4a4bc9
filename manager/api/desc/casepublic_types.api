syntax = "v1"

import "types.api"

type Case {
    ProjectId string `json:"project_id"`
    BranchId string `json:"branch_id"`
    CaseId string `json:"case_id"`
    Name string `json:"name"`
    Description string `json:"description"`
    CaseType string `json:"case_type"`
    Priority int64 `json:"priority"`
    Tags []string `json:"tags"`
    State string `json:"state"`
    AccountConfig *AccountConfig `json:"account_config"`
    Version string `json:"version"`
    MaintainedBy *FullUserInfo `json:"maintained_by"`
    CreatedBy *FullUserInfo `json:"created_by"`
    UpdatedBy *FullUserInfo `json:"updated_by"`
    CreatedAt int64 `json:"created_at"`
    UpdatedAt int64 `json:"updated_at"`
    Nodes []*Node `json:"nodes"`
    Edges []*Edge `json:"edges"`
    Combos []*Combo `json:"combos"`
}
type FailCase {
    Case
    Rank uint32 `json:"rank"`
    FailCount uint32 `json:"fail_count"`
}

// 查询API用例
type (
    SearchCaseReq {
        ProjectId string `json:"project_id" validate:"required" zh:"项目ID"`
        Condition *Condition `json:"condition,omitempty,optional" zh:"查询条件"`
        Pagination *Pagination `json:"pagination,omitempty,optional" zh:"查询分页"`
        Sort []*SortField `json:"sort,omitempty,optional" zh:"查询排序"`
    }
    SearchCaseResp {
        CurrentPage uint64 `json:"current_page"`
        PageSize uint64 `json:"page_size"`
        TotalCount uint64 `json:"total_count"`
        TotalPage uint64 `json:"total_page"`
        Items []*Case `json:"items"`
    }
)

// 查询API用例
type (
    SearchCaseFailLogReq {
        ProjectId string `json:"project_id" validate:"required" zh:"项目ID"`
        Condition *Condition `json:"condition,omitempty,optional" zh:"查询条件"`
        Pagination *Pagination `json:"pagination,omitempty,optional" zh:"查询分页"`
        Sort []*SortField `json:"sort,omitempty,optional" zh:"查询排序"`
    }
    SearchCaseFailLogResp {
        CurrentPage uint64 `json:"current_page"`
        PageSize uint64 `json:"page_size"`
        TotalCount uint64 `json:"total_count"`
        TotalPage uint64 `json:"total_page"`
        Items []*FailCase `json:"items"`
    }
)

// 删除case用例失败记录
type (
    DeleteCaseFailLogReq {
        ProjectId string `json:"project_id" validate:"required" zh:"项目ID"`
        CaseId string `json:"case_id"`
        CaseType string `json:"case_type"`
    }
    DeleteCaseFailLogResp {
    }

    BatchDeleteCaseFailLogReq {
        ProjectId string `json:"project_id" validate:"required" zh:"项目ID"`
        DeleteCaseFailLogList []DeleteCaseFailLog `json:"delete_case_fail_log_list"`
    }

    BatchDeleteCaseFailLogResp {
    }

    DeleteCaseFailLog {
        CaseId string `json:"case_id"`
        CaseType string `json:"case_type"`
    }
)
