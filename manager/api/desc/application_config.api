syntax = "v1"

import "application_config_types.api"

@server (
    prefix: manager/v1
    group: applicationConfig
)
service manager {
    @handler createApplicationConfig
    post /application_config/create (CreateApplicationConfigReq) returns (CreateApplicationConfigResp)

    @handler removeApplicationConfig
    put /application_config/remove (RemoveApplicationConfigReq) returns (RemoveApplicationConfigResp)

    @handler modifyApplicationConfig
    put /application_config/modify (ModifyApplicationConfigReq) returns (ModifyApplicationConfigResp)

    @handler searchApplicationConfig
    post /application_config/search (SearchApplicationConfigReq) returns (SearchApplicationConfigResp)

    @handler viewApplicationConfig
    get /application_config/view (ViewApplicationConfigReq) returns (ViewApplicationConfigResp)

    @handler searchApplicationConfigReference
    post /application_config/reference/search (SearchApplicationConfigReferenceReq) returns (SearchApplicationConfigReferenceResp)
}
