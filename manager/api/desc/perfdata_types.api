syntax = "v1"

import "types.api"

type PerfData {
    ProjectId   string        `json:"project_id"`
    DataId      string        `json:"data_id"`
    Name        string        `json:"name"`
    Description string        `json:"description"`
    Extension   string        `json:"extension"`
    Hash        string        `json:"hash"`
    Size        uint32        `json:"size"`
    NumberOfVu  uint32        `json:"number_of_vu"`
    CreatedBy   *FullUserInfo `json:"created_by"`
    UpdatedBy   *FullUserInfo `json:"updated_by"`
    CreatedAt   int64         `json:"created_at"`
    UpdatedAt   int64         `json:"updated_at"`
}

type (
    UploadPerfDataReq {
        ProjectId string `form:"project_id" validate:"required" zh:"项目ID"`
    }
    UploadPerfDataResp {
        DataId string `json:"data_id"`
    }
)

type (
    DownloadPerfDataReq {
        ProjectId string `form:"project_id" validate:"required" zh:"项目ID"`
        DataId    string `form:"data_id" validate:"required" zh:"压测数据ID"`
    }
    DownloadPerfDataResp {}
)

type (
    RemovePerfDataReq {
	    ProjectId string   `json:"project_id" validate:"required" zh:"项目ID"`
	    DataIds   []string `json:"data_ids" validate:"gt=0" zh:"压测数据ID列表"`
    }
    RemovePerfDataResp {}
)

type (
    SearchPerfDataReq {
        ProjectId  string       `json:"project_id" validate:"required" zh:"项目ID"`
        Condition  *Condition   `json:"condition,omitempty,optional" zh:"查询条件"`
        Pagination *Pagination  `json:"pagination,omitempty,optional" zh:"查询分页"`
        Sort       []*SortField `json:"sort,omitempty,optional" zh:"查询排序"`
    }
    SearchPerfDataResp {
        CurrentPage uint64      `json:"current_page"`
        PageSize    uint64      `json:"page_size"`
        TotalCount  uint64      `json:"total_count"`
        TotalPage   uint64      `json:"total_page"`
        Items       []*PerfData `json:"items"`
    }
)
