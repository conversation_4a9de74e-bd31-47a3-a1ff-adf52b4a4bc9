syntax = "v1"

import "category_types.api"

@server (
    prefix: manager/v1
    group: category
)
service manager {
    @doc "create category"
    @handler createCategory
    post /category/create (CreateCategoryReq) returns (CreateCategoryResp)

    @doc "delete category"
    @handler removeCategory
    put /category/remove (RemoveCategoryReq) returns (RemoveCategoryResp)

    @doc "modify category"
    @handler modifyCategory
    put /category/modify (ModifyCategoryReq) returns (ModifyCategoryResp)

    @doc "search categories"
    @handler searchCategory
    post /category/search (SearchCategoryReq) returns (SearchCategoryResp)

    @doc "move category tree"
    @handler moveCategoryTree
    post /category/tree/move (MoveCategoryTreeReq) returns (MoveCategoryTreeResp)

    @doc "get category tree"
    @handler getCategoryTree
    get /category/tree/get (GetCategoryTreeReq) returns (GetCategoryTreeResp)
}
