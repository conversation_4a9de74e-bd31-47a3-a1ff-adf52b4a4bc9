syntax = "v1"

import "apiplan_types.api"

@server (
    prefix: manager/v1
    group: apiplan
)
service manager {
    @doc "create api test plan"
    @handler createApiPlan
    post /api_plan/create (CreateApiPlanReq) returns (CreateApiPlanResp)

    @doc "delete api test plan"
    @handler removeApiPlan
    put /api_plan/remove (RemoveApiPlanReq) returns (RemoveApiPlanResp)

    @doc "modify api test plan"
    @handler modifyApiPlan
    put /api_plan/modify (ModifyApiPlanReq) returns (ModifyApiPlanResp)

    @doc "search api test plans"
    @handler searchApiPlan
    post /api_plan/search (SearchApiPlanReq) returns (SearchApiPlanResp)

    @doc "view api test plan"
    @handler viewApiPlan
    get /api_plan/view (ViewApiPlanReq) returns (ViewApiPlanResp)

    @doc "search suites in api test plan"
    @handler searchSuiteInApiPlan
    post /api_plan/suite/search (SearchSuiteInApiPlanReq) returns (SearchSuiteInApiPlanResp)

    @doc "search suites not in api test plan"
    @handler searchSuiteNotInApiPlan
    post /api_plan/suite/remaining_search (SearchSuiteNotInApiPlanReq) returns (SearchSuiteNotInApiPlanResp)

    @doc "advanced search suites not in api test plan"
    @handler AdvancedSearchSuiteNotInApiPlan
    post /api_plan/suite/advanced_remaining_search (AdvancedSearchSuiteNotInApiPlanReq) returns (AdvancedSearchSuiteNotInApiPlanResp)

    @doc "add suites to api test plan or remove suites from api test plan"
    @handler modifySuiteListOfApiPlan
    put /api_plan/suite/modify (ModifySuiteListOfApiPlanReq) returns (ModifySuiteListOfApiPlanResp)

    @doc "search cases in api test plan"
    @handler searchCaseInApiPlan
    post /api_plan/suite/case/search (SearchCaseInApiPlanReq) returns (SearchCaseInApiPlanResp)

    @doc "modify reference state of suites or cases whose in api test plan"
    @handler modifyApiPlanReferenceState
    put /api_plan/reference_state/modify (ModifyApiPlanReferenceStateReq) returns (ModifyApiPlanReferenceStateResp)

    @doc "search like api test plans"
    @handler searchLikeApiPlan
    post /api_plan/like/search (SearchApiPlanReq) returns (SearchApiPlanResp)
}
