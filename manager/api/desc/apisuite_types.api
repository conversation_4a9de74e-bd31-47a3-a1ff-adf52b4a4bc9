syntax = "v1"

import (
    "types.api"
    "apicase_types.api"
)

type ApiSuite {
    ProjectId         string        `json:"project_id"`          // 项目ID
    CategoryId        string        `json:"category_id"`         // 所属分类ID
    SuiteId           string        `json:"suite_id"`            // 集合ID
    Name              string        `json:"name"`                // 集合名称
    Description       string        `json:"description"`         // 集合描述
    Priority          int64         `json:"priority"`            // 优先级
    Tags              []string      `json:"tags"`                // 标签
    State             int8          `json:"state"`               // 状态
    CaseExecutionMode int8          `json:"case_execution_mode"` // 用例执行方式
    MaintainedBy      *FullUserInfo `json:"maintained_by"`       // 维护者
    CreatedBy         *FullUserInfo `json:"created_by"`          // 创建者
    UpdatedBy         *FullUserInfo `json:"updated_by"`          // 更新者
    CreatedAt         int64         `json:"created_at"`          // 创建时间
    UpdatedAt         int64         `json:"updated_at"`          // 更新时间
}

type SearchApiSuiteReferenceItem {
    ProjectId      string        `json:"project_id"`      // 项目ID
    SuiteId        string        `json:"suite_id"`        // 集合ID
    ReferenceType  string        `json:"reference_type"`  // 引用对象类型
    ReferenceId    string        `json:"reference_id"`    // 引用对象ID
    Name           string        `json:"name"`            // 引用对象名称
    Description    string        `json:"description"`     // 引用对象描述
    Priority       int64         `json:"priority"`        // 优先级
    Tags           []string      `json:"tags"`            // 标签
    State          int8          `json:"state"`           // 状态
    ReferenceState int8          `json:"reference_state"` // 引用状态
    MaintainedBy   *FullUserInfo `json:"maintained_by"`   // 维护者
    CreatedBy      *FullUserInfo `json:"created_by"`      // 创建者
    UpdatedBy      *FullUserInfo `json:"updated_by"`      // 更新者
    CreatedAt      int64         `json:"created_at"`      // 创建时间
    UpdatedAt      int64         `json:"updated_at"`      // 更新时间
}

type SearchCaseInApiSuiteItem {
    ProjectId     string         `json:"project_id"`
    CategoryId    string         `json:"category_id,omitempty,optional" zh:"场景用例的所属分类ID"`
    DocumentId    string         `json:"document_id,omitempty,optional" zh:"接口用例的接口文档ID"`
    CaseType      string         `json:"case_type" validate:"required,oneof=API_CASE INTERFACE_CASE" zh:"用例类型"`
    CaseId        string         `json:"case_id"`
    Name          string         `json:"name"`
    Description   string         `json:"description"`
    Priority      int64          `json:"priority"`
    Tags          []string       `json:"tags"`
    State         string         `json:"state"`
    AccountConfig *AccountConfig `json:"account_config"`
    Version       string         `json:"version"`
    MaintainedBy  *FullUserInfo  `json:"maintained_by"`
    CreatedBy     *FullUserInfo  `json:"created_by"`
    UpdatedBy     *FullUserInfo  `json:"updated_by"`
    CreatedAt     int64          `json:"created_at"`
    UpdatedAt     int64          `json:"updated_at"`
}

type CaseTypeId {
    CaseType string `json:"case_type" validate:"required,oneof=API_CASE INTERFACE_CASE" zh:"用例类型"`
    CaseId   string `json:"case_id" validate:"required" zh:"用例ID"`
}

// 创建API集合
type (
    CreateApiSuiteReq {
        ProjectId         string        `json:"project_id" validate:"required" zh:"项目ID"`
        CategoryId        string        `json:"category_id" validate:"required" zh:"分类ID"`
        Name              string        `json:"name" validate:"gte=1,lte=64" zh:"API集合名称"`
        Description       string        `json:"description" validate:"lte=255" zh:"API集合描述"`
        Priority          int64         `json:"priority" validate:"gte=0" zh:"优先级"`
        Tags              []string      `json:"tags" validate:"gte=0" zh:"API集合标签"`
        CaseExecutionMode int8          `json:"case_execution_mode" validate:"oneof=1 2" zh:"用例执行方式"`
        MaintainedBy      string        `json:"maintained_by" validate:"lte=64" zh:"API集合维护者"`
        CaseIds           []*CaseTypeId `json:"case_ids,optional" validate:"omitempty,gte=0" zh:"导入的用例列表"`
    }
    CreateApiSuiteResp {
        SuiteId string `json:"suite_id"`
    }
)

// 删除API集合
type (
    RemoveApiSuiteReq {
        ProjectId string   `json:"project_id" validate:"required" zh:"项目ID"`
        SuiteIds  []string `json:"suite_ids" validate:"gt=0" zh:"API集合ID列表"`
    }
    RemoveApiSuiteResp {}
)

// 编辑API集合
type (
    ModifyApiSuiteReq {
        ProjectId         string   `json:"project_id" validate:"required" zh:"项目ID"`
        CategoryId        string   `json:"category_id" validate:"required" zh:"分类ID"`
        SuiteId           string   `json:"suite_id" validate:"required" zh:"API集合ID"`
        Name              string   `json:"name" validate:"gte=1,lte=64" zh:"API集合名称"`
        Description       string   `json:"description" validate:"lte=255" zh:"API集合描述"`
        Priority          int64    `json:"priority" validate:"gte=0" zh:"优先级"`
        Tags              []string `json:"tags" validate:"gte=0" zh:"API集合标签"`
        State             int8     `json:"state" validate:"oneof=1 2" zh:"API集合状态"`
        CaseExecutionMode int8     `json:"case_execution_mode" validate:"oneof=1 2" zh:"用例执行方式"`
        MaintainedBy      string   `json:"maintained_by" validate:"lte=64" zh:"API集合维护者"`
    }
    ModifyApiSuiteResp {}
)

// 查询API集合
type (
    SearchApiSuiteReq {
        ProjectId  string       `json:"project_id" validate:"required" zh:"项目ID"`
        CategoryId string       `json:"category_id" validate:"required" zh:"分类ID"`
        Condition  *Condition   `json:"condition,omitempty,optional" zh:"查询条件"`
        Pagination *Pagination  `json:"pagination,omitempty,optional" zh:"查询分页"`
        Sort       []*SortField `json:"sort,omitempty,optional" zh:"查询排序"`
    }
    SearchApiSuiteResp {
        CurrentPage uint64      `json:"current_page"`
        PageSize    uint64      `json:"page_size"`
        TotalCount  uint64      `json:"total_count"`
        TotalPage   uint64      `json:"total_page"`
        Items       []*ApiSuite `json:"items"`
    }
)

// 查看API集合
type (
    ViewApiSuiteReq {
        ProjectId string `form:"project_id" validate:"required" zh:"项目ID"`
        SuiteId   string `form:"suite_id" validate:"required" zh:"API集合ID"`
    }
    ViewApiSuiteResp {
        *ApiSuite
    }
)

// 搜索API集合中的API用例
type (
    SearchApiCaseInApiSuiteReq {
        ProjectId  string       `json:"project_id" validate:"required" zh:"项目ID"`
        SuiteId    string       `json:"suite_id" validate:"required" zh:"集合ID"`
        Condition  *Condition   `json:"condition,omitempty,optional" zh:"查询条件"`
        Pagination *Pagination  `json:"pagination,omitempty,optional" zh:"查询分页"`
        Sort       []*SortField `json:"sort,omitempty,optional" zh:"查询排序"`
    }
    SearchApiCaseInApiSuiteResp {
        CurrentPage uint64     `json:"current_page"`
        PageSize    uint64     `json:"page_size"`
        TotalCount  uint64     `json:"total_count"`
        TotalPage   uint64     `json:"total_page"`
        Items       []*ApiCase `json:"items"`
    }
)

// 搜索不在指定的API集合中的API用例
type (
    SearchApiCaseNotInApiSuiteReq {
        ProjectId  string       `json:"project_id" validate:"required" zh:"项目ID"`
        SuiteId    string       `json:"suite_id" validate:"required" zh:"集合ID"`
        CategoryId string       `json:"category_id" validate:"required" zh:"用例分类树的分类ID"`
        Condition  *Condition   `json:"condition,omitempty,optional" zh:"查询条件"`
        Pagination *Pagination  `json:"pagination,omitempty,optional" zh:"查询分页"`
        Sort       []*SortField `json:"sort,omitempty,optional" zh:"查询排序"`
    }
    SearchApiCaseNotInApiSuiteResp {
        CurrentPage uint64     `json:"current_page"`
        PageSize    uint64     `json:"page_size"`
        TotalCount  uint64     `json:"total_count"`
        TotalPage   uint64     `json:"total_page"`
        Items       []*ApiCase `json:"items"`
    }
)

// 编辑API集合的API用例列表
type (
    ModifyApiCaseListOfApiSuiteReq {
        ProjectId     string   `json:"project_id" validate:"required" zh:"项目ID"`
        SuiteId       string   `json:"suite_id" validate:"required" zh:"集合ID"`
        CaseIds       []string `json:"case_ids" validate:"gt=0" zh:"待变更的API用例ID列表"`
        OperationType string   `json:"operation_type" validate:"oneof=ADD REMOVE" zh:"操作类型"`
    }
    ModifyApiCaseListOfApiSuiteResp {}
)

// 搜索API集合引用详情
type (
    SearchApiSuiteReferenceReq {
        ProjectId  string       `json:"project_id" validate:"required" zh:"项目ID"`
        SuiteId    string       `json:"suite_id" validate:"required" zh:"集合ID"`
        Condition  *Condition   `json:"condition,omitempty,optional" zh:"查询条件"`
        Pagination *Pagination  `json:"pagination,omitempty,optional" zh:"查询分页"`
        Sort       []*SortField `json:"sort,omitempty,optional" zh:"查询排序"`
    }
    SearchApiSuiteReferenceResp {
        CurrentPage uint64                         `json:"current_page"`
        PageSize    uint64                         `json:"page_size"`
        TotalCount  uint64                         `json:"total_count"`
        TotalPage   uint64                         `json:"total_page"`
        Items       []*SearchApiSuiteReferenceItem `json:"items"`
    }
)

// 修改API集合所在的API计划的引用状态
type (
    ModifyApiSuiteReferenceStateReq {
        ProjectId string   `json:"project_id" validate:"required" zh:"项目ID"`
        SuiteId   string   `json:"suite_id" validate:"required" zh:"API集合ID"`
        PlanIds   []string `json:"plan_ids" validate:"gt=0" zh:"API计划ID列表"`
        State     int8     `json:"state" validate:"oneof=1 2" zh:"API集合引用状态"`
    }
    ModifyApiSuiteReferenceStateResp {}
)

// 搜索API集合中的用例
type (
    SearchCaseInApiSuiteReq {
        ProjectId  string       `json:"project_id" validate:"required" zh:"项目ID"`
        SuiteId    string       `json:"suite_id" validate:"required" zh:"集合ID"`
        Condition  *Condition   `json:"condition,omitempty,optional" zh:"查询条件"`
        Pagination *Pagination  `json:"pagination,omitempty,optional" zh:"查询分页"`
        Sort       []*SortField `json:"sort,omitempty,optional" zh:"查询排序"`
    }
    SearchCaseInApiSuiteResp {
        CurrentPage uint64                      `json:"current_page"`
        PageSize    uint64                      `json:"page_size"`
        TotalCount  uint64                      `json:"total_count"`
        TotalPage   uint64                      `json:"total_page"`
        Items       []*SearchCaseInApiSuiteItem `json:"items"`
    }
)

// 编辑API集合的用例列表
type (
    ModifyCaseListOfApiSuiteReq {
        ProjectId     string        `json:"project_id" validate:"required" zh:"项目ID"`
        SuiteId       string        `json:"suite_id" validate:"required" zh:"集合ID"`
        CaseIds       []*CaseTypeId `json:"case_ids" validate:"gt=0" zh:"待变更的用例列表"`
        OperationType string        `json:"operation_type" validate:"oneof=ADD REMOVE" zh:"操作类型"`
    }
    ModifyCaseListOfApiSuiteResp {}
)

type (
    SearchServiceCaseNotInApiSuiteReq {
        ProjectId  string       `json:"project_id" validate:"required" zh:"项目ID"`
        SuiteId    string       `json:"suite_id" validate:"required" zh:"集合ID"`
        Services   []string     `json:"services" zh:"服务列表"`
        Condition  *Condition   `json:"condition,omitempty,optional" zh:"查询条件"`
        Pagination *Pagination  `json:"pagination,omitempty,optional" zh:"查询分页"`
        Sort       []*SortField `json:"sort,omitempty,optional" zh:"查询排序"`
    }
    SearchServiceCaseNotInApiSuiteResp {
        CurrentPage uint64                      `json:"current_page"`
        PageSize    uint64                      `json:"page_size"`
        TotalCount  uint64                      `json:"total_count"`
        TotalPage   uint64                      `json:"total_page"`
        Items       []*SearchCaseInApiSuiteItem `json:"items"`
    }
)