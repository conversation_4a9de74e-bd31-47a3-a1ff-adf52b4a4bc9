syntax = "v1"

import "perfplan_types.api"

@server (
    prefix: manager/v1
    group: perfPlan
)
service manager {
    @handler createPerfPlan
    post /perf_plan/create (CreatePerfPlanReq) returns (CreatePerfPlanResp)

    @handler removePerfPlan
    put /perf_plan/remove (RemovePerfPlanReq) returns (RemovePerfPlanResp)

    @handler modifyPerfPlan
    put /perf_plan/modify (ModifyPerfPlanReq) returns (ModifyPerfPlanResp)

    @handler searchPerfPlan
    post /perf_plan/search (SearchPerfPlanReq) returns (SearchPerfPlanResp)

    @handler viewPerfPlan
    get /perf_plan/view (ViewPerfPlanReq) returns (ViewPerfPlanResp)

    @handler searchCaseInPerfPlan
    post /perf_plan/case/search (SearchCaseInPerfPlanReq) returns (SearchCaseInPerfPlanResp)
}
