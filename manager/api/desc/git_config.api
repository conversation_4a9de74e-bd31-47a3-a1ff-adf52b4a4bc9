syntax = "v1"

import "git_config_types.api"

@server (
    prefix: manager/v1
    group: gitconfig
)
service manager {
    @doc "create git config"
    @handler createGitConfig
    post /git_config/create (CreateGitConfigReq) returns (CreateGitConfigResp)

    @doc "delete git config"
    @handler removeGitConfig
    put /git_config/remove (RemoveGitConfigReq) returns (RemoveGitConfigResp)

    @doc "modify git config"
    @handler modifyGitConfig
    put /git_config/modify (ModifyGitConfigReq) returns (ModifyGitConfigResp)

    @doc "search git configs"
    @handler searchGitConfig
    post /git_config/search (SearchGitConfigReq) returns (SearchGitConfigResp)

    @doc "view git config"
    @handler viewGitConfig
    get /git_config/view (ViewGitConfigReq) returns (ViewGitConfigResp)

    @doc "test git config"
    @handler testGitConfig
    post /git_config/test (TestGitConfigReq) returns (TestGitConfigResp)

    @doc "sync git config"
    @handler syncGitConfig
    post /git_config/sync (SyncGitConfigReq) returns (SyncGitConfigResp)

    @doc "sync git config by webhook"
    @handler syncGitConfigByWebhook
    post /git_config/sync/webhook (SyncGitConfigByWebhookReq) returns (SyncGitConfigByWebhookResp)
}
