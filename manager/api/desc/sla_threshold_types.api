syntax = "v1"

import "types.api"

type (
    ModifySlaThresholdReq {
        ProjectId string `json:"project_id" validate:"required" zh:"项目ID"`
        IosRelease string `json:"ios_release" validate:"required" zh:"IOS正式分支的阈值配置"`
        AndroidRelease string `json:"android_release" validate:"required" zh:"Android正式分支的阈值配置"`
        AndroidTesting string `json:"android_testing" validate:"required" zh:"Android测试分支的阈值配置"`
    }
    ModifySlaThresholdResp {}
)

type (
    GetSlaThresholdReq{
        ProjectId string `form:"project_id" validate:"required" zh:"项目ID"`
    }
    GetSlaThresholdResp{
        IosRelease string `json:"ios_release" zh:"IOS正式分支的阈值配置"`
        AndroidRelease string `json:"android_release" zh:"Android正式分支的阈值配置"`
        AndroidTesting string `json:"android_testing" zh:"Android测试分支的阈值配置"`
    }
)