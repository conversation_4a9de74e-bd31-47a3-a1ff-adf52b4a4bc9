syntax = "v1"

import "types.api"

type GeneralConfiguration {
    ProjectId   string          `json:"project_id"`
    ConfigId    string          `json:"config_id"`
    Type        string          `json:"type"`
    Name        string          `json:"name"`
    Description string          `json:"description"`
    BaseUrl     string          `json:"base_url"`
    Verify      bool            `json:"verify"`
    Variables   []*KeyValuePair `json:"variables"`
    CreatedBy   *FullUserInfo   `json:"created_by"`
    UpdatedBy   *FullUserInfo   `json:"updated_by"`
    CreatedAt   int64           `json:"created_at"`
    UpdatedAt   int64           `json:"updated_at"`
}

type (
    CreateGeneralConfigReq {
        ProjectId   string          `json:"project_id" validate:"required" zh:"项目ID"`
        Type        string          `json:"type" validate:"required,oneof=API PERF" zh:"类型（接口测试、压力测试）"`
        Name        string          `json:"name" validate:"gte=1,lte=64" zh:"通用配置名称"`
        Description string          `json:"description" validate:"lte=255" zh:"通用配置描述"`
        BaseUrl     string          `json:"base_url" validate:"omitempty,url,lte=128" zh:"通用配置中的HTTP请求基础URL"`
        Verify      bool            `json:"verify" validate:"omitempty,boolean" zh:"是否开启SSL"`
        Variables   []*KeyValuePair `json:"variables" validate:"gte=0" zh:"通用配置中的变量列表"`
    }
    CreateGeneralConfigResp{
        ConfigId string `json:"config_id"`
    }
)

type (
    RemoveGeneralConfigReq {
        ProjectId string   `json:"project_id" zh:"项目ID"`
        ConfigIds []string `json:"config_ids" zh:"通用配置ID列表"`
    }
    RemoveGeneralConfigResp {}
)

type (
    ModifyGeneralConfigReq {
        ProjectId   string          `json:"project_id" validate:"required" zh:"项目ID"`
        ConfigId    string          `json:"config_id" validate:"required" zh:"通用配置ID"`
        Name        string          `json:"name" validate:"gte=1,lte=64" zh:"通用配置名称"`
        Description string          `json:"description" validate:"lte=255" zh:"通用配置描述"`
        BaseUrl     string          `json:"base_url" validate:"omitempty,url,lte=128" zh:"通用配置中的HTTP请求基础URL"`
        Verify      bool            `json:"verify" validate:"omitempty,boolean" zh:"是否开启SSL"`
        Variables   []*KeyValuePair `json:"variables" validate:"gte=0" zh:"通用配置中的变量列表"`
    }
    ModifyGeneralConfigResp {}
)

type (
    SearchGeneralConfigReq {
        ProjectId  string       `json:"project_id" validate:"required" zh:"项目ID"`
        Condition  *Condition   `json:"condition,omitempty,optional" zh:"查询条件"`
        Pagination *Pagination  `json:"pagination,omitempty,optional" zh:"查询分页"`
        Sort       []*SortField `json:"sort,omitempty,optional" zh:"查询排序"`
    }
    SearchGeneralConfigResp {
        CurrentPage uint64                  `json:"current_page"`
        PageSize    uint64                  `json:"page_size"`
        TotalCount  uint64                  `json:"total_count"`
        TotalPage   uint64                  `json:"total_page"`
        Items       []*GeneralConfiguration `json:"items"`
    }
)

type (
    ViewGeneralConfigReq {
        ProjectId string `form:"project_id" validate:"required" zh:"项目ID"`
        ConfigId  string `form:"config_id" validate:"required" zh:"通用配置ID"`
    }
    ViewGeneralConfigResp {
        *GeneralConfiguration
    }
)
