syntax = "v1"

import "prompt_config_types.api"

@server (
    prefix: manager/v1
    group: promptConfig
)
service manager {
    @handler createPromptConfig
    post /prompt_config/create (CreatePromptConfigReq) returns (CreatePromptConfigResp)

    @handler removePromptConfig
    put /prompt_config/remove (RemovePromptConfigReq) returns (RemovePromptConfigResp)

    @handler modifyPromptConfig
    put /prompt_config/modify (ModifyPromptConfigReq) returns (ModifyPromptConfigResp)

    @handler searchPromptConfig
    post /prompt_config/search (SearchPromptConfigReq) returns (SearchPromptConfigResp)

    @handler viewPromptConfig
    get /prompt_config/view (ViewPromptConfigReq) returns (ViewPromptConfigResp)

    @handler searchPromptConfigReference
    post /prompt_config/reference/search (SearchPromptConfigReferenceReq) returns (SearchPromptConfigReferenceResp)
}
