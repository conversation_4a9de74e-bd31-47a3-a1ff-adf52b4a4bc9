syntax = "v1"

import "types.api"
import "notify_types.api"

type PerfPlanV2 {
    ProjectId          string         `json:"project_id"`         // 项目ID
    PlanId             string         `json:"plan_id"`            // 计划ID
    CategoryId         string         `json:"category_id"`        // 分类ID

    Name               string         `json:"name"`               // 计划名称
    Description        string         `json:"description"`        // 计划描述
    Type               string         `json:"type"`               // 计划类型（手动、定时、接口）
    CronExpression     string         `json:"cron_expression"`    // 定时触发的Cron表达式
    Tags               []string       `json:"tags"`               // 标签
    Protocol           string         `json:"protocol"`           // 协议（TT私有协议、TT登录压测场景专用、通用gRPC协议、通用HTTP协议）
    TargetEnv          string         `json:"target_env"`         // 目标环境（生产环境、测试环境）
    ProtobufConfigId   string         `json:"protobuf_config_id"` // Deprecated: Protobuf配置ID
    GeneralConfigId    string         `json:"general_config_id"`  // 通用配置ID
    AccountConfigId    string         `json:"account_config_id"`  // 池账号配置ID
    AuthRateLimits     []*RateLimitV2 `json:"auth_rate_limits"`   // TT登录压测场景专用的登录接口限流配置

	CustomDuration       bool         `json:"custom_duration"`       // 是否自定义压测持续时长
	Duration             uint32       `json:"duration"`              // 压测持续时长
	CreateLarkChat       bool         `json:"create_lark_chat"`      // 是否需要自动拉群
	AdvancedNotification bool         `json:"advanced_notification"` // 是否需要提前通知

    State              int8           `json:"state"`              // 状态
    MaintainedBy       *FullUserInfo  `json:"maintained_by"`      // 维护者

    CreatedBy          *FullUserInfo  `json:"created_by"`         // 创建者
    UpdatedBy          *FullUserInfo  `json:"updated_by"`         // 更新者
    CreatedAt          int64          `json:"created_at"`         // 创建时间
    UpdatedAt          int64          `json:"updated_at"`         // 更新时间
}

type PerfPlanCaseV2Item {
    CaseId        string         `json:"case_id"`
    RateLimits    []*RateLimitV2 `json:"rate_limits"` // 限流配置

    BasicPerfData
    LoadGenerator
}

type SearchPerfPlanV2Item {
    ProjectId      string        `json:"project_id"`       // 项目ID
    PlanId         string        `json:"plan_id"`          // 计划ID
    CategoryId     string        `json:"category_id"`      // 分类ID

    Name           string         `json:"name"`            // 计划名称
    Description    string         `json:"description"`     // 计划描述
    Type           string         `json:"type"`            // 计划类型（手动、定时、接口）
    CronExpression string         `json:"cron_expression"` // 定时触发的Cron表达式
    Tags           []string       `json:"tags"`            // 标签
    Protocol       string         `json:"protocol"`        // 协议（TT私有协议、TT登录压测场景专用、通用gRPC协议、通用HTTP协议）
    TargetEnv      string         `json:"target_env"`      // 目标环境（生产环境、测试环境）

    CustomDuration bool           `json:"custom_duration"` // 是否自定义压测持续时长
	Duration       uint32         `json:"duration"`        // 压测持续时长

    NumberOfCases  uint32         `json:"number_of_cases"` // 用例数量
    NumberOfSteps  uint32         `json:"number_of_steps"` // 步骤数量（串行和并行）
    StatsOfStep    []*StatsOfStep `json:"stats_of_step"`   // 步骤统计信息

    State          int8          `json:"state"`            // 状态
    MaintainedBy   *FullUserInfo `json:"maintained_by"`    // 维护者

    CreatedBy      *FullUserInfo `json:"created_by"`       // 创建者
    UpdatedBy      *FullUserInfo `json:"updated_by"`       // 更新者
    CreatedAt      int64         `json:"created_at"`       // 创建时间
    UpdatedAt      int64         `json:"updated_at"`       // 更新时间
}

type StatsOfStep {
	TargetRps     int64  `json:"target_rps"`      // 目标的RPS
	NumberOfSteps uint32 `json:"number_of_steps"` // 步骤数量
}

type SearchCaseInPerfPlanV2Item {
	ProjectId   string `json:"project_id"`
	CategoryId  string `json:"category_id"`
	CaseId      string `json:"case_id"`

	Name          string `json:"name"`
	Description   string `json:"description"`
	RateLimits    []*RateLimitV2 `json:"rate_limits"` // 限流配置
    SerialSteps   []*PerfCaseStepV2 `json:"serial_steps"`
    ParallelSteps []*PerfCaseStepV2 `json:"parallel_steps"`

	BasicPerfData // 压测数据配置
	LoadGenerator // 施压机资源配置

	CreatedBy   *FullUserInfo `json:"created_by"`
	UpdatedBy   *FullUserInfo `json:"updated_by"`
	CreatedAt   int64         `json:"created_at"`
	UpdatedAt   int64         `json:"updated_at"`
}

type SearchProtobufInPerfPlanV2Item {
    ProjectId    string        `json:"project_id"`
    ConfigId     string        `json:"config_id"`

    Name         string        `json:"name"`
    Description  string        `json:"description"`
    GitConfigId  string        `json:"git_config"`
    ImportPath   string        `json:"import_path"`
    ExcludePaths []string      `json:"exclude_paths"`
    ExcludeFiles []string      `json:"exclude_files"`
    Dependencies []string      `json:"dependencies"`

    CreatedBy    *FullUserInfo `json:"created_by"`
    UpdatedBy    *FullUserInfo `json:"updated_by"`
    CreatedAt    int64         `json:"created_at"`
    UpdatedAt    int64         `json:"updated_at"`
}

type SearchRuleInPerfPlanV2Item {
    ProjectId   string `json:"project_id"` // 项目ID
    RuleId      string `json:"rule_id"`    // 规则ID

    Name        string  `json:"name"`        // 规则名称
    Description string  `json:"description"` // 规则描述
    MetricType  string  `json:"metric_type"` // 指标类型
    Threshold   float32 `json:"threshold"`   // 阀值
    Duration    uint32  `json:"duration"`    // 持续时间
    State       int8    `json:"state"`       // 状态

    CreatedBy   *FullUserInfo `json:"created_by"` // 创建者
    UpdatedBy   *FullUserInfo `json:"updated_by"` // 更新者
    CreatedAt   int64         `json:"created_at"` // 创建时间
    UpdatedAt   int64         `json:"updated_at"` // 更新时间
}

// 创建压测计划
type (
    CreatePerfPlanV2Req {
        ProjectId            string                `json:"project_id" validate:"required" zh:"项目ID"`
        CategoryId           string                `json:"category_id" validate:"required" zh:"分类ID"`
        Name                 string                `json:"name" validate:"gte=1,lte=64" zh:"压测计划名称"`
        Description          string                `json:"description" validate:"lte=255" zh:"压测计划描述"`
        Type                 string                `json:"type" validate:"oneof=MANUAL SCHEDULE" zh:"计划类型"`
        CronExpression       string                `json:"cron_expression" validate:"omitempty,gte=9,lte=128" zh:"定时触发的Cron表达式"`
        Tags                 []string              `json:"tags" validate:"gte=0" zh:"压测计划标签"`
        Protocol             string                `json:"protocol" validate:"oneof=TT TT_AUTH gRPC HTTP" zh:"协议"`
        TargetEnv            string                `json:"target_env" validate:"oneof=PRODUCTION CANARY STAGING TESTING DEVELOPMENT" zh:"目标环境"`
        ProtobufConfigs      []string              `json:"protobuf_configs" validate:"omitempty,gte=0,dive,gte=1,lte=64" zh:"Protobuf配置ID列表"`
        GeneralConfigId      string                `json:"general_config_id" validate:"omitempty,gte=1,lte=64" zh:"通用配置ID"`
        AccountConfigId      string                `json:"account_config_id" validate:"omitempty,gte=1,lte=64" zh:"池账号配置ID"`
        AuthRateLimits       []*RateLimitV2        `json:"auth_rate_limits" zh:"TT登录压测场景专用的登录接口限流配置"`
        CustomDuration       bool                  `json:"custom_duration" zh:"是否自定义压测持续时长"`
        Duration             uint32                `json:"duration" validate:"omitempty,gte=0,lte=3600" zh:"压测持续时长"`
        CreateLarkChat       bool                  `json:"create_lark_chat" zh:"是否需要自动拉群"`
        LarkChats            []*LarkChat           `json:"lark_chats" validate:"omitempty,gte=0" zh:"飞书通知群组列表"`
        AdvancedNotification bool                  `json:"advanced_notification,omitempty,optional" zh:"是否需要提前通知"`
        Rules                []string              `json:"rules" validate:"omitempty,gte=0" zh:"压测停止规则列表"`
        Cases                []*PerfPlanCaseV2Item `json:"cases" validate:"gte=1,lte=20" zh:"压测用例列表"`
        MaintainedBy         string                `json:"maintained_by" validate:"lte=64" zh:"压测计划维护者"`
    }
    CreatePerfPlanV2Resp {
        PlanId string `json:"plan_id"`
    }
)

// 删除压测计划
type (
    RemovePerfPlanV2Req {
        ProjectId string  `json:"project_id" validate:"required" zh:"项目ID"`
        PlanIds  []string `json:"plan_ids" validate:"gt=0" zh:"压测计划ID列表"`
    }
    RemovePerfPlanV2Resp {}
)

// 编辑压测计划
type (
    ModifyPerfPlanV2Req {
	ProjectId            string                `json:"project_id" validate:"required" zh:"项目ID"`
	PlanId               string                `json:"plan_id" validate:"required" zh:"压测计划ID"`
	CategoryId           string                `json:"category_id" validate:"required" zh:"分类ID"`
	Name                 string                `json:"name" validate:"gte=1,lte=64" zh:"压测计划名称"`
	Description          string                `json:"description" validate:"lte=255" zh:"压测计划描述"`
	Type                 string                `json:"type" validate:"oneof=MANUAL SCHEDULE" zh:"计划类型"`
	CronExpression       string                `json:"cron_expression" validate:"omitempty,gte=9,lte=128" zh:"定时触发的Cron表达式"`
	Tags                 []string              `json:"tags" validate:"gte=0" zh:"压测计划标签"`
	Protocol             string                `json:"protocol" validate:"oneof=TT TT_AUTH gRPC HTTP" zh:"协议"`
	TargetEnv            string                `json:"target_env" validate:"oneof=PRODUCTION CANARY STAGING TESTING DEVELOPMENT" zh:"目标环境"`
	ProtobufConfigs      []string              `json:"protobuf_configs" validate:"omitempty,gte=0,dive,gte=1,lte=64" zh:"Protobuf配置ID列表"`
	GeneralConfigId      string                `json:"general_config_id" validate:"omitempty,gte=1,lte=64" zh:"通用配置ID"`
	AccountConfigId      string                `json:"account_config_id" validate:"omitempty,gte=1,lte=64" zh:"池账号配置ID"`
	AuthRateLimits       []*RateLimitV2        `json:"auth_rate_limits" zh:"TT登录压测场景专用的登录接口限流配置"`
	CustomDuration       bool                  `json:"custom_duration" zh:"是否自定义压测持续时长"`
	Duration             uint32                `json:"duration" validate:"omitempty,gte=0,lte=3600" zh:"压测持续时长"`
	CreateLarkChat       bool                  `json:"create_lark_chat" zh:"是否需要自动拉群"`
	LarkChats            []*LarkChat           `json:"lark_chats" validate:"omitempty,gte=0" zh:"飞书通知群组列表"`
	AdvancedNotification bool                  `json:"advanced_notification,omitempty,optional" zh:"是否需要提前通知"`
	Rules                []string              `json:"rules" validate:"omitempty,gte=0" zh:"压测停止规则列表"`
	Cases                []*PerfPlanCaseV2Item `json:"cases" validate:"gte=1,lte=20" zh:"压测用例列表"`
	State                int8                  `json:"state" validate:"oneof=1 2" zh:"压测计划状态"`
	MaintainedBy         string                `json:"maintained_by" validate:"lte=64" zh:"压测计划维护者"`
    }
    ModifyPerfPlanV2Resp {}
)

// 查询压测计划
type (
    SearchPerfPlanV2Req {
        ProjectId  string       `json:"project_id" validate:"required" zh:"项目ID"`
        CategoryId string       `json:"category_id,omitempty,optional" zh:"分类ID"`
        Condition  *Condition   `json:"condition,omitempty,optional" zh:"查询条件"`
        Pagination *Pagination  `json:"pagination,omitempty,optional" zh:"查询分页"`
        Sort       []*SortField `json:"sort,omitempty,optional" zh:"查询排序"`
    }
    SearchPerfPlanV2Resp {
        CurrentPage uint64                  `json:"current_page"`
        PageSize    uint64                  `json:"page_size"`
        TotalCount  uint64                  `json:"total_count"`
        TotalPage   uint64                  `json:"total_page"`
        Items       []*SearchPerfPlanV2Item `json:"items"`
    }
)

// 查看压测计划
type (
    ViewPerfPlanV2Req {
        ProjectId string `form:"project_id" validate:"required" zh:"项目ID"`
        PlanId    string `form:"plan_id" validate:"required" zh:"压测计划ID"`
    }
    ViewPerfPlanV2Resp {
        *PerfPlanV2
    }
)

// 搜索压测计划中的压测用例
type (
    SearchCaseInPerfPlanV2Req {
        ProjectId  string       `json:"project_id" validate:"required" zh:"项目ID"`
        PlanId     string       `json:"plan_id" validate:"required" zh:"压测计划ID"`
        Condition  *Condition   `json:"condition,omitempty,optional" zh:"查询条件"`
        Pagination *Pagination  `json:"pagination,omitempty,optional" zh:"查询分页"`
        Sort       []*SortField `json:"sort,omitempty,optional" zh:"查询排序"`
    }
    SearchCaseInPerfPlanV2Resp {
        CurrentPage uint64                        `json:"current_page"`
        PageSize    uint64                        `json:"page_size"`
        TotalCount  uint64                        `json:"total_count"`
        TotalPage   uint64                        `json:"total_page"`
        Items       []*SearchCaseInPerfPlanV2Item `json:"items"`
    }
)

// 搜索压测计划中的Protobuf配置
type (
    SearchProtobufInPerfPlanV2Req {
        ProjectId  string       `json:"project_id" validate:"required" zh:"项目ID"`
        PlanId     string       `json:"plan_id" validate:"required" zh:"压测计划ID"`
        Condition  *Condition   `json:"condition,omitempty,optional" zh:"查询条件"`
        Pagination *Pagination  `json:"pagination,omitempty,optional" zh:"查询分页"`
        Sort       []*SortField `json:"sort,omitempty,optional" zh:"查询排序"`
    }
    SearchProtobufInPerfPlanV2Resp {
        CurrentPage uint64                            `json:"current_page"`
        PageSize    uint64                            `json:"page_size"`
        TotalCount  uint64                            `json:"total_count"`
        TotalPage   uint64                            `json:"total_page"`
        Items       []*SearchProtobufInPerfPlanV2Item `json:"items"`
    }
)

// 搜索压测计划中的停止规则
type (
    SearchRuleInPerfPlanV2Req {
        ProjectId  string       `json:"project_id" validate:"required" zh:"项目ID"`
        PlanId     string       `json:"plan_id" validate:"required" zh:"压测计划ID"`
        Condition  *Condition   `json:"condition,omitempty,optional" zh:"查询条件"`
        Pagination *Pagination  `json:"pagination,omitempty,optional" zh:"查询分页"`
        Sort       []*SortField `json:"sort,omitempty,optional" zh:"查询排序"`
    }
    SearchRuleInPerfPlanV2Resp {
        CurrentPage uint64                        `json:"current_page"`
        PageSize    uint64                        `json:"page_size"`
        TotalCount  uint64                        `json:"total_count"`
        TotalPage   uint64                        `json:"total_page"`
        Items       []*SearchRuleInPerfPlanV2Item `json:"items"`
    }
)
