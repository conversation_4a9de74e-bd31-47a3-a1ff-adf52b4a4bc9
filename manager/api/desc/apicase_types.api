syntax = "v1"

import "types.api"

type ApiCase {
    ProjectId     string         `json:"project_id"`
    CategoryId    string         `json:"category_id"`
    CaseId        string         `json:"case_id"`
    Name          string         `json:"name"`
    Description   string         `json:"description"`
    Priority      int64          `json:"priority"`
    Tags          []string       `json:"tags"`
    State         string         `json:"state"`
    AccountConfig *AccountConfig `json:"account_config"`
    Version       string         `json:"version"`
    MaintainedBy  *FullUserInfo  `json:"maintained_by"`
    CreatedBy     *FullUserInfo  `json:"created_by"`
    UpdatedBy     *FullUserInfo  `json:"updated_by"`
    CreatedAt     int64          `json:"created_at"`
    UpdatedAt     int64          `json:"updated_at"`
    Nodes         []*Node        `json:"nodes"`
    Edges         []*Edge        `json:"edges"`
    Combos        []*Combo       `json:"combos"`
}

type SearchApiCaseReferenceItem {
    ProjectId     string        `json:"project_id"`     // 项目ID
    CaseId        string        `json:"case_id"`        // 用例ID
    ReferenceType string        `json:"reference_type"` // 引用对象类型
    ReferenceId   string        `json:"reference_id"`   // 引用对象ID
    Name          string        `json:"name"`           // 引用对象名称
    Description   string        `json:"description"`    // 引用对象描述
    Priority      int64         `json:"priority"`       // 优先级
    Tags          []string      `json:"tags"`           // 标签
    State         int8          `json:"state"`          // 引用对象状态
    MaintainedBy  *FullUserInfo `json:"maintained_by"`  // 维护者
    CreatedBy     *FullUserInfo `json:"created_by"`     // 创建者
    UpdatedBy     *FullUserInfo `json:"updated_by"`     // 更新者
    CreatedAt     int64         `json:"created_at"`     // 创建时间
    UpdatedAt     int64         `json:"updated_at"`     // 更新时间
}

// 创建API用例
type (
    CreateApiCaseReq {
        ProjectId     string         `json:"project_id" validate:"required" zh:"项目ID"`
        CategoryId    string         `json:"category_id" validate:"required" zh:"分类ID"`
        Name          string         `json:"name" validate:"gte=1,lte=64" zh:"API用例名称"`
        Description   string         `json:"description" validate:"lte=255" zh:"API用例描述"`
        Priority      int64          `json:"priority" validate:"gte=0" zh:"优先级"`
        Tags          []string       `json:"tags" validate:"gte=0" zh:"API用例标签"`
        AccountConfig *AccountConfig `json:"account_config" zh:"API用例池账号配置数"`
        Nodes         []*Node        `json:"nodes" validate:"gt=2" zh:"API用例画布中的节点列表"`
        Edges         []*Edge        `json:"edges" validate:"gt=1" zh:"API用例画布中的线段列表"`
        Combos        []*Combo       `json:"combos" validate:"gte=0" zh:"API用例画布中的组合列表"`
        Relations     []*Relation    `json:"relations" validate:"gte=2" zh:"API用例画布中的节点与组合的关系列表"`
        MaintainedBy  string         `json:"maintained_by" validate:"lte=64" zh:"API用例维护者"`
    }
    CreateApiCaseResp {
        CaseId string `json:"case_id"`
    }
)

// 删除API用例
type (
    RemoveApiCaseReq {
        ProjectId string   `json:"project_id" validate:"required" zh:"项目ID"`
        CaseIds   []string `json:"case_ids" validate:"gt=0" zh:"API用例ID列表"`
    }
    RemoveApiCaseResp {}
)

// 编辑API用例
type (
    ModifyApiCaseReq {
        ProjectId     string         `json:"project_id" validate:"required" zh:"项目ID"`
        CategoryId    string         `json:"category_id" validate:"required" zh:"分类ID"`
        CaseId        string         `json:"case_id" validate:"required" zh:"API用例ID"`
        Name          string         `json:"name" validate:"gte=1,lte=64" zh:"API用例名称"`
        Description   string         `json:"description" validate:"lte=255" zh:"API用例描述"`
        Priority      int64          `json:"priority" validate:"gte=0" zh:"优先级"`
        Tags          []string       `json:"tags" validate:"gte=0" zh:"API用例标签"`
        //State         int8           `json:"state" validate:"oneof=1 2" zh:"API用例状态"`
        AccountConfig *AccountConfig `json:"account_config" zh:"API用例池账号配置数"`
        Nodes         []*Node        `json:"nodes" validate:"gt=2" zh:"API用例画布中的节点列表"`
        Edges         []*Edge        `json:"edges" validate:"gt=1" zh:"API用例画布中的线段列表"`
        Combos        []*Combo       `json:"combos" validate:"gte=0" zh:"API用例画布中的组合列表"`
        Relations     []*Relation    `json:"relations" validate:"gte=2" zh:"API用例画布中的节点与组合的关系列表"`
        MaintainedBy  string         `json:"maintained_by" validate:"lte=64" zh:"API用例维护者"`
    }
    ModifyApiCaseResp {}
)

// 查询API用例
type (
    SearchApiCaseReq {
        ProjectId  string       `json:"project_id" validate:"required" zh:"项目ID"`
        CategoryId string       `json:"category_id" validate:"required" zh:"分类ID"`
        Condition  *Condition   `json:"condition,omitempty,optional" zh:"查询条件"`
        Pagination *Pagination  `json:"pagination,omitempty,optional" zh:"查询分页"`
        Sort       []*SortField `json:"sort,omitempty,optional" zh:"查询排序"`
    }
    SearchApiCaseResp {
        CurrentPage uint64     `json:"current_page"`
        PageSize    uint64     `json:"page_size"`
        TotalCount  uint64     `json:"total_count"`
        TotalPage   uint64     `json:"total_page"`
        Items       []*ApiCase `json:"items"`
    }
)

// 查看API用例
type (
    ViewApiCaseReq {
        ProjectId string `form:"project_id" validate:"required" zh:"项目ID"`
        CaseId    string `form:"case_id" validate:"required" zh:"API用例ID"`
        Version   string `form:"version,omitempty,optional" zh:"API用例版本"`
    }
    ViewApiCaseResp {
        *ApiCase
    }
)

// 搜索API用例引用详情
type (
    SearchApiCaseReferenceReq {
        ProjectId  string       `json:"project_id" validate:"required" zh:"项目ID"`
        CaseId     string       `json:"case_id" validate:"required" zh:"用例ID"`
        Condition  *Condition   `json:"condition,omitempty,optional" zh:"查询条件"`
        Pagination *Pagination  `json:"pagination,omitempty,optional" zh:"查询分页"`
        Sort       []*SortField `json:"sort,omitempty,optional" zh:"查询排序"`
    }
    SearchApiCaseReferenceResp {
        CurrentPage uint64                        `json:"current_page"`
        PageSize    uint64                        `json:"page_size"`
        TotalCount  uint64                        `json:"total_count"`
        TotalPage   uint64                        `json:"total_page"`
        Items       []*SearchApiCaseReferenceItem `json:"items"`
    }
)

// 维护API用例
type (
    MaintainApiCaseReq {
        ProjectId    string `json:"project_id" validate:"required" zh:"项目ID"`
        CaseId       string `json:"case_id" validate:"required" zh:"用例ID"`
        MaintainedBy string `json:"maintained_by" validate:"lte=64" zh:"API用例维护者"`
    }
    MaintainApiCaseResp {}
)

// 发布API用例
type (
    PublishApiCaseReq {
        ProjectId string `json:"project_id" validate:"required" zh:"项目ID"`
        CaseId    string `json:"case_id" validate:"required" zh:"用例ID"`
    }
    PublishApiCaseResp {}
)
