syntax = "v1"

import "general_config_types.api"

@server (
    prefix: manager/v1
    group: generalconfig
)
service manager {
    @doc "create general config"
    @handler createGeneralConfig
    post /general_config/create (CreateGeneralConfigReq) returns (CreateGeneralConfigResp)

    @doc "delete general config"
    @handler removeGeneralConfig
    put /general_config/remove (RemoveGeneralConfigReq) returns (RemoveGeneralConfigResp)

    @doc "modify general config"
    @handler modifyGeneralConfig
    put /general_config/modify (ModifyGeneralConfigReq) returns (ModifyGeneralConfigResp)

    @doc "search general configs"
    @handler searchGeneralConfig
    post /general_config/search (SearchGeneralConfigReq) returns (SearchGeneralConfigResp)

    @doc "view general config"
    @handler viewGeneralConfig
    get /general_config/view (ViewGeneralConfigReq) returns (ViewGeneralConfigResp)
}
