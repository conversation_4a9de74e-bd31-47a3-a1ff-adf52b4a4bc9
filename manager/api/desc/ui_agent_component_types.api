syntax = "v1"

import "types.api"

type UIAgentComponent {
    ProjectId   string `json:"project_id"`
    CategoryId  string `json:"category_id"`
    ComponentId string `json:"component_id"`

    Name          string                       `json:"name"`
    Description   string                       `json:"description"`
    State         int8                         `json:"state"`
    Tags          []string                     `json:"tags"`
    PlatformType  int64                        `json:"platform_type"`
    ApplicationId string                       `json:"application_id"`
    StepByStep    bool                         `json:"step_by_step"`
    Steps         []*UIAgentComponentStep      `json:"steps"`
    Expectation   *UIAgentComponentExpectation `json:"expectation"`
    Variables     []*KeyValuePair              `json:"variables"`

    LatestExecutedAt int64 `json:"latest_executed_at"`
    LatestResult     int64 `json:"latest_result"`

    MaintainedBy *FullUserInfo `json:"maintained_by"`
    CreatedBy    *FullUserInfo `json:"created_by"`
    UpdatedBy    *FullUserInfo `json:"updated_by"`
    CreatedAt    int64         `json:"created_at"`
    UpdatedAt    int64         `json:"updated_at"`
}

type UIAgentComponentStep {
    Content     string                       `json:"content" validate:"required"`
    Expectation *UIAgentComponentExpectation `json:"expectation,optional"`
    WaitingTime float32                      `json:"waiting_time,default=2.5" validate:"gte=0,lte=300"`
}

type UIAgentComponentExpectation {
    Text  string `json:"text,optional"`
    Image string `json:"image,optional"`
}

type (
    CreateUIAgentComponentReq {
        ProjectId     string   `json:"project_id" validate:"required" zh:"项目ID"`
        CategoryId    string   `json:"category_id" validate:"required" zh:"分类ID"`
        Name          string   `json:"name" validate:"gte=1,lte=64" zh:"组件名称"`
        Description   string   `json:"description" validate:"lte=255" zh:"组件描述"`
        Tags          []string `json:"tags" validate:"gte=0" zh:"组件标签"`
        ApplicationId string   `json:"application_id" validate:"required" zh:"应用配置ID"`
        StepByStep    bool     `json:"step_by_step" validate:"boolean" zh:"是否分步执行"`
        Steps         []*UIAgentComponentStep      `json:"steps" validate:"gte=1,dive,required" zh:"步骤列表"`
        Expectation   *UIAgentComponentExpectation `json:"expectation" zh:"期望结果"`
        Variables     []*KeyValuePair              `json:"variables" validate:"gte=0" zh:"变量列表"`
        MaintainedBy  string                       `json:"maintained_by" validate:"lte=64" zh:"组件维护者"`
    }
    CreateUIAgentComponentResp {
        ComponentId string `json:"component_id"`
    }
)

type (
    RemoveUIAgentComponentReq {
        ProjectId     string   `json:"project_id" validate:"required" zh:"项目ID"`
        ComponentIds  []string `json:"component_ids" validate:"gt=0" zh:"组件ID列表"`
    }
    RemoveUIAgentComponentResp {}
)

type (
    ModifyUIAgentComponentReq {
        ProjectId     string   `json:"project_id" validate:"required" zh:"项目ID"`
        CategoryId    string   `json:"category_id" validate:"required" zh:"分类ID"`
        ComponentId   string   `json:"component_id" validate:"required" zh:"组件ID"`
        Name          string   `json:"name" validate:"gte=1,lte=64" zh:"组件名称"`
        Description   string   `json:"description" validate:"lte=255" zh:"组件描述"`
        Tags          []string `json:"tags" validate:"gte=0" zh:"组件标签"`
        ApplicationId string   `json:"application_id" validate:"required" zh:"应用配置ID"`
        StepByStep    bool     `json:"step_by_step" validate:"boolean" zh:"是否分步执行"`
        Steps         []*UIAgentComponentStep      `json:"steps" validate:"gte=1,dive,required" zh:"步骤列表"`
        Expectation   *UIAgentComponentExpectation `json:"expectation" zh:"期望结果"`
        Variables     []*KeyValuePair              `json:"variables" validate:"gte=0" zh:"变量列表"`
        State         int8                         `json:"state" validate:"oneof=1 2" zh:"组件状态"`
        MaintainedBy  string                       `json:"maintained_by" validate:"lte=64" zh:"组件维护者"`
    }
    ModifyUIAgentComponentResp {}
)

type (
    SearchUIAgentComponentReq {
        ProjectId  string       `json:"project_id" validate:"required" zh:"项目ID"`
        CategoryId string       `json:"category_id" validate:"required" zh:"分类ID"`
        Condition  *Condition   `json:"condition,omitempty,optional" zh:"查询条件"`
        Pagination *Pagination  `json:"pagination,omitempty,optional" zh:"查询分页"`
        Sort       []*SortField `json:"sort,omitempty,optional" zh:"查询排序"`
    }
    SearchUIAgentComponentResp {
        CurrentPage uint64              `json:"current_page"`
        PageSize    uint64              `json:"page_size"`
        TotalCount  uint64              `json:"total_count"`
        TotalPage   uint64              `json:"total_page"`
        Items       []*UIAgentComponent `json:"items"`
    }
)

type (
    ViewUIAgentComponentReq {
        ProjectId   string `form:"project_id" validate:"required" zh:"项目ID"`
        ComponentId string `form:"component_id" validate:"required" zh:"组件ID"`
    }
    ViewUIAgentComponentResp {
        *UIAgentComponent
    }
)
