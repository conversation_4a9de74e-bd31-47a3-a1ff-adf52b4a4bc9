syntax = "v1"

import "types.api"

type Statistic {
    Total     int64 `json:"total"`
    Increased int64 `json:"increased"`
    Modified  int64 `json:"modified"`
    Unchanged int64 `json:"unchanged"`
    Failure   int64 `json:"failure"`
}

type EnumValDesc {
    Value       interface{} `json:"value"`
    Description string      `json:"description"`
}

type RefSchema {
    SchemaId    string `json:"schemaId"`
    FullName    string `json:"fullName"`
    DisplayName string `json:"displayName"`
}

type TextDescExample {
    Description string `json:"description"`
    Example     string `json:"example"`
}

type SchemaData {
    ProjectId   string        `json:"project_id"`
    CategoryId  string        `json:"category_id"`
    SchemaId    string        `json:"schema_id"`
    FullName    string        `json:"full_name"`
    Name        string        `json:"name"`
    Description string        `json:"description"`
    Mode        string        `json:"mode"`
    ImportType  string        `json:"import_type"`
    Data        *Schema       `json:"data"`
    CreatedBy   *FullUserInfo `json:"created_by"`
    UpdatedBy   *FullUserInfo `json:"updated_by"`
    CreatedAt   int64         `json:"created_at"`
    UpdatedAt   int64         `json:"updated_at"`
}

type Schema {
    Title            string            `json:"title,omitempty,optional" zh:"字段名称"`
    Type             string            `json:"type" validate:"required,oneof=string integer number boolean array object null any allOf anyOf oneOf custom schema file" zh:"字段类型"`
    Description      string            `json:"description,omitempty,optional" zh:"字段描述"`
    Index            int32             `json:"index,omitempty,optional" zh:"字段序号"`
    FieldRequired    bool              `json:"fieldRequired,omitempty,optional" zh:"字段是否必需"`
    Deprecated       bool              `json:"deprecated,omitempty,optional" zh:"字段是否不建议使用"`
    Default          interface{}       `json:"default,omitempty,optional" zh:"字段默认值"`
    Example          interface{}       `json:"example,omitempty,optional" zh:"字段样例"`
    Format           string            `json:"format,omitempty,optional" validate:"omitempty,oneof=int32 int64 float double password date-time date time duration email hostname ipv4 ipv6 uri regex" zh:"字段值格式"`
    MultipleOf       int32             `json:"multipleOf,omitempty,optional"`
    Maximum          float64           `json:"maximum,omitempty,optional"`
    ExclusiveMaximum float64           `json:"exclusiveMaximum,omitempty,optional"`
    Minimum          float64           `json:"minimum,omitempty,optional"`
    ExclusiveMinimum float64           `json:"exclusiveMinimum,omitempty,optional"`
    MaxLength        int32             `json:"maxLength,omitempty,optional"`
    MinLength        int32             `json:"minLength,omitempty,optional"`
    Pattern          string            `json:"pattern,omitempty,optional"`
    MaxItems         int32             `json:"maxItems,omitempty,optional"`
    MinItems         int32             `json:"minItems,omitempty,optional"`
    UniqueItems      bool              `json:"uniqueItems,omitempty,optional"`
    Enum             []interface{}     `json:"enum,omitempty,optional"`
    Enums            []EnumValDesc     `json:"enums,omitempty,optional"`
    Items            *Schema           `json:"items,omitempty,optional"`
    Ref              *RefSchema        `json:"ref,omitempty,optional"`
    Properties       map[string]Schema `json:"properties,omitempty,optional"`
    Required         []string          `json:"required,omitempty,optional"`
    Raw              string            `json:"raw,omitempty,optional"`
}

type DocumentData {
    ProjectId    string        `json:"project_id"`
    CategoryId   string        `json:"category_id"`
    DocumentId   string        `json:"document_id"`
    Name         string        `json:"name"`
    Description  string        `json:"description"`
    Type         string        `json:"type"`
    Mode         string        `json:"mode"`
    ImportType   string        `json:"import_type"`
    Status       int64         `json:"status,omitempty"`
    Priority     int64         `json:"priority"`
    Tags         []string      `json:"tags"`
    Service      string        `json:"service"`
    Path         string        `json:"path"`
    Method       string        `json:"method"`
    Data         *Document     `json:"data"`
    MaintainedBy *FullUserInfo `json:"maintained_by"`
    CreatedBy    *FullUserInfo `json:"created_by"`
    UpdatedBy    *FullUserInfo `json:"updated_by"`
    CreatedAt    int64         `json:"created_at"`
    UpdatedAt    int64         `json:"updated_at"`
}

type Document {
    Headers     []*Schema    `json:"headers"`
    PathParams  []*Schema    `json:"path_params"`
    QueryParams []*Schema    `json:"query_params"`
    Body        *BodyData    `json:"body"`
    Responses   map[string]*ResponseData `json:"responses"`
}

type DetailDefinition {
    Headers     map[string]Schema        `json:"headers"`
    PathParams  map[string]Schema        `json:"path_params"`
    QueryParams map[string]Schema        `json:"query_params"`
    Body        *BodyData                `json:"body"`
    Responses   map[string]*ResponseData `json:"responses"`
}

type BodyData {
    Type string            `json:"type"`
    Form map[string]Schema `json:"form"`
    Json map[string]Schema `json:"json"`
    Text *TextDescExample  `json:"text"`
}

type ResponseData {
    StatusCode  string            `json:"status_code"`
    Description string            `json:"description"`
    Headers     map[string]Schema `json:"headers"`
    Body        *BodyData         `json:"body"`
}

type InterfaceCase {
    ProjectId     string         `json:"project_id"`
    DocumentId    string         `json:"document_id"`
    CaseId        string         `json:"case_id"`
    Name          string         `json:"name"`
    Description   string         `json:"description"`
    Priority      int64          `json:"priority"`
    Tags          []string       `json:"tags"`
    State         string         `json:"state"`
    AccountConfig *AccountConfig `json:"account_config"`
    Version       string         `json:"version"`
    MaintainedBy  *FullUserInfo  `json:"maintained_by,omitempty"`
    CreatedBy     *FullUserInfo  `json:"created_by"`
    UpdatedBy     *FullUserInfo  `json:"updated_by"`
    CreatedAt     int64          `json:"created_at"`
    UpdatedAt     int64          `json:"updated_at"`
    Nodes         []*Node        `json:"nodes"`
    Edges         []*Edge        `json:"edges"`
    Combos        []*Combo       `json:"combos"`
}

type SearchInterfaceDocumentReferenceItem {
    ProjectId      string        `json:"project_id"`      // 项目ID
    DocumentId     string        `json:"document_id"`     // 接口ID
    ReferenceType  string        `json:"reference_type"`  // 引用对象类型
    ReferenceId    string        `json:"reference_id"`    // 引用对象ID
    Name           string        `json:"name"`            // 引用对象名称
    Description    string        `json:"description"`     // 引用对象描述
    Priority       int64         `json:"priority"`        // 优先级
    Tags           []string      `json:"tags"`            // 标签
    State          int8          `json:"state"`           // 引用对象状态
    ReferenceState int8          `json:"reference_state"` // 集合的引用状态
    MaintainedBy   *FullUserInfo `json:"maintained_by"`   // 维护者
    CreatedBy      *FullUserInfo `json:"created_by"`      // 创建者
    UpdatedBy      *FullUserInfo `json:"updated_by"`      // 更新者
    CreatedAt      int64         `json:"created_at"`      // 创建时间
    UpdatedAt      int64         `json:"updated_at"`      // 更新时间
}

type SearchInterfaceCaseReferenceItem {
    ProjectId     string        `json:"project_id"`     // 项目ID
    CaseId        string        `json:"case_id"`        // 用例ID
    ReferenceType string        `json:"reference_type"` // 引用对象类型
    ReferenceId   string        `json:"reference_id"`   // 引用对象ID
    Name          string        `json:"name"`           // 引用对象名称
    Description   string        `json:"description"`    // 引用对象描述
    Priority      int64         `json:"priority"`       // 优先级
    Tags          []string      `json:"tags"`           // 标签
    State         int8          `json:"state"`          // 引用对象状态
    MaintainedBy  *FullUserInfo `json:"maintained_by"`  // 维护者
    CreatedBy     *FullUserInfo `json:"created_by"`     // 创建者
    UpdatedBy     *FullUserInfo `json:"updated_by"`     // 更新者
    CreatedAt     int64         `json:"created_at"`     // 创建时间
    UpdatedAt     int64         `json:"updated_at"`     // 更新时间
}

type InterfaceCoverageData {
    NumberOfApis  []uint32 `json:"number_of_apis"`  // 接口数量
    NumberOfCases []uint32 `json:"number_of_cases"` // 用例数量
    CountedAt     []string `json:"counted_at"`      // 统计日期
}


// 导入接口定义
type (
	Target {
		GitUrl string `json:"git_url,omitempty,optional" zh:"git地址"`
		Branch string `json:"branch,default=master" zh:"分支"`
		Path   string `json:"path,omitempty,optional" zh:"本地路径"`
	}
    LocalImportInterfaceDefinitionReq {
        ProjectId    string   `json:"project_id" validate:"required" zh:"项目ID"`
        Type         string   `json:"type" validate:"required,oneof=OpenApi gRPC YApi TT TTMeta" zh:"接口类型"`
		Target       Target   `json:"target" validate:"required" zh:"导入目标"`
		Dependencies []Target `json:"dependencies,omitempty,optional" validate:"gte=0,dive,required" zh:"导入目标关联的依赖"`
    }
    LocalImportInterfaceDefinitionResp {
        Document *Statistic `json:"document"`
        Schema   *Statistic `json:"schema"`
    }
    RemoteImportInterfaceDefinitionReq {}
    RemoteImportInterfaceDefinitionResp {}
)

// 创建接口文档
type (
    CreateInterfaceDocumentReq {
        ProjectId    string        `json:"project_id" validate:"required" zh:"项目ID"`
        CategoryId   string        `json:"category_id" validate:"required" zh:"分类ID"`
        Name         string        `json:"name" validate:"gte=1,lte=64" zh:"接口名称"`
        Description  string        `json:"description" validate:"lte=255" zh:"接口描述"`
        Type         string        `json:"type" validate:"required,oneof=HTTP gRPC" zh:"接口类型"`
        Mode         string        `json:"mode,omitempty,optional,default=manual" validate:"omitempty,oneof=manual plugin" zh:"创建方式"`
        Status       int64         `json:"status" validate:"gte=1" zh:"接口状态"`
        Priority     int64         `json:"priority" validate:"gte=0" zh:"优先级"`
        Tags         []string      `json:"tags" validate:"gte=0" zh:"接口标签"`
        Service      string        `json:"service" validate:"lte=64" zh:"服务名称"`
        Path         string        `json:"path" validate:"gte=1,lte=128" zh:"接口路径"`
        Method       string        `json:"method" validate:"gte=1,lte=64" zh:"接口方法"`
        Data         *Document     `json:"data" validate:"required" zh:"接口详细数据"`
        MaintainedBy string        `json:"maintained_by" validate:"lte=64" zh:"接口维护者"`
    }
    CreateInterfaceDocumentResp {
        DocumentId string `json:"document_id"`
    }
)

// 删除接口文档
type (
    RemoveInterfaceDocumentReq {}
    RemoveInterfaceDocumentResp {}
)

// 编辑接口文档
type (
    ModifyInterfaceDocumentReq {
        ProjectId    string        `json:"project_id" validate:"required" zh:"项目ID"`
        CategoryId   string        `json:"category_id" validate:"required" zh:"分类ID"`
        DocumentId   string        `json:"document_id" validate:"required" zh:"接口文档ID"`
        Name         string        `json:"name" validate:"gte=1,lte=64" zh:"接口名称"`
        Description  string        `json:"description" validate:"lte=255" zh:"接口描述"`
        Status       int64         `json:"status" validate:"gte=1" zh:"接口状态"`
        Priority     int64         `json:"priority" validate:"gte=0" zh:"优先级"`
        Tags         []string      `json:"tags" validate:"gte=0" zh:"接口标签"`
        Service      string        `json:"service" validate:"lte=64" zh:"服务名称"`
        Path         string        `json:"path" validate:"gte=1,lte=128" zh:"接口路径"`
        Method       string        `json:"method" validate:"gte=1,lte=64" zh:"接口方法"`
        Data         *Document     `json:"data" validate:"required" zh:"接口详细数据"`
        MaintainedBy string        `json:"maintained_by" validate:"lte=64" zh:"接口维护者"`
    }
    ModifyInterfaceDocumentResp {}
)

// 查询接口文档
type (
    SearchInterfaceDocumentReq {
        ProjectId  string       `json:"project_id" validate:"required" zh:"项目ID"`
        CategoryId string       `json:"category_id" validate:"required" zh:"分类ID"`
        Condition  *Condition   `json:"condition,omitempty,optional" zh:"查询条件"`
        Pagination *Pagination  `json:"pagination,omitempty,optional" zh:"查询分页"`
        Sort       []*SortField `json:"sort,omitempty,optional" zh:"查询排序"`
    }
    SearchInterfaceDocumentResp {
        CurrentPage uint64          `json:"current_page"`
        PageSize    uint64          `json:"page_size"`
        TotalCount  uint64          `json:"total_count"`
        TotalPage   uint64          `json:"total_page"`
        Items       []*DocumentData `json:"items"`
    }
)

// 查看接口文档
type (
    ViewInterfaceDocumentReq {
        ProjectId  string `form:"project_id" validate:"required" zh:"项目ID"`
        DocumentId string `form:"document_id" validate:"required" zh:"接口定义ID"`
    }
    ViewInterfaceDocumentResp {
        *DocumentData
    }
)

// 根据接口文档生成接口用例数据
type (
    MockInterfaceDocumentReq {
        ProjectId  string `form:"project_id" validate:"required" zh:"项目ID"`
        DocumentId string `form:"document_id" validate:"required" zh:"接口定义ID"`
        WithConfig int8   `form:"with_config" validate:"oneof=0 1 2" zh:"是否使用接口配置"`
        ConfigId   string `form:"config_id,optional" validate:"required_if=WithConfig 2" zh:"接口配置ID"`
    }
    MockInterfaceDocumentResp {}
)

// 搜索API集合引用详情
type (
    SearchInterfaceDocumentReferenceReq {
        ProjectId  string       `json:"project_id" validate:"required" zh:"项目ID"`
        DocumentId string       `json:"document_id" validate:"required" zh:"接口ID"`
        Condition  *Condition   `json:"condition,omitempty,optional" zh:"查询条件"`
        Pagination *Pagination  `json:"pagination,omitempty,optional" zh:"查询分页"`
        Sort       []*SortField `json:"sort,omitempty,optional" zh:"查询排序"`
    }
    SearchInterfaceDocumentReferenceResp {
        CurrentPage uint64                         `json:"current_page"`
        PageSize    uint64                         `json:"page_size"`
        TotalCount  uint64                         `json:"total_count"`
        TotalPage   uint64                         `json:"total_page"`
        Items       []*SearchInterfaceDocumentReferenceItem `json:"items"`
    }
)

// 修改API集合所在的API计划的引用状态
type (
    ModifyInterfaceDocumentReferenceStateReq {
        ProjectId  string   `json:"project_id" validate:"required" zh:"项目ID"`
        DocumentId string   `json:"document_id" validate:"required" zh:"接口集合ID"`
        PlanIds    []string `json:"plan_ids" validate:"gt=0" zh:"API计划ID列表"`
        State      int8     `json:"state" validate:"oneof=1 2" zh:"接口集合引用状态"`
    }
    ModifyInterfaceDocumentReferenceStateResp {}
)

// 创建接口数据模型
type (
    CreateInterfaceSchemaReq {
        ProjectId   string  `json:"project_id" validate:"required" zh:"项目ID"`
        CategoryId  string  `json:"category_id" validate:"required" zh:"分类ID"`
        Name        string  `json:"name" validate:"gte=1,lte=64" zh:"数据模型名称"`
        Description string  `json:"description" validate:"lte=255" zh:"数据模型描述"`
        Data        *Schema `json:"data" validate:"required" zh:"数据模型数据"`
    }
    CreateInterfaceSchemaResp {
        SchemaId string `json:"schema_id"`
    }
)

// 删除接口数据模型
type (
    RemoveInterfaceSchemaReq {}
    RemoveInterfaceSchemaResp {}
)

// 编辑接口数据模型
type (
    ModifyInterfaceSchemaReq {
        ProjectId   string  `json:"project_id" validate:"required" zh:"项目ID"`
        CategoryId  string  `json:"category_id" validate:"required" zh:"分类ID"`
        SchemaId    string  `json:"schema_id" validate:"required" zh:"数据模型ID"`
        Name        string  `json:"name" validate:"gte=1,lte=64" zh:"数据模型名称"`
        Description string  `json:"description" validate:"lte=255" zh:"数据模型描述"`
        Data        *Schema `json:"data" validate:"required" zh:"数据模型数据"`
    }
    ModifyInterfaceSchemaResp {}
)

// 查询接口数据模型
type (
    SearchInterfaceSchemaReq {
        ProjectId  string       `json:"project_id" validate:"required" zh:"项目ID"`
        CategoryId string       `json:"category_id" validate:"required" zh:"分类ID"`
        Condition  *Condition   `json:"condition,omitempty,optional" zh:"查询条件"`
        Pagination *Pagination  `json:"pagination,omitempty,optional" zh:"查询分页"`
        Sort       []*SortField `json:"sort,omitempty,optional" zh:"查询排序"`
    }

    SearchInterfaceSchemaResp {
        CurrentPage uint64          `json:"current_page"`
        PageSize    uint64          `json:"page_size"`
        TotalCount  uint64          `json:"total_count"`
        TotalPage   uint64          `json:"total_page"`
        Items       []*SchemaData   `json:"items"`
    }
)

// 查看接口数据模型
type (
    ViewInterfaceSchemaReq {
        ProjectId string `form:"project_id" validate:"required" zh:"项目ID"`
        SchemaId  string `form:"schema_id" validate:"required" zh:"接口数据模型ID"`
    }
    ViewInterfaceSchemaResp {
        *SchemaData
    }
)

// 创建接口配置
type (
    CreateInterfaceConfigReq {
        ProjectId    string    `json:"project_id" validate:"required" zh:"项目ID"`
        DocumentId   string    `json:"document_id" validate:"required" zh:"接口ID"`
        Name         string    `json:"name" validate:"gte=1,lte=64" zh:"接口配置名称"`
        Description  string    `json:"description" validate:"lte=255" zh:"接口配置描述"`
        Path         string    `json:"path" validate:"gte=1,lte=128" zh:"接口配置请求路径"`
        Method       string    `json:"method" validate:"gte=1,lte=64" zh:"接口配置请求方法"`
        Data         *Document `json:"data" validate:"required" zh:"接口配置数据"`
    }
    CreateInterfaceConfigResp {
        ConfigId string `json:"config_id"`
    }
)

// 删除接口配置
type (
    RemoveInterfaceConfigReq {}
    RemoveInterfaceConfigResp {}
)

// 编辑接口配置
type (
    ModifyInterfaceConfigReq {

    }
    ModifyInterfaceConfigResp {}
)

// 查询接口配置
type (
    SearchInterfaceConfigReq {}
    SearchInterfaceConfigResp {}
)

// 查看接口配置
type (
    ViewInterfaceConfigReq {}
    ViewInterfaceConfigResp {}
)


// 创建接口用例
type (
    CreateInterfaceCaseReq {
        ProjectId     string         `json:"project_id" validate:"required" zh:"项目ID"`
        DocumentId    string         `json:"document_id" validate:"required" zh:"接口ID"`
        Name          string         `json:"name" validate:"gte=1,lte=64" zh:"接口用例名称"`
        Description   string         `json:"description" validate:"lte=255" zh:"接口用例描述"`
        Priority      int64          `json:"priority" validate:"gte=0" zh:"优先级"`
        Tags          []string       `json:"tags" validate:"gte=0" zh:"接口用例标签"`
        AccountConfig *AccountConfig `json:"account_config" zh:"接口用例池账号配置数"`
        Nodes         []*Node        `json:"nodes" validate:"gt=2" zh:"接口用例画布中的节点列表"`
        Edges         []*Edge        `json:"edges" validate:"gt=1" zh:"接口用例画布中的线段列表"`
        Combos        []*Combo       `json:"combos" validate:"gte=0" zh:"接口用例画布中的组合列表"`
        Relations     []*Relation    `json:"relations" validate:"gte=2" zh:"接口用例画布中的节点与组合的关系列表"`
        MaintainedBy  string         `json:"maintained_by" validate:"lte=64" zh:"接口用例维护者"`
    }
    CreateInterfaceCaseResp {
        CaseId string `json:"case_id"`
    }
)

// 删除接口用例
type (
    RemoveInterfaceCaseReq {
        ProjectId  string   `json:"project_id" validate:"required" zh:"项目ID"`
        DocumentId string   `json:"document_id" validate:"required" zh:"接口ID"`
        CaseIds    []string `json:"case_ids" validate:"gt=0" zh:"接口用例ID列表"`
    }
    RemoveInterfaceCaseResp {}
)

// 编辑接口用例
type (
    ModifyInterfaceCaseReq {
        ProjectId     string         `json:"project_id" validate:"required" zh:"项目ID"`
        DocumentId    string         `json:"document_id" validate:"required" zh:"接口ID"`
        CaseId        string         `json:"case_id" validate:"required" zh:"接口用例ID"`
        Name          string         `json:"name" validate:"gte=1,lte=64" zh:"接口用例名称"`
        Description   string         `json:"description" validate:"lte=255" zh:"接口用例描述"`
        Priority      int64          `json:"priority" validate:"gte=0" zh:"优先级"`
        Tags          []string       `json:"tags" validate:"gte=0" zh:"接口用例标签"`
        AccountConfig *AccountConfig `json:"account_config" zh:"接口用例池账号配置数"`
        Nodes         []*Node        `json:"nodes" validate:"gt=2" zh:"接口用例画布中的节点列表"`
        Edges         []*Edge        `json:"edges" validate:"gt=1" zh:"接口用例画布中的线段列表"`
        Combos        []*Combo       `json:"combos" validate:"gte=0" zh:"接口用例画布中的组合列表"`
        Relations     []*Relation    `json:"relations" validate:"gte=2" zh:"接口用例画布中的节点与组合的关系列表"`
        MaintainedBy  string         `json:"maintained_by" validate:"lte=64" zh:"接口用例维护者"`
    }
    ModifyInterfaceCaseResp {}
)

// 查询接口用例
type (
    SearchInterfaceCaseReq {
        ProjectId  string       `json:"project_id" validate:"required" zh:"项目ID"`
        DocumentId string       `json:"document_id" validate:"required" zh:"接口ID"`
        Condition  *Condition   `json:"condition,omitempty,optional" zh:"查询条件"`
        Pagination *Pagination  `json:"pagination,omitempty,optional" zh:"查询分页"`
        Sort       []*SortField `json:"sort,omitempty,optional" zh:"查询排序"`
    }
    SearchInterfaceCaseResp {
        CurrentPage uint64           `json:"current_page"`
        PageSize    uint64           `json:"page_size"`
        TotalCount  uint64           `json:"total_count"`
        TotalPage   uint64           `json:"total_page"`
        Items       []*InterfaceCase `json:"items"`
    }
)

// 查看接口用例
type (
    ViewInterfaceCaseReq {
        ProjectId  string `form:"project_id" validate:"required" zh:"项目ID"`
        DocumentId string `form:"document_id,omitempty,optional" zh:"接口ID"`
        CaseId     string `form:"case_id" validate:"required" zh:"接口用例ID"`
        Version    string `form:"version,omitempty,optional" zh:"接口用例版本"`
    }
    ViewInterfaceCaseResp {
        *InterfaceCase
    }
)

// 维护接口用例
type (
    MaintainInterfaceCaseReq {
        ProjectId    string `json:"project_id" validate:"required" zh:"项目ID"`
        DocumentId   string `json:"document_id" validate:"required" zh:"接口ID"`
        CaseId       string `json:"case_id" validate:"required" zh:"接口用例ID"`
        MaintainedBy string `json:"maintained_by" validate:"lte=64" zh:"接口用例维护者"`
    }
    MaintainInterfaceCaseResp {}
)

// 发布接口用例
type (
    PublishInterfaceCaseReq {
        ProjectId  string `json:"project_id" validate:"required" zh:"项目ID"`
        DocumentId string `json:"document_id" validate:"required" zh:"接口ID"`
        CaseId     string `json:"case_id" validate:"required" zh:"接口用例ID"`
    }
    PublishInterfaceCaseResp {}
)

// 搜索接口用例引用详情
type (
    SearchInterfaceCaseReferenceReq {
        ProjectId  string       `json:"project_id" validate:"required" zh:"项目ID"`
        CaseId     string       `json:"case_id" validate:"required" zh:"接口用例ID"`
        Condition  *Condition   `json:"condition,omitempty,optional" zh:"查询条件"`
        Pagination *Pagination  `json:"pagination,omitempty,optional" zh:"查询分页"`
        Sort       []*SortField `json:"sort,omitempty,optional" zh:"查询排序"`
    }
    SearchInterfaceCaseReferenceResp {
        CurrentPage uint64                              `json:"current_page"`
        PageSize    uint64                              `json:"page_size"`
        TotalCount  uint64                              `json:"total_count"`
        TotalPage   uint64                              `json:"total_page"`
        Items       []*SearchInterfaceCaseReferenceItem `json:"items"`
    }
)

// 获取接口覆盖率相关的团队
type (
    GetInterfaceCoverageTeamsReq {
        ProjectId string `form:"project_id" validate:"required" zh:"项目ID"`
    }
    GetInterfaceCoverageTeamsResp {
        Teams []string `json:"teams"`
    }
)

// 获取接口覆盖率数据
type (
    GetInterfaceCoverageDataReq {
        ProjectId string `form:"project_id" validate:"required" zh:"项目ID"`
        Team      string `form:"team" validate:"required" zh:"团队名称"`
        From      string `form:"from" validate:"required,datetime=2006-01-02" zh:"开始日期"`
        To        string `form:"to" validate:"required,datetime=2006-01-02" zh:"结束日期"`
    }
    GetInterfaceCoverageDataResp {
        *InterfaceCoverageData
    }
)
