syntax = "v1"

import "tag_types.api"

@server (
    prefix: manager/v1
    group: tag
)
service manager {
    @doc "create tag"
    @handler createTag
    post /tag/create (CreateTagReq) returns (CreateTagResp)

    @doc "delete tag"
    @handler removeTag
    put /tag/remove (RemoveTagReq) returns (RemoveTagResp)

    @doc "modify tag"
    @handler modifyTag
    put /tag/modify (ModifyTagReq) returns (ModifyTagResp)

    @doc "search tag"
    @handler searchTag
    post /tag/search (SearchTagReq) returns (SearchTagResp)

    @doc "view tag"
    @handler viewTag
    get /tag/view (ViewTagReq) returns (ViewTagResp)
}
