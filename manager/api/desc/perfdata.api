syntax = "v1"

import "perfdata_types.api"

@server (
    prefix: manager/v1
    group: perfData
)
service manager {
    @handler uploadPerfData
    post /perf_data/upload (UploadPerfDataReq) returns (UploadPerfDataResp)

    @handler downloadPerfData
    get /perf_data/download (DownloadPerfDataReq)

    @handler removePerfData
    put /perf_data/remove (RemovePerfDataReq) returns (RemovePerfDataResp)

    @handler searchPerfData
    post /perf_data/search (SearchPerfDataReq) returns (SearchPerfDataResp)
}
