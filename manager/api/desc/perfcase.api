syntax = "v1"

import "perfcase_types.api"

@server (
    prefix: manager/v1
    group: perfCase
)
service manager {
    @handler uploadPerfCase
    post /perf_case/upload (UploadPerfCaseReq) returns (UploadPerfCaseResp)

    @handler downloadPerfCase
    get /perf_case/download (DownloadPerfCaseReq)

    @handler removePerfCase
    put /perf_case/remove (RemovePerfCaseReq) returns (RemovePerfCaseResp)

    @handler modifyPerfCase
    put /perf_case/modify (ModifyPerfCaseReq) returns (ModifyPerfCaseResp)

    @handler viewPerfCase
    get /perf_case/view (ViewPerfCaseReq) returns (ViewPerfCaseResp)

    @handler calculatePerfCase
    get /perf_case/calculate (CalculatePerfCaseReq) returns (CalculatePerfCaseResp)
}
