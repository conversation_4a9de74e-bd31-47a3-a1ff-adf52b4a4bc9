syntax = "v1"

import "review_types.api"

@server (
    prefix: manager/v1
    group: review
)
service manager {
    @doc "create review record"
    @handler createReviewRecord
    post /review/apply (CreateReviewRecordReq) returns (CreateReviewRecordResp)

    @doc "modify review record"
    @handler modifyReviewRecord
    put /review/modify (ModifyReviewRecordReq) returns (ModifyReviewRecordResp)

    @doc "revoke review record"
    @handler revokeReviewRecord
    put /review/revoke (RevokeReviewRecordReq) returns (RevokeReviewRecordResp)

    @doc "approve review record"
    @handler approveReviewRecord
    post /review/approve (ApproveReviewRecordReq) returns (ApproveReviewRecordResp)

    @doc "search review record"
    @handler searchReviewRecord
    post /review/search (SearchReviewRecordReq) returns (SearchReviewRecordResp)
}
