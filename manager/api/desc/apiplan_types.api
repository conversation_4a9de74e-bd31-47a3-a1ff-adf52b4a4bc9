syntax = "v1"

import "types.api"

type ApiPlan {
    ProjectId          string        `json:"project_id"`           // 项目ID
    CategoryId         string        `json:"category_id"`          // 分类ID
    PlanId             string        `json:"plan_id"`              // 计划ID
    Name               string        `json:"name"`                 // 计划名称
    Description        string        `json:"description"`          // 计划描述
    Priority           int64         `json:"priority"`             // 优先级
    Tags               []string      `json:"tags"`                 // 标签
    State              int8          `json:"state"`                // 状态
    Type               string        `json:"type"`                 // 计划类型（手动、定时、接口）
    Purpose            string        `json:"purpose"`              // 计划用途（常规、精准测试）
    CronExpression     string        `json:"cron_expression"`      // 定时触发计划的Cron表达式
    GeneralConfigId    string        `json:"general_config_id"`    // 通用配置ID
    AccountConfigIds   []string      `json:"account_config_ids"`   // 池账号配置ID列表
    SuiteExecutionMode int8          `json:"suite_execution_mode"` // 集合执行方式
    CaseExecutionMode  int8          `json:"case_execution_mode"`  // 用例执行方式
    MaintainedBy       *FullUserInfo `json:"maintained_by"`        // 维护者
    CreatedBy          *FullUserInfo `json:"created_by"`           // 创建者
    UpdatedBy          *FullUserInfo `json:"updated_by"`           // 更新者
    CreatedAt          int64         `json:"created_at"`           // 创建时间
    UpdatedAt          int64         `json:"updated_at"`           // 更新时间
}

type SuiteTypeId {
    SuiteType string `json:"suite_type" validate:"oneof=API_SUITE INTERFACE_DOCUMENT" zh:"集合类型"`
    SuiteId   string `json:"suite_id" validate:"required" zh:"集合ID"`
}

type SearchApiPlanItem {
    ProjectId          string        `json:"project_id"`           // 项目ID
    CategoryId         string        `json:"category_id"`          // 分类ID
    PlanId             string        `json:"plan_id"`              // 计划ID
    Name               string        `json:"name"`                 // 计划名称
    Description        string        `json:"description"`          // 集合描述
    Priority           int64         `json:"priority"`             // 优先级
    Tags               []string      `json:"tags"`                 // 标签
    State              int8          `json:"state"`                // 状态
    Type               string        `json:"type"`                 // 计划类型（手动、定时、接口）
    Purpose            string        `json:"purpose"`              // 计划用途（常规、精准测试
    CronExpression     string        `json:"cron_expression"`      // 定时触发计划的Cron表达式
    SuiteExecutionMode int8          `json:"suite_execution_mode"` // 集合执行方式
    CaseExecutionMode  int8          `json:"case_execution_mode"`  // 用例执行方式
    SuiteIncluded      int64         `json:"suite_included"`       // 包含的集合数
    MaintainedBy       *FullUserInfo `json:"maintained_by"`        // 维护者
    CreatedBy          *FullUserInfo `json:"created_by"`           // 创建者
    UpdatedBy          *FullUserInfo `json:"updated_by"`           // 更新者
    CreatedAt          int64         `json:"created_at"`           // 创建时间
    UpdatedAt          int64         `json:"updated_at"`           // 更新时间
}

type SearchSuiteInApiPlanItem {
    ProjectId      string        `json:"project_id"`      // 项目ID
    SuiteType      string        `json:"suite_type"`      // 集合类型
    SuiteId        string        `json:"suite_id"`        // 集合ID
    Name           string        `json:"name"`            // 集合名称
    Description    string        `json:"description"`     // 集合描述
    Priority       int64         `json:"priority"`        // 优先级
    Tags           []string      `json:"tags"`            // 标签
    State          int8          `json:"state"`           // 集合状态
    ReferenceState int8          `json:"reference_state"` // 引用状态
    CaseIncluded   int64         `json:"case_included"`   // 包含的用例数
    CaseSkipped    int64         `json:"case_skipped"`    // 跳过的用例数
    MaintainedBy   *FullUserInfo `json:"maintained_by"`   // 维护者
    CreatedBy      *FullUserInfo `json:"created_by"`      // 创建者
    UpdatedBy      *FullUserInfo `json:"updated_by"`      // 更新者
    CreatedAt      int64         `json:"created_at"`      // 创建时间
    UpdatedAt      int64         `json:"updated_at"`      // 更新时间
}

type SearchSuiteNotInApiPlanItem {
    ProjectId    string        `json:"project_id"`    // 项目ID
    CategoryId   string        `json:"category_id"`   // 分类ID
    SuiteId      string        `json:"suite_id"`      // 集合ID
    Name         string        `json:"name"`          // 集合名称
    Description  string        `json:"description"`   // 集合描述
    Priority     int64         `json:"priority"`      // 优先级
    Tags         []string      `json:"tags"`          // 标签
    State        int8          `json:"state"`         // 集合状态
    MaintainedBy *FullUserInfo `json:"maintained_by"` // 维护者
    CreatedBy    *FullUserInfo `json:"created_by"`    // 创建者
    UpdatedBy    *FullUserInfo `json:"updated_by"`    // 更新者
    CreatedAt    int64         `json:"created_at"`    // 创建时间
    UpdatedAt    int64         `json:"updated_at"`    // 更新时间
}

type SearchCaseInApiPlanItem {
    ProjectId      string        `json:"project_id"`      // 项目ID
    CaseType       string        `json:"case_type"`       // 用例类型
    CaseId         string        `json:"case_id"`         // 用例ID
    Name           string        `json:"name"`            // 用例名称
    Description    string        `json:"description"`     // 用例描述
    Priority       int64         `json:"priority"`        // 优先级
    Tags           []string      `json:"tags"`            // 标签
    State          string        `json:"state"`           // 用例状态
    ReferenceState int8          `json:"reference_state"` // 引用状态
    MaintainedBy   *FullUserInfo `json:"maintained_by"`   // 维护者
    CreatedBy      *FullUserInfo `json:"created_by"`      // 创建者
    UpdatedBy      *FullUserInfo `json:"updated_by"`      // 更新者
    CreatedAt      int64         `json:"created_at"`      // 创建时间
    UpdatedAt      int64         `json:"updated_at"`      // 更新时间
}


// 创建API计划
type (
    CreateApiPlanReq {
        ProjectId          string         `json:"project_id" validate:"required" zh:"项目ID"`
        CategoryId         string         `json:"category_id" validate:"required" zh:"分类ID"`
        Name               string         `json:"name" validate:"gte=1,lte=64" zh:"API计划名称"`
        Description        string         `json:"description" validate:"lte=255" zh:"API计划描述"`
        Priority           int64          `json:"priority" validate:"gte=0" zh:"优先级"`
        Tags               []string       `json:"tags" validate:"gte=0" zh:"API计划标签"`
        Type               string         `json:"type" validate:"oneof=MANUAL SCHEDULE INTERFACE" zh:"计划类型"`
        Purpose            string         `json:"purpose,default=NORMAL" validate:"oneof=NORMAL PRECISION_TESTING" zh:"计划用途"`
        CronExpression     string         `json:"cron_expression" validate:"omitempty,gte=9,lte=128" zh:"定时触发的Cron表达式"`
        GeneralConfigId    string         `json:"general_config_id" validate:"omitempty,gte=1,lte=64" zh:"通用配置ID"`
        AccountConfigIds   []string       `json:"account_config_ids" validate:"omitempty,gte=0" zh:"池账号配置ID列表"`
        SuiteExecutionMode int8           `json:"suite_execution_mode" validate:"oneof=1 2" zh:"集合执行方式"`
        CaseExecutionMode  int8           `json:"case_execution_mode" validate:"oneof=1 2" zh:"用例执行方式"`
        MaintainedBy       string         `json:"maintained_by" validate:"lte=64" zh:"API计划维护者"`
        SuiteIds           []*SuiteTypeId `json:"suite_ids,optional" validate:"omitempty,gte=0" zh:"导入的集合ID列表"`
    }
    CreateApiPlanResp {
        PlanId string `json:"plan_id"`
    }
)

// 删除API计划
type (
    RemoveApiPlanReq {
        ProjectId string  `json:"project_id" validate:"required" zh:"项目ID"`
        PlanIds  []string `json:"plan_ids" validate:"gt=0" zh:"API计划ID列表"`
    }
    RemoveApiPlanResp {}
)

// 编辑API计划
type (
    ModifyApiPlanReq {
        ProjectId          string        `json:"project_id" validate:"required" zh:"项目ID"`
        CategoryId         string        `json:"category_id" validate:"required" zh:"分类ID"`
        PlanId             string        `json:"plan_id" validate:"required" zh:"API计划ID"`
        Name               string        `json:"name" validate:"gte=1,lte=64" zh:"API计划名称"`
        Description        string        `json:"description" validate:"lte=255" zh:"API计划描述"`
        Priority           int64         `json:"priority" validate:"gte=0" zh:"优先级"`
        Tags               []string      `json:"tags" validate:"gte=0" zh:"API计划标签"`
        State              int8          `json:"state" validate:"oneof=1 2" zh:"API计划状态"`
        Type               string        `json:"type" validate:"oneof=MANUAL SCHEDULE INTERFACE" zh:"计划类型"`
        Purpose            string        `json:"purpose,default=UNDEFINED" validate:"oneof=UNDEFINED NORMAL PRECISION_TESTING" zh:"计划用途"`
        CronExpression     string        `json:"cron_expression" validate:"omitempty,gte=9,lte=128" zh:"定时触发的Cron表达式"`
        GeneralConfigId    string        `json:"general_config_id" validate:"omitempty,gte=1,lte=64" zh:"通用配置ID"`
        AccountConfigIds   []string      `json:"account_config_ids" validate:"omitempty,gte=0" zh:"池账号配置ID列表"`
        SuiteExecutionMode int8          `json:"suite_execution_mode" validate:"oneof=1 2" zh:"集合执行方式"`
        CaseExecutionMode  int8          `json:"case_execution_mode" validate:"oneof=1 2" zh:"用例执行方式"`
        MaintainedBy       string        `json:"maintained_by" validate:"lte=64" zh:"API计划维护者"`
    }
    ModifyApiPlanResp {}
)

// 查询API计划
type (
    SearchApiPlanReq {
        ProjectId  string       `json:"project_id" validate:"required" zh:"项目ID"`
        CategoryId string       `json:"category_id,omitempty,optional" zh:"分类ID"`
        Condition  *Condition   `json:"condition,omitempty,optional" zh:"查询条件"`
        Pagination *Pagination  `json:"pagination,omitempty,optional" zh:"查询分页"`
        Sort       []*SortField `json:"sort,omitempty,optional" zh:"查询排序"`
    }
    SearchApiPlanResp {
        CurrentPage uint64               `json:"current_page"`
        PageSize    uint64               `json:"page_size"`
        TotalCount  uint64               `json:"total_count"`
        TotalPage   uint64               `json:"total_page"`
        Items       []*SearchApiPlanItem `json:"items"`
    }
)

// 查看API计划
type (
    ViewApiPlanReq {
        ProjectId string `form:"project_id" validate:"required" zh:"项目ID"`
        PlanId    string `form:"plan_id" validate:"required" zh:"API计划ID"`
    }
    ViewApiPlanResp {
        *ApiPlan
    }
)

// 搜索API计划中的集合
type (
    SearchSuiteInApiPlanReq {
        ProjectId  string       `json:"project_id" validate:"required" zh:"项目ID"`
        PlanId     string       `json:"plan_id" validate:"required" zh:"API计划ID"`
        SuiteType  string       `json:"suite_type" validate:"omitempty,oneof=API_SUITE INTERFACE_DOCUMENT" zh:"集合类型"`
        Condition  *Condition   `json:"condition,omitempty,optional" zh:"查询条件"`
        Pagination *Pagination  `json:"pagination,omitempty,optional" zh:"查询分页"`
        Sort       []*SortField `json:"sort,omitempty,optional" zh:"查询排序"`
    }
    SearchSuiteInApiPlanResp {
        CurrentPage uint64      `json:"current_page"`
        PageSize    uint64      `json:"page_size"`
        TotalCount  uint64      `json:"total_count"`
        TotalPage   uint64      `json:"total_page"`
        Items       []*SearchSuiteInApiPlanItem `json:"items"`
    }
)

// 搜索不在指定的API计划中的集合
type (
    SearchSuiteNotInApiPlanReq {
        ProjectId  string       `json:"project_id" validate:"required" zh:"项目ID"`
        PlanId     string       `json:"plan_id" validate:"required" zh:"API计划ID"`
        SuiteType  string       `json:"suite_type" validate:"oneof=API_SUITE INTERFACE_DOCUMENT" zh:"集合类型"`
        CategoryId string       `json:"category_id" validate:"required" zh:"集合分类树的分类ID"`
        Condition  *Condition   `json:"condition,omitempty,optional" zh:"查询条件"`
        Pagination *Pagination  `json:"pagination,omitempty,optional" zh:"查询分页"`
        Sort       []*SortField `json:"sort,omitempty,optional" zh:"查询排序"`
    }
    SearchSuiteNotInApiPlanResp {
        CurrentPage uint64      `json:"current_page"`
        PageSize    uint64      `json:"page_size"`
        TotalCount  uint64      `json:"total_count"`
        TotalPage   uint64      `json:"total_page"`
        Items       []*SearchSuiteNotInApiPlanItem `json:"items"`
    }
)

// 编辑API计划的集合列表
type (
    ModifySuiteListOfApiPlanReq {
        ProjectId     string         `json:"project_id" validate:"required" zh:"项目ID"`
        PlanId        string         `json:"plan_id" validate:"required" zh:"API计划ID"`
        SuiteIds      []*SuiteTypeId `json:"suite_ids" validate:"gt=0" zh:"待变更的集合ID列表"`
        OperationType string         `json:"operation_type" validate:"oneof=ADD REMOVE" zh:"操作类型"`
    }
    ModifySuiteListOfApiPlanResp {}
)

// 搜索API计划中指定集合中的用例
type (
    SearchCaseInApiPlanReq {
        ProjectId  string       `json:"project_id" validate:"required" zh:"项目ID"`
        PlanId     string       `json:"plan_id" validate:"required" zh:"API计划ID"`
        SuiteType  string       `json:"suite_type" validate:"oneof=API_SUITE INTERFACE_DOCUMENT" zh:"集合类型"`
        SuiteId    string       `json:"suite_id" validate:"required" zh:"集合ID"`
        Condition  *Condition   `json:"condition,omitempty,optional" zh:"查询条件"`
        Pagination *Pagination  `json:"pagination,omitempty,optional" zh:"查询分页"`
        Sort       []*SortField `json:"sort,omitempty,optional" zh:"查询排序"`
    }
    SearchCaseInApiPlanResp {
        CurrentPage uint64      `json:"current_page"`
        PageSize    uint64      `json:"page_size"`
        TotalCount  uint64      `json:"total_count"`
        TotalPage   uint64      `json:"total_page"`
        Items       []*SearchCaseInApiPlanItem `json:"items"`
    }
)

// 修改API计划执行数据的引用状态
type (
    ModifyApiPlanReferenceStateReq {
        ProjectId string `json:"project_id" validate:"required" zh:"项目ID"`
        PlanId    string `json:"plan_id" validate:"required" zh:"API计划ID"`
        SuiteType string `json:"suite_type" validate:"oneof=API_SUITE INTERFACE_DOCUMENT" zh:"集合类型"`
        SuiteId   string `json:"suite_id" validate:"required" zh:"集合ID"`
        CaseType  string `json:"case_type" validate:"omitempty,oneof=API_CASE INTERFACE_CASE" zh:"用例类型"`
        CaseId    string `json:"case_id" validate:"omitempty" zh:"用例ID"`
        State     int8   `json:"state" validate:"oneof=1 2" zh:"引用状态"`
    }
    ModifyApiPlanReferenceStateResp {}
)

type AdvancedSearchSuiteNotInApiPlanItem {
    ProjectId    string        `json:"project_id"`    // 项目ID
    CategoryId   string        `json:"category_id"`   // 分类ID
    SuiteId      string        `json:"suite_id"`      // 集合ID
    Name         string        `json:"name"`          // 集合名称
    Description  string        `json:"description"`   // 集合描述
    Priority     int64         `json:"priority"`      // 优先级
    Tags         []string      `json:"tags"`          // 标签
    State        int8          `json:"state"`         // 状态
    CaseCount    int64         `json:"case_count"`    // 集合内用例数
    MaintainedBy *FullUserInfo `json:"maintained_by"` // 维护者
    CreatedBy    *FullUserInfo `json:"created_by"`    // 创建者
    UpdatedBy    *FullUserInfo `json:"updated_by"`    // 更新者
    CreatedAt    int64         `json:"created_at"`    // 创建时间
    UpdatedAt    int64         `json:"updated_at"`    // 更新时间
}

type (
    AdvancedSearchSuiteNotInApiPlanReq {
        ProjectId  string       `json:"project_id" validate:"required" zh:"项目ID"`
        PlanId     string       `json:"plan_id" validate:"required" zh:"API计划ID"`
        SuiteType  string       `json:"suite_type" validate:"oneof=API_SUITE INTERFACE_DOCUMENT" zh:"集合类型"`
        Condition  *Condition   `json:"condition,omitempty,optional" zh:"查询条件"`
        Pagination *Pagination  `json:"pagination,omitempty,optional" zh:"查询分页"`
        Sort       []*SortField `json:"sort,omitempty,optional" zh:"查询排序"`
    }
    AdvancedSearchSuiteNotInApiPlanResp {
        CurrentPage uint64                                 `json:"current_page"`
        PageSize    uint64                                 `json:"page_size"`
        TotalCount  uint64                                 `json:"total_count"`
        TotalPage   uint64                                 `json:"total_page"`
        Items       []*AdvancedSearchSuiteNotInApiPlanItem `json:"items"`
    }
)
