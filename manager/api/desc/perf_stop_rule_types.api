syntax = "v1"

import "types.api"

type PerfStopRule {
    ProjectId   string         `json:"project_id"`  // 项目ID
    RuleId      string         `json:"rule_id"`     // 规则ID

    Name        string         `json:"name"`        // 规则名称
    Description string         `json:"description"` // 规则描述
    MetricType  string         `json:"metric_type"` // 指标类型
    Threshold   float64        `json:"threshold"`   // 阀值
    Duration    uint32         `json:"duration"`    // 持续时间

    State       int8           `json:"state"`       // 状态

    CreatedBy   *FullUserInfo  `json:"created_by"`  // 创建者
    UpdatedBy   *FullUserInfo  `json:"updated_by"`  // 更新者
    CreatedAt   int64          `json:"created_at"`  // 创建时间
    UpdatedAt   int64          `json:"updated_at"`  // 更新时间
}

// 创建压测停止规则
type (
    CreatePerfStopRuleReq {
        ProjectId   string  `json:"project_id" validate:"required" zh:"项目ID"`
        Name        string  `json:"name" validate:"gte=1,lte=64" zh:"压测停止规则名称"`
        Description string  `json:"description" validate:"lte=255" zh:"压测停止规则描述"`
        MetricType  string  `json:"metric_type" validate:"oneof=qps fail_ratio p99 p95 p90 p75 p50" zh:"指标类型"`
        Threshold   float64 `json:"threshold" validate:"gte=0" zh:"阀值"`
        Duration    uint32  `json:"duration" validate:"gte=60,lte=3600" zh:"持续时间"`
    }
    CreatePerfStopRuleResp {
        RuleId string `json:"rule_id"`
    }
)

// 删除压测停止规则
type (
    RemovePerfStopRuleReq {
        ProjectId string  `json:"project_id" validate:"required" zh:"项目ID"`
        RuleIds  []string `json:"rule_ids" validate:"gt=0" zh:"压测停止规则ID列表"`
    }
    RemovePerfStopRuleResp {}
)

// 编辑压测停止规则
type (
    ModifyPerfStopRuleReq {
        ProjectId   string  `json:"project_id" validate:"required" zh:"项目ID"`
        RuleId      string  `json:"rule_id" validate:"required" zh:"压测停止规则ID"`
        Name        string  `json:"name" validate:"gte=1,lte=64" zh:"压测停止规则名称"`
        Description string  `json:"description" validate:"lte=255" zh:"压测停止规则描述"`
        MetricType  string  `json:"metric_type" validate:"oneof=qps fail_ratio p99 p95 p90 p75 p50" zh:"指标类型"`
        Threshold   float64 `json:"threshold" validate:"gte=0" zh:"阀值"`
        Duration    uint32  `json:"duration" validate:"gte=60,lte=3600" zh:"持续时间"`
        State       int8    `json:"state,omitempty,optional,default=1" validate:"oneof=1 2" zh:"压测停止规则状态"`
    }
    ModifyPerfStopRuleResp {}
)

// 查询压测停止规则
type (
    SearchPerfStopRuleReq {
        ProjectId  string       `json:"project_id" validate:"required" zh:"项目ID"`
        Condition  *Condition   `json:"condition,omitempty,optional" zh:"查询条件"`
        Pagination *Pagination  `json:"pagination,omitempty,optional" zh:"查询分页"`
        Sort       []*SortField `json:"sort,omitempty,optional" zh:"查询排序"`
    }
    SearchPerfStopRuleResp {
        CurrentPage uint64          `json:"current_page"`
        PageSize    uint64          `json:"page_size"`
        TotalCount  uint64          `json:"total_count"`
        TotalPage   uint64          `json:"total_page"`
        Items       []*PerfStopRule `json:"items"`
    }
)

// 查看压测停止规则
type (
    ViewPerfStopRuleReq {
        ProjectId string `form:"project_id" validate:"required" zh:"项目ID"`
        RuleId    string `form:"rule_id" validate:"required" zh:"压测停止规则ID"`
    }
    ViewPerfStopRuleResp {
        *PerfStopRule
    }
)
