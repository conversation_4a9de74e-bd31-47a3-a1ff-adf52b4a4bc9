syntax = "v1"

import "component_group_types.api"

@server (
    prefix: manager/v1
    group: componentgroup
)
service manager {
    @doc "create component group"
    @handler createComponentGroup
    post /component_group/create (CreateComponentGroupReq) returns (CreateComponentGroupResp)

    @doc "delete component group"
    @handler removeComponentGroup
    put /component_group/remove (RemoveComponentGroupReq) returns (RemoveComponentGroupResp)

    @doc "modify component group"
    @handler modifyComponentGroup
    put /component_group/modify (ModifyComponentGroupReq) returns (ModifyComponentGroupResp)

    @doc "search component groups"
    @handler searchComponentGroup
    post /component_group/search (SearchComponentGroupReq) returns (SearchComponentGroupResp)

    @doc "view component group"
    @handler viewComponentGroup
    get /component_group/view (ViewComponentGroupReq) returns (ViewComponentGroupResp)

    @doc "search reference data of component group"
    @handler searchComponentGroupReference
    post /component_group/reference/search (SearchComponentGroupReferenceReq) returns (SearchComponentGroupReferenceResp)
}
