syntax = "v1"

import "perfcasev2_types.api"

@server (
    prefix: manager/v1
    group: perfCaseV2
)
service manager {
    @handler createPerfCaseV2
    post /perf_case_v2/create (CreatePerfCaseV2Req) returns (CreatePerfCaseV2Resp)

    @handler removePerfCaseV2
    put /perf_case_v2/remove (RemovePerfCaseV2Req) returns (RemovePerfCaseV2Resp)

    @handler modifyPerfCaseV2
    put /perf_case_v2/modify (ModifyPerfCaseV2Req) returns (ModifyPerfCaseV2Resp)

    @handler searchPerfCaseV2
    post /perf_case_v2/search (SearchPerfCaseV2Req) returns (SearchPerfCaseV2Resp)

    @handler viewPerfCaseV2
    get /perf_case_v2/view (ViewPerfCaseV2Req) returns (ViewPerfCaseV2Resp)
}
