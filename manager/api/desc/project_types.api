syntax = "v1"

import "types.api"

type Project {
    ProjectId         string        `json:"project_id"`
    Name              string        `json:"name"`
    Description       string        `json:"description"`
    ReviewEnabled     bool          `json:"review_enabled"`
    CoverageEnabled   bool          `json:"coverage_enabled"`
    CoverageLarkChats []*LarkChat   `json:"coverage_lark_chats"`
    CreatedBy         *FullUserInfo `json:"created_by"`
    UpdatedBy         *FullUserInfo `json:"updated_by"`
    CreatedAt         int64         `json:"created_at"`
    UpdatedAt         int64         `json:"updated_at"`
}

// 创建项目
type (
    CreateProjectReq {
        Name          string `json:"name" validate:"gte=1,lte=64" zh:"项目名称"`
        Description   string `json:"description" validate:"lte=255" zh:"项目描述"`
        ReviewEnabled bool   `json:"review_enabled,omitempty,optional,default=false" validate:"omitempty" zh:"是否开启用例审核功能"`
    }
    CreateProjectResp {
        ProjectId string `json:"project_id"`
    }
)

// 删除项目
type (
    RemoveProjectReq {
        ProjectId string `json:"project_id" validate:"required" zh:"项目ID"`
    }
    RemoveProjectResp {}
)

// 编辑项目
type (
    ModifyProjectReq {
        ProjectId         string      `json:"project_id" validate:"required" zh:"项目ID"`
        Name              string      `json:"name" validate:"gte=1,lte=64" zh:"项目名称"`
        Description       string      `json:"description" validate:"lte=255" zh:"项目描述"`
        ReviewEnabled     bool        `json:"review_enabled,omitempty,optional,default=false" validate:"omitempty" zh:"是否开启用例审核功能"`
        CoverageEnabled   bool        `json:"coverage_enabled,omitempty,optional,default=false" validate:"omitempty" zh:"是否开启接口用例覆盖率统计功能"`
        CoverageLarkChats []*LarkChat `json:"coverage_lark_chats,omitempty,optional" validate:"omitempty,lte=5" zh:"接口用例覆盖率飞书通知群组"`
    }
    ModifyProjectResp {}
)

// 查询项目
type (
    SearchProjectReq {
        Condition  *Condition   `json:"condition,omitempty,optional" zh:"查询条件"`
        Pagination *Pagination  `json:"pagination,omitempty,optional" zh:"查询分页"`
        Sort       []*SortField `json:"sort,omitempty,optional" zh:"查询排序"`
    }
    SearchProjectResp {
        CurrentPage uint64     `json:"current_page"`
        PageSize    uint64     `json:"page_size"`
        TotalCount  uint64     `json:"total_count"`
        TotalPage   uint64     `json:"total_page"`
        Items       []*Project `json:"items"`
    }
)

// 查看项目
type (
    ViewProjectReq {
        ProjectId string `form:"project_id" validate:"required" zh:"项目ID"`
    }
    ViewProjectResp {
        *Project
    }
)

// 查询项目用户
type (
    SearchProjectUserReq {
        ProjectId  string       `json:"project_id" validate:"required" zh:"项目ID"`
        Condition  *Condition   `json:"condition,omitempty,optional" zh:"查询条件"`
        Pagination *Pagination  `json:"pagination,omitempty,optional" zh:"查询分页"`
        Sort       []*SortField `json:"sort,omitempty,optional" zh:"查询排序"`
    }
    SearchProjectUserResp {
        CurrentPage uint64          `json:"current_page"`
        PageSize    uint64          `json:"page_size"`
        TotalCount  uint64          `json:"total_count"`
        TotalPage   uint64          `json:"total_page"`
        Items       []*FullUserInfo `json:"items"`
    }
)

// 开启、关闭用例审核功能
type (
	ModifyProjectReviewFunctionReq {
		ProjectId     string `json:"project_id" validate:"required" zh:"项目ID"`
		ReviewEnabled bool   `json:"review_enabled" validate:"omitempty" zh:"是否开启用例审核功能"`
	}
	ModifyProjectReviewFunctionResp {}
)

// 开启、关闭接口用例覆盖率功能
type (
	ModifyProjectCoverageFunctionReq {
		ProjectId         string      `json:"project_id" validate:"required" zh:"项目ID"`
		CoverageEnabled   bool        `json:"coverage_enabled" validate:"omitempty" zh:"是否开启接口用例覆盖率统计功能"`
		CoverageLarkChats []*LarkChat `json:"coverage_lark_chats" validate:"lte=5" zh:"接口用例覆盖率飞书通知群组"`
	}
	ModifyProjectCoverageFunctionResp {}
)
