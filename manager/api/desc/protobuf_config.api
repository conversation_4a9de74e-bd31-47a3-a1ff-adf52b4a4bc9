syntax = "v1"

import "protobuf_config_types.api"

@server (
    prefix: manager/v1
    group: protobufConfig
)
service manager {
    @handler createProtobufConfig
    post /protobuf_config/create (CreateProtobufConfigReq) returns (CreateProtobufConfigResp)

    @handler removeProtobufConfig
    put /protobuf_config/remove (RemoveProtobufConfigReq) returns (RemoveProtobufConfigResp)

    @handler modifyProtobufConfig
    put /protobuf_config/modify (ModifyProtobufConfigReq) returns (ModifyProtobufConfigResp)

    @handler searchProtobufConfig
    post /protobuf_config/search (SearchProtobufConfigReq) returns (SearchProtobufConfigResp)

    @handler viewProtobufConfig
    get /protobuf_config/view (ViewProtobufConfigReq) returns (ViewProtobufConfigResp)
}
