syntax = "v1"

import "types.api"

type PerfCase {
    ProjectId     string        `json:"project_id"`
    CaseId        string        `json:"case_id"`
    Name          string        `json:"name"`
    Description   string        `json:"description"`
    Extension     string        `json:"extension"`
    Hash          string        `json:"hash"`
    Size          uint32        `json:"size"`

    RateLimit     // 限流配置
    BasicPerfData // 压测数据配置
    LoadGenerator // 施压机资源配置

    CreatedBy     *FullUserInfo `json:"created_by"`
    UpdatedBy     *FullUserInfo `json:"updated_by"`
    CreatedAt     int64         `json:"created_at"`
    UpdatedAt     int64         `json:"updated_at"`
}

type (
    UploadPerfCaseReq {
	    ProjectId        string `form:"project_id" validate:"required" zh:"项目ID"`
	    PerfDataId       string `form:"perf_data_id,optional" zh:"压测数据ID"`
	    NumberOfVu       uint32 `form:"number_of_vu,optional,default=0,range=[0:200000]" zh:"虚拟用户数"`
	    NumberOfLg       uint32 `form:"number_of_lg,optional,default=0,range=[0:200]" zh:"施压机数量"`
	    RequestsOfCpu    string `form:"requests_of_cpu,optional" zh:"最小分配的CPU资源"`
	    RequestsOfMemory string `form:"requests_of_memory,optional" zh:"最小分配的内存资源"`
	    LimitsOfCpu      string `form:"limits_of_cpu,optional" zh:"最大分配的CPU资源"`
	    LimitsOfMemory   string `form:"limits_of_memory,optional" zh:"最大分配的内存资源"`
    }
    UploadPerfCaseResp {
        *PerfCase
    }
)

type (
    DownloadPerfCaseReq {
	    ProjectId string `form:"project_id" validate:"required" zh:"项目ID"`
	    CaseId    string `form:"case_id" validate:"required" zh:"压测用例ID"`
    }
    DownloadPerfCaseResp {}
)

type (
    RemovePerfCaseReq {
	    ProjectId string   `json:"project_id" validate:"required" zh:"项目ID"`
	    CaseIds   []string `json:"case_ids" validate:"gt=0" zh:"压测用例ID列表"`
    }
    RemovePerfCaseResp {}
)

type (
    ModifyPerfCaseReq {
	    ProjectId        string `form:"project_id" validate:"required" zh:"项目ID"`
	    CaseId           string `form:"case_id" validate:"required" zh:"压测用例ID"`
	    PerfDataId       string `form:"perf_data_id,optional" zh:"压测数据ID"`
	    NumberOfVu       uint32 `form:"number_of_vu,optional,default=0,range=[0:200000]" zh:"虚拟用户数"`
	    NumberOfLg       uint32 `form:"number_of_lg,optional,default=0,range=[0:200]" zh:"施压机数量"`
	    RequestsOfCpu    string `form:"requests_of_cpu,optional" zh:"最小分配的CPU资源"`
	    RequestsOfMemory string `form:"requests_of_memory,optional" zh:"最小分配的内存资源"`
	    LimitsOfCpu      string `form:"limits_of_cpu,optional" zh:"最大分配的CPU资源"`
	    LimitsOfMemory   string `form:"limits_of_memory,optional" zh:"最大分配的内存资源"`
    }
    ModifyPerfCaseResp {}
)

type (
    ViewPerfCaseReq {
        ProjectId string `form:"project_id" validate:"required" zh:"项目ID"`
        CaseId    string `form:"case_id" validate:"required" zh:"压测用例ID"`
    }
    ViewPerfCaseResp {
        *PerfCase
    }
)

type (
    CalculatePerfCaseReq {
        ProjectId        string `form:"project_id" validate:"required" zh:"项目ID"`
        CaseId           string `form:"case_id" validate:"required" zh:"压测用例ID"`
        PerfDataId       string `form:"perf_data_id,optional" zh:"压测数据ID"`
        NumberOfVu       uint32 `form:"number_of_vu,optional,default=0,range=[0:200000]" zh:"虚拟用户数"`
        NumberOfLg       uint32 `form:"number_of_lg,optional,default=0,range=[0:200]" zh:"施压机数量"`
        RequestsOfCpu    string `form:"requests_of_cpu,optional" zh:"最小分配的CPU资源"`
        RequestsOfMemory string `form:"requests_of_memory,optional" zh:"最小分配的内存资源"`
        LimitsOfCpu      string `form:"limits_of_cpu,optional" zh:"最大分配的CPU资源"`
        LimitsOfMemory   string `form:"limits_of_memory,optional" zh:"最大分配的内存资源"`
    }
    CalculatePerfCaseResp {
        *PerfCase
    }
)
