syntax = "v1"

import "perfplanv2_types.api"

@server (
    prefix: manager/v1
    group: perfPlanV2
)
service manager {
    @handler createPerfPlanV2
    post /perf_plan_v2/create (CreatePerfPlanV2Req) returns (CreatePerfPlanV2Resp)

    @handler removePerfPlanV2
    put /perf_plan_v2/remove (RemovePerfPlanV2Req) returns (RemovePerfPlanV2Resp)

    @handler modifyPerfPlanV2
    put /perf_plan_v2/modify (ModifyPerfPlanV2Req) returns (ModifyPerfPlanV2Resp)

    @handler searchPerfPlanV2
    post /perf_plan_v2/search (SearchPerfPlanV2Req) returns (SearchPerfPlanV2Resp)

    @handler viewPerfPlanV2
    get /perf_plan_v2/view (ViewPerfPlanV2Req) returns (ViewPerfPlanV2Resp)

    @handler searchCaseInPerfPlanV2
    post /perf_plan_v2/case/search (SearchCaseInPerfPlanV2Req) returns (SearchCaseInPerfPlanV2Resp)

    @handler searchProtobufInPerfPlanV2
    post /perf_plan_v2/protobuf/search (SearchProtobufInPerfPlanV2Req) returns (SearchProtobufInPerfPlanV2Resp)

    @handler searchRuleInPerfPlanV2
    post /perf_plan_v2/rule/search (SearchRuleInPerfPlanV2Req) returns (SearchRuleInPerfPlanV2Resp)
}
