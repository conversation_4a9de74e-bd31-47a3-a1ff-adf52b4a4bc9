syntax = "v1"

import "ui_agent_component_types.api"

@server (
    prefix: manager/v1
    group: uiAgentComponent
)
service manager {
    @handler createUIAgentComponent
    post /ui_agent_component/create (CreateUIAgentComponentReq) returns (CreateUIAgentComponentResp)

    @handler removeUIAgentComponent
    put /ui_agent_component/remove (RemoveUIAgentComponentReq) returns (RemoveUIAgentComponentResp)

    @handler modifyUIAgentComponent
    put /ui_agent_component/modify (ModifyUIAgentComponentReq) returns (ModifyUIAgentComponentResp)

    @handler searchUIAgentComponent
    post /ui_agent_component/search (SearchUIAgentComponentReq) returns (SearchUIAgentComponentResp)

    @handler viewUIAgentComponent
    get /ui_agent_component/view (ViewUIAgentComponentReq) returns (ViewUIAgentComponentResp)
}
