syntax = "v1"

type (
    UploadUIAgentImageReq {
        ProjectId string `form:"project_id" validate:"required" zh:"项目ID"`
    }
    UploadUIAgentImageResp {
        ImageId string `json:"image_id"`
    }
)

type (
    DownloadUIAgentImageReq {
        ProjectId string `form:"project_id" validate:"required" zh:"项目ID"`
        ImageId   string `form:"image_id" validate:"required" zh:"图片ID"`
    }
    DownloadUIAgentImageResp {}
)
