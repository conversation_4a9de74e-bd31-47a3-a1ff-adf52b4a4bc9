// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.1
// 	protoc        v5.29.2
// source: manager/element.proto

package pb

import (
	reflect "reflect"
	sync "sync"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	structpb "google.golang.org/protobuf/types/known/structpb"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Node 点
type Node struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Type          string                 `protobuf:"bytes,2,opt,name=type,proto3" json:"type,omitempty"`
	Label         string                 `protobuf:"bytes,3,opt,name=label,proto3" json:"label,omitempty"`
	ItemType      string                 `protobuf:"bytes,4,opt,name=itemType,proto3" json:"itemType,omitempty"`
	Width         float64                `protobuf:"fixed64,5,opt,name=width,proto3" json:"width,omitempty"`
	Height        float64                `protobuf:"fixed64,6,opt,name=height,proto3" json:"height,omitempty"`
	Size          []float64              `protobuf:"fixed64,7,rep,packed,name=size,proto3" json:"size,omitempty"`
	X             float64                `protobuf:"fixed64,8,opt,name=x,proto3" json:"x,omitempty"`
	Y             float64                `protobuf:"fixed64,9,opt,name=y,proto3" json:"y,omitempty"`
	AnchorPoints  []*structpb.ListValue  `protobuf:"bytes,10,rep,name=anchorPoints,proto3" json:"anchorPoints,omitempty"`
	Icon          string                 `protobuf:"bytes,11,opt,name=icon,proto3" json:"icon,omitempty"`
	Style         *structpb.Struct       `protobuf:"bytes,12,opt,name=style,proto3" json:"style,omitempty"`
	LabelCfg      *structpb.Struct       `protobuf:"bytes,13,opt,name=labelCfg,proto3" json:"labelCfg,omitempty"`
	ComboId       string                 `protobuf:"bytes,14,opt,name=comboId,proto3" json:"comboId,omitempty"`
	Data          *structpb.Struct       `protobuf:"bytes,15,opt,name=data,proto3" json:"data,omitempty"`
	Order         int32                  `protobuf:"varint,16,opt,name=order,json=_order,proto3" json:"order,omitempty"` // 注：不能定义为`int64`，否则序列化的时候会被转为`string`，再反序列化的时候就会报错
	LayoutOrder   int32                  `protobuf:"varint,17,opt,name=layoutOrder,proto3" json:"layoutOrder,omitempty"` // 注：不能定义为`int64`，否则序列化的时候会被转为`string`，再反序列化的时候就会报错
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Node) Reset() {
	*x = Node{}
	mi := &file_manager_element_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Node) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Node) ProtoMessage() {}

func (x *Node) ProtoReflect() protoreflect.Message {
	mi := &file_manager_element_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Node.ProtoReflect.Descriptor instead.
func (*Node) Descriptor() ([]byte, []int) {
	return file_manager_element_proto_rawDescGZIP(), []int{0}
}

func (x *Node) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Node) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *Node) GetLabel() string {
	if x != nil {
		return x.Label
	}
	return ""
}

func (x *Node) GetItemType() string {
	if x != nil {
		return x.ItemType
	}
	return ""
}

func (x *Node) GetWidth() float64 {
	if x != nil {
		return x.Width
	}
	return 0
}

func (x *Node) GetHeight() float64 {
	if x != nil {
		return x.Height
	}
	return 0
}

func (x *Node) GetSize() []float64 {
	if x != nil {
		return x.Size
	}
	return nil
}

func (x *Node) GetX() float64 {
	if x != nil {
		return x.X
	}
	return 0
}

func (x *Node) GetY() float64 {
	if x != nil {
		return x.Y
	}
	return 0
}

func (x *Node) GetAnchorPoints() []*structpb.ListValue {
	if x != nil {
		return x.AnchorPoints
	}
	return nil
}

func (x *Node) GetIcon() string {
	if x != nil {
		return x.Icon
	}
	return ""
}

func (x *Node) GetStyle() *structpb.Struct {
	if x != nil {
		return x.Style
	}
	return nil
}

func (x *Node) GetLabelCfg() *structpb.Struct {
	if x != nil {
		return x.LabelCfg
	}
	return nil
}

func (x *Node) GetComboId() string {
	if x != nil {
		return x.ComboId
	}
	return ""
}

func (x *Node) GetData() *structpb.Struct {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *Node) GetOrder() int32 {
	if x != nil {
		return x.Order
	}
	return 0
}

func (x *Node) GetLayoutOrder() int32 {
	if x != nil {
		return x.LayoutOrder
	}
	return 0
}

// Edge 线
type Edge struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	Id              string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Type            string                 `protobuf:"bytes,2,opt,name=type,proto3" json:"type,omitempty"`
	Label           string                 `protobuf:"bytes,3,opt,name=label,proto3" json:"label,omitempty"`
	Source          string                 `protobuf:"bytes,4,opt,name=source,proto3" json:"source,omitempty"`
	SourceAnchor    float64                `protobuf:"fixed64,5,opt,name=sourceAnchor,proto3" json:"sourceAnchor,omitempty"`
	Target          string                 `protobuf:"bytes,6,opt,name=target,proto3" json:"target,omitempty"`
	TargetAnchor    float64                `protobuf:"fixed64,7,opt,name=targetAnchor,proto3" json:"targetAnchor,omitempty"`
	LineAppendWidth int32                  `protobuf:"varint,8,opt,name=lineAppendWidth,proto3" json:"lineAppendWidth,omitempty"`
	Clazz           string                 `protobuf:"bytes,9,opt,name=clazz,proto3" json:"clazz,omitempty"`
	Attrs           *structpb.Struct       `protobuf:"bytes,10,opt,name=attrs,proto3" json:"attrs,omitempty"`
	Style           *structpb.Struct       `protobuf:"bytes,11,opt,name=style,proto3" json:"style,omitempty"`
	LabelCfg        *structpb.Struct       `protobuf:"bytes,12,opt,name=labelCfg,proto3" json:"labelCfg,omitempty"`
	StartPoint      *structpb.Struct       `protobuf:"bytes,13,opt,name=startPoint,proto3" json:"startPoint,omitempty"`
	EndPoint        *structpb.Struct       `protobuf:"bytes,14,opt,name=endPoint,proto3" json:"endPoint,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *Edge) Reset() {
	*x = Edge{}
	mi := &file_manager_element_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Edge) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Edge) ProtoMessage() {}

func (x *Edge) ProtoReflect() protoreflect.Message {
	mi := &file_manager_element_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Edge.ProtoReflect.Descriptor instead.
func (*Edge) Descriptor() ([]byte, []int) {
	return file_manager_element_proto_rawDescGZIP(), []int{1}
}

func (x *Edge) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Edge) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *Edge) GetLabel() string {
	if x != nil {
		return x.Label
	}
	return ""
}

func (x *Edge) GetSource() string {
	if x != nil {
		return x.Source
	}
	return ""
}

func (x *Edge) GetSourceAnchor() float64 {
	if x != nil {
		return x.SourceAnchor
	}
	return 0
}

func (x *Edge) GetTarget() string {
	if x != nil {
		return x.Target
	}
	return ""
}

func (x *Edge) GetTargetAnchor() float64 {
	if x != nil {
		return x.TargetAnchor
	}
	return 0
}

func (x *Edge) GetLineAppendWidth() int32 {
	if x != nil {
		return x.LineAppendWidth
	}
	return 0
}

func (x *Edge) GetClazz() string {
	if x != nil {
		return x.Clazz
	}
	return ""
}

func (x *Edge) GetAttrs() *structpb.Struct {
	if x != nil {
		return x.Attrs
	}
	return nil
}

func (x *Edge) GetStyle() *structpb.Struct {
	if x != nil {
		return x.Style
	}
	return nil
}

func (x *Edge) GetLabelCfg() *structpb.Struct {
	if x != nil {
		return x.LabelCfg
	}
	return nil
}

func (x *Edge) GetStartPoint() *structpb.Struct {
	if x != nil {
		return x.StartPoint
	}
	return nil
}

func (x *Edge) GetEndPoint() *structpb.Struct {
	if x != nil {
		return x.EndPoint
	}
	return nil
}

// Combo 框
type Combo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Type          string                 `protobuf:"bytes,2,opt,name=type,proto3" json:"type,omitempty"`
	Label         string                 `protobuf:"bytes,3,opt,name=label,proto3" json:"label,omitempty"`
	ItemType      string                 `protobuf:"bytes,4,opt,name=itemType,proto3" json:"itemType,omitempty"`
	X             float64                `protobuf:"fixed64,5,opt,name=x,proto3" json:"x,omitempty"`
	Y             float64                `protobuf:"fixed64,6,opt,name=y,proto3" json:"y,omitempty"`
	AnchorPoints  []*structpb.ListValue  `protobuf:"bytes,7,rep,name=anchorPoints,proto3" json:"anchorPoints,omitempty"`
	Icon          string                 `protobuf:"bytes,8,opt,name=icon,proto3" json:"icon,omitempty"`
	Style         *structpb.Struct       `protobuf:"bytes,9,opt,name=style,proto3" json:"style,omitempty"`
	LabelCfg      *structpb.Struct       `protobuf:"bytes,10,opt,name=labelCfg,proto3" json:"labelCfg,omitempty"`
	Depth         int32                  `protobuf:"varint,11,opt,name=depth,proto3" json:"depth,omitempty"` // 注：不能定义为`int64`，否则序列化的时候会被转为`string`，再反序列化的时候就会报错
	Padding       []float64              `protobuf:"fixed64,12,rep,packed,name=padding,proto3" json:"padding,omitempty"`
	Collapsed     bool                   `protobuf:"varint,13,opt,name=collapsed,proto3" json:"collapsed,omitempty"`
	Children      []*Combo_ComboChild    `protobuf:"bytes,14,rep,name=children,proto3" json:"children,omitempty"`
	Data          *structpb.Struct       `protobuf:"bytes,15,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Combo) Reset() {
	*x = Combo{}
	mi := &file_manager_element_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Combo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Combo) ProtoMessage() {}

func (x *Combo) ProtoReflect() protoreflect.Message {
	mi := &file_manager_element_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Combo.ProtoReflect.Descriptor instead.
func (*Combo) Descriptor() ([]byte, []int) {
	return file_manager_element_proto_rawDescGZIP(), []int{2}
}

func (x *Combo) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Combo) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *Combo) GetLabel() string {
	if x != nil {
		return x.Label
	}
	return ""
}

func (x *Combo) GetItemType() string {
	if x != nil {
		return x.ItemType
	}
	return ""
}

func (x *Combo) GetX() float64 {
	if x != nil {
		return x.X
	}
	return 0
}

func (x *Combo) GetY() float64 {
	if x != nil {
		return x.Y
	}
	return 0
}

func (x *Combo) GetAnchorPoints() []*structpb.ListValue {
	if x != nil {
		return x.AnchorPoints
	}
	return nil
}

func (x *Combo) GetIcon() string {
	if x != nil {
		return x.Icon
	}
	return ""
}

func (x *Combo) GetStyle() *structpb.Struct {
	if x != nil {
		return x.Style
	}
	return nil
}

func (x *Combo) GetLabelCfg() *structpb.Struct {
	if x != nil {
		return x.LabelCfg
	}
	return nil
}

func (x *Combo) GetDepth() int32 {
	if x != nil {
		return x.Depth
	}
	return 0
}

func (x *Combo) GetPadding() []float64 {
	if x != nil {
		return x.Padding
	}
	return nil
}

func (x *Combo) GetCollapsed() bool {
	if x != nil {
		return x.Collapsed
	}
	return false
}

func (x *Combo) GetChildren() []*Combo_ComboChild {
	if x != nil {
		return x.Children
	}
	return nil
}

func (x *Combo) GetData() *structpb.Struct {
	if x != nil {
		return x.Data
	}
	return nil
}

type ListFloat struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	List          []float32              `protobuf:"fixed32,1,rep,packed,name=list,proto3" json:"list,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListFloat) Reset() {
	*x = ListFloat{}
	mi := &file_manager_element_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListFloat) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListFloat) ProtoMessage() {}

func (x *ListFloat) ProtoReflect() protoreflect.Message {
	mi := &file_manager_element_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListFloat.ProtoReflect.Descriptor instead.
func (*ListFloat) Descriptor() ([]byte, []int) {
	return file_manager_element_proto_rawDescGZIP(), []int{3}
}

func (x *ListFloat) GetList() []float32 {
	if x != nil {
		return x.List
	}
	return nil
}

type Combo_ComboChild struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	ComboId       string                 `protobuf:"bytes,2,opt,name=comboId,proto3" json:"comboId,omitempty"`
	ItemType      string                 `protobuf:"bytes,3,opt,name=itemType,proto3" json:"itemType,omitempty"`
	Depth         int32                  `protobuf:"varint,4,opt,name=depth,proto3" json:"depth,omitempty"` // 注：不能定义为`int64`，否则序列化的时候会被转为`string`，再反序列化的时候就会报错
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Combo_ComboChild) Reset() {
	*x = Combo_ComboChild{}
	mi := &file_manager_element_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Combo_ComboChild) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Combo_ComboChild) ProtoMessage() {}

func (x *Combo_ComboChild) ProtoReflect() protoreflect.Message {
	mi := &file_manager_element_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Combo_ComboChild.ProtoReflect.Descriptor instead.
func (*Combo_ComboChild) Descriptor() ([]byte, []int) {
	return file_manager_element_proto_rawDescGZIP(), []int{2, 0}
}

func (x *Combo_ComboChild) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Combo_ComboChild) GetComboId() string {
	if x != nil {
		return x.ComboId
	}
	return ""
}

func (x *Combo_ComboChild) GetItemType() string {
	if x != nil {
		return x.ItemType
	}
	return ""
}

func (x *Combo_ComboChild) GetDepth() int32 {
	if x != nil {
		return x.Depth
	}
	return 0
}

var File_manager_element_proto protoreflect.FileDescriptor

var file_manager_element_proto_rawDesc = []byte{
	0x0a, 0x15, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2f, 0x65, 0x6c, 0x65, 0x6d, 0x65, 0x6e,
	0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x07, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72,
	0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2f, 0x73, 0x74, 0x72, 0x75, 0x63, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xf2,
	0x03, 0x0a, 0x04, 0x4e, 0x6f, 0x64, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x6c,
	0x61, 0x62, 0x65, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6c, 0x61, 0x62, 0x65,
	0x6c, 0x12, 0x1a, 0x0a, 0x08, 0x69, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x69, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x12, 0x14, 0x0a,
	0x05, 0x77, 0x69, 0x64, 0x74, 0x68, 0x18, 0x05, 0x20, 0x01, 0x28, 0x01, 0x52, 0x05, 0x77, 0x69,
	0x64, 0x74, 0x68, 0x12, 0x16, 0x0a, 0x06, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x01, 0x52, 0x06, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x73,
	0x69, 0x7a, 0x65, 0x18, 0x07, 0x20, 0x03, 0x28, 0x01, 0x52, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x12,
	0x0c, 0x0a, 0x01, 0x78, 0x18, 0x08, 0x20, 0x01, 0x28, 0x01, 0x52, 0x01, 0x78, 0x12, 0x0c, 0x0a,
	0x01, 0x79, 0x18, 0x09, 0x20, 0x01, 0x28, 0x01, 0x52, 0x01, 0x79, 0x12, 0x3e, 0x0a, 0x0c, 0x61,
	0x6e, 0x63, 0x68, 0x6f, 0x72, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x18, 0x0a, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x0c, 0x61,
	0x6e, 0x63, 0x68, 0x6f, 0x72, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x69,
	0x63, 0x6f, 0x6e, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x69, 0x63, 0x6f, 0x6e, 0x12,
	0x2d, 0x0a, 0x05, 0x73, 0x74, 0x79, 0x6c, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x53, 0x74, 0x72, 0x75, 0x63, 0x74, 0x52, 0x05, 0x73, 0x74, 0x79, 0x6c, 0x65, 0x12, 0x33,
	0x0a, 0x08, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x43, 0x66, 0x67, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x17, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x75, 0x63, 0x74, 0x52, 0x08, 0x6c, 0x61, 0x62, 0x65, 0x6c,
	0x43, 0x66, 0x67, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x6d, 0x62, 0x6f, 0x49, 0x64, 0x18, 0x0e,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6f, 0x6d, 0x62, 0x6f, 0x49, 0x64, 0x12, 0x2b, 0x0a,
	0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74,
	0x72, 0x75, 0x63, 0x74, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x12, 0x15, 0x0a, 0x05, 0x6f, 0x72,
	0x64, 0x65, 0x72, 0x18, 0x10, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x5f, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x12, 0x20, 0x0a, 0x0b, 0x6c, 0x61, 0x79, 0x6f, 0x75, 0x74, 0x4f, 0x72, 0x64, 0x65, 0x72,
	0x18, 0x11, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x6c, 0x61, 0x79, 0x6f, 0x75, 0x74, 0x4f, 0x72,
	0x64, 0x65, 0x72, 0x22, 0xf9, 0x03, 0x0a, 0x04, 0x45, 0x64, 0x67, 0x65, 0x12, 0x0e, 0x0a, 0x02,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65,
	0x12, 0x14, 0x0a, 0x05, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x22,
	0x0a, 0x0c, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x41, 0x6e, 0x63, 0x68, 0x6f, 0x72, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x01, 0x52, 0x0c, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x41, 0x6e, 0x63, 0x68,
	0x6f, 0x72, 0x12, 0x16, 0x0a, 0x06, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x12, 0x22, 0x0a, 0x0c, 0x74, 0x61,
	0x72, 0x67, 0x65, 0x74, 0x41, 0x6e, 0x63, 0x68, 0x6f, 0x72, 0x18, 0x07, 0x20, 0x01, 0x28, 0x01,
	0x52, 0x0c, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x41, 0x6e, 0x63, 0x68, 0x6f, 0x72, 0x12, 0x28,
	0x0a, 0x0f, 0x6c, 0x69, 0x6e, 0x65, 0x41, 0x70, 0x70, 0x65, 0x6e, 0x64, 0x57, 0x69, 0x64, 0x74,
	0x68, 0x18, 0x08, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0f, 0x6c, 0x69, 0x6e, 0x65, 0x41, 0x70, 0x70,
	0x65, 0x6e, 0x64, 0x57, 0x69, 0x64, 0x74, 0x68, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x6c, 0x61, 0x7a,
	0x7a, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x63, 0x6c, 0x61, 0x7a, 0x7a, 0x12, 0x2d,
	0x0a, 0x05, 0x61, 0x74, 0x74, 0x72, 0x73, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x53, 0x74, 0x72, 0x75, 0x63, 0x74, 0x52, 0x05, 0x61, 0x74, 0x74, 0x72, 0x73, 0x12, 0x2d, 0x0a,
	0x05, 0x73, 0x74, 0x79, 0x6c, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53,
	0x74, 0x72, 0x75, 0x63, 0x74, 0x52, 0x05, 0x73, 0x74, 0x79, 0x6c, 0x65, 0x12, 0x33, 0x0a, 0x08,
	0x6c, 0x61, 0x62, 0x65, 0x6c, 0x43, 0x66, 0x67, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x53, 0x74, 0x72, 0x75, 0x63, 0x74, 0x52, 0x08, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x43, 0x66,
	0x67, 0x12, 0x37, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x18,
	0x0d, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x75, 0x63, 0x74, 0x52, 0x0a,
	0x73, 0x74, 0x61, 0x72, 0x74, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x12, 0x33, 0x0a, 0x08, 0x65, 0x6e,
	0x64, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53,
	0x74, 0x72, 0x75, 0x63, 0x74, 0x52, 0x08, 0x65, 0x6e, 0x64, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x22,
	0xcd, 0x04, 0x0a, 0x05, 0x43, 0x6f, 0x6d, 0x62, 0x6f, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x14, 0x0a,
	0x05, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6c, 0x61,
	0x62, 0x65, 0x6c, 0x12, 0x1a, 0x0a, 0x08, 0x69, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x69, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x0c, 0x0a, 0x01, 0x78, 0x18, 0x05, 0x20, 0x01, 0x28, 0x01, 0x52, 0x01, 0x78, 0x12, 0x0c, 0x0a,
	0x01, 0x79, 0x18, 0x06, 0x20, 0x01, 0x28, 0x01, 0x52, 0x01, 0x79, 0x12, 0x3e, 0x0a, 0x0c, 0x61,
	0x6e, 0x63, 0x68, 0x6f, 0x72, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x0c, 0x61,
	0x6e, 0x63, 0x68, 0x6f, 0x72, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x69,
	0x63, 0x6f, 0x6e, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x69, 0x63, 0x6f, 0x6e, 0x12,
	0x2d, 0x0a, 0x05, 0x73, 0x74, 0x79, 0x6c, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x53, 0x74, 0x72, 0x75, 0x63, 0x74, 0x52, 0x05, 0x73, 0x74, 0x79, 0x6c, 0x65, 0x12, 0x33,
	0x0a, 0x08, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x43, 0x66, 0x67, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x17, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x75, 0x63, 0x74, 0x52, 0x08, 0x6c, 0x61, 0x62, 0x65, 0x6c,
	0x43, 0x66, 0x67, 0x12, 0x14, 0x0a, 0x05, 0x64, 0x65, 0x70, 0x74, 0x68, 0x18, 0x0b, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x05, 0x64, 0x65, 0x70, 0x74, 0x68, 0x12, 0x18, 0x0a, 0x07, 0x70, 0x61, 0x64,
	0x64, 0x69, 0x6e, 0x67, 0x18, 0x0c, 0x20, 0x03, 0x28, 0x01, 0x52, 0x07, 0x70, 0x61, 0x64, 0x64,
	0x69, 0x6e, 0x67, 0x12, 0x1c, 0x0a, 0x09, 0x63, 0x6f, 0x6c, 0x6c, 0x61, 0x70, 0x73, 0x65, 0x64,
	0x18, 0x0d, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x63, 0x6f, 0x6c, 0x6c, 0x61, 0x70, 0x73, 0x65,
	0x64, 0x12, 0x35, 0x0a, 0x08, 0x63, 0x68, 0x69, 0x6c, 0x64, 0x72, 0x65, 0x6e, 0x18, 0x0e, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x43, 0x6f,
	0x6d, 0x62, 0x6f, 0x2e, 0x43, 0x6f, 0x6d, 0x62, 0x6f, 0x43, 0x68, 0x69, 0x6c, 0x64, 0x52, 0x08,
	0x63, 0x68, 0x69, 0x6c, 0x64, 0x72, 0x65, 0x6e, 0x12, 0x2b, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61,
	0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x75, 0x63, 0x74, 0x52,
	0x04, 0x64, 0x61, 0x74, 0x61, 0x1a, 0x68, 0x0a, 0x0a, 0x43, 0x6f, 0x6d, 0x62, 0x6f, 0x43, 0x68,
	0x69, 0x6c, 0x64, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x02, 0x69, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x6d, 0x62, 0x6f, 0x49, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6f, 0x6d, 0x62, 0x6f, 0x49, 0x64, 0x12, 0x1a, 0x0a,
	0x08, 0x69, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x69, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x64, 0x65, 0x70,
	0x74, 0x68, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x64, 0x65, 0x70, 0x74, 0x68, 0x22,
	0x1f, 0x0a, 0x09, 0x4c, 0x69, 0x73, 0x74, 0x46, 0x6c, 0x6f, 0x61, 0x74, 0x12, 0x12, 0x0a, 0x04,
	0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x02, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74,
	0x42, 0x41, 0x5a, 0x3f, 0x67, 0x69, 0x74, 0x6c, 0x61, 0x62, 0x2e, 0x74, 0x74, 0x79, 0x75, 0x79,
	0x69, 0x6e, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x54, 0x65, 0x73, 0x74, 0x44, 0x65, 0x76, 0x65, 0x6c,
	0x6f, 0x70, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x70, 0x72, 0x6f, 0x62, 0x65, 0x2d, 0x62, 0x61, 0x63,
	0x6b, 0x65, 0x6e, 0x64, 0x2f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2f, 0x72, 0x70, 0x63,
	0x2f, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_manager_element_proto_rawDescOnce sync.Once
	file_manager_element_proto_rawDescData = file_manager_element_proto_rawDesc
)

func file_manager_element_proto_rawDescGZIP() []byte {
	file_manager_element_proto_rawDescOnce.Do(func() {
		file_manager_element_proto_rawDescData = protoimpl.X.CompressGZIP(file_manager_element_proto_rawDescData)
	})
	return file_manager_element_proto_rawDescData
}

var file_manager_element_proto_msgTypes = make([]protoimpl.MessageInfo, 5)
var file_manager_element_proto_goTypes = []any{
	(*Node)(nil),               // 0: manager.Node
	(*Edge)(nil),               // 1: manager.Edge
	(*Combo)(nil),              // 2: manager.Combo
	(*ListFloat)(nil),          // 3: manager.ListFloat
	(*Combo_ComboChild)(nil),   // 4: manager.Combo.ComboChild
	(*structpb.ListValue)(nil), // 5: google.protobuf.ListValue
	(*structpb.Struct)(nil),    // 6: google.protobuf.Struct
}
var file_manager_element_proto_depIdxs = []int32{
	5,  // 0: manager.Node.anchorPoints:type_name -> google.protobuf.ListValue
	6,  // 1: manager.Node.style:type_name -> google.protobuf.Struct
	6,  // 2: manager.Node.labelCfg:type_name -> google.protobuf.Struct
	6,  // 3: manager.Node.data:type_name -> google.protobuf.Struct
	6,  // 4: manager.Edge.attrs:type_name -> google.protobuf.Struct
	6,  // 5: manager.Edge.style:type_name -> google.protobuf.Struct
	6,  // 6: manager.Edge.labelCfg:type_name -> google.protobuf.Struct
	6,  // 7: manager.Edge.startPoint:type_name -> google.protobuf.Struct
	6,  // 8: manager.Edge.endPoint:type_name -> google.protobuf.Struct
	5,  // 9: manager.Combo.anchorPoints:type_name -> google.protobuf.ListValue
	6,  // 10: manager.Combo.style:type_name -> google.protobuf.Struct
	6,  // 11: manager.Combo.labelCfg:type_name -> google.protobuf.Struct
	4,  // 12: manager.Combo.children:type_name -> manager.Combo.ComboChild
	6,  // 13: manager.Combo.data:type_name -> google.protobuf.Struct
	14, // [14:14] is the sub-list for method output_type
	14, // [14:14] is the sub-list for method input_type
	14, // [14:14] is the sub-list for extension type_name
	14, // [14:14] is the sub-list for extension extendee
	0,  // [0:14] is the sub-list for field type_name
}

func init() { file_manager_element_proto_init() }
func file_manager_element_proto_init() {
	if File_manager_element_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_manager_element_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   5,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_manager_element_proto_goTypes,
		DependencyIndexes: file_manager_element_proto_depIdxs,
		MessageInfos:      file_manager_element_proto_msgTypes,
	}.Build()
	File_manager_element_proto = out.File
	file_manager_element_proto_rawDesc = nil
	file_manager_element_proto_goTypes = nil
	file_manager_element_proto_depIdxs = nil
}
