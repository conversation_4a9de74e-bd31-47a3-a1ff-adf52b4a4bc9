// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.1
// 	protoc        v5.29.2
// source: manager/project_device.proto

package pb

import (
	reflect "reflect"
	sync "sync"

	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"

	pb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
	pb1 "gitlab.ttyuyin.com/TestDevelopment/probe-backend/devicehub/rpc/pb"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type BindDevice struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Udid          string                 `protobuf:"bytes,1,opt,name=udid,proto3" json:"udid,omitempty"`                            // 设备编号
	Usage         pb.DeviceUsage         `protobuf:"varint,2,opt,name=usage,proto3,enum=common.DeviceUsage" json:"usage,omitempty"` // 设备用途
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BindDevice) Reset() {
	*x = BindDevice{}
	mi := &file_manager_project_device_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BindDevice) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BindDevice) ProtoMessage() {}

func (x *BindDevice) ProtoReflect() protoreflect.Message {
	mi := &file_manager_project_device_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BindDevice.ProtoReflect.Descriptor instead.
func (*BindDevice) Descriptor() ([]byte, []int) {
	return file_manager_project_device_proto_rawDescGZIP(), []int{0}
}

func (x *BindDevice) GetUdid() string {
	if x != nil {
		return x.Udid
	}
	return ""
}

func (x *BindDevice) GetUsage() pb.DeviceUsage {
	if x != nil {
		return x.Usage
	}
	return pb.DeviceUsage(0)
}

type DeviceRelationship struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Udid          string                 `protobuf:"bytes,1,opt,name=udid,proto3" json:"udid,omitempty"`    // 设备编号
	Count         int64                  `protobuf:"varint,2,opt,name=count,proto3" json:"count,omitempty"` // 关联的计划数量（包括：UI测试计划、稳定性测试计划）
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeviceRelationship) Reset() {
	*x = DeviceRelationship{}
	mi := &file_manager_project_device_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeviceRelationship) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeviceRelationship) ProtoMessage() {}

func (x *DeviceRelationship) ProtoReflect() protoreflect.Message {
	mi := &file_manager_project_device_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeviceRelationship.ProtoReflect.Descriptor instead.
func (*DeviceRelationship) Descriptor() ([]byte, []int) {
	return file_manager_project_device_proto_rawDescGZIP(), []int{1}
}

func (x *DeviceRelationship) GetUdid() string {
	if x != nil {
		return x.Udid
	}
	return ""
}

func (x *DeviceRelationship) GetCount() int64 {
	if x != nil {
		return x.Count
	}
	return 0
}

type ProjectDevice struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Device        *pb1.Device            `protobuf:"bytes,1,opt,name=device,proto3" json:"device,omitempty"`
	ProjectId     string                 `protobuf:"bytes,11,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"` // 项目ID
	Usage         pb.DeviceUsage         `protobuf:"varint,12,opt,name=usage,proto3,enum=common.DeviceUsage" json:"usage,omitempty"` // 设备用途
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ProjectDevice) Reset() {
	*x = ProjectDevice{}
	mi := &file_manager_project_device_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ProjectDevice) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProjectDevice) ProtoMessage() {}

func (x *ProjectDevice) ProtoReflect() protoreflect.Message {
	mi := &file_manager_project_device_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProjectDevice.ProtoReflect.Descriptor instead.
func (*ProjectDevice) Descriptor() ([]byte, []int) {
	return file_manager_project_device_proto_rawDescGZIP(), []int{2}
}

func (x *ProjectDevice) GetDevice() *pb1.Device {
	if x != nil {
		return x.Device
	}
	return nil
}

func (x *ProjectDevice) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *ProjectDevice) GetUsage() pb.DeviceUsage {
	if x != nil {
		return x.Usage
	}
	return pb.DeviceUsage(0)
}

type SearchProjectDeviceReferenceItem struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ProjectId     string                 `protobuf:"bytes,1,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`              // 项目ID
	Udid          string                 `protobuf:"bytes,2,opt,name=udid,proto3" json:"udid,omitempty"`                                         // 设备编号
	ReferenceType string                 `protobuf:"bytes,11,opt,name=reference_type,json=referenceType,proto3" json:"reference_type,omitempty"` // 引用对象类型（UI测试计划、稳定性测试计划）
	ReferenceId   string                 `protobuf:"bytes,12,opt,name=reference_id,json=referenceId,proto3" json:"reference_id,omitempty"`       // 引用对象ID
	CategoryId    string                 `protobuf:"bytes,13,opt,name=category_id,json=categoryId,proto3" json:"category_id,omitempty"`          // 引用对象分类ID
	Name          string                 `protobuf:"bytes,14,opt,name=name,proto3" json:"name,omitempty"`                                        // 引用对象名称
	Description   string                 `protobuf:"bytes,15,opt,name=description,proto3" json:"description,omitempty"`                          // 引用对象描述
	MaintainedBy  string                 `protobuf:"bytes,16,opt,name=maintained_by,json=maintainedBy,proto3" json:"maintained_by,omitempty"`    // 维护者
	CreatedBy     string                 `protobuf:"bytes,96,opt,name=created_by,json=createdBy,proto3" json:"created_by,omitempty"`             // 创建者
	UpdatedBy     string                 `protobuf:"bytes,97,opt,name=updated_by,json=updatedBy,proto3" json:"updated_by,omitempty"`             // 更新者
	CreatedAt     int64                  `protobuf:"varint,98,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`            // 创建时间
	UpdatedAt     int64                  `protobuf:"varint,99,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`            // 更新时间
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SearchProjectDeviceReferenceItem) Reset() {
	*x = SearchProjectDeviceReferenceItem{}
	mi := &file_manager_project_device_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SearchProjectDeviceReferenceItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchProjectDeviceReferenceItem) ProtoMessage() {}

func (x *SearchProjectDeviceReferenceItem) ProtoReflect() protoreflect.Message {
	mi := &file_manager_project_device_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchProjectDeviceReferenceItem.ProtoReflect.Descriptor instead.
func (*SearchProjectDeviceReferenceItem) Descriptor() ([]byte, []int) {
	return file_manager_project_device_proto_rawDescGZIP(), []int{3}
}

func (x *SearchProjectDeviceReferenceItem) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *SearchProjectDeviceReferenceItem) GetUdid() string {
	if x != nil {
		return x.Udid
	}
	return ""
}

func (x *SearchProjectDeviceReferenceItem) GetReferenceType() string {
	if x != nil {
		return x.ReferenceType
	}
	return ""
}

func (x *SearchProjectDeviceReferenceItem) GetReferenceId() string {
	if x != nil {
		return x.ReferenceId
	}
	return ""
}

func (x *SearchProjectDeviceReferenceItem) GetCategoryId() string {
	if x != nil {
		return x.CategoryId
	}
	return ""
}

func (x *SearchProjectDeviceReferenceItem) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *SearchProjectDeviceReferenceItem) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *SearchProjectDeviceReferenceItem) GetMaintainedBy() string {
	if x != nil {
		return x.MaintainedBy
	}
	return ""
}

func (x *SearchProjectDeviceReferenceItem) GetCreatedBy() string {
	if x != nil {
		return x.CreatedBy
	}
	return ""
}

func (x *SearchProjectDeviceReferenceItem) GetUpdatedBy() string {
	if x != nil {
		return x.UpdatedBy
	}
	return ""
}

func (x *SearchProjectDeviceReferenceItem) GetCreatedAt() int64 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

func (x *SearchProjectDeviceReferenceItem) GetUpdatedAt() int64 {
	if x != nil {
		return x.UpdatedAt
	}
	return 0
}

type DeleteDisabledDeviceTaskInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ProjectId     string                 `protobuf:"bytes,1,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"` // 项目ID
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteDisabledDeviceTaskInfo) Reset() {
	*x = DeleteDisabledDeviceTaskInfo{}
	mi := &file_manager_project_device_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteDisabledDeviceTaskInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteDisabledDeviceTaskInfo) ProtoMessage() {}

func (x *DeleteDisabledDeviceTaskInfo) ProtoReflect() protoreflect.Message {
	mi := &file_manager_project_device_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteDisabledDeviceTaskInfo.ProtoReflect.Descriptor instead.
func (*DeleteDisabledDeviceTaskInfo) Descriptor() ([]byte, []int) {
	return file_manager_project_device_proto_rawDescGZIP(), []int{4}
}

func (x *DeleteDisabledDeviceTaskInfo) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

var File_manager_project_device_proto protoreflect.FileDescriptor

var file_manager_project_device_proto_rawDesc = []byte{
	0x0a, 0x1c, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2f, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63,
	0x74, 0x5f, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x07,
	0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74,
	0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x11, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x16, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x68, 0x75, 0x62, 0x2f, 0x64,
	0x65, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x60, 0x0a, 0x0a, 0x42,
	0x69, 0x6e, 0x64, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x12, 0x1d, 0x0a, 0x04, 0x75, 0x64, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x01,
	0x18, 0x40, 0x52, 0x04, 0x75, 0x64, 0x69, 0x64, 0x12, 0x33, 0x0a, 0x05, 0x75, 0x73, 0x61, 0x67,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x13, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x55, 0x73, 0x61, 0x67, 0x65, 0x42, 0x08, 0xfa, 0x42,
	0x05, 0x82, 0x01, 0x02, 0x20, 0x00, 0x52, 0x05, 0x75, 0x73, 0x61, 0x67, 0x65, 0x22, 0x52, 0x0a,
	0x12, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x68, 0x69, 0x70, 0x12, 0x1d, 0x0a, 0x04, 0x75, 0x64, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x40, 0x52, 0x04, 0x75, 0x64,
	0x69, 0x64, 0x12, 0x1d, 0x0a, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x28, 0x00, 0x52, 0x05, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x22, 0x84, 0x01, 0x0a, 0x0d, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x44, 0x65, 0x76,
	0x69, 0x63, 0x65, 0x12, 0x29, 0x0a, 0x06, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x68, 0x75, 0x62, 0x2e,
	0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x52, 0x06, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x12, 0x1d,
	0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x0b, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x12, 0x29, 0x0a,
	0x05, 0x75, 0x73, 0x61, 0x67, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x13, 0x2e, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x55, 0x73, 0x61, 0x67,
	0x65, 0x52, 0x05, 0x75, 0x73, 0x61, 0x67, 0x65, 0x22, 0x97, 0x03, 0x0a, 0x20, 0x53, 0x65, 0x61,
	0x72, 0x63, 0x68, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65,
	0x52, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x1d, 0x0a,
	0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04,
	0x75, 0x64, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x75, 0x64, 0x69, 0x64,
	0x12, 0x25, 0x0a, 0x0e, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65,
	0x6e, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x72, 0x65, 0x66, 0x65, 0x72,
	0x65, 0x6e, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x72,
	0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x61,
	0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0a, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12,
	0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0f,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x12, 0x23, 0x0a, 0x0d, 0x6d, 0x61, 0x69, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x64, 0x5f,
	0x62, 0x79, 0x18, 0x10, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x6d, 0x61, 0x69, 0x6e, 0x74, 0x61,
	0x69, 0x6e, 0x65, 0x64, 0x42, 0x79, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x64, 0x5f, 0x62, 0x79, 0x18, 0x60, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x64, 0x42, 0x79, 0x12, 0x1d, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64,
	0x5f, 0x62, 0x79, 0x18, 0x61, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x64, 0x42, 0x79, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f,
	0x61, 0x74, 0x18, 0x62, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x64, 0x41, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61,
	0x74, 0x18, 0x63, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64,
	0x41, 0x74, 0x22, 0x5d, 0x0a, 0x1c, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x44, 0x69, 0x73, 0x61,
	0x62, 0x6c, 0x65, 0x64, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x49, 0x6e,
	0x66, 0x6f, 0x12, 0x3d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x1e, 0xfa, 0x42, 0x1b, 0x72, 0x19, 0x32, 0x17, 0x28,
	0x3f, 0x3a, 0x5e, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x3a, 0x2e, 0x2b,
	0x3f, 0x7c, 0x5e, 0x31, 0x24, 0x29, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49,
	0x64, 0x42, 0x41, 0x5a, 0x3f, 0x67, 0x69, 0x74, 0x6c, 0x61, 0x62, 0x2e, 0x74, 0x74, 0x79, 0x75,
	0x79, 0x69, 0x6e, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x54, 0x65, 0x73, 0x74, 0x44, 0x65, 0x76, 0x65,
	0x6c, 0x6f, 0x70, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x70, 0x72, 0x6f, 0x62, 0x65, 0x2d, 0x62, 0x61,
	0x63, 0x6b, 0x65, 0x6e, 0x64, 0x2f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2f, 0x72, 0x70,
	0x63, 0x2f, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_manager_project_device_proto_rawDescOnce sync.Once
	file_manager_project_device_proto_rawDescData = file_manager_project_device_proto_rawDesc
)

func file_manager_project_device_proto_rawDescGZIP() []byte {
	file_manager_project_device_proto_rawDescOnce.Do(func() {
		file_manager_project_device_proto_rawDescData = protoimpl.X.CompressGZIP(file_manager_project_device_proto_rawDescData)
	})
	return file_manager_project_device_proto_rawDescData
}

var file_manager_project_device_proto_msgTypes = make([]protoimpl.MessageInfo, 5)
var file_manager_project_device_proto_goTypes = []any{
	(*BindDevice)(nil),                       // 0: manager.BindDevice
	(*DeviceRelationship)(nil),               // 1: manager.DeviceRelationship
	(*ProjectDevice)(nil),                    // 2: manager.ProjectDevice
	(*SearchProjectDeviceReferenceItem)(nil), // 3: manager.SearchProjectDeviceReferenceItem
	(*DeleteDisabledDeviceTaskInfo)(nil),     // 4: manager.DeleteDisabledDeviceTaskInfo
	(pb.DeviceUsage)(0),                      // 5: common.DeviceUsage
	(*pb1.Device)(nil),                       // 6: devicehub.Device
}
var file_manager_project_device_proto_depIdxs = []int32{
	5, // 0: manager.BindDevice.usage:type_name -> common.DeviceUsage
	6, // 1: manager.ProjectDevice.device:type_name -> devicehub.Device
	5, // 2: manager.ProjectDevice.usage:type_name -> common.DeviceUsage
	3, // [3:3] is the sub-list for method output_type
	3, // [3:3] is the sub-list for method input_type
	3, // [3:3] is the sub-list for extension type_name
	3, // [3:3] is the sub-list for extension extendee
	0, // [0:3] is the sub-list for field type_name
}

func init() { file_manager_project_device_proto_init() }
func file_manager_project_device_proto_init() {
	if File_manager_project_device_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_manager_project_device_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   5,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_manager_project_device_proto_goTypes,
		DependencyIndexes: file_manager_project_device_proto_depIdxs,
		MessageInfos:      file_manager_project_device_proto_msgTypes,
	}.Build()
	File_manager_project_device_proto = out.File
	file_manager_project_device_proto_rawDesc = nil
	file_manager_project_device_proto_goTypes = nil
	file_manager_project_device_proto_depIdxs = nil
}
