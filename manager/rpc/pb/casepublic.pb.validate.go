// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: manager/casepublic.proto

package pb

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on Case with the rules defined in the proto
// definition for this message. If any rules are violated, the first error
// encountered is returned, or nil if there are no violations.
func (m *Case) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Case with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in CaseMultiError, or nil if none found.
func (m *Case) ValidateAll() error {
	return m.validate(true)
}

func (m *Case) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ProjectId

	// no validation rules for BranchId

	// no validation rules for CaseId

	// no validation rules for Name

	// no validation rules for Description

	// no validation rules for Priority

	// no validation rules for State

	if all {
		switch v := interface{}(m.GetAccountConfig()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CaseValidationError{
					field:  "AccountConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CaseValidationError{
					field:  "AccountConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAccountConfig()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CaseValidationError{
				field:  "AccountConfig",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Version

	// no validation rules for MaintainedBy

	// no validation rules for CreatedBy

	// no validation rules for UpdatedBy

	// no validation rules for CreatedAt

	// no validation rules for UpdatedAt

	for idx, item := range m.GetNodes() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CaseValidationError{
						field:  fmt.Sprintf("Nodes[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CaseValidationError{
						field:  fmt.Sprintf("Nodes[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CaseValidationError{
					field:  fmt.Sprintf("Nodes[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetEdges() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CaseValidationError{
						field:  fmt.Sprintf("Edges[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CaseValidationError{
						field:  fmt.Sprintf("Edges[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CaseValidationError{
					field:  fmt.Sprintf("Edges[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetCombos() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CaseValidationError{
						field:  fmt.Sprintf("Combos[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CaseValidationError{
						field:  fmt.Sprintf("Combos[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CaseValidationError{
					field:  fmt.Sprintf("Combos[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for CaseType

	if len(errors) > 0 {
		return CaseMultiError(errors)
	}

	return nil
}

// CaseMultiError is an error wrapping multiple validation errors returned by
// Case.ValidateAll() if the designated constraints aren't met.
type CaseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CaseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CaseMultiError) AllErrors() []error { return m }

// CaseValidationError is the validation error returned by Case.Validate if the
// designated constraints aren't met.
type CaseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CaseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CaseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CaseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CaseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CaseValidationError) ErrorName() string { return "CaseValidationError" }

// Error satisfies the builtin error interface
func (e CaseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCase.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CaseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CaseValidationError{}

// Validate checks the field values on FailCase with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *FailCase) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FailCase with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in FailCaseMultiError, or nil
// if none found.
func (m *FailCase) ValidateAll() error {
	return m.validate(true)
}

func (m *FailCase) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ProjectId

	// no validation rules for BranchId

	// no validation rules for CaseId

	// no validation rules for Name

	// no validation rules for Description

	// no validation rules for Priority

	// no validation rules for State

	if all {
		switch v := interface{}(m.GetAccountConfig()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FailCaseValidationError{
					field:  "AccountConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FailCaseValidationError{
					field:  "AccountConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAccountConfig()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FailCaseValidationError{
				field:  "AccountConfig",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Version

	// no validation rules for MaintainedBy

	// no validation rules for CreatedBy

	// no validation rules for UpdatedBy

	// no validation rules for CreatedAt

	// no validation rules for UpdatedAt

	for idx, item := range m.GetNodes() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, FailCaseValidationError{
						field:  fmt.Sprintf("Nodes[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, FailCaseValidationError{
						field:  fmt.Sprintf("Nodes[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return FailCaseValidationError{
					field:  fmt.Sprintf("Nodes[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetEdges() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, FailCaseValidationError{
						field:  fmt.Sprintf("Edges[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, FailCaseValidationError{
						field:  fmt.Sprintf("Edges[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return FailCaseValidationError{
					field:  fmt.Sprintf("Edges[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetCombos() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, FailCaseValidationError{
						field:  fmt.Sprintf("Combos[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, FailCaseValidationError{
						field:  fmt.Sprintf("Combos[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return FailCaseValidationError{
					field:  fmt.Sprintf("Combos[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for CaseType

	// no validation rules for FailCount

	// no validation rules for Rank

	if len(errors) > 0 {
		return FailCaseMultiError(errors)
	}

	return nil
}

// FailCaseMultiError is an error wrapping multiple validation errors returned
// by FailCase.ValidateAll() if the designated constraints aren't met.
type FailCaseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FailCaseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FailCaseMultiError) AllErrors() []error { return m }

// FailCaseValidationError is the validation error returned by
// FailCase.Validate if the designated constraints aren't met.
type FailCaseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FailCaseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FailCaseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FailCaseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FailCaseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FailCaseValidationError) ErrorName() string { return "FailCaseValidationError" }

// Error satisfies the builtin error interface
func (e FailCaseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFailCase.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FailCaseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FailCaseValidationError{}

// Validate checks the field values on CaseFailStatForMq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *CaseFailStatForMq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CaseFailStatForMq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CaseFailStatForMqMultiError, or nil if none found.
func (m *CaseFailStatForMq) ValidateAll() error {
	return m.validate(true)
}

func (m *CaseFailStatForMq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ProjectId

	// no validation rules for BranchId

	// no validation rules for ExecuteId

	// no validation rules for CaseId

	// no validation rules for CaseType

	if len(errors) > 0 {
		return CaseFailStatForMqMultiError(errors)
	}

	return nil
}

// CaseFailStatForMqMultiError is an error wrapping multiple validation errors
// returned by CaseFailStatForMq.ValidateAll() if the designated constraints
// aren't met.
type CaseFailStatForMqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CaseFailStatForMqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CaseFailStatForMqMultiError) AllErrors() []error { return m }

// CaseFailStatForMqValidationError is the validation error returned by
// CaseFailStatForMq.Validate if the designated constraints aren't met.
type CaseFailStatForMqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CaseFailStatForMqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CaseFailStatForMqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CaseFailStatForMqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CaseFailStatForMqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CaseFailStatForMqValidationError) ErrorName() string {
	return "CaseFailStatForMqValidationError"
}

// Error satisfies the builtin error interface
func (e CaseFailStatForMqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCaseFailStatForMq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CaseFailStatForMqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CaseFailStatForMqValidationError{}
