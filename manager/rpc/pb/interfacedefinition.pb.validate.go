// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: manager/interfacedefinition.proto

package pb

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on EnumValDesc with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *EnumValDesc) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on EnumValDesc with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in EnumValDescMultiError, or
// nil if none found.
func (m *EnumValDesc) ValidateAll() error {
	return m.validate(true)
}

func (m *EnumValDesc) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetValue()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, EnumValDescValidationError{
					field:  "Value",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, EnumValDescValidationError{
					field:  "Value",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetValue()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return EnumValDescValidationError{
				field:  "Value",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Description

	if len(errors) > 0 {
		return EnumValDescMultiError(errors)
	}

	return nil
}

// EnumValDescMultiError is an error wrapping multiple validation errors
// returned by EnumValDesc.ValidateAll() if the designated constraints aren't met.
type EnumValDescMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m EnumValDescMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m EnumValDescMultiError) AllErrors() []error { return m }

// EnumValDescValidationError is the validation error returned by
// EnumValDesc.Validate if the designated constraints aren't met.
type EnumValDescValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e EnumValDescValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e EnumValDescValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e EnumValDescValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e EnumValDescValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e EnumValDescValidationError) ErrorName() string { return "EnumValDescValidationError" }

// Error satisfies the builtin error interface
func (e EnumValDescValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sEnumValDesc.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = EnumValDescValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = EnumValDescValidationError{}

// Validate checks the field values on RefSchema with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *RefSchema) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RefSchema with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in RefSchemaMultiError, or nil
// if none found.
func (m *RefSchema) ValidateAll() error {
	return m.validate(true)
}

func (m *RefSchema) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SchemaId

	// no validation rules for FullName

	// no validation rules for DisplayName

	for idx, item := range m.GetCategoryPath() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, RefSchemaValidationError{
						field:  fmt.Sprintf("CategoryPath[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, RefSchemaValidationError{
						field:  fmt.Sprintf("CategoryPath[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return RefSchemaValidationError{
					field:  fmt.Sprintf("CategoryPath[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return RefSchemaMultiError(errors)
	}

	return nil
}

// RefSchemaMultiError is an error wrapping multiple validation errors returned
// by RefSchema.ValidateAll() if the designated constraints aren't met.
type RefSchemaMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RefSchemaMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RefSchemaMultiError) AllErrors() []error { return m }

// RefSchemaValidationError is the validation error returned by
// RefSchema.Validate if the designated constraints aren't met.
type RefSchemaValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RefSchemaValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RefSchemaValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RefSchemaValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RefSchemaValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RefSchemaValidationError) ErrorName() string { return "RefSchemaValidationError" }

// Error satisfies the builtin error interface
func (e RefSchemaValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRefSchema.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RefSchemaValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RefSchemaValidationError{}

// Validate checks the field values on TextDescExample with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *TextDescExample) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on TextDescExample with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// TextDescExampleMultiError, or nil if none found.
func (m *TextDescExample) ValidateAll() error {
	return m.validate(true)
}

func (m *TextDescExample) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Description

	// no validation rules for Example

	if len(errors) > 0 {
		return TextDescExampleMultiError(errors)
	}

	return nil
}

// TextDescExampleMultiError is an error wrapping multiple validation errors
// returned by TextDescExample.ValidateAll() if the designated constraints
// aren't met.
type TextDescExampleMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TextDescExampleMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TextDescExampleMultiError) AllErrors() []error { return m }

// TextDescExampleValidationError is the validation error returned by
// TextDescExample.Validate if the designated constraints aren't met.
type TextDescExampleValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TextDescExampleValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TextDescExampleValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TextDescExampleValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TextDescExampleValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TextDescExampleValidationError) ErrorName() string { return "TextDescExampleValidationError" }

// Error satisfies the builtin error interface
func (e TextDescExampleValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTextDescExample.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TextDescExampleValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TextDescExampleValidationError{}

// Validate checks the field values on BodyData with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *BodyData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BodyData with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in BodyDataMultiError, or nil
// if none found.
func (m *BodyData) ValidateAll() error {
	return m.validate(true)
}

func (m *BodyData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if _, ok := _BodyData_Type_InLookup[m.GetType()]; !ok {
		err := BodyDataValidationError{
			field:  "Type",
			reason: "value must be in list [none multipart/form-data application/x-www-form-urlencoded application/json text/plain]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	for idx, item := range m.GetForm() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, BodyDataValidationError{
						field:  fmt.Sprintf("Form[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, BodyDataValidationError{
						field:  fmt.Sprintf("Form[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return BodyDataValidationError{
					field:  fmt.Sprintf("Form[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetJson()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, BodyDataValidationError{
					field:  "Json",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, BodyDataValidationError{
					field:  "Json",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetJson()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return BodyDataValidationError{
				field:  "Json",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetText()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, BodyDataValidationError{
					field:  "Text",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, BodyDataValidationError{
					field:  "Text",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetText()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return BodyDataValidationError{
				field:  "Text",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return BodyDataMultiError(errors)
	}

	return nil
}

// BodyDataMultiError is an error wrapping multiple validation errors returned
// by BodyData.ValidateAll() if the designated constraints aren't met.
type BodyDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BodyDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BodyDataMultiError) AllErrors() []error { return m }

// BodyDataValidationError is the validation error returned by
// BodyData.Validate if the designated constraints aren't met.
type BodyDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BodyDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BodyDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BodyDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BodyDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BodyDataValidationError) ErrorName() string { return "BodyDataValidationError" }

// Error satisfies the builtin error interface
func (e BodyDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBodyData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BodyDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BodyDataValidationError{}

var _BodyData_Type_InLookup = map[string]struct{}{
	"none":                              {},
	"multipart/form-data":               {},
	"application/x-www-form-urlencoded": {},
	"application/json":                  {},
	"text/plain":                        {},
}

// Validate checks the field values on ResponseData with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ResponseData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ResponseData with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ResponseDataMultiError, or
// nil if none found.
func (m *ResponseData) ValidateAll() error {
	return m.validate(true)
}

func (m *ResponseData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for StatusCode

	// no validation rules for Description

	for idx, item := range m.GetHeaders() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ResponseDataValidationError{
						field:  fmt.Sprintf("Headers[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ResponseDataValidationError{
						field:  fmt.Sprintf("Headers[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ResponseDataValidationError{
					field:  fmt.Sprintf("Headers[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetBody()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ResponseDataValidationError{
					field:  "Body",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ResponseDataValidationError{
					field:  "Body",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBody()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ResponseDataValidationError{
				field:  "Body",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ResponseDataMultiError(errors)
	}

	return nil
}

// ResponseDataMultiError is an error wrapping multiple validation errors
// returned by ResponseData.ValidateAll() if the designated constraints aren't met.
type ResponseDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ResponseDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ResponseDataMultiError) AllErrors() []error { return m }

// ResponseDataValidationError is the validation error returned by
// ResponseData.Validate if the designated constraints aren't met.
type ResponseDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ResponseDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ResponseDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ResponseDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ResponseDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ResponseDataValidationError) ErrorName() string { return "ResponseDataValidationError" }

// Error satisfies the builtin error interface
func (e ResponseDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sResponseData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ResponseDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ResponseDataValidationError{}

// Validate checks the field values on Schema with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Schema) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Schema with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in SchemaMultiError, or nil if none found.
func (m *Schema) ValidateAll() error {
	return m.validate(true)
}

func (m *Schema) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetTitle() != "" {

	}

	if _, ok := _Schema_Type_InLookup[m.GetType()]; !ok {
		err := SchemaValidationError{
			field:  "Type",
			reason: "value must be in list [string integer number boolean array object null any allOf anyOf oneOf custom schema file]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for Description

	// no validation rules for Index

	// no validation rules for FieldRequired

	// no validation rules for Deprecated

	if all {
		switch v := interface{}(m.GetDefault()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SchemaValidationError{
					field:  "Default",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SchemaValidationError{
					field:  "Default",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDefault()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SchemaValidationError{
				field:  "Default",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetExample()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SchemaValidationError{
					field:  "Example",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SchemaValidationError{
					field:  "Example",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetExample()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SchemaValidationError{
				field:  "Example",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if m.GetFormat() != "" {

		if _, ok := _Schema_Format_InLookup[m.GetFormat()]; !ok {
			err := SchemaValidationError{
				field:  "Format",
				reason: "value must be in list [int32 int64 float double password date-time date time duration email hostname ipv4 ipv6 uri regex]",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	// no validation rules for MultipleOf

	// no validation rules for Maximum

	// no validation rules for ExclusiveMaximum

	// no validation rules for Minimum

	// no validation rules for ExclusiveMinimum

	// no validation rules for MaxLength

	// no validation rules for MinLength

	// no validation rules for Pattern

	// no validation rules for MaxItems

	// no validation rules for MinItems

	// no validation rules for UniqueItems

	for idx, item := range m.GetEnum() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SchemaValidationError{
						field:  fmt.Sprintf("Enum[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SchemaValidationError{
						field:  fmt.Sprintf("Enum[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SchemaValidationError{
					field:  fmt.Sprintf("Enum[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetEnums() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SchemaValidationError{
						field:  fmt.Sprintf("Enums[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SchemaValidationError{
						field:  fmt.Sprintf("Enums[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SchemaValidationError{
					field:  fmt.Sprintf("Enums[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetItems()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SchemaValidationError{
					field:  "Items",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SchemaValidationError{
					field:  "Items",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetItems()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SchemaValidationError{
				field:  "Items",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetRef()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SchemaValidationError{
					field:  "Ref",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SchemaValidationError{
					field:  "Ref",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRef()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SchemaValidationError{
				field:  "Ref",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	{
		sorted_keys := make([]string, len(m.GetProperties()))
		i := 0
		for key := range m.GetProperties() {
			sorted_keys[i] = key
			i++
		}
		sort.Slice(sorted_keys, func(i, j int) bool { return sorted_keys[i] < sorted_keys[j] })
		for _, key := range sorted_keys {
			val := m.GetProperties()[key]
			_ = val

			// no validation rules for Properties[key]

			if all {
				switch v := interface{}(val).(type) {
				case interface{ ValidateAll() error }:
					if err := v.ValidateAll(); err != nil {
						errors = append(errors, SchemaValidationError{
							field:  fmt.Sprintf("Properties[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				case interface{ Validate() error }:
					if err := v.Validate(); err != nil {
						errors = append(errors, SchemaValidationError{
							field:  fmt.Sprintf("Properties[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				}
			} else if v, ok := interface{}(val).(interface{ Validate() error }); ok {
				if err := v.Validate(); err != nil {
					return SchemaValidationError{
						field:  fmt.Sprintf("Properties[%v]", key),
						reason: "embedded message failed validation",
						cause:  err,
					}
				}
			}

		}
	}

	// no validation rules for SortKey

	// no validation rules for Raw

	if len(errors) > 0 {
		return SchemaMultiError(errors)
	}

	return nil
}

// SchemaMultiError is an error wrapping multiple validation errors returned by
// Schema.ValidateAll() if the designated constraints aren't met.
type SchemaMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SchemaMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SchemaMultiError) AllErrors() []error { return m }

// SchemaValidationError is the validation error returned by Schema.Validate if
// the designated constraints aren't met.
type SchemaValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SchemaValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SchemaValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SchemaValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SchemaValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SchemaValidationError) ErrorName() string { return "SchemaValidationError" }

// Error satisfies the builtin error interface
func (e SchemaValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSchema.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SchemaValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SchemaValidationError{}

var _Schema_Type_InLookup = map[string]struct{}{
	"string":  {},
	"integer": {},
	"number":  {},
	"boolean": {},
	"array":   {},
	"object":  {},
	"null":    {},
	"any":     {},
	"allOf":   {},
	"anyOf":   {},
	"oneOf":   {},
	"custom":  {},
	"schema":  {},
	"file":    {},
}

var _Schema_Format_InLookup = map[string]struct{}{
	"int32":     {},
	"int64":     {},
	"float":     {},
	"double":    {},
	"password":  {},
	"date-time": {},
	"date":      {},
	"time":      {},
	"duration":  {},
	"email":     {},
	"hostname":  {},
	"ipv4":      {},
	"ipv6":      {},
	"uri":       {},
	"regex":     {},
}

// Validate checks the field values on InterfaceSchema with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *InterfaceSchema) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on InterfaceSchema with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// InterfaceSchemaMultiError, or nil if none found.
func (m *InterfaceSchema) ValidateAll() error {
	return m.validate(true)
}

func (m *InterfaceSchema) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ProjectId

	// no validation rules for CategoryId

	// no validation rules for SchemaId

	// no validation rules for FullName

	// no validation rules for Name

	// no validation rules for Description

	// no validation rules for Mode

	// no validation rules for ImportType

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InterfaceSchemaValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InterfaceSchemaValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InterfaceSchemaValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for CreatedBy

	// no validation rules for UpdatedBy

	// no validation rules for CreatedAt

	// no validation rules for UpdatedAt

	if len(errors) > 0 {
		return InterfaceSchemaMultiError(errors)
	}

	return nil
}

// InterfaceSchemaMultiError is an error wrapping multiple validation errors
// returned by InterfaceSchema.ValidateAll() if the designated constraints
// aren't met.
type InterfaceSchemaMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m InterfaceSchemaMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m InterfaceSchemaMultiError) AllErrors() []error { return m }

// InterfaceSchemaValidationError is the validation error returned by
// InterfaceSchema.Validate if the designated constraints aren't met.
type InterfaceSchemaValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e InterfaceSchemaValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e InterfaceSchemaValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e InterfaceSchemaValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e InterfaceSchemaValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e InterfaceSchemaValidationError) ErrorName() string { return "InterfaceSchemaValidationError" }

// Error satisfies the builtin error interface
func (e InterfaceSchemaValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sInterfaceSchema.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = InterfaceSchemaValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = InterfaceSchemaValidationError{}

// Validate checks the field values on Document with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Document) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Document with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in DocumentMultiError, or nil
// if none found.
func (m *Document) ValidateAll() error {
	return m.validate(true)
}

func (m *Document) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetHeaders() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, DocumentValidationError{
						field:  fmt.Sprintf("Headers[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, DocumentValidationError{
						field:  fmt.Sprintf("Headers[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return DocumentValidationError{
					field:  fmt.Sprintf("Headers[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetPathParams() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, DocumentValidationError{
						field:  fmt.Sprintf("PathParams[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, DocumentValidationError{
						field:  fmt.Sprintf("PathParams[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return DocumentValidationError{
					field:  fmt.Sprintf("PathParams[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetQueryParams() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, DocumentValidationError{
						field:  fmt.Sprintf("QueryParams[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, DocumentValidationError{
						field:  fmt.Sprintf("QueryParams[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return DocumentValidationError{
					field:  fmt.Sprintf("QueryParams[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetBody()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DocumentValidationError{
					field:  "Body",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DocumentValidationError{
					field:  "Body",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBody()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DocumentValidationError{
				field:  "Body",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	{
		sorted_keys := make([]string, len(m.GetResponses()))
		i := 0
		for key := range m.GetResponses() {
			sorted_keys[i] = key
			i++
		}
		sort.Slice(sorted_keys, func(i, j int) bool { return sorted_keys[i] < sorted_keys[j] })
		for _, key := range sorted_keys {
			val := m.GetResponses()[key]
			_ = val

			// no validation rules for Responses[key]

			if all {
				switch v := interface{}(val).(type) {
				case interface{ ValidateAll() error }:
					if err := v.ValidateAll(); err != nil {
						errors = append(errors, DocumentValidationError{
							field:  fmt.Sprintf("Responses[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				case interface{ Validate() error }:
					if err := v.Validate(); err != nil {
						errors = append(errors, DocumentValidationError{
							field:  fmt.Sprintf("Responses[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				}
			} else if v, ok := interface{}(val).(interface{ Validate() error }); ok {
				if err := v.Validate(); err != nil {
					return DocumentValidationError{
						field:  fmt.Sprintf("Responses[%v]", key),
						reason: "embedded message failed validation",
						cause:  err,
					}
				}
			}

		}
	}

	if len(errors) > 0 {
		return DocumentMultiError(errors)
	}

	return nil
}

// DocumentMultiError is an error wrapping multiple validation errors returned
// by Document.ValidateAll() if the designated constraints aren't met.
type DocumentMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DocumentMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DocumentMultiError) AllErrors() []error { return m }

// DocumentValidationError is the validation error returned by
// Document.Validate if the designated constraints aren't met.
type DocumentValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DocumentValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DocumentValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DocumentValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DocumentValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DocumentValidationError) ErrorName() string { return "DocumentValidationError" }

// Error satisfies the builtin error interface
func (e DocumentValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDocument.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DocumentValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DocumentValidationError{}

// Validate checks the field values on InterfaceDocument with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *InterfaceDocument) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on InterfaceDocument with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// InterfaceDocumentMultiError, or nil if none found.
func (m *InterfaceDocument) ValidateAll() error {
	return m.validate(true)
}

func (m *InterfaceDocument) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ProjectId

	// no validation rules for CategoryId

	// no validation rules for DocumentId

	// no validation rules for Name

	// no validation rules for Description

	// no validation rules for Type

	// no validation rules for Mode

	// no validation rules for ImportType

	// no validation rules for Status

	// no validation rules for Priority

	// no validation rules for State

	// no validation rules for CaseExecutionMode

	// no validation rules for Service

	// no validation rules for Path

	// no validation rules for Method

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InterfaceDocumentValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InterfaceDocumentValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InterfaceDocumentValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for MaintainedBy

	// no validation rules for CreatedBy

	// no validation rules for UpdatedBy

	// no validation rules for CreatedAt

	// no validation rules for UpdatedAt

	if len(errors) > 0 {
		return InterfaceDocumentMultiError(errors)
	}

	return nil
}

// InterfaceDocumentMultiError is an error wrapping multiple validation errors
// returned by InterfaceDocument.ValidateAll() if the designated constraints
// aren't met.
type InterfaceDocumentMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m InterfaceDocumentMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m InterfaceDocumentMultiError) AllErrors() []error { return m }

// InterfaceDocumentValidationError is the validation error returned by
// InterfaceDocument.Validate if the designated constraints aren't met.
type InterfaceDocumentValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e InterfaceDocumentValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e InterfaceDocumentValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e InterfaceDocumentValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e InterfaceDocumentValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e InterfaceDocumentValidationError) ErrorName() string {
	return "InterfaceDocumentValidationError"
}

// Error satisfies the builtin error interface
func (e InterfaceDocumentValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sInterfaceDocument.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = InterfaceDocumentValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = InterfaceDocumentValidationError{}

// Validate checks the field values on InputParameter with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *InputParameter) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on InputParameter with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in InputParameterMultiError,
// or nil if none found.
func (m *InputParameter) ValidateAll() error {
	return m.validate(true)
}

func (m *InputParameter) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetName() != "" {

		if utf8.RuneCountInString(m.GetName()) < 1 {
			err := InputParameterValidationError{
				field:  "Name",
				reason: "value length must be at least 1 runes",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	// no validation rules for Description

	if _, ok := VariableSource_name[int32(m.GetSource())]; !ok {
		err := InputParameterValidationError{
			field:  "Source",
			reason: "value must be one of the defined enum values",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetManual() == nil {
		err := InputParameterValidationError{
			field:  "Manual",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetManual()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InputParameterValidationError{
					field:  "Manual",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InputParameterValidationError{
					field:  "Manual",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetManual()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InputParameterValidationError{
				field:  "Manual",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if m.GetExport() == nil {
		err := InputParameterValidationError{
			field:  "Export",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetExport()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InputParameterValidationError{
					field:  "Export",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InputParameterValidationError{
					field:  "Export",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetExport()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InputParameterValidationError{
				field:  "Export",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if m.GetEnvironment() == nil {
		err := InputParameterValidationError{
			field:  "Environment",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetEnvironment()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InputParameterValidationError{
					field:  "Environment",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InputParameterValidationError{
					field:  "Environment",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetEnvironment()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InputParameterValidationError{
				field:  "Environment",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if m.GetFunction() == nil {
		err := InputParameterValidationError{
			field:  "Function",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetFunction()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InputParameterValidationError{
					field:  "Function",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InputParameterValidationError{
					field:  "Function",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFunction()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InputParameterValidationError{
				field:  "Function",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return InputParameterMultiError(errors)
	}

	return nil
}

// InputParameterMultiError is an error wrapping multiple validation errors
// returned by InputParameter.ValidateAll() if the designated constraints
// aren't met.
type InputParameterMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m InputParameterMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m InputParameterMultiError) AllErrors() []error { return m }

// InputParameterValidationError is the validation error returned by
// InputParameter.Validate if the designated constraints aren't met.
type InputParameterValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e InputParameterValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e InputParameterValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e InputParameterValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e InputParameterValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e InputParameterValidationError) ErrorName() string { return "InputParameterValidationError" }

// Error satisfies the builtin error interface
func (e InputParameterValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sInputParameter.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = InputParameterValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = InputParameterValidationError{}

// Validate checks the field values on OutputParameter with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *OutputParameter) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on OutputParameter with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// OutputParameterMultiError, or nil if none found.
func (m *OutputParameter) ValidateAll() error {
	return m.validate(true)
}

func (m *OutputParameter) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetName() != "" {

		if utf8.RuneCountInString(m.GetName()) < 1 {
			err := OutputParameterValidationError{
				field:  "Name",
				reason: "value length must be at least 1 runes",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if _, ok := ResponseSource_name[int32(m.GetSource())]; !ok {
		err := OutputParameterValidationError{
			field:  "Source",
			reason: "value must be one of the defined enum values",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetHeaders() == nil {
		err := OutputParameterValidationError{
			field:  "Headers",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetHeaders()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, OutputParameterValidationError{
					field:  "Headers",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, OutputParameterValidationError{
					field:  "Headers",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeaders()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return OutputParameterValidationError{
				field:  "Headers",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if m.GetBody() == nil {
		err := OutputParameterValidationError{
			field:  "Body",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetBody()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, OutputParameterValidationError{
					field:  "Body",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, OutputParameterValidationError{
					field:  "Body",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBody()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return OutputParameterValidationError{
				field:  "Body",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return OutputParameterMultiError(errors)
	}

	return nil
}

// OutputParameterMultiError is an error wrapping multiple validation errors
// returned by OutputParameter.ValidateAll() if the designated constraints
// aren't met.
type OutputParameterMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m OutputParameterMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m OutputParameterMultiError) AllErrors() []error { return m }

// OutputParameterValidationError is the validation error returned by
// OutputParameter.Validate if the designated constraints aren't met.
type OutputParameterValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e OutputParameterValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e OutputParameterValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e OutputParameterValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e OutputParameterValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e OutputParameterValidationError) ErrorName() string { return "OutputParameterValidationError" }

// Error satisfies the builtin error interface
func (e OutputParameterValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sOutputParameter.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = OutputParameterValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = OutputParameterValidationError{}

// Validate checks the field values on InterfaceConfig with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *InterfaceConfig) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on InterfaceConfig with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// InterfaceConfigMultiError, or nil if none found.
func (m *InterfaceConfig) ValidateAll() error {
	return m.validate(true)
}

func (m *InterfaceConfig) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ProjectId

	// no validation rules for DocumentId

	// no validation rules for ConfigId

	// no validation rules for Name

	// no validation rules for Description

	// no validation rules for Path

	// no validation rules for Method

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InterfaceConfigValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InterfaceConfigValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InterfaceConfigValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetInputParameters() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, InterfaceConfigValidationError{
						field:  fmt.Sprintf("InputParameters[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, InterfaceConfigValidationError{
						field:  fmt.Sprintf("InputParameters[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return InterfaceConfigValidationError{
					field:  fmt.Sprintf("InputParameters[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetOutputParameters() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, InterfaceConfigValidationError{
						field:  fmt.Sprintf("OutputParameters[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, InterfaceConfigValidationError{
						field:  fmt.Sprintf("OutputParameters[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return InterfaceConfigValidationError{
					field:  fmt.Sprintf("OutputParameters[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for CreatedBy

	// no validation rules for UpdatedBy

	// no validation rules for CreatedAt

	// no validation rules for UpdatedAt

	if len(errors) > 0 {
		return InterfaceConfigMultiError(errors)
	}

	return nil
}

// InterfaceConfigMultiError is an error wrapping multiple validation errors
// returned by InterfaceConfig.ValidateAll() if the designated constraints
// aren't met.
type InterfaceConfigMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m InterfaceConfigMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m InterfaceConfigMultiError) AllErrors() []error { return m }

// InterfaceConfigValidationError is the validation error returned by
// InterfaceConfig.Validate if the designated constraints aren't met.
type InterfaceConfigValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e InterfaceConfigValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e InterfaceConfigValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e InterfaceConfigValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e InterfaceConfigValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e InterfaceConfigValidationError) ErrorName() string { return "InterfaceConfigValidationError" }

// Error satisfies the builtin error interface
func (e InterfaceConfigValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sInterfaceConfig.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = InterfaceConfigValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = InterfaceConfigValidationError{}

// Validate checks the field values on InterfaceCase with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *InterfaceCase) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on InterfaceCase with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in InterfaceCaseMultiError, or
// nil if none found.
func (m *InterfaceCase) ValidateAll() error {
	return m.validate(true)
}

func (m *InterfaceCase) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ProjectId

	// no validation rules for DocumentId

	// no validation rules for CaseId

	// no validation rules for Name

	// no validation rules for Description

	// no validation rules for Priority

	// no validation rules for State

	if all {
		switch v := interface{}(m.GetAccountConfig()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InterfaceCaseValidationError{
					field:  "AccountConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InterfaceCaseValidationError{
					field:  "AccountConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAccountConfig()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InterfaceCaseValidationError{
				field:  "AccountConfig",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Version

	if all {
		switch v := interface{}(m.GetLatestRecord()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InterfaceCaseValidationError{
					field:  "LatestRecord",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InterfaceCaseValidationError{
					field:  "LatestRecord",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLatestRecord()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InterfaceCaseValidationError{
				field:  "LatestRecord",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for MaintainedBy

	// no validation rules for CreatedBy

	// no validation rules for UpdatedBy

	// no validation rules for CreatedAt

	// no validation rules for UpdatedAt

	for idx, item := range m.GetNodes() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, InterfaceCaseValidationError{
						field:  fmt.Sprintf("Nodes[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, InterfaceCaseValidationError{
						field:  fmt.Sprintf("Nodes[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return InterfaceCaseValidationError{
					field:  fmt.Sprintf("Nodes[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetEdges() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, InterfaceCaseValidationError{
						field:  fmt.Sprintf("Edges[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, InterfaceCaseValidationError{
						field:  fmt.Sprintf("Edges[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return InterfaceCaseValidationError{
					field:  fmt.Sprintf("Edges[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetCombos() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, InterfaceCaseValidationError{
						field:  fmt.Sprintf("Combos[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, InterfaceCaseValidationError{
						field:  fmt.Sprintf("Combos[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return InterfaceCaseValidationError{
					field:  fmt.Sprintf("Combos[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return InterfaceCaseMultiError(errors)
	}

	return nil
}

// InterfaceCaseMultiError is an error wrapping multiple validation errors
// returned by InterfaceCase.ValidateAll() if the designated constraints
// aren't met.
type InterfaceCaseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m InterfaceCaseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m InterfaceCaseMultiError) AllErrors() []error { return m }

// InterfaceCaseValidationError is the validation error returned by
// InterfaceCase.Validate if the designated constraints aren't met.
type InterfaceCaseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e InterfaceCaseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e InterfaceCaseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e InterfaceCaseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e InterfaceCaseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e InterfaceCaseValidationError) ErrorName() string { return "InterfaceCaseValidationError" }

// Error satisfies the builtin error interface
func (e InterfaceCaseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sInterfaceCase.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = InterfaceCaseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = InterfaceCaseValidationError{}

// Validate checks the field values on SearchInterfaceDocumentReferenceItem
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *SearchInterfaceDocumentReferenceItem) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SearchInterfaceDocumentReferenceItem
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// SearchInterfaceDocumentReferenceItemMultiError, or nil if none found.
func (m *SearchInterfaceDocumentReferenceItem) ValidateAll() error {
	return m.validate(true)
}

func (m *SearchInterfaceDocumentReferenceItem) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ProjectId

	// no validation rules for DocumentId

	// no validation rules for ReferenceType

	// no validation rules for ReferenceId

	// no validation rules for Name

	// no validation rules for Description

	// no validation rules for Priority

	// no validation rules for State

	// no validation rules for ReferenceState

	// no validation rules for MaintainedBy

	// no validation rules for CreatedBy

	// no validation rules for UpdatedBy

	// no validation rules for CreatedAt

	// no validation rules for UpdatedAt

	if len(errors) > 0 {
		return SearchInterfaceDocumentReferenceItemMultiError(errors)
	}

	return nil
}

// SearchInterfaceDocumentReferenceItemMultiError is an error wrapping multiple
// validation errors returned by
// SearchInterfaceDocumentReferenceItem.ValidateAll() if the designated
// constraints aren't met.
type SearchInterfaceDocumentReferenceItemMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SearchInterfaceDocumentReferenceItemMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SearchInterfaceDocumentReferenceItemMultiError) AllErrors() []error { return m }

// SearchInterfaceDocumentReferenceItemValidationError is the validation error
// returned by SearchInterfaceDocumentReferenceItem.Validate if the designated
// constraints aren't met.
type SearchInterfaceDocumentReferenceItemValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SearchInterfaceDocumentReferenceItemValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SearchInterfaceDocumentReferenceItemValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SearchInterfaceDocumentReferenceItemValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SearchInterfaceDocumentReferenceItemValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SearchInterfaceDocumentReferenceItemValidationError) ErrorName() string {
	return "SearchInterfaceDocumentReferenceItemValidationError"
}

// Error satisfies the builtin error interface
func (e SearchInterfaceDocumentReferenceItemValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSearchInterfaceDocumentReferenceItem.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SearchInterfaceDocumentReferenceItemValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SearchInterfaceDocumentReferenceItemValidationError{}

// Validate checks the field values on UpdateInterfaceCoverageTaskInfo with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateInterfaceCoverageTaskInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateInterfaceCoverageTaskInfo with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// UpdateInterfaceCoverageTaskInfoMultiError, or nil if none found.
func (m *UpdateInterfaceCoverageTaskInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateInterfaceCoverageTaskInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if !_UpdateInterfaceCoverageTaskInfo_ProjectId_Pattern.MatchString(m.GetProjectId()) {
		err := UpdateInterfaceCoverageTaskInfoValidationError{
			field:  "ProjectId",
			reason: "value does not match regex pattern \"(?:^project_id:.+?|^1$)\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if val := m.GetKeepDays(); val < 1 || val > 365 {
		err := UpdateInterfaceCoverageTaskInfoValidationError{
			field:  "KeepDays",
			reason: "value must be inside range [1, 365]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return UpdateInterfaceCoverageTaskInfoMultiError(errors)
	}

	return nil
}

// UpdateInterfaceCoverageTaskInfoMultiError is an error wrapping multiple
// validation errors returned by UpdateInterfaceCoverageTaskInfo.ValidateAll()
// if the designated constraints aren't met.
type UpdateInterfaceCoverageTaskInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateInterfaceCoverageTaskInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateInterfaceCoverageTaskInfoMultiError) AllErrors() []error { return m }

// UpdateInterfaceCoverageTaskInfoValidationError is the validation error
// returned by UpdateInterfaceCoverageTaskInfo.Validate if the designated
// constraints aren't met.
type UpdateInterfaceCoverageTaskInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateInterfaceCoverageTaskInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateInterfaceCoverageTaskInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateInterfaceCoverageTaskInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateInterfaceCoverageTaskInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateInterfaceCoverageTaskInfoValidationError) ErrorName() string {
	return "UpdateInterfaceCoverageTaskInfoValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateInterfaceCoverageTaskInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateInterfaceCoverageTaskInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateInterfaceCoverageTaskInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateInterfaceCoverageTaskInfoValidationError{}

var _UpdateInterfaceCoverageTaskInfo_ProjectId_Pattern = regexp.MustCompile("(?:^project_id:.+?|^1$)")

// Validate checks the field values on UpdateInterfaceDefinitionTaskInfo with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *UpdateInterfaceDefinitionTaskInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateInterfaceDefinitionTaskInfo
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// UpdateInterfaceDefinitionTaskInfoMultiError, or nil if none found.
func (m *UpdateInterfaceDefinitionTaskInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateInterfaceDefinitionTaskInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if !_UpdateInterfaceDefinitionTaskInfo_ProjectId_Pattern.MatchString(m.GetProjectId()) {
		err := UpdateInterfaceDefinitionTaskInfoValidationError{
			field:  "ProjectId",
			reason: "value does not match regex pattern \"(?:^project_id:.+?|^1$)\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if _, ok := _UpdateInterfaceDefinitionTaskInfo_Type_InLookup[m.GetType()]; !ok {
		err := UpdateInterfaceDefinitionTaskInfoValidationError{
			field:  "Type",
			reason: "value must be in list [OpenApi gRPC YApi TT TTMeta Recommend]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetLocalPath()) < 1 {
		err := UpdateInterfaceDefinitionTaskInfoValidationError{
			field:  "LocalPath",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(m.GetDepLocalPaths()) > 0 {

		for idx, item := range m.GetDepLocalPaths() {
			_, _ = idx, item

			if utf8.RuneCountInString(item) < 1 {
				err := UpdateInterfaceDefinitionTaskInfoValidationError{
					field:  fmt.Sprintf("DepLocalPaths[%v]", idx),
					reason: "value length must be at least 1 runes",
				}
				if !all {
					return err
				}
				errors = append(errors, err)
			}

		}

	}

	if len(errors) > 0 {
		return UpdateInterfaceDefinitionTaskInfoMultiError(errors)
	}

	return nil
}

// UpdateInterfaceDefinitionTaskInfoMultiError is an error wrapping multiple
// validation errors returned by
// UpdateInterfaceDefinitionTaskInfo.ValidateAll() if the designated
// constraints aren't met.
type UpdateInterfaceDefinitionTaskInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateInterfaceDefinitionTaskInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateInterfaceDefinitionTaskInfoMultiError) AllErrors() []error { return m }

// UpdateInterfaceDefinitionTaskInfoValidationError is the validation error
// returned by UpdateInterfaceDefinitionTaskInfo.Validate if the designated
// constraints aren't met.
type UpdateInterfaceDefinitionTaskInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateInterfaceDefinitionTaskInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateInterfaceDefinitionTaskInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateInterfaceDefinitionTaskInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateInterfaceDefinitionTaskInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateInterfaceDefinitionTaskInfoValidationError) ErrorName() string {
	return "UpdateInterfaceDefinitionTaskInfoValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateInterfaceDefinitionTaskInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateInterfaceDefinitionTaskInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateInterfaceDefinitionTaskInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateInterfaceDefinitionTaskInfoValidationError{}

var _UpdateInterfaceDefinitionTaskInfo_ProjectId_Pattern = regexp.MustCompile("(?:^project_id:.+?|^1$)")

var _UpdateInterfaceDefinitionTaskInfo_Type_InLookup = map[string]struct{}{
	"OpenApi":   {},
	"gRPC":      {},
	"YApi":      {},
	"TT":        {},
	"TTMeta":    {},
	"Recommend": {},
}

// Validate checks the field values on SearchInterfaceCaseReferenceItem with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *SearchInterfaceCaseReferenceItem) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SearchInterfaceCaseReferenceItem with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// SearchInterfaceCaseReferenceItemMultiError, or nil if none found.
func (m *SearchInterfaceCaseReferenceItem) ValidateAll() error {
	return m.validate(true)
}

func (m *SearchInterfaceCaseReferenceItem) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ProjectId

	// no validation rules for CaseId

	// no validation rules for ReferenceType

	// no validation rules for ReferenceId

	// no validation rules for Name

	// no validation rules for Description

	// no validation rules for Priority

	// no validation rules for State

	// no validation rules for MaintainedBy

	// no validation rules for CreatedBy

	// no validation rules for UpdatedBy

	// no validation rules for CreatedAt

	// no validation rules for UpdatedAt

	if len(errors) > 0 {
		return SearchInterfaceCaseReferenceItemMultiError(errors)
	}

	return nil
}

// SearchInterfaceCaseReferenceItemMultiError is an error wrapping multiple
// validation errors returned by
// SearchInterfaceCaseReferenceItem.ValidateAll() if the designated
// constraints aren't met.
type SearchInterfaceCaseReferenceItemMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SearchInterfaceCaseReferenceItemMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SearchInterfaceCaseReferenceItemMultiError) AllErrors() []error { return m }

// SearchInterfaceCaseReferenceItemValidationError is the validation error
// returned by SearchInterfaceCaseReferenceItem.Validate if the designated
// constraints aren't met.
type SearchInterfaceCaseReferenceItemValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SearchInterfaceCaseReferenceItemValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SearchInterfaceCaseReferenceItemValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SearchInterfaceCaseReferenceItemValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SearchInterfaceCaseReferenceItemValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SearchInterfaceCaseReferenceItemValidationError) ErrorName() string {
	return "SearchInterfaceCaseReferenceItemValidationError"
}

// Error satisfies the builtin error interface
func (e SearchInterfaceCaseReferenceItemValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSearchInterfaceCaseReferenceItem.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SearchInterfaceCaseReferenceItemValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SearchInterfaceCaseReferenceItemValidationError{}

// Validate checks the field values on InterfaceCoverageData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *InterfaceCoverageData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on InterfaceCoverageData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// InterfaceCoverageDataMultiError, or nil if none found.
func (m *InterfaceCoverageData) ValidateAll() error {
	return m.validate(true)
}

func (m *InterfaceCoverageData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return InterfaceCoverageDataMultiError(errors)
	}

	return nil
}

// InterfaceCoverageDataMultiError is an error wrapping multiple validation
// errors returned by InterfaceCoverageData.ValidateAll() if the designated
// constraints aren't met.
type InterfaceCoverageDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m InterfaceCoverageDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m InterfaceCoverageDataMultiError) AllErrors() []error { return m }

// InterfaceCoverageDataValidationError is the validation error returned by
// InterfaceCoverageData.Validate if the designated constraints aren't met.
type InterfaceCoverageDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e InterfaceCoverageDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e InterfaceCoverageDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e InterfaceCoverageDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e InterfaceCoverageDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e InterfaceCoverageDataValidationError) ErrorName() string {
	return "InterfaceCoverageDataValidationError"
}

// Error satisfies the builtin error interface
func (e InterfaceCoverageDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sInterfaceCoverageData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = InterfaceCoverageDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = InterfaceCoverageDataValidationError{}
