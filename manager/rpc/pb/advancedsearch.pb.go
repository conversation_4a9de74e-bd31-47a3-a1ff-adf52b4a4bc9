// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.1
// 	protoc        v5.29.2
// source: manager/advancedsearch.proto

package pb

import (
	reflect "reflect"
	sync "sync"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type SceneType int32

const (
	SceneType_ST_NULL               SceneType = 0
	SceneType_ST_API_SUITE          SceneType = 1
	SceneType_ST_INTERFACE_DOCUMENT SceneType = 2
)

// Enum value maps for SceneType.
var (
	SceneType_name = map[int32]string{
		0: "ST_NULL",
		1: "ST_API_SUITE",
		2: "ST_INTERFACE_DOCUMENT",
	}
	SceneType_value = map[string]int32{
		"ST_NULL":               0,
		"ST_API_SUITE":          1,
		"ST_INTERFACE_DOCUMENT": 2,
	}
)

func (x SceneType) Enum() *SceneType {
	p := new(SceneType)
	*p = x
	return p
}

func (x SceneType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (SceneType) Descriptor() protoreflect.EnumDescriptor {
	return file_manager_advancedsearch_proto_enumTypes[0].Descriptor()
}

func (SceneType) Type() protoreflect.EnumType {
	return &file_manager_advancedsearch_proto_enumTypes[0]
}

func (x SceneType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use SceneType.Descriptor instead.
func (SceneType) EnumDescriptor() ([]byte, []int) {
	return file_manager_advancedsearch_proto_rawDescGZIP(), []int{0}
}

// 预留给高级搜索字段管理用
type FieldType int32

const (
	FieldType_FT_NULL      FieldType = 0
	FieldType_FT_STRING    FieldType = 1
	FieldType_FT_TIMESTAMP FieldType = 2
)

// Enum value maps for FieldType.
var (
	FieldType_name = map[int32]string{
		0: "FT_NULL",
		1: "FT_STRING",
		2: "FT_TIMESTAMP",
	}
	FieldType_value = map[string]int32{
		"FT_NULL":      0,
		"FT_STRING":    1,
		"FT_TIMESTAMP": 2,
	}
)

func (x FieldType) Enum() *FieldType {
	p := new(FieldType)
	*p = x
	return p
}

func (x FieldType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (FieldType) Descriptor() protoreflect.EnumDescriptor {
	return file_manager_advancedsearch_proto_enumTypes[1].Descriptor()
}

func (FieldType) Type() protoreflect.EnumType {
	return &file_manager_advancedsearch_proto_enumTypes[1]
}

func (x FieldType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use FieldType.Descriptor instead.
func (FieldType) EnumDescriptor() ([]byte, []int) {
	return file_manager_advancedsearch_proto_rawDescGZIP(), []int{1}
}

type AdvancedSearchField struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	FieldId       string                 `protobuf:"bytes,1,opt,name=field_id,json=fieldId,proto3" json:"field_id,omitempty"`
	ProjectId     string                 `protobuf:"bytes,2,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`
	FieldType     string                 `protobuf:"bytes,3,opt,name=field_type,json=fieldType,proto3" json:"field_type,omitempty"`
	SceneType     SceneType              `protobuf:"varint,4,opt,name=scene_type,json=sceneType,proto3,enum=manager.SceneType" json:"scene_type,omitempty"` // 场景类型
	FrontName     string                 `protobuf:"bytes,5,opt,name=front_name,json=frontName,proto3" json:"front_name,omitempty"`                         // 前端显示名称
	FieldName     string                 `protobuf:"bytes,6,opt,name=field_name,json=fieldName,proto3" json:"field_name,omitempty"`                         // 集合描述
	CreatedAt     int64                  `protobuf:"varint,7,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`                        // 创建时间
	UpdatedAt     int64                  `protobuf:"varint,8,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`                        // 更新时间
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AdvancedSearchField) Reset() {
	*x = AdvancedSearchField{}
	mi := &file_manager_advancedsearch_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AdvancedSearchField) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AdvancedSearchField) ProtoMessage() {}

func (x *AdvancedSearchField) ProtoReflect() protoreflect.Message {
	mi := &file_manager_advancedsearch_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AdvancedSearchField.ProtoReflect.Descriptor instead.
func (*AdvancedSearchField) Descriptor() ([]byte, []int) {
	return file_manager_advancedsearch_proto_rawDescGZIP(), []int{0}
}

func (x *AdvancedSearchField) GetFieldId() string {
	if x != nil {
		return x.FieldId
	}
	return ""
}

func (x *AdvancedSearchField) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *AdvancedSearchField) GetFieldType() string {
	if x != nil {
		return x.FieldType
	}
	return ""
}

func (x *AdvancedSearchField) GetSceneType() SceneType {
	if x != nil {
		return x.SceneType
	}
	return SceneType_ST_NULL
}

func (x *AdvancedSearchField) GetFrontName() string {
	if x != nil {
		return x.FrontName
	}
	return ""
}

func (x *AdvancedSearchField) GetFieldName() string {
	if x != nil {
		return x.FieldName
	}
	return ""
}

func (x *AdvancedSearchField) GetCreatedAt() int64 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

func (x *AdvancedSearchField) GetUpdatedAt() int64 {
	if x != nil {
		return x.UpdatedAt
	}
	return 0
}

type AdvancedSearchCondition struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ConditionId   string                 `protobuf:"bytes,1,opt,name=condition_id,json=conditionId,proto3" json:"condition_id,omitempty"`
	FrontName     string                 `protobuf:"bytes,2,opt,name=front_name,json=frontName,proto3" json:"front_name,omitempty"`  // 前端显示名称
	Compare       string                 `protobuf:"bytes,3,opt,name=compare,proto3" json:"compare,omitempty"`                       // 对比方式
	CreatedAt     int64                  `protobuf:"varint,4,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"` // 创建时间
	UpdatedAt     int64                  `protobuf:"varint,5,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"` // 更新时间
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AdvancedSearchCondition) Reset() {
	*x = AdvancedSearchCondition{}
	mi := &file_manager_advancedsearch_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AdvancedSearchCondition) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AdvancedSearchCondition) ProtoMessage() {}

func (x *AdvancedSearchCondition) ProtoReflect() protoreflect.Message {
	mi := &file_manager_advancedsearch_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AdvancedSearchCondition.ProtoReflect.Descriptor instead.
func (*AdvancedSearchCondition) Descriptor() ([]byte, []int) {
	return file_manager_advancedsearch_proto_rawDescGZIP(), []int{1}
}

func (x *AdvancedSearchCondition) GetConditionId() string {
	if x != nil {
		return x.ConditionId
	}
	return ""
}

func (x *AdvancedSearchCondition) GetFrontName() string {
	if x != nil {
		return x.FrontName
	}
	return ""
}

func (x *AdvancedSearchCondition) GetCompare() string {
	if x != nil {
		return x.Compare
	}
	return ""
}

func (x *AdvancedSearchCondition) GetCreatedAt() int64 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

func (x *AdvancedSearchCondition) GetUpdatedAt() int64 {
	if x != nil {
		return x.UpdatedAt
	}
	return 0
}

var File_manager_advancedsearch_proto protoreflect.FileDescriptor

var file_manager_advancedsearch_proto_rawDesc = []byte{
	0x0a, 0x1c, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2f, 0x61, 0x64, 0x76, 0x61, 0x6e, 0x63,
	0x65, 0x64, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x07,
	0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x22, 0x9d, 0x02, 0x0a, 0x13, 0x41, 0x64, 0x76, 0x61,
	0x6e, 0x63, 0x65, 0x64, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x12,
	0x19, 0x0a, 0x08, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72,
	0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x66, 0x69, 0x65,
	0x6c, 0x64, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x66,
	0x69, 0x65, 0x6c, 0x64, 0x54, 0x79, 0x70, 0x65, 0x12, 0x31, 0x0a, 0x0a, 0x73, 0x63, 0x65, 0x6e,
	0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x12, 0x2e, 0x6d,
	0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x53, 0x63, 0x65, 0x6e, 0x65, 0x54, 0x79, 0x70, 0x65,
	0x52, 0x09, 0x73, 0x63, 0x65, 0x6e, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x66,
	0x72, 0x6f, 0x6e, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x66, 0x69,
	0x65, 0x6c, 0x64, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x66, 0x69, 0x65, 0x6c, 0x64, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x63,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x75, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x22, 0xb3, 0x01, 0x0a, 0x17, 0x41, 0x64, 0x76, 0x61,
	0x6e, 0x63, 0x65, 0x64, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x12, 0x21, 0x0a, 0x0c, 0x63, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x6f, 0x6e, 0x64, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x66, 0x72, 0x6f, 0x6e,
	0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x72, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x72, 0x65, 0x12,
	0x1d, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x1d,
	0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x2a, 0x45, 0x0a,
	0x09, 0x53, 0x63, 0x65, 0x6e, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x0b, 0x0a, 0x07, 0x53, 0x54,
	0x5f, 0x4e, 0x55, 0x4c, 0x4c, 0x10, 0x00, 0x12, 0x10, 0x0a, 0x0c, 0x53, 0x54, 0x5f, 0x41, 0x50,
	0x49, 0x5f, 0x53, 0x55, 0x49, 0x54, 0x45, 0x10, 0x01, 0x12, 0x19, 0x0a, 0x15, 0x53, 0x54, 0x5f,
	0x49, 0x4e, 0x54, 0x45, 0x52, 0x46, 0x41, 0x43, 0x45, 0x5f, 0x44, 0x4f, 0x43, 0x55, 0x4d, 0x45,
	0x4e, 0x54, 0x10, 0x02, 0x2a, 0x39, 0x0a, 0x09, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x0b, 0x0a, 0x07, 0x46, 0x54, 0x5f, 0x4e, 0x55, 0x4c, 0x4c, 0x10, 0x00, 0x12, 0x0d,
	0x0a, 0x09, 0x46, 0x54, 0x5f, 0x53, 0x54, 0x52, 0x49, 0x4e, 0x47, 0x10, 0x01, 0x12, 0x10, 0x0a,
	0x0c, 0x46, 0x54, 0x5f, 0x54, 0x49, 0x4d, 0x45, 0x53, 0x54, 0x41, 0x4d, 0x50, 0x10, 0x02, 0x42,
	0x41, 0x5a, 0x3f, 0x67, 0x69, 0x74, 0x6c, 0x61, 0x62, 0x2e, 0x74, 0x74, 0x79, 0x75, 0x79, 0x69,
	0x6e, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x54, 0x65, 0x73, 0x74, 0x44, 0x65, 0x76, 0x65, 0x6c, 0x6f,
	0x70, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x70, 0x72, 0x6f, 0x62, 0x65, 0x2d, 0x62, 0x61, 0x63, 0x6b,
	0x65, 0x6e, 0x64, 0x2f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2f, 0x72, 0x70, 0x63, 0x2f,
	0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_manager_advancedsearch_proto_rawDescOnce sync.Once
	file_manager_advancedsearch_proto_rawDescData = file_manager_advancedsearch_proto_rawDesc
)

func file_manager_advancedsearch_proto_rawDescGZIP() []byte {
	file_manager_advancedsearch_proto_rawDescOnce.Do(func() {
		file_manager_advancedsearch_proto_rawDescData = protoimpl.X.CompressGZIP(file_manager_advancedsearch_proto_rawDescData)
	})
	return file_manager_advancedsearch_proto_rawDescData
}

var file_manager_advancedsearch_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_manager_advancedsearch_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_manager_advancedsearch_proto_goTypes = []any{
	(SceneType)(0),                  // 0: manager.SceneType
	(FieldType)(0),                  // 1: manager.FieldType
	(*AdvancedSearchField)(nil),     // 2: manager.AdvancedSearchField
	(*AdvancedSearchCondition)(nil), // 3: manager.AdvancedSearchCondition
}
var file_manager_advancedsearch_proto_depIdxs = []int32{
	0, // 0: manager.AdvancedSearchField.scene_type:type_name -> manager.SceneType
	1, // [1:1] is the sub-list for method output_type
	1, // [1:1] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_manager_advancedsearch_proto_init() }
func file_manager_advancedsearch_proto_init() {
	if File_manager_advancedsearch_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_manager_advancedsearch_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_manager_advancedsearch_proto_goTypes,
		DependencyIndexes: file_manager_advancedsearch_proto_depIdxs,
		EnumInfos:         file_manager_advancedsearch_proto_enumTypes,
		MessageInfos:      file_manager_advancedsearch_proto_msgTypes,
	}.Build()
	File_manager_advancedsearch_proto = out.File
	file_manager_advancedsearch_proto_rawDesc = nil
	file_manager_advancedsearch_proto_goTypes = nil
	file_manager_advancedsearch_proto_depIdxs = nil
}
