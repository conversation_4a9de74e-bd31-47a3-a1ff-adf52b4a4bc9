// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: manager/gitproject.proto

package pb

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on GitProjectTreeNode with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GitProjectTreeNode) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GitProjectTreeNode with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GitProjectTreeNodeMultiError, or nil if none found.
func (m *GitProjectTreeNode) ValidateAll() error {
	return m.validate(true)
}

func (m *GitProjectTreeNode) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ProjectId

	// no validation rules for GitConfigId

	// no validation rules for Path

	// no validation rules for ParentPath

	// no validation rules for Name

	// no validation rules for Alias

	// no validation rules for Type

	// no validation rules for CreatedBy

	// no validation rules for UpdatedBy

	// no validation rules for CreatedAt

	// no validation rules for UpdatedAt

	if len(errors) > 0 {
		return GitProjectTreeNodeMultiError(errors)
	}

	return nil
}

// GitProjectTreeNodeMultiError is an error wrapping multiple validation errors
// returned by GitProjectTreeNode.ValidateAll() if the designated constraints
// aren't met.
type GitProjectTreeNodeMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GitProjectTreeNodeMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GitProjectTreeNodeMultiError) AllErrors() []error { return m }

// GitProjectTreeNodeValidationError is the validation error returned by
// GitProjectTreeNode.Validate if the designated constraints aren't met.
type GitProjectTreeNodeValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GitProjectTreeNodeValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GitProjectTreeNodeValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GitProjectTreeNodeValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GitProjectTreeNodeValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GitProjectTreeNodeValidationError) ErrorName() string {
	return "GitProjectTreeNodeValidationError"
}

// Error satisfies the builtin error interface
func (e GitProjectTreeNodeValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGitProjectTreeNode.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GitProjectTreeNodeValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GitProjectTreeNodeValidationError{}
