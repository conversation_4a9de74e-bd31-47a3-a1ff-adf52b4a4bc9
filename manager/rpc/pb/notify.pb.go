// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.1
// 	protoc        v5.29.2
// source: manager/notify.proto

package pb

import (
	reflect "reflect"
	sync "sync"

	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type NotifyMode int32

const (
	NotifyMode_NOTIFY_MODE_NULL  NotifyMode = 0
	NotifyMode_ALWAYS_NOTIFY     NotifyMode = 1
	NotifyMode_ONLY_FALSE_NOTIFY NotifyMode = 2
)

// Enum value maps for NotifyMode.
var (
	NotifyMode_name = map[int32]string{
		0: "NOTIFY_MODE_NULL",
		1: "ALWAYS_NOTIFY",
		2: "ONLY_FALSE_NOTIFY",
	}
	NotifyMode_value = map[string]int32{
		"NOTIFY_MODE_NULL":  0,
		"ALWAYS_NOTIFY":     1,
		"ONLY_FALSE_NOTIFY": 2,
	}
)

func (x NotifyMode) Enum() *NotifyMode {
	p := new(NotifyMode)
	*p = x
	return p
}

func (x NotifyMode) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (NotifyMode) Descriptor() protoreflect.EnumDescriptor {
	return file_manager_notify_proto_enumTypes[0].Descriptor()
}

func (NotifyMode) Type() protoreflect.EnumType {
	return &file_manager_notify_proto_enumTypes[0]
}

func (x NotifyMode) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use NotifyMode.Descriptor instead.
func (NotifyMode) EnumDescriptor() ([]byte, []int) {
	return file_manager_notify_proto_rawDescGZIP(), []int{0}
}

type NotifyType int32

const (
	NotifyType_NOTIFY_TYPE_NULL NotifyType = 0
	NotifyType_LARK_GROUP       NotifyType = 1 // 通过自定义机器人的Webhook发送消息到飞书群
	NotifyType_EMAIL            NotifyType = 2
	NotifyType_LARK_CHAT        NotifyType = 3 // 通过自建应用所在的飞书群ID发送消息到飞书群
)

// Enum value maps for NotifyType.
var (
	NotifyType_name = map[int32]string{
		0: "NOTIFY_TYPE_NULL",
		1: "LARK_GROUP",
		2: "EMAIL",
		3: "LARK_CHAT",
	}
	NotifyType_value = map[string]int32{
		"NOTIFY_TYPE_NULL": 0,
		"LARK_GROUP":       1,
		"EMAIL":            2,
		"LARK_CHAT":        3,
	}
)

func (x NotifyType) Enum() *NotifyType {
	p := new(NotifyType)
	*p = x
	return p
}

func (x NotifyType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (NotifyType) Descriptor() protoreflect.EnumDescriptor {
	return file_manager_notify_proto_enumTypes[1].Descriptor()
}

func (NotifyType) Type() protoreflect.EnumType {
	return &file_manager_notify_proto_enumTypes[1]
}

func (x NotifyType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use NotifyType.Descriptor instead.
func (NotifyType) EnumDescriptor() ([]byte, []int) {
	return file_manager_notify_proto_rawDescGZIP(), []int{1}
}

type NotifyItem struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	NotifyMode    NotifyMode             `protobuf:"varint,1,opt,name=notify_mode,json=notifyMode,proto3,enum=manager.NotifyMode" json:"notify_mode,omitempty"` // 通知模式
	NotifyType    NotifyType             `protobuf:"varint,2,opt,name=notify_type,json=notifyType,proto3,enum=manager.NotifyType" json:"notify_type,omitempty"` // 通知类型
	Receiver      string                 `protobuf:"bytes,3,opt,name=receiver,proto3" json:"receiver,omitempty"`                                                // 接受者
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *NotifyItem) Reset() {
	*x = NotifyItem{}
	mi := &file_manager_notify_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NotifyItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NotifyItem) ProtoMessage() {}

func (x *NotifyItem) ProtoReflect() protoreflect.Message {
	mi := &file_manager_notify_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NotifyItem.ProtoReflect.Descriptor instead.
func (*NotifyItem) Descriptor() ([]byte, []int) {
	return file_manager_notify_proto_rawDescGZIP(), []int{0}
}

func (x *NotifyItem) GetNotifyMode() NotifyMode {
	if x != nil {
		return x.NotifyMode
	}
	return NotifyMode_NOTIFY_MODE_NULL
}

func (x *NotifyItem) GetNotifyType() NotifyType {
	if x != nil {
		return x.NotifyType
	}
	return NotifyType_NOTIFY_TYPE_NULL
}

func (x *NotifyItem) GetReceiver() string {
	if x != nil {
		return x.Receiver
	}
	return ""
}

type CreateNotifyItem struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ReceiverName  string                 `protobuf:"bytes,1,opt,name=receiver_name,json=receiverName,proto3" json:"receiver_name,omitempty"`
	Receiver      string                 `protobuf:"bytes,2,opt,name=receiver,proto3" json:"receiver,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateNotifyItem) Reset() {
	*x = CreateNotifyItem{}
	mi := &file_manager_notify_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateNotifyItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateNotifyItem) ProtoMessage() {}

func (x *CreateNotifyItem) ProtoReflect() protoreflect.Message {
	mi := &file_manager_notify_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateNotifyItem.ProtoReflect.Descriptor instead.
func (*CreateNotifyItem) Descriptor() ([]byte, []int) {
	return file_manager_notify_proto_rawDescGZIP(), []int{1}
}

func (x *CreateNotifyItem) GetReceiverName() string {
	if x != nil {
		return x.ReceiverName
	}
	return ""
}

func (x *CreateNotifyItem) GetReceiver() string {
	if x != nil {
		return x.Receiver
	}
	return ""
}

type Notify struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ProjectId     string                 `protobuf:"bytes,1,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`
	PlanId        string                 `protobuf:"bytes,2,opt,name=plan_id,json=planId,proto3" json:"plan_id,omitempty"`
	NotifyId      string                 `protobuf:"bytes,3,opt,name=notify_id,json=notifyId,proto3" json:"notify_id,omitempty"`
	NotifyMode    NotifyMode             `protobuf:"varint,4,opt,name=notify_mode,json=notifyMode,proto3,enum=manager.NotifyMode" json:"notify_mode,omitempty"`
	NotifyType    NotifyType             `protobuf:"varint,5,opt,name=notify_type,json=notifyType,proto3,enum=manager.NotifyType" json:"notify_type,omitempty"`
	ReceiverName  string                 `protobuf:"bytes,6,opt,name=receiver_name,json=receiverName,proto3" json:"receiver_name,omitempty"`
	Receiver      string                 `protobuf:"bytes,7,opt,name=receiver,proto3" json:"receiver,omitempty"`
	CreatedBy     string                 `protobuf:"bytes,8,opt,name=created_by,json=createdBy,proto3" json:"created_by,omitempty"`   // 创建者
	UpdatedBy     string                 `protobuf:"bytes,9,opt,name=updated_by,json=updatedBy,proto3" json:"updated_by,omitempty"`   // 更新者
	CreatedAt     int64                  `protobuf:"varint,10,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"` // 创建时间
	UpdatedAt     int64                  `protobuf:"varint,11,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"` // 更新时间
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Notify) Reset() {
	*x = Notify{}
	mi := &file_manager_notify_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Notify) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Notify) ProtoMessage() {}

func (x *Notify) ProtoReflect() protoreflect.Message {
	mi := &file_manager_notify_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Notify.ProtoReflect.Descriptor instead.
func (*Notify) Descriptor() ([]byte, []int) {
	return file_manager_notify_proto_rawDescGZIP(), []int{2}
}

func (x *Notify) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *Notify) GetPlanId() string {
	if x != nil {
		return x.PlanId
	}
	return ""
}

func (x *Notify) GetNotifyId() string {
	if x != nil {
		return x.NotifyId
	}
	return ""
}

func (x *Notify) GetNotifyMode() NotifyMode {
	if x != nil {
		return x.NotifyMode
	}
	return NotifyMode_NOTIFY_MODE_NULL
}

func (x *Notify) GetNotifyType() NotifyType {
	if x != nil {
		return x.NotifyType
	}
	return NotifyType_NOTIFY_TYPE_NULL
}

func (x *Notify) GetReceiverName() string {
	if x != nil {
		return x.ReceiverName
	}
	return ""
}

func (x *Notify) GetReceiver() string {
	if x != nil {
		return x.Receiver
	}
	return ""
}

func (x *Notify) GetCreatedBy() string {
	if x != nil {
		return x.CreatedBy
	}
	return ""
}

func (x *Notify) GetUpdatedBy() string {
	if x != nil {
		return x.UpdatedBy
	}
	return ""
}

func (x *Notify) GetCreatedAt() int64 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

func (x *Notify) GetUpdatedAt() int64 {
	if x != nil {
		return x.UpdatedAt
	}
	return 0
}

var File_manager_notify_proto protoreflect.FileDescriptor

var file_manager_notify_proto_rawDesc = []byte{
	0x0a, 0x14, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2f, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x79,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x07, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x1a,
	0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61,
	0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x94, 0x01, 0x0a, 0x0a, 0x4e, 0x6f, 0x74,
	0x69, 0x66, 0x79, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x34, 0x0a, 0x0b, 0x6e, 0x6f, 0x74, 0x69, 0x66,
	0x79, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x13, 0x2e, 0x6d,
	0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x4d, 0x6f, 0x64,
	0x65, 0x52, 0x0a, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x4d, 0x6f, 0x64, 0x65, 0x12, 0x34, 0x0a,
	0x0b, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x13, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x4e, 0x6f, 0x74,
	0x69, 0x66, 0x79, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0a, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x72, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x72, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x72, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x72, 0x22,
	0xc6, 0x01, 0x0a, 0x10, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x79,
	0x49, 0x74, 0x65, 0x6d, 0x12, 0x2c, 0x0a, 0x0d, 0x72, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x72,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04,
	0x72, 0x02, 0x18, 0x40, 0x52, 0x0c, 0x72, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x72, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x83, 0x01, 0x0a, 0x08, 0x72, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x72, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x67, 0xfa, 0x42, 0x64, 0x72, 0x62, 0x32, 0x60, 0x28, 0x3f,
	0x3a, 0x5e, 0x68, 0x74, 0x74, 0x70, 0x73, 0x3a, 0x2f, 0x2f, 0x6f, 0x70, 0x65, 0x6e, 0x5c, 0x2e,
	0x66, 0x65, 0x69, 0x73, 0x68, 0x75, 0x5c, 0x2e, 0x63, 0x6e, 0x2f, 0x6f, 0x70, 0x65, 0x6e, 0x2d,
	0x61, 0x70, 0x69, 0x73, 0x7c, 0x5b, 0x5c, 0x77, 0x5d, 0x2b, 0x40, 0x5b, 0x41, 0x2d, 0x5a, 0x61,
	0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x5d, 0x2b, 0x28, 0x2e, 0x5b, 0x41, 0x2d, 0x5a, 0x61, 0x2d, 0x7a,
	0x30, 0x2d, 0x39, 0x5d, 0x2b, 0x29, 0x7b, 0x31, 0x2c, 0x32, 0x7d, 0x29, 0x7c, 0x5e, 0x6f, 0x63,
	0x5f, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x5d, 0x7b, 0x33, 0x32, 0x7d, 0x24, 0x52, 0x08,
	0x72, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x72, 0x22, 0x86, 0x03, 0x0a, 0x06, 0x4e, 0x6f, 0x74,
	0x69, 0x66, 0x79, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74,
	0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x70, 0x6c, 0x61, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x70, 0x6c, 0x61, 0x6e, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x6e,
	0x6f, 0x74, 0x69, 0x66, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x6e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x49, 0x64, 0x12, 0x34, 0x0a, 0x0b, 0x6e, 0x6f, 0x74, 0x69,
	0x66, 0x79, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x13, 0x2e,
	0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x4d, 0x6f,
	0x64, 0x65, 0x52, 0x0a, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x4d, 0x6f, 0x64, 0x65, 0x12, 0x34,
	0x0a, 0x0b, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x13, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x4e, 0x6f,
	0x74, 0x69, 0x66, 0x79, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0a, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x79,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x72, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x72,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x72, 0x65, 0x63,
	0x65, 0x69, 0x76, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x72, 0x65, 0x63,
	0x65, 0x69, 0x76, 0x65, 0x72, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x72, 0x65, 0x63,
	0x65, 0x69, 0x76, 0x65, 0x72, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64,
	0x5f, 0x62, 0x79, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x64, 0x42, 0x79, 0x12, 0x1d, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f,
	0x62, 0x79, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x64, 0x42, 0x79, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61,
	0x74, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64,
	0x41, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74,
	0x18, 0x0b, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41,
	0x74, 0x2a, 0x4c, 0x0a, 0x0a, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x4d, 0x6f, 0x64, 0x65, 0x12,
	0x14, 0x0a, 0x10, 0x4e, 0x4f, 0x54, 0x49, 0x46, 0x59, 0x5f, 0x4d, 0x4f, 0x44, 0x45, 0x5f, 0x4e,
	0x55, 0x4c, 0x4c, 0x10, 0x00, 0x12, 0x11, 0x0a, 0x0d, 0x41, 0x4c, 0x57, 0x41, 0x59, 0x53, 0x5f,
	0x4e, 0x4f, 0x54, 0x49, 0x46, 0x59, 0x10, 0x01, 0x12, 0x15, 0x0a, 0x11, 0x4f, 0x4e, 0x4c, 0x59,
	0x5f, 0x46, 0x41, 0x4c, 0x53, 0x45, 0x5f, 0x4e, 0x4f, 0x54, 0x49, 0x46, 0x59, 0x10, 0x02, 0x2a,
	0x4c, 0x0a, 0x0a, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x54, 0x79, 0x70, 0x65, 0x12, 0x14, 0x0a,
	0x10, 0x4e, 0x4f, 0x54, 0x49, 0x46, 0x59, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4e, 0x55, 0x4c,
	0x4c, 0x10, 0x00, 0x12, 0x0e, 0x0a, 0x0a, 0x4c, 0x41, 0x52, 0x4b, 0x5f, 0x47, 0x52, 0x4f, 0x55,
	0x50, 0x10, 0x01, 0x12, 0x09, 0x0a, 0x05, 0x45, 0x4d, 0x41, 0x49, 0x4c, 0x10, 0x02, 0x12, 0x0d,
	0x0a, 0x09, 0x4c, 0x41, 0x52, 0x4b, 0x5f, 0x43, 0x48, 0x41, 0x54, 0x10, 0x03, 0x42, 0x41, 0x5a,
	0x3f, 0x67, 0x69, 0x74, 0x6c, 0x61, 0x62, 0x2e, 0x74, 0x74, 0x79, 0x75, 0x79, 0x69, 0x6e, 0x2e,
	0x63, 0x6f, 0x6d, 0x2f, 0x54, 0x65, 0x73, 0x74, 0x44, 0x65, 0x76, 0x65, 0x6c, 0x6f, 0x70, 0x6d,
	0x65, 0x6e, 0x74, 0x2f, 0x70, 0x72, 0x6f, 0x62, 0x65, 0x2d, 0x62, 0x61, 0x63, 0x6b, 0x65, 0x6e,
	0x64, 0x2f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2f, 0x72, 0x70, 0x63, 0x2f, 0x70, 0x62,
	0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_manager_notify_proto_rawDescOnce sync.Once
	file_manager_notify_proto_rawDescData = file_manager_notify_proto_rawDesc
)

func file_manager_notify_proto_rawDescGZIP() []byte {
	file_manager_notify_proto_rawDescOnce.Do(func() {
		file_manager_notify_proto_rawDescData = protoimpl.X.CompressGZIP(file_manager_notify_proto_rawDescData)
	})
	return file_manager_notify_proto_rawDescData
}

var file_manager_notify_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_manager_notify_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_manager_notify_proto_goTypes = []any{
	(NotifyMode)(0),          // 0: manager.NotifyMode
	(NotifyType)(0),          // 1: manager.NotifyType
	(*NotifyItem)(nil),       // 2: manager.NotifyItem
	(*CreateNotifyItem)(nil), // 3: manager.CreateNotifyItem
	(*Notify)(nil),           // 4: manager.Notify
}
var file_manager_notify_proto_depIdxs = []int32{
	0, // 0: manager.NotifyItem.notify_mode:type_name -> manager.NotifyMode
	1, // 1: manager.NotifyItem.notify_type:type_name -> manager.NotifyType
	0, // 2: manager.Notify.notify_mode:type_name -> manager.NotifyMode
	1, // 3: manager.Notify.notify_type:type_name -> manager.NotifyType
	4, // [4:4] is the sub-list for method output_type
	4, // [4:4] is the sub-list for method input_type
	4, // [4:4] is the sub-list for extension type_name
	4, // [4:4] is the sub-list for extension extendee
	0, // [0:4] is the sub-list for field type_name
}

func init() { file_manager_notify_proto_init() }
func file_manager_notify_proto_init() {
	if File_manager_notify_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_manager_notify_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_manager_notify_proto_goTypes,
		DependencyIndexes: file_manager_notify_proto_depIdxs,
		EnumInfos:         file_manager_notify_proto_enumTypes,
		MessageInfos:      file_manager_notify_proto_msgTypes,
	}.Build()
	File_manager_notify_proto = out.File
	file_manager_notify_proto_rawDesc = nil
	file_manager_notify_proto_goTypes = nil
	file_manager_notify_proto_depIdxs = nil
}
