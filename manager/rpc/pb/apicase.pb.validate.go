// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: manager/apicase.proto

package pb

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on ApiCase with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ApiCase) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ApiCase with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in ApiCaseMultiError, or nil if none found.
func (m *ApiCase) ValidateAll() error {
	return m.validate(true)
}

func (m *ApiCase) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ProjectId

	// no validation rules for CategoryId

	// no validation rules for CaseId

	// no validation rules for Name

	// no validation rules for Description

	// no validation rules for Priority

	// no validation rules for State

	if all {
		switch v := interface{}(m.GetAccountConfig()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ApiCaseValidationError{
					field:  "AccountConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ApiCaseValidationError{
					field:  "AccountConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAccountConfig()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ApiCaseValidationError{
				field:  "AccountConfig",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Version

	// no validation rules for MaintainedBy

	// no validation rules for CreatedBy

	// no validation rules for UpdatedBy

	// no validation rules for CreatedAt

	// no validation rules for UpdatedAt

	for idx, item := range m.GetNodes() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ApiCaseValidationError{
						field:  fmt.Sprintf("Nodes[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ApiCaseValidationError{
						field:  fmt.Sprintf("Nodes[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ApiCaseValidationError{
					field:  fmt.Sprintf("Nodes[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetEdges() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ApiCaseValidationError{
						field:  fmt.Sprintf("Edges[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ApiCaseValidationError{
						field:  fmt.Sprintf("Edges[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ApiCaseValidationError{
					field:  fmt.Sprintf("Edges[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetCombos() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ApiCaseValidationError{
						field:  fmt.Sprintf("Combos[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ApiCaseValidationError{
						field:  fmt.Sprintf("Combos[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ApiCaseValidationError{
					field:  fmt.Sprintf("Combos[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ApiCaseMultiError(errors)
	}

	return nil
}

// ApiCaseMultiError is an error wrapping multiple validation errors returned
// by ApiCase.ValidateAll() if the designated constraints aren't met.
type ApiCaseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ApiCaseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ApiCaseMultiError) AllErrors() []error { return m }

// ApiCaseValidationError is the validation error returned by ApiCase.Validate
// if the designated constraints aren't met.
type ApiCaseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ApiCaseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ApiCaseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ApiCaseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ApiCaseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ApiCaseValidationError) ErrorName() string { return "ApiCaseValidationError" }

// Error satisfies the builtin error interface
func (e ApiCaseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sApiCase.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ApiCaseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ApiCaseValidationError{}

// Validate checks the field values on SearchApiCaseReferenceItem with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SearchApiCaseReferenceItem) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SearchApiCaseReferenceItem with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SearchApiCaseReferenceItemMultiError, or nil if none found.
func (m *SearchApiCaseReferenceItem) ValidateAll() error {
	return m.validate(true)
}

func (m *SearchApiCaseReferenceItem) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ProjectId

	// no validation rules for CaseId

	// no validation rules for ReferenceType

	// no validation rules for ReferenceId

	// no validation rules for Name

	// no validation rules for Description

	// no validation rules for Priority

	// no validation rules for State

	// no validation rules for MaintainedBy

	// no validation rules for CreatedBy

	// no validation rules for UpdatedBy

	// no validation rules for CreatedAt

	// no validation rules for UpdatedAt

	if len(errors) > 0 {
		return SearchApiCaseReferenceItemMultiError(errors)
	}

	return nil
}

// SearchApiCaseReferenceItemMultiError is an error wrapping multiple
// validation errors returned by SearchApiCaseReferenceItem.ValidateAll() if
// the designated constraints aren't met.
type SearchApiCaseReferenceItemMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SearchApiCaseReferenceItemMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SearchApiCaseReferenceItemMultiError) AllErrors() []error { return m }

// SearchApiCaseReferenceItemValidationError is the validation error returned
// by SearchApiCaseReferenceItem.Validate if the designated constraints aren't met.
type SearchApiCaseReferenceItemValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SearchApiCaseReferenceItemValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SearchApiCaseReferenceItemValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SearchApiCaseReferenceItemValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SearchApiCaseReferenceItemValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SearchApiCaseReferenceItemValidationError) ErrorName() string {
	return "SearchApiCaseReferenceItemValidationError"
}

// Error satisfies the builtin error interface
func (e SearchApiCaseReferenceItemValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSearchApiCaseReferenceItem.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SearchApiCaseReferenceItemValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SearchApiCaseReferenceItemValidationError{}
