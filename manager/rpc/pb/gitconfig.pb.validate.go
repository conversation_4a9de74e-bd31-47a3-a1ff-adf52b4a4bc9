// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: manager/gitconfig.proto

package pb

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on GitConfiguration with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *GitConfiguration) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GitConfiguration with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GitConfigurationMultiError, or nil if none found.
func (m *GitConfiguration) ValidateAll() error {
	return m.validate(true)
}

func (m *GitConfiguration) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ProjectId

	// no validation rules for ConfigId

	// no validation rules for Type

	// no validation rules for Name

	// no validation rules for Description

	// no validation rules for Url

	// no validation rules for AccessToken

	// no validation rules for Branch

	// no validation rules for Purpose

	// no validation rules for CreatedBy

	// no validation rules for UpdatedBy

	// no validation rules for CreatedAt

	// no validation rules for UpdatedAt

	if len(errors) > 0 {
		return GitConfigurationMultiError(errors)
	}

	return nil
}

// GitConfigurationMultiError is an error wrapping multiple validation errors
// returned by GitConfiguration.ValidateAll() if the designated constraints
// aren't met.
type GitConfigurationMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GitConfigurationMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GitConfigurationMultiError) AllErrors() []error { return m }

// GitConfigurationValidationError is the validation error returned by
// GitConfiguration.Validate if the designated constraints aren't met.
type GitConfigurationValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GitConfigurationValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GitConfigurationValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GitConfigurationValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GitConfigurationValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GitConfigurationValidationError) ErrorName() string { return "GitConfigurationValidationError" }

// Error satisfies the builtin error interface
func (e GitConfigurationValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGitConfiguration.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GitConfigurationValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GitConfigurationValidationError{}
