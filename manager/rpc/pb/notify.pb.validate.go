// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: manager/notify.proto

package pb

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on NotifyItem with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *NotifyItem) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on NotifyItem with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in NotifyItemMultiError, or
// nil if none found.
func (m *NotifyItem) ValidateAll() error {
	return m.validate(true)
}

func (m *NotifyItem) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for NotifyMode

	// no validation rules for NotifyType

	// no validation rules for Receiver

	if len(errors) > 0 {
		return NotifyItemMultiError(errors)
	}

	return nil
}

// NotifyItemMultiError is an error wrapping multiple validation errors
// returned by NotifyItem.ValidateAll() if the designated constraints aren't met.
type NotifyItemMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m NotifyItemMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m NotifyItemMultiError) AllErrors() []error { return m }

// NotifyItemValidationError is the validation error returned by
// NotifyItem.Validate if the designated constraints aren't met.
type NotifyItemValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e NotifyItemValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e NotifyItemValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e NotifyItemValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e NotifyItemValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e NotifyItemValidationError) ErrorName() string { return "NotifyItemValidationError" }

// Error satisfies the builtin error interface
func (e NotifyItemValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sNotifyItem.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = NotifyItemValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = NotifyItemValidationError{}

// Validate checks the field values on CreateNotifyItem with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *CreateNotifyItem) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateNotifyItem with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateNotifyItemMultiError, or nil if none found.
func (m *CreateNotifyItem) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateNotifyItem) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetReceiverName()) > 64 {
		err := CreateNotifyItemValidationError{
			field:  "ReceiverName",
			reason: "value length must be at most 64 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_CreateNotifyItem_Receiver_Pattern.MatchString(m.GetReceiver()) {
		err := CreateNotifyItemValidationError{
			field:  "Receiver",
			reason: "value does not match regex pattern \"(?:^https://open\\\\.feishu\\\\.cn/open-apis|[\\\\w]+@[A-Za-z0-9]+(.[A-Za-z0-9]+){1,2})|^oc_[a-z0-9]{32}$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return CreateNotifyItemMultiError(errors)
	}

	return nil
}

// CreateNotifyItemMultiError is an error wrapping multiple validation errors
// returned by CreateNotifyItem.ValidateAll() if the designated constraints
// aren't met.
type CreateNotifyItemMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateNotifyItemMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateNotifyItemMultiError) AllErrors() []error { return m }

// CreateNotifyItemValidationError is the validation error returned by
// CreateNotifyItem.Validate if the designated constraints aren't met.
type CreateNotifyItemValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateNotifyItemValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateNotifyItemValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateNotifyItemValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateNotifyItemValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateNotifyItemValidationError) ErrorName() string { return "CreateNotifyItemValidationError" }

// Error satisfies the builtin error interface
func (e CreateNotifyItemValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateNotifyItem.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateNotifyItemValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateNotifyItemValidationError{}

var _CreateNotifyItem_Receiver_Pattern = regexp.MustCompile("(?:^https://open\\.feishu\\.cn/open-apis|[\\w]+@[A-Za-z0-9]+(.[A-Za-z0-9]+){1,2})|^oc_[a-z0-9]{32}$")

// Validate checks the field values on Notify with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Notify) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Notify with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in NotifyMultiError, or nil if none found.
func (m *Notify) ValidateAll() error {
	return m.validate(true)
}

func (m *Notify) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ProjectId

	// no validation rules for PlanId

	// no validation rules for NotifyId

	// no validation rules for NotifyMode

	// no validation rules for NotifyType

	// no validation rules for ReceiverName

	// no validation rules for Receiver

	// no validation rules for CreatedBy

	// no validation rules for UpdatedBy

	// no validation rules for CreatedAt

	// no validation rules for UpdatedAt

	if len(errors) > 0 {
		return NotifyMultiError(errors)
	}

	return nil
}

// NotifyMultiError is an error wrapping multiple validation errors returned by
// Notify.ValidateAll() if the designated constraints aren't met.
type NotifyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m NotifyMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m NotifyMultiError) AllErrors() []error { return m }

// NotifyValidationError is the validation error returned by Notify.Validate if
// the designated constraints aren't met.
type NotifyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e NotifyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e NotifyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e NotifyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e NotifyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e NotifyValidationError) ErrorName() string { return "NotifyValidationError" }

// Error satisfies the builtin error interface
func (e NotifyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sNotify.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = NotifyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = NotifyValidationError{}
