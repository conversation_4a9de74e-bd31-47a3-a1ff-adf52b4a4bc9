// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: manager/perf_stop_rule.proto

package pb

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	pb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = pb.MetricType(0)
)

// Validate checks the field values on PerfStopRule with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *PerfStopRule) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PerfStopRule with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in PerfStopRuleMultiError, or
// nil if none found.
func (m *PerfStopRule) ValidateAll() error {
	return m.validate(true)
}

func (m *PerfStopRule) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ProjectId

	// no validation rules for RuleId

	// no validation rules for Name

	// no validation rules for Description

	// no validation rules for MetricType

	// no validation rules for Threshold

	// no validation rules for Duration

	// no validation rules for State

	// no validation rules for CreatedBy

	// no validation rules for UpdatedBy

	// no validation rules for CreatedAt

	// no validation rules for UpdatedAt

	if len(errors) > 0 {
		return PerfStopRuleMultiError(errors)
	}

	return nil
}

// PerfStopRuleMultiError is an error wrapping multiple validation errors
// returned by PerfStopRule.ValidateAll() if the designated constraints aren't met.
type PerfStopRuleMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PerfStopRuleMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PerfStopRuleMultiError) AllErrors() []error { return m }

// PerfStopRuleValidationError is the validation error returned by
// PerfStopRule.Validate if the designated constraints aren't met.
type PerfStopRuleValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PerfStopRuleValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PerfStopRuleValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PerfStopRuleValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PerfStopRuleValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PerfStopRuleValidationError) ErrorName() string { return "PerfStopRuleValidationError" }

// Error satisfies the builtin error interface
func (e PerfStopRuleValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPerfStopRule.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PerfStopRuleValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PerfStopRuleValidationError{}
