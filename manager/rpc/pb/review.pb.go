// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.1
// 	protoc        v5.29.2
// source: manager/review.proto

package pb

import (
	reflect "reflect"
	sync "sync"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"

	_ "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// ReviewResourceType 审核资源类型
type ReviewResourceType int32

const (
	ReviewResourceType_RRT_NULL               ReviewResourceType = 0  // NULL
	ReviewResourceType_RRT_COMPONENT_GROUP    ReviewResourceType = 1  // 组件组
	ReviewResourceType_RRT_CASE               ReviewResourceType = 2  // 用例
	ReviewResourceType_RRT_SETUP_COMPONENT    ReviewResourceType = 11 // 前置组件
	ReviewResourceType_RRT_TEARDOWN_COMPONENT ReviewResourceType = 12 // 后置组件
	ReviewResourceType_RRT_BUSINESS_COMPONENT ReviewResourceType = 13 // 业务组件
	ReviewResourceType_RRT_API_CASE           ReviewResourceType = 14 // 场景用例
	ReviewResourceType_RRT_INTERFACE_CASE     ReviewResourceType = 15 // 接口用例
)

// Enum value maps for ReviewResourceType.
var (
	ReviewResourceType_name = map[int32]string{
		0:  "RRT_NULL",
		1:  "RRT_COMPONENT_GROUP",
		2:  "RRT_CASE",
		11: "RRT_SETUP_COMPONENT",
		12: "RRT_TEARDOWN_COMPONENT",
		13: "RRT_BUSINESS_COMPONENT",
		14: "RRT_API_CASE",
		15: "RRT_INTERFACE_CASE",
	}
	ReviewResourceType_value = map[string]int32{
		"RRT_NULL":               0,
		"RRT_COMPONENT_GROUP":    1,
		"RRT_CASE":               2,
		"RRT_SETUP_COMPONENT":    11,
		"RRT_TEARDOWN_COMPONENT": 12,
		"RRT_BUSINESS_COMPONENT": 13,
		"RRT_API_CASE":           14,
		"RRT_INTERFACE_CASE":     15,
	}
)

func (x ReviewResourceType) Enum() *ReviewResourceType {
	p := new(ReviewResourceType)
	*p = x
	return p
}

func (x ReviewResourceType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ReviewResourceType) Descriptor() protoreflect.EnumDescriptor {
	return file_manager_review_proto_enumTypes[0].Descriptor()
}

func (ReviewResourceType) Type() protoreflect.EnumType {
	return &file_manager_review_proto_enumTypes[0]
}

func (x ReviewResourceType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ReviewResourceType.Descriptor instead.
func (ReviewResourceType) EnumDescriptor() ([]byte, []int) {
	return file_manager_review_proto_rawDescGZIP(), []int{0}
}

// ReviewResourceEvent 审核资源事件
type ReviewResourceEvent int32

const (
	ReviewResourceEvent_RRE_NULL                                  ReviewResourceEvent = 0 // NULL
	ReviewResourceEvent_RRE_ASSIGNED_TO_THE_RESPONSIBLE_PERSON    ReviewResourceEvent = 1 // 分配负责人
	ReviewResourceEvent_RRE_APPLY_FOR_REVIEW_AFTER_IMPLEMENTATION ReviewResourceEvent = 2 // 实现后申请审核
	ReviewResourceEvent_RRE_APPLY_FOR_REVIEW_AFTER_MAINTENANCE    ReviewResourceEvent = 3 // 维护后申请审核
	ReviewResourceEvent_RRE_REVIEW_APPROVED                       ReviewResourceEvent = 4 // 审核通过
	ReviewResourceEvent_RRE_REVIEW_REJECTED                       ReviewResourceEvent = 5 // 审核驳回
)

// Enum value maps for ReviewResourceEvent.
var (
	ReviewResourceEvent_name = map[int32]string{
		0: "RRE_NULL",
		1: "RRE_ASSIGNED_TO_THE_RESPONSIBLE_PERSON",
		2: "RRE_APPLY_FOR_REVIEW_AFTER_IMPLEMENTATION",
		3: "RRE_APPLY_FOR_REVIEW_AFTER_MAINTENANCE",
		4: "RRE_REVIEW_APPROVED",
		5: "RRE_REVIEW_REJECTED",
	}
	ReviewResourceEvent_value = map[string]int32{
		"RRE_NULL":                                  0,
		"RRE_ASSIGNED_TO_THE_RESPONSIBLE_PERSON":    1,
		"RRE_APPLY_FOR_REVIEW_AFTER_IMPLEMENTATION": 2,
		"RRE_APPLY_FOR_REVIEW_AFTER_MAINTENANCE":    3,
		"RRE_REVIEW_APPROVED":                       4,
		"RRE_REVIEW_REJECTED":                       5,
	}
)

func (x ReviewResourceEvent) Enum() *ReviewResourceEvent {
	p := new(ReviewResourceEvent)
	*p = x
	return p
}

func (x ReviewResourceEvent) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ReviewResourceEvent) Descriptor() protoreflect.EnumDescriptor {
	return file_manager_review_proto_enumTypes[1].Descriptor()
}

func (ReviewResourceEvent) Type() protoreflect.EnumType {
	return &file_manager_review_proto_enumTypes[1]
}

func (x ReviewResourceEvent) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ReviewResourceEvent.Descriptor instead.
func (ReviewResourceEvent) EnumDescriptor() ([]byte, []int) {
	return file_manager_review_proto_rawDescGZIP(), []int{1}
}

// ReviewStatus 审核状态
type ReviewStatus int32

const (
	ReviewStatus_REVIEW_STATUS_NULL     ReviewStatus = 0 // NULL（避免跟`base.proto:ResourceState.RS_NULL`冲突，因此前缀改为`REVIEW_STATUS_`）
	ReviewStatus_REVIEW_STATUS_PENDING  ReviewStatus = 1 // 待审核
	ReviewStatus_REVIEW_STATUS_REVOKED  ReviewStatus = 2 // 已撤销
	ReviewStatus_REVIEW_STATUS_APPROVED ReviewStatus = 3 // 已通过
	ReviewStatus_REVIEW_STATUS_REJECTED ReviewStatus = 4 // 已驳回
)

// Enum value maps for ReviewStatus.
var (
	ReviewStatus_name = map[int32]string{
		0: "REVIEW_STATUS_NULL",
		1: "REVIEW_STATUS_PENDING",
		2: "REVIEW_STATUS_REVOKED",
		3: "REVIEW_STATUS_APPROVED",
		4: "REVIEW_STATUS_REJECTED",
	}
	ReviewStatus_value = map[string]int32{
		"REVIEW_STATUS_NULL":     0,
		"REVIEW_STATUS_PENDING":  1,
		"REVIEW_STATUS_REVOKED":  2,
		"REVIEW_STATUS_APPROVED": 3,
		"REVIEW_STATUS_REJECTED": 4,
	}
)

func (x ReviewStatus) Enum() *ReviewStatus {
	p := new(ReviewStatus)
	*p = x
	return p
}

func (x ReviewStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ReviewStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_manager_review_proto_enumTypes[2].Descriptor()
}

func (ReviewStatus) Type() protoreflect.EnumType {
	return &file_manager_review_proto_enumTypes[2]
}

func (x ReviewStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ReviewStatus.Descriptor instead.
func (ReviewStatus) EnumDescriptor() ([]byte, []int) {
	return file_manager_review_proto_rawDescGZIP(), []int{2}
}

// ReviewRecord 审核记录
type ReviewRecord struct {
	state              protoimpl.MessageState `protogen:"open.v1"`
	ProjectId          string                 `protobuf:"bytes,1,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`                                                               // 项目ID
	ReviewId           string                 `protobuf:"bytes,2,opt,name=review_id,json=reviewId,proto3" json:"review_id,omitempty"`                                                                  // 审核ID
	ResourceBranch     string                 `protobuf:"bytes,3,opt,name=resource_branch,json=resourceBranch,proto3" json:"resource_branch,omitempty"`                                                // 资源所属分支（分类树ID、接口文档ID）
	ResourceParentType ReviewResourceType     `protobuf:"varint,4,opt,name=resource_parent_type,json=resourceParentType,proto3,enum=manager.ReviewResourceType" json:"resource_parent_type,omitempty"` // 资源父类型（组件组、用例）
	ResourceType       ReviewResourceType     `protobuf:"varint,5,opt,name=resource_type,json=resourceType,proto3,enum=manager.ReviewResourceType" json:"resource_type,omitempty"`                     // 资源类型（业务组件、前置组件、后置组件、场景用例、接口用例）
	ResourceId         string                 `protobuf:"bytes,6,opt,name=resource_id,json=resourceId,proto3" json:"resource_id,omitempty"`                                                            // 资源ID（组件组ID、场景用例ID、接口用例ID）
	ResourceName       string                 `protobuf:"bytes,7,opt,name=resource_name,json=resourceName,proto3" json:"resource_name,omitempty"`                                                      // 资源名称（组件组名称、场景用例名称、接口用例名称）
	RemarkOfPending    string                 `protobuf:"bytes,8,opt,name=remark_of_pending,json=remarkOfPending,proto3" json:"remark_of_pending,omitempty"`                                           // 申请时的备注
	RemarkOfRevoked    string                 `protobuf:"bytes,9,opt,name=remark_of_revoked,json=remarkOfRevoked,proto3" json:"remark_of_revoked,omitempty"`                                           // 撤回时的备注
	RemarkOfReviewed   string                 `protobuf:"bytes,10,opt,name=remark_of_reviewed,json=remarkOfReviewed,proto3" json:"remark_of_reviewed,omitempty"`                                       // 审批时的备注
	AssignedReviewers  []string               `protobuf:"bytes,11,rep,name=assigned_reviewers,json=assignedReviewers,proto3" json:"assigned_reviewers,omitempty"`                                      // 指派的审核者
	Status             ReviewStatus           `protobuf:"varint,12,opt,name=status,proto3,enum=manager.ReviewStatus" json:"status,omitempty"`                                                          // 审核状态
	CreatedBy          string                 `protobuf:"bytes,96,opt,name=created_by,json=createdBy,proto3" json:"created_by,omitempty"`
	UpdatedBy          string                 `protobuf:"bytes,97,opt,name=updated_by,json=updatedBy,proto3" json:"updated_by,omitempty"`
	CreatedAt          int64                  `protobuf:"varint,98,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt          int64                  `protobuf:"varint,99,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *ReviewRecord) Reset() {
	*x = ReviewRecord{}
	mi := &file_manager_review_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ReviewRecord) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReviewRecord) ProtoMessage() {}

func (x *ReviewRecord) ProtoReflect() protoreflect.Message {
	mi := &file_manager_review_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReviewRecord.ProtoReflect.Descriptor instead.
func (*ReviewRecord) Descriptor() ([]byte, []int) {
	return file_manager_review_proto_rawDescGZIP(), []int{0}
}

func (x *ReviewRecord) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *ReviewRecord) GetReviewId() string {
	if x != nil {
		return x.ReviewId
	}
	return ""
}

func (x *ReviewRecord) GetResourceBranch() string {
	if x != nil {
		return x.ResourceBranch
	}
	return ""
}

func (x *ReviewRecord) GetResourceParentType() ReviewResourceType {
	if x != nil {
		return x.ResourceParentType
	}
	return ReviewResourceType_RRT_NULL
}

func (x *ReviewRecord) GetResourceType() ReviewResourceType {
	if x != nil {
		return x.ResourceType
	}
	return ReviewResourceType_RRT_NULL
}

func (x *ReviewRecord) GetResourceId() string {
	if x != nil {
		return x.ResourceId
	}
	return ""
}

func (x *ReviewRecord) GetResourceName() string {
	if x != nil {
		return x.ResourceName
	}
	return ""
}

func (x *ReviewRecord) GetRemarkOfPending() string {
	if x != nil {
		return x.RemarkOfPending
	}
	return ""
}

func (x *ReviewRecord) GetRemarkOfRevoked() string {
	if x != nil {
		return x.RemarkOfRevoked
	}
	return ""
}

func (x *ReviewRecord) GetRemarkOfReviewed() string {
	if x != nil {
		return x.RemarkOfReviewed
	}
	return ""
}

func (x *ReviewRecord) GetAssignedReviewers() []string {
	if x != nil {
		return x.AssignedReviewers
	}
	return nil
}

func (x *ReviewRecord) GetStatus() ReviewStatus {
	if x != nil {
		return x.Status
	}
	return ReviewStatus_REVIEW_STATUS_NULL
}

func (x *ReviewRecord) GetCreatedBy() string {
	if x != nil {
		return x.CreatedBy
	}
	return ""
}

func (x *ReviewRecord) GetUpdatedBy() string {
	if x != nil {
		return x.UpdatedBy
	}
	return ""
}

func (x *ReviewRecord) GetCreatedAt() int64 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

func (x *ReviewRecord) GetUpdatedAt() int64 {
	if x != nil {
		return x.UpdatedAt
	}
	return 0
}

var File_manager_review_proto protoreflect.FileDescriptor

var file_manager_review_proto_rawDesc = []byte{
	0x0a, 0x14, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2f, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x07, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x1a,
	0x16, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xaa, 0x05, 0x0a, 0x0c, 0x52, 0x65, 0x76, 0x69,
	0x65, 0x77, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a,
	0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x72,
	0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x72, 0x65, 0x76, 0x69, 0x65,
	0x77, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x72, 0x65, 0x76, 0x69,
	0x65, 0x77, 0x49, 0x64, 0x12, 0x27, 0x0a, 0x0f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x5f, 0x62, 0x72, 0x61, 0x6e, 0x63, 0x68, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x72,
	0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x42, 0x72, 0x61, 0x6e, 0x63, 0x68, 0x12, 0x4d, 0x0a,
	0x14, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1b, 0x2e, 0x6d, 0x61,
	0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x52, 0x65, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x12, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x50, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x40, 0x0a, 0x0d,
	0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x1b, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x52, 0x65,
	0x76, 0x69, 0x65, 0x77, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65,
	0x52, 0x0c, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1f,
	0x0a, 0x0b, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0a, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x49, 0x64, 0x12,
	0x23, 0x0a, 0x0d, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x2a, 0x0a, 0x11, 0x72, 0x65, 0x6d, 0x61, 0x72, 0x6b, 0x5f, 0x6f,
	0x66, 0x5f, 0x70, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0f, 0x72, 0x65, 0x6d, 0x61, 0x72, 0x6b, 0x4f, 0x66, 0x50, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67,
	0x12, 0x2a, 0x0a, 0x11, 0x72, 0x65, 0x6d, 0x61, 0x72, 0x6b, 0x5f, 0x6f, 0x66, 0x5f, 0x72, 0x65,
	0x76, 0x6f, 0x6b, 0x65, 0x64, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x72, 0x65, 0x6d,
	0x61, 0x72, 0x6b, 0x4f, 0x66, 0x52, 0x65, 0x76, 0x6f, 0x6b, 0x65, 0x64, 0x12, 0x2c, 0x0a, 0x12,
	0x72, 0x65, 0x6d, 0x61, 0x72, 0x6b, 0x5f, 0x6f, 0x66, 0x5f, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77,
	0x65, 0x64, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x72, 0x65, 0x6d, 0x61, 0x72, 0x6b,
	0x4f, 0x66, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x65, 0x64, 0x12, 0x2d, 0x0a, 0x12, 0x61, 0x73,
	0x73, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x5f, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x65, 0x72, 0x73,
	0x18, 0x0b, 0x20, 0x03, 0x28, 0x09, 0x52, 0x11, 0x61, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x65, 0x64,
	0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x65, 0x72, 0x73, 0x12, 0x2d, 0x0a, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x15, 0x2e, 0x6d, 0x61, 0x6e, 0x61,
	0x67, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x64, 0x5f, 0x62, 0x79, 0x18, 0x60, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x64, 0x42, 0x79, 0x12, 0x1d, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x64, 0x5f, 0x62, 0x79, 0x18, 0x61, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x75, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x64, 0x42, 0x79, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x64, 0x5f, 0x61, 0x74, 0x18, 0x62, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64,
	0x5f, 0x61, 0x74, 0x18, 0x63, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x64, 0x41, 0x74, 0x2a, 0xa9, 0x02, 0x0a, 0x12, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x52,
	0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x0c, 0x0a, 0x08, 0x52,
	0x52, 0x54, 0x5f, 0x4e, 0x55, 0x4c, 0x4c, 0x10, 0x00, 0x12, 0x2c, 0x0a, 0x13, 0x52, 0x52, 0x54,
	0x5f, 0x43, 0x4f, 0x4d, 0x50, 0x4f, 0x4e, 0x45, 0x4e, 0x54, 0x5f, 0x47, 0x52, 0x4f, 0x55, 0x50,
	0x10, 0x01, 0x1a, 0x13, 0x82, 0xb5, 0x18, 0x0f, 0x43, 0x4f, 0x4d, 0x50, 0x4f, 0x4e, 0x45, 0x4e,
	0x54, 0x5f, 0x47, 0x52, 0x4f, 0x55, 0x50, 0x12, 0x16, 0x0a, 0x08, 0x52, 0x52, 0x54, 0x5f, 0x43,
	0x41, 0x53, 0x45, 0x10, 0x02, 0x1a, 0x08, 0x82, 0xb5, 0x18, 0x04, 0x43, 0x41, 0x53, 0x45, 0x12,
	0x22, 0x0a, 0x13, 0x52, 0x52, 0x54, 0x5f, 0x53, 0x45, 0x54, 0x55, 0x50, 0x5f, 0x43, 0x4f, 0x4d,
	0x50, 0x4f, 0x4e, 0x45, 0x4e, 0x54, 0x10, 0x0b, 0x1a, 0x09, 0x82, 0xb5, 0x18, 0x05, 0x53, 0x45,
	0x54, 0x55, 0x50, 0x12, 0x28, 0x0a, 0x16, 0x52, 0x52, 0x54, 0x5f, 0x54, 0x45, 0x41, 0x52, 0x44,
	0x4f, 0x57, 0x4e, 0x5f, 0x43, 0x4f, 0x4d, 0x50, 0x4f, 0x4e, 0x45, 0x4e, 0x54, 0x10, 0x0c, 0x1a,
	0x0c, 0x82, 0xb5, 0x18, 0x08, 0x54, 0x45, 0x41, 0x52, 0x44, 0x4f, 0x57, 0x4e, 0x12, 0x25, 0x0a,
	0x16, 0x52, 0x52, 0x54, 0x5f, 0x42, 0x55, 0x53, 0x49, 0x4e, 0x45, 0x53, 0x53, 0x5f, 0x43, 0x4f,
	0x4d, 0x50, 0x4f, 0x4e, 0x45, 0x4e, 0x54, 0x10, 0x0d, 0x1a, 0x09, 0x82, 0xb5, 0x18, 0x05, 0x47,
	0x52, 0x4f, 0x55, 0x50, 0x12, 0x1e, 0x0a, 0x0c, 0x52, 0x52, 0x54, 0x5f, 0x41, 0x50, 0x49, 0x5f,
	0x43, 0x41, 0x53, 0x45, 0x10, 0x0e, 0x1a, 0x0c, 0x82, 0xb5, 0x18, 0x08, 0x41, 0x50, 0x49, 0x5f,
	0x43, 0x41, 0x53, 0x45, 0x12, 0x2a, 0x0a, 0x12, 0x52, 0x52, 0x54, 0x5f, 0x49, 0x4e, 0x54, 0x45,
	0x52, 0x46, 0x41, 0x43, 0x45, 0x5f, 0x43, 0x41, 0x53, 0x45, 0x10, 0x0f, 0x1a, 0x12, 0x82, 0xb5,
	0x18, 0x0e, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x46, 0x41, 0x43, 0x45, 0x5f, 0x43, 0x41, 0x53, 0x45,
	0x2a, 0xf3, 0x02, 0x0a, 0x13, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x52, 0x65, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x12, 0x0c, 0x0a, 0x08, 0x52, 0x52, 0x45, 0x5f,
	0x4e, 0x55, 0x4c, 0x4c, 0x10, 0x00, 0x12, 0x4e, 0x0a, 0x26, 0x52, 0x52, 0x45, 0x5f, 0x41, 0x53,
	0x53, 0x49, 0x47, 0x4e, 0x45, 0x44, 0x5f, 0x54, 0x4f, 0x5f, 0x54, 0x48, 0x45, 0x5f, 0x52, 0x45,
	0x53, 0x50, 0x4f, 0x4e, 0x53, 0x49, 0x42, 0x4c, 0x45, 0x5f, 0x50, 0x45, 0x52, 0x53, 0x4f, 0x4e,
	0x10, 0x01, 0x1a, 0x22, 0x82, 0xb5, 0x18, 0x1e, 0x41, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x65, 0x64,
	0x54, 0x6f, 0x54, 0x68, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x69, 0x62, 0x6c, 0x65,
	0x50, 0x65, 0x72, 0x73, 0x6f, 0x6e, 0x12, 0x54, 0x0a, 0x29, 0x52, 0x52, 0x45, 0x5f, 0x41, 0x50,
	0x50, 0x4c, 0x59, 0x5f, 0x46, 0x4f, 0x52, 0x5f, 0x52, 0x45, 0x56, 0x49, 0x45, 0x57, 0x5f, 0x41,
	0x46, 0x54, 0x45, 0x52, 0x5f, 0x49, 0x4d, 0x50, 0x4c, 0x45, 0x4d, 0x45, 0x4e, 0x54, 0x41, 0x54,
	0x49, 0x4f, 0x4e, 0x10, 0x02, 0x1a, 0x25, 0x82, 0xb5, 0x18, 0x21, 0x41, 0x70, 0x70, 0x6c, 0x79,
	0x46, 0x6f, 0x72, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x41, 0x66, 0x74, 0x65, 0x72, 0x49, 0x6d,
	0x70, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x4e, 0x0a, 0x26,
	0x52, 0x52, 0x45, 0x5f, 0x41, 0x50, 0x50, 0x4c, 0x59, 0x5f, 0x46, 0x4f, 0x52, 0x5f, 0x52, 0x45,
	0x56, 0x49, 0x45, 0x57, 0x5f, 0x41, 0x46, 0x54, 0x45, 0x52, 0x5f, 0x4d, 0x41, 0x49, 0x4e, 0x54,
	0x45, 0x4e, 0x41, 0x4e, 0x43, 0x45, 0x10, 0x03, 0x1a, 0x22, 0x82, 0xb5, 0x18, 0x1e, 0x41, 0x70,
	0x70, 0x6c, 0x79, 0x46, 0x6f, 0x72, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x41, 0x66, 0x74, 0x65,
	0x72, 0x4d, 0x61, 0x69, 0x6e, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x63, 0x65, 0x12, 0x2b, 0x0a, 0x13,
	0x52, 0x52, 0x45, 0x5f, 0x52, 0x45, 0x56, 0x49, 0x45, 0x57, 0x5f, 0x41, 0x50, 0x50, 0x52, 0x4f,
	0x56, 0x45, 0x44, 0x10, 0x04, 0x1a, 0x12, 0x82, 0xb5, 0x18, 0x0e, 0x52, 0x65, 0x76, 0x69, 0x65,
	0x77, 0x41, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x12, 0x2b, 0x0a, 0x13, 0x52, 0x52, 0x45,
	0x5f, 0x52, 0x45, 0x56, 0x49, 0x45, 0x57, 0x5f, 0x52, 0x45, 0x4a, 0x45, 0x43, 0x54, 0x45, 0x44,
	0x10, 0x05, 0x1a, 0x12, 0x82, 0xb5, 0x18, 0x0e, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x52, 0x65,
	0x6a, 0x65, 0x63, 0x74, 0x65, 0x64, 0x2a, 0xca, 0x01, 0x0a, 0x0c, 0x52, 0x65, 0x76, 0x69, 0x65,
	0x77, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x16, 0x0a, 0x12, 0x52, 0x45, 0x56, 0x49, 0x45,
	0x57, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x4e, 0x55, 0x4c, 0x4c, 0x10, 0x00, 0x12,
	0x26, 0x0a, 0x15, 0x52, 0x45, 0x56, 0x49, 0x45, 0x57, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53,
	0x5f, 0x50, 0x45, 0x4e, 0x44, 0x49, 0x4e, 0x47, 0x10, 0x01, 0x1a, 0x0b, 0x82, 0xb5, 0x18, 0x07,
	0x50, 0x45, 0x4e, 0x44, 0x49, 0x4e, 0x47, 0x12, 0x26, 0x0a, 0x15, 0x52, 0x45, 0x56, 0x49, 0x45,
	0x57, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x52, 0x45, 0x56, 0x4f, 0x4b, 0x45, 0x44,
	0x10, 0x02, 0x1a, 0x0b, 0x82, 0xb5, 0x18, 0x07, 0x52, 0x45, 0x56, 0x4f, 0x4b, 0x45, 0x44, 0x12,
	0x28, 0x0a, 0x16, 0x52, 0x45, 0x56, 0x49, 0x45, 0x57, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53,
	0x5f, 0x41, 0x50, 0x50, 0x52, 0x4f, 0x56, 0x45, 0x44, 0x10, 0x03, 0x1a, 0x0c, 0x82, 0xb5, 0x18,
	0x08, 0x41, 0x50, 0x50, 0x52, 0x4f, 0x56, 0x45, 0x44, 0x12, 0x28, 0x0a, 0x16, 0x52, 0x45, 0x56,
	0x49, 0x45, 0x57, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x52, 0x45, 0x4a, 0x45, 0x43,
	0x54, 0x45, 0x44, 0x10, 0x04, 0x1a, 0x0c, 0x82, 0xb5, 0x18, 0x08, 0x52, 0x45, 0x4a, 0x45, 0x43,
	0x54, 0x45, 0x44, 0x42, 0x41, 0x5a, 0x3f, 0x67, 0x69, 0x74, 0x6c, 0x61, 0x62, 0x2e, 0x74, 0x74,
	0x79, 0x75, 0x79, 0x69, 0x6e, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x54, 0x65, 0x73, 0x74, 0x44, 0x65,
	0x76, 0x65, 0x6c, 0x6f, 0x70, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x70, 0x72, 0x6f, 0x62, 0x65, 0x2d,
	0x62, 0x61, 0x63, 0x6b, 0x65, 0x6e, 0x64, 0x2f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2f,
	0x72, 0x70, 0x63, 0x2f, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_manager_review_proto_rawDescOnce sync.Once
	file_manager_review_proto_rawDescData = file_manager_review_proto_rawDesc
)

func file_manager_review_proto_rawDescGZIP() []byte {
	file_manager_review_proto_rawDescOnce.Do(func() {
		file_manager_review_proto_rawDescData = protoimpl.X.CompressGZIP(file_manager_review_proto_rawDescData)
	})
	return file_manager_review_proto_rawDescData
}

var file_manager_review_proto_enumTypes = make([]protoimpl.EnumInfo, 3)
var file_manager_review_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_manager_review_proto_goTypes = []any{
	(ReviewResourceType)(0),  // 0: manager.ReviewResourceType
	(ReviewResourceEvent)(0), // 1: manager.ReviewResourceEvent
	(ReviewStatus)(0),        // 2: manager.ReviewStatus
	(*ReviewRecord)(nil),     // 3: manager.ReviewRecord
}
var file_manager_review_proto_depIdxs = []int32{
	0, // 0: manager.ReviewRecord.resource_parent_type:type_name -> manager.ReviewResourceType
	0, // 1: manager.ReviewRecord.resource_type:type_name -> manager.ReviewResourceType
	2, // 2: manager.ReviewRecord.status:type_name -> manager.ReviewStatus
	3, // [3:3] is the sub-list for method output_type
	3, // [3:3] is the sub-list for method input_type
	3, // [3:3] is the sub-list for extension type_name
	3, // [3:3] is the sub-list for extension extendee
	0, // [0:3] is the sub-list for field type_name
}

func init() { file_manager_review_proto_init() }
func file_manager_review_proto_init() {
	if File_manager_review_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_manager_review_proto_rawDesc,
			NumEnums:      3,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_manager_review_proto_goTypes,
		DependencyIndexes: file_manager_review_proto_depIdxs,
		EnumInfos:         file_manager_review_proto_enumTypes,
		MessageInfos:      file_manager_review_proto_msgTypes,
	}.Build()
	File_manager_review_proto = out.File
	file_manager_review_proto_rawDesc = nil
	file_manager_review_proto_goTypes = nil
	file_manager_review_proto_depIdxs = nil
}
