// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: manager/perfcase.proto

package pb

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	pb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = pb.Protocol(0)
)

// Validate checks the field values on PerfCase with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *PerfCase) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PerfCase with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in PerfCaseMultiError, or nil
// if none found.
func (m *PerfCase) ValidateAll() error {
	return m.validate(true)
}

func (m *PerfCase) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ProjectId

	// no validation rules for CaseId

	// no validation rules for Name

	// no validation rules for Description

	// no validation rules for Extension

	// no validation rules for Hash

	// no validation rules for Size

	// no validation rules for TargetRps

	// no validation rules for InitialRps

	// no validation rules for StepHeight

	// no validation rules for StepDuration

	// no validation rules for PerfDataId

	// no validation rules for NumberOfVu

	// no validation rules for NumberOfLg

	// no validation rules for RequestsOfCpu

	// no validation rules for RequestsOfMemory

	// no validation rules for LimitsOfCpu

	// no validation rules for LimitsOfMemory

	// no validation rules for CreatedBy

	// no validation rules for UpdatedBy

	// no validation rules for CreatedAt

	// no validation rules for UpdatedAt

	if len(errors) > 0 {
		return PerfCaseMultiError(errors)
	}

	return nil
}

// PerfCaseMultiError is an error wrapping multiple validation errors returned
// by PerfCase.ValidateAll() if the designated constraints aren't met.
type PerfCaseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PerfCaseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PerfCaseMultiError) AllErrors() []error { return m }

// PerfCaseValidationError is the validation error returned by
// PerfCase.Validate if the designated constraints aren't met.
type PerfCaseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PerfCaseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PerfCaseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PerfCaseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PerfCaseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PerfCaseValidationError) ErrorName() string { return "PerfCaseValidationError" }

// Error satisfies the builtin error interface
func (e PerfCaseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPerfCase.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PerfCaseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PerfCaseValidationError{}

// Validate checks the field values on PerfCaseV2 with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *PerfCaseV2) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PerfCaseV2 with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in PerfCaseV2MultiError, or
// nil if none found.
func (m *PerfCaseV2) ValidateAll() error {
	return m.validate(true)
}

func (m *PerfCaseV2) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ProjectId

	// no validation rules for CategoryId

	// no validation rules for CaseId

	// no validation rules for Name

	// no validation rules for Description

	// no validation rules for Protocol

	for idx, item := range m.GetRateLimits() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, PerfCaseV2ValidationError{
						field:  fmt.Sprintf("RateLimits[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, PerfCaseV2ValidationError{
						field:  fmt.Sprintf("RateLimits[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return PerfCaseV2ValidationError{
					field:  fmt.Sprintf("RateLimits[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetSetupSteps() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, PerfCaseV2ValidationError{
						field:  fmt.Sprintf("SetupSteps[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, PerfCaseV2ValidationError{
						field:  fmt.Sprintf("SetupSteps[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return PerfCaseV2ValidationError{
					field:  fmt.Sprintf("SetupSteps[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetSerialSteps() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, PerfCaseV2ValidationError{
						field:  fmt.Sprintf("SerialSteps[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, PerfCaseV2ValidationError{
						field:  fmt.Sprintf("SerialSteps[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return PerfCaseV2ValidationError{
					field:  fmt.Sprintf("SerialSteps[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetParallelSteps() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, PerfCaseV2ValidationError{
						field:  fmt.Sprintf("ParallelSteps[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, PerfCaseV2ValidationError{
						field:  fmt.Sprintf("ParallelSteps[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return PerfCaseV2ValidationError{
					field:  fmt.Sprintf("ParallelSteps[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetTeardownSteps() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, PerfCaseV2ValidationError{
						field:  fmt.Sprintf("TeardownSteps[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, PerfCaseV2ValidationError{
						field:  fmt.Sprintf("TeardownSteps[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return PerfCaseV2ValidationError{
					field:  fmt.Sprintf("TeardownSteps[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for NumberOfSteps

	// no validation rules for TargetRps

	// no validation rules for State

	// no validation rules for MaintainedBy

	// no validation rules for CreatedBy

	// no validation rules for UpdatedBy

	// no validation rules for CreatedAt

	// no validation rules for UpdatedAt

	if len(errors) > 0 {
		return PerfCaseV2MultiError(errors)
	}

	return nil
}

// PerfCaseV2MultiError is an error wrapping multiple validation errors
// returned by PerfCaseV2.ValidateAll() if the designated constraints aren't met.
type PerfCaseV2MultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PerfCaseV2MultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PerfCaseV2MultiError) AllErrors() []error { return m }

// PerfCaseV2ValidationError is the validation error returned by
// PerfCaseV2.Validate if the designated constraints aren't met.
type PerfCaseV2ValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PerfCaseV2ValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PerfCaseV2ValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PerfCaseV2ValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PerfCaseV2ValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PerfCaseV2ValidationError) ErrorName() string { return "PerfCaseV2ValidationError" }

// Error satisfies the builtin error interface
func (e PerfCaseV2ValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPerfCaseV2.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PerfCaseV2ValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PerfCaseV2ValidationError{}

// Validate checks the field values on SearchPerfCaseV2Item with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SearchPerfCaseV2Item) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SearchPerfCaseV2Item with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SearchPerfCaseV2ItemMultiError, or nil if none found.
func (m *SearchPerfCaseV2Item) ValidateAll() error {
	return m.validate(true)
}

func (m *SearchPerfCaseV2Item) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ProjectId

	// no validation rules for CategoryId

	// no validation rules for CaseId

	// no validation rules for Name

	// no validation rules for Description

	// no validation rules for Protocol

	for idx, item := range m.GetRateLimits() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SearchPerfCaseV2ItemValidationError{
						field:  fmt.Sprintf("RateLimits[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SearchPerfCaseV2ItemValidationError{
						field:  fmt.Sprintf("RateLimits[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SearchPerfCaseV2ItemValidationError{
					field:  fmt.Sprintf("RateLimits[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetSerialSteps() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SearchPerfCaseV2ItemValidationError{
						field:  fmt.Sprintf("SerialSteps[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SearchPerfCaseV2ItemValidationError{
						field:  fmt.Sprintf("SerialSteps[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SearchPerfCaseV2ItemValidationError{
					field:  fmt.Sprintf("SerialSteps[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetParallelSteps() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SearchPerfCaseV2ItemValidationError{
						field:  fmt.Sprintf("ParallelSteps[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SearchPerfCaseV2ItemValidationError{
						field:  fmt.Sprintf("ParallelSteps[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SearchPerfCaseV2ItemValidationError{
					field:  fmt.Sprintf("ParallelSteps[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for NumberOfSteps

	// no validation rules for State

	// no validation rules for MaintainedBy

	// no validation rules for CreatedBy

	// no validation rules for UpdatedBy

	// no validation rules for CreatedAt

	// no validation rules for UpdatedAt

	if len(errors) > 0 {
		return SearchPerfCaseV2ItemMultiError(errors)
	}

	return nil
}

// SearchPerfCaseV2ItemMultiError is an error wrapping multiple validation
// errors returned by SearchPerfCaseV2Item.ValidateAll() if the designated
// constraints aren't met.
type SearchPerfCaseV2ItemMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SearchPerfCaseV2ItemMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SearchPerfCaseV2ItemMultiError) AllErrors() []error { return m }

// SearchPerfCaseV2ItemValidationError is the validation error returned by
// SearchPerfCaseV2Item.Validate if the designated constraints aren't met.
type SearchPerfCaseV2ItemValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SearchPerfCaseV2ItemValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SearchPerfCaseV2ItemValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SearchPerfCaseV2ItemValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SearchPerfCaseV2ItemValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SearchPerfCaseV2ItemValidationError) ErrorName() string {
	return "SearchPerfCaseV2ItemValidationError"
}

// Error satisfies the builtin error interface
func (e SearchPerfCaseV2ItemValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSearchPerfCaseV2Item.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SearchPerfCaseV2ItemValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SearchPerfCaseV2ItemValidationError{}

// Validate checks the field values on UpdatePerfPlanByCaseTaskInfo with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdatePerfPlanByCaseTaskInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdatePerfPlanByCaseTaskInfo with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdatePerfPlanByCaseTaskInfoMultiError, or nil if none found.
func (m *UpdatePerfPlanByCaseTaskInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdatePerfPlanByCaseTaskInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if !_UpdatePerfPlanByCaseTaskInfo_ProjectId_Pattern.MatchString(m.GetProjectId()) {
		err := UpdatePerfPlanByCaseTaskInfoValidationError{
			field:  "ProjectId",
			reason: "value does not match regex pattern \"(?:^project_id:.+?|^1$)\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_UpdatePerfPlanByCaseTaskInfo_PlanId_Pattern.MatchString(m.GetPlanId()) {
		err := UpdatePerfPlanByCaseTaskInfoValidationError{
			field:  "PlanId",
			reason: "value does not match regex pattern \"(?:^perf_plan_id:.+?)\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_UpdatePerfPlanByCaseTaskInfo_CaseId_Pattern.MatchString(m.GetCaseId()) {
		err := UpdatePerfPlanByCaseTaskInfoValidationError{
			field:  "CaseId",
			reason: "value does not match regex pattern \"(?:^perf_case_id:.+?)\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return UpdatePerfPlanByCaseTaskInfoMultiError(errors)
	}

	return nil
}

// UpdatePerfPlanByCaseTaskInfoMultiError is an error wrapping multiple
// validation errors returned by UpdatePerfPlanByCaseTaskInfo.ValidateAll() if
// the designated constraints aren't met.
type UpdatePerfPlanByCaseTaskInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdatePerfPlanByCaseTaskInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdatePerfPlanByCaseTaskInfoMultiError) AllErrors() []error { return m }

// UpdatePerfPlanByCaseTaskInfoValidationError is the validation error returned
// by UpdatePerfPlanByCaseTaskInfo.Validate if the designated constraints
// aren't met.
type UpdatePerfPlanByCaseTaskInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdatePerfPlanByCaseTaskInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdatePerfPlanByCaseTaskInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdatePerfPlanByCaseTaskInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdatePerfPlanByCaseTaskInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdatePerfPlanByCaseTaskInfoValidationError) ErrorName() string {
	return "UpdatePerfPlanByCaseTaskInfoValidationError"
}

// Error satisfies the builtin error interface
func (e UpdatePerfPlanByCaseTaskInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdatePerfPlanByCaseTaskInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdatePerfPlanByCaseTaskInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdatePerfPlanByCaseTaskInfoValidationError{}

var _UpdatePerfPlanByCaseTaskInfo_ProjectId_Pattern = regexp.MustCompile("(?:^project_id:.+?|^1$)")

var _UpdatePerfPlanByCaseTaskInfo_PlanId_Pattern = regexp.MustCompile("(?:^perf_plan_id:.+?)")

var _UpdatePerfPlanByCaseTaskInfo_CaseId_Pattern = regexp.MustCompile("(?:^perf_case_id:.+?)")
