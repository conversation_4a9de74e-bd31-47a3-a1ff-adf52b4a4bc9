// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: manager/uiplan.proto

package pb

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	pb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = pb.TriggerMode(0)
)

// Validate checks the field values on UiPlan with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *UiPlan) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UiPlan with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in UiPlanMultiError, or nil if none found.
func (m *UiPlan) ValidateAll() error {
	return m.validate(true)
}

func (m *UiPlan) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ProjectId

	// no validation rules for PlanId

	// no validation rules for Name

	// no validation rules for Description

	// no validation rules for Type

	// no validation rules for CronExpression

	// no validation rules for PriorityType

	// no validation rules for GitConfigId

	// no validation rules for ExecutionMode

	// no validation rules for DeviceType

	// no validation rules for PlatformType

	// no validation rules for PackageName

	// no validation rules for CallbackUrl

	// no validation rules for AppDownloadLink

	// no validation rules for AppVersion

	// no validation rules for AppName

	// no validation rules for TestLanguage

	// no validation rules for TestLanguageVersion

	// no validation rules for TestFramework

	// no validation rules for ExecutionEnvironment

	// no validation rules for FailRetry

	// no validation rules for State

	// no validation rules for MaintainedBy

	// no validation rules for CategoryId

	// no validation rules for Together

	// no validation rules for CreatedBy

	// no validation rules for UpdatedBy

	// no validation rules for CreatedAt

	// no validation rules for UpdatedAt

	if len(errors) > 0 {
		return UiPlanMultiError(errors)
	}

	return nil
}

// UiPlanMultiError is an error wrapping multiple validation errors returned by
// UiPlan.ValidateAll() if the designated constraints aren't met.
type UiPlanMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UiPlanMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UiPlanMultiError) AllErrors() []error { return m }

// UiPlanValidationError is the validation error returned by UiPlan.Validate if
// the designated constraints aren't met.
type UiPlanValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UiPlanValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UiPlanValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UiPlanValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UiPlanValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UiPlanValidationError) ErrorName() string { return "UiPlanValidationError" }

// Error satisfies the builtin error interface
func (e UiPlanValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUiPlan.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UiPlanValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UiPlanValidationError{}

// Validate checks the field values on CasePathItem with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *CasePathItem) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CasePathItem with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in CasePathItemMultiError, or
// nil if none found.
func (m *CasePathItem) ValidateAll() error {
	return m.validate(true)
}

func (m *CasePathItem) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if !_CasePathItem_CasePath_Pattern.MatchString(m.GetCasePath()) {
		err := CasePathItemValidationError{
			field:  "CasePath",
			reason: "value does not match regex pattern \"(?:^case_path:.+?)\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return CasePathItemMultiError(errors)
	}

	return nil
}

// CasePathItemMultiError is an error wrapping multiple validation errors
// returned by CasePathItem.ValidateAll() if the designated constraints aren't met.
type CasePathItemMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CasePathItemMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CasePathItemMultiError) AllErrors() []error { return m }

// CasePathItemValidationError is the validation error returned by
// CasePathItem.Validate if the designated constraints aren't met.
type CasePathItemValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CasePathItemValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CasePathItemValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CasePathItemValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CasePathItemValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CasePathItemValidationError) ErrorName() string { return "CasePathItemValidationError" }

// Error satisfies the builtin error interface
func (e CasePathItemValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCasePathItem.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CasePathItemValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CasePathItemValidationError{}

var _CasePathItem_CasePath_Pattern = regexp.MustCompile("(?:^case_path:.+?)")

// Validate checks the field values on Path with the rules defined in the proto
// definition for this message. If any rules are violated, the first error
// encountered is returned, or nil if there are no violations.
func (m *Path) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Path with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in PathMultiError, or nil if none found.
func (m *Path) ValidateAll() error {
	return m.validate(true)
}

func (m *Path) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Path

	if len(errors) > 0 {
		return PathMultiError(errors)
	}

	return nil
}

// PathMultiError is an error wrapping multiple validation errors returned by
// Path.ValidateAll() if the designated constraints aren't met.
type PathMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PathMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PathMultiError) AllErrors() []error { return m }

// PathValidationError is the validation error returned by Path.Validate if the
// designated constraints aren't met.
type PathValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PathValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PathValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PathValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PathValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PathValidationError) ErrorName() string { return "PathValidationError" }

// Error satisfies the builtin error interface
func (e PathValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPath.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PathValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PathValidationError{}

// Validate checks the field values on UICaseTreeNode with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *UICaseTreeNode) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UICaseTreeNode with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in UICaseTreeNodeMultiError,
// or nil if none found.
func (m *UICaseTreeNode) ValidateAll() error {
	return m.validate(true)
}

func (m *UICaseTreeNode) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ProjectId

	// no validation rules for GitConfigId

	// no validation rules for Path

	// no validation rules for ParentPath

	// no validation rules for Name

	// no validation rules for Alias

	// no validation rules for Type

	// no validation rules for CreatedBy

	// no validation rules for UpdatedBy

	// no validation rules for CreatedAt

	// no validation rules for UpdatedAt

	// no validation rules for Amount

	for idx, item := range m.GetChildren() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, UICaseTreeNodeValidationError{
						field:  fmt.Sprintf("Children[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, UICaseTreeNodeValidationError{
						field:  fmt.Sprintf("Children[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return UICaseTreeNodeValidationError{
					field:  fmt.Sprintf("Children[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return UICaseTreeNodeMultiError(errors)
	}

	return nil
}

// UICaseTreeNodeMultiError is an error wrapping multiple validation errors
// returned by UICaseTreeNode.ValidateAll() if the designated constraints
// aren't met.
type UICaseTreeNodeMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UICaseTreeNodeMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UICaseTreeNodeMultiError) AllErrors() []error { return m }

// UICaseTreeNodeValidationError is the validation error returned by
// UICaseTreeNode.Validate if the designated constraints aren't met.
type UICaseTreeNodeValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UICaseTreeNodeValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UICaseTreeNodeValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UICaseTreeNodeValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UICaseTreeNodeValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UICaseTreeNodeValidationError) ErrorName() string { return "UICaseTreeNodeValidationError" }

// Error satisfies the builtin error interface
func (e UICaseTreeNodeValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUICaseTreeNode.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UICaseTreeNodeValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UICaseTreeNodeValidationError{}

// Validate checks the field values on SearchCaseInUIPlanItem with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SearchCaseInUIPlanItem) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SearchCaseInUIPlanItem with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SearchCaseInUIPlanItemMultiError, or nil if none found.
func (m *SearchCaseInUIPlanItem) ValidateAll() error {
	return m.validate(true)
}

func (m *SearchCaseInUIPlanItem) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ProjectId

	// no validation rules for GitConfigId

	// no validation rules for Path

	// no validation rules for ParentPath

	// no validation rules for Name

	// no validation rules for Alias

	// no validation rules for Type

	// no validation rules for CreatedBy

	// no validation rules for UpdatedBy

	// no validation rules for CreatedAt

	// no validation rules for UpdatedAt

	if len(errors) > 0 {
		return SearchCaseInUIPlanItemMultiError(errors)
	}

	return nil
}

// SearchCaseInUIPlanItemMultiError is an error wrapping multiple validation
// errors returned by SearchCaseInUIPlanItem.ValidateAll() if the designated
// constraints aren't met.
type SearchCaseInUIPlanItemMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SearchCaseInUIPlanItemMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SearchCaseInUIPlanItemMultiError) AllErrors() []error { return m }

// SearchCaseInUIPlanItemValidationError is the validation error returned by
// SearchCaseInUIPlanItem.Validate if the designated constraints aren't met.
type SearchCaseInUIPlanItemValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SearchCaseInUIPlanItemValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SearchCaseInUIPlanItemValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SearchCaseInUIPlanItemValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SearchCaseInUIPlanItemValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SearchCaseInUIPlanItemValidationError) ErrorName() string {
	return "SearchCaseInUIPlanItemValidationError"
}

// Error satisfies the builtin error interface
func (e SearchCaseInUIPlanItemValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSearchCaseInUIPlanItem.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SearchCaseInUIPlanItemValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SearchCaseInUIPlanItemValidationError{}

// Validate checks the field values on SearchCaseNotInUIPlanItem with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SearchCaseNotInUIPlanItem) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SearchCaseNotInUIPlanItem with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SearchCaseNotInUIPlanItemMultiError, or nil if none found.
func (m *SearchCaseNotInUIPlanItem) ValidateAll() error {
	return m.validate(true)
}

func (m *SearchCaseNotInUIPlanItem) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ProjectId

	// no validation rules for GitConfigId

	// no validation rules for Path

	// no validation rules for ParentPath

	// no validation rules for Name

	// no validation rules for Alias

	// no validation rules for Type

	// no validation rules for CreatedBy

	// no validation rules for UpdatedBy

	// no validation rules for CreatedAt

	// no validation rules for UpdatedAt

	if len(errors) > 0 {
		return SearchCaseNotInUIPlanItemMultiError(errors)
	}

	return nil
}

// SearchCaseNotInUIPlanItemMultiError is an error wrapping multiple validation
// errors returned by SearchCaseNotInUIPlanItem.ValidateAll() if the
// designated constraints aren't met.
type SearchCaseNotInUIPlanItemMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SearchCaseNotInUIPlanItemMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SearchCaseNotInUIPlanItemMultiError) AllErrors() []error { return m }

// SearchCaseNotInUIPlanItemValidationError is the validation error returned by
// SearchCaseNotInUIPlanItem.Validate if the designated constraints aren't met.
type SearchCaseNotInUIPlanItemValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SearchCaseNotInUIPlanItemValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SearchCaseNotInUIPlanItemValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SearchCaseNotInUIPlanItemValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SearchCaseNotInUIPlanItemValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SearchCaseNotInUIPlanItemValidationError) ErrorName() string {
	return "SearchCaseNotInUIPlanItemValidationError"
}

// Error satisfies the builtin error interface
func (e SearchCaseNotInUIPlanItemValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSearchCaseNotInUIPlanItem.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SearchCaseNotInUIPlanItemValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SearchCaseNotInUIPlanItemValidationError{}
