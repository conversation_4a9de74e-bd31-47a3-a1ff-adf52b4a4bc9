// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.1
// 	protoc        v5.29.2
// source: manager/apicase.proto

package pb

import (
	reflect "reflect"
	sync "sync"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ApiCase struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ProjectId     string                 `protobuf:"bytes,1,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`             // 项目ID
	CategoryId    string                 `protobuf:"bytes,2,opt,name=category_id,json=categoryId,proto3" json:"category_id,omitempty"`          // 所属分类ID
	CaseId        string                 `protobuf:"bytes,3,opt,name=case_id,json=caseId,proto3" json:"case_id,omitempty"`                      // 用例ID
	Name          string                 `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`                                        // 用例名称
	Description   string                 `protobuf:"bytes,5,opt,name=description,proto3" json:"description,omitempty"`                          // 用例描述
	Priority      int64                  `protobuf:"varint,6,opt,name=priority,proto3" json:"priority,omitempty"`                               // 优先级
	Tags          []string               `protobuf:"bytes,7,rep,name=tags,proto3" json:"tags,omitempty"`                                        // 标签
	State         ResourceState          `protobuf:"varint,8,opt,name=state,proto3,enum=manager.ResourceState" json:"state,omitempty"`          // 状态
	AccountConfig *AccountConfig         `protobuf:"bytes,9,opt,name=account_config,json=accountConfig,proto3" json:"account_config,omitempty"` // 池账号配置信息
	Version       string                 `protobuf:"bytes,10,opt,name=version,proto3" json:"version,omitempty"`                                 // 用例版本
	MaintainedBy  string                 `protobuf:"bytes,11,opt,name=maintained_by,json=maintainedBy,proto3" json:"maintained_by,omitempty"`   // 维护者
	CreatedBy     string                 `protobuf:"bytes,12,opt,name=created_by,json=createdBy,proto3" json:"created_by,omitempty"`            // 创建者
	UpdatedBy     string                 `protobuf:"bytes,13,opt,name=updated_by,json=updatedBy,proto3" json:"updated_by,omitempty"`            // 更新者
	CreatedAt     int64                  `protobuf:"varint,14,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`           // 创建时间
	UpdatedAt     int64                  `protobuf:"varint,15,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`           // 更新时间
	Nodes         []*Node                `protobuf:"bytes,16,rep,name=nodes,proto3" json:"nodes,omitempty"`                                     // 点
	Edges         []*Edge                `protobuf:"bytes,17,rep,name=edges,proto3" json:"edges,omitempty"`                                     // 线
	Combos        []*Combo               `protobuf:"bytes,18,rep,name=combos,proto3" json:"combos,omitempty"`                                   // 框
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ApiCase) Reset() {
	*x = ApiCase{}
	mi := &file_manager_apicase_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ApiCase) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ApiCase) ProtoMessage() {}

func (x *ApiCase) ProtoReflect() protoreflect.Message {
	mi := &file_manager_apicase_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ApiCase.ProtoReflect.Descriptor instead.
func (*ApiCase) Descriptor() ([]byte, []int) {
	return file_manager_apicase_proto_rawDescGZIP(), []int{0}
}

func (x *ApiCase) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *ApiCase) GetCategoryId() string {
	if x != nil {
		return x.CategoryId
	}
	return ""
}

func (x *ApiCase) GetCaseId() string {
	if x != nil {
		return x.CaseId
	}
	return ""
}

func (x *ApiCase) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ApiCase) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *ApiCase) GetPriority() int64 {
	if x != nil {
		return x.Priority
	}
	return 0
}

func (x *ApiCase) GetTags() []string {
	if x != nil {
		return x.Tags
	}
	return nil
}

func (x *ApiCase) GetState() ResourceState {
	if x != nil {
		return x.State
	}
	return ResourceState_RS_NULL
}

func (x *ApiCase) GetAccountConfig() *AccountConfig {
	if x != nil {
		return x.AccountConfig
	}
	return nil
}

func (x *ApiCase) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *ApiCase) GetMaintainedBy() string {
	if x != nil {
		return x.MaintainedBy
	}
	return ""
}

func (x *ApiCase) GetCreatedBy() string {
	if x != nil {
		return x.CreatedBy
	}
	return ""
}

func (x *ApiCase) GetUpdatedBy() string {
	if x != nil {
		return x.UpdatedBy
	}
	return ""
}

func (x *ApiCase) GetCreatedAt() int64 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

func (x *ApiCase) GetUpdatedAt() int64 {
	if x != nil {
		return x.UpdatedAt
	}
	return 0
}

func (x *ApiCase) GetNodes() []*Node {
	if x != nil {
		return x.Nodes
	}
	return nil
}

func (x *ApiCase) GetEdges() []*Edge {
	if x != nil {
		return x.Edges
	}
	return nil
}

func (x *ApiCase) GetCombos() []*Combo {
	if x != nil {
		return x.Combos
	}
	return nil
}

type SearchApiCaseReferenceItem struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ProjectId     string                 `protobuf:"bytes,1,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`             // 项目ID
	CaseId        string                 `protobuf:"bytes,2,opt,name=case_id,json=caseId,proto3" json:"case_id,omitempty"`                      // 用例ID
	ReferenceType string                 `protobuf:"bytes,3,opt,name=reference_type,json=referenceType,proto3" json:"reference_type,omitempty"` // 引用对象类型
	ReferenceId   string                 `protobuf:"bytes,4,opt,name=reference_id,json=referenceId,proto3" json:"reference_id,omitempty"`       // 引用对象ID
	Name          string                 `protobuf:"bytes,5,opt,name=name,proto3" json:"name,omitempty"`                                        // 引用对象名称
	Description   string                 `protobuf:"bytes,6,opt,name=description,proto3" json:"description,omitempty"`                          // 引用对象描述
	Priority      int64                  `protobuf:"varint,7,opt,name=priority,proto3" json:"priority,omitempty"`                               // 优先级
	Tags          []string               `protobuf:"bytes,8,rep,name=tags,proto3" json:"tags,omitempty"`                                        // 引用对象标签
	State         CommonState            `protobuf:"varint,9,opt,name=state,proto3,enum=manager.CommonState" json:"state,omitempty"`            // 引用对象状态（API集合的状态）
	MaintainedBy  string                 `protobuf:"bytes,10,opt,name=maintained_by,json=maintainedBy,proto3" json:"maintained_by,omitempty"`   // 维护者
	CreatedBy     string                 `protobuf:"bytes,11,opt,name=created_by,json=createdBy,proto3" json:"created_by,omitempty"`            // 创建者
	UpdatedBy     string                 `protobuf:"bytes,12,opt,name=updated_by,json=updatedBy,proto3" json:"updated_by,omitempty"`            // 更新者
	CreatedAt     int64                  `protobuf:"varint,13,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`           // 创建时间
	UpdatedAt     int64                  `protobuf:"varint,14,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`           // 更新时间
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SearchApiCaseReferenceItem) Reset() {
	*x = SearchApiCaseReferenceItem{}
	mi := &file_manager_apicase_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SearchApiCaseReferenceItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchApiCaseReferenceItem) ProtoMessage() {}

func (x *SearchApiCaseReferenceItem) ProtoReflect() protoreflect.Message {
	mi := &file_manager_apicase_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchApiCaseReferenceItem.ProtoReflect.Descriptor instead.
func (*SearchApiCaseReferenceItem) Descriptor() ([]byte, []int) {
	return file_manager_apicase_proto_rawDescGZIP(), []int{1}
}

func (x *SearchApiCaseReferenceItem) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *SearchApiCaseReferenceItem) GetCaseId() string {
	if x != nil {
		return x.CaseId
	}
	return ""
}

func (x *SearchApiCaseReferenceItem) GetReferenceType() string {
	if x != nil {
		return x.ReferenceType
	}
	return ""
}

func (x *SearchApiCaseReferenceItem) GetReferenceId() string {
	if x != nil {
		return x.ReferenceId
	}
	return ""
}

func (x *SearchApiCaseReferenceItem) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *SearchApiCaseReferenceItem) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *SearchApiCaseReferenceItem) GetPriority() int64 {
	if x != nil {
		return x.Priority
	}
	return 0
}

func (x *SearchApiCaseReferenceItem) GetTags() []string {
	if x != nil {
		return x.Tags
	}
	return nil
}

func (x *SearchApiCaseReferenceItem) GetState() CommonState {
	if x != nil {
		return x.State
	}
	return CommonState_CS_NULL
}

func (x *SearchApiCaseReferenceItem) GetMaintainedBy() string {
	if x != nil {
		return x.MaintainedBy
	}
	return ""
}

func (x *SearchApiCaseReferenceItem) GetCreatedBy() string {
	if x != nil {
		return x.CreatedBy
	}
	return ""
}

func (x *SearchApiCaseReferenceItem) GetUpdatedBy() string {
	if x != nil {
		return x.UpdatedBy
	}
	return ""
}

func (x *SearchApiCaseReferenceItem) GetCreatedAt() int64 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

func (x *SearchApiCaseReferenceItem) GetUpdatedAt() int64 {
	if x != nil {
		return x.UpdatedAt
	}
	return 0
}

var File_manager_apicase_proto protoreflect.FileDescriptor

var file_manager_apicase_proto_rawDesc = []byte{
	0x0a, 0x15, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2f, 0x61, 0x70, 0x69, 0x63, 0x61, 0x73,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x07, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72,
	0x1a, 0x12, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2f, 0x62, 0x61, 0x73, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x15, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2f, 0x65, 0x6c,
	0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xe2, 0x04, 0x0a, 0x07,
	0x41, 0x70, 0x69, 0x43, 0x61, 0x73, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65,
	0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x72, 0x6f,
	0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f,
	0x72, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x61, 0x74,
	0x65, 0x67, 0x6f, 0x72, 0x79, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x63, 0x61, 0x73, 0x65, 0x5f,
	0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x63, 0x61, 0x73, 0x65, 0x49, 0x64,
	0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x72, 0x69, 0x6f, 0x72, 0x69,
	0x74, 0x79, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x70, 0x72, 0x69, 0x6f, 0x72, 0x69,
	0x74, 0x79, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x61, 0x67, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x09,
	0x52, 0x04, 0x74, 0x61, 0x67, 0x73, 0x12, 0x2c, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x16, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e,
	0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x05, 0x73,
	0x74, 0x61, 0x74, 0x65, 0x12, 0x3d, 0x0a, 0x0e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f,
	0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x6d,
	0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x52, 0x0d, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x12, 0x18, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x0a,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x23, 0x0a,
	0x0d, 0x6d, 0x61, 0x69, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x64, 0x5f, 0x62, 0x79, 0x18, 0x0b,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x6d, 0x61, 0x69, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x64,
	0x42, 0x79, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x62, 0x79,
	0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x42,
	0x79, 0x12, 0x1d, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x62, 0x79, 0x18,
	0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x42, 0x79,
	0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0e,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12,
	0x1d, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0f, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x23,
	0x0a, 0x05, 0x6e, 0x6f, 0x64, 0x65, 0x73, 0x18, 0x10, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0d, 0x2e,
	0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x4e, 0x6f, 0x64, 0x65, 0x52, 0x05, 0x6e, 0x6f,
	0x64, 0x65, 0x73, 0x12, 0x23, 0x0a, 0x05, 0x65, 0x64, 0x67, 0x65, 0x73, 0x18, 0x11, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x45, 0x64, 0x67,
	0x65, 0x52, 0x05, 0x65, 0x64, 0x67, 0x65, 0x73, 0x12, 0x26, 0x0a, 0x06, 0x63, 0x6f, 0x6d, 0x62,
	0x6f, 0x73, 0x18, 0x12, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67,
	0x65, 0x72, 0x2e, 0x43, 0x6f, 0x6d, 0x62, 0x6f, 0x52, 0x06, 0x63, 0x6f, 0x6d, 0x62, 0x6f, 0x73,
	0x22, 0xd1, 0x03, 0x0a, 0x1a, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x41, 0x70, 0x69, 0x43, 0x61,
	0x73, 0x65, 0x52, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x12,
	0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x12, 0x17,
	0x0a, 0x07, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x63, 0x61, 0x73, 0x65, 0x49, 0x64, 0x12, 0x25, 0x0a, 0x0e, 0x72, 0x65, 0x66, 0x65, 0x72,
	0x65, 0x6e, 0x63, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0d, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x21,
	0x0a, 0x0c, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x49,
	0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x72, 0x69, 0x6f, 0x72,
	0x69, 0x74, 0x79, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x70, 0x72, 0x69, 0x6f, 0x72,
	0x69, 0x74, 0x79, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x61, 0x67, 0x73, 0x18, 0x08, 0x20, 0x03, 0x28,
	0x09, 0x52, 0x04, 0x74, 0x61, 0x67, 0x73, 0x12, 0x2a, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65,
	0x18, 0x09, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72,
	0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x05, 0x73, 0x74,
	0x61, 0x74, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x6d, 0x61, 0x69, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65,
	0x64, 0x5f, 0x62, 0x79, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x6d, 0x61, 0x69, 0x6e,
	0x74, 0x61, 0x69, 0x6e, 0x65, 0x64, 0x42, 0x79, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x64, 0x5f, 0x62, 0x79, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x64, 0x42, 0x79, 0x12, 0x1d, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x64, 0x5f, 0x62, 0x79, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x75, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x64, 0x42, 0x79, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x64, 0x5f, 0x61, 0x74, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64,
	0x5f, 0x61, 0x74, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x64, 0x41, 0x74, 0x42, 0x41, 0x5a, 0x3f, 0x67, 0x69, 0x74, 0x6c, 0x61, 0x62, 0x2e, 0x74,
	0x74, 0x79, 0x75, 0x79, 0x69, 0x6e, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x54, 0x65, 0x73, 0x74, 0x44,
	0x65, 0x76, 0x65, 0x6c, 0x6f, 0x70, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x70, 0x72, 0x6f, 0x62, 0x65,
	0x2d, 0x62, 0x61, 0x63, 0x6b, 0x65, 0x6e, 0x64, 0x2f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72,
	0x2f, 0x72, 0x70, 0x63, 0x2f, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_manager_apicase_proto_rawDescOnce sync.Once
	file_manager_apicase_proto_rawDescData = file_manager_apicase_proto_rawDesc
)

func file_manager_apicase_proto_rawDescGZIP() []byte {
	file_manager_apicase_proto_rawDescOnce.Do(func() {
		file_manager_apicase_proto_rawDescData = protoimpl.X.CompressGZIP(file_manager_apicase_proto_rawDescData)
	})
	return file_manager_apicase_proto_rawDescData
}

var file_manager_apicase_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_manager_apicase_proto_goTypes = []any{
	(*ApiCase)(nil),                    // 0: manager.ApiCase
	(*SearchApiCaseReferenceItem)(nil), // 1: manager.SearchApiCaseReferenceItem
	(ResourceState)(0),                 // 2: manager.ResourceState
	(*AccountConfig)(nil),              // 3: manager.AccountConfig
	(*Node)(nil),                       // 4: manager.Node
	(*Edge)(nil),                       // 5: manager.Edge
	(*Combo)(nil),                      // 6: manager.Combo
	(CommonState)(0),                   // 7: manager.CommonState
}
var file_manager_apicase_proto_depIdxs = []int32{
	2, // 0: manager.ApiCase.state:type_name -> manager.ResourceState
	3, // 1: manager.ApiCase.account_config:type_name -> manager.AccountConfig
	4, // 2: manager.ApiCase.nodes:type_name -> manager.Node
	5, // 3: manager.ApiCase.edges:type_name -> manager.Edge
	6, // 4: manager.ApiCase.combos:type_name -> manager.Combo
	7, // 5: manager.SearchApiCaseReferenceItem.state:type_name -> manager.CommonState
	6, // [6:6] is the sub-list for method output_type
	6, // [6:6] is the sub-list for method input_type
	6, // [6:6] is the sub-list for extension type_name
	6, // [6:6] is the sub-list for extension extendee
	0, // [0:6] is the sub-list for field type_name
}

func init() { file_manager_apicase_proto_init() }
func file_manager_apicase_proto_init() {
	if File_manager_apicase_proto != nil {
		return
	}
	file_manager_base_proto_init()
	file_manager_element_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_manager_apicase_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_manager_apicase_proto_goTypes,
		DependencyIndexes: file_manager_apicase_proto_depIdxs,
		MessageInfos:      file_manager_apicase_proto_msgTypes,
	}.Build()
	File_manager_apicase_proto = out.File
	file_manager_apicase_proto_rawDesc = nil
	file_manager_apicase_proto_goTypes = nil
	file_manager_apicase_proto_depIdxs = nil
}
