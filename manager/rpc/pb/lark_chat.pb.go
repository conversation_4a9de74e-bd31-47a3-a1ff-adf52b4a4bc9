// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.1
// 	protoc        v5.29.2
// source: manager/lark_chat.proto

package pb

import (
	reflect "reflect"
	sync "sync"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type LarkChat struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ProjectId     string                 `protobuf:"bytes,1,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`   // 项目ID
	ChatId        string                 `protobuf:"bytes,2,opt,name=chat_id,json=chatId,proto3" json:"chat_id,omitempty"`            // 飞书群组ID
	Name          string                 `protobuf:"bytes,11,opt,name=name,proto3" json:"name,omitempty"`                             // 飞书群组名称
	Avatar        string                 `protobuf:"bytes,12,opt,name=avatar,proto3" json:"avatar,omitempty"`                         // 飞书群组头像URL
	Description   string                 `protobuf:"bytes,13,opt,name=description,proto3" json:"description,omitempty"`               // 飞书群组描述
	External      bool                   `protobuf:"varint,14,opt,name=external,proto3" json:"external,omitempty"`                    // 是否是外部群
	Status        string                 `protobuf:"bytes,15,opt,name=status,proto3" json:"status,omitempty"`                         // 飞书群组状态
	CreatedBy     string                 `protobuf:"bytes,96,opt,name=created_by,json=createdBy,proto3" json:"created_by,omitempty"`  // 创建者
	UpdatedBy     string                 `protobuf:"bytes,97,opt,name=updated_by,json=updatedBy,proto3" json:"updated_by,omitempty"`  // 更新者
	CreatedAt     int64                  `protobuf:"varint,98,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"` // 创建时间
	UpdatedAt     int64                  `protobuf:"varint,99,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"` // 更新时间
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *LarkChat) Reset() {
	*x = LarkChat{}
	mi := &file_manager_lark_chat_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LarkChat) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LarkChat) ProtoMessage() {}

func (x *LarkChat) ProtoReflect() protoreflect.Message {
	mi := &file_manager_lark_chat_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LarkChat.ProtoReflect.Descriptor instead.
func (*LarkChat) Descriptor() ([]byte, []int) {
	return file_manager_lark_chat_proto_rawDescGZIP(), []int{0}
}

func (x *LarkChat) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *LarkChat) GetChatId() string {
	if x != nil {
		return x.ChatId
	}
	return ""
}

func (x *LarkChat) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *LarkChat) GetAvatar() string {
	if x != nil {
		return x.Avatar
	}
	return ""
}

func (x *LarkChat) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *LarkChat) GetExternal() bool {
	if x != nil {
		return x.External
	}
	return false
}

func (x *LarkChat) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *LarkChat) GetCreatedBy() string {
	if x != nil {
		return x.CreatedBy
	}
	return ""
}

func (x *LarkChat) GetUpdatedBy() string {
	if x != nil {
		return x.UpdatedBy
	}
	return ""
}

func (x *LarkChat) GetCreatedAt() int64 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

func (x *LarkChat) GetUpdatedAt() int64 {
	if x != nil {
		return x.UpdatedAt
	}
	return 0
}

var File_manager_lark_chat_proto protoreflect.FileDescriptor

var file_manager_lark_chat_proto_rawDesc = []byte{
	0x0a, 0x17, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2f, 0x6c, 0x61, 0x72, 0x6b, 0x5f, 0x63,
	0x68, 0x61, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x07, 0x6d, 0x61, 0x6e, 0x61, 0x67,
	0x65, 0x72, 0x22, 0xc0, 0x02, 0x0a, 0x08, 0x4c, 0x61, 0x72, 0x6b, 0x43, 0x68, 0x61, 0x74, 0x12,
	0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x12, 0x17,
	0x0a, 0x07, 0x63, 0x68, 0x61, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x63, 0x68, 0x61, 0x74, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x61,
	0x76, 0x61, 0x74, 0x61, 0x72, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x61, 0x76, 0x61,
	0x74, 0x61, 0x72, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1a, 0x0a, 0x08, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61,
	0x6c, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61,
	0x6c, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x0f, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x64, 0x5f, 0x62, 0x79, 0x18, 0x60, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x42, 0x79, 0x12, 0x1d, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x64, 0x5f, 0x62, 0x79, 0x18, 0x61, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x75, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x64, 0x42, 0x79, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x62, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x63, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x64, 0x5f, 0x61, 0x74, 0x18, 0x63, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x64, 0x41, 0x74, 0x42, 0x41, 0x5a, 0x3f, 0x67, 0x69, 0x74, 0x6c, 0x61, 0x62, 0x2e,
	0x74, 0x74, 0x79, 0x75, 0x79, 0x69, 0x6e, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x54, 0x65, 0x73, 0x74,
	0x44, 0x65, 0x76, 0x65, 0x6c, 0x6f, 0x70, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x70, 0x72, 0x6f, 0x62,
	0x65, 0x2d, 0x62, 0x61, 0x63, 0x6b, 0x65, 0x6e, 0x64, 0x2f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65,
	0x72, 0x2f, 0x72, 0x70, 0x63, 0x2f, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_manager_lark_chat_proto_rawDescOnce sync.Once
	file_manager_lark_chat_proto_rawDescData = file_manager_lark_chat_proto_rawDesc
)

func file_manager_lark_chat_proto_rawDescGZIP() []byte {
	file_manager_lark_chat_proto_rawDescOnce.Do(func() {
		file_manager_lark_chat_proto_rawDescData = protoimpl.X.CompressGZIP(file_manager_lark_chat_proto_rawDescData)
	})
	return file_manager_lark_chat_proto_rawDescData
}

var file_manager_lark_chat_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_manager_lark_chat_proto_goTypes = []any{
	(*LarkChat)(nil), // 0: manager.LarkChat
}
var file_manager_lark_chat_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_manager_lark_chat_proto_init() }
func file_manager_lark_chat_proto_init() {
	if File_manager_lark_chat_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_manager_lark_chat_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_manager_lark_chat_proto_goTypes,
		DependencyIndexes: file_manager_lark_chat_proto_depIdxs,
		MessageInfos:      file_manager_lark_chat_proto_msgTypes,
	}.Build()
	File_manager_lark_chat_proto = out.File
	file_manager_lark_chat_proto_rawDesc = nil
	file_manager_lark_chat_proto_goTypes = nil
	file_manager_lark_chat_proto_depIdxs = nil
}
