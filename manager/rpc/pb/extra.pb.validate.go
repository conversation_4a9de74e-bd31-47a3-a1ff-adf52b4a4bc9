// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: manager/extra.proto

package pb

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on ApiPlanExtraData with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ApiPlanExtraData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ApiPlanExtraData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ApiPlanExtraDataMultiError, or nil if none found.
func (m *ApiPlanExtraData) ValidateAll() error {
	return m.validate(true)
}

func (m *ApiPlanExtraData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(m.GetServices()) > 0 {

		for idx, item := range m.GetServices() {
			_, _ = idx, item

			if utf8.RuneCountInString(item) < 1 {
				err := ApiPlanExtraDataValidationError{
					field:  fmt.Sprintf("Services[%v]", idx),
					reason: "value length must be at least 1 runes",
				}
				if !all {
					return err
				}
				errors = append(errors, err)
			}

		}

	}

	if len(errors) > 0 {
		return ApiPlanExtraDataMultiError(errors)
	}

	return nil
}

// ApiPlanExtraDataMultiError is an error wrapping multiple validation errors
// returned by ApiPlanExtraData.ValidateAll() if the designated constraints
// aren't met.
type ApiPlanExtraDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ApiPlanExtraDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ApiPlanExtraDataMultiError) AllErrors() []error { return m }

// ApiPlanExtraDataValidationError is the validation error returned by
// ApiPlanExtraData.Validate if the designated constraints aren't met.
type ApiPlanExtraDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ApiPlanExtraDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ApiPlanExtraDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ApiPlanExtraDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ApiPlanExtraDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ApiPlanExtraDataValidationError) ErrorName() string { return "ApiPlanExtraDataValidationError" }

// Error satisfies the builtin error interface
func (e ApiPlanExtraDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sApiPlanExtraData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ApiPlanExtraDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ApiPlanExtraDataValidationError{}

// Validate checks the field values on UiPlanExtraData with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *UiPlanExtraData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UiPlanExtraData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UiPlanExtraDataMultiError, or nil if none found.
func (m *UiPlanExtraData) ValidateAll() error {
	return m.validate(true)
}

func (m *UiPlanExtraData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(m.GetDevices()) > 0 {

		for idx, item := range m.GetDevices() {
			_, _ = idx, item

			if l := utf8.RuneCountInString(item); l < 1 || l > 64 {
				err := UiPlanExtraDataValidationError{
					field:  fmt.Sprintf("Devices[%v]", idx),
					reason: "value length must be between 1 and 64 runes, inclusive",
				}
				if !all {
					return err
				}
				errors = append(errors, err)
			}

		}

	}

	// no validation rules for Together

	if len(errors) > 0 {
		return UiPlanExtraDataMultiError(errors)
	}

	return nil
}

// UiPlanExtraDataMultiError is an error wrapping multiple validation errors
// returned by UiPlanExtraData.ValidateAll() if the designated constraints
// aren't met.
type UiPlanExtraDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UiPlanExtraDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UiPlanExtraDataMultiError) AllErrors() []error { return m }

// UiPlanExtraDataValidationError is the validation error returned by
// UiPlanExtraData.Validate if the designated constraints aren't met.
type UiPlanExtraDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UiPlanExtraDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UiPlanExtraDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UiPlanExtraDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UiPlanExtraDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UiPlanExtraDataValidationError) ErrorName() string { return "UiPlanExtraDataValidationError" }

// Error satisfies the builtin error interface
func (e UiPlanExtraDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUiPlanExtraData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UiPlanExtraDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UiPlanExtraDataValidationError{}
