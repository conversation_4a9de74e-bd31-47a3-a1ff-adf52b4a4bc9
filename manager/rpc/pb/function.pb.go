// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.1
// 	protoc        v5.29.2
// source: manager/function.proto

package pb

import (
	reflect "reflect"
	sync "sync"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// FunctionType 函数类型
type FunctionType int32

const (
	FunctionType_BUILTIN FunctionType = 0 // 内置
	FunctionType_CUSTOM  FunctionType = 1 // 自定义
)

// Enum value maps for FunctionType.
var (
	FunctionType_name = map[int32]string{
		0: "BUILTIN",
		1: "CUSTOM",
	}
	FunctionType_value = map[string]int32{
		"BUILTIN": 0,
		"CUSTOM":  1,
	}
)

func (x FunctionType) Enum() *FunctionType {
	p := new(FunctionType)
	*p = x
	return p
}

func (x FunctionType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (FunctionType) Descriptor() protoreflect.EnumDescriptor {
	return file_manager_function_proto_enumTypes[0].Descriptor()
}

func (FunctionType) Type() protoreflect.EnumType {
	return &file_manager_function_proto_enumTypes[0]
}

func (x FunctionType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use FunctionType.Descriptor instead.
func (FunctionType) EnumDescriptor() ([]byte, []int) {
	return file_manager_function_proto_rawDescGZIP(), []int{0}
}

// CodeLanguage 编程语言
type CodeLanguage int32

const (
	CodeLanguage_GOLANG CodeLanguage = 0 // GoLang
	CodeLanguage_PYTHON CodeLanguage = 1 // Python
)

// Enum value maps for CodeLanguage.
var (
	CodeLanguage_name = map[int32]string{
		0: "GOLANG",
		1: "PYTHON",
	}
	CodeLanguage_value = map[string]int32{
		"GOLANG": 0,
		"PYTHON": 1,
	}
)

func (x CodeLanguage) Enum() *CodeLanguage {
	p := new(CodeLanguage)
	*p = x
	return p
}

func (x CodeLanguage) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CodeLanguage) Descriptor() protoreflect.EnumDescriptor {
	return file_manager_function_proto_enumTypes[1].Descriptor()
}

func (CodeLanguage) Type() protoreflect.EnumType {
	return &file_manager_function_proto_enumTypes[1]
}

func (x CodeLanguage) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CodeLanguage.Descriptor instead.
func (CodeLanguage) EnumDescriptor() ([]byte, []int) {
	return file_manager_function_proto_rawDescGZIP(), []int{1}
}

// ParameterOrReturnType 参数或返回值类型
type ParameterOrReturnType int32

const (
	ParameterOrReturnType_STRING  ParameterOrReturnType = 0 // 字符串
	ParameterOrReturnType_NUMBER  ParameterOrReturnType = 1 // 数字
	ParameterOrReturnType_ARRAY   ParameterOrReturnType = 2 // 列表
	ParameterOrReturnType_OBJECT  ParameterOrReturnType = 3 // 对象
	ParameterOrReturnType_BOOLEAN ParameterOrReturnType = 4 // 布尔
	ParameterOrReturnType_NULL    ParameterOrReturnType = 5 // 空值
	ParameterOrReturnType_ANY     ParameterOrReturnType = 6 // 任意类型
)

// Enum value maps for ParameterOrReturnType.
var (
	ParameterOrReturnType_name = map[int32]string{
		0: "STRING",
		1: "NUMBER",
		2: "ARRAY",
		3: "OBJECT",
		4: "BOOLEAN",
		5: "NULL",
		6: "ANY",
	}
	ParameterOrReturnType_value = map[string]int32{
		"STRING":  0,
		"NUMBER":  1,
		"ARRAY":   2,
		"OBJECT":  3,
		"BOOLEAN": 4,
		"NULL":    5,
		"ANY":     6,
	}
)

func (x ParameterOrReturnType) Enum() *ParameterOrReturnType {
	p := new(ParameterOrReturnType)
	*p = x
	return p
}

func (x ParameterOrReturnType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ParameterOrReturnType) Descriptor() protoreflect.EnumDescriptor {
	return file_manager_function_proto_enumTypes[2].Descriptor()
}

func (ParameterOrReturnType) Type() protoreflect.EnumType {
	return &file_manager_function_proto_enumTypes[2]
}

func (x ParameterOrReturnType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ParameterOrReturnType.Descriptor instead.
func (ParameterOrReturnType) EnumDescriptor() ([]byte, []int) {
	return file_manager_function_proto_rawDescGZIP(), []int{2}
}

type Parameter struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Name          string                 `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`                                     // 参数名称
	Description   string                 `protobuf:"bytes,2,opt,name=description,proto3" json:"description,omitempty"`                       // 参数描述
	Type          ParameterOrReturnType  `protobuf:"varint,3,opt,name=type,proto3,enum=manager.ParameterOrReturnType" json:"type,omitempty"` // 参数类型
	Default       string                 `protobuf:"bytes,4,opt,name=default,proto3" json:"default,omitempty"`                               // 参数默认值
	Variadic      bool                   `protobuf:"varint,5,opt,name=variadic,proto3" json:"variadic,omitempty"`                            // 是否可变参数
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Parameter) Reset() {
	*x = Parameter{}
	mi := &file_manager_function_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Parameter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Parameter) ProtoMessage() {}

func (x *Parameter) ProtoReflect() protoreflect.Message {
	mi := &file_manager_function_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Parameter.ProtoReflect.Descriptor instead.
func (*Parameter) Descriptor() ([]byte, []int) {
	return file_manager_function_proto_rawDescGZIP(), []int{0}
}

func (x *Parameter) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Parameter) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *Parameter) GetType() ParameterOrReturnType {
	if x != nil {
		return x.Type
	}
	return ParameterOrReturnType_STRING
}

func (x *Parameter) GetDefault() string {
	if x != nil {
		return x.Default
	}
	return ""
}

func (x *Parameter) GetVariadic() bool {
	if x != nil {
		return x.Variadic
	}
	return false
}

type Return struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Name          string                 `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`                                     // 返回值名称
	Description   string                 `protobuf:"bytes,2,opt,name=description,proto3" json:"description,omitempty"`                       // 返回值描述
	Type          ParameterOrReturnType  `protobuf:"varint,3,opt,name=type,proto3,enum=manager.ParameterOrReturnType" json:"type,omitempty"` // 返回值类型
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Return) Reset() {
	*x = Return{}
	mi := &file_manager_function_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Return) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Return) ProtoMessage() {}

func (x *Return) ProtoReflect() protoreflect.Message {
	mi := &file_manager_function_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Return.ProtoReflect.Descriptor instead.
func (*Return) Descriptor() ([]byte, []int) {
	return file_manager_function_proto_rawDescGZIP(), []int{1}
}

func (x *Return) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Return) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *Return) GetType() ParameterOrReturnType {
	if x != nil {
		return x.Type
	}
	return ParameterOrReturnType_STRING
}

type DataProcessingFunction struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ProjectId     string                 `protobuf:"bytes,1,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`         // 项目ID
	Name          string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`                                    // 函数名称
	Type          FunctionType           `protobuf:"varint,3,opt,name=type,proto3,enum=manager.FunctionType" json:"type,omitempty"`         // 函数类型
	Category      string                 `protobuf:"bytes,4,opt,name=category,proto3" json:"category,omitempty"`                            // 函数分类
	Description   string                 `protobuf:"bytes,5,opt,name=description,proto3" json:"description,omitempty"`                      // 函数描述
	Language      CodeLanguage           `protobuf:"varint,6,opt,name=language,proto3,enum=manager.CodeLanguage" json:"language,omitempty"` // 编程语言
	Content       string                 `protobuf:"bytes,7,opt,name=content,proto3" json:"content,omitempty"`                              // 函数内容
	Parameters    []*Parameter           `protobuf:"bytes,8,rep,name=parameters,proto3" json:"parameters,omitempty"`                        // 参数列表
	Returns       []*Return              `protobuf:"bytes,9,rep,name=returns,proto3" json:"returns,omitempty"`                              // 返回值列表
	Example       string                 `protobuf:"bytes,10,opt,name=example,proto3" json:"example,omitempty"`                             // 函数使用例子
	Version       string                 `protobuf:"bytes,11,opt,name=version,proto3" json:"version,omitempty"`                             // 函数版本
	CreatedBy     string                 `protobuf:"bytes,12,opt,name=created_by,json=createdBy,proto3" json:"created_by,omitempty"`
	UpdatedBy     string                 `protobuf:"bytes,13,opt,name=updated_by,json=updatedBy,proto3" json:"updated_by,omitempty"`
	CreatedAt     int64                  `protobuf:"varint,14,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt     int64                  `protobuf:"varint,15,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DataProcessingFunction) Reset() {
	*x = DataProcessingFunction{}
	mi := &file_manager_function_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DataProcessingFunction) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DataProcessingFunction) ProtoMessage() {}

func (x *DataProcessingFunction) ProtoReflect() protoreflect.Message {
	mi := &file_manager_function_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DataProcessingFunction.ProtoReflect.Descriptor instead.
func (*DataProcessingFunction) Descriptor() ([]byte, []int) {
	return file_manager_function_proto_rawDescGZIP(), []int{2}
}

func (x *DataProcessingFunction) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *DataProcessingFunction) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *DataProcessingFunction) GetType() FunctionType {
	if x != nil {
		return x.Type
	}
	return FunctionType_BUILTIN
}

func (x *DataProcessingFunction) GetCategory() string {
	if x != nil {
		return x.Category
	}
	return ""
}

func (x *DataProcessingFunction) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *DataProcessingFunction) GetLanguage() CodeLanguage {
	if x != nil {
		return x.Language
	}
	return CodeLanguage_GOLANG
}

func (x *DataProcessingFunction) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *DataProcessingFunction) GetParameters() []*Parameter {
	if x != nil {
		return x.Parameters
	}
	return nil
}

func (x *DataProcessingFunction) GetReturns() []*Return {
	if x != nil {
		return x.Returns
	}
	return nil
}

func (x *DataProcessingFunction) GetExample() string {
	if x != nil {
		return x.Example
	}
	return ""
}

func (x *DataProcessingFunction) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *DataProcessingFunction) GetCreatedBy() string {
	if x != nil {
		return x.CreatedBy
	}
	return ""
}

func (x *DataProcessingFunction) GetUpdatedBy() string {
	if x != nil {
		return x.UpdatedBy
	}
	return ""
}

func (x *DataProcessingFunction) GetCreatedAt() int64 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

func (x *DataProcessingFunction) GetUpdatedAt() int64 {
	if x != nil {
		return x.UpdatedAt
	}
	return 0
}

var File_manager_function_proto protoreflect.FileDescriptor

var file_manager_function_proto_rawDesc = []byte{
	0x0a, 0x16, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2f, 0x66, 0x75, 0x6e, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x07, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65,
	0x72, 0x22, 0xab, 0x01, 0x0a, 0x09, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x12,
	0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x32, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x1e, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x50, 0x61,
	0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x4f, 0x72, 0x52, 0x65, 0x74, 0x75, 0x72, 0x6e, 0x54,
	0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x64, 0x65, 0x66,
	0x61, 0x75, 0x6c, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x64, 0x65, 0x66, 0x61,
	0x75, 0x6c, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x76, 0x61, 0x72, 0x69, 0x61, 0x64, 0x69, 0x63, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x76, 0x61, 0x72, 0x69, 0x61, 0x64, 0x69, 0x63, 0x22,
	0x72, 0x0a, 0x06, 0x52, 0x65, 0x74, 0x75, 0x72, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a,
	0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12,
	0x32, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1e, 0x2e,
	0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65,
	0x72, 0x4f, 0x72, 0x52, 0x65, 0x74, 0x75, 0x72, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74,
	0x79, 0x70, 0x65, 0x22, 0x90, 0x04, 0x0a, 0x16, 0x44, 0x61, 0x74, 0x61, 0x50, 0x72, 0x6f, 0x63,
	0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x46, 0x75, 0x6e, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1d,
	0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x12, 0x12, 0x0a,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x12, 0x29, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x15, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x46, 0x75, 0x6e, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x1a, 0x0a, 0x08,
	0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64,
	0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x31, 0x0a, 0x08, 0x6c, 0x61,
	0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x15, 0x2e, 0x6d,
	0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x43, 0x6f, 0x64, 0x65, 0x4c, 0x61, 0x6e, 0x67, 0x75,
	0x61, 0x67, 0x65, 0x52, 0x08, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x12, 0x18, 0x0a,
	0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x32, 0x0a, 0x0a, 0x70, 0x61, 0x72, 0x61, 0x6d,
	0x65, 0x74, 0x65, 0x72, 0x73, 0x18, 0x08, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x6d, 0x61,
	0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x52,
	0x0a, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x12, 0x29, 0x0a, 0x07, 0x72,
	0x65, 0x74, 0x75, 0x72, 0x6e, 0x73, 0x18, 0x09, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x6d,
	0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x74, 0x75, 0x72, 0x6e, 0x52, 0x07, 0x72,
	0x65, 0x74, 0x75, 0x72, 0x6e, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x65, 0x78, 0x61, 0x6d, 0x70, 0x6c,
	0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x65, 0x78, 0x61, 0x6d, 0x70, 0x6c, 0x65,
	0x12, 0x18, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x0b, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x62, 0x79, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x42, 0x79, 0x12, 0x1d, 0x0a, 0x0a, 0x75, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x64, 0x5f, 0x62, 0x79, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x75,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x42, 0x79, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x63, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x75, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x2a, 0x27, 0x0a, 0x0c, 0x46, 0x75, 0x6e, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x0b, 0x0a, 0x07, 0x42, 0x55, 0x49, 0x4c, 0x54, 0x49,
	0x4e, 0x10, 0x00, 0x12, 0x0a, 0x0a, 0x06, 0x43, 0x55, 0x53, 0x54, 0x4f, 0x4d, 0x10, 0x01, 0x2a,
	0x26, 0x0a, 0x0c, 0x43, 0x6f, 0x64, 0x65, 0x4c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x12,
	0x0a, 0x0a, 0x06, 0x47, 0x4f, 0x4c, 0x41, 0x4e, 0x47, 0x10, 0x00, 0x12, 0x0a, 0x0a, 0x06, 0x50,
	0x59, 0x54, 0x48, 0x4f, 0x4e, 0x10, 0x01, 0x2a, 0x66, 0x0a, 0x15, 0x50, 0x61, 0x72, 0x61, 0x6d,
	0x65, 0x74, 0x65, 0x72, 0x4f, 0x72, 0x52, 0x65, 0x74, 0x75, 0x72, 0x6e, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x0a, 0x0a, 0x06, 0x53, 0x54, 0x52, 0x49, 0x4e, 0x47, 0x10, 0x00, 0x12, 0x0a, 0x0a, 0x06,
	0x4e, 0x55, 0x4d, 0x42, 0x45, 0x52, 0x10, 0x01, 0x12, 0x09, 0x0a, 0x05, 0x41, 0x52, 0x52, 0x41,
	0x59, 0x10, 0x02, 0x12, 0x0a, 0x0a, 0x06, 0x4f, 0x42, 0x4a, 0x45, 0x43, 0x54, 0x10, 0x03, 0x12,
	0x0b, 0x0a, 0x07, 0x42, 0x4f, 0x4f, 0x4c, 0x45, 0x41, 0x4e, 0x10, 0x04, 0x12, 0x08, 0x0a, 0x04,
	0x4e, 0x55, 0x4c, 0x4c, 0x10, 0x05, 0x12, 0x07, 0x0a, 0x03, 0x41, 0x4e, 0x59, 0x10, 0x06, 0x42,
	0x41, 0x5a, 0x3f, 0x67, 0x69, 0x74, 0x6c, 0x61, 0x62, 0x2e, 0x74, 0x74, 0x79, 0x75, 0x79, 0x69,
	0x6e, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x54, 0x65, 0x73, 0x74, 0x44, 0x65, 0x76, 0x65, 0x6c, 0x6f,
	0x70, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x70, 0x72, 0x6f, 0x62, 0x65, 0x2d, 0x62, 0x61, 0x63, 0x6b,
	0x65, 0x6e, 0x64, 0x2f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2f, 0x72, 0x70, 0x63, 0x2f,
	0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_manager_function_proto_rawDescOnce sync.Once
	file_manager_function_proto_rawDescData = file_manager_function_proto_rawDesc
)

func file_manager_function_proto_rawDescGZIP() []byte {
	file_manager_function_proto_rawDescOnce.Do(func() {
		file_manager_function_proto_rawDescData = protoimpl.X.CompressGZIP(file_manager_function_proto_rawDescData)
	})
	return file_manager_function_proto_rawDescData
}

var file_manager_function_proto_enumTypes = make([]protoimpl.EnumInfo, 3)
var file_manager_function_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_manager_function_proto_goTypes = []any{
	(FunctionType)(0),              // 0: manager.FunctionType
	(CodeLanguage)(0),              // 1: manager.CodeLanguage
	(ParameterOrReturnType)(0),     // 2: manager.ParameterOrReturnType
	(*Parameter)(nil),              // 3: manager.Parameter
	(*Return)(nil),                 // 4: manager.Return
	(*DataProcessingFunction)(nil), // 5: manager.DataProcessingFunction
}
var file_manager_function_proto_depIdxs = []int32{
	2, // 0: manager.Parameter.type:type_name -> manager.ParameterOrReturnType
	2, // 1: manager.Return.type:type_name -> manager.ParameterOrReturnType
	0, // 2: manager.DataProcessingFunction.type:type_name -> manager.FunctionType
	1, // 3: manager.DataProcessingFunction.language:type_name -> manager.CodeLanguage
	3, // 4: manager.DataProcessingFunction.parameters:type_name -> manager.Parameter
	4, // 5: manager.DataProcessingFunction.returns:type_name -> manager.Return
	6, // [6:6] is the sub-list for method output_type
	6, // [6:6] is the sub-list for method input_type
	6, // [6:6] is the sub-list for extension type_name
	6, // [6:6] is the sub-list for extension extendee
	0, // [0:6] is the sub-list for field type_name
}

func init() { file_manager_function_proto_init() }
func file_manager_function_proto_init() {
	if File_manager_function_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_manager_function_proto_rawDesc,
			NumEnums:      3,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_manager_function_proto_goTypes,
		DependencyIndexes: file_manager_function_proto_depIdxs,
		EnumInfos:         file_manager_function_proto_enumTypes,
		MessageInfos:      file_manager_function_proto_msgTypes,
	}.Build()
	File_manager_function_proto = out.File
	file_manager_function_proto_rawDesc = nil
	file_manager_function_proto_goTypes = nil
	file_manager_function_proto_depIdxs = nil
}
