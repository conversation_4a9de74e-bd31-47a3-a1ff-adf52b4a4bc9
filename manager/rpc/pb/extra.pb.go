// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.1
// 	protoc        v5.29.2
// source: manager/extra.proto

package pb

import (
	reflect "reflect"
	sync "sync"

	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ApiPlanExtraData struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Services      []string               `protobuf:"bytes,1,rep,name=services,proto3" json:"services,omitempty"` // 服务列表，用于精准测试
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ApiPlanExtraData) Reset() {
	*x = ApiPlanExtraData{}
	mi := &file_manager_extra_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ApiPlanExtraData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ApiPlanExtraData) ProtoMessage() {}

func (x *ApiPlanExtraData) ProtoReflect() protoreflect.Message {
	mi := &file_manager_extra_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ApiPlanExtraData.ProtoReflect.Descriptor instead.
func (*ApiPlanExtraData) Descriptor() ([]byte, []int) {
	return file_manager_extra_proto_rawDescGZIP(), []int{0}
}

func (x *ApiPlanExtraData) GetServices() []string {
	if x != nil {
		return x.Services
	}
	return nil
}

type UiPlanExtraData struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Devices       []string               `protobuf:"bytes,1,rep,name=devices,proto3" json:"devices,omitempty"`    // 设备列表
	Together      bool                   `protobuf:"varint,2,opt,name=together,proto3" json:"together,omitempty"` // 选择的设备是否一起执行
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UiPlanExtraData) Reset() {
	*x = UiPlanExtraData{}
	mi := &file_manager_extra_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UiPlanExtraData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UiPlanExtraData) ProtoMessage() {}

func (x *UiPlanExtraData) ProtoReflect() protoreflect.Message {
	mi := &file_manager_extra_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UiPlanExtraData.ProtoReflect.Descriptor instead.
func (*UiPlanExtraData) Descriptor() ([]byte, []int) {
	return file_manager_extra_proto_rawDescGZIP(), []int{1}
}

func (x *UiPlanExtraData) GetDevices() []string {
	if x != nil {
		return x.Devices
	}
	return nil
}

func (x *UiPlanExtraData) GetTogether() bool {
	if x != nil {
		return x.Together
	}
	return false
}

var File_manager_extra_proto protoreflect.FileDescriptor

var file_manager_extra_proto_rawDesc = []byte{
	0x0a, 0x13, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2f, 0x65, 0x78, 0x74, 0x72, 0x61, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x07, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x1a, 0x17,
	0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x3e, 0x0a, 0x10, 0x41, 0x70, 0x69, 0x50, 0x6c,
	0x61, 0x6e, 0x45, 0x78, 0x74, 0x72, 0x61, 0x44, 0x61, 0x74, 0x61, 0x12, 0x2a, 0x0a, 0x08, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x42, 0x0e, 0xfa,
	0x42, 0x0b, 0x92, 0x01, 0x08, 0x22, 0x04, 0x72, 0x02, 0x10, 0x01, 0x28, 0x01, 0x52, 0x08, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x22, 0x59, 0x0a, 0x0f, 0x55, 0x69, 0x50, 0x6c, 0x61,
	0x6e, 0x45, 0x78, 0x74, 0x72, 0x61, 0x44, 0x61, 0x74, 0x61, 0x12, 0x2a, 0x0a, 0x07, 0x64, 0x65,
	0x76, 0x69, 0x63, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x42, 0x10, 0xfa, 0x42, 0x0d,
	0x92, 0x01, 0x0a, 0x22, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x40, 0x28, 0x01, 0x52, 0x07, 0x64,
	0x65, 0x76, 0x69, 0x63, 0x65, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x74, 0x6f, 0x67, 0x65, 0x74, 0x68,
	0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x74, 0x6f, 0x67, 0x65, 0x74, 0x68,
	0x65, 0x72, 0x42, 0x41, 0x5a, 0x3f, 0x67, 0x69, 0x74, 0x6c, 0x61, 0x62, 0x2e, 0x74, 0x74, 0x79,
	0x75, 0x79, 0x69, 0x6e, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x54, 0x65, 0x73, 0x74, 0x44, 0x65, 0x76,
	0x65, 0x6c, 0x6f, 0x70, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x70, 0x72, 0x6f, 0x62, 0x65, 0x2d, 0x62,
	0x61, 0x63, 0x6b, 0x65, 0x6e, 0x64, 0x2f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2f, 0x72,
	0x70, 0x63, 0x2f, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_manager_extra_proto_rawDescOnce sync.Once
	file_manager_extra_proto_rawDescData = file_manager_extra_proto_rawDesc
)

func file_manager_extra_proto_rawDescGZIP() []byte {
	file_manager_extra_proto_rawDescOnce.Do(func() {
		file_manager_extra_proto_rawDescData = protoimpl.X.CompressGZIP(file_manager_extra_proto_rawDescData)
	})
	return file_manager_extra_proto_rawDescData
}

var file_manager_extra_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_manager_extra_proto_goTypes = []any{
	(*ApiPlanExtraData)(nil), // 0: manager.ApiPlanExtraData
	(*UiPlanExtraData)(nil),  // 1: manager.UiPlanExtraData
}
var file_manager_extra_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_manager_extra_proto_init() }
func file_manager_extra_proto_init() {
	if File_manager_extra_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_manager_extra_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_manager_extra_proto_goTypes,
		DependencyIndexes: file_manager_extra_proto_depIdxs,
		MessageInfos:      file_manager_extra_proto_msgTypes,
	}.Build()
	File_manager_extra_proto = out.File
	file_manager_extra_proto_rawDesc = nil
	file_manager_extra_proto_goTypes = nil
	file_manager_extra_proto_depIdxs = nil
}
