// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: manager/project_device.proto

package pb

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	pb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = pb.DeviceUsage(0)
)

// Validate checks the field values on BindDevice with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *BindDevice) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BindDevice with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in BindDeviceMultiError, or
// nil if none found.
func (m *BindDevice) ValidateAll() error {
	return m.validate(true)
}

func (m *BindDevice) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if l := utf8.RuneCountInString(m.GetUdid()); l < 1 || l > 64 {
		err := BindDeviceValidationError{
			field:  "Udid",
			reason: "value length must be between 1 and 64 runes, inclusive",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if _, ok := _BindDevice_Usage_NotInLookup[m.GetUsage()]; ok {
		err := BindDeviceValidationError{
			field:  "Usage",
			reason: "value must not be in list [DU_NULL]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return BindDeviceMultiError(errors)
	}

	return nil
}

// BindDeviceMultiError is an error wrapping multiple validation errors
// returned by BindDevice.ValidateAll() if the designated constraints aren't met.
type BindDeviceMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BindDeviceMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BindDeviceMultiError) AllErrors() []error { return m }

// BindDeviceValidationError is the validation error returned by
// BindDevice.Validate if the designated constraints aren't met.
type BindDeviceValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BindDeviceValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BindDeviceValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BindDeviceValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BindDeviceValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BindDeviceValidationError) ErrorName() string { return "BindDeviceValidationError" }

// Error satisfies the builtin error interface
func (e BindDeviceValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBindDevice.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BindDeviceValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BindDeviceValidationError{}

var _BindDevice_Usage_NotInLookup = map[pb.DeviceUsage]struct{}{
	0: {},
}

// Validate checks the field values on DeviceRelationship with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DeviceRelationship) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeviceRelationship with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeviceRelationshipMultiError, or nil if none found.
func (m *DeviceRelationship) ValidateAll() error {
	return m.validate(true)
}

func (m *DeviceRelationship) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if l := utf8.RuneCountInString(m.GetUdid()); l < 1 || l > 64 {
		err := DeviceRelationshipValidationError{
			field:  "Udid",
			reason: "value length must be between 1 and 64 runes, inclusive",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetCount() < 0 {
		err := DeviceRelationshipValidationError{
			field:  "Count",
			reason: "value must be greater than or equal to 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return DeviceRelationshipMultiError(errors)
	}

	return nil
}

// DeviceRelationshipMultiError is an error wrapping multiple validation errors
// returned by DeviceRelationship.ValidateAll() if the designated constraints
// aren't met.
type DeviceRelationshipMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeviceRelationshipMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeviceRelationshipMultiError) AllErrors() []error { return m }

// DeviceRelationshipValidationError is the validation error returned by
// DeviceRelationship.Validate if the designated constraints aren't met.
type DeviceRelationshipValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeviceRelationshipValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeviceRelationshipValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeviceRelationshipValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeviceRelationshipValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeviceRelationshipValidationError) ErrorName() string {
	return "DeviceRelationshipValidationError"
}

// Error satisfies the builtin error interface
func (e DeviceRelationshipValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeviceRelationship.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeviceRelationshipValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeviceRelationshipValidationError{}

// Validate checks the field values on ProjectDevice with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ProjectDevice) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ProjectDevice with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ProjectDeviceMultiError, or
// nil if none found.
func (m *ProjectDevice) ValidateAll() error {
	return m.validate(true)
}

func (m *ProjectDevice) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetDevice()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProjectDeviceValidationError{
					field:  "Device",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProjectDeviceValidationError{
					field:  "Device",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDevice()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProjectDeviceValidationError{
				field:  "Device",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ProjectId

	// no validation rules for Usage

	if len(errors) > 0 {
		return ProjectDeviceMultiError(errors)
	}

	return nil
}

// ProjectDeviceMultiError is an error wrapping multiple validation errors
// returned by ProjectDevice.ValidateAll() if the designated constraints
// aren't met.
type ProjectDeviceMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProjectDeviceMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProjectDeviceMultiError) AllErrors() []error { return m }

// ProjectDeviceValidationError is the validation error returned by
// ProjectDevice.Validate if the designated constraints aren't met.
type ProjectDeviceValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProjectDeviceValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProjectDeviceValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ProjectDeviceValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProjectDeviceValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProjectDeviceValidationError) ErrorName() string { return "ProjectDeviceValidationError" }

// Error satisfies the builtin error interface
func (e ProjectDeviceValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProjectDevice.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProjectDeviceValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProjectDeviceValidationError{}

// Validate checks the field values on SearchProjectDeviceReferenceItem with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *SearchProjectDeviceReferenceItem) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SearchProjectDeviceReferenceItem with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// SearchProjectDeviceReferenceItemMultiError, or nil if none found.
func (m *SearchProjectDeviceReferenceItem) ValidateAll() error {
	return m.validate(true)
}

func (m *SearchProjectDeviceReferenceItem) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ProjectId

	// no validation rules for Udid

	// no validation rules for ReferenceType

	// no validation rules for ReferenceId

	// no validation rules for CategoryId

	// no validation rules for Name

	// no validation rules for Description

	// no validation rules for MaintainedBy

	// no validation rules for CreatedBy

	// no validation rules for UpdatedBy

	// no validation rules for CreatedAt

	// no validation rules for UpdatedAt

	if len(errors) > 0 {
		return SearchProjectDeviceReferenceItemMultiError(errors)
	}

	return nil
}

// SearchProjectDeviceReferenceItemMultiError is an error wrapping multiple
// validation errors returned by
// SearchProjectDeviceReferenceItem.ValidateAll() if the designated
// constraints aren't met.
type SearchProjectDeviceReferenceItemMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SearchProjectDeviceReferenceItemMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SearchProjectDeviceReferenceItemMultiError) AllErrors() []error { return m }

// SearchProjectDeviceReferenceItemValidationError is the validation error
// returned by SearchProjectDeviceReferenceItem.Validate if the designated
// constraints aren't met.
type SearchProjectDeviceReferenceItemValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SearchProjectDeviceReferenceItemValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SearchProjectDeviceReferenceItemValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SearchProjectDeviceReferenceItemValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SearchProjectDeviceReferenceItemValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SearchProjectDeviceReferenceItemValidationError) ErrorName() string {
	return "SearchProjectDeviceReferenceItemValidationError"
}

// Error satisfies the builtin error interface
func (e SearchProjectDeviceReferenceItemValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSearchProjectDeviceReferenceItem.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SearchProjectDeviceReferenceItemValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SearchProjectDeviceReferenceItemValidationError{}

// Validate checks the field values on DeleteDisabledDeviceTaskInfo with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DeleteDisabledDeviceTaskInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteDisabledDeviceTaskInfo with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeleteDisabledDeviceTaskInfoMultiError, or nil if none found.
func (m *DeleteDisabledDeviceTaskInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteDisabledDeviceTaskInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if !_DeleteDisabledDeviceTaskInfo_ProjectId_Pattern.MatchString(m.GetProjectId()) {
		err := DeleteDisabledDeviceTaskInfoValidationError{
			field:  "ProjectId",
			reason: "value does not match regex pattern \"(?:^project_id:.+?|^1$)\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return DeleteDisabledDeviceTaskInfoMultiError(errors)
	}

	return nil
}

// DeleteDisabledDeviceTaskInfoMultiError is an error wrapping multiple
// validation errors returned by DeleteDisabledDeviceTaskInfo.ValidateAll() if
// the designated constraints aren't met.
type DeleteDisabledDeviceTaskInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteDisabledDeviceTaskInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteDisabledDeviceTaskInfoMultiError) AllErrors() []error { return m }

// DeleteDisabledDeviceTaskInfoValidationError is the validation error returned
// by DeleteDisabledDeviceTaskInfo.Validate if the designated constraints
// aren't met.
type DeleteDisabledDeviceTaskInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteDisabledDeviceTaskInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteDisabledDeviceTaskInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteDisabledDeviceTaskInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteDisabledDeviceTaskInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteDisabledDeviceTaskInfoValidationError) ErrorName() string {
	return "DeleteDisabledDeviceTaskInfoValidationError"
}

// Error satisfies the builtin error interface
func (e DeleteDisabledDeviceTaskInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteDisabledDeviceTaskInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteDisabledDeviceTaskInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteDisabledDeviceTaskInfoValidationError{}

var _DeleteDisabledDeviceTaskInfo_ProjectId_Pattern = regexp.MustCompile("(?:^project_id:.+?|^1$)")
