// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: manager/function.proto

package pb

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on Parameter with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Parameter) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Parameter with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ParameterMultiError, or nil
// if none found.
func (m *Parameter) ValidateAll() error {
	return m.validate(true)
}

func (m *Parameter) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Name

	// no validation rules for Description

	// no validation rules for Type

	// no validation rules for Default

	// no validation rules for Variadic

	if len(errors) > 0 {
		return ParameterMultiError(errors)
	}

	return nil
}

// ParameterMultiError is an error wrapping multiple validation errors returned
// by Parameter.ValidateAll() if the designated constraints aren't met.
type ParameterMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ParameterMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ParameterMultiError) AllErrors() []error { return m }

// ParameterValidationError is the validation error returned by
// Parameter.Validate if the designated constraints aren't met.
type ParameterValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ParameterValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ParameterValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ParameterValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ParameterValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ParameterValidationError) ErrorName() string { return "ParameterValidationError" }

// Error satisfies the builtin error interface
func (e ParameterValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sParameter.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ParameterValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ParameterValidationError{}

// Validate checks the field values on Return with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Return) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Return with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in ReturnMultiError, or nil if none found.
func (m *Return) ValidateAll() error {
	return m.validate(true)
}

func (m *Return) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Name

	// no validation rules for Description

	// no validation rules for Type

	if len(errors) > 0 {
		return ReturnMultiError(errors)
	}

	return nil
}

// ReturnMultiError is an error wrapping multiple validation errors returned by
// Return.ValidateAll() if the designated constraints aren't met.
type ReturnMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ReturnMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ReturnMultiError) AllErrors() []error { return m }

// ReturnValidationError is the validation error returned by Return.Validate if
// the designated constraints aren't met.
type ReturnValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ReturnValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ReturnValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ReturnValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ReturnValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ReturnValidationError) ErrorName() string { return "ReturnValidationError" }

// Error satisfies the builtin error interface
func (e ReturnValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sReturn.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ReturnValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ReturnValidationError{}

// Validate checks the field values on DataProcessingFunction with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DataProcessingFunction) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DataProcessingFunction with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DataProcessingFunctionMultiError, or nil if none found.
func (m *DataProcessingFunction) ValidateAll() error {
	return m.validate(true)
}

func (m *DataProcessingFunction) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ProjectId

	// no validation rules for Name

	// no validation rules for Type

	// no validation rules for Category

	// no validation rules for Description

	// no validation rules for Language

	// no validation rules for Content

	for idx, item := range m.GetParameters() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, DataProcessingFunctionValidationError{
						field:  fmt.Sprintf("Parameters[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, DataProcessingFunctionValidationError{
						field:  fmt.Sprintf("Parameters[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return DataProcessingFunctionValidationError{
					field:  fmt.Sprintf("Parameters[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetReturns() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, DataProcessingFunctionValidationError{
						field:  fmt.Sprintf("Returns[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, DataProcessingFunctionValidationError{
						field:  fmt.Sprintf("Returns[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return DataProcessingFunctionValidationError{
					field:  fmt.Sprintf("Returns[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for Example

	// no validation rules for Version

	// no validation rules for CreatedBy

	// no validation rules for UpdatedBy

	// no validation rules for CreatedAt

	// no validation rules for UpdatedAt

	if len(errors) > 0 {
		return DataProcessingFunctionMultiError(errors)
	}

	return nil
}

// DataProcessingFunctionMultiError is an error wrapping multiple validation
// errors returned by DataProcessingFunction.ValidateAll() if the designated
// constraints aren't met.
type DataProcessingFunctionMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DataProcessingFunctionMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DataProcessingFunctionMultiError) AllErrors() []error { return m }

// DataProcessingFunctionValidationError is the validation error returned by
// DataProcessingFunction.Validate if the designated constraints aren't met.
type DataProcessingFunctionValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DataProcessingFunctionValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DataProcessingFunctionValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DataProcessingFunctionValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DataProcessingFunctionValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DataProcessingFunctionValidationError) ErrorName() string {
	return "DataProcessingFunctionValidationError"
}

// Error satisfies the builtin error interface
func (e DataProcessingFunctionValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDataProcessingFunction.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DataProcessingFunctionValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DataProcessingFunctionValidationError{}
