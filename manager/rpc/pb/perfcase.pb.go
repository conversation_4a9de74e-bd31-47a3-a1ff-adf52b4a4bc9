// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.1
// 	protoc        v5.29.2
// source: manager/perfcase.proto

package pb

import (
	reflect "reflect"
	sync "sync"

	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"

	pb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type PerfCase struct {
	state       protoimpl.MessageState `protogen:"open.v1"`
	ProjectId   string                 `protobuf:"bytes,1,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"` // 项目ID
	CaseId      string                 `protobuf:"bytes,2,opt,name=case_id,json=caseId,proto3" json:"case_id,omitempty"`          // 压测用例ID
	Name        string                 `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`                            // 压测用例名称
	Description string                 `protobuf:"bytes,4,opt,name=description,proto3" json:"description,omitempty"`              // 压测用例描述
	Extension   string                 `protobuf:"bytes,5,opt,name=extension,proto3" json:"extension,omitempty"`                  // 压测用例文件的扩展名
	Hash        string                 `protobuf:"bytes,6,opt,name=hash,proto3" json:"hash,omitempty"`                            // 压测用例文件的一致性哈希值
	Size        uint32                 `protobuf:"varint,7,opt,name=size,proto3" json:"size,omitempty"`                           // 压测用例文件的大小
	// 限流配置
	TargetRps    int64 `protobuf:"varint,11,opt,name=target_rps,json=targetRps,proto3" json:"target_rps,omitempty"`          // 目标的RPS
	InitialRps   int64 `protobuf:"varint,12,opt,name=initial_rps,json=initialRps,proto3" json:"initial_rps,omitempty"`       // 初始的RPS
	StepHeight   int64 `protobuf:"varint,13,opt,name=step_height,json=stepHeight,proto3" json:"step_height,omitempty"`       // 每次改变RPS的量
	StepDuration int64 `protobuf:"varint,14,opt,name=step_duration,json=stepDuration,proto3" json:"step_duration,omitempty"` // 改变后的RPS的持续时间
	// 压测数据配置
	PerfDataId string `protobuf:"bytes,21,opt,name=perf_data_id,json=perfDataId,proto3" json:"perf_data_id,omitempty"`  // 压测数据ID
	NumberOfVu uint32 `protobuf:"varint,22,opt,name=number_of_vu,json=numberOfVu,proto3" json:"number_of_vu,omitempty"` // 虚拟用户数
	// 施压机资源配置
	NumberOfLg       uint32 `protobuf:"varint,31,opt,name=number_of_lg,json=numberOfLg,proto3" json:"number_of_lg,omitempty"`                  // 施压机数量
	RequestsOfCpu    string `protobuf:"bytes,32,opt,name=requests_of_cpu,json=requestsOfCpu,proto3" json:"requests_of_cpu,omitempty"`          // 最小分配的CPU资源
	RequestsOfMemory string `protobuf:"bytes,33,opt,name=requests_of_memory,json=requestsOfMemory,proto3" json:"requests_of_memory,omitempty"` // 最小分配的内存资源
	LimitsOfCpu      string `protobuf:"bytes,34,opt,name=limits_of_cpu,json=limitsOfCpu,proto3" json:"limits_of_cpu,omitempty"`                // 最大分配的CPU资源
	LimitsOfMemory   string `protobuf:"bytes,35,opt,name=limits_of_memory,json=limitsOfMemory,proto3" json:"limits_of_memory,omitempty"`       // 最大分配的内存资源
	CreatedBy        string `protobuf:"bytes,96,opt,name=created_by,json=createdBy,proto3" json:"created_by,omitempty"`                        // 创建者
	UpdatedBy        string `protobuf:"bytes,97,opt,name=updated_by,json=updatedBy,proto3" json:"updated_by,omitempty"`                        // 更新者
	CreatedAt        int64  `protobuf:"varint,98,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`                       // 创建时间
	UpdatedAt        int64  `protobuf:"varint,99,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`                       // 更新时间
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *PerfCase) Reset() {
	*x = PerfCase{}
	mi := &file_manager_perfcase_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PerfCase) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PerfCase) ProtoMessage() {}

func (x *PerfCase) ProtoReflect() protoreflect.Message {
	mi := &file_manager_perfcase_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PerfCase.ProtoReflect.Descriptor instead.
func (*PerfCase) Descriptor() ([]byte, []int) {
	return file_manager_perfcase_proto_rawDescGZIP(), []int{0}
}

func (x *PerfCase) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *PerfCase) GetCaseId() string {
	if x != nil {
		return x.CaseId
	}
	return ""
}

func (x *PerfCase) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *PerfCase) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *PerfCase) GetExtension() string {
	if x != nil {
		return x.Extension
	}
	return ""
}

func (x *PerfCase) GetHash() string {
	if x != nil {
		return x.Hash
	}
	return ""
}

func (x *PerfCase) GetSize() uint32 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *PerfCase) GetTargetRps() int64 {
	if x != nil {
		return x.TargetRps
	}
	return 0
}

func (x *PerfCase) GetInitialRps() int64 {
	if x != nil {
		return x.InitialRps
	}
	return 0
}

func (x *PerfCase) GetStepHeight() int64 {
	if x != nil {
		return x.StepHeight
	}
	return 0
}

func (x *PerfCase) GetStepDuration() int64 {
	if x != nil {
		return x.StepDuration
	}
	return 0
}

func (x *PerfCase) GetPerfDataId() string {
	if x != nil {
		return x.PerfDataId
	}
	return ""
}

func (x *PerfCase) GetNumberOfVu() uint32 {
	if x != nil {
		return x.NumberOfVu
	}
	return 0
}

func (x *PerfCase) GetNumberOfLg() uint32 {
	if x != nil {
		return x.NumberOfLg
	}
	return 0
}

func (x *PerfCase) GetRequestsOfCpu() string {
	if x != nil {
		return x.RequestsOfCpu
	}
	return ""
}

func (x *PerfCase) GetRequestsOfMemory() string {
	if x != nil {
		return x.RequestsOfMemory
	}
	return ""
}

func (x *PerfCase) GetLimitsOfCpu() string {
	if x != nil {
		return x.LimitsOfCpu
	}
	return ""
}

func (x *PerfCase) GetLimitsOfMemory() string {
	if x != nil {
		return x.LimitsOfMemory
	}
	return ""
}

func (x *PerfCase) GetCreatedBy() string {
	if x != nil {
		return x.CreatedBy
	}
	return ""
}

func (x *PerfCase) GetUpdatedBy() string {
	if x != nil {
		return x.UpdatedBy
	}
	return ""
}

func (x *PerfCase) GetCreatedAt() int64 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

func (x *PerfCase) GetUpdatedAt() int64 {
	if x != nil {
		return x.UpdatedAt
	}
	return 0
}

type PerfCaseV2 struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ProjectId     string                 `protobuf:"bytes,1,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`                 // 项目ID
	CategoryId    string                 `protobuf:"bytes,2,opt,name=category_id,json=categoryId,proto3" json:"category_id,omitempty"`              // 所属分类ID
	CaseId        string                 `protobuf:"bytes,3,opt,name=case_id,json=caseId,proto3" json:"case_id,omitempty"`                          // 用例ID
	Name          string                 `protobuf:"bytes,11,opt,name=name,proto3" json:"name,omitempty"`                                           // 用例名称
	Description   string                 `protobuf:"bytes,12,opt,name=description,proto3" json:"description,omitempty"`                             // 用例描述
	Tags          []string               `protobuf:"bytes,13,rep,name=tags,proto3" json:"tags,omitempty"`                                           // 标签
	Protocol      pb.Protocol            `protobuf:"varint,14,opt,name=protocol,proto3,enum=common.Protocol" json:"protocol,omitempty"`             // 协议
	RateLimits    []*pb.RateLimitV2      `protobuf:"bytes,15,rep,name=rate_limits,json=rateLimits,proto3" json:"rate_limits,omitempty"`             // 限流配置
	SetupSteps    []*pb.PerfCaseStepV2   `protobuf:"bytes,21,rep,name=setup_steps,json=setupSteps,proto3" json:"setup_steps,omitempty"`             // 前置步骤列表
	SerialSteps   []*pb.PerfCaseStepV2   `protobuf:"bytes,22,rep,name=serial_steps,json=serialSteps,proto3" json:"serial_steps,omitempty"`          // 串行步骤列表
	ParallelSteps []*pb.PerfCaseStepV2   `protobuf:"bytes,23,rep,name=parallel_steps,json=parallelSteps,proto3" json:"parallel_steps,omitempty"`    // 并行步骤列表
	TeardownSteps []*pb.PerfCaseStepV2   `protobuf:"bytes,24,rep,name=teardown_steps,json=teardownSteps,proto3" json:"teardown_steps,omitempty"`    // 后置步骤列表
	NumberOfSteps uint32                 `protobuf:"varint,25,opt,name=number_of_steps,json=numberOfSteps,proto3" json:"number_of_steps,omitempty"` // 测试步骤数
	TargetRps     int64                  `protobuf:"varint,26,opt,name=target_rps,json=targetRps,proto3" json:"target_rps,omitempty"`               // 目标的RPS
	State         CommonState            `protobuf:"varint,31,opt,name=state,proto3,enum=manager.CommonState" json:"state,omitempty"`               // 状态
	MaintainedBy  string                 `protobuf:"bytes,32,opt,name=maintained_by,json=maintainedBy,proto3" json:"maintained_by,omitempty"`       // 维护者
	CreatedBy     string                 `protobuf:"bytes,96,opt,name=created_by,json=createdBy,proto3" json:"created_by,omitempty"`                // 创建者
	UpdatedBy     string                 `protobuf:"bytes,97,opt,name=updated_by,json=updatedBy,proto3" json:"updated_by,omitempty"`                // 更新者
	CreatedAt     int64                  `protobuf:"varint,98,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`               // 创建时间
	UpdatedAt     int64                  `protobuf:"varint,99,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`               // 更新时间
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PerfCaseV2) Reset() {
	*x = PerfCaseV2{}
	mi := &file_manager_perfcase_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PerfCaseV2) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PerfCaseV2) ProtoMessage() {}

func (x *PerfCaseV2) ProtoReflect() protoreflect.Message {
	mi := &file_manager_perfcase_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PerfCaseV2.ProtoReflect.Descriptor instead.
func (*PerfCaseV2) Descriptor() ([]byte, []int) {
	return file_manager_perfcase_proto_rawDescGZIP(), []int{1}
}

func (x *PerfCaseV2) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *PerfCaseV2) GetCategoryId() string {
	if x != nil {
		return x.CategoryId
	}
	return ""
}

func (x *PerfCaseV2) GetCaseId() string {
	if x != nil {
		return x.CaseId
	}
	return ""
}

func (x *PerfCaseV2) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *PerfCaseV2) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *PerfCaseV2) GetTags() []string {
	if x != nil {
		return x.Tags
	}
	return nil
}

func (x *PerfCaseV2) GetProtocol() pb.Protocol {
	if x != nil {
		return x.Protocol
	}
	return pb.Protocol(0)
}

func (x *PerfCaseV2) GetRateLimits() []*pb.RateLimitV2 {
	if x != nil {
		return x.RateLimits
	}
	return nil
}

func (x *PerfCaseV2) GetSetupSteps() []*pb.PerfCaseStepV2 {
	if x != nil {
		return x.SetupSteps
	}
	return nil
}

func (x *PerfCaseV2) GetSerialSteps() []*pb.PerfCaseStepV2 {
	if x != nil {
		return x.SerialSteps
	}
	return nil
}

func (x *PerfCaseV2) GetParallelSteps() []*pb.PerfCaseStepV2 {
	if x != nil {
		return x.ParallelSteps
	}
	return nil
}

func (x *PerfCaseV2) GetTeardownSteps() []*pb.PerfCaseStepV2 {
	if x != nil {
		return x.TeardownSteps
	}
	return nil
}

func (x *PerfCaseV2) GetNumberOfSteps() uint32 {
	if x != nil {
		return x.NumberOfSteps
	}
	return 0
}

func (x *PerfCaseV2) GetTargetRps() int64 {
	if x != nil {
		return x.TargetRps
	}
	return 0
}

func (x *PerfCaseV2) GetState() CommonState {
	if x != nil {
		return x.State
	}
	return CommonState_CS_NULL
}

func (x *PerfCaseV2) GetMaintainedBy() string {
	if x != nil {
		return x.MaintainedBy
	}
	return ""
}

func (x *PerfCaseV2) GetCreatedBy() string {
	if x != nil {
		return x.CreatedBy
	}
	return ""
}

func (x *PerfCaseV2) GetUpdatedBy() string {
	if x != nil {
		return x.UpdatedBy
	}
	return ""
}

func (x *PerfCaseV2) GetCreatedAt() int64 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

func (x *PerfCaseV2) GetUpdatedAt() int64 {
	if x != nil {
		return x.UpdatedAt
	}
	return 0
}

type SearchPerfCaseV2Item struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ProjectId     string                 `protobuf:"bytes,1,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`                 // 项目ID
	CategoryId    string                 `protobuf:"bytes,2,opt,name=category_id,json=categoryId,proto3" json:"category_id,omitempty"`              // 所属分类ID
	CaseId        string                 `protobuf:"bytes,3,opt,name=case_id,json=caseId,proto3" json:"case_id,omitempty"`                          // 压测用例ID
	Name          string                 `protobuf:"bytes,11,opt,name=name,proto3" json:"name,omitempty"`                                           // 压测用例名称
	Description   string                 `protobuf:"bytes,12,opt,name=description,proto3" json:"description,omitempty"`                             // 压测用例描述
	Tags          []string               `protobuf:"bytes,13,rep,name=tags,proto3" json:"tags,omitempty"`                                           // 标签
	Protocol      pb.Protocol            `protobuf:"varint,14,opt,name=protocol,proto3,enum=common.Protocol" json:"protocol,omitempty"`             // 协议
	RateLimits    []*pb.RateLimitV2      `protobuf:"bytes,15,rep,name=rate_limits,json=rateLimits,proto3" json:"rate_limits,omitempty"`             // 限流配置
	SerialSteps   []*pb.PerfCaseStepV2   `protobuf:"bytes,16,rep,name=serial_steps,json=serialSteps,proto3" json:"serial_steps,omitempty"`          // 串行步骤列表
	ParallelSteps []*pb.PerfCaseStepV2   `protobuf:"bytes,17,rep,name=parallel_steps,json=parallelSteps,proto3" json:"parallel_steps,omitempty"`    // 并行步骤列表
	NumberOfSteps uint32                 `protobuf:"varint,21,opt,name=number_of_steps,json=numberOfSteps,proto3" json:"number_of_steps,omitempty"` // 测试步骤数
	State         CommonState            `protobuf:"varint,31,opt,name=state,proto3,enum=manager.CommonState" json:"state,omitempty"`               // 状态
	MaintainedBy  string                 `protobuf:"bytes,32,opt,name=maintained_by,json=maintainedBy,proto3" json:"maintained_by,omitempty"`       // 压测用例维护者
	CreatedBy     string                 `protobuf:"bytes,96,opt,name=created_by,json=createdBy,proto3" json:"created_by,omitempty"`                // 创建者
	UpdatedBy     string                 `protobuf:"bytes,97,opt,name=updated_by,json=updatedBy,proto3" json:"updated_by,omitempty"`                // 更新者
	CreatedAt     int64                  `protobuf:"varint,98,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`               // 创建时间
	UpdatedAt     int64                  `protobuf:"varint,99,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`               // 更新时间
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SearchPerfCaseV2Item) Reset() {
	*x = SearchPerfCaseV2Item{}
	mi := &file_manager_perfcase_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SearchPerfCaseV2Item) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchPerfCaseV2Item) ProtoMessage() {}

func (x *SearchPerfCaseV2Item) ProtoReflect() protoreflect.Message {
	mi := &file_manager_perfcase_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchPerfCaseV2Item.ProtoReflect.Descriptor instead.
func (*SearchPerfCaseV2Item) Descriptor() ([]byte, []int) {
	return file_manager_perfcase_proto_rawDescGZIP(), []int{2}
}

func (x *SearchPerfCaseV2Item) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *SearchPerfCaseV2Item) GetCategoryId() string {
	if x != nil {
		return x.CategoryId
	}
	return ""
}

func (x *SearchPerfCaseV2Item) GetCaseId() string {
	if x != nil {
		return x.CaseId
	}
	return ""
}

func (x *SearchPerfCaseV2Item) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *SearchPerfCaseV2Item) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *SearchPerfCaseV2Item) GetTags() []string {
	if x != nil {
		return x.Tags
	}
	return nil
}

func (x *SearchPerfCaseV2Item) GetProtocol() pb.Protocol {
	if x != nil {
		return x.Protocol
	}
	return pb.Protocol(0)
}

func (x *SearchPerfCaseV2Item) GetRateLimits() []*pb.RateLimitV2 {
	if x != nil {
		return x.RateLimits
	}
	return nil
}

func (x *SearchPerfCaseV2Item) GetSerialSteps() []*pb.PerfCaseStepV2 {
	if x != nil {
		return x.SerialSteps
	}
	return nil
}

func (x *SearchPerfCaseV2Item) GetParallelSteps() []*pb.PerfCaseStepV2 {
	if x != nil {
		return x.ParallelSteps
	}
	return nil
}

func (x *SearchPerfCaseV2Item) GetNumberOfSteps() uint32 {
	if x != nil {
		return x.NumberOfSteps
	}
	return 0
}

func (x *SearchPerfCaseV2Item) GetState() CommonState {
	if x != nil {
		return x.State
	}
	return CommonState_CS_NULL
}

func (x *SearchPerfCaseV2Item) GetMaintainedBy() string {
	if x != nil {
		return x.MaintainedBy
	}
	return ""
}

func (x *SearchPerfCaseV2Item) GetCreatedBy() string {
	if x != nil {
		return x.CreatedBy
	}
	return ""
}

func (x *SearchPerfCaseV2Item) GetUpdatedBy() string {
	if x != nil {
		return x.UpdatedBy
	}
	return ""
}

func (x *SearchPerfCaseV2Item) GetCreatedAt() int64 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

func (x *SearchPerfCaseV2Item) GetUpdatedAt() int64 {
	if x != nil {
		return x.UpdatedAt
	}
	return 0
}

type UpdatePerfPlanByCaseTaskInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ProjectId     string                 `protobuf:"bytes,1,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"` // 项目ID
	PlanId        string                 `protobuf:"bytes,2,opt,name=plan_id,json=planId,proto3" json:"plan_id,omitempty"`          // 计划ID
	CaseId        string                 `protobuf:"bytes,3,opt,name=case_id,json=caseId,proto3" json:"case_id,omitempty"`          // 用例ID
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdatePerfPlanByCaseTaskInfo) Reset() {
	*x = UpdatePerfPlanByCaseTaskInfo{}
	mi := &file_manager_perfcase_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdatePerfPlanByCaseTaskInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdatePerfPlanByCaseTaskInfo) ProtoMessage() {}

func (x *UpdatePerfPlanByCaseTaskInfo) ProtoReflect() protoreflect.Message {
	mi := &file_manager_perfcase_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdatePerfPlanByCaseTaskInfo.ProtoReflect.Descriptor instead.
func (*UpdatePerfPlanByCaseTaskInfo) Descriptor() ([]byte, []int) {
	return file_manager_perfcase_proto_rawDescGZIP(), []int{3}
}

func (x *UpdatePerfPlanByCaseTaskInfo) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *UpdatePerfPlanByCaseTaskInfo) GetPlanId() string {
	if x != nil {
		return x.PlanId
	}
	return ""
}

func (x *UpdatePerfPlanByCaseTaskInfo) GetCaseId() string {
	if x != nil {
		return x.CaseId
	}
	return ""
}

var File_manager_perfcase_proto protoreflect.FileDescriptor

var file_manager_perfcase_proto_rawDesc = []byte{
	0x0a, 0x16, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2f, 0x70, 0x65, 0x72, 0x66, 0x63, 0x61,
	0x73, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x07, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65,
	0x72, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69,
	0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x11, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x12, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x11, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x70, 0x65, 0x72, 0x66, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x12, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2f, 0x62, 0x61,
	0x73, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xca, 0x05, 0x0a, 0x08, 0x50, 0x65, 0x72,
	0x66, 0x43, 0x61, 0x73, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65,
	0x63, 0x74, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x69, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x63, 0x61, 0x73, 0x65, 0x49, 0x64, 0x12, 0x12, 0x0a,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x12, 0x1c, 0x0a, 0x09, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f,
	0x6e, 0x12, 0x12, 0x0a, 0x04, 0x68, 0x61, 0x73, 0x68, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x68, 0x61, 0x73, 0x68, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x74, 0x61, 0x72,
	0x67, 0x65, 0x74, 0x5f, 0x72, 0x70, 0x73, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x74,
	0x61, 0x72, 0x67, 0x65, 0x74, 0x52, 0x70, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x69, 0x6e, 0x69, 0x74,
	0x69, 0x61, 0x6c, 0x5f, 0x72, 0x70, 0x73, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x69,
	0x6e, 0x69, 0x74, 0x69, 0x61, 0x6c, 0x52, 0x70, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x73, 0x74, 0x65,
	0x70, 0x5f, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a,
	0x73, 0x74, 0x65, 0x70, 0x48, 0x65, 0x69, 0x67, 0x68, 0x74, 0x12, 0x23, 0x0a, 0x0d, 0x73, 0x74,
	0x65, 0x70, 0x5f, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0e, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x0c, 0x73, 0x74, 0x65, 0x70, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12,
	0x20, 0x0a, 0x0c, 0x70, 0x65, 0x72, 0x66, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x69, 0x64, 0x18,
	0x15, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x70, 0x65, 0x72, 0x66, 0x44, 0x61, 0x74, 0x61, 0x49,
	0x64, 0x12, 0x20, 0x0a, 0x0c, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x5f, 0x6f, 0x66, 0x5f, 0x76,
	0x75, 0x18, 0x16, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x4f,
	0x66, 0x56, 0x75, 0x12, 0x20, 0x0a, 0x0c, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x5f, 0x6f, 0x66,
	0x5f, 0x6c, 0x67, 0x18, 0x1f, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x6e, 0x75, 0x6d, 0x62, 0x65,
	0x72, 0x4f, 0x66, 0x4c, 0x67, 0x12, 0x26, 0x0a, 0x0f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x73, 0x5f, 0x6f, 0x66, 0x5f, 0x63, 0x70, 0x75, 0x18, 0x20, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d,
	0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x73, 0x4f, 0x66, 0x43, 0x70, 0x75, 0x12, 0x2c, 0x0a,
	0x12, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x73, 0x5f, 0x6f, 0x66, 0x5f, 0x6d, 0x65, 0x6d,
	0x6f, 0x72, 0x79, 0x18, 0x21, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x72, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x73, 0x4f, 0x66, 0x4d, 0x65, 0x6d, 0x6f, 0x72, 0x79, 0x12, 0x22, 0x0a, 0x0d, 0x6c,
	0x69, 0x6d, 0x69, 0x74, 0x73, 0x5f, 0x6f, 0x66, 0x5f, 0x63, 0x70, 0x75, 0x18, 0x22, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0b, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x73, 0x4f, 0x66, 0x43, 0x70, 0x75, 0x12,
	0x28, 0x0a, 0x10, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x73, 0x5f, 0x6f, 0x66, 0x5f, 0x6d, 0x65, 0x6d,
	0x6f, 0x72, 0x79, 0x18, 0x23, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x6c, 0x69, 0x6d, 0x69, 0x74,
	0x73, 0x4f, 0x66, 0x4d, 0x65, 0x6d, 0x6f, 0x72, 0x79, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x64, 0x5f, 0x62, 0x79, 0x18, 0x60, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x42, 0x79, 0x12, 0x1d, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x64, 0x5f, 0x62, 0x79, 0x18, 0x61, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x75, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x64, 0x42, 0x79, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x62, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x63, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x64, 0x5f, 0x61, 0x74, 0x18, 0x63, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x64, 0x41, 0x74, 0x22, 0x99, 0x06, 0x0a, 0x0a, 0x50, 0x65, 0x72, 0x66, 0x43, 0x61,
	0x73, 0x65, 0x56, 0x32, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63,
	0x74, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x5f,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f,
	0x72, 0x79, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x69, 0x64, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x63, 0x61, 0x73, 0x65, 0x49, 0x64, 0x12, 0x12, 0x0a,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x61, 0x67, 0x73, 0x18, 0x0d, 0x20, 0x03, 0x28,
	0x09, 0x52, 0x04, 0x74, 0x61, 0x67, 0x73, 0x12, 0x2c, 0x0a, 0x08, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x63, 0x6f, 0x6c, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x10, 0x2e, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2e, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x52, 0x08, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x12, 0x34, 0x0a, 0x0b, 0x72, 0x61, 0x74, 0x65, 0x5f, 0x6c, 0x69,
	0x6d, 0x69, 0x74, 0x73, 0x18, 0x0f, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x52, 0x61, 0x74, 0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x56, 0x32, 0x52,
	0x0a, 0x72, 0x61, 0x74, 0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x73, 0x12, 0x37, 0x0a, 0x0b, 0x73,
	0x65, 0x74, 0x75, 0x70, 0x5f, 0x73, 0x74, 0x65, 0x70, 0x73, 0x18, 0x15, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x16, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x65, 0x72, 0x66, 0x43, 0x61,
	0x73, 0x65, 0x53, 0x74, 0x65, 0x70, 0x56, 0x32, 0x52, 0x0a, 0x73, 0x65, 0x74, 0x75, 0x70, 0x53,
	0x74, 0x65, 0x70, 0x73, 0x12, 0x39, 0x0a, 0x0c, 0x73, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x5f, 0x73,
	0x74, 0x65, 0x70, 0x73, 0x18, 0x16, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x65, 0x72, 0x66, 0x43, 0x61, 0x73, 0x65, 0x53, 0x74, 0x65, 0x70,
	0x56, 0x32, 0x52, 0x0b, 0x73, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x53, 0x74, 0x65, 0x70, 0x73, 0x12,
	0x3d, 0x0a, 0x0e, 0x70, 0x61, 0x72, 0x61, 0x6c, 0x6c, 0x65, 0x6c, 0x5f, 0x73, 0x74, 0x65, 0x70,
	0x73, 0x18, 0x17, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x50, 0x65, 0x72, 0x66, 0x43, 0x61, 0x73, 0x65, 0x53, 0x74, 0x65, 0x70, 0x56, 0x32, 0x52,
	0x0d, 0x70, 0x61, 0x72, 0x61, 0x6c, 0x6c, 0x65, 0x6c, 0x53, 0x74, 0x65, 0x70, 0x73, 0x12, 0x3d,
	0x0a, 0x0e, 0x74, 0x65, 0x61, 0x72, 0x64, 0x6f, 0x77, 0x6e, 0x5f, 0x73, 0x74, 0x65, 0x70, 0x73,
	0x18, 0x18, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e,
	0x50, 0x65, 0x72, 0x66, 0x43, 0x61, 0x73, 0x65, 0x53, 0x74, 0x65, 0x70, 0x56, 0x32, 0x52, 0x0d,
	0x74, 0x65, 0x61, 0x72, 0x64, 0x6f, 0x77, 0x6e, 0x53, 0x74, 0x65, 0x70, 0x73, 0x12, 0x26, 0x0a,
	0x0f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x5f, 0x6f, 0x66, 0x5f, 0x73, 0x74, 0x65, 0x70, 0x73,
	0x18, 0x19, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0d, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x4f, 0x66,
	0x53, 0x74, 0x65, 0x70, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f,
	0x72, 0x70, 0x73, 0x18, 0x1a, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x74, 0x61, 0x72, 0x67, 0x65,
	0x74, 0x52, 0x70, 0x73, 0x12, 0x2a, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x1f, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x43, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65,
	0x12, 0x23, 0x0a, 0x0d, 0x6d, 0x61, 0x69, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x64, 0x5f, 0x62,
	0x79, 0x18, 0x20, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x6d, 0x61, 0x69, 0x6e, 0x74, 0x61, 0x69,
	0x6e, 0x65, 0x64, 0x42, 0x79, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64,
	0x5f, 0x62, 0x79, 0x18, 0x60, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x64, 0x42, 0x79, 0x12, 0x1d, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f,
	0x62, 0x79, 0x18, 0x61, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x64, 0x42, 0x79, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61,
	0x74, 0x18, 0x62, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64,
	0x41, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74,
	0x18, 0x63, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41,
	0x74, 0x22, 0x8c, 0x05, 0x0a, 0x14, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x50, 0x65, 0x72, 0x66,
	0x43, 0x61, 0x73, 0x65, 0x56, 0x32, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72,
	0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x61, 0x74,
	0x65, 0x67, 0x6f, 0x72, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a,
	0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x63, 0x61,
	0x73, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x63, 0x61, 0x73,
	0x65, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x61, 0x67,
	0x73, 0x18, 0x0d, 0x20, 0x03, 0x28, 0x09, 0x52, 0x04, 0x74, 0x61, 0x67, 0x73, 0x12, 0x2c, 0x0a,
	0x08, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x10, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f,
	0x6c, 0x52, 0x08, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x12, 0x34, 0x0a, 0x0b, 0x72,
	0x61, 0x74, 0x65, 0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x73, 0x18, 0x0f, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x13, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x52, 0x61, 0x74, 0x65, 0x4c, 0x69,
	0x6d, 0x69, 0x74, 0x56, 0x32, 0x52, 0x0a, 0x72, 0x61, 0x74, 0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74,
	0x73, 0x12, 0x39, 0x0a, 0x0c, 0x73, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x5f, 0x73, 0x74, 0x65, 0x70,
	0x73, 0x18, 0x10, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x50, 0x65, 0x72, 0x66, 0x43, 0x61, 0x73, 0x65, 0x53, 0x74, 0x65, 0x70, 0x56, 0x32, 0x52,
	0x0b, 0x73, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x53, 0x74, 0x65, 0x70, 0x73, 0x12, 0x3d, 0x0a, 0x0e,
	0x70, 0x61, 0x72, 0x61, 0x6c, 0x6c, 0x65, 0x6c, 0x5f, 0x73, 0x74, 0x65, 0x70, 0x73, 0x18, 0x11,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x65,
	0x72, 0x66, 0x43, 0x61, 0x73, 0x65, 0x53, 0x74, 0x65, 0x70, 0x56, 0x32, 0x52, 0x0d, 0x70, 0x61,
	0x72, 0x61, 0x6c, 0x6c, 0x65, 0x6c, 0x53, 0x74, 0x65, 0x70, 0x73, 0x12, 0x26, 0x0a, 0x0f, 0x6e,
	0x75, 0x6d, 0x62, 0x65, 0x72, 0x5f, 0x6f, 0x66, 0x5f, 0x73, 0x74, 0x65, 0x70, 0x73, 0x18, 0x15,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x0d, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x4f, 0x66, 0x53, 0x74,
	0x65, 0x70, 0x73, 0x12, 0x2a, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x1f, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x14, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x43, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x12,
	0x23, 0x0a, 0x0d, 0x6d, 0x61, 0x69, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x64, 0x5f, 0x62, 0x79,
	0x18, 0x20, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x6d, 0x61, 0x69, 0x6e, 0x74, 0x61, 0x69, 0x6e,
	0x65, 0x64, 0x42, 0x79, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f,
	0x62, 0x79, 0x18, 0x60, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x64, 0x42, 0x79, 0x12, 0x1d, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x62,
	0x79, 0x18, 0x61, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64,
	0x42, 0x79, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74,
	0x18, 0x62, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41,
	0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18,
	0x63, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74,
	0x22, 0xcb, 0x01, 0x0a, 0x1c, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x65, 0x72, 0x66, 0x50,
	0x6c, 0x61, 0x6e, 0x42, 0x79, 0x43, 0x61, 0x73, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x49, 0x6e, 0x66,
	0x6f, 0x12, 0x3d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x1e, 0xfa, 0x42, 0x1b, 0x72, 0x19, 0x32, 0x17, 0x28, 0x3f,
	0x3a, 0x5e, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x3a, 0x2e, 0x2b, 0x3f,
	0x7c, 0x5e, 0x31, 0x24, 0x29, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64,
	0x12, 0x35, 0x0a, 0x07, 0x70, 0x6c, 0x61, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x1c, 0xfa, 0x42, 0x19, 0x72, 0x17, 0x32, 0x15, 0x28, 0x3f, 0x3a, 0x5e, 0x70, 0x65,
	0x72, 0x66, 0x5f, 0x70, 0x6c, 0x61, 0x6e, 0x5f, 0x69, 0x64, 0x3a, 0x2e, 0x2b, 0x3f, 0x29, 0x52,
	0x06, 0x70, 0x6c, 0x61, 0x6e, 0x49, 0x64, 0x12, 0x35, 0x0a, 0x07, 0x63, 0x61, 0x73, 0x65, 0x5f,
	0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x1c, 0xfa, 0x42, 0x19, 0x72, 0x17, 0x32,
	0x15, 0x28, 0x3f, 0x3a, 0x5e, 0x70, 0x65, 0x72, 0x66, 0x5f, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x69,
	0x64, 0x3a, 0x2e, 0x2b, 0x3f, 0x29, 0x52, 0x06, 0x63, 0x61, 0x73, 0x65, 0x49, 0x64, 0x42, 0x41,
	0x5a, 0x3f, 0x67, 0x69, 0x74, 0x6c, 0x61, 0x62, 0x2e, 0x74, 0x74, 0x79, 0x75, 0x79, 0x69, 0x6e,
	0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x54, 0x65, 0x73, 0x74, 0x44, 0x65, 0x76, 0x65, 0x6c, 0x6f, 0x70,
	0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x70, 0x72, 0x6f, 0x62, 0x65, 0x2d, 0x62, 0x61, 0x63, 0x6b, 0x65,
	0x6e, 0x64, 0x2f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2f, 0x72, 0x70, 0x63, 0x2f, 0x70,
	0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_manager_perfcase_proto_rawDescOnce sync.Once
	file_manager_perfcase_proto_rawDescData = file_manager_perfcase_proto_rawDesc
)

func file_manager_perfcase_proto_rawDescGZIP() []byte {
	file_manager_perfcase_proto_rawDescOnce.Do(func() {
		file_manager_perfcase_proto_rawDescData = protoimpl.X.CompressGZIP(file_manager_perfcase_proto_rawDescData)
	})
	return file_manager_perfcase_proto_rawDescData
}

var file_manager_perfcase_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_manager_perfcase_proto_goTypes = []any{
	(*PerfCase)(nil),                     // 0: manager.PerfCase
	(*PerfCaseV2)(nil),                   // 1: manager.PerfCaseV2
	(*SearchPerfCaseV2Item)(nil),         // 2: manager.SearchPerfCaseV2Item
	(*UpdatePerfPlanByCaseTaskInfo)(nil), // 3: manager.UpdatePerfPlanByCaseTaskInfo
	(pb.Protocol)(0),                     // 4: common.Protocol
	(*pb.RateLimitV2)(nil),               // 5: common.RateLimitV2
	(*pb.PerfCaseStepV2)(nil),            // 6: common.PerfCaseStepV2
	(CommonState)(0),                     // 7: manager.CommonState
}
var file_manager_perfcase_proto_depIdxs = []int32{
	4,  // 0: manager.PerfCaseV2.protocol:type_name -> common.Protocol
	5,  // 1: manager.PerfCaseV2.rate_limits:type_name -> common.RateLimitV2
	6,  // 2: manager.PerfCaseV2.setup_steps:type_name -> common.PerfCaseStepV2
	6,  // 3: manager.PerfCaseV2.serial_steps:type_name -> common.PerfCaseStepV2
	6,  // 4: manager.PerfCaseV2.parallel_steps:type_name -> common.PerfCaseStepV2
	6,  // 5: manager.PerfCaseV2.teardown_steps:type_name -> common.PerfCaseStepV2
	7,  // 6: manager.PerfCaseV2.state:type_name -> manager.CommonState
	4,  // 7: manager.SearchPerfCaseV2Item.protocol:type_name -> common.Protocol
	5,  // 8: manager.SearchPerfCaseV2Item.rate_limits:type_name -> common.RateLimitV2
	6,  // 9: manager.SearchPerfCaseV2Item.serial_steps:type_name -> common.PerfCaseStepV2
	6,  // 10: manager.SearchPerfCaseV2Item.parallel_steps:type_name -> common.PerfCaseStepV2
	7,  // 11: manager.SearchPerfCaseV2Item.state:type_name -> manager.CommonState
	12, // [12:12] is the sub-list for method output_type
	12, // [12:12] is the sub-list for method input_type
	12, // [12:12] is the sub-list for extension type_name
	12, // [12:12] is the sub-list for extension extendee
	0,  // [0:12] is the sub-list for field type_name
}

func init() { file_manager_perfcase_proto_init() }
func file_manager_perfcase_proto_init() {
	if File_manager_perfcase_proto != nil {
		return
	}
	file_manager_base_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_manager_perfcase_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_manager_perfcase_proto_goTypes,
		DependencyIndexes: file_manager_perfcase_proto_depIdxs,
		MessageInfos:      file_manager_perfcase_proto_msgTypes,
	}.Build()
	File_manager_perfcase_proto = out.File
	file_manager_perfcase_proto_rawDesc = nil
	file_manager_perfcase_proto_goTypes = nil
	file_manager_perfcase_proto_depIdxs = nil
}
