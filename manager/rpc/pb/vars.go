package pb

import (
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
)

var (
	ComponentExecutionDataFuncMap = map[constants.ComponentType]componentExecutionDataFunc{
		constants.START: func() (isApiExecutionData_Data, protobuf.ValidateMessage) {
			v := &ApiExecutionData_Start{Start: &StartComponent{}}
			return v, v.Start
		},
		constants.END: func() (isApiExecutionData_Data, protobuf.ValidateMessage) {
			v := &ApiExecutionData_End{End: &EndComponent{}}
			return v, v.End
		},
		constants.SETUP: func() (isApiExecutionData_Data, protobuf.ValidateMessage) {
			v := &ApiExecutionData_Setup{Setup: &SetupComponent{}}
			return v, v.Setup
		},
		constants.TEARDOWN: func() (isApiExecutionData_Data, protobuf.ValidateMessage) {
			v := &ApiExecutionData_Teardown{Teardown: &TeardownComponent{}}
			return v, v.Teardown
		},
		constants.SINGLE: func() (isApiExecutionData_Data, protobuf.ValidateMessage) {
			v := &ApiExecutionData_BusinessSingle{BusinessSingle: &BusinessSingleComponent{}}
			return v, v.BusinessSingle
		},
		constants.GROUP: func() (isApiExecutionData_Data, protobuf.ValidateMessage) {
			v := &ApiExecutionData_BusinessGroup{BusinessGroup: &BusinessGroupComponent{}}
			return v, v.BusinessGroup
		},
		constants.LOOP: func() (isApiExecutionData_Data, protobuf.ValidateMessage) {
			v := &ApiExecutionData_Loop{Loop: &LoopComponent{}}
			return v, v.Loop
		},
		constants.HTTP: func() (isApiExecutionData_Data, protobuf.ValidateMessage) {
			v := &ApiExecutionData_HttpRequest{HttpRequest: &HttpRequestComponent{}}
			return v, v.HttpRequest
		},
		constants.PRECISIONTESTINGHTTP: func() (isApiExecutionData_Data, protobuf.ValidateMessage) {
			v := &ApiExecutionData_PrecisionTestingHttpRequest{PrecisionTestingHttpRequest: &HttpRequestComponent{}}
			return v, v.PrecisionTestingHttpRequest
		},
		constants.REFERENCE: func() (isApiExecutionData_Data, protobuf.ValidateMessage) {
			v := &ApiExecutionData_Reference{Reference: &ReferenceComponent{}}
			return v, v.Reference
		},
		constants.CONDITION: func() (isApiExecutionData_Data, protobuf.ValidateMessage) {
			v := &ApiExecutionData_Condition{Condition: &ConditionComponent{}}
			return v, v.Condition
		},
		constants.WAIT: func() (isApiExecutionData_Data, protobuf.ValidateMessage) {
			v := &ApiExecutionData_Wait{Wait: &WaitComponent{}}
			return v, v.Wait
		},
		constants.ASSERT: func() (isApiExecutionData_Data, protobuf.ValidateMessage) {
			v := &ApiExecutionData_Assert{Assert: &AssertComponent{}}
			return v, v.Assert
		},
		constants.ACCOUNT: func() (isApiExecutionData_Data, protobuf.ValidateMessage) {
			v := &ApiExecutionData_Account{Account: &PoolAccountComponent{}}
			return v, v.Account
		},
		//constants.PARALLEL: func() (isApiExecutionData_Data, ValidateMessage) {
		//
		//},
		constants.PROCESSING: func() (isApiExecutionData_Data, protobuf.ValidateMessage) {
			v := &ApiExecutionData_Processing{Processing: &DataProcessingComponent{}}
			return v, v.Processing
		},
		//constants.DRIVEN: func() (isApiExecutionData_Data, ValidateMessage) {
		//
		//},
		constants.SQL: func() (isApiExecutionData_Data, protobuf.ValidateMessage) {
			v := &ApiExecutionData_Sql{Sql: &SqlExecutionComponent{}}
			return v, v.Sql
		},
	}

	DefaultMarshalOptions    = protobuf.DefaultProtoJSONMarshalOptions
	DefaultUnmarshalOptions  = protobuf.DefaultProtoJSONUnmarshalOptions
	ValidateUnmarshalOptions = protobuf.ValidateProtoJSONUnmarshalOptions
)
