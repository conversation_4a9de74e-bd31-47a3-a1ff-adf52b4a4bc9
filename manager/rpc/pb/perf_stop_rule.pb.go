// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.1
// 	protoc        v5.29.2
// source: manager/perf_stop_rule.proto

package pb

import (
	reflect "reflect"
	sync "sync"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"

	pb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type PerfStopRule struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ProjectId     string                 `protobuf:"bytes,1,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`                             // 项目ID
	RuleId        string                 `protobuf:"bytes,2,opt,name=rule_id,json=ruleId,proto3" json:"rule_id,omitempty"`                                      // 规则ID
	Name          string                 `protobuf:"bytes,11,opt,name=name,proto3" json:"name,omitempty"`                                                       // 规则名称
	Description   string                 `protobuf:"bytes,12,opt,name=description,proto3" json:"description,omitempty"`                                         // 规则描述
	MetricType    pb.MetricType          `protobuf:"varint,13,opt,name=metric_type,json=metricType,proto3,enum=common.MetricType" json:"metric_type,omitempty"` // 指标类型
	Threshold     float64                `protobuf:"fixed64,14,opt,name=threshold,proto3" json:"threshold,omitempty"`                                           // 阀值
	Duration      uint32                 `protobuf:"varint,15,opt,name=duration,proto3" json:"duration,omitempty"`                                              // 持续时间
	State         CommonState            `protobuf:"varint,21,opt,name=state,proto3,enum=manager.CommonState" json:"state,omitempty"`                           // 状态
	CreatedBy     string                 `protobuf:"bytes,96,opt,name=created_by,json=createdBy,proto3" json:"created_by,omitempty"`                            // 创建者
	UpdatedBy     string                 `protobuf:"bytes,97,opt,name=updated_by,json=updatedBy,proto3" json:"updated_by,omitempty"`                            // 更新者
	CreatedAt     int64                  `protobuf:"varint,98,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`                           // 创建时间
	UpdatedAt     int64                  `protobuf:"varint,99,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`                           // 更新时间
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PerfStopRule) Reset() {
	*x = PerfStopRule{}
	mi := &file_manager_perf_stop_rule_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PerfStopRule) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PerfStopRule) ProtoMessage() {}

func (x *PerfStopRule) ProtoReflect() protoreflect.Message {
	mi := &file_manager_perf_stop_rule_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PerfStopRule.ProtoReflect.Descriptor instead.
func (*PerfStopRule) Descriptor() ([]byte, []int) {
	return file_manager_perf_stop_rule_proto_rawDescGZIP(), []int{0}
}

func (x *PerfStopRule) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *PerfStopRule) GetRuleId() string {
	if x != nil {
		return x.RuleId
	}
	return ""
}

func (x *PerfStopRule) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *PerfStopRule) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *PerfStopRule) GetMetricType() pb.MetricType {
	if x != nil {
		return x.MetricType
	}
	return pb.MetricType(0)
}

func (x *PerfStopRule) GetThreshold() float64 {
	if x != nil {
		return x.Threshold
	}
	return 0
}

func (x *PerfStopRule) GetDuration() uint32 {
	if x != nil {
		return x.Duration
	}
	return 0
}

func (x *PerfStopRule) GetState() CommonState {
	if x != nil {
		return x.State
	}
	return CommonState_CS_NULL
}

func (x *PerfStopRule) GetCreatedBy() string {
	if x != nil {
		return x.CreatedBy
	}
	return ""
}

func (x *PerfStopRule) GetUpdatedBy() string {
	if x != nil {
		return x.UpdatedBy
	}
	return ""
}

func (x *PerfStopRule) GetCreatedAt() int64 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

func (x *PerfStopRule) GetUpdatedAt() int64 {
	if x != nil {
		return x.UpdatedAt
	}
	return 0
}

var File_manager_perf_stop_rule_proto protoreflect.FileDescriptor

var file_manager_perf_stop_rule_proto_rawDesc = []byte{
	0x0a, 0x1c, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2f, 0x70, 0x65, 0x72, 0x66, 0x5f, 0x73,
	0x74, 0x6f, 0x70, 0x5f, 0x72, 0x75, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x07,
	0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x1a, 0x11, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f,
	0x65, 0x6e, 0x75, 0x6d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x12, 0x6d, 0x61, 0x6e, 0x61,
	0x67, 0x65, 0x72, 0x2f, 0x62, 0x61, 0x73, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x93,
	0x03, 0x0a, 0x0c, 0x50, 0x65, 0x72, 0x66, 0x53, 0x74, 0x6f, 0x70, 0x52, 0x75, 0x6c, 0x65, 0x12,
	0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x12, 0x17,
	0x0a, 0x07, 0x72, 0x75, 0x6c, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x72, 0x75, 0x6c, 0x65, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64,
	0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x33, 0x0a,
	0x0b, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0d, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x12, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x4d, 0x65, 0x74, 0x72,
	0x69, 0x63, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0a, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x74, 0x68, 0x72, 0x65, 0x73, 0x68, 0x6f, 0x6c, 0x64, 0x18,
	0x0e, 0x20, 0x01, 0x28, 0x01, 0x52, 0x09, 0x74, 0x68, 0x72, 0x65, 0x73, 0x68, 0x6f, 0x6c, 0x64,
	0x12, 0x1a, 0x0a, 0x08, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0f, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x08, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x2a, 0x0a, 0x05,
	0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x15, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x6d, 0x61,
	0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74,
	0x65, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x64, 0x5f, 0x62, 0x79, 0x18, 0x60, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x64, 0x42, 0x79, 0x12, 0x1d, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x64, 0x5f, 0x62, 0x79, 0x18, 0x61, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x75, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x64, 0x42, 0x79, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x64, 0x5f, 0x61, 0x74, 0x18, 0x62, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64,
	0x5f, 0x61, 0x74, 0x18, 0x63, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x64, 0x41, 0x74, 0x42, 0x41, 0x5a, 0x3f, 0x67, 0x69, 0x74, 0x6c, 0x61, 0x62, 0x2e, 0x74,
	0x74, 0x79, 0x75, 0x79, 0x69, 0x6e, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x54, 0x65, 0x73, 0x74, 0x44,
	0x65, 0x76, 0x65, 0x6c, 0x6f, 0x70, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x70, 0x72, 0x6f, 0x62, 0x65,
	0x2d, 0x62, 0x61, 0x63, 0x6b, 0x65, 0x6e, 0x64, 0x2f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72,
	0x2f, 0x72, 0x70, 0x63, 0x2f, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_manager_perf_stop_rule_proto_rawDescOnce sync.Once
	file_manager_perf_stop_rule_proto_rawDescData = file_manager_perf_stop_rule_proto_rawDesc
)

func file_manager_perf_stop_rule_proto_rawDescGZIP() []byte {
	file_manager_perf_stop_rule_proto_rawDescOnce.Do(func() {
		file_manager_perf_stop_rule_proto_rawDescData = protoimpl.X.CompressGZIP(file_manager_perf_stop_rule_proto_rawDescData)
	})
	return file_manager_perf_stop_rule_proto_rawDescData
}

var file_manager_perf_stop_rule_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_manager_perf_stop_rule_proto_goTypes = []any{
	(*PerfStopRule)(nil), // 0: manager.PerfStopRule
	(pb.MetricType)(0),   // 1: common.MetricType
	(CommonState)(0),     // 2: manager.CommonState
}
var file_manager_perf_stop_rule_proto_depIdxs = []int32{
	1, // 0: manager.PerfStopRule.metric_type:type_name -> common.MetricType
	2, // 1: manager.PerfStopRule.state:type_name -> manager.CommonState
	2, // [2:2] is the sub-list for method output_type
	2, // [2:2] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_manager_perf_stop_rule_proto_init() }
func file_manager_perf_stop_rule_proto_init() {
	if File_manager_perf_stop_rule_proto != nil {
		return
	}
	file_manager_base_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_manager_perf_stop_rule_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_manager_perf_stop_rule_proto_goTypes,
		DependencyIndexes: file_manager_perf_stop_rule_proto_depIdxs,
		MessageInfos:      file_manager_perf_stop_rule_proto_msgTypes,
	}.Build()
	File_manager_perf_stop_rule_proto = out.File
	file_manager_perf_stop_rule_proto_rawDesc = nil
	file_manager_perf_stop_rule_proto_goTypes = nil
	file_manager_perf_stop_rule_proto_depIdxs = nil
}
