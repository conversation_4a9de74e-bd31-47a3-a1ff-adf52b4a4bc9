// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.1
// 	protoc        v5.29.2
// source: manager/sla_threshold.proto

package pb

import (
	reflect "reflect"
	sync "sync"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"

	pb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type SlaThreshold struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ProjectId     string                 `protobuf:"bytes,1,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`
	PlatformType  pb.PlatformType        `protobuf:"varint,2,opt,name=platform_type,json=platformType,proto3,enum=common.PlatformType" json:"platform_type,omitempty"`
	BranchType    pb.BranchType          `protobuf:"varint,3,opt,name=branch_type,json=branchType,proto3,enum=common.BranchType" json:"branch_type,omitempty"`
	Name          string                 `protobuf:"bytes,4,opt,name=Name,proto3" json:"Name,omitempty"`
	Unit          string                 `protobuf:"bytes,5,opt,name=Unit,proto3" json:"Unit,omitempty"`
	Value         int64                  `protobuf:"varint,6,opt,name=Value,proto3" json:"Value,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SlaThreshold) Reset() {
	*x = SlaThreshold{}
	mi := &file_manager_sla_threshold_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SlaThreshold) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SlaThreshold) ProtoMessage() {}

func (x *SlaThreshold) ProtoReflect() protoreflect.Message {
	mi := &file_manager_sla_threshold_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SlaThreshold.ProtoReflect.Descriptor instead.
func (*SlaThreshold) Descriptor() ([]byte, []int) {
	return file_manager_sla_threshold_proto_rawDescGZIP(), []int{0}
}

func (x *SlaThreshold) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *SlaThreshold) GetPlatformType() pb.PlatformType {
	if x != nil {
		return x.PlatformType
	}
	return pb.PlatformType(0)
}

func (x *SlaThreshold) GetBranchType() pb.BranchType {
	if x != nil {
		return x.BranchType
	}
	return pb.BranchType(0)
}

func (x *SlaThreshold) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *SlaThreshold) GetUnit() string {
	if x != nil {
		return x.Unit
	}
	return ""
}

func (x *SlaThreshold) GetValue() int64 {
	if x != nil {
		return x.Value
	}
	return 0
}

var File_manager_sla_threshold_proto protoreflect.FileDescriptor

var file_manager_sla_threshold_proto_rawDesc = []byte{
	0x0a, 0x1b, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2f, 0x73, 0x6c, 0x61, 0x5f, 0x74, 0x68,
	0x72, 0x65, 0x73, 0x68, 0x6f, 0x6c, 0x64, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x07, 0x6d,
	0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x1a, 0x11, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x65,
	0x6e, 0x75, 0x6d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xdb, 0x01, 0x0a, 0x0c, 0x53, 0x6c,
	0x61, 0x54, 0x68, 0x72, 0x65, 0x73, 0x68, 0x6f, 0x6c, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72,
	0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x12, 0x39, 0x0a, 0x0d, 0x70, 0x6c, 0x61,
	0x74, 0x66, 0x6f, 0x72, 0x6d, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x14, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x6c, 0x61, 0x74, 0x66, 0x6f,
	0x72, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0c, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x33, 0x0a, 0x0b, 0x62, 0x72, 0x61, 0x6e, 0x63, 0x68, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x12, 0x2e, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2e, 0x42, 0x72, 0x61, 0x6e, 0x63, 0x68, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0a, 0x62,
	0x72, 0x61, 0x6e, 0x63, 0x68, 0x54, 0x79, 0x70, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x4e, 0x61, 0x6d,
	0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a,
	0x04, 0x55, 0x6e, 0x69, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x55, 0x6e, 0x69,
	0x74, 0x12, 0x14, 0x0a, 0x05, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x05, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x42, 0x41, 0x5a, 0x3f, 0x67, 0x69, 0x74, 0x6c, 0x61,
	0x62, 0x2e, 0x74, 0x74, 0x79, 0x75, 0x79, 0x69, 0x6e, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x54, 0x65,
	0x73, 0x74, 0x44, 0x65, 0x76, 0x65, 0x6c, 0x6f, 0x70, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x70, 0x72,
	0x6f, 0x62, 0x65, 0x2d, 0x62, 0x61, 0x63, 0x6b, 0x65, 0x6e, 0x64, 0x2f, 0x6d, 0x61, 0x6e, 0x61,
	0x67, 0x65, 0x72, 0x2f, 0x72, 0x70, 0x63, 0x2f, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x33,
}

var (
	file_manager_sla_threshold_proto_rawDescOnce sync.Once
	file_manager_sla_threshold_proto_rawDescData = file_manager_sla_threshold_proto_rawDesc
)

func file_manager_sla_threshold_proto_rawDescGZIP() []byte {
	file_manager_sla_threshold_proto_rawDescOnce.Do(func() {
		file_manager_sla_threshold_proto_rawDescData = protoimpl.X.CompressGZIP(file_manager_sla_threshold_proto_rawDescData)
	})
	return file_manager_sla_threshold_proto_rawDescData
}

var file_manager_sla_threshold_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_manager_sla_threshold_proto_goTypes = []any{
	(*SlaThreshold)(nil), // 0: manager.SlaThreshold
	(pb.PlatformType)(0), // 1: common.PlatformType
	(pb.BranchType)(0),   // 2: common.BranchType
}
var file_manager_sla_threshold_proto_depIdxs = []int32{
	1, // 0: manager.SlaThreshold.platform_type:type_name -> common.PlatformType
	2, // 1: manager.SlaThreshold.branch_type:type_name -> common.BranchType
	2, // [2:2] is the sub-list for method output_type
	2, // [2:2] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_manager_sla_threshold_proto_init() }
func file_manager_sla_threshold_proto_init() {
	if File_manager_sla_threshold_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_manager_sla_threshold_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_manager_sla_threshold_proto_goTypes,
		DependencyIndexes: file_manager_sla_threshold_proto_depIdxs,
		MessageInfos:      file_manager_sla_threshold_proto_msgTypes,
	}.Build()
	File_manager_sla_threshold_proto = out.File
	file_manager_sla_threshold_proto_rawDesc = nil
	file_manager_sla_threshold_proto_goTypes = nil
	file_manager_sla_threshold_proto_depIdxs = nil
}
