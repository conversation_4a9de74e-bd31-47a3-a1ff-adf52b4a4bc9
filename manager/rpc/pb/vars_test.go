package pb

import (
	"testing"

	_ "google.golang.org/protobuf/encoding/protojson"
)

func TestValidateUnmarshalOptions(t *testing.T) {
	in := `{
        "body": {
            "raw": "",
            "type": 0,
            "form_data": [
                {
                    "key": "",
                    "value": "",
                    "description": ""
                }
            ]
        },
        "name": "请求1",
        "method": "GET",
        "exports": [
            {
                "body": {
                    "type": 0,
                    "expression": "$.body.args.a"
                },
                "name": "export_a",
                "source": 1,
                "headers": {
                    "key": ""
                }
            }
        ],
        "headers": [
            {
                "key": "",
                "value": "",
                "description": ""
            }
        ],
        "imports": [
            {
                "name": "a",
                "export": {
                    "value": "",
                    "node_id": ""
                },
                "manual": {
                    "value": "1"
                },
                "source": 0,
                "account": {},
                "environment": {}
            },
            {
                "name": "b",
                "export": {
                    "value": "",
                    "node_id": ""
                },
                "manual": {
                    "value": "2"
                },
                "source": 0,
                "account": {},
                "environment": {}
            }
        ],
        "timeout": {
            "connect_timeout": 6000,
            "request_timeout": 6001,
            "response_timeout": 6002
        },
        "assertions": [
            {
                "body": {
                    "type": 1,
                    "regex": {
                        "expression": ""
                    },
                    "jmespath": {
                        "compare": "EQ",
                        "expression": "",
                        "expectation": ""
                    }
                },
                "source": 2,
                "headers": {
                    "key": "",
                    "expression": ""
                },
                "status_code": {
                    "compare": "EQ",
                    "expectation": "200"
                }
            }
        ],
        "query_params": [
            {
                "key": "a",
                "value": "{{a}}",
                "description": ""
            },
            {
                "key": "b",
                "value": "{{b}}",
                "description": ""
            }
        ],
        "authorization": {
            "type": 1,
            "api_key": {
                "key": "",
                "value": "",
                "add_to": 1
            },
            "basic_auth": {
                "password": "",
                "username": ""
            },
            "bearer_token": {
                "token": ""
            }
        }
    }`

	c := &HttpRequestComponent{}
	if err := ValidateUnmarshalOptions.Unmarshal([]byte(in), c); err != nil {
		t.Fatal(err)
	}

	if err := c.ValidateAll(); err != nil {
		t.Fatal(err)
	}

	t.Logf("Url: %s\n", c.Url)
}
