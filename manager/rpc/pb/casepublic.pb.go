// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.1
// 	protoc        v5.29.2
// source: manager/casepublic.proto

package pb

import (
	reflect "reflect"
	sync "sync"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Case struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ProjectId     string                 `protobuf:"bytes,1,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`             // 项目ID
	BranchId      string                 `protobuf:"bytes,2,opt,name=branch_id,json=branchId,proto3" json:"branch_id,omitempty"`                // 分支ID
	CaseId        string                 `protobuf:"bytes,3,opt,name=case_id,json=caseId,proto3" json:"case_id,omitempty"`                      // 用例ID
	Name          string                 `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`                                        // 用例名称
	Description   string                 `protobuf:"bytes,5,opt,name=description,proto3" json:"description,omitempty"`                          // 用例描述
	Priority      int64                  `protobuf:"varint,6,opt,name=priority,proto3" json:"priority,omitempty"`                               // 优先级
	Tags          []string               `protobuf:"bytes,7,rep,name=tags,proto3" json:"tags,omitempty"`                                        // 标签
	State         string                 `protobuf:"bytes,8,opt,name=state,proto3" json:"state,omitempty"`                                      // 状态
	AccountConfig *AccountConfig         `protobuf:"bytes,9,opt,name=account_config,json=accountConfig,proto3" json:"account_config,omitempty"` // 池账号配置信息
	Version       string                 `protobuf:"bytes,10,opt,name=version,proto3" json:"version,omitempty"`                                 // 用例版本
	MaintainedBy  string                 `protobuf:"bytes,11,opt,name=maintained_by,json=maintainedBy,proto3" json:"maintained_by,omitempty"`   // 维护者
	CreatedBy     string                 `protobuf:"bytes,12,opt,name=created_by,json=createdBy,proto3" json:"created_by,omitempty"`            // 创建者
	UpdatedBy     string                 `protobuf:"bytes,13,opt,name=updated_by,json=updatedBy,proto3" json:"updated_by,omitempty"`            // 更新者
	CreatedAt     int64                  `protobuf:"varint,14,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`           // 创建时间
	UpdatedAt     int64                  `protobuf:"varint,15,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`           // 更新时间
	Nodes         []*Node                `protobuf:"bytes,16,rep,name=nodes,proto3" json:"nodes,omitempty"`
	Edges         []*Edge                `protobuf:"bytes,17,rep,name=edges,proto3" json:"edges,omitempty"`
	Combos        []*Combo               `protobuf:"bytes,18,rep,name=combos,proto3" json:"combos,omitempty"`
	CaseType      string                 `protobuf:"bytes,19,opt,name=case_type,json=caseType,proto3" json:"case_type,omitempty"` // 用例ID
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Case) Reset() {
	*x = Case{}
	mi := &file_manager_casepublic_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Case) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Case) ProtoMessage() {}

func (x *Case) ProtoReflect() protoreflect.Message {
	mi := &file_manager_casepublic_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Case.ProtoReflect.Descriptor instead.
func (*Case) Descriptor() ([]byte, []int) {
	return file_manager_casepublic_proto_rawDescGZIP(), []int{0}
}

func (x *Case) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *Case) GetBranchId() string {
	if x != nil {
		return x.BranchId
	}
	return ""
}

func (x *Case) GetCaseId() string {
	if x != nil {
		return x.CaseId
	}
	return ""
}

func (x *Case) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Case) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *Case) GetPriority() int64 {
	if x != nil {
		return x.Priority
	}
	return 0
}

func (x *Case) GetTags() []string {
	if x != nil {
		return x.Tags
	}
	return nil
}

func (x *Case) GetState() string {
	if x != nil {
		return x.State
	}
	return ""
}

func (x *Case) GetAccountConfig() *AccountConfig {
	if x != nil {
		return x.AccountConfig
	}
	return nil
}

func (x *Case) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *Case) GetMaintainedBy() string {
	if x != nil {
		return x.MaintainedBy
	}
	return ""
}

func (x *Case) GetCreatedBy() string {
	if x != nil {
		return x.CreatedBy
	}
	return ""
}

func (x *Case) GetUpdatedBy() string {
	if x != nil {
		return x.UpdatedBy
	}
	return ""
}

func (x *Case) GetCreatedAt() int64 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

func (x *Case) GetUpdatedAt() int64 {
	if x != nil {
		return x.UpdatedAt
	}
	return 0
}

func (x *Case) GetNodes() []*Node {
	if x != nil {
		return x.Nodes
	}
	return nil
}

func (x *Case) GetEdges() []*Edge {
	if x != nil {
		return x.Edges
	}
	return nil
}

func (x *Case) GetCombos() []*Combo {
	if x != nil {
		return x.Combos
	}
	return nil
}

func (x *Case) GetCaseType() string {
	if x != nil {
		return x.CaseType
	}
	return ""
}

type FailCase struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ProjectId     string                 `protobuf:"bytes,1,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`             // 项目ID
	BranchId      string                 `protobuf:"bytes,2,opt,name=branch_id,json=branchId,proto3" json:"branch_id,omitempty"`                // 分支ID
	CaseId        string                 `protobuf:"bytes,3,opt,name=case_id,json=caseId,proto3" json:"case_id,omitempty"`                      // 用例ID
	Name          string                 `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`                                        // 用例名称
	Description   string                 `protobuf:"bytes,5,opt,name=description,proto3" json:"description,omitempty"`                          // 用例描述
	Priority      int64                  `protobuf:"varint,6,opt,name=priority,proto3" json:"priority,omitempty"`                               // 优先级
	Tags          []string               `protobuf:"bytes,7,rep,name=tags,proto3" json:"tags,omitempty"`                                        // 标签
	State         string                 `protobuf:"bytes,8,opt,name=state,proto3" json:"state,omitempty"`                                      // 状态
	AccountConfig *AccountConfig         `protobuf:"bytes,9,opt,name=account_config,json=accountConfig,proto3" json:"account_config,omitempty"` // 池账号配置信息
	Version       string                 `protobuf:"bytes,10,opt,name=version,proto3" json:"version,omitempty"`                                 // 用例版本
	MaintainedBy  string                 `protobuf:"bytes,11,opt,name=maintained_by,json=maintainedBy,proto3" json:"maintained_by,omitempty"`   // 维护者
	CreatedBy     string                 `protobuf:"bytes,12,opt,name=created_by,json=createdBy,proto3" json:"created_by,omitempty"`            // 创建者
	UpdatedBy     string                 `protobuf:"bytes,13,opt,name=updated_by,json=updatedBy,proto3" json:"updated_by,omitempty"`            // 更新者
	CreatedAt     int64                  `protobuf:"varint,14,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`           // 创建时间
	UpdatedAt     int64                  `protobuf:"varint,15,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`           // 更新时间
	Nodes         []*Node                `protobuf:"bytes,16,rep,name=nodes,proto3" json:"nodes,omitempty"`
	Edges         []*Edge                `protobuf:"bytes,17,rep,name=edges,proto3" json:"edges,omitempty"`
	Combos        []*Combo               `protobuf:"bytes,18,rep,name=combos,proto3" json:"combos,omitempty"`
	CaseType      string                 `protobuf:"bytes,19,opt,name=case_type,json=caseType,proto3" json:"case_type,omitempty"`     // 用例类型
	FailCount     uint32                 `protobuf:"varint,21,opt,name=fail_count,json=failCount,proto3" json:"fail_count,omitempty"` // 失败次数
	Rank          uint32                 `protobuf:"varint,22,opt,name=rank,proto3" json:"rank,omitempty"`                            // 排名
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FailCase) Reset() {
	*x = FailCase{}
	mi := &file_manager_casepublic_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FailCase) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FailCase) ProtoMessage() {}

func (x *FailCase) ProtoReflect() protoreflect.Message {
	mi := &file_manager_casepublic_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FailCase.ProtoReflect.Descriptor instead.
func (*FailCase) Descriptor() ([]byte, []int) {
	return file_manager_casepublic_proto_rawDescGZIP(), []int{1}
}

func (x *FailCase) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *FailCase) GetBranchId() string {
	if x != nil {
		return x.BranchId
	}
	return ""
}

func (x *FailCase) GetCaseId() string {
	if x != nil {
		return x.CaseId
	}
	return ""
}

func (x *FailCase) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *FailCase) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *FailCase) GetPriority() int64 {
	if x != nil {
		return x.Priority
	}
	return 0
}

func (x *FailCase) GetTags() []string {
	if x != nil {
		return x.Tags
	}
	return nil
}

func (x *FailCase) GetState() string {
	if x != nil {
		return x.State
	}
	return ""
}

func (x *FailCase) GetAccountConfig() *AccountConfig {
	if x != nil {
		return x.AccountConfig
	}
	return nil
}

func (x *FailCase) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *FailCase) GetMaintainedBy() string {
	if x != nil {
		return x.MaintainedBy
	}
	return ""
}

func (x *FailCase) GetCreatedBy() string {
	if x != nil {
		return x.CreatedBy
	}
	return ""
}

func (x *FailCase) GetUpdatedBy() string {
	if x != nil {
		return x.UpdatedBy
	}
	return ""
}

func (x *FailCase) GetCreatedAt() int64 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

func (x *FailCase) GetUpdatedAt() int64 {
	if x != nil {
		return x.UpdatedAt
	}
	return 0
}

func (x *FailCase) GetNodes() []*Node {
	if x != nil {
		return x.Nodes
	}
	return nil
}

func (x *FailCase) GetEdges() []*Edge {
	if x != nil {
		return x.Edges
	}
	return nil
}

func (x *FailCase) GetCombos() []*Combo {
	if x != nil {
		return x.Combos
	}
	return nil
}

func (x *FailCase) GetCaseType() string {
	if x != nil {
		return x.CaseType
	}
	return ""
}

func (x *FailCase) GetFailCount() uint32 {
	if x != nil {
		return x.FailCount
	}
	return 0
}

func (x *FailCase) GetRank() uint32 {
	if x != nil {
		return x.Rank
	}
	return 0
}

type CaseFailStatForMq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ProjectId     string                 `protobuf:"bytes,1,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"` // 项目ID
	BranchId      string                 `protobuf:"bytes,2,opt,name=branch_id,json=branchId,proto3" json:"branch_id,omitempty"`    // 分支ID
	ExecuteId     string                 `protobuf:"bytes,3,opt,name=execute_id,json=executeId,proto3" json:"execute_id,omitempty"` // 执行ID
	CaseId        string                 `protobuf:"bytes,4,opt,name=case_id,json=caseId,proto3" json:"case_id,omitempty"`          // 用例ID
	CaseType      string                 `protobuf:"bytes,5,opt,name=case_type,json=caseType,proto3" json:"case_type,omitempty"`    // 用例类型
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CaseFailStatForMq) Reset() {
	*x = CaseFailStatForMq{}
	mi := &file_manager_casepublic_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CaseFailStatForMq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CaseFailStatForMq) ProtoMessage() {}

func (x *CaseFailStatForMq) ProtoReflect() protoreflect.Message {
	mi := &file_manager_casepublic_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CaseFailStatForMq.ProtoReflect.Descriptor instead.
func (*CaseFailStatForMq) Descriptor() ([]byte, []int) {
	return file_manager_casepublic_proto_rawDescGZIP(), []int{2}
}

func (x *CaseFailStatForMq) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *CaseFailStatForMq) GetBranchId() string {
	if x != nil {
		return x.BranchId
	}
	return ""
}

func (x *CaseFailStatForMq) GetExecuteId() string {
	if x != nil {
		return x.ExecuteId
	}
	return ""
}

func (x *CaseFailStatForMq) GetCaseId() string {
	if x != nil {
		return x.CaseId
	}
	return ""
}

func (x *CaseFailStatForMq) GetCaseType() string {
	if x != nil {
		return x.CaseType
	}
	return ""
}

var File_manager_casepublic_proto protoreflect.FileDescriptor

var file_manager_casepublic_proto_rawDesc = []byte{
	0x0a, 0x18, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2f, 0x63, 0x61, 0x73, 0x65, 0x70, 0x75,
	0x62, 0x6c, 0x69, 0x63, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x07, 0x6d, 0x61, 0x6e, 0x61,
	0x67, 0x65, 0x72, 0x1a, 0x12, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2f, 0x62, 0x61, 0x73,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x15, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72,
	0x2f, 0x65, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xe0,
	0x04, 0x0a, 0x04, 0x43, 0x61, 0x73, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65,
	0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x72, 0x6f,
	0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x62, 0x72, 0x61, 0x6e, 0x63, 0x68,
	0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x62, 0x72, 0x61, 0x6e, 0x63,
	0x68, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x63, 0x61, 0x73, 0x65, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x72, 0x69, 0x6f, 0x72, 0x69, 0x74, 0x79, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x70, 0x72, 0x69, 0x6f, 0x72, 0x69, 0x74, 0x79, 0x12, 0x12,
	0x0a, 0x04, 0x74, 0x61, 0x67, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x09, 0x52, 0x04, 0x74, 0x61,
	0x67, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x12, 0x3d, 0x0a, 0x0e, 0x61, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x16, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x41, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x0d, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x18, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69,
	0x6f, 0x6e, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f,
	0x6e, 0x12, 0x23, 0x0a, 0x0d, 0x6d, 0x61, 0x69, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x64, 0x5f,
	0x62, 0x79, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x6d, 0x61, 0x69, 0x6e, 0x74, 0x61,
	0x69, 0x6e, 0x65, 0x64, 0x42, 0x79, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x64, 0x5f, 0x62, 0x79, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x64, 0x42, 0x79, 0x12, 0x1d, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64,
	0x5f, 0x62, 0x79, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x64, 0x42, 0x79, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f,
	0x61, 0x74, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x64, 0x41, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61,
	0x74, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64,
	0x41, 0x74, 0x12, 0x23, 0x0a, 0x05, 0x6e, 0x6f, 0x64, 0x65, 0x73, 0x18, 0x10, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x0d, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x4e, 0x6f, 0x64, 0x65,
	0x52, 0x05, 0x6e, 0x6f, 0x64, 0x65, 0x73, 0x12, 0x23, 0x0a, 0x05, 0x65, 0x64, 0x67, 0x65, 0x73,
	0x18, 0x11, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72,
	0x2e, 0x45, 0x64, 0x67, 0x65, 0x52, 0x05, 0x65, 0x64, 0x67, 0x65, 0x73, 0x12, 0x26, 0x0a, 0x06,
	0x63, 0x6f, 0x6d, 0x62, 0x6f, 0x73, 0x18, 0x12, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x6d,
	0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x43, 0x6f, 0x6d, 0x62, 0x6f, 0x52, 0x06, 0x63, 0x6f,
	0x6d, 0x62, 0x6f, 0x73, 0x12, 0x1b, 0x0a, 0x09, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x13, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x61, 0x73, 0x65, 0x54, 0x79, 0x70,
	0x65, 0x22, 0x97, 0x05, 0x0a, 0x08, 0x46, 0x61, 0x69, 0x6c, 0x43, 0x61, 0x73, 0x65, 0x12, 0x1d,
	0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x12, 0x1b, 0x0a,
	0x09, 0x62, 0x72, 0x61, 0x6e, 0x63, 0x68, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x62, 0x72, 0x61, 0x6e, 0x63, 0x68, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x63, 0x61,
	0x73, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x63, 0x61, 0x73,
	0x65, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x72, 0x69,
	0x6f, 0x72, 0x69, 0x74, 0x79, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x70, 0x72, 0x69,
	0x6f, 0x72, 0x69, 0x74, 0x79, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x61, 0x67, 0x73, 0x18, 0x07, 0x20,
	0x03, 0x28, 0x09, 0x52, 0x04, 0x74, 0x61, 0x67, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x74, 0x61,
	0x74, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x12,
	0x3d, 0x0a, 0x0e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65,
	0x72, 0x2e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52,
	0x0d, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x18,
	0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x23, 0x0a, 0x0d, 0x6d, 0x61, 0x69, 0x6e,
	0x74, 0x61, 0x69, 0x6e, 0x65, 0x64, 0x5f, 0x62, 0x79, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0c, 0x6d, 0x61, 0x69, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x64, 0x42, 0x79, 0x12, 0x1d, 0x0a,
	0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x62, 0x79, 0x18, 0x0c, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x42, 0x79, 0x12, 0x1d, 0x0a, 0x0a,
	0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x62, 0x79, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x42, 0x79, 0x12, 0x1d, 0x0a, 0x0a, 0x63,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x75, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09,
	0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x23, 0x0a, 0x05, 0x6e, 0x6f, 0x64,
	0x65, 0x73, 0x18, 0x10, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67,
	0x65, 0x72, 0x2e, 0x4e, 0x6f, 0x64, 0x65, 0x52, 0x05, 0x6e, 0x6f, 0x64, 0x65, 0x73, 0x12, 0x23,
	0x0a, 0x05, 0x65, 0x64, 0x67, 0x65, 0x73, 0x18, 0x11, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0d, 0x2e,
	0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x45, 0x64, 0x67, 0x65, 0x52, 0x05, 0x65, 0x64,
	0x67, 0x65, 0x73, 0x12, 0x26, 0x0a, 0x06, 0x63, 0x6f, 0x6d, 0x62, 0x6f, 0x73, 0x18, 0x12, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x43, 0x6f,
	0x6d, 0x62, 0x6f, 0x52, 0x06, 0x63, 0x6f, 0x6d, 0x62, 0x6f, 0x73, 0x12, 0x1b, 0x0a, 0x09, 0x63,
	0x61, 0x73, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x13, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x63, 0x61, 0x73, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x66, 0x61, 0x69, 0x6c,
	0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x15, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x66, 0x61,
	0x69, 0x6c, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x72, 0x61, 0x6e, 0x6b, 0x18,
	0x16, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x72, 0x61, 0x6e, 0x6b, 0x22, 0xa4, 0x01, 0x0a, 0x11,
	0x43, 0x61, 0x73, 0x65, 0x46, 0x61, 0x69, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x46, 0x6f, 0x72, 0x4d,
	0x71, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64,
	0x12, 0x1b, 0x0a, 0x09, 0x62, 0x72, 0x61, 0x6e, 0x63, 0x68, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x62, 0x72, 0x61, 0x6e, 0x63, 0x68, 0x49, 0x64, 0x12, 0x1d, 0x0a,
	0x0a, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07,
	0x63, 0x61, 0x73, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x63,
	0x61, 0x73, 0x65, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x61, 0x73, 0x65, 0x54, 0x79,
	0x70, 0x65, 0x42, 0x41, 0x5a, 0x3f, 0x67, 0x69, 0x74, 0x6c, 0x61, 0x62, 0x2e, 0x74, 0x74, 0x79,
	0x75, 0x79, 0x69, 0x6e, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x54, 0x65, 0x73, 0x74, 0x44, 0x65, 0x76,
	0x65, 0x6c, 0x6f, 0x70, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x70, 0x72, 0x6f, 0x62, 0x65, 0x2d, 0x62,
	0x61, 0x63, 0x6b, 0x65, 0x6e, 0x64, 0x2f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2f, 0x72,
	0x70, 0x63, 0x2f, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_manager_casepublic_proto_rawDescOnce sync.Once
	file_manager_casepublic_proto_rawDescData = file_manager_casepublic_proto_rawDesc
)

func file_manager_casepublic_proto_rawDescGZIP() []byte {
	file_manager_casepublic_proto_rawDescOnce.Do(func() {
		file_manager_casepublic_proto_rawDescData = protoimpl.X.CompressGZIP(file_manager_casepublic_proto_rawDescData)
	})
	return file_manager_casepublic_proto_rawDescData
}

var file_manager_casepublic_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_manager_casepublic_proto_goTypes = []any{
	(*Case)(nil),              // 0: manager.Case
	(*FailCase)(nil),          // 1: manager.FailCase
	(*CaseFailStatForMq)(nil), // 2: manager.CaseFailStatForMq
	(*AccountConfig)(nil),     // 3: manager.AccountConfig
	(*Node)(nil),              // 4: manager.Node
	(*Edge)(nil),              // 5: manager.Edge
	(*Combo)(nil),             // 6: manager.Combo
}
var file_manager_casepublic_proto_depIdxs = []int32{
	3, // 0: manager.Case.account_config:type_name -> manager.AccountConfig
	4, // 1: manager.Case.nodes:type_name -> manager.Node
	5, // 2: manager.Case.edges:type_name -> manager.Edge
	6, // 3: manager.Case.combos:type_name -> manager.Combo
	3, // 4: manager.FailCase.account_config:type_name -> manager.AccountConfig
	4, // 5: manager.FailCase.nodes:type_name -> manager.Node
	5, // 6: manager.FailCase.edges:type_name -> manager.Edge
	6, // 7: manager.FailCase.combos:type_name -> manager.Combo
	8, // [8:8] is the sub-list for method output_type
	8, // [8:8] is the sub-list for method input_type
	8, // [8:8] is the sub-list for extension type_name
	8, // [8:8] is the sub-list for extension extendee
	0, // [0:8] is the sub-list for field type_name
}

func init() { file_manager_casepublic_proto_init() }
func file_manager_casepublic_proto_init() {
	if File_manager_casepublic_proto != nil {
		return
	}
	file_manager_base_proto_init()
	file_manager_element_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_manager_casepublic_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_manager_casepublic_proto_goTypes,
		DependencyIndexes: file_manager_casepublic_proto_depIdxs,
		MessageInfos:      file_manager_casepublic_proto_msgTypes,
	}.Build()
	File_manager_casepublic_proto = out.File
	file_manager_casepublic_proto_rawDesc = nil
	file_manager_casepublic_proto_goTypes = nil
	file_manager_casepublic_proto_depIdxs = nil
}
