// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.1
// 	protoc        v5.29.2
// source: manager/apiplan.proto

package pb

import (
	reflect "reflect"
	sync "sync"

	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"

	pb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ApiPlan struct {
	state              protoimpl.MessageState `protogen:"open.v1"`
	ProjectId          string                 `protobuf:"bytes,1,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`                                                           // 项目ID
	PlanId             string                 `protobuf:"bytes,2,opt,name=plan_id,json=planId,proto3" json:"plan_id,omitempty"`                                                                    // 计划ID
	Name               string                 `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`                                                                                      // 计划名称
	Description        string                 `protobuf:"bytes,4,opt,name=description,proto3" json:"description,omitempty"`                                                                        // 集合描述
	Priority           int64                  `protobuf:"varint,5,opt,name=priority,proto3" json:"priority,omitempty"`                                                                             // 优先级
	Tags               []string               `protobuf:"bytes,6,rep,name=tags,proto3" json:"tags,omitempty"`                                                                                      // 标签
	State              CommonState            `protobuf:"varint,7,opt,name=state,proto3,enum=manager.CommonState" json:"state,omitempty"`                                                          // 状态
	Type               pb.TriggerMode         `protobuf:"varint,8,opt,name=type,proto3,enum=common.TriggerMode" json:"type,omitempty"`                                                             // 计划类型（手动、定时、接口）
	Purpose            pb.PurposeType         `protobuf:"varint,9,opt,name=purpose,proto3,enum=common.PurposeType" json:"purpose,omitempty"`                                                       // 计划用途(常规、精准测试)
	CronExpression     string                 `protobuf:"bytes,10,opt,name=cron_expression,json=cronExpression,proto3" json:"cron_expression,omitempty"`                                           // 定时触发计划的Cron表达式
	GeneralConfigId    string                 `protobuf:"bytes,11,opt,name=general_config_id,json=generalConfigId,proto3" json:"general_config_id,omitempty"`                                      // 通用配置ID
	AccountConfigIds   []string               `protobuf:"bytes,12,rep,name=account_config_ids,json=accountConfigIds,proto3" json:"account_config_ids,omitempty"`                                   // 池账号配置ID列表
	SuiteExecutionMode ExecutionMode          `protobuf:"varint,13,opt,name=suite_execution_mode,json=suiteExecutionMode,proto3,enum=manager.ExecutionMode" json:"suite_execution_mode,omitempty"` // 集合执行方式
	CaseExecutionMode  ExecutionMode          `protobuf:"varint,14,opt,name=case_execution_mode,json=caseExecutionMode,proto3,enum=manager.ExecutionMode" json:"case_execution_mode,omitempty"`    // 用例执行方式
	MaintainedBy       string                 `protobuf:"bytes,15,opt,name=maintained_by,json=maintainedBy,proto3" json:"maintained_by,omitempty"`                                                 // 维护者
	CreatedBy          string                 `protobuf:"bytes,16,opt,name=created_by,json=createdBy,proto3" json:"created_by,omitempty"`                                                          // 创建者
	UpdatedBy          string                 `protobuf:"bytes,17,opt,name=updated_by,json=updatedBy,proto3" json:"updated_by,omitempty"`                                                          // 更新者
	CreatedAt          int64                  `protobuf:"varint,18,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`                                                         // 创建时间
	UpdatedAt          int64                  `protobuf:"varint,19,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`                                                         // 更新时间
	CategoryId         string                 `protobuf:"bytes,20,opt,name=category_id,json=categoryId,proto3" json:"category_id,omitempty"`                                                       // 分类ID
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *ApiPlan) Reset() {
	*x = ApiPlan{}
	mi := &file_manager_apiplan_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ApiPlan) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ApiPlan) ProtoMessage() {}

func (x *ApiPlan) ProtoReflect() protoreflect.Message {
	mi := &file_manager_apiplan_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ApiPlan.ProtoReflect.Descriptor instead.
func (*ApiPlan) Descriptor() ([]byte, []int) {
	return file_manager_apiplan_proto_rawDescGZIP(), []int{0}
}

func (x *ApiPlan) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *ApiPlan) GetPlanId() string {
	if x != nil {
		return x.PlanId
	}
	return ""
}

func (x *ApiPlan) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ApiPlan) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *ApiPlan) GetPriority() int64 {
	if x != nil {
		return x.Priority
	}
	return 0
}

func (x *ApiPlan) GetTags() []string {
	if x != nil {
		return x.Tags
	}
	return nil
}

func (x *ApiPlan) GetState() CommonState {
	if x != nil {
		return x.State
	}
	return CommonState_CS_NULL
}

func (x *ApiPlan) GetType() pb.TriggerMode {
	if x != nil {
		return x.Type
	}
	return pb.TriggerMode(0)
}

func (x *ApiPlan) GetPurpose() pb.PurposeType {
	if x != nil {
		return x.Purpose
	}
	return pb.PurposeType(0)
}

func (x *ApiPlan) GetCronExpression() string {
	if x != nil {
		return x.CronExpression
	}
	return ""
}

func (x *ApiPlan) GetGeneralConfigId() string {
	if x != nil {
		return x.GeneralConfigId
	}
	return ""
}

func (x *ApiPlan) GetAccountConfigIds() []string {
	if x != nil {
		return x.AccountConfigIds
	}
	return nil
}

func (x *ApiPlan) GetSuiteExecutionMode() ExecutionMode {
	if x != nil {
		return x.SuiteExecutionMode
	}
	return ExecutionMode_EM_NULL
}

func (x *ApiPlan) GetCaseExecutionMode() ExecutionMode {
	if x != nil {
		return x.CaseExecutionMode
	}
	return ExecutionMode_EM_NULL
}

func (x *ApiPlan) GetMaintainedBy() string {
	if x != nil {
		return x.MaintainedBy
	}
	return ""
}

func (x *ApiPlan) GetCreatedBy() string {
	if x != nil {
		return x.CreatedBy
	}
	return ""
}

func (x *ApiPlan) GetUpdatedBy() string {
	if x != nil {
		return x.UpdatedBy
	}
	return ""
}

func (x *ApiPlan) GetCreatedAt() int64 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

func (x *ApiPlan) GetUpdatedAt() int64 {
	if x != nil {
		return x.UpdatedAt
	}
	return 0
}

func (x *ApiPlan) GetCategoryId() string {
	if x != nil {
		return x.CategoryId
	}
	return ""
}

type SuiteTypeId struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	SuiteType     string                 `protobuf:"bytes,1,opt,name=suite_type,json=suiteType,proto3" json:"suite_type,omitempty"` // 集合类型
	SuiteId       string                 `protobuf:"bytes,2,opt,name=suite_id,json=suiteId,proto3" json:"suite_id,omitempty"`       // 集合ID
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SuiteTypeId) Reset() {
	*x = SuiteTypeId{}
	mi := &file_manager_apiplan_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SuiteTypeId) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SuiteTypeId) ProtoMessage() {}

func (x *SuiteTypeId) ProtoReflect() protoreflect.Message {
	mi := &file_manager_apiplan_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SuiteTypeId.ProtoReflect.Descriptor instead.
func (*SuiteTypeId) Descriptor() ([]byte, []int) {
	return file_manager_apiplan_proto_rawDescGZIP(), []int{1}
}

func (x *SuiteTypeId) GetSuiteType() string {
	if x != nil {
		return x.SuiteType
	}
	return ""
}

func (x *SuiteTypeId) GetSuiteId() string {
	if x != nil {
		return x.SuiteId
	}
	return ""
}

type SearchApiPlanItem struct {
	state              protoimpl.MessageState `protogen:"open.v1"`
	ProjectId          string                 `protobuf:"bytes,1,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`                                                           // 项目ID
	PlanId             string                 `protobuf:"bytes,2,opt,name=plan_id,json=planId,proto3" json:"plan_id,omitempty"`                                                                    // 计划ID
	Name               string                 `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`                                                                                      // 计划名称
	Description        string                 `protobuf:"bytes,4,opt,name=description,proto3" json:"description,omitempty"`                                                                        // 计划描述
	Priority           int64                  `protobuf:"varint,5,opt,name=priority,proto3" json:"priority,omitempty"`                                                                             // 优先级
	Tags               []string               `protobuf:"bytes,6,rep,name=tags,proto3" json:"tags,omitempty"`                                                                                      // 标签
	State              CommonState            `protobuf:"varint,7,opt,name=state,proto3,enum=manager.CommonState" json:"state,omitempty"`                                                          // 状态
	Type               pb.TriggerMode         `protobuf:"varint,8,opt,name=type,proto3,enum=common.TriggerMode" json:"type,omitempty"`                                                             // 计划类型（手动、定时、接口）
	Purpose            pb.PurposeType         `protobuf:"varint,9,opt,name=purpose,proto3,enum=common.PurposeType" json:"purpose,omitempty"`                                                       // 计划用途
	CronExpression     string                 `protobuf:"bytes,10,opt,name=cron_expression,json=cronExpression,proto3" json:"cron_expression,omitempty"`                                           // 定时触发计划的Cron表达式
	SuiteExecutionMode ExecutionMode          `protobuf:"varint,11,opt,name=suite_execution_mode,json=suiteExecutionMode,proto3,enum=manager.ExecutionMode" json:"suite_execution_mode,omitempty"` // 集合执行方式
	CaseExecutionMode  ExecutionMode          `protobuf:"varint,12,opt,name=case_execution_mode,json=caseExecutionMode,proto3,enum=manager.ExecutionMode" json:"case_execution_mode,omitempty"`    // 用例执行方式
	SuiteIncluded      int64                  `protobuf:"varint,13,opt,name=suite_included,json=suiteIncluded,proto3" json:"suite_included,omitempty"`                                             // 包含的集合数
	MaintainedBy       string                 `protobuf:"bytes,14,opt,name=maintained_by,json=maintainedBy,proto3" json:"maintained_by,omitempty"`                                                 // 维护者
	CreatedBy          string                 `protobuf:"bytes,15,opt,name=created_by,json=createdBy,proto3" json:"created_by,omitempty"`                                                          // 创建者
	UpdatedBy          string                 `protobuf:"bytes,16,opt,name=updated_by,json=updatedBy,proto3" json:"updated_by,omitempty"`                                                          // 更新者
	CreatedAt          int64                  `protobuf:"varint,17,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`                                                         // 创建时间
	UpdatedAt          int64                  `protobuf:"varint,18,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`                                                         // 更新时间
	CategoryId         string                 `protobuf:"bytes,19,opt,name=category_id,json=categoryId,proto3" json:"category_id,omitempty"`                                                       // 分类ID
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *SearchApiPlanItem) Reset() {
	*x = SearchApiPlanItem{}
	mi := &file_manager_apiplan_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SearchApiPlanItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchApiPlanItem) ProtoMessage() {}

func (x *SearchApiPlanItem) ProtoReflect() protoreflect.Message {
	mi := &file_manager_apiplan_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchApiPlanItem.ProtoReflect.Descriptor instead.
func (*SearchApiPlanItem) Descriptor() ([]byte, []int) {
	return file_manager_apiplan_proto_rawDescGZIP(), []int{2}
}

func (x *SearchApiPlanItem) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *SearchApiPlanItem) GetPlanId() string {
	if x != nil {
		return x.PlanId
	}
	return ""
}

func (x *SearchApiPlanItem) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *SearchApiPlanItem) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *SearchApiPlanItem) GetPriority() int64 {
	if x != nil {
		return x.Priority
	}
	return 0
}

func (x *SearchApiPlanItem) GetTags() []string {
	if x != nil {
		return x.Tags
	}
	return nil
}

func (x *SearchApiPlanItem) GetState() CommonState {
	if x != nil {
		return x.State
	}
	return CommonState_CS_NULL
}

func (x *SearchApiPlanItem) GetType() pb.TriggerMode {
	if x != nil {
		return x.Type
	}
	return pb.TriggerMode(0)
}

func (x *SearchApiPlanItem) GetPurpose() pb.PurposeType {
	if x != nil {
		return x.Purpose
	}
	return pb.PurposeType(0)
}

func (x *SearchApiPlanItem) GetCronExpression() string {
	if x != nil {
		return x.CronExpression
	}
	return ""
}

func (x *SearchApiPlanItem) GetSuiteExecutionMode() ExecutionMode {
	if x != nil {
		return x.SuiteExecutionMode
	}
	return ExecutionMode_EM_NULL
}

func (x *SearchApiPlanItem) GetCaseExecutionMode() ExecutionMode {
	if x != nil {
		return x.CaseExecutionMode
	}
	return ExecutionMode_EM_NULL
}

func (x *SearchApiPlanItem) GetSuiteIncluded() int64 {
	if x != nil {
		return x.SuiteIncluded
	}
	return 0
}

func (x *SearchApiPlanItem) GetMaintainedBy() string {
	if x != nil {
		return x.MaintainedBy
	}
	return ""
}

func (x *SearchApiPlanItem) GetCreatedBy() string {
	if x != nil {
		return x.CreatedBy
	}
	return ""
}

func (x *SearchApiPlanItem) GetUpdatedBy() string {
	if x != nil {
		return x.UpdatedBy
	}
	return ""
}

func (x *SearchApiPlanItem) GetCreatedAt() int64 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

func (x *SearchApiPlanItem) GetUpdatedAt() int64 {
	if x != nil {
		return x.UpdatedAt
	}
	return 0
}

func (x *SearchApiPlanItem) GetCategoryId() string {
	if x != nil {
		return x.CategoryId
	}
	return ""
}

type SearchSuiteInApiPlanItem struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	ProjectId      string                 `protobuf:"bytes,1,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`                                          // 项目ID
	SuiteType      string                 `protobuf:"bytes,2,opt,name=suite_type,json=suiteType,proto3" json:"suite_type,omitempty"`                                          // 集合类型
	SuiteId        string                 `protobuf:"bytes,3,opt,name=suite_id,json=suiteId,proto3" json:"suite_id,omitempty"`                                                // 集合ID
	Name           string                 `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`                                                                     // 集合名称
	Description    string                 `protobuf:"bytes,5,opt,name=description,proto3" json:"description,omitempty"`                                                       // 集合描述
	Priority       int64                  `protobuf:"varint,6,opt,name=priority,proto3" json:"priority,omitempty"`                                                            // 优先级
	Tags           []string               `protobuf:"bytes,7,rep,name=tags,proto3" json:"tags,omitempty"`                                                                     // 标签
	State          CommonState            `protobuf:"varint,8,opt,name=state,proto3,enum=manager.CommonState" json:"state,omitempty"`                                         // 状态
	ReferenceState CommonState            `protobuf:"varint,9,opt,name=reference_state,json=referenceState,proto3,enum=manager.CommonState" json:"reference_state,omitempty"` // 引用状态
	CaseIncluded   int64                  `protobuf:"varint,10,opt,name=case_included,json=caseIncluded,proto3" json:"case_included,omitempty"`                               // 包含的用例数
	CaseSkipped    int64                  `protobuf:"varint,11,opt,name=case_skipped,json=caseSkipped,proto3" json:"case_skipped,omitempty"`                                  // 跳过的用例数
	MaintainedBy   string                 `protobuf:"bytes,12,opt,name=maintained_by,json=maintainedBy,proto3" json:"maintained_by,omitempty"`                                // 维护者
	CreatedBy      string                 `protobuf:"bytes,13,opt,name=created_by,json=createdBy,proto3" json:"created_by,omitempty"`                                         // 创建者
	UpdatedBy      string                 `protobuf:"bytes,14,opt,name=updated_by,json=updatedBy,proto3" json:"updated_by,omitempty"`                                         // 更新者
	CreatedAt      int64                  `protobuf:"varint,15,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`                                        // 创建时间
	UpdatedAt      int64                  `protobuf:"varint,16,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`                                        // 更新时间
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *SearchSuiteInApiPlanItem) Reset() {
	*x = SearchSuiteInApiPlanItem{}
	mi := &file_manager_apiplan_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SearchSuiteInApiPlanItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchSuiteInApiPlanItem) ProtoMessage() {}

func (x *SearchSuiteInApiPlanItem) ProtoReflect() protoreflect.Message {
	mi := &file_manager_apiplan_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchSuiteInApiPlanItem.ProtoReflect.Descriptor instead.
func (*SearchSuiteInApiPlanItem) Descriptor() ([]byte, []int) {
	return file_manager_apiplan_proto_rawDescGZIP(), []int{3}
}

func (x *SearchSuiteInApiPlanItem) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *SearchSuiteInApiPlanItem) GetSuiteType() string {
	if x != nil {
		return x.SuiteType
	}
	return ""
}

func (x *SearchSuiteInApiPlanItem) GetSuiteId() string {
	if x != nil {
		return x.SuiteId
	}
	return ""
}

func (x *SearchSuiteInApiPlanItem) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *SearchSuiteInApiPlanItem) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *SearchSuiteInApiPlanItem) GetPriority() int64 {
	if x != nil {
		return x.Priority
	}
	return 0
}

func (x *SearchSuiteInApiPlanItem) GetTags() []string {
	if x != nil {
		return x.Tags
	}
	return nil
}

func (x *SearchSuiteInApiPlanItem) GetState() CommonState {
	if x != nil {
		return x.State
	}
	return CommonState_CS_NULL
}

func (x *SearchSuiteInApiPlanItem) GetReferenceState() CommonState {
	if x != nil {
		return x.ReferenceState
	}
	return CommonState_CS_NULL
}

func (x *SearchSuiteInApiPlanItem) GetCaseIncluded() int64 {
	if x != nil {
		return x.CaseIncluded
	}
	return 0
}

func (x *SearchSuiteInApiPlanItem) GetCaseSkipped() int64 {
	if x != nil {
		return x.CaseSkipped
	}
	return 0
}

func (x *SearchSuiteInApiPlanItem) GetMaintainedBy() string {
	if x != nil {
		return x.MaintainedBy
	}
	return ""
}

func (x *SearchSuiteInApiPlanItem) GetCreatedBy() string {
	if x != nil {
		return x.CreatedBy
	}
	return ""
}

func (x *SearchSuiteInApiPlanItem) GetUpdatedBy() string {
	if x != nil {
		return x.UpdatedBy
	}
	return ""
}

func (x *SearchSuiteInApiPlanItem) GetCreatedAt() int64 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

func (x *SearchSuiteInApiPlanItem) GetUpdatedAt() int64 {
	if x != nil {
		return x.UpdatedAt
	}
	return 0
}

type SearchSuiteNotInApiPlanItem struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ProjectId     string                 `protobuf:"bytes,1,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`          // 项目ID
	CategoryId    string                 `protobuf:"bytes,2,opt,name=category_id,json=categoryId,proto3" json:"category_id,omitempty"`       // 分类ID
	SuiteId       string                 `protobuf:"bytes,3,opt,name=suite_id,json=suiteId,proto3" json:"suite_id,omitempty"`                // 集合ID
	Name          string                 `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`                                     // 集合名称
	Description   string                 `protobuf:"bytes,5,opt,name=description,proto3" json:"description,omitempty"`                       // 集合描述
	Priority      int64                  `protobuf:"varint,6,opt,name=priority,proto3" json:"priority,omitempty"`                            // 优先级
	Tags          []string               `protobuf:"bytes,7,rep,name=tags,proto3" json:"tags,omitempty"`                                     // 标签
	State         CommonState            `protobuf:"varint,8,opt,name=state,proto3,enum=manager.CommonState" json:"state,omitempty"`         // 状态
	MaintainedBy  string                 `protobuf:"bytes,9,opt,name=maintained_by,json=maintainedBy,proto3" json:"maintained_by,omitempty"` // 维护者
	CreatedBy     string                 `protobuf:"bytes,10,opt,name=created_by,json=createdBy,proto3" json:"created_by,omitempty"`         // 创建者
	UpdatedBy     string                 `protobuf:"bytes,11,opt,name=updated_by,json=updatedBy,proto3" json:"updated_by,omitempty"`         // 更新者
	CreatedAt     int64                  `protobuf:"varint,12,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`        // 创建时间
	UpdatedAt     int64                  `protobuf:"varint,13,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`        // 更新时间
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SearchSuiteNotInApiPlanItem) Reset() {
	*x = SearchSuiteNotInApiPlanItem{}
	mi := &file_manager_apiplan_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SearchSuiteNotInApiPlanItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchSuiteNotInApiPlanItem) ProtoMessage() {}

func (x *SearchSuiteNotInApiPlanItem) ProtoReflect() protoreflect.Message {
	mi := &file_manager_apiplan_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchSuiteNotInApiPlanItem.ProtoReflect.Descriptor instead.
func (*SearchSuiteNotInApiPlanItem) Descriptor() ([]byte, []int) {
	return file_manager_apiplan_proto_rawDescGZIP(), []int{4}
}

func (x *SearchSuiteNotInApiPlanItem) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *SearchSuiteNotInApiPlanItem) GetCategoryId() string {
	if x != nil {
		return x.CategoryId
	}
	return ""
}

func (x *SearchSuiteNotInApiPlanItem) GetSuiteId() string {
	if x != nil {
		return x.SuiteId
	}
	return ""
}

func (x *SearchSuiteNotInApiPlanItem) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *SearchSuiteNotInApiPlanItem) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *SearchSuiteNotInApiPlanItem) GetPriority() int64 {
	if x != nil {
		return x.Priority
	}
	return 0
}

func (x *SearchSuiteNotInApiPlanItem) GetTags() []string {
	if x != nil {
		return x.Tags
	}
	return nil
}

func (x *SearchSuiteNotInApiPlanItem) GetState() CommonState {
	if x != nil {
		return x.State
	}
	return CommonState_CS_NULL
}

func (x *SearchSuiteNotInApiPlanItem) GetMaintainedBy() string {
	if x != nil {
		return x.MaintainedBy
	}
	return ""
}

func (x *SearchSuiteNotInApiPlanItem) GetCreatedBy() string {
	if x != nil {
		return x.CreatedBy
	}
	return ""
}

func (x *SearchSuiteNotInApiPlanItem) GetUpdatedBy() string {
	if x != nil {
		return x.UpdatedBy
	}
	return ""
}

func (x *SearchSuiteNotInApiPlanItem) GetCreatedAt() int64 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

func (x *SearchSuiteNotInApiPlanItem) GetUpdatedAt() int64 {
	if x != nil {
		return x.UpdatedAt
	}
	return 0
}

type SearchCaseInApiPlanItem struct {
	state     protoimpl.MessageState `protogen:"open.v1"`
	ProjectId string                 `protobuf:"bytes,1,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"` // 项目ID
	//  string category_id = 2; // 分类ID
	CaseId         string        `protobuf:"bytes,3,opt,name=case_id,json=caseId,proto3" json:"case_id,omitempty"`                                                   // 用例ID
	Name           string        `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`                                                                     // 用例名称
	Description    string        `protobuf:"bytes,5,opt,name=description,proto3" json:"description,omitempty"`                                                       // 用例描述
	Priority       int64         `protobuf:"varint,6,opt,name=priority,proto3" json:"priority,omitempty"`                                                            // 优先级
	Tags           []string      `protobuf:"bytes,7,rep,name=tags,proto3" json:"tags,omitempty"`                                                                     // 标签
	State          ResourceState `protobuf:"varint,8,opt,name=state,proto3,enum=manager.ResourceState" json:"state,omitempty"`                                       // 状态
	ReferenceState CommonState   `protobuf:"varint,9,opt,name=reference_state,json=referenceState,proto3,enum=manager.CommonState" json:"reference_state,omitempty"` // 引用状态
	MaintainedBy   string        `protobuf:"bytes,10,opt,name=maintained_by,json=maintainedBy,proto3" json:"maintained_by,omitempty"`                                // 维护者
	CreatedBy      string        `protobuf:"bytes,11,opt,name=created_by,json=createdBy,proto3" json:"created_by,omitempty"`                                         // 创建者
	UpdatedBy      string        `protobuf:"bytes,12,opt,name=updated_by,json=updatedBy,proto3" json:"updated_by,omitempty"`                                         // 更新者
	CreatedAt      int64         `protobuf:"varint,13,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`                                        // 创建时间
	UpdatedAt      int64         `protobuf:"varint,14,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`                                        // 更新时间
	CaseType       string        `protobuf:"bytes,15,opt,name=case_type,json=caseType,proto3" json:"case_type,omitempty"`                                            // 用例类型
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *SearchCaseInApiPlanItem) Reset() {
	*x = SearchCaseInApiPlanItem{}
	mi := &file_manager_apiplan_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SearchCaseInApiPlanItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchCaseInApiPlanItem) ProtoMessage() {}

func (x *SearchCaseInApiPlanItem) ProtoReflect() protoreflect.Message {
	mi := &file_manager_apiplan_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchCaseInApiPlanItem.ProtoReflect.Descriptor instead.
func (*SearchCaseInApiPlanItem) Descriptor() ([]byte, []int) {
	return file_manager_apiplan_proto_rawDescGZIP(), []int{5}
}

func (x *SearchCaseInApiPlanItem) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *SearchCaseInApiPlanItem) GetCaseId() string {
	if x != nil {
		return x.CaseId
	}
	return ""
}

func (x *SearchCaseInApiPlanItem) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *SearchCaseInApiPlanItem) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *SearchCaseInApiPlanItem) GetPriority() int64 {
	if x != nil {
		return x.Priority
	}
	return 0
}

func (x *SearchCaseInApiPlanItem) GetTags() []string {
	if x != nil {
		return x.Tags
	}
	return nil
}

func (x *SearchCaseInApiPlanItem) GetState() ResourceState {
	if x != nil {
		return x.State
	}
	return ResourceState_RS_NULL
}

func (x *SearchCaseInApiPlanItem) GetReferenceState() CommonState {
	if x != nil {
		return x.ReferenceState
	}
	return CommonState_CS_NULL
}

func (x *SearchCaseInApiPlanItem) GetMaintainedBy() string {
	if x != nil {
		return x.MaintainedBy
	}
	return ""
}

func (x *SearchCaseInApiPlanItem) GetCreatedBy() string {
	if x != nil {
		return x.CreatedBy
	}
	return ""
}

func (x *SearchCaseInApiPlanItem) GetUpdatedBy() string {
	if x != nil {
		return x.UpdatedBy
	}
	return ""
}

func (x *SearchCaseInApiPlanItem) GetCreatedAt() int64 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

func (x *SearchCaseInApiPlanItem) GetUpdatedAt() int64 {
	if x != nil {
		return x.UpdatedAt
	}
	return 0
}

func (x *SearchCaseInApiPlanItem) GetCaseType() string {
	if x != nil {
		return x.CaseType
	}
	return ""
}

type AdvancedSearchSuiteItem struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ProjectId     string                 `protobuf:"bytes,1,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`
	CategoryId    string                 `protobuf:"bytes,2,opt,name=category_id,json=categoryId,proto3" json:"category_id,omitempty"`
	SuiteId       string                 `protobuf:"bytes,3,opt,name=suite_id,json=suiteId,proto3" json:"suite_id,omitempty"`
	Name          string                 `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`
	Description   string                 `protobuf:"bytes,5,opt,name=description,proto3" json:"description,omitempty"`
	Priority      int64                  `protobuf:"varint,6,opt,name=priority,proto3" json:"priority,omitempty"` // 优先级
	Tags          []string               `protobuf:"bytes,7,rep,name=tags,proto3" json:"tags,omitempty"`          // 标签
	State         int64                  `protobuf:"varint,8,opt,name=state,proto3" json:"state,omitempty"`
	CaseCount     int64                  `protobuf:"varint,9,opt,name=case_count,json=caseCount,proto3" json:"case_count,omitempty"`
	MaintainedBy  string                 `protobuf:"bytes,10,opt,name=maintained_by,json=maintainedBy,proto3" json:"maintained_by,omitempty"`
	CreatedBy     string                 `protobuf:"bytes,11,opt,name=created_by,json=createdBy,proto3" json:"created_by,omitempty"`
	UpdatedBy     string                 `protobuf:"bytes,12,opt,name=updated_by,json=updatedBy,proto3" json:"updated_by,omitempty"`
	CreatedAt     int64                  `protobuf:"varint,13,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt     int64                  `protobuf:"varint,14,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AdvancedSearchSuiteItem) Reset() {
	*x = AdvancedSearchSuiteItem{}
	mi := &file_manager_apiplan_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AdvancedSearchSuiteItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AdvancedSearchSuiteItem) ProtoMessage() {}

func (x *AdvancedSearchSuiteItem) ProtoReflect() protoreflect.Message {
	mi := &file_manager_apiplan_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AdvancedSearchSuiteItem.ProtoReflect.Descriptor instead.
func (*AdvancedSearchSuiteItem) Descriptor() ([]byte, []int) {
	return file_manager_apiplan_proto_rawDescGZIP(), []int{6}
}

func (x *AdvancedSearchSuiteItem) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *AdvancedSearchSuiteItem) GetCategoryId() string {
	if x != nil {
		return x.CategoryId
	}
	return ""
}

func (x *AdvancedSearchSuiteItem) GetSuiteId() string {
	if x != nil {
		return x.SuiteId
	}
	return ""
}

func (x *AdvancedSearchSuiteItem) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *AdvancedSearchSuiteItem) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *AdvancedSearchSuiteItem) GetPriority() int64 {
	if x != nil {
		return x.Priority
	}
	return 0
}

func (x *AdvancedSearchSuiteItem) GetTags() []string {
	if x != nil {
		return x.Tags
	}
	return nil
}

func (x *AdvancedSearchSuiteItem) GetState() int64 {
	if x != nil {
		return x.State
	}
	return 0
}

func (x *AdvancedSearchSuiteItem) GetCaseCount() int64 {
	if x != nil {
		return x.CaseCount
	}
	return 0
}

func (x *AdvancedSearchSuiteItem) GetMaintainedBy() string {
	if x != nil {
		return x.MaintainedBy
	}
	return ""
}

func (x *AdvancedSearchSuiteItem) GetCreatedBy() string {
	if x != nil {
		return x.CreatedBy
	}
	return ""
}

func (x *AdvancedSearchSuiteItem) GetUpdatedBy() string {
	if x != nil {
		return x.UpdatedBy
	}
	return ""
}

func (x *AdvancedSearchSuiteItem) GetCreatedAt() int64 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

func (x *AdvancedSearchSuiteItem) GetUpdatedAt() int64 {
	if x != nil {
		return x.UpdatedAt
	}
	return 0
}

var File_manager_apiplan_proto protoreflect.FileDescriptor

var file_manager_apiplan_proto_rawDesc = []byte{
	0x0a, 0x15, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2f, 0x61, 0x70, 0x69, 0x70, 0x6c, 0x61,
	0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x07, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72,
	0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64,
	0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x11, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x12, 0x6d, 0x61,
	0x6e, 0x61, 0x67, 0x65, 0x72, 0x2f, 0x62, 0x61, 0x73, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x22, 0x82, 0x06, 0x0a, 0x07, 0x41, 0x70, 0x69, 0x50, 0x6c, 0x61, 0x6e, 0x12, 0x1d, 0x0a, 0x0a,
	0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x70,
	0x6c, 0x61, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x70, 0x6c,
	0x61, 0x6e, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64,
	0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x72,
	0x69, 0x6f, 0x72, 0x69, 0x74, 0x79, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x70, 0x72,
	0x69, 0x6f, 0x72, 0x69, 0x74, 0x79, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x61, 0x67, 0x73, 0x18, 0x06,
	0x20, 0x03, 0x28, 0x09, 0x52, 0x04, 0x74, 0x61, 0x67, 0x73, 0x12, 0x2a, 0x0a, 0x05, 0x73, 0x74,
	0x61, 0x74, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x6d, 0x61, 0x6e, 0x61,
	0x67, 0x65, 0x72, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52,
	0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x12, 0x27, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x13, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x72,
	0x69, 0x67, 0x67, 0x65, 0x72, 0x4d, 0x6f, 0x64, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12,
	0x2d, 0x0a, 0x07, 0x70, 0x75, 0x72, 0x70, 0x6f, 0x73, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x13, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x75, 0x72, 0x70, 0x6f, 0x73,
	0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x07, 0x70, 0x75, 0x72, 0x70, 0x6f, 0x73, 0x65, 0x12, 0x27,
	0x0a, 0x0f, 0x63, 0x72, 0x6f, 0x6e, 0x5f, 0x65, 0x78, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f,
	0x6e, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x63, 0x72, 0x6f, 0x6e, 0x45, 0x78, 0x70,
	0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x2a, 0x0a, 0x11, 0x67, 0x65, 0x6e, 0x65, 0x72,
	0x61, 0x6c, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x69, 0x64, 0x18, 0x0b, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0f, 0x67, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x6c, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x49, 0x64, 0x12, 0x2c, 0x0a, 0x12, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x63,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x0c, 0x20, 0x03, 0x28, 0x09, 0x52,
	0x10, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x49, 0x64,
	0x73, 0x12, 0x48, 0x0a, 0x14, 0x73, 0x75, 0x69, 0x74, 0x65, 0x5f, 0x65, 0x78, 0x65, 0x63, 0x75,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x16, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74,
	0x69, 0x6f, 0x6e, 0x4d, 0x6f, 0x64, 0x65, 0x52, 0x12, 0x73, 0x75, 0x69, 0x74, 0x65, 0x45, 0x78,
	0x65, 0x63, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x6f, 0x64, 0x65, 0x12, 0x46, 0x0a, 0x13, 0x63,
	0x61, 0x73, 0x65, 0x5f, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x6f,
	0x64, 0x65, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x16, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67,
	0x65, 0x72, 0x2e, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x6f, 0x64, 0x65,
	0x52, 0x11, 0x63, 0x61, 0x73, 0x65, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x4d,
	0x6f, 0x64, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x6d, 0x61, 0x69, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65,
	0x64, 0x5f, 0x62, 0x79, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x6d, 0x61, 0x69, 0x6e,
	0x74, 0x61, 0x69, 0x6e, 0x65, 0x64, 0x42, 0x79, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x64, 0x5f, 0x62, 0x79, 0x18, 0x10, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x64, 0x42, 0x79, 0x12, 0x1d, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x64, 0x5f, 0x62, 0x79, 0x18, 0x11, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x75, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x64, 0x42, 0x79, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x64, 0x5f, 0x61, 0x74, 0x18, 0x12, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64,
	0x5f, 0x61, 0x74, 0x18, 0x13, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x64, 0x41, 0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79,
	0x5f, 0x69, 0x64, 0x18, 0x14, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x61, 0x74, 0x65, 0x67,
	0x6f, 0x72, 0x79, 0x49, 0x64, 0x22, 0xa2, 0x01, 0x0a, 0x0b, 0x53, 0x75, 0x69, 0x74, 0x65, 0x54,
	0x79, 0x70, 0x65, 0x49, 0x64, 0x12, 0x43, 0x0a, 0x0a, 0x73, 0x75, 0x69, 0x74, 0x65, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x24, 0xfa, 0x42, 0x21, 0x72, 0x1f,
	0x52, 0x09, 0x41, 0x50, 0x49, 0x5f, 0x53, 0x55, 0x49, 0x54, 0x45, 0x52, 0x12, 0x49, 0x4e, 0x54,
	0x45, 0x52, 0x46, 0x41, 0x43, 0x45, 0x5f, 0x44, 0x4f, 0x43, 0x55, 0x4d, 0x45, 0x4e, 0x54, 0x52,
	0x09, 0x73, 0x75, 0x69, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x4e, 0x0a, 0x08, 0x73, 0x75,
	0x69, 0x74, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x33, 0xfa, 0x42,
	0x30, 0x72, 0x2e, 0x32, 0x2c, 0x28, 0x3f, 0x3a, 0x5e, 0x73, 0x75, 0x69, 0x74, 0x65, 0x5f, 0x69,
	0x64, 0x3a, 0x2e, 0x2b, 0x3f, 0x7c, 0x5e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x66, 0x61, 0x63, 0x65,
	0x5f, 0x64, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x3a, 0x2e, 0x2b, 0x3f,
	0x29, 0x52, 0x07, 0x73, 0x75, 0x69, 0x74, 0x65, 0x49, 0x64, 0x22, 0xd9, 0x05, 0x0a, 0x11, 0x53,
	0x65, 0x61, 0x72, 0x63, 0x68, 0x41, 0x70, 0x69, 0x50, 0x6c, 0x61, 0x6e, 0x49, 0x74, 0x65, 0x6d,
	0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x12,
	0x17, 0x0a, 0x07, 0x70, 0x6c, 0x61, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x70, 0x6c, 0x61, 0x6e, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b,
	0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1a,
	0x0a, 0x08, 0x70, 0x72, 0x69, 0x6f, 0x72, 0x69, 0x74, 0x79, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x08, 0x70, 0x72, 0x69, 0x6f, 0x72, 0x69, 0x74, 0x79, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x61,
	0x67, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x09, 0x52, 0x04, 0x74, 0x61, 0x67, 0x73, 0x12, 0x2a,
	0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e,
	0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x53, 0x74,
	0x61, 0x74, 0x65, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x12, 0x27, 0x0a, 0x04, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x13, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2e, 0x54, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x4d, 0x6f, 0x64, 0x65, 0x52, 0x04, 0x74,
	0x79, 0x70, 0x65, 0x12, 0x2d, 0x0a, 0x07, 0x70, 0x75, 0x72, 0x70, 0x6f, 0x73, 0x65, 0x18, 0x09,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x13, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x75,
	0x72, 0x70, 0x6f, 0x73, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x07, 0x70, 0x75, 0x72, 0x70, 0x6f,
	0x73, 0x65, 0x12, 0x27, 0x0a, 0x0f, 0x63, 0x72, 0x6f, 0x6e, 0x5f, 0x65, 0x78, 0x70, 0x72, 0x65,
	0x73, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x63, 0x72, 0x6f,
	0x6e, 0x45, 0x78, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x48, 0x0a, 0x14, 0x73,
	0x75, 0x69, 0x74, 0x65, 0x5f, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6d,
	0x6f, 0x64, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x16, 0x2e, 0x6d, 0x61, 0x6e, 0x61,
	0x67, 0x65, 0x72, 0x2e, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x6f, 0x64,
	0x65, 0x52, 0x12, 0x73, 0x75, 0x69, 0x74, 0x65, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x69, 0x6f,
	0x6e, 0x4d, 0x6f, 0x64, 0x65, 0x12, 0x46, 0x0a, 0x13, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x65, 0x78,
	0x65, 0x63, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x18, 0x0c, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x16, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x45, 0x78, 0x65,
	0x63, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x6f, 0x64, 0x65, 0x52, 0x11, 0x63, 0x61, 0x73, 0x65,
	0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x6f, 0x64, 0x65, 0x12, 0x25, 0x0a,
	0x0e, 0x73, 0x75, 0x69, 0x74, 0x65, 0x5f, 0x69, 0x6e, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x64, 0x18,
	0x0d, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0d, 0x73, 0x75, 0x69, 0x74, 0x65, 0x49, 0x6e, 0x63, 0x6c,
	0x75, 0x64, 0x65, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x6d, 0x61, 0x69, 0x6e, 0x74, 0x61, 0x69, 0x6e,
	0x65, 0x64, 0x5f, 0x62, 0x79, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x6d, 0x61, 0x69,
	0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x64, 0x42, 0x79, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x64, 0x5f, 0x62, 0x79, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x42, 0x79, 0x12, 0x1d, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x64, 0x5f, 0x62, 0x79, 0x18, 0x10, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x75, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x64, 0x42, 0x79, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x11, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x63, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x64, 0x5f, 0x61, 0x74, 0x18, 0x12, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72,
	0x79, 0x5f, 0x69, 0x64, 0x18, 0x13, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x61, 0x74, 0x65,
	0x67, 0x6f, 0x72, 0x79, 0x49, 0x64, 0x22, 0xad, 0x04, 0x0a, 0x18, 0x53, 0x65, 0x61, 0x72, 0x63,
	0x68, 0x53, 0x75, 0x69, 0x74, 0x65, 0x49, 0x6e, 0x41, 0x70, 0x69, 0x50, 0x6c, 0x61, 0x6e, 0x49,
	0x74, 0x65, 0x6d, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74,
	0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x75, 0x69, 0x74, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x75, 0x69, 0x74, 0x65, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x19, 0x0a, 0x08, 0x73, 0x75, 0x69, 0x74, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x73, 0x75, 0x69, 0x74, 0x65, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x72, 0x69, 0x6f, 0x72, 0x69, 0x74, 0x79, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x70, 0x72, 0x69, 0x6f, 0x72, 0x69, 0x74, 0x79, 0x12, 0x12,
	0x0a, 0x04, 0x74, 0x61, 0x67, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x09, 0x52, 0x04, 0x74, 0x61,
	0x67, 0x73, 0x12, 0x2a, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x14, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x43, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x12, 0x3d,
	0x0a, 0x0f, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x5f, 0x73, 0x74, 0x61, 0x74,
	0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65,
	0x72, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x0e, 0x72,
	0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x23, 0x0a,
	0x0d, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x69, 0x6e, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x64, 0x18, 0x0a,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x63, 0x61, 0x73, 0x65, 0x49, 0x6e, 0x63, 0x6c, 0x75, 0x64,
	0x65, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x73, 0x6b, 0x69, 0x70, 0x70,
	0x65, 0x64, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x63, 0x61, 0x73, 0x65, 0x53, 0x6b,
	0x69, 0x70, 0x70, 0x65, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x6d, 0x61, 0x69, 0x6e, 0x74, 0x61, 0x69,
	0x6e, 0x65, 0x64, 0x5f, 0x62, 0x79, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x6d, 0x61,
	0x69, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x64, 0x42, 0x79, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x62, 0x79, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x42, 0x79, 0x12, 0x1d, 0x0a, 0x0a, 0x75, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x64, 0x5f, 0x62, 0x79, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x75,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x42, 0x79, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x63, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x10, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x75, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x22, 0xab, 0x03, 0x0a, 0x1b, 0x53, 0x65, 0x61, 0x72, 0x63,
	0x68, 0x53, 0x75, 0x69, 0x74, 0x65, 0x4e, 0x6f, 0x74, 0x49, 0x6e, 0x41, 0x70, 0x69, 0x50, 0x6c,
	0x61, 0x6e, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63,
	0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a,
	0x65, 0x63, 0x74, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72,
	0x79, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x61, 0x74, 0x65,
	0x67, 0x6f, 0x72, 0x79, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x73, 0x75, 0x69, 0x74, 0x65, 0x5f,
	0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x73, 0x75, 0x69, 0x74, 0x65, 0x49,
	0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x72, 0x69, 0x6f, 0x72,
	0x69, 0x74, 0x79, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x70, 0x72, 0x69, 0x6f, 0x72,
	0x69, 0x74, 0x79, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x61, 0x67, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28,
	0x09, 0x52, 0x04, 0x74, 0x61, 0x67, 0x73, 0x12, 0x2a, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65,
	0x18, 0x08, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72,
	0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x05, 0x73, 0x74,
	0x61, 0x74, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x6d, 0x61, 0x69, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65,
	0x64, 0x5f, 0x62, 0x79, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x6d, 0x61, 0x69, 0x6e,
	0x74, 0x61, 0x69, 0x6e, 0x65, 0x64, 0x42, 0x79, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x64, 0x5f, 0x62, 0x79, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x64, 0x42, 0x79, 0x12, 0x1d, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x64, 0x5f, 0x62, 0x79, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x75, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x64, 0x42, 0x79, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x64, 0x5f, 0x61, 0x74, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64,
	0x5f, 0x61, 0x74, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x64, 0x41, 0x74, 0x22, 0x83, 0x04, 0x0a, 0x17, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x43,
	0x61, 0x73, 0x65, 0x49, 0x6e, 0x41, 0x70, 0x69, 0x50, 0x6c, 0x61, 0x6e, 0x49, 0x74, 0x65, 0x6d,
	0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x12,
	0x17, 0x0a, 0x07, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x63, 0x61, 0x73, 0x65, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b,
	0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1a,
	0x0a, 0x08, 0x70, 0x72, 0x69, 0x6f, 0x72, 0x69, 0x74, 0x79, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x08, 0x70, 0x72, 0x69, 0x6f, 0x72, 0x69, 0x74, 0x79, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x61,
	0x67, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x09, 0x52, 0x04, 0x74, 0x61, 0x67, 0x73, 0x12, 0x2c,
	0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x16, 0x2e,
	0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x12, 0x3d, 0x0a, 0x0f,
	0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18,
	0x09, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e,
	0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x0e, 0x72, 0x65, 0x66,
	0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x6d,
	0x61, 0x69, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x64, 0x5f, 0x62, 0x79, 0x18, 0x0a, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0c, 0x6d, 0x61, 0x69, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x64, 0x42, 0x79,
	0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x62, 0x79, 0x18, 0x0b,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x42, 0x79, 0x12,
	0x1d, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x62, 0x79, 0x18, 0x0c, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x42, 0x79, 0x12, 0x1d,
	0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0d, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x1d, 0x0a,
	0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0e, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x3c, 0x0a, 0x09,
	0x63, 0x61, 0x73, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x1f, 0xfa, 0x42, 0x1c, 0x72, 0x1a, 0x52, 0x08, 0x41, 0x50, 0x49, 0x5f, 0x43, 0x41, 0x53, 0x45,
	0x52, 0x0e, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x46, 0x41, 0x43, 0x45, 0x5f, 0x43, 0x41, 0x53, 0x45,
	0x52, 0x08, 0x63, 0x61, 0x73, 0x65, 0x54, 0x79, 0x70, 0x65, 0x22, 0xb0, 0x03, 0x0a, 0x17, 0x41,
	0x64, 0x76, 0x61, 0x6e, 0x63, 0x65, 0x64, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x53, 0x75, 0x69,
	0x74, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63,
	0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a,
	0x65, 0x63, 0x74, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72,
	0x79, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x61, 0x74, 0x65,
	0x67, 0x6f, 0x72, 0x79, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x73, 0x75, 0x69, 0x74, 0x65, 0x5f,
	0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x73, 0x75, 0x69, 0x74, 0x65, 0x49,
	0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x72, 0x69, 0x6f, 0x72,
	0x69, 0x74, 0x79, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x70, 0x72, 0x69, 0x6f, 0x72,
	0x69, 0x74, 0x79, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x61, 0x67, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28,
	0x09, 0x52, 0x04, 0x74, 0x61, 0x67, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65,
	0x18, 0x08, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x12, 0x1d, 0x0a,
	0x0a, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x09, 0x63, 0x61, 0x73, 0x65, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x23, 0x0a, 0x0d,
	0x6d, 0x61, 0x69, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x64, 0x5f, 0x62, 0x79, 0x18, 0x0a, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0c, 0x6d, 0x61, 0x69, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x64, 0x42,
	0x79, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x62, 0x79, 0x18,
	0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x42, 0x79,
	0x12, 0x1d, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x62, 0x79, 0x18, 0x0c,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x42, 0x79, 0x12,
	0x1d, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0d, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x1d,
	0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0e, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x42, 0x41, 0x5a,
	0x3f, 0x67, 0x69, 0x74, 0x6c, 0x61, 0x62, 0x2e, 0x74, 0x74, 0x79, 0x75, 0x79, 0x69, 0x6e, 0x2e,
	0x63, 0x6f, 0x6d, 0x2f, 0x54, 0x65, 0x73, 0x74, 0x44, 0x65, 0x76, 0x65, 0x6c, 0x6f, 0x70, 0x6d,
	0x65, 0x6e, 0x74, 0x2f, 0x70, 0x72, 0x6f, 0x62, 0x65, 0x2d, 0x62, 0x61, 0x63, 0x6b, 0x65, 0x6e,
	0x64, 0x2f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2f, 0x72, 0x70, 0x63, 0x2f, 0x70, 0x62,
	0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_manager_apiplan_proto_rawDescOnce sync.Once
	file_manager_apiplan_proto_rawDescData = file_manager_apiplan_proto_rawDesc
)

func file_manager_apiplan_proto_rawDescGZIP() []byte {
	file_manager_apiplan_proto_rawDescOnce.Do(func() {
		file_manager_apiplan_proto_rawDescData = protoimpl.X.CompressGZIP(file_manager_apiplan_proto_rawDescData)
	})
	return file_manager_apiplan_proto_rawDescData
}

var file_manager_apiplan_proto_msgTypes = make([]protoimpl.MessageInfo, 7)
var file_manager_apiplan_proto_goTypes = []any{
	(*ApiPlan)(nil),                     // 0: manager.ApiPlan
	(*SuiteTypeId)(nil),                 // 1: manager.SuiteTypeId
	(*SearchApiPlanItem)(nil),           // 2: manager.SearchApiPlanItem
	(*SearchSuiteInApiPlanItem)(nil),    // 3: manager.SearchSuiteInApiPlanItem
	(*SearchSuiteNotInApiPlanItem)(nil), // 4: manager.SearchSuiteNotInApiPlanItem
	(*SearchCaseInApiPlanItem)(nil),     // 5: manager.SearchCaseInApiPlanItem
	(*AdvancedSearchSuiteItem)(nil),     // 6: manager.AdvancedSearchSuiteItem
	(CommonState)(0),                    // 7: manager.CommonState
	(pb.TriggerMode)(0),                 // 8: common.TriggerMode
	(pb.PurposeType)(0),                 // 9: common.PurposeType
	(ExecutionMode)(0),                  // 10: manager.ExecutionMode
	(ResourceState)(0),                  // 11: manager.ResourceState
}
var file_manager_apiplan_proto_depIdxs = []int32{
	7,  // 0: manager.ApiPlan.state:type_name -> manager.CommonState
	8,  // 1: manager.ApiPlan.type:type_name -> common.TriggerMode
	9,  // 2: manager.ApiPlan.purpose:type_name -> common.PurposeType
	10, // 3: manager.ApiPlan.suite_execution_mode:type_name -> manager.ExecutionMode
	10, // 4: manager.ApiPlan.case_execution_mode:type_name -> manager.ExecutionMode
	7,  // 5: manager.SearchApiPlanItem.state:type_name -> manager.CommonState
	8,  // 6: manager.SearchApiPlanItem.type:type_name -> common.TriggerMode
	9,  // 7: manager.SearchApiPlanItem.purpose:type_name -> common.PurposeType
	10, // 8: manager.SearchApiPlanItem.suite_execution_mode:type_name -> manager.ExecutionMode
	10, // 9: manager.SearchApiPlanItem.case_execution_mode:type_name -> manager.ExecutionMode
	7,  // 10: manager.SearchSuiteInApiPlanItem.state:type_name -> manager.CommonState
	7,  // 11: manager.SearchSuiteInApiPlanItem.reference_state:type_name -> manager.CommonState
	7,  // 12: manager.SearchSuiteNotInApiPlanItem.state:type_name -> manager.CommonState
	11, // 13: manager.SearchCaseInApiPlanItem.state:type_name -> manager.ResourceState
	7,  // 14: manager.SearchCaseInApiPlanItem.reference_state:type_name -> manager.CommonState
	15, // [15:15] is the sub-list for method output_type
	15, // [15:15] is the sub-list for method input_type
	15, // [15:15] is the sub-list for extension type_name
	15, // [15:15] is the sub-list for extension extendee
	0,  // [0:15] is the sub-list for field type_name
}

func init() { file_manager_apiplan_proto_init() }
func file_manager_apiplan_proto_init() {
	if File_manager_apiplan_proto != nil {
		return
	}
	file_manager_base_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_manager_apiplan_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   7,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_manager_apiplan_proto_goTypes,
		DependencyIndexes: file_manager_apiplan_proto_depIdxs,
		MessageInfos:      file_manager_apiplan_proto_msgTypes,
	}.Build()
	File_manager_apiplan_proto = out.File
	file_manager_apiplan_proto_rawDesc = nil
	file_manager_apiplan_proto_goTypes = nil
	file_manager_apiplan_proto_depIdxs = nil
}
