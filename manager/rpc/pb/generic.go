package pb

import (
	"google.golang.org/protobuf/reflect/protoreflect"
	"google.golang.org/protobuf/types/known/structpb"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
)

type ProtoMessage interface {
	ProtoReflect() protoreflect.Message
}

type Element interface {
	ProtoMessage

	ElementId() string
	ElementType() string
}

func (x *Node) ElementId() string {
	return x.GetId()
}

func (x *Node) ElementType() string {
	return constants.NODE
}

func (x *Edge) ElementId() string {
	return x.GetId()
}

func (x *Edge) ElementType() string {
	return constants.EDGE
}

func (x *Combo) ElementId() string {
	return x.GetId()
}

func (x *Combo) ElementType() string {
	return constants.COMBO
}

type Component interface {
	ProtoMessage

	ComponentId() string
	ComponentType() string
	ComponentName() string
	ComponentData() *structpb.Struct
	ComponentDataMapValue() map[string]any
}

func (x *Node) ComponentId() string {
	return x.GetId()
}

func (x *Node) ComponentType() string {
	return x.GetType()
}

func (x *Node) ComponentName() string {
	return x.GetLabel()
}

func (x *Node) ComponentData() *structpb.Struct {
	return x.GetData()
}

func (x *Node) ComponentDataMapValue() map[string]any {
	return x.GetData().AsMap()
}

func (x *Combo) ComponentId() string {
	return x.GetId()
}

func (x *Combo) ComponentType() string {
	return x.GetType()
}

func (x *Combo) ComponentName() string {
	return x.GetLabel()
}

func (x *Combo) ComponentData() *structpb.Struct {
	return x.GetData()
}

func (x *Combo) ComponentDataMapValue() map[string]any {
	return x.GetData().AsMap()
}

// Argument contains both input and output parameters
type Argument interface {
	*Import | *Export

	ArgName() string
	ArgDesc() string
}

func (x *Import) ArgName() string {
	return x.GetName()
}

func (x *Import) ArgDesc() string {
	return x.GetDescription()
}

func (x *Export) ArgName() string {
	return x.GetName()
}

func (x *Export) ArgDesc() string {
	return x.GetDescription()
}

type SortType interface {
	LessKey() string
}

func (x *Node) LessKey() string {
	return x.GetId()
}

func (x *Edge) LessKey() string {
	return x.GetId()
}

func (x *Combo) LessKey() string {
	return x.GetId()
}
