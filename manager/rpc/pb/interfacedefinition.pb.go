// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.1
// 	protoc        v5.29.2
// source: manager/interfacedefinition.proto

package pb

import (
	reflect "reflect"
	sync "sync"

	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	structpb "google.golang.org/protobuf/types/known/structpb"

	pb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// WithConfig 使用接口配置的方式
type WithConfig int32

const (
	WithConfig_DO_NOT_USE WithConfig = 0 // 不使用
	WithConfig_AUTOMATIC  WithConfig = 1 // 自动选择
	WithConfig_SPECIFIED  WithConfig = 2 // 使用指定的
)

// Enum value maps for WithConfig.
var (
	WithConfig_name = map[int32]string{
		0: "DO_NOT_USE",
		1: "AUTOMATIC",
		2: "SPECIFIED",
	}
	WithConfig_value = map[string]int32{
		"DO_NOT_USE": 0,
		"AUTOMATIC":  1,
		"SPECIFIED":  2,
	}
)

func (x WithConfig) Enum() *WithConfig {
	p := new(WithConfig)
	*p = x
	return p
}

func (x WithConfig) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (WithConfig) Descriptor() protoreflect.EnumDescriptor {
	return file_manager_interfacedefinition_proto_enumTypes[0].Descriptor()
}

func (WithConfig) Type() protoreflect.EnumType {
	return &file_manager_interfacedefinition_proto_enumTypes[0]
}

func (x WithConfig) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use WithConfig.Descriptor instead.
func (WithConfig) EnumDescriptor() ([]byte, []int) {
	return file_manager_interfacedefinition_proto_rawDescGZIP(), []int{0}
}

type EnumValDesc struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Value         *structpb.Value        `protobuf:"bytes,1,opt,name=value,proto3" json:"value,omitempty"`
	Description   string                 `protobuf:"bytes,2,opt,name=description,proto3" json:"description,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *EnumValDesc) Reset() {
	*x = EnumValDesc{}
	mi := &file_manager_interfacedefinition_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *EnumValDesc) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EnumValDesc) ProtoMessage() {}

func (x *EnumValDesc) ProtoReflect() protoreflect.Message {
	mi := &file_manager_interfacedefinition_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EnumValDesc.ProtoReflect.Descriptor instead.
func (*EnumValDesc) Descriptor() ([]byte, []int) {
	return file_manager_interfacedefinition_proto_rawDescGZIP(), []int{0}
}

func (x *EnumValDesc) GetValue() *structpb.Value {
	if x != nil {
		return x.Value
	}
	return nil
}

func (x *EnumValDesc) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

type RefSchema struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	SchemaId      string                 `protobuf:"bytes,1,opt,name=schemaId,proto3" json:"schemaId,omitempty"`
	FullName      string                 `protobuf:"bytes,2,opt,name=fullName,proto3" json:"fullName,omitempty"`
	DisplayName   string                 `protobuf:"bytes,3,opt,name=displayName,proto3" json:"displayName,omitempty"`
	CategoryPath  []*CategoryNode        `protobuf:"bytes,4,rep,name=categoryPath,proto3" json:"categoryPath,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RefSchema) Reset() {
	*x = RefSchema{}
	mi := &file_manager_interfacedefinition_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RefSchema) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RefSchema) ProtoMessage() {}

func (x *RefSchema) ProtoReflect() protoreflect.Message {
	mi := &file_manager_interfacedefinition_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RefSchema.ProtoReflect.Descriptor instead.
func (*RefSchema) Descriptor() ([]byte, []int) {
	return file_manager_interfacedefinition_proto_rawDescGZIP(), []int{1}
}

func (x *RefSchema) GetSchemaId() string {
	if x != nil {
		return x.SchemaId
	}
	return ""
}

func (x *RefSchema) GetFullName() string {
	if x != nil {
		return x.FullName
	}
	return ""
}

func (x *RefSchema) GetDisplayName() string {
	if x != nil {
		return x.DisplayName
	}
	return ""
}

func (x *RefSchema) GetCategoryPath() []*CategoryNode {
	if x != nil {
		return x.CategoryPath
	}
	return nil
}

type TextDescExample struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Description   string                 `protobuf:"bytes,1,opt,name=description,proto3" json:"description,omitempty"`
	Example       string                 `protobuf:"bytes,2,opt,name=example,proto3" json:"example,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TextDescExample) Reset() {
	*x = TextDescExample{}
	mi := &file_manager_interfacedefinition_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TextDescExample) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TextDescExample) ProtoMessage() {}

func (x *TextDescExample) ProtoReflect() protoreflect.Message {
	mi := &file_manager_interfacedefinition_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TextDescExample.ProtoReflect.Descriptor instead.
func (*TextDescExample) Descriptor() ([]byte, []int) {
	return file_manager_interfacedefinition_proto_rawDescGZIP(), []int{2}
}

func (x *TextDescExample) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *TextDescExample) GetExample() string {
	if x != nil {
		return x.Example
	}
	return ""
}

type BodyData struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Type          string                 `protobuf:"bytes,1,opt,name=type,proto3" json:"type,omitempty"` // 请求体类型
	Form          []*Schema              `protobuf:"bytes,2,rep,name=form,proto3" json:"form,omitempty"` // 请求体表单信息
	Json          *Schema                `protobuf:"bytes,3,opt,name=json,proto3" json:"json,omitempty"` // 请求体JSON信息
	Text          *TextDescExample       `protobuf:"bytes,4,opt,name=text,proto3" json:"text,omitempty"` // 请求体文本信息
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BodyData) Reset() {
	*x = BodyData{}
	mi := &file_manager_interfacedefinition_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BodyData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BodyData) ProtoMessage() {}

func (x *BodyData) ProtoReflect() protoreflect.Message {
	mi := &file_manager_interfacedefinition_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BodyData.ProtoReflect.Descriptor instead.
func (*BodyData) Descriptor() ([]byte, []int) {
	return file_manager_interfacedefinition_proto_rawDescGZIP(), []int{3}
}

func (x *BodyData) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *BodyData) GetForm() []*Schema {
	if x != nil {
		return x.Form
	}
	return nil
}

func (x *BodyData) GetJson() *Schema {
	if x != nil {
		return x.Json
	}
	return nil
}

func (x *BodyData) GetText() *TextDescExample {
	if x != nil {
		return x.Text
	}
	return nil
}

type ResponseData struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	StatusCode    string                 `protobuf:"bytes,1,opt,name=status_code,json=statusCode,proto3" json:"status_code,omitempty"` // 状态码
	Description   string                 `protobuf:"bytes,2,opt,name=description,proto3" json:"description,omitempty"`                 // 响应描述
	Headers       []*Schema              `protobuf:"bytes,3,rep,name=headers,proto3" json:"headers,omitempty"`                         // 响应头信息
	Body          *BodyData              `protobuf:"bytes,4,opt,name=body,proto3" json:"body,omitempty"`                               // 响应体信息
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ResponseData) Reset() {
	*x = ResponseData{}
	mi := &file_manager_interfacedefinition_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ResponseData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResponseData) ProtoMessage() {}

func (x *ResponseData) ProtoReflect() protoreflect.Message {
	mi := &file_manager_interfacedefinition_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResponseData.ProtoReflect.Descriptor instead.
func (*ResponseData) Descriptor() ([]byte, []int) {
	return file_manager_interfacedefinition_proto_rawDescGZIP(), []int{4}
}

func (x *ResponseData) GetStatusCode() string {
	if x != nil {
		return x.StatusCode
	}
	return ""
}

func (x *ResponseData) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *ResponseData) GetHeaders() []*Schema {
	if x != nil {
		return x.Headers
	}
	return nil
}

func (x *ResponseData) GetBody() *BodyData {
	if x != nil {
		return x.Body
	}
	return nil
}

type Schema struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	Title            string                 `protobuf:"bytes,1,opt,name=title,proto3" json:"title,omitempty"`                  // 字段名称
	Type             string                 `protobuf:"bytes,2,opt,name=type,proto3" json:"type,omitempty"`                    // 字段类型
	Description      string                 `protobuf:"bytes,3,opt,name=description,proto3" json:"description,omitempty"`      // 字段描述
	Index            int32                  `protobuf:"varint,4,opt,name=index,proto3" json:"index,omitempty"`                 // 字段序号 // 注：不能定义为`int64`，否则序列化的时候会被转为`string`，再反序列化的时候就会报错
	FieldRequired    bool                   `protobuf:"varint,5,opt,name=fieldRequired,proto3" json:"fieldRequired,omitempty"` // 字段是否必需
	Deprecated       bool                   `protobuf:"varint,6,opt,name=deprecated,proto3" json:"deprecated,omitempty"`       // 字段是否不建议使用
	Default          *structpb.Value        `protobuf:"bytes,7,opt,name=default,proto3" json:"default,omitempty"`              // 字段默认值
	Example          *structpb.Value        `protobuf:"bytes,8,opt,name=example,proto3" json:"example,omitempty"`              // 字段样例
	Format           string                 `protobuf:"bytes,9,opt,name=format,proto3" json:"format,omitempty"`                // 字段值格式
	MultipleOf       int32                  `protobuf:"varint,10,opt,name=multipleOf,proto3" json:"multipleOf,omitempty"`      // 注：不能定义为`int64`，否则序列化的时候会被转为`string`，再反序列化的时候就会报错
	Maximum          float32                `protobuf:"fixed32,11,opt,name=maximum,proto3" json:"maximum,omitempty"`
	ExclusiveMaximum float32                `protobuf:"fixed32,12,opt,name=exclusiveMaximum,proto3" json:"exclusiveMaximum,omitempty"`
	Minimum          float32                `protobuf:"fixed32,13,opt,name=minimum,proto3" json:"minimum,omitempty"`
	ExclusiveMinimum float32                `protobuf:"fixed32,14,opt,name=exclusiveMinimum,proto3" json:"exclusiveMinimum,omitempty"`
	MaxLength        int32                  `protobuf:"varint,15,opt,name=maxLength,proto3" json:"maxLength,omitempty"` // 注：不能定义为`int64`，否则序列化的时候会被转为`string`，再反序列化的时候就会报错
	MinLength        int32                  `protobuf:"varint,16,opt,name=minLength,proto3" json:"minLength,omitempty"` // 注：不能定义为`int64`，否则序列化的时候会被转为`string`，再反序列化的时候就会报错
	Pattern          string                 `protobuf:"bytes,17,opt,name=pattern,proto3" json:"pattern,omitempty"`
	MaxItems         int32                  `protobuf:"varint,18,opt,name=maxItems,proto3" json:"maxItems,omitempty"` // 注：不能定义为`int64`，否则序列化的时候会被转为`string`，再反序列化的时候就会报错
	MinItems         int32                  `protobuf:"varint,19,opt,name=minItems,proto3" json:"minItems,omitempty"` // 注：不能定义为`int64`，否则序列化的时候会被转为`string`，再反序列化的时候就会报错
	UniqueItems      bool                   `protobuf:"varint,20,opt,name=uniqueItems,proto3" json:"uniqueItems,omitempty"`
	Enum             []*structpb.Value      `protobuf:"bytes,21,rep,name=enum,proto3" json:"enum,omitempty"`
	Enums            []*EnumValDesc         `protobuf:"bytes,22,rep,name=enums,proto3" json:"enums,omitempty"`
	Items            *Schema                `protobuf:"bytes,23,opt,name=items,proto3" json:"items,omitempty"`
	Ref              *RefSchema             `protobuf:"bytes,24,opt,name=ref,proto3" json:"ref,omitempty"`
	Properties       map[string]*Schema     `protobuf:"bytes,25,rep,name=properties,proto3" json:"properties,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	Required         []string               `protobuf:"bytes,26,rep,name=required,proto3" json:"required,omitempty"`
	SortKey          string                 `protobuf:"bytes,27,opt,name=sortKey,proto3" json:"sortKey,omitempty"` // 前端创建的字段
	Raw              string                 `protobuf:"bytes,28,opt,name=raw,proto3" json:"raw,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *Schema) Reset() {
	*x = Schema{}
	mi := &file_manager_interfacedefinition_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Schema) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Schema) ProtoMessage() {}

func (x *Schema) ProtoReflect() protoreflect.Message {
	mi := &file_manager_interfacedefinition_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Schema.ProtoReflect.Descriptor instead.
func (*Schema) Descriptor() ([]byte, []int) {
	return file_manager_interfacedefinition_proto_rawDescGZIP(), []int{5}
}

func (x *Schema) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *Schema) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *Schema) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *Schema) GetIndex() int32 {
	if x != nil {
		return x.Index
	}
	return 0
}

func (x *Schema) GetFieldRequired() bool {
	if x != nil {
		return x.FieldRequired
	}
	return false
}

func (x *Schema) GetDeprecated() bool {
	if x != nil {
		return x.Deprecated
	}
	return false
}

func (x *Schema) GetDefault() *structpb.Value {
	if x != nil {
		return x.Default
	}
	return nil
}

func (x *Schema) GetExample() *structpb.Value {
	if x != nil {
		return x.Example
	}
	return nil
}

func (x *Schema) GetFormat() string {
	if x != nil {
		return x.Format
	}
	return ""
}

func (x *Schema) GetMultipleOf() int32 {
	if x != nil {
		return x.MultipleOf
	}
	return 0
}

func (x *Schema) GetMaximum() float32 {
	if x != nil {
		return x.Maximum
	}
	return 0
}

func (x *Schema) GetExclusiveMaximum() float32 {
	if x != nil {
		return x.ExclusiveMaximum
	}
	return 0
}

func (x *Schema) GetMinimum() float32 {
	if x != nil {
		return x.Minimum
	}
	return 0
}

func (x *Schema) GetExclusiveMinimum() float32 {
	if x != nil {
		return x.ExclusiveMinimum
	}
	return 0
}

func (x *Schema) GetMaxLength() int32 {
	if x != nil {
		return x.MaxLength
	}
	return 0
}

func (x *Schema) GetMinLength() int32 {
	if x != nil {
		return x.MinLength
	}
	return 0
}

func (x *Schema) GetPattern() string {
	if x != nil {
		return x.Pattern
	}
	return ""
}

func (x *Schema) GetMaxItems() int32 {
	if x != nil {
		return x.MaxItems
	}
	return 0
}

func (x *Schema) GetMinItems() int32 {
	if x != nil {
		return x.MinItems
	}
	return 0
}

func (x *Schema) GetUniqueItems() bool {
	if x != nil {
		return x.UniqueItems
	}
	return false
}

func (x *Schema) GetEnum() []*structpb.Value {
	if x != nil {
		return x.Enum
	}
	return nil
}

func (x *Schema) GetEnums() []*EnumValDesc {
	if x != nil {
		return x.Enums
	}
	return nil
}

func (x *Schema) GetItems() *Schema {
	if x != nil {
		return x.Items
	}
	return nil
}

func (x *Schema) GetRef() *RefSchema {
	if x != nil {
		return x.Ref
	}
	return nil
}

func (x *Schema) GetProperties() map[string]*Schema {
	if x != nil {
		return x.Properties
	}
	return nil
}

func (x *Schema) GetRequired() []string {
	if x != nil {
		return x.Required
	}
	return nil
}

func (x *Schema) GetSortKey() string {
	if x != nil {
		return x.SortKey
	}
	return ""
}

func (x *Schema) GetRaw() string {
	if x != nil {
		return x.Raw
	}
	return ""
}

type InterfaceSchema struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ProjectId     string                 `protobuf:"bytes,1,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`    // 项目ID
	CategoryId    string                 `protobuf:"bytes,2,opt,name=category_id,json=categoryId,proto3" json:"category_id,omitempty"` // 分类ID
	SchemaId      string                 `protobuf:"bytes,3,opt,name=schema_id,json=schemaId,proto3" json:"schema_id,omitempty"`       // 数据模型ID
	FullName      string                 `protobuf:"bytes,4,opt,name=full_name,json=fullName,proto3" json:"full_name,omitempty"`       // 数据模型完整名称
	Name          string                 `protobuf:"bytes,5,opt,name=name,proto3" json:"name,omitempty"`                               // 数据模型名称
	Description   string                 `protobuf:"bytes,6,opt,name=description,proto3" json:"description,omitempty"`                 // 数据模型描述
	Mode          string                 `protobuf:"bytes,7,opt,name=mode,proto3" json:"mode,omitempty"`                               // 创建方式
	ImportType    string                 `protobuf:"bytes,8,opt,name=import_type,json=importType,proto3" json:"import_type,omitempty"` // 导入类型
	Data          *Schema                `protobuf:"bytes,9,opt,name=data,proto3" json:"data,omitempty"`                               // 数据模型数据
	CreatedBy     string                 `protobuf:"bytes,10,opt,name=created_by,json=createdBy,proto3" json:"created_by,omitempty"`
	UpdatedBy     string                 `protobuf:"bytes,11,opt,name=updated_by,json=updatedBy,proto3" json:"updated_by,omitempty"`
	CreatedAt     int64                  `protobuf:"varint,12,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt     int64                  `protobuf:"varint,13,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *InterfaceSchema) Reset() {
	*x = InterfaceSchema{}
	mi := &file_manager_interfacedefinition_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *InterfaceSchema) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InterfaceSchema) ProtoMessage() {}

func (x *InterfaceSchema) ProtoReflect() protoreflect.Message {
	mi := &file_manager_interfacedefinition_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InterfaceSchema.ProtoReflect.Descriptor instead.
func (*InterfaceSchema) Descriptor() ([]byte, []int) {
	return file_manager_interfacedefinition_proto_rawDescGZIP(), []int{6}
}

func (x *InterfaceSchema) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *InterfaceSchema) GetCategoryId() string {
	if x != nil {
		return x.CategoryId
	}
	return ""
}

func (x *InterfaceSchema) GetSchemaId() string {
	if x != nil {
		return x.SchemaId
	}
	return ""
}

func (x *InterfaceSchema) GetFullName() string {
	if x != nil {
		return x.FullName
	}
	return ""
}

func (x *InterfaceSchema) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *InterfaceSchema) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *InterfaceSchema) GetMode() string {
	if x != nil {
		return x.Mode
	}
	return ""
}

func (x *InterfaceSchema) GetImportType() string {
	if x != nil {
		return x.ImportType
	}
	return ""
}

func (x *InterfaceSchema) GetData() *Schema {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *InterfaceSchema) GetCreatedBy() string {
	if x != nil {
		return x.CreatedBy
	}
	return ""
}

func (x *InterfaceSchema) GetUpdatedBy() string {
	if x != nil {
		return x.UpdatedBy
	}
	return ""
}

func (x *InterfaceSchema) GetCreatedAt() int64 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

func (x *InterfaceSchema) GetUpdatedAt() int64 {
	if x != nil {
		return x.UpdatedAt
	}
	return 0
}

type Document struct {
	state         protoimpl.MessageState   `protogen:"open.v1"`
	Headers       []*Schema                `protobuf:"bytes,1,rep,name=headers,proto3" json:"headers,omitempty"`                                                                               // 请求头信息
	PathParams    []*Schema                `protobuf:"bytes,2,rep,name=path_params,json=pathParams,proto3" json:"path_params,omitempty"`                                                       // 路径参数信息
	QueryParams   []*Schema                `protobuf:"bytes,3,rep,name=query_params,json=queryParams,proto3" json:"query_params,omitempty"`                                                    // 查询参数信息
	Body          *BodyData                `protobuf:"bytes,4,opt,name=body,proto3" json:"body,omitempty"`                                                                                     // 请求体信息
	Responses     map[string]*ResponseData `protobuf:"bytes,5,rep,name=responses,proto3" json:"responses,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"` // 响应信息
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Document) Reset() {
	*x = Document{}
	mi := &file_manager_interfacedefinition_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Document) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Document) ProtoMessage() {}

func (x *Document) ProtoReflect() protoreflect.Message {
	mi := &file_manager_interfacedefinition_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Document.ProtoReflect.Descriptor instead.
func (*Document) Descriptor() ([]byte, []int) {
	return file_manager_interfacedefinition_proto_rawDescGZIP(), []int{7}
}

func (x *Document) GetHeaders() []*Schema {
	if x != nil {
		return x.Headers
	}
	return nil
}

func (x *Document) GetPathParams() []*Schema {
	if x != nil {
		return x.PathParams
	}
	return nil
}

func (x *Document) GetQueryParams() []*Schema {
	if x != nil {
		return x.QueryParams
	}
	return nil
}

func (x *Document) GetBody() *BodyData {
	if x != nil {
		return x.Body
	}
	return nil
}

func (x *Document) GetResponses() map[string]*ResponseData {
	if x != nil {
		return x.Responses
	}
	return nil
}

type InterfaceDocument struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	ProjectId         string                 `protobuf:"bytes,1,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`                                                        // 项目ID
	CategoryId        string                 `protobuf:"bytes,2,opt,name=category_id,json=categoryId,proto3" json:"category_id,omitempty"`                                                     // 分类ID
	DocumentId        string                 `protobuf:"bytes,3,opt,name=document_id,json=documentId,proto3" json:"document_id,omitempty"`                                                     // 接口ID
	Name              string                 `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`                                                                                   // 接口名称
	Description       string                 `protobuf:"bytes,5,opt,name=description,proto3" json:"description,omitempty"`                                                                     // 接口描述
	Type              string                 `protobuf:"bytes,6,opt,name=type,proto3" json:"type,omitempty"`                                                                                   // 接口类型
	Mode              string                 `protobuf:"bytes,7,opt,name=mode,proto3" json:"mode,omitempty"`                                                                                   // 创建方式
	ImportType        string                 `protobuf:"bytes,8,opt,name=import_type,json=importType,proto3" json:"import_type,omitempty"`                                                     // 导入类型
	Status            int64                  `protobuf:"varint,9,opt,name=status,proto3" json:"status,omitempty"`                                                                              // 接口状态
	Priority          int64                  `protobuf:"varint,10,opt,name=priority,proto3" json:"priority,omitempty"`                                                                         // 优先级
	Tags              []string               `protobuf:"bytes,11,rep,name=tags,proto3" json:"tags,omitempty"`                                                                                  // 标签
	State             CommonState            `protobuf:"varint,12,opt,name=state,proto3,enum=manager.CommonState" json:"state,omitempty"`                                                      // 接口集合状态
	CaseExecutionMode ExecutionMode          `protobuf:"varint,13,opt,name=case_execution_mode,json=caseExecutionMode,proto3,enum=manager.ExecutionMode" json:"case_execution_mode,omitempty"` // 用例执行方式
	Service           string                 `protobuf:"bytes,14,opt,name=service,proto3" json:"service,omitempty"`                                                                            // 服务名称
	Path              string                 `protobuf:"bytes,15,opt,name=path,proto3" json:"path,omitempty"`                                                                                  // 接口路径
	Method            string                 `protobuf:"bytes,16,opt,name=method,proto3" json:"method,omitempty"`                                                                              // 接口方法
	Data              *Document              `protobuf:"bytes,17,opt,name=data,proto3" json:"data,omitempty"`                                                                                  // 接口详细数据
	MaintainedBy      string                 `protobuf:"bytes,18,opt,name=maintained_by,json=maintainedBy,proto3" json:"maintained_by,omitempty"`                                              // 维护者
	CreatedBy         string                 `protobuf:"bytes,19,opt,name=created_by,json=createdBy,proto3" json:"created_by,omitempty"`                                                       // 创建者
	UpdatedBy         string                 `protobuf:"bytes,20,opt,name=updated_by,json=updatedBy,proto3" json:"updated_by,omitempty"`                                                       // 更新者
	CreatedAt         int64                  `protobuf:"varint,21,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`                                                      // 创建时间
	UpdatedAt         int64                  `protobuf:"varint,22,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`                                                      // 更新时间
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *InterfaceDocument) Reset() {
	*x = InterfaceDocument{}
	mi := &file_manager_interfacedefinition_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *InterfaceDocument) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InterfaceDocument) ProtoMessage() {}

func (x *InterfaceDocument) ProtoReflect() protoreflect.Message {
	mi := &file_manager_interfacedefinition_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InterfaceDocument.ProtoReflect.Descriptor instead.
func (*InterfaceDocument) Descriptor() ([]byte, []int) {
	return file_manager_interfacedefinition_proto_rawDescGZIP(), []int{8}
}

func (x *InterfaceDocument) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *InterfaceDocument) GetCategoryId() string {
	if x != nil {
		return x.CategoryId
	}
	return ""
}

func (x *InterfaceDocument) GetDocumentId() string {
	if x != nil {
		return x.DocumentId
	}
	return ""
}

func (x *InterfaceDocument) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *InterfaceDocument) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *InterfaceDocument) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *InterfaceDocument) GetMode() string {
	if x != nil {
		return x.Mode
	}
	return ""
}

func (x *InterfaceDocument) GetImportType() string {
	if x != nil {
		return x.ImportType
	}
	return ""
}

func (x *InterfaceDocument) GetStatus() int64 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *InterfaceDocument) GetPriority() int64 {
	if x != nil {
		return x.Priority
	}
	return 0
}

func (x *InterfaceDocument) GetTags() []string {
	if x != nil {
		return x.Tags
	}
	return nil
}

func (x *InterfaceDocument) GetState() CommonState {
	if x != nil {
		return x.State
	}
	return CommonState_CS_NULL
}

func (x *InterfaceDocument) GetCaseExecutionMode() ExecutionMode {
	if x != nil {
		return x.CaseExecutionMode
	}
	return ExecutionMode_EM_NULL
}

func (x *InterfaceDocument) GetService() string {
	if x != nil {
		return x.Service
	}
	return ""
}

func (x *InterfaceDocument) GetPath() string {
	if x != nil {
		return x.Path
	}
	return ""
}

func (x *InterfaceDocument) GetMethod() string {
	if x != nil {
		return x.Method
	}
	return ""
}

func (x *InterfaceDocument) GetData() *Document {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *InterfaceDocument) GetMaintainedBy() string {
	if x != nil {
		return x.MaintainedBy
	}
	return ""
}

func (x *InterfaceDocument) GetCreatedBy() string {
	if x != nil {
		return x.CreatedBy
	}
	return ""
}

func (x *InterfaceDocument) GetUpdatedBy() string {
	if x != nil {
		return x.UpdatedBy
	}
	return ""
}

func (x *InterfaceDocument) GetCreatedAt() int64 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

func (x *InterfaceDocument) GetUpdatedAt() int64 {
	if x != nil {
		return x.UpdatedAt
	}
	return 0
}

type InputParameter struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Name          string                 `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`                                  // 入参变量名称
	Description   string                 `protobuf:"bytes,2,opt,name=description,proto3" json:"description,omitempty"`                    // 入参变量描述
	Source        VariableSource         `protobuf:"varint,3,opt,name=source,proto3,enum=manager.VariableSource" json:"source,omitempty"` // 来源
	Manual        *VariableManual        `protobuf:"bytes,4,opt,name=manual,proto3" json:"manual,omitempty"`                              // 手工填写
	Export        *VariableExport        `protobuf:"bytes,5,opt,name=export,proto3" json:"export,omitempty"`                              // 前面节点导出
	Environment   *VariableEnvironment   `protobuf:"bytes,6,opt,name=environment,proto3" json:"environment,omitempty"`                    // 通用配置
	Function      *VariableFunction      `protobuf:"bytes,7,opt,name=function,proto3" json:"function,omitempty"`                          // 函数处理
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *InputParameter) Reset() {
	*x = InputParameter{}
	mi := &file_manager_interfacedefinition_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *InputParameter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InputParameter) ProtoMessage() {}

func (x *InputParameter) ProtoReflect() protoreflect.Message {
	mi := &file_manager_interfacedefinition_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InputParameter.ProtoReflect.Descriptor instead.
func (*InputParameter) Descriptor() ([]byte, []int) {
	return file_manager_interfacedefinition_proto_rawDescGZIP(), []int{9}
}

func (x *InputParameter) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *InputParameter) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *InputParameter) GetSource() VariableSource {
	if x != nil {
		return x.Source
	}
	return VariableSource_MANUAL
}

func (x *InputParameter) GetManual() *VariableManual {
	if x != nil {
		return x.Manual
	}
	return nil
}

func (x *InputParameter) GetExport() *VariableExport {
	if x != nil {
		return x.Export
	}
	return nil
}

func (x *InputParameter) GetEnvironment() *VariableEnvironment {
	if x != nil {
		return x.Environment
	}
	return nil
}

func (x *InputParameter) GetFunction() *VariableFunction {
	if x != nil {
		return x.Function
	}
	return nil
}

type OutputParameter struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Name          string                 `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`                                  // 出参变量名称
	Source        ResponseSource         `protobuf:"varint,2,opt,name=source,proto3,enum=manager.ResponseSource" json:"source,omitempty"` // 来源
	Headers       *VariableHeader        `protobuf:"bytes,3,opt,name=headers,proto3" json:"headers,omitempty"`                            // 响应头
	Body          *VariableBody          `protobuf:"bytes,4,opt,name=body,proto3" json:"body,omitempty"`                                  // 响应体
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *OutputParameter) Reset() {
	*x = OutputParameter{}
	mi := &file_manager_interfacedefinition_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *OutputParameter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OutputParameter) ProtoMessage() {}

func (x *OutputParameter) ProtoReflect() protoreflect.Message {
	mi := &file_manager_interfacedefinition_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OutputParameter.ProtoReflect.Descriptor instead.
func (*OutputParameter) Descriptor() ([]byte, []int) {
	return file_manager_interfacedefinition_proto_rawDescGZIP(), []int{10}
}

func (x *OutputParameter) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *OutputParameter) GetSource() ResponseSource {
	if x != nil {
		return x.Source
	}
	return ResponseSource_HEADERS
}

func (x *OutputParameter) GetHeaders() *VariableHeader {
	if x != nil {
		return x.Headers
	}
	return nil
}

func (x *OutputParameter) GetBody() *VariableBody {
	if x != nil {
		return x.Body
	}
	return nil
}

type InterfaceConfig struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	ProjectId        string                 `protobuf:"bytes,1,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`              // 项目ID
	DocumentId       string                 `protobuf:"bytes,2,opt,name=document_id,json=documentId,proto3" json:"document_id,omitempty"`           // 接口ID
	ConfigId         string                 `protobuf:"bytes,3,opt,name=config_id,json=configId,proto3" json:"config_id,omitempty"`                 // 配置ID
	Name             string                 `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`                                         // 配置名称
	Description      string                 `protobuf:"bytes,5,opt,name=description,proto3" json:"description,omitempty"`                           // 配置描述
	Path             string                 `protobuf:"bytes,6,opt,name=path,proto3" json:"path,omitempty"`                                         // 接口路径
	Method           string                 `protobuf:"bytes,7,opt,name=method,proto3" json:"method,omitempty"`                                     // 接口方法
	Data             *Document              `protobuf:"bytes,8,opt,name=data,proto3" json:"data,omitempty"`                                         // 接口配置数据
	InputParameters  []*InputParameter      `protobuf:"bytes,9,rep,name=input_parameters,json=imports,proto3" json:"input_parameters,omitempty"`    // 入参列表
	OutputParameters []*OutputParameter     `protobuf:"bytes,10,rep,name=output_parameters,json=exports,proto3" json:"output_parameters,omitempty"` // 出参列表
	CreatedBy        string                 `protobuf:"bytes,11,opt,name=created_by,json=createdBy,proto3" json:"created_by,omitempty"`             // 创建者
	UpdatedBy        string                 `protobuf:"bytes,12,opt,name=updated_by,json=updatedBy,proto3" json:"updated_by,omitempty"`             // 更新者
	CreatedAt        int64                  `protobuf:"varint,13,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`            // 创建时间
	UpdatedAt        int64                  `protobuf:"varint,14,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`            // 更新时间
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *InterfaceConfig) Reset() {
	*x = InterfaceConfig{}
	mi := &file_manager_interfacedefinition_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *InterfaceConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InterfaceConfig) ProtoMessage() {}

func (x *InterfaceConfig) ProtoReflect() protoreflect.Message {
	mi := &file_manager_interfacedefinition_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InterfaceConfig.ProtoReflect.Descriptor instead.
func (*InterfaceConfig) Descriptor() ([]byte, []int) {
	return file_manager_interfacedefinition_proto_rawDescGZIP(), []int{11}
}

func (x *InterfaceConfig) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *InterfaceConfig) GetDocumentId() string {
	if x != nil {
		return x.DocumentId
	}
	return ""
}

func (x *InterfaceConfig) GetConfigId() string {
	if x != nil {
		return x.ConfigId
	}
	return ""
}

func (x *InterfaceConfig) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *InterfaceConfig) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *InterfaceConfig) GetPath() string {
	if x != nil {
		return x.Path
	}
	return ""
}

func (x *InterfaceConfig) GetMethod() string {
	if x != nil {
		return x.Method
	}
	return ""
}

func (x *InterfaceConfig) GetData() *Document {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *InterfaceConfig) GetInputParameters() []*InputParameter {
	if x != nil {
		return x.InputParameters
	}
	return nil
}

func (x *InterfaceConfig) GetOutputParameters() []*OutputParameter {
	if x != nil {
		return x.OutputParameters
	}
	return nil
}

func (x *InterfaceConfig) GetCreatedBy() string {
	if x != nil {
		return x.CreatedBy
	}
	return ""
}

func (x *InterfaceConfig) GetUpdatedBy() string {
	if x != nil {
		return x.UpdatedBy
	}
	return ""
}

func (x *InterfaceConfig) GetCreatedAt() int64 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

func (x *InterfaceConfig) GetUpdatedAt() int64 {
	if x != nil {
		return x.UpdatedAt
	}
	return 0
}

type InterfaceCase struct {
	state         protoimpl.MessageState                           `protogen:"open.v1"`
	ProjectId     string                                           `protobuf:"bytes,1,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`             // 项目ID
	DocumentId    string                                           `protobuf:"bytes,2,opt,name=document_id,json=documentId,proto3" json:"document_id,omitempty"`          // 接口ID
	CaseId        string                                           `protobuf:"bytes,3,opt,name=case_id,json=caseId,proto3" json:"case_id,omitempty"`                      // 用例ID
	Name          string                                           `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`                                        // 用例名称
	Description   string                                           `protobuf:"bytes,5,opt,name=description,proto3" json:"description,omitempty"`                          // 用例描述
	Priority      int64                                            `protobuf:"varint,6,opt,name=priority,proto3" json:"priority,omitempty"`                               // 优先级
	Tags          []string                                         `protobuf:"bytes,7,rep,name=tags,proto3" json:"tags,omitempty"`                                        // 标签
	State         ResourceState                                    `protobuf:"varint,8,opt,name=state,proto3,enum=manager.ResourceState" json:"state,omitempty"`          // 状态
	AccountConfig *AccountConfig                                   `protobuf:"bytes,9,opt,name=account_config,json=accountConfig,proto3" json:"account_config,omitempty"` // 池账号配置信息
	Version       string                                           `protobuf:"bytes,10,opt,name=version,proto3" json:"version,omitempty"`                                 // 用例版本
	LatestRecord  *pb.GetCaseLatestRecordResponse_RecordCaseRecord `protobuf:"bytes,11,opt,name=latest_record,json=latestRecord,proto3" json:"latest_record,omitempty"`
	MaintainedBy  string                                           `protobuf:"bytes,12,opt,name=maintained_by,json=maintainedBy,proto3" json:"maintained_by,omitempty"` // 维护者
	CreatedBy     string                                           `protobuf:"bytes,13,opt,name=created_by,json=createdBy,proto3" json:"created_by,omitempty"`          // 创建者
	UpdatedBy     string                                           `protobuf:"bytes,14,opt,name=updated_by,json=updatedBy,proto3" json:"updated_by,omitempty"`          // 更新者
	CreatedAt     int64                                            `protobuf:"varint,15,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`         // 创建时间
	UpdatedAt     int64                                            `protobuf:"varint,16,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`         // 更新时间
	Nodes         []*Node                                          `protobuf:"bytes,17,rep,name=nodes,proto3" json:"nodes,omitempty"`                                   // 点
	Edges         []*Edge                                          `protobuf:"bytes,18,rep,name=edges,proto3" json:"edges,omitempty"`                                   // 线
	Combos        []*Combo                                         `protobuf:"bytes,19,rep,name=combos,proto3" json:"combos,omitempty"`                                 // 框
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *InterfaceCase) Reset() {
	*x = InterfaceCase{}
	mi := &file_manager_interfacedefinition_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *InterfaceCase) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InterfaceCase) ProtoMessage() {}

func (x *InterfaceCase) ProtoReflect() protoreflect.Message {
	mi := &file_manager_interfacedefinition_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InterfaceCase.ProtoReflect.Descriptor instead.
func (*InterfaceCase) Descriptor() ([]byte, []int) {
	return file_manager_interfacedefinition_proto_rawDescGZIP(), []int{12}
}

func (x *InterfaceCase) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *InterfaceCase) GetDocumentId() string {
	if x != nil {
		return x.DocumentId
	}
	return ""
}

func (x *InterfaceCase) GetCaseId() string {
	if x != nil {
		return x.CaseId
	}
	return ""
}

func (x *InterfaceCase) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *InterfaceCase) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *InterfaceCase) GetPriority() int64 {
	if x != nil {
		return x.Priority
	}
	return 0
}

func (x *InterfaceCase) GetTags() []string {
	if x != nil {
		return x.Tags
	}
	return nil
}

func (x *InterfaceCase) GetState() ResourceState {
	if x != nil {
		return x.State
	}
	return ResourceState_RS_NULL
}

func (x *InterfaceCase) GetAccountConfig() *AccountConfig {
	if x != nil {
		return x.AccountConfig
	}
	return nil
}

func (x *InterfaceCase) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *InterfaceCase) GetLatestRecord() *pb.GetCaseLatestRecordResponse_RecordCaseRecord {
	if x != nil {
		return x.LatestRecord
	}
	return nil
}

func (x *InterfaceCase) GetMaintainedBy() string {
	if x != nil {
		return x.MaintainedBy
	}
	return ""
}

func (x *InterfaceCase) GetCreatedBy() string {
	if x != nil {
		return x.CreatedBy
	}
	return ""
}

func (x *InterfaceCase) GetUpdatedBy() string {
	if x != nil {
		return x.UpdatedBy
	}
	return ""
}

func (x *InterfaceCase) GetCreatedAt() int64 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

func (x *InterfaceCase) GetUpdatedAt() int64 {
	if x != nil {
		return x.UpdatedAt
	}
	return 0
}

func (x *InterfaceCase) GetNodes() []*Node {
	if x != nil {
		return x.Nodes
	}
	return nil
}

func (x *InterfaceCase) GetEdges() []*Edge {
	if x != nil {
		return x.Edges
	}
	return nil
}

func (x *InterfaceCase) GetCombos() []*Combo {
	if x != nil {
		return x.Combos
	}
	return nil
}

type SearchInterfaceDocumentReferenceItem struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	ProjectId      string                 `protobuf:"bytes,1,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`                                           // 项目ID
	DocumentId     string                 `protobuf:"bytes,2,opt,name=document_id,json=documentId,proto3" json:"document_id,omitempty"`                                        // 接口ID
	ReferenceType  string                 `protobuf:"bytes,3,opt,name=reference_type,json=referenceType,proto3" json:"reference_type,omitempty"`                               // 引用对象类型
	ReferenceId    string                 `protobuf:"bytes,4,opt,name=reference_id,json=referenceId,proto3" json:"reference_id,omitempty"`                                     // 引用对象ID
	Name           string                 `protobuf:"bytes,5,opt,name=name,proto3" json:"name,omitempty"`                                                                      // 引用对象名称
	Description    string                 `protobuf:"bytes,6,opt,name=description,proto3" json:"description,omitempty"`                                                        // 引用对象描述
	Priority       int64                  `protobuf:"varint,7,opt,name=priority,proto3" json:"priority,omitempty"`                                                             // 优先级
	Tags           []string               `protobuf:"bytes,8,rep,name=tags,proto3" json:"tags,omitempty"`                                                                      // 引用对象标签
	State          CommonState            `protobuf:"varint,9,opt,name=state,proto3,enum=manager.CommonState" json:"state,omitempty"`                                          // 引用对象状态（接口集合的状态）
	ReferenceState CommonState            `protobuf:"varint,10,opt,name=reference_state,json=referenceState,proto3,enum=manager.CommonState" json:"reference_state,omitempty"` // 引用状态（接口集合的引用状态）
	MaintainedBy   string                 `protobuf:"bytes,11,opt,name=maintained_by,json=maintainedBy,proto3" json:"maintained_by,omitempty"`                                 // 维护者
	CreatedBy      string                 `protobuf:"bytes,12,opt,name=created_by,json=createdBy,proto3" json:"created_by,omitempty"`                                          // 创建者
	UpdatedBy      string                 `protobuf:"bytes,13,opt,name=updated_by,json=updatedBy,proto3" json:"updated_by,omitempty"`                                          // 更新者
	CreatedAt      int64                  `protobuf:"varint,14,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`                                         // 创建时间
	UpdatedAt      int64                  `protobuf:"varint,15,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`                                         // 更新时间
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *SearchInterfaceDocumentReferenceItem) Reset() {
	*x = SearchInterfaceDocumentReferenceItem{}
	mi := &file_manager_interfacedefinition_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SearchInterfaceDocumentReferenceItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchInterfaceDocumentReferenceItem) ProtoMessage() {}

func (x *SearchInterfaceDocumentReferenceItem) ProtoReflect() protoreflect.Message {
	mi := &file_manager_interfacedefinition_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchInterfaceDocumentReferenceItem.ProtoReflect.Descriptor instead.
func (*SearchInterfaceDocumentReferenceItem) Descriptor() ([]byte, []int) {
	return file_manager_interfacedefinition_proto_rawDescGZIP(), []int{13}
}

func (x *SearchInterfaceDocumentReferenceItem) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *SearchInterfaceDocumentReferenceItem) GetDocumentId() string {
	if x != nil {
		return x.DocumentId
	}
	return ""
}

func (x *SearchInterfaceDocumentReferenceItem) GetReferenceType() string {
	if x != nil {
		return x.ReferenceType
	}
	return ""
}

func (x *SearchInterfaceDocumentReferenceItem) GetReferenceId() string {
	if x != nil {
		return x.ReferenceId
	}
	return ""
}

func (x *SearchInterfaceDocumentReferenceItem) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *SearchInterfaceDocumentReferenceItem) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *SearchInterfaceDocumentReferenceItem) GetPriority() int64 {
	if x != nil {
		return x.Priority
	}
	return 0
}

func (x *SearchInterfaceDocumentReferenceItem) GetTags() []string {
	if x != nil {
		return x.Tags
	}
	return nil
}

func (x *SearchInterfaceDocumentReferenceItem) GetState() CommonState {
	if x != nil {
		return x.State
	}
	return CommonState_CS_NULL
}

func (x *SearchInterfaceDocumentReferenceItem) GetReferenceState() CommonState {
	if x != nil {
		return x.ReferenceState
	}
	return CommonState_CS_NULL
}

func (x *SearchInterfaceDocumentReferenceItem) GetMaintainedBy() string {
	if x != nil {
		return x.MaintainedBy
	}
	return ""
}

func (x *SearchInterfaceDocumentReferenceItem) GetCreatedBy() string {
	if x != nil {
		return x.CreatedBy
	}
	return ""
}

func (x *SearchInterfaceDocumentReferenceItem) GetUpdatedBy() string {
	if x != nil {
		return x.UpdatedBy
	}
	return ""
}

func (x *SearchInterfaceDocumentReferenceItem) GetCreatedAt() int64 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

func (x *SearchInterfaceDocumentReferenceItem) GetUpdatedAt() int64 {
	if x != nil {
		return x.UpdatedAt
	}
	return 0
}

type UpdateInterfaceCoverageTaskInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ProjectId     string                 `protobuf:"bytes,1,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"` // 项目ID
	KeepDays      int64                  `protobuf:"varint,2,opt,name=keep_days,json=keepDays,proto3" json:"keep_days,omitempty"`   // 保留天数
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateInterfaceCoverageTaskInfo) Reset() {
	*x = UpdateInterfaceCoverageTaskInfo{}
	mi := &file_manager_interfacedefinition_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateInterfaceCoverageTaskInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateInterfaceCoverageTaskInfo) ProtoMessage() {}

func (x *UpdateInterfaceCoverageTaskInfo) ProtoReflect() protoreflect.Message {
	mi := &file_manager_interfacedefinition_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateInterfaceCoverageTaskInfo.ProtoReflect.Descriptor instead.
func (*UpdateInterfaceCoverageTaskInfo) Descriptor() ([]byte, []int) {
	return file_manager_interfacedefinition_proto_rawDescGZIP(), []int{14}
}

func (x *UpdateInterfaceCoverageTaskInfo) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *UpdateInterfaceCoverageTaskInfo) GetKeepDays() int64 {
	if x != nil {
		return x.KeepDays
	}
	return 0
}

type UpdateInterfaceDefinitionTaskInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ProjectId     string                 `protobuf:"bytes,1,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`               // 项目ID
	Type          string                 `protobuf:"bytes,2,opt,name=type,proto3" json:"type,omitempty"`                                          // 接口类型
	LocalPath     string                 `protobuf:"bytes,3,opt,name=local_path,json=localPath,proto3" json:"local_path,omitempty"`               // 本地路径
	DepLocalPaths []string               `protobuf:"bytes,4,rep,name=dep_local_paths,json=depLocalPaths,proto3" json:"dep_local_paths,omitempty"` // 依赖本地路径
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateInterfaceDefinitionTaskInfo) Reset() {
	*x = UpdateInterfaceDefinitionTaskInfo{}
	mi := &file_manager_interfacedefinition_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateInterfaceDefinitionTaskInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateInterfaceDefinitionTaskInfo) ProtoMessage() {}

func (x *UpdateInterfaceDefinitionTaskInfo) ProtoReflect() protoreflect.Message {
	mi := &file_manager_interfacedefinition_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateInterfaceDefinitionTaskInfo.ProtoReflect.Descriptor instead.
func (*UpdateInterfaceDefinitionTaskInfo) Descriptor() ([]byte, []int) {
	return file_manager_interfacedefinition_proto_rawDescGZIP(), []int{15}
}

func (x *UpdateInterfaceDefinitionTaskInfo) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *UpdateInterfaceDefinitionTaskInfo) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *UpdateInterfaceDefinitionTaskInfo) GetLocalPath() string {
	if x != nil {
		return x.LocalPath
	}
	return ""
}

func (x *UpdateInterfaceDefinitionTaskInfo) GetDepLocalPaths() []string {
	if x != nil {
		return x.DepLocalPaths
	}
	return nil
}

type SearchInterfaceCaseReferenceItem struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ProjectId     string                 `protobuf:"bytes,1,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`             // 项目ID
	CaseId        string                 `protobuf:"bytes,2,opt,name=case_id,json=caseId,proto3" json:"case_id,omitempty"`                      // 用例ID
	ReferenceType string                 `protobuf:"bytes,3,opt,name=reference_type,json=referenceType,proto3" json:"reference_type,omitempty"` // 引用对象类型
	ReferenceId   string                 `protobuf:"bytes,4,opt,name=reference_id,json=referenceId,proto3" json:"reference_id,omitempty"`       // 引用对象ID
	Name          string                 `protobuf:"bytes,5,opt,name=name,proto3" json:"name,omitempty"`                                        // 引用对象名称
	Description   string                 `protobuf:"bytes,6,opt,name=description,proto3" json:"description,omitempty"`                          // 引用对象描述
	Priority      int64                  `protobuf:"varint,7,opt,name=priority,proto3" json:"priority,omitempty"`                               // 优先级
	Tags          []string               `protobuf:"bytes,8,rep,name=tags,proto3" json:"tags,omitempty"`                                        // 引用对象标签
	State         CommonState            `protobuf:"varint,9,opt,name=state,proto3,enum=manager.CommonState" json:"state,omitempty"`            // 引用对象状态（API集合的状态）
	MaintainedBy  string                 `protobuf:"bytes,10,opt,name=maintained_by,json=maintainedBy,proto3" json:"maintained_by,omitempty"`   // 维护者
	CreatedBy     string                 `protobuf:"bytes,11,opt,name=created_by,json=createdBy,proto3" json:"created_by,omitempty"`            // 创建者
	UpdatedBy     string                 `protobuf:"bytes,12,opt,name=updated_by,json=updatedBy,proto3" json:"updated_by,omitempty"`            // 更新者
	CreatedAt     int64                  `protobuf:"varint,13,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`           // 创建时间
	UpdatedAt     int64                  `protobuf:"varint,14,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`           // 更新时间
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SearchInterfaceCaseReferenceItem) Reset() {
	*x = SearchInterfaceCaseReferenceItem{}
	mi := &file_manager_interfacedefinition_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SearchInterfaceCaseReferenceItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchInterfaceCaseReferenceItem) ProtoMessage() {}

func (x *SearchInterfaceCaseReferenceItem) ProtoReflect() protoreflect.Message {
	mi := &file_manager_interfacedefinition_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchInterfaceCaseReferenceItem.ProtoReflect.Descriptor instead.
func (*SearchInterfaceCaseReferenceItem) Descriptor() ([]byte, []int) {
	return file_manager_interfacedefinition_proto_rawDescGZIP(), []int{16}
}

func (x *SearchInterfaceCaseReferenceItem) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *SearchInterfaceCaseReferenceItem) GetCaseId() string {
	if x != nil {
		return x.CaseId
	}
	return ""
}

func (x *SearchInterfaceCaseReferenceItem) GetReferenceType() string {
	if x != nil {
		return x.ReferenceType
	}
	return ""
}

func (x *SearchInterfaceCaseReferenceItem) GetReferenceId() string {
	if x != nil {
		return x.ReferenceId
	}
	return ""
}

func (x *SearchInterfaceCaseReferenceItem) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *SearchInterfaceCaseReferenceItem) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *SearchInterfaceCaseReferenceItem) GetPriority() int64 {
	if x != nil {
		return x.Priority
	}
	return 0
}

func (x *SearchInterfaceCaseReferenceItem) GetTags() []string {
	if x != nil {
		return x.Tags
	}
	return nil
}

func (x *SearchInterfaceCaseReferenceItem) GetState() CommonState {
	if x != nil {
		return x.State
	}
	return CommonState_CS_NULL
}

func (x *SearchInterfaceCaseReferenceItem) GetMaintainedBy() string {
	if x != nil {
		return x.MaintainedBy
	}
	return ""
}

func (x *SearchInterfaceCaseReferenceItem) GetCreatedBy() string {
	if x != nil {
		return x.CreatedBy
	}
	return ""
}

func (x *SearchInterfaceCaseReferenceItem) GetUpdatedBy() string {
	if x != nil {
		return x.UpdatedBy
	}
	return ""
}

func (x *SearchInterfaceCaseReferenceItem) GetCreatedAt() int64 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

func (x *SearchInterfaceCaseReferenceItem) GetUpdatedAt() int64 {
	if x != nil {
		return x.UpdatedAt
	}
	return 0
}

type InterfaceCoverageData struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	NumberOfApis  []uint32               `protobuf:"varint,1,rep,packed,name=number_of_apis,json=numberOfApis,proto3" json:"number_of_apis,omitempty"`    // 接口数量
	NumberOfCases []uint32               `protobuf:"varint,2,rep,packed,name=number_of_cases,json=numberOfCases,proto3" json:"number_of_cases,omitempty"` // 用例数量
	CountedAt     []string               `protobuf:"bytes,3,rep,name=counted_at,json=countedAt,proto3" json:"counted_at,omitempty"`                       // 统计日期
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *InterfaceCoverageData) Reset() {
	*x = InterfaceCoverageData{}
	mi := &file_manager_interfacedefinition_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *InterfaceCoverageData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InterfaceCoverageData) ProtoMessage() {}

func (x *InterfaceCoverageData) ProtoReflect() protoreflect.Message {
	mi := &file_manager_interfacedefinition_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InterfaceCoverageData.ProtoReflect.Descriptor instead.
func (*InterfaceCoverageData) Descriptor() ([]byte, []int) {
	return file_manager_interfacedefinition_proto_rawDescGZIP(), []int{17}
}

func (x *InterfaceCoverageData) GetNumberOfApis() []uint32 {
	if x != nil {
		return x.NumberOfApis
	}
	return nil
}

func (x *InterfaceCoverageData) GetNumberOfCases() []uint32 {
	if x != nil {
		return x.NumberOfCases
	}
	return nil
}

func (x *InterfaceCoverageData) GetCountedAt() []string {
	if x != nil {
		return x.CountedAt
	}
	return nil
}

var File_manager_interfacedefinition_proto protoreflect.FileDescriptor

var file_manager_interfacedefinition_proto_rawDesc = []byte{
	0x0a, 0x21, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x66,
	0x61, 0x63, 0x65, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x12, 0x07, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x1a, 0x1c, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x73, 0x74,
	0x72, 0x75, 0x63, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69,
	0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x12, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2f, 0x62, 0x61, 0x73,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x16, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72,
	0x2f, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x17, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2f, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65,
	0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x15, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65,
	0x72, 0x2f, 0x65, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x17, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x65, 0x72, 0x2f, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74,
	0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x5d, 0x0a, 0x0b, 0x45, 0x6e, 0x75, 0x6d,
	0x56, 0x61, 0x6c, 0x44, 0x65, 0x73, 0x63, 0x12, 0x2c, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0xa0, 0x01, 0x0a, 0x09, 0x52, 0x65, 0x66, 0x53,
	0x63, 0x68, 0x65, 0x6d, 0x61, 0x12, 0x1a, 0x0a, 0x08, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x49,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x49,
	0x64, 0x12, 0x1a, 0x0a, 0x08, 0x66, 0x75, 0x6c, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x66, 0x75, 0x6c, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a,
	0x0b, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0b, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x39, 0x0a, 0x0c, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x50, 0x61, 0x74, 0x68, 0x18,
	0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e,
	0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x4e, 0x6f, 0x64, 0x65, 0x52, 0x0c, 0x63, 0x61,
	0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x50, 0x61, 0x74, 0x68, 0x22, 0x4d, 0x0a, 0x0f, 0x54, 0x65,
	0x78, 0x74, 0x44, 0x65, 0x73, 0x63, 0x45, 0x78, 0x61, 0x6d, 0x70, 0x6c, 0x65, 0x12, 0x20, 0x0a,
	0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12,
	0x18, 0x0a, 0x07, 0x65, 0x78, 0x61, 0x6d, 0x70, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x65, 0x78, 0x61, 0x6d, 0x70, 0x6c, 0x65, 0x22, 0xf9, 0x01, 0x0a, 0x08, 0x42, 0x6f,
	0x64, 0x79, 0x44, 0x61, 0x74, 0x61, 0x12, 0x75, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x61, 0xfa, 0x42, 0x5e, 0x72, 0x5c, 0x52, 0x04, 0x6e, 0x6f, 0x6e,
	0x65, 0x52, 0x13, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x70, 0x61, 0x72, 0x74, 0x2f, 0x66, 0x6f, 0x72,
	0x6d, 0x2d, 0x64, 0x61, 0x74, 0x61, 0x52, 0x21, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x2f, 0x78, 0x2d, 0x77, 0x77, 0x77, 0x2d, 0x66, 0x6f, 0x72, 0x6d, 0x2d, 0x75,
	0x72, 0x6c, 0x65, 0x6e, 0x63, 0x6f, 0x64, 0x65, 0x64, 0x52, 0x10, 0x61, 0x70, 0x70, 0x6c, 0x69,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x6a, 0x73, 0x6f, 0x6e, 0x52, 0x0a, 0x74, 0x65, 0x78,
	0x74, 0x2f, 0x70, 0x6c, 0x61, 0x69, 0x6e, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x23, 0x0a,
	0x04, 0x66, 0x6f, 0x72, 0x6d, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x6d, 0x61,
	0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x53, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x52, 0x04, 0x66, 0x6f,
	0x72, 0x6d, 0x12, 0x23, 0x0a, 0x04, 0x6a, 0x73, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x0f, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x53, 0x63, 0x68, 0x65, 0x6d,
	0x61, 0x52, 0x04, 0x6a, 0x73, 0x6f, 0x6e, 0x12, 0x2c, 0x0a, 0x04, 0x74, 0x65, 0x78, 0x74, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e,
	0x54, 0x65, 0x78, 0x74, 0x44, 0x65, 0x73, 0x63, 0x45, 0x78, 0x61, 0x6d, 0x70, 0x6c, 0x65, 0x52,
	0x04, 0x74, 0x65, 0x78, 0x74, 0x22, 0xa3, 0x01, 0x0a, 0x0c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x44, 0x61, 0x74, 0x61, 0x12, 0x1f, 0x0a, 0x0b, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x29, 0x0a, 0x07, 0x68, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x6d, 0x61, 0x6e,
	0x61, 0x67, 0x65, 0x72, 0x2e, 0x53, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x52, 0x07, 0x68, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x73, 0x12, 0x25, 0x0a, 0x04, 0x62, 0x6f, 0x64, 0x79, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x11, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x42, 0x6f, 0x64,
	0x79, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x62, 0x6f, 0x64, 0x79, 0x22, 0xfb, 0x09, 0x0a, 0x06,
	0x53, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x12, 0x1e, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x72, 0x03, 0xd0, 0x01, 0x01, 0x52,
	0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x80, 0x01, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x6c, 0xfa, 0x42, 0x69, 0x72, 0x67, 0x52, 0x06, 0x73, 0x74,
	0x72, 0x69, 0x6e, 0x67, 0x52, 0x07, 0x69, 0x6e, 0x74, 0x65, 0x67, 0x65, 0x72, 0x52, 0x06, 0x6e,
	0x75, 0x6d, 0x62, 0x65, 0x72, 0x52, 0x07, 0x62, 0x6f, 0x6f, 0x6c, 0x65, 0x61, 0x6e, 0x52, 0x05,
	0x61, 0x72, 0x72, 0x61, 0x79, 0x52, 0x06, 0x6f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x52, 0x04, 0x6e,
	0x75, 0x6c, 0x6c, 0x52, 0x03, 0x61, 0x6e, 0x79, 0x52, 0x05, 0x61, 0x6c, 0x6c, 0x4f, 0x66, 0x52,
	0x05, 0x61, 0x6e, 0x79, 0x4f, 0x66, 0x52, 0x05, 0x6f, 0x6e, 0x65, 0x4f, 0x66, 0x52, 0x06, 0x63,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x52, 0x06, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x52, 0x04, 0x66,
	0x69, 0x6c, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b,
	0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x14, 0x0a, 0x05, 0x69,
	0x6e, 0x64, 0x65, 0x78, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x69, 0x6e, 0x64, 0x65,
	0x78, 0x12, 0x24, 0x0a, 0x0d, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72,
	0x65, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0d, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x52,
	0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x12, 0x1e, 0x0a, 0x0a, 0x64, 0x65, 0x70, 0x72, 0x65,
	0x63, 0x61, 0x74, 0x65, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0a, 0x64, 0x65, 0x70,
	0x72, 0x65, 0x63, 0x61, 0x74, 0x65, 0x64, 0x12, 0x30, 0x0a, 0x07, 0x64, 0x65, 0x66, 0x61, 0x75,
	0x6c, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x56, 0x61, 0x6c, 0x75, 0x65,
	0x52, 0x07, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x12, 0x30, 0x0a, 0x07, 0x65, 0x78, 0x61,
	0x6d, 0x70, 0x6c, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x56, 0x61, 0x6c,
	0x75, 0x65, 0x52, 0x07, 0x65, 0x78, 0x61, 0x6d, 0x70, 0x6c, 0x65, 0x12, 0x91, 0x01, 0x0a, 0x06,
	0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x42, 0x79, 0xfa, 0x42,
	0x76, 0x72, 0x74, 0x52, 0x05, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x52, 0x05, 0x69, 0x6e, 0x74, 0x36,
	0x34, 0x52, 0x05, 0x66, 0x6c, 0x6f, 0x61, 0x74, 0x52, 0x06, 0x64, 0x6f, 0x75, 0x62, 0x6c, 0x65,
	0x52, 0x08, 0x70, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x52, 0x09, 0x64, 0x61, 0x74, 0x65,
	0x2d, 0x74, 0x69, 0x6d, 0x65, 0x52, 0x04, 0x64, 0x61, 0x74, 0x65, 0x52, 0x04, 0x74, 0x69, 0x6d,
	0x65, 0x52, 0x08, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x05, 0x65, 0x6d, 0x61,
	0x69, 0x6c, 0x52, 0x08, 0x68, 0x6f, 0x73, 0x74, 0x6e, 0x61, 0x6d, 0x65, 0x52, 0x04, 0x69, 0x70,
	0x76, 0x34, 0x52, 0x04, 0x69, 0x70, 0x76, 0x36, 0x52, 0x03, 0x75, 0x72, 0x69, 0x52, 0x05, 0x72,
	0x65, 0x67, 0x65, 0x78, 0xd0, 0x01, 0x01, 0x52, 0x06, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x12,
	0x1e, 0x0a, 0x0a, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x70, 0x6c, 0x65, 0x4f, 0x66, 0x18, 0x0a, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x0a, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x70, 0x6c, 0x65, 0x4f, 0x66, 0x12,
	0x18, 0x0a, 0x07, 0x6d, 0x61, 0x78, 0x69, 0x6d, 0x75, 0x6d, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x02,
	0x52, 0x07, 0x6d, 0x61, 0x78, 0x69, 0x6d, 0x75, 0x6d, 0x12, 0x2a, 0x0a, 0x10, 0x65, 0x78, 0x63,
	0x6c, 0x75, 0x73, 0x69, 0x76, 0x65, 0x4d, 0x61, 0x78, 0x69, 0x6d, 0x75, 0x6d, 0x18, 0x0c, 0x20,
	0x01, 0x28, 0x02, 0x52, 0x10, 0x65, 0x78, 0x63, 0x6c, 0x75, 0x73, 0x69, 0x76, 0x65, 0x4d, 0x61,
	0x78, 0x69, 0x6d, 0x75, 0x6d, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x69, 0x6e, 0x69, 0x6d, 0x75, 0x6d,
	0x18, 0x0d, 0x20, 0x01, 0x28, 0x02, 0x52, 0x07, 0x6d, 0x69, 0x6e, 0x69, 0x6d, 0x75, 0x6d, 0x12,
	0x2a, 0x0a, 0x10, 0x65, 0x78, 0x63, 0x6c, 0x75, 0x73, 0x69, 0x76, 0x65, 0x4d, 0x69, 0x6e, 0x69,
	0x6d, 0x75, 0x6d, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x02, 0x52, 0x10, 0x65, 0x78, 0x63, 0x6c, 0x75,
	0x73, 0x69, 0x76, 0x65, 0x4d, 0x69, 0x6e, 0x69, 0x6d, 0x75, 0x6d, 0x12, 0x1c, 0x0a, 0x09, 0x6d,
	0x61, 0x78, 0x4c, 0x65, 0x6e, 0x67, 0x74, 0x68, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09,
	0x6d, 0x61, 0x78, 0x4c, 0x65, 0x6e, 0x67, 0x74, 0x68, 0x12, 0x1c, 0x0a, 0x09, 0x6d, 0x69, 0x6e,
	0x4c, 0x65, 0x6e, 0x67, 0x74, 0x68, 0x18, 0x10, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x6d, 0x69,
	0x6e, 0x4c, 0x65, 0x6e, 0x67, 0x74, 0x68, 0x12, 0x18, 0x0a, 0x07, 0x70, 0x61, 0x74, 0x74, 0x65,
	0x72, 0x6e, 0x18, 0x11, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x70, 0x61, 0x74, 0x74, 0x65, 0x72,
	0x6e, 0x12, 0x1a, 0x0a, 0x08, 0x6d, 0x61, 0x78, 0x49, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x12, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x08, 0x6d, 0x61, 0x78, 0x49, 0x74, 0x65, 0x6d, 0x73, 0x12, 0x1a, 0x0a,
	0x08, 0x6d, 0x69, 0x6e, 0x49, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x13, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x08, 0x6d, 0x69, 0x6e, 0x49, 0x74, 0x65, 0x6d, 0x73, 0x12, 0x20, 0x0a, 0x0b, 0x75, 0x6e, 0x69,
	0x71, 0x75, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x14, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b,
	0x75, 0x6e, 0x69, 0x71, 0x75, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x73, 0x12, 0x2a, 0x0a, 0x04, 0x65,
	0x6e, 0x75, 0x6d, 0x18, 0x15, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x56, 0x61, 0x6c, 0x75,
	0x65, 0x52, 0x04, 0x65, 0x6e, 0x75, 0x6d, 0x12, 0x2a, 0x0a, 0x05, 0x65, 0x6e, 0x75, 0x6d, 0x73,
	0x18, 0x16, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72,
	0x2e, 0x45, 0x6e, 0x75, 0x6d, 0x56, 0x61, 0x6c, 0x44, 0x65, 0x73, 0x63, 0x52, 0x05, 0x65, 0x6e,
	0x75, 0x6d, 0x73, 0x12, 0x25, 0x0a, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x17, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x53, 0x63, 0x68,
	0x65, 0x6d, 0x61, 0x52, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x12, 0x24, 0x0a, 0x03, 0x72, 0x65,
	0x66, 0x18, 0x18, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65,
	0x72, 0x2e, 0x52, 0x65, 0x66, 0x53, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x52, 0x03, 0x72, 0x65, 0x66,
	0x12, 0x3f, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x18, 0x19,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x53,
	0x63, 0x68, 0x65, 0x6d, 0x61, 0x2e, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0a, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65,
	0x73, 0x12, 0x1a, 0x0a, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x18, 0x1a, 0x20,
	0x03, 0x28, 0x09, 0x52, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x12, 0x18, 0x0a,
	0x07, 0x73, 0x6f, 0x72, 0x74, 0x4b, 0x65, 0x79, 0x18, 0x1b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x73, 0x6f, 0x72, 0x74, 0x4b, 0x65, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x72, 0x61, 0x77, 0x18, 0x1c,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x72, 0x61, 0x77, 0x1a, 0x4e, 0x0a, 0x0f, 0x50, 0x72, 0x6f,
	0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03,
	0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x25,
	0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0f, 0x2e,
	0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x53, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x52, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x97, 0x03, 0x0a, 0x0f, 0x49, 0x6e,
	0x74, 0x65, 0x72, 0x66, 0x61, 0x63, 0x65, 0x53, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x12, 0x1d, 0x0a,
	0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b,
	0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0a, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x49, 0x64, 0x12, 0x1b, 0x0a,
	0x09, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x66, 0x75,
	0x6c, 0x6c, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x66,
	0x75, 0x6c, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64,
	0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x12, 0x0a,
	0x04, 0x6d, 0x6f, 0x64, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6d, 0x6f, 0x64,
	0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x69, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x69, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x23, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x0f, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x53, 0x63, 0x68, 0x65, 0x6d,
	0x61, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x64, 0x5f, 0x62, 0x79, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x64, 0x42, 0x79, 0x12, 0x1d, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x64, 0x5f, 0x62, 0x79, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x64, 0x42, 0x79, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64,
	0x5f, 0x61, 0x74, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x64, 0x41, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f,
	0x61, 0x74, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x64, 0x41, 0x74, 0x22, 0xd7, 0x02, 0x0a, 0x08, 0x44, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74,
	0x12, 0x29, 0x0a, 0x07, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x0f, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x53, 0x63, 0x68, 0x65,
	0x6d, 0x61, 0x52, 0x07, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x73, 0x12, 0x30, 0x0a, 0x0b, 0x70,
	0x61, 0x74, 0x68, 0x5f, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x0f, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x53, 0x63, 0x68, 0x65, 0x6d,
	0x61, 0x52, 0x0a, 0x70, 0x61, 0x74, 0x68, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x32, 0x0a,
	0x0c, 0x71, 0x75, 0x65, 0x72, 0x79, 0x5f, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x18, 0x03, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x53, 0x63,
	0x68, 0x65, 0x6d, 0x61, 0x52, 0x0b, 0x71, 0x75, 0x65, 0x72, 0x79, 0x50, 0x61, 0x72, 0x61, 0x6d,
	0x73, 0x12, 0x25, 0x0a, 0x04, 0x62, 0x6f, 0x64, 0x79, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x11, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x42, 0x6f, 0x64, 0x79, 0x44, 0x61,
	0x74, 0x61, 0x52, 0x04, 0x62, 0x6f, 0x64, 0x79, 0x12, 0x3e, 0x0a, 0x09, 0x72, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x6d, 0x61,
	0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x44, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x09, 0x72,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x73, 0x1a, 0x53, 0x0a, 0x0e, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65,
	0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x2b, 0x0a, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x6d, 0x61,
	0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x44, 0x61,
	0x74, 0x61, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0xbd, 0x05,
	0x0a, 0x11, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x66, 0x61, 0x63, 0x65, 0x44, 0x6f, 0x63, 0x75, 0x6d,
	0x65, 0x6e, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74,
	0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x5f, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72,
	0x79, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x64, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x5f,
	0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x64, 0x6f, 0x63, 0x75, 0x6d, 0x65,
	0x6e, 0x74, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64,
	0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x12,
	0x0a, 0x04, 0x6d, 0x6f, 0x64, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6d, 0x6f,
	0x64, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x69, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x69, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x09, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x70,
	0x72, 0x69, 0x6f, 0x72, 0x69, 0x74, 0x79, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x70,
	0x72, 0x69, 0x6f, 0x72, 0x69, 0x74, 0x79, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x61, 0x67, 0x73, 0x18,
	0x0b, 0x20, 0x03, 0x28, 0x09, 0x52, 0x04, 0x74, 0x61, 0x67, 0x73, 0x12, 0x2a, 0x0a, 0x05, 0x73,
	0x74, 0x61, 0x74, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x6d, 0x61, 0x6e,
	0x61, 0x67, 0x65, 0x72, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x65,
	0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x12, 0x46, 0x0a, 0x13, 0x63, 0x61, 0x73, 0x65, 0x5f,
	0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x18, 0x0d,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x16, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x45,
	0x78, 0x65, 0x63, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x6f, 0x64, 0x65, 0x52, 0x11, 0x63, 0x61,
	0x73, 0x65, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x6f, 0x64, 0x65, 0x12,
	0x18, 0x0a, 0x07, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x74,
	0x68, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x70, 0x61, 0x74, 0x68, 0x12, 0x16, 0x0a,
	0x06, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x18, 0x10, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6d,
	0x65, 0x74, 0x68, 0x6f, 0x64, 0x12, 0x25, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x11, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x44, 0x6f,
	0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x12, 0x23, 0x0a, 0x0d,
	0x6d, 0x61, 0x69, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x64, 0x5f, 0x62, 0x79, 0x18, 0x12, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0c, 0x6d, 0x61, 0x69, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x64, 0x42,
	0x79, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x62, 0x79, 0x18,
	0x13, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x42, 0x79,
	0x12, 0x1d, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x62, 0x79, 0x18, 0x14,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x42, 0x79, 0x12,
	0x1d, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x15, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x1d,
	0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x16, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x22, 0x8e, 0x03,
	0x0a, 0x0e, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72,
	0x12, 0x1e, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0a,
	0xfa, 0x42, 0x07, 0x72, 0x05, 0x10, 0x01, 0xd0, 0x01, 0x01, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x39, 0x0a, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x17, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x56, 0x61, 0x72,
	0x69, 0x61, 0x62, 0x6c, 0x65, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x42, 0x08, 0xfa, 0x42, 0x05,
	0x82, 0x01, 0x02, 0x10, 0x01, 0x52, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x39, 0x0a,
	0x06, 0x6d, 0x61, 0x6e, 0x75, 0x61, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e,
	0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x56, 0x61, 0x72, 0x69, 0x61, 0x62, 0x6c, 0x65,
	0x4d, 0x61, 0x6e, 0x75, 0x61, 0x6c, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01,
	0x52, 0x06, 0x6d, 0x61, 0x6e, 0x75, 0x61, 0x6c, 0x12, 0x39, 0x0a, 0x06, 0x65, 0x78, 0x70, 0x6f,
	0x72, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67,
	0x65, 0x72, 0x2e, 0x56, 0x61, 0x72, 0x69, 0x61, 0x62, 0x6c, 0x65, 0x45, 0x78, 0x70, 0x6f, 0x72,
	0x74, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x06, 0x65, 0x78, 0x70,
	0x6f, 0x72, 0x74, 0x12, 0x48, 0x0a, 0x0b, 0x65, 0x6e, 0x76, 0x69, 0x72, 0x6f, 0x6e, 0x6d, 0x65,
	0x6e, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67,
	0x65, 0x72, 0x2e, 0x56, 0x61, 0x72, 0x69, 0x61, 0x62, 0x6c, 0x65, 0x45, 0x6e, 0x76, 0x69, 0x72,
	0x6f, 0x6e, 0x6d, 0x65, 0x6e, 0x74, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01,
	0x52, 0x0b, 0x65, 0x6e, 0x76, 0x69, 0x72, 0x6f, 0x6e, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x3f, 0x0a,
	0x08, 0x66, 0x75, 0x6e, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x19, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x56, 0x61, 0x72, 0x69, 0x61, 0x62,
	0x6c, 0x65, 0x46, 0x75, 0x6e, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a,
	0x01, 0x02, 0x10, 0x01, 0x52, 0x08, 0x66, 0x75, 0x6e, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0xde,
	0x01, 0x0a, 0x0f, 0x4f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74,
	0x65, 0x72, 0x12, 0x1e, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x0a, 0xfa, 0x42, 0x07, 0x72, 0x05, 0x10, 0x01, 0xd0, 0x01, 0x01, 0x52, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x12, 0x39, 0x0a, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x17, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x42, 0x08, 0xfa, 0x42, 0x05,
	0x82, 0x01, 0x02, 0x10, 0x01, 0x52, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x3b, 0x0a,
	0x07, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17,
	0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x56, 0x61, 0x72, 0x69, 0x61, 0x62, 0x6c,
	0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10,
	0x01, 0x52, 0x07, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x73, 0x12, 0x33, 0x0a, 0x04, 0x62, 0x6f,
	0x64, 0x79, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67,
	0x65, 0x72, 0x2e, 0x56, 0x61, 0x72, 0x69, 0x61, 0x62, 0x6c, 0x65, 0x42, 0x6f, 0x64, 0x79, 0x42,
	0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x04, 0x62, 0x6f, 0x64, 0x79, 0x22,
	0xed, 0x03, 0x0a, 0x0f, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x66, 0x61, 0x63, 0x65, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74,
	0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x64, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x64, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e,
	0x74, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x69, 0x64,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x49, 0x64,
	0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x74, 0x68, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x70, 0x61, 0x74, 0x68, 0x12, 0x16, 0x0a, 0x06, 0x6d, 0x65,
	0x74, 0x68, 0x6f, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6d, 0x65, 0x74, 0x68,
	0x6f, 0x64, 0x12, 0x25, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x11, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x44, 0x6f, 0x63, 0x75, 0x6d,
	0x65, 0x6e, 0x74, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x12, 0x3a, 0x0a, 0x10, 0x69, 0x6e, 0x70,
	0x75, 0x74, 0x5f, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x18, 0x09, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x49, 0x6e,
	0x70, 0x75, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x52, 0x07, 0x69, 0x6d,
	0x70, 0x6f, 0x72, 0x74, 0x73, 0x12, 0x3c, 0x0a, 0x11, 0x6f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x5f,
	0x70, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x18, 0x0a, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x18, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x4f, 0x75, 0x74, 0x70, 0x75,
	0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x52, 0x07, 0x65, 0x78, 0x70, 0x6f,
	0x72, 0x74, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x62,
	0x79, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64,
	0x42, 0x79, 0x12, 0x1d, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x62, 0x79,
	0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x42,
	0x79, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18,
	0x0d, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74,
	0x12, 0x1d, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0e,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x22,
	0xc5, 0x05, 0x0a, 0x0d, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x66, 0x61, 0x63, 0x65, 0x43, 0x61, 0x73,
	0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64,
	0x12, 0x1f, 0x0a, 0x0b, 0x64, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x64, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x49,
	0x64, 0x12, 0x17, 0x0a, 0x07, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x63, 0x61, 0x73, 0x65, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x20,
	0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x12, 0x1a, 0x0a, 0x08, 0x70, 0x72, 0x69, 0x6f, 0x72, 0x69, 0x74, 0x79, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x08, 0x70, 0x72, 0x69, 0x6f, 0x72, 0x69, 0x74, 0x79, 0x12, 0x12, 0x0a, 0x04,
	0x74, 0x61, 0x67, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x09, 0x52, 0x04, 0x74, 0x61, 0x67, 0x73,
	0x12, 0x2c, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x16, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x12, 0x3d,
	0x0a, 0x0e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72,
	0x2e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x0d,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x18, 0x0a,
	0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x5b, 0x0a, 0x0d, 0x6c, 0x61, 0x74, 0x65, 0x73,
	0x74, 0x5f, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x36,
	0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x61, 0x73,
	0x65, 0x4c, 0x61, 0x74, 0x65, 0x73, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x43, 0x61, 0x73, 0x65,
	0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x0c, 0x6c, 0x61, 0x74, 0x65, 0x73, 0x74, 0x52, 0x65,
	0x63, 0x6f, 0x72, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x6d, 0x61, 0x69, 0x6e, 0x74, 0x61, 0x69, 0x6e,
	0x65, 0x64, 0x5f, 0x62, 0x79, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x6d, 0x61, 0x69,
	0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x64, 0x42, 0x79, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x64, 0x5f, 0x62, 0x79, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x42, 0x79, 0x12, 0x1d, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x64, 0x5f, 0x62, 0x79, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x75, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x64, 0x42, 0x79, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x63, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x64, 0x5f, 0x61, 0x74, 0x18, 0x10, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x23, 0x0a, 0x05, 0x6e, 0x6f, 0x64, 0x65, 0x73, 0x18, 0x11,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x4e,
	0x6f, 0x64, 0x65, 0x52, 0x05, 0x6e, 0x6f, 0x64, 0x65, 0x73, 0x12, 0x23, 0x0a, 0x05, 0x65, 0x64,
	0x67, 0x65, 0x73, 0x18, 0x12, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x6d, 0x61, 0x6e, 0x61,
	0x67, 0x65, 0x72, 0x2e, 0x45, 0x64, 0x67, 0x65, 0x52, 0x05, 0x65, 0x64, 0x67, 0x65, 0x73, 0x12,
	0x26, 0x0a, 0x06, 0x63, 0x6f, 0x6d, 0x62, 0x6f, 0x73, 0x18, 0x13, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x0e, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x43, 0x6f, 0x6d, 0x62, 0x6f, 0x52,
	0x06, 0x63, 0x6f, 0x6d, 0x62, 0x6f, 0x73, 0x22, 0xa2, 0x04, 0x0a, 0x24, 0x53, 0x65, 0x61, 0x72,
	0x63, 0x68, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x66, 0x61, 0x63, 0x65, 0x44, 0x6f, 0x63, 0x75, 0x6d,
	0x65, 0x6e, 0x74, 0x52, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d,
	0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x12,
	0x1f, 0x0a, 0x0b, 0x64, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x64, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64,
	0x12, 0x25, 0x0a, 0x0e, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65,
	0x6e, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x72, 0x65, 0x66, 0x65, 0x72,
	0x65, 0x6e, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x72,
	0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x20,
	0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x12, 0x1a, 0x0a, 0x08, 0x70, 0x72, 0x69, 0x6f, 0x72, 0x69, 0x74, 0x79, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x08, 0x70, 0x72, 0x69, 0x6f, 0x72, 0x69, 0x74, 0x79, 0x12, 0x12, 0x0a, 0x04,
	0x74, 0x61, 0x67, 0x73, 0x18, 0x08, 0x20, 0x03, 0x28, 0x09, 0x52, 0x04, 0x74, 0x61, 0x67, 0x73,
	0x12, 0x2a, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x14, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x12, 0x3d, 0x0a, 0x0f,
	0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18,
	0x0a, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e,
	0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x0e, 0x72, 0x65, 0x66,
	0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x6d,
	0x61, 0x69, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x64, 0x5f, 0x62, 0x79, 0x18, 0x0b, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0c, 0x6d, 0x61, 0x69, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x64, 0x42, 0x79,
	0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x62, 0x79, 0x18, 0x0c,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x42, 0x79, 0x12,
	0x1d, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x62, 0x79, 0x18, 0x0d, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x42, 0x79, 0x12, 0x1d,
	0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0e, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x1d, 0x0a,
	0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0f, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x22, 0x89, 0x01, 0x0a,
	0x1f, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x66, 0x61, 0x63, 0x65,
	0x43, 0x6f, 0x76, 0x65, 0x72, 0x61, 0x67, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x49, 0x6e, 0x66, 0x6f,
	0x12, 0x3d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x1e, 0xfa, 0x42, 0x1b, 0x72, 0x19, 0x32, 0x17, 0x28, 0x3f, 0x3a,
	0x5e, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x3a, 0x2e, 0x2b, 0x3f, 0x7c,
	0x5e, 0x31, 0x24, 0x29, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x12,
	0x27, 0x0a, 0x09, 0x6b, 0x65, 0x65, 0x70, 0x5f, 0x64, 0x61, 0x79, 0x73, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x03, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x22, 0x05, 0x18, 0xed, 0x02, 0x28, 0x01, 0x52, 0x08,
	0x6b, 0x65, 0x65, 0x70, 0x44, 0x61, 0x79, 0x73, 0x22, 0x89, 0x02, 0x0a, 0x21, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x66, 0x61, 0x63, 0x65, 0x44, 0x65, 0x66, 0x69,
	0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x61, 0x73, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x3d,
	0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x1e, 0xfa, 0x42, 0x1b, 0x72, 0x19, 0x32, 0x17, 0x28, 0x3f, 0x3a, 0x5e, 0x70,
	0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x3a, 0x2e, 0x2b, 0x3f, 0x7c, 0x5e, 0x31,
	0x24, 0x29, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x12, 0x45, 0x0a,
	0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x31, 0xfa, 0x42, 0x2e,
	0x72, 0x2c, 0x52, 0x07, 0x4f, 0x70, 0x65, 0x6e, 0x41, 0x70, 0x69, 0x52, 0x04, 0x67, 0x52, 0x50,
	0x43, 0x52, 0x04, 0x59, 0x41, 0x70, 0x69, 0x52, 0x02, 0x54, 0x54, 0x52, 0x06, 0x54, 0x54, 0x4d,
	0x65, 0x74, 0x61, 0x52, 0x09, 0x52, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x52, 0x04,
	0x74, 0x79, 0x70, 0x65, 0x12, 0x26, 0x0a, 0x0a, 0x6c, 0x6f, 0x63, 0x61, 0x6c, 0x5f, 0x70, 0x61,
	0x74, 0x68, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10,
	0x01, 0x52, 0x09, 0x6c, 0x6f, 0x63, 0x61, 0x6c, 0x50, 0x61, 0x74, 0x68, 0x12, 0x36, 0x0a, 0x0f,
	0x64, 0x65, 0x70, 0x5f, 0x6c, 0x6f, 0x63, 0x61, 0x6c, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x73, 0x18,
	0x04, 0x20, 0x03, 0x28, 0x09, 0x42, 0x0e, 0xfa, 0x42, 0x0b, 0x92, 0x01, 0x08, 0x22, 0x04, 0x72,
	0x02, 0x10, 0x01, 0x28, 0x01, 0x52, 0x0d, 0x64, 0x65, 0x70, 0x4c, 0x6f, 0x63, 0x61, 0x6c, 0x50,
	0x61, 0x74, 0x68, 0x73, 0x22, 0xd7, 0x03, 0x0a, 0x20, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x49,
	0x6e, 0x74, 0x65, 0x72, 0x66, 0x61, 0x63, 0x65, 0x43, 0x61, 0x73, 0x65, 0x52, 0x65, 0x66, 0x65,
	0x72, 0x65, 0x6e, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f,
	0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70,
	0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x63, 0x61, 0x73, 0x65,
	0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x63, 0x61, 0x73, 0x65, 0x49,
	0x64, 0x12, 0x25, 0x0a, 0x0e, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x72, 0x65, 0x66, 0x65, 0x72,
	0x65, 0x6e, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x72, 0x65, 0x66, 0x65,
	0x72, 0x65, 0x6e, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b,
	0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12,
	0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x72, 0x69, 0x6f, 0x72, 0x69, 0x74, 0x79, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x08, 0x70, 0x72, 0x69, 0x6f, 0x72, 0x69, 0x74, 0x79, 0x12, 0x12, 0x0a,
	0x04, 0x74, 0x61, 0x67, 0x73, 0x18, 0x08, 0x20, 0x03, 0x28, 0x09, 0x52, 0x04, 0x74, 0x61, 0x67,
	0x73, 0x12, 0x2a, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x14, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x12, 0x23, 0x0a,
	0x0d, 0x6d, 0x61, 0x69, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x64, 0x5f, 0x62, 0x79, 0x18, 0x0a,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x6d, 0x61, 0x69, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x64,
	0x42, 0x79, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x62, 0x79,
	0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x42,
	0x79, 0x12, 0x1d, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x62, 0x79, 0x18,
	0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x42, 0x79,
	0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0d,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12,
	0x1d, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0e, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x22, 0x84,
	0x01, 0x0a, 0x15, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x66, 0x61, 0x63, 0x65, 0x43, 0x6f, 0x76, 0x65,
	0x72, 0x61, 0x67, 0x65, 0x44, 0x61, 0x74, 0x61, 0x12, 0x24, 0x0a, 0x0e, 0x6e, 0x75, 0x6d, 0x62,
	0x65, 0x72, 0x5f, 0x6f, 0x66, 0x5f, 0x61, 0x70, 0x69, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0d,
	0x52, 0x0c, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x4f, 0x66, 0x41, 0x70, 0x69, 0x73, 0x12, 0x26,
	0x0a, 0x0f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x5f, 0x6f, 0x66, 0x5f, 0x63, 0x61, 0x73, 0x65,
	0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0d, 0x52, 0x0d, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x4f,
	0x66, 0x43, 0x61, 0x73, 0x65, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x65,
	0x64, 0x5f, 0x61, 0x74, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x52, 0x09, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x65, 0x64, 0x41, 0x74, 0x2a, 0x3a, 0x0a, 0x0a, 0x57, 0x69, 0x74, 0x68, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x12, 0x0e, 0x0a, 0x0a, 0x44, 0x4f, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x55, 0x53,
	0x45, 0x10, 0x00, 0x12, 0x0d, 0x0a, 0x09, 0x41, 0x55, 0x54, 0x4f, 0x4d, 0x41, 0x54, 0x49, 0x43,
	0x10, 0x01, 0x12, 0x0d, 0x0a, 0x09, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10,
	0x02, 0x42, 0x41, 0x5a, 0x3f, 0x67, 0x69, 0x74, 0x6c, 0x61, 0x62, 0x2e, 0x74, 0x74, 0x79, 0x75,
	0x79, 0x69, 0x6e, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x54, 0x65, 0x73, 0x74, 0x44, 0x65, 0x76, 0x65,
	0x6c, 0x6f, 0x70, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x70, 0x72, 0x6f, 0x62, 0x65, 0x2d, 0x62, 0x61,
	0x63, 0x6b, 0x65, 0x6e, 0x64, 0x2f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2f, 0x72, 0x70,
	0x63, 0x2f, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_manager_interfacedefinition_proto_rawDescOnce sync.Once
	file_manager_interfacedefinition_proto_rawDescData = file_manager_interfacedefinition_proto_rawDesc
)

func file_manager_interfacedefinition_proto_rawDescGZIP() []byte {
	file_manager_interfacedefinition_proto_rawDescOnce.Do(func() {
		file_manager_interfacedefinition_proto_rawDescData = protoimpl.X.CompressGZIP(file_manager_interfacedefinition_proto_rawDescData)
	})
	return file_manager_interfacedefinition_proto_rawDescData
}

var file_manager_interfacedefinition_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_manager_interfacedefinition_proto_msgTypes = make([]protoimpl.MessageInfo, 20)
var file_manager_interfacedefinition_proto_goTypes = []any{
	(WithConfig)(0),                              // 0: manager.WithConfig
	(*EnumValDesc)(nil),                          // 1: manager.EnumValDesc
	(*RefSchema)(nil),                            // 2: manager.RefSchema
	(*TextDescExample)(nil),                      // 3: manager.TextDescExample
	(*BodyData)(nil),                             // 4: manager.BodyData
	(*ResponseData)(nil),                         // 5: manager.ResponseData
	(*Schema)(nil),                               // 6: manager.Schema
	(*InterfaceSchema)(nil),                      // 7: manager.InterfaceSchema
	(*Document)(nil),                             // 8: manager.Document
	(*InterfaceDocument)(nil),                    // 9: manager.InterfaceDocument
	(*InputParameter)(nil),                       // 10: manager.InputParameter
	(*OutputParameter)(nil),                      // 11: manager.OutputParameter
	(*InterfaceConfig)(nil),                      // 12: manager.InterfaceConfig
	(*InterfaceCase)(nil),                        // 13: manager.InterfaceCase
	(*SearchInterfaceDocumentReferenceItem)(nil), // 14: manager.SearchInterfaceDocumentReferenceItem
	(*UpdateInterfaceCoverageTaskInfo)(nil),      // 15: manager.UpdateInterfaceCoverageTaskInfo
	(*UpdateInterfaceDefinitionTaskInfo)(nil),    // 16: manager.UpdateInterfaceDefinitionTaskInfo
	(*SearchInterfaceCaseReferenceItem)(nil),     // 17: manager.SearchInterfaceCaseReferenceItem
	(*InterfaceCoverageData)(nil),                // 18: manager.InterfaceCoverageData
	nil,                                          // 19: manager.Schema.PropertiesEntry
	nil,                                          // 20: manager.Document.ResponsesEntry
	(*structpb.Value)(nil),                       // 21: google.protobuf.Value
	(*CategoryNode)(nil),                         // 22: manager.CategoryNode
	(CommonState)(0),                             // 23: manager.CommonState
	(ExecutionMode)(0),                           // 24: manager.ExecutionMode
	(VariableSource)(0),                          // 25: manager.VariableSource
	(*VariableManual)(nil),                       // 26: manager.VariableManual
	(*VariableExport)(nil),                       // 27: manager.VariableExport
	(*VariableEnvironment)(nil),                  // 28: manager.VariableEnvironment
	(*VariableFunction)(nil),                     // 29: manager.VariableFunction
	(ResponseSource)(0),                          // 30: manager.ResponseSource
	(*VariableHeader)(nil),                       // 31: manager.VariableHeader
	(*VariableBody)(nil),                         // 32: manager.VariableBody
	(ResourceState)(0),                           // 33: manager.ResourceState
	(*AccountConfig)(nil),                        // 34: manager.AccountConfig
	(*pb.GetCaseLatestRecordResponse_RecordCaseRecord)(nil), // 35: reporter.GetCaseLatestRecordResponse.RecordCaseRecord
	(*Node)(nil),  // 36: manager.Node
	(*Edge)(nil),  // 37: manager.Edge
	(*Combo)(nil), // 38: manager.Combo
}
var file_manager_interfacedefinition_proto_depIdxs = []int32{
	21, // 0: manager.EnumValDesc.value:type_name -> google.protobuf.Value
	22, // 1: manager.RefSchema.categoryPath:type_name -> manager.CategoryNode
	6,  // 2: manager.BodyData.form:type_name -> manager.Schema
	6,  // 3: manager.BodyData.json:type_name -> manager.Schema
	3,  // 4: manager.BodyData.text:type_name -> manager.TextDescExample
	6,  // 5: manager.ResponseData.headers:type_name -> manager.Schema
	4,  // 6: manager.ResponseData.body:type_name -> manager.BodyData
	21, // 7: manager.Schema.default:type_name -> google.protobuf.Value
	21, // 8: manager.Schema.example:type_name -> google.protobuf.Value
	21, // 9: manager.Schema.enum:type_name -> google.protobuf.Value
	1,  // 10: manager.Schema.enums:type_name -> manager.EnumValDesc
	6,  // 11: manager.Schema.items:type_name -> manager.Schema
	2,  // 12: manager.Schema.ref:type_name -> manager.RefSchema
	19, // 13: manager.Schema.properties:type_name -> manager.Schema.PropertiesEntry
	6,  // 14: manager.InterfaceSchema.data:type_name -> manager.Schema
	6,  // 15: manager.Document.headers:type_name -> manager.Schema
	6,  // 16: manager.Document.path_params:type_name -> manager.Schema
	6,  // 17: manager.Document.query_params:type_name -> manager.Schema
	4,  // 18: manager.Document.body:type_name -> manager.BodyData
	20, // 19: manager.Document.responses:type_name -> manager.Document.ResponsesEntry
	23, // 20: manager.InterfaceDocument.state:type_name -> manager.CommonState
	24, // 21: manager.InterfaceDocument.case_execution_mode:type_name -> manager.ExecutionMode
	8,  // 22: manager.InterfaceDocument.data:type_name -> manager.Document
	25, // 23: manager.InputParameter.source:type_name -> manager.VariableSource
	26, // 24: manager.InputParameter.manual:type_name -> manager.VariableManual
	27, // 25: manager.InputParameter.export:type_name -> manager.VariableExport
	28, // 26: manager.InputParameter.environment:type_name -> manager.VariableEnvironment
	29, // 27: manager.InputParameter.function:type_name -> manager.VariableFunction
	30, // 28: manager.OutputParameter.source:type_name -> manager.ResponseSource
	31, // 29: manager.OutputParameter.headers:type_name -> manager.VariableHeader
	32, // 30: manager.OutputParameter.body:type_name -> manager.VariableBody
	8,  // 31: manager.InterfaceConfig.data:type_name -> manager.Document
	10, // 32: manager.InterfaceConfig.input_parameters:type_name -> manager.InputParameter
	11, // 33: manager.InterfaceConfig.output_parameters:type_name -> manager.OutputParameter
	33, // 34: manager.InterfaceCase.state:type_name -> manager.ResourceState
	34, // 35: manager.InterfaceCase.account_config:type_name -> manager.AccountConfig
	35, // 36: manager.InterfaceCase.latest_record:type_name -> reporter.GetCaseLatestRecordResponse.RecordCaseRecord
	36, // 37: manager.InterfaceCase.nodes:type_name -> manager.Node
	37, // 38: manager.InterfaceCase.edges:type_name -> manager.Edge
	38, // 39: manager.InterfaceCase.combos:type_name -> manager.Combo
	23, // 40: manager.SearchInterfaceDocumentReferenceItem.state:type_name -> manager.CommonState
	23, // 41: manager.SearchInterfaceDocumentReferenceItem.reference_state:type_name -> manager.CommonState
	23, // 42: manager.SearchInterfaceCaseReferenceItem.state:type_name -> manager.CommonState
	6,  // 43: manager.Schema.PropertiesEntry.value:type_name -> manager.Schema
	5,  // 44: manager.Document.ResponsesEntry.value:type_name -> manager.ResponseData
	45, // [45:45] is the sub-list for method output_type
	45, // [45:45] is the sub-list for method input_type
	45, // [45:45] is the sub-list for extension type_name
	45, // [45:45] is the sub-list for extension extendee
	0,  // [0:45] is the sub-list for field type_name
}

func init() { file_manager_interfacedefinition_proto_init() }
func file_manager_interfacedefinition_proto_init() {
	if File_manager_interfacedefinition_proto != nil {
		return
	}
	file_manager_base_proto_init()
	file_manager_category_proto_init()
	file_manager_component_proto_init()
	file_manager_element_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_manager_interfacedefinition_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   20,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_manager_interfacedefinition_proto_goTypes,
		DependencyIndexes: file_manager_interfacedefinition_proto_depIdxs,
		EnumInfos:         file_manager_interfacedefinition_proto_enumTypes,
		MessageInfos:      file_manager_interfacedefinition_proto_msgTypes,
	}.Build()
	File_manager_interfacedefinition_proto = out.File
	file_manager_interfacedefinition_proto_rawDesc = nil
	file_manager_interfacedefinition_proto_goTypes = nil
	file_manager_interfacedefinition_proto_depIdxs = nil
}
