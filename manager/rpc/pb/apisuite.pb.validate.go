// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: manager/apisuite.proto

package pb

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on ApiSuite with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ApiSuite) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ApiSuite with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ApiSuiteMultiError, or nil
// if none found.
func (m *ApiSuite) ValidateAll() error {
	return m.validate(true)
}

func (m *ApiSuite) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ProjectId

	// no validation rules for CategoryId

	// no validation rules for SuiteId

	// no validation rules for Name

	// no validation rules for Description

	// no validation rules for Priority

	// no validation rules for State

	// no validation rules for CaseExecutionMode

	// no validation rules for MaintainedBy

	// no validation rules for CreatedBy

	// no validation rules for UpdatedBy

	// no validation rules for CreatedAt

	// no validation rules for UpdatedAt

	if len(errors) > 0 {
		return ApiSuiteMultiError(errors)
	}

	return nil
}

// ApiSuiteMultiError is an error wrapping multiple validation errors returned
// by ApiSuite.ValidateAll() if the designated constraints aren't met.
type ApiSuiteMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ApiSuiteMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ApiSuiteMultiError) AllErrors() []error { return m }

// ApiSuiteValidationError is the validation error returned by
// ApiSuite.Validate if the designated constraints aren't met.
type ApiSuiteValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ApiSuiteValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ApiSuiteValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ApiSuiteValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ApiSuiteValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ApiSuiteValidationError) ErrorName() string { return "ApiSuiteValidationError" }

// Error satisfies the builtin error interface
func (e ApiSuiteValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sApiSuite.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ApiSuiteValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ApiSuiteValidationError{}

// Validate checks the field values on SearchApiSuiteReferenceItem with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SearchApiSuiteReferenceItem) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SearchApiSuiteReferenceItem with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SearchApiSuiteReferenceItemMultiError, or nil if none found.
func (m *SearchApiSuiteReferenceItem) ValidateAll() error {
	return m.validate(true)
}

func (m *SearchApiSuiteReferenceItem) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ProjectId

	// no validation rules for SuiteId

	// no validation rules for ReferenceType

	// no validation rules for ReferenceId

	// no validation rules for Name

	// no validation rules for Description

	// no validation rules for Priority

	// no validation rules for State

	// no validation rules for ReferenceState

	// no validation rules for MaintainedBy

	// no validation rules for CreatedBy

	// no validation rules for UpdatedBy

	// no validation rules for CreatedAt

	// no validation rules for UpdatedAt

	if len(errors) > 0 {
		return SearchApiSuiteReferenceItemMultiError(errors)
	}

	return nil
}

// SearchApiSuiteReferenceItemMultiError is an error wrapping multiple
// validation errors returned by SearchApiSuiteReferenceItem.ValidateAll() if
// the designated constraints aren't met.
type SearchApiSuiteReferenceItemMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SearchApiSuiteReferenceItemMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SearchApiSuiteReferenceItemMultiError) AllErrors() []error { return m }

// SearchApiSuiteReferenceItemValidationError is the validation error returned
// by SearchApiSuiteReferenceItem.Validate if the designated constraints
// aren't met.
type SearchApiSuiteReferenceItemValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SearchApiSuiteReferenceItemValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SearchApiSuiteReferenceItemValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SearchApiSuiteReferenceItemValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SearchApiSuiteReferenceItemValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SearchApiSuiteReferenceItemValidationError) ErrorName() string {
	return "SearchApiSuiteReferenceItemValidationError"
}

// Error satisfies the builtin error interface
func (e SearchApiSuiteReferenceItemValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSearchApiSuiteReferenceItem.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SearchApiSuiteReferenceItemValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SearchApiSuiteReferenceItemValidationError{}

// Validate checks the field values on SearchCaseInApiSuiteItem with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SearchCaseInApiSuiteItem) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SearchCaseInApiSuiteItem with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SearchCaseInApiSuiteItemMultiError, or nil if none found.
func (m *SearchCaseInApiSuiteItem) ValidateAll() error {
	return m.validate(true)
}

func (m *SearchCaseInApiSuiteItem) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ProjectId

	// no validation rules for CategoryId

	// no validation rules for DocumentId

	if _, ok := _SearchCaseInApiSuiteItem_CaseType_InLookup[m.GetCaseType()]; !ok {
		err := SearchCaseInApiSuiteItemValidationError{
			field:  "CaseType",
			reason: "value must be in list [API_CASE INTERFACE_CASE]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for CaseId

	// no validation rules for Name

	// no validation rules for Description

	// no validation rules for Priority

	// no validation rules for State

	if all {
		switch v := interface{}(m.GetAccountConfig()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SearchCaseInApiSuiteItemValidationError{
					field:  "AccountConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SearchCaseInApiSuiteItemValidationError{
					field:  "AccountConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAccountConfig()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SearchCaseInApiSuiteItemValidationError{
				field:  "AccountConfig",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Version

	// no validation rules for MaintainedBy

	// no validation rules for CreatedBy

	// no validation rules for UpdatedBy

	// no validation rules for CreatedAt

	// no validation rules for UpdatedAt

	if len(errors) > 0 {
		return SearchCaseInApiSuiteItemMultiError(errors)
	}

	return nil
}

// SearchCaseInApiSuiteItemMultiError is an error wrapping multiple validation
// errors returned by SearchCaseInApiSuiteItem.ValidateAll() if the designated
// constraints aren't met.
type SearchCaseInApiSuiteItemMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SearchCaseInApiSuiteItemMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SearchCaseInApiSuiteItemMultiError) AllErrors() []error { return m }

// SearchCaseInApiSuiteItemValidationError is the validation error returned by
// SearchCaseInApiSuiteItem.Validate if the designated constraints aren't met.
type SearchCaseInApiSuiteItemValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SearchCaseInApiSuiteItemValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SearchCaseInApiSuiteItemValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SearchCaseInApiSuiteItemValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SearchCaseInApiSuiteItemValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SearchCaseInApiSuiteItemValidationError) ErrorName() string {
	return "SearchCaseInApiSuiteItemValidationError"
}

// Error satisfies the builtin error interface
func (e SearchCaseInApiSuiteItemValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSearchCaseInApiSuiteItem.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SearchCaseInApiSuiteItemValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SearchCaseInApiSuiteItemValidationError{}

var _SearchCaseInApiSuiteItem_CaseType_InLookup = map[string]struct{}{
	"API_CASE":       {},
	"INTERFACE_CASE": {},
}

// Validate checks the field values on CaseTypeId with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *CaseTypeId) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CaseTypeId with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in CaseTypeIdMultiError, or
// nil if none found.
func (m *CaseTypeId) ValidateAll() error {
	return m.validate(true)
}

func (m *CaseTypeId) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if _, ok := _CaseTypeId_CaseType_InLookup[m.GetCaseType()]; !ok {
		err := CaseTypeIdValidationError{
			field:  "CaseType",
			reason: "value must be in list [API_CASE INTERFACE_CASE]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for CaseId

	if len(errors) > 0 {
		return CaseTypeIdMultiError(errors)
	}

	return nil
}

// CaseTypeIdMultiError is an error wrapping multiple validation errors
// returned by CaseTypeId.ValidateAll() if the designated constraints aren't met.
type CaseTypeIdMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CaseTypeIdMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CaseTypeIdMultiError) AllErrors() []error { return m }

// CaseTypeIdValidationError is the validation error returned by
// CaseTypeId.Validate if the designated constraints aren't met.
type CaseTypeIdValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CaseTypeIdValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CaseTypeIdValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CaseTypeIdValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CaseTypeIdValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CaseTypeIdValidationError) ErrorName() string { return "CaseTypeIdValidationError" }

// Error satisfies the builtin error interface
func (e CaseTypeIdValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCaseTypeId.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CaseTypeIdValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CaseTypeIdValidationError{}

var _CaseTypeId_CaseType_InLookup = map[string]struct{}{
	"API_CASE":       {},
	"INTERFACE_CASE": {},
}
