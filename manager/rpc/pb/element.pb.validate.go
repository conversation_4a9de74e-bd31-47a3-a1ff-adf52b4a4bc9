// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: manager/element.proto

package pb

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on Node with the rules defined in the proto
// definition for this message. If any rules are violated, the first error
// encountered is returned, or nil if there are no violations.
func (m *Node) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Node with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in NodeMultiError, or nil if none found.
func (m *Node) ValidateAll() error {
	return m.validate(true)
}

func (m *Node) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for Type

	// no validation rules for Label

	// no validation rules for ItemType

	// no validation rules for Width

	// no validation rules for Height

	// no validation rules for X

	// no validation rules for Y

	for idx, item := range m.GetAnchorPoints() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, NodeValidationError{
						field:  fmt.Sprintf("AnchorPoints[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, NodeValidationError{
						field:  fmt.Sprintf("AnchorPoints[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return NodeValidationError{
					field:  fmt.Sprintf("AnchorPoints[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for Icon

	if all {
		switch v := interface{}(m.GetStyle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, NodeValidationError{
					field:  "Style",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, NodeValidationError{
					field:  "Style",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStyle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return NodeValidationError{
				field:  "Style",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetLabelCfg()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, NodeValidationError{
					field:  "LabelCfg",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, NodeValidationError{
					field:  "LabelCfg",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLabelCfg()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return NodeValidationError{
				field:  "LabelCfg",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ComboId

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, NodeValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, NodeValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return NodeValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Order

	// no validation rules for LayoutOrder

	if len(errors) > 0 {
		return NodeMultiError(errors)
	}

	return nil
}

// NodeMultiError is an error wrapping multiple validation errors returned by
// Node.ValidateAll() if the designated constraints aren't met.
type NodeMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m NodeMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m NodeMultiError) AllErrors() []error { return m }

// NodeValidationError is the validation error returned by Node.Validate if the
// designated constraints aren't met.
type NodeValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e NodeValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e NodeValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e NodeValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e NodeValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e NodeValidationError) ErrorName() string { return "NodeValidationError" }

// Error satisfies the builtin error interface
func (e NodeValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sNode.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = NodeValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = NodeValidationError{}

// Validate checks the field values on Edge with the rules defined in the proto
// definition for this message. If any rules are violated, the first error
// encountered is returned, or nil if there are no violations.
func (m *Edge) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Edge with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in EdgeMultiError, or nil if none found.
func (m *Edge) ValidateAll() error {
	return m.validate(true)
}

func (m *Edge) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for Type

	// no validation rules for Label

	// no validation rules for Source

	// no validation rules for SourceAnchor

	// no validation rules for Target

	// no validation rules for TargetAnchor

	// no validation rules for LineAppendWidth

	// no validation rules for Clazz

	if all {
		switch v := interface{}(m.GetAttrs()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, EdgeValidationError{
					field:  "Attrs",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, EdgeValidationError{
					field:  "Attrs",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAttrs()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return EdgeValidationError{
				field:  "Attrs",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetStyle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, EdgeValidationError{
					field:  "Style",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, EdgeValidationError{
					field:  "Style",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStyle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return EdgeValidationError{
				field:  "Style",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetLabelCfg()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, EdgeValidationError{
					field:  "LabelCfg",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, EdgeValidationError{
					field:  "LabelCfg",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLabelCfg()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return EdgeValidationError{
				field:  "LabelCfg",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetStartPoint()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, EdgeValidationError{
					field:  "StartPoint",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, EdgeValidationError{
					field:  "StartPoint",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStartPoint()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return EdgeValidationError{
				field:  "StartPoint",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetEndPoint()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, EdgeValidationError{
					field:  "EndPoint",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, EdgeValidationError{
					field:  "EndPoint",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetEndPoint()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return EdgeValidationError{
				field:  "EndPoint",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return EdgeMultiError(errors)
	}

	return nil
}

// EdgeMultiError is an error wrapping multiple validation errors returned by
// Edge.ValidateAll() if the designated constraints aren't met.
type EdgeMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m EdgeMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m EdgeMultiError) AllErrors() []error { return m }

// EdgeValidationError is the validation error returned by Edge.Validate if the
// designated constraints aren't met.
type EdgeValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e EdgeValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e EdgeValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e EdgeValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e EdgeValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e EdgeValidationError) ErrorName() string { return "EdgeValidationError" }

// Error satisfies the builtin error interface
func (e EdgeValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sEdge.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = EdgeValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = EdgeValidationError{}

// Validate checks the field values on Combo with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Combo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Combo with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in ComboMultiError, or nil if none found.
func (m *Combo) ValidateAll() error {
	return m.validate(true)
}

func (m *Combo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for Type

	// no validation rules for Label

	// no validation rules for ItemType

	// no validation rules for X

	// no validation rules for Y

	for idx, item := range m.GetAnchorPoints() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ComboValidationError{
						field:  fmt.Sprintf("AnchorPoints[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ComboValidationError{
						field:  fmt.Sprintf("AnchorPoints[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ComboValidationError{
					field:  fmt.Sprintf("AnchorPoints[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for Icon

	if all {
		switch v := interface{}(m.GetStyle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ComboValidationError{
					field:  "Style",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ComboValidationError{
					field:  "Style",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStyle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ComboValidationError{
				field:  "Style",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetLabelCfg()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ComboValidationError{
					field:  "LabelCfg",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ComboValidationError{
					field:  "LabelCfg",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLabelCfg()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ComboValidationError{
				field:  "LabelCfg",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Depth

	// no validation rules for Collapsed

	for idx, item := range m.GetChildren() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ComboValidationError{
						field:  fmt.Sprintf("Children[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ComboValidationError{
						field:  fmt.Sprintf("Children[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ComboValidationError{
					field:  fmt.Sprintf("Children[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ComboValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ComboValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ComboValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ComboMultiError(errors)
	}

	return nil
}

// ComboMultiError is an error wrapping multiple validation errors returned by
// Combo.ValidateAll() if the designated constraints aren't met.
type ComboMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ComboMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ComboMultiError) AllErrors() []error { return m }

// ComboValidationError is the validation error returned by Combo.Validate if
// the designated constraints aren't met.
type ComboValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ComboValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ComboValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ComboValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ComboValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ComboValidationError) ErrorName() string { return "ComboValidationError" }

// Error satisfies the builtin error interface
func (e ComboValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCombo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ComboValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ComboValidationError{}

// Validate checks the field values on ListFloat with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ListFloat) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListFloat with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ListFloatMultiError, or nil
// if none found.
func (m *ListFloat) ValidateAll() error {
	return m.validate(true)
}

func (m *ListFloat) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return ListFloatMultiError(errors)
	}

	return nil
}

// ListFloatMultiError is an error wrapping multiple validation errors returned
// by ListFloat.ValidateAll() if the designated constraints aren't met.
type ListFloatMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListFloatMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListFloatMultiError) AllErrors() []error { return m }

// ListFloatValidationError is the validation error returned by
// ListFloat.Validate if the designated constraints aren't met.
type ListFloatValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListFloatValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListFloatValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListFloatValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListFloatValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListFloatValidationError) ErrorName() string { return "ListFloatValidationError" }

// Error satisfies the builtin error interface
func (e ListFloatValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListFloat.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListFloatValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListFloatValidationError{}

// Validate checks the field values on Combo_ComboChild with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *Combo_ComboChild) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Combo_ComboChild with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// Combo_ComboChildMultiError, or nil if none found.
func (m *Combo_ComboChild) ValidateAll() error {
	return m.validate(true)
}

func (m *Combo_ComboChild) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for ComboId

	// no validation rules for ItemType

	// no validation rules for Depth

	if len(errors) > 0 {
		return Combo_ComboChildMultiError(errors)
	}

	return nil
}

// Combo_ComboChildMultiError is an error wrapping multiple validation errors
// returned by Combo_ComboChild.ValidateAll() if the designated constraints
// aren't met.
type Combo_ComboChildMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m Combo_ComboChildMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m Combo_ComboChildMultiError) AllErrors() []error { return m }

// Combo_ComboChildValidationError is the validation error returned by
// Combo_ComboChild.Validate if the designated constraints aren't met.
type Combo_ComboChildValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e Combo_ComboChildValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e Combo_ComboChildValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e Combo_ComboChildValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e Combo_ComboChildValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e Combo_ComboChildValidationError) ErrorName() string { return "Combo_ComboChildValidationError" }

// Error satisfies the builtin error interface
func (e Combo_ComboChildValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCombo_ComboChild.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = Combo_ComboChildValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = Combo_ComboChildValidationError{}
