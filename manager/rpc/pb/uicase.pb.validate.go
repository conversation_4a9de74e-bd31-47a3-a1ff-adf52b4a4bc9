// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: manager/uicase.proto

package pb

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on UiCase with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *UiCase) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UiCase with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in UiCaseMultiError, or nil if none found.
func (m *UiCase) ValidateAll() error {
	return m.validate(true)
}

func (m *UiCase) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Path

	// no validation rules for ParentPath

	// no validation rules for Name

	// no validation rules for Alias

	// no validation rules for Type

	// no validation rules for State

	// no validation rules for Selected

	// no validation rules for CreatedBy

	// no validation rules for UpdatedBy

	// no validation rules for CreatedAt

	// no validation rules for UpdatedAt

	for idx, item := range m.GetChildren() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, UiCaseValidationError{
						field:  fmt.Sprintf("Children[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, UiCaseValidationError{
						field:  fmt.Sprintf("Children[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return UiCaseValidationError{
					field:  fmt.Sprintf("Children[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return UiCaseMultiError(errors)
	}

	return nil
}

// UiCaseMultiError is an error wrapping multiple validation errors returned by
// UiCase.ValidateAll() if the designated constraints aren't met.
type UiCaseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UiCaseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UiCaseMultiError) AllErrors() []error { return m }

// UiCaseValidationError is the validation error returned by UiCase.Validate if
// the designated constraints aren't met.
type UiCaseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UiCaseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UiCaseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UiCaseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UiCaseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UiCaseValidationError) ErrorName() string { return "UiCaseValidationError" }

// Error satisfies the builtin error interface
func (e UiCaseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUiCase.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UiCaseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UiCaseValidationError{}
