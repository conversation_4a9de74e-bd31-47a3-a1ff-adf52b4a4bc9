// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.1
// 	protoc        v5.29.2
// source: manager/uiplan.proto

package pb

import (
	reflect "reflect"
	sync "sync"

	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"

	pb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type UiPlan struct {
	state                protoimpl.MessageState `protogen:"open.v1"`
	ProjectId            string                 `protobuf:"bytes,1,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`                                         // 项目ID
	PlanId               string                 `protobuf:"bytes,2,opt,name=plan_id,json=planId,proto3" json:"plan_id,omitempty"`                                                  // 计划ID
	Name                 string                 `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`                                                                    // 计划名称
	Description          string                 `protobuf:"bytes,4,opt,name=description,proto3" json:"description,omitempty"`                                                      // 描述
	Type                 pb.TriggerMode         `protobuf:"varint,5,opt,name=type,proto3,enum=common.TriggerMode" json:"type,omitempty"`                                           // 计划类型（手动、定时、接口）
	CronExpression       string                 `protobuf:"bytes,6,opt,name=cron_expression,json=cronExpression,proto3" json:"cron_expression,omitempty"`                          // 定时触发计划的Cron表达式
	PriorityType         pb.PriorityType        `protobuf:"varint,7,opt,name=priority_type,json=priorityType,proto3,enum=common.PriorityType" json:"priority_type,omitempty"`      // 优先级
	GitConfigId          string                 `protobuf:"bytes,8,opt,name=git_config_id,json=gitConfigId,proto3" json:"git_config_id,omitempty"`                                 // Git配置ID
	ExecutionMode        ExecutionMode          `protobuf:"varint,9,opt,name=execution_mode,json=executionMode,proto3,enum=manager.ExecutionMode" json:"execution_mode,omitempty"` // 执行方式（并行、串行）
	DeviceType           pb.DeviceType          `protobuf:"varint,10,opt,name=device_type,json=deviceType,proto3,enum=common.DeviceType" json:"device_type,omitempty"`             // 设备类型（真机、云手机）
	PlatformType         pb.PlatformType        `protobuf:"varint,11,opt,name=platform_type,json=platformType,proto3,enum=common.PlatformType" json:"platform_type,omitempty"`     // 平台类型（Android、iOS）
	PackageName          string                 `protobuf:"bytes,12,opt,name=package_name,json=packageName,proto3" json:"package_name,omitempty"`                                  // 包名，用于启动APP
	CallbackUrl          string                 `protobuf:"bytes,13,opt,name=callback_url,json=callbackUrl,proto3" json:"callback_url,omitempty"`                                  // 回调地址
	AppDownloadLink      string                 `protobuf:"bytes,14,opt,name=app_download_link,json=appDownloadLink,proto3" json:"app_download_link,omitempty"`                    // APP下载地址
	AppVersion           string                 `protobuf:"bytes,15,opt,name=app_version,json=appVersion,proto3" json:"app_version,omitempty"`                                     // APP版本
	AppName              string                 `protobuf:"bytes,16,opt,name=app_name,json=appName,proto3" json:"app_name,omitempty"`                                              // 应用名称
	TestLanguage         pb.TestLanguage        `protobuf:"varint,17,opt,name=test_language,json=testLanguage,proto3,enum=common.TestLanguage" json:"test_language,omitempty"`     // 测试语言
	TestLanguageVersion  string                 `protobuf:"bytes,18,opt,name=test_language_version,json=testLanguageVersion,proto3" json:"test_language_version,omitempty"`        // 测试语言版本
	TestFramework        pb.TestFramework       `protobuf:"varint,19,opt,name=test_framework,json=testFramework,proto3,enum=common.TestFramework" json:"test_framework,omitempty"` // 测试框架
	TestArgs             []string               `protobuf:"bytes,20,rep,name=test_args,json=testArgs,proto3" json:"test_args,omitempty"`                                           // 附加参数
	ExecutionEnvironment string                 `protobuf:"bytes,21,opt,name=execution_environment,json=executionEnvironment,proto3" json:"execution_environment,omitempty"`       // 执行环境
	FailRetry            pb.FailRetry           `protobuf:"varint,22,opt,name=fail_retry,json=failRetry,proto3,enum=common.FailRetry" json:"fail_retry,omitempty"`                 // 失败重试（0次、1次、2次）、
	State                CommonState            `protobuf:"varint,23,opt,name=state,proto3,enum=manager.CommonState" json:"state,omitempty"`                                       // 状态
	MaintainedBy         string                 `protobuf:"bytes,24,opt,name=maintained_by,json=maintainedBy,proto3" json:"maintained_by,omitempty"`                               // 维护者
	CategoryId           string                 `protobuf:"bytes,25,opt,name=category_id,json=categoryId,proto3" json:"category_id,omitempty"`                                     // 分类ID
	Devices              []string               `protobuf:"bytes,26,rep,name=devices,proto3" json:"devices,omitempty"`                                                             // 设备列表
	Together             bool                   `protobuf:"varint,27,opt,name=together,proto3" json:"together,omitempty"`                                                          // 选择的设备是否一起执行
	CreatedBy            string                 `protobuf:"bytes,96,opt,name=created_by,json=createdBy,proto3" json:"created_by,omitempty"`                                        // 创建者
	UpdatedBy            string                 `protobuf:"bytes,97,opt,name=updated_by,json=updatedBy,proto3" json:"updated_by,omitempty"`                                        // 更新者
	CreatedAt            int64                  `protobuf:"varint,98,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`                                       // 创建时间
	UpdatedAt            int64                  `protobuf:"varint,99,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`                                       // 更新时间
	unknownFields        protoimpl.UnknownFields
	sizeCache            protoimpl.SizeCache
}

func (x *UiPlan) Reset() {
	*x = UiPlan{}
	mi := &file_manager_uiplan_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UiPlan) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UiPlan) ProtoMessage() {}

func (x *UiPlan) ProtoReflect() protoreflect.Message {
	mi := &file_manager_uiplan_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UiPlan.ProtoReflect.Descriptor instead.
func (*UiPlan) Descriptor() ([]byte, []int) {
	return file_manager_uiplan_proto_rawDescGZIP(), []int{0}
}

func (x *UiPlan) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *UiPlan) GetPlanId() string {
	if x != nil {
		return x.PlanId
	}
	return ""
}

func (x *UiPlan) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *UiPlan) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *UiPlan) GetType() pb.TriggerMode {
	if x != nil {
		return x.Type
	}
	return pb.TriggerMode(0)
}

func (x *UiPlan) GetCronExpression() string {
	if x != nil {
		return x.CronExpression
	}
	return ""
}

func (x *UiPlan) GetPriorityType() pb.PriorityType {
	if x != nil {
		return x.PriorityType
	}
	return pb.PriorityType(0)
}

func (x *UiPlan) GetGitConfigId() string {
	if x != nil {
		return x.GitConfigId
	}
	return ""
}

func (x *UiPlan) GetExecutionMode() ExecutionMode {
	if x != nil {
		return x.ExecutionMode
	}
	return ExecutionMode_EM_NULL
}

func (x *UiPlan) GetDeviceType() pb.DeviceType {
	if x != nil {
		return x.DeviceType
	}
	return pb.DeviceType(0)
}

func (x *UiPlan) GetPlatformType() pb.PlatformType {
	if x != nil {
		return x.PlatformType
	}
	return pb.PlatformType(0)
}

func (x *UiPlan) GetPackageName() string {
	if x != nil {
		return x.PackageName
	}
	return ""
}

func (x *UiPlan) GetCallbackUrl() string {
	if x != nil {
		return x.CallbackUrl
	}
	return ""
}

func (x *UiPlan) GetAppDownloadLink() string {
	if x != nil {
		return x.AppDownloadLink
	}
	return ""
}

func (x *UiPlan) GetAppVersion() string {
	if x != nil {
		return x.AppVersion
	}
	return ""
}

func (x *UiPlan) GetAppName() string {
	if x != nil {
		return x.AppName
	}
	return ""
}

func (x *UiPlan) GetTestLanguage() pb.TestLanguage {
	if x != nil {
		return x.TestLanguage
	}
	return pb.TestLanguage(0)
}

func (x *UiPlan) GetTestLanguageVersion() string {
	if x != nil {
		return x.TestLanguageVersion
	}
	return ""
}

func (x *UiPlan) GetTestFramework() pb.TestFramework {
	if x != nil {
		return x.TestFramework
	}
	return pb.TestFramework(0)
}

func (x *UiPlan) GetTestArgs() []string {
	if x != nil {
		return x.TestArgs
	}
	return nil
}

func (x *UiPlan) GetExecutionEnvironment() string {
	if x != nil {
		return x.ExecutionEnvironment
	}
	return ""
}

func (x *UiPlan) GetFailRetry() pb.FailRetry {
	if x != nil {
		return x.FailRetry
	}
	return pb.FailRetry(0)
}

func (x *UiPlan) GetState() CommonState {
	if x != nil {
		return x.State
	}
	return CommonState_CS_NULL
}

func (x *UiPlan) GetMaintainedBy() string {
	if x != nil {
		return x.MaintainedBy
	}
	return ""
}

func (x *UiPlan) GetCategoryId() string {
	if x != nil {
		return x.CategoryId
	}
	return ""
}

func (x *UiPlan) GetDevices() []string {
	if x != nil {
		return x.Devices
	}
	return nil
}

func (x *UiPlan) GetTogether() bool {
	if x != nil {
		return x.Together
	}
	return false
}

func (x *UiPlan) GetCreatedBy() string {
	if x != nil {
		return x.CreatedBy
	}
	return ""
}

func (x *UiPlan) GetUpdatedBy() string {
	if x != nil {
		return x.UpdatedBy
	}
	return ""
}

func (x *UiPlan) GetCreatedAt() int64 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

func (x *UiPlan) GetUpdatedAt() int64 {
	if x != nil {
		return x.UpdatedAt
	}
	return 0
}

type CasePathItem struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	CasePath      string                 `protobuf:"bytes,1,opt,name=CasePath,proto3" json:"CasePath,omitempty"` // 用例路径
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CasePathItem) Reset() {
	*x = CasePathItem{}
	mi := &file_manager_uiplan_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CasePathItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CasePathItem) ProtoMessage() {}

func (x *CasePathItem) ProtoReflect() protoreflect.Message {
	mi := &file_manager_uiplan_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CasePathItem.ProtoReflect.Descriptor instead.
func (*CasePathItem) Descriptor() ([]byte, []int) {
	return file_manager_uiplan_proto_rawDescGZIP(), []int{1}
}

func (x *CasePathItem) GetCasePath() string {
	if x != nil {
		return x.CasePath
	}
	return ""
}

type Path struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Path          string                 `protobuf:"bytes,1,opt,name=path,proto3" json:"path,omitempty"` // 路径
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Path) Reset() {
	*x = Path{}
	mi := &file_manager_uiplan_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Path) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Path) ProtoMessage() {}

func (x *Path) ProtoReflect() protoreflect.Message {
	mi := &file_manager_uiplan_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Path.ProtoReflect.Descriptor instead.
func (*Path) Descriptor() ([]byte, []int) {
	return file_manager_uiplan_proto_rawDescGZIP(), []int{2}
}

func (x *Path) GetPath() string {
	if x != nil {
		return x.Path
	}
	return ""
}

type UICaseTreeNode struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ProjectId     string                 `protobuf:"bytes,1,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`         // 项目ID
	GitConfigId   string                 `protobuf:"bytes,2,opt,name=git_config_id,json=gitConfigId,proto3" json:"git_config_id,omitempty"` // Git配置ID
	Path          string                 `protobuf:"bytes,3,opt,name=path,proto3" json:"path,omitempty"`                                    // 节点路径（相对于根路径）
	ParentPath    string                 `protobuf:"bytes,4,opt,name=parent_path,json=parentPath,proto3" json:"parent_path,omitempty"`      // 父节点路径
	Name          string                 `protobuf:"bytes,5,opt,name=name,proto3" json:"name,omitempty"`                                    // 节点名称
	Alias         string                 `protobuf:"bytes,6,opt,name=alias,proto3" json:"alias,omitempty"`                                  // 节点别名
	Type          string                 `protobuf:"bytes,7,opt,name=type,proto3" json:"type,omitempty"`                                    // 节点类型(目录、文件、包、模块、类、函数)
	Tags          []string               `protobuf:"bytes,8,rep,name=tags,proto3" json:"tags,omitempty"`                                    // 标签
	CreatedBy     string                 `protobuf:"bytes,9,opt,name=created_by,json=createdBy,proto3" json:"created_by,omitempty"`         // 创建者
	UpdatedBy     string                 `protobuf:"bytes,10,opt,name=updated_by,json=updatedBy,proto3" json:"updated_by,omitempty"`        // 更新者
	CreatedAt     int64                  `protobuf:"varint,11,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`       // 创建时间
	UpdatedAt     int64                  `protobuf:"varint,12,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`       // 更新时间
	Amount        int64                  `protobuf:"varint,13,opt,name=amount,proto3" json:"amount,omitempty"`                              // 叶子节点数量
	Children      []*UICaseTreeNode      `protobuf:"bytes,14,rep,name=children,proto3" json:"children,omitempty"`                           // 子节点
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UICaseTreeNode) Reset() {
	*x = UICaseTreeNode{}
	mi := &file_manager_uiplan_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UICaseTreeNode) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UICaseTreeNode) ProtoMessage() {}

func (x *UICaseTreeNode) ProtoReflect() protoreflect.Message {
	mi := &file_manager_uiplan_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UICaseTreeNode.ProtoReflect.Descriptor instead.
func (*UICaseTreeNode) Descriptor() ([]byte, []int) {
	return file_manager_uiplan_proto_rawDescGZIP(), []int{3}
}

func (x *UICaseTreeNode) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *UICaseTreeNode) GetGitConfigId() string {
	if x != nil {
		return x.GitConfigId
	}
	return ""
}

func (x *UICaseTreeNode) GetPath() string {
	if x != nil {
		return x.Path
	}
	return ""
}

func (x *UICaseTreeNode) GetParentPath() string {
	if x != nil {
		return x.ParentPath
	}
	return ""
}

func (x *UICaseTreeNode) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *UICaseTreeNode) GetAlias() string {
	if x != nil {
		return x.Alias
	}
	return ""
}

func (x *UICaseTreeNode) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *UICaseTreeNode) GetTags() []string {
	if x != nil {
		return x.Tags
	}
	return nil
}

func (x *UICaseTreeNode) GetCreatedBy() string {
	if x != nil {
		return x.CreatedBy
	}
	return ""
}

func (x *UICaseTreeNode) GetUpdatedBy() string {
	if x != nil {
		return x.UpdatedBy
	}
	return ""
}

func (x *UICaseTreeNode) GetCreatedAt() int64 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

func (x *UICaseTreeNode) GetUpdatedAt() int64 {
	if x != nil {
		return x.UpdatedAt
	}
	return 0
}

func (x *UICaseTreeNode) GetAmount() int64 {
	if x != nil {
		return x.Amount
	}
	return 0
}

func (x *UICaseTreeNode) GetChildren() []*UICaseTreeNode {
	if x != nil {
		return x.Children
	}
	return nil
}

type SearchCaseInUIPlanItem struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ProjectId     string                 `protobuf:"bytes,1,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`         // 项目ID
	GitConfigId   string                 `protobuf:"bytes,2,opt,name=git_config_id,json=gitConfigId,proto3" json:"git_config_id,omitempty"` // Git配置ID
	Path          string                 `protobuf:"bytes,3,opt,name=path,proto3" json:"path,omitempty"`                                    // 节点路径（相对于根路径）
	ParentPath    string                 `protobuf:"bytes,4,opt,name=parent_path,json=parentPath,proto3" json:"parent_path,omitempty"`      // 父节点路径
	Name          string                 `protobuf:"bytes,5,opt,name=name,proto3" json:"name,omitempty"`                                    // 节点名称
	Alias         string                 `protobuf:"bytes,6,opt,name=alias,proto3" json:"alias,omitempty"`                                  // 节点别名
	Type          string                 `protobuf:"bytes,7,opt,name=type,proto3" json:"type,omitempty"`                                    // 节点类型（目录、文件、包、模块、类、函数）
	Tags          []string               `protobuf:"bytes,8,rep,name=tags,proto3" json:"tags,omitempty"`                                    // 标签
	CreatedBy     string                 `protobuf:"bytes,9,opt,name=created_by,json=createdBy,proto3" json:"created_by,omitempty"`         // 创建者
	UpdatedBy     string                 `protobuf:"bytes,10,opt,name=updated_by,json=updatedBy,proto3" json:"updated_by,omitempty"`        // 更新者
	CreatedAt     int64                  `protobuf:"varint,11,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`       // 创建时间
	UpdatedAt     int64                  `protobuf:"varint,12,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`       // 更新时间
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SearchCaseInUIPlanItem) Reset() {
	*x = SearchCaseInUIPlanItem{}
	mi := &file_manager_uiplan_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SearchCaseInUIPlanItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchCaseInUIPlanItem) ProtoMessage() {}

func (x *SearchCaseInUIPlanItem) ProtoReflect() protoreflect.Message {
	mi := &file_manager_uiplan_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchCaseInUIPlanItem.ProtoReflect.Descriptor instead.
func (*SearchCaseInUIPlanItem) Descriptor() ([]byte, []int) {
	return file_manager_uiplan_proto_rawDescGZIP(), []int{4}
}

func (x *SearchCaseInUIPlanItem) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *SearchCaseInUIPlanItem) GetGitConfigId() string {
	if x != nil {
		return x.GitConfigId
	}
	return ""
}

func (x *SearchCaseInUIPlanItem) GetPath() string {
	if x != nil {
		return x.Path
	}
	return ""
}

func (x *SearchCaseInUIPlanItem) GetParentPath() string {
	if x != nil {
		return x.ParentPath
	}
	return ""
}

func (x *SearchCaseInUIPlanItem) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *SearchCaseInUIPlanItem) GetAlias() string {
	if x != nil {
		return x.Alias
	}
	return ""
}

func (x *SearchCaseInUIPlanItem) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *SearchCaseInUIPlanItem) GetTags() []string {
	if x != nil {
		return x.Tags
	}
	return nil
}

func (x *SearchCaseInUIPlanItem) GetCreatedBy() string {
	if x != nil {
		return x.CreatedBy
	}
	return ""
}

func (x *SearchCaseInUIPlanItem) GetUpdatedBy() string {
	if x != nil {
		return x.UpdatedBy
	}
	return ""
}

func (x *SearchCaseInUIPlanItem) GetCreatedAt() int64 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

func (x *SearchCaseInUIPlanItem) GetUpdatedAt() int64 {
	if x != nil {
		return x.UpdatedAt
	}
	return 0
}

type SearchCaseNotInUIPlanItem struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ProjectId     string                 `protobuf:"bytes,1,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`         // 项目ID
	GitConfigId   string                 `protobuf:"bytes,2,opt,name=git_config_id,json=gitConfigId,proto3" json:"git_config_id,omitempty"` // Git配置ID
	Path          string                 `protobuf:"bytes,3,opt,name=path,proto3" json:"path,omitempty"`                                    // 节点路径（相对于根路径）
	ParentPath    string                 `protobuf:"bytes,4,opt,name=parent_path,json=parentPath,proto3" json:"parent_path,omitempty"`      // 父节点路径
	Name          string                 `protobuf:"bytes,5,opt,name=name,proto3" json:"name,omitempty"`                                    // 节点名称
	Alias         string                 `protobuf:"bytes,6,opt,name=alias,proto3" json:"alias,omitempty"`                                  // 节点别名
	Type          string                 `protobuf:"bytes,7,opt,name=type,proto3" json:"type,omitempty"`                                    // 节点类型（目录、文件、包、模块、类、函数）
	Tags          []string               `protobuf:"bytes,8,rep,name=tags,proto3" json:"tags,omitempty"`                                    // 标签
	CreatedBy     string                 `protobuf:"bytes,9,opt,name=created_by,json=createdBy,proto3" json:"created_by,omitempty"`         // 创建者
	UpdatedBy     string                 `protobuf:"bytes,10,opt,name=updated_by,json=updatedBy,proto3" json:"updated_by,omitempty"`        // 更新者
	CreatedAt     int64                  `protobuf:"varint,11,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`       // 创建时间
	UpdatedAt     int64                  `protobuf:"varint,12,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`       // 更新时间
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SearchCaseNotInUIPlanItem) Reset() {
	*x = SearchCaseNotInUIPlanItem{}
	mi := &file_manager_uiplan_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SearchCaseNotInUIPlanItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchCaseNotInUIPlanItem) ProtoMessage() {}

func (x *SearchCaseNotInUIPlanItem) ProtoReflect() protoreflect.Message {
	mi := &file_manager_uiplan_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchCaseNotInUIPlanItem.ProtoReflect.Descriptor instead.
func (*SearchCaseNotInUIPlanItem) Descriptor() ([]byte, []int) {
	return file_manager_uiplan_proto_rawDescGZIP(), []int{5}
}

func (x *SearchCaseNotInUIPlanItem) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *SearchCaseNotInUIPlanItem) GetGitConfigId() string {
	if x != nil {
		return x.GitConfigId
	}
	return ""
}

func (x *SearchCaseNotInUIPlanItem) GetPath() string {
	if x != nil {
		return x.Path
	}
	return ""
}

func (x *SearchCaseNotInUIPlanItem) GetParentPath() string {
	if x != nil {
		return x.ParentPath
	}
	return ""
}

func (x *SearchCaseNotInUIPlanItem) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *SearchCaseNotInUIPlanItem) GetAlias() string {
	if x != nil {
		return x.Alias
	}
	return ""
}

func (x *SearchCaseNotInUIPlanItem) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *SearchCaseNotInUIPlanItem) GetTags() []string {
	if x != nil {
		return x.Tags
	}
	return nil
}

func (x *SearchCaseNotInUIPlanItem) GetCreatedBy() string {
	if x != nil {
		return x.CreatedBy
	}
	return ""
}

func (x *SearchCaseNotInUIPlanItem) GetUpdatedBy() string {
	if x != nil {
		return x.UpdatedBy
	}
	return ""
}

func (x *SearchCaseNotInUIPlanItem) GetCreatedAt() int64 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

func (x *SearchCaseNotInUIPlanItem) GetUpdatedAt() int64 {
	if x != nil {
		return x.UpdatedAt
	}
	return 0
}

var File_manager_uiplan_proto protoreflect.FileDescriptor

var file_manager_uiplan_proto_rawDesc = []byte{
	0x0a, 0x14, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2f, 0x75, 0x69, 0x70, 0x6c, 0x61, 0x6e,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x07, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x1a,
	0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61,
	0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x11, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x12, 0x6d, 0x61, 0x6e,
	0x61, 0x67, 0x65, 0x72, 0x2f, 0x62, 0x61, 0x73, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22,
	0xd9, 0x09, 0x0a, 0x06, 0x55, 0x69, 0x50, 0x6c, 0x61, 0x6e, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72,
	0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x70, 0x6c, 0x61,
	0x6e, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x70, 0x6c, 0x61, 0x6e,
	0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x27, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x13, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e,
	0x54, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x4d, 0x6f, 0x64, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70,
	0x65, 0x12, 0x27, 0x0a, 0x0f, 0x63, 0x72, 0x6f, 0x6e, 0x5f, 0x65, 0x78, 0x70, 0x72, 0x65, 0x73,
	0x73, 0x69, 0x6f, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x63, 0x72, 0x6f, 0x6e,
	0x45, 0x78, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x39, 0x0a, 0x0d, 0x70, 0x72,
	0x69, 0x6f, 0x72, 0x69, 0x74, 0x79, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x14, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x72, 0x69, 0x6f, 0x72,
	0x69, 0x74, 0x79, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0c, 0x70, 0x72, 0x69, 0x6f, 0x72, 0x69, 0x74,
	0x79, 0x54, 0x79, 0x70, 0x65, 0x12, 0x22, 0x0a, 0x0d, 0x67, 0x69, 0x74, 0x5f, 0x63, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x5f, 0x69, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x67, 0x69,
	0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x49, 0x64, 0x12, 0x3d, 0x0a, 0x0e, 0x65, 0x78, 0x65,
	0x63, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x16, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x45, 0x78, 0x65, 0x63,
	0x75, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x6f, 0x64, 0x65, 0x52, 0x0d, 0x65, 0x78, 0x65, 0x63, 0x75,
	0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x6f, 0x64, 0x65, 0x12, 0x33, 0x0a, 0x0b, 0x64, 0x65, 0x76, 0x69,
	0x63, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x12, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x54, 0x79, 0x70,
	0x65, 0x52, 0x0a, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x39, 0x0a,
	0x0d, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0b,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x6c,
	0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0c, 0x70, 0x6c, 0x61, 0x74,
	0x66, 0x6f, 0x72, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x70, 0x61, 0x63, 0x6b,
	0x61, 0x67, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b,
	0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x63,
	0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x0d, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0b, 0x63, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x55, 0x72, 0x6c, 0x12, 0x2a,
	0x0a, 0x11, 0x61, 0x70, 0x70, 0x5f, 0x64, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x5f, 0x6c,
	0x69, 0x6e, 0x6b, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x61, 0x70, 0x70, 0x44, 0x6f,
	0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x4c, 0x69, 0x6e, 0x6b, 0x12, 0x1f, 0x0a, 0x0b, 0x61, 0x70,
	0x70, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0a, 0x61, 0x70, 0x70, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x19, 0x0a, 0x08, 0x61,
	0x70, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x10, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61,
	0x70, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x39, 0x0a, 0x0d, 0x74, 0x65, 0x73, 0x74, 0x5f, 0x6c,
	0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x18, 0x11, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x4c, 0x61, 0x6e, 0x67, 0x75,
	0x61, 0x67, 0x65, 0x52, 0x0c, 0x74, 0x65, 0x73, 0x74, 0x4c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67,
	0x65, 0x12, 0x32, 0x0a, 0x15, 0x74, 0x65, 0x73, 0x74, 0x5f, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61,
	0x67, 0x65, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x12, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x13, 0x74, 0x65, 0x73, 0x74, 0x4c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x56, 0x65,
	0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x3c, 0x0a, 0x0e, 0x74, 0x65, 0x73, 0x74, 0x5f, 0x66, 0x72,
	0x61, 0x6d, 0x65, 0x77, 0x6f, 0x72, 0x6b, 0x18, 0x13, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x15, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x46, 0x72, 0x61, 0x6d, 0x65,
	0x77, 0x6f, 0x72, 0x6b, 0x52, 0x0d, 0x74, 0x65, 0x73, 0x74, 0x46, 0x72, 0x61, 0x6d, 0x65, 0x77,
	0x6f, 0x72, 0x6b, 0x12, 0x1b, 0x0a, 0x09, 0x74, 0x65, 0x73, 0x74, 0x5f, 0x61, 0x72, 0x67, 0x73,
	0x18, 0x14, 0x20, 0x03, 0x28, 0x09, 0x52, 0x08, 0x74, 0x65, 0x73, 0x74, 0x41, 0x72, 0x67, 0x73,
	0x12, 0x33, 0x0a, 0x15, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x65, 0x6e,
	0x76, 0x69, 0x72, 0x6f, 0x6e, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x15, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x14, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x45, 0x6e, 0x76, 0x69, 0x72, 0x6f,
	0x6e, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x30, 0x0a, 0x0a, 0x66, 0x61, 0x69, 0x6c, 0x5f, 0x72, 0x65,
	0x74, 0x72, 0x79, 0x18, 0x16, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x11, 0x2e, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2e, 0x46, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x74, 0x72, 0x79, 0x52, 0x09, 0x66, 0x61,
	0x69, 0x6c, 0x52, 0x65, 0x74, 0x72, 0x79, 0x12, 0x2a, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65,
	0x18, 0x17, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72,
	0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x05, 0x73, 0x74,
	0x61, 0x74, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x6d, 0x61, 0x69, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65,
	0x64, 0x5f, 0x62, 0x79, 0x18, 0x18, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x6d, 0x61, 0x69, 0x6e,
	0x74, 0x61, 0x69, 0x6e, 0x65, 0x64, 0x42, 0x79, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x61, 0x74, 0x65,
	0x67, 0x6f, 0x72, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x19, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63,
	0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x49, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x64, 0x65, 0x76,
	0x69, 0x63, 0x65, 0x73, 0x18, 0x1a, 0x20, 0x03, 0x28, 0x09, 0x52, 0x07, 0x64, 0x65, 0x76, 0x69,
	0x63, 0x65, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x74, 0x6f, 0x67, 0x65, 0x74, 0x68, 0x65, 0x72, 0x18,
	0x1b, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x74, 0x6f, 0x67, 0x65, 0x74, 0x68, 0x65, 0x72, 0x12,
	0x1d, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x62, 0x79, 0x18, 0x60, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x42, 0x79, 0x12, 0x1d,
	0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x62, 0x79, 0x18, 0x61, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x42, 0x79, 0x12, 0x1d, 0x0a,
	0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x62, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x1d, 0x0a, 0x0a,
	0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x63, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x22, 0x45, 0x0a, 0x0c, 0x43,
	0x61, 0x73, 0x65, 0x50, 0x61, 0x74, 0x68, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x35, 0x0a, 0x08, 0x43,
	0x61, 0x73, 0x65, 0x50, 0x61, 0x74, 0x68, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x19, 0xfa,
	0x42, 0x16, 0x72, 0x14, 0x32, 0x12, 0x28, 0x3f, 0x3a, 0x5e, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x70,
	0x61, 0x74, 0x68, 0x3a, 0x2e, 0x2b, 0x3f, 0x29, 0x52, 0x08, 0x43, 0x61, 0x73, 0x65, 0x50, 0x61,
	0x74, 0x68, 0x22, 0x1a, 0x0a, 0x04, 0x50, 0x61, 0x74, 0x68, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61,
	0x74, 0x68, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x70, 0x61, 0x74, 0x68, 0x22, 0xa3,
	0x03, 0x0a, 0x0e, 0x55, 0x49, 0x43, 0x61, 0x73, 0x65, 0x54, 0x72, 0x65, 0x65, 0x4e, 0x6f, 0x64,
	0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64,
	0x12, 0x22, 0x0a, 0x0d, 0x67, 0x69, 0x74, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x67, 0x69, 0x74, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x74, 0x68, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x70, 0x61, 0x74, 0x68, 0x12, 0x1f, 0x0a, 0x0b, 0x70, 0x61, 0x72, 0x65,
	0x6e, 0x74, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x70,
	0x61, 0x72, 0x65, 0x6e, 0x74, 0x50, 0x61, 0x74, 0x68, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a,
	0x05, 0x61, 0x6c, 0x69, 0x61, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x6c,
	0x69, 0x61, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x61, 0x67, 0x73, 0x18,
	0x08, 0x20, 0x03, 0x28, 0x09, 0x52, 0x04, 0x74, 0x61, 0x67, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x63,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x62, 0x79, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x42, 0x79, 0x12, 0x1d, 0x0a, 0x0a, 0x75, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x62, 0x79, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x42, 0x79, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x63,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x75, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e,
	0x74, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12,
	0x33, 0x0a, 0x08, 0x63, 0x68, 0x69, 0x6c, 0x64, 0x72, 0x65, 0x6e, 0x18, 0x0e, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x17, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x55, 0x49, 0x43, 0x61,
	0x73, 0x65, 0x54, 0x72, 0x65, 0x65, 0x4e, 0x6f, 0x64, 0x65, 0x52, 0x08, 0x63, 0x68, 0x69, 0x6c,
	0x64, 0x72, 0x65, 0x6e, 0x22, 0xde, 0x02, 0x0a, 0x16, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x43,
	0x61, 0x73, 0x65, 0x49, 0x6e, 0x55, 0x49, 0x50, 0x6c, 0x61, 0x6e, 0x49, 0x74, 0x65, 0x6d, 0x12,
	0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x12, 0x22,
	0x0a, 0x0d, 0x67, 0x69, 0x74, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x69, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x67, 0x69, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x74, 0x68, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x70, 0x61, 0x74, 0x68, 0x12, 0x1f, 0x0a, 0x0b, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74,
	0x5f, 0x70, 0x61, 0x74, 0x68, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x70, 0x61, 0x72,
	0x65, 0x6e, 0x74, 0x50, 0x61, 0x74, 0x68, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x61,
	0x6c, 0x69, 0x61, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x6c, 0x69, 0x61,
	0x73, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x61, 0x67, 0x73, 0x18, 0x08, 0x20,
	0x03, 0x28, 0x09, 0x52, 0x04, 0x74, 0x61, 0x67, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x64, 0x5f, 0x62, 0x79, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x42, 0x79, 0x12, 0x1d, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x64, 0x5f, 0x62, 0x79, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x75, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x64, 0x42, 0x79, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x63, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x64, 0x5f, 0x61, 0x74, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x64, 0x41, 0x74, 0x22, 0xe1, 0x02, 0x0a, 0x19, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68,
	0x43, 0x61, 0x73, 0x65, 0x4e, 0x6f, 0x74, 0x49, 0x6e, 0x55, 0x49, 0x50, 0x6c, 0x61, 0x6e, 0x49,
	0x74, 0x65, 0x6d, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74,
	0x49, 0x64, 0x12, 0x22, 0x0a, 0x0d, 0x67, 0x69, 0x74, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x67, 0x69, 0x74, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x74, 0x68, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x70, 0x61, 0x74, 0x68, 0x12, 0x1f, 0x0a, 0x0b, 0x70, 0x61,
	0x72, 0x65, 0x6e, 0x74, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0a, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x50, 0x61, 0x74, 0x68, 0x12, 0x12, 0x0a, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12,
	0x14, 0x0a, 0x05, 0x61, 0x6c, 0x69, 0x61, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x61, 0x6c, 0x69, 0x61, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x61, 0x67,
	0x73, 0x18, 0x08, 0x20, 0x03, 0x28, 0x09, 0x52, 0x04, 0x74, 0x61, 0x67, 0x73, 0x12, 0x1d, 0x0a,
	0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x62, 0x79, 0x18, 0x09, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x42, 0x79, 0x12, 0x1d, 0x0a, 0x0a,
	0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x62, 0x79, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x42, 0x79, 0x12, 0x1d, 0x0a, 0x0a, 0x63,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x75, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09,
	0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x42, 0x41, 0x5a, 0x3f, 0x67, 0x69, 0x74,
	0x6c, 0x61, 0x62, 0x2e, 0x74, 0x74, 0x79, 0x75, 0x79, 0x69, 0x6e, 0x2e, 0x63, 0x6f, 0x6d, 0x2f,
	0x54, 0x65, 0x73, 0x74, 0x44, 0x65, 0x76, 0x65, 0x6c, 0x6f, 0x70, 0x6d, 0x65, 0x6e, 0x74, 0x2f,
	0x70, 0x72, 0x6f, 0x62, 0x65, 0x2d, 0x62, 0x61, 0x63, 0x6b, 0x65, 0x6e, 0x64, 0x2f, 0x6d, 0x61,
	0x6e, 0x61, 0x67, 0x65, 0x72, 0x2f, 0x72, 0x70, 0x63, 0x2f, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_manager_uiplan_proto_rawDescOnce sync.Once
	file_manager_uiplan_proto_rawDescData = file_manager_uiplan_proto_rawDesc
)

func file_manager_uiplan_proto_rawDescGZIP() []byte {
	file_manager_uiplan_proto_rawDescOnce.Do(func() {
		file_manager_uiplan_proto_rawDescData = protoimpl.X.CompressGZIP(file_manager_uiplan_proto_rawDescData)
	})
	return file_manager_uiplan_proto_rawDescData
}

var file_manager_uiplan_proto_msgTypes = make([]protoimpl.MessageInfo, 6)
var file_manager_uiplan_proto_goTypes = []any{
	(*UiPlan)(nil),                    // 0: manager.UiPlan
	(*CasePathItem)(nil),              // 1: manager.CasePathItem
	(*Path)(nil),                      // 2: manager.Path
	(*UICaseTreeNode)(nil),            // 3: manager.UICaseTreeNode
	(*SearchCaseInUIPlanItem)(nil),    // 4: manager.SearchCaseInUIPlanItem
	(*SearchCaseNotInUIPlanItem)(nil), // 5: manager.SearchCaseNotInUIPlanItem
	(pb.TriggerMode)(0),               // 6: common.TriggerMode
	(pb.PriorityType)(0),              // 7: common.PriorityType
	(ExecutionMode)(0),                // 8: manager.ExecutionMode
	(pb.DeviceType)(0),                // 9: common.DeviceType
	(pb.PlatformType)(0),              // 10: common.PlatformType
	(pb.TestLanguage)(0),              // 11: common.TestLanguage
	(pb.TestFramework)(0),             // 12: common.TestFramework
	(pb.FailRetry)(0),                 // 13: common.FailRetry
	(CommonState)(0),                  // 14: manager.CommonState
}
var file_manager_uiplan_proto_depIdxs = []int32{
	6,  // 0: manager.UiPlan.type:type_name -> common.TriggerMode
	7,  // 1: manager.UiPlan.priority_type:type_name -> common.PriorityType
	8,  // 2: manager.UiPlan.execution_mode:type_name -> manager.ExecutionMode
	9,  // 3: manager.UiPlan.device_type:type_name -> common.DeviceType
	10, // 4: manager.UiPlan.platform_type:type_name -> common.PlatformType
	11, // 5: manager.UiPlan.test_language:type_name -> common.TestLanguage
	12, // 6: manager.UiPlan.test_framework:type_name -> common.TestFramework
	13, // 7: manager.UiPlan.fail_retry:type_name -> common.FailRetry
	14, // 8: manager.UiPlan.state:type_name -> manager.CommonState
	3,  // 9: manager.UICaseTreeNode.children:type_name -> manager.UICaseTreeNode
	10, // [10:10] is the sub-list for method output_type
	10, // [10:10] is the sub-list for method input_type
	10, // [10:10] is the sub-list for extension type_name
	10, // [10:10] is the sub-list for extension extendee
	0,  // [0:10] is the sub-list for field type_name
}

func init() { file_manager_uiplan_proto_init() }
func file_manager_uiplan_proto_init() {
	if File_manager_uiplan_proto != nil {
		return
	}
	file_manager_base_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_manager_uiplan_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   6,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_manager_uiplan_proto_goTypes,
		DependencyIndexes: file_manager_uiplan_proto_depIdxs,
		MessageInfos:      file_manager_uiplan_proto_msgTypes,
	}.Build()
	File_manager_uiplan_proto = out.File
	file_manager_uiplan_proto_rawDesc = nil
	file_manager_uiplan_proto_goTypes = nil
	file_manager_uiplan_proto_depIdxs = nil
}
