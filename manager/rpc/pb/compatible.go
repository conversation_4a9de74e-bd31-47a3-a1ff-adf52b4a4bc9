// 当ProtoBuf无法完全实现API定义的数据结构时，可在此文件中编写兼容方法

package pb

import (
	"github.com/zeromicro/go-zero/core/jsonx"
	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/types/known/structpb"
)

func (x *Relation) GetChildrenRelations() [][]*Relation {
	if x == nil {
		return nil
	}

	children := x.GetChildren()
	rs := make([][]*Relation, 0, len(children))
	for _, lv := range children {
		vs := lv.GetValues()
		rs = append(rs, make([]*Relation, 0, len(vs)))
		index := len(rs) - 1

		for _, v := range vs {
			b, err := v.MarshalJSON()
			if err != nil {
				continue
			}

			var r Relation
			if err = protojson.Unmarshal(b, &r); err != nil {
				continue
			}

			rs[index] = append(rs[index], &r)
		}
	}

	return rs
}

func (x *Relation) ToPBValue() (*structpb.Value, error) {
	b, err := protojson.Marshal(x)
	if err != nil {
		return nil, err
	}

	var m map[string]any
	if err := jsonx.Unmarshal(b, &m); err != nil {
		return nil, err
	}

	return structpb.NewValue(m)
}
