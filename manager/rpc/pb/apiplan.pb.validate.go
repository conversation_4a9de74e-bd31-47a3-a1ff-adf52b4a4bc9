// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: manager/apiplan.proto

package pb

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	pb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = pb.TriggerMode(0)
)

// Validate checks the field values on ApiPlan with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ApiPlan) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ApiPlan with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in ApiPlanMultiError, or nil if none found.
func (m *ApiPlan) ValidateAll() error {
	return m.validate(true)
}

func (m *ApiPlan) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ProjectId

	// no validation rules for PlanId

	// no validation rules for Name

	// no validation rules for Description

	// no validation rules for Priority

	// no validation rules for State

	// no validation rules for Type

	// no validation rules for Purpose

	// no validation rules for CronExpression

	// no validation rules for GeneralConfigId

	// no validation rules for SuiteExecutionMode

	// no validation rules for CaseExecutionMode

	// no validation rules for MaintainedBy

	// no validation rules for CreatedBy

	// no validation rules for UpdatedBy

	// no validation rules for CreatedAt

	// no validation rules for UpdatedAt

	// no validation rules for CategoryId

	if len(errors) > 0 {
		return ApiPlanMultiError(errors)
	}

	return nil
}

// ApiPlanMultiError is an error wrapping multiple validation errors returned
// by ApiPlan.ValidateAll() if the designated constraints aren't met.
type ApiPlanMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ApiPlanMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ApiPlanMultiError) AllErrors() []error { return m }

// ApiPlanValidationError is the validation error returned by ApiPlan.Validate
// if the designated constraints aren't met.
type ApiPlanValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ApiPlanValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ApiPlanValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ApiPlanValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ApiPlanValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ApiPlanValidationError) ErrorName() string { return "ApiPlanValidationError" }

// Error satisfies the builtin error interface
func (e ApiPlanValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sApiPlan.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ApiPlanValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ApiPlanValidationError{}

// Validate checks the field values on SuiteTypeId with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *SuiteTypeId) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SuiteTypeId with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in SuiteTypeIdMultiError, or
// nil if none found.
func (m *SuiteTypeId) ValidateAll() error {
	return m.validate(true)
}

func (m *SuiteTypeId) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if _, ok := _SuiteTypeId_SuiteType_InLookup[m.GetSuiteType()]; !ok {
		err := SuiteTypeIdValidationError{
			field:  "SuiteType",
			reason: "value must be in list [API_SUITE INTERFACE_DOCUMENT]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_SuiteTypeId_SuiteId_Pattern.MatchString(m.GetSuiteId()) {
		err := SuiteTypeIdValidationError{
			field:  "SuiteId",
			reason: "value does not match regex pattern \"(?:^suite_id:.+?|^interface_document_id:.+?)\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return SuiteTypeIdMultiError(errors)
	}

	return nil
}

// SuiteTypeIdMultiError is an error wrapping multiple validation errors
// returned by SuiteTypeId.ValidateAll() if the designated constraints aren't met.
type SuiteTypeIdMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SuiteTypeIdMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SuiteTypeIdMultiError) AllErrors() []error { return m }

// SuiteTypeIdValidationError is the validation error returned by
// SuiteTypeId.Validate if the designated constraints aren't met.
type SuiteTypeIdValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SuiteTypeIdValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SuiteTypeIdValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SuiteTypeIdValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SuiteTypeIdValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SuiteTypeIdValidationError) ErrorName() string { return "SuiteTypeIdValidationError" }

// Error satisfies the builtin error interface
func (e SuiteTypeIdValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSuiteTypeId.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SuiteTypeIdValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SuiteTypeIdValidationError{}

var _SuiteTypeId_SuiteType_InLookup = map[string]struct{}{
	"API_SUITE":          {},
	"INTERFACE_DOCUMENT": {},
}

var _SuiteTypeId_SuiteId_Pattern = regexp.MustCompile("(?:^suite_id:.+?|^interface_document_id:.+?)")

// Validate checks the field values on SearchApiPlanItem with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *SearchApiPlanItem) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SearchApiPlanItem with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SearchApiPlanItemMultiError, or nil if none found.
func (m *SearchApiPlanItem) ValidateAll() error {
	return m.validate(true)
}

func (m *SearchApiPlanItem) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ProjectId

	// no validation rules for PlanId

	// no validation rules for Name

	// no validation rules for Description

	// no validation rules for Priority

	// no validation rules for State

	// no validation rules for Type

	// no validation rules for Purpose

	// no validation rules for CronExpression

	// no validation rules for SuiteExecutionMode

	// no validation rules for CaseExecutionMode

	// no validation rules for SuiteIncluded

	// no validation rules for MaintainedBy

	// no validation rules for CreatedBy

	// no validation rules for UpdatedBy

	// no validation rules for CreatedAt

	// no validation rules for UpdatedAt

	// no validation rules for CategoryId

	if len(errors) > 0 {
		return SearchApiPlanItemMultiError(errors)
	}

	return nil
}

// SearchApiPlanItemMultiError is an error wrapping multiple validation errors
// returned by SearchApiPlanItem.ValidateAll() if the designated constraints
// aren't met.
type SearchApiPlanItemMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SearchApiPlanItemMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SearchApiPlanItemMultiError) AllErrors() []error { return m }

// SearchApiPlanItemValidationError is the validation error returned by
// SearchApiPlanItem.Validate if the designated constraints aren't met.
type SearchApiPlanItemValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SearchApiPlanItemValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SearchApiPlanItemValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SearchApiPlanItemValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SearchApiPlanItemValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SearchApiPlanItemValidationError) ErrorName() string {
	return "SearchApiPlanItemValidationError"
}

// Error satisfies the builtin error interface
func (e SearchApiPlanItemValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSearchApiPlanItem.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SearchApiPlanItemValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SearchApiPlanItemValidationError{}

// Validate checks the field values on SearchSuiteInApiPlanItem with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SearchSuiteInApiPlanItem) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SearchSuiteInApiPlanItem with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SearchSuiteInApiPlanItemMultiError, or nil if none found.
func (m *SearchSuiteInApiPlanItem) ValidateAll() error {
	return m.validate(true)
}

func (m *SearchSuiteInApiPlanItem) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ProjectId

	// no validation rules for SuiteType

	// no validation rules for SuiteId

	// no validation rules for Name

	// no validation rules for Description

	// no validation rules for Priority

	// no validation rules for State

	// no validation rules for ReferenceState

	// no validation rules for CaseIncluded

	// no validation rules for CaseSkipped

	// no validation rules for MaintainedBy

	// no validation rules for CreatedBy

	// no validation rules for UpdatedBy

	// no validation rules for CreatedAt

	// no validation rules for UpdatedAt

	if len(errors) > 0 {
		return SearchSuiteInApiPlanItemMultiError(errors)
	}

	return nil
}

// SearchSuiteInApiPlanItemMultiError is an error wrapping multiple validation
// errors returned by SearchSuiteInApiPlanItem.ValidateAll() if the designated
// constraints aren't met.
type SearchSuiteInApiPlanItemMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SearchSuiteInApiPlanItemMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SearchSuiteInApiPlanItemMultiError) AllErrors() []error { return m }

// SearchSuiteInApiPlanItemValidationError is the validation error returned by
// SearchSuiteInApiPlanItem.Validate if the designated constraints aren't met.
type SearchSuiteInApiPlanItemValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SearchSuiteInApiPlanItemValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SearchSuiteInApiPlanItemValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SearchSuiteInApiPlanItemValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SearchSuiteInApiPlanItemValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SearchSuiteInApiPlanItemValidationError) ErrorName() string {
	return "SearchSuiteInApiPlanItemValidationError"
}

// Error satisfies the builtin error interface
func (e SearchSuiteInApiPlanItemValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSearchSuiteInApiPlanItem.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SearchSuiteInApiPlanItemValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SearchSuiteInApiPlanItemValidationError{}

// Validate checks the field values on SearchSuiteNotInApiPlanItem with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SearchSuiteNotInApiPlanItem) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SearchSuiteNotInApiPlanItem with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SearchSuiteNotInApiPlanItemMultiError, or nil if none found.
func (m *SearchSuiteNotInApiPlanItem) ValidateAll() error {
	return m.validate(true)
}

func (m *SearchSuiteNotInApiPlanItem) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ProjectId

	// no validation rules for CategoryId

	// no validation rules for SuiteId

	// no validation rules for Name

	// no validation rules for Description

	// no validation rules for Priority

	// no validation rules for State

	// no validation rules for MaintainedBy

	// no validation rules for CreatedBy

	// no validation rules for UpdatedBy

	// no validation rules for CreatedAt

	// no validation rules for UpdatedAt

	if len(errors) > 0 {
		return SearchSuiteNotInApiPlanItemMultiError(errors)
	}

	return nil
}

// SearchSuiteNotInApiPlanItemMultiError is an error wrapping multiple
// validation errors returned by SearchSuiteNotInApiPlanItem.ValidateAll() if
// the designated constraints aren't met.
type SearchSuiteNotInApiPlanItemMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SearchSuiteNotInApiPlanItemMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SearchSuiteNotInApiPlanItemMultiError) AllErrors() []error { return m }

// SearchSuiteNotInApiPlanItemValidationError is the validation error returned
// by SearchSuiteNotInApiPlanItem.Validate if the designated constraints
// aren't met.
type SearchSuiteNotInApiPlanItemValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SearchSuiteNotInApiPlanItemValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SearchSuiteNotInApiPlanItemValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SearchSuiteNotInApiPlanItemValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SearchSuiteNotInApiPlanItemValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SearchSuiteNotInApiPlanItemValidationError) ErrorName() string {
	return "SearchSuiteNotInApiPlanItemValidationError"
}

// Error satisfies the builtin error interface
func (e SearchSuiteNotInApiPlanItemValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSearchSuiteNotInApiPlanItem.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SearchSuiteNotInApiPlanItemValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SearchSuiteNotInApiPlanItemValidationError{}

// Validate checks the field values on SearchCaseInApiPlanItem with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SearchCaseInApiPlanItem) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SearchCaseInApiPlanItem with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SearchCaseInApiPlanItemMultiError, or nil if none found.
func (m *SearchCaseInApiPlanItem) ValidateAll() error {
	return m.validate(true)
}

func (m *SearchCaseInApiPlanItem) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ProjectId

	// no validation rules for CaseId

	// no validation rules for Name

	// no validation rules for Description

	// no validation rules for Priority

	// no validation rules for State

	// no validation rules for ReferenceState

	// no validation rules for MaintainedBy

	// no validation rules for CreatedBy

	// no validation rules for UpdatedBy

	// no validation rules for CreatedAt

	// no validation rules for UpdatedAt

	if _, ok := _SearchCaseInApiPlanItem_CaseType_InLookup[m.GetCaseType()]; !ok {
		err := SearchCaseInApiPlanItemValidationError{
			field:  "CaseType",
			reason: "value must be in list [API_CASE INTERFACE_CASE]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return SearchCaseInApiPlanItemMultiError(errors)
	}

	return nil
}

// SearchCaseInApiPlanItemMultiError is an error wrapping multiple validation
// errors returned by SearchCaseInApiPlanItem.ValidateAll() if the designated
// constraints aren't met.
type SearchCaseInApiPlanItemMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SearchCaseInApiPlanItemMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SearchCaseInApiPlanItemMultiError) AllErrors() []error { return m }

// SearchCaseInApiPlanItemValidationError is the validation error returned by
// SearchCaseInApiPlanItem.Validate if the designated constraints aren't met.
type SearchCaseInApiPlanItemValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SearchCaseInApiPlanItemValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SearchCaseInApiPlanItemValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SearchCaseInApiPlanItemValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SearchCaseInApiPlanItemValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SearchCaseInApiPlanItemValidationError) ErrorName() string {
	return "SearchCaseInApiPlanItemValidationError"
}

// Error satisfies the builtin error interface
func (e SearchCaseInApiPlanItemValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSearchCaseInApiPlanItem.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SearchCaseInApiPlanItemValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SearchCaseInApiPlanItemValidationError{}

var _SearchCaseInApiPlanItem_CaseType_InLookup = map[string]struct{}{
	"API_CASE":       {},
	"INTERFACE_CASE": {},
}

// Validate checks the field values on AdvancedSearchSuiteItem with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *AdvancedSearchSuiteItem) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AdvancedSearchSuiteItem with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AdvancedSearchSuiteItemMultiError, or nil if none found.
func (m *AdvancedSearchSuiteItem) ValidateAll() error {
	return m.validate(true)
}

func (m *AdvancedSearchSuiteItem) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ProjectId

	// no validation rules for CategoryId

	// no validation rules for SuiteId

	// no validation rules for Name

	// no validation rules for Description

	// no validation rules for Priority

	// no validation rules for State

	// no validation rules for CaseCount

	// no validation rules for MaintainedBy

	// no validation rules for CreatedBy

	// no validation rules for UpdatedBy

	// no validation rules for CreatedAt

	// no validation rules for UpdatedAt

	if len(errors) > 0 {
		return AdvancedSearchSuiteItemMultiError(errors)
	}

	return nil
}

// AdvancedSearchSuiteItemMultiError is an error wrapping multiple validation
// errors returned by AdvancedSearchSuiteItem.ValidateAll() if the designated
// constraints aren't met.
type AdvancedSearchSuiteItemMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AdvancedSearchSuiteItemMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AdvancedSearchSuiteItemMultiError) AllErrors() []error { return m }

// AdvancedSearchSuiteItemValidationError is the validation error returned by
// AdvancedSearchSuiteItem.Validate if the designated constraints aren't met.
type AdvancedSearchSuiteItemValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AdvancedSearchSuiteItemValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AdvancedSearchSuiteItemValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AdvancedSearchSuiteItemValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AdvancedSearchSuiteItemValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AdvancedSearchSuiteItemValidationError) ErrorName() string {
	return "AdvancedSearchSuiteItemValidationError"
}

// Error satisfies the builtin error interface
func (e AdvancedSearchSuiteItemValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAdvancedSearchSuiteItem.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AdvancedSearchSuiteItemValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AdvancedSearchSuiteItemValidationError{}
