// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: manager/review.proto

package pb

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on ReviewRecord with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ReviewRecord) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ReviewRecord with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ReviewRecordMultiError, or
// nil if none found.
func (m *ReviewRecord) ValidateAll() error {
	return m.validate(true)
}

func (m *ReviewRecord) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ProjectId

	// no validation rules for ReviewId

	// no validation rules for ResourceBranch

	// no validation rules for ResourceParentType

	// no validation rules for ResourceType

	// no validation rules for ResourceId

	// no validation rules for ResourceName

	// no validation rules for RemarkOfPending

	// no validation rules for RemarkOfRevoked

	// no validation rules for RemarkOfReviewed

	// no validation rules for Status

	// no validation rules for CreatedBy

	// no validation rules for UpdatedBy

	// no validation rules for CreatedAt

	// no validation rules for UpdatedAt

	if len(errors) > 0 {
		return ReviewRecordMultiError(errors)
	}

	return nil
}

// ReviewRecordMultiError is an error wrapping multiple validation errors
// returned by ReviewRecord.ValidateAll() if the designated constraints aren't met.
type ReviewRecordMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ReviewRecordMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ReviewRecordMultiError) AllErrors() []error { return m }

// ReviewRecordValidationError is the validation error returned by
// ReviewRecord.Validate if the designated constraints aren't met.
type ReviewRecordValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ReviewRecordValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ReviewRecordValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ReviewRecordValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ReviewRecordValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ReviewRecordValidationError) ErrorName() string { return "ReviewRecordValidationError" }

// Error satisfies the builtin error interface
func (e ReviewRecordValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sReviewRecord.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ReviewRecordValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ReviewRecordValidationError{}
