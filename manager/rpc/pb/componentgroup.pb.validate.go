// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: manager/componentgroup.proto

package pb

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on ComponentGroup with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ComponentGroup) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ComponentGroup with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ComponentGroupMultiError,
// or nil if none found.
func (m *ComponentGroup) ValidateAll() error {
	return m.validate(true)
}

func (m *ComponentGroup) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ProjectId

	// no validation rules for CategoryId

	// no validation rules for ComponentGroupId

	// no validation rules for ComponentGroupType

	// no validation rules for Name

	// no validation rules for Description

	// no validation rules for Priority

	for idx, item := range m.GetImports() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ComponentGroupValidationError{
						field:  fmt.Sprintf("Imports[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ComponentGroupValidationError{
						field:  fmt.Sprintf("Imports[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ComponentGroupValidationError{
					field:  fmt.Sprintf("Imports[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetExports() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ComponentGroupValidationError{
						field:  fmt.Sprintf("Exports[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ComponentGroupValidationError{
						field:  fmt.Sprintf("Exports[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ComponentGroupValidationError{
					field:  fmt.Sprintf("Exports[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetAccountConfig()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ComponentGroupValidationError{
					field:  "AccountConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ComponentGroupValidationError{
					field:  "AccountConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAccountConfig()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ComponentGroupValidationError{
				field:  "AccountConfig",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Version

	// no validation rules for MaintainedBy

	// no validation rules for CreatedBy

	// no validation rules for UpdatedBy

	// no validation rules for CreatedAt

	// no validation rules for UpdatedAt

	for idx, item := range m.GetNodes() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ComponentGroupValidationError{
						field:  fmt.Sprintf("Nodes[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ComponentGroupValidationError{
						field:  fmt.Sprintf("Nodes[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ComponentGroupValidationError{
					field:  fmt.Sprintf("Nodes[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetEdges() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ComponentGroupValidationError{
						field:  fmt.Sprintf("Edges[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ComponentGroupValidationError{
						field:  fmt.Sprintf("Edges[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ComponentGroupValidationError{
					field:  fmt.Sprintf("Edges[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetCombos() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ComponentGroupValidationError{
						field:  fmt.Sprintf("Combos[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ComponentGroupValidationError{
						field:  fmt.Sprintf("Combos[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ComponentGroupValidationError{
					field:  fmt.Sprintf("Combos[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ComponentGroupMultiError(errors)
	}

	return nil
}

// ComponentGroupMultiError is an error wrapping multiple validation errors
// returned by ComponentGroup.ValidateAll() if the designated constraints
// aren't met.
type ComponentGroupMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ComponentGroupMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ComponentGroupMultiError) AllErrors() []error { return m }

// ComponentGroupValidationError is the validation error returned by
// ComponentGroup.Validate if the designated constraints aren't met.
type ComponentGroupValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ComponentGroupValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ComponentGroupValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ComponentGroupValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ComponentGroupValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ComponentGroupValidationError) ErrorName() string { return "ComponentGroupValidationError" }

// Error satisfies the builtin error interface
func (e ComponentGroupValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sComponentGroup.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ComponentGroupValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ComponentGroupValidationError{}

// Validate checks the field values on Referenced with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Referenced) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Referenced with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ReferencedMultiError, or
// nil if none found.
func (m *Referenced) ValidateAll() error {
	return m.validate(true)
}

func (m *Referenced) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ProjectId

	// no validation rules for ReferenceId

	// no validation rules for ReferenceType

	// no validation rules for ReferenceVersion

	if len(errors) > 0 {
		return ReferencedMultiError(errors)
	}

	return nil
}

// ReferencedMultiError is an error wrapping multiple validation errors
// returned by Referenced.ValidateAll() if the designated constraints aren't met.
type ReferencedMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ReferencedMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ReferencedMultiError) AllErrors() []error { return m }

// ReferencedValidationError is the validation error returned by
// Referenced.Validate if the designated constraints aren't met.
type ReferencedValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ReferencedValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ReferencedValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ReferencedValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ReferencedValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ReferencedValidationError) ErrorName() string { return "ReferencedValidationError" }

// Error satisfies the builtin error interface
func (e ReferencedValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sReferenced.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ReferencedValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ReferencedValidationError{}

// Validate checks the field values on SearchComponentGroupReferenceItem with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *SearchComponentGroupReferenceItem) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SearchComponentGroupReferenceItem
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// SearchComponentGroupReferenceItemMultiError, or nil if none found.
func (m *SearchComponentGroupReferenceItem) ValidateAll() error {
	return m.validate(true)
}

func (m *SearchComponentGroupReferenceItem) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ProjectId

	// no validation rules for ComponentGroupId

	// no validation rules for ReferenceType

	// no validation rules for ReferenceId

	// no validation rules for ReferenceVersion

	// no validation rules for ReferenceParentId

	// no validation rules for ReferenceComponentGroupType

	// no validation rules for Name

	// no validation rules for Description

	// no validation rules for Priority

	// no validation rules for State

	// no validation rules for MaintainedBy

	// no validation rules for CreatedBy

	// no validation rules for UpdatedBy

	// no validation rules for CreatedAt

	// no validation rules for UpdatedAt

	if len(errors) > 0 {
		return SearchComponentGroupReferenceItemMultiError(errors)
	}

	return nil
}

// SearchComponentGroupReferenceItemMultiError is an error wrapping multiple
// validation errors returned by
// SearchComponentGroupReferenceItem.ValidateAll() if the designated
// constraints aren't met.
type SearchComponentGroupReferenceItemMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SearchComponentGroupReferenceItemMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SearchComponentGroupReferenceItemMultiError) AllErrors() []error { return m }

// SearchComponentGroupReferenceItemValidationError is the validation error
// returned by SearchComponentGroupReferenceItem.Validate if the designated
// constraints aren't met.
type SearchComponentGroupReferenceItemValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SearchComponentGroupReferenceItemValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SearchComponentGroupReferenceItemValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SearchComponentGroupReferenceItemValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SearchComponentGroupReferenceItemValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SearchComponentGroupReferenceItemValidationError) ErrorName() string {
	return "SearchComponentGroupReferenceItemValidationError"
}

// Error satisfies the builtin error interface
func (e SearchComponentGroupReferenceItemValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSearchComponentGroupReferenceItem.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SearchComponentGroupReferenceItemValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SearchComponentGroupReferenceItemValidationError{}
