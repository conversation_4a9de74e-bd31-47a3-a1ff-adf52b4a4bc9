// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.1
// 	protoc        v5.29.2
// source: manager/componentgroup.proto

package pb

import (
	reflect "reflect"
	sync "sync"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ComponentGroup struct {
	state              protoimpl.MessageState `protogen:"open.v1"`
	ProjectId          string                 `protobuf:"bytes,1,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`                              // 项目ID
	CategoryId         string                 `protobuf:"bytes,2,opt,name=category_id,json=categoryId,proto3" json:"category_id,omitempty"`                           // 所属分类ID
	ComponentGroupId   string                 `protobuf:"bytes,3,opt,name=component_group_id,json=componentGroupId,proto3" json:"component_group_id,omitempty"`       // 组件组ID
	ComponentGroupType string                 `protobuf:"bytes,4,opt,name=component_group_type,json=componentGroupType,proto3" json:"component_group_type,omitempty"` // 组件组类型
	Name               string                 `protobuf:"bytes,5,opt,name=name,proto3" json:"name,omitempty"`                                                         // 组件组名称
	Description        string                 `protobuf:"bytes,6,opt,name=description,proto3" json:"description,omitempty"`                                           // 组件组描述
	Priority           int64                  `protobuf:"varint,7,opt,name=priority,proto3" json:"priority,omitempty"`                                                // 优先级
	Tags               []string               `protobuf:"bytes,8,rep,name=tags,proto3" json:"tags,omitempty"`                                                         // 标签
	Imports            []*Import              `protobuf:"bytes,9,rep,name=imports,proto3" json:"imports,omitempty"`                                                   // 入参
	Exports            []*Export              `protobuf:"bytes,10,rep,name=exports,proto3" json:"exports,omitempty"`                                                  // 出参
	AccountConfig      *AccountConfig         `protobuf:"bytes,11,opt,name=account_config,json=accountConfig,proto3" json:"account_config,omitempty"`                 // 池账号配置信息
	Version            string                 `protobuf:"bytes,12,opt,name=version,proto3" json:"version,omitempty"`                                                  // 用例版本
	MaintainedBy       string                 `protobuf:"bytes,13,opt,name=maintained_by,json=maintainedBy,proto3" json:"maintained_by,omitempty"`                    // 维护者
	CreatedBy          string                 `protobuf:"bytes,14,opt,name=created_by,json=createdBy,proto3" json:"created_by,omitempty"`                             // 创建者
	UpdatedBy          string                 `protobuf:"bytes,15,opt,name=updated_by,json=updatedBy,proto3" json:"updated_by,omitempty"`                             // 更新者
	CreatedAt          int64                  `protobuf:"varint,16,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`                            // 创建时间
	UpdatedAt          int64                  `protobuf:"varint,17,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`                            // 更新时间
	Nodes              []*Node                `protobuf:"bytes,18,rep,name=nodes,proto3" json:"nodes,omitempty"`
	Edges              []*Edge                `protobuf:"bytes,19,rep,name=edges,proto3" json:"edges,omitempty"`
	Combos             []*Combo               `protobuf:"bytes,20,rep,name=combos,proto3" json:"combos,omitempty"`
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *ComponentGroup) Reset() {
	*x = ComponentGroup{}
	mi := &file_manager_componentgroup_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ComponentGroup) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ComponentGroup) ProtoMessage() {}

func (x *ComponentGroup) ProtoReflect() protoreflect.Message {
	mi := &file_manager_componentgroup_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ComponentGroup.ProtoReflect.Descriptor instead.
func (*ComponentGroup) Descriptor() ([]byte, []int) {
	return file_manager_componentgroup_proto_rawDescGZIP(), []int{0}
}

func (x *ComponentGroup) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *ComponentGroup) GetCategoryId() string {
	if x != nil {
		return x.CategoryId
	}
	return ""
}

func (x *ComponentGroup) GetComponentGroupId() string {
	if x != nil {
		return x.ComponentGroupId
	}
	return ""
}

func (x *ComponentGroup) GetComponentGroupType() string {
	if x != nil {
		return x.ComponentGroupType
	}
	return ""
}

func (x *ComponentGroup) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ComponentGroup) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *ComponentGroup) GetPriority() int64 {
	if x != nil {
		return x.Priority
	}
	return 0
}

func (x *ComponentGroup) GetTags() []string {
	if x != nil {
		return x.Tags
	}
	return nil
}

func (x *ComponentGroup) GetImports() []*Import {
	if x != nil {
		return x.Imports
	}
	return nil
}

func (x *ComponentGroup) GetExports() []*Export {
	if x != nil {
		return x.Exports
	}
	return nil
}

func (x *ComponentGroup) GetAccountConfig() *AccountConfig {
	if x != nil {
		return x.AccountConfig
	}
	return nil
}

func (x *ComponentGroup) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *ComponentGroup) GetMaintainedBy() string {
	if x != nil {
		return x.MaintainedBy
	}
	return ""
}

func (x *ComponentGroup) GetCreatedBy() string {
	if x != nil {
		return x.CreatedBy
	}
	return ""
}

func (x *ComponentGroup) GetUpdatedBy() string {
	if x != nil {
		return x.UpdatedBy
	}
	return ""
}

func (x *ComponentGroup) GetCreatedAt() int64 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

func (x *ComponentGroup) GetUpdatedAt() int64 {
	if x != nil {
		return x.UpdatedAt
	}
	return 0
}

func (x *ComponentGroup) GetNodes() []*Node {
	if x != nil {
		return x.Nodes
	}
	return nil
}

func (x *ComponentGroup) GetEdges() []*Edge {
	if x != nil {
		return x.Edges
	}
	return nil
}

func (x *ComponentGroup) GetCombos() []*Combo {
	if x != nil {
		return x.Combos
	}
	return nil
}

type Referenced struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	ProjectId        string                 `protobuf:"bytes,1,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`
	ReferenceId      string                 `protobuf:"bytes,2,opt,name=reference_id,json=referenceId,proto3" json:"reference_id,omitempty"`
	ReferenceType    string                 `protobuf:"bytes,3,opt,name=reference_type,json=referenceType,proto3" json:"reference_type,omitempty"`
	ReferenceVersion string                 `protobuf:"bytes,4,opt,name=reference_version,json=referenceVersion,proto3" json:"reference_version,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *Referenced) Reset() {
	*x = Referenced{}
	mi := &file_manager_componentgroup_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Referenced) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Referenced) ProtoMessage() {}

func (x *Referenced) ProtoReflect() protoreflect.Message {
	mi := &file_manager_componentgroup_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Referenced.ProtoReflect.Descriptor instead.
func (*Referenced) Descriptor() ([]byte, []int) {
	return file_manager_componentgroup_proto_rawDescGZIP(), []int{1}
}

func (x *Referenced) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *Referenced) GetReferenceId() string {
	if x != nil {
		return x.ReferenceId
	}
	return ""
}

func (x *Referenced) GetReferenceType() string {
	if x != nil {
		return x.ReferenceType
	}
	return ""
}

func (x *Referenced) GetReferenceVersion() string {
	if x != nil {
		return x.ReferenceVersion
	}
	return ""
}

type SearchComponentGroupReferenceItem struct {
	state                       protoimpl.MessageState `protogen:"open.v1"`
	ProjectId                   string                 `protobuf:"bytes,1,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`                                                           // 项目ID
	ComponentGroupId            string                 `protobuf:"bytes,2,opt,name=component_group_id,json=componentGroupId,proto3" json:"component_group_id,omitempty"`                                    // 组件组ID
	ReferenceType               string                 `protobuf:"bytes,3,opt,name=reference_type,json=referenceType,proto3" json:"reference_type,omitempty"`                                               // 引用对象类型
	ReferenceId                 string                 `protobuf:"bytes,4,opt,name=reference_id,json=referenceId,proto3" json:"reference_id,omitempty"`                                                     // 引用对象ID
	ReferenceVersion            string                 `protobuf:"bytes,5,opt,name=reference_version,json=referenceVersion,proto3" json:"reference_version,omitempty"`                                      // 引用对象版本
	ReferenceParentId           string                 `protobuf:"bytes,6,opt,name=reference_parent_id,json=referenceParentId,proto3" json:"reference_parent_id,omitempty"`                                 // 引用对象的父ID
	ReferenceComponentGroupType string                 `protobuf:"bytes,7,opt,name=reference_component_group_type,json=referenceComponentGroupType,proto3" json:"reference_component_group_type,omitempty"` // 引用对象的组件组类型
	Name                        string                 `protobuf:"bytes,8,opt,name=name,proto3" json:"name,omitempty"`                                                                                      // 引用对象名称
	Description                 string                 `protobuf:"bytes,9,opt,name=description,proto3" json:"description,omitempty"`                                                                        // 引用对象描述
	Priority                    int64                  `protobuf:"varint,10,opt,name=priority,proto3" json:"priority,omitempty"`                                                                            // 优先级
	Tags                        []string               `protobuf:"bytes,11,rep,name=tags,proto3" json:"tags,omitempty"`                                                                                     // 标签
	State                       CommonState            `protobuf:"varint,12,opt,name=state,proto3,enum=manager.CommonState" json:"state,omitempty"`                                                         // 状态
	MaintainedBy                string                 `protobuf:"bytes,13,opt,name=maintained_by,json=maintainedBy,proto3" json:"maintained_by,omitempty"`                                                 // 维护者
	CreatedBy                   string                 `protobuf:"bytes,14,opt,name=created_by,json=createdBy,proto3" json:"created_by,omitempty"`                                                          // 创建者
	UpdatedBy                   string                 `protobuf:"bytes,15,opt,name=updated_by,json=updatedBy,proto3" json:"updated_by,omitempty"`                                                          // 更新者
	CreatedAt                   int64                  `protobuf:"varint,16,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`                                                         // 创建时间
	UpdatedAt                   int64                  `protobuf:"varint,17,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`                                                         // 更新时间
	unknownFields               protoimpl.UnknownFields
	sizeCache                   protoimpl.SizeCache
}

func (x *SearchComponentGroupReferenceItem) Reset() {
	*x = SearchComponentGroupReferenceItem{}
	mi := &file_manager_componentgroup_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SearchComponentGroupReferenceItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchComponentGroupReferenceItem) ProtoMessage() {}

func (x *SearchComponentGroupReferenceItem) ProtoReflect() protoreflect.Message {
	mi := &file_manager_componentgroup_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchComponentGroupReferenceItem.ProtoReflect.Descriptor instead.
func (*SearchComponentGroupReferenceItem) Descriptor() ([]byte, []int) {
	return file_manager_componentgroup_proto_rawDescGZIP(), []int{2}
}

func (x *SearchComponentGroupReferenceItem) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *SearchComponentGroupReferenceItem) GetComponentGroupId() string {
	if x != nil {
		return x.ComponentGroupId
	}
	return ""
}

func (x *SearchComponentGroupReferenceItem) GetReferenceType() string {
	if x != nil {
		return x.ReferenceType
	}
	return ""
}

func (x *SearchComponentGroupReferenceItem) GetReferenceId() string {
	if x != nil {
		return x.ReferenceId
	}
	return ""
}

func (x *SearchComponentGroupReferenceItem) GetReferenceVersion() string {
	if x != nil {
		return x.ReferenceVersion
	}
	return ""
}

func (x *SearchComponentGroupReferenceItem) GetReferenceParentId() string {
	if x != nil {
		return x.ReferenceParentId
	}
	return ""
}

func (x *SearchComponentGroupReferenceItem) GetReferenceComponentGroupType() string {
	if x != nil {
		return x.ReferenceComponentGroupType
	}
	return ""
}

func (x *SearchComponentGroupReferenceItem) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *SearchComponentGroupReferenceItem) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *SearchComponentGroupReferenceItem) GetPriority() int64 {
	if x != nil {
		return x.Priority
	}
	return 0
}

func (x *SearchComponentGroupReferenceItem) GetTags() []string {
	if x != nil {
		return x.Tags
	}
	return nil
}

func (x *SearchComponentGroupReferenceItem) GetState() CommonState {
	if x != nil {
		return x.State
	}
	return CommonState_CS_NULL
}

func (x *SearchComponentGroupReferenceItem) GetMaintainedBy() string {
	if x != nil {
		return x.MaintainedBy
	}
	return ""
}

func (x *SearchComponentGroupReferenceItem) GetCreatedBy() string {
	if x != nil {
		return x.CreatedBy
	}
	return ""
}

func (x *SearchComponentGroupReferenceItem) GetUpdatedBy() string {
	if x != nil {
		return x.UpdatedBy
	}
	return ""
}

func (x *SearchComponentGroupReferenceItem) GetCreatedAt() int64 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

func (x *SearchComponentGroupReferenceItem) GetUpdatedAt() int64 {
	if x != nil {
		return x.UpdatedAt
	}
	return 0
}

var File_manager_componentgroup_proto protoreflect.FileDescriptor

var file_manager_componentgroup_proto_rawDesc = []byte{
	0x0a, 0x1c, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2f, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e,
	0x65, 0x6e, 0x74, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x07,
	0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x1a, 0x12, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72,
	0x2f, 0x62, 0x61, 0x73, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x6d, 0x61, 0x6e,
	0x61, 0x67, 0x65, 0x72, 0x2f, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x15, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2f, 0x65, 0x6c,
	0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xd8, 0x05, 0x0a, 0x0e,
	0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x12, 0x1d,
	0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x12, 0x1f, 0x0a,
	0x0b, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0a, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x49, 0x64, 0x12, 0x2c,
	0x0a, 0x12, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x5f, 0x67, 0x72, 0x6f, 0x75,
	0x70, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x63, 0x6f, 0x6d, 0x70,
	0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x49, 0x64, 0x12, 0x30, 0x0a, 0x14,
	0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x63, 0x6f, 0x6d, 0x70,
	0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x54, 0x79, 0x70, 0x65, 0x12, 0x12,
	0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x72, 0x69, 0x6f, 0x72, 0x69, 0x74, 0x79,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x70, 0x72, 0x69, 0x6f, 0x72, 0x69, 0x74, 0x79,
	0x12, 0x12, 0x0a, 0x04, 0x74, 0x61, 0x67, 0x73, 0x18, 0x08, 0x20, 0x03, 0x28, 0x09, 0x52, 0x04,
	0x74, 0x61, 0x67, 0x73, 0x12, 0x29, 0x0a, 0x07, 0x69, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x18,
	0x09, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e,
	0x49, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x52, 0x07, 0x69, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x12,
	0x29, 0x0a, 0x07, 0x65, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x18, 0x0a, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x0f, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x45, 0x78, 0x70, 0x6f, 0x72,
	0x74, 0x52, 0x07, 0x65, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x12, 0x3d, 0x0a, 0x0e, 0x61, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x0b, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x16, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x41, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x0d, 0x61, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x18, 0x0a, 0x07, 0x76, 0x65, 0x72,
	0x73, 0x69, 0x6f, 0x6e, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73,
	0x69, 0x6f, 0x6e, 0x12, 0x23, 0x0a, 0x0d, 0x6d, 0x61, 0x69, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65,
	0x64, 0x5f, 0x62, 0x79, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x6d, 0x61, 0x69, 0x6e,
	0x74, 0x61, 0x69, 0x6e, 0x65, 0x64, 0x42, 0x79, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x64, 0x5f, 0x62, 0x79, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x64, 0x42, 0x79, 0x12, 0x1d, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x64, 0x5f, 0x62, 0x79, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x75, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x64, 0x42, 0x79, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x64, 0x5f, 0x61, 0x74, 0x18, 0x10, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64,
	0x5f, 0x61, 0x74, 0x18, 0x11, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x64, 0x41, 0x74, 0x12, 0x23, 0x0a, 0x05, 0x6e, 0x6f, 0x64, 0x65, 0x73, 0x18, 0x12, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x4e, 0x6f,
	0x64, 0x65, 0x52, 0x05, 0x6e, 0x6f, 0x64, 0x65, 0x73, 0x12, 0x23, 0x0a, 0x05, 0x65, 0x64, 0x67,
	0x65, 0x73, 0x18, 0x13, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67,
	0x65, 0x72, 0x2e, 0x45, 0x64, 0x67, 0x65, 0x52, 0x05, 0x65, 0x64, 0x67, 0x65, 0x73, 0x12, 0x26,
	0x0a, 0x06, 0x63, 0x6f, 0x6d, 0x62, 0x6f, 0x73, 0x18, 0x14, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0e,
	0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x43, 0x6f, 0x6d, 0x62, 0x6f, 0x52, 0x06,
	0x63, 0x6f, 0x6d, 0x62, 0x6f, 0x73, 0x22, 0xa2, 0x01, 0x0a, 0x0a, 0x52, 0x65, 0x66, 0x65, 0x72,
	0x65, 0x6e, 0x63, 0x65, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65,
	0x63, 0x74, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63,
	0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x72, 0x65, 0x66, 0x65,
	0x72, 0x65, 0x6e, 0x63, 0x65, 0x49, 0x64, 0x12, 0x25, 0x0a, 0x0e, 0x72, 0x65, 0x66, 0x65, 0x72,
	0x65, 0x6e, 0x63, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0d, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x2b,
	0x0a, 0x11, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x5f, 0x76, 0x65, 0x72, 0x73,
	0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x72, 0x65, 0x66, 0x65, 0x72,
	0x65, 0x6e, 0x63, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x22, 0x8f, 0x05, 0x0a, 0x21,
	0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x47,
	0x72, 0x6f, 0x75, 0x70, 0x52, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x49, 0x74, 0x65,
	0x6d, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64,
	0x12, 0x2c, 0x0a, 0x12, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x5f, 0x67, 0x72,
	0x6f, 0x75, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x63, 0x6f,
	0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x49, 0x64, 0x12, 0x25,
	0x0a, 0x0e, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63,
	0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e,
	0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x72, 0x65, 0x66,
	0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x49, 0x64, 0x12, 0x2b, 0x0a, 0x11, 0x72, 0x65, 0x66, 0x65,
	0x72, 0x65, 0x6e, 0x63, 0x65, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x10, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x56, 0x65,
	0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x2e, 0x0a, 0x13, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e,
	0x63, 0x65, 0x5f, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x11, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x50, 0x61, 0x72,
	0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x43, 0x0a, 0x1e, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e,
	0x63, 0x65, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x5f, 0x67, 0x72, 0x6f,
	0x75, 0x70, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x1b, 0x72,
	0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e,
	0x74, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x54, 0x79, 0x70, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x20,
	0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x09, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x12, 0x1a, 0x0a, 0x08, 0x70, 0x72, 0x69, 0x6f, 0x72, 0x69, 0x74, 0x79, 0x18, 0x0a, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x08, 0x70, 0x72, 0x69, 0x6f, 0x72, 0x69, 0x74, 0x79, 0x12, 0x12, 0x0a, 0x04,
	0x74, 0x61, 0x67, 0x73, 0x18, 0x0b, 0x20, 0x03, 0x28, 0x09, 0x52, 0x04, 0x74, 0x61, 0x67, 0x73,
	0x12, 0x2a, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x14, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x12, 0x23, 0x0a, 0x0d,
	0x6d, 0x61, 0x69, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x64, 0x5f, 0x62, 0x79, 0x18, 0x0d, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0c, 0x6d, 0x61, 0x69, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x64, 0x42,
	0x79, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x62, 0x79, 0x18,
	0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x42, 0x79,
	0x12, 0x1d, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x62, 0x79, 0x18, 0x0f,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x42, 0x79, 0x12,
	0x1d, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x10, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x1d,
	0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x11, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x42, 0x41, 0x5a,
	0x3f, 0x67, 0x69, 0x74, 0x6c, 0x61, 0x62, 0x2e, 0x74, 0x74, 0x79, 0x75, 0x79, 0x69, 0x6e, 0x2e,
	0x63, 0x6f, 0x6d, 0x2f, 0x54, 0x65, 0x73, 0x74, 0x44, 0x65, 0x76, 0x65, 0x6c, 0x6f, 0x70, 0x6d,
	0x65, 0x6e, 0x74, 0x2f, 0x70, 0x72, 0x6f, 0x62, 0x65, 0x2d, 0x62, 0x61, 0x63, 0x6b, 0x65, 0x6e,
	0x64, 0x2f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2f, 0x72, 0x70, 0x63, 0x2f, 0x70, 0x62,
	0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_manager_componentgroup_proto_rawDescOnce sync.Once
	file_manager_componentgroup_proto_rawDescData = file_manager_componentgroup_proto_rawDesc
)

func file_manager_componentgroup_proto_rawDescGZIP() []byte {
	file_manager_componentgroup_proto_rawDescOnce.Do(func() {
		file_manager_componentgroup_proto_rawDescData = protoimpl.X.CompressGZIP(file_manager_componentgroup_proto_rawDescData)
	})
	return file_manager_componentgroup_proto_rawDescData
}

var file_manager_componentgroup_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_manager_componentgroup_proto_goTypes = []any{
	(*ComponentGroup)(nil),                    // 0: manager.ComponentGroup
	(*Referenced)(nil),                        // 1: manager.Referenced
	(*SearchComponentGroupReferenceItem)(nil), // 2: manager.SearchComponentGroupReferenceItem
	(*Import)(nil),                            // 3: manager.Import
	(*Export)(nil),                            // 4: manager.Export
	(*AccountConfig)(nil),                     // 5: manager.AccountConfig
	(*Node)(nil),                              // 6: manager.Node
	(*Edge)(nil),                              // 7: manager.Edge
	(*Combo)(nil),                             // 8: manager.Combo
	(CommonState)(0),                          // 9: manager.CommonState
}
var file_manager_componentgroup_proto_depIdxs = []int32{
	3, // 0: manager.ComponentGroup.imports:type_name -> manager.Import
	4, // 1: manager.ComponentGroup.exports:type_name -> manager.Export
	5, // 2: manager.ComponentGroup.account_config:type_name -> manager.AccountConfig
	6, // 3: manager.ComponentGroup.nodes:type_name -> manager.Node
	7, // 4: manager.ComponentGroup.edges:type_name -> manager.Edge
	8, // 5: manager.ComponentGroup.combos:type_name -> manager.Combo
	9, // 6: manager.SearchComponentGroupReferenceItem.state:type_name -> manager.CommonState
	7, // [7:7] is the sub-list for method output_type
	7, // [7:7] is the sub-list for method input_type
	7, // [7:7] is the sub-list for extension type_name
	7, // [7:7] is the sub-list for extension extendee
	0, // [0:7] is the sub-list for field type_name
}

func init() { file_manager_componentgroup_proto_init() }
func file_manager_componentgroup_proto_init() {
	if File_manager_componentgroup_proto != nil {
		return
	}
	file_manager_base_proto_init()
	file_manager_component_proto_init()
	file_manager_element_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_manager_componentgroup_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_manager_componentgroup_proto_goTypes,
		DependencyIndexes: file_manager_componentgroup_proto_depIdxs,
		MessageInfos:      file_manager_componentgroup_proto_msgTypes,
	}.Build()
	File_manager_componentgroup_proto = out.File
	file_manager_componentgroup_proto_rawDesc = nil
	file_manager_componentgroup_proto_goTypes = nil
	file_manager_componentgroup_proto_depIdxs = nil
}
