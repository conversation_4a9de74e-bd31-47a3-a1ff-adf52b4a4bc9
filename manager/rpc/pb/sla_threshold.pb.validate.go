// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: manager/sla_threshold.proto

package pb

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	pb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = pb.PlatformType(0)
)

// Validate checks the field values on SlaThreshold with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *SlaThreshold) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SlaThreshold with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in SlaThresholdMultiError, or
// nil if none found.
func (m *SlaThreshold) ValidateAll() error {
	return m.validate(true)
}

func (m *SlaThreshold) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ProjectId

	// no validation rules for PlatformType

	// no validation rules for BranchType

	// no validation rules for Name

	// no validation rules for Unit

	// no validation rules for Value

	if len(errors) > 0 {
		return SlaThresholdMultiError(errors)
	}

	return nil
}

// SlaThresholdMultiError is an error wrapping multiple validation errors
// returned by SlaThreshold.ValidateAll() if the designated constraints aren't met.
type SlaThresholdMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SlaThresholdMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SlaThresholdMultiError) AllErrors() []error { return m }

// SlaThresholdValidationError is the validation error returned by
// SlaThreshold.Validate if the designated constraints aren't met.
type SlaThresholdValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SlaThresholdValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SlaThresholdValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SlaThresholdValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SlaThresholdValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SlaThresholdValidationError) ErrorName() string { return "SlaThresholdValidationError" }

// Error satisfies the builtin error interface
func (e SlaThresholdValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSlaThreshold.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SlaThresholdValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SlaThresholdValidationError{}
