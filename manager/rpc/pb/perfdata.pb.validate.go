// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: manager/perfdata.proto

package pb

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on PerfData with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *PerfData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PerfData with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in PerfDataMultiError, or nil
// if none found.
func (m *PerfData) ValidateAll() error {
	return m.validate(true)
}

func (m *PerfData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ProjectId

	// no validation rules for DataId

	// no validation rules for Name

	// no validation rules for Description

	// no validation rules for Extension

	// no validation rules for Hash

	// no validation rules for Size

	// no validation rules for NumberOfVu

	// no validation rules for CreatedBy

	// no validation rules for UpdatedBy

	// no validation rules for CreatedAt

	// no validation rules for UpdatedAt

	if len(errors) > 0 {
		return PerfDataMultiError(errors)
	}

	return nil
}

// PerfDataMultiError is an error wrapping multiple validation errors returned
// by PerfData.ValidateAll() if the designated constraints aren't met.
type PerfDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PerfDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PerfDataMultiError) AllErrors() []error { return m }

// PerfDataValidationError is the validation error returned by
// PerfData.Validate if the designated constraints aren't met.
type PerfDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PerfDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PerfDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PerfDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PerfDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PerfDataValidationError) ErrorName() string { return "PerfDataValidationError" }

// Error satisfies the builtin error interface
func (e PerfDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPerfData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PerfDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PerfDataValidationError{}
