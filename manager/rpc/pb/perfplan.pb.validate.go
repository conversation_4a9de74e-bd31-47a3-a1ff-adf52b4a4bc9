// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: manager/perfplan.proto

package pb

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	pb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = pb.TriggerMode(0)
)

// Validate checks the field values on PerfPlan with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *PerfPlan) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PerfPlan with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in PerfPlanMultiError, or nil
// if none found.
func (m *PerfPlan) ValidateAll() error {
	return m.validate(true)
}

func (m *PerfPlan) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ProjectId

	// no validation rules for PlanId

	// no validation rules for Name

	// no validation rules for Description

	// no validation rules for Type

	// no validation rules for State

	// no validation rules for Protocol

	// no validation rules for ProtobufConfigId

	// no validation rules for GeneralConfigId

	// no validation rules for AccountConfigId

	// no validation rules for Duration

	// no validation rules for TargetEnv

	if all {
		switch v := interface{}(m.GetKeepalive()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PerfPlanValidationError{
					field:  "Keepalive",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PerfPlanValidationError{
					field:  "Keepalive",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetKeepalive()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PerfPlanValidationError{
				field:  "Keepalive",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Delay

	// no validation rules for MaintainedBy

	// no validation rules for CreatedBy

	// no validation rules for UpdatedBy

	// no validation rules for CreatedAt

	// no validation rules for UpdatedAt

	if len(errors) > 0 {
		return PerfPlanMultiError(errors)
	}

	return nil
}

// PerfPlanMultiError is an error wrapping multiple validation errors returned
// by PerfPlan.ValidateAll() if the designated constraints aren't met.
type PerfPlanMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PerfPlanMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PerfPlanMultiError) AllErrors() []error { return m }

// PerfPlanValidationError is the validation error returned by
// PerfPlan.Validate if the designated constraints aren't met.
type PerfPlanValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PerfPlanValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PerfPlanValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PerfPlanValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PerfPlanValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PerfPlanValidationError) ErrorName() string { return "PerfPlanValidationError" }

// Error satisfies the builtin error interface
func (e PerfPlanValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPerfPlan.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PerfPlanValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PerfPlanValidationError{}

// Validate checks the field values on SearchPerfPlanItem with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SearchPerfPlanItem) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SearchPerfPlanItem with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SearchPerfPlanItemMultiError, or nil if none found.
func (m *SearchPerfPlanItem) ValidateAll() error {
	return m.validate(true)
}

func (m *SearchPerfPlanItem) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ProjectId

	// no validation rules for PlanId

	// no validation rules for Name

	// no validation rules for Description

	// no validation rules for Type

	// no validation rules for State

	// no validation rules for Protocol

	// no validation rules for Duration

	// no validation rules for TargetEnv

	// no validation rules for NumberOfCase

	// no validation rules for NumberOfApi

	for idx, item := range m.GetStatsOfApi() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SearchPerfPlanItemValidationError{
						field:  fmt.Sprintf("StatsOfApi[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SearchPerfPlanItemValidationError{
						field:  fmt.Sprintf("StatsOfApi[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SearchPerfPlanItemValidationError{
					field:  fmt.Sprintf("StatsOfApi[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for MaintainedBy

	// no validation rules for CreatedBy

	// no validation rules for UpdatedBy

	// no validation rules for CreatedAt

	// no validation rules for UpdatedAt

	if len(errors) > 0 {
		return SearchPerfPlanItemMultiError(errors)
	}

	return nil
}

// SearchPerfPlanItemMultiError is an error wrapping multiple validation errors
// returned by SearchPerfPlanItem.ValidateAll() if the designated constraints
// aren't met.
type SearchPerfPlanItemMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SearchPerfPlanItemMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SearchPerfPlanItemMultiError) AllErrors() []error { return m }

// SearchPerfPlanItemValidationError is the validation error returned by
// SearchPerfPlanItem.Validate if the designated constraints aren't met.
type SearchPerfPlanItemValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SearchPerfPlanItemValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SearchPerfPlanItemValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SearchPerfPlanItemValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SearchPerfPlanItemValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SearchPerfPlanItemValidationError) ErrorName() string {
	return "SearchPerfPlanItemValidationError"
}

// Error satisfies the builtin error interface
func (e SearchPerfPlanItemValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSearchPerfPlanItem.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SearchPerfPlanItemValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SearchPerfPlanItemValidationError{}

// Validate checks the field values on StatsOfApi with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *StatsOfApi) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on StatsOfApi with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in StatsOfApiMultiError, or
// nil if none found.
func (m *StatsOfApi) ValidateAll() error {
	return m.validate(true)
}

func (m *StatsOfApi) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TargetRps

	// no validation rules for NumberOfApi

	if len(errors) > 0 {
		return StatsOfApiMultiError(errors)
	}

	return nil
}

// StatsOfApiMultiError is an error wrapping multiple validation errors
// returned by StatsOfApi.ValidateAll() if the designated constraints aren't met.
type StatsOfApiMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m StatsOfApiMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m StatsOfApiMultiError) AllErrors() []error { return m }

// StatsOfApiValidationError is the validation error returned by
// StatsOfApi.Validate if the designated constraints aren't met.
type StatsOfApiValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e StatsOfApiValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e StatsOfApiValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e StatsOfApiValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e StatsOfApiValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e StatsOfApiValidationError) ErrorName() string { return "StatsOfApiValidationError" }

// Error satisfies the builtin error interface
func (e StatsOfApiValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sStatsOfApi.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = StatsOfApiValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = StatsOfApiValidationError{}

// Validate checks the field values on SearchCaseInPerfPlanItem with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SearchCaseInPerfPlanItem) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SearchCaseInPerfPlanItem with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SearchCaseInPerfPlanItemMultiError, or nil if none found.
func (m *SearchCaseInPerfPlanItem) ValidateAll() error {
	return m.validate(true)
}

func (m *SearchCaseInPerfPlanItem) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ProjectId

	// no validation rules for CaseId

	// no validation rules for Name

	// no validation rules for Description

	// no validation rules for Extension

	// no validation rules for Hash

	// no validation rules for Size

	// no validation rules for TargetRps

	// no validation rules for InitialRps

	// no validation rules for StepHeight

	// no validation rules for StepDuration

	// no validation rules for PerfDataId

	// no validation rules for NumberOfVu

	// no validation rules for NumberOfLg

	// no validation rules for RequestsOfCpu

	// no validation rules for RequestsOfMemory

	// no validation rules for LimitsOfCpu

	// no validation rules for LimitsOfMemory

	// no validation rules for CreatedBy

	// no validation rules for UpdatedBy

	// no validation rules for CreatedAt

	// no validation rules for UpdatedAt

	if len(errors) > 0 {
		return SearchCaseInPerfPlanItemMultiError(errors)
	}

	return nil
}

// SearchCaseInPerfPlanItemMultiError is an error wrapping multiple validation
// errors returned by SearchCaseInPerfPlanItem.ValidateAll() if the designated
// constraints aren't met.
type SearchCaseInPerfPlanItemMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SearchCaseInPerfPlanItemMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SearchCaseInPerfPlanItemMultiError) AllErrors() []error { return m }

// SearchCaseInPerfPlanItemValidationError is the validation error returned by
// SearchCaseInPerfPlanItem.Validate if the designated constraints aren't met.
type SearchCaseInPerfPlanItemValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SearchCaseInPerfPlanItemValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SearchCaseInPerfPlanItemValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SearchCaseInPerfPlanItemValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SearchCaseInPerfPlanItemValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SearchCaseInPerfPlanItemValidationError) ErrorName() string {
	return "SearchCaseInPerfPlanItemValidationError"
}

// Error satisfies the builtin error interface
func (e SearchCaseInPerfPlanItemValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSearchCaseInPerfPlanItem.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SearchCaseInPerfPlanItemValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SearchCaseInPerfPlanItemValidationError{}

// Validate checks the field values on PerfPlanV2 with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *PerfPlanV2) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PerfPlanV2 with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in PerfPlanV2MultiError, or
// nil if none found.
func (m *PerfPlanV2) ValidateAll() error {
	return m.validate(true)
}

func (m *PerfPlanV2) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ProjectId

	// no validation rules for PlanId

	// no validation rules for CategoryId

	// no validation rules for Name

	// no validation rules for Description

	// no validation rules for Type

	// no validation rules for CronExpression

	// no validation rules for Protocol

	// no validation rules for TargetEnv

	// no validation rules for ProtobufConfigId

	// no validation rules for GeneralConfigId

	// no validation rules for AccountConfigId

	for idx, item := range m.GetAuthRateLimits() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, PerfPlanV2ValidationError{
						field:  fmt.Sprintf("AuthRateLimits[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, PerfPlanV2ValidationError{
						field:  fmt.Sprintf("AuthRateLimits[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return PerfPlanV2ValidationError{
					field:  fmt.Sprintf("AuthRateLimits[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for CustomDuration

	// no validation rules for Duration

	// no validation rules for CreateLarkChat

	// no validation rules for LarkChatId

	// no validation rules for AdvancedNotification

	// no validation rules for State

	// no validation rules for MaintainedBy

	// no validation rules for CreatedBy

	// no validation rules for UpdatedBy

	// no validation rules for CreatedAt

	// no validation rules for UpdatedAt

	if len(errors) > 0 {
		return PerfPlanV2MultiError(errors)
	}

	return nil
}

// PerfPlanV2MultiError is an error wrapping multiple validation errors
// returned by PerfPlanV2.ValidateAll() if the designated constraints aren't met.
type PerfPlanV2MultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PerfPlanV2MultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PerfPlanV2MultiError) AllErrors() []error { return m }

// PerfPlanV2ValidationError is the validation error returned by
// PerfPlanV2.Validate if the designated constraints aren't met.
type PerfPlanV2ValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PerfPlanV2ValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PerfPlanV2ValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PerfPlanV2ValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PerfPlanV2ValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PerfPlanV2ValidationError) ErrorName() string { return "PerfPlanV2ValidationError" }

// Error satisfies the builtin error interface
func (e PerfPlanV2ValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPerfPlanV2.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PerfPlanV2ValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PerfPlanV2ValidationError{}

// Validate checks the field values on SearchPerfPlanV2Item with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SearchPerfPlanV2Item) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SearchPerfPlanV2Item with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SearchPerfPlanV2ItemMultiError, or nil if none found.
func (m *SearchPerfPlanV2Item) ValidateAll() error {
	return m.validate(true)
}

func (m *SearchPerfPlanV2Item) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ProjectId

	// no validation rules for PlanId

	// no validation rules for CategoryId

	// no validation rules for Name

	// no validation rules for Description

	// no validation rules for Type

	// no validation rules for CronExpression

	// no validation rules for Protocol

	// no validation rules for TargetEnv

	// no validation rules for CustomDuration

	// no validation rules for Duration

	// no validation rules for NumberOfCases

	// no validation rules for NumberOfSteps

	for idx, item := range m.GetStatsOfStep() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SearchPerfPlanV2ItemValidationError{
						field:  fmt.Sprintf("StatsOfStep[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SearchPerfPlanV2ItemValidationError{
						field:  fmt.Sprintf("StatsOfStep[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SearchPerfPlanV2ItemValidationError{
					field:  fmt.Sprintf("StatsOfStep[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for State

	// no validation rules for MaintainedBy

	// no validation rules for CreatedBy

	// no validation rules for UpdatedBy

	// no validation rules for CreatedAt

	// no validation rules for UpdatedAt

	if len(errors) > 0 {
		return SearchPerfPlanV2ItemMultiError(errors)
	}

	return nil
}

// SearchPerfPlanV2ItemMultiError is an error wrapping multiple validation
// errors returned by SearchPerfPlanV2Item.ValidateAll() if the designated
// constraints aren't met.
type SearchPerfPlanV2ItemMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SearchPerfPlanV2ItemMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SearchPerfPlanV2ItemMultiError) AllErrors() []error { return m }

// SearchPerfPlanV2ItemValidationError is the validation error returned by
// SearchPerfPlanV2Item.Validate if the designated constraints aren't met.
type SearchPerfPlanV2ItemValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SearchPerfPlanV2ItemValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SearchPerfPlanV2ItemValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SearchPerfPlanV2ItemValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SearchPerfPlanV2ItemValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SearchPerfPlanV2ItemValidationError) ErrorName() string {
	return "SearchPerfPlanV2ItemValidationError"
}

// Error satisfies the builtin error interface
func (e SearchPerfPlanV2ItemValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSearchPerfPlanV2Item.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SearchPerfPlanV2ItemValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SearchPerfPlanV2ItemValidationError{}

// Validate checks the field values on StatsOfStep with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *StatsOfStep) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on StatsOfStep with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in StatsOfStepMultiError, or
// nil if none found.
func (m *StatsOfStep) ValidateAll() error {
	return m.validate(true)
}

func (m *StatsOfStep) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TargetRps

	// no validation rules for NumberOfSteps

	if len(errors) > 0 {
		return StatsOfStepMultiError(errors)
	}

	return nil
}

// StatsOfStepMultiError is an error wrapping multiple validation errors
// returned by StatsOfStep.ValidateAll() if the designated constraints aren't met.
type StatsOfStepMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m StatsOfStepMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m StatsOfStepMultiError) AllErrors() []error { return m }

// StatsOfStepValidationError is the validation error returned by
// StatsOfStep.Validate if the designated constraints aren't met.
type StatsOfStepValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e StatsOfStepValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e StatsOfStepValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e StatsOfStepValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e StatsOfStepValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e StatsOfStepValidationError) ErrorName() string { return "StatsOfStepValidationError" }

// Error satisfies the builtin error interface
func (e StatsOfStepValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sStatsOfStep.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = StatsOfStepValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = StatsOfStepValidationError{}

// Validate checks the field values on PerfPlanCaseV2Item with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *PerfPlanCaseV2Item) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PerfPlanCaseV2Item with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PerfPlanCaseV2ItemMultiError, or nil if none found.
func (m *PerfPlanCaseV2Item) ValidateAll() error {
	return m.validate(true)
}

func (m *PerfPlanCaseV2Item) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetCaseId()) > 64 {
		err := PerfPlanCaseV2ItemValidationError{
			field:  "CaseId",
			reason: "value length must be at most 64 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_PerfPlanCaseV2Item_CaseId_Pattern.MatchString(m.GetCaseId()) {
		err := PerfPlanCaseV2ItemValidationError{
			field:  "CaseId",
			reason: "value does not match regex pattern \"(?:^perf_case_id:.+?)\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetPerfDataId() != "" {

		if utf8.RuneCountInString(m.GetPerfDataId()) > 64 {
			err := PerfPlanCaseV2ItemValidationError{
				field:  "PerfDataId",
				reason: "value length must be at most 64 runes",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if !_PerfPlanCaseV2Item_PerfDataId_Pattern.MatchString(m.GetPerfDataId()) {
			err := PerfPlanCaseV2ItemValidationError{
				field:  "PerfDataId",
				reason: "value does not match regex pattern \"(?:^perf_data_id:.+?)\"",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	// no validation rules for CustomVu

	if m.GetNumberOfVu() < 0 {
		err := PerfPlanCaseV2ItemValidationError{
			field:  "NumberOfVu",
			reason: "value must be greater than or equal to 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for CustomLg

	if m.GetNumberOfLg() < 0 {
		err := PerfPlanCaseV2ItemValidationError{
			field:  "NumberOfLg",
			reason: "value must be greater than or equal to 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetRequestsOfCpu() != "" {

		if utf8.RuneCountInString(m.GetRequestsOfCpu()) < 1 {
			err := PerfPlanCaseV2ItemValidationError{
				field:  "RequestsOfCpu",
				reason: "value length must be at least 1 runes",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if m.GetRequestsOfMemory() != "" {

		if utf8.RuneCountInString(m.GetRequestsOfMemory()) < 1 {
			err := PerfPlanCaseV2ItemValidationError{
				field:  "RequestsOfMemory",
				reason: "value length must be at least 1 runes",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if m.GetLimitsOfCpu() != "" {

		if utf8.RuneCountInString(m.GetLimitsOfCpu()) < 1 {
			err := PerfPlanCaseV2ItemValidationError{
				field:  "LimitsOfCpu",
				reason: "value length must be at least 1 runes",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if m.GetLimitsOfMemory() != "" {

		if utf8.RuneCountInString(m.GetLimitsOfMemory()) < 1 {
			err := PerfPlanCaseV2ItemValidationError{
				field:  "LimitsOfMemory",
				reason: "value length must be at least 1 runes",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	for idx, item := range m.GetRateLimits() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, PerfPlanCaseV2ItemValidationError{
						field:  fmt.Sprintf("RateLimits[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, PerfPlanCaseV2ItemValidationError{
						field:  fmt.Sprintf("RateLimits[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return PerfPlanCaseV2ItemValidationError{
					field:  fmt.Sprintf("RateLimits[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return PerfPlanCaseV2ItemMultiError(errors)
	}

	return nil
}

// PerfPlanCaseV2ItemMultiError is an error wrapping multiple validation errors
// returned by PerfPlanCaseV2Item.ValidateAll() if the designated constraints
// aren't met.
type PerfPlanCaseV2ItemMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PerfPlanCaseV2ItemMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PerfPlanCaseV2ItemMultiError) AllErrors() []error { return m }

// PerfPlanCaseV2ItemValidationError is the validation error returned by
// PerfPlanCaseV2Item.Validate if the designated constraints aren't met.
type PerfPlanCaseV2ItemValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PerfPlanCaseV2ItemValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PerfPlanCaseV2ItemValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PerfPlanCaseV2ItemValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PerfPlanCaseV2ItemValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PerfPlanCaseV2ItemValidationError) ErrorName() string {
	return "PerfPlanCaseV2ItemValidationError"
}

// Error satisfies the builtin error interface
func (e PerfPlanCaseV2ItemValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPerfPlanCaseV2Item.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PerfPlanCaseV2ItemValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PerfPlanCaseV2ItemValidationError{}

var _PerfPlanCaseV2Item_CaseId_Pattern = regexp.MustCompile("(?:^perf_case_id:.+?)")

var _PerfPlanCaseV2Item_PerfDataId_Pattern = regexp.MustCompile("(?:^perf_data_id:.+?)")

// Validate checks the field values on SearchCaseInPerfPlanV2Item with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SearchCaseInPerfPlanV2Item) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SearchCaseInPerfPlanV2Item with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SearchCaseInPerfPlanV2ItemMultiError, or nil if none found.
func (m *SearchCaseInPerfPlanV2Item) ValidateAll() error {
	return m.validate(true)
}

func (m *SearchCaseInPerfPlanV2Item) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ProjectId

	// no validation rules for CategoryId

	// no validation rules for CaseId

	// no validation rules for Name

	// no validation rules for Description

	// no validation rules for Protocol

	for idx, item := range m.GetRateLimits() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SearchCaseInPerfPlanV2ItemValidationError{
						field:  fmt.Sprintf("RateLimits[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SearchCaseInPerfPlanV2ItemValidationError{
						field:  fmt.Sprintf("RateLimits[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SearchCaseInPerfPlanV2ItemValidationError{
					field:  fmt.Sprintf("RateLimits[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetSerialSteps() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SearchCaseInPerfPlanV2ItemValidationError{
						field:  fmt.Sprintf("SerialSteps[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SearchCaseInPerfPlanV2ItemValidationError{
						field:  fmt.Sprintf("SerialSteps[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SearchCaseInPerfPlanV2ItemValidationError{
					field:  fmt.Sprintf("SerialSteps[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetParallelSteps() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SearchCaseInPerfPlanV2ItemValidationError{
						field:  fmt.Sprintf("ParallelSteps[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SearchCaseInPerfPlanV2ItemValidationError{
						field:  fmt.Sprintf("ParallelSteps[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SearchCaseInPerfPlanV2ItemValidationError{
					field:  fmt.Sprintf("ParallelSteps[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for NumberOfSteps

	// no validation rules for TargetRps

	// no validation rules for PerfDataId

	// no validation rules for CustomVu

	// no validation rules for NumberOfVu

	// no validation rules for CustomLg

	// no validation rules for NumberOfLg

	// no validation rules for RequestsOfCpu

	// no validation rules for RequestsOfMemory

	// no validation rules for LimitsOfCpu

	// no validation rules for LimitsOfMemory

	// no validation rules for State

	// no validation rules for MaintainedBy

	// no validation rules for CreatedBy

	// no validation rules for UpdatedBy

	// no validation rules for CreatedAt

	// no validation rules for UpdatedAt

	if len(errors) > 0 {
		return SearchCaseInPerfPlanV2ItemMultiError(errors)
	}

	return nil
}

// SearchCaseInPerfPlanV2ItemMultiError is an error wrapping multiple
// validation errors returned by SearchCaseInPerfPlanV2Item.ValidateAll() if
// the designated constraints aren't met.
type SearchCaseInPerfPlanV2ItemMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SearchCaseInPerfPlanV2ItemMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SearchCaseInPerfPlanV2ItemMultiError) AllErrors() []error { return m }

// SearchCaseInPerfPlanV2ItemValidationError is the validation error returned
// by SearchCaseInPerfPlanV2Item.Validate if the designated constraints aren't met.
type SearchCaseInPerfPlanV2ItemValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SearchCaseInPerfPlanV2ItemValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SearchCaseInPerfPlanV2ItemValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SearchCaseInPerfPlanV2ItemValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SearchCaseInPerfPlanV2ItemValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SearchCaseInPerfPlanV2ItemValidationError) ErrorName() string {
	return "SearchCaseInPerfPlanV2ItemValidationError"
}

// Error satisfies the builtin error interface
func (e SearchCaseInPerfPlanV2ItemValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSearchCaseInPerfPlanV2Item.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SearchCaseInPerfPlanV2ItemValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SearchCaseInPerfPlanV2ItemValidationError{}

// Validate checks the field values on SearchProtobufInPerfPlanV2Item with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SearchProtobufInPerfPlanV2Item) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SearchProtobufInPerfPlanV2Item with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// SearchProtobufInPerfPlanV2ItemMultiError, or nil if none found.
func (m *SearchProtobufInPerfPlanV2Item) ValidateAll() error {
	return m.validate(true)
}

func (m *SearchProtobufInPerfPlanV2Item) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ProjectId

	// no validation rules for ConfigId

	// no validation rules for Name

	// no validation rules for Description

	// no validation rules for GitConfigId

	// no validation rules for ImportPath

	// no validation rules for CreatedBy

	// no validation rules for UpdatedBy

	// no validation rules for CreatedAt

	// no validation rules for UpdatedAt

	if len(errors) > 0 {
		return SearchProtobufInPerfPlanV2ItemMultiError(errors)
	}

	return nil
}

// SearchProtobufInPerfPlanV2ItemMultiError is an error wrapping multiple
// validation errors returned by SearchProtobufInPerfPlanV2Item.ValidateAll()
// if the designated constraints aren't met.
type SearchProtobufInPerfPlanV2ItemMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SearchProtobufInPerfPlanV2ItemMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SearchProtobufInPerfPlanV2ItemMultiError) AllErrors() []error { return m }

// SearchProtobufInPerfPlanV2ItemValidationError is the validation error
// returned by SearchProtobufInPerfPlanV2Item.Validate if the designated
// constraints aren't met.
type SearchProtobufInPerfPlanV2ItemValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SearchProtobufInPerfPlanV2ItemValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SearchProtobufInPerfPlanV2ItemValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SearchProtobufInPerfPlanV2ItemValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SearchProtobufInPerfPlanV2ItemValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SearchProtobufInPerfPlanV2ItemValidationError) ErrorName() string {
	return "SearchProtobufInPerfPlanV2ItemValidationError"
}

// Error satisfies the builtin error interface
func (e SearchProtobufInPerfPlanV2ItemValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSearchProtobufInPerfPlanV2Item.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SearchProtobufInPerfPlanV2ItemValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SearchProtobufInPerfPlanV2ItemValidationError{}

// Validate checks the field values on SearchRuleInPerfPlanV2Item with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SearchRuleInPerfPlanV2Item) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SearchRuleInPerfPlanV2Item with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SearchRuleInPerfPlanV2ItemMultiError, or nil if none found.
func (m *SearchRuleInPerfPlanV2Item) ValidateAll() error {
	return m.validate(true)
}

func (m *SearchRuleInPerfPlanV2Item) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ProjectId

	// no validation rules for RuleId

	// no validation rules for Name

	// no validation rules for Description

	// no validation rules for MetricType

	// no validation rules for Threshold

	// no validation rules for Duration

	// no validation rules for State

	// no validation rules for CreatedBy

	// no validation rules for UpdatedBy

	// no validation rules for CreatedAt

	// no validation rules for UpdatedAt

	if len(errors) > 0 {
		return SearchRuleInPerfPlanV2ItemMultiError(errors)
	}

	return nil
}

// SearchRuleInPerfPlanV2ItemMultiError is an error wrapping multiple
// validation errors returned by SearchRuleInPerfPlanV2Item.ValidateAll() if
// the designated constraints aren't met.
type SearchRuleInPerfPlanV2ItemMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SearchRuleInPerfPlanV2ItemMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SearchRuleInPerfPlanV2ItemMultiError) AllErrors() []error { return m }

// SearchRuleInPerfPlanV2ItemValidationError is the validation error returned
// by SearchRuleInPerfPlanV2Item.Validate if the designated constraints aren't met.
type SearchRuleInPerfPlanV2ItemValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SearchRuleInPerfPlanV2ItemValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SearchRuleInPerfPlanV2ItemValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SearchRuleInPerfPlanV2ItemValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SearchRuleInPerfPlanV2ItemValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SearchRuleInPerfPlanV2ItemValidationError) ErrorName() string {
	return "SearchRuleInPerfPlanV2ItemValidationError"
}

// Error satisfies the builtin error interface
func (e SearchRuleInPerfPlanV2ItemValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSearchRuleInPerfPlanV2Item.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SearchRuleInPerfPlanV2ItemValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SearchRuleInPerfPlanV2ItemValidationError{}
