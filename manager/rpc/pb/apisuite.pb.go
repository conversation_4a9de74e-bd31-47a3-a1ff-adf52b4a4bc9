// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.1
// 	protoc        v5.29.2
// source: manager/apisuite.proto

package pb

import (
	reflect "reflect"
	sync "sync"

	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ApiSuite struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	ProjectId         string                 `protobuf:"bytes,1,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`                                                       // 项目ID
	CategoryId        string                 `protobuf:"bytes,2,opt,name=category_id,json=categoryId,proto3" json:"category_id,omitempty"`                                                    // 所属分类ID
	SuiteId           string                 `protobuf:"bytes,3,opt,name=suite_id,json=suiteId,proto3" json:"suite_id,omitempty"`                                                             // 集合ID
	Name              string                 `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`                                                                                  // 集合名称
	Description       string                 `protobuf:"bytes,5,opt,name=description,proto3" json:"description,omitempty"`                                                                    // 集合描述
	Priority          int64                  `protobuf:"varint,6,opt,name=priority,proto3" json:"priority,omitempty"`                                                                         // 优先级
	Tags              []string               `protobuf:"bytes,7,rep,name=tags,proto3" json:"tags,omitempty"`                                                                                  // 标签
	State             CommonState            `protobuf:"varint,8,opt,name=state,proto3,enum=manager.CommonState" json:"state,omitempty"`                                                      // 状态
	CaseExecutionMode ExecutionMode          `protobuf:"varint,9,opt,name=case_execution_mode,json=caseExecutionMode,proto3,enum=manager.ExecutionMode" json:"case_execution_mode,omitempty"` // 用例执行方式
	MaintainedBy      string                 `protobuf:"bytes,10,opt,name=maintained_by,json=maintainedBy,proto3" json:"maintained_by,omitempty"`                                             // 维护者
	CreatedBy         string                 `protobuf:"bytes,11,opt,name=created_by,json=createdBy,proto3" json:"created_by,omitempty"`                                                      // 创建者
	UpdatedBy         string                 `protobuf:"bytes,12,opt,name=updated_by,json=updatedBy,proto3" json:"updated_by,omitempty"`                                                      // 更新者
	CreatedAt         int64                  `protobuf:"varint,13,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`                                                     // 创建时间
	UpdatedAt         int64                  `protobuf:"varint,14,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`                                                     // 更新时间
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *ApiSuite) Reset() {
	*x = ApiSuite{}
	mi := &file_manager_apisuite_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ApiSuite) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ApiSuite) ProtoMessage() {}

func (x *ApiSuite) ProtoReflect() protoreflect.Message {
	mi := &file_manager_apisuite_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ApiSuite.ProtoReflect.Descriptor instead.
func (*ApiSuite) Descriptor() ([]byte, []int) {
	return file_manager_apisuite_proto_rawDescGZIP(), []int{0}
}

func (x *ApiSuite) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *ApiSuite) GetCategoryId() string {
	if x != nil {
		return x.CategoryId
	}
	return ""
}

func (x *ApiSuite) GetSuiteId() string {
	if x != nil {
		return x.SuiteId
	}
	return ""
}

func (x *ApiSuite) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ApiSuite) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *ApiSuite) GetPriority() int64 {
	if x != nil {
		return x.Priority
	}
	return 0
}

func (x *ApiSuite) GetTags() []string {
	if x != nil {
		return x.Tags
	}
	return nil
}

func (x *ApiSuite) GetState() CommonState {
	if x != nil {
		return x.State
	}
	return CommonState_CS_NULL
}

func (x *ApiSuite) GetCaseExecutionMode() ExecutionMode {
	if x != nil {
		return x.CaseExecutionMode
	}
	return ExecutionMode_EM_NULL
}

func (x *ApiSuite) GetMaintainedBy() string {
	if x != nil {
		return x.MaintainedBy
	}
	return ""
}

func (x *ApiSuite) GetCreatedBy() string {
	if x != nil {
		return x.CreatedBy
	}
	return ""
}

func (x *ApiSuite) GetUpdatedBy() string {
	if x != nil {
		return x.UpdatedBy
	}
	return ""
}

func (x *ApiSuite) GetCreatedAt() int64 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

func (x *ApiSuite) GetUpdatedAt() int64 {
	if x != nil {
		return x.UpdatedAt
	}
	return 0
}

type SearchApiSuiteReferenceItem struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	ProjectId      string                 `protobuf:"bytes,1,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`                                           // 项目ID
	SuiteId        string                 `protobuf:"bytes,2,opt,name=suite_id,json=suiteId,proto3" json:"suite_id,omitempty"`                                                 // 集合ID
	ReferenceType  string                 `protobuf:"bytes,3,opt,name=reference_type,json=referenceType,proto3" json:"reference_type,omitempty"`                               // 引用对象类型
	ReferenceId    string                 `protobuf:"bytes,4,opt,name=reference_id,json=referenceId,proto3" json:"reference_id,omitempty"`                                     // 引用对象ID
	Name           string                 `protobuf:"bytes,5,opt,name=name,proto3" json:"name,omitempty"`                                                                      // 引用对象名称
	Description    string                 `protobuf:"bytes,6,opt,name=description,proto3" json:"description,omitempty"`                                                        // 引用对象描述
	Priority       int64                  `protobuf:"varint,7,opt,name=priority,proto3" json:"priority,omitempty"`                                                             // 优先级
	Tags           []string               `protobuf:"bytes,8,rep,name=tags,proto3" json:"tags,omitempty"`                                                                      // 标签
	State          CommonState            `protobuf:"varint,9,opt,name=state,proto3,enum=manager.CommonState" json:"state,omitempty"`                                          // 状态
	ReferenceState CommonState            `protobuf:"varint,10,opt,name=reference_state,json=referenceState,proto3,enum=manager.CommonState" json:"reference_state,omitempty"` // 引用状态
	MaintainedBy   string                 `protobuf:"bytes,11,opt,name=maintained_by,json=maintainedBy,proto3" json:"maintained_by,omitempty"`                                 // 维护者
	CreatedBy      string                 `protobuf:"bytes,12,opt,name=created_by,json=createdBy,proto3" json:"created_by,omitempty"`                                          // 创建者
	UpdatedBy      string                 `protobuf:"bytes,13,opt,name=updated_by,json=updatedBy,proto3" json:"updated_by,omitempty"`                                          // 更新者
	CreatedAt      int64                  `protobuf:"varint,14,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`                                         // 创建时间
	UpdatedAt      int64                  `protobuf:"varint,15,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`                                         // 更新时间
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *SearchApiSuiteReferenceItem) Reset() {
	*x = SearchApiSuiteReferenceItem{}
	mi := &file_manager_apisuite_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SearchApiSuiteReferenceItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchApiSuiteReferenceItem) ProtoMessage() {}

func (x *SearchApiSuiteReferenceItem) ProtoReflect() protoreflect.Message {
	mi := &file_manager_apisuite_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchApiSuiteReferenceItem.ProtoReflect.Descriptor instead.
func (*SearchApiSuiteReferenceItem) Descriptor() ([]byte, []int) {
	return file_manager_apisuite_proto_rawDescGZIP(), []int{1}
}

func (x *SearchApiSuiteReferenceItem) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *SearchApiSuiteReferenceItem) GetSuiteId() string {
	if x != nil {
		return x.SuiteId
	}
	return ""
}

func (x *SearchApiSuiteReferenceItem) GetReferenceType() string {
	if x != nil {
		return x.ReferenceType
	}
	return ""
}

func (x *SearchApiSuiteReferenceItem) GetReferenceId() string {
	if x != nil {
		return x.ReferenceId
	}
	return ""
}

func (x *SearchApiSuiteReferenceItem) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *SearchApiSuiteReferenceItem) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *SearchApiSuiteReferenceItem) GetPriority() int64 {
	if x != nil {
		return x.Priority
	}
	return 0
}

func (x *SearchApiSuiteReferenceItem) GetTags() []string {
	if x != nil {
		return x.Tags
	}
	return nil
}

func (x *SearchApiSuiteReferenceItem) GetState() CommonState {
	if x != nil {
		return x.State
	}
	return CommonState_CS_NULL
}

func (x *SearchApiSuiteReferenceItem) GetReferenceState() CommonState {
	if x != nil {
		return x.ReferenceState
	}
	return CommonState_CS_NULL
}

func (x *SearchApiSuiteReferenceItem) GetMaintainedBy() string {
	if x != nil {
		return x.MaintainedBy
	}
	return ""
}

func (x *SearchApiSuiteReferenceItem) GetCreatedBy() string {
	if x != nil {
		return x.CreatedBy
	}
	return ""
}

func (x *SearchApiSuiteReferenceItem) GetUpdatedBy() string {
	if x != nil {
		return x.UpdatedBy
	}
	return ""
}

func (x *SearchApiSuiteReferenceItem) GetCreatedAt() int64 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

func (x *SearchApiSuiteReferenceItem) GetUpdatedAt() int64 {
	if x != nil {
		return x.UpdatedAt
	}
	return 0
}

type SearchCaseInApiSuiteItem struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ProjectId     string                 `protobuf:"bytes,1,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`              // 项目ID
	CategoryId    string                 `protobuf:"bytes,2,opt,name=category_id,json=categoryId,proto3" json:"category_id,omitempty"`           // 场景用例的所属分类ID
	DocumentId    string                 `protobuf:"bytes,3,opt,name=document_id,json=documentId,proto3" json:"document_id,omitempty"`           // 接口用例的接口文档ID
	CaseType      string                 `protobuf:"bytes,4,opt,name=case_type,json=caseType,proto3" json:"case_type,omitempty"`                 // 用例类型
	CaseId        string                 `protobuf:"bytes,5,opt,name=case_id,json=caseId,proto3" json:"case_id,omitempty"`                       // 用例ID
	Name          string                 `protobuf:"bytes,6,opt,name=name,proto3" json:"name,omitempty"`                                         // 用例名称
	Description   string                 `protobuf:"bytes,7,opt,name=description,proto3" json:"description,omitempty"`                           // 用例描述
	Priority      int64                  `protobuf:"varint,8,opt,name=priority,proto3" json:"priority,omitempty"`                                // 优先级
	Tags          []string               `protobuf:"bytes,9,rep,name=tags,proto3" json:"tags,omitempty"`                                         // 标签
	State         ResourceState          `protobuf:"varint,10,opt,name=state,proto3,enum=manager.ResourceState" json:"state,omitempty"`          // 状态
	AccountConfig *AccountConfig         `protobuf:"bytes,11,opt,name=account_config,json=accountConfig,proto3" json:"account_config,omitempty"` // 池账号配置信息
	Version       string                 `protobuf:"bytes,12,opt,name=version,proto3" json:"version,omitempty"`                                  // 用例版本
	MaintainedBy  string                 `protobuf:"bytes,13,opt,name=maintained_by,json=maintainedBy,proto3" json:"maintained_by,omitempty"`    // 维护者
	CreatedBy     string                 `protobuf:"bytes,14,opt,name=created_by,json=createdBy,proto3" json:"created_by,omitempty"`             // 创建者
	UpdatedBy     string                 `protobuf:"bytes,15,opt,name=updated_by,json=updatedBy,proto3" json:"updated_by,omitempty"`             // 更新者
	CreatedAt     int64                  `protobuf:"varint,16,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`            // 创建时间
	UpdatedAt     int64                  `protobuf:"varint,17,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`            // 更新时间
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SearchCaseInApiSuiteItem) Reset() {
	*x = SearchCaseInApiSuiteItem{}
	mi := &file_manager_apisuite_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SearchCaseInApiSuiteItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchCaseInApiSuiteItem) ProtoMessage() {}

func (x *SearchCaseInApiSuiteItem) ProtoReflect() protoreflect.Message {
	mi := &file_manager_apisuite_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchCaseInApiSuiteItem.ProtoReflect.Descriptor instead.
func (*SearchCaseInApiSuiteItem) Descriptor() ([]byte, []int) {
	return file_manager_apisuite_proto_rawDescGZIP(), []int{2}
}

func (x *SearchCaseInApiSuiteItem) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *SearchCaseInApiSuiteItem) GetCategoryId() string {
	if x != nil {
		return x.CategoryId
	}
	return ""
}

func (x *SearchCaseInApiSuiteItem) GetDocumentId() string {
	if x != nil {
		return x.DocumentId
	}
	return ""
}

func (x *SearchCaseInApiSuiteItem) GetCaseType() string {
	if x != nil {
		return x.CaseType
	}
	return ""
}

func (x *SearchCaseInApiSuiteItem) GetCaseId() string {
	if x != nil {
		return x.CaseId
	}
	return ""
}

func (x *SearchCaseInApiSuiteItem) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *SearchCaseInApiSuiteItem) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *SearchCaseInApiSuiteItem) GetPriority() int64 {
	if x != nil {
		return x.Priority
	}
	return 0
}

func (x *SearchCaseInApiSuiteItem) GetTags() []string {
	if x != nil {
		return x.Tags
	}
	return nil
}

func (x *SearchCaseInApiSuiteItem) GetState() ResourceState {
	if x != nil {
		return x.State
	}
	return ResourceState_RS_NULL
}

func (x *SearchCaseInApiSuiteItem) GetAccountConfig() *AccountConfig {
	if x != nil {
		return x.AccountConfig
	}
	return nil
}

func (x *SearchCaseInApiSuiteItem) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *SearchCaseInApiSuiteItem) GetMaintainedBy() string {
	if x != nil {
		return x.MaintainedBy
	}
	return ""
}

func (x *SearchCaseInApiSuiteItem) GetCreatedBy() string {
	if x != nil {
		return x.CreatedBy
	}
	return ""
}

func (x *SearchCaseInApiSuiteItem) GetUpdatedBy() string {
	if x != nil {
		return x.UpdatedBy
	}
	return ""
}

func (x *SearchCaseInApiSuiteItem) GetCreatedAt() int64 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

func (x *SearchCaseInApiSuiteItem) GetUpdatedAt() int64 {
	if x != nil {
		return x.UpdatedAt
	}
	return 0
}

type CaseTypeId struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	CaseType      string                 `protobuf:"bytes,1,opt,name=case_type,json=caseType,proto3" json:"case_type,omitempty"` // 用例类型
	CaseId        string                 `protobuf:"bytes,2,opt,name=case_id,json=caseId,proto3" json:"case_id,omitempty"`       // 用例ID
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CaseTypeId) Reset() {
	*x = CaseTypeId{}
	mi := &file_manager_apisuite_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CaseTypeId) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CaseTypeId) ProtoMessage() {}

func (x *CaseTypeId) ProtoReflect() protoreflect.Message {
	mi := &file_manager_apisuite_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CaseTypeId.ProtoReflect.Descriptor instead.
func (*CaseTypeId) Descriptor() ([]byte, []int) {
	return file_manager_apisuite_proto_rawDescGZIP(), []int{3}
}

func (x *CaseTypeId) GetCaseType() string {
	if x != nil {
		return x.CaseType
	}
	return ""
}

func (x *CaseTypeId) GetCaseId() string {
	if x != nil {
		return x.CaseId
	}
	return ""
}

var File_manager_apisuite_proto protoreflect.FileDescriptor

var file_manager_apisuite_proto_rawDesc = []byte{
	0x0a, 0x16, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x75, 0x69,
	0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x07, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65,
	0x72, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69,
	0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x12, 0x6d, 0x61, 0x6e, 0x61,
	0x67, 0x65, 0x72, 0x2f, 0x62, 0x61, 0x73, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xe0,
	0x03, 0x0a, 0x08, 0x41, 0x70, 0x69, 0x53, 0x75, 0x69, 0x74, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x70,
	0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x61,
	0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0a, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x73,
	0x75, 0x69, 0x74, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x73,
	0x75, 0x69, 0x74, 0x65, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1a, 0x0a, 0x08,
	0x70, 0x72, 0x69, 0x6f, 0x72, 0x69, 0x74, 0x79, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08,
	0x70, 0x72, 0x69, 0x6f, 0x72, 0x69, 0x74, 0x79, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x61, 0x67, 0x73,
	0x18, 0x07, 0x20, 0x03, 0x28, 0x09, 0x52, 0x04, 0x74, 0x61, 0x67, 0x73, 0x12, 0x2a, 0x0a, 0x05,
	0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x6d, 0x61,
	0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74,
	0x65, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x12, 0x46, 0x0a, 0x13, 0x63, 0x61, 0x73, 0x65,
	0x5f, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x18,
	0x09, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x16, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e,
	0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x6f, 0x64, 0x65, 0x52, 0x11, 0x63,
	0x61, 0x73, 0x65, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x6f, 0x64, 0x65,
	0x12, 0x23, 0x0a, 0x0d, 0x6d, 0x61, 0x69, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x64, 0x5f, 0x62,
	0x79, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x6d, 0x61, 0x69, 0x6e, 0x74, 0x61, 0x69,
	0x6e, 0x65, 0x64, 0x42, 0x79, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64,
	0x5f, 0x62, 0x79, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x64, 0x42, 0x79, 0x12, 0x1d, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f,
	0x62, 0x79, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x64, 0x42, 0x79, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61,
	0x74, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64,
	0x41, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74,
	0x18, 0x0e, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41,
	0x74, 0x22, 0x93, 0x04, 0x0a, 0x1b, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x41, 0x70, 0x69, 0x53,
	0x75, 0x69, 0x74, 0x65, 0x52, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x49, 0x74, 0x65,
	0x6d, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64,
	0x12, 0x19, 0x0a, 0x08, 0x73, 0x75, 0x69, 0x74, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x73, 0x75, 0x69, 0x74, 0x65, 0x49, 0x64, 0x12, 0x25, 0x0a, 0x0e, 0x72,
	0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0d, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x5f,
	0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65,
	0x6e, 0x63, 0x65, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b,
	0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1a, 0x0a, 0x08, 0x70,
	0x72, 0x69, 0x6f, 0x72, 0x69, 0x74, 0x79, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x70,
	0x72, 0x69, 0x6f, 0x72, 0x69, 0x74, 0x79, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x61, 0x67, 0x73, 0x18,
	0x08, 0x20, 0x03, 0x28, 0x09, 0x52, 0x04, 0x74, 0x61, 0x67, 0x73, 0x12, 0x2a, 0x0a, 0x05, 0x73,
	0x74, 0x61, 0x74, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x6d, 0x61, 0x6e,
	0x61, 0x67, 0x65, 0x72, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x65,
	0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x12, 0x3d, 0x0a, 0x0f, 0x72, 0x65, 0x66, 0x65, 0x72,
	0x65, 0x6e, 0x63, 0x65, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x14, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x0e, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63,
	0x65, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x6d, 0x61, 0x69, 0x6e, 0x74, 0x61,
	0x69, 0x6e, 0x65, 0x64, 0x5f, 0x62, 0x79, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x6d,
	0x61, 0x69, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x64, 0x42, 0x79, 0x12, 0x1d, 0x0a, 0x0a, 0x63,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x62, 0x79, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x42, 0x79, 0x12, 0x1d, 0x0a, 0x0a, 0x75, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x62, 0x79, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x42, 0x79, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x63,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x75, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x22, 0xe0, 0x04, 0x0a, 0x18, 0x53, 0x65, 0x61, 0x72,
	0x63, 0x68, 0x43, 0x61, 0x73, 0x65, 0x49, 0x6e, 0x41, 0x70, 0x69, 0x53, 0x75, 0x69, 0x74, 0x65,
	0x49, 0x74, 0x65, 0x6d, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63,
	0x74, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x5f,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f,
	0x72, 0x79, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x64, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74,
	0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x64, 0x6f, 0x63, 0x75, 0x6d,
	0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x3c, 0x0a, 0x09, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x1f, 0xfa, 0x42, 0x1c, 0x72, 0x1a, 0x52,
	0x08, 0x41, 0x50, 0x49, 0x5f, 0x43, 0x41, 0x53, 0x45, 0x52, 0x0e, 0x49, 0x4e, 0x54, 0x45, 0x52,
	0x46, 0x41, 0x43, 0x45, 0x5f, 0x43, 0x41, 0x53, 0x45, 0x52, 0x08, 0x63, 0x61, 0x73, 0x65, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x63, 0x61, 0x73, 0x65, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x72, 0x69, 0x6f, 0x72, 0x69, 0x74, 0x79, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x70, 0x72, 0x69, 0x6f, 0x72, 0x69, 0x74, 0x79, 0x12, 0x12,
	0x0a, 0x04, 0x74, 0x61, 0x67, 0x73, 0x18, 0x09, 0x20, 0x03, 0x28, 0x09, 0x52, 0x04, 0x74, 0x61,
	0x67, 0x73, 0x12, 0x2c, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x16, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65,
	0x12, 0x3d, 0x0a, 0x0e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x63, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67,
	0x65, 0x72, 0x2e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x52, 0x0d, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12,
	0x18, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x23, 0x0a, 0x0d, 0x6d, 0x61, 0x69,
	0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x64, 0x5f, 0x62, 0x79, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0c, 0x6d, 0x61, 0x69, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x64, 0x42, 0x79, 0x12, 0x1d,
	0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x62, 0x79, 0x18, 0x0e, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x42, 0x79, 0x12, 0x1d, 0x0a,
	0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x62, 0x79, 0x18, 0x0f, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x42, 0x79, 0x12, 0x1d, 0x0a, 0x0a,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x10, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x75,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x11, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x22, 0x63, 0x0a, 0x0a, 0x43, 0x61,
	0x73, 0x65, 0x54, 0x79, 0x70, 0x65, 0x49, 0x64, 0x12, 0x3c, 0x0a, 0x09, 0x63, 0x61, 0x73, 0x65,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x1f, 0xfa, 0x42, 0x1c,
	0x72, 0x1a, 0x52, 0x08, 0x41, 0x50, 0x49, 0x5f, 0x43, 0x41, 0x53, 0x45, 0x52, 0x0e, 0x49, 0x4e,
	0x54, 0x45, 0x52, 0x46, 0x41, 0x43, 0x45, 0x5f, 0x43, 0x41, 0x53, 0x45, 0x52, 0x08, 0x63, 0x61,
	0x73, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x63, 0x61, 0x73, 0x65, 0x49, 0x64, 0x42,
	0x41, 0x5a, 0x3f, 0x67, 0x69, 0x74, 0x6c, 0x61, 0x62, 0x2e, 0x74, 0x74, 0x79, 0x75, 0x79, 0x69,
	0x6e, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x54, 0x65, 0x73, 0x74, 0x44, 0x65, 0x76, 0x65, 0x6c, 0x6f,
	0x70, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x70, 0x72, 0x6f, 0x62, 0x65, 0x2d, 0x62, 0x61, 0x63, 0x6b,
	0x65, 0x6e, 0x64, 0x2f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2f, 0x72, 0x70, 0x63, 0x2f,
	0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_manager_apisuite_proto_rawDescOnce sync.Once
	file_manager_apisuite_proto_rawDescData = file_manager_apisuite_proto_rawDesc
)

func file_manager_apisuite_proto_rawDescGZIP() []byte {
	file_manager_apisuite_proto_rawDescOnce.Do(func() {
		file_manager_apisuite_proto_rawDescData = protoimpl.X.CompressGZIP(file_manager_apisuite_proto_rawDescData)
	})
	return file_manager_apisuite_proto_rawDescData
}

var file_manager_apisuite_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_manager_apisuite_proto_goTypes = []any{
	(*ApiSuite)(nil),                    // 0: manager.ApiSuite
	(*SearchApiSuiteReferenceItem)(nil), // 1: manager.SearchApiSuiteReferenceItem
	(*SearchCaseInApiSuiteItem)(nil),    // 2: manager.SearchCaseInApiSuiteItem
	(*CaseTypeId)(nil),                  // 3: manager.CaseTypeId
	(CommonState)(0),                    // 4: manager.CommonState
	(ExecutionMode)(0),                  // 5: manager.ExecutionMode
	(ResourceState)(0),                  // 6: manager.ResourceState
	(*AccountConfig)(nil),               // 7: manager.AccountConfig
}
var file_manager_apisuite_proto_depIdxs = []int32{
	4, // 0: manager.ApiSuite.state:type_name -> manager.CommonState
	5, // 1: manager.ApiSuite.case_execution_mode:type_name -> manager.ExecutionMode
	4, // 2: manager.SearchApiSuiteReferenceItem.state:type_name -> manager.CommonState
	4, // 3: manager.SearchApiSuiteReferenceItem.reference_state:type_name -> manager.CommonState
	6, // 4: manager.SearchCaseInApiSuiteItem.state:type_name -> manager.ResourceState
	7, // 5: manager.SearchCaseInApiSuiteItem.account_config:type_name -> manager.AccountConfig
	6, // [6:6] is the sub-list for method output_type
	6, // [6:6] is the sub-list for method input_type
	6, // [6:6] is the sub-list for extension type_name
	6, // [6:6] is the sub-list for extension extendee
	0, // [0:6] is the sub-list for field type_name
}

func init() { file_manager_apisuite_proto_init() }
func file_manager_apisuite_proto_init() {
	if File_manager_apisuite_proto != nil {
		return
	}
	file_manager_base_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_manager_apisuite_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_manager_apisuite_proto_goTypes,
		DependencyIndexes: file_manager_apisuite_proto_depIdxs,
		MessageInfos:      file_manager_apisuite_proto_msgTypes,
	}.Build()
	File_manager_apisuite_proto = out.File
	file_manager_apisuite_proto_rawDesc = nil
	file_manager_apisuite_proto_goTypes = nil
	file_manager_apisuite_proto_depIdxs = nil
}
