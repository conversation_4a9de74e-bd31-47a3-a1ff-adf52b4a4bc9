// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: manager/advancedsearch.proto

package pb

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on AdvancedSearchField with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *AdvancedSearchField) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AdvancedSearchField with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AdvancedSearchFieldMultiError, or nil if none found.
func (m *AdvancedSearchField) ValidateAll() error {
	return m.validate(true)
}

func (m *AdvancedSearchField) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for FieldId

	// no validation rules for ProjectId

	// no validation rules for FieldType

	// no validation rules for SceneType

	// no validation rules for FrontName

	// no validation rules for FieldName

	// no validation rules for CreatedAt

	// no validation rules for UpdatedAt

	if len(errors) > 0 {
		return AdvancedSearchFieldMultiError(errors)
	}

	return nil
}

// AdvancedSearchFieldMultiError is an error wrapping multiple validation
// errors returned by AdvancedSearchField.ValidateAll() if the designated
// constraints aren't met.
type AdvancedSearchFieldMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AdvancedSearchFieldMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AdvancedSearchFieldMultiError) AllErrors() []error { return m }

// AdvancedSearchFieldValidationError is the validation error returned by
// AdvancedSearchField.Validate if the designated constraints aren't met.
type AdvancedSearchFieldValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AdvancedSearchFieldValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AdvancedSearchFieldValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AdvancedSearchFieldValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AdvancedSearchFieldValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AdvancedSearchFieldValidationError) ErrorName() string {
	return "AdvancedSearchFieldValidationError"
}

// Error satisfies the builtin error interface
func (e AdvancedSearchFieldValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAdvancedSearchField.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AdvancedSearchFieldValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AdvancedSearchFieldValidationError{}

// Validate checks the field values on AdvancedSearchCondition with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *AdvancedSearchCondition) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AdvancedSearchCondition with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AdvancedSearchConditionMultiError, or nil if none found.
func (m *AdvancedSearchCondition) ValidateAll() error {
	return m.validate(true)
}

func (m *AdvancedSearchCondition) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ConditionId

	// no validation rules for FrontName

	// no validation rules for Compare

	// no validation rules for CreatedAt

	// no validation rules for UpdatedAt

	if len(errors) > 0 {
		return AdvancedSearchConditionMultiError(errors)
	}

	return nil
}

// AdvancedSearchConditionMultiError is an error wrapping multiple validation
// errors returned by AdvancedSearchCondition.ValidateAll() if the designated
// constraints aren't met.
type AdvancedSearchConditionMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AdvancedSearchConditionMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AdvancedSearchConditionMultiError) AllErrors() []error { return m }

// AdvancedSearchConditionValidationError is the validation error returned by
// AdvancedSearchCondition.Validate if the designated constraints aren't met.
type AdvancedSearchConditionValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AdvancedSearchConditionValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AdvancedSearchConditionValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AdvancedSearchConditionValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AdvancedSearchConditionValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AdvancedSearchConditionValidationError) ErrorName() string {
	return "AdvancedSearchConditionValidationError"
}

// Error satisfies the builtin error interface
func (e AdvancedSearchConditionValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAdvancedSearchCondition.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AdvancedSearchConditionValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AdvancedSearchConditionValidationError{}
