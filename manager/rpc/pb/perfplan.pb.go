// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.1
// 	protoc        v5.29.2
// source: manager/perfplan.proto

package pb

import (
	reflect "reflect"
	sync "sync"

	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"

	pb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type PerfPlan struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	ProjectId        string                 `protobuf:"bytes,1,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`                                 // 项目ID
	PlanId           string                 `protobuf:"bytes,2,opt,name=plan_id,json=planId,proto3" json:"plan_id,omitempty"`                                          // 计划ID
	Name             string                 `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`                                                            // 计划名称
	Description      string                 `protobuf:"bytes,4,opt,name=description,proto3" json:"description,omitempty"`                                              // 计划描述
	Type             pb.TriggerMode         `protobuf:"varint,5,opt,name=type,proto3,enum=common.TriggerMode" json:"type,omitempty"`                                   // 计划类型（手动）
	Tags             []string               `protobuf:"bytes,6,rep,name=tags,proto3" json:"tags,omitempty"`                                                            // 标签
	State            CommonState            `protobuf:"varint,7,opt,name=state,proto3,enum=manager.CommonState" json:"state,omitempty"`                                // 状态
	Protocol         pb.Protocol            `protobuf:"varint,8,opt,name=protocol,proto3,enum=common.Protocol" json:"protocol,omitempty"`                              // 协议
	ProtobufConfigId string                 `protobuf:"bytes,9,opt,name=protobuf_config_id,json=protobufConfigId,proto3" json:"protobuf_config_id,omitempty"`          // Protobuf配置ID
	GeneralConfigId  string                 `protobuf:"bytes,10,opt,name=general_config_id,json=generalConfigId,proto3" json:"general_config_id,omitempty"`            // 通用配置ID
	AccountConfigId  string                 `protobuf:"bytes,11,opt,name=account_config_id,json=accountConfigId,proto3" json:"account_config_id,omitempty"`            // 池账号配置ID
	Duration         uint32                 `protobuf:"varint,12,opt,name=duration,proto3" json:"duration,omitempty"`                                                  // 压测持续时长
	TargetEnv        pb.TargetEnvironment   `protobuf:"varint,13,opt,name=target_env,json=targetEnv,proto3,enum=common.TargetEnvironment" json:"target_env,omitempty"` // 目标环境
	Keepalive        *pb.PerfKeepalive      `protobuf:"bytes,14,opt,name=keepalive,proto3" json:"keepalive,omitempty"`                                                 // 保活参数
	Delay            uint32                 `protobuf:"varint,15,opt,name=delay,proto3" json:"delay,omitempty"`                                                        // 延迟执行时间
	MaintainedBy     string                 `protobuf:"bytes,16,opt,name=maintained_by,json=maintainedBy,proto3" json:"maintained_by,omitempty"`                       // 维护者
	CreatedBy        string                 `protobuf:"bytes,96,opt,name=created_by,json=createdBy,proto3" json:"created_by,omitempty"`                                // 创建者
	UpdatedBy        string                 `protobuf:"bytes,97,opt,name=updated_by,json=updatedBy,proto3" json:"updated_by,omitempty"`                                // 更新者
	CreatedAt        int64                  `protobuf:"varint,98,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`                               // 创建时间
	UpdatedAt        int64                  `protobuf:"varint,99,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`                               // 更新时间
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *PerfPlan) Reset() {
	*x = PerfPlan{}
	mi := &file_manager_perfplan_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PerfPlan) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PerfPlan) ProtoMessage() {}

func (x *PerfPlan) ProtoReflect() protoreflect.Message {
	mi := &file_manager_perfplan_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PerfPlan.ProtoReflect.Descriptor instead.
func (*PerfPlan) Descriptor() ([]byte, []int) {
	return file_manager_perfplan_proto_rawDescGZIP(), []int{0}
}

func (x *PerfPlan) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *PerfPlan) GetPlanId() string {
	if x != nil {
		return x.PlanId
	}
	return ""
}

func (x *PerfPlan) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *PerfPlan) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *PerfPlan) GetType() pb.TriggerMode {
	if x != nil {
		return x.Type
	}
	return pb.TriggerMode(0)
}

func (x *PerfPlan) GetTags() []string {
	if x != nil {
		return x.Tags
	}
	return nil
}

func (x *PerfPlan) GetState() CommonState {
	if x != nil {
		return x.State
	}
	return CommonState_CS_NULL
}

func (x *PerfPlan) GetProtocol() pb.Protocol {
	if x != nil {
		return x.Protocol
	}
	return pb.Protocol(0)
}

func (x *PerfPlan) GetProtobufConfigId() string {
	if x != nil {
		return x.ProtobufConfigId
	}
	return ""
}

func (x *PerfPlan) GetGeneralConfigId() string {
	if x != nil {
		return x.GeneralConfigId
	}
	return ""
}

func (x *PerfPlan) GetAccountConfigId() string {
	if x != nil {
		return x.AccountConfigId
	}
	return ""
}

func (x *PerfPlan) GetDuration() uint32 {
	if x != nil {
		return x.Duration
	}
	return 0
}

func (x *PerfPlan) GetTargetEnv() pb.TargetEnvironment {
	if x != nil {
		return x.TargetEnv
	}
	return pb.TargetEnvironment(0)
}

func (x *PerfPlan) GetKeepalive() *pb.PerfKeepalive {
	if x != nil {
		return x.Keepalive
	}
	return nil
}

func (x *PerfPlan) GetDelay() uint32 {
	if x != nil {
		return x.Delay
	}
	return 0
}

func (x *PerfPlan) GetMaintainedBy() string {
	if x != nil {
		return x.MaintainedBy
	}
	return ""
}

func (x *PerfPlan) GetCreatedBy() string {
	if x != nil {
		return x.CreatedBy
	}
	return ""
}

func (x *PerfPlan) GetUpdatedBy() string {
	if x != nil {
		return x.UpdatedBy
	}
	return ""
}

func (x *PerfPlan) GetCreatedAt() int64 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

func (x *PerfPlan) GetUpdatedAt() int64 {
	if x != nil {
		return x.UpdatedAt
	}
	return 0
}

type SearchPerfPlanItem struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ProjectId     string                 `protobuf:"bytes,1,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`                                 // 项目ID
	PlanId        string                 `protobuf:"bytes,2,opt,name=plan_id,json=planId,proto3" json:"plan_id,omitempty"`                                          // 计划ID
	Name          string                 `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`                                                            // 计划名称
	Description   string                 `protobuf:"bytes,4,opt,name=description,proto3" json:"description,omitempty"`                                              // 计划描述
	Type          pb.TriggerMode         `protobuf:"varint,5,opt,name=type,proto3,enum=common.TriggerMode" json:"type,omitempty"`                                   // 计划类型（手动）
	Tags          []string               `protobuf:"bytes,6,rep,name=tags,proto3" json:"tags,omitempty"`                                                            // 标签
	State         CommonState            `protobuf:"varint,7,opt,name=state,proto3,enum=manager.CommonState" json:"state,omitempty"`                                // 状态
	Protocol      pb.Protocol            `protobuf:"varint,8,opt,name=protocol,proto3,enum=common.Protocol" json:"protocol,omitempty"`                              // 协议
	Duration      uint32                 `protobuf:"varint,9,opt,name=duration,proto3" json:"duration,omitempty"`                                                   // 压测持续时长
	TargetEnv     pb.TargetEnvironment   `protobuf:"varint,10,opt,name=target_env,json=targetEnv,proto3,enum=common.TargetEnvironment" json:"target_env,omitempty"` // 目标环境
	NumberOfCase  uint32                 `protobuf:"varint,11,opt,name=number_of_case,json=numberOfCase,proto3" json:"number_of_case,omitempty"`                    // 压测用例数量
	NumberOfApi   uint32                 `protobuf:"varint,12,opt,name=number_of_api,json=numberOfApi,proto3" json:"number_of_api,omitempty"`                       // 压测接口数量
	StatsOfApi    []*StatsOfApi          `protobuf:"bytes,13,rep,name=stats_of_api,json=statsOfApi,proto3" json:"stats_of_api,omitempty"`                           // 压测接口统计信息
	MaintainedBy  string                 `protobuf:"bytes,14,opt,name=maintained_by,json=maintainedBy,proto3" json:"maintained_by,omitempty"`                       // 维护者
	CreatedBy     string                 `protobuf:"bytes,96,opt,name=created_by,json=createdBy,proto3" json:"created_by,omitempty"`                                // 创建者
	UpdatedBy     string                 `protobuf:"bytes,97,opt,name=updated_by,json=updatedBy,proto3" json:"updated_by,omitempty"`                                // 更新者
	CreatedAt     int64                  `protobuf:"varint,98,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`                               // 创建时间
	UpdatedAt     int64                  `protobuf:"varint,99,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`                               // 更新时间
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SearchPerfPlanItem) Reset() {
	*x = SearchPerfPlanItem{}
	mi := &file_manager_perfplan_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SearchPerfPlanItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchPerfPlanItem) ProtoMessage() {}

func (x *SearchPerfPlanItem) ProtoReflect() protoreflect.Message {
	mi := &file_manager_perfplan_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchPerfPlanItem.ProtoReflect.Descriptor instead.
func (*SearchPerfPlanItem) Descriptor() ([]byte, []int) {
	return file_manager_perfplan_proto_rawDescGZIP(), []int{1}
}

func (x *SearchPerfPlanItem) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *SearchPerfPlanItem) GetPlanId() string {
	if x != nil {
		return x.PlanId
	}
	return ""
}

func (x *SearchPerfPlanItem) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *SearchPerfPlanItem) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *SearchPerfPlanItem) GetType() pb.TriggerMode {
	if x != nil {
		return x.Type
	}
	return pb.TriggerMode(0)
}

func (x *SearchPerfPlanItem) GetTags() []string {
	if x != nil {
		return x.Tags
	}
	return nil
}

func (x *SearchPerfPlanItem) GetState() CommonState {
	if x != nil {
		return x.State
	}
	return CommonState_CS_NULL
}

func (x *SearchPerfPlanItem) GetProtocol() pb.Protocol {
	if x != nil {
		return x.Protocol
	}
	return pb.Protocol(0)
}

func (x *SearchPerfPlanItem) GetDuration() uint32 {
	if x != nil {
		return x.Duration
	}
	return 0
}

func (x *SearchPerfPlanItem) GetTargetEnv() pb.TargetEnvironment {
	if x != nil {
		return x.TargetEnv
	}
	return pb.TargetEnvironment(0)
}

func (x *SearchPerfPlanItem) GetNumberOfCase() uint32 {
	if x != nil {
		return x.NumberOfCase
	}
	return 0
}

func (x *SearchPerfPlanItem) GetNumberOfApi() uint32 {
	if x != nil {
		return x.NumberOfApi
	}
	return 0
}

func (x *SearchPerfPlanItem) GetStatsOfApi() []*StatsOfApi {
	if x != nil {
		return x.StatsOfApi
	}
	return nil
}

func (x *SearchPerfPlanItem) GetMaintainedBy() string {
	if x != nil {
		return x.MaintainedBy
	}
	return ""
}

func (x *SearchPerfPlanItem) GetCreatedBy() string {
	if x != nil {
		return x.CreatedBy
	}
	return ""
}

func (x *SearchPerfPlanItem) GetUpdatedBy() string {
	if x != nil {
		return x.UpdatedBy
	}
	return ""
}

func (x *SearchPerfPlanItem) GetCreatedAt() int64 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

func (x *SearchPerfPlanItem) GetUpdatedAt() int64 {
	if x != nil {
		return x.UpdatedAt
	}
	return 0
}

type StatsOfApi struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	TargetRps     int64                  `protobuf:"varint,1,opt,name=target_rps,json=targetRps,proto3" json:"target_rps,omitempty"`         // 目标的RPS
	NumberOfApi   uint32                 `protobuf:"varint,2,opt,name=number_of_api,json=numberOfApi,proto3" json:"number_of_api,omitempty"` // 接口数量
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *StatsOfApi) Reset() {
	*x = StatsOfApi{}
	mi := &file_manager_perfplan_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StatsOfApi) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StatsOfApi) ProtoMessage() {}

func (x *StatsOfApi) ProtoReflect() protoreflect.Message {
	mi := &file_manager_perfplan_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StatsOfApi.ProtoReflect.Descriptor instead.
func (*StatsOfApi) Descriptor() ([]byte, []int) {
	return file_manager_perfplan_proto_rawDescGZIP(), []int{2}
}

func (x *StatsOfApi) GetTargetRps() int64 {
	if x != nil {
		return x.TargetRps
	}
	return 0
}

func (x *StatsOfApi) GetNumberOfApi() uint32 {
	if x != nil {
		return x.NumberOfApi
	}
	return 0
}

type SearchCaseInPerfPlanItem struct {
	state       protoimpl.MessageState `protogen:"open.v1"`
	ProjectId   string                 `protobuf:"bytes,1,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"` // 项目ID
	CaseId      string                 `protobuf:"bytes,2,opt,name=case_id,json=caseId,proto3" json:"case_id,omitempty"`          // 压测用例ID
	Name        string                 `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`                            // 压测用例名称
	Description string                 `protobuf:"bytes,4,opt,name=description,proto3" json:"description,omitempty"`              // 压测用例描述
	Extension   string                 `protobuf:"bytes,5,opt,name=extension,proto3" json:"extension,omitempty"`                  // 压测用例文件的扩展名
	Hash        string                 `protobuf:"bytes,6,opt,name=hash,proto3" json:"hash,omitempty"`                            // 压测用例文件的一致性哈希值
	Size        uint32                 `protobuf:"varint,7,opt,name=size,proto3" json:"size,omitempty"`                           // 压测用例文件的大小
	// 限流配置
	TargetRps    int64  `protobuf:"varint,11,opt,name=target_rps,json=targetRps,proto3" json:"target_rps,omitempty"`         // 目标的RPS
	InitialRps   int64  `protobuf:"varint,12,opt,name=initial_rps,json=initialRps,proto3" json:"initial_rps,omitempty"`      // 初始的RPS
	StepHeight   int64  `protobuf:"varint,13,opt,name=step_height,json=stepHeight,proto3" json:"step_height,omitempty"`      // 每次改变RPS的量
	StepDuration string `protobuf:"bytes,14,opt,name=step_duration,json=stepDuration,proto3" json:"step_duration,omitempty"` // 改变后的RPS的持续时间
	// 压测数据配置
	PerfDataId string `protobuf:"bytes,21,opt,name=perf_data_id,json=perfDataId,proto3" json:"perf_data_id,omitempty"`  // 压测数据ID
	NumberOfVu uint32 `protobuf:"varint,22,opt,name=number_of_vu,json=numberOfVu,proto3" json:"number_of_vu,omitempty"` // 虚拟用户数
	// 施压机资源配置
	NumberOfLg       uint32 `protobuf:"varint,31,opt,name=number_of_lg,json=numberOfLg,proto3" json:"number_of_lg,omitempty"`                  // 施压机数量
	RequestsOfCpu    string `protobuf:"bytes,32,opt,name=requests_of_cpu,json=requestsOfCpu,proto3" json:"requests_of_cpu,omitempty"`          // 最小分配的CPU资源
	RequestsOfMemory string `protobuf:"bytes,33,opt,name=requests_of_memory,json=requestsOfMemory,proto3" json:"requests_of_memory,omitempty"` // 最小分配的内存资源
	LimitsOfCpu      string `protobuf:"bytes,34,opt,name=limits_of_cpu,json=limitsOfCpu,proto3" json:"limits_of_cpu,omitempty"`                // 最大分配的CPU资源
	LimitsOfMemory   string `protobuf:"bytes,35,opt,name=limits_of_memory,json=limitsOfMemory,proto3" json:"limits_of_memory,omitempty"`       // 最大分配的内存资源
	CreatedBy        string `protobuf:"bytes,96,opt,name=created_by,json=createdBy,proto3" json:"created_by,omitempty"`                        // 创建者
	UpdatedBy        string `protobuf:"bytes,97,opt,name=updated_by,json=updatedBy,proto3" json:"updated_by,omitempty"`                        // 更新者
	CreatedAt        int64  `protobuf:"varint,98,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`                       // 创建时间
	UpdatedAt        int64  `protobuf:"varint,99,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`                       // 更新时间
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *SearchCaseInPerfPlanItem) Reset() {
	*x = SearchCaseInPerfPlanItem{}
	mi := &file_manager_perfplan_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SearchCaseInPerfPlanItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchCaseInPerfPlanItem) ProtoMessage() {}

func (x *SearchCaseInPerfPlanItem) ProtoReflect() protoreflect.Message {
	mi := &file_manager_perfplan_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchCaseInPerfPlanItem.ProtoReflect.Descriptor instead.
func (*SearchCaseInPerfPlanItem) Descriptor() ([]byte, []int) {
	return file_manager_perfplan_proto_rawDescGZIP(), []int{3}
}

func (x *SearchCaseInPerfPlanItem) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *SearchCaseInPerfPlanItem) GetCaseId() string {
	if x != nil {
		return x.CaseId
	}
	return ""
}

func (x *SearchCaseInPerfPlanItem) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *SearchCaseInPerfPlanItem) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *SearchCaseInPerfPlanItem) GetExtension() string {
	if x != nil {
		return x.Extension
	}
	return ""
}

func (x *SearchCaseInPerfPlanItem) GetHash() string {
	if x != nil {
		return x.Hash
	}
	return ""
}

func (x *SearchCaseInPerfPlanItem) GetSize() uint32 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *SearchCaseInPerfPlanItem) GetTargetRps() int64 {
	if x != nil {
		return x.TargetRps
	}
	return 0
}

func (x *SearchCaseInPerfPlanItem) GetInitialRps() int64 {
	if x != nil {
		return x.InitialRps
	}
	return 0
}

func (x *SearchCaseInPerfPlanItem) GetStepHeight() int64 {
	if x != nil {
		return x.StepHeight
	}
	return 0
}

func (x *SearchCaseInPerfPlanItem) GetStepDuration() string {
	if x != nil {
		return x.StepDuration
	}
	return ""
}

func (x *SearchCaseInPerfPlanItem) GetPerfDataId() string {
	if x != nil {
		return x.PerfDataId
	}
	return ""
}

func (x *SearchCaseInPerfPlanItem) GetNumberOfVu() uint32 {
	if x != nil {
		return x.NumberOfVu
	}
	return 0
}

func (x *SearchCaseInPerfPlanItem) GetNumberOfLg() uint32 {
	if x != nil {
		return x.NumberOfLg
	}
	return 0
}

func (x *SearchCaseInPerfPlanItem) GetRequestsOfCpu() string {
	if x != nil {
		return x.RequestsOfCpu
	}
	return ""
}

func (x *SearchCaseInPerfPlanItem) GetRequestsOfMemory() string {
	if x != nil {
		return x.RequestsOfMemory
	}
	return ""
}

func (x *SearchCaseInPerfPlanItem) GetLimitsOfCpu() string {
	if x != nil {
		return x.LimitsOfCpu
	}
	return ""
}

func (x *SearchCaseInPerfPlanItem) GetLimitsOfMemory() string {
	if x != nil {
		return x.LimitsOfMemory
	}
	return ""
}

func (x *SearchCaseInPerfPlanItem) GetCreatedBy() string {
	if x != nil {
		return x.CreatedBy
	}
	return ""
}

func (x *SearchCaseInPerfPlanItem) GetUpdatedBy() string {
	if x != nil {
		return x.UpdatedBy
	}
	return ""
}

func (x *SearchCaseInPerfPlanItem) GetCreatedAt() int64 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

func (x *SearchCaseInPerfPlanItem) GetUpdatedAt() int64 {
	if x != nil {
		return x.UpdatedAt
	}
	return 0
}

type PerfPlanV2 struct {
	state                protoimpl.MessageState `protogen:"open.v1"`
	ProjectId            string                 `protobuf:"bytes,1,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`                                    // 项目ID
	PlanId               string                 `protobuf:"bytes,2,opt,name=plan_id,json=planId,proto3" json:"plan_id,omitempty"`                                             // 计划ID
	CategoryId           string                 `protobuf:"bytes,3,opt,name=category_id,json=categoryId,proto3" json:"category_id,omitempty"`                                 // 分类ID
	Name                 string                 `protobuf:"bytes,11,opt,name=name,proto3" json:"name,omitempty"`                                                              // 计划名称
	Description          string                 `protobuf:"bytes,12,opt,name=description,proto3" json:"description,omitempty"`                                                // 计划描述
	Type                 pb.TriggerMode         `protobuf:"varint,13,opt,name=type,proto3,enum=common.TriggerMode" json:"type,omitempty"`                                     // 计划类型（手动、定时）
	CronExpression       string                 `protobuf:"bytes,14,opt,name=cron_expression,json=cronExpression,proto3" json:"cron_expression,omitempty"`                    // 定时触发计划的Cron表达式
	Tags                 []string               `protobuf:"bytes,15,rep,name=tags,proto3" json:"tags,omitempty"`                                                              // 标签
	Protocol             pb.Protocol            `protobuf:"varint,16,opt,name=protocol,proto3,enum=common.Protocol" json:"protocol,omitempty"`                                // 协议
	TargetEnv            pb.TargetEnvironment   `protobuf:"varint,17,opt,name=target_env,json=targetEnv,proto3,enum=common.TargetEnvironment" json:"target_env,omitempty"`    // 目标环境
	ProtobufConfigId     string                 `protobuf:"bytes,18,opt,name=protobuf_config_id,json=protobufConfigId,proto3" json:"protobuf_config_id,omitempty"`            // Deprecated: Protobuf配置ID
	GeneralConfigId      string                 `protobuf:"bytes,19,opt,name=general_config_id,json=generalConfigId,proto3" json:"general_config_id,omitempty"`               // 通用配置ID
	AccountConfigId      string                 `protobuf:"bytes,20,opt,name=account_config_id,json=accountConfigId,proto3" json:"account_config_id,omitempty"`               // 池账号配置ID
	AuthRateLimits       []*pb.RateLimitV2      `protobuf:"bytes,21,rep,name=auth_rate_limits,json=authRateLimits,proto3" json:"auth_rate_limits,omitempty"`                  // TT登录压测场景专用的登录接口限流配置
	CustomDuration       bool                   `protobuf:"varint,31,opt,name=custom_duration,json=customDuration,proto3" json:"custom_duration,omitempty"`                   // 是否自定义压测持续时长
	Duration             uint32                 `protobuf:"varint,32,opt,name=duration,proto3" json:"duration,omitempty"`                                                     // 压测持续时长
	CreateLarkChat       bool                   `protobuf:"varint,33,opt,name=create_lark_chat,json=createLarkChat,proto3" json:"create_lark_chat,omitempty"`                 // 是否需要自动拉群
	LarkChatId           string                 `protobuf:"bytes,34,opt,name=lark_chat_id,json=larkChatId,proto3" json:"lark_chat_id,omitempty"`                              // 通过自动拉群创建的飞书群ID
	AdvancedNotification bool                   `protobuf:"varint,35,opt,name=advanced_notification,json=advancedNotification,proto3" json:"advanced_notification,omitempty"` // 是否需要提前通知
	State                CommonState            `protobuf:"varint,41,opt,name=state,proto3,enum=manager.CommonState" json:"state,omitempty"`                                  // 状态
	MaintainedBy         string                 `protobuf:"bytes,42,opt,name=maintained_by,json=maintainedBy,proto3" json:"maintained_by,omitempty"`                          // 维护者
	CreatedBy            string                 `protobuf:"bytes,96,opt,name=created_by,json=createdBy,proto3" json:"created_by,omitempty"`                                   // 创建者
	UpdatedBy            string                 `protobuf:"bytes,97,opt,name=updated_by,json=updatedBy,proto3" json:"updated_by,omitempty"`                                   // 更新者
	CreatedAt            int64                  `protobuf:"varint,98,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`                                  // 创建时间
	UpdatedAt            int64                  `protobuf:"varint,99,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`                                  // 更新时间
	unknownFields        protoimpl.UnknownFields
	sizeCache            protoimpl.SizeCache
}

func (x *PerfPlanV2) Reset() {
	*x = PerfPlanV2{}
	mi := &file_manager_perfplan_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PerfPlanV2) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PerfPlanV2) ProtoMessage() {}

func (x *PerfPlanV2) ProtoReflect() protoreflect.Message {
	mi := &file_manager_perfplan_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PerfPlanV2.ProtoReflect.Descriptor instead.
func (*PerfPlanV2) Descriptor() ([]byte, []int) {
	return file_manager_perfplan_proto_rawDescGZIP(), []int{4}
}

func (x *PerfPlanV2) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *PerfPlanV2) GetPlanId() string {
	if x != nil {
		return x.PlanId
	}
	return ""
}

func (x *PerfPlanV2) GetCategoryId() string {
	if x != nil {
		return x.CategoryId
	}
	return ""
}

func (x *PerfPlanV2) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *PerfPlanV2) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *PerfPlanV2) GetType() pb.TriggerMode {
	if x != nil {
		return x.Type
	}
	return pb.TriggerMode(0)
}

func (x *PerfPlanV2) GetCronExpression() string {
	if x != nil {
		return x.CronExpression
	}
	return ""
}

func (x *PerfPlanV2) GetTags() []string {
	if x != nil {
		return x.Tags
	}
	return nil
}

func (x *PerfPlanV2) GetProtocol() pb.Protocol {
	if x != nil {
		return x.Protocol
	}
	return pb.Protocol(0)
}

func (x *PerfPlanV2) GetTargetEnv() pb.TargetEnvironment {
	if x != nil {
		return x.TargetEnv
	}
	return pb.TargetEnvironment(0)
}

func (x *PerfPlanV2) GetProtobufConfigId() string {
	if x != nil {
		return x.ProtobufConfigId
	}
	return ""
}

func (x *PerfPlanV2) GetGeneralConfigId() string {
	if x != nil {
		return x.GeneralConfigId
	}
	return ""
}

func (x *PerfPlanV2) GetAccountConfigId() string {
	if x != nil {
		return x.AccountConfigId
	}
	return ""
}

func (x *PerfPlanV2) GetAuthRateLimits() []*pb.RateLimitV2 {
	if x != nil {
		return x.AuthRateLimits
	}
	return nil
}

func (x *PerfPlanV2) GetCustomDuration() bool {
	if x != nil {
		return x.CustomDuration
	}
	return false
}

func (x *PerfPlanV2) GetDuration() uint32 {
	if x != nil {
		return x.Duration
	}
	return 0
}

func (x *PerfPlanV2) GetCreateLarkChat() bool {
	if x != nil {
		return x.CreateLarkChat
	}
	return false
}

func (x *PerfPlanV2) GetLarkChatId() string {
	if x != nil {
		return x.LarkChatId
	}
	return ""
}

func (x *PerfPlanV2) GetAdvancedNotification() bool {
	if x != nil {
		return x.AdvancedNotification
	}
	return false
}

func (x *PerfPlanV2) GetState() CommonState {
	if x != nil {
		return x.State
	}
	return CommonState_CS_NULL
}

func (x *PerfPlanV2) GetMaintainedBy() string {
	if x != nil {
		return x.MaintainedBy
	}
	return ""
}

func (x *PerfPlanV2) GetCreatedBy() string {
	if x != nil {
		return x.CreatedBy
	}
	return ""
}

func (x *PerfPlanV2) GetUpdatedBy() string {
	if x != nil {
		return x.UpdatedBy
	}
	return ""
}

func (x *PerfPlanV2) GetCreatedAt() int64 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

func (x *PerfPlanV2) GetUpdatedAt() int64 {
	if x != nil {
		return x.UpdatedAt
	}
	return 0
}

type SearchPerfPlanV2Item struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	ProjectId      string                 `protobuf:"bytes,1,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`                                 // 项目ID
	PlanId         string                 `protobuf:"bytes,2,opt,name=plan_id,json=planId,proto3" json:"plan_id,omitempty"`                                          // 计划ID
	CategoryId     string                 `protobuf:"bytes,3,opt,name=category_id,json=categoryId,proto3" json:"category_id,omitempty"`                              // 分类ID
	Name           string                 `protobuf:"bytes,11,opt,name=name,proto3" json:"name,omitempty"`                                                           // 计划名称
	Description    string                 `protobuf:"bytes,12,opt,name=description,proto3" json:"description,omitempty"`                                             // 计划描述
	Type           pb.TriggerMode         `protobuf:"varint,13,opt,name=type,proto3,enum=common.TriggerMode" json:"type,omitempty"`                                  // 计划类型（手动、定时）
	CronExpression string                 `protobuf:"bytes,14,opt,name=cron_expression,json=cronExpression,proto3" json:"cron_expression,omitempty"`                 // 定时触发计划的Cron表达式
	Tags           []string               `protobuf:"bytes,15,rep,name=tags,proto3" json:"tags,omitempty"`                                                           // 标签
	Protocol       pb.Protocol            `protobuf:"varint,16,opt,name=protocol,proto3,enum=common.Protocol" json:"protocol,omitempty"`                             // 协议
	TargetEnv      pb.TargetEnvironment   `protobuf:"varint,17,opt,name=target_env,json=targetEnv,proto3,enum=common.TargetEnvironment" json:"target_env,omitempty"` // 目标环境
	CustomDuration bool                   `protobuf:"varint,31,opt,name=custom_duration,json=customDuration,proto3" json:"custom_duration,omitempty"`                // 是否自定义压测持续时长
	Duration       uint32                 `protobuf:"varint,32,opt,name=duration,proto3" json:"duration,omitempty"`                                                  // 压测持续时长
	NumberOfCases  uint32                 `protobuf:"varint,41,opt,name=number_of_cases,json=numberOfCases,proto3" json:"number_of_cases,omitempty"`                 // 用例数量
	NumberOfSteps  uint32                 `protobuf:"varint,42,opt,name=number_of_steps,json=numberOfSteps,proto3" json:"number_of_steps,omitempty"`                 // 步骤数量（串行和并行）
	StatsOfStep    []*StatsOfStep         `protobuf:"bytes,43,rep,name=stats_of_step,json=statsOfStep,proto3" json:"stats_of_step,omitempty"`                        // 步骤统计信息
	State          CommonState            `protobuf:"varint,51,opt,name=state,proto3,enum=manager.CommonState" json:"state,omitempty"`                               // 状态
	MaintainedBy   string                 `protobuf:"bytes,52,opt,name=maintained_by,json=maintainedBy,proto3" json:"maintained_by,omitempty"`                       // 维护者
	CreatedBy      string                 `protobuf:"bytes,96,opt,name=created_by,json=createdBy,proto3" json:"created_by,omitempty"`                                // 创建者
	UpdatedBy      string                 `protobuf:"bytes,97,opt,name=updated_by,json=updatedBy,proto3" json:"updated_by,omitempty"`                                // 更新者
	CreatedAt      int64                  `protobuf:"varint,98,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`                               // 创建时间
	UpdatedAt      int64                  `protobuf:"varint,99,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`                               // 更新时间
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *SearchPerfPlanV2Item) Reset() {
	*x = SearchPerfPlanV2Item{}
	mi := &file_manager_perfplan_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SearchPerfPlanV2Item) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchPerfPlanV2Item) ProtoMessage() {}

func (x *SearchPerfPlanV2Item) ProtoReflect() protoreflect.Message {
	mi := &file_manager_perfplan_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchPerfPlanV2Item.ProtoReflect.Descriptor instead.
func (*SearchPerfPlanV2Item) Descriptor() ([]byte, []int) {
	return file_manager_perfplan_proto_rawDescGZIP(), []int{5}
}

func (x *SearchPerfPlanV2Item) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *SearchPerfPlanV2Item) GetPlanId() string {
	if x != nil {
		return x.PlanId
	}
	return ""
}

func (x *SearchPerfPlanV2Item) GetCategoryId() string {
	if x != nil {
		return x.CategoryId
	}
	return ""
}

func (x *SearchPerfPlanV2Item) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *SearchPerfPlanV2Item) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *SearchPerfPlanV2Item) GetType() pb.TriggerMode {
	if x != nil {
		return x.Type
	}
	return pb.TriggerMode(0)
}

func (x *SearchPerfPlanV2Item) GetCronExpression() string {
	if x != nil {
		return x.CronExpression
	}
	return ""
}

func (x *SearchPerfPlanV2Item) GetTags() []string {
	if x != nil {
		return x.Tags
	}
	return nil
}

func (x *SearchPerfPlanV2Item) GetProtocol() pb.Protocol {
	if x != nil {
		return x.Protocol
	}
	return pb.Protocol(0)
}

func (x *SearchPerfPlanV2Item) GetTargetEnv() pb.TargetEnvironment {
	if x != nil {
		return x.TargetEnv
	}
	return pb.TargetEnvironment(0)
}

func (x *SearchPerfPlanV2Item) GetCustomDuration() bool {
	if x != nil {
		return x.CustomDuration
	}
	return false
}

func (x *SearchPerfPlanV2Item) GetDuration() uint32 {
	if x != nil {
		return x.Duration
	}
	return 0
}

func (x *SearchPerfPlanV2Item) GetNumberOfCases() uint32 {
	if x != nil {
		return x.NumberOfCases
	}
	return 0
}

func (x *SearchPerfPlanV2Item) GetNumberOfSteps() uint32 {
	if x != nil {
		return x.NumberOfSteps
	}
	return 0
}

func (x *SearchPerfPlanV2Item) GetStatsOfStep() []*StatsOfStep {
	if x != nil {
		return x.StatsOfStep
	}
	return nil
}

func (x *SearchPerfPlanV2Item) GetState() CommonState {
	if x != nil {
		return x.State
	}
	return CommonState_CS_NULL
}

func (x *SearchPerfPlanV2Item) GetMaintainedBy() string {
	if x != nil {
		return x.MaintainedBy
	}
	return ""
}

func (x *SearchPerfPlanV2Item) GetCreatedBy() string {
	if x != nil {
		return x.CreatedBy
	}
	return ""
}

func (x *SearchPerfPlanV2Item) GetUpdatedBy() string {
	if x != nil {
		return x.UpdatedBy
	}
	return ""
}

func (x *SearchPerfPlanV2Item) GetCreatedAt() int64 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

func (x *SearchPerfPlanV2Item) GetUpdatedAt() int64 {
	if x != nil {
		return x.UpdatedAt
	}
	return 0
}

type StatsOfStep struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	TargetRps     int64                  `protobuf:"varint,1,opt,name=target_rps,json=targetRps,proto3" json:"target_rps,omitempty"`               // 目标的RPS
	NumberOfSteps uint32                 `protobuf:"varint,2,opt,name=number_of_steps,json=numberOfSteps,proto3" json:"number_of_steps,omitempty"` // 步骤数量
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *StatsOfStep) Reset() {
	*x = StatsOfStep{}
	mi := &file_manager_perfplan_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StatsOfStep) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StatsOfStep) ProtoMessage() {}

func (x *StatsOfStep) ProtoReflect() protoreflect.Message {
	mi := &file_manager_perfplan_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StatsOfStep.ProtoReflect.Descriptor instead.
func (*StatsOfStep) Descriptor() ([]byte, []int) {
	return file_manager_perfplan_proto_rawDescGZIP(), []int{6}
}

func (x *StatsOfStep) GetTargetRps() int64 {
	if x != nil {
		return x.TargetRps
	}
	return 0
}

func (x *StatsOfStep) GetNumberOfSteps() uint32 {
	if x != nil {
		return x.NumberOfSteps
	}
	return 0
}

type PerfPlanCaseV2Item struct {
	state  protoimpl.MessageState `protogen:"open.v1"`
	CaseId string                 `protobuf:"bytes,1,opt,name=case_id,json=caseId,proto3" json:"case_id,omitempty"` // 用例ID
	// 压测数据配置
	PerfDataId string `protobuf:"bytes,11,opt,name=perf_data_id,json=perfDataId,proto3" json:"perf_data_id,omitempty"`  // 压测数据ID
	CustomVu   bool   `protobuf:"varint,12,opt,name=custom_vu,json=customVu,proto3" json:"custom_vu,omitempty"`         // 是否自定义虚拟用户数
	NumberOfVu uint32 `protobuf:"varint,13,opt,name=number_of_vu,json=numberOfVu,proto3" json:"number_of_vu,omitempty"` // 虚拟用户数
	// 施压机资源配置
	CustomLg         bool   `protobuf:"varint,21,opt,name=custom_lg,json=customLg,proto3" json:"custom_lg,omitempty"`                          // 是否自定义施压机资源
	NumberOfLg       uint32 `protobuf:"varint,22,opt,name=number_of_lg,json=numberOfLg,proto3" json:"number_of_lg,omitempty"`                  // 施压机数量
	RequestsOfCpu    string `protobuf:"bytes,23,opt,name=requests_of_cpu,json=requestsOfCpu,proto3" json:"requests_of_cpu,omitempty"`          // 最小分配的CPU资源
	RequestsOfMemory string `protobuf:"bytes,24,opt,name=requests_of_memory,json=requestsOfMemory,proto3" json:"requests_of_memory,omitempty"` // 最小分配的内存资源
	LimitsOfCpu      string `protobuf:"bytes,25,opt,name=limits_of_cpu,json=limitsOfCpu,proto3" json:"limits_of_cpu,omitempty"`                // 最大分配的CPU资源
	LimitsOfMemory   string `protobuf:"bytes,26,opt,name=limits_of_memory,json=limitsOfMemory,proto3" json:"limits_of_memory,omitempty"`       // 最大分配的内存资源
	// 用例限流配置
	RateLimits    []*pb.RateLimitV2 `protobuf:"bytes,27,rep,name=rate_limits,json=rateLimits,proto3" json:"rate_limits,omitempty"` // 限流配置
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PerfPlanCaseV2Item) Reset() {
	*x = PerfPlanCaseV2Item{}
	mi := &file_manager_perfplan_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PerfPlanCaseV2Item) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PerfPlanCaseV2Item) ProtoMessage() {}

func (x *PerfPlanCaseV2Item) ProtoReflect() protoreflect.Message {
	mi := &file_manager_perfplan_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PerfPlanCaseV2Item.ProtoReflect.Descriptor instead.
func (*PerfPlanCaseV2Item) Descriptor() ([]byte, []int) {
	return file_manager_perfplan_proto_rawDescGZIP(), []int{7}
}

func (x *PerfPlanCaseV2Item) GetCaseId() string {
	if x != nil {
		return x.CaseId
	}
	return ""
}

func (x *PerfPlanCaseV2Item) GetPerfDataId() string {
	if x != nil {
		return x.PerfDataId
	}
	return ""
}

func (x *PerfPlanCaseV2Item) GetCustomVu() bool {
	if x != nil {
		return x.CustomVu
	}
	return false
}

func (x *PerfPlanCaseV2Item) GetNumberOfVu() uint32 {
	if x != nil {
		return x.NumberOfVu
	}
	return 0
}

func (x *PerfPlanCaseV2Item) GetCustomLg() bool {
	if x != nil {
		return x.CustomLg
	}
	return false
}

func (x *PerfPlanCaseV2Item) GetNumberOfLg() uint32 {
	if x != nil {
		return x.NumberOfLg
	}
	return 0
}

func (x *PerfPlanCaseV2Item) GetRequestsOfCpu() string {
	if x != nil {
		return x.RequestsOfCpu
	}
	return ""
}

func (x *PerfPlanCaseV2Item) GetRequestsOfMemory() string {
	if x != nil {
		return x.RequestsOfMemory
	}
	return ""
}

func (x *PerfPlanCaseV2Item) GetLimitsOfCpu() string {
	if x != nil {
		return x.LimitsOfCpu
	}
	return ""
}

func (x *PerfPlanCaseV2Item) GetLimitsOfMemory() string {
	if x != nil {
		return x.LimitsOfMemory
	}
	return ""
}

func (x *PerfPlanCaseV2Item) GetRateLimits() []*pb.RateLimitV2 {
	if x != nil {
		return x.RateLimits
	}
	return nil
}

type SearchCaseInPerfPlanV2Item struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ProjectId     string                 `protobuf:"bytes,1,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`                 // 项目ID
	CategoryId    string                 `protobuf:"bytes,2,opt,name=category_id,json=categoryId,proto3" json:"category_id,omitempty"`              // 所属分类ID
	CaseId        string                 `protobuf:"bytes,3,opt,name=case_id,json=caseId,proto3" json:"case_id,omitempty"`                          // 用例ID
	Name          string                 `protobuf:"bytes,11,opt,name=name,proto3" json:"name,omitempty"`                                           // 用例名称
	Description   string                 `protobuf:"bytes,12,opt,name=description,proto3" json:"description,omitempty"`                             // 用例描述
	Tags          []string               `protobuf:"bytes,13,rep,name=tags,proto3" json:"tags,omitempty"`                                           // 标签
	Protocol      pb.Protocol            `protobuf:"varint,14,opt,name=protocol,proto3,enum=common.Protocol" json:"protocol,omitempty"`             // 协议
	RateLimits    []*pb.RateLimitV2      `protobuf:"bytes,15,rep,name=rate_limits,json=rateLimits,proto3" json:"rate_limits,omitempty"`             // 限流配置
	SerialSteps   []*pb.PerfCaseStepV2   `protobuf:"bytes,16,rep,name=serial_steps,json=serialSteps,proto3" json:"serial_steps,omitempty"`          // 串行步骤列表
	ParallelSteps []*pb.PerfCaseStepV2   `protobuf:"bytes,17,rep,name=parallel_steps,json=parallelSteps,proto3" json:"parallel_steps,omitempty"`    // 并行步骤列表
	NumberOfSteps uint32                 `protobuf:"varint,21,opt,name=number_of_steps,json=numberOfSteps,proto3" json:"number_of_steps,omitempty"` // 测试步骤数
	TargetRps     int64                  `protobuf:"varint,22,opt,name=target_rps,json=targetRps,proto3" json:"target_rps,omitempty"`               // 目标的RPS
	// 压测数据配置
	PerfDataId string `protobuf:"bytes,31,opt,name=perf_data_id,json=perfDataId,proto3" json:"perf_data_id,omitempty"`  // 压测数据ID
	CustomVu   bool   `protobuf:"varint,32,opt,name=custom_vu,json=customVu,proto3" json:"custom_vu,omitempty"`         // 是否自定义虚拟用户数
	NumberOfVu uint32 `protobuf:"varint,33,opt,name=number_of_vu,json=numberOfVu,proto3" json:"number_of_vu,omitempty"` // 虚拟用户数
	// 施压机资源配置
	CustomLg         bool        `protobuf:"varint,41,opt,name=custom_lg,json=customLg,proto3" json:"custom_lg,omitempty"`                          // 是否自定义施压机资源
	NumberOfLg       uint32      `protobuf:"varint,42,opt,name=number_of_lg,json=numberOfLg,proto3" json:"number_of_lg,omitempty"`                  // 施压机数量
	RequestsOfCpu    string      `protobuf:"bytes,43,opt,name=requests_of_cpu,json=requestsOfCpu,proto3" json:"requests_of_cpu,omitempty"`          // 最小分配的CPU资源
	RequestsOfMemory string      `protobuf:"bytes,44,opt,name=requests_of_memory,json=requestsOfMemory,proto3" json:"requests_of_memory,omitempty"` // 最小分配的内存资源
	LimitsOfCpu      string      `protobuf:"bytes,45,opt,name=limits_of_cpu,json=limitsOfCpu,proto3" json:"limits_of_cpu,omitempty"`                // 最大分配的CPU资源
	LimitsOfMemory   string      `protobuf:"bytes,46,opt,name=limits_of_memory,json=limitsOfMemory,proto3" json:"limits_of_memory,omitempty"`       // 最大分配的内存资源
	State            CommonState `protobuf:"varint,51,opt,name=state,proto3,enum=manager.CommonState" json:"state,omitempty"`                       // 状态
	MaintainedBy     string      `protobuf:"bytes,52,opt,name=maintained_by,json=maintainedBy,proto3" json:"maintained_by,omitempty"`               // 维护者
	CreatedBy        string      `protobuf:"bytes,96,opt,name=created_by,json=createdBy,proto3" json:"created_by,omitempty"`                        // 创建者
	UpdatedBy        string      `protobuf:"bytes,97,opt,name=updated_by,json=updatedBy,proto3" json:"updated_by,omitempty"`                        // 更新者
	CreatedAt        int64       `protobuf:"varint,98,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`                       // 创建时间
	UpdatedAt        int64       `protobuf:"varint,99,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`                       // 更新时间
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *SearchCaseInPerfPlanV2Item) Reset() {
	*x = SearchCaseInPerfPlanV2Item{}
	mi := &file_manager_perfplan_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SearchCaseInPerfPlanV2Item) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchCaseInPerfPlanV2Item) ProtoMessage() {}

func (x *SearchCaseInPerfPlanV2Item) ProtoReflect() protoreflect.Message {
	mi := &file_manager_perfplan_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchCaseInPerfPlanV2Item.ProtoReflect.Descriptor instead.
func (*SearchCaseInPerfPlanV2Item) Descriptor() ([]byte, []int) {
	return file_manager_perfplan_proto_rawDescGZIP(), []int{8}
}

func (x *SearchCaseInPerfPlanV2Item) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *SearchCaseInPerfPlanV2Item) GetCategoryId() string {
	if x != nil {
		return x.CategoryId
	}
	return ""
}

func (x *SearchCaseInPerfPlanV2Item) GetCaseId() string {
	if x != nil {
		return x.CaseId
	}
	return ""
}

func (x *SearchCaseInPerfPlanV2Item) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *SearchCaseInPerfPlanV2Item) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *SearchCaseInPerfPlanV2Item) GetTags() []string {
	if x != nil {
		return x.Tags
	}
	return nil
}

func (x *SearchCaseInPerfPlanV2Item) GetProtocol() pb.Protocol {
	if x != nil {
		return x.Protocol
	}
	return pb.Protocol(0)
}

func (x *SearchCaseInPerfPlanV2Item) GetRateLimits() []*pb.RateLimitV2 {
	if x != nil {
		return x.RateLimits
	}
	return nil
}

func (x *SearchCaseInPerfPlanV2Item) GetSerialSteps() []*pb.PerfCaseStepV2 {
	if x != nil {
		return x.SerialSteps
	}
	return nil
}

func (x *SearchCaseInPerfPlanV2Item) GetParallelSteps() []*pb.PerfCaseStepV2 {
	if x != nil {
		return x.ParallelSteps
	}
	return nil
}

func (x *SearchCaseInPerfPlanV2Item) GetNumberOfSteps() uint32 {
	if x != nil {
		return x.NumberOfSteps
	}
	return 0
}

func (x *SearchCaseInPerfPlanV2Item) GetTargetRps() int64 {
	if x != nil {
		return x.TargetRps
	}
	return 0
}

func (x *SearchCaseInPerfPlanV2Item) GetPerfDataId() string {
	if x != nil {
		return x.PerfDataId
	}
	return ""
}

func (x *SearchCaseInPerfPlanV2Item) GetCustomVu() bool {
	if x != nil {
		return x.CustomVu
	}
	return false
}

func (x *SearchCaseInPerfPlanV2Item) GetNumberOfVu() uint32 {
	if x != nil {
		return x.NumberOfVu
	}
	return 0
}

func (x *SearchCaseInPerfPlanV2Item) GetCustomLg() bool {
	if x != nil {
		return x.CustomLg
	}
	return false
}

func (x *SearchCaseInPerfPlanV2Item) GetNumberOfLg() uint32 {
	if x != nil {
		return x.NumberOfLg
	}
	return 0
}

func (x *SearchCaseInPerfPlanV2Item) GetRequestsOfCpu() string {
	if x != nil {
		return x.RequestsOfCpu
	}
	return ""
}

func (x *SearchCaseInPerfPlanV2Item) GetRequestsOfMemory() string {
	if x != nil {
		return x.RequestsOfMemory
	}
	return ""
}

func (x *SearchCaseInPerfPlanV2Item) GetLimitsOfCpu() string {
	if x != nil {
		return x.LimitsOfCpu
	}
	return ""
}

func (x *SearchCaseInPerfPlanV2Item) GetLimitsOfMemory() string {
	if x != nil {
		return x.LimitsOfMemory
	}
	return ""
}

func (x *SearchCaseInPerfPlanV2Item) GetState() CommonState {
	if x != nil {
		return x.State
	}
	return CommonState_CS_NULL
}

func (x *SearchCaseInPerfPlanV2Item) GetMaintainedBy() string {
	if x != nil {
		return x.MaintainedBy
	}
	return ""
}

func (x *SearchCaseInPerfPlanV2Item) GetCreatedBy() string {
	if x != nil {
		return x.CreatedBy
	}
	return ""
}

func (x *SearchCaseInPerfPlanV2Item) GetUpdatedBy() string {
	if x != nil {
		return x.UpdatedBy
	}
	return ""
}

func (x *SearchCaseInPerfPlanV2Item) GetCreatedAt() int64 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

func (x *SearchCaseInPerfPlanV2Item) GetUpdatedAt() int64 {
	if x != nil {
		return x.UpdatedAt
	}
	return 0
}

type SearchProtobufInPerfPlanV2Item struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ProjectId     string                 `protobuf:"bytes,1,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`           // 项目ID
	ConfigId      string                 `protobuf:"bytes,2,opt,name=config_id,json=configId,proto3" json:"config_id,omitempty"`              // Protobuf配置ID
	Name          string                 `protobuf:"bytes,11,opt,name=name,proto3" json:"name,omitempty"`                                     // Protobuf配置名称
	Description   string                 `protobuf:"bytes,12,opt,name=description,proto3" json:"description,omitempty"`                       // Protobuf配置描述
	GitConfigId   string                 `protobuf:"bytes,13,opt,name=git_config_id,json=gitConfigId,proto3" json:"git_config_id,omitempty"`  // Git配置ID
	ImportPath    string                 `protobuf:"bytes,14,opt,name=import_path,json=importPath,proto3" json:"import_path,omitempty"`       // 导入路径
	ExcludePaths  []string               `protobuf:"bytes,15,rep,name=exclude_paths,json=excludePaths,proto3" json:"exclude_paths,omitempty"` // 排除的路径列表
	ExcludeFiles  []string               `protobuf:"bytes,16,rep,name=exclude_files,json=excludeFiles,proto3" json:"exclude_files,omitempty"` // 排除的文件列表
	Dependencies  []string               `protobuf:"bytes,17,rep,name=dependencies,proto3" json:"dependencies,omitempty"`                     // 依赖列表
	CreatedBy     string                 `protobuf:"bytes,96,opt,name=created_by,json=createdBy,proto3" json:"created_by,omitempty"`          // 创建者
	UpdatedBy     string                 `protobuf:"bytes,97,opt,name=updated_by,json=updatedBy,proto3" json:"updated_by,omitempty"`          // 更新者
	CreatedAt     int64                  `protobuf:"varint,98,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`         // 创建时间
	UpdatedAt     int64                  `protobuf:"varint,99,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`         // 更新时间
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SearchProtobufInPerfPlanV2Item) Reset() {
	*x = SearchProtobufInPerfPlanV2Item{}
	mi := &file_manager_perfplan_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SearchProtobufInPerfPlanV2Item) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchProtobufInPerfPlanV2Item) ProtoMessage() {}

func (x *SearchProtobufInPerfPlanV2Item) ProtoReflect() protoreflect.Message {
	mi := &file_manager_perfplan_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchProtobufInPerfPlanV2Item.ProtoReflect.Descriptor instead.
func (*SearchProtobufInPerfPlanV2Item) Descriptor() ([]byte, []int) {
	return file_manager_perfplan_proto_rawDescGZIP(), []int{9}
}

func (x *SearchProtobufInPerfPlanV2Item) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *SearchProtobufInPerfPlanV2Item) GetConfigId() string {
	if x != nil {
		return x.ConfigId
	}
	return ""
}

func (x *SearchProtobufInPerfPlanV2Item) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *SearchProtobufInPerfPlanV2Item) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *SearchProtobufInPerfPlanV2Item) GetGitConfigId() string {
	if x != nil {
		return x.GitConfigId
	}
	return ""
}

func (x *SearchProtobufInPerfPlanV2Item) GetImportPath() string {
	if x != nil {
		return x.ImportPath
	}
	return ""
}

func (x *SearchProtobufInPerfPlanV2Item) GetExcludePaths() []string {
	if x != nil {
		return x.ExcludePaths
	}
	return nil
}

func (x *SearchProtobufInPerfPlanV2Item) GetExcludeFiles() []string {
	if x != nil {
		return x.ExcludeFiles
	}
	return nil
}

func (x *SearchProtobufInPerfPlanV2Item) GetDependencies() []string {
	if x != nil {
		return x.Dependencies
	}
	return nil
}

func (x *SearchProtobufInPerfPlanV2Item) GetCreatedBy() string {
	if x != nil {
		return x.CreatedBy
	}
	return ""
}

func (x *SearchProtobufInPerfPlanV2Item) GetUpdatedBy() string {
	if x != nil {
		return x.UpdatedBy
	}
	return ""
}

func (x *SearchProtobufInPerfPlanV2Item) GetCreatedAt() int64 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

func (x *SearchProtobufInPerfPlanV2Item) GetUpdatedAt() int64 {
	if x != nil {
		return x.UpdatedAt
	}
	return 0
}

type SearchRuleInPerfPlanV2Item struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ProjectId     string                 `protobuf:"bytes,1,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`                             // 项目ID
	RuleId        string                 `protobuf:"bytes,2,opt,name=rule_id,json=ruleId,proto3" json:"rule_id,omitempty"`                                      // 规则ID
	Name          string                 `protobuf:"bytes,11,opt,name=name,proto3" json:"name,omitempty"`                                                       // 规则名称
	Description   string                 `protobuf:"bytes,12,opt,name=description,proto3" json:"description,omitempty"`                                         // 规则描述
	MetricType    pb.MetricType          `protobuf:"varint,13,opt,name=metric_type,json=metricType,proto3,enum=common.MetricType" json:"metric_type,omitempty"` // 指标类型
	Threshold     float64                `protobuf:"fixed64,14,opt,name=threshold,proto3" json:"threshold,omitempty"`                                           // 阀值
	Duration      uint32                 `protobuf:"varint,15,opt,name=duration,proto3" json:"duration,omitempty"`                                              // 持续时间
	State         CommonState            `protobuf:"varint,21,opt,name=state,proto3,enum=manager.CommonState" json:"state,omitempty"`                           // 状态
	CreatedBy     string                 `protobuf:"bytes,96,opt,name=created_by,json=createdBy,proto3" json:"created_by,omitempty"`                            // 创建者
	UpdatedBy     string                 `protobuf:"bytes,97,opt,name=updated_by,json=updatedBy,proto3" json:"updated_by,omitempty"`                            // 更新者
	CreatedAt     int64                  `protobuf:"varint,98,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`                           // 创建时间
	UpdatedAt     int64                  `protobuf:"varint,99,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`                           // 更新时间
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SearchRuleInPerfPlanV2Item) Reset() {
	*x = SearchRuleInPerfPlanV2Item{}
	mi := &file_manager_perfplan_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SearchRuleInPerfPlanV2Item) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchRuleInPerfPlanV2Item) ProtoMessage() {}

func (x *SearchRuleInPerfPlanV2Item) ProtoReflect() protoreflect.Message {
	mi := &file_manager_perfplan_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchRuleInPerfPlanV2Item.ProtoReflect.Descriptor instead.
func (*SearchRuleInPerfPlanV2Item) Descriptor() ([]byte, []int) {
	return file_manager_perfplan_proto_rawDescGZIP(), []int{10}
}

func (x *SearchRuleInPerfPlanV2Item) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *SearchRuleInPerfPlanV2Item) GetRuleId() string {
	if x != nil {
		return x.RuleId
	}
	return ""
}

func (x *SearchRuleInPerfPlanV2Item) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *SearchRuleInPerfPlanV2Item) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *SearchRuleInPerfPlanV2Item) GetMetricType() pb.MetricType {
	if x != nil {
		return x.MetricType
	}
	return pb.MetricType(0)
}

func (x *SearchRuleInPerfPlanV2Item) GetThreshold() float64 {
	if x != nil {
		return x.Threshold
	}
	return 0
}

func (x *SearchRuleInPerfPlanV2Item) GetDuration() uint32 {
	if x != nil {
		return x.Duration
	}
	return 0
}

func (x *SearchRuleInPerfPlanV2Item) GetState() CommonState {
	if x != nil {
		return x.State
	}
	return CommonState_CS_NULL
}

func (x *SearchRuleInPerfPlanV2Item) GetCreatedBy() string {
	if x != nil {
		return x.CreatedBy
	}
	return ""
}

func (x *SearchRuleInPerfPlanV2Item) GetUpdatedBy() string {
	if x != nil {
		return x.UpdatedBy
	}
	return ""
}

func (x *SearchRuleInPerfPlanV2Item) GetCreatedAt() int64 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

func (x *SearchRuleInPerfPlanV2Item) GetUpdatedAt() int64 {
	if x != nil {
		return x.UpdatedAt
	}
	return 0
}

var File_manager_perfplan_proto protoreflect.FileDescriptor

var file_manager_perfplan_proto_rawDesc = []byte{
	0x0a, 0x16, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2f, 0x70, 0x65, 0x72, 0x66, 0x70, 0x6c,
	0x61, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x07, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65,
	0x72, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69,
	0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x11, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x12, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x11, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x70, 0x65, 0x72, 0x66, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x12, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2f, 0x62, 0x61,
	0x73, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xd7, 0x05, 0x0a, 0x08, 0x50, 0x65, 0x72,
	0x66, 0x50, 0x6c, 0x61, 0x6e, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65,
	0x63, 0x74, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x70, 0x6c, 0x61, 0x6e, 0x5f, 0x69, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x70, 0x6c, 0x61, 0x6e, 0x49, 0x64, 0x12, 0x12, 0x0a,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x12, 0x27, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x13, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x72, 0x69, 0x67, 0x67,
	0x65, 0x72, 0x4d, 0x6f, 0x64, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x12, 0x0a, 0x04,
	0x74, 0x61, 0x67, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x09, 0x52, 0x04, 0x74, 0x61, 0x67, 0x73,
	0x12, 0x2a, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x14, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x12, 0x2c, 0x0a, 0x08,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x10,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c,
	0x52, 0x08, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x12, 0x2c, 0x0a, 0x12, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x69, 0x64,
	0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x49, 0x64, 0x12, 0x2a, 0x0a, 0x11, 0x67, 0x65, 0x6e, 0x65,
	0x72, 0x61, 0x6c, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x69, 0x64, 0x18, 0x0a, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0f, 0x67, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x6c, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x49, 0x64, 0x12, 0x2a, 0x0a, 0x11, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f,
	0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x69, 0x64, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x49, 0x64,
	0x12, 0x1a, 0x0a, 0x08, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0c, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x08, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x38, 0x0a, 0x0a,
	0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x65, 0x6e, 0x76, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x19, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74,
	0x45, 0x6e, 0x76, 0x69, 0x72, 0x6f, 0x6e, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x09, 0x74, 0x61, 0x72,
	0x67, 0x65, 0x74, 0x45, 0x6e, 0x76, 0x12, 0x33, 0x0a, 0x09, 0x6b, 0x65, 0x65, 0x70, 0x61, 0x6c,
	0x69, 0x76, 0x65, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2e, 0x50, 0x65, 0x72, 0x66, 0x4b, 0x65, 0x65, 0x70, 0x61, 0x6c, 0x69, 0x76, 0x65,
	0x52, 0x09, 0x6b, 0x65, 0x65, 0x70, 0x61, 0x6c, 0x69, 0x76, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x64,
	0x65, 0x6c, 0x61, 0x79, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05, 0x64, 0x65, 0x6c, 0x61,
	0x79, 0x12, 0x23, 0x0a, 0x0d, 0x6d, 0x61, 0x69, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x64, 0x5f,
	0x62, 0x79, 0x18, 0x10, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x6d, 0x61, 0x69, 0x6e, 0x74, 0x61,
	0x69, 0x6e, 0x65, 0x64, 0x42, 0x79, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x64, 0x5f, 0x62, 0x79, 0x18, 0x60, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x64, 0x42, 0x79, 0x12, 0x1d, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64,
	0x5f, 0x62, 0x79, 0x18, 0x61, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x64, 0x42, 0x79, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f,
	0x61, 0x74, 0x18, 0x62, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x64, 0x41, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61,
	0x74, 0x18, 0x63, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64,
	0x41, 0x74, 0x22, 0x91, 0x05, 0x0a, 0x12, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x50, 0x65, 0x72,
	0x66, 0x50, 0x6c, 0x61, 0x6e, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f,
	0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70,
	0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x70, 0x6c, 0x61, 0x6e,
	0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x70, 0x6c, 0x61, 0x6e, 0x49,
	0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x27, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x13, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54,
	0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x4d, 0x6f, 0x64, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65,
	0x12, 0x12, 0x0a, 0x04, 0x74, 0x61, 0x67, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x09, 0x52, 0x04,
	0x74, 0x61, 0x67, 0x73, 0x12, 0x2a, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x43, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65,
	0x12, 0x2c, 0x0a, 0x08, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x10, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x72, 0x6f, 0x74,
	0x6f, 0x63, 0x6f, 0x6c, 0x52, 0x08, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x12, 0x1a,
	0x0a, 0x08, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x08, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x38, 0x0a, 0x0a, 0x74, 0x61,
	0x72, 0x67, 0x65, 0x74, 0x5f, 0x65, 0x6e, 0x76, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x19,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x45, 0x6e,
	0x76, 0x69, 0x72, 0x6f, 0x6e, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x09, 0x74, 0x61, 0x72, 0x67, 0x65,
	0x74, 0x45, 0x6e, 0x76, 0x12, 0x24, 0x0a, 0x0e, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x5f, 0x6f,
	0x66, 0x5f, 0x63, 0x61, 0x73, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0c, 0x6e, 0x75,
	0x6d, 0x62, 0x65, 0x72, 0x4f, 0x66, 0x43, 0x61, 0x73, 0x65, 0x12, 0x22, 0x0a, 0x0d, 0x6e, 0x75,
	0x6d, 0x62, 0x65, 0x72, 0x5f, 0x6f, 0x66, 0x5f, 0x61, 0x70, 0x69, 0x18, 0x0c, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x0b, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x4f, 0x66, 0x41, 0x70, 0x69, 0x12, 0x35,
	0x0a, 0x0c, 0x73, 0x74, 0x61, 0x74, 0x73, 0x5f, 0x6f, 0x66, 0x5f, 0x61, 0x70, 0x69, 0x18, 0x0d,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x53,
	0x74, 0x61, 0x74, 0x73, 0x4f, 0x66, 0x41, 0x70, 0x69, 0x52, 0x0a, 0x73, 0x74, 0x61, 0x74, 0x73,
	0x4f, 0x66, 0x41, 0x70, 0x69, 0x12, 0x23, 0x0a, 0x0d, 0x6d, 0x61, 0x69, 0x6e, 0x74, 0x61, 0x69,
	0x6e, 0x65, 0x64, 0x5f, 0x62, 0x79, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x6d, 0x61,
	0x69, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x64, 0x42, 0x79, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x62, 0x79, 0x18, 0x60, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x42, 0x79, 0x12, 0x1d, 0x0a, 0x0a, 0x75, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x64, 0x5f, 0x62, 0x79, 0x18, 0x61, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x75,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x42, 0x79, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x62, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x63, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x63, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x75, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x22, 0x4f, 0x0a, 0x0a, 0x53, 0x74, 0x61, 0x74, 0x73, 0x4f,
	0x66, 0x41, 0x70, 0x69, 0x12, 0x1d, 0x0a, 0x0a, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x72,
	0x70, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74,
	0x52, 0x70, 0x73, 0x12, 0x22, 0x0a, 0x0d, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x5f, 0x6f, 0x66,
	0x5f, 0x61, 0x70, 0x69, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0b, 0x6e, 0x75, 0x6d, 0x62,
	0x65, 0x72, 0x4f, 0x66, 0x41, 0x70, 0x69, 0x22, 0xda, 0x05, 0x0a, 0x18, 0x53, 0x65, 0x61, 0x72,
	0x63, 0x68, 0x43, 0x61, 0x73, 0x65, 0x49, 0x6e, 0x50, 0x65, 0x72, 0x66, 0x50, 0x6c, 0x61, 0x6e,
	0x49, 0x74, 0x65, 0x6d, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63,
	0x74, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x63, 0x61, 0x73, 0x65, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x1c, 0x0a, 0x09, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e,
	0x12, 0x12, 0x0a, 0x04, 0x68, 0x61, 0x73, 0x68, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x68, 0x61, 0x73, 0x68, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x74, 0x61, 0x72, 0x67,
	0x65, 0x74, 0x5f, 0x72, 0x70, 0x73, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x74, 0x61,
	0x72, 0x67, 0x65, 0x74, 0x52, 0x70, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x69, 0x6e, 0x69, 0x74, 0x69,
	0x61, 0x6c, 0x5f, 0x72, 0x70, 0x73, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x69, 0x6e,
	0x69, 0x74, 0x69, 0x61, 0x6c, 0x52, 0x70, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x73, 0x74, 0x65, 0x70,
	0x5f, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x73,
	0x74, 0x65, 0x70, 0x48, 0x65, 0x69, 0x67, 0x68, 0x74, 0x12, 0x23, 0x0a, 0x0d, 0x73, 0x74, 0x65,
	0x70, 0x5f, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0c, 0x73, 0x74, 0x65, 0x70, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x20,
	0x0a, 0x0c, 0x70, 0x65, 0x72, 0x66, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x69, 0x64, 0x18, 0x15,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x70, 0x65, 0x72, 0x66, 0x44, 0x61, 0x74, 0x61, 0x49, 0x64,
	0x12, 0x20, 0x0a, 0x0c, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x5f, 0x6f, 0x66, 0x5f, 0x76, 0x75,
	0x18, 0x16, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x4f, 0x66,
	0x56, 0x75, 0x12, 0x20, 0x0a, 0x0c, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x5f, 0x6f, 0x66, 0x5f,
	0x6c, 0x67, 0x18, 0x1f, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72,
	0x4f, 0x66, 0x4c, 0x67, 0x12, 0x26, 0x0a, 0x0f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x73,
	0x5f, 0x6f, 0x66, 0x5f, 0x63, 0x70, 0x75, 0x18, 0x20, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x72,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x73, 0x4f, 0x66, 0x43, 0x70, 0x75, 0x12, 0x2c, 0x0a, 0x12,
	0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x73, 0x5f, 0x6f, 0x66, 0x5f, 0x6d, 0x65, 0x6d, 0x6f,
	0x72, 0x79, 0x18, 0x21, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x73, 0x4f, 0x66, 0x4d, 0x65, 0x6d, 0x6f, 0x72, 0x79, 0x12, 0x22, 0x0a, 0x0d, 0x6c, 0x69,
	0x6d, 0x69, 0x74, 0x73, 0x5f, 0x6f, 0x66, 0x5f, 0x63, 0x70, 0x75, 0x18, 0x22, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0b, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x73, 0x4f, 0x66, 0x43, 0x70, 0x75, 0x12, 0x28,
	0x0a, 0x10, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x73, 0x5f, 0x6f, 0x66, 0x5f, 0x6d, 0x65, 0x6d, 0x6f,
	0x72, 0x79, 0x18, 0x23, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x73,
	0x4f, 0x66, 0x4d, 0x65, 0x6d, 0x6f, 0x72, 0x79, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x64, 0x5f, 0x62, 0x79, 0x18, 0x60, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x64, 0x42, 0x79, 0x12, 0x1d, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x64, 0x5f, 0x62, 0x79, 0x18, 0x61, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x75, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x64, 0x42, 0x79, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x64, 0x5f, 0x61, 0x74, 0x18, 0x62, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64,
	0x5f, 0x61, 0x74, 0x18, 0x63, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x64, 0x41, 0x74, 0x22, 0xc1, 0x07, 0x0a, 0x0a, 0x50, 0x65, 0x72, 0x66, 0x50, 0x6c, 0x61,
	0x6e, 0x56, 0x32, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74,
	0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x70, 0x6c, 0x61, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x70, 0x6c, 0x61, 0x6e, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x63,
	0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0a, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x27, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x13, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x72, 0x69, 0x67, 0x67, 0x65,
	0x72, 0x4d, 0x6f, 0x64, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x27, 0x0a, 0x0f, 0x63,
	0x72, 0x6f, 0x6e, 0x5f, 0x65, 0x78, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x0e,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x63, 0x72, 0x6f, 0x6e, 0x45, 0x78, 0x70, 0x72, 0x65, 0x73,
	0x73, 0x69, 0x6f, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x61, 0x67, 0x73, 0x18, 0x0f, 0x20, 0x03,
	0x28, 0x09, 0x52, 0x04, 0x74, 0x61, 0x67, 0x73, 0x12, 0x2c, 0x0a, 0x08, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x63, 0x6f, 0x6c, 0x18, 0x10, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x10, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x52, 0x08, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x12, 0x38, 0x0a, 0x0a, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74,
	0x5f, 0x65, 0x6e, 0x76, 0x18, 0x11, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x19, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x45, 0x6e, 0x76, 0x69, 0x72, 0x6f,
	0x6e, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x09, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x45, 0x6e, 0x76,
	0x12, 0x2c, 0x0a, 0x12, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x5f, 0x63, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x5f, 0x69, 0x64, 0x18, 0x12, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x49, 0x64, 0x12, 0x2a,
	0x0a, 0x11, 0x67, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x6c, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x5f, 0x69, 0x64, 0x18, 0x13, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x67, 0x65, 0x6e, 0x65, 0x72,
	0x61, 0x6c, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x49, 0x64, 0x12, 0x2a, 0x0a, 0x11, 0x61, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x69, 0x64, 0x18,
	0x14, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x49, 0x64, 0x12, 0x3d, 0x0a, 0x10, 0x61, 0x75, 0x74, 0x68, 0x5f, 0x72,
	0x61, 0x74, 0x65, 0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x73, 0x18, 0x15, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x13, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x52, 0x61, 0x74, 0x65, 0x4c, 0x69,
	0x6d, 0x69, 0x74, 0x56, 0x32, 0x52, 0x0e, 0x61, 0x75, 0x74, 0x68, 0x52, 0x61, 0x74, 0x65, 0x4c,
	0x69, 0x6d, 0x69, 0x74, 0x73, 0x12, 0x27, 0x0a, 0x0f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x5f,
	0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x1f, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0e,
	0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1a,
	0x0a, 0x08, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x20, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x08, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x28, 0x0a, 0x10, 0x63, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x5f, 0x6c, 0x61, 0x72, 0x6b, 0x5f, 0x63, 0x68, 0x61, 0x74, 0x18, 0x21,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x0e, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4c, 0x61, 0x72, 0x6b,
	0x43, 0x68, 0x61, 0x74, 0x12, 0x20, 0x0a, 0x0c, 0x6c, 0x61, 0x72, 0x6b, 0x5f, 0x63, 0x68, 0x61,
	0x74, 0x5f, 0x69, 0x64, 0x18, 0x22, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x6c, 0x61, 0x72, 0x6b,
	0x43, 0x68, 0x61, 0x74, 0x49, 0x64, 0x12, 0x33, 0x0a, 0x15, 0x61, 0x64, 0x76, 0x61, 0x6e, 0x63,
	0x65, 0x64, 0x5f, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x23, 0x20, 0x01, 0x28, 0x08, 0x52, 0x14, 0x61, 0x64, 0x76, 0x61, 0x6e, 0x63, 0x65, 0x64, 0x4e,
	0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x2a, 0x0a, 0x05, 0x73,
	0x74, 0x61, 0x74, 0x65, 0x18, 0x29, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x6d, 0x61, 0x6e,
	0x61, 0x67, 0x65, 0x72, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x65,
	0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x6d, 0x61, 0x69, 0x6e, 0x74,
	0x61, 0x69, 0x6e, 0x65, 0x64, 0x5f, 0x62, 0x79, 0x18, 0x2a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c,
	0x6d, 0x61, 0x69, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x64, 0x42, 0x79, 0x12, 0x1d, 0x0a, 0x0a,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x62, 0x79, 0x18, 0x60, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x42, 0x79, 0x12, 0x1d, 0x0a, 0x0a, 0x75,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x62, 0x79, 0x18, 0x61, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x42, 0x79, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x62, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x75, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x63, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x75,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x22, 0x8f, 0x06, 0x0a, 0x14, 0x53, 0x65, 0x61,
	0x72, 0x63, 0x68, 0x50, 0x65, 0x72, 0x66, 0x50, 0x6c, 0x61, 0x6e, 0x56, 0x32, 0x49, 0x74, 0x65,
	0x6d, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64,
	0x12, 0x17, 0x0a, 0x07, 0x70, 0x6c, 0x61, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x70, 0x6c, 0x61, 0x6e, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x61, 0x74,
	0x65, 0x67, 0x6f, 0x72, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a,
	0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x20,
	0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0c, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x12, 0x27, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x13,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x4d,
	0x6f, 0x64, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x27, 0x0a, 0x0f, 0x63, 0x72, 0x6f,
	0x6e, 0x5f, 0x65, 0x78, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x0e, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0e, 0x63, 0x72, 0x6f, 0x6e, 0x45, 0x78, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69,
	0x6f, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x61, 0x67, 0x73, 0x18, 0x0f, 0x20, 0x03, 0x28, 0x09,
	0x52, 0x04, 0x74, 0x61, 0x67, 0x73, 0x12, 0x2c, 0x0a, 0x08, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63,
	0x6f, 0x6c, 0x18, 0x10, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x10, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2e, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x52, 0x08, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x63, 0x6f, 0x6c, 0x12, 0x38, 0x0a, 0x0a, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x65,
	0x6e, 0x76, 0x18, 0x11, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x19, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2e, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x45, 0x6e, 0x76, 0x69, 0x72, 0x6f, 0x6e, 0x6d,
	0x65, 0x6e, 0x74, 0x52, 0x09, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x45, 0x6e, 0x76, 0x12, 0x27,
	0x0a, 0x0f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x5f, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x18, 0x1f, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0e, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x44,
	0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1a, 0x0a, 0x08, 0x64, 0x75, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x18, 0x20, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x64, 0x75, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x12, 0x26, 0x0a, 0x0f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x5f, 0x6f, 0x66,
	0x5f, 0x63, 0x61, 0x73, 0x65, 0x73, 0x18, 0x29, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0d, 0x6e, 0x75,
	0x6d, 0x62, 0x65, 0x72, 0x4f, 0x66, 0x43, 0x61, 0x73, 0x65, 0x73, 0x12, 0x26, 0x0a, 0x0f, 0x6e,
	0x75, 0x6d, 0x62, 0x65, 0x72, 0x5f, 0x6f, 0x66, 0x5f, 0x73, 0x74, 0x65, 0x70, 0x73, 0x18, 0x2a,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x0d, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x4f, 0x66, 0x53, 0x74,
	0x65, 0x70, 0x73, 0x12, 0x38, 0x0a, 0x0d, 0x73, 0x74, 0x61, 0x74, 0x73, 0x5f, 0x6f, 0x66, 0x5f,
	0x73, 0x74, 0x65, 0x70, 0x18, 0x2b, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x6d, 0x61, 0x6e,
	0x61, 0x67, 0x65, 0x72, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x73, 0x4f, 0x66, 0x53, 0x74, 0x65, 0x70,
	0x52, 0x0b, 0x73, 0x74, 0x61, 0x74, 0x73, 0x4f, 0x66, 0x53, 0x74, 0x65, 0x70, 0x12, 0x2a, 0x0a,
	0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x33, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x6d,
	0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x53, 0x74, 0x61,
	0x74, 0x65, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x6d, 0x61, 0x69,
	0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x64, 0x5f, 0x62, 0x79, 0x18, 0x34, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0c, 0x6d, 0x61, 0x69, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x64, 0x42, 0x79, 0x12, 0x1d,
	0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x62, 0x79, 0x18, 0x60, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x42, 0x79, 0x12, 0x1d, 0x0a,
	0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x62, 0x79, 0x18, 0x61, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x42, 0x79, 0x12, 0x1d, 0x0a, 0x0a,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x62, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x75,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x63, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x22, 0x54, 0x0a, 0x0b, 0x53, 0x74,
	0x61, 0x74, 0x73, 0x4f, 0x66, 0x53, 0x74, 0x65, 0x70, 0x12, 0x1d, 0x0a, 0x0a, 0x74, 0x61, 0x72,
	0x67, 0x65, 0x74, 0x5f, 0x72, 0x70, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x74,
	0x61, 0x72, 0x67, 0x65, 0x74, 0x52, 0x70, 0x73, 0x12, 0x26, 0x0a, 0x0f, 0x6e, 0x75, 0x6d, 0x62,
	0x65, 0x72, 0x5f, 0x6f, 0x66, 0x5f, 0x73, 0x74, 0x65, 0x70, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x0d, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x4f, 0x66, 0x53, 0x74, 0x65, 0x70, 0x73,
	0x22, 0xac, 0x04, 0x0a, 0x12, 0x50, 0x65, 0x72, 0x66, 0x50, 0x6c, 0x61, 0x6e, 0x43, 0x61, 0x73,
	0x65, 0x56, 0x32, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x37, 0x0a, 0x07, 0x63, 0x61, 0x73, 0x65, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x1e, 0xfa, 0x42, 0x1b, 0x72, 0x19, 0x18,
	0x40, 0x32, 0x15, 0x28, 0x3f, 0x3a, 0x5e, 0x70, 0x65, 0x72, 0x66, 0x5f, 0x63, 0x61, 0x73, 0x65,
	0x5f, 0x69, 0x64, 0x3a, 0x2e, 0x2b, 0x3f, 0x29, 0x52, 0x06, 0x63, 0x61, 0x73, 0x65, 0x49, 0x64,
	0x12, 0x43, 0x0a, 0x0c, 0x70, 0x65, 0x72, 0x66, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x69, 0x64,
	0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x42, 0x21, 0xfa, 0x42, 0x1e, 0x72, 0x1c, 0x18, 0x40, 0x32,
	0x15, 0x28, 0x3f, 0x3a, 0x5e, 0x70, 0x65, 0x72, 0x66, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x69,
	0x64, 0x3a, 0x2e, 0x2b, 0x3f, 0x29, 0xd0, 0x01, 0x01, 0x52, 0x0a, 0x70, 0x65, 0x72, 0x66, 0x44,
	0x61, 0x74, 0x61, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x5f,
	0x76, 0x75, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x56, 0x75, 0x12, 0x29, 0x0a, 0x0c, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x5f, 0x6f, 0x66, 0x5f,
	0x76, 0x75, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0d, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x2a, 0x02, 0x28,
	0x00, 0x52, 0x0a, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x4f, 0x66, 0x56, 0x75, 0x12, 0x1b, 0x0a,
	0x09, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x5f, 0x6c, 0x67, 0x18, 0x15, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x08, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x4c, 0x67, 0x12, 0x29, 0x0a, 0x0c, 0x6e, 0x75,
	0x6d, 0x62, 0x65, 0x72, 0x5f, 0x6f, 0x66, 0x5f, 0x6c, 0x67, 0x18, 0x16, 0x20, 0x01, 0x28, 0x0d,
	0x42, 0x07, 0xfa, 0x42, 0x04, 0x2a, 0x02, 0x28, 0x00, 0x52, 0x0a, 0x6e, 0x75, 0x6d, 0x62, 0x65,
	0x72, 0x4f, 0x66, 0x4c, 0x67, 0x12, 0x32, 0x0a, 0x0f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x73, 0x5f, 0x6f, 0x66, 0x5f, 0x63, 0x70, 0x75, 0x18, 0x17, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0a,
	0xfa, 0x42, 0x07, 0x72, 0x05, 0x10, 0x01, 0xd0, 0x01, 0x01, 0x52, 0x0d, 0x72, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x73, 0x4f, 0x66, 0x43, 0x70, 0x75, 0x12, 0x38, 0x0a, 0x12, 0x72, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x73, 0x5f, 0x6f, 0x66, 0x5f, 0x6d, 0x65, 0x6d, 0x6f, 0x72, 0x79, 0x18,
	0x18, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x72, 0x05, 0x10, 0x01, 0xd0, 0x01,
	0x01, 0x52, 0x10, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x73, 0x4f, 0x66, 0x4d, 0x65, 0x6d,
	0x6f, 0x72, 0x79, 0x12, 0x2e, 0x0a, 0x0d, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x73, 0x5f, 0x6f, 0x66,
	0x5f, 0x63, 0x70, 0x75, 0x18, 0x19, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x72,
	0x05, 0x10, 0x01, 0xd0, 0x01, 0x01, 0x52, 0x0b, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x73, 0x4f, 0x66,
	0x43, 0x70, 0x75, 0x12, 0x34, 0x0a, 0x10, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x73, 0x5f, 0x6f, 0x66,
	0x5f, 0x6d, 0x65, 0x6d, 0x6f, 0x72, 0x79, 0x18, 0x1a, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0a, 0xfa,
	0x42, 0x07, 0x72, 0x05, 0x10, 0x01, 0xd0, 0x01, 0x01, 0x52, 0x0e, 0x6c, 0x69, 0x6d, 0x69, 0x74,
	0x73, 0x4f, 0x66, 0x4d, 0x65, 0x6d, 0x6f, 0x72, 0x79, 0x12, 0x34, 0x0a, 0x0b, 0x72, 0x61, 0x74,
	0x65, 0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x73, 0x18, 0x1b, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x13,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x52, 0x61, 0x74, 0x65, 0x4c, 0x69, 0x6d, 0x69,
	0x74, 0x56, 0x32, 0x52, 0x0a, 0x72, 0x61, 0x74, 0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x73, 0x22,
	0xf5, 0x07, 0x0a, 0x1a, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x43, 0x61, 0x73, 0x65, 0x49, 0x6e,
	0x50, 0x65, 0x72, 0x66, 0x50, 0x6c, 0x61, 0x6e, 0x56, 0x32, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x1d,
	0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x12, 0x1f, 0x0a,
	0x0b, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0a, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x49, 0x64, 0x12, 0x17,
	0x0a, 0x07, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x63, 0x61, 0x73, 0x65, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64,
	0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x12, 0x0a,
	0x04, 0x74, 0x61, 0x67, 0x73, 0x18, 0x0d, 0x20, 0x03, 0x28, 0x09, 0x52, 0x04, 0x74, 0x61, 0x67,
	0x73, 0x12, 0x2c, 0x0a, 0x08, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x18, 0x0e, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x10, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x72, 0x6f,
	0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x52, 0x08, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x12,
	0x34, 0x0a, 0x0b, 0x72, 0x61, 0x74, 0x65, 0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x73, 0x18, 0x0f,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x52, 0x61,
	0x74, 0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x56, 0x32, 0x52, 0x0a, 0x72, 0x61, 0x74, 0x65, 0x4c,
	0x69, 0x6d, 0x69, 0x74, 0x73, 0x12, 0x39, 0x0a, 0x0c, 0x73, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x5f,
	0x73, 0x74, 0x65, 0x70, 0x73, 0x18, 0x10, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x65, 0x72, 0x66, 0x43, 0x61, 0x73, 0x65, 0x53, 0x74, 0x65,
	0x70, 0x56, 0x32, 0x52, 0x0b, 0x73, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x53, 0x74, 0x65, 0x70, 0x73,
	0x12, 0x3d, 0x0a, 0x0e, 0x70, 0x61, 0x72, 0x61, 0x6c, 0x6c, 0x65, 0x6c, 0x5f, 0x73, 0x74, 0x65,
	0x70, 0x73, 0x18, 0x11, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2e, 0x50, 0x65, 0x72, 0x66, 0x43, 0x61, 0x73, 0x65, 0x53, 0x74, 0x65, 0x70, 0x56, 0x32,
	0x52, 0x0d, 0x70, 0x61, 0x72, 0x61, 0x6c, 0x6c, 0x65, 0x6c, 0x53, 0x74, 0x65, 0x70, 0x73, 0x12,
	0x26, 0x0a, 0x0f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x5f, 0x6f, 0x66, 0x5f, 0x73, 0x74, 0x65,
	0x70, 0x73, 0x18, 0x15, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0d, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72,
	0x4f, 0x66, 0x53, 0x74, 0x65, 0x70, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x74, 0x61, 0x72, 0x67, 0x65,
	0x74, 0x5f, 0x72, 0x70, 0x73, 0x18, 0x16, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x74, 0x61, 0x72,
	0x67, 0x65, 0x74, 0x52, 0x70, 0x73, 0x12, 0x20, 0x0a, 0x0c, 0x70, 0x65, 0x72, 0x66, 0x5f, 0x64,
	0x61, 0x74, 0x61, 0x5f, 0x69, 0x64, 0x18, 0x1f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x70, 0x65,
	0x72, 0x66, 0x44, 0x61, 0x74, 0x61, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x63, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x5f, 0x76, 0x75, 0x18, 0x20, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x63, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x56, 0x75, 0x12, 0x20, 0x0a, 0x0c, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x5f,
	0x6f, 0x66, 0x5f, 0x76, 0x75, 0x18, 0x21, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x6e, 0x75, 0x6d,
	0x62, 0x65, 0x72, 0x4f, 0x66, 0x56, 0x75, 0x12, 0x1b, 0x0a, 0x09, 0x63, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x5f, 0x6c, 0x67, 0x18, 0x29, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x63, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x4c, 0x67, 0x12, 0x20, 0x0a, 0x0c, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x5f, 0x6f,
	0x66, 0x5f, 0x6c, 0x67, 0x18, 0x2a, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x6e, 0x75, 0x6d, 0x62,
	0x65, 0x72, 0x4f, 0x66, 0x4c, 0x67, 0x12, 0x26, 0x0a, 0x0f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x73, 0x5f, 0x6f, 0x66, 0x5f, 0x63, 0x70, 0x75, 0x18, 0x2b, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0d, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x73, 0x4f, 0x66, 0x43, 0x70, 0x75, 0x12, 0x2c,
	0x0a, 0x12, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x73, 0x5f, 0x6f, 0x66, 0x5f, 0x6d, 0x65,
	0x6d, 0x6f, 0x72, 0x79, 0x18, 0x2c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x72, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x73, 0x4f, 0x66, 0x4d, 0x65, 0x6d, 0x6f, 0x72, 0x79, 0x12, 0x22, 0x0a, 0x0d,
	0x6c, 0x69, 0x6d, 0x69, 0x74, 0x73, 0x5f, 0x6f, 0x66, 0x5f, 0x63, 0x70, 0x75, 0x18, 0x2d, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0b, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x73, 0x4f, 0x66, 0x43, 0x70, 0x75,
	0x12, 0x28, 0x0a, 0x10, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x73, 0x5f, 0x6f, 0x66, 0x5f, 0x6d, 0x65,
	0x6d, 0x6f, 0x72, 0x79, 0x18, 0x2e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x6c, 0x69, 0x6d, 0x69,
	0x74, 0x73, 0x4f, 0x66, 0x4d, 0x65, 0x6d, 0x6f, 0x72, 0x79, 0x12, 0x2a, 0x0a, 0x05, 0x73, 0x74,
	0x61, 0x74, 0x65, 0x18, 0x33, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x6d, 0x61, 0x6e, 0x61,
	0x67, 0x65, 0x72, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52,
	0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x6d, 0x61, 0x69, 0x6e, 0x74, 0x61,
	0x69, 0x6e, 0x65, 0x64, 0x5f, 0x62, 0x79, 0x18, 0x34, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x6d,
	0x61, 0x69, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x64, 0x42, 0x79, 0x12, 0x1d, 0x0a, 0x0a, 0x63,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x62, 0x79, 0x18, 0x60, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x42, 0x79, 0x12, 0x1d, 0x0a, 0x0a, 0x75, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x62, 0x79, 0x18, 0x61, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x42, 0x79, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x62, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x63,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x63, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x75, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x22, 0xc1, 0x03, 0x0a, 0x1e, 0x53, 0x65, 0x61, 0x72,
	0x63, 0x68, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x49, 0x6e, 0x50, 0x65, 0x72, 0x66,
	0x50, 0x6c, 0x61, 0x6e, 0x56, 0x32, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72,
	0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x63, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x0b,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x22, 0x0a, 0x0d,
	0x67, 0x69, 0x74, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x69, 0x64, 0x18, 0x0d, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0b, 0x67, 0x69, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x49, 0x64,
	0x12, 0x1f, 0x0a, 0x0b, 0x69, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x18,
	0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x69, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x50, 0x61, 0x74,
	0x68, 0x12, 0x23, 0x0a, 0x0d, 0x65, 0x78, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x5f, 0x70, 0x61, 0x74,
	0x68, 0x73, 0x18, 0x0f, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0c, 0x65, 0x78, 0x63, 0x6c, 0x75, 0x64,
	0x65, 0x50, 0x61, 0x74, 0x68, 0x73, 0x12, 0x23, 0x0a, 0x0d, 0x65, 0x78, 0x63, 0x6c, 0x75, 0x64,
	0x65, 0x5f, 0x66, 0x69, 0x6c, 0x65, 0x73, 0x18, 0x10, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0c, 0x65,
	0x78, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x46, 0x69, 0x6c, 0x65, 0x73, 0x12, 0x22, 0x0a, 0x0c, 0x64,
	0x65, 0x70, 0x65, 0x6e, 0x64, 0x65, 0x6e, 0x63, 0x69, 0x65, 0x73, 0x18, 0x11, 0x20, 0x03, 0x28,
	0x09, 0x52, 0x0c, 0x64, 0x65, 0x70, 0x65, 0x6e, 0x64, 0x65, 0x6e, 0x63, 0x69, 0x65, 0x73, 0x12,
	0x1d, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x62, 0x79, 0x18, 0x60, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x42, 0x79, 0x12, 0x1d,
	0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x62, 0x79, 0x18, 0x61, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x42, 0x79, 0x12, 0x1d, 0x0a,
	0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x62, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x1d, 0x0a, 0x0a,
	0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x63, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x22, 0xa1, 0x03, 0x0a, 0x1a,
	0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x52, 0x75, 0x6c, 0x65, 0x49, 0x6e, 0x50, 0x65, 0x72, 0x66,
	0x50, 0x6c, 0x61, 0x6e, 0x56, 0x32, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72,
	0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x72, 0x75, 0x6c,
	0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x75, 0x6c, 0x65,
	0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x33, 0x0a, 0x0b, 0x6d, 0x65, 0x74, 0x72,
	0x69, 0x63, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x12, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x54, 0x79, 0x70,
	0x65, 0x52, 0x0a, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1c, 0x0a,
	0x09, 0x74, 0x68, 0x72, 0x65, 0x73, 0x68, 0x6f, 0x6c, 0x64, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x01,
	0x52, 0x09, 0x74, 0x68, 0x72, 0x65, 0x73, 0x68, 0x6f, 0x6c, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x64,
	0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x64,
	0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x2a, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65,
	0x18, 0x15, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72,
	0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x05, 0x73, 0x74,
	0x61, 0x74, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x62,
	0x79, 0x18, 0x60, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64,
	0x42, 0x79, 0x12, 0x1d, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x62, 0x79,
	0x18, 0x61, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x42,
	0x79, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18,
	0x62, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74,
	0x12, 0x1d, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x63,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x42,
	0x41, 0x5a, 0x3f, 0x67, 0x69, 0x74, 0x6c, 0x61, 0x62, 0x2e, 0x74, 0x74, 0x79, 0x75, 0x79, 0x69,
	0x6e, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x54, 0x65, 0x73, 0x74, 0x44, 0x65, 0x76, 0x65, 0x6c, 0x6f,
	0x70, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x70, 0x72, 0x6f, 0x62, 0x65, 0x2d, 0x62, 0x61, 0x63, 0x6b,
	0x65, 0x6e, 0x64, 0x2f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2f, 0x72, 0x70, 0x63, 0x2f,
	0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_manager_perfplan_proto_rawDescOnce sync.Once
	file_manager_perfplan_proto_rawDescData = file_manager_perfplan_proto_rawDesc
)

func file_manager_perfplan_proto_rawDescGZIP() []byte {
	file_manager_perfplan_proto_rawDescOnce.Do(func() {
		file_manager_perfplan_proto_rawDescData = protoimpl.X.CompressGZIP(file_manager_perfplan_proto_rawDescData)
	})
	return file_manager_perfplan_proto_rawDescData
}

var file_manager_perfplan_proto_msgTypes = make([]protoimpl.MessageInfo, 11)
var file_manager_perfplan_proto_goTypes = []any{
	(*PerfPlan)(nil),                       // 0: manager.PerfPlan
	(*SearchPerfPlanItem)(nil),             // 1: manager.SearchPerfPlanItem
	(*StatsOfApi)(nil),                     // 2: manager.StatsOfApi
	(*SearchCaseInPerfPlanItem)(nil),       // 3: manager.SearchCaseInPerfPlanItem
	(*PerfPlanV2)(nil),                     // 4: manager.PerfPlanV2
	(*SearchPerfPlanV2Item)(nil),           // 5: manager.SearchPerfPlanV2Item
	(*StatsOfStep)(nil),                    // 6: manager.StatsOfStep
	(*PerfPlanCaseV2Item)(nil),             // 7: manager.PerfPlanCaseV2Item
	(*SearchCaseInPerfPlanV2Item)(nil),     // 8: manager.SearchCaseInPerfPlanV2Item
	(*SearchProtobufInPerfPlanV2Item)(nil), // 9: manager.SearchProtobufInPerfPlanV2Item
	(*SearchRuleInPerfPlanV2Item)(nil),     // 10: manager.SearchRuleInPerfPlanV2Item
	(pb.TriggerMode)(0),                    // 11: common.TriggerMode
	(CommonState)(0),                       // 12: manager.CommonState
	(pb.Protocol)(0),                       // 13: common.Protocol
	(pb.TargetEnvironment)(0),              // 14: common.TargetEnvironment
	(*pb.PerfKeepalive)(nil),               // 15: common.PerfKeepalive
	(*pb.RateLimitV2)(nil),                 // 16: common.RateLimitV2
	(*pb.PerfCaseStepV2)(nil),              // 17: common.PerfCaseStepV2
	(pb.MetricType)(0),                     // 18: common.MetricType
}
var file_manager_perfplan_proto_depIdxs = []int32{
	11, // 0: manager.PerfPlan.type:type_name -> common.TriggerMode
	12, // 1: manager.PerfPlan.state:type_name -> manager.CommonState
	13, // 2: manager.PerfPlan.protocol:type_name -> common.Protocol
	14, // 3: manager.PerfPlan.target_env:type_name -> common.TargetEnvironment
	15, // 4: manager.PerfPlan.keepalive:type_name -> common.PerfKeepalive
	11, // 5: manager.SearchPerfPlanItem.type:type_name -> common.TriggerMode
	12, // 6: manager.SearchPerfPlanItem.state:type_name -> manager.CommonState
	13, // 7: manager.SearchPerfPlanItem.protocol:type_name -> common.Protocol
	14, // 8: manager.SearchPerfPlanItem.target_env:type_name -> common.TargetEnvironment
	2,  // 9: manager.SearchPerfPlanItem.stats_of_api:type_name -> manager.StatsOfApi
	11, // 10: manager.PerfPlanV2.type:type_name -> common.TriggerMode
	13, // 11: manager.PerfPlanV2.protocol:type_name -> common.Protocol
	14, // 12: manager.PerfPlanV2.target_env:type_name -> common.TargetEnvironment
	16, // 13: manager.PerfPlanV2.auth_rate_limits:type_name -> common.RateLimitV2
	12, // 14: manager.PerfPlanV2.state:type_name -> manager.CommonState
	11, // 15: manager.SearchPerfPlanV2Item.type:type_name -> common.TriggerMode
	13, // 16: manager.SearchPerfPlanV2Item.protocol:type_name -> common.Protocol
	14, // 17: manager.SearchPerfPlanV2Item.target_env:type_name -> common.TargetEnvironment
	6,  // 18: manager.SearchPerfPlanV2Item.stats_of_step:type_name -> manager.StatsOfStep
	12, // 19: manager.SearchPerfPlanV2Item.state:type_name -> manager.CommonState
	16, // 20: manager.PerfPlanCaseV2Item.rate_limits:type_name -> common.RateLimitV2
	13, // 21: manager.SearchCaseInPerfPlanV2Item.protocol:type_name -> common.Protocol
	16, // 22: manager.SearchCaseInPerfPlanV2Item.rate_limits:type_name -> common.RateLimitV2
	17, // 23: manager.SearchCaseInPerfPlanV2Item.serial_steps:type_name -> common.PerfCaseStepV2
	17, // 24: manager.SearchCaseInPerfPlanV2Item.parallel_steps:type_name -> common.PerfCaseStepV2
	12, // 25: manager.SearchCaseInPerfPlanV2Item.state:type_name -> manager.CommonState
	18, // 26: manager.SearchRuleInPerfPlanV2Item.metric_type:type_name -> common.MetricType
	12, // 27: manager.SearchRuleInPerfPlanV2Item.state:type_name -> manager.CommonState
	28, // [28:28] is the sub-list for method output_type
	28, // [28:28] is the sub-list for method input_type
	28, // [28:28] is the sub-list for extension type_name
	28, // [28:28] is the sub-list for extension extendee
	0,  // [0:28] is the sub-list for field type_name
}

func init() { file_manager_perfplan_proto_init() }
func file_manager_perfplan_proto_init() {
	if File_manager_perfplan_proto != nil {
		return
	}
	file_manager_base_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_manager_perfplan_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   11,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_manager_perfplan_proto_goTypes,
		DependencyIndexes: file_manager_perfplan_proto_depIdxs,
		MessageInfos:      file_manager_perfplan_proto_msgTypes,
	}.Build()
	File_manager_perfplan_proto = out.File
	file_manager_perfplan_proto_rawDesc = nil
	file_manager_perfplan_proto_goTypes = nil
	file_manager_perfplan_proto_depIdxs = nil
}
