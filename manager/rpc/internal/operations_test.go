package internal

import (
	"reflect"
	"testing"
	"time"

	"github.com/gorhill/cronexpr"
	"github.com/robfig/cron/v3"
)

func TestParseCronExpressionWithCronV3(t *testing.T) {
	startedAt := time.Date(2025, time.March, 6, 14, 32, 43, 0, time.Local)

	type args struct {
		spec  string
		times int
	}
	tests := []struct {
		name string
		args args
		want []time.Time
	}{
		{
			name: "at 21:11 every Wednesday",
			args: args{
				spec:  "11 21 * * 3",
				times: 5,
			},
			want: []time.Time{
				time.Date(2025, time.March, 12, 21, 11, 0, 0, time.Local),
				time.Date(2025, time.March, 19, 21, 11, 0, 0, time.Local),
				time.Date(2025, time.March, 26, 21, 11, 0, 0, time.Local),
				time.Date(2025, time.April, 2, 21, 11, 0, 0, time.Local),
				time.Date(2025, time.April, 9, 21, 11, 0, 0, time.Local),
			},
		},
		{
			name: "at 22:22 every day",
			args: args{
				spec:  "22 22 * * ?",
				times: 5,
			},
			want: []time.Time{
				time.Date(2025, time.March, 6, 22, 22, 0, 0, time.Local),
				time.Date(2025, time.March, 7, 22, 22, 0, 0, time.Local),
				time.Date(2025, time.March, 8, 22, 22, 0, 0, time.Local),
				time.Date(2025, time.March, 9, 22, 22, 0, 0, time.Local),
				time.Date(2025, time.March, 10, 22, 22, 0, 0, time.Local),
			},
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				schedule, err := cron.ParseStandard(tt.args.spec)
				if err != nil {
					t.Fatal(err)
				}

				times := tt.args.times
				if times <= 0 {
					times = 1
				}

				time_ := startedAt
				got := make([]time.Time, 0, times)
				for i := 0; i < times; i++ {
					time_ = schedule.Next(time_)
					got = append(got, time_)
					t.Logf("%d next: %s", i, time_.Format(time.DateTime))
				}

				if !reflect.DeepEqual(tt.want, got) {
					t.Errorf("\nwant: %v\ngot:  %v", tt.want, got)
				}
			},
		)
	}
}

func TestParseCronExpressionWithCronExpr(t *testing.T) {
	startedAt, err := time.ParseInLocation(time.DateTime, "2025-03-06 14:32:43", time.Local)
	if err != nil {
		t.Fatal(err)
	}

	type args struct {
		spec  string
		times int
	}
	tests := []struct {
		name string
		args args
		want []time.Time
	}{
		{
			name: "at 21:11 every Wednesday",
			args: args{
				spec:  "11 21 * * 3",
				times: 5,
			},
			want: []time.Time{
				time.Date(2025, time.March, 12, 21, 11, 0, 0, time.Local),
				time.Date(2025, time.March, 19, 21, 11, 0, 0, time.Local),
				time.Date(2025, time.March, 26, 21, 11, 0, 0, time.Local),
				time.Date(2025, time.April, 2, 21, 11, 0, 0, time.Local),
				time.Date(2025, time.April, 9, 21, 11, 0, 0, time.Local),
			},
		},
		{
			name: "at 22:22 every day",
			args: args{
				spec:  "22 22 * * ?",
				times: 5,
			},
			want: []time.Time{
				time.Date(2025, time.March, 6, 22, 22, 0, 0, time.Local),
				time.Date(2025, time.March, 7, 22, 22, 0, 0, time.Local),
				time.Date(2025, time.March, 8, 22, 22, 0, 0, time.Local),
				time.Date(2025, time.March, 9, 22, 22, 0, 0, time.Local),
				time.Date(2025, time.March, 10, 22, 22, 0, 0, time.Local),
			},
		},
		{
			name: "at 10:10 every the 4th Tuesday of the month",
			args: args{
				spec:  "0 10 * * 2#4",
				times: 10,
			},
			want: []time.Time{
				time.Date(2025, time.March, 25, 10, 0, 0, 0, time.Local),
				time.Date(2025, time.April, 22, 10, 0, 0, 0, time.Local),
				time.Date(2025, time.May, 27, 10, 0, 0, 0, time.Local),
				time.Date(2025, time.June, 24, 10, 0, 0, 0, time.Local),
				time.Date(2025, time.July, 22, 10, 0, 0, 0, time.Local),
				time.Date(2025, time.August, 26, 10, 0, 0, 0, time.Local),
				time.Date(2025, time.September, 23, 10, 0, 0, 0, time.Local),
				time.Date(2025, time.October, 28, 10, 0, 0, 0, time.Local),
				time.Date(2025, time.November, 25, 10, 0, 0, 0, time.Local),
				time.Date(2025, time.December, 23, 10, 0, 0, 0, time.Local),
			},
		},
		{
			name: "at 12:23 every last Thursday of the month",
			args: args{
				spec:  "23 12 * * 4L",
				times: 12,
			},
			want: []time.Time{
				time.Date(2025, time.March, 27, 12, 23, 0, 0, time.Local),
				time.Date(2025, time.April, 24, 12, 23, 0, 0, time.Local),
				time.Date(2025, time.May, 29, 12, 23, 0, 0, time.Local),
				time.Date(2025, time.June, 26, 12, 23, 0, 0, time.Local),
				time.Date(2025, time.July, 31, 12, 23, 0, 0, time.Local),
				time.Date(2025, time.August, 28, 12, 23, 0, 0, time.Local),
				time.Date(2025, time.September, 25, 12, 23, 0, 0, time.Local),
				time.Date(2025, time.October, 30, 12, 23, 0, 0, time.Local),
				time.Date(2025, time.November, 27, 12, 23, 0, 0, time.Local),
				time.Date(2025, time.December, 25, 12, 23, 0, 0, time.Local),
				time.Date(2026, time.January, 29, 12, 23, 0, 0, time.Local),
				time.Date(2026, time.February, 26, 12, 23, 0, 0, time.Local),
			},
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				schedule, err := cronexpr.Parse(tt.args.spec)
				if err != nil {
					t.Fatal(err)
				}

				times := tt.args.times
				if times <= 0 {
					times = 1
				}

				time_ := startedAt
				got := make([]time.Time, 0, times)
				for i := 0; i < times; i++ {
					time_ = schedule.Next(time_)
					got = append(got, time_)
					t.Logf("%d next: %s", i, time_.Format(time.DateTime))
				}

				if !reflect.DeepEqual(tt.want, got) {
					t.Errorf("\nwant: %v\ngot:  %v", tt.want, got)
				}
			},
		)
	}
}
