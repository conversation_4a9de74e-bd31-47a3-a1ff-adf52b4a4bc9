package internal

import (
	"context"
	"time"

	"github.com/zeromicro/go-zero/core/jsonx"
	"github.com/zeromicro/go-zero/core/logx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/caller"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworkerv2/base"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/redislock"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type UpdateInterfaceDefinitionHandler struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func newUpdateInterfaceDefinitionHandler(svcCtx *svc.ServiceContext) *UpdateInterfaceDefinitionHandler {
	ctx := context.Background()
	return &UpdateInterfaceDefinitionHandler{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (h *UpdateInterfaceDefinitionHandler) Update() error {
	var cancel context.CancelFunc
	h.ctx, cancel = context.WithTimeout(h.ctx, common.ConstExpireOfUpdateInterfaceDefinitionTask)
	defer cancel()

	fn := func() error {
		configs, err := h.svcCtx.InterfaceUpdateConfigModel.FindAll(h.ctx)
		if err != nil {
			return err
		}

		tp := constants.MQTaskTypeManagerHandleUpdateInterfaceDefinition
		for _, config := range configs {
			var depLocalPaths []string
			if config.DepLocalPaths.Valid && config.DepLocalPaths.String != "" {
				_ = jsonx.UnmarshalFromString(config.DepLocalPaths.String, &depLocalPaths)
			}

			payload := protobuf.MarshalJSONIgnoreError(
				&pb.UpdateInterfaceDefinitionTaskInfo{
					ProjectId:     config.ProjectId,
					Type:          config.Type,
					LocalPath:     config.LocalPath,
					DepLocalPaths: depLocalPaths,
				},
			)
			if _, err := h.svcCtx.ManagerProducer.Send(
				h.ctx,
				base.NewTask(tp, payload, base.WithRetentionOptions(12*time.Hour)),
				base.QueuePriorityDefault,
			); err != nil {
				h.Errorf("failed to send task to mq, type: %s, payload: %s, error: %+v", tp, payload, err)
			} else {
				h.Debugf("send task to mq successfully, type: %s, payload: %s", tp, payload)
			}
		}

		return nil
	}

	key := common.ConstLockUpdateInterfaceDefinition
	if err := caller.LockWithOptionDo(
		h.svcCtx.Redis, key, fn, redislock.WithExpire(common.ConstExpireOfUpdateInterfaceDefinitionTask),
	); err != nil {
		if re, ok := errorx.RootError(err); !ok || re.Code() != errorx.AcquireRedisLockFailure {
			h.Errorf("failed to acquire the redis lock, key: %s, error: %+v", key, err)
			return err
		}

		h.Infof("another service is executing the task of update interface definition, key: %s", key)
	} else {
		h.Infof("finished to update interface definition, key: %s", key)
	}

	return nil
}
