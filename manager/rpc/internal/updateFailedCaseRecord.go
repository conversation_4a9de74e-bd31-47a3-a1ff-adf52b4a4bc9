package internal

import (
	"context"

	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/mr"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/caller"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/redislock"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	casecommonservicelogic "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/logic/casecommonservice"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type UpdateFailedCaseRecordHandler struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func newUpdateFailedCaseRecordHandler(svcCtx *svc.ServiceContext) *UpdateFailedCaseRecordHandler {
	ctx := context.Background()
	return &UpdateFailedCaseRecordHandler{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (h *UpdateFailedCaseRecordHandler) Update() error {
	var cancel context.CancelFunc
	h.ctx, cancel = context.WithTimeout(context.Background(), common.ConstExpireOfClearAndUpdateFailedCaseTask)
	defer cancel()

	days := h.svcCtx.Config.FailedCaseCleaner.KeepDays
	key := common.ConstLockClearAndUpdateFailedCase
	fn := func() error {
		if err := h.svcCtx.CaseFailStatModel.DeleteBeforeNDaysRecords(h.ctx, nil, days); err != nil {
			return err
		}

		records, err := h.svcCtx.CaseFailStatModel.FindAll(h.ctx)
		if err != nil {
			return err
		}

		return mr.MapReduceVoid[*model.CaseFailStat, any](
			func(source chan<- *model.CaseFailStat) {
				for _, record := range records {
					source <- record
				}
			},
			func(item *model.CaseFailStat, writer mr.Writer[any], cancel func(error)) {
				if item == nil {
					return
				}

				if _, err := casecommonservicelogic.NewCreateOrModifyFailedCaseLogic(
					h.ctx, h.svcCtx,
				).CreateOrModifyFailedCase(
					&pb.CreateOrModifyFailedCaseReq{
						ProjectId: item.ProjectId,
						CaseType:  item.CaseType,
						CaseId:    item.CaseId,
					},
				); err != nil {
					h.Errorf(
						"failed to create or modify the failed case record, project_id: %s, case_type: %s, case_id: %s, error: %+v",
						item.ProjectId, item.CaseType, item.CaseId, err,
					)
				}
			},
			func(pipe <-chan any, cancel func(error)) {
			},
			mr.WithContext(h.ctx),
		)
	}
	if err := caller.LockWithOptionDo(
		h.svcCtx.Redis, key, fn, redislock.WithExpire(common.ConstExpireOfClearAndUpdateFailedCaseTask),
	); err != nil {
		if re, ok := errorx.RootError(err); !ok || re.Code() != errorx.AcquireRedisLockFailure {
			h.Errorf("failed to acquire the redis lock, key: %s, error: %+v", key, err)
			return err
		}

		h.Infof("another service is executing the task of clear and update failed case records, key: %s", key)
	} else {
		h.Infof("finished to clear and update the failed case records, key: %s, days: %d", key, days)
	}

	return nil
}
