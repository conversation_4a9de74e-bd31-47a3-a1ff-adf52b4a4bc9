package tasks

import (
	"context"
	"runtime/debug"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/trace"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworkerv2/base"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/userinfo"
	qetutils "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	interfacedefinitionservicelogic "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/logic/interfacedefinitionservice"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type HandleUpdateInterfaceDefinitionTaskLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewHandleUpdateInterfaceDefinitionTaskLogic(
	ctx context.Context, svcCtx *svc.ServiceContext,
) *HandleUpdateInterfaceDefinitionTaskLogic {
	return &HandleUpdateInterfaceDefinitionTaskLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *HandleUpdateInterfaceDefinitionTaskLogic) Consume(payload []byte) (err error) {
	if payload == nil {
		return nil
	}
	l.Debugf("the payload of update interface definition task: %s", payload)

	var info pb.UpdateInterfaceDefinitionTaskInfo
	if err = protobuf.UnmarshalJSON(payload, &info); err != nil {
		return errors.Wrapf(
			errorx.Err(errorx.SerializationError, err.Error()),
			"failed to unmarshal the payload of update interface definition, payload: %s, error: %+v",
			payload, err,
		)
	}

	if !qetutils.Exists(info.GetLocalPath()) {
		l.Warnf("no need to update interface definition cause by the local path is not exists, payload: %s", payload)
		return nil
	}

	if err = l.updateGitRepo(&info); err != nil {
		return errors.Wrapf(
			errorx.Err(errorx.FileOperationFailure, err.Error()),
			"failed to update git repository, payload: %s, error: %+v",
			payload, err,
		)
	}

	if err = l.localImport(&info); err != nil {
		return err
	}

	if err = l.updateTags(&info); err != nil {
		return err
	}

	return nil
}

func (l *HandleUpdateInterfaceDefinitionTaskLogic) updateGitRepo(info *pb.UpdateInterfaceDefinitionTaskInfo) error {
	commit, err := qetutils.PullWithContext(l.ctx, info.GetLocalPath())
	if err != nil {
		return err
	}

	l.Infof("pull interface definition successfully, path: %s, commit: %s", info.GetLocalPath(), commit.String())
	return nil
}

func (l *HandleUpdateInterfaceDefinitionTaskLogic) localImport(info *pb.UpdateInterfaceDefinitionTaskInfo) error {
	deps := make([]*pb.LocalImportInterfaceDefinitionReq_Target, 0, len(info.GetDepLocalPaths()))
	for _, p := range info.GetDepLocalPaths() {
		deps = append(
			deps, &pb.LocalImportInterfaceDefinitionReq_Target{
				Path: p,
			},
		)
	}

	in := &pb.LocalImportInterfaceDefinitionReq{
		ProjectId: info.GetProjectId(),
		Type:      info.GetType(),
		Target: &pb.LocalImportInterfaceDefinitionReq_Target{
			Path: info.GetLocalPath(),
		},
		Dependencies: deps,
	}
	out, err := interfacedefinitionservicelogic.NewLocalImportInterfaceDefinitionLogic(l.ctx, l.svcCtx).
		LocalImportInterfaceDefinition(in)
	if err != nil {
		return err
	}

	l.Infof(
		"local import interface definition successfully, in: %s, out: %s",
		protobuf.MarshalJSONIgnoreError(in), protobuf.MarshalJSONIgnoreError(out),
	)
	return nil
}

func (l *HandleUpdateInterfaceDefinitionTaskLogic) updateTags(info *pb.UpdateInterfaceDefinitionTaskInfo) error {
	_, err := interfacedefinitionservicelogic.NewUpdateInterfaceDocumentTagsLogic(
		l.ctx, l.svcCtx,
	).UpdateInterfaceDocumentTags(
		&pb.UpdateInterfaceDocumentTagsReq{
			ProjectId: info.GetProjectId(),
		},
	)
	if err != nil {
		return err
	}

	l.Infof("update interface document tags successfully, project_id: %s", info.GetProjectId())
	return nil
}

type UpdateInterfaceDefinitionTaskProcessor struct {
	svcCtx *svc.ServiceContext
}

func NewUpdateInterfaceDefinitionTaskProcessor(svcCtx *svc.ServiceContext) base.Handler {
	return &UpdateInterfaceDefinitionTaskProcessor{
		svcCtx: svcCtx,
	}
}

func (p *UpdateInterfaceDefinitionTaskProcessor) ProcessTask(ctx context.Context, task *base.Task) (
	result []byte, err error,
) {
	logger := logx.WithContext(ctx)
	logger.Debugf(
		"processor trace_id: %s, span_id: %s, task_name: %s",
		trace.TraceIDFromContext(ctx), trace.SpanIDFromContext(ctx), task.Typename,
	)
	defer func() {
		if r := recover(); r != nil {
			logger.Errorf("processor panic: %+v, stack: %s", r, debug.Stack())
		}
	}()

	ctx, cancel := context.WithTimeout(ctx, common.ConstExpireOfUpdateInterfaceDefinitionTask)
	defer cancel()

	systemUser := userinfo.System()
	ctx = userinfo.WithContext(ctx, &systemUser.TokenUserInfo)

	if err = NewHandleUpdateInterfaceDefinitionTaskLogic(ctx, p.svcCtx).Consume(task.Payload); err != nil {
		logger.Errorf("processor err: %+v", err)
		return []byte(constants.FAILURE), nil
	}

	return []byte(constants.SUCCESS), nil
}
