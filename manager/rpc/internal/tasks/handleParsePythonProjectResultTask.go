package tasks

import (
	"context"
	"database/sql"
	"fmt"
	"runtime/debug"
	"sync"
	"time"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"
	"github.com/zeromicro/go-zero/core/lang"
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/mr"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/trace"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworker"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworkerv2/base"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/redislock"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	commonpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/types"
)

var _ base.Handler = (*ProcessorParsePythonProjectResult)(nil)

type HandleParsePythonProjectResultTaskLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext

	// searchUIPlanLogic *uiplanservicelogic.SearchUiPlanByProjectIdConfigIdLogic
	// checkUIPlanLogic  *uiplanservicelogic.CheckPlanStateLogic

	once sync.Once
	src  map[string]*model.GitProjectTree

	nodes []*model.GitProjectTree
	cache types.Recorders
}

func NewHandleParsePythonProjectResultTaskLogic(
	ctx context.Context, svcCtx *svc.ServiceContext,
) *HandleParsePythonProjectResultTaskLogic {
	return &HandleParsePythonProjectResultTaskLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,

		// searchUIPlanLogic: uiplanservicelogic.NewSearchUiPlanByProjectIdConfigIdLogic(ctx, svcCtx),
		// checkUIPlanLogic:  uiplanservicelogic.NewCheckPlanStateLogic(ctx, svcCtx),

		nodes: make(
			[]*model.GitProjectTree, 0, constants.ConstDefaultMakeSliceSize,
		), // 预置切片容量为`constants.ConstDefaultMakeSliceSize`，减少切片扩容的情况
		cache: make(types.Recorders, constants.ConstDefaultMakeMapSize),
	}
}

func (l *HandleParsePythonProjectResultTaskLogic) Consume(payload []byte) (err error) {
	if payload == nil {
		// 忽略`payload`为`nil`的情况，
		return nil
	}
	l.Debugf("the payload of parse python project result task: %s", payload)

	var info commonpb.ParsePythonProjectTaskResult
	if err = protobuf.UnmarshalJSON(payload, &info); err != nil {
		return errors.Wrapf(
			errorx.Err(errorx.SerializationError, err.Error()),
			"failed to unmarshal the handle-parse-python-project-result task payload, payload: %s, error: %+v",
			payload, err,
		)
	}

	projectID := info.GetProjectId()
	configID := info.GetConfigId()
	triggeredBy := info.GetTriggerAccount()
	triggeredAt := time.UnixMilli(info.GetTriggerTime())
	if _, err = model.CheckGitConfigByConfigID(l.ctx, l.svcCtx.GitConfigModel, projectID, configID); err != nil {
		return err
	}

	root, err := l.svcCtx.GitProjectTreeModel.FindRootNodeOfTree(l.ctx, projectID, configID)
	if err != nil && !errors.Is(err, model.ErrNotFound) {
		return errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()),
			"failed to find git project tree root node with project_id[%s] and config_id[%s], error: %+v",
			projectID, configID, err,
		)
	} else if root != nil && root.TriggeredAt.After(triggeredAt) {
		l.Infof(
			"the trigger time[%s] in payload is earlier than the trigger time[%s] in db, this payload will not be stored",
			triggeredAt.String(), root.TriggeredAt.String(),
		)
		return nil
	}

	// acquire redis lock
	key := fmt.Sprintf(
		"%s:%s:%s", common.ConstLockHandleParsePythonProjectResultTaskProjectIDConfigIDPrefix, projectID, configID,
	)
	lock, err := redislock.NewRedisLockAndAcquire(
		l.svcCtx.Redis, key, redislock.WithExpire(common.ConstLockHandleParsePythonProjectResultExpireTime),
	)
	if err != nil {
		return err
	}
	defer func() {
		// release redis lock
		e := lock.Release()
		if e != nil {
			if re, ok := errorx.RootError(e); ok && re.Code() != errorx.ReleaseRedisLockFailure && err == nil {
				err = e
			}
		}
	}()

	// flatten by root node
	l.flatten(nil, info.GetRootNode())
	if len(l.nodes) == 0 {
		l.Errorf("not found git project node with project_id[%s] and config_id[%s]", projectID, configID)
		return nil
	}

	if err = l.loadCurrentNodes(projectID, configID); err != nil {
		return err
	}

	if err = l.updateGitProjectTree(projectID, configID, triggeredBy, triggeredAt); err != nil {
		return err
	}

	// [2024-04-23] 去掉`UI`计划状态检查
	//if err = l.updateUIPlanState(projectID, configID); err != nil {
	//	return err
	//}

	return nil
}

func (l *HandleParsePythonProjectResultTaskLogic) flatten(parent, node *commonpb.Node) {
	if node == nil {
		return
	}

	path := node.GetPath()
	if _, ok := l.cache[path]; ok {
		l.Errorf("found a duplicated node path[%s]", path)
	} else {
		var parentPath string
		if parent != nil {
			parentPath = parent.GetPath()
		}

		// 由于`protojson`只能序列化`[]proto.Message`，而`tags`字段类型为`[]string`，Go Struct跟PB没有大的区别，因此直接使用`jsonx`暂代`protojson`
		tags := jsonx.MarshalToStringIgnoreError(node.GetTags())

		l.nodes = append(
			l.nodes, &model.GitProjectTree{
				Path: path,
				ParentPath: sql.NullString{
					String: parentPath,
					Valid:  parentPath != "",
				},
				Name: node.GetName(),
				Alias: sql.NullString{
					String: node.GetAlias(),
					Valid:  node.GetAlias() != "",
				},
				Type: string(convertNodeType(node.GetType())),
				Tags: sql.NullString{
					String: tags,
					Valid:  tags != "",
				},
			},
		)

		l.cache[path] = lang.Placeholder
	}

	for _, n := range node.GetChildren() {
		l.flatten(node, n)
	}
}

func (l *HandleParsePythonProjectResultTaskLogic) loadCurrentNodes(projectID, configID string) error {
	var err error

	l.once.Do(
		func() {
			var ns []*model.GitProjectTree

			ns, err = l.svcCtx.GitProjectTreeModel.FindAll(l.ctx, projectID, configID)
			if err != nil && !errors.Is(err, model.ErrNotFound) {
				err = errors.Wrapf(
					errorx.Err(errorx.DBError, err.Error()),
					"failed to find the all git project nodes with project_id[%s] and config_id[%s], error: %+v",
					projectID, configID, err,
				)
				return
			}

			l.src = make(map[string]*model.GitProjectTree, len(ns))

			for _, n := range ns {
				l.src[fmt.Sprintf("%s:%s", n.Path, n.ParentPath.String)] = n
			}
		},
	)

	return err
}

func (l *HandleParsePythonProjectResultTaskLogic) updateGitProjectTree(
	projectID, configID, triggeredBy string, triggeredAt time.Time,
) error {
	return l.svcCtx.GitProjectTreeModel.Trans(
		l.ctx, func(context context.Context, session sqlx.Session) error {
			return mr.MapReduceVoid[*operatorItem, any](
				func(source chan<- *operatorItem) {
					for _, node := range l.nodes {
						k := fmt.Sprintf("%s:%s", node.Path, node.ParentPath.String)
						if v, ok := l.src[k]; ok {
							node.Id = v.Id
							node.CreatedBy = v.CreatedBy
							node.CreatedAt = v.CreatedAt

							delete(l.src, k)

							// update: in `l.src`
							source <- &operatorItem{
								operator: constOperatorTypeUpdate,
								node:     node,
							}
						} else {
							// insert: not in `l.src`
							source <- &operatorItem{
								operator: constOperatorTypeInsert,
								node:     node,
							}
						}
					}

					for _, v := range l.src {
						// delete: remainder of `l.src`
						source <- &operatorItem{
							operator: constOperatorTypeDelete,
							node:     v,
						}
					}
				}, func(item *operatorItem, writer mr.Writer[any], cancel func(error)) {
					var err error
					defer func() {
						if err != nil {
							cancel(err)
						}
					}()

					item.node.ProjectId = projectID
					item.node.GitConfigId = configID
					item.node.TriggeredBy = triggeredBy
					item.node.TriggeredAt = triggeredAt
					if item.node.CreatedBy == "" {
						item.node.CreatedBy = triggeredBy
					}
					if item.node.UpdatedBy == "" {
						item.node.UpdatedBy = triggeredBy
					}

					switch item.operator {
					case constOperatorTypeInsert:
						_, err = l.svcCtx.GitProjectTreeModel.InsertTX(context, session, item.node)
					case constOperatorTypeUpdate:
						_, err = l.svcCtx.GitProjectTreeModel.UpdateTX(context, session, item.node)
					case constOperatorTypeDelete:
						err = l.svcCtx.GitProjectTreeModel.LogicDelete(context, session, item.node.Id)
					}
				}, func(pipe <-chan any, cancel func(error)) {
				},
			)
		},
	)
}

//func (l *HandleParsePythonProjectResultTaskLogic) updateUIPlanState(projectID, configID string) error {
//	// 获取需要同步状态的所有计划
//	uiPlans, err := l.searchUIPlanLogic.SearchUiPlanByProjectIdConfigId(
//		&pb.SearchUiPlanByProjectIdConfigIdReq{
//			ProjectId:   projectID,
//			GitConfigId: configID,
//		},
//	)
//	if err != nil {
//		return err
//	}
//
//	// 同步计划状态
//	for _, uiPlan := range uiPlans.Items {
//		_, err = l.checkUIPlanLogic.CheckPlanState(
//			&pb.CheckPlanStateReq{
//				ProjectId: uiPlan.ProjectId,
//				PlanId:    uiPlan.PlanId,
//			},
//		)
//		if err != nil {
//			return err
//		}
//	}
//
//	return nil
//}

func convertNodeType(typ commonpb.NodeType) common.NodeType {
	switch typ {
	case commonpb.NodeType_NT_DIRECTORY:
		return common.ConstNodeTypeDirectory
	case commonpb.NodeType_NT_FILE:
		return common.ConstNodeTypeFile
	case commonpb.NodeType_NT_PACKAGE:
		return common.ConstNodeTypePackage
	case commonpb.NodeType_NT_MODULE:
		return common.ConstNodeTypeModule
	case commonpb.NodeType_NT_CLASS:
		return common.ConstNodeTypeClass
	case commonpb.NodeType_NT_FUNCTION:
		return common.ConstNodeTypeFunction
	default:
		return ""
	}
}

// HandleParsePythonProjectResultTask
// Deprecated: use new api.
func HandleParsePythonProjectResultTask(svcCtx *svc.ServiceContext) mqworker.BytesPayloadTaskWithContext {
	return func(ctx context.Context, payload []byte) (err error) {
		return NewHandleParsePythonProjectResultTaskLogic(ctx, svcCtx).Consume(payload)
	}
}

// HandleParsePythonProjectResultTaskMiddle HandleParsePythonProjectResultTaskMiddle
func HandleParsePythonProjectResultTaskMiddle(svcCtx *svc.ServiceContext) mqworker.BytesPayloadTaskWithContext {
	return func(ctx context.Context, payload []byte) (err error) {
		newTask := base.NewTask(
			constants.MQTaskTypeManagerHandleParsePythonProjectResult,
			payload,
			base.WithMaxRetryOptions(100),
			base.WithRetentionOptions(time.Second*300),
		)
		_, err = svcCtx.WorkerConsumerV1Producer.Send(ctx, newTask, base.QueuePriorityDefault)
		if err != nil {
			return err
		}
		return nil
	}
}

type ProcessorParsePythonProjectResult struct {
	svcCtx *svc.ServiceContext
}

func NewProcessorParsePythonProjectResult(svcCtx *svc.ServiceContext) base.Handler {
	return &ProcessorParsePythonProjectResult{
		svcCtx: svcCtx,
	}
}

func (p *ProcessorParsePythonProjectResult) ProcessTask(ctx context.Context, task *base.Task) (
	result []byte, err error,
) {
	logger := logx.WithContext(ctx)
	logger.Debugf(
		"processor TraceID: %s, SpanID: %s, task name: %s",
		trace.TraceIDFromContext(ctx), trace.SpanIDFromContext(ctx), task.Typename,
	)
	defer func() {
		if r := recover(); r != nil {
			logger.Errorf("processor panic: %+v, stack: %s", r, debug.Stack())
		}
	}()

	if err = NewHandleParsePythonProjectResultTaskLogic(ctx, p.svcCtx).Consume(task.Payload); err != nil {
		logger.Errorf("processor err: %+v", err)
		return nil, err
	}

	return []byte(constants.SUCCESS), nil
}
