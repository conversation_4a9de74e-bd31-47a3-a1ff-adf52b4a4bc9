package tasks

import (
	"context"
	"runtime/debug"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/trace"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworkerv2/base"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	interfacedefinitionservicelogic "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/logic/interfacedefinitionservice"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type UpdateInterfaceCoverageTaskProcessor struct {
	svcCtx *svc.ServiceContext
}

func NewUpdateInterfaceCoverageTaskProcessor(svcCtx *svc.ServiceContext) base.Handler {
	return &UpdateInterfaceCoverageTaskProcessor{
		svcCtx: svcCtx,
	}
}

func (p *UpdateInterfaceCoverageTaskProcessor) ProcessTask(ctx context.Context, task *base.Task) (
	result []byte, err error,
) {
	logger := logx.WithContext(ctx)
	logger.Debugf(
		"processor trace_id: %s, span_id: %s, task_name: %s",
		trace.TraceIDFromContext(ctx), trace.SpanIDFromContext(ctx), task.Typename,
	)
	defer func() {
		if r := recover(); r != nil {
			logger.Errorf("processor panic: %+v, stack: %s", r, debug.Stack())
		}
	}()

	ctx, cancel := context.WithTimeout(ctx, common.ConstExpireOfUpdateInterfaceCaseCoverageTask)
	defer cancel()

	logger.Infof("the payload of update interface coverage task: %s", task.Payload)
	if err = p.Consume(ctx, task.Payload); err != nil {
		logger.Errorf(
			"failed to consume the update interface coverage task, payload: %s, error: %+v", task.Payload, err,
		)
		return []byte(constants.FAILURE), nil
	}

	return []byte(constants.SUCCESS), nil
}

func (p *UpdateInterfaceCoverageTaskProcessor) Consume(ctx context.Context, payload []byte) error {
	if payload == nil {
		return nil
	}

	var info pb.UpdateInterfaceCoverageTaskInfo
	if err := protobuf.UnmarshalJSON(payload, &info); err != nil {
		return errors.Wrapf(
			errorx.Err(errorx.SerializationError, err.Error()),
			"failed to unmarshal the payload of update interface coverage, payload: %s, error: %+v",
			payload, err,
		)
	}

	_, err := interfacedefinitionservicelogic.NewUpdateInterfaceCoverageDataLogic(ctx, p.svcCtx).
		UpdateInterfaceCoverageData(
			&pb.UpdateInterfaceCoverageDataReq{
				ProjectId: info.GetProjectId(),
				KeepDays:  info.GetKeepDays(),
			},
		)
	return err
}
