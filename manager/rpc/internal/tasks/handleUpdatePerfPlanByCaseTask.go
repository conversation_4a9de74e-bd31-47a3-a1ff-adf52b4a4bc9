package tasks

import (
	"context"
	"runtime/debug"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/trace"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworkerv2/base"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	perfplanv2servicelogic "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/logic/perfplanv2service"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type HandleUpdatePerfPlanByCaseTaskLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewHandleUpdatePerfPlanByCaseTaskLogic(
	ctx context.Context, svcCtx *svc.ServiceContext,
) *HandleUpdatePerfPlanByCaseTaskLogic {
	return &HandleUpdatePerfPlanByCaseTaskLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *HandleUpdatePerfPlanByCaseTaskLogic) Consume(payload []byte) (err error) {
	if payload == nil {
		return nil
	}
	l.Debugf("the payload of update perf plan case task: %s", payload)

	var info pb.UpdatePerfPlanByCaseTaskInfo
	if err = protobuf.UnmarshalJSON(payload, &info); err != nil {
		return errors.Wrapf(
			errorx.Err(errorx.SerializationError, err.Error()),
			"failed to unmarshal the payload of update perf plan by case, payload: %s, error: %+v",
			payload, err,
		)
	}

	_, err = perfplanv2servicelogic.NewUpdatePerfPlanByCaseLogic(l.ctx, l.svcCtx).UpdatePerfPlanByCase(
		&pb.UpdatePerfPlanByCaseReq{
			ProjectId: info.GetProjectId(),
			PlanId:    info.GetPlanId(),
			CaseId:    info.GetCaseId(),
		},
	)
	return err
}

type UpdatePerfPlanByCaseTaskProcessor struct {
	svcCtx *svc.ServiceContext
}

func NewUpdatePerfPlanByCaseTaskProcessor(svcCtx *svc.ServiceContext) base.Handler {
	return &UpdatePerfPlanByCaseTaskProcessor{
		svcCtx: svcCtx,
	}
}

func (p *UpdatePerfPlanByCaseTaskProcessor) ProcessTask(ctx context.Context, task *base.Task) (result []byte, err error) {
	logger := logx.WithContext(ctx)
	logger.Debugf(
		"processor trace_id: %s, span_id: %s, task_name: %s",
		trace.TraceIDFromContext(ctx), trace.SpanIDFromContext(ctx), task.Typename,
	)
	defer func() {
		if r := recover(); r != nil {
			logger.Errorf("processor panic: %+v, stack: %s", r, debug.Stack())
		}
	}()

	if err = NewHandleUpdatePerfPlanByCaseTaskLogic(ctx, p.svcCtx).Consume(task.Payload); err != nil {
		logger.Errorf("processor err: %+v", err)
		return nil, err
	}

	return []byte(constants.SUCCESS), nil
}
