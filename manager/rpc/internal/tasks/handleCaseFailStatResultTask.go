package tasks

import (
	"context"
	"fmt"
	"runtime/debug"
	"time"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/trace"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworkerv2/base"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/redislock"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	casecommonservicelogic "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/logic/casecommonservice"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

var _ base.Handler = (*CaseFailStatResultTaskProcessor)(nil)

type HandleCaseFailStatResultTaskLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewHandleCaseFailStatResultTaskLogic(
	ctx context.Context, svcCtx *svc.ServiceContext,
) *HandleCaseFailStatResultTaskLogic {
	return &HandleCaseFailStatResultTaskLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

// Deprecated: use Consume instead.
func (l *HandleCaseFailStatResultTaskLogic) ConsumeV1(payload []byte) (err error) {
	if payload == nil {
		return nil
	}
	l.Debugf("the handle-case-fail-stat-result task payload: %s", payload)

	var info pb.CaseFailStatForMq
	if err = protobuf.UnmarshalJSON(payload, &info); err != nil {
		return errors.Wrapf(
			errorx.Err(errorx.SerializationError, err.Error()),
			"failed to unmarshal the payload of case fail stat result, payload: %s, error: %+v",
			payload, err,
		)
	}

	countNum := 0

replay:
	caseTmp, err := l.svcCtx.CaseFailStatModel.FindNoCacheByProjectIdCaseIdCaseType(
		l.ctx, info.GetProjectId(), info.GetCaseId(), info.GetCaseType(),
	)
	if err != nil {
		l.Warnf("CaseFailStatResultTaskProcessor error: %s", err)
		return err
	}
	if caseTmp != nil {
		result, err := l.svcCtx.CaseFailStatModel.UpdateByProjectIdCaseIdCaseTypeForFailCountIncr(
			l.ctx, nil, info.GetProjectId(), info.GetCaseId(), info.GetCaseType(),
		)
		if err != nil {
			l.Warnf("CaseFailStatResultTaskProcessor error: %s", err)
			return err
		}
		affected, err := result.RowsAffected()
		if err != nil {
			l.Warnf("CaseFailStatResultTaskProcessor error: %s", err)
			return err
		}
		if affected == 0 {
			return errorx.Err(errorx.DBError, "并发更新下竞争失败")
		}
	} else {
		lock, err := redislock.NewRedisLockAndAcquire(
			l.svcCtx.Redis, fmt.Sprintf(
				"manager:CaseFailStatResultTaskProcessor:%s:%s:%s", info.GetProjectId(), info.GetCaseId(),
				info.GetCaseType(),
			),
			redislock.WithExpire(5*time.Second),
		)
		if err != nil {
			l.Warnf("task CaseFailStatResultTaskProcessor end, NewRedisLockAndAcquire error: %s", err)
			return err
		}
		defer func(lock *redislock.RedisLock) {
			err := lock.Release()
			if err != nil {
				l.Warnf("task CaseFailStatResultTaskProcessor end, lock.Release() error: %s", err)
			}
		}(lock)
		// doubel check
		caseTmp, err := l.svcCtx.CaseFailStatModel.FindNoCacheByProjectIdCaseIdCaseType(
			l.ctx, info.GetProjectId(), info.GetCaseId(), info.GetCaseType(),
		)
		if err != nil {
			l.Warnf("CaseFailStatResultTaskProcessor error: %s", err)
			return err
		}
		if caseTmp != nil {
			countNum++
			if countNum > 3 {
				return errorx.Errorf(
					errorx.AlreadyExists, "replay more than max[3], caseId[%s]", info.GetCaseId(),
				)
			}
			goto replay
		}

		// 查询分支id
		switch info.CaseType {
		case constants.CaseTypeInterfaceCase:
			cacheForProjectIdCaseId, err := l.svcCtx.InterfaceCaseModel.FindLatestOneNoCacheForProjectIdCaseId(
				l.ctx, info.GetProjectId(), info.GetCaseId(),
			)
			if err != nil {
				l.Warnf("CaseFailStatResultTaskProcessor error: %s", err)
				return err
			}
			if cacheForProjectIdCaseId != nil {
				info.BranchId = cacheForProjectIdCaseId.DocumentId
			}
		case constants.CaseTypeApiCase:
			cacheForProjectIdCaseId, err := l.svcCtx.ApiCaseModel.FindLatestOneNoCache(
				l.ctx, info.GetProjectId(), info.GetCaseId(),
			)
			if err != nil {
				l.Warnf("CaseFailStatResultTaskProcessor error: %s", err)
				return err
			}
			if cacheForProjectIdCaseId != nil {
				info.BranchId = cacheForProjectIdCaseId.CategoryId
			}
		}

		// 保存数据
		err = l.svcCtx.CaseFailStatModel.Trans(
			l.ctx, func(context context.Context, session sqlx.Session) error {
				_, err := l.svcCtx.CaseFailStatModel.InsertTX(
					context, session, &model.CaseFailStat{
						ProjectId: info.GetProjectId(),
						CaseId:    info.GetCaseId(),
						BranchId:  info.GetBranchId(),
						CaseType:  info.GetCaseType(),
						FailCount: 1,
						Version:   1,
						CreatedBy: "sys",
						UpdatedBy: "sys",
					},
				)
				if err != nil {
					l.Warnf("CaseFailStatResultTaskProcessor db error: %s", err)
					return err
				}
				return nil
			},
		)
		if err != nil {
			l.Warnf("CaseFailStatResultTaskProcessor error: %s", err)
			return err
		}
	}

	return nil
}

func (l *HandleCaseFailStatResultTaskLogic) Consume(payload []byte) (err error) {
	if payload == nil {
		return nil
	}
	l.Debugf("the payload of case fail stat result task: %s", payload)

	var info pb.CaseFailStatForMq
	if err = protobuf.UnmarshalJSON(payload, &info); err != nil {
		return errors.Wrapf(
			errorx.Err(errorx.SerializationError, err.Error()),
			"failed to unmarshal the payload of case fail stat result, payload: %s, error: %+v",
			payload, err,
		)
	}

	_, err = casecommonservicelogic.NewCreateOrModifyFailedCaseLogic(l.ctx, l.svcCtx).CreateOrModifyFailedCase(
		&pb.CreateOrModifyFailedCaseReq{
			ProjectId: info.GetProjectId(),
			CaseType:  info.GetCaseType(),
			CaseId:    info.GetCaseId(),
		},
	)
	return err
}

type CaseFailStatResultTaskProcessor struct {
	svcCtx *svc.ServiceContext
}

func NewCaseFailStatResultTaskProcessor(svcCtx *svc.ServiceContext) base.Handler {
	return &CaseFailStatResultTaskProcessor{
		svcCtx: svcCtx,
	}
}

func (p *CaseFailStatResultTaskProcessor) ProcessTask(ctx context.Context, task *base.Task) (
	result []byte, err error,
) {
	logger := logx.WithContext(ctx)
	logger.Debugf(
		"processor trace_id: %s, span_id: %s, task_name: %s",
		trace.TraceIDFromContext(ctx), trace.SpanIDFromContext(ctx), task.Typename,
	)
	defer func() {
		if r := recover(); r != nil {
			logger.Errorf("processor panic: %+v, stack: %s", r, debug.Stack())
		}
	}()

	if err = NewHandleCaseFailStatResultTaskLogic(ctx, p.svcCtx).Consume(task.Payload); err != nil {
		logger.Errorf("processor err: %+v", err)
		return nil, err
	}

	return []byte(constants.SUCCESS), nil
}
