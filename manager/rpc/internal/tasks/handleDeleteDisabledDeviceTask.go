package tasks

import (
	"context"
	"runtime/debug"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/trace"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworkerv2/base"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	projectdeviceservicelogic "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/logic/projectdeviceservice"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type DeleteDisabledDeviceTaskProcessor struct {
	svcCtx *svc.ServiceContext
}

func NewDeleteDisabledDeviceTaskProcessor(svcCtx *svc.ServiceContext) base.Handler {
	return &DeleteDisabledDeviceTaskProcessor{
		svcCtx: svcCtx,
	}
}

func (p *DeleteDisabledDeviceTaskProcessor) ProcessTask(ctx context.Context, task *base.Task) (result []byte, err error) {
	logger := logx.WithContext(ctx)
	logger.Debugf(
		"processor trace_id: %s, span_id: %s, task_name: %s",
		trace.TraceIDFromContext(ctx), trace.SpanIDFromContext(ctx), task.Typename,
	)
	defer func() {
		if r := recover(); r != nil {
			logger.Errorf("processor panic: %+v, stack: %s", r, debug.Stack())
		}
	}()

	ctx, cancel := context.WithTimeout(ctx, common.ConstExpireOfDeleteDisabledDeviceTask)
	defer cancel()

	logger.Infof("the payload of delete disabled device task: %s", task.Payload)
	if err = p.Consume(ctx, task.Payload); err != nil {
		logger.Errorf(
			"failed to consume the delete disabled device task, payload: %s, error: %+v", task.Payload, err,
		)
		return []byte(constants.FAILURE), nil
	}

	return []byte(constants.SUCCESS), nil
}

func (p *DeleteDisabledDeviceTaskProcessor) Consume(ctx context.Context, payload []byte) error {
	if payload == nil {
		return nil
	}

	var info pb.DeleteDisabledDeviceTaskInfo
	if err := protobuf.UnmarshalJSON(payload, &info); err != nil {
		return errors.Wrapf(
			errorx.Err(errorx.SerializationError, err.Error()),
			"failed to unmarshal the payload of delete disabled device, payload: %s, error: %+v",
			payload, err,
		)
	}

	_, err := projectdeviceservicelogic.NewDeleteDisabledProjectDeviceLogic(ctx, p.svcCtx).
		DeleteDisabledProjectDevice(
			&pb.DeleteDisabledProjectDeviceReq{
				ProjectId: info.GetProjectId(),
			},
		)
	return err
}
