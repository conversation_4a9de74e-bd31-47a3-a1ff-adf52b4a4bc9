package tasks

import (
	"context"
	"runtime/debug"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/trace"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworkerv2/base"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	commonpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	larkchatservicelogic "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/logic/larkchatservice"
	perflarkchatservicelogic "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/logic/perflarkchatservice"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type ProcessorOfLarkChatBotDeletedTask struct {
	svcCtx *svc.ServiceContext
}

func NewProcessorOfLarkChatBotDeletedTask(svcCtx *svc.ServiceContext) base.Handler {
	return &ProcessorOfLarkChatBotDeletedTask{
		svcCtx: svcCtx,
	}
}

func (p *ProcessorOfLarkChatBotDeletedTask) ProcessTask(ctx context.Context, task *base.Task) ([]byte, error) {
	logger := logx.WithContext(ctx)
	logger.Debugf(
		"processor trace_id: %s, span_id: %s, task_name: %s",
		trace.TraceIDFromContext(ctx), trace.SpanIDFromContext(ctx), task.Typename,
	)
	defer func() {
		if r := recover(); r != nil {
			logger.Errorf("processor panic: %+v, stack: %s", r, debug.Stack())
		}
	}()

	ctx, cancel := context.WithTimeout(ctx, common.ConstExpireOfLarkChatBotDeletedTask)
	defer cancel()

	logger.Infof("the payload of lark chat bot deleted task: %s", task.Payload)
	if err := p.Consume(ctx, task.Payload); err != nil {
		logger.Errorf("failed to consume the lark chat bot deleted task, payload: %s, error: %+v", task.Payload, err)
		return []byte(constants.FAILURE), err
	}

	return []byte(constants.SUCCESS), nil
}

func (p *ProcessorOfLarkChatBotDeletedTask) Consume(ctx context.Context, payload []byte) error {
	if payload == nil {
		return nil
	}

	var info commonpb.LarkChatBotDeletedTaskInfo
	if err := protobuf.UnmarshalJSON(payload, &info); err != nil {
		return errors.Wrapf(
			errorx.Err(errorx.SerializationError, err.Error()),
			"failed to unmarshal the payload of lark chat bot deleted task, payload: %s, error: %+v",
			payload, err,
		)
	}

	_, err := perflarkchatservicelogic.NewDeletePerfLarkChatLogic(ctx, p.svcCtx).DeletePerfLarkChat(
		&pb.DeletePerfLarkChatReq{
			ChatId: info.GetChatId(),
		},
	)
	if err != nil {
		return err
	}

	_, err = larkchatservicelogic.NewDeleteLarkChatLogic(ctx, p.svcCtx).DeleteLarkChat(
		&pb.DeleteLarkChatReq{
			ChatId: info.GetChatId(),
		},
	)
	return err
}
