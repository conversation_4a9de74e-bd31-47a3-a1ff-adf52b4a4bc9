package svc

import (
	"time"

	"github.com/zeromicro/go-zero/core/stores/redis"
	"github.com/zeromicro/go-zero/core/stores/sqlx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/cronscheduler"
	consumerv1 "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworker/consumer"
	producerv1 "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworker/producer"
	consumerv2 "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworkerv2/consumer"
	producerv2 "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworkerv2/producer"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/thirdparty/appInsight"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/thirdparty/cmdb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common/zrpc/beat"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common/zrpc/devicehub"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common/zrpc/larkproxy"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common/zrpc/notifier"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common/zrpc/permission/domainservice"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common/zrpc/permission/functionservice"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common/zrpc/permission/policyservice"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common/zrpc/permission/roleservice"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common/zrpc/relation"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common/zrpc/reporter"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common/zrpc/user/userservice"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/config"
)

type ServiceContext struct {
	Config config.Config

	DB        sqlx.SqlConn
	Redis     *redis.Redis
	Scheduler *cronscheduler.Scheduler

	ProjectModel                         model.ProjectModel
	CategoryModel                        model.CategoryModel
	CategoryTreeModel                    model.CategoryTreeModel
	TagModel                             model.TagModel
	TagReferenceModel                    model.TagReferenceRelationshipModel
	FunctionModel                        model.FunctionModel
	FunctionReferenceModel               model.FunctionReferenceRelationshipModel
	GeneralConfigModel                   model.GeneralConfigurationModel
	AccountConfigModel                   model.AccountConfigurationModel
	AccountConfigReferenceModel          model.AccountConfigurationReferenceRelationshipModel
	InterfaceDocumentModel               model.InterfaceDocumentModel
	InterfaceSchemaModel                 model.InterfaceSchemaModel
	InterfaceSchemaReferenceModel        model.InterfaceSchemaReferenceRelationshipModel
	InterfaceConfigurationModel          model.InterfaceConfigurationModel
	InterfaceCaseModel                   model.InterfaceCaseModel
	InterfaceCaseElementModel            model.InterfaceCaseElementModel
	InterfaceUpdateConfigModel           model.InterfaceUpdateConfigurationModel
	InterfaceCoverageModel               model.InterfaceCoverageModel
	InterfaceMetricsReferenceModel       model.InterfaceMetricsReferenceModel
	ComponentModel                       model.ComponentModel
	ComponentGroupModel                  model.ComponentGroupModel
	ComponentGroupElementModel           model.ComponentGroupElementModel
	ApiCaseModel                         model.ApiCaseModel
	ApiCaseElementModel                  model.ApiCaseElementModel
	ComponentGroupReferenceModel         model.ComponentGroupReferenceRelationshipModel
	ApiSuiteModel                        model.ApiSuiteModel
	ApiSuiteReferenceModel               model.ApiSuiteReferenceRelationshipModel
	ApiCaseReferenceModel                model.ApiCaseReferenceRelationshipModel
	ApiPlanModel                         model.ApiPlanModel
	ApiPlanReferenceModel                model.ApiPlanReferenceRelationshipModel
	AdvancedSearchConditionModel         model.AdvancedSearchConditionModel
	AdvancedSearchFieldModel             model.AdvancedSearchFieldModel
	AdvancedSearchFieldRelationshipModel model.AdvancedSearchFieldRelationshipModel
	NotifyModel                          model.NotifyModel
	GitConfigModel                       model.GitConfigurationModel
	GitProjectTreeModel                  model.GitProjectTreeModel
	UiPlanModel                          model.UiPlanModel
	UiPlanReferenceModel                 model.UiPlanReferenceRelationshipModel
	PlanUserLikeRelationshipModel        model.PlanUserLikeRelationshipModel
	ReviewRecordModel                    model.ReviewRecordModel
	CaseFailStatModel                    model.CaseFailStatModel
	AICaseModel                          model.AICaseModel
	ProtobufConfigModel                  model.ProtobufConfigurationModel
	ProtobufDependenceModel              model.ProtobufDependenceModel
	PerfDataModel                        model.PerfDataModel
	PerfCaseModel                        model.PerfCaseModel                      // Deprecated
	PerfCaseStepModel                    model.PerfCaseStepModel                  // Deprecated
	PerfPlanModel                        model.PerfPlanModel                      // Deprecated
	PerfPlanReferenceModel               model.PerfPlanReferenceRelationshipModel // Deprecated
	PerfCaseV2Model                      model.PerfCaseV2Model
	PerfCaseStepV2Model                  model.PerfCaseStepV2Model
	PerfPlanV2Model                      model.PerfPlanV2Model
	PerfPlanCaseModel                    model.PerfPlanCaseRelationshipModel
	PerfStopRuleModel                    model.PerfStopRuleModel
	PerfPlanRuleModel                    model.PerfPlanRuleRelationshipModel
	PerfLarkMemberModel                  model.PerfLarkMemberModel
	ProtobufConfigReferenceModel         model.ProtobufConfigurationReferenceRelationshipModel
	PerfLarkChatModel                    model.PerfLarkChatModel
	LarkChatModel                        model.LarkChatModel
	ProjectDeviceModel                   model.ProjectDeviceModel
	ProjectDeviceReferenceModel          model.ProjectDeviceReferenceRelationshipModel
	StabilityPlanModel                   model.StabilityPlanModel
	SlaThresholdModel                    model.SlaThresholdModel
	SlaReportNotifierModel               model.SlaReportNotifierModel
	PromptConfigModel                    model.PromptConfigurationModel
	PromptConfigReferenceModel           model.PromptConfigurationReferenceRelationshipModel
	ApplicationConfigModel               model.ApplicationConfigurationModel
	ApplicationConfigReferenceModel      model.ApplicationConfigurationReferenceRelationshipModel
	UIAgentComponentModel                model.UiAgentComponentModel
	UIAgentImageModel                    model.UiAgentImageModel
	UIAgentImageReferenceModel           model.UiAgentImageReferenceRelationshipModel

	BeatRPC               *beat.RPCClient
	DeviceHubRPC          *devicehub.RPCClient
	LarkProxyRPC          *larkproxy.RPCClient
	NotifierRPC           *notifier.RPCClient
	PermissionDomainRPC   *domainservice.PermissionDomainRPC
	PermissionFunctionRPC *functionservice.PermissionFunctionRPC
	PermissionPolicyRPC   *policyservice.PermissionPolicyRPC
	PermissionRoleRPC     *roleservice.PermissionRoleRPC
	RelationRPC           *relation.RPCClient
	ReporterRPC           *reporter.RPCClient
	UserRPC               *userservice.UserRPC

	WorkerConsumerV1         *consumerv1.Consumer // WorkerConsumerV1[消费] ==> WorkerConsumerV1Producer「生产」==> WorkerConsumerV2[消费]
	WorkerConsumerV2         *consumerv2.Consumer
	WorkerConsumerV1Producer *producerv2.Producer
	WorkerProducerV1         *producerv1.Producer
	ManagerConsumer          *consumerv2.Consumer
	ManagerProducer          *producerv2.Producer
	LarkProxyConsumer        *consumerv2.Consumer
	DispatcherProducer       *producerv2.Producer

	CMDBClient       *cmdb.Client
	AppInsightClient *appInsight.Client
}

func NewServiceContext(c config.Config) *ServiceContext {
	sqlConn := sqlx.NewMysql(c.DB.DataSource)
	scheduler := cronscheduler.NewScheduler()
	scheduler.Scheduler.ChangeLocation(time.Local) // change to local timezone

	return &ServiceContext{
		Config: c,

		// Middleware
		DB:        sqlConn,
		Redis:     redis.MustNewRedis(c.Redis.RedisConf, redis.WithDB(c.Redis.RedisConf.DB)),
		Scheduler: scheduler,

		// Model
		ProjectModel:                         model.NewProjectModel(sqlConn, c.Cache),
		CategoryModel:                        model.NewCategoryModel(sqlConn, c.Cache),
		CategoryTreeModel:                    model.NewCategoryTreeModel(sqlConn, c.Cache),
		TagModel:                             model.NewTagModel(sqlConn, c.Cache),
		TagReferenceModel:                    model.NewTagReferenceRelationshipModel(sqlConn, c.Cache),
		FunctionModel:                        model.NewFunctionModel(sqlConn, c.Cache),
		FunctionReferenceModel:               model.NewFunctionReferenceRelationshipModel(sqlConn, c.Cache),
		GeneralConfigModel:                   model.NewGeneralConfigurationModel(sqlConn, c.Cache),
		AccountConfigModel:                   model.NewAccountConfigurationModel(sqlConn, c.Cache),
		AccountConfigReferenceModel:          model.NewAccountConfigurationReferenceRelationshipModel(sqlConn, c.Cache),
		InterfaceDocumentModel:               model.NewInterfaceDocumentModel(sqlConn, c.Cache),
		InterfaceSchemaModel:                 model.NewInterfaceSchemaModel(sqlConn, c.Cache),
		InterfaceSchemaReferenceModel:        model.NewInterfaceSchemaReferenceRelationshipModel(sqlConn, c.Cache),
		InterfaceConfigurationModel:          model.NewInterfaceConfigurationModel(sqlConn, c.Cache),
		InterfaceCaseModel:                   model.NewInterfaceCaseModel(sqlConn, c.Cache),
		InterfaceCaseElementModel:            model.NewInterfaceCaseElementModel(sqlConn, c.Cache),
		InterfaceUpdateConfigModel:           model.NewInterfaceUpdateConfigurationModel(sqlConn, c.Cache),
		InterfaceCoverageModel:               model.NewInterfaceCoverageModel(sqlConn, c.Cache),
		InterfaceMetricsReferenceModel:       model.NewInterfaceMetricsReferenceModel(sqlConn, c.Cache),
		ComponentModel:                       model.NewComponentModel(sqlConn, c.Cache),
		ComponentGroupModel:                  model.NewComponentGroupModel(sqlConn, c.Cache),
		ComponentGroupElementModel:           model.NewComponentGroupElementModel(sqlConn, c.Cache),
		ApiCaseModel:                         model.NewApiCaseModel(sqlConn, c.Cache),
		ApiCaseElementModel:                  model.NewApiCaseElementModel(sqlConn, c.Cache),
		ComponentGroupReferenceModel:         model.NewComponentGroupReferenceRelationshipModel(sqlConn, c.Cache),
		ApiSuiteModel:                        model.NewApiSuiteModel(sqlConn, c.Cache),
		ApiSuiteReferenceModel:               model.NewApiSuiteReferenceRelationshipModel(sqlConn, c.Cache),
		ApiCaseReferenceModel:                model.NewApiCaseReferenceRelationshipModel(sqlConn, c.Cache),
		ApiPlanModel:                         model.NewApiPlanModel(sqlConn, c.Cache),
		ApiPlanReferenceModel:                model.NewApiPlanReferenceRelationshipModel(sqlConn, c.Cache),
		AdvancedSearchConditionModel:         model.NewAdvancedSearchConditionModel(sqlConn, c.Cache),
		AdvancedSearchFieldModel:             model.NewAdvancedSearchFieldModel(sqlConn, c.Cache),
		AdvancedSearchFieldRelationshipModel: model.NewAdvancedSearchFieldRelationshipModel(sqlConn, c.Cache),
		NotifyModel:                          model.NewNotifyModel(sqlConn, c.Cache),
		GitConfigModel:                       model.NewGitConfigurationModel(sqlConn, c.Cache),
		GitProjectTreeModel:                  model.NewGitProjectTreeModel(sqlConn, c.Cache),
		UiPlanModel:                          model.NewUiPlanModel(sqlConn, c.Cache),
		UiPlanReferenceModel:                 model.NewUiPlanReferenceRelationshipModel(sqlConn, c.Cache),
		PlanUserLikeRelationshipModel:        model.NewPlanUserLikeRelationshipModel(sqlConn, c.Cache),
		ReviewRecordModel:                    model.NewReviewRecordModel(sqlConn, c.Cache),
		CaseFailStatModel:                    model.NewCaseFailStatModel(sqlConn, c.Cache),
		AICaseModel:                          model.NewAICaseModel(sqlConn, c.Cache),
		ProtobufConfigModel:                  model.NewProtobufConfigurationModel(sqlConn, c.Cache),
		ProtobufDependenceModel:              model.NewProtobufDependenceModel(sqlConn, c.Cache),
		PerfDataModel:                        model.NewPerfDataModel(sqlConn, c.Cache),
		PerfCaseModel:                        model.NewPerfCaseModel(sqlConn, c.Cache),
		PerfCaseStepModel:                    model.NewPerfCaseStepModel(sqlConn, c.Cache),
		PerfPlanModel:                        model.NewPerfPlanModel(sqlConn, c.Cache),
		PerfPlanReferenceModel:               model.NewPerfPlanReferenceRelationshipModel(sqlConn, c.Cache),
		PerfCaseV2Model:                      model.NewPerfCaseV2Model(sqlConn, c.Cache),
		PerfCaseStepV2Model:                  model.NewPerfCaseStepV2Model(sqlConn, c.Cache),
		PerfPlanV2Model:                      model.NewPerfPlanV2Model(sqlConn, c.Cache),
		PerfPlanCaseModel:                    model.NewPerfPlanCaseRelationshipModel(sqlConn, c.Cache),
		PerfStopRuleModel:                    model.NewPerfStopRuleModel(sqlConn, c.Cache),
		PerfPlanRuleModel:                    model.NewPerfPlanRuleRelationshipModel(sqlConn, c.Cache),
		PerfLarkMemberModel:                  model.NewPerfLarkMemberModel(sqlConn, c.Cache),
		ProtobufConfigReferenceModel: model.NewProtobufConfigurationReferenceRelationshipModel(
			sqlConn, c.Cache,
		),
		PerfLarkChatModel:           model.NewPerfLarkChatModel(sqlConn, c.Cache),
		LarkChatModel:               model.NewLarkChatModel(sqlConn, c.Cache),
		ProjectDeviceModel:          model.NewProjectDeviceModel(sqlConn, c.Cache),
		ProjectDeviceReferenceModel: model.NewProjectDeviceReferenceRelationshipModel(sqlConn, c.Cache),
		StabilityPlanModel:          model.NewStabilityPlanModel(sqlConn, c.Cache),
		SlaThresholdModel:           model.NewSlaThresholdModel(sqlConn, c.Cache),
		SlaReportNotifierModel:      model.NewSlaReportNotifierModel(sqlConn, c.Cache),
		PromptConfigModel:           model.NewPromptConfigurationModel(sqlConn, c.Cache),
		PromptConfigReferenceModel:  model.NewPromptConfigurationReferenceRelationshipModel(sqlConn, c.Cache),
		ApplicationConfigModel:      model.NewApplicationConfigurationModel(sqlConn, c.Cache),
		ApplicationConfigReferenceModel: model.NewApplicationConfigurationReferenceRelationshipModel(
			sqlConn, c.Cache,
		),
		UIAgentComponentModel:      model.NewUiAgentComponentModel(sqlConn, c.Cache),
		UIAgentImageModel:          model.NewUiAgentImageModel(sqlConn, c.Cache),
		UIAgentImageReferenceModel: model.NewUiAgentImageReferenceRelationshipModel(sqlConn, c.Cache),

		// RPC
		BeatRPC:               beat.NewRPCClient(c.Beat),
		DeviceHubRPC:          devicehub.NewRPCClient(c.DeviceHub),
		LarkProxyRPC:          larkproxy.NewRPCClient(c.LarkProxy),
		NotifierRPC:           notifier.NewRPCClient(c.Notifier),
		PermissionDomainRPC:   domainservice.NewPermissionDomainRPC(c.Permission),
		PermissionFunctionRPC: functionservice.NewPermissionPolicyRPC(c.Permission),
		PermissionPolicyRPC:   policyservice.NewPermissionPolicyRPC(c.Permission),
		PermissionRoleRPC:     roleservice.NewPermissionRoleRPC(c.Permission),
		RelationRPC:           relation.NewRPCClient(c.Relation),
		ReporterRPC:           reporter.NewRPCClient(c.Reporter),
		UserRPC:               userservice.NewUserRPC(c.User),

		// MQ
		WorkerConsumerV1:         consumerv1.NewConsumer(c.WorkerConsumerV1),
		WorkerConsumerV2:         consumerv2.NewConsumer(c.WorkerConsumerV2),
		WorkerConsumerV1Producer: producerv2.NewProducer(c.WorkerConsumerV1Producer),
		WorkerProducerV1:         producerv1.NewProducer(c.WorkerProducerV1),
		ManagerConsumer:          consumerv2.NewConsumer(c.ManagerConsumer),
		ManagerProducer:          producerv2.NewProducer(c.ManagerProducer),
		LarkProxyConsumer:        consumerv2.NewConsumer(c.LarkProxyConsumer),
		DispatcherProducer:       producerv2.NewProducer(c.DispatcherProducer),

		CMDBClient:       cmdb.NewClient(c.CMDB),
		AppInsightClient: appInsight.NewClient(c.AppInsight),
	}
}
