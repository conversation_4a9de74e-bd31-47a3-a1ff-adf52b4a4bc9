package internal

import (
	"context"
	"time"

	"github.com/zeromicro/go-zero/core/logx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/caller"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworkerv2/base"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/redislock"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type UpdateInterfaceCoverageHandler struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func newUpdateInterfaceCoverageHandler(svcCtx *svc.ServiceContext) *UpdateInterfaceCoverageHandler {
	ctx := context.Background()
	return &UpdateInterfaceCoverageHandler{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (h *UpdateInterfaceCoverageHandler) Update() error {
	var cancel context.CancelFunc
	h.ctx, cancel = context.WithTimeout(h.ctx, common.ConstExpireOfUpdateInterfaceCaseCoverageTask)
	defer cancel()

	days := h.svcCtx.Config.UpdateInterfaceCoverage.KeepDays
	key := common.ConstLockUpdateInterfaceCoverage
	fn := func() error {
		projects, err := h.svcCtx.ProjectModel.FindAll(h.ctx)
		if err != nil {
			return err
		}

		tp := constants.MQTaskTypeManagerHandleUpdateInterfaceCoverage
		for _, project := range projects {
			if project.CoverageEnabled == 0 {
				continue
			}

			payload := protobuf.MarshalJSONIgnoreError(
				&pb.UpdateInterfaceCoverageTaskInfo{
					ProjectId: project.ProjectId,
					KeepDays:  int64(days),
				},
			)
			if _, err := h.svcCtx.ManagerProducer.Send(
				h.ctx,
				base.NewTask(tp, payload, base.WithRetentionOptions(12*time.Hour)),
				base.QueuePriorityDefault,
			); err != nil {
				h.Errorf("failed to send task to mq, type: %s, payload: %s, error: %+v", tp, payload, err)
			} else {
				h.Debugf("send task to mq successfully, type: %s, payload: %s", tp, payload)
			}
		}

		return nil
	}

	if err := caller.LockWithOptionDo(
		h.svcCtx.Redis, key, fn, redislock.WithExpire(common.ConstExpireOfUpdateInterfaceCaseCoverageTask),
	); err != nil {
		if re, ok := errorx.RootError(err); !ok || re.Code() != errorx.AcquireRedisLockFailure {
			h.Errorf("failed to acquire the redis lock, key: %s, error: %+v", key, err)
			return err
		}

		h.Infof("another service is executing the task of update interface coverage, key: %s", key)
	} else {
		h.Infof("finished to update interface coverage, key: %s, days: %d", key, days)
	}

	return nil
}
