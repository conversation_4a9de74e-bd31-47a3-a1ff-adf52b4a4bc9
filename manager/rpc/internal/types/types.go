package types

import (
	"github.com/zeromicro/go-zero/core/lang"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sqlbuilder/rpc"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type StringPlaceholderCache = map[string]lang.PlaceholderType

type SearchByCategoryId struct {
	ProjectId  string           `json:"project_id" validate:"required" zh:"项目ID"`
	CategoryId string           `json:"category_id" validate:"required" zh:"分类ID"`
	Condition  *rpc.Condition   `json:"condition,omitempty,optional" zh:"查询条件"`
	Pagination *rpc.Pagination  `json:"pagination,omitempty,optional" zh:"查询分页"`
	Sort       []*rpc.SortField `json:"sort,omitempty,optional" zh:"查询排序"`
}

// ChangeFlag 更新标记，用于标记被引用的组件组变更的地方（只区分整体和需要变更时需要同步的部分）
type ChangeFlag struct {
	Whole       bool // 整体
	Name        bool // 名称
	Description bool // 描述
	Imports     bool // 入参
	Exports     bool // 出参
}

// UpdateReference 更新引用，`component_group`、`api_case`、`interface_case`都需要用到，因此定义在这里
type UpdateReference struct {
	ProjectId        string       `json:"project_id"`
	ReferenceType    string       `json:"reference_type"`
	ReferenceId      string       `json:"reference_id"`
	ReferenceVersion string       `json:"reference_version"`
	ComponentGroupId string       `json:"component_group_id"`
	Name             string       `json:"name"`
	Description      string       `json:"description"`
	Imports          []*pb.Import `json:"imports"`
	Exports          []*pb.Export `json:"exports"`
}

type CreateOrUpdateTagReference struct {
	ProjectId        string               `json:"project_id"`
	ReferenceType    common.ReferenceType `json:"reference_type"`
	ReferenceId      string               `json:"reference_id"`
	ReferenceVersion string               `json:"reference_version"`
	Tags             []string             `json:"tags"`
}

type CreateOrUpdateNotifyReference struct {
	ProjectID   string                 `json:"project_id"`
	PlanID      string                 `json:"plan_id"`
	NotifyMode  pb.NotifyMode          `json:"notify_mode"`
	NotifyType  pb.NotifyType          `json:"notify_type"`
	NotifyItems []*pb.CreateNotifyItem `json:"notify_items"`
}

type (
	FieldName   = string
	FieldSchema = map[FieldName]*pb.Schema
)

type ArraySchema = []*pb.Schema

type (
	StatusCode     = string
	StatusResponse = map[StatusCode]*pb.ResponseData
)

// DocumentRecorder 接口文档记录器
type DocumentRecorder struct {
	DocumentId string          `json:"document_id"`
	FullName   string          `json:"full_name"`
	Service    string          `json:"service"`
	ApiName    string          `json:"api_name"`
	ReqSchema  *SchemaRecorder `json:"req_schema"`
	RespSchema *SchemaRecorder `json:"resp_schema"`
	Cmd        uint32          `json:"cmd"`
}

// SchemaRecorder 接口数据模型记录器
type SchemaRecorder struct {
	SchemaId   string            `json:"schema_id"`
	FullName   string            `json:"full_name"`
	Name       string            `json:"name"`
	Data       *pb.Schema        `json:"data"`
	References []*SchemaRecorder `json:"references"`
}

type Recorders = map[string]lang.PlaceholderType
