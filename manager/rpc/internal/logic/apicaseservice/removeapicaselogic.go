package apicaseservicelogic

import (
	"context"
	"fmt"

	"github.com/hashicorp/go-multierror"
	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/mr"
	"github.com/zeromicro/go-zero/core/stores/sqlx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/redislock"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type RemoveApiCaseLogic struct {
	*BaseLogic
}

func NewRemoveApiCaseLogic(ctx context.Context, svcCtx *svc.ServiceContext) *RemoveApiCaseLogic {
	return &RemoveApiCaseLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// RemoveApiCase 删除API用例
func (l *RemoveApiCaseLogic) RemoveApiCase(in *pb.RemoveApiCaseReq) (resp *pb.RemoveApiCaseResp, err error) {
	// validate the project_id in req
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, in.GetProjectId()); err != nil {
		return nil, err
	}

	caseIds := in.GetCaseIds()
	workers := len(caseIds)
	if workers > common.ConstMRMaxWorkers {
		workers = common.ConstMRMaxWorkers
	}

	mr.ForEach(
		func(source chan<- string) {
			for _, caseId := range caseIds {
				source <- caseId
			}
		}, func(item string) {
			if e := l.remove(in.GetProjectId(), item); e != nil {
				err = multierror.Append(err, e)
			}
		}, mr.WithContext(l.ctx), mr.WithWorkers(workers),
	)

	return &pb.RemoveApiCaseResp{}, err
}

func (l *RemoveApiCaseLogic) remove(projectId, caseId string) (err error) {
	// validate the case_id in req
	apiCase, err := model.CheckApiCaseByCaseId(l.ctx, l.svcCtx.ApiCaseModel, projectId, caseId, "")
	if err != nil {
		return err
	}

	// validate the relationship with api suite
	// rrs, err := l.svcCtx.ApiCaseReferenceModel.FindReferenceByCaseId(l.ctx, l.svcCtx.ApiCaseModel, projectId, caseId)
	rrs, err := l.svcCtx.ApiSuiteReferenceModel.FindReferenceByReference(
		l.ctx, projectId, common.ConstReferenceTypeApiCase, caseId,
	)
	if err != nil {
		return errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()),
			"failed to find api case reference with project_id[%s] and case_id[%s], error: %+v",
			projectId, caseId, err,
		)
	} else if len(rrs) > 0 {
		return errorx.Errorf(
			errorx.ProhibitedBehavior,
			"the api case[%s] in suite cannot be deleted, please remove the api case from suite first",
			apiCase.Name,
		)
	}

	// acquire redis lock
	key := fmt.Sprintf("%s:%s:%s", common.ConstLockApiCaseProjectIdCaseIdPrefix, projectId, caseId)
	lock, err := redislock.NewRedisLockAndAcquire(l.svcCtx.Redis, key, redislock.WithExpire(common.ConstLockExpireTime))
	if err != nil {
		return err
	}
	defer func() {
		// release redis lock
		e := lock.Release()
		if e != nil {
			l.Logger.Error(e)
			if re, ok := errorx.RootError(e); ok && re.Code() != errorx.ReleaseRedisLockFailure && err == nil {
				err = e
			}
		}
	}()

	return l.svcCtx.ApiCaseModel.Trans(
		l.ctx, func(context context.Context, session sqlx.Session) error {
			// Table: api_plan_reference_relationship
			if _, err := l.svcCtx.ApiPlanReferenceModel.RemoveByReferenceId(
				context, session, projectId, common.ConstReferenceTypeApiCase, caseId,
			); err != nil {
				return errors.Wrapf(
					errorx.Err(errorx.DBError, err.Error()),
					"failed to remove api plan reference relationship with project_id[%s], reference_type[%s] and reference_id[%s], error: %+v",
					projectId, common.ConstReferenceTypeApiCase, caseId, err,
				)
			}

			// Table: tag_reference_relationship
			if _, err := l.svcCtx.TagReferenceModel.RemoveByReferenceId(
				context, session, projectId, common.ConstReferenceTypeApiCase, caseId, "",
			); err != nil {
				return errors.Wrapf(
					errorx.Err(errorx.DBError, err.Error()),
					"failed to remove tag reference relationship with project_id[%s], reference_type[%s] and reference_id[%s], error: %+v",
					projectId, common.ConstReferenceTypeApiCase, caseId, err,
				)
			}

			// Table: function_reference_relationship
			if _, err := l.svcCtx.FunctionReferenceModel.RemoveByReferenceId(
				context, session, projectId, constants.ApiCase, caseId,
			); err != nil {
				return errors.Wrapf(
					errorx.Err(errorx.DBError, err.Error()),
					"failed to remove data processing function reference relationship with project_id[%s], reference_type[%s] and reference_id[%s], error: %+v",
					projectId, constants.ApiCase, caseId, err,
				)
			}

			// Table: review_record
			if _, err := l.svcCtx.ReviewRecordModel.RemoveByResource(
				context, session, projectId, common.ConstReviewResourceTypeAPICase, caseId,
			); err != nil {
				return errors.Wrapf(
					errorx.Err(errorx.DBError, err.Error()),
					"failed to remove review record with project_id[%s], resource_type[%s] and resource_id[%s], error: %+v",
					projectId, common.ConstReviewResourceTypeAPICase, caseId, err,
				)
			}

			// Table: component_group_reference_relationship
			if _, err := l.svcCtx.ComponentGroupReferenceModel.RemoveByReferenceId(
				context, session, projectId, constants.ApiCase, caseId,
			); err != nil {
				return errors.Wrapf(
					errorx.Err(errorx.DBError, err.Error()),
					"failed to remove component group reference relationship with project_id[%s], reference_type[%s] and reference_id[%s], error: %+v",
					projectId, constants.ApiCase, caseId, err,
				)
			}

			// Table: api_case_element
			if _, err := l.svcCtx.ApiCaseElementModel.RemoveByCaseId(context, session, projectId, caseId); err != nil {
				return errors.Wrapf(
					errorx.Err(errorx.DBError, err.Error()),
					"failed to remove api case element with project_id[%s] and case_id[%s], error: %+v",
					projectId, caseId, err,
				)
			}

			// Table: component
			if _, err := l.svcCtx.ComponentModel.RemoveByParentId(
				context, session, projectId, caseId, constants.ApiCase,
			); err != nil {
				return errors.Wrapf(
					errorx.Err(errorx.DBError, err.Error()),
					"failed to remove component with project_id[%s], parent_id[%s], parent_type[%s], error: %+v",
					projectId, caseId, constants.ApiCase, err,
				)
			}

			// Table: api_case
			if _, err := l.svcCtx.ApiCaseModel.RemoveByCaseId(context, session, projectId, caseId); err != nil {
				return errors.Wrapf(
					errorx.Err(errorx.DBError, err.Error()),
					"failed to remove api case with project_id[%s] and case_id[%s], error: %+v",
					projectId, caseId, err,
				)
			}

			return nil
		},
	)
}
