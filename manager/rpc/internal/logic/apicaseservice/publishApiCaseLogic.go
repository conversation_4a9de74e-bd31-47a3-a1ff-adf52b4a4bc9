package apicaseservicelogic

import (
	"context"
	"fmt"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/caller"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/logic/fsm"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type PublishApiCaseLogic struct {
	*BaseLogic
}

func NewPublishApiCaseLogic(ctx context.Context, svcCtx *svc.ServiceContext) *PublishApiCaseLogic {
	return &PublishApiCaseLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// PublishApiCase 发布API用例
func (l *PublishApiCaseLogic) PublishApiCase(in *pb.PublishApiCaseReq) (out *pb.PublishApiCaseResp, err error) {
	// validate the project_id in req
	project, err := model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, in.GetProjectId())
	if err != nil {
		return nil, err
	}

	key := fmt.Sprintf("%s:%s:%s", common.ConstLockApiCaseProjectIdCaseIdPrefix, in.GetProjectId(), in.GetCaseId())
	fn := func() error {
		// validate the case_id in req
		origin, err := model.CheckApiCaseByCaseId(
			l.ctx, l.svcCtx.ApiCaseModel, in.GetProjectId(), in.GetCaseId(), "",
		)
		if err != nil {
			return err
		}

		if project.ReviewEnabled != 0 {
			return errorx.Errorf(
				errorx.ProhibitedBehavior,
				"the review function of the project has been enabled, and you need to follow the review process to modify the status of api cases, project_id: %s, case_id: %s",
				in.GetProjectId(), in.GetCaseId(),
			)
		}
		if origin.State != string(common.ConstResourceStateToBeImplemented) && origin.State != string(common.ConstResourceStateToBeMaintained) {
			return errorx.Errorf(
				errorx.ProhibitedBehavior,
				"the status of api case must be 'TO_BE_IMPLEMENTED' or 'TO_BE_MAINTAINED' before it can be changed to 'PUBLISHED', project_id: %s, case_id: %s, state: %s",
				in.GetProjectId(), in.GetCaseId(), origin.State,
			)
		}
		if !origin.MaintainedBy.Valid || origin.MaintainedBy.String == "" {
			return errorx.Errorf(
				errorx.ProhibitedBehavior,
				"the maintainer of api case must be set before it can be changed to 'PUBLISHED', project_id: %s, case_id: %s",
				in.GetProjectId(), in.GetCaseId(),
			)
		}

		maintainedBy := origin.MaintainedBy.String
		user, err := l.getUserInfoByAccount(maintainedBy)
		if err != nil {
			return err
		} else if !user.GetEnabled() {
			return errorx.Errorf(
				errorx.ProhibitedBehavior,
				"cannot assign a disabled user as a maintainer, project_id: %s, case_id: %s, maintained_by: %s",
				in.GetProjectId(), in.GetCaseId(), maintainedBy,
			)
		}

		event := common.ConstReviewResourceEventPublishedAfterImplementation
		if origin.State == string(common.ConstResourceStateToBeMaintained) {
			event = common.ConstReviewResourceEventPublishedAfterMaintenance
		}
		if err = fsm.NewResourceFSM(
			l.ctx, l.svcCtx, &ResourceOfApiCase{
				ApiCase: origin,
				Event:   event,
				Emails:  []string{user.GetEmail()},
			}, nil,
		).TransitionWithoutLock(); err != nil {
			return errors.Wrapf(
				errorx.Err(errorx.FSMError, err.Error()),
				"got an error regarding state transition about api case, project_id: %s, resource_id: %s, event: %s, error: %+v",
				in.GetProjectId(), in.GetCaseId(), event, err,
			)
		}

		out = &pb.PublishApiCaseResp{Case: &pb.ApiCase{}}
		if err = utils.Copy(out.Case, origin, l.converters...); err != nil {
			return errors.Wrapf(
				errorx.Err(errorx.CopyToStructFailure, err.Error()),
				"failed to copy api case[%+v] to response, error: %+v",
				origin, err,
			)
		}

		return nil
	}
	err = caller.LockDo(l.svcCtx.Redis, key, fn)

	return out, err
}
