package apicaseservicelogic

import (
	"context"
	"testing"

	"github.com/r3labs/diff/v3"
	"github.com/sergi/go-diff/diffmatchpatch"
	"github.com/zeromicro/go-zero/core/conf"
	"github.com/zeromicro/go-zero/core/jsonx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/config"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

var (
	origin  *pb.ModifyApiCaseReq
	current = &pb.ModifyApiCaseReq{}

	textDiffer      = diffmatchpatch.New()
	structDiffer, _ = diff.<PERSON>Differ()
)

func TestMain(m *testing.M) {
	var c config.Config
	conf.MustLoad("./rpc/etc/manager.yaml", &c)

	svcCtx := svc.NewServiceContext(c)
	view := NewViewApiCaseLogic(context.Background(), svcCtx)

	reqStr := `{
    "project_id": "project_id:YWb3GBvLl1AWSKCf5SL-c",
    "category_id": "category_id:5QK2X6B10ci2qy7xOEjZ_",
    "case_id": "case_id:FH7S7-1jBLO8w6sAXEgCy",
    "name": "修改个性签名",
    "description": "",
    "account_config": {},
    "nodes": [
        {
            "id": "START_1wcean-tX_avfhyau69lw-5EnuRk",
            "type": "START",
            "label": "开始",
            "itemType": "node",
            "width": 40,
            "height": 40,
            "size": [
                40,
                40
            ],
            "x": 704,
            "y": 53,
            "anchorPoints": [
                [
                    0.5,
                    1
                ]
            ],
            "icon": "/img/start.894f1842.svg",
            "style": {
                "active": {
                    "fill": "rgb(247, 250, 255)",
                    "lineWidth": 2,
                    "shadowBlur": 10,
                    "shadowColor": "rgb(95, 149, 255)",
                    "stroke": "rgb(95, 149, 255)"
                },
                "selected": {
                    "fill": "rgb(255, 255, 255)",
                    "lineWidth": 4,
                    "shadowBlur": 10,
                    "shadowColor": "rgb(95, 149, 255)",
                    "stroke": "rgb(95, 149, 255)",
                    "text-shape": {
                        "fontWeight": 500
                    }
                },
                "highlight": {
                    "fill": "rgb(223, 234, 255)",
                    "lineWidth": 2,
                    "stroke": "#4572d9",
                    "text-shape": {
                        "fontWeight": 500
                    }
                },
                "inactive": {
                    "fill": "rgb(247, 250, 255)",
                    "lineWidth": 1,
                    "stroke": "rgb(191, 213, 255)"
                },
                "disable": {
                    "fill": "rgb(250, 250, 250)",
                    "lineWidth": 1,
                    "stroke": "rgb(224, 224, 224)"
                },
                "fill": "#fdf6e7",
                "fillOpacity": 3,
                "lineDash": [],
                "lineWidth": 3,
                "stroke": "#fbad5a"
            }
        },
        {
            "id": "END_1673bg-s1-zdYbJyp7mxDGcQUdq6",
            "type": "END",
            "label": "结束",
            "itemType": "node",
            "width": 40,
            "height": 40,
            "size": [
                40,
                40
            ],
            "x": 705,
            "y": 665,
            "anchorPoints": [
                [
                    0.5,
                    0
                ]
            ],
            "icon": "/img/end.9687f898.svg",
            "style": {
                "active": {
                    "fill": "rgb(247, 250, 255)",
                    "lineWidth": 2,
                    "shadowBlur": 10,
                    "shadowColor": "rgb(95, 149, 255)",
                    "stroke": "rgb(95, 149, 255)"
                },
                "selected": {
                    "fill": "rgb(255, 255, 255)",
                    "lineWidth": 4,
                    "shadowBlur": 10,
                    "shadowColor": "rgb(95, 149, 255)",
                    "stroke": "rgb(95, 149, 255)",
                    "text-shape": {
                        "fontWeight": 500
                    }
                },
                "highlight": {
                    "fill": "rgb(223, 234, 255)",
                    "lineWidth": 2,
                    "stroke": "#4572d9",
                    "text-shape": {
                        "fontWeight": 500
                    }
                },
                "inactive": {
                    "fill": "rgb(247, 250, 255)",
                    "lineWidth": 1,
                    "stroke": "rgb(191, 213, 255)"
                },
                "disable": {
                    "fill": "rgb(250, 250, 250)",
                    "lineWidth": 1,
                    "stroke": "rgb(224, 224, 224)"
                },
                "fill": "#fce6e9",
                "fillOpacity": 3,
                "lineDash": [],
                "lineWidth": 3,
                "stroke": "#f46369"
            }
        },
        {
            "id": "HTTP_1alpqk-R9wG8ia2E5Ziawn-YswhE",
            "type": "HTTP",
            "label": "登录",
            "itemType": "node",
            "width": 80,
            "height": 40,
            "size": [
                80,
                40
            ],
            "x": 703,
            "y": 128,
            "anchorPoints": [
                [
                    0.5,
                    0
                ],
                [
                    0.5,
                    1
                ],
                [
                    1,
                    0.5
                ],
                [
                    0,
                    0.5
                ]
            ],
            "icon": "/img/task.df105b8d.svg",
            "style": {
                "active": {
                    "fill": "rgb(247, 250, 255)",
                    "lineWidth": 2,
                    "shadowBlur": 10,
                    "shadowColor": "rgb(95, 149, 255)",
                    "stroke": "rgb(95, 149, 255)"
                },
                "selected": {
                    "fill": "rgb(255, 255, 255)",
                    "lineWidth": 4,
                    "shadowBlur": 10,
                    "shadowColor": "rgb(95, 149, 255)",
                    "stroke": "rgb(95, 149, 255)",
                    "text-shape": {
                        "fontWeight": 500
                    }
                },
                "highlight": {
                    "fill": "rgb(223, 234, 255)",
                    "lineWidth": 2,
                    "stroke": "#4572d9",
                    "text-shape": {
                        "fontWeight": 500
                    }
                },
                "inactive": {
                    "fill": "rgb(247, 250, 255)",
                    "lineWidth": 1,
                    "stroke": "rgb(191, 213, 255)"
                },
                "disable": {
                    "fill": "rgb(250, 250, 250)",
                    "lineWidth": 1,
                    "stroke": "rgb(224, 224, 224)"
                },
                "fill": "#e7f7fe",
                "fillOpacity": 3,
                "lineDash": [],
                "lineWidth": 3,
                "stroke": "#9ed2fe"
            },
            "data": {
                "assertions": [
                    {
                        "body": {
                            "jmespath": {
                                "compare": "EQ",
                                "expectation": "",
                                "expression": ""
                            },
                            "regex": {
                                "expression": ""
                            },
                            "type": 1
                        },
                        "headers": {
                            "expression": "",
                            "key": ""
                        },
                        "source": 2,
                        "status_code": {
                            "compare": "EQ",
                            "expectation": "200"
                        }
                    }
                ],
                "authorization": {
                    "api_key": {
                        "add_to": 1,
                        "key": "",
                        "value": ""
                    },
                    "basic_auth": {
                        "password": "",
                        "username": ""
                    },
                    "bearer_token": {
                        "token": ""
                    },
                    "type": 0
                },
                "body": {
                    "type": 2,
                    "form_data": [
                        {
                            "description": "",
                            "key": "",
                            "value": ""
                        }
                    ],
                    "raw": "{\n    \"account\": \"*********\",\n    \"client_version\": \"6.0.0\",\n    \"drv_type\": 3,\n    \"is_tes_account\": true,\n    \"password\": \"Tt123456\",\n    \"platform_type\": \"1\",\n    \"prod_type\": 1,\n    \"uri\": \"tcp://testing-login.ttyuyin.com\",\n    \"use_epoll\": true\n}"
                },
                "exports": [
                    {
                        "body": {
                            "expression": "data.cid",
                            "type": 0
                        },
                        "headers": {
                            "key": ""
                        },
                        "name": "cid",
                        "source": 1
                    },
                    {
                        "body": {
                            "expression": "data.account",
                            "type": 0
                        },
                        "headers": {
                            "key": ""
                        },
                        "name": "account",
                        "source": 1
                    }
                ],
                "headers": [
                    {
                        "description": "",
                        "key": "",
                        "value": ""
                    }
                ],
                "imports": [
                    {
                        "account": {},
                        "environment": {},
                        "export": {
                            "node_id": "",
                            "value": ""
                        },
                        "manual": {
                            "value": ""
                        },
                        "name": "",
                        "source": 1
                    }
                ],
                "method": "POST",
                "name": "登录",
                "query_params": [
                    {
                        "description": "",
                        "key": "",
                        "value": ""
                    }
                ],
                "timeout": {
                    "connect_timeout": 6000,
                    "request_timeout": 6001,
                    "response_timeout": 6002
                },
                "url": "http://probe.ttyuyin.com/tds/common/client"
            }
        },
        {
            "id": "HTTP_1cwy3g-_kU0yCEAOCj6nZEgiLjgF",
            "type": "HTTP",
            "label": "修改个性签名",
            "itemType": "node",
            "width": 80,
            "height": 40,
            "size": [
                80,
                40
            ],
            "x": 701,
            "y": 418,
            "anchorPoints": [
                [
                    0.5,
                    0
                ],
                [
                    0.5,
                    1
                ],
                [
                    1,
                    0.5
                ],
                [
                    0,
                    0.5
                ]
            ],
            "icon": "/img/task.df105b8d.svg",
            "style": {
                "active": {
                    "fill": "rgb(247, 250, 255)",
                    "lineWidth": 2,
                    "shadowBlur": 10,
                    "shadowColor": "rgb(95, 149, 255)",
                    "stroke": "rgb(95, 149, 255)"
                },
                "selected": {
                    "fill": "rgb(255, 255, 255)",
                    "lineWidth": 4,
                    "shadowBlur": 10,
                    "shadowColor": "rgb(95, 149, 255)",
                    "stroke": "rgb(95, 149, 255)",
                    "text-shape": {
                        "fontWeight": 500
                    }
                },
                "highlight": {
                    "fill": "rgb(223, 234, 255)",
                    "lineWidth": 2,
                    "stroke": "#4572d9",
                    "text-shape": {
                        "fontWeight": 500
                    }
                },
                "inactive": {
                    "fill": "rgb(247, 250, 255)",
                    "lineWidth": 1,
                    "stroke": "rgb(191, 213, 255)"
                },
                "disable": {
                    "fill": "rgb(250, 250, 250)",
                    "lineWidth": 1,
                    "stroke": "rgb(224, 224, 224)"
                },
                "fill": "#e7f7fe",
                "fillOpacity": 3,
                "lineDash": [],
                "lineWidth": 3,
                "stroke": "#9ed2fe"
            },
            "data": {
                "assertions": [
                    {
                        "body": {
                            "jmespath": {
                                "compare": "EQ",
                                "expectation": "",
                                "expression": ""
                            },
                            "regex": {
                                "expression": ""
                            },
                            "type": 1
                        },
                        "headers": {
                            "expression": "",
                            "key": ""
                        },
                        "source": 2,
                        "status_code": {
                            "compare": "EQ",
                            "expectation": "200"
                        }
                    }
                ],
                "authorization": {
                    "api_key": {
                        "add_to": 1,
                        "key": "",
                        "value": ""
                    },
                    "basic_auth": {
                        "password": "",
                        "username": ""
                    },
                    "bearer_token": {
                        "token": ""
                    },
                    "type": 0
                },
                "body": {
                    "form_data": [
                        {
                            "description": "",
                            "key": "",
                            "value": ""
                        }
                    ],
                    "raw": "{\n    \"api_name\": \"ModifySignature\",\n    \"client\": {\n        \"cid\": {{.cid}}\n    },\n    \"proto_json\": {\n        \"new_signature\": \"真正的吃货敢于直面粗壮的大腿\"\n    }\n}",
                    "type": 2
                },
                "exports": [
                    {
                        "body": {
                            "expression": "提取表达式",
                            "type": 1
                        },
                        "headers": {
                            "key": ""
                        },
                        "name": "",
                        "source": 0
                    }
                ],
                "headers": [
                    {
                        "description": "",
                        "key": "",
                        "value": ""
                    }
                ],
                "imports": [
                    {
                        "account": {},
                        "environment": {},
                        "export": {
                            "node_id": "HTTP_1alpqk-R9wG8ia2E5Ziawn-YswhE",
                            "value": "cid"
                        },
                        "manual": {
                            "value": ""
                        },
                        "name": "cid",
                        "source": 1
                    }
                ],
                "method": "POST",
                "name": "修改个性签名",
                "query_params": [
                    {
                        "description": "",
                        "key": "",
                        "value": ""
                    }
                ],
                "timeout": {
                    "connect_timeout": 6000,
                    "request_timeout": 6001,
                    "response_timeout": 6002
                },
                "url": "http://probe.ttyuyin.com/tds/common/call"
            }
        },
        {
            "id": "HTTP_18hmqr-MgvID6gCEiNyj_V886nwf",
            "type": "HTTP",
            "label": "查询个性签名",
            "itemType": "node",
            "width": 80,
            "height": 40,
            "size": [
                80,
                40
            ],
            "x": 700,
            "y": 341,
            "anchorPoints": [
                [
                    0.5,
                    0
                ],
                [
                    0.5,
                    1
                ],
                [
                    1,
                    0.5
                ],
                [
                    0,
                    0.5
                ]
            ],
            "icon": "/img/task.df105b8d.svg",
            "style": {
                "active": {
                    "fill": "rgb(247, 250, 255)",
                    "lineWidth": 2,
                    "shadowBlur": 10,
                    "shadowColor": "rgb(95, 149, 255)",
                    "stroke": "rgb(95, 149, 255)"
                },
                "selected": {
                    "fill": "rgb(255, 255, 255)",
                    "lineWidth": 4,
                    "shadowBlur": 10,
                    "shadowColor": "rgb(95, 149, 255)",
                    "stroke": "rgb(95, 149, 255)",
                    "text-shape": {
                        "fontWeight": 500
                    }
                },
                "highlight": {
                    "fill": "rgb(223, 234, 255)",
                    "lineWidth": 2,
                    "stroke": "#4572d9",
                    "text-shape": {
                        "fontWeight": 500
                    }
                },
                "inactive": {
                    "fill": "rgb(247, 250, 255)",
                    "lineWidth": 1,
                    "stroke": "rgb(191, 213, 255)"
                },
                "disable": {
                    "fill": "rgb(250, 250, 250)",
                    "lineWidth": 1,
                    "stroke": "rgb(224, 224, 224)"
                },
                "fill": "#e7f7fe",
                "fillOpacity": 3,
                "lineDash": [],
                "lineWidth": 3,
                "stroke": "#9ed2fe"
            },
            "data": {
                "assertions": [
                    {
                        "body": {
                            "jmespath": {
                                "compare": "EQ",
                                "expectation": "",
                                "expression": ""
                            },
                            "regex": {
                                "expression": ""
                            },
                            "type": 1
                        },
                        "headers": {
                            "expression": "",
                            "key": ""
                        },
                        "source": 2,
                        "status_code": {
                            "compare": "EQ",
                            "expectation": "201"
                        }
                    }
                ],
                "authorization": {
                    "api_key": {
                        "add_to": 1,
                        "key": "",
                        "value": ""
                    },
                    "basic_auth": {
                        "password": "",
                        "username": ""
                    },
                    "bearer_token": {
                        "token": ""
                    },
                    "type": 0
                },
                "body": {
                    "form_data": [
                        {
                            "description": "",
                            "key": "",
                            "value": ""
                        }
                    ],
                    "raw": "{\n    \"api_name\": \"GetUserDetail\",\n    \"client\": {\n        \"cid\": {{.cid}}\n    },\n    \"proto_json\": {\n        \"target_account\": \"{{.account}}\"\n    }\n}",
                    "type": 2
                },
                "exports": [
                    {
                        "body": {
                            "expression": "data.contact.signature",
                            "type": 0
                        },
                        "headers": {
                            "key": ""
                        },
                        "name": "init_signature",
                        "source": 1
                    }
                ],
                "headers": [
                    {
                        "description": "",
                        "key": "",
                        "value": ""
                    }
                ],
                "imports": [
                    {
                        "account": {},
                        "environment": {},
                        "export": {
                            "node_id": "HTTP_1alpqk-R9wG8ia2E5Ziawn-YswhE",
                            "value": "cid"
                        },
                        "manual": {
                            "value": ""
                        },
                        "name": "cid",
                        "source": 1
                    },
                    {
                        "account": {},
                        "environment": {},
                        "export": {
                            "node_id": "HTTP_1alpqk-R9wG8ia2E5Ziawn-YswhE",
                            "value": "account"
                        },
                        "manual": {
                            "value": ""
                        },
                        "name": "account",
                        "source": 1
                    }
                ],
                "method": "POST",
                "name": "查询个性签名",
                "query_params": [
                    {
                        "description": "",
                        "key": "",
                        "value": ""
                    }
                ],
                "timeout": {
                    "connect_timeout": 6000,
                    "request_timeout": 6001,
                    "response_timeout": 6002
                },
                "url": "http://probe.ttyuyin.com/tds/common/call"
            }
        },
        {
            "id": "HTTP_wz9rhj-U5VOM-BAlQ1wLtysjR4nj",
            "type": "HTTP",
            "label": "验证修改的个性签名",
            "itemType": "node",
            "width": 80,
            "height": 40,
            "size": [
                80,
                40
            ],
            "x": 702,
            "y": 504,
            "anchorPoints": [
                [
                    0.5,
                    0
                ],
                [
                    0.5,
                    1
                ],
                [
                    1,
                    0.5
                ],
                [
                    0,
                    0.5
                ]
            ],
            "icon": "/img/task.df105b8d.svg",
            "style": {
                "active": {
                    "fill": "rgb(247, 250, 255)",
                    "lineWidth": 2,
                    "shadowBlur": 10,
                    "shadowColor": "rgb(95, 149, 255)",
                    "stroke": "rgb(95, 149, 255)"
                },
                "selected": {
                    "fill": "rgb(255, 255, 255)",
                    "lineWidth": 4,
                    "shadowBlur": 10,
                    "shadowColor": "rgb(95, 149, 255)",
                    "stroke": "rgb(95, 149, 255)",
                    "text-shape": {
                        "fontWeight": 500
                    }
                },
                "highlight": {
                    "fill": "rgb(223, 234, 255)",
                    "lineWidth": 2,
                    "stroke": "#4572d9",
                    "text-shape": {
                        "fontWeight": 500
                    }
                },
                "inactive": {
                    "fill": "rgb(247, 250, 255)",
                    "lineWidth": 1,
                    "stroke": "rgb(191, 213, 255)"
                },
                "disable": {
                    "fill": "rgb(250, 250, 250)",
                    "lineWidth": 1,
                    "stroke": "rgb(224, 224, 224)"
                },
                "fill": "#e7f7fe",
                "fillOpacity": 3,
                "lineDash": [],
                "lineWidth": 3,
                "stroke": "#9ed2fe"
            },
            "data": {
                "assertions": [
                    {
                        "body": {
                            "jmespath": {
                                "compare": "EQ",
                                "expectation": "",
                                "expression": ""
                            },
                            "regex": {
                                "expression": ""
                            },
                            "type": 1
                        },
                        "headers": {
                            "expression": "",
                            "key": ""
                        },
                        "source": 2,
                        "status_code": {
                            "compare": "EQ",
                            "expectation": "200"
                        }
                    }
                ],
                "authorization": {
                    "api_key": {
                        "add_to": 1,
                        "key": "",
                        "value": ""
                    },
                    "basic_auth": {
                        "password": "",
                        "username": ""
                    },
                    "bearer_token": {
                        "token": ""
                    },
                    "type": 0
                },
                "body": {
                    "form_data": [
                        {
                            "description": "",
                            "key": "",
                            "value": ""
                        }
                    ],
                    "raw": "{\n    \"api_name\": \"GetUserDetail\",\n    \"client\": {\n        \"cid\": {{.cid}}\n    },\n    \"proto_json\": {\n        \"target_account\": \"{{.account}}\"\n    }\n}",
                    "type": 2
                },
                "exports": [
                    {
                        "body": {
                            "expression": "提取表达式",
                            "type": 1
                        },
                        "headers": {
                            "key": ""
                        },
                        "name": "",
                        "source": 0
                    }
                ],
                "headers": [
                    {
                        "description": "",
                        "key": "",
                        "value": ""
                    }
                ],
                "imports": [
                    {
                        "account": {},
                        "environment": {},
                        "export": {
                            "node_id": "HTTP_1alpqk-R9wG8ia2E5Ziawn-YswhE",
                            "value": "cid"
                        },
                        "manual": {
                            "value": ""
                        },
                        "name": "cid",
                        "source": 1
                    },
                    {
                        "account": {},
                        "environment": {},
                        "export": {
                            "node_id": "HTTP_1alpqk-R9wG8ia2E5Ziawn-YswhE",
                            "value": "account"
                        },
                        "manual": {
                            "value": ""
                        },
                        "name": "account",
                        "source": 1
                    }
                ],
                "method": "POST",
                "name": "验证修改的个性签名",
                "query_params": [
                    {
                        "description": "",
                        "key": "",
                        "value": ""
                    }
                ],
                "timeout": {
                    "connect_timeout": 6000,
                    "request_timeout": 6001,
                    "response_timeout": 6002
                },
                "url": "http://probe.ttyuyin.com/tds/common/call"
            }
        },
        {
            "id": "HTTP_14xfyc-wXqpjjnFlfym_EwyC_FP-",
            "type": "HTTP",
            "label": "将个性签名修改回原有签名",
            "itemType": "node",
            "width": 80,
            "height": 40,
            "size": [
                80,
                40
            ],
            "x": 704,
            "y": 587,
            "anchorPoints": [
                [
                    0.5,
                    0
                ],
                [
                    0.5,
                    1
                ],
                [
                    1,
                    0.5
                ],
                [
                    0,
                    0.5
                ]
            ],
            "icon": "/img/task.df105b8d.svg",
            "style": {
                "active": {
                    "fill": "rgb(247, 250, 255)",
                    "lineWidth": 2,
                    "shadowBlur": 10,
                    "shadowColor": "rgb(95, 149, 255)",
                    "stroke": "rgb(95, 149, 255)"
                },
                "selected": {
                    "fill": "rgb(255, 255, 255)",
                    "lineWidth": 4,
                    "shadowBlur": 10,
                    "shadowColor": "rgb(95, 149, 255)",
                    "stroke": "rgb(95, 149, 255)",
                    "text-shape": {
                        "fontWeight": 500
                    }
                },
                "highlight": {
                    "fill": "rgb(223, 234, 255)",
                    "lineWidth": 2,
                    "stroke": "#4572d9",
                    "text-shape": {
                        "fontWeight": 500
                    }
                },
                "inactive": {
                    "fill": "rgb(247, 250, 255)",
                    "lineWidth": 1,
                    "stroke": "rgb(191, 213, 255)"
                },
                "disable": {
                    "fill": "rgb(250, 250, 250)",
                    "lineWidth": 1,
                    "stroke": "rgb(224, 224, 224)"
                },
                "fill": "#e7f7fe",
                "fillOpacity": 3,
                "lineDash": [],
                "lineWidth": 3,
                "stroke": "#9ed2fe"
            },
            "data": {
                "assertions": [
                    {
                        "body": {
                            "jmespath": {
                                "compare": "EQ",
                                "expectation": "",
                                "expression": ""
                            },
                            "regex": {
                                "expression": ""
                            },
                            "type": 1
                        },
                        "headers": {
                            "expression": "",
                            "key": ""
                        },
                        "source": 2,
                        "status_code": {
                            "compare": "EQ",
                            "expectation": "200"
                        }
                    }
                ],
                "authorization": {
                    "api_key": {
                        "add_to": 1,
                        "key": "",
                        "value": ""
                    },
                    "basic_auth": {
                        "password": "",
                        "username": ""
                    },
                    "bearer_token": {
                        "token": ""
                    },
                    "type": 0
                },
                "body": {
                    "form_data": [
                        {
                            "description": "",
                            "key": "",
                            "value": ""
                        }
                    ],
                    "raw": "{\n    \"api_name\": \"ModifySignature\",\n    \"client\": {\n        \"cid\": {{.cid}}\n    },\n    \"proto_json\": {\n        \"new_signature\": \"{{.init_signature}}\"\n    }\n}",
                    "type": 2
                },
                "exports": [
                    {
                        "body": {
                            "expression": "提取表达式",
                            "type": 1
                        },
                        "headers": {
                            "key": ""
                        },
                        "name": "",
                        "source": 0
                    }
                ],
                "headers": [
                    {
                        "description": "",
                        "key": "",
                        "value": ""
                    }
                ],
                "imports": [
                    {
                        "account": {},
                        "environment": {},
                        "export": {
                            "node_id": "HTTP_1alpqk-R9wG8ia2E5Ziawn-YswhE",
                            "value": "cid"
                        },
                        "manual": {
                            "value": ""
                        },
                        "name": "cid",
                        "source": 1
                    },
                    {
                        "account": {},
                        "environment": {},
                        "export": {
                            "node_id": "HTTP_18hmqr-MgvID6gCEiNyj_V886nwf",
                            "value": "init_signature"
                        },
                        "manual": {
                            "value": ""
                        },
                        "name": "init_signature",
                        "source": 1
                    }
                ],
                "method": "POST",
                "name": "将个性签名修改回原有签名",
                "query_params": [
                    {
                        "description": "",
                        "key": "",
                        "value": ""
                    }
                ],
                "timeout": {
                    "connect_timeout": 6000,
                    "request_timeout": 6001,
                    "response_timeout": 6002
                },
                "url": "http://probe.ttyuyin.com/tds/common/call"
            }
        },
        {
            "type": "COMPONENT_GROUP",
            "label": "业务组件0",
            "width": 80,
            "height": 110,
            "size": [
                80,
                110
            ],
            "anchorPoints": [
                [
                    0.5,
                    0
                ],
                [
                    0.5,
                    1
                ],
                [
                    1,
                    0.5
                ],
                [
                    0,
                    0.5
                ]
            ],
            "icon": "/img/square.d4d3095a.svg",
            "style": {
                "active": {
                    "fill": "rgb(247, 250, 255)",
                    "stroke": "rgb(95, 149, 255)",
                    "lineWidth": 2,
                    "shadowColor": "rgb(95, 149, 255)",
                    "shadowBlur": 10
                },
                "selected": {
                    "fill": "rgb(255, 255, 255)",
                    "stroke": "rgb(95, 149, 255)",
                    "lineWidth": 4,
                    "shadowColor": "rgb(95, 149, 255)",
                    "shadowBlur": 10,
                    "text-shape": {
                        "fontWeight": 500
                    }
                },
                "highlight": {
                    "fill": "rgb(223, 234, 255)",
                    "stroke": "#4572d9",
                    "lineWidth": 2,
                    "text-shape": {
                        "fontWeight": 500
                    }
                },
                "inactive": {
                    "fill": "rgb(247, 250, 255)",
                    "stroke": "rgb(191, 213, 255)",
                    "lineWidth": 1
                },
                "disable": {
                    "fill": "rgb(250, 250, 250)",
                    "stroke": "rgb(224, 224, 224)",
                    "lineWidth": 1
                },
                "fill": "#f0fea9",
                "stroke": "#013914",
                "fillOpacity": 3,
                "lineWidth": 3,
                "lineDash": [],
                "opacity": 0.5
            },
            "labelCfg": {
                "style": {
                    "fill": "#000",
                    "opacity": 1
                },
                "position": "bottom"
            },
            "data": {
                "reference_id": "component_group_id:lVxTnJac1t8ukX-Zx2xKA",
                "exports": [
                    {
                        "name": "a",
                        "export": {
                            "value": "export_a",
                            "node_id": "HTTP_1vmv8d-oexWSmpnWjPTES1PjaJQN"
                        }
                    }
                ],
                "imports": [
                    {
                        "name": "name",
                        "export": {
                            "value": "",
                            "node_id": ""
                        },
                        "manual": {
                            "value": "allen"
                        },
                        "source": 0,
                        "environment": {
                            "value": ""
                        }
                    },
                    {
                        "name": "age",
                        "export": {
                            "value": "",
                            "node_id": ""
                        },
                        "manual": {
                            "value": "20"
                        },
                        "source": 0,
                        "environment": {
                            "value": ""
                        }
                    }
                ],
                "account_config": {
                    "combo": {
                        "2": "TT语音2"
                    },
                    "whole": {
                        "2": "TT语音2"
                    }
                }
            },
            "x": 702,
            "y": 232,
            "id": "COMPONENT_GROUP_1h9817-_Oeurqv-D_aozBJ7Ibo5K",
            "itemType": "node"
        }
    ],
    "edges": [
        {
            "id": "16fdgt-5ib3hESziyheHTQw_5uNa",
            "type": "custom-edge",
            "label": "",
            "source": "HTTP_18hmqr-MgvID6gCEiNyj_V886nwf",
            "sourceAnchor": 3,
            "target": "HTTP_1cwy3g-_kU0yCEAOCj6nZEgiLjgF",
            "targetAnchor": 1,
            "lineAppendWidth": 20,
            "attrs": {
                "end": "HTTP_1cwy3g-_kU0yCEAOCj6nZEgiLjgF",
                "start": "HTTP_18hmqr-MgvID6gCEiNyj_V886nwf"
            },
            "style": {
                "active": {
                    "lineWidth": 1,
                    "stroke": "rgb(95, 149, 255)"
                },
                "disable": {
                    "lineWidth": 1,
                    "stroke": "rgb(245, 245, 245)"
                },
                "endArrow": {
                    "d": 5,
                    "fill": "#999999",
                    "lineWidth": 3,
                    "opacity": 0.3,
                    "path": "M 10,0 L 20,-5 L 20,5 Z",
                    "stroke": "#999999"
                },
                "highlight": {
                    "lineWidth": 2,
                    "stroke": "rgb(95, 149, 255)",
                    "text-shape": {
                        "fontWeight": 500
                    }
                },
                "inactive": {
                    "lineWidth": 1,
                    "stroke": "rgb(234, 234, 234)"
                },
                "lineDash": [],
                "lineWidth": 3,
                "selected": {
                    "lineWidth": 2,
                    "shadowBlur": 10,
                    "shadowColor": "rgb(95, 149, 255)",
                    "stroke": "rgb(95, 149, 255)",
                    "text-shape": {
                        "fontWeight": 500
                    }
                },
                "startArrow": false,
                "stroke": "#999999"
            },
            "labelCfg": {
                "position": "center",
                "refX": 0,
                "style": {
                    "fontSize": 16
                }
            },
            "startPoint": {
                "x": 700,
                "y": 362.5,
                "anchorIndex": 3,
                "id": "70|||36"
            },
            "endPoint": {
                "x": 700,
                "y": 396.5,
                "anchorIndex": 1,
                "id": "70|||40"
            }
        },
        {
            "id": "1wc07r-lx65eLI6bbGsm6ox6upYU",
            "type": "custom-edge",
            "label": "",
            "source": "HTTP_14xfyc-wXqpjjnFlfym_EwyC_FP-",
            "sourceAnchor": 3,
            "target": "END_1673bg-s1-zdYbJyp7mxDGcQUdq6",
            "targetAnchor": 0,
            "lineAppendWidth": 20,
            "attrs": {
                "end": "END_1673bg-s1-zdYbJyp7mxDGcQUdq6",
                "start": "HTTP_14xfyc-wXqpjjnFlfym_EwyC_FP-"
            },
            "style": {
                "active": {
                    "lineWidth": 1,
                    "stroke": "rgb(95, 149, 255)"
                },
                "disable": {
                    "lineWidth": 1,
                    "stroke": "rgb(245, 245, 245)"
                },
                "endArrow": {
                    "d": 5,
                    "fill": "#999999",
                    "lineWidth": 3,
                    "opacity": 0.3,
                    "path": "M 10,0 L 20,-5 L 20,5 Z",
                    "stroke": "#999999"
                },
                "highlight": {
                    "lineWidth": 2,
                    "stroke": "rgb(95, 149, 255)",
                    "text-shape": {
                        "fontWeight": 500
                    }
                },
                "inactive": {
                    "lineWidth": 1,
                    "stroke": "rgb(234, 234, 234)"
                },
                "lineDash": [],
                "lineWidth": 3,
                "selected": {
                    "lineWidth": 2,
                    "shadowBlur": 10,
                    "shadowColor": "rgb(95, 149, 255)",
                    "stroke": "rgb(95, 149, 255)",
                    "text-shape": {
                        "fontWeight": 500
                    }
                },
                "startArrow": false,
                "stroke": "#999999"
            },
            "labelCfg": {
                "position": "center",
                "refX": 0,
                "style": {
                    "fontSize": 16
                }
            },
            "startPoint": {
                "x": 704,
                "y": 608.5,
                "anchorIndex": 3,
                "id": "704|||608.5"
            },
            "endPoint": {
                "x": 705,
                "y": 643.5,
                "anchorIndex": 0,
                "id": "705|||643.5"
            }
        },
        {
            "id": "16e8b4-LhPYfXa3JXbG_WxZD5A8y",
            "type": "custom-edge",
            "label": "",
            "source": "START_1wcean-tX_avfhyau69lw-5EnuRk",
            "sourceAnchor": 0,
            "target": "HTTP_1alpqk-R9wG8ia2E5Ziawn-YswhE",
            "targetAnchor": 1,
            "lineAppendWidth": 20,
            "attrs": {
                "end": "HTTP_1alpqk-R9wG8ia2E5Ziawn-YswhE",
                "start": "START_1wcean-tX_avfhyau69lw-5EnuRk"
            },
            "style": {
                "active": {
                    "lineWidth": 1,
                    "stroke": "rgb(95, 149, 255)"
                },
                "disable": {
                    "lineWidth": 1,
                    "stroke": "rgb(245, 245, 245)"
                },
                "endArrow": {
                    "d": 5,
                    "fill": "#999999",
                    "lineWidth": 3,
                    "opacity": 0.3,
                    "path": "M 10,0 L 20,-5 L 20,5 Z",
                    "stroke": "#999999"
                },
                "highlight": {
                    "lineWidth": 2,
                    "stroke": "rgb(95, 149, 255)",
                    "text-shape": {
                        "fontWeight": 500
                    }
                },
                "inactive": {
                    "lineWidth": 1,
                    "stroke": "rgb(234, 234, 234)"
                },
                "lineDash": [],
                "lineWidth": 3,
                "selected": {
                    "lineWidth": 2,
                    "shadowBlur": 10,
                    "shadowColor": "rgb(95, 149, 255)",
                    "stroke": "rgb(95, 149, 255)",
                    "text-shape": {
                        "fontWeight": 500
                    }
                },
                "startArrow": false,
                "stroke": "#999999"
            },
            "labelCfg": {
                "position": "center",
                "refX": 0,
                "style": {
                    "fontSize": 16
                }
            },
            "startPoint": {
                "x": 704,
                "y": 74.5,
                "anchorIndex": 0,
                "id": "70|||7"
            },
            "endPoint": {
                "x": 704,
                "y": 106.5,
                "anchorIndex": 1,
                "id": "70|||11"
            }
        },
        {
            "id": "1n4ap4-NrvgRjbDIs7UmGqP26CJ3",
            "type": "custom-edge",
            "label": "",
            "source": "HTTP_1cwy3g-_kU0yCEAOCj6nZEgiLjgF",
            "sourceAnchor": 3,
            "target": "HTTP_wz9rhj-U5VOM-BAlQ1wLtysjR4nj",
            "targetAnchor": 1,
            "lineAppendWidth": 20,
            "attrs": {
                "end": "HTTP_wz9rhj-U5VOM-BAlQ1wLtysjR4nj",
                "start": "HTTP_1cwy3g-_kU0yCEAOCj6nZEgiLjgF"
            },
            "style": {
                "active": {
                    "lineWidth": 1,
                    "stroke": "rgb(95, 149, 255)"
                },
                "disable": {
                    "lineWidth": 1,
                    "stroke": "rgb(245, 245, 245)"
                },
                "endArrow": {
                    "d": 5,
                    "fill": "#999999",
                    "lineWidth": 3,
                    "opacity": 0.3,
                    "path": "M 10,0 L 20,-5 L 20,5 Z",
                    "stroke": "#999999"
                },
                "highlight": {
                    "lineWidth": 2,
                    "stroke": "rgb(95, 149, 255)",
                    "text-shape": {
                        "fontWeight": 500
                    }
                },
                "inactive": {
                    "lineWidth": 1,
                    "stroke": "rgb(234, 234, 234)"
                },
                "lineDash": [],
                "lineWidth": 3,
                "selected": {
                    "lineWidth": 2,
                    "shadowBlur": 10,
                    "shadowColor": "rgb(95, 149, 255)",
                    "stroke": "rgb(95, 149, 255)",
                    "text-shape": {
                        "fontWeight": 500
                    }
                },
                "startArrow": false,
                "stroke": "#999999"
            },
            "labelCfg": {
                "position": "center",
                "refX": 0,
                "style": {
                    "fontSize": 16
                }
            },
            "startPoint": {
                "x": 701,
                "y": 439.5,
                "anchorIndex": 3,
                "id": "70|||44"
            },
            "endPoint": {
                "x": 701,
                "y": 482.5,
                "anchorIndex": 1,
                "id": "70|||48"
            }
        },
        {
            "id": "cc0nuv-c1hFcxoRymT3wszcv-wbR",
            "type": "custom-edge",
            "label": "",
            "source": "HTTP_wz9rhj-U5VOM-BAlQ1wLtysjR4nj",
            "sourceAnchor": 3,
            "target": "HTTP_14xfyc-wXqpjjnFlfym_EwyC_FP-",
            "targetAnchor": 1,
            "lineAppendWidth": 20,
            "attrs": {
                "end": "HTTP_14xfyc-wXqpjjnFlfym_EwyC_FP-",
                "start": "HTTP_wz9rhj-U5VOM-BAlQ1wLtysjR4nj"
            },
            "style": {
                "active": {
                    "lineWidth": 1,
                    "stroke": "rgb(95, 149, 255)"
                },
                "disable": {
                    "lineWidth": 1,
                    "stroke": "rgb(245, 245, 245)"
                },
                "endArrow": {
                    "d": 5,
                    "fill": "#999999",
                    "lineWidth": 3,
                    "opacity": 0.3,
                    "path": "M 10,0 L 20,-5 L 20,5 Z",
                    "stroke": "#999999"
                },
                "highlight": {
                    "lineWidth": 2,
                    "stroke": "rgb(95, 149, 255)",
                    "text-shape": {
                        "fontWeight": 500
                    }
                },
                "inactive": {
                    "lineWidth": 1,
                    "stroke": "rgb(234, 234, 234)"
                },
                "lineDash": [],
                "lineWidth": 3,
                "selected": {
                    "lineWidth": 2,
                    "shadowBlur": 10,
                    "shadowColor": "rgb(95, 149, 255)",
                    "stroke": "rgb(95, 149, 255)",
                    "text-shape": {
                        "fontWeight": 500
                    }
                },
                "startArrow": false,
                "stroke": "#999999"
            },
            "labelCfg": {
                "position": "center",
                "refX": 0,
                "style": {
                    "fontSize": 16
                }
            },
            "startPoint": {
                "x": 702,
                "y": 525.5,
                "anchorIndex": 3,
                "id": "70|||53"
            },
            "endPoint": {
                "x": 702,
                "y": 565.5,
                "anchorIndex": 1,
                "id": "70|||57"
            }
        },
        {
            "id": "2ad9co-bSIR8NFhUlJZzkSoTyjrR",
            "source": "HTTP_1alpqk-R9wG8ia2E5Ziawn-YswhE",
            "sourceAnchor": 3,
            "target": "COMPONENT_GROUP_1h9817-_Oeurqv-D_aozBJ7Ibo5K",
            "lineAppendWidth": 20,
            "attrs": {
                "start": "HTTP_1alpqk-R9wG8ia2E5Ziawn-YswhE",
                "end": "COMPONENT_GROUP_1h9817-_Oeurqv-D_aozBJ7Ibo5K"
            },
            "style": {
                "active": {
                    "stroke": "rgb(95, 149, 255)",
                    "lineWidth": 1
                },
                "selected": {
                    "stroke": "rgb(95, 149, 255)",
                    "lineWidth": 2,
                    "shadowColor": "rgb(95, 149, 255)",
                    "shadowBlur": 10,
                    "text-shape": {
                        "fontWeight": 500
                    }
                },
                "highlight": {
                    "stroke": "rgb(95, 149, 255)",
                    "lineWidth": 2,
                    "text-shape": {
                        "fontWeight": 500
                    }
                },
                "inactive": {
                    "stroke": "rgb(234, 234, 234)",
                    "lineWidth": 1
                },
                "disable": {
                    "stroke": "rgb(245, 245, 245)",
                    "lineWidth": 1
                },
                "stroke": "#999999",
                "lineWidth": 3,
                "lineDash": [],
                "startArrow": false,
                "endArrow": {
                    "path": "M 10,0 L 20,-5 L 20,5 Z",
                    "d": 5,
                    "fill": "#999999",
                    "stroke": "#999999",
                    "opacity": 1,
                    "lineWidth": 3
                }
            },
            "type": "custom-edge",
            "label": "",
            "labelCfg": {
                "position": "center",
                "refX": 0,
                "style": {
                    "fontSize": 16
                }
            },
            "startPoint": {
                "x": 703,
                "y": 149.5,
                "anchorIndex": 3,
                "id": "70|||15"
            },
            "endPoint": {
                "x": 703,
                "y": 175.5,
                "anchorIndex": 1,
                "id": "70|||18"
            },
            "targetAnchor": 1
        },
        {
            "id": "23u5i6-RkujBjf5gpZ-_I52LOeOX",
            "source": "COMPONENT_GROUP_1h9817-_Oeurqv-D_aozBJ7Ibo5K",
            "sourceAnchor": 3,
            "target": "HTTP_18hmqr-MgvID6gCEiNyj_V886nwf",
            "lineAppendWidth": 20,
            "attrs": {
                "start": "COMPONENT_GROUP_1h9817-_Oeurqv-D_aozBJ7Ibo5K",
                "end": "HTTP_18hmqr-MgvID6gCEiNyj_V886nwf"
            },
            "style": {
                "active": {
                    "stroke": "rgb(95, 149, 255)",
                    "lineWidth": 1
                },
                "selected": {
                    "stroke": "rgb(95, 149, 255)",
                    "lineWidth": 2,
                    "shadowColor": "rgb(95, 149, 255)",
                    "shadowBlur": 10,
                    "text-shape": {
                        "fontWeight": 500
                    }
                },
                "highlight": {
                    "stroke": "rgb(95, 149, 255)",
                    "lineWidth": 2,
                    "text-shape": {
                        "fontWeight": 500
                    }
                },
                "inactive": {
                    "stroke": "rgb(234, 234, 234)",
                    "lineWidth": 1
                },
                "disable": {
                    "stroke": "rgb(245, 245, 245)",
                    "lineWidth": 1
                },
                "stroke": "#999999",
                "lineWidth": 3,
                "lineDash": [],
                "startArrow": false,
                "endArrow": {
                    "path": "M 10,0 L 20,-5 L 20,5 Z",
                    "d": 5,
                    "fill": "#999999",
                    "stroke": "#999999",
                    "opacity": 1,
                    "lineWidth": 3
                }
            },
            "type": "custom-edge",
            "label": "",
            "labelCfg": {
                "position": "center",
                "refX": 0,
                "style": {
                    "fontSize": 16
                }
            },
            "startPoint": {
                "x": 702,
                "y": 288.5,
                "anchorIndex": 3,
                "id": "70|||29"
            },
            "endPoint": {
                "x": 702,
                "y": 319.5,
                "anchorIndex": 1,
                "id": "70|||32"
            },
            "targetAnchor": 1
        }
    ],
    "combos": [],
    "relations": [
        {
            "type": "START",
            "id": "START_1wcean-tX_avfhyau69lw-5EnuRk",
            "children": []
        },
        {
            "type": "HTTP",
            "id": "HTTP_1alpqk-R9wG8ia2E5Ziawn-YswhE",
            "children": []
        },
        {
            "type": "COMPONENT_GROUP",
            "id": "COMPONENT_GROUP_1h9817-_Oeurqv-D_aozBJ7Ibo5K",
            "children": [],
            "reference_id": "component_group_id:lVxTnJac1t8ukX-Zx2xKA"
        },
        {
            "type": "HTTP",
            "id": "HTTP_18hmqr-MgvID6gCEiNyj_V886nwf",
            "children": []
        },
        {
            "type": "HTTP",
            "id": "HTTP_1cwy3g-_kU0yCEAOCj6nZEgiLjgF",
            "children": []
        },
        {
            "type": "HTTP",
            "id": "HTTP_wz9rhj-U5VOM-BAlQ1wLtysjR4nj",
            "children": []
        },
        {
            "type": "HTTP",
            "id": "HTTP_14xfyc-wXqpjjnFlfym_EwyC_FP-",
            "children": []
        },
        {
            "type": "END",
            "id": "END_1673bg-s1-zdYbJyp7mxDGcQUdq6",
            "children": []
        }
    ]
}`
	_ = jsonx.UnmarshalFromString(reqStr, &current)

	v, _ := view.ViewApiCase(
		&pb.ViewApiCaseReq{
			ProjectId: current.GetProjectId(),
			CaseId:    current.GetCaseId(),
			Version:   "",
		},
	)

	ac, _ := model.CheckApiCaseByCaseId(
		view.ctx, view.svcCtx.ApiCaseModel, current.GetProjectId(), current.GetCaseId(), "",
	)

	var relations []*pb.Relation
	_ = protobuf.UnmarshalJSONWithMessagesFromString(ac.Structure, &relations)

	origin = &pb.ModifyApiCaseReq{
		ProjectId:     v.GetCase().GetProjectId(),
		CategoryId:    v.GetCase().GetCategoryId(),
		CaseId:        v.GetCase().GetCaseId(),
		Name:          v.GetCase().GetName(),
		Description:   v.GetCase().GetDescription(),
		AccountConfig: v.GetCase().GetAccountConfig(),
		Nodes:         v.GetCase().GetNodes(),
		Edges:         v.GetCase().GetEdges(),
		Combos:        v.GetCase().GetCombos(),
		Relations:     relations,
	}

	m.Run()
}

func textDiff() {
	b1, _ := jsonx.Marshal(origin)
	b2, _ := jsonx.Marshal(current)

	_ = textDiffer.DiffMain(string(b1), string(b2), false)
}

func structDiff() {
	_, _ = structDiffer.Diff(origin, current)
}

func TestStructDiff(t *testing.T) {
	cl, err := structDiffer.Diff(origin, current)
	if err != nil {
		t.Fatal(err)
	}
	t.Log(cl)
}

func BenchmarkTextDiff(b *testing.B) {
	for i := 0; i < b.N; i++ {
		textDiff()
	}
}

func BenchmarkStructDiff(b *testing.B) {
	for i := 0; i < b.N; i++ {
		structDiff()
	}
}
