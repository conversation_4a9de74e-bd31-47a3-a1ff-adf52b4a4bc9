package apicaseservicelogic

import (
	"context"
	"math"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sqlbuilder/rpc"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type SearchApiCaseReferenceLogic struct {
	*BaseLogic
}

func NewSearchApiCaseReferenceLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SearchApiCaseReferenceLogic {
	return &SearchApiCaseReferenceLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// SearchApiCaseReference 搜索API用例引用详情
func (l *SearchApiCaseReferenceLogic) SearchApiCaseReference(in *pb.SearchApiCaseReferenceReq) (resp *pb.SearchApiCaseReferenceResp, err error) {
	resp = &pb.SearchApiCaseReferenceResp{}

	// validate the project_id in req
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, in.GetProjectId()); err != nil {
		return nil, err
	}

	// validate the case_id in req
	if _, err = model.CheckApiCaseByCaseId(l.ctx, l.svcCtx.ApiCaseModel, in.GetProjectId(), in.GetCaseId(), ""); err != nil {
		return nil, err
	}

	selectBuilder, countBuilder := l.svcCtx.ApiCaseModel.GenerateSearchApiCaseReferenceSqlBuilder2(model.SearchApiCaseReferenceReq{
		ProjectId:  in.GetProjectId(),
		CaseId:     in.GetCaseId(),
		Condition:  in.GetCondition(),
		Pagination: in.GetPagination(),
		Sort:       rpc.ConvertSortFields(in.GetSort()),
	})

	count, err := l.svcCtx.ApiCaseModel.FindCountApiCaseReference(l.ctx, countBuilder)
	if err != nil {
		return nil, errors.Wrapf(errorx.Err(errorx.DBError, err.Error()), "failed to count reference data of api case with project_id[%s] and case_id[%s], error: %+v", in.GetProjectId(), in.GetCaseId(), err)
	}
	resp.TotalCount = uint64(count)
	resp.TotalPage = 1

	apiCases, err := l.svcCtx.ApiCaseModel.FindApiCaseReference(l.ctx, selectBuilder)
	if err != nil {
		return nil, errors.Wrapf(errorx.Err(errorx.DBError, err.Error()), "failed to find reference data of api case with project_id[%s] and case_id[%s], error: %+v", in.GetProjectId(), in.GetCaseId(), err)
	}

	resp.Items = make([]*pb.SearchApiCaseReferenceItem, 0, len(apiCases))
	for _, apiCase := range apiCases {
		item := &pb.SearchApiCaseReferenceItem{}
		if err = utils.Copy(item, apiCase, l.converters...); err != nil {
			return nil, errors.Wrapf(errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy reference data of api case[%+v] to response, error: %+v", apiCase, err)
		}

		resp.Items = append(resp.Items, item)
	}

	pagination := in.GetPagination()
	if pagination != nil {
		resp.CurrentPage = pagination.GetCurrentPage()
		resp.PageSize = pagination.GetPageSize()
		resp.TotalPage = uint64(math.Ceil(float64(resp.TotalCount) / float64(resp.PageSize)))
	}

	return resp, nil
}
