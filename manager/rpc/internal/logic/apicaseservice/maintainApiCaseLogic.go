package apicaseservicelogic

import (
	"context"
	"database/sql"
	"fmt"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/caller"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/logic/fsm"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type MaintainApiCaseLogic struct {
	*BaseLogic
}

func NewMaintainApiCaseLogic(ctx context.Context, svcCtx *svc.ServiceContext) *MaintainApiCaseLogic {
	return &MaintainApiCaseLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// MaintainApiCase 维护API用例
func (l *MaintainApiCaseLogic) MaintainApiCase(in *pb.MaintainApiCaseReq) (out *pb.MaintainApiCaseResp, err error) {
	// validate the project_id in req
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, in.GetProjectId()); err != nil {
		return nil, err
	}

	key := fmt.Sprintf("%s:%s:%s", common.ConstLockApiCaseProjectIdCaseIdPrefix, in.GetProjectId(), in.GetCaseId())
	fn := func() error {
		// validate the case_id in req
		origin, err := model.CheckApiCaseByCaseId(
			l.ctx, l.svcCtx.ApiCaseModel, in.GetProjectId(), in.GetCaseId(), "",
		)
		if err != nil {
			return err
		}

		if origin.State != string(common.ConstResourceStatePublished) {
			return errorx.Errorf(
				errorx.ProhibitedBehavior,
				"the status of api case must be 'PUBLISHED' before it can be changed to 'TO_BE_MAINTAINED', project_id: %s, case_id: %s, state: %s",
				in.GetProjectId(), in.GetCaseId(), origin.State,
			)
		}

		if in.GetMaintainedBy() != "" {
			origin.MaintainedBy = sql.NullString{
				String: in.GetMaintainedBy(),
				Valid:  true,
			}
		}
		if !origin.MaintainedBy.Valid || origin.MaintainedBy.String == "" {
			return errorx.Errorf(
				errorx.ProhibitedBehavior,
				"the maintainer of api case must be set before it can be changed to 'TO_BE_MAINTAINED', project_id: %s, case_id: %s",
				in.GetProjectId(), in.GetCaseId(),
			)
		}

		maintainedBy := origin.MaintainedBy.String
		user, err := l.getUserInfoByAccount(maintainedBy)
		if err != nil {
			return err
		} else if !user.GetEnabled() {
			return errorx.Errorf(
				errorx.ProhibitedBehavior,
				"cannot assign a disabled user as a maintainer, project_id: %s, case_id: %s, maintained_by: %s",
				in.GetProjectId(), in.GetCaseId(), maintainedBy,
			)
		}

		event := common.ConstReviewResourceEventReturnToMaintenanceAfterPublished
		if err = fsm.NewResourceFSM(
			l.ctx, l.svcCtx, &ResourceOfApiCase{
				ApiCase: origin,
				Event:   event,
				Emails:  []string{user.GetEmail()},
			}, nil,
		).TransitionWithoutLock(); err != nil {
			return errors.Wrapf(
				errorx.Err(errorx.FSMError, err.Error()),
				"got an error regarding state transition about api case, project_id: %s, resource_id: %s, event: %s, error: %+v",
				in.GetProjectId(), in.GetCaseId(), event, err,
			)
		}

		out = &pb.MaintainApiCaseResp{Case: &pb.ApiCase{}}
		if err = utils.Copy(out.Case, origin, l.converters...); err != nil {
			return errors.Wrapf(
				errorx.Err(errorx.CopyToStructFailure, err.Error()),
				"failed to copy api case[%+v] to response, error: %+v",
				origin, err,
			)
		}

		return nil
	}
	err = caller.LockDo(l.svcCtx.Redis, key, fn)

	return out, err
}
