package apicaseservicelogic

import (
	"context"
	"database/sql"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"
	"github.com/zeromicro/go-zero/core/stores/sqlx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
	qetutils "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"
	userpb "gitlab.ttyuyin.com/TestDevelopment/qet-backend-middleware/user/rpc/pb"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/utils"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/logic/fsm"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type CreateApiCaseLogic struct {
	*BaseLogic
}

func NewCreateApiCaseLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CreateApiCaseLogic {
	return &CreateApiCaseLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// CreateApiCase 创建API用例
func (l *CreateApiCaseLogic) CreateApiCase(in *pb.CreateApiCaseReq) (resp *pb.CreateApiCaseResp, err error) {
	// validate the project_id in req
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, in.GetProjectId()); err != nil {
		return nil, err
	}

	var c *model.Category
	// validate the category_id in req
	if c, err = model.CheckCategoryByCategoryId(
		l.ctx, l.svcCtx.CategoryModel, in.GetProjectId(), common.ConstCategoryTreeTypeApiCase, in.GetCategoryId(),
	); err != nil {
		return nil, err
	} else if c.CategoryType != common.ConstCategoryTypeDirectory {
		return nil, errorx.Errorf(
			errorx.ProhibitedBehavior,
			"the type of parent category[%s] does not support creation of sub category",
			c.CategoryType,
		)
	}

	// validate the circular reference exists
	if err = logic.CheckCircularReference(
		l.ctx, l.svcCtx.ComponentGroupModel, in.GetProjectId(), in.GetRelations(),
	); err != nil {
		return nil, err
	}

	apiCase, err := l.create(in)
	if err != nil {
		return nil, err
	}

	resp = &pb.CreateApiCaseResp{Case: &pb.ApiCase{}}
	if err = qetutils.Copy(resp.Case, apiCase, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy api case[%+v] to response, error: %+v",
			apiCase, err,
		)
	}

	return resp, nil
}

// in order to reduce cyclomatic complexity of CreateApiCaseLogic.CreateApiCase
func (l *CreateApiCaseLogic) create(req *pb.CreateApiCaseReq) (*model.ApiCase, error) {
	caseId, err := l.generateCaseId(req.GetProjectId())
	if err != nil {
		return nil, err
	}
	version := utils.GenVersion()

	// 由于`protojson`只能序列化`[]proto.Message`，而`tags`字段类型为`[]string`，Go Struct跟PB没有大的区别，因此直接使用`jsonx`暂代`protojson`
	tags := jsonx.MarshalToStringIgnoreError(req.GetTags())

	var user *userpb.UserInfo
	maintainedBy := req.GetMaintainedBy()
	if maintainedBy != "" {
		if user, err = l.getUserInfoByAccount(maintainedBy); err != nil {
			return nil, err
		} else if !user.GetEnabled() {
			return nil, errorx.Errorf(
				errorx.ProhibitedBehavior,
				"cannot assign a disabled user as a maintainer of api case, project_id: %s, maintained_by: %s",
				req.GetProjectId(), maintainedBy,
			)
		}
	}

	// [2024-04-10] delete this rule:
	//if maintainedBy == "" {
	//	// if the maintainer is not set, the creator is set as the maintainer
	//	maintainedBy = l.currentUser.Account
	//}

	apiCase := &model.ApiCase{
		ProjectId:  req.GetProjectId(),
		CategoryId: req.GetCategoryId(),
		CaseId:     caseId,
		Name:       req.GetName(),
		Description: sql.NullString{
			String: req.GetDescription(),
			Valid:  req.GetDescription() != "",
		},
		Priority: req.GetPriority(),
		Tags: sql.NullString{
			String: tags,
			Valid:  tags != "",
		},
		State:         string(common.ConstResourceStateNew),
		AccountConfig: protobuf.MarshalJSONToStringIgnoreError(req.GetAccountConfig()),
		Version:       version,
		Structure:     protobuf.MarshalJSONWithMessagesToStringIgnoreError(req.GetRelations()),
		Latest:        int64(constants.IsLatestVersion),
		MaintainedBy:  sql.NullString{}, // 为了避免出现状态为「新」且维护者非空的情况，维护者由`fsm`进行修改
		CreatedBy:     l.currentUser.Account,
		UpdatedBy:     l.currentUser.Account,
	}

	// create api case in a transaction
	if err = l.svcCtx.ApiCaseModel.Trans(
		l.ctx, func(context context.Context, session sqlx.Session) error {
			if _, err := l.svcCtx.ApiCaseModel.InsertTX(context, session, apiCase); err != nil {
				return errors.Wrapf(
					errorx.Err(errorx.DBError, err.Error()), "failed to insert table[%s] with values[%+v], error: %+v",
					l.svcCtx.ApiCaseModel.Table(), apiCase, err,
				)
			}

			// create the component group reference relationship
			if err := l.createReferenceRelationship(
				context, session, createReferenceRelationshipInternalReq{
					ProjectId: apiCase.ProjectId,
					CaseId:    apiCase.CaseId,
					Version:   apiCase.Version,
					Relations: req.GetRelations(),
				},
			); err != nil {
				return err
			}

			// create the elements and components of the component group
			if err := l.createElementAndComponent(
				context, session, createElementAndComponentInternalReq{
					ProjectId: apiCase.ProjectId,
					CaseId:    apiCase.CaseId,
					Version:   apiCase.Version,
					Nodes:     req.GetNodes(),
					Edges:     req.GetEdges(),
					Combos:    req.GetCombos(),
				},
			); err != nil {
				return err
			}

			// create the new tag and tag reference of api case
			if err := l.createTagLogic.CreateTagAndReferenceForInternal(
				context, session, types.CreateOrUpdateTagReference{
					ProjectId:        apiCase.ProjectId,
					ReferenceType:    common.ConstReferenceTypeApiCase,
					ReferenceId:      apiCase.CaseId,
					ReferenceVersion: apiCase.Version,
					Tags:             req.GetTags(),
				},
			); err != nil {
				return err
			}

			return nil
		},
	); err != nil {
		return nil, err
	}

	// [2024-04-10] add a new rule as follows:
	if user != nil {
		// get the latest version of api case again
		// 注意：这里需要重新获取最新版本的数据，因为需要获取新的`id`
		apiCase, err = model.CheckApiCaseByCaseId(l.ctx, l.svcCtx.ApiCaseModel, req.GetProjectId(), caseId, "")
		if err != nil {
			return nil, err
		}

		apiCase.MaintainedBy = sql.NullString{
			String: maintainedBy,
			Valid:  maintainedBy != "",
		}

		event := common.ConstReviewResourceEventAssignedToTheResponsiblePerson
		if err = fsm.NewResourceFSM(
			l.ctx, l.svcCtx, &ResourceOfApiCase{
				ApiCase: apiCase,
				Event:   event,
				Emails:  []string{user.GetEmail()},
			}, nil,
		).TransitionWithoutLock(); err != nil {
			return nil, errors.Wrapf(
				errorx.Err(errorx.FSMError, err.Error()),
				"got an error regarding state transition after creating an api case, project_id: %s, resource_id: %s, event: %s, error: %+v",
				req.GetProjectId(), apiCase.CaseId, event, err,
			)
		}
	}

	return apiCase, nil
}
