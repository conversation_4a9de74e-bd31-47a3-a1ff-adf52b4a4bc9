package apicaseservicelogic

import (
	"context"
	"fmt"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/stores/sqlx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/logic/fsm"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

var _ fsm.Resource = (*ResourceOfApiCase)(nil)

type createReferenceRelationshipInternalReq struct {
	ProjectId string         `json:"project_id"`
	CaseId    string         `json:"case_id"`
	Version   string         `json:"version"`
	Relations []*pb.Relation `json:"relations"`
}

type createElementAndComponentInternalReq struct {
	ProjectId string      `json:"project_id"`
	CaseId    string      `json:"case_id"`
	Version   string      `json:"version"`
	Nodes     []*pb.Node  `json:"nodes"`
	Edges     []*pb.Edge  `json:"edges"`
	Combos    []*pb.Combo `json:"combos"`
}

type searchApiCaseInternalReq struct {
	*pb.SearchApiCaseReq

	DrillDown bool `json:"drill_down"`
}

type ResourceOfApiCase struct {
	*model.ApiCase

	Event  common.ReviewResourceEvent `json:"event"`
	Emails []string                   `json:"emails"`
	Result common.ReviewStatus        `json:"result"`
	Remark string                     `json:"remark"`
}

func (r *ResourceOfApiCase) GenLockKey() string {
	return fmt.Sprintf("%s:%s:%s", common.ConstLockApiCaseProjectIdCaseIdPrefix, r.ProjectID(), r.ResourceID())
}

func (r *ResourceOfApiCase) SetResourceState(state string) {
	r.ApiCase.State = state
}

func (r *ResourceOfApiCase) SetResourceUpdatedBy(updatedBy string) {
	r.ApiCase.UpdatedBy = updatedBy
}

func (r *ResourceOfApiCase) Refresh(ctx context.Context, svcCtx *svc.ServiceContext) error {
	apiCase, err := model.CheckApiCaseByCaseId(ctx, svcCtx.ApiCaseModel, r.ProjectId, r.CaseId, "")
	if err != nil {
		return err
	}

	r.ApiCase = apiCase
	return nil
}

func (r *ResourceOfApiCase) Update(ctx context.Context, svcCtx *svc.ServiceContext, session sqlx.Session) error {
	if _, err := svcCtx.ApiCaseModel.UpdateTX(ctx, session, r.ApiCase); err != nil {
		return errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()),
			"failed to update table[%s] with values[%+v], error: %+v",
			svcCtx.ApiCaseModel.Table(), r.ApiCase, err,
		)
	}

	return nil
}

func (r *ResourceOfApiCase) ReviewEvent() common.ReviewResourceEvent {
	return r.Event
}

func (r *ResourceOfApiCase) Receivers() []string {
	return r.Emails
}

func (r *ResourceOfApiCase) ReviewResult() common.ReviewStatus {
	return r.Result
}

func (r *ResourceOfApiCase) ReviewRemark() string {
	return r.Remark
}
