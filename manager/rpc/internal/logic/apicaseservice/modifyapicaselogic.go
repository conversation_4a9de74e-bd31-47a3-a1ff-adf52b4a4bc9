package apicaseservicelogic

import (
	"context"
	"database/sql"
	"fmt"
	"sort"

	"github.com/pkg/errors"
	"github.com/sergi/go-diff/diffmatchpatch"
	"github.com/zeromicro/go-zero/core/jsonx"
	"github.com/zeromicro/go-zero/core/stores/sqlx"

	qetconstants "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/redislock"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"
	userpb "gitlab.ttyuyin.com/TestDevelopment/qet-backend-middleware/user/rpc/pb"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/logic/fsm"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type ModifyApiCaseLogic struct {
	*BaseLogic
}

func NewModifyApiCaseLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ModifyApiCaseLogic {
	return &ModifyApiCaseLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// ModifyApiCase 编辑API用例
func (l *ModifyApiCaseLogic) ModifyApiCase(in *pb.ModifyApiCaseReq) (resp *pb.ModifyApiCaseResp, err error) {
	// validate the project_id in req
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, in.GetProjectId()); err != nil {
		return nil, err
	}

	var c *model.Category
	// validate the category_id in req
	if c, err = model.CheckCategoryByCategoryId(
		l.ctx, l.svcCtx.CategoryModel, in.GetProjectId(), common.ConstCategoryTreeTypeApiCase, in.GetCategoryId(),
	); err != nil {
		return nil, err
	} else if c.CategoryType != common.ConstCategoryTypeDirectory {
		return nil, errorx.Errorf(
			errorx.ProhibitedBehavior,
			"the type of parent category[%s] does not support creation of sub category",
			c.CategoryType,
		)
	}

	// validate the case_id in req
	origin, err := model.CheckApiCaseByCaseId(l.ctx, l.svcCtx.ApiCaseModel, in.GetProjectId(), in.GetCaseId(), "")
	if err != nil {
		return nil, err
	}

	// validate the api case data is the same
	if change, err := l.hasChange(in, origin); err != nil {
		return nil, err
	} else if !change.Whole {
		l.Logger.Infof(
			"there is no change with api case, project_id: %s, case_id: %s", in.GetProjectId(), in.GetCaseId(),
		)

		resp = &pb.ModifyApiCaseResp{Case: &pb.ApiCase{}}
		if err = utils.Copy(resp.Case, origin, l.converters...); err != nil {
			return nil, errors.Wrapf(
				errorx.Err(errorx.CopyToStructFailure, err.Error()),
				"failed to copy api case[%+v] to response, error: %+v",
				origin, err,
			)
		}
		return resp, nil
	}

	// validate the maintained_by in req
	if in.GetMaintainedBy() == "" && origin.MaintainedBy.Valid && origin.MaintainedBy.String != "" {
		return nil, errorx.Errorf(
			errorx.ProhibitedBehavior,
			"cannot change the maintainer of api case to be empty, source: %s, target: %s",
			origin.MaintainedBy.String, in.GetMaintainedBy(),
		)
	}

	// validate the circular reference exists
	if err = logic.CheckCircularReference(
		l.ctx, l.svcCtx.ComponentGroupModel, in.GetProjectId(), in.GetRelations(),
	); err != nil {
		return nil, err
	}

	// acquire redis lock
	key := fmt.Sprintf("%s:%s:%s", common.ConstLockApiCaseProjectIdCaseIdPrefix, in.GetProjectId(), in.GetCaseId())
	lock, err := redislock.NewRedisLockAndAcquire(l.svcCtx.Redis, key, redislock.WithExpire(common.ConstLockExpireTime))
	if err != nil {
		return nil, err
	}
	defer func() {
		// release redis lock
		e := lock.Release()
		if e != nil {
			if re, ok := errorx.RootError(e); ok && re.Code() != errorx.ReleaseRedisLockFailure && err == nil {
				err = e
			}
		}
	}()

	apiCase, err := l.modify(in)
	if err != nil {
		return nil, err
	}

	resp = &pb.ModifyApiCaseResp{Case: &pb.ApiCase{}}
	if err = utils.Copy(resp.Case, apiCase, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy api case[%+v] to response, error: %+v",
			apiCase, err,
		)
	}

	return resp, nil
}

func (l *ModifyApiCaseLogic) hasChange(req *pb.ModifyApiCaseReq, ac *model.ApiCase) (types.ChangeFlag, error) {
	if req == nil || ac == nil {
		return types.ChangeFlag{}, errorx.Err(
			errorx.InternalError,
			"failed to check for changes in api case because source object or target object is null",
		)
	}

	ch := types.ChangeFlag{
		Name:        ac.Name != req.GetName(),
		Description: ac.Description.String != req.GetDescription(),
	}

	nodes, edges, combos, err := l.getElements(ac.ProjectId, ac.CaseId, ac.Version)
	if err != nil {
		return ch, err
	}

	var tags []string
	if ac.Tags.Valid && ac.Tags.String != "" {
		// 由于`protojson`只能反序列化到`[]proto.Message`，而`tags`字段类型为`[]string`，Go Struct跟PB没有大的区别，因此直接使用`jsonx`暂代`protojson`
		if err = jsonx.UnmarshalFromString(ac.Tags.String, &tags); err != nil {
			return ch, errors.Wrapf(
				errorx.Err(errorx.SerializationError, err.Error()),
				"failed to unmarshal api case tags[%s], error: %+v",
				ac.Tags.String, err,
			)
		}
	}

	var accountConfig pb.AccountConfig
	if err = protobuf.UnmarshalJSONFromString(ac.AccountConfig, &accountConfig); err != nil {
		return ch, errors.Wrapf(
			errorx.Err(errorx.SerializationError, err.Error()),
			"failed to unmarshal api case account config[%s], error: %+v",
			ac.AccountConfig, err,
		)
	}

	// `pb.Relation`中含有二维数组，Go Struct跟PB有区别，因此这里必须使用兼容函数进行反序列化
	var relations []*pb.Relation
	err = protobuf.UnmarshalJSONWithMessagesFromString(ac.Structure, &relations)
	if err != nil {
		return ch, errors.Wrapf(
			errorx.Err(errorx.SerializationError, err.Error()),
			"failed to unmarshal api case structure[%s], error: %+v",
			ac.Structure, err,
		)
	}

	sort.SliceStable(
		nodes, func(i, j int) bool {
			return nodes[i].Id < nodes[j].Id
		},
	)
	sort.SliceStable(
		edges, func(i, j int) bool {
			return edges[i].Id < edges[j].Id
		},
	)
	sort.SliceStable(
		combos, func(i, j int) bool {
			return combos[i].Id < combos[j].Id
		},
	)

	origin := &pb.ModifyApiCaseReq{
		ProjectId:     ac.ProjectId,
		CategoryId:    ac.CategoryId,
		CaseId:        ac.CaseId,
		Name:          ac.Name,
		Description:   ac.Description.String,
		Priority:      ac.Priority,
		Tags:          tags,
		AccountConfig: &accountConfig,
		Nodes:         nodes,
		Edges:         edges,
		Combos:        combos,
		Relations:     relations,
		MaintainedBy:  ac.MaintainedBy.String,
	}

	if ns := req.GetNodes(); ns != nil {
		sort.SliceStable(
			ns, func(i, j int) bool {
				return ns[i].Id < ns[j].Id
			},
		)
	}

	if es := req.GetEdges(); es != nil {
		sort.SliceStable(
			es, func(i, j int) bool {
				return es[i].Id < es[j].Id
			},
		)
	}

	if cs := req.GetCombos(); cs != nil {
		sort.SliceStable(
			cs, func(i, j int) bool {
				return cs[i].Id < cs[j].Id
			},
		)
	}

	dmp := diffmatchpatch.New()
	ds := dmp.DiffMain(
		protobuf.MarshalJSONToStringIgnoreError(origin), protobuf.MarshalJSONToStringIgnoreError(req), false,
	)
	if len(ds) != 0 {
		for _, d := range ds {
			if d.Type != diffmatchpatch.DiffEqual {
				ch.Whole = true
				return ch, nil
			}
		}
	}

	return ch, nil
}

// in order to reduce cyclomatic complexity of ModifyApiCaseLogic.ModifyApiCase
func (l *ModifyApiCaseLogic) modify(req *pb.ModifyApiCaseReq) (*model.ApiCase, error) {
	// get the latest version of api case again
	// 注意：这里需要重新获取最新版本的数据，因为当高并发的时候，上锁前拿到的可能已经不是最新版本的数据了
	origin, err := model.CheckApiCaseByCaseId(l.ctx, l.svcCtx.ApiCaseModel, req.GetProjectId(), req.GetCaseId(), "")
	if err != nil {
		return nil, err
	}

	version, err := l.generateVersion(origin.ProjectId, origin.CaseId)
	if err != nil {
		return nil, err
	}

	// 由于`protojson`只能序列化`[]proto.Message`，而`tags`字段类型为`[]string`，Go Struct跟PB没有大的区别，因此直接使用`jsonx`暂代`protojson`
	tags := jsonx.MarshalToStringIgnoreError(req.GetTags())

	var user *userpb.UserInfo
	maintainedBy := req.GetMaintainedBy()
	if maintainedBy != "" {
		if user, err = l.getUserInfoByAccount(maintainedBy); err != nil {
			return nil, err
		} else if !user.GetEnabled() {
			return nil, errorx.Errorf(
				errorx.ProhibitedBehavior,
				"cannot assign a disabled user as a maintainer of api case, project_id: %s, case_id: %s, maintained_by: %s",
				req.GetProjectId(), req.GetCaseId(), maintainedBy,
			)
		}
	}

	apiCase := &model.ApiCase{
		// 由于API用例有版本概念，`apiCase`对象用于创建，这里不能沿用`origin.Id`
		ProjectId:  origin.ProjectId,
		CategoryId: req.GetCategoryId(),
		CaseId:     origin.CaseId,
		Name:       req.GetName(),
		Description: sql.NullString{
			String: req.GetDescription(),
			Valid:  req.GetDescription() != "",
		},
		Priority: req.GetPriority(),
		Tags: sql.NullString{
			String: tags,
			Valid:  tags != "",
		},
		State:         origin.State, // 状态由`fsm`进行修改
		AccountConfig: protobuf.MarshalJSONToStringIgnoreError(req.GetAccountConfig()),
		Version:       version,
		Structure:     protobuf.MarshalJSONWithMessagesToStringIgnoreError(req.GetRelations()),
		Latest:        int64(qetconstants.IsLatestVersion),
		MaintainedBy: sql.NullString{
			String: maintainedBy,
			Valid:  maintainedBy != "",
		},
		CreatedBy: origin.CreatedBy,
		UpdatedBy: l.currentUser.Account,
		CreatedAt: origin.CreatedAt,
	}

	// update api case in a transaction
	if err = l.svcCtx.ApiCaseModel.Trans(
		l.ctx, func(context context.Context, session sqlx.Session) error {
			origin.Latest = int64(qetconstants.IsNotLatestVersion)

			if _, err := l.svcCtx.ApiCaseModel.UpdateAllToNotLatest(
				context, session, origin.ProjectId, origin.CaseId,
			); err != nil {
				return errors.Wrapf(
					errorx.Err(errorx.DBError, err.Error()),
					"failed to update the latest field of api case with project_id[%s] and case_id[%s], error: %+v",
					origin.ProjectId, origin.CaseId, err,
				)
			}

			if _, err := l.svcCtx.ApiCaseModel.InsertTX(context, session, apiCase); err != nil {
				return errors.Wrapf(
					errorx.Err(errorx.DBError, err.Error()),
					"failed to insert table[%s] with values[%+v], error: %+v",
					l.svcCtx.ApiCaseModel.Table(), apiCase, err,
				)
			}

			// create the component group reference relationship
			if err := l.createReferenceRelationship(
				context, session, createReferenceRelationshipInternalReq{
					ProjectId: apiCase.ProjectId,
					CaseId:    apiCase.CaseId,
					Version:   apiCase.Version,
					Relations: req.GetRelations(),
				},
			); err != nil {
				return err
			}

			// create the elements and components of the component group
			if err := l.createElementAndComponent(
				context, session, createElementAndComponentInternalReq{
					ProjectId: apiCase.ProjectId,
					CaseId:    apiCase.CaseId,
					Version:   apiCase.Version,
					Nodes:     req.GetNodes(),
					Edges:     req.GetEdges(),
					Combos:    req.GetCombos(),
				},
			); err != nil {
				return err
			}

			// create the new tag and tag reference of api case
			if err := l.createTagLogic.CreateTagAndReferenceForInternal(
				context, session, types.CreateOrUpdateTagReference{
					ProjectId:        apiCase.ProjectId,
					ReferenceType:    common.ConstReferenceTypeApiCase,
					ReferenceId:      apiCase.CaseId,
					ReferenceVersion: version,
					Tags:             req.GetTags(),
				},
			); err != nil {
				return err
			}

			return nil
		},
	); err != nil {
		return nil, err
	}

	// only transition when setting the maintainer fot the first time
	if origin.State == string(common.ConstResourceStateNew) &&
		(!origin.MaintainedBy.Valid || origin.MaintainedBy.String == "") && maintainedBy != "" && user != nil {
		// get the latest version of api case again
		// 注意：这里需要重新获取最新版本的数据，因为需要获取新的`id`
		apiCase, err = model.CheckApiCaseByCaseId(l.ctx, l.svcCtx.ApiCaseModel, req.GetProjectId(), req.GetCaseId(), "")
		if err != nil {
			return nil, err
		}

		event := common.ConstReviewResourceEventAssignedToTheResponsiblePerson
		if err = fsm.NewResourceFSM(
			l.ctx, l.svcCtx, &ResourceOfApiCase{
				ApiCase: apiCase,
				Event:   event,
				Emails:  []string{user.GetEmail()},
			}, nil,
		).TransitionWithoutLock(); err != nil {
			return nil, errors.Wrapf(
				errorx.Err(errorx.FSMError, err.Error()),
				"got an error regarding state transition about api case, project_id: %s, resource_id: %s, event: %s, error: %+v",
				req.GetProjectId(), req.GetCaseId(), event, err,
			)
		}
	}

	return apiCase, nil
}

func (l *ModifyApiCaseLogic) UpdateReferenceByApiCase(ur types.UpdateReference, ch types.ChangeFlag) (err error) {
	// acquire redis lock
	key := fmt.Sprintf("%s:%s:%s", common.ConstLockApiCaseProjectIdCaseIdPrefix, ur.ProjectId, ur.ReferenceId)
	lock, err := redislock.NewRedisLockAndAcquire(
		l.svcCtx.Redis, key, redislock.WithExpire(common.ConstLockExpireTime),
		redislock.WithTimeout(common.ConstAcquireLockTimeout),
	)
	if err != nil {
		return err
	}
	defer func() {
		if err != nil {
			l.Logger.Warnf(
				"error occurred while updating the reference, project_id: %s, reference_type: %s, reference_id: %s, reference_version: %s, component_group_id: %s, error: \n%+v",
				ur.ProjectId, ur.ReferenceType, ur.ReferenceId, ur.ReferenceVersion, ur.ComponentGroupId, err,
			)
		}
		// release redis lock
		e := lock.Release()
		if e != nil {
			l.Logger.Warn(e)
			if re, ok := errorx.RootError(e); ok && re.Code() != errorx.ReleaseRedisLockFailure && err == nil {
				err = e
			}
		}
	}()

	ac, err := l.svcCtx.ApiCaseModel.FindOneByProjectIdCaseIdVersion(
		l.ctx, ur.ProjectId, ur.ReferenceId, ur.ReferenceVersion,
	)
	if err != nil {
		if !errors.Is(err, model.ErrNotFound) {
			return errors.Wrapf(
				errorx.Err(errorx.DBError, err.Error()),
				"failed to find api case, project_id: %s, case_id: %s, version: %s, error: %+v", ur.ProjectId,
				ur.ReferenceId, ur.ReferenceVersion, err,
			)
		} else {
			return errors.WithStack(
				errorx.Err(
					errorx.NotExists, fmt.Sprintf(
						"api case doesn't exist, project_id: %s, case_id: %s, version: %s", ur.ProjectId,
						ur.ReferenceId, ur.ReferenceVersion,
					),
				),
			)
		}
	} else if qetconstants.LatestVersionFlag(ac.Latest) != qetconstants.IsLatestVersion {
		// 引用当前被编辑的组件组的API用例不是最新版本，则无需更新
		return nil
	}

	nodes, _, _, err := l.getElements(ur.ProjectId, ur.ReferenceId, ur.ReferenceVersion)
	if err != nil {
		return err
	}

	for _, node := range nodes {
		m := node.ComponentDataMapValue()

		if m == nil || node.ComponentType() != constants.REFERENCE {
			continue
		}

		v, ok := m[common.ConstFieldJSONNameReferenceId]
		if !ok || ur.ComponentGroupId != v {
			continue
		}

		ace, err := l.svcCtx.ApiCaseElementModel.FindOneByProjectIdCaseIdVersionElementId(
			l.ctx, ur.ProjectId, ur.ReferenceId, ur.ReferenceVersion, node.GetId(),
		)
		if err != nil {
			l.Logger.Warnf(
				"failed to find the api case element, project_id: %s, case_id: %s, version: %s, element_id: %s, error: %+v",
				ur.ProjectId, ur.ReferenceId, ur.ReferenceVersion, node.GetId(), err,
			)
			continue
		}

		if ch.Name {
			m[common.ConstFieldJSONNameName] = ur.Name
			m[common.ConstFieldJSONNameLabel] = ur.Name
			node.Label = ur.Name
		}
		if ch.Description {
			m[common.ConstFieldJSONNameDescription] = ur.Description
		}
		if ch.Imports {
			logic.UpdateReferenceImports(ur.Imports, m)
		}
		if ch.Exports {
			logic.UpdateReferenceExports(ur.Exports, m)
		}

		node.Data, err = protobuf.NewStruct(m)
		if err != nil {
			l.Logger.Warnf(
				"failed to new a *structpb.Struct with the data of api case element, project_id: %s, case_id: %s, version: %s, element_id: %s, error: %+v",
				ur.ProjectId, ur.ReferenceId, ur.ReferenceVersion, node.GetId(), err,
			)
			continue
		}

		ace.Data = protobuf.MarshalJSONToStringIgnoreError(node)
		if _, err = l.svcCtx.ApiCaseElementModel.UpdateTX(l.ctx, nil, ace); err != nil {
			l.Logger.Warnf(
				"failed to update the api case element, project_id: %s, case_id: %s, version: %s, element_id: %s, error: %+v",
				ace.ProjectId, ace.CaseId, ace.Version, ace.ElementId, err,
			)
			continue
		}
	}

	return nil
}
