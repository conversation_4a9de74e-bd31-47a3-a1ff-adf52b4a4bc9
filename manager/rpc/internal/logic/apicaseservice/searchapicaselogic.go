package apicaseservicelogic

import (
	"context"
	"math"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type SearchApiCaseLogic struct {
	*BaseLogic
}

func NewSearchApiCaseLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SearchApiCaseLogic {
	return &SearchApiCaseLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// SearchApiCase 搜索API用例
func (l *SearchApiCaseLogic) SearchApiCase(in *pb.SearchApiCaseReq) (resp *pb.SearchApiCaseResp, err error) {
	resp = &pb.SearchApiCaseResp{}

	// validate the project_id in req
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, in.GetProjectId()); err != nil {
		return nil, err
	}

	// validate the category_id in req
	if _, err = model.CheckCategoryByCategoryId(l.ctx, l.svcCtx.CategoryModel, in.GetProjectId(), common.ConstCategoryTreeTypeApiCase, in.GetCategoryId()); err != nil {
		return nil, err
	}

	selectBuilder, countBuilder := l.generateSearchSqlBuilder(searchApiCaseInternalReq{
		SearchApiCaseReq: in,
		DrillDown:        true,
	})

	count, err := l.svcCtx.ApiCaseModel.FindCount(l.ctx, countBuilder)
	if err != nil {
		return nil, errors.Wrapf(errorx.Err(errorx.DBError, err.Error()), "failed to count api case with project_id[%s] and category_id[%s], error: %+v", in.GetProjectId(), in.GetCategoryId(), err)
	}
	resp.TotalCount = uint64(count)
	resp.TotalPage = 1

	apiCases, err := l.svcCtx.ApiCaseModel.FindNoCacheByQuery(l.ctx, selectBuilder)
	if err != nil {
		return nil, errors.Wrapf(errorx.Err(errorx.DBError, err.Error()), "failed to find api case with project_id[%s] and category_id[%s], error: %+v", in.GetProjectId(), in.GetCategoryId(), err)
	}

	resp.Items = make([]*pb.ApiCase, 0, len(apiCases))
	for _, apiCase := range apiCases {
		item := &pb.ApiCase{}
		if err = utils.Copy(item, apiCase, l.converters...); err != nil {
			return nil, errors.Wrapf(errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy api case[%+v] to response, error: %+v", apiCase, err)
		}

		resp.Items = append(resp.Items, item)
	}

	pagination := in.GetPagination()
	if pagination != nil {
		resp.CurrentPage = pagination.GetCurrentPage()
		resp.PageSize = pagination.GetPageSize()
		resp.TotalPage = uint64(math.Ceil(float64(resp.TotalCount) / float64(resp.PageSize)))
	}

	return resp, nil
}
