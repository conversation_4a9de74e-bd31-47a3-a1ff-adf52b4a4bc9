package apicaseservicelogic

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type ViewApiCaseLogic struct {
	*BaseLogic
}

func NewViewApiCaseLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ViewApiCaseLogic {
	return &ViewApiCaseLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// ViewApiCase 查看API用例
func (l *ViewApiCaseLogic) ViewApiCase(in *pb.ViewApiCaseReq) (resp *pb.ViewApiCaseResp, err error) {
	// validate the project_id in req
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, in.GetProjectId()); err != nil {
		return nil, err
	}

	// validate the case_id in req
	apiCase, err := model.CheckApiCaseByCaseId(l.ctx, l.svcCtx.ApiCaseModel, in.GetProjectId(), in.GetCaseId(), in.GetVersion())
	if err != nil {
		return nil, err
	}

	resp = &pb.ViewApiCaseResp{Case: &pb.ApiCase{}}
	if err = utils.Copy(resp.Case, apiCase, l.converters...); err != nil {
		return nil, errors.Wrapf(errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy api case[%+v] to response, error: %+v", apiCase, err)
	}

	resp.Case.Nodes, resp.Case.Edges, resp.Case.Combos, err = l.getElements(in.GetProjectId(), in.GetCaseId(), apiCase.Version)
	if err != nil {
		return nil, err
	}

	return resp, nil
}
