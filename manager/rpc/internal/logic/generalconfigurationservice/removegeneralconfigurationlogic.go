package generalconfigurationservicelogic

import (
	"context"
	"fmt"

	"github.com/hashicorp/go-multierror"
	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/mr"
	"github.com/zeromicro/go-zero/core/stores/sqlx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/redislock"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type RemoveGeneralConfigurationLogic struct {
	*BaseLogic
}

func NewRemoveGeneralConfigurationLogic(
	ctx context.Context, svcCtx *svc.ServiceContext,
) *RemoveGeneralConfigurationLogic {
	return &RemoveGeneralConfigurationLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// RemoveGeneralConfiguration 删除通用配置
func (l *RemoveGeneralConfigurationLogic) RemoveGeneralConfiguration(in *pb.RemoveGeneralConfigurationReq) (
	resp *pb.RemoveGeneralConfigurationResp, err error,
) {
	// validate the project_id in req
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, in.GetProjectId()); err != nil {
		return nil, err
	}

	configIds := in.GetConfigIds()
	workers := len(configIds)
	if workers > common.ConstMRMaxWorkers {
		workers = common.ConstMRMaxWorkers
	}

	mr.ForEach(
		func(source chan<- any) {
			for _, configId := range configIds {
				source <- configId
			}
		}, func(item any) {
			configId, ok := item.(string)
			if !ok {
				err = multierror.Append(
					err,
					errorx.Err(errorx.InternalError, fmt.Sprintf("the general config id[%v] is not a string", item)),
				)
			} else {
				if e := l.remove(in.GetProjectId(), configId); e != nil {
					err = multierror.Append(err, e)
				}
			}
		}, mr.WithContext(l.ctx), mr.WithWorkers(workers),
	)

	return &pb.RemoveGeneralConfigurationResp{}, err
}

func (l *RemoveGeneralConfigurationLogic) remove(projectId, configId string) (err error) {
	// validate the config_id in req
	generalConfig, err := model.CheckGeneralConfigByConfigId(l.ctx, l.svcCtx.GeneralConfigModel, projectId, configId)
	if err != nil {
		return err
	}

	apiPlans, err := l.svcCtx.ApiPlanModel.FindByGeneralConfigId(l.ctx, projectId, configId)
	if err != nil {
		return errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()),
			"failed to find api plan, project_id: %s, config_id: %s, config_name: %s, error: %+v",
			projectId, configId, generalConfig.Name, err,
		)
	} else if len(apiPlans) > 0 {
		return errors.WithStack(
			errorx.Err(
				errorx.ProhibitedBehavior, fmt.Sprintf(
					"cannot to delete the general configuration which has been referenced by %d api plan[s], project_id: %s, config_id: %s, config_name: %s",
					len(apiPlans), projectId, configId, generalConfig.Name,
				),
			),
		)
	}

	perfPlans, err := l.svcCtx.PerfPlanV2Model.FindByGeneralConfigID(l.ctx, projectId, configId)
	if err != nil {
		return errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()),
			"failed to find perf plan, project_id: %s, config_id: %s, config_name: %s, error: %+v",
			projectId, configId, generalConfig.Name, err,
		)
	} else if len(perfPlans) > 0 {
		return errors.WithStack(
			errorx.Err(
				errorx.ProhibitedBehavior, fmt.Sprintf(
					"cannot to delete the general configuration which has been referenced by %d perf plan[s], project_id: %s, config_id: %s, config_name: %s",
					len(perfPlans), projectId, configId, generalConfig.Name,
				),
			),
		)
	}

	// acquire redis lock
	key := fmt.Sprintf("%s:%s:%s", common.ConstLockGeneralConfigProjectIdConfigIdPrefix, projectId, configId)
	lock, err := redislock.NewRedisLockAndAcquire(l.svcCtx.Redis, key, redislock.WithExpire(common.ConstLockExpireTime))
	if err != nil {
		return err
	}
	defer func() {
		// release redis lock
		e := lock.Release()
		if e != nil {
			l.Logger.Error(e)
			if re, ok := errorx.RootError(e); ok && re.Code() != errorx.ReleaseRedisLockFailure && err == nil {
				err = e
			}
		}
	}()

	return l.svcCtx.GeneralConfigModel.Trans(
		l.ctx, func(context context.Context, session sqlx.Session) error {
			// Table: general_configuration
			if _, err := l.svcCtx.GeneralConfigModel.RemoveByConfigId(
				context, session, projectId, configId,
			); err != nil {
				return err
			}

			return nil
		},
	)
}
