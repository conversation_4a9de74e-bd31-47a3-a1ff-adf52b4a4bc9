package generalconfigurationservicelogic

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type ViewGeneralConfigurationLogic struct {
	*BaseLogic
}

func NewViewGeneralConfigurationLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ViewGeneralConfigurationLogic {
	return &ViewGeneralConfigurationLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// ViewGeneralConfiguration 查看通用配置
func (l *ViewGeneralConfigurationLogic) ViewGeneralConfiguration(in *pb.ViewGeneralConfigurationReq) (resp *pb.ViewGeneralConfigurationResp, err error) {
	// validate the project_id in req
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, in.GetProjectId()); err != nil {
		return nil, err
	}

	// validate the config_id in req
	generalConfig, err := model.CheckGeneralConfigByConfigId(l.ctx, l.svcCtx.GeneralConfigModel, in.GetProjectId(), in.GetConfigId())
	if err != nil {
		return nil, err
	}

	resp = &pb.ViewGeneralConfigurationResp{Configuration: &pb.GeneralConfiguration{}}
	if err = utils.Copy(resp.Configuration, generalConfig, l.converters...); err != nil {
		return nil, errors.Wrapf(errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy general configuration[%+v] to response, error: %+v", generalConfig, err)
	}

	return resp, nil
}
