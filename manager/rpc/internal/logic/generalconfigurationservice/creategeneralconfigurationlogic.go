package generalconfigurationservicelogic

import (
	"context"
	"database/sql"
	"time"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	commonpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type CreateGeneralConfigurationLogic struct {
	*BaseLogic
}

func NewCreateGeneralConfigurationLogic(
	ctx context.Context, svcCtx *svc.ServiceContext,
) *CreateGeneralConfigurationLogic {
	return &CreateGeneralConfigurationLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// CreateGeneralConfiguration 创建通用配置
func (l *CreateGeneralConfigurationLogic) CreateGeneralConfiguration(in *pb.CreateGeneralConfigurationReq) (
	resp *pb.CreateGeneralConfigurationResp, err error,
) {
	// validate the project_id in req
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, in.GetProjectId()); err != nil {
		return nil, err
	}

	if in.GetVariables() == nil {
		in.SetVariables(make([]*commonpb.GeneralConfigVar, 0))
	}

	configId, err := l.generateConfigId(in.GetProjectId())
	if err != nil {
		return nil, err
	}

	configuration := &model.GeneralConfiguration{
		ProjectId: in.GetProjectId(),
		ConfigId:  configId,
		Type:      in.GetType(),
		Name:      in.GetName(),
		Description: sql.NullString{
			String: in.GetDescription(),
			Valid:  in.GetDescription() != "",
		},
		BaseUrl: sql.NullString{
			String: in.GetBaseUrl(),
			Valid:  in.GetBaseUrl() != "",
		},
		Verify: func() int64 {
			if in.GetVerify() {
				return 1
			}
			return 0
		}(),
		Variables: protobuf.MarshalJSONWithMessagesToStringIgnoreError(in.GetVariables()),
		CreatedBy: l.currentUser.Account,
		UpdatedBy: l.currentUser.Account,
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}
	if _, err = l.svcCtx.GeneralConfigModel.Insert(l.ctx, nil, configuration); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()),
			"failed to insert values to table, table: %s, values: %s, error: %+v",
			l.svcCtx.GeneralConfigModel.Table(), jsonx.MarshalIgnoreError(configuration), err,
		)
	}

	resp = &pb.CreateGeneralConfigurationResp{Configuration: &pb.GeneralConfiguration{}}
	if err = utils.Copy(resp.Configuration, configuration, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy git configuration to response, config: %s, error: %+v",
			jsonx.MarshalIgnoreError(configuration), err,
		)
	}

	return resp, nil
}
