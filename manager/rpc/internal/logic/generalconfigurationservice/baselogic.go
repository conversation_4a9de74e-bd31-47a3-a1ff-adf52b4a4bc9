package generalconfigurationservicelogic

import (
	"context"

	"github.com/Masterminds/squirrel"
	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/logx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sqlbuilder"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/userinfo"
	qetutils "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/utils"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type BaseLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext

	currentUser *userinfo.UserInfo

	converters []qetutils.TypeConverter
}

func newBaseLogic(ctx context.Context, svcCtx *svc.ServiceContext) *BaseLogic {
	return &BaseLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,

		currentUser: userinfo.FromContext(ctx),

		converters: []qetutils.TypeConverter{
			logic.StringToVariables(),
		},
	}
}

func (l *BaseLogic) generateConfigId(projectId string) (string, error) {
	g := qetutils.NewUniqueIdGenerator(
		qetutils.WithGenerateFunc(utils.GenGeneralConfigId), qetutils.WithIsUniqueFunc(
			func(id string) bool {
				r, err := l.svcCtx.GeneralConfigModel.FindOneByProjectIdConfigId(l.ctx, projectId, id)
				if errors.Is(err, model.ErrNotFound) || r == nil {
					return true
				}
				return false
			},
		),
	)
	configId := g.Next()
	if configId == "" {
		return "", errorx.Err(
			errorx.GenerateUniqueIdFailure, "failed to generate general config id, please try it later",
		)
	}

	return configId, nil
}

func (l *BaseLogic) generateSearchSqlBuilder(req *pb.SearchGeneralConfigurationReq) (sb, scb squirrel.SelectBuilder) {
	m := l.svcCtx.GeneralConfigModel

	sb = sqlbuilder.SearchOptions(
		m.SelectBuilder().Where("`project_id` = ?", req.GetProjectId()),
		sqlbuilder.WithCondition(m, req.GetCondition()),
		sqlbuilder.WithPagination(m, req.GetPagination()),
		sqlbuilder.WithSort(m, req.GetSort()),
	)
	scb = sqlbuilder.SearchOptions(
		m.SelectCountBuilder().Where("`project_id` = ?", req.GetProjectId()),
		sqlbuilder.WithCondition(m, req.GetCondition()),
	)

	return sb, scb
}
