package generalconfigurationservicelogic

import (
	"context"
	"math"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type SearchGeneralConfigurationLogic struct {
	*BaseLogic
}

func NewSearchGeneralConfigurationLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SearchGeneralConfigurationLogic {
	return &SearchGeneralConfigurationLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// SearchGeneralConfiguration 搜索通用配置
func (l *SearchGeneralConfigurationLogic) SearchGeneralConfiguration(in *pb.SearchGeneralConfigurationReq) (resp *pb.SearchGeneralConfigurationResp, err error) {
	resp = &pb.SearchGeneralConfigurationResp{}

	// validate the project_id in req
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, in.GetProjectId()); err != nil {
		return nil, err
	}

	selectBuilder, countBuilder := l.generateSearchSqlBuilder(in)

	count, err := l.svcCtx.GeneralConfigModel.FindCount(l.ctx, countBuilder)
	if err != nil {
		return nil, errors.Wrapf(errorx.Err(errorx.DBError, err.Error()), "failed to count general configuration with project_id[%s], error: %+v", in.GetProjectId(), err)
	}
	resp.TotalCount = uint64(count)
	resp.TotalPage = 1

	generalConfigs, err := l.svcCtx.GeneralConfigModel.FindNoCacheByQuery(l.ctx, selectBuilder)
	if err != nil {
		return nil, errors.Wrapf(errorx.Err(errorx.DBError, err.Error()), "failed to find general configuration with project_id[%s], error: %+v", in.GetProjectId(), err)
	}

	resp.Items = make([]*pb.GeneralConfiguration, 0, len(generalConfigs))
	for _, generalConfig := range generalConfigs {
		item := &pb.GeneralConfiguration{}
		if err = utils.Copy(item, generalConfig, l.converters...); err != nil {
			return nil, errors.Wrapf(errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy general configuration[%+v] to response, error: %+v", generalConfig, err)
		}

		resp.Items = append(resp.Items, item)
	}

	pagination := in.GetPagination()
	if pagination != nil {
		resp.CurrentPage = pagination.GetCurrentPage()
		resp.PageSize = pagination.GetPageSize()
		resp.TotalPage = uint64(math.Ceil(float64(resp.TotalCount) / float64(resp.PageSize)))
	}

	return resp, nil
}
