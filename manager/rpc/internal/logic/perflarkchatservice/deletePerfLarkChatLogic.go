package perflarkchatservicelogic

import (
	"context"
	"fmt"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/caller"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/redislock"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type DeletePerfLarkChatLogic struct {
	*BaseLogic
}

func NewDeletePerfLarkChatLogic(ctx context.Context, svcCtx *svc.ServiceContext) *DeletePerfLarkChatLogic {
	return &DeletePerfLarkChatLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// DeletePerfLarkChat 删除压测通知飞书群组（由飞书群解散事件触发）
func (l *DeletePerfLarkChatLogic) DeletePerfLarkChat(in *pb.DeletePerfLarkChatReq) (
	out *pb.DeletePerfLarkChatResp, err error,
) {
	key := fmt.Sprintf("%s:%s", common.ConstLockPerfLarkChatChatIDPrefix, in.GetChatId())
	fn := func() error {
		if _, err = l.svcCtx.PerfLarkChatModel.DeleteByChatID(l.ctx, nil, in.GetChatId()); err != nil {
			return errors.Wrapf(
				errorx.Err(errorx.DBError, err.Error()),
				"failed to delete items from table, table: %s, chat_id: %s, error: %+v",
				l.svcCtx.PerfLarkChatModel.Table(), in.GetChatId(), err,
			)
		}

		return nil
	}
	if err = caller.LockWithOptionDo(
		l.svcCtx.Redis, key, fn,
		redislock.WithTimeout(common.ConstExpireOfLarkChatDisbandedTask),
	); err != nil {
		return nil, err
	}

	return &pb.DeletePerfLarkChatResp{}, nil
}
