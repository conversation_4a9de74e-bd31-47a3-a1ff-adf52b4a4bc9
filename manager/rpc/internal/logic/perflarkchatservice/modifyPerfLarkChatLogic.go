package perflarkchatservicelogic

import (
	"context"
	"database/sql"
	"fmt"
	"time"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zyedidia/generic"
	"github.com/zyedidia/generic/hashmap"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/caller"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"

	larkproxypb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/larkproxy/rpc/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type ModifyPerfLarkChatLogic struct {
	*BaseLogic
}

func NewModifyPerfLarkChatLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ModifyPerfLarkChatLogic {
	return &ModifyPerfLarkChatLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// ModifyPerfLarkChat 编辑压测通知飞书群组列表
// Deprecated: use `larkchatservicelogic.ModifyLarkChat` instead.
func (l *ModifyPerfLarkChatLogic) ModifyPerfLarkChat(in *pb.ModifyPerfLarkChatReq) (
	out *pb.ModifyPerfLarkChatResp, err error,
) {
	// validate the project_id in req
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, in.GetProjectId()); err != nil {
		return nil, err
	}

	key := fmt.Sprintf("%s:%s", common.ConstLockPerfLarkChatProjectIDPrefix, in.GetProjectId())
	fn := func() error {
		return l.modify(in)
	}
	if err = caller.LockDo(l.svcCtx.Redis, key, fn); err != nil {
		return nil, err
	}

	return &pb.ModifyPerfLarkChatResp{}, nil
}

func (l *ModifyPerfLarkChatLogic) modify(req *pb.ModifyPerfLarkChatReq) error {
	var (
		projectID = req.GetProjectId()
		chats     = req.GetLarkChats()

		now = time.Now()
	)

	all, err := l.getAllLarkChats()
	if err != nil {
		return err
	}

	fromMap, err := l.getPerfLarkChats(projectID)
	if err != nil {
		return err
	}

	toMap := hashmap.New[string, *larkproxypb.ListChatRespData](
		uint64(len(chats)), generic.Equals[string], generic.HashString,
	)
	for _, chatID := range chats {
		if chatID == "" {
			continue
		}

		if v, ok := all.Get(chatID); ok {
			toMap.Put(chatID, v)
		}
	}

	return l.svcCtx.PerfLarkChatModel.Trans(
		l.ctx, func(context context.Context, session sqlx.Session) error {
			var e error

			toMap.Each(
				func(key string, val *larkproxypb.ListChatRespData) {
					if e != nil {
						return
					} else if _, ok := fromMap.Get(key); ok {
						return
					}

					data := &model.PerfLarkChat{
						ProjectId: projectID,
						ChatId:    key,
						Name:      val.GetName(),
						Avatar: sql.NullString{
							String: val.GetAvatar(),
							Valid:  val.GetAvatar() != "",
						},
						Description: sql.NullString{
							String: val.GetDescription(),
							Valid:  val.GetDescription() != "",
						},
						External: func() int64 {
							if val.GetExternal() {
								return 1
							}
							return 0
						}(),
						Status:    val.GetStatus(),
						CreatedBy: l.currentUser.Account,
						UpdatedBy: l.currentUser.Account,
						CreatedAt: now,
						UpdatedAt: now,
					}
					if _, err := l.svcCtx.PerfLarkChatModel.Insert(context, session, data); err != nil {
						e = errors.Wrapf(
							errorx.Err(errorx.DBError, err.Error()),
							"failed to batch insert values to table, table: %s, values: %s, error: %+v",
							l.svcCtx.PerfLarkChatModel.Table(), jsonx.MarshalIgnoreError(data), err,
						)
					}
				},
			)
			if e != nil {
				return e
			}

			fromMap.Each(
				func(key string, val *model.PerfLarkChat) {
					if e != nil {
						return
					}

					_, ok1 := all.Get(key)
					_, ok2 := toMap.Get(key)
					if !ok1 || !ok2 {
						if err := l.svcCtx.PerfLarkChatModel.Delete(context, session, val.Id); err != nil {
							e = errors.Wrapf(
								errorx.Err(errorx.DBError, err.Error()),
								"failed to delete item from table, table: %s, item: %s, error: %+v",
								l.svcCtx.PerfLarkChatModel.Table(), jsonx.MarshalIgnoreError(val), err,
							)
						}
					}
				},
			)

			return e
		},
	)
}

func (l *ModifyPerfLarkChatLogic) getAllLarkChats() (*hashmap.Map[string, *larkproxypb.ListChatRespData], error) {
	out, err := l.svcCtx.LarkProxyRPC.ListChat(l.ctx, &larkproxypb.ListChatReq{})
	if err != nil {
		return nil, err
	}

	cache := hashmap.New[string, *larkproxypb.ListChatRespData](
		uint64(len(out.GetItems())), generic.Equals[string], generic.HashString,
	)
	for _, item := range out.GetItems() {
		if item.GetChatId() == "" {
			continue
		} else if item.GetStatus() != string(common.ConstLarkChatStatusNormal) {
			continue
		}

		cache.Put(item.GetChatId(), item)
	}

	return cache, nil
}

func (l *ModifyPerfLarkChatLogic) getPerfLarkChats(projectID string) (
	*hashmap.Map[string, *model.PerfLarkChat], error,
) {
	records, err := l.svcCtx.PerfLarkChatModel.FindAll(l.ctx, projectID)
	if err != nil {
		return nil, err
	}

	cache := hashmap.New[string, *model.PerfLarkChat](
		uint64(len(records)), generic.Equals[string], generic.HashString,
	)
	for _, record := range records {
		if record == nil || record.Id == 0 {
			continue
		}

		cache.Put(record.ChatId, record)
	}

	return cache, nil
}
