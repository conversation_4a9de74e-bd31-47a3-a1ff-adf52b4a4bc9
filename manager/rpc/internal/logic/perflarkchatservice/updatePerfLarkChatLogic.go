package perflarkchatservicelogic

import (
	"context"
	"fmt"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/caller"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/redislock"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type UpdatePerfLarkChatLogic struct {
	*BaseLogic
}

func NewUpdatePerfLarkChatLogic(ctx context.Context, svcCtx *svc.ServiceContext) *UpdatePerfLarkChatLogic {
	return &UpdatePerfLarkChatLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// UpdatePerfLarkChat 更新压测通知飞书群组（由飞书群配置修改事件触发）
func (l *UpdatePerfLarkChatLogic) UpdatePerfLarkChat(in *pb.UpdatePerfLarkChatReq) (
	out *pb.UpdatePerfLarkChatResp, err error,
) {
	key := fmt.Sprintf("%s:%s", common.ConstLockPerfLarkChatChatIDPrefix, in.GetChatId())
	fn := func() error {
		req := model.UpdatePerfLarkChatReq{
			ChatID:      in.GetChatId(),
			Name:        in.GetName(),
			Avatar:      in.GetAvatar(),
			Description: in.GetDescription(),
			External:    in.GetExternal(),
		}
		if _, err := l.svcCtx.PerfLarkChatModel.UpdateByChatID(l.ctx, nil, req); err != nil {
			return errors.Wrapf(
				errorx.Err(errorx.DBError, err.Error()),
				"failed to update values to table, table: %s, values: %s, error: %+v",
				l.svcCtx.PerfLarkChatModel.Table(), jsonx.MarshalIgnoreError(req), err,
			)
		}

		return nil
	}
	if err = caller.LockWithOptionDo(
		l.svcCtx.Redis, key, fn,
		redislock.WithTimeout(common.ConstExpireOfLarkChatUpdatedTask),
	); err != nil {
		return nil, err
	}

	return &pb.UpdatePerfLarkChatResp{}, nil
}
