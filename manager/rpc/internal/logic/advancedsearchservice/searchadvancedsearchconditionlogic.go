package advancedsearchservicelogic

import (
	"context"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/logx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type SearchAdvancedSearchConditionLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewSearchAdvancedSearchConditionLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SearchAdvancedSearchConditionLogic {
	return &SearchAdvancedSearchConditionLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

func (l *SearchAdvancedSearchConditionLogic) SearchAdvancedSearchCondition(in *pb.SearchAdvancedSearchConditionReq) (resp *pb.SearchAdvancedSearchConditionResp, err error) {
	resp = &pb.SearchAdvancedSearchConditionResp{}

	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, in.GetProjectId()); err != nil {
		return nil, err
	}

	conditions, err := l.svcCtx.AdvancedSearchConditionModel.FindSearchConditionByFieldId(l.ctx, model.FindSearchConditionByFieldIdReq{
		ProjectId: in.GetProjectId(),
		FieldId:   in.GetFieldId(),
	})
	if err != nil {
		return nil, err
	}

	resp.Items = make([]*pb.AdvancedSearchCondition, 0, len(conditions))

	for _, field := range conditions {
		item := &pb.AdvancedSearchCondition{}
		if err = utils.Copy(item, field); err != nil {
			return nil, errors.Wrapf(errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy field[%+v] to response, error: %+v", field, err)
		}

		resp.Items = append(resp.Items, item)
	}

	return resp, nil
}
