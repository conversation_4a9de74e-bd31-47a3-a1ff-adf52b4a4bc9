package advancedsearchservicelogic

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type SearchAdvancedSearchFieldLogic struct {
	*BaseLogic
}

func NewSearchAdvancedSearchFieldLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SearchAdvancedSearchFieldLogic {
	return &SearchAdvancedSearchFieldLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *SearchAdvancedSearchFieldLogic) SearchAdvancedSearchField(in *pb.SearchAdvancedSearchFieldReq) (resp *pb.SearchAdvancedSearchFieldResp, err error) {
	resp = &pb.SearchAdvancedSearchFieldResp{}

	// validate the project_id in req
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, in.GetProjectId()); err != nil {
		return nil, err
	}

	fields, err := l.svcCtx.AdvancedSearchFieldModel.FindSearchFieldBySceneType(l.ctx, model.FindSearchFieldBySceneTypeReq{
		ProjectId: in.GetProjectId(),
		SceneType: in.GetSceneType().String(),
	})
	if err != nil {
		return nil, err
	}

	resp.Items = make([]*pb.AdvancedSearchField, 0, len(fields))

	for _, field := range fields {
		item := &pb.AdvancedSearchField{}
		if err = utils.Copy(item, field, l.converters...); err != nil {
			return nil, errors.Wrapf(errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy field[%+v] to response, error: %+v", field, err)
		}

		resp.Items = append(resp.Items, item)
	}

	return resp, nil
}
