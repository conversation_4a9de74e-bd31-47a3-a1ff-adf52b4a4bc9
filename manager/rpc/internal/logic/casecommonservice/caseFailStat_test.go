package casecommonservicelogic

import (
	"context"
	"os"
	"testing"
	"time"

	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/trace"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworkerv2/base"
	producer2 "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworkerv2/producer"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	managerpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

func TestName(t *testing.T) {
	// 发送manager失败统计消息
	err := os.Setenv("OTEL_RESOURCE_ATTRIBUTES", "token=YKasGxIXZtyEfEiCvjUi")
	if err != nil {
		return
	}
	trace.StartAgent(
		trace.Config{
			Name:     "main",
			Endpoint: "ap-beijing.apm.tencentcs.com:4317",
			Batcher:  "otlpgrpc",
			Sampler:  1.0,
		},
	)
	defer trace.StopAgent()
	producer := producer2.NewProducer(
		producer2.Config{
			Broker:  "192.168.118.128:6379",
			Backend: "192.168.118.128:6379",
			Queue:   "mqc:manager",
			Db:      0,
		},
	)
	/*
	   INSERT INTO manager.case_fail_stat (id, project_id, case_id, branch_id, execute_id, case_type, fail_count, version, deleted, created_by, updated_by, deleted_by, created_at, updated_at, deleted_at) VALUES (1, 'project_id:Kqllt5-9fA-I5UOdhjA5d', 'case_id:0YNd3Jmd03dC43mc9K2W2', 'category_id:5dPmE5tYh9K3Lz9bX9Xrp', 'Ex:f0238h3', 'API_CASE', 5, 5, 0, 'T4440', 'T4440', null, '2024-04-17 17:52:01', '2024-04-18 11:30:20', null);

	*/
	mqRequest := &managerpb.CaseFailStatForMq{
		ProjectId: "project_id:Kqllt5-9fA-I5UOdhjA5d",
		// BranchId:  source.GetTaskId(),
		ExecuteId: "Ex:f0238h3",
		CaseId:    "case_id:0YNd3Jmd03dC43mc9K2W2",
		CaseType:  "API_CASE",
	}

	payload := protobuf.MarshalJSONIgnoreError(mqRequest)
	task := base.NewTask(
		constants.MQTaskTypeManagerHandleCaseFailStatResult, payload,
		base.WithMaxRetryOptions(3),
		base.WithRetentionOptions(time.Minute*10),
	)
	ctx := context.Background()

	_, err = producer.Send(ctx, task, base.QueuePriorityDefault)
	if err != nil {
		logx.WithContext(ctx).Errorf("UICaseNode TaskInfoProcessorSync,error:%s", err)
	}
}

func TestName1(t *testing.T) {
	// 发送manager失败统计消息
	err := os.Setenv("OTEL_RESOURCE_ATTRIBUTES", "token=YKasGxIXZtyEfEiCvjUi")
	if err != nil {
		return
	}
	s := "{\"trigger_mode\":1,\"project_id\":\"project_id:B3HxxLu4Hngh4LLAG1lYu\",\"task_id\":\"task_id:0aJFh2rSRT8acC_NriKUi\",\"execute_type\":4,\"callback_type\":1,\"purpose_type\":1,\"Data\":{\"Case\":{\"suite_id\":\"suite_id:n40LM4JmUPjMco4hTsLP7\",\"suite_execute_id\":\"execute_id:zS9mQQMQr-4d9eHiGgWDX\",\"case_id\":\"case_id:UVIx08SffOdvLRKgrldXd\",\"case_execute_id\":\"execute_id:PQCHzDAGpjL5feQeZ2SLx\",\"case_state\":21,\"version\":\"version:20240306122041319:TnmuI\"}}}"
	trace.StartAgent(
		trace.Config{
			Name:     "main",
			Endpoint: "ap-beijing.apm.tencentcs.com:4317",
			Batcher:  "otlpgrpc",
			Sampler:  1.0,
		},
	)
	defer trace.StopAgent()
	producer := producer2.NewProducer(
		producer2.Config{
			Broker:  "192.168.118.128:6379",
			Backend: "192.168.118.128:6379",
			Queue:   "mqc:dispatcher",
			Db:      0,
		},
	)

	task := base.NewTask(
		constants.MQTaskTypeDispatcherCallback, []byte(s),
		base.WithMaxRetryOptions(3),
		base.WithRetentionOptions(time.Minute*10),
	)
	ctx := context.Background()

	_, err = producer.Send(ctx, task, base.QueuePriorityDefault)
	if err != nil {
		logx.WithContext(ctx).Errorf("UICaseNode TaskInfoProcessorSync,error:%s", err)
	}
}
