package casecommonservicelogic

import (
	"context"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/mr"
	"github.com/zeromicro/go-zero/core/stores/sqlx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type BatchDeleteFailLogCaseLogic struct {
	*BaseLogic
}

func NewBatchDeleteFailLogCaseLogic(ctx context.Context, svcCtx *svc.ServiceContext) *BatchDeleteFailLogCaseLogic {
	return &BatchDeleteFailLogCaseLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// BatchDeleteFailLogCase 批量删除（忽略）用例失败记录
func (l *BatchDeleteFailLogCaseLogic) BatchDeleteFailLogCase(in *pb.BatchDeleteCaseFailLogReq) (
	out *pb.BatchDeleteCaseFailLogResp, err error,
) {
	projectId := in.GetProjectId()

	if err = l.svcCtx.CaseFailStatModel.Trans(
		l.ctx, func(context context.Context, session sqlx.Session) error {
			err = mr.MapReduceVoid[*pb.DeleteCaseFailLog, *model.CaseFailStat](
				func(source chan<- *pb.DeleteCaseFailLog) {
					for _, item := range in.GetDeleteCaseFailLogList() {
						source <- item
					}
				}, func(item *pb.DeleteCaseFailLog, writer mr.Writer[*model.CaseFailStat], cancel func(error)) {
					var err error
					defer func() {
						if err != nil {
							cancel(err)
						}
					}()
					if v, err := l.svcCtx.CaseFailStatModel.FindNoCacheByProjectIdCaseIdCaseType(
						l.ctx, projectId, item.GetCaseId(), item.GetCaseType(),
					); err == nil && v != nil {
						writer.Write(v)
					}
				}, func(pipe <-chan *model.CaseFailStat, cancel func(error)) {
					var err error
					defer func() {
						if err != nil {
							cancel(err)
						}
					}()
					for item := range pipe {
						err = l.DeleteFailLogCaseHandler(context, session, projectId, item.CaseId, item.CaseType)
					}
				}, mr.WithContext(l.ctx), mr.WithWorkers(len(in.GetDeleteCaseFailLogList())),
			)
			if err != nil {
				return errors.Wrapf(
					errorx.Err(errorx.DBError, err.Error()),
					"DeleteFailLogCaseHandler case with project_id[%s]error: %+v",
					in.GetProjectId(), err,
				)
			}
			return nil
		},
	); err != nil {
		return nil, err
	}

	return &pb.BatchDeleteCaseFailLogResp{}, nil
}
