package casecommonservicelogic

import (
	"context"
	"math"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sqlbuilder/rpc"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type SearchNotReleasedCaseLogic struct {
	*BaseLogic
}

func NewSearchNotReleasedCaseLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SearchNotReleasedCaseLogic {
	return &SearchNotReleasedCaseLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// SearchNotReleasedCase 搜索未上线用例
func (l *SearchNotReleasedCaseLogic) SearchNotReleasedCase(in *pb.SearchCaseReq) (resp *pb.SearchCaseResp, err error) {
	resp = &pb.SearchCaseResp{}

	req := model.SearchNotReleasedCaseReq{
		ProjectId:  in.GetProjectId(),
		Condition:  in.GetCondition(),
		Pagination: in.GetPagination(),
		Sort:       rpc.ConvertSortFields(in.GetSort()),
	}

	selectBuilder, countBuilder := l.svcCtx.AICaseModel.GenerateSearchNotReleasedCaseSqlBuilder(req)

	count, err := l.svcCtx.AICaseModel.FindCount(l.ctx, countBuilder.SelectBuilder)
	if err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()), "failed to count case with project_id[%s], error: %+v",
			in.GetProjectId(), err,
		)
	}
	resp.TotalCount = uint64(count)
	resp.TotalPage = 1

	cases, err := l.svcCtx.AICaseModel.FindNoCacheByQuery(l.ctx, selectBuilder.SelectBuilder)
	if err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()), "failed to find case with project_id[%s], error: %+v",
			in.GetProjectId(), err,
		)
	}

	resp.Items = make([]*pb.Case, 0, len(cases))
	for _, _Case := range cases {
		item := &pb.Case{}
		if err = utils.Copy(item, _Case, l.converters...); err != nil {
			return nil, errors.Wrapf(
				errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy case[%+v] to response, error: %+v",
				_Case, err,
			)
		}

		resp.Items = append(resp.Items, item)
	}

	pagination := in.GetPagination()
	if pagination != nil {
		resp.CurrentPage = pagination.GetCurrentPage()
		resp.PageSize = pagination.GetPageSize()
		resp.TotalPage = uint64(math.Ceil(float64(resp.TotalCount) / float64(resp.PageSize)))
	}

	return resp, nil
}
