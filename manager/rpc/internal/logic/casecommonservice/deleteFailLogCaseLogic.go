package casecommonservicelogic

import (
	"context"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/stores/sqlx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type DeleteFailLogCaseLogic struct {
	*BaseLogic
}

func NewDeleteFailLogCaseLogic(ctx context.Context, svcCtx *svc.ServiceContext) *DeleteFailLogCaseLogic {
	return &DeleteFailLogCaseLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// DeleteFailLogCase 删除（忽略）用例失败记录
func (l *DeleteFailLogCaseLogic) DeleteFailLogCase(in *pb.DeleteFailLogCaseReq) (
	out *pb.DeleteFailLogCaseResp, err error,
) {
	if err = l.svcCtx.CaseFailStatModel.Trans(
		l.ctx, func(context context.Context, session sqlx.Session) error {
			err := l.DeleteFailLogCaseHandler(context, session, in.GetProjectId(), in.GetCaseId(), in.GetCaseType())
			if err != nil {
				return err
			}
			return nil
		},
	); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()),
			"DeleteFailLogCaseHandler case with project_id[%s],case_id[%s], error: %+v",
			in.GetProjectId(), in.GetCaseId(), err,
		)
	}

	return &pb.DeleteFailLogCaseResp{}, nil
}
