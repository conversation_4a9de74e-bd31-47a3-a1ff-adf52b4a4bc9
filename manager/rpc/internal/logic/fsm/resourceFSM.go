package fsm

import (
	"bytes"
	"context"
	"net/url"
	ttemplate "text/template"
	"time"

	"github.com/looplab/fsm"
	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/stores/sqlx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/caller"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/userinfo"
	notifierpb "gitlab.ttyuyin.com/TestDevelopment/qet-backend-middleware/notifier/rpc/pb"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
)

type ResourceFSM[R Resource] struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext

	currentUser *userinfo.UserInfo
	resource    R
	session     sqlx.Session

	fsm *fsm.FSM
}

func NewResourceFSM[R Resource](
	ctx context.Context, svcCtx *svc.ServiceContext, resource R, session sqlx.Session,
) *ResourceFSM[R] {
	l := &ResourceFSM[R]{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,

		currentUser: userinfo.FromContext(ctx),
		resource:    resource,
		session:     session,
	}

	l.fsm = fsm.NewFSM(
		string(common.ConstResourceStateNew),
		fsm.Events{
			{
				// 分配负责人（新 -> 待实现）
				Name: string(common.ConstReviewResourceEventAssignedToTheResponsiblePerson),
				Src:  []string{string(common.ConstResourceStateNew)},
				Dst:  string(common.ConstResourceStateToBeImplemented),
			},
			{
				// 实现后申请审核（待实现 -> 待审核）
				Name: string(common.ConstReviewResourceEventApplyForReviewAfterImplementation),
				Src:  []string{string(common.ConstResourceStateToBeImplemented)},
				Dst:  string(common.ConstResourceStatePendingReview),
			},
			{
				// 维护后申请审核（待维护 -> 待审核）
				Name: string(common.ConstReviewResourceEventApplyForReviewAfterMaintenance),
				Src:  []string{string(common.ConstResourceStateToBeMaintained)},
				Dst:  string(common.ConstResourceStatePendingReview),
			},
			{
				// 编辑实现后提交的申请（待审核 -> 待审核）
				Name: string(common.ConstReviewResourceEventModifyTheReviewAfterImplementation),
				Src:  []string{string(common.ConstResourceStatePendingReview)},
				Dst:  string(common.ConstResourceStatePendingReview),
			},
			{
				// 编辑维护后提交的申请（待审核 -> 待审核）
				Name: string(common.ConstReviewResourceEventModifyTheReviewAfterMaintenance),
				Src:  []string{string(common.ConstResourceStatePendingReview)},
				Dst:  string(common.ConstResourceStatePendingReview),
			},
			{
				// 撤回实现后提交的申请（待审核 -> 待实现）
				Name: string(common.ConstReviewResourceEventRevokeTheReviewAfterImplementation),
				Src:  []string{string(common.ConstResourceStatePendingReview)},
				Dst:  string(common.ConstResourceStateToBeImplemented),
			},
			{
				// 撤回维护后提交的申请（待审核 -> 待维护）
				Name: string(common.ConstReviewResourceEventRevokeTheReviewAfterMaintenance),
				Src:  []string{string(common.ConstResourceStatePendingReview)},
				Dst:  string(common.ConstResourceStateToBeMaintained),
			},
			{
				// 审核通过（待审核 -> 已上线）
				Name: string(common.ConstReviewResourceEventReviewApproved),
				Src:  []string{string(common.ConstResourceStatePendingReview)},
				Dst:  string(common.ConstResourceStatePublished),
			},
			{
				// 审核驳回（待审核 -> 待维护）
				Name: string(common.ConstReviewResourceEventReviewRejected),
				Src:  []string{string(common.ConstResourceStatePendingReview)},
				Dst:  string(common.ConstResourceStateToBeMaintained),
			},
			{
				// 上线后再维护（已上线 -> 待维护）
				Name: string(common.ConstReviewResourceEventReturnToMaintenanceAfterPublished),
				Src:  []string{string(common.ConstResourceStatePublished)},
				Dst:  string(common.ConstResourceStateToBeMaintained),
			},
			{
				// 实现后直接上线（待实现 -> 已上线）
				Name: string(common.ConstReviewResourceEventPublishedAfterImplementation),
				Src:  []string{string(common.ConstResourceStateToBeImplemented)},
				Dst:  string(common.ConstResourceStatePublished),
			},
			{
				// 维护后直接上线（待维护 -> 已上线）
				Name: string(common.ConstReviewResourceEventPublishedAfterMaintenance),
				Src:  []string{string(common.ConstResourceStateToBeMaintained)},
				Dst:  string(common.ConstResourceStatePublished),
			},
		},
		fsm.Callbacks{
			"enter_state": l.updateState,
			"after_event": l.notify,
		},
	)

	return l
}

func (l *ResourceFSM[R]) Transition() error {
	key := l.resource.GenLockKey()
	fn := func() error {
		if err := l.resource.Refresh(l.ctx, l.svcCtx); err != nil {
			return err
		}

		return l.TransitionWithoutLock()
	}
	return caller.LockDo(l.svcCtx.Redis, key, fn)
}

func (l *ResourceFSM[R]) TransitionWithoutLock() error {
	// validate the project_id in req
	if _, err := model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, l.resource.ProjectID()); err != nil {
		return err
	}

	// set the state of resource to the current state of fsm
	l.fsm.SetState(string(l.resource.ResourceState()))

	switch err := l.fsm.Event(l.ctx, string(l.resource.ReviewEvent())); {
	case errors.As(err, logic.FSMNoTransitionError):
		return nil
	default:
		return err
	}
}

func (l *ResourceFSM[R]) check(e *fsm.Event) (*R, error) {
	if e == nil {
		return nil, errorx.Err(errorx.FSMError, "got a null fsm event")
	}

	if e.Err != nil {
		return nil, errorx.Errorf(errorx.FSMError, "got an error from fsm event, error: %+v", e.Err)
	}

	if len(e.Args) == 0 {
		return nil, errorx.Errorf(
			errorx.FSMError,
			"got no arguments, src: %s, dst: %s, event: %s, args: %s",
			e.Src, e.Dst, e.Event, jsonx.MarshalIgnoreError(e.Args),
		)
	}

	item, ok := e.Args[0].(*R)
	if !ok || item == nil {
		return nil, errorx.Errorf(
			errorx.FSMError,
			"invalid argument, src: %s, dst: %s, event: %s, args[0]: %T, %s",
			e.Src, e.Dst, e.Event, e.Args[0], jsonx.MarshalIgnoreError(e.Args[0]),
		)
	}

	l.Debugf(
		"got valid argument by checking fsm event, src: %s, dst: %s, event: %s, args: %s",
		e.Src, e.Dst, e.Event, jsonx.MarshalIgnoreError(e.Args),
	)

	return item, nil
}

func (l *ResourceFSM[R]) updateState(ctx context.Context, e *fsm.Event) {
	var err error
	defer func() {
		if err != nil {
			l.Errorf(
				"failed to update state for event cause by an error, src: %s, dst: %s, event: %s, error: %+v",
				e.Src, e.Dst, e.Event, err,
			)

			e.Cancel(err)
		}
	}()

	if string(l.resource.ResourceState()) == e.Dst {
		l.Infof(
			"no need to update state of interface case, src: %s, dst: %s, event: %s",
			e.Src, e.Dst, e.Event,
		)
		return
	}

	l.resource.SetResourceState(e.Dst)
	l.resource.SetResourceUpdatedBy(l.currentUser.Account)
	if err = l.resource.Update(ctx, l.svcCtx, l.session); err != nil {
		return
	}
}

func (l *ResourceFSM[R]) notify(ctx context.Context, e *fsm.Event) {
	var (
		notifyT, urlT *ttemplate.Template
		err           error
	)
	defer func() {
		if err != nil {
			l.Errorf(
				"failed to send notification for event cause by an error, src: %s, dst: %s, event: %s, error: %+v",
				e.Src, e.Dst, e.Event, err,
			)

			// failure to send notification does not affect state transition
			// e.Cancel(err)
		}
	}()

	// get lark receivers by R
	receivers := l.getLarkReceivers()
	if len(receivers) == 0 {
		l.Warnf(
			"no need to send notification for event cause by the receivers list is empty, src: %s, dst: %s, event: %s, resource: %s",
			e.Src, e.Dst, e.Event, jsonx.MarshalIgnoreError(l.resource),
		)
		return
	}

	// get template by R
	notifyT, urlT = l.getTemplate()
	if notifyT == nil || urlT == nil {
		l.Infof(
			"no need to send notification for event cause by not found template, src: %s, dst: %s, event: %s, resource: %s",
			e.Src, e.Dst, e.Event, jsonx.MarshalIgnoreError(l.resource),
		)
		return
	}

	// get the notification content by executing template with data
	content, err := l.getContent(notifyT, urlT)
	if err != nil {
		return
	}

	// send a lark custom app notification by notifier
	if _, err = l.svcCtx.NotifierRPC.Notify(
		ctx, &notifierpb.NotifyReq{
			Items: []*notifierpb.NotifyItem{
				{
					Item: &notifierpb.NotifyItem_LarkCustomApp{
						LarkCustomApp: &notifierpb.LarkCustomApp{
							Receivers: receivers,
							MsgType:   notifierpb.LarkMessageType_INTERACTIVE,
							Content:   content,
						},
					},
				},
			},
		},
	); err != nil {
		return
	}
}

func (l *ResourceFSM[R]) getLarkReceivers() []*notifierpb.LarkReceiver {
	emails := l.resource.Receivers()
	receivers := make([]*notifierpb.LarkReceiver, 0, len(emails))
	for _, email := range emails {
		if email == "" {
			continue
		}

		receivers = append(
			receivers, &notifierpb.LarkReceiver{
				Id:   email,
				Type: notifierpb.LarkReceiveIdType_EMAIL,
			},
		)
	}

	return receivers
}

func (l *ResourceFSM[R]) getTemplate() (notifyT, urlT *ttemplate.Template) {
	switch l.resource.ReviewEvent() {
	case common.ConstReviewResourceEventAssignedToTheResponsiblePerson:
		notifyT = common.ToBeImplementedNotifyTemplate
	case common.ConstReviewResourceEventReturnToMaintenanceAfterPublished:
		notifyT = common.ToBeMaintainedNotifyTemplate
	case common.ConstReviewResourceEventApplyForReviewAfterImplementation,
		common.ConstReviewResourceEventApplyForReviewAfterMaintenance:
		notifyT = common.PendingReviewNotifyTemplate
	case common.ConstReviewResourceEventReviewApproved, common.ConstReviewResourceEventReviewRejected:
		notifyT = common.ReviewResultNotifyTemplate
	default:
		return notifyT, urlT
	}

	if notifyT == common.PendingReviewNotifyTemplate {
		urlT = common.PendingReviewRecordsURLTemplate
		return notifyT, urlT
	}

	switch l.resource.ResourceType() {
	case common.ConstReviewResourceTypeSetupComponent,
		common.ConstReviewResourceTypeTeardownComponent,
		common.ConstReviewResourceTypeBusinessComponent:
		urlT = common.ModifyComponentGroupURLTemplate
	case common.ConstReviewResourceTypeAPICase:
		urlT = common.ModifyApiCaseURLTemplate
	case common.ConstReviewResourceTypeInterfaceCase:
		urlT = common.ModifyInterfaceCaseURLTemplate
	default:
		return notifyT, urlT
	}

	return notifyT, urlT
}

func (l *ResourceFSM[R]) getContent(notifyT, urlT *ttemplate.Template) (string, error) {
	var (
		info = &NotificationItem{
			ProjectID:            l.resource.ProjectID(),
			ResourceBranch:       l.resource.ResourceBranch(),
			ResourceParentType:   l.resource.ResourceParentType(),
			ResourceParentTypeZH: ConvertToReviewResourceTypeZH(l.resource.ResourceParentType()),
			ResourceType:         l.resource.ResourceType(),
			ResourceTypeZH:       ConvertToReviewResourceTypeZH(l.resource.ResourceType()),
			ResourceID:           l.resource.ResourceID(),
			ResourceName:         l.resource.ResourceName(),
			ReviewResult:         l.resource.ReviewResult(),
			ReviewResultZH:       ConvertToReviewStatusZH(l.resource.ReviewResult()),
			ReviewRemark:         l.resource.ReviewRemark(),
			NotifiedBy:           l.currentUser.Fullname,
			NotifiedAt:           time.Now(),
			RedirectURL:          "", // set after executing url template
		}

		buf = new(bytes.Buffer)
	)
	defer func() {
		buf.Reset()
	}()

	if err := urlT.Execute(buf, info); err != nil {
		return "", err
	}

	_url, _ := url.JoinPath(l.svcCtx.Config.StarProbeURL, "#", buf.String())
	info.RedirectURL, _ = url.QueryUnescape(_url)
	buf.Reset()

	if err := notifyT.Execute(buf, info); err != nil {
		return "", err
	}

	return buf.String(), nil
}
