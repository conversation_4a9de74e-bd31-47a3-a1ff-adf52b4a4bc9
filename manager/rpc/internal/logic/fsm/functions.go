package fsm

import "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"

func ConvertToReviewResourceTypeZH(v common.ReviewResourceType) common.ReviewResourceTypeZH {
	switch v {
	case common.ConstReviewResourceTypeComponentGroup:
		return common.ConstReviewResourceTypeZHComponentGroup
	case common.ConstReviewResourceTypeCase:
		return common.ConstReviewResourceTypeZHCase
	case common.ConstReviewResourceTypeSetupComponent:
		return common.ConstReviewResourceTypeZHSetupComponent
	case common.ConstReviewResourceTypeTeardownComponent:
		return common.ConstReviewResourceTypeZHTeardownComponent
	case common.ConstReviewResourceTypeBusinessComponent:
		return common.ConstReviewResourceTypeZHBusinessComponent
	case common.ConstReviewResourceTypeAPICase:
		return common.ConstReviewResourceTypeZHAPICase
	case common.ConstReviewResourceTypeInterfaceCase:
		return common.ConstReviewResourceTypeZHInterfaceCase
	default:
		return ""
	}
}

func ConvertToReviewStatusZH(v common.ReviewStatus) common.ReviewStatusZH {
	switch v {
	case common.ConstReviewStatusPending:
		return common.ConstReviewStatusZHPending
	case common.ConstReviewStatusRevoked:
		return common.ConstReviewStatusZHRevoked
	case common.ConstReviewStatusApproved:
		return common.ConstReviewStatusZHApproved
	case common.ConstReviewStatusRejected:
		return common.ConstReviewStatusZHRejected
	default:
		return ""
	}
}
