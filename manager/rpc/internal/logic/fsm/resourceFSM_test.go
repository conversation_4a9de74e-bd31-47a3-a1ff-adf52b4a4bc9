package fsm

import (
	"bytes"
	"fmt"
	"net/url"
	"strconv"
	"testing"
	ttemplate "text/template"
	"time"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
)

func TestGetNotificationContent(t *testing.T) {
	type args struct {
		event  common.ReviewResourceEvent
		emails []string
		result common.ReviewStatus
		remark string
	}
	tests := []struct {
		name string
		args args
	}{
		{
			name: "待实现",
			args: args{
				event:  common.ConstReviewResourceEventAssignedToTheResponsiblePerson,
				emails: []string{"T1704"},
				result: "",
				remark: "实现此用例",
			},
		},
		{
			name: "待维护",
			args: args{
				event:  common.ConstReviewResourceEventReturnToMaintenanceAfterPublished,
				emails: []string{"T1704"},
				result: "",
				remark: "维护此用例",
			},
		},
		{
			name: "待审核",
			args: args{
				event:  common.ConstReviewResourceEventApplyForReviewAfterImplementation,
				emails: []string{"T1704", "T4440"},
				result: "",
				remark: "请审核此用例",
			},
		},
		{
			name: "审核通过",
			args: args{
				event:  common.ConstReviewResourceEventReviewApproved,
				emails: []string{"T1704"},
				result: common.ConstReviewStatusApproved,
				remark: "实现得很好\n通过",
			},
		},
		{
			name: "审核驳回",
			args: args{
				event:  common.ConstReviewResourceEventReviewRejected,
				emails: []string{"T1704"},
				result: common.ConstReviewStatusRejected,
				remark: "还是存在问题\n修改后再提交审核申请吧",
			},
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				item := &NotificationItem{
					ProjectID:            "project_id:Kqllt5-9fA-I5UOdhjA5d",
					ResourceBranch:       "category_id:GfMpPvPyShPCF5w37Z_D3",
					ResourceParentType:   common.ConstReviewResourceTypeCase,
					ResourceParentTypeZH: ConvertToReviewResourceTypeZH(common.ConstReviewResourceTypeCase),
					ResourceType:         common.ConstReviewResourceTypeAPICase,
					ResourceTypeZH:       ConvertToReviewResourceTypeZH(common.ConstReviewResourceTypeAPICase),
					ResourceID:           "case_id:tyz0U401tOW9Bx4g1NzCT",
					ResourceName:         "P1-获取主题列表传异常参数 \"试一下双引号\"\n再看看回车",
					ReviewResult:         tt.args.result,
					ReviewResultZH:       ConvertToReviewStatusZH(tt.args.result),
					ReviewRemark:         tt.args.remark,
					NotifiedBy:           "K",
					NotifiedAt:           time.Now(),
					RedirectURL:          "",
				}

				notifyT, urlT := getTemplate(tt.args.event, item.ResourceType)
				if notifyT == nil || urlT == nil {
					t.Log("no need to send notification for event cause by not found template")
					return
				}

				buf := new(bytes.Buffer)
				if err := urlT.Execute(buf, item); err != nil {
					t.Fatal(err)
				}

				starProbeURL := "https://dev-quality.ttyuyin.com/"
				_url, _ := url.JoinPath(starProbeURL, "#", buf.String())
				item.RedirectURL, _ = url.QueryUnescape(_url)

				buf.Reset()
				if err := notifyT.Execute(buf, item); err != nil {
					t.Fatal(err)
				}

				t.Logf("content: \n%s", buf.String())
			},
		)
	}
}

func getTemplate(event common.ReviewResourceEvent, typ common.ReviewResourceType) (notifyT, urlT *ttemplate.Template) {
	switch event {
	case common.ConstReviewResourceEventAssignedToTheResponsiblePerson:
		notifyT = common.ToBeImplementedNotifyTemplate
	case common.ConstReviewResourceEventReturnToMaintenanceAfterPublished:
		notifyT = common.ToBeMaintainedNotifyTemplate
	case common.ConstReviewResourceEventApplyForReviewAfterImplementation,
		common.ConstReviewResourceEventApplyForReviewAfterMaintenance:
		notifyT = common.PendingReviewNotifyTemplate
	case common.ConstReviewResourceEventReviewApproved, common.ConstReviewResourceEventReviewRejected:
		notifyT = common.ReviewResultNotifyTemplate
	default:
		return notifyT, urlT
	}

	if notifyT == common.PendingReviewNotifyTemplate {
		urlT = common.PendingReviewRecordsURLTemplate
		return notifyT, urlT
	}

	switch typ {
	case common.ConstReviewResourceTypeSetupComponent,
		common.ConstReviewResourceTypeTeardownComponent,
		common.ConstReviewResourceTypeBusinessComponent:
		urlT = common.ModifyComponentGroupURLTemplate
	case common.ConstReviewResourceTypeAPICase:
		urlT = common.ModifyApiCaseURLTemplate
	case common.ConstReviewResourceTypeInterfaceCase:
		urlT = common.ModifyInterfaceCaseURLTemplate
	default:
		return notifyT, urlT
	}

	return notifyT, urlT
}

func TestQuote(t *testing.T) {
	name := "P1-获取主题列表传异常参数 \"exec: 好的\""
	name1 := fmt.Sprintf("%q", name)
	name2 := strconv.Quote(name)
	t.Logf("\n0: %s\n1: %s\n2: %s", name, name1, name2)
}
