package reviewservicelogic

import (
	"context"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"
	"github.com/zeromicro/go-zero/core/lang"
	"github.com/zeromicro/go-zero/core/logx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/userinfo"
	qetutils "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"
	userpb "gitlab.ttyuyin.com/TestDevelopment/qet-backend-middleware/user/rpc/pb"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/utils"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/logic"
	apicaseservicelogic "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/logic/apicaseservice"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/logic/fsm"
	interfacedefinitionservicelogic "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/logic/interfacedefinitionservice"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type BaseLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext

	currentUser *userinfo.UserInfo

	converters []qetutils.TypeConverter
}

func newBaseLogic(ctx context.Context, svcCtx *svc.ServiceContext) *BaseLogic {
	return &BaseLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,

		currentUser: userinfo.FromContext(ctx),

		converters: []qetutils.TypeConverter{
			logic.StringToReviewResourceType(),
			logic.StringToReviewers(),
			logic.StringToReviewStatus(),
		},
	}
}

func (l *BaseLogic) generateReviewID(projectID string) (string, error) {
	g := qetutils.NewUniqueIdGenerator(
		qetutils.WithGenerateFunc(utils.GenReviewId), qetutils.WithIsUniqueFunc(
			func(id string) bool {
				r, err := l.svcCtx.ReviewRecordModel.FindOneByProjectIdReviewId(l.ctx, projectID, id)
				if errors.Is(err, model.ErrNotFound) || r == nil {
					return true
				}
				return false
			},
		),
	)
	reviewID := g.Next()
	if reviewID == "" {
		return "", errors.WithStack(
			errorx.Err(
				errorx.GenerateUniqueIdFailure, "failed to generate review id, please try it later",
			),
		)
	}

	return reviewID, nil
}

func (l *BaseLogic) checkResource(
	projectID string, resourceType pb.ReviewResourceType, resourceID string,
) (fsm.ResourceMetadata, error) {
	switch resourceType {
	case pb.ReviewResourceType_RRT_SETUP_COMPONENT, pb.ReviewResourceType_RRT_TEARDOWN_COMPONENT, pb.ReviewResourceType_RRT_BUSINESS_COMPONENT:
		return l.checkComponentGroup(projectID, string(resourceType.ConvertTo()), resourceID)
	case pb.ReviewResourceType_RRT_API_CASE:
		return l.checkAPICase(projectID, resourceID)
	case pb.ReviewResourceType_RRT_INTERFACE_CASE:
		return l.checkInterfaceCase(projectID, resourceID)
	default:
		return nil, errorx.Errorf(
			errorx.ValidateParamError, "invalid resource type: %s", protobuf.GetEnumStringOf(resourceType),
		)
	}
}

func (l *BaseLogic) checkComponentGroup(projectID, componentGroupType, componentGroupID string) (
	fsm.ResourceMetadata, error,
) {
	cg, err := model.CheckComponentGroupByComponentGroupId(
		l.ctx, l.svcCtx.ComponentGroupModel, projectID, componentGroupID, "",
	)
	if err != nil {
		return nil, err
	} else if cg.ComponentGroupType != componentGroupType {
		return nil, errorx.Errorf(
			errorx.ValidateParamError,
			"the component group type is mismatch, input: [%s, %s], actual: %s",
			componentGroupID, componentGroupType, cg.ComponentGroupType,
		)
	}

	return cg, nil
}

func (l *BaseLogic) checkAPICase(projectID, caseID string) (fsm.ResourceMetadata, error) {
	return model.CheckApiCaseByCaseId(l.ctx, l.svcCtx.ApiCaseModel, projectID, caseID, "")
}

func (l *BaseLogic) checkInterfaceCase(projectID, caseID string) (fsm.ResourceMetadata, error) {
	return model.CheckInterfaceCaseByCaseId(l.ctx, l.svcCtx.InterfaceCaseModel, projectID, "", caseID, "")
}

func (l *BaseLogic) checkReviewers(in []string) (users []*userpb.UserInfo, accounts, emails []string) {
	cache := make(types.StringPlaceholderCache, len(in))
	users = make([]*userpb.UserInfo, 0, len(in))
	accounts = make([]string, 0, len(in))
	emails = make([]string, 0, len(in))

	for _, reviewer := range in {
		if _, ok := cache[reviewer]; ok {
			continue
		}

		resp, err := l.svcCtx.UserRPC.ViewUser(
			l.ctx, &userpb.GetUserReq{
				Account: reviewer,
			},
		)
		if err != nil {
			l.Errorf("failed to get user info by account, account: %s, err: %v", reviewer, err)
		} else if ui := resp.GetUserInfo(); ui != nil && ui.GetEnabled() && ui.GetEmail() != "" {
			users = append(users, ui)
			accounts = append(accounts, reviewer)
			emails = append(emails, ui.GetEmail())
		}

		cache[reviewer] = lang.Placeholder
	}

	return users, accounts, emails
}

func newFSMResource(
	resource fsm.ResourceMetadata, event common.ReviewResourceEvent, emails []string, result common.ReviewStatus,
	remark string,
) (fsm.Resource, error) {
	switch v := resource.(type) {
	case *model.ApiCase:
		return &apicaseservicelogic.ResourceOfApiCase{
			ApiCase: v,

			Event:  event,
			Emails: emails,
			Result: result,
			Remark: remark,
		}, nil
	case *model.InterfaceCase:
		return &interfacedefinitionservicelogic.ResourceOfInterfaceCase{
			InterfaceCase: v,

			Event:  event,
			Emails: emails,
			Result: result,
			Remark: remark,
		}, nil
	default:
		switch resource.ResourceType() {
		case common.ConstReviewResourceTypeSetupComponent, common.ConstReviewResourceTypeTeardownComponent, common.ConstReviewResourceTypeBusinessComponent:
			return nil, errorx.Errorf(
				errorx.DoesNotSupport,
				"the review function of component group is not currently supported, project_id: %s, resource_branch: %s, resource_type: %s, resource_id: %s, event: %s, emails: %s",
				resource.ProjectID(), resource.ResourceBranch(), resource.ResourceType(), resource.ResourceID(),
				event, jsonx.MarshalIgnoreError(emails),
			)
		default:
			return nil, errorx.Errorf(
				errorx.DoesNotSupport,
				"invalid resource type, project_id: %s, resource_branch: %s, resource_type: %s, resource_id: %s, event: %s, emails: %s",
				resource.ProjectID(), resource.ResourceBranch(), resource.ResourceType(), resource.ResourceID(),
				event, jsonx.MarshalIgnoreError(emails),
			)
		}
	}
}
