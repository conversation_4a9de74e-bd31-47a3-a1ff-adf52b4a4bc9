package reviewservicelogic

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sqlbuilder/rpc"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type SearchReviewRecordLogic struct {
	*BaseLogic
}

func NewSearchReviewRecordLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SearchReviewRecordLogic {
	return &SearchReviewRecordLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// SearchReviewRecord 搜索审核记录（包括申请记录）
func (l *SearchReviewRecordLogic) SearchReviewRecord(in *pb.SearchReviewRecordReq) (
	out *pb.SearchReviewRecordResp, err error,
) {
	out = &pb.SearchReviewRecordResp{}

	// validate the project_id in req
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, in.GetProjectId()); err != nil {
		return nil, err
	}

	selectBuilder, countBuilder := l.svcCtx.ReviewRecordModel.GenerateSearchReviewRecordSQLBuilder(
		model.SearchReviewRecordReq{
			ProjectID:  in.GetProjectId(),
			Condition:  in.GetCondition(),
			Pagination: in.GetPagination(),
			Sort:       rpc.ConvertSortFields(in.GetSort()),
		},
	)

	count, err := l.svcCtx.ReviewRecordModel.FindCountReviewRecords(l.ctx, countBuilder)
	if err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()),
			"failed to count review record with project_id[%s], error: %+v",
			in.GetProjectId(), err,
		)
	}
	out.TotalCount = uint64(count)
	out.TotalPage = 1

	records, err := l.svcCtx.ReviewRecordModel.FindReviewRecords(l.ctx, selectBuilder)
	if err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()),
			"failed to find review record with project_id[%s], error: %+v",
			in.GetProjectId(), err,
		)
	}

	out.Items = make([]*pb.ReviewRecord, 0, len(records))
	for _, record := range records {
		item := &pb.ReviewRecord{}
		if err = utils.Copy(item, record, l.converters...); err != nil {
			return nil, errors.Wrapf(
				errorx.Err(errorx.CopyToStructFailure, err.Error()),
				"failed to copy review record[%+v] to response, error: %+v",
				record, err,
			)
		}

		out.Items = append(out.Items, item)
	}

	pagination := in.GetPagination()
	if pagination != nil {
		out.CurrentPage = pagination.GetCurrentPage()
		out.PageSize = pagination.GetPageSize()
		out.TotalPage = (out.TotalCount + out.PageSize - 1) / out.PageSize // round up
	}

	return out, nil
}
