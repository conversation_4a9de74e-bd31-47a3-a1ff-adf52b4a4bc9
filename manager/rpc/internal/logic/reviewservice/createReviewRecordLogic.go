package reviewservicelogic

import (
	"context"
	"database/sql"
	"fmt"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"
	"github.com/zeromicro/go-zero/core/stores/sqlx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/caller"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/logic/fsm"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type CreateReviewRecordLogic struct {
	*BaseLogic
}

func NewCreateReviewRecordLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CreateReviewRecordLogic {
	return &CreateReviewRecordLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// CreateReviewRecord 申请审核
func (l *CreateReviewRecordLogic) CreateReviewRecord(in *pb.CreateReviewRecordReq) (
	out *pb.CreateReviewRecordResp, err error,
) {
	// validate the project_id in req
	project, err := model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, in.GetProjectId())
	if err != nil {
		return nil, err
	} else if project.ReviewEnabled == 0 {
		return nil, errorx.Errorf(
			errorx.ProhibitedBehavior,
			"the review function of the project has been disabled, therefore you cannot create a review record, project_id: %s",
			in.GetProjectId(),
		)
	}

	// validate the resource_id and resource_type in req
	resource, err := l.checkResource(in.GetProjectId(), in.GetResourceType(), in.GetResourceId())
	if err != nil {
		return nil, err
	}

	// validate the assigned_reviewers in req
	users, accounts, emails := l.checkReviewers(in.GetAssignedReviewers())
	if len(users) == 0 {
		return nil, errorx.Errorf(
			errorx.ProhibitedBehavior,
			"cannot to create a review record without assigning a valid reviewer, assigned_reviewers: %s",
			protobuf.MarshalJSONIgnoreError(in.GetAssignedReviewers()),
		)
	}

	var reviewID string

	key := fmt.Sprintf(
		"%s:%s:%s", common.ConstLockReviewRecordProjectIdResourceIdPrefix, in.GetProjectId(), in.GetResourceId(),
	)
	fn := func() error {
		records, err := l.svcCtx.ReviewRecordModel.FindFullReviewRecordsByResource(
			l.ctx, in.GetProjectId(), in.GetResourceType().ConvertTo(), in.GetResourceId(),
			[]common.ReviewStatus{common.ConstReviewStatusPending},
		)
		if err != nil {
			return errors.Wrapf(
				errorx.Err(errorx.DBError, err.Error()),
				"failed to find review record with project_id[%s], resource_type[%s] and resource_id[%s], error: %+v",
				in.GetProjectId(), in.GetResourceType().ConvertTo(), in.GetResourceId(), err,
			)
		} else if len(records) != 0 {
			return errorx.Errorf(
				errorx.ProhibitedBehavior,
				"there is an existing uncompleted review record for this resource, so another review cannot be applied, project_id: %s, resource_type: %s, resource_id: %s",
				in.GetProjectId(), in.GetResourceType().ConvertTo(), in.GetResourceId(),
			)
		}

		reviewID, err = l.generateReviewID(in.GetProjectId())
		if err != nil {
			return err
		}

		reviewRecord := &model.ReviewRecord{
			ProjectId:      in.GetProjectId(),
			ReviewId:       reviewID,
			ResourceType:   protobuf.GetEnumStringOf(in.GetResourceType()),
			ResourceId:     in.GetResourceId(),
			ResourceStatus: string(resource.ResourceState()),
			RemarkOfPending: sql.NullString{
				String: in.GetRemark(),
				Valid:  in.GetRemark() != "",
			},
			AssignedReviewers: jsonx.MarshalToStringIgnoreError(accounts),
			Status:            string(common.ConstReviewStatusPending),
			CreatedBy:         l.currentUser.Account,
			UpdatedBy:         l.currentUser.Account,
		}

		if err = l.svcCtx.ReviewRecordModel.Trans(
			l.ctx, func(context context.Context, session sqlx.Session) error {
				// create review record
				if _, err := l.svcCtx.ReviewRecordModel.InsertTX(context, session, reviewRecord); err != nil {
					return errors.Wrapf(
						errorx.Err(errorx.DBError, err.Error()),
						"failed to insert table[%s] with values[%+v], error: %+v",
						l.svcCtx.ReviewRecordModel.Table(), reviewRecord, err,
					)
				}

				// change resource status
				event := common.ConstReviewResourceEventApplyForReviewAfterImplementation // 待实现 -> 待审核
				if resource.ResourceState() == common.ConstResourceStateToBeMaintained {
					event = common.ConstReviewResourceEventApplyForReviewAfterMaintenance // 待维护 -> 待审核
				}

				r, err := newFSMResource(resource, event, emails, common.ConstReviewStatusPending, in.GetRemark())
				if err != nil {
					return err
				}
				if err = fsm.NewResourceFSM(context, l.svcCtx, r, session).Transition(); err != nil {
					return errors.Wrapf(
						err,
						"failed to transition the state of review resource, project_id: %s, resource_branch: %s, resource_type: %s, resource_id: %s, event: %s, emails: %s, error: %+v",
						r.ProjectID(), r.ResourceBranch(), r.ResourceType(), r.ResourceID(), event,
						jsonx.MarshalIgnoreError(emails), err,
					)
				}

				return nil
			},
		); err != nil {
			return err
		}

		return nil
	}
	if err = caller.LockDo(l.svcCtx.Redis, key, fn); err != nil {
		return nil, err
	}

	record, err := model.CheckReviewRecordByReviewID(l.ctx, l.svcCtx.ReviewRecordModel, in.GetProjectId(), reviewID)
	if err != nil {
		return nil, err
	}
	out = &pb.CreateReviewRecordResp{Record: &pb.ReviewRecord{}}
	if err = utils.Copy(out.Record, record, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy review record[%+v] to response, error: %+v",
			record, err,
		)
	}

	return out, nil
}
