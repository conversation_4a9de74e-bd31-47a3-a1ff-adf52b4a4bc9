package reviewservicelogic

import (
	"context"
	"database/sql"
	"fmt"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"
	"github.com/zeromicro/go-zero/core/stores/sqlx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/caller"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/logic/fsm"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type RevokeReviewRecordLogic struct {
	*BaseLogic
}

func NewRevokeReviewRecordLogic(ctx context.Context, svcCtx *svc.ServiceContext) *RevokeReviewRecordLogic {
	return &RevokeReviewRecordLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// RevokeReviewRecord 撤销审核
func (l *RevokeReviewRecordLogic) RevokeReviewRecord(in *pb.RevokeReviewRecordReq) (
	out *pb.RevokeReviewRecordResp, err error,
) {
	// validate the project_id in req
	project, err := model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, in.GetProjectId())
	if err != nil {
		return nil, err
	} else if project.ReviewEnabled == 0 {
		return nil, errorx.Errorf(
			errorx.ProhibitedBehavior,
			"the review function of the project has been disabled, therefore you cannot revoke this review record, project_id: %s, review_id: %s",
			in.GetProjectId(), in.GetReviewId(),
		)
	}

	var record *model.FullReviewRecord

	key := fmt.Sprintf(
		"%s:%s:%s", common.ConstLockReviewRecordProjectIdReviewIdPrefix, in.GetProjectId(), in.GetReviewId(),
	)
	fn := func() error {
		record, err = model.CheckReviewRecordByReviewID(
			l.ctx, l.svcCtx.ReviewRecordModel, in.GetProjectId(), in.GetReviewId(),
		)
		if err != nil {
			return err
		} else if record.CreatedBy != l.currentUser.Account {
			return errorx.Errorf(
				errorx.ProhibitedBehavior,
				"cannot revoke this review record that was not created by you, project_id: %s, review_id: %s, created_by: %s",
				in.GetProjectId(), in.GetReviewId(), record.CreatedBy,
			)
		} else if record.Status != string(common.ConstReviewStatusPending) {
			return errorx.Errorf(
				errorx.ProhibitedBehavior,
				"the review record has been completed, therefore it cannot be revoked, project_id: %s, review_id: %s, status: %s",
				in.GetProjectId(), in.GetReviewId(), record.Status,
			)
		}

		resource, err := l.checkResource(
			in.GetProjectId(), logic.ConvertStringToReviewResourceType(record.ResourceType), record.ResourceId,
		)
		if err != nil {
			return err
		}

		if err := l.svcCtx.ReviewRecordModel.Trans(
			l.ctx, func(context context.Context, session sqlx.Session) error {
				// 1. update review record status from `PENDING` to `REVOKED`
				record.RemarkOfRevoked = sql.NullString{
					String: in.GetRemark(),
					Valid:  in.GetRemark() != "",
				}
				record.Status = string(common.ConstReviewStatusRevoked)
				record.UpdatedBy = l.currentUser.Account

				if _, err := l.svcCtx.ReviewRecordModel.UpdateTX(context, session, &record.ReviewRecord); err != nil {
					return errors.Wrapf(
						errorx.Err(errorx.DBError, err.Error()),
						"failed to update table[%s] with values[%+v], error: %+v",
						l.svcCtx.ReviewRecordModel.Table(), record.ReviewRecord, err,
					)
				}

				// 2. update resource status from `REVIEW_PENDING` to previous status
				event := common.ConstReviewResourceEventRevokeTheReviewAfterImplementation
				if record.ResourceStatus == string(common.ConstResourceStateToBeMaintained) {
					event = common.ConstReviewResourceEventRevokeTheReviewAfterMaintenance
				}

				r, err := newFSMResource(resource, event, nil, common.ConstReviewStatusPending, in.GetRemark())
				if err != nil {
					return err
				}
				if err = fsm.NewResourceFSM(context, l.svcCtx, r, session).Transition(); err != nil {
					return errors.Wrapf(
						err,
						"failed to transition the state of review resource, project_id: %s, resource_branch: %s, resource_type: %s, resource_id: %s, event: %s, emails: %s, error: %+v",
						r.ProjectID(), r.ResourceBranch(), r.ResourceType(), r.ResourceID(), event,
						jsonx.MarshalIgnoreError(nil), err,
					)
				}

				return nil
			},
		); err != nil {
			return err
		}

		return nil
	}
	if err = caller.LockDo(l.svcCtx.Redis, key, fn); err != nil {
		return nil, err
	}

	out = &pb.RevokeReviewRecordResp{Record: &pb.ReviewRecord{}}
	if err = utils.Copy(out.Record, record, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy review record[%+v] to response, error: %+v",
			record, err,
		)
	}

	return out, nil
}
