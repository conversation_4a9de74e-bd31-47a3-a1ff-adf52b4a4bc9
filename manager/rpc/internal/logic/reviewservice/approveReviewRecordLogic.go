package reviewservicelogic

import (
	"context"
	"database/sql"
	"fmt"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"
	"github.com/zeromicro/go-zero/core/stores/sqlx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/caller"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/logic/fsm"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type ApproveReviewRecordLogic struct {
	*BaseLogic
}

func NewApproveReviewRecordLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ApproveReviewRecordLogic {
	return &ApproveReviewRecordLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// ApproveReviewRecord 审批审核
func (l *ApproveReviewRecordLogic) ApproveReviewRecord(in *pb.ApproveReviewRecordReq) (
	out *pb.ApproveReviewRecordResp, err error,
) {
	// validate the project_id in req
	project, err := model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, in.GetProjectId())
	if err != nil {
		return nil, err
	} else if project.ReviewEnabled == 0 {
		return nil, errorx.Errorf(
			errorx.ProhibitedBehavior,
			"the review function of the project has been disabled, therefore you cannot review this review record, project_id: %s, review_id: %s",
			in.GetProjectId(), in.GetReviewId(),
		)
	}

	// validate the result and remark in req
	if in.GetResult() == pb.ReviewStatus_REVIEW_STATUS_REJECTED && in.GetRemark() == "" {
		return nil, errorx.Errorf(
			errorx.ProhibitedBehavior,
			"when the review is rejected, the review remark must be filled in, project_id: %s, review_id: %s",
			in.GetProjectId(), in.GetReviewId(),
		)
	}

	var record *model.FullReviewRecord

	key := fmt.Sprintf(
		"%s:%s:%s", common.ConstLockReviewRecordProjectIdReviewIdPrefix, in.GetProjectId(), in.GetReviewId(),
	)
	fn := func() error {
		record, err = model.CheckReviewRecordByReviewID(
			l.ctx, l.svcCtx.ReviewRecordModel, in.GetProjectId(), in.GetReviewId(),
		)
		if err != nil {
			return err
		} else if record.Status != string(common.ConstReviewStatusPending) {
			return errorx.Errorf(
				errorx.ProhibitedBehavior,
				"the review record has been completed, therefore it cannot be reviewed, project_id: %s, review_id: %s, status: %s",
				in.GetProjectId(), in.GetReviewId(), record.Status,
			)
		}

		resource, err := l.checkResource(
			in.GetProjectId(), logic.ConvertStringToReviewResourceType(record.ResourceType), record.ResourceId,
		)
		if err != nil {
			return err
		}

		_, _, emails := l.checkReviewers([]string{resource.Maintainer()})

		if err := l.svcCtx.ReviewRecordModel.Trans(
			l.ctx, func(context context.Context, session sqlx.Session) error {
				// 1. update review record status from `PENDING` to `APPROVED` or `REJECTED`
				record.RemarkOfReviewed = sql.NullString{
					String: in.GetRemark(),
					Valid:  in.GetRemark() != "",
				}
				record.Status = protobuf.GetEnumStringOf(in.GetResult())
				record.UpdatedBy = l.currentUser.Account

				if _, err := l.svcCtx.ReviewRecordModel.UpdateTX(context, session, &record.ReviewRecord); err != nil {
					return errors.Wrapf(
						errorx.Err(errorx.DBError, err.Error()),
						"failed to update table[%s] with values[%+v], error: %+v",
						l.svcCtx.ReviewRecordModel.Table(), record.ReviewRecord, err,
					)
				}

				// 2. update resource status from `PENDING_REVIEW` to `PUBLISHED` or `TO_BE_MAINTAINED`
				event := common.ConstReviewResourceEventReviewApproved
				if in.GetResult() == pb.ReviewStatus_REVIEW_STATUS_REJECTED {
					event = common.ConstReviewResourceEventReviewRejected
				}

				r, err := newFSMResource(resource, event, emails, in.GetResult().ConvertTo(), in.GetRemark())
				if err != nil {
					return err
				}
				if err = fsm.NewResourceFSM(context, l.svcCtx, r, session).Transition(); err != nil {
					return errors.Wrapf(
						err,
						"failed to transition the state of review resource, project_id: %s, resource_branch: %s, resource_type: %s, resource_id: %s, event: %s, emails: %s, error: %+v",
						r.ProjectID(), r.ResourceBranch(), r.ResourceType(), r.ResourceID(), event,
						jsonx.MarshalIgnoreError(emails), err,
					)
				}

				return nil
			},
		); err != nil {
			return err
		}

		return nil
	}
	if err = caller.LockDo(l.svcCtx.Redis, key, fn); err != nil {
		return nil, err
	}

	out = &pb.ApproveReviewRecordResp{Record: &pb.ReviewRecord{}}
	if err = utils.Copy(out.Record, record, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy review record[%+v] to response, error: %+v",
			record, err,
		)
	}

	return out, nil
}
