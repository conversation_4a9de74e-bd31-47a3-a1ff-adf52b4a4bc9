package notifyservicelogic

import (
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zyedidia/generic"
	"github.com/zyedidia/generic/hashmap"
	"github.com/zyedidia/generic/set"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

const separator = "||"

type CreatePlanNotifyLogic struct {
	*BaseLogic
}

func NewCreatePlanNotifyLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CreatePlanNotifyLogic {
	return &CreatePlanNotifyLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// CreatePlanNotify 创建计划通知对象
func (l *CreatePlanNotifyLogic) CreatePlanNotify(in *pb.CreatePlanNotifyReq) (
	resp *pb.CreatePlanNotifyResp, err error,
) {
	// validate the project_id in req
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, in.GetProjectId()); err != nil {
		return nil, err
	}

	resp = &pb.CreatePlanNotifyResp{}
	var receiverItems []*model.ReceiverItems
	if err = utils.Copy(&receiverItems, in.ReceiverInfos); err != nil {
		return nil, err
	}

	for _, v := range receiverItems {
		v.NotifyId, err = l.generateNotifyId(in.GetPlanId(), nil)
		if err != nil {
			return nil, err
		}

		resp.Ids = append(resp.Ids, v.NotifyId)
	}

	_, err = l.svcCtx.NotifyModel.BulkInsert(
		model.CreateNotifyReq{
			ProjectId:     in.ProjectId,
			PlanId:        in.PlanId,
			NotifyType:    in.GetNotifyType().String(),
			NotifyMode:    in.GetNotifyMode().String(),
			ReceiverInfos: receiverItems,
			CreatedBy:     l.currentUser.Account,
			UpdatedBy:     l.currentUser.Account,
		},
	)
	if err != nil {
		return nil, err
	}

	return resp, nil
}

func (l *CreatePlanNotifyLogic) CreatePlanNotifyForInternal(
	ctx context.Context, session sqlx.Session, nr types.CreateOrUpdateNotifyReference,
) error {
	if ctx == nil {
		ctx = l.ctx
	}

	records, err := l.svcCtx.NotifyModel.FindByPlanID(ctx, nr.ProjectID, nr.PlanID)
	if err != nil {
		return errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()),
			"failed to find plan notify, project_id: %s, plan_id: %s, error: %+v",
			nr.ProjectID, nr.PlanID, err,
		)
	}

	fromMap := hashmap.New[string, []*model.Notify](
		uint64(len(records)), generic.Equals[string], generic.HashString,
	)
	fromSet := set.NewHashset[string](uint64(len(records)), generic.Equals[string], generic.HashString)
	for _, record := range records {
		if record == nil || record.Id == 0 {
			continue
		}

		key := strings.Join([]string{record.ReceiverName, record.Receiver}, separator)
		val, ok := fromMap.Get(key)
		if !ok {
			fromMap.Put(key, []*model.Notify{record})
			fromSet.Put(key)
		} else {
			val = append(val, record)
			fromMap.Put(key, val)
		}
	}

	toSet := set.NewHashset[string](uint64(len(nr.NotifyItems)), generic.Equals[string], generic.HashString)
	for _, item := range nr.NotifyItems {
		if item == nil {
			continue
		}

		toSet.Put(fmt.Sprintf("%s%s%s", item.GetReceiverName(), separator, item.GetReceiver()))
	}

	removes := fromSet.Difference(toSet)
	increases := toSet.Difference(fromSet)

	now := time.Now()
	cache := make(types.StringPlaceholderCache)
	items := make([]*model.Notify, 0, increases.Size())
	for _, key := range increases.Keys() {
		ss := strings.Split(key, separator)
		if len(ss) != 2 {
			continue
		}

		notifyID, err := l.generateNotifyId(nr.ProjectID, cache)
		if err != nil {
			return err
		}

		items = append(
			items, &model.Notify{
				ProjectId:    nr.ProjectID,
				PlanId:       nr.PlanID,
				NotifyId:     notifyID,
				NotifyMode:   nr.NotifyMode.String(),
				NotifyType:   nr.NotifyType.String(),
				ReceiverName: ss[0],
				Receiver:     ss[1],
				CreatedBy:    l.currentUser.Account,
				UpdatedBy:    l.currentUser.Account,
				CreatedAt:    now,
				UpdatedAt:    now,
			},
		)
	}

	fn := func(context context.Context, session sqlx.Session) error {
		for _, key := range removes.Keys() {
			vals, ok := fromMap.Get(key)
			if !ok {
				continue
			}

			for _, val := range vals {
				if val == nil || val.Id == 0 {
					continue
				}

				if err := l.svcCtx.NotifyModel.LogicDelete(context, session, val.Id); err != nil {
					return errors.Wrapf(
						errorx.Err(errorx.DBError, err.Error()),
						"failed to delete item from table, table: %s, item: %s, error: %+v",
						l.svcCtx.NotifyModel.Table(), jsonx.MarshalIgnoreError(val), err,
					)
				}
			}
		}

		if _, err := l.svcCtx.NotifyModel.BatchInsert(ctx, session, items); err != nil {
			return errors.Wrapf(
				errorx.Err(errorx.DBError, err.Error()),
				"failed to batch insert values to table, table: %s, values: %s, error: %+v",
				l.svcCtx.NotifyModel.Table(), jsonx.MarshalIgnoreError(items), err,
			)
		}

		return nil
	}
	if session == nil {
		return l.svcCtx.NotifyModel.Trans(ctx, fn)
	}

	return fn(ctx, session)
}
