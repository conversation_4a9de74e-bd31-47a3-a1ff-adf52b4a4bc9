package notifyservicelogic

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type GetPlanNotifyLogic struct {
	*BaseLogic
}

func NewGetPlanNotifyLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetPlanNotifyLogic {
	return &GetPlanNotifyLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *GetPlanNotifyLogic) GetPlanNotify(in *pb.GetPlanNotifyReq) (resp *pb.GetPlanNotifyResp, err error) {
	resp = &pb.GetPlanNotifyResp{}

	selectBuilder, _ := l.svcCtx.NotifyModel.SearchNotifySqlBuilder(model.SearchNotifyReq{
		ProjectId: in.GetProjectId(),
		PlanId:    in.GetPlanId(),
	})

	notifys, err := l.svcCtx.NotifyModel.FindNotify(l.ctx, selectBuilder)
	if err != nil {
		return nil, errors.Wrapf(errorx.Err(errorx.DBError, err.Error()), "failed to find notify with project_id[%s], error: %+v", in.GetProjectId(), err)
	}

	resp.Items = make([]*pb.NotifyItem, 0, len(notifys))
	for _, notify := range notifys {
		item := &pb.NotifyItem{}
		if err = utils.Copy(item, notify, l.converters...); err != nil {
			return nil, errors.Wrapf(errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy notify [%+v] to response, error: %+v", notify, err)
		}

		resp.Items = append(resp.Items, item)
	}

	return resp, nil
}
