package notifyservicelogic

import (
	"context"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/lang"
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/mr"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/userinfo"
	qetutils "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/utils"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/types"
)

type BaseLogic struct {
	logx.Logger
	ctx         context.Context
	svcCtx      *svc.ServiceContext
	currentUser *userinfo.UserInfo

	converters []qetutils.TypeConverter
}

func newBaseLogic(ctx context.Context, svcCtx *svc.ServiceContext) *BaseLogic {
	return &BaseLogic{
		Logger:      logx.WithContext(ctx),
		ctx:         ctx,
		svcCtx:      svcCtx,
		currentUser: userinfo.FromContext(ctx),

		converters: []qetutils.TypeConverter{
			logic.StringToNotifyMode(),
			logic.NotifyModeToString(),
			logic.StringToNotifyType(),
			logic.NotifyTypeToString(),
		},
	}
}

func (l *BaseLogic) generateNotifyId(projectId string, cache types.StringPlaceholderCache) (string, error) {
	g := qetutils.NewUniqueIdGenerator(
		qetutils.WithGenerateFunc(utils.GenNotifyId), qetutils.WithIsUniqueFunc(
			func(id string) bool {
				if cache != nil {
					if _, ok := cache[id]; ok {
						// 本次生成的 `document_id` 已存在于记录器中，需要重新创建
						return false
					}
				}

				defer func() {
					if cache != nil {
						cache[id] = lang.Placeholder
					}
				}()

				r, err := l.svcCtx.NotifyModel.FindOneByProjectIdNotifyId(l.ctx, projectId, id)

				if errors.Is(err, model.ErrNotFound) || r == nil {
					return true
				}
				return false
			},
		),
	)
	notifyID := g.Next()
	if notifyID == "" {
		return "", errors.WithStack(
			errorx.Err(
				errorx.GenerateUniqueIdFailure, "failed to generate notify id, please try it later",
			),
		)
	}

	return notifyID, nil
}

func (l *BaseLogic) removeNotify(ctx context.Context, session sqlx.Session, req RemoveNotifyReq) error {
	if ctx == nil {
		ctx = l.ctx
	}

	NotifyIds := stringx.Distinct(req.NotifyIds)

	workers := len(NotifyIds)
	if workers > common.ConstMRMaxWorkers {
		workers = common.ConstMRMaxWorkers
	}

	if workers == 0 {
		return nil
	}

	return mr.MapReduceVoid[string, any](
		func(source chan<- string) {
			for _, notifyId := range NotifyIds {
				source <- notifyId
			}
		}, func(item string, writer mr.Writer[any], cancel func(error)) {
			var err error
			defer func() {
				if err != nil {
					cancel(err)
				}
			}()

			notifyId := item

			if _, err = l.svcCtx.NotifyModel.RemoveByNotifyId(
				ctx, session, req.ProjectId, req.PlanId, notifyId,
			); err != nil {
				err = errors.Wrapf(
					errorx.Err(errorx.DBError, err.Error()),
					"failed to remove notify by project_id[%s], plan_id[%s], notify_id[%s], error: %+v", req.ProjectId,
					req.PlanId, notifyId, err,
				)
				return
			}
		}, func(pipe <-chan any, cancel func(error)) {
		}, mr.WithContext(ctx), mr.WithWorkers(workers),
	)
}
