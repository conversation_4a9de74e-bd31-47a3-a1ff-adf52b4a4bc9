package notifyservicelogic

import (
	"context"
	"fmt"
	"time"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/redislock"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type ModifyPlanNotifyLogic struct {
	*BaseLogic
}

func NewModifyPlanNotifyLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ModifyPlanNotifyLogic {
	return &ModifyPlanNotifyLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// ModifyPlanNotify 修改计划通知对象
func (l *ModifyPlanNotifyLogic) ModifyPlanNotify(in *pb.ModifyPlanNotifyReq) (
	resp *pb.ModifyPlanNotifyResp, err error,
) {
	// validate the project_id in req
	origin, err := model.CheckNotifyByNotifyId(l.ctx, l.svcCtx.NotifyModel, in.GetProjectId(), in.GetNotifyId())
	if err != nil {
		return nil, err
	}

	// acquire redis lock
	key := fmt.Sprintf("%s:%s", common.ConstLockNotifyProjectIDNotifyIdPrefix, in.GetNotifyId())
	lock, err := redislock.NewRedisLockAndAcquire(l.svcCtx.Redis, key, redislock.WithExpire(common.ConstLockExpireTime))
	if err != nil {
		return nil, err
	}
	defer func() {
		// release redis lock
		e := lock.Release()
		if e != nil {
			l.Logger.Error(e)
			if re, ok := errorx.RootError(e); ok && re.Code() != errorx.ReleaseRedisLockFailure && err == nil {
				err = e
			}
		}
	}()

	origin.NotifyMode = in.GetNotifyMode().String()
	origin.NotifyType = in.GetNotifyType().String()
	origin.ReceiverName = in.ReceiverName
	origin.Receiver = in.Receiver
	origin.UpdatedAt = time.Now()
	origin.UpdatedBy = l.currentUser.Account
	_, err = l.svcCtx.NotifyModel.Update(l.ctx, nil, origin)
	if err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()), "failed to update table[%s] with values[%+v], error: %+v",
			l.svcCtx.NotifyModel.Table(), origin, err,
		)
	}

	return &pb.ModifyPlanNotifyResp{}, nil
}
