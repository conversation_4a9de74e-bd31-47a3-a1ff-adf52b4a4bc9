package notifyservicelogic

import (
	"context"
	"fmt"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/lang"
	"github.com/zeromicro/go-zero/core/stores/sqlx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/redislock"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type RemovePlanNotifyLogic struct {
	*BaseLogic
}

func NewRemovePlanNotifyLogic(ctx context.Context, svcCtx *svc.ServiceContext) *RemovePlanNotifyLogic {
	return &RemovePlanNotifyLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *RemovePlanNotifyLogic) RemovePlanNotify(in *pb.RemovePlanNotifyReq) (
	resp *pb.RemovePlanNotifyResp, err error,
) {
	// validate the project_id in req
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, in.GetProjectId()); err != nil {
		return nil, err
	}

	selectBuilder, _ := l.svcCtx.NotifyModel.SearchNotifySqlBuilder(
		model.SearchNotifyReq{
			ProjectId: in.GetProjectId(),
			PlanId:    in.GetPlanId(),
		},
	)
	notifys, err := l.svcCtx.NotifyModel.FindNotify(l.ctx, selectBuilder)
	if err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()),
			"failed to find notify with project_id[%s] and plan_id[%s], error: %+v", in.GetProjectId(), in.GetPlanId(),
			err,
		)
	}

	// 记录已在计划中的通知
	cache := make(types.StringPlaceholderCache, len(notifys))
	for _, notify := range notifys {
		k := notify.NotifyId
		if _, ok := cache[k]; ok {
			continue
		} else {
			cache[k] = lang.Placeholder
		}
	}

	// acquire redis lock
	key := fmt.Sprintf("%s:%s:%s", common.ConstLockNotifyProjectIDPlanIdPrefix, in.GetProjectId(), in.GetPlanId())
	lock, err := redislock.NewRedisLockAndAcquire(l.svcCtx.Redis, key, redislock.WithExpire(common.ConstLockExpireTime))
	if err != nil {
		return nil, err
	}
	defer func() {
		// release redis lock
		e := lock.Release()
		if e != nil {
			// l.Logger.Error(e)
			if re, ok := errorx.RootError(e); ok && re.Code() != errorx.ReleaseRedisLockFailure && err == nil {
				err = e
			}
		}
	}()

	if err = l.svcCtx.NotifyModel.Trans(
		l.ctx, func(context context.Context, session sqlx.Session) error {
			removeNotifyIdInPlan := make(
				[]string, 0, constants.ConstDefaultMakeSliceSize,
			) // 预置切片容量为`constants.ConstDefaultMakeSliceSize`，减少切片扩容的情况
			// 不在计划中的集合无需移除
			for _, notifyId := range in.GetNotifyIds() {
				if _, ok := cache[notifyId]; !ok {
					continue
				}
				removeNotifyIdInPlan = append(removeNotifyIdInPlan, notifyId)
			}

			if err := l.removeNotify(
				context, session, RemoveNotifyReq{
					ProjectId: in.GetProjectId(),
					PlanId:    in.GetPlanId(),
					NotifyIds: removeNotifyIdInPlan,
				},
			); err != nil {
				return err
			}

			return nil
		},
	); err != nil {
		return nil, err
	}

	return &pb.RemovePlanNotifyResp{}, nil
}
