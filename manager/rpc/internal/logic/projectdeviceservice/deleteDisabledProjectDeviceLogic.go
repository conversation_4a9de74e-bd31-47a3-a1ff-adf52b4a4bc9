package projectdeviceservicelogic

import (
	"context"
	"fmt"
	"slices"

	"github.com/zeromicro/go-zero/core/mr"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/caller"

	commonpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type DeleteDisabledProjectDeviceLogic struct {
	*BaseLogic
}

func NewDeleteDisabledProjectDeviceLogic(
	ctx context.Context, svcCtx *svc.ServiceContext,
) *DeleteDisabledProjectDeviceLogic {
	return &DeleteDisabledProjectDeviceLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// DeleteDisabledProjectDevice 删除无效（设备中心不存在）的项目设备
func (l *DeleteDisabledProjectDeviceLogic) DeleteDisabledProjectDevice(in *pb.DeleteDisabledProjectDeviceReq) (
	out *pb.DeleteDisabledProjectDeviceResp, err error,
) {
	projectID := in.GetProjectId()

	// validate the project_id in req
	_, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, projectID)
	if err != nil {
		return nil, err
	}

	key := fmt.Sprintf("%s:%s", common.ConstLockProjectDeviceProjectID, in.GetProjectId())
	fn := func() error {
		return l.DeleteDisabledProjectDeviceForInternal(projectID)
	}
	if err = caller.LockDo(l.svcCtx.Redis, key, fn); err != nil {
		return nil, err
	}

	return &pb.DeleteDisabledProjectDeviceResp{}, nil
}

func (l *DeleteDisabledProjectDeviceLogic) DeleteDisabledProjectDeviceForInternal(projectID string) error {
	set1, _, err := l.getAllDevices()
	if err != nil {
		return err
	}

	set2, _, err := l.getProjectDevices(projectID, commonpb.DeviceUsage_DU_NULL)
	if err != nil {
		return err
	}

	disabled := set2.Difference(set1)
	for _, udid := range disabled.Keys() {
		records, err := l.svcCtx.ProjectDeviceReferenceModel.FindByUDID(l.ctx, projectID, udid)
		if err != nil {
			l.Errorf("failed to find project device reference, project_id: %s, udid: %s, err: %v", projectID, udid, err)
			continue
		}

		_ = mr.MapReduceVoid[*model.ProjectDeviceReferenceRelationship, any](
			func(source chan<- *model.ProjectDeviceReferenceRelationship) {
				for _, record := range records {
					if record == nil || record.Id == 0 {
						continue
					}

					source <- record
				}
			},
			func(item *model.ProjectDeviceReferenceRelationship, writer mr.Writer[any], cancel func(error)) {
				if item == nil {
					return
				}

				var (
					plan model.ProjectDevicePlan
					err  error
				)

				switch item.ReferenceType {
				case common.ConstReferenceTypeUIPlan:
					plan, err = l.svcCtx.UiPlanModel.FindOneByProjectIdPlanId(
						l.ctx, item.ProjectId, item.ReferenceId,
					)
					if err != nil {
						l.Errorf(
							"failed to find ui plan, project_id: %s, plan_id: %s, err: %v",
							item.ProjectId, item.ReferenceId, err,
						)
						return
					}
				case common.ConstReferenceTypeStabilityPlan:
					plan, err = l.svcCtx.StabilityPlanModel.FindOneByProjectIdPlanId(
						l.ctx, item.ProjectId, item.ReferenceId,
					)
					if err != nil {
						l.Errorf(
							"failed to find stability plan, project_id: %s, plan_id: %s, err: %v",
							item.ProjectId, item.ReferenceId, err,
						)
						return
					}
				default:
					l.Warnf("unknown reference type: %s", item.ReferenceType)
					return
				}

				devices := plan.GetDevices()
				if len(devices) == 0 {
					return
				}

				for i := 0; i < len(devices); i++ {
					if udid == devices[i] {
						devices = slices.Delete(devices, i, i+1)
						break
					}
				}
				plan.SetDevices(devices)

				writer.Write(plan)
			},
			func(pipe <-chan any, cancel func(error)) {
				for item := range pipe {
					switch v := item.(type) {
					case *model.UiPlan:
						if _, err := l.svcCtx.UiPlanModel.Update(l.ctx, nil, v); err != nil {
							l.Errorf(
								"failed to update ui plan, project_id: %s, plan_id: %s, err: %v",
								v.ProjectId, v.PlanId, err,
							)
						}
					case *model.StabilityPlan:
						if _, err := l.svcCtx.StabilityPlanModel.Update(l.ctx, nil, v); err != nil {
							l.Errorf(
								"failed to update stability plan, project_id: %s, plan_id: %s, err: %v",
								v.ProjectId, v.PlanId, err,
							)
						}
					default:
						l.Warnf("unknown type: %T", v)
					}
				}
			},
		)

		if _, err = l.svcCtx.ProjectDeviceReferenceModel.RemoveByUDID(l.ctx, nil, projectID, udid); err != nil {
			l.Errorf(
				"failed to delete project device reference, project_id: %s, udid: %s, err: %v", projectID, udid, err,
			)
		}

		if _, err = l.svcCtx.ProjectDeviceModel.DeleteByUDID(l.ctx, nil, udid); err != nil {
			l.Errorf("failed to delete project device, project_id: %s, udid: %s, err: %v", projectID, udid, err)
		}
	}

	return nil
}
