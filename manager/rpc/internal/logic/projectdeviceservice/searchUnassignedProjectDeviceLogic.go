package projectdeviceservicelogic

import (
	"context"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sqlbuilder/rpc"

	commonpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
	devicehubcommon "gitlab.ttyuyin.com/TestDevelopment/probe-backend/devicehub/common"
	devicehubpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/devicehub/rpc/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type SearchUnassignedProjectDeviceLogic struct {
	*BaseLogic
}

func NewSearchUnassignedProjectDeviceLogic(
	ctx context.Context, svcCtx *svc.ServiceContext,
) *SearchUnassignedProjectDeviceLogic {
	return &SearchUnassignedProjectDeviceLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// SearchUnassignedProjectDevice 搜索未分配到项目的设备
func (l *SearchUnassignedProjectDeviceLogic) SearchUnassignedProjectDevice(in *pb.SearchUnassignedProjectDeviceReq) (
	out *pb.SearchUnassignedProjectDeviceResp, err error,
) {
	var (
		projectID  = in.GetProjectId()
		condition  = in.GetCondition()
		pagination = in.GetPagination()
		sort       = in.GetSort()
	)

	out = &pb.SearchUnassignedProjectDeviceResp{}

	// validate the project_id in req
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, projectID); err != nil {
		return nil, err
	}

	ids, _, err := l.getProjectDevices(projectID, commonpb.DeviceUsage_DU_NULL)
	if err != nil {
		return nil, err
	}

	devices, err := l.svcCtx.DeviceHubRPC.SearchDevice(
		l.ctx, &devicehubpb.SearchDeviceReq{
			Condition: &rpc.Condition{
				Group: &rpc.GroupCondition{
					Relationship: constants.AND,
					Conditions: []*rpc.Condition{
						{
							Single: &rpc.SingleCondition{
								Field:   string(devicehubcommon.DeviceFieldOfUDID),
								Compare: constants.DBNotIn,
								In:      ids.Keys(),
							},
						},
						condition,
					},
				},
			},
			Pagination: pagination,
			Sort:       sort,
		},
	)
	if err != nil {
		return nil, err
	}

	out.Items = make([]*pb.ProjectDevice, 0, len(devices.GetItems()))
	for _, item := range devices.GetItems() {
		out.Items = append(
			out.Items, &pb.ProjectDevice{
				Device:    item,
				ProjectId: projectID,
				Usage:     commonpb.DeviceUsage_DU_NULL,
			},
		)
	}

	if pagination != nil {
		out.CurrentPage = pagination.GetCurrentPage()
		out.PageSize = pagination.GetPageSize()
		out.TotalPage = (out.TotalCount + out.PageSize - 1) / out.PageSize // round up
	}

	return out, nil
}
