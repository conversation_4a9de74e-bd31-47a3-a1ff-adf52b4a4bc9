package projectdeviceservicelogic

import (
	"context"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sqlbuilder/rpc"

	commonpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
	devicehubcommon "gitlab.ttyuyin.com/TestDevelopment/probe-backend/devicehub/common"
	devicehubpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/devicehub/rpc/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type SearchProjectDeviceLogic struct {
	*BaseLogic
}

func NewSearchProjectDeviceLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SearchProjectDeviceLogic {
	return &SearchProjectDeviceLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// SearchProjectDevice 搜索项目设备
func (l *SearchProjectDeviceLogic) SearchProjectDevice(in *pb.SearchProjectDeviceReq) (
	out *pb.SearchProjectDeviceResp, err error,
) {
	var (
		projectID  = in.GetProjectId()
		usage      = in.GetUsage()
		condition  = in.GetCondition()
		pagination = in.GetPagination()
		sort       = in.GetSort()
	)

	out = &pb.SearchProjectDeviceResp{}

	// validate the project_id in req
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, projectID); err != nil {
		return nil, err
	}

	ids, projectDevices, err := l.getProjectDevices(projectID, usage)
	if err != nil {
		return nil, err
	}

	count := projectDevices.Size()
	out.Items = make([]*pb.ProjectDevice, 0, count)
	if count == 0 {
		out.TotalCount = uint64(count)
		out.TotalPage = 1
		return out, nil
	}

	devices, err := l.svcCtx.DeviceHubRPC.SearchDevice(
		l.ctx, &devicehubpb.SearchDeviceReq{
			Condition: &rpc.Condition{
				Group: &rpc.GroupCondition{
					Relationship: constants.AND,
					Conditions: []*rpc.Condition{
						{
							Single: &rpc.SingleCondition{
								Field:   string(devicehubcommon.DeviceFieldOfUDID),
								Compare: constants.DBIn,
								In:      ids.Keys(),
							},
						},
						condition,
					},
				},
			},
			Pagination: pagination,
			Sort:       sort,
		},
	)
	if err != nil {
		return nil, err
	}

	out.CurrentPage = devices.GetCurrentPage()
	out.PageSize = devices.GetPageSize()
	out.TotalCount = devices.GetTotalCount()
	out.TotalPage = devices.GetTotalPage()

	for _, device := range devices.GetItems() {
		v, ok := projectDevices.Get(device.GetUdid())
		if !ok {
			continue
		}
		if _, ok = commonpb.DeviceUsage_name[int32(v.Usage)]; !ok {
			continue
		}

		out.Items = append(
			out.Items, &pb.ProjectDevice{
				Device: device,

				ProjectId: projectID,
				Usage:     commonpb.DeviceUsage(v.Usage),
			},
		)
	}

	return out, nil
}
