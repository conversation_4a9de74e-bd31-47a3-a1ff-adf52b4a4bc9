package projectdeviceservicelogic

import (
	"context"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sqlbuilder/rpc"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type SearchProjectDeviceReferenceLogic struct {
	*BaseLogic
}

func NewSearchProjectDeviceReferenceLogic(
	ctx context.Context, svcCtx *svc.ServiceContext,
) *SearchProjectDeviceReferenceLogic {
	return &SearchProjectDeviceReferenceLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// SearchProjectDeviceReference 搜索项目设备引用详情
func (l *SearchProjectDeviceReferenceLogic) SearchProjectDeviceReference(in *pb.SearchProjectDeviceReferenceReq) (
	out *pb.SearchProjectDeviceReferenceResp, err error,
) {
	out = &pb.SearchProjectDeviceReferenceResp{}

	// validate the project_id in req
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, in.GetProjectId()); err != nil {
		return nil, err
	}

	// validate the udid in req
	if _, err = model.CheckProjectDeviceByUDID(
		l.ctx, l.svcCtx.ProjectDeviceModel, in.GetProjectId(), in.GetUdid(),
	); err != nil {
		return nil, err
	}

	req := model.SearchProjectDeviceReferenceReq{
		BaseSearchReq: model.BaseSearchReq{
			ProjectID:  in.GetProjectId(),
			Condition:  in.GetCondition(),
			Pagination: in.GetPagination(),
			Sort:       rpc.ConvertSortFields(in.GetSort()),
		},
		UDID: in.GetUdid(),
	}
	count, err := l.svcCtx.ProjectDeviceModel.FindCountReferenceBySearchReq(l.ctx, req)
	if err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()),
			"failed to count reference data of project device, project_id: %s, udid: %s, error: %+v",
			in.GetProjectId(), in.GetUdid(), err,
		)
	}
	out.TotalCount = uint64(count)
	out.TotalPage = 1

	records, err := l.svcCtx.ProjectDeviceModel.FindReferenceBySearchReq(l.ctx, req)
	if err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()),
			"failed to find reference data of project device, project_id: %s, udid: %s, error: %+v",
			in.GetProjectId(), in.GetUdid(), err,
		)
	}

	out.Items = make([]*pb.SearchProjectDeviceReferenceItem, 0, len(records))
	for _, record := range records {
		item := &pb.SearchProjectDeviceReferenceItem{}
		if err = utils.Copy(item, record, l.converters...); err != nil {
			return nil, errors.Wrapf(
				errorx.Err(errorx.CopyToStructFailure, err.Error()),
				"failed to copy reference data of project device to response, data: %s, error: %+v",
				jsonx.MarshalIgnoreError(record), err,
			)
		}

		out.Items = append(out.Items, item)
	}

	pagination := in.GetPagination()
	if pagination != nil {
		out.CurrentPage = pagination.GetCurrentPage()
		out.PageSize = pagination.GetPageSize()
		out.TotalPage = (out.TotalCount + out.PageSize - 1) / out.PageSize // round up
	}

	return out, nil
}
