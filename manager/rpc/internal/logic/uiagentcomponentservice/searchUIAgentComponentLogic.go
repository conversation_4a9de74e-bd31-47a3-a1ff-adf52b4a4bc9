package uiagentcomponentservicelogic

import (
	"context"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sqlbuilder/rpc"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type SearchUIAgentComponentLogic struct {
	*BaseLogic
}

func NewSearchUIAgentComponentLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SearchUIAgentComponentLogic {
	return &SearchUIAgentComponentLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// SearchUIAgentComponent 搜索UIAgent组件
func (l *SearchUIAgentComponentLogic) SearchUIAgentComponent(in *pb.SearchUIAgentComponentReq) (
	out *pb.SearchUIAgentComponentResp, err error,
) {
	var (
		projectID  = in.GetProjectId()
		categoryID = in.GetCategoryId()
	)
	out = &pb.SearchUIAgentComponentResp{}

	// validate the project_id in req
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, projectID); err != nil {
		return nil, err
	}

	// validate the category_id in req
	if _, err = model.CheckCategoryByCategoryId(
		l.ctx, l.svcCtx.CategoryModel, projectID, common.ConstCategoryTreeTypeUIAgentComponent, categoryID,
	); err != nil {
		return nil, err
	}

	req := model.SearchUIAgentComponentReq{
		BaseSearchReq: model.BaseSearchReq{
			ProjectID:  in.GetProjectId(),
			Condition:  in.GetCondition(),
			Pagination: in.GetPagination(),
			Sort:       rpc.ConvertSortFields(in.GetSort()),
		},
		CategoryID:    categoryID,
		DrillDown:     true,
		CategoryModel: l.svcCtx.CategoryModel,
	}
	count, err := l.svcCtx.UIAgentComponentModel.FindCountBySearchReq(l.ctx, req)
	if err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()),
			"failed to count ui agent component, project_id: %s, category_id: %s, error: %+v",
			projectID, categoryID, err,
		)
	}
	out.TotalCount = uint64(count)
	out.TotalPage = 1

	components, err := l.svcCtx.UIAgentComponentModel.FindAllBySearchReq(l.ctx, req)
	if err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()),
			"failed to find ui agent component, project_id: %s, category_id: %s, error: %+v",
			projectID, categoryID, err,
		)
	}

	out.Items = make([]*pb.UIAgentComponent, 0, len(components))
	for _, component := range components {
		item := &pb.UIAgentComponent{}
		if err = utils.Copy(item, component, l.converters...); err != nil {
			return nil, errors.Wrapf(
				errorx.Err(errorx.CopyToStructFailure, err.Error()),
				"failed to copy ui agent component to response, component: %s, error: %+v",
				jsonx.MarshalIgnoreError(component), err,
			)
		}

		// no need to return `steps` and `variables` while searching ui agent components
		item.Steps = nil
		item.Variables = nil
		out.Items = append(out.Items, item)
	}

	if pagination := in.GetPagination(); pagination != nil {
		out.CurrentPage = pagination.GetCurrentPage()
		out.PageSize = pagination.GetPageSize()
		out.TotalPage = (out.TotalCount + out.PageSize - 1) / out.PageSize // round up
	}

	return out, nil
}
