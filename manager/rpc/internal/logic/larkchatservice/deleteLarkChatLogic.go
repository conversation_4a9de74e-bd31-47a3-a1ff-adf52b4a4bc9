package larkchatservicelogic

import (
	"context"
	"fmt"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/caller"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/redislock"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type DeleteLarkChatLogic struct {
	*BaseLogic
}

func NewDeleteLarkChatLogic(ctx context.Context, svcCtx *svc.ServiceContext) *DeleteLarkChatLogic {
	return &DeleteLarkChatLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// DeleteLarkChat 删除测试通知飞书群组（由飞书群解散事件触发）
func (l *DeleteLarkChatLogic) DeleteLarkChat(in *pb.DeleteLarkChatReq) (out *pb.DeleteLarkChatResp, err error) {
	key := fmt.Sprintf("%s:%s", common.ConstLockLarkChatChatIDPrefix, in.GetChatId())
	fn := func() error {
		if _, err = l.svcCtx.LarkChatModel.DeleteByChatID(l.ctx, nil, in.GetChatId()); err != nil {
			return errors.Wrapf(
				errorx.Err(errorx.DBError, err.Error()),
				"failed to delete items from table, table: %s, chat_id: %s, error: %+v",
				l.svcCtx.LarkChatModel.Table(), in.GetChatId(), err,
			)
		}

		return nil
	}
	if err = caller.LockWithOptionDo(
		l.svcCtx.Redis, key, fn,
		redislock.WithTimeout(common.ConstExpireOfLarkChatDisbandedTask),
	); err != nil {
		return nil, err
	}

	return &pb.DeleteLarkChatResp{}, nil
}
