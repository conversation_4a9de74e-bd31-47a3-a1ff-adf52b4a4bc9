package larkchatservicelogic

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sqlbuilder/rpc"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type SearchLarkChatLogic struct {
	*BaseLogic
}

func NewSearchLarkChatLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SearchLarkChatLogic {
	return &SearchLarkChatLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// SearchLarkChat 搜索测试通知飞书群组
func (l *SearchLarkChatLogic) SearchLarkChat(in *pb.SearchLarkChatReq) (
	out *pb.SearchLarkChatResp, err error,
) {
	var (
		projectID  = in.GetProjectId()
		condition  = in.GetCondition()
		pagination = in.GetPagination()
		sort       = in.GetSort()
		_type      = in.GetType()
	)

	out = &pb.SearchLarkChatResp{}

	// validate the project_id in req
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, projectID); err != nil {
		return nil, err
	}

	req := model.SearchLarkChatReq{
		BaseSearchReq: model.BaseSearchReq{
			ProjectID:  projectID,
			Condition:  condition,
			Pagination: pagination,
			Sort:       rpc.ConvertSortFields(sort),
		},
		Type: _type,
	}
	count, err := l.svcCtx.LarkChatModel.FindCountBySearchReq(l.ctx, req)
	if err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()),
			"failed to count lark chat, project_id: %s, type: %s, error: %+v",
			projectID, _type, err,
		)
	}
	out.TotalCount = uint64(count)
	out.TotalPage = 1

	chats, err := l.svcCtx.LarkChatModel.FindAllBySearchReq(l.ctx, req)
	if err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()),
			"failed to find lark chat, project_id: %s, type: %s, error: %+v",
			projectID, _type, err,
		)
	}

	out.Items = make([]*pb.LarkChat, 0, len(chats))
	for _, chat := range chats {
		item := &pb.LarkChat{}
		if err = utils.Copy(item, chat, l.converters...); err != nil {
			return nil, errors.Wrapf(
				errorx.Err(errorx.CopyToStructFailure, err.Error()),
				"failed to copy lark chat to response, chat: %s, error: %+v",
				protobuf.MarshalJSONIgnoreError(chat), err,
			)
		}

		out.Items = append(out.Items, item)
	}

	if pagination != nil {
		out.CurrentPage = pagination.GetCurrentPage()
		out.PageSize = pagination.GetPageSize()
		out.TotalPage = (out.TotalCount + out.PageSize - 1) / out.PageSize // round up
	}

	return out, nil
}
