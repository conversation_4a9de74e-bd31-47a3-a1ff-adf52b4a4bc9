package larkchatservicelogic

import (
	"context"
	"database/sql"
	"fmt"
	"time"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zyedidia/generic"
	"github.com/zyedidia/generic/hashmap"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/caller"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/utils"
	larkproxypb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/larkproxy/rpc/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type ModifyLarkChatLogic struct {
	*BaseLogic
}

func NewModifyLarkChatLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ModifyLarkChatLogic {
	return &ModifyLarkChatLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// ModifyLarkChat 编辑测试通知飞书群组列表
func (l *ModifyLarkChatLogic) ModifyLarkChat(in *pb.ModifyLarkChatReq) (out *pb.ModifyLarkChatResp, err error) {
	// validate the project_id in req
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, in.GetProjectId()); err != nil {
		return nil, err
	}

	key := fmt.Sprintf("%s:%s:%s", common.ConstLockLarkChatProjectIDTypePrefix, in.GetProjectId(), in.GetType())
	fn := func() error {
		return l.modify(in)
	}
	if err = caller.LockDo(l.svcCtx.Redis, key, fn); err != nil {
		return nil, err
	}

	return &pb.ModifyLarkChatResp{}, nil
}

func (l *ModifyLarkChatLogic) modify(req *pb.ModifyLarkChatReq) error {
	var (
		projectID = req.GetProjectId()
		_type     = req.GetType()
		chats     = req.GetLarkChats()

		now = time.Now()
	)

	all, err := l.getAllLarkChats()
	if err != nil {
		return err
	}

	fromMap, err := l.getLarkChats(projectID, _type)
	if err != nil {
		return err
	}

	toMap := hashmap.New[string, *larkproxypb.ListChatRespData](
		uint64(len(chats)), generic.Equals[string], generic.HashString,
	)
	for _, chatID := range chats {
		if chatID == "" {
			continue
		}

		if v, ok := all.Get(chatID); ok {
			toMap.Put(chatID, v)
		}
	}

	return l.svcCtx.LarkChatModel.Trans(
		l.ctx, func(context context.Context, session sqlx.Session) error {
			var e error

			toMap.Each(
				func(key string, val *larkproxypb.ListChatRespData) {
					if e != nil {
						return
					} else if _, ok := fromMap.Get(key); ok {
						return
					}

					data := &model.LarkChat{
						ProjectId: projectID,
						Type:      _type,
						ChatId:    key,
						Name:      val.GetName(),
						Avatar: sql.NullString{
							String: val.GetAvatar(),
							Valid:  val.GetAvatar() != "",
						},
						Description: sql.NullString{
							String: val.GetDescription(),
							Valid:  val.GetDescription() != "",
						},
						External: func() int64 {
							if val.GetExternal() {
								return 1
							}
							return 0
						}(),
						Status:    val.GetStatus(),
						CreatedBy: l.currentUser.Account,
						UpdatedBy: l.currentUser.Account,
						CreatedAt: now,
						UpdatedAt: now,
					}
					if _, err := l.svcCtx.LarkChatModel.Insert(context, session, data); err != nil {
						e = errors.Wrapf(
							errorx.Err(errorx.DBError, err.Error()),
							"failed to batch insert values to table, table: %s, values: %s, error: %+v",
							l.svcCtx.LarkChatModel.Table(), jsonx.MarshalIgnoreError(data), err,
						)
					}
				},
			)
			if e != nil {
				return e
			}

			fromMap.Each(
				func(key string, val *model.LarkChat) {
					if e != nil {
						return
					}

					_, ok1 := all.Get(key)
					_, ok2 := toMap.Get(key)
					if !ok1 || !ok2 {
						var err error
						if err = l.svcCtx.LarkChatModel.Delete(context, session, val.Id); err != nil {
							e = errors.Wrapf(
								errorx.Err(errorx.DBError, err.Error()),
								"failed to delete item from table, table: %s, item: %s, error: %+v",
								l.svcCtx.LarkChatModel.Table(), jsonx.MarshalIgnoreError(val), err,
							)
						}
						if err == nil && _type == string(common.ConstTestTypeStability) {
							if _, err = l.svcCtx.NotifyModel.RemoveByPlanIdPrefix(
								context, session, projectID, utils.ConstStabilityPlanIDPrefix, key,
							); err != nil {
								e = errors.Wrapf(
									errorx.Err(errorx.DBError, err.Error()),
									"failed to delete %s prefix notifies from table, table: %s, project_id: %s, receiver: %s, error: %+v",
									utils.ConstStabilityPlanIDPrefix, l.svcCtx.NotifyModel.Table(), projectID, key, err,
								)
							}
						}
					}
				},
			)

			return e
		},
	)
}

func (l *ModifyLarkChatLogic) getAllLarkChats() (*hashmap.Map[string, *larkproxypb.ListChatRespData], error) {
	out, err := l.svcCtx.LarkProxyRPC.ListChat(l.ctx, &larkproxypb.ListChatReq{})
	if err != nil {
		return nil, err
	}

	cache := hashmap.New[string, *larkproxypb.ListChatRespData](
		uint64(len(out.GetItems())), generic.Equals[string], generic.HashString,
	)
	for _, item := range out.GetItems() {
		if item.GetChatId() == "" {
			continue
		} else if item.GetStatus() != string(common.ConstLarkChatStatusNormal) {
			continue
		}

		cache.Put(item.GetChatId(), item)
	}

	return cache, nil
}

func (l *ModifyLarkChatLogic) getLarkChats(projectID, _type string) (
	*hashmap.Map[string, *model.LarkChat], error,
) {
	records, err := l.svcCtx.LarkChatModel.FindAll(l.ctx, projectID, _type)
	if err != nil {
		return nil, err
	}

	cache := hashmap.New[string, *model.LarkChat](
		uint64(len(records)), generic.Equals[string], generic.HashString,
	)
	for _, record := range records {
		if record == nil || record.Id == 0 {
			continue
		}

		cache.Put(record.ChatId, record)
	}

	return cache, nil
}
