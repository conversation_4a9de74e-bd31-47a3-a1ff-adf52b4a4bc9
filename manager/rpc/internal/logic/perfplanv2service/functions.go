package perfplanv2servicelogic

import (
	"time"

	"github.com/robfig/cron/v3"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
)

func checkScheduleWithDuration(schedule cron.Schedule, duration time.Duration, times int) error {
	if times <= 0 {
		times = common.ConstCheckScheduleTimes
	} else if times == 1 {
		// at least check 2 times to avoid the case that whether the trigger interval
		// of the cron expression is less than the duration of the perf plan
		times = 2
	}

	now := time.Now()
	last := time.Time{}
	for i := 0; i < times; i++ {
		next := schedule.Next(now)

		// check whether the trigger time of the cron expression is outside working hours
		if next.Hour() < workingTimeLeft.Hour() ||
			(next.Hour() == workingTimeLeft.Hour() && next.Minute() < workingTimeLeft.Minute()) ||
			next.Hour() > workingTimeRight.Hour() ||
			(next.Hour() == workingTimeRight.Hour() && next.Minute() > workingTimeRight.Minute()) {
			return errorx.Errorf(
				errorx.ProhibitedBehavior,
				"the trigger time of the cron expression cannot be outside working hours, %s not in [%s, %s]",
				next.Format("15:04"), constants.ConstWorkingTimeRangeOfLeft, constants.ConstWorkingTimeRangeOfRight,
			)
		}

		// check whether the trigger time of the cron expression is on weekends
		if next.Weekday() == time.Saturday || next.Weekday() == time.Sunday {
			return errorx.Errorf(
				errorx.ProhibitedBehavior,
				"the trigger time of the cron expression cannot be on weekends, %s is weekend",
				next.Format(time.DateOnly),
			)
		}

		// check whether the trigger interval of the cron expression is less than the duration of the perf plan
		if !last.IsZero() && next.Sub(last) < duration {
			return errorx.Errorf(
				errorx.ProhibitedBehavior,
				"the trigger interval of the cron expression cannot be less than the duration of the perf plan, [%s, %s] < %s",
				last.Format(time.DateTime), next.Format(time.DateTime), duration,
			)
		}

		now = next
		last = next
	}

	return nil
}
