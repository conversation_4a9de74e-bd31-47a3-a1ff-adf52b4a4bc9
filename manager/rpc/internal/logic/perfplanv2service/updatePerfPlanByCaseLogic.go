package perfplanv2servicelogic

import (
	"context"
	"fmt"
	"time"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"
	"github.com/zeromicro/go-zero/core/mr"
	"github.com/zeromicro/go-zero/core/stores/sqlx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/caller"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/redislock"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/calculate"
	commonpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type UpdatePerfPlanByCaseLogic struct {
	*BaseLogic
}

func NewUpdatePerfPlanByCaseLogic(ctx context.Context, svcCtx *svc.ServiceContext) *UpdatePerfPlanByCaseLogic {
	return &UpdatePerfPlanByCaseLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// UpdatePerfPlanByCase 通过编辑压测用例触发关联的压测计划进行相应的更新（包括：持续时长、压测数据、施压机资源）
func (l *UpdatePerfPlanByCaseLogic) UpdatePerfPlanByCase(in *pb.UpdatePerfPlanByCaseReq) (
	out *pb.UpdatePerfPlanByCaseResp, err error,
) {
	// validate the project_id in req
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, in.GetProjectId()); err != nil {
		return nil, err
	}

	// validate the plan_id in req
	perfPlan, err := model.CheckPerfPlanV2ByPlanID(l.ctx, l.svcCtx.PerfPlanV2Model, in.GetProjectId(), in.GetPlanId())
	if err != nil {
		return nil, err
	}

	key := fmt.Sprintf("%s:%s:%s", common.ConstLockPerfPlanProjectIDPlanIDPrefix, in.GetProjectId(), in.GetPlanId())
	fn := func() error {
		return l.update(in, perfPlan)
	}
	if err = caller.LockWithOptionDo(l.svcCtx.Redis, key, fn, redislock.WithTimeout(5*time.Second)); err != nil {
		return nil, err
	}

	return &pb.UpdatePerfPlanByCaseResp{}, nil
}

func (l *UpdatePerfPlanByCaseLogic) update(req *pb.UpdatePerfPlanByCaseReq, perfPlan *model.PerfPlanV2) error {
	var (
		projectID = req.GetProjectId()
		planID    = req.GetPlanId()
		caseID    = req.GetCaseId()
	)

	var authRateLimits []*commonpb.RateLimitV2
	if perfPlan.AuthRateLimits.Valid && perfPlan.AuthRateLimits.String != "" {
		if err := protobuf.UnmarshalJSONWithMessagesFromString(
			perfPlan.AuthRateLimits.String, &authRateLimits,
		); err != nil {
			return errors.Wrapf(
				errorx.Err(errorx.SerializationError, err.Error()),
				"failed to unmarshal auth rate limits, project_id: %s, plan_id: %s, error: %+v",
				projectID, planID, err,
			)
		}
	}

	perfCase, err := l.getPerfCaseByCaseID(projectID, caseID, authRateLimits)
	if err != nil {
		return err
	}

	rrs, err := l.svcCtx.PerfPlanCaseModel.FindByPlanID(l.ctx, projectID, planID)
	if err != nil {
		return errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()),
			"failed to find perf case in perf plan, project_id: %s, plan_id: %s, error: %+v",
			projectID, planID, err,
		)
	}

	return l.svcCtx.PerfPlanV2Model.Trans(
		l.ctx, func(context context.Context, session sqlx.Session) error {
			return mr.MapReduceVoid[*model.PerfPlanCaseRelationship, calculate.ExecutionDurations](
				func(source chan<- *model.PerfPlanCaseRelationship) {
					for _, rr := range rrs {
						source <- rr
					}
				}, func(
					item *model.PerfPlanCaseRelationship, writer mr.Writer[calculate.ExecutionDurations],
					cancel func(error),
				) {
					if item == nil {
						return
					}

					// non-customized
					if item.PerfDataId == "" && item.CustomVu == 0 {
						// calculate the number of virtual users by serial and parallel steps
						item.NumberOfVu = calculate.CalculateNumberOfVirtualUsers(
							perfCase.GetSerialSteps(), perfCase.GetParallelSteps(), perfCase.GetRateLimits(),
						)
					}
					if item.CustomLg == 0 {
						lg := calculate.CalculateLoadGeneratorResource(
							uint32(item.NumberOfVu), &commonpb.LoadGenerator{},
						)
						item.NumberOfLg = int64(lg.GetNumberOfLg())
						item.RequestsOfCpu = lg.GetRequestsOfCpu()
						item.RequestsOfMemory = lg.GetRequestsOfMemory()
						item.LimitsOfCpu = lg.GetLimitsOfCpu()
						item.LimitsOfMemory = lg.GetLimitsOfMemory()
					}

					if _, err := l.svcCtx.PerfPlanCaseModel.Update(context, session, item); err != nil {
						l.Errorf(
							"failed to update perf plan case, item: %s, error: %+v",
							jsonx.MarshalIgnoreError(item), err,
						)
					}

					if perfPlan.CustomDuration == 1 {
						return
					}

					options := make([]calculate.Option, 0, 2)
					switch perfCase.GetProtocol() {
					case commonpb.Protocol_PROTOCOL_TT:
						if len(authRateLimits) > 0 {
							options = append(options, calculate.WithAuthRateLimits(authRateLimits))
						}
					case commonpb.Protocol_PROTOCOL_HTTP,
						commonpb.Protocol_PROTOCOL_GRPC,
						commonpb.Protocol_PROTOCOL_TT_AUTH:
						options = append(options, calculate.WithoutAuth())
					}
					options = append(options, calculate.WithGlobalRateLimits(perfCase.GetRateLimits()))

					writer.Write(
						calculate.CalculateTotalCaseDuration(
							&commonpb.PerfCaseContentV2{
								SetupSteps:    perfCase.GetSetupSteps(),
								SerialSteps:   perfCase.GetSerialSteps(),
								ParallelSteps: perfCase.GetParallelSteps(),
								TeardownSteps: perfCase.GetTeardownSteps(),
							}, uint32(item.NumberOfVu), options...,
						),
					)
				}, func(pipe <-chan calculate.ExecutionDurations, cancel func(error)) {
					var duration time.Duration
					for item := range pipe {
						if item.TotalDuration > duration {
							duration = item.TotalDuration
						}
					}

					perfPlan.Duration = int64(duration.Seconds())
					if _, err := l.svcCtx.PerfPlanV2Model.Update(context, session, perfPlan); err != nil {
						l.Errorf(
							"failed to update perf plan, item: %s, error: %+v", jsonx.MarshalIgnoreError(perfPlan), err,
						)
					}
				},
			)
		},
	)
}
