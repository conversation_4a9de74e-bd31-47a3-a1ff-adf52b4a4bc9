package perfplanv2servicelogic

import (
	"testing"
	"time"

	"github.com/gorhill/cronexpr"
)

func Test_checkScheduleWithDuration(t *testing.T) {
	type args struct {
		cronExpression string
		duration       time.Duration
		times          int
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name: "",
			args: args{
				cronExpression: "50 16 ? * TUE",
				duration:       time.Minute,
				times:          0,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				schedule, err := cronexpr.Parse(tt.args.cronExpression)
				if err != nil {
					t.Fatal(err)
				}

				if err = checkScheduleWithDuration(
					schedule, tt.args.duration, tt.args.times,
				); (err != nil) != tt.wantErr {
					t.Errorf("checkScheduleWithDuration() error = %v, wantErr %v", err, tt.wantErr)
				}
			},
		)
	}
}
