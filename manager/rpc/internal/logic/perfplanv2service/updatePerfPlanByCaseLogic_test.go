package perfplanv2servicelogic

import (
	"context"
	"testing"

	"github.com/zeromicro/go-zero/core/conf"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/config"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

func TestUpdatePerfPlanByCaseLogic_UpdatePerfPlanByCase(t *testing.T) {
	var c config.Config
	conf.MustLoad("../../../etc/manager.yaml", &c)

	ctx := context.Background()
	svcCtx := svc.NewServiceContext(c)
	l := NewUpdatePerfPlanByCaseLogic(ctx, svcCtx)

	type args struct {
		in *pb.UpdatePerfPlanByCaseReq
	}
	tests := []struct {
		name    string
		args    args
		wantOut *pb.UpdatePerfPlanByCaseResp
		wantErr bool
	}{
		{
			name: "",
			args: args{
				in: &pb.UpdatePerfPlanByCaseReq{
					ProjectId: "project_id:Kqllt5-9fA-I5UOdhjA5d",
					PlanId:    "perf_plan_id:0hHrXVYrT8NG9MGBIWIYm",
					CaseId:    "perf_case_id:xwqf9JfxzaBtfSaQ1KaHi",
				},
			},
			wantOut: nil,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				gotOut, err := l.UpdatePerfPlanByCase(tt.args.in)
				if (err != nil) != tt.wantErr {
					t.Errorf("UpdatePerfPlanByCase() error = %v, wantErr %v", err, tt.wantErr)
					return
				}
				//if !reflect.DeepEqual(gotOut, tt.wantOut) {
				//	t.Errorf("UpdatePerfPlanByCase() gotOut = %v, want %v", gotOut, tt.wantOut)
				//}
				t.Logf("out: %s", protobuf.MarshalJSONIgnoreError(gotOut))
			},
		)
	}
}
