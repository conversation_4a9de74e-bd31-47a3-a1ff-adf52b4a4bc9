package perfplanv2servicelogic

import (
	"context"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sqlbuilder/rpc"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type SearchPerfPlanV2Logic struct {
	*BaseLogic
}

func NewSearchPerfPlanV2Logic(ctx context.Context, svcCtx *svc.ServiceContext) *SearchPerfPlanV2Logic {
	return &SearchPerfPlanV2Logic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// SearchPerfPlanV2 搜索压测计划
func (l *SearchPerfPlanV2Logic) SearchPerfPlanV2(in *pb.SearchPerfPlanV2Req) (out *pb.SearchPerfPlanV2Resp, err error) {
	out = &pb.SearchPerfPlanV2Resp{}

	// validate the project_id in req
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, in.GetProjectId()); err != nil {
		return nil, err
	}

	var (
		count     int64
		perfPlans []*model.SearchPerfPlanV2Item
	)

	if in.GetCategoryId() != "" {
		// validate the category_id in req
		if _, err = model.CheckCategoryByCategoryId(
			l.ctx, l.svcCtx.CategoryModel, in.GetProjectId(), common.ConstCategoryTreeTypePerfPlan, in.GetCategoryId(),
		); err != nil {
			return nil, err
		}

		selectBuilder, countBuilder := l.svcCtx.PerfPlanV2Model.GenerateSearchPerfPlanV2SqlBuilder(
			model.SearchPerfPlanV2Req{
				BaseSearchReq: model.BaseSearchReq{
					ProjectID:  in.GetProjectId(),
					Condition:  in.GetCondition(),
					Pagination: in.GetPagination(),
					Sort:       rpc.ConvertSortFields(in.GetSort()),
				},
				CategoryID:    in.GetCategoryId(),
				DrillDown:     true,
				CategoryModel: l.svcCtx.CategoryModel,
			},
		)

		count, err = l.svcCtx.PerfPlanV2Model.FindCountPerfPlansV2(l.ctx, countBuilder)
		if err != nil {
			return nil, errors.Wrapf(
				errorx.Err(errorx.DBError, err.Error()),
				"failed to count perf plan, project_id: %s, category_id: %s, error: %+v",
				in.GetProjectId(), in.GetCategoryId(), err,
			)
		}
		out.TotalCount = uint64(count)
		out.TotalPage = 1

		perfPlans, err = l.svcCtx.PerfPlanV2Model.FindPerfPlansV2(l.ctx, selectBuilder)
		if err != nil {
			return nil, errors.Wrapf(
				errorx.Err(errorx.DBError, err.Error()),
				"failed to find perf plan, project_id: %s, category_id: %s, error: %+v",
				in.GetProjectId(), in.GetCategoryId(), err,
			)
		}
	} else {
		req := model.SearchPerfPlanV2Req{
			BaseSearchReq: model.BaseSearchReq{
				ProjectID:  in.GetProjectId(),
				Condition:  in.GetCondition(),
				Pagination: in.GetPagination(),
				Sort:       rpc.ConvertSortFields(in.GetSort()),
			},
		}
		count, err = l.svcCtx.PerfPlanV2Model.FindCountByReq(l.ctx, req)
		if err != nil {
			return nil, errors.Wrapf(
				errorx.Err(errorx.DBError, err.Error()),
				"failed to count perf plan, project_id: %s, error: %+v",
				in.GetProjectId(), err,
			)
		}
		out.TotalCount = uint64(count)
		out.TotalPage = 1

		perfPlans, err = l.svcCtx.PerfPlanV2Model.FindAllByReq(l.ctx, req)
		if err != nil {
			return nil, errors.Wrapf(
				errorx.Err(errorx.DBError, err.Error()),
				"failed to find perf plan, project_id: %s, error: %+v",
				in.GetProjectId(), err,
			)
		}
	}

	out.Items = make([]*pb.SearchPerfPlanV2Item, 0, len(perfPlans))
	for _, perfPlan := range perfPlans {
		item := &pb.SearchPerfPlanV2Item{}
		if err = utils.Copy(item, perfPlan, l.converters...); err != nil {
			return nil, errors.Wrapf(
				errorx.Err(errorx.CopyToStructFailure, err.Error()),
				"failed to copy perf plan to response, perf plan: %s, error: %+v",
				jsonx.MarshalIgnoreError(perfPlan), err,
			)
		}

		out.Items = append(out.Items, item)
	}

	pagination := in.GetPagination()
	if pagination != nil {
		out.CurrentPage = pagination.GetCurrentPage()
		out.PageSize = pagination.GetPageSize()
		out.TotalPage = (out.TotalCount + out.PageSize - 1) / out.PageSize // round up
	}

	return out, nil
}
