package perfplanv2servicelogic

import (
	"context"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type ViewPerfPlanV2Logic struct {
	*BaseLogic
}

func NewViewPerfPlanV2Logic(ctx context.Context, svcCtx *svc.ServiceContext) *ViewPerfPlanV2Logic {
	return &ViewPerfPlanV2Logic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// ViewPerfPlanV2 查看压测计划
func (l *ViewPerfPlanV2Logic) ViewPerfPlanV2(in *pb.ViewPerfPlanV2Req) (out *pb.ViewPerfPlanV2Resp, err error) {
	// validate the project_id in req
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, in.GetProjectId()); err != nil {
		return nil, err
	}

	// validate the plan_id in req
	perfPlan, err := model.CheckPerfPlanV2ByPlanID(l.ctx, l.svcCtx.PerfPlanV2Model, in.GetProjectId(), in.GetPlanId())
	if err != nil {
		return nil, err
	}

	out = &pb.ViewPerfPlanV2Resp{Plan: &pb.PerfPlanV2{}}
	if err = utils.Copy(out.Plan, perfPlan, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy perf plan to response, perf plan: %s, error: %+v",
			jsonx.MarshalIgnoreError(perfPlan), err,
		)
	}

	return out, nil
}
