package perfplanv2servicelogic

import (
	"context"
	"fmt"

	"github.com/hashicorp/go-multierror"
	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/mr"
	"github.com/zeromicro/go-zero/core/stores/sqlx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/caller"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type RemovePerfPlanV2Logic struct {
	*BaseLogic
}

func NewRemovePerfPlanV2Logic(ctx context.Context, svcCtx *svc.ServiceContext) *RemovePerfPlanV2Logic {
	return &RemovePerfPlanV2Logic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// RemovePerfPlanV2 删除压测计划
func (l *RemovePerfPlanV2Logic) RemovePerfPlanV2(in *pb.RemovePerfPlanV2Req) (out *pb.RemovePerfPlanV2Resp, err error) {
	// validate the project_id in req
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, in.GetProjectId()); err != nil {
		return nil, err
	}

	planIDs := in.GetPlanIds()
	workers := len(planIDs)
	if workers > common.ConstMRMaxWorkers {
		workers = common.ConstMRMaxWorkers
	}

	mr.ForEach[string](
		func(source chan<- string) {
			for _, planID := range planIDs {
				source <- planID
			}
		}, func(item string) {
			if e := l.remove(in.GetProjectId(), item); e != nil {
				err = multierror.Append(err, e)
			}
		}, mr.WithContext(l.ctx), mr.WithWorkers(workers),
	)

	return &pb.RemovePerfPlanV2Resp{}, nil
}

func (l *RemovePerfPlanV2Logic) remove(projectID, planID string) (err error) {
	// validate the plan_id in req
	if _, err = model.CheckPerfPlanV2ByPlanID(l.ctx, l.svcCtx.PerfPlanV2Model, projectID, planID); err != nil {
		return err
	}

	key := fmt.Sprintf("%s:%s:%s", common.ConstLockPerfPlanProjectIDPlanIDPrefix, projectID, planID)
	fn := func() error {
		return l.svcCtx.PerfPlanV2Model.Trans(
			l.ctx, func(context context.Context, session sqlx.Session) error {
				// Table: tag_reference_relationship
				if _, err := l.svcCtx.TagReferenceModel.RemoveByReferenceId(
					context, session, projectID, common.ConstReferenceTypePerfPlan, planID, "",
				); err != nil {
					return errors.Wrapf(
						errorx.Err(errorx.DBError, err.Error()),
						"failed to remove tag reference relationship, project_id: %s, reference_type: %s, reference_id: %s, error: %+v",
						projectID, common.ConstReferenceTypePerfPlan, planID, err,
					)
				}

				// Table: protobuf_configuration_reference_relationship
				if _, err := l.svcCtx.ProtobufConfigReferenceModel.RemoveByReference(
					context, session, projectID, common.ConstReferenceTypePerfPlan, planID,
				); err != nil {
					return errors.Wrapf(
						errorx.Err(errorx.DBError, err.Error()),
						"failed to remove protobuf configuration reference relationship, project_id: %s, reference_type: %s, reference_id: %s, error: %+v",
						projectID, common.ConstReferenceTypePerfPlan, planID, err,
					)
				}

				// Table: perf_stop_rule_reference_relationship
				if _, err := l.svcCtx.PerfPlanRuleModel.RemoveByPlanID(
					context, session, projectID, planID,
				); err != nil {
					return errors.Wrapf(
						errorx.Err(errorx.DBError, err.Error()),
						"failed to remove perf plan rule relationship, project_id: %s, plan_id: %s, error: %+v",
						projectID, planID, err,
					)
				}

				// Table: perf_plan_case_relationship
				if _, err := l.svcCtx.PerfPlanCaseModel.RemoveByPlanID(
					context, session, projectID, planID,
				); err != nil {
					return errors.Wrapf(
						errorx.Err(errorx.DBError, err.Error()),
						"failed to remove perf plan case relationship, project_id: %s, plan_id: %s, error: %+v",
						projectID, planID, err,
					)
				}

				// Table: notify
				if _, err := l.svcCtx.NotifyModel.RemoveByPlanId(context, session, projectID, planID); err != nil {
					return errors.Wrapf(
						errorx.Err(errorx.DBError, err.Error()),
						"failed to remove plan notify, project_id: %s, plan_id: %s, error: %+v",
						projectID, planID, err,
					)
				}

				// Table: perf_plan
				if _, err := l.svcCtx.PerfPlanV2Model.RemoveByPlanID(context, session, projectID, planID); err != nil {
					return errors.Wrapf(
						errorx.Err(errorx.DBError, err.Error()),
						"failed to remove perf plan, project_id: %s, plan_id: %s, error: %+v",
						projectID, planID, err,
					)
				}

				return nil
			},
		)
	}
	return caller.LockDo(l.svcCtx.Redis, key, fn)
}
