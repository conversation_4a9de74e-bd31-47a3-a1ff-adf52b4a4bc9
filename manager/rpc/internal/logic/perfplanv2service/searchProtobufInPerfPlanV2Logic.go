package perfplanv2servicelogic

import (
	"context"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sqlbuilder/rpc"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type SearchProtobufInPerfPlanV2Logic struct {
	*BaseLogic
}

func NewSearchProtobufInPerfPlanV2Logic(
	ctx context.Context, svcCtx *svc.ServiceContext,
) *SearchProtobufInPerfPlanV2Logic {
	return &SearchProtobufInPerfPlanV2Logic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// SearchProtobufInPerfPlanV2 搜索压测计划中的Protobuf配置
func (l *SearchProtobufInPerfPlanV2Logic) SearchProtobufInPerfPlanV2(in *pb.SearchProtobufInPerfPlanV2Req) (
	out *pb.SearchProtobufInPerfPlanV2Resp, err error,
) {
	out = &pb.SearchProtobufInPerfPlanV2Resp{}

	// validate the project_id in req
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, in.GetProjectId()); err != nil {
		return nil, err
	}

	// validate the plan_id in req
	if _, err = model.CheckPerfPlanV2ByPlanID(
		l.ctx, l.svcCtx.PerfPlanV2Model, in.GetProjectId(), in.GetPlanId(),
	); err != nil {
		return nil, err
	}

	req := model.SearchProtobufConfigInPerfPlanV2Req{
		BaseSearchReq: model.BaseSearchReq{
			ProjectID:  in.GetProjectId(),
			Condition:  in.GetCondition(),
			Pagination: in.GetPagination(),
			Sort:       rpc.ConvertSortFields(in.GetSort()),
		},
		PlanID:              in.GetPlanId(),
		ProtobufConfigModel: l.svcCtx.ProtobufConfigModel,
	}
	count, err := l.svcCtx.PerfPlanV2Model.FindCountProtobufConfigsInPerfPlan(l.ctx, req)
	if err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()),
			"failed to count protobuf configuration in perf plan, project_id: %s, plan_id: %s, error: %+v",
			in.GetProjectId(), in.GetPlanId(), err,
		)
	}
	out.TotalCount = uint64(count)
	out.TotalPage = 1

	configs, err := l.svcCtx.PerfPlanV2Model.FindProtobufConfigsInPerfPlan(l.ctx, req)
	if err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()),
			"failed to find protobuf configuration in perf plan, project_id: %s, plan_id: %s, error: %+v",
			in.GetProjectId(), in.GetPlanId(), err,
		)
	}

	out.Items = make([]*pb.SearchProtobufInPerfPlanV2Item, 0, len(configs))
	for _, config := range configs {
		item := &pb.SearchProtobufInPerfPlanV2Item{}
		if err = utils.Copy(item, config, l.converters...); err != nil {
			return nil, errors.Wrapf(
				errorx.Err(errorx.CopyToStructFailure, err.Error()),
				"failed to copy protobuf configuration to response, protobuf config: %s, error: %+v",
				jsonx.MarshalIgnoreError(config), err,
			)
		}

		out.Items = append(out.Items, item)
	}

	pagination := in.GetPagination()
	if pagination != nil {
		out.CurrentPage = pagination.GetCurrentPage()
		out.PageSize = pagination.GetPageSize()
		out.TotalPage = (out.TotalCount + out.PageSize - 1) / out.PageSize // round up
	}

	return out, nil
}
