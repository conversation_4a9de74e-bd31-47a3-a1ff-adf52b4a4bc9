package perfplanv2servicelogic

import (
	"context"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sqlbuilder/rpc"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type SearchCaseInPerfPlanV2Logic struct {
	*BaseLogic
}

func NewSearchCaseInPerfPlanV2Logic(ctx context.Context, svcCtx *svc.ServiceContext) *SearchCaseInPerfPlanV2Logic {
	return &SearchCaseInPerfPlanV2Logic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// SearchCaseInPerfPlanV2 搜索压测计划中的压测用例
func (l *SearchCaseInPerfPlanV2Logic) SearchCaseInPerfPlanV2(in *pb.SearchCaseInPerfPlanV2Req) (
	out *pb.SearchCaseInPerfPlanV2Resp, err error,
) {
	out = &pb.SearchCaseInPerfPlanV2Resp{}

	// validate the project_id in req
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, in.GetProjectId()); err != nil {
		return nil, err
	}

	// validate the plan_id in req
	perfPlan, err := model.CheckPerfPlanV2ByPlanID(l.ctx, l.svcCtx.PerfPlanV2Model, in.GetProjectId(), in.GetPlanId())
	if err != nil {
		return nil, err
	}

	req := model.SearchCaseInPerfPlanV2Req{
		BaseSearchReq: model.BaseSearchReq{
			ProjectID:  in.GetProjectId(),
			Condition:  in.GetCondition(),
			Pagination: in.GetPagination(),
			Sort:       rpc.ConvertSortFields(in.GetSort()),
		},
		PlanID:          in.GetPlanId(),
		PerfCaseV2Model: l.svcCtx.PerfCaseV2Model,
	}
	count, err := l.svcCtx.PerfPlanV2Model.FindCountCasesInPerfPlan(l.ctx, req)
	if err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()),
			"failed to count perf case in perf plan, project_id: %s, plan_id: %s, error: %+v",
			in.GetProjectId(), in.GetPlanId(), err,
		)
	}
	out.TotalCount = uint64(count)
	out.TotalPage = 1

	perfCases, err := l.svcCtx.PerfPlanV2Model.FindCasesInPerfPlan(l.ctx, req)
	if err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()),
			"failed to find perf case in perf plan, project_id: %s, plan_id: %s, error: %+v",
			in.GetProjectId(), in.GetPlanId(), err,
		)
	}

	out.Items = make([]*pb.SearchCaseInPerfPlanV2Item, 0, len(perfCases))
	for _, perfCase := range perfCases {
		item := &pb.SearchCaseInPerfPlanV2Item{}
		if err = utils.Copy(item, perfCase, l.converters...); err != nil {
			return nil, errors.Wrapf(
				errorx.Err(errorx.CopyToStructFailure, err.Error()),
				"failed to copy perf case to response, perf case: %s, error: %+v",
				jsonx.MarshalIgnoreError(perfCase), err,
			)
		}

		if perfCase.RateLimits.Valid {
			if err := protobuf.UnmarshalJSONWithMessagesFromString(perfCase.RateLimits.String, &item.RateLimits); err != nil {
				return nil, errors.Wrapf(
					errorx.Err(errorx.CopyToStructFailure, err.Error()),
					"failed to unmarshal perfCase.rate_limits to response, perf case: %s, error: %+v",
					jsonx.MarshalIgnoreError(perfCase), err,
				)
			}
		}

		out.Items = append(out.Items, item)
	}

	if err = l.setReferenceQPS(
		l.ctx,
		perfPlan.ProjectId,
		perfPlan.Protocol,
		out.Items,
	); err != nil {
		return nil, err
	}

	pagination := in.GetPagination()
	if pagination != nil {
		out.CurrentPage = pagination.GetCurrentPage()
		out.PageSize = pagination.GetPageSize()
		out.TotalPage = (out.TotalCount + out.PageSize - 1) / out.PageSize // round up
	}

	return out, nil
}
