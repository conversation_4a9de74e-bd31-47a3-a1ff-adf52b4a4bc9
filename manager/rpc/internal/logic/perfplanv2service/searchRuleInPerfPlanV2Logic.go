package perfplanv2servicelogic

import (
	"context"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sqlbuilder/rpc"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type SearchRuleInPerfPlanV2Logic struct {
	*BaseLogic
}

func NewSearchRuleInPerfPlanV2Logic(ctx context.Context, svcCtx *svc.ServiceContext) *SearchRuleInPerfPlanV2Logic {
	return &SearchRuleInPerfPlanV2Logic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// SearchRuleInPerfPlanV2 搜索压测计划中的停止规则
func (l *SearchRuleInPerfPlanV2Logic) SearchRuleInPerfPlanV2(in *pb.SearchRuleInPerfPlanV2Req) (
	out *pb.SearchRuleInPerfPlanV2Resp, err error,
) {
	out = &pb.SearchRuleInPerfPlanV2Resp{}

	// validate the project_id in req
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, in.GetProjectId()); err != nil {
		return nil, err
	}

	// validate the plan_id in req
	if _, err = model.CheckPerfPlanV2ByPlanID(
		l.ctx, l.svcCtx.PerfPlanV2Model, in.GetProjectId(), in.GetPlanId(),
	); err != nil {
		return nil, err
	}

	req := model.SearchRuleInPerfPlanV2Req{
		BaseSearchReq: model.BaseSearchReq{
			ProjectID:  in.GetProjectId(),
			Condition:  in.GetCondition(),
			Pagination: in.GetPagination(),
			Sort:       rpc.ConvertSortFields(in.GetSort()),
		},
		PlanID:            in.GetPlanId(),
		PerfStopRuleModel: l.svcCtx.PerfStopRuleModel,
	}
	count, err := l.svcCtx.PerfPlanV2Model.FindCountRulesInPerfPlan(l.ctx, req)
	if err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()),
			"failed to count perf stop rule in perf plan, project_id: %s, plan_id: %s, error: %+v",
			in.GetProjectId(), in.GetPlanId(), err,
		)
	}
	out.TotalCount = uint64(count)
	out.TotalPage = 1

	stopRules, err := l.svcCtx.PerfPlanV2Model.FindRulesInPerfPlan(l.ctx, req)
	if err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()),
			"failed to find perf stop rule in perf plan, project_id: %s, plan_id: %s, error: %+v",
			in.GetProjectId(), in.GetPlanId(), err,
		)
	}

	out.Items = make([]*pb.SearchRuleInPerfPlanV2Item, 0, len(stopRules))
	for _, stopRule := range stopRules {
		item := &pb.SearchRuleInPerfPlanV2Item{}
		if err = utils.Copy(item, stopRule, l.converters...); err != nil {
			return nil, errors.Wrapf(
				errorx.Err(errorx.CopyToStructFailure, err.Error()),
				"failed to copy perf stop rule to response, stop rule: %s, error: %+v",
				jsonx.MarshalIgnoreError(stopRule), err,
			)
		}

		out.Items = append(out.Items, item)
	}

	pagination := in.GetPagination()
	if pagination != nil {
		out.CurrentPage = pagination.GetCurrentPage()
		out.PageSize = pagination.GetPageSize()
		out.TotalPage = (out.TotalCount + out.PageSize - 1) / out.PageSize // round up
	}

	return out, nil
}
