package perfplanv2servicelogic

import (
	"context"
	"database/sql"
	"fmt"
	"time"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/caller"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/redislock"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type UpdatePerfPlanByChatIDLogic struct {
	*BaseLogic
}

func NewUpdatePerfPlanByChatIDLogic(ctx context.Context, svcCtx *svc.ServiceContext) *UpdatePerfPlanByChatIDLogic {
	return &UpdatePerfPlanByChatIDLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// UpdatePerfPlanByChatID 更新压测计划中自动创建的飞书群ID
func (l *UpdatePerfPlanByChatIDLogic) UpdatePerfPlanByChatID(in *pb.UpdatePerfPlanByChatIDReq) (
	out *pb.UpdatePerfPlanByChatIDResp, err error,
) {
	// validate the project_id in req
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, in.GetProjectId()); err != nil {
		return nil, err
	}

	// validate the plan_id in req
	perfPlan, err := model.CheckPerfPlanV2ByPlanID(l.ctx, l.svcCtx.PerfPlanV2Model, in.GetProjectId(), in.GetPlanId())
	if err != nil {
		return nil, err
	}

	key := fmt.Sprintf("%s:%s:%s", common.ConstLockPerfPlanProjectIDPlanIDPrefix, in.GetProjectId(), in.GetPlanId())
	fn := func() error {
		perfPlan.LarkChatId = sql.NullString{
			String: in.GetChatId(),
			Valid:  in.GetChatId() != "",
		}
		if _, err = l.svcCtx.PerfPlanV2Model.Update(l.ctx, nil, perfPlan); err != nil {
			return errors.Wrapf(
				errorx.Err(errorx.DBError, err.Error()),
				"failed to update values to table, table: %s, values: %s, error: %+v",
				l.svcCtx.PerfPlanV2Model.Table(), jsonx.MarshalIgnoreError(perfPlan), err,
			)
		}

		return nil
	}
	if err = caller.LockWithOptionDo(l.svcCtx.Redis, key, fn, redislock.WithTimeout(5*time.Second)); err != nil {
		return nil, err
	}

	return &pb.UpdatePerfPlanByChatIDResp{}, nil
}
