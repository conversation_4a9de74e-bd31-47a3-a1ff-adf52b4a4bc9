package interfacedefinitionservicelogic

import (
	"context"
	"database/sql"
	"fmt"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/caller"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/logic/fsm"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type MaintainInterfaceCaseLogic struct {
	*BaseLogic
}

func NewMaintainInterfaceCaseLogic(ctx context.Context, svcCtx *svc.ServiceContext) *MaintainInterfaceCaseLogic {
	return &MaintainInterfaceCaseLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// MaintainInterfaceCase 维护接口用例
func (l *MaintainInterfaceCaseLogic) MaintainInterfaceCase(in *pb.MaintainInterfaceCaseReq) (
	out *pb.MaintainInterfaceCaseResp, err error,
) {
	// validate the project_id in req
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, in.GetProjectId()); err != nil {
		return nil, err
	}

	// validate the document_id in req
	if _, err = model.CheckInterfaceDocumentByDocumentId(
		l.ctx, l.svcCtx.InterfaceDocumentModel, in.GetProjectId(), in.GetDocumentId(),
	); err != nil {
		return nil, err
	}

	key := fmt.Sprintf(
		"%s:%s:%s:%s", common.ConstLockInterfaceCaseProjectIdDocumentIdCaseIdPrefix, in.GetProjectId(),
		in.GetDocumentId(), in.GetCaseId(),
	)
	fn := func() error {
		// validate the case_id in req
		origin, err := model.CheckInterfaceCaseByCaseId(
			l.ctx, l.svcCtx.InterfaceCaseModel, in.GetProjectId(), in.GetDocumentId(), in.GetCaseId(), "",
		)
		if err != nil {
			return err
		}

		if origin.State != string(common.ConstResourceStatePublished) {
			return errorx.Errorf(
				errorx.ProhibitedBehavior,
				"the status of interface case must be 'PUBLISHED' before it can be changed to 'TO_BE_MAINTAINED', project_id: %s, document_id: %s, case_id: %s, state: %s",
				in.GetProjectId(), in.GetDocumentId(), in.GetCaseId(), origin.State,
			)
		}

		if in.GetMaintainedBy() != "" {
			origin.MaintainedBy = sql.NullString{
				String: in.GetMaintainedBy(),
				Valid:  true,
			}
		}
		if !origin.MaintainedBy.Valid || origin.MaintainedBy.String == "" {
			return errorx.Errorf(
				errorx.ProhibitedBehavior,
				"the maintainer of interface case must be set before it can be changed to 'TO_BE_MAINTAINED', project_id: %s, document_id: %s, case_id: %s",
				in.GetProjectId(), in.GetDocumentId(), in.GetCaseId(),
			)
		}

		maintainedBy := origin.MaintainedBy.String
		user, err := l.getUserInfoByAccount(maintainedBy)
		if err != nil {
			return err
		} else if !user.GetEnabled() {
			return errorx.Errorf(
				errorx.ProhibitedBehavior,
				"cannot assign a disabled user as a maintainer of interface case, project_id: %s, document_id: %s, case_id: %s, maintained_by: %s",
				in.GetProjectId(), in.GetDocumentId(), in.GetCaseId(), maintainedBy,
			)
		}

		event := common.ConstReviewResourceEventReturnToMaintenanceAfterPublished
		if err = fsm.NewResourceFSM(
			l.ctx, l.svcCtx, &ResourceOfInterfaceCase{
				InterfaceCase: origin,
				Event:         event,
				Emails:        []string{user.GetEmail()},
			}, nil,
		).TransitionWithoutLock(); err != nil {
			return errors.Wrapf(
				errorx.Err(errorx.FSMError, err.Error()),
				"got an error regarding state transition about interface case, project_id: %s, resource_id: %s, event: %s, error: %+v",
				in.GetProjectId(), in.GetCaseId(), event, err,
			)
		}

		out = &pb.MaintainInterfaceCaseResp{Case: &pb.InterfaceCase{}}
		if err = utils.Copy(out.Case, origin, l.converters...); err != nil {
			return errors.Wrapf(
				errorx.Err(errorx.CopyToStructFailure, err.Error()),
				"failed to copy interface case[%+v] to response, error: %+v",
				origin, err,
			)
		}

		return nil
	}
	err = caller.LockDo(l.svcCtx.Redis, key, fn)

	return out, err
}
