package interfacedefinitionservicelogic

import (
	"context"
	"math"

	"github.com/Masterminds/squirrel"
	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sqlbuilder"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type SearchInterfaceConfigLogic struct {
	*BaseLogic
}

func NewSearchInterfaceConfigLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SearchInterfaceConfigLogic {
	return &SearchInterfaceConfigLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// SearchInterfaceConfig 搜索接口配置
func (l *SearchInterfaceConfigLogic) SearchInterfaceConfig(in *pb.SearchInterfaceConfigReq) (resp *pb.SearchInterfaceConfigResp, err error) {
	resp = &pb.SearchInterfaceConfigResp{}

	// validate the project_id in req
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, in.GetProjectId()); err != nil {
		return nil, err
	}

	// validate the document_id in req
	if _, err = model.CheckInterfaceDocumentByDocumentId(l.ctx, l.svcCtx.InterfaceDocumentModel, in.GetProjectId(), in.GetDocumentId()); err != nil {
		return nil, err
	}

	selectBuilder, countBuilder := l.generateSearchSqlBuilder(in)

	count, err := l.svcCtx.InterfaceConfigurationModel.FindCount(l.ctx, countBuilder)
	if err != nil {
		return nil, errors.Wrapf(errorx.Err(errorx.DBError, err.Error()), "failed to count interface configuration with project_id[%s] and document_id[%s], error: %+v", in.GetProjectId(), in.GetDocumentId(), err)
	}
	resp.TotalCount = uint64(count)
	resp.TotalPage = 1

	interfaceConfigs, err := l.svcCtx.InterfaceConfigurationModel.FindNoCacheByQuery(l.ctx, selectBuilder)
	if err != nil {
		return nil, errors.Wrapf(errorx.Err(errorx.DBError, err.Error()), "failed to find interface configuration with project_id[%s] and document_id[%s], error: %+v", in.GetProjectId(), in.GetDocumentId(), err)
	}

	resp.Items = make([]*pb.InterfaceConfig, 0, len(interfaceConfigs))
	for _, interfaceConfig := range interfaceConfigs {
		ic := &pb.InterfaceConfig{}
		if err = utils.Copy(ic, interfaceConfig); err != nil {
			return nil, errors.Wrapf(errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy interface configuration[%+v] to response, error: %+v", interfaceConfig, err)
		}

		// 搜索时返回的内容：`data` 为 null，`imports` 和 `exports` 为空列表
		ic.Data = nil
		ic.InputParameters = make([]*pb.InputParameter, 0)
		ic.OutputParameters = make([]*pb.OutputParameter, 0)
		resp.Items = append(resp.Items, ic)
	}

	pagination := in.GetPagination()
	if pagination != nil {
		resp.CurrentPage = pagination.GetCurrentPage()
		resp.PageSize = pagination.GetPageSize()
		resp.TotalPage = uint64(math.Ceil(float64(resp.TotalCount) / float64(resp.PageSize)))
	}

	return resp, nil
}

func (l *SearchInterfaceConfigLogic) generateSearchSqlBuilder(req *pb.SearchInterfaceConfigReq) (sb, scb squirrel.SelectBuilder) {
	m := l.svcCtx.InterfaceConfigurationModel

	sb = m.SelectBuilder().Where("`project_id` = ? AND `document_id` = ?", req.GetProjectId(), req.GetDocumentId())
	scb = m.SelectCountBuilder().Where("`project_id` = ? AND `document_id` = ?", req.GetProjectId(), req.GetDocumentId())

	sb = sqlbuilder.SearchOptions(sb, sqlbuilder.WithCondition(m, req.GetCondition()), sqlbuilder.WithPagination(m, req.GetPagination()), sqlbuilder.WithSort(m, req.GetSort()))
	scb = sqlbuilder.SearchOptions(scb, sqlbuilder.WithCondition(m, req.GetCondition()))

	return sb, scb
}
