package interfacedefinitionservicelogic

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type GetInterfaceCoverageTeamsLogic struct {
	*BaseLogic
}

func NewGetInterfaceCoverageTeamsLogic(
	ctx context.Context, svcCtx *svc.ServiceContext,
) *GetInterfaceCoverageTeamsLogic {
	return &GetInterfaceCoverageTeamsLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// GetInterfaceCoverageTeams 获取接口覆盖率相关的团队
func (l *GetInterfaceCoverageTeamsLogic) GetInterfaceCoverageTeams(in *pb.GetInterfaceCoverageTeamsReq) (
	out *pb.GetInterfaceCoverageTeamsResp, err error,
) {
	projectID := in.GetProjectId()

	// validate the project_id in req
	project, err := model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, projectID)
	if err != nil {
		return nil, err
	}
	if project.CoverageEnabled == 0 {
		return nil, errorx.Errorf(
			errorx.ProhibitedBehavior,
			"the project has not enabled the interface coverage function, project_id: %s",
			projectID,
		)
	}

	teams, err := l.svcCtx.InterfaceCoverageModel.FindTeamByProjectID(l.ctx, projectID)
	if err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()),
			"failed to find the interface coverage teams, project_id: %s, error: %+v",
			projectID, err,
		)
	}

	return &pb.GetInterfaceCoverageTeamsResp{
		Teams: teams,
	}, nil
}
