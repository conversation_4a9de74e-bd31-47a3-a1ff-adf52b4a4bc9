package interfacedefinitionservicelogic

import (
	"context"
	"math"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sqlbuilder/rpc"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"
)

type SearchInterfaceCaseReferenceLogic struct {
	*BaseLogic
}

func NewSearchInterfaceCaseReferenceLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SearchInterfaceCaseReferenceLogic {
	return &SearchInterfaceCaseReferenceLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// SearchInterfaceCaseReference 搜索接口用例引用详情
func (l *SearchInterfaceCaseReferenceLogic) SearchInterfaceCaseReference(in *pb.SearchInterfaceCaseReferenceReq) (resp *pb.SearchInterfaceCaseReferenceResp, err error) {
	resp = &pb.SearchInterfaceCaseReferenceResp{}

	// validate the project_id in req
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, in.GetProjectId()); err != nil {
		return nil, err
	}

	// validate the case_id in req
	if _, err = model.CheckInterfaceCaseByCaseId(l.ctx, l.svcCtx.InterfaceCaseModel, in.GetProjectId(), "", in.GetCaseId(), ""); err != nil {
		return nil, err
	}

	selectBuilder, countBuilder := l.svcCtx.InterfaceCaseModel.GenerateSearchInterfaceCaseReferenceSqlBuilder(model.SearchInterfaceCaseReferenceReq{
		ProjectId:  in.GetProjectId(),
		CaseId:     in.GetCaseId(),
		Condition:  in.GetCondition(),
		Pagination: in.GetPagination(),
		Sort:       rpc.ConvertSortFields(in.GetSort()),
	})

	count, err := l.svcCtx.InterfaceCaseModel.FindCountInterfaceCaseReference(l.ctx, countBuilder)
	if err != nil {
		return nil, errors.Wrapf(errorx.Err(errorx.DBError, err.Error()), "failed to count reference data of interface case with project_id[%s] and case_id[%s], error: %+v", in.GetProjectId(), in.GetCaseId(), err)
	}
	resp.TotalCount = uint64(count)
	resp.TotalPage = 1

	interfaceCases, err := l.svcCtx.InterfaceCaseModel.FindInterfaceCaseReference(l.ctx, selectBuilder)
	if err != nil {
		return nil, errors.Wrapf(errorx.Err(errorx.DBError, err.Error()), "failed to find reference data of interface case with project_id[%s] and case_id[%s], error: %+v", in.GetProjectId(), in.GetCaseId(), err)
	}

	resp.Items = make([]*pb.SearchInterfaceCaseReferenceItem, 0, len(interfaceCases))
	for _, interfaceCase := range interfaceCases {
		item := &pb.SearchInterfaceCaseReferenceItem{}
		if err = utils.Copy(item, interfaceCase, l.converters...); err != nil {
			return nil, errors.Wrapf(errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy reference data of interface case[%+v] to response, error: %+v", interfaceCase, err)
		}

		resp.Items = append(resp.Items, item)
	}

	pagination := in.GetPagination()
	if pagination != nil {
		resp.CurrentPage = pagination.GetCurrentPage()
		resp.PageSize = pagination.GetPageSize()
		resp.TotalPage = uint64(math.Ceil(float64(resp.TotalCount) / float64(resp.PageSize)))
	}

	return resp, nil
}
