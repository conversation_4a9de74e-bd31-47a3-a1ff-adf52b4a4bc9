package interfacedefinitionservicelogic

import (
	"context"
	"fmt"
	"reflect"
	"strconv"
	"strings"

	dll "github.com/emirpasic/gods/lists/doublylinkedlist"
	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type MockInterfaceDocumentLogic struct {
	*BaseLogic
}

func NewMockInterfaceDocumentLogic(ctx context.Context, svcCtx *svc.ServiceContext) *MockInterfaceDocumentLogic {
	return &MockInterfaceDocumentLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// MockInterfaceDocument 根据接口文档生成接口用例数据
func (l *MockInterfaceDocumentLogic) MockInterfaceDocument(in *pb.MockInterfaceDocumentReq) (
	resp *pb.MockInterfaceDocumentResp, err error,
) {
	// validate the project_id in req
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, in.GetProjectId()); err != nil {
		return nil, err
	}

	// validate the document_id in req
	document, err := model.CheckInterfaceDocumentByDocumentId(
		l.ctx, l.svcCtx.InterfaceDocumentModel, in.GetProjectId(), in.GetDocumentId(),
	)
	if err != nil {
		return nil, err
	}

	var config *model.InterfaceConfiguration
	if in.GetWithConfig() == pb.WithConfig_AUTOMATIC {
		cs, err := l.svcCtx.InterfaceConfigurationModel.FindConfigByDocumentId(
			l.ctx, in.GetProjectId(), in.GetDocumentId(),
		)
		if err != nil && !errors.Is(err, model.ErrNotFound) {
			return nil, errors.Wrapf(
				errorx.Err(errorx.DBError, err.Error()),
				"failed to find interface configuration with project_id[%s] and document_id[%s], error: %+v",
				in.GetProjectId(), in.GetDocumentId(), err,
			)
		} else {
			num := len(cs)
			l.Logger.Infof(
				"find %d interface configuration[s] by project_id[%s] and document_id[%s]", num, in.GetProjectId(),
				in.GetDocumentId(),
			)

			if num > 0 {
				// 有接口配置则优先使用接口配置
				config = cs[0]
			}
		}
	} else if in.GetWithConfig() == pb.WithConfig_SPECIFIED {
		config, err = model.CheckInterfaceConfigByConfigId(
			l.ctx, l.svcCtx.InterfaceConfigurationModel, in.GetProjectId(), in.GetDocumentId(), in.GetConfigId(),
		)
		if err != nil {
			return nil, err
		}
	}

	component := &pb.HttpRequestComponent{}
	if err = l.mockHttp(document, config, component); err != nil {
		return nil, err
	}

	return &pb.MockInterfaceDocumentResp{Mock: component}, nil
}

func (l *MockInterfaceDocumentLogic) mockHttp(
	document *model.InterfaceDocument, config *model.InterfaceConfiguration, component *pb.HttpRequestComponent,
) error {
	path := document.Path
	method := document.Method
	data := document.Data

	if config != nil {
		path = config.Path
		method = config.Method
		data = config.Data

		if err := protobuf.UnmarshalJSONWithMessagesFromString(config.InputParameters, &component.Imports); err != nil {
			return errors.Wrapf(
				errorx.Err(errorx.SerializationError, err.Error()),
				"failed to unmarshal the input parameters of interface configuration[%s], error: %+v",
				config.InputParameters, err,
			)
		}

		if err := protobuf.UnmarshalJSONWithMessagesFromString(
			config.OutputParameters, &component.Exports,
		); err != nil {
			return errors.Wrapf(
				errorx.Err(errorx.SerializationError, err.Error()),
				"failed to unmarshal the output parameters of interface configuration[%s], error: %+v",
				config.OutputParameters, err,
			)
		}
	}

	var doc pb.Document
	if err := protobuf.UnmarshalJSONFromString(data, &doc); err != nil {
		return errors.Wrapf(
			errorx.Err(errorx.SerializationError, err.Error()), "failed to unmarshal document data[%s], error: %+v",
			data, err,
		)
	}

	component.Url = path
	component.Method = method

	l.processHeaders(document.ProjectId, document.ImportType.String, doc.Headers, component)
	l.processPathParams(document.ProjectId, document.ImportType.String, doc.PathParams, component)
	l.processQueryParams(document.ProjectId, document.ImportType.String, doc.QueryParams, component)
	l.processBody(document.ProjectId, document.ImportType.String, doc.Body, component)

	return nil
}

func (l *MockInterfaceDocumentLogic) processHeaders(
	projectId string, importType common.InterfaceDefinitionImportType, headers types.ArraySchema,
	component *pb.HttpRequestComponent,
) {
	component.Headers = make([]*pb.HttpRequestComponent_KeyValueDesc, 0, len(headers))
	for _, header := range headers {
		if header.Title == "" {
			continue
		}

		component.Headers = append(
			component.Headers, &pb.HttpRequestComponent_KeyValueDesc{
				Key:         header.Title,
				Value:       fmt.Sprintf("%v", l.getValueBySchema(projectId, importType, header, nil)),
				Description: header.Description,
			},
		)
	}
}

func (l *MockInterfaceDocumentLogic) processPathParams(
	projectId string, importType common.InterfaceDefinitionImportType, params types.ArraySchema,
	component *pb.HttpRequestComponent,
) {
	ss := strings.SplitN(component.Url, "?", 2)
	if len(ss) == 0 {
		return
	}

	for _, param := range params {
		if param.Title == "" {
			continue
		}

		ss[0] = strings.Replace(
			ss[0], fmt.Sprintf("{%s}", param.Title),
			fmt.Sprintf("%v", l.getValueBySchema(projectId, importType, param, nil)), 1,
		)
	}

	component.Url = strings.Join(ss, "?")
}

func (l *MockInterfaceDocumentLogic) processQueryParams(
	projectId string, importType common.InterfaceDefinitionImportType, params types.ArraySchema,
	component *pb.HttpRequestComponent,
) {
	component.QueryParams = make([]*pb.HttpRequestComponent_KeyValueDesc, 0, len(params))
	for _, param := range params {
		if param.Title == "" {
			continue
		}

		component.QueryParams = append(
			component.QueryParams, &pb.HttpRequestComponent_KeyValueDesc{
				Key:         param.Title,
				Value:       fmt.Sprintf("%v", l.getValueBySchema(projectId, importType, param, nil)),
				Description: param.Description,
			},
		)
	}
}

func (l *MockInterfaceDocumentLogic) processBody(
	projectId string, importType common.InterfaceDefinitionImportType, body *pb.BodyData,
	component *pb.HttpRequestComponent,
) {
	b := &pb.HttpRequestComponent_Body{}

	switch body.Type {
	case common.ConstMediaTypeFormData, common.ConstMediaTypeUrlEncoded:
		if body.Type == common.ConstMediaTypeFormData {
			b.Type = pb.HttpRequestComponent_Body_MULTIPART_FORM_DATA
		} else {
			b.Type = pb.HttpRequestComponent_Body_APPLICATION_FORM_URLENCODED
		}
		b.FormData = make([]*pb.HttpRequestComponent_KeyValueDesc, 0, len(body.Form))

		for _, s := range body.Form {
			if s.Title == "" {
				continue
			}

			b.FormData = append(
				b.FormData, &pb.HttpRequestComponent_KeyValueDesc{
					Key:         s.Title,
					Value:       fmt.Sprintf("%v", l.getValueBySchema(projectId, importType, s, nil)),
					Description: s.Description,
				},
			)
		}
	case common.ConstMediaTypeApplicationJson:
		b.Type = pb.HttpRequestComponent_Body_APPLICATION_JSON
		if body.Json != nil {
			if body.Json.Raw != "" {
				// using the json data in interface configuration
				b.Raw = body.Json.Raw
			} else {
				b.Raw = jsonx.MarshalToStringIgnoreError(l.getValueBySchema(projectId, importType, body.Json, nil))
			}
		} else {
			b.Raw = "{}"
		}
	case common.ConstMediaTypeTextPlain:
		b.Type = pb.HttpRequestComponent_Body_TEXT_PLAIN
		if body.Text != nil {
			b.Raw = body.Text.Example
		}
	default:
		b.Type = pb.HttpRequestComponent_Body_NONE
	}

	component.Body = b
}

func (l *MockInterfaceDocumentLogic) getValueBySchema(
	projectId string, importType common.InterfaceDefinitionImportType, schema *pb.Schema, stack *dll.List,
) any {
	if stack == nil {
		stack = dll.New()
	}

	switch schema.GetType() {
	case common.ConstInterfaceDefinitionFieldTypeString:
		return processStringType(schema)
	case common.ConstInterfaceDefinitionFieldTypeInteger:
		return processIntegerType(schema)
	case common.ConstInterfaceDefinitionFieldTypeNumber:
		return processNumberType(schema)
	case common.ConstInterfaceDefinitionFieldTypeBoolean:
		return processBooleanType(schema)
	case common.ConstInterfaceDefinitionFieldTypeArray:
		return l.getValueFromArraySchema(projectId, importType, schema, stack)
	case common.ConstInterfaceDefinitionFieldTypeObject:
		return l.getValueFromObjectSchema(projectId, importType, schema, stack)
	case common.ConstInterfaceDefinitionFieldTypeSchema:
		var (
			def = logic.ConvertValueToAny(schema.GetDefault())
			emp = logic.ConvertValueToAny(schema.GetExample())
			ref = schema.GetRef()
		)

		if def != nil {
			return def
		} else if emp != nil {
			return emp
		} else if ref != nil {
			if !stack.Contains(ref.GetSchemaId()) {
				stack.Prepend(ref.GetSchemaId())
				defer stack.Remove(0)

				return l.processSchemaType(projectId, importType, ref, stack)
			}
		}

		return nil
	case common.ConstInterfaceDefinitionFieldTypeOneOf, common.ConstInterfaceDefinitionFieldTypeAnyOf:
		return l.getValueFromOneOfOrAnyOfSchema(projectId, importType, schema, stack)
	case common.ConstInterfaceDefinitionFieldTypeAllOf:
		return l.getValueFromAllOfSchema(projectId, importType, schema, stack)
	}

	return nil
}

func (l *MockInterfaceDocumentLogic) getValueFromArraySchema(
	projectId string, importType common.InterfaceDefinitionImportType, schema *pb.Schema, stack *dll.List,
) any {
	if schema.GetType() != common.ConstInterfaceDefinitionFieldTypeArray {
		return nil
	}

	a := make([]any, 0, 1)
	items := schema.GetItems()
	if items != nil {
		a = append(a, l.getValueBySchema(projectId, importType, items, stack))
	}

	return a
}

func (l *MockInterfaceDocumentLogic) getValueFromObjectSchema(
	projectId string, importType common.InterfaceDefinitionImportType, schema *pb.Schema, stack *dll.List,
) any {
	if schema.GetType() != common.ConstInterfaceDefinitionFieldTypeObject {
		return nil
	}

	properties := schema.GetProperties()
	o := make(map[string]any, len(properties))
	for n, p := range properties {
		// `oneOf`类型 且 `protobuf`协议的导入类型
		if p.GetType() == common.ConstInterfaceDefinitionFieldTypeOneOf && stringx.Contains(
			common.InterfaceDefinitionProtobufImportTypes, importType,
		) {
			// `protobuf`的`oneof`不需要最外层的字段
			for n_, p_ := range p.GetProperties() {
				o[n_] = l.getValueBySchema(projectId, importType, p_, stack)
				break
			}
		} else {
			o[n] = l.getValueBySchema(projectId, importType, p, stack)
		}
	}

	return o
}

func (l *MockInterfaceDocumentLogic) getValueFromOneOfOrAnyOfSchema(
	projectId string, importType common.InterfaceDefinitionImportType, schema *pb.Schema, stack *dll.List,
) any {
	typ := schema.GetType()
	if typ != common.ConstInterfaceDefinitionFieldTypeOneOf && typ != common.ConstInterfaceDefinitionFieldTypeAnyOf {
		return nil
	}

	for _, p := range schema.GetProperties() {
		// `oneOf`和`anyOf`都只取第一个
		return l.getValueBySchema(projectId, importType, p, stack)
	}

	return nil
}

func (l *MockInterfaceDocumentLogic) getValueFromAllOfSchema(
	projectId string, importType common.InterfaceDefinitionImportType, schema *pb.Schema, stack *dll.List,
) any {
	if schema.GetType() != common.ConstInterfaceDefinitionFieldTypeAllOf {
		return nil
	}

	properties := schema.GetProperties()
	o := make(map[string]any)
	for _, p := range properties {
		val := l.getValueBySchema(projectId, importType, p, stack)
		if m, ok := val.(map[string]any); ok {
			for k, v := range m {
				o[k] = v
			}
		}
	}

	return o
}

func (l *MockInterfaceDocumentLogic) processSchemaType(
	projectId string, importType common.InterfaceDefinitionImportType, ref *pb.RefSchema, stack *dll.List,
) any {
	is, err := model.CheckInterfaceSchemaBySchemaId(l.ctx, l.svcCtx.InterfaceSchemaModel, projectId, ref.GetSchemaId())
	if err != nil {
		l.Logger.Error(err)
		return nil
	}

	var s pb.Schema
	if err = protobuf.UnmarshalJSONFromString(is.Data, &s); err != nil {
		l.Logger.Errorf("failed to unmarshal document data[%s], error: %+v", is.Data, err)
		return nil
	}

	return l.getValueBySchema(projectId, importType, &s, stack)
}

func processStringType(schema *pb.Schema) string {
	if schema.Type != common.ConstInterfaceDefinitionFieldTypeString {
		return ""
	}

	vs := make([]any, 0, 2+len(schema.Enum))
	vs = append(vs, logic.ConvertValueToAny(schema.Default))
	vs = append(vs, logic.ConvertValuesToAny(schema.Enum...)...)
	vs = append(vs, logic.ConvertValueToAny(schema.Example))

	for _, v := range vs {
		s, ok := v.(string)
		if ok {
			return s
		}
	}

	return ""
}

func processIntegerType(schema *pb.Schema) any {
	if schema.GetType() != common.ConstInterfaceDefinitionFieldTypeInteger {
		return 0
	}

	vs := make([]any, 0, 2+len(schema.GetEnum()))
	vs = append(vs, logic.ConvertValueToAny(schema.GetDefault()))
	vs = append(vs, logic.ConvertValuesToAny(schema.GetEnum()...)...)
	vs = append(vs, logic.ConvertValueToAny(schema.GetExample()))

	for _, v := range vs {
		rv := reflect.ValueOf(v)
		switch rv.Kind() {
		case reflect.Int, reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64:
			i64 := rv.Int()
			if schema.Format == common.ConstInterfaceDefinitionFieldFormatInt32 {
				return int32(i64)
			}

			return i64
		case reflect.Uint, reflect.Uint8, reflect.Uint16, reflect.Uint32, reflect.Uint64:
			ui64 := rv.Uint()
			if schema.Format == common.ConstInterfaceDefinitionFieldFormatInt32 {
				return int32(ui64)
			}

			return int64(ui64)
		case reflect.String: // include `json.Number`
			i64, err := strconv.ParseInt(rv.String(), 0, 0)
			if err == nil {
				if schema.Format == common.ConstInterfaceDefinitionFieldFormatInt32 {
					return int32(i64)
				}

				return i64
			}
		default:
			continue
		}
	}

	return 0
}

func processNumberType(schema *pb.Schema) any {
	if schema.GetType() != common.ConstInterfaceDefinitionFieldTypeNumber {
		return 0.0
	}

	vs := make([]any, 0, 2+len(schema.GetEnum()))
	vs = append(vs, logic.ConvertValueToAny(schema.GetDefault()))
	vs = append(vs, logic.ConvertValuesToAny(schema.GetEnum()...)...)
	vs = append(vs, logic.ConvertValueToAny(schema.GetExample()))

	for _, v := range vs {
		rv := reflect.ValueOf(v)
		switch rv.Kind() {
		case reflect.Int, reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64:
			i64 := rv.Int()
			if schema.Format == common.ConstInterfaceDefinitionFieldFormatFloat {
				return float32(i64)
			}

			return float64(i64)
		case reflect.Uint, reflect.Uint8, reflect.Uint16, reflect.Uint32, reflect.Uint64:
			ui64 := rv.Uint()
			if schema.Format == common.ConstInterfaceDefinitionFieldFormatFloat {
				return float32(ui64)
			}

			return float64(ui64)
		case reflect.Float32, reflect.Float64:
			f64 := rv.Float()
			if schema.Format == common.ConstInterfaceDefinitionFieldFormatFloat {
				return float32(f64)
			}

			return f64
		case reflect.String: // include `json.Number`
			f64, err := strconv.ParseFloat(rv.String(), 64)
			if err == nil {
				if schema.Format == common.ConstInterfaceDefinitionFieldFormatFloat {
					return float32(f64)
				}

				return f64
			}
		default:
			continue
		}
	}

	return 0.0
}

func processBooleanType(schema *pb.Schema) bool {
	if schema.GetType() != common.ConstInterfaceDefinitionFieldTypeBoolean {
		return false
	}

	for _, v := range []any{
		logic.ConvertValueToAny(schema.GetDefault()),
		logic.ConvertValueToAny(schema.GetExample()),
	} {
		rv := reflect.ValueOf(v)
		switch rv.Kind() {
		case reflect.Int, reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64:
			if rv.Int() != 0 {
				return true
			}

			return false
		case reflect.Uint, reflect.Uint8, reflect.Uint16, reflect.Uint32, reflect.Uint64:
			if rv.Uint() != 0 {
				return true
			}

			return false
		case reflect.Float32, reflect.Float64:
			if rv.Float() != 0 {
				return true
			}

			return false
		case reflect.String:
			switch rv.String() {
			case "1", "t", "T", "true", "TRUE", "True", "y", "Y", "yes", "Yes", "YES":
				return true
			case "0", "f", "F", "false", "FALSE", "False", "n", "N", "no", "No", "NO":
			}

			return false
		case reflect.Bool:
			return rv.Bool()
		default:
			continue
		}
	}

	return false
}
