package interfacedefinitionservicelogic

import (
	"net/http"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/userinfo"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

var systemUser = userinfo.System()

var (
	mediaTypeApplicationJsonSchema = &pb.Schema{
		Title:         ConstMediaTypeApplicationJsonHeaderContentType,
		Type:          common.ConstInterfaceDefinitionFieldTypeString,
		Index:         1,
		FieldRequired: true,
		Default:       logic.ConvertAnyToValue(common.ConstMediaTypeApplicationJson),
		Example:       logic.ConvertAnyToValue(common.ConstMediaTypeApplicationJson),
	}

	tdsCommonCallReqSchema = &pb.Schema{
		Title: common.ConstJsonSchemaRootTitle,
		Type:  common.ConstInterfaceDefinitionFieldTypeObject,
		Properties: types.FieldSchema{
			ConstTDSCommonCallFieldClient: &pb.Schema{
				Title:         ConstTDSCommonCallFieldClient,
				Type:          common.ConstInterfaceDefinitionFieldTypeSchema,
				Index:         1,
				FieldRequired: true,
				Ref: &pb.RefSchema{
					SchemaId:    common.ConstTDSSearchClientSchemaId,
					FullName:    common.ConstTDSServiceName + "." + common.ConstTDSSearchClientSchemaName,
					DisplayName: common.ConstTDSSearchClientSchemaName,
				},
			},
			ConstTDSCommonCallFieldApiName: &pb.Schema{
				Title:         ConstTDSCommonCallFieldApiName,
				Type:          common.ConstInterfaceDefinitionFieldTypeString,
				Description:   "Api Name",
				Index:         2,
				FieldRequired: true,
				Example:       logic.ConvertAnyToValue("ChannelEnter"),
			},
			ConstTDSCommonCallFieldFixDict: &pb.Schema{
				Title:       ConstTDSCommonCallFieldFixDict,
				Type:        common.ConstInterfaceDefinitionFieldTypeSchema,
				Description: "Fix Dict",
				Index:       3,
				Ref: &pb.RefSchema{
					SchemaId:    common.ConstTDSFixDictSchemaId,
					FullName:    common.ConstTDSServiceName + "." + common.ConstTDSFixDictSchemaName,
					DisplayName: common.ConstTDSFixDictSchemaName,
				},
			},
			ConstTDSCommonCallFieldProtoJson: &pb.Schema{
				Title:       ConstTDSCommonCallFieldProtoJson,
				Type:        common.ConstInterfaceDefinitionFieldTypeObject,
				Description: "ProtoBuf Json",
				Index:       4,
			},
			ConstTDSCommonCallFieldProtoFile: &pb.Schema{
				Title:       ConstTDSCommonCallFieldProtoFile,
				Type:        common.ConstInterfaceDefinitionFieldTypeString,
				Description: "Proto File",
				Index:       5,
			},
			ConstTDSCommonCallFieldProtoMsg: &pb.Schema{
				Title:       ConstTDSCommonCallFieldProtoMsg,
				Type:        common.ConstInterfaceDefinitionFieldTypeString,
				Description: "Proto Msg",
				Index:       6,
			},
			ConstTDSCommonCallFieldReqMsg: &pb.Schema{
				Title:       ConstTDSCommonCallFieldReqMsg,
				Type:        common.ConstInterfaceDefinitionFieldTypeString,
				Description: "Req Msg",
				Index:       7,
			},
			ConstTDSCommonCallFieldStubName: &pb.Schema{
				Title:       ConstTDSCommonCallFieldStubName,
				Type:        common.ConstInterfaceDefinitionFieldTypeString,
				Description: "Stub Name",
				Index:       8,
			},
			ConstTDSCommonCallFieldMetadata: &pb.Schema{
				Title:       ConstTDSCommonCallFieldMetadata,
				Type:        common.ConstInterfaceDefinitionFieldTypeString,
				Description: "Metadata",
				Index:       9,
			},
			ConstTDSCommonCallFieldHeaders: &pb.Schema{
				Title:       ConstTDSCommonCallFieldHeaders,
				Type:        common.ConstInterfaceDefinitionFieldTypeString,
				Description: "Headers",
				Index:       10,
			},
			ConstTDSCommonCallFieldMethod: &pb.Schema{
				Title:       ConstTDSCommonCallFieldMethod,
				Type:        common.ConstInterfaceDefinitionFieldTypeString,
				Description: "Method",
				Index:       11,
			},
		},
		Required: []string{ConstTDSCommonCallFieldClient, ConstTDSCommonCallFieldApiName},
	}
	tdsSearchClientSchema = &pb.Schema{
		Title: common.ConstJsonSchemaRootTitle,
		Type:  common.ConstInterfaceDefinitionFieldTypeObject,
		Properties: types.FieldSchema{
			ConstTDSCommonCallFieldCid: &pb.Schema{
				Title:       ConstTDSCommonCallFieldCid,
				Type:        common.ConstInterfaceDefinitionFieldTypeInteger,
				Description: "Client ID",
				Index:       1,
			},
			ConstTDSCommonCallFieldAccount: &pb.Schema{
				Title:       ConstTDSCommonCallFieldAccount,
				Type:        common.ConstInterfaceDefinitionFieldTypeString,
				Description: "Account",
				Index:       2,
			},
			ConstTDSCommonCallFieldUid: &pb.Schema{
				Title:       ConstTDSCommonCallFieldUid,
				Type:        common.ConstInterfaceDefinitionFieldTypeInteger,
				Description: "User ID",
				Index:       3,
			},
			ConstTDSCommonCallFieldUri: &pb.Schema{
				Title:       ConstTDSCommonCallFieldUri,
				Type:        common.ConstInterfaceDefinitionFieldTypeString,
				Description: "Uri",
				Index:       4,
			},
			ConstTDSCommonCallFieldDrvType: &pb.Schema{
				Title:       ConstTDSCommonCallFieldDrvType,
				Type:        common.ConstInterfaceDefinitionFieldTypeInteger,
				Description: "Driver Type",
				Index:       5,
				Enum:        logic.ConvertAnyToValues(1, 2, 3, 4, 5, 6, 7),
				Enums: []*pb.EnumValDesc{
					{
						Value:       logic.ConvertAnyToValue(1),
						Description: "HTTP",
					},
					{
						Value:       logic.ConvertAnyToValue(2),
						Description: "WS",
					},
					{
						Value:       logic.ConvertAnyToValue(3),
						Description: "TCP",
					},
					{
						Value:       logic.ConvertAnyToValue(4),
						Description: "KAFKA",
					},
					{
						Value:       logic.ConvertAnyToValue(5),
						Description: "MYSQL",
					},
					{
						Value:       logic.ConvertAnyToValue(6),
						Description: "REDIS",
					},
					{
						Value:       logic.ConvertAnyToValue(7),
						Description: "GRPC",
					},
				},
			},
			ConstTDSCommonCallFieldProdType: &pb.Schema{
				Title:       ConstTDSCommonCallFieldProdType,
				Type:        common.ConstInterfaceDefinitionFieldTypeInteger,
				Description: "Product Type",
				Index:       6,
				Enum:        logic.ConvertAnyToValues(1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15),
				Enums: []*pb.EnumValDesc{
					{
						Value:       logic.ConvertAnyToValue(1),
						Description: "TT_APP",
					},
					{
						Value:       logic.ConvertAnyToValue(2),
						Description: "YP_APP",
					},
					{
						Value:       logic.ConvertAnyToValue(3),
						Description: "KJ_APP",
					},
					{
						Value:       logic.ConvertAnyToValue(4),
						Description: "TT_WEB",
					},
					{
						Value:       logic.ConvertAnyToValue(5),
						Description: "YP_WEB",
					},
					{
						Value:       logic.ConvertAnyToValue(6),
						Description: "KJ_WEB",
					},
					{
						Value:       logic.ConvertAnyToValue(7),
						Description: "MP",
					},
					{
						Value:       logic.ConvertAnyToValue(8),
						Description: "DB",
					},
					{
						Value:       logic.ConvertAnyToValue(9),
						Description: "TTCHAT_APP",
					},
					{
						Value:       logic.ConvertAnyToValue(10),
						Description: "GRPC",
					},
					{
						Value:       logic.ConvertAnyToValue(11),
						Description: "KAFKA",
					},
					{
						Value:       logic.ConvertAnyToValue(12),
						Description: "REC",
					},
					{
						Value:       logic.ConvertAnyToValue(13),
						Description: "WEFLY",
					},
					{
						Value:       logic.ConvertAnyToValue(14),
						Description: "BPM",
					},
					{
						Value:       logic.ConvertAnyToValue(15),
						Description: "TD",
					},
				},
			},
			ConstTDSCommonCallFieldPlatformType: &pb.Schema{
				Title:       ConstTDSCommonCallFieldPlatformType,
				Type:        common.ConstInterfaceDefinitionFieldTypeInteger,
				Description: "Platform Type",
				Index:       7,
				Enum:        logic.ConvertAnyToValues(1, 3),
				Enums: []*pb.EnumValDesc{
					{
						Value:       logic.ConvertAnyToValue(1),
						Description: "MOBILE",
					},
					{
						Value:       logic.ConvertAnyToValue(3),
						Description: "PC",
					},
				},
			},
		},
	}
	tdsFixDictSchema = &pb.Schema{
		Title: common.ConstJsonSchemaRootTitle,
		Type:  common.ConstInterfaceDefinitionFieldTypeObject,
		Properties: types.FieldSchema{
			ConstTDSCommonCallFieldCmd: &pb.Schema{
				Title:       ConstTDSCommonCallFieldCmd,
				Type:        common.ConstInterfaceDefinitionFieldTypeInteger,
				Description: "TT Cmd Value",
				Index:       1,
				Example:     logic.ConvertAnyToValue(423),
			},
			ConstTDSCommonCallFieldProtocol: &pb.Schema{
				Title:       ConstTDSCommonCallFieldProtocol,
				Type:        common.ConstInterfaceDefinitionFieldTypeString,
				Description: "SD Protocol Value",
				Index:       2,
			},
			ConstTDSCommonCallFieldPacket: &pb.Schema{
				Title:       ConstTDSCommonCallFieldPacket,
				Type:        common.ConstInterfaceDefinitionFieldTypeString,
				Description: "SD Packet Value",
				Index:       3,
			},
		},
	}
	tdsCommonRespSchema = &pb.Schema{
		Title: common.ConstJsonSchemaRootTitle,
		Type:  common.ConstInterfaceDefinitionFieldTypeObject,
		Properties: types.FieldSchema{
			ConstTDSCommonCallFieldCode: &pb.Schema{
				Title:         ConstTDSCommonCallFieldCode,
				Type:          common.ConstInterfaceDefinitionFieldTypeInteger,
				Index:         1,
				FieldRequired: true,
				Default:       logic.ConvertAnyToValue(http.StatusOK),
			},
			ConstTDSCommonCallFieldMessage: &pb.Schema{
				Title:         ConstTDSCommonCallFieldMessage,
				Type:          common.ConstInterfaceDefinitionFieldTypeString,
				Index:         2,
				FieldRequired: true,
				Default:       logic.ConvertAnyToValue("请求成功"),
			},
			ConstTDSCommonCallFieldData: &pb.Schema{
				Title:         ConstTDSCommonCallFieldData,
				Type:          common.ConstInterfaceDefinitionFieldTypeAny,
				Index:         3,
				FieldRequired: true,
			},
			ConstTDSCommonCallFieldElapsedTime: &pb.Schema{
				Title:  ConstTDSCommonCallFieldElapsedTime,
				Type:   common.ConstInterfaceDefinitionFieldTypeNumber,
				Index:  4,
				Format: common.ConstInterfaceDefinitionFieldFormatFloat,
			},
			ConstTDSCommonCallFieldRet: &pb.Schema{
				Title: ConstTDSCommonCallFieldRet,
				Type:  common.ConstInterfaceDefinitionFieldTypeInteger,
				Index: 5,
			},
			ConstTDSCommonCallFieldTraceId: &pb.Schema{
				Title:   ConstTDSCommonCallFieldTraceId,
				Type:    common.ConstInterfaceDefinitionFieldTypeString,
				Index:   6,
				Default: logic.ConvertAnyToValue(""),
			},
			ConstTDSCommonCallFieldApmId: &pb.Schema{
				Title:   ConstTDSCommonCallFieldApmId,
				Type:    common.ConstInterfaceDefinitionFieldTypeString,
				Index:   7,
				Default: logic.ConvertAnyToValue(""),
			},
		},
		Required: []string{ConstTDSCommonCallFieldCode, ConstTDSCommonCallFieldMessage, ConstTDSCommonCallFieldData},
	}
	tdsErrorRespSchema = &pb.Schema{
		Title: common.ConstJsonSchemaRootTitle,
		Type:  common.ConstInterfaceDefinitionFieldTypeObject,
		Properties: types.FieldSchema{
			ConstTDSCommonCallFieldCode: &pb.Schema{
				Title:         ConstTDSCommonCallFieldCode,
				Type:          common.ConstInterfaceDefinitionFieldTypeInteger,
				Index:         1,
				FieldRequired: true,
				Default:       logic.ConvertAnyToValue(http.StatusInternalServerError),
			},
			ConstTDSCommonCallFieldMessage: &pb.Schema{
				Title:         ConstTDSCommonCallFieldMessage,
				Type:          common.ConstInterfaceDefinitionFieldTypeString,
				Index:         2,
				FieldRequired: true,
				Default:       logic.ConvertAnyToValue("请求失败"),
			},
			ConstTDSCommonCallFieldData: &pb.Schema{
				Title:         ConstTDSCommonCallFieldData,
				Type:          common.ConstInterfaceDefinitionFieldTypeNull,
				Index:         3,
				FieldRequired: true,
			},
			ConstTDSCommonCallFieldElapsedTime: &pb.Schema{
				Title:  ConstTDSCommonCallFieldElapsedTime,
				Type:   common.ConstInterfaceDefinitionFieldTypeNumber,
				Index:  4,
				Format: common.ConstInterfaceDefinitionFieldFormatFloat,
			},
			ConstTDSCommonCallFieldRet: &pb.Schema{
				Title: ConstTDSCommonCallFieldRet,
				Type:  common.ConstInterfaceDefinitionFieldTypeInteger,
				Index: 5,
			},
			ConstTDSCommonCallFieldTraceId: &pb.Schema{
				Title:   ConstTDSCommonCallFieldTraceId,
				Type:    common.ConstInterfaceDefinitionFieldTypeString,
				Index:   6,
				Default: logic.ConvertAnyToValue(""),
			},
			ConstTDSCommonCallFieldApmId: &pb.Schema{
				Title:   ConstTDSCommonCallFieldApmId,
				Type:    common.ConstInterfaceDefinitionFieldTypeString,
				Index:   7,
				Default: logic.ConvertAnyToValue(""),
			},
		},
		Required: []string{ConstTDSCommonCallFieldCode, ConstTDSCommonCallFieldMessage, ConstTDSCommonCallFieldData},
	}
)

var (
	ttMetaCommonCallReqSchema = &pb.Schema{
		Title: common.ConstJsonSchemaRootTitle,
		Type:  common.ConstInterfaceDefinitionFieldTypeObject,
		Properties: types.FieldSchema{
			ConstTTMetaCommonCallFieldClient: &pb.Schema{
				Title:         ConstTTMetaCommonCallFieldClient,
				Type:          common.ConstInterfaceDefinitionFieldTypeSchema,
				Index:         1,
				FieldRequired: true,
				Ref: &pb.RefSchema{
					SchemaId:    common.ConstTTMetaClientSearchClientSchemaId,
					FullName:    common.ConstTTMetaServiceName + "." + common.ConstTTMetaClientSearchClientSchemaName,
					DisplayName: common.ConstTTMetaClientSearchClientSchemaName,
				},
			},
			ConstTTMetaCommonCallFieldApiName: &pb.Schema{
				Title:         ConstTTMetaCommonCallFieldApiName,
				Type:          common.ConstInterfaceDefinitionFieldTypeString,
				Description:   "Method Name",
				Index:         2,
				FieldRequired: true,
				Example:       logic.ConvertAnyToValue("CreateComment"),
			},
			ConstTTMetaCommonCallFieldServerName: &pb.Schema{
				Title:         ConstTTMetaCommonCallFieldServerName,
				Type:          common.ConstInterfaceDefinitionFieldTypeString,
				Description:   "Server Name",
				Index:         3,
				FieldRequired: true,
				Example:       logic.ConvertAnyToValue("Thread"),
			},
			ConstTDSCommonCallFieldProtoJson: &pb.Schema{
				Title:       ConstTDSCommonCallFieldProtoJson,
				Type:        common.ConstInterfaceDefinitionFieldTypeObject,
				Description: "ProtoBuf Json",
				Index:       4,
			},
		},
		Required: []string{
			ConstTTMetaCommonCallFieldClient, ConstTTMetaCommonCallFieldApiName, ConstTTMetaCommonCallFieldServerName,
		},
	}
	ttMetaSearchClientSchema = &pb.Schema{
		Title: common.ConstJsonSchemaRootTitle,
		Type:  common.ConstInterfaceDefinitionFieldTypeObject,
		Properties: types.FieldSchema{
			ConstTTMetaCommonCallFieldUid: &pb.Schema{
				Title:       ConstTTMetaCommonCallFieldUid,
				Type:        common.ConstInterfaceDefinitionFieldTypeInteger,
				Description: "User ID",
				Index:       1,
			},
		},
	}
	ttMetaCommonRespSchema = &pb.Schema{
		Title: common.ConstJsonSchemaRootTitle,
		Type:  common.ConstInterfaceDefinitionFieldTypeObject,
		Properties: types.FieldSchema{
			ConstTTMetaCommonCallFieldCode: &pb.Schema{
				Title:         ConstTTMetaCommonCallFieldCode,
				Type:          common.ConstInterfaceDefinitionFieldTypeInteger,
				Index:         1,
				FieldRequired: true,
				Default:       logic.ConvertAnyToValue(http.StatusOK),
			},
			ConstTTMetaCommonCallFieldMessage: &pb.Schema{
				Title:         ConstTTMetaCommonCallFieldMessage,
				Type:          common.ConstInterfaceDefinitionFieldTypeString,
				Index:         2,
				FieldRequired: true,
				Default:       logic.ConvertAnyToValue(""),
			},
			ConstTTMetaCommonCallFieldData: &pb.Schema{
				Title:         ConstTDSCommonCallFieldData,
				Type:          common.ConstInterfaceDefinitionFieldTypeAny,
				Index:         3,
				FieldRequired: true,
			},
		},
		Required: []string{ConstTDSCommonCallFieldCode, ConstTDSCommonCallFieldMessage, ConstTDSCommonCallFieldData},
	}
)

var (
	apiProxyCommonApiCallReqSchema = &pb.Schema{
		Title: common.ConstJsonSchemaRootTitle,
		Type:  common.ConstInterfaceDefinitionFieldTypeObject,
		Properties: types.FieldSchema{
			ConstApiProxyCommonApiCallFieldMethod: &pb.Schema{
				Title:       ConstApiProxyCommonApiCallFieldMethod,
				Type:        common.ConstInterfaceDefinitionFieldTypeString,
				Description: "Method",
				Index:       1,
			},
			ConstApiProxyCommonApiCallFieldCid: &pb.Schema{
				Title:         ConstApiProxyCommonApiCallFieldCid,
				Type:          common.ConstInterfaceDefinitionFieldTypeString,
				Description:   "Cid",
				Index:         2,
				FieldRequired: true,
			},
			ConstApiProxyCommonApiCallFieldHeaders: &pb.Schema{
				Title:       ConstApiProxyCommonApiCallFieldHeaders,
				Type:        common.ConstInterfaceDefinitionFieldTypeObject,
				Description: "Headers",
				Index:       3,
			},
			ConstApiProxyCommonApiCallFieldBody: &pb.Schema{
				Title:       ConstApiProxyCommonApiCallFieldBody,
				Type:        common.ConstInterfaceDefinitionFieldTypeObject,
				Description: "ProtoBuf Json Body",
				Index:       4,
			},
			ConstApiProxyCommonApiCallFieldUrl: &pb.Schema{
				Title:       ConstApiProxyCommonApiCallFieldUrl,
				Type:        common.ConstInterfaceDefinitionFieldTypeString,
				Description: "Url",
				Index:       5,
			},
		},
		Required: []string{
			ConstApiProxyCommonApiCallFieldCid,
		},
	}

	apiProxyRespSchema = &pb.Schema{
		Title: common.ConstJsonSchemaRootTitle,
		Type:  common.ConstInterfaceDefinitionFieldTypeObject,
		Properties: types.FieldSchema{
			ConstApiProxyCommonApiCallFieldCode: &pb.Schema{
				Title:         ConstApiProxyCommonApiCallFieldCode,
				Type:          common.ConstInterfaceDefinitionFieldTypeInteger,
				Index:         1,
				FieldRequired: true,
				Default:       logic.ConvertAnyToValue(http.StatusOK),
			},
			ConstApiProxyCommonApiCallFieldMessage: &pb.Schema{
				Title:         ConstApiProxyCommonApiCallFieldMessage,
				Type:          common.ConstInterfaceDefinitionFieldTypeString,
				Index:         2,
				FieldRequired: true,
				Default:       logic.ConvertAnyToValue(""),
			},
			ConstApiProxyCommonApiCallFieldData: &pb.Schema{
				Title:         ConstApiProxyCommonApiCallFieldData,
				Type:          common.ConstInterfaceDefinitionFieldTypeSchema,
				Index:         3,
				FieldRequired: true,
				Ref: &pb.RefSchema{
					SchemaId:    common.ConstApiProxyCommonRespDataSchemaId,
					FullName:    common.ConstApiProxyServiceName + "." + common.ConstApiProxyCommonRespDataSchemaName,
					DisplayName: common.ConstApiProxyCommonRespDataSchemaName,
				},
			},
		},
		Required: []string{
			ConstApiProxyCommonApiCallFieldCode, ConstApiProxyCommonApiCallFieldMessage,
			ConstApiProxyCommonApiCallFieldData,
		},
	}

	apiProxyCommonRespDataSchema = &pb.Schema{
		Title: common.ConstJsonSchemaRootTitle,
		Type:  common.ConstInterfaceDefinitionFieldTypeObject,
		Properties: types.FieldSchema{
			ConstApiProxyCommonApiCallFieldCustomFields: &pb.Schema{
				Title:       ConstApiProxyCommonApiCallFieldCustomFields,
				Type:        common.ConstInterfaceDefinitionFieldTypeObject,
				Description: "Custom Fields",
				Index:       1,
			},
			ConstApiProxyCommonApiCallFieldCallResp: &pb.Schema{
				Title:         ConstApiProxyCommonApiCallFieldCallResp,
				Type:          common.ConstInterfaceDefinitionFieldTypeSchema,
				Description:   "Call Resp",
				Index:         2,
				FieldRequired: true,
				Ref: &pb.RefSchema{
					SchemaId:    common.ConstApiProxyCommonRespDataCallRespSchemaId,
					FullName:    common.ConstApiProxyServiceName + "." + common.ConstApiProxyCommonRespDataCallRespSchemaName,
					DisplayName: common.ConstApiProxyCommonRespDataCallRespSchemaName,
				},
			},
		},
	}

	apiProxyCommonRespDataCallRespSchema = &pb.Schema{
		Title: common.ConstJsonSchemaRootTitle,
		Type:  common.ConstInterfaceDefinitionFieldTypeObject,
		Properties: types.FieldSchema{
			ConstApiProxyCommonApiCallFieldCallRespHeaders: &pb.Schema{
				Title:       ConstApiProxyCommonApiCallFieldCallRespHeaders,
				Type:        common.ConstInterfaceDefinitionFieldTypeObject,
				Description: "Call Resp Headers",
				Index:       1,
			},
			ConstApiProxyCommonApiCallFieldCallRespBody: &pb.Schema{
				Title:         ConstApiProxyCommonApiCallFieldCallRespBody,
				Type:          common.ConstInterfaceDefinitionFieldTypeObject,
				Description:   "Call Resp Body",
				Index:         2,
				FieldRequired: true,
			},
			ConstApiProxyCommonApiCallFieldCallRespStatus: &pb.Schema{
				Title:         ConstApiProxyCommonApiCallFieldCallRespStatus,
				Type:          common.ConstInterfaceDefinitionFieldTypeInteger,
				Description:   "Call Resp Status",
				Index:         3,
				FieldRequired: true,
			},
		},
	}
)
