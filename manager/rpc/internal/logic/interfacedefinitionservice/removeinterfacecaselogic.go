package interfacedefinitionservicelogic

import (
	"context"
	"fmt"

	"github.com/hashicorp/go-multierror"
	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/mr"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/redislock"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type RemoveInterfaceCaseLogic struct {
	*BaseLogic
}

func NewRemoveInterfaceCaseLogic(ctx context.Context, svcCtx *svc.ServiceContext) *RemoveInterfaceCaseLogic {
	return &RemoveInterfaceCaseLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// RemoveInterfaceCase 删除接口用例
func (l *RemoveInterfaceCaseLogic) RemoveInterfaceCase(in *pb.RemoveInterfaceCaseReq) (
	resp *pb.RemoveInterfaceCaseResp, err error,
) {
	// validate the project_id in req
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, in.GetProjectId()); err != nil {
		return nil, err
	}

	// validate the document_id in req
	if _, err = model.CheckInterfaceDocumentByDocumentId(
		l.ctx, l.svcCtx.InterfaceDocumentModel, in.GetProjectId(), in.GetDocumentId(),
	); err != nil {
		return nil, err
	}

	caseIds := stringx.Distinct(in.GetCaseIds())

	workers := len(caseIds)
	if workers > common.ConstMRMaxWorkers {
		workers = common.ConstMRMaxWorkers
	}

	mr.ForEach(
		func(source chan<- any) {
			for _, caseId := range caseIds {
				source <- caseId
			}
		}, func(item any) {
			caseId, ok := item.(string)
			if !ok {
				err = multierror.Append(
					err, errorx.Err(
						errorx.InternalError, fmt.Sprintf("the interface case id[%v (%T)] is not a string", item, item),
					),
				)
			} else {
				if e := l.remove(in.GetProjectId(), in.GetDocumentId(), caseId); e != nil {
					err = multierror.Append(err, e)
				}
			}
		}, mr.WithContext(l.ctx), mr.WithWorkers(workers),
	)

	return &pb.RemoveInterfaceCaseResp{}, err
}

func (l *RemoveInterfaceCaseLogic) remove(projectId, documentId, caseId string) (err error) {
	// validate the case_id in req
	interfaceCase, err := model.CheckInterfaceCaseByCaseId(l.ctx, l.svcCtx.InterfaceCaseModel, projectId, documentId, caseId, "")
	if err != nil {
		return err
	}

	// validate the relationship with api suite
	rrs, err := l.svcCtx.ApiSuiteReferenceModel.FindReferenceByReference(l.ctx, projectId, common.ConstReferenceTypeInterfaceCase, caseId)
	if err != nil {
		return errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()),
			"failed to find interface case reference with project_id[%s] and case_id[%s], error: %+v",
			projectId, caseId, err,
		)
	} else if len(rrs) > 0 {
		return errors.WithStack(
			errorx.Errorf(
				errorx.ProhibitedBehavior,
				"the interface case[%s] in suite cannot be deleted, please remove the interface case from suite first",
				interfaceCase.Name,
			),
		)
	}

	// acquire redis lock
	key := fmt.Sprintf(
		"%s:%s:%s:%s", common.ConstLockInterfaceCaseProjectIdDocumentIdCaseIdPrefix, projectId, documentId, caseId,
	)
	lock, err := redislock.NewRedisLockAndAcquire(l.svcCtx.Redis, key, redislock.WithExpire(common.ConstLockExpireTime))
	if err != nil {
		return err
	}
	defer func() {
		// release redis lock
		e := lock.Release()
		if e != nil {
			l.Logger.Error(e)
			if re, ok := errorx.RootError(e); ok && re.Code() != errorx.ReleaseRedisLockFailure && err == nil {
				err = e
			}
		}
	}()

	return l.svcCtx.InterfaceCaseModel.Trans(
		l.ctx, func(context context.Context, session sqlx.Session) error {
			// Table: api_plan_reference_relationship
			if _, err := l.svcCtx.ApiPlanReferenceModel.RemoveByReferenceId(
				context, session, projectId, common.ConstReferenceTypeInterfaceCase, caseId,
			); err != nil {
				return errors.Wrapf(
					errorx.Err(errorx.DBError, err.Error()),
					"failed to remove api plan reference relationship with project_id[%s], reference_type[%s] and reference_id[%s], error: %+v",
					projectId, common.ConstReferenceTypeInterfaceCase, caseId, err,
				)
			}

			// Table: tag_reference_relationship
			if _, err := l.svcCtx.TagReferenceModel.RemoveByReferenceId(
				context, session, projectId, common.ConstReferenceTypeInterfaceCase, caseId, "",
			); err != nil {
				return errors.Wrapf(
					errorx.Err(errorx.DBError, err.Error()),
					"failed to remove tag reference relationship with project_id[%s], reference_type[%s] and reference_id[%s], error: %+v",
					projectId, common.ConstReferenceTypeInterfaceCase, caseId, err,
				)
			}

			// Table: function_reference_relationship
			if _, err := l.svcCtx.FunctionReferenceModel.RemoveByReferenceId(
				context, session, projectId, constants.InterfaceCase, caseId,
			); err != nil {
				return errors.Wrapf(
					errorx.Err(errorx.DBError, err.Error()),
					"failed to remove data processing function reference relationship with project_id[%s], reference_type[%s] and reference_id[%s], error: %+v",
					projectId, constants.InterfaceCase, caseId, err,
				)
			}

			// Table: review_record
			if _, err := l.svcCtx.ReviewRecordModel.RemoveByResource(
				context, session, projectId, common.ConstReviewResourceTypeInterfaceCase, caseId,
			); err != nil {
				return errors.Wrapf(
					errorx.Err(errorx.DBError, err.Error()),
					"failed to remove review record with project_id[%s], resource_type[%s] and resource_id[%s], error: %+v",
					projectId, common.ConstReviewResourceTypeInterfaceCase, caseId, err,
				)
			}

			// Table: component_group_reference_relationship
			if _, err := l.svcCtx.ComponentGroupReferenceModel.RemoveByReferenceId(
				context, session, projectId, constants.InterfaceCase, caseId,
			); err != nil {
				return errors.Wrapf(
					errorx.Err(errorx.DBError, err.Error()),
					"failed to remove component group reference relationship with project_id[%s], reference_type[%s] and reference_id[%s], error: %+v",
					projectId, constants.InterfaceCase, caseId, err,
				)
			}

			// Table: interface_case_element
			if _, err := l.svcCtx.InterfaceCaseElementModel.RemoveByCaseId(
				context, session, projectId, caseId,
			); err != nil {
				return errors.Wrapf(
					errorx.Err(errorx.DBError, err.Error()),
					"failed to remove interface case element with project_id[%s] and case_id[%s], error: %+v",
					projectId, caseId, err,
				)
			}

			// Table: component
			if _, err := l.svcCtx.ComponentModel.RemoveByParentId(
				context, session, projectId, caseId, constants.InterfaceCase,
			); err != nil {
				return errors.Wrapf(
					errorx.Err(errorx.DBError, err.Error()),
					"failed to remove component with project_id[%s], parent_id[%s], parent_type[%s], error: %+v",
					projectId, caseId, constants.InterfaceCase, err,
				)
			}

			// Table: interface_case
			if _, err := l.svcCtx.InterfaceCaseModel.RemoveByCaseId(
				context, session, projectId, documentId, caseId,
			); err != nil {
				return errors.Wrapf(
					errorx.Err(errorx.DBError, err.Error()),
					"failed to remove interface case with project_id[%s], document_id[%s] and case_id[%s], error: %+v",
					projectId, documentId, caseId, err,
				)
			}

			return nil
		},
	)
}
