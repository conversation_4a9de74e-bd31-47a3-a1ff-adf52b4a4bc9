package interfacedefinitionservicelogic

import (
	"context"
	"fmt"
	"strings"
	"sync"
	"sync/atomic"
	"time"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/mr"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/caller"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type (
	UpdateInterfaceCoverageDataLogic struct {
		*BaseLogic

		coverageData CoverageMap
		coverageLock sync.RWMutex
	}
	methodTeamItem struct {
		method  string
		team    string
		covered bool
	}
)

func NewUpdateInterfaceCoverageDataLogic(
	ctx context.Context, svcCtx *svc.ServiceContext,
) *UpdateInterfaceCoverageDataLogic {
	data := make(CoverageMap, constants.ConstDefaultMakeMapSize)
	data[defaultAllTeam] = &coverageItem{
		Team:  defaultAllTeam,
		Apis:  new(atomic.Uint32),
		Cases: new(atomic.Uint32),
	}

	return &UpdateInterfaceCoverageDataLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),

		coverageData: data,
	}
}

// UpdateInterfaceCoverageData 更新接口覆盖率数据
func (l *UpdateInterfaceCoverageDataLogic) UpdateInterfaceCoverageData(in *pb.UpdateInterfaceCoverageDataReq) (
	out *pb.UpdateInterfaceCoverageDataResp, err error,
) {
	projectID := in.GetProjectId()

	// validate the project_id in req
	project, err := model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, projectID)
	if err != nil {
		return nil, err
	}
	if project.CoverageEnabled == 0 {
		return nil, errorx.Errorf(
			errorx.ProhibitedBehavior,
			"the project has not enabled the interface coverage function, project_id: %s",
			projectID,
		)
	}

	documents, err := l.svcCtx.InterfaceDocumentModel.FindAll(l.ctx, projectID)
	if err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()),
			"failed to find interface documents by project_id, project_id: %s, error: %+v",
			projectID, err,
		)
	} else if len(documents) == 0 {
		l.Warnf("there are no interface documents in the project, project_id: %s", projectID)
		return &pb.UpdateInterfaceCoverageDataResp{}, nil
	}

	key := fmt.Sprintf("%s:%s", common.ConstLockUpdateInterfaceCoverageProjectIDPrefix, projectID)
	fn := func() error {
		_ = mr.MapReduceVoid[*model.InterfaceDocument, *methodTeamItem](
			func(source chan<- *model.InterfaceDocument) {
				for _, document := range documents {
					if !strings.EqualFold(document.Path, ConstApiProxyCommonApiCallPath) {
						continue
					}

					source <- document
				}
			},
			func(item *model.InterfaceDocument, writer mr.Writer[*methodTeamItem], cancel func(error)) {
				if item == nil {
					return
				}

				var err error
				defer func() {
					if err != nil {
						l.Error(err)
					}
				}()

				method, err := getMethodFromDocument(item)
				if err != nil {
					return
				}

				had := l.hasPublishedCase(projectID, item.DocumentId)
				if !had {
					l.Infof(
						"not found any published cases for the document, project_id: %s, document_id: %s, method: %s",
						projectID, item.DocumentId, method,
					)
				}
				writer.Write(
					&methodTeamItem{
						method:  method,
						team:    defaultAllTeam,
						covered: had,
					},
				)

				team, err := l.getTeamByMethod(method)
				if err != nil {
					return
				}
				writer.Write(
					&methodTeamItem{
						method:  method,
						team:    team,
						covered: had,
					},
				)
			},
			func(pipe <-chan *methodTeamItem, cancel func(error)) {
				for item := range pipe {
					if item == nil {
						continue
					}

					l.addAPI(item.team)
					if item.covered {
						l.addCase(item.team)
					}
				}
			},
			mr.WithContext(l.ctx), mr.WithWorkers(common.ConstMRMaxWorkers),
		)

		l.updateCoverageData(projectID)
		l.removeExpiredCoverageData(projectID, in.GetKeepDays())
		return nil
	}
	if err = caller.LockDo(l.svcCtx.Redis, key, fn); err != nil {
		return nil, err
	}

	return &pb.UpdateInterfaceCoverageDataResp{}, nil
}

func (l *UpdateInterfaceCoverageDataLogic) hasPublishedCase(projectID, documentID string) bool {
	cases, err := l.svcCtx.InterfaceCaseModel.FindLatestByDocumentId(l.ctx, projectID, documentID)
	if err != nil {
		return false
	}

	for _, v := range cases {
		if v.State == string(common.ConstResourceStatePublished) {
			return true
		}
	}

	return false
}

func (l *UpdateInterfaceCoverageDataLogic) addAPI(team string) {
	l.coverageLock.Lock()
	defer l.coverageLock.Unlock()

	_, ok := l.coverageData[team]
	if !ok {
		l.coverageData[team] = &coverageItem{
			Team:  team,
			Apis:  new(atomic.Uint32),
			Cases: new(atomic.Uint32),
		}
	}

	l.coverageData[team].Apis.Add(1)
}

func (l *UpdateInterfaceCoverageDataLogic) addCase(team string) {
	l.coverageLock.Lock()
	defer l.coverageLock.Unlock()

	_, ok := l.coverageData[team]
	if !ok {
		l.coverageData[team] = &coverageItem{
			Team:  team,
			Apis:  new(atomic.Uint32),
			Cases: new(atomic.Uint32),
		}
	}

	l.coverageData[team].Cases.Add(1)
}

func (l *UpdateInterfaceCoverageDataLogic) updateCoverageData(projectID string) {
	user := l.currentUser
	if user == nil {
		user = &systemUser.TokenUserInfo
	}
	now := time.Now()

	for _, v := range l.coverageData {
		item, err := l.svcCtx.InterfaceCoverageModel.FindOneByProjectIdTeamCountedAt(l.ctx, projectID, v.Team, now)
		if err != nil {
			if !errors.Is(err, model.ErrNotFound) {
				l.Errorf(
					"failed to find the interface coverage data, project_id: %s, team: %s, counted_at: %s, error: %+v",
					projectID, v.Team, now.Format("2006-01-02"), err,
				)
				continue
			}

			item = &model.InterfaceCoverage{
				ProjectId:     projectID,
				Team:          v.Team,
				NumberOfApis:  int64(v.Apis.Load()),
				NumberOfCases: int64(v.Cases.Load()),
				CountedAt:     now,
				CreatedBy:     user.Account,
				UpdatedBy:     user.Account,
				CreatedAt:     now,
				UpdatedAt:     now,
			}
			if _, err = l.svcCtx.InterfaceCoverageModel.Insert(l.ctx, nil, item); err != nil {
				l.Errorf(
					"failed to insert the interface coverage data, project_id: %s, team: %s, apis: %d, cases: %d, error: %+v",
					projectID, v.Team, v.Apis, v.Cases, err,
				)
			}
		} else {
			item.NumberOfApis = int64(v.Apis.Load())
			item.NumberOfCases = int64(v.Cases.Load())
			item.UpdatedBy = user.Account
			item.UpdatedAt = now
			if _, err = l.svcCtx.InterfaceCoverageModel.Update(l.ctx, nil, item); err != nil {
				l.Errorf(
					"failed to update the interface coverage data, project_id: %s, team: %s, apis: %d, cases: %d, error: %+v",
					projectID, v.Team, v.Apis, v.Cases, err,
				)
			}
		}
	}
}

func (l *UpdateInterfaceCoverageDataLogic) removeExpiredCoverageData(projectID string, days int64) {
	if days < 1 || days > 365 {
		days = defaultCoverageDataKeepDays
	}

	if err := l.svcCtx.InterfaceCoverageModel.DeleteBeforeNDaysRecordsByProjectID(
		l.ctx, nil, projectID, int(days),
	); err != nil {
		l.Errorf(
			"failed to delete the expired interface coverage data, project_id: %s, error: %+v",
			projectID, err,
		)
	}
}
