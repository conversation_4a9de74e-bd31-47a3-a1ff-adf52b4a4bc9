package interfacedefinitionservicelogic

import (
	"context"
	"math"

	"github.com/Masterminds/squirrel"
	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sqlbuilder"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
	reporterpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
)

type SearchInterfaceCaseLogic struct {
	*BaseLogic
}

func NewSearchInterfaceCaseLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SearchInterfaceCaseLogic {
	return &SearchInterfaceCaseLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// SearchInterfaceCase 搜索接口用例
func (l *SearchInterfaceCaseLogic) SearchInterfaceCase(in *pb.SearchInterfaceCaseReq) (
	resp *pb.SearchInterfaceCaseResp, err error,
) {
	resp = &pb.SearchInterfaceCaseResp{}

	// validate the project_id in req
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, in.GetProjectId()); err != nil {
		return nil, err
	}

	// validate the document_id in req
	if _, err = model.CheckInterfaceDocumentByDocumentId(
		l.ctx, l.svcCtx.InterfaceDocumentModel, in.GetProjectId(), in.GetDocumentId(),
	); err != nil {
		return nil, err
	}

	selectBuilder, countBuilder := l.generateSearchSqlBuilder(in)

	count, err := l.svcCtx.InterfaceCaseModel.FindCount(l.ctx, countBuilder)
	if err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()),
			"failed to count interface case with project_id[%s] and document_id[%s], error: %+v",
			in.GetProjectId(), in.GetDocumentId(), err,
		)
	}
	resp.TotalCount = uint64(count)
	resp.TotalPage = 1

	interfaceCases, err := l.svcCtx.InterfaceCaseModel.FindNoCacheByQuery(l.ctx, selectBuilder)
	if err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()),
			"failed to find interface case with project_id[%s] and document_id[%s], error: %+v",
			in.GetProjectId(), in.GetDocumentId(), err,
		)
	}

	interfaceCaseNum := len(interfaceCases)
	resp.Items = make([]*pb.InterfaceCase, 0, interfaceCaseNum)

	if interfaceCaseNum > 0 {
		hashMap := make(map[string]*pb.InterfaceCase, interfaceCaseNum)
		keys := make([]string, 0, interfaceCaseNum)

		for _, interfaceCase := range interfaceCases {
			ic := &pb.InterfaceCase{}
			if err = utils.Copy(ic, interfaceCase, l.converters...); err != nil {
				return nil, errors.Wrapf(
					errorx.Err(errorx.CopyToStructFailure, err.Error()),
					"failed to copy interface case[%+v] to response, error: %+v",
					interfaceCase, err,
				)
			}

			hashMap[ic.CaseId] = ic
			keys = append(keys, ic.CaseId)

			resp.Items = append(resp.Items, ic)
		}

		// call rpc interface of reporter
		latestRecord, err := l.svcCtx.ReporterRPC.GetCaseLatestRecord(
			l.ctx, &reporterpb.GetCaseLatestRecordRequest{
				ProjectId:   in.GetProjectId(),
				InterfaceId: in.GetDocumentId(),
				CaseIdArray: keys,
			},
		)
		if err != nil {
			// 调用`reporter`的rpc接口失败，暂不影响当前接口的返回
			l.Logger.Errorf("failed to call ReporterRPC.GetCaseLatestRecord, error: %+v", err)
		} else if latestRecord != nil {
			for _, record := range latestRecord.CaseRecordArray {
				if ic, ok := hashMap[record.CaseId]; ok {
					// 暂不返回`content`内容
					record.Content = ""
					ic.LatestRecord = record
				}
			}
		}
	}

	pagination := in.GetPagination()
	if pagination != nil {
		resp.CurrentPage = pagination.GetCurrentPage()
		resp.PageSize = pagination.GetPageSize()
		resp.TotalPage = uint64(math.Ceil(float64(resp.TotalCount) / float64(resp.PageSize)))
	}

	return resp, nil
}

func (l *SearchInterfaceCaseLogic) generateSearchSqlBuilder(req *pb.SearchInterfaceCaseReq) (sb, scb squirrel.SelectBuilder) {
	m := l.svcCtx.InterfaceCaseModel

	sb = m.SelectBuilder().Where(
		"`project_id` = ? AND `document_id` = ? AND `latest` = ?",
		req.GetProjectId(), req.GetDocumentId(), constants.IsLatestVersion,
	)
	scb = m.SelectCountBuilder().Where(
		"`project_id` = ? AND `document_id` = ? AND `latest` = ?",
		req.GetProjectId(), req.GetDocumentId(), constants.IsLatestVersion,
	)

	sb = sqlbuilder.SearchOptions(
		sb, sqlbuilder.WithCondition(m, req.GetCondition()), sqlbuilder.WithPagination(m, req.GetPagination()),
		sqlbuilder.WithSort(m, req.GetSort()),
	)
	scb = sqlbuilder.SearchOptions(scb, sqlbuilder.WithCondition(m, req.GetCondition()))

	return sb, scb
}
