package interfacedefinitionservicelogic

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type ViewInterfaceConfigLogic struct {
	*BaseLogic
}

func NewViewInterfaceConfigLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ViewInterfaceConfigLogic {
	return &ViewInterfaceConfigLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// ViewInterfaceConfig 查看接口配置
func (l *ViewInterfaceConfigLogic) ViewInterfaceConfig(in *pb.ViewInterfaceConfigReq) (resp *pb.ViewInterfaceConfigResp, err error) {
	// validate the project_id in req
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, in.GetProjectId()); err != nil {
		return nil, err
	}

	// validate the document_id in req
	if _, err = model.CheckInterfaceDocumentByDocumentId(l.ctx, l.svcCtx.InterfaceDocumentModel, in.GetProjectId(), in.GetDocumentId()); err != nil {
		return nil, err
	}

	// validate the config_id in req
	interfaceConfig, err := model.CheckInterfaceConfigByConfigId(l.ctx, l.svcCtx.InterfaceConfigurationModel, in.GetProjectId(), in.GetDocumentId(), in.GetConfigId())
	if err != nil {
		return nil, err
	}

	resp = &pb.ViewInterfaceConfigResp{Config: &pb.InterfaceConfig{}}
	if err = utils.Copy(resp.Config, interfaceConfig, l.converters...); err != nil {
		return nil, errors.Wrapf(errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy interface configuration[%+v] to response, error: %+v", interfaceConfig, err)
	}

	return resp, nil
}
