package interfacedefinitionservicelogic

import (
	"context"
	"fmt"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type ViewInterfaceCaseLogic struct {
	*BaseLogic
}

func NewViewInterfaceCaseLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ViewInterfaceCaseLogic {
	return &ViewInterfaceCaseLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// ViewInterfaceCase 查看接口用例
func (l *ViewInterfaceCaseLogic) ViewInterfaceCase(in *pb.ViewInterfaceCaseReq) (resp *pb.ViewInterfaceCaseResp, err error) {
	// validate the project_id in req
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, in.GetProjectId()); err != nil {
		return nil, err
	}

	// validate the document_id in req
	if in.GetDocumentId() != "" {
		if _, err = model.CheckInterfaceDocumentByDocumentId(l.ctx, l.svcCtx.InterfaceDocumentModel, in.GetProjectId(), in.GetDocumentId()); err != nil {
			return nil, err
		}
	}

	var fn func() (*model.InterfaceCase, error)
	var msg string

	if in.GetVersion() == "" {
		fn = func() (*model.InterfaceCase, error) {
			return l.svcCtx.InterfaceCaseModel.FindLatestOneNoCache(l.ctx, in.GetProjectId(), in.GetDocumentId(), in.GetCaseId())
		}
		msg = fmt.Sprintf("latest interface case with project_id[%s], document_id[%s] and case_id[%s]", in.GetProjectId(), in.GetDocumentId(), in.GetCaseId())
	} else {
		fn = func() (*model.InterfaceCase, error) {
			return l.svcCtx.InterfaceCaseModel.FindOneByProjectIdDocumentIdCaseIdVersion(l.ctx, in.GetProjectId(), in.GetDocumentId(), in.GetCaseId(), in.GetVersion())
		}
		msg = fmt.Sprintf("interface case with project_id[%s], document_id[%s], case_id[%s] and version[%s]", in.GetProjectId(), in.GetDocumentId(), in.GetCaseId(), in.GetVersion())
	}
	interfaceCase, err := fn()
	if err != nil {
		if err != model.ErrNotFound {
			return nil, errors.Wrapf(errorx.Err(errorx.DBError, err.Error()), "failed to find %s, error: %+v", msg, err)
		} else {
			return nil, errors.WithStack(errorx.Err(errorx.NotExists, fmt.Sprintf("%s doesn't exist", msg)))
		}
	}

	resp = &pb.ViewInterfaceCaseResp{Case: &pb.InterfaceCase{}}
	if err = utils.Copy(resp.Case, interfaceCase, l.converters...); err != nil {
		return nil, errors.Wrapf(errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy interface case[%+v] to response, error: %+v", interfaceCase, err)
	}

	resp.Case.Nodes, resp.Case.Edges, resp.Case.Combos, err = l.getElements(in.GetProjectId(), in.GetCaseId(), interfaceCase.Version)
	if err != nil {
		return nil, err
	}

	return resp, nil
}
