package interfacedefinitionservicelogic

import (
	"context"
	"database/sql"
	"fmt"
	"sort"

	"github.com/pkg/errors"
	"github.com/sergi/go-diff/diffmatchpatch"
	"github.com/zeromicro/go-zero/core/jsonx"
	"github.com/zeromicro/go-zero/core/stores/sqlx"

	qetconstants "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/redislock"
	qetutils "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	userpb "gitlab.ttyuyin.com/TestDevelopment/qet-backend-middleware/user/rpc/pb"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/utils"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/logic/fsm"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type ModifyInterfaceCaseLogic struct {
	*BaseLogic
}

func NewModifyInterfaceCaseLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ModifyInterfaceCaseLogic {
	return &ModifyInterfaceCaseLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// ModifyInterfaceCase 编辑接口用例
func (l *ModifyInterfaceCaseLogic) ModifyInterfaceCase(in *pb.ModifyInterfaceCaseReq) (
	resp *pb.ModifyInterfaceCaseResp, err error,
) {
	// validate the project_id in req
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, in.GetProjectId()); err != nil {
		return nil, err
	}

	// validate the document_id in req
	if _, err = model.CheckInterfaceDocumentByDocumentId(
		l.ctx, l.svcCtx.InterfaceDocumentModel, in.GetProjectId(), in.GetDocumentId(),
	); err != nil {
		return nil, err
	}

	// validate the case_id in req
	origin, err := model.CheckInterfaceCaseByCaseId(
		l.ctx, l.svcCtx.InterfaceCaseModel, in.GetProjectId(), in.GetDocumentId(), in.GetCaseId(), "",
	)
	if err != nil {
		return nil, err
	}

	// validate the interface case data is the same
	if change, err := l.hasChange(in, origin); err != nil {
		return nil, err
	} else if !change.Whole {
		l.Logger.Infof(
			"there is no change with interface case, project_id: %s, document_id: %s, case_id: %s",
			in.GetProjectId(), in.GetDocumentId(), in.GetCaseId(),
		)

		resp = &pb.ModifyInterfaceCaseResp{Case: &pb.InterfaceCase{}}
		if err = qetutils.Copy(resp.Case, origin, l.converters...); err != nil {
			return nil, errors.Wrapf(
				errorx.Err(errorx.CopyToStructFailure, err.Error()),
				"failed to copy interface case[%+v] to response, error: %+v", origin, err,
			)
		}
		return resp, nil
	}

	// validate the maintained_by in req
	if in.GetMaintainedBy() == "" && origin.MaintainedBy.Valid && origin.MaintainedBy.String != "" {
		return nil, errorx.Errorf(
			errorx.ProhibitedBehavior,
			"cannot change the maintainer of interface case to be empty, source: %s, target: %s",
			origin.MaintainedBy.String, in.GetMaintainedBy(),
		)
	}

	// validate the circular reference exists
	if err = logic.CheckCircularReference(
		l.ctx, l.svcCtx.ComponentGroupModel, in.GetProjectId(), in.GetRelations(),
	); err != nil {
		return nil, err
	}

	// acquire redis lock
	key := fmt.Sprintf(
		"%s:%s:%s:%s", common.ConstLockInterfaceCaseProjectIdDocumentIdCaseIdPrefix, in.GetProjectId(),
		in.GetDocumentId(), in.GetCaseId(),
	)
	lock, err := redislock.NewRedisLockAndAcquire(l.svcCtx.Redis, key, redislock.WithExpire(common.ConstLockExpireTime))
	if err != nil {
		return nil, err
	}
	defer func() {
		// release redis lock
		e := lock.Release()
		if e != nil {
			if re, ok := errorx.RootError(e); ok && re.Code() != errorx.ReleaseRedisLockFailure && err == nil {
				err = e
			}
		}
	}()

	interfaceCase, err := l.modify(in)
	if err != nil {
		return nil, err
	}

	resp = &pb.ModifyInterfaceCaseResp{Case: &pb.InterfaceCase{}}
	if err = qetutils.Copy(resp.Case, interfaceCase, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy interface case[%+v] to response, error: %+v", interfaceCase, err,
		)
	}

	return resp, nil
}

func (l *ModifyInterfaceCaseLogic) hasChange(req *pb.ModifyInterfaceCaseReq, ic *model.InterfaceCase) (
	types.ChangeFlag, error,
) {
	if req == nil || ic == nil {
		return types.ChangeFlag{}, errorx.Err(
			errorx.InternalError,
			"failed to check for changes in interface case because source object or target object is null",
		)
	}

	ch := types.ChangeFlag{
		Name:        ic.Name != req.GetName(),
		Description: ic.Description.String != req.GetDescription(),
	}

	nodes, edges, combos, err := l.getElements(ic.ProjectId, ic.CaseId, ic.Version)
	if err != nil {
		return ch, err
	}

	var tags []string
	if ic.Tags.Valid && ic.Tags.String != "" {
		// 由于`protojson`只能反序列化到`[]proto.Message`，而`tags`字段类型为`[]string`，Go Struct跟PB没有大的区别，因此直接使用`jsonx`暂代`protojson`
		if err = jsonx.UnmarshalFromString(ic.Tags.String, &tags); err != nil {
			return ch, errors.Wrapf(
				errorx.Err(errorx.SerializationError, err.Error()),
				"failed to unmarshal interface case tags[%s], error: %+v",
				ic.Tags.String, err,
			)
		}
	}

	var accountConfig pb.AccountConfig
	if err = protobuf.UnmarshalJSONFromString(ic.AccountConfig, &accountConfig); err != nil {
		return ch, errors.Wrapf(
			errorx.Err(errorx.SerializationError, err.Error()),
			"failed to unmarshal interface case account config[%s], error: %+v",
			ic.AccountConfig, err,
		)
	}

	// `pb.Relation`中含有二维数组，Go Struct跟PB有区别，因此这里必须使用兼容函数进行反序列化
	var relations []*pb.Relation
	err = protobuf.UnmarshalJSONWithMessagesFromString(ic.Structure, &relations)
	if err != nil {
		return ch, errors.Wrapf(
			errorx.Err(errorx.SerializationError, err.Error()),
			"failed to unmarshal interface case structure[%s], error: %+v",
			ic.Structure, err,
		)
	}

	sort.SliceStable(
		nodes, func(i, j int) bool {
			return nodes[i].Id < nodes[j].Id
		},
	)
	sort.SliceStable(
		edges, func(i, j int) bool {
			return edges[i].Id < edges[j].Id
		},
	)
	sort.SliceStable(
		combos, func(i, j int) bool {
			return combos[i].Id < combos[j].Id
		},
	)

	origin := &pb.ModifyInterfaceCaseReq{
		ProjectId:     ic.ProjectId,
		DocumentId:    ic.DocumentId,
		CaseId:        ic.CaseId,
		Name:          ic.Name,
		Description:   ic.Description.String,
		Priority:      ic.Priority,
		Tags:          tags,
		AccountConfig: &accountConfig,
		Nodes:         nodes,
		Edges:         edges,
		Combos:        combos,
		Relations:     relations,
		MaintainedBy:  ic.MaintainedBy.String,
	}

	if ns := req.GetNodes(); ns != nil {
		sort.SliceStable(
			ns, func(i, j int) bool {
				return ns[i].Id < ns[j].Id
			},
		)
	}

	if es := req.GetEdges(); es != nil {
		sort.SliceStable(
			es, func(i, j int) bool {
				return es[i].Id < es[j].Id
			},
		)
	}

	if cs := req.GetCombos(); cs != nil {
		sort.SliceStable(
			cs, func(i, j int) bool {
				return cs[i].Id < cs[j].Id
			},
		)
	}

	dmp := diffmatchpatch.New()
	ds := dmp.DiffMain(
		protobuf.MarshalJSONToStringIgnoreError(origin), protobuf.MarshalJSONToStringIgnoreError(req), false,
	)
	if len(ds) != 0 {
		for _, d := range ds {
			if d.Type != diffmatchpatch.DiffEqual {
				ch.Whole = true
				return ch, nil
			}
		}
	}

	return ch, nil
}

func (l *ModifyInterfaceCaseLogic) generateVersion(projectId, documentId, caseId string) (string, error) {
	g := qetutils.NewUniqueIdGenerator(
		qetutils.WithGenerateFunc(utils.GenVersion), qetutils.WithIsUniqueFunc(
			func(id string) bool {
				r, err := l.svcCtx.InterfaceCaseModel.FindOneByProjectIdDocumentIdCaseIdVersion(
					l.ctx, projectId, documentId, caseId, id,
				)
				if errors.Is(err, model.ErrNotFound) || r == nil {
					return true
				}
				return false
			},
		),
	)
	version := g.Next()
	if version == "" {
		return "", errors.WithStack(
			errorx.Err(
				errorx.GenerateUniqueIdFailure, "failed to generate version id, please try it later",
			),
		)
	}

	return version, nil
}

// in order to reduce cyclomatic complexity of ModifyInterfaceCaseLogic.ModifyInterfaceCase
func (l *ModifyInterfaceCaseLogic) modify(req *pb.ModifyInterfaceCaseReq) (*model.InterfaceCase, error) {
	// get the latest version of interface case again
	// 注意：这里需要重新获取最新版本的数据，因为当高并发的时候，上锁前拿到的可能已经不是最新版本的数据了
	origin, err := model.CheckInterfaceCaseByCaseId(
		l.ctx, l.svcCtx.InterfaceCaseModel, req.GetProjectId(), req.GetDocumentId(), req.GetCaseId(), "",
	)
	if err != nil {
		return nil, err
	}

	version, err := l.generateVersion(origin.ProjectId, origin.DocumentId, origin.CaseId)
	if err != nil {
		return nil, err
	}

	// 由于`protojson`只能序列化`[]proto.Message`，而`tags`字段类型为`[]string`，Go Struct跟PB没有大的区别，因此直接使用`jsonx`暂代`protojson`
	tags := jsonx.MarshalToStringIgnoreError(req.GetTags())

	var user *userpb.UserInfo
	maintainedBy := req.GetMaintainedBy()
	if maintainedBy != "" {
		if user, err = l.getUserInfoByAccount(maintainedBy); err != nil {
			return nil, err
		} else if !user.GetEnabled() {
			return nil, errorx.Errorf(
				errorx.ProhibitedBehavior,
				"cannot assign a disabled user as a maintainer of interface case, project_id: %s, document_id: %s, case_id: %s, maintained_by: %s",
				req.GetProjectId(), req.GetDocumentId(), req.GetCaseId(), maintainedBy,
			)
		}
	}

	interfaceCase := &model.InterfaceCase{
		ProjectId:  origin.ProjectId,
		DocumentId: origin.DocumentId,
		CaseId:     origin.CaseId,
		Name:       req.GetName(),
		Description: sql.NullString{
			String: req.GetDescription(),
			Valid:  req.GetDescription() != "",
		},
		Priority: req.GetPriority(),
		Tags: sql.NullString{
			String: tags,
			Valid:  tags != "",
		},
		State:         origin.State, // 状态由`fsm`进行修改
		AccountConfig: protobuf.MarshalJSONToStringIgnoreError(req.GetAccountConfig()),
		Version:       version,
		Structure:     protobuf.MarshalJSONWithMessagesToStringIgnoreError(req.GetRelations()),
		Latest:        int64(qetconstants.IsLatestVersion),
		MaintainedBy: sql.NullString{
			String: maintainedBy,
			Valid:  maintainedBy != "",
		},
		CreatedBy: origin.CreatedBy,
		UpdatedBy: l.currentUser.Account,
		CreatedAt: origin.CreatedAt,
	}

	// update interface case in a transaction
	if err = l.svcCtx.InterfaceCaseModel.Trans(
		l.ctx, func(context context.Context, session sqlx.Session) error {
			origin.Latest = int64(qetconstants.IsNotLatestVersion)

			if _, err := l.svcCtx.InterfaceCaseModel.UpdateAllToNotLatest(
				context, session, origin.ProjectId, origin.DocumentId, origin.CaseId,
			); err != nil {
				return errors.Wrapf(
					errorx.Err(errorx.DBError, err.Error()),
					"failed to update the latest field of interface case with project_id[%s], document_id[%s] and case_id[%s], error: %+v",
					origin.ProjectId, origin.DocumentId, origin.CaseId, err,
				)
			}

			if _, err := l.svcCtx.InterfaceCaseModel.InsertTX(context, session, interfaceCase); err != nil {
				return errors.Wrapf(
					errorx.Err(errorx.DBError, err.Error()), "failed to insert table[%s] with values[%+v], error: %+v",
					l.svcCtx.InterfaceCaseModel.Table(), interfaceCase, err,
				)
			}

			// create component group reference relationship
			if err := l.createComponentGroupReference(
				context, session, createComponentGroupReferenceInternalReq{
					ProjectId: interfaceCase.ProjectId,
					CaseId:    interfaceCase.CaseId,
					Version:   interfaceCase.Version,
					Relations: req.GetRelations(),
				},
			); err != nil {
				return err
			}

			// create the elements and components of the component group
			if err := l.createElementAndComponent(
				context, session, createElementAndComponentInternalReq{
					ProjectId: interfaceCase.ProjectId,
					CaseId:    interfaceCase.CaseId,
					Version:   interfaceCase.Version,
					Nodes:     req.GetNodes(),
					Edges:     req.GetEdges(),
					Combos:    req.GetCombos(),
				},
			); err != nil {
				return err
			}

			// create the new tag and tag reference of interface case
			if err := l.createTagLogic.CreateTagAndReferenceForInternal(
				context, session, types.CreateOrUpdateTagReference{
					ProjectId:        interfaceCase.ProjectId,
					ReferenceType:    common.ConstReferenceTypeInterfaceCase,
					ReferenceId:      interfaceCase.CaseId,
					ReferenceVersion: version,
					Tags:             req.GetTags(),
				},
			); err != nil {
				return err
			}

			return nil
		},
	); err != nil {
		return nil, err
	}

	// only transition when setting the maintainer fot the first time
	if origin.State == string(common.ConstResourceStateNew) &&
		(!origin.MaintainedBy.Valid || origin.MaintainedBy.String == "") && maintainedBy != "" && user != nil {
		// get the latest version of interface case again
		// 注意：这里需要重新获取最新版本的数据，因为需要获取新的`id`
		interfaceCase, err = model.CheckInterfaceCaseByCaseId(
			l.ctx, l.svcCtx.InterfaceCaseModel, req.GetProjectId(), req.GetDocumentId(), req.GetCaseId(), "",
		)
		if err != nil {
			return nil, err
		}

		event := common.ConstReviewResourceEventAssignedToTheResponsiblePerson
		if err = fsm.NewResourceFSM(
			l.ctx, l.svcCtx, &ResourceOfInterfaceCase{
				InterfaceCase: interfaceCase,
				Event:         event,
				Emails:        []string{user.GetEmail()},
			}, nil,
		).TransitionWithoutLock(); err != nil {
			return nil, errors.Wrapf(
				errorx.Err(errorx.FSMError, err.Error()),
				"got an error regarding state transition about interface case, project_id: %s, resource_id: %s, event: %s, error: %+v",
				req.GetProjectId(), req.GetCaseId(), event, err,
			)
		}
	}

	return interfaceCase, nil
}

func (l *ModifyInterfaceCaseLogic) UpdateReferenceByInterfaceCase(
	ur types.UpdateReference, ch types.ChangeFlag,
) (err error) {
	ic, err := l.svcCtx.InterfaceCaseModel.FindOneByProjectIdDocumentIdCaseIdVersion(
		l.ctx, ur.ProjectId, "", ur.ReferenceId, ur.ReferenceVersion,
	)
	if err != nil {
		if !errors.Is(err, model.ErrNotFound) {
			return errors.Wrapf(
				errorx.Err(errorx.DBError, err.Error()),
				"failed to find interface case, project_id: %s, case_id: %s, version: %s, error: %+v", ur.ProjectId,
				ur.ReferenceId, ur.ReferenceVersion, err,
			)
		} else {
			return errors.WithStack(
				errorx.Err(
					errorx.NotExists, fmt.Sprintf(
						"interface case doesn't exist, project_id: %s, case_id: %s, version: %s", ur.ProjectId,
						ur.ReferenceId, ur.ReferenceVersion,
					),
				),
			)
		}
	} else if qetconstants.LatestVersionFlag(ic.Latest) != qetconstants.IsLatestVersion {
		// 引用当前被编辑的组件组的接口用例不是最新版本，则无需更新
		return nil
	}

	// acquire redis lock
	key := fmt.Sprintf(
		"%s:%s:%s:%s", common.ConstLockInterfaceCaseProjectIdDocumentIdCaseIdPrefix, ur.ProjectId, ic.DocumentId,
		ur.ReferenceId,
	)
	lock, err := redislock.NewRedisLockAndAcquire(
		l.svcCtx.Redis, key, redislock.WithExpire(common.ConstLockExpireTime),
		redislock.WithTimeout(common.ConstAcquireLockTimeout),
	)
	if err != nil {
		return err
	}
	defer func() {
		if err != nil {
			l.Logger.Warnf(
				"error occurred while updating the reference, project_id: %s, reference_type: %s, reference_id: %s, reference_version: %s, component_group_id: %s, error: \n%+v",
				ur.ProjectId, ur.ReferenceType, ur.ReferenceId, ur.ReferenceVersion, ur.ComponentGroupId, err,
			)
		}
		// release redis lock
		e := lock.Release()
		if e != nil {
			l.Logger.Warn(e)
			if re, ok := errorx.RootError(e); ok && re.Code() != errorx.ReleaseRedisLockFailure && err == nil {
				err = e
			}
		}
	}()

	nodes, _, _, err := l.getElements(ur.ProjectId, ur.ReferenceId, ur.ReferenceVersion)
	if err != nil {
		return err
	}

	for _, node := range nodes {
		m := node.ComponentDataMapValue()

		if m == nil || node.ComponentType() != constants.REFERENCE {
			continue
		}

		v, ok := m[common.ConstFieldJSONNameReferenceId]
		if !ok || ur.ComponentGroupId != v {
			continue
		}

		ice, err := l.svcCtx.InterfaceCaseElementModel.FindOneByProjectIdCaseIdVersionElementId(
			l.ctx, ur.ProjectId, ur.ReferenceId, ur.ReferenceVersion, node.GetId(),
		)
		if err != nil {
			l.Logger.Warnf(
				"failed to find the interface case element, project_id: %s, case_id: %s, version: %s, element_id: %s, error: %+v",
				ur.ProjectId, ur.ReferenceId, ur.ReferenceVersion, node.GetId(), err,
			)
			continue
		}

		if ch.Name {
			m[common.ConstFieldJSONNameName] = ur.Name
			m[common.ConstFieldJSONNameLabel] = ur.Name
			node.Label = ur.Name
		}
		if ch.Description {
			m[common.ConstFieldJSONNameDescription] = ur.Description
		}
		if ch.Imports {
			logic.UpdateReferenceImports(ur.Imports, m)
		}
		if ch.Exports {
			logic.UpdateReferenceExports(ur.Exports, m)
		}

		node.Data, err = protobuf.NewStruct(m)
		if err != nil {
			l.Logger.Warnf(
				"failed to new a *structpb.Struct with the data of interface case element, project_id: %s, case_id: %s, version: %s, element_id: %s, error: %+v",
				ur.ProjectId, ur.ReferenceId, ur.ReferenceVersion, node.GetId(), err,
			)
			continue
		}

		ice.Data = protobuf.MarshalJSONToStringIgnoreError(node)
		if _, err = l.svcCtx.InterfaceCaseElementModel.UpdateTX(l.ctx, nil, ice); err != nil {
			l.Logger.Warnf(
				"failed to update the interface case element, project_id: %s, case_id: %s, version: %s, element_id: %s, error: %+v",
				ice.ProjectId, ice.CaseId, ice.Version, ice.ElementId, err,
			)
			continue
		}
	}

	return nil
}
