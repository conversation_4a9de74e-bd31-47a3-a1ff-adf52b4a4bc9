package interfacedefinitionservicelogic

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type ViewInterfaceDocumentLogic struct {
	*BaseLogic
}

func NewViewInterfaceDocumentLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ViewInterfaceDocumentLogic {
	return &ViewInterfaceDocumentLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// ViewInterfaceDocument 查看接口文档
func (l *ViewInterfaceDocumentLogic) ViewInterfaceDocument(in *pb.ViewInterfaceDocumentReq) (resp *pb.ViewInterfaceDocumentResp, err error) {
	// validate the project_id in req
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, in.GetProjectId()); err != nil {
		return nil, err
	}

	document, err := model.CheckInterfaceDocumentByDocumentId(l.ctx, l.svcCtx.InterfaceDocumentModel, in.GetProjectId(), in.GetDocumentId())
	if err != nil {
		return nil, err
	}

	resp = &pb.ViewInterfaceDocumentResp{Document: &pb.InterfaceDocument{}}
	if err = utils.Copy(resp.Document, document, l.converters...); err != nil {
		return nil, errors.Wrapf(errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy interface document[%+v] to response, error: %+v", document, err)
	}

	if err = l.setCategoryPathToDocument(in.GetProjectId(), resp.Document.Data); err != nil {
		return nil, err
	}

	return resp, nil
}
