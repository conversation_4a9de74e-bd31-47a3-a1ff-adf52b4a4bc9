package interfacedefinitionservicelogic

import (
	"context"
	"database/sql"
	"fmt"
	"time"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/redislock"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type ModifyInterfaceConfigLogic struct {
	*BaseLogic
}

func NewModifyInterfaceConfigLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ModifyInterfaceConfigLogic {
	return &ModifyInterfaceConfigLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// ModifyInterfaceConfig 编辑接口配置
func (l *ModifyInterfaceConfigLogic) ModifyInterfaceConfig(in *pb.ModifyInterfaceConfigReq) (resp *pb.ModifyInterfaceConfigResp, err error) {
	// validate the project_id in req
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, in.GetProjectId()); err != nil {
		return nil, err
	}

	// validate the document_id in req
	if _, err = model.CheckInterfaceDocumentByDocumentId(l.ctx, l.svcCtx.InterfaceDocumentModel, in.GetProjectId(), in.GetDocumentId()); err != nil {
		return nil, err
	}

	// validate the config_id in req
	origin, err := model.CheckInterfaceConfigByConfigId(l.ctx, l.svcCtx.InterfaceConfigurationModel, in.GetProjectId(), in.GetDocumentId(), in.GetConfigId())
	if err != nil {
		return nil, err
	}

	// acquire redis lock
	key := fmt.Sprintf("%s:%s:%s:%s", common.ConstLockInterfaceConfigProjectIdDocumentIdConfigIdPrefix, in.GetProjectId(), in.GetDocumentId(), in.GetConfigId())
	lock, err := redislock.NewRedisLockAndAcquire(l.svcCtx.Redis, key, redislock.WithExpire(common.ConstLockExpireTime))
	if err != nil {
		return nil, err
	}
	defer func() {
		// release redis lock
		e := lock.Release()
		if e != nil {
			l.Logger.Error(e)
			if re, ok := errorx.RootError(e); ok && re.Code() != errorx.ReleaseRedisLockFailure && err == nil {
				err = e
			}
		}
	}()

	origin.Name = in.GetName()
	origin.Description = sql.NullString{
		String: in.GetDescription(),
		Valid:  in.GetDescription() != "",
	}
	origin.Path = in.GetPath()
	origin.Method = in.GetMethod()

	if in.Data == nil {
		in.Data = &pb.Document{}
	}
	origin.Data = protobuf.MarshalJSONToStringIgnoreError(in.GetData())

	if in.InputParameters == nil {
		in.SetInputParameters(make([]*pb.InputParameter, 0))
	}
	origin.InputParameters = protobuf.MarshalJSONWithMessagesToStringIgnoreError(in.InputParameters)

	if in.OutputParameters == nil {
		in.SetOutputParameters(make([]*pb.OutputParameter, 0))
	}
	origin.OutputParameters = protobuf.MarshalJSONWithMessagesToStringIgnoreError(in.OutputParameters)

	origin.UpdatedBy = l.currentUser.Account
	origin.UpdatedAt = time.Now()

	if _, err = l.svcCtx.InterfaceConfigurationModel.Update(l.ctx, nil, origin); err != nil {
		return nil, errors.Wrapf(errorx.Err(errorx.DBError, err.Error()), "failed to update table[%s] with values[%+v], error: %+v", l.svcCtx.InterfaceConfigurationModel.Table(), origin, err)
	}

	resp = &pb.ModifyInterfaceConfigResp{Config: &pb.InterfaceConfig{}}
	if err = utils.Copy(resp.Config, origin, l.converters...); err != nil {
		return nil, errors.Wrapf(errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy interface config[%+v] to response, error: %+v", origin, err)
	}

	return resp, nil
}
