package interfacedefinitionservicelogic

import (
	"fmt"
	"net/http"
	"net/url"
	"strings"
	"sync"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/logx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

var schemaPool = sync.Pool{New: func() any { return newSchema() }}

func getNameFromGitURL(gitURL string) (string, error) {
	u, err := url.Parse(gitURL)
	if err != nil {
		return "", err
	}

	path := u.Path

	// 移除开头的斜杠
	path = strings.TrimLeft(path, constSlash)

	// 移除`.git`扩展名
	path = strings.TrimSuffix(path, constDotGit)

	// 分割路径，获取最后一个部分，即目录名称
	parts := strings.Split(path, constSlash)
	name := parts[len(parts)-1]

	return name, nil
}

func newSchema() *pb.Schema {
	return &pb.Schema{}
}

func findServiceCategory(serviceName string, categories []*model.Category) *model.Category {
	for _, c := range categories {
		if serviceName == c.Name {
			return c
		}
	}

	return nil
}

func marshalTDSDocument(document types.DocumentRecorder) string {
	var (
		body, resp200, resp500, media *pb.Schema
		err                           error
	)

	defer func() {
		if err != nil {
			logx.Error(err)
		}
	}()

	body, err = NewSchemaByName(common.ConstTDSCommonCallReqSchemaName)
	if err != nil {
		return ""
	}

	resp200, err = NewSchemaByName(common.ConstTDSCommonRespSchemaName)
	if err != nil {
		return ""
	}

	resp500, err = NewSchemaByName(common.ConstTDSErrorRespSchemaName)
	if err != nil {
		return ""
	}

	media, err = NewSchemaByName(ConstMediaTypeApplicationJsonName)
	if err != nil {
		return ""
	}

	defer func() {
		ReleaseSchema(body)
		ReleaseSchema(resp200)
		ReleaseSchema(resp500)
		ReleaseSchema(media)
	}()

	body.Properties[ConstTDSCommonCallFieldApiName].Default = logic.ConvertAnyToValue(document.ApiName)
	if document.Cmd != 0 {
		body.Properties[ConstTDSCommonCallFieldFixDict].Default = logic.ConvertAnyToValue(
			map[string]any{
				ConstTDSCommonCallFieldCmd: document.Cmd,
			},
		)
	}
	if document.ReqSchema != nil {
		body.Properties[ConstTDSCommonCallFieldProtoJson].Type = common.ConstInterfaceDefinitionFieldTypeSchema
		body.Properties[ConstTDSCommonCallFieldProtoJson].Ref = &pb.RefSchema{
			SchemaId:    document.ReqSchema.SchemaId,
			FullName:    document.ReqSchema.FullName,
			DisplayName: document.ReqSchema.Name,
		}
	}
	if document.RespSchema != nil {
		resp200.Properties[ConstTDSCommonCallFieldData].Type = common.ConstInterfaceDefinitionFieldTypeSchema
		resp200.Properties[ConstTDSCommonCallFieldData].Ref = &pb.RefSchema{
			SchemaId:    document.RespSchema.SchemaId,
			FullName:    document.RespSchema.FullName,
			DisplayName: document.RespSchema.Name,
		}
	}

	d := &pb.Document{
		Headers: types.ArraySchema{media},
		Body: &pb.BodyData{
			Type: common.ConstMediaTypeApplicationJson,
			Json: body,
		},
		Responses: types.StatusResponse{
			fmt.Sprintf("%d", http.StatusOK): &pb.ResponseData{
				StatusCode:  fmt.Sprintf("%d", http.StatusOK),
				Description: http.StatusText(http.StatusOK),
				Headers:     types.ArraySchema{media},
				Body: &pb.BodyData{
					Type: common.ConstMediaTypeApplicationJson,
					Json: resp200,
				},
			},
			fmt.Sprintf("%d", http.StatusInternalServerError): &pb.ResponseData{
				StatusCode:  fmt.Sprintf("%d", http.StatusInternalServerError),
				Description: http.StatusText(http.StatusInternalServerError),
				Headers:     types.ArraySchema{media},
				Body: &pb.BodyData{
					Type: common.ConstMediaTypeApplicationJson,
					Json: resp500,
				},
			},
		},
	}

	return protobuf.MarshalJSONToStringIgnoreError(d)
}

func marshalTTMetaClientDocument(document types.DocumentRecorder) string {
	var (
		body, resp200, media *pb.Schema
		err                  error
	)

	defer func() {
		if err != nil {
			logx.Error(err)
		}
	}()

	body, err = NewSchemaByName(common.ConstTTMetaClientCommonCallReqSchemaName)
	if err != nil {
		return ""
	}

	resp200, err = NewSchemaByName(common.ConstTTMetaClientCommonRespSchemaName)
	if err != nil {
		return ""
	}

	//resp500, err = NewSchemaByName(common.ConstTTMetaClientErrorRespSchemaName)
	//if err != nil {
	//	return ""
	//}

	media, err = NewSchemaByName(ConstMediaTypeApplicationJsonName)
	if err != nil {
		return ""
	}

	defer func() {
		ReleaseSchema(body)
		ReleaseSchema(resp200)
		// ReleaseSchema(resp500)
		ReleaseSchema(media)
	}()

	body.Properties[ConstTTMetaCommonCallFieldApiName].Default = logic.ConvertAnyToValue(document.ApiName)
	body.Properties[ConstTTMetaCommonCallFieldServerName].Default = logic.ConvertAnyToValue(document.Service)
	//if document.Cmd != 0 {
	//	body.Properties[ConstTDSCommonCallFieldFixDict].Default = logic.ConvertAnyToValue(map[string]any{
	//		ConstTDSCommonCallFieldCmd: document.Cmd,
	//	})
	//}
	if document.ReqSchema != nil {
		body.Properties[ConstTTMetaCommonCallFieldProtoJson].Type = common.ConstInterfaceDefinitionFieldTypeSchema
		body.Properties[ConstTTMetaCommonCallFieldProtoJson].Ref = &pb.RefSchema{
			SchemaId:    document.ReqSchema.SchemaId,
			FullName:    document.ReqSchema.FullName,
			DisplayName: document.ReqSchema.Name,
		}
	}
	if document.RespSchema != nil {
		resp200.Properties[ConstTTMetaCommonCallFieldData].Type = common.ConstInterfaceDefinitionFieldTypeSchema
		resp200.Properties[ConstTTMetaCommonCallFieldData].Ref = &pb.RefSchema{
			SchemaId:    document.RespSchema.SchemaId,
			FullName:    document.RespSchema.FullName,
			DisplayName: document.RespSchema.Name,
		}
	}

	d := &pb.Document{
		Headers: types.ArraySchema{media},
		Body: &pb.BodyData{
			Type: common.ConstMediaTypeApplicationJson,
			Json: body,
		},
		Responses: types.StatusResponse{
			fmt.Sprintf("%d", http.StatusOK): &pb.ResponseData{
				StatusCode:  fmt.Sprintf("%d", http.StatusOK),
				Description: http.StatusText(http.StatusOK),
				Headers:     types.ArraySchema{media},
				Body: &pb.BodyData{
					Type: common.ConstMediaTypeApplicationJson,
					Json: resp200,
				},
			},
			//fmt.Sprintf("%d", http.StatusInternalServerError): &pb.ResponseData{
			//	StatusCode:  fmt.Sprintf("%d", http.StatusInternalServerError),
			//	Description: http.StatusText(http.StatusInternalServerError),
			//	Headers:     types.ArraySchema{media},
			//	Body: &pb.BodyData{
			//		Type: common.ConstMediaTypeApplicationJson,
			//		Json: resp500,
			//	},
			//},
		},
	}

	return protobuf.MarshalJSONToStringIgnoreError(d)
}

func marshalApiProxyDocument(document types.DocumentRecorder) string {
	var (
		body, media, resp200, resp200Data, resp200DataCallResp *pb.Schema
		err                                                    error
	)

	defer func() {
		if err != nil {
			logx.Error(err)
		}
	}()

	body, err = NewSchemaByName(common.ConstApiProxyCommonApiCallReqSchemaName)
	if err != nil {
		return ""
	}

	resp200, err = NewSchemaByName(common.ConstApiProxyCommonRespSchemaName)
	if err != nil {
		return ""
	}
	resp200Data, err = NewSchemaByName(common.ConstApiProxyCommonRespDataSchemaName)
	if err != nil {
		return ""
	}
	resp200DataCallResp, err = NewSchemaByName(common.ConstApiProxyCommonRespDataCallRespSchemaName)
	if err != nil {
		return ""
	}

	resp200.Properties[ConstApiProxyCommonApiCallFieldData] = resp200Data
	resp200Data.Properties[ConstApiProxyCommonApiCallFieldCallResp] = resp200DataCallResp

	//resp500, err = NewSchemaByName(common.ConstTDSErrorRespSchemaName)
	//if err != nil {
	//	return ""
	//}

	media, err = NewSchemaByName(ConstMediaTypeApplicationJsonName)
	if err != nil {
		return ""
	}

	defer func() {
		ReleaseSchema(body)
		ReleaseSchema(resp200)
		// ReleaseSchema(resp500)
		ReleaseSchema(media)
		ReleaseSchema(resp200Data)
		ReleaseSchema(resp200DataCallResp)
	}()

	body.Properties[ConstApiProxyCommonApiCallFieldMethod].Default = logic.ConvertAnyToValue(document.FullName)

	if document.ReqSchema != nil {
		body.Properties[ConstApiProxyCommonApiCallFieldBody].Type = common.ConstInterfaceDefinitionFieldTypeSchema
		body.Properties[ConstApiProxyCommonApiCallFieldBody].Ref = &pb.RefSchema{
			SchemaId:    document.ReqSchema.SchemaId,
			FullName:    document.ReqSchema.FullName,
			DisplayName: document.ReqSchema.Name,
		}
	}
	if document.RespSchema != nil {
		resp200DataCallResp.Properties[ConstApiProxyCommonApiCallFieldCallRespBody].Type = common.ConstInterfaceDefinitionFieldTypeSchema
		resp200DataCallResp.Properties[ConstApiProxyCommonApiCallFieldCallRespBody].Ref = &pb.RefSchema{
			SchemaId:    document.RespSchema.SchemaId,
			FullName:    document.RespSchema.FullName,
			DisplayName: document.RespSchema.Name,
		}
	}

	d := &pb.Document{
		Headers: types.ArraySchema{media},
		Body: &pb.BodyData{
			Type: common.ConstMediaTypeApplicationJson,
			Json: body,
		},
		Responses: types.StatusResponse{
			fmt.Sprintf("%d", http.StatusOK): &pb.ResponseData{
				StatusCode:  fmt.Sprintf("%d", http.StatusOK),
				Description: http.StatusText(http.StatusOK),
				Headers:     types.ArraySchema{media},
				Body: &pb.BodyData{
					Type: common.ConstMediaTypeApplicationJson,
					Json: resp200,
				},
			},
			//fmt.Sprintf("%d", http.StatusInternalServerError): &pb.ResponseData{
			//	StatusCode:  fmt.Sprintf("%d", http.StatusInternalServerError),
			//	Description: http.StatusText(http.StatusInternalServerError),
			//	Headers:     types.ArraySchema{media},
			//	Body: &pb.BodyData{
			//		Type: common.ConstMediaTypeApplicationJson,
			//		Json: resp500,
			//	},
			//},
		},
	}

	return protobuf.MarshalJSONToStringIgnoreError(d)
}

func findReferenceInDocument(document *pb.Document) (out []string) {
	if document.Body != nil && document.Body.Type == common.ConstMediaTypeApplicationJson && document.Body.Json != nil {
		out = append(out, findReferenceInSchema(document.Body.Json)...)
	}

	for _, rd := range document.Responses {
		if rd.Body != nil && rd.Body.Type == common.ConstMediaTypeApplicationJson && rd.Body.Json != nil {
			out = append(out, findReferenceInSchema(rd.Body.Json)...)
		}
	}

	return
}

func findReferenceInSchema(schema *pb.Schema) (out []string) {
	switch schema.Type {
	case common.ConstInterfaceDefinitionFieldTypeSchema:
		if schema.Ref != nil {
			out = append(out, schema.Ref.SchemaId)
		}
	case common.ConstInterfaceDefinitionFieldTypeArray:
		if schema.Items != nil {
			out = append(out, findReferenceInSchema(schema.Items)...)
		}
	case common.ConstInterfaceDefinitionFieldTypeObject, common.ConstInterfaceDefinitionFieldTypeAllOf,
		common.ConstInterfaceDefinitionFieldTypeAnyOf, common.ConstInterfaceDefinitionFieldTypeOneOf:
		for _, s := range schema.Properties {
			if s == nil {
				continue
			}
			out = append(out, findReferenceInSchema(s)...)
		}
	}

	return
}

func findReferenceInFieldSchema(fieldSchema types.FieldSchema) (out []string) {
	for _, schema := range fieldSchema {
		out = append(out, findReferenceInSchema(schema)...)
	}

	return
}

func NewSchemaByName(name string) (*pb.Schema, error) {
	var from *pb.Schema

	switch name {
	case common.ConstApiProxyCommonApiCallReqSchemaName:
		from = apiProxyCommonApiCallReqSchema
	case common.ConstApiProxyCommonRespSchemaName:
		from = apiProxyRespSchema
	case common.ConstApiProxyCommonRespDataSchemaName:
		from = apiProxyCommonRespDataSchema
	case common.ConstApiProxyCommonRespDataCallRespSchemaName:
		from = apiProxyCommonRespDataCallRespSchema
	case common.ConstTDSCommonCallReqSchemaName:
		from = tdsCommonCallReqSchema
	case common.ConstTDSSearchClientSchemaName:
		from = tdsSearchClientSchema
	case common.ConstTDSFixDictSchemaName:
		from = tdsFixDictSchema
	case common.ConstTDSCommonRespSchemaName:
		from = tdsCommonRespSchema
	case common.ConstTDSErrorRespSchemaName:
		from = tdsErrorRespSchema
	case ConstMediaTypeApplicationJsonName:
		from = mediaTypeApplicationJsonSchema
	case common.ConstTTMetaClientCommonCallReqSchemaName:
		from = ttMetaCommonCallReqSchema
	case common.ConstTTMetaClientSearchClientSchemaName:
		from = ttMetaSearchClientSchema
	case common.ConstTTMetaClientCommonRespSchemaName:
		from = ttMetaCommonRespSchema

	default:
		return nil, errorx.Errorf(errorx.DoesNotSupport, "the schema name[%s] is not currently supported", name)
	}

	to := schemaPool.Get()
	if err := utils.CleanUp(to); err != nil {
		return nil, err
	}
	if err := utils.Copy(to, from); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to deep copy the schema[%s], error: %+v",
			name, err,
		)
	}

	return to.(*pb.Schema), nil
}

func ReleaseSchema(s *pb.Schema) {
	schemaPool.Put(s)
}

func convertStatistic(s *common.Statistic) *pb.Statistic {
	return &pb.Statistic{
		Total:     s.Total.Load(),
		Increased: s.Increased.Load(),
		Modified:  s.Modified.Load(),
		Unchanged: s.Unchanged.Load(),
		Skipped:   s.Skipped.Load(),
		Failure:   s.Failure.Load(),
	}
}

func getMethodFromDocument(document *model.InterfaceDocument) (string, error) {
	var item pb.Document
	if err := protobuf.UnmarshalJSONFromString(document.Data, &item); err != nil {
		return "", errorx.Errorf(
			errorx.SerializationError,
			"failed to unmarshal the document data, document_id: %s, name: %s, error: %+v",
			document.DocumentId, document.Name, err,
		)
	}

	properties := item.GetBody().GetJson().GetProperties()
	if properties == nil {
		return "", errorx.Errorf(
			errorx.ValidateParamError,
			"the document has no properties, document_id: %s, name: %s",
			document.DocumentId, document.Name,
		)
	}

	schema, ok := properties[ConstApiProxyCommonApiCallFieldMethod]
	if !ok {
		return "", errorx.Errorf(
			errorx.ValidateParamError,
			"the properties of document has no method field, document_id: %s, name: %s",
			document.DocumentId, document.Name,
		)
	}

	method := schema.GetDefault().GetStringValue()
	if len(method) == 0 {
		return "", errorx.Errorf(
			errorx.ValidateParamError,
			"the method of document is empty, document_id: %s, name: %s",
			document.DocumentId, document.Name,
		)
	}

	return method, nil
}
