package interfacedefinitionservicelogic

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type ViewInterfaceSchemaLogic struct {
	*BaseLogic
}

func NewViewInterfaceSchemaLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ViewInterfaceSchemaLogic {
	return &ViewInterfaceSchemaLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// ViewInterfaceSchema 查看接口数据模型
func (l *ViewInterfaceSchemaLogic) ViewInterfaceSchema(in *pb.ViewInterfaceSchemaReq) (
	resp *pb.ViewInterfaceSchemaResp, err error,
) {
	// validate the project_id in req
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, in.GetProjectId()); err != nil {
		return nil, err
	}

	schema, err := model.CheckInterfaceSchemaBySchemaId(
		l.ctx, l.svcCtx.InterfaceSchemaModel, in.GetProjectId(), in.GetSchemaId(),
	)
	if err != nil {
		return nil, err
	}

	resp = &pb.ViewInterfaceSchemaResp{Schema: &pb.InterfaceSchema{}}
	if err = utils.Copy(resp.Schema, schema, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy interface schema[%+v] to response, error: %+v",
			schema, err,
		)
	}

	// 对引用模型设置分类树路径
	if err = l.setCategoryPathToSchema(in.GetProjectId(), resp.Schema.Data); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.InternalError, err.Error()), "failed to set the category path to schema[%s], error: %+v",
			schema.Name, err,
		)
	}

	return resp, nil
}
