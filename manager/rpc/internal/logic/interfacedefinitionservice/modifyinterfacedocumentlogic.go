package interfacedefinitionservicelogic

import (
	"context"
	"database/sql"
	"fmt"
	"time"

	"github.com/emirpasic/gods/sets/hashset"
	"github.com/pkg/errors"
	"github.com/r3labs/diff/v3"
	"github.com/zeromicro/go-zero/core/jsonx"
	"github.com/zeromicro/go-zero/core/mr"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/redislock"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/codes"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type ModifyInterfaceDocumentLogic struct {
	*BaseLogic
}

func NewModifyInterfaceDocumentLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ModifyInterfaceDocumentLogic {
	return &ModifyInterfaceDocumentLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// ModifyInterfaceDocument 编辑接口文档
func (l *ModifyInterfaceDocumentLogic) ModifyInterfaceDocument(in *pb.ModifyInterfaceDocumentReq) (
	resp *pb.ModifyInterfaceDocumentResp, err error,
) {
	// validate the project_id in req
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, in.GetProjectId()); err != nil {
		return nil, err
	}

	var c *model.Category
	// validate the category_id in req
	if c, err = model.CheckCategoryByCategoryId(
		l.ctx, l.svcCtx.CategoryModel, in.GetProjectId(), common.ConstCategoryTreeTypeInterfaceDocument,
		in.GetCategoryId(),
	); err != nil {
		return nil, err
	} else if c.CategoryType != common.ConstCategoryTypeDirectory {
		return nil, errorx.Errorf(
			errorx.ProhibitedBehavior,
			"the type of parent category[%s] does not support creation of sub category",
			c.CategoryType,
		)
	}

	// validate the document_id in req
	origin, err := model.CheckInterfaceDocumentByDocumentId(
		l.ctx, l.svcCtx.InterfaceDocumentModel, in.GetProjectId(), in.GetDocumentId(),
	)
	if err != nil {
		return nil, err
	}

	// cannot modify builtin document
	if origin.Mode == common.ConstInterfaceDefinitionCreateModeBuiltin {
		return nil, errorx.Errorf(
			errorx.ProhibitedBehavior, "cannot modify the builtin interface document[%s]", origin.Name,
		)
	}

	var change bool
	if change, err = l.hasChange(in, *origin); err != nil {
		return nil, err
	} else if !change {
		l.Logger.Infof(
			"there is no change with interface document, project_id: %s, document_id: %s",
			in.GetProjectId(), in.GetDocumentId(),
		)

		resp = &pb.ModifyInterfaceDocumentResp{Document: &pb.InterfaceDocument{}}
		if err = utils.Copy(resp.Document, origin, l.converters...); err != nil {
			return nil, errors.Wrapf(
				errorx.Err(errorx.CopyToStructFailure, err.Error()),
				"failed to copy interface document[%+v] to response, error: %+v",
				origin, err,
			)
		}
		return resp, nil
	}

	// cannot modify document name to the builtin names
	if origin.Name != in.GetName() && stringx.Contains(common.InterfaceDocumentBuiltinCategoryNames, in.GetName()) {
		return nil, errorx.Errorf(
			errorx.ProhibitedBehavior,
			"the name[%s] conflicts with the builtin name of interface document category tree and is not allowed to be used",
			in.GetName(),
		)
	}

	// acquire redis lock
	key := fmt.Sprintf(
		"%s:%s:%s", common.ConstLockInterfaceDocumentProjectIdDocumentIdPrefix, in.GetProjectId(), in.GetDocumentId(),
	)
	lock, err := redislock.NewRedisLockAndAcquire(l.svcCtx.Redis, key, redislock.WithExpire(common.ConstLockExpireTime))
	if err != nil {
		return nil, err
	}
	defer func() {
		// release redis lock
		e := lock.Release()
		if e != nil {
			l.Logger.Error(e)
			if re, ok := errorx.RootError(e); ok && re.Code() != errorx.ReleaseRedisLockFailure && err == nil {
				err = e
			}
		}
	}()

	interfaceDocument, err := l.modify(in, origin)
	if err != nil {
		return nil, err
	}

	resp = &pb.ModifyInterfaceDocumentResp{Document: &pb.InterfaceDocument{}}
	if err = utils.Copy(resp.Document, interfaceDocument, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy interface document[%+v] to response, error: %+v",
			interfaceDocument, err,
		)
	}

	return resp, nil
}

func (l *ModifyInterfaceDocumentLogic) hasChange(req *pb.ModifyInterfaceDocumentReq, id model.InterfaceDocument) (
	bool, error,
) {
	var tags []string
	if id.Tags.Valid && id.Tags.String != "" {
		// 由于`protojson`只能反序列化到`[]proto.Message`，而`tags`字段类型为`[]string`，Go Struct跟PB没有大的区别，因此直接使用`jsonx`暂代`protojson`
		if err := jsonx.UnmarshalFromString(id.Tags.String, &tags); err != nil {
			return false, errors.Wrapf(
				errorx.Err(errorx.SerializationError, err.Error()),
				"failed to unmarshal interface document tags[%s], error: %+v",
				id.Tags.String, err,
			)
		}
	}

	var data pb.Document
	if err := protobuf.UnmarshalJSONFromString(id.Data, &data); err != nil {
		return false, errors.Wrapf(
			errorx.Err(errorx.SerializationError, err.Error()),
			"failed to unmarshal interface document data[%s], error: %+v",
			id.Data, err,
		)
	}

	origin := &pb.ModifyInterfaceDocumentReq{
		ProjectId:    id.ProjectId,
		CategoryId:   id.CategoryId,
		DocumentId:   id.DocumentId,
		Name:         id.Name,
		Description:  id.Description.String,
		Status:       id.Status,
		Priority:     id.Priority,
		Tags:         tags,
		State:        pb.CommonState(id.State),
		Service:      id.Service.String,
		Path:         id.Path,
		Method:       id.Method,
		Data:         &data,
		MaintainedBy: id.MaintainedBy.String,
	}

	differ, err := diff.NewDiffer()
	if err != nil {
		return false, errors.Wrapf(
			errorx.Err(errorx.NewObjectFailure, err.Error()), "failed to new a differ object, error: %+v", err,
		)
	}

	if cl, err := differ.Diff(origin, req); err != nil {
		return false, errors.Wrapf(
			errorx.Err(codes.StructDiffFailure, err.Error()),
			"failed to diff interface document[%s], error: %+v",
			id.Name, err,
		)
	} else if len(cl) != 0 {
		return true, nil
	}

	return false, nil
}

// in order to reduce cyclomatic complexity of ModifyInterfaceDocumentLogic.ModifyInterfaceDocument
func (l *ModifyInterfaceDocumentLogic) modify(
	req *pb.ModifyInterfaceDocumentReq, origin *model.InterfaceDocument,
) (*model.InterfaceDocument, error) {
	// 由于`protojson`只能序列化`[]proto.Message`，而`tags`字段类型为`[]string`，Go Struct跟PB没有大的区别，因此直接使用`jsonx`暂代`protojson`
	tags := jsonx.MarshalToStringIgnoreError(req.GetTags())

	document := &model.InterfaceDocument{
		Id:         origin.Id,
		ProjectId:  origin.ProjectId,
		CategoryId: req.GetCategoryId(),
		DocumentId: origin.DocumentId,
		Name:       req.GetName(),
		Description: sql.NullString{
			String: req.GetDescription(),
			Valid:  req.GetDescription() != "",
		},
		Type:       origin.Type,
		Mode:       origin.Mode,
		ImportType: origin.ImportType,
		Status:     req.GetStatus(),
		Priority:   req.GetPriority(),
		Tags: sql.NullString{
			String: tags,
			Valid:  tags != "",
		},
		State:             int64(req.GetState()),
		CaseExecutionMode: origin.CaseExecutionMode,
		Service: sql.NullString{
			String: req.GetService(),
			Valid:  req.GetService() != "",
		},
		Path:   req.GetPath(),
		Method: req.GetMethod(),
		Data:   jsonx.MarshalToStringIgnoreError(req.GetData()),
		MaintainedBy: sql.NullString{
			String: req.GetMaintainedBy(),
			Valid:  req.GetMaintainedBy() != "",
		},
		CreatedBy: origin.CreatedBy,
		UpdatedBy: l.currentUser.Account,
		CreatedAt: origin.CreatedAt,
	}

	// update interface document in a transaction
	if err := l.svcCtx.InterfaceDocumentModel.Trans(
		l.ctx, func(context context.Context, session sqlx.Session) error {
			if _, err := l.svcCtx.InterfaceDocumentModel.UpdateTX(context, session, document); err != nil {
				return errors.Wrapf(
					errorx.Err(errorx.DBError, err.Error()),
					"failed to update table[%s] with values[%+v], error: %+v",
					l.svcCtx.InterfaceDocumentModel.Table(), document, err,
				)
			}

			documentCategory, err := l.svcCtx.CategoryModel.FindOneByNodeId(
				context, origin.ProjectId, common.ConstCategoryTreeTypeInterfaceDocument, origin.DocumentId,
			)
			if err != nil {
				return errors.Wrapf(
					errorx.Err(errorx.DBError, err.Error()),
					"failed to find category with project_id[%s], type[%s] and document_id[%s], error: %+v",
					origin.ProjectId, common.ConstCategoryTreeTypeInterfaceDocument, origin.DocumentId, err,
				)
			}

			if document.Name != origin.Name || document.Description != origin.Description {
				// modify the interface document category
				if _, err = l.modifyCategoryLogic.ModifyCategoryForInternal(
					context, session, &pb.ModifyCategoryReq{
						ProjectId:   req.GetProjectId(),
						Type:        common.ConstCategoryTreeTypeInterfaceDocument,
						CategoryId:  documentCategory.CategoryId,
						Name:        document.Name,
						Description: document.Description.String,
					},
				); err != nil {
					return err
				}
			}

			if origin.CategoryId != req.GetCategoryId() {
				// move the component group category from c.CategoryId to req.CategoryId
				if err = l.moveCategoryLogic.MoveCategoryTreeForInternal(
					context, session, &pb.MoveCategoryTreeReq{
						ProjectId:     req.GetProjectId(),
						Type:          common.ConstCategoryTreeTypeInterfaceDocument,
						SourceId:      documentCategory.CategoryId,
						TargetId:      req.GetCategoryId(),
						BeingModified: true,
					},
				); err != nil {
					return err
				}
			}

			rrs, err := l.svcCtx.InterfaceSchemaReferenceModel.FindReferenceByReference(
				context, req.GetProjectId(), common.ConstInterfaceDefinitionTypeDocument, req.GetDocumentId(),
			)
			if err != nil && !errors.Is(err, model.ErrNotFound) {
				return errors.Wrapf(
					errorx.Err(errorx.DBError, err.Error()),
					"failed to find the reference relationship with project_id[%s] and document_id[%s], error: %+v",
					req.GetProjectId(), req.GetDocumentId(), err,
				)
			}

			if err = l.updateReference(context, session, req, rrs); err != nil {
				return err
			}

			// create the new tag and tag reference of interface document
			if err := l.createTagLogic.CreateTagAndReferenceForInternal(
				context, session, types.CreateOrUpdateTagReference{
					ProjectId:     document.ProjectId,
					ReferenceType: common.ConstReferenceTypeInterfaceDocument,
					ReferenceId:   document.DocumentId,
					Tags:          req.GetTags(),
				},
			); err != nil {
				return err
			}

			return nil
		},
	); err != nil {
		return nil, err
	}

	return document, nil
}

func (l *ModifyInterfaceDocumentLogic) updateReference(
	context context.Context, session sqlx.Session, req *pb.ModifyInterfaceDocumentReq,
	rrs []*model.InterfaceSchemaReferenceRelationship,
) error {
	if context == nil {
		context = l.ctx
	}

	m := make(map[string]*model.InterfaceSchemaReferenceRelationship, len(rrs))

	oSet := hashset.New()
	for _, rr := range rrs {
		oSet.Add(rr.SchemaId)
		m[rr.SchemaId] = rr
	}

	cSet := hashset.New()
	for _, s := range findReferenceInDocument(req.GetData()) {
		cSet.Add(s)
	}

	// 待删除：在`oSet`中而不在`cSet`中的元素
	dSet := oSet.Difference(cSet)
	// 待新增：在`cSet`中而不在`oSet`中的元素
	iSet := cSet.Difference(oSet)

	// 删除引用关系
	if err := mr.MapReduceVoid[*model.InterfaceSchemaReferenceRelationship, any](
		func(source chan<- *model.InterfaceSchemaReferenceRelationship) {
			for _, v := range dSet.Values() {
				if s, ok := v.(string); !ok {
					continue
				} else if rs, ok := m[s]; ok {
					source <- rs
				}
			}
		}, func(item *model.InterfaceSchemaReferenceRelationship, writer mr.Writer[any], cancel func(error)) {
			var err error
			defer func() {
				if err != nil {
					cancel(err)
				}
			}()

			item.Deleted = int64(constants.HasDeleted)
			item.DeletedBy = sql.NullString{
				String: l.currentUser.Account,
				Valid:  true,
			}
			item.DeletedAt = sql.NullTime{
				Time:  time.Now(),
				Valid: true,
			}
			if _, err = l.svcCtx.InterfaceSchemaReferenceModel.Update(context, session, item); err != nil {
				err = errors.Wrapf(
					errorx.Err(errorx.DBError, err.Error()),
					"failed to update table[%s] with values[%+v], error: %+v",
					l.svcCtx.InterfaceSchemaReferenceModel.Table(), item, err,
				)
			}
		}, func(pipe <-chan any, cancel func(error)) {
		}, mr.WithContext(l.ctx),
	); err != nil {
		return err
	}

	// 新增引用关系
	return mr.MapReduceVoid[string, any](
		func(source chan<- string) {
			for _, v := range iSet.Values() {
				source <- v.(string)
			}
		}, func(item string, writer mr.Writer[any], cancel func(error)) {
			var err error
			defer func() {
				if err != nil {
					cancel(err)
				}
			}()

			err = l.createSchemaReference(
				context, session, &createSchemaReferenceInternalReq{
					ProjectId:     req.GetProjectId(),
					ReferenceType: common.ConstInterfaceDefinitionTypeDocument,
					ReferenceId:   req.GetDocumentId(),
					SchemaId:      item,
				},
			)
		}, func(pipe <-chan any, cancel func(error)) {
		}, mr.WithContext(l.ctx),
	)
}
