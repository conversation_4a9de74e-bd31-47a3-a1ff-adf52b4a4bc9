package interfacedefinitionservicelogic

import (
	"context"
	"fmt"

	"github.com/hashicorp/go-multierror"
	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/mr"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/redislock"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type RemoveInterfaceDocumentLogic struct {
	*BaseLogic
}

func NewRemoveInterfaceDocumentLogic(ctx context.Context, svcCtx *svc.ServiceContext) *RemoveInterfaceDocumentLogic {
	return &RemoveInterfaceDocumentLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// RemoveInterfaceDocument 删除接口文档
func (l *RemoveInterfaceDocumentLogic) RemoveInterfaceDocument(in *pb.RemoveInterfaceDocumentReq) (resp *pb.RemoveInterfaceDocumentResp, err error) {
	// validate the project_id in req
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, in.GetProjectId()); err != nil {
		return nil, err
	}

	documentIds := stringx.Distinct(in.GetDocumentIds())

	workers := len(documentIds)
	if workers > common.ConstMRMaxWorkers {
		workers = common.ConstMRMaxWorkers
	}

	mr.ForEach(func(source chan<- any) {
		for _, documentId := range documentIds {
			source <- documentId
		}
	}, func(item any) {
		documentId, ok := item.(string)
		if !ok {
			err = multierror.Append(err, errorx.Err(errorx.InternalError, fmt.Sprintf("the document id[%v (%T)] is not a string", item, item)))
		} else {
			if e := l.remove(in.GetProjectId(), documentId); e != nil {
				err = multierror.Append(err, e)
			}
		}
	}, mr.WithContext(l.ctx), mr.WithWorkers(workers))

	return &pb.RemoveInterfaceDocumentResp{}, err
}

func (l *RemoveInterfaceDocumentLogic) remove(projectId, documentId string) (err error) {
	// validate the document_id in req
	document, err := model.CheckInterfaceDocumentByDocumentId(l.ctx, l.svcCtx.InterfaceDocumentModel, projectId, documentId)
	if err != nil {
		return err
	} else if document.Mode == common.ConstInterfaceDefinitionCreateModeBuiltin {
		return errors.WithStack(errorx.Err(errorx.ProhibitedBehavior, fmt.Sprintf("cannot remove the builtin interface document[%s]", document.Name)))
	}

	// validate the reference relationship between document and case
	cs, err := l.svcCtx.InterfaceCaseModel.FindLatestByDocumentId(l.ctx, projectId, documentId)
	if err != nil && err != model.ErrNotFound {
		return errors.Wrapf(errorx.Err(errorx.DBError, err.Error()), "failed to find the interface case with project_id[%s] and document_id[%s], error: %+v", projectId, documentId, err)
	} else if len(cs) > 0 {
		return errors.WithStack(errorx.Err(errorx.ProhibitedBehavior, fmt.Sprintf("cannot remove the interface document[%s] which is referenced by %d interface case[s]", document.Name, len(cs))))
	}

	// validate the reference relationship between api plan and document
	rrs, err := l.svcCtx.ApiPlanReferenceModel.FindReferenceByReference(l.ctx, projectId, common.ConstReferenceTypeInterfaceDocument, documentId, false)
	if err != nil && err != model.ErrNotFound {
		return errors.Wrapf(errorx.Err(errorx.DBError, err.Error()), "failed to find api plan reference with project_id[%s], reference_type[%s] and reference_id[%s], error: %+v", projectId, common.ConstReferenceTypeInterfaceDocument, documentId, err)
	} else if len(rrs) > 0 {
		return errors.WithStack(errorx.Err(errorx.ProhibitedBehavior, fmt.Sprintf("cannot remove the interface document[%s] which is referenced by %d api plan[s]", document.Name, len(rrs))))
	}

	// acquire redis lock
	key := fmt.Sprintf("%s:%s:%s", common.ConstLockInterfaceDocumentProjectIdDocumentIdPrefix, projectId, documentId)
	lock, err := redislock.NewRedisLockAndAcquire(l.svcCtx.Redis, key, redislock.WithExpire(common.ConstLockExpireTime))
	if err != nil {
		return err
	}
	defer func() {
		// release redis lock
		e := lock.Release()
		if e != nil {
			l.Logger.Error(e)
			if re, ok := errorx.RootError(e); ok && re.Code() != errorx.ReleaseRedisLockFailure && err == nil {
				err = e
			}
		}
	}()

	return l.svcCtx.InterfaceDocumentModel.Trans(l.ctx, func(context context.Context, session sqlx.Session) error {
		// Table: category_tree
		if _, err := l.svcCtx.CategoryTreeModel.RemoveByNodeId(context, session, l.svcCtx.CategoryModel, projectId, common.ConstCategoryTreeTypeInterfaceDocument, documentId); err != nil {
			return errors.Wrapf(errorx.Err(errorx.DBError, err.Error()), "failed to remove category tree with project_id[%s] and document_id[%s], error: %+v", projectId, documentId, err)
		}

		// Table: category
		if _, err := l.svcCtx.CategoryModel.RemoveByNodeId(context, session, projectId, common.ConstCategoryTreeTypeInterfaceDocument, documentId); err != nil {
			return errors.Wrapf(errorx.Err(errorx.DBError, err.Error()), "failed to remove category with project_id[%s] and document_id[%s], error: %+v", projectId, documentId, err)
		}

		// Table: interface_configuration
		if _, err := l.svcCtx.InterfaceConfigurationModel.RemoveByDocumentId(context, session, projectId, documentId); err != nil {
			return errors.Wrapf(errorx.Err(errorx.DBError, err.Error()), "failed to remove interface configuration with project_id[%s] and document_id[%s], error: %+v", projectId, documentId, err)
		}

		// Table: interface_document
		if _, err := l.svcCtx.InterfaceDocumentModel.RemoveByDocumentId(context, session, projectId, documentId); err != nil {
			return errors.Wrapf(errorx.Err(errorx.DBError, err.Error()), "failed to remove interface document with project_id[%s] and document_id[%s], error: %+v", projectId, documentId, err)
		}

		return nil
	})
}
