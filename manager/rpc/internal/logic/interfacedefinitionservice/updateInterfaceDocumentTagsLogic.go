package interfacedefinitionservicelogic

import (
	"context"
	"database/sql"
	"fmt"
	"slices"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"
	"github.com/zeromicro/go-zero/core/mr"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zyedidia/generic"
	"github.com/zyedidia/generic/set"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/caller"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
	relationpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/relation/rpc/pb"
)

type (
	UpdateInterfaceDocumentTagsLogic struct {
		*BaseLogic
	}
	serviceTeamItem struct {
		service string
		team    string
	}
	documentTagsItem struct {
		document *model.InterfaceDocument
		tags     []string
	}
)

func NewUpdateInterfaceDocumentTagsLogic(
	ctx context.Context, svcCtx *svc.ServiceContext,
) *UpdateInterfaceDocumentTagsLogic {
	return &UpdateInterfaceDocumentTagsLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// UpdateInterfaceDocumentTags 更新接口文档标签
func (l *UpdateInterfaceDocumentTagsLogic) UpdateInterfaceDocumentTags(in *pb.UpdateInterfaceDocumentTagsReq) (
	out *pb.UpdateInterfaceDocumentTagsResp, err error,
) {
	projectID := in.GetProjectId()

	// validate the project_id in req
	_, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, projectID)
	if err != nil {
		return nil, err
	}

	serviceTeams, teams, err := l.getServiceTeams()
	if err != nil {
		return nil, err
	} else if len(serviceTeams) == 0 {
		l.Warnf("there are no service teams, no need to update document tags, project_id: %s", projectID)
		return &pb.UpdateInterfaceDocumentTagsResp{}, nil
	}
	l.Infof("the number of service teams: %d", len(serviceTeams))

	documents, err := l.svcCtx.InterfaceDocumentModel.FindAll(l.ctx, projectID)
	if err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()),
			"failed to find interface documents by project_id, project_id: %s, error: %+v",
			projectID, err,
		)
	} else if len(documents) == 0 {
		l.Warnf("there are no interface documents in the project, project_id: %s", projectID)
		return &pb.UpdateInterfaceDocumentTagsResp{}, nil
	}
	l.Infof("the number of interface documents: %d", len(documents))

	key := fmt.Sprintf("%s:%s", common.ConstLockUpdateInterfaceDocumentTagsProjectIDPrefix, projectID)
	fn := func() error {
		_ = mr.MapReduceVoid[*model.InterfaceDocument, *documentTagsItem](
			func(source chan<- *model.InterfaceDocument) {
				for _, document := range documents {
					source <- document
				}
			},
			func(item *model.InterfaceDocument, writer mr.Writer[*documentTagsItem], cancel func(error)) {
				if item == nil {
					return
				}

				var err error
				defer func() {
					if err != nil {
						l.Error(err)
					}
				}()

				data, ok := serviceTeams[item.Service.String]
				if !ok {
					method, err := getMethodFromDocument(item)
					if err != nil {
						return
					}

					service, err := l.getServiceByMethod(method)
					if err != nil {
						return
					}

					if data, ok = serviceTeams[service]; !ok {
						l.Warnf("not found the team for the service, service: %s, method: %s", service, method)
						return
					}
				}

				var tags []string
				if item.Tags.Valid && item.Tags.String != "" {
					if err = jsonx.UnmarshalFromString(item.Tags.String, &tags); err != nil {
						err = errorx.Errorf(
							errorx.SerializationError,
							"failed to unmarshal the tags, tags: %s, error: %+v",
							item.Tags.String, err,
						)
						return
					}
				}

				tagsSet := set.NewHashset[string](
					constants.ConstDefaultMakeSliceSize, generic.Equals[string], generic.HashString,
				)
				for _, tag := range tags {
					if !slices.Contains(teams, tag) {
						tagsSet.Put(tag)
					}
				}
				tagsSet.Put(data.team)

				tag := jsonx.MarshalToStringIgnoreError(tagsSet.Keys())
				l.Infof(
					"update document tags, document_id: %s, name: %s, tags: %s => %s",
					item.DocumentId, item.Name, item.Tags.String, tag,
				)
				item.Tags = sql.NullString{
					String: tag,
					Valid:  tag != "",
				}

				writer.Write(
					&documentTagsItem{
						document: item,
						tags:     tagsSet.Keys(),
					},
				)
			},
			func(pipe <-chan *documentTagsItem, cancel func(error)) {
				for item := range pipe {
					if item == nil {
						continue
					}

					if err := l.svcCtx.InterfaceDocumentModel.Trans(
						l.ctx, func(context context.Context, session sqlx.Session) error {
							if _, err := l.svcCtx.InterfaceDocumentModel.UpdateTX(
								context, session, item.document,
							); err != nil {
								return err
							}

							return l.createTagLogic.CreateTagAndReferenceForInternal(
								context, session, types.CreateOrUpdateTagReference{
									ProjectId:     item.document.ProjectId,
									ReferenceType: common.ConstReferenceTypeInterfaceDocument,
									ReferenceId:   item.document.DocumentId,
									Tags:          item.tags,
								},
							)
						},
					); err != nil {
						l.Errorf(
							"failed to update the document, document_id: %s, name: %s, tags: %s, error: %+v",
							item.document.DocumentId, item.document.Name, item.document.Tags.String, err,
						)
					}
				}
			},
			mr.WithContext(l.ctx), mr.WithWorkers(common.ConstMRMaxWorkers),
		)

		return nil
	}
	if err = caller.LockDo(l.svcCtx.Redis, key, fn); err != nil {
		return nil, err
	}

	return &pb.UpdateInterfaceDocumentTagsResp{}, nil
}

func (l *UpdateInterfaceDocumentTagsLogic) getServiceTeams() (map[string]*serviceTeamItem, []string, error) {
	out, err := l.svcCtx.RelationRPC.GetAllServiceTeams(l.ctx, &relationpb.GetAllServiceTeamsReq{})
	if err != nil {
		return nil, nil, err
	}

	relations := make(map[string]*serviceTeamItem, len(out.GetRelations()))
	teams := set.NewHashset[string](constants.ConstDefaultMakeSliceSize, generic.Equals[string], generic.HashString)
	for _, relation := range out.GetRelations() {
		relations[relation.GetService()] = &serviceTeamItem{
			service: relation.GetService(),
			team:    relation.GetTeamName(),
		}
		teams.Put(relation.GetTeamName())
	}

	return relations, teams.Keys(), nil
}
