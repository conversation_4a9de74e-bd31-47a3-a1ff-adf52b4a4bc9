package loader

import (
	"strings"

	"google.golang.org/protobuf/reflect/protoreflect"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/types"
)

type Loader interface {
	Init() error
	RangeServices(f func(serviceName string, documents []types.DocumentRecorder) bool)
	RangeFiles(f func(fileName string, schemas []types.SchemaRecorder) bool)
	RangeSchemas(f func(apiName string, schema types.SchemaRecorder) bool)
	GetSchemaRecorderByFullName(fullName string) (types.SchemaRecorder, error)
	GetFieldSchemaByFullName(fullName string, excludeRoot bool) (types.FieldSchema, error)
}

func getName(fd protoreflect.FieldDescriptor) string {
	// 接口数据模型字段名称采用蛇形命名，`FieldDescriptor.JSONName()`返回的是小驼峰命名
	//if fd.HasJSONName() {
	//	return fd.JSONName()
	//}

	return string(fd.Name())
}

func getType(fd protoreflect.FieldDescriptor, getInnerType bool) common.InterfaceDefinitionFieldType {
	if !getInnerType {
		if fd.IsList() {
			return common.ConstInterfaceDefinitionFieldTypeArray
		} else if fd.IsMap() {
			return common.ConstInterfaceDefinitionFieldTypeMap
		}
	}

	switch fd.Kind() {
	case protoreflect.StringKind, protoreflect.BytesKind:
		return common.ConstInterfaceDefinitionFieldTypeString
	case protoreflect.EnumKind, protoreflect.Int32Kind, protoreflect.Sint32Kind, protoreflect.Uint32Kind, protoreflect.Sfixed32Kind, protoreflect.Fixed32Kind,
		protoreflect.Int64Kind, protoreflect.Sint64Kind, protoreflect.Uint64Kind, protoreflect.Sfixed64Kind, protoreflect.Fixed64Kind:
		return common.ConstInterfaceDefinitionFieldTypeInteger
	case protoreflect.FloatKind, protoreflect.DoubleKind:
		return common.ConstInterfaceDefinitionFieldTypeNumber
	case protoreflect.BoolKind:
		return common.ConstInterfaceDefinitionFieldTypeBoolean
	case protoreflect.MessageKind:
		return common.ConstInterfaceDefinitionFieldTypeSchema
	}

	return common.ConstInterfaceDefinitionFieldTypeAny
}

func getRequired(fd protoreflect.FieldDescriptor) bool {
	if fd.Syntax() == protoreflect.Proto2 && fd.Cardinality() == protoreflect.Required {
		return true
	}

	return false
}

func getDefault(fd protoreflect.FieldDescriptor) any {
	if fd.HasDefault() {
		return fd.Default().Interface()
	}

	return nil
}

func getFormat(fd protoreflect.FieldDescriptor) common.InterfaceDefinitionFieldFormat {
	switch fd.Kind() {
	case protoreflect.EnumKind, protoreflect.Int32Kind, protoreflect.Sint32Kind, protoreflect.Uint32Kind, protoreflect.Sfixed32Kind, protoreflect.Fixed32Kind:
		return common.ConstInterfaceDefinitionFieldFormatInt32
	case protoreflect.Int64Kind, protoreflect.Sint64Kind, protoreflect.Uint64Kind, protoreflect.Sfixed64Kind, protoreflect.Fixed64Kind:
		return common.ConstInterfaceDefinitionFieldFormatInt64
	case protoreflect.FloatKind:
		return common.ConstInterfaceDefinitionFieldFormatFloat
	case protoreflect.DoubleKind:
		return common.ConstInterfaceDefinitionFieldFormatDouble
	default:
		return ""
	}
}

func getFileNameByMessageDescriptor(md protoreflect.MessageDescriptor) string {
	fd := md.ParentFile()
	if fd == nil {
		return ""
	}

	return strings.TrimSuffix(fd.Path(), ConstProtobufFileExt)
}
