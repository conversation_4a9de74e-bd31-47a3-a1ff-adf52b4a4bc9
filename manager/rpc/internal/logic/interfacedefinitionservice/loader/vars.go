package loader

import (
	"fmt"

	"github.com/dlclark/regexp2"
	"github.com/pkg/errors"
)

var (
	zeroProtoProjects   = errors.New("the projects of product is empty")
	notFoundCmdDefFile  = errors.Errorf("%s not found", ConstCmdDefFileName)
	notFoundFixJsonFile = errors.Errorf("%s not found", ConstFixJsonFileName)

	cmdRegexCompile = regexp2.MustCompile(
		fmt.Sprintf(
			`^(?!//).*?((?<%s>CMD_\w+)\s*=\s*(?<%s>\d+)\s*;.*)`, ConstGroupNameCmdName, ConstGroupNameCmdValue,
		), regexp2.None,
	)
	suffixRegexCompile = regexp2.MustCompile(
		fmt.Sprintf(`^(?<%s>.*?)(req|rsp|resp)$`, ConstGroupNameCmdName), regexp2.IgnoreCase,
	)
)
