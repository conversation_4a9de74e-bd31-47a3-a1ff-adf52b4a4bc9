package loader

import (
	"fmt"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/lang"
	"google.golang.org/protobuf/reflect/protoreflect"
	"google.golang.org/protobuf/types/known/structpb"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/tt"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/utils"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

var _ Loader = (*GRPCLoader)(nil)

type (
	GRPCLoader struct {
		product protobuf.Product
		pm      *protobuf.ProtoManager

		documentIdRecorders types.Recorders
		schemaIdRecorders   types.Recorders
		serviceRecorders    ServiceRecorders
		fileRecorders       FileRecorders
		schemaRecorders     SchemaRecorders

		genDocumentIdFunc GenIdFunc
		genSchemaIdFunc   GenIdFunc
	}
	GRPCLoaderOption func(*GRPCLoader)
)

func NewGRPCLoader(product protobuf.Product, options ...GRPCLoaderOption) *GRPCLoader {
	l := &GRPCLoader{
		product: product,

		documentIdRecorders: make(types.Recorders),
		schemaIdRecorders:   make(types.Recorders),
		serviceRecorders:    make(ServiceRecorders),
		fileRecorders:       make(FileRecorders),
		schemaRecorders:     make(SchemaRecorders),

		genDocumentIdFunc: func(name string, cache types.Recorders) string {
			// 相信生成的 `document_id` 重复概率很低，默认不做检查
			id := utils.GenInterfaceDocumentId()
			cache[id] = lang.Placeholder
			return id
		},
		genSchemaIdFunc: func(name string, cache types.Recorders) string {
			// 相信生成的 `schema_id` 重复概率很低，默认不做检查
			id := utils.GenInterfaceSchemaId()
			cache[id] = lang.Placeholder
			return id
		},
	}

	for _, option := range options {
		option(l)
	}

	return l
}

func WithGenDocumentIDFunc(f GenIdFunc) GRPCLoaderOption {
	return func(l *GRPCLoader) {
		l.genDocumentIdFunc = f
	}
}

func WithGenSchemaIDFunc(f GenIdFunc) GRPCLoaderOption {
	return func(l *GRPCLoader) {
		l.genSchemaIdFunc = f
	}
}

func (l *GRPCLoader) Init() (err error) {
	l.pm, err = protobuf.NewProtoManager(protobuf.WithProducts(l.product))
	if err != nil {
		return err
	}

	// 生成接口定义及数据模型
	l.generateDefinitionsAndSchemas()
	return nil
}

// generateDefinitionsAndSchemas 生成接口定义及数据模型
func (l *GRPCLoader) generateDefinitionsAndSchemas() {
	_ = l.pm.RangeMessageDescriptors(
		func(md protoreflect.MessageDescriptor) bool {
			l.generateSchemaByMessageDescriptor(md)
			return true
		},
	)

	_ = l.pm.RangeServiceDescriptors(
		func(sd protoreflect.ServiceDescriptor) bool {
			methods := sd.Methods()
			for i := 0; i < methods.Len(); i++ {
				l.setDefinitionSchema(sd, methods.Get(i))
			}
			return true
		},
	)
}

// setDefinitionSchema 设置接口定义
func (l *GRPCLoader) setDefinitionSchema(sd protoreflect.ServiceDescriptor, md protoreflect.MethodDescriptor) {
	var (
		serviceName     = string(sd.Name())
		serviceRecorder = l.getServiceRecorderByServiceName(serviceName)

		methodName       = string(md.Name())
		methodFullName   = string(md.FullName())
		requestFullName  = string(md.Input().FullName())
		responseFullName = string(md.Output().FullName())
	)

	opts := tt.GetLogicOptions(md)
	doc := &types.DocumentRecorder{
		DocumentId: l.genDocumentIdFunc(serviceName+":"+methodName, l.documentIdRecorders),
		FullName:   methodFullName,
		Service:    serviceName,
		ApiName:    methodName,
		ReqSchema:  l.schemaRecorders[requestFullName],
		RespSchema: l.schemaRecorders[responseFullName],
		Cmd:        opts.MethodOptions.ID, // 此字段暂时只有`TT`有效
	}
	serviceRecorder.DocumentRecorders[methodName] = doc
}

// generateSchemaByMessageDescriptor 通过 `MessageDescriptor` 生成 `*SchemaRecorder`
func (l *GRPCLoader) generateSchemaByMessageDescriptor(md protoreflect.MessageDescriptor) *types.SchemaRecorder {
	mdFullName := string(md.FullName())
	mdName := string(md.Name())

	sr, ok := l.schemaRecorders[mdFullName]
	if ok {
		return sr
	}

	ps := &pb.Schema{
		Title:      common.ConstJsonSchemaRootTitle,
		Type:       common.ConstInterfaceDefinitionFieldTypeObject,
		Properties: make(types.FieldSchema),
		Index:      1,
	}
	// 默认增加根节点 `root`
	sr = &types.SchemaRecorder{
		SchemaId: l.genSchemaIdFunc(mdFullName, l.schemaIdRecorders),
		FullName: mdFullName,
		Name:     mdName,
		Data:     ps,
	}
	// 避免循环引用导致死循环，提前设置
	l.schemaRecorders[mdFullName] = sr

	fileName := getFileNameByMessageDescriptor(md)
	if fileName != "" {
		fr := l.getFileRecorderByFileName(fileName)
		if _, ok = fr.SchemaRecorders[mdFullName]; !ok {
			fr.SchemaRecorders[mdFullName] = sr
		}
	}

	fds := md.Fields()
	for i := 0; i < fds.Len(); i++ {
		schema := ps
		fd := fds.Get(i)
		fdName := getName(fd)

		od := fd.ContainingOneof()
		if od != nil {
			// `od` 不等于nil，表示当前的 `FieldDescriptor` 属于 `oneof` 中
			odName := string(od.Name()) // `od.Name()` 返回的是 `oneof` 这个字段名称，如：data

			schema, ok = ps.Properties[odName]
			if !ok {
				// `oneof` 字段未在数据模型中则需要先创建
				schema = &pb.Schema{
					Title:       odName,
					Type:        common.ConstInterfaceDefinitionFieldTypeOneOf,
					Description: string(od.FullName()),
					Properties:  make(types.FieldSchema, od.Fields().Len()),
					Index:       int32(len(ps.Properties) + 1),
				}
				ps.Properties[odName] = schema
			}
		}
		// 当前的 `FieldDescriptor` 创建到数据模型中（包括 `oneof` 字段的 `properties` 下）
		s := l.generateSchemaByFieldDescriptor(fd)
		s.Index = int32(len(schema.Properties) + 1)
		if s.FieldRequired {
			schema.Required = append(schema.Required, fdName)
		}
		if s.Ref != nil {
			if v, ok := l.schemaRecorders[s.Ref.FullName]; ok {
				sr.References = append(sr.References, v)
			}
		}
		schema.Properties[fdName] = s
	}

	return sr
}

// generateSchemaByFieldDescriptor 通过 `FieldDescriptor` 生成 `*pb.Schema`
func (l *GRPCLoader) generateSchemaByFieldDescriptor(fd protoreflect.FieldDescriptor) *pb.Schema {
	s := &pb.Schema{
		Title:         getName(fd),
		Type:          getType(fd, false),
		Description:   string(fd.FullName()),
		FieldRequired: getRequired(fd),
		Default:       logic.ConvertAnyToValue(getDefault(fd)),
		Format:        getFormat(fd),
	}

	if fd.IsList() {
		l.handleListType(fd, s)
	} else if fd.IsMap() {
		l.handleMapType(fd, s)
	} else if fd.Kind() == protoreflect.MessageKind {
		l.handleMessageType(fd, s)
	} else if fd.Kind() == protoreflect.EnumKind {
		l.handleEnumType(fd, s)
	}

	return s
}

func (l *GRPCLoader) handleListType(fd protoreflect.FieldDescriptor, s *pb.Schema) {
	s.Items = &pb.Schema{
		Title: "items",
		Type:  getType(fd, true),
	}

	if fd.Kind() == protoreflect.MessageKind {
		md := fd.Message()
		fullName := string(md.FullName())
		r, ok := l.schemaRecorders[fullName]
		if !ok {
			r = l.generateSchemaByMessageDescriptor(md)
		}
		s.Items.Description = fullName
		s.Items.Ref = &pb.RefSchema{
			SchemaId:    r.SchemaId,
			FullName:    fullName,
			DisplayName: string(md.Name()),
		}
	}
}

func (l *GRPCLoader) handleMapType(fd protoreflect.FieldDescriptor, s *pb.Schema) {
	s.Description = fmt.Sprintf("%s: map[%s, %s]", s.Description, fd.MapKey().FullName(), fd.MapValue().FullName())
}

func (l *GRPCLoader) handleMessageType(fd protoreflect.FieldDescriptor, s *pb.Schema) {
	md := fd.Message()
	fullName := string(md.FullName())
	r, ok := l.schemaRecorders[fullName]
	if !ok {
		r = l.generateSchemaByMessageDescriptor(md)
	}
	s.Ref = &pb.RefSchema{
		SchemaId:    r.SchemaId,
		FullName:    fullName,
		DisplayName: string(md.Name()),
	}
}

func (l *GRPCLoader) handleEnumType(fd protoreflect.FieldDescriptor, s *pb.Schema) {
	evs := fd.Enum().Values()
	ll := evs.Len()

	s.Enum = make([]*structpb.Value, ll)
	s.Enums = make([]*pb.EnumValDesc, ll)

	for j := 0; j < ll; j++ {
		ev := evs.Get(j)
		s.Enum[j] = logic.ConvertAnyToValue(ev.Number())
		s.Enums[j] = &pb.EnumValDesc{
			Value:       logic.ConvertAnyToValue(ev.Number()),
			Description: string(ev.FullName()),
		}
	}
}

func (l *GRPCLoader) getServiceRecorderByServiceName(serviceName string) *ServiceRecorder {
	sr, ok := l.serviceRecorders[serviceName]
	if !ok {
		sr = &ServiceRecorder{
			DocumentRecorders: make(DocumentRecorders),
		}
		l.serviceRecorders[serviceName] = sr
	}

	return sr
}

func (l *GRPCLoader) getFileRecorderByFileName(fileName string) *FileRecorder {
	fr, ok := l.fileRecorders[fileName]
	if !ok {
		fr = &FileRecorder{
			SchemaRecorders: make(SchemaRecorders),
		}
		l.fileRecorders[fileName] = fr
	}

	return fr
}

func (l *GRPCLoader) RangeServices(f func(serviceName string, documents []types.DocumentRecorder) bool) {
	for srvName, serviceRecorder := range l.serviceRecorders {
		documents := make([]types.DocumentRecorder, 0, len(serviceRecorder.DocumentRecorders))

		for _, dr := range serviceRecorder.DocumentRecorders {
			documents = append(documents, *dr)
		}
		if !f(srvName, documents) {
			return
		}
	}
}

func (l *GRPCLoader) RangeFiles(f func(fileName string, schemas []types.SchemaRecorder) bool) {
	for fileName, fileRecorder := range l.fileRecorders {
		schemas := make([]types.SchemaRecorder, 0, len(fileRecorder.SchemaRecorders))

		for _, sr := range fileRecorder.SchemaRecorders {
			schemas = append(schemas, *sr)
		}
		if !f(fileName, schemas) {
			return
		}
	}
}

func (l *GRPCLoader) RangeSchemas(f func(apiName string, schema types.SchemaRecorder) bool) {
	for apiName, schema := range l.schemaRecorders {
		if !f(apiName, *schema) {
			return
		}
	}
}

func (l *GRPCLoader) GetSchemaRecorderByFullName(fullName string) (types.SchemaRecorder, error) {
	sr, ok := l.schemaRecorders[fullName]
	if !ok {
		return types.SchemaRecorder{}, errors.Errorf("schema[%s] not found", fullName)
	}

	return *sr, nil
}

func (l *GRPCLoader) GetFieldSchemaByFullName(fullName string, excludeRoot bool) (types.FieldSchema, error) {
	sr, err := l.GetSchemaRecorderByFullName(fullName)
	if err != nil {
		return nil, err
	}

	data := sr.Data
	if excludeRoot {
		return data.Properties, nil
	}

	return types.FieldSchema{common.ConstJsonSchemaRootTitle: data}, nil
}
