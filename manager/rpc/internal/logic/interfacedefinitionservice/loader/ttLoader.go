package loader

import (
	"bufio"
	"bytes"
	"io"
	"os"
	"path/filepath"
	"strconv"
	"strings"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"
	"github.com/zeromicro/go-zero/core/logx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
)

var _ Loader = (*TTLoader)(nil)

// Deprecated: use `GRPCLoader` instead.
type TTLoader struct {
	*GRPCLoader

	cmdRecorders CmdRecorders
	fixRecorders FixRecorders
}

func NewTTLoader(product protobuf.Product, options ...GRPCLoaderOption) *TTLoader {
	return &TTLoader{
		GRPCLoader: NewGRPCLoader(product, options...),

		cmdRecorders: make(CmdRecorders),
		fixRecorders: make(FixRecorders),
	}
}

// Init 初始化TT接口定义加载器
func (l *TTLoader) Init() error {
	return l.GRPCLoader.Init()
}

// Deprecated: loadCmdDefFile 加载 `cmddef.h` 文件
func (l *TTLoader) loadCmdDefFile() error {
	if len(l.product.Projects) == 0 {
		return zeroProtoProjects
	}

	cmdDefFile := filepath.Join(l.product.Projects[0].Path, ConstCmdDefFileName)
	fi, err := os.Stat(cmdDefFile)
	if os.IsNotExist(err) {
		return notFoundCmdDefFile
	} else if err != nil {
		return err
	} else if fi.IsDir() {
		return notFoundCmdDefFile
	}

	bs, err := os.ReadFile(cmdDefFile)
	if err != nil {
		return err
	}

	reader := bufio.NewReader(bytes.NewReader(bs))
	for {
		line, err := reader.ReadString('\n')
		if err != nil && err != io.EOF {
			return err
		} else if err == io.EOF {
			break
		}

		line = strings.TrimSpace(line)
		match, err := cmdRegexCompile.FindStringMatch(line)
		if err != nil {
			logx.Errorf("failed to find string match with the content[%s], error: %+v", line, err)
			continue
		} else if match == nil {
			// not match pattern
			continue
		}

		key := match.GroupByName(ConstGroupNameCmdName)
		if key == nil {
			logx.Errorf("failed to get the match group by name[%s] with the content[%s]", ConstGroupNameCmdName, line)
			continue
		}
		cmdKey := key.String()

		val := match.GroupByName(ConstGroupNameCmdValue)
		if val == nil {
			logx.Errorf("failed to get the match group by name[%s] with the content[%s]", ConstGroupNameCmdValue, line)
			continue
		}
		cmdVal, err := strconv.ParseUint(val.String(), 10, 32)
		if err != nil {
			logx.Errorf(
				"the content[%s] matches the pattern, but the cmd value[%s] cannot convert to integer, error: %+v",
				line, val.String(), err,
			)
			continue
		}

		l.cmdRecorders[cmdKey] = uint32(cmdVal)
	}

	return nil
}

// Deprecated: loadFixJsonFile 加载 `fix.json` 文件
func (l *TTLoader) loadFixJsonFile() error {
	if len(l.product.Projects) == 0 {
		return zeroProtoProjects
	}

	fixJsonFile := filepath.Join(l.product.Projects[0].Path, ConstFixJsonFileName)
	fi, err := os.Stat(fixJsonFile)
	if os.IsNotExist(err) {
		return notFoundFixJsonFile
	} else if err != nil {
		return err
	} else if fi.IsDir() {
		return notFoundFixJsonFile
	}

	bs, err := os.ReadFile(fixJsonFile)
	if err != nil {
		return err
	}

	var fos []FixObj
	err = jsonx.Unmarshal(bs, &fos)
	if err != nil {
		return err
	}

	for _, fo := range fos {
		if fo.ApiName == "" || fo.Cmd == 0 {
			continue
		}

		if _, ok := l.fixRecorders[fo.ApiName]; ok {
			continue
		}

		l.fixRecorders[fo.ApiName] = fo.Cmd
	}

	return nil
}

// Deprecated: getCmd 获取 `cmd` 值
func (l *TTLoader) getCmd(name string) (uint32, error) {
	var (
		cmdName string
		apiName string
	)

	cmdName = name
	apiName = cmdName
	match, err := suffixRegexCompile.FindStringMatch(name)
	if err == nil && match != nil {
		group := match.GroupByName(ConstGroupNameCmdName)
		if group == nil {
			logx.Errorf("failed to get the match group by name[%s]", ConstGroupNameCmdName)
		} else {
			cmdName = group.String()
			apiName = cmdName
		}
	}

	if !strings.HasPrefix(cmdName, ConstCmdNamePrefix) {
		cmdName = ConstCmdNamePrefix + cmdName
	}

	for _, n := range []string{cmdName, strings.ToUpper(cmdName)} {
		cmd, ok := l.cmdRecorders[n]
		if ok {
			return cmd, nil
		}
	}

	cmd, ok := l.fixRecorders[apiName]
	if ok {
		return cmd, nil
	}

	return 0, errors.Errorf("cannot find the cmd by the name[%s => %s, %s]", name, cmdName, apiName)
}
