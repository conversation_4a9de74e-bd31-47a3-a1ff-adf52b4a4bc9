package loader

import (
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/types"
)

// ServiceRecorder 服务记录器
type ServiceRecorder struct {
	DocumentRecorders DocumentRecorders `json:"document_recorders"`
}
type FileRecorder struct {
	SchemaRecorders SchemaRecorders `json:"schema_recorders"`
}

type (
	FileRecorders     = map[string]*FileRecorder           // key: fileName
	ServiceRecorders  = map[string]*ServiceRecorder        // key: serviceName
	DocumentRecorders = map[string]*types.DocumentRecorder // key: apiName
	SchemaRecorders   = map[string]*types.SchemaRecorder   // key: fullName
	CmdRecorders      = map[string]uint32                  // key: cmdKey
	FixRecorders      = map[string]uint32                  // key: apiName
	GenIdFunc         func(name string, cache types.Recorders) string
)

type FixObj struct {
	ApiName string `json:"api_name"`
	// ReqMessage  string `json:"req_message"`
	// RespMessage string `json:"resp_message"`
	Cmd uint32 `json:"cmd"`
}
