package loader

import (
	"os"
	"path/filepath"
	"testing"

	"github.com/zeromicro/go-zero/core/jsonx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/types"
)

func TestTTLoader(t *testing.T) {
	goModCachePath := os.Getenv("GOMODCACHE")

	tests := []struct {
		name string
		args struct {
			target  protobuf.Target
			schemas []string
		}
	}{
		{
			name: "probe",
			args: struct {
				target  protobuf.Target
				schemas []string
			}{
				target: protobuf.Target{
					Path:   "../../../../../../protos",
					Branch: "main",
					ImportPaths: []string{
						"../../../../../../dep_protos/protovalidate/proto/protovalidate",
						filepath.Join(goModCachePath, "github.com/envoyproxy/protoc-gen-validate@v1.0.4"),
						filepath.Join(
							goModCachePath,
							"gitlab.ttyuyin.com/!test!development/qet-backend-common@v1.20.3-0.20241206021021-923e78a00301",
							"protos",
						),
						filepath.Join(
							goModCachePath,
							"gitlab.ttyuyin.com/!test!development/qet-backend-middleware@v1.19.4-0.20241204082321-c6ed2be8addf",
							"protos",
						),
					},
				},
				schemas: []string{"manager.GetApiExecutionDataReq", "manager.ApiExecutionData"},
			},
		},
		{
			name: "tt",
			args: struct {
				target  protobuf.Target
				schemas []string
			}{
				target: protobuf.Target{
					Path:         "/Users/<USER>/Workspace/TTProjects/app",
					Branch:       "master",
					ExcludeFiles: []string{"validate.proto"},
				},
				schemas: []string{"ga.channel.ChannelEnterReq", "ga.channel.ChannelEnterResp"},
			},
		},
		{
			name: "recommend",
			args: struct {
				target  protobuf.Target
				schemas []string
			}{
				target: protobuf.Target{
					Path:   "/Users/<USER>/Workspace/TTProjects/rcmd/api/rcmd",
					Branch: "release/tt_rel_prod",
					ImportPaths: []string{
						"../../../../../../dep_protos/googleapis",
						filepath.Join(goModCachePath, "github.com/envoyproxy/protoc-gen-validate@v1.0.4"),
					},
				},
				schemas: []string{
					"topic_channel.recommendation_gen.GetRecommendationListReq",
					"topic_channel.recommendation_gen.GetRecommendationListResp",
				},
			},
		},
	}

	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				loader := NewGRPCLoader(tt.args.target)
				err := loader.Init()
				if err != nil {
					t.Errorf("Init error: %+v", err)
					return
				}

				t.Logf("NumFiles: %d", len(loader.fileRecorders))
				t.Logf("Services: %d", len(loader.serviceRecorders))
				loader.RangeServices(
					func(serviceName string, documents []types.DocumentRecorder) bool {
						t.Logf("Service: %s, %d", serviceName, len(documents))
						return true
					},
				)

				for _, s := range tt.args.schemas {
					t.Logf("\n%s", jsonx.MarshalToIndentStringIgnoreError(loader.schemaRecorders[s], "", "  "))
				}
			},
		)
	}
}
