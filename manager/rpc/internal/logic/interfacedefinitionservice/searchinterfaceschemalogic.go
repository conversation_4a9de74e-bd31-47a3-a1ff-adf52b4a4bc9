package interfacedefinitionservicelogic

import (
	"context"
	"math"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type SearchInterfaceSchemaLogic struct {
	*BaseLogic
}

func NewSearchInterfaceSchemaLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SearchInterfaceSchemaLogic {
	return &SearchInterfaceSchemaLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// SearchInterfaceSchema 搜索接口数据模型
func (l *SearchInterfaceSchemaLogic) SearchInterfaceSchema(in *pb.SearchInterfaceSchemaReq) (resp *pb.SearchInterfaceSchemaResp, err error) {
	resp = &pb.SearchInterfaceSchemaResp{}

	// validate the project_id in req
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, in.GetProjectId()); err != nil {
		return nil, err
	}

	// validate the category_id in req
	if _, err = model.CheckCategoryByCategoryId(l.ctx, l.svcCtx.CategoryModel, in.GetProjectId(), common.ConstCategoryTreeTypeInterfaceSchema, in.GetCategoryId()); err != nil {
		return nil, err
	}

	selectBuilder, countBuilder, err := l.generateSearchSqlBuilderByType(common.ConstInterfaceDefinitionTypeSchema, searchInternalReq{
		SearchByCategoryId: types.SearchByCategoryId{
			ProjectId:  in.GetProjectId(),
			CategoryId: in.GetCategoryId(),
			Condition:  in.GetCondition(),
			Pagination: in.GetPagination(),
			Sort:       in.GetSort(),
		},
		DrillDown: true,
	})
	if err != nil {
		return nil, err
	}

	count, err := l.svcCtx.InterfaceSchemaModel.FindCount(l.ctx, countBuilder)
	if err != nil {
		return nil, errors.Wrapf(errorx.Err(errorx.DBError, err.Error()), "failed to count interface schema with project_id[%s] and category_id[%s], error: %+v", in.GetProjectId(), in.GetCategoryId(), err)
	}
	resp.TotalCount = uint64(count)
	resp.TotalPage = 1

	schemas, err := l.svcCtx.InterfaceSchemaModel.FindNoCacheByQuery(l.ctx, selectBuilder)
	if err != nil {
		return nil, errors.Wrapf(errorx.Err(errorx.DBError, err.Error()), "failed to find interface schema with project_id[%s] and category_id[%s], error: %+v", in.GetProjectId(), in.GetCategoryId(), err)
	}

	resp.Items = make([]*pb.InterfaceSchema, 0, len(schemas))
	for _, schema := range schemas {
		s := &pb.InterfaceSchema{}
		if err = utils.Copy(s, schema); err != nil {
			return nil, errors.Wrapf(errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy interface schema[%+v] to response, error: %+v", schema, err)
		}

		// 搜索时不返回 `data`
		s.Data = nil
		resp.Items = append(resp.Items, s)
	}

	pagination := in.GetPagination()
	if pagination != nil {
		resp.CurrentPage = pagination.GetCurrentPage()
		resp.PageSize = pagination.GetPageSize()
		resp.TotalPage = uint64(math.Ceil(float64(resp.TotalCount) / float64(resp.PageSize)))
	}

	return resp, nil
}
