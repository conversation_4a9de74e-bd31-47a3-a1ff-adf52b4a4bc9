package interfacedefinitionservicelogic

import (
	"context"
	"math"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sqlbuilder/rpc"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type SearchInterfaceDocumentReferenceLogic struct {
	*BaseLogic
}

func NewSearchInterfaceDocumentReferenceLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SearchInterfaceDocumentReferenceLogic {
	return &SearchInterfaceDocumentReferenceLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// SearchInterfaceDocumentReference 搜索接口集合引用详情
func (l *SearchInterfaceDocumentReferenceLogic) SearchInterfaceDocumentReference(in *pb.SearchInterfaceDocumentReferenceReq) (resp *pb.SearchInterfaceDocumentReferenceResp, err error) {
	resp = &pb.SearchInterfaceDocumentReferenceResp{}

	// validate the project_id in req
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, in.GetProjectId()); err != nil {
		return nil, err
	}

	// validate the document_id in req
	if _, err = model.CheckInterfaceDocumentByDocumentId(l.ctx, l.svcCtx.InterfaceDocumentModel, in.GetProjectId(), in.GetDocumentId()); err != nil {
		return nil, err
	}

	selectBuilder, countBuilder := l.svcCtx.InterfaceDocumentModel.GenerateSearchInterfaceDocumentReferenceSqlBuilder(model.SearchInterfaceDocumentReferenceReq{
		ProjectId:  in.GetProjectId(),
		DocumentId: in.GetDocumentId(),
		Condition:  in.GetCondition(),
		Pagination: in.GetPagination(),
		Sort:       rpc.ConvertSortFields(in.GetSort()),
	})

	count, err := l.svcCtx.InterfaceDocumentModel.FindCountInterfaceDocumentReference(l.ctx, countBuilder)
	if err != nil {
		return nil, errors.Wrapf(errorx.Err(errorx.DBError, err.Error()), "failed to count reference data of interface document with project_id[%s] and suite_id[%s], error: %+v", in.GetProjectId(), in.GetDocumentId(), err)
	}
	resp.TotalCount = uint64(count)
	resp.TotalPage = 1

	documents, err := l.svcCtx.InterfaceDocumentModel.FindInterfaceDocumentReference(l.ctx, selectBuilder)
	if err != nil {
		return nil, errors.Wrapf(errorx.Err(errorx.DBError, err.Error()), "failed to find reference data of interface document with project_id[%s] and suite_id[%s], error: %+v", in.GetProjectId(), in.GetDocumentId(), err)
	}

	resp.Items = make([]*pb.SearchInterfaceDocumentReferenceItem, 0, len(documents))
	for _, document := range documents {
		item := &pb.SearchInterfaceDocumentReferenceItem{}
		if err = utils.Copy(item, document, l.converters...); err != nil {
			return nil, errors.Wrapf(errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy reference data of interface document[%+v] to response, error: %+v", document, err)
		}

		resp.Items = append(resp.Items, item)
	}

	pagination := in.GetPagination()
	if pagination != nil {
		resp.CurrentPage = pagination.GetCurrentPage()
		resp.PageSize = pagination.GetPageSize()
		resp.TotalPage = uint64(math.Ceil(float64(resp.TotalCount) / float64(resp.PageSize)))
	}

	return resp, nil
}
