package interfacedefinitionservicelogic

import (
	"context"
	"math"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type SearchInterfaceDocumentLogic struct {
	*BaseLogic
}

func NewSearchInterfaceDocumentLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SearchInterfaceDocumentLogic {
	return &SearchInterfaceDocumentLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// SearchInterfaceDocument 搜索接口文档
func (l *SearchInterfaceDocumentLogic) SearchInterfaceDocument(in *pb.SearchInterfaceDocumentReq) (resp *pb.SearchInterfaceDocumentResp, err error) {
	resp = &pb.SearchInterfaceDocumentResp{}

	// validate the project_id in req
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, in.GetProjectId()); err != nil {
		return nil, err
	}

	// validate the category_id in req
	if _, err = model.CheckCategoryByCategoryId(l.ctx, l.svcCtx.CategoryModel, in.GetProjectId(), common.ConstCategoryTreeTypeInterfaceDocument, in.GetCategoryId()); err != nil {
		return nil, err
	}

	selectBuilder, countBuilder, err := l.generateSearchSqlBuilderByType(common.ConstInterfaceDefinitionTypeDocument, searchInternalReq{
		SearchByCategoryId: types.SearchByCategoryId{
			ProjectId:  in.GetProjectId(),
			CategoryId: in.GetCategoryId(),
			Condition:  in.GetCondition(),
			Pagination: in.GetPagination(),
			Sort:       in.GetSort(),
		},
		DrillDown: true,
	})
	if err != nil {
		return nil, err
	}

	count, err := l.svcCtx.InterfaceDocumentModel.FindCount(l.ctx, countBuilder)
	if err != nil {
		return nil, errors.Wrapf(errorx.Err(errorx.DBError, err.Error()), "failed to count interface document with project_id[%s] and category_id[%s], error: %+v", in.GetProjectId(), in.GetCategoryId(), err)
	}
	resp.TotalCount = uint64(count)
	resp.TotalPage = 1

	documents, err := l.svcCtx.InterfaceDocumentModel.FindNoCacheByQuery(l.ctx, selectBuilder)
	if err != nil {
		return nil, errors.Wrapf(errorx.Err(errorx.DBError, err.Error()), "failed to find interface document with project_id[%s] and category_id[%s], error: %+v", in.GetProjectId(), in.GetCategoryId(), err)
	}

	resp.Items = make([]*pb.InterfaceDocument, 0, len(documents))
	for _, document := range documents {
		d := &pb.InterfaceDocument{}
		if err = utils.Copy(d, document, l.converters...); err != nil {
			return nil, errors.Wrapf(errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy interface document[%+v] to response, error: %+v", document, err)
		}

		// 搜索时不返回 `data`
		d.Data = nil
		resp.Items = append(resp.Items, d)
	}

	pagination := in.GetPagination()
	if pagination != nil {
		resp.CurrentPage = pagination.GetCurrentPage()
		resp.PageSize = pagination.GetPageSize()
		resp.TotalPage = uint64(math.Ceil(float64(resp.TotalCount) / float64(resp.PageSize)))
	}

	return resp, nil
}
