package interfacedefinitionservicelogic

import (
	"context"
	"database/sql"
	"fmt"
	"time"

	"github.com/hashicorp/go-multierror"
	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/mr"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/redislock"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type RemoveInterfaceConfigLogic struct {
	*BaseLogic
}

func NewRemoveInterfaceConfigLogic(ctx context.Context, svcCtx *svc.ServiceContext) *RemoveInterfaceConfigLogic {
	return &RemoveInterfaceConfigLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// RemoveInterfaceConfig 删除接口配置
func (l *RemoveInterfaceConfigLogic) RemoveInterfaceConfig(in *pb.RemoveInterfaceConfigReq) (
	resp *pb.RemoveInterfaceConfigResp, err error,
) {
	// validate the project_id in req
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, in.GetProjectId()); err != nil {
		return nil, err
	}

	// validate the document_id in req
	if _, err = model.CheckInterfaceDocumentByDocumentId(
		l.ctx, l.svcCtx.InterfaceDocumentModel, in.GetProjectId(), in.GetDocumentId(),
	); err != nil {
		return nil, err
	}

	configIds := stringx.Distinct(in.GetConfigIds())

	workers := len(configIds)
	if workers > common.ConstMRMaxWorkers {
		workers = common.ConstMRMaxWorkers
	}

	mr.ForEach(
		func(source chan<- any) {
			for _, configId := range configIds {
				source <- configId
			}
		}, func(item any) {
			configId, ok := item.(string)
			if !ok {
				err = multierror.Append(
					err,
					errorx.Err(errorx.InternalError, fmt.Sprintf("the interface config id[%v] is not a string", item)),
				)
			} else {
				if e := l.remove(in.GetProjectId(), in.GetDocumentId(), configId); e != nil {
					err = multierror.Append(err, e)
				}
			}
		}, mr.WithContext(l.ctx), mr.WithWorkers(workers),
	)

	return &pb.RemoveInterfaceConfigResp{}, err
}

func (l *RemoveInterfaceConfigLogic) remove(projectId, documentId, configId string) (err error) {
	// validate the config_id in req
	origin, err := model.CheckInterfaceConfigByConfigId(
		l.ctx, l.svcCtx.InterfaceConfigurationModel, projectId, documentId, configId,
	)
	if err != nil {
		return err
	}

	// acquire redis lock
	key := fmt.Sprintf(
		"%s:%s:%s:%s",
		common.ConstLockInterfaceConfigProjectIdDocumentIdConfigIdPrefix, projectId, documentId, configId,
	)
	lock, err := redislock.NewRedisLockAndAcquire(l.svcCtx.Redis, key, redislock.WithExpire(common.ConstLockExpireTime))
	if err != nil {
		return err
	}
	defer func() {
		// release redis lock
		e := lock.Release()
		if e != nil {
			l.Logger.Error(e)
			if re, ok := errorx.RootError(e); ok && re.Code() != errorx.ReleaseRedisLockFailure && err == nil {
				err = e
			}
		}
	}()

	origin.Deleted = int64(constants.HasDeleted)
	origin.DeletedAt = sql.NullTime{
		Time:  time.Now(),
		Valid: true,
	}
	origin.DeletedBy = sql.NullString{
		String: l.currentUser.Account,
		Valid:  true,
	}

	if _, err = l.svcCtx.InterfaceConfigurationModel.Update(l.ctx, nil, origin); err != nil {
		return errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()),
			"failed to update table[%s] with values[%+v], error: %+v",
			l.svcCtx.InterfaceConfigurationModel.Table(), origin, err,
		)
	}

	return nil
}
