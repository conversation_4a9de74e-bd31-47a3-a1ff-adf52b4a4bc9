package interfacedefinitionservicelogic

import (
	"context"
	"database/sql"
	"time"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type CreateInterfaceConfigLogic struct {
	*BaseLogic
}

func NewCreateInterfaceConfigLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CreateInterfaceConfigLogic {
	return &CreateInterfaceConfigLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// CreateInterfaceConfig 创建接口配置
func (l *CreateInterfaceConfigLogic) CreateInterfaceConfig(in *pb.CreateInterfaceConfigReq) (resp *pb.CreateInterfaceConfigResp, err error) {
	// validate the project_id in req
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, in.GetProjectId()); err != nil {
		return nil, err
	}

	// validate the document_id in req
	if _, err = model.CheckInterfaceDocumentByDocumentId(l.ctx, l.svcCtx.InterfaceDocumentModel, in.GetProjectId(), in.GetDocumentId()); err != nil {
		return nil, err
	}

	configId, err := l.generateConfigId(in.GetProjectId(), in.GetDocumentId())
	if err != nil {
		return nil, err
	}

	interfaceConfig := &model.InterfaceConfiguration{
		ProjectId:  in.GetProjectId(),
		DocumentId: in.GetDocumentId(),
		ConfigId:   configId,
		Name:       in.GetName(),
		Description: sql.NullString{
			String: in.GetDescription(),
			Valid:  in.GetDescription() != "",
		},
		Path:             in.GetPath(),
		Method:           in.GetMethod(),
		Data:             protobuf.MarshalJSONToStringIgnoreError(in.GetData()),
		InputParameters:  protobuf.MarshalJSONWithMessagesToStringIgnoreError(in.GetInputParameters()),
		OutputParameters: protobuf.MarshalJSONWithMessagesToStringIgnoreError(in.GetOutputParameters()),
		CreatedBy:        l.currentUser.Account,
		UpdatedBy:        l.currentUser.Account,
		CreatedAt:        time.Now(),
		UpdatedAt:        time.Now(),
	}

	if _, err = l.svcCtx.InterfaceConfigurationModel.Insert(l.ctx, nil, interfaceConfig); err != nil {
		return nil, errors.Wrapf(errorx.Err(errorx.DBError, err.Error()), "failed to insert table[%s] with values[%+v], error: %+v", l.svcCtx.InterfaceConfigurationModel.Table(), interfaceConfig, err)
	}

	resp = &pb.CreateInterfaceConfigResp{Config: &pb.InterfaceConfig{}}
	if err = utils.Copy(resp.Config, interfaceConfig, l.converters...); err != nil {
		return nil, errors.Wrapf(errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy interface config[%+v] to response, error: %+v", interfaceConfig, err)
	}

	return resp, nil
}
