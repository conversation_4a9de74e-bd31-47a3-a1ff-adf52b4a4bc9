package interfacedefinitionservicelogic

import (
	"context"
	"fmt"
	"sync/atomic"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/stores/sqlx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/logic/fsm"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

var _ fsm.Resource = (*ResourceOfInterfaceCase)(nil)

type (
	DocumentMap        = map[string]*model.InterfaceDocument                    // key: ProjectId:DocumentId or ProjectId:Name
	SchemaMap          = map[string]*model.InterfaceSchema                      // key: ProjectId:SchemaId or ProjectId:FullName
	SchemaRefRelMap    = map[string]*model.InterfaceSchemaReferenceRelationship // key: ProjectId:SchemaId
	SchemaRefRelMapMap = map[string]SchemaRefRelMap                             // key: ProjectId:ReferenceType:ReferenceId
	CoverageMap        = map[string]*coverageItem                               // key: team
)

type importInternalReq interface {
	*importDocumentInternalReq | *importSchemaInternalReq
}

type importDocumentInternalReq struct {
	types.DocumentRecorder

	ProjectId  string                               `json:"project_id"`
	CategoryId string                               `json:"category_id"`
	ImportType common.InterfaceDefinitionImportType `json:"import_type"`
}

type importSchemaInternalReq struct {
	types.SchemaRecorder

	ProjectId  string                               `json:"project_id"`
	CategoryId string                               `json:"category_id"`
	ImportType common.InterfaceDefinitionImportType `json:"import_type"`
}

type createSchemaReferenceInternalReq struct {
	ProjectId     string `json:"project_id"`
	ReferenceType string `json:"reference_type"`
	ReferenceId   string `json:"reference_id"`
	SchemaId      string `json:"schema_id"`
}

type createComponentGroupReferenceInternalReq struct {
	ProjectId string         `json:"project_id"`
	CaseId    string         `json:"case_id"`
	Version   string         `json:"version"`
	Relations []*pb.Relation `json:"relations"`
}

type createElementAndComponentInternalReq struct {
	ProjectId string      `json:"project_id"`
	CaseId    string      `json:"case_id"`
	Version   string      `json:"version"`
	Nodes     []*pb.Node  `json:"nodes"`
	Edges     []*pb.Edge  `json:"edges"`
	Combos    []*pb.Combo `json:"combos"`
}

type createElementInternalReq struct {
	ProjectId string     `json:"project_id"`
	CaseId    string     `json:"case_id"`
	Version   string     `json:"version"`
	Element   pb.Element `json:"element"`
}

type createComponentInternalReq struct {
	ProjectId string       `json:"project_id"`
	CaseId    string       `json:"case_id"`
	Version   string       `json:"version"`
	Component pb.Component `json:"component"`
}

type searchInternalReq struct {
	types.SearchByCategoryId

	DrillDown bool `json:"drill_down"`
}

type CreateInterfaceDocumentInternalReq struct {
	*pb.CreateInterfaceDocumentReq

	DocumentId string                               `json:"document_id"`
	Mode       common.InterfaceDefinitionCreateMode `json:"mode"`
	ImportType common.InterfaceDefinitionImportType `json:"import_type"`
}

type CreateInterfaceSchemaInternalReq struct {
	*pb.CreateInterfaceSchemaReq

	SchemaId   string                               `json:"schema_id"`
	Mode       common.InterfaceDefinitionCreateMode `json:"mode"`
	ImportType common.InterfaceDefinitionImportType `json:"import_type"`
}

type createApiPlanReferenceInternalReq struct {
	ProjectId  string `json:"project_id"`
	DocumentId string `json:"document_id"`
	CaseId     string `json:"case_id"`
}

type coverageItem struct {
	Team  string
	Apis  *atomic.Uint32
	Cases *atomic.Uint32
}

type ResourceOfInterfaceCase struct {
	*model.InterfaceCase

	Event  common.ReviewResourceEvent `json:"event"`
	Emails []string                   `json:"emails"`
	Result common.ReviewStatus        `json:"result"`
	Remark string                     `json:"remark"`
}

func (r *ResourceOfInterfaceCase) GenLockKey() string {
	return fmt.Sprintf(
		"%s:%s:%s:%s",
		common.ConstLockInterfaceCaseProjectIdDocumentIdCaseIdPrefix, r.ProjectID(), r.ResourceBranch(), r.ResourceID(),
	)
}

func (r *ResourceOfInterfaceCase) SetResourceState(state string) {
	r.InterfaceCase.State = state
}

func (r *ResourceOfInterfaceCase) SetResourceUpdatedBy(updatedBy string) {
	r.InterfaceCase.UpdatedBy = updatedBy
}

func (r *ResourceOfInterfaceCase) Refresh(ctx context.Context, svcCtx *svc.ServiceContext) error {
	interfaceCase, err := model.CheckInterfaceCaseByCaseId(
		ctx, svcCtx.InterfaceCaseModel, r.ProjectId, r.DocumentId, r.CaseId, "",
	)
	if err != nil {
		return err
	}

	r.InterfaceCase = interfaceCase
	return nil
}

func (r *ResourceOfInterfaceCase) Update(ctx context.Context, svcCtx *svc.ServiceContext, session sqlx.Session) error {
	if _, err := svcCtx.InterfaceCaseModel.UpdateTX(ctx, session, r.InterfaceCase); err != nil {
		return errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()),
			"failed to update table[%s] with values[%+v], error: %+v",
			svcCtx.InterfaceCaseModel.Table(), r.InterfaceCase, err,
		)
	}

	return nil
}

func (r *ResourceOfInterfaceCase) ReviewEvent() common.ReviewResourceEvent {
	return r.Event
}

func (r *ResourceOfInterfaceCase) Receivers() []string {
	return r.Emails
}

func (r *ResourceOfInterfaceCase) ReviewResult() common.ReviewStatus {
	return r.Result
}

func (r *ResourceOfInterfaceCase) ReviewRemark() string {
	return r.Remark
}
