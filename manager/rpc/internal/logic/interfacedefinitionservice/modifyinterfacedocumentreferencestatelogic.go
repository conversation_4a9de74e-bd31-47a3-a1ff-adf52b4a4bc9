package interfacedefinitionservicelogic

import (
	"context"
	"fmt"

	"github.com/emirpasic/gods/sets/hashset"
	"github.com/hashicorp/go-multierror"
	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/mr"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/redislock"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type ModifyInterfaceDocumentReferenceStateLogic struct {
	*BaseLogic
}

func NewModifyInterfaceDocumentReferenceStateLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ModifyInterfaceDocumentReferenceStateLogic {
	return &ModifyInterfaceDocumentReferenceStateLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// ModifyInterfaceDocumentReferenceState 修改接口集合所在的API计划的引用状态
func (l *ModifyInterfaceDocumentReferenceStateLogic) ModifyInterfaceDocumentReferenceState(in *pb.ModifyInterfaceDocumentReferenceStateReq) (resp *pb.ModifyInterfaceDocumentReferenceStateResp, err error) {
	// validate the project_id in req
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, in.GetProjectId()); err != nil {
		return nil, err
	}

	// validate the document_id in req
	document, err := model.CheckInterfaceDocumentByDocumentId(l.ctx, l.svcCtx.InterfaceDocumentModel, in.GetProjectId(), in.GetDocumentId())
	if err != nil {
		return nil, err
	}

	rrs, err := l.svcCtx.ApiPlanReferenceModel.FindReferenceByReference(l.ctx, in.GetProjectId(), common.ConstReferenceTypeInterfaceDocument, in.GetDocumentId(), false)
	if err != nil {
		if err != model.ErrNotFound {
			return nil, errors.Wrapf(errorx.Err(errorx.DBError, err.Error()), "failed to find api plan containing the specified interface document with project_id[%s] and document_id[%s], error: %+v", in.GetProjectId(), in.GetDocumentId(), err)
		}
		return nil, errors.WithStack(errorx.Err(errorx.ProhibitedBehavior, fmt.Sprintf("cannot modify the reference state of interface document[%s] which is not in any api plan", document.Name)))
	}

	recorder := make(map[string]*model.ApiPlanReferenceRelationship)
	allSet := hashset.New()
	for _, rr := range rrs {
		rr := rr
		allSet.Add(rr.PlanId)
		recorder[rr.PlanId] = rr
	}

	modifySet := hashset.New()
	for _, planId := range in.PlanIds {
		modifySet.Add(planId)
	}

	diffSet := modifySet.Difference(allSet)
	if !diffSet.Empty() {
		return nil, errors.WithStack(errorx.Err(errorx.ProhibitedBehavior, fmt.Sprintf("cannot modify the reference state of interface document[%s] which is not in the api plans %s", document.Name, logic.ContainerToString(diffSet))))
	}

	// acquire redis lock
	key := fmt.Sprintf("%s:%s:%s", common.ConstLockInterfaceDocumentProjectIdDocumentIdPrefix, in.GetProjectId(), in.GetDocumentId())
	lock, err := redislock.NewRedisLockAndAcquire(l.svcCtx.Redis, key, redislock.WithExpire(common.ConstLockExpireTime))
	if err != nil {
		return nil, err
	}
	defer func() {
		// release redis lock
		e := lock.Release()
		if e != nil {
			l.Logger.Error(e)
			if re, ok := errorx.RootError(e); ok && re.Code() != errorx.ReleaseRedisLockFailure && err == nil {
				err = e
			}
		}
	}()

	workers := modifySet.Size()
	if workers > common.ConstMRMaxWorkers {
		workers = common.ConstMRMaxWorkers
	}

	mr.ForEach(func(source chan<- any) {
		for _, v := range modifySet.Values() {
			source <- v
		}
	}, func(item any) {
		if planId, ok := item.(string); !ok {
			err = multierror.Append(err, errors.Wrapf(errorx.ErrGrpcInternal, "the api plan id[%v (%T)] is not a string", item, item))
		} else if reference, ok := recorder[planId]; !ok {
			err = multierror.Append(err, errors.Wrapf(errorx.ErrGrpcInternal, "cannot find the reference of api plan[%s]", planId))
		} else {
			// 当前的引用状态跟目标不一样时才需要修改
			state := in.GetState()
			if reference.State != int64(state) {
				reference.State = int64(state)

				if e := l.modify(document, reference); e != nil {
					err = multierror.Append(err, e)
				}
			}
		}
	}, mr.WithContext(l.ctx), mr.WithWorkers(workers))

	return &pb.ModifyInterfaceDocumentReferenceStateResp{}, err
}

func (l *ModifyInterfaceDocumentReferenceStateLogic) modify(document *model.InterfaceDocument, reference *model.ApiPlanReferenceRelationship) (err error) {
	apiPlan, err := model.CheckApiPlanByPlanId(l.ctx, l.svcCtx.ApiPlanModel, reference.ProjectId, reference.PlanId)
	if err != nil {
		return err
	}

	// acquire redis lock
	key := fmt.Sprintf("%s:%s:%s", common.ConstLockApiPlanProjectIdPlanIdPrefix, apiPlan.ProjectId, apiPlan.PlanId)
	lock, err := redislock.NewRedisLockAndAcquire(l.svcCtx.Redis, key, redislock.WithExpire(common.ConstLockExpireTime))
	if err != nil {
		return err
	}
	defer func() {
		// release redis lock
		e := lock.Release()
		if e != nil {
			// l.Logger.Error(e)
			if re, ok := errorx.RootError(e); ok && re.Code() != errorx.ReleaseRedisLockFailure && err == nil {
				err = e
			}
		}
	}()

	if _, err = l.svcCtx.ApiPlanReferenceModel.Update(l.ctx, nil, reference); err != nil {
		err = errors.Wrapf(errorx.Err(errorx.DBError, err.Error()), "failed to update the reference state of interface document[%s] which is in the api plan[%s], error: %+v", document.Name, apiPlan.Name, err)
	}

	return
}
