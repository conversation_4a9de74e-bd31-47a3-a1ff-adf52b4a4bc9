package interfacedefinitionservicelogic

import (
	"context"
	"database/sql"
	"fmt"
	"net/http"
	"path/filepath"
	"sync"
	"time"

	"github.com/emirpasic/gods/sets/hashset"
	"github.com/go-git/go-git/v5/plumbing/object"
	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"
	"github.com/zeromicro/go-zero/core/lang"
	"github.com/zeromicro/go-zero/core/mr"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"go.uber.org/atomic"

	qetconstants "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/redislock"
	qetutils "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/codes"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/utils"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/logic"
	categoryservicelogic "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/logic/categoryservice"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/logic/interfacedefinitionservice/loader"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type LocalImportInterfaceDefinitionLogic struct {
	*BaseLogic

	loader loader.Loader
	dStat  *common.Statistic // interface document statistic
	sStat  *common.Statistic // interface schema statistic

	dIndexMap sync.Map // index of document category tree
	sIndexMap sync.Map // index of schema category tree

	dOnce sync.Once
	sOnce sync.Once

	dIdMap   DocumentMap        // document id map
	dNameMap DocumentMap        // document name map
	sIdMap   SchemaMap          // schema id map
	sNameMap SchemaMap          // schema full_name map
	rrMap    SchemaRefRelMapMap // schema reference relationship map
}

func NewLocalImportInterfaceDefinitionLogic(
	ctx context.Context, svcCtx *svc.ServiceContext,
) *LocalImportInterfaceDefinitionLogic {
	return &LocalImportInterfaceDefinitionLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
		dStat:     common.NewStatistic(),
		sStat:     common.NewStatistic(),
	}
}

// LocalImportInterfaceDefinition 本地导入接口定义
func (l *LocalImportInterfaceDefinitionLogic) LocalImportInterfaceDefinition(in *pb.LocalImportInterfaceDefinitionReq) (
	resp *pb.LocalImportInterfaceDefinitionResp, err error,
) {
	// acquire redis lock
	key := fmt.Sprintf("%s:%s", common.ConstLockImportInterfaceDefinitionProjectIDPrefix, in.GetProjectId())
	lock, err := redislock.NewRedisLockAndAcquire(
		l.svcCtx.Redis, key, redislock.WithExpire(common.ConstLockImportInterfaceDefinitionExpireTime),
	)
	if err != nil {
		return nil, err
	}
	defer func() {
		// release redis lock
		e := lock.Release()
		if e != nil {
			if re, ok := errorx.RootError(e); ok && re.Code() != errorx.ReleaseRedisLockFailure && err == nil {
				err = e
			}
		}
	}()

	switch in.GetType() {
	case common.ConstInterfaceDefinitionImportTypeOpenApi,
		common.ConstInterfaceDefinitionImportTypeGrpc,
		common.ConstInterfaceDefinitionImportTypeProbe,
		common.ConstInterfaceDefinitionImportTypeYApi:
		err = errorx.Errorf(
			errorx.DoesNotSupport, "the interface definition import type[%s] is not yet supported", in.GetType(),
		)
	case common.ConstInterfaceDefinitionImportTypeTT,
		common.ConstInterfaceDefinitionImportTypeTTMeta,
		common.ConstInterfaceDefinitionImportTypeRecommend:
		err = l.importGRPCInterfaceDefinition(in)
	default:
		err = errorx.Errorf(
			errorx.DoesNotSupport, "the interface definition import type[%s] is unknown, please check", in.GetType(),
		)
	}

	if err != nil {
		return nil, err
	}

	return &pb.LocalImportInterfaceDefinitionResp{
		Document: convertStatistic(l.dStat),
		Schema:   convertStatistic(l.sStat),
	}, nil
}

func (l *LocalImportInterfaceDefinitionLogic) importGRPCInterfaceDefinition(req *pb.LocalImportInterfaceDefinitionReq) error {
	var (
		projectID = req.GetProjectId()
		tp        = req.GetType()
	)

	err := l.initGRPCLoader(req)
	if err != nil {
		return err
	}

	err = l.generateSchemaRefRelMap(projectID)
	if err != nil {
		return err
	}

	documentCategory, documentChildrenCategories, err := l.getCategoriesByType(
		projectID, common.ConstInterfaceDefinitionTypeDocument,
	)
	if err != nil {
		return err
	}

	schemaCategory, schemaChildrenCategories, err := l.getCategoriesByType(
		projectID, common.ConstInterfaceDefinitionTypeSchema,
	)
	if err != nil {
		return err
	}

	return mr.MapReduceVoid(
		l.mrGenerateFunc(
			projectID, tp, documentCategory, documentChildrenCategories, schemaCategory, schemaChildrenCategories,
		),
		l.mrMapperFunc(),
		func(pipe <-chan any, cancel func(error)) {},
		mr.WithContext(l.ctx), mr.WithWorkers(common.ConstMRMaxWorkers),
	)
}

func (l *LocalImportInterfaceDefinitionLogic) checkLoadType(
	tp string, target *pb.LocalImportInterfaceDefinitionReq_Target,
) (
	loadType string, err error,
) {
	if target.GetGitUrl() != "" {
		if !qetutils.IsValidGitUrl(target.GetGitUrl()) {
			return common.InterfaceDefinitionLoadTypeError, errorx.Errorf(
				errorx.DoesNotSupport,
				"import %q interface definitions can be imported from a git repository, but the url[%s] is not a git repository",
				tp, target.GetGitUrl(),
			)
		}

		return common.InterfaceDefinitionLoadTypeGit, nil
	} else if target.GetPath() != "" {
		if !qetutils.IsDir(target.GetPath()) {
			return common.InterfaceDefinitionLoadTypeError, errorx.Errorf(
				errorx.DoesNotSupport,
				"import %q interface definitions can be imported from a directory, but the path[%s] is not a directory",
				tp, target.GetPath(),
			)
		}

		return common.InterfaceDefinitionLoadTypeLocal, nil
	} else {
		return common.InterfaceDefinitionLoadTypeError, errorx.Errorf(
			errorx.DoesNotSupport,
			"import %q interface definitions can be imported from a git repository or directory, but got nothing",
			tp,
		)
	}
}

func (l *LocalImportInterfaceDefinitionLogic) cloneOrPull(gitURL, branch, path string) error {
	var (
		commit *object.Commit
		err    error
	)

	if qetutils.Exists(path) {
		commit, err = qetutils.PullWithContext(l.ctx, path)
	} else {
		commit, err = qetutils.CloneWithContext(l.ctx, gitURL, path, branch)
	}
	if err != nil {
		return errors.Wrapf(
			errorx.Err(errorx.GitOperationFailure, err.Error()),
			"failed to clone or pull the git repo, git: %s, branch: %s, path: %s, error: %+v",
			gitURL, branch, path, err,
		)
	}

	l.Infof(
		"finish to clone or pull the git repo, git: %s, branch: %s, path: %s, commit: %s",
		gitURL, branch, path, commit.String(),
	)
	return nil
}

func (l *LocalImportInterfaceDefinitionLogic) getProtoProduct(req *pb.LocalImportInterfaceDefinitionReq) (
	product protobuf.Product, err error,
) {
	var (
		projectID    = req.GetProjectId()
		tp           = req.GetType()
		target       = req.GetTarget()
		dependencies = req.GetDependencies()

		basePath, rootPath string
	)

	basePath = filepath.Join(l.svcCtx.Config.PVCPath, defaultLocalImportDir, projectID)

	loadType, err := l.checkLoadType(tp, target)
	if err != nil {
		return product, err
	}

	switch loadType {
	case common.InterfaceDefinitionLoadTypeLocal:
		rootPath = target.GetPath()
	case common.InterfaceDefinitionLoadTypeGit:
		rootPath = filepath.Join(basePath, tp)
		if err = l.cloneOrPull(target.GetGitUrl(), target.GetBranch(), rootPath); err != nil {
			return product, err
		}
	default:
		return product, errorx.Errorf(errorx.DoesNotSupport, "unsupport load type: %s", loadType)
	}

	depPaths := make([]string, 0, len(dependencies))
	for _, dep := range dependencies {
		loadType, err = l.checkLoadType(tp, dep)
		if err != nil {
			return product, err
		}

		var path string
		switch loadType {
		case common.InterfaceDefinitionLoadTypeLocal:
			path = dep.GetPath()
		case common.InterfaceDefinitionLoadTypeGit:
			name, err := getNameFromGitURL(dep.GetGitUrl())
			if err != nil {
				return product, errors.Errorf(
					"failed to get the name from git url, git: %s, error: %+v", dep.GetGitUrl(), err,
				)
			}

			path = filepath.Join(basePath, name)
			if err = l.cloneOrPull(dep.GetGitUrl(), dep.GetBranch(), path); err != nil {
				return product, err
			}
		default:
			continue
		}

		if path != "" {
			depPaths = append(depPaths, path)
		}
	}

	return protobuf.Product{
		Name:   tp,
		Branch: "",
		Projects: []protobuf.Project{
			{
				Name:        filepath.Base(rootPath),
				Branch:      target.GetBranch(),
				Path:        rootPath,
				ImportPaths: depPaths,
			},
		},
	}, nil
}

func (l *LocalImportInterfaceDefinitionLogic) initGRPCLoader(req *pb.LocalImportInterfaceDefinitionReq) error {
	// `T次元`项目依赖下列`protobuf`项目
	// https://github.com/googleapis/googleapis
	// https://github.com/bufbuild/protovalidate/tree/main/proto/protovalidate
	//
	// `推荐`项目依赖下列`protobuf`项目
	// https://github.com/googleapis/googleapis
	// https://github.com/bufbuild/protoc-gen-validate
	product, err := l.getProtoProduct(req)
	if err != nil {
		return err
	}

	l.loader = loader.NewGRPCLoader(
		product, loader.WithGenDocumentIDFunc(
			func(name string, cache types.Recorders) string {
				if err := l.generateDocumentMaps(req.GetProjectId()); err != nil {
					return ""
				}

				documentID, err := l.generateDocumentId(req.GetProjectId(), name, cache)
				if err != nil {
					return ""
				}

				return documentID
			},
		), loader.WithGenSchemaIDFunc(
			func(name string, cache types.Recorders) string {
				if err := l.generateSchemaMaps(req.GetProjectId()); err != nil {
					return ""
				}

				schemaID, err := l.generateSchemaId(req.GetProjectId(), name, cache)
				if err != nil {
					return ""
				}

				return schemaID
			},
		),
	)

	err = l.loader.Init()
	if err != nil {
		return errors.Wrapf(
			errorx.Err(codes.ParseInterfaceFileFailure, err.Error()),
			"failed to parse %q interface definition files, target: %s, dependencies: %s, error: %+v",
			req.GetType(), protobuf.MarshalJSONIgnoreError(req.GetTarget()),
			protobuf.MarshalJSONIgnoreError(req.GetDependencies()), err,
		)
	}

	return nil
}

// generateDocumentMaps 为了减少数据库的操作，把数据都缓存到内存
func (l *LocalImportInterfaceDefinitionLogic) generateDocumentMaps(projectId string) error {
	var err error

	l.dOnce.Do(
		func() {
			var rs []*model.InterfaceDocument

			rs, err = l.svcCtx.InterfaceDocumentModel.FindAll(l.ctx, projectId)
			if err != nil && !errors.Is(model.ErrNotFound, err) {
				err = errors.Wrapf(
					errorx.Err(errorx.DBError, err.Error()),
					"failed to find the all document with project_id[%s], error: %+v",
					projectId, err,
				)
				return
			}

			l.dIdMap = make(DocumentMap, len(rs))
			l.dNameMap = make(DocumentMap, len(rs))

			for _, r := range rs {
				l.dIdMap[fmt.Sprintf("%s:%s:%s", ConstMapKeyProjectIdDocumentId, r.ProjectId, r.DocumentId)] = r
				l.dNameMap[fmt.Sprintf(
					"%s:%s:%s:%s", ConstMapKeyProjectIdDocumentServiceDocumentName, r.ProjectId, r.Service.String,
					r.Name,
				)] = r
			}
		},
	)

	return err
}

// generateSchemaMaps 为了减少数据库的操作，把数据都缓存到内存
func (l *LocalImportInterfaceDefinitionLogic) generateSchemaMaps(projectId string) error {
	var err error

	l.sOnce.Do(
		func() {
			var rs []*model.InterfaceSchema

			rs, err = l.svcCtx.InterfaceSchemaModel.FindAll(l.ctx, projectId)
			if err != nil && !errors.Is(model.ErrNotFound, err) {
				err = errors.Wrapf(
					errorx.Err(errorx.DBError, err.Error()),
					"failed to find the all schema with project_id[%s], error: %+v",
					projectId, err,
				)
				return
			}

			l.sIdMap = make(SchemaMap, len(rs))
			l.sNameMap = make(SchemaMap, len(rs))

			for _, r := range rs {
				l.sIdMap[fmt.Sprintf("%s:%s:%s", ConstMapKeyProjectIdSchemaId, r.ProjectId, r.SchemaId)] = r
				l.sNameMap[fmt.Sprintf("%s:%s:%s", ConstMapKeyProjectIdSchemaFullName, r.ProjectId, r.FullName)] = r
			}
		},
	)

	return err
}

func (l *LocalImportInterfaceDefinitionLogic) generateDocumentId(projectId, name string, cache types.Recorders) (
	string, error,
) {
	d, ok := l.dNameMap[fmt.Sprintf("%s:%s:%s", ConstMapKeyProjectIdDocumentServiceDocumentName, projectId, name)]
	if !ok {
		g := qetutils.NewUniqueIdGenerator(
			qetutils.WithGenerateFunc(utils.GenInterfaceDocumentId), qetutils.WithIsUniqueFunc(
				func(id string) bool {
					_, ok = l.dIdMap[fmt.Sprintf("%s:%s:%s", ConstMapKeyProjectIdDocumentId, projectId, id)]
					if ok {
						// 本次生成的 `document_id` 已存在于数据库，需要重新创建
						return false
					}
					if cache != nil {
						if _, ok = cache[id]; ok {
							// 本次生成的 `document_id` 已存在于记录器中，需要重新创建
							return false
						}
						cache[id] = lang.Placeholder
					}
					return true
				},
			),
		)
		documentId := g.Next()
		if documentId == "" {
			return "", errors.WithStack(
				errorx.Err(
					errorx.GenerateUniqueIdFailure, "failed to generate document id, please try it later",
				),
			)
		}

		return documentId, nil
	}

	// 当前的接口文档名称已存在于数据库，则沿用该 `document_id`
	return d.DocumentId, nil
}

func (l *LocalImportInterfaceDefinitionLogic) generateSchemaId(
	projectId, fullName string, cache types.Recorders,
) (string, error) {
	s, ok := l.sNameMap[fmt.Sprintf("%s:%s:%s", ConstMapKeyProjectIdSchemaFullName, projectId, fullName)]
	if !ok {
		g := qetutils.NewUniqueIdGenerator(
			qetutils.WithGenerateFunc(utils.GenInterfaceSchemaId), qetutils.WithIsUniqueFunc(
				func(id string) bool {
					_, ok = l.sIdMap[fmt.Sprintf("%s:%s:%s", ConstMapKeyProjectIdSchemaId, projectId, id)]
					if ok {
						// 本次生成的 `schema_id` 已存在于数据库，需要重新创建
						return false
					}
					if cache != nil {
						if _, ok = cache[id]; ok {
							// 本次生成的 `schema_id` 已存在于记录器中，需要重新创建
							return false
						}
						cache[id] = lang.Placeholder
					}
					return true
				},
			),
		)
		schemaId := g.Next()
		if schemaId == "" {
			return "", errors.WithStack(
				errorx.Err(
					errorx.GenerateUniqueIdFailure, "failed to generate schema id, please try it later",
				),
			)
		}

		return schemaId, nil
	}

	// 当前的接口数据模型完整名称已存在于数据库，则沿用该 `schema_id`
	return s.SchemaId, nil
}

// generateSchemaRefRelMap 为了减少数据库的操作，把数据都缓存到内存
func (l *LocalImportInterfaceDefinitionLogic) generateSchemaRefRelMap(projectId string) error {
	var (
		rrs []*model.InterfaceSchemaReferenceRelationship
		err error
	)

	rrs, err = l.svcCtx.InterfaceSchemaReferenceModel.FindAll(l.ctx, projectId)
	if err != nil && !errors.Is(err, model.ErrNotFound) {
		err = errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()),
			"failed to find the all schema reference relationship with project_id[%s], error: %+v",
			projectId, err,
		)
	} else {
		l.rrMap = make(SchemaRefRelMapMap, len(rrs))

		for _, rr := range rrs {
			key1 := fmt.Sprintf(
				"%s:%s:%s:%s", ConstMapKeyProjectIdReferenceTypeReferenceId, projectId, rr.ReferenceType,
				rr.ReferenceId,
			)
			key2 := fmt.Sprintf("%s:%s:%s", ConstMapKeyProjectIdSchemaId, projectId, rr.SchemaId)
			if m, ok := l.rrMap[key1]; ok {
				m[key2] = rr
			} else {
				l.rrMap[key1] = SchemaRefRelMap{key2: rr}
			}
		}
	}

	return err
}

func (l *LocalImportInterfaceDefinitionLogic) mrGenerateFunc(
	projectId, importType string, documentCategory *model.Category, documentChildrenCategories []*model.Category,
	schemaCategory *model.Category, schemaChildrenCategories []*model.Category,
) mr.GenerateFunc[any] {
	return func(source chan<- any) {
		var (
			documentServiceCategoryId, schemaServiceCategoryId   string
			documentServiceChildrenNum, schemaServiceChildrenNum int64
			err                                                  error
		)

		l.loader.RangeServices(
			func(serviceName string, documents []types.DocumentRecorder) bool {
				// 设置 `接口文档` 和 `数据模型` 的总数
				dTotal := int64(len(documents))
				l.dStat.Total.Add(dTotal)

				// 「文档」分类数下创建「服务」目录
				documentServiceCategoryId, documentServiceChildrenNum, err = l.createServiceCategory(
					projectId, common.ConstCategoryTreeTypeInterfaceDocument, documentCategory.CategoryId, serviceName,
					common.ConstCategoryRootAllDocument, documentChildrenCategories,
				)
				if err != nil {
					// 创建「服务」目录失败，该服务下的接口都不被导入
					l.Errorf(
						"failed to create document service category[%s] with project_id[%s], error: %+v",
						serviceName, projectId, err,
					)
					l.dStat.Skipped.Add(dTotal)
				} else {
					l.dIndexMap.LoadOrStore(documentServiceCategoryId, atomic.NewInt64(documentServiceChildrenNum))

					for _, document := range documents {
						source <- &importDocumentInternalReq{
							DocumentRecorder: document,
							ProjectId:        projectId,
							CategoryId:       documentServiceCategoryId,
							ImportType:       importType,
						}
					}
				}
				return true
			},
		)
		l.loader.RangeFiles(
			func(fileName string, schemas []types.SchemaRecorder) bool {
				sTotal := int64(len(schemas))
				l.sStat.Total.Add(sTotal)
				// 「数据模型」分类数下创建「文件」目录
				schemaServiceCategoryId, schemaServiceChildrenNum, err = l.createServiceCategory(
					projectId, common.ConstCategoryTreeTypeInterfaceSchema, schemaCategory.CategoryId, fileName,
					common.ConstCategoryRootAllSchema, schemaChildrenCategories,
				)
				if err != nil {
					// 创建「文件」目录失败，该服务下的数据模型都不被导入
					l.Errorf(
						"failed to create schema file category[%s] with project_id[%s], error: %+v", fileName,
						projectId, err,
					)
					l.sStat.Skipped.Add(sTotal)
				} else {
					l.sIndexMap.LoadOrStore(schemaServiceCategoryId, atomic.NewInt64(schemaServiceChildrenNum))

					for _, schema := range schemas {
						source <- &importSchemaInternalReq{
							SchemaRecorder: schema,
							ProjectId:      projectId,
							CategoryId:     schemaServiceCategoryId,
							ImportType:     importType,
						}
					}
				}

				return true
			},
		)
	}
}

func (l *LocalImportInterfaceDefinitionLogic) mrMapperFunc() mr.MapperFunc[any, any] {
	return func(item any, writer mr.Writer[any], cancel func(error)) {
		var (
			stat *common.Statistic
			ot   model.OperationType
			err  error
		)

		// 一个文档或者数据模型导入失败，将不影响其它继续导入
		switch v := item.(type) {
		case *importDocumentInternalReq:
			stat = l.dStat
			ot, err = l.createInterfaceDocument(v)
			if err != nil {
				l.Errorf("failed to create interface document, error: %+v", err)
			}
		case *importSchemaInternalReq:
			stat = l.sStat
			ot, err = l.createInterfaceSchema(v)
			if err != nil {
				l.Errorf("failed to create interface schema, error: %+v", err)
			}
		default:
			l.Warnf("the type[%T] of item is not currently supported", v)
		}

		if stat != nil {
			if err != nil {
				ot = model.ConstOperationTypeError
			}

			switch ot {
			case model.ConstOperationTypeError:
				stat.Failure.Inc()
			case model.ConstOperationTypeInsert:
				stat.Increased.Inc()
			case model.ConstOperationTypeUpdate:
				stat.Modified.Inc()
			default:
				l.Warnf("unsupported operation type: %d", ot)
			}
		}
	}
}

func (l *LocalImportInterfaceDefinitionLogic) createServiceCategory(
	projectId, tp, categoryId, serviceName, rootType string, childrenCategories []*model.Category,
) (serviceCategoryId string, childrenNum int64, err error) {
	serviceCategory := findServiceCategory(serviceName, childrenCategories)
	if serviceCategory == nil {
		category, e := l.createCategoryLogic.CreateCategoryForInternal(
			l.ctx, nil, categoryservicelogic.CreateCategoryInternalReq{
				CreateCategoryReq: &pb.CreateCategoryReq{
					ProjectId: projectId,
					Type:      tp,
					Name:      serviceName,
					ParentId:  categoryId,
				},
				CategoryType:   common.ConstCategoryTypeDirectory,
				RootType:       rootType,
				IsInternalCall: true,
			},
		)
		if e != nil {
			err = e
		} else {
			serviceCategoryId = category.CategoryId
		}
	} else {
		serviceCategoryId = serviceCategory.CategoryId
		if fn, e := logic.GetCountFunctionByCategoryTreeType(l.ctx, l.svcCtx, tp); e == nil {
			childrenNum, _ = fn(projectId, categoryId, false)
		}
	}

	return serviceCategoryId, childrenNum, err
}

func (l *LocalImportInterfaceDefinitionLogic) createInterfaceDocument(req *importDocumentInternalReq) (
	ot model.OperationType, err error,
) {
	var (
		callPath        string
		documentMarshal func(document types.DocumentRecorder) string
	)
	user := l.currentUser
	if user == nil {
		user = &systemUser.TokenUserInfo
	}

	switch req.ImportType {
	case common.ConstInterfaceDefinitionImportTypeTT,
		common.ConstInterfaceDefinitionImportTypeRecommend:
		callPath = ConstApiProxyCommonApiCallPath
		documentMarshal = marshalApiProxyDocument
	case common.ConstInterfaceDefinitionImportTypeTTMeta:
		callPath = ConstTTMetaClientCommonCallPath
		documentMarshal = marshalTTMetaClientDocument
	}

	// create interface document in a transaction
	err = l.svcCtx.InterfaceDocumentModel.Trans(
		l.ctx, func(context context.Context, session sqlx.Session) error {
			document := &model.InterfaceDocument{
				ProjectId:  req.ProjectId,
				CategoryId: req.CategoryId,
				DocumentId: req.DocumentId,
				Name:       req.ApiName,
				Type:       common.ConstInterfaceTypeHttp,
				Mode:       common.ConstInterfaceDefinitionCreateModeLocal,
				ImportType: sql.NullString{
					String: req.ImportType,
					Valid:  req.ImportType != "",
				},
				Status:            int64(common.ConstInterfaceDefinitionStatusNone),
				State:             int64(qetconstants.EnableStatus),
				CaseExecutionMode: int64(constants.Parallel),
				Service: sql.NullString{
					String: req.Service,
					Valid:  req.Service != "",
				},
				Path:      callPath,
				Method:    http.MethodPost,
				Data:      documentMarshal(req.DocumentRecorder),
				CreatedBy: user.Account,
				UpdatedBy: user.Account,
			}

			// create or update the interface document
			ot, err = l.insertOrUpdateDocument(context, session, document)
			if err != nil {
				return err
			}

			if ot == model.ConstOperationTypeInsert {
				v, ok := l.dIndexMap.Load(req.CategoryId)
				if !ok {
					return errors.Errorf(
						"the category[%s] of document[%s] is not found", req.CategoryId, req.DocumentId,
					)
				}
				i, ok := v.(*atomic.Int64)
				if !ok {
					return errors.Errorf(
						"the index of category[%s] expected be an *atomic.Int64, but got %T", req.CategoryId, v,
					)
				}

				// create a category in the interface definition category tree
				if _, err = l.createCategoryLogic.CreateCategoryForInternal(
					context, session, categoryservicelogic.CreateCategoryInternalReq{
						CreateCategoryReq: &pb.CreateCategoryReq{
							ProjectId: req.ProjectId,
							Type:      common.ConstCategoryTreeTypeInterfaceDocument,
							Name:      req.ApiName,
							ParentId:  req.CategoryId,
							Index:     i.Inc(),
						},
						CategoryType:   common.ConstCategoryTypeFile,
						RootType:       common.GetRootTypeByNodeType(common.ConstInterfaceDefinitionTypeDocument),
						NodeType:       common.ConstInterfaceDefinitionTypeDocument,
						NodeId:         req.DocumentId,
						IsInternalCall: true,
					},
				); err != nil {
					return err
				}
			}

			cm := make(map[string]*createSchemaReferenceInternalReq)
			if req.ReqSchema != nil {
				// create reference relationship of document and request schema
				cm[fmt.Sprintf(
					"%s:%s:%s", ConstMapKeyProjectIdSchemaId, req.ProjectId, req.ReqSchema.SchemaId,
				)] = &createSchemaReferenceInternalReq{
					ProjectId:     req.ProjectId,
					ReferenceType: common.ConstInterfaceDefinitionTypeDocument,
					ReferenceId:   req.DocumentId,
					SchemaId:      req.ReqSchema.SchemaId,
				}
			}

			if req.RespSchema != nil {
				// create reference relationship of document and response schema
				cm[fmt.Sprintf(
					"%s:%s:%s", ConstMapKeyProjectIdSchemaId, req.ProjectId, req.RespSchema.SchemaId,
				)] = &createSchemaReferenceInternalReq{
					ProjectId:     req.ProjectId,
					ReferenceType: common.ConstInterfaceDefinitionTypeDocument,
					ReferenceId:   req.DocumentId,
					SchemaId:      req.RespSchema.SchemaId,
				}
			}

			return l.updateReference(
				context, session, cm,
				l.getSchemaRefRelMap(req.ProjectId, common.ConstInterfaceDefinitionTypeDocument, req.DocumentId),
			)
		},
	)

	return ot, err
}

func (l *LocalImportInterfaceDefinitionLogic) createInterfaceSchema(req *importSchemaInternalReq) (
	ot model.OperationType, err error,
) {
	user := l.currentUser
	if user == nil {
		user = &systemUser.TokenUserInfo
	}

	// create interface schema in a transaction
	err = l.svcCtx.InterfaceSchemaModel.Trans(
		l.ctx, func(context context.Context, session sqlx.Session) error {
			schema := &model.InterfaceSchema{
				ProjectId:  req.ProjectId,
				CategoryId: req.CategoryId,
				SchemaId:   req.SchemaId,
				FullName:   req.FullName,
				Name:       req.Name,
				Mode:       common.ConstInterfaceDefinitionCreateModeLocal,
				ImportType: sql.NullString{
					String: req.ImportType,
					Valid:  req.ImportType != "",
				},
				Data:      jsonx.MarshalToStringIgnoreError(req.Data),
				CreatedBy: user.Account,
				UpdatedBy: user.Account,
			}

			// create or update the interface schema
			ot, err = l.insertOrUpdateSchema(context, session, schema)
			if err != nil {
				return err
			}

			if ot == model.ConstOperationTypeInsert {
				v, ok := l.sIndexMap.Load(req.CategoryId)
				if !ok {
					return errors.Errorf(
						"the category[%s] of schema[%s] is not found", req.CategoryId, req.SchemaId,
					)
				}
				i, ok := v.(*atomic.Int64)
				if !ok {
					return errors.Errorf(
						"the index of category[%s] expected be an *atomic.Int64, but got %T", req.CategoryId, v,
					)
				}

				// create a category in the interface definition category tree
				if _, err = l.createCategoryLogic.CreateCategoryForInternal(
					context, session, categoryservicelogic.CreateCategoryInternalReq{
						CreateCategoryReq: &pb.CreateCategoryReq{
							ProjectId: req.ProjectId,
							Type:      common.ConstCategoryTreeTypeInterfaceSchema,
							Name:      req.Name,
							ParentId:  req.CategoryId,
							Index:     i.Inc(),
						},
						CategoryType:   common.ConstCategoryTypeFile,
						RootType:       common.GetRootTypeByNodeType(common.ConstInterfaceDefinitionTypeSchema),
						NodeType:       common.ConstInterfaceDefinitionTypeSchema,
						NodeId:         req.SchemaId,
						IsInternalCall: true,
					},
				); err != nil {
					return err
				}
			}

			cm := make(map[string]*createSchemaReferenceInternalReq, len(req.References))
			for _, sr := range req.References {
				cm[fmt.Sprintf(
					"%s:%s:%s", ConstMapKeyProjectIdSchemaId, req.ProjectId, sr.SchemaId,
				)] = &createSchemaReferenceInternalReq{
					ProjectId:     req.ProjectId,
					ReferenceType: common.ConstInterfaceDefinitionTypeSchema,
					ReferenceId:   req.SchemaId,
					SchemaId:      sr.SchemaId,
				}
			}

			return l.updateReference(
				context, session, cm,
				l.getSchemaRefRelMap(req.ProjectId, common.ConstInterfaceDefinitionTypeSchema, req.SchemaId),
			)
		},
	)

	return ot, err
}

func (l *LocalImportInterfaceDefinitionLogic) updateReference(
	context context.Context, session sqlx.Session, req map[string]*createSchemaReferenceInternalReq,
	rrm SchemaRefRelMap,
) error {
	user := l.currentUser
	if user == nil {
		user = &systemUser.TokenUserInfo
	}

	oSet := hashset.New()
	for k := range rrm {
		oSet.Add(k)
	}

	cSet := hashset.New()
	for k := range req {
		cSet.Add(k)
	}

	// 待删除：在`oSet`中而不在`cSet`中的元素
	dSet := oSet.Difference(cSet)
	// 待新增：在`cSet`中而不在`oSet`中的元素
	iSet := cSet.Difference(oSet)

	// 删除引用关系
	if err := mr.MapReduceVoid[*model.InterfaceSchemaReferenceRelationship, any](
		func(source chan<- *model.InterfaceSchemaReferenceRelationship) {
			for _, v := range dSet.Values() {
				if s, ok := v.(string); !ok {
					continue
				} else if rr, ok := rrm[s]; ok {
					source <- rr
				}
			}
		}, func(item *model.InterfaceSchemaReferenceRelationship, writer mr.Writer[any], cancel func(error)) {
			var err error
			defer func() {
				if err != nil {
					cancel(err)
				}
			}()

			item.Deleted = int64(qetconstants.HasDeleted)
			item.DeletedBy = sql.NullString{
				String: user.Account,
				Valid:  true,
			}
			item.DeletedAt = sql.NullTime{
				Time:  time.Now(),
				Valid: true,
			}
			if _, err = l.svcCtx.InterfaceSchemaReferenceModel.Update(context, session, item); err != nil {
				err = errors.Wrapf(
					errorx.Err(errorx.DBError, err.Error()), "failed to update table[%s] with values[%+v], error: %+v",
					l.svcCtx.InterfaceSchemaReferenceModel.Table(), item, err,
				)
			}
		}, func(pipe <-chan any, cancel func(error)) {
		}, mr.WithContext(l.ctx),
	); err != nil {
		return err
	}

	// 新增引用关系
	return mr.MapReduceVoid[*createSchemaReferenceInternalReq, any](
		func(source chan<- *createSchemaReferenceInternalReq) {
			for _, v := range iSet.Values() {
				if s, ok := v.(string); !ok {
					continue
				} else if r, ok := req[s]; ok {
					source <- r
				}
			}
		}, func(item *createSchemaReferenceInternalReq, writer mr.Writer[any], cancel func(error)) {
			var err error
			defer func() {
				if err != nil {
					cancel(err)
				}
			}()

			err = l.createSchemaReference(context, session, item)
		}, func(pipe <-chan any, cancel func(error)) {
		}, mr.WithContext(l.ctx),
	)
}

func (l *LocalImportInterfaceDefinitionLogic) getSchemaRefRelMap(projectId, referenceType, referenceId string) SchemaRefRelMap {
	m, ok := l.rrMap[fmt.Sprintf(
		"%s:%s:%s:%s", ConstMapKeyProjectIdReferenceTypeReferenceId, projectId, referenceType, referenceId,
	)]
	if !ok {
		return nil
	}

	return m
}
