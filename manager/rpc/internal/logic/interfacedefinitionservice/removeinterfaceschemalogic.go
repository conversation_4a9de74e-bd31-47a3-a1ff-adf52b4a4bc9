package interfacedefinitionservicelogic

import (
	"context"
	"fmt"

	"github.com/hashicorp/go-multierror"
	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/mr"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/redislock"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type RemoveInterfaceSchemaLogic struct {
	*BaseLogic
}

func NewRemoveInterfaceSchemaLogic(ctx context.Context, svcCtx *svc.ServiceContext) *RemoveInterfaceSchemaLogic {
	return &RemoveInterfaceSchemaLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// RemoveInterfaceSchema 删除接口数据模型
func (l *RemoveInterfaceSchemaLogic) RemoveInterfaceSchema(in *pb.RemoveInterfaceSchemaReq) (resp *pb.RemoveInterfaceSchemaResp, err error) {
	// validate the project_id in req
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, in.GetProjectId()); err != nil {
		return nil, err
	}

	schemaIds := stringx.Distinct(in.GetSchemaIds())

	workers := len(schemaIds)
	if workers > common.ConstMRMaxWorkers {
		workers = common.ConstMRMaxWorkers
	}

	mr.ForEach(func(source chan<- any) {
		for _, schemaId := range schemaIds {
			source <- schemaId
		}
	}, func(item any) {
		schemaId, ok := item.(string)
		if !ok {
			err = multierror.Append(err, errorx.Err(errorx.InternalError, fmt.Sprintf("the schema id[%v (%T)] is not a string", item, item)))
		} else {
			if e := l.remove(in.GetProjectId(), schemaId); e != nil {
				err = multierror.Append(err, e)
			}
		}
	}, mr.WithContext(l.ctx), mr.WithWorkers(workers))

	return &pb.RemoveInterfaceSchemaResp{}, err
}

func (l *RemoveInterfaceSchemaLogic) remove(projectId, schemaId string) error {
	// validate the schema_id in req
	schema, err := model.CheckInterfaceSchemaBySchemaId(l.ctx, l.svcCtx.InterfaceSchemaModel, projectId, schemaId)
	if err != nil {
		return err
	} else if schema.Mode == common.ConstInterfaceDefinitionCreateModeBuiltin {
		return errors.WithStack(errorx.Err(errorx.ProhibitedBehavior, fmt.Sprintf("cannot remove the builtin interface schema[%s]", schema.Name)))
	}

	// validate the reference relationship between schema and (document or schema)
	srs, err := l.svcCtx.InterfaceSchemaReferenceModel.FindReferenceBySchemaId(l.ctx, l.svcCtx.InterfaceSchemaModel, l.svcCtx.InterfaceDocumentModel, projectId, schemaId)
	if err != nil && err != model.ErrNotFound {
		return errors.Wrapf(errorx.Err(errorx.DBError, err.Error()), "failed to find the interface schema with project_id[%s] and schema_id[%s], error: %+v", projectId, schemaId, err)
	} else if len(srs) > 0 {
		return errors.WithStack(errorx.Err(errorx.ProhibitedBehavior, fmt.Sprintf("cannot remove the interface schema[%s] which is referenced by %d interface document and schema", schema.Name, len(srs))))
	}

	// acquire redis lock
	key := fmt.Sprintf("%s:%s:%s", common.ConstLockInterfaceSchemaProjectIdSchemaIdPrefix, projectId, schemaId)
	lock, err := redislock.NewRedisLockAndAcquire(l.svcCtx.Redis, key, redislock.WithExpire(common.ConstLockExpireTime))
	if err != nil {
		return err
	}
	defer func() {
		// release redis lock
		e := lock.Release()
		if e != nil {
			l.Logger.Error(e)
			if re, ok := errorx.RootError(e); ok && re.Code() != errorx.ReleaseRedisLockFailure && err == nil {
				err = e
			}
		}
	}()

	return l.svcCtx.InterfaceSchemaModel.Trans(l.ctx, func(context context.Context, session sqlx.Session) error {
		// Table: category_tree
		if _, err := l.svcCtx.CategoryTreeModel.RemoveByNodeId(context, session, l.svcCtx.CategoryModel, projectId, common.ConstCategoryTreeTypeInterfaceSchema, schemaId); err != nil {
			return errors.Wrapf(errorx.Err(errorx.DBError, err.Error()), "failed to remove category tree with project_id[%s] and schema_id[%s], error: %+v", projectId, schemaId, err)
		}

		// Table: category
		if _, err := l.svcCtx.CategoryModel.RemoveByNodeId(context, session, projectId, common.ConstCategoryTreeTypeInterfaceSchema, schemaId); err != nil {
			return errors.Wrapf(errorx.Err(errorx.DBError, err.Error()), "failed to remove category with project_id[%s] and schema_id[%s], error: %+v", projectId, schemaId, err)
		}

		// Table: interface_schema
		if _, err := l.svcCtx.InterfaceSchemaModel.RemoveBySchemaId(context, session, projectId, schemaId); err != nil {
			return errors.Wrapf(errorx.Err(errorx.DBError, err.Error()), "failed to remove interface schema with project_id[%s] and schema_id[%s], error: %+v", projectId, schemaId, err)
		}

		return nil
	})
}
