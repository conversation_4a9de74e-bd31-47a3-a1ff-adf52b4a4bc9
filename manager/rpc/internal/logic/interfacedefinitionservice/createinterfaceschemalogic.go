package interfacedefinitionservicelogic

import (
	"context"
	"database/sql"
	"fmt"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"
	"github.com/zeromicro/go-zero/core/mr"
	"github.com/zeromicro/go-zero/core/stores/sqlx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	categoryservicelogic "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/logic/categoryservice"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type CreateInterfaceSchemaLogic struct {
	*BaseLogic
}

func NewCreateInterfaceSchemaLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CreateInterfaceSchemaLogic {
	return &CreateInterfaceSchemaLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// CreateInterfaceSchema 创建接口数据模型
func (l *CreateInterfaceSchemaLogic) CreateInterfaceSchema(in *pb.CreateInterfaceSchemaReq) (resp *pb.CreateInterfaceSchemaResp, err error) {
	var c *model.Category

	// validate the project_id in req
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, in.GetProjectId()); err != nil {
		return nil, err
	}

	// validate the category_id in req
	if c, err = model.CheckCategoryByCategoryId(l.ctx, l.svcCtx.CategoryModel, in.GetProjectId(), common.ConstCategoryTreeTypeInterfaceSchema, in.GetCategoryId()); err != nil {
		return nil, err
	} else if c.CategoryType != common.ConstCategoryTypeDirectory {
		return nil, errors.WithStack(errorx.Err(errorx.ProhibitedBehavior, fmt.Sprintf("the type of parent category[%s] does not support creation of sub category", c.CategoryType)))
	} else if c.Builtin != 0 {
		return nil, errors.WithStack(errorx.Err(errorx.ProhibitedBehavior, fmt.Sprintf("cannot create interface schema in the builtin category[%s]", c.Name)))
	}

	// validate the full_name in req
	if _, err = l.svcCtx.InterfaceSchemaModel.FindOneByProjectIdFullName(l.ctx, in.GetProjectId(), in.GetFullName()); err != model.ErrNotFound {
		if err != nil {
			return nil, errors.Wrapf(errorx.Err(errorx.DBError, err.Error()), "failed to find interface schema with project_id[%s] and full_name[%s], error: %+v", in.GetProjectId(), in.GetFullName(), err)
		}
		return nil, errors.WithStack(errorx.Err(errorx.AlreadyExists, fmt.Sprintf("interface schema with project_id[%s] and full_name[%s] already exists", in.GetProjectId(), in.GetFullName())))
	}

	schema, err := l.CreateInterfaceSchemaForInternal(l.ctx, nil, CreateInterfaceSchemaInternalReq{
		CreateInterfaceSchemaReq: in,
		SchemaId:                 "",
		Mode:                     common.ConstInterfaceDefinitionCreateModeManual,
	})
	if err != nil {
		return nil, err
	}

	resp = &pb.CreateInterfaceSchemaResp{Schema: &pb.InterfaceSchema{}}
	if err = utils.Copy(resp.Schema, schema, l.converters...); err != nil {
		return nil, errors.Wrapf(errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy interface schema[%+v] to response, error: %+v", schema, err)
	}

	return resp, nil
}

func (l *CreateInterfaceSchemaLogic) CreateInterfaceSchemaForInternal(ctx context.Context, session sqlx.Session, req CreateInterfaceSchemaInternalReq) (*model.InterfaceSchema, error) {
	var (
		schemaId string
		err      error
	)

	if req.SchemaId != "" {
		schemaId = req.SchemaId
	} else {
		schemaId, err = l.generateSchemaId(req.GetProjectId(), nil)
		if err != nil {
			return nil, err
		}
	}

	if req.Mode == "" {
		req.Mode = common.ConstInterfaceDefinitionCreateModeManual
	}

	schema := &model.InterfaceSchema{
		ProjectId:  req.GetProjectId(),
		CategoryId: req.GetCategoryId(),
		SchemaId:   schemaId,
		FullName:   req.GetFullName(),
		Name:       req.GetName(),
		Description: sql.NullString{
			String: req.GetDescription(),
			Valid:  req.GetDescription() != "",
		},
		Mode: req.Mode,
		ImportType: sql.NullString{
			String: req.ImportType,
			Valid:  req.ImportType != "",
		},
		Data:      jsonx.MarshalToStringIgnoreError(req.GetData()),
		CreatedBy: l.currentUser.Account,
		UpdatedBy: l.currentUser.Account,
	}

	// create interface schema in a transaction
	fn := func(context context.Context, session sqlx.Session) error {
		if _, err = l.svcCtx.InterfaceSchemaModel.InsertTX(context, session, schema); err != nil {
			return errors.Wrapf(errorx.Err(errorx.DBError, err.Error()), "failed to insert table[%s] with values[%+v], error: %+v", l.svcCtx.InterfaceSchemaModel.Table(), schema, err)
		}

		// create a category in the interface schema category tree
		if _, err = l.createCategoryLogic.CreateCategoryForInternal(context, session, categoryservicelogic.CreateCategoryInternalReq{
			CreateCategoryReq: &pb.CreateCategoryReq{
				ProjectId:   req.GetProjectId(),
				Type:        common.ConstCategoryTreeTypeInterfaceSchema,
				Name:        req.GetName(),
				Description: req.GetDescription(),
				ParentId:    req.GetCategoryId(),
			},
			CategoryType:   common.ConstCategoryTypeFile,
			RootType:       common.GetRootTypeByNodeType(common.ConstInterfaceDefinitionTypeSchema),
			NodeType:       common.ConstInterfaceDefinitionTypeSchema,
			NodeId:         schemaId,
			IsInternalCall: true,
		}); err != nil {
			return err
		}

		// create reference relationship between schema and schema
		return mr.MapReduceVoid[string, any](func(source chan<- string) {
			for _, sid := range findReferenceInSchema(req.Data) {
				source <- sid
			}
		}, func(item string, writer mr.Writer[any], cancel func(error)) {
			var err error
			defer func() {
				if err != nil {
					cancel(err)
				}
			}()

			err = l.createSchemaReference(context, session, &createSchemaReferenceInternalReq{
				ProjectId:     req.GetProjectId(),
				ReferenceType: common.ConstInterfaceDefinitionTypeSchema,
				ReferenceId:   schemaId,
				SchemaId:      item,
			})
		}, func(pipe <-chan any, cancel func(error)) {
		}, mr.WithContext(l.ctx))
	}

	if session != nil {
		if ctx == nil {
			ctx = l.ctx
		}
		err = fn(ctx, session)
	} else {
		err = l.svcCtx.InterfaceSchemaModel.Trans(l.ctx, fn)
	}
	if err != nil {
		return nil, err
	}

	return schema, nil
}
