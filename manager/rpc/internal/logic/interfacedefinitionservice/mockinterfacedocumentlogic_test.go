package interfacedefinitionservicelogic

import (
	"context"
	"reflect"
	"testing"

	"github.com/davecgh/go-spew/spew"
	"github.com/zeromicro/go-zero/core/conf"
	"github.com/zeromicro/go-zero/core/jsonx"

	qetutils "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/config"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

func TestMockInterfaceDocumentLogic_processIntegerType(t *testing.T) {
	type args struct {
		schema *pb.Schema
	}
	tests := []struct {
		name string
		args args
		want any
	}{
		{
			name: "integer - int32 - default",
			args: args{
				schema: &pb.Schema{
					Type:    common.ConstInterfaceDefinitionFieldTypeInteger,
					Format:  common.ConstInterfaceDefinitionFieldFormatInt32,
					Default: logic.ConvertAnyToValue(1),
					Example: logic.ConvertAnyToValue(11),
					Enum:    logic.ConvertAnyToValues(1, 11, 111),
				},
			},
			want: int32(1),
		},
		{
			name: "integer - int64 - example",
			args: args{
				schema: &pb.Schema{
					Type:    common.ConstInterfaceDefinitionFieldTypeInteger,
					Format:  common.ConstInterfaceDefinitionFieldFormatInt64,
					Example: logic.ConvertAnyToValue(11),
				},
			},
			want: int64(11),
		},
		{
			name: "integer - enum",
			args: args{
				schema: &pb.Schema{
					Type:    common.ConstInterfaceDefinitionFieldTypeInteger,
					Example: logic.ConvertAnyToValue(11),
					Enum:    logic.ConvertAnyToValues(111, 11, 1),
				},
			},
			want: int64(111),
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				if got := processIntegerType(tt.args.schema); got != tt.want {
					t.Errorf("processIntegerType() = %v (%T), want %v (%T)", got, got, tt.want, tt.want)
				} else {
					t.Logf("processIntegerType() = %v (%T), want %v (%T)", got, got, tt.want, tt.want)
				}
			},
		)
	}
}

func TestMockInterfaceDocumentLogic_getValueBySchema(t *testing.T) {
	type fields struct {
		BaseLogic *BaseLogic
	}
	type args struct {
		projectId string
		schema    *pb.Schema
	}

	var c config.Config
	conf.MustLoad("./rpc/etc/manager.yaml", &c)

	schema, err := NewSchemaByName(common.ConstTDSCommonCallReqSchemaName)
	if err != nil {
		t.Fatal(err)
	}
	defer ReleaseSchema(schema)

	tests := []struct {
		name   string
		fields fields
		args   args
		want   any
	}{
		{
			name: "",
			fields: fields{
				BaseLogic: newBaseLogic(context.Background(), svc.NewServiceContext(c)),
			},
			args: args{
				projectId: "project_id:YWb3GBvLl1AWSKCf5SL-c",
				schema:    schema,
			},
			want: nil,
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				l := &MockInterfaceDocumentLogic{
					BaseLogic: tt.fields.BaseLogic,
				}
				got := l.getValueBySchema(
					tt.args.projectId, common.ConstInterfaceDefinitionImportTypeTT, tt.args.schema, nil,
				)
				t.Logf("getValueBySchema: \n%s", jsonx.MarshalToIndentStringIgnoreError(got, "", "  "))
			},
		)
	}
}

func TestMockInterfaceDocumentLogic_NewHttpRequestComponent(t *testing.T) {
	type args struct {
		obj any
	}
	tests := []struct {
		name string
		args args
	}{
		{
			name: "HttpRequestComponent struct",
			args: args{
				obj: pb.HttpRequestComponent{},
			},
		},
		{
			name: "HttpRequestComponent struct pointer",
			args: args{
				obj: &pb.HttpRequestComponent{},
			},
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				val := qetutils.New(reflect.TypeOf(tt.args.obj))
				t.Logf("%+v", spew.Sprint(val.Interface()))
			},
		)
	}
}
