package interfacedefinitionservicelogic

import (
	"context"
	"database/sql"
	"fmt"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"
	"github.com/zeromicro/go-zero/core/mr"
	"github.com/zeromicro/go-zero/core/stores/sqlx"

	qetconstants "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	categoryservicelogic "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/logic/categoryservice"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type CreateInterfaceDocumentLogic struct {
	*BaseLogic
}

func NewCreateInterfaceDocumentLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CreateInterfaceDocumentLogic {
	return &CreateInterfaceDocumentLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// CreateInterfaceDocument 创建接口文档
func (l *CreateInterfaceDocumentLogic) CreateInterfaceDocument(in *pb.CreateInterfaceDocumentReq) (
	resp *pb.CreateInterfaceDocumentResp, err error,
) {
	var c *model.Category

	// validate the project_id in req
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, in.GetProjectId()); err != nil {
		return nil, err
	}

	// validate the category_id in req
	if c, err = model.CheckCategoryByCategoryId(
		l.ctx, l.svcCtx.CategoryModel, in.GetProjectId(), common.ConstCategoryTreeTypeInterfaceDocument,
		in.GetCategoryId(),
	); err != nil {
		return nil, err
	} else if c.CategoryType != common.ConstCategoryTypeDirectory {
		return nil, errors.WithStack(
			errorx.Err(
				errorx.ProhibitedBehavior,
				fmt.Sprintf("the type of parent category[%s] does not support creation of sub category", c.CategoryType),
			),
		)
	} else if c.Builtin != 0 {
		return nil, errors.WithStack(
			errorx.Err(
				errorx.ProhibitedBehavior,
				fmt.Sprintf("cannot create interface document in the builtin category[%s]", c.Name),
			),
		)
	}

	document, err := l.CreateInterfaceDocumentForInternal(
		l.ctx, nil, CreateInterfaceDocumentInternalReq{
			CreateInterfaceDocumentReq: in,
			DocumentId:                 "",
			Mode:                       in.GetMode(),
		},
	)
	if err != nil {
		return nil, err
	}

	resp = &pb.CreateInterfaceDocumentResp{Document: &pb.InterfaceDocument{}}
	if err = utils.Copy(resp.Document, document, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy interface document[%+v] to response, error: %+v", document, err,
		)
	}

	return resp, nil
}

func (l *CreateInterfaceDocumentLogic) CreateInterfaceDocumentForInternal(
	ctx context.Context, session sqlx.Session, req CreateInterfaceDocumentInternalReq,
) (*model.InterfaceDocument, error) {
	var (
		documentId string
		err        error
	)

	if req.DocumentId != "" {
		documentId = req.DocumentId
	} else {
		documentId, err = l.generateDocumentId(req.GetProjectId(), nil)
		if err != nil {
			return nil, err
		}
	}

	if req.Mode == "" {
		req.Mode = common.ConstInterfaceDefinitionCreateModeManual
	}

	// 由于`protojson`只能序列化`[]proto.Message`，而`tags`字段类型为`[]string`，Go Struct跟PB没有大的区别，因此直接使用`jsonx`暂代`protojson`
	tags := jsonx.MarshalToStringIgnoreError(req.GetTags())

	maintainedBy := req.GetMaintainedBy()
	if maintainedBy == "" {
		// if the maintainer is not set, the creator is set as the maintainer
		maintainedBy = l.currentUser.Account
	}

	document := &model.InterfaceDocument{
		ProjectId:  req.GetProjectId(),
		CategoryId: req.GetCategoryId(),
		DocumentId: documentId,
		Name:       req.GetName(),
		Description: sql.NullString{
			String: req.GetDescription(),
			Valid:  req.GetDescription() != "",
		},
		Type: req.GetType(),
		Mode: req.Mode,
		ImportType: sql.NullString{
			String: req.ImportType,
			Valid:  req.ImportType != "",
		},
		Status:   req.GetStatus(),
		Priority: req.GetPriority(),
		Tags: sql.NullString{
			String: tags,
			Valid:  tags != "",
		},
		State:             int64(qetconstants.EnableStatus),
		CaseExecutionMode: int64(constants.Parallel),
		Service: sql.NullString{
			String: req.GetService(),
			Valid:  req.GetService() != "",
		},
		Path:   req.GetPath(),
		Method: req.GetMethod(),
		Data:   jsonx.MarshalToStringIgnoreError(req.GetData()),
		MaintainedBy: sql.NullString{
			String: maintainedBy,
			Valid:  maintainedBy != "",
		},
		CreatedBy: l.currentUser.Account,
		UpdatedBy: l.currentUser.Account,
	}

	// create interface document in a transaction
	fn := func(context context.Context, session sqlx.Session) error {
		if _, err = l.svcCtx.InterfaceDocumentModel.InsertTX(context, session, document); err != nil {
			return errors.Wrapf(
				errorx.Err(errorx.DBError, err.Error()), "failed to insert table[%s] with values[%+v], error: %+v",
				l.svcCtx.InterfaceDocumentModel.Table(), document, err,
			)
		}

		// create a category in the interface document category tree
		if _, err = l.createCategoryLogic.CreateCategoryForInternal(
			context, session, categoryservicelogic.CreateCategoryInternalReq{
				CreateCategoryReq: &pb.CreateCategoryReq{
					ProjectId:   req.GetProjectId(),
					Type:        common.ConstCategoryTreeTypeInterfaceDocument,
					Name:        req.GetName(),
					Description: req.GetDescription(),
					ParentId:    req.GetCategoryId(),
				},
				CategoryType:   common.ConstCategoryTypeFile,
				RootType:       common.GetRootTypeByNodeType(common.ConstInterfaceDefinitionTypeDocument),
				NodeType:       common.ConstInterfaceDefinitionTypeDocument,
				NodeId:         documentId,
				IsInternalCall: true,
			},
		); err != nil {
			return err
		}

		// create reference relationship between document and schema
		if err = mr.MapReduceVoid[string, any](
			func(source chan<- string) {
				for _, sid := range findReferenceInDocument(req.Data) {
					source <- sid
				}
			}, func(item string, writer mr.Writer[any], cancel func(error)) {
				var err error
				defer func() {
					if err != nil {
						cancel(err)
					}
				}()

				err = l.createSchemaReference(
					context, session, &createSchemaReferenceInternalReq{
						ProjectId:     req.GetProjectId(),
						ReferenceType: common.ConstInterfaceDefinitionTypeDocument,
						ReferenceId:   documentId,
						SchemaId:      item,
					},
				)
			}, func(pipe <-chan any, cancel func(error)) {
			}, mr.WithContext(l.ctx),
		); err != nil {
			return err
		}

		// create the new tag and tag reference of interface document
		if err := l.createTagLogic.CreateTagAndReferenceForInternal(
			context, session, types.CreateOrUpdateTagReference{
				ProjectId:     document.ProjectId,
				ReferenceType: common.ConstReferenceTypeInterfaceDocument,
				ReferenceId:   document.DocumentId,
				Tags:          req.GetTags(),
			},
		); err != nil {
			return err
		}

		return nil
	}

	if session != nil {
		if ctx == nil {
			ctx = l.ctx
		}
		err = fn(ctx, session)
	} else {
		err = l.svcCtx.InterfaceDocumentModel.Trans(l.ctx, fn)
	}
	if err != nil {
		return nil, err
	}

	return document, nil
}
