package interfacedefinitionservicelogic

import (
	"context"
	"database/sql"
	"fmt"
	"sync"
	"time"

	"github.com/Masterminds/squirrel"
	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"
	"github.com/zeromicro/go-zero/core/lang"
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/mr"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/syncx"

	qetconstants "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sqlbuilder"
	qettypes "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/types"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/userinfo"
	qetutils "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"
	userpb "gitlab.ttyuyin.com/TestDevelopment/qet-backend-middleware/user/rpc/pb"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/utils"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/logic"
	apiplanservicelogic "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/logic/apiplanservice"
	categoryservicelogic "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/logic/categoryservice"
	tagservicelogic "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/logic/tagservice"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
	relationpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/relation/rpc/pb"
)

type BaseLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext

	serviceCache map[string]string
	serviceLock  sync.RWMutex

	teamCache map[string]string
	teamLock  sync.RWMutex

	singleFlight syncx.SingleFlight

	currentUser *userinfo.UserInfo

	createCategoryLogic    *categoryservicelogic.CreateCategoryLogic
	modifyCategoryLogic    *categoryservicelogic.ModifyCategoryLogic
	moveCategoryLogic      *categoryservicelogic.MoveCategoryTreeLogic
	createTagLogic         *tagservicelogic.CreateTagLogic
	addSuiteToApiPlanLogic *apiplanservicelogic.AddSuiteToApiPlanLogic

	converters []qetutils.TypeConverter
}

func newBaseLogic(ctx context.Context, svcCtx *svc.ServiceContext) *BaseLogic {
	user := userinfo.FromContext(ctx)
	if user == nil {
		user = &systemUser.TokenUserInfo
	}

	return &BaseLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,

		serviceCache: make(map[string]string, constants.ConstDefaultMakeMapSize),
		teamCache:    make(map[string]string, constants.ConstDefaultMakeMapSize),

		singleFlight: syncx.NewSingleFlight(),

		currentUser: user,

		createCategoryLogic:    categoryservicelogic.NewCreateCategoryLogic(ctx, svcCtx),
		modifyCategoryLogic:    categoryservicelogic.NewModifyCategoryLogic(ctx, svcCtx),
		moveCategoryLogic:      categoryservicelogic.NewMoveCategoryTreeLogic(ctx, svcCtx),
		createTagLogic:         tagservicelogic.NewCreateTagLogic(ctx, svcCtx),
		addSuiteToApiPlanLogic: apiplanservicelogic.NewAddSuiteToApiPlanLogic(ctx, svcCtx),

		converters: []qetutils.TypeConverter{
			logic.SqlNullStringToTags(),
			logic.StringToResourceState(),
			logic.StringToAccountConfig(),
			logic.StringToDocument(),
			logic.StringToSchema(),
			logic.StringToInputParameters(),
			logic.StringToOutputParameters(),
		},
	}
}

// generateDocumentId 生成接口文档ID
func (l *BaseLogic) generateDocumentId(projectId string, cache types.Recorders) (string, error) {
	g := qetutils.NewUniqueIdGenerator(
		qetutils.WithGenerateFunc(utils.GenInterfaceDocumentId), qetutils.WithIsUniqueFunc(
			func(id string) bool {
				if cache != nil {
					if _, ok := cache[id]; ok {
						// 本次生成的 `document_id` 已存在于记录器中，需要重新创建
						return false
					}
				}

				defer func() {
					if cache != nil {
						cache[id] = lang.Placeholder
					}
				}()

				r, err := l.svcCtx.InterfaceDocumentModel.FindOneByProjectIdDocumentId(l.ctx, projectId, id)
				if errors.Is(err, model.ErrNotFound) || r == nil {
					return true
				}
				return false
			},
		),
	)
	documentId := g.Next()
	if documentId == "" {
		return "", errors.WithStack(
			errorx.Err(
				errorx.GenerateUniqueIdFailure, "failed to generate document id, please try it later",
			),
		)
	}

	return documentId, nil
}

// generateSchemaId 生成接口数据模型ID
func (l *BaseLogic) generateSchemaId(projectId string, cache types.Recorders) (string, error) {
	g := qetutils.NewUniqueIdGenerator(
		qetutils.WithGenerateFunc(utils.GenInterfaceSchemaId), qetutils.WithIsUniqueFunc(
			func(id string) bool {
				if cache != nil {
					if _, ok := cache[id]; ok {
						// 本次生成的 `schema_id` 已存在于记录器中，需要重新创建
						return false
					}
				}

				defer func() {
					if cache != nil {
						cache[id] = lang.Placeholder
					}
				}()

				r, err := l.svcCtx.InterfaceSchemaModel.FindOneByProjectIdSchemaId(l.ctx, projectId, id)
				if errors.Is(err, model.ErrNotFound) || r == nil {
					return true
				}
				return false
			},
		),
	)
	schemaId := g.Next()
	if schemaId == "" {
		return "", errorx.Err(errorx.GenerateUniqueIdFailure, "failed to generate schema id, please try it later")
	}

	return schemaId, nil
}

// generateConfigId 生产接口配置ID
func (l *BaseLogic) generateConfigId(projectId, documentId string) (string, error) {
	g := qetutils.NewUniqueIdGenerator(
		qetutils.WithGenerateFunc(utils.GenInterfaceConfigId), qetutils.WithIsUniqueFunc(
			func(id string) bool {
				r, err := l.svcCtx.InterfaceConfigurationModel.FindOneByProjectIdDocumentIdConfigId(
					l.ctx, projectId, documentId, id,
				)
				if errors.Is(err, model.ErrNotFound) || r == nil {
					return true
				}
				return false
			},
		),
	)
	configId := g.Next()
	if configId == "" {
		return "", errors.WithStack(
			errorx.Err(
				errorx.GenerateUniqueIdFailure, "failed to generate interface config id, please try it later",
			),
		)
	}

	return configId, nil
}

// generateCaseId 生成接口用例ID
func (l *BaseLogic) generateCaseId(projectId, documentId string) (string, error) {
	g := qetutils.NewUniqueIdGenerator(
		qetutils.WithGenerateFunc(utils.GenInterfaceCaseId), qetutils.WithIsUniqueFunc(
			func(id string) bool {
				r, err := l.svcCtx.InterfaceCaseModel.FindLatestOneNoCache(l.ctx, projectId, documentId, id)
				if errors.Is(err, model.ErrNotFound) || r == nil {
					return true
				}
				return false
			},
		),
	)
	caseId := g.Next()
	if caseId == "" {
		return "", errors.WithStack(
			errorx.Err(
				errorx.GenerateUniqueIdFailure, "failed to generate interface case id, please try it later",
			),
		)
	}

	return caseId, nil
}

// getCategoriesByType 根据接口定义类型（文档、数据模型）获取分类树目录
func (l *BaseLogic) getCategoriesByType(projectId, tp string) (*model.Category, []*model.Category, error) {
	var (
		categoryTreeType common.CategoryTreeType
		categoryRootName common.CategoryBuiltinName
		msg              string
	)
	switch tp {
	case common.ConstInterfaceDefinitionTypeDocument:
		categoryTreeType = common.ConstCategoryTreeTypeInterfaceDocument
		categoryRootName = common.ConstCategoryRootAllDocument
		msg = "interface document"
	case common.ConstInterfaceDefinitionTypeSchema:
		categoryTreeType = common.ConstCategoryTreeTypeInterfaceSchema
		categoryRootName = common.ConstCategoryRootAllSchema
		msg = "interface schema"
	default:
		return nil, nil, errorx.Errorf(
			errorx.DoesNotSupport, "the interface definition type[%s] is not currently supported", tp,
		)
	}

	c, err := l.svcCtx.CategoryModel.FindOneByProjectIdTypeName(l.ctx, projectId, categoryTreeType, categoryRootName)
	if err != nil {
		if !errors.Is(err, model.ErrNotFound) {
			return nil, nil, errors.Wrapf(
				errorx.Err(errorx.DBError, err.Error()),
				"failed to find %s category with project_id[%s], error: %+v",
				msg, projectId, err,
			)
		} else {
			return nil, nil, errorx.Errorf(
				errorx.NotExists, "%s category with project_id[%s] doesn't exist", msg, projectId,
			)
		}
	}

	cs, err := l.svcCtx.CategoryModel.FindDescendantCategories(
		l.ctx, model.GetCategoryTreeCondition{
			ProjectId:     projectId,
			Type:          categoryTreeType,
			CategoryId:    c.CategoryId,
			Depth:         1,
			OnlyDirectory: true,
			ExcludeSelf:   true,
		},
	)
	if err != nil && !errors.Is(err, model.ErrNotFound) {
		return nil, nil, errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()),
			"failed to find descendant categories of %s category with project_id[%s], error: %+v",
			msg, projectId, err,
		)
	}

	return c, cs, nil
}

// insertOrUpdateDocument 增或改接口文档
func (l *BaseLogic) insertOrUpdateDocument(
	ctx context.Context, session sqlx.Session, document *model.InterfaceDocument,
) (model.OperationType, error) {
	var (
		data *model.InterfaceDocument
		err  error

		idm = l.svcCtx.InterfaceDocumentModel
		ot  = model.ConstOperationTypeError
	)

	if document.Id != 0 {
		data, err = idm.FindOne(ctx, document.Id)
	} else if document.DocumentId != "" {
		data, err = idm.FindOneByProjectIdDocumentId(ctx, document.ProjectId, document.DocumentId)
	} else {
		data, err = idm.FindOneByProjectIdName(ctx, document.ProjectId, document.Name)
	}

	if err != nil {
		if !errors.Is(err, model.ErrNotFound) {
			return ot, errors.Wrapf(
				errorx.Err(errorx.DBError, err.Error()),
				"failed to find the interface document, project_id: %s, name: %s, error: %+v",
				document.ProjectId, document.Name, err,
			)
		}

		// 新增
		if _, err = idm.InsertTX(ctx, session, document); err != nil {
			return ot, errors.Wrapf(
				errorx.Err(errorx.DBError, err.Error()),
				"failed to insert table[%s] with values[%+v], error: %+v",
				idm.Table(), document, err,
			)
		}

		ot = model.ConstOperationTypeInsert
	} else {
		// 更新
		document.Id = data.Id
		document.DocumentId = data.DocumentId
		if document.Status != data.Status && document.Status == int64(common.ConstInterfaceDefinitionStatusNone) {
			document.Status = data.Status
		}
		document.Priority = data.Priority
		document.Tags = data.Tags
		if _, err = idm.UpdateTX(ctx, session, document); err != nil {
			return ot, errors.Wrapf(
				errorx.Err(errorx.DBError, err.Error()),
				"failed to update table[%s] with values[%+v], error: %+v",
				idm.Table(), document, err,
			)
		}

		ot = model.ConstOperationTypeUpdate
	}

	return ot, nil
}

// insertOrUpdateSchema 增或改接口数据模型
func (l *BaseLogic) insertOrUpdateSchema(
	ctx context.Context, session sqlx.Session, schema *model.InterfaceSchema,
) (model.OperationType, error) {
	var (
		data *model.InterfaceSchema
		err  error

		ism = l.svcCtx.InterfaceSchemaModel
		ot  = model.ConstOperationTypeError
	)

	if schema.Id != 0 {
		data, err = ism.FindOne(ctx, schema.Id)
	} else if schema.SchemaId != "" {
		data, err = ism.FindOneByProjectIdSchemaId(ctx, schema.ProjectId, schema.SchemaId)
	} else {
		data, err = ism.FindOneByProjectIdFullName(ctx, schema.ProjectId, schema.FullName)
	}

	if err != nil {
		if !errors.Is(err, model.ErrNotFound) {
			return ot, errors.Wrapf(
				errorx.Err(errorx.DBError, err.Error()),
				"failed to find the interface schema, project_id: %s, full_name: %s, error: %+v",
				schema.ProjectId, schema.FullName, err,
			)
		}

		// 新增
		if _, err = ism.InsertTX(ctx, session, schema); err != nil {
			return ot, errors.Wrapf(
				errorx.Err(errorx.DBError, err.Error()),
				"failed to insert table[%s] with values[%+v], error: %+v",
				ism.Table(), schema, err,
			)
		}

		ot = model.ConstOperationTypeInsert
	} else {
		// 更新
		schema.Id = data.Id
		schema.SchemaId = data.SchemaId
		if _, err = ism.UpdateTX(ctx, session, schema); err != nil {
			return ot, errors.Wrapf(
				errorx.Err(errorx.DBError, err.Error()),
				"failed to update table[%s] with values[%+v], error: %+v",
				ism.Table(), schema, err,
			)
		}

		ot = model.ConstOperationTypeUpdate
	}

	return ot, nil
}

// createSchemaReference 创建数据模型引用关系
func (l *BaseLogic) createSchemaReference(
	ctx context.Context, session sqlx.Session, req *createSchemaReferenceInternalReq,
) error {
	m := l.svcCtx.InterfaceSchemaReferenceModel

	data := &model.InterfaceSchemaReferenceRelationship{
		ProjectId:     req.ProjectId,
		ReferenceType: req.ReferenceType,
		ReferenceId:   req.ReferenceId,
		SchemaId:      req.SchemaId,
		CreatedBy:     l.currentUser.Account,
		UpdatedBy:     l.currentUser.Account,
		CreatedAt:     time.Now(),
		UpdatedAt:     time.Now(),
	}
	if _, err := m.Insert(ctx, session, data); err != nil {
		return errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()),
			"failed to insert table[%s] with values[%+v], error: %+v",
			m.Table(), data, err,
		)
	}

	return nil
}

// generateSearchSqlBuilderByType 根据接口定义类型（文档、数据模型）生成搜索的`SQL Builder`
func (l *BaseLogic) generateSearchSqlBuilderByType(tp string, req searchInternalReq) (
	sb, scb squirrel.SelectBuilder, err error,
) {
	/*
		SQL:
		SELECT t.*
		FROM interface_document AS t
			LEFT JOIN (
				SELECT t1.*
			    FROM category AS t1
					INNER JOIN category_tree AS t2 ON t1.`project_id` = t2.`project_id` AND t1.`category_id` = t2.`descendant`
			    WHERE t1.`project_id` = ?
			      AND t1.`type` = ?
			      AND t2.`ancestor` = ?
			      AND t1.`deleted` = ?
			      AND t2.`deleted` = ?
			    ORDER BY t2.`depth` DESC, t2.`index` ASC, t1.`created_at` ASC
			) AS t1 ON t.`category_id` = t1.`category_id`
		WHERE t.`project_id` = ?
		  AND t.`deleted` = ?
		  AND t1.`category_id` IS NOT NULL
	*/

	var (
		categoryTreeType common.CategoryTreeType
		m                qettypes.DBModel
		alias            = "t"
	)

	switch tp {
	case common.ConstInterfaceDefinitionTypeDocument:
		categoryTreeType = common.ConstCategoryTreeTypeInterfaceDocument
		m = l.svcCtx.InterfaceDocumentModel
	case common.ConstInterfaceDefinitionTypeSchema:
		categoryTreeType = common.ConstCategoryTreeTypeInterfaceSchema
		m = l.svcCtx.InterfaceSchemaModel
	default:
		return sb, scb, errors.WithStack(
			errorx.Errorf(
				errorx.DoesNotSupport, "the interface definition type[%s] is not currently supported", tp,
			),
		)
	}

	sb = squirrel.Select(utils.AddTableNameToFields(alias, m.Fields())...).
		From(fmt.Sprintf("%s AS %s", m.Table(), alias)).
		Where(
			fmt.Sprintf("%s.`project_id` = ? AND %s.`deleted` = ?", alias, alias),
			req.ProjectId, qetconstants.NotDeleted,
		)
	scb = squirrel.Select("COUNT(*)").
		From(fmt.Sprintf("%s AS %s", m.Table(), alias)).
		Where(
			fmt.Sprintf("%s.`project_id` = ? AND %s.`deleted` = ?", alias, alias),
			req.ProjectId, qetconstants.NotDeleted,
		)

	if req.DrillDown {
		sub := l.svcCtx.CategoryModel.FindDescendantCategoriesSqlBuilder(
			model.GetCategoryTreeCondition{
				ProjectId:     req.ProjectId,
				Type:          categoryTreeType,
				CategoryId:    req.CategoryId,
				OnlyDirectory: true,
			},
		)

		sb = sb.JoinClause(
			sub.Prefix("LEFT JOIN (").Suffix(
				fmt.Sprintf(
					") AS t1 ON %s.`category_id` = t1.`category_id`", alias,
				),
			),
		).
			Where("t1.`category_id` IS NOT NULL")
		scb = scb.JoinClause(
			sub.Prefix("LEFT JOIN (").Suffix(
				fmt.Sprintf(
					") AS t1 ON %s.`category_id` = t1.`category_id`", alias,
				),
			),
		).
			Where("t1.`category_id` IS NOT NULL")
	} else {
		sb = sb.Where(fmt.Sprintf("%s.`category_id` = ?", alias), req.CategoryId)
		scb = scb.Where(fmt.Sprintf("%s.`category_id` = ?", alias), req.CategoryId)
	}

	sb = sqlbuilder.SearchOptionsWithAlias(
		sb, alias, sqlbuilder.WithCondition(m, req.Condition), sqlbuilder.WithPagination(m, req.Pagination),
		sqlbuilder.WithSort(m, req.Sort),
	)
	scb = sqlbuilder.SearchOptionsWithAlias(scb, alias, sqlbuilder.WithCondition(m, req.Condition))

	return sb, scb, err
}

// createComponentGroupReference 创建接口用例跟组件组的引用关系
func (l *BaseLogic) createComponentGroupReference(
	ctx context.Context, session sqlx.Session, req createComponentGroupReferenceInternalReq,
) error {
	if ctx == nil {
		ctx = l.ctx
	}

	workers := len(req.Relations)
	if workers > common.ConstMRMaxWorkers {
		workers = common.ConstMRMaxWorkers
	}

	return mr.MapReduceVoid[*pb.Relation, any](
		func(source chan<- *pb.Relation) {
			for _, relation := range req.Relations {
				source <- relation
			}
		}, func(item *pb.Relation, writer mr.Writer[any], cancel func(error)) {
			var err error
			defer func() {
				if err != nil {
					cancel(err)
				}
			}()

			referenceId := item.GetReferenceId()
			children := item.GetChildrenRelations()

			if referenceId != "" {
				data := &model.ComponentGroupReferenceRelationship{
					ProjectId:        req.ProjectId,
					ReferenceType:    constants.InterfaceCase,
					ReferenceId:      req.CaseId,
					ReferenceVersion: req.Version,
					ComponentGroupId: referenceId,
					Deleted:          int64(qetconstants.NotDeleted),
					CreatedBy:        l.currentUser.Account,
					UpdatedBy:        l.currentUser.Account,
					CreatedAt:        time.Now(),
					UpdatedAt:        time.Now(),
				}
				if _, err = l.svcCtx.ComponentGroupReferenceModel.Insert(ctx, session, data); err != nil {
					err = errors.Wrapf(
						errorx.Err(errorx.DBError, err.Error()),
						"failed to insert table[%s] with values[%+v], error: %+v",
						l.svcCtx.ComponentGroupReferenceModel.Table(), data, err,
					)
					return
				}
			}

			if len(children) > 0 {
				for _, child := range children {
					if err = l.createComponentGroupReference(
						ctx, session, createComponentGroupReferenceInternalReq{
							ProjectId: req.ProjectId,
							CaseId:    req.CaseId,
							Version:   req.Version,
							Relations: child,
						},
					); err != nil {
						return
					}
				}
			}
		}, func(pipe <-chan any, cancel func(error)) {
		}, mr.WithContext(ctx), mr.WithWorkers(workers),
	)
}

// createElementAndComponent 创建接口用例跟元素和组件关系（包含跟函数处理的关系）
func (l *BaseLogic) createElementAndComponent(
	ctx context.Context, session sqlx.Session, req createElementAndComponentInternalReq,
) error {
	if ctx == nil {
		ctx = l.ctx
	}

	workers := len(req.Edges) + len(req.Combos) + len(req.Nodes)
	if workers > common.ConstMRMaxWorkers {
		workers = common.ConstMRMaxWorkers
	}

	return mr.MapReduceVoid[any, *model.FunctionReferenceRelationship](
		func(source chan<- any) {
			generate(req.Edges, source)
			generate(req.Combos, source)
			generate(req.Nodes, source)
		}, func(item any, writer mr.Writer[*model.FunctionReferenceRelationship], cancel func(error)) {
			var err error
			defer func() {
				if err != nil {
					cancel(err)
				}
			}()

			if e, ok := item.(pb.Element); ok {
				if err = l.createElement(
					ctx, session, createElementInternalReq{
						ProjectId: req.ProjectId,
						CaseId:    req.CaseId,
						Version:   req.Version,
						Element:   e,
					}, writer,
				); err != nil {
					return
				}
			}

			if c, ok := item.(pb.Component); ok {
				if err = l.createComponent(
					ctx, session, createComponentInternalReq{
						ProjectId: req.ProjectId,
						CaseId:    req.CaseId,
						Version:   req.Version,
						Component: c,
					}, writer,
				); err != nil {
					return
				}
			}
		}, func(pipe <-chan *model.FunctionReferenceRelationship, cancel func(error)) {
			var (
				err   error
				cache = make(types.StringPlaceholderCache)
			)
			defer func() {
				if err != nil {
					cancel(err)
				}
			}()

			for item := range pipe {
				key := fmt.Sprintf("%s:%s", item.FunctionName, item.FunctionType)
				if _, ok := cache[key]; ok {
					continue
				}

				if _, err = l.svcCtx.FunctionReferenceModel.Insert(ctx, session, item); err != nil {
					err = errors.Wrapf(
						errorx.Err(errorx.DBError, err.Error()),
						"failed to insert table[%s] with values[%+v], error: %+v",
						l.svcCtx.FunctionReferenceModel.Table(), item, err,
					)
					return
				}

				cache[key] = lang.Placeholder
			}
		}, mr.WithContext(ctx), mr.WithWorkers(workers),
	)
}

func generate[E pb.Element](elements []E, source chan<- any) {
	for _, element := range elements {
		source <- element
	}
}

func (l *BaseLogic) createElement(
	ctx context.Context, session sqlx.Session, req createElementInternalReq,
	_ mr.Writer[*model.FunctionReferenceRelationship],
) error {
	if ctx == nil {
		ctx = l.ctx
	}

	v := &model.InterfaceCaseElement{
		ProjectId:   req.ProjectId,
		CaseId:      req.CaseId,
		Version:     req.Version,
		ElementId:   req.Element.ElementId(),
		ElementType: req.Element.ElementType(),
		Data:        jsonx.MarshalToStringIgnoreError(req.Element),
		CreatedBy:   l.currentUser.Account,
		UpdatedBy:   l.currentUser.Account,
	}
	if _, err := l.svcCtx.InterfaceCaseElementModel.InsertTX(ctx, session, v); err != nil {
		return errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()),
			"failed to insert table[%s] with values[%+v], error: %+v",
			l.svcCtx.InterfaceCaseElementModel.Table(), v, err,
		)
	}

	return nil
}

func (l *BaseLogic) createComponent(
	ctx context.Context, session sqlx.Session, req createComponentInternalReq,
	writer mr.Writer[*model.FunctionReferenceRelationship],
) error {
	if ctx == nil {
		ctx = l.ctx
	}

	data := sql.NullString{}
	if req.Component.ComponentData() != nil {
		data.String = protobuf.MarshalJSONToStringIgnoreError(req.Component.ComponentData())
		data.Valid = true

		fn, ok := pb.ComponentExecutionDataFuncMap[req.Component.ComponentType()]
		if !ok {
			return errors.WithStack(
				errorx.Errorf(
					errorx.DoesNotSupport, "the component type[%s] doesn't support", req.Component.ComponentType(),
				),
			)
		}

		_, v := fn()
		if err := pb.ValidateUnmarshalOptions.Unmarshal([]byte(data.String), v); err != nil {
			return errors.Wrapf(
				errorx.Err(errorx.ValidateFailure, err.Error()),
				"component data[%s] unmarshal to proto message[%T] failure, error: %+v",
				data.String, v, err,
			)
		}

		if err := v.ValidateAll(); err != nil {
			return errors.Wrapf(
				errorx.Err(errorx.ValidateFailure, err.Error()),
				"component data[%s] convert to proto message[%T] failure, error: %+v",
				data.String, v, err,
			)
		}

		if f, ok := v.(pb.FunctionComponent); ok {
			for _, function := range f.Functions() {
				writer.Write(
					&model.FunctionReferenceRelationship{
						ProjectId:        req.ProjectId,
						ReferenceType:    constants.InterfaceCase,
						ReferenceId:      req.CaseId,
						ReferenceVersion: req.Version,
						FunctionName:     function.Name,
						FunctionType:     function.Type,
						CreatedBy:        l.currentUser.Account,
						UpdatedBy:        l.currentUser.Account,
						CreatedAt:        time.Now(),
						UpdatedAt:        time.Now(),
					},
				)
			}
		}
	}

	v := &model.Component{
		ProjectId:     req.ProjectId,
		ParentId:      req.CaseId,
		ParentType:    constants.InterfaceCase,
		ParentVersion: req.Version,
		ComponentId:   req.Component.ComponentId(),
		ComponentType: req.Component.ComponentType(),
		Name:          req.Component.ComponentName(),
		Description:   sql.NullString{},
		Data:          data,
		CreatedBy:     l.currentUser.Account,
		UpdatedBy:     l.currentUser.Account,
	}
	if _, err := l.svcCtx.ComponentModel.InsertTX(ctx, session, v); err != nil {
		return errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()),
			"failed to insert table[%s] with values[%+v], error: %+v",
			l.svcCtx.ComponentModel.Table(), v, err,
		)
	}

	return nil
}

func (l *BaseLogic) getElements(projectId, caseId, version string) (
	nodes []*pb.Node, edges []*pb.Edge, combos []*pb.Combo, err error,
) {
	m := l.svcCtx.InterfaceCaseElementModel

	err = mr.MapReduceVoid[any, pb.Element](
		func(source chan<- any) {
			elements, err := m.FindNoCacheByQuery(
				l.ctx, m.SelectBuilder().Where(
					squirrel.Eq{
						"`project_id`": projectId,
						"`case_id`":    caseId,
						"`version`":    version,
					},
				),
			)

			if err != nil {
				source <- errors.Wrapf(
					errorx.Err(errorx.DBError, err.Error()),
					"failed to find elements of interface case with project_id[%s], case_id[%s] and version[%s], error: %+v",
					projectId, caseId, version, err,
				)
			} else {
				// avoid returning null to the front end
				l := len(elements)
				nodes = make([]*pb.Node, 0, l)
				edges = make([]*pb.Edge, 0, l)
				combos = make([]*pb.Combo, 0, l)

				for _, element := range elements {
					source <- element
				}
			}
		}, func(item any, writer mr.Writer[pb.Element], cancel func(error)) {
			var err error
			defer func() {
				if err != nil {
					cancel(err)
				}
			}()

			switch v := item.(type) {
			case error:
				err = v
			case *model.InterfaceCaseElement:
				var e pb.Element
				switch v.ElementType {
				case constants.NODE:
					e = &pb.Node{}
				case constants.EDGE:
					e = &pb.Edge{}
				case constants.COMBO:
					e = &pb.Combo{}
				default:
					l.Logger.Warnf("the type[%s] of element[%+v] is not currently supported", v.ElementType, v)
				}

				if e != nil {
					if err = protobuf.UnmarshalJSONFromString(v.Data, e); err != nil {
						err = errors.Wrapf(
							errorx.Err(errorx.SerializationError, err.Error()),
							"failed to unmarshal element data[%s], error: %+v",
							v.Data, err,
						)
					} else {
						writer.Write(e)
					}
				}
			default:
				l.Logger.Warnf("the type[%T] of item is not currently supported", v)
			}
		}, func(pipe <-chan pb.Element, cancel func(error)) {
			for item := range pipe {
				switch v := item.(type) {
				case *pb.Node:
					nodes = append(nodes, v)
				case *pb.Edge:
					edges = append(edges, v)
				case *pb.Combo:
					combos = append(combos, v)
				default:
					l.Logger.Warnf("the type[%T] of item is not currently supported", v)
				}
			}
		}, mr.WithContext(l.ctx),
	)

	return nodes, edges, combos, err
}

// createApiPlanReference 创建API计划与接口文档、接口用例的引用关系
func (l *BaseLogic) createApiPlanReference(
	ctx context.Context, session sqlx.Session, req createApiPlanReferenceInternalReq,
) error {
	if ctx == nil {
		ctx = l.ctx
	}

	rrs, err := l.svcCtx.ApiPlanReferenceModel.FindReferenceByReference(
		l.ctx, req.ProjectId, common.ConstReferenceTypeInterfaceDocument, req.DocumentId, false,
	)
	if err != nil {
		return errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()),
			"failed to find api plan reference with project_id[%s], reference_type[%s] and reference_id[%s], error: %+v",
			req.ProjectId, common.ConstReferenceTypeInterfaceDocument, req.DocumentId, err,
		)
	} else if len(rrs) == 0 {
		return nil
	}

	workers := len(rrs)
	if workers > common.ConstMRMaxWorkers {
		workers = common.ConstMRMaxWorkers
	}

	cache := make(types.StringPlaceholderCache, len(rrs))

	var caseIds []string
	if req.CaseId == "" {
		ics, err := l.svcCtx.InterfaceCaseModel.FindLatestByDocumentId(ctx, req.ProjectId, req.DocumentId)
		if err != nil {
			return errors.Wrapf(
				errorx.Err(errorx.DBError, err.Error()),
				"failed to find interface cases with project_id[%s] and document_id[%s], error: %+v",
				req.ProjectId, req.DocumentId, err,
			)
		}

		for _, ic := range ics {
			caseIds = append(caseIds, ic.CaseId)
		}
	} else {
		caseIds = []string{req.CaseId}
	}

	return mr.MapReduceVoid[string, any](
		func(source chan<- string) {
			for _, rr := range rrs {
				if _, ok := cache[rr.PlanId]; ok {
					continue
				} else {
					cache[rr.PlanId] = lang.Placeholder
				}

				source <- rr.PlanId
			}
		}, func(item string, writer mr.Writer[any], cancel func(error)) {
			var err error
			defer func() {
				if err != nil {
					cancel(err)
				}
			}()

			err = l.addSuiteToApiPlanLogic.AddReferenceToApiPlanForInternal(
				ctx, session, apiplanservicelogic.CreateOrRemoveReferenceInternalReq{
					ProjectId:         req.ProjectId,
					ReferenceParentId: req.DocumentId,
					ReferenceType:     common.ConstReferenceTypeInterfaceCase,
					ReferenceIds:      caseIds,
					PlanId:            item,
					NoNeedToCheck:     true, // `req.CaseId`为空时是根据`req.DocumentId`查询的，无需再检查；`req.CaseId`非空（暂时只有创建接口用例）时数据库还没有该用例的数据，所以也无需检查
				},
			)
		}, func(pipe <-chan any, cancel func(error)) {
		}, mr.WithContext(ctx), mr.WithWorkers(workers),
	)
}

func (l *BaseLogic) setCategoryPathToDocument(projectId string, document *pb.Document) error {
	if document == nil {
		return nil
	}

	var (
		as  types.ArraySchema
		bds []*pb.BodyData
	)

	as = append(as, document.Headers...)
	as = append(as, document.PathParams...)
	as = append(as, document.QueryParams...)

	bds = append(bds, document.Body)

	for _, resp := range document.Responses {
		as = append(as, resp.Headers...)
		bds = append(bds, resp.Body)
	}

	for _, bd := range bds {
		if bd == nil {
			continue
		}

		if bd.Type == common.ConstMediaTypeFormData || bd.Type == common.ConstMediaTypeUrlEncoded {
			as = append(as, bd.Form...)
		} else if bd.Type == common.ConstMediaTypeApplicationJson {
			if err := l.setCategoryPathToSchema(projectId, bd.Json); err != nil {
				return err
			}
		}
	}

	for _, s := range as {
		if err := l.setCategoryPathToSchema(projectId, s); err != nil {
			return err
		}
	}

	return nil
}

func (l *BaseLogic) setCategoryPathToSchema(projectId string, schema *pb.Schema) error {
	if schema == nil {
		return nil
	}

	switch schema.Type {
	case common.ConstInterfaceDefinitionFieldTypeSchema:
		if schema.Ref != nil {
			o, err := l.svcCtx.CategoryModel.FindOneByNodeId(
				l.ctx, projectId, common.ConstCategoryTreeTypeInterfaceSchema, schema.Ref.SchemaId,
			)
			if err != nil {
				return errors.Wrapf(
					errorx.Err(errorx.DBError, err.Error()),
					"failed to find category with project_id[%s], type[%s] and schema_id[%s], error: %+v",
					projectId, common.ConstCategoryTreeTypeInterfaceSchema, schema.Ref.SchemaId, err,
				)
			}

			cs, err := l.svcCtx.CategoryModel.FindAncestralCategories(
				l.ctx, model.GetCategoryTreeCondition{
					ProjectId:  projectId,
					Type:       common.ConstCategoryTreeTypeInterfaceSchema,
					CategoryId: o.CategoryId,
				},
			)
			if err != nil {
				return errors.Wrapf(
					errorx.Err(errorx.DBError, err.Error()),
					"failed to find ancestral categories of the interface schema category[%s], error: %+v",
					o.Name, err,
				)
			}

			schema.Ref.CategoryPath = make([]*pb.CategoryNode, 0, len(cs))
			for _, c := range cs {
				schema.Ref.CategoryPath = append(
					schema.Ref.CategoryPath, &pb.CategoryNode{
						ProjectId:    c.ProjectId,
						CategoryId:   c.CategoryId,
						Type:         c.Type,
						CategoryType: c.CategoryType,
						RootType:     c.RootType.String,
						NodeType:     c.NodeType.String,
						NodeId:       c.NodeId.String,
						Name:         c.Name,
						Description:  c.Description.String,
						Builtin:      c.Builtin == 1,
					},
				)
			}
		}
	case common.ConstInterfaceDefinitionFieldTypeArray:
		if schema.Items != nil {
			return l.setCategoryPathToSchema(projectId, schema.Items)
		}
	case common.ConstInterfaceDefinitionFieldTypeObject, common.ConstInterfaceDefinitionFieldTypeAllOf,
		common.ConstInterfaceDefinitionFieldTypeAnyOf, common.ConstInterfaceDefinitionFieldTypeOneOf:
		for _, s := range schema.Properties {
			if s == nil {
				continue
			}

			if err := l.setCategoryPathToSchema(projectId, s); err != nil {
				return err
			}
		}
	}

	return nil
}

func (l *BaseLogic) getUserInfoByAccount(account string) (*userpb.UserInfo, error) {
	resp, err := l.svcCtx.UserRPC.ViewUser(
		l.ctx, &userpb.GetUserReq{
			Account: account,
		},
	)
	if err != nil {
		return nil, err
	}

	return resp.GetUserInfo(), nil
}

func (l *BaseLogic) getTeamByMethod(method string) (string, error) {
	service, err := l.getServiceByMethod(method)
	if err != nil {
		return "", err
	}

	return l.getTeamByService(service)
}

func (l *BaseLogic) getServiceByMethod(method string) (string, error) {
	v, err := l.singleFlight.Do(
		method, func() (any, error) {
			l.serviceLock.RLock()
			service, ok := l.serviceCache[method]
			l.serviceLock.RUnlock()
			if ok {
				return service, nil
			}

			var err error
			defer func() {
				if err == nil && service != "" {
					l.serviceLock.Lock()
					_, ok = l.serviceCache[method]
					if !ok {
						l.serviceCache[method] = service
					}
					l.serviceLock.Unlock()
				}
			}()

			out, err := l.svcCtx.RelationRPC.GetServiceByMethod(
				l.ctx, &relationpb.GetServiceByMethodReq{Method: method},
			)
			if err != nil {
				return "", err
			}

			if len(out.GetRelations()) == 0 {
				return "", errorx.Errorf(errorx.NotExists, "not found any service by method, method: %s", method)
			}

			service = out.GetRelations()[0].GetService()
			if len(service) == 0 {
				return "", errorx.Errorf(errorx.NotExists, "the service is empty, method: %s", method)
			}

			return service, nil
		},
	)
	if err != nil {
		return "", err
	}

	service, ok := v.(string)
	if !ok {
		return "", errorx.Errorf(errorx.TypeError, "the type of result is not string, result: %+v", v)
	}

	return service, nil
}

func (l *BaseLogic) getTeamByService(service string) (string, error) {
	v, err := l.singleFlight.Do(
		service, func() (any, error) {
			l.teamLock.RLock()
			team, ok := l.teamCache[service]
			l.teamLock.RUnlock()
			if ok {
				return team, nil
			}

			var err error
			defer func() {
				if err == nil && team != "" {
					l.teamLock.Lock()
					_, ok = l.teamCache[service]
					if !ok {
						l.teamCache[service] = team
					}
					l.teamLock.Unlock()
				}
			}()

			out, err := l.svcCtx.RelationRPC.GetTeamByService(l.ctx, &relationpb.GetTeamByServiceReq{Service: service})
			if err != nil {
				return "", err
			}

			if len(out.GetRelations()) == 0 {
				return "", errorx.Errorf(errorx.NotExists, "not found any team by service, service: %s", service)
			}

			team = out.GetRelations()[0].GetTeamName()
			if len(team) == 0 {
				return "", errorx.Errorf(errorx.NotExists, "the team is empty, service: %s", service)
			}

			return team, nil
		},
	)
	if err != nil {
		return "", err
	}

	team, ok := v.(string)
	if !ok {
		return "", errorx.Errorf(errorx.TypeError, "the type of result is not string, result: %+v", v)
	}

	return team, nil
}
