package interfacedefinitionservicelogic

import (
	"context"

	"github.com/dromara/carbon/v2"
	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type GetInterfaceCoverageDataLogic struct {
	*BaseLogic
}

func NewGetInterfaceCoverageDataLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetInterfaceCoverageDataLogic {
	return &GetInterfaceCoverageDataLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// GetInterfaceCoverageData 获取接口覆盖率数据
func (l *GetInterfaceCoverageDataLogic) GetInterfaceCoverageData(in *pb.GetInterfaceCoverageDataReq) (
	out *pb.GetInterfaceCoverageDataResp, err error,
) {
	from, to, err := l.validate(in)
	if err != nil {
		return nil, err
	}

	days := from.DiffInDays(to)
	out = &pb.GetInterfaceCoverageDataResp{
		Data: &pb.InterfaceCoverageData{
			NumberOfApis:  make([]uint32, days+1),
			NumberOfCases: make([]uint32, days+1),
			CountedAt:     make([]string, days+1),
		},
	}

	data, err := l.svcCtx.InterfaceCoverageModel.FindCoverageData(
		l.ctx, in.GetProjectId(), in.GetTeam(), in.GetFrom(), in.GetTo(),
	)
	if err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()),
			"failed to find the interface coverage data, project_id: %s, team: %s, from: %s, to: %s, error: %+v",
			in.GetProjectId(), in.GetTeam(), in.GetFrom(), in.GetTo(), err,
		)
	}

	cache := make(map[string]*model.InterfaceCoverage, len(data))
	for _, v := range data {
		cache[v.CountedAt.Format("2006-01-02")] = v
	}

	date := from
	for i := int64(0); i <= days; i++ {
		if i != 0 {
			date = date.AddDay()
		}

		countedAt := date.Layout("2006-01-02")
		if v, ok := cache[countedAt]; ok {
			out.Data.NumberOfApis[i] = uint32(v.NumberOfApis)
			out.Data.NumberOfCases[i] = uint32(v.NumberOfCases)
			out.Data.CountedAt[i] = countedAt
		} else {
			out.Data.NumberOfApis[i] = 0
			out.Data.NumberOfCases[i] = 0
			out.Data.CountedAt[i] = countedAt
		}
	}
	return out, nil
}

func (l *GetInterfaceCoverageDataLogic) validate(in *pb.GetInterfaceCoverageDataReq) (
	fromC, toC carbon.Carbon, err error,
) {
	var (
		projectID = in.GetProjectId()
		from      = in.GetFrom()
		to        = in.GetTo()
	)

	// validate the `project_id` in req
	project, err := model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, projectID)
	if err != nil {
		return fromC, toC, err
	}
	if project.CoverageEnabled == 0 {
		return fromC, toC, errorx.Errorf(
			errorx.ProhibitedBehavior,
			"the project has not enabled the interface coverage function, project_id: %s",
			projectID,
		)
	}

	// validate the `from` in req
	fromC = carbon.Parse(from, carbon.Local)
	if fromC.Error != nil {
		return fromC, toC, errors.Wrapf(
			errorx.Err(errorx.ValidateParamError, fromC.Error.Error()),
			"failed to parse the `from` datetime, from: %s, error: %+v",
			from, fromC.Error,
		)
	}

	// validate the `to` in req
	toC = carbon.Parse(to, carbon.Local)
	if toC.Error != nil {
		return fromC, toC, errors.Wrapf(
			errorx.Err(errorx.ValidateParamError, toC.Error.Error()),
			"failed to parse the `to` datetime, to: %s, error: %+v",
			to, toC.Error,
		)
	}

	if toC.Lt(fromC) {
		return fromC, toC, errorx.Errorf(
			errorx.ValidateParamError,
			"the `to` datetime must be greater than the `from` datetime, from: %s, to: %s",
			from, to,
		)
	}

	return fromC, toC, nil
}
