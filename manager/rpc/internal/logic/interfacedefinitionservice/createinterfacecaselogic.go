package interfacedefinitionservicelogic

import (
	"context"
	"database/sql"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"
	"github.com/zeromicro/go-zero/core/stores/sqlx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
	commonutils "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	userpb "gitlab.ttyuyin.com/TestDevelopment/qet-backend-middleware/user/rpc/pb"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/utils"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/logic/fsm"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type CreateInterfaceCaseLogic struct {
	*BaseLogic
}

func NewCreateInterfaceCaseLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CreateInterfaceCaseLogic {
	return &CreateInterfaceCaseLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// CreateInterfaceCase 创建接口用例
func (l *CreateInterfaceCaseLogic) CreateInterfaceCase(in *pb.CreateInterfaceCaseReq) (
	resp *pb.CreateInterfaceCaseResp, err error,
) {
	// validate the project_id in req
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, in.GetProjectId()); err != nil {
		return nil, err
	}

	// validate the document_id in req
	if _, err = model.CheckInterfaceDocumentByDocumentId(
		l.ctx, l.svcCtx.InterfaceDocumentModel, in.GetProjectId(), in.GetDocumentId(),
	); err != nil {
		return nil, err
	}

	// validate the circular reference exists
	if err = logic.CheckCircularReference(
		l.ctx, l.svcCtx.ComponentGroupModel, in.GetProjectId(), in.GetRelations(),
	); err != nil {
		return nil, err
	}

	interfaceCase, err := l.create(in)
	if err != nil {
		return nil, err
	}

	resp = &pb.CreateInterfaceCaseResp{Case: &pb.InterfaceCase{}}
	if err = commonutils.Copy(resp.Case, interfaceCase, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy interface case[%+v] to response, error: %+v",
			interfaceCase, err,
		)
	}

	return resp, nil
}

// in order to reduce cyclomatic complexity of CreateInterfaceCaseLogic.CreateInterfaceCase
func (l *CreateInterfaceCaseLogic) create(req *pb.CreateInterfaceCaseReq) (*model.InterfaceCase, error) {
	caseId, err := l.generateCaseId(req.GetProjectId(), req.GetDocumentId())
	if err != nil {
		return nil, err
	}
	version := utils.GenVersion()

	// 由于`protojson`只能序列化`[]proto.Message`，而`tags`字段类型为`[]string`，Go Struct跟PB没有大的区别，因此直接使用`jsonx`暂代`protojson`
	tags := jsonx.MarshalToStringIgnoreError(req.GetTags())

	var user *userpb.UserInfo
	maintainedBy := req.GetMaintainedBy()
	if maintainedBy != "" {
		if user, err = l.getUserInfoByAccount(maintainedBy); err != nil {
			return nil, err
		} else if !user.GetEnabled() {
			return nil, errorx.Errorf(
				errorx.ProhibitedBehavior,
				"cannot assign a disabled user as a maintainer, project_id: %s, maintained_by: %s",
				req.GetProjectId(), maintainedBy,
			)
		}
	}

	// [2024-04-10] delete this rule:
	//if maintainedBy == "" {
	//	// if the maintainer is not set, the creator is set as the maintainer
	//	maintainedBy = l.currentUser.Account
	//}

	interfaceCase := &model.InterfaceCase{
		ProjectId:  req.GetProjectId(),
		DocumentId: req.GetDocumentId(),
		CaseId:     caseId,
		Name:       req.GetName(),
		Description: sql.NullString{
			String: req.GetDescription(),
			Valid:  req.GetDescription() != "",
		},
		Priority: req.GetPriority(),
		Tags: sql.NullString{
			String: tags,
			Valid:  tags != "",
		},
		State:         string(common.ConstResourceStateNew),
		AccountConfig: protobuf.MarshalJSONToStringIgnoreError(req.GetAccountConfig()),
		Version:       version,
		Structure:     protobuf.MarshalJSONWithMessagesToStringIgnoreError(req.GetRelations()),
		Latest:        int64(constants.IsLatestVersion),
		MaintainedBy:  sql.NullString{}, // 为了避免出现状态为「新」且维护者非空的情况，维护者由`fsm`进行修改
		CreatedBy:     l.currentUser.Account,
		UpdatedBy:     l.currentUser.Account,
	}

	// create interface case in a transaction
	if err = l.svcCtx.InterfaceCaseModel.Trans(
		l.ctx, func(context context.Context, session sqlx.Session) error {
			if _, err := l.svcCtx.InterfaceCaseModel.InsertTX(context, session, interfaceCase); err != nil {
				return errors.Wrapf(
					errorx.Err(errorx.DBError, err.Error()),
					"failed to insert table[%s] with values[%+v], error: %+v",
					l.svcCtx.InterfaceCaseModel.Table(), interfaceCase, err,
				)
			}

			// create component group reference relationship
			if err := l.createComponentGroupReference(
				context, session, createComponentGroupReferenceInternalReq{
					ProjectId: interfaceCase.ProjectId,
					CaseId:    interfaceCase.CaseId,
					Version:   interfaceCase.Version,
					Relations: req.GetRelations(),
				},
			); err != nil {
				return err
			}

			// create the elements and components of the interface case
			if err := l.createElementAndComponent(
				context, session, createElementAndComponentInternalReq{
					ProjectId: interfaceCase.ProjectId,
					CaseId:    interfaceCase.CaseId,
					Version:   interfaceCase.Version,
					Nodes:     req.GetNodes(),
					Edges:     req.GetEdges(),
					Combos:    req.GetCombos(),
				},
			); err != nil {
				return err
			}

			// create the new tag and tag reference of interface case
			if err := l.createTagLogic.CreateTagAndReferenceForInternal(
				context, session, types.CreateOrUpdateTagReference{
					ProjectId:        interfaceCase.ProjectId,
					ReferenceType:    common.ConstReferenceTypeInterfaceCase,
					ReferenceId:      interfaceCase.CaseId,
					ReferenceVersion: interfaceCase.Version,
					Tags:             req.GetTags(),
				},
			); err != nil {
				return err
			}

			// create api plan reference of interface case if interface document in any api plan
			if err := l.createApiPlanReference(
				context, session, createApiPlanReferenceInternalReq{
					ProjectId:  interfaceCase.ProjectId,
					DocumentId: interfaceCase.DocumentId,
					CaseId:     interfaceCase.CaseId,
				},
			); err != nil {
				return err
			}

			return nil
		},
	); err != nil {
		return nil, err
	}

	// [2024-04-10] add a new rule as follows:
	if user != nil {
		// get the latest version of interface case again
		// 注意：这里需要重新获取最新版本的数据，因为需要获取新的`id`
		interfaceCase, err = model.CheckInterfaceCaseByCaseId(
			l.ctx, l.svcCtx.InterfaceCaseModel, req.GetProjectId(), req.GetDocumentId(), caseId, "",
		)
		if err != nil {
			return nil, err
		}

		interfaceCase.MaintainedBy = sql.NullString{
			String: maintainedBy,
			Valid:  maintainedBy != "",
		}

		event := common.ConstReviewResourceEventAssignedToTheResponsiblePerson
		if err = fsm.NewResourceFSM(
			l.ctx, l.svcCtx, &ResourceOfInterfaceCase{
				InterfaceCase: interfaceCase,
				Event:         event,
				Emails:        []string{user.GetEmail()},
			}, nil,
		).TransitionWithoutLock(); err != nil {
			return nil, errors.Wrapf(
				errorx.Err(errorx.FSMError, err.Error()),
				"got an error regarding state transition after creating an interface case, project_id: %s, resource_id: %s, event: %s, error: %+v",
				req.GetProjectId(), interfaceCase.CaseId, event, err,
			)
		}
	}

	return interfaceCase, nil
}
