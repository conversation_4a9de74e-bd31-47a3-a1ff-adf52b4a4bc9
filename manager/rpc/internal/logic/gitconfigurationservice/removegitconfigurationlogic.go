package gitconfigurationservicelogic

import (
	"context"
	"fmt"

	"github.com/hashicorp/go-multierror"
	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/mr"
	"github.com/zeromicro/go-zero/core/stores/sqlx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/redislock"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type RemoveGitConfigurationLogic struct {
	*BaseLogic
}

func NewRemoveGitConfigurationLogic(ctx context.Context, svcCtx *svc.ServiceContext) *RemoveGitConfigurationLogic {
	return &RemoveGitConfigurationLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// RemoveGitConfiguration 删除Git配置
func (l *RemoveGitConfigurationLogic) RemoveGitConfiguration(in *pb.RemoveGitConfigurationReq) (
	resp *pb.RemoveGitConfigurationResp, err error,
) {
	// validate the project_id in req
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, in.GetProjectId()); err != nil {
		return nil, err
	}

	configIds := in.GetConfigIds()
	workers := len(configIds)
	if workers > common.ConstMRMaxWorkers {
		workers = common.ConstMRMaxWorkers
	}

	mr.ForEach(
		func(source chan<- any) {
			for _, configId := range configIds {
				source <- configId
			}
		}, func(item any) {
			configId, ok := item.(string)
			if !ok {
				err = multierror.Append(
					err, errorx.Err(
						errorx.InternalError,
						fmt.Sprintf("the gitConfiguration id[%v (%T)] is not a string", item, item),
					),
				)
			} else {
				if e := l.remove(in.GetProjectId(), configId); e != nil {
					err = multierror.Append(err, e)
				}
			}
		}, mr.WithContext(l.ctx), mr.WithWorkers(workers),
	)

	return &pb.RemoveGitConfigurationResp{}, err
}

func (l *RemoveGitConfigurationLogic) remove(projectId, configId string) (err error) {
	// validate the config_id in req
	_, err = model.CheckGitConfigByConfigID(l.ctx, l.svcCtx.GitConfigModel, projectId, configId)
	if err != nil {
		return err
	}

	// 获取redis锁
	key := fmt.Sprintf("%s:%s:%s", common.ConstLockGitConfigProjectIDConfigIDPrefix, projectId, configId)
	lock, err := redislock.NewRedisLockAndAcquire(l.svcCtx.Redis, key, redislock.WithExpire(common.ConstLockExpireTime))
	if err != nil {
		return err
	}
	defer func() {
		// 释放redis锁定
		e := lock.Release()
		if e != nil {
			if re, ok := errorx.RootError(e); ok && re.Code() != errorx.ReleaseRedisLockFailure && err == nil {
				err = e
			}
		}
	}()

	return l.svcCtx.GitConfigModel.Trans(
		l.ctx, func(context context.Context, session sqlx.Session) error {
			// Table: notify
			//if _, err := l.svcCtx.NotifyModel.RemoveByPlanId(context, session, projectId, configId); err != nil {
			//	return errors.Wrapf(
			//		errorx.Err(errorx.DBError, err.Error()),
			//		"failed to remove notify with project_id[%s] and plan_id[%s], error: %+v", projectId, configId, err,
			//	)
			//}

			// Table: GitConfig
			if _, err := l.svcCtx.GitConfigModel.RemoveByConfigId(context, session, projectId, configId); err != nil {
				return errors.Wrapf(
					errorx.Err(errorx.DBError, err.Error()),
					"failed to remove git config with project_id[%s] and config_id[%s], error: %+v", projectId,
					configId, err,
				)
			}

			return nil
		},
	)
}
