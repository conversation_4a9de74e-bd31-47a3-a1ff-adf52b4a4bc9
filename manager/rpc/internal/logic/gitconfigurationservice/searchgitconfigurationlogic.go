package gitconfigurationservicelogic

import (
	"context"
	"math"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sqlbuilder/rpc"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type SearchGitConfigurationLogic struct {
	*BaseLogic
}

func NewSearchGitConfigurationLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SearchGitConfigurationLogic {
	return &SearchGitConfigurationLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// SearchGitConfiguration 搜索Git配置
func (l *SearchGitConfigurationLogic) SearchGitConfiguration(in *pb.SearchGitConfigurationReq) (
	resp *pb.SearchGitConfigurationResp, err error,
) {
	resp = &pb.SearchGitConfigurationResp{}

	// validate the project_id in req
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, in.GetProjectId()); err != nil {
		return nil, err
	}

	selectBuilder, countBuilder := l.svcCtx.GitConfigModel.GenerateSearchGitConfigurationSqlBuilder(
		model.SearchGitConfigurationReq{
			ProjectId:  in.GetProjectId(),
			Condition:  in.GetCondition(),
			Pagination: in.GetPagination(),
			Sort:       rpc.ConvertSortFields(in.GetSort()),
		},
	)

	count, err := l.svcCtx.GitConfigModel.FindCountGitConfigurations(l.ctx, countBuilder)
	if err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()),
			"failed to count git configurations, project_id: %s, error: %+v",
			in.GetProjectId(), err,
		)
	}

	resp.TotalCount = uint64(count)
	resp.TotalPage = 1

	gitConfigurations, err := l.svcCtx.GitConfigModel.FindGitConfigurations(l.ctx, selectBuilder)
	if err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()),
			"failed to find git configurations, project_id: %s, error: %+v",
			in.GetProjectId(), err,
		)
	}

	resp.Items = make([]*pb.GitConfiguration, 0, len(gitConfigurations))
	for _, gitConfiguration := range gitConfigurations {
		item := &pb.GitConfiguration{}
		if err = utils.Copy(item, gitConfiguration, l.converters...); err != nil {
			return nil, errors.Wrapf(
				errorx.Err(errorx.CopyToStructFailure, err.Error()),
				"failed to copy git configurations to response, config: %s, error: %+v",
				jsonx.MarshalIgnoreError(gitConfiguration), err,
			)
		}

		resp.Items = append(resp.Items, item)
	}

	pagination := in.GetPagination()
	if pagination != nil {
		resp.CurrentPage = pagination.GetCurrentPage()
		resp.PageSize = pagination.GetPageSize()
		resp.TotalPage = uint64(math.Ceil(float64(resp.TotalCount) / float64(resp.PageSize)))
	}

	return resp, nil
}
