package gitconfigurationservicelogic

import (
	"context"
	"database/sql"
	"time"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"
	"github.com/zeromicro/go-zero/core/stores/sqlx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type CreateGitConfigurationLogic struct {
	*BaseLogic
}

func NewCreateGitConfigurationLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CreateGitConfigurationLogic {
	return &CreateGitConfigurationLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// CreateGitConfiguration 创建Git配置
func (l *CreateGitConfigurationLogic) CreateGitConfiguration(in *pb.CreateGitConfigurationReq) (
	resp *pb.CreateGitConfigurationResp, err error,
) {
	// validate the project_id in req
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, in.GetProjectId()); err != nil {
		return nil, err
	}

	configuration, err := l.create(in)
	if err != nil {
		return nil, err
	}

	resp = &pb.CreateGitConfigurationResp{Configuration: &pb.GitConfiguration{}}
	if err = utils.Copy(resp.Configuration, configuration, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy git configuration to response, config: %s, error: %+v",
			jsonx.MarshalIgnoreError(configuration), err,
		)
	}

	return resp, nil
}

func (l *CreateGitConfigurationLogic) create(req *pb.CreateGitConfigurationReq) (*model.GitConfiguration, error) {
	configId, err := l.generateConfigID(req.GetProjectId())
	if err != nil {
		return nil, err
	}

	now := time.Now()
	accessToken := req.GetAccessToken()
	if req.GetType() == common.ConstGitTypeGitLab && accessToken == "" {
		accessToken = l.svcCtx.Config.GitLab.Token
	}
	gitConfiguration := &model.GitConfiguration{
		ProjectId: req.GetProjectId(),
		ConfigId:  configId,
		Type:      req.GetType(),
		Name:      req.GetName(),
		Description: sql.NullString{
			String: req.GetDescription(),
			Valid:  req.GetDescription() != "",
		},

		Url:         req.GetUrl(),
		AccessToken: accessToken,
		Branch:      req.GetBranch(),
		Purpose:     req.GetPurpose(),

		CreatedBy: l.currentUser.Account,
		UpdatedBy: l.currentUser.Account,
		CreatedAt: now,
		UpdatedAt: now,
	}
	if err = l.svcCtx.GitConfigModel.Trans(
		l.ctx, func(context context.Context, session sqlx.Session) error {
			if _, err := l.svcCtx.GitConfigModel.InsertTX(context, session, gitConfiguration); err != nil {
				return errors.Wrapf(
					errorx.Err(errorx.DBError, err.Error()),
					"failed to insert values to table, table: %s, values: %s, error: %+v",
					l.svcCtx.GitConfigModel.Table(), jsonx.MarshalIgnoreError(gitConfiguration), err,
				)
			}

			return nil
		},
	); err != nil {
		return nil, err
	}

	return gitConfiguration, nil
}
