package gitconfigurationservicelogic

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type ViewGitConfigurationLogic struct {
	*BaseLogic
}

func NewViewGitConfigurationLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ViewGitConfigurationLogic {
	return &ViewGitConfigurationLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// ViewGitConfiguration 查看Git配置
func (l *ViewGitConfigurationLogic) ViewGitConfiguration(in *pb.ViewGitConfigurationReq) (
	resp *pb.ViewGitConfigurationResp, err error,
) {
	// validate the project_id in req
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, in.GetProjectId()); err != nil {
		return nil, err
	}

	// validate the config_id in req
	gitConfiguration, err := model.CheckGitConfigByConfigID(
		l.ctx, l.svcCtx.GitConfigModel, in.GetProjectId(), in.GetConfigId(),
	)
	if err != nil {
		return nil, err
	}

	resp = &pb.ViewGitConfigurationResp{Configuration: &pb.GitConfiguration{}}
	if err = utils.Copy(resp.Configuration, gitConfiguration, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy gitConfiguration[%+v] to response, error: %+v", gitConfiguration, err,
		)
	}

	return resp, nil
}
