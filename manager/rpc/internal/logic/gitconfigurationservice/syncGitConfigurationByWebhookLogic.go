package gitconfigurationservicelogic

import (
	"context"
	"time"

	"github.com/RichardKnop/machinery/v2/tasks"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/userinfo"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	commonpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type SyncGitConfigurationByWebhookLogic struct {
	*BaseLogic
}

func NewSyncGitConfigurationByWebhookLogic(
	ctx context.Context, svcCtx *svc.ServiceContext,
) *SyncGitConfigurationByWebhookLogic {
	return &SyncGitConfigurationByWebhookLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// SyncGitConfigurationByWebhook 通过Webhook同步Git配置对应的Git项目测试数据
func (l *SyncGitConfigurationByWebhookLogic) SyncGitConfigurationByWebhook(in *pb.SyncGitConfigurationByWebhookReq) (
	out *pb.SyncGitConfigurationByWebhookResp, err error,
) {
	gitConfigs, err := l.svcCtx.GitConfigModel.FindGitConfigurationsByUrlAndBranch(
		l.ctx, in.GetGitUrl(), in.GetTargetBranch(),
	)
	if err != nil {
		return nil, err
	}

	// TODO: get the user info by `email` in req
	// get the user info from context
	ui := userinfo.FromContext(l.ctx)
	if ui == nil {
		return nil, errorx.Errorf(
			errorx.AuthError,
			"not found the user info in context while synchronizing git configuration by webhook, url[%s], target_branch[%s] and email[%s]",
			in.GetGitUrl(), in.GetTargetBranch(), in.GetEmail(),
		)
	}

	for _, gitConfig := range gitConfigs {
		if gitConfig.Purpose != string(common.ConstGitPurposeUI) {
			l.Infof(
				"no need to sync cause by the purpose of git configuration is not %q, project_id: %s, config_id: %s",
				common.ConstGitPurposeUI, gitConfig.ProjectId, gitConfig.ConfigId,
			)
			continue
		}

		taskData := &commonpb.ParsePythonProjectTaskInfo{
			ProjectId: gitConfig.ProjectId,
			Config: &commonpb.GitConfig{
				ProjectId:   gitConfig.ProjectId,
				ConfigId:    gitConfig.ConfigId,
				Type:        gitConfig.Type,
				Name:        gitConfig.Name,
				Description: gitConfig.Description.String,
				Url:         gitConfig.Url,
				AccessToken: gitConfig.AccessToken,
				Branch:      gitConfig.Branch,
			},
			TriggerMode:    commonpb.TriggerMode_INTERFACE,
			TriggerAccount: ui.Account,
			TriggerTime:    time.Now().UnixMilli(),
		}

		taskSign := &tasks.Signature{
			RoutingKey: l.svcCtx.Config.UIWorkerProducer.Queue,
			Name:       constants.MQTaskTypeWorkerParsePythonProject,
			Args: []tasks.Arg{
				{
					Value: protobuf.MarshalJSONIgnoreError(taskData),
					Type:  "[]byte",
				},
			},
		}

		if _, err = l.svcCtx.WorkerProducerV1.AsyncPush(l.ctx, taskSign, l.svcCtx.Config.Name); err != nil {
			l.Logger.Errorf(
				"failed to create task of parse python project[%s, %s, %s], error: %+v",
				taskData.ProjectId, taskData.Config.ConfigId, taskData.Config.Name, err,
			)
			return nil, err
		}
	}

	return &pb.SyncGitConfigurationByWebhookResp{}, nil
}
