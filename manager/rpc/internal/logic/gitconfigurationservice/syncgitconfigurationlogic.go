package gitconfigurationservicelogic

import (
	"context"
	"time"

	"github.com/RichardKnop/machinery/v2/tasks"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/userinfo"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	commonpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type SyncGitConfigurationLogic struct {
	*BaseLogic
}

func NewSyncGitConfigurationLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SyncGitConfigurationLogic {
	return &SyncGitConfigurationLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// SyncGitConfiguration 同步Git配置对应的Git项目测试数据
func (l *SyncGitConfigurationLogic) SyncGitConfiguration(in *pb.SyncGitConfigurationReq) (
	resp *pb.SyncGitConfigurationResp, err error,
) {
	// validate the project_id in req
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, in.GetProjectId()); err != nil {
		return nil, err
	}

	// validate the config_id in req
	gitConfig, err := model.CheckGitConfigByConfigID(
		l.ctx, l.svcCtx.GitConfigModel, in.GetProjectId(), in.GetConfigId(),
	)
	if err != nil {
		return nil, err
	}

	if gitConfig.Purpose != string(common.ConstGitPurposeUI) {
		l.Infof(
			"no need to sync cause by the purpose of git configuration is not %q, project_id: %s, config_id: %s",
			common.ConstGitPurposeUI, in.GetProjectId(), in.GetConfigId(),
		)
		return &pb.SyncGitConfigurationResp{}, nil
	}

	// validate the trigger_mode in req
	if in.GetTriggerMode() == commonpb.TriggerMode_NULL {
		in.TriggerMode = commonpb.TriggerMode_MANUAL
	}

	// get the user info from context
	ui := userinfo.FromContext(l.ctx)
	if ui == nil {
		return nil, errorx.Errorf(
			errorx.AuthError,
			"not found the user info in context while synchronizing git configuration, project_id[%s] and config_id[%s]",
			in.GetProjectId(), in.GetConfigId(),
		)
	}

	taskData := &commonpb.ParsePythonProjectTaskInfo{
		ProjectId: in.GetProjectId(),
		Config: &commonpb.GitConfig{
			ProjectId:   in.GetProjectId(),
			ConfigId:    in.GetConfigId(),
			Type:        gitConfig.Type,
			Name:        gitConfig.Name,
			Description: gitConfig.Description.String,
			Url:         gitConfig.Url,
			AccessToken: gitConfig.AccessToken,
			Branch:      gitConfig.Branch,
		},
		TriggerMode:    in.GetTriggerMode(),
		TriggerAccount: ui.Account,
		TriggerTime:    time.Now().UnixMilli(),
	}

	taskSign := &tasks.Signature{
		RoutingKey: l.svcCtx.Config.UIWorkerProducer.Queue,
		Name:       constants.MQTaskTypeWorkerParsePythonProject,
		Args: []tasks.Arg{
			{
				Value: protobuf.MarshalJSONIgnoreError(taskData),
				Type:  "[]byte",
			},
		},
		/*OnSuccess: []*tasks.Signature{
			{
				RoutingKey: l.svcCtx.Config.WorkerConsumerV2.Queue,
				Name:       constants.MQTaskTypeManagerHandleParsePythonProjectResult,
			},
		},*/
	}

	if _, err = l.svcCtx.WorkerProducerV1.AsyncPush(l.ctx, taskSign, l.svcCtx.Config.Name); err != nil {
		l.Logger.Errorf(
			"failed to create task of parse python project[%s, %s, %s], error: %+v", taskData.ProjectId,
			taskData.Config.ConfigId, taskData.Config.Name, err,
		)
		return nil, err
	}

	return &pb.SyncGitConfigurationResp{}, nil
}
