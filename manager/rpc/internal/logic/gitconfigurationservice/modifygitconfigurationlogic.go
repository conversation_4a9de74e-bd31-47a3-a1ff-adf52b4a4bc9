package gitconfigurationservicelogic

import (
	"context"
	"database/sql"
	"fmt"
	"time"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/stores/sqlx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/redislock"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type ModifyGitConfigurationLogic struct {
	*BaseLogic
}

func NewModifyGitConfigurationLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ModifyGitConfigurationLogic {
	return &ModifyGitConfigurationLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// ModifyGitConfiguration 编辑Git配置
func (l *ModifyGitConfigurationLogic) ModifyGitConfiguration(in *pb.ModifyGitConfigurationReq) (
	resp *pb.ModifyGitConfigurationResp, err error,
) {
	// validate the project_id in req
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, in.GetProjectId()); err != nil {
		return nil, err
	}

	// validate the config_id in req
	origin, err := model.CheckGitConfigByConfigID(l.ctx, l.svcCtx.GitConfigModel, in.GetProjectId(), in.GetConfigId())
	if err != nil {
		return nil, err
	}

	// 获取redis锁
	key := fmt.Sprintf(
		"%s:%s:%s", common.ConstLockGitConfigProjectIDConfigIDPrefix, in.GetProjectId(), in.GetConfigId(),
	)
	lock, err := redislock.NewRedisLockAndAcquire(l.svcCtx.Redis, key, redislock.WithExpire(common.ConstLockExpireTime))
	if err != nil {
		return nil, err
	}
	defer func() {
		// release redis lock
		e := lock.Release()
		if e != nil {
			if re, ok := errorx.RootError(e); ok && re.Code() != errorx.ReleaseRedisLockFailure && err == nil {
				err = e
			}
		}
	}()

	configuration, err := l.modify(in, origin)
	if err != nil {
		return nil, err
	}

	resp = &pb.ModifyGitConfigurationResp{Configuration: &pb.GitConfiguration{}}
	if err = utils.Copy(resp.Configuration, configuration, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy configuration[%+v] to response, error: %+v", configuration, err,
		)
	}

	return resp, nil
}

func (l *ModifyGitConfigurationLogic) modify(
	req *pb.ModifyGitConfigurationReq, origin *model.GitConfiguration,
) (*model.GitConfiguration, error) {
	gitConfiguration := &model.GitConfiguration{
		Id:        origin.Id,
		ProjectId: origin.ProjectId,
		ConfigId:  origin.ConfigId,
		Type:      origin.Type,
		Name:      req.GetName(),
		Description: sql.NullString{
			String: req.GetDescription(),
			Valid:  req.GetDescription() != "",
		},
		Url:         origin.Url,
		AccessToken: req.GetAccessToken(),
		Branch:      origin.Branch,
		Purpose:     origin.Purpose,
		CreatedBy:   origin.CreatedBy,
		UpdatedBy:   l.currentUser.Account,
		CreatedAt:   origin.CreatedAt,
		UpdatedAt:   time.Now(),
	}

	if err := l.svcCtx.GitConfigModel.Trans(
		l.ctx, func(context context.Context, session sqlx.Session) error {
			if _, err := l.svcCtx.GitConfigModel.UpdateTX(l.ctx, nil, gitConfiguration); err != nil {
				return errors.Wrapf(
					errorx.Err(errorx.DBError, err.Error()),
					"failed to update table[%s] with values[%+v], error: %+v",
					l.svcCtx.GitConfigModel.Table(), gitConfiguration, err,
				)
			}

			return nil
		},
	); err != nil {
		return nil, err
	}

	return gitConfiguration, nil
}
