package gitconfigurationservicelogic

import (
	"context"
	"testing"

	"github.com/zeromicro/go-zero/core/conf"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/config"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
)

func TestGetBranches(t *testing.T) {
	var (
		c          config.Config
		configFile = "../../../etc/manager.yaml"
	)
	conf.MustLoad(configFile, &c)
	svcCtx := svc.NewServiceContext(c)

	l := NewTestGitConfigurationLogic(context.Background(), svcCtx)

	tests := []struct {
		gitURL      string
		accessToken string
	}{
		{
			gitURL:      "https://gitlab.ttyuyin.com/tt-protocols/app.git",
			accessToken: "",
		},
		{
			gitURL:      "https://github.com/google/go-github.git",
			accessToken: "",
		},
		{
			gitURL:      "https://github.com/xanzy/go-gitlab",
			accessToken: "****************************************",
		},
	}
	for _, tt := range tests {
		branches, err := l.getBranches(tt.gitURL, tt.accessToken)
		if err != nil {
			t.Fatal(err)
		}

		t.Logf("URL: %s, Branches: %+v", tt.gitURL, branches)
	}
}
