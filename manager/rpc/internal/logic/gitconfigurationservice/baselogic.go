package gitconfigurationservicelogic

import (
	"context"
	"fmt"
	"net/url"
	"strings"

	"github.com/google/go-github/v63/github"
	"github.com/pkg/errors"
	"github.com/xanzy/go-gitlab"
	"github.com/zeromicro/go-zero/core/logx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/userinfo"
	qetutils "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	commonpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/utils"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
)

type BaseLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext

	currentUser *userinfo.UserInfo

	converters []qetutils.TypeConverter
}

func newBaseLogic(ctx context.Context, svcCtx *svc.ServiceContext) *BaseLogic {
	return &BaseLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,

		currentUser: userinfo.FromContext(ctx),

		converters: []qetutils.TypeConverter{
			commonpb.StringToTriggerMode(),
			logic.SqlNullStringToTags(),
		},
	}
}

func (l *BaseLogic) generateConfigID(projectID string) (string, error) {
	g := qetutils.NewUniqueIdGenerator(
		qetutils.WithGenerateFunc(utils.GenGitConfigID), qetutils.WithIsUniqueFunc(
			func(id string) bool {
				r, err := l.svcCtx.GitConfigModel.FindOneByProjectIdConfigId(l.ctx, projectID, id)
				if errors.Is(err, model.ErrNotFound) || r == nil {
					return true
				}
				return false
			},
		),
	)
	configID := g.Next()
	if configID == "" {
		return "", errors.WithStack(
			errorx.Err(
				errorx.GenerateUniqueIdFailure, "failed to generate git config id, please try it later",
			),
		)
	}

	return configID, nil
}

func (l *BaseLogic) getBranchesOfGitLab(gitURL, accessToken string) ([]string, error) {
	u, err := url.Parse(gitURL)
	if err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.ValidateParamError, err.Error()),
			"failed to parse the git url, url: %s, error: %+v",
			gitURL, err,
		)
	}

	baseURL := fmt.Sprintf("%s://%s", u.Scheme, u.Host)
	token := accessToken
	if token == "" {
		// using personal access token of `probe`
		baseURL = l.svcCtx.Config.GitLab.BaseURL
		token = l.svcCtx.Config.GitLab.Token
	}

	path := strings.TrimSuffix(u.Path, ".git")
	if len(path) < 2 {
		return nil, errorx.Errorf(
			errorx.ValidateParamError, "invalid path of git url, url: %s", gitURL,
		)
	}

	c, err := gitlab.NewClient(
		token, gitlab.WithBaseURL(baseURL), gitlab.WithRequestOptions(gitlab.WithContext(l.ctx)),
	)
	if err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.NewObjectFailure, err.Error()),
			"failed to new a gitlab client, url: %s, token: %s, error: %+v",
			gitURL, accessToken, err,
		)
	}

	var (
		opts = &gitlab.ListBranchesOptions{
			ListOptions: gitlab.ListOptions{
				Page:    1,
				PerPage: 100,
			},
		}

		result []string
	)

	for {
		branches, resp, err := c.Branches.ListBranches(path[1:], opts)
		if err != nil {
			return nil, errors.Wrapf(
				errorx.Errorf(
					errorx.CallExternalAPIFailure,
					"failed to get branches of the git project, url: %s, error: %+v",
					gitURL, err,
				),
				"failed to get branches, url: %s, token: %s, error: %+v",
				gitURL, accessToken, err,
			)
		} else if len(branches) == 0 {
			break
		}

		if result == nil {
			result = make([]string, 0, resp.TotalItems)
		}
		for _, branch := range branches {
			if branch.Name == "" {
				continue
			}

			result = append(result, branch.Name)
		}

		if resp.NextPage == 0 {
			break
		}
		opts.Page = resp.NextPage
	}

	return result, nil
}

func (l *BaseLogic) getBranchesOfGitHub(gitURL, accessToken string) ([]string, error) {
	u, err := url.Parse(gitURL)
	if err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.ValidateParamError, err.Error()),
			"failed to parse the git url, url: %s, error: %+v",
			gitURL, err,
		)
	}

	paths := strings.Split(strings.TrimSuffix(u.Path, ".git"), "/")
	if len(paths) < 3 {
		return nil, errorx.Errorf(
			errorx.ValidateParamError, "invalid path of git url, url: %s", gitURL,
		)
	}

	c := github.NewClient(nil)
	if accessToken != "" {
		c = c.WithAuthToken(accessToken)
	}

	var (
		opts = &github.BranchListOptions{
			ListOptions: github.ListOptions{
				Page:    1,
				PerPage: 100,
			},
		}

		result []string
	)

	for {
		branches, resp, err := c.Repositories.ListBranches(l.ctx, paths[1], paths[2], opts)
		if err != nil {
			return nil, errors.Wrapf(
				errorx.Errorf(
					errorx.CallExternalAPIFailure,
					"failed to get branches of the git project, url: %s, token: %s, error: %+v",
					gitURL, accessToken, err,
				),
				"failed to get branches, url: %s, token: %s, error: %+v",
				gitURL, accessToken, err,
			)
		} else if len(branches) == 0 {
			break
		}

		for _, branch := range branches {
			if branch.GetName() == "" {
				continue
			}

			result = append(result, branch.GetName())
		}

		if resp.NextPage == 0 {
			break
		}
		opts.Page = resp.NextPage
	}

	return result, nil
}
