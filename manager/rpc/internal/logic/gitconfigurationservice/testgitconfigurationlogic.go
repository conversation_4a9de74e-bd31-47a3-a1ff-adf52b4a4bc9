package gitconfigurationservicelogic

import (
	"context"
	"net/url"
	"strings"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type TestGitConfigurationLogic struct {
	*BaseLogic
}

func NewTestGitConfigurationLogic(ctx context.Context, svcCtx *svc.ServiceContext) *TestGitConfigurationLogic {
	return &TestGitConfigurationLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// TestGitConfiguration 测试Git配置
func (l *TestGitConfigurationLogic) TestGitConfiguration(in *pb.TestGitConfigurationReq) (
	resp *pb.TestGitConfigurationResp, err error,
) {
	branches, err := l.getBranches(in.GetUrl(), in.GetAccessToken())
	if err != nil {
		return nil, err
	}

	return &pb.TestGitConfigurationResp{
		Branches: branches,
	}, nil
}

func (l *TestGitConfigurationLogic) getBranches(gitURL, accessToken string) ([]string, error) {
	u, err := url.Parse(gitURL)
	if err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.ValidateParamError, err.Error()),
			"failed to parse the git url, url: %s, error: %+v",
			gitURL, err,
		)
	}

	host := u.Host
	gitType := host[0:strings.Index(host, ".")]

	switch gitType {
	case common.ConstGitTypeGitLab:
		return l.getBranchesOfGitLab(gitURL, accessToken)
	case common.ConstGitTypeGitHub:
		return l.getBranchesOfGitHub(gitURL, accessToken)
	case common.ConstGitTypeGitee:
		return nil, errorx.Errorf(
			errorx.DoesNotSupport, "the git type is not supported at the moment, git type: %s", gitType,
		)
	default:
		return nil, errorx.Errorf(errorx.DoesNotSupport, "invalid git type: %s", gitType)
	}
}
