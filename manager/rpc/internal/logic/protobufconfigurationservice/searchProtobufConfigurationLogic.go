package protobufconfigurationservicelogic

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sqlbuilder/rpc"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type SearchProtobufConfigurationLogic struct {
	*BaseLogic
}

func NewSearchProtobufConfigurationLogic(
	ctx context.Context, svcCtx *svc.ServiceContext,
) *SearchProtobufConfigurationLogic {
	return &SearchProtobufConfigurationLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// SearchProtobufConfiguration 搜索Protobuf配置
func (l *SearchProtobufConfigurationLogic) SearchProtobufConfiguration(in *pb.SearchProtobufConfigurationReq) (
	out *pb.SearchProtobufConfigurationResp, err error,
) {
	out = &pb.SearchProtobufConfigurationResp{}

	// validate the project_id in req
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, in.GetProjectId()); err != nil {
		return nil, err
	}

	req := model.SearchProtobufConfigurationReq{
		BaseSearchReq: model.BaseSearchReq{
			ProjectID:  in.GetProjectId(),
			Condition:  in.GetCondition(),
			Pagination: in.GetPagination(),
			Sort:       rpc.ConvertSortFields(in.GetSort()),
		},
	}
	count, err := l.svcCtx.ProtobufConfigModel.FindCountBySearchReq(l.ctx, req)
	if err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()),
			"failed to count protobuf configurations, project_id: %s, error: %+v",
			in.GetProjectId(), err,
		)
	}
	out.TotalCount = uint64(count)
	out.TotalPage = 1

	configurations, err := l.svcCtx.ProtobufConfigModel.FindAllBySearchReq(l.ctx, req)
	if err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()),
			"failed to find protobuf configurations, project_id: %s, error: %+v",
			in.GetProjectId(), err,
		)
	}

	out.Items = make([]*pb.ProtobufConfiguration, 0, len(configurations))
	for _, configuration := range configurations {
		item := &pb.ProtobufConfiguration{}
		if err = utils.Copy(item, configuration, l.converters...); err != nil {
			return nil, errors.Wrapf(
				errorx.Err(errorx.CopyToStructFailure, err.Error()),
				"failed to copy protobuf configuration to response, config: %s, error: %+v",
				protobuf.MarshalJSONIgnoreError(configuration), err,
			)
		}

		// no need to return dependencies while searching protobuf configurations
		item.Dependencies = nil
		out.Items = append(out.Items, item)
	}

	pagination := in.GetPagination()
	if pagination != nil {
		out.CurrentPage = pagination.GetCurrentPage()
		out.PageSize = pagination.GetPageSize()
		out.TotalPage = (out.TotalCount + out.PageSize - 1) / out.PageSize // round up
	}

	return out, nil
}
