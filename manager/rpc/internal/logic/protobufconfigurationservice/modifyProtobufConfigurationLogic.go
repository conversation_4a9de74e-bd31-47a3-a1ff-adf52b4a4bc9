package protobufconfigurationservicelogic

import (
	"context"
	"database/sql"
	"fmt"
	"time"

	dll "github.com/emirpasic/gods/lists/doublylinkedlist"
	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zyedidia/generic"
	"github.com/zyedidia/generic/set"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/caller"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type ModifyProtobufConfigurationLogic struct {
	*BaseLogic
}

func NewModifyProtobufConfigurationLogic(
	ctx context.Context, svcCtx *svc.ServiceContext,
) *ModifyProtobufConfigurationLogic {
	return &ModifyProtobufConfigurationLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// ModifyProtobufConfiguration 编辑Protobuf配置
func (l *ModifyProtobufConfigurationLogic) ModifyProtobufConfiguration(in *pb.ModifyProtobufConfigurationReq) (
	out *pb.ModifyProtobufConfigurationResp, err error,
) {
	// validate the project_id in req
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, in.GetProjectId()); err != nil {
		return nil, err
	}

	// validate the config_id in req
	origin, err := model.CheckProtobufConfigByConfigID(
		l.ctx, l.svcCtx.ProtobufConfigModel, in.GetProjectId(), in.GetConfigId(),
	)
	if err != nil {
		return nil, err
	}

	// check if the dependencies has circular references.
	stack := dll.New(in.GetConfigId())
	if err = l.checkCircularReference(in.GetProjectId(), in.GetDependencies(), stack); err != nil {
		return nil, err
	}

	if in.GetGitConfigId() != origin.GitConfigId {
		// validate the git_config_id in req
		if _, err = model.CheckGitConfigByConfigID(
			l.ctx, l.svcCtx.GitConfigModel, in.GetProjectId(), in.GetGitConfigId(),
		); err != nil {
			return nil, err
		}
	}

	var configuration *model.ProtobufConfiguration

	key := fmt.Sprintf(
		"%s:%s:%s", common.ConstLockProtobufConfigProjectIDConfigIDPrefix, in.GetProjectId(), in.GetConfigId(),
	)
	fn := func() error {
		configuration, err = l.modify(in, origin)
		return err
	}
	if err = caller.LockDo(l.svcCtx.Redis, key, fn); err != nil {
		return nil, err
	}

	out = &pb.ModifyProtobufConfigurationResp{Configuration: &pb.ProtobufConfiguration{}}
	if err = utils.Copy(out.Configuration, configuration, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy protobuf configuration to response, config: %s, error: %+v",
			jsonx.MarshalIgnoreError(configuration), err,
		)
	}

	return out, nil
}

func (l *ModifyProtobufConfigurationLogic) modify(
	req *pb.ModifyProtobufConfigurationReq, origin *model.ProtobufConfiguration,
) (*model.ProtobufConfiguration, error) {
	now := time.Now()
	protobufConfig := &model.ProtobufConfiguration{
		Id:        origin.Id,
		ProjectId: origin.ProjectId,
		ConfigId:  origin.ConfigId,
		Name:      req.GetName(),
		Description: sql.NullString{
			String: req.GetDescription(),
			Valid:  req.GetDescription() != "",
		},
		GitConfigId: req.GetGitConfigId(),
		ImportPath:  req.GetImportPath(),
		ExcludePaths: sql.NullString{
			String: jsonx.MarshalToStringIgnoreError(req.GetExcludePaths()),
			Valid:  len(req.GetExcludePaths()) > 0,
		},
		ExcludeFiles: sql.NullString{
			String: jsonx.MarshalToStringIgnoreError(req.GetExcludeFiles()),
			Valid:  len(req.GetExcludeFiles()) > 0,
		},
		CreatedBy: origin.CreatedBy,
		UpdatedBy: l.currentUser.Account,
		CreatedAt: origin.CreatedAt,
		UpdatedAt: now,
	}

	fromMap, fromSet, err := l.getDependencies(protobufConfig.ProjectId, protobufConfig.ConfigId)
	if err != nil {
		return nil, err
	}
	toSet := set.NewHashset[string](
		uint64(len(req.GetDependencies())), generic.Equals[string], generic.HashString, req.GetDependencies()...,
	)

	removes := fromSet.Difference(toSet)
	increases := toSet.Difference(fromSet)

	dependencies := make([]*model.ProtobufDependence, 0, increases.Size())
	increases.Each(
		func(key string) {
			dependencies = append(
				dependencies, &model.ProtobufDependence{
					ProjectId:   protobufConfig.ProjectId,
					ConfigId:    protobufConfig.ConfigId,
					DepConfigId: key,
					CreatedBy:   l.currentUser.Account,
					UpdatedBy:   l.currentUser.Account,
					CreatedAt:   now,
					UpdatedAt:   now,
				},
			)
		},
	)

	// modify protobuf configuration in a transaction
	if err = l.svcCtx.ProtobufConfigModel.Trans(
		l.ctx, func(context context.Context, session sqlx.Session) error {
			if _, err := l.svcCtx.ProtobufConfigModel.Update(context, session, protobufConfig); err != nil {
				return errors.Wrapf(
					errorx.Err(errorx.DBError, err.Error()),
					"failed to update values to table, table: %s, values: %s, error: %+v",
					l.svcCtx.ProtobufConfigModel.Table(), jsonx.MarshalIgnoreError(protobufConfig), err,
				)
			}

			for _, key := range removes.Keys() {
				val, ok := fromMap.Get(key)
				if !ok || val.Id == 0 {
					continue
				}

				if err := l.svcCtx.ProtobufDependenceModel.LogicDelete(context, session, val.Id); err != nil {
					return errors.Wrapf(
						errorx.Err(errorx.DBError, err.Error()),
						"failed to delete item from table, table: %s, item: %s, error: %+v",
						l.svcCtx.ProtobufDependenceModel.Table(), jsonx.MarshalIgnoreError(val), err,
					)
				}
			}

			if _, err := l.svcCtx.ProtobufDependenceModel.BatchInsert(context, session, dependencies); err != nil {
				return errors.Wrapf(
					errorx.Err(errorx.DBError, err.Error()),
					"failed to batch insert values to table, table: %s, values: %s, error: %+v",
					l.svcCtx.ProtobufDependenceModel.Table(), jsonx.MarshalIgnoreError(dependencies), err,
				)
			}

			return nil
		},
	); err != nil {
		return nil, err
	}

	return protobufConfig, nil
}
