package protobufconfigurationservicelogic

import (
	"context"
	"fmt"

	"github.com/hashicorp/go-multierror"
	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/mr"
	"github.com/zeromicro/go-zero/core/stores/sqlx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/caller"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type RemoveProtobufConfigurationLogic struct {
	*BaseLogic
}

func NewRemoveProtobufConfigurationLogic(
	ctx context.Context, svcCtx *svc.ServiceContext,
) *RemoveProtobufConfigurationLogic {
	return &RemoveProtobufConfigurationLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// RemoveProtobufConfiguration 删除Protobuf配置
func (l *RemoveProtobufConfigurationLogic) RemoveProtobufConfiguration(in *pb.RemoveProtobufConfigurationReq) (
	out *pb.RemoveProtobufConfigurationResp, err error,
) {
	// validate the project_id in req
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, in.GetProjectId()); err != nil {
		return nil, err
	}

	configIDs := in.GetConfigIds()
	workers := len(configIDs)
	if workers > common.ConstMRMaxWorkers {
		workers = common.ConstMRMaxWorkers
	}

	mr.ForEach[string](
		func(source chan<- string) {
			for _, configID := range configIDs {
				source <- configID
			}
		}, func(item string) {
			if e := l.remove(in.GetProjectId(), item); e != nil {
				err = multierror.Append(err, e)
			}
		}, mr.WithContext(l.ctx), mr.WithWorkers(workers),
	)

	return &pb.RemoveProtobufConfigurationResp{}, err
}

func (l *RemoveProtobufConfigurationLogic) remove(projectID, configID string) (err error) {
	// validate the config_id in req
	protobufConfig, err := model.CheckProtobufConfigByConfigID(l.ctx, l.svcCtx.ProtobufConfigModel, projectID, configID)
	if err != nil {
		if re, ok := errorx.RootError(err); ok && re.Code() == errorx.NotExists {
			return nil
		}

		return err
	}

	pds, err := l.svcCtx.ProtobufDependenceModel.FindByDepConfigID(l.ctx, projectID, configID)
	if err != nil {
		return errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()),
			"failed to find protobuf dependence config, project_id: %s, dep_config_id: %s, dep_config_name: %s, error: %+v",
			projectID, configID, protobufConfig.Name, err,
		)
	} else if len(pds) > 0 {
		return errorx.Errorf(
			errorx.ProhibitedBehavior,
			"cannot to delete the protobuf configuration which has been depended by %d protobuf configuration[s], project_id: %s, dep_config_id: %s, dep_config_name: %s",
			len(pds), projectID, configID, protobufConfig.Name,
		)
	}

	pps, err := l.svcCtx.PerfPlanV2Model.FindByProtobufConfigID(l.ctx, projectID, configID)
	if err != nil {
		return errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()),
			"failed to find perf plan, project_id: %s, config_id: %s, config_name: %s, error: %+v",
			projectID, configID, protobufConfig.Name, err,
		)
	} else if len(pps) > 0 {
		return errorx.Errorf(
			errorx.ProhibitedBehavior,
			"cannot to delete the protobuf configuration which has been referenced by %d perf plan[s], project_id: %s, config_id: %s, config_name: %s",
			len(pps), projectID, configID, protobufConfig.Name,
		)
	}

	key := fmt.Sprintf(
		"%s:%s:%s", common.ConstLockProtobufConfigProjectIDConfigIDPrefix, projectID, configID,
	)
	fn := func() error {
		return l.svcCtx.ProtobufConfigModel.Trans(
			l.ctx, func(context context.Context, session sqlx.Session) error {
				// Table: protobuf_dependence
				if _, err := l.svcCtx.ProtobufDependenceModel.RemoveByConfigID(
					context, session, projectID, configID,
				); err != nil {
					return errors.Wrapf(
						errorx.Err(errorx.DBError, err.Error()),
						"failed to remove protobuf dependence configuration, project_id: %s, config_id: %s, error: %+v",
						projectID, configID, err,
					)
				}

				// Table: protobuf_configuration
				if _, err := l.svcCtx.ProtobufConfigModel.RemoveByConfigID(
					context, session, projectID, configID,
				); err != nil {
					return errors.Wrapf(
						errorx.Err(errorx.DBError, err.Error()),
						"failed to remove protobuf configuration, project_id: %s, config_id: %s, error: %+v",
						projectID, configID, err,
					)
				}

				return nil
			},
		)
	}
	return caller.LockDo(l.svcCtx.Redis, key, fn)
}
