package protobufconfigurationservicelogic

import (
	"context"
	"database/sql"
	"time"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"
	"github.com/zeromicro/go-zero/core/stores/sqlx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type CreateProtobufConfigurationLogic struct {
	*BaseLogic
}

func NewCreateProtobufConfigurationLogic(
	ctx context.Context, svcCtx *svc.ServiceContext,
) *CreateProtobufConfigurationLogic {
	return &CreateProtobufConfigurationLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// CreateProtobufConfiguration 创建Protobuf配置
func (l *CreateProtobufConfigurationLogic) CreateProtobufConfiguration(in *pb.CreateProtobufConfigurationReq) (
	out *pb.CreateProtobufConfigurationResp, err error,
) {
	// validate the project_id in req
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, in.GetProjectId()); err != nil {
		return nil, err
	}

	// validate the git_config_id in req
	if _, err = model.CheckGitConfigByConfigID(
		l.ctx, l.svcCtx.GitConfigModel, in.GetProjectId(), in.GetGitConfigId(),
	); err != nil {
		return nil, err
	}

	// check if the dependencies has circular references.
	if err = l.checkCircularReference(in.GetProjectId(), in.GetDependencies(), nil); err != nil {
		return nil, err
	}

	configuration, err := l.create(in)
	if err != nil {
		return nil, err
	}

	out = &pb.CreateProtobufConfigurationResp{Configuration: &pb.ProtobufConfiguration{}}
	if err = utils.Copy(out.Configuration, configuration, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy protobuf configuration to response, config: %s, error: %+v",
			jsonx.MarshalIgnoreError(configuration), err,
		)
	}

	return out, nil
}

func (l *CreateProtobufConfigurationLogic) create(in *pb.CreateProtobufConfigurationReq) (
	*model.ProtobufConfiguration, error,
) {
	configID, err := l.generateConfigID(in.GetProjectId())
	if err != nil {
		return nil, err
	}

	now := time.Now()
	protobufConfig := &model.ProtobufConfiguration{
		ProjectId: in.GetProjectId(),
		ConfigId:  configID,
		Name:      in.GetName(),
		Description: sql.NullString{
			String: in.GetDescription(),
			Valid:  in.GetDescription() != "",
		},
		GitConfigId: in.GetGitConfigId(),
		ImportPath:  in.GetImportPath(),
		ExcludePaths: sql.NullString{
			String: jsonx.MarshalToStringIgnoreError(in.GetExcludePaths()),
			Valid:  len(in.GetExcludePaths()) > 0,
		},
		ExcludeFiles: sql.NullString{
			String: jsonx.MarshalToStringIgnoreError(in.GetExcludeFiles()),
			Valid:  len(in.GetExcludeFiles()) > 0,
		},
		CreatedBy: l.currentUser.Account,
		UpdatedBy: l.currentUser.Account,
		CreatedAt: now,
		UpdatedAt: now,
	}

	dependencies := make([]*model.ProtobufDependence, 0, len(in.GetDependencies()))
	for _, dep := range in.GetDependencies() {
		dependencies = append(
			dependencies, &model.ProtobufDependence{
				ProjectId:   protobufConfig.ProjectId,
				ConfigId:    protobufConfig.ConfigId,
				DepConfigId: dep,
				CreatedBy:   l.currentUser.Account,
				UpdatedBy:   l.currentUser.Account,
				CreatedAt:   now,
				UpdatedAt:   now,
			},
		)
	}

	// create protobuf configuration in a transaction
	if err = l.svcCtx.ProtobufConfigModel.Trans(
		l.ctx, func(context context.Context, session sqlx.Session) error {
			if _, err := l.svcCtx.ProtobufConfigModel.Insert(context, session, protobufConfig); err != nil {
				return errors.Wrapf(
					errorx.Err(errorx.DBError, err.Error()),
					"failed to insert values to table, table: %s, values: %s, error: %+v",
					l.svcCtx.ProtobufConfigModel.Table(), jsonx.MarshalIgnoreError(protobufConfig), err,
				)
			}

			if _, err := l.svcCtx.ProtobufDependenceModel.BatchInsert(context, session, dependencies); err != nil {
				return errors.Wrapf(
					errorx.Err(errorx.DBError, err.Error()),
					"failed to batch insert values to table, table: %s, values: %s, error: %+v",
					l.svcCtx.ProtobufDependenceModel.Table(), jsonx.MarshalIgnoreError(dependencies), err,
				)
			}

			return nil
		},
	); err != nil {
		return nil, err
	}

	return protobufConfig, nil
}
