package protobufconfigurationservicelogic

import (
	"context"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type ViewProtobufConfigurationLogic struct {
	*BaseLogic
}

func NewViewProtobufConfigurationLogic(
	ctx context.Context, svcCtx *svc.ServiceContext,
) *ViewProtobufConfigurationLogic {
	return &ViewProtobufConfigurationLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// ViewProtobufConfiguration 查看Protobuf配置
func (l *ViewProtobufConfigurationLogic) ViewProtobufConfiguration(in *pb.ViewProtobufConfigurationReq) (
	out *pb.ViewProtobufConfigurationResp, err error,
) {
	// validate the project_id in req
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, in.GetProjectId()); err != nil {
		return nil, err
	}

	// validate the config_id in req
	configuration, err := model.CheckProtobufConfigByConfigID(
		l.ctx, l.svcCtx.ProtobufConfigModel, in.GetProjectId(), in.GetConfigId(),
	)
	if err != nil {
		return nil, err
	}

	out = &pb.ViewProtobufConfigurationResp{Configuration: &pb.ProtobufConfiguration{}}
	if err = utils.Copy(out.Configuration, configuration, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy protobuf configuration to response, config: %s, error: %+v",
			jsonx.MarshalIgnoreError(configuration), err,
		)
	}

	// get dependencies of protobuf configuration
	_, dependencies, err := l.getDependencies(in.GetProjectId(), in.GetConfigId())
	if err != nil {
		return nil, err
	}

	out.Configuration.Dependencies = dependencies.Keys()

	return out, nil
}
