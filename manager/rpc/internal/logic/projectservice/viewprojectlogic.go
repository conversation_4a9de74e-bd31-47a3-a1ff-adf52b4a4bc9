package projectservicelogic

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type ViewProjectLogic struct {
	*BaseLogic
}

func NewViewProjectLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ViewProjectLogic {
	return &ViewProjectLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// ViewProject 查看项目
func (l *ViewProjectLogic) ViewProject(in *pb.ViewProjectReq) (resp *pb.ViewProjectResp, err error) {
	// validate the project_id in req
	project, err := model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, in.GetProjectId())
	if err != nil {
		return nil, err
	}

	resp = &pb.ViewProjectResp{Project: &pb.Project{}}
	if err = utils.Copy(resp.Project, project); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy project[%+v] to response, error: %+v",
			project, err,
		)
	}

	return resp, nil
}
