package projectservicelogic

import (
	"context"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type RemoveProjectLogic struct {
	*BaseLogic
}

func NewRemoveProjectLogic(ctx context.Context, svcCtx *svc.ServiceContext) *RemoveProjectLogic {
	return &RemoveProjectLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// RemoveProject 删除项目
func (l *RemoveProjectLogic) RemoveProject(in *pb.RemoveProjectReq) (*pb.RemoveProjectResp, error) {
	// todo: add your logic here and delete this line

	return &pb.RemoveProjectResp{}, nil
}
