package projectservicelogic

import (
	"context"
	"math"

	"google.golang.org/grpc"
	"google.golang.org/grpc/encoding/gzip"

	permissionpb "gitlab.ttyuyin.com/TestDevelopment/qet-backend-middleware/permission/rpc/pb"
	userpb "gitlab.ttyuyin.com/TestDevelopment/qet-backend-middleware/user/rpc/pb"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type SearchProjectUserLogic struct {
	*BaseLogic
}

func NewSearchProjectUserLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SearchProjectUserLogic {
	return &SearchProjectUserLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// SearchProjectUser 搜索项目用户
func (l *SearchProjectUserLogic) SearchProjectUser(in *pb.SearchProjectUserReq) (
	resp *pb.SearchProjectUserResp, err error,
) {
	resp = &pb.SearchProjectUserResp{}

	// validate the project_id in req
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, in.GetProjectId()); err != nil {
		return nil, err
	}

	// search user by user service
	users, err := l.svcCtx.UserRPC.SearchUser(
		l.ctx, &userpb.UserListReq{
			Condition: in.GetCondition(),
			// Pagination: in.GetPagination(), // NOTICE: cannot do the pagination in the user service
			Sort: in.GetSort(),
		}, grpc.UseCompressor(gzip.Name),
	)
	if err != nil {
		return nil, err
	}

	items := users.GetItems()
	cache := make(map[string]*userpb.UserInfo, len(items))
	for _, item := range items {
		cache[item.Account] = item
	}

	// get the user_id list in the specified project by permission service
	projectUsers, err := l.svcCtx.PermissionRoleRPC.GetProjectUsers(
		l.ctx, &permissionpb.UserListReq{
			DomainSource: common.ConstDomainSourceProbe,
			DomainType:   constants.DomainTypeOfProject,
			DomainId:     in.GetProjectId(),
		}, grpc.UseCompressor(gzip.Name),
	)
	if err != nil {
		return nil, err
	}
	userRoles := projectUsers.GetUserRoleList()

	var skips, matches, total uint64

	pagination := in.GetPagination()
	if pagination != nil {
		currentPage := pagination.GetCurrentPage()
		if currentPage == 0 {
			currentPage = 1
		}
		pageSize := pagination.GetPageSize()
		if pageSize == 0 {
			pageSize = 1
		}

		resp.CurrentPage = currentPage
		resp.PageSize = pageSize
		resp.Items = make([]*userpb.UserInfo, 0, pageSize)

		skips = (currentPage - 1) * pageSize
		total = skips + pageSize
	} else {
		total = uint64(len(userRoles))
		resp.Items = make([]*userpb.UserInfo, 0, total)
	}

	// ignore if `skips` greater or equal to the length of `userRoles` slice
	if skips < uint64(len(userRoles)) {
		for _, userRole := range userRoles {
			userId := userRole.GetUser().GetAccount()
			if v, ok := cache[userId]; ok {
				matches += 1
				if skips < matches && matches <= total {
					resp.Items = append(resp.Items, v)
				}
			}
		}
	}
	resp.TotalCount = uint64(len(resp.Items))

	if pagination != nil {
		resp.TotalPage = uint64(math.Ceil(float64(resp.TotalCount) / float64(resp.PageSize)))
	}

	return resp, nil
}
