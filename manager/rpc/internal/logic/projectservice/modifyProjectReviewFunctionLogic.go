package projectservicelogic

import (
	"context"
	"fmt"

	"github.com/pkg/errors"
	"github.com/spf13/cast"
	"github.com/zeromicro/go-zero/core/jsonx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/caller"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	commonpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type ModifyProjectReviewFunctionLogic struct {
	*BaseLogic

	modifyProjectLogic *ModifyProjectLogic
}

func NewModifyProjectReviewFunctionLogic(
	ctx context.Context, svcCtx *svc.ServiceContext,
) *ModifyProjectReviewFunctionLogic {
	return &ModifyProjectReviewFunctionLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),

		modifyProjectLogic: NewModifyProjectLogic(ctx, svcCtx),
	}
}

// ModifyProjectReviewFunction 开启、关闭用例审核功能
func (l *ModifyProjectReviewFunctionLogic) ModifyProjectReviewFunction(in *pb.ModifyProjectReviewFunctionReq) (
	out *pb.ModifyProjectReviewFunctionResp, err error,
) {
	origin, err := model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, in.GetProjectId())
	if err != nil {
		return nil, err
	}

	var project *model.Project
	key := fmt.Sprintf("%s:%s", common.ConstLockProjectProjectIdPrefix, in.GetProjectId())
	fn := func() error {
		var larkChats []*commonpb.LarkChat
		if origin.CoverageLarkChats.Valid && origin.CoverageLarkChats.String != "" {
			if err = protobuf.UnmarshalJSONWithMessagesFromString(
				origin.CoverageLarkChats.String, &larkChats,
			); err != nil {
				return errors.Wrapf(
					errorx.Err(errorx.SerializationError, err.Error()),
					"failed to unmarshal coverage lark chats, project_id: %s, lark chats: %s, error: %+v",
					in.GetProjectId(), origin.CoverageLarkChats.String, err,
				)
			}
		}

		req := &pb.ModifyProjectReq{
			ProjectId:         in.GetProjectId(),
			Name:              origin.Name,
			Description:       origin.Description.String,
			ReviewEnabled:     in.GetReviewEnabled(),
			CoverageEnabled:   cast.ToBool(origin.CoverageEnabled),
			CoverageLarkChats: larkChats,
		}
		project, err = l.modifyProjectLogic.modify(req, origin)
		return err
	}
	if err = caller.LockDo(l.svcCtx.Redis, key, fn); err != nil {
		return nil, err
	}

	out = &pb.ModifyProjectReviewFunctionResp{Project: &pb.Project{}}
	if err = utils.Copy(out.Project, project, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy project to response, project: %s, error: %+v",
			jsonx.MarshalIgnoreError(project), err,
		)
	}

	return out, nil
}
