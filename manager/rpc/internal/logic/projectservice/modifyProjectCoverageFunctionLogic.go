package projectservicelogic

import (
	"context"
	"fmt"

	"github.com/pkg/errors"
	"github.com/spf13/cast"
	"github.com/zeromicro/go-zero/core/jsonx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/caller"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type ModifyProjectCoverageFunctionLogic struct {
	*BaseLogic

	modifyProjectLogic *ModifyProjectLogic
}

func NewModifyProjectCoverageFunctionLogic(
	ctx context.Context, svcCtx *svc.ServiceContext,
) *ModifyProjectCoverageFunctionLogic {
	return &ModifyProjectCoverageFunctionLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),

		modifyProjectLogic: NewModifyProjectLogic(ctx, svcCtx),
	}
}

// ModifyProjectCoverageFunction 开启、关闭接口用例覆盖率功能
func (l *ModifyProjectCoverageFunctionLogic) ModifyProjectCoverageFunction(in *pb.ModifyProjectCoverageFunctionReq) (
	out *pb.ModifyProjectCoverageFunctionResp, err error,
) {
	origin, err := model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, in.GetProjectId())
	if err != nil {
		return nil, err
	}

	var project *model.Project
	key := fmt.Sprintf("%s:%s", common.ConstLockProjectProjectIdPrefix, in.GetProjectId())
	fn := func() error {
		req := &pb.ModifyProjectReq{
			ProjectId:         in.GetProjectId(),
			Name:              origin.Name,
			Description:       origin.Description.String,
			ReviewEnabled:     cast.ToBool(origin.ReviewEnabled),
			CoverageEnabled:   in.GetCoverageEnabled(),
			CoverageLarkChats: in.GetCoverageLarkChats(),
		}
		project, err = l.modifyProjectLogic.modify(req, origin)
		return err
	}
	if err = caller.LockDo(l.svcCtx.Redis, key, fn); err != nil {
		return nil, err
	}

	out = &pb.ModifyProjectCoverageFunctionResp{Project: &pb.Project{}}
	if err = utils.Copy(out.Project, project, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy project to response, project: %s, error: %+v",
			jsonx.MarshalIgnoreError(project), err,
		)
	}

	return out, nil
}
