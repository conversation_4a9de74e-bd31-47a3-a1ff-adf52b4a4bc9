package projectservicelogic

import (
	"context"
	"database/sql"
	"fmt"
	"time"

	"github.com/pkg/errors"
	"github.com/spf13/cast"
	"github.com/zeromicro/go-zero/core/jsonx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/caller"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type ModifyProjectLogic struct {
	*BaseLogic
}

func NewModifyProjectLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ModifyProjectLogic {
	return &ModifyProjectLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// ModifyProject 编辑项目
func (l *ModifyProjectLogic) ModifyProject(in *pb.ModifyProjectReq) (out *pb.ModifyProjectResp, err error) {
	origin, err := model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, in.GetProjectId())
	if err != nil {
		return nil, err
	}

	var project *model.Project
	key := fmt.Sprintf("%s:%s", common.ConstLockProjectProjectIdPrefix, in.GetProjectId())
	fn := func() error {
		project, err = l.modify(in, origin)
		return err
	}
	if err = caller.LockDo(l.svcCtx.Redis, key, fn); err != nil {
		return nil, err
	}

	out = &pb.ModifyProjectResp{Project: &pb.Project{}}
	if err = utils.Copy(out.Project, project, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy project to response, project: %s, error: %+v",
			jsonx.MarshalIgnoreError(project), err,
		)
	}

	return out, nil
}

func (l *ModifyProjectLogic) modify(req *pb.ModifyProjectReq, origin *model.Project) (*model.Project, error) {
	var (
		projectID   = req.GetProjectId()
		name        = req.GetName()
		description = req.GetDescription()
	)

	reviewEnabled := cast.ToInt64(req.GetReviewEnabled())
	if origin.ReviewEnabled != 0 && reviewEnabled == 0 {
		// `review_enabled` field from `enable` to `disable`, check if there are any pending review records
		records, err := l.svcCtx.ReviewRecordModel.FindFullReviewRecordsByProjectID(
			l.ctx, projectID, []common.ReviewStatus{common.ConstReviewStatusPending},
		)
		if err != nil {
			return nil, errors.Wrapf(
				errorx.Err(errorx.DBError, err.Error()),
				"failed to find review records by project_id, project_id: %s, error: %+v",
				projectID, err,
			)
		} else if len(records) > 0 {
			return nil, errorx.Errorf(
				errorx.ProhibitedBehavior,
				"there are serval uncompleted review record for this project, so the review function cannot be disabled, project_id: %s, records: %d",
				projectID, len(records),
			)
		}
	}

	var coverageLarkChats string
	coverageEnabled := cast.ToInt64(req.GetCoverageEnabled())
	larkChats := req.GetCoverageLarkChats()
	if coverageEnabled != 0 {
		// `coverage_enabled` field set to be enables, check if there are any interface documents
		records, err := l.svcCtx.InterfaceDocumentModel.FindAll(l.ctx, projectID)
		if err != nil {
			return nil, errors.Wrapf(
				errorx.Err(errorx.DBError, err.Error()),
				"failed to find interface documents by project_id, project_id: %s, error: %+v",
				projectID, err,
			)
		} else if len(records) == 0 {
			return nil, errorx.Errorf(
				errorx.ProhibitedBehavior,
				"there are no interface documents for this project, so the coverage function cannot be enabled, project_id: %s",
				projectID,
			)
		}

		coverageLarkChats = protobuf.MarshalJSONWithMessagesToStringIgnoreError(larkChats)
	}

	project := &model.Project{
		Id:        origin.Id,
		ProjectId: projectID,
		Name:      name,
		Description: sql.NullString{
			String: description,
			Valid:  description != "",
		},
		ReviewEnabled:   reviewEnabled,
		CoverageEnabled: coverageEnabled,
		CoverageLarkChats: sql.NullString{
			String: coverageLarkChats,
			Valid:  len(larkChats) > 0,
		},
		CreatedBy: origin.CreatedBy,
		UpdatedBy: l.currentUser.Account,
		CreatedAt: origin.CreatedAt,
		UpdatedAt: time.Now(),
	}

	if _, err := l.svcCtx.ProjectModel.Update(l.ctx, nil, project); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()),
			"failed to update values to table, table: %s, values: %s, error: %+v",
			l.svcCtx.ProjectModel.Table(), jsonx.MarshalIgnoreError(project), err,
		)
	}

	return project, nil
}
