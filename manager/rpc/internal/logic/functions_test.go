package logic

import (
	"testing"

	"github.com/google/go-cmp/cmp"
	"github.com/zeromicro/go-zero/core/jsonx"
	"google.golang.org/protobuf/types/known/structpb"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

func TestUnmarshalFromString(t *testing.T) {
	str1 := `{"id": "BUSINESS_GROUP_pwya9g-XEnMwSxKyhHoaZSsGLX_y", "type": "BUSINESS_GROUP", "children": [[{"id": "HTTP_1xzxuv-4h6fknYeObK0bXIUxbGHb", "type": "HTTP"}]]}`
	str2 := `{"id": "BUSINESS_GROUP_18nxlt-LXLxjgiJl6OX91Emse2FT", "type": "BUSINESS_GROUP", "children": [[{"id": "POOL_ACCOUNT_a283hb-soObx8E8QMNmFogYJxK85", "type": "POOL_ACCOUNT"}, {"id": "HTTP_zwg734-iYiTjy0W59cBNqGs9wJzC", "type": "HTTP"}]]}`

	var (
		r1 types.Relation
		r2 pb.Relation
	)

	err := jsonx.UnmarshalFromString(str1, &r1)
	if err != nil {
		t.Fatal(err)
	}
	t.Logf("r1: %+v", &r1)

	err = protobuf.UnmarshalJSONFromString(str2, &r2)
	if err != nil {
		t.Log(err)
	}
	t.Logf("r2: %+v", &r2)
	t.Logf("r2: %s", protobuf.MarshalJSONToStringIgnoreError(&r2))

	r3 := &pb.Relation{
		Id:   "BUSINESS_GROUP_18nxlt-LXLxjgiJl6OX91Emse2FT",
		Type: "BUSINESS_GROUP",
		//Children: []*pb.Relation_Relations{
		//	{
		//		Relations: []*pb.Relation{
		//			{
		//				Id:   "POOL_ACCOUNT_a283hb-soObx8E8QMNmFogYJxK85",
		//				Type: "POOL_ACCOUNT",
		//			},
		//			{
		//				Id:   "HTTP_zwg734-iYiTjy0W59cBNqGs9wJzC",
		//				Type: "HTTP",
		//			},
		//		},
		//	},
		//},
		Children: []*structpb.ListValue{
			{
				Values: []*structpb.Value{
					structpb.NewStructValue(&structpb.Struct{
						Fields: map[string]*structpb.Value{
							"id":   structpb.NewStringValue("POOL_ACCOUNT_a283hb-soObx8E8QMNmFogYJxK85"),
							"type": structpb.NewStringValue("POOL_ACCOUNT"),
						},
					}),
					structpb.NewStructValue(&structpb.Struct{
						Fields: map[string]*structpb.Value{
							"id":   structpb.NewStringValue("HTTP_zwg734-iYiTjy0W59cBNqGs9wJzC"),
							"type": structpb.NewStringValue("HTTP"),
						},
					}),
				},
			},
		},
	}
	str3 := protobuf.MarshalJSONToStringIgnoreError(r3)
	t.Logf("r3: %s", str3)

	diff := cmp.Diff(str2, str3)
	t.Logf("diff: %s", diff)
}
