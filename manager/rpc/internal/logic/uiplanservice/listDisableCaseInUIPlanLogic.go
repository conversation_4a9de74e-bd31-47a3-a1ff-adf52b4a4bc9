package uiplanservicelogic

import (
	"context"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type ListDisableCaseInUIPlanLogic struct {
	*BaseLogic
}

func NewListDisableCaseInUIPlanLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ListDisableCaseInUIPlanLogic {
	return &ListDisableCaseInUIPlanLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// ListDisableCaseInUIPlan 搜索UI计划中的失效用例
func (l *ListDisableCaseInUIPlanLogic) ListDisableCaseInUIPlan(in *pb.ListDisableCaseInUIPlanReq) (
	out *pb.ListDisableCaseInUIPlanResp, err error,
) {
	out = &pb.ListDisableCaseInUIPlanResp{}
	// validate the project_id in req
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, in.GetProjectId()); err != nil {
		return nil, err
	}
	// validate the plan_id in req
	plan, err := model.CheckUiPlanByPlanId(l.ctx, l.svcCtx.UiPlanModel, in.GetProjectId(), in.GetPlanId())
	if err != nil {
		return nil, err
	}
	_, nodePathSet, err := l.getAllGitProjectTreeNodesForHashmap(in.GetProjectId(), plan.GitConfigId)
	if err != nil {
		return nil, err
	}
	cases, pathSet, err := l.getAllCasesInUIPlanForHashmap(in.GetProjectId(), in.GetPlanId())
	if err != nil {
		return nil, err
	}

	out.Items = make([]*pb.SearchCaseInUIPlanItem, 0, cases.Size())
	difference := pathSet.Difference(nodePathSet)
	difference.Each(
		func(key string) {
			relationship, _ := cases.Get(key)
			query, _ := l.svcCtx.GitProjectTreeModel.FindRemovedPathNoCacheByQuery(
				l.ctx, relationship.ProjectId, relationship.GitConfigId, relationship.Path,
			)
			if query != nil {
				out.Items = append(
					out.Items, &pb.SearchCaseInUIPlanItem{
						ProjectId:   relationship.ProjectId,
						GitConfigId: relationship.GitConfigId,
						Path:        relationship.Path,
						ParentPath:  query.ParentPath.String,
						Name:        query.Name,
						Alias:       query.Alias.String,
						Type:        query.Type,
					},
				)
			}
		},
	)
	return out, nil
}
