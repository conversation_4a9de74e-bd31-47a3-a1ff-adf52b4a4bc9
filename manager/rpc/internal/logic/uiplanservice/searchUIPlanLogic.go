package uiplanservicelogic

import (
	"context"
	"math"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sqlbuilder/rpc"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type SearchUiPlanLogic struct {
	*BaseLogic
}

func NewSearchUiPlanLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SearchUiPlanLogic {
	return &SearchUiPlanLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// SearchUiPlan 搜索UI计划
func (l *SearchUiPlanLogic) SearchUiPlan(in *pb.SearchUiPlanReq) (resp *pb.SearchUiPlanResp, err error) {
	resp = &pb.SearchUiPlanResp{}

	// 验证req中的project_id
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, in.GetProjectId()); err != nil {
		return nil, err
	}

	// 验证req中的category_id
	if _, err = model.CheckCategoryByCategoryId(l.ctx, l.svcCtx.CategoryModel, in.GetProjectId(), common.ConstCategoryTreeTypeUiPlan, in.GetCategoryId()); err != nil {
		return nil, err
	}

	selectBuilder, countBuilder := l.svcCtx.UiPlanModel.GenerateSearchUiPlanSqlBuilder(
		model.SearchUiPlanWithCategoryReq{
			SearchUiPlanReq: model.SearchUiPlanReq{
				ProjectId:  in.GetProjectId(),
				Condition:  in.GetCondition(),
				Pagination: in.GetPagination(),
				Sort:       rpc.ConvertSortFields(in.GetSort()),
			},
			CategoryId:    in.GetCategoryId(),
			DrillDown:     true,
			CategoryModel: l.svcCtx.CategoryModel,
		},
	)

	count, err := l.svcCtx.UiPlanModel.FindCountUiPlans(l.ctx, countBuilder)
	if err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()), "failed to count ui plan with project_id[%s] and category_id[%s], error: %+v",
			in.GetProjectId(), in.GetCategoryId(), err,
		)
	}

	resp.TotalCount = uint64(count)
	resp.TotalPage = 1

	uiPlans, err := l.svcCtx.UiPlanModel.FindUiPlans(l.ctx, selectBuilder)
	if err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()), "failed to find ui plan with project_id[%s] and category_id[%s], error: %+v",
			in.GetProjectId(), in.GetCategoryId(), err,
		)
	}

	resp.Items = make([]*pb.UiPlan, 0, len(uiPlans))
	for _, uiPlan := range uiPlans {
		item := &pb.UiPlan{}
		if err = utils.Copy(item, uiPlan, l.converters...); err != nil {
			return nil, errors.Wrapf(
				errorx.Err(errorx.CopyToStructFailure, err.Error()),
				"failed to copy ui plan[%+v] to response, error: %+v", uiPlan, err,
			)
		}

		resp.Items = append(resp.Items, item)
	}

	pagination := in.GetPagination()
	if pagination != nil {
		resp.CurrentPage = pagination.GetCurrentPage()
		resp.PageSize = pagination.GetPageSize()
		resp.TotalPage = uint64(math.Ceil(float64(resp.TotalCount) / float64(resp.PageSize)))
	}

	return resp, nil
}
