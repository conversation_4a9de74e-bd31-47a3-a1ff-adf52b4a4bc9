package uiplanservicelogic

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type SearchUiPlanByProjectIdConfigIdLogic struct {
	*BaseLogic
}

func NewSearchUiPlanByProjectIdConfigIdLogic(
	ctx context.Context, svcCtx *svc.ServiceContext,
) *SearchUiPlanByProjectIdConfigIdLogic {
	return &SearchUiPlanByProjectIdConfigIdLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *SearchUiPlanByProjectIdConfigIdLogic) SearchUiPlanByProjectIdConfigId(in *pb.SearchUiPlanByProjectIdConfigIdReq) (
	resp *pb.SearchUiPlanByProjectIdConfigIdResp, err error,
) {
	resp = &pb.SearchUiPlanByProjectIdConfigIdResp{}

	// 验证req中的project_id
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, in.GetProjectId()); err != nil {
		return nil, err
	}

	uiPlans, err := l.svcCtx.UiPlanModel.SearchUiPlanByProjectIdConfigId(l.ctx, in.ProjectId, in.GitConfigId)
	if err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()), "failed to find ui plan with project_id[%s], error: %+v",
			in.GetProjectId(), err,
		)
	}

	resp.Items = make([]*pb.UiPlan, 0, len(uiPlans))
	for _, uiPlan := range uiPlans {
		item := &pb.UiPlan{}
		if err = utils.Copy(item, uiPlan, l.converters...); err != nil {
			return nil, errors.Wrapf(
				errorx.Err(errorx.CopyToStructFailure, err.Error()),
				"failed to copy ui plan[%+v] to response, error: %+v", uiPlan, err,
			)
		}
		resp.Items = append(resp.Items, item)
	}

	return resp, nil
}
