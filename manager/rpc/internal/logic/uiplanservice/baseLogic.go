package uiplanservicelogic

import (
	"context"
	"fmt"
	"strconv"
	"time"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"
	"github.com/zeromicro/go-zero/core/lang"
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zyedidia/generic"
	"github.com/zyedidia/generic/hashmap"
	"github.com/zyedidia/generic/set"

	qetconstants "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sqlbuilder/rpc"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/userinfo"
	qetutils "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	commonpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/utils"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/logic"
	tagservicelogic "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/logic/tagservice"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
)

type BaseLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext

	currentUser *userinfo.UserInfo

	createTagLogic *tagservicelogic.CreateTagLogic

	converters []qetutils.TypeConverter
}

func newBaseLogic(ctx context.Context, svcCtx *svc.ServiceContext) *BaseLogic {
	return &BaseLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,

		currentUser: userinfo.FromContext(ctx),

		createTagLogic: tagservicelogic.NewCreateTagLogic(ctx, svcCtx),

		converters: []qetutils.TypeConverter{
			commonpb.StringToTriggerMode(),

			logic.SqlNullStringToTags(),
			logic.SqlNullStringToDeviceIDs(),
		},
	}
}

func (l *BaseLogic) generatePlanId(projectId string) (string, error) {
	g := qetutils.NewUniqueIdGenerator(
		qetutils.WithGenerateFunc(utils.GenUIPlanID), qetutils.WithIsUniqueFunc(
			func(id string) bool {
				r, err := l.svcCtx.UiPlanModel.FindOneByProjectIdPlanId(l.ctx, projectId, id)
				if errors.Is(err, model.ErrNotFound) || r == nil {
					return true
				}
				return false
			},
		),
	)
	caseId := g.Next()
	if caseId == "" {
		return "", errors.WithStack(
			errorx.Err(
				errorx.GenerateUniqueIdFailure, "failed to generate ui plan id, please try it later",
			),
		)
	}

	return caseId, nil
}

func (l *BaseLogic) generatePeriodicTaskName(projectId, planId string) string {
	return fmt.Sprintf("%s:projectId:planId:%s:%s", constants.MQTaskTypeDispatcherPeriodicPlanTask, projectId, planId)
}

func (l *BaseLogic) getAllGitProjectTreeNodes(projectID, gitConfigID string) ([]*model.GitProjectTree, error) {
	nodes, err := l.svcCtx.GitProjectTreeModel.FindAll(l.ctx, projectID, gitConfigID)
	if err != nil && !errors.Is(err, model.ErrNotFound) {
		return nil, errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()),
			"failed to find git project tree with project_id[%s] and git_config_id[%s], error: %+v",
			projectID, gitConfigID, err,
		)
	}

	return nodes, nil
}

func (l *BaseLogic) getAllGitProjectTreeNodesForHashmap(projectID, gitConfigID string) (
	*hashmap.Map[string, *model.GitProjectTree], set.Set[string], error,
) {
	nodes, err := l.svcCtx.GitProjectTreeModel.FindAll(l.ctx, projectID, gitConfigID)
	if err != nil && !errors.Is(err, model.ErrNotFound) {
		return nil, set.Set[string]{}, errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()),
			"failed to find git project tree with project_id[%s] and git_config_id[%s], error: %+v",
			projectID, gitConfigID, err,
		)
	}
	m := hashmap.New[string, *model.GitProjectTree](uint64(len(nodes)), generic.Equals[string], generic.HashString)

	pathSet := set.NewHashset(uint64(len(nodes)), generic.Equals[string], generic.HashString)

	for _, node := range nodes {
		m.Put(node.Path, node)
		pathSet.Put(node.Path)
	}
	return m, pathSet, nil
}

func (l *BaseLogic) getAllCasesInUIPlan(projectID, planID string) ([]*model.UiPlanReferenceRelationship, error) {
	rrs, err := l.svcCtx.UiPlanReferenceModel.FindReferenceByPlanId(l.ctx, projectID, planID)
	if err != nil && !errors.Is(err, model.ErrNotFound) {
		return nil, errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()),
			"failed to find reference of ui plan with project_id[%s] and plan_id[%s], error: %+v",
			projectID, planID, err,
		)
	}

	return rrs, nil
}

func (l *BaseLogic) getAllCasesInUIPlanForHashmap(projectID, planID string) (
	*hashmap.Map[string, *model.UiPlanReferenceRelationship], set.Set[string], error,
) {
	rrs, err := l.svcCtx.UiPlanReferenceModel.FindReferenceByPlanId(l.ctx, projectID, planID)
	if err != nil && !errors.Is(err, model.ErrNotFound) {
		return nil, set.Set[string]{}, errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()),
			"failed to find reference of ui plan with project_id[%s] and plan_id[%s], error: %+v",
			projectID, planID, err,
		)
	}
	m := hashmap.New[string, *model.UiPlanReferenceRelationship](
		uint64(len(rrs)), generic.Equals[string], generic.HashString,
	)
	pathSet := set.NewHashset(uint64(len(rrs)), generic.Equals[string], generic.HashString)
	for _, rr := range rrs {
		m.Put(rr.Path, rr)
		if rr.Path == "" {
			continue
		}
		pathSet.Put(rr.Path)
	}
	return m, pathSet, nil
}

func (l *BaseLogic) getCacheOfAllCasesInUIPlan(projectID, planID string) (
	*hashmap.Map[string, *model.UiPlanReferenceRelationship], error,
) {
	rrs, err := l.getAllCasesInUIPlan(projectID, planID)
	if err != nil {
		return nil, err
	}

	m := hashmap.New[string, *model.UiPlanReferenceRelationship](
		uint64(len(rrs)), generic.Equals[string], generic.HashString,
	)
	for _, rr := range rrs {
		m.Put(rr.Path, rr)
	}

	return m, nil
}

func (l *BaseLogic) getAllUIProjectDevices(projectID string) (*hashmap.Map[string, *model.ProjectDevice], error) {
	records, err := l.svcCtx.ProjectDeviceModel.FindAllBySearchReq(
		l.ctx, model.SearchProjectDeviceReq{
			BaseSearchReq: model.BaseSearchReq{
				ProjectID: projectID,
				Condition: &rpc.Condition{
					Single: &rpc.SingleCondition{
						Field:   string(common.DeviceFieldOfUsage),
						Compare: qetconstants.EQ,
						Other: &rpc.Other{
							Value: strconv.Itoa(int(commonpb.DeviceUsage_UI_TESTING)),
						},
					},
				},
			},
		},
	)
	if err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()),
			"failed to find project device, project_id: %s, usage: %s, error: %+v",
			projectID, commonpb.DeviceUsage_UI_TESTING, err,
		)
	}

	m := hashmap.New[string, *model.ProjectDevice](uint64(len(records)), generic.Equals[string], generic.HashString)
	for _, record := range records {
		if record == nil || record.Id == 0 || record.Udid == "" {
			continue
		}

		m.Put(record.Udid, record)
	}

	return m, nil
}

func (l *BaseLogic) updateProjectDeviceRelationship(
	ctx context.Context, session sqlx.Session, uiPlan *model.UiPlan, devices []string,
) error {
	var (
		projectID = uiPlan.ProjectId
		planID    = uiPlan.PlanId

		now = time.Now()
	)

	if ctx == nil {
		ctx = l.ctx
	}

	rs, err := l.svcCtx.ProjectDeviceReferenceModel.FindByReference(
		ctx, projectID, common.ConstReferenceTypeUIPlan, planID,
	)
	if err != nil && !errors.Is(err, model.ErrNotFound) {
		return errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()),
			"failed to find project device relationship, project_id: %s, plan_id: %s, error: %+v",
			projectID, planID, err,
		)
	}

	if len(devices) == 0 && len(rs) == 0 {
		return nil
	}

	from := hashmap.New[string, *model.ProjectDeviceReferenceRelationship](
		uint64(len(rs)), generic.Equals[string], generic.HashString,
	)
	for _, r := range rs {
		if r == nil {
			continue
		}

		from.Put(r.Udid, r)
	}

	to := hashmap.New[string, lang.PlaceholderType](uint64(len(devices)), generic.Equals[string], generic.HashString)
	for _, d := range devices {
		to.Put(d, lang.Placeholder)
	}

	to.Each(
		func(key string, val lang.PlaceholderType) {
			if err != nil {
				return
			}

			if _, ok := from.Get(key); !ok {
				item := &model.ProjectDeviceReferenceRelationship{
					ProjectId:     uiPlan.ProjectId,
					ReferenceType: common.ConstReferenceTypeUIPlan,
					ReferenceId:   uiPlan.PlanId,
					Udid:          key,
					CreatedBy:     l.currentUser.Account,
					UpdatedBy:     l.currentUser.Account,
					CreatedAt:     now,
					UpdatedAt:     now,
				}
				if _, err = l.svcCtx.ProjectDeviceReferenceModel.Insert(ctx, session, item); err != nil {
					err = errors.Wrapf(
						errorx.Err(errorx.DBError, err.Error()),
						"failed to insert values to table, table: %s, values: %s, error: %+v",
						l.svcCtx.ProjectDeviceReferenceModel.Table(), jsonx.MarshalIgnoreError(item), err,
					)
					return
				}
			}
		},
	)
	if err != nil {
		return err
	}

	from.Each(
		func(key string, val *model.ProjectDeviceReferenceRelationship) {
			if err != nil {
				return
			}

			if _, ok := to.Get(key); !ok {
				if err = l.svcCtx.ProjectDeviceReferenceModel.Delete(ctx, session, val.Id); err != nil {
					err = errors.Wrapf(
						errorx.Err(errorx.DBError, err.Error()),
						"failed to delete item from table, table: %s, item: %s, error: %+v",
						l.svcCtx.ProjectDeviceReferenceModel.Table(), jsonx.MarshalIgnoreError(val), err,
					)
					return
				}
			}
		},
	)

	return err
}
