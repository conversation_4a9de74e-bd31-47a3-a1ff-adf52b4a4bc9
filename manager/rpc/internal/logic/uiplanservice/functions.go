package uiplanservicelogic

import (
	"time"

	"github.com/robfig/cron/v3"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
)

func checkScheduleWithMinInterval(schedule cron.Schedule, minInterval time.Duration, times int) error {
	if minInterval <= 0 {
		minInterval = common.ConstCheckScheduleMinInterval
	}
	if times <= 0 {
		times = common.ConstCheckScheduleTimes
	} else if times == 1 {
		// at least check 2 times to avoid the case that whether the trigger interval
		// of the cron expression is less than the specified interval
		times = 2
	}

	now := time.Now()
	last := time.Time{}
	for i := 0; i < times; i++ {
		next := schedule.Next(now)

		if !last.IsZero() && next.Sub(last) < minInterval {
			return errorx.Errorf(
				errorx.ProhibitedBehavior,
				"the trigger interval of the cron expression cannot be less than the specified interval, [%s, %s] < %s",
				last.Format(time.DateTime), next.Format(time.DateTime), minInterval,
			)
		}

		now = next
		last = next
	}

	return nil
}
