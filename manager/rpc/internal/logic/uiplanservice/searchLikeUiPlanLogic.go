package uiplanservicelogic

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sqlbuilder/rpc"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type SearchLikeUiPlanLogic struct {
	*BaseLogic
}

func NewSearchLikeUiPlanLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SearchLikeUiPlanLogic {
	return &SearchLikeUiPlanLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// SearchLikeUiPlan 搜索收藏UI计划
func (l *SearchLikeUiPlanLogic) SearchLikeUiPlan(in *pb.SearchUiPlanReq) (out *pb.SearchUiPlanResp, err error) {
	out = new(pb.SearchUiPlanResp)
	user := l.currentUser
	if user == nil {
		return nil, nil
	}

	searchUiPlanReq := model.SearchUiPlanReq{
		ProjectId:  in.GetProjectId(),
		Condition:  in.GetCondition(),
		Pagination: in.GetPagination(),
		Sort:       rpc.ConvertSortFields(in.GetSort()),
	}
	queryBuilder, countBuilder := l.svcCtx.PlanUserLikeRelationshipModel.GenerateUiPlanJoinPlanLikeSqlBuilderForProjectIdAndQuery(
		in.GetProjectId(), user.Account, searchUiPlanReq,
	)
	count, err := l.svcCtx.PlanUserLikeRelationshipModel.FindCount(l.ctx, countBuilder.SelectBuilder)
	if err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()), "failed to count api plan with project_id[%s], error: %+v",
			in.GetProjectId(), err,
		)
	}
	out.TotalCount = uint64(count)
	out.TotalPage = 1
	searchJoinUiPlanItemList, err := l.svcCtx.PlanUserLikeRelationshipModel.FindNoCacheJoinUiPlanByQuery(
		l.ctx, queryBuilder.SelectBuilder,
	)
	if err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()), "failed to list api plan with project_id[%s], error: %+v",
			in.GetProjectId(), err,
		)
	}

	out.Items = make([]*pb.UiPlan, 0, len(searchJoinUiPlanItemList))
	for _, uiPlan := range searchJoinUiPlanItemList {
		item := &pb.UiPlan{}
		if err = utils.Copy(item, uiPlan, l.converters...); err != nil {
			return nil, errors.Wrapf(
				errorx.Err(errorx.CopyToStructFailure, err.Error()),
				"failed to copy api plan[%+v] to response, error: %+v", uiPlan, err,
			)
		}

		out.Items = append(out.Items, item)
	}

	pagination := in.GetPagination()
	if pagination != nil {
		out.CurrentPage = pagination.GetCurrentPage()
		out.PageSize = pagination.GetPageSize()
		out.TotalPage = (out.TotalCount + out.PageSize - 1) / out.PageSize // round up
	}
	return out, nil
}
