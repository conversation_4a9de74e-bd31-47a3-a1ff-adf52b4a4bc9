package uiplanservicelogic

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sqlbuilder/rpc"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type SearchCaseInUIPlanLogic struct {
	*BaseLogic
}

func NewSearchCaseInUIPlanLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SearchCaseInUIPlanLogic {
	return &SearchCaseInUIPlanLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// SearchCaseInUIPlan 搜索UI计划中的用例
func (l *SearchCaseInUIPlanLogic) SearchCaseInUIPlan(in *pb.SearchCaseInUIPlanReq) (
	out *pb.SearchCaseInUIPlanResp, err error,
) {
	out = &pb.SearchCaseInUIPlanResp{}

	// validate the project_id in req
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, in.GetProjectId()); err != nil {
		return nil, err
	}

	// validate the plan_id in req
	plan, err := model.CheckUiPlanByPlanId(l.ctx, l.svcCtx.UiPlanModel, in.GetProjectId(), in.GetPlanId())
	if err != nil {
		return nil, err
	}

	// validate the path in req
	var root *model.GitProjectTree
	if in.GetPath() == "" {
		root, err = model.GetRootNodeOfGitProjectTree(
			l.ctx, l.svcCtx.GitProjectTreeModel, plan.ProjectId, plan.GitConfigId,
		)
	} else {
		root, err = model.CheckGitProjectTreeByPath(
			l.ctx, l.svcCtx.GitProjectTreeModel, plan.ProjectId, plan.GitConfigId, in.GetPath(),
		)
	}
	if err != nil {
		return nil, err
	} else if root.Type == string(common.ConstNodeTypeFunction) {
		return nil, errorx.Errorf(
			errorx.ValidateParamError,
			"invalid node type, project_id: %s, plan_id: %s, git_config_id: %s, path: %s, type: %s",
			in.GetProjectId(), in.GetPlanId(), plan.GitConfigId, root.Path, root.Type,
		)
	}

	selectBuilder, countBuilder := l.svcCtx.UiPlanModel.GenerateSearchCaseInUIPlanSqlBuilder(
		model.SearchCaseInUIPlanReq{
			GitProjectTreeModel: l.svcCtx.GitProjectTreeModel,
			ProjectID:           in.GetProjectId(),
			PlanID:              in.GetPlanId(),
			Path:                root.Path,
			Condition:           in.GetCondition(),
			Pagination:          in.GetPagination(),
			Sort:                rpc.ConvertSortFields(in.GetSort()),
		},
	)

	count, err := l.svcCtx.UiPlanModel.FindCountCasesInUIPlan(l.ctx, countBuilder)
	if err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()),
			"failed to count case in ui plan with project_id[%s], plan_id[%s] and path[%s], error: %+v",
			in.GetProjectId(), in.GetPlanId(), root.Path, err,
		)
	}
	out.TotalCount = uint64(count)
	out.TotalPage = 1

	cases, err := l.svcCtx.UiPlanModel.FindCasesInUIPlan(l.ctx, selectBuilder)
	if err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()),
			"failed to find case in ui plan with project_id[%s], plan_id[%s] and path[%s], error: %+v",
			in.GetProjectId(), in.GetPlanId(), root.Path, err,
		)
	}

	out.Items = make([]*pb.SearchCaseInUIPlanItem, 0, len(cases))
	for _, _case := range cases {
		item := &pb.SearchCaseInUIPlanItem{}
		if err = utils.Copy(item, _case, l.converters...); err != nil {
			return nil, errors.Wrapf(
				errorx.Err(errorx.CopyToStructFailure, err.Error()),
				"failed to copy ui case[%s] to response, error: %+v",
				protobuf.MarshalJSONIgnoreError(_case), err,
			)
		}

		out.Items = append(out.Items, item)
	}

	pagination := in.GetPagination()
	if pagination != nil {
		out.CurrentPage = pagination.GetCurrentPage()
		out.PageSize = pagination.GetPageSize()
		out.TotalPage = (out.TotalCount + out.PageSize - 1) / out.PageSize // round up
	}

	return out, nil
}
