package uiplanservicelogic

import (
	"context"
	"database/sql"
	"time"

	"github.com/gorhill/cronexpr"
	"github.com/pkg/errors"
	"github.com/spf13/cast"
	"github.com/zeromicro/go-zero/core/jsonx"
	"github.com/zeromicro/go-zero/core/stores/sqlx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"
	beatcommon "gitlab.ttyuyin.com/TestDevelopment/qet-backend-middleware/beat/common"
	beatpb "gitlab.ttyuyin.com/TestDevelopment/qet-backend-middleware/beat/rpc/pb"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	commonpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pkgpuller"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type CreateUiPlanLogic struct {
	*BaseLogic
}

func NewCreateUiPlanLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CreateUiPlanLogic {
	return &CreateUiPlanLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// CreateUiPlan 创建UI计划
func (l *CreateUiPlanLogic) CreateUiPlan(in *pb.CreateUiPlanReq) (out *pb.CreateUiPlanResp, err error) {
	// validate the project_id in req
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, in.GetProjectId()); err != nil {
		return nil, err
	}

	// validate the cron_expression in req if the plan type is SCHEDULE
	if in.GetType() == commonpb.TriggerMode_SCHEDULE {
		if in.GetPriorityType() == commonpb.PriorityType_Default {
			in.PriorityType = commonpb.PriorityType_Low
		}

		schedule, err := cronexpr.Parse(in.GetCronExpression())
		if err != nil {
			return nil, errors.Wrapf(
				errorx.Err(errorx.CronExpressionParseError, err.Error()),
				"failed to parse the cron expression, cron: %s, error: %+v",
				in.GetCronExpression(), err,
			)
		}

		if err = checkScheduleWithMinInterval(schedule, 0, 0); err != nil {
			return nil, err
		}
	} else {
		if in.GetPriorityType() == commonpb.PriorityType_Default {
			in.PriorityType = commonpb.PriorityType_Middle
		}
	}

	// validate the git_config_id in req
	gitConfig, err := model.CheckGitConfigByConfigID(
		l.ctx, l.svcCtx.GitConfigModel, in.GetProjectId(), in.GetGitConfigId())
	if err != nil {
		return nil, err
	}

	// validate git purpose
	if gitConfig.Purpose != string(common.ConstGitPurposeUI) {
		return nil, errorx.Errorf(
			errorx.ProhibitedBehavior,
			"the git configuration is not for UI testing, project_id: %s, config_id: %s, purpose: %s",
			in.GetProjectId(), in.GetGitConfigId(), gitConfig.Purpose,
		)
	}

	// validate the devices in req
	if len(in.GetDevices()) > 0 {
		if in.GetDeviceType() != commonpb.DeviceType_REAL_PHONE {
			return nil, errorx.Err(
				errorx.ProhibitedBehavior, "device assignment is only permitted when the device type is `real_phone`",
			)
		}

		devices, err := l.getAllUIProjectDevices(in.GetProjectId())
		if err != nil {
			return nil, err
		}

		for _, udid := range in.GetDevices() {
			if _, ok := devices.Get(udid); !ok {
				return nil, errorx.Errorf(
					errorx.ProhibitedBehavior,
					"the selected device is not included in the project devices for UI testing, udid: %s",
					udid,
				)
			}
		}
	}

	// validate the app_download_link in req if the empty is valid
	if len(in.GetAppDownloadLink()) == 0 {
		_, err = pkgpuller.NewAppPkgPuller(l.ctx, pkgpuller.AppPkgNameType(in.GetPackageName()), "")
		if err != nil {
			return nil, errorx.Errorf(
				errorx.ProhibitedBehavior,
				"the app_download_link can't be empty, error: %+v", err,
			)
		}
	}

	uiPlan, err := l.create(in)
	if err != nil {
		return nil, err
	}

	out = &pb.CreateUiPlanResp{Plan: &pb.UiPlan{}}
	if err = utils.Copy(out.Plan, uiPlan, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy api plan[%+v] to response, error: %+v",
			uiPlan, err,
		)
	}

	return out, nil
}

func (l *CreateUiPlanLogic) create(req *pb.CreateUiPlanReq) (*model.UiPlan, error) {
	planID, err := l.generatePlanId(req.GetProjectId())
	if err != nil {
		return nil, err
	}

	var devices string
	if len(req.GetDevices()) > 0 {
		// 由于`protojson`只能序列化`[]proto.Message`，而`tags`字段类型为`[]string`，Go Struct跟PB没有大的区别，因此直接使用`jsonx`暂代`protojson`
		devices = jsonx.MarshalToStringIgnoreError(req.GetDevices())
	}

	maintainedBy := req.GetMaintainedBy()
	if maintainedBy == "" {
		maintainedBy = l.currentUser.Account
	}

	testArgs := jsonx.MarshalToStringIgnoreError(req.GetTestArgs())

	uiPlan := &model.UiPlan{
		ProjectId:  req.GetProjectId(),
		CategoryId: req.GetCategoryId(),
		PlanId:     planID,
		Name:       req.GetName(),
		Description: sql.NullString{
			String: req.GetDescription(),
			Valid:  req.GetDescription() != "",
		},
		Type: req.GetType().String(),
		CronExpression: sql.NullString{
			String: req.GetCronExpression(),
			Valid:  req.GetCronExpression() != "",
		},
		PriorityType:  int64(req.GetPriorityType()),
		GitConfigId:   req.GetGitConfigId(),
		ExecutionMode: int64(req.GetExecutionMode()),
		DeviceType:    int64(req.GetDeviceType()),
		PlatformType:  int64(req.GetPlatformType()),
		Devices: sql.NullString{
			String: devices,
			Valid:  devices != "",
		},
		Together:    cast.ToInt64(req.GetTogether()),
		PackageName: req.GetPackageName(),
		AppName: sql.NullString{
			String: req.GetAppName(),
			Valid:  req.GetAppName() != "",
		},
		CallbackUrl: sql.NullString{
			String: req.GetCallbackUrl(),
			Valid:  req.GetCallbackUrl() != "",
		},
		AppDownloadLink: req.GetAppDownloadLink(),
		AppVersion: sql.NullString{
			String: req.GetAppVersion(),
			Valid:  req.GetAppVersion() != "",
		},
		TestLanguage:        int64(req.GetTestLanguage()),
		TestLanguageVersion: req.GetTestLanguageVersion(),
		TestFramework:       int64(req.GetTestFramework()),
		TestArgs: sql.NullString{
			String: testArgs,
			Valid:  testArgs != "",
		},
		ExecutionEnvironment: sql.NullString{
			String: req.GetExecutionEnvironment(),
			Valid:  req.GetExecutionEnvironment() != "",
		},
		FailRetry: int64(req.GetFailRetry()),
		State:     int64(pb.CommonState_CS_ENABLE),
		MaintainedBy: sql.NullString{
			String: maintainedBy,
			Valid:  maintainedBy != "",
		},
		CreatedBy: l.currentUser.Account,
		UpdatedBy: l.currentUser.Account,
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}
	if err = l.svcCtx.UiPlanModel.Trans(
		l.ctx, func(context context.Context, session sqlx.Session) error {
			if err := l.updateProjectDeviceRelationship(context, session, uiPlan, req.GetDevices()); err != nil {
				return err
			}

			if _, err := l.svcCtx.UiPlanModel.InsertTX(context, session, uiPlan); err != nil {
				return errors.Wrapf(
					errorx.Err(errorx.DBError, err.Error()),
					"failed to insert values to table, table: %s, values: %s, error: %+v",
					l.svcCtx.UiPlanModel.Table(), jsonx.MarshalIgnoreError(uiPlan), err,
				)
			}

			if uiPlan.Type == commonpb.TriggerMode_SCHEDULE.String() {
				if _, err := l.svcCtx.BeatRPC.CreateOrModifyPeriodicTask(
					l.ctx, &beatpb.CreateOrModifyPeriodicTaskReq{
						Name:  l.generatePeriodicTaskName(uiPlan.ProjectId, uiPlan.PlanId),
						Type:  constants.MQTaskTypeDispatcherPeriodicPlanTask,
						Queue: constants.MQNamePeriodicPlanTask,
						Spec:  uiPlan.CronExpression.String,
						Payload: protobuf.MarshalJSONToStringIgnoreError(
							&commonpb.PeriodicPlanTaskInfo{
								ProjectId:      uiPlan.ProjectId,
								PlanId:         uiPlan.PlanId,
								CronExpression: uiPlan.CronExpression.String,
								PlanType:       commonpb.PlanType_UI,
							},
						),
						Version: beatcommon.V2,
					},
				); err != nil {
					l.Logger.Errorf(
						"failed to create schedule task of ui plan[%s, %s, %s], error: %+v", uiPlan.ProjectId,
						uiPlan.PlanId, uiPlan.Name, err,
					)
					return err
				}
			}

			return nil
		},
	); err != nil {
		return nil, err
	}

	return uiPlan, nil
}
