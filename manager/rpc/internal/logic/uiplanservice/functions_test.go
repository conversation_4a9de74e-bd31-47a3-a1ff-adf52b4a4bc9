package uiplanservicelogic

import (
	"testing"
	"time"

	"github.com/gorhill/cronexpr"
)

func Test_checkScheduleWithMinInterval(t *testing.T) {
	type args struct {
		cronExpression string
		minInterval    time.Duration
		times          int
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name: "every minute",
			args: args{
				cronExpression: "* * * * *",
				minInterval:    0,
				times:          0,
			},
			wantErr: true,
		},
		{
			name: "every 30 minute",
			args: args{
				cronExpression: "*/30 * * * *",
				minInterval:    0,
				times:          1,
			},
			wantErr: true,
		},
		{
			name: "every hour",
			args: args{
				cronExpression: "10 * * * *",
				minInterval:    0,
				times:          0,
			},
			wantErr: false,
		},
		{
			name: "10:10,22:10 on the 1st day of every month",
			args: args{
				cronExpression: "10 10,22 1 * ?",
				minInterval:    0,
				times:          0,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				schedule, err := cronexpr.Parse(tt.args.cronExpression)
				if err != nil {
					t.Fatal(err)
				}

				if err = checkScheduleWithMinInterval(
					schedule, tt.args.minInterval, tt.args.times,
				); (err != nil) != tt.wantErr {
					t.Errorf("checkScheduleWithMinInterval() error = %v, wantErr %v", err, tt.wantErr)
				}
			},
		)
	}
}
