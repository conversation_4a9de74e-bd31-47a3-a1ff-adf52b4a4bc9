package uiplanservicelogic

import (
	"context"
	"fmt"
	"strings"

	"github.com/hashicorp/go-multierror"
	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/mr"
	"github.com/zeromicro/go-zero/core/stores/sqlx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/redislock"
	beatpb "gitlab.ttyuyin.com/TestDevelopment/qet-backend-middleware/beat/rpc/pb"

	commonpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common/zrpc/beat"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type RemoveUiPlanLogic struct {
	*BaseLogic
}

func NewRemoveUiPlanLogic(ctx context.Context, svcCtx *svc.ServiceContext) *RemoveUiPlanLogic {
	return &RemoveUiPlanLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// RemoveUiPlan 删除UI计划
func (l *RemoveUiPlanLogic) RemoveUiPlan(in *pb.RemoveUiPlanReq) (resp *pb.RemoveUiPlanResp, err error) {
	// validate the project_id in req
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, in.GetProjectId()); err != nil {
		return nil, err
	}

	planIds := in.GetPlanIds()
	workers := len(planIds)
	if workers > common.ConstMRMaxWorkers {
		workers = common.ConstMRMaxWorkers
	}

	mr.ForEach(
		func(source chan<- any) {
			for _, planId := range planIds {
				source <- planId
			}
		}, func(item any) {
			planId, ok := item.(string)
			if !ok {
				err = multierror.Append(
					err,
					errorx.Err(
						errorx.InternalError, fmt.Sprintf("the ui plan id[%v (%T)] is not a string", item, item),
					),
				)
			} else {
				if e := l.remove(in.GetProjectId(), planId); e != nil {
					err = multierror.Append(err, e)
				}
			}
		}, mr.WithContext(l.ctx), mr.WithWorkers(workers),
	)

	return &pb.RemoveUiPlanResp{}, err
}

func (l *RemoveUiPlanLogic) remove(projectId, planId string) (err error) {
	// validate the plan_id in req
	uiPlan, err := model.CheckUiPlanByPlanId(l.ctx, l.svcCtx.UiPlanModel, projectId, planId)
	if err != nil {
		return err
	}

	// 获取redis锁
	key := fmt.Sprintf("%s:%s:%s", common.ConstLockUiPlanProjectIdPlanIdPrefix, projectId, planId)
	lock, err := redislock.NewRedisLockAndAcquire(l.svcCtx.Redis, key, redislock.WithExpire(common.ConstLockExpireTime))
	if err != nil {
		return err
	}
	defer func() {
		// 释放redis锁定
		e := lock.Release()
		if e != nil {
			if re, ok := errorx.RootError(e); ok && re.Code() != errorx.ReleaseRedisLockFailure && err == nil {
				err = e
			}
		}
	}()

	return l.svcCtx.UiPlanModel.Trans(
		l.ctx, func(context context.Context, session sqlx.Session) error {
			// Table: notify
			if _, err := l.svcCtx.NotifyModel.RemoveByPlanId(context, session, projectId, planId); err != nil {
				return errors.Wrapf(
					errorx.Err(errorx.DBError, err.Error()),
					"failed to remove notify, project_id: %s, plan_id: %s, error: %+v",
					projectId, planId, err,
				)
			}

			// Table: ui_plan_reference_relationship
			if _, err := l.svcCtx.UiPlanReferenceModel.RemoveByPlanId(
				context, session, projectId, planId,
			); err != nil {
				return errors.Wrapf(
					errorx.Err(errorx.DBError, err.Error()),
					"failed to remove ui plan reference relationship, project_id: %s, plan_id: %s, error: %+v",
					projectId, planId, err,
				)
			}

			// Table: plan_user_like_relationship
			if err := l.svcCtx.PlanUserLikeRelationshipModel.DeleteByProjectIdAndPlanIdAndPlanType(
				context, session, projectId, planId, commonpb.PlanType_UI,
			); err != nil {
				return errors.Wrapf(
					errorx.Err(errorx.DBError, err.Error()),
					"failed to remove plan user like relationship, project_id: %s, plan_id: %s, error: %+v",
					projectId, planId, err,
				)
			}

			// Table: project_device_reference_relationship
			if _, err := l.svcCtx.ProjectDeviceReferenceModel.RemoveByReference(
				context, session, projectId, common.ConstReferenceTypeUIPlan, planId,
			); err != nil {
				return errors.Wrapf(
					errorx.Err(errorx.DBError, err.Error()),
					"failed to remove project device reference relationship, project_id: %s, plan_id: %s, error: %+v",
					projectId, planId, err,
				)
			}

			// Table: ui_plan
			if _, err := l.svcCtx.UiPlanModel.RemoveByPlanId(context, session, projectId, planId); err != nil {
				return errors.Wrapf(
					errorx.Err(errorx.DBError, err.Error()),
					"failed to remove ui plan, project_id: %s, plan_id: %s, error: %+v",
					projectId, planId, err,
				)
			}

			return l.handleScheduleUiPlan(uiPlan)
		},
	)
}

func (l *RemoveUiPlanLogic) handleScheduleUiPlan(origin *model.UiPlan) error {
	var beatOperator beat.ScheduleTaskOperator

	// stop schedule task
	if origin.Type == commonpb.TriggerMode_SCHEDULE.String() {
		beatOperator = beat.ConstBeatScheduleTaskOperatorRemove
	}

	if beatOperator != "" {
		// create or remove schedule task
		switch beatOperator {
		case beat.ConstBeatScheduleTaskOperatorRemove:
			if _, err := l.svcCtx.BeatRPC.RemovePeriodicTask(
				l.ctx, &beatpb.RemovePeriodicTaskReq{
					Name: l.generatePeriodicTaskName(origin.ProjectId, origin.PlanId),
				},
			); err != nil {
				l.Logger.Errorf(
					"failed to %s schedule task of ui plan[%s, %s, %s], error: %+v", strings.ToLower(beatOperator),
					origin.ProjectId, origin.PlanId, origin.Name, err,
				)
				return err
			}
		}
	}

	return nil
}
