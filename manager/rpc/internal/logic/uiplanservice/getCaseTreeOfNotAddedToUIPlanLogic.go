package uiplanservicelogic

import (
	"context"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type GetCaseTreeOfNotAddedToUIPlanLogic struct {
	*BaseLogic
}

func NewGetCaseTreeOfNotAddedToUIPlanLogic(
	ctx context.Context, svcCtx *svc.ServiceContext,
) *GetCaseTreeOfNotAddedToUIPlanLogic {
	return &GetCaseTreeOfNotAddedToUIPlanLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// GetCaseTreeOfNotAddedToUIPlan 获取不在指定的UI计划中的用例树
func (l *GetCaseTreeOfNotAddedToUIPlanLogic) GetCaseTreeOfNotAddedToUIPlan(in *pb.GetCaseTreeOfNotAddedToUIPlanReq) (
	out *pb.GetCaseTreeOfNotAddedToUIPlanResp, err error,
) {
	// validate the project_id in req
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, in.GetProjectId()); err != nil {
		return nil, err
	}

	// validate the plan_id in req
	plan, err := model.CheckUiPlanByPlanId(l.ctx, l.svcCtx.UiPlanModel, in.GetProjectId(), in.GetPlanId())
	if err != nil {
		return nil, err
	}

	nodes, err := l.getAllGitProjectTreeNodes(plan.ProjectId, plan.GitConfigId)
	if err != nil {
		return nil, err
	}

	cases, err := l.getCacheOfAllCasesInUIPlan(plan.ProjectId, plan.PlanId)
	if err != nil {
		return nil, err
	}

	var (
		root          *pb.UICaseTreeNode
		rootPath      string
		childrenCache = make(ParentChildrenCache, len(nodes))
		amountCache   = make(ParentAmount, len(nodes))
	)

	for _, node := range nodes {
		n := &pb.UICaseTreeNode{}
		if err = utils.Copy(n, node, l.converters...); err != nil {
			return nil, errors.Wrapf(
				errorx.Err(errorx.CopyToStructFailure, err.Error()),
				"failed to copy git project tree node[%s] to response, error: %+v",
				jsonx.MarshalToStringIgnoreError(node), err,
			)
		}

		if !node.ParentPath.Valid || node.ParentPath.String == "" {
			// root node
			root = n
			rootPath = node.Path
			root.Children = childrenCache[rootPath]
			for _, v := range root.Children {
				root.Amount += v.Amount
			}

			// in normal circumstances, the root node is the last node, so we can break the loop here
			break
		}

		if node.Type == string(common.ConstNodeTypeFunction) {
			_, ok := cases.Get(node.Path)
			if !ok {
				amountCache[node.ParentPath.String] += 1
			}

			continue
		} else {
			if v, ok := amountCache[node.Path]; ok {
				n.Amount = v
			}
			n.Children = childrenCache[node.Path]
			for _, v := range n.Children {
				n.Amount += v.Amount
			}
		}

		if _, ok := childrenCache[node.ParentPath.String]; !ok {
			childrenCache[node.ParentPath.String] = []*pb.UICaseTreeNode{n}
		} else {
			childrenCache[node.ParentPath.String] = append(childrenCache[node.ParentPath.String], n)
		}
	}

	if root == nil {
		return nil, errorx.Errorf(
			errorx.NotExists,
			"not found the root node of ui case tree, project_id: %s, git_config_id: %s",
			in.GetProjectId(), plan.GitConfigId,
		)
	}

	return &pb.GetCaseTreeOfNotAddedToUIPlanResp{CaseTree: []*pb.UICaseTreeNode{root}}, nil
}
