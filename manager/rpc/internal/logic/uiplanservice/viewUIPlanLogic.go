package uiplanservicelogic

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type ViewUiPlanLogic struct {
	*BaseLogic
}

func NewViewUiPlanLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ViewUiPlanLogic {
	return &ViewUiPlanLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// ViewUiPlan   查看计划
func (l *ViewUiPlanLogic) ViewUiPlan(in *pb.ViewUiPlanReq) (resp *pb.ViewUiPlanResp, err error) {
	// 验证req中的project_id
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, in.GetProjectId()); err != nil {
		return nil, err
	}

	// 验证req中的plan_id
	uiPlan, err := model.CheckUiPlanByPlanId(l.ctx, l.svcCtx.UiPlanModel, in.GetProjectId(), in.GetPlanId())
	if err != nil {
		return nil, err
	}

	resp = &pb.ViewUiPlanResp{Plan: &pb.UiPlan{}}
	if err = utils.Copy(resp.Plan, uiPlan, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy ui plan[%+v] to response, error: %+v",
			uiPlan, err,
		)
	}

	return resp, nil
}
