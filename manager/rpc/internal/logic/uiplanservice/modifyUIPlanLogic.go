package uiplanservicelogic

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/gorhill/cronexpr"
	"github.com/pkg/errors"
	"github.com/spf13/cast"
	"github.com/zeromicro/go-zero/core/jsonx"
	"github.com/zeromicro/go-zero/core/stores/sqlx"

	qetconstants "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/redislock"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"
	beatcommon "gitlab.ttyuyin.com/TestDevelopment/qet-backend-middleware/beat/common"
	beatpb "gitlab.ttyuyin.com/TestDevelopment/qet-backend-middleware/beat/rpc/pb"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	commonpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pkgpuller"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common/zrpc/beat"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type ModifyUiPlanLogic struct {
	*BaseLogic
}

func NewModifyUiPlanLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ModifyUiPlanLogic {
	return &ModifyUiPlanLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// ModifyUiPlan 编辑UI计划
func (l *ModifyUiPlanLogic) ModifyUiPlan(in *pb.ModifyUiPlanReq) (resp *pb.ModifyUiPlanResp, err error) {
	// validate the project_id in req
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, in.GetProjectId()); err != nil {
		return nil, err
	}

	// validate the plan_id in req
	origin, err := model.CheckUiPlanByPlanId(l.ctx, l.svcCtx.UiPlanModel, in.GetProjectId(), in.GetPlanId())
	if err != nil {
		return nil, err
	}

	// validate the cron_expression in req if the plan type is SCHEDULE
	if in.GetType() == commonpb.TriggerMode_SCHEDULE {
		schedule, err := cronexpr.Parse(in.GetCronExpression())
		if err != nil {
			return nil, errors.Wrapf(
				errorx.Err(errorx.CronExpressionParseError, err.Error()),
				"failed to parse the cron expression, cron: %s, error: %+v",
				in.GetCronExpression(), err,
			)
		}

		if err = checkScheduleWithMinInterval(schedule, 0, 0); err != nil {
			return nil, err
		}
	}

	// validate the git_config_id in req
	gitConfig, err := model.CheckGitConfigByConfigID(
		l.ctx, l.svcCtx.GitConfigModel, in.GetProjectId(), in.GetGitConfigId())
	if err != nil {
		return nil, err
	}

	// validate git purpose
	if gitConfig.Purpose != string(common.ConstGitPurposeUI) {
		return nil, errorx.Errorf(
			errorx.ProhibitedBehavior,
			"the git configuration is not for UI testing, project_id: %s, config_id: %s, purpose: %s",
			in.GetProjectId(), in.GetGitConfigId(), gitConfig.Purpose,
		)
	}

	// validate the devices in req
	if len(in.GetDevices()) > 0 {
		if in.GetDeviceType() != commonpb.DeviceType_REAL_PHONE {
			return nil, errorx.Err(
				errorx.ProhibitedBehavior, "device assignment is only permitted when the device type is `real_phone`",
			)
		}

		devices, err := l.getAllUIProjectDevices(in.GetProjectId())
		if err != nil {
			return nil, err
		}

		for _, udid := range in.GetDevices() {
			if _, ok := devices.Get(udid); !ok {
				return nil, errorx.Errorf(
					errorx.ProhibitedBehavior,
					"the selected device is not included in the project devices for UI testing, udid: %s",
					udid,
				)
			}
		}
	}

	// validate the app_download_link in req if the empty is valid
	if len(in.GetAppDownloadLink()) == 0 {
		_, err = pkgpuller.NewAppPkgPuller(l.ctx, pkgpuller.AppPkgNameType(in.GetPackageName()), "")
		if err != nil {
			return nil, errorx.Errorf(
				errorx.ProhibitedBehavior,
				"the app_download_link can't be empty, error: %+v", err,
			)
		}
	}

	// 获取redis锁
	key := fmt.Sprintf("%s:%s:%s", common.ConstLockUiPlanProjectIdPlanIdPrefix, in.GetProjectId(), in.GetPlanId())
	lock, err := redislock.NewRedisLockAndAcquire(l.svcCtx.Redis, key, redislock.WithExpire(common.ConstLockExpireTime))
	if err != nil {
		return nil, err
	}
	defer func() {
		// release redis lock
		e := lock.Release()
		if e != nil {
			// l.Logger.Error(e)
			if re, ok := errorx.RootError(e); ok && re.Code() != errorx.ReleaseRedisLockFailure && err == nil {
				err = e
			}
		}
	}()

	uiPlan, err := l.modify(in, origin)
	if err != nil {
		return nil, err
	}

	resp = &pb.ModifyUiPlanResp{Plan: &pb.UiPlan{}}
	if err = utils.Copy(resp.Plan, uiPlan, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy ui plan[%+v] to response, error: %+v",
			uiPlan, err,
		)
	}

	return resp, nil
}

func (l *ModifyUiPlanLogic) modify(req *pb.ModifyUiPlanReq, origin *model.UiPlan) (*model.UiPlan, error) {
	var devices string
	if len(req.GetDevices()) > 0 {
		// 由于`protojson`只能序列化`[]proto.Message`，而`tags`字段类型为`[]string`，Go Struct跟PB没有大的区别，因此直接使用`jsonx`暂代`protojson`
		devices = jsonx.MarshalToStringIgnoreError(req.GetDevices())
	}

	testArgs := jsonx.MarshalToStringIgnoreError(req.GetTestArgs())

	uiPlan := &model.UiPlan{
		Id:         origin.Id,
		ProjectId:  origin.ProjectId,
		CategoryId: req.GetCategoryId(),
		PlanId:     origin.PlanId,
		Name:       req.GetName(),
		Description: sql.NullString{
			String: req.GetDescription(),
			Valid:  req.GetDescription() != "",
		},
		Type: req.GetType().String(),
		CronExpression: sql.NullString{
			String: req.GetCronExpression(),
			Valid:  req.GetCronExpression() != "",
		},
		PriorityType:  int64(req.GetPriorityType()),
		GitConfigId:   req.GetGitConfigId(),
		ExecutionMode: int64(req.GetExecutionMode()),
		DeviceType:    int64(req.GetDeviceType()),
		PlatformType:  int64(req.GetPlatformType()),
		Devices: sql.NullString{
			String: devices,
			Valid:  devices != "",
		},
		Together:    cast.ToInt64(req.GetTogether()),
		PackageName: req.GetPackageName(),
		AppName: sql.NullString{
			String: req.GetAppName(),
			Valid:  req.GetAppName() != "",
		},
		CallbackUrl: sql.NullString{
			String: req.GetCallbackUrl(),
			Valid:  req.GetCallbackUrl() != "",
		},
		AppDownloadLink: req.GetAppDownloadLink(),
		AppVersion: sql.NullString{
			String: req.GetAppVersion(),
			Valid:  req.GetAppVersion() != "",
		},
		TestLanguage:        int64(req.GetTestLanguage()),
		TestLanguageVersion: req.GetTestLanguageVersion(),
		TestFramework:       int64(req.GetTestFramework()),
		TestArgs: sql.NullString{
			String: testArgs,
			Valid:  testArgs != "",
		},
		ExecutionEnvironment: sql.NullString{
			String: req.GetExecutionEnvironment(),
			Valid:  req.GetExecutionEnvironment() != "",
		},
		FailRetry: int64(req.GetFailRetry()),
		State:     int64(req.GetState()),
		MaintainedBy: sql.NullString{
			String: req.GetMaintainedBy(),
			Valid:  req.GetMaintainedBy() != "",
		},
		CreatedBy: origin.CreatedBy,
		UpdatedBy: l.currentUser.Account,
		CreatedAt: origin.CreatedAt,
		UpdatedAt: time.Now(),
	}

	if err := l.svcCtx.UiPlanModel.Trans(
		l.ctx, func(context context.Context, session sqlx.Session) error {
			if err := l.updateProjectDeviceRelationship(context, session, uiPlan, req.GetDevices()); err != nil {
				return err
			}

			if _, err := l.svcCtx.UiPlanModel.UpdateTX(context, session, uiPlan); err != nil {
				return errors.Wrapf(
					errorx.Err(errorx.DBError, err.Error()),
					"failed to update values to table, table: %s, values: %s, error: %+v",
					l.svcCtx.UiPlanModel.Table(), jsonx.MarshalIgnoreError(uiPlan), err,
				)
			}

			return l.handleScheduleUiPlan(req, origin)
		},
	); err != nil {
		return nil, err
	}

	return uiPlan, nil
}

func (l *ModifyUiPlanLogic) handleScheduleUiPlan(req *pb.ModifyUiPlanReq, origin *model.UiPlan) error {
	var (
		beatOperator beat.ScheduleTaskOperator

		reqState = req.GetState()
		reqType  = req.GetType()
		reqCron  = req.GetCronExpression()
	)

	// enable to disable
	if origin.State == int64(qetconstants.EnableStatus) && reqState == pb.CommonState_CS_DISABLE {
		// stop schedule task
		if origin.Type == commonpb.TriggerMode_SCHEDULE.String() {
			beatOperator = beat.ConstBeatScheduleTaskOperatorRemove
		}
	}

	// disable to enable
	if origin.State == int64(qetconstants.DisableStatus) && reqState == pb.CommonState_CS_ENABLE {
		// start schedule task
		if reqType == commonpb.TriggerMode_SCHEDULE {
			beatOperator = beat.ConstBeatScheduleTaskOperatorCreate
		}
	}

	// enable to enable
	if origin.State == int64(qetconstants.EnableStatus) && reqState == pb.CommonState_CS_ENABLE {
		// schedule to other type
		if origin.Type == commonpb.TriggerMode_SCHEDULE.String() && reqType != commonpb.TriggerMode_SCHEDULE {
			beatOperator = beat.ConstBeatScheduleTaskOperatorRemove
		}

		// other type to schedule
		if origin.Type != commonpb.TriggerMode_SCHEDULE.String() && reqType == commonpb.TriggerMode_SCHEDULE {
			beatOperator = beat.ConstBeatScheduleTaskOperatorCreate
		}

		// schedule to schedule, and cron expression has been changed
		if origin.Type == commonpb.TriggerMode_SCHEDULE.String() && reqType == commonpb.TriggerMode_SCHEDULE && origin.CronExpression.String != req.GetCronExpression() {
			beatOperator = beat.ConstBeatScheduleTaskOperatorCreate
		}
	}

	if beatOperator != "" {
		// create or remove schedule task
		switch beatOperator {
		case beat.ConstBeatScheduleTaskOperatorCreate:
			if _, err := l.svcCtx.BeatRPC.CreateOrModifyPeriodicTask(
				l.ctx, &beatpb.CreateOrModifyPeriodicTaskReq{
					Name:  l.generatePeriodicTaskName(origin.ProjectId, origin.PlanId),
					Type:  constants.MQTaskTypeDispatcherPeriodicPlanTask,
					Queue: constants.MQNamePeriodicPlanTask,
					Spec:  reqCron,
					Payload: protobuf.MarshalJSONToStringIgnoreError(
						&commonpb.PeriodicPlanTaskInfo{
							ProjectId:      origin.ProjectId,
							PlanId:         origin.PlanId,
							CronExpression: reqCron,
							PlanType:       commonpb.PlanType_UI,
						},
					),
					Version: beatcommon.V2,
				},
			); err != nil {
				l.Logger.Errorf(
					"failed to %s schedule task of ui plan[%s, %s, %s], error: %+v",
					strings.ToLower(beatOperator), origin.ProjectId, origin.PlanId, origin.Name, err,
				)
				return err
			}
		case beat.ConstBeatScheduleTaskOperatorRemove:
			if _, err := l.svcCtx.BeatRPC.RemovePeriodicTask(
				l.ctx, &beatpb.RemovePeriodicTaskReq{
					Name: l.generatePeriodicTaskName(origin.ProjectId, origin.PlanId),
				},
			); err != nil {
				l.Logger.Errorf(
					"failed to %s schedule task of ui plan[%s, %s, %s], error: %+v",
					strings.ToLower(beatOperator), origin.ProjectId, origin.PlanId, origin.Name, err,
				)
				return err
			}
		}
	}

	return nil
}
