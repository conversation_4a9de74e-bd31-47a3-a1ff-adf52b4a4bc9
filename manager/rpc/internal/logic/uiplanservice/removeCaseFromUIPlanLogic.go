package uiplanservicelogic

import (
	"context"
	"database/sql"
	"fmt"
	"time"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/mr"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zyedidia/generic"
	"github.com/zyedidia/generic/hashmap"
	"github.com/zyedidia/generic/set"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/caller"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type RemoveCaseFromUIPlanLogic struct {
	*BaseLogic
}

func NewRemoveCaseFromUIPlanLogic(ctx context.Context, svcCtx *svc.ServiceContext) *RemoveCaseFromUIPlanLogic {
	return &RemoveCaseFromUIPlanLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// RemoveCaseFromUIPlan 移除UI计划中的用例
func (l *RemoveCaseFromUIPlanLogic) RemoveCaseFromUIPlan(in *pb.RemoveCaseFromUIPlanReq) (
	out *pb.RemoveCaseFromUIPlanResp, err error,
) {
	// validate the project_id in req
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, in.GetProjectId()); err != nil {
		return nil, err
	}

	// validate the plan_id in req
	plan, err := model.CheckUiPlanByPlanId(l.ctx, l.svcCtx.UiPlanModel, in.GetProjectId(), in.GetPlanId())
	if err != nil {
		return nil, err
	}

	if len(in.GetCasePaths()) == 0 {
		l.Warn("the number of UI cases to be removed is 0")
		return &pb.RemoveCaseFromUIPlanResp{}, nil
	}

	removes := set.NewHashset(uint64(len(in.GetCasePaths())), generic.Equals[string], generic.HashString)
	for _, path := range in.GetCasePaths() {
		removes.Put(path)
	}

	// all cases in ui plan
	cache, existing, err := l.getAllCasesInUIPlan(plan.ProjectId, plan.PlanId)
	if err != nil {
		return nil, err
	}

	// the cases that are both in `removes` and `existing`
	removes = removes.Intersection(existing)
	if removes.Size() == 0 {
		l.Warnf(
			"the actual number of UI cases to be removed is 0, project_id: %s, plan_id: %s, git_config_id: %s",
			plan.ProjectId, plan.PlanId, plan.GitConfigId,
		)
		return &pb.RemoveCaseFromUIPlanResp{}, nil
	}

	key := fmt.Sprintf("%s:%s:%s", common.ConstLockUiPlanProjectIdPlanIdPrefix, in.GetProjectId(), in.GetPlanId())
	fn := func() error {
		return l.svcCtx.UiPlanReferenceModel.Trans(
			l.ctx,
			func(context context.Context, session sqlx.Session) error {
				return mr.MapReduceVoid[*model.UiPlanReferenceRelationship, any](
					func(source chan<- *model.UiPlanReferenceRelationship) {
						for _, remove := range removes.Keys() {
							if remove == "" {
								continue
							}

							item, ok := cache.Get(remove)
							if !ok || item.Id == 0 {
								continue
							}

							source <- item
						}
					}, func(item *model.UiPlanReferenceRelationship, writer mr.Writer[any], cancel func(error)) {
						var err error
						defer func() {
							if err != nil {
								cancel(err)
							}
						}()

						if item == nil {
							return
						}

						item.Deleted = int64(constants.HasDeleted)
						item.DeletedBy = sql.NullString{
							String: l.currentUser.Account,
							Valid:  true,
						}
						item.DeletedAt = sql.NullTime{
							Time:  time.Now(),
							Valid: true,
						}

						if _, err = l.svcCtx.UiPlanReferenceModel.Update(
							context, session, item,
						); err != nil {
							err = errors.Wrapf(
								errorx.Err(errorx.DBError, err.Error()),
								"failed to remove ui plan reference by project_id[%s], plan_id[%s], git_config_id[%s] and path[%s], error: %+v",
								item.ProjectId, item.PlanId, item.GitConfigId, item.Path, err,
							)
							return
						}
					}, func(pipe <-chan any, cancel func(error)) {
					},
				)
			},
		)
	}
	if err = caller.LockDo(l.svcCtx.Redis, key, fn); err != nil {
		return nil, err
	}

	return &pb.RemoveCaseFromUIPlanResp{}, nil
}

func (l *RemoveCaseFromUIPlanLogic) getAllCases(projectID, gitConfigID string) (*set.Set[string], error) {
	nodes, err := l.BaseLogic.getAllGitProjectTreeNodes(projectID, gitConfigID)
	if err != nil {
		return nil, err
	}

	s := set.NewHashset(uint64(len(nodes)), generic.Equals[string], generic.HashString)
	for _, node := range nodes {
		if node.Type != string(common.ConstNodeTypeFunction) {
			continue
		}

		s.Put(node.Path)
	}

	return &s, nil
}

func (l *RemoveCaseFromUIPlanLogic) getAllCasesInUIPlan(projectID, planID string) (
	*hashmap.Map[string, *model.UiPlanReferenceRelationship], *set.Set[string], error,
) {
	rrs, err := l.BaseLogic.getAllCasesInUIPlan(projectID, planID)
	if err != nil {
		return nil, nil, err
	}

	m := hashmap.New[string, *model.UiPlanReferenceRelationship](
		uint64(len(rrs)), generic.Equals[string], generic.HashString,
	)
	s := set.NewHashset(uint64(len(rrs)), generic.Equals[string], generic.HashString)
	for _, rr := range rrs {
		m.Put(rr.Path, rr)
		s.Put(rr.Path)
	}

	return m, &s, nil
}
