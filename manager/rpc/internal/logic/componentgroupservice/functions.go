package componentgroupservicelogic

import (
	"fmt"

	"github.com/emirpasic/gods/sets/hashset"
	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

func diffArguments[A pb.Argument](original, current []A, hasReferenced bool) (bool, error) {
	getNames := func(in []A) []any {
		out := make([]any, 0, len(in))
		for _, i := range in {
			name := i.ArgName()
			if name != "" {
				out = append(out, name)
			}
		}
		return out
	}

	oSet := hashset.New(getNames(original)...)
	cSet := hashset.New(getNames(current)...)

	// 差集：在`oSet`中而不在`cSet`中的元素
	dSet := oSet.Difference(cSet)
	if dSet.Size() > 0 {
		// `dSet`中有数据，代表原来有现在没有了，即减少
		if hasReferenced {
			// 当前组件组已被组件组或API用例引用，则不允许移除原有的参数（包括：入参、出参）
			return true, errors.WithStack(errorx.Err(errorx.ProhibitedBehavior, fmt.Sprintf("cannot remove the original parameters when it has been referenced: %s", logic.ContainerToString(dSet))))
		}
		return true, nil
	}

	// 差集中没有元素 且 `oSet`中的元素个数少于`cSet`，即新增
	if oSet.Size() < cSet.Size() {
		return true, nil
	}

	// 数量没有变化，则判断其它字段是否有改变
	for _, o := range original {
		for _, c := range current {
			if o.ArgName() == c.ArgName() && o.ArgDesc() != c.ArgDesc() {
				return true, nil
			}
		}
	}

	return false, nil
}
