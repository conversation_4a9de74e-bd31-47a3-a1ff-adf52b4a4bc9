package componentgroupservicelogic

import (
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type createReferenceRelationshipInternalReq struct {
	ProjectId        string         `json:"project_id"`
	ComponentGroupId string         `json:"component_group_id"`
	Version          string         `json:"version"`
	Relations        []*pb.Relation `json:"relations"`
}

type createElementAndComponentInternalReq struct {
	ProjectId        string      `json:"project_id"`
	ComponentGroupId string      `json:"component_group_id"`
	Version          string      `json:"version"`
	Nodes            []*pb.Node  `json:"nodes"`
	Edges            []*pb.Edge  `json:"edges"`
	Combos           []*pb.Combo `json:"combos"`
}

type searchComponentGroupInternalReq struct {
	*pb.SearchComponentGroupReq

	DrillDown bool `json:"drill_down"`
}
