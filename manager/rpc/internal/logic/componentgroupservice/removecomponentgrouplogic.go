package componentgroupservicelogic

import (
	"context"
	"fmt"

	"github.com/hashicorp/go-multierror"
	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/mr"
	"github.com/zeromicro/go-zero/core/stores/sqlx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/redislock"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type RemoveComponentGroupLogic struct {
	*BaseLogic
}

func NewRemoveComponentGroupLogic(ctx context.Context, svcCtx *svc.ServiceContext) *RemoveComponentGroupLogic {
	return &RemoveComponentGroupLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// RemoveComponentGroup 删除组件组
func (l *RemoveComponentGroupLogic) RemoveComponentGroup(in *pb.RemoveComponentGroupReq) (
	resp *pb.RemoveComponentGroupResp, err error,
) {
	// validate the project_id in req
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, in.GetProjectId()); err != nil {
		return nil, err
	}

	componentGroupIds := in.GetComponentGroupIds()
	workers := len(componentGroupIds)
	if workers > common.ConstMRMaxWorkers {
		workers = common.ConstMRMaxWorkers
	}

	mr.ForEach(
		func(source chan<- any) {
			for _, componentGroupId := range componentGroupIds {
				source <- componentGroupId
			}
		}, func(item any) {
			componentGroupId, ok := item.(string)
			if !ok {
				err = multierror.Append(
					err, errorx.Err(
						errorx.InternalError,
						fmt.Sprintf("the component group id[%v (%T)] is not a string", item, item),
					),
				)
			} else {
				if e := l.remove(in.GetProjectId(), componentGroupId); e != nil {
					err = multierror.Append(err, e)
				}
			}
		}, mr.WithContext(l.ctx), mr.WithWorkers(workers),
	)

	return &pb.RemoveComponentGroupResp{}, err
}

func (l *RemoveComponentGroupLogic) remove(projectId, componentGroupId string) (err error) {
	// validate the component_group_id in req
	cg, err := model.CheckComponentGroupByComponentGroupId(
		l.ctx, l.svcCtx.ComponentGroupModel, projectId, componentGroupId, "",
	)
	if err != nil {
		return err
	}

	// validate the reference relationship
	rrs, err := l.svcCtx.ComponentGroupReferenceModel.FindLatestReferenceByComponentGroupId(
		l.ctx, l.svcCtx.ComponentGroupModel, l.svcCtx.ApiCaseModel, l.svcCtx.InterfaceCaseModel, projectId,
		componentGroupId,
	)
	if err != nil && !errors.Is(err, model.ErrNotFound) {
		return errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()),
			"failed to find the reference relationship with project_id[%s] and component_group_id[%s], error: %+v",
			projectId, componentGroupId, err,
		)
	} else if len(rrs) > 0 {
		return errors.WithStack(
			errorx.Err(
				errorx.ProhibitedBehavior, fmt.Sprintf(
					"cannot remove the component group[%s] which is referenced by %d component groups or api cases or interface cases",
					cg.Name, len(rrs),
				),
			),
		)
	}

	// acquire redis lock
	key := fmt.Sprintf(
		"%s:%s:%s", common.ConstLockComponentGroupProjectIdComponentGroupIdPrefix, projectId, componentGroupId,
	)
	lock, err := redislock.NewRedisLockAndAcquire(l.svcCtx.Redis, key, redislock.WithExpire(common.ConstLockExpireTime))
	if err != nil {
		return err
	}
	defer func() {
		// release redis lock
		e := lock.Release()
		if e != nil {
			l.Logger.Error(e)
			if re, ok := errorx.RootError(e); ok && re.Code() != errorx.ReleaseRedisLockFailure && err == nil {
				err = e
			}
		}
	}()

	return l.svcCtx.ComponentGroupModel.Trans(
		l.ctx, func(context context.Context, session sqlx.Session) error {
			// Table: tag_reference_relationship
			if _, err := l.svcCtx.TagReferenceModel.RemoveByReferenceId(
				context, session, projectId, common.ConstReferenceTypeComponentGroup, componentGroupId, "",
			); err != nil {
				return errors.Wrapf(
					errorx.Err(errorx.DBError, err.Error()),
					"failed to remove tag reference relationship with project_id[%s], reference_type[%s] and reference_id[%s], error: %+v",
					projectId, common.ConstReferenceTypeComponentGroup, componentGroupId, err,
				)
			}

			// Table: function_reference_relationship
			if _, err := l.svcCtx.FunctionReferenceModel.RemoveByReferenceId(
				context, session, projectId, constants.ComponentGroup, componentGroupId,
			); err != nil {
				return errors.Wrapf(
					errorx.Err(errorx.DBError, err.Error()),
					"failed to remove data processing function reference relationship with project_id[%s], reference_type[%s] and reference_id[%s], error: %+v",
					projectId, constants.ComponentGroup, componentGroupId, err,
				)
			}

			// Table: review_record
			if _, err := l.svcCtx.ReviewRecordModel.RemoveByResource(
				context, session, projectId, common.ReviewResourceType(cg.ComponentGroupType), componentGroupId,
			); err != nil {
				return errors.Wrapf(
					errorx.Err(errorx.DBError, err.Error()),
					"failed to remove review record with project_id[%s], resource_type[%s] and resource_id[%s], error: %+v",
					projectId, cg.ComponentGroupType, componentGroupId, err,
				)
			}

			// Table: component_group_reference_relationship
			if _, err := l.svcCtx.ComponentGroupReferenceModel.RemoveByReferenceId(
				context, session, projectId, constants.ComponentGroup, componentGroupId,
			); err != nil {
				return errors.Wrapf(
					errorx.Err(errorx.DBError, err.Error()),
					"failed to remove component group reference relationship with project_id[%s], reference_type[%s] and reference_id[%s], error: %+v",
					projectId, constants.ComponentGroup, componentGroupId, err,
				)
			}

			// Table: component_group_element
			if _, err := l.svcCtx.ComponentGroupElementModel.RemoveByComponentGroupId(
				context, session, projectId, componentGroupId,
			); err != nil {
				return errors.Wrapf(
					errorx.Err(errorx.DBError, err.Error()),
					"failed to remove component group element with project_id[%s] and component_group_id[%s], error: %+v",
					projectId, componentGroupId, err,
				)
			}

			// Table: component
			if _, err := l.svcCtx.ComponentModel.RemoveByParentId(
				context, session, projectId, componentGroupId, constants.ComponentGroup,
			); err != nil {
				return errors.Wrapf(
					errorx.Err(errorx.DBError, err.Error()),
					"failed to remove component with project_id[%s], parent_id[%s], parent_type[%s], error: %+v",
					projectId, componentGroupId, constants.ComponentGroup, err,
				)
			}

			// Table: category_tree
			if _, err := l.svcCtx.CategoryTreeModel.RemoveByNodeId(
				context, session, l.svcCtx.CategoryModel, projectId, common.ConstCategoryTreeTypeComponentGroup,
				componentGroupId,
			); err != nil {
				return errors.Wrapf(
					errorx.Err(errorx.DBError, err.Error()),
					"failed to remove category tree with project_id[%s] and component_group_id[%s], error: %+v",
					projectId, componentGroupId, err,
				)
			}

			// Table: category
			if _, err := l.svcCtx.CategoryModel.RemoveByNodeId(
				context, session, projectId, common.ConstCategoryTreeTypeComponentGroup, componentGroupId,
			); err != nil {
				return errors.Wrapf(
					errorx.Err(errorx.DBError, err.Error()),
					"failed to remove category with project_id[%s] and component_group_id[%s], error: %+v",
					projectId, componentGroupId, err,
				)
			}

			// Table: component_group
			if _, err := l.svcCtx.ComponentGroupModel.RemoveByComponentGroupId(
				context, session, projectId, componentGroupId,
			); err != nil {
				return errors.Wrapf(
					errorx.Err(errorx.DBError, err.Error()),
					"failed to remove component group with project_id[%s] and component_group_id[%s], error: %+v",
					projectId, componentGroupId, err,
				)
			}

			return nil
		},
	)
}
