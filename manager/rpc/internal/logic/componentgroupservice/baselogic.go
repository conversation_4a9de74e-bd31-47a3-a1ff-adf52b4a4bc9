package componentgroupservicelogic

import (
	"context"
	"database/sql"
	"fmt"
	"time"

	"github.com/Masterminds/squirrel"
	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"
	"github.com/zeromicro/go-zero/core/lang"
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/mr"
	"github.com/zeromicro/go-zero/core/stores/sqlx"

	qetconstants "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sqlbuilder"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/userinfo"
	qetutils "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/utils"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/logic"
	apicaseservicelogic "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/logic/apicaseservice"
	categoryservicelogic "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/logic/categoryservice"
	interfacecaseservicelogic "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/logic/interfacedefinitionservice"
	tagservicelogic "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/logic/tagservice"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type BaseLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext

	currentUser *userinfo.UserInfo

	createCategoryLogic      *categoryservicelogic.CreateCategoryLogic
	modifyCategoryLogic      *categoryservicelogic.ModifyCategoryLogic
	moveCategoryLogic        *categoryservicelogic.MoveCategoryTreeLogic
	createTagLogic           *tagservicelogic.CreateTagLogic
	modifyApiCaseLogic       *apicaseservicelogic.ModifyApiCaseLogic
	modifyInterfaceCaseLogic *interfacecaseservicelogic.ModifyInterfaceCaseLogic

	converters []qetutils.TypeConverter
}

func newBaseLogic(ctx context.Context, svcCtx *svc.ServiceContext) *BaseLogic {
	return &BaseLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,

		currentUser: userinfo.FromContext(ctx),

		createCategoryLogic:      categoryservicelogic.NewCreateCategoryLogic(ctx, svcCtx),
		modifyCategoryLogic:      categoryservicelogic.NewModifyCategoryLogic(ctx, svcCtx),
		moveCategoryLogic:        categoryservicelogic.NewMoveCategoryTreeLogic(ctx, svcCtx),
		createTagLogic:           tagservicelogic.NewCreateTagLogic(ctx, svcCtx),
		modifyApiCaseLogic:       apicaseservicelogic.NewModifyApiCaseLogic(ctx, svcCtx),
		modifyInterfaceCaseLogic: interfacecaseservicelogic.NewModifyInterfaceCaseLogic(ctx, svcCtx),

		converters: []qetutils.TypeConverter{
			logic.SqlNullStringToTags(),
			logic.StringToResourceState(),
			logic.StringToImports(),
			logic.StringToExports(),
			logic.StringToAccountConfig(),
		},
	}
}

func (l *BaseLogic) generateComponentGroupId(projectId string) (string, error) {
	g := qetutils.NewUniqueIdGenerator(
		qetutils.WithGenerateFunc(utils.GenComponentGroupId), qetutils.WithIsUniqueFunc(
			func(id string) bool {
				r, err := l.svcCtx.ComponentGroupModel.FindLatestOneNoCache(l.ctx, projectId, id)
				if err == model.ErrNotFound || r == nil {
					return true
				}
				return false
			},
		),
	)
	componentGroupId := g.Next()
	if componentGroupId == "" {
		return "", errors.WithStack(
			errorx.Err(
				errorx.GenerateUniqueIdFailure, "failed to generate component group id, please try it later",
			),
		)
	}

	return componentGroupId, nil
}

func (l *BaseLogic) generateVersion(projectId, componentGroupId string) (string, error) {
	g := qetutils.NewUniqueIdGenerator(
		qetutils.WithGenerateFunc(utils.GenVersion), qetutils.WithIsUniqueFunc(
			func(id string) bool {
				r, err := l.svcCtx.ComponentGroupModel.FindOneByProjectIdComponentGroupIdVersion(
					l.ctx, projectId, componentGroupId, id,
				)
				if err == model.ErrNotFound || r == nil {
					return true
				}
				return false
			},
		),
	)
	version := g.Next()
	if version == "" {
		return "", errors.WithStack(
			errorx.Err(
				errorx.GenerateUniqueIdFailure, "failed to generate version id, please try it later",
			),
		)
	}

	return version, nil
}

func (l *BaseLogic) createReferenceRelationship(
	ctx context.Context, session sqlx.Session, req createReferenceRelationshipInternalReq,
) error {
	if ctx == nil {
		ctx = l.ctx
	}

	workers := len(req.Relations)
	if workers > common.ConstMRMaxWorkers {
		workers = common.ConstMRMaxWorkers
	}

	return mr.MapReduceVoid[*pb.Relation, any](
		func(source chan<- *pb.Relation) {
			for _, relation := range req.Relations {
				source <- relation
			}
		}, func(item *pb.Relation, writer mr.Writer[any], cancel func(error)) {
			var err error
			defer func() {
				if err != nil {
					cancel(err)
				}
			}()

			referenceId := item.GetReferenceId()
			children := item.GetChildrenRelations()

			if referenceId != "" {
				data := &model.ComponentGroupReferenceRelationship{
					ProjectId:        req.ProjectId,
					ReferenceType:    constants.ComponentGroup,
					ReferenceId:      req.ComponentGroupId,
					ReferenceVersion: req.Version,
					ComponentGroupId: referenceId,
					Deleted:          int64(qetconstants.NotDeleted),
					CreatedBy:        l.currentUser.Account,
					UpdatedBy:        l.currentUser.Account,
					CreatedAt:        time.Now(),
					UpdatedAt:        time.Now(),
				}
				if _, err = l.svcCtx.ComponentGroupReferenceModel.Insert(ctx, session, data); err != nil {
					err = errors.Wrapf(
						errorx.Err(errorx.DBError, err.Error()),
						"failed to insert table[%s] with values[%+v], error: %+v",
						l.svcCtx.ComponentGroupReferenceModel.Table(), data, err,
					)
					return
				}
			}

			if len(children) > 0 {
				for _, child := range children {
					if err = l.createReferenceRelationship(
						ctx, session, createReferenceRelationshipInternalReq{
							ProjectId:        req.ProjectId,
							ComponentGroupId: req.ComponentGroupId,
							Version:          req.Version,
							Relations:        child,
						},
					); err != nil {
						return
					}
				}
			}
		}, func(pipe <-chan any, cancel func(error)) {
		}, mr.WithContext(ctx), mr.WithWorkers(workers),
	)
}

func (l *BaseLogic) createElementAndComponent(
	ctx context.Context, session sqlx.Session, req createElementAndComponentInternalReq,
) error {
	if ctx == nil {
		ctx = l.ctx
	}

	workers := len(req.Edges) + len(req.Combos) + len(req.Nodes)
	if workers > common.ConstMRMaxWorkers {
		workers = common.ConstMRMaxWorkers
	}

	return mr.MapReduceVoid[any, *model.FunctionReferenceRelationship](
		func(source chan<- any) {
			generate(req.Edges, source)
			generate(req.Combos, source)
			generate(req.Nodes, source)
		}, func(item any, writer mr.Writer[*model.FunctionReferenceRelationship], cancel func(error)) {
			var err error
			defer func() {
				if err != nil {
					cancel(err)
				}
			}()

			if e, ok := item.(pb.Element); ok {
				if err = l.createElement(
					ctx, session, req.ProjectId, req.ComponentGroupId, req.Version, e, writer,
				); err != nil {
					return
				}
			}

			if c, ok := item.(pb.Component); ok {
				if err = l.createComponent(
					ctx, session, req.ProjectId, req.ComponentGroupId, req.Version, c, writer,
				); err != nil {
					return
				}
			}
		}, func(pipe <-chan *model.FunctionReferenceRelationship, cancel func(error)) {
			var (
				err   error
				cache = make(types.StringPlaceholderCache)
			)
			defer func() {
				if err != nil {
					cancel(err)
				}
			}()

			for item := range pipe {
				key := fmt.Sprintf("%s:%s", item.FunctionName, item.FunctionType)
				if _, ok := cache[key]; ok {
					continue
				}

				if _, err = l.svcCtx.FunctionReferenceModel.Insert(ctx, session, item); err != nil {
					err = errors.Wrapf(
						errorx.Err(errorx.DBError, err.Error()),
						"failed to insert table[%s] with values[%+v], error: %+v",
						l.svcCtx.FunctionReferenceModel.Table(), item, err,
					)
					return
				}

				cache[key] = lang.Placeholder
			}
		}, mr.WithContext(ctx), mr.WithWorkers(workers),
	)
}

func generate[E pb.Element](elements []E, source chan<- any) {
	for _, element := range elements {
		source <- element
	}
}

func (l *BaseLogic) createElement(
	ctx context.Context, session sqlx.Session, projectId, componentGroupId, version string, element pb.Element,
	_ mr.Writer[*model.FunctionReferenceRelationship],
) error {
	if ctx == nil {
		ctx = l.ctx
	}

	v := &model.ComponentGroupElement{
		ProjectId:        projectId,
		ComponentGroupId: componentGroupId,
		Version:          version,
		ElementId:        element.ElementId(),
		ElementType:      element.ElementType(),
		Data:             jsonx.MarshalToStringIgnoreError(element),
		CreatedBy:        l.currentUser.Account,
		UpdatedBy:        l.currentUser.Account,
	}
	if _, err := l.svcCtx.ComponentGroupElementModel.InsertTX(ctx, session, v); err != nil {
		return errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()), "failed to insert table[%s] with values[%+v], error: %+v",
			l.svcCtx.ComponentGroupElementModel.Table(), v, err,
		)
	}

	return nil
}

func (l *BaseLogic) createComponent(
	ctx context.Context, session sqlx.Session, projectId, componentGroupId, version string, component pb.Component,
	writer mr.Writer[*model.FunctionReferenceRelationship],
) error {
	if ctx == nil {
		ctx = l.ctx
	}

	data := sql.NullString{}
	if component.ComponentData() != nil {
		data.String = protobuf.MarshalJSONToStringIgnoreError(component.ComponentData())
		data.Valid = true

		fn, ok := pb.ComponentExecutionDataFuncMap[component.ComponentType()]
		if !ok {
			return errors.WithStack(
				errorx.Err(
					errorx.DoesNotSupport,
					fmt.Sprintf("the component type[%s] doesn't support", component.ComponentType()),
				),
			)
		}

		_, v := fn()
		if err := pb.ValidateUnmarshalOptions.Unmarshal([]byte(data.String), v); err != nil {
			return errors.Wrapf(
				errorx.Err(errorx.ValidateFailure, err.Error()),
				"component data[%s] unmarshal to proto message[%T] failure, error: %+v", data.String, v, err,
			)
		}

		if err := v.ValidateAll(); err != nil {
			return errors.Wrapf(
				errorx.Err(errorx.ValidateFailure, err.Error()),
				"component data[%s] convert to proto message[%T] failure, error: %+v", data.String, v, err,
			)
		}

		if f, ok := v.(pb.FunctionComponent); ok {
			for _, function := range f.Functions() {
				writer.Write(
					&model.FunctionReferenceRelationship{
						ProjectId:        projectId,
						ReferenceType:    constants.ComponentGroup,
						ReferenceId:      componentGroupId,
						ReferenceVersion: version,
						FunctionName:     function.Name,
						FunctionType:     function.Type,
						CreatedBy:        l.currentUser.Account,
						UpdatedBy:        l.currentUser.Account,
						CreatedAt:        time.Now(),
						UpdatedAt:        time.Now(),
					},
				)
			}
		}
	}

	v := &model.Component{
		ProjectId:     projectId,
		ParentId:      componentGroupId,
		ParentType:    constants.ComponentGroup,
		ParentVersion: version,
		ComponentId:   component.ComponentId(),
		ComponentType: component.ComponentType(),
		Name:          component.ComponentName(),
		Description:   sql.NullString{},
		Data:          data,
		CreatedBy:     l.currentUser.Account,
		UpdatedBy:     l.currentUser.Account,
	}
	if _, err := l.svcCtx.ComponentModel.InsertTX(ctx, session, v); err != nil {
		return errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()), "failed to insert table[%s] with values[%+v], error: %+v",
			l.svcCtx.ComponentModel.Table(), v, err,
		)
	}

	return nil
}

func (l *BaseLogic) generateSearchSqlBuilder(req searchComponentGroupInternalReq) (sb, scb squirrel.SelectBuilder) {
	/*
		SQL:
		SELECT t.*
		FROM component_group AS t
			LEFT JOIN (
				SELECT t1.*
			    FROM category AS t1
					INNER JOIN category_tree AS t2 ON t1.`project_id` = t2.`project_id` AND t1.`category_id` = t2.`descendant`
			    WHERE t1.`project_id` = ?
			      AND t1.`type` = ?
			      AND t2.`ancestor` = ?
			      AND t1.`deleted` = ?
			      AND t2.`deleted` = ?
			    ORDER BY t2.`depth` DESC, t2.`index` ASC, t1.`created_at` ASC
			) AS t1 ON t.`category_id` = t1.`category_id`
		WHERE t.`project_id` = ?
		  AND t.`latest` = ?
		  AND t.`deleted` = ?
		  AND t1.`category_id` IS NOT NULL
	*/

	var (
		m     = l.svcCtx.ComponentGroupModel
		alias = "t"

		projectId  = req.GetProjectId()
		categoryId = req.GetCategoryId()
	)

	sb = squirrel.Select(utils.AddTableNameToFields(alias, m.Fields())...).
		From(fmt.Sprintf("%s AS %s", m.Table(), alias)).
		Where(
			fmt.Sprintf("%s.`project_id` = ? AND %s.`latest` = ? AND %s.`deleted` = ?", alias, alias, alias), projectId,
			qetconstants.IsLatestVersion, qetconstants.NotDeleted,
		)
	scb = squirrel.Select("COUNT(*)").
		From(fmt.Sprintf("%s AS %s", m.Table(), alias)).
		Where(
			fmt.Sprintf("%s.`project_id` = ? AND %s.`latest` = ? AND %s.`deleted` = ?", alias, alias, alias), projectId,
			qetconstants.IsLatestVersion, qetconstants.NotDeleted,
		)

	if req.DrillDown {
		sub := l.svcCtx.CategoryModel.FindDescendantCategoriesSqlBuilder(
			model.GetCategoryTreeCondition{
				ProjectId:     projectId,
				Type:          common.ConstCategoryTreeTypeComponentGroup,
				CategoryId:    categoryId,
				OnlyDirectory: true,
			},
		)

		sb = sb.JoinClause(
			sub.Prefix("LEFT JOIN (").Suffix(
				fmt.Sprintf(
					") AS t1 ON %s.`category_id` = t1.`category_id`", alias,
				),
			),
		).
			Where("t1.`category_id` IS NOT NULL")
		scb = scb.JoinClause(
			sub.Prefix("LEFT JOIN (").Suffix(
				fmt.Sprintf(
					") AS t1 ON %s.`category_id` = t1.`category_id`", alias,
				),
			),
		).
			Where("t1.`category_id` IS NOT NULL")
	} else {
		sb = sb.Where(fmt.Sprintf("%s.`category_id` = ?", alias), categoryId)
		scb = scb.Where(fmt.Sprintf("%s.`category_id` = ?", alias), categoryId)
	}

	sb = sqlbuilder.SearchOptionsWithAlias(
		sb, alias, sqlbuilder.WithCondition(m, req.GetCondition()), sqlbuilder.WithPagination(m, req.GetPagination()),
		sqlbuilder.WithSort(m, req.GetSort()),
	)
	scb = sqlbuilder.SearchOptionsWithAlias(scb, alias, sqlbuilder.WithCondition(m, req.GetCondition()))

	return sb, scb
}

func (l *BaseLogic) getElements(projectId, componentGroupId, version string) (
	nodes []*pb.Node, edges []*pb.Edge, combos []*pb.Combo, err error,
) {
	m := l.svcCtx.ComponentGroupElementModel

	err = mr.MapReduceVoid[any, pb.Element](
		func(source chan<- any) {
			elements, err := m.FindNoCacheByQuery(
				l.ctx, m.SelectBuilder().Where(
					squirrel.Eq{
						"`project_id`":         projectId,
						"`component_group_id`": componentGroupId,
						"`version`":            version,
					},
				),
			)

			if err != nil {
				source <- errors.Wrapf(
					errorx.Err(errorx.DBError, err.Error()),
					"failed to find elements of component group with project_id[%s], component_group_id[%s] and version[%s], error: %+v",
					projectId, componentGroupId, version, err,
				)
			} else {
				// avoid returning null to the front end
				l := len(elements)
				nodes = make([]*pb.Node, 0, l)
				edges = make([]*pb.Edge, 0, l)
				combos = make([]*pb.Combo, 0, l)

				for _, element := range elements {
					source <- element
				}
			}
		}, func(item any, writer mr.Writer[pb.Element], cancel func(error)) {
			var err error
			defer func() {
				if err != nil {
					cancel(err)
				}
			}()

			switch v := item.(type) {
			case error:
				err = v
			case *model.ComponentGroupElement:
				var e pb.Element
				switch v.ElementType {
				case constants.NODE:
					e = &pb.Node{}
				case constants.EDGE:
					e = &pb.Edge{}
				case constants.COMBO:
					e = &pb.Combo{}
				default:
					logx.Warnf("the type[%s] of element[%+v] is not currently supported", v.ElementType, v)
				}

				if e != nil {
					if err = protobuf.UnmarshalJSONFromString(v.Data, e); err != nil {
						err = errors.Wrapf(
							errorx.Err(errorx.SerializationError, err.Error()),
							"failed to unmarshal element data[%s], error: %+v", v.Data, err,
						)
					} else {
						writer.Write(e)
					}
				}
			default:
				logx.Warnf("the type[%T] of item is not currently supported", v)
			}
		}, func(pipe <-chan pb.Element, cancel func(error)) {
			for item := range pipe {
				switch v := item.(type) {
				case *pb.Node:
					nodes = append(nodes, v)
				case *pb.Edge:
					edges = append(edges, v)
				case *pb.Combo:
					combos = append(combos, v)
				default:
					logx.Warnf("the type[%T] of item is not currently supported", v)
				}
			}
		}, mr.WithContext(l.ctx),
	)

	return nodes, edges, combos, err
}
