package componentgroupservicelogic

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type ViewComponentGroupLogic struct {
	*BaseLogic
}

func NewViewComponentGroupLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ViewComponentGroupLogic {
	return &ViewComponentGroupLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// ViewComponentGroup 查看组件组
func (l *ViewComponentGroupLogic) ViewComponentGroup(in *pb.ViewComponentGroupReq) (
	resp *pb.ViewComponentGroupResp, err error,
) {
	// validate the project_id in req
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, in.GetProjectId()); err != nil {
		return nil, err
	}

	// validate the component_group_id in req
	componentGroup, err := model.CheckComponentGroupByComponentGroupId(
		l.ctx, l.svcCtx.ComponentGroupModel, in.GetProjectId(), in.GetComponentGroupId(), in.GetVersion(),
	)
	if err != nil {
		return nil, err
	}

	resp = &pb.ViewComponentGroupResp{
		ComponentGroup: &pb.ComponentGroup{
			Nodes:  []*pb.Node{},
			Edges:  []*pb.Edge{},
			Combos: []*pb.Combo{},
		},
		ReferencedRelations: []*pb.Referenced{},
	}
	if err = utils.Copy(resp.ComponentGroup, componentGroup, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy component group[%+v] to response, error: %+v", componentGroup, err,
		)
	}

	// just get basic info of component group
	if in.OnlyInfo {
		return resp, nil
	}

	resp.ComponentGroup.Nodes, resp.ComponentGroup.Edges, resp.ComponentGroup.Combos, err = l.getElements(
		in.GetProjectId(), in.GetComponentGroupId(), componentGroup.Version,
	)
	if err != nil {
		return nil, err
	}

	rrs, err := l.svcCtx.ComponentGroupReferenceModel.FindLatestReferenceByComponentGroupId(
		l.ctx, l.svcCtx.ComponentGroupModel, l.svcCtx.ApiCaseModel, l.svcCtx.InterfaceCaseModel, in.GetProjectId(),
		in.GetComponentGroupId(),
	)
	if err != nil && err != model.ErrNotFound {
		return nil, errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()),
			"failed to find the latest reference relationship with project_id[%s] and component_group_id[%s], error: %+v",
			in.GetProjectId(), in.GetComponentGroupId(), err,
		)
	}

	resp.ReferencedRelations = make([]*pb.Referenced, len(rrs))
	for i, rs := range rrs {
		resp.ReferencedRelations[i] = &pb.Referenced{
			ProjectId:        rs.ProjectId,
			ReferenceId:      rs.ReferenceId,
			ReferenceType:    rs.ReferenceType,
			ReferenceVersion: rs.ReferenceVersion,
		}
	}

	return resp, nil
}
