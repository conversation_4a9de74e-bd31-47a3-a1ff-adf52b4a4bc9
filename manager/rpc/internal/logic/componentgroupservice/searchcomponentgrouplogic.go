package componentgroupservicelogic

import (
	"context"
	"math"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type SearchComponentGroupLogic struct {
	*BaseLogic
}

func NewSearchComponentGroupLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SearchComponentGroupLogic {
	return &SearchComponentGroupLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// SearchComponentGroup 搜索组件组
func (l *SearchComponentGroupLogic) SearchComponentGroup(in *pb.SearchComponentGroupReq) (resp *pb.SearchComponentGroupResp, err error) {
	resp = &pb.SearchComponentGroupResp{}

	// validate the project_id in req
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, in.GetProjectId()); err != nil {
		return nil, err
	}

	// validate the category_id in req
	if _, err = model.CheckCategoryByCategoryId(l.ctx, l.svcCtx.CategoryModel, in.GetProjectId(), common.ConstCategoryTreeTypeComponentGroup, in.GetCategoryId()); err != nil {
		return nil, err
	}

	selectBuilder, countBuilder := l.generateSearchSqlBuilder(searchComponentGroupInternalReq{
		SearchComponentGroupReq: in,
		DrillDown:               true,
	})

	count, err := l.svcCtx.ComponentGroupModel.FindCount(l.ctx, countBuilder)
	if err != nil {
		return nil, errors.Wrapf(errorx.Err(errorx.DBError, err.Error()), "failed to count component group with project_id[%s] and category_id[%s], error: %+v", in.GetProjectId(), in.GetCategoryId(), err)
	}
	resp.TotalCount = uint64(count)
	resp.TotalPage = 1

	componentGroups, err := l.svcCtx.ComponentGroupModel.FindNoCacheByQuery(l.ctx, selectBuilder)
	if err != nil {
		return nil, errors.Wrapf(errorx.Err(errorx.DBError, err.Error()), "failed to find component group with project_id[%s] and category_id[%s], error: %+v", in.GetProjectId(), in.GetCategoryId(), err)
	}

	resp.Items = make([]*pb.ComponentGroup, 0, len(componentGroups))
	for _, componentGroup := range componentGroups {
		item := &pb.ComponentGroup{}
		if err = utils.Copy(item, componentGroup, l.converters...); err != nil {
			return nil, errors.Wrapf(errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy component group[%+v] to response, error: %+v", componentGroup, err)
		}

		resp.Items = append(resp.Items, item)
	}

	pagination := in.GetPagination()
	if pagination != nil {
		resp.CurrentPage = pagination.GetCurrentPage()
		resp.PageSize = pagination.GetPageSize()
		resp.TotalPage = uint64(math.Ceil(float64(resp.TotalCount) / float64(resp.PageSize)))
	}

	return resp, nil
}
