package componentgroupservicelogic

import (
	"context"
	"math"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sqlbuilder/rpc"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type SearchComponentGroupReferenceLogic struct {
	*BaseLogic
}

func NewSearchComponentGroupReferenceLogic(
	ctx context.Context, svcCtx *svc.ServiceContext,
) *SearchComponentGroupReferenceLogic {
	return &SearchComponentGroupReferenceLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// SearchComponentGroupReference 搜索组件组引用详情
func (l *SearchComponentGroupReferenceLogic) SearchComponentGroupReference(in *pb.SearchComponentGroupReferenceReq) (
	resp *pb.SearchComponentGroupReferenceResp, err error,
) {
	resp = &pb.SearchComponentGroupReferenceResp{}

	// validate the project_id in req
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, in.GetProjectId()); err != nil {
		return nil, err
	}

	// validate the component_group_id in req
	if _, err = model.CheckComponentGroupByComponentGroupId(
		l.ctx, l.svcCtx.ComponentGroupModel, in.GetProjectId(), in.GetComponentGroupId(), "",
	); err != nil {
		return nil, err
	}

	selectBuilder, countBuilder := l.svcCtx.ComponentGroupModel.GenerateSearchComponentGroupReferenceSqlBuilder(
		model.SearchComponentGroupReferenceReq{
			ProjectId:        in.GetProjectId(),
			ComponentGroupId: in.GetComponentGroupId(),
			Condition:        in.GetCondition(),
			Pagination:       in.GetPagination(),
			Sort:             rpc.ConvertSortFields(in.GetSort()),
		},
	)

	count, err := l.svcCtx.ComponentGroupModel.FindCountComponentGroupReference(l.ctx, countBuilder)
	if err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()),
			"failed to count reference data of component group with project_id[%s] and component_group_id[%s], error: %+v",
			in.GetProjectId(), in.GetComponentGroupId(), err,
		)
	}
	resp.TotalCount = uint64(count)
	resp.TotalPage = 1

	componentGroups, err := l.svcCtx.ComponentGroupModel.FindComponentGroupReference(l.ctx, selectBuilder)
	if err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()),
			"failed to find reference data of component group with project_id[%s] and component_group_id[%s], error: %+v",
			in.GetProjectId(), in.GetComponentGroupId(), err,
		)
	}

	resp.Items = make([]*pb.SearchComponentGroupReferenceItem, 0, len(componentGroups))
	for _, componentGroup := range componentGroups {
		item := &pb.SearchComponentGroupReferenceItem{
			State: logic.ConvertStringToCommonState(componentGroup.State),
		}
		if err = utils.Copy(item, componentGroup, l.converters...); err != nil {
			return nil, errors.Wrapf(
				errorx.Err(errorx.CopyToStructFailure, err.Error()),
				"failed to copy reference data of component group[%+v] to response, error: %+v", componentGroup, err,
			)
		}

		resp.Items = append(resp.Items, item)
	}

	pagination := in.GetPagination()
	if pagination != nil {
		resp.CurrentPage = pagination.GetCurrentPage()
		resp.PageSize = pagination.GetPageSize()
		resp.TotalPage = uint64(math.Ceil(float64(resp.TotalCount) / float64(resp.PageSize)))
	}

	return resp, nil
}
