package componentgroupservicelogic

import (
	"context"
	"database/sql"
	"fmt"
	"sort"

	"github.com/hashicorp/go-multierror"
	"github.com/pkg/errors"
	"github.com/sergi/go-diff/diffmatchpatch"
	"github.com/zeromicro/go-zero/core/jsonx"
	"github.com/zeromicro/go-zero/core/mr"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	qetconstants "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/redislock"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type ModifyComponentGroupLogic struct {
	*BaseLogic
}

func NewModifyComponentGroupLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ModifyComponentGroupLogic {
	return &ModifyComponentGroupLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *ModifyComponentGroupLogic) doValidateAndGetOrigin(in *pb.ModifyComponentGroupReq) (
	*model.ComponentGroup, error,
) {
	var err error

	// validate the project_id in req
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, in.GetProjectId()); err != nil {
		return nil, err
	}

	var c *model.Category
	// validate the category_id in req
	if c, err = model.CheckCategoryByCategoryId(
		l.ctx, l.svcCtx.CategoryModel, in.GetProjectId(), common.ConstCategoryTreeTypeComponentGroup,
		in.GetCategoryId(),
	); err != nil {
		return nil, err
	} else if c.CategoryType != common.ConstCategoryTypeDirectory {
		return nil, errors.WithStack(
			errorx.Err(
				errorx.ProhibitedBehavior,
				fmt.Sprintf(
					"the type of parent category[%s] does not support creation of sub category", c.CategoryType,
				),
			),
		)
	}

	// validate the component_group_id in req
	origin, err := model.CheckComponentGroupByComponentGroupId(
		l.ctx, l.svcCtx.ComponentGroupModel, in.GetProjectId(), in.GetComponentGroupId(), "",
	)
	if err != nil {
		return nil, err
	}

	return origin, err
}

// ModifyComponentGroup 编辑组件组
func (l *ModifyComponentGroupLogic) ModifyComponentGroup(in *pb.ModifyComponentGroupReq) (
	resp *pb.ModifyComponentGroupResp, err error,
) {
	origin, err := l.doValidateAndGetOrigin(in)
	if err != nil {
		return nil, err
	}

	// get the latest
	rrs, err := l.svcCtx.ComponentGroupReferenceModel.FindLatestReferenceByComponentGroupId(
		l.ctx, l.svcCtx.ComponentGroupModel, l.svcCtx.ApiCaseModel, l.svcCtx.InterfaceCaseModel, in.GetProjectId(),
		in.GetComponentGroupId(),
	)
	if err != nil && !errors.Is(err, model.ErrNotFound) {
		return nil, errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()),
			"failed to find the latest reference relationship with project_id[%s] and component_group_id[%s], error: %+v",
			in.GetProjectId(), in.GetComponentGroupId(), err,
		)
	}

	// validate the component group data has been changed
	change, err := l.hasChange(in, *origin, len(rrs) > 0)
	if err != nil {
		return nil, err
	} else if !change.Whole {
		l.Logger.Infof(
			"there is no change with component group, project_id: %s, component_group_id: %s",
			in.GetProjectId(), in.GetComponentGroupId(),
		)

		resp = &pb.ModifyComponentGroupResp{ComponentGroup: &pb.ComponentGroup{}}
		if err = utils.Copy(resp.ComponentGroup, origin, l.converters...); err != nil {
			return nil, errors.Wrapf(
				errorx.Err(errorx.CopyToStructFailure, err.Error()),
				"failed to copy component group[%+v] to response, error: %+v", origin, err,
			)
		}
		return resp, nil
	}

	// cannot change the component group type
	if origin.ComponentGroupType != in.GetComponentGroupType() {
		return nil, errors.WithStack(
			errorx.Err(
				errorx.ProhibitedBehavior, fmt.Sprintf(
					"the component_group_type[%s] is different from the original value[%s]", in.GetComponentGroupType(),
					origin.ComponentGroupType,
				),
			),
		)
	}

	// cannot change to the builtin names
	if origin.Name != in.GetName() && stringx.Contains(common.ComponentGroupBuiltinCategoryNames, in.GetName()) {
		return nil, errors.WithStack(
			errorx.Err(
				errorx.ProhibitedBehavior, fmt.Sprintf(
					"the name[%s] conflicts with the builtin name of component group category tree and is not allowed to be used",
					in.GetName(),
				),
			),
		)
	}

	// validate the circular reference exists
	if err = logic.CheckCircularReference(
		l.ctx, l.svcCtx.ComponentGroupModel, in.GetProjectId(), in.GetRelations(),
	); err != nil {
		return nil, err
	}

	// acquire redis lock
	key := fmt.Sprintf(
		"%s:%s:%s", common.ConstLockComponentGroupProjectIdComponentGroupIdPrefix, in.GetProjectId(),
		in.GetComponentGroupId(),
	)
	lock, err := redislock.NewRedisLockAndAcquire(l.svcCtx.Redis, key, redislock.WithExpire(common.ConstLockExpireTime))
	if err != nil {
		return nil, err
	}
	defer func() {
		// release redis lock
		e := lock.Release()
		if e != nil {
			l.Logger.Error(e)
			if re, ok := errorx.RootError(e); ok && re.Code() != errorx.ReleaseRedisLockFailure && err == nil {
				err = e
			}
		}
	}()

	componentGroup, err := l.modify(in)
	if err != nil {
		return nil, err
	}

	if change.Name || change.Description || change.Imports || change.Exports {
		// 入参或出参有变化，则需要更新引用当前组件组的用例或组件组
		err = l.updateReference(in, rrs, change)
		if err != nil {
			// 更新引用失败暂时不让用户感知，只把错误信息打印到日志
			l.Logger.Warnf(
				"failed to update reference, project_id: %s, component_group_id: %s, error: \n%+v", in.GetProjectId(),
				in.GetComponentGroupId(), err,
			)
		}
	}

	resp = &pb.ModifyComponentGroupResp{ComponentGroup: &pb.ComponentGroup{}}
	if err = utils.Copy(resp.ComponentGroup, componentGroup, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy component group[%+v] to response, error: %+v", componentGroup, err,
		)
	}

	return resp, nil
}

func (l *ModifyComponentGroupLogic) hasChange(
	req *pb.ModifyComponentGroupReq, cg model.ComponentGroup, hasReferenced bool,
) (types.ChangeFlag, error) {
	ch := types.ChangeFlag{
		Name:        cg.Name != req.GetName(),
		Description: cg.Description.String != req.GetDescription(),
	}

	nodes, edges, combos, err := l.getElements(cg.ProjectId, cg.ComponentGroupId, cg.Version)
	if err != nil {
		return ch, err
	}

	var tags []string
	if cg.Tags.Valid && cg.Tags.String != "" {
		// 由于`protojson`只能反序列化到`[]proto.Message`，而`tags`字段类型为`[]string`，Go Struct跟PB没有大的区别，因此直接使用`jsonx`暂代`protojson`
		if err = jsonx.UnmarshalFromString(cg.Tags.String, &tags); err != nil {
			return ch, errors.Wrapf(
				errorx.Err(errorx.SerializationError, err.Error()),
				"failed to unmarshal component group tags[%s], error: %+v", cg.Tags.String, err,
			)
		}
	}

	var imports []*pb.Import
	if err = protobuf.UnmarshalJSONWithMessagesFromString(cg.Imports, &imports); err != nil {
		return ch, errors.Wrapf(
			errorx.Err(errorx.SerializationError, err.Error()),
			"failed to unmarshal component group imports[%s], error: %+v", cg.Imports, err,
		)
	}

	var exports []*pb.Export
	if err = protobuf.UnmarshalJSONWithMessagesFromString(cg.Exports, &exports); err != nil {
		return ch, errors.Wrapf(
			errorx.Err(errorx.SerializationError, err.Error()),
			"failed to unmarshal component group exports[%s], error: %+v", cg.Exports, err,
		)
	}

	var accountConfig pb.AccountConfig
	if err = protobuf.UnmarshalJSONFromString(cg.AccountConfig, &accountConfig); err != nil {
		return ch, errors.Wrapf(
			errorx.Err(errorx.SerializationError, err.Error()),
			"failed to unmarshal component group account config[%s], error: %+v", cg.AccountConfig, err,
		)
	}

	// `pb.Relation`中含有二维数组，Go Struct跟PB有区别，因此这里必须使用兼容函数进行反序列化
	var relations []*pb.Relation
	err = protobuf.UnmarshalJSONWithMessagesFromString(cg.Structure, &relations)
	if err != nil {
		return ch, errors.Wrapf(
			errorx.Err(errorx.SerializationError, err.Error()),
			"failed to unmarshal component group structure[%s], error: %+v", cg.Structure, err,
		)
	}

	var referenceRelations pb.Relation
	if err = protobuf.UnmarshalJSONFromString(cg.ReferenceStructure, &referenceRelations); err != nil {
		return ch, errors.Wrapf(
			errorx.Err(errorx.SerializationError, err.Error()),
			"failed to unmarshal component group reference structure[%s], error: %+v", cg.ReferenceStructure, err,
		)
	}

	sort.SliceStable(
		nodes, func(i, j int) bool {
			return nodes[i].Id < nodes[j].Id
		},
	)
	sort.SliceStable(
		edges, func(i, j int) bool {
			return edges[i].Id < edges[j].Id
		},
	)
	sort.SliceStable(
		combos, func(i, j int) bool {
			return combos[i].Id < combos[j].Id
		},
	)

	origin := &pb.ModifyComponentGroupReq{
		ProjectId:          cg.ProjectId,
		CategoryId:         cg.CategoryId,
		ComponentGroupId:   cg.ComponentGroupId,
		ComponentGroupType: cg.ComponentGroupType,
		Name:               cg.Name,
		Description:        cg.Description.String,
		Priority:           cg.Priority,
		Tags:               tags,
		Imports:            imports,
		Exports:            exports,
		AccountConfig:      &accountConfig,
		Nodes:              nodes,
		Edges:              edges,
		Combos:             combos,
		Relations:          relations,
		ReferenceRelations: &referenceRelations,
	}

	if ns := req.GetNodes(); ns != nil {
		sort.SliceStable(
			ns, func(i, j int) bool {
				return ns[i].Id < ns[j].Id
			},
		)
	}

	if es := req.GetEdges(); es != nil {
		sort.SliceStable(
			es, func(i, j int) bool {
				return es[i].Id < es[j].Id
			},
		)
	}

	if cs := req.GetCombos(); cs != nil {
		sort.SliceStable(
			cs, func(i, j int) bool {
				return cs[i].Id < cs[j].Id
			},
		)
	}

	dmp := diffmatchpatch.New()
	ds := dmp.DiffMain(jsonx.MarshalToStringIgnoreError(origin), jsonx.MarshalToStringIgnoreError(req), false)
	if len(ds) != 0 {
		for _, d := range ds {
			if d.Type != diffmatchpatch.DiffEqual {
				ch.Whole = true

				ch.Imports, err = diffArguments(imports, req.GetImports(), hasReferenced)
				if err != nil {
					return ch, err
				}
				ch.Exports, err = diffArguments(exports, req.GetExports(), hasReferenced)
				if err != nil {
					return ch, err
				}

				return ch, nil
			}
		}
	}

	return ch, nil
}

// in order to reduce cyclomatic complexity of ModifyComponentGroupLogic.ModifyComponentGroup
func (l *ModifyComponentGroupLogic) modify(req *pb.ModifyComponentGroupReq) (*model.ComponentGroup, error) {
	// get the latest version of api case again
	// 注意：这里需要重新获取最新版本的数据，因为当高并发的时候，上锁前拿到的可能已经不是最新版本的数据了
	origin, err := model.CheckComponentGroupByComponentGroupId(
		l.ctx, l.svcCtx.ComponentGroupModel, req.GetProjectId(), req.GetComponentGroupId(), "",
	)
	if err != nil {
		return nil, err
	}

	version, err := l.generateVersion(origin.ProjectId, origin.ComponentGroupId)
	if err != nil {
		return nil, err
	}

	// 由于`protojson`只能序列化`[]proto.Message`，而`tags`字段类型为`[]string`，Go Struct跟PB没有大的区别，因此直接使用`jsonx`暂代`protojson`
	tags := jsonx.MarshalToStringIgnoreError(req.GetTags())

	componentGroup := &model.ComponentGroup{
		// Id:                 origin.Id, // 由于组件组有版本概念，`componentGroup`对象用于创建，这里不能沿用`origin.Id`
		ProjectId:          origin.ProjectId,
		CategoryId:         req.GetCategoryId(),
		ComponentGroupId:   origin.ComponentGroupId,
		ComponentGroupType: origin.ComponentGroupType,
		Name:               req.GetName(),
		Description: sql.NullString{
			String: req.GetDescription(),
			Valid:  req.GetDescription() != "",
		},
		Priority: req.GetPriority(),
		Tags: sql.NullString{
			String: tags,
			Valid:  tags != "",
		},
		Imports:            jsonx.MarshalToStringIgnoreError(req.GetImports()),
		Exports:            jsonx.MarshalToStringIgnoreError(req.GetExports()),
		AccountConfig:      protobuf.MarshalJSONToStringIgnoreError(req.GetAccountConfig()),
		Version:            version,
		Structure:          protobuf.MarshalJSONWithMessagesToStringIgnoreError(req.GetRelations()),
		ReferenceStructure: protobuf.MarshalJSONToStringIgnoreError(req.GetReferenceRelations()),
		Latest:             int64(qetconstants.IsLatestVersion),
		MaintainedBy: sql.NullString{
			String: req.GetMaintainedBy(),
			Valid:  req.GetMaintainedBy() != "",
		},
		CreatedBy: origin.CreatedBy,
		UpdatedBy: l.currentUser.Account,
		CreatedAt: origin.CreatedAt,
	}

	// update component group in a transaction
	if err = l.svcCtx.ComponentGroupModel.Trans(
		l.ctx, func(context context.Context, session sqlx.Session) error {
			origin.Latest = int64(qetconstants.IsNotLatestVersion)
			//if _, err := l.svcCtx.ComponentGroupModel.UpdateTX(context, session, origin); err != nil {
			//	return errors.Wrapf(errorx.Err(errorx.DBError, err.Error()), "failed to update table[%s] with values[%+v], error: %+v", l.svcCtx.ComponentGroupModel.Table(), origin, err)
			//}
			if _, err := l.svcCtx.ComponentGroupModel.UpdateAllToNotLatest(
				context, session, origin.ProjectId, origin.ComponentGroupId,
			); err != nil {
				return errors.Wrapf(
					errorx.Err(errorx.DBError, err.Error()),
					"failed to update the latest field of component group with project_id[%s] and component_group_id[%s], error: %+v",
					origin.ProjectId, origin.ComponentGroupId, err,
				)
			}

			if _, err := l.svcCtx.ComponentGroupModel.InsertTX(context, session, componentGroup); err != nil {
				return errors.Wrapf(
					errorx.Err(errorx.DBError, err.Error()), "failed to insert table[%s] with values[%+v], error: %+v",
					l.svcCtx.ComponentGroupModel.Table(), componentGroup, err,
				)
			}

			componentGroupCategory, err := l.svcCtx.CategoryModel.FindOneByNodeId(
				context, origin.ProjectId, common.ConstCategoryTreeTypeComponentGroup, origin.ComponentGroupId,
			)
			if err != nil {
				return errors.Wrapf(
					errorx.Err(errorx.DBError, err.Error()),
					"failed to find category with project_id[%s], type[%s] and component_group_id[%s], error: %+v",
					origin.ProjectId, common.ConstCategoryTreeTypeComponentGroup, origin.ComponentGroupId, err,
				)
			}

			if componentGroup.Name != origin.Name || componentGroup.Description != origin.Description {
				// modify the component group category
				if _, err := l.modifyCategoryLogic.ModifyCategoryForInternal(
					context, session, &pb.ModifyCategoryReq{
						ProjectId:   componentGroup.ProjectId,
						Type:        common.ConstCategoryTreeTypeComponentGroup,
						CategoryId:  componentGroupCategory.CategoryId,
						Name:        componentGroup.Name,
						Description: componentGroup.Description.String,
					},
				); err != nil {
					return err
				}
			}

			if origin.CategoryId != req.GetCategoryId() {
				// move the component group category from c.CategoryId to req.CategoryId
				if err = l.moveCategoryLogic.MoveCategoryTreeForInternal(
					context, session, &pb.MoveCategoryTreeReq{
						ProjectId:     componentGroup.ProjectId,
						Type:          common.ConstCategoryTreeTypeComponentGroup,
						SourceId:      componentGroupCategory.CategoryId,
						TargetId:      req.GetCategoryId(),
						BeingModified: true,
					},
				); err != nil {
					return err
				}
			}

			// create component group reference relationship
			if err := l.createReferenceRelationship(
				context, session, createReferenceRelationshipInternalReq{
					ProjectId:        componentGroup.ProjectId,
					ComponentGroupId: componentGroup.ComponentGroupId,
					Version:          componentGroup.Version,
					Relations:        req.GetRelations(),
				},
			); err != nil {
				return err
			}

			// create the elements and components of the component group
			if err := l.createElementAndComponent(
				context, session, createElementAndComponentInternalReq{
					ProjectId:        componentGroup.ProjectId,
					ComponentGroupId: componentGroup.ComponentGroupId,
					Version:          componentGroup.Version,
					Nodes:            req.GetNodes(),
					Edges:            req.GetEdges(),
					Combos:           req.GetCombos(),
				},
			); err != nil {
				return err
			}

			// create the new tag and tag reference of component group
			if err := l.createTagLogic.CreateTagAndReferenceForInternal(
				context, session, types.CreateOrUpdateTagReference{
					ProjectId:        componentGroup.ProjectId,
					ReferenceType:    common.ConstReferenceTypeComponentGroup,
					ReferenceId:      componentGroup.ComponentGroupId,
					ReferenceVersion: version,
					Tags:             req.GetTags(),
				},
			); err != nil {
				return err
			}

			return nil
		},
	); err != nil {
		return nil, err
	}

	return componentGroup, nil
}

func (l *ModifyComponentGroupLogic) updateReference(
	req *pb.ModifyComponentGroupReq, rrs []*model.ComponentGroupReferenceRelationship, ch types.ChangeFlag,
) (err error) {
	mr.ForEach(
		func(source chan<- any) {
			for _, rs := range rrs {
				source <- rs
			}
		}, func(item any) {
			r, ok := item.(*model.ComponentGroupReferenceRelationship)
			if !ok {
				return
			}

			var e error
			ur := types.UpdateReference{
				ProjectId:        req.GetProjectId(),
				ReferenceType:    r.ReferenceType,
				ReferenceId:      r.ReferenceId,
				ReferenceVersion: r.ReferenceVersion,
				ComponentGroupId: r.ComponentGroupId,
				Name:             req.GetName(),
				Description:      req.GetDescription(),
				Imports:          req.GetImports(),
				Exports:          req.GetExports(),
			}
			if r.ReferenceType == constants.ComponentGroup {
				e = l.UpdateReferenceByComponentGroup(ur, ch)
			} else if r.ReferenceType == constants.ApiCase {
				e = l.modifyApiCaseLogic.UpdateReferenceByApiCase(ur, ch)
			} else if r.ReferenceType == constants.InterfaceCase {
				e = l.modifyInterfaceCaseLogic.UpdateReferenceByInterfaceCase(ur, ch)
			}

			if e != nil {
				err = multierror.Append(err, e)
			}
		},
	)

	return err
}

func (l *ModifyComponentGroupLogic) UpdateReferenceByComponentGroup(
	ur types.UpdateReference, ch types.ChangeFlag,
) (err error) {
	// acquire redis lock
	key := fmt.Sprintf(
		"%s:%s:%s", common.ConstLockComponentGroupProjectIdComponentGroupIdPrefix, ur.ProjectId, ur.ReferenceId,
	)
	lock, err := redislock.NewRedisLockAndAcquire(
		l.svcCtx.Redis, key, redislock.WithExpire(common.ConstLockExpireTime),
		redislock.WithTimeout(common.ConstAcquireLockTimeout),
	)
	if err != nil {
		return err
	}
	defer func() {
		if err != nil {
			l.Logger.Warnf(
				"error occurred while updating the reference, project_id: %s, reference_type: %s, reference_id: %s, reference_version: %s, component_group_id: %s, error: \n%+v",
				ur.ProjectId, ur.ReferenceType, ur.ReferenceId, ur.ReferenceVersion, ur.ComponentGroupId, err,
			)
		}
		// release redis lock
		e := lock.Release()
		if e != nil {
			l.Logger.Warn(e)
			if re, ok := errorx.RootError(e); ok && re.Code() != errorx.ReleaseRedisLockFailure && err == nil {
				err = e
			}
		}
	}()

	// `ComponentGroupReferenceModel.FindLatestReferenceByComponentGroupId`返回的就是最新版本的，所以这里不再判断了
	//cg, err := l.svcCtx.ComponentGroupModel.FindOneByProjectIdComponentGroupIdVersion(l.ctx, ur.ProjectId, ur.ReferenceId, ur.ReferenceVersion)
	//if err != nil {
	//	if err != model.ErrNotFound {
	//		return errors.Wrapf(errorx.Err(errorx.DBError, err.Error()), "failed to find component group, project_id: %s, component_group_id: %s, version: %s, error: %+v", ur.ProjectId, ur.ReferenceId, ur.ReferenceVersion, err)
	//	} else {
	//		return errors.WithStack(errorx.Err(errorx.NotExists, fmt.Sprintf("component group doesn't exist, project_id: %s, component_group_id: %s, version: %s", ur.ProjectId, ur.ReferenceId, ur.ReferenceVersion)))
	//	}
	//} else if constants.LatestVersionFlag(cg.Latest) != constants.IsLatestVersion {
	//	// 引用当前被编辑的组件组的组件组不是最新版本，则无需更新
	//	return nil
	//}

	nodes, _, _, err := l.getElements(ur.ProjectId, ur.ReferenceId, ur.ReferenceVersion)
	if err != nil {
		return err
	}

	for _, node := range nodes {
		m := node.ComponentDataMapValue()

		if m == nil || node.ComponentType() != constants.REFERENCE {
			continue
		}

		v, ok := m[common.ConstFieldJSONNameReferenceId]
		if !ok || ur.ComponentGroupId != v {
			continue
		}

		cge, err := l.svcCtx.ComponentGroupElementModel.FindOneByProjectIdComponentGroupIdVersionElementId(
			l.ctx, ur.ProjectId, ur.ReferenceId, ur.ReferenceVersion, node.GetId(),
		)
		if err != nil {
			l.Logger.Warnf(
				"failed to find the component group element, project_id: %s, component_group_id: %s, version: %s, element_id: %s, error: %+v",
				ur.ProjectId, ur.ReferenceId, ur.ReferenceVersion, node.GetId(), err,
			)
			continue
		}

		if ch.Name {
			m[common.ConstFieldJSONNameName] = ur.Name
			m[common.ConstFieldJSONNameLabel] = ur.Name
			node.Label = ur.Name
		}
		if ch.Description {
			m[common.ConstFieldJSONNameDescription] = ur.Description
		}
		if ch.Imports {
			logic.UpdateReferenceImports(ur.Imports, m)
		}
		if ch.Exports {
			logic.UpdateReferenceExports(ur.Exports, m)
		}

		node.Data, err = protobuf.NewStruct(m)
		if err != nil {
			l.Logger.Warnf(
				"failed to new a *structpb.Struct with the data of component group element, project_id: %s, component_group_id: %s, version: %s, element_id: %s, error: %+v",
				ur.ProjectId, ur.ReferenceId, ur.ReferenceVersion, node.GetId(), err,
			)
			continue
		}

		cge.Data = jsonx.MarshalToStringIgnoreError(node)
		if _, err = l.svcCtx.ComponentGroupElementModel.UpdateTX(l.ctx, nil, cge); err != nil {
			l.Logger.Warnf(
				"failed to update the component group element, project_id: %s, component_group_id: %s, version: %s, element_id: %s, error: %+v",
				cge.ProjectId, cge.ComponentGroupId, cge.Version, cge.ElementId, err,
			)
			continue
		}
	}

	return nil
}
