package dataprocessingfunctionservicelogic

import (
	"context"

	"github.com/Masterminds/squirrel"
	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/logx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sqlbuilder"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/userinfo"
	commonutils "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/utils"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type BaseLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext

	currentUser *userinfo.UserInfo

	converters []commonutils.TypeConverter
}

func newBaseLogic(ctx context.Context, svcCtx *svc.ServiceContext) *BaseLogic {
	return &BaseLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,

		currentUser: userinfo.FromContext(ctx),

		converters: []commonutils.TypeConverter{
			logic.StringToFunctionType(),
			logic.StringToCodeLanguage(),
			logic.StringToParameters(),
			logic.StringToReturns(),
			logic.StringToParameterOrReturnType(),
		},
	}
}

func (l *BaseLogic) generateVersion(projectId, name, tp string) (string, error) {
	g := commonutils.NewUniqueIdGenerator(commonutils.WithGenerateFunc(utils.GenVersion), commonutils.WithIsUniqueFunc(func(id string) bool {
		r, err := l.svcCtx.FunctionModel.FindOneByProjectIdNameTypeVersion(l.ctx, projectId, name, tp, id)
		if err == model.ErrNotFound || r == nil {
			return true
		}
		return false
	}))
	version := g.Next()
	if version == "" {
		return "", errors.WithStack(errorx.Err(errorx.GenerateUniqueIdFailure, "failed to generate version id, please try it later"))
	}

	return version, nil
}

func (l *BaseLogic) generateSearchSqlBuilder(req *pb.SearchDataProcessingFunctionReq) (sb, scb squirrel.SelectBuilder) {
	m := l.svcCtx.FunctionModel
	or := squirrel.Or{squirrel.Eq{"`project_id`": req.GetProjectId()}, squirrel.Eq{"`project_id`": common.ConstBuiltinGlobalProjectId}}

	sb = m.SelectBuilder().Where(or).Where("`latest` = ?", constants.IsLatestVersion)
	scb = m.SelectCountBuilder().Where(or).Where("`latest` = ?", constants.IsLatestVersion)

	sb = sqlbuilder.SearchOptions(sb, sqlbuilder.WithCondition(m, req.GetCondition()), sqlbuilder.WithPagination(m, req.GetPagination()), sqlbuilder.WithSort(m, req.GetSort()))
	scb = sqlbuilder.SearchOptions(scb, sqlbuilder.WithCondition(m, req.GetCondition()))

	return
}
