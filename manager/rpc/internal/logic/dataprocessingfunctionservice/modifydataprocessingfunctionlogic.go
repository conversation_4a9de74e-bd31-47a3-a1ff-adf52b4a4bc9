package dataprocessingfunctionservicelogic

import (
	"context"
	"database/sql"
	"fmt"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/stores/sqlx"

	qetconstants "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/redislock"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type ModifyDataProcessingFunctionLogic struct {
	*BaseLogic
}

func NewModifyDataProcessingFunctionLogic(
	ctx context.Context, svcCtx *svc.ServiceContext,
) *ModifyDataProcessingFunctionLogic {
	return &ModifyDataProcessingFunctionLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// ModifyDataProcessingFunction 编辑数据处理函数
func (l *ModifyDataProcessingFunctionLogic) ModifyDataProcessingFunction(in *pb.CreateOrModifyDataProcessingFunctionReq) (
	resp *pb.CreateOrModifyDataProcessingFunctionResp, err error,
) {
	// validate the project_id in req
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, in.GetProjectId()); err != nil {
		return nil, err
	}

	origin, err := model.CheckFunctionByNameAndType(
		l.ctx, l.svcCtx.FunctionModel, in.GetProjectId(), in.GetName(), constants.CUSTOM,
	)
	if err != nil {
		return nil, err
	}

	// validate the custom function
	if err = ValidateCustomFunction(in); err != nil {
		return nil, err
	}

	var parameters []*pb.Parameter
	if err = protobuf.UnmarshalJSONWithMessagesFromString(origin.Parameters, &parameters); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.SerializationError, err.Error()),
			"failed to unmarshal data processing function parameters[%s], error: %+v", origin.Parameters, err,
		)
	}

	if err = ValidateModifyParameters(parameters, in.GetParameters()); err != nil {
		return nil, err
	}

	// acquire redis lock
	key := fmt.Sprintf(
		"%s:%s:%s:%s", common.ConstLockFunctionProjectIdNameTypePrefix, in.GetProjectId(), in.GetName(),
		constants.CUSTOM,
	)
	lock, err := redislock.NewRedisLockAndAcquire(l.svcCtx.Redis, key, redislock.WithExpire(common.ConstLockExpireTime))
	if err != nil {
		return nil, err
	}
	defer func() {
		// release redis lock
		e := lock.Release()
		if e != nil {
			// l.Logger.Error(e)
			if re, ok := errorx.RootError(e); ok && re.Code() != errorx.ReleaseRedisLockFailure && err == nil {
				err = e
			}
		}
	}()

	function, err := l.modify(in)
	if err != nil {
		return nil, err
	}

	resp = &pb.CreateOrModifyDataProcessingFunctionResp{Function: &pb.DataProcessingFunction{}}
	if err = utils.Copy(resp.Function, function, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy data processing function[%+v] to response, error: %+v", function, err,
		)
	}

	return resp, nil
}

// in order to reduce cyclomatic complexity of ModifyDataProcessingFunctionLogic.ModifyDataProcessingFunction
func (l *ModifyDataProcessingFunctionLogic) modify(req *pb.CreateOrModifyDataProcessingFunctionReq) (
	*model.Function, error,
) {
	// get the latest version of data processing function again
	// 注意：这里需要重新获取最新版本的数据，因为当高并发的时候，上锁前拿到的可能已经不是最新版本的数据了
	origin, err := model.CheckFunctionByNameAndType(
		l.ctx, l.svcCtx.FunctionModel, req.GetProjectId(), req.GetName(), constants.CUSTOM,
	)
	if err != nil {
		return nil, err
	}

	version, err := l.generateVersion(origin.ProjectId, origin.Name, origin.Type)
	if err != nil {
		return nil, err
	}

	function := &model.Function{
		ProjectId: origin.ProjectId,
		Name:      origin.Name,
		Type:      origin.Type,
		Category:  req.GetCategory(),
		Description: sql.NullString{
			String: req.GetDescription(),
			Valid:  req.GetDescription() != "",
		},
		Language: req.GetLanguage().String(),
		Content: sql.NullString{
			String: req.GetContent(),
			Valid:  req.GetContent() != "",
		},
		Parameters: protobuf.MarshalJSONWithMessagesToStringIgnoreError(req.GetParameters()),
		Returns:    protobuf.MarshalJSONWithMessagesToStringIgnoreError(req.GetReturns()),
		Example: sql.NullString{
			String: req.GetExample(),
			Valid:  req.GetExample() != "",
		},
		Version:   version,
		Latest:    int64(qetconstants.IsLatestVersion),
		CreatedBy: origin.CreatedBy,
		UpdatedBy: l.currentUser.Account,
		CreatedAt: origin.CreatedAt,
	}

	// update data processing function in a transaction
	if err = l.svcCtx.FunctionModel.Trans(
		l.ctx, func(context context.Context, session sqlx.Session) error {
			origin.Latest = int64(qetconstants.IsNotLatestVersion)
			if _, err := l.svcCtx.FunctionModel.UpdateTX(context, session, origin); err != nil {
				return errors.Wrapf(
					errorx.Err(errorx.DBError, err.Error()), "failed to update table[%s] with values[%+v], error: %+v",
					l.svcCtx.FunctionModel.Table(), origin, err,
				)
			}

			if _, err := l.svcCtx.FunctionModel.InsertTX(context, session, function); err != nil {
				return errors.Wrapf(
					errorx.Err(errorx.DBError, err.Error()), "failed to insert table[%s] with values[%+v], error: %+v",
					l.svcCtx.FunctionModel.Table(), function, err,
				)
			}

			return nil
		},
	); err != nil {
		return nil, err
	}

	return function, nil
}
