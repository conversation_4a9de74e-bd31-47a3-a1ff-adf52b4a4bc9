package dataprocessingfunctionservicelogic

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type ViewDataProcessingFunctionLogic struct {
	*BaseLogic
}

func NewViewDataProcessingFunctionLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ViewDataProcessingFunctionLogic {
	return &ViewDataProcessingFunctionLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// ViewDataProcessingFunction 查看数据处理函数
func (l *ViewDataProcessingFunctionLogic) ViewDataProcessingFunction(in *pb.ViewDataProcessingFunctionReq) (resp *pb.ViewDataProcessingFunctionResp, err error) {
	// validate the project_id in req
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, in.GetProjectId()); err != nil {
		return nil, err
	}

	projectId := in.GetProjectId()
	if in.GetType() == pb.FunctionType_BUILTIN {
		projectId = common.ConstBuiltinGlobalProjectId
	}
	function, err := model.CheckFunctionByNameAndType(l.ctx, l.svcCtx.FunctionModel, projectId, in.GetName(), in.GetType().String())
	if err != nil {
		return nil, err
	}

	resp = &pb.ViewDataProcessingFunctionResp{Function: &pb.DataProcessingFunction{}}
	if err = utils.Copy(resp.Function, function, l.converters...); err != nil {
		return nil, errors.Wrapf(errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy data processing function[%+v] to response, error: %+v", function, err)
	}

	// 注：这个逻辑放在`api`层做
	//if resp.Function.ProjectId == common.ConstBuiltinGlobalProjectId || resp.Function.Type == pb.FunctionType_BUILTIN {
	//	// desensitized builtin global project_id
	//	resp.Function.ProjectId = constants.SensitiveWorld
	//}

	return resp, nil
}
