package dataprocessingfunctionservicelogic

import (
	"context"
	"fmt"

	"github.com/hashicorp/go-multierror"
	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/mr"
	"github.com/zeromicro/go-zero/core/stores/sqlx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/redislock"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type RemoveDataProcessingFunctionLogic struct {
	*BaseLogic
}

func NewRemoveDataProcessingFunctionLogic(
	ctx context.Context, svcCtx *svc.ServiceContext,
) *RemoveDataProcessingFunctionLogic {
	return &RemoveDataProcessingFunctionLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// RemoveDataProcessingFunction 删除数据处理函数
func (l *RemoveDataProcessingFunctionLogic) RemoveDataProcessingFunction(in *pb.RemoveDataProcessingFunctionReq) (
	resp *pb.RemoveDataProcessingFunctionResp, err error,
) {
	// validate the project_id in req
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, in.GetProjectId()); err != nil {
		return nil, err
	}

	names := in.GetNames()
	workers := len(names)
	if workers > common.ConstMRMaxWorkers {
		workers = common.ConstMRMaxWorkers
	}

	mr.ForEach(
		func(source chan<- any) {
			for _, name := range names {
				source <- name
			}
		}, func(item any) {
			name, ok := item.(string)
			if !ok {
				err = multierror.Append(
					err, errorx.Err(
						errorx.InternalError,
						fmt.Sprintf("the data processing function name[%v (%T)] is not a string", item, item),
					),
				)
			} else {
				if e := l.remove(in.GetProjectId(), name); e != nil {
					err = multierror.Append(err, e)
				}
			}
		}, mr.WithContext(l.ctx), mr.WithWorkers(workers),
	)

	return &pb.RemoveDataProcessingFunctionResp{}, err
}

func (l *RemoveDataProcessingFunctionLogic) remove(projectId, name string) (err error) {
	// validate the function name in req
	function, err := model.CheckFunctionByNameAndType(l.ctx, l.svcCtx.FunctionModel, projectId, name, constants.CUSTOM)
	if err != nil {
		return err
	}

	// validate the reference relationship
	rrs, err := l.svcCtx.FunctionReferenceModel.FindLatestReferenceByNameAndType(
		l.ctx, l.svcCtx.ComponentGroupModel, l.svcCtx.ApiCaseModel, l.svcCtx.InterfaceCaseModel, projectId, name,
		constants.CUSTOM,
	)
	if err != nil && err != model.ErrNotFound {
		return errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()),
			"failed to find the reference relationship with project_id[%s] and name[%s], error: %+v", projectId, name,
			err,
		)
	} else if len(rrs) > 0 {
		return errors.WithStack(
			errorx.Err(
				errorx.ProhibitedBehavior, fmt.Sprintf(
					"cannot remove the data processing function[%s] which is referenced by %d component groups or api cases or interface cases",
					function.Name, len(rrs),
				),
			),
		)
	}

	// acquire redis lock
	key := fmt.Sprintf(
		"%s:%s:%s:%s", common.ConstLockFunctionProjectIdNameTypePrefix, projectId, name, constants.CUSTOM,
	)
	lock, err := redislock.NewRedisLockAndAcquire(l.svcCtx.Redis, key, redislock.WithExpire(common.ConstLockExpireTime))
	if err != nil {
		return err
	}
	defer func() {
		// release redis lock
		e := lock.Release()
		if e != nil {
			// l.Logger.Error(e)
			if re, ok := errorx.RootError(e); ok && re.Code() != errorx.ReleaseRedisLockFailure && err == nil {
				err = e
			}
		}
	}()

	return l.svcCtx.FunctionModel.Trans(
		l.ctx, func(context context.Context, session sqlx.Session) error {
			// Table: function
			if _, err := l.svcCtx.FunctionModel.RemoveByNameAndType(
				context, session, projectId, name, constants.CUSTOM,
			); err != nil {
				return errors.Wrapf(
					errorx.Err(errorx.DBError, err.Error()),
					"failed to remove data processing function with project_id[%s] and name[%s], error: %+v", projectId,
					name, err,
				)
			}
			return nil
		},
	)
}
