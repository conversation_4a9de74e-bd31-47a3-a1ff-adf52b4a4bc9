package dataprocessingfunctionservicelogic

import (
	"fmt"
	"reflect"

	"github.com/emirpasic/gods/sets/hashset"
	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

func ValidateCustomFunction(req *pb.CreateOrModifyDataProcessingFunctionReq) error {
	if req.Content == "" {
		return errors.WithStack(errorx.Err(errorx.ProhibitedBehavior, "cannot to create or modify the content of function to be empty"))
	}

	if len(req.Returns) != 1 {
		return errors.WithStack(errorx.Err(errorx.ProhibitedBehavior, "can only set one return value"))
	}

	var hasDefault bool
	for i, p := range req.Parameters {
		if hasDefault && (p.Default == "" || !p.Variadic) {
			return errors.WithStack(errorx.Err(errorx.ProhibitedBehavior, fmt.Sprintf("non-default parameter[%s] cannot follow default parameter", p.Name)))
		}
		if p.Default != "" {
			hasDefault = true
			if err := ValidateParameterWithDefaultValue(p); err != nil {
				return err
			}
		}
		if p.Variadic && (i+1 != len(req.Parameters)) {
			return errors.WithStack(errorx.Err(errorx.ProhibitedBehavior, fmt.Sprintf("variadic parameter[%s] only can be the last parameter (in order to adapt to other programming languages, thus this rule does not match Python's grammar)", p.Name)))
		}
	}

	return nil
}

func ValidateParameterWithDefaultValue(p *pb.Parameter) error {
	if p.Default == "" {
		return nil
	}

	var v any
	if err := jsonx.UnmarshalFromString(p.Default, &v); err != nil {
		return errors.WithStack(errorx.Err(errorx.SerializationError, fmt.Sprintf("failed to unmarshal with the default value[%s] of parameter[%s]", p.Default, p.Name)))
	}

	vk := reflect.Indirect(reflect.ValueOf(v)).Kind()

	switch p.Type {
	case pb.ParameterOrReturnType_STRING:
		if vk == reflect.String {
			return nil
		}
	case pb.ParameterOrReturnType_NUMBER:
		switch vk {
		case reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64, reflect.Int,
			reflect.Uint8, reflect.Uint16, reflect.Uint32, reflect.Uint64, reflect.Uint,
			reflect.Float32, reflect.Float64:
			return nil
		}
	case pb.ParameterOrReturnType_ARRAY:
		if vk == reflect.Array || vk == reflect.Slice {
			return nil
		}
	case pb.ParameterOrReturnType_OBJECT:
		if vk == reflect.Map || vk == reflect.Struct {
			return nil
		}
	case pb.ParameterOrReturnType_BOOLEAN:
		if vk == reflect.Bool {
			return nil
		}
	case pb.ParameterOrReturnType_NULL:
		if vk == reflect.Invalid {
			return nil
		}
	case pb.ParameterOrReturnType_ANY:
		return nil
	}

	return errors.WithStack(errorx.Err(errorx.ProhibitedBehavior, fmt.Sprintf("the type[%T] of default value[%s] is mismatch with parameter type[%s]", v, p.Default, p.Type)))
}

func convertParameterSlice(in []*pb.Parameter) []any {
	out := make([]any, len(in))

	for i, v := range in {
		out[i] = v
	}

	return out
}

func ValidateModifyParameters(from, to []*pb.Parameter) error {
	fs := convertParameterSlice(from)
	ts := convertParameterSlice(to)

	set := hashset.New(ts...)
	if contains := set.Contains(fs...); !contains {
		return errors.WithStack(errorx.Err(errorx.ProhibitedBehavior, "the parameter list of function only increases"))
	}

	return nil
}
