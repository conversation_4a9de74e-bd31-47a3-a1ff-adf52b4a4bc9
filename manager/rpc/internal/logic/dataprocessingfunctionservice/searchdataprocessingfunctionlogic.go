package dataprocessingfunctionservicelogic

import (
	"context"
	"math"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type SearchDataProcessingFunctionLogic struct {
	*BaseLogic
}

func NewSearchDataProcessingFunctionLogic(
	ctx context.Context, svcCtx *svc.ServiceContext,
) *SearchDataProcessingFunctionLogic {
	return &SearchDataProcessingFunctionLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// SearchDataProcessingFunction 搜索数据处理函数
func (l *SearchDataProcessingFunctionLogic) SearchDataProcessingFunction(in *pb.SearchDataProcessingFunctionReq) (
	resp *pb.SearchDataProcessingFunctionResp, err error,
) {
	resp = &pb.SearchDataProcessingFunctionResp{}

	// validate the project_id in req
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, in.GetProjectId()); err != nil {
		return nil, err
	}

	selectBuilder, countBuilder := l.generateSearchSqlBuilder(in)

	count, err := l.svcCtx.FunctionModel.FindCount(l.ctx, countBuilder)
	if err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()),
			"failed to count data processing function with project_id[%s], error: %+v", in.GetProjectId(), err,
		)
	}
	resp.TotalCount = uint64(count)
	resp.TotalPage = 1

	functions, err := l.svcCtx.FunctionModel.FindNoCacheByQuery(l.ctx, selectBuilder)
	if err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()),
			"failed to find data processing function with project_id[%s], error: %+v", in.GetProjectId(), err,
		)
	}

	resp.Items = make([]*pb.DataProcessingFunction, 0, len(functions))
	for _, function := range functions {
		item := &pb.DataProcessingFunction{}
		if err = utils.Copy(item, function, l.converters...); err != nil {
			return nil, errors.Wrapf(
				errorx.Err(errorx.CopyToStructFailure, err.Error()),
				"failed to copy data processing function[%+v] to response, error: %+v", function, err,
			)
		}

		resp.Items = append(resp.Items, item)
	}

	pagination := in.GetPagination()
	if pagination != nil {
		resp.CurrentPage = pagination.GetCurrentPage()
		resp.PageSize = pagination.GetPageSize()
		resp.TotalPage = uint64(math.Ceil(float64(resp.TotalCount) / float64(resp.PageSize)))
	}

	return resp, nil
}
