package dataprocessingfunctionservicelogic

import (
	"context"
	"database/sql"
	"fmt"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/stores/sqlx"

	qetconstants "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/redislock"
	qetutils "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/utils"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type CreateDataProcessingFunctionLogic struct {
	*BaseLogic
}

func NewCreateDataProcessingFunctionLogic(
	ctx context.Context, svcCtx *svc.ServiceContext,
) *CreateDataProcessingFunctionLogic {
	return &CreateDataProcessingFunctionLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// CreateDataProcessingFunction 创建数据处理函数
func (l *CreateDataProcessingFunctionLogic) CreateDataProcessingFunction(in *pb.CreateOrModifyDataProcessingFunctionReq) (
	resp *pb.CreateOrModifyDataProcessingFunctionResp, err error,
) {
	// validate the project_id in req
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, in.GetProjectId()); err != nil {
		return nil, err
	}

	if _, err = l.svcCtx.FunctionModel.FindLatestOneNoCache(
		l.ctx, in.GetProjectId(), in.GetName(), constants.CUSTOM,
	); err != model.ErrNotFound {
		if err != nil {
			return nil, errors.Wrapf(
				errorx.Err(errorx.DBError, err.Error()),
				"failed to find data processing function with project_id[%s] and name[%s], error: %+v",
				in.GetProjectId(), in.GetName(), err,
			)
		}
		return nil, errors.WithStack(
			errorx.Err(
				errorx.AlreadyExists, fmt.Sprintf(
					"data processing function with project_id[%s] and name[%s] already exists", in.GetProjectId(),
					in.GetName(),
				),
			),
		)
	}

	// validate the custom function
	if err = ValidateCustomFunction(in); err != nil {
		return nil, err
	}

	// acquire redis lock
	key := fmt.Sprintf(
		"%s:%s:%s:%s", common.ConstLockFunctionProjectIdNameTypePrefix, in.GetProjectId(), in.GetName(),
		constants.CUSTOM,
	)
	lock, err := redislock.NewRedisLockAndAcquire(l.svcCtx.Redis, key, redislock.WithExpire(common.ConstLockExpireTime))
	if err != nil {
		return nil, err
	}
	defer func() {
		// release redis lock
		e := lock.Release()
		if e != nil {
			// l.Logger.Error(e)
			if re, ok := errorx.RootError(e); ok && re.Code() != errorx.ReleaseRedisLockFailure && err == nil {
				err = e
			}
		}
	}()

	function, err := l.create(in)
	if err != nil {
		return nil, err
	}

	resp = &pb.CreateOrModifyDataProcessingFunctionResp{Function: &pb.DataProcessingFunction{}}
	if err = qetutils.Copy(resp.Function, function, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy data processing function[%+v] to response, error: %+v", function, err,
		)
	}

	return resp, nil
}

// in order to reduce cyclomatic complexity of CreateDataProcessingFunctionLogic.CreateDataProcessingFunction
func (l *CreateDataProcessingFunctionLogic) create(req *pb.CreateOrModifyDataProcessingFunctionReq) (
	*model.Function, error,
) {
	version := utils.GenVersion()

	function := &model.Function{
		ProjectId: req.GetProjectId(),
		Name:      req.GetName(),
		Type:      pb.FunctionType_CUSTOM.String(),
		Category:  req.GetCategory(),
		Description: sql.NullString{
			String: req.GetDescription(),
			Valid:  req.GetDescription() != "",
		},
		Language: req.GetLanguage().String(),
		Content: sql.NullString{
			String: req.GetContent(),
			Valid:  req.GetContent() != "",
		},
		Parameters: protobuf.MarshalJSONWithMessagesToStringIgnoreError(req.GetParameters()),
		Returns:    protobuf.MarshalJSONWithMessagesToStringIgnoreError(req.GetReturns()),
		Example: sql.NullString{
			String: req.GetExample(),
			Valid:  req.GetExample() != "",
		},
		Version:   version,
		Latest:    int64(qetconstants.IsLatestVersion),
		CreatedBy: l.currentUser.Account,
		UpdatedBy: l.currentUser.Account,
	}

	if err := l.svcCtx.FunctionModel.Trans(
		l.ctx, func(context context.Context, session sqlx.Session) error {
			if _, err := l.svcCtx.FunctionModel.InsertTX(context, session, function); err != nil {
				return errors.Wrapf(
					errorx.Err(errorx.DBError, err.Error()), "failed to insert table[%s] with values[%+v], error: %+v",
					l.svcCtx.FunctionModel.Table(), function, err,
				)
			}

			return nil
		},
	); err != nil {
		return nil, err
	}

	return function, nil
}
