package tagservicelogic

import (
	"context"

	"github.com/Masterminds/squirrel"
	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/logx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sqlbuilder"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/userinfo"
	qetutils "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/utils"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type BaseLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext

	currentUser *userinfo.UserInfo

	converters []qetutils.TypeConverter
}

func newBaseLogic(ctx context.Context, svcCtx *svc.ServiceContext) *BaseLogic {
	return &BaseLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,

		currentUser: userinfo.FromContext(ctx),

		converters: []qetutils.TypeConverter{},
	}
}

func (l *BaseLogic) generateTagId(projectId string) (string, error) {
	g := qetutils.NewUniqueIdGenerator(
		qetutils.WithGenerateFunc(utils.GenTagId), qetutils.WithIsUniqueFunc(
			func(id string) bool {
				r, err := l.svcCtx.TagModel.FindOneByProjectIdTagId(l.ctx, projectId, id)
				if errors.Is(err, model.ErrNotFound) || r == nil {
					return true
				}
				return false
			},
		),
	)
	tagId := g.Next()
	if tagId == "" {
		return "", errors.WithStack(
			errorx.Err(
				errorx.GenerateUniqueIdFailure, "failed to generate tag id, please try it later",
			),
		)
	}

	return tagId, nil
}

func (l *BaseLogic) generateSearchSqlBuilder(req *pb.SearchTagReq) (sb, scb squirrel.SelectBuilder) {
	m := l.svcCtx.TagModel

	sb = sqlbuilder.SearchOptions(
		l.svcCtx.TagModel.SelectBuilder().Where("`project_id` = ? AND `type` = ?", req.GetProjectId(), req.GetType()),
		sqlbuilder.WithCondition(m, req.GetCondition()),
		sqlbuilder.WithPagination(m, req.GetPagination()),
		sqlbuilder.WithSort(m, req.GetSort()),
	)
	scb = sqlbuilder.SearchOptions(
		l.svcCtx.TagModel.SelectCountBuilder().Where(
			"`project_id` = ? AND `type` = ?", req.GetProjectId(), req.GetType(),
		),
		sqlbuilder.WithCondition(m, req.GetCondition()),
	)

	return sb, scb
}
