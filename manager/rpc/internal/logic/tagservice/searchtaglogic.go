package tagservicelogic

import (
	"context"
	"math"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type SearchTagLogic struct {
	*BaseLogic
}

func NewSearchTagLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SearchTagLogic {
	return &SearchTagLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// SearchTag 查询标签
func (l *SearchTagLogic) SearchTag(in *pb.SearchTagReq) (resp *pb.SearchTagResp, err error) {
	resp = &pb.SearchTagResp{}

	// validate the project_id in req
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, in.GetProjectId()); err != nil {
		return nil, err
	}

	selectBuilder, countBuilder := l.generateSearchSqlBuilder(in)

	count, err := l.svcCtx.TagModel.FindCount(l.ctx, countBuilder)
	if err != nil {
		return nil, errors.Wrapf(errorx.Err(errorx.DBError, err.Error()), "failed to count tag with project_id[%s], error: %+v", in.GetProjectId(), err)
	}
	resp.TotalCount = uint64(count)
	resp.TotalPage = 1

	tags, err := l.svcCtx.TagModel.FindNoCacheByQuery(l.ctx, selectBuilder)
	if err != nil {
		return nil, errors.Wrapf(errorx.Err(errorx.DBError, err.Error()), "failed to find tag with project_id[%s], error: %+v", in.GetProjectId(), err)
	}

	resp.Items = make([]*pb.Tag, 0, len(tags))
	for _, tag := range tags {
		item := &pb.Tag{}
		if err = utils.Copy(item, tag, l.converters...); err != nil {
			return nil, errors.Wrapf(errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy tag[%+v] to response, error: %+v", tag, err)
		}

		resp.Items = append(resp.Items, item)
	}

	pagination := in.GetPagination()
	if pagination != nil {
		resp.CurrentPage = pagination.GetCurrentPage()
		resp.PageSize = pagination.GetPageSize()
		resp.TotalPage = uint64(math.Ceil(float64(resp.TotalCount) / float64(resp.PageSize)))
	}

	return resp, nil
}
