package tagservicelogic

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type ViewTagLogic struct {
	*BaseLogic
}

func NewViewTagLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ViewTagLogic {
	return &ViewTagLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// ViewTag   查看标签
func (l *ViewTagLogic) ViewTag(in *pb.ViewTagReq) (resp *pb.ViewTagResp, err error) {
	// validate the project_id in req
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, in.GetProjectId()); err != nil {
		return nil, err
	}

	// validate the tag_id in req
	tag, err := model.CheckTagByTagId(l.ctx, l.svcCtx.TagModel, in.GetProjectId(), in.GetTagId())
	if err != nil {
		return nil, err
	}

	resp = &pb.ViewTagResp{Tag: &pb.Tag{}}
	if err = utils.Copy(resp.Tag, tag, l.converters...); err != nil {
		return nil, errors.Wrapf(errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy tag[%+v] to response, error: %+v", tag, err)
	}

	return resp, nil
}
