package tagservicelogic

import "github.com/emirpasic/gods/sets/hashset"

func DiffTags(fromTags, toTags []string) (increaseTags, reduceTags []string) {
	fromSet := hashset.New()
	toSet := hashset.New()

	for _, tag := range fromTags {
		fromSet.Add(tag)
	}

	for _, tag := range toTags {
		toSet.Add(tag)
	}

	// 在`fromSet`中而不在`toSet`中的元素，即减少
	dSet1 := fromSet.Difference(toSet)
	reduceTags = make([]string, 0, dSet1.Size())
	for _, val := range dSet1.Values() {
		if s, ok := val.(string); ok {
			reduceTags = append(reduceTags, s)
		}
	}

	// 在`toSet`中而不在`fromSet`中的元素，即增加
	dSet2 := toSet.Difference(fromSet)
	increaseTags = make([]string, 0, dSet2.Size())
	for _, val := range dSet2.Values() {
		if s, ok := val.(string); ok {
			increaseTags = append(increaseTags, s)
		}
	}

	return increaseTags, reduceTags
}
