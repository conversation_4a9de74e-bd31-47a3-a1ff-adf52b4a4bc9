package tagservicelogic

import (
	"context"
	"fmt"

	"github.com/hashicorp/go-multierror"
	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/mr"
	"github.com/zeromicro/go-zero/core/stores/sqlx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/redislock"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type RemoveTagLogic struct {
	*BaseLogic
}

func NewRemoveTagLogic(ctx context.Context, svcCtx *svc.ServiceContext) *RemoveTagLogic {
	return &RemoveTagLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// RemoveTag 删除标签
func (l *RemoveTagLogic) RemoveTag(in *pb.RemoveTagReq) (resp *pb.RemoveTagResp, err error) {
	// validate the project_id in req
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, in.GetProjectId()); err != nil {
		return nil, err
	}

	tagIds := in.GetTagIds()
	workers := len(tagIds)
	if workers > common.ConstMRMaxWorkers {
		workers = common.ConstMRMaxWorkers
	}

	mr.ForEach(func(source chan<- any) {
		for _, tagId := range tagIds {
			source <- tagId
		}
	}, func(item any) {
		tagId, ok := item.(string)
		if !ok {
			err = multierror.Append(err, errorx.Err(errorx.InternalError, fmt.Sprintf("the tag id[%v (%T)] is not a string", item, item)))
		} else {
			if e := l.remove(in.GetProjectId(), tagId); e != nil {
				err = multierror.Append(err, e)
			}
		}
	}, mr.WithContext(l.ctx), mr.WithWorkers(workers))

	return &pb.RemoveTagResp{}, err
}

func (l *RemoveTagLogic) remove(projectId, tagId string) (err error) {
	// validate the tag_id in req
	tag, err := model.CheckTagByTagId(l.ctx, l.svcCtx.TagModel, projectId, tagId)
	if err != nil {
		return err
	}

	// validate the relationship with tag
	rrs, err := l.svcCtx.TagReferenceModel.FindReferenceByTagId(l.ctx, projectId, tagId)
	if err != nil {
		return errors.Wrapf(errorx.Err(errorx.DBError, err.Error()), "failed to find tag reference with project_id[%s] and tag_id[%s], error: %+v", projectId, tagId, err)
	} else if len(rrs) > 0 {
		return errors.WithStack(errorx.Err(errorx.ProhibitedBehavior, fmt.Sprintf("cannot to delete the tag[%s] which has been referenced by %d item[s]", tag.Name, len(rrs))))
	}

	// acquire redis lock
	key := fmt.Sprintf("%s:%s:%s", common.ConstLockTagProjectIdTagIdPrefix, projectId, tagId)
	lock, err := redislock.NewRedisLockAndAcquire(l.svcCtx.Redis, key, redislock.WithExpire(common.ConstLockExpireTime))
	if err != nil {
		return err
	}
	defer func() {
		// release redis lock
		e := lock.Release()
		if e != nil {
			// l.Logger.Error(e)
			if re, ok := errorx.RootError(e); ok && re.Code() != errorx.ReleaseRedisLockFailure && err == nil {
				err = e
			}
		}
	}()

	key1 := fmt.Sprintf("%s:%s:%s:%s", common.ConstLockTagProjectIdTypeNamePrefix, projectId, tag.Type, tag.Name)
	lock1, err := redislock.NewRedisLockAndAcquire(l.svcCtx.Redis, key1, redislock.WithExpire(common.ConstLockExpireTime))
	if err != nil {
		return err
	}
	defer func() {
		// release redis lock
		e := lock1.Release()
		if e != nil {
			// l.Logger.Error(e)
			if re, ok := errorx.RootError(e); ok && re.Code() != errorx.ReleaseRedisLockFailure && err == nil {
				err = e
			}
		}
	}()

	return l.svcCtx.TagModel.Trans(l.ctx, func(context context.Context, session sqlx.Session) error {
		// Table: tag
		if _, err := l.svcCtx.TagModel.RemoveByTagId(context, session, projectId, tagId); err != nil {
			return err
		}

		return nil
	})
}
