package apiplanservicelogic

import (
	"context"
	"math"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sqlbuilder/rpc"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type SearchApiPlanLogic struct {
	*BaseLogic
}

func NewSearchApiPlanLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SearchApiPlanLogic {
	return &SearchApiPlanLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// SearchApiPlan 搜索API计划
func (l *SearchApiPlanLogic) SearchApiPlan(in *pb.SearchApiPlanReq) (resp *pb.SearchApiPlanResp, err error) {
	resp = &pb.SearchApiPlanResp{}

	// validate the project_id in req
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, in.GetProjectId()); err != nil {
		return nil, err
	}

	// validate the category_id in req
	if _, err = model.CheckCategoryByCategoryId(
		l.ctx, l.svcCtx.CategoryModel, in.GetProjectId(), common.ConstCategoryTreeTypeApiPlan, in.GetCategoryId(),
	); err != nil {
		return nil, err
	}

	selectBuilder, countBuilder := l.svcCtx.ApiPlanModel.GenerateSearchApiPlanSqlBuilder(
		model.SearchApiPlanWithCategoryReq{
			SearchApiPlanReq: model.SearchApiPlanReq{
				ProjectId:  in.GetProjectId(),
				Condition:  in.GetCondition(),
				Pagination: in.GetPagination(),
				Sort:       rpc.ConvertSortFields(in.GetSort()),
			},
			CategoryId:    in.GetCategoryId(),
			DrillDown:     true,
			CategoryModel: l.svcCtx.CategoryModel,
		},
	)

	count, err := l.svcCtx.ApiPlanModel.FindCountApiPlans(l.ctx, countBuilder)
	if err != nil {
		return nil, errors.Wrapf(errorx.Err(errorx.DBError, err.Error()), "failed to count api plan with project_id[%s], error: %+v", in.GetProjectId(), err)
	}
	resp.TotalCount = uint64(count)
	resp.TotalPage = 1

	apiPlans, err := l.svcCtx.ApiPlanModel.FindApiPlans(l.ctx, selectBuilder)
	if err != nil {
		return nil, errors.Wrapf(errorx.Err(errorx.DBError, err.Error()), "failed to find api plan with project_id[%s], error: %+v", in.GetProjectId(), err)
	}

	resp.Items = make([]*pb.SearchApiPlanItem, 0, len(apiPlans))
	for _, apiPlan := range apiPlans {
		item := &pb.SearchApiPlanItem{}
		if err = utils.Copy(item, apiPlan, l.converters...); err != nil {
			return nil, errors.Wrapf(errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy api plan[%+v] to response, error: %+v", apiPlan, err)
		}

		resp.Items = append(resp.Items, item)
	}

	pagination := in.GetPagination()
	if pagination != nil {
		resp.CurrentPage = pagination.GetCurrentPage()
		resp.PageSize = pagination.GetPageSize()
		resp.TotalPage = uint64(math.Ceil(float64(resp.TotalCount) / float64(resp.PageSize)))
	}

	return resp, nil
}
