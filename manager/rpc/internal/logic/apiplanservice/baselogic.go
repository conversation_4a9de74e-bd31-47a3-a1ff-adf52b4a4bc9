package apiplanservicelogic

import (
	"context"
	"database/sql"
	"fmt"
	"time"

	"github.com/emirpasic/gods/sets/hashset"
	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/lang"
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/mr"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	qetconstants "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/userinfo"
	qetutils "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/utils"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/logic"
	tagservicelogic "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/logic/tagservice"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/types"
)

type BaseLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext

	currentUser *userinfo.UserInfo

	createTagLogic *tagservicelogic.CreateTagLogic

	converters []qetutils.TypeConverter
}

func newBaseLogic(ctx context.Context, svcCtx *svc.ServiceContext) *BaseLogic {
	return &BaseLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,

		currentUser: userinfo.FromContext(ctx),

		createTagLogic: tagservicelogic.NewCreateTagLogic(ctx, svcCtx),

		converters: []qetutils.TypeConverter{
			logic.SqlNullStringToTags(),
			logic.StringToResourceState(),
			logic.StringToTriggerMode(),
			logic.StringToPurposeType(),
		},
	}
}

func (l *BaseLogic) generatePlanId(projectId string) (string, error) {
	g := qetutils.NewUniqueIdGenerator(
		qetutils.WithGenerateFunc(utils.GenPlanId), qetutils.WithIsUniqueFunc(
			func(id string) bool {
				r, err := l.svcCtx.ApiPlanModel.FindOneByProjectIdPlanId(l.ctx, projectId, id)
				if errors.Is(err, model.ErrNotFound) || r == nil {
					return true
				}
				return false
			},
		),
	)
	planId := g.Next()
	if planId == "" {
		return "", errors.WithStack(
			errorx.Err(
				errorx.GenerateUniqueIdFailure, "failed to generate api plan id, please try it later",
			),
		)
	}

	return planId, nil
}

func (l *BaseLogic) createAccountConfigReference(
	ctx context.Context, session sqlx.Session, req createAccountConfigReferenceInternalReq,
) error {
	if ctx == nil {
		ctx = l.ctx
	}

	rs, err := l.svcCtx.AccountConfigReferenceModel.FindReferenceByReference(
		ctx, req.ProjectId, req.ReferenceType, req.ReferenceId,
	)
	if err != nil && !errors.Is(err, model.ErrNotFound) {
		return errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()),
			"failed to find the account configuration reference with project_id[%s], reference_type[%s] and reference_id[%s], error: %+v",
			req.ProjectId, req.ReferenceType, req.ReferenceId, err,
		)
	}

	toSet := hashset.New()
	for _, configId := range req.ConfigIds {
		toSet.Add(configId)
	}

	workers := len(rs) + toSet.Size()
	if workers > common.ConstMRMaxWorkers {
		workers = common.ConstMRMaxWorkers
	}

	if workers == 0 {
		return nil
	}

	return mr.MapReduceVoid[any, any](
		func(source chan<- any) {
			for _, r := range rs {
				r := r

				if toSet.Contains(r.ConfigId) {
					// 原配置在本次全量更新中，则无需新增（从列表中移除）
					toSet.Remove(r.ConfigId)
				} else {
					// 原配置不在本次全量更新中，则需要删除
					source <- r
				}
			}

			for _, configId := range toSet.Values() {
				source <- configId
			}
		}, func(item any, writer mr.Writer[any], cancel func(error)) {
			var err error
			defer func() {
				if err != nil {
					cancel(err)
				}
			}()

			switch v := item.(type) {
			case string:
				// 新增
				data := &model.AccountConfigurationReferenceRelationship{
					ProjectId:     req.ProjectId,
					ReferenceType: req.ReferenceType,
					ReferenceId:   req.ReferenceId,
					ConfigId:      v,
					CreatedBy:     l.currentUser.Account,
					UpdatedBy:     l.currentUser.Account,
					CreatedAt:     time.Now(),
					UpdatedAt:     time.Now(),
				}
				if _, err = l.svcCtx.AccountConfigReferenceModel.Insert(ctx, session, data); err != nil {
					err = errors.Wrapf(
						errorx.Err(errorx.DBError, err.Error()),
						"failed to insert table[%s] with values[%+v], error: %+v",
						l.svcCtx.AccountConfigReferenceModel.Table(), data, err,
					)
					return
				}
			case *model.AccountConfigurationReferenceRelationship:
				// 删除
				v.Deleted = int64(qetconstants.HasDeleted)
				v.DeletedBy = sql.NullString{
					String: l.currentUser.Account,
					Valid:  true,
				}
				v.DeletedAt = sql.NullTime{
					Time:  time.Now(),
					Valid: true,
				}
				if _, err = l.svcCtx.AccountConfigReferenceModel.Update(ctx, session, v); err != nil {
					err = errors.Wrapf(
						errorx.Err(errorx.DBError, err.Error()),
						"failed to update table[%s] with values[%+v], error: %+v",
						l.svcCtx.AccountConfigReferenceModel.Table(), v, err,
					)
					return
				}
			}
		}, func(pipe <-chan any, cancel func(error)) {
		}, mr.WithContext(ctx), mr.WithWorkers(workers),
	)
}

func (l *BaseLogic) createApiPlanReference(
	ctx context.Context, session sqlx.Session, req CreateOrRemoveReferenceInternalReq,
	cache types.StringPlaceholderCache,
) error {
	if ctx == nil {
		ctx = l.ctx
	}

	referenceIds := stringx.Distinct(req.ReferenceIds)

	workers := len(referenceIds)
	if workers > common.ConstMRMaxWorkers {
		workers = common.ConstMRMaxWorkers
	}

	if workers == 0 {
		return nil
	}

	if cache == nil {
		// 查询API计划下的引用关系
		rrs, err := l.svcCtx.ApiPlanReferenceModel.FindReferenceByPlanId(ctx, req.ProjectId, req.PlanId)
		if err != nil {
			return errors.Wrapf(
				errorx.Err(errorx.DBError, err.Error()),
				"failed to find api plan reference with project_id[%s] and plan_id[%s], error: %+v", req.ProjectId,
				req.PlanId, err,
			)
		}

		cache = make(types.StringPlaceholderCache, len(rrs))
		for _, rr := range rrs {
			cache[rr.ReferenceParentId.String+":"+rr.ReferenceType+":"+rr.ReferenceId] = lang.Placeholder
		}
	}

	return mr.MapReduceVoid[string, any](
		func(source chan<- string) {
			for _, referenceId := range referenceIds {
				source <- referenceId
			}
		}, l.createApiPlanReferenceMapperFunc(ctx, session, req, cache), func(pipe <-chan any, cancel func(error)) {
		}, mr.WithContext(ctx), mr.WithWorkers(workers),
	)
}

func (l *BaseLogic) createApiPlanReference2(
	ctx context.Context, session sqlx.Session, req CreateOrRemoveReference2InternalReq,
	cache types.StringPlaceholderCache,
) error {
	if ctx == nil {
		ctx = l.ctx
	}

	references := make(map[string]*CreateOrRemoveReference)
	for _, reference := range req.References {
		if _, ok := references[reference.ReferenceParentId+":"+reference.ReferenceType+":"+reference.ReferenceId]; ok {
			continue
		} else {
			references[reference.ReferenceParentId+":"+reference.ReferenceType+":"+reference.ReferenceId] = reference
		}
	}

	workers := len(references)
	if workers > common.ConstMRMaxWorkers {
		workers = common.ConstMRMaxWorkers
	}

	if workers == 0 {
		return nil
	}

	if cache == nil {
		// 查询API计划下的引用关系
		rrs, err := l.svcCtx.ApiPlanReferenceModel.FindReferenceByPlanId(ctx, req.ProjectId, req.PlanId)
		if err != nil {
			return errors.Wrapf(
				errorx.Err(errorx.DBError, err.Error()),
				"failed to find api plan reference with project_id[%s] and plan_id[%s], error: %+v", req.ProjectId,
				req.PlanId, err,
			)
		}

		cache = make(types.StringPlaceholderCache, len(rrs))
		for _, rr := range rrs {
			cache[rr.ReferenceParentId.String+":"+rr.ReferenceType+":"+rr.ReferenceId] = lang.Placeholder
		}
	}

	return mr.MapReduceVoid[*CreateOrRemoveReference, any](
		func(source chan<- *CreateOrRemoveReference) {
			for _, reference := range references {
				source <- reference
			}
		}, l.createApiPlanReference2MapperFunc(ctx, session, req, cache), func(pipe <-chan any, cancel func(error)) {
		}, mr.WithContext(ctx), mr.WithWorkers(workers),
	)
}

func (l *BaseLogic) createApiPlanReferenceMapperFunc(
	ctx context.Context, session sqlx.Session, req CreateOrRemoveReferenceInternalReq,
	cache types.StringPlaceholderCache,
) mr.MapperFunc[string, any] {
	return func(item string, writer mr.Writer[any], cancel func(error)) {
		var err error
		defer func() {
			if err != nil {
				cancel(err)
			}
		}()

		var (
			referenceId   = item
			referenceType common.ReferenceType
			ids           []string
		)

		switch req.ReferenceType {
		case common.ConstReferenceTypeApiSuite:
			if !req.NoNeedToCheck {
				if _, err = model.CheckApiSuiteBySuiteId(
					ctx, l.svcCtx.ApiSuiteModel, req.ProjectId, referenceId,
				); err != nil {
					return
				}
			}

			referenceType = common.ConstReferenceTypeApiCase
			ids, err = l.getApiCaseIdsByApiSuiteId(ctx, req.ProjectId, referenceId)
			if err != nil {
				return
			}
		case common.ConstReferenceTypeInterfaceDocument:
			if !req.NoNeedToCheck {
				if _, err = model.CheckInterfaceDocumentByDocumentId(
					ctx, l.svcCtx.InterfaceDocumentModel, req.ProjectId, referenceId,
				); err != nil {
					return
				}
			}

			referenceType = common.ConstReferenceTypeInterfaceCase
			ids, err = l.getCaseIdsByDocumentId(ctx, req.ProjectId, referenceId)
			if err != nil {
				return
			}
		case common.ConstReferenceTypeApiCase:
			if !req.NoNeedToCheck {
				if _, err = model.CheckApiCaseByCaseId(
					ctx, l.svcCtx.ApiCaseModel, req.ProjectId, referenceId, "",
				); err != nil {
					return
				}
			}
		case common.ConstReferenceTypeInterfaceCase:
			if !req.NoNeedToCheck {
				if _, err = model.CheckInterfaceCaseByCaseId(
					ctx, l.svcCtx.InterfaceCaseModel, req.ProjectId, req.ReferenceParentId, referenceId, "",
				); err != nil {
					return
				}
			}
		default:
			l.Logger.Warnf("the reference type[%s] doesn't support", req.ReferenceType)
			return
		}

		// 引用关系已存在，则无需新增
		if _, ok := cache[req.ReferenceParentId+":"+req.ReferenceType+":"+referenceId]; !ok {
			data := &model.ApiPlanReferenceRelationship{
				ProjectId: req.ProjectId,
				ReferenceParentId: sql.NullString{
					String: req.ReferenceParentId,
					Valid:  req.ReferenceParentId != "",
				},
				ReferenceType: req.ReferenceType,
				ReferenceId:   referenceId,
				PlanId:        req.PlanId,
				State:         int64(qetconstants.EnableStatus),
				CreatedBy:     l.currentUser.Account,
				UpdatedBy:     l.currentUser.Account,
				CreatedAt:     time.Now(),
				UpdatedAt:     time.Now(),
			}
			if _, err = l.svcCtx.ApiPlanReferenceModel.Insert(ctx, session, data); err != nil {
				err = errors.Wrapf(
					errorx.Err(errorx.DBError, err.Error()), "failed to insert table[%s] with values[%+v], error: %+v",
					l.svcCtx.ApiPlanReferenceModel.Table(), data, err,
				)
				return
			}
		}

		if referenceType != "" && len(ids) > 0 {
			err = l.createApiPlanReference(
				ctx, session, CreateOrRemoveReferenceInternalReq{
					ProjectId:         req.ProjectId,
					ReferenceParentId: referenceId,
					ReferenceType:     referenceType,
					ReferenceIds:      ids,
					PlanId:            req.PlanId,
					NoNeedToCheck:     true,
				}, cache,
			)
		}
	}
}

func (l *BaseLogic) createApiPlanReference2MapperFunc(
	ctx context.Context, session sqlx.Session, req CreateOrRemoveReference2InternalReq,
	cache types.StringPlaceholderCache,
) mr.MapperFunc[*CreateOrRemoveReference, any] {
	return func(item *CreateOrRemoveReference, writer mr.Writer[any], cancel func(error)) {
		var err error
		defer func() {
			if err != nil {
				cancel(err)
			}
		}()

		var (
			referenceId       = item.ReferenceId
			referenceType     = item.ReferenceType
			referenceParentId = item.ReferenceParentId
			references        []*CreateOrRemoveReference
		)

		switch referenceType {
		case common.ConstReferenceTypeApiSuite:
			if !req.NoNeedToCheck {
				if _, err = model.CheckApiSuiteBySuiteId(
					ctx, l.svcCtx.ApiSuiteModel, req.ProjectId, referenceId,
				); err != nil {
					return
				}
			}

			references, err = l.getReferencesByApiSuiteId(ctx, req.ProjectId, referenceId)
			if err != nil {
				return
			}
		case common.ConstReferenceTypeInterfaceDocument:
			if !req.NoNeedToCheck {
				if _, err = model.CheckInterfaceDocumentByDocumentId(
					ctx, l.svcCtx.InterfaceDocumentModel, req.ProjectId, referenceId,
				); err != nil {
					return
				}
			}

			caseIds, err := l.getCaseIdsByDocumentId(ctx, req.ProjectId, referenceId)
			if err != nil {
				return
			}
			for _, caseId := range caseIds {
				references = append(references, &CreateOrRemoveReference{
					ReferenceParentId: referenceId,
					ReferenceType:     common.ConstReferenceTypeInterfaceCase,
					ReferenceId:       caseId,
				})
			}
		case common.ConstReferenceTypeApiCase:
			if !req.NoNeedToCheck {
				if _, err = model.CheckApiCaseByCaseId(
					ctx, l.svcCtx.ApiCaseModel, req.ProjectId, referenceId, "",
				); err != nil {
					return
				}
			}
		case common.ConstReferenceTypeInterfaceCase:
			if !req.NoNeedToCheck {
				if _, err = model.CheckInterfaceCaseByCaseId(
					ctx, l.svcCtx.InterfaceCaseModel, req.ProjectId, "", referenceId, "",
				); err != nil {
					return
				}
			}
		default:
			l.Logger.Warnf("the reference type[%s] doesn't support", referenceType)
			return
		}

		// 引用关系已存在，则无需新增
		if _, ok := cache[referenceParentId+":"+referenceType+":"+referenceId]; !ok {
			data := &model.ApiPlanReferenceRelationship{
				ProjectId: req.ProjectId,
				ReferenceParentId: sql.NullString{
					String: referenceParentId,
					Valid:  referenceParentId != "",
				},
				ReferenceType: referenceType,
				ReferenceId:   referenceId,
				PlanId:        req.PlanId,
				State:         int64(qetconstants.EnableStatus),
				CreatedBy:     l.currentUser.Account,
				UpdatedBy:     l.currentUser.Account,
				CreatedAt:     time.Now(),
				UpdatedAt:     time.Now(),
			}
			if _, err = l.svcCtx.ApiPlanReferenceModel.Insert(ctx, session, data); err != nil {
				err = errors.Wrapf(
					errorx.Err(errorx.DBError, err.Error()), "failed to insert table[%s] with values[%+v], error: %+v",
					l.svcCtx.ApiPlanReferenceModel.Table(), data, err,
				)
				return
			}
		}

		if len(references) > 0 {
			err = l.createApiPlanReference2(
				ctx, session, CreateOrRemoveReference2InternalReq{
					ProjectId:     req.ProjectId,
					References:    references,
					PlanId:        req.PlanId,
					NoNeedToCheck: true,
				}, cache,
			)
		}
	}
}

func (l *BaseLogic) getApiCaseIdsByApiSuiteId(ctx context.Context, projectId, suiteId string) ([]string, error) {
	var (
		ids      []string
		err      error
		apiCases []*model.ApiCase
	)

	if ctx == nil {
		ctx = l.ctx
	}

	apiCases, err = l.svcCtx.ApiCaseModel.FindLatestBySuiteId(ctx, projectId, suiteId)
	if err != nil && err != model.ErrNotFound {
		return nil, errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()),
			"failed to find api cases in api suite with project_id[%s] and suite_id[%s], error: %+v", projectId,
			suiteId, err,
		)
	}

	ids = make([]string, 0, len(apiCases))
	for _, apiCase := range apiCases {
		ids = append(ids, apiCase.CaseId)
	}

	return ids, nil
}

func (l *BaseLogic) getReferencesByApiSuiteId(ctx context.Context, projectId, suiteId string) ([]*CreateOrRemoveReference, error) {
	if ctx == nil {
		ctx = l.ctx
	}

	rrs, err := l.svcCtx.ApiSuiteReferenceModel.FindReferenceBySuiteId(ctx, projectId, suiteId)
	if err != nil && err != model.ErrNotFound {
		return nil, errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()),
			"failed to find cases in api suite with project_id[%s] and suite_id[%s], error: %+v",
			projectId, suiteId, err,
		)
	}

	references := make([]*CreateOrRemoveReference, 0, len(rrs))
	for _, rr := range rrs {
		references = append(references, &CreateOrRemoveReference{
			ReferenceParentId: suiteId,
			ReferenceType:     rr.ReferenceType,
			ReferenceId:       rr.ReferenceId,
		})
	}

	return references, nil
}

func (l *BaseLogic) getCaseIdsByDocumentId(ctx context.Context, projectId, documentId string) ([]string, error) {
	var (
		ids            []string
		err            error
		interfaceCases []*model.InterfaceCase
	)

	if ctx == nil {
		ctx = l.ctx
	}

	interfaceCases, err = l.svcCtx.InterfaceCaseModel.FindLatestByDocumentId(ctx, projectId, documentId)
	if err != nil && err != model.ErrNotFound {
		return nil, errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()),
			"failed to find interface cases with project_id[%s] and document_id[%s], error: %+v", projectId, documentId,
			err,
		)
	}

	ids = make([]string, 0, len(interfaceCases))
	for _, interfaceCase := range interfaceCases {
		ids = append(ids, interfaceCase.CaseId)
	}

	return ids, err
}

func (l *BaseLogic) removeApiPlanReference(
	ctx context.Context, session sqlx.Session, req CreateOrRemoveReferenceInternalReq,
) error {
	if ctx == nil {
		ctx = l.ctx
	}

	referenceIds := stringx.Distinct(req.ReferenceIds)

	workers := len(referenceIds)
	if workers > common.ConstMRMaxWorkers {
		workers = common.ConstMRMaxWorkers
	}

	if workers == 0 {
		return nil
	}

	return mr.MapReduceVoid[string, any](
		func(source chan<- string) {
			for _, referenceId := range referenceIds {
				source <- referenceId
			}
		}, func(item string, writer mr.Writer[any], cancel func(error)) {
			var err error
			defer func() {
				if err != nil {
					cancel(err)
				}
			}()

			referenceId := item

			switch req.ReferenceType {
			case common.ConstReferenceTypeApiSuite,
				common.ConstReferenceTypeInterfaceDocument,
				common.ConstReferenceTypeApiCase,
				common.ConstReferenceTypeInterfaceCase:
			default:
				l.Logger.Warnf("the reference type[%s] doesn't support", req.ReferenceType)
				return
			}

			if _, err = l.svcCtx.ApiPlanReferenceModel.RemoveByPlanIdReference(
				ctx, session, req.ProjectId, req.PlanId, req.ReferenceParentId, req.ReferenceType, referenceId,
			); err != nil {
				err = errors.Wrapf(
					errorx.Err(errorx.DBError, err.Error()),
					"failed to remove api plan reference by project_id[%s], plan_id[%s], reference_parent_id[%s], reference_type[%s] and reference_id[%s], error: %+v",
					req.ProjectId, req.PlanId, req.ReferenceParentId, req.ReferenceType, referenceId, err,
				)
				return
			}
		}, func(pipe <-chan any, cancel func(error)) {
		}, mr.WithContext(ctx), mr.WithWorkers(workers),
	)
}

func (l *BaseLogic) removeApiPlanReference2(
	ctx context.Context, session sqlx.Session, req CreateOrRemoveReference2InternalReq,
) error {
	if ctx == nil {
		ctx = l.ctx
	}

	references := make(map[string]*CreateOrRemoveReference)
	for _, reference := range req.References {
		if _, ok := references[reference.ReferenceParentId+":"+reference.ReferenceType+":"+reference.ReferenceId]; ok {
			continue
		} else {
			references[reference.ReferenceParentId+":"+reference.ReferenceType+":"+reference.ReferenceId] = reference
		}
	}

	workers := len(references)
	if workers > common.ConstMRMaxWorkers {
		workers = common.ConstMRMaxWorkers
	}

	if workers == 0 {
		return nil
	}

	return mr.MapReduceVoid[*CreateOrRemoveReference, any](
		func(source chan<- *CreateOrRemoveReference) {
			for _, reference := range references {
				source <- reference
			}
		}, func(item *CreateOrRemoveReference, writer mr.Writer[any], cancel func(error)) {
			var err error
			defer func() {
				if err != nil {
					cancel(err)
				}
			}()

			var (
				referenceId       = item.ReferenceId
				referenceType     = item.ReferenceType
				referenceParentId = item.ReferenceParentId
			)

			switch referenceType {
			case common.ConstReferenceTypeApiSuite,
				common.ConstReferenceTypeInterfaceDocument,
				common.ConstReferenceTypeApiCase,
				common.ConstReferenceTypeInterfaceCase:
			default:
				l.Logger.Warnf("the reference type[%s] doesn't support", referenceType)
				return
			}

			if _, err = l.svcCtx.ApiPlanReferenceModel.RemoveByPlanIdReference(
				ctx, session, req.ProjectId, req.PlanId, referenceParentId, referenceType, referenceId,
			); err != nil {
				err = errors.Wrapf(
					errorx.Err(errorx.DBError, err.Error()),
					"failed to remove api plan reference by project_id[%s], plan_id[%s], reference_parent_id[%s], reference_type[%s] and reference_id[%s], error: %+v",
					req.ProjectId, req.PlanId, referenceParentId, referenceType, referenceId, err,
				)
				return
			}
		}, func(pipe <-chan any, cancel func(error)) {
		}, mr.WithContext(ctx), mr.WithWorkers(workers),
	)
}

func (l *BaseLogic) generatePeriodicTaskName(projectId, planId string) string {
	return fmt.Sprintf("%s:projectId:planId:%s:%s", constants.MQTaskTypeDispatcherPeriodicPlanTask, projectId, planId)
}
