package apiplanservicelogic

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sqlbuilder/rpc"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type SearchLikeApiPlanLogic struct {
	*BaseLogic
}

func NewSearchLikeApiPlanLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SearchLikeApiPlanLogic {
	return &SearchLikeApiPlanLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// SearchLikeApiPlan 搜索API计划
func (l *SearchLikeApiPlanLogic) SearchLikeApiPlan(in *pb.SearchApiPlanReq) (out *pb.SearchApiPlanResp, err error) {
	out = new(pb.SearchApiPlanResp)
	user := l.currentUser
	if user == nil {
		return nil, nil
	}

	searchApiPlanReq := model.SearchApiPlanReq{
		ProjectId:  in.GetProjectId(),
		Condition:  in.GetCondition(),
		Pagination: in.GetPagination(),
		Sort:       rpc.ConvertSortFields(in.GetSort()),
	}
	queryBuilder, countBuilder := l.svcCtx.PlanUserLikeRelationshipModel.GenerateApiPlanJoinPlanLikeSqlBuilderForProjectIdAndQuery(
		in.GetProjectId(), user.Account, searchApiPlanReq,
	)
	count, err := l.svcCtx.PlanUserLikeRelationshipModel.FindCount(l.ctx, countBuilder.SelectBuilder)
	if err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()), "failed to count api plan with project_id[%s], error: %+v",
			in.GetProjectId(), err,
		)
	}
	out.TotalCount = uint64(count)
	out.TotalPage = 1
	searchJoinApiPlanItemList, err := l.svcCtx.PlanUserLikeRelationshipModel.FindNoCacheJoinApiPlanByQuery(
		l.ctx, queryBuilder.SelectBuilder,
	)
	if err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()), "failed to list api plan with project_id[%s], error: %+v",
			in.GetProjectId(), err,
		)
	}

	out.Items = make([]*pb.SearchApiPlanItem, 0, len(searchJoinApiPlanItemList))
	for _, apiPlan := range searchJoinApiPlanItemList {
		item := &pb.SearchApiPlanItem{}
		if err = utils.Copy(item, apiPlan, l.converters...); err != nil {
			return nil, errors.Wrapf(
				errorx.Err(errorx.CopyToStructFailure, err.Error()),
				"failed to copy api plan[%+v] to response, error: %+v", apiPlan, err,
			)
		}

		out.Items = append(out.Items, item)
	}

	pagination := in.GetPagination()
	if pagination != nil {
		out.CurrentPage = pagination.GetCurrentPage()
		out.PageSize = pagination.GetPageSize()
		out.TotalPage = (out.TotalCount + out.PageSize - 1) / out.PageSize // round up
	}
	return out, nil
}
