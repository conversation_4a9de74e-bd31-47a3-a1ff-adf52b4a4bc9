package apiplanservicelogic

import (
	"context"
	"database/sql"
	"fmt"
	"strings"

	"github.com/gorhill/cronexpr"
	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"
	"github.com/zeromicro/go-zero/core/stores/sqlx"

	qetconstants "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/redislock"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"
	beatcommon "gitlab.ttyuyin.com/TestDevelopment/qet-backend-middleware/beat/common"
	beatpb "gitlab.ttyuyin.com/TestDevelopment/qet-backend-middleware/beat/rpc/pb"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	commonpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common/zrpc/beat"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type ModifyApiPlanLogic struct {
	*BaseLogic
}

func NewModifyApiPlanLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ModifyApiPlanLogic {
	return &ModifyApiPlanLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// ModifyApiPlan 编辑API计划
func (l *ModifyApiPlanLogic) ModifyApiPlan(in *pb.ModifyApiPlanReq) (resp *pb.ModifyApiPlanResp, err error) {
	// validate the project_id in req
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, in.GetProjectId()); err != nil {
		return nil, err
	}

	// validate the plan_id in req
	origin, err := model.CheckApiPlanByPlanId(l.ctx, l.svcCtx.ApiPlanModel, in.GetProjectId(), in.GetPlanId())
	if err != nil {
		return nil, err
	}

	// validate the cron_expression in req if plan type is SCHEDULE
	if in.GetType() == commonpb.TriggerMode_SCHEDULE {
		if _, err = cronexpr.Parse(in.GetCronExpression()); err != nil {
			return nil, errors.Wrapf(
				errorx.Err(errorx.CronExpressionParseError, err.Error()),
				"failed to parse the cron expression, cron: %s, error: %+v",
				in.GetCronExpression(), err,
			)
		}
	}

	// validate the general_config_id in req
	if in.GetGeneralConfigId() != "" {
		gc, err := model.CheckGeneralConfigByConfigId(
			l.ctx, l.svcCtx.GeneralConfigModel, in.GetProjectId(), in.GetGeneralConfigId(),
		)
		if err != nil {
			return nil, err
		}
		if gc.Type != string(common.ConstTestTypeAPI) {
			return nil, errorx.Errorf(
				errorx.ProhibitedBehavior,
				"cannot to choose a general configuration whose type is not %s for api plan, name: %s, type: %s",
				common.ConstTestTypeAPI, gc.Name, gc.Type,
			)
		}
	}

	// validate the account_config_ids in req
	for _, configId := range in.AccountConfigIds {
		if _, err = model.CheckAccountConfigByConfigId(
			l.ctx, l.svcCtx.AccountConfigModel, in.GetProjectId(), configId,
		); err != nil {
			return nil, err
		}

		// TODO: 需要判断池账号配置是否满足需要
	}

	// acquire redis lock
	key := fmt.Sprintf("%s:%s:%s", common.ConstLockApiPlanProjectIdPlanIdPrefix, in.GetProjectId(), in.GetPlanId())
	lock, err := redislock.NewRedisLockAndAcquire(l.svcCtx.Redis, key, redislock.WithExpire(common.ConstLockExpireTime))
	if err != nil {
		return nil, err
	}
	defer func() {
		// release redis lock
		e := lock.Release()
		if e != nil {
			// l.Logger.Error(e)
			if re, ok := errorx.RootError(e); ok && re.Code() != errorx.ReleaseRedisLockFailure && err == nil {
				err = e
			}
		}
	}()

	apiPlan, err := l.modify(in, origin)
	if err != nil {
		return nil, err
	}

	resp = &pb.ModifyApiPlanResp{Plan: &pb.ApiPlan{}}
	if err = utils.Copy(resp.Plan, apiPlan, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy api plan[%+v] to response, error: %+v",
			apiPlan, err,
		)
	}

	return resp, nil
}

func (l *ModifyApiPlanLogic) modify(req *pb.ModifyApiPlanReq, origin *model.ApiPlan) (*model.ApiPlan, error) {
	// 由于`protojson`只能序列化`[]proto.Message`，而`tags`字段类型为`[]string`，Go Struct跟PB没有大的区别，因此直接使用`jsonx`暂代`protojson`
	tags := jsonx.MarshalToStringIgnoreError(req.GetTags())

	var updatePurpose string
	if req.GetPurpose() == commonpb.PurposeType_UNDEFINED {
		existPlan, existErr := l.svcCtx.ApiPlanModel.FindOne(l.ctx, origin.Id)
		if existErr != nil {
			return nil, existErr
		}
		updatePurpose = existPlan.Purpose
	} else {
		updatePurpose = req.GetPurpose().String()
	}

	apiPlan := &model.ApiPlan{
		Id:         origin.Id,
		ProjectId:  origin.ProjectId,
		CategoryId: req.GetCategoryId(),
		PlanId:     origin.PlanId,
		Name:       req.GetName(),
		Description: sql.NullString{
			String: req.GetDescription(),
			Valid:  req.GetDescription() != "",
		},
		Priority: req.GetPriority(),
		Tags: sql.NullString{
			String: tags,
			Valid:  tags != "",
		},
		State:   int64(req.GetState()),
		Type:    req.GetType().String(),
		Purpose: updatePurpose,
		CronExpression: sql.NullString{
			String: req.GetCronExpression(),
			Valid:  req.GetCronExpression() != "",
		},
		GeneralConfigId: sql.NullString{
			String: req.GetGeneralConfigId(),
			Valid:  req.GetGeneralConfigId() != "",
		},
		SuiteExecutionMode: int64(req.GetSuiteExecutionMode()),
		CaseExecutionMode:  int64(req.GetCaseExecutionMode()),
		MaintainedBy: sql.NullString{
			String: req.GetMaintainedBy(),
			Valid:  req.GetMaintainedBy() != "",
		},
		CreatedBy: origin.CreatedBy,
		UpdatedBy: l.currentUser.Account,
		CreatedAt: origin.CreatedAt,
	}

	if err := l.svcCtx.ApiPlanModel.Trans(
		l.ctx, func(context context.Context, session sqlx.Session) error {
			if _, err := l.svcCtx.ApiPlanModel.UpdateTX(context, session, apiPlan); err != nil {
				return errors.Wrapf(
					errorx.Err(errorx.DBError, err.Error()), "failed to update table[%s] with values[%+v], error: %+v",
					l.svcCtx.ApiPlanModel.Table(), apiPlan, err,
				)
			}

			// update the account configuration reference of api plan
			if err := l.createAccountConfigReference(
				context, session, createAccountConfigReferenceInternalReq{
					ProjectId:     apiPlan.ProjectId,
					ReferenceType: common.ConstReferenceTypeApiPlan,
					ReferenceId:   apiPlan.PlanId,
					ConfigIds:     req.GetAccountConfigIds(),
				},
			); err != nil {
				return err
			}

			// update the new tag and tag reference of api plan
			if err := l.createTagLogic.CreateTagAndReferenceForInternal(
				context, session, types.CreateOrUpdateTagReference{
					ProjectId:     apiPlan.ProjectId,
					ReferenceType: common.ConstReferenceTypeApiPlan,
					ReferenceId:   apiPlan.PlanId,
					Tags:          req.GetTags(),
				},
			); err != nil {
				return err
			}

			return l.handleScheduleApiPlan(req, origin)
		},
	); err != nil {
		return nil, err
	}

	return apiPlan, nil
}

func (l *ModifyApiPlanLogic) handleScheduleApiPlan(req *pb.ModifyApiPlanReq, origin *model.ApiPlan) error {
	var (
		beatOperator beat.ScheduleTaskOperator

		reqState = req.GetState()
		reqType  = req.GetType()
		reqCron  = req.GetCronExpression()
	)

	// enable to disable
	if origin.State == int64(qetconstants.EnableStatus) && reqState == pb.CommonState_CS_DISABLE {
		// stop schedule task
		if origin.Type == commonpb.TriggerMode_SCHEDULE.String() {
			beatOperator = beat.ConstBeatScheduleTaskOperatorRemove
		}
	}

	// disable to enable
	if origin.State == int64(qetconstants.DisableStatus) && reqState == pb.CommonState_CS_ENABLE {
		// start schedule task
		if reqType == commonpb.TriggerMode_SCHEDULE {
			beatOperator = beat.ConstBeatScheduleTaskOperatorCreate
		}
	}

	// enable to enable
	if origin.State == int64(qetconstants.EnableStatus) && reqState == pb.CommonState_CS_ENABLE {
		// schedule to other type
		if origin.Type == commonpb.TriggerMode_SCHEDULE.String() && reqType != commonpb.TriggerMode_SCHEDULE {
			beatOperator = beat.ConstBeatScheduleTaskOperatorRemove
		}

		// other type to schedule
		if origin.Type != commonpb.TriggerMode_SCHEDULE.String() && reqType == commonpb.TriggerMode_SCHEDULE {
			beatOperator = beat.ConstBeatScheduleTaskOperatorCreate
		}

		// schedule to schedule, and cron expression has been changed
		if origin.Type == commonpb.TriggerMode_SCHEDULE.String() && reqType == commonpb.TriggerMode_SCHEDULE && origin.CronExpression.String != req.GetCronExpression() {
			beatOperator = beat.ConstBeatScheduleTaskOperatorCreate
		}
	}

	if beatOperator != "" {
		// create or remove schedule task
		switch beatOperator {
		case beat.ConstBeatScheduleTaskOperatorCreate:
			if _, err := l.svcCtx.BeatRPC.CreateOrModifyPeriodicTask(
				l.ctx, &beatpb.CreateOrModifyPeriodicTaskReq{
					Name:  l.generatePeriodicTaskName(origin.ProjectId, origin.PlanId),
					Type:  constants.MQTaskTypeDispatcherPeriodicPlanTask,
					Queue: constants.MQNamePeriodicPlanTask,
					Spec:  reqCron,
					Payload: protobuf.MarshalJSONToStringIgnoreError(
						&commonpb.PeriodicPlanTaskInfo{
							ProjectId:      origin.ProjectId,
							PlanId:         origin.PlanId,
							CronExpression: reqCron,
							PlanType:       commonpb.PlanType_API,
						},
					),
					Version: beatcommon.V2,
				},
			); err != nil {
				l.Logger.Errorf(
					"failed to %s schedule task of api plan[%s, %s, %s], error: %+v",
					strings.ToLower(beatOperator), origin.ProjectId, origin.PlanId, origin.Name, err,
				)
				return err
			}
		case beat.ConstBeatScheduleTaskOperatorRemove:
			if _, err := l.svcCtx.BeatRPC.RemovePeriodicTask(
				l.ctx, &beatpb.RemovePeriodicTaskReq{
					Name: l.generatePeriodicTaskName(origin.ProjectId, origin.PlanId),
				},
			); err != nil {
				l.Logger.Errorf(
					"failed to %s schedule task of api plan[%s, %s, %s], error: %+v",
					strings.ToLower(beatOperator), origin.ProjectId, origin.PlanId, origin.Name, err,
				)
				return err
			}
		}
	}

	return nil
}
