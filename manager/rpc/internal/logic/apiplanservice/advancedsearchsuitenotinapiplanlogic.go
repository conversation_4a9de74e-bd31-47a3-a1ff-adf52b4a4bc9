package apiplanservicelogic

import (
	"context"
	"math"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sqlbuilder/rpc"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type AdvancedSearchSuiteNotInApiPlanLogic struct {
	*BaseLogic
}

func NewAdvancedSearchSuiteNotInApiPlanLogic(ctx context.Context, svcCtx *svc.ServiceContext) *AdvancedSearchSuiteNotInApiPlanLogic {
	return &AdvancedSearchSuiteNotInApiPlanLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// AdvancedSearchSuiteNotInApiPlan 高级搜索不在当前测试计划中的测试集合
func (l *AdvancedSearchSuiteNotInApiPlanLogic) AdvancedSearchSuiteNotInApiPlan(in *pb.AdvancedSearchSuiteNotInApiPlanReq) (resp *pb.AdvancedSearchSuiteNotInApiPlanResp, err error) {
	resp = &pb.AdvancedSearchSuiteNotInApiPlanResp{}

	// validate the project_id in req
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, in.GetProjectId()); err != nil {
		return nil, err
	}

	// validate the plan_id in req
	if _, err = model.CheckApiPlanByPlanId(l.ctx, l.svcCtx.ApiPlanModel, in.GetProjectId(), in.GetPlanId()); err != nil {
		return nil, err
	}

	selectBuilder, countBuilder := l.svcCtx.ApiPlanModel.GenerateAdvancedSearchSuiteNotInApiPlanSqlBuilder2(model.AdvancedSearchSuiteNotInApiPlanReq{
		ProjectId:  in.GetProjectId(),
		PlanId:     in.GetPlanId(),
		SuiteType:  in.GetSuiteType(),
		Condition:  in.GetCondition(),
		Pagination: in.GetPagination(),
		Sort:       rpc.ConvertSortFields(in.GetSort()),
	})
	count, err := l.svcCtx.ApiPlanModel.FindCountAdvanceSearchSuiteNotInApiPlan(l.ctx, countBuilder)
	if err != nil {
		return nil, errors.Wrapf(errorx.Err(errorx.DBError, err.Error()), "failed to count interface suite in advanced search with project_id[%s], error: %+v", in.GetProjectId(), err)
	}

	resp.TotalCount = uint64(count)
	resp.TotalPage = 1

	suites, err := l.svcCtx.ApiPlanModel.FindAdvanceSearchSuiteNotInApiPlan(l.ctx, selectBuilder)
	if err != nil {
		return nil, errors.Wrapf(errorx.Err(errorx.DBError, err.Error()), "failed to find interface suite in advanced search with project_id[%s], error: %+v", in.GetProjectId(), err)
	}

	resp.Items = make([]*pb.AdvancedSearchSuiteItem, 0, len(suites))
	for _, suite := range suites {
		item := &pb.AdvancedSearchSuiteItem{}
		if err = utils.Copy(item, suite, l.converters...); err != nil {
			return nil, errors.Wrapf(errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy interface suite [%+v] to response, error: %+v", suite, err)
		}

		resp.Items = append(resp.Items, item)
	}

	pagination := in.GetPagination()
	if pagination != nil {
		resp.CurrentPage = pagination.GetCurrentPage()
		resp.PageSize = pagination.GetPageSize()
		resp.TotalPage = uint64(math.Ceil(float64(resp.TotalCount) / float64(resp.PageSize)))
	}

	return resp, nil
}
