package apiplanservicelogic

import (
	"context"
	"database/sql"

	"github.com/gorhill/cronexpr"
	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"
	"github.com/zeromicro/go-zero/core/lang"
	"github.com/zeromicro/go-zero/core/stores/sqlx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"
	beatcommon "gitlab.ttyuyin.com/TestDevelopment/qet-backend-middleware/beat/common"
	beatpb "gitlab.ttyuyin.com/TestDevelopment/qet-backend-middleware/beat/rpc/pb"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	commonpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type CreateApiPlanLogic struct {
	*BaseLogic
}

func NewCreateApiPlanLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CreateApiPlanLogic {
	return &CreateApiPlanLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// CreateApiPlan 创建API计划
func (l *CreateApiPlanLogic) CreateApiPlan(in *pb.CreateApiPlanReq) (resp *pb.CreateApiPlanResp, err error) {
	// validate the project_id in req
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, in.GetProjectId()); err != nil {
		return nil, err
	}

	// validate the cron_expression in req if plan type is SCHEDULE
	if in.GetType() == commonpb.TriggerMode_SCHEDULE {
		if _, err = cronexpr.Parse(in.GetCronExpression()); err != nil {
			return nil, errors.Wrapf(
				errorx.Err(errorx.CronExpressionParseError, err.Error()),
				"failed to parse the cron expression, cron: %s, error: %+v",
				in.GetCronExpression(), err,
			)
		}
	}

	// validate the general_config_id in req
	if in.GetGeneralConfigId() != "" {
		gc, err := model.CheckGeneralConfigByConfigId(
			l.ctx, l.svcCtx.GeneralConfigModel, in.GetProjectId(), in.GetGeneralConfigId(),
		)
		if err != nil {
			return nil, err
		}
		if gc.Type != string(common.ConstTestTypeAPI) {
			return nil, errorx.Errorf(
				errorx.ProhibitedBehavior,
				"cannot to choose a general configuration whose type is not %s for api plan, name: %s, type: %s",
				common.ConstTestTypeAPI, gc.Name, gc.Type,
			)
		}
	}

	// validate the account_config_ids in req
	for _, configId := range in.GetAccountConfigIds() {
		if _, err = model.CheckAccountConfigByConfigId(
			l.ctx, l.svcCtx.AccountConfigModel, in.GetProjectId(), configId,
		); err != nil {
			return nil, err
		}

		// TODO: 需要判断池账号配置是否满足需要
		//if len(in.SuiteIds) > 0 {
		//
		//}
	}

	apiPlan, err := l.create(in)
	if err != nil {
		return nil, err
	}

	resp = &pb.CreateApiPlanResp{Plan: &pb.ApiPlan{}}
	if err = utils.Copy(resp.Plan, apiPlan, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy api plan[%+v] to response, error: %+v",
			apiPlan, err,
		)
	}

	return resp, nil
}

func (l *CreateApiPlanLogic) create(req *pb.CreateApiPlanReq) (*model.ApiPlan, error) {
	planId, err := l.generatePlanId(req.GetProjectId())
	if err != nil {
		return nil, err
	}

	// 由于`protojson`只能序列化`[]proto.Message`，而`tags`字段类型为`[]string`，Go Struct跟PB没有大的区别，因此直接使用`jsonx`暂代`protojson`
	tags := jsonx.MarshalToStringIgnoreError(req.GetTags())

	maintainedBy := req.GetMaintainedBy()
	if maintainedBy == "" {
		// if the maintainer is not set, the creator is set as the maintainer
		maintainedBy = l.currentUser.Account
	}

	apiPlan := &model.ApiPlan{
		ProjectId:  req.GetProjectId(),
		CategoryId: req.GetCategoryId(),
		PlanId:     planId,
		Name:       req.GetName(),
		Description: sql.NullString{
			String: req.GetDescription(),
			Valid:  req.GetDescription() != "",
		},
		Priority: req.GetPriority(),
		Tags: sql.NullString{
			String: tags,
			Valid:  tags != "",
		},
		State:   int64(pb.CommonState_CS_ENABLE),
		Type:    req.GetType().String(),
		Purpose: req.GetPurpose().String(),
		CronExpression: sql.NullString{
			String: req.GetCronExpression(),
			Valid:  req.GetCronExpression() != "",
		},
		GeneralConfigId: sql.NullString{
			String: req.GetGeneralConfigId(),
			Valid:  req.GetGeneralConfigId() != "",
		},
		SuiteExecutionMode: int64(req.GetSuiteExecutionMode()),
		CaseExecutionMode:  int64(req.GetCaseExecutionMode()),
		MaintainedBy: sql.NullString{
			String: maintainedBy,
			Valid:  maintainedBy != "",
		},
		CreatedBy: l.currentUser.Account,
		UpdatedBy: l.currentUser.Account,
	}

	if err = l.svcCtx.ApiPlanModel.Trans(
		l.ctx, func(context context.Context, session sqlx.Session) error {
			if _, err := l.svcCtx.ApiPlanModel.InsertTX(context, session, apiPlan); err != nil {
				return errors.Wrapf(
					errorx.Err(errorx.DBError, err.Error()), "failed to insert table[%s] with values[%+v], error: %+v",
					l.svcCtx.ApiPlanModel.Table(), apiPlan, err,
				)
			}

			// crate the account configuration reference of api plan
			if err := l.createAccountConfigReference(
				context, session, createAccountConfigReferenceInternalReq{
					ProjectId:     apiPlan.ProjectId,
					ReferenceType: common.ConstReferenceTypeApiPlan,
					ReferenceId:   planId,
					ConfigIds:     req.GetAccountConfigIds(),
				},
			); err != nil {
				return err
			}

			// create the suite and case reference of api plan
			cache := make(types.StringPlaceholderCache)
			for _, suiteTypeId := range req.GetSuiteIds() {
				var (
					suiteType = suiteTypeId.GetSuiteType()
					suiteId   = suiteTypeId.GetSuiteId()
				)

				key := suiteType + ":" + suiteId
				if _, ok := cache[key]; ok {
					continue
				} else if suiteType != common.ConstReferenceTypeApiSuite && suiteType != common.ConstReferenceTypeInterfaceDocument {
					continue
				} else {
					cache[key] = lang.Placeholder
				}

				if err := l.createApiPlanReference2(
					context, session, CreateOrRemoveReference2InternalReq{
						ProjectId: apiPlan.ProjectId,
						References: []*CreateOrRemoveReference{{
							ReferenceType: suiteType,
							ReferenceId:   suiteId,
						}},
						PlanId:        planId,
						NoNeedToCheck: false,
					}, nil,
				); err != nil {
					return err
				}
			}

			// create the new tag and tag reference of api plan
			if err := l.createTagLogic.CreateTagAndReferenceForInternal(
				context, session, types.CreateOrUpdateTagReference{
					ProjectId:     apiPlan.ProjectId,
					ReferenceType: common.ConstReferenceTypeApiPlan,
					ReferenceId:   planId,
					Tags:          req.GetTags(),
				},
			); err != nil {
				return err
			}

			if apiPlan.Type == commonpb.TriggerMode_SCHEDULE.String() {
				if _, err := l.svcCtx.BeatRPC.CreateOrModifyPeriodicTask(
					l.ctx, &beatpb.CreateOrModifyPeriodicTaskReq{
						Name:  l.generatePeriodicTaskName(apiPlan.ProjectId, apiPlan.PlanId),
						Type:  constants.MQTaskTypeDispatcherPeriodicPlanTask,
						Queue: constants.MQNamePeriodicPlanTask,
						Spec:  apiPlan.CronExpression.String,
						Payload: protobuf.MarshalJSONToStringIgnoreError(
							&commonpb.PeriodicPlanTaskInfo{
								ProjectId:      apiPlan.ProjectId,
								PlanId:         apiPlan.PlanId,
								CronExpression: apiPlan.CronExpression.String,
								PlanType:       commonpb.PlanType_API,
							},
						),
						Version: beatcommon.V2,
					},
				); err != nil {
					l.Logger.Errorf(
						"failed to create schedule task of api plan[%s, %s, %s], error: %+v",
						apiPlan.ProjectId, apiPlan.PlanId, apiPlan.Name, err,
					)
					return err
				}
			}

			return nil
		},
	); err != nil {
		return nil, err
	}

	return apiPlan, nil
}
