package apiplanservicelogic

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type ViewApiPlanLogic struct {
	*BaseLogic
}

func NewViewApiPlanLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ViewApiPlanLogic {
	return &ViewApiPlanLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// ViewApiPlan 查看API计划
func (l *ViewApiPlanLogic) ViewApiPlan(in *pb.ViewApiPlanReq) (resp *pb.ViewApiPlanResp, err error) {
	// validate the project_id in req
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, in.GetProjectId()); err != nil {
		return nil, err
	}

	// validate the plan_id in req
	apiPlan, err := model.CheckApiPlanByPlanId(l.ctx, l.svcCtx.ApiPlanModel, in.GetProjectId(), in.GetPlanId())
	if err != nil {
		return nil, err
	}

	acs, err := l.svcCtx.ApiPlanModel.FindAccountConfigByPlanId(l.ctx, model.FindAccountConfigByPlanIdReq{
		ProjectId: in.GetProjectId(),
		PlanId:    in.GetPlanId(),
	})
	if err != nil {
		return nil, errors.Wrapf(errorx.Err(errorx.DBError, err.Error()), "failed to find account configurations of api plan with project_id[%s] and plan_id[%s], error: %+v", in.GetProjectId(), in.GetPlanId(), err)
	}

	resp = &pb.ViewApiPlanResp{Plan: &pb.ApiPlan{}}
	if err = utils.Copy(resp.Plan, apiPlan, l.converters...); err != nil {
		return nil, errors.Wrapf(errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy api plan[%+v] to response, error: %+v", apiPlan, err)
	}

	resp.Plan.AccountConfigIds = make([]string, 0, len(acs))
	for _, ac := range acs {
		resp.Plan.AccountConfigIds = append(resp.Plan.AccountConfigIds, ac.ConfigId)
	}

	return resp, nil
}
