package apiplanservicelogic

import (
	"context"
	"fmt"

	"github.com/zeromicro/go-zero/core/lang"
	"github.com/zeromicro/go-zero/core/stores/sqlx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/redislock"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type AddSuiteToApiPlanLogic struct {
	*BaseLogic
}

func NewAddSuiteToApiPlanLogic(ctx context.Context, svcCtx *svc.ServiceContext) *AddSuiteToApiPlanLogic {
	return &AddSuiteToApiPlanLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// AddSuiteToApiPlan 添加集合到API计划中
func (l *AddSuiteToApiPlanLogic) AddSuiteToApiPlan(in *pb.AddSuiteToApiPlanReq) (resp *pb.AddSuiteToApiPlanResp, err error) {
	// validate the project_id in req
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, in.GetProjectId()); err != nil {
		return nil, err
	}

	// validate the plan_id in req
	if _, err = model.CheckApiPlanByPlanId(l.ctx, l.svcCtx.ApiPlanModel, in.GetProjectId(), in.GetPlanId()); err != nil {
		return nil, err
	}

	// acquire redis lock
	key := fmt.Sprintf("%s:%s:%s", common.ConstLockApiPlanProjectIdPlanIdPrefix, in.GetProjectId(), in.GetPlanId())
	lock, err := redislock.NewRedisLockAndAcquire(l.svcCtx.Redis, key, redislock.WithExpire(common.ConstLockExpireTime))
	if err != nil {
		return nil, err
	}
	defer func() {
		// release redis lock
		e := lock.Release()
		if e != nil {
			// l.Logger.Error(e)
			if re, ok := errorx.RootError(e); ok && re.Code() != errorx.ReleaseRedisLockFailure && err == nil {
				err = e
			}
		}
	}()

	suiteTypeIds := in.GetSuiteIds()
	addMap := make(types.StringPlaceholderCache, len(suiteTypeIds))

	if err = l.svcCtx.ApiPlanReferenceModel.Trans(l.ctx, func(context context.Context, session sqlx.Session) error {
		for _, suiteTypeId := range suiteTypeIds {
			suiteType := suiteTypeId.GetSuiteType()
			// 忽略非API集合和接口集合的
			if suiteType != common.ConstReferenceTypeApiSuite && suiteType != common.ConstReferenceTypeInterfaceDocument {
				continue
			}

			k := suiteTypeId.Key()
			if _, ok := addMap[k]; ok {
				// 忽略重复导入的集合
				continue
			} else {
				addMap[k] = lang.Placeholder

				if err := l.createApiPlanReference2(context, session, CreateOrRemoveReference2InternalReq{
					ProjectId: in.GetProjectId(),
					References: []*CreateOrRemoveReference{{
						ReferenceType: suiteType,
						ReferenceId:   suiteTypeId.GetSuiteId(),
					}},
					PlanId:        in.GetPlanId(),
					NoNeedToCheck: false,
				}, nil); err != nil {
					return err
				}
			}
		}

		return nil
	}); err != nil {
		return nil, err
	}

	return &pb.AddSuiteToApiPlanResp{}, nil
}

func (l *AddSuiteToApiPlanLogic) AddReferenceToApiPlanForInternal(ctx context.Context, session sqlx.Session, req CreateOrRemoveReferenceInternalReq) error {
	if ctx == nil {
		ctx = l.ctx
	}

	// acquire redis lock
	key := fmt.Sprintf("%s:%s:%s", common.ConstLockApiPlanProjectIdPlanIdPrefix, req.ProjectId, req.PlanId)
	lock, err := redislock.NewRedisLockAndAcquire(l.svcCtx.Redis, key, redislock.WithExpire(common.ConstLockExpireTime))
	if err != nil {
		return err
	}
	defer func() {
		// release redis lock
		e := lock.Release()
		if e != nil {
			// l.Logger.Error(e)
			if re, ok := errorx.RootError(e); ok && re.Code() != errorx.ReleaseRedisLockFailure && err == nil {
				err = e
			}
		}
	}()

	return l.createApiPlanReference(ctx, session, req, nil)
}

func (l *AddSuiteToApiPlanLogic) AddReferenceToApiPlan2ForInternal(ctx context.Context, session sqlx.Session, req CreateOrRemoveReference2InternalReq) error {
	if ctx == nil {
		ctx = l.ctx
	}

	// acquire redis lock
	key := fmt.Sprintf("%s:%s:%s", common.ConstLockApiPlanProjectIdPlanIdPrefix, req.ProjectId, req.PlanId)
	lock, err := redislock.NewRedisLockAndAcquire(l.svcCtx.Redis, key, redislock.WithExpire(common.ConstLockExpireTime))
	if err != nil {
		return err
	}
	defer func() {
		// release redis lock
		e := lock.Release()
		if e != nil {
			// l.Logger.Error(e)
			if re, ok := errorx.RootError(e); ok && re.Code() != errorx.ReleaseRedisLockFailure && err == nil {
				err = e
			}
		}
	}()

	return l.createApiPlanReference2(ctx, session, req, nil)
}
