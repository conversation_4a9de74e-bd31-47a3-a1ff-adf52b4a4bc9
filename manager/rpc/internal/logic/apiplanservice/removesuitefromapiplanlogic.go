package apiplanservicelogic

import (
	"context"
	"fmt"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/lang"
	"github.com/zeromicro/go-zero/core/stores/sqlx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/redislock"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type RemoveSuiteFromApiPlanLogic struct {
	*BaseLogic
}

func NewRemoveSuiteFromApiPlanLogic(ctx context.Context, svcCtx *svc.ServiceContext) *RemoveSuiteFromApiPlanLogic {
	return &RemoveSuiteFromApiPlanLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// RemoveSuiteFromApiPlan 移除API计划中的集合
func (l *RemoveSuiteFromApiPlanLogic) RemoveSuiteFromApiPlan(in *pb.RemoveSuiteFromApiPlanReq) (
	resp *pb.RemoveSuiteFromApiPlanResp, err error,
) {
	// validate the project_id in req
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, in.GetProjectId()); err != nil {
		return nil, err
	}

	// validate the plan_id in req
	if _, err = model.CheckApiPlanByPlanId(
		l.ctx, l.svcCtx.ApiPlanModel, in.GetProjectId(), in.GetPlanId(),
	); err != nil {
		return nil, err
	}

	selectBuilder, _ := l.svcCtx.ApiPlanModel.GenerateSearchSuiteInApiPlanSqlBuilder(
		model.SearchSuiteInApiPlanReq{
			ApiSuiteModel:          l.svcCtx.ApiSuiteModel,
			InterfaceDocumentModel: l.svcCtx.InterfaceDocumentModel,
			ProjectId:              in.GetProjectId(),
			PlanId:                 in.GetPlanId(),
		},
	)
	suites, err := l.svcCtx.ApiPlanModel.FindSuitesInApiPlan(l.ctx, selectBuilder)
	if err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()),
			"failed to find suite in api plan with project_id[%s] and plan_id[%s], error: %+v", in.GetProjectId(),
			in.GetPlanId(), err,
		)
	}

	// 记录已在计划中的集合
	cache := make(types.StringPlaceholderCache, len(suites))
	for _, suite := range suites {
		k := suite.SuiteType + ":" + suite.SuiteId
		if _, ok := cache[k]; ok {
			continue
		} else {
			cache[k] = lang.Placeholder
		}
	}

	// acquire redis lock
	key := fmt.Sprintf("%s:%s:%s", common.ConstLockApiPlanProjectIdPlanIdPrefix, in.GetProjectId(), in.GetPlanId())
	lock, err := redislock.NewRedisLockAndAcquire(l.svcCtx.Redis, key, redislock.WithExpire(common.ConstLockExpireTime))
	if err != nil {
		return nil, err
	}
	defer func() {
		// release redis lock
		e := lock.Release()
		if e != nil {
			// l.Logger.Error(e)
			if re, ok := errorx.RootError(e); ok && re.Code() != errorx.ReleaseRedisLockFailure && err == nil {
				err = e
			}
		}
	}()

	if err = l.svcCtx.ApiPlanReferenceModel.Trans(
		l.ctx, func(context context.Context, session sqlx.Session) error {
			references := make([]*CreateOrRemoveReference, 0, len(in.GetSuiteIds()))

			// 不在计划中的集合无需移除
			for _, suiteTypeId := range in.GetSuiteIds() {
				var (
					k         = suiteTypeId.Key()
					suiteType = suiteTypeId.GetSuiteType()
					suiteId   = suiteTypeId.GetSuiteId()
				)

				if _, ok := cache[k]; !ok {
					continue
				} else if suiteType != common.ConstReferenceTypeApiSuite && suiteType != common.ConstReferenceTypeInterfaceDocument {
					continue
				}

				references = append(references, &CreateOrRemoveReference{
					ReferenceType: suiteType,
					ReferenceId:   suiteId,
				})
			}

			if err := l.removeApiPlanReference2(
				context, session, CreateOrRemoveReference2InternalReq{
					ProjectId:  in.GetProjectId(),
					References: references,
					PlanId:     in.GetPlanId(),
				},
			); err != nil {
				return err
			}

			return nil
		},
	); err != nil {
		return nil, err
	}

	return &pb.RemoveSuiteFromApiPlanResp{}, nil
}

func (l *RemoveSuiteFromApiPlanLogic) RemoveReferenceFromApiPlanForInternal(
	ctx context.Context, session sqlx.Session, req CreateOrRemoveReferenceInternalReq,
) error {
	if ctx == nil {
		ctx = l.ctx
	}

	// acquire redis lock
	key := fmt.Sprintf("%s:%s:%s", common.ConstLockApiPlanProjectIdPlanIdPrefix, req.ProjectId, req.PlanId)
	lock, err := redislock.NewRedisLockAndAcquire(l.svcCtx.Redis, key, redislock.WithExpire(common.ConstLockExpireTime))
	if err != nil {
		return err
	}
	defer func() {
		// release redis lock
		e := lock.Release()
		if e != nil {
			// l.Logger.Error(e)
			if re, ok := errorx.RootError(e); ok && re.Code() != errorx.ReleaseRedisLockFailure && err == nil {
				err = e
			}
		}
	}()

	return l.removeApiPlanReference(ctx, session, req)
}

func (l *RemoveSuiteFromApiPlanLogic) RemoveReferenceFromApiPlan2ForInternal(
	ctx context.Context, session sqlx.Session, req CreateOrRemoveReference2InternalReq,
) error {
	if ctx == nil {
		ctx = l.ctx
	}

	// acquire redis lock
	key := fmt.Sprintf("%s:%s:%s", common.ConstLockApiPlanProjectIdPlanIdPrefix, req.ProjectId, req.PlanId)
	lock, err := redislock.NewRedisLockAndAcquire(l.svcCtx.Redis, key, redislock.WithExpire(common.ConstLockExpireTime))
	if err != nil {
		return err
	}
	defer func() {
		// release redis lock
		e := lock.Release()
		if e != nil {
			// l.Logger.Error(e)
			if re, ok := errorx.RootError(e); ok && re.Code() != errorx.ReleaseRedisLockFailure && err == nil {
				err = e
			}
		}
	}()

	return l.removeApiPlanReference2(ctx, session, req)
}
