package apiplanservicelogic

import (
	"context"
	"math"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sqlbuilder/rpc"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type SearchSuiteInApiPlanLogic struct {
	*BaseLogic
}

func NewSearchSuiteInApiPlanLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SearchSuiteInApiPlanLogic {
	return &SearchSuiteInApiPlanLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// SearchSuiteInApiPlan 搜索API计划中的集合（包括：场景集合和接口集合）
func (l *SearchSuiteInApiPlanLogic) SearchSuiteInApiPlan(in *pb.SearchSuiteInApiPlanReq) (resp *pb.SearchSuiteInApiPlanResp, err error) {
	resp = &pb.SearchSuiteInApiPlanResp{}

	// validate the project_id in req
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, in.GetProjectId()); err != nil {
		return nil, err
	}

	// validate the plan_id in req
	if _, err = model.CheckApiPlanByPlanId(l.ctx, l.svcCtx.ApiPlanModel, in.GetProjectId(), in.GetPlanId()); err != nil {
		return nil, err
	}

	selectBuilder, countBuilder := l.svcCtx.ApiPlanModel.GenerateSearchSuiteInApiPlanSqlBuilder(model.SearchSuiteInApiPlanReq{
		ApiSuiteModel:          l.svcCtx.ApiSuiteModel,
		InterfaceDocumentModel: l.svcCtx.InterfaceDocumentModel,
		ProjectId:              in.GetProjectId(),
		PlanId:                 in.GetPlanId(),
		SuiteType:              in.GetSuiteType(),
		Condition:              in.GetCondition(),
		Pagination:             in.GetPagination(),
		Sort:                   rpc.ConvertSortFields(in.GetSort()),
	})

	count, err := l.svcCtx.ApiPlanModel.FindCountSuitesInApiPlan(l.ctx, countBuilder)
	if err != nil {
		return nil, errors.Wrapf(errorx.Err(errorx.DBError, err.Error()), "failed to count suite in api plan with project_id[%s], plan_id[%s] and suite_type[%s], error: %+v", in.GetProjectId(), in.GetPlanId(), in.GetSuiteType(), err)
	}
	resp.TotalCount = uint64(count)
	resp.TotalPage = 1

	suites, err := l.svcCtx.ApiPlanModel.FindSuitesInApiPlan(l.ctx, selectBuilder)
	if err != nil {
		return nil, errors.Wrapf(errorx.Err(errorx.DBError, err.Error()), "failed to find suite in api plan with project_id[%s], plan_id[%s] and suite_type[%s], error: %+v", in.GetProjectId(), in.GetPlanId(), in.GetSuiteType(), err)
	}

	resp.Items = make([]*pb.SearchSuiteInApiPlanItem, 0, len(suites))
	for _, suite := range suites {
		item := &pb.SearchSuiteInApiPlanItem{}
		if err = utils.Copy(item, suite, l.converters...); err != nil {
			return nil, errors.Wrapf(errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy api suite[%+v] to response, error: %+v", suite, err)
		}

		resp.Items = append(resp.Items, item)
	}

	pagination := in.GetPagination()
	if pagination != nil {
		resp.CurrentPage = pagination.GetCurrentPage()
		resp.PageSize = pagination.GetPageSize()
		resp.TotalPage = uint64(math.Ceil(float64(resp.TotalCount) / float64(resp.PageSize)))
	}

	return resp, nil
}
