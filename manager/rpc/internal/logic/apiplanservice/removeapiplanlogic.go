package apiplanservicelogic

import (
	"context"
	"fmt"
	"strings"

	"github.com/hashicorp/go-multierror"
	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/mr"
	"github.com/zeromicro/go-zero/core/stores/sqlx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/redislock"
	beatpb "gitlab.ttyuyin.com/TestDevelopment/qet-backend-middleware/beat/rpc/pb"

	commonpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common/zrpc/beat"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type RemoveApiPlanLogic struct {
	*BaseLogic
}

func NewRemoveApiPlanLogic(ctx context.Context, svcCtx *svc.ServiceContext) *RemoveApiPlanLogic {
	return &RemoveApiPlanLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// RemoveApiPlan 删除API计划
func (l *RemoveApiPlanLogic) RemoveApiPlan(in *pb.RemoveApiPlanReq) (resp *pb.RemoveApiPlanResp, err error) {
	// validate the project_id in req
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, in.GetProjectId()); err != nil {
		return nil, err
	}

	planIds := in.GetPlanIds()
	workers := len(planIds)
	if workers > common.ConstMRMaxWorkers {
		workers = common.ConstMRMaxWorkers
	}

	mr.ForEach[string](
		func(source chan<- string) {
			for _, planId := range planIds {
				source <- planId
			}
		}, func(item string) {
			if e := l.remove(in.GetProjectId(), item); e != nil {
				err = multierror.Append(err, e)
			}
		}, mr.WithContext(l.ctx), mr.WithWorkers(workers),
	)

	return &pb.RemoveApiPlanResp{}, err
}

func (l *RemoveApiPlanLogic) remove(projectId, planId string) (err error) {
	// validate the plan_id in req
	apiPlan, err := model.CheckApiPlanByPlanId(l.ctx, l.svcCtx.ApiPlanModel, projectId, planId)
	if err != nil {
		return err
	}

	// 【2022-12-14】注意：项目经理、产品经理、品控经理统一认为删除项目时无需做限制，用户若随意删除请勿咨询如何恢复

	// acquire redis lock
	key := fmt.Sprintf("%s:%s:%s", common.ConstLockApiPlanProjectIdPlanIdPrefix, projectId, planId)
	lock, err := redislock.NewRedisLockAndAcquire(l.svcCtx.Redis, key, redislock.WithExpire(common.ConstLockExpireTime))
	if err != nil {
		return err
	}
	defer func() {
		// release redis lock
		e := lock.Release()
		if e != nil {
			// l.Logger.Error(e)
			if re, ok := errorx.RootError(e); ok && re.Code() != errorx.ReleaseRedisLockFailure && err == nil {
				err = e
			}
		}
	}()

	return l.svcCtx.ApiPlanModel.Trans(
		l.ctx, func(context context.Context, session sqlx.Session) error {
			// Table: tag_reference_relationship
			if _, err := l.svcCtx.TagReferenceModel.RemoveByReferenceId(
				context, session, projectId, common.ConstReferenceTypeApiPlan, planId, "",
			); err != nil {
				return errors.Wrapf(
					errorx.Err(errorx.DBError, err.Error()),
					"failed to remove tag reference relationship with project_id[%s], reference_type[%s] and reference_id[%s], error: %+v",
					projectId, common.ConstReferenceTypeApiPlan, planId, err,
				)
			}

			// Table: account_configuration_reference_relationship
			if _, err := l.svcCtx.AccountConfigReferenceModel.RemoveByReferenceId(
				context, session, projectId, common.ConstReferenceTypeApiPlan, planId,
			); err != nil {
				return errors.Wrapf(
					errorx.Err(errorx.DBError, err.Error()),
					"failed to remove account configuration reference relationship with project_id[%s], reference_type[%s] and reference_id[%s], error: %+v",
					projectId, common.ConstReferenceTypeApiPlan, planId, err,
				)
			}

			// Table: api_plan_reference_relationship
			if _, err := l.svcCtx.ApiPlanReferenceModel.RemoveByPlanId(
				context, session, projectId, planId,
			); err != nil {
				return errors.Wrapf(
					errorx.Err(errorx.DBError, err.Error()),
					"failed to remove api plan reference relationship with project_id[%s] and plan_id[%s], error: %+v",
					projectId, planId, err,
				)
			}

			// Table: plan_user_like_relationship
			if err := l.svcCtx.PlanUserLikeRelationshipModel.DeleteByProjectIdAndPlanIdAndPlanType(
				context, session, projectId, planId, commonpb.PlanType_API,
			); err != nil {
				return errors.Wrapf(
					errorx.Err(errorx.DBError, err.Error()),
					"failed to remove plan user like relationship with project_id[%s] and plan_id[%s], error: %+v",
					projectId, planId, err,
				)
			}

			// Table: notify
			if _, err := l.svcCtx.NotifyModel.RemoveByPlanId(context, session, projectId, planId); err != nil {
				return errors.Wrapf(
					errorx.Err(errorx.DBError, err.Error()),
					"failed to remove notify with project_id[%s] and plan_id[%s], error: %+v",
					projectId, planId, err,
				)
			}

			// Table: api_plan
			if _, err := l.svcCtx.ApiPlanModel.RemoveByPlanId(context, session, projectId, planId); err != nil {
				return errors.Wrapf(
					errorx.Err(errorx.DBError, err.Error()),
					"failed to remove api plan with project_id[%s] and plan_id[%s], error: %+v",
					projectId, planId, err,
				)
			}

			return l.handleScheduleApiPlan(apiPlan)
		},
	)
}

func (l *RemoveApiPlanLogic) handleScheduleApiPlan(origin *model.ApiPlan) error {
	var beatOperator beat.ScheduleTaskOperator

	// stop schedule task
	if origin.Type == commonpb.TriggerMode_SCHEDULE.String() {
		beatOperator = beat.ConstBeatScheduleTaskOperatorRemove
	}

	if beatOperator != "" {
		// remove schedule task
		switch beatOperator {
		case beat.ConstBeatScheduleTaskOperatorRemove:
			if _, err := l.svcCtx.BeatRPC.RemovePeriodicTask(
				l.ctx, &beatpb.RemovePeriodicTaskReq{
					Name: l.generatePeriodicTaskName(origin.ProjectId, origin.PlanId),
				},
			); err != nil {
				l.Logger.Errorf(
					"failed to %s schedule task of api plan[%s, %s, %s], error: %+v", strings.ToLower(beatOperator),
					origin.ProjectId, origin.PlanId, origin.Name, err,
				)
				return err
			}
		}
	}

	return nil
}
