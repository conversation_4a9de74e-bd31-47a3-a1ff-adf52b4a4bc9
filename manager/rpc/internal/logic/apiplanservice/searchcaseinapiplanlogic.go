package apiplanservicelogic

import (
	"context"
	"math"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sqlbuilder/rpc"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type SearchCaseInApiPlanLogic struct {
	*BaseLogic
}

func NewSearchCaseInApiPlanLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SearchCaseInApiPlanLogic {
	return &SearchCaseInApiPlanLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// SearchCaseInApiPlan 搜索API计划中指定集合中的用例
func (l *SearchCaseInApiPlanLogic) SearchCaseInApiPlan(in *pb.SearchCaseInApiPlanReq) (resp *pb.SearchCaseInApiPlanResp, err error) {
	resp = &pb.SearchCaseInApiPlanResp{}

	// validate the project_id in req
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, in.GetProjectId()); err != nil {
		return nil, err
	}

	// validate the plan_id in req
	if _, err = model.CheckApiPlanByPlanId(l.ctx, l.svcCtx.ApiPlanModel, in.GetProjectId(), in.GetPlanId()); err != nil {
		return nil, err
	}

	// validate the suite_type and suite_id in req
	if in.GetSuiteType() == common.ConstReferenceTypeApiSuite {
		if _, err = model.CheckApiSuiteBySuiteId(l.ctx, l.svcCtx.ApiSuiteModel, in.GetProjectId(), in.GetSuiteId()); err != nil {
			return nil, err
		}
	} else {
		if _, err = model.CheckInterfaceDocumentByDocumentId(l.ctx, l.svcCtx.InterfaceDocumentModel, in.GetProjectId(), in.GetSuiteId()); err != nil {
			return nil, err
		}
	}

	selectBuilder, countBuilder := l.svcCtx.ApiPlanModel.GenerateSearchCaseInApiPlanSqlBuilder2(model.SearchCaseInApiPlanReq{
		ApiCaseModel:       l.svcCtx.ApiCaseModel,
		InterfaceCaseModel: l.svcCtx.InterfaceCaseModel,
		ProjectId:          in.GetProjectId(),
		PlanId:             in.GetPlanId(),
		SuiteType:          in.GetSuiteType(),
		SuiteId:            in.GetSuiteId(),
		Condition:          in.GetCondition(),
		Pagination:         in.GetPagination(),
		Sort:               rpc.ConvertSortFields(in.GetSort()),
	})

	count, err := l.svcCtx.ApiPlanModel.FindCountCasesInApiPlan(l.ctx, countBuilder)
	if err != nil {
		return nil, errors.Wrapf(errorx.Err(errorx.DBError, err.Error()), "failed to count case in api plan with project_id[%s], plan_id[%s], suite_type[%s] and suite_id[%s], error: %+v", in.GetProjectId(), in.GetPlanId(), in.GetSuiteType(), in.GetSuiteId(), err)
	}
	resp.TotalCount = uint64(count)
	resp.TotalPage = 1

	cases, err := l.svcCtx.ApiPlanModel.FindCasesInApiPlan(l.ctx, selectBuilder)
	if err != nil {
		return nil, errors.Wrapf(errorx.Err(errorx.DBError, err.Error()), "failed to find case in api plan with project_id[%s], plan_id[%s], suite_type[%s] and suite_id[%s], error: %+v", in.GetProjectId(), in.GetPlanId(), in.GetSuiteType(), in.GetSuiteId(), err)
	}

	resp.Items = make([]*pb.SearchCaseInApiPlanItem, 0, len(cases))
	for _, c := range cases {
		item := &pb.SearchCaseInApiPlanItem{}
		if err = utils.Copy(item, c, l.converters...); err != nil {
			return nil, errors.Wrapf(errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy case[%+v] to response, error: %+v", c, err)
		}

		resp.Items = append(resp.Items, item)
	}

	pagination := in.GetPagination()
	if pagination != nil {
		resp.CurrentPage = pagination.GetCurrentPage()
		resp.PageSize = pagination.GetPageSize()
		resp.TotalPage = uint64(math.Ceil(float64(resp.TotalCount) / float64(resp.PageSize)))
	}

	return resp, nil
}
