package apiplanservicelogic

import (
	"context"
	"fmt"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/redislock"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type ModifyApiPlanReferenceStateLogic struct {
	*BaseLogic
}

func NewModifyApiPlanReferenceStateLogic(
	ctx context.Context, svcCtx *svc.ServiceContext,
) *ModifyApiPlanReferenceStateLogic {
	return &ModifyApiPlanReferenceStateLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// ModifyApiPlanReferenceState 修改API计划执行数据的引用状态（包括：集合以及集合中的用例）
func (l *ModifyApiPlanReferenceStateLogic) ModifyApiPlanReferenceState(in *pb.ModifyApiPlanReferenceStateReq) (
	resp *pb.ModifyApiPlanReferenceStateResp, err error,
) {
	// validate the project_id in req
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, in.GetProjectId()); err != nil {
		return nil, err
	}

	// validate the plan_id in req
	if _, err = model.CheckApiPlanByPlanId(
		l.ctx, l.svcCtx.ApiPlanModel, in.GetProjectId(), in.GetPlanId(),
	); err != nil {
		return nil, err
	}

	var (
		suiteType = in.GetSuiteType()
		suiteId   = in.GetSuiteId()
		caseType  = in.GetCaseType()
		caseId    = in.GetCaseId()

		referenceParentId = ""
		referenceType     = suiteType
		referenceId       = suiteId
	)

	if caseId != "" {
		referenceParentId = suiteId
		referenceType = caseType
		referenceId = caseId
	}

	// if suiteType == common.ConstReferenceTypeApiSuite && caseId != "" {
	// 	referenceParentId = suiteId
	// 	referenceType = common.ConstReferenceTypeApiCase
	// 	referenceId = in.GetCaseId()

	// 	//_, err = model.CheckApiSuiteBySuiteId(l.ctx, l.svcCtx.ApiSuiteModel, in.GetProjectId(), in.GetSuiteId())
	// 	//if err == nil && in.GetCaseId() != "" {
	// 	//	_, err = model.CheckApiCaseByCaseId(l.ctx, l.svcCtx.ApiCaseModel, in.GetProjectId(), in.GetCaseId(), "")
	// 	//}
	// } else if suiteType == common.ConstReferenceTypeInterfaceDocument && caseId != "" {
	// 	referenceParentId = suiteId
	// 	referenceType = common.ConstReferenceTypeInterfaceCase
	// 	referenceId = caseId

	// 	//_, err = model.CheckInterfaceDocumentByDocumentId(l.ctx, l.svcCtx.InterfaceDocumentModel, in.GetProjectId(), in.GetSuiteId())
	// 	//if err == nil && in.GetCaseId() != "" {
	// 	//	_, err = model.CheckInterfaceCaseByCaseId(l.ctx, l.svcCtx.InterfaceCaseModel, in.GetProjectId(), in.GetSuiteId(), in.GetCaseId(), "")
	// 	//}
	// }

	// acquire redis lock
	key := fmt.Sprintf("%s:%s:%s", common.ConstLockApiPlanProjectIdPlanIdPrefix, in.GetProjectId(), in.GetPlanId())
	lock, err := redislock.NewRedisLockAndAcquire(l.svcCtx.Redis, key, redislock.WithExpire(common.ConstLockExpireTime))
	if err != nil {
		return nil, err
	}
	defer func() {
		// release redis lock
		e := lock.Release()
		if e != nil {
			// l.Logger.Error(e)
			if re, ok := errorx.RootError(e); ok && re.Code() != errorx.ReleaseRedisLockFailure && err == nil {
				err = e
			}
		}
	}()

	rr, err := l.svcCtx.ApiPlanReferenceModel.FindOneByPlanIdReference(
		l.ctx, in.GetProjectId(), in.GetPlanId(), referenceParentId, referenceType, referenceId,
	)
	if err != nil {
		if !errors.Is(err, model.ErrNotFound) {
			return nil, errors.Wrapf(
				errorx.Err(errorx.DBError, err.Error()),
				"failed to find api plan reference with project_id[%s], plan_id[%s], reference_parent_id[%s], reference_type[%s] and reference_id[%s], error: %+v",
				in.GetProjectId(), in.GetPlanId(), referenceParentId, referenceType, referenceId, err,
			)
		} else {
			return nil, errors.WithStack(
				errorx.Err(
					errorx.NotExists, fmt.Sprintf(
						"api plan reference with project_id[%s], plan_id[%s], reference_parent_id[%s], reference_type[%s] and reference_id[%s] doesn't exist",
						in.GetProjectId(), in.GetPlanId(), referenceParentId, referenceType, referenceId,
					),
				),
			)
		}
	}

	state := in.GetState()
	if rr.State != int64(state) {
		rr.State = int64(state)
		if _, err = l.svcCtx.ApiPlanReferenceModel.Update(l.ctx, nil, rr); err != nil {
			return nil, errors.Wrapf(
				errorx.Err(errorx.DBError, err.Error()), "failed to update table[%s] with values[%+v], error: %+v",
				l.svcCtx.ApiPlanReferenceModel.Table(), rr, err,
			)
		}
	}

	return &pb.ModifyApiPlanReferenceStateResp{}, nil
}
