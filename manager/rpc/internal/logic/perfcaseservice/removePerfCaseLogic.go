package perfcaseservicelogic

import (
	"context"
	"fmt"
	"os"

	"github.com/hashicorp/go-multierror"
	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/mr"
	"github.com/zeromicro/go-zero/core/stores/sqlx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/caller"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type RemovePerfCaseLogic struct {
	*BaseLogic
}

func NewRemovePerfCaseLogic(ctx context.Context, svcCtx *svc.ServiceContext) *RemovePerfCaseLogic {
	return &RemovePerfCaseLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// RemovePerfCase 删除压测用例
func (l *RemovePerfCaseLogic) RemovePerfCase(in *pb.RemovePerfCaseReq) (out *pb.RemovePerfCaseResp, err error) {
	// validate the project_id in req
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, in.GetProjectId()); err != nil {
		return nil, err
	}

	caseIDs := in.GetCaseIds()
	workers := len(caseIDs)
	if workers > common.ConstMRMaxWorkers {
		workers = common.ConstMRMaxWorkers
	}

	mr.ForEach[string](
		func(source chan<- string) {
			for _, caseID := range caseIDs {
				source <- caseID
			}
		}, func(item string) {
			if e := l.remove(in.GetProjectId(), item); e != nil {
				err = multierror.Append(err, e)
			}
		}, mr.WithContext(l.ctx), mr.WithWorkers(workers),
	)

	return &pb.RemovePerfCaseResp{}, err
}

func (l *RemovePerfCaseLogic) remove(projectID, caseID string) (err error) {
	// validate the case_id in req
	perfCase, err := model.CheckPerfCaseByCaseID(l.ctx, l.svcCtx.PerfCaseModel, projectID, caseID)
	if err != nil {
		if re, ok := errorx.RootError(err); ok && re.Code() == errorx.NotExists {
			return nil
		}

		return err
	}

	filename := perfCase.Name + perfCase.Extension

	rrs, err := l.svcCtx.PerfPlanReferenceModel.FindReferenceByReference(
		l.ctx, projectID, common.ConstReferenceTypePerfCase, caseID,
	)
	if err != nil {
		return errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()),
			"failed to find perf plan, project_id: %s, case_id: %s, error: %+v",
			projectID, caseID, err,
		)
	} else if len(rrs) > 0 {
		return errorx.Errorf(
			errorx.ProhibitedBehavior,
			"cannot to delete the perf case which has been associated with %d perf plan[s], please remove the perf case from perf plan first, project_id: %s, case_id: %s, filename: %s",
			len(rrs), projectID, caseID, filename,
		)
	}

	key := fmt.Sprintf(
		"%s:%s:%s", common.ConstLockPerfCaseProjectIDCaseIDPrefix, projectID, caseID,
	)
	fn := func() error {
		return l.svcCtx.PerfDataModel.Trans(
			l.ctx, func(context context.Context, session sqlx.Session) error {
				// Table: perf_case_step, physical deletion
				if _, err := l.svcCtx.PerfCaseStepModel.DeleteByCaseID(
					context, session, projectID, caseID,
				); err != nil {
					return errors.Wrapf(
						errorx.Err(errorx.DBError, err.Error()),
						"failed to remove perf case steps, project_id: %s, case_id: %s, filename: %s, error: %+v",
						projectID, caseID, filename, err,
					)
				}

				// Table: perf_case, physical deletion
				if err := l.svcCtx.PerfCaseModel.Delete(
					context, session, perfCase.Id,
				); err != nil {
					return errors.Wrapf(
						errorx.Err(errorx.DBError, err.Error()),
						"failed to remove perf case, project_id: %s, case_id: %s, filename: %s, error: %+v",
						projectID, caseID, filename, err,
					)
				}

				// remove the perf case file
				if err := os.Remove(perfCase.Path); err != nil {
					return errors.Wrapf(
						errorx.Err(errorx.FileOperationFailure, err.Error()),
						"failed to remove the perf case file, project_id: %s, case_id: %s, filename: %s, error: %+v",
						projectID, caseID, filename, err,
					)
				}

				return nil
			},
		)
	}
	return caller.LockDo(l.svcCtx.Redis, key, fn)
}
