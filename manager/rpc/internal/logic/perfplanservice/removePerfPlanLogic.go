package perfplanservicelogic

import (
	"context"
	"fmt"

	"github.com/hashicorp/go-multierror"
	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/mr"
	"github.com/zeromicro/go-zero/core/stores/sqlx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/caller"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	perfcase "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/logic/perfcaseservice"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type RemovePerfPlanLogic struct {
	*BaseLogic

	removeCaseLogic *perfcase.RemovePerfCaseLogic
}

func NewRemovePerfPlanLogic(ctx context.Context, svcCtx *svc.ServiceContext) *RemovePerfPlanLogic {
	return &RemovePerfPlanLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),

		removeCaseLogic: perfcase.NewRemovePerfCaseLogic(ctx, svcCtx),
	}
}

// RemovePerfPlan 删除压测计划
func (l *RemovePerfPlanLogic) RemovePerfPlan(in *pb.RemovePerfPlanReq) (out *pb.RemovePerfPlanResp, err error) {
	// validate the project_id in req
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, in.GetProjectId()); err != nil {
		return nil, err
	}

	planIDs := in.GetPlanIds()
	workers := len(planIDs)
	if workers > common.ConstMRMaxWorkers {
		workers = common.ConstMRMaxWorkers
	}

	mr.ForEach[string](
		func(source chan<- string) {
			for _, configID := range planIDs {
				source <- configID
			}
		}, func(item string) {
			if e := l.remove(in.GetProjectId(), item); e != nil {
				err = multierror.Append(err, e)
			}
		}, mr.WithContext(l.ctx), mr.WithWorkers(workers),
	)

	return &pb.RemovePerfPlanResp{}, err
}

func (l *RemovePerfPlanLogic) remove(projectID, planID string) (err error) {
	// validate the plan_id in req
	if _, err = model.CheckPerfPlanByPlanID(l.ctx, l.svcCtx.PerfPlanModel, projectID, planID); err != nil {
		return err
	}

	// remove the perf cases in the perf plan
	// since the deletion of the perf case involves the deletion of files,
	// therefore it is not handled in the database transaction
	cases, err := l.svcCtx.PerfPlanModel.FindCasesInPerfPlan(
		l.ctx, model.SearchCaseInPerfPlanReq{
			BaseSearchReq: model.BaseSearchReq{
				ProjectID: projectID,
			},
			PlanID:        planID,
			PerfCaseModel: l.svcCtx.PerfCaseModel,
		},
	)
	if err != nil {
		l.Errorf(
			"failed to find perf case in perf plan, project_id: %s, plan_id: %s, error: %+v",
			projectID, planID, err,
		)
	}

	key := fmt.Sprintf("%s:%s:%s", common.ConstLockPerfPlanProjectIDPlanIDPrefix, projectID, planID)
	fn := func() error {
		return l.svcCtx.PerfPlanModel.Trans(
			l.ctx, func(context context.Context, session sqlx.Session) error {
				// Table: tag_reference_relationship
				if _, err := l.svcCtx.TagReferenceModel.RemoveByReferenceId(
					context, session, projectID, common.ConstReferenceTypePerfPlan, planID, "",
				); err != nil {
					return errors.Wrapf(
						errorx.Err(errorx.DBError, err.Error()),
						"failed to remove tag reference relationship, project_id: %s, reference_type: %s, reference_id: %s, error: %+v",
						projectID, common.ConstReferenceTypePerfPlan, planID, err,
					)
				}

				// Table: perf_plan_reference_relationship
				if _, err := l.svcCtx.PerfPlanReferenceModel.RemoveByPlanID(
					context, session, projectID, planID,
				); err != nil {
					return errors.Wrapf(
						errorx.Err(errorx.DBError, err.Error()),
						"failed to remove perf plan reference relationship, project_id: %s, plan_id: %s, error: %+v",
						projectID, planID, err,
					)
				}

				// Table: notify
				if _, err := l.svcCtx.NotifyModel.RemoveByPlanId(context, session, projectID, planID); err != nil {
					return errors.Wrapf(
						errorx.Err(errorx.DBError, err.Error()),
						"failed to remove plan notify, project_id: %s, plan_id: %s, error: %+v",
						projectID, planID, err,
					)
				}

				// Table: perf_plan
				if _, err := l.svcCtx.PerfPlanModel.RemoveByPlanID(context, session, projectID, planID); err != nil {
					return errors.Wrapf(
						errorx.Err(errorx.DBError, err.Error()),
						"failed to remove perf plan, project_id: %s, plan_id: %s, error: %+v",
						projectID, planID, err,
					)
				}

				return nil
			},
		)
	}
	if err = caller.LockDo(l.svcCtx.Redis, key, fn); err != nil {
		return err
	}

	caseIDs := make([]string, 0, len(cases))
	for _, case_ := range cases {
		caseIDs = append(caseIDs, case_.CaseId)
	}
	if _, err = l.removeCaseLogic.RemovePerfCase(
		&pb.RemovePerfCaseReq{
			ProjectId: projectID,
			CaseIds:   caseIDs,
		},
	); err != nil {
		l.Error(err)
	}

	return nil
}
