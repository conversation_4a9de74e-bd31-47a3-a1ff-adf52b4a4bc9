package perfplanservicelogic

import (
	"context"
	"database/sql"
	"time"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"
	"github.com/zeromicro/go-zero/core/mr"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type CreatePerfPlanLogic struct {
	*BaseLogic
}

func NewCreatePerfPlanLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CreatePerfPlanLogic {
	return &CreatePerfPlanLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// CreatePerfPlan 创建压测计划
func (l *CreatePerfPlanLogic) CreatePerfPlan(in *pb.CreatePerfPlanReq) (out *pb.CreatePerfPlanResp, err error) {
	// validate the project_id in req
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, in.GetProjectId()); err != nil {
		return nil, err
	}

	// validate the protobuf_config_id in req
	if in.GetProtobufConfigId() != "" {
		if _, err = model.CheckProtobufConfigByConfigID(
			l.ctx, l.svcCtx.ProtobufConfigModel, in.GetProjectId(), in.GetProtobufConfigId(),
		); err != nil {
			return nil, err
		}
	}

	// validate the general_config_id in req
	if in.GetGeneralConfigId() != "" {
		if _, err = model.CheckGeneralConfigByConfigId(
			l.ctx, l.svcCtx.GeneralConfigModel, in.GetProjectId(), in.GetGeneralConfigId(),
		); err != nil {
			return nil, err
		}
	}

	// validate the account_config_id in req
	if in.GetAccountConfigId() != "" {
		if _, err = model.CheckAccountConfigByConfigId(
			l.ctx, l.svcCtx.AccountConfigModel, in.GetProjectId(), in.GetAccountConfigId(),
		); err != nil {
			return nil, err
		}
	}

	perfPlan, err := l.create(in)
	if err != nil {
		return nil, err
	}

	out = &pb.CreatePerfPlanResp{Plan: &pb.PerfPlan{}}
	if err = utils.Copy(out.Plan, perfPlan, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy perf plan to response, perf plan: %s, error: %+v",
			jsonx.MarshalIgnoreError(perfPlan), err,
		)
	}

	return out, nil
}

func (l *CreatePerfPlanLogic) create(req *pb.CreatePerfPlanReq) (*model.PerfPlan, error) {
	planID, err := l.generatePlanID(req.GetProjectId())
	if err != nil {
		return nil, err
	}

	// 由于`protojson`只能序列化`[]proto.Message`，而`tags`字段类型为`[]string`，Go Struct跟PB没有大的区别，因此直接使用`jsonx`暂代`protojson`
	tags := jsonx.MarshalToStringIgnoreError(req.GetTags())

	maintainedBy := req.GetMaintainedBy()
	if maintainedBy == "" {
		// if the maintainer is not set, the creator is set as the maintainer
		maintainedBy = l.currentUser.Account
	}

	now := time.Now()
	perfPlan := &model.PerfPlan{
		ProjectId: req.GetProjectId(),
		PlanId:    planID,
		Name:      req.GetName(),
		Description: sql.NullString{
			String: req.GetDescription(),
			Valid:  req.GetDescription() != "",
		},
		Type: req.GetType().String(),
		Tags: sql.NullString{
			String: tags,
			Valid:  tags != "",
		},
		Protocol: protobuf.GetEnumStringOf(req.GetProtocol()),
		ProtobufConfigId: sql.NullString{
			String: req.GetProtobufConfigId(),
			Valid:  req.GetProtobufConfigId() != "",
		},
		GeneralConfigId: sql.NullString{
			String: req.GetGeneralConfigId(),
			Valid:  req.GetGeneralConfigId() != "",
		},
		AccountConfigId: sql.NullString{
			String: req.GetAccountConfigId(),
			Valid:  req.GetAccountConfigId() != "",
		},
		Duration:  int64(req.GetDuration()),
		TargetEnv: protobuf.GetEnumStringOf(req.GetTargetEnv()),
		Keepalive: protobuf.MarshalJSONToStringIgnoreError(req.GetKeepalive()),
		Delay:     int64(req.GetDelay()),
		State:     int64(pb.CommonState_CS_ENABLE),
		MaintainedBy: sql.NullString{
			String: maintainedBy,
			Valid:  maintainedBy != "",
		},
		CreatedBy: l.currentUser.Account,
		UpdatedBy: l.currentUser.Account,
		CreatedAt: now,
		UpdatedAt: now,
	}

	if err = l.svcCtx.PerfPlanModel.Trans(
		l.ctx, func(context context.Context, session sqlx.Session) error {
			if _, err := l.svcCtx.PerfPlanModel.Insert(context, session, perfPlan); err != nil {
				return errors.Wrapf(
					errorx.Err(errorx.DBError, err.Error()),
					"failed to insert values to table, table: %s, values: %s, error: %+v",
					l.svcCtx.PerfPlanModel.Table(), jsonx.MarshalIgnoreError(perfPlan), err,
				)
			}

			// create the case reference of perf plan
			caseIDs := stringx.Distinct(req.GetCases())
			if err := mr.MapReduceVoid[string, *model.PerfPlanReferenceRelationship](
				func(source chan<- string) {
					for _, caseID := range caseIDs {
						source <- caseID
					}
				}, func(item string, writer mr.Writer[*model.PerfPlanReferenceRelationship], cancel func(error)) {
					var err error
					defer func() {
						if err != nil {
							cancel(err)
						}
					}()

					if _, err = model.CheckPerfCaseByCaseID(
						context, l.svcCtx.PerfCaseModel, req.GetProjectId(), item,
					); err != nil {
						return
					}

					writer.Write(
						&model.PerfPlanReferenceRelationship{
							ProjectId:     perfPlan.ProjectId,
							ReferenceType: common.ConstReferenceTypePerfCase,
							ReferenceId:   item,
							PlanId:        perfPlan.PlanId,
							State:         int64(pb.CommonState_CS_ENABLE),
							CreatedBy:     l.currentUser.Account,
							UpdatedBy:     l.currentUser.Account,
							CreatedAt:     now,
							UpdatedAt:     now,
						},
					)
				}, func(pipe <-chan *model.PerfPlanReferenceRelationship, cancel func(error)) {
					var err error
					defer func() {
						if err != nil {
							cancel(err)
						}
					}()

					items := make([]*model.PerfPlanReferenceRelationship, 0, len(caseIDs))
					for item := range pipe {
						items = append(items, item)
					}

					if _, err = l.svcCtx.PerfPlanReferenceModel.BatchInsert(context, session, items); err != nil {
						err = errors.Wrapf(
							errorx.Err(errorx.DBError, err.Error()),
							"failed to batch insert values to table, table: %s, values: %s, error: %+v",
							l.svcCtx.PerfPlanReferenceModel.Table(), jsonx.MarshalIgnoreError(items), err,
						)
						return
					}
				},
			); err != nil {
				return err
			}

			// create the new tag and tag reference of perf plan
			if err := l.createTagLogic.CreateTagAndReferenceForInternal(
				context, session, types.CreateOrUpdateTagReference{
					ProjectId:     perfPlan.ProjectId,
					ReferenceType: common.ConstReferenceTypePerfPlan,
					ReferenceId:   perfPlan.PlanId,
					Tags:          req.GetTags(),
				},
			); err != nil {
				return err
			}

			// create notify item of perf plan
			notifyItems := make([]*pb.CreateNotifyItem, 0, len(req.GetLarkChats()))
			for _, chat := range req.GetLarkChats() {
				notifyItems = append(
					notifyItems, &pb.CreateNotifyItem{
						ReceiverName: chat.GetName(),
						Receiver:     chat.GetChatId(),
					},
				)
			}
			if err := l.createNotifyLogic.CreatePlanNotifyForInternal(
				context, session, types.CreateOrUpdateNotifyReference{
					ProjectID:   perfPlan.ProjectId,
					PlanID:      perfPlan.PlanId,
					NotifyMode:  pb.NotifyMode_ALWAYS_NOTIFY,
					NotifyType:  pb.NotifyType_LARK_CHAT,
					NotifyItems: notifyItems,
				},
			); err != nil {
				return err
			}

			return nil
		},
	); err != nil {
		return nil, err
	}

	return perfPlan, nil
}
