package perfplanservicelogic

import (
	"context"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type ViewPerfPlanLogic struct {
	*BaseLogic
}

func NewViewPerfPlanLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ViewPerfPlanLogic {
	return &ViewPerfPlanLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// ViewPerfPlan 查看压测计划
func (l *ViewPerfPlanLogic) ViewPerfPlan(in *pb.ViewPerfPlanReq) (out *pb.ViewPerfPlanResp, err error) {
	// validate the project_id in req
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, in.GetProjectId()); err != nil {
		return nil, err
	}

	// validate the plan_id in req
	perfPlan, err := model.CheckPerfPlanByPlanID(l.ctx, l.svcCtx.PerfPlanModel, in.GetProjectId(), in.GetPlanId())
	if err != nil {
		return nil, err
	}

	out = &pb.ViewPerfPlanResp{Plan: &pb.PerfPlan{}}
	if err = utils.Copy(out.Plan, perfPlan, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy perf plan to response, perf plan: %s, error: %+v",
			jsonx.MarshalIgnoreError(perfPlan), err,
		)
	}

	return out, nil
}
