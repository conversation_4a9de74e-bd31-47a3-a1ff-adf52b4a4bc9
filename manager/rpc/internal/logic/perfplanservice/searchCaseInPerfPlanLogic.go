package perfplanservicelogic

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sqlbuilder/rpc"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type SearchCaseInPerfPlanLogic struct {
	*BaseLogic
}

func NewSearchCaseInPerfPlanLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SearchCaseInPerfPlanLogic {
	return &SearchCaseInPerfPlanLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// SearchCaseInPerfPlan 搜索压测计划中的压测用例
func (l *SearchCaseInPerfPlanLogic) SearchCaseInPerfPlan(in *pb.SearchCaseInPerfPlanReq) (
	out *pb.SearchCaseInPerfPlanResp, err error,
) {
	out = &pb.SearchCaseInPerfPlanResp{}

	// validate the project_id in req
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, in.GetProjectId()); err != nil {
		return nil, err
	}

	// validate the plan_id in req
	if _, err = model.CheckPerfPlanByPlanID(
		l.ctx, l.svcCtx.PerfPlanModel, in.GetProjectId(), in.GetPlanId(),
	); err != nil {
		return nil, err
	}

	req := model.SearchCaseInPerfPlanReq{
		BaseSearchReq: model.BaseSearchReq{
			ProjectID:  in.GetProjectId(),
			Condition:  in.GetCondition(),
			Pagination: in.GetPagination(),
			Sort:       rpc.ConvertSortFields(in.GetSort()),
		},
		PlanID:        in.GetPlanId(),
		PerfCaseModel: l.svcCtx.PerfCaseModel,
	}
	count, err := l.svcCtx.PerfPlanModel.FindCountCasesInPerfPlan(l.ctx, req)
	if err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()),
			"failed to count perf case in perf plan, project_id: %s, plan_id: %s, error: %+v",
			in.GetProjectId(), in.GetPlanId(), err,
		)
	}
	out.TotalCount = uint64(count)
	out.TotalPage = 1

	perfCases, err := l.svcCtx.PerfPlanModel.FindCasesInPerfPlan(l.ctx, req)
	if err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()),
			"failed to find perf case in perf plan, project_id: %s, plan_id: %s, error: %+v",
			in.GetProjectId(), in.GetPlanId(), err,
		)
	}

	out.Items = make([]*pb.SearchCaseInPerfPlanItem, 0, len(perfCases))
	for _, perfCase := range perfCases {
		item := &pb.SearchCaseInPerfPlanItem{}
		if err = utils.Copy(item, perfCase, l.converters...); err != nil {
			return nil, errors.Wrapf(
				errorx.Err(errorx.CopyToStructFailure, err.Error()),
				"failed to copy perf case to response, config: %s, error: %+v",
				protobuf.MarshalJSONIgnoreError(perfCase), err,
			)
		}

		out.Items = append(out.Items, item)
	}

	pagination := in.GetPagination()
	if pagination != nil {
		out.CurrentPage = pagination.GetCurrentPage()
		out.PageSize = pagination.GetPageSize()
		out.TotalPage = (out.TotalCount + out.PageSize - 1) / out.PageSize // round up
	}

	return out, nil
}
