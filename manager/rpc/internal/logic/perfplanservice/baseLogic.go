package perfplanservicelogic

import (
	"context"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/logx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/userinfo"
	commonutils "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	commonpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/utils"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/logic"
	notifyservicelogic "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/logic/notifyservice"
	tagservicelogic "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/logic/tagservice"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
)

type BaseLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext

	currentUser *userinfo.UserInfo

	createTagLogic    *tagservicelogic.CreateTagLogic
	createNotifyLogic *notifyservicelogic.CreatePlanNotifyLogic

	converters []commonutils.TypeConverter
}

func newBaseLogic(ctx context.Context, svcCtx *svc.ServiceContext) *BaseLogic {
	return &BaseLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,

		currentUser: userinfo.FromContext(ctx),

		createTagLogic:    tagservicelogic.NewCreateTagLogic(ctx, svcCtx),
		createNotifyLogic: notifyservicelogic.NewCreatePlanNotifyLogic(ctx, svcCtx),

		converters: []commonutils.TypeConverter{
			commonpb.StringToTriggerMode(),
			commonpb.StringToProtocol(),
			commonpb.StringToTargetEnvironment(),
			logic.Int64ToCommonState(),
			logic.SqlNullStringToTags(),
			logic.StringToPerfKeepalive(),
			logic.StringToStatsOfAPIs(),
		},
	}
}

func (l *BaseLogic) generatePlanID(projectID string) (string, error) {
	g := commonutils.NewUniqueIdGenerator(
		commonutils.WithGenerateFunc(utils.GenPerfPlanID), commonutils.WithIsUniqueFunc(
			func(id string) bool {
				r, err := l.svcCtx.PerfPlanModel.FindOneByProjectIdPlanId(l.ctx, projectID, id)
				if errors.Is(err, model.ErrNotFound) || r == nil {
					return true
				}
				return false
			},
		),
	)
	planID := g.Next()
	if planID == "" {
		return "", errors.WithStack(
			errorx.Err(
				errorx.GenerateUniqueIdFailure, "failed to generate perf plan id, please try it later",
			),
		)
	}

	return planID, nil
}
