package perfplanservicelogic

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sqlbuilder/rpc"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type SearchPerfPlanLogic struct {
	*BaseLogic
}

func NewSearchPerfPlanLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SearchPerfPlanLogic {
	return &SearchPerfPlanLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// SearchPerfPlan 搜索压测计划
func (l *SearchPerfPlanLogic) SearchPerfPlan(in *pb.SearchPerfPlanReq) (out *pb.SearchPerfPlanResp, err error) {
	out = &pb.SearchPerfPlanResp{}

	// validate the project_id in req
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, in.GetProjectId()); err != nil {
		return nil, err
	}

	req := model.SearchPerfPlanReq{
		BaseSearchReq: model.BaseSearchReq{
			ProjectID:  in.GetProjectId(),
			Condition:  in.GetCondition(),
			Pagination: in.GetPagination(),
			Sort:       rpc.ConvertSortFields(in.GetSort()),
		},
	}
	count, err := l.svcCtx.PerfPlanModel.FindCountByReq(l.ctx, req)
	if err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()),
			"failed to count perf plan, project_id: %s, error: %+v",
			in.GetProjectId(), err,
		)
	}
	out.TotalCount = uint64(count)
	out.TotalPage = 1

	perfPlans, err := l.svcCtx.PerfPlanModel.FindAllByReq(l.ctx, req)
	if err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()),
			"failed to find perf plan, project_id: %s, error: %+v",
			in.GetProjectId(), err,
		)
	}

	out.Items = make([]*pb.SearchPerfPlanItem, 0, len(perfPlans))
	for _, perfPlan := range perfPlans {
		item := &pb.SearchPerfPlanItem{}
		if err = utils.Copy(item, perfPlan, l.converters...); err != nil {
			return nil, errors.Wrapf(
				errorx.Err(errorx.CopyToStructFailure, err.Error()),
				"failed to copy perf plan to response, perf plan: %s, error: %+v",
				protobuf.MarshalJSONIgnoreError(perfPlan), err,
			)
		}

		out.Items = append(out.Items, item)
	}

	pagination := in.GetPagination()
	if pagination != nil {
		out.CurrentPage = pagination.GetCurrentPage()
		out.PageSize = pagination.GetPageSize()
		out.TotalPage = (out.TotalCount + out.PageSize - 1) / out.PageSize // round up
	}

	return out, nil
}
