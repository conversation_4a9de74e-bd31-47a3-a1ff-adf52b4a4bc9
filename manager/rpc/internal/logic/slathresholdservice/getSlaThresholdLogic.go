package slathresholdservicelogic

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"
)

type GetSlaThresholdLogic struct {
	*BaseLogic
}

func NewGetSlaThresholdLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetSlaThresholdLogic {
	return &GetSlaThresholdLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// GetSlaThreshold 获取SLA阈值配置
func (l *GetSlaThresholdLogic) GetSlaThreshold(in *pb.GetSlaThresholdReq) (out *pb.GetSlaThresholdResp, err error) {
	out = &pb.GetSlaThresholdResp{}

	// validate the project_id in req
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, in.GetProjectId()); err != nil {
		return nil, err
	}

	thresholds, err := l.svcCtx.SlaThresholdModel.FindAll(l.ctx, in.GetProjectId())
	if err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()), "failed to find SLA Thresholds by project_id: %s, error: %+v",
			in.GetProjectId(), err,
		)
	}

	out.Thresholds = make([]*pb.SlaThreshold, 0, len(thresholds))
	for _, threshold := range thresholds {
		item := &pb.SlaThreshold{}
		if err = utils.Copy(item, threshold, l.converters...); err != nil {
			return nil, errors.Wrapf(
				errorx.Err(errorx.CopyToStructFailure, err.Error()),
				"failed to copy model threshold[%+v] to rpc threshold, error: %+v", threshold, err,
			)
		}

		out.Thresholds = append(out.Thresholds, item)
	}

	return out, nil
}
