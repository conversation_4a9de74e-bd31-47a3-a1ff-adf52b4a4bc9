package slathresholdservicelogic

import (
	"context"
	"errors"
	"time"

	"github.com/zeromicro/go-zero/core/stores/sqlx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type ModifySlaThresholdLogic struct {
	*BaseLogic
}

func NewModifySlaThresholdLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ModifySlaThresholdLogic {
	return &ModifySlaThresholdLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// ModifySlaThreshold( 编辑SLA阈值配置
func (l *ModifySlaThresholdLogic) ModifySlaThreshold(in *pb.ModifySlaThresholdReq) (out *pb.ModifySlaThresholdResp, err error) {
	if err := l.svcCtx.SlaThresholdModel.Trans(
		l.ctx, func(ctx context.Context, session sqlx.Session) error {
			for _, threshold := range in.GetThresholds() {
				var (
					projectID    = threshold.GetProjectId()
					platformType = int64(threshold.GetPlatformType())
					branchType   = int64(threshold.GetBranchType())
					name         = threshold.GetName()
					unit         = threshold.GetUnit()
					value        = threshold.GetValue()
					now          = time.Now()
				)

				// validate the project_id in req
				if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, projectID); err != nil {
					return err
				}

				slaThreshold, err := l.svcCtx.SlaThresholdModel.FindOneByProjectIdPlatformTypeBranchTypeName(
					ctx, projectID, platformType, branchType, name,
				)
				if err != nil {
					if errors.Is(err, model.ErrNotFound) {
						if _, err = l.svcCtx.SlaThresholdModel.Insert(
							ctx, session, &model.SlaThreshold{
								ProjectId:    projectID,
								PlatformType: platformType,
								BranchType:   branchType,
								Name:         name,
								Unit:         unit,
								Value:        value,
								CreatedBy:    l.currentUser.Account,
								UpdatedBy:    l.currentUser.Account,
								CreatedAt:    now,
								UpdatedAt:    now,
							},
						); err != nil {
							return err
						}
						continue
					} else {
						return err
					}
				}

				slaThreshold.Unit = unit
				slaThreshold.Value = value
				slaThreshold.UpdatedBy = l.currentUser.Account
				slaThreshold.UpdatedAt = now
				if _, err = l.svcCtx.SlaThresholdModel.Update(ctx, session, slaThreshold); err != nil {
					return err
				}
			}

			return nil
		},
	); err != nil {
		return nil, err
	}

	return &pb.ModifySlaThresholdResp{}, nil
}
