package perflarkmemberservicelogic

import (
	"context"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/stores/sqlx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
)

type RemovePerfLarkMemberLogic struct {
	*BaseLogic
}

func NewRemovePerfLarkMemberLogic(ctx context.Context, svcCtx *svc.ServiceContext) *RemovePerfLarkMemberLogic {
	return &RemovePerfLarkMemberLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// RemovePerfLarkMember 删除飞书自动拉群成员
func (l *RemovePerfLarkMemberLogic) RemovePerfLarkMember(in *pb.RemovePerfLarkMemberReq) (out *pb.RemovePerfLarkMemberResp, err error) {
	var (
		projectId = in.GetProjectId()
		account   = in.GetAccount()
	)

	// validate the project_id in req
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, projectId); err != nil {
		return nil, err
	}

	if err = l.remove(projectId, account); err != nil {
		return nil, err
	}

	return &pb.RemovePerfLarkMemberResp{}, nil
}

func (l *RemovePerfLarkMemberLogic) remove(projectId, account string) error {
	member, err := model.CheckPerfLarkMemberByAccount(l.ctx, l.svcCtx.PerfLarkMemberModel, projectId, account)
	if err != nil {
		return err
	}

	return l.svcCtx.PerfLarkMemberModel.Trans(
		l.ctx, func(context context.Context, session sqlx.Session) error {
			if err := l.svcCtx.PerfLarkMemberModel.Delete(context, session, member.Id); err != nil {
				return errors.Wrapf(
					errorx.Err(errorx.DBError, err.Error()),
					"failed to remove perf lark member, project_id: %s, account: %s, error: %+v",
					projectId, account, err,
				)
			}
			return nil
		},
	)
}
