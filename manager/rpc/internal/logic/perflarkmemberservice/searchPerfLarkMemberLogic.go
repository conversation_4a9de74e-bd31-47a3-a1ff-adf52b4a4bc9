package perflarkmemberservicelogic

import (
	"context"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"
	pb1 "gitlab.ttyuyin.com/TestDevelopment/qet-backend-middleware/user/rpc/pb"
)

type SearchPerfLarkMemberLogic struct {
	*BaseLogic
}

func NewSearchPerfLarkMemberLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SearchPerfLarkMemberLogic {
	return &SearchPerfLarkMemberLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// SearchPerfLarkMember 搜索飞书自动拉群成员
func (l *SearchPerfLarkMemberLogic) SearchPerfLarkMember(in *pb.SearchPerfLarkMemberReq) (out *pb.SearchPerfLarkMemberResp, err error) {
	projectId := in.GetProjectId()

	// validate the project_id in req
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, projectId); err != nil {
		return nil, err
	}

	out = &pb.SearchPerfLarkMemberResp{}
	count, err := l.svcCtx.PerfLarkMemberModel.FindCount(l.ctx,
		l.svcCtx.PerfLarkMemberModel.SelectCountBuilder().Where("`project_id` = ?", projectId))
	if err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()),
			"failed to count perf lark member, project_id: %s, error: %+v",
			projectId, err,
		)
	}
	out.TotalCount = uint64(count)
	out.TotalPage = 1

	members, err := l.svcCtx.PerfLarkMemberModel.FindNoCacheByQuery(l.ctx,
		l.svcCtx.PerfLarkMemberModel.SelectBuilder().Where("`project_id` = ?", projectId))
	if err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()),
			"failed to find perf lark member, project_id: %s, error: %+v",
			projectId, err,
		)
	}

	out.Items = make([]*pb1.UserInfo, 0, len(members))
	for _, member := range members {
		item := &pb1.UserInfo{}
		if err = utils.Copy(item, member, l.converters...); err != nil {
			return nil, errors.Wrapf(
				errorx.Err(errorx.CopyToStructFailure, err.Error()),
				"failed to copy perf lark member to response, perf lark member: %s, error: %+v",
				jsonx.MarshalIgnoreError(member), err,
			)
		}
		if member.LarkUserId.Valid {
			item.LarkId = member.LarkUserId.String
		}
		out.Items = append(out.Items, item)
	}

	return out, nil
}
