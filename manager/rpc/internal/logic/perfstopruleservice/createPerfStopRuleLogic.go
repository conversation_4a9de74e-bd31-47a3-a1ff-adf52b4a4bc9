package perfstopruleservicelogic

import (
	"context"
	"database/sql"
	"fmt"
	"time"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"
	"github.com/zeromicro/go-zero/core/stores/sqlx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/caller"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type CreatePerfStopRuleLogic struct {
	*BaseLogic
}

func NewCreatePerfStopRuleLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CreatePerfStopRuleLogic {
	return &CreatePerfStopRuleLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// CreatePerfStopRule 创建压测停止规则
func (l *CreatePerfStopRuleLogic) CreatePerfStopRule(in *pb.CreatePerfStopRuleReq) (
	out *pb.CreatePerfStopRuleResp, err error,
) {
	var (
		projectID  = in.GetProjectId()
		metricType = protobuf.GetEnumStringOf(in.GetMetricType())
	)

	// validate the project_id in req
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, projectID); err != nil {
		return nil, err
	}

	if err = validateByMetricType(in.GetMetricType(), in.GetThreshold(), in.GetDuration()); err != nil {
		return nil, err
	}

	var perfStopRule *model.PerfStopRule
	key := fmt.Sprintf("%s:%s:%s", common.ConstLockPerfStopRuleProjectIDMetricTypePrefix, projectID, metricType)
	if err = caller.LockDo(
		l.svcCtx.Redis, key, func() error {
			perfStopRule, err = l.create(in)
			return err
		},
	); err != nil {
		return nil, err
	}

	out = &pb.CreatePerfStopRuleResp{Rule: &pb.PerfStopRule{}}
	if err = utils.Copy(out.Rule, perfStopRule, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy perf stop rule to response, perf stop rule: %s, error: %+v",
			jsonx.MarshalIgnoreError(perfStopRule), err,
		)
	}

	return out, nil
}

func (l *CreatePerfStopRuleLogic) create(req *pb.CreatePerfStopRuleReq) (*model.PerfStopRule, error) {
	ruleID, err := l.generateRuleID(req.GetProjectId())
	if err != nil {
		return nil, err
	}

	now := time.Now()
	perfStopRule := &model.PerfStopRule{
		ProjectId: req.GetProjectId(),
		RuleId:    ruleID,
		Name:      req.GetName(),
		Description: sql.NullString{
			String: req.GetDescription(),
			Valid:  req.GetDescription() != "",
		},
		MetricType: protobuf.GetEnumStringOf(req.GetMetricType()),
		Threshold:  float64(req.GetThreshold()),
		Duration:   int64(req.GetDuration()),
		State:      int64(pb.CommonState_CS_ENABLE),
		CreatedBy:  l.currentUser.Account,
		UpdatedBy:  l.currentUser.Account,
		CreatedAt:  now,
		UpdatedAt:  now,
	}

	if err = l.svcCtx.PerfStopRuleModel.Trans(
		l.ctx, func(context context.Context, session sqlx.Session) error {
			if _, err = l.svcCtx.PerfStopRuleModel.Insert(context, session, perfStopRule); err != nil {
				return errors.Wrapf(
					errorx.Err(errorx.DBError, err.Error()),
					"failed to insert values to table, table: %s, values: %s, error: %+v",
					l.svcCtx.PerfStopRuleModel.Table(), jsonx.MarshalIgnoreError(perfStopRule), err,
				)
			}

			return nil
		},
	); err != nil {
		return nil, err
	}

	return perfStopRule, nil
}
