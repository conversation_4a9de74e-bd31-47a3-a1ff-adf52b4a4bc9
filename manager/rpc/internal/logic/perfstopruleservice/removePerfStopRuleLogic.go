package perfstopruleservicelogic

import (
	"context"
	"fmt"

	"github.com/hashicorp/go-multierror"
	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/mr"
	"github.com/zeromicro/go-zero/core/stores/sqlx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/caller"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type RemovePerfStopRuleLogic struct {
	*BaseLogic
}

func NewRemovePerfStopRuleLogic(ctx context.Context, svcCtx *svc.ServiceContext) *RemovePerfStopRuleLogic {
	return &RemovePerfStopRuleLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// RemovePerfStopRule 删除压测停止规则
func (l *RemovePerfStopRuleLogic) RemovePerfStopRule(in *pb.RemovePerfStopRuleReq) (
	out *pb.RemovePerfStopRuleResp, err error,
) {
	// validate the project_id in req
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, in.GetProjectId()); err != nil {
		return nil, err
	}

	ruleIDs := in.GetRuleIds()
	workers := len(ruleIDs)
	if workers > common.ConstMRMaxWorkers {
		workers = common.ConstMRMaxWorkers
	}

	mr.ForEach[string](
		func(source chan<- string) {
			for _, ruleID := range ruleIDs {
				source <- ruleID
			}
		}, func(item string) {
			if e := l.remove(in.GetProjectId(), item); e != nil {
				err = multierror.Append(err, e)
			}
		}, mr.WithContext(l.ctx), mr.WithWorkers(workers),
	)

	return &pb.RemovePerfStopRuleResp{}, nil
}

func (l *RemovePerfStopRuleLogic) remove(projectID, ruleID string) (err error) {
	// validate the rule_id in req
	rule, err := model.CheckPerfStopRuleByRuleID(l.ctx, l.svcCtx.PerfStopRuleModel, projectID, ruleID)
	if err != nil {
		return err
	}

	rrs, err := l.svcCtx.PerfPlanRuleModel.FindByRuleID(l.ctx, projectID, ruleID)
	if err != nil {
		return errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()),
			"failed to find perf stop rule by rule id, project_id: %s, rule_id: %s, error: %+v",
			projectID, ruleID, err,
		)
	} else if len(rrs) > 0 {
		return errorx.Errorf(
			errorx.ProhibitedBehavior,
			"cannot to delete the perf stop rule which has been associated with %d perf plan[s], please remove the perf stop rule from perf plan first, project_id: %s, rule_id: %s",
			len(rrs), projectID, ruleID,
		)
	}

	key := fmt.Sprintf("%s:%s:%s", common.ConstLockPerfStopRuleProjectIDRuleIDPrefix, projectID, ruleID)
	fn := func() error {
		return l.svcCtx.PerfStopRuleModel.Trans(
			l.ctx, func(context context.Context, session sqlx.Session) error {
				// Table: perf_stop_rule, physical deletion
				if err := l.svcCtx.PerfStopRuleModel.Delete(
					context, session, rule.Id,
				); err != nil {
					return errors.Wrapf(
						errorx.Err(errorx.DBError, err.Error()),
						"failed to remove perf stop rule, project_id: %s, rule_id: %s, error: %+v",
						projectID, ruleID, err,
					)
				}

				return nil
			},
		)
	}
	return caller.LockDo(l.svcCtx.Redis, key, fn)
}
