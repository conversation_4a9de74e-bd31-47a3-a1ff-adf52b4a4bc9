package perfstopruleservicelogic

import (
	"context"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type ViewPerfStopRuleLogic struct {
	*BaseLogic
}

func NewViewPerfStopRuleLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ViewPerfStopRuleLogic {
	return &ViewPerfStopRuleLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// ViewPerfStopRule 查看压测停止规则
func (l *ViewPerfStopRuleLogic) ViewPerfStopRule(in *pb.ViewPerfStopRuleReq) (out *pb.ViewPerfStopRuleResp, err error) {
	// validate the project_id in req
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, in.GetProjectId()); err != nil {
		return nil, err
	}

	// validate the rule_id in req
	perfStopRule, err := model.CheckPerfStopRuleByRuleID(
		l.ctx, l.svcCtx.PerfStopRuleModel, in.GetProjectId(), in.GetRuleId(),
	)
	if err != nil {
		return nil, err
	}

	out = &pb.ViewPerfStopRuleResp{Rule: &pb.PerfStopRule{}}
	if err = utils.Copy(out.Rule, perfStopRule, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy perf stop rule to response, perf stop rule: %s, error: %+v",
			jsonx.MarshalIgnoreError(perfStopRule), err,
		)
	}

	return out, nil
}
