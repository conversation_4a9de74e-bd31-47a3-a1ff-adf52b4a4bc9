package perfstopruleservicelogic

import (
	"context"
	"database/sql"
	"fmt"
	"time"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"
	"github.com/zeromicro/go-zero/core/stores/sqlx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/caller"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type ModifyPerfStopRuleLogic struct {
	*BaseLogic
}

func NewModifyPerfStopRuleLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ModifyPerfStopRuleLogic {
	return &ModifyPerfStopRuleLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// ModifyPerfStopRule 编辑压测停止规则
func (l *ModifyPerfStopRuleLogic) ModifyPerfStopRule(in *pb.ModifyPerfStopRuleReq) (
	out *pb.ModifyPerfStopRuleResp, err error,
) {
	var (
		projectID  = in.GetProjectId()
		ruleID     = in.GetRuleId()
		metricType = protobuf.GetEnumStringOf(in.GetMetricType())
	)

	// validate the project_id in req
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, projectID); err != nil {
		return nil, err
	}

	// validate the rule_id in req
	origin, err := model.CheckPerfStopRuleByRuleID(l.ctx, l.svcCtx.PerfStopRuleModel, projectID, ruleID)
	if err != nil {
		return nil, err
	}

	if metricType != origin.MetricType {
		rrs, err := l.svcCtx.PerfPlanRuleModel.FindByRuleID(l.ctx, projectID, ruleID)
		if err != nil && !errors.Is(err, model.ErrNotFound) {
			return nil, errors.Wrapf(
				errorx.Err(errorx.DBError, err.Error()),
				"failed to find perf stop rule, project_id: %s, rule_id: %s",
				projectID, ruleID,
			)
		}
		if len(rrs) > 0 {
			return nil, errorx.Errorf(
				errorx.ProhibitedBehavior,
				"cannot to modify the metric type of perf stop rule which has been associated with %d perf plan[s], please remove the perf stop rule from perf plan first, project_id: %s, rule_id: %s, metric_type: %s -> %s",
				len(rrs), projectID, ruleID, origin.MetricType, metricType,
			)
		}
	}

	if err = validateByMetricType(in.GetMetricType(), in.GetThreshold(), in.GetDuration()); err != nil {
		return nil, err
	}

	var perfStopRule *model.PerfStopRule
	key1 := fmt.Sprintf("%s:%s:%s", common.ConstLockPerfStopRuleProjectIDMetricTypePrefix, projectID, metricType)
	fn := func() error {
		key2 := fmt.Sprintf("%s:%s:%s", common.ConstLockPerfStopRuleProjectIDRuleIDPrefix, projectID, ruleID)
		return caller.LockDo(
			l.svcCtx.Redis, key2, func() error {
				perfStopRule, err = l.modify(in, origin)
				return err
			},
		)
	}
	if err = caller.LockDo(l.svcCtx.Redis, key1, fn); err != nil {
		return nil, err
	}

	out = &pb.ModifyPerfStopRuleResp{Rule: &pb.PerfStopRule{}}
	if err = utils.Copy(out.Rule, perfStopRule, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy perf stop rule to response, perf plan: %s, error: %+v",
			jsonx.MarshalIgnoreError(perfStopRule), err,
		)
	}

	return out, nil
}

func (l *ModifyPerfStopRuleLogic) modify(
	req *pb.ModifyPerfStopRuleReq, origin *model.PerfStopRule,
) (*model.PerfStopRule, error) {
	now := time.Now()
	perfStopRule := &model.PerfStopRule{
		Id:        origin.Id,
		ProjectId: origin.ProjectId,
		RuleId:    origin.RuleId,
		Name:      req.GetName(),
		Description: sql.NullString{
			String: req.GetDescription(),
			Valid:  req.GetDescription() != "",
		},
		MetricType: protobuf.GetEnumStringOf(req.GetMetricType()),
		Threshold:  float64(req.GetThreshold()),
		Duration:   int64(req.GetDuration()),
		State:      int64(req.GetState()),
		CreatedBy:  origin.CreatedBy,
		UpdatedBy:  l.currentUser.Account,
		CreatedAt:  origin.CreatedAt,
		UpdatedAt:  now,
	}

	if err := l.svcCtx.PerfStopRuleModel.Trans(
		l.ctx, func(context context.Context, session sqlx.Session) error {
			if _, err := l.svcCtx.PerfStopRuleModel.Update(context, session, perfStopRule); err != nil {
				return errors.Wrapf(
					errorx.Err(errorx.DBError, err.Error()),
					"failed to update values to table, table: %s, values: %s, error: %+v",
					l.svcCtx.PerfStopRuleModel.Table(), jsonx.MarshalIgnoreError(perfStopRule), err,
				)
			}

			return nil
		},
	); err != nil {
		return nil, err
	}

	return perfStopRule, nil
}
