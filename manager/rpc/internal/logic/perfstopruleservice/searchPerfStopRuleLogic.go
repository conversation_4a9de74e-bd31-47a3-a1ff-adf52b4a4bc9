package perfstopruleservicelogic

import (
	"context"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sqlbuilder/rpc"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type SearchPerfStopRuleLogic struct {
	*BaseLogic
}

func NewSearchPerfStopRuleLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SearchPerfStopRuleLogic {
	return &SearchPerfStopRuleLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// SearchPerfStopRule 搜索压测停止规则
func (l *SearchPerfStopRuleLogic) SearchPerfStopRule(in *pb.SearchPerfStopRuleReq) (
	out *pb.SearchPerfStopRuleResp, err error,
) {
	out = &pb.SearchPerfStopRuleResp{}

	// validate the project_id in req
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, in.GetProjectId()); err != nil {
		return nil, err
	}

	req := model.SearchPerfStopRuleReq{
		BaseSearchReq: model.BaseSearchReq{
			ProjectID:  in.GetProjectId(),
			Condition:  in.GetCondition(),
			Pagination: in.GetPagination(),
			Sort:       rpc.ConvertSortFields(in.GetSort()),
		},
	}
	count, err := l.svcCtx.PerfStopRuleModel.FindCountByReq(l.ctx, req)
	if err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()),
			"failed to count perf stop rule, project_id: %s, error: %+v",
			in.GetProjectId(), err,
		)
	}
	out.TotalCount = uint64(count)
	out.TotalPage = 1

	perfStopRules, err := l.svcCtx.PerfStopRuleModel.FindAllByReq(l.ctx, req)
	if err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()),
			"failed to find perf stop rule, project_id: %s, error: %+v",
			in.GetProjectId(), err,
		)
	}

	out.Items = make([]*pb.PerfStopRule, 0, len(perfStopRules))
	for _, perfStopRule := range perfStopRules {
		item := &pb.PerfStopRule{}
		if err = utils.Copy(item, perfStopRule, l.converters...); err != nil {
			return nil, errors.Wrapf(
				errorx.Err(errorx.CopyToStructFailure, err.Error()),
				"failed to copy perf strop rule to response, perf stop rule: %s, error: %+v",
				jsonx.MarshalIgnoreError(perfStopRule), err,
			)
		}

		out.Items = append(out.Items, item)
	}

	pagination := in.GetPagination()
	if pagination != nil {
		out.CurrentPage = pagination.GetCurrentPage()
		out.PageSize = pagination.GetPageSize()
		out.TotalPage = (out.TotalCount + out.PageSize - 1) / out.PageSize // round up
	}

	return out, nil
}
