package perfstopruleservicelogic

import (
	"context"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/logx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/userinfo"
	qetutils "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	commonpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/utils"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
)

type BaseLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext

	currentUser *userinfo.UserInfo

	converters []qetutils.TypeConverter
}

func newBaseLogic(ctx context.Context, svcCtx *svc.ServiceContext) *BaseLogic {
	return &BaseLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,

		currentUser: userinfo.FromContext(ctx),

		converters: []qetutils.TypeConverter{
			commonpb.StringToMetricType(),
			logic.Int64ToCommonState(),
		},
	}
}

func (l *BaseLogic) generateRuleID(projectID string) (string, error) {
	g := qetutils.NewUniqueIdGenerator(
		qetutils.WithGenerateFunc(utils.GenPerfStopRuleID), qetutils.WithIsUniqueFunc(
			func(id string) bool {
				r, err := l.svcCtx.PerfStopRuleModel.FindOneByProjectIdRuleId(l.ctx, projectID, id)
				if errors.Is(err, model.ErrNotFound) || r == nil {
					return true
				}
				return false
			},
		),
	)
	ruleID := g.Next()
	if ruleID == "" {
		return "", errorx.Err(
			errorx.GenerateUniqueIdFailure, "failed to generate perf stop rule id, please try it later",
		)
	}

	return ruleID, nil
}
