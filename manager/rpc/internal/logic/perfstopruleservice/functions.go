package perfstopruleservicelogic

import (
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"

	commonpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
)

func validateByMetricType(metricType commonpb.MetricType, threshold float64, duration uint32) error {
	switch metricType {
	case commonpb.MetricType_MetricType_QPS,
		commonpb.MetricType_MetricType_P99,
		commonpb.MetricType_MetricType_P95,
		commonpb.MetricType_MetricType_P90,
		commonpb.MetricType_MetricType_P75,
		commonpb.MetricType_MetricType_P50:
		if threshold < 0 {
			return errorx.Errorf(
				errorx.ValidateParamError,
				"the threshold must be greater than 0, metric_type: %s, threshold: %f",
				protobuf.GetEnumStringOf(metricType), threshold,
			)
		}
	case commonpb.MetricType_MetricType_FailRatio:
		if threshold < 0 || threshold > 100 {
			return errorx.Errorf(
				errorx.ValidateParamError,
				"the threshold must be in [0, 100], metric_type: %s, threshold: %.2f",
				protobuf.GetEnumStringOf(metricType), threshold,
			)
		}
	default:
		return errorx.Errorf(
			errorx.ValidateParamError,
			"the metric_type[%s] is not supported",
			protobuf.GetEnumStringOf(metricType),
		)
	}

	if duration < 60 || duration > 3600 {
		return errorx.Errorf(
			errorx.ValidateParamError,
			"the duration[%d] must be in [60, 3600]",
			duration,
		)
	}

	return nil
}
