package accountconfigurationservicelogic

import (
	"context"
	"database/sql"
	"time"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type CreateAccountConfigurationLogic struct {
	*BaseLogic
}

func NewCreateAccountConfigurationLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CreateAccountConfigurationLogic {
	return &CreateAccountConfigurationLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// CreateAccountConfiguration 创建通用配置
func (l *CreateAccountConfigurationLogic) CreateAccountConfiguration(in *pb.CreateAccountConfigurationReq) (resp *pb.CreateAccountConfigurationResp, err error) {
	// validate the project_id in req
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, in.GetProjectId()); err != nil {
		return nil, err
	}

	configId, err := l.generateConfigId(in.GetProjectId())
	if err != nil {
		return nil, err
	}

	configuration := &model.AccountConfiguration{
		ProjectId: in.GetProjectId(),
		ConfigId:  configId,
		Name:      in.GetName(),
		Description: sql.NullString{
			String: in.GetDescription(),
			Valid:  in.GetDescription() != "",
		},
		ProductType:  in.GetProductType(),
		ProductName:  in.GetProductName(),
		PoolEnvTable: in.GetPoolEnvTable(),
		PoolEnvName:  in.GetPoolEnvName(),
		CreatedBy:    l.currentUser.Account,
		UpdatedBy:    l.currentUser.Account,
		CreatedAt:    time.Now(),
		UpdatedAt:    time.Now(),
	}

	if _, err = l.svcCtx.AccountConfigModel.Insert(l.ctx, nil, configuration); err != nil {
		return nil, errors.Wrapf(errorx.Err(errorx.DBError, err.Error()), "failed to insert table[%s] with values[%+v], error: %+v", l.svcCtx.AccountConfigModel.Table(), configuration, err)
	}

	resp = &pb.CreateAccountConfigurationResp{Configuration: &pb.AccountConfiguration{}}
	if err = utils.Copy(resp.Configuration, configuration, l.converters...); err != nil {
		return nil, errors.Wrapf(errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy account configuration[%+v] to response, error: %+v", configuration, err)
	}

	return resp, nil
}
