package accountconfigurationservicelogic

import (
	"context"
	"fmt"

	"github.com/hashicorp/go-multierror"
	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/mr"
	"github.com/zeromicro/go-zero/core/stores/sqlx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/redislock"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type RemoveAccountConfigurationLogic struct {
	*BaseLogic
}

func NewRemoveAccountConfigurationLogic(
	ctx context.Context, svcCtx *svc.ServiceContext,
) *RemoveAccountConfigurationLogic {
	return &RemoveAccountConfigurationLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// RemoveAccountConfiguration 删除通用配置
func (l *RemoveAccountConfigurationLogic) RemoveAccountConfiguration(in *pb.RemoveAccountConfigurationReq) (
	resp *pb.RemoveAccountConfigurationResp, err error,
) {
	// validate the project_id in req
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, in.GetProjectId()); err != nil {
		return nil, err
	}

	workers := len(in.GetConfigIds())
	if workers > common.ConstMRMaxWorkers {
		workers = common.ConstMRMaxWorkers
	}

	mr.ForEach(
		func(source chan<- any) {
			for _, configId := range in.GetConfigIds() {
				source <- configId
			}
		}, func(item any) {
			configId, ok := item.(string)
			if !ok {
				err = multierror.Append(
					err,
					errorx.Err(errorx.InternalError, fmt.Sprintf("the account config id[%v] is not a string", item)),
				)
			} else {
				if e := l.remove(in.GetProjectId(), configId); e != nil {
					err = multierror.Append(err, e)
				}
			}
		}, mr.WithContext(l.ctx), mr.WithWorkers(workers),
	)

	return &pb.RemoveAccountConfigurationResp{}, err
}

func (l *RemoveAccountConfigurationLogic) remove(projectId, configId string) (err error) {
	// validate the config_id in req
	accountConfig, err := model.CheckAccountConfigByConfigId(l.ctx, l.svcCtx.AccountConfigModel, projectId, configId)
	if err != nil {
		return err
	}

	// validate the relationship with account configuration
	rrs, err := l.svcCtx.AccountConfigReferenceModel.FindReferenceByConfigId(
		l.ctx, l.svcCtx.AccountConfigModel, projectId, configId,
	)
	if err != nil {
		return errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()),
			"failed to find account configuration reference with project_id[%s] and config_id[%s], error: %+v",
			projectId, configId, err,
		)
	} else if len(rrs) > 0 {
		return errors.WithStack(
			errorx.Err(
				errorx.ProhibitedBehavior, fmt.Sprintf(
					"cannot to delete the account configuration[%s] which has been referenced by %d api plan[s]",
					accountConfig.Name, len(rrs),
				),
			),
		)
	}

	perfPlans, err := l.svcCtx.PerfPlanV2Model.FindByAccountConfigID(l.ctx, projectId, configId)
	if err != nil {
		return errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()),
			"failed to find perf plan, project_id: %s, config_id: %s, config_name: %s, error: %+v",
			projectId, configId, accountConfig.Name, err,
		)
	} else if len(perfPlans) > 0 {
		return errors.WithStack(
			errorx.Err(
				errorx.ProhibitedBehavior, fmt.Sprintf(
					"cannot to delete the account configuration which has been referenced by %d perf plan[s], project_id: %s, config_id: %s, config_name: %s",
					len(perfPlans), projectId, configId, accountConfig.Name,
				),
			),
		)
	}

	// acquire redis lock
	key := fmt.Sprintf("%s:%s:%s", common.ConstLockAccountConfigProjectIdConfigIdPrefix, projectId, configId)
	lock, err := redislock.NewRedisLockAndAcquire(l.svcCtx.Redis, key, redislock.WithExpire(common.ConstLockExpireTime))
	if err != nil {
		return err
	}
	defer func() {
		// release redis lock
		e := lock.Release()
		if e != nil {
			l.Logger.Error(e)
			if re, ok := errorx.RootError(e); ok && re.Code() != errorx.ReleaseRedisLockFailure && err == nil {
				err = e
			}
		}
	}()

	return l.svcCtx.AccountConfigModel.Trans(
		l.ctx, func(context context.Context, session sqlx.Session) error {
			// Table: account_configuration
			if _, err := l.svcCtx.AccountConfigModel.RemoveByConfigId(
				context, session, projectId, configId,
			); err != nil {
				return err
			}

			return nil
		},
	)
}
