package accountconfigurationservicelogic

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type ViewAccountConfigurationLogic struct {
	*BaseLogic
}

func NewViewAccountConfigurationLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ViewAccountConfigurationLogic {
	return &ViewAccountConfigurationLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// ViewAccountConfiguration 查看通用配置
func (l *ViewAccountConfigurationLogic) ViewAccountConfiguration(in *pb.ViewAccountConfigurationReq) (resp *pb.ViewAccountConfigurationResp, err error) {
	// validate the project_id in req
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, in.GetProjectId()); err != nil {
		return nil, err
	}

	// validate the config_id in req
	accountConfig, err := model.CheckAccountConfigByConfigId(l.ctx, l.svcCtx.AccountConfigModel, in.GetProjectId(), in.GetConfigId())
	if err != nil {
		return nil, err
	}

	resp = &pb.ViewAccountConfigurationResp{Configuration: &pb.AccountConfiguration{}}
	if err = utils.Copy(resp.Configuration, accountConfig, l.converters...); err != nil {
		return nil, errors.Wrapf(errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy account configuration[%+v] to response, error: %+v", accountConfig, err)
	}

	return resp, nil
}
