package accountconfigurationservicelogic

import (
	"context"
	"database/sql"
	"fmt"
	"time"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/redislock"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type ModifyAccountConfigurationLogic struct {
	*BaseLogic
}

func NewModifyAccountConfigurationLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ModifyAccountConfigurationLogic {
	return &ModifyAccountConfigurationLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// ModifyAccountConfiguration 编辑通用配置
func (l *ModifyAccountConfigurationLogic) ModifyAccountConfiguration(in *pb.ModifyAccountConfigurationReq) (resp *pb.ModifyAccountConfigurationResp, err error) {
	// validate the project_id in req
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, in.GetProjectId()); err != nil {
		return nil, err
	}

	// validate the config_id in req
	origin, err := model.CheckAccountConfigByConfigId(l.ctx, l.svcCtx.AccountConfigModel, in.GetProjectId(), in.GetConfigId())
	if err != nil {
		return nil, err
	}

	// acquire redis lock
	key := fmt.Sprintf("%s:%s:%s", common.ConstLockAccountConfigProjectIdConfigIdPrefix, in.GetProjectId(), in.GetConfigId())
	lock, err := redislock.NewRedisLockAndAcquire(l.svcCtx.Redis, key, redislock.WithExpire(common.ConstLockExpireTime))
	if err != nil {
		return nil, err
	}
	defer func() {
		// release redis lock
		e := lock.Release()
		if e != nil {
			l.Logger.Error(e)
			if re, ok := errorx.RootError(e); ok && re.Code() != errorx.ReleaseRedisLockFailure && err == nil {
				err = e
			}
		}
	}()

	configuration := &model.AccountConfiguration{
		Id:        origin.Id,
		ProjectId: origin.ProjectId,
		ConfigId:  origin.ConfigId,
		Name:      in.GetName(),
		Description: sql.NullString{
			String: in.GetDescription(),
			Valid:  in.GetDescription() != "",
		},
		ProductType:  in.GetProductType(),
		ProductName:  in.GetProductName(),
		PoolEnvTable: in.GetPoolEnvTable(),
		PoolEnvName:  in.GetPoolEnvName(),
		Deleted:      0,
		CreatedBy:    origin.CreatedBy,
		UpdatedBy:    l.currentUser.Account,
		CreatedAt:    origin.CreatedAt,
		UpdatedAt:    time.Now(),
	}

	if _, err = l.svcCtx.AccountConfigModel.Update(l.ctx, nil, configuration); err != nil {
		return nil, errors.Wrapf(errorx.Err(errorx.DBError, err.Error()), "failed to update table[%s] with values[%+v], error: %+v", l.svcCtx.AccountConfigModel.Table(), configuration, err)
	}

	resp = &pb.ModifyAccountConfigurationResp{Configuration: &pb.AccountConfiguration{}}
	if err = utils.Copy(resp.Configuration, configuration, l.converters...); err != nil {
		return nil, errors.Wrapf(errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy account configuration[%+v] to response, error: %+v", configuration, err)
	}

	return resp, nil
}
