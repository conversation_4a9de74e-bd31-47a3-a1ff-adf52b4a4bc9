package accountconfigurationservicelogic

import (
	"context"

	"github.com/Masterminds/squirrel"
	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/logx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sqlbuilder"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/userinfo"
	commonutils "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/utils"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type BaseLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext

	currentUser *userinfo.UserInfo

	converters []commonutils.TypeConverter
}

func newBaseLogic(ctx context.Context, svcCtx *svc.ServiceContext) *BaseLogic {
	return &BaseLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,

		currentUser: userinfo.FromContext(ctx),

		converters: []commonutils.TypeConverter{},
	}
}

func (l *BaseLogic) generateConfigId(projectId string) (string, error) {
	g := commonutils.NewUniqueIdGenerator(commonutils.WithGenerateFunc(utils.GenAccountConfigId), commonutils.WithIsUniqueFunc(func(id string) bool {
		r, err := l.svcCtx.AccountConfigModel.FindOneByProjectIdConfigId(l.ctx, projectId, id)
		if err == model.ErrNotFound || r == nil {
			return true
		}
		return false
	}))
	configId := g.Next()
	if configId == "" {
		return "", errors.WithStack(errorx.Err(errorx.GenerateUniqueIdFailure, "failed to generate account config id, please try it later"))
	}

	return configId, nil
}

func (l *BaseLogic) generateSearchSqlBuilder(req *pb.SearchAccountConfigurationReq) (sb, scb squirrel.SelectBuilder) {
	m := l.svcCtx.AccountConfigModel

	sb = sqlbuilder.SearchOptions(
		m.SelectBuilder().Where("`project_id` = ?", req.GetProjectId()),
		sqlbuilder.WithCondition(m, req.GetCondition()),
		sqlbuilder.WithPagination(m, req.GetPagination()),
		sqlbuilder.WithSort(m, req.GetSort()),
	)
	scb = sqlbuilder.SearchOptions(
		m.SelectCountBuilder().Where("`project_id` = ?", req.GetProjectId()),
		sqlbuilder.WithCondition(m, req.GetCondition()),
	)

	return sb, scb
}
