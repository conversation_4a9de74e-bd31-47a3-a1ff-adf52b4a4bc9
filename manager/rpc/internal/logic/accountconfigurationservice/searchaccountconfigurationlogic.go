package accountconfigurationservicelogic

import (
	"context"
	"math"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type SearchAccountConfigurationLogic struct {
	*BaseLogic
}

func NewSearchAccountConfigurationLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SearchAccountConfigurationLogic {
	return &SearchAccountConfigurationLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// SearchAccountConfiguration 搜索通用配置
func (l *SearchAccountConfigurationLogic) SearchAccountConfiguration(in *pb.SearchAccountConfigurationReq) (resp *pb.SearchAccountConfigurationResp, err error) {
	resp = &pb.SearchAccountConfigurationResp{}

	// validate the project_id in req
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, in.GetProjectId()); err != nil {
		return nil, err
	}

	selectBuilder, countBuilder := l.generateSearchSqlBuilder(in)

	count, err := l.svcCtx.AccountConfigModel.FindCount(l.ctx, countBuilder)
	if err != nil {
		return nil, errors.Wrapf(errorx.Err(errorx.DBError, err.Error()), "failed to count account configuration with project_id[%s], error: %+v", in.GetProjectId(), err)
	}
	resp.TotalCount = uint64(count)
	resp.TotalPage = 1

	accountConfigs, err := l.svcCtx.AccountConfigModel.FindNoCacheByQuery(l.ctx, selectBuilder)
	if err != nil {
		return nil, errors.Wrapf(errorx.Err(errorx.DBError, err.Error()), "failed to find account configuration with project_id[%s], error: %+v", in.GetProjectId(), err)
	}

	resp.Items = make([]*pb.AccountConfiguration, 0, len(accountConfigs))
	for _, accountConfig := range accountConfigs {
		item := &pb.AccountConfiguration{}
		if err = utils.Copy(item, accountConfig, l.converters...); err != nil {
			return nil, errors.Wrapf(errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy account configuration[%+v] to response, error: %+v", accountConfig, err)
		}

		resp.Items = append(resp.Items, item)
	}

	pagination := in.GetPagination()
	if pagination != nil {
		resp.CurrentPage = pagination.GetCurrentPage()
		resp.PageSize = pagination.GetPageSize()
		resp.TotalPage = uint64(math.Ceil(float64(resp.TotalCount) / float64(resp.PageSize)))
	}

	return resp, nil
}
