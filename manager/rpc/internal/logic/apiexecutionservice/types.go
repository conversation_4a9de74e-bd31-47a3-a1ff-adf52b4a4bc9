package apiexecutionservicelogic

import (
	"github.com/pkg/errors"

	ctypes "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

var skipError = errors.New("skip error")

type getApiExecutionDataFunc func(projectId, typeId, version string) (*pb.ApiExecutionData, error)

type componentData struct {
	ProjectId     string `json:"project_id"`
	ParentId      string `json:"parent_id"`
	ParentType    string `json:"parent_type"`
	ParentVersion string `json:"parent_version"`

	ApiExecutionData *pb.ApiExecutionData `json:"api_execution_data"`
}

type genApiExecutionData struct {
	componentData

	ChildrenIndex int64
	ChildIndex    int64
	Relation      *ctypes.Relation // 注：这里直接使用Go Struct的`Relation`，而不是ProtoBuf的`Relation`
}

type genApiExecutionStructure struct {
	componentData

	item any
}

type serviceMethodData struct {
	Service    string `json:"service"`
	Namespace  string `json:"namespace"`
	Cmd        uint32 `json:"cmd"`
	GrpcPath   string `json:"grpc_path"`
	QueryPath  string `json:"query_path"`
	Deprecated bool   `json:"deprecated"`
}
