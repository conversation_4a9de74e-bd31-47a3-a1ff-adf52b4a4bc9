package apiexecutionservicelogic

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/codes"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type GetApiExecutionDataStructureLogic struct {
	*BaseLogic
}

func NewGetApiExecutionDataStructureLogic(
	ctx context.Context, svcCtx *svc.ServiceContext,
) *GetApiExecutionDataStructureLogic {
	return &GetApiExecutionDataStructureLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// GetApiExecutionDataStructure 获取API执行数据结构
func (l *GetApiExecutionDataStructureLogic) GetApiExecutionDataStructure(in *pb.GetApiExecutionDataStructureReq) (
	resp *pb.ApiExecutionData, err error,
) {
	// validate the project_id
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, in.GetProjectId()); err != nil {
		return nil, err
	}
	switch in.GetType() {
	case pb.ApiExecutionDataType_API_SUITE: // API集合
		return l.getApiSuiteExecutionStructure(in.GetProjectId(), in.GetId())
	case pb.ApiExecutionDataType_INTERFACE_DOCUMENT: // 接口集合
		return l.getInterfaceDocumentExecutionStructure(in.GetProjectId(), in.GetId())
	case pb.ApiExecutionDataType_API_PLAN: // API计划
		return l.getApiPlanExecutionStructure(in.GetProjectId(), in.GetId(), in.GetApiPlanExtraData())
	case pb.ApiExecutionDataType_UI_PLAN: // UI计划
		return l.getUIPlanExecutionData(in.GetProjectId(), in.GetId(), in.GetUiPlanExtraData())
	}

	return nil, errors.WithStack(
		errorx.Errorf(
			codes.GenerateApiExecutionDataFailure,
			"the type of api execution data is not currently supported, project_id: %s, type: %s, type_id: %s",
			in.GetProjectId(), in.GetType(), in.GetId(),
		),
	)
}
