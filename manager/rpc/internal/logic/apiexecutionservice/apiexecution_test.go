package apiexecutionservicelogic

import (
	"testing"

	"github.com/zeromicro/go-zero/core/jsonx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-middleware/permission/common/mock"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

func Test_GetApiExecutionDataStructure(t *testing.T) {
	lo := NewGetApiExecutionDataStructureLogic(mock.Mock_Context(), svc.Mock_ServiceContext())
	out, err := lo.GetApiExecutionDataStructure(
		&pb.GetApiExecutionDataStructureReq{
			ProjectId: "project_id:Kqllt5-9fA-I5UOdhjA5d",
			Type:      pb.ApiExecutionDataType_API_PLAN,
			Id:        "plan_id:T9Fot4riMHOvCaThRrlTL",
			Services:  []string{"channel-convene"},
		},
	)
	if err != nil {
		t.<PERSON><PERSON><PERSON>("%s", err)
		t.<PERSON>ail<PERSON>ow()
	}

	t.Log(jsonx.MarshalToStringIgnoreError(out))
}
