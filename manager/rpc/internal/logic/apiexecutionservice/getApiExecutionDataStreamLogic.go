package apiexecutionservicelogic

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/codes"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type GetApiExecutionDataStreamLogic struct {
	*BaseLogic
}

func NewGetApiExecutionDataStreamLogic(
	ctx context.Context, svcCtx *svc.ServiceContext,
) *GetApiExecutionDataStreamLogic {
	return &GetApiExecutionDataStreamLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// GetApiExecutionDataStream 批量获取API执行数据（服务端流式）
func (l *GetApiExecutionDataStreamLogic) GetApiExecutionDataStream(
	in *pb.GetApiExecutionDataStreamReq, stream pb.ApiExecutionService_GetApiExecutionDataStreamServer,
) error {
	// validate the project_id
	if _, err := model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, in.GetProjectId()); err != nil {
		return err
	}

	var fn getApiExecutionDataFunc

	switch in.GetType() {
	case pb.ApiExecutionDataType_API_COMPONENT_GROUP:
		fn = l.getComponentGroupExecutionData
	case pb.ApiExecutionDataType_API_CASE:
		fn = l.getApiCaseExecutionData
	case pb.ApiExecutionDataType_INTERFACE_CASE:
		fn = l.getInterfaceCaseExecutionData
	default:
		return errors.WithStack(
			errorx.Errorf(
				codes.GenerateApiExecutionDataFailure,
				"the type of api execution data is not currently supported, project_id: %s, type: %s",
				in.GetProjectId(), in.GetType(),
			),
		)
	}

	for _, item := range in.GetItems() {
		if item == nil {
			continue
		}

		data, err := fn(in.GetProjectId(), item.GetId(), item.GetVersion())
		if err != nil {
			// 流式处理发生错误时返回一个带错误数据的 `*ApiExecutionData`
			if item.GetVersion() == "" {
				l.Logger.Errorf(
					"failed to get api execution data, project_id: %s, type_id: %s, error: %+v",
					in.GetProjectId(), item.GetId(), err,
				)
			} else {
				l.Logger.Errorf(
					"failed to get api execution data, project_id: %s, type_id: %s, version: %s, error: %+v",
					in.GetProjectId(), item.GetId(), item.GetVersion(), err,
				)
			}

			data = l.generateApiExecutionDataWithError(in.GetType(), item.GetId(), err)
		}

		if err = stream.Send(data); err != nil {
			return err
		}
	}

	return nil
}
