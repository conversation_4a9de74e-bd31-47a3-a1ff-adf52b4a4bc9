package perfdataservicelogic

import (
	"context"
	"fmt"
	"os"

	"github.com/hashicorp/go-multierror"
	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/mr"
	"github.com/zeromicro/go-zero/core/stores/sqlx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/caller"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type RemovePerfDataLogic struct {
	*BaseLogic
}

func NewRemovePerfDataLogic(ctx context.Context, svcCtx *svc.ServiceContext) *RemovePerfDataLogic {
	return &RemovePerfDataLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// RemovePerfData 删除压测数据
func (l *RemovePerfDataLogic) RemovePerfData(in *pb.RemovePerfDataReq) (out *pb.RemovePerfDataResp, err error) {
	// validate the project_id in req
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, in.GetProjectId()); err != nil {
		return nil, err
	}

	dataIDs := in.GetDataIds()
	workers := len(dataIDs)
	if workers > common.ConstMRMaxWorkers {
		workers = common.ConstMRMaxWorkers
	}

	mr.ForEach[string](
		func(source chan<- string) {
			for _, dataID := range dataIDs {
				source <- dataID
			}
		}, func(item string) {
			if e := l.remove(in.GetProjectId(), item); e != nil {
				err = multierror.Append(err, e)
			}
		}, mr.WithContext(l.ctx), mr.WithWorkers(workers),
	)

	return &pb.RemovePerfDataResp{}, err
}

func (l *RemovePerfDataLogic) remove(projectID, dataID string) (err error) {
	// validate the data_id in req
	perfData, err := model.CheckPerfDataByDataID(l.ctx, l.svcCtx.PerfDataModel, projectID, dataID)
	if err != nil {
		if re, ok := errorx.RootError(err); ok && re.Code() == errorx.NotExists {
			return nil
		}

		return err
	}

	filename := perfData.Name + perfData.Extension

	rrs, err := l.svcCtx.PerfCaseModel.FindByDataID(l.ctx, projectID, dataID)
	if err != nil {
		return errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()),
			"failed to find perf case, project_id: %s, data_id: %s, error: %+v",
			projectID, dataID, err,
		)
	} else if len(rrs) > 0 {
		return errorx.Errorf(
			errorx.ProhibitedBehavior,
			"cannot to delete the perf data which has been associated with %d perf case[s], project_id: %s, data_id: %s, filename: %s",
			len(rrs), projectID, dataID, filename,
		)
	}

	key := fmt.Sprintf(
		"%s:%s:%s", common.ConstLockPerfDataProjectIDDataIDPrefix, projectID, dataID,
	)
	fn := func() error {
		return l.svcCtx.PerfDataModel.Trans(
			l.ctx, func(context context.Context, session sqlx.Session) error {
				// Table: perf_data, physical deletion
				if err := l.svcCtx.PerfDataModel.Delete(
					context, session, perfData.Id,
				); err != nil {
					return errors.Wrapf(
						errorx.Err(errorx.DBError, err.Error()),
						"failed to remove perf data, project_id: %s, data_id: %s, filename: %s, error: %+v",
						projectID, dataID, filename, err,
					)
				}

				// remove the perf data file
				if err := os.Remove(perfData.Path); err != nil {
					return errors.Wrapf(
						errorx.Err(errorx.FileOperationFailure, err.Error()),
						"failed to remove the perf data file, project_id: %s, data_id: %s, filename: %s, error: %+v",
						projectID, dataID, filename, err,
					)
				}

				return nil
			},
		)
	}
	return caller.LockDo(l.svcCtx.Redis, key, fn)
}
