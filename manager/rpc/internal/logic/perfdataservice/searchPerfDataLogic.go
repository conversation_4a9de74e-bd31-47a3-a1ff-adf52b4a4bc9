package perfdataservicelogic

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sqlbuilder/rpc"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type SearchPerfDataLogic struct {
	*BaseLogic
}

func NewSearchPerfDataLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SearchPerfDataLogic {
	return &SearchPerfDataLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// SearchPerfData 搜索压测数据
func (l *SearchPerfDataLogic) SearchPerfData(in *pb.SearchPerfDataReq) (out *pb.SearchPerfDataResp, err error) {
	out = &pb.SearchPerfDataResp{}

	// validate the project_id in req
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, in.GetProjectId()); err != nil {
		return nil, err
	}

	req := model.SearchPerfDataReq{
		BaseSearchReq: model.BaseSearchReq{
			ProjectID:  in.GetProjectId(),
			Condition:  in.GetCondition(),
			Pagination: in.GetPagination(),
			Sort:       rpc.ConvertSortFields(in.GetSort()),
		},
	}
	count, err := l.svcCtx.PerfDataModel.FindCountBySearchReq(l.ctx, req)
	if err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()),
			"failed to count perf datas, project_id: %s, error: %+v",
			in.GetProjectId(), err,
		)
	}
	out.TotalCount = uint64(count)
	out.TotalPage = 1

	perfDatas, err := l.svcCtx.PerfDataModel.FindAllBySearchReq(l.ctx, req)
	if err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()),
			"failed to find perf datas, project_id: %s, error: %+v",
			in.GetProjectId(), err,
		)
	}

	out.Items = make([]*pb.PerfData, 0, len(perfDatas))
	for _, perfData := range perfDatas {
		item := &pb.PerfData{}
		if err = utils.Copy(item, perfData, l.converters...); err != nil {
			return nil, errors.Wrapf(
				errorx.Err(errorx.CopyToStructFailure, err.Error()),
				"failed to copy perf data to response, perf data: %s, error: %+v",
				protobuf.MarshalJSONIgnoreError(perfData), err,
			)
		}

		out.Items = append(out.Items, item)
	}

	pagination := in.GetPagination()
	if pagination != nil {
		out.CurrentPage = pagination.GetCurrentPage()
		out.PageSize = pagination.GetPageSize()
		out.TotalPage = (out.TotalCount + out.PageSize - 1) / out.PageSize // round up
	}

	return out, nil
}
