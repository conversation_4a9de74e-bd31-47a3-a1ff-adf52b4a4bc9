package plancommonservicelogic

import (
	"context"
	"fmt"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/stores/sqlx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/codes"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type HandleLikePlanLogic struct {
	*BaseLogic
}

func NewHandleLikePlanLogic(ctx context.Context, svcCtx *svc.ServiceContext) *HandleLikePlanLogic {
	return &HandleLikePlanLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// HandleLikePlan 处理计划收藏
func (l *HandleLikePlanLogic) HandleLikePlan(in *pb.HandleLikePlanReq) (out *pb.HandleLikePlanResp, err error) {
	user := l.currentUser
	if user == nil {
		return nil, nil
	}
	var (
		b        bool
		modelVal *model.PlanUserLikeRelationship
	)
	if b, modelVal, err = checkLikePlan(
		l.ctx, user.Account, in.GetPlanType(), in.GetProjectId(), in.GetPlanId(),
		l.svcCtx.PlanUserLikeRelationshipModel,
	); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.InternalError, err.Error()),
			"HandleLikePlan[%s:%s], error: %+v",
			user.Account, in.GetPlanId(), err,
		)
	}
	if in.IsLike {
		if !b {
			err = l.svcCtx.PlanUserLikeRelationshipModel.Trans(
				l.ctx, func(context context.Context, session sqlx.Session) error {
					_, err := l.svcCtx.PlanUserLikeRelationshipModel.InsertTX(
						context, session, &model.PlanUserLikeRelationship{
							ProjectId: in.GetProjectId(),
							PlanId:    in.GetPlanId(),
							Account:   user.Account,
							PlanType:  int64(in.GetPlanType()),
							Deleted:   0,
							CreatedBy: user.Account,
							UpdatedBy: user.Account,
						},
					)
					return err
				},
			)
			if err != nil {
				return nil, errors.Wrapf(
					errorx.Err(errorx.InternalError, err.Error()),
					"HandleLikePlan[%s:%s], error: %+v",
					user.Account, in.GetPlanId(), err,
				)
			}
		} else {
			return nil, errors.WithStack(
				errorx.Err(
					codes.HavelikedFailure,
					fmt.Sprintf(
						"current data has been liked by the user[%s], plan id[%s]", user.Account, in.GetPlanId(),
					),
				),
			)
		}
	} else {
		if b {
			err = l.svcCtx.PlanUserLikeRelationshipModel.Trans(
				l.ctx, func(context context.Context, session sqlx.Session) error {
					err := l.svcCtx.PlanUserLikeRelationshipModel.DeleteByData(l.ctx, session, modelVal)
					return err
				},
			)
			if err != nil {
				return nil, errors.Wrapf(
					errorx.Err(errorx.InternalError, err.Error()),
					"HandleLikePlan[%s:%s], error: %+v",
					user.Account, in.GetPlanId(), err,
				)
			}
		}
	}

	return &pb.HandleLikePlanResp{}, err
}
