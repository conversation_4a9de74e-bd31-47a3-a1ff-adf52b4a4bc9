package plancommonservicelogic

import (
	"context"
	"errors"

	"github.com/zeromicro/go-zero/core/stores/sqlx"

	commonpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type CheckLikePlanLogic struct {
	*BaseLogic
}

func NewCheckLikePlanLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CheckLikePlanLogic {
	return &CheckLikePlanLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// CheckLikePlan 检查计划收藏情况
func (l *CheckLikePlanLogic) CheckLikePlan(in *pb.CheckLikePlanReq) (out *pb.CheckLikePlanResp, err error) {
	out = new(pb.CheckLikePlanResp)
	user := l.currentUser
	if user != nil {
		if b, _, err := checkLikePlan(
			l.ctx, user.Account, in.GetPlanType(), in.GetProjectId(), in.GetPlanId(),
			l.svcCtx.PlanUserLikeRelationshipModel,
		); err == nil && b {
			out.IsLike = true
		}
	}
	return out, nil
}

func checkLikePlan(
	ctx context.Context, account string, planType commonpb.PlanType, projectId, planId string,
	model model.PlanUserLikeRelationshipModel,
) (b bool, planUserLikeRelationship *model.PlanUserLikeRelationship, err error) {
	planUserLikeRelationship, err = model.FindByAccountAndPlanTypeAndProjectIdAndPlanId(
		ctx, account, planType, projectId, planId,
	)
	if errors.Is(err, sqlx.ErrNotFound) {
		return false, nil, nil
	}
	if err != nil {
		return false, nil, err
	}
	if planUserLikeRelationship != nil && planUserLikeRelationship.PlanId != "" {
		return true, planUserLikeRelationship, nil
	}
	return false, nil, nil
}
