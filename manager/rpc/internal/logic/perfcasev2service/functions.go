package perfcasev2servicelogic

import (
	"time"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	commonpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/utils"
)

const zeroSeconds = "0s"

type IPerfCase interface {
	GetProtocol() commonpb.Protocol
	GetRateLimits() []*commonpb.RateLimitV2
	GetSetupSteps() []*commonpb.PerfCaseStepV2
	GetSerialSteps() []*commonpb.PerfCaseStepV2
	GetParallelSteps() []*commonpb.PerfCaseStepV2
	GetTeardownSteps() []*commonpb.PerfCaseStepV2
}

func validatePerfCase[C IPerfCase](perfCase C) error {
	var (
		protocol      = perfCase.GetProtocol()
		rateLimits    = perfCase.GetRateLimits()
		setupSteps    = perfCase.GetSetupSteps()
		serialSteps   = perfCase.GetSerialSteps()
		parallelSteps = perfCase.GetParallelSteps()
		teardownSteps = perfCase.GetTeardownSteps()

		numberOfSetupSteps    = len(setupSteps)
		numberOfSerialSteps   = len(serialSteps)
		numberOfParallelSteps = len(parallelSteps)
		numberOfTeardownSteps = len(teardownSteps)
		totalSteps            = numberOfSetupSteps + numberOfSerialSteps + numberOfParallelSteps + numberOfTeardownSteps
	)

	switch protocol {
	case commonpb.Protocol_PROTOCOL_TT_AUTH:
		if totalSteps != 0 {
			return errorx.Errorf(
				errorx.ValidateParamError,
				"when the protocol is `TT_AUTH`, there is no need to set perf case steps, total steps: %d",
				totalSteps,
			)
		}
		return nil
	default:
		if numberOfSerialSteps == 0 && numberOfParallelSteps == 0 {
			return errorx.Errorf(
				errorx.ValidateParamError,
				"when the protocol is %q, the serial steps and parallel steps cannot both be empty",
				protobuf.GetEnumStringOf(protocol),
			)
		}
	}

	if err := validateSerialSteps(rateLimits, serialSteps); err != nil {
		return err
	}
	if err := validateParallelSteps(parallelSteps); err != nil {
		return err
	}

	return nil
}

func validateSerialSteps(caseRateLimits []*commonpb.RateLimitV2, steps []*commonpb.PerfCaseStepV2) error {
	number := len(steps)

	if number > constants.ConstMaxPerfTestSerialSteps {
		return errorx.Errorf(
			errorx.ValidateParamError,
			"the number of serial steps exceeds the maximum value, number: %d, maximum: %d",
			number, constants.ConstMaxPerfTestSerialSteps,
		)
	}

	for _, step := range steps {
		name := step.GetName()
		sleep := step.GetSleep()
		if sleep == "" {
			step.Sleep = zeroSeconds
			continue
		}

		sleepDuration, err := time.ParseDuration(sleep)
		if err != nil {
			return errorx.Errorf(
				errorx.ValidateParamError,
				"the format of the sleep time for the serial step is incorrect, step: %s, sleep: %s, error: %+v",
				name, sleep, err,
			)
		}

		if sleepDuration > constants.ConstMaxPerfTestStepSleepTime {
			return errorx.Errorf(
				errorx.ValidateParamError,
				"the sleep time for the serial step exceeds the maximum value, step: %s, sleep: %s, maximum: %s",
				name, sleep, constants.ConstMaxPerfTestStepSleepTime,
			)
		}

		rateLimits := step.GetRateLimits()
		if len(caseRateLimits) == 0 && len(rateLimits) == 0 {
			continue
		} else if len(rateLimits) == 0 {
			rateLimits = caseRateLimits
		}

		rps := utils.GetMaxRPSFromRateLimits(rateLimits)
		if rps > constants.ConstDividePerfTestStepRPS &&
			sleepDuration > constants.ConstMaxPerfTestStepWithDivRPSSleepTime {
			return errorx.Errorf(
				errorx.ValidateParamError,
				"the sleep time for the serial step with high rps exceeds the maximum value, step: %s, rps: %d > %d, sleep: %s > %s",
				name, rps, constants.ConstDividePerfTestStepRPS,
				sleep, constants.ConstMaxPerfTestStepWithDivRPSSleepTime,
			)
		}
	}

	return nil
}

func validateParallelSteps(steps []*commonpb.PerfCaseStepV2) error {
	number := len(steps)

	if number > constants.ConstMaxPerfTestParallelSteps {
		return errorx.Errorf(
			errorx.ValidateParamError,
			"the number of parallel steps exceeds the maximum value, number: %d, maximum: %d",
			number, constants.ConstMaxPerfTestParallelSteps,
		)
	}

	for _, step := range steps {
		name := step.GetName()
		sleep := step.GetSleep()
		if sleep == "" {
			step.Sleep = zeroSeconds
			continue
		}

		_, err := time.ParseDuration(sleep)
		if err != nil {
			return errorx.Errorf(
				errorx.ValidateParamError,
				"the format of the sleep time for the parallel step is incorrect, step: %s, sleep: %s, error: %+v",
				name, sleep, err,
			)
		}
	}

	return nil
}
