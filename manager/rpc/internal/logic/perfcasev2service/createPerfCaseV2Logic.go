package perfcasev2servicelogic

import (
	"context"
	"database/sql"
	"time"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"
	"github.com/zeromicro/go-zero/core/stores/sqlx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"
	userpb "gitlab.ttyuyin.com/TestDevelopment/qet-backend-middleware/user/rpc/pb"

	commonpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
	commonutils "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/utils"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type CreatePerfCaseV2Logic struct {
	*BaseLogic
}

func NewCreatePerfCaseV2Logic(ctx context.Context, svcCtx *svc.ServiceContext) *CreatePerfCaseV2Logic {
	return &CreatePerfCaseV2Logic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// CreatePerfCaseV2 创建压测用例
func (l *CreatePerfCaseV2Logic) CreatePerfCaseV2(in *pb.CreatePerfCaseV2Req) (out *pb.CreatePerfCaseV2Resp, err error) {
	// validate the project_id in req
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, in.GetProjectId()); err != nil {
		return nil, err
	}

	var c *model.Category
	// validate the category_id in req
	if c, err = model.CheckCategoryByCategoryId(
		l.ctx, l.svcCtx.CategoryModel, in.GetProjectId(), common.ConstCategoryTreeTypePerfCase, in.GetCategoryId(),
	); err != nil {
		return nil, err
	} else if c.CategoryType != common.ConstCategoryTypeDirectory {
		return nil, errors.WithStack(
			errorx.Errorf(
				errorx.ProhibitedBehavior,
				"the type of parent category[%s] does not support creation of sub category",
				c.CategoryType,
			),
		)
	}

	// validate perf case data
	if err = validatePerfCase(in); err != nil {
		return nil, err
	}

	perfCase, err := l.create(in)
	if err != nil {
		return nil, err
	}

	out = &pb.CreatePerfCaseV2Resp{Case: &pb.PerfCaseV2{}}
	if err = utils.Copy(out.Case, perfCase, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy perf case to response, perf case: %s, error: %+v",
			jsonx.MarshalIgnoreError(perfCase), err,
		)
	}

	return out, nil
}

func (l *CreatePerfCaseV2Logic) create(req *pb.CreatePerfCaseV2Req) (*model.PerfCaseV2, error) {
	var (
		projectID     = req.GetProjectId()
		description   = req.GetDescription()
		rateLimits    = req.GetRateLimits()
		setupSteps    = req.GetSetupSteps()
		serialSteps   = req.GetSerialSteps()
		parallelSteps = req.GetParallelSteps()
		teardownSteps = req.GetTeardownSteps()
		maintainedBy  = req.GetMaintainedBy()

		tags                                                              sql.NullString
		setupStepsStr, serialStepsStr, parallelStepsStr, teardownStepsStr sql.NullString
		numberOfSteps                                                     int64
	)

	caseID, err := l.generatePerfCaseID(projectID)
	if err != nil {
		return nil, err
	}

	if len(req.GetTags()) > 0 {
		// 由于`protojson`只能序列化`[]proto.Message`，而`tags`字段类型为`[]string`，Go Struct跟PB没有大的区别，因此直接使用`jsonx`暂代`protojson`
		tags.String = jsonx.MarshalToStringIgnoreError(req.GetTags())
		tags.Valid = true
	}

	if maintainedBy != "" {
		var user *userpb.UserInfo
		if user, err = l.getUserInfoByAccount(maintainedBy); err != nil {
			return nil, err
		} else if !user.GetEnabled() {
			return nil, errorx.Errorf(
				errorx.ProhibitedBehavior,
				"cannot assign a disabled user as a maintainer of perf case, project_id: %s, maintained_by: %s",
				projectID, maintainedBy,
			)
		}
	} else {
		// if the maintainer is not set, the creator is set as the maintainer
		maintainedBy = l.currentUser.Account
	}

	if len(setupSteps) > 0 {
		setupStepsStr.String = protobuf.MarshalJSONWithMessagesToStringIgnoreError(setupSteps)
		setupStepsStr.Valid = true
		numberOfSteps += int64(len(setupSteps))
	}
	if len(serialSteps) > 0 {
		serialStepsStr.String = protobuf.MarshalJSONWithMessagesToStringIgnoreError(serialSteps)
		serialStepsStr.Valid = true
		numberOfSteps += int64(len(serialSteps))
	}
	if len(parallelSteps) > 0 {
		parallelStepsStr.String = protobuf.MarshalJSONWithMessagesToStringIgnoreError(parallelSteps)
		parallelStepsStr.Valid = true
		numberOfSteps += int64(len(parallelSteps))
	}
	if len(teardownSteps) > 0 {
		teardownStepsStr.String = protobuf.MarshalJSONWithMessagesToStringIgnoreError(teardownSteps)
		teardownStepsStr.Valid = true
		numberOfSteps += int64(len(teardownSteps))
	}

	caseRPS := commonutils.GetMaxRPSFromRateLimits(rateLimits)
	stepsRPS := commonutils.GetMaxRPSFromSerialStepsAndParallelStepsV2(serialSteps, parallelSteps)

	now := time.Now()
	perfCase := &model.PerfCaseV2{
		ProjectId:  projectID,
		CategoryId: req.GetCategoryId(),
		CaseId:     caseID,
		Name:       req.GetName(),
		Description: sql.NullString{
			String: description,
			Valid:  description != "",
		},
		Tags:          tags,
		Protocol:      protobuf.GetEnumStringOf(req.GetProtocol()),
		RateLimits:    protobuf.MarshalJSONWithMessagesToStringIgnoreError(req.GetRateLimits()),
		SetupSteps:    setupStepsStr,
		SerialSteps:   serialStepsStr,
		ParallelSteps: parallelStepsStr,
		TeardownSteps: teardownStepsStr,
		NumberOfSteps: numberOfSteps,
		TargetRps:     utils.Max(caseRPS, stepsRPS),
		State:         int64(pb.CommonState_CS_ENABLE),
		MaintainedBy: sql.NullString{
			String: maintainedBy,
			Valid:  maintainedBy != "",
		},
		CreatedBy: l.currentUser.Account,
		UpdatedBy: l.currentUser.Account,
		CreatedAt: now,
		UpdatedAt: now,
	}

	// create perf case in a transaction
	if err = l.svcCtx.PerfCaseV2Model.Trans(
		l.ctx, func(context context.Context, session sqlx.Session) error {
			// create perf case steps
			for _, v := range []struct {
				stepType commonpb.PerfCaseStepType
				steps    []*commonpb.PerfCaseStepV2
			}{
				{
					stepType: commonpb.PerfCaseStepType_PerfCaseStepType_SETUP,
					steps:    setupSteps,
				},
				{
					stepType: commonpb.PerfCaseStepType_PerfCaseStepType_SERIAL,
					steps:    serialSteps,
				},
				{
					stepType: commonpb.PerfCaseStepType_PerfCaseStepType_PARALLEL,
					steps:    parallelSteps,
				},
				{
					stepType: commonpb.PerfCaseStepType_PerfCaseStepType_TEARDOWN,
					steps:    teardownSteps,
				},
			} {
				if err := l.createPerfCaseSteps(context, session, perfCase, caseRPS, v.stepType, v.steps); err != nil {
					return err
				}
			}

			// create perf case
			if _, err := l.svcCtx.PerfCaseV2Model.Insert(context, session, perfCase); err != nil {
				return errors.Wrapf(
					errorx.Err(errorx.DBError, err.Error()),
					"failed to insert values to table, table: %s, values: %s, error: %+v",
					l.svcCtx.PerfCaseV2Model.Table(), jsonx.MarshalIgnoreError(perfCase), err,
				)
			}

			// create the new tag and tag reference of perf case
			if err := l.createTagLogic.CreateTagAndReferenceForInternal(
				context, session, types.CreateOrUpdateTagReference{
					ProjectId:     perfCase.ProjectId,
					ReferenceType: common.ConstReferenceTypePerfCase,
					ReferenceId:   perfCase.CaseId,
					Tags:          req.GetTags(),
				},
			); err != nil {
				return err
			}

			return nil
		},
	); err != nil {
		return nil, err
	}

	return perfCase, nil
}
