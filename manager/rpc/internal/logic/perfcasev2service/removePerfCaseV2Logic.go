package perfcasev2servicelogic

import (
	"context"
	"fmt"

	"github.com/hashicorp/go-multierror"
	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/mr"
	"github.com/zeromicro/go-zero/core/stores/sqlx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/caller"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type RemovePerfCaseV2Logic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewRemovePerfCaseV2Logic(ctx context.Context, svcCtx *svc.ServiceContext) *RemovePerfCaseV2Logic {
	return &RemovePerfCaseV2Logic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// RemovePerfCaseV2 删除压测用例
func (l *RemovePerfCaseV2Logic) RemovePerfCaseV2(in *pb.RemovePerfCaseV2Req) (out *pb.RemovePerfCaseV2Resp, err error) {
	// validate the project_id in req
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, in.GetProjectId()); err != nil {
		return nil, err
	}

	caseIDs := in.GetCaseIds()
	workers := len(caseIDs)
	if workers > common.ConstMRMaxWorkers {
		workers = common.ConstMRMaxWorkers
	}

	mr.ForEach[string](
		func(source chan<- string) {
			for _, caseID := range caseIDs {
				source <- caseID
			}
		}, func(item string) {
			if e := l.remove(in.GetProjectId(), item); e != nil {
				err = multierror.Append(err, e)
			}
		}, mr.WithContext(l.ctx), mr.WithWorkers(workers),
	)

	return &pb.RemovePerfCaseV2Resp{}, nil
}

func (l *RemovePerfCaseV2Logic) remove(projectID, caseID string) (err error) {
	// validate the case_id in req
	perfCase, err := model.CheckPerfCaseV2ByCaseID(l.ctx, l.svcCtx.PerfCaseV2Model, projectID, caseID)
	if err != nil {
		if re, ok := errorx.RootError(err); ok && re.Code() == errorx.NotExists {
			return nil
		}

		return err
	}

	rrs, err := l.svcCtx.PerfPlanCaseModel.FindByCaseID(l.ctx, projectID, caseID)
	if err != nil {
		return errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()),
			"failed to find perf plan by perf case id, project_id: %s, case_id: %s, error: %+v",
			projectID, caseID, err,
		)
	} else if len(rrs) > 0 {
		return errorx.Errorf(
			errorx.ProhibitedBehavior,
			"cannot to delete the perf case which has been associated with %d perf plan[s], please remove the perf case from perf plan first, project_id: %s, case_id: %s",
			len(rrs), projectID, caseID,
		)
	}

	key := fmt.Sprintf(
		"%s:%s:%s", common.ConstLockPerfCaseProjectIDCaseIDPrefix, projectID, caseID,
	)
	fn := func() error {
		return l.svcCtx.PerfCaseStepV2Model.Trans(
			l.ctx, func(context context.Context, session sqlx.Session) error {
				// Table: perf_case_step_v2, physical deletion
				if _, err := l.svcCtx.PerfCaseStepV2Model.DeleteByCaseID(
					context, session, projectID, caseID,
				); err != nil {
					return errors.Wrapf(
						errorx.Err(errorx.DBError, err.Error()),
						"failed to remove perf case steps, project_id: %s, case_id: %s, error: %+v",
						projectID, caseID, err,
					)
				}

				// Table: perf_case, physical deletion
				if err := l.svcCtx.PerfCaseV2Model.Delete(
					context, session, perfCase.Id,
				); err != nil {
					return errors.Wrapf(
						errorx.Err(errorx.DBError, err.Error()),
						"failed to remove perf case, project_id: %s, case_id: %s, error: %+v",
						projectID, caseID, err,
					)
				}

				return nil
			},
		)
	}
	return caller.LockDo(l.svcCtx.Redis, key, fn)
}
