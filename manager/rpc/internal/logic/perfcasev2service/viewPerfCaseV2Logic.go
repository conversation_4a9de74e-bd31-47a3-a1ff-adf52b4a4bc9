package perfcasev2servicelogic

import (
	"context"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type ViewPerfCaseV2Logic struct {
	*BaseLogic
}

func NewViewPerfCaseV2Logic(ctx context.Context, svcCtx *svc.ServiceContext) *ViewPerfCaseV2Logic {
	return &ViewPerfCaseV2Logic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// ViewPerfCaseV2 查看压测用例
func (l *ViewPerfCaseV2Logic) ViewPerfCaseV2(in *pb.ViewPerfCaseV2Req) (out *pb.ViewPerfCaseV2Resp, err error) {
	// validate the project_id in req
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, in.GetProjectId()); err != nil {
		return nil, err
	}

	perfCase, err := model.CheckPerfCaseV2ByCaseID(l.ctx, l.svcCtx.PerfCaseV2Model, in.GetProjectId(), in.GetCaseId())
	if err != nil {
		return nil, err
	}

	out = &pb.ViewPerfCaseV2Resp{Case: &pb.PerfCaseV2{}}
	if err = utils.Copy(out.Case, perfCase, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy perf case to response, perf case: %s, error: %+v",
			jsonx.MarshalIgnoreError(perfCase), err,
		)
	}

	if err = l.setReferenceQPS(
		l.ctx,
		out.GetCase().GetProjectId(),
		out.GetCase().GetProtocol(),
		out.GetCase().GetSetupSteps(),
		out.GetCase().GetSerialSteps(),
		out.GetCase().GetParallelSteps(),
		out.GetCase().GetTeardownSteps(),
	); err != nil {
		return nil, err
	}

	return out, nil
}
