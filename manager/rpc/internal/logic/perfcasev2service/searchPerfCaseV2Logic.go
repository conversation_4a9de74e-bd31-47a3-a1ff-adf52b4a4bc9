package perfcasev2servicelogic

import (
	"context"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sqlbuilder/rpc"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type SearchPerfCaseV2Logic struct {
	*BaseLogic
}

func NewSearchPerfCaseV2Logic(ctx context.Context, svcCtx *svc.ServiceContext) *SearchPerfCaseV2Logic {
	return &SearchPerfCaseV2Logic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// SearchPerfCaseV2 搜索压测用例
func (l *SearchPerfCaseV2Logic) SearchPerfCaseV2(in *pb.SearchPerfCaseV2Req) (out *pb.SearchPerfCaseV2Resp, err error) {
	out = &pb.SearchPerfCaseV2Resp{}

	// validate the project_id in req
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, in.GetProjectId()); err != nil {
		return nil, err
	}

	// validate the category_id in req
	if _, err = model.CheckCategoryByCategoryId(
		l.ctx, l.svcCtx.CategoryModel, in.GetProjectId(), common.ConstCategoryTreeTypePerfCase, in.GetCategoryId(),
	); err != nil {
		return nil, err
	}

	selectBuilder, countBuilder := l.svcCtx.PerfCaseV2Model.GenerateSearchPerfCaseV2SqlBuilder(
		model.SearchPerfCaseV2Req{
			BaseSearchReq: model.BaseSearchReq{
				ProjectID:  in.GetProjectId(),
				Condition:  in.GetCondition(),
				Pagination: in.GetPagination(),
				Sort:       rpc.ConvertSortFields(in.GetSort()),
			},
			CategoryID:    in.GetCategoryId(),
			DrillDown:     true,
			CategoryModel: l.svcCtx.CategoryModel,
		},
	)

	count, err := l.svcCtx.PerfCaseV2Model.FindCountPerfCasesV2(l.ctx, countBuilder)
	if err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()),
			"failed to count perf case, project_id: %s, category_id: %s, error: %+v",
			in.GetProjectId(), in.GetCategoryId(), err,
		)
	}
	out.TotalCount = uint64(count)
	out.TotalPage = 1

	perfCases, err := l.svcCtx.PerfCaseV2Model.FindPerfCasesV2(l.ctx, selectBuilder)
	if err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()),
			"failed to find perf case, project_id: %s, category_id: %s, error: %+v",
			in.GetProjectId(), in.GetCategoryId(), err,
		)
	}

	out.Items = make([]*pb.SearchPerfCaseV2Item, 0, len(perfCases))
	for _, perfCase := range perfCases {
		item := &pb.SearchPerfCaseV2Item{}
		if err = utils.Copy(item, perfCase, l.converters...); err != nil {
			return nil, errors.Wrapf(
				errorx.Err(errorx.CopyToStructFailure, err.Error()),
				"failed to copy perf case to response, perf case: %s, error: %+v",
				jsonx.MarshalIgnoreError(perfCase), err,
			)
		}

		if err = l.setReferenceQPS(
			l.ctx,
			item.GetProjectId(),
			item.GetProtocol(),
			item.GetSerialSteps(),
			item.GetParallelSteps(),
		); err != nil {
			return nil, err
		}

		out.Items = append(out.Items, item)
	}

	pagination := in.GetPagination()
	if pagination != nil {
		out.CurrentPage = pagination.GetCurrentPage()
		out.PageSize = pagination.GetPageSize()
		out.TotalPage = (out.TotalCount + out.PageSize - 1) / out.PageSize // round up
	}

	return out, nil
}
