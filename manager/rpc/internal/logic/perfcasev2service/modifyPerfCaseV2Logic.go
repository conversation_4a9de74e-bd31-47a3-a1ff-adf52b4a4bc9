package perfcasev2servicelogic

import (
	"context"
	"database/sql"
	"fmt"
	"time"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"
	"github.com/zeromicro/go-zero/core/stores/sqlx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/caller"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworkerv2/base"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"
	userpb "gitlab.ttyuyin.com/TestDevelopment/qet-backend-middleware/user/rpc/pb"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	commonpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
	commonutils "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/utils"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type ModifyPerfCaseV2Logic struct {
	*BaseLogic
}

func NewModifyPerfCaseV2Logic(ctx context.Context, svcCtx *svc.ServiceContext) *ModifyPerfCaseV2Logic {
	return &ModifyPerfCaseV2Logic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// ModifyPerfCaseV2 编辑压测用例
func (l *ModifyPerfCaseV2Logic) ModifyPerfCaseV2(in *pb.ModifyPerfCaseV2Req) (out *pb.ModifyPerfCaseV2Resp, err error) {
	// validate the project_id in req
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, in.GetProjectId()); err != nil {
		return nil, err
	}

	var c *model.Category
	// validate the category_id in req
	if c, err = model.CheckCategoryByCategoryId(
		l.ctx, l.svcCtx.CategoryModel, in.GetProjectId(), common.ConstCategoryTreeTypePerfCase, in.GetCategoryId(),
	); err != nil {
		return nil, err
	} else if c.CategoryType != common.ConstCategoryTypeDirectory {
		return nil, errors.WithStack(
			errorx.Errorf(
				errorx.ProhibitedBehavior,
				"the type of parent category[%s] does not support creation of sub category",
				c.CategoryType,
			),
		)
	}

	// validate the case_id in req
	origin, err := model.CheckPerfCaseV2ByCaseID(l.ctx, l.svcCtx.PerfCaseV2Model, in.GetProjectId(), in.GetCaseId())
	if err != nil {
		return nil, err
	}

	// validate perf case data
	if err = validatePerfCase(in); err != nil {
		return nil, err
	}

	rrs, err := l.svcCtx.PerfPlanCaseModel.FindByCaseID(l.ctx, in.GetProjectId(), in.GetCaseId())
	if err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()),
			"failed to find perf plan by perf case id, project_id: %s, case_id: %s, error: %+v",
			in.GetProjectId(), in.GetCaseId(), err,
		)
	}
	defer func() {
		if err == nil {
			l.sendUpdatePerfPlanByCaseTasks(rrs)
		}
	}()

	// validate the protocol in req
	protocol := protobuf.GetEnumStringOf(in.GetProtocol())
	if protocol != origin.Protocol && len(rrs) > 0 {
		return nil, errorx.Errorf(
			errorx.ProhibitedBehavior,
			"cannot to modify the protocol of perf case which has been associated with %d perf plan[s], please remove the perf case from perf plan first, project_id: %s, case_id: %s, protocol: %s -> %s",
			len(rrs), in.GetProjectId(), in.GetCaseId(), origin.Protocol, protocol,
		)
	}

	// validate the maintained_by in req
	if in.GetMaintainedBy() == "" && origin.MaintainedBy.Valid && origin.MaintainedBy.String != "" {
		return nil, errorx.Errorf(
			errorx.ProhibitedBehavior,
			"cannot change the maintainer of perf case to be empty, source: %s, target: %s",
			origin.MaintainedBy.String, in.GetMaintainedBy(),
		)
	}

	var perfCase *model.PerfCaseV2
	key := fmt.Sprintf(
		"%s:%s:%s", common.ConstLockPerfCaseProjectIDCaseIDPrefix, in.GetProjectId(), in.GetCaseId(),
	)
	fn := func() error {
		perfCase, err = l.modify(in, origin)
		return err
	}
	if err = caller.LockDo(l.svcCtx.Redis, key, fn); err != nil {
		return nil, err
	}

	out = &pb.ModifyPerfCaseV2Resp{Case: &pb.PerfCaseV2{}}
	if err = utils.Copy(out.Case, perfCase, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy perf case to response, perf case: %s, error: %+v",
			jsonx.MarshalIgnoreError(perfCase), err,
		)
	}

	return out, nil
}

func (l *ModifyPerfCaseV2Logic) modify(req *pb.ModifyPerfCaseV2Req, origin *model.PerfCaseV2) (
	*model.PerfCaseV2, error,
) {
	var (
		projectID     = req.GetProjectId()
		caseID        = req.GetCaseId()
		description   = req.GetDescription()
		rateLimits    = req.GetRateLimits()
		setupSteps    = req.GetSetupSteps()
		serialSteps   = req.GetSerialSteps()
		parallelSteps = req.GetParallelSteps()
		teardownSteps = req.GetTeardownSteps()
		maintainedBy  = req.GetMaintainedBy()

		tags                                                              sql.NullString
		setupStepsStr, serialStepsStr, parallelStepsStr, teardownStepsStr sql.NullString
		numberOfSteps                                                     int64
		err                                                               error
	)

	if len(req.GetTags()) > 0 {
		// 由于`protojson`只能序列化`[]proto.Message`，而`tags`字段类型为`[]string`，Go Struct跟PB没有大的区别，因此直接使用`jsonx`暂代`protojson`
		tags.String = jsonx.MarshalToStringIgnoreError(req.GetTags())
		tags.Valid = true
	}

	if maintainedBy != "" {
		var user *userpb.UserInfo
		if user, err = l.getUserInfoByAccount(maintainedBy); err != nil {
			return nil, err
		} else if !user.GetEnabled() {
			return nil, errorx.Errorf(
				errorx.ProhibitedBehavior,
				"cannot assign a disabled user as a maintainer of perf case, project_id: %s, case_id: %s, maintained_by: %s",
				projectID, caseID, maintainedBy,
			)
		}
	} else {
		// if the maintainer is not set, the creator is set as the maintainer
		maintainedBy = l.currentUser.Account
	}

	if len(setupSteps) > 0 {
		setupStepsStr.String = protobuf.MarshalJSONWithMessagesToStringIgnoreError(setupSteps)
		setupStepsStr.Valid = true
		numberOfSteps += int64(len(setupSteps))
	}
	if len(serialSteps) > 0 {
		serialStepsStr.String = protobuf.MarshalJSONWithMessagesToStringIgnoreError(serialSteps)
		serialStepsStr.Valid = true
		numberOfSteps += int64(len(serialSteps))
	}
	if len(parallelSteps) > 0 {
		parallelStepsStr.String = protobuf.MarshalJSONWithMessagesToStringIgnoreError(parallelSteps)
		parallelStepsStr.Valid = true
		numberOfSteps += int64(len(parallelSteps))
	}
	if len(teardownSteps) > 0 {
		teardownStepsStr.String = protobuf.MarshalJSONWithMessagesToStringIgnoreError(teardownSteps)
		teardownStepsStr.Valid = true
		numberOfSteps += int64(len(teardownSteps))
	}

	caseRPS := commonutils.GetMaxRPSFromRateLimits(rateLimits)
	stepsRPS := commonutils.GetMaxRPSFromSerialStepsAndParallelStepsV2(serialSteps, parallelSteps)

	now := time.Now()
	perfCase := &model.PerfCaseV2{
		Id:         origin.Id,
		ProjectId:  projectID,
		CategoryId: req.GetCategoryId(),
		CaseId:     caseID,
		Name:       req.GetName(),
		Description: sql.NullString{
			String: description,
			Valid:  description != "",
		},
		Tags:          tags,
		Protocol:      protobuf.GetEnumStringOf(req.GetProtocol()),
		RateLimits:    protobuf.MarshalJSONWithMessagesToStringIgnoreError(req.GetRateLimits()),
		SetupSteps:    setupStepsStr,
		SerialSteps:   serialStepsStr,
		ParallelSteps: parallelStepsStr,
		TeardownSteps: teardownStepsStr,
		NumberOfSteps: numberOfSteps,
		TargetRps:     utils.Max(caseRPS, stepsRPS),
		State:         origin.State,
		MaintainedBy: sql.NullString{
			String: maintainedBy,
			Valid:  maintainedBy != "",
		},
		CreatedBy: origin.CreatedBy,
		UpdatedBy: l.currentUser.Account,
		CreatedAt: origin.CreatedAt,
		UpdatedAt: now,
	}

	if err = l.svcCtx.PerfCaseV2Model.Trans(
		l.ctx, func(context context.Context, session sqlx.Session) error {
			// delete perf case steps
			if _, err := l.svcCtx.PerfCaseStepV2Model.DeleteByCaseID(
				context, session, perfCase.ProjectId, perfCase.CaseId,
			); err != nil {
				return errors.Wrapf(
					errorx.Err(errorx.DBError, err.Error()),
					"failed to remove perf case steps, project_id: %s, case_id: %s, error: %+v",
					perfCase.ProjectId, perfCase.CaseId, err,
				)
			}

			// create perf case steps
			for _, v := range []struct {
				stepType commonpb.PerfCaseStepType
				steps    []*commonpb.PerfCaseStepV2
			}{
				{
					stepType: commonpb.PerfCaseStepType_PerfCaseStepType_SETUP,
					steps:    setupSteps,
				},
				{
					stepType: commonpb.PerfCaseStepType_PerfCaseStepType_SERIAL,
					steps:    serialSteps,
				},
				{
					stepType: commonpb.PerfCaseStepType_PerfCaseStepType_PARALLEL,
					steps:    parallelSteps,
				},
				{
					stepType: commonpb.PerfCaseStepType_PerfCaseStepType_TEARDOWN,
					steps:    teardownSteps,
				},
			} {
				if err := l.createPerfCaseSteps(context, session, perfCase, caseRPS, v.stepType, v.steps); err != nil {
					return err
				}
			}

			// update perf case
			if _, err := l.svcCtx.PerfCaseV2Model.Update(context, session, perfCase); err != nil {
				return errors.Wrapf(
					errorx.Err(errorx.DBError, err.Error()),
					"failed to update values to table, table: %s, values: %s, error: %+v",
					l.svcCtx.PerfCaseV2Model.Table(), jsonx.MarshalIgnoreError(perfCase), err,
				)
			}

			// update the new tag and tag reference of perf case
			if err := l.createTagLogic.CreateTagAndReferenceForInternal(
				context, session, types.CreateOrUpdateTagReference{
					ProjectId:     perfCase.ProjectId,
					ReferenceType: common.ConstReferenceTypePerfCase,
					ReferenceId:   perfCase.CaseId,
					Tags:          req.GetTags(),
				},
			); err != nil {
				return err
			}

			return nil
		},
	); err != nil {
		return nil, err
	}

	return perfCase, nil
}

func (l *ModifyPerfCaseV2Logic) sendUpdatePerfPlanByCaseTasks(rrs []*model.PerfPlanCaseRelationship) {
	for _, rr := range rrs {
		payload := protobuf.MarshalJSONIgnoreError(
			&pb.UpdatePerfPlanByCaseTaskInfo{
				ProjectId: rr.ProjectId,
				PlanId:    rr.PlanId,
				CaseId:    rr.CaseId,
			},
		)
		if _, err := l.svcCtx.ManagerProducer.Send(
			l.ctx, base.NewTask(
				constants.MQTaskTypeManagerHandleUpdatePerfPlanByCase,
				payload,
				base.WithRetentionOptions(5*time.Minute),
			), base.QueuePriorityDefault,
		); err != nil {
			l.Errorf(
				"failed to send task to mq, type: %s, payload: %s, error: %+v",
				constants.MQTaskTypeManagerHandleUpdatePerfPlanByCase, payload, err,
			)
		} else {
			l.Debugf(
				"send task to mq successfully, type: %s, payload: %s",
				constants.MQTaskTypeManagerHandleUpdatePerfPlanByCase, payload,
			)
		}
	}
}
