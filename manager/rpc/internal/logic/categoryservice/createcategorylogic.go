package categoryservicelogic

import (
	"context"
	"database/sql"
	"time"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/stores/sqlx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type CreateCategoryLogic struct {
	*BaseLogic
}

func NewCreateCategoryLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CreateCategoryLogic {
	return &CreateCategoryLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// CreateCategory 创建分类
func (l *CreateCategoryLogic) CreateCategory(in *pb.CreateCategoryReq) (resp *pb.CreateCategoryResp, err error) {
	// validate the project_id in req
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, in.GetProjectId()); err != nil {
		return nil, err
	}

	// validate the name in req
	if err = common.CheckCategoryNameByType(in.GetName(), in.GetType()); err != nil {
		return nil, err
	}

	category, err := l.CreateCategoryForInternal(
		l.ctx, nil, CreateCategoryInternalReq{
			CreateCategoryReq: in,
			CategoryType:      common.ConstCategoryTypeDirectory,
		},
	)
	if err != nil {
		return nil, err
	}

	resp = &pb.CreateCategoryResp{Category: &pb.Category{}}
	if err = utils.Copy(resp.Category, category); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy category[%+v] to response, error: %+v",
			category, err,
		)
	}

	return resp, nil
}

func (l *CreateCategoryLogic) CreateCategoryForInternal(
	ctx context.Context, session sqlx.Session, req CreateCategoryInternalReq,
) (*model.Category, error) {
	var (
		projectId = req.GetProjectId()
		typ       = req.GetType()
		parentId  = req.GetParentId()
		index     = req.GetIndex()
	)
	categoryId, err := l.generateCategoryId(projectId, typ)
	if err != nil {
		return nil, err
	}

	// do the check if not an internal call and parent_id isn't an empty string
	if !req.IsInternalCall && parentId != "" {
		var (
			c *model.Category
			i int64
		)

		// validate the parent category by parent_id
		c, err = model.CheckCategoryByCategoryId(l.ctx, l.svcCtx.CategoryModel, projectId, typ, parentId)
		if err != nil {
			return nil, err
		} else if c.CategoryType != common.ConstCategoryTypeDirectory {
			return nil, errorx.Errorf(
				errorx.ProhibitedBehavior,
				"the type of parent category[%s] does not support creation of sub category",
				c.CategoryType,
			)
		} else if req.Type == common.ConstCategoryTreeTypeComponentGroup && c.Name == common.ConstCategoryRootAllComponentGroup {
			return nil, errorx.Errorf(
				errorx.ProhibitedBehavior,
				"cannot create category under the root of component group category tree[%s]",
				common.ConstCategoryRootAllComponentGroup,
			)
		} else if req.Type == common.ConstCategoryTreeTypeInterfaceSchema && c.Name == common.ConstCategorySubRootTDSService {
			return nil, errorx.Errorf(
				errorx.ProhibitedBehavior,
				"cannot create category under the builtin tds service category tree[%s]",
				common.ConstCategorySubRootTDSService,
			)
		}

		req.RootType = c.RootType.String

		if index == 0 {
			// get the max index under the parent category
			i, err = l.svcCtx.CategoryTreeModel.FindMaxIndexInSameLevel(l.ctx, projectId, parentId)
			if err != nil && !errors.Is(err, model.ErrNotFound) {
				return nil, errors.Wrapf(
					errorx.Err(errorx.DBError, err.Error()),
					"failed to find the max index of category[%s]'s children categories, error: %+v",
					c.Name, err,
				)
			}
			req.SetIndex(i + 1)
		}
	} else if index == 0 {
		req.SetIndex(1)
	}

	var b int64
	if req.Builtin {
		b = 1
	}

	category := &model.Category{
		ProjectId:    projectId,
		Type:         typ,
		CategoryId:   categoryId,
		CategoryType: req.CategoryType,
		RootType: sql.NullString{
			String: req.RootType,
			Valid:  req.RootType != "",
		},
		NodeType: sql.NullString{
			String: req.NodeType,
			Valid:  req.NodeType != "",
		},
		NodeId: sql.NullString{
			String: req.NodeId,
			Valid:  req.NodeId != "",
		},
		Name: req.GetName(),
		Description: sql.NullString{
			String: req.GetDescription(),
			Valid:  req.GetDescription() != "",
		},
		Builtin:   b,
		CreatedBy: l.currentUser.Account,
		UpdatedBy: l.currentUser.Account,
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}
	categoryTree := &model.CategoryTree{
		ProjectId:  projectId,
		Ancestor:   categoryId,
		Descendant: categoryId,
		Depth:      0,
		Index:      req.GetIndex(),
		CreatedBy:  l.currentUser.Account,
		UpdatedBy:  l.currentUser.Account,
		CreatedAt:  time.Now(),
		UpdatedAt:  time.Now(),
	}

	fn := func(context context.Context, session sqlx.Session) error {
		if _, err = l.svcCtx.CategoryModel.Insert(context, session, category); err != nil {
			return errors.Wrapf(
				errorx.Err(errorx.DBError, err.Error()),
				"failed to insert table[%s] with values[%+v], error: %+v",
				l.svcCtx.CategoryModel.Table(), category, err,
			)
		}

		if _, err = l.svcCtx.CategoryTreeModel.InsertTree(context, session, parentId, categoryTree); err != nil {
			return errors.Wrapf(
				errorx.Err(errorx.DBError, err.Error()),
				"failed to insert table[%s] with values[parentId: %s, %+v], error: %+v",
				l.svcCtx.CategoryTreeModel.Table(), parentId, categoryTree, err,
			)
		}

		return nil
	}

	if session != nil {
		if ctx == nil {
			ctx = l.ctx
		}
		if err = fn(ctx, session); err != nil {
			return nil, err
		}
	} else if err = l.svcCtx.CategoryModel.Trans(l.ctx, fn); err != nil {
		return nil, err
	}

	return category, nil
}
