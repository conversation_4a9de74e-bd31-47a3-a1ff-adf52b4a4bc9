package categoryservicelogic

import (
	"context"
	"fmt"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/stores/sqlx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/redislock"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type MoveCategoryTreeLogic struct {
	*BaseLogic
}

func NewMoveCategoryTreeLogic(ctx context.Context, svcCtx *svc.ServiceContext) *MoveCategoryTreeLogic {
	return &MoveCategoryTreeLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// MoveCategoryTree 移动分类树
/*
如，当前的模块树如下：

         0
       /  \
      1    7
    / | \   \
   2  3  4   8
        / \
       5   6
            \
             9

把`6`及其子树移动到`1`下的`2`的后面时，
1. 跟`6`同层且排在其后面的全部向前移动一位
2. 删除 0-6, 0-9, 1-6, 1-9, 4-6, 4-9（不删除 6-6, 6-9, 9-9）
3. 新增 0-6, 0-9, 1-6, 1-9（注意距离的变化以及 1-6 的序号）（加上上面没有删除的 6-6, 6-9, 9-9）
4. 跟`6`同层且排在其后面的全部向后移动一位
*/
func (l *MoveCategoryTreeLogic) MoveCategoryTree(in *pb.MoveCategoryTreeReq) (
	resp *pb.MoveCategoryTreeResp, err error,
) {
	// validate the project_id in req
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, in.GetProjectId()); err != nil {
		return nil, err
	}

	if in.GetSourceId() == in.GetSiblingId() {
		l.Infof(
			"the source category[%s] and the sibling category[%s] are the same, no need to move",
			in.GetSourceId(), in.GetSiblingId(),
		)
		return &pb.MoveCategoryTreeResp{}, nil
	}

	if err = l.MoveCategoryTreeForInternal(l.ctx, nil, in); err != nil {
		return nil, err
	}

	return &pb.MoveCategoryTreeResp{}, nil
}

func (l *MoveCategoryTreeLogic) MoveCategoryTreeForInternal(
	ctx context.Context, session sqlx.Session, req *pb.MoveCategoryTreeReq,
) (err error) {
	// validate the source_id in req
	source, err := model.CheckCategoryByCategoryId(
		l.ctx, l.svcCtx.CategoryModel, req.GetProjectId(), req.GetType(), req.GetSourceId(),
	)
	if err != nil {
		return err
	} else if source.Builtin != 0 {
		return errors.WithStack(
			errorx.Errorf(
				errorx.ProhibitedBehavior, "cannot move the builtin category[%s]", source.Name,
			),
		)
	}

	// validate the target_id in req
	target, err := model.CheckCategoryByCategoryId(
		l.ctx, l.svcCtx.CategoryModel, req.GetProjectId(), req.GetType(), req.GetTargetId(),
	)
	if err != nil {
		return err
	} else if target.CategoryType != common.ConstCategoryTypeDirectory {
		return errors.WithStack(
			errorx.Errorf(
				errorx.ProhibitedBehavior,
				"cannot move a category[%s] to under a non directory category[%s]",
				source.Name, target.Name,
			),
		)
	}

	// the root of component group category tree cannot be the target category
	if req.Type == common.ConstCategoryTreeTypeComponentGroup && target.Name == common.ConstCategoryRootAllComponentGroup {
		return errors.WithStack(
			errorx.Errorf(
				errorx.ProhibitedBehavior,
				"cannot move a category[%s] to under the root category[%s] of component group category tree",
				source.Name, target.Name,
			),
		)
	}

	// the target category cannot be a descendant of the source category
	if _, err = l.checkSourceDescendants(req, source, target); err != nil {
		return err
	}

	parent, err := l.checkSourceParent(req, source)
	if err != nil {
		return err
	}
	index, err := l.checkAndCalculateIndex(req, source, target, parent)
	if err != nil {
		return err
	}

	// acquire redis lock
	key := fmt.Sprintf("%s:%s:%s", common.ConstLockCategoryTreeProjectIdTypePrefix, req.ProjectId, req.Type)
	lock, err := redislock.NewRedisLockAndAcquire(l.svcCtx.Redis, key, redislock.WithExpire(common.ConstLockExpireTime))
	if err != nil {
		return err
	}
	defer func() {
		// release redis lock
		e := lock.Release()
		if e != nil {
			l.Logger.Error(e)
			if re, ok := errorx.RootError(e); ok && re.Code() != errorx.ReleaseRedisLockFailure && err == nil {
				err = e
			}
		}
	}()

	treeNode := &model.CategoryTree{
		ProjectId:  req.GetProjectId(),
		Ancestor:   req.GetTargetId(),
		Descendant: req.GetSourceId(),
		Depth:      1,
		Index:      index,
		CreatedBy:  source.CreatedBy,
		UpdatedBy:  l.currentUser.Account,
		// CreatedAt:  source.CreatedAt,
		// UpdatedAt:  time.Now(),
	}
	return l.move(
		ctx, session, req, source, target, treeNode, parent.CategoryId == target.CategoryId, req.BeingModified,
	)
}

func (l *MoveCategoryTreeLogic) checkSourceDescendants(
	req *pb.MoveCategoryTreeReq, source, target *model.Category,
) ([]*model.Category, error) {
	var (
		rootType      string
		nodeType      string
		componentName string
	)

	condition := model.GetCategoryTreeCondition{
		ProjectId:  req.GetProjectId(),
		Type:       req.GetType(),
		CategoryId: req.GetSourceId(),
	}
	descendants, err := l.svcCtx.CategoryModel.FindDescendantCategories(l.ctx, condition)
	if err != nil && !errors.Is(err, model.ErrNotFound) {
		return nil, errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()),
			"failed to find descendant categories of source category[%s]",
			source.Name,
		)
	}
	for _, descendant := range descendants {
		// source descendant categories cannot be the target category
		if descendant.CategoryId == req.GetTargetId() {
			return nil, errors.WithStack(
				errorx.Errorf(
					errorx.ProhibitedBehavior,
					"cannot move a category[%s] to under itself or sub categories of itself[%s]",
					source.Name, target.Name,
				),
			)
		} else if descendant.CategoryType == common.ConstCategoryTypeFile && descendant.NodeType.Valid && nodeType == "" {
			rootType = descendant.RootType.String
			nodeType = descendant.NodeType.String
			componentName = descendant.Name
		}
	}

	// cannot move business component group to under the setup-teardown category and vice versa
	if req.GetType() == common.ConstCategoryTreeTypeComponentGroup && rootType != "" && rootType != target.RootType.String {
		return nil, errors.WithStack(
			errorx.Errorf(
				errorx.ProhibitedBehavior,
				"cannot move the component group[%s] to the category[%s] which is different root type[%s -> %s]",
				componentName, target.Name, rootType, target.RootType.String,
			),
		)

		// 增加了`root_type`字段后，无需要下面代码的判断了
		//condition = model.GetCategoryTreeCondition{
		//	ProjectId:  req.ProjectId,
		//	Type:       req.Type,
		//	CategoryId: req.TargetId,
		//}
		//ancestors, err := l.svcCtx.CategoryModel.FindAncestralCategories(l.ctx, l.svcCtx.CategoryTreeModel, condition)
		//if err != nil && err != model.ErrNotFound {
		//	return nil, errors.Wrapf(errorx.Err(errorx.DBError, err.Error()), "failed to find ancestral categories of target category[%s]", target.Name)
		//} else if ancestors != nil {
		//	rootName := logic.ConstCategorySubRootBusinessComponent
		//	if nodeType == logic.ConstComponentGroupTypeSetup || nodeType == logic.ConstComponentGroupTypeTeardown {
		//		rootName = logic.ConstCategorySubRootSetupTeardownComponent
		//	}
		//
		//	for _, ancestor := range ancestors {
		//		/*
		//			1. source: setup or teardown, target: business
		//			2. source: single or group, target: setup-teardown
		//		*/
		//		if ancestor.Name == logic.ConstCategorySubRootBusinessComponent || ancestor.Name == logic.ConstCategorySubRootSetupTeardownComponent {
		//			if ancestor.Name != rootName {
		//				return nil, errors.WithStack(errorx.Err(codes.ProhibitedBehavior, fmt.Sprintf("cannot move the component group[%s] to under the category tree[%s]", componentName, ancestor.Name)))
		//			}
		//			break
		//		}
		//	}
		//}
	}

	return descendants, nil
}

func (l *MoveCategoryTreeLogic) checkTargetChildren(
	req *pb.MoveCategoryTreeReq, target *model.Category,
) ([]*model.Category, *model.Category, error) {
	var sibling *model.Category

	condition := model.GetCategoryTreeCondition{
		ProjectId:  req.GetProjectId(),
		Type:       req.GetType(),
		CategoryId: req.GetTargetId(),
	}
	children, err := l.svcCtx.CategoryModel.FindChildrenCategories(l.ctx, condition)
	if err != nil && !errors.Is(err, model.ErrNotFound) {
		return nil, nil, errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()),
			"failed to find children categories of target category[%s]",
			target.Name,
		)
	}
	for _, child := range children {
		child := child
		if child.CategoryId == req.SiblingId {
			sibling = child
			break
		}
	}
	if sibling == nil {
		return nil, nil, errors.WithStack(
			errorx.Errorf(
				errorx.ProhibitedBehavior,
				"the sibling category[%s] is not a child category of the target category[%s]",
				req.GetSiblingId(), target.Name,
			),
		)
	}

	return children, sibling, nil
}

func (l *MoveCategoryTreeLogic) checkSourceParent(req *pb.MoveCategoryTreeReq, source *model.Category) (
	*model.Category, error,
) {
	condition := model.GetCategoryTreeCondition{
		ProjectId:  req.GetProjectId(),
		Type:       req.GetType(),
		CategoryId: req.GetSourceId(),
	}
	parent, err := l.svcCtx.CategoryModel.FindParentCategory(l.ctx, condition)
	if err != nil {
		if !errors.Is(err, model.ErrNotFound) {
			return nil, errors.Wrapf(
				errorx.Err(errorx.DBError, err.Error()),
				"failed to find parent category of source category[%s], error: %+v",
				source.Name, err,
			)
		} else {
			return nil, errors.WithStack(
				errorx.Errorf(
					errorx.NotExists, "parent category of source category[%s] doesn't exist", source.Name,
				),
			)
		}
	}

	return parent, nil
}

func (l *MoveCategoryTreeLogic) checkAndCalculateIndex(
	req *pb.MoveCategoryTreeReq, source, target, parent *model.Category,
) (int64, error) {
	var (
		index int64 = 1

		moveType  = req.GetMoveType()
		siblingId = req.GetSiblingId()
	)

	if moveType != common.ConstCategoryMoveTypeInner && siblingId != "" {
		// the sibling category must be a child of the target category
		_, sibling, err := l.checkTargetChildren(req, target)
		if err != nil {
			return index, err
		}

		index, err = l.svcCtx.CategoryTreeModel.FindIndexByProjectIdParentIdChildId(
			l.ctx, req.GetProjectId(), req.GetTargetId(), siblingId,
		)
		if err != nil {
			return index, errors.Wrapf(
				errorx.Err(errorx.DBError, err.Error()),
				"failed to find the index of the sibling category[%s]",
				sibling.Name,
			)
		}
	}

	if parent.CategoryId == target.CategoryId && moveType != common.ConstCategoryMoveTypeInner && siblingId != "" {
		// in the same parent category
		if si, err := l.svcCtx.CategoryTreeModel.FindIndexByProjectIdParentIdChildId(
			l.ctx, req.GetProjectId(), parent.CategoryId, req.GetSourceId(),
		); err != nil {
			return index, errors.Wrapf(
				errorx.Err(errorx.DBError, err.Error()),
				"failed to find the index of the source category[%s]",
				source.Name,
			)
		} else if moveType == common.ConstCategoryMoveTypeBefore && si < index {
			// move down
			index--
		} else if moveType == common.ConstCategoryMoveTypeAfter && si > index {
			// move up
			index++
		}
	} else if parent.CategoryId != target.CategoryId && moveType == common.ConstCategoryMoveTypeAfter && siblingId != "" {
		// in the different parent category
		index++
	}

	return index, nil
}

func (l *MoveCategoryTreeLogic) move(
	ctx context.Context, session sqlx.Session, req *pb.MoveCategoryTreeReq, source, target *model.Category,
	node *model.CategoryTree, isSameLevel, beingModified bool,
) error {
	fn := func(context context.Context, session sqlx.Session) error {
		var err error

		// only in this case[file node && node id is valid && move to different level && not in modify process],
		// update the category id of the item[component group or interface document or interface schema]
		//
		// bug feedback:
		// in modify process, just update the category id of the component group with original version, thus do not update this item
		if source.CategoryType == common.ConstCategoryTypeFile && source.NodeId.Valid && !isSameLevel && !beingModified {
			switch source.Type {
			case common.ConstCategoryTreeTypeComponentGroup:
				err = l.updateComponentGroupCategoryId(context, session, source, target)
			case common.ConstCategoryTreeTypeInterfaceDocument:
				err = l.updateInterfaceDocumentCategoryId(context, session, source, target)
			case common.ConstCategoryTreeTypeInterfaceSchema:
				err = l.updateInterfaceSchemaCategoryId(context, session, source, target)
			default:
				err = errors.WithStack(
					errorx.Errorf(
						errorx.DoesNotSupport, "the type of category tree[%s] doesn't support", source.Type,
					),
				)
			}

			if err != nil {
				return err
			}
		}

		if source.RootType != target.RootType {
			if _, err = l.svcCtx.CategoryModel.UpdateDescendantRootType(
				context, session, req.GetProjectId(), req.GetType(), req.GetSourceId(), target.RootType.String,
			); err != nil {
				return errors.Wrapf(
					errorx.Err(errorx.DBError, err.Error()),
					"failed to update the root type of source category tree[%s] to [%s]",
					source.RootType.String, target.RootType.String,
				)
			}
		}

		if err = l.svcCtx.CategoryTreeModel.MoveTree(context, session, node, isSameLevel); err != nil {
			return errors.Wrapf(
				errorx.Err(errorx.DBError, err.Error()),
				"failed to move the source category[%s] to the target category[%s]",
				source.Name, target.Name,
			)
		}

		return nil
	}

	if session != nil {
		if ctx == nil {
			ctx = l.ctx
		}
		return fn(ctx, session)
	}
	return l.svcCtx.CategoryModel.Trans(l.ctx, fn)
}

func (l *MoveCategoryTreeLogic) updateComponentGroupCategoryId(
	context context.Context, session sqlx.Session, source, target *model.Category,
) error {
	// get the component group by component group id
	cg, err := model.CheckComponentGroupByComponentGroupId(
		context, l.svcCtx.ComponentGroupModel, source.ProjectId, source.NodeId.String, "",
	)
	if err != nil {
		return err
	}

	// move the component group to other category
	tmp := cg.CategoryId
	cg.CategoryId = target.CategoryId
	if _, err = l.svcCtx.ComponentGroupModel.UpdateTX(context, session, cg); err != nil {
		return errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()),
			"failed to update the category id of source component group[%s] to [%s], error: %+v",
			tmp, target.CategoryId, err,
		)
	}

	return nil
}

func (l *MoveCategoryTreeLogic) updateInterfaceDocumentCategoryId(
	context context.Context, session sqlx.Session, source, target *model.Category,
) error {
	// get the interface document by interface document id
	id, err := model.CheckInterfaceDocumentByDocumentId(
		context, l.svcCtx.InterfaceDocumentModel, source.ProjectId, source.NodeId.String,
	)
	if err != nil {
		return err
	}

	// move the interface document to other category
	tmp := id.CategoryId
	id.CategoryId = target.CategoryId
	if _, err = l.svcCtx.InterfaceDocumentModel.UpdateTX(context, session, id); err != nil {
		return errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()),
			"failed to update the category id of source interface document[%s] to [%s], error: %+v",
			tmp, target.CategoryId, err,
		)
	}

	return nil
}

func (l *MoveCategoryTreeLogic) updateInterfaceSchemaCategoryId(
	context context.Context, session sqlx.Session, source, target *model.Category,
) error {
	// get the interface schema data by interface schema id
	is, err := model.CheckInterfaceSchemaBySchemaId(
		context, l.svcCtx.InterfaceSchemaModel, source.ProjectId, source.NodeId.String,
	)
	if err != nil {
		return err
	}

	// move the interface schema to other category
	tmp := is.CategoryId
	is.CategoryId = target.CategoryId
	if _, err = l.svcCtx.InterfaceSchemaModel.UpdateTX(context, session, is); err != nil {
		return errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()),
			"failed to update the category id of source interface schema[%s] to [%s], error: %+v",
			tmp, target.CategoryId, err,
		)
	}

	return nil
}
