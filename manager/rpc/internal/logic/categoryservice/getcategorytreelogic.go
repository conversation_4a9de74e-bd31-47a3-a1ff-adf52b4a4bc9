package categoryservicelogic

import (
	"context"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/stores/sqlx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type GetCategoryTreeLogic struct {
	*BaseLogic

	createCategoryLogic *CreateCategoryLogic
}

func NewGetCategoryTreeLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetCategoryTreeLogic {
	return &GetCategoryTreeLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),

		createCategoryLogic: NewCreateCategoryLogic(ctx, svcCtx),
	}
}

// GetCategoryTree 获取分类树
func (l *GetCategoryTreeLogic) GetCategoryTree(in *pb.GetCategoryTreeReq) (resp *pb.GetCategoryTreeResp, err error) {
	// validate the project_id in req
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, in.GetProjectId()); err != nil {
		return nil, err
	}

	if in.GetCategoryId() != "" {
		// validate the category_id in req
		if _, err = model.CheckCategoryByCategoryId(
			l.ctx, l.svcCtx.CategoryModel, in.GetProjectId(), in.GetType(), in.GetCategoryId(),
		); err != nil {
			return nil, err
		}
	} else {
		// get the root category by type
		var root *model.Category
		root, err = model.GetRootCategoryByType(l.ctx, l.svcCtx.CategoryModel, in.GetProjectId(), in.GetType())
		if err != nil {
			if re, ok := errorx.RootError(err); !ok || re.Code() != errorx.NotExists {
				return nil, err
			}

			// create the root category of the project automatically
			if err = l.createProjectCategoryTree(in); err != nil {
				return nil, err
			}

			// get the root category by type again
			root, err = model.GetRootCategoryByType(l.ctx, l.svcCtx.CategoryModel, in.GetProjectId(), in.GetType())
			if err != nil {
				return nil, err
			}
		}

		in.SetCategoryId(root.CategoryId)
	}

	categories, err := l.getCategoryTree(in)
	if err != nil {
		return nil, err
	}

	return &pb.GetCategoryTreeResp{CategoryTree: categories}, nil
}

func (l *GetCategoryTreeLogic) getCategoryTree(req *pb.GetCategoryTreeReq) ([]*pb.Category, error) {
	empty := make([]*pb.Category, 0)

	nodes, err := l.svcCtx.CategoryModel.GetTreeNodes(
		l.ctx, model.GetCategoryTreeCondition{
			ProjectId:     req.GetProjectId(),
			Type:          req.GetType(),
			CategoryId:    req.GetCategoryId(),
			Depth:         req.GetDepth(),
			OnlyDirectory: req.GetOnlyDirectory(),
			ExcludeSelf:   !req.GetIncludeSelf(),
		},
	)
	if err != nil {
		if !errors.Is(err, model.ErrNotFound) {
			return nil, errors.Wrapf(
				errorx.Err(errorx.DBError, err.Error()),
				"failed to get category tree nodes with project_id[%s], type[%s] and category_id[%s], error: %+v",
				req.GetProjectId(), req.GetType(), req.GetCategoryId(), err,
			)
		} else {
			return empty, nil
		}
	}

	// 由于SQL暂时去掉了排序，因此改为通过程序进行排序
	// `depth` DESC, `index` ASC, `created_at` ASC
	//sort.SliceStable(nodes, func(i, j int) bool {
	//	if nodes[i].Depth != nodes[j].Depth {
	//		return nodes[i].Depth > nodes[j].Depth
	//	} else if nodes[i].Index != nodes[j].Index {
	//		return nodes[i].Index < nodes[j].Index
	//	} else if nodes[i].CreatedAt.Equal(nodes[j].CreatedAt) {
	//		return nodes[i].CreatedAt.UnixMilli() < nodes[j].CreatedAt.UnixMilli()
	//	}
	//	return false
	//})

	m := make(CategoryChildrenCache)

	for _, node := range nodes {
		category := &pb.Category{}
		if err = utils.Copy(category, node); err != nil {
			return nil, errors.Wrapf(
				errorx.Err(errorx.CopyToStructFailure, err.Error()),
				"failed to copy category node[%+v] to response, error: %+v",
				node, err,
			)
		}

		if v, ok := m[node.CategoryId]; ok {
			category.Children = v

			if node.CategoryType == common.ConstCategoryTypeDirectory {
				for _, x := range v {
					category.Amount += x.Amount
				}
			}
		}

		if node.CategoryId == node.ParentId {
			m[node.ParentId] = []*pb.Category{category}
		} else if _, ok := m[node.ParentId]; !ok {
			m[node.ParentId] = []*pb.Category{category}
		} else {
			m[node.ParentId] = append(m[node.ParentId], category)
		}
	}

	resp, ok := m[req.GetCategoryId()]
	if !ok {
		return empty, nil
	}

	return resp, nil
}

type relation struct {
	Type         string
	CategoryType string
	RootType     string
	NodeType     string
	NodeId       string
	Name         string
	Children     []relation
}

var categoryBuiltinRelations = map[string][]relation{
	common.ConstCategoryTreeTypeInterfaceDocument: {
		{
			// 接口文档
			Type:         common.ConstCategoryTreeTypeInterfaceDocument,
			CategoryType: common.ConstCategoryTypeDirectory,
			Name:         common.ConstCategoryRootAllDocument,
		},
	},
	common.ConstCategoryTreeTypeInterfaceSchema: {
		{
			// 接口数据模型
			Type:         common.ConstCategoryTreeTypeInterfaceSchema,
			CategoryType: common.ConstCategoryTypeDirectory,
			Name:         common.ConstCategoryRootAllSchema,
			Children: []relation{
				{
					Type:         common.ConstCategoryTreeTypeInterfaceSchema,
					CategoryType: common.ConstCategoryTypeDirectory,
					RootType:     common.ConstCategorySubRootTDSService,
					Name:         common.ConstCategorySubRootTDSService,
					Children: []relation{
						{
							Type:         common.ConstCategoryTreeTypeInterfaceSchema,
							CategoryType: common.ConstCategoryTypeFile,
							RootType:     common.ConstCategorySubRootTDSService,
							NodeType:     common.ConstInterfaceDefinitionTypeSchema,
							NodeId:       common.ConstTDSCommonCallReqSchemaId,
							Name:         common.ConstTDSCommonCallReqSchemaName,
						},
						{
							Type:         common.ConstCategoryTreeTypeInterfaceSchema,
							CategoryType: common.ConstCategoryTypeFile,
							RootType:     common.ConstCategorySubRootTDSService,
							NodeType:     common.ConstInterfaceDefinitionTypeSchema,
							NodeId:       common.ConstTDSSearchClientSchemaId,
							Name:         common.ConstTDSSearchClientSchemaName,
						},
						{
							Type:         common.ConstCategoryTreeTypeInterfaceSchema,
							CategoryType: common.ConstCategoryTypeFile,
							RootType:     common.ConstCategorySubRootTDSService,
							NodeType:     common.ConstInterfaceDefinitionTypeSchema,
							NodeId:       common.ConstTDSFixDictSchemaId,
							Name:         common.ConstTDSFixDictSchemaName,
						},
						{
							Type:         common.ConstCategoryTreeTypeInterfaceSchema,
							CategoryType: common.ConstCategoryTypeFile,
							RootType:     common.ConstCategorySubRootTDSService,
							NodeType:     common.ConstInterfaceDefinitionTypeSchema,
							NodeId:       common.ConstTDSCommonRespSchemaId,
							Name:         common.ConstTDSCommonRespSchemaName,
						},
						{
							Type:         common.ConstCategoryTreeTypeInterfaceSchema,
							CategoryType: common.ConstCategoryTypeFile,
							RootType:     common.ConstCategorySubRootTDSService,
							NodeType:     common.ConstInterfaceDefinitionTypeSchema,
							NodeId:       common.ConstTDSErrorRespSchemaId,
							Name:         common.ConstTDSErrorRespSchemaName,
						},
					},
				},
				{
					Type:         common.ConstCategoryTreeTypeInterfaceSchema,
					CategoryType: common.ConstCategoryTypeDirectory,
					RootType:     common.ConstCategorySubRootTTMetaClient,
					Name:         common.ConstCategorySubRootTTMetaClient,
					Children: []relation{
						{
							Type:         common.ConstCategoryTreeTypeInterfaceSchema,
							CategoryType: common.ConstCategoryTypeFile,
							RootType:     common.ConstCategorySubRootTTMetaClient,
							NodeType:     common.ConstInterfaceDefinitionTypeSchema,
							NodeId:       common.ConstTTMetaCommonCallReqSchemaId,
							Name:         common.ConstTTMetaClientCommonCallReqSchemaName,
						},
						{
							Type:         common.ConstCategoryTreeTypeInterfaceSchema,
							CategoryType: common.ConstCategoryTypeFile,
							RootType:     common.ConstCategorySubRootTDSService,
							NodeType:     common.ConstInterfaceDefinitionTypeSchema,
							NodeId:       common.ConstTTMetaClientSearchClientSchemaId,
							Name:         common.ConstTTMetaClientSearchClientSchemaName,
						},
						{
							Type:         common.ConstCategoryTreeTypeInterfaceSchema,
							CategoryType: common.ConstCategoryTypeFile,
							RootType:     common.ConstCategorySubRootTDSService,
							NodeType:     common.ConstInterfaceDefinitionTypeSchema,
							NodeId:       common.ConstTTMetaCommonRespSchemaId,
							Name:         common.ConstTTMetaClientCommonRespSchemaName,
						},
					},
				},
				{
					Type:         common.ConstCategoryTreeTypeInterfaceSchema,
					CategoryType: common.ConstCategoryTypeDirectory,
					RootType:     common.ConstCategorySubRootApiProxy,
					Name:         common.ConstCategorySubRootApiProxy,
					Children: []relation{
						{
							Type:         common.ConstCategoryTreeTypeInterfaceSchema,
							CategoryType: common.ConstCategoryTypeFile,
							RootType:     common.ConstCategorySubRootApiProxy,
							NodeType:     common.ConstInterfaceDefinitionTypeSchema,
							NodeId:       common.ConstApiProxyCommonApiCallReqSchemaId,
							Name:         common.ConstApiProxyCommonApiCallReqSchemaName,
						},
						{
							Type:         common.ConstCategoryTreeTypeInterfaceSchema,
							CategoryType: common.ConstCategoryTypeFile,
							RootType:     common.ConstCategorySubRootApiProxy,
							NodeType:     common.ConstInterfaceDefinitionTypeSchema,
							NodeId:       common.ConstApiProxyCommonRespSchemaId,
							Name:         common.ConstApiProxyCommonRespSchemaName,
						},
						{
							Type:         common.ConstCategoryTreeTypeInterfaceSchema,
							CategoryType: common.ConstCategoryTypeFile,
							RootType:     common.ConstCategorySubRootApiProxy,
							NodeType:     common.ConstInterfaceDefinitionTypeSchema,
							NodeId:       common.ConstApiProxyCommonRespDataSchemaId,
							Name:         common.ConstApiProxyCommonRespDataSchemaName,
						},
						{
							Type:         common.ConstCategoryTreeTypeInterfaceSchema,
							CategoryType: common.ConstCategoryTypeFile,
							RootType:     common.ConstCategorySubRootApiProxy,
							NodeType:     common.ConstInterfaceDefinitionTypeSchema,
							NodeId:       common.ConstApiProxyCommonRespDataCallRespSchemaId,
							Name:         common.ConstApiProxyCommonRespDataCallRespSchemaName,
						},
					},
				},
			},
		},
	},
	common.ConstCategoryTreeTypeComponentGroup: {
		{
			// 组件组
			Type:         common.ConstCategoryTreeTypeComponentGroup,
			CategoryType: common.ConstCategoryTypeDirectory,
			Name:         common.ConstCategoryRootAllComponentGroup,
			Children: []relation{
				{
					Type:         common.ConstCategoryTreeTypeComponentGroup,
					CategoryType: common.ConstCategoryTypeDirectory,
					RootType:     common.ConstCategorySubRootBusinessComponent,
					Name:         common.ConstCategorySubRootBusinessComponent,
				},
				{
					Type:         common.ConstCategoryTreeTypeComponentGroup,
					CategoryType: common.ConstCategoryTypeDirectory,
					RootType:     common.ConstCategorySubRootSetupTeardownComponent,
					Name:         common.ConstCategorySubRootSetupTeardownComponent,
				},
			},
		},
	},
	common.ConstCategoryTreeTypeApiCase: {
		{
			// API测试用例
			Type:         common.ConstCategoryTreeTypeApiCase,
			CategoryType: common.ConstCategoryTypeDirectory,
			Name:         common.ConstCategoryRootAllApiCase,
		},
	},
	common.ConstCategoryTreeTypeApiSuite: {
		{
			// API测试集合
			Type:         common.ConstCategoryTreeTypeApiSuite,
			CategoryType: common.ConstCategoryTypeDirectory,
			Name:         common.ConstCategoryRootAllApiSuite,
		},
	},
	common.ConstCategoryTreeTypeApiPlan: {
		{
			// API测试计划
			Type:         common.ConstCategoryTreeTypeApiPlan,
			CategoryType: common.ConstCategoryTypeDirectory,
			Name:         common.ConstCategoryRootAllApiPlan,
		},
	},
	common.ConstCategoryTreeTypeUiPlan: {
		{
			// UI测试计划
			Type:         common.ConstCategoryTreeTypeUiPlan,
			CategoryType: common.ConstCategoryTypeDirectory,
			Name:         common.ConstCategoryRootAllUiPlan,
		},
	},
	common.ConstCategoryTreeTypePerfCase: {
		{
			// 压力测试用例
			Type:         common.ConstCategoryTreeTypePerfCase,
			CategoryType: common.ConstCategoryTypeDirectory,
			Name:         common.ConstCategoryRootAllPerfScenario,
		},
	},
	common.ConstCategoryTreeTypePerfPlan: {
		{
			// 压力测试计划
			Type:         common.ConstCategoryTreeTypePerfPlan,
			CategoryType: common.ConstCategoryTypeDirectory,
			Name:         common.ConstCategoryRootAllPerfPlan,
		},
	},
	common.ConstCategoryTreeTypeStabilityPlan: {
		{
			// 稳定性测试计划
			Type:         common.ConstCategoryTreeTypeStabilityPlan,
			CategoryType: common.ConstCategoryTypeDirectory,
			Name:         common.ConstCategoryRootAllStabilityPlan,
		},
	},
	common.ConstCategoryTreeTypeUIAgentComponent: {
		{
			// UI Agent组件
			Type:         common.ConstCategoryTreeTypeUIAgentComponent,
			CategoryType: common.ConstCategoryTypeDirectory,
			Name:         common.ConstCategoryRootAllUIAgentComponent,
		},
	},
}

func (l *GetCategoryTreeLogic) createProjectCategoryTree(req *pb.GetCategoryTreeReq) error {
	relations, ok := categoryBuiltinRelations[req.GetType()]
	if !ok {
		return errorx.Errorf(errorx.DoesNotSupport, "invalid category type: %s", req.GetType())
	}

	return l.svcCtx.CategoryModel.Trans(
		l.ctx, func(context context.Context, session sqlx.Session) error {
			return l.createProjectCategoryTreeByRelation(context, session, req.GetProjectId(), "", relations)
		},
	)
}

func (l *GetCategoryTreeLogic) createProjectCategoryTreeByRelation(
	ctx context.Context, session sqlx.Session, projectID, parentID string, relations []relation,
) error {
	if ctx == nil {
		ctx = l.ctx
	}

	for i, r := range relations {
		req := CreateCategoryInternalReq{
			CreateCategoryReq: &pb.CreateCategoryReq{
				ProjectId:   projectID,
				Type:        r.Type,
				Name:        r.Name,
				Description: r.Name,
				ParentId:    parentID,
			},
			CategoryType:   r.CategoryType,
			RootType:       r.RootType,
			NodeType:       r.NodeType,
			NodeId:         r.NodeId,
			Builtin:        true,
			IsInternalCall: true,
		}

		if parentID != "" {
			req.Index = int64(i + 1)
		} else {
			req.Index = 1
		}

		category, err := l.createCategoryLogic.CreateCategoryForInternal(ctx, session, req)
		if err != nil {
			return err
		}

		if len(r.Children) > 0 {
			if err = l.createProjectCategoryTreeByRelation(
				ctx, session, projectID, category.CategoryId, r.Children,
			); err != nil {
				return err
			}
		}
	}

	return nil
}
