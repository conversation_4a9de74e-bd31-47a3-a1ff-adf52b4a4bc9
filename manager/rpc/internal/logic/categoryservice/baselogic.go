package categoryservicelogic

import (
	"context"

	"github.com/Masterminds/squirrel"
	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/logx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sqlbuilder"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/userinfo"
	qetutils "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/utils"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type BaseLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext

	currentUser *userinfo.UserInfo

	converters []qetutils.TypeConverter
}

func newBaseLogic(ctx context.Context, svcCtx *svc.ServiceContext) *BaseLogic {
	return &BaseLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,

		currentUser: userinfo.FromContext(ctx),

		converters: []qetutils.TypeConverter{},
	}
}

func (l *BaseLogic) generateCategoryId(projectId, tp string) (string, error) {
	g := qetutils.NewUniqueIdGenerator(
		qetutils.WithGenerateFunc(utils.GenCategoryId), qetutils.WithIsUniqueFunc(
			func(id string) bool {
				r, err := l.svcCtx.CategoryModel.FindOneByProjectIdTypeCategoryId(l.ctx, projectId, tp, id)
				if errors.Is(err, model.ErrNotFound) || r == nil {
					return true
				}
				return false
			},
		),
	)
	categoryId := g.Next()
	if categoryId == "" {
		return "", errors.WithStack(
			errorx.Err(
				errorx.GenerateUniqueIdFailure, "failed to generate category id, please try it later",
			),
		)
	}

	return categoryId, nil
}

func (l *BaseLogic) generateSearchSqlBuilder(req *pb.SearchCategoryReq) (sb squirrel.SelectBuilder) {
	sb = l.svcCtx.CategoryModel.SelectBuilder().Where(
		"`project_id` = ? AND `type` = ?", req.GetProjectId(), req.GetType(),
	)
	sb = sqlbuilder.SearchOptions(sb, sqlbuilder.WithCondition(l.svcCtx.CategoryModel, req.GetCondition()))

	return
}
