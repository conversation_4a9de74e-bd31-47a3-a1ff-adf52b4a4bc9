package categoryservicelogic

import "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"

// CategoryCache key: category id, val: category
type CategoryCache = map[string]*pb.Category

// CategoryChildrenCache key: parent category id, val: children categories
type CategoryChildrenCache = map[string][]*pb.Category

type CreateCategoryInternalReq struct {
	*pb.CreateCategoryReq

	CategoryType   string `json:"category_type"`    // 分类类型（目录、文件（即叶子节点））
	RootType       string `json:"root_type"`        // 根类型（组件组：业务组件、前后置组件，接口定义：接口文档、数据模型
	NodeType       string `json:"node_type"`        // 节点类型（组件组：业务单请求组件、业务行为组组件、前置组件、后置组件，接口定义：接口文档、数据模型）
	NodeId         string `json:"node_id"`          // 节点ID
	Builtin        bool   `json:"builtin"`          // 是否内建分类
	IsInternalCall bool   `json:"is_internal_call"` // 是否内部调用（由于内部调用时`ParentId`不能保证为空，所以增加此字段来判断）
}
