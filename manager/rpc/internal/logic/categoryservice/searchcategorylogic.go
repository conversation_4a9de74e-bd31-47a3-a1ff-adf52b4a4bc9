package categoryservicelogic

import (
	"context"

	"github.com/pkg/errors"
	"github.com/zyedidia/generic/stack"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type SearchCategoryLogic struct {
	*BaseLogic
}

func NewSearchCategoryLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SearchCategoryLogic {
	return &SearchCategoryLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// SearchCategory 搜索分类
func (l *SearchCategoryLogic) SearchCategory(in *pb.SearchCategoryReq) (resp *pb.SearchCategoryResp, err error) {
	resp = &pb.SearchCategoryResp{TotalPage: 1}

	// validate the project_id in req
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, in.GetProjectId()); err != nil {
		return nil, err
	}

	nodes, err := l.svcCtx.CategoryModel.SearchTreeNodes(
		l.ctx, model.SearchCategoryTreeCondition{
			ProjectId:       in.GetProjectId(),
			Type:            in.GetType(),
			SearchCondition: in.GetCondition(),
		},
	)
	if err != nil {
		if !errors.Is(err, model.ErrNotFound) {
			return nil, errors.Wrapf(
				errorx.Err(errorx.DBError, err.Error()),
				"failed to search category tree nodes with project_id[%s] and type[%s], error: %+v",
				in.GetProjectId(), in.GetType(), err,
			)
		} else {
			resp.Items = []*pb.Category{}
			return resp, nil
		}
	}

	// NOTE: the `total` is wrong, because the `nodes` contains the ancestor nodes of match nodes
	total := len(nodes)
	resp.TotalCount = uint64(total)

	if total == 0 {
		return resp, nil
	}

	// get the root category by type
	root, err := model.GetRootCategoryByType(l.ctx, l.svcCtx.CategoryModel, in.GetProjectId(), in.GetType())
	if err != nil {
		return nil, err
	}

	m := make(CategoryCache)

	for _, node := range nodes {
		var category *pb.Category

		if v, ok := m[node.CategoryId]; !ok {
			category = &pb.Category{}
			if err = utils.Copy(category, node); err != nil {
				return nil, errors.Wrapf(
					errorx.Err(errorx.CopyToStructFailure, err.Error()),
					"failed to copy category node[%+v] to response, error: %+v",
					node, err,
				)
			}

			m[node.CategoryId] = category
		} else {
			category = v
		}

		if node.CategoryId != node.ParentId {
			if v, ok := m[node.ParentId]; ok {
				v.Children = append(v.Children, category)
			}
		}
	}

	// calculate the amount of root category
	if r, ok := m[root.CategoryId]; ok {
		calculateAmountByIteration(r)
		resp.Items = []*pb.Category{r}
	}

	return resp, nil
}

// calculateAmountByRecursive calculate the amount of category whose category type is directory
//
// Deprecated: use calculateAmountByIteration instead.
// Avoid the infinite depth of the category tree leading to the overflow of the stack
func calculateAmountByRecursive(category *pb.Category) {
	category.Amount = 0
	if category.CategoryType == common.ConstCategoryTypeDirectory {
		for _, child := range category.Children {
			if child.CategoryType == common.ConstCategoryTypeDirectory {
				calculateAmountByRecursive(child)
				category.Amount += child.Amount
			} else if child.CategoryType == common.ConstCategoryTypeFile {
				category.Amount += 1
			}
		}
	}
}

// calculateAmountByIteration calculate the amount of category whose category type is directory
func calculateAmountByIteration(category *pb.Category) {
	// key: child category id, val: parent category
	m := make(CategoryCache)
	s := stack.New[*pb.Category]()
	s.Push(category)

	for s.Size() != 0 {
		c := s.Pop()
		if c.CategoryType == common.ConstCategoryTypeDirectory {
			c.Amount = 0
			for _, child := range c.Children {
				if child.CategoryType == common.ConstCategoryTypeDirectory {
					s.Push(child)
					m[child.CategoryId] = c
				} else if child.CategoryType == common.ConstCategoryTypeFile {
					c.Amount += 1
				}
			}

			// summarize the results of the amount of child category to the parent category
			if v, ok := m[c.CategoryId]; ok {
				v.Amount += c.Amount
			}
		}
	}
}
