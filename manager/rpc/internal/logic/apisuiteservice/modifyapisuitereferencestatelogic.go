package apisuiteservicelogic

import (
	"context"
	"fmt"

	"github.com/emirpasic/gods/sets/hashset"
	"github.com/hashicorp/go-multierror"
	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/mr"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/redislock"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type ModifyApiSuiteReferenceStateLogic struct {
	*BaseLogic
}

func NewModifyApiSuiteReferenceStateLogic(
	ctx context.Context, svcCtx *svc.ServiceContext,
) *ModifyApiSuiteReferenceStateLogic {
	return &ModifyApiSuiteReferenceStateLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// ModifyApiSuiteReferenceState 修改API集合所在的API计划的引用状态
func (l *ModifyApiSuiteReferenceStateLogic) ModifyApiSuiteReferenceState(in *pb.ModifyApiSuiteReferenceStateReq) (
	resp *pb.ModifyApiSuiteReferenceStateResp, err error,
) {
	// validate the project_id in req
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, in.GetProjectId()); err != nil {
		return nil, err
	}

	// validate the suite_id in req
	apiSuite, err := model.CheckApiSuiteBySuiteId(l.ctx, l.svcCtx.ApiSuiteModel, in.GetProjectId(), in.GetSuiteId())
	if err != nil {
		return nil, err
	}

	rrs, err := l.svcCtx.ApiPlanReferenceModel.FindReferenceByReference(
		l.ctx, in.GetProjectId(), common.ConstReferenceTypeApiSuite, in.GetSuiteId(), false,
	)
	if err != nil {
		if !errors.Is(err, model.ErrNotFound) {
			return nil, errors.Wrapf(
				errorx.Err(errorx.DBError, err.Error()),
				"failed to find api plan containing the specified api suite with project_id[%s] and suite_id[%s], error: %+v",
				in.GetProjectId(), in.GetSuiteId(), err,
			)
		}
		return nil, errors.WithStack(
			errorx.Err(
				errorx.ProhibitedBehavior, fmt.Sprintf(
					"cannot modify the reference state of api suite[%s] which is not in any api plan", apiSuite.Name,
				),
			),
		)
	}

	recorder := make(map[string]*model.ApiPlanReferenceRelationship)
	allSet := hashset.New()
	for _, rr := range rrs {
		rr := rr
		allSet.Add(rr.PlanId)
		recorder[rr.PlanId] = rr
	}

	modifySet := hashset.New()
	for _, planId := range in.GetPlanIds() {
		modifySet.Add(planId)
	}

	diffSet := modifySet.Difference(allSet)
	if !diffSet.Empty() {
		return nil, errors.WithStack(
			errorx.Err(
				errorx.ProhibitedBehavior, fmt.Sprintf(
					"cannot modify the reference state of api suite[%s] which is not in the api plans %s",
					apiSuite.Name, logic.ContainerToString(diffSet),
				),
			),
		)
	}

	// acquire redis lock
	key := fmt.Sprintf("%s:%s:%s", common.ConstLockApiSuiteProjectIdSuiteIdPrefix, in.GetProjectId(), in.GetSuiteId())
	lock, err := redislock.NewRedisLockAndAcquire(l.svcCtx.Redis, key, redislock.WithExpire(common.ConstLockExpireTime))
	if err != nil {
		return nil, err
	}
	defer func() {
		// release redis lock
		e := lock.Release()
		if e != nil {
			// l.Logger.Error(e)
			if re, ok := errorx.RootError(e); ok && re.Code() != errorx.ReleaseRedisLockFailure && err == nil {
				err = e
			}
		}
	}()

	workers := modifySet.Size()
	if workers > common.ConstMRMaxWorkers {
		workers = common.ConstMRMaxWorkers
	}

	mr.ForEach(
		func(source chan<- any) {
			for _, v := range modifySet.Values() {
				source <- v
			}
		}, func(item any) {
			if planId, ok := item.(string); !ok {
				err = multierror.Append(
					err, errors.Wrapf(errorx.ErrGrpcInternal, "the api plan id[%v (%T)] is not a string", item, item),
				)
			} else if reference, ok := recorder[planId]; !ok {
				err = multierror.Append(
					err, errors.Wrapf(errorx.ErrGrpcInternal, "cannot find the reference of api plan[%s]", planId),
				)
			} else {
				// 当前的引用状态跟目标不一样时才需要修改
				if reference.State != int64(in.State) {
					reference.State = int64(in.State)

					if e := l.modify(apiSuite, reference); e != nil {
						err = multierror.Append(err, e)
					}
				}
			}
		}, mr.WithContext(l.ctx), mr.WithWorkers(workers),
	)

	return &pb.ModifyApiSuiteReferenceStateResp{}, err
}

func (l *ModifyApiSuiteReferenceStateLogic) modify(
	apiSuite *model.ApiSuite, reference *model.ApiPlanReferenceRelationship,
) (err error) {
	apiPlan, err := model.CheckApiPlanByPlanId(l.ctx, l.svcCtx.ApiPlanModel, reference.ProjectId, reference.PlanId)
	if err != nil {
		return err
	}

	// acquire redis lock
	key := fmt.Sprintf("%s:%s:%s", common.ConstLockApiPlanProjectIdPlanIdPrefix, apiPlan.ProjectId, apiPlan.PlanId)
	lock, err := redislock.NewRedisLockAndAcquire(l.svcCtx.Redis, key, redislock.WithExpire(common.ConstLockExpireTime))
	if err != nil {
		return err
	}
	defer func() {
		// release redis lock
		e := lock.Release()
		if e != nil {
			// l.Logger.Error(e)
			if re, ok := errorx.RootError(e); ok && re.Code() != errorx.ReleaseRedisLockFailure && err == nil {
				err = e
			}
		}
	}()

	if _, err = l.svcCtx.ApiPlanReferenceModel.Update(l.ctx, nil, reference); err != nil {
		err = errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()),
			"failed to update the reference state of api suite[%s] which is in the api plan[%s], error: %+v",
			apiSuite.Name, apiPlan.Name, err,
		)
	}

	return err
}
