package apisuiteservicelogic

import (
	"context"
	"math"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sqlbuilder/rpc"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type SearchApiCaseInApiSuiteLogic struct {
	*BaseLogic
}

func NewSearchApiCaseInApiSuiteLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SearchApiCaseInApiSuiteLogic {
	return &SearchApiCaseInApiSuiteLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// SearchApiCaseInApiSuite 搜索API集合中的API用例
func (l *SearchApiCaseInApiSuiteLogic) SearchApiCaseInApiSuite(in *pb.SearchApiCaseInApiSuiteReq) (resp *pb.SearchApiCaseInApiSuiteResp, err error) {
	resp = &pb.SearchApiCaseInApiSuiteResp{}

	// validate the project_id in req
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, in.GetProjectId()); err != nil {
		return nil, err
	}

	// validate the suite_id in req
	if _, err = model.CheckApiSuiteBySuiteId(l.ctx, l.svcCtx.ApiSuiteModel, in.GetProjectId(), in.GetSuiteId()); err != nil {
		return nil, err
	}

	selectBuilder, countBuilder := l.svcCtx.ApiSuiteModel.GenerateSearchApiCaseInApiSuiteSqlBuilder(model.SearchApiCaseInApiSuiteReq{
		ProjectId:  in.GetProjectId(),
		SuiteId:    in.GetSuiteId(),
		Condition:  in.GetCondition(),
		Pagination: in.GetPagination(),
		Sort:       rpc.ConvertSortFields(in.GetSort()),
	})

	count, err := l.svcCtx.ApiSuiteModel.FindCountApiCasesInApiSuite(l.ctx, countBuilder)
	if err != nil {
		return nil, errors.Wrapf(errorx.Err(errorx.DBError, err.Error()), "failed to count api case in api suite with project_id[%s] and suite_id[%s], error: %+v", in.GetProjectId(), in.GetSuiteId(), err)
	}
	resp.TotalCount = uint64(count)
	resp.TotalPage = 1

	apiCases, err := l.svcCtx.ApiSuiteModel.FindApiCasesInApiSuite(l.ctx, selectBuilder)
	if err != nil {
		return nil, errors.Wrapf(errorx.Err(errorx.DBError, err.Error()), "failed to find api case in api suite with project_id[%s] and suite_id[%s], error: %+v", in.GetProjectId(), in.GetSuiteId(), err)
	}

	resp.Items = make([]*pb.ApiCase, 0, len(apiCases))
	for _, apiCase := range apiCases {
		ac := &pb.ApiCase{}
		if err = utils.Copy(ac, apiCase, l.converters...); err != nil {
			return nil, errors.Wrapf(errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy api case[%+v] to response, error: %+v", apiCase, err)
		}

		resp.Items = append(resp.Items, ac)
	}

	pagination := in.GetPagination()
	if pagination != nil {
		resp.CurrentPage = pagination.GetCurrentPage()
		resp.PageSize = pagination.GetPageSize()
		resp.TotalPage = uint64(math.Ceil(float64(resp.TotalCount) / float64(resp.PageSize)))
	}

	return resp, nil
}
