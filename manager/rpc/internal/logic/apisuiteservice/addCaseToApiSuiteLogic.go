package apisuiteservicelogic

import (
	"context"
	"fmt"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/lang"
	"github.com/zeromicro/go-zero/core/mr"
	"github.com/zeromicro/go-zero/core/stores/sqlx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/redislock"
)

type AddCaseToApiSuiteLogic struct {
	*BaseLogic
}

func NewAddCaseToApiSuiteLogic(ctx context.Context, svcCtx *svc.ServiceContext) *AddCaseToApiSuiteLogic {
	return &AddCaseToApiSuiteLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// AddCaseToApiSuite 添加用例到API集合中
func (l *AddCaseToApiSuiteLogic) AddCaseToApiSuite(in *pb.AddCaseToApiSuiteReq) (
	resp *pb.AddCaseToApiSuiteResp, err error,
) {
	// validate the project_id in req
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, in.GetProjectId()); err != nil {
		return nil, err
	}

	// validate the suite_id in req
	if _, err = model.CheckApiSuiteBySuiteId(
		l.ctx, l.svcCtx.ApiSuiteModel, in.GetProjectId(), in.GetSuiteId(),
	); err != nil {
		return nil, err
	}

	rrs, err := l.svcCtx.ApiSuiteReferenceModel.FindReferenceBySuiteId(
		l.ctx, in.GetProjectId(), in.GetSuiteId(),
	)
	if err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()),
			"failed to find api suite reference with project_id[%s] and suite_id[%s], error: %+v", in.GetProjectId(),
			in.GetSuiteId(), err,
		)
	}

	cache := make(types.StringPlaceholderCache, len(rrs))
	for _, rr := range rrs {
		cache[rr.ReferenceType+":"+rr.ReferenceId] = lang.Placeholder
	}

	cases := make(map[string]*pb.CaseTypeId)
	for _, item := range in.GetCaseIds() {
		if _, ok := cases[item.GetCaseType()+":"+item.GetCaseId()]; ok {
			continue
		} else {
			cases[item.GetCaseType()+":"+item.GetCaseId()] = item
		}
	}

	// acquire redis lock
	key := fmt.Sprintf("%s:%s:%s", common.ConstLockApiSuiteProjectIdSuiteIdPrefix, in.GetProjectId(), in.GetSuiteId())
	lock, err := redislock.NewRedisLockAndAcquire(l.svcCtx.Redis, key, redislock.WithExpire(common.ConstLockExpireTime))
	if err != nil {
		return nil, err
	}
	defer func() {
		// release redis lock
		e := lock.Release()
		if e != nil {
			// l.Logger.Error(e)
			if re, ok := errorx.RootError(e); ok && re.Code() != errorx.ReleaseRedisLockFailure && err == nil {
				err = e
			}
		}
	}()

	if err = l.svcCtx.ApiSuiteReferenceModel.Trans(
		l.ctx, func(context context.Context, session sqlx.Session) error {
			return mr.MapReduceVoid[*pb.CaseTypeId, *pb.CaseTypeId](
				func(source chan<- *pb.CaseTypeId) {
					for _, item := range cases {
						if _, ok := cache[item.GetCaseType()+":"+item.GetCaseId()]; ok {
							continue
						} else {
							cache[item.GetCaseType()+":"+item.GetCaseId()] = lang.Placeholder
						}

						source <- item
					}
				}, func(item *pb.CaseTypeId, writer mr.Writer[*pb.CaseTypeId], cancel func(error)) {
					var err error
					defer func() {
						if err != nil {
							cancel(err)
						}
					}()

					switch item.GetCaseType() {
					case common.ConstReferenceTypeApiCase:
						if _, err = model.CheckApiCaseByCaseId(
							l.ctx, l.svcCtx.ApiCaseModel, in.GetProjectId(), item.GetCaseId(), "",
						); err != nil {
							return
						}
					case common.ConstReferenceTypeInterfaceCase:
						if _, err = model.CheckInterfaceCaseByCaseId(
							l.ctx, l.svcCtx.InterfaceCaseModel, in.GetProjectId(), "", item.GetCaseId(), "",
						); err != nil {
							return
						}
					}

					writer.Write(item)
				}, func(pipe <-chan *pb.CaseTypeId, cancel func(error)) {
					var err error
					defer func() {
						if err != nil {
							cancel(err)
						}
					}()

					req := createApiSuiteReferenceInternalReq{
						ProjectId: in.GetProjectId(),
						SuiteId:   in.GetSuiteId(),
						Items: make(
							[]*pb.CaseTypeId, 0, constants.ConstDefaultMakeSliceSize,
						), // 预置切片容量为`constants.ConstDefaultMakeSliceSize`，减少切片扩容的情况
					}

					for item := range pipe {
						req.Items = append(req.Items, item)
					}

					// create api suite reference of case
					if err = l.createApiSuiteReference(context, session, req); err != nil {
						return
					}

					// create api plan reference of case which is added to api suite
					if err = l.createApiPlanReference2(
						context, session, createOrRemoveApiPlanReference2InternalReq{
							createApiSuiteReferenceInternalReq: req,
						},
					); err != nil {
						return
					}
				}, mr.WithContext(context), mr.WithWorkers(len(cases)),
			)
		},
	); err != nil {
		return nil, err
	}

	return &pb.AddCaseToApiSuiteResp{}, nil
}
