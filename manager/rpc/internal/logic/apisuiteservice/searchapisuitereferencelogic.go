package apisuiteservicelogic

import (
	"context"
	"math"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sqlbuilder/rpc"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type SearchApiSuiteReferenceLogic struct {
	*BaseLogic
}

func NewSearchApiSuiteReferenceLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SearchApiSuiteReferenceLogic {
	return &SearchApiSuiteReferenceLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// SearchApiSuiteReference 搜索API集合引用详情
func (l *SearchApiSuiteReferenceLogic) SearchApiSuiteReference(in *pb.SearchApiSuiteReferenceReq) (resp *pb.SearchApiSuiteReferenceResp, err error) {
	resp = &pb.SearchApiSuiteReferenceResp{}

	// validate the project_id in req
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, in.GetProjectId()); err != nil {
		return nil, err
	}

	// validate the suite_id in req
	if _, err = model.CheckApiSuiteBySuiteId(l.ctx, l.svcCtx.ApiSuiteModel, in.GetProjectId(), in.GetSuiteId()); err != nil {
		return nil, err
	}

	selectBuilder, countBuilder := l.svcCtx.ApiSuiteModel.GenerateSearchApiSuiteReferenceSqlBuilder(model.SearchApiSuiteReferenceReq{
		ProjectId:  in.GetProjectId(),
		SuiteId:    in.GetSuiteId(),
		Condition:  in.GetCondition(),
		Pagination: in.GetPagination(),
		Sort:       rpc.ConvertSortFields(in.GetSort()),
	})

	count, err := l.svcCtx.ApiSuiteModel.FindCountApiSuiteReference(l.ctx, countBuilder)
	if err != nil {
		return nil, errors.Wrapf(errorx.Err(errorx.DBError, err.Error()), "failed to count reference data of api suite with project_id[%s] and suite_id[%s], error: %+v", in.GetProjectId(), in.GetSuiteId(), err)
	}
	resp.TotalCount = uint64(count)
	resp.TotalPage = 1

	apiSuites, err := l.svcCtx.ApiSuiteModel.FindApiSuiteReference(l.ctx, selectBuilder)
	if err != nil {
		return nil, errors.Wrapf(errorx.Err(errorx.DBError, err.Error()), "failed to find reference data of api suite with project_id[%s] and suite_id[%s], error: %+v", in.GetProjectId(), in.GetSuiteId(), err)
	}

	resp.Items = make([]*pb.SearchApiSuiteReferenceItem, 0, len(apiSuites))
	for _, apiSuite := range apiSuites {
		item := &pb.SearchApiSuiteReferenceItem{}
		if err = utils.Copy(item, apiSuite, l.converters...); err != nil {
			return nil, errors.Wrapf(errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy reference data of api suite[%+v] to response, error: %+v", apiSuite, err)
		}

		resp.Items = append(resp.Items, item)
	}

	pagination := in.GetPagination()
	if pagination != nil {
		resp.CurrentPage = pagination.GetCurrentPage()
		resp.PageSize = pagination.GetPageSize()
		resp.TotalPage = uint64(math.Ceil(float64(resp.TotalCount) / float64(resp.PageSize)))
	}

	return resp, nil
}
