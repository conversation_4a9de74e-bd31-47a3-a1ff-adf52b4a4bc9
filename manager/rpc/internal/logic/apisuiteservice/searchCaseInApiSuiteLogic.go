package apisuiteservicelogic

import (
	"context"
	"math"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sqlbuilder/rpc"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"
)

type SearchCaseInApiSuiteLogic struct {
	*BaseLogic
}

func NewSearchCaseInApiSuiteLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SearchCaseInApiSuiteLogic {
	return &SearchCaseInApiSuiteLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// SearchCaseInApiSuite 搜索API集合中的用例
func (l *SearchCaseInApiSuiteLogic) SearchCaseInApiSuite(in *pb.SearchCaseInApiSuiteReq) (out *pb.SearchCaseInApiSuiteResp, err error) {
	resp := &pb.SearchCaseInApiSuiteResp{}

	// validate the project_id in req
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, in.GetProjectId()); err != nil {
		return nil, err
	}

	// validate the suite_id in req
	if _, err = model.CheckApiSuiteBySuiteId(l.ctx, l.svcCtx.ApiSuiteModel, in.GetProjectId(), in.GetSuiteId()); err != nil {
		return nil, err
	}

	selectBuilder, countBuilder := l.svcCtx.ApiSuiteModel.
		GenerateSearchCaseInApiSuiteSqlBuilder(model.SearchCaseInApiSuiteReq{
			ProjectId:  in.GetProjectId(),
			SuiteId:    in.GetSuiteId(),
			Condition:  in.GetCondition(),
			Pagination: in.GetPagination(),
			Sort:       rpc.ConvertSortFields(in.GetSort()),
		})

	count, err := l.svcCtx.ApiSuiteModel.FindCountCasesInApiSuite(l.ctx, countBuilder)
	if err != nil {
		return nil, errors.Wrapf(errorx.Err(errorx.DBError, err.Error()), "failed to count case in api suite with project_id[%s] and suite_id[%s], error: %+v", in.GetProjectId(), in.GetSuiteId(), err)
	}
	resp.TotalCount = uint64(count)
	resp.TotalPage = 1

	cases, err := l.svcCtx.ApiSuiteModel.FindCasesInApiSuite(l.ctx, selectBuilder)
	if err != nil {
		return nil, errors.Wrapf(errorx.Err(errorx.DBError, err.Error()), "failed to find case in api suite with project_id[%s] and suite_id[%s], error: %+v", in.GetProjectId(), in.GetSuiteId(), err)
	}

	resp.Items = make([]*pb.SearchCaseInApiSuiteItem, 0, len(cases))
	for _, _case := range cases {
		item := &pb.SearchCaseInApiSuiteItem{}
		if err = utils.Copy(item, _case, l.converters...); err != nil {
			return nil, errors.Wrapf(errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy case[%+v] to response, error: %+v", _case, err)
		}

		resp.Items = append(resp.Items, item)
	}

	pagination := in.GetPagination()
	if pagination != nil {
		resp.CurrentPage = pagination.GetCurrentPage()
		resp.PageSize = pagination.GetPageSize()
		resp.TotalPage = uint64(math.Ceil(float64(resp.TotalCount) / float64(resp.PageSize)))
	}

	return resp, nil
}
