package apisuiteservicelogic

import (
	"context"
	"fmt"

	"github.com/hashicorp/go-multierror"
	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/mr"
	"github.com/zeromicro/go-zero/core/stores/sqlx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/redislock"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type RemoveApiSuiteLogic struct {
	*BaseLogic
}

func NewRemoveApiSuiteLogic(ctx context.Context, svcCtx *svc.ServiceContext) *RemoveApiSuiteLogic {
	return &RemoveApiSuiteLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// RemoveApiSuite 删除API集合
func (l *RemoveApiSuiteLogic) RemoveApiSuite(in *pb.RemoveApiSuiteReq) (resp *pb.RemoveApiSuiteResp, err error) {
	// validate the project_id in req
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, in.GetProjectId()); err != nil {
		return nil, err
	}

	suiteIds := in.GetSuiteIds()
	workers := len(suiteIds)
	if workers > common.ConstMRMaxWorkers {
		workers = common.ConstMRMaxWorkers
	}

	mr.ForEach(func(source chan<- any) {
		for _, suiteId := range suiteIds {
			source <- suiteId
		}
	}, func(item any) {
		suiteId, ok := item.(string)
		if !ok {
			err = multierror.Append(err, errorx.Err(errorx.InternalError, fmt.Sprintf("the api suite id[%v (%T)] is not a string", item, item)))
		} else {
			if e := l.remove(in.GetProjectId(), suiteId); e != nil {
				err = multierror.Append(err, e)
			}
		}
	}, mr.WithContext(l.ctx), mr.WithWorkers(workers))

	return &pb.RemoveApiSuiteResp{}, err
}

func (l *RemoveApiSuiteLogic) remove(projectId, suiteId string) (err error) {
	// validate the suite_id in req
	apiSuite, err := model.CheckApiSuiteBySuiteId(l.ctx, l.svcCtx.ApiSuiteModel, projectId, suiteId)
	if err != nil {
		return err
	}

	rrs, err := l.svcCtx.ApiPlanReferenceModel.FindReferenceByReference(l.ctx, projectId, common.ConstReferenceTypeApiSuite, suiteId, false)
	if err != nil {
		return errors.Wrapf(errorx.Err(errorx.DBError, err.Error()), "failed to find api suite reference with project_id[%s] and suite_id[%s], error: %+v", projectId, suiteId, err)
	} else if len(rrs) > 0 {
		return errors.WithStack(errorx.Err(errorx.ProhibitedBehavior, fmt.Sprintf("the api suite[%s] in api plan cannot be deleted, please remove the api suite from api plan first", apiSuite.Name)))
	}

	// acquire redis lock
	key := fmt.Sprintf("%s:%s:%s", common.ConstLockApiSuiteProjectIdSuiteIdPrefix, projectId, suiteId)
	lock, err := redislock.NewRedisLockAndAcquire(l.svcCtx.Redis, key, redislock.WithExpire(common.ConstLockExpireTime))
	if err != nil {
		return err
	}
	defer func() {
		// release redis lock
		e := lock.Release()
		if e != nil {
			// l.Logger.Error(e)
			if re, ok := errorx.RootError(e); ok && re.Code() != errorx.ReleaseRedisLockFailure && err == nil {
				err = e
			}
		}
	}()

	return l.svcCtx.ApiSuiteModel.Trans(l.ctx, func(context context.Context, session sqlx.Session) error {
		// Table: tag_reference_relationship
		if _, err := l.svcCtx.TagReferenceModel.RemoveByReferenceId(context, session, projectId, common.ConstReferenceTypeApiSuite, suiteId, ""); err != nil {
			return errors.Wrapf(errorx.Err(errorx.DBError, err.Error()), "failed to remove tag reference relationship with project_id[%s], reference_type[%s] and reference_id[%s], error: %+v", projectId, common.ConstReferenceTypeApiSuite, suiteId, err)
		}

		// Table: api_case_reference_relationship
		// if _, err := l.svcCtx.ApiCaseReferenceModel.RemoveByReferenceId(context, session, projectId, common.ConstReferenceTypeApiSuite, suiteId); err != nil {
		// 	return errors.Wrapf(errorx.Err(errorx.DBError, err.Error()), "failed to remove api case reference relationship with project_id[%s], reference_type[%s] and reference_id[%s], error: %+v", projectId, common.ConstReferenceTypeApiSuite, suiteId, err)
		// }
		// Table: api_suite_reference_relationship
		if _, err := l.svcCtx.ApiSuiteReferenceModel.RemoveBySuiteId(context, session, projectId, suiteId); err != nil {
			return errors.Wrapf(errorx.Err(errorx.DBError, err.Error()), "failed to remove api suite reference relationship with project_id[%s], reference_type[%s] and reference_id[%s], error: %+v", projectId, common.ConstReferenceTypeApiSuite, suiteId, err)
		}

		// Table: api_suite
		if _, err := l.svcCtx.ApiSuiteModel.RemoveBySuiteId(context, session, projectId, suiteId); err != nil {
			return errors.Wrapf(errorx.Err(errorx.DBError, err.Error()), "failed to remove api suite with project_id[%s] and suite_id[%s], error: %+v", projectId, suiteId, err)
		}

		return nil
	})
}
