package apisuiteservicelogic

import (
	"context"
	"database/sql"
	"fmt"
	"time"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"
	"github.com/zeromicro/go-zero/core/stores/sqlx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type CreateApiSuiteLogic struct {
	*BaseLogic
}

func NewCreateApiSuiteLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CreateApiSuiteLogic {
	return &CreateApiSuiteLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// CreateApiSuite 创建API集合
func (l *CreateApiSuiteLogic) CreateApiSuite(in *pb.CreateApiSuiteReq) (resp *pb.CreateApiSuiteResp, err error) {
	// validate the project_id in req
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, in.GetProjectId()); err != nil {
		return nil, err
	}

	var c *model.Category
	// validate the category_id in req
	if c, err = model.CheckCategoryByCategoryId(l.ctx, l.svcCtx.CategoryModel, in.GetProjectId(), common.ConstCategoryTreeTypeApiSuite, in.GetCategoryId()); err != nil {
		return nil, err
	} else if c.CategoryType != common.ConstCategoryTypeDirectory {
		return nil, errors.WithStack(errorx.Err(errorx.ProhibitedBehavior, fmt.Sprintf("the type of parent category[%s] does not support creation of sub category", c.CategoryType)))
	}

	apiSuite, err := l.create(in)
	if err != nil {
		return nil, err
	}

	resp = &pb.CreateApiSuiteResp{Suite: &pb.ApiSuite{}}
	if err = utils.Copy(resp.Suite, apiSuite, l.converters...); err != nil {
		return nil, errors.Wrapf(errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy api suite[%+v] to response, error: %+v", apiSuite, err)
	}

	return resp, nil
}

// in order to reduce cyclomatic complexity of CreateApiSuiteLogic.CreateApiSuite
func (l *CreateApiSuiteLogic) create(req *pb.CreateApiSuiteReq) (*model.ApiSuite, error) {
	suiteId, err := l.generateSuiteId(req.GetProjectId())
	if err != nil {
		return nil, err
	}

	// 由于`protojson`只能序列化`[]proto.Message`，而`tags`字段类型为`[]string`，Go Struct跟PB没有大的区别，因此直接使用`jsonx`暂代`protojson`
	tags := jsonx.MarshalToStringIgnoreError(req.GetTags())

	maintainedBy := req.GetMaintainedBy()
	if maintainedBy == "" {
		// if the maintainer is not set, the creator is set as the maintainer
		maintainedBy = l.currentUser.Account
	}

	apiSuite := &model.ApiSuite{
		ProjectId:  req.GetProjectId(),
		CategoryId: req.GetCategoryId(),
		SuiteId:    suiteId,
		Name:       req.GetName(),
		Description: sql.NullString{
			String: req.GetDescription(),
			Valid:  req.GetDescription() != "",
		},
		Priority: req.GetPriority(),
		Tags: sql.NullString{
			String: tags,
			Valid:  tags != "",
		},
		State:             int64(pb.CommonState_CS_ENABLE),
		CaseExecutionMode: req.GetCaseExecutionMode(),
		MaintainedBy: sql.NullString{
			String: maintainedBy,
			Valid:  maintainedBy != "",
		},
		CreatedBy: l.currentUser.Account,
		UpdatedBy: l.currentUser.Account,
	}

	cases := make(map[string]*pb.CaseTypeId)
	for _, item := range req.GetCaseIds() {
		if _, ok := cases[item.GetCaseType()+":"+item.GetCaseId()]; ok {
			continue
		} else {
			cases[item.GetCaseType()+":"+item.GetCaseId()] = item
		}
	}

	if err = l.svcCtx.ApiSuiteModel.Trans(l.ctx, func(context context.Context, session sqlx.Session) error {
		if _, err := l.svcCtx.ApiSuiteModel.InsertTX(context, session, apiSuite); err != nil {
			return errors.Wrapf(errorx.Err(errorx.DBError, err.Error()), "failed to insert table[%s] with values[%+v], error: %+v", l.svcCtx.ApiSuiteModel.Table(), apiSuite, err)
		}

		// create the api case reference of api suite
		for _, item := range cases {
			data := &model.ApiSuiteReferenceRelationship{
				ProjectId:     apiSuite.ProjectId,
				ReferenceType: item.GetCaseType(),
				ReferenceId:   item.GetCaseId(),
				SuiteId:       apiSuite.SuiteId,
				CreatedBy:     l.currentUser.Account,
				UpdatedBy:     l.currentUser.Account,
				CreatedAt:     time.Now(),
				UpdatedAt:     time.Now(),
			}
			if _, err = l.svcCtx.ApiSuiteReferenceModel.Insert(context, session, data); err != nil {
				return errors.Wrapf(errorx.Err(errorx.DBError, err.Error()), "failed to insert table[%s] with values[%+v], error: %+v", l.svcCtx.ApiSuiteReferenceModel.Table(), data, err)
			}
		}

		// create the new tag and tag reference of api suite
		if err := l.createTagLogic.CreateTagAndReferenceForInternal(context, session, types.CreateOrUpdateTagReference{
			ProjectId:     apiSuite.ProjectId,
			ReferenceType: common.ConstReferenceTypeApiSuite,
			ReferenceId:   apiSuite.SuiteId,
			Tags:          req.GetTags(),
		}); err != nil {
			return err
		}

		return nil
	}); err != nil {
		return nil, err
	}

	return apiSuite, nil
}
