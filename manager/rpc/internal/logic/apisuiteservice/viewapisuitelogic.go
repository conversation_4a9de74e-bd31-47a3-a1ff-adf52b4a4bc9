package apisuiteservicelogic

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type ViewApiSuiteLogic struct {
	*BaseLogic
}

func NewViewApiSuiteLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ViewApiSuiteLogic {
	return &ViewApiSuiteLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// ViewApiSuite 查看API集合
func (l *ViewApiSuiteLogic) ViewApiSuite(in *pb.ViewApiSuiteReq) (resp *pb.ViewApiSuiteResp, err error) {
	// validate the project_id in req
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, in.GetProjectId()); err != nil {
		return nil, err
	}

	// validate the suite_id in req
	apiSuite, err := model.CheckApiSuiteBySuiteId(l.ctx, l.svcCtx.ApiSuiteModel, in.GetProjectId(), in.GetSuiteId())
	if err != nil {
		return nil, err
	}

	s := &pb.ApiSuite{}
	if err = utils.Copy(s, apiSuite, l.converters...); err != nil {
		return nil, errors.Wrapf(errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy api suite[%+v] to response, error: %+v", apiSuite, err)
	}

	return &pb.ViewApiSuiteResp{Suite: s}, nil
}
