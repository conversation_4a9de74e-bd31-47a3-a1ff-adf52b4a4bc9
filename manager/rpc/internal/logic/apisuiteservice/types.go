package apisuiteservicelogic

import (
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type searchApiSuiteInternalReq struct {
	*pb.SearchApiSuiteReq

	DrillDown bool `json:"drill_down"`
}

type searchApiCaseNotInApiSuiteInternalReq struct {
	*pb.SearchApiCaseNotInApiSuiteReq

	DrillDown bool `json:"drill_down"`
}

type createApiSuiteReferenceInternalReq struct {
	ProjectId string           `json:"project_id"`
	SuiteId   string           `json:"suite_id"`
	Items     []*pb.CaseTypeId `json:"items"`
}

type createApiCaseReferenceInternalReq struct {
	ProjectId string   `json:"project_id"`
	SuiteId   string   `json:"suite_id"`
	CaseIds   []string `json:"case_ids"`
}

type createOrRemoveApiPlanReferenceInternalReq struct {
	createApiCaseReferenceInternalReq
}

type createOrRemoveApiPlanReference2InternalReq struct {
	createApiSuiteReferenceInternalReq
}
