package apisuiteservicelogic

import (
	"context"
	"math"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type SearchApiSuiteLogic struct {
	*BaseLogic
}

func NewSearchApiSuiteLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SearchApiSuiteLogic {
	return &SearchApiSuiteLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// SearchApiSuite 搜索API集合
func (l *SearchApiSuiteLogic) SearchApiSuite(in *pb.SearchApiSuiteReq) (resp *pb.SearchApiSuiteResp, err error) {
	resp = &pb.SearchApiSuiteResp{}

	// validate the project_id in req
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, in.GetProjectId()); err != nil {
		return nil, err
	}

	// validate the category_id in req
	if _, err = model.CheckCategoryByCategoryId(l.ctx, l.svcCtx.CategoryModel, in.GetProjectId(), common.ConstCategoryTreeTypeApiSuite, in.GetCategoryId()); err != nil {
		return nil, err
	}

	selectBuilder, countBuilder := l.generateSearchSqlBuilder(searchApiSuiteInternalReq{
		SearchApiSuiteReq: in,
		DrillDown:         true,
	})

	count, err := l.svcCtx.ApiSuiteModel.FindCount(l.ctx, countBuilder)
	if err != nil {
		return nil, errors.Wrapf(errorx.Err(errorx.DBError, err.Error()), "failed to count api suite with project_id[%s] and category_id[%s], error: %+v", in.GetProjectId(), in.GetCategoryId(), err)
	}
	resp.TotalCount = uint64(count)
	resp.TotalPage = 1

	apiSuites, err := l.svcCtx.ApiSuiteModel.FindNoCacheByQuery(l.ctx, selectBuilder)
	if err != nil {
		return nil, errors.Wrapf(errorx.Err(errorx.DBError, err.Error()), "failed to find api suite with project_id[%s] and category_id[%s], error: %+v", in.GetProjectId(), in.GetCategoryId(), err)
	}

	resp.Items = make([]*pb.ApiSuite, 0, len(apiSuites))
	for _, apiSuite := range apiSuites {
		as := &pb.ApiSuite{}
		if err = utils.Copy(as, apiSuite, l.converters...); err != nil {
			return nil, errors.Wrapf(errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy api suite[%+v] to response, error: %+v", apiSuite, err)
		}

		resp.Items = append(resp.Items, as)
	}

	pagination := in.GetPagination()
	if pagination != nil {
		resp.CurrentPage = pagination.GetCurrentPage()
		resp.PageSize = pagination.GetPageSize()
		resp.TotalPage = uint64(math.Ceil(float64(resp.TotalCount) / float64(resp.PageSize)))
	}

	return resp, nil
}
