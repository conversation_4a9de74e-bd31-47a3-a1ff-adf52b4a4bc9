package apisuiteservicelogic

import (
	"context"
	"math"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type SearchApiCaseNotInApiSuiteLogic struct {
	*BaseLogic
}

func NewSearchApiCaseNotInApiSuiteLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SearchApiCaseNotInApiSuiteLogic {
	return &SearchApiCaseNotInApiSuiteLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// SearchApiCaseNotInApiSuite 搜索不在指定的API集合中的API用例
func (l *SearchApiCaseNotInApiSuiteLogic) SearchApiCaseNotInApiSuite(in *pb.SearchApiCaseNotInApiSuiteReq) (resp *pb.SearchApiCaseNotInApiSuiteResp, err error) {
	resp = &pb.SearchApiCaseNotInApiSuiteResp{}

	// validate the project_id in req
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, in.GetProjectId()); err != nil {
		return nil, err
	}

	// validate the suite_id in req
	if _, err = model.CheckApiSuiteBySuiteId(l.ctx, l.svcCtx.ApiSuiteModel, in.GetProjectId(), in.GetSuiteId()); err != nil {
		return nil, err
	}

	// validate the category_id in req
	if _, err = model.CheckCategoryByCategoryId(l.ctx, l.svcCtx.CategoryModel, in.GetProjectId(), common.ConstCategoryTreeTypeApiCase, in.GetCategoryId()); err != nil {
		return nil, err
	}

	selectBuilder, countBuilder := l.generateSearchCasesNotInSuiteSqlBuilder(searchApiCaseNotInApiSuiteInternalReq{
		SearchApiCaseNotInApiSuiteReq: in,
		DrillDown:                     true,
	})

	count, err := l.svcCtx.ApiCaseModel.FindCount(l.ctx, countBuilder)
	if err != nil {
		return nil, errors.Wrapf(errorx.Err(errorx.DBError, err.Error()), "failed to count api case not in suite with project_id[%s], suite_id[%s] and category_id[%s], error: %+v", in.GetProjectId(), in.GetSuiteId(), in.GetCategoryId(), err)
	}
	resp.TotalCount = uint64(count)
	resp.TotalPage = 1

	apiCases, err := l.svcCtx.ApiCaseModel.FindNoCacheByQuery(l.ctx, selectBuilder)
	if err != nil {
		return nil, errors.Wrapf(errorx.Err(errorx.DBError, err.Error()), "failed to find api case not in suite with project_id[%s], suite_id[%s] and category_id[%s], error: %+v", in.GetProjectId(), in.GetSuiteId(), in.GetCategoryId(), err)
	}

	resp.Items = make([]*pb.ApiCase, 0, len(apiCases))
	for _, apiCase := range apiCases {
		ac := &pb.ApiCase{}
		if err = utils.Copy(ac, apiCase, l.converters...); err != nil {
			return nil, errors.Wrapf(errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy api case[%+v] to response, error: %+v", apiCase, err)
		}

		resp.Items = append(resp.Items, ac)
	}

	pagination := in.GetPagination()
	if pagination != nil {
		resp.CurrentPage = pagination.GetCurrentPage()
		resp.PageSize = pagination.GetPageSize()
		resp.TotalPage = uint64(math.Ceil(float64(resp.TotalCount) / float64(resp.PageSize)))
	}

	return resp, nil
}
