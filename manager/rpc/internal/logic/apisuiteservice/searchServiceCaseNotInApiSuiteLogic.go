package apisuiteservicelogic

import (
	"context"
	"math"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
	relationpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/relation/rpc/pb"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sqlbuilder/rpc"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"
)

type SearchServiceCaseNotInApiSuiteLogic struct {
	*BaseLogic
}

func NewSearchServiceCaseNotInApiSuiteLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SearchServiceCaseNotInApiSuiteLogic {
	return &SearchServiceCaseNotInApiSuiteLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// SearchServiceCaseNotInApiSuite 搜索不在指定的API集合中的精准测试用例
func (l *SearchServiceCaseNotInApiSuiteLogic) SearchServiceCaseNotInApiSuite(in *pb.SearchServiceCaseNotInApiSuiteReq) (resp *pb.SearchServiceCaseNotInApiSuiteResp, err error) {
	resp = &pb.SearchServiceCaseNotInApiSuiteResp{}

	// validate the project_id in req
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, in.GetProjectId()); err != nil {
		return nil, err
	}

	// validate the suite_id in req
	if _, err = model.CheckApiSuiteBySuiteId(l.ctx, l.svcCtx.ApiSuiteModel, in.GetProjectId(), in.GetSuiteId()); err != nil {
		return nil, err
	}

	var (
		projectID        = in.GetProjectId()
		suiteID          = in.GetSuiteId()
		relations        []*relationpb.ServiceCaseRelation
		apiCaseIds       []string
		interfaceCaseIds []string
	)
	for _, service := range in.GetServices() {
		out, err := l.svcCtx.RelationRPC.GetCaseOnlyByService(
			l.ctx, &relationpb.GetCaseOnlyByServiceReq{
				ProjectId: projectID,
				Service:   service,
			},
		)
		if err != nil {
			l.Errorf(
				"failed to get case by service, project_id: %s, service: %s, error: %+v",
				projectID, service, err,
			)
			return nil, err
		}
		relations = append(relations, out.GetRelations()...)
	}

	for _, relation := range relations {
		if len(relation.GetDocumentId()) == 0 {
			apiCaseIds = append(apiCaseIds, relation.GetCaseId())
		} else {
			interfaceCaseIds = append(interfaceCaseIds, relation.GetCaseId())
		}
	}

	if len(in.GetServices()) == 0 || len(relations) == 0 {
		resp.TotalCount = 0
		resp.TotalPage = 1
		resp.Items = make([]*pb.SearchCaseInApiSuiteItem, 0)
	} else {
		selectBuilder, countBuilder := l.svcCtx.ApiSuiteModel.
			GenerateSearchServiceCaseNotInApiSuiteSqlBuilder(
				model.SearchServiceCaseNotInApiSuiteReq{
					ProjectId:        projectID,
					SuiteId:          suiteID,
					ApiCaseIds:       apiCaseIds,
					InterfaceCaseIds: interfaceCaseIds,
					Condition:        in.GetCondition(),
					Pagination:       in.GetPagination(),
					Sort:             rpc.ConvertSortFields(in.GetSort()),
				},
			)

		count, err := l.svcCtx.ApiSuiteModel.FindCountCasesInApiSuite(l.ctx, countBuilder)
		if err != nil {
			return nil, errors.Wrapf(errorx.Err(errorx.DBError, err.Error()), "failed to count service case in api suite with project_id[%s] and suite_id[%s], error: %+v", in.GetProjectId(), in.GetSuiteId(), err)
		}
		resp.TotalCount = uint64(count)
		resp.TotalPage = 1

		cases, err := l.svcCtx.ApiSuiteModel.FindCasesInApiSuite(l.ctx, selectBuilder)
		if err != nil {
			return nil, errors.Wrapf(errorx.Err(errorx.DBError, err.Error()), "failed to find service case in api suite with project_id[%s] and suite_id[%s], error: %+v", in.GetProjectId(), in.GetSuiteId(), err)
		}

		resp.Items = make([]*pb.SearchCaseInApiSuiteItem, 0, len(cases))
		for _, _case := range cases {
			item := &pb.SearchCaseInApiSuiteItem{}
			if err = utils.Copy(item, _case, l.converters...); err != nil {
				return nil, errors.Wrapf(errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy service case[%+v] to response, error: %+v", _case, err)
			}

			resp.Items = append(resp.Items, item)
		}
	}

	pagination := in.GetPagination()
	if pagination != nil {
		resp.CurrentPage = pagination.GetCurrentPage()
		resp.PageSize = pagination.GetPageSize()
		resp.TotalPage = uint64(math.Ceil(float64(resp.TotalCount) / float64(resp.PageSize)))
	}

	return resp, nil
}
