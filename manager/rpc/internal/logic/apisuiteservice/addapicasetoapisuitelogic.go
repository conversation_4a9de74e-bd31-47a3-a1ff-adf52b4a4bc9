package apisuiteservicelogic

import (
	"context"
	"fmt"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/lang"
	"github.com/zeromicro/go-zero/core/mr"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/redislock"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type AddApiCaseToApiSuiteLogic struct {
	*BaseLogic
}

func NewAddApiCaseToApiSuiteLogic(ctx context.Context, svcCtx *svc.ServiceContext) *AddApiCaseToApiSuiteLogic {
	return &AddApiCaseToApiSuiteLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// AddApiCaseToApiSuite 添加API用例到API集合中
func (l *AddApiCaseToApiSuiteLogic) AddApiCaseToApiSuite(in *pb.AddApiCaseToApiSuiteReq) (
	resp *pb.AddApiCaseToApiSuiteResp, err error,
) {
	// validate the project_id in req
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, in.GetProjectId()); err != nil {
		return nil, err
	}

	// validate the suite_id in req
	if _, err = model.CheckApiSuiteBySuiteId(
		l.ctx, l.svcCtx.ApiSuiteModel, in.GetProjectId(), in.GetSuiteId(),
	); err != nil {
		return nil, err
	}

	rrs, err := l.svcCtx.ApiCaseReferenceModel.FindReferenceByReference(
		l.ctx, in.GetProjectId(), common.ConstReferenceTypeApiSuite, in.GetSuiteId(),
	)
	if err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()),
			"failed to find api case reference with project_id[%s] and suite_id[%s], error: %+v", in.GetProjectId(),
			in.GetSuiteId(), err,
		)
	}

	cache := make(types.StringPlaceholderCache, len(rrs))
	for _, rr := range rrs {
		cache[rr.CaseId] = lang.Placeholder
	}

	caseIds := stringx.Distinct(in.GetCaseIds())

	// acquire redis lock
	key := fmt.Sprintf("%s:%s:%s", common.ConstLockApiSuiteProjectIdSuiteIdPrefix, in.GetProjectId(), in.GetSuiteId())
	lock, err := redislock.NewRedisLockAndAcquire(l.svcCtx.Redis, key, redislock.WithExpire(common.ConstLockExpireTime))
	if err != nil {
		return nil, err
	}
	defer func() {
		// release redis lock
		e := lock.Release()
		if e != nil {
			// l.Logger.Error(e)
			if re, ok := errorx.RootError(e); ok && re.Code() != errorx.ReleaseRedisLockFailure && err == nil {
				err = e
			}
		}
	}()

	if err = l.svcCtx.ApiCaseReferenceModel.Trans(
		l.ctx, func(context context.Context, session sqlx.Session) error {
			return mr.MapReduceVoid[string, string](
				func(source chan<- string) {
					for _, caseId := range caseIds {
						if _, ok := cache[caseId]; ok {
							continue
						} else {
							cache[caseId] = lang.Placeholder
						}

						source <- caseId
					}
				}, func(item string, writer mr.Writer[string], cancel func(error)) {
					var err error
					defer func() {
						if err != nil {
							cancel(err)
						}
					}()

					if _, err = model.CheckApiCaseByCaseId(
						l.ctx, l.svcCtx.ApiCaseModel, in.GetProjectId(), item, "",
					); err != nil {
						return
					}

					writer.Write(item)
				}, func(pipe <-chan string, cancel func(error)) {
					var err error
					defer func() {
						if err != nil {
							cancel(err)
						}
					}()

					req := createApiCaseReferenceInternalReq{
						ProjectId: in.GetProjectId(),
						SuiteId:   in.GetSuiteId(),
						CaseIds: make(
							[]string, 0, constants.ConstDefaultMakeSliceSize,
						), // 预置切片容量为`constants.ConstDefaultMakeSliceSize`，减少切片扩容的情况
					}

					for item := range pipe {
						req.CaseIds = append(req.CaseIds, item)
					}

					// create api case reference of api suite
					if err = l.createApiCaseReference(context, session, req); err != nil {
						return
					}

					// create api plan reference of api case which is added to api suite
					if err = l.createApiPlanReference(
						context, session, createOrRemoveApiPlanReferenceInternalReq{
							createApiCaseReferenceInternalReq: req,
						},
					); err != nil {
						return
					}
				}, mr.WithContext(context), mr.WithWorkers(len(caseIds)),
			)
		},
	); err != nil {
		return nil, err
	}

	return &pb.AddApiCaseToApiSuiteResp{}, nil
}
