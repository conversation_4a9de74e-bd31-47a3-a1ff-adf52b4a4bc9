package apisuiteservicelogic

import (
	"context"
	"database/sql"
	"fmt"
	"time"

	"github.com/Masterminds/squirrel"
	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/lang"
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/mr"
	"github.com/zeromicro/go-zero/core/stores/sqlx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sqlbuilder"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/userinfo"
	commonutils "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/utils"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/logic"
	apiplanservicelogic "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/logic/apiplanservice"
	tagservicelogic "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/logic/tagservice"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type BaseLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext

	currentUser *userinfo.UserInfo

	createTagLogic              *tagservicelogic.CreateTagLogic
	addSuiteToApiPlanLogic      *apiplanservicelogic.AddSuiteToApiPlanLogic
	removeSuiteFromApiPlanLogic *apiplanservicelogic.RemoveSuiteFromApiPlanLogic

	converters []commonutils.TypeConverter
}

func newBaseLogic(ctx context.Context, svcCtx *svc.ServiceContext) *BaseLogic {
	return &BaseLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,

		currentUser: userinfo.FromContext(ctx),

		createTagLogic:              tagservicelogic.NewCreateTagLogic(ctx, svcCtx),
		addSuiteToApiPlanLogic:      apiplanservicelogic.NewAddSuiteToApiPlanLogic(ctx, svcCtx),
		removeSuiteFromApiPlanLogic: apiplanservicelogic.NewRemoveSuiteFromApiPlanLogic(ctx, svcCtx),

		converters: []commonutils.TypeConverter{
			logic.SqlNullStringToTags(),
			logic.StringToResourceState(),
		},
	}
}

func (l *BaseLogic) generateSuiteId(projectId string) (string, error) {
	g := commonutils.NewUniqueIdGenerator(
		commonutils.WithGenerateFunc(utils.GenSuiteId), commonutils.WithIsUniqueFunc(
			func(id string) bool {
				r, err := l.svcCtx.ApiSuiteModel.FindOneByProjectIdSuiteId(l.ctx, projectId, id)
				if errors.Is(err, model.ErrNotFound) || r == nil {
					return true
				}
				return false
			},
		),
	)
	caseId := g.Next()
	if caseId == "" {
		return "", errors.WithStack(
			errorx.Err(
				errorx.GenerateUniqueIdFailure, "failed to generate api suite id, please try it later",
			),
		)
	}

	return caseId, nil
}

func (l *BaseLogic) generateSearchSqlBuilder(req searchApiSuiteInternalReq) (sb, scb squirrel.SelectBuilder) {
	/*
		SQL:
		SELECT t.*
		FROM api_suite AS t
			LEFT JOIN (
				SELECT t1.*
			    FROM category AS t1
					INNER JOIN category_tree AS t2 ON t1.`project_id` = t2.`project_id` AND t1.`category_id` = t2.`descendant`
			    WHERE t1.`project_id` = ?
			      AND t1.`type` = ?
			      AND t2.`ancestor` = ?
			      AND t1.`deleted` = ?
			      AND t2.`deleted` = ?
			    ORDER BY t2.`depth` DESC, t2.`index` ASC, t1.`created_at` ASC
			) AS t1 ON t.`category_id` = t1.`category_id`
		WHERE t.`project_id` = ?
		  AND t.`deleted` = ?
		  AND t1.`category_id` IS NOT NULL
	*/

	var (
		m     = l.svcCtx.ApiSuiteModel
		alias = "t"

		projectId  = req.GetProjectId()
		categoryId = req.GetCategoryId()
	)

	sb = squirrel.Select(utils.AddTableNameToFields(alias, m.Fields())...).
		From(fmt.Sprintf("%s AS %s", m.Table(), alias)).
		Where(fmt.Sprintf("%s.`project_id` = ? AND %s.`deleted` = ?", alias, alias), projectId, constants.NotDeleted)
	scb = squirrel.Select("COUNT(*)").
		From(fmt.Sprintf("%s AS %s", m.Table(), alias)).
		Where(fmt.Sprintf("%s.`project_id` = ? AND %s.`deleted` = ?", alias, alias), projectId, constants.NotDeleted)

	if req.DrillDown {
		sub := l.svcCtx.CategoryModel.FindDescendantCategoriesSqlBuilder(
			model.GetCategoryTreeCondition{
				ProjectId:     projectId,
				Type:          common.ConstCategoryTreeTypeApiSuite,
				CategoryId:    categoryId,
				OnlyDirectory: true,
			},
		)

		sb = sb.JoinClause(
			sub.Prefix("LEFT JOIN (").Suffix(
				fmt.Sprintf(
					") AS t1 ON %s.`category_id` = t1.`category_id`", alias,
				),
			),
		).Where("t1.`category_id` IS NOT NULL")
		scb = scb.JoinClause(
			sub.Prefix("LEFT JOIN (").Suffix(
				fmt.Sprintf(
					") AS t1 ON %s.`category_id` = t1.`category_id`", alias,
				),
			),
		).Where("t1.`category_id` IS NOT NULL")
	} else {
		sb = sb.Where(fmt.Sprintf("%s.`category_id` = ?", alias), categoryId)
		scb = scb.Where(fmt.Sprintf("%s.`category_id` = ?", alias), categoryId)
	}

	sb = sqlbuilder.SearchOptionsWithAlias(
		sb, alias, sqlbuilder.WithCondition(m, req.GetCondition()), sqlbuilder.WithPagination(m, req.GetPagination()),
		sqlbuilder.WithSort(m, req.GetSort()),
	)
	scb = sqlbuilder.SearchOptionsWithAlias(scb, alias, sqlbuilder.WithCondition(m, req.GetCondition()))

	return sb, scb
}

func (l *BaseLogic) generateSearchCasesInSuiteSqlBuilder(req *pb.SearchApiCaseInApiSuiteReq) (sb, scb squirrel.SelectBuilder) {
	/*
		SQL:
		SELECT t1.*
		FROM `api_case` AS t1, `api_case_reference_relationship` AS t2
		WHERE t1.`project_id` = t2.`project_id`
		  AND t1.`case_id` = t2.`case_id`
		  AND t1.`project_id` = ?
		  AND t1.`latest` = 1
		  AND t1.`deleted` = 0
		  AND t2.`reference_type` = 'API_SUITE'
		  AND t2.`reference_id` = ?
		  AND t2.`deleted` = 0;
	*/

	m := l.svcCtx.ApiCaseModel

	sb = squirrel.Select(utils.AddTableNameToFields("t1", m.Fields())...).
		From(fmt.Sprintf("%s AS t1, %s AS t2", m.Table(), l.svcCtx.ApiCaseReferenceModel.Table())).
		Where(
			"t1.`project_id` = t2.`project_id` AND t1.`case_id` = t2.`case_id` AND t1.`project_id` = ? AND t1.`latest` = ? AND t1.`deleted` = ? AND t2.`reference_type` = ? AND t2.`reference_id` = ? AND t2.`deleted` = ?",
			req.GetProjectId(), constants.IsLatestVersion, constants.NotDeleted, common.ConstReferenceTypeApiSuite,
			req.GetSuiteId(), constants.NotDeleted,
		)
	scb = squirrel.Select("COUNT(t1.`id`)").
		From(fmt.Sprintf("%s AS t1, %s AS t2", m.Table(), l.svcCtx.ApiCaseReferenceModel.Table())).
		Where(
			"t1.`project_id` = t2.`project_id` AND t1.`case_id` = t2.`case_id` AND t1.`project_id` = ? AND t1.`latest` = ? AND t1.`deleted` = ? AND t2.`reference_type` = ? AND t2.`reference_id` = ? AND t2.`deleted` = ?",
			req.GetProjectId(), constants.IsLatestVersion, constants.NotDeleted, common.ConstReferenceTypeApiSuite,
			req.GetSuiteId(), constants.NotDeleted,
		)

	sb = sqlbuilder.SearchOptionsWithAlias(
		sb, "t1", sqlbuilder.WithCondition(m, req.GetCondition()), sqlbuilder.WithPagination(m, req.GetPagination()),
		sqlbuilder.WithSort(m, req.GetSort()),
	)
	scb = sqlbuilder.SearchOptionsWithAlias(scb, "t1", sqlbuilder.WithCondition(m, req.GetCondition()))

	return sb, scb
}

func (l *BaseLogic) generateSearchCasesInSuiteSqlBuilder2(req *pb.SearchApiCaseInApiSuiteReq) (sb, scb squirrel.SelectBuilder) {
	/*
		SQL:
		SELECT t1.*
		FROM `api_case` AS t1, `api_suite_reference_relationship` AS t2
		WHERE t1.`project_id` = t2.`project_id`
		  AND t1.`case_id` = t2.`reference_id`
		  AND t1.`project_id` = ?
		  AND t1.`latest` = 1
		  AND t1.`deleted` = 0
		  AND t2.`reference_type` = 'API_CASE'
		  AND t2.`suite_id` = ?
		  AND t2.`deleted` = 0;
	*/

	m := l.svcCtx.ApiCaseModel

	sb = squirrel.Select(utils.AddTableNameToFields("t1", m.Fields())...).
		From(fmt.Sprintf("%s AS t1, %s AS t2", m.Table(), l.svcCtx.ApiSuiteReferenceModel.Table())).
		Where(
			"t1.`project_id` = t2.`project_id` AND t1.`case_id` = t2.`reference_id` AND t1.`project_id` = ? AND t1.`latest` = ? AND t1.`deleted` = ? AND t2.`reference_type` = ? AND t2.`suite_id` = ? AND t2.`deleted` = ?",
			req.GetProjectId(), constants.IsLatestVersion, constants.NotDeleted, common.ConstReferenceTypeApiCase,
			req.GetSuiteId(), constants.NotDeleted,
		)
	scb = squirrel.Select("COUNT(t1.`id`)").
		From(fmt.Sprintf("%s AS t1, %s AS t2", m.Table(), l.svcCtx.ApiCaseReferenceModel.Table())).
		Where(
			"t1.`project_id` = t2.`project_id` AND t1.`case_id` = t2.`case_id` AND t1.`project_id` = ? AND t1.`latest` = ? AND t1.`deleted` = ? AND t2.`reference_type` = ? AND t2.`reference_id` = ? AND t2.`deleted` = ?",
			req.GetProjectId(), constants.IsLatestVersion, constants.NotDeleted, common.ConstReferenceTypeApiSuite,
			req.GetSuiteId(), constants.NotDeleted,
		)

	sb = sqlbuilder.SearchOptionsWithAlias(
		sb, "t1", sqlbuilder.WithCondition(m, req.GetCondition()), sqlbuilder.WithPagination(m, req.GetPagination()),
		sqlbuilder.WithSort(m, req.GetSort()),
	)
	scb = sqlbuilder.SearchOptionsWithAlias(scb, "t1", sqlbuilder.WithCondition(m, req.GetCondition()))

	return sb, scb
}

func (l *BaseLogic) generateSearchCasesNotInSuiteSqlBuilder(req searchApiCaseNotInApiSuiteInternalReq) (sb, scb squirrel.SelectBuilder) {
	/*
		SQL:
		SELECT t.*
		FROM api_case AS t
			LEFT JOIN (
				SELECT t1.*
		        FROM category AS t1
		        	INNER JOIN category_tree AS t2 ON t1.`project_id` = t2.`project_id` AND t1.`category_id` = t2.`descendant`
				WHERE t1.`project_id` = ?
		          AND t1.`type` = 'API_CASE'
		          AND t1.`category_type` = 'DIRECTORY'
		          AND t2.`ancestor` = ?
		          AND t1.`deleted` = ?
		          AND t2.`deleted` = ?
		          ORDER BY t2.`depth` DESC, t2.`index` ASC, t1.`created_at` ASC) AS t1 ON t.category_id = t1.category_id
			LEFT JOIN (
				SELECT t1.*
		        FROM api_case AS t1, api_suite_reference_relationship AS t2
		        WHERE t1.`project_id` = t2.`project_id`
				  AND t1.`case_id` = t2.`reference_id`
		          AND t1.`project_id` = ?
		          AND t1.`latest` = ?
		          AND t1.`deleted` = ?
		          AND t2.`reference_type` = 'API_CASE'
		          AND t2.`suite_id` = ?
		          AND t2.`deleted` = ?) AS t2 ON t.`case_id` = t2.`case_id`
		WHERE t.`project_id` = ?
		  AND t.`deleted` = ?
		  AND t.`latest` = ?
		  AND t1.`category_id` IS NOT NULL
		  AND t2.`case_id` IS NULL
	*/

	var (
		m     = l.svcCtx.ApiCaseModel
		alias = "t"

		projectId  = req.GetProjectId()
		categoryId = req.GetCategoryId()
	)

	sb = squirrel.Select(utils.AddTableNameToFields(alias, m.Fields())...).
		From(fmt.Sprintf("%s AS %s", m.Table(), alias)).
		Where(
			fmt.Sprintf("%s.`project_id` = ? AND %s.`latest` = ? AND %s.`deleted` = ?", alias, alias, alias), projectId,
			constants.IsLatestVersion, constants.NotDeleted,
		)
	scb = squirrel.Select("COUNT(*)").
		From(fmt.Sprintf("%s AS %s", m.Table(), alias)).
		Where(
			fmt.Sprintf("%s.`project_id` = ? AND %s.`latest` = ? AND %s.`deleted` = ?", alias, alias, alias), projectId,
			constants.IsLatestVersion, constants.NotDeleted,
		)

	if req.DrillDown {
		sub1 := l.svcCtx.CategoryModel.FindDescendantCategoriesSqlBuilder(
			model.GetCategoryTreeCondition{
				ProjectId:     projectId,
				Type:          common.ConstCategoryTreeTypeApiCase,
				CategoryId:    categoryId,
				OnlyDirectory: true,
			},
		)

		sb = sb.JoinClause(
			sub1.Prefix("LEFT JOIN (").Suffix(
				fmt.Sprintf(
					") AS t1 ON %s.`category_id` = t1.`category_id`", alias,
				),
			),
		).
			Where("t1.`category_id` IS NOT NULL")
		scb = scb.JoinClause(
			sub1.Prefix("LEFT JOIN (").Suffix(
				fmt.Sprintf(
					") AS t1 ON %s.`category_id` = t1.`category_id`", alias,
				),
			),
		).
			Where("t1.`category_id` IS NOT NULL")
	} else {
		sb = sb.Where(fmt.Sprintf("%s.`category_id` = ?", alias), categoryId)
		scb = scb.Where(fmt.Sprintf("%s.`category_id` = ?", alias), categoryId)
	}

	// sub2, _ := l.generateSearchCasesInSuiteSqlBuilder(
	sub2, _ := l.generateSearchCasesInSuiteSqlBuilder2(
		&pb.SearchApiCaseInApiSuiteReq{
			ProjectId: projectId,
			SuiteId:   req.GetSuiteId(),
		},
	)

	sb = sb.JoinClause(sub2.Prefix("LEFT JOIN (").Suffix(fmt.Sprintf(") AS t2 ON %s.`case_id` = t2.`case_id`", alias))).
		Where("t2.`case_id` IS NULL")
	scb = scb.JoinClause(
		sub2.Prefix("LEFT JOIN (").Suffix(
			fmt.Sprintf(
				") AS t2 ON %s.`case_id` = t2.`case_id`", alias,
			),
		),
	).
		Where("t2.`case_id` IS NULL")

	sb = sqlbuilder.SearchOptionsWithAlias(
		sb, alias, sqlbuilder.WithCondition(m, req.GetCondition()), sqlbuilder.WithPagination(m, req.GetPagination()),
		sqlbuilder.WithSort(m, req.GetSort()),
	)
	scb = sqlbuilder.SearchOptionsWithAlias(scb, alias, sqlbuilder.WithCondition(m, req.GetCondition()))

	return sb, scb
}

// createApiCaseReference 创建API集合与API用例的引用关系
func (l *BaseLogic) createApiCaseReference(
	ctx context.Context, session sqlx.Session, req createApiCaseReferenceInternalReq,
) error {
	if ctx == nil {
		ctx = l.ctx
	}

	m := l.svcCtx.ApiCaseReferenceModel

	datas := make([]*model.ApiCaseReferenceRelationship, 0, len(req.CaseIds))
	for _, caseId := range req.CaseIds {
		datas = append(
			datas, &model.ApiCaseReferenceRelationship{
				ProjectId:     req.ProjectId,
				ReferenceType: common.ConstReferenceTypeApiSuite,
				ReferenceId:   req.SuiteId,
				CaseId:        caseId,
				CreatedBy:     l.currentUser.Account,
				UpdatedBy:     l.currentUser.Account,
				CreatedAt:     time.Now(),
				UpdatedAt:     time.Now(),
			},
		)
	}

	if _, err := l.svcCtx.ApiCaseReferenceModel.BatchInsertTX(ctx, session, datas); err != nil {
		return errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()), "failed to batch insert table[%s] with values[%+v], error: %+v",
			m.Table(), datas, err,
		)
	}

	return nil
}

// createApiSuiteReference 创建API集合与用例的引用关系
func (l *BaseLogic) createApiSuiteReference(
	ctx context.Context, session sqlx.Session, req createApiSuiteReferenceInternalReq,
) error {
	if ctx == nil {
		ctx = l.ctx
	}

	m := l.svcCtx.ApiSuiteReferenceModel

	datas := make([]*model.ApiSuiteReferenceRelationship, 0, len(req.Items))
	for _, item := range req.Items {
		datas = append(
			datas, &model.ApiSuiteReferenceRelationship{
				ProjectId:     req.ProjectId,
				ReferenceType: item.GetCaseType(),
				ReferenceId:   item.CaseId,
				SuiteId:       req.SuiteId,
				CreatedBy:     l.currentUser.Account,
				UpdatedBy:     l.currentUser.Account,
				CreatedAt:     time.Now(),
				UpdatedAt:     time.Now(),
			},
		)
	}

	if _, err := l.svcCtx.ApiSuiteReferenceModel.BatchInsertTX(ctx, session, datas); err != nil {
		return errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()), "failed to batch insert table[%s] with values[%+v], error: %+v",
			m.Table(), datas, err,
		)
	}

	return nil
}

// removeApiCaseReference 删除API集合与API用例的引用关系
func (l *BaseLogic) removeApiCaseReference(
	ctx context.Context, session sqlx.Session, data *model.ApiCaseReferenceRelationship,
) error {
	if ctx == nil {
		ctx = l.ctx
	}

	m := l.svcCtx.ApiCaseReferenceModel

	data.Deleted = int64(constants.HasDeleted)
	data.DeletedBy = sql.NullString{
		String: l.currentUser.Account,
		Valid:  true,
	}
	data.DeletedAt = sql.NullTime{
		Time:  time.Now(),
		Valid: true,
	}
	if _, err := l.svcCtx.ApiCaseReferenceModel.Update(ctx, session, data); err != nil {
		return errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()), "failed to update table[%s] with values[%+v], error: %+v",
			m.Table(), data, err,
		)
	}

	return nil
}

// removeApiSuiteReference 删除API集合与用例的引用关系
func (l *BaseLogic) removeApiSuiteReference(
	ctx context.Context, session sqlx.Session, data *model.ApiSuiteReferenceRelationship,
) error {
	if ctx == nil {
		ctx = l.ctx
	}

	m := l.svcCtx.ApiSuiteReferenceModel

	data.Deleted = int64(constants.HasDeleted)
	data.DeletedBy = sql.NullString{
		String: l.currentUser.Account,
		Valid:  true,
	}
	data.DeletedAt = sql.NullTime{
		Time:  time.Now(),
		Valid: true,
	}
	if _, err := l.svcCtx.ApiSuiteReferenceModel.Update(ctx, session, data); err != nil {
		return errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()), "failed to update table[%s] with values[%+v], error: %+v",
			m.Table(), data, err,
		)
	}

	return nil
}

// createApiPlanReference 创建API计划与API集合、API用例的引用关系
func (l *BaseLogic) createApiPlanReference(
	ctx context.Context, session sqlx.Session, req createOrRemoveApiPlanReferenceInternalReq,
) error {
	if ctx == nil {
		ctx = l.ctx
	}

	rrs, err := l.svcCtx.ApiPlanReferenceModel.FindReferenceByReference(
		l.ctx, req.ProjectId, common.ConstReferenceTypeApiSuite, req.SuiteId, false,
	)
	if err != nil {
		return errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()),
			"failed to find api plan reference with project_id[%s], reference_type[%s] and reference_id[%s], error: %+v",
			req.ProjectId, common.ConstReferenceTypeApiSuite, req.SuiteId, err,
		)
	} else if len(rrs) == 0 {
		return nil
	}

	workers := len(rrs)
	if workers > common.ConstMRMaxWorkers {
		workers = common.ConstMRMaxWorkers
	}

	cache := make(types.StringPlaceholderCache, len(rrs))

	var caseIds []string
	if len(req.CaseIds) == 0 {
		acs, err := l.svcCtx.ApiCaseModel.FindLatestBySuiteId(ctx, req.ProjectId, req.SuiteId)
		if err != nil {
			return errors.Wrapf(
				errorx.Err(errorx.DBError, err.Error()),
				"failed to find api cases with project_id[%s] and suite_id[%s], error: %+v", req.ProjectId, req.SuiteId,
				err,
			)
		}

		for _, ac := range acs {
			caseIds = append(caseIds, ac.CaseId)
		}
	} else {
		caseIds = req.CaseIds
	}

	return mr.MapReduceVoid[string, any](
		func(source chan<- string) {
			for _, rr := range rrs {
				if _, ok := cache[rr.PlanId]; ok {
					continue
				} else {
					cache[rr.PlanId] = lang.Placeholder
				}

				source <- rr.PlanId
			}
		}, func(item string, writer mr.Writer[any], cancel func(error)) {
			var err error
			defer func() {
				if err != nil {
					cancel(err)
				}
			}()

			err = l.addSuiteToApiPlanLogic.AddReferenceToApiPlanForInternal(
				ctx, session, apiplanservicelogic.CreateOrRemoveReferenceInternalReq{
					ProjectId:         req.ProjectId,
					ReferenceParentId: req.SuiteId,
					ReferenceType:     common.ConstReferenceTypeApiCase,
					ReferenceIds:      caseIds,
					PlanId:            item,
				},
			)
		}, func(pipe <-chan any, cancel func(error)) {
		}, mr.WithContext(ctx), mr.WithWorkers(workers),
	)
}

// createApiPlanReference2 创建API计划与API集合、用例的引用关系
func (l *BaseLogic) createApiPlanReference2(
	ctx context.Context, session sqlx.Session, req createOrRemoveApiPlanReference2InternalReq,
) error {
	if ctx == nil {
		ctx = l.ctx
	}

	rrs, err := l.svcCtx.ApiPlanReferenceModel.FindReferenceByReference(
		l.ctx, req.ProjectId, common.ConstReferenceTypeApiSuite, req.SuiteId, false,
	)
	if err != nil {
		return errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()),
			"failed to find api plan reference with project_id[%s], reference_type[%s] and reference_id[%s], error: %+v",
			req.ProjectId, common.ConstReferenceTypeApiSuite, req.SuiteId, err,
		)
	} else if len(rrs) == 0 {
		return nil
	}

	workers := len(rrs)
	if workers > common.ConstMRMaxWorkers {
		workers = common.ConstMRMaxWorkers
	}

	cache := make(types.StringPlaceholderCache, len(rrs))

	var references []*apiplanservicelogic.CreateOrRemoveReference
	for _, item := range req.Items {
		references = append(references, &apiplanservicelogic.CreateOrRemoveReference{
			ReferenceParentId: req.SuiteId,
			ReferenceType:     item.GetCaseType(),
			ReferenceId:       item.GetCaseId(),
		})
	}

	return mr.MapReduceVoid[string, any](
		func(source chan<- string) {
			for _, rr := range rrs {
				if _, ok := cache[rr.PlanId]; ok {
					continue
				} else {
					cache[rr.PlanId] = lang.Placeholder
				}

				source <- rr.PlanId
			}
		}, func(item string, writer mr.Writer[any], cancel func(error)) {
			var err error
			defer func() {
				if err != nil {
					cancel(err)
				}
			}()

			err = l.addSuiteToApiPlanLogic.AddReferenceToApiPlan2ForInternal(
				ctx, session, apiplanservicelogic.CreateOrRemoveReference2InternalReq{
					ProjectId:  req.ProjectId,
					References: references,
					PlanId:     item,
				},
			)
		}, func(pipe <-chan any, cancel func(error)) {
		}, mr.WithContext(ctx), mr.WithWorkers(workers),
	)
}

// removeApiPlanReference 删除API计划与API集合、API用例的引用关系
func (l *BaseLogic) removeApiPlanReference(
	ctx context.Context, session sqlx.Session, req createOrRemoveApiPlanReferenceInternalReq,
) error {
	if ctx == nil {
		ctx = l.ctx
	}

	rrs, err := l.svcCtx.ApiPlanReferenceModel.FindReferenceByReference(
		l.ctx, req.ProjectId, common.ConstReferenceTypeApiSuite, req.SuiteId, false,
	)
	if err != nil {
		return errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()),
			"failed to find api plan reference with project_id[%s], reference_type[%s] and reference_id[%s], error: %+v",
			req.ProjectId, common.ConstReferenceTypeApiSuite, req.SuiteId, err,
		)
	} else if len(rrs) == 0 {
		return nil
	}

	workers := len(rrs)
	if workers > common.ConstMRMaxWorkers {
		workers = common.ConstMRMaxWorkers
	}

	cache := make(types.StringPlaceholderCache, len(rrs))

	return mr.MapReduceVoid[string, any](
		func(source chan<- string) {
			for _, rr := range rrs {
				if _, ok := cache[rr.PlanId]; ok {
					continue
				} else {
					cache[rr.PlanId] = lang.Placeholder
				}

				source <- rr.PlanId
			}
		}, func(item string, writer mr.Writer[any], cancel func(error)) {
			var err error
			defer func() {
				if err != nil {
					cancel(err)
				}
			}()

			if len(req.CaseIds) != 0 {
				// 从API集合中移除API用例
				err = l.removeSuiteFromApiPlanLogic.RemoveReferenceFromApiPlanForInternal(
					ctx, session, apiplanservicelogic.CreateOrRemoveReferenceInternalReq{
						ProjectId:         req.ProjectId,
						ReferenceParentId: req.SuiteId,
						ReferenceType:     common.ConstReferenceTypeApiCase,
						ReferenceIds:      req.CaseIds,
						PlanId:            item,
					},
				)
			} else {
				// 删除API集合
				err = l.removeSuiteFromApiPlanLogic.RemoveReferenceFromApiPlanForInternal(
					ctx, session, apiplanservicelogic.CreateOrRemoveReferenceInternalReq{
						ProjectId:     req.ProjectId,
						ReferenceType: common.ConstReferenceTypeApiSuite,
						ReferenceIds:  []string{req.SuiteId},
						PlanId:        item,
					},
				)
			}
		}, func(pipe <-chan any, cancel func(error)) {
		}, mr.WithContext(ctx), mr.WithWorkers(workers),
	)
}

// removeApiPlanReference2 删除API计划与API集合、用例的引用关系
func (l *BaseLogic) removeApiPlanReference2(
	ctx context.Context, session sqlx.Session, req createOrRemoveApiPlanReference2InternalReq,
) error {
	if ctx == nil {
		ctx = l.ctx
	}

	rrs, err := l.svcCtx.ApiPlanReferenceModel.FindReferenceByReference(
		l.ctx, req.ProjectId, common.ConstReferenceTypeApiSuite, req.SuiteId, false,
	)
	if err != nil {
		return errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()),
			"failed to find api plan reference with project_id[%s], reference_type[%s] and reference_id[%s], error: %+v",
			req.ProjectId, common.ConstReferenceTypeApiSuite, req.SuiteId, err,
		)
	} else if len(rrs) == 0 {
		return nil
	}

	workers := len(rrs)
	if workers > common.ConstMRMaxWorkers {
		workers = common.ConstMRMaxWorkers
	}

	cache := make(types.StringPlaceholderCache, len(rrs))

	var references []*apiplanservicelogic.CreateOrRemoveReference
	for _, item := range req.Items {
		references = append(references, &apiplanservicelogic.CreateOrRemoveReference{
			ReferenceParentId: req.SuiteId,
			ReferenceType:     item.GetCaseType(),
			ReferenceId:       item.GetCaseId(),
		})
	}

	return mr.MapReduceVoid[string, any](
		func(source chan<- string) {
			for _, rr := range rrs {
				if _, ok := cache[rr.PlanId]; ok {
					continue
				} else {
					cache[rr.PlanId] = lang.Placeholder
				}

				source <- rr.PlanId
			}
		}, func(item string, writer mr.Writer[any], cancel func(error)) {
			var err error
			defer func() {
				if err != nil {
					cancel(err)
				}
			}()

			if len(req.Items) != 0 {
				// 从API集合中移除用例
				err = l.removeSuiteFromApiPlanLogic.RemoveReferenceFromApiPlan2ForInternal(
					ctx, session, apiplanservicelogic.CreateOrRemoveReference2InternalReq{
						ProjectId:  req.ProjectId,
						References: references,
						PlanId:     item,
					},
				)
			} else {
				// 删除API集合
				err = l.removeSuiteFromApiPlanLogic.RemoveReferenceFromApiPlanForInternal(
					ctx, session, apiplanservicelogic.CreateOrRemoveReferenceInternalReq{
						ProjectId:     req.ProjectId,
						ReferenceType: common.ConstReferenceTypeApiSuite,
						ReferenceIds:  []string{req.SuiteId},
						PlanId:        item,
					},
				)
			}
		}, func(pipe <-chan any, cancel func(error)) {
		}, mr.WithContext(ctx), mr.WithWorkers(workers),
	)
}
