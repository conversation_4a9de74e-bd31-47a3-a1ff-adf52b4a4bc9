package apisuiteservicelogic

import (
	"context"
	"database/sql"
	"fmt"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"
	"github.com/zeromicro/go-zero/core/stores/sqlx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/redislock"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type ModifyApiSuiteLogic struct {
	*BaseLogic
}

func NewModifyApiSuiteLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ModifyApiSuiteLogic {
	return &ModifyApiSuiteLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// ModifyApiSuite 编辑API集合
func (l *ModifyApiSuiteLogic) ModifyApiSuite(in *pb.ModifyApiSuiteReq) (resp *pb.ModifyApiSuiteResp, err error) {
	// validate the project_id in req
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, in.GetProjectId()); err != nil {
		return nil, err
	}

	var c *model.Category
	// validate the category_id in req
	if c, err = model.CheckCategoryByCategoryId(l.ctx, l.svcCtx.CategoryModel, in.GetProjectId(), common.ConstCategoryTreeTypeApiSuite, in.GetCategoryId()); err != nil {
		return nil, err
	} else if c.CategoryType != common.ConstCategoryTypeDirectory {
		return nil, errors.WithStack(errorx.Err(errorx.ProhibitedBehavior, fmt.Sprintf("the type of parent category[%s] does not support creation of sub category", c.CategoryType)))
	}

	// validate the suite_id in req
	origin, err := model.CheckApiSuiteBySuiteId(l.ctx, l.svcCtx.ApiSuiteModel, in.GetProjectId(), in.GetSuiteId())
	if err != nil {
		return nil, err
	}

	// acquire redis lock
	key := fmt.Sprintf("%s:%s:%s", common.ConstLockApiSuiteProjectIdSuiteIdPrefix, in.GetProjectId(), in.GetSuiteId())
	lock, err := redislock.NewRedisLockAndAcquire(l.svcCtx.Redis, key, redislock.WithExpire(common.ConstLockExpireTime))
	if err != nil {
		return nil, err
	}
	defer func() {
		// release redis lock
		e := lock.Release()
		if e != nil {
			// l.Logger.Error(e)
			if re, ok := errorx.RootError(e); ok && re.Code() != errorx.ReleaseRedisLockFailure && err == nil {
				err = e
			}
		}
	}()

	apiSuite, err := l.modify(in, origin)
	if err != nil {
		return nil, err
	}

	resp = &pb.ModifyApiSuiteResp{Suite: &pb.ApiSuite{}}
	if err = utils.Copy(resp.Suite, apiSuite, l.converters...); err != nil {
		return nil, errors.Wrapf(errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy api suite[%+v] to response, error: %+v", apiSuite, err)
	}

	return resp, nil
}

// in order to reduce cyclomatic complexity of ModifyApiSuiteLogic.ModifyApiSuite
func (l *ModifyApiSuiteLogic) modify(req *pb.ModifyApiSuiteReq, origin *model.ApiSuite) (*model.ApiSuite, error) {
	// 由于`protojson`只能序列化`[]proto.Message`，而`tags`字段类型为`[]string`，Go Struct跟PB没有大的区别，因此直接使用`jsonx`暂代`protojson`
	tags := jsonx.MarshalToStringIgnoreError(req.GetTags())

	apiSuite := &model.ApiSuite{
		Id:         origin.Id,
		ProjectId:  origin.ProjectId,
		CategoryId: req.GetCategoryId(),
		SuiteId:    origin.SuiteId,
		Name:       req.GetName(),
		Description: sql.NullString{
			String: req.GetDescription(),
			Valid:  req.GetDescription() != "",
		},
		Priority: req.GetPriority(),
		Tags: sql.NullString{
			String: tags,
			Valid:  tags != "",
		},
		State:             int64(req.GetState()),
		CaseExecutionMode: int64(req.GetCaseExecutionMode()),
		MaintainedBy: sql.NullString{
			String: req.GetMaintainedBy(),
			Valid:  req.GetMaintainedBy() != "",
		},
		CreatedBy: origin.CreatedBy,
		UpdatedBy: l.currentUser.Account,
		CreatedAt: origin.CreatedAt,
	}

	// update api suite in a transaction
	if err := l.svcCtx.ApiSuiteModel.Trans(l.ctx, func(context context.Context, session sqlx.Session) error {
		if _, err := l.svcCtx.ApiSuiteModel.UpdateTX(context, session, apiSuite); err != nil {
			return errors.Wrapf(errorx.Err(errorx.DBError, err.Error()), "failed to update table[%s] with values[%+v], error: %+v", l.svcCtx.ApiSuiteModel.Table(), apiSuite, err)
		}

		// update the new tag and tag reference of api suite
		if err := l.createTagLogic.CreateTagAndReferenceForInternal(context, session, types.CreateOrUpdateTagReference{
			ProjectId:     apiSuite.ProjectId,
			ReferenceType: common.ConstReferenceTypeApiSuite,
			ReferenceId:   apiSuite.SuiteId,
			Tags:          req.GetTags(),
		}); err != nil {
			return err
		}

		return nil
	}); err != nil {
		return nil, err
	}

	return apiSuite, nil
}
