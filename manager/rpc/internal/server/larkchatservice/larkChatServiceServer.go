// Code generated by goctl. DO NOT EDIT.
// goctl 1.8.3
// Source: manager.proto

package server

import (
	"context"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"

	larkchatservicelogic "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/logic/larkchatservice"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type LarkChatServiceServer struct {
	svcCtx *svc.ServiceContext
	pb.UnimplementedLarkChatServiceServer
}

func NewLarkChatServiceServer(svcCtx *svc.ServiceContext) *LarkChatServiceServer {
	return &LarkChatServiceServer{
		svcCtx: svcCtx,
	}
}

// ModifyLarkChat 编辑测试通知飞书群组列表
func (s *LarkChatServiceServer) ModifyLarkChat(ctx context.Context, in *pb.ModifyLarkChatReq) (*pb.ModifyLarkChatResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := larkchatservicelogic.NewModifyLarkChatLogic(ctx, s.svcCtx)

	return l.ModifyLarkChat(in)
}

// SearchLarkChat 搜索测试通知飞书群组
func (s *LarkChatServiceServer) SearchLarkChat(ctx context.Context, in *pb.SearchLarkChatReq) (*pb.SearchLarkChatResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := larkchatservicelogic.NewSearchLarkChatLogic(ctx, s.svcCtx)

	return l.SearchLarkChat(in)
}

// DeleteLarkChat 删除测试通知飞书群组（由飞书群解散事件触发）
func (s *LarkChatServiceServer) DeleteLarkChat(ctx context.Context, in *pb.DeleteLarkChatReq) (*pb.DeleteLarkChatResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := larkchatservicelogic.NewDeleteLarkChatLogic(ctx, s.svcCtx)

	return l.DeleteLarkChat(in)
}

// UpdateLarkChat 更新测试通知飞书群组（由飞书群配置修改事件触发）
func (s *LarkChatServiceServer) UpdateLarkChat(ctx context.Context, in *pb.UpdateLarkChatReq) (*pb.UpdateLarkChatResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := larkchatservicelogic.NewUpdateLarkChatLogic(ctx, s.svcCtx)

	return l.UpdateLarkChat(in)
}
