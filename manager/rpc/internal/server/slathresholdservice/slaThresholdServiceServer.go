// Code generated by goctl. DO NOT EDIT.
// goctl 1.8.3
// Source: manager.proto

package server

import (
	"context"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"

	slathresholdservicelogic "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/logic/slathresholdservice"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type SlaThresholdServiceServer struct {
	svcCtx *svc.ServiceContext
	pb.UnimplementedSlaThresholdServiceServer
}

func NewSlaThresholdServiceServer(svcCtx *svc.ServiceContext) *SlaThresholdServiceServer {
	return &SlaThresholdServiceServer{
		svcCtx: svcCtx,
	}
}

// ModifySlaThreshold( 编辑SLA阈值配置
func (s *SlaThresholdServiceServer) ModifySlaThreshold(ctx context.Context, in *pb.ModifySlaThresholdReq) (*pb.ModifySlaThresholdResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := slathresholdservicelogic.NewModifySlaThresholdLogic(ctx, s.svcCtx)

	return l.ModifySlaThreshold(in)
}

// GetSlaThreshold 获取SLA阈值配置
func (s *SlaThresholdServiceServer) GetSlaThreshold(ctx context.Context, in *pb.GetSlaThresholdReq) (*pb.GetSlaThresholdResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := slathresholdservicelogic.NewGetSlaThresholdLogic(ctx, s.svcCtx)

	return l.GetSlaThreshold(in)
}
