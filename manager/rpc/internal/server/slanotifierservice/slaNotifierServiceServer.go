// Code generated by goctl. DO NOT EDIT.
// goctl 1.8.3
// Source: manager.proto

package server

import (
	"context"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"

	slanotifierservicelogic "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/logic/slanotifierservice"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type SlaNotifierServiceServer struct {
	svcCtx *svc.ServiceContext
	pb.UnimplementedSlaNotifierServiceServer
}

func NewSlaNotifierServiceServer(svcCtx *svc.ServiceContext) *SlaNotifierServiceServer {
	return &SlaNotifierServiceServer{
		svcCtx: svcCtx,
	}
}

// SearchSlaNotifier 获取SLA通知人员
func (s *SlaNotifierServiceServer) SearchSlaNotifier(ctx context.Context, in *pb.SearchSlaNotifierReq) (*pb.SearchSlaNotifierResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := slanotifierservicelogic.NewSearchSlaNotifierLogic(ctx, s.svcCtx)

	return l.SearchSlaNotifier(in)
}
