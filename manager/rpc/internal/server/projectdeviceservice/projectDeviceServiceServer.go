// Code generated by goctl. DO NOT EDIT.
// goctl 1.8.3
// Source: manager.proto

package server

import (
	"context"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"

	projectdeviceservicelogic "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/logic/projectdeviceservice"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type ProjectDeviceServiceServer struct {
	svcCtx *svc.ServiceContext
	pb.UnimplementedProjectDeviceServiceServer
}

func NewProjectDeviceServiceServer(svcCtx *svc.ServiceContext) *ProjectDeviceServiceServer {
	return &ProjectDeviceServiceServer{
		svcCtx: svcCtx,
	}
}

// ModifyProjectDevice 编辑项目设备列表
func (s *ProjectDeviceServiceServer) ModifyProjectDevice(ctx context.Context, in *pb.ModifyProjectDeviceReq) (*pb.ModifyProjectDeviceResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := projectdeviceservicelogic.NewModifyProjectDeviceLogic(ctx, s.svcCtx)

	return l.ModifyProjectDevice(in)
}

// GetProjectDevice 获取项目设备
func (s *ProjectDeviceServiceServer) GetProjectDevice(ctx context.Context, in *pb.GetProjectDeviceReq) (*pb.GetProjectDeviceResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := projectdeviceservicelogic.NewGetProjectDeviceLogic(ctx, s.svcCtx)

	return l.GetProjectDevice(in)
}

// SearchProjectDevice 搜索项目设备
func (s *ProjectDeviceServiceServer) SearchProjectDevice(ctx context.Context, in *pb.SearchProjectDeviceReq) (*pb.SearchProjectDeviceResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := projectdeviceservicelogic.NewSearchProjectDeviceLogic(ctx, s.svcCtx)

	return l.SearchProjectDevice(in)
}

// SearchUnassignedProjectDevice 搜索未分配到项目的设备
func (s *ProjectDeviceServiceServer) SearchUnassignedProjectDevice(ctx context.Context, in *pb.SearchUnassignedProjectDeviceReq) (*pb.SearchUnassignedProjectDeviceResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := projectdeviceservicelogic.NewSearchUnassignedProjectDeviceLogic(ctx, s.svcCtx)

	return l.SearchUnassignedProjectDevice(in)
}

// SearchProjectDeviceReference 搜索项目设备引用详情
func (s *ProjectDeviceServiceServer) SearchProjectDeviceReference(ctx context.Context, in *pb.SearchProjectDeviceReferenceReq) (*pb.SearchProjectDeviceReferenceResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := projectdeviceservicelogic.NewSearchProjectDeviceReferenceLogic(ctx, s.svcCtx)

	return l.SearchProjectDeviceReference(in)
}

// Deprecated: use `ProjectDeviceService.AcquireProjectDeviceByCondition` instead. AcquireProjectDevice 占用项目设备
func (s *ProjectDeviceServiceServer) AcquireProjectDevice(ctx context.Context, in *pb.AcquireProjectDeviceReq) (*pb.AcquireProjectDeviceResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := projectdeviceservicelogic.NewAcquireProjectDeviceLogic(ctx, s.svcCtx)

	return l.AcquireProjectDevice(in)
}

// AcquireProjectDeviceByUDID 占用项目设备（通过`udid`）
func (s *ProjectDeviceServiceServer) AcquireProjectDeviceByUDID(ctx context.Context, in *pb.AcquireProjectDeviceByUDIDReq) (*pb.AcquireProjectDeviceByUDIDResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := projectdeviceservicelogic.NewAcquireProjectDeviceByUDIDLogic(ctx, s.svcCtx)

	return l.AcquireProjectDeviceByUDID(in)
}

// AcquireProjectDeviceByCondition 占用项目设备（通过查询条件）
func (s *ProjectDeviceServiceServer) AcquireProjectDeviceByCondition(ctx context.Context, in *pb.AcquireProjectDeviceByConditionReq) (*pb.AcquireProjectDeviceByConditionResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := projectdeviceservicelogic.NewAcquireProjectDeviceByConditionLogic(ctx, s.svcCtx)

	return l.AcquireProjectDeviceByCondition(in)
}

// ReleaseProjectDevice 释放项目设备
func (s *ProjectDeviceServiceServer) ReleaseProjectDevice(ctx context.Context, in *pb.ReleaseProjectDeviceReq) (*pb.ReleaseProjectDeviceResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := projectdeviceservicelogic.NewReleaseProjectDeviceLogic(ctx, s.svcCtx)

	return l.ReleaseProjectDevice(in)
}

// DeleteDisabledProjectDevice 删除无效（设备中心不存在）的项目设备
func (s *ProjectDeviceServiceServer) DeleteDisabledProjectDevice(ctx context.Context, in *pb.DeleteDisabledProjectDeviceReq) (*pb.DeleteDisabledProjectDeviceResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := projectdeviceservicelogic.NewDeleteDisabledProjectDeviceLogic(ctx, s.svcCtx)

	return l.DeleteDisabledProjectDevice(in)
}
