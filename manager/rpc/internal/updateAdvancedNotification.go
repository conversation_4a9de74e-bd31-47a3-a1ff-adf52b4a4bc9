package internal

import (
	"context"
	"sort"
	"time"

	"github.com/gorhill/cronexpr"
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/mr"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/caller"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworkerv2/base"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/redislock"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	commonpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
	dispatcherpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/rpc/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	apiexecutionservicelogic "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/logic/apiexecutionservice"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type UpdateAdvancedNotificationHandler struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func newUpdateAdvancedNotificationHandler(svcCtx *svc.ServiceContext) *UpdateAdvancedNotificationHandler {
	ctx := context.Background()
	return &UpdateAdvancedNotificationHandler{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (h *UpdateAdvancedNotificationHandler) Update() error {
	var cancel context.CancelFunc
	h.ctx, cancel = context.WithTimeout(context.Background(), common.ConstExpireOfAdvancedNotificationTask)
	defer cancel()

	fn := func() error {
		now := time.Now()

		items, err := h.svcCtx.PerfPlanV2Model.FindNoCacheByQuery(
			h.ctx,
			h.svcCtx.PerfPlanV2Model.SelectBuilder().
				Where("`type` = ? and `advanced_notification` = ?", commonpb.TriggerMode_SCHEDULE.String(), 1),
		)
		if err != nil {
			return err
		}

		return mr.MapReduceVoid[*model.PerfPlanV2, any](
			func(source chan<- *model.PerfPlanV2) {
				for _, item := range items {
					source <- item
				}
			},
			func(item *model.PerfPlanV2, writer mr.Writer[any], cancel func(error)) {
				projectID := item.ProjectId
				planID := item.PlanId

				if !item.CronExpression.Valid {
					h.Errorf(
						"the cron expression is invalid even if advanced_notification is enabled, project_id: %s, plan_id: %s",
						projectID, planID,
					)
					return
				}

				cronSpec := item.CronExpression.String
				schedule, err := cronexpr.Parse(cronSpec)
				if err != nil {
					h.Errorf(
						"failed to parse the cron expression, cron: %s, project_id: %s, plan_id: %s, error: %+v",
						cronSpec, projectID, planID, err,
					)
					return
				}

				due := schedule.Next(now)
				sub := time.Date(due.Year(), due.Month(), due.Day(), 0, 0, 0, 0, due.Location()).
					Sub(time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location()))

				// Friday also needs to notify saturday and sunday besides next weekday(monday)
				if now.Weekday() == time.Friday {
					if sub < common.ConstAdvancedNotificationDays*24*time.Hour || sub > (common.ConstAdvancedNotificationDays+2)*24*time.Hour {
						h.Debugf(
							"the next cron time[%s] is not expected, cron: %s, project_id: %s, plan_id: %s",
							due, cronSpec, projectID, planID,
						)
						return
					}
				} else {
					// Others just need to notify next weekday
					if sub != common.ConstAdvancedNotificationDays*24*time.Hour {
						h.Debugf(
							"the next cron time[%s] is not expected, cron: %s, project_id: %s, plan_id: %s",
							due, cronSpec, projectID, planID,
						)
						return
					}
				}

				data, err := apiexecutionservicelogic.NewGetApiExecutionDataLogic(h.ctx, h.svcCtx).
					GetApiExecutionData(
						&pb.GetApiExecutionDataReq{
							ProjectId: projectID,
							Id:        planID,
							Type:      pb.ApiExecutionDataType_PERF_PLAN,
						},
					)
				if err != nil {
					h.Errorf(
						"failed to get the api execution data, project_id: %s, plan_id: %s, error: %+v",
						projectID, planID, err,
					)
					return
				}

				cases := generatePerfReportCallbackCases(data)
				task := base.NewTask(
					constants.MQTaskTypeDispatcherSendPerfNotification,
					protobuf.MarshalJSONIgnoreError(
						&dispatcherpb.PerfReportCallback{
							ProjectId: projectID,
							PlanId:    planID,
							Stage:     dispatcherpb.StageType_ST_ADVANCED,
							Cases:     cases,
							Services:  data.GetPerfPlan().GetMetaData().Services,
						},
					),
					base.WithMaxRetryOptions(0),
					base.WithRetentionOptions(5*time.Minute),
				)

				_, err = h.svcCtx.DispatcherProducer.Send(h.ctx, task, base.QueuePriorityDefault)
				if err != nil {
					h.Errorf(
						"failed to send the perf advanced notification, queue: %s, payload: %s, error: %+v",
						task.Queue, task.Payload, err,
					)
					return
				} else {
					h.Infof(
						"send the perf advanced notification successfully, project_id: %s, plan_id: %s",
						projectID, planID,
					)
				}
			},
			func(pipe <-chan any, cancel func(error)) {
			},
			mr.WithContext(h.ctx),
		)
	}

	key := common.ConstLockUpdateAdvancedNotification
	if err := caller.LockWithOptionDo(
		h.svcCtx.Redis, key, fn, redislock.WithExpire(common.ConstExpireOfAdvancedNotificationTask),
	); err != nil {
		if re, ok := errorx.RootError(err); !ok || re.Code() != errorx.AcquireRedisLockFailure {
			h.Errorf("failed to acquire the redis lock, key: %s, error: %+v", key, err)
			return err
		}
		h.Infof("another service is executing the task of update advanced notification, key: %s", key)
	} else {
		h.Infof("finished to update advanced notification, key: %s", key)
	}

	return nil
}

func generatePerfReportCallbackCases(data *pb.ApiExecutionData) []*dispatcherpb.PerfReportCallback_PerfCase {
	cases := make([]*dispatcherpb.PerfReportCallback_PerfCase, 0, constants.ConstDefaultMakeSliceSize)
	if len(data.GetChildren()) > 0 {
		for _, item1 := range data.Children[0].GetChild() {
			suite := item1.GetPerfSuite()
			if suite == nil {
				continue
			}
			if len(item1.GetChildren()) == 0 {
				continue
			}

			for _, item2 := range item1.Children[0].GetChild() {
				case_ := item2.GetPerfCase()
				if case_ == nil {
					continue
				}
				if case_.GetState() != pb.CommonState_CS_ENABLE {
					continue
				}

				cmds := make([]uint32, 0, len(case_.GetSerialSteps())+len(case_.GetParallelSteps()))
				for _, steps := range [][]*commonpb.PerfCaseStepV2{
					case_.GetSerialSteps(),
					case_.GetParallelSteps(),
				} {
					for _, step := range steps {
						if cmd := step.GetCmd(); cmd != 0 {
							cmds = append(cmds, cmd)
						}
					}
				}
				sort.Slice(
					cmds, func(i, j int) bool {
						return cmds[i] < cmds[j]
					},
				)

				cases = append(
					cases, &dispatcherpb.PerfReportCallback_PerfCase{
						SuiteName: suite.GetName(),
						CaseName:  case_.GetName(),
						TargetRps: case_.GetTargetRps(),
						Cmds:      cmds,
					},
				)
			}
		}
	}
	return cases
}
