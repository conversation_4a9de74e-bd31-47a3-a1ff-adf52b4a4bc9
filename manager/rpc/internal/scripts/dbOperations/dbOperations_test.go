package dbOperations

import (
	"context"
	"database/sql"
	"flag"
	"fmt"
	"net/url"
	"path"
	"reflect"
	"strconv"
	"strings"
	"testing"
	"time"

	_ "github.com/go-sql-driver/mysql"
	"github.com/redis/go-redis/v9"
	"github.com/zeromicro/go-zero/core/jsonx"
)

const (
	schemeOfMySQL = "mysql"
	schemeOfRedis = "redis"

	defaultTimeout = 10 * time.Minute
)

var (
	dataSource = flag.String("data_source", "", "data source")
	command    = flag.String("command", "", "command")
	timeout    = flag.String("timeout", "", "timeout")

	timeoutDuration = defaultTimeout
)

func TestMain(m *testing.M) {
	if !flag.Parsed() {
		flag.Parse()
	}

	m.Run()
}

func TestDBOperations(t *testing.T) {
	if *dataSource == "" {
		t.Fatal("not set the `data_source`")
	}
	if *command == "" {
		t.Fatal("not set the `sql`")
	}
	if *timeout != "" {
		if d, err := time.ParseDuration(*timeout); err == nil {
			timeoutDuration = d
		}
	}

	u, err := url.Parse(*dataSource)
	if err != nil {
		t.Fatalf("parse data source error: %v", err)
	}

	ctx, cancel := context.WithTimeout(context.Background(), timeoutDuration)
	defer cancel()

	if strings.EqualFold(u.Scheme, schemeOfRedis) {
		err = handleCommandOfRedis(t, ctx, u)
	} else {
		err = handleCommandOfMySQL(t, ctx, u)
	}
	if err != nil {
		t.Fatalf("handle command error: %v", err)
	}
}

func handleCommandOfMySQL(t *testing.T, ctx context.Context, u *url.URL) error {
	if u == nil {
		return nil
	}

	dsn := getMySQLDSNFromURL(u)
	db, err := sql.Open(schemeOfMySQL, dsn)
	if err != nil {
		return err
	}
	defer func() {
		_ = db.Close()
	}()

	db.SetMaxIdleConns(64)
	db.SetMaxOpenConns(64)
	db.SetConnMaxLifetime(time.Minute)

	if err = db.Ping(); err != nil {
		return err
	}

	if strings.HasPrefix(*command, "select") || strings.HasSuffix(*command, "SELECT") {
		rows, err := db.QueryContext(ctx, *command)
		if err != nil {
			return fmt.Errorf("query sql error: %v", err)
		}
		defer func() {
			_ = rows.Close()
		}()

		columns, err := rows.Columns()
		if err != nil {
			return fmt.Errorf("get columns error: %v", err)
		}

		tt, err := rows.ColumnTypes()
		if err != nil {
			return fmt.Errorf("get column types error: %v", err)
		}

		colTypes := make([]string, len(tt))
		types := make([]reflect.Type, len(tt))
		for i, tp := range tt {
			st := tp.ScanType()
			if st == nil {
				t.Errorf("scantype is null for column %q", tp.Name())
				continue
			}
			colTypes[i] = st.Name()
			types[i] = st
		}
		values := make([]any, len(tt))
		for i := range values {
			values[i] = reflect.New(types[i]).Interface()
		}

		t.Logf("columns: %s", strings.Join(columns, ", "))
		t.Logf("column types: %s", strings.Join(colTypes, ", "))
		for rows.Next() {
			err = rows.Scan(values...)
			if err != nil {
				return fmt.Errorf("scan values error: %v", err)
			}

			t.Logf("values: %s", jsonx.MarshalIgnoreError(values))
		}
	} else {
		result, err := db.ExecContext(ctx, *command)
		if err != nil {
			return fmt.Errorf("exec sql error: %v", err)
		}

		lastInsertId, _ := result.LastInsertId()
		rowsAffected, _ := result.RowsAffected()
		t.Logf("result: %d, %d", lastInsertId, rowsAffected)
	}

	return nil
}

func getMySQLDSNFromURL(u *url.URL) string {
	username := u.User.Username()
	password, _ := u.User.Password()
	hostname := u.Hostname()
	port := u.Port()
	dbname := path.Base(u.Path)
	params := u.Query()

	return fmt.Sprintf("%s:%s@tcp(%s:%s)/%s?%s", username, password, hostname, port, dbname, params.Encode())
}

func handleCommandOfRedis(t *testing.T, ctx context.Context, u *url.URL) error {
	if u == nil {
		return nil
	}

	options := getRedisOptionsFromURL(u)
	c := redis.NewClient(options)
	defer func() {
		_ = c.Close()
	}()

	_, err := c.Ping(ctx).Result()
	if err != nil {
		return err
	}

	commands := strings.Split(*command, " ")
	args := make([]any, len(commands))
	for i, cmd := range commands {
		args[i] = cmd
	}
	result, err := c.Do(ctx, args...).Result()
	if err != nil {
		return err
	}

	t.Logf("result: %v", result)
	return nil
}

func getRedisOptionsFromURL(u *url.URL) *redis.Options {
	username := u.User.Username()
	password, _ := u.User.Password()
	hostname := u.Hostname()
	port := u.Port()
	dbname := path.Base(u.Path)

	db, err := strconv.Atoi(dbname)
	if err != nil {
		db = 0
	}

	return &redis.Options{
		Addr:         fmt.Sprintf("%s:%s", hostname, port),
		Username:     username,
		Password:     password,
		DB:           db,
		MaxRetries:   3,
		MinIdleConns: 8,
	}
}
