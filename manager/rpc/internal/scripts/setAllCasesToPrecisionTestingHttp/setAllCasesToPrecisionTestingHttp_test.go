package setAllCasesToPrecisionTestingHttp

import (
	"context"
	"flag"
	"sync"
	"testing"

	"github.com/zeromicro/go-zero/core/conf"
	"github.com/zeromicro/go-zero/core/mr"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"google.golang.org/protobuf/types/known/structpb"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/config"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
	qetconstants "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
)

var (
	projectID = flag.String("project_id", "", "project id")
	cgmap     sync.Map
)

func TestMain(m *testing.M) {
	if !flag.Parsed() {
		flag.Parse()
	}

	m.Run()
}

func TestSetAllCasesToPrecisionTestingHttp(t *testing.T) {
	if *projectID == "" {
		t.Fatal("please set quality platform project_id")
	}

	var c config.Config
	// conf.MustLoad("../../../etc/manager.yaml", &c)
	conf.MustLoad("./rpc/etc/manager.yaml", &c)

	ctx := context.Background()
	sqlConn := sqlx.NewMysql(c.DB.DataSource)
	apiCaseModel := model.NewApiCaseModel(sqlConn, c.Cache)
	apiCaseElementModel := model.NewApiCaseElementModel(sqlConn, c.Cache)
	interfaceCaseModel := model.NewInterfaceCaseModel(sqlConn, c.Cache)
	interfaceCaseElementModel := model.NewInterfaceCaseElementModel(sqlConn, c.Cache)
	componentGroupModel := model.NewComponentGroupModel(sqlConn, c.Cache)
	componentGroupElementModel := model.NewComponentGroupElementModel(sqlConn, c.Cache)
	componentModel := model.NewComponentModel(sqlConn, c.Cache)

	if err := updateApiCaseHttpComponent(
		t, ctx, apiCaseModel, componentModel, apiCaseElementModel,
	); err != nil {
		t.Fatal(err)
	}

	if err := updateInterfaceCaseHttpComponent(
		t, ctx, interfaceCaseModel, componentModel, interfaceCaseElementModel,
	); err != nil {
		t.Fatal(err)
	}

	cgmap.Range(func(key, value interface{}) bool {
		t.Logf("%v: %v\n", key, value)
		return true
	})

	if err := updateComponentGroupHttpComponent(
		t, ctx, componentGroupModel, componentModel, componentGroupElementModel,
	); err != nil {
		t.Fatal(err)
	}

	cgmap.Range(func(key, value interface{}) bool {
		t.Logf("%v: %v\n", key, value)
		return true
	})

	t.Log("Update all http components successful!!!")
}

func updateComponentGroupHttpComponent(
	t *testing.T,
	ctx context.Context,
	componentGroupModel model.ComponentGroupModel,
	componentModel model.ComponentModel,
	componentGroupElementModel model.ComponentGroupElementModel,
) error {
	for {
		hasCg := false
		err := mr.MapReduceVoid[string, any](
			func(source chan<- string) {
				cgmap.Range(func(key, value interface{}) bool {
					if value.(bool) {
						hasCg = true
						source <- key.(string)
					}
					return true
				})
			},
			func(item string, writer mr.Writer[any], cancel func(error)) {
				componentGroup, err := componentGroupModel.FindLatestOneNoCache(ctx, *projectID, item)
				if err != nil {
					t.Error(err)
					return
				}

				realtions, reference_relations, hasHttp, err := updateRelations(
					componentGroup.Structure, componentGroup.ReferenceStructure,
				)
				if err != nil {
					t.Error(err)
					return
				}

				if hasHttp {
					componentGroup.Structure = realtions
					componentGroup.ReferenceStructure = reference_relations
					components, err := componentModel.FindNoCacheByQuery(
						ctx,
						componentModel.SelectBuilder().Where(
							"`project_id` = ? AND `parent_id` = ? AND `parent_type` = ? AND `parent_version` = ? AND `component_type` = ?",
							*projectID, componentGroup.ComponentGroupId, "COMPONENT_GROUP", componentGroup.Version, "HTTP",
						),
					)
					if err != nil {
						t.Error(err)
						return
					}

					err = componentModel.Trans(
						ctx, func(ctx context.Context, session sqlx.Session) error {
							if _, err := componentGroupModel.Update(ctx, session, componentGroup); err != nil {
								return err
							}

							for _, component := range components {
								component.ComponentType = "PRECISION_TESTING_HTTP"
								if component.Data.Valid {
									data := &structpb.Struct{}
									err = protobuf.UnmarshalJSONFromString(component.Data.String, data)
									if err != nil {
										return err
									}
									data.Fields["clazz"] = structpb.NewStringValue("PRECISION_TESTING_HTTP")
									component.Data.String = protobuf.MarshalJSONToStringIgnoreError(data)
								}
								_, err := componentModel.Update(ctx, session, component)
								if err != nil {
									return err
								}

								element, err := componentGroupElementModel.FindOneByProjectIdComponentGroupIdVersionElementId(
									ctx, *projectID, componentGroup.ComponentGroupId, componentGroup.Version, component.ComponentId,
								)
								if err != nil {
									return err
								}
								data := &pb.Node{}
								err = protobuf.UnmarshalJSONFromString(element.Data, data)
								if err != nil {
									return err
								}
								data.Type = "PRECISION_TESTING_HTTP"
								data.GetData().Fields["clazz"] = structpb.NewStringValue("PRECISION_TESTING_HTTP")
								element.Data = protobuf.MarshalJSONToStringIgnoreError(data)
								_, err = componentGroupElementModel.Update(ctx, session, element)
								if err != nil {
									return err
								}
							}
							return nil
						},
					)
					if err != nil {
						t.Error(err)
						return
					}
				}
				cgmap.Store(item, false)
			},
			func(pipe <-chan any, cancel func(error)) {
			},
			mr.WithContext(ctx),
		)
		if err != nil {
			return err
		}
		if !hasCg {
			break
		}
	}
	return nil
}

func updateInterfaceCaseHttpComponent(
	t *testing.T,
	ctx context.Context,
	interfaceCaseModel model.InterfaceCaseModel,
	componentModel model.ComponentModel,
	interfaceCaseElementModel model.InterfaceCaseElementModel,
) error {
	interfaceCases, err := interfaceCaseModel.FindNoCacheByQuery(
		ctx,
		interfaceCaseModel.SelectBuilder().
			Where(
				"`project_id` = ?  AND `latest` = ? AND `document_id` <> ''",
				*projectID, qetconstants.IsLatestVersion,
			),
	)
	if err != nil {
		return err
	}

	err = mr.MapReduceVoid[*model.InterfaceCase, any](
		func(source chan<- *model.InterfaceCase) {
			for _, interfaceCase := range interfaceCases {
				source <- interfaceCase
			}
		},
		func(interfaceCase *model.InterfaceCase, writer mr.Writer[any], cancel func(error)) {
			realtions, _, hasHttp, err := updateRelations(
				interfaceCase.Structure, "",
			)
			if err != nil {
				t.Error(err)
				return
			}
			t.Logf("name: %s hasHttp: %v realtions: %s\n", interfaceCase.CaseId, hasHttp, realtions)

			if hasHttp {
				interfaceCase.Structure = realtions
				components, err := componentModel.FindNoCacheByQuery(
					ctx,
					componentModel.SelectBuilder().Where(
						"`project_id` = ? AND `parent_id` = ? AND `parent_type` = ? AND `parent_version` = ? AND `component_type` = ?",
						*projectID, interfaceCase.CaseId, "INTERFACE_CASE", interfaceCase.Version, "HTTP",
					),
				)
				if err != nil {
					t.Error(err)
					return
				}

				err = interfaceCaseModel.Trans(
					ctx, func(ctx context.Context, session sqlx.Session) error {
						if _, err := interfaceCaseModel.Update(ctx, session, interfaceCase); err != nil {
							return err
						}

						for _, component := range components {
							component.ComponentType = "PRECISION_TESTING_HTTP"
							if component.Data.Valid {
								data := &structpb.Struct{}
								err = protobuf.UnmarshalJSONFromString(component.Data.String, data)
								if err != nil {
									return err
								}
								data.Fields["clazz"] = structpb.NewStringValue("PRECISION_TESTING_HTTP")
								component.Data.String = protobuf.MarshalJSONToStringIgnoreError(data)
							}
							_, err := componentModel.Update(ctx, session, component)
							if err != nil {
								return err
							}

							element, err := interfaceCaseElementModel.FindOneByProjectIdCaseIdVersionElementId(
								ctx, *projectID, interfaceCase.CaseId, interfaceCase.Version, component.ComponentId,
							)
							if err != nil {
								return err
							}
							data := &pb.Node{}
							err = protobuf.UnmarshalJSONFromString(element.Data, data)
							if err != nil {
								return err
							}
							data.Type = "PRECISION_TESTING_HTTP"
							data.GetData().Fields["clazz"] = structpb.NewStringValue("PRECISION_TESTING_HTTP")
							element.Data = protobuf.MarshalJSONToStringIgnoreError(data)
							_, err = interfaceCaseElementModel.Update(ctx, session, element)
							if err != nil {
								return err
							}
						}
						return nil
					},
				)
				if err != nil {
					t.Error(err)
					return
				}
			}
		},
		func(pipe <-chan any, cancel func(error)) {
		},
		mr.WithContext(ctx),
	)
	if err != nil {
		return err
	}
	return nil
}

func updateApiCaseHttpComponent(
	t *testing.T,
	ctx context.Context,
	apiCaseModel model.ApiCaseModel,
	componentModel model.ComponentModel,
	apiCaseElementModel model.ApiCaseElementModel,
) error {
	apiCases, err := apiCaseModel.FindNoCacheByQuery(
		ctx,
		apiCaseModel.SelectBuilder().
			Where(
				"`project_id` = ? AND `latest` = ? AND `category_id` <> ''",
				*projectID, qetconstants.IsLatestVersion,
			),
	)
	if err != nil {
		return err
	}

	err = mr.MapReduceVoid[*model.ApiCase, any](
		func(source chan<- *model.ApiCase) {
			for _, apiCase := range apiCases {
				source <- apiCase
			}
		},
		func(apiCase *model.ApiCase, writer mr.Writer[any], cancel func(error)) {
			realtions, _, hasHttp, err := updateRelations(
				apiCase.Structure, "",
			)
			if err != nil {
				t.Error(err)
				return
			}
			t.Logf("name: %s hasHttp: %v realtions: %s\n", apiCase.CaseId, hasHttp, realtions)

			if hasHttp {
				apiCase.Structure = realtions
				components, err := componentModel.FindNoCacheByQuery(
					ctx,
					componentModel.SelectBuilder().Where(
						"`project_id` = ? AND `parent_id` = ? AND `parent_type` = ? AND `parent_version` = ? AND `component_type` = ?",
						*projectID, apiCase.CaseId, "API_CASE", apiCase.Version, "HTTP",
					),
				)
				if err != nil {
					t.Error(err)
					return
				}

				err = apiCaseModel.Trans(
					ctx, func(ctx context.Context, session sqlx.Session) error {
						if _, err := apiCaseModel.Update(ctx, session, apiCase); err != nil {
							return err
						}

						for _, component := range components {
							component.ComponentType = "PRECISION_TESTING_HTTP"
							if component.Data.Valid {
								data := &structpb.Struct{}
								err = protobuf.UnmarshalJSONFromString(component.Data.String, data)
								if err != nil {
									return err
								}
								data.Fields["clazz"] = structpb.NewStringValue("PRECISION_TESTING_HTTP")
								component.Data.String = protobuf.MarshalJSONToStringIgnoreError(data)
							}
							_, err := componentModel.Update(ctx, session, component)
							if err != nil {
								return err
							}

							element, err := apiCaseElementModel.FindOneByProjectIdCaseIdVersionElementId(
								ctx, *projectID, apiCase.CaseId, apiCase.Version, component.ComponentId,
							)
							if err != nil {
								return err
							}
							data := &pb.Node{}
							err = protobuf.UnmarshalJSONFromString(element.Data, data)
							if err != nil {
								return err
							}
							data.Type = "PRECISION_TESTING_HTTP"
							data.GetData().Fields["clazz"] = structpb.NewStringValue("PRECISION_TESTING_HTTP")
							element.Data = protobuf.MarshalJSONToStringIgnoreError(data)
							_, err = apiCaseElementModel.Update(ctx, session, element)
							if err != nil {
								return err
							}
						}
						return nil
					},
				)
				if err != nil {
					t.Error(err)
					return
				}
			}
		},
		func(pipe <-chan any, cancel func(error)) {
		},
		mr.WithContext(ctx),
	)
	if err != nil {
		return err
	}
	return nil
}

func updateRelationChildren(children *structpb.ListValue, hasHttp *bool) {
	for _, childs := range children.GetValues() {
		for _, child := range childs.GetListValue().GetValues() {
			v := child.GetStructValue().GetFields()
			switch v["type"].GetStringValue() {
			case "HTTP":
				*hasHttp = true
				v["type"] = structpb.NewStringValue("PRECISION_TESTING_HTTP")
			case "COMPONENT_GROUP":
				reference_id := v["reference_id"].GetStringValue()
				if len(reference_id) > 0 {
					if _, ok := cgmap.Load(reference_id); !ok {
						cgmap.Store(reference_id, true)
					}
				}
			}
			updateRelationChildren(v["children"].GetListValue(), hasHttp)
		}
	}
}

func updateRelation(relation *pb.Relation, hasHttp *bool) {
	switch relation.Type {
	case "HTTP":
		*hasHttp = true
		relation.Type = "PRECISION_TESTING_HTTP"
	case "COMPONENT_GROUP":
		reference_id := relation.GetReferenceId()
		if len(reference_id) > 0 {
			if _, ok := cgmap.Load(reference_id); !ok {
				cgmap.Store(reference_id, true)
			}
		}

	}

	for _, childs := range relation.GetChildren() {
		for _, child := range childs.GetValues() {
			v := child.GetStructValue().GetFields()
			switch v["type"].GetStringValue() {
			case "HTTP":
				*hasHttp = true
				v["type"] = structpb.NewStringValue("PRECISION_TESTING_HTTP")
			case "COMPONENT_GROUP":
				reference_id := v["reference_id"].GetStringValue()
				if len(reference_id) > 0 {
					if _, ok := cgmap.Load(reference_id); !ok {
						cgmap.Store(reference_id, true)
					}
				}
			}
			updateRelationChildren(v["children"].GetListValue(), hasHttp)
		}
	}
}

func updateRelations(
	structure, reference_structure string,
) (string, string, bool, error) {
	hasHttp := &[]bool{false}[0]

	relations := make([]*pb.Relation, 0)
	err := protobuf.UnmarshalJSONWithMessagesFromString(structure, &relations)
	if err != nil {
		return "", "", false, err
	}
	for _, relation := range relations {
		updateRelation(relation, hasHttp)
	}

	var reference_relation_structure string
	if len(reference_structure) > 0 {
		reference_relation := &pb.Relation{}
		err := protobuf.UnmarshalJSONFromString(reference_structure, reference_relation)
		if err != nil {
			return "", "", false, err
		}
		updateRelation(reference_relation, hasHttp)

		if *hasHttp {
			reference_relation_structure = protobuf.MarshalJSONToStringIgnoreError(reference_relation)
		}
	}

	if *hasHttp {
		return protobuf.MarshalJSONWithMessagesToStringIgnoreError(relations),
			reference_relation_structure, true, nil
	}
	return "", "", false, nil
}
