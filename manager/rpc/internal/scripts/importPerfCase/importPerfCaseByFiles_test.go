package importPerfCase

import (
	"context"
	"flag"
	"fmt"
	"io/fs"
	"os"
	"path/filepath"
	"strings"
	"testing"
	"time"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/conf"
	"github.com/zeromicro/go-zero/core/lang"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/userinfo"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/cache"
	commonpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/utils"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/config"
	categoryservicelogic "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/logic/categoryservice"
	perfcasev2servicelogic "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/logic/perfcasev2service"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

var (
	projectID      = flag.String("project_id", "", "project id")
	rootCategoryID = flag.String("root_category_id", "", "root category id")
	filePath       = flag.String("file_path", "", "file path")
)

func TestMain(m *testing.M) {
	if !flag.Parsed() {
		flag.Parse()
	}

	m.Run()
}

func TestImportPerfCaseByFiles(t *testing.T) {
	if *projectID == "" {
		t.Fatal("not set the `project_id`")
	}
	if *rootCategoryID == "" {
		t.Fatal("not set the `root_category_id`")
	}
	if *filePath == "" {
		t.Fatal("not set the `file_path`")
	}

	var c config.Config
	conf.MustLoad("./rpc/etc/manager.yaml", &c)

	user := &userinfo.UserInfo{
		Account:  "T1704",
		Fullname: "韩子健",
		DeptName: "平台研发组",
		Email:    "<EMAIL>",
	}
	ctx := userinfo.WithContext(context.Background(), user)
	svcCtx := svc.NewServiceContext(c)
	createCategoryLogic := categoryservicelogic.NewCreateCategoryLogic(ctx, svcCtx)
	createPerfCaseLogic := perfcasev2servicelogic.NewCreatePerfCaseV2Logic(ctx, svcCtx)
	modifyPerfCaseLogic := perfcasev2servicelogic.NewModifyPerfCaseV2Logic(ctx, svcCtx)

	*filePath, _ = filepath.Abs(*filePath)
	categoryIDCache := make(map[string]string, 64)
	caseNameCache := make(map[string]lang.PlaceholderType, 512)
	if err := filepath.Walk(
		*filePath, func(path string, info fs.FileInfo, err error) error {
			if err != nil {
				return err
			}

			if info.IsDir() {
				parentCategoryID := *rootCategoryID
				if *filePath != path {
					if v, ok := categoryIDCache[filepath.Dir(path)]; ok {
						parentCategoryID = v
					}
				}

				category, err1 := svcCtx.CategoryModel.FindOneByProjectIdTypeName(
					ctx, *projectID, "PERF_CASE", info.Name(),
				)
				if err1 != nil {
					// 创建分类树目录
					out, err1 := createCategoryLogic.CreateCategory(
						&pb.CreateCategoryReq{
							ProjectId:   *projectID,
							Type:        "PERF_CASE",
							Name:        info.Name(),
							Description: "通过脚本创建的分类树目录：" + info.Name(),
							ParentId:    parentCategoryID,
						},
					)
					if err1 != nil {
						t.Errorf("failed to create category, name: %s, error: %+v", info.Name(), err1)
						return filepath.SkipDir
					}

					categoryIDCache[path] = out.GetCategory().GetCategoryId()
					t.Logf("create category: %s, %s", parentCategoryID, out.GetCategory().GetName())
				} else {
					// 分类树目录已存在
					categoryIDCache[path] = category.CategoryId
					t.Logf("exists category: %s, %s", category.CategoryId, category.Name)
				}
			} else {
				if strings.HasPrefix(info.Name(), ".") || !strings.HasSuffix(info.Name(), ".yaml") {
					return nil
				}

				parentCategoryID, ok := categoryIDCache[filepath.Dir(path)]
				if !ok {
					t.Errorf("cannot found the category id by parent path name, file: %s", path)
					return nil
				}

				// 创建压测用例
				file, err1 := os.Open(path)
				if err1 != nil {
					t.Errorf("failed to open the file, file: %s, error: %+v", path, err1)
					return nil
				}
				defer file.Close()

				content, err1 := utils.GetPerfCaseFromReader(file)
				if err1 != nil {
					t.Errorf("failed to get perf case content from file, file: %s, error: %+v", path, err1)
					return nil
				}

				for _, req := range convertPerfCaseContent(*projectID, parentCategoryID, content, user) {
					if _, ok := caseNameCache[req.GetName()]; ok {
						t.Logf("the perf case has been created, file: %s, name: %s", path, req.GetName())
						continue
					}

					perfCase, err1 := findPerfCaseByName(ctx, svcCtx.PerfCaseV2Model, *projectID, req.GetName())
					if err1 != nil {
						if !errors.Is(err1, model.ErrNotFound) {
							t.Errorf(
								"failed to find the perf case, file: %s, name: %s, error: %+v",
								path, req.GetName(), err1,
							)
						}

						out, err1 := createPerfCaseLogic.CreatePerfCaseV2(req)
						if err1 != nil {
							t.Errorf(
								"failed to create perf case, file: %s, name: %s, error: %+v", path, req.GetName(), err1,
							)
							continue
						}

						t.Logf("create perf case: %s, %s", out.GetCase().GetCaseId(), out.GetCase().GetName())
					} else {
						out, err1 := modifyPerfCaseLogic.ModifyPerfCaseV2(
							&pb.ModifyPerfCaseV2Req{
								ProjectId:     req.GetProjectId(),
								CategoryId:    req.GetCategoryId(),
								CaseId:        perfCase.CaseId,
								Name:          req.GetName(),
								Description:   req.GetDescription(),
								Tags:          req.GetTags(),
								Protocol:      req.GetProtocol(),
								RateLimits:    req.GetRateLimits(),
								SetupSteps:    req.GetSetupSteps(),
								SerialSteps:   req.GetSerialSteps(),
								ParallelSteps: req.GetParallelSteps(),
								TeardownSteps: req.GetTeardownSteps(),
								MaintainedBy:  req.GetMaintainedBy(),
							},
						)
						if err1 != nil {
							t.Errorf(
								"failed to modify perf case, file: %s, name: %s, error: %+v", path, perfCase.Name, err1,
							)
						}

						t.Logf("modify perf case: %s, %s", out.GetCase().GetCaseId(), out.GetCase().GetName())
					}

					caseNameCache[req.GetName()] = lang.Placeholder
				}
			}

			return nil
		},
	); err != nil {
		t.Fatal(err)
	}
}

func findPerfCaseByName(
	ctx context.Context, perfCaseModel model.PerfCaseV2Model, projectID, name string,
) (*model.PerfCaseV2, error) {
	r, err := perfCaseModel.FindNoCacheByQuery(
		ctx, perfCaseModel.SelectBuilder().Where(
			"`project_id` = ? AND `name` = ?", projectID, name,
		).Limit(1),
	)
	if err != nil {
		return nil, err
	} else if len(r) == 0 {
		return nil, model.ErrNotFound
	}
	return r[0], nil
}

func convertRateLimit(rateLimit *commonpb.RateLimit) []*commonpb.RateLimitV2 {
	steps := (rateLimit.GetTargetRps() - rateLimit.GetInitialRps() + rateLimit.GetStepHeight() - 1) / rateLimit.GetStepHeight()
	stepDuration := cache.Parse(rateLimit.GetStepDuration())
	duration := (time.Duration(steps)*stepDuration + time.Second - 1) / time.Second

	return []*commonpb.RateLimitV2{
		{
			TargetRps:      rateLimit.GetTargetRps(),
			InitialRps:     rateLimit.GetInitialRps(),
			ChangeDuration: fmt.Sprintf("%ds", duration),
			TargetDuration: "5m",
		},
	}
}

func convertPerfCaseSteps(steps []*commonpb.PerfCaseStep) []*commonpb.PerfCaseStepV2 {
	out := make([]*commonpb.PerfCaseStepV2, 0, len(steps))
	for _, step := range steps {
		exports := make([]*commonpb.PerfCaseStepV2_Export, 0, len(step.GetExports()))
		for _, export := range step.GetExports() {
			exports = append(
				exports, &commonpb.PerfCaseStepV2_Export{
					Name:       export.GetName(),
					Expression: export.GetExpression(),
				},
			)
		}

		sleep := step.GetSleep()
		if sleep == "" {
			sleep = "0s"
		}

		out = append(
			out, &commonpb.PerfCaseStepV2{
				Name:       step.GetName(),
				RateLimits: convertRateLimit(step.GetRateLimit()),
				Url:        step.GetUrl(),
				Method:     step.GetMethod(),
				Headers:    step.GetHeaders(),
				Body:       step.GetBody(),
				Exports:    exports,
				Sleep:      sleep,
			},
		)
	}

	return out
}

func convertPerfCaseContent(
	projectID, categoryID string, content *commonpb.PerfCaseContent, user *userinfo.UserInfo,
) []*pb.CreatePerfCaseV2Req {
	var (
		serialSteps   = content.GetSerialSteps()
		parallelSteps = content.GetParallelSteps()
		setupSteps    = convertPerfCaseSteps(content.GetSetupSteps())
		teardownSteps = convertPerfCaseSteps(content.GetTeardownSteps())

		out = make([]*pb.CreatePerfCaseV2Req, 0, len(serialSteps)+len(parallelSteps))
	)

	for _, item := range []struct {
		stepType commonpb.PerfCaseStepType
		steps    []*commonpb.PerfCaseStep
	}{
		{
			stepType: commonpb.PerfCaseStepType_PerfCaseStepType_SERIAL,
			steps:    serialSteps,
		},
		{
			stepType: commonpb.PerfCaseStepType_PerfCaseStepType_PARALLEL,
			steps:    parallelSteps,
		},
	} {
		for _, step := range item.steps {
			req := &pb.CreatePerfCaseV2Req{
				ProjectId:     projectID,
				CategoryId:    categoryID,
				Name:          step.GetName(),
				Description:   "通过脚本创建的压测用例：" + step.GetName(),
				Tags:          nil,
				Protocol:      commonpb.Protocol_PROTOCOL_TT,
				RateLimits:    convertRateLimit(step.GetRateLimit()),
				SetupSteps:    setupSteps,
				SerialSteps:   nil,
				ParallelSteps: nil,
				TeardownSteps: teardownSteps,
				MaintainedBy:  user.Account,
			}

			if item.stepType == commonpb.PerfCaseStepType_PerfCaseStepType_SERIAL {
				req.SerialSteps = convertPerfCaseSteps([]*commonpb.PerfCaseStep{step})
			} else if item.stepType == commonpb.PerfCaseStepType_PerfCaseStepType_PARALLEL {
				req.ParallelSteps = convertPerfCaseSteps([]*commonpb.PerfCaseStep{step})
			}

			out = append(out, req)
		}
	}

	return out
}
