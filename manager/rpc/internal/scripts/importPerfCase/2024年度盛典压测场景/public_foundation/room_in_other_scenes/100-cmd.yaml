#  房间内其他场景。
SetupSteps:
  - Name:           进入房间-50858
    RateLimit:
      TargetRPS:    100
      InitialRPS:   0
      StepHeight:   50
      StepDuration: 1s
    Method:         ga.api.channel_core.ChannelCoreLogic.ChannelEnterV2
    Body:           '
{
    "channel_id": 185563011
}
'

SerialSteps:
  - Name:           50028 - 是否展示进房管理入口和申请
    RateLimit:
      TargetRPS:    100
      InitialRPS:   0
      StepHeight:   10
      StepDuration: 1s
    Method:         ga.api.melee_channel.MeleeChannelLogic.EnterRoomWhitelistConfig
    Body:           '
{
    "base_req": {},
    "cid": 185563011,
    "tab_id": 1
}
'

  - Name:           3004 - 拉取发布弹窗官方推荐房间名
    RateLimit:
      TargetRPS:    100
      InitialRPS:   0
      StepHeight:   10
      StepDuration: 1s
    Method:         ga.api.topic_channel.TopicChannelLogic.GetTopicChannelRoomName
    Body:           '
{
    "base_req": {},
    "tab_id": [1]
}
'

  - Name:           3056 - 点击切换玩法展示的玩法列表
    RateLimit:
      TargetRPS:    100
      InitialRPS:   0
      StepHeight:   10
      StepDuration: 1s
    Method:         ga.api.channel_play.ChannelPlayLogic.ShowTopicChannelTabList
    Body:           '
{
    "base_req": {},
    "self_game_ids": []
}
'


TeardownSteps:
  - Name:           离开房间-424
    RateLimit:
      TargetRPS:    100
      InitialRPS:   0
      StepHeight:   50
      StepDuration: 1s
    Method:         ga.api.channel_core.ChannelCoreLogic.ChannelQuit
    Body:           '
{
    "channel_id": 185563011
}
'
