#  房间内其他场景。
SetupSteps:
  - Name:           进入房间-50858
    RateLimit:
      TargetRPS:    100
      InitialRPS:   0
      StepHeight:   50
      StepDuration: 1s
    Method:         ga.api.channel_core.ChannelCoreLogic.ChannelEnterV2
    Body:           '
{
    "channel_id": 185563011
}
'

SerialSteps:
  - Name:           3100 - 获取房间tab配置相关信息
    RateLimit:
      TargetRPS:    1500
      InitialRPS:   0
      StepHeight:   10
      StepDuration: 1s
    Method:     ga.api.channel_play.ChannelPlayLogic.GetTopicChannelCfgInfo
    Body:           '
{
    "base_req": {},
    "channel_id": 185563011,
    "channel_type": 3
}
'

TeardownSteps:
  - Name:           离开房间-424
    RateLimit:
      TargetRPS:    100
      InitialRPS:   0
      StepHeight:   50
      StepDuration: 1s
    Method:         ga.api.channel_core.ChannelCoreLogic.ChannelQuit
    Body:           '
{
    "channel_id": 185563011
}
'
