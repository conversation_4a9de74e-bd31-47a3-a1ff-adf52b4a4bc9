#  房间内其他场景。
SetupSteps:
  - Name:           进入房间-50858
    RateLimit:
      TargetRPS:    100
      InitialRPS:   0
      StepHeight:   50
      StepDuration: 1s
    Method:         ga.api.channel_core.ChannelCoreLogic.ChannelEnterV2
    Body:           '
{
    "channel_id": 185563011
}
'

SerialSteps:
  - Name:           2532 - 获取UGC房间更多新增配置
    RateLimit:
      TargetRPS:    400
      InitialRPS:   0
      StepHeight:   10
      StepDuration: 1s
    Method:     ga.api.user.UserLogic.GetUgcMoreConfigList
    Body:           '
{
    "base_req": {},
    "channel_id": 185563011,
    "tab_id": 1
}
'

TeardownSteps:
  - Name:           离开房间-424
    RateLimit:
      TargetRPS:    100
      InitialRPS:   0
      StepHeight:   50
      StepDuration: 1s
    Method:         ga.api.channel_core.ChannelCoreLogic.ChannelQuit
    Body:           '
{
    "channel_id": 185563011
}
'
