#  房间内其他场景。
SetupSteps:
  - Name:           进入房间-50858
    RateLimit:
      TargetRPS:    100
      InitialRPS:   0
      StepHeight:   50
      StepDuration: 1s
    Method:         ga.api.channel_core.ChannelCoreLogic.ChannelEnterV2
    Body:           '
{
    "channel_id": 185563011
}
'

SerialSteps:
  - Name:           2531 - 获取UGC房间降噪模式
    RateLimit:
      TargetRPS:    1000
      InitialRPS:   0
      StepHeight:   10
      StepDuration: 1s
    Method:     ga.api.user.UserLogic.GetUgcChannelDenoiseMode
    Body:           '
{
    "base_req": {},
    "channel_id": 185563011
}
'

TeardownSteps:
  - Name:           离开房间-424
    RateLimit:
      TargetRPS:    100
      InitialRPS:   0
      StepHeight:   50
      StepDuration: 1s
    Method:         ga.api.channel_core.ChannelCoreLogic.ChannelQuit
    Body:           '
{
    "channel_id": 185563011
}
'
