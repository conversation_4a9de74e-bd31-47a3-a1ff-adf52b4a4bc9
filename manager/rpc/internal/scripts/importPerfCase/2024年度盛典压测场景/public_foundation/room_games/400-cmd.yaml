SetupSteps:
  - Name:           进入房间-50858
    RateLimit:
      TargetRPS:    200
      InitialRPS:   0
      StepHeight:   50
      StepDuration: 1s
    Method:         ga.api.channel_core.ChannelCoreLogic.ChannelEnterV2
    Body:           '
{
    "channel_id": {{int .my_channel_id}}
}
'

  - Name: 切换房间兴趣玩法-31501
    RateLimit:
      TargetRPS:    200
      InitialRPS:   0
      StepHeight:   50
      StepDuration: 1s
    Method: ga.api.hobby_channel.HobbyChannelLogic.SwitchHobbyChannelSubject
    Body: '
      {
          "base_req":     {},
          "channel_id":   {{int .my_channel_id}},
          "tab_id": 423
      }
    '

SerialSteps:
  - Name: 设置小游戏模式|修改游戏附加信息-30119
    RateLimit:
      TargetRPS:    200
      InitialRPS:   0
      StepHeight:   50
      StepDuration: 1s
    Method: ga.api.channel_open_game_controller.ChannelOpenGameControllerLogic.SetChannelGameModeInfo
    Body: '{
      "base_req": {}, 
      "channel_id": {{int .my_channel_id}}, 
      "game_mode": {"mode_key": "1player", "game_param": "v8_share", "player_limit_list": [1]},
      "load_seq": {{int .load_seq}}
    }'


TeardownSteps:
  - Name: 切换房间兴趣玩法-31501
    RateLimit:
      TargetRPS:    200
      InitialRPS:   0
      StepHeight:   50
      StepDuration: 1s
    Method: ga.api.hobby_channel.HobbyChannelLogic.SwitchHobbyChannelSubject
    Body: '{
      "base_req":   {},
      "channel_id": {{int .my_channel_id}},
      "tab_id":     1
    }'

  - Name:        离开房间-424
    RateLimit:
      TargetRPS: 200
      InitialRPS: 0
      StepHeight: 50
      StepDuration: 1s
    Method: ga.api.channel_core.ChannelCoreLogic.ChannelQuit
    Body: '
{
    "channel_id": {{int .my_channel_id}}
}
'
