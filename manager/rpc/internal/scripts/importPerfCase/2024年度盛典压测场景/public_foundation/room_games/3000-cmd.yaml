SetupSteps:
  - Name:           进入房间-50858
    RateLimit:
      TargetRPS:    200
      InitialRPS:   0
      StepHeight:   50
      StepDuration: 1s
    Method:         ga.api.channel_core.ChannelCoreLogic.ChannelEnterV2
    Body:           '
{
    "channel_id": {{int .my_channel_id}}
}
'

  - Name: 切换房间兴趣玩法-31501
    RateLimit:
      TargetRPS:    100
      InitialRPS:   0
      StepHeight:   50
      StepDuration: 1s
    Method: ga.api.hobby_channel.HobbyChannelLogic.SwitchHobbyChannelSubject
    Body: '
      {
          "base_req":     {},
          "channel_id":   {{int .my_channel_id}},
          "tab_id": 423
      }
    '

SerialSteps:
  - Name: 获取当前在玩小游戏信息|获取房间游戏加载情况-30072
    RateLimit:
      TargetRPS:    3000
      InitialRPS:   0
      StepHeight:   50
      StepDuration: 1s
    Method: ga.api.channel_open_game.ChannelOpenGameLogic.GetChannelLoadingGame
    Body: '
    {"base_req": {}, 
    "channel_id" : {{int .my_channel_id}}}
  '

TeardownSteps:
  - Name: 切换房间兴趣玩法-31501
    RateLimit:
      TargetRPS:    100
      InitialRPS:   0
      StepHeight:   50
      StepDuration: 1s
    Method: ga.api.hobby_channel.HobbyChannelLogic.SwitchHobbyChannelSubject
    Body: '{
      "base_req":   {},
      "channel_id": {{int .my_channel_id}},
      "tab_id":     1
    }'

  - Name:        离开房间-424
    RateLimit:
      TargetRPS: 200
      InitialRPS: 0
      StepHeight: 50
      StepDuration: 1s
    Method: ga.api.channel_core.ChannelCoreLogic.ChannelQuit
    Body: '
{
    "channel_id": {{int .my_channel_id}}
}
'
