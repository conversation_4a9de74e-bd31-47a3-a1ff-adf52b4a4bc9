SetupSteps:
  - Name:           进入房间-50858
    RateLimit:
      TargetRPS:    200
      InitialRPS:   0
      StepHeight:   50
      StepDuration: 1s
    Method:         ga.api.channel_core.ChannelCoreLogic.ChannelEnterV2
    Body:           '
{
    "channel_id": {{int .my_channel_id}}
}
'

SerialSteps:

  - Name:           上麦-430
    RateLimit:
      TargetRPS:    600
      InitialRPS:   0
      StepHeight:   5
      StepDuration: 1s
    Method:         ga.api.channel_core.ChannelCoreLogic.ChannelMicHold
    Body:           '
{
    "base_req":     {},
    "channel_id":   {{int .my_channel_id}},
    "permission_info": {"channel_type":3},
    "micr_info":       {"mic_id":{{randInt 1 5}}}
}
'

TeardownSteps:
  - Name:           离开房间-424
    RateLimit:
      TargetRPS:    200
      InitialRPS:   0
      StepHeight:   50
      StepDuration: 1s
    Method:         ga.api.channel_core.ChannelCoreLogic.ChannelQuit
    Body:           '
{
    "channel_id": {{int .my_channel_id}}
}
'
