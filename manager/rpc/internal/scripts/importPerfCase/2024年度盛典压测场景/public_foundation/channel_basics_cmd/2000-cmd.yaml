# 压测cmd有：2020、30350、1157、33100、2064共5个
SetupSteps:
  - Name:           进入房间-50858
    RateLimit:
      TargetRPS:    200
      InitialRPS:   0
      StepHeight:   50
      StepDuration: 1s
    Method:         ga.api.channel_core.ChannelCoreLogic.ChannelEnterV2
    Body:           '
{
    "channel_id": 185563011
}
'

SerialSteps:

  - Name:           获取房间背景信息-2020
    RateLimit:
      TargetRPS:    2000
      InitialRPS:   0
      StepHeight:   10
      StepDuration: 1s
    Method:         ga.api.channel_background.ChannelBackgroundLogic.GetCurChannelBackgroundInfo
    Body:           '
{
    "base_req":     {},
    "channel_id":   185563011
}
'

  - Name:           获取房间节目单-30350
    RateLimit:
      TargetRPS:    2000
      InitialRPS:   0
      StepHeight:   10
      StepDuration: 1s
    Method:         ga.api.channel_performance.ChannelPerformanceLogic.GetChannelPerformance
    Body:           '
{
    "base_req":     {},
    "channel_id":   185563011
}
'

  - Name:           获取当前玩法信息-33100
    RateLimit:
      TargetRPS:    2000
      InitialRPS:   0
      StepHeight:   10
      StepDuration: 1s
    Method:         ga.api.channel_scheme.ChannelSchemeLogic.GetChannelSchemeInfo
    Body:           '
{
    "base_req":     {},
    "cid":          185563011
}
'

  - Name:           房间战歌播放器状态-2064
    RateLimit:
      TargetRPS:    2000
      InitialRPS:   0
      StepHeight:   10
      StepDuration: 1s
    Method:         ga.api.channel_music.ChannelMusicLogic.ChannelMusicV2GetStatus
    Body:           '
{
    "base_req":     {},
    "channel_id":   185563011
}
'


TeardownSteps:
  - Name:           离开房间-424
    RateLimit:
      TargetRPS:    200
      InitialRPS:   0
      StepHeight:   50
      StepDuration: 1s
    Method:         ga.api.channel_core.ChannelCoreLogic.ChannelQuit
    Body:           '
{
    "channel_id": 185563011
}
'
