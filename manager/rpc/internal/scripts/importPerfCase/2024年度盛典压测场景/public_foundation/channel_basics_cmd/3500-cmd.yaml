# 压测cmd有：900
SetupSteps:
  - Name:           进入房间-50858
    RateLimit:
      TargetRPS:    200
      InitialRPS:   0
      StepHeight:   50
      StepDuration: 1s
    Method:         ga.api.channel_core.ChannelCoreLogic.ChannelEnterV2
    Body:           '
{
    "channel_id": 185563011
}
'

SerialSteps:

  - Name:           获取房间推送通道token-900
    RateLimit:
      TargetRPS:    3500
      InitialRPS:   0
      StepHeight:   20
      StepDuration: 1s
    Method:         ga.api.push.PushLogic.GetPushToken
    Body:           '
{
    "base_req":     {},
    "channel_id":   185563011
}
'


TeardownSteps:
  - Name:           离开房间-424
    RateLimit:
      TargetRPS:    200
      InitialRPS:   0
      StepHeight:   50
      StepDuration: 1s
    Method:         ga.api.channel_core.ChannelCoreLogic.ChannelQuit
    Body:           '
{
    "channel_id": 185563011
}
'
