SetupSteps:
  - Name:           进入房间-50858
    RateLimit:
      TargetRPS:    200
      InitialRPS:   0
      StepHeight:   50
      StepDuration: 1s
    Method:         ga.api.channel_core.ChannelCoreLogic.ChannelEnterV2
    Body:           '
{
    "channel_id": 185563011
}
'

SerialSteps:
  - Name:           获取麦下用户列表-3501
    RateLimit:
      TargetRPS:    5000
      InitialRPS:   0
      StepHeight:   20
      StepDuration: 1s
    Method:     ga.api.channelol_logic_go.ChannelolLogicGo.CChannelGetUnderTheMicroList
    Body:           '
{
    "base_req":     {},
    "channel_id":   185563011 
}
'

TeardownSteps:
  - Name:           离开房间-424
    RateLimit:
      TargetRPS:    200
      InitialRPS:   0
      StepHeight:   50
      StepDuration: 1s
    Method:         ga.api.channel_core.ChannelCoreLogic.ChannelQuit
    Body:           '
{
    "channel_id": 185563011
}
'
