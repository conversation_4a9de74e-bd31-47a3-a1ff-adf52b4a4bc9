# 2712 
SetupSteps:
  - Name:           进入房间-50858
    RateLimit:
      TargetRPS:    100
      InitialRPS:   0
      StepHeight:   50
      StepDuration: 1s
    Method:         ga.api.channel_core.ChannelCoreLogic.ChannelEnterV2
    Body:           '
{
    "channel_id": 185563011
}
'

SerialSteps:

  - Name:           获取排麦列表-2712
    RateLimit:
      TargetRPS:    200
      InitialRPS:   0
      StepHeight:   5
      StepDuration: 1s
    Method:         ga.api.channel_live.ChannelLiveCppLogic.ChannelNormalQueueUpMicList
    Body:           '
{
    "base_req":     {},
    "channel_id":   185563011,
    "offset":       0,
    "limit":        20
}
'

TeardownSteps:
  - Name:           离开房间-424
    RateLimit:
      TargetRPS:    100
      InitialRPS:   0
      StepHeight:   50
      StepDuration: 1s
    Method:         ga.api.channel_core.ChannelCoreLogic.ChannelQuit
    Body:           '
{
    "channel_id": 185563011
}
'
