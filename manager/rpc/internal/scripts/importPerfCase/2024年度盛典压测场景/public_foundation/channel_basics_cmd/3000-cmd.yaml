SetupSteps:
  - Name:           进入房间-50858
    RateLimit:
      TargetRPS:    200
      InitialRPS:   0
      StepHeight:   50
      StepDuration: 1s
    Method:         ga.api.channel_core.ChannelCoreLogic.ChannelEnterV2
    Body:           '
{
    "channel_id": 185563011
}
'

SerialSteps:

  - Name:           获取房间扩展信息-2052
    RateLimit:
      TargetRPS:    3000
      InitialRPS:   0
      StepHeight:   20
      StepDuration: 1s
    Method:         ga.api.channel.ChannelLogic.ChannelGetExtendInfo
    Body:           '
{
    "base_req":     {},
    "channel_id":   185563011,
    "extend_type_bitmap": 3
}
'

  - Name:           获取歌曲列表-2060
    RateLimit:
      TargetRPS:    3000
      InitialRPS:   0
      StepHeight:   50
      StepDuration: 1s
    Method:         ga.api.channel_music.ChannelMusicLogic.ChannelMusicV2GetList
    Body:           '
{
    "base_req":     {},
    "channel_id":    185563011
}
'

TeardownSteps:
  - Name:           离开房间-424
    RateLimit:
      TargetRPS:    200
      InitialRPS:   0
      StepHeight:   50
      StepDuration: 1s
    Method:         ga.api.channel_core.ChannelCoreLogic.ChannelQuit
    Body:           '
{
    "channel_id": 185563011
}
'
