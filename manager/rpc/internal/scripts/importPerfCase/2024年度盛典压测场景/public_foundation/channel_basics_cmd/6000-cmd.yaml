SetupSteps:
  - Name:           进入房间-50858
    RateLimit:
      TargetRPS:    200
      InitialRPS:   0
      StepHeight:   50
      StepDuration: 1s
    Method:         ga.api.channel_core.ChannelCoreLogic.ChannelEnterV2
    Body:           '
{
    "channel_id": 185563011
}
'

SerialSteps:
  - Name:           定时获取房间音频token-50851
    RateLimit:
      TargetRPS:    6000
      InitialRPS:   0
      StepHeight:   30
      StepDuration: 1s
    Method:         ga.api.channel_audio_token.ChannelAudioTokenLogic.GetChannelAudioToken
    Body:           '
{
    "base_req":     {},
    "scene":        1,
    "cid":          185563011,
    "assist_info":  {"scheme_id": 1}
}
'

TeardownSteps:
  - Name:           离开房间-424
    RateLimit:
      TargetRPS:    200
      InitialRPS:   0
      StepHeight:   50
      StepDuration: 1s
    Method:         ga.api.channel_core.ChannelCoreLogic.ChannelQuit
    Body:           '
{
    "channel_id": 185563011
}
'
