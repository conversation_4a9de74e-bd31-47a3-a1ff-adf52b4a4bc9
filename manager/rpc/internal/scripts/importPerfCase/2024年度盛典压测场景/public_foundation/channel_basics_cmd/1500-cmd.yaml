# 433、50858、424、50859、435、30370、432 共7个接口
SerialSteps:
  - Name:           433 - 获取房间详情
    RateLimit:
      TargetRPS:    1500
      InitialRPS:   0
      StepHeight:   10
      StepDuration: 1s
    Method:         ga.api.channel.ChannelLogic.GetChannelDetail
    Body:           '
{
    "channel_id": 185563011
}
'

  - Name:           50858 - 进房(核心SLA命令)
    RateLimit:
      TargetRPS:    1500
      InitialRPS:   0
      StepHeight:   10
      StepDuration: 1s
    Method:         ga.api.channel_core.ChannelCoreLogic.ChannelEnterV2
    Body:           '
{
    "channel_id": 185563011
}
'

  - Name:           离开房间-424
    RateLimit:
      TargetRPS:    1500
      InitialRPS:   0
      StepHeight:   10
      StepDuration: 1s
    Method:         ga.api.channel_core.ChannelCoreLogic.ChannelQuit
    Body:           '
{
    "channel_id":   185563011
}
'

  - Name:           获取进房扩展信息-50859
    RateLimit:
      TargetRPS:    1500
      InitialRPS:   0
      StepHeight:   10
      StepDuration: 1s
    Method:         ga.api.channel_logic_go.ChannelLogicGo.GetChannelEnterExtInfo
    Body:           '
{
    "channel_id":   185563011,
    "enter_source":  {{randInt 1 50}}
}
'

  - Name:           拉取房间公屏消息-435
    RateLimit:
      TargetRPS:    1500
      InitialRPS:   0
      StepHeight:   10
      StepDuration: 1s
    Method:         ga.api.channel_im.ChannelImGoLogic.GetChannelMsg
    Body:           '
{
    "channel_id":   185563011,
    "begin_seq":    0,
    "end_seq":      10,
    "is_init":      true
}
'

  - Name:           获取房间等级信息-30370
    RateLimit:
      TargetRPS:    1500
      InitialRPS:   0
      StepHeight:   10
      StepDuration: 1s
    Method:         ga.api.channel_level.ChannelLevelLogic.GetChannelLevelInfo
    Body:           '
{
    "channel_id": 185563011
}
'
  - Name:           拉取麦位列表-432
    RateLimit:
      TargetRPS:    1500
      InitialRPS:   0
      StepHeight:   10
      StepDuration: 1s
    Method:         ga.api.channel_mic.ChannelMicLogic.ChannelGetMicList
    Body:           '
{
    "channel_id": 185563011
}
'

