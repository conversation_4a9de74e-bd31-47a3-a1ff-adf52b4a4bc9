SerialSteps:
  - Name:           604 - 检查是否有关联登录账号
    RateLimit:
      TargetRPS: 250
      InitialRPS: 0
      StepHeight: 100
      StepDuration: 1s
    Method:     ga.api.account.AccountLogic.CheckRelatedLoginAccount
    Body:           '
{
    "base_req": {},
    "ignore_related_uid_list": [{{.uid}}] 
}
'
  - Name:           605 - 获取关联登录账号
    RateLimit:
      TargetRPS: 250
      InitialRPS: 0
      StepHeight: 100
      StepDuration: 1s
    Method:     ga.api.account.AccountLogic.GetRelatedLoginAccount
    Body:           '
{
    "base_req": {},
    "ignore_related_uid_list": []
}
'
  - Name:           1123 - 获取用户离线好友列表
    RateLimit:
      TargetRPS:    250
      InitialRPS:   0
      StepHeight:   100
      StepDuration: 1s
    Method:     ga.api.im.ImLogic.GetMessagePeerReadStatus
    Body:           '
{
    "base_req": {},
    "account_list": []     
}
'
  - Name:           1125 - 获取频道跟随开关
    RateLimit:
      TargetRPS: 250
      InitialRPS: 0
      StepHeight: 100
      StepDuration: 1s
    Method:     ga.api.online.OnlineLogic.GetFollowChannelAuth
    Body:           '
{
    "base_req": {},
    "uid": {{ .uid }}
}
'
  - Name:           2605 - 获取关联登录账号
    RateLimit:
      TargetRPS: 250
      InitialRPS: 0
      StepHeight: 100
      StepDuration: 1s
    Method:     ga.api.realnameauth_go.RealNameAuthGoLogic.GetRealNameAuthStateV2
    Body:           '
{
    "base_req": {}
}
' 
