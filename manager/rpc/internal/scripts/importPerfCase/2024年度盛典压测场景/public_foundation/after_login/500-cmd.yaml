# 登录后进房前基础命令场景。 30472、30473 共2个cmd

SerialSteps:
  - Name:           30472 - 获取所有需要更新的配置文件列表
    RateLimit:
      TargetRPS:    500
      InitialRPS:   0
      StepHeight:   10
      StepDuration: 1s
    Method:         ga.api.client_conf_mgr.ClientConfMgrLogic.GetConfList
    Body:           '
{
    "base_req": {},
    "seq": 0
}
'
  - Name:           30473 - 检查通告是否有更新
    RateLimit:
      TargetRPS:    500
      InitialRPS:   0
      StepHeight:   10
      StepDuration: 1s
    Method:     ga.api.client_conf_mgr.ClientConfMgrLogic.CheckAnnouncementUpdate
    Body:           '
{
    "base_req": {}
}
'


