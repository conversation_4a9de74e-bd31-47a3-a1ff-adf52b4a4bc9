# 登录后进房前基础命令场景。6601、95001、30010、30175、192、401、604、605、1123、1125、2605 共11个cmd

SerialSteps:
  - Name:           6601 - 获取PC登录申请
    RateLimit:
      TargetRPS:    1000
      InitialRPS:   0
      StepHeight:   50
      StepDuration: 1s
    Method:         ga.api.auth.AuthLogic.GetPcAuthApply
    Body:           '
{
    "base_req": {}
}
'
  - Name:           95001 - 检查vip客服入口权限
    RateLimit:
      TargetRPS:    1000
      InitialRPS:   0
      StepHeight:   50
      StepDuration: 1s
    Method:         ga.api.udesk_api.UdeskApiLogic.CheckVipKefuAccess
    Body:           '
{
    "base_req": {}
}
'

  - Name:           30010 - 同步配置
    RateLimit:
      TargetRPS:    1000
      InitialRPS:   0
      StepHeight:   50
      StepDuration: 1s
    Method:         ga.api.sync.SyncLogic.ConfigV2Sync
    Body:           '
{
    "base_req": {},
    "config_type_list": []                 
}
'

  - Name:           30175 - 获取黑白盒开关
    RateLimit:
      TargetRPS:    1000
      InitialRPS:   0
      StepHeight:   50
      StepDuration: 1s
    Method:         ga.api.unclaimed.UnclaimedLogic.GetBlackWhiteBoxSuperviseSwitch
    Body:           '
{
    "base_req": {}
}
'

  - Name:           192 - 获取im消息的对方已读状态
    RateLimit:
      TargetRPS:    1000
      InitialRPS:   0
      StepHeight:   50
      StepDuration: 1s
    Method:         ga.api.im.ImLogic.GetMessagePeerReadStatus
    Body:           '
{
    "base_req": {},
    "account_list": []      
}
'

  - Name:           401 - 上报离线推送
    RateLimit:
      TargetRPS:    1000
      InitialRPS:   0
      StepHeight:   50
      StepDuration: 1s
    Method:         ga.api.push.PushLogic.RegisterPushDeviceToken
    Body:           '
{
    "base_req": {},
    "cmd": 1,
    "device_token": "{{ str .uid | b64enc }}",
    "os_type": 0,
    "os_ver": ""
}
'

  - Name:           604 - 检查是否有关联登录账号
    RateLimit:
      TargetRPS:    1000
      InitialRPS: 0
      StepHeight: 50
      StepDuration: 1s
    Method:     ga.api.account.AccountLogic.CheckRelatedLoginAccount
    Body:           '
{
    "base_req": {},
    "ignore_related_uid_list": [{{.uid}}] 
}
'
  - Name:           605 - 获取关联登录账号
    RateLimit:
      TargetRPS:    1000
      InitialRPS: 0
      StepHeight: 50
      StepDuration: 1s
    Method:     ga.api.account.AccountLogic.GetRelatedLoginAccount
    Body:           '
{
    "base_req": {},
    "ignore_related_uid_list": []
}
'
  - Name:           1123 - 获取用户离线好友列表
    RateLimit:
      TargetRPS:    1000
      InitialRPS:   0
      StepHeight:   50
      StepDuration: 1s
    Method:     ga.api.im.ImLogic.GetMessagePeerReadStatus
    Body:           '
{
    "base_req": {},
    "account_list": []     
}
'
  - Name:           1125 - 获取频道跟随开关
    RateLimit:
      TargetRPS:    1000
      InitialRPS: 0
      StepHeight: 50
      StepDuration: 1s
    Method:     ga.api.online.OnlineLogic.GetFollowChannelAuth
    Body:           '
{
    "base_req": {},
    "uid": {{ .uid }}
}
'
  - Name:           2605 - 获取关联登录账号
    RateLimit:
      TargetRPS:    1000
      InitialRPS:   0
      StepHeight:   50
      StepDuration: 1s
    Method:     ga.api.realnameauth_go.RealNameAuthGoLogic.GetRealNameAuthStateV2
    Body:           '
{
    "base_req": {}
}
' 

