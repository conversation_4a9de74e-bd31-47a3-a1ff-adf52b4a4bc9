SetupSteps:
  - Name:           进入房间-50858
    RateLimit:
      TargetRPS:    100
      InitialRPS:   0
      StepHeight:   50
      StepDuration: 1s
    Method:         ga.api.channel_core.ChannelCoreLogic.ChannelEnterV2
    Body:           '
{
    "channel_id": {{int .channel_id}}
}
'

SerialSteps:
  - Name:           1194 - 送礼
    RateLimit:
      TargetRPS:    200
      InitialRPS:   0
      StepHeight:   10
      StepDuration: 1s
    Method:     ga.api.present_go.PresentGoLogic.CommonSendPresent
    Body:           '
{
    "base_req": {},
    "item_id": 12,
    "channel_id": {{int .channel_id}},
    "count": 1,
    "item_source": 0,
    "send_type": 0,
    "uid_list": [{{.target_uid}}]
}
'

TeardownSteps:
  - Name:           离开房间-424
    RateLimit:
      TargetRPS:    100
      InitialRPS:   0
      StepHeight:   50
      StepDuration: 1s
    Method:         ga.api.channel_core.ChannelCoreLogic.ChannelQuit
    Body:           '
{
    "channel_id": {{int .channel_id}}
}
'