#  房间内营收场景。
SetupSteps:
  - Name:           进入房间-50858
    RateLimit:
      TargetRPS:    50
      InitialRPS:   0
      StepHeight:   10
      StepDuration: 1s
    Method:         ga.api.channel_core.ChannelCoreLogic.ChannelEnterV2
    Body:           '
{
    "channel_id": 185563011
}
'

SerialSteps:
  - Name:           31201 - 获取用户全部升级礼物当前版本的状态
    RateLimit:
      TargetRPS:    1000
      InitialRPS:   0
      StepHeight:   10
      StepDuration: 1s
    Method:         ga.api.levelup_present.LevelUpPresentLogic.GetUserLevelUpPresentStatus
    Body:           '
{
    "base_req":     {}
}
'

  - Name:           5058 - 根据channelId获取房间的标签信息
    RateLimit:
      TargetRPS:    1000
      InitialRPS:   0
      StepHeight:   10
      StepDuration: 1s
    Method:     ga.api.channel_recommend.ChannelRecommendLogic.GetChannelTagInfoService
    Body:           '
{
    "base_req":     {},
    "channel_id":   185563011
}
'

  - Name:           2510 - 火箭跑道信息获取
    RateLimit:
      TargetRPS:    1000
      InitialRPS:   0
      StepHeight:   10
      StepDuration: 1s
    Method:         ga.api.channel_minigame_go.ChannelMiniGameGoLogic.GetChannelPresentRunwayList
    Body:           '
{
    "base_req":     {},
    "channel_id":   185563011
}
'

  - Name:           30681 - 用于判断防止重复发挚友邀请
    RateLimit:
      TargetRPS:    1000
      InitialRPS:   0
      StepHeight:   10
      StepDuration: 1s
    Method:         ga.api.fellow.FellowLogic.GetAllChannelFellowInvite
    Body:           '
{
    "base_req":     {},
    "channel_id":   185563011
}
'


TeardownSteps:
  - Name:           离开房间-424
    RateLimit:
      TargetRPS:    50
      InitialRPS:   0
      StepHeight:   10
      StepDuration: 1s
    Method:         ga.api.channel_core.ChannelCoreLogic.ChannelQuit
    Body:           '
{
    "channel_id": 185563011
}
'
