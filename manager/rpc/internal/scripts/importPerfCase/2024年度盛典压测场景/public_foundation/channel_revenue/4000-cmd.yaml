#  房间内营收场景。
SetupSteps:
  - Name:           进入房间-50858
    RateLimit:
      TargetRPS:    50
      InitialRPS:   0
      StepHeight:   50
      StepDuration: 1s
    Method:         ga.api.channel_core.ChannelCoreLogic.ChannelEnterV2
    Body:           '
{
    "channel_id": 185563011
}
'

SerialSteps:
  - Name:           2195 - 获取背包物品信息
    RateLimit:
      TargetRPS:    4000
      InitialRPS:   0
      StepHeight:   10
      StepDuration: 1s
    Method:         ga.api.backpack_go.BackpackLogicGo.GetUserFragment
    Body:           '
{
    "base_req":     {}
}
'

  - Name:           1191 - 获取礼物的额外配置
    RateLimit:
      TargetRPS:    4000
      InitialRPS:   0
      StepHeight:   10
      StepDuration: 1s
    Method:         ga.api.present_go.PresentGoLogic.GetPresentExtraConfig
    Body:           '
{
    "base_req":     {}
}
'

TeardownSteps:
  - Name:           离开房间-424
    RateLimit:
      TargetRPS:    50
      InitialRPS:   0
      StepHeight:   50
      StepDuration: 1s
    Method:         ga.api.channel_core.ChannelCoreLogic.ChannelQuit
    Body:           '
{
    "channel_id": 185563011
}
'
