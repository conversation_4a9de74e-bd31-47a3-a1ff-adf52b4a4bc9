#  房间内营收场景。
SetupSteps:
  - Name:           进入房间-50858
    RateLimit:
      TargetRPS:    50
      InitialRPS:   0
      StepHeight:   10
      StepDuration: 1s
    Method:         ga.api.channel_core.ChannelCoreLogic.ChannelEnterV2
    Body:           '
{
    "channel_id": 185563011
}
'

SerialSteps:
  - Name:           426 - 房间榜单（房间顶部的在线榜）
    RateLimit:
      TargetRPS:    6000
      InitialRPS:   0
      StepHeight:   10
      StepDuration: 1s
    Method:         ga.api.channel_rank.ChannelRankLogic.ChannelGetMemberList
    Body:           '
{
    "base_req": {},
    "channel_id": 185563011,
    "begin_id": 0,
    "req_cnt": 50
}
'

TeardownSteps:
  - Name:           离开房间-424
    RateLimit:
      TargetRPS:    50
      InitialRPS:   0
      StepHeight:   10
      StepDuration: 1s
    Method:         ga.api.channel_core.ChannelCoreLogic.ChannelQuit
    Body:           '
{
    "channel_id": 185563011
}
'
