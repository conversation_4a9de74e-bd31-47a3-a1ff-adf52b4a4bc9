#  房间内营收场景。31151、31200、31201、36200、31154
SetupSteps:
  - Name:           进入房间-50858
    RateLimit:
      TargetRPS:    50
      InitialRPS:   0
      StepHeight:   10
      StepDuration: 1s
    Method:         ga.api.channel_core.ChannelCoreLogic.ChannelEnterV2
    Body:           '
{
    "channel_id": 185563011
}
'

SerialSteps:
  - Name:           31151 - 幸运礼物权限入口
    RateLimit:
      TargetRPS:    2000
      InitialRPS:   0
      StepHeight:   10
      StepDuration: 1s
    Method:         ga.api.magic_spirit.MagicSpiritLogic.GetMagicSpiritUsable
    Body:           '
{
    "base_req":     {},
    "channel_id":   185563011
}
'

  - Name:           31154 - 幸运礼物待开箱礼物信息
    RateLimit:
      TargetRPS:    2000
      InitialRPS:   0
      StepHeight:   10
      StepDuration: 1s
    Method:     ga.api.magic_spirit.MagicSpiritLogic.GetChannelAllUnpackGift
    Body:           '
{
    "base_req":     {},
    "channel_id":   185563011
}
'

  - Name:           6902 - 体验券（如大神带飞券）的红点提醒
    RateLimit:
      TargetRPS:    2000
      InitialRPS:   0
      StepHeight:   10
      StepDuration: 1s
    Method:         ga.api.pgc_channel.PgcChannelLogicService.GetUserTicketRemind
    Body:           '
{
    "base_req":     {},
    "cid":   185563011
}
'


TeardownSteps:
  - Name:           离开房间-424
    RateLimit:
      TargetRPS:    50
      InitialRPS:   0
      StepHeight:   10
      StepDuration: 1s
    Method:         ga.api.channel_core.ChannelCoreLogic.ChannelQuit
    Body:           '
{
    "channel_id": 185563011
}
'
