#  房间内营收场景。
SetupSteps:
  - Name:           进入房间-50858
    RateLimit:
      TargetRPS:    50
      InitialRPS:   0
      StepHeight:   10
      StepDuration: 1s
    Method:         ga.api.channel_core.ChannelCoreLogic.ChannelEnterV2
    Body:           '
{
    "channel_id": 185563011
}
'

SerialSteps:
  - Name:           36200 - 获取专属定制礼物配置列表
    RateLimit:
      TargetRPS:    3000
      InitialRPS:   0
      StepHeight:   10
      StepDuration: 1s
    Method:         ga.api.present_go.PresentGoLogic.GetCustomizedPresentList
    Body:           '
{
    "base_req":     {}
}
'

  - Name:           1172 - 获取冠名礼物配置信息
    RateLimit:
      TargetRPS:    3000
      InitialRPS:   0
      StepHeight:   10
      StepDuration: 1s
    Method:         ga.api.channel_recommend.ChannelRecommendLogic.GetChannelTagInfoService
    Body:           '
{
    "base_req":     {},
    "channel_id": 185563011
}
'

TeardownSteps:
  - Name:           离开房间-424
    RateLimit:
      TargetRPS:    50
      InitialRPS:   0
      StepHeight:   10
      StepDuration: 1s
    Method:         ga.api.channel_core.ChannelCoreLogic.ChannelQuit
    Body:           '
{
    "channel_id": 185563011
}
'
