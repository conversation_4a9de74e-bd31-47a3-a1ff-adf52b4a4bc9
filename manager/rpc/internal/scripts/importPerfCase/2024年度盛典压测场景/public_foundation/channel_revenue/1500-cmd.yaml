#  房间内营收场景。
SetupSteps:
  - Name:           进入房间-50858
    RateLimit:
      TargetRPS:    50
      InitialRPS:   0
      StepHeight:   10
      StepDuration: 1s
    Method:         ga.api.channel_core.ChannelCoreLogic.ChannelEnterV2
    Body:           '
{
    "channel_id": 185563011
}
'

SerialSteps:
  - Name:           31200 - 获取全部的升级礼物配置
    RateLimit:
      TargetRPS:    1500
      InitialRPS:   0
      StepHeight:   10
      StepDuration: 1s
    Method:         ga.api.levelup_present.LevelUpPresentLogic.GetAllLevelUpPresentList
    Body:           '
{
    "base_req":     {}
}
'

TeardownSteps:
  - Name:           离开房间-424
    RateLimit:
      TargetRPS:    50
      InitialRPS:   0
      StepHeight:   10
      StepDuration: 1s
    Method:         ga.api.channel_core.ChannelCoreLogic.ChannelQuit
    Body:           '
{
    "channel_id": 185563011
}
'
