SetupSteps:
  - Name:           进入房间-50858
    RateLimit:
      TargetRPS:    200
      InitialRPS:   0
      StepHeight:   50
      StepDuration: 1s
    Method:         ga.api.channel_core.ChannelCoreLogic.ChannelEnterV2
    Body:           '
{
    "channel_id": 185060428
}
'
ParallelSteps:
  #	star-trek-logic	@林贤@周韶勇
  - Name: 32142 - 星际巡航玩法信息
    RateLimit:
      TargetRPS: 1500
      InitialRPS: 0
      StepHeight: 50
      StepDuration: 3s
    Method: ga.api.star_trek.StarTrekLogic.GetStatTrekInfo
    Body: '{
        "base_req":   {}
      }'
#	magic-spirit-logic	@周韶勇
  - Name: 31151 - 获取是否可用魔法精灵 过滤房间UID黑名单
    RateLimit:
      TargetRPS: 1500
      InitialRPS: 0
      StepHeight: 50
      StepDuration: 3s
    Method: ga.api.magic_spirit.MagicSpiritLogic.GetMagicSpiritUsable
    Body: '{
      "base_req":   {},
      "channel_id": 185060428
    }'
#	levelup-present-logic	@王有斌
  - Name: 31200 - 获取全部的升级礼物配置（基础+批量）
    RateLimit:
      TargetRPS: 1500
      InitialRPS: 0
      StepHeight: 50
      StepDuration: 3s
    Method: ga.api.levelup_present.LevelUpPresentLogic.GetAllLevelUpPresentList
    Body: '{
      "base_req":   {}
    }'
#	levelup-present-logic	@王有斌
  - Name: 31201 - 获取用户全部升级礼物当前版本的状态
    RateLimit:
      TargetRPS: 1500
      InitialRPS: 0
      StepHeight: 50
      StepDuration: 3s
    Method:     ga.api.levelup_present.LevelUpPresentLogic.GetUserLevelUpPresentStatus
    Body: '{
      "base_req":   {}
    }'
#	anchor-check-logic	@王有斌
  - Name: 32009 - 获取直播间主播任务入口
    RateLimit:
      TargetRPS: 1500
      InitialRPS: 0
      StepHeight: 50
      StepDuration: 3s
    Method: ga.api.anchor_check.AnchorCheckLogic.GetLiveAnchorTaskEntry
    Body: '{
      "base_req":   {},
      "anchor_uid": 608024,
      "channel_id": 185060428
    }'
#	channel-minigame-go-logic	@陈伟贤
  - Name: 32105 - 检查入口状态
    RateLimit:
      TargetRPS: 1500
      InitialRPS: 0
      StepHeight: 50
      StepDuration: 3s
    Method: ga.api.channel_minigame_go.ChannelMiniGameGoLogic.GetVotePkStatus
    Body: '{
      "base_req":   {},
      "channel_id": 185060428
    }'
TeardownSteps:
  - Name:           离开房间-424
    RateLimit:
      TargetRPS:    200
      InitialRPS:   0
      StepHeight:   50
      StepDuration: 1s
    Method:         ga.api.channel_core.ChannelCoreLogic.ChannelQuit
    Body:           '
{
    "channel_id": 185060428
}
'
