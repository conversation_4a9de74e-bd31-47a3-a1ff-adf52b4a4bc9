ParallelSteps:
#	present-go-logic	@赵亮
  - Name: 36200 - 获取专属定制礼物配置列表
    RateLimit:
      TargetRPS: 1500
      InitialRPS: 0
      StepHeight: 50
      StepDuration: 3s
    Method:     ga.api.present_go.PresentGoLogic.GetCustomizedPresentList
    Body: '{
      "base_req":   {}
    }'
#	revenue-ext-game-logic	@林贤
  - Name: 36595 - 获取用户游戏信息
    RateLimit:
      TargetRPS: 1500
      InitialRPS: 0
      StepHeight: 50
      StepDuration: 3s
    Method: ga.api.revenue_ext_game.RevenueExtGameLogic.GetUserExtGameInfo
    Body: '{
      "base_req":   {},
      "channel_id": 185060428
    }'
#	revenue-ext-game-logic	@林贤
  - Name: 36596 - 获取直播间游戏权限入口
    RateLimit:
      TargetRPS: 1500
      InitialRPS: 0
      StepHeight: 50
      StepDuration: 3s
    Method: ga.api.revenue_ext_game.RevenueExtGameLogic.GetChannelExtGameAccess
    Body: '{
      "base_req":   {},
      "channel_id": 185060428
    }'
#	revenue-audio-stream-logic	@林贤@周韶勇
  - Name: 36952 - 进房拉取连麦信息
    RateLimit:
      TargetRPS: 1500
      InitialRPS: 0
      StepHeight: 50
      StepDuration: 3s
    Method: ga.api.revenue_audio_stream.RevenueAudioStreamLogic.GetAudioStreamInfo
    Body: '{
      "base_req":   {},
      "channel_id": 185060428
    }'
#	hunt-monster-logic	@王群盛
  - Name: 5604 - 获取用户打龙道具数量
    RateLimit:
      TargetRPS: 1500
      InitialRPS: 0
      StepHeight: 50
      StepDuration: 3s
    Method: ga.api.hunt_monster.HuntMonsterLogic.GetHuntMonsterItem
    Body: '{
      "base_req":   {}
    }'
#	hunt-monster-logic	@王群盛
  - Name: 5605 - 获取用户打龙任务信息
    RateLimit:
      TargetRPS: 1500
      InitialRPS: 0
      StepHeight: 50
      StepDuration: 3s
    Method: ga.api.hunt_monster.HuntMonsterLogic.GetUserHuntMissionInfo
    Body: '{
      "base_req":   {},
      "channel_id": 185060428,
      "uid": 329661167,
      "mission_type": 0
    }'