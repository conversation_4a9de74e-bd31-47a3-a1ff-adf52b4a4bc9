SetupSteps:
  - Name: 进入房间-50858
    RateLimit:
      TargetRPS: 500
      InitialRPS: 0
      StepHeight: 50
      StepDuration: 1s
    Method: ga.api.channel_core.ChannelCoreLogic.ChannelEnterV2
    Body: '
{
    "base_req": {},
    "channel_id":   185060428
}
'
SerialSteps:
  #	gaproxysession	@谢绍兵
  #  - Name: 7 - 房间心跳
  #    RateLimit:
  #      TargetRPS: 375
  #      InitialRPS: 0
  #      StepHeight: 50
  #      StepDuration: 1s
  #    Method: ga.session.SessionKeepAlive
  #    Body:           '
  #{
  #    "base_req": {},
  #    "session_id": 185060428
  #}
  #'
  #	sync-logic	@陈剑平@田文杰
  - Name: 74 - 检查所有可用的sync key
    RateLimit:
      TargetRPS: 375
      InitialRPS: 0
      StepHeight: 50
      StepDuration: 1s
    Method: ga.api.sync.SyncGoLogic.CheckSyncKey
    Body: '
{
    "base_req": {}
}
'

  - Name: 发送公屏文本消息消息-434
    RateLimit:
      TargetRPS: 100
      InitialRPS: 0
      StepHeight: 10
      StepDuration: 1s
    Method: ga.api.channel_im.ChannelImGoLogic.SendChannelTextMsg
    Body: '
{
    "channel_id": 185060428,
    "content":    "{{nounZH}}",
    "type": 1
}
'
  #	channel-core-logic	@陈伟贤@吴楚明
  - Name: 424 - 退房
    RateLimit:
      TargetRPS: 100
      InitialRPS: 0
      StepHeight: 50
      StepDuration: 1s
    Method: ga.api.channel_core.ChannelCoreLogic.ChannelQuit
    Body: '
{
    "base_req": {},
    "channel_id":   185060428
}
'

  - Name: 进入房间-50858
    RateLimit:
      TargetRPS: 100
      InitialRPS: 0
      StepHeight: 50
      StepDuration: 1s
    Method: ga.api.channel_core.ChannelCoreLogic.ChannelEnterV2
    Body: '
{
    "base_req": {},
    "channel_id":   185060428
}
'

TeardownSteps:
  #	channel-core-logic	@陈伟贤@吴楚明
  - Name: 424 - 退房
    RateLimit:
      TargetRPS: 500
      InitialRPS: 0
      StepHeight: 50
      StepDuration: 1s
    Method: ga.api.channel_core.ChannelCoreLogic.ChannelQuit
    Body: '
{
    "base_req": {},
    "channel_id":   185060428
}
'
