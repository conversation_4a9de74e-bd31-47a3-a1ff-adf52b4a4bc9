SetupSteps:
  - Name:           进入房间-50858
    RateLimit:
      TargetRPS:    200
      InitialRPS:   0
      StepHeight:   50
      StepDuration: 1s
    Method:         ga.api.channel_core.ChannelCoreLogic.ChannelEnterV2
    Body:           '
{
    "channel_id": 185060428
}
'
ParallelSteps:
  #	official-live-channel-logic	@陈伟贤@詹明俊
  - Name: 30413 - 获取直播间被转播信息
    RateLimit:
      TargetRPS: 1500
      InitialRPS: 0
      StepHeight: 50
      StepDuration: 1s
    Method: ga.api.official_live_channel.OfficialLiveChannelLogic.GetLiveChannelRelay
    Body: '{
      "base_req":   {}, 
      "channel_id": 185060428
    }'

#	channel-live-logic	@李金安
  - Name: 3610 - 获取主播荣誉铭牌
    RateLimit:
      TargetRPS: 1500
      InitialRPS: 0
      StepHeight: 50
      StepDuration: 1s
    Method: ga.api.channel_live.ChannelLiveLogic.GetAnchorHonorNameplate
    Body: '{
      "base_req":   {}, 
      "actor_uid": 608024
    }'
#	masked-pk-logic	@周韶勇
  - Name: 30567 - 获取蒙面pk快捷送礼配置
    RateLimit:
      TargetRPS: 1500
      InitialRPS: 0
      StepHeight: 50
      StepDuration: 1s
    Method: ga.api.masked_pk.MaskedPKLogic.GetLiveQuickSendPresentConfig
    Body: '{
      "base_req":   {},
      "channel_id": 185060428
    }'
#	channel-lottery-logic	@周韶勇
  - Name: 30431 - 获取是否有抽奖功能
    RateLimit:
      TargetRPS: 1500
      InitialRPS: 0
      StepHeight: 50
      StepDuration: 1s
    Method: ga.api.channel_lottery.ChannelLotteryLogic.ShowChannelLotterySetting
    Body: '{
      "base_req":   {},
      "channel_id": 185060428
    }'
#	channel-live-logic	@李金安
  - Name: 3583 - 查询对应UID的直播房状态
    RateLimit:
      TargetRPS: 1500
      InitialRPS: 0
      StepHeight: 50
      StepDuration: 1s
    Method: ga.api.channel_live.ChannelLiveLogic.GetChannelLiveStatus
    Body: '{
      "base_req":   {}, 
      "channel_id": 185060428,
      "uid": 608024
    }'
  - Name: 3593 - 直播数据统计
    RateLimit:
      TargetRPS: 1500
      InitialRPS: 0
      StepHeight: 50
      StepDuration: 1s
    Method: ga.api.channel_live.ChannelLiveLogic.GetChannelLiveData
    Body: '{
      "base_req":   {}, 
      "channel_id": 185060428,
      "uid": 608024
    }'
#	channel-live-logic	@李金安
  - Name: 3571 - 获取PK信息
    RateLimit:
      TargetRPS: 1500
      InitialRPS: 0
      StepHeight: 50
      StepDuration: 1s
    Method: ga.api.channel_live.ChannelLiveLogic.GetPkInfo
    Body: '{
      "base_req":   {}, 
      "channel_id": 185060428,
      "anchor_uid": 608024
    }'
#	channel-live-logic	@李金安
  - Name: 3573 - 获取PK道具配置
    RateLimit:
      TargetRPS: 1500
      InitialRPS: 0
      StepHeight: 50
      StepDuration: 1s
    Method: ga.api.channel_live.ChannelLiveLogic.GetItemConfig
    Body: '{
      "base_req":   {}, 
      "item_id_list": []
    }'
#	channel-live-logic	@李金安
  - Name: 3603 - "每隔5分钟处理一次用户任务（用于相关观看时长的任务）"
    RateLimit:
      TargetRPS: 1500
      InitialRPS: 0
      StepHeight: 50
      StepDuration: 1s
    Method: ga.api.channel_live.ChannelLiveLogic.HandleUserMissionAtInterval
    Body: '{
      "base_req":   {}, 
      "channel_id": 185060428
    }'
#	masked-pk-logic	@周韶勇
  - Name: 30563 - 获取房间蒙面PK信息
    RateLimit:
      TargetRPS: 1500
      InitialRPS: 0
      StepHeight: 50
      StepDuration: 1s
    Method: ga.api.masked_pk.MaskedPKLogic.GetLiveChannelMaskedPKInfo
    Body: '{
      "base_req":   {},
      "channel_id": 185060428
    }'
TeardownSteps:
  - Name:           离开房间-424
    RateLimit:
      TargetRPS:    200
      InitialRPS:   0
      StepHeight:   50
      StepDuration: 1s
    Method:         ga.api.channel_core.ChannelCoreLogic.ChannelQuit
    Body:           '
{
    "channel_id": 185060428
}
'
