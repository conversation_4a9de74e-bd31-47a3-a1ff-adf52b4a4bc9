ParallelSteps:
  #	unclaimed-logic	@陈明宇
  - Name: 30171 - 获取扩展入口
    RateLimit:
      TargetRPS: 600
      InitialRPS: 0
      StepHeight: 50
      StepDuration: 1s
    Method: ga.api.unclaimed.UnclaimedLogic.GetExpandEntranceList
    Body: '
{
    "base_req": {}
}
'
  #	event-report-logic	@田文杰
  - Name: 90001 - 上报新用户统计的时间
    RateLimit:
      TargetRPS: 600
      InitialRPS: 0
      StepHeight: 50
      StepDuration: 1s
    Method: ga.api.event_report.EventReportLogic.RecordNewUserTimeCount
    Body: '
{
    "base_req": {},
    "channel_info": {"channel_type": 2, "second": 1730967587},
    "im_second": 1730967587,
    "dynamics_second": 1730967587
}
'