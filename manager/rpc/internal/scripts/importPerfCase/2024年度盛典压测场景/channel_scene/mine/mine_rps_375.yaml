ParallelSteps:
#	presentlogic	@赵亮
  - Name: 1166 - 获取用户的礼物信息
    RateLimit:
      TargetRPS: 1500
      InitialRPS: 0
      StepHeight: 50
      StepDuration: 1s
    Method:     ga.api.present.PresentLogic.PresentGetUserPresentInfo
    Body:           '
{
    "base_req": {},
    "target_uid": *********
}
'
#	present-go-logic	@赵亮
  - Name: 1190 - 获取用户活动礼物区域（礼物墙星座活动礼物区域）
    RateLimit:
      TargetRPS: 1500
      InitialRPS: 0
      StepHeight: 50
      StepDuration: 1s
    Method:     ga.api.present_go.PresentGoLogic.GetUserActPresentArea
    Body:           '
{
    "base_req": {},
    "uid": *********
}
'
#	accountlogic	@田文杰@韩杰
  - Name: 1201 - 获取用户相册
    RateLimit:
      TargetRPS: 1500
      InitialRPS: 0
      StepHeight: 50
      StepDuration: 1s
    Method:     ga.api.account_go.AccountGoLogic.GetPhotoAlbum
    Body:           '
{
    "base_req": {},
    "uid": *********
}
'
#	activitylogic	@李金安
  - Name: 1222 - 获取多用途活动入口配置信息
    RateLimit:
      TargetRPS: 1500
      InitialRPS: 0
      StepHeight: 50
      StepDuration: 1s
    Method:     ga.api.activity.ActivityLogic.GetMutiLocationActEntry
    Body:           '
{
    "base_req": {},
    "location_type": 1
}
'
#  	ugc-logic	@蒋海波@李文生
  - Name: 2575 - 获取个人主页游戏入口
    RateLimit:
      TargetRPS: 1500
      InitialRPS: 0
      StepHeight: 50
      StepDuration: 1s
    Method:     ga.api.content.ContentLogic.GetGameEntrance
    Body:           '
{
    "base_req": {},
    "page_owner_uid": *********
}
'
#	guild-go-logic	@陈剑平
  - Name: 837 - 获取成员的职位信息
    RateLimit:
      TargetRPS: 1500
      InitialRPS: 0
      StepHeight: 50
      StepDuration: 1s
    Method:     ga.api.guild_go.GuildGoLogic.GuildGetUidOfficialInfo
    Body:           '
{
    "base_req": {},
    "target_uid": *********
}
'