ParallelSteps:
  #	channel-member-logic	@陈伟贤@吴楚明
  - Name: 31012 - 获取用户房间vip统计
    RateLimit:
      TargetRPS: 1000
      InitialRPS: 0
      StepHeight: 50
      StepDuration: 1s
    Method: ga.api.channel_member.ChannelMemberLogic.ChannelGetChannelMemberVipStatistics
    Body: '
{
    "base_req": {},
    "uid": {{.uid}}
}
'
  #	game-card-logic	@冯灿威@马少鹏
  - Name: 31303 - 获取游戏卡配置
    RateLimit:
      TargetRPS: 1000
      InitialRPS: 0
      StepHeight: 50
      StepDuration: 1s
    Method: ga.api.game_card.GameCardLogic.GetAllGameCardConf
    Body: '
{
    "base_req": {}
}
'
