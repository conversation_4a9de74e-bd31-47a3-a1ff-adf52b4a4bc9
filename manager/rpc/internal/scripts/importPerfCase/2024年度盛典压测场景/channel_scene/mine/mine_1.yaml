SerialSteps:
#	account-go-logic	@田文杰@韩杰
  - Name: 27 - 获取用户详细信息
    RateLimit:
      TargetRPS: 375
      InitialRPS: 0
      StepHeight: 50
      StepDuration: 1s
    Method:     ga.api.account.AccountLogic.GetUserDetail
    Body:           '
{
    "base_req": {},
    "target_account": "*********"
}
'
#	accountlogic	@吴淑杰
  - Name: 233 - 获取用户官方认证列表
    RateLimit:
      TargetRPS: 375
      InitialRPS: 0
      StepHeight: 50
      StepDuration: 1s
    Method:     ga.api.account.AccountLogic.GetUserCertificationList
    Body:           '
{
    "base_req": {},
    "target_uid": *********,
    "request_type": 0
}
'
#	guild-go-logic	@陈剑平
  - Name: 837 - 获取成员的职位信息
    RateLimit:
      TargetRPS: 375
      InitialRPS: 0
      StepHeight: 50
      StepDuration: 1s
    Method:     ga.api.guild_go.GuildGoLogic.GuildGetUidOfficialInfo
    Body:           '
{
    "base_req": {},
    "target_uid": *********
}
'
#	presentlogic	@赵亮
  - Name: 1166 - 获取用户的礼物信息
    RateLimit:
      TargetRPS: 375
      InitialRPS: 0
      StepHeight: 50
      StepDuration: 1s
    Method:     ga.api.present.PresentLogic.PresentGetUserPresentInfo
    Body:           '
{
    "base_req": {},
    "target_uid": *********
}
'
#	presentlogic	@赵亮
  - Name: 1172 - 获取冠名礼物配置信息
    RateLimit:
      TargetRPS: 375
      InitialRPS: 0
      StepHeight: 50
      StepDuration: 1s
    Method:     ga.api.channel_recommend.ChannelRecommendLogic.GetChannelTagInfoService
    Body:           '
{
    "base_req": {},
    "channel_id": *********
}
'
#	present-go-logic	@赵亮
  - Name: 1190 - 获取用户活动礼物区域（礼物墙星座活动礼物区域）
    RateLimit:
      TargetRPS: 375
      InitialRPS: 0
      StepHeight: 50
      StepDuration: 1s
    Method:     ga.api.present_go.PresentGoLogic.GetUserActPresentArea
    Body:           '
{
    "base_req": {},
    "uid": *********
}
'
#	accountlogic	@田文杰@韩杰
  - Name: 1201 - 获取用户相册
    RateLimit:
      TargetRPS: 375
      InitialRPS: 0
      StepHeight: 50
      StepDuration: 1s
    Method:     ga.api.account.AccountLogic.GetPhotoAlbum
    Body:           '
{
    "base_req": {},
    "uid": *********
}
'
#	activitylogic	@李金安
  - Name: 1221 - 首充活动入口 是否出现的check
    RateLimit:
      TargetRPS: 375
      InitialRPS: 0
      StepHeight: 50
      StepDuration: 1s
    Method:     ga.api.activity.ActivityLogic.CheckFirstRechargeActEntry
    Body:           '
{
    "base_req": {}
}
'
#	activitylogic	@李金安
  - Name: 1222 - 获取多用途活动入口配置信息
    RateLimit:
      TargetRPS: 375
      InitialRPS: 0
      StepHeight: 50
      StepDuration: 1s
    Method:     ga.api.activity.ActivityLogic.GetMutiLocationActEntry
    Body:           '
{
    "base_req": {},
    "location_type": 1
}
'
