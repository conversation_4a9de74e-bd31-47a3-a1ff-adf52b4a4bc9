ParallelSteps:
  #    	backpack-logic-go	@马富达
  - Name: 2194 - 该命令字全量转发到CMD_NewGetUserFuncCardUse = 36902
    RateLimit:
      TargetRPS: 2000
      InitialRPS: 0
      StepHeight: 50
      StepDuration: 1s
    Method: ga.api.backpack.BackpackLogic.GetUserFuncCardUse
    Body: '
  {
      "base_req": {},
      "uid": {{.uid}}
  }
  '
  #      	profile-logic	@赵亮
  - Name: 32005 - 获取用户考核认证信息
    RateLimit:
      TargetRPS: 2000
      InitialRPS: 0
      StepHeight: 50
      StepDuration: 1s
    Method: ga.api.profile.ProfileLogic.GetUserExamineCert
    Body: '
{
    "base_req": {},
    "uid": {{.uid}}
}
'
