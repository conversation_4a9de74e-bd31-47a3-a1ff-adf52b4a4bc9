ParallelSteps:
#  	presentlogic	@赵亮
  - Name: 1172 - 获取冠名礼物配置信息
    RateLimit:
      TargetRPS: 3000
      InitialRPS: 0
      StepHeight: 50
      StepDuration: 1s
    Method: ga.api.channel_recommend.ChannelRecommendLogic.GetChannelTagInfoService
    Body: '
{
    "base_req": {},
    "channel_id": 185563030
}
'
  #	activitylogic	@林贤
  - Name: 39371 - 首充活动入口 是否出现的check
    RateLimit:
      TargetRPS: 3000
      InitialRPS: 0
      StepHeight: 50
      StepDuration: 1s
    Method: ga.api.tt_rev_common_logic.TTRevCommonLogic.GetNewRechargeActEntryInfo
    Body: '
{
    "base_req": {}
}
'
