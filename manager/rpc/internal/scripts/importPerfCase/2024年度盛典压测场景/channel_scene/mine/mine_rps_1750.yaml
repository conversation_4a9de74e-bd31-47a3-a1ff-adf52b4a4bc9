ParallelSteps:
  #		game-card-logic	@冯灿威@马少鹏
  - Name: 31304 - 获取用户保存的游戏卡
    RateLimit:
      TargetRPS: 7000
      InitialRPS: 0
      StepHeight: 50
      StepDuration: 2s
    Method: ga.api.game_card.GameCardLogic.GetGameCard
    Body: '
{
    "base_req": {},
    "target_uid": {{int .uid}}
}
'
  #  	authlogic	@田文杰
  - Name: 33000 - 获取新union_token
    RateLimit:
      TargetRPS: 7000
      InitialRPS: 0
      StepHeight: 50
      StepDuration: 2s
    Method: ga.api.auth.AuthCppLogic.RefreshUnionTokenService
    Body: '
{
    "base_req": {}
}
'
  #  	offer-room-logic	@郭灏@王群盛
  - Name: 96044 - 拍卖房资料卡关系信息
    RateLimit:
      TargetRPS: 7000
      InitialRPS: 0
      StepHeight: 50
      StepDuration: 2s
    Method: ga.api.offer_room.OfferRoom.OfferRoomCardInfo
    Body: '
{
    "base_req": {},
    "target_uid": {{int .uid}}
}
'


