ParallelSteps:
#	accountlogic	@马富达
  - Name: 233 - 获取用户官方认证列表
    RateLimit:
      TargetRPS: 4000
      InitialRPS: 0
      StepHeight: 50
      StepDuration: 2s
    Method:     ga.api.account.AccountLogic.GetUserCertificationList
    Body:           '
{
    "base_req": {},
    "target_uid": {{.uid}},
    "request_type": 0
}
'
#	ugc-logic	@蒋海波@李文生
  - Name: 2560 - 获取用户的ugc详情
    RateLimit:
      TargetRPS: 4000
      InitialRPS: 0
      StepHeight: 50
      StepDuration: 2s
    Method:     ga.api.ugc.UgcUserLogic.GetUserInfo
    Body:           '
{
    "base_req": {},
    "user_identifier": {"uid": {{.uid}} }
}
'
#	userrecommendlogic	@王有斌
  - Name: 5065 - 得到合约信息
    RateLimit:
      TargetRPS: 4000
      InitialRPS: 0
      StepHeight: 50
      StepDuration: 2s
    Method:     ga.api.user_recommend.UserRecommendLogic.GetContractInfo
    Body:           '
{
    "base_req": {},
    "actor_uid": {{.uid}}
}
'
#	fellow-logic	@吴世衍@曾幸华
  - Name: 30661 - 获取挚友名单
    RateLimit:
      TargetRPS: 4000
      InitialRPS: 0
      StepHeight: 50
      StepDuration: 2s
    Method:     ga.api.fellow.FellowLogic.GetFellowList
    Body:           '
{
    "base_req": {},
    "uid": {{.uid}}
}
'
#	personal-certification-logic	@蒋海波@李文生
  - Name: 31327 - 获取个人认证 ( 包括 乐窝主理人 ）
    RateLimit:
      TargetRPS: 4000
      InitialRPS: 0
      StepHeight: 50
      StepDuration: 2s
    Method:     ga.api.personal_certification.PersonalCertificationLogic.GetPersonalCertification
    Body:           '
{
    "base_req": {},
    "uid": {{.uid}}
}
'
# 	revenue-ext-game-logic	@林贤@周韶勇
  - Name: 36598 - 获取游戏榜单外显标识
    RateLimit:
      TargetRPS: 4000
      InitialRPS: 0
      StepHeight: 50
      StepDuration: 2s
    Method:     ga.api.revenue_ext_game.RevenueExtGameLogic.GetExtGameRankNameplate
    Body:           '
{
    "base_req": {},
    "uid": {{.uid}},
    "show_type": 2
}
'
#	head-dynamic-image-logic	@赵亮
  - Name: 96015 - 查询是否有动态头像权限
    RateLimit:
      TargetRPS: 4000
      InitialRPS: 0
      StepHeight: 50
      StepDuration: 2s
    Method:     ga.api.head_dynamic_image.HeadDynamicImageLogic.GetHeadDynamicImagePrivilege
    Body:           '
{
    "base_req": {}
}
'
#	offer-room-logic	@郭灏@王群盛
  - Name: 96041 - 获取关系列表
    RateLimit:
      TargetRPS: 4000
      InitialRPS: 0
      StepHeight: 50
      StepDuration: 2s
    Method:     ga.api.offer_room.OfferRoom.OfferRoomOfferingRelationships
    Body:           '
{
    "base_req": {},
    "target_uid": {{.uid}},
    "page_token": ""
}
'

