ParallelSteps:
#	account-go-logic	@田文杰@韩杰
  - Name: 27 - 获取用户详细信息
    RateLimit:
      TargetRPS: 5000
      InitialRPS: 0
      StepHeight: 50
      StepDuration: 2s
    Method:     ga.api.account_go.AccountGoLogic.GetUserDetail
    Body:           '
{
    "base_req": {},
    "target_account": "*********"
}
'
#	profile-logic	@赵亮
  - Name: 3402 - 用于获取用户当前佩戴的装饰品
    RateLimit:
      TargetRPS: 5000
      InitialRPS: 0
      StepHeight: 50
      StepDuration: 2s
    Method:     ga.api.profile.ProfileLogic.UserCurrDecoration
    Body:           '
{
    "base_req": {},
    "uid": {{.uid}},
    "typ": 1
}
'
#	user-tag-logic-go	@冯灿威@马少鹏
  - Name: 30634 - 新获取用户标签命令
    RateLimit:
      TargetRPS: 5000
      InitialRPS: 0
      StepHeight: 50
      StepDuration: 2s
    Method:     ga.api.user_tag_go.UserTagLogicGo.GetUserTagV2
    Body:           '
{
    "base_req": {},
    "target_uid": {{.uid}}
}
'
#	nameplate-logic	@郭灏
  - Name: 103060 - 获取用户铭牌
    RateLimit:
      TargetRPS: 5000
      InitialRPS: 0
      StepHeight: 50
      StepDuration: 2s
    Method:     ga.api.nameplate.NameplateLogic.GetUserNameplate
    Body:           '
{
    "base_req": {},
    "user_id": {{.uid}}
}
'
#	you-know-who-logic	@马富达
  - Name: 3792 - 指定uid获取神秘人用户信息
    RateLimit:
      TargetRPS: 5000
      InitialRPS: 0
      StepHeight: 50
      StepDuration: 2s
    Method:     ga.api.you_know_who.YouKnowWhoLogic.GetUKWUserProfile
    Body:           '
{
    "base_req": {},
    "uid": {{.uid}},
    "is_personal_homepage": true
}
'
#	numeric-logic	@李昊哲
  - Name: 31410 - 获取用户荣耀榜单
    RateLimit:
      TargetRPS: 5000
      InitialRPS: 0
      StepHeight: 50
      StepDuration: 2s
    Method:     ga.api.numeric.NumericLogic.GetUserGloryRank
    Body:           '
{
    "base_req": {},
    "uid": {{.uid}}
}
'
#	revenue-nameplate-logic	@马富达
  - Name: 3861 - 获取用户铭牌配置
    RateLimit:
      TargetRPS: 5000
      InitialRPS: 0
      StepHeight: 50
      StepDuration: 2s
    Method:     ga.api.revenue_nameplate.RevenueNameplateLogic.GetUserNameplateInfo
    Body:           '
{
    "base_req": {},
    "uid": {{.uid}}
}
'

