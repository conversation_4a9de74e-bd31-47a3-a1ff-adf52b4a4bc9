ParallelSteps:
#	channelextlogic	@陈伟贤@吴楚明
  - Name: 620 - 娱乐-语聊-获取轮播榜
    RateLimit:
      TargetRPS: 1000
      InitialRPS: 0
      StepHeight: 50
      StepDuration: 3s
    Method:     ga.api.channel_ext.ChannelExtLogic.GetCharmAndRichLableInfo
    Body:           '
    {
        "base_req": {},
        "info_limit": 1
    }
    '
#	channelconvenelogic	@陈伟贤@吴楚明
  - Name: 1151 - 获取收藏房间列表
    RateLimit:
      TargetRPS: 1000
      InitialRPS: 0
      StepHeight: 50
      StepDuration: 3s
    Method:     ga.api.channel_convene.ChannelConveneLogic.ChannelGetCollectionList
    Body:           '
    {
        "base_req": {},
        "request_type": 2
    }
    '
#	channelconvenelogic	@陈伟贤@吴楚明
  - Name: 1155 - 获取正在召集指定用户的频道
    RateLimit:
      TargetRPS: 1000
      InitialRPS: 0
      StepHeight: 50
      StepDuration: 3s
    Method:     ga.api.channel_convene.ChannelConveneLogic.ChannelGetUserConveneChannelList
    Body:           '
    {
        "base_req": {}
    }
    '
#	activitylogic	@李金安
  - Name: 2246 - 活动信息战况
    RateLimit:
      TargetRPS: 1000
      InitialRPS: 0
      StepHeight: 50
      StepDuration: 3s
    Method:     ga.api.activity.ActivityLogic.GetCommonWebActiveBreakingInfo
    Body:           '
    {
        "base_req": {}
    }
    '
#	realnameauth-go-logic	@田文杰@韩杰
  - Name: 2601 - 得到认证用户的身份证信息(是否成年等)
    RateLimit:
      TargetRPS: 1000
      InitialRPS: 0
      StepHeight: 50
      StepDuration: 3s
    Method:     ga.api.realnameauth_go.RealNameAuthGoLogic.GetUserIdentityInfo
    Body:           '
    {
        "base_req": {}
    }
    '