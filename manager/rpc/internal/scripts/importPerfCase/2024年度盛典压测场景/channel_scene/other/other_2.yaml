ParallelSteps:
#	ugc-logic	@蒋海波@李文生
  - Name: 2607 - 获取心情配置
    RateLimit:
      TargetRPS: 1000
      InitialRPS: 0
      StepHeight: 50
      StepDuration: 1s
    Method:     ga.api.content.ContentLogic.GetMoodConfig
    Body:           '
{
    "base_req": {}
}
'
#	topic-channel-logic-v2	@黄立尧@马少鹏
  - Name: 3071 - 获取热门玩法
    RateLimit:
      TargetRPS: 1000
      InitialRPS: 0
      StepHeight: 50
      StepDuration: 1s
    Method:     ga.api.topic_channel.TopicChannelLogic.GetGameLabels
    Body:           '
    {
        "base_req": {},
        "tab_id": 1
    }
    '
#	super-player-logic	@马富达
  - Name: 3700 - 获取超级会员配置
    RateLimit:
      TargetRPS: 1000
      InitialRPS: 0
      StepHeight: 50
      StepDuration: 1s
    Method:     ga.api.super_player.SuperPlayerLogic.GetSuperPlayerConf
    Body:           '
    {
        "base_req": {}
    }
    '
#	super-player-logic	@马富达
  - Name: 3701 - 获取超级会员信息
    RateLimit:
      TargetRPS: 1000
      InitialRPS: 0
      StepHeight: 50
      StepDuration: 1s
    Method:     ga.api.super_player.SuperPlayerLogic.GetSuperPlayerInfo
    Body:           '
    {
        "base_req": {},
        "super_player_uid": {{int .uid}}
    }
    '
