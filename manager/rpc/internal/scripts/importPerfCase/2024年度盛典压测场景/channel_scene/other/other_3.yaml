SerialSteps:
  #	super-player-dress-logic	@马富达
  - Name: 3751 - 获取超级会员装扮配置版本
    RateLimit:
      TargetRPS: 1000
      InitialRPS: 0
      StepHeight: 50
      StepDuration: 1s
    Method: ga.api.super_player_dress.SuperPlayerDressLogic.GetDressConfigMaxVersion
    Body: '
      {
          "base_req": {},
          "max_version": 1699857465,
          "dress_type": 1
      }
      '
  #	super-player-dress-logic	@马富达
  - Name: 3753 - 获取超级会员特别关心装扮
    RateLimit:
      TargetRPS: 1000
      InitialRPS: 0
      StepHeight: 50
      StepDuration: 1s
    Method: ga.api.super_player_dress.SuperPlayerDressLogic.GetUserCurrSpecialConcernDressId
    Body: '
  {
      "base_req": {},
      "uid": {{int .uid}}
  }
  '
  #	you-know-who-logic	@马富达
  - Name: 3791 - 获取神秘人信息
    RateLimit:
      TargetRPS: 1000
      InitialRPS: 0
      StepHeight: 50
      StepDuration: 1s
    Method: ga.api.you_know_who.YouKnowWhoLogic.GetUKWInfo
    Body: '
      {
          "base_req": {}
      }
      '
  #	usergrowlogic	@李金安
  - Name: 5069 - 获取贵族相关信息
    RateLimit:
      TargetRPS: 1000
      InitialRPS: 0
      StepHeight: 50
      StepDuration: 1s
    Method: ga.api.user_grow.UserGrowLogic.GetNobilityDetailInfo
    Body: '
      {
          "base_req": {},
          "uid": {{int .uid}}
      }
      '