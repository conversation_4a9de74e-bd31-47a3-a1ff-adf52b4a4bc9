SerialSteps:
#  	channel-recommend-logic	@李金安
  - Name: 5083 - 获取娱乐tab快速入口场景配置（SLA核心命令）
    RateLimit:
      TargetRPS: 500
      InitialRPS: 0
      StepHeight: 50
      StepDuration: 1s
    Method: ga.api.channel_recommend_go.ChannelRecommendLogicService.GetQuickEntryConfigV2Service
    Body: '
{
    "base_req": {}
}
'
#  	channel-recommend-logic	@李金安
  - Name: 5080 - 拿娱乐推荐房列表（SLA核心命令）
    RateLimit:
      TargetRPS: 500
      InitialRPS: 0
      StepHeight: 50
      StepDuration: 1s
    Method: ga.api.channel_recommend_go.ChannelRecommendLogicService.GetRecommendChannelsV2Service
    Body: '
{
    "base_req": {},
    "start": 0,
    "count": 10,
    "home_type": {{randInt 0 2}}
}
'