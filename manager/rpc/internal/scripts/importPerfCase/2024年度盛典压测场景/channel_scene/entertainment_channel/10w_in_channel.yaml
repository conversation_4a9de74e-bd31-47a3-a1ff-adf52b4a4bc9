SetupSteps:
  - Name:           进入房间-50858
    RateLimit:
      TargetRPS:    500
      InitialRPS:   0
      StepHeight:   50
      StepDuration: 1s
    Method:         ga.api.channel_core.ChannelCoreLogic.ChannelEnterV2
    Body:           '
{
    "channel_id": 185563011
}
'

SerialSteps:

  - Name:           发送公屏文本消息消息-434
    RateLimit:
      TargetRPS:    100
      InitialRPS:   0
      StepHeight:   10
      StepDuration: 1s
    Method:         ga.api.channel_im.ChannelImGoLogic.SendChannelTextMsg
    Body:           '
{
    "channel_id": 185563011,
    "content":    "{{nounZH}}",
    "type": 1
}
'
    Sleep:          10s

TeardownSteps:
  - Name:           离开房间-424
    RateLimit:
      TargetRPS:    500
      InitialRPS:   0
      StepHeight:   50
      StepDuration: 1s
    Method:         ga.api.channel_core.ChannelCoreLogic.ChannelQuit
    Body:           '
{
    "channel_id": 185563011
}
'

