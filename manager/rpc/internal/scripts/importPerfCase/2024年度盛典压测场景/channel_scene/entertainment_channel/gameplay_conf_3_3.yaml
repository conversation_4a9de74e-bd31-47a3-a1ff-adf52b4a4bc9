SetupSteps:
  - Name:           进入房间-50858
    RateLimit:
      TargetRPS:    200
      InitialRPS:   0
      StepHeight:   50
      StepDuration: 1s
    Method:         ga.api.channel_core.ChannelCoreLogic.ChannelEnterV2
    Body:           '
{
    "channel_id": 185563011
}
'
ParallelSteps:
    #	channel-smallgame-logic	@冯灿威@马少鹏
  - Name: 50605 - 获取“玩点什么”道具开关
    RateLimit:
      TargetRPS: 1500
      InitialRPS: 0
      StepHeight: 50
      StepDuration: 2s
    Method: ga.api.channel_smallgame.ChannelSmallGameLogic.GetPropConfig
    Body: '
{
    "base_req": {},
    "channel_id": 185563011
}
'
    #	channel-minigame-go-logic	@陈伟贤@林贤
  - Name: 50607 - 获取互动表情权限
    RateLimit:
      TargetRPS: 1500
      InitialRPS: 0
      StepHeight: 50
      StepDuration: 2s
    Method: ga.api.channel_minigame_go.ChannelMiniGameGoLogic.GetInteractionEmojiPrivilege
    Body: '
{
    "base_req": {}
}
'
    #	treasure-house-logic	@李昊哲@赵亮
  - Name: 50951 - 获取珍宝馆活动列表
    RateLimit:
      TargetRPS: 1500
      InitialRPS: 0
      StepHeight: 50
      StepDuration: 2s
    Method: ga.api.treasure_house.TreasureHouseLogic.GetTreasureActivityList
    Body: '
{
    "base_req": {}
}
'
    #	channel-smallgame-logic	@冯灿威@马少鹏
  - Name: 50600 - 获取当前数字炸弹游戏状态
    RateLimit:
      TargetRPS: 1500
      InitialRPS: 0
      StepHeight: 50
      StepDuration: 2s
    Method: ga.api.channel_smallgame.ChannelSmallGameLogic.GetNumBombStatus
    Body: '
{
    "base_req": {},
    "channel_id": 185563011
}
'
    #	channel-logic-go	@吴楚明
  - Name: 50859 - 拉取进房信息/扩展信息
    RateLimit:
      TargetRPS: 1500
      InitialRPS: 0
      StepHeight: 50
      StepDuration: 2s
    Method: ga.api.channel_logic_go.ChannelLogicGo.GetChannelEnterExtInfo
    Body: '
{
    "base_req": {},
    "enter_source": 1,
    "channel_id": 185563011,
    "is_knock_door": false
}
'
TeardownSteps:
  - Name:           离开房间-424
    RateLimit:
      TargetRPS:    200
      InitialRPS:   0
      StepHeight:   50
      StepDuration: 1s
    Method:         ga.api.channel_core.ChannelCoreLogic.ChannelQuit
    Body:           '
{
    "channel_id": 185563011
}
'