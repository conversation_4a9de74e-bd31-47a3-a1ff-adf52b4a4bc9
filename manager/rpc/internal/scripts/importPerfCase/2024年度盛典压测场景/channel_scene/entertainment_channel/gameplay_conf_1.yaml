SerialSteps:
  # @李金安
#  - Name: 5080 - 拿娱乐推荐房列表
#    RateLimit:
#      TargetRPS: 375
#      InitialRPS: 0
#      StepHeight: 50
#      StepDuration: 1s
#    Method: ga.api.channel_recommend_go.ChannelRecommendLogicService.GetRecommendChannelsV2Service
#
#    Body: '
#{
#    "base_req": {},
#    "start": 0,
#    "count": 10,
#    "home_type": {{randInt 0 2}}
#}
#'
 # @周韶勇
  - Name: 30036 - 拉取转转入口配置
    RateLimit:
      TargetRPS: 1500
      InitialRPS: 0
      StepHeight: 50
      StepDuration: 1s
    Method: ga.api.smash_egg.SmashEggLogic.GetConfig
    Body: '
{
    "base_req": {},
    "platform": {{randInt 1 2}},
    "channel_id": 185563011
}
'
    # @周韶勇
  - Name: 30037 - 获取转转奖池及兑换变更推
    RateLimit:
      TargetRPS: 1500
      InitialRPS: 0
      StepHeight: 50
      StepDuration: 1s
    Method: ga.api.smash_egg_notify.SmashEggNotifyLogic.GetNotify
    Body: '
{
    "base_req": {},
    "notify_type": {{randInt 1 4}}
}
'
    # @周韶勇
  - Name: 30558 - 获取蒙面pk快捷送礼配配
    RateLimit:
      TargetRPS: 1500
      InitialRPS: 0
      StepHeight: 50
      StepDuration: 1s
    Method: ga.api.masked_pk.MaskedPKLogic.GetQuickSendPresentConfig
    Body: '
{
    "base_req": {},
    "channel_id": 185563011
}
'
    # @吴楚明@陈伟贤
  - Name: 2064 - 房间播放器状态
    RateLimit:
      TargetRPS: 1500
      InitialRPS: 0
      StepHeight: 50
      StepDuration: 1s
    Method: ga.api.channel_music.ChannelMusicLogic.ChannelMusicV2GetStatus
    Body: '
{
    "base_req": {},
    "channel_id": 185563011
}
'
