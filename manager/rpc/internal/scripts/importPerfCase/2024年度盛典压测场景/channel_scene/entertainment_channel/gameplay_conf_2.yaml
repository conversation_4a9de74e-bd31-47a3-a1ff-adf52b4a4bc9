ParallelSteps:
  - Name: 2707 - 获取通用实时榜单配置
    RateLimit:
      TargetRPS: 1500
      InitialRPS: 0
      StepHeight: 50
      StepDuration: 1s
    Method: ga.api.activity.ActivityLogic.GetCommonRankingListConfig
    Body: '
{
    "base_req": {}
}
'
  - Name: 2195 - 获取用户的碎片
    RateLimit:
      TargetRPS: 1500
      InitialRPS: 0
      StepHeight: 50
      StepDuration: 1s
    Method: ga.api.backpack.BackpackLogic.GetUserFragment
    Body: '
{
    "base_req": {},
    "viewed_uid": 329661167
}
'
  - Name: 30080 - 查询 首页开黑tab广告位配置
    RateLimit:
      TargetRPS: 1500
      InitialRPS: 0
      StepHeight: 50
      StepDuration: 1s
    Method: ga.api.activity.ActivityLogic.GetGangupTabAdvConf
    Body: '
{
    "base_req": {},
    "channel_pack_tag": "",
    "adv_sort": 1
}
'

  - Name: 30438 - 获取抽奖信息
    RateLimit:
      TargetRPS: 1500
      InitialRPS: 0
      StepHeight: 50
      StepDuration: 1s
    Method: ga.api.channel_lottery.ChannelLotteryLogic.GetChannelLotteryInfo
    Body: '
{
    "base_req": {},
    "channel_id": 185563011
}
'
  - Name: 1177 - 获取涂鸦礼物的参数信息
    RateLimit:
      TargetRPS: 1500
      InitialRPS: 0
      StepHeight: 50
      StepDuration: 1s
    Method: ga.api.present.PresentLogic.GetDrawPresentParService
    Body: '
{
    "base_req": {}
}
'
