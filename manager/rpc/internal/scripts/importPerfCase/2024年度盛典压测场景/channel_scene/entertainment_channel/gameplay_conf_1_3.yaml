ParallelSteps:
    #	masked-pk-logic	@周韶勇
  - Name: 30552 - 获取房间蒙面PK信息
    RateLimit:
      TargetRPS: 1500
      InitialRPS: 0
      StepHeight: 50
      StepDuration: 1s
    Method: ga.api.masked_pk.MaskedPKLogic.GetChannelMaskedPKInfo
    Body: '{
      "base_req":   {},
      "channel_id": 185563011
    }'

    #	channel-minigame-go-logic	@赵亮
  - Name: 5005 - 获取房间投票信息
    RateLimit:
      TargetRPS: 1500
      InitialRPS: 0
      StepHeight: 50
      StepDuration: 1s
    Method: ga.api.channel_minigame_go.ChannelMiniGameGoLogic.ChannelVotePKGetInfo
    Body: '
{
    "base_req": {},
    "channel_id": 185563011
}
'
    #	ad-center-logic	@陈剑平
  - Name: 88888 - 获取广告信息
    RateLimit:
      TargetRPS: 1500
      InitialRPS: 0
      StepHeight: 50
      StepDuration: 1s
    Method: ga.api.ad_center.AdCenterLogic.BatchGetAd
    Body: '
{
    "base_req": {},
    "ad_id_list": [{{randInt 1 20}}],
    "channel_id": 185563011,
    "channel_type": 3
}
'
    #	channelgamelogic	@李金安
  - Name: 5014 - 获取画布开关
    RateLimit:
      TargetRPS: 1500
      InitialRPS: 0
      StepHeight: 50
      StepDuration: 1s
    Method: ga.api.channelgame.ChannelGameLogic.ChannelDrawGameGetBoardStatus
    Body: '
{
    "base_req": {},
    "channel_id": 185563011
}
'
    #	presentlogic	@赵亮
  - Name: 1172 - 获取冠名礼物配置信息
    RateLimit:
      TargetRPS: 1500
      InitialRPS: 0
      StepHeight: 100
      StepDuration: 1s
    Method: ga.api.present.PresentLogic.GetNamingPresentConfigList
    Body: '
{
    "base_req": {}
}
'