SetupSteps:
  - Name:           进入房间-50858
    RateLimit:
      TargetRPS:    100
      InitialRPS:   0
      StepHeight:   50
      StepDuration: 1s
    Method:         ga.api.channel_core.ChannelCoreLogic.ChannelEnterV2
    Body:           '
{
    "channel_id": 185563011
}
'
ParallelSteps:
    #	channel-performance-logic	@陈伟贤@吴楚明
  - Name: 30350 - 获取房间节目单
    RateLimit:
      TargetRPS: 1500
      InitialRPS: 0
      StepHeight: 50
      StepDuration: 1s
    Method: ga.api.channel_performance.ChannelPerformanceLogic.GetChannelPerformance
    Body: '
{
    "base_req": {},
    "channel_id": 185563011
}
'
  - Name: 1157 - 获取频道的召集信息
    RateLimit:
      TargetRPS: 1500
      InitialRPS: 0
      StepHeight: 50
      StepDuration: 1s
    Method: ga.api.channel_convene.ChannelConveneLogic.ChannelGetConveneInfo
    Body: '
{
    "base_req": {},
    "channel_id": 185563011
}
'
  - Name: 623 - 根据房间id获取房间小时榜排名信息
    RateLimit:
      TargetRPS: 1500
      InitialRPS: 0
      StepHeight: 50
      StepDuration: 1s
    Method: ga.api.channel_rank.ChannelRankLogic.ChannelHourRankById
    Body: '
{
    "base_req": {},
    "rank_type": 0,
    "channel_id": 185563011
}
'

  - Name: 30622 - 统一房间内显示功能判断接口
    RateLimit:
      TargetRPS: 1500
      InitialRPS: 0
      StepHeight: 50
      StepDuration: 1s
    Method: ga.api.unified_interface.UnifiedInterfaceLogic.UnifiedChannelShowStatus
    Body: '
{
    "base_req": {},
    "channel_type": 1,
    "channel_id": 185563011,
    "channel_role": 1
}
'
  - Name: 5603 - 随机取一个有boss的房间
    RateLimit:
      TargetRPS: 1500
      InitialRPS: 0
      StepHeight: 50
      StepDuration: 1s
    Method: ga.api.hunt_monster.HuntMonsterLogic.GetOneMonsterChannel
    Body: '
{
    "base_req": {}
}
'
  - Name: 32121 - 是否可见入口，以及浮层显示
    RateLimit:
      TargetRPS: 1500
      InitialRPS: 0
      StepHeight: 50
      StepDuration: 1s
    Method: ga.api.one_piece.OnePieceLogic.GetEntryAndNotifyInfo
    Body: '
{
    "base_req": {},
    "channel_id": 185563011
}
'
  - Name: 32141 - 星际巡航入口 、浮层信息
    RateLimit:
      TargetRPS: 1500
      InitialRPS: 0
      StepHeight: 50
      StepDuration: 1s
    Method: ga.api.star_trek.StarTrekLogic.StarTrekEntryAndNotify
    Body: '
{
    "base_req": {},
    "channel_id": 185563011
}
'
  - Name: 30952 - 获取房间当前红包列表
    RateLimit:
      TargetRPS: 1500
      InitialRPS: 0
      StepHeight: 50
      StepDuration: 1s
    Method: ga.api.channel_red_packet.ChannelRedPacketLogic.GetRedPacketList
    Body: '
{
    "base_req": {},
    "channel_id": 185563011
}
'
    #	treasure-house-logic	@李昊哲
  - Name: 50955 - 拉取珍宝馆更新消息
    RateLimit:
      TargetRPS: 1500
      InitialRPS: 0
      StepHeight: 50
      StepDuration: 1s
    Method: ga.api.treasure_house.TreasureHouseLogic.GetTreasureActivityUpdateInfo
    Body: '
{
    "base_req": {}
}
'
    #	magic-spirit-logic	@周韶勇
  - Name: 31154 - 获取房间待开箱列表
    RateLimit:
      TargetRPS: 1500
      InitialRPS: 0
      StepHeight: 50
      StepDuration: 1s
    Method: ga.api.magic_spirit.MagicSpiritLogic.GetChannelAllUnpackGift
    Body: '
{
    "base_req": {},
    "channel_id": 185563011
}
'
TeardownSteps:
  - Name:           离开房间-424
    RateLimit:
      TargetRPS:    100
      InitialRPS:   0
      StepHeight:   50
      StepDuration: 1s
    Method:         ga.api.channel_core.ChannelCoreLogic.ChannelQuit
    Body:           '
{
    "channel_id": 185563011
}
'
