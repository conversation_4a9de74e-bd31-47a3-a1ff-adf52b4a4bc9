SerialSteps:
  - Name:           拉取房间公屏消息-435
    RateLimit:
      TargetRPS:    1500
      InitialRPS:   0
      StepHeight:   10
      StepDuration: 1s
    Method:         ga.api.channel_im.ChannelImGoLogic.GetChannelMsg
    Body:           '
{
    "channel_id":   185563011,
    "begin_seq":    0,
    "end_seq":      10,
    "is_init":      true
}
'

  - Name:           拉取麦位列表-432
    RateLimit:
      TargetRPS:    1500
      InitialRPS:   0
      StepHeight:   10
      StepDuration: 1s
    Method:         ga.api.channel_mic.ChannelMicLogic.ChannelGetMicList
    Body:           '
{
    "channel_id": 185563011
}
'

  - Name:           获取排麦列表-2712
    RateLimit:
      TargetRPS:    1500
      InitialRPS:   0
      StepHeight:   10
      StepDuration: 1s
    Method:         ga.api.channel_live.ChannelLiveCppLogic.ChannelNormalQueueUpMicList
    Body:           '
{
    "base_req":     {},
    "channel_id":   185563011,
    "offset":       0,
    "limit":        20
}
'

  - Name:           获取房间扩展信息-2052
    RateLimit:
      TargetRPS:    1500
      InitialRPS:   0
      StepHeight:   10
      StepDuration: 1s
    Method:         ga.api.channel.ChannelLogic.ChannelGetExtendInfo
    Body:           '
{
    "base_req":     {},
    "channel_id":   185563011,
    "extend_type_bitmap": 3
}
'

  - Name:           获取房间背景信息-2020
    RateLimit:
      TargetRPS:    1500
      InitialRPS:   0
      StepHeight:   10
      StepDuration: 1s
    Method:         ga.api.channel_background.ChannelBackgroundLogic.GetCurChannelBackgroundInfo
    Body:           '
{
    "base_req":     {},
    "channel_id":   185563011
}
'

  - Name:           5058 - 根据channelId获取房间的标签信息
    RateLimit:
      TargetRPS:    1500
      InitialRPS:   0
      StepHeight:   10
      StepDuration: 1s
    Method:     ga.api.channel_recommend.ChannelRecommendLogic.GetChannelTagInfoService
    Body:           '
{
    "base_req":     {},
    "channel_id":   185563011
}
'

  - Name:           获取房间成员列表-426
    RateLimit:
      TargetRPS:    1500
      InitialRPS:   0
      StepHeight:   10
      StepDuration: 1s
    Method:         ga.api.channel_rank.ChannelRankLogic.ChannelGetMemberList
    Body:           '
{
    "base_req": {},
    "channel_id": 185563011,
    "begin_id": 0,
    "req_cnt": 50
}
'

  - Name:           获取歌曲列表-2060
    RateLimit:
      TargetRPS:    1500
      InitialRPS:   0
      StepHeight:   10
      StepDuration: 1s
    Method:         ga.api.channel_music.ChannelMusicLogic.ChannelMusicV2GetList
    Body:           '
{
    "base_req":     {},
    "channel_id":    185563011
}
'
