SetupSteps:
  - Name:           进入房间-50858
    RateLimit:
      TargetRPS:    100
      InitialRPS:   0
      StepHeight:   50
      StepDuration: 1s
    Method:         ga.api.channel_core.ChannelCoreLogic.ChannelEnterV2
    Body:           '
{
    "channel_id": 185563011
}
'
ParallelSteps:
    #	melee-channel-logic	@冯灿威@卢伟任
  - Name: 50016 - 上麦资格查询
    RateLimit:
      TargetRPS: 1500
      InitialRPS: 0
      StepHeight: 50
      StepDuration: 1s
    Method: ga.api.melee_channel.MeleeChannelLogic.GetSelfOnMicQualifications
    Body: '
{
    "base_req": {},
    "channel_id": 185563011
}
'
    #	playerlogic	@冯灿威@马少鹏
  - Name: 30141 - 发放玩伴
    RateLimit:
      TargetRPS: 1500
      InitialRPS: 0
      StepHeight: 50
      StepDuration: 1s
    Method: ga.api.player.PlayerLogic.PlayerProvided
    Body: '
{
    "base_req": {},
    "uid": 209042124,
    "page_no": 0,
    "type": 0,
    "room_type": 1,
    "get_count": 1,
    "never_into_channel": false,
    "channel_package_id": "",
    "is_player_provide_open": true,
    "force_type": 1,
    "in_room_duration": 60
}
'
    #	channel-lottery-logic	@周韶勇
  - Name: 30439 - 获取自定义礼物列表
    RateLimit:
      TargetRPS: 1500
      InitialRPS: 0
      StepHeight: 50
      StepDuration: 1s
    Method: ga.api.channel_lottery.ChannelLotteryLogic.SearchCustomGifts
    Body: '
{
    "base_req": {},
    "offset": 1,
    "limit": 10,
    "channel_id": 185563011
}
'
    #	channel-scheme-logic	@陈伟贤@吴楚明
  - Name: 33100 - 获取当前房间玩法信息
    RateLimit:
      TargetRPS: 1500
      InitialRPS: 0
      StepHeight: 50
      StepDuration: 1s
    Method: ga.api.channel_scheme.ChannelSchemeLogic.GetChannelSchemeInfo
    Body: '
{
    "base_req": {},
    "cid": 185563011
}
'
    #	pgc-channel-game-logic	@吴世衍@曾幸华
  - Name: 36351 - 获取PGC房小游戏列表
    RateLimit:
      TargetRPS: 1500
      InitialRPS: 0
      StepHeight: 50
      StepDuration: 1s
    Method: ga.api.pgc_channel_game.PgcChannelGameLogic.GetGameList
    Body: '
{
    "base_req": {},
    "channel_id": 185563011
}
'
    #	cat-canteen-logic	@林贤@周韶勇
  - Name: 36571 - 是否可见入口，以及浮层显示
    RateLimit:
      TargetRPS: 1500
      InitialRPS: 0
      StepHeight: 50
      StepDuration: 1s
    Method: ga.api.cat_canteen.CatCanteenLogic.GetChanceGameAccessNotifyInfo
    Body: '
{
    "base_req": {},
    "channel_id": 185563011
}
'
    #	glory-world-logic	@马富达
  - Name: 36611 - 获取荣耀世界入口信息
    RateLimit:
      TargetRPS: 1500
      InitialRPS: 0
      StepHeight: 50
      StepDuration: 1s
    Method: ga.api.glory_world.GloryWorldLogic.GetGloryEnterInfo
    Body: '
{
    "base_req": {}
}
'
TeardownSteps:
  - Name:           离开房间-424
    RateLimit:
      TargetRPS:    100
      InitialRPS:   0
      StepHeight:   50
      StepDuration: 1s
    Method:         ga.api.channel_core.ChannelCoreLogic.ChannelQuit
    Body:           '
{
    "channel_id": 185563011
}
'
