package migratecaserelationship

import (
	"context"
	"flag"
	"testing"

	"github.com/zeromicro/go-zero/core/conf"
	"github.com/zeromicro/go-zero/core/stores/sqlx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/config"
)

func TestMain(m *testing.M) {
	if !flag.Parsed() {
		flag.Parse()
	}

	m.Run()
}

func TestMigrateCaseRelationship(t *testing.T) {
	var c config.Config
	conf.MustLoad("../../../etc/manager.yaml", &c)
	// conf.MustLoad("./rpc/etc/manager.yaml", &c)

	ctx := context.Background()
	sqlConn := sqlx.NewMysql(c.DB.DataSource)
	acrrModel := model.NewApiCaseReferenceRelationshipModel(sqlConn, c.Cache)

	caseRelationships, err := acrrModel.FindNoCacheByQuery(
		ctx,
		acrrModel.SelectBuilder().Where("`id` > ? AND `id` < ?", 3626, 5000),
	)
	if err != nil {
		t.Fatal(err)
	}

	asrrModel := model.NewApiSuiteReferenceRelationshipModel(sqlConn, c.Cache)
	for _, caseRelationship := range caseRelationships {
		_, err = asrrModel.Insert(
			ctx,
			nil,
			&model.ApiSuiteReferenceRelationship{
				ProjectId:     caseRelationship.ProjectId,
				ReferenceType: "API_CASE",
				ReferenceId:   caseRelationship.CaseId,
				SuiteId:       caseRelationship.ReferenceId,
				CreatedBy:     caseRelationship.CreatedBy,
				UpdatedBy:     caseRelationship.UpdatedBy,
			},
		)
		if err != nil {
			t.Fatal(err)
		}
	}
}
