package setCategoryIdForTestPlan

import (
	"context"
	"flag"
	"net/http"
	"testing"

	"github.com/zeromicro/go-zero/core/conf"
	"github.com/zeromicro/go-zero/core/stores/sqlx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/config"
)

var (
	host  = flag.String("host", "", "Quality Platform Host")
	token = flag.String("token", "", "Quality Platform Token")
)

func TestMain(m *testing.M) {
	if !flag.Parsed() {
		flag.Parse()
	}

	m.Run()
}

func TestSetCategoryIdForTestPlan(t *testing.T) {
	if *host == "" || *token == "" {
		t.Fatal("please set quality platform host and token")
	}

	var c config.Config
	// conf.MustLoad("../../../etc/manager.yaml", &c)
	conf.MustLoad("./rpc/etc/manager.yaml", &c)

	ctx := context.Background()
	sqlConn := sqlx.NewMysql(c.DB.DataSource)
	categoryModel := model.NewCategoryModel(sqlConn, c.Cache)

	client := &http.Client{}
	apiURL := "https://" + *host + "/manager/v1/category/tree/get"

	apiPlanModel := model.NewApiPlanModel(sqlConn, c.Cache)
	apiPlans, err := apiPlanModel.FindNoCacheByQuery(
		ctx,
		apiPlanModel.SelectBuilder(),
	)
	if err != nil {
		t.Fatal(err)
	}

	apcm := make(map[string]string)
	for _, apiPlan := range apiPlans {
		if len(apiPlan.CategoryId) > 0 {
			continue
		}

		if _, ok := apcm[apiPlan.ProjectId]; !ok {
			query := "?category_id=&depth=0&include_self=true&type=API_PLAN&project_id=" + apiPlan.ProjectId
			req, err := http.NewRequest("GET", apiURL+query, nil)
			if err != nil {
				t.Fatal(err)
			}
			t.Logf("url: %s\n", req.URL.String())

			req.Header.Add("X-AUTH", *token)

			resp, err := client.Do(req)
			if err != nil {
				t.Fatal(err)
			}
			resp.Body.Close()

			category, err := categoryModel.FindOneByProjectIdTypeName(ctx, apiPlan.ProjectId, "API_PLAN", "全部计划")
			if err != nil {
				t.Fatal(err)
			}
			apcm[apiPlan.ProjectId] = category.CategoryId
			t.Logf("project_id: %s | category_id: %s\n", apiPlan.ProjectId, category.CategoryId)
		}

		apiPlan.CategoryId = apcm[apiPlan.ProjectId]
		_, err := apiPlanModel.UpdateTX(ctx, sqlConn, apiPlan)
		if err != nil {
			t.Fatal(err)
		}
	}

	uiPlanModel := model.NewUiPlanModel(sqlConn, c.Cache)
	uiPlans, err := uiPlanModel.FindNoCacheByQuery(
		ctx,
		uiPlanModel.SelectBuilder(),
	)
	if err != nil {
		t.Fatal(err)
	}

	upcm := make(map[string]string)
	for _, uiPlan := range uiPlans {
		if len(uiPlan.CategoryId) > 0 {
			continue
		}

		if _, ok := upcm[uiPlan.ProjectId]; !ok {
			query := "?category_id=&depth=0&include_self=true&type=UI_PLAN&project_id=" + uiPlan.ProjectId
			req, err := http.NewRequest("GET", apiURL+query, nil)
			if err != nil {
				t.Fatal(err)
			}
			t.Logf("url: %s\n", req.URL.String())

			req.Header.Add("X-AUTH", *token)

			resp, err := client.Do(req)
			if err != nil {
				t.Fatal(err)
			}
			resp.Body.Close()

			category, err := categoryModel.FindOneByProjectIdTypeName(ctx, uiPlan.ProjectId, "UI_PLAN", "全部计划")
			if err != nil {
				t.Fatal(err)
			}
			upcm[uiPlan.ProjectId] = category.CategoryId
			t.Logf("project_id: %s | category_id: %s\n", uiPlan.ProjectId, category.CategoryId)
		}

		uiPlan.CategoryId = upcm[uiPlan.ProjectId]
		_, err := uiPlanModel.UpdateTX(ctx, sqlConn, uiPlan)
		if err != nil {
			t.Fatal(err)
		}
	}
}
