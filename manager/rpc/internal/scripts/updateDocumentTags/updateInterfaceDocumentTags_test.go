package updateDocumentTags

import (
	"context"
	"database/sql"
	_ "embed"
	"encoding/csv"
	"flag"
	"strings"
	"testing"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/conf"
	"github.com/zeromicro/go-zero/core/jsonx"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zyedidia/generic"
	"github.com/zyedidia/generic/set"
	"golang.org/x/exp/slices"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/userinfo"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/config"
	tagservicelogic "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/logic/tagservice"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
	relationpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/relation/rpc/pb"
)

var (
	//go:embed project_app_mapping.csv
	mappingContent string

	projectID = flag.String("project_id", "", "project id")

	oldFeatureTeams = []string{"营收组", "TT-业务平台", "手游社交组", "兴趣内容组", "谜境"}
)

type mappingData struct {
	// ProbeServiceName    string `json:"probe_service_name"`
	// SentinelServiceName string `json:"sentinel_service_name"`
	// FeatureTeamName     string `json:"feature_team_name"`
	AppName     string `json:"app_name"`
	ProjectName string `json:"project_name"`
}

func TestMain(m *testing.M) {
	if !flag.Parsed() {
		flag.Parse()
	}

	m.Run()
}

func TestUpdateInterfaceDocumentTags(t *testing.T) {
	if *projectID == "" {
		t.Fatal("not set the `project_id`")
	}

	var c config.Config
	conf.MustLoad("./rpc/etc/manager.yaml", &c)

	ctx := userinfo.WithContext(
		context.Background(), &userinfo.UserInfo{
			Account:  "T1704",
			Fullname: "韩子健",
			DeptName: "平台研发组",
			Email:    "<EMAIL>",
		},
	)
	svcCtx := svc.NewServiceContext(c)
	createTagLogic := tagservicelogic.NewCreateTagLogic(ctx, svcCtx)

	reader := csv.NewReader(strings.NewReader(mappingContent))
	records, err := reader.ReadAll()
	if err != nil {
		t.Fatalf("failed to read the csv file, error: %+v", err)
	}

	mapping := make(map[string]mappingData, len(records)-1)
	for i, record := range records {
		if i == 0 {
			continue
		}

		if len(record) < 2 || record[0] == "" || record[1] == "" {
			t.Logf("skip the record: %s", jsonx.MarshalIgnoreError(record))
			continue
		}

		mapping[record[0]] = mappingData{
			AppName:     record[0],
			ProjectName: record[1],
		}
	}
	t.Logf("the number of records: %d", len(mapping))

	documents, err := svcCtx.InterfaceDocumentModel.FindAll(ctx, *projectID)
	if err != nil {
		t.Fatalf("failed to find documents, project_id: %s, error: %+v", *projectID, err)
	}

	var (
		success = 0
		failed  = 0
	)
	for _, document := range documents {
		data, ok := mapping[document.Service.String]
		if !ok {
			method, err := getMethodFromDocument(document)
			if err != nil {
				t.Error(err)
				continue
			}

			service, err := getServiceByMethod(ctx, svcCtx, method)
			if err != nil {
				t.Error(err)
				continue
			}

			data, ok = mapping[service]
			if !ok {
				t.Logf("not found the service in mapping, service: %s", service)
				continue
			}
		}

		var tags []string
		if document.Tags.Valid && document.Tags.String != "" {
			if err = jsonx.UnmarshalFromString(document.Tags.String, &tags); err != nil {
				failed += 1
				t.Errorf("failed to unmarshal the tags, tags: %s, error: %+v", document.Tags.String, err)
				continue
			}
		}

		tagsSet := set.NewHashset[string](
			constants.ConstDefaultMakeSliceSize, generic.Equals[string], generic.HashString,
		)
		for _, tag := range tags {
			if !slices.Contains(oldFeatureTeams, tag) {
				tagsSet.Put(tag)
			}
		}
		tagsSet.Put(data.ProjectName)

		tag := jsonx.MarshalToStringIgnoreError(tagsSet.Keys())
		t.Logf(
			"document: %s, service: %s - %s, tags: %s => %s",
			document.Name, data.AppName, data.ProjectName, document.Tags.String, tag,
		)
		document.Tags = sql.NullString{
			String: tag,
			Valid:  tag != "",
		}

		if err = svcCtx.InterfaceDocumentModel.Trans(
			ctx, func(context context.Context, session sqlx.Session) error {
				if _, err = svcCtx.InterfaceDocumentModel.UpdateTX(context, session, document); err != nil {
					return err
				}

				if err = createTagLogic.CreateTagAndReferenceForInternal(
					context, session, types.CreateOrUpdateTagReference{
						ProjectId:     document.ProjectId,
						ReferenceType: common.ConstReferenceTypeInterfaceDocument,
						ReferenceId:   document.DocumentId,
						Tags:          tagsSet.Keys(),
					},
				); err != nil {
					return err
				}

				return nil
			},
		); err != nil {
			failed += 1
			t.Errorf(
				"failed to update the document, document_id: %s, name: %s, tags: %s, error: %+v",
				document.DocumentId, document.Name, tag, err,
			)
			continue
		} else {
			success += 1
		}
	}

	t.Logf("update the tags of documents completed, success: %d, failed: %d", success, failed)
}

func getMethodFromDocument(document *model.InterfaceDocument) (string, error) {
	item := &pb.Document{}
	if err := protobuf.UnmarshalJSONFromString(document.Data, item); err != nil {
		return "", errors.Errorf(
			"failed to unmarshal the document data, document_id: %s, name: %s, error: %+v",
			document.DocumentId, document.Name, err,
		)
	}

	properties := item.GetBody().GetJson().GetProperties()
	if properties == nil {
		return "", errors.Errorf(
			"no properties in the document, document_id: %s, name: %s", document.DocumentId, document.Name,
		)
	}

	schema, ok := properties["method"]
	if !ok {
		return "", errors.Errorf(
			"no `method` in the document properties, document_id: %s, name: %s", document.DocumentId, document.Name,
		)
	}

	method := schema.GetDefault().GetStringValue()
	if len(method) == 0 {
		return "", errors.Errorf("the method is empty, document_id: %s, name: %s", document.DocumentId, document.Name)
	}

	return method, nil
}

func getServiceByMethod(ctx context.Context, svcCtx *svc.ServiceContext, method string) (string, error) {
	out, err := svcCtx.RelationRPC.GetServiceByMethod(
		ctx, &relationpb.GetServiceByMethodReq{
			Method: method,
		},
	)
	if err != nil {
		return "", err
	}

	if len(out.GetRelations()) == 0 {
		return "", errors.Errorf("no service found, method: %s", method)
	}

	service := out.GetRelations()[0].GetService()
	if len(service) == 0 {
		return "", errors.Errorf("the service is empty, method: %s", method)
	}

	return service, nil
}
