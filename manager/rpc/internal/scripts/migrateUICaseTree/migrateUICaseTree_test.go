package migrateUICaseTree

import (
	"context"
	"sort"
	"testing"

	"github.com/zeromicro/go-zero/core/conf"
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/stores/sqlx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/config"
)

func TestMigrateUICaseTree(t *testing.T) {
	var c config.Config
	conf.MustLoad("./rpc/etc/manager.yaml", &c)

	ctx := context.Background()
	sqlConn := sqlx.NewMysql(c.DB.DataSource)
	uiPlanModel := model.NewUiPlanModel(sqlConn, c.Cache)
	uiPlanReferenceRelationshipModel := model.NewUiPlanReferenceRelationshipModel(sqlConn, c.Cache)
	uiCaseTreeModel := model.NewUiCaseTreeModel(sqlConn, c.Cache)

	data, err := uiCaseTreeModel.FindNoCacheByQuery(ctx, uiCaseTreeModel.SelectBuilder())
	if err != nil {
		t.Fatalf("svcCtx.UiCaseTreeModel.FindNoCacheByQuery: %+v", err)
	}

	cache := make(map[string]*model.UiPlan)
	records := make([]*model.UiPlanReferenceRelationship, 0, len(data))

	for _, d := range data {
		if d.Type != string(common.ConstNodeTypeFunction) {
			continue
		}

		item := &model.UiPlanReferenceRelationship{
			ProjectId: d.ProjectId,
			Path:      d.Path,
			PlanId:    d.PlanId,
			Deleted:   d.Deleted,
			CreatedBy: d.CreatedBy,
			UpdatedBy: d.UpdatedBy,
			DeletedBy: d.DeletedBy,
			CreatedAt: d.CreatedAt,
			UpdatedAt: d.UpdatedAt,
			DeletedAt: d.DeletedAt,
		}

		if v, ok := cache[d.ProjectId+":"+d.PlanId]; !ok {
			plan, err := uiPlanModel.FindOneByProjectIdPlanId(ctx, d.ProjectId, d.PlanId)
			if err != nil {
				t.Fatalf("svcCtx.UiPlanModel.FindOneByProjectIdPlanId: %+v", err)
			}

			item.GitConfigId = plan.GitConfigId
			cache[d.ProjectId+":"+d.PlanId] = plan
		} else {
			item.GitConfigId = v.GitConfigId
		}

		records = append(records, item)
	}

	sort.SliceStable(
		records, func(i, j int) bool {
			if records[i].ProjectId != records[j].ProjectId {
				return records[i].ProjectId > records[j].ProjectId
			} else if records[i].PlanId != records[j].PlanId {
				return records[i].PlanId > records[j].PlanId
			} else if records[i].Path != records[j].Path {
				return records[i].Path > records[j].Path
			} else if !records[i].CreatedAt.Equal(records[j].CreatedAt) {
				return records[i].CreatedAt.Before(records[j].CreatedAt)
			}

			return false
		},
	)

	logx.Infof("the number of migrated records: %d", len(records))
	if err = uiPlanReferenceRelationshipModel.Trans(
		ctx, func(context context.Context, session sqlx.Session) error {
			stmt, _, err := uiPlanReferenceRelationshipModel.InsertBuilder(&model.UiPlanReferenceRelationship{}).ToSql()
			if err != nil {
				return err
			}

			inserter, err := sqlx.NewBulkInserter(sqlConn, stmt)
			if err != nil {
				return err
			}

			for _, record := range records {
				_, args, err := uiPlanReferenceRelationshipModel.InsertBuilder(record).ToSql()
				if err != nil {
					return err
				}

				if err = inserter.Insert(args...); err != nil {
					return err
				}
			}

			inserter.Flush()

			return nil
		},
	); err != nil {
		t.Fatalf("svcCtx.UiPlanReferenceModel.Insert: %+v", err)
	}
}
