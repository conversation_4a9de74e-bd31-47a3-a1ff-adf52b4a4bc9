package v1_17_13

import (
	"context"
	"database/sql"
	"flag"
	"testing"
	"time"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/conf"
	"github.com/zeromicro/go-zero/core/jsonx"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/userinfo"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/utils"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/config"
)

var projectID = flag.String("project_id", "", "project id")

func TestMain(m *testing.M) {
	if !flag.Parsed() {
		flag.Parse()
	}

	m.Run()
}

func TestUpdateInterfaceDocumentTags(t *testing.T) {
	var c config.Config
	conf.MustLoad("./rpc/etc/manager.yaml", &c)

	ctx := context.Background()
	sqlConn := sqlx.NewMysql(c.DB.DataSource)
	documentModel := model.NewInterfaceDocumentModel(sqlConn, c.Cache)
	caseModel := model.NewInterfaceCaseModel(sqlConn, c.Cache)
	tagModel := model.NewTagModel(sqlConn, c.Cache)
	tagRefModel := model.NewTagReferenceRelationshipModel(sqlConn, c.Cache)

	if *projectID == "" {
		t.Fatal("not set the `project_id`")
	}

	ctx = userinfo.WithContext(
		ctx, &userinfo.UserInfo{
			Account:  "T1704",
			Fullname: "韩子健",
			DeptName: "平台研发组",
			Email:    "<EMAIL>",
		},
	)

	documents, err := documentModel.FindAll(ctx, *projectID)
	if err != nil {
		t.Fatalf("model.InterfaceDocumentModel FindAll: %+v", err)
	}

	for i, document := range documents {
		cases, err := caseModel.FindLatestByDocumentId(ctx, document.ProjectId, document.DocumentId)
		if err != nil {
			t.Errorf(
				"model.InterfaceCaseModel FindLatestByDocumentId, document_id: %s, name: %s, error: %+v",
				document.DocumentId, document.Name, err,
			)
			continue
		}

		var allTags []string
		if document.Tags.Valid && document.Tags.String != "" {
			if err = jsonx.UnmarshalFromString(document.Tags.String, &allTags); err != nil {
				t.Errorf(
					"jsonx.UnmarshalFromString, document_id: %s, name: %s, tags: %s, error: %+v",
					document.DocumentId, document.Name, document.Tags.String, err,
				)
			}
		}

		for _, _case := range cases {
			if !_case.Tags.Valid || _case.Tags.String == "" {
				continue
			}

			var caseTags []string
			if err = jsonx.UnmarshalFromString(_case.Tags.String, &caseTags); err != nil {
				t.Errorf(
					"jsonx.UnmarshalFromString, document_id: %s, name: %s, case_id: %s, case_name: %s, case_tags: %s, error: %+v",
					document.DocumentId, document.Name, _case.CaseId, _case.Name, _case.Tags.String, err,
				)
				continue
			}

			for _, tag := range caseTags {
				if tag == "" {
					continue
				}

				allTags = append(allTags, tag)
			}
		}

		if _, err = tagRefModel.RemoveByReferenceId(
			ctx, nil, document.ProjectId, common.ConstReferenceTypeInterfaceDocument, document.DocumentId, "",
		); err != nil {
			t.Errorf(
				"model.TagReferenceRelationshipModel RemoveByReferenceId, document_id: %s, name: %s, error: %+v",
				document.DocumentId, document.Name, err,
			)
		}

		allTags = stringx.Distinct(allTags)
		tags := make([]string, 0, len(allTags))
		for _, tag := range allTags {
			var tagID string
			_tag, err := tagModel.FindOneByProjectIdTypeName(
				ctx, document.ProjectId, common.ConstTagTypeInterfaceDocument, tag,
			)
			if err != nil {
				if !errors.Is(err, model.ErrNotFound) {
					t.Errorf(
						"model.TagModel FindOneByProjectIdTypeName, type: %s, tag: %s, error: %+v",
						common.ConstTagTypeInterfaceDocument, tag, err,
					)
					continue
				}

				tagID = utils.GenTagId()
				if _, err = tagModel.InsertTX(
					ctx, nil, &model.Tag{
						ProjectId: document.ProjectId,
						Type:      common.ConstTagTypeInterfaceDocument,
						TagId:     tagID,
						Name:      tag,
						Status:    int64(constants.EnableStatus),
						Deleted:   0,
						CreatedBy: document.CreatedBy,
						UpdatedBy: document.UpdatedBy,
					},
				); err != nil {
					t.Errorf(
						"model.TagModel InsertTX, type: %s, tag: %s, error: %+v",
						common.ConstTagTypeInterfaceDocument, tag, err,
					)
					continue
				}
			} else {
				tagID = _tag.TagId
			}

			if tagID != "" {
				if _, err = tagRefModel.Insert(
					ctx, nil, &model.TagReferenceRelationship{
						ProjectId:     document.ProjectId,
						ReferenceType: common.ConstReferenceTypeInterfaceDocument,
						ReferenceId:   document.DocumentId,
						TagId:         tagID,
						CreatedBy:     document.CreatedBy,
						UpdatedBy:     document.UpdatedBy,
						CreatedAt:     time.Now(),
						UpdatedAt:     time.Now(),
					},
				); err != nil {
					t.Errorf(
						"model.TagReferenceRelationshipModel Insert, ref_type: %s, ref_id: %s, tag_id: %s, error: %+v",
						common.ConstReferenceTypeInterfaceDocument, document.DocumentId, tagID, err,
					)
					continue
				}
			}

			tags = append(tags, tag)
		}

		tag := jsonx.MarshalToStringIgnoreError(tags)
		t.Logf("%d: document tags: %s => %s", i, document.Tags.String, tag)
		document.Tags = sql.NullString{
			String: tag,
			Valid:  tag != "",
		}
		if _, err = documentModel.UpdateTX(ctx, nil, document); err != nil {
			t.Errorf(
				"model.InterfaceDocumentModel UpdateTX, document_id: %s, name: %s, tags: %s, error: %+v",
				document.DocumentId, document.Name, tag, err,
			)
			continue
		}
	}
}
