package setAppDownloadLinkToNull

import (
	"context"
	"flag"
	"testing"

	"github.com/zeromicro/go-zero/core/conf"
	"github.com/zeromicro/go-zero/core/lang"
	"github.com/zeromicro/go-zero/core/stores/sqlx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/config"
)

var projectID = flag.String("project_id", "", "project id")

func TestMain(m *testing.M) {
	if !flag.Parsed() {
		flag.Parse()
	}

	m.Run()
}

func TestSetAppDownloadLinkToNull(t *testing.T) {
	if *projectID == "" {
		t.Fatal("please set `project_id` belongs to ui plans")
	}

	var c config.Config
	// conf.MustLoad("../../../etc/manager.yaml", &c)
	conf.MustLoad("./rpc/etc/manager.yaml", &c)

	ctx := context.Background()
	sqlConn := sqlx.NewMysql(c.DB.DataSource)
	uiPlanModel := model.NewUiPlanModel(sqlConn, c.Cache)

	uiPlans, err := uiPlanModel.FindNoCacheByQuery(
		ctx,
		uiPlanModel.SelectBuilder().Where("`project_id` = ?", *projectID),
	)
	if err != nil {
		t.Fatalf("fetch ui plans failed, error: %+v\n", err)
	}

	packageNames := map[string]lang.PlaceholderType{
		"com.yiyou.ga":            lang.Placeholder,
		"com.yiyou.enterprise.tt": lang.Placeholder,
	}

	for _, uiPlan := range uiPlans {
		if len(uiPlan.AppDownloadLink) == 0 {
			t.Logf("app_downlink_link has been empty, plan_name: %s, plan_id: %s\n", uiPlan.Name, uiPlan.PlanId)
			continue
		}
		if _, ok := packageNames[uiPlan.PackageName]; !ok {
			t.Logf("unsupport package name: %s, plan_name: %s, plan_id: %s\n", uiPlan.PackageName, uiPlan.Name, uiPlan.PlanId)
			continue
		}
		uiPlan.AppDownloadLink = ""
		_, err := uiPlanModel.UpdateTX(ctx, sqlConn, uiPlan)
		if err != nil {
			t.Fatalf("update app_download_link to empty failed, plan_name: %s, plan_id: %s, error: %+v\n", uiPlan.Name, uiPlan.PlanId, err)
		}
	}
}
