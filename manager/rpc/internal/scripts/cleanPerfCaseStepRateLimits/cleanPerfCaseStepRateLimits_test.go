package cleanPerfCaseStepRateLimits

import (
	"context"
	"flag"
	"testing"

	"github.com/zeromicro/go-zero/core/conf"
	"github.com/zeromicro/go-zero/core/stores/sqlx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/config"
)

var projectID = flag.String("project_id", "", "project id")

func TestMain(m *testing.M) {
	if !flag.Parsed() {
		flag.Parse()
	}

	m.Run()
}

func TestCleanPerfCaseStepRateLimits(t *testing.T) {
	var c config.Config
	// conf.MustLoad("../../../etc/manager.yaml", &c)
	conf.MustLoad("./rpc/etc/manager.yaml", &c)

	ctx := context.Background()
	sqlConn := sqlx.NewMysql(c.DB.DataSource)
	caseModel := model.NewPerfCaseV2Model(sqlConn, c.Cache)
	stepModel := model.NewPerfCaseStepV2Model(sqlConn, c.Cache)

	if *projectID == "" {
		t.Fatal("not set the `project_id`")
	}

	steps, err := stepModel.FindNoCacheByQuery(
		ctx, stepModel.SelectBuilder().Where(
			"`project_id` = ? AND `type` IN (?, ?)",
			projectID, common.ConstPerfCaseStepTypeSerial, common.ConstPerfCaseStepTypeParallel,
		),
	)
	if err != nil {
		t.Fatal(err)
	}
	t.Logf("steps len: %d", len(steps))

	for _, stepItem := range steps {
		if stepItem.RateLimits.Valid {
			stepItem.RateLimits.Valid = false
			stepItem.UpdatedBy = "T5210"

			stepModel.Trans(
				ctx, func(ctx context.Context, session sqlx.Session) error {
					if _, err := stepModel.Update(ctx, session, stepItem); err != nil {
						t.Errorf("update perf case step[%s] failed: %v", stepItem.StepId, err)
						return err
					}
					return nil
				},
			)
		}
	}

	cases, err := caseModel.FindNoCacheByQuery(ctx, caseModel.SelectBuilder().Where("`project_id` = ?", projectID))
	if err != nil {
		t.Fatal(err)
	}
	t.Logf("cases len: %d", len(cases))

	for _, caseItem := range cases {
		var updated bool

		if caseItem.SerialSteps.Valid {
			var serialSteps []*pb.PerfCaseStepV2
			if err := protobuf.UnmarshalJSONWithMessagesFromString(
				caseItem.SerialSteps.String, &serialSteps,
			); err != nil {
				t.Fatal(err)
			}

			for _, serialStep := range serialSteps {
				if serialStep.RateLimits != nil {
					serialStep.RateLimits = nil
				}
			}

			caseItem.SerialSteps.String = protobuf.MarshalJSONWithMessagesToStringIgnoreError(serialSteps)
			updated = true
		}

		if caseItem.ParallelSteps.Valid {
			var parallelSteps []*pb.PerfCaseStepV2
			if err := protobuf.UnmarshalJSONWithMessagesFromString(
				caseItem.ParallelSteps.String, &parallelSteps,
			); err != nil {
				t.Fatal(err)
			}

			for _, parallelStep := range parallelSteps {
				if parallelStep.RateLimits != nil {
					parallelStep.RateLimits = nil
				}
			}

			caseItem.ParallelSteps.String = protobuf.MarshalJSONWithMessagesToStringIgnoreError(parallelSteps)
			updated = true
		}

		if updated {
			caseItem.UpdatedBy = "T5210"
			caseModel.Trans(
				ctx, func(ctx context.Context, session sqlx.Session) error {
					if _, err := caseModel.Update(ctx, session, caseItem); err != nil {
						t.Errorf("update perf case[%s] failed: %v", caseItem.CaseId, err)
						return err
					}
					return nil
				},
			)
		}
	}
}
