ARG BUILDTIME_IMAGE="golang:1.18.10"
FROM $BUILDTIME_IMAGE AS builder

ARG SOURCE

LABEL stage=gobuilder

ENV CGO_ENABLED 0
ENV GO111MODULE="on" \
    GOPROXY="http://yw-nexus.ttyuyin.com:8081/repository/group-go/,https://goproxy.cn,direct" \
    GONOSUMDB="registry.ttyuyin.com,gitlab.ttyuyin.com"

RUN sed -i 's/dl-cdn.alpinelinux.org/mirrors.aliyun.com/g' /etc/apk/repositories && \
    apk update --no-cache && \
    apk add --no-cache tzdata git make && \
    git config --global http.sslVerify false

WORKDIR /build

COPY . .
RUN go mod download

RUN cd manager && make $SOURCE

FROM scratch

ARG TARGET

COPY --from=builder /etc/ssl/certs/ca-certificates.crt /etc/ssl/certs/ca-certificates.crt
COPY --from=builder /usr/share/zoneinfo/Asia/Shanghai /usr/share/zoneinfo/Asia/Shanghai
ENV TZ Asia/Shanghai

WORKDIR /app
COPY --from=builder /build/manager/bin/$TARGET /app/manager
ENV PATH=${PATH}:/app

ENTRYPOINT ["/app/manager"]
