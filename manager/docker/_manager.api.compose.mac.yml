version: '3.1'

services:
  manager-api:
    image: registry.ttyuyin.com/test-dev/manager-api:${VERSION}
    restart: always
    volumes:
      - ../api/etc/manager.yaml:/app/etc/manager.yaml
    command: --config /app/etc/manager.yaml
    ports:
      - 11001:11001
    env_file:
      - compose.env

# 注:
# mac系统不支持host的network_mode，为了让容器能否访问宿主机网络，参考: https://docs.docker.com/desktop/mac/networking/
# gateway.docker.internal 将被解释为宿主机地址