package common

import (
	ttemplate "text/template"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/template"
)

const (
	// toBeImplementedNotifyTemplate 待实现通知模板
	toBeImplementedNotifyTemplate = `{
  "config": {
    "wide_screen_mode": true
  },
  "header": {
    "template": "yellow",
    "title": {
      "content": "你有一个{{.ResourceTypeZH}}待实现",
      "tag": "plain_text"
    }
  },
  "elements": [
    {
      "tag": "div",
      "fields": [
        {
          "is_short": true,
          "text": {
            "tag": "lark_md",
            "content": "**分配人：**{{.NotifiedBy}}"
          }
        },
        {
          "is_short": true,
          "text": {
            "tag": "lark_md",
            "content": "**分配时间：**{{.NotifiedAt.Format "2006-01-02 15:04:05"}}"
          }
        }
      ]
    },
    {
      "tag": "div",
      "fields": [
        {
          "is_short": true,
          "text": {
            "tag": "lark_md",
            "content": "**类型：**{{.ResourceParentTypeZH}}-{{.ResourceTypeZH}}"
          }
        },
        {
          "is_short": true,
          "text": {
            "tag": "lark_md",
            "content": "**{{.ResourceTypeZH}}名称：**{{.ResourceName | jsonEscaper}}"
          }
        }
      ]
    },
    {
      "tag": "action",
      "actions": [
        {
          "tag": "button",
          "text": {
            "tag": "plain_text",
            "content": "立刻处理"
          },
          "url": "{{.RedirectURL}}",
          "type": "primary"
        }
      ]
    }
  ]
}`
	// toBeMaintainedNotifyTemplate 待维护通知模板
	toBeMaintainedNotifyTemplate = `{
  "config": {
    "wide_screen_mode": true
  },
  "header": {
    "template": "yellow",
    "title": {
      "content": "你有一个{{.ResourceTypeZH}}待维护",
      "tag": "plain_text"
    }
  },
  "elements": [
    {
      "tag": "div",
      "fields": [
        {
          "is_short": true,
          "text": {
            "tag": "lark_md",
            "content": "**分配人：**{{.NotifiedBy}}"
          }
        },
        {
          "is_short": true,
          "text": {
            "tag": "lark_md",
            "content": "**分配时间：**{{.NotifiedAt.Format "2006-01-02 15:04:05"}}"
          }
        }
      ]
    },
    {
      "tag": "div",
      "fields": [
        {
          "is_short": true,
          "text": {
            "tag": "lark_md",
            "content": "**类型：**{{.ResourceParentTypeZH}}-{{.ResourceTypeZH}}"
          }
        },
        {
          "is_short": true,
          "text": {
            "tag": "lark_md",
            "content": "**{{.ResourceTypeZH}}名称：**{{.ResourceName | jsonEscaper}}"
          }
        }
      ]
    },
    {
      "tag": "action",
      "actions": [
        {
          "tag": "button",
          "text": {
            "tag": "plain_text",
            "content": "立刻处理"
          },
          "url": "{{.RedirectURL}}",
          "type": "primary"
        }
      ]
    }
  ]
}`
	// pendingReviewNotifyTemplate 待审核通知模板
	pendingReviewNotifyTemplate = `{
  "config": {
    "wide_screen_mode": true
  },
  "header": {
    "template": "blue",
    "title": {
      "content": "你有一个{{.ResourceTypeZH}}待审核",
      "tag": "plain_text"
    }
  },
  "elements": [
    {
      "tag": "div",
      "fields": [
        {
          "is_short": true,
          "text": {
            "tag": "lark_md",
            "content": "**申请人：**{{.NotifiedBy}}"
          }
        },
        {
          "is_short": true,
          "text": {
            "tag": "lark_md",
            "content": "**申请时间：**{{.NotifiedAt.Format "2006-01-02 15:04:05"}}"
          }
        }
      ]
    },
    {
      "tag": "div",
      "fields": [
        {
          "is_short": true,
          "text": {
            "tag": "lark_md",
            "content": "**类型：**{{.ResourceParentTypeZH}}-{{.ResourceTypeZH}}"
          }
        },
        {
          "is_short": true,
          "text": {
            "tag": "lark_md",
            "content": "**{{.ResourceTypeZH}}名称：**{{.ResourceName | jsonEscaper}}"
          }
        }
      ]
    },
    {
      "tag": "action",
      "actions": [
        {
          "tag": "button",
          "text": {
            "tag": "plain_text",
            "content": "立刻处理"
          },
          "url": "{{.RedirectURL}}",
          "type": "primary"
        }
      ]
    }
  ]
}`
	// reviewResultNotifyTemplate 审核结果通知模板
	reviewResultNotifyTemplate = `{
  "config": {
    "wide_screen_mode": true
  },
  "header": {
    "template": "{{if eq .ReviewResultZH "通过"}}green{{else}}red{{end}}",
    "title": {
      "content": "你有一个{{.ResourceTypeZH}}审核{{.ReviewResultZH}}",
      "tag": "plain_text"
    }
  },
  "elements": [
    {
      "tag": "div",
      "fields": [
        {
          "is_short": true,
          "text": {
            "tag": "lark_md",
            "content": "**审核人：**{{.NotifiedBy}}"
          }
        },
        {
          "is_short": true,
          "text": {
            "tag": "lark_md",
            "content": "**审核时间：**{{.NotifiedAt.Format "2006-01-02 15:04:05"}}"
          }
        }
      ]
    },
    {
      "tag": "div",
      "fields": [
        {
          "is_short": true,
          "text": {
            "tag": "lark_md",
            "content": "**类型：**{{.ResourceParentTypeZH}}-{{.ResourceTypeZH}}"
          }
        },
        {
          "is_short": true,
          "text": {
            "tag": "lark_md",
            "content": "**{{.ResourceTypeZH}}名称：**{{.ResourceName | jsonEscaper}}"
          }
        }
      ]
    },
    {
      "tag": "div",
      "fields": [
        {
          "is_short": false,
          "text": {
            "tag": "lark_md",
            "content": "**意见：**\n{{.ReviewRemark | jsonEscaper}}"
          }
        }
      ]
    },
    {
      "tag": "action",
      "actions": [
        {
          "tag": "button",
          "text": {
            "tag": "plain_text",
            "content": "查看"
          },
          "url": "{{.RedirectURL}}",
          "type": "primary"
        }
      ]
    }
  ]
}`
)

const (
	// modifyComponentGroupURLTemplate 编辑组件组跳转地址模板
	modifyComponentGroupURLTemplate = "/api-test/business-component/edit?project_id={{.ProjectID}}&category_id={{.ResourceBranch}}&component_group_id={{.ResourceID}}&compomentMode={{.ResourceType}}&mode=edit"
	// modifyApiCaseURLTemplate 编辑场景用例跳转地址模板
	modifyApiCaseURLTemplate = "/api-test/testcase/edit?project_id={{.ProjectID}}&category_id={{.ResourceBranch}}&case_id={{.ResourceID}}&mode=edit"
	// modifyInterfaceCaseURLTemplate 编辑接口用例跳转地址模板
	modifyInterfaceCaseURLTemplate = "/api-test/testcase/edit?project_id={{.ProjectID}}&document_id={{.ResourceBranch}}&case_id={{.ResourceID}}&mode=edit"

	// pendingReviewRecordsURLTemplate 待审核的审核记录页跳转地址模板
	pendingReviewRecordsURLTemplate = "/project/case-review/approval?project_id={{.ProjectID}}&from_router=projectIndex&permissions-type=Project"
)

var (
	ToBeImplementedNotifyTemplate = ttemplate.Must(
		template.ParseWithFuncs(
			"ToBeImplementedNotifyTemplate", toBeImplementedNotifyTemplate,
			ttemplate.FuncMap{"jsonEscaper": jsonEscaper},
		),
	)
	ToBeMaintainedNotifyTemplate = ttemplate.Must(
		template.ParseWithFuncs(
			"ToBeMaintainedNotifyTemplate", toBeMaintainedNotifyTemplate,
			ttemplate.FuncMap{"jsonEscaper": jsonEscaper},
		),
	)
	PendingReviewNotifyTemplate = ttemplate.Must(
		template.ParseWithFuncs(
			"PendingReviewNotifyTemplate", pendingReviewNotifyTemplate,
			ttemplate.FuncMap{"jsonEscaper": jsonEscaper},
		),
	)
	ReviewResultNotifyTemplate = ttemplate.Must(
		template.ParseWithFuncs(
			"ReviewResultNotifyTemplate", reviewResultNotifyTemplate,
			ttemplate.FuncMap{"jsonEscaper": jsonEscaper},
		),
	)

	ModifyComponentGroupURLTemplate = ttemplate.Must(
		template.Parse("ModifyComponentGroupURLTemplate", modifyComponentGroupURLTemplate),
	)
	ModifyApiCaseURLTemplate = ttemplate.Must(
		template.Parse("ModifyApiCaseURLTemplate", modifyApiCaseURLTemplate),
	)
	ModifyInterfaceCaseURLTemplate = ttemplate.Must(
		template.Parse("ModifyInterfaceCaseURLTemplate", modifyInterfaceCaseURLTemplate),
	)
	PendingReviewRecordsURLTemplate = ttemplate.Must(
		template.Parse("PendingReviewRecordsURLTemplate", pendingReviewRecordsURLTemplate),
	)
)
