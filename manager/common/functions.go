package common

import (
	"github.com/zeromicro/go-zero/core/jsonx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/userinfo"

	commonpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
)

func CheckCategoryNameByType(name string, tp CategoryTreeType) error {
	var names []string
	switch tp {
	case ConstCategoryTreeTypeInterfaceDocument: // 接口文档
		names = InterfaceDocumentBuiltinCategoryNames
	case ConstCategoryTreeTypeInterfaceSchema: // 接口数据模型
		names = InterfaceSchemaBuiltinCategoryNames
	case ConstCategoryTreeTypeComponentGroup: // 组件组
		names = ComponentGroupBuiltinCategoryNames
	case ConstCategoryTreeTypeApiCase: // API用例
		names = ApiCaseBuiltinCategoryNames
	case ConstCategoryTreeTypeApiSuite: // API集合
		names = ApiSuiteBuiltinCategoryNames
	case ConstCategoryTreeTypeApiPlan: // API计划
		names = ApiPlanBuiltinCategoryNames
	case ConstCategoryTreeTypeUiPlan: // UI计划
		names = UiPlanBuiltinCategoryNames
	case ConstCategoryTreeTypePerfCase: // 压测用例
		names = PerfCaseBuiltinCategoryNames
	case ConstCategoryTreeTypePerfPlan: // 压测计划
		names = PerfPlanBuiltinCategoryNames
	case ConstCategoryTreeTypeStabilityPlan: // 稳测计划
		names = StabilityPlanBuiltinCategoryNames
	case ConstCategoryTreeTypeUIAgentComponent:
		names = UIAgentComponentBuiltinCategoryNames
	default:
		return errorx.Errorf(
			errorx.DoesNotSupport, "the type of category tree[%s] doesn't support", tp,
		)
	}

	if stringx.Contains(names, name) {
		return errorx.Errorf(
			errorx.ProhibitedBehavior,
			"the name[%s] conflicts with the builtin name of category tree[%s] and is not allowed to be used",
			name, tp,
		)
	}

	return nil
}

func GetRootTypeByNodeType(nodeType string) CategoryBuiltinName {
	switch nodeType {
	case ConstComponentGroupTypeSingle, ConstComponentGroupTypeGroup:
		return ConstCategorySubRootBusinessComponent
	case ConstComponentGroupTypeSetup, ConstComponentGroupTypeTeardown:
		return ConstCategorySubRootSetupTeardownComponent
	case ConstInterfaceDefinitionTypeDocument:
		return ConstCategoryRootAllDocument
	case ConstInterfaceDefinitionTypeSchema:
		return ConstCategoryRootAllSchema
	default:
		return ""
	}
}

func GetTagTypeByReferenceType(referenceType ReferenceType) TagType {
	switch referenceType {
	case ConstReferenceTypeComponentGroup:
		return ConstTagTypeComponentGroup
	case ConstReferenceTypeApiCase, ConstReferenceTypeInterfaceCase, ConstReferenceTypePerfCase:
		return ConstTagTypeCase
	case ConstReferenceTypeApiSuite, ConstReferenceTypePerfSuite:
		return ConstTagTypeSuite
	case ConstReferenceTypeApiPlan, ConstReferenceTypePerfPlan, ConstReferenceTypeStabilityPlan:
		return ConstTagTypePlan
	case ConstReferenceTypeInterfaceDocument:
		return ConstTagTypeInterfaceDocument
	case ConstReferenceTypeUIAgentComponent, ConstReferenceTypeUIAgentCase, ConstReferenceTypeUIAgentPlan:
		return ConstTagTypeUIAgent
	default:
		return ""
	}
}

func GetReviewResourceTypeByComponentGroupType(componentGroupType ComponentGroupType) ReviewResourceType {
	switch componentGroupType {
	case ConstComponentGroupTypeSingle:
		return ConstReviewResourceTypeBusinessComponent
	case ConstComponentGroupTypeGroup:
		return ConstReviewResourceTypeBusinessComponent
	case ConstComponentGroupTypeSetup:
		return ConstReviewResourceTypeSetupComponent
	case ConstComponentGroupTypeTeardown:
		return ConstReviewResourceTypeTeardownComponent
	default:
		return ""
	}
}

func CheckCurrentUser(user *userinfo.UserInfo) error {
	if user == nil {
		return errorx.Err(
			errorx.CurrentUserIsNull, "cannot get your user info, please confirm whether you have logged in",
		)
	}

	return nil
}

//func GetBranchesByURLAndAccessToken(
//	ctx context.Context, gitType GitType, gitlabURL, projectPathEncode, accessToken string,
//) (branches []string, err error) {
//	switch gitType {
//	case ConstGitTypeGitLab: // GitLab
//		apiURL := fmt.Sprintf("%s/api/v4/projects/%s/repository/branches", gitlabURL, projectPathEncode)
//
//		client := &http.Client{}
//		req, err := http.NewRequestWithContext(ctx, "GET", apiURL, nil)
//		if err != nil {
//			return nil, err
//		}
//
//		req.Header.Add("PRIVATE-TOKEN", accessToken)
//
//		resp, err := client.Do(req) //nolint:bodyclose
//		if err != nil {
//			return nil, err
//		}
//		defer func(body io.ReadCloser) {
//			if body != nil {
//				_ = body.Close()
//			}
//		}(resp.Body)
//
//		if resp.StatusCode == http.StatusOK {
//			jsonData, err := io.ReadAll(resp.Body)
//			if err != nil {
//				return nil, err
//			}
//
//			var bs []Branch
//			err = json.Unmarshal(jsonData, &bs)
//			if err != nil {
//				return nil, err
//			}
//
//			for _, b := range bs {
//				branches = append(branches, b.Name)
//			}
//		} else {
//			return nil, errorx.Errorf(
//				errorx.CallExternalAPIFailure, "failed to call external api, api: %s, status code: %s",
//				apiURL, resp.Status,
//			)
//		}
//
//		return branches, nil
//	case ConstGitTypeGitHub, ConstGitTypeGitee: // GitHub or Gitee
//		return nil, errorx.Errorf(
//			errorx.DoesNotSupport, "the git type is not supported at the moment, git type: %s", gitType,
//		)
//	default:
//		return nil, errorx.Errorf(errorx.DoesNotSupport, "invalid git type: %s", gitType)
//	}
//}

func GetMediaTypeByExtension(extension FileExtension) MediaType {
	switch extension {
	case ConstFileExtensionJson:
		return ConstMediaTypeApplicationJson
	case ConstFileExtensionYaml, ConstFileExtensionYml:
		return ConstMediaTypeTextVNDYaml
	case ConstFileExtensionCsv:
		return ConstMediaTypeTextCsv
	default:
		return ConstMediaTypeApplicationOctetStream
	}
}

func jsonEscaper(content string) (string, error) {
	escapedContent, err := jsonx.Marshal(content)
	if err != nil {
		return "", err
	}

	if escapedContent[0] == '"' && escapedContent[len(escapedContent)-1] == '"' {
		escapedContent = escapedContent[1 : len(escapedContent)-1]
	}

	return string(escapedContent), nil
}

func GetTTAuthPerfCaseStep() *commonpb.PerfCaseStepV2 {
	s := defaultPerfCaseStepOfTTAuth
	return &commonpb.PerfCaseStepV2{
		Name:       s.GetName(),
		RateLimits: s.GetRateLimits(),
		Url:        s.GetUrl(),
		Method:     s.GetMethod(),
		Headers:    s.GetHeaders(),
		Body:       s.GetBody(),
		Exports:    s.GetExports(),
		Sleep:      s.GetSleep(),
		Key:        s.GetKey(),
		Service:    s.GetService(),
		Namespace:  s.GetNamespace(),
		Cmd:        s.GetCmd(),
		GrpcPath:   s.GetGrpcPath(),
		Deprecated: s.GetDeprecated(),
	}
}
