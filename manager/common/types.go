package common

import "go.uber.org/atomic"

type Function struct {
	Name string `json:"name"`
	Type string `json:"type,options=BUILTIN|CUSTOM"`
}

type Branch struct {
	Name string `json:"name"`
}

type Statistic struct {
	Total     *atomic.Int64 `json:"total"`
	Increased *atomic.Int64 `json:"increased"`
	Modified  *atomic.Int64 `json:"modified"`
	Unchanged *atomic.Int64 `json:"unchanged"`
	Skipped   *atomic.Int64 `json:"skipped"`
	Failure   *atomic.Int64 `json:"failure"`
}

func NewStatistic(ns ...int64) *Statistic {
	n := make([]int64, 6)
	copy(n, ns)

	return &Statistic{
		Total:     atomic.NewInt64(n[0]),
		Increased: atomic.NewInt64(n[1]),
		Modified:  atomic.NewInt64(n[2]),
		Unchanged: atomic.NewInt64(n[3]),
		Skipped:   atomic.NewInt64(n[4]),
		Failure:   atomic.NewInt64(n[5]),
	}
}
