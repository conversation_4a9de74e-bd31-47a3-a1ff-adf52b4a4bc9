package devicehub

import (
	"context"

	"github.com/zeromicro/go-zero/zrpc"
	"google.golang.org/grpc"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/devicehub/rpc/client/deviceservice"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/devicehub/rpc/pb"
)

type RPCClient struct {
	conf zrpc.RpcClientConf

	client deviceservice.DeviceService
}

func NewRPCClient(c zrpc.RpcClientConf) *RPCClient {
	return &RPCClient{
		conf: c,

		client: deviceservice.NewDeviceService(zrpc.MustNewClient(c)),
	}
}

func (c *RPCClient) GetDevice(ctx context.Context, in *pb.GetDeviceReq, opts ...grpc.CallOption) (
	*pb.GetDeviceResp, error,
) {
	return c.client.GetDevice(ctx, in, opts...)
}

func (c *RPCClient) SearchDevice(
	ctx context.Context, in *pb.SearchDeviceReq, opts ...grpc.CallOption,
) (*pb.SearchDeviceResp, error) {
	return c.client.SearchDevice(ctx, in, opts...)
}

func (c *RPCClient) AcquireDeviceByUDID(
	ctx context.Context, in *pb.AcquireDeviceReq, opts ...grpc.CallOption,
) (*pb.AcquireDeviceResp, error) {
	return c.client.AcquireDevice(ctx, in, opts...)
}

func (c *RPCClient) AcquireDeviceByCondition(
	ctx context.Context, in *pb.SearchAcquireDeviceReq, opts ...grpc.CallOption,
) (*pb.SearchAcquireDeviceResp, error) {
	return c.client.SearchAcquireDevice(ctx, in, opts...)
}

func (c *RPCClient) ReleaseDevice(
	ctx context.Context, in *pb.ReleaseDeviceReq, opts ...grpc.CallOption,
) (*pb.ReleaseDeviceResp, error) {
	return c.client.ReleaseDevice(ctx, in, opts...)
}
