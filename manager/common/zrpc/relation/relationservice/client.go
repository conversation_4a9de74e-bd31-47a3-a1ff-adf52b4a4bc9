package relationservice

import (
	"context"

	"github.com/zeromicro/go-zero/zrpc"
	"google.golang.org/grpc"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/interceptors/clientinterceptors"

	client "gitlab.ttyuyin.com/TestDevelopment/probe-backend/relation/rpc/client/relationservice"
	relationpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/relation/rpc/pb"
)

// Deprecated: use `relation.RPCClient` instead.
type RelationRpc struct {
	conf zrpc.RpcClientConf
	name string

	client client.RelationService
}

// Deprecated: use `relation.NewRPCClient` instead.
func NewRelationRpc(c zrpc.RpcClientConf) *RelationRpc {
	return &RelationRpc{
		conf:   c,
		name:   c.Etcd.Key,
		client: client.NewRelationService(zrpc.MustNewClient(c, clientinterceptors.UnaryUserInfoClientOption())),
	}
}

// Deprecated: use `RelationRpc.GetCaseByService` instead.
func (c *RelationRpc) GetServiceBindTestCase(
	ctx context.Context, req *relationpb.GetServiceBindTestCaseRequest, opts ...grpc.CallOption,
) (resp *relationpb.GetServiceBindTestCaseResponse, err error) {
	return c.client.GetServiceBindTestCase(ctx, req, opts...)
}

// Deprecated: use `relation.RPCClient.GetServiceByMethod` instead.
func (c *RelationRpc) GetServiceByMethod(
	ctx context.Context, req *relationpb.GetServiceByMethodReq, opts ...grpc.CallOption,
) (resp *relationpb.GetServiceByMethodResp, err error) {
	return c.client.GetServiceByMethod(ctx, req, opts...)
}

// Deprecated: use `relation.RPCClient.GetCaseByService` instead.
func (c *RelationRpc) GetCaseByService(
	ctx context.Context, req *relationpb.GetCaseByServiceReq, opts ...grpc.CallOption,
) (*relationpb.GetCaseByServiceResp, error) {
	return c.client.GetCaseByService(ctx, req, opts...)
}

// Deprecated: use `relation.RPCClient.GetTeamByService` instead.
func (c *RelationRpc) GetTeamByService(
	ctx context.Context, req *relationpb.GetTeamByServiceReq, opts ...grpc.CallOption,
) (*relationpb.GetTeamByServiceResp, error) {
	return c.client.GetTeamByService(ctx, req, opts...)
}

// Deprecated: use `relation.RPCClient.GetAllServiceTeams` instead.
func (c *RelationRpc) GetAllServiceTeams(
	ctx context.Context, req *relationpb.GetAllServiceTeamsReq, opts ...grpc.CallOption,
) (*relationpb.GetAllServiceTeamsResp, error) {
	return c.client.GetAllServiceTeams(ctx, req, opts...)
}

// Deprecated: use `relation.RPCClient.GetAllServiceMethods` instead.
func (c *RelationRpc) GetAllServiceMethods(
	ctx context.Context, req *relationpb.GetAllServiceMethodsReq, opts ...grpc.CallOption,
) (*relationpb.GetAllServiceMethodsResp, error) {
	return c.client.GetAllServiceMethods(ctx, req, opts...)
}
