package relation

import (
	"context"

	"github.com/zeromicro/go-zero/zrpc"
	"google.golang.org/grpc"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/interceptors/clientinterceptors"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/relation/rpc/client/relationservice"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/relation/rpc/pb"
)

type RPCClient struct {
	conf zrpc.RpcClientConf

	relation relationservice.RelationService
}

func NewRPCClient(c zrpc.RpcClientConf) *RPCClient {
	return &RPCClient{
		conf: c,

		relation: relationservice.NewRelationService(
			zrpc.MustNewClient(
				c, clientinterceptors.UnaryUserInfoClientOption(),
			),
		),
	}
}

// Deprecated: use `RPCClient.GetCaseByService` instead.
func (c *RPCClient) GetServiceBindTestCase(
	ctx context.Context, req *pb.GetServiceBindTestCaseRequest, opts ...grpc.CallOption,
) (resp *pb.GetServiceBindTestCaseResponse, err error) {
	return c.relation.GetServiceBindTestCase(ctx, req, opts...)
}

func (c *RPCClient) GetServiceByMethod(
	ctx context.Context, req *pb.GetServiceByMethodReq, opts ...grpc.CallOption,
) (resp *pb.GetServiceByMethodResp, err error) {
	return c.relation.GetServiceByMethod(ctx, req, opts...)
}

func (c *RPCClient) GetCaseByService(
	ctx context.Context, req *pb.GetCaseByServiceReq, opts ...grpc.CallOption,
) (*pb.GetCaseByServiceResp, error) {
	return c.relation.GetCaseByService(ctx, req, opts...)
}

func (c *RPCClient) GetTeamByService(
	ctx context.Context, req *pb.GetTeamByServiceReq, opts ...grpc.CallOption,
) (*pb.GetTeamByServiceResp, error) {
	return c.relation.GetTeamByService(ctx, req, opts...)
}

func (c *RPCClient) GetAllServiceTeams(
	ctx context.Context, req *pb.GetAllServiceTeamsReq, opts ...grpc.CallOption,
) (*pb.GetAllServiceTeamsResp, error) {
	return c.relation.GetAllServiceTeams(ctx, req, opts...)
}

func (c *RPCClient) GetAllServiceMethods(
	ctx context.Context, req *pb.GetAllServiceMethodsReq, opts ...grpc.CallOption,
) (*pb.GetAllServiceMethodsResp, error) {
	return c.relation.GetAllServiceMethods(ctx, req, opts...)
}

func (c *RPCClient) GetCaseOnlyByService(
	ctx context.Context, req *pb.GetCaseOnlyByServiceReq, opts ...grpc.CallOption,
) (*pb.GetCaseOnlyByServiceResp, error) {
	return c.relation.GetCaseOnlyByService(ctx, req, opts...)
}
