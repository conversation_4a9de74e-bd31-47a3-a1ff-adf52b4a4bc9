package domainservice

import (
	"context"

	"github.com/zeromicro/go-zero/zrpc"
	"google.golang.org/grpc"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/interceptors/clientinterceptors"
	client "gitlab.ttyuyin.com/TestDevelopment/qet-backend-middleware/permission/rpc/client/domainservice"
)

type PermissionDomainRPC struct {
	conf zrpc.RpcClientConf
	name string

	client client.DomainService
}

func NewPermissionDomainRPC(c zrpc.RpcClientConf) *PermissionDomainRPC {
	return &PermissionDomainRPC{
		conf:   c,
		name:   c.Etcd.Key,
		client: client.NewDomainService(zrpc.MustNewClient(c, clientinterceptors.UnaryUserInfoClientOption())),
	}
}

func (c *PermissionDomainRPC) Client() client.DomainService {
	return c.client
}

func (c *PermissionDomainRPC) CreateDomain(ctx context.Context, req *client.CreateDomainReq, opts ...grpc.CallOption) (*client.CreateDomainResp, error) {
	return c.client.CreateDomain(ctx, req, opts...)
}

func (c *PermissionDomainRPC) RemoveDomain(ctx context.Context, req *client.RemoveDomainReq, opts ...grpc.CallOption) (*client.RemoveDomainResp, error) {
	return c.client.RemoveDomain(ctx, req, opts...)
}

func (c *PermissionDomainRPC) ModifyDomain(ctx context.Context, req *client.UpdateDomainReq, opts ...grpc.CallOption) (*client.UpdateDomainResp, error) {
	return c.client.UpdateDomain(ctx, req, opts...)
}
