package policyservice

import (
	"github.com/zeromicro/go-zero/zrpc"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/interceptors/clientinterceptors"

	client "gitlab.ttyuyin.com/TestDevelopment/qet-backend-middleware/permission/rpc/client/policyservice"
)

type PermissionPolicyRPC struct {
	conf zrpc.RpcClientConf
	name string

	client client.PolicyService
}

func NewPermissionPolicyRPC(c zrpc.RpcClientConf) *PermissionPolicyRPC {
	return &PermissionPolicyRPC{
		conf:   c,
		name:   c.Etcd.Key,
		client: client.NewPolicyService(zrpc.MustNewClient(c, clientinterceptors.UnaryUserInfoClientOption())),
	}
}

func (c *PermissionPolicyRPC) Client() client.PolicyService {
	return c.client
}
