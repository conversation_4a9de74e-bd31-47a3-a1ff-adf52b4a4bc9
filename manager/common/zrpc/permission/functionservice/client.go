package functionservice

import (
	"github.com/zeromicro/go-zero/zrpc"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/interceptors/clientinterceptors"

	client "gitlab.ttyuyin.com/TestDevelopment/qet-backend-middleware/permission/rpc/client/functionservice"
)

type PermissionFunctionRPC struct {
	conf zrpc.RpcClientConf
	name string

	client client.FunctionService
}

func NewPermissionPolicyRPC(c zrpc.RpcClientConf) *PermissionFunctionRPC {
	return &PermissionFunctionRPC{
		conf:   c,
		name:   c.Etcd.Key,
		client: client.NewFunctionService(zrpc.MustNewClient(c, clientinterceptors.UnaryUserInfoClientOption())),
	}
}

func (c *PermissionFunctionRPC) Client() client.FunctionService {
	return c.client
}
