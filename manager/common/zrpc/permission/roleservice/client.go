package roleservice

import (
	"context"

	"github.com/zeromicro/go-zero/zrpc"
	"google.golang.org/grpc"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/interceptors/clientinterceptors"

	client "gitlab.ttyuyin.com/TestDevelopment/qet-backend-middleware/permission/rpc/client/roleservice"
)

type PermissionRoleRPC struct {
	conf zrpc.RpcClientConf
	name string

	client client.RoleService
}

func NewPermissionRoleRPC(c zrpc.RpcClientConf) *PermissionRoleRPC {
	return &PermissionRoleRPC{
		conf:   c,
		name:   c.Etcd.Key,
		client: client.NewRoleService(zrpc.MustNewClient(c, clientinterceptors.UnaryUserInfoClientOption())),
	}
}

func (c *PermissionRoleRPC) Client() client.RoleService {
	return c.client
}

func (c *PermissionRoleRPC) CreateRole(
	ctx context.Context, req *client.CreateRoleReq, opts ...grpc.CallOption,
) (*client.CreateRoleResp, error) {
	return c.client.Create(ctx, req, opts...)
}

func (c *PermissionRoleRPC) BindUserAndRole(
	ctx context.Context, req *client.UserCreateReq, opts ...grpc.CallOption,
) (*client.UserCreateResp, error) {
	return c.client.UserCreate(ctx, req, opts...)
}

func (c *PermissionRoleRPC) GetProjectUsers(
	ctx context.Context, req *client.UserListReq, opts ...grpc.CallOption,
) (*client.UserListResp, error) {
	return c.client.UserList(ctx, req, opts...)
}

func (c *PermissionRoleRPC) GetUserAllProjectRoles(
	ctx context.Context, req *client.UserViewAllReq, opts ...grpc.CallOption,
) (*client.UserViewAllResp, error) {
	return c.client.UserViewAll(ctx, req, opts...)
}
