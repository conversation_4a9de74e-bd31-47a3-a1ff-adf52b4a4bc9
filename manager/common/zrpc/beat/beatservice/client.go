package beatservice

import (
	"context"

	"github.com/zeromicro/go-zero/zrpc"
	"google.golang.org/grpc"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/interceptors/clientinterceptors"
	client "gitlab.ttyuyin.com/TestDevelopment/qet-backend-middleware/beat/rpc/client/periodictaskservice"
	beatpb "gitlab.ttyuyin.com/TestDevelopment/qet-backend-middleware/beat/rpc/pb"
)

// Deprecated: use `beat.RPCClient` instead.
type BeatRpc struct {
	conf zrpc.RpcClientConf
	name string

	client client.PeriodicTaskService
}

// Deprecated: use `beat.NewRPCClient` instead.
func NewBeatRpc(c zrpc.RpcClientConf) *BeatRpc {
	return &BeatRpc{
		conf:   c,
		name:   c.Etcd.Key,
		client: client.NewPeriodicTaskService(zrpc.MustNewClient(c, clientinterceptors.UnaryUserInfoClientOption())),
	}
}

// Deprecated: use `beat.RPCClient.CreateOrModifyPeriodicTask` instead.
func (c *BeatRpc) CreateScheduleTaskOfApiPlan(
	ctx context.Context, req *beatpb.CreateOrModifyPeriodicTaskReq, opts ...grpc.CallOption,
) (*beatpb.CreateOrModifyPeriodicTaskResp, error) {
	return c.client.CreateOrModifyPeriodicTask(ctx, req, opts...)
}

// Deprecated: use `beat.RPCClient.CreateOrModifyPeriodicTask` instead.
func (c *BeatRpc) CreateScheduleTaskOfUiPlan(
	ctx context.Context, req *client.CreateOrModifyPeriodicTaskReq, opts ...grpc.CallOption,
) (*beatpb.CreateOrModifyPeriodicTaskResp, error) {
	return c.client.CreateOrModifyPeriodicTask(ctx, req, opts...)
}

// Deprecated: use `beat.RPCClient.CreateOrModifyPeriodicTask` instead.
func (c *BeatRpc) CreateScheduleTaskOfPerfPlan(
	ctx context.Context, req *beatpb.CreateOrModifyPeriodicTaskReq, opts ...grpc.CallOption,
) (*beatpb.CreateOrModifyPeriodicTaskResp, error) {
	return c.client.CreateOrModifyPeriodicTask(ctx, req, opts...)
}

// Deprecated: use `beat.RPCClient.RemovePeriodicTask` instead.
func (c *BeatRpc) RemoveScheduleTaskOfApiPlan(
	ctx context.Context, req *beatpb.RemovePeriodicTaskReq, opts ...grpc.CallOption,
) (*beatpb.RemovePeriodicTaskResp, error) {
	return c.client.RemovePeriodicTask(ctx, req, opts...)
}

// Deprecated: use `beat.RPCClient.RemovePeriodicTask` instead.
func (c *BeatRpc) RemoveScheduleTaskOfUiPlan(
	ctx context.Context, req *beatpb.RemovePeriodicTaskReq, opts ...grpc.CallOption,
) (*beatpb.RemovePeriodicTaskResp, error) {
	return c.client.RemovePeriodicTask(ctx, req, opts...)
}

// Deprecated: use `beat.RPCClient.RemovePeriodicTask` instead.
func (c *BeatRpc) RemoveScheduleTaskOfPerfPlan(
	ctx context.Context, req *beatpb.RemovePeriodicTaskReq, opts ...grpc.CallOption,
) (*beatpb.RemovePeriodicTaskResp, error) {
	return c.client.RemovePeriodicTask(ctx, req, opts...)
}
