package beat

import (
	"context"

	"github.com/zeromicro/go-zero/zrpc"
	"google.golang.org/grpc"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/interceptors/clientinterceptors"
	periodictaskservice "gitlab.ttyuyin.com/TestDevelopment/qet-backend-middleware/beat/rpc/client/periodictaskservice"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-middleware/beat/rpc/pb"
)

type RPCClient struct {
	conf zrpc.RpcClientConf

	task periodictaskservice.PeriodicTaskService
}

func NewRPCClient(c zrpc.RpcClientConf) *RPCClient {
	return &RPCClient{
		conf: c,

		task: periodictaskservice.NewPeriodicTaskService(
			zrpc.MustNewClient(
				c, clientinterceptors.UnaryUserInfoClientOption(),
			),
		),
	}
}

func (c *RPCClient) CreateOrModifyPeriodicTask(
	ctx context.Context, in *pb.CreateOrModifyPeriodicTaskReq, opts ...grpc.CallOption,
) (*pb.CreateOrModifyPeriodicTaskResp, error) {
	return c.task.CreateOrModifyPeriodicTask(ctx, in, opts...)
}

func (c *RPCClient) RemovePeriodicTask(
	ctx context.Context, in *pb.RemovePeriodicTaskReq, opts ...grpc.CallOption,
) (*pb.RemovePeriodicTaskResp, error) {
	return c.task.RemovePeriodicTask(ctx, in, opts...)
}
