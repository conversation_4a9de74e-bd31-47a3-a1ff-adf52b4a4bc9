package larkproxy

import (
	"context"

	"github.com/zeromicro/go-zero/zrpc"
	"google.golang.org/grpc"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/interceptors/clientinterceptors"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/larkproxy/rpc/client/larkcontactuserservice"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/larkproxy/rpc/client/larkimchatmembersservice"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/larkproxy/rpc/client/larkimchatsservice"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/larkproxy/rpc/pb"
)

type RPCClient struct {
	conf zrpc.RpcClientConf

	user   larkcontactuserservice.LarkContactUserService
	member larkimchatmembersservice.LarkIMChatMembersService
	chat   larkimchatsservice.LarkIMChatsService
}

func NewRPCClient(c zrpc.RpcClientConf) *RPCClient {
	return &RPCClient{
		conf: c,

		user: larkcontactuserservice.NewLarkContactUserService(
			zrpc.MustNewClient(
				c, clientinterceptors.UnaryUserInfoClientOption(),
			),
		),
		member: larkimchatmembersservice.NewLarkIMChatMembersService(
			zrpc.MustNewClient(
				c, clientinterceptors.UnaryUserInfoClientOption(),
			),
		),
		chat: larkimchatsservice.NewLarkIMChatsService(
			zrpc.MustNewClient(
				c, clientinterceptors.UnaryUserInfoClientOption(),
			),
		),
	}
}

func (c *RPCClient) GetBatchUserID(
	ctx context.Context, in *pb.GetBatchUserIDReq, opts ...grpc.CallOption,
) (*pb.GetBatchUserIDResp, error) {
	return c.user.GetBatchUserID(ctx, in, opts...)
}

func (c *RPCClient) GetChatMembers(
	ctx context.Context, in *pb.GetChatMembersReq, opts ...grpc.CallOption,
) (*pb.GetChatMembersResp, error) {
	return c.member.GetChatMembers(ctx, in, opts...)
}

func (c *RPCClient) ListChat(ctx context.Context, in *pb.ListChatReq, opts ...grpc.CallOption) (
	*pb.ListChatResp, error,
) {
	return c.chat.ListChat(ctx, in, opts...)
}
