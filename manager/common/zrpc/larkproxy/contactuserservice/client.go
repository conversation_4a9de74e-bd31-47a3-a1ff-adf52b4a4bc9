package contactuserservice

import (
	"context"

	"github.com/zeromicro/go-zero/zrpc"
	"google.golang.org/grpc"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/interceptors/clientinterceptors"

	client "gitlab.ttyuyin.com/TestDevelopment/probe-backend/larkproxy/rpc/client/larkcontactuserservice"
)

// Deprecated: use `larkproxy.RPCClient` instead.
type LarkProxyContactUserRpc struct {
	conf zrpc.RpcClientConf
	name string

	client client.LarkContactUserService
}

// Deprecated: use `larkproxy.NewRPCClient` instead.
func NewLarkProxyContactUserRpc(c zrpc.RpcClientConf) *LarkProxyContactUserRpc {
	return &LarkProxyContactUserRpc{
		conf:   c,
		name:   c.Etcd.Key,
		client: client.NewLarkContactUserService(zrpc.MustNewClient(c, clientinterceptors.UnaryUserInfoClientOption())),
	}
}

// Deprecated: use `larkproxy.RPCClient.GetBatchUserID` instead.
func (c *LarkProxyContactUserRpc) GetBatchUserID(
	ctx context.Context, req *client.GetBatchUserIDReq, opts ...grpc.CallOption,
) (*client.GetBatchUserIDResp, error) {
	return c.client.GetBatchUserID(ctx, req, opts...)
}
