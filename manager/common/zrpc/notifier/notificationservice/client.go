package notificationservice

import (
	"context"

	"github.com/zeromicro/go-zero/zrpc"
	"google.golang.org/grpc"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/interceptors/clientinterceptors"

	client "gitlab.ttyuyin.com/TestDevelopment/qet-backend-middleware/notifier/rpc/client/notificationservice"
)

// Deprecated: use `notifier.RPCClient` instead.
type NotifierRpc struct {
	conf zrpc.RpcClientConf
	name string

	client client.NotificationService
}

// Deprecated: use `notifier.NewRPCClient` instead.
func NewNotifierRpc(c zrpc.RpcClientConf) *NotifierRpc {
	return &NotifierRpc{
		conf:   c,
		name:   c.Etcd.Key,
		client: client.NewNotificationService(zrpc.MustNewClient(c, clientinterceptors.UnaryUserInfoClientOption())),
	}
}

// Deprecated: use `notifier.RPCClient.Notify` instead.
func (c *NotifierRpc) Notify(ctx context.Context, req *client.NotifyReq, opts ...grpc.CallOption) (
	*client.NotifyResp, error,
) {
	return c.client.Notify(ctx, req, opts...)
}
