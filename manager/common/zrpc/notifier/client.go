package notifier

import (
	"context"

	"github.com/zeromicro/go-zero/zrpc"
	"google.golang.org/grpc"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/interceptors/clientinterceptors"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-middleware/notifier/rpc/client/notificationservice"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-middleware/notifier/rpc/pb"
)

type RPCClient struct {
	conf zrpc.RpcClientConf

	notification notificationservice.NotificationService
}

func NewRPCClient(c zrpc.RpcClientConf) *RPCClient {
	return &RPCClient{
		conf: c,

		notification: notificationservice.NewNotificationService(
			zrpc.MustNewClient(
				c, clientinterceptors.UnaryUserInfoClientOption(),
			),
		),
	}
}

func (c *RPCClient) Notify(ctx context.Context, in *pb.NotifyReq, opts ...grpc.CallOption) (*pb.NotifyResp, error) {
	return c.notification.Notify(ctx, in, opts...)
}
