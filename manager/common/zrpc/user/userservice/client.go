package userservice

import (
	"context"

	"github.com/zeromicro/go-zero/zrpc"
	"google.golang.org/grpc"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/interceptors/clientinterceptors"

	client "gitlab.ttyuyin.com/TestDevelopment/qet-backend-middleware/user/rpc/client/userservice"
)

type UserRPC struct {
	conf zrpc.RpcClientConf
	name string

	client client.UserService
}

func NewUserRPC(c zrpc.RpcClientConf) *UserRPC {
	return &UserRPC{
		conf:   c,
		name:   c.Etcd.Key,
		client: client.NewUserService(zrpc.MustNewClient(c, clientinterceptors.UnaryUserInfoClientOption())),
	}
}

func (c *UserRPC) Client() client.UserService {
	return c.client
}

func (c *UserRPC) SearchUser(ctx context.Context, req *client.UserListReq, opts ...grpc.CallOption) (*client.UserListResp, error) {
	return c.client.UserList(ctx, req, opts...)
}

func (c *UserRPC) ViewUser(ctx context.Context, req *client.GetUserReq, opts ...grpc.CallOption) (*client.GetUserResp, error) {
	return c.client.GetUser(ctx, req, opts...)
}
