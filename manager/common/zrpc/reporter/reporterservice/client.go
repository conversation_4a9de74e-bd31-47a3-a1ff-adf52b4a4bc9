package reporterservice

import (
	"context"
	"time"

	"github.com/zeromicro/go-zero/zrpc"
	"google.golang.org/grpc"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/interceptors/clientinterceptors"

	client "gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/client/reporter"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
)

const timeoutOfCountFailedCase = 5 * time.Second

// Deprecated: use `reporter.RPCClient` instead.
type ReporterRpc struct {
	conf zrpc.RpcClientConf
	name string

	client client.Reporter
}

// Deprecated: use `reporter.NewRPCClient` instead.
func NewReporterRpc(c zrpc.RpcClientConf) *ReporterRpc {
	return &ReporterRpc{
		conf:   c,
		name:   c.Etcd.Key,
		client: client.NewReporter(zrpc.MustNewClient(c, clientinterceptors.UnaryUserInfoClientOption())),
	}
}

func (c *ReporterRpc) GetCaseLatestRecord(
	ctx context.Context, req *pb.GetCaseLatestRecordRequest, opts ...grpc.CallOption,
) (resp *pb.GetCaseLatestRecordResponse, err error) {
	return c.client.GetCaseLatestRecord(ctx, req, opts...)
}

func (c *ReporterRpc) DelCaseFailStatForPlan(
	ctx context.Context, req *pb.DelCaseFailStatForPlanReq, opts ...grpc.CallOption,
) (resp *pb.DelCaseFailStatForPlanResp, err error) {
	return c.client.DelCaseFailStatForPlan(ctx, req, opts...)
}

func (c *ReporterRpc) CountFailedCaseInLastNDays(
	ctx context.Context, req *pb.CountFailedCaseInLastNDaysReq, opts ...grpc.CallOption,
) (resp *pb.CountFailedCaseInLastNDaysResp, err error) {
	opts = append([]grpc.CallOption{zrpc.WithCallTimeout(timeoutOfCountFailedCase)}, opts...)
	return c.client.CountFailedCaseInLastNDays(ctx, req, opts...)
}
