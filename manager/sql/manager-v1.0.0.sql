-- fix reference details of component group
CREATE INDEX ix_cgrr_project_id_component_group_id_deleted_reference ON component_group_reference_relationship (project_id, component_group_id, deleted, reference_type, reference_id, reference_version);
CREATE INDEX ix_ic_project_id_case_id_version ON interface_case (project_id, case_id, version);

alter table advanced_search_field add field_type enum ('FT_STRING', 'FT_TIMESTAMP') default 'FT_STRING' not null comment '字段类型，给前端渲染用' after field_id;

-- add a `maintained_by` field to the `component_group` table
ALTER TABLE component_group ADD maintained_by VARCHAR(64) NULL COMMENT '维护者的用户ID' AFTER deleted;

-- modify `priority` field of the `interface_case`, `api_case` table
ALTER TABLE interface_case MODIFY priority TINYINT(1) DEFAULT 0 NOT NULL COMMENT '优先级（NULL、P0、P1、P2、P3...）' AFTER description;
ALTER TABLE api_case MODIFY priority TINYINT(1) DEFAULT 0 NOT NULL COMMENT '优先级（NULL、P0、P1、P2、P3...）' AFTER description;

-- add a `priority` field to the `component_group`, `api_suite`, `api_plan` table
ALTER TABLE component_group ADD priority TINYINT(1) DEFAULT 0 NULL COMMENT '优先级（NULL、P0、P1、P2、P3...）' AFTER description;
ALTER TABLE interface_document ADD priority TINYINT(1) DEFAULT 0 NULL COMMENT '优先级（NULL、P0、P1、P2、P3...）' AFTER status;
ALTER TABLE api_suite ADD priority TINYINT(1) DEFAULT 0 NULL COMMENT '优先级（NULL、P0、P1、P2、P3...）' AFTER description;
ALTER TABLE api_plan ADD priority TINYINT(1) DEFAULT 0 NULL COMMENT '优先级（NULL、P0、P1、P2、P3...）' AFTER description;

-- update empty maintainer to creator
UPDATE component_group SET maintained_by = created_by WHERE maintained_by is NULL OR maintained_by = '';
UPDATE interface_case SET maintained_by = created_by WHERE maintained_by is NULL OR maintained_by = '';
UPDATE interface_document SET maintained_by = created_by WHERE maintained_by is NULL OR maintained_by = '';
UPDATE api_case SET maintained_by = created_by WHERE maintained_by is NULL OR maintained_by = '';
UPDATE api_suite SET maintained_by = created_by WHERE maintained_by is NULL OR maintained_by = '';
UPDATE api_plan SET maintained_by = created_by WHERE maintained_by is NULL OR maintained_by = '';

-- update base_url length
alter table general_configuration modify base_url varchar(128) null comment 'HTTP请求基础URL';
