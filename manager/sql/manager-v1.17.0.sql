-- UI计划下的用例树优化
CREATE UNIQUE INDEX uix_cge_project_id_component_group_id_version_element_id ON component_group_element (project_id, component_group_id, version, element_id);
CREATE UNIQUE INDEX uix_ace_project_id_case_id_version_element_id ON api_case_element (project_id, case_id, version, element_id);
ALTER TABLE ui_plan MODIFY priority_type TINYINT DEFAULT 1 NOT NULL COMMENT '优先级（Default、Middle、High、Ultra、Low)' after cron_expression;

ALTER TABLE `git_configuration` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;
ALTER TABLE `git_project_tree` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;
ALTER TABLE `ui_plan` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;

CREATE TABLE IF NOT EXISTS `ui_plan_reference_relationship`
(
    `id`            INT(11)      NOT NULL COMMENT '自增ID' AUTO_INCREMENT,
    `project_id`    VARCHAR(64)  NOT NULL COMMENT '项目ID',
    `git_config_id` VARCHAR(64)  NOT NULL COMMENT 'Git配置ID',
    `path`          VARCHAR(255) NOT NULL COMMENT '节点路径（相对于根路径）',
    `plan_id`       VARCHAR(64)  NOT NULL COMMENT '计划ID',
    `deleted`       TINYINT(1)   NOT NULL DEFAULT 0 COMMENT '逻辑删除标识（未删除、已删除）',
    `created_by`    VARCHAR(64)  NOT NULL COMMENT '创建者的用户ID',
    `updated_by`    VARCHAR(64)  NOT NULL COMMENT '最近一次更新者的用户ID',
    `deleted_by`    VARCHAR(64)  NULL COMMENT '删除者的用户ID',
    `created_at`    TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at`    TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at`    TIMESTAMP    NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    KEY `ix_uprr_project_id_deleted` (`project_id`, `deleted`),
    KEY `ix_uprr_project_id_plan_id_deleted` (`project_id`, `plan_id`, `deleted`),
    KEY `ix_uprr_project_id_git_config_id_path_deleted` (`project_id`, `git_config_id`, `path`, `deleted`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = 'UI计划引用关系表';


-- 审核功能
ALTER TABLE `project` ADD `review_enabled` TINYINT(1) DEFAULT 0 NOT NULL COMMENT '是否开启审核功能' AFTER `description`;
ALTER TABLE `api_case` MODIFY `state` VARCHAR(64) DEFAULT '' NOT NULL COMMENT '用例状态（新、待实现、待维护、待审核、已上线）';
UPDATE `api_case` SET `state` = 'TO_BE_MAINTAINED' WHERE `state` != '1';
UPDATE `api_case` SET `state` = 'PUBLISHED' WHERE `state` = '1';
ALTER TABLE `interface_case` MODIFY `state` VARCHAR(64) DEFAULT '' NOT NULL COMMENT '用例状态（新、待实现、待维护、待审核、已上线）';
UPDATE `interface_case` SET `state` = 'TO_BE_MAINTAINED' WHERE `state` != '1';
UPDATE `interface_case` SET `state` = 'PUBLISHED' WHERE `state` = '1';

CREATE TABLE IF NOT EXISTS `review_record`
(
    `id`                   INT(11)       NOT NULL COMMENT '自增ID' AUTO_INCREMENT,
    `project_id`           VARCHAR(64)   NOT NULL COMMENT '项目ID',
    `review_id`            VARCHAR(64)   NOT NULL COMMENT '审核ID',
    `resource_type`        VARCHAR(64)   NOT NULL COMMENT '资源类型（业务组件、前置组件、后置组件、场景用例、接口用例）',
    `resource_id`          VARCHAR(64)   NOT NULL COMMENT '资源ID（组件组ID、场景用例ID、接口用例ID）',
    `resource_status`      VARCHAR(64)   NOT NULL COMMENT '资源状态（申请审核时的状态，如：待实现、待维护）',
    `remark_of_pending`    VARCHAR(1024) NULL COMMENT '申请时的备注',
    `remark_of_revoked`    VARCHAR(1024) NULL COMMENT '撤回时的备注',
    `remark_of_reviewed`   VARCHAR(1024) NULL COMMENT '审批时的备注',
    `assigned_reviewers`   JSON          NOT NULL COMMENT '指派的审核者',
    `status`               VARCHAR(64)   NOT NULL COMMENT '审核状态（待审核、已撤销、通过、驳回）',
    `deleted`              TINYINT(1)    NOT NULL DEFAULT 0 COMMENT '逻辑删除标识（未删除、已删除）',
    `created_by`           VARCHAR(64)   NOT NULL COMMENT '创建者的用户ID',
    `updated_by`           VARCHAR(64)   NOT NULL COMMENT '最近一次更新者的用户ID',
    `deleted_by`           VARCHAR(64)   NULL COMMENT '删除者的用户ID',
    `created_at`           TIMESTAMP     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at`           TIMESTAMP     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at`           TIMESTAMP     NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uix_rr_project_id_review_id` (`project_id`, `review_id`),
    KEY `ix_rr_project_id_deleted` (`project_id`, `deleted`),
    KEY `ix_rr_project_id_review_id_deleted` (`project_id`, `review_id`, `deleted`),
    KEY `ix_rr_project_id_resource_type_resource_id_deleted` (`project_id`, `resource_type`, `resource_id`, `deleted`),
    KEY `ix_rr_project_id_status_deleted` (`project_id`, `status`, `deleted`),
    KEY `ix_rr_project_id_created_by_deleted` (`project_id`, `created_by`, `deleted`),
    KEY `ix_rr_project_id_updated_by_deleted` (`project_id`, `updated_by`, `deleted`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '审核记录表';


-- 移除废弃的表
DROP TABLE IF EXISTS `ui_case_tree`;
