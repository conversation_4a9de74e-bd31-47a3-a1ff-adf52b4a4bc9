USE `manager`;


# `project`
DROP INDEX ix_project_id_deleted ON project;
CREATE INDEX ix_project_project_id_deleted ON project (project_id, deleted);
DROP INDEX uix_project_id ON project;
CREATE UNIQUE INDEX uix_project_project_id ON project (project_id);


# `category`
DROP INDEX ix_project_id_deleted ON category;
CREATE INDEX ix_c_project_id_deleted ON category (project_id, deleted);
DROP INDEX ix_project_id_type_category_type_deleted ON category;
CREATE INDEX ix_c_project_id_type_category_type_deleted ON category (project_id, type, category_type, deleted);
DROP INDEX ix_project_id_type_deleted ON category;
CREATE INDEX ix_c_project_id_type_deleted ON category (project_id, type, deleted);
DROP INDEX uix_project_id_type_category_id ON category;
CREATE UNIQUE INDEX uix_c_project_id_type_category_id ON category (project_id, type, category_id);


# `category_tree`
DROP INDEX ix_project_id_ancestor_deleted ON category_tree;
CREATE INDEX ix_ct_project_id_ancestor_deleted ON category_tree (project_id, ancestor, deleted);
DROP INDEX ix_project_id_deleted ON category_tree;
CREATE INDEX ix_ct_project_id_deleted ON category_tree (project_id, deleted);
DROP INDEX ix_project_id_descendant_deleted ON category_tree;
CREATE INDEX ix_ct_project_id_descendant_deleted ON category_tree (project_id, descendant, deleted);
DROP INDEX uix_project_id_ancestor_descendant_depth_index ON category_tree;
CREATE UNIQUE INDEX uix_ct_project_id_ancestor_descendant_depth_index ON category_tree (project_id, ancestor, descendant, depth, `index`);


# `general_configuration`
DROP INDEX ix_project_id_configuration_id_deleted ON general_configuration;
CREATE INDEX ix_gc_project_id_configuration_id_deleted ON general_configuration (project_id, config_id, deleted);
DROP INDEX ix_project_id_deleted ON general_configuration;
CREATE INDEX ix_gc_project_id_deleted ON general_configuration (project_id, deleted);
DROP INDEX uix_project_id_configuration_id ON general_configuration;
CREATE UNIQUE INDEX uix_gc_project_id_configuration_id ON general_configuration (project_id, config_id);


# `account_configuration`
DROP INDEX ix_project_id_configuration_id_deleted ON account_configuration;
CREATE INDEX ix_ac_project_id_configuration_id_deleted ON account_configuration (project_id, config_id, deleted);
DROP INDEX ix_project_id_deleted ON account_configuration;
CREATE INDEX ix_ac_project_id_deleted ON account_configuration (project_id, deleted);
DROP INDEX uix_project_id_configuration_id ON account_configuration;
CREATE UNIQUE INDEX uix_ac_project_id_configuration_id ON account_configuration (project_id, config_id);


# `component_group_reference_relationship`
DROP INDEX ix_project_id_component_group_id_deleted ON component_group_reference_relationship;
CREATE INDEX ix_cgrr_project_id_component_group_id_deleted ON component_group_reference_relationship (project_id, component_group_id, deleted);
DROP INDEX ix_project_id_deleted ON component_group_reference_relationship;
CREATE INDEX ix_cgrr_project_id_deleted ON component_group_reference_relationship (project_id, deleted);
DROP INDEX ix_project_id_reference_deleted ON component_group_reference_relationship;
CREATE INDEX ix_cgrr_project_id_reference_deleted ON component_group_reference_relationship (project_id, reference_type, reference_id, reference_version, deleted);


# `component_group_element`
CREATE UNIQUE INDEX uix_cge_project_id_component_group_id_version_element_id ON component_group_element (project_id, component_group_id, version, element_id);


# `api_case_element`
CREATE UNIQUE INDEX uix_ace_project_id_case_id_version_element_id ON api_case_element (project_id, case_id, version, element_id);


CREATE TABLE IF NOT EXISTS `function`
(
    `id`             INT(11)      NOT NULL AUTO_INCREMENT,
    `project_id`     VARCHAR(64)  NOT NULL COMMENT '项目ID',
    `name`           VARCHAR(64)  NOT NULL COMMENT '函数名称',
    `type`           VARCHAR(64)  NOT NULL COMMENT '函数类型（内置、自定义）',
    `category`       VARCHAR(64)  NOT NULL DEFAULT 'Other' COMMENT '函数分类',
    `description`    VARCHAR(255) NULL COMMENT '函数描述',
    `language`       VARCHAR(64)  NOT NULL DEFAULT 'PYTHON' COMMENT '编程语言',
    `content`        TEXT         NULL COMMENT '函数内容',
    `parameters`     JSON         NOT NULL COMMENT '参数列表',
    `returns`        JSON         NOT NULL COMMENT '返回值列表',
    `example`        TEXT         NULL COMMENT '函数使用例子',
    `version`        VARCHAR(64)  NOT NULL COMMENT '函数版本',
    `latest`         TINYINT(1)   NOT NULL DEFAULT '0' COMMENT '是否最新版本',
    `deleted`        TINYINT(1)   NOT NULL DEFAULT '0' COMMENT '逻辑删除标识（未删除、已删除）',
    `created_by`     VARCHAR(64)  NOT NULL COMMENT '创建者的用户ID',
    `updated_by`     VARCHAR(64)  NOT NULL COMMENT '最近一次更新者的用户ID',
    `deleted_by`     VARCHAR(64)  NULL COMMENT '删除者的用户ID',
    `created_at`     TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at`     TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at`     TIMESTAMP    NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uix_function_project_id_name_type_version` (`project_id`, `name`, `type`, `version`),
    KEY `ix_function_project_id_deleted` (`project_id`, `deleted`),
    KEY `ix_function_project_id_type_latest_deleted` (`project_id`, `type`, `latest`, `deleted`),
    KEY `ix_function_project_id_name_type_latest_deleted` (`project_id`, `name`, `type`, `latest`, `deleted`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT = '数据处理函数表';


CREATE TABLE IF NOT EXISTS `function_reference_relationship`
(
    `id`                INT(11)     NOT NULL AUTO_INCREMENT,
    `project_id`        VARCHAR(64) NOT NULL COMMENT '项目ID',
    `reference_type`    VARCHAR(64) NOT NULL COMMENT '引用类型（组件组、API用例）',
    `reference_id`      VARCHAR(64) NOT NULL COMMENT '引用ID（组件组ID、API用例ID）',
    `reference_version` VARCHAR(64) NOT NULL COMMENT '引用版本（组件组版本、API用例版本）',
    `function_name`     VARCHAR(64) NOT NULL COMMENT '函数名称',
    `function_type`     VARCHAR(64) NOT NULL COMMENT '函数类型（内置、自定义）',
    `deleted`           TINYINT(1)  NOT NULL DEFAULT '0' COMMENT '逻辑删除标识（未删除、已删除）',
    `created_by`        VARCHAR(64) NOT NULL COMMENT '创建者的用户ID',
    `updated_by`        VARCHAR(64) NOT NULL COMMENT '最近一次更新者的用户ID',
    `deleted_by`        VARCHAR(64) NULL COMMENT '删除者的用户ID',
    `created_at`        TIMESTAMP   NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at`        TIMESTAMP   NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at`        TIMESTAMP   NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    KEY `ix_frr_project_id_deleted` (`project_id`, `deleted`),
    KEY `ix_frr_project_id_function_deleted` (`project_id`, `function_name`, `function_type`, `deleted`),
    KEY `ix_frr_project_id_reference_deleted` (`project_id`, `reference_type`, `reference_id`, `reference_version`, `deleted`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT = '数据处理函数引用关系表';


ALTER TABLE category MODIFY type VARCHAR(64) NOT NULL COMMENT '类型，即所属的分类树类型（接口定义、组件组、用例、集合）';
ALTER TABLE category MODIFY category_type VARCHAR(64) NOT NULL COMMENT '分类类型（目录、文件（即叶子节点））';
ALTER TABLE category MODIFY root_type VARCHAR(64) NULL COMMENT '根分类类型（组件组：业务组件、前后置组件，接口定义：接口文档、数据模型）';
ALTER TABLE category CHANGE component_type node_type VARCHAR(64) NULL COMMENT '节点类型（组件组：业务单请求组件、业务行为组组件、前置组件、后置组件，接口定义：文档、数据模型）';
ALTER TABLE category CHANGE component_id node_id VARCHAR(64) NULL COMMENT '节点ID';
UPDATE category SET category_type = 'FILE' WHERE category_type = 'COMPONENT_GROUP';


CREATE TABLE IF NOT EXISTS `interface_document`
(
    `id`            INT(11)      NOT NULL AUTO_INCREMENT,
    `project_id`    VARCHAR(64)  NOT NULL COMMENT '项目ID',
    `category_id`   VARCHAR(64)  NOT NULL COMMENT '所属分类ID',
    `document_id`   VARCHAR(64)  NOT NULL COMMENT '接口ID',
    `name`          VARCHAR(64)  NOT NULL COMMENT '接口名称',
    `description`   VARCHAR(255) NULL COMMENT '接口描述',
    `type`          VARCHAR(16)  NOT NULL COMMENT '接口类型（HTTP、gRPC）',
    `mode`          VARCHAR(16)  NOT NULL COMMENT '创建方式（builtin、local、remote、manual）',
    `import_type`   VARCHAR(16)  NULL COMMENT '导入类型（OpenApi、gRPC、YApi、TT）',
    `status`        TINYINT(1)   NOT NULL DEFAULT 0 COMMENT '接口状态（NULL、开发中、测试中、已发布、已废弃）',
    `service`       VARCHAR(64)  NULL COMMENT '服务名称',
    `path`          VARCHAR(128) NOT NULL COMMENT '接口路径',
    `method`        VARCHAR(64)  NOT NULL COMMENT '接口方法',
    `data`          JSON         NOT NULL COMMENT '接口详细数据',
    `deleted`       TINYINT(1)   NOT NULL DEFAULT 0 COMMENT '逻辑删除标识（未删除、已删除）',
    `maintained_by` VARCHAR(64)  NULL COMMENT '维护者的用户ID',
    `created_by`    VARCHAR(64)  NOT NULL COMMENT '创建者的用户ID',
    `updated_by`    VARCHAR(64)  NOT NULL COMMENT '最近一次更新者的用户ID',
    `deleted_by`    VARCHAR(64)  NULL COMMENT '删除者的用户ID',
    `created_at`    TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at`    TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at`    TIMESTAMP    NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uix_id_project_id_document_id` (`project_id`, `document_id`),
    KEY `ix_id_project_id_deleted` (`project_id`, `deleted`),
    KEY `ix_id_project_id_name_deleted` (`project_id`, `name`, `deleted`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='接口文档表';


CREATE TABLE IF NOT EXISTS `interface_schema`
(
    `id`          INT(11)      NOT NULL AUTO_INCREMENT,
    `project_id`  VARCHAR(64)  NOT NULL COMMENT '项目ID',
    `category_id` VARCHAR(64)  NOT NULL COMMENT '所属分类ID',
    `schema_id`   VARCHAR(64)  NOT NULL COMMENT '接口数据模型ID',
    `full_name`   VARCHAR(128) NOT NULL COMMENT '接口数据模型完整名称',
    `name`        VARCHAR(64)  NOT NULL COMMENT '接口数据模型名称',
    `description` VARCHAR(255) NULL COMMENT '接口数据模型描述',
    `mode`        VARCHAR(16)  NOT NULL COMMENT '创建方式（builtin、local、remote、manual）',
    `import_type` VARCHAR(16)  NULL COMMENT '导入类型（OpenApi、gRPC、YApi、TT）',
    `data`        JSON         NOT NULL COMMENT '接口数据模型数据',
    `deleted`     TINYINT(1)   NOT NULL DEFAULT 0 COMMENT '逻辑删除标识（未删除、已删除）',
    `created_by`  VARCHAR(64)  NOT NULL COMMENT '创建者的用户ID',
    `updated_by`  VARCHAR(64)  NOT NULL COMMENT '最近一次更新者的用户ID',
    `deleted_by`  VARCHAR(64)  NULL COMMENT '删除者的用户ID',
    `created_at`  TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at`  TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at`  TIMESTAMP    NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uix_is_project_id_schema_id` (`project_id`, `schema_id`),
    KEY `ix_is_project_id_deleted` (`project_id`, `deleted`),
    KEY `ix_is_project_id_full_name_deleted` (`project_id`, `full_name`, `deleted`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='接口数据模型表';


CREATE TABLE IF NOT EXISTS `interface_schema_reference_relationship`
(
    `id`             INT(11)     NOT NULL AUTO_INCREMENT,
    `project_id`     VARCHAR(64) NOT NULL COMMENT '项目ID',
    `reference_type` VARCHAR(64) NOT NULL COMMENT '引用类型（接口文档、数据模型）',
    `reference_id`   VARCHAR(64) NOT NULL COMMENT '引用ID（文档ID、模型ID）',
    `schema_id`      VARCHAR(64) NOT NULL COMMENT '接口数据模型ID',
    `deleted`        TINYINT(1)  NOT NULL DEFAULT 0 COMMENT '逻辑删除标识（未删除、已删除）',
    `created_by`     VARCHAR(64) NOT NULL COMMENT '创建者的用户ID',
    `updated_by`     VARCHAR(64) NOT NULL COMMENT '最近一次更新者的用户ID',
    `deleted_by`     VARCHAR(64) NULL COMMENT '删除者的用户ID',
    `created_at`     TIMESTAMP   NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at`     TIMESTAMP   NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at`     TIMESTAMP   NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    KEY `ix_isrr_project_id_deleted` (`project_id`, `deleted`),
    KEY `ix_isrr_project_id_schema_id_deleted` (`project_id`, `schema_id`, `deleted`),
    KEY `ix_isrr_project_id_reference_deleted` (`project_id`, `reference_type`, `reference_id`, `deleted`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='接口数据模型引用关系表';


CREATE TABLE IF NOT EXISTS `interface_configuration`
(
    `id`                INT(11)      NOT NULL AUTO_INCREMENT,
    `project_id`        VARCHAR(64)  NOT NULL COMMENT '项目ID',
    `document_id`       VARCHAR(64)  NOT NULL COMMENT '接口ID',
    `configuration_id`  VARCHAR(64)  NOT NULL COMMENT '配置ID',
    `name`              VARCHAR(64)  NOT NULL COMMENT '配置名称',
    `description`       VARCHAR(255) NULL COMMENT '配置描述',
    `path`              VARCHAR(128) NOT NULL COMMENT '配置的请求路径',
    `method`            VARCHAR(64)  NOT NULL COMMENT '配置的请求方法',
    `data`              JSON         NOT NULL COMMENT '配置的详细数据',
    `input_parameters`  JSON         NOT NULL COMMENT '配置的输入参数列表',
    `output_parameters` JSON         NOT NULL COMMENT '配置的输出参数列表',
    `deleted`           TINYINT(1)   NOT NULL DEFAULT 0 COMMENT '逻辑删除标识（未删除、已删除）',
    `created_by`        VARCHAR(64)  NOT NULL COMMENT '创建者的用户ID',
    `updated_by`        VARCHAR(64)  NOT NULL COMMENT '最近一次更新者的用户ID',
    `deleted_by`        VARCHAR(64)  NULL COMMENT '删除者的用户ID',
    `created_at`        TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at`        TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at`        TIMESTAMP    NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uix_ic_project_id_document_id_configuration_id` (`project_id`, `document_id`, `configuration_id`),
    KEY `ix_ic_project_id_deleted` (`project_id`, `deleted`),
    KEY `ix_ic_project_id_document_id_deleted` (`project_id`, `document_id`, `deleted`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='接口组件配置表';


CREATE TABLE IF NOT EXISTS `interface_case`
(
    `id`             INT(11)      NOT NULL AUTO_INCREMENT,
    `project_id`     VARCHAR(64)  NOT NULL COMMENT '项目ID',
    `document_id`    VARCHAR(64)  NOT NULL COMMENT '接口ID',
    `case_id`        VARCHAR(64)  NOT NULL COMMENT '用例ID',
    `name`           VARCHAR(64)  NOT NULL COMMENT '用例名称',
    `description`    VARCHAR(255) NULL COMMENT '用例描述',
    `account_config` JSON         NOT NULL COMMENT '池账号配置数',
    `version`        VARCHAR(64)  NOT NULL COMMENT '用例版本',
    `priority`       TINYINT(1)   NOT NULL DEFAULT 0 COMMENT '优先级（NULL、P0、P1、P2、P3...）',
    `structure`      JSON         NOT NULL COMMENT '用例中各节点的关系结构',
    `latest`         TINYINT(1)   NOT NULL DEFAULT 0 COMMENT '是否最新版本',
    `deleted`        TINYINT(1)   NOT NULL DEFAULT 0 COMMENT '逻辑删除标识（未删除、已删除）',
    `maintained_by`  VARCHAR(64)  NULL COMMENT '维护者的用户ID',
    `created_by`     VARCHAR(64)  NOT NULL COMMENT '创建者的用户ID',
    `updated_by`     VARCHAR(64)  NOT NULL COMMENT '最近一次更新者的用户ID',
    `deleted_by`     VARCHAR(64)  NULL COMMENT '删除者的用户ID',
    `created_at`     TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at`     TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at`     TIMESTAMP    NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uix_ic_project_id_document_id_case_id_version` (`project_id`, `document_id`, `case_id`, `version`),
    KEY `ix_ic_project_id_deleted` (`project_id`, `deleted`),
    KEY `ix_ic_project_id_latest_deleted` (`project_id`, `latest`, `deleted`),
    KEY `ix_ic_project_id_document_id_name_deleted` (`project_id`, `document_id`, `name`, `deleted`),
    KEY `ix_ic_project_id_document_id_case_id_latest_deleted` (`project_id`, `document_id`, `case_id`, `latest`, `deleted`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='接口用例表';


DELIMITER ;;
CREATE PROCEDURE `manager`.`create_categories`()
BEGIN
    -- 定义变量
    -- `project_id`, `created_by`, `created_at`
    DECLARE pid VARCHAR(64);
    DECLARE cby VARCHAR(64);
    DECLARE cat TIMESTAMP;

    -- `category_ids`
    DECLARE cid1 VARCHAR(64);
    DECLARE cid2 VARCHAR(64);
    DECLARE cid21 VARCHAR(64);
    DECLARE cid211 VARCHAR(64);
    DECLARE cid212 VARCHAR(64);
    DECLARE cid213 VARCHAR(64);
    DECLARE cid214 VARCHAR(64);
    DECLARE cid215 VARCHAR(64);

    -- `schema_ids`
    DECLARE sid1 VARCHAR(64);
    DECLARE sid2 VARCHAR(64);
    DECLARE sid3 VARCHAR(64);
    DECLARE sid4 VARCHAR(64);
    DECLARE sid5 VARCHAR(64);

    DECLARE finished BOOLEAN DEFAULT FALSE;

    -- 定义游标，把 `SQL` 结果集赋值到游标
    DECLARE result CURSOR FOR SELECT `project_id`, `created_by`, `created_at` FROM `project` WHERE `deleted` = 0;
    DECLARE CONTINUE HANDLER FOR NOT FOUND SET finished = TRUE;

    -- 设置变量
    SET sid1 = 'interface_schema_id:builtin-tds-common-call-req';
    SET sid2 = 'interface_schema_id:builtin-tds-search-client';
    SET sid3 = 'interface_schema_id:builtin-tds-fix-dict';
    SET sid4 = 'interface_schema_id:builtin-tds-common-resp';
    SET sid5 = 'interface_schema_id:builtin-tds-error-resp';

    -- 打开游标
    OPEN result;

    -- 将游标中的值赋值给变量
    FETCH result INTO pid, cby, cat;

    WHILE NOT finished DO
        -- 生成 `category_id`
        SELECT CONCAT('category_id:', SUBSTRING(UUID(), 1, 21)) INTO cid1;
        SELECT CONCAT('category_id:', SUBSTRING(UUID(), 1, 21)) INTO cid2;
        SELECT CONCAT('category_id:', SUBSTRING(UUID(), 1, 21)) INTO cid21;
        SELECT CONCAT('category_id:', SUBSTRING(UUID(), 1, 21)) INTO cid211;
        SELECT CONCAT('category_id:', SUBSTRING(UUID(), 1, 21)) INTO cid212;
        SELECT CONCAT('category_id:', SUBSTRING(UUID(), 1, 21)) INTO cid213;
        SELECT CONCAT('category_id:', SUBSTRING(UUID(), 1, 21)) INTO cid214;
        SELECT CONCAT('category_id:', SUBSTRING(UUID(), 1, 21)) INTO cid215;

        -- `category` 表
        INSERT INTO `category` (`project_id`, `type`, `category_id`, `category_type`, `root_type`, `name`, `description`, `builtin`, `created_by`, `updated_by`, `created_at`, `updated_at`)
        VALUES (pid, 'INTERFACE_DOCUMENT', cid1, 'DIRECTORY', NULL, '全部接口文档', '全部接口文档', 1, cby, cby, cat, cat);
        INSERT INTO `category` (`project_id`, `type`, `category_id`, `category_type`, `root_type`, `name`, `description`, `builtin`, `created_by`, `updated_by`, `created_at`, `updated_at`)
        VALUES (pid, 'INTERFACE_SCHEMA', cid2, 'DIRECTORY', NULL, '全部数据模型', '全部数据模型', 1, cby, cby, cat, cat);
        INSERT INTO `category` (`project_id`, `type`, `category_id`, `category_type`, `root_type`, `name`, `description`, `builtin`, `created_by`, `updated_by`, `created_at`, `updated_at`)
        VALUES (pid, 'INTERFACE_SCHEMA', cid21, 'DIRECTORY', '全部数据模型', 'TDS服务', 'TDS服务', 1, cby, cby, cat, cat);
        INSERT INTO `category` (`project_id`, `type`, `category_id`, `category_type`, `root_type`, `node_type`, `node_id`, `name`, `description`, `builtin`, `created_by`, `updated_by`, `created_at`, `updated_at`)
        VALUES (pid, 'INTERFACE_SCHEMA', cid211, 'FILE', '全部数据模型', 'SCHEMA', sid1, 'CommonCallReq', NULL, 1, cby, cby, cat, cat);
        INSERT INTO `category` (`project_id`, `type`, `category_id`, `category_type`, `root_type`, `node_type`, `node_id`, `name`, `description`, `builtin`, `created_by`, `updated_by`, `created_at`, `updated_at`)
        VALUES (pid, 'INTERFACE_SCHEMA', cid212, 'FILE', '全部数据模型', 'SCHEMA', sid2, 'SearchClientForm', NULL, 1, cby, cby, cat, cat);
        INSERT INTO `category` (`project_id`, `type`, `category_id`, `category_type`, `root_type`, `node_type`, `node_id`, `name`, `description`, `builtin`, `created_by`, `updated_by`, `created_at`, `updated_at`)
        VALUES (pid, 'INTERFACE_SCHEMA', cid213, 'FILE', '全部数据模型', 'SCHEMA', sid3, 'FixDictFieldForm', NULL, 1, cby, cby, cat, cat);
        INSERT INTO `category` (`project_id`, `type`, `category_id`, `category_type`, `root_type`, `node_type`, `node_id`, `name`, `description`, `builtin`, `created_by`, `updated_by`, `created_at`, `updated_at`)
        VALUES (pid, 'INTERFACE_SCHEMA', cid214, 'FILE', '全部数据模型', 'SCHEMA', sid4, 'CommonResp', NULL, 1, cby, cby, cat, cat);
        INSERT INTO `category` (`project_id`, `type`, `category_id`, `category_type`, `root_type`, `node_type`, `node_id`, `name`, `description`, `builtin`, `created_by`, `updated_by`, `created_at`, `updated_at`)
        VALUES (pid, 'INTERFACE_SCHEMA', cid215, 'FILE', '全部数据模型', 'SCHEMA', sid5, 'ErrorResp', NULL, 1, cby, cby, cat, cat);

        -- `category_tree` 表
        INSERT INTO `category_tree` (`project_id`, `ancestor`, `descendant`, `depth`, `index`, `created_by`, `updated_by`, `created_at`, `updated_at`)
        VALUES (pid, cid1, cid1, 0, 1, cby, cby, cat, cat);
        INSERT INTO `category_tree` (`project_id`, `ancestor`, `descendant`, `depth`, `index`, `created_by`, `updated_by`, `created_at`, `updated_at`)
        VALUES (pid, cid2, cid2, 0, 1, cby, cby, cat, cat);

        INSERT INTO `category_tree` (`project_id`, `ancestor`, `descendant`, `depth`, `index`, `created_by`, `updated_by`, `created_at`, `updated_at`)
        VALUES (pid, cid21, cid21, 0, 1, cby, cby, cat, cat);
        INSERT INTO `category_tree` (`project_id`, `ancestor`, `descendant`, `depth`, `index`, `created_by`, `updated_by`, `created_at`, `updated_at`)
        VALUES (pid, cid2, cid21, 1, 1, cby, cby, cat, cat);

        INSERT INTO `category_tree` (`project_id`, `ancestor`, `descendant`, `depth`, `index`, `created_by`, `updated_by`, `created_at`, `updated_at`)
        VALUES (pid, cid211, cid211, 0, 1, cby, cby, cat, cat);
        INSERT INTO `category_tree` (`project_id`, `ancestor`, `descendant`, `depth`, `index`, `created_by`, `updated_by`, `created_at`, `updated_at`)
        VALUES (pid, cid21, cid211, 1, 1, cby, cby, cat, cat);
        INSERT INTO `category_tree` (`project_id`, `ancestor`, `descendant`, `depth`, `index`, `created_by`, `updated_by`, `created_at`, `updated_at`)
        VALUES (pid, cid2, cid211, 2, 1, cby, cby, cat, cat);

        INSERT INTO `category_tree` (`project_id`, `ancestor`, `descendant`, `depth`, `index`, `created_by`, `updated_by`, `created_at`, `updated_at`)
        VALUES (pid, cid212, cid212, 0, 1, cby, cby, cat, cat);
        INSERT INTO `category_tree` (`project_id`, `ancestor`, `descendant`, `depth`, `index`, `created_by`, `updated_by`, `created_at`, `updated_at`)
        VALUES (pid, cid21, cid212, 1, 2, cby, cby, cat, cat);
        INSERT INTO `category_tree` (`project_id`, `ancestor`, `descendant`, `depth`, `index`, `created_by`, `updated_by`, `created_at`, `updated_at`)
        VALUES (pid, cid2, cid212, 2, 1, cby, cby, cat, cat);

        INSERT INTO `category_tree` (`project_id`, `ancestor`, `descendant`, `depth`, `index`, `created_by`, `updated_by`, `created_at`, `updated_at`)
        VALUES (pid, cid213, cid213, 0, 1, cby, cby, cat, cat);
        INSERT INTO `category_tree` (`project_id`, `ancestor`, `descendant`, `depth`, `index`, `created_by`, `updated_by`, `created_at`, `updated_at`)
        VALUES (pid, cid21, cid213, 1, 3, cby, cby, cat, cat);
        INSERT INTO `category_tree` (`project_id`, `ancestor`, `descendant`, `depth`, `index`, `created_by`, `updated_by`, `created_at`, `updated_at`)
        VALUES (pid, cid2, cid213, 2, 1, cby, cby, cat, cat);

        INSERT INTO `category_tree` (`project_id`, `ancestor`, `descendant`, `depth`, `index`, `created_by`, `updated_by`, `created_at`, `updated_at`)
        VALUES (pid, cid214, cid214, 0, 1, cby, cby, cat, cat);
        INSERT INTO `category_tree` (`project_id`, `ancestor`, `descendant`, `depth`, `index`, `created_by`, `updated_by`, `created_at`, `updated_at`)
        VALUES (pid, cid21, cid214, 1, 4, cby, cby, cat, cat);
        INSERT INTO `category_tree` (`project_id`, `ancestor`, `descendant`, `depth`, `index`, `created_by`, `updated_by`, `created_at`, `updated_at`)
        VALUES (pid, cid2, cid214, 2, 1, cby, cby, cat, cat);

        INSERT INTO `category_tree` (`project_id`, `ancestor`, `descendant`, `depth`, `index`, `created_by`, `updated_by`, `created_at`, `updated_at`)
        VALUES (pid, cid215, cid215, 0, 1, cby, cby, cat, cat);
        INSERT INTO `category_tree` (`project_id`, `ancestor`, `descendant`, `depth`, `index`, `created_by`, `updated_by`, `created_at`, `updated_at`)
        VALUES (pid, cid21, cid215, 1, 5, cby, cby, cat, cat);
        INSERT INTO `category_tree` (`project_id`, `ancestor`, `descendant`, `depth`, `index`, `created_by`, `updated_by`, `created_at`, `updated_at`)
        VALUES (pid, cid2, cid215, 2, 1, cby, cby, cat, cat);

        -- `interface_schema` 表
        INSERT INTO `interface_schema` (`project_id`, `category_id`, `schema_id`, `full_name`, `name`, `description`, `mode`, `import_type`, `data`, `created_by`, `updated_by`, `created_at`, `updated_at`)
        VALUES (pid, cid21, sid1, 'tds.CommonCallReq', 'CommonCallReq', NULL, 'builtin', NULL, '{"title":"root","type":"object","index":0,"properties":{"api_name":{"title":"api_name","type":"string","description":"Api Name","index":2,"fieldRequired":true,"example":"ChannelEnter"},"client":{"title":"client","type":"schema","index":1,"fieldRequired":true,"ref":{"schemaId":"interface_schema_id:builtin-tds-search-client","fullName":"tds.SearchClientForm","displayName":"SearchClientForm"}},"fix_dict":{"title":"fix_dict","type":"schema","description":"Fix Dict","index":3,"ref":{"schemaId":"interface_schema_id:builtin-tds-fix-dict","fullName":"tds.FixDictFieldForm","displayName":"FixDictFieldForm"}},"headers":{"title":"headers","type":"string","description":"Headers","index":10},"metadata":{"title":"metadata","type":"string","description":"Metadata","index":9},"method":{"title":"method","type":"string","description":"Method","index":11},"proto_file":{"title":"proto_file","type":"string","description":"Proto File","index":5},"proto_json":{"title":"proto_json","type":"object","description":"ProtoBuf Json","index":4},"proto_msg":{"title":"proto_msg","type":"string","description":"Proto Msg","index":6},"req_msg":{"title":"req_msg","type":"string","description":"Req Msg","index":7},"stub_name":{"title":"stub_name","type":"string","description":"Stub Name","index":8}},"required":["client","api_name"]}', cby, cby, cat, cat);
        INSERT INTO `interface_schema` (`project_id`, `category_id`, `schema_id`, `full_name`, `name`, `description`, `mode`, `import_type`, `data`, `created_by`, `updated_by`, `created_at`, `updated_at`)
        VALUES (pid, cid21, sid2, 'tds.SearchClientForm', 'SearchClientForm', NULL, 'builtin', NULL, '{"title":"root","type":"object","index":0,"properties":{"account":{"title":"account","type":"string","description":"Account","index":2},"cid":{"title":"cid","type":"integer","description":"Client ID","index":1},"drv_type":{"title":"drv_type","type":"integer","description":"Driver Type","index":5,"enum":[1,2,3,4,5,6,7],"enums":[{"value":1,"description":"HTTP"},{"value":2,"description":"WS"},{"value":3,"description":"TCP"},{"value":4,"description":"KAFKA"},{"value":5,"description":"MYSQL"},{"value":6,"description":"REDIS"},{"value":7,"description":"GRPC"}]},"platform_type":{"title":"platform_type","type":"integer","description":"Platform Type","index":7,"enum":[1,3],"enums":[{"value":1,"description":"MOBILE"},{"value":3,"description":"PC"}]},"prod_type":{"title":"prod_type","type":"integer","description":"Product Type","index":6,"enum":[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15],"enums":[{"value":1,"description":"TT_APP"},{"value":2,"description":"YP_APP"},{"value":3,"description":"KJ_APP"},{"value":4,"description":"TT_WEB"},{"value":5,"description":"YP_WEB"},{"value":6,"description":"KJ_WEB"},{"value":7,"description":"MP"},{"value":8,"description":"DB"},{"value":9,"description":"TTCHAT_APP"},{"value":10,"description":"GRPC"},{"value":11,"description":"KAFKA"},{"value":12,"description":"REC"},{"value":13,"description":"WEFLY"},{"value":14,"description":"BPM"},{"value":15,"description":"TD"}]},"uid":{"title":"uid","type":"integer","description":"User ID","index":3},"uri":{"title":"uri","type":"string","description":"Uri","index":4}}}', cby, cby, cat, cat);
        INSERT INTO `interface_schema` (`project_id`, `category_id`, `schema_id`, `full_name`, `name`, `description`, `mode`, `import_type`, `data`, `created_by`, `updated_by`, `created_at`, `updated_at`)
        VALUES (pid, cid21, sid3, 'tds.FixDictFieldForm', 'FixDictFieldForm', NULL, 'builtin', NULL, '{"title":"root","type":"object","index":0,"properties":{"cmd":{"title":"cmd","type":"integer","description":"TT Cmd Value","index":1,"example":423},"packet":{"title":"packet","type":"string","description":"SD Packet Value","index":3},"protocol":{"title":"protocol","type":"string","description":"SD Protocol Value","index":2}}}', cby, cby, cat, cat);
        INSERT INTO `interface_schema` (`project_id`, `category_id`, `schema_id`, `full_name`, `name`, `description`, `mode`, `import_type`, `data`, `created_by`, `updated_by`, `created_at`, `updated_at`)
        VALUES (pid, cid21, sid4, 'tds.CommonResp', 'CommonResp', NULL, 'builtin', NULL, '{"title":"root","type":"object","index":0,"properties":{"apm_id":{"title":"apm_id","type":"string","index":7,"default":""},"code":{"title":"code","type":"integer","index":1,"fieldRequired":true,"default":200},"data":{"title":"data","type":"any","index":3,"fieldRequired":true},"elapsed_time":{"title":"elapsed_time","type":"number","index":4,"format":"float"},"message":{"title":"message","type":"string","index":2,"fieldRequired":true,"default":"请求成功"},"ret":{"title":"ret","type":"integer","index":5},"trace_id":{"title":"trace_id","type":"string","index":6,"default":""}},"required":["code","message","data"]}', cby, cby, cat, cat);
        INSERT INTO `interface_schema` (`project_id`, `category_id`, `schema_id`, `full_name`, `name`, `description`, `mode`, `import_type`, `data`, `created_by`, `updated_by`, `created_at`, `updated_at`)
        VALUES (pid, cid21, sid5, 'tds.ErrorResp', 'ErrorResp', NULL, 'builtin', NULL, '{"title":"root","type":"object","index":0,"properties":{"apm_id":{"title":"apm_id","type":"string","index":7,"default":""},"code":{"title":"code","type":"integer","index":1,"fieldRequired":true,"default":500},"data":{"title":"data","type":"null","index":3,"fieldRequired":true},"elapsed_time":{"title":"elapsed_time","type":"number","index":4,"format":"float"},"message":{"title":"message","type":"string","index":2,"fieldRequired":true,"default":"请求成功"},"ret":{"title":"ret","type":"integer","index":5},"trace_id":{"title":"trace_id","type":"string","index":6,"default":""}},"required":["code","message","data"]}', cby, cby, cat, cat);

        -- 将游标中的值赋值给变量
        FETCH result INTO pid, cby, cat;
    END WHILE;

    -- 关闭游标
    CLOSE result;
END;;
DELIMITER ;

CALL `manager`.`create_categories`();
DROP PROCEDURE IF EXISTS `manager`.`create_categories`;


ALTER TABLE api_case ADD priority TINYINT(1) DEFAULT 0 NOT NULL COMMENT '优先级（NULL、P0、P1、P2、P3...）' AFTER version;
ALTER TABLE api_case ADD maintained_by VARCHAR(64) NULL COMMENT '维护者的用户ID' AFTER deleted;


CREATE TABLE `interface_case_element`
(
    `id`           INT(11)     NOT NULL AUTO_INCREMENT,
    `project_id`   VARCHAR(64) NOT NULL COMMENT '项目ID',
    `case_id`      VARCHAR(64) NOT NULL COMMENT '用例ID',
    `version`      VARCHAR(64) NOT NULL COMMENT '用例版本',
    `element_id`   VARCHAR(64) NOT NULL COMMENT '元素ID',
    `element_type` VARCHAR(64) NOT NULL COMMENT '元素类型（点、线、框）',
    `data`         JSON        NOT NULL COMMENT '元素数据（不同类型的元素的数据结构不一样）',
    `deleted`      TINYINT(1)  NOT NULL DEFAULT 0 COMMENT '逻辑删除标识（未删除、已删除）',
    `created_by`   VARCHAR(64) NOT NULL COMMENT '创建者的用户ID',
    `updated_by`   VARCHAR(64) NOT NULL COMMENT '最近一次更新者的用户ID',
    `deleted_by`   VARCHAR(64) NULL COMMENT '删除者的用户ID',
    `created_at`   TIMESTAMP   NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at`   TIMESTAMP   NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at`   TIMESTAMP   NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uix_ace_project_id_case_id_version_element_id` (`project_id`, `case_id`, `version`, `element_id`),
    KEY `ix_ace_project_id_deleted` (`project_id`, `deleted`),
    KEY `ix_ace_project_id_case_id_version_deleted` (`project_id`, `case_id`, `version`, `deleted`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='接口用例元素表';


ALTER TABLE component MODIFY parent_id VARCHAR(64) NOT NULL COMMENT '父对象的ID（组件组ID、API用例ID、接口用例ID）';
ALTER TABLE component MODIFY parent_type VARCHAR(64) NOT NULL COMMENT '父对象的类型（组件组、API用例、接口用例）';
ALTER TABLE component MODIFY parent_version VARCHAR(64) NOT NULL COMMENT '父对象的版本（组件组版本、API用例版本、接口用例版本）';
ALTER TABLE component_group_reference_relationship MODIFY reference_type VARCHAR(64) NOT NULL COMMENT '引用类型（组件组、API用例、接口用例）';
ALTER TABLE component_group_reference_relationship MODIFY reference_id VARCHAR(64) NOT NULL COMMENT '引用ID（组件组ID、API用例ID、接口用例ID）';
ALTER TABLE component_group_reference_relationship MODIFY reference_version VARCHAR(64) NOT NULL COMMENT '引用版本（组件组版本、API用例版本、接口用例版本）';
ALTER TABLE function_reference_relationship MODIFY reference_type VARCHAR(64) NOT NULL COMMENT '引用类型（组件组、API用例、接口用例）';
ALTER TABLE function_reference_relationship MODIFY reference_id VARCHAR(64) NOT NULL COMMENT '引用ID（组件组ID、API用例ID、接口用例ID）';
ALTER TABLE function_reference_relationship MODIFY reference_version VARCHAR(64) NOT NULL COMMENT '引用版本（组件组版本、API用例版本、接口用例版本）';
