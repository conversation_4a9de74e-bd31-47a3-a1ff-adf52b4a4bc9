-- 设备按用途分组管理
CREATE TABLE IF NOT EXISTS `project_device`
(
    `id`         INT         NOT NULL AUTO_INCREMENT COMMENT '自增ID',
    `project_id` VARCHAR(64) NOT NULL COMMENT '项目ID',
    `udid`       VARCHAR(64) NOT NULL COMMENT '设备编号',
    `usage`      TINYINT     NOT NULL DEFAULT 1 COMMENT '用途（UI测试、稳定性测试）',
    `deleted`    TINYINT     NOT NULL DEFAULT 0 COMMENT '逻辑删除标识（未删除、已删除）',
    `created_by` VARCHAR(64) NOT NULL COMMENT '创建者的用户ID',
    `updated_by` VARCHAR(64) NOT NULL COMMENT '最近一次更新者的用户ID',
    `deleted_by` VARCHAR(64) NULL COMMENT '删除者的用户ID',
    `created_at` TIMESTAMP   NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` TIMESTAMP   NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at` TIMESTAMP   NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uix_pd_project_id_udid` (`project_id`, `udid`),
    KEY `ix_pd_project_id_usage` (`project_id`, `usage`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT ='项目设备表';


-- UI测试支持指定设备执行
ALTER TABLE `ui_plan` ADD `devices` JSON NULL COMMENT '指定的设备' AFTER `platform_type`;
ALTER TABLE `ui_plan` ADD `together` TINYINT NOT NULL DEFAULT 0 COMMENT '所选设备是否一起执行计划下的用例' AFTER `devices`;
ALTER TABLE `ui_plan` ADD INDEX `ix_up_project_id_devices_deleted` (`project_id`, (CAST(`devices` AS CHAR(255) ARRAY)), `deleted`);

CREATE TABLE IF NOT EXISTS `project_device_reference_relationship`
(
    `id`             INT         NOT NULL AUTO_INCREMENT COMMENT '自增ID',
    `project_id`     VARCHAR(64) NOT NULL COMMENT '项目ID',
    `reference_type` VARCHAR(64) NOT NULL COMMENT '引用类型（UI测试计划、稳定性测试计划）',
    `reference_id`   VARCHAR(64) NOT NULL COMMENT '引用ID（UI测试计划ID、稳定性测试计划ID）',
    `udid`           VARCHAR(64) NOT NULL COMMENT '设备编号',
    `deleted`        TINYINT     NOT NULL DEFAULT 0 COMMENT '逻辑删除标识（未删除、已删除）',
    `created_by`     VARCHAR(64) NOT NULL COMMENT '创建者的用户ID',
    `updated_by`     VARCHAR(64) NOT NULL COMMENT '最近一次更新者的用户ID',
    `deleted_by`     VARCHAR(64) NULL COMMENT '删除者的用户ID',
    `created_at`     TIMESTAMP   NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at`     TIMESTAMP   NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at`     TIMESTAMP   NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    KEY `ix_pdrr_project_id_deleted` (`project_id`, `deleted`),
    KEY `ix_pdrr_project_id_udid_deleted` (`project_id`, `udid`, `deleted`),
    KEY `ix_pdrr_project_id_reference_deleted` (`project_id`, `reference_type`, `reference_id`, `deleted`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT ='项目设备引用关系表';

CREATE TABLE IF NOT EXISTS `stability_plan`
(
    `id`                 INT          NOT NULL AUTO_INCREMENT COMMENT '自增ID',
    `project_id`         VARCHAR(64)  NOT NULL COMMENT '项目ID',
    `category_id`        VARCHAR(64)  NOT NULL COMMENT '所属分类ID',
    `plan_id`            VARCHAR(64)  NOT NULL COMMENT '计划ID',
    `name`               VARCHAR(64)  NOT NULL COMMENT '计划名称',
    `description`        VARCHAR(255) NULL     COMMENT '计划描述',
    `state`              TINYINT(1)   NOT NULL DEFAULT 1 COMMENT '计划状态（生效、失效）',
    `type`               VARCHAR(64)  NOT NULL COMMENT '计划类型（手动、定时）',
    `priority_type`      TINYINT      NOT NULL DEFAULT 1 COMMENT '优先级（Default、Middle、High、Ultra、Low)',
    `cron_expression`    VARCHAR(128) NULL     COMMENT '定时触发计划的Cron表达式',
    `tags`               JSON         NULL     COMMENT '标签',
    `account_config_id`  VARCHAR(64)  NULL     COMMENT '池账号配置ID',
    `device_type`        TINYINT(1)   NOT NULL DEFAULT 1 COMMENT '设备类型（真机、云手机）',
    `platform_type`      TINYINT(1)   NOT NULL DEFAULT 1 COMMENT '测试系统（Android、IOS）',
    `devices`            JSON         NULL     COMMENT '指定设备编号',
    `package_name`       VARCHAR(128) NOT NULL COMMENT '用于启动APP',
    `app_download_link`  VARCHAR(128) NOT NULL COMMENT 'APP下载地址',
    `duration`           INT          NOT NULL DEFAULT 30 COMMENT '运行时长，单位为分钟',
    `activities`         JSON         NULL     COMMENT 'activity白名单',
    `custom_script`      JSON         NULL     COMMENT '自定义脚本',
    `maintained_by`      VARCHAR(64)  NULL     COMMENT '维护者的用户ID',
    `deleted`            TINYINT(1)   NOT NULL DEFAULT 0 COMMENT '逻辑删除标识（未删除、已删除）',
    `created_by`         VARCHAR(64)  NOT NULL COMMENT '创建者的用户ID',
    `updated_by`         VARCHAR(64)  NOT NULL COMMENT '最近一次更新者的用户ID',
    `deleted_by`         VARCHAR(64)  NULL     COMMENT '删除者的用户ID',
    `created_at`         TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at`         TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at`         TIMESTAMP    NULL     COMMENT '删除时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uix_sp_project_id_plan_id` (`project_id`, `plan_id`),
    KEY `ix_sp_project_id_plan_id_deleted` (`project_id`, `plan_id`, `deleted`),
    KEY `ix_sp_project_id_deleted` (`project_id`, `deleted`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT ='稳定性测试计划表';

Create Table IF NOT EXISTS `lark_chat` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `project_id` varchar(64) COLLATE utf8mb4_general_ci NOT NULL COMMENT '项目ID',
  `type` varchar(16) COLLATE utf8mb4_general_ci NOT NULL COMMENT '测试类型',
  `chat_id` varchar(64) COLLATE utf8mb4_general_ci NOT NULL COMMENT '飞书群组ID',
  `name` varchar(64) COLLATE utf8mb4_general_ci NOT NULL COMMENT '飞书群组名称',
  `avatar` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '飞书群组头像URL',
  `description` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '飞书群组描述',
  `external` tinyint NOT NULL DEFAULT '0' COMMENT '是否是外部群',
  `status` varchar(32) COLLATE utf8mb4_general_ci NOT NULL COMMENT '飞书群组状态（正常、解散、解散并保留）',
  `deleted` tinyint NOT NULL DEFAULT '0' COMMENT '逻辑删除标识（未删除、已删除）',
  `created_by` varchar(64) COLLATE utf8mb4_general_ci NOT NULL COMMENT '创建者的用户ID',
  `updated_by` varchar(64) COLLATE utf8mb4_general_ci NOT NULL COMMENT '最近一次更新者的用户ID',
  `deleted_by` varchar(64) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '删除者的用户ID',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted_at` timestamp NULL DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`),
  KEY `ix_lc_chat_id` (`chat_id`),
  KEY `ix_lc_project_id_deleted` (`project_id`, `deleted`),
  KEY `ix_lc_project_id_type_deleted` (`project_id`, `type`, `deleted`)
) ENGINE = InnoDB AUTO_INCREMENT = 36 DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '测试通知飞书群组表';