USE `manager`;

-- auto-generated definition
create table advanced_search_condition
(
    id           int auto_increment
        primary key,
    condition_id varchar(64)                          not null comment '条件ID',
    front_name   varchar(255)                         not null comment '前端展示名称',
    compare      varchar(255)                         not null comment '例如：EQ、NE、GT',
    deleted      tinyint(1) default 0                 not null comment '是否已删除',
    created_at   timestamp  default CURRENT_TIMESTAMP not null comment '创建时间',
    updated_at   timestamp  default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间'
)
    comment '高级搜索条件';

create index advanced_search_condition_condition_id_index
    on advanced_search_condition (condition_id);

-- auto-generated definition
create table advanced_search_field
(
    id         int auto_increment
        primary key,
    project_id varchar(64)                          not null comment '项目ID',
    field_id   varchar(64)                          not null comment '高级搜索字段ID',
    scene_type varchar(255)                         not null comment '所属组件（如：API_SUITE, INTERFACE_DOCUMENT）',
    front_name varchar(255)                         not null comment '前端展示字段名称，可以是中文',
    field_name varchar(255)                         not null comment '对应表字段名',
    deleted    tinyint(1) default 0                 not null comment '是否已删除',
    created_at timestamp  default CURRENT_TIMESTAMP not null comment '创建时间',
    updated_at timestamp  default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间'
)
    comment '高级搜索字段';

create index advanced_search_field_belong_type_project_id_index
    on advanced_search_field (scene_type, project_id);


-- auto-generated definition
create table advanced_search_field_relationship
(
    id           int auto_increment
        primary key,
    project_id   varchar(64)                          null comment '项目ID',
    field_id     varchar(64)                          not null comment '字段ID',
    condition_id varchar(64)                          not null comment '对比条件ID',
    deleted      tinyint(1) default 0                 not null comment '是否已删除',
    created_at   timestamp  default CURRENT_TIMESTAMP not null comment '创建时间',
    updated_at   timestamp  default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间'
)
    comment '高级搜索类型关系';

create index advanced_search_field_relationship_condition_id_index
    on advanced_search_field_relationship (condition_id);

create index advanced_search_field_relationship_field_id_index
    on advanced_search_field_relationship (field_id);


-- auto-generated definition
create table notify
(
    id            int auto_increment
        primary key,
    project_id    varchar(64)                                 not null comment '项目ID',
    plan_id       varchar(64)                                 not null comment '计划ID',
    notify_id     varchar(64)                                 not null comment '通知ID',
    notify_mode   enum ('ONLY_FALSE_NOTIFY', 'ALWAYS_NOTIFY') not null comment '通知方式(ALWAYS_NOTIFY/ONLY_FALSE_NOTIFY)',
    notify_type   enum ('EMAIL', 'LARK_GROUP')                not null comment '通知类型：邮箱/飞书群',
    receiver_name varchar(64)                                 not null comment '接受者名称',
    receiver      varchar(255)                                not null comment '接受者主体',
    deleted       tinyint(1) default 0                        not null,
    created_by    varchar(64)                                 not null comment '创建者ID',
    updated_by    varchar(64)                                 not null comment '最近一次更新者的用户ID',
    deleted_by    varchar(64)                                 null comment '删除者的用户ID',
    created_at    timestamp  default CURRENT_TIMESTAMP        not null comment '创建时间',
    updated_at    timestamp  default CURRENT_TIMESTAMP        not null on update CURRENT_TIMESTAMP comment '更新时间',
    deleted_at    timestamp                                   null comment '删除时间',
    constraint notify_pk
        unique (project_id, notify_id)
)
    comment '通知实体';

create index notify_project_id_plan_id_index
    on notify (project_id, plan_id);



