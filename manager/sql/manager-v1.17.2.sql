CREATE TABLE IF NOT EXISTS `case_fail_stat`
(
    `id`         int                                    NOT NULL AUTO_INCREMENT COMMENT '自增ID',
    `project_id` varchar(64) COLLATE utf8mb4_general_ci NOT NULL COMMENT '项目ID',
    `case_id`    varchar(64) COLLATE utf8mb4_general_ci NOT NULL COMMENT '用例ID',
    `branch_id`  varchar(64) COLLATE utf8mb4_general_ci NOT NULL COMMENT '分支ID',
    `execute_id` varchar(64) COLLATE utf8mb4_general_ci NOT NULL COMMENT '执行ID',
    `case_type`  varchar(64) COLLATE utf8mb4_general_ci NOT NULL COMMENT '计划类型[API_CASE，INTERFACE_CASE]',
    `fail_count` int                                    NOT NULL DEFAULT '0' COMMENT '失败次数',
    `version`    int                                    NOT NULL DEFAULT '0' COMMENT 'version',
    `deleted`    tinyint NOT NULL DEFAULT '0' COMMENT '逻辑删除标识（未删除、已删除）',
    `created_by` varchar(64) COLLATE utf8mb4_general_ci NOT NULL COMMENT '创建者的用户ID',
    `updated_by` varchar(64) COLLATE utf8mb4_general_ci NOT NULL COMMENT '最近一次更新者的用户ID',
    `deleted_by` varchar(64) COLLATE utf8mb4_general_ci          DEFAULT NULL COMMENT '删除者的用户ID',
    `created_at` timestamp                              NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` timestamp                              NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at` timestamp NULL DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    KEY          `idx_cfs_pici` (`project_id`,`case_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='用例失败统计表'