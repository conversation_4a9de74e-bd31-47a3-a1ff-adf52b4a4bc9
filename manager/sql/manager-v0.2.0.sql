USE `manager`;

/* 一期的数据跟二期不兼容，因此需要清理一期的数据 */
TRUNCATE TABLE `component`;
TRUNCATE TABLE `component_group_element`;
TRUNCATE TABLE `api_case_element`;
TRUNCATE TABLE `component_group`;
TRUNCATE TABLE `api_case`;

ALTER TABLE `component_group`
    CHANGE `module_id` `category_id` VARCHAR(64) NOT NULL COMMENT '所属分类ID';
ALTER TABLE `api_case`
    CHANGE `module_id` `category_id` VARCHAR(64) NOT NULL COMMENT '所属分类ID';
ALTER TABLE `component_group`
    ADD `account_config` JSON NOT NULL COMMENT '池账号配置数' AFTER `output_parameters`;
ALTER TABLE `api_case`
    ADD `account_config` JSON NOT NULL COMMENT '池账号配置数' AFTER `description`;
ALTER TABLE `component_group`
    CHANGE `input_parameters` `imports` JSON NOT NULL COMMENT '组件组的输入参数列表';
ALTER TABLE `component_group`
    CHANGE `output_parameters` `exports` JSON NOT NULL COMMENT '组件组的输出参数列表';
ALTER TABLE `component_group`
    ADD `reference_structure` JSON NOT NULL COMMENT '组件组中引用时涉及的各节点的关系结构' AFTER `structure`;

CREATE TABLE IF NOT EXISTS `project`
(
    `id`          INT(11)      NOT NULL AUTO_INCREMENT,
    `project_id`  VARCHAR(64)  NOT NULL COMMENT '项目ID',
    `name`        VARCHAR(64)  NOT NULL COMMENT '组件名称',
    `description` VARCHAR(255) NULL COMMENT '组件描述',
    `deleted`     TINYINT(1)   NOT NULL DEFAULT '0' COMMENT '逻辑删除标识（未删除、已删除）',
    `created_by`  VARCHAR(64)  NOT NULL COMMENT '创建者的用户ID',
    `updated_by`  VARCHAR(64)  NOT NULL COMMENT '最近一次更新者的用户ID',
    `deleted_by`  VARCHAR(64)  NULL COMMENT '删除者的用户ID',
    `created_at`  TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at`  TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at`  TIMESTAMP    NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uix_project_id` (`project_id`),
    KEY `ix_project_id_deleted` (`project_id`, `deleted`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='项目表';

CREATE TABLE IF NOT EXISTS `category`
(
    `id`             INT(11)      NOT NULL AUTO_INCREMENT,
    `project_id`     VARCHAR(64)  NOT NULL COMMENT '项目ID',
    `type`           VARCHAR(64)  NOT NULL COMMENT '类型，即所属的分类树类型（组件组、用例、集合）',
    `category_id`    VARCHAR(64)  NOT NULL COMMENT '分类ID',
    `category_type`  VARCHAR(64)  NOT NULL COMMENT '分类类型（目录、组件组）',
    `root_type`      VARCHAR(64)  NULL COMMENT '根类型（业务组件、前后置组件）',
    `component_type` VARCHAR(64)  NULL COMMENT '组件组类型（业务单请求组件、业务行为组组件、前置组件、后置组件）',
    `component_id`   VARCHAR(64)  NULL COMMENT '组件组ID',
    `name`           VARCHAR(64)  NOT NULL COMMENT '分类名称',
    `description`    VARCHAR(255) NULL COMMENT '分类描述',
    `builtin`        TINYINT(1)   NOT NULL DEFAULT '0' COMMENT '是否内建分类',
    `deleted`        TINYINT(1)   NOT NULL DEFAULT '0' COMMENT '逻辑删除标识（未删除、已删除）',
    `created_by`     VARCHAR(64)  NOT NULL COMMENT '创建者的用户ID',
    `updated_by`     VARCHAR(64)  NOT NULL COMMENT '最近一次更新者的用户ID',
    `deleted_by`     VARCHAR(64)  NULL COMMENT '删除者的用户ID',
    `created_at`     TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at`     TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at`     TIMESTAMP    NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uix_project_id_type_category_id` (`project_id`, `type`, `category_id`),
    KEY `ix_project_id_deleted` (`project_id`, `deleted`),
    KEY `ix_project_id_type_deleted` (`project_id`, `type`, `deleted`),
    KEY `ix_project_id_type_category_type_deleted` (`project_id`, `type`, `category_type`, `deleted`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT = '分类表';

CREATE TABLE IF NOT EXISTS `category_tree`
(
    `id`         INT(11)     NOT NULL AUTO_INCREMENT,
    `project_id` VARCHAR(64) NOT NULL COMMENT '项目ID',
    `ancestor`   VARCHAR(64) NOT NULL COMMENT '祖先分类ID',
    `descendant` VARCHAR(64) NOT NULL COMMENT '后代分类ID',
    `depth`      TINYINT(4)  NOT NULL DEFAULT '0' COMMENT '深度',
    `index`      INT(11)     NOT NULL DEFAULT '0' COMMENT '序号（同一深度下按序号升序排列）',
    `deleted`    TINYINT(1)  NOT NULL DEFAULT '0' COMMENT '逻辑删除标识（未删除、已删除）',
    `created_by` VARCHAR(64) NOT NULL COMMENT '创建者的用户ID',
    `updated_by` VARCHAR(64) NOT NULL COMMENT '最近一次更新者的用户ID',
    `deleted_by` VARCHAR(64) NULL COMMENT '删除者的用户ID',
    `created_at` TIMESTAMP   NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` TIMESTAMP   NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at` TIMESTAMP   NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uix_project_id_ancestor_descendant_depth_index` (`project_id`, `ancestor`, `descendant`, `depth`, `index`),
    KEY `ix_project_id_deleted` (`project_id`, `deleted`),
    KEY `ix_project_id_ancestor_deleted` (`project_id`, `ancestor`, `deleted`),
    KEY `ix_project_id_descendant_deleted` (`project_id`, `descendant`, `deleted`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT = '分类树表';

CREATE TABLE IF NOT EXISTS `general_configuration`
(
    `id`               INT(11)      NOT NULL AUTO_INCREMENT,
    `project_id`       VARCHAR(64)  NOT NULL COMMENT '项目ID',
    `configuration_id` VARCHAR(64)  NOT NULL COMMENT '通用配置ID',
    `name`             VARCHAR(64)  NOT NULL COMMENT '通用配置名称',
    `description`      VARCHAR(255) NULL COMMENT '通用配置描述',
    `base_url`         VARCHAR(64)  NULL COMMENT 'HTTP请求基础URL',
    `verify`           TINYINT(1)            DEFAULT '0' COMMENT '是否验证服务器的TLS证书',
    `variables`        JSON         NOT NULL COMMENT '变量列表',
    `deleted`          TINYINT(1)   NOT NULL DEFAULT '0' COMMENT '逻辑删除标识（未删除、已删除）',
    `created_by`       VARCHAR(64)  NOT NULL COMMENT '创建者的用户ID',
    `updated_by`       VARCHAR(64)  NOT NULL COMMENT '最近一次更新者的用户ID',
    `deleted_by`       VARCHAR(64)  NULL COMMENT '删除者的用户ID',
    `created_at`       TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at`       TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at`       TIMESTAMP    NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uix_project_id_configuration_id` (`project_id`, `configuration_id`),
    KEY `ix_project_id_deleted` (`project_id`, `deleted`),
    KEY `ix_project_id_configuration_id_deleted` (`project_id`, `configuration_id`, `deleted`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT = '通用配置表';

CREATE TABLE IF NOT EXISTS `account_configuration`
(
    `id`               INT(11)      NOT NULL AUTO_INCREMENT,
    `project_id`       VARCHAR(64)  NOT NULL COMMENT '项目ID',
    `configuration_id` VARCHAR(64)  NOT NULL COMMENT '池账号配置ID',
    `name`             VARCHAR(64)  NOT NULL COMMENT '池账号配置名称',
    `description`      VARCHAR(255) NULL COMMENT '池账号配置描述',
    `product_type`     TINYINT(4)   NOT NULL COMMENT '产品类型',
    `product_name`     VARCHAR(64)  NOT NULL COMMENT '产品名称',
    `pool_env_table`   VARCHAR(64)  NOT NULL COMMENT '账号池环境表名称',
    `pool_env_name`    VARCHAR(64)  NOT NULL COMMENT '账号池环境名称',
    `deleted`          TINYINT(1)   NOT NULL DEFAULT '0' COMMENT '逻辑删除标识（未删除、已删除）',
    `created_by`       VARCHAR(64)  NOT NULL COMMENT '创建者的用户ID',
    `updated_by`       VARCHAR(64)  NOT NULL COMMENT '最近一次更新者的用户ID',
    `deleted_by`       VARCHAR(64)  NULL COMMENT '删除者的用户ID',
    `created_at`       TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at`       TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at`       TIMESTAMP    NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uix_project_id_configuration_id` (`project_id`, `configuration_id`),
    KEY `ix_project_id_deleted` (`project_id`, `deleted`),
    KEY `ix_project_id_configuration_id_deleted` (`project_id`, `configuration_id`, `deleted`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT = '池账号配置表';

CREATE TABLE IF NOT EXISTS `component_group_reference_relationship`
(
    `id`                 INT(11)     NOT NULL AUTO_INCREMENT,
    `project_id`         VARCHAR(64) NOT NULL COMMENT '项目ID',
    `reference_type`     VARCHAR(64) NOT NULL COMMENT '引用类型（组件组、API用例）',
    `reference_id`       VARCHAR(64) NOT NULL COMMENT '引用ID（组件组ID、API用例ID）',
    `reference_version`  VARCHAR(64) NOT NULL COMMENT '引用版本（组件组版本、API用例版本）',
    `component_group_id` VARCHAR(64) NOT NULL COMMENT '组件组ID，即被引用的ID',
    `deleted`            TINYINT(1)  NOT NULL DEFAULT '0' COMMENT '逻辑删除标识（未删除、已删除）',
    `created_by`         VARCHAR(64) NOT NULL COMMENT '创建者的用户ID',
    `updated_by`         VARCHAR(64) NOT NULL COMMENT '最近一次更新者的用户ID',
    `deleted_by`         VARCHAR(64) NULL COMMENT '删除者的用户ID',
    `created_at`         TIMESTAMP   NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at`         TIMESTAMP   NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at`         TIMESTAMP   NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    KEY `ix_project_id_deleted` (`project_id`, `deleted`),
    KEY `ix_project_id_component_group_id_deleted` (`project_id`, `component_group_id`, `deleted`),
    KEY `ix_project_id_reference_deleted` (`project_id`, `reference_type`, `reference_id`, `reference_version`, `deleted`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT = '组件组引用关系表';
