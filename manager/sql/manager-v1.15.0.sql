use `manager`;

update category set name='全部组件' where name='全部组件组';
update category set name='接口' where name='全部接口文档';

alter table ui_plan
    add app_name varchar(255) null comment '应用名称' after package_name;

alter table ui_plan
    add test_language tinyint(1) default 1 null comment '测试语言 0：Python 1：Golang' after app_version;

alter table ui_plan
    add test_language_version varchar(64) default '3.7.16' null comment '测试语言版本' after test_language;

alter table ui_plan
    add test_framework tinyint(1) default 1 null comment '测试框架 1：pytest' after test_language_version;

alter table ui_plan
    add test_args json null comment '附加参数' after test_framework;
