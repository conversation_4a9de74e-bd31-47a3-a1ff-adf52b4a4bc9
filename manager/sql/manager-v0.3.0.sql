USE `manager`;


-- 增加`tags`字段
ALTER TABLE `component_group` ADD `tags` JSON NULL COMMENT '标签' AFTER `description`;
ALTER TABLE `api_case` ADD `tags` JSON NULL COMMENT '标签' AFTER `description`;
ALTER TABLE `interface_document` ADD `tags` JSON NULL COMMENT '标签' AFTER `status`;
ALTER TABLE `interface_case` ADD `tags` JSON NULL COMMENT '标签' AFTER `description`;

-- 增加`state`字段
ALTER TABLE `api_case` ADD `state` TINYINT(1) NOT NULL DEFAULT 1 COMMENT '用例状态（生效、失效）' AFTER `tags`;
ALTER TABLE `interface_document` ADD `state` TINYINT(1) NOT NULL DEFAULT 1 COMMENT '接口集合状态（生效、失效）' AFTER `tags`;
ALTER TABLE `interface_case` ADD `state` TINYINT(1) NOT NULL DEFAULT 1 COMMENT '用例状态（生效、失效）' AFTER `tags`;

-- 增加`case_execution_mode`字段
ALTER TABLE `interface_document` ADD `case_execution_mode` TINYINT(1) NOT NULL DEFAULT 1 COMMENT '用例执行方式（并行、串行）' AFTER `state`;

-- 增加索引
CREATE INDEX `ix_ac_project_id_case_id_state_latest_deleted` ON `api_case` (`project_id`, `case_id`, `state`, `latest`, `deleted`);
CREATE INDEX `ix_ic_project_id_document_id_case_id_state_latest_deleted` ON `interface_case` (`project_id`, `document_id`, `case_id`, `state`, `latest`, `deleted`);
CREATE INDEX `ix_id_project_id_document_id_deleted` ON `interface_document` (`project_id`, `document_id`, `deleted`);
CREATE INDEX `ix_id_project_id_document_id_state_deleted` ON `interface_document` (`project_id`, `document_id`, `state`, `deleted`);


CREATE TABLE IF NOT EXISTS `tag`
(
    `id`          INT(11)      NOT NULL AUTO_INCREMENT,
    `project_id`  VARCHAR(64)  NOT NULL COMMENT '项目ID',
    `type`        VARCHAR(64)  NOT NULL COMMENT '标签类型（组件组、用例、集合、计划、接口文档）',
    `tag_id`      VARCHAR(64)  NOT NULL COMMENT '标签ID',
    `name`        VARCHAR(64)  NOT NULL COMMENT '标签名称',
    `description` VARCHAR(255) NULL COMMENT '标签描述',
    `status`      TINYINT(1)   NOT NULL DEFAULT 1 COMMENT '标签状态（生效、失效）',
    `deleted`     TINYINT(1)   NOT NULL DEFAULT '0' COMMENT '逻辑删除标识（未删除、已删除）',
    `created_by`  VARCHAR(64)  NOT NULL COMMENT '创建者的用户ID',
    `updated_by`  VARCHAR(64)  NOT NULL COMMENT '最近一次更新者的用户ID',
    `deleted_by`  VARCHAR(64)  NULL COMMENT '删除者的用户ID',
    `created_at`  TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at`  TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at`  TIMESTAMP    NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uix_tag_project_id_tag_id` (`project_id`, `tag_id`),
    KEY `ix_tag_project_id_deleted` (`project_id`, `deleted`),
    KEY `ix_tag_project_id_type_name_deleted` (`project_id`, `type`, `name`, `deleted`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT = '标签表';


CREATE TABLE IF NOT EXISTS `tag_reference_relationship`
(
    `id`                INT(11)     NOT NULL AUTO_INCREMENT,
    `project_id`        VARCHAR(64) NOT NULL COMMENT '项目ID',
    `reference_type`    VARCHAR(64) NOT NULL COMMENT '引用类型（组件组、API用例、API集合、API计划、接口文档、接口用例）',
    `reference_id`      VARCHAR(64) NOT NULL COMMENT '引用ID（组件组ID、API用例ID、API集合ID、API计划ID、接口文档ID、接口用例ID）',
    `reference_version` VARCHAR(64) NULL COMMENT '引用版本（API用例版本、接口用例版本）',
    `tag_id`            VARCHAR(64) NOT NULL COMMENT '标签ID',
    `deleted`           TINYINT(1)  NOT NULL DEFAULT '0' COMMENT '逻辑删除标识（未删除、已删除）',
    `created_by`        VARCHAR(64) NOT NULL COMMENT '创建者的用户ID',
    `updated_by`        VARCHAR(64) NOT NULL COMMENT '最近一次更新者的用户ID',
    `deleted_by`        VARCHAR(64) NULL COMMENT '删除者的用户ID',
    `created_at`        TIMESTAMP   NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at`        TIMESTAMP   NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at`        TIMESTAMP   NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    KEY `ix_trr_project_id_deleted` (`project_id`, `deleted`),
    KEY `ix_trr_project_id_tag_id_deleted` (`project_id`, `tag_id`, `deleted`),
    KEY `ix_trr_project_id_reference_deleted` (`project_id`, `reference_type`, `reference_id`, `reference_version`, `deleted`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT = '标签引用关系表';


CREATE TABLE IF NOT EXISTS `api_suite`
(
    `id`                  INT(11)      NOT NULL AUTO_INCREMENT,
    `project_id`          VARCHAR(64)  NOT NULL COMMENT '项目ID',
    `category_id`         VARCHAR(64)  NOT NULL COMMENT '所属分类ID',
    `suite_id`            VARCHAR(64)  NOT NULL COMMENT '集合ID',
    `name`                VARCHAR(64)  NOT NULL COMMENT '集合名称',
    `description`         VARCHAR(255) NULL COMMENT '集合描述',
    `tags`                JSON         NULL COMMENT '标签',
    `state`               TINYINT(1)   NOT NULL DEFAULT 1 COMMENT '集合状态（生效、失效）',
    `case_execution_mode` TINYINT(1)   NOT NULL DEFAULT 1 COMMENT '用例执行方式（并行、串行）',
    `deleted`             TINYINT(1)   NOT NULL DEFAULT 0 COMMENT '逻辑删除标识（未删除、已删除）',
    `maintained_by`       VARCHAR(64)  NULL COMMENT '维护者的用户ID',
    `created_by`          VARCHAR(64)  NOT NULL COMMENT '创建者的用户ID',
    `updated_by`          VARCHAR(64)  NOT NULL COMMENT '最近一次更新者的用户ID',
    `deleted_by`          VARCHAR(64)  NULL COMMENT '删除者的用户ID',
    `created_at`          TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at`          TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at`          TIMESTAMP    NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uix_as_project_id_suite_id` (`project_id`, `suite_id`),
    KEY `ix_as_project_id_suite_id_state_deleted` (`project_id`, `suite_id`, `state`, `deleted`),
    KEY `ix_as_project_id_suite_id_deleted` (`project_id`, `suite_id`, `deleted`),
    KEY `ix_as_project_id_deleted` (`project_id`, `deleted`),
    KEY `ix_as_project_id_name_deleted` (`project_id`, `name`, `deleted`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='API集合表';


CREATE TABLE IF NOT EXISTS `api_case_reference_relationship`
(
    `id`             INT(11)     NOT NULL AUTO_INCREMENT,
    `project_id`     VARCHAR(64) NOT NULL COMMENT '项目ID',
    `reference_type` VARCHAR(64) NOT NULL COMMENT '引用类型（API集合）',
    `reference_id`   VARCHAR(64) NOT NULL COMMENT '引用ID（API集合ID）',
    `case_id`        VARCHAR(64) NOT NULL COMMENT 'API用例ID',
    `deleted`        TINYINT(1)  NOT NULL DEFAULT 0 COMMENT '逻辑删除标识（未删除、已删除）',
    `created_by`     VARCHAR(64) NOT NULL COMMENT '创建者的用户ID',
    `updated_by`     VARCHAR(64) NOT NULL COMMENT '最近一次更新者的用户ID',
    `deleted_by`     VARCHAR(64) NULL COMMENT '删除者的用户ID',
    `created_at`     TIMESTAMP   NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at`     TIMESTAMP   NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at`     TIMESTAMP   NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    KEY `ix_acrr_project_id_deleted` (`project_id`, `deleted`),
    KEY `ix_acrr_project_id_reference_deleted` (`project_id`, `reference_type`, `reference_id`, `deleted`),
    KEY `ix_acrr_project_id_case_id_deleted` (`project_id`, `case_id`, `deleted`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='API用例引用关系表';


CREATE TABLE IF NOT EXISTS `api_plan`
(
    `id`                       INT(11)      NOT NULL AUTO_INCREMENT,
    `project_id`               VARCHAR(64)  NOT NULL COMMENT '项目ID',
    `plan_id`                  VARCHAR(64)  NOT NULL COMMENT '计划ID',
    `name`                     VARCHAR(64)  NOT NULL COMMENT '计划名称',
    `description`              VARCHAR(255) NULL COMMENT '计划描述',
    `tags`                     JSON         NULL COMMENT '标签',
    `state`                    TINYINT(1)   NOT NULL DEFAULT 1 COMMENT '计划状态（生效、失效）',
    `type`                     VARCHAR(64)  NOT NULL COMMENT '计划类型（手动、定时、接口）',
    `cron_expression`          VARCHAR(128) NULL COMMENT '定时触发计划的Cron表达式',
    `general_configuration_id` VARCHAR(64)  NULL COMMENT '通用配置ID',
    `suite_execution_mode`     TINYINT(1)   NOT NULL DEFAULT 1 COMMENT '集合执行方式（并行、串行）',
    `case_execution_mode`      TINYINT(1)   NOT NULL DEFAULT 1 COMMENT '用例执行方式（并行、串行）',
    `deleted`                  TINYINT(1)   NOT NULL DEFAULT 0 COMMENT '逻辑删除标识（未删除、已删除）',
    `maintained_by`            VARCHAR(64)  NULL COMMENT '维护者的用户ID',
    `created_by`               VARCHAR(64)  NOT NULL COMMENT '创建者的用户ID',
    `updated_by`               VARCHAR(64)  NOT NULL COMMENT '最近一次更新者的用户ID',
    `deleted_by`               VARCHAR(64)  NULL COMMENT '删除者的用户ID',
    `created_at`               TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at`               TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at`               TIMESTAMP    NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uix_ap_project_id_suite_id` (`project_id`, `plan_id`),
    KEY `ix_ap_project_id_plan_id_state_deleted` (`project_id`, `plan_id`, `state`, `deleted`),
    KEY `ix_ap_project_id_plan_id_deleted` (`project_id`, `plan_id`, `deleted`),
    KEY `ix_ap_project_id_deleted` (`project_id`, `deleted`),
    KEY `ix_ap_project_id_name_deleted` (`project_id`, `name`, `deleted`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='API计划表';


/*
CREATE TABLE IF NOT EXISTS `general_configuration_reference_relationship`
(
    `id`             INT(11)     NOT NULL AUTO_INCREMENT,
    `project_id`     VARCHAR(64) NOT NULL COMMENT '项目ID',
    `reference_type` VARCHAR(64) NOT NULL COMMENT '引用类型（API计划）',
    `reference_id`   VARCHAR(64) NOT NULL COMMENT '引用ID（API计划ID）',
    `config_id`      VARCHAR(64) NOT NULL COMMENT '配置ID',
    `deleted`        TINYINT(1)  NOT NULL DEFAULT '0' COMMENT '逻辑删除标识（未删除、已删除）',
    `created_by`     VARCHAR(64) NOT NULL COMMENT '创建者的用户ID',
    `updated_by`     VARCHAR(64) NOT NULL COMMENT '最近一次更新者的用户ID',
    `deleted_by`     VARCHAR(64) NULL COMMENT '删除者的用户ID',
    `created_at`     TIMESTAMP   NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at`     TIMESTAMP   NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at`     TIMESTAMP   NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    KEY `ix_psr_project_id_deleted` (`project_id`, `deleted`),
    KEY `ix_psr_project_id_reference_deleted` (`project_id`, `reference_type`, `reference_id`, `deleted`),
    KEY `ix_psr_project_id_config_id_deleted` (`project_id`, `config_id`, `deleted`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='通用配置引用关系表';
 */


CREATE TABLE IF NOT EXISTS `account_configuration_reference_relationship`
(
    `id`               INT(11)     NOT NULL AUTO_INCREMENT,
    `project_id`       VARCHAR(64) NOT NULL COMMENT '项目ID',
    `reference_type`   VARCHAR(64) NOT NULL COMMENT '引用类型（API计划）',
    `reference_id`     VARCHAR(64) NOT NULL COMMENT '引用ID（API计划ID）',
    `configuration_id` VARCHAR(64) NOT NULL COMMENT '配置ID',
    `deleted`          TINYINT(1)  NOT NULL DEFAULT 0 COMMENT '逻辑删除标识（未删除、已删除）',
    `created_by`       VARCHAR(64) NOT NULL COMMENT '创建者的用户ID',
    `updated_by`       VARCHAR(64) NOT NULL COMMENT '最近一次更新者的用户ID',
    `deleted_by`       VARCHAR(64) NULL COMMENT '删除者的用户ID',
    `created_at`       TIMESTAMP   NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at`       TIMESTAMP   NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at`       TIMESTAMP   NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    KEY `ix_acrr_project_id_deleted` (`project_id`, `deleted`),
    KEY `ix_acrr_project_id_reference_deleted` (`project_id`, `reference_type`, `reference_id`, `deleted`),
    KEY `ix_acrr_project_id_configuration_id_deleted` (`project_id`, `configuration_id`, `deleted`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='池账号配置引用关系表';


CREATE TABLE IF NOT EXISTS `api_plan_reference_relationship`
(
    `id`                  INT(11)     NOT NULL AUTO_INCREMENT,
    `project_id`          VARCHAR(64) NOT NULL COMMENT '项目ID',
    `reference_parent_id` VARCHAR(64) NULL COMMENT '引用父ID（API集合ID、接口ID）',
    `reference_type`      VARCHAR(64) NOT NULL COMMENT '引用类型（API用例、API集合、接口文档、接口用例）',
    `reference_id`        VARCHAR(64) NOT NULL COMMENT '引用ID（API用例ID、API集合ID、接口ID、接口用例ID）',
    `plan_id`             VARCHAR(64) NOT NULL COMMENT '计划ID',
    `state`               TINYINT(1)  NOT NULL DEFAULT 1 COMMENT '引用状态（已使用、未使用）',
    `deleted`             TINYINT(1)  NOT NULL DEFAULT 0 COMMENT '逻辑删除标识（未删除、已删除）',
    `created_by`          VARCHAR(64) NOT NULL COMMENT '创建者的用户ID',
    `updated_by`          VARCHAR(64) NOT NULL COMMENT '最近一次更新者的用户ID',
    `deleted_by`          VARCHAR(64) NULL COMMENT '删除者的用户ID',
    `created_at`          TIMESTAMP   NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at`          TIMESTAMP   NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at`          TIMESTAMP   NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    KEY `ix_aprr_project_id_deleted` (`project_id`, `deleted`),
    KEY `ix_aprr_project_id_reference1_deleted` (`project_id`, `reference_parent_id`, `deleted`),
    KEY `ix_aprr_project_id_reference2_deleted` (`project_id`, `reference_type`, `reference_id`, `deleted`),
    KEY `ix_aprr_project_id_plan_id_deleted` (`project_id`, `plan_id`, `deleted`),
    KEY `ix_aprr_project_id_plan_id_state_deleted` (`project_id`, `plan_id`, `state`, `deleted`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='API计划引用关系表';


-- CREATE TABLE IF NOT EXISTS `notification_configuration`
-- (
--     `id`         INT(11)      NOT NULL AUTO_INCREMENT,
--     `project_id` VARCHAR(64)  NOT NULL COMMENT '项目ID',
--     `config_id`  VARCHAR(64)  NOT NULL COMMENT '配置ID',
--     `type`       VARCHAR(64)  NOT NULL COMMENT '通知类型（邮件、飞书群）',
--     `name`       VARCHAR(64)  NOT NULL COMMENT '通知名称（收件人名称、飞书群名称）',
--     `data`       VARCHAR(128) NOT NULL COMMENT '通知数据（邮箱地址、飞书群WebHook地址）',
--     `deleted`    TINYINT(1)   NOT NULL DEFAULT 0 COMMENT '逻辑删除标识（未删除、已删除）',
--     `created_by` VARCHAR(64)  NOT NULL COMMENT '创建者的用户ID',
--     `updated_by` VARCHAR(64)  NOT NULL COMMENT '最近一次更新者的用户ID',
--     `deleted_by` VARCHAR(64)  NULL COMMENT '删除者的用户ID',
--     `created_at` TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
--     `updated_at` TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
--     `deleted_at` TIMESTAMP    NULL COMMENT '删除时间',
--     PRIMARY KEY (`id`),
--     UNIQUE KEY `uix_nc_project_id_configuration_id` (`project_id`, configuration_id),
--     KEY `ix_nc_project_id_deleted` (`project_id`, `deleted`),
--     KEY `ix_nc_project_id_configuration_id_deleted` (`project_id`, configuration_id, `deleted`)
-- ) ENGINE = InnoDB
--   DEFAULT CHARSET = utf8mb4 COMMENT ='通知配置表';
--
--
-- CREATE TABLE IF NOT EXISTS `notification_configuration_reference_relationship`
-- (
--     `id`             INT(11)     NOT NULL AUTO_INCREMENT,
--     `project_id`     VARCHAR(64) NOT NULL COMMENT '项目ID',
--     `reference_type` VARCHAR(64) NOT NULL COMMENT '引用类型（API计划）',
--     `reference_id`   VARCHAR(64) NOT NULL COMMENT '引用ID（API计划ID）',
--     `config_id`      VARCHAR(64) NOT NULL COMMENT '配置ID',
--     `deleted`        TINYINT(1)  NOT NULL DEFAULT 0 COMMENT '逻辑删除标识（未删除、已删除）',
--     `created_by`     VARCHAR(64) NOT NULL COMMENT '创建者的用户ID',
--     `updated_by`     VARCHAR(64) NOT NULL COMMENT '最近一次更新者的用户ID',
--     `deleted_by`     VARCHAR(64) NULL COMMENT '删除者的用户ID',
--     `created_at`     TIMESTAMP   NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
--     `updated_at`     TIMESTAMP   NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
--     `deleted_at`     TIMESTAMP   NULL COMMENT '删除时间',
--     PRIMARY KEY (`id`),
--     KEY `ix_ncrr_project_id_deleted` (`project_id`, `deleted`),
--     KEY `ix_ncrr_project_id_reference_deleted` (`project_id`, `reference_type`, `reference_id`, `deleted`),
--     KEY `ix_ncrr_project_id_configuration_id_deleted` (`project_id`, `configuration_id`, `deleted`)
-- ) ENGINE = InnoDB
--   DEFAULT CHARSET = utf8mb4 COMMENT ='通知配置引用关系表';


-- 把`configuration_id`改为`config_id`
ALTER TABLE account_configuration CHANGE configuration_id config_id VARCHAR(64) NOT NULL COMMENT '池账号配置ID';
ALTER TABLE general_configuration CHANGE configuration_id config_id VARCHAR(64) NOT NULL COMMENT '通用配置ID';
ALTER TABLE account_configuration_reference_relationship CHANGE configuration_id config_id VARCHAR(64) NOT NULL COMMENT '配置ID';
ALTER TABLE api_plan CHANGE general_configuration_id general_config_id VARCHAR(64) NULL COMMENT '通用配置ID';
ALTER TABLE interface_configuration CHANGE configuration_id config_id VARCHAR(64) NOT NULL COMMENT '配置ID';

-- 增加索引用于优化获取分类树接口
CREATE INDEX ix_id_project_id_category_id_deleted ON `interface_document` (`project_id`, `category_id`, `deleted`);
CREATE INDEX ix_is_project_id_category_id_deleted ON `interface_schema` (`project_id`, `category_id`, `deleted`);
CREATE INDEX ix_cg_project_id_category_id_latest_deleted ON `component_group` (`project_id`, `category_id`, `latest`, `deleted`);
CREATE INDEX ix_ac_project_id_category_id_latest_deleted ON `api_case` (`project_id`, `category_id`, `latest`, `deleted`);
CREATE INDEX ix_as_project_id_category_id_deleted ON `api_suite` (`project_id`, `category_id`, `deleted`);

-- 上一版本会把`state`字段错误地更新为`0`，因此这里把错误的数据更新回正确的状态
UPDATE `api_case` SET `state` = 1 WHERE `state` = 0;
UPDATE `interface_document` SET `state` = 1 WHERE `state` = 0;
UPDATE `interface_case` SET `state` = 1 WHERE `state` = 0;
