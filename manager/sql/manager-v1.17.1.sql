CREATE TABLE IF NOT EXISTS `plan_user_like_relationship`
(
    `id`            INT(11)      NOT NULL COMMENT '自增ID' AUTO_INCREMENT,
    `project_id`    VARCHAR(64)  NOT NULL COMMENT '项目ID',
    `plan_id`       VARCHAR(64)  NOT NULL COMMENT '计划ID',
    `account`       VARCHAR(255) NOT NULL COMMENT '用户id同创建者的用户ID',
    `plan_type`     TINYINT(1)   NOT NULL DEFAULT 1 COMMENT 'API:0,UI:1',
    `deleted`       TINYINT(1)   NOT NULL DEFAULT 0 COMMENT '逻辑删除标识（未删除、已删除）',
    `created_by`    VARCHAR(64)  NOT NULL COMMENT '创建者的用户ID',
    `updated_by`    VARCHAR(64)  NOT NULL COMMENT '最近一次更新者的用户ID',
    `deleted_by`    VARCHAR(64)  NULL COMMENT '删除者的用户ID',
    `created_at`    TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at`    TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at`    TIMESTAMP    NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    KEY `idx_pulr_ap` (`account`,`project_id`),
    KEY `idx_pulr_p` (`plan_id`)-- 支持删除用例
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COLLATE=utf8mb4_general_ci COMMENT = '计划用户收藏关系表';