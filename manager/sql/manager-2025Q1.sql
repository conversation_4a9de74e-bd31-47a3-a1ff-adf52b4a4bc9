-- 接口用例覆盖率统计
ALTER TABLE `project` MODIFY `review_enabled` TINYINT(1) DEFAULT 0 NOT NULL COMMENT '是否开启用例审核功能';
ALTER TABLE `project` ADD `coverage_enabled` TINYINT(1) DEFAULT 0 NOT NULL COMMENT '是否开启接口用例覆盖率统计功能' AFTER `review_enabled`;
ALTER TABLE `project` ADD `coverage_lark_chats` JSON NULL COMMENT '接口用例覆盖率飞书通知群组' AFTER `coverage_enabled`;

CREATE TABLE IF NOT EXISTS `interface_coverage`
(
    `id`              INT          NOT NULL AUTO_INCREMENT COMMENT '自增ID',
    `project_id`      VARCHAR(64)  NOT NULL COMMENT '项目ID',
    `team`            VARCHAR(128) NOT NULL COMMENT '团队名称',
    `number_of_apis`  INT          NOT NULL DEFAULT 0 COMMENT '接口数量',
    `number_of_cases` INT          NOT NULL DEFAULT 0 COMMENT '用例数量',
    `counted_at`      DATE         NOT NULL DEFAULT (CURRENT_DATE()) COMMENT '统计日期',
    `deleted`         TINYINT      NOT NULL DEFAULT 0 COMMENT '逻辑删除标识（未删除、已删除）',
    `created_by`      VARCHAR(64)  NOT NULL COMMENT '创建者的用户ID',
    `updated_by`      VARCHAR(64)  NOT NULL COMMENT '最近一次更新者的用户ID',
    `deleted_by`      VARCHAR(64)  NULL COMMENT '删除者的用户ID',
    `created_at`      TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at`      TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at`      TIMESTAMP    NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uix_ic_project_id_team_counted_at` (`project_id`, `team`, `counted_at`),
    KEY `ix_ic_project_id_team_deleted` (`project_id`, `team`, `deleted`),
    KEY `ix_ic_project_id_counted_at` (`project_id`, `counted_at`),
    KEY `ix_ic_project_id_updated_at` (`project_id`, `updated_at`),
    KEY `ix_ic_project_id_deleted` (`project_id`, `deleted`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT ='接口覆盖率表';

CREATE TABLE IF NOT EXISTS `interface_update_configuration`
(
    `id`              INT          NOT NULL AUTO_INCREMENT COMMENT '自增ID',
    `project_id`      VARCHAR(64)  NOT NULL COMMENT '项目ID',
    `type`            VARCHAR(16)  NOT NULL COMMENT '接口类型（OpenApi、gRPC、YApi、TT、TTMeta、Recommend）',
    `local_path`      VARCHAR(255) NOT NULL COMMENT '本地路径',
    `dep_local_paths` JSON         NULL COMMENT '依赖的本地路径',
    `deleted`         TINYINT      NOT NULL DEFAULT 0 COMMENT '逻辑删除标识（未删除、已删除）',
    `created_by`      VARCHAR(64)  NOT NULL COMMENT '创建者的用户ID',
    `updated_by`      VARCHAR(64)  NOT NULL COMMENT '最近一次更新者的用户ID',
    `deleted_by`      VARCHAR(64)  NULL COMMENT '删除者的用户ID',
    `created_at`      TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at`      TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at`      TIMESTAMP    NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uix_iic_project_id_type_local_path` (`project_id`, `type`, `local_path`),
    KEY `ix_iic_project_id_deleted` (`project_id`, `deleted`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT ='接口更新配置表';


-- 支持用户自定义压测终止规则
ALTER TABLE `perf_plan_case_relationship` COMMENT '压测计划与压测用例关系表';

CREATE TABLE IF NOT EXISTS `perf_plan_rule_relationship`
(
    `id`         INT         NOT NULL AUTO_INCREMENT COMMENT '自增ID',
    `project_id` VARCHAR(64) NOT NULL COMMENT '项目ID',
    `plan_id`    VARCHAR(64) NOT NULL COMMENT '压测计划ID',
    `rule_id`    VARCHAR(64) NOT NULL COMMENT '压测停止规则ID',
    `deleted`    TINYINT(1)  NOT NULL DEFAULT 0 COMMENT '逻辑删除标识（未删除、已删除）',
    `created_by` VARCHAR(64) NOT NULL COMMENT '创建者的用户ID',
    `updated_by` VARCHAR(64) NOT NULL COMMENT '最近一次更新者的用户ID',
    `deleted_by` VARCHAR(64) NULL COMMENT '删除者的用户ID',
    `created_at` TIMESTAMP   NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` TIMESTAMP   NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at` TIMESTAMP   NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    KEY `ix_pprr_project_id_deleted` (`project_id`, `deleted`),
    KEY `ix_pprr_project_id_rule_id_deleted` (`project_id`, `rule_id`, `deleted`),
    KEY `ix_pprr_project_id_plan_id_deleted` (`project_id`, `plan_id`, `deleted`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '压测计划与压测停止规则关系表';


-- 支持压测极速版PC端
ALTER TABLE `perf_plan_v2` MODIFY `protobuf_config_id` VARCHAR(64) NULL COMMENT 'Deprecated: Protobuf项目配置ID';

CREATE TABLE IF NOT EXISTS `protobuf_configuration_reference_relationship`
(
    `id`             INT         NOT NULL AUTO_INCREMENT COMMENT '自增ID',
    `project_id`     VARCHAR(64) NOT NULL COMMENT '项目ID',
    `reference_type` VARCHAR(64) NOT NULL COMMENT '引用类型（压测计划）',
    `reference_id`   VARCHAR(64) NOT NULL COMMENT '引用ID（压测计划ID）',
    `config_id`      VARCHAR(64) NOT NULL COMMENT 'Protobuf配置ID',
    `deleted`        TINYINT     NOT NULL DEFAULT 0 COMMENT '逻辑删除标识（未删除、已删除）',
    `created_by`     VARCHAR(64) NOT NULL COMMENT '创建者的用户ID',
    `updated_by`     VARCHAR(64) NOT NULL COMMENT '最近一次更新者的用户ID',
    `deleted_by`     VARCHAR(64) NULL COMMENT '删除者的用户ID',
    `created_at`     TIMESTAMP   NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at`     TIMESTAMP   NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at`     TIMESTAMP   NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    KEY `ix_pcrr_project_id_deleted` (`project_id`, `deleted`),
    KEY `ix_pcrr_project_id_reference_deleted` (`project_id`, `reference_type`, `reference_id`, `deleted`),
    KEY `ix_pcrr_project_id_config_id_deleted` (`project_id`, `config_id`, `deleted`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT ='Protobuf配置引用关系表';


-- 压力测试计划中添加用例的限流配置
ALTER TABLE `perf_plan_case_relationship` ADD `rate_limits` JSON NULL COMMENT '压测计划下的用例限流配置' AFTER `case_id`;

-- 压力测试计划中记录用例的目标RPS
ALTER TABLE `perf_plan_case_relationship` ADD `target_rps` INT NOT NULL DEFAULT 0 COMMENT '压测计划下用例的目标RPS' AFTER `rate_limits`;

-- 压力测试计划中添加计划的目录管理
ALTER TABLE `perf_plan_v2` ADD `category_id` VARCHAR(64) NOT NULL DEFAULT '' COMMENT '所属分类ID' AFTER `project_id`;


-- 通用配置区分接口测试和压力测试
ALTER TABLE `general_configuration` ADD `type` VARCHAR(16) NOT NULL COMMENT '类型（接口测试、压力测试）' AFTER `config_id`;
UPDATE `general_configuration` SET `type` = 'API' WHERE `type` = '';

ALTER TABLE `general_configuration`
    DROP INDEX `ix_gc_project_id_configuration_id_deleted`,
    DROP INDEX `ix_gc_project_id_deleted`,
    DROP INDEX `uix_gc_project_id_configuration_id`;
ALTER TABLE `general_configuration`
    ADD INDEX `ix_gc_project_id_config_id_deleted` (`project_id`, `config_id`, `deleted`),
    ADD INDEX `ix_gc_project_id_type_deleted` (`project_id`, `type`, `deleted`),
    ADD UNIQUE INDEX `uix_gc_project_id_config_id` (`project_id`, `config_id`);


-- 支持压测接口显示QPS参考值
CREATE TABLE IF NOT EXISTS `interface_metrics_reference`
(
    `id`              INT          NOT NULL AUTO_INCREMENT COMMENT '自增ID',
    `project_id`      VARCHAR(64)  NOT NULL COMMENT '项目ID',
    `protocol`        VARCHAR(64)  NOT NULL COMMENT '协议',
    `method`          VARCHAR(512) NOT NULL COMMENT '接口名称',
    `reference_qps`   INT          NULL COMMENT 'QPS推荐值',
    `deleted`         TINYINT      NOT NULL DEFAULT 0 COMMENT '逻辑删除标识（未删除、已删除）',
    `created_by`      VARCHAR(64)  NOT NULL COMMENT '创建者的用户ID',
    `updated_by`      VARCHAR(64)  NOT NULL COMMENT '最近一次更新者的用户ID',
    `deleted_by`      VARCHAR(64)  NULL COMMENT '删除者的用户ID',
    `created_at`      TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at`      TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at`      TIMESTAMP    NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uix_imr_project_id_protocol_method` (`project_id`, `protocol`, `method`),
    KEY `ix_imr_updated_at` (`updated_at`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT ='接口指标参考表';


-- 压测通知飞书群组
CREATE TABLE IF NOT EXISTS `perf_lark_chat`
(
    `id`          INT          NOT NULL AUTO_INCREMENT COMMENT '自增ID',
    `project_id`  VARCHAR(64)  NOT NULL COMMENT '项目ID',
    `chat_id`     VARCHAR(64)  NOT NULL COMMENT '飞书群组ID',
    `name`        VARCHAR(64)  NOT NULL COMMENT '飞书群组名称',
    `avatar`      VARCHAR(255) NULL COMMENT '飞书群组头像URL',
    `description` VARCHAR(255) NULL COMMENT '飞书群组描述',
    `external`    TINYINT      NOT NULL DEFAULT 0 COMMENT '是否是外部群',
    `status`      VARCHAR(32)  NOT NULL COMMENT '飞书群组状态（正常、解散、解散并保留）',
    `deleted`     TINYINT      NOT NULL DEFAULT 0 COMMENT '逻辑删除标识（未删除、已删除）',
    `created_by`  VARCHAR(64)  NOT NULL COMMENT '创建者的用户ID',
    `updated_by`  VARCHAR(64)  NOT NULL COMMENT '最近一次更新者的用户ID',
    `deleted_by`  VARCHAR(64)  NULL COMMENT '删除者的用户ID',
    `created_at`  TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at`  TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at`  TIMESTAMP    NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    KEY `ix_plc_project_id_deleted` (`project_id`, `deleted`),
    KEY `ix_plc_project_id_chat_id_deleted` (`project_id`, `chat_id`, `deleted`),
    KEY `ix_plc_project_id_name_deleted` (`project_id`, `name`, `deleted`),
    KEY `ix_plc_project_id_status_deleted` (`project_id`, `status`, `deleted`),
    KEY `ix_plc_project_id_name_status_deleted` (`project_id`, `name`, `status`, `deleted`),
    KEY `ix_plc_chat_id` (`chat_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT ='压测通知飞书群组表';


-- 支持压测添加自动拉群人员
CREATE TABLE IF NOT EXISTS `perf_lark_member`
(
    `id`              INT          NOT NULL AUTO_INCREMENT COMMENT '自增ID',
    `project_id`      VARCHAR(64)  NOT NULL COMMENT '项目ID',
    `account`         VARCHAR(64)  NOT NULL COMMENT '用户名（工号）',
    `fullname`        VARCHAR(128) NOT NULL COMMENT '姓名',
    `full_dept_name`  VARCHAR(256) NULL COMMENT '完整部门名称',
    `email`           VARCHAR(64)  NULL COMMENT '邮箱',
    `lark_user_id`    VARCHAR(64)  NULL COMMENT '飞书用户ID',
    `deleted`         TINYINT      NOT NULL DEFAULT 0 COMMENT '逻辑删除标识（未删除、已删除）',
    `created_by`      VARCHAR(64)  NOT NULL COMMENT '创建者的用户ID',
    `updated_by`      VARCHAR(64)  NOT NULL COMMENT '最近一次更新者的用户ID',
    `deleted_by`      VARCHAR(64)  NULL COMMENT '删除者的用户ID',
    `created_at`      TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at`      TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at`      TIMESTAMP    NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uix_plm_project_id_account` (`project_id`, `account`), -- 获取单个用户
    KEY `ix_plm_project_id_deleted` (`project_id`, `deleted`), -- 获取项目下没有被逻辑删除的所有用户
    KEY `ix_plm_project_id_updated_at_deleted` (`project_id`, `updated_at`, `deleted`) -- 逻辑删除该项目下不是当前更新时间的用户
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT ='压测飞书自动拉群成员添加表';


-- 压力测试计划中添加`提前通知`选项
ALTER TABLE `perf_plan_v2` ADD `advanced_notification` TINYINT NOT NULL DEFAULT 0 COMMENT '是否提前通知' AFTER `lark_chat_id`;

-- 接口测试中添加测试计划的目录管理
ALTER TABLE `api_plan` ADD `category_id` VARCHAR(64) NOT NULL COMMENT '所属分类ID' AFTER `project_id`;

-- UI测试中添加测试计划的目录管理
ALTER TABLE `ui_plan` ADD `category_id` VARCHAR(64) NOT NULL COMMENT '所属分类ID' AFTER `project_id`;

CREATE TABLE IF NOT EXISTS `api_suite_reference_relationship`
(
    `id`             INT(11)     NOT NULL AUTO_INCREMENT,
    `project_id`     VARCHAR(64) NOT NULL COMMENT '项目ID',
    `reference_type` VARCHAR(64) NOT NULL COMMENT '引用类型（用例类型）',
    `reference_id`   VARCHAR(64) NOT NULL COMMENT '引用ID（用例ID）',
    `suite_id`       VARCHAR(64) NOT NULL COMMENT 'API集合ID',
    `deleted`        TINYINT(1)  NOT NULL DEFAULT 0 COMMENT '逻辑删除标识（未删除、已删除）',
    `created_by`     VARCHAR(64) NOT NULL COMMENT '创建者的用户ID',
    `updated_by`     VARCHAR(64) NOT NULL COMMENT '最近一次更新者的用户ID',
    `deleted_by`     VARCHAR(64) NULL COMMENT '删除者的用户ID',
    `created_at`     TIMESTAMP   NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at`     TIMESTAMP   NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at`     TIMESTAMP   NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    KEY `ix_asrr_project_id_deleted` (`project_id`, `deleted`),
    KEY `ix_asrr_project_id_suite_id_deleted` (`project_id`, `suite_id`, `deleted`), -- 查询集合的所有用例
    KEY `ix_asrr_project_id_reference_deleted` (`project_id`, `reference_type`, `reference_id`, `deleted`) -- 查询用例的引用集合
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci
  COMMENT ='API集合引用关系表';
