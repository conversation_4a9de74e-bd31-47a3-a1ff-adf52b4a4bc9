CREATE DATABASE IF NOT EXISTS `manager` DEFAULT CHARSET = utf8mb4 COLLATE utf8mb4_general_ci;

USE `manager`;

CREATE TABLE IF NOT EXISTS `component`
(
    `id`             int(11)      NOT NULL AUTO_INCREMENT,
    `project_id`     varchar(64)  NOT NULL COMMENT '项目ID',
    `parent_id`      varchar(64)  NOT NULL COMMENT '父对象的ID（组件组ID、用例ID）',
    `parent_type`    varchar(64)  NOT NULL COMMENT '父对象的类型（组件组、API用例）',
    `parent_version` varchar(64)  NOT NULL COMMENT '父对象的版本',
    `component_id`   varchar(64)  NOT NULL COMMENT '组件ID',
    `component_type` varchar(64)  NOT NULL COMMENT '组件类型',
    `name`           varchar(64)  NOT NULL COMMENT '组件名称',
    `description`    varchar(255) NULL COMMENT '组件描述',
    `data`           json         NULL COMMENT '组件数据（不同类型的组件的数据结构不一样）',
    `deleted`        tinyint(1)   NOT NULL DEFAULT '0' COMMENT '逻辑删除标识（未删除、已删除）',
    `created_by`     varchar(64)  NOT NULL COMMENT '创建者的用户ID',
    `updated_by`     varchar(64)  NOT NULL COMMENT '最近一次更新者的用户ID',
    `deleted_by`     varchar(64)  NULL COMMENT '删除者的用户ID',
    `created_at`     timestamp    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at`     timestamp    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at`     timestamp    NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uix_component_project_referenced_version_component` (`project_id`, `parent_id`, `parent_type`, `parent_version`, `component_id`),
    KEY `ix_component_project_id_deleted` (`project_id`, `deleted`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='组件表';

CREATE TABLE IF NOT EXISTS `component_group`
(
    `id`                   int(11)      NOT NULL AUTO_INCREMENT,
    `project_id`           varchar(64)  NOT NULL COMMENT '项目ID',
    `module_id`            varchar(64)  NULL COMMENT '所属模块ID（不属于任一模块时为NULL）',
    `component_group_id`   varchar(64)  NOT NULL COMMENT '组件组ID',
    `component_group_type` varchar(64)  NOT NULL COMMENT '组件组类型',
    `name`                 varchar(64)  NOT NULL COMMENT '组件组名称',
    `description`          varchar(255) NULL COMMENT '组件组描述',
    `input_parameters`     json         NULL COMMENT '组件组的输入参数列表',
    `output_parameters`    json         NULL COMMENT '组件组的输出参数列表',
    `version`              varchar(64)  NOT NULL COMMENT '组件组版本',
    `structure`            json         NOT NULL COMMENT '组件组中各节点的关系结构',
    `latest`               tinyint(1)   NOT NULL DEFAULT '0' COMMENT '是否最新版本',
    `deleted`              tinyint(1)   NOT NULL DEFAULT '0' COMMENT '逻辑删除标识（未删除、已删除）',
    `created_by`           varchar(64)  NOT NULL COMMENT '创建者的用户ID',
    `updated_by`           varchar(64)  NOT NULL COMMENT '最近一次更新者的用户ID',
    `deleted_by`           varchar(64)  NULL COMMENT '删除者的用户ID',
    `created_at`           timestamp    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at`           timestamp    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at`           timestamp    NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uix_cg_project_id_component_group_id_version` (`project_id`, `component_group_id`, `version`),
    KEY `ix_cg_project_id_deleted` (`project_id`, `deleted`),
    KEY `ix_cg_project_id_latest_deleted` (`project_id`, `latest`, `deleted`),
    KEY `ix_cg_project_id_name_deleted` (`project_id`, `name`, `deleted`),
    KEY `ix_cg_project_id_component_group_id_latest_deleted` (`project_id`, `component_group_id`, `latest`, `deleted`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='组件组表';

CREATE TABLE IF NOT EXISTS `api_case`
(
    `id`          int(11)      NOT NULL AUTO_INCREMENT,
    `project_id`  varchar(64)  NOT NULL COMMENT '项目ID',
    `module_id`   varchar(64)  NULL COMMENT '所属模块ID（不属于任一模块时为NULL）',
    `case_id`     varchar(64)  NOT NULL COMMENT '用例ID',
    `name`        varchar(64)  NOT NULL COMMENT '用例名称',
    `description` varchar(255) NULL COMMENT '用例描述',
    `version`     varchar(64)  NOT NULL COMMENT '用例版本',
    `structure`   json         NOT NULL COMMENT '用例中各节点的关系结构',
    `latest`      tinyint(1)   NOT NULL DEFAULT '0' COMMENT '是否最新版本',
    `deleted`     tinyint(1)   NOT NULL DEFAULT '0' COMMENT '逻辑删除标识（未删除、已删除）',
    `created_by`  varchar(64)  NOT NULL COMMENT '创建者的用户ID',
    `updated_by`  varchar(64)  NOT NULL COMMENT '最近一次更新者的用户ID',
    `deleted_by`  varchar(64)  NULL COMMENT '删除者的用户ID',
    `created_at`  timestamp    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at`  timestamp    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at`  timestamp    NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uix_ac_project_id_case_id_version` (`project_id`, `case_id`, `version`),
    KEY `ix_ac_project_id_deleted` (`project_id`, `deleted`),
    KEY `ix_ac_project_id_latest_deleted` (`project_id`, `latest`, `deleted`),
    KEY `ix_ac_project_id_name_deleted` (`project_id`, `name`, `deleted`),
    KEY `ix_ac_project_id_case_id_latest_deleted` (`project_id`, `case_id`, `latest`, `deleted`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='API用例表';

CREATE TABLE IF NOT EXISTS `component_group_element`
(
    `id`                 int(11)     NOT NULL AUTO_INCREMENT,
    `project_id`         varchar(64) NOT NULL COMMENT '项目ID',
    `component_group_id` varchar(64) NOT NULL COMMENT '组件组ID',
    `version`            varchar(64) NOT NULL COMMENT '组件组版本',
    `element_id`         varchar(64) NOT NULL COMMENT '元素ID',
    `element_type`       varchar(64) NOT NULL COMMENT '元素类型（点、线、框）',
    `data`               json        NOT NULL COMMENT '元素数据（不同类型的元素的数据结构不一样）',
    `deleted`            tinyint(1)  NOT NULL DEFAULT '0' COMMENT '逻辑删除标识（未删除、已删除）',
    `created_by`         varchar(64) NOT NULL COMMENT '创建者的用户ID',
    `updated_by`         varchar(64) NOT NULL COMMENT '最近一次更新者的用户ID',
    `deleted_by`         varchar(64) NULL COMMENT '删除者的用户ID',
    `created_at`         timestamp   NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at`         timestamp   NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at`         timestamp   NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    # UNIQUE KEY `uix_cge_project_id_component_group_id_version_element_id` (`project_id`, `component_group_id`, `version`, `element_id`),
    KEY `ix_cge_project_id_deleted` (`project_id`, `deleted`),
    KEY `ix_cge_project_id_component_group_id_version_deleted` (`project_id`, `component_group_id`, `version`, `deleted`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='组件组元素表';

CREATE TABLE IF NOT EXISTS `api_case_element`
(
    `id`           int(11)     NOT NULL AUTO_INCREMENT,
    `project_id`   varchar(64) NOT NULL COMMENT '项目ID',
    `case_id`      varchar(64) NOT NULL COMMENT '用例ID',
    `version`      varchar(64) NOT NULL COMMENT '用例版本',
    `element_id`   varchar(64) NOT NULL COMMENT '元素ID',
    `element_type` varchar(64) NOT NULL COMMENT '元素类型（点、线、框）',
    `data`         json        NOT NULL COMMENT '元素数据（不同类型的元素的数据结构不一样）',
    `deleted`      tinyint(1)  NOT NULL DEFAULT '0' COMMENT '逻辑删除标识（未删除、已删除）',
    `created_by`   varchar(64) NOT NULL COMMENT '创建者的用户ID',
    `updated_by`   varchar(64) NOT NULL COMMENT '最近一次更新者的用户ID',
    `deleted_by`   varchar(64) NULL COMMENT '删除者的用户ID',
    `created_at`   timestamp   NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at`   timestamp   NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at`   timestamp   NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    # UNIQUE KEY `uix_ace_project_id_case_id_version_element_id` (`project_id`, `case_id`, `version`, `element_id`),
    KEY `ix_ace_project_id_deleted` (`project_id`, `deleted`),
    KEY `ix_ace_project_id_case_id_version_deleted` (`project_id`, `case_id`, `version`, `deleted`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='API用例元素表';

/* 下面两个表暂时没有用到
CREATE TABLE IF NOT EXISTS `component_group_tree` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `project_id` varchar(64) NOT NULL COMMENT '项目ID',
    `component_group_id` varchar(64) NOT NULL COMMENT '组件组ID',
    `version` varchar(64) NOT NULL COMMENT '组件组版本',
    `ancestor` varchar(64) NOT NULL COMMENT '祖先节点',
    `descendant` varchar(64) NOT NULL COMMENT '后代节点',
    `descendant_type` varchar(64) NOT NULL COMMENT '后代类型（组件、组件组）',
    `depth` tinyint(4) NOT NULL DEFAULT '0' COMMENT '深度',
    `index` int(11) NOT NULL DEFAULT '0' COMMENT '序号（同一深度下按序号升序排序）',
    `deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '逻辑删除标识（未删除、已删除）',
    `created_by` varchar(64) NOT NULL COMMENT '创建者的用户ID',
    `updated_by` varchar(64) NOT NULL COMMENT '最近一次更新者的用户ID',
    `deleted_by` varchar(64) DEFAULT NULL COMMENT '删除者的用户ID',
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at` timestamp NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    KEY `ix_cgt_project_id` (`project_id`),
    KEY `ix_cgt_project_id_component_group_id_version` (`project_id`,`component_group_id`,`version`),
    KEY `ix_cgt_project_id_ancestor` (`project_id`,`ancestor`),
    KEY `ix_cgt_project_id_descendant` (`project_id`,`descendant`),
    KEY `ix_cgt_project_id_deleted` (`project_id`,`deleted`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='组件组树结构关系表';

CREATE TABLE IF NOT EXISTS `api_case_tree` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `project_id` varchar(64) NOT NULL COMMENT '项目ID',
    `case_id` varchar(64) NOT NULL COMMENT 'API用例ID',
    `version` varchar(64) NOT NULL COMMENT 'API用例版本',
    `ancestor` varchar(64) NOT NULL COMMENT '祖先节点',
    `descendant` varchar(64) NOT NULL COMMENT '后代节点',
    `descendant_type` varchar(64) NOT NULL COMMENT '后代类型（组件、组件组）',
    `depth` tinyint(4) NOT NULL DEFAULT '0' COMMENT '深度',
    `index` int(11) NOT NULL DEFAULT '0' COMMENT '序号（同一深度下按序号升序排序）',
    `deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '逻辑删除标识（未删除、已删除）',
    `created_by` varchar(64) NOT NULL COMMENT '创建者的用户ID',
    `updated_by` varchar(64) NOT NULL COMMENT '最近一次更新者的用户ID',
    `deleted_by` varchar(64) DEFAULT NULL COMMENT '删除者的用户ID',
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at` timestamp NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    KEY `ix_act_project_id` (`project_id`),
    KEY `ix_act_project_id_case_id_version` (`project_id`,`case_id`,`version`),
    KEY `ix_act_project_id_ancestor` (`project_id`,`ancestor`),
    KEY `ix_act_project_id_descendant` (`project_id`,`descendant`),
    KEY `ix_act_project_id_deleted` (`project_id`,`deleted`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='API用例树结构关系表';
*/
