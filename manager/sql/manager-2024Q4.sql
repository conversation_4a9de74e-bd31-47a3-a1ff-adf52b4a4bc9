-- 统计用例7天内执行失败数量
ALTER TABLE `case_fail_stat` DROP COLUMN `execute_id`;

DROP INDEX `idx_cfs_pi` ON `case_fail_stat`;
DROP INDEX `idx_cfs_pici` ON `case_fail_stat`;
DROP INDEX `idx_cfs_picica` ON `case_fail_stat`;

CREATE INDEX `ix_cfs_project_id_case_id_updated_at` ON `case_fail_stat` (`project_id`, `case_id`, `updated_at`);
CREATE INDEX `ix_cfs_project_id_case_type_updated_at` ON `case_fail_stat` (`project_id`, `case_type`, `updated_at`);
CREATE INDEX `ix_ic_project_id_case_id_latest_deleted` ON `interface_case` (`project_id`, `case_id`, `latest`, `deleted`);


-- 常态化压测
CREATE TABLE IF NOT EXISTS `perf_case_v2`
(
    `id`              INT          NOT NULL AUTO_INCREMENT COMMENT '自增ID',
    `project_id`      VARCHAR(64)  NOT NULL COMMENT '项目ID',
    `category_id`     VARCHAR(64)  NOT NULL COMMENT '所属分类ID',
    `case_id`         VARCHAR(64)  NOT NULL COMMENT '压测用例ID',
    `name`            VARCHAR(64)  NOT NULL COMMENT '压测用例名称',
    `description`     VARCHAR(255) NULL COMMENT '压测用例描述',
    `tags`            JSON         NULL COMMENT '标签',
    `protocol`        VARCHAR(64)  NOT NULL COMMENT '协议（TT私有协议、TT登录压测场景专用、通用gRPC协议、通用HTTP协议）',
    `rate_limits`     JSON         NOT NULL COMMENT '限流配置',
    `setup_steps`     JSON         NULL COMMENT '前置步骤',
    `serial_steps`    JSON         NULL COMMENT '串行步骤',
    `parallel_steps`  JSON         NULL COMMENT '并行步骤',
    `teardown_steps`  JSON         NULL COMMENT '后置步骤',
    `number_of_steps` INT          NOT NULL DEFAULT 0 COMMENT '测试步骤数',
    `target_rps`      INT          NOT NULL DEFAULT 0 COMMENT '目标的RPS',
    `state`           TINYINT      NOT NULL DEFAULT 1 COMMENT '计划状态（生效、失效）',
    `deleted`         TINYINT      NOT NULL DEFAULT 0 COMMENT '逻辑删除标识（未删除、已删除）',
    `maintained_by`   VARCHAR(64)  NULL COMMENT '维护者的用户ID',
    `created_by`      VARCHAR(64)  NOT NULL COMMENT '创建者的用户ID',
    `updated_by`      VARCHAR(64)  NOT NULL COMMENT '最近一次更新者的用户ID',
    `deleted_by`      VARCHAR(64)  NULL COMMENT '删除者的用户ID',
    `created_at`      TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at`      TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at`      TIMESTAMP    NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uix_pc_project_id_case_id` (`project_id`, `case_id`),
    KEY `ix_pc_project_id_case_id_deleted` (`project_id`, `case_id`, `deleted`),
    KEY `ix_pc_project_id_case_id_state_deleted` (`project_id`, `case_id`, `state`, `deleted`),
    KEY `ix_pc_project_id_category_id_deleted` (`project_id`, `category_id`, `deleted`),
    KEY `ix_pc_project_id_protocol_deleted` (`project_id`, `protocol`, `deleted`),
    KEY `ix_pc_project_id_deleted` (`project_id`, `deleted`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT ='压测用例表';

CREATE TABLE IF NOT EXISTS `perf_case_step_v2`
(
    `id`            INT           NOT NULL AUTO_INCREMENT COMMENT '自增ID',
    `project_id`    VARCHAR(64)   NOT NULL COMMENT '项目ID',
    `case_id`       VARCHAR(64)   NOT NULL COMMENT '压测用例ID',
    `step_id`       VARCHAR(64)   NOT NULL COMMENT '压测用例步骤ID',
    `type`          VARCHAR(32)   NOT NULL COMMENT '压测用例步骤类型（前置、串行、并行、后置）',
    `index`         INT           NOT NULL DEFAULT 0 COMMENT '序号',
    `name`          VARCHAR(64)   NOT NULL COMMENT '压测用例步骤名称',
    `rate_limits`   JSON          NULL COMMENT '限流配置',
    `url`           VARCHAR(255)  NOT NULL DEFAULT '' COMMENT '请求URL',
    `method`        VARCHAR(255)  NOT NULL DEFAULT '' COMMENT '请求方法',
    `headers`       JSON          NULL COMMENT '请求头',
    `body`          VARCHAR(1024) NOT NULL DEFAULT '{}' COMMENT '请求体',
    `exports`       JSON          NULL COMMENT '提取参数',
    `sleep`         VARCHAR(32)   NOT NULL DEFAULT '0s' COMMENT '请求后休眠时间',
    `target_rps`    INT           NOT NULL DEFAULT 0 COMMENT '目标的RPS',
    `state`         TINYINT       NOT NULL DEFAULT 1 COMMENT '计划状态（生效、失效）',
    `deleted`       TINYINT       NOT NULL DEFAULT 0 COMMENT '逻辑删除标识（未删除、已删除）',
    `created_by`    VARCHAR(64)   NOT NULL COMMENT '创建者的用户ID',
    `updated_by`    VARCHAR(64)   NOT NULL COMMENT '最近一次更新者的用户ID',
    `deleted_by`    VARCHAR(64)   NULL COMMENT '删除者的用户ID',
    `created_at`    TIMESTAMP     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at`    TIMESTAMP     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at`    TIMESTAMP     NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uix_pcs_project_id_case_id_step_id` (`project_id`, `case_id`, `step_id`),
    KEY `ix_pcs_project_id_case_id_deleted` (`project_id`, `case_id`, `deleted`),
    KEY `ix_pcs_project_id_case_id_type_index_deleted` (`project_id`, `case_id`, `type`, `index`, `deleted`),
    KEY `ix_pcs_project_id_deleted` (`project_id`, `deleted`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT ='压测用例步骤表';

CREATE TABLE IF NOT EXISTS `perf_plan_v2`
(
    `id`                 INT          NOT NULL AUTO_INCREMENT COMMENT '自增ID',
    `project_id`         VARCHAR(64)  NOT NULL COMMENT '项目ID',
    `plan_id`            VARCHAR(64)  NOT NULL COMMENT '计划ID',
    `name`               VARCHAR(64)  NOT NULL COMMENT '计划名称',
    `description`        VARCHAR(255) NULL COMMENT '计划描述',
    `type`               VARCHAR(64)  NOT NULL COMMENT '计划类型（手动、定时、接口）',
    `cron_expression`    VARCHAR(128) NULL COMMENT '定时触发计划的Cron表达式',
    `tags`               JSON         NULL COMMENT '标签',
    `protocol`           VARCHAR(64)  NOT NULL COMMENT '协议（TT私有协议、TT登录压测场景专用、通用gRPC协议、通用HTTP协议）',
    `target_env`         VARCHAR(32)  NOT NULL COMMENT '目标环境（生产环境、测试环境）',
    `protobuf_config_id` VARCHAR(64)  NULL COMMENT 'Protobuf项目配置ID',
    `general_config_id`  VARCHAR(64)  NULL COMMENT '通用配置ID',
    `account_config_id`  VARCHAR(64)  NULL COMMENT '池账号配置ID',
    `auth_rate_limits`   JSON         NULL COMMENT '登录的限流配置',
    `custom_duration`    TINYINT      NOT NULL DEFAULT 0 COMMENT '是否自定义压测持续时长',
    `duration`           INT          NOT NULL DEFAULT 60 COMMENT '压测持续时长，单位为秒',
    `create_lark_chat`   TINYINT      NOT NULL DEFAULT 0 COMMENT '是否需要自动拉群',
    `lark_chat_id`       VARCHAR(64)  NULL COMMENT '通过自动拉群创建的飞书群ID',
    `state`              TINYINT      NOT NULL DEFAULT 1 COMMENT '计划状态（生效、失效）',
    `deleted`            TINYINT      NOT NULL DEFAULT 0 COMMENT '逻辑删除标识（未删除、已删除）',
    `maintained_by`      VARCHAR(64)  NULL COMMENT '维护者的用户ID',
    `created_by`         VARCHAR(64)  NOT NULL COMMENT '创建者的用户ID',
    `updated_by`         VARCHAR(64)  NOT NULL COMMENT '最近一次更新者的用户ID',
    `deleted_by`         VARCHAR(64)  NULL COMMENT '删除者的用户ID',
    `created_at`         TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at`         TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at`         TIMESTAMP    NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uix_pp_project_id_plan_id` (`project_id`, `plan_id`),
    KEY `ix_pp_project_id_plan_id_deleted` (`project_id`, `plan_id`, `deleted`),
    KEY `ix_pp_project_id_name_deleted` (`project_id`, `name`, `deleted`),
    KEY `ix_pp_project_id_protobuf_config_id_deleted` (`project_id`, `protobuf_config_id`, `deleted`),
    KEY `ix_pp_project_id_general_config_id_deleted` (`project_id`, `general_config_id`, `deleted`),
    KEY `ix_pp_project_id_account_config_id_deleted` (`project_id`, `account_config_id`, `deleted`),
    KEY `ix_pp_project_id_target_env_deleted` (`project_id`, `target_env`, `deleted`),
    KEY `ix_pp_project_id_deleted` (`project_id`, `deleted`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT ='压测计划表';

CREATE TABLE IF NOT EXISTS `perf_plan_case_relationship`
(
    `id`                 INT         NOT NULL AUTO_INCREMENT COMMENT '自增ID',
    `project_id`         VARCHAR(64) NOT NULL COMMENT '项目ID',
    `plan_id`            VARCHAR(64) NOT NULL COMMENT '压测计划ID',
    `case_id`            VARCHAR(64) NOT NULL COMMENT '压测用例ID',
    `perf_data_id`       VARCHAR(64) NOT NULL DEFAULT '' COMMENT '压测数据ID',
    `custom_vu`          TINYINT     NOT NULL DEFAULT 0 COMMENT '是否自定义虚拟用户数',
    `number_of_vu`       INT         NOT NULL DEFAULT 0 COMMENT '虚拟用户数',
    `custom_lg`          TINYINT     NOT NULL DEFAULT 0 COMMENT '是否自定义施压机资源',
    `number_of_lg`       INT         NOT NULL DEFAULT 1 COMMENT '施压机数',
    `requests_of_cpu`    VARCHAR(32) NOT NULL DEFAULT '1' COMMENT '最小分配的CPU资源',
    `requests_of_memory` VARCHAR(32) NOT NULL DEFAULT '100MiB' COMMENT '最小分配的内存资源',
    `limits_of_cpu`      VARCHAR(32) NOT NULL DEFAULT '1' COMMENT '最大分配的CPU资源',
    `limits_of_memory`   VARCHAR(32) NOT NULL DEFAULT '100MiB' COMMENT '最大分配的内存资源',
    `estimated_duration` INT         NOT NULL DEFAULT 0 COMMENT '预估压测时长，单位为秒',
    `state`              TINYINT     NOT NULL DEFAULT 1 COMMENT '引用状态（已使用、未使用）',
    `deleted`            TINYINT     NOT NULL DEFAULT 0 COMMENT '逻辑删除标识（未删除、已删除）',
    `created_by`         VARCHAR(64) NOT NULL COMMENT '创建者的用户ID',
    `updated_by`         VARCHAR(64) NOT NULL COMMENT '最近一次更新者的用户ID',
    `deleted_by`         VARCHAR(64) NULL COMMENT '删除者的用户ID',
    `created_at`         TIMESTAMP   NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at`         TIMESTAMP   NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at`         TIMESTAMP   NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    KEY `ix_ppcr_project_id_deleted` (`project_id`, `deleted`),
    KEY `ix_ppcr_project_id_plan_id_deleted` (`project_id`, `plan_id`, `deleted`),
    KEY `ix_ppcr_project_id_case_id_deleted` (`project_id`, `case_id`, `deleted`),
    KEY `ix_ppcr_project_id_plan_id_state_deleted` (`project_id`, `plan_id`, `state`, `deleted`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT ='压测计划引用关系表';


-- 压测停止规则
CREATE TABLE IF NOT EXISTS `perf_stop_rule`
(
    `id`              INT          NOT NULL AUTO_INCREMENT COMMENT '自增ID',
    `project_id`      VARCHAR(64)  NOT NULL COMMENT '项目ID',
    `rule_id`         VARCHAR(64)  NOT NULL COMMENT '压测停止规则ID',
    `name`            VARCHAR(64)  NOT NULL COMMENT '压测停止规则名称',
    `description`     VARCHAR(255) NULL COMMENT '压测停止规则描述',
    `metric_type`     VARCHAR(16)  NOT NULL COMMENT '指标类型',
    `threshold`       FLOAT        NOT NULL COMMENT '阈值，保留2位小数（不同的指标类型单位不同）',
    `duration`        INT          NOT NULL COMMENT '持续时间，单位为秒',
    `state`           TINYINT      NOT NULL DEFAULT 1 COMMENT '规则状态（生效、失效）',
    `deleted`         TINYINT      NOT NULL DEFAULT 0 COMMENT '逻辑删除标识（未删除、已删除）',
    `created_by`      VARCHAR(64)  NOT NULL COMMENT '创建者的用户ID',
    `updated_by`      VARCHAR(64)  NOT NULL COMMENT '最近一次更新者的用户ID',
    `deleted_by`      VARCHAR(64)  NULL COMMENT '删除者的用户ID',
    `created_at`      TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at`      TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at`      TIMESTAMP    NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uix_psr_project_id_rule_id` (`project_id`, `rule_id`),
    KEY `ix_psr_project_id_deleted` (`project_id`, `deleted`),
    KEY `ix_psr_project_id_metric_type_deleted` (`project_id`, `metric_type`, `deleted`),
    KEY `ix_psr_project_id_state_deleted` (`project_id`, `state`, `deleted`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT ='压测停止规则表';
