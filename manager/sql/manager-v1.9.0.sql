CREATE TABLE IF NOT EXISTS `git_configuration`
(
    `id`           INT(11)      NOT NULL COMMENT '自增ID' AUTO_INCREMENT,
    `project_id`   VARCHAR(64)  NOT NULL COMMENT '项目ID',
    `config_id`    VARCHAR(64)  NOT NULL COMMENT 'Git配置ID',
    `type`         VARCHAR(16)  NOT NULL DEFAULT 'GitLab' COMMENT 'Git类型（GitLab、GitHub、Gitee）',
    `name`         VARCHAR(64)  NOT NULL COMMENT 'Git配置名称',
    `description`  VARCHAR(255) NULL COMMENT 'Git配置描述',
    `url`          VARCHAR(255) NOT NULL COMMENT 'Git项目URL（http）',
    `access_token` VARCHAR(128) NOT NULL COMMENT 'Git项目访问令牌',
    `branch`       VARCHAR(128) NOT NULL COMMENT 'Git项目分支名称',
    `deleted`      TINYINT(1)   NOT NULL DEFAULT 0 COMMENT '逻辑删除标识（未删除、已删除）',
    `created_by`   VARCHAR(64)  NOT NULL COMMENT '创建者的用户ID',
    `updated_by`   VARCHAR(64)  NOT NULL COMMENT '最近一次更新者的用户ID',
    `deleted_by`   VARCHAR(64)  NULL COMMENT '删除者的用户ID',
    `created_at`   TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at`   TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at`   TIMESTAMP    NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uix_gc_project_id_config_id` (`project_id`, `config_id`),
    KEY `ix_gc_project_id_deleted` (`project_id`, `deleted`),
    KEY `ix_gc_project_id_config_id_deleted` (`project_id`, `config_id`, `deleted`),
    KEY `ix_gc_project_id_type_deleted` (`project_id`, `type`, `deleted`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT = 'Git配置表';

CREATE TABLE IF NOT EXISTS `git_project_tree`
(
    `id`            INT(11)      NOT NULL COMMENT '自增ID' AUTO_INCREMENT,
    `project_id`    VARCHAR(64)  NOT NULL COMMENT '项目ID',
    `git_config_id` VARCHAR(64)  NOT NULL COMMENT 'Git配置ID',
    `path`          VARCHAR(255) NOT NULL COMMENT '节点路径（相对于根路径）',
    `parent_path`   VARCHAR(255) NULL COMMENT '父节点路径',
    `name`          VARCHAR(64)  NOT NULL COMMENT '节点名称',
    `alias`         VARCHAR(64)  NULL COMMENT '节点别名',
    `type`          VARCHAR(64)  NOT NULL COMMENT '节点类型（目录、文件、包、模块、类、函数）',
    `tags`          JSON         NULL COMMENT '标签',
    `triggered_by`  VARCHAR(64)  NOT NULL COMMENT '触发者的用户ID',
    `triggered_at`  TIMESTAMP    NOT NULL COMMENT '触发时间',
    `deleted`       TINYINT(1)   NOT NULL DEFAULT 0 COMMENT '逻辑删除标识（未删除、已删除）',
    `created_by`    VARCHAR(64)  NOT NULL COMMENT '创建者的用户ID',
    `updated_by`    VARCHAR(64)  NOT NULL COMMENT '最近一次更新者的用户ID',
    `deleted_by`    VARCHAR(64)  NULL COMMENT '删除者的用户ID',
    `created_at`    TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at`    TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at`    TIMESTAMP    NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    KEY `ix_gpt_project_id_deleted` (`project_id`, `deleted`),
    KEY `ix_gpt_project_id_git_config_id_deleted` (`project_id`, `git_config_id`, `deleted`),
    KEY `ix_gpt_project_id_git_config_id_path_deleted` (`project_id`, `git_config_id`, `path`, `deleted`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT = 'Git项目树表';

CREATE TABLE IF NOT EXISTS `ui_plan`
(
    `id`                       INT(11)      NOT NULL COMMENT '自增ID' AUTO_INCREMENT,
    `project_id`               VARCHAR(64)  NOT NULL COMMENT '项目ID',
    `plan_id`                  VARCHAR(64)  NOT NULL COMMENT '计划ID',
    `name`                     VARCHAR(64)  NOT NULL COMMENT '计划名称',
    `description`              VARCHAR(255) NULL COMMENT '计划描述',
    `type`                     VARCHAR(64)  NOT NULL COMMENT '计划类型（手动、定时、接口）',
    `cron_expression`          VARCHAR(128) NULL COMMENT '定时触发计划的Cron表达式',
    `git_config_id`            VARCHAR(64)  NOT NULL COMMENT 'Git配置ID',
    `execution_mode`           TINYINT(1)   NOT NULL DEFAULT 1 COMMENT '用例执行方式（并行、串行）',
    `platform_type`            TINYINT(1)   NOT NULL DEFAULT 1 COMMENT '测试系统（Android、IOS）',
    `package_name`             VARCHAR(128) NOT NULL COMMENT '用于启动APP',
    `callback_url`             VARCHAR(128) NULL COMMENT '回调地址',
    `app_download_link`        VARCHAR(128) NOT NULL COMMENT 'APP下载地址',
    `app_version`              VARCHAR(64)  NULL COMMENT 'APP版本',
    `execution_environment`    VARCHAR(64)  NULL COMMENT '执行环境',
    `fail_retry`               TINYINT(1)   NOT NULL DEFAULT 0 COMMENT '失败重试（0次、1次、2次）',
    `state`                    TINYINT(1)   NOT NULL DEFAULT 1 COMMENT '计划状态（1生效、2失效）',
    `deleted`                  TINYINT(1)   NOT NULL DEFAULT 0 COMMENT '逻辑删除标识（未删除、已删除）',
    `maintained_by`            VARCHAR(64)  NULL COMMENT '维护者的用户ID',
    `created_by`               VARCHAR(64)  NOT NULL COMMENT '创建者的用户ID',
    `updated_by`               VARCHAR(64)  NOT NULL COMMENT '最近一次更新者的用户ID',
    `deleted_by`               VARCHAR(64)  NULL COMMENT '删除者的用户ID',
    `created_at`               TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at`               TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at`               TIMESTAMP    NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uix_uiap_project_id_suite_id` (`project_id`, `plan_id`),
    KEY `ix_uiap_project_id_plan_id_deleted` (`project_id`, `plan_id`, `deleted`),
    KEY `ix_uiap_project_id_name_deleted` (`project_id`, `name`, `deleted`)
) ENGINE = InnoDB
    DEFAULT CHARSET = utf8mb4 COMMENT ='UI计划表';

CREATE TABLE IF NOT EXISTS `ui_case_tree`
(
    `id`                        INT(11)      NOT NULL COMMENT '自增ID' AUTO_INCREMENT,
    `project_id`                VARCHAR(64)  NOT NULL COMMENT '项目ID',
    `plan_id`                   VARCHAR(64)  NOT NULL COMMENT 'Git配置ID',
    `path`                      VARCHAR(255) NOT NULL COMMENT '节点路径（相对于根路径）',
    `parent_path`               VARCHAR(255) NULL COMMENT '父节点路径',
    `name`                      VARCHAR(64)  NOT NULL COMMENT '节点名称',
    `alias`                     VARCHAR(64)  NULL COMMENT '节点别名',
    `type`                      VARCHAR(64)  NOT NULL COMMENT '节点类型（目录、文件、包、模块、类、函数）',
    `tags`                      VARCHAR(64)  NULL COMMENT '标签',
    `deleted`                   TINYINT(1)   NOT NULL DEFAULT 0 COMMENT '逻辑删除标识（未删除、已删除）',
    `created_by`                VARCHAR(64)  NOT NULL COMMENT '创建者的用户ID',
    `updated_by`                VARCHAR(64)  NOT NULL COMMENT '最近一次更新者的用户ID',
    `deleted_by`                VARCHAR(64)  NULL COMMENT '删除者的用户ID',
    `created_at`                TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at`                TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at`                TIMESTAMP    NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    KEY `ix_uct_project_id_deleted` (`project_id`, `deleted`),
    KEY `ix_uct_project_id_git_plan_id_path_deleted` (`project_id`, `plan_id`, `path`, `deleted`),
    KEY `ix_uct_project_id_plan_id_deleted` (`project_id`, `plan_id`, `deleted`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='ui用例树表';

ALTER TABLE manager.api_plan ADD `purpose` VARCHAR(64) NOT NULL DEFAULT 'NORMAL' comment '计划用途（普通、精准测试）' AFTER `type`;
