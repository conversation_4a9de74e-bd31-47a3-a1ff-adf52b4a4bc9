-- 修改`api_plan`的索引名称
drop index `uix_ap_project_id_suite_id` on `api_plan`;
create unique index `uix_ap_project_id_plan_id` on `api_plan` (`project_id`, `plan_id`);


-- 压力测试平台化
ALTER TABLE `git_configuration` ADD `purpose` VARCHAR(16) NOT NULL COMMENT '用途（API测试、UI测试）' AFTER `branch`;
ALTER TABLE `notify` MODIFY `notify_type` ENUM ('EMAIL', 'LARK_GROUP', 'LARK_CHAT') NOT NULL COMMENT '通知类型：邮箱/飞书群';

UPDATE `git_configuration` SET `purpose` = 'UI' WHERE `purpose` = '';

CREATE TABLE IF NOT EXISTS `protobuf_configuration`
(
    `id`            INT          NOT NULL COMMENT '自增ID' AUTO_INCREMENT,
    `project_id`    VARCHAR(64)  NOT NULL COMMENT '项目ID',
    `config_id`     VARCHAR(64)  NOT NULL COMMENT 'Protobuf配置ID',
    `name`          VARCHAR(64)  NOT NULL COMMENT 'Protobuf配置名称',
    `description`   VARCHAR(255) NULL COMMENT 'Protobuf配置描述',
    `git_config_id` VARCHAR(64)  NOT NULL COMMENT 'Git配置ID',
    `import_path`   VARCHAR(255) NOT NULL DEFAULT '.' COMMENT '导入路径（相对路径）',
    `exclude_paths` JSON         NULL COMMENT '排除的路径（相对路径）',
    `exclude_files` JSON         NULL COMMENT '排除的文件（相对路径）',
    `deleted`       TINYINT      NOT NULL DEFAULT 0 COMMENT '逻辑删除标识（未删除、已删除）',
    `created_by`    VARCHAR(64)  NOT NULL COMMENT '创建者的用户ID',
    `updated_by`    VARCHAR(64)  NOT NULL COMMENT '最近一次更新者的用户ID',
    `deleted_by`    VARCHAR(64)  NULL COMMENT '删除者的用户ID',
    `created_at`    TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at`    TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at`    TIMESTAMP    NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uix_pc_project_id_config_id` (`project_id`, `config_id`),
    KEY `ix_pc_project_id_deleted` (`project_id`, `deleted`),
    KEY `ix_pc_project_id_config_id_deleted` (`project_id`, `config_id`, `deleted`),
    KEY `ix_pc_project_id_git_config_id_deleted` (`project_id`, `git_config_id`, `deleted`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = 'Protobuf配置表';

CREATE TABLE IF NOT EXISTS `protobuf_dependence`
(
    `id`            INT          NOT NULL COMMENT '自增ID' AUTO_INCREMENT,
    `project_id`    VARCHAR(64)  NOT NULL COMMENT '项目ID',
    `config_id`     VARCHAR(64)  NOT NULL COMMENT 'Protobuf配置ID',
    `dep_config_id` VARCHAR(64)  NOT NULL COMMENT '依赖的Protobuf配置ID',
    `deleted`       TINYINT      NOT NULL DEFAULT 0 COMMENT '逻辑删除标识（未删除、已删除）',
    `created_by`    VARCHAR(64)  NOT NULL COMMENT '创建者的用户ID',
    `updated_by`    VARCHAR(64)  NOT NULL COMMENT '最近一次更新者的用户ID',
    `deleted_by`    VARCHAR(64)  NULL COMMENT '删除者的用户ID',
    `created_at`    TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at`    TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at`    TIMESTAMP    NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    KEY `ix_pd_project_id_config_id_deleted` (`project_id`, `config_id`, `deleted`),
    KEY `ix_pd_project_id_dep_config_id_deleted` (`project_id`, `dep_config_id`, `deleted`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = 'Protobuf依赖表';

CREATE TABLE IF NOT EXISTS `perf_data`
(
    `id`            INT          NOT NULL COMMENT '自增ID' AUTO_INCREMENT,
    `project_id`    VARCHAR(64)  NOT NULL COMMENT '项目ID',
    `data_id`       VARCHAR(64)  NOT NULL COMMENT '压测数据ID',
    `name`          VARCHAR(64)  NOT NULL COMMENT '压测数据名称',
    `description`   VARCHAR(255) NULL COMMENT '压测数据描述',
    `extension`     VARCHAR(16)  NOT NULL DEFAULT '' COMMENT '文件扩展名',
    `hash`          VARCHAR(32)  NOT NULL COMMENT '压测数据文件的一致性哈希值（MD5）',
    `size`          INT          NOT NULL DEFAULT 0 COMMENT '压测数据文件的大小',
    `path`          VARCHAR(255) NOT NULL COMMENT '压测数据文件的路径',
    `number_of_vu`  INT          NOT NULL DEFAULT 0 COMMENT '虚拟用户数',
    `deleted`       TINYINT      NOT NULL DEFAULT 0 COMMENT '逻辑删除标识（未删除、已删除）',
    `maintained_by` VARCHAR(64)  NULL COMMENT '维护者的用户ID',
    `created_by`    VARCHAR(64)  NOT NULL COMMENT '创建者的用户ID',
    `updated_by`    VARCHAR(64)  NOT NULL COMMENT '最近一次更新者的用户ID',
    `deleted_by`    VARCHAR(64)  NULL COMMENT '删除者的用户ID',
    `created_at`    TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at`    TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at`    TIMESTAMP    NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uix_pd_project_id_data_id` (`project_id`, `data_id`),
    KEY `ix_pd_project_id_data_id_deleted` (`project_id`, `data_id`, `deleted`),
    KEY `ix_pd_project_id_hash_deleted` (`project_id`, `hash`, `deleted`),
    KEY `ix_pd_project_id_path_deleted` (`project_id`, `path`, `deleted`),
    KEY `ix_pd_project_id_deleted` (`project_id`, `deleted`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT ='压测数据表';

CREATE TABLE IF NOT EXISTS `perf_case`
(
    `id`                 INT          NOT NULL COMMENT '自增ID' AUTO_INCREMENT,
    `project_id`         VARCHAR(64)  NOT NULL COMMENT '项目ID',
    `case_id`            VARCHAR(64)  NOT NULL COMMENT '压测用例ID',
    `name`               VARCHAR(64)  NOT NULL COMMENT '压测用例名称',
    `description`        VARCHAR(255) NULL COMMENT '压测用例描述',
    `extension`          VARCHAR(16)  NOT NULL DEFAULT '' COMMENT '文件扩展名',
    `hash`               VARCHAR(32)  NOT NULL COMMENT '压测用例文件的一致性哈希值（MD5）',
    `size`               INT          NOT NULL DEFAULT 0 COMMENT '压测用例文件的大小',
    `path`               VARCHAR(255) NOT NULL COMMENT '压测用例文件的路径',
    `target_rps`         INT          NOT NULL DEFAULT 1 COMMENT '目标的RPS',
    `initial_rps`        INT          NOT NULL DEFAULT 1 COMMENT '初始的RPS',
    `step_height`        INT          NOT NULL DEFAULT 0 COMMENT '每次改变RPS的量',
    `step_duration`      VARCHAR(32)  NOT NULL DEFAULT '0s' COMMENT '改变后的RPS的持续时间',
    `perf_data_id`       VARCHAR(64)  NOT NULL DEFAULT '' COMMENT '压测数据ID',
    `number_of_vu`       INT          NOT NULL DEFAULT 0 COMMENT '虚拟用户数',
    `number_of_lg`       INT          NOT NULL DEFAULT 1 COMMENT '施压机数',
    `requests_of_cpu`    VARCHAR(32)  NOT NULL DEFAULT '1' COMMENT '最小分配的CPU资源',
    `requests_of_memory` VARCHAR(32)  NOT NULL DEFAULT '100MiB' COMMENT '最小分配的内存资源',
    `limits_of_cpu`      VARCHAR(32)  NOT NULL DEFAULT '1' COMMENT '最大分配的CPU资源',
    `limits_of_memory`   VARCHAR(32)  NOT NULL DEFAULT '100MiB' COMMENT '最大分配的内存资源',
    `state`              TINYINT      NOT NULL DEFAULT 1 COMMENT '计划状态（生效、失效）',
    `deleted`            TINYINT      NOT NULL DEFAULT 0 COMMENT '逻辑删除标识（未删除、已删除）',
    `maintained_by`      VARCHAR(64)  NULL COMMENT '维护者的用户ID',
    `created_by`         VARCHAR(64)  NOT NULL COMMENT '创建者的用户ID',
    `updated_by`         VARCHAR(64)  NOT NULL COMMENT '最近一次更新者的用户ID',
    `deleted_by`         VARCHAR(64)  NULL COMMENT '删除者的用户ID',
    `created_at`         TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at`         TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at`         TIMESTAMP    NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uix_pc_project_id_case_id` (`project_id`, `case_id`),
    KEY `ix_pc_project_id_case_id_deleted` (`project_id`, `case_id`, `deleted`),
    KEY `ix_pc_project_id_hash_deleted` (`project_id`, `hash`, `deleted`),
    KEY `ix_pc_project_id_path_deleted` (`project_id`, `path`, `deleted`),
    KEY `ix_pc_project_id_perf_data_id_deleted` (`project_id`, `perf_data_id`, `deleted`),
    KEY `ix_pc_project_id_deleted` (`project_id`, `deleted`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT ='压测用例表';

CREATE TABLE IF NOT EXISTS `perf_case_step`
(
    `id`            INT           NOT NULL COMMENT '自增ID' AUTO_INCREMENT,
    `project_id`    VARCHAR(64)   NOT NULL COMMENT '项目ID',
    `case_id`       VARCHAR(64)   NOT NULL COMMENT '压测用例ID',
    `step_id`       VARCHAR(64)   NOT NULL COMMENT '压测用例步骤ID',
    `type`          VARCHAR(32)   NOT NULL COMMENT '压测用例步骤类型（前置、串行、并行、后置）',
    `name`          VARCHAR(64)   NOT NULL COMMENT '压测用例步骤名称',
    `target_rps`    INT           NOT NULL DEFAULT 1 COMMENT '目标的RPS',
    `initial_rps`   INT           NOT NULL DEFAULT 1 COMMENT '初始的RPS',
    `step_height`   INT           NOT NULL DEFAULT 0 COMMENT '每次改变RPS的量',
    `step_duration` VARCHAR(32)   NOT NULL DEFAULT '0s' COMMENT '改变后的RPS的持续时间',
    `url`           VARCHAR(255)  NOT NULL DEFAULT '' COMMENT '请求URL',
    `method`        VARCHAR(255)  NOT NULL DEFAULT '' COMMENT '请求方法',
    `headers`       JSON          NULL COMMENT '请求头',
    `body`          VARCHAR(1024) NOT NULL DEFAULT '{}' COMMENT '请求体',
    `exports`       JSON          NULL COMMENT '提取参数',
    `sleep`         VARCHAR(32)   NOT NULL DEFAULT '0s' COMMENT '请求后休眠时间',
    `state`         TINYINT       NOT NULL DEFAULT 1 COMMENT '计划状态（生效、失效）',
    `deleted`       TINYINT       NOT NULL DEFAULT 0 COMMENT '逻辑删除标识（未删除、已删除）',
    `created_by`    VARCHAR(64)   NOT NULL COMMENT '创建者的用户ID',
    `updated_by`    VARCHAR(64)   NOT NULL COMMENT '最近一次更新者的用户ID',
    `deleted_by`    VARCHAR(64)   NULL COMMENT '删除者的用户ID',
    `created_at`    TIMESTAMP     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at`    TIMESTAMP     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at`    TIMESTAMP     NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uix_pcs_project_id_case_id_step_id` (`project_id`, `case_id`, `step_id`),
    KEY `ix_pcs_project_id_case_id_deleted` (`project_id`, `case_id`, `deleted`),
    KEY `ix_pcs_project_id_case_id_type_target_rps_deleted` (`project_id`, `case_id`, `type`, `target_rps`, `deleted`),
    KEY `ix_pcs_project_id_deleted` (`project_id`, `deleted`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT ='压测用例步骤表';

CREATE TABLE IF NOT EXISTS `perf_plan`
(
    `id`                 INT          NOT NULL COMMENT '自增ID' AUTO_INCREMENT,
    `project_id`         VARCHAR(64)  NOT NULL COMMENT '项目ID',
    `plan_id`            VARCHAR(64)  NOT NULL COMMENT '计划ID',
    `name`               VARCHAR(64)  NOT NULL COMMENT '计划名称',
    `description`        VARCHAR(255) NULL COMMENT '计划描述',
    `type`               VARCHAR(64)  NOT NULL COMMENT '计划类型（手动、定时、接口）',
    `tags`               JSON         NULL COMMENT '标签',
    `protocol`           VARCHAR(64)  NOT NULL COMMENT '协议（TT私有协议、通用gRPC协议、通用HTTP协议）',
    `protobuf_config_id` VARCHAR(64)  NULL COMMENT 'Protobuf项目配置ID',
    `general_config_id`  VARCHAR(64)  NULL COMMENT '通用配置ID',
    `account_config_id`  VARCHAR(64)  NULL COMMENT '池账号配置ID',
    `duration`           INT          NOT NULL DEFAULT 60 COMMENT '压测持续时长，单位为秒',
    `target_env`         VARCHAR(32)  NOT NULL COMMENT '目标环境（生产环境、测试环境）',
    `keepalive`          JSON         NOT NULL COMMENT '保活参数（登录、心跳的限流配置）',
    `delay`              INT          NOT NULL DEFAULT 0 COMMENT '延迟执行时间，单位为秒',
    `state`              TINYINT(1)   NOT NULL DEFAULT 1 COMMENT '计划状态（生效、失效）',
    `deleted`            TINYINT(1)   NOT NULL DEFAULT 0 COMMENT '逻辑删除标识（未删除、已删除）',
    `maintained_by`      VARCHAR(64)  NULL COMMENT '维护者的用户ID',
    `created_by`         VARCHAR(64)  NOT NULL COMMENT '创建者的用户ID',
    `updated_by`         VARCHAR(64)  NOT NULL COMMENT '最近一次更新者的用户ID',
    `deleted_by`         VARCHAR(64)  NULL COMMENT '删除者的用户ID',
    `created_at`         TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at`         TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at`         TIMESTAMP    NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uix_pp_project_id_plan_id` (`project_id`, `plan_id`),
    KEY `ix_pp_project_id_plan_id_deleted` (`project_id`, `plan_id`, `deleted`),
    KEY `ix_pp_project_id_name_deleted` (`project_id`, `name`, `deleted`),
    KEY `ix_pp_project_id_protobuf_config_id_deleted` (`project_id`, `protobuf_config_id`, `deleted`),
    KEY `ix_pp_project_id_general_config_id_deleted` (`project_id`, `general_config_id`, `deleted`),
    KEY `ix_pp_project_id_account_config_id_deleted` (`project_id`, `account_config_id`, `deleted`),
    KEY `ix_pp_project_id_target_deleted` (`project_id`, `target_env`, `deleted`),
    KEY `ix_pp_project_id_deleted` (`project_id`, `deleted`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT ='压测计划表';

CREATE TABLE IF NOT EXISTS `perf_plan_reference_relationship`
(
    `id`             INT         NOT NULL COMMENT '自增ID' AUTO_INCREMENT,
    `project_id`     VARCHAR(64) NOT NULL COMMENT '项目ID',
    `reference_type` VARCHAR(64) NOT NULL COMMENT '引用类型（压测用例）',
    `reference_id`   VARCHAR(64) NOT NULL COMMENT '引用ID（压测用例ID）',
    `plan_id`        VARCHAR(64) NOT NULL COMMENT '计划ID',
    `state`          TINYINT     NOT NULL DEFAULT 1 COMMENT '引用状态（已使用、未使用）',
    `deleted`        TINYINT     NOT NULL DEFAULT 0 COMMENT '逻辑删除标识（未删除、已删除）',
    `created_by`     VARCHAR(64) NOT NULL COMMENT '创建者的用户ID',
    `updated_by`     VARCHAR(64) NOT NULL COMMENT '最近一次更新者的用户ID',
    `deleted_by`     VARCHAR(64) NULL COMMENT '删除者的用户ID',
    `created_at`     TIMESTAMP   NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at`     TIMESTAMP   NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at`     TIMESTAMP   NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    KEY `ix_pprr_project_id_deleted` (`project_id`, `deleted`),
    KEY `ix_pprr_project_id_reference_deleted` (`project_id`, `reference_type`, `reference_id`, `deleted`),
    KEY `ix_pprr_project_id_plan_id_deleted` (`project_id`, `plan_id`, `deleted`),
    KEY `ix_pprr_project_id_plan_id_state_deleted` (`project_id`, `plan_id`, `state`, `deleted`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT ='压测计划引用关系表';
