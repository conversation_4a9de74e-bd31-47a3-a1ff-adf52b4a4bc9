package proxy

import (
	"fmt"

	"github.com/spf13/pflag"
)

var (
	_ pflag.Value = (*DeviceType)(nil)

	allowedDeviceTypes = []DeviceType{
		DeviceTypeOfAndroid,
		DeviceTypeOfIOS,
	}
)

type DeviceType string

const (
	DeviceTypeOfAndroid DeviceType = "android"
	DeviceTypeOfIOS     DeviceType = "ios"
)

func (d *DeviceType) String() string {
	return string(*d)
}

func (d *DeviceType) Set(s string) error {
	for _, dt := range allowedDeviceTypes {
		if s == string(dt) {
			*d = dt
			return nil
		}
	}

	return fmt.Errorf("%s is not included in %v", s, allowedDeviceTypes)
}

func (d *DeviceType) Type() string {
	return "device_type"
}

func (d *DeviceType) Options() []string {
	opts := make([]string, len(allowedDeviceTypes))
	for i, dt := range allowedDeviceTypes {
		opts[i] = string(dt)
	}
	return opts
}
