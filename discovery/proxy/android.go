package proxy

import (
	"fmt"

	"github.com/electricbubble/gadb"
	"github.com/electricbubble/gadb/proxy"
)

func StartAndroidProxy(port int, serial string) error {
	c, err := gadb.NewClient()
	if err != nil {
		return fmt.Errorf("创建`adb`客户端失败，error: %s", err)
	}

	s := proxy.NewServer(c, serial)
	defer func() {
		_ = s.Close()
	}()

	if err = s.ListenAndServe("tcp", fmt.Sprintf(":%d", port)); err != nil {
		return fmt.Errorf("监听端口失败，port: %d, error: %s", port, err)
	}

	return nil
}
