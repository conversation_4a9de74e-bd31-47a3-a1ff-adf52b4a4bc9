
SERVICE_NAME = "proxy"
MAKEFILE_DIR := $(patsubst %/,%,$(dir $(abspath $(lastword $(MAKEFILE_LIST)))))
BIN_DIR := ${MAKEFILE_DIR}/bin

ifeq (, $(GITLAB_GROUP))
GITLAB_GROUP := "gitlab.ttyuyin.com/TestDevelopment"
endif

ifeq (, $(VERSION))
VERSION := $(shell [ -f "../VERSION" ] && cat ../VERSION || echo "0.0.1")
endif

ifeq (, $(VERSION_PACKAGE))
VERSION_PACKAGE := $(GITLAB_GROUP)/qet-backend-common/version
endif

ifeq (, $(LDFLAGS))
$(shell cd $(MAKEFILE_DIR)/..)

GIT_BRANCH := $(shell git rev-parse --abbrev-ref HEAD)
GIT_TAG := $(shell [ "`git describe --tags --abbrev=0 2>/dev/null`" != "" ] && git describe --tags --abbrev=0 || git log --pretty=format:'%h' -n 1)
GIT_COMMIT := $(shell git rev-parse --short HEAD)
BUILD_DATETIME := $(shell TZ=Asia/Shanghai date +'%F %T %Z')
BUILD_GO_VERSION := $(shell go env GOVERSION)
PLATFORM := $(shell uname)

GCFLAGS := "-N -l"
LDFLAGS := "-s -w -X \"${VERSION_PACKAGE}.Version=${VERSION}\" -X \"${VERSION_PACKAGE}.GitBranch=${GIT_BRANCH}\" -X \"${VERSION_PACKAGE}.GitTag=${GIT_TAG}\" -X \"${VERSION_PACKAGE}.GitCommit=${GIT_COMMIT}\" -X \"${VERSION_PACKAGE}.BuildDatetime=${BUILD_DATETIME}\" -X \"${VERSION_PACKAGE}.BuildGoVersion=${BUILD_GO_VERSION}\""

$(shell cd $(MAKEFILE_DIR))
endif

# check if `gofumpt` command exists
GOFUMPT_EXISTS := $(shell command -v gofumpt >/dev/null 2>&1 && echo 1 || echo 0)

GO_FORMAT_CMD := gofmt -s -w
ifeq (1, $(GOFUMPT_EXISTS))
GO_FORMAT_CMD = gofumpt -l -w -extra
endif

define cmd-build
	@# args:
	@# $(1): name
	@# $(2): os
	@# $(3): arch

	@$(eval PRE_ENV := GOOS=$(2))
	@$(eval PRE_ENV := $(if $(filter "",$(3)),$(PRE_ENV),$(PRE_ENV) GOARCH=$(3)))
	@$(eval TARGET := $(if $(filter windows,$(2)),$(SERVICE_NAME)-$(1).exe,$(SERVICE_NAME)-$(1)))
	@$(eval SOURCE := $(SERVICE_NAME).go)
	@echo "build $(TARGET) by $(PRE_ENV)"
	@$(PRE_ENV) go build -o $(BIN_DIR)/$(TARGET) -gcflags $(GCFLAGS) -ldflags $(LDFLAGS) $(MAKEFILE_DIR)/$(SOURCE)
endef

.PHONY: echo
echo:
	@echo "SERVICE_NAME: $(SERVICE_NAME)"
	@echo "MAKEFILE_DIR: $(MAKEFILE_DIR)"
	@echo "BIN_DIR: $(BIN_DIR)"
	@echo "GITLAB_GROUP: $(GITLAB_GROUP)"
	@echo "VERSION: $(VERSION)"
	@echo "VERSION_PACKAGE: $(VERSION_PACKAGE)"
	@echo "GIT_BRANCH: $(GIT_BRANCH)"
	@echo "GIT_TAG: $(GIT_TAG)"
	@echo "GIT_COMMIT: $(GIT_COMMIT)"
	@echo "BUILD_DATETIME: $(BUILD_DATETIME)"
	@echo "BUILD_GO_VERSION: $(BUILD_GO_VERSION)"
	@echo "PLATFORM: $(PLATFORM)"
	@echo "GCFLAGS: $(GCFLAGS)"
	@echo "LDFLAGS: $(LDFLAGS)"
	@echo "GO_FORMAT_CMD: $(GO_FORMAT_CMD)"
	@echo ""

.PHONY: fmt
fmt:
	@go list -f {{.Dir}} $(MAKEFILE_DIR)/... | xargs $(GO_FORMAT_CMD)
	@goimports -l -w -local $(GITLAB_GROUP) $(MAKEFILE_DIR)

.PHONY: lint
lint:
	@golangci-lint run -c $(MAKEFILE_DIR)/../../.golangci.yaml

.PHONY: build
build: clean mac-arm64 mac-amd64 linux win

.PHONY: mac-arm64
mac-arm64:
	$(call cmd-build,macos-aarch64,darwin,arm64)

.PHONY: mac-amd64
mac-amd64:
	$(call cmd-build,macos-x86_64,darwin,amd64)

.PHONY: linux
linux:
	$(call cmd-build,linux-x86_64,linux,amd64)

.PHONY: win
win:
	$(call cmd-build,win64,windows,amd64)

.PHONY: clean
clean:
	@$(eval TARGET := $(SERVICE_NAME))
	@echo "clean $(TARGET)*"
	@rm -f $(BIN_DIR)/$(TARGET)*
