package main

import (
	"fmt"
	"strings"

	"github.com/spf13/cobra"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/version"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/discovery/proxy"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/discovery/tools/cmd"
)

var (
	tp     = new(proxy.DeviceType)
	port   = new(int)
	device = new(string)
)

func main() {
	root := cmd.NewRootCommand()
	root.Args = cobra.NoArgs
	root.RunE = func(cmd *cobra.Command, args []string) error {
		switch *tp {
		case proxy.DeviceTypeOfAndroid:
			fmt.Printf("Connect with `adb connect ${当前主机IP}:%d`\n", *port)
			return proxy.StartAndroidProxy(*port, *device)
		case proxy.DeviceTypeOfIOS:
			return fmt.Errorf("%s is not supported yet", *tp)
		default:
			return fmt.Errorf("unknown device type: %s", *tp)
		}
	}

	vi := version.NewVersionInfo()
	root.Version = vi.String()

	flags := root.Flags()
	flags.SortFlags = false
	flags.VarP(tp, "type", "t", fmt.Sprintf("设备类型，可选值有：%s", strings.Join(tp.Options(), ", ")))
	flags.IntVarP(port, "port", "p", 0, "对外暴露的端口号")
	flags.StringVarP(device, "device", "d", "", "设备编号，对于`Android`则为`serial`，对于`iOS`则为`udid`")

	cobra.CheckErr(root.Execute())
}
