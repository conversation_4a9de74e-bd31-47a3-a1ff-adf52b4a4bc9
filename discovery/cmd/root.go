package cmd

import (
	"github.com/spf13/cobra"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/command"
)

const (
	rootCmdUse   = "discovery"
	rootCmdShort = "Discovery is one of the microservices of the Quality Platform"
	rootCmdLong  = `Discovery is one of the microservices of the Quality Platform. 
The main function is to discover and manage devices, including: Android, iOS, etc.`
)

func NewRootCommand() *cobra.Command {
	return command.NewRootCommand(rootCmdUse, rootCmdShort, rootCmdLong)
}
