// Code generated by goctl. DO NOT EDIT.
// goctl 1.8.3
// Source: discovery.proto

package discoveryservice

import (
	"context"

	"github.com/zeromicro/go-zero/zrpc"
	"google.golang.org/grpc"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/discovery/rpc/pb"
)

type (
	DownloadAppReq  = pb.DownloadAppReq
	DownloadAppResp = pb.DownloadAppResp
	InstallAppReq   = pb.InstallAppReq
	InstallAppResp  = pb.InstallAppResp
	RemoveAppReq    = pb.RemoveAppReq
	RemoveAppResp   = pb.RemoveAppResp

	DiscoveryService interface {
		// DownloadApp 下载应用
		DownloadApp(ctx context.Context, in *DownloadAppReq, opts ...grpc.CallOption) (*DownloadAppResp, error)
		// InstallApp 安装应用
		InstallApp(ctx context.Context, in *InstallAppReq, opts ...grpc.CallOption) (*InstallAppResp, error)
		// RemoveApp 删除应用文件
		RemoveApp(ctx context.Context, in *RemoveAppReq, opts ...grpc.CallOption) (*RemoveAppResp, error)
	}

	defaultDiscoveryService struct {
		cli zrpc.Client
	}
)

func NewDiscoveryService(cli zrpc.Client) DiscoveryService {
	return &defaultDiscoveryService{
		cli: cli,
	}
}

// DownloadApp 下载应用
func (m *defaultDiscoveryService) DownloadApp(ctx context.Context, in *DownloadAppReq, opts ...grpc.CallOption) (*DownloadAppResp, error) {
	client := pb.NewDiscoveryServiceClient(m.cli.Conn())
	return client.DownloadApp(ctx, in, opts...)
}

// InstallApp 安装应用
func (m *defaultDiscoveryService) InstallApp(ctx context.Context, in *InstallAppReq, opts ...grpc.CallOption) (*InstallAppResp, error) {
	client := pb.NewDiscoveryServiceClient(m.cli.Conn())
	return client.InstallApp(ctx, in, opts...)
}

// RemoveApp 删除应用文件
func (m *defaultDiscoveryService) RemoveApp(ctx context.Context, in *RemoveAppReq, opts ...grpc.CallOption) (*RemoveAppResp, error) {
	client := pb.NewDiscoveryServiceClient(m.cli.Conn())
	return client.RemoveApp(ctx, in, opts...)
}
