// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.29.2
// source: discovery/discovery.proto

package pb

import (
	context "context"

	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	DiscoveryService_DownloadApp_FullMethodName = "/discovery.DiscoveryService/DownloadApp"
	DiscoveryService_InstallApp_FullMethodName  = "/discovery.DiscoveryService/InstallApp"
	DiscoveryService_RemoveApp_FullMethodName   = "/discovery.DiscoveryService/RemoveApp"
)

// DiscoveryServiceClient is the client API for DiscoveryService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type DiscoveryServiceClient interface {
	// DownloadApp 下载应用
	DownloadApp(ctx context.Context, in *DownloadAppReq, opts ...grpc.CallOption) (*DownloadAppResp, error)
	// InstallApp 安装应用
	InstallApp(ctx context.Context, in *InstallAppReq, opts ...grpc.CallOption) (*InstallAppResp, error)
	// RemoveApp 删除应用文件
	RemoveApp(ctx context.Context, in *RemoveAppReq, opts ...grpc.CallOption) (*RemoveAppResp, error)
}

type discoveryServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewDiscoveryServiceClient(cc grpc.ClientConnInterface) DiscoveryServiceClient {
	return &discoveryServiceClient{cc}
}

func (c *discoveryServiceClient) DownloadApp(ctx context.Context, in *DownloadAppReq, opts ...grpc.CallOption) (*DownloadAppResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DownloadAppResp)
	err := c.cc.Invoke(ctx, DiscoveryService_DownloadApp_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *discoveryServiceClient) InstallApp(ctx context.Context, in *InstallAppReq, opts ...grpc.CallOption) (*InstallAppResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(InstallAppResp)
	err := c.cc.Invoke(ctx, DiscoveryService_InstallApp_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *discoveryServiceClient) RemoveApp(ctx context.Context, in *RemoveAppReq, opts ...grpc.CallOption) (*RemoveAppResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(RemoveAppResp)
	err := c.cc.Invoke(ctx, DiscoveryService_RemoveApp_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// DiscoveryServiceServer is the server API for DiscoveryService service.
// All implementations must embed UnimplementedDiscoveryServiceServer
// for forward compatibility.
type DiscoveryServiceServer interface {
	// DownloadApp 下载应用
	DownloadApp(context.Context, *DownloadAppReq) (*DownloadAppResp, error)
	// InstallApp 安装应用
	InstallApp(context.Context, *InstallAppReq) (*InstallAppResp, error)
	// RemoveApp 删除应用文件
	RemoveApp(context.Context, *RemoveAppReq) (*RemoveAppResp, error)
	mustEmbedUnimplementedDiscoveryServiceServer()
}

// UnimplementedDiscoveryServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedDiscoveryServiceServer struct{}

func (UnimplementedDiscoveryServiceServer) DownloadApp(context.Context, *DownloadAppReq) (*DownloadAppResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DownloadApp not implemented")
}
func (UnimplementedDiscoveryServiceServer) InstallApp(context.Context, *InstallAppReq) (*InstallAppResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method InstallApp not implemented")
}
func (UnimplementedDiscoveryServiceServer) RemoveApp(context.Context, *RemoveAppReq) (*RemoveAppResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RemoveApp not implemented")
}
func (UnimplementedDiscoveryServiceServer) mustEmbedUnimplementedDiscoveryServiceServer() {}
func (UnimplementedDiscoveryServiceServer) testEmbeddedByValue()                          {}

// UnsafeDiscoveryServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to DiscoveryServiceServer will
// result in compilation errors.
type UnsafeDiscoveryServiceServer interface {
	mustEmbedUnimplementedDiscoveryServiceServer()
}

func RegisterDiscoveryServiceServer(s grpc.ServiceRegistrar, srv DiscoveryServiceServer) {
	// If the following call pancis, it indicates UnimplementedDiscoveryServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&DiscoveryService_ServiceDesc, srv)
}

func _DiscoveryService_DownloadApp_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DownloadAppReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DiscoveryServiceServer).DownloadApp(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DiscoveryService_DownloadApp_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DiscoveryServiceServer).DownloadApp(ctx, req.(*DownloadAppReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _DiscoveryService_InstallApp_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(InstallAppReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DiscoveryServiceServer).InstallApp(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DiscoveryService_InstallApp_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DiscoveryServiceServer).InstallApp(ctx, req.(*InstallAppReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _DiscoveryService_RemoveApp_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RemoveAppReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DiscoveryServiceServer).RemoveApp(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DiscoveryService_RemoveApp_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DiscoveryServiceServer).RemoveApp(ctx, req.(*RemoveAppReq))
	}
	return interceptor(ctx, in, info, handler)
}

// DiscoveryService_ServiceDesc is the grpc.ServiceDesc for DiscoveryService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var DiscoveryService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "discovery.DiscoveryService",
	HandlerType: (*DiscoveryServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "DownloadApp",
			Handler:    _DiscoveryService_DownloadApp_Handler,
		},
		{
			MethodName: "InstallApp",
			Handler:    _DiscoveryService_InstallApp_Handler,
		},
		{
			MethodName: "RemoveApp",
			Handler:    _DiscoveryService_RemoveApp_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "discovery/discovery.proto",
}
