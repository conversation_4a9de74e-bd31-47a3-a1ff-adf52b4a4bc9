// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.1
// 	protoc        v5.29.2
// source: discovery/discovery.proto

package pb

import (
	reflect "reflect"
	sync "sync"

	_ "buf.build/gen/go/bufbuild/protovalidate/protocolbuffers/go/buf/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"

	pb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type DownloadAppReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	PlatformType  pb.PlatformType        `protobuf:"varint,1,opt,name=platform_type,json=platformType,proto3,enum=common.PlatformType" json:"platform_type,omitempty"` // 平台类型（Android、iOS）
	AppName       string                 `protobuf:"bytes,2,opt,name=app_name,json=appName,proto3" json:"app_name,omitempty"`                                          // 应用名称（Android: package_name; iOS: bundle_id）
	Link          string                 `protobuf:"bytes,3,opt,name=link,proto3" json:"link,omitempty"`                                                               // 下载地址
	Filename      string                 `protobuf:"bytes,4,opt,name=filename,proto3" json:"filename,omitempty"`                                                       // 指定下载后保存的文件名
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DownloadAppReq) Reset() {
	*x = DownloadAppReq{}
	mi := &file_discovery_discovery_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DownloadAppReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DownloadAppReq) ProtoMessage() {}

func (x *DownloadAppReq) ProtoReflect() protoreflect.Message {
	mi := &file_discovery_discovery_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DownloadAppReq.ProtoReflect.Descriptor instead.
func (*DownloadAppReq) Descriptor() ([]byte, []int) {
	return file_discovery_discovery_proto_rawDescGZIP(), []int{0}
}

func (x *DownloadAppReq) GetPlatformType() pb.PlatformType {
	if x != nil {
		return x.PlatformType
	}
	return pb.PlatformType(0)
}

func (x *DownloadAppReq) GetAppName() string {
	if x != nil {
		return x.AppName
	}
	return ""
}

func (x *DownloadAppReq) GetLink() string {
	if x != nil {
		return x.Link
	}
	return ""
}

func (x *DownloadAppReq) GetFilename() string {
	if x != nil {
		return x.Filename
	}
	return ""
}

type DownloadAppResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Filename      string                 `protobuf:"bytes,1,opt,name=filename,proto3" json:"filename,omitempty"`              // 下载后的文件名
	Link          string                 `protobuf:"bytes,2,opt,name=link,proto3" json:"link,omitempty"`                      // 下载地址
	Version       string                 `protobuf:"bytes,3,opt,name=version,proto3" json:"version,omitempty"`                // 应用版本
	AppName       string                 `protobuf:"bytes,4,opt,name=app_name,json=appName,proto3" json:"app_name,omitempty"` // 应用名称（Android: package_name; iOS: bundle_id）
	Size          int64                  `protobuf:"varint,5,opt,name=size,proto3" json:"size,omitempty"`                     // 应用大小
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DownloadAppResp) Reset() {
	*x = DownloadAppResp{}
	mi := &file_discovery_discovery_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DownloadAppResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DownloadAppResp) ProtoMessage() {}

func (x *DownloadAppResp) ProtoReflect() protoreflect.Message {
	mi := &file_discovery_discovery_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DownloadAppResp.ProtoReflect.Descriptor instead.
func (*DownloadAppResp) Descriptor() ([]byte, []int) {
	return file_discovery_discovery_proto_rawDescGZIP(), []int{1}
}

func (x *DownloadAppResp) GetFilename() string {
	if x != nil {
		return x.Filename
	}
	return ""
}

func (x *DownloadAppResp) GetLink() string {
	if x != nil {
		return x.Link
	}
	return ""
}

func (x *DownloadAppResp) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *DownloadAppResp) GetAppName() string {
	if x != nil {
		return x.AppName
	}
	return ""
}

func (x *DownloadAppResp) GetSize() int64 {
	if x != nil {
		return x.Size
	}
	return 0
}

type InstallAppReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	PlatformType  pb.PlatformType        `protobuf:"varint,1,opt,name=platform_type,json=platformType,proto3,enum=common.PlatformType" json:"platform_type,omitempty"` // 平台类型（Android、iOS）
	Udid          string                 `protobuf:"bytes,2,opt,name=udid,proto3" json:"udid,omitempty"`                                                               // 设备编号
	RemoteAddress string                 `protobuf:"bytes,3,opt,name=remote_address,json=remoteAddress,proto3" json:"remote_address,omitempty"`                        // 设备远程地址
	Filename      string                 `protobuf:"bytes,4,opt,name=filename,proto3" json:"filename,omitempty"`                                                       // 应用文件名
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *InstallAppReq) Reset() {
	*x = InstallAppReq{}
	mi := &file_discovery_discovery_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *InstallAppReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InstallAppReq) ProtoMessage() {}

func (x *InstallAppReq) ProtoReflect() protoreflect.Message {
	mi := &file_discovery_discovery_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InstallAppReq.ProtoReflect.Descriptor instead.
func (*InstallAppReq) Descriptor() ([]byte, []int) {
	return file_discovery_discovery_proto_rawDescGZIP(), []int{2}
}

func (x *InstallAppReq) GetPlatformType() pb.PlatformType {
	if x != nil {
		return x.PlatformType
	}
	return pb.PlatformType(0)
}

func (x *InstallAppReq) GetUdid() string {
	if x != nil {
		return x.Udid
	}
	return ""
}

func (x *InstallAppReq) GetRemoteAddress() string {
	if x != nil {
		return x.RemoteAddress
	}
	return ""
}

func (x *InstallAppReq) GetFilename() string {
	if x != nil {
		return x.Filename
	}
	return ""
}

type InstallAppResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *InstallAppResp) Reset() {
	*x = InstallAppResp{}
	mi := &file_discovery_discovery_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *InstallAppResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InstallAppResp) ProtoMessage() {}

func (x *InstallAppResp) ProtoReflect() protoreflect.Message {
	mi := &file_discovery_discovery_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InstallAppResp.ProtoReflect.Descriptor instead.
func (*InstallAppResp) Descriptor() ([]byte, []int) {
	return file_discovery_discovery_proto_rawDescGZIP(), []int{3}
}

type RemoveAppReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Filename      string                 `protobuf:"bytes,1,opt,name=filename,proto3" json:"filename,omitempty"` // 应用文件名
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RemoveAppReq) Reset() {
	*x = RemoveAppReq{}
	mi := &file_discovery_discovery_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RemoveAppReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RemoveAppReq) ProtoMessage() {}

func (x *RemoveAppReq) ProtoReflect() protoreflect.Message {
	mi := &file_discovery_discovery_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RemoveAppReq.ProtoReflect.Descriptor instead.
func (*RemoveAppReq) Descriptor() ([]byte, []int) {
	return file_discovery_discovery_proto_rawDescGZIP(), []int{4}
}

func (x *RemoveAppReq) GetFilename() string {
	if x != nil {
		return x.Filename
	}
	return ""
}

type RemoveAppResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RemoveAppResp) Reset() {
	*x = RemoveAppResp{}
	mi := &file_discovery_discovery_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RemoveAppResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RemoveAppResp) ProtoMessage() {}

func (x *RemoveAppResp) ProtoReflect() protoreflect.Message {
	mi := &file_discovery_discovery_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RemoveAppResp.ProtoReflect.Descriptor instead.
func (*RemoveAppResp) Descriptor() ([]byte, []int) {
	return file_discovery_discovery_proto_rawDescGZIP(), []int{5}
}

var File_discovery_discovery_proto protoreflect.FileDescriptor

var file_discovery_discovery_proto_rawDesc = []byte{
	0x0a, 0x19, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x79, 0x2f, 0x64, 0x69, 0x73, 0x63,
	0x6f, 0x76, 0x65, 0x72, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x09, 0x64, 0x69, 0x73,
	0x63, 0x6f, 0x76, 0x65, 0x72, 0x79, 0x1a, 0x1b, 0x62, 0x75, 0x66, 0x2f, 0x76, 0x61, 0x6c, 0x69,
	0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x11, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x65, 0x6e, 0x75, 0x6d,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xc5, 0x01, 0x0a, 0x0e, 0x44, 0x6f, 0x77, 0x6e, 0x6c,
	0x6f, 0x61, 0x64, 0x41, 0x70, 0x70, 0x52, 0x65, 0x71, 0x12, 0x43, 0x0a, 0x0d, 0x70, 0x6c, 0x61,
	0x74, 0x66, 0x6f, 0x72, 0x6d, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x14, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x6c, 0x61, 0x74, 0x66, 0x6f,
	0x72, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x42, 0x08, 0xba, 0x48, 0x05, 0x82, 0x01, 0x02, 0x20, 0x00,
	0x52, 0x0c, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x12, 0x25,
	0x0a, 0x08, 0x61, 0x70, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x0a, 0xba, 0x48, 0x07, 0xd8, 0x01, 0x01, 0x72, 0x02, 0x10, 0x01, 0x52, 0x07, 0x61, 0x70,
	0x70, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x04, 0x6c, 0x69, 0x6e, 0x6b, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x0b, 0xba, 0x48, 0x08, 0xd8, 0x01, 0x01, 0x72, 0x03, 0x88, 0x01, 0x01,
	0x52, 0x04, 0x6c, 0x69, 0x6e, 0x6b, 0x12, 0x26, 0x0a, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0a, 0xba, 0x48, 0x07, 0xd8, 0x01, 0x01,
	0x72, 0x02, 0x10, 0x01, 0x52, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0x8a,
	0x01, 0x0a, 0x0f, 0x44, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x41, 0x70, 0x70, 0x52, 0x65,
	0x73, 0x70, 0x12, 0x1a, 0x0a, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x12,
	0x0a, 0x04, 0x6c, 0x69, 0x6e, 0x6b, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6c, 0x69,
	0x6e, 0x6b, 0x12, 0x18, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x19, 0x0a, 0x08,
	0x61, 0x70, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x61, 0x70, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x22, 0xc9, 0x01, 0x0a, 0x0d,
	0x49, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x41, 0x70, 0x70, 0x52, 0x65, 0x71, 0x12, 0x43, 0x0a,
	0x0d, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x6c,
	0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x42, 0x08, 0xba, 0x48, 0x05, 0x82,
	0x01, 0x02, 0x20, 0x00, 0x52, 0x0c, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x1b, 0x0a, 0x04, 0x75, 0x64, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x07, 0xba, 0x48, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x04, 0x75, 0x64, 0x69, 0x64, 0x12,
	0x31, 0x0a, 0x0e, 0x72, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73,
	0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0a, 0xba, 0x48, 0x07, 0xd8, 0x01, 0x01, 0x72,
	0x02, 0x10, 0x01, 0x52, 0x0d, 0x72, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x41, 0x64, 0x64, 0x72, 0x65,
	0x73, 0x73, 0x12, 0x23, 0x0a, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xba, 0x48, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x08, 0x66,
	0x69, 0x6c, 0x65, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0x10, 0x0a, 0x0e, 0x49, 0x6e, 0x73, 0x74, 0x61,
	0x6c, 0x6c, 0x41, 0x70, 0x70, 0x52, 0x65, 0x73, 0x70, 0x22, 0x33, 0x0a, 0x0c, 0x52, 0x65, 0x6d,
	0x6f, 0x76, 0x65, 0x41, 0x70, 0x70, 0x52, 0x65, 0x71, 0x12, 0x23, 0x0a, 0x08, 0x66, 0x69, 0x6c,
	0x65, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xba, 0x48, 0x04,
	0x72, 0x02, 0x10, 0x01, 0x52, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0x0f,
	0x0a, 0x0d, 0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x41, 0x70, 0x70, 0x52, 0x65, 0x73, 0x70, 0x32,
	0xdb, 0x01, 0x0a, 0x10, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x79, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x12, 0x44, 0x0a, 0x0b, 0x44, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64,
	0x41, 0x70, 0x70, 0x12, 0x19, 0x2e, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x79, 0x2e,
	0x44, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x41, 0x70, 0x70, 0x52, 0x65, 0x71, 0x1a, 0x1a,
	0x2e, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x79, 0x2e, 0x44, 0x6f, 0x77, 0x6e, 0x6c,
	0x6f, 0x61, 0x64, 0x41, 0x70, 0x70, 0x52, 0x65, 0x73, 0x70, 0x12, 0x41, 0x0a, 0x0a, 0x49, 0x6e,
	0x73, 0x74, 0x61, 0x6c, 0x6c, 0x41, 0x70, 0x70, 0x12, 0x18, 0x2e, 0x64, 0x69, 0x73, 0x63, 0x6f,
	0x76, 0x65, 0x72, 0x79, 0x2e, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x41, 0x70, 0x70, 0x52,
	0x65, 0x71, 0x1a, 0x19, 0x2e, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x79, 0x2e, 0x49,
	0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x41, 0x70, 0x70, 0x52, 0x65, 0x73, 0x70, 0x12, 0x3e, 0x0a,
	0x09, 0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x41, 0x70, 0x70, 0x12, 0x17, 0x2e, 0x64, 0x69, 0x73,
	0x63, 0x6f, 0x76, 0x65, 0x72, 0x79, 0x2e, 0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x41, 0x70, 0x70,
	0x52, 0x65, 0x71, 0x1a, 0x18, 0x2e, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x79, 0x2e,
	0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x41, 0x70, 0x70, 0x52, 0x65, 0x73, 0x70, 0x42, 0x43, 0x5a,
	0x41, 0x67, 0x69, 0x74, 0x6c, 0x61, 0x62, 0x2e, 0x74, 0x74, 0x79, 0x75, 0x79, 0x69, 0x6e, 0x2e,
	0x63, 0x6f, 0x6d, 0x2f, 0x54, 0x65, 0x73, 0x74, 0x44, 0x65, 0x76, 0x65, 0x6c, 0x6f, 0x70, 0x6d,
	0x65, 0x6e, 0x74, 0x2f, 0x70, 0x72, 0x6f, 0x62, 0x65, 0x2d, 0x62, 0x61, 0x63, 0x6b, 0x65, 0x6e,
	0x64, 0x2f, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x79, 0x2f, 0x72, 0x70, 0x63, 0x2f,
	0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_discovery_discovery_proto_rawDescOnce sync.Once
	file_discovery_discovery_proto_rawDescData = file_discovery_discovery_proto_rawDesc
)

func file_discovery_discovery_proto_rawDescGZIP() []byte {
	file_discovery_discovery_proto_rawDescOnce.Do(func() {
		file_discovery_discovery_proto_rawDescData = protoimpl.X.CompressGZIP(file_discovery_discovery_proto_rawDescData)
	})
	return file_discovery_discovery_proto_rawDescData
}

var file_discovery_discovery_proto_msgTypes = make([]protoimpl.MessageInfo, 6)
var file_discovery_discovery_proto_goTypes = []any{
	(*DownloadAppReq)(nil),  // 0: discovery.DownloadAppReq
	(*DownloadAppResp)(nil), // 1: discovery.DownloadAppResp
	(*InstallAppReq)(nil),   // 2: discovery.InstallAppReq
	(*InstallAppResp)(nil),  // 3: discovery.InstallAppResp
	(*RemoveAppReq)(nil),    // 4: discovery.RemoveAppReq
	(*RemoveAppResp)(nil),   // 5: discovery.RemoveAppResp
	(pb.PlatformType)(0),    // 6: common.PlatformType
}
var file_discovery_discovery_proto_depIdxs = []int32{
	6, // 0: discovery.DownloadAppReq.platform_type:type_name -> common.PlatformType
	6, // 1: discovery.InstallAppReq.platform_type:type_name -> common.PlatformType
	0, // 2: discovery.DiscoveryService.DownloadApp:input_type -> discovery.DownloadAppReq
	2, // 3: discovery.DiscoveryService.InstallApp:input_type -> discovery.InstallAppReq
	4, // 4: discovery.DiscoveryService.RemoveApp:input_type -> discovery.RemoveAppReq
	1, // 5: discovery.DiscoveryService.DownloadApp:output_type -> discovery.DownloadAppResp
	3, // 6: discovery.DiscoveryService.InstallApp:output_type -> discovery.InstallAppResp
	5, // 7: discovery.DiscoveryService.RemoveApp:output_type -> discovery.RemoveAppResp
	5, // [5:8] is the sub-list for method output_type
	2, // [2:5] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_discovery_discovery_proto_init() }
func file_discovery_discovery_proto_init() {
	if File_discovery_discovery_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_discovery_discovery_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   6,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_discovery_discovery_proto_goTypes,
		DependencyIndexes: file_discovery_discovery_proto_depIdxs,
		MessageInfos:      file_discovery_discovery_proto_msgTypes,
	}.Build()
	File_discovery_discovery_proto = out.File
	file_discovery_discovery_proto_rawDesc = nil
	file_discovery_discovery_proto_goTypes = nil
	file_discovery_discovery_proto_depIdxs = nil
}
