// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: discovery/discovery.proto

package pb

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	pb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = pb.PlatformType(0)
)

// Validate checks the field values on DownloadAppReq with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *DownloadAppReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DownloadAppReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in DownloadAppReqMultiError,
// or nil if none found.
func (m *DownloadAppReq) ValidateAll() error {
	return m.validate(true)
}

func (m *DownloadAppReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for PlatformType

	// no validation rules for AppName

	// no validation rules for Link

	// no validation rules for Filename

	if len(errors) > 0 {
		return DownloadAppReqMultiError(errors)
	}

	return nil
}

// DownloadAppReqMultiError is an error wrapping multiple validation errors
// returned by DownloadAppReq.ValidateAll() if the designated constraints
// aren't met.
type DownloadAppReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DownloadAppReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DownloadAppReqMultiError) AllErrors() []error { return m }

// DownloadAppReqValidationError is the validation error returned by
// DownloadAppReq.Validate if the designated constraints aren't met.
type DownloadAppReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DownloadAppReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DownloadAppReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DownloadAppReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DownloadAppReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DownloadAppReqValidationError) ErrorName() string { return "DownloadAppReqValidationError" }

// Error satisfies the builtin error interface
func (e DownloadAppReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDownloadAppReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DownloadAppReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DownloadAppReqValidationError{}

// Validate checks the field values on DownloadAppResp with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *DownloadAppResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DownloadAppResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DownloadAppRespMultiError, or nil if none found.
func (m *DownloadAppResp) ValidateAll() error {
	return m.validate(true)
}

func (m *DownloadAppResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Filename

	// no validation rules for Link

	// no validation rules for Version

	// no validation rules for AppName

	// no validation rules for Size

	if len(errors) > 0 {
		return DownloadAppRespMultiError(errors)
	}

	return nil
}

// DownloadAppRespMultiError is an error wrapping multiple validation errors
// returned by DownloadAppResp.ValidateAll() if the designated constraints
// aren't met.
type DownloadAppRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DownloadAppRespMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DownloadAppRespMultiError) AllErrors() []error { return m }

// DownloadAppRespValidationError is the validation error returned by
// DownloadAppResp.Validate if the designated constraints aren't met.
type DownloadAppRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DownloadAppRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DownloadAppRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DownloadAppRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DownloadAppRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DownloadAppRespValidationError) ErrorName() string { return "DownloadAppRespValidationError" }

// Error satisfies the builtin error interface
func (e DownloadAppRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDownloadAppResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DownloadAppRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DownloadAppRespValidationError{}

// Validate checks the field values on InstallAppReq with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *InstallAppReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on InstallAppReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in InstallAppReqMultiError, or
// nil if none found.
func (m *InstallAppReq) ValidateAll() error {
	return m.validate(true)
}

func (m *InstallAppReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for PlatformType

	// no validation rules for Udid

	// no validation rules for RemoteAddress

	// no validation rules for Filename

	if len(errors) > 0 {
		return InstallAppReqMultiError(errors)
	}

	return nil
}

// InstallAppReqMultiError is an error wrapping multiple validation errors
// returned by InstallAppReq.ValidateAll() if the designated constraints
// aren't met.
type InstallAppReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m InstallAppReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m InstallAppReqMultiError) AllErrors() []error { return m }

// InstallAppReqValidationError is the validation error returned by
// InstallAppReq.Validate if the designated constraints aren't met.
type InstallAppReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e InstallAppReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e InstallAppReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e InstallAppReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e InstallAppReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e InstallAppReqValidationError) ErrorName() string { return "InstallAppReqValidationError" }

// Error satisfies the builtin error interface
func (e InstallAppReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sInstallAppReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = InstallAppReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = InstallAppReqValidationError{}

// Validate checks the field values on InstallAppResp with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *InstallAppResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on InstallAppResp with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in InstallAppRespMultiError,
// or nil if none found.
func (m *InstallAppResp) ValidateAll() error {
	return m.validate(true)
}

func (m *InstallAppResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return InstallAppRespMultiError(errors)
	}

	return nil
}

// InstallAppRespMultiError is an error wrapping multiple validation errors
// returned by InstallAppResp.ValidateAll() if the designated constraints
// aren't met.
type InstallAppRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m InstallAppRespMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m InstallAppRespMultiError) AllErrors() []error { return m }

// InstallAppRespValidationError is the validation error returned by
// InstallAppResp.Validate if the designated constraints aren't met.
type InstallAppRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e InstallAppRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e InstallAppRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e InstallAppRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e InstallAppRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e InstallAppRespValidationError) ErrorName() string { return "InstallAppRespValidationError" }

// Error satisfies the builtin error interface
func (e InstallAppRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sInstallAppResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = InstallAppRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = InstallAppRespValidationError{}

// Validate checks the field values on RemoveAppReq with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *RemoveAppReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RemoveAppReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in RemoveAppReqMultiError, or
// nil if none found.
func (m *RemoveAppReq) ValidateAll() error {
	return m.validate(true)
}

func (m *RemoveAppReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Filename

	if len(errors) > 0 {
		return RemoveAppReqMultiError(errors)
	}

	return nil
}

// RemoveAppReqMultiError is an error wrapping multiple validation errors
// returned by RemoveAppReq.ValidateAll() if the designated constraints aren't met.
type RemoveAppReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RemoveAppReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RemoveAppReqMultiError) AllErrors() []error { return m }

// RemoveAppReqValidationError is the validation error returned by
// RemoveAppReq.Validate if the designated constraints aren't met.
type RemoveAppReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RemoveAppReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RemoveAppReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RemoveAppReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RemoveAppReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RemoveAppReqValidationError) ErrorName() string { return "RemoveAppReqValidationError" }

// Error satisfies the builtin error interface
func (e RemoveAppReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRemoveAppReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RemoveAppReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RemoveAppReqValidationError{}

// Validate checks the field values on RemoveAppResp with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *RemoveAppResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RemoveAppResp with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in RemoveAppRespMultiError, or
// nil if none found.
func (m *RemoveAppResp) ValidateAll() error {
	return m.validate(true)
}

func (m *RemoveAppResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return RemoveAppRespMultiError(errors)
	}

	return nil
}

// RemoveAppRespMultiError is an error wrapping multiple validation errors
// returned by RemoveAppResp.ValidateAll() if the designated constraints
// aren't met.
type RemoveAppRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RemoveAppRespMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RemoveAppRespMultiError) AllErrors() []error { return m }

// RemoveAppRespValidationError is the validation error returned by
// RemoveAppResp.Validate if the designated constraints aren't met.
type RemoveAppRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RemoveAppRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RemoveAppRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RemoveAppRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RemoveAppRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RemoveAppRespValidationError) ErrorName() string { return "RemoveAppRespValidationError" }

// Error satisfies the builtin error interface
func (e RemoveAppRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRemoveAppResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RemoveAppRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RemoveAppRespValidationError{}
