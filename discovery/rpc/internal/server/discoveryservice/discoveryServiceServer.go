// Code generated by goctl. DO NOT EDIT.
// goctl 1.8.3
// Source: discovery.proto

package server

import (
	"context"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"

	discoveryservicelogic "gitlab.ttyuyin.com/TestDevelopment/probe-backend/discovery/rpc/internal/logic/discoveryservice"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/discovery/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/discovery/rpc/pb"
)

type DiscoveryServiceServer struct {
	svcCtx *svc.ServiceContext
	pb.UnimplementedDiscoveryServiceServer
}

func NewDiscoveryServiceServer(svcCtx *svc.ServiceContext) *DiscoveryServiceServer {
	return &DiscoveryServiceServer{
		svcCtx: svcCtx,
	}
}

// DownloadApp 下载应用
func (s *DiscoveryServiceServer) DownloadApp(ctx context.Context, in *pb.DownloadAppReq) (*pb.DownloadAppResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := discoveryservicelogic.NewDownloadAppLogic(ctx, s.svcCtx)

	return l.DownloadApp(in)
}

// InstallApp 安装应用
func (s *DiscoveryServiceServer) InstallApp(ctx context.Context, in *pb.InstallAppReq) (*pb.InstallAppResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := discoveryservicelogic.NewInstallAppLogic(ctx, s.svcCtx)

	return l.InstallApp(in)
}

// RemoveApp 删除应用文件
func (s *DiscoveryServiceServer) RemoveApp(ctx context.Context, in *pb.RemoveAppReq) (*pb.RemoveAppResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := discoveryservicelogic.NewRemoveAppLogic(ctx, s.svcCtx)

	return l.RemoveApp(in)
}
