package discoveryservicelogic

import (
	"context"
	"fmt"
	"os"
	"path/filepath"
	"strings"

	"github.com/electricbubble/gidevice/pkg/ipa"
	"github.com/shogo82148/androidbinary/apk"
	"github.com/zeromicro/go-zero/core/jsonx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	commonpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pkgpuller"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/discovery/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/discovery/rpc/pb"
)

type DownloadAppLogic struct {
	*BaseLogic
}

func NewDownloadAppLogic(ctx context.Context, svcCtx *svc.ServiceContext) *DownloadAppLogic {
	return &DownloadAppLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// DownloadApp 下载应用
func (l *DownloadAppLogic) DownloadApp(in *pb.DownloadAppReq) (out *pb.DownloadAppResp, err error) {
	var (
		platformType = in.GetPlatformType()
		appName      = in.GetAppName()
		link         = in.GetLink()
		filename     = in.GetFilename()
	)
	l.Infof(
		"download app, req: %s, user: %s", protobuf.MarshalJSONIgnoreError(in), jsonx.MarshalIgnoreError(l.currentUser),
	)

	key := strings.Join(
		[]string{
			"download_app",
			protobuf.GetEnumStringOf(platformType),
			appName,
			link,
			filename,
		}, "|",
	)
	v, err := l.svcCtx.SingleFlight.Do(
		key, func() (any, error) {
			suffix := ""
			switch platformType {
			case commonpb.PlatformType_ANDROID, commonpb.PlatformType_HarmonyOS:
				suffix = constants.ConstSuffixOfApk
			case commonpb.PlatformType_IOS:
				suffix = constants.ConstSuffixOfIpa
			default:
				return nil, errorx.Errorf(
					errorx.DoesNotSupport,
					"unknown platform type: %s",
					protobuf.GetEnumStringOf(platformType),
				)
			}

			if appName == "" && link == "" {
				return nil, errorx.Err(
					errorx.ValidateParamError, "the app_name and link can't be empty at the same time",
				)
			}

			if filename == "" {
				filename = fmt.Sprintf("%s%s", utils.GenNanoId("app_"), suffix)
			}
			appPath := filepath.Join(l.svcCtx.Config.LocalPath, dirNameOfDownload, filename)
			if err := os.MkdirAll(filepath.Dir(appPath), 0o755); err != nil {
				return nil, errorx.Errorf(
					errorx.FileOperationFailure,
					"failed to create dir, path: %s, error: %+v",
					filepath.Dir(appPath), err,
				)
			}

			if link != "" {
				err = utils.DownloadFromUrl(l.ctx, link, appPath)
			} else {
				var puller pkgpuller.AppPkgPuller
				puller, err = pkgpuller.NewAppPkgPuller(l.ctx, pkgpuller.AppPkgNameType(appName), appPath)
				if err != nil {
					return nil, errorx.Errorf(
						errorx.NewObjectFailure,
						"failed to new app package puller, package_name: %s, path: %s, error: %+v",
						appName, appPath, err,
					)
				}

				link, err = puller.Pull()
			}
			if err != nil {
				return nil, errorx.Errorf(
					errorx.FileOperationFailure,
					"failed to download the app package, package_name: %s, link: %s, path: %s, error: %+v",
					appName, link, appPath, err,
				)
			}

			stat, err := os.Stat(appPath)
			if err != nil {
				return nil, errorx.Errorf(
					errorx.NotExists,
					"the app package was not found locally after download, package_name: %s, link: %s, path: %s, error: %+v",
					appName, link, appPath, err,
				)
			}

			version := ""
			if platformType == commonpb.PlatformType_IOS {
				info, err := ipa.Info(appPath)
				if err != nil {
					l.Errorf(
						"failed to open ipa file, package_name: %s, link: %s, path: %s, size: %d, error: %+v",
						appName, link, appPath, stat.Size(), err,
					)
				} else {
					appName = info.CFBundleIdentifier
					version = info.CFBundleShortVersionString
					if len(info.CFBundleVersion) > 0 {
						version += "-" + info.CFBundleVersion
					}
				}
			} else {
				info, err := apk.OpenFile(appPath)
				if err != nil {
					l.Errorf(
						"failed to open apk file, package_name: %s, link: %s, path: %s, size: %d, error: %+v",
						appName, link, appPath, stat.Size(), err,
					)
				} else {
					appName = info.PackageName()
					version = info.Manifest().VersionName
				}
			}

			return &pb.DownloadAppResp{
				Filename: filename,
				Link:     link,
				Version:  version,
				AppName:  appName,
				Size:     stat.Size(),
			}, nil
		},
	)
	if err != nil {
		return nil, err
	}

	return v.(*pb.DownloadAppResp), nil
}
