package discoveryservicelogic

import (
	"context"
	"os"
	"path/filepath"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
	qetutils "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/discovery/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/discovery/rpc/pb"
)

type RemoveAppLogic struct {
	*BaseLogic
}

func NewRemoveAppLogic(ctx context.Context, svcCtx *svc.ServiceContext) *RemoveAppLogic {
	return &RemoveAppLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// RemoveApp 删除应用文件
func (l *RemoveAppLogic) RemoveApp(in *pb.RemoveAppReq) (out *pb.RemoveAppResp, err error) {
	l.Infof(
		"remove app, req: %s, user: %s", protobuf.MarshalJSONIgnoreError(in), jsonx.MarshalIgnoreError(l.currentUser),
	)

	filename := in.GetFilename()
	filePath := filepath.Join(l.svcCtx.Config.LocalPath, dirNameOfDownload, filename)
	if !qetutils.Exists(filePath) {
		return nil, errorx.Errorf(errorx.NotExists, "the app package doesn't exist, filename: %s", filename)
	}

	if err = os.Remove(filePath); err != nil {
		return nil, errors.Errorf("failed to remove the app package, filename: %s, error: %+v", filename, err)
	}

	return &pb.RemoveAppResp{}, nil
}
