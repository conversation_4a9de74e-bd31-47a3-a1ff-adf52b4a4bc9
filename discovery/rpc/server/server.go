package server

import (
	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/conf"
	"github.com/zeromicro/go-zero/core/service"
	"github.com/zeromicro/go-zero/zrpc"
	"google.golang.org/grpc"
	"google.golang.org/grpc/reflection"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/log"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/server"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/discovery/rpc/internal/config"
	discoveryservice "gitlab.ttyuyin.com/TestDevelopment/probe-backend/discovery/rpc/internal/server/discoveryservice"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/discovery/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/discovery/rpc/pb"
)

// NewRPCServer for single server startup
func NewRPCServer(configFile string) (*server.Server, error) {
	return server.NewServer(Options(configFile)...)
}

func NewServer(c server.Config, w *log.ZapWriter) (service.Service, error) {
	cc, ok := c.(config.Config)
	if !ok {
		return nil, errors.Errorf("failed to new mqc server, cause by the config[%T] isn't a mqc config", c)
	}

	if err := cc.ServiceConf.SetUp(); err != nil {
		return nil, errors.Errorf("failed to setup service config, error: %+v", err)
	}

	svcCtx := svc.NewServiceContext(cc)
	s := zrpc.MustNewServer(
		cc.RpcServerConf, func(grpcServer *grpc.Server) {
			pb.RegisterDiscoveryServiceServer(grpcServer, discoveryservice.NewDiscoveryServiceServer(svcCtx))

			if cc.Mode == service.DevMode || cc.Mode == service.TestMode {
				reflection.Register(grpcServer)
			}
		},
	)

	// 需要在 `ServiceConf.SetUp` 后再设置 `Writer`
	log.SetWriter(w)

	return s, nil
}

// NewConfig new a config of server
func NewConfig(configFile string) server.Config {
	var c config.Config
	conf.MustLoad(configFile, &c, conf.UseEnv())

	return c
}

func Options(configFile string) []server.Option {
	return []server.Option{
		server.WithNewConfigFunc(
			func() server.Config {
				return NewConfig(configFile)
			},
		),
		server.WithNewServiceFunc(NewServer),
	}
}
