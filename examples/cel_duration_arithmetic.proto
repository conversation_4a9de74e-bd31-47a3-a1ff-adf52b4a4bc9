// Copyright 2023 Buf Technologies, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

import "buf/validate/validate.proto";
import "google/protobuf/duration.proto";

message IndirectFlight {
  option (buf.validate.message).cel = {
    id: "total_length_limit"
    message: "the entire trip should be less than 48 hours"
    expression:
      // This validates that the total duration of an indirect flight should be
      // less than 48 hours.
      // Adding two duration yields a duration.
      // Note that if the CEL expression is too long, you can break it down into
      // multiple lines like so:
      "this.first_flight_duration + this.second_flight_duration"
      // Durations can be compared with `<`, `<=`, `>=`, `>`, `==` and `!=`.
      "+ this.layover_duration < duration('48h')"
  };

  google.protobuf.Duration first_flight_duration = 1;
  google.protobuf.Duration layover_duration = 2;
  google.protobuf.Duration second_flight_duration = 3;
}
