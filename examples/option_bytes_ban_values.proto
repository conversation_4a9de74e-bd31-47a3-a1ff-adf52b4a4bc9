// Copyright 2023 Buf Technologies, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

import "buf/validate/validate.proto";

service PasswordService {
  // UpdatePassword Updates a Password.
  rpc UpdatePassword(UpdatePasswordRequest) returns (UpdatePasswordResponse);
}

message UpdatePasswordRequest {
  // user_id is the user id.
  uint64 user_id = 1;
  // new_password is the new password.
  bytes new_password = 2 [(buf.validate.field).bytes = {
    // `min_len` validates that the password is at least 8 bytes
    min_len: 8
    // `not_in` validates that the new password is not any password in the list:
    not_in: [
      "12345678",
      "00000000",
      "password"
    ]
  }];
}

message UpdatePasswordResponse {}
