// Copyright 2023 Buf Technologies, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

import "buf/validate/validate.proto";
import "google/protobuf/field_mask.proto";

service HealthService {
  rpc UpdateHealthInfo(UpdateHealthInfoRequest) returns (UpdateHealthInfoResponse);
}

message UpdateHealthInfoRequest {
  fixed64 user_id = 1;
  // IGNORE_ALWAYS skips evaluating validation rules for a certain field. This
  // includes skipping validation rules defined within that message's definition
  // if the field is a message.
  // In this case, when the validator validates a `UpdateHealthInfoRequest`
  // message, it will not validate field `health_info`. i.e. it will not validate
  // that health_info.height > 0 and health_info.weight > 0.
  HealthInfo health_info = 2 [(buf.validate.field).ignore = IGNORE_ALWAYS];
  google.protobuf.FieldMask field_mask = 3;
}

message HealthInfo {
  float height = 1 [(buf.validate.field).float.gt = 0];
  float weight = 2 [(buf.validate.field).float.gt = 0];
}

message UpdateHealthInfoResponse {}
