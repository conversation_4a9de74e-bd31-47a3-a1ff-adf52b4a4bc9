// Copyright 2023 Buf Technologies, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

import "buf/validate/validate.proto";

// GoodNews is good news in the stock market.
message GoodNews {
  string content = 1 [(buf.validate.field).string = {
    // `contains` validates that a string field contains this string.
    // In this example, it validates that content contains `up`.
    contains: "up"
    // `not_contains` validates that a string field doesn't contain this string.
    // In this example, it validates that content doesn't contain `down`.
    not_contains: "down"
  }];
}
