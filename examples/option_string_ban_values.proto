// Copyright 2023 Buf Technologies, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

import "buf/validate/validate.proto";

service TeamService {
  rpc UpdateTeamName(UpdateTeamNameRequest) returns (UpdateTeamNameResponse);
}

message UpdateTeamNameRequest {
  string name = 1 [(buf.validate.field).string = {
    // `not_in` validates that a string field is not any value from the list
    // defined. In this case, it validates that you cannot set a team name
    // to "The winners" or "winners".
    not_in: [
      "The winners",
      "winners"
    ]
  }];
}

message UpdateTeamNameResponse {}
