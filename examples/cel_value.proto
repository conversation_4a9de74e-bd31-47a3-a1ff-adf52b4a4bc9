// Copyright 2023 Buf Technologies, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

import "buf/validate/validate.proto";
import "google/protobuf/struct.proto";

service DataStoreService {
  // WriteKeyValuePair writes a key value pair to the data store.
  rpc WriteKeyValuePair(WriteKeyValuePairRequest) returns (WriteKeyValuePairResponse);
}

message WriteKeyValuePairRequest {
  // key is the key.
  google.protobuf.Value key = 1 [(buf.validate.field).cel = {
    id: "write_key_value_pair_request_valid_key_type"
    message: "key must be one of int, uint, double, bool and string"
    expression: "type(this) in [int, uint, double, bool, string]"
  }];
  // value is the value.
  // google.protobuf.Value is the value.
  google.protobuf.Value value = 2;
}

message WriteKeyValuePairResponse {}
