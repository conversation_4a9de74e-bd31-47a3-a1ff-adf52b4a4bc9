// Copyright 2023 Buf Technologies, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

import "buf/validate/validate.proto";

service CalculatorService {
  rpc Division(DivisionRequest) returns (DivisionResponse);
}
message DivisionRequest {
  float numerator = 1;
  float denominator = 2 [(buf.validate.field) = {
    float: {
      // `not_in` validates that the field is not any of the values specified.
      // In this case, it validates the value is not zero.
      not_in: [0]
    }
  }];
}

message DivisionResponse {
  float result = 1;
}
