// Copyright 2023 Buf Technologies, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

import "buf/validate/validate.proto";

service CarService {
  // SearchCar searches for cars.
  rpc SearchCar(SearchCarRequest) returns (SearchCarResponse);
}

message SearchCarRequest {
  optional fixed32 number_of_seats = 1 [(buf.validate.field) = {
    // `IGNORE_IF_UNPOPULATED` skips validation if the field isn't set.
    ignore: IGNORE_IF_UNPOPULATED
    fixed32: {
      // `in` requires that the value must be one of the specified values.
      // In this case, it validates that the number of seats is either 5 or 7.
      in: [
        5,
        7
      ]
    }
  }];
}

message SearchCarResponse {
  message Car {}
  repeated Car cars = 1;
}
