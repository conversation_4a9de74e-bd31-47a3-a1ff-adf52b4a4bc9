// Copyright 2023 Buf Technologies, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

import "buf/validate/validate.proto";

service ResourceService {
  rpc UploadResource(UploadResourceRequest) returns (UploadResourceResponse);
}

message UploadResourceRequest {
  string uri = 1 [(buf.validate.field).cel = {
    id: "valid_uri"
    message: "uri must be a valid URI"
    // `isUri` validates that a string is an absolute URI.
    // This expression validates that the uri field is an absolute URI.
    // Note: to allow relative URI, use `isUriRef`.
    expression: "this.isUri()"
  }];
  bytes data = 2;
}

message UploadResourceResponse {}
