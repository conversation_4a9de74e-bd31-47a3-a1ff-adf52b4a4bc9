// Copyright 2023 Buf Technologies, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

import "buf/validate/validate.proto";
import "google/protobuf/timestamp.proto";

message EventFromTheTwentiethCentury {
  // name is the name of the event
  string name = 1;
  // description is the description of the event
  string description = 2;
  // timestamp is the timestamp when this event happened.
  google.protobuf.Timestamp timestamp = 3 [(buf.validate.field).timestamp = {
    // `gte` validates that a timestamp is greater than or equal to a value.
    // In this case, it validates the event happens at or after 1900-01-01T00:00:00Z,
    // which is ********** seconds before the Unix epoch.
    gte: {seconds: -**********}
    // `le` validates that a timestamp is less than a value.
    // In this case, it validates the event happens before 2000-01-01T00:00:00Z,
    // which is ********* seconds after the Unix epoch.
    lt: {seconds: *********}
    // You can also use `gt` for "greater than" and `lte` for "less than or equal to".
  }];
}
