// Copyright 2023 Buf Technologies, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

import "buf/validate/validate.proto";

service ArticleService {
  // PublishArticle publishes an article.
  rpc PublishArticle(PublishArticleRequest) returns (PublishArticleResponse);
}

message PublishArticleRequest {
  option (buf.validate.message).cel = {
    id: "article_contain_keyword"
    message: "article must contain all the keywords it is tagged with"
    // For each keyword `kw`, validate that it appears in the article.
    // `contains` checks whether a string contains another string.
    expression: "this.keyword.all(kw, this.article_content.contains(kw))"
  };

  string article_content = 1;
  // keyword is the keyword that this article is associated with.
  repeated string keyword = 2;
}

message PublishArticleResponse {}
