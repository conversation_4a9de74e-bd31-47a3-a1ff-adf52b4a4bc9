// Copyright 2023 Buf Technologies, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

import "buf/validate/validate.proto";

service LocationService {
  rpc LocationForIp(LocationForIpRequest) returns (LocationForIpResponse);
  rpc LocationForIpPrefix(LocationForIpPrefixRequest) returns (LocationForIpPrefixResponse);
}

message LocationForIpRequest {
  string ip_address = 1 [(buf.validate.field).cel = {
    id: "valid_address"
    message: "."
    // `some_string.isIp()` returns whether the string is a valid ip address.
    // `isIp(4)` returns whether a string is an ipv4 address.
    // `isIp(6)` returns whether a string is an ipv6 address.
    // In this case, it validates that field `ip_address` must be a valid ip
    // address, either ipv4 or ipv6.
    expression: "this.isIp()"
  }];
}

message LocationForIpResponse {
  // ...
}

message LocationForIpPrefixRequest {
  string ip_prefix = 1 [(buf.validate.field).cel = {
    id: "valid_prefix"
    message: "."
    // `some_string.isIpPrefix()` returns whether the string is a valid ip with prefix length.
    // `isIpPrefix(4)` returns whether a string is an ipv4 with prefix length.
    // `isIpPrefix(6)` returns whether a string is an ipv6 with prefix length.
    // `isIpPrefix(true)` returns whether a string is an ip prefix.
    // `isIpPrefix(4, true)` returns whether a string is an ipv4 prefix.
    // `isIpPrefix(6, true)` returns whether a string is an ipv6 prefix.
    // In this case, it validates that field `ip_prefix` must be a valid ip with
    // prefix length, either ipv4 or ipv6.
    expression: "this.isIpPrefix()"
  }];
}

message LocationForIpPrefixResponse {
  // ...
}
