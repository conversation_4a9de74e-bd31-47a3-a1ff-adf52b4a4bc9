// Copyright 2023 Buf Technologies, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

import "buf/validate/validate.proto";

service GroupChatService {
  // CreateGroupChat creates a group chat.
  rpc CreateGroupChat(CreateGroupChatRequest) returns (CreateGroupChatResponse);
}

message CreateGroupChatRequest {
  // all members of the group chat must be verified users
  // Note: to treat a repeated field as a list in cel, access it from the message
  // option as opposed to the field option.
  option (buf.validate.message).cel = {
    id: "group_chat_member_must_be_verified"
    message: "all group chat members must be verified users"
    // `m.is_verified` must be true for all `m` in `this.member`
    expression: "this.member.all(m, m.is_verified)"
  };

  // member is the members of the group chat
  repeated ChatAppUser member = 1;
}

message ChatAppUser {
  uint64 user_id = 1;
  bool is_verified = 2;
}

message CreateGroupChatResponse {}
