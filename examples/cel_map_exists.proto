// Copyright 2023 Buf Technologies, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

import "buf/validate/validate.proto";

// CustomerReviews is customers' reviews for a product.
message CustomerReviews {
  // This validates that there is at least one review from customer_id_to_review
  // such that its rating is the same as highest_rating.
  option (buf.validate.message).cel = {
    id: "highest_rating_exists"
    message: "highest_rating must be the highest from reviews"
    expression:
      // `some_map.exists` validates that a predicate (condition) is true for some
      // key in this map. To validate that a predicate is true for some _value_ in
      // a map, access the value with `some_map[key]` in the predicate to validate
      // the value.
      "this.customer_id_to_review.exists("
      "  id,"
      "  this.customer_id_to_review[id].rating == this.highest_rating"
      ")"
  };

  // customer_id_to_review maps a customer id to a review.
  map<fixed64, ProductReview> customer_id_to_review = 1;
  // highest_rating is the highest rating this product receives.
  float highest_rating = 2;
}

message ProductReview {
  // rating is how much the review rates a product.
  float rating = 1;
  // review is the detailed review for a product.
  string review = 2;
}
