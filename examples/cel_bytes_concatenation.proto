// Copyright 2023 Buf Technologies, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

import "buf/validate/validate.proto";

message CompactDocument {
  option (buf.validate.message).cel = {
    id: "header_footer_size_limit"
    message: "header and footer should be less than 500 bytes in total"
    // the `+` operator concatenates two `bytes` together and `size` returns
    // the length of a `bytes`.
    // This validates that the combined size of header and footer should be < 500.
    expression: "size(this.header + this.footer) < 500"
  };

  bytes header = 1;
  bytes footer = 2;
}
