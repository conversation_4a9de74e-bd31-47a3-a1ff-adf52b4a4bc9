// Copyright 2023 Buf Technologies, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

import "buf/validate/validate.proto";

service DivisionService {
  // IsDivisible solves whether one number is divisible by another.
  rpc IsDivisible(IsDivisibleRequest) returns (IsDivisibleResponse);
}

message IsDivisibleRequest {
  // dividend is the dividend in the division.
  int32 dividend = 1;
  // divisor is the divisor in the division.
  int32 divisor = 2 [(buf.validate.field).cel = {
    id: "divisor_must_be_a_small_prime"
    message: "the divisor must be one of 2, 3 and 5"
    // `in` evaluates list membership. The expression evaluates to
    // true when the value is in the list.
    // Validates that divisor must be one of 2, 3 and 5.
    expression: "this in [2, 3, 5]"
    // Similarly, you can assert that it's not in a list with `!(this in [2, 3, 5])`.
  }];
}

message IsDivisibleResponse {
  // is_divisible is the result.
  bool is_divisible = 1;
}
