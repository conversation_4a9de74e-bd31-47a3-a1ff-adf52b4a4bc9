// Copyright 2023 Buf Technologies, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

import "buf/validate/validate.proto";

service StarService {
  // AddStar adds a star.
  rpc AddStar(AddStarRequest) returns (AddStarResponse);
}

message AddStarRequest {
  // name is the name of the star.
  string name = 1;
  // distance_ly is its distance from the Sun in light years.
  double distance_ly = 2 [(buf.validate.field).double = {
    // `gt` validates that the distance is positive.
    gt: 0
    // `finite` validates a double or float is not infinity.
    // In this case, it validates that the distance is not positive infinity.
    finite: true
  }];
  // mystery_index is how mysterious the star is.
  // `gte = +inf` validates that the number must be greater than positive infinity.
  // This means mystery_index must be +inf.
  // protoc fails to compile this line but Buf compiles it successfully:
  // float mystery_index = 3 [(buf.validate.field).float.gte = +inf];
}

message AddStarResponse {}
