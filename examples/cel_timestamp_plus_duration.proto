// Copyright 2023 Buf Technologies, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

import "buf/validate/validate.proto";
import "google/protobuf/duration.proto";
import "google/protobuf/timestamp.proto";

service FlightInfoService {
  // UpdateFlightInfo updates a flight's information.
  rpc UpdateFlightInfo(UpdateFlightInfoRequest) returns (UpdateFlightInfoResponse);
}

message UpdateFlightInfoRequest {
  option (buf.validate.message).cel = {
    id: "correct_duration"
    message: "duration must be the period of time between departure and arrival"
    // Adding a timestamp and a duration evaluates to a new timestamp.
    // Two timestamps can be compared with `==`, `!=`, `<`, `<=`, `>` and `>=`.
    expression: "this.new_departure_time + this.new_duration  == this.new_arrival_time"
  };

  uint64 flight_id = 1;
  // departure_time is the departure time.
  google.protobuf.Timestamp new_departure_time = 2;
  // arrival_time is the arrival time.
  google.protobuf.Timestamp new_arrival_time = 3;
  // duration is the flight's duration.
  google.protobuf.Duration new_duration = 4;
}

message UpdateFlightInfoResponse {}
