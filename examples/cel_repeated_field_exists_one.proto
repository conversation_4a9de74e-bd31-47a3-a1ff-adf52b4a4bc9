// Copyright 2023 Buf Technologies, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

import "buf/validate/validate.proto";

// TeamRoster is a team roster.
message TeamRoster {
  // one and only one players on the team is the captain.
  option (buf.validate.message).cel = {
    id: "captain_exists"
    message: "captain must be among the list of players"
    // `exists_one` checks that one and only one item in the list satisfies
    // the predicate, which is `p.name == this.captain_name` in this case.
    expression: "this.players.exists_one(p, p.name == this.captain_name)"
  };

  message Player {
    string name = 1;
    uint32 jersy_number = 2;
  }

  repeated Player players = 1;
  string captain_name = 2;
}
