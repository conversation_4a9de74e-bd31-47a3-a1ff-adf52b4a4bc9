// Copyright 2023 Buf Technologies, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

import "buf/validate/validate.proto";

service AccountService {
  // DeleteAccount deletes an account.
  rpc DeleteAccount(DeleteAccountRequest) returns (DeleteAccountResponse);
}

message DeleteAccountRequest {
  // This validates that field `acknowledgement` must be equal to the bytes for
  // "I am aware.".
  bytes acknowledgement = 1 [(buf.validate.field).bytes.const = "I am aware."];
}

message DeleteAccountResponse {}
