// Copyright 2023 Buf Technologies, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

import "buf/validate/validate.proto";

message Polygon {
  // number_of_sides is the number of side
  // `const` validates that a field must have a particular value. In this
  // case, it validates the the polygon must be a triangle.
  uint32 number_of_sides = 1 [(buf.validate.field).uint32.const = 3];
  // area is the area of the polygon
  // `gt` validates that the area greater than a particular value. In this
  // case, it validates that the area is positive.
  double area = 2 [(buf.validate.field).double.gt = 0.0];
}
