// Copyright 2023 Buf Technologies, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

import "buf/validate/validate.proto";

service WholeSaleService {
  // PlaceWholeSaleOrder places an order.
  rpc PlaceWholeSaleOrder(PlaceWholeSaleOrderRequest) returns (PlaceWholeSaleOrderResponse);
}

message PlaceWholeSaleOrderRequest {
  // item_id is the ID of the item to purchase.
  uint64 item_id = 1;
  // quantity is the quantity of the item to purchase.
  uint32 quantity = 2 [(buf.validate.field).cel = {
    id: "minimum_whole_sale_quantity"
    message: "order quantity must be 100 or greater"
    // `this` refers to this field and the expression evaluates to a boolean result.
    // If the result is false, validation will fail with the above error message.
    expression: "this >= 100"
  }];
}

message PlaceWholeSaleOrderResponse {}
