// Copyright 2023 Buf Technologies, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

import "buf/validate/validate.proto";
import "google/protobuf/wrappers.proto";

service FridgeControlService {
  // SetTemperature sets the fridge's target temperature.
  rpc SetTemperature(SetTemperatureRequest) returns (SetTemperatureResponse);
}

message SetTemperatureRequest {
  // temperature is the target temperature in degree Celsius.
  // Validate that the field is set. There is a difference between
  // not setting the value and setting it to 0 degree Celsius.
  google.protobuf.FloatValue temperature_celsius = 3 [(buf.validate.field).required = true];
}

message SetTemperatureResponse {}
