// Copyright 2023 Buf Technologies, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

import "buf/validate/validate.proto";
import "google/protobuf/any.proto";

// AuditLog is an audit log entry.
message AuditLog {
  // id is the ID of the log.
  uint64 id = 1;
  // payload is the payload of the log.
  google.protobuf.Any payload = 2 [(buf.validate.field).any = {
    // Validate that the payload itself must not be an audit log or a timestamp.
    // Values in `not_in` should be a string representing a type_url, which is
    // typically `type.googleapis/<fully qualified name>`.
    not_in: [
      "type.googleapis.com/google.protobuf.Timestamp",
      "type.googleapis.com/AuditLog"
    ]
  }];
}
