// Copyright 2023 Buf Technologies, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

import "buf/validate/validate.proto";
import "google/protobuf/duration.proto";

service TimerService {
  // StartTimer starts a timer on the server.
  rpc StartTimer(StartTimerRequest) returns (StartTimerResponse);
}

message StartTimerRequest {
  google.protobuf.Duration duration = 1 [(buf.validate.field).cel = {
    id: "maximum_duration"
    message: "timer duration must be shorter than a day"
    // You can create a duration from a string literal in the form of `1m30s`,
    // where each number is has a unit suffix. The suffix supported are "h", "m",
    // "s", "ms", "us" and "ns".
    // Validate the timer duration is less than a day. Note that durations
    // can be compared.
    expression: "this <= duration('23h59m59s')"
  }];
}

message StartTimerResponse {}
