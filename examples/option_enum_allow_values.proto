// Copyright 2023 Buf Technologies, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

import "buf/validate/validate.proto";

service OnlineStatusService {
  // SetOnlineStatus sets a user's online status.
  rpc SetOnlineStatus(SetOnlineStatusRequest) returns (SetOnlineStatusResponse);
}

message SetOnlineStatusRequest {
  // new_status is the new status.
  OnlineStatus new_status = 1 [(buf.validate.field).enum = {
    // `defined_only` validates that this enum value must be a defined value.
    // In this case, the enum's value must be one of 0, 1, 2, 5 and 10.
    // Note that unspecified value `ONLINE_STATUS_UNSPECIFIED = 0;` is considered defined.
    defined_only: true
    // `in` validates that this enum must be a value from the list provided.
    // In this case, it validates this enum must be one of 1, 2, 5 and 10.
    in: [
      1,
      2,
      5,
      10
    ]
  }];
}

message SetOnlineStatusResponse {}

enum OnlineStatus {
  ONLINE_STATUS_UNSPECIFIED = 0;
  ONLINE_STATUS_ONLINE = 1;
  ONLINE_STATUS_AWAY = 2;
  ONLINE_STATUS_BANNED = 3;
  ONLINE_STATUS_OFFLINE = 5;
  ONLINE_STATUS_FOCUS = 10;
}
