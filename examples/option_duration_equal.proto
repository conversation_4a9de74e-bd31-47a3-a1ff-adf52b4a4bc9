// Copyright 2023 Buf Technologies, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

import "buf/validate/validate.proto";
import "google/protobuf/duration.proto";

message ExpiredPacket {
  // time_to_live is the time before a packet expires.
  google.protobuf.Duration time_to_live = 1 [(buf.validate.field).duration = {
    // `const` validates that a duration must be equal to this duration.
    // In this case, `time_to_live` must equal to 0.
    const: {
      seconds: 0
      nanos: 0
    }
  }];
}
