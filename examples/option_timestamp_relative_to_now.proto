// Copyright 2023 Buf Technologies, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

import "buf/validate/validate.proto";
import "google/protobuf/timestamp.proto";

message BreakingNews {
  string summary = 1;
  google.protobuf.Timestamp create_time = 2 [(buf.validate.field).timestamp = {
    // `within` validates that a timestamp must be within a certain amount of
    // from the current time when validation takes place
    // This validates that the news must be created during [now - 1hr, now + 1hr].
    within: {seconds: 3600}
    // `lt_now` validates that a timestamp is before the current time when validation
    // takes place.
    lt_now: true
    // Similarly, you can set `gt_now` to validate a timestamp in the future. Although
    // it is not necessary in this case.
    gt_now: false
  }];
}
