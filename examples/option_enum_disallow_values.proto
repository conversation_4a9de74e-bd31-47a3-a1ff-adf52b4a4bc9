// Copyright 2023 Buf Technologies, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

import "buf/validate/validate.proto";

service ReminderService {
  // CreateReminder creates a reminder.
  rpc CreateReminder(CreateReminderRequest) returns (CreateReminderResponse);
}

message CreateReminderRequest {
  // reminder status is the status of a reminder.
  ReminderStatus reminder_status = 1 [(buf.validate.field).enum = {
    // `not_in` specifies a list and validates that an enum field is not any
    // variant from the list.
    // In this case, it validates that you cannot create a reminder with
    // status 0, 1 or 4.
    not_in: [
      0,
      3,
      4
    ]
  }];
}

message CreateReminderResponse {}

enum ReminderStatus {
  REMINDER_STATUS_UNSPECIFIED = 0;
  REMINDER_STATUS_SCHEDULED = 1;
  REMINDER_STATUS_UNSCHEDULED = 2;
  REMINDER_STATUS_COMPLETED = 3;
  REMINDER_STATUS_CANCELED = 4;
}
