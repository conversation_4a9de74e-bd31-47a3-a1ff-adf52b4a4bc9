// Copyright 2023 Buf Technologies, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

import "buf/validate/validate.proto";

service NewsLetterService {
  rpc JoinNewsLetter(JoinNewsLetterRequest) returns (JoinNewsLetterResponse);
}

message JoinNewsLetterRequest {
  string email = 1 [(buf.validate.field).cel = {
    id: "join_news_letter_request_valid_email"
    expression:
      "this.isEmail() ? ''"
      // The `+` operator is overloaded for strings and concatenates two strings.
      // In this case, it is used to construct a more meaningful error message.
      // An error message will look like: "xyz" is not a valid email.
      ": '\"' + this + '\" is not a valid email'"
  }];
}

message JoinNewsLetterResponse {}
