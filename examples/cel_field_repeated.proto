// Copyright 2023 Buf Technologies, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

import "buf/validate/validate.proto";

message IPAllowlist {
  repeated string allow_cidr = 1 [(buf.validate.field).repeated = {
    min_items: 1
    items: {
      cel: [
        {
          id: "ip_prefix"
          message: "value must be IPv4 prefix"
          expression: "this.isIpPrefix(4, true)"
        }
      ]
    }
  }];
}
