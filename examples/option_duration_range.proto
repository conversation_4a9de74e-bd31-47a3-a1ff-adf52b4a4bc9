// Copyright 2023 Buf Technologies, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

import "buf/validate/validate.proto";
import "google/protobuf/duration.proto";

service SoundTrackService {
  // UploadSoundTrack uploads a sound track.
  rpc UploadSoundTrack(UploadSoundTrackRequest) returns (UploadSoundTrackResponse);
}

message UploadSoundTrackRequest {
  // data is the sound track data.
  bytes data = 1;
  // duration is the sound track's duration
  google.protobuf.Duration duration = 5 [
    // the field is required
    (buf.validate.field).required = true,
    // duration must be longer than 1.1 second but no longer than 10 hours.
    (buf.validate.field).duration = {
      // Validates that duration is greater than 1.1 second
      gt: {
        seconds: 1
        nanos: *********
      }
      // Validates that duration is less than or equal to 10 hours.
      lte: {seconds: 36000}
    }
  ];
}

message UploadSoundTrackResponse {}
