// Copyright 2023 Buf Technologies, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

import "buf/validate/validate.proto";
import "google/protobuf/any.proto";

// NetworkError is a network error.
message NetworkError {
  // message is the error message.
  uint64 message = 1;
  // detail is the network error's detail
  google.protobuf.Any detail = 2 [(buf.validate.field).any = {
    // Validate that detail must be either google.protobuf.Struct or CustomErrorDetails.
    // Values in `in` should be a string representing a type_url, which is
    // typically `type.googleapis/<fully qualified name>`.
    in: [
      "type.googleapis.com/google.protobuf.Struct",
      "type.googleapis.com/CustomErrorDetails"
    ]
  }];
}
