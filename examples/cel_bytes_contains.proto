// Copyright 2023 Buf Technologies, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

import "buf/validate/validate.proto";

// Application is an application.
message Application {
  // binary is the application's binary
  bytes binary = 1 [(buf.validate.field).cel = {
    id: "without_malicious_code"
    message: "binary should not contain malicious code"
    // `contains` returns if the receiver `bytes` contains the argument `bytes`.
    // This validates that the application binary should not contain bytes for
    // `'malicious code'`.
    // Note that when checking whether bytes for a string is contained, `bytes()`
    // must be called to convert the string into bytes.
    expression: "!this.contains(bytes('malicious code'))"
  }];
}
