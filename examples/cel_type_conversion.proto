// Copyright 2023 Buf Technologies, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

import "buf/validate/validate.proto";

message Sound {
  uint32 frequency_hz = 1 [(buf.validate.field).cel = {
    id: "frequency_in_the_audible_range"
    expression:
      // `string()` converts an `uint` (and other primitive types) to a `string`.
      // In this case, the frequency is first converted to a string before being
      // prepended to the error message.
      "this < 20 ? string(this) + ' hz is too low for human ears'"
      ": this > 20000 ? string(this) + ' hz is too high for human ears'"
      ": ''"
    // Other type conversions are `uint()`, `int()`, `bytes()` and `double()`.
    // See https://github.com/google/cel-spec/blob/master/doc/langdef.md#list-of-standard-definitions.
  }];
}
