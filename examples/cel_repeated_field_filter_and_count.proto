// Copyright 2023 Buf Technologies, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

import "buf/validate/validate.proto";

// FiveTruthsThreeLies is a new game based on the classic Two Truths One Lie.
message FiveTruthsThreeLies {
  option (buf.validate.message).cel = {
    id: "exactly_three_lies"
    message: "there must be exactly three lies"
    // `this.statement.filter` evaluates to a new list, whose elements are those
    // from the original one that satisfies the predicate. In this case, the filter
    // evaluates to the list of statements that are true.
    // `size` evaluates to the length of the list, and we compare it to 3 to make
    // validate after filtering out the true statements, there are three false statements left.
    expression: "size(this.statement.filter(s, !s.is_truth)) == 3"
  };
  option (buf.validate.message).cel = {
    id: "exactly_five_truths"
    message: "there must be exactly five truths"
    // Similarly, validate that exactly five truths
    expression: "size(this.statement.filter(s, s.is_truth)) == 5"
  };

  repeated TruthOrLieStatement statement = 1;
}

message TruthOrLieStatement {
  string statement = 1;
  bool is_truth = 2;
}
