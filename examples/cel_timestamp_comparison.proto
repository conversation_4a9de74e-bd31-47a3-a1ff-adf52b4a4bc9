// Copyright 2023 Buf Technologies, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

import "buf/validate/validate.proto";
import "google/protobuf/timestamp.proto";

message EventFromTheNineteenthCentury {
  google.protobuf.Timestamp time = 1 [(buf.validate.field).cel = {
    id: "timestamp_in_the_1800s"
    message: "the event must be from the nineteenth century"
    expression:
      // `timestamp()` converts a string to a timestamp according to [rtf3339]
      // (https://datatracker.ietf.org/doc/html/rfc3339#section-5.8).
      // The `<=`, `<`, `>`, `>=`, `==`, `!=` operators are overloaded for
      // timestamps as well. Where 'less than' really means 'earlier than'.
      // In this case, the expression validates that the field is after
      // or equal to 1800-01-01T00:00:00+00:00 but before 1900-01-01T00:00:00+00:00.
      "timestamp('1800-01-01T00:00:00+00:00') <= this"
      "&& this < timestamp('1900-01-01T00:00:00+00:00')"
  }];
}
