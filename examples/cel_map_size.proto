// Copyright 2023 Buf Technologies, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

import "buf/validate/validate.proto";

// PenInventory is the pen inventory.
message PenInventory {
  option (buf.validate.message).cel = {
    id: "at_most_30_colors"
    message: "there must not be more than 30 colors"
    // `size(some_map)` returns the number of keys in this map.
    // In this case, it validates that the map has at most 30 keys.
    expression: "size(this.color_to_count) <= 30"
  };

  // color_to_count maps from a color id to the amount of pens of the color.
  map<uint32, uint32> color_to_count = 1;
}
