// Copyright 2023 Buf Technologies, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

import "buf/validate/validate.proto";

// TeamRoles has role information for each player.
message TeamRoles {
  option (buf.validate.message).cel = {
    id: "exactly_one_captain"
    message: "there must be exactly one captain"
    // `exists_one` validates that exactly one key in the map satisfies the predicate.
    // In this case, it validates that exactly one player in the team has the
    // captain role.
    expression: "this.name_to_role.exists_one(name, this.name_to_role[name] == 1)"
  };

  // name_to_role maps from a team member's name to role.
  map<string, TeamRole> name_to_role = 1;
}

enum TeamRole {
  TEAM_ROLE_UNSPECIFIED = 0;
  TEAM_ROLE_CAPTAIN = 1;
  TEAM_ROLE_PLAYER = 2;
}
