# Example for `/etc/nginx/conf.d/default.conf`
server {
  listen 80;
  server_name localhost;

  location / {
    root /usr/share/nginx/html;
    index index.html;
    try_files $uri $uri/ /index.html;
  }

  location /files {
    alias /app/data/files/;
    autoindex on;
  }

  location /reports {
    alias /app/data/reports/;
    autoindex on;
  }

  # redirect server error pages to the static page /50x.html
  error_page   500 502 503 504  /50x.html;
  location = /50x.html {
    root /usr/share/nginx/html;
  }
}
