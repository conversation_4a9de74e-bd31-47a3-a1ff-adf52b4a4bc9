# these ARGs are used in the FROM statements or as global variables if declared again (without the default value)
# use `cr.ttyuyin.com/probe/nginx:1.28.0` as runtime image in Sentinel
ARG RUNTIME_IMAGE="nginx:1.28.0"

FROM $RUNTIME_IMAGE

RUN sed -i 's/deb.debian.org/mirrors.aliyun.com/g' /etc/apt/sources.list.d/debian.sources && \
    apt update && \
    apt install -y iputils-ping net-tools procps dnsutils vim locales fonts-wqy-zenhei fonts-noto-cjk && \
    sed -i 's/# zh_CN.UTF-8 UTF-8/zh_CN.UTF-8 UTF-8/g' /etc/locale.gen && \
    locale-gen

ENV TZ=Asia/Shanghai \
    LANG=zh_CN.UTF-8

COPY storage/nginx.conf /etc/nginx/nginx.conf
COPY storage/default.conf /etc/nginx/conf.d/default.conf

ENTRYPOINT ["nginx", "-g", "daemon off;"]
