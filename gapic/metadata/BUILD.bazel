# This file was automatically generated by BuildFileGenerator

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")

proto_library(
    name = "metadata_proto",
    srcs = [
        "gapic_metadata.proto",
    ],
    deps = [
    ],
)

##############################################################################
# Java
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_grpc_library",
    "java_proto_library",
)

java_proto_library(
    name = "metadata_java_proto",
    deps = [":metadata_proto"],
)

java_grpc_library(
    name = "metadata_java_grpc",
    srcs = [":metadata_proto"],
    deps = [":metadata_java_proto"],
)

##############################################################################
# Go
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_proto_library",
)

go_proto_library(
    name = "metadata_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "google.golang.org/genproto/googleapis/gapic/metadata",
    protos = [":metadata_proto"],
    deps = [
    ],
)

##############################################################################
# Python
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "py_proto_library",
    "py_gapic_assembly_pkg",
)

py_proto_library(
    name = "metadata_py_proto",
    deps = [":metadata_proto"],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "metadata-py",
    deps = [
        ":metadata_py_proto"
    ],
)

##############################################################################
# PHP
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_proto_library",
)

php_proto_library(
    name = "metadata_php_proto",
    deps = [":metadata_proto"],
)

##############################################################################
# Node.js
##############################################################################

##############################################################################
# Ruby
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "metadata_ruby_proto",
    deps = [":metadata_proto"],
)

ruby_grpc_library(
    name = "metadata_ruby_grpc",
    srcs = [":metadata_proto"],
    deps = [":metadata_ruby_proto"],
)

##############################################################################
# C#
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_grpc_library",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "metadata_csharp_proto",
    deps = [":metadata_proto"],
)

csharp_grpc_library(
    name = "metadata_csharp_grpc",
    srcs = [":metadata_proto"],
    deps = [":metadata_csharp_proto"],
)

##############################################################################
# C++
##############################################################################
# Put your C++ code here
