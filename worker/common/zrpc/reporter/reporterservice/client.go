package reporterservice

import (
	"context"

	"github.com/zeromicro/go-zero/zrpc"
	"google.golang.org/grpc"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/interceptors/clientinterceptors"

	client "gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/client/reporter"
)

type ReporterRpc struct {
	conf zrpc.RpcClientConf
	name string

	client client.Reporter
}

func NewReporterRpc(c zrpc.RpcClientConf) *ReporterRpc {
	return &ReporterRpc{
		conf:   c,
		name:   c.Etcd.Key,
		client: client.NewReporter(zrpc.MustNewClient(c, clientinterceptors.UnaryUserInfoClientOption())),
	}
}

func (c *ReporterRpc) GetPlanRecord(ctx context.Context, req *client.GetPlanRecordRequest, opts ...grpc.CallOption) (resp *client.GetPlanRecordResponse, err error) {
	return c.client.GetPlanRecord(ctx, req, opts...)
}
