#!/bin/sh

path="/app/worker"
project="worker"


mkdir -p ${path}/mqc/etc/

curl -X GET -s -H "Content-Type:application/json;charset=UTF-8" \
 -H "Authorization:${APOLLO_AUTH_TOKEN}" \
  http://${APOLLO_ADDR}/openapi/v1/envs/dev/apps/probe-backend/clusters/${project}/namespaces/mqc.yaml | jq .items[0].value | sed 's/^"\|"$//g' | sed ':a;:N;$!ba;s/\\n/\n/g' \
   > ${path}/mqc/etc/${project}.yaml

nohup ${path}/${project}.linux --mqc-config ${path}/mqc/etc/${project}.yaml >> /app/logs/${project}/stdout.log 2>&1 &
