package config

import (
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/service"
	"github.com/zeromicro/go-zero/core/stores/redis"
	"github.com/zeromicro/go-zero/zrpc"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
	consumerv2 "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworkerv2/consumer"
)

type Config struct {
	service.ServiceConf

	Redis redis.RedisKeyConf

	ReporterRPC    zrpc.RpcClientConf
	WorkerConsumer consumerv2.Config

	ReporterURI string
	PVCPath     string
}

func (c Config) LogConfig() logx.LogConf {
	return c.ServiceConf.Log
}

func (c Config) ListenOn() string {
	return constants.NoNeedToListen
}
