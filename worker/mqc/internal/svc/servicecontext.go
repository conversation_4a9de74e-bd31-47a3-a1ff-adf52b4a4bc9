package svc

import (
	"github.com/zeromicro/go-zero/core/stores/redis"

	consumerv2 "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworkerv2/consumer"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/worker/common/zrpc/reporter/reporterservice"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/worker/mqc/internal/config"
)

type ServiceContext struct {
	Config config.Config

	Redis *redis.Redis

	ReporterRPC    *reporterservice.ReporterRpc
	WorkerConsumer *consumerv2.Consumer
}

func NewServiceContext(c config.Config) *ServiceContext {
	return &ServiceContext{
		Config: c,

		Redis: redis.MustNewRedis(c.Redis.RedisConf, redis.WithDB(c.Redis.RedisConf.DB)),

		ReporterRPC:    reporterservice.NewReporterRpc(c.ReporterRPC),
		WorkerConsumer: consumerv2.NewConsumer(c.WorkerConsumer),
	}
}
