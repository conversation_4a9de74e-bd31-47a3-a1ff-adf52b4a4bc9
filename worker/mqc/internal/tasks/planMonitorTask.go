package tasks

import (
	"context"
	"encoding/json"
	"fmt"
	"sync/atomic"
	"time"

	"github.com/jinzhu/copier"
	"github.com/pkg/errors"
	"github.com/valyala/fasthttp"
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/trace"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworkerv2/base"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworker"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/codes"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/rpc/pb"
	reporterCli "gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/client/reporter"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/worker/mqc/internal/svc"
)

type PlanMonitorTaskLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

type TestInfo struct {
	NoCaseServices []string `json:"no_case_services"`
}

type Record struct {
	Status      string   `json:"status"`
	TotalCase   int64    `json:"total_case"`
	SuccessCase int64    `json:"success_case"`
	FailureCase int64    `json:"failure_case"`
	ReportPath  string   `json:"report_path"`
	TestInfo    TestInfo `json:"test_info"`
}

type CallBackBody struct {
	Status string `json:"status"`
	Record Record `json:"record"`
}

func NewPlanMonitorTaskLogic(ctx context.Context, svcCtx *svc.ServiceContext) *PlanMonitorTaskLogic {
	return &PlanMonitorTaskLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *PlanMonitorTaskLogic) Consume(payload []byte) (err error) {
	var req pb.PlanMonitorReq
	if err = protobuf.UnmarshalJSON(payload, &req); err != nil {
		return errors.Wrapf(
			errorx.Err(errorx.SerializationError, err.Error()),
			"failed to unmarshal the plan-monitor task payload[%s], error: %+v", payload, err,
		)
	}

	if err = l.Run(&req); err != nil {
		return errors.Wrapf(
			errorx.Err(codes.PlanMonitorTaskExecuteFailure, err.Error()),
			"failed to run the plan-monitor task[%s, %s, %s], error: %+v", req.GetProjectId(), req.GetTaskId(),
			req.GetPlanExecuteId(), err,
		)
	}

	return nil
}

func (l *PlanMonitorTaskLogic) Run(req *pb.PlanMonitorReq) (err error) {
	l.setup()

	err = l.run(req)

	l.teardown()

	return err
}

func (l *PlanMonitorTaskLogic) setup() {
}

func (l *PlanMonitorTaskLogic) run(req *pb.PlanMonitorReq) error {
	if req.GetCallbackUrl() == "" || req.GetTimeout() == 0 {
		return nil
	}

	// 错误次数
	var errNum uint64 = 0
	// 当前时间戳
	var startTime = time.Now().Unix()

	// 回调消息体
	var body = &CallBackBody{}

	var testInfo TestInfo
	_ = copier.Copy(&testInfo, &req.TestInfo)

	for {
		respPlanRecord, err := l.svcCtx.ReporterRPC.GetPlanRecord(
			l.ctx, &reporterCli.GetPlanRecordRequest{
				ProjectId:     req.GetProjectId(),
				TaskId:        req.GetTaskId(),
				PlanExecuteId: req.GetPlanExecuteId(),
			},
		)
		if err != nil {
			atomic.AddUint64(&errNum, 1)
			if errNum > 3 {
				body = &CallBackBody{
					Status: "无法获取计划结果",
					Record: Record{
						Status:      "Exception",
						TotalCase:   0,
						SuccessCase: 0,
						FailureCase: 0,
						ReportPath:  "",
						TestInfo:    testInfo,
					},
				}
				break
			}
		}

		if respPlanRecord.GetEndedAt() > 0 {
			planStatus := respPlanRecord.GetStatus()
			// 精准测试用例数量为0执行结果为成功
			if respPlanRecord.GetPurposeType() == "PRECISION_TESTING" && respPlanRecord.GetTotalCase() == 0 {
				planStatus = "Success"
			}

			reportPath := fmt.Sprintf(
				"%s/#/api-test/testplan/reporter?plan_id=%s&task_id=%s&plan_execute_id=%s&project_id=%s&execute_id=%s&plan_purpose=%s",
				l.svcCtx.Config.ReporterURI,
				respPlanRecord.GetPlanId(),
				req.GetTaskId(),
				req.GetPlanExecuteId(),
				req.GetProjectId(),
				req.GetPlanExecuteId(),
				respPlanRecord.GetPurposeType(),
			)
			body = &CallBackBody{
				Status: "success",
				Record: Record{
					Status:      planStatus,
					TotalCase:   respPlanRecord.GetTotalCase(),
					SuccessCase: respPlanRecord.GetSuccessCase(),
					FailureCase: respPlanRecord.GetFailureCase(),
					ReportPath:  reportPath,
					TestInfo:    testInfo,
				},
			}
			break
		}

		now := time.Now().Unix()
		if now-startTime > req.GetTimeout() {
			body = &CallBackBody{
				Status: fmt.Sprintf("任务超时，大于%d秒", req.GetTimeout()),
				Record: Record{
					Status:      "Exception",
					TotalCase:   0,
					SuccessCase: 0,
					FailureCase: 0,
					ReportPath:  "",
					TestInfo:    testInfo,
				},
			}
			break
		}

		time.Sleep(1 * time.Second)
	}

	return l.callback(req, body)
}

func (l *PlanMonitorTaskLogic) callback(in *pb.PlanMonitorReq, body *CallBackBody) (err error) {
	req := fasthttp.AcquireRequest()
	resp := fasthttp.AcquireResponse()
	defer func() {
		fasthttp.ReleaseResponse(resp)
		fasthttp.ReleaseRequest(req)
	}()

	req.Header.SetContentType("application/json")
	req.Header.SetMethod("POST")

	req.SetRequestURI(in.GetCallbackUrl())

	requestBody, _ := json.Marshal(body)
	req.SetBody(requestBody)

	if err = fasthttp.Do(req, resp); err != nil {
		logx.Errorf(
			"计划执行Id「%s」回调错误为「%s」, 当前时间戳（纳秒）是：%d", in.GetPlanExecuteId(), err.Error(),
			time.Now().UnixNano(),
		)
		return err
	}

	logx.Infof(
		"计划执行Id「%s」回调响应为「%s」, 当前时间戳（纳秒）是：%d", in.GetPlanExecuteId(), string(resp.Body()),
		time.Now().UnixNano(),
	)
	return
}

func (l *PlanMonitorTaskLogic) teardown() {
}

// Deprecated: use new api.
func PlanMonitorTask(svcCtx *svc.ServiceContext) mqworker.BytesPayloadTaskWithContext {
	return func(ctx context.Context, payload []byte) error {
		return NewPlanMonitorTaskLogic(ctx, svcCtx).Consume(payload)
	}
}

type Processor struct {
	svcCtx *svc.ServiceContext
}

func (processor *Processor) ProcessTask(ctx context.Context, task *base.Task) (result []byte, err error) {
	logx.WithContext(ctx).Debug(
		fmt.Sprintf(
			"processor TraceID:%s, task name:%s", trace.TraceIDFromContext(ctx), task.Typename,
		),
	)
	logx.WithContext(ctx).Debug(fmt.Sprintf("processor SpanID:%s", trace.SpanIDFromContext(ctx)))

	if err = NewPlanMonitorTaskLogic(ctx, processor.svcCtx).Consume(task.Payload); err != nil {
		logx.WithContext(ctx).Error(fmt.Sprintf("processor error:%s", err))
		return nil, err
	}

	return []byte("success"), nil
}

func NewProcessor(svcCtx *svc.ServiceContext) (call base.Handler) {
	return &Processor{
		svcCtx: svcCtx,
	}
}
