package tasks

import (
	"testing"

	"gopkg.in/ini.v1"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
)

func TestPyParser_Parse(t *testing.T) {
	type fields struct {
		RootPath  string
		IsSubPath bool
	}
	tests := []struct {
		name    string
		fields  fields
		wantErr bool
	}{
		{
			name: "atx_test_project",
			fields: fields{
				RootPath:  "/Users/<USER>/Workspace/PycharmProjects/atx_test_project/testcase",
				IsSubPath: true,
			},
			wantErr: false,
		},
		{
			name: "ui_test_project",
			fields: fields{
				RootPath:  "/Users/<USER>/Workspace/PycharmProjects/ui_test_project/testcase",
				IsSubPath: true,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			p := NewPyParser(tt.fields.RootPath, tt.fields.IsSubPath)
			if err := p.Parse(); (err != nil) != tt.wantErr {
				t.Errorf("Parse() error = %v, wantErr %v", err, tt.wantErr)
			}

			node := p.GetRootNode()
			t.Logf("GetRootNode() = %s", protobuf.MarshalJSONToStringIgnoreError(node))
		})
	}
}

func TestLoadIniFile(t *testing.T) {
	type fields struct {
		Path string
	}
	tests := []struct {
		name   string
		fields fields
	}{
		{
			name: "pytest.ini",
			fields: fields{
				Path: "/Users/<USER>/Workspace/PycharmProjects/ios_autotest/pytest.ini",
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			cfg, err := ini.LoadSources(ini.LoadOptions{
				AllowPythonMultilineValues: true,
			}, tt.fields.Path)
			if err != nil {
				t.Fatal(err)
			}

			t.Logf("ini.Load() = %+v", cfg)
		})
	}
}
