package tasks

import (
	"bufio"
	"context"
	"fmt"
	"io"
	"io/fs"
	"net/url"
	"os"
	"path/filepath"
	"strings"

	"github.com/RichardKnop/machinery/v2/tasks"
	"github.com/dlclark/regexp2"
	"github.com/go-python/gpython/ast"
	"github.com/go-python/gpython/parser"
	"github.com/go-python/gpython/py"
	"github.com/pkg/errors"
	"github.com/spf13/cast"
	"github.com/zeromicro/go-zero/core/conf"
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/trace"
	"github.com/zyedidia/generic"
	"github.com/zyedidia/generic/hashmap"
	"gopkg.in/ini.v1"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworker"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworkerv2/base"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/redislock"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/codes"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	commonpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/worker/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/worker/mqc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/worker/mqc/internal/types"
)

const (
	constPyTestIniFile                = "pytest.ini"
	constPyTestIniSection             = "pytest"
	constPyTestIniSectionKeyTestPaths = "testpaths"

	constPyCacheDirectory        = "__pycache__"
	constPlatformInfoYamlFile    = "platform.yaml"
	constFolderInfoYamlFile      = "folder_info.yaml"
	constPyInitFile              = "__init__.py"
	constGroupNameFileAlias      = "file_alias"
	constPyTestClassPrefix       = "Test"
	constPyTestFunctionPrefix    = "test"
	constPyClassAliasFuncName    = "allure.feature"
	constPyFunctionAliasFuncName = "allure.title"
	constPyTestMarkPrefix        = "pytest.mark."
	constPyTestMarkParametrize   = constPyTestMarkPrefix + "parametrize"
)

var (
	pyTestFileRegexCompile    = regexp2.MustCompile(`^(test_.*?\.py|.*?_test\.py)$`, regexp2.None)
	textFileAliasRegexCompile = regexp2.MustCompile(
		fmt.Sprintf(
			`#\s*textfile_alias\s*=\s*(?<%s>.*?)$`, constGroupNameFileAlias,
		), regexp2.None,
	)
)

type ParsePythonProjectTaskLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewParsePythonProjectTaskLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ParsePythonProjectTaskLogic {
	return &ParsePythonProjectTaskLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *ParsePythonProjectTaskLogic) Consume(payload []byte) (result []byte, err error) {
	var info commonpb.ParsePythonProjectTaskInfo
	if err = protobuf.UnmarshalJSON(payload, &info); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.SerializationError, err.Error()),
			"failed to unmarshal the parse-python-project task payload[%s], error: %+v", payload, err,
		)
	}

	projectID := info.GetProjectId()
	gitConfig := info.GetConfig()
	gitConfigID := gitConfig.GetConfigId()
	gitURL := gitConfig.GetUrl()
	gitAccessToken := gitConfig.GetAccessToken()
	gitBranch := gitConfig.GetBranch()
	triggerTime := info.GetTriggerTime()

	u, err := url.Parse(gitURL)
	if err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.ValidateParamError, err.Error()), "failed to parse the git url[%s], error: %+v", gitURL,
			err,
		)
	} else if _, ok := u.User.Password(); !ok || u.User.Username() == "" {
		u.User = url.UserPassword(constants.ConstDefaultGitUsername, gitAccessToken)
	}
	gitURLWithAuth := u.String()

	// acquire redis lock
	key := fmt.Sprintf(
		"%s:%s:%s", common.ConstLockParsePythonProjectTaskProjectIDConfigIDPrefix, projectID, gitConfigID,
	)
	lock, err := redislock.NewRedisLockAndAcquire(
		l.svcCtx.Redis, key, redislock.WithValue(cast.ToString(triggerTime)),
		redislock.WithExpire(common.ConstLockParsePythonProjectTaskExpireTime),
	)
	if err != nil {
		if re, ok := errorx.RootError(err); ok && re.Code() == errorx.AcquireRedisLockFailure {
			val, err := l.svcCtx.Redis.Get(key)
			if err != nil {
				return nil, err
			}

			currentTriggerTime := cast.ToInt64(val)
			if triggerTime > currentTriggerTime {
				// 当前事件的触发时间比正在处理的事件的触发时间晚，稍后重试
				return nil, tasks.NewErrRetryTaskLater(
					fmt.Sprintf(
						"the task[%s, %s, %d] that was triggered earlier is being processed and the current task[%s, %s, %d] will be retried later",
						projectID, gitConfigID, currentTriggerTime, projectID, gitConfigID, triggerTime,
					), common.ConstLockParsePythonProjectTaskExpireTime,
				)
			} else {
				// 当前事件的触发时间不比正在处理的事件的触发时间晚，此任务将会被丢弃
				l.Infof(
					"the current task[%s, %s, %d] is earlier than the processing task[%s, %s, %d]", projectID,
					gitConfigID, triggerTime, projectID, gitConfigID, currentTriggerTime,
				)
				return nil, nil
			}
		}

		return nil, err
	}
	defer func() {
		// release redis lock
		e := lock.Release()
		if e != nil {
			if re, ok := errorx.RootError(e); ok && re.Code() != errorx.ReleaseRedisLockFailure && err == nil {
				err = e
			}
		}
	}()

	targetPath := filepath.Join(l.svcCtx.Config.PVCPath, fmt.Sprintf("%s_%s", projectID, gitConfigID))
	defer func(path string) {
		err := os.RemoveAll(path)
		if err != nil {
			l.Errorf("failed to remove the target path[%s], error: %+v", targetPath, err)
		}
	}(targetPath)

	commit, err := utils.CloneWithContext(l.ctx, gitURLWithAuth, targetPath, gitBranch)
	if err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.GitOperationFailure, err.Error()),
			"failed to clone the python project, git: %s, branch: %s, error: %+v",
			gitURL, gitBranch, err,
		)
	}

	l.Infof(
		"finish to clone the python project, git: %s, path: %s, branch: %s, commit: %s",
		gitURL, targetPath, gitBranch, commit.String(),
	)

	rootPath := l.getRootPath(targetPath)
	pyParser := NewPyParser(rootPath, rootPath != targetPath)
	if err = pyParser.Parse(); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(codes.ParsePythonProjectTaskFailure, err.Error()),
			"failed to parse the python project, git: %s, branch: %s, error: %+v",
			gitURL, gitBranch, err,
		)
	}

	message := &commonpb.ParsePythonProjectTaskResult{
		ProjectId:      projectID,
		ConfigId:       gitConfigID,
		RootNode:       pyParser.GetRootNode(),
		TriggerAccount: info.GetTriggerAccount(),
		TriggerTime:    info.GetTriggerTime(),
	}

	return protobuf.MarshalJSON(message)
}

func (l *ParsePythonProjectTaskLogic) getRootPath(targetPath string) (rootPath string) {
	platformInfoYamlFile := filepath.Join(targetPath, constPlatformInfoYamlFile)
	pytestIniFile := filepath.Join(targetPath, constPyTestIniFile)

	rootPath = targetPath

	if utils.Exists(platformInfoYamlFile) {
		var pi types.PlatformInfo
		if err := conf.Load(platformInfoYamlFile, &pi); err != nil {
			l.Errorf("failed to load the file[%s], error: %+v", constPlatformInfoYamlFile, err)
			return rootPath
		}

		rootPath = pi.TestPath
	} else if utils.Exists(pytestIniFile) {
		cfg, err := ini.LoadSources(
			ini.LoadOptions{
				AllowPythonMultilineValues: true,
			}, pytestIniFile,
		)
		if err != nil {
			l.Errorf("failed to load the file[%s], error: %+v", constPyTestIniFile, err)
			return rootPath
		}

		sec, err := cfg.GetSection(constPyTestIniSection)
		if err != nil {
			l.Errorf(
				"failed to get the section[%s] from file[%s], error: %+v", constPyTestIniSection, constPyTestIniFile,
				err,
			)
			return rootPath
		}

		key, err := sec.GetKey(constPyTestIniSectionKeyTestPaths)
		if err != nil {
			l.Errorf(
				"failed to get the key[%s] of section[%s] from file[%s], error: %+v", constPyTestIniSectionKeyTestPaths,
				constPyTestIniSection, constPyTestIniFile, err,
			)
			return rootPath
		}

		values := key.Strings("\n")
		if len(values) == 0 {
			l.Errorf(
				"failed to get the values of key[%s] in section[%s] from file[%s], error: %+v",
				constPyTestIniSectionKeyTestPaths, constPyTestIniSection, constPyTestIniFile, err,
			)
			return rootPath
		}

		rootPath = filepath.Join(targetPath, values[0])
	} else {
		l.Errorf(
			"failed to find the test path, because of neither [%s] nor [%s] can be located", constPlatformInfoYamlFile,
			constPyTestIniFile,
		)
		return rootPath
	}

	if !filepath.IsAbs(rootPath) {
		rootPath = filepath.Join(targetPath, rootPath)
	}

	return rootPath
}

// Deprecated: use new api.
func ParsePythonProjectTask(svcCtx *svc.ServiceContext) mqworker.BytesPayloadBytesResultTaskWithContext {
	return func(ctx context.Context, payload []byte) ([]byte, error) {
		return NewParsePythonProjectTaskLogic(ctx, svcCtx).Consume(payload)
	}
}

type ProcessorParse struct {
	svcCtx *svc.ServiceContext
}

func (processor *ProcessorParse) ProcessTask(ctx context.Context, task *base.Task) (result []byte, err error) {
	logx.WithContext(ctx).Debug(
		fmt.Sprintf(
			"processor TraceID:%s, task name:%s", trace.TraceIDFromContext(ctx), task.Typename,
		),
	)
	logx.WithContext(ctx).Debug(fmt.Sprintf("processor SpanID:%s", trace.SpanIDFromContext(ctx)))
	consume, err := NewParsePythonProjectTaskLogic(ctx, processor.svcCtx).Consume(task.Payload)
	if err != nil {
		logx.WithContext(ctx).Error(fmt.Sprintf("processor error:%s", err))
		return nil, err
	}

	return consume, nil
}

func NewProcessorParse(svcCtx *svc.ServiceContext) (call base.Handler) {
	return &ProcessorParse{
		svcCtx: svcCtx,
	}
}

type PyParser struct {
	RootPath string

	isSubPath      bool
	rootParentPath string
	pathNodeCache  *hashmap.Map[string, *commonpb.Node]
}

func NewPyParser(rootPath string, isSubPath bool) *PyParser {
	rootParentPath := filepath.Dir(rootPath)
	if !isSubPath {
		rootParentPath = rootPath
	}

	return &PyParser{
		RootPath: rootPath,

		isSubPath:      isSubPath,
		rootParentPath: rootParentPath,
		pathNodeCache: hashmap.New[string, *commonpb.Node](
			common.ConstHashMapCapacity, generic.Equals[string], generic.HashString,
		),
	}
}

func (p *PyParser) relativeToRootParentPath(path string) string {
	relativePath, _ := filepath.Rel(p.rootParentPath, path)
	return relativePath
}

func (p *PyParser) absoluteWithRootParentPath(path string) string {
	return filepath.Join(p.rootParentPath, path)
}

func (p *PyParser) walk(path string, info fs.FileInfo, err error) error {
	if err != nil && err != filepath.SkipDir {
		return err
	} else if err == filepath.SkipDir {
		return nil
	}

	if info.IsDir() {
		// 排除指定目录及隐藏目录
		if info.Name() == constPyCacheDirectory || info.Name() != "." && strings.HasPrefix(info.Name(), ".") {
			return filepath.SkipDir
		}

		// 目录
		p.newDirectoryNode(path, info)
	} else {
		// 排除隐藏文件
		if info.Name() != "." && strings.HasPrefix(info.Name(), ".") {
			return nil
		}

		// 文件
		if err = p.newFileNode(path, info); err != nil {
			logx.Errorf("failed to parse the file[%s], error: %+v", path, err)
		}
	}

	return nil
}

func (p *PyParser) newDirectoryNode(path string, info fs.FileInfo) {
	name := info.Name()
	relativePath := p.relativeToRootParentPath(path)
	if relativePath == "." {
		name = "."
	}

	node := &commonpb.Node{
		Name: name,
		Path: relativePath,
		Type: commonpb.NodeType_NT_DIRECTORY,
	}
	p.pathNodeCache.Put(relativePath, node)

	if p.RootPath != path {
		parent, ok := p.pathNodeCache.Get(p.relativeToRootParentPath(filepath.Dir(path)))
		if ok {
			parent.Children = append(parent.Children, node)
		}
	}
}

func (p *PyParser) newFileNode(path string, info fs.FileInfo) (err error) {
	parent, ok := p.pathNodeCache.Get(p.relativeToRootParentPath(filepath.Dir(path)))
	if !ok {
		return nil
	}

	relativePath := p.relativeToRootParentPath(path)
	node := &commonpb.Node{
		Name: info.Name(),
		Path: relativePath,
		Type: commonpb.NodeType_NT_FILE,
	}
	parent.Children = append(parent.Children, node)
	p.pathNodeCache.Put(node.Path, node)

	if info.Name() == constFolderInfoYamlFile {
		// 目录信息文件（解析文件内容，把`folder_alias`的值设置为父节点的别名）
		var fi types.FolderInfo
		if err = conf.Load(path, &fi); err != nil {
			return err
		}

		parent.Alias = fi.FolderAlias
	} else if info.Name() == constPyInitFile {
		// Package
		node.Type = commonpb.NodeType_NT_PACKAGE
	} else if ok, _ = pyTestFileRegexCompile.MatchString(info.Name()); ok {
		// Module
		node.Type = commonpb.NodeType_NT_MODULE

		err = p.astParsePyModule(node)
	}

	return err
}

func (p *PyParser) astParsePyModule(module *commonpb.Node) (err error) {
	file, err := os.Open(p.absoluteWithRootParentPath(module.Path))
	if err != nil {
		return err
	}
	defer func(file *os.File) {
		err := file.Close()
		if err != nil {
			logx.Errorf("failed to close file, error: %+v", err)
		}
	}(file)

	module.Alias = getFileAliasFromFirstLine(file)

	mod, err := parser.Parse(file, module.Name, py.ExecMode)
	if err != nil {
		return err
	}

	p.astWalk(module, mod)

	return
}

func (p *PyParser) astWalk(parent *commonpb.Node, obj ast.Ast) {
	switch o := obj.(type) {
	case *ast.Module:
		// Body []Stmt
		p.astWalkStmts(parent, o.Body)
	case *ast.ClassDef:
		if strings.HasPrefix(string(o.Name), constPyTestClassPrefix) {
			p.newClassNode(parent, o)
		}
	case *ast.FunctionDef:
		if strings.HasPrefix(string(o.Name), constPyTestFunctionPrefix) {
			p.newFunctionNode(parent, o)
		}
	}
}

func (p *PyParser) astWalkStmts(parent *commonpb.Node, stmts []ast.Stmt) {
	for _, stmt := range stmts {
		p.astWalk(parent, stmt)
	}
}

func (p *PyParser) newClassNode(parent *commonpb.Node, def *ast.ClassDef) {
	name := string(def.Name)
	id := fmt.Sprintf("%s::%s", parent.Path, name)
	alias, markers := parseDecoratorList(def.DecoratorList, constPyClassAliasFuncName)

	node := &commonpb.Node{
		Name:  name,
		Path:  id,
		Alias: alias,
		Type:  commonpb.NodeType_NT_CLASS,
		Tags:  markers,
	}
	parent.Children = append(parent.Children, node)
	p.pathNodeCache.Put(node.Path, node)

	// Body []Stmt
	p.astWalkStmts(node, def.Body)
}

func (p *PyParser) newFunctionNode(parent *commonpb.Node, def *ast.FunctionDef) {
	name := string(def.Name)
	id := fmt.Sprintf("%s::%s", parent.Path, name)
	alias, markers := parseDecoratorList(def.DecoratorList, constPyFunctionAliasFuncName)

	node := &commonpb.Node{
		Name:  name,
		Path:  id,
		Alias: alias,
		Type:  commonpb.NodeType_NT_FUNCTION,
		Tags:  markers,
	}
	parent.Children = append(parent.Children, node)
	p.pathNodeCache.Put(node.Path, node)
}

func (p *PyParser) Parse() (err error) {
	err = filepath.Walk(p.RootPath, p.walk)
	if err != nil {
		return errorx.Errorf(
			codes.ParsePythonProjectTaskFailure, "failed to walks the file tree rooted at root path[%s], error: %+v",
			p.RootPath, err,
		)
	}

	return nil
}

func (p *PyParser) GetRootNode() *commonpb.Node {
	node, _ := p.pathNodeCache.Get(p.relativeToRootParentPath(p.RootPath))
	return node
}

func getFileAliasFromFirstLine(file *os.File) (alias string) {
	defer func(file *os.File) {
		_, err := file.Seek(0, io.SeekStart)
		if err != nil {
			logx.Errorf("failed to seek the file[%s], error: %+v", file.Name(), err)
		}
	}(file)

	line, _ := bufio.NewReader(file).ReadString('\n')
	line = strings.TrimRight(line, "\n")
	match, err := textFileAliasRegexCompile.FindStringMatch(line)
	if err == nil && match != nil {
		group := match.GroupByName(constGroupNameFileAlias)
		if group == nil {
			logx.Errorf(
				"failed to get the match group by name[%s] from first line text[%s]", constGroupNameFileAlias, line,
			)
		} else {
			alias = group.String()
		}
	}

	return
}

func parseDecoratorList(decorators []ast.Expr, funcName string) (alias string, markers []string) {
	for _, decorator := range decorators {
		// alias example: Call(func=Name(id='allure.feature', ctx=Load()), args=[Str(s='娱乐tab用例')], keywords=[], starargs=None, kwargs=None)
		// marker example: Name(id='pytest.mark.mine', ctx=Load())

		switch obj := decorator.(type) {
		case *ast.Call:
			if alias == "" {
				// find alias
				name, ok := obj.Func.(*ast.Name)
				if !ok {
					continue
				}

				if string(name.Id) != funcName {
					continue
				}

				for _, arg := range obj.Args {
					str, ok := arg.(*ast.Str)
					if !ok {
						continue
					}

					alias = string(str.S)
				}
			}
		case *ast.Name:
			// find marker
			if strings.HasPrefix(
				string(obj.Id), constPyTestMarkPrefix,
			) && string(obj.Id) != constPyTestMarkParametrize {
				markers = append(markers, strings.TrimPrefix(string(obj.Id), constPyTestMarkPrefix))
			}
		}
	}

	return alias, markers
}
