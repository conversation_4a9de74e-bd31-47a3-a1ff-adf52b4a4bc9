package internal

import (
	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/proc"
	"github.com/zeromicro/go-zero/core/threading"

	consumerv2 "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworkerv2/consumer"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/worker/mqc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/worker/mqc/internal/tasks"
)

func SetupOperation(svcCtx *svc.ServiceContext, needToLaunch bool) error {
	// register tasks
	if err := registerTasks(svcCtx); err != nil {
		return errors.Wrap(err, "failed to register tasks")
	}

	if needToLaunch {
		// start consumer
		launchConsumer(svcCtx.WorkerConsumer)
	}

	return nil
}

func registerTasks(svcCtx *svc.ServiceContext) error {
	return svcCtx.WorkerConsumer.RegisterHandlers(
		consumerv2.NewTaskHandlerOjb(constants.MQTaskTypeWorkerPlanMonitor, tasks.NewProcessor(svcCtx)), // 计划监控任务
		consumerv2.NewTaskHandlerOjb(
			constants.MQTaskTypeWorkerParsePythonProject, tasks.NewProcessorParse(svcCtx),
		), // 解析Python项目任务
	)
}

func launchConsumer(consumer *consumerv2.Consumer) {
	threading.GoSafe(consumer.Start)

	proc.AddShutdownListener(
		func() {
			logx.Info("stopping the consumer")
			consumer.Stop()
		},
	)
}
