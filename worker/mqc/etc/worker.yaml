Name: worker

Log:
  ServiceName: mqc.worker
  Encoding: plain
  Level: info
  Path: /app/logs/worker

Prometheus:
  Host: 0.0.0.0
  Port: 20621
  Path: /metrics

Telemetry:
  Name: mqc.worker
  Endpoint: http://127.0.0.1:14268/api/traces
  Sampler: 1.0
  Batcher: jaeger

DevServer:
  Enabled: true
  Port: 20631

Redis:
  Key: mqc.worker
  Host: 127.0.0.1:6379
  Type: node
  Pass: Quwan@2020
  DB: 15

ReporterRPC:
  Etcd:
    Hosts:
      - 127.0.0.1:2379
    Key: rpc.reporter
  NonBlock: true
  Timeout: 0

WorkerConsumer:
  Broker: Quwan@2020@127.0.0.1:6379
  Backend: Quwan@2020@127.0.0.1:6379
  Queue: mqc:worker
  ConsumerTag: mqc_worker
  IsEnableMetricsExporter: false # 新增加 暂时不需要开启
  IsMonitorHttp: false # 新增加 选择性开启
  Db: 15
  MaxWorker: 0

ReporterUri: https://test-quality.ttyuyin.com

PVCPath: ${PVC_PATH}
